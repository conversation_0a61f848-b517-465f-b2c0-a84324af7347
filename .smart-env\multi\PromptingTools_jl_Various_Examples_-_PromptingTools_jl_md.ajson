"smart_sources:PromptingTools.jl/Various Examples - PromptingTools.jl.md": {"path":"PromptingTools.jl/Various Examples - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11123254,-0.00549129,-0.01378846,-0.05745674,-0.03306271,0.0128299,-0.03970887,0.04354667,0.03556689,-0.03490745,-0.01730517,0.01126849,-0.02105538,0.03768124,-0.00116022,0.02203839,-0.03879945,0.01589922,-0.08010755,-0.02002407,0.08160866,0.06969286,0.02984072,-0.02370196,-0.00153065,0.04783315,0.02256063,-0.02968712,0.007889,-0.18698683,0.04140214,-0.01156725,-0.00501026,-0.01082305,-0.03136298,-0.00486325,-0.0556497,0.05506197,-0.0502368,-0.00883114,0.03224314,0.02027536,-0.0424419,-0.00910541,-0.0640979,-0.09204935,-0.01937203,-0.02970592,0.01489916,-0.05192013,-0.00659591,-0.02218256,0.05262451,-0.03110824,0.0188667,0.00028842,0.00964668,0.09689204,0.02803768,0.00494553,0.03991349,0.01404315,-0.18803604,0.14107874,0.0155284,0.0345736,-0.0084026,0.00776605,0.02897268,0.0435195,-0.02427075,0.03533012,-0.00700275,0.0745175,0.00500919,-0.02200055,-0.03482509,-0.0146433,-0.03264547,-0.09635042,-0.02471149,-0.01519733,-0.00329697,-0.00679995,-0.04231307,0.00837827,0.01130152,-0.02125567,0.06927701,0.0049047,-0.00061268,-0.05991105,0.00561566,0.04047463,-0.05592159,-0.02034079,0.03035645,-0.03039793,-0.07636487,0.11714292,-0.01511754,0.00160961,0.02846821,-0.02499697,0.03548287,-0.0657049,0.02727004,-0.04748549,-0.02167109,0.02697245,-0.00775423,-0.01733799,-0.01223329,-0.08021268,-0.02043336,-0.06431844,0.01879459,-0.01968232,0.00855666,-0.03991481,0.01269081,0.0097,0.01093537,0.00189655,-0.00904481,0.02807803,0.0280857,0.03916482,0.00002425,0.01936179,0.05068092,0.01022267,-0.06396879,-0.01948503,-0.01094819,0.05677745,0.05025251,-0.05879106,0.03413072,-0.01113375,0.02615833,-0.001044,0.00602515,-0.12495121,0.01121483,0.04596737,-0.03298416,0.002369,-0.02026453,-0.02756753,0.03924612,0.04723352,-0.04383264,0.01135219,0.07062256,0.06599298,0.07856423,0.07163771,0.00371114,0.03051727,-0.06766272,-0.04612808,-0.00479894,0.15687145,0.04114063,-0.06469458,-0.08124078,-0.02511532,-0.05999945,-0.04592458,0.01913695,0.05087201,-0.06328195,-0.00373063,0.06709719,0.05242691,-0.06254365,0.01429616,-0.00004932,0.02701045,0.0336569,-0.06205615,-0.06486511,0.01129782,-0.02249589,-0.01446741,-0.00909402,-0.06978654,0.00182337,0.04577578,-0.06925551,0.01423223,0.05378623,-0.02947106,-0.01316957,-0.00757097,-0.00104009,-0.09472084,0.0667529,-0.05160338,0.04379374,0.02793647,0.05005456,0.01330796,-0.01072544,0.04029913,0.06125681,-0.01158065,0.12616147,0.01996782,-0.0976131,-0.06190358,0.06408152,-0.00087039,-0.01685928,-0.02611574,-0.06166736,0.03216575,0.01283979,0.07323517,-0.02183737,-0.02266729,-0.04748773,-0.22113758,0.00631464,0.03110101,-0.07105246,0.06231502,-0.09761768,0.08422361,-0.0374734,-0.02598136,0.07080343,0.09005395,-0.05213004,0.04677304,-0.01960237,0.00989188,-0.02183024,-0.05526953,0.00782602,0.00864418,0.03799834,0.01057106,-0.0082449,0.03201585,-0.08583267,0.00798211,-0.0254408,0.09751198,0.04328636,0.04517059,-0.09592272,0.02235698,-0.01102304,0.0640766,-0.10156184,-0.00180896,0.01917296,-0.03213064,0.04925799,0.09304443,0.02905309,-0.03384288,0.05261422,-0.00539558,-0.03494093,-0.01385216,-0.01014086,-0.05900367,-0.03865991,-0.04674657,0.02088857,0.01730567,-0.05193986,0.01975186,-0.00463234,0.00768192,-0.02088895,-0.04569067,-0.05077923,-0.00339143,0.00933583,0.02216737,-0.02089146,0.00104718,-0.03503687,0.05644617,0.04248393,0.0051321,-0.00648305,0.04052918,0.00849308,-0.03746219,0.11061776,0.03925527,0.05459522,-0.0213522,0.06326099,-0.00107467,-0.06466012,-0.0118756,-0.03861034,-0.04884506,0.05601599,0.00336666,0.0323376,0.02610343,0.06108451,0.0310678,-0.03087101,0.10602765,-0.05587203,-0.02005119,0.01369632,0.01756459,-0.0167892,0.02671256,0.01615991,-0.23227952,0.03510979,0.01246583,0.02275087,-0.01987871,0.06941468,0.01406051,-0.02325119,-0.0035971,0.04604531,-0.04314251,0.00170297,0.01290692,-0.0280333,0.05276049,0.0652275,0.02128752,0.01118811,0.03632551,-0.08226646,0.04103931,0.02355151,0.22924319,-0.02537553,0.0250934,0.00376504,-0.00914047,-0.05390656,0.06759027,0.04339628,0.03442488,0.0499183,0.16057704,-0.00409053,0.04613668,0.00554919,-0.02361077,-0.01530929,0.02157925,0.00658511,-0.03132774,0.04878049,-0.08037795,-0.0023898,0.05937999,-0.01984688,-0.00164059,-0.09291209,-0.05183918,0.04640659,-0.00396531,-0.00574964,-0.02191875,0.00361642,0.01612306,-0.00355872,0.0493828,0.00979385,-0.07072733,-0.00109782,0.06858263,-0.02194965,0.09818614,-0.00744639,-0.01670015],"last_embed":{"hash":"d76899c18eb8529432e5ce2bb6f4f2d8d1840f599d0f20f5f93ed8d367a2286c","tokens":464}}},"last_read":{"hash":"d76899c18eb8529432e5ce2bb6f4f2d8d1840f599d0f20f5f93ed8d367a2286c","at":1745995226991},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/examples/readme_examples/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"ai* Functions Overview","target":"#ai*-Functions-Overview \"ai* Functions Overview\"","line":243},{"title":"Seamless Integration Into Your Workflow","target":"#Seamless-Integration-Into-Your-Workflow \"Seamless Integration Into Your Workflow\"","line":244},{"title":"Advanced Prompts / Conversations","target":"#Advanced-Prompts-/-Conversations \"Advanced Prompts / Conversations\"","line":245},{"title":"Templated Prompts","target":"#Templated-Prompts \"Templated Prompts\"","line":246},{"title":"Asynchronous Execution","target":"#Asynchronous-Execution \"Asynchronous Execution\"","line":247},{"title":"Model Aliases","target":"#Model-Aliases \"Model Aliases\"","line":248},{"title":"Embeddings","target":"#Embeddings \"Embeddings\"","line":249},{"title":"Classification","target":"#Classification \"Classification\"","line":250},{"title":"Routing to Defined Categories","target":"#Routing-to-Defined-Categories \"Routing to Defined Categories\"","line":251},{"title":"Data Extraction","target":"#Data-Extraction \"Data Extraction\"","line":252},{"title":"OCR and Image Comprehension","target":"#OCR-and-Image-Comprehension \"OCR and Image Comprehension\"","line":253},{"title":"Experimental Agent Workflows / Output Validation with airetry!","target":"#Experimental-Agent-Workflows-/-Output-Validation-with-airetry! \"Experimental Agent Workflows / Output Validation with airetry!\"","line":254},{"title":"Using Ollama models","target":"#Using-Ollama-models \"Using Ollama models\"","line":255},{"title":"Using MistralAI API and other OpenAI-compatible APIs","target":"#Using-MistralAI-API-and-other-OpenAI-compatible-APIs \"Using MistralAI API and other OpenAI-compatible APIs\"","line":256},{"title":"​","target":"#Various-Examples","line":258},{"title":"​","target":"#ai*-Functions-Overview","line":260},{"title":"​","target":"#Seamless-Integration-Into-Your-Workflow","line":324},{"title":" Info: Tokens: 102 @ Cost: $0.0002 in 2.7 seconds\n┌ Info: AIMessage> To ignore a file called \"XYZ\" in any folder or subfolder, you can add the following line to your .gitignore file:\n│ \n│ ```\n│ **/XYZ\n│ ```\n│ \n└ This pattern uses the double asterisk (`**`) to match any folder or subfolder, and then specifies the name of the file you want to ignore.\n```\n\nYou probably saved 3-5 minutes on this task and probably another 5-10 minutes, because of the context switch/distraction you avoided. It's a small win, but it adds up quickly.\n\n## Advanced Prompts / Conversations [​","target":"#Advanced-Prompts-/-Conversations","line":337},{"title":"​","target":"#Templated-Prompts","line":395},{"title":"Examples","target":"https://github.com/svilupp/PromptingTools.jl/tree/main/examples","line":464},{"title":"​","target":"#Asynchronous-Execution","line":466},{"title":"​","target":"#Model-Aliases","line":479},{"title":"​","target":"#Embeddings","line":496},{"title":"​","target":"#Classification","line":520},{"title":"Templated Prompts","target":"/PromptingTools.jl/dev/examples/readme_examples#templated-prompts","line":544},{"title":"​","target":"#Routing-to-Defined-Categories","line":546},{"title":"​","target":"#Data-Extraction","line":569},{"title":"​","target":"#OCR-and-Image-Comprehension","line":623},{"title":" Info: Tokens: 1141 @ Cost: \\$0.0117 in 2.2 seconds\n# AIMessage(\"The image shows a logo consisting of the word \"julia\" written in lowercase\")\n```\n\nOr you can do an OCR of a screenshot. Let's transcribe some SQL code from a screenshot (no more re-typing!), we use a template `:OCRTask`:\n\njulia\n\n```\n# Screenshot of some SQL code\nimage_url = \"https://www.sqlservercentral.com/wp-content/uploads/legacy/8755f69180b7ac7ee76a69ae68ec36872a116ad4/24622.png\"\nmsg = aiscan(:OCRTask; image_url, model=\"gpt4v\", task=\"Transcribe the SQL code in the image.\", api_kwargs=(; max_tokens=2500))\n\n# [ Info: Tokens: 362 @ Cost: \\$0.0045 in 2.5 seconds\n# AIMessage(\"```sql\n# update Orders <continue>\n```\n\nYou can add syntax highlighting of the outputs via Markdown\n\njulia\n\n```\nusing Markdown\nmsg.content |> Markdown.parse\n```\n\n## Experimental Agent Workflows / Output Validation with `airetry!` [​","target":"#Experimental-Agent-Workflows-/-Output-Validation-with-airetry!","line":633},{"title":"Language Agent Tree Search paper","target":"https://arxiv.org/abs/2310.04406","line":729},{"title":"DSPy Assertions paper","target":"https://arxiv.org/abs/2312.13382","line":729},{"title":"​","target":"#Using-Ollama-models","line":731},{"title":"Ollama.ai","target":"https://ollama.ai/","line":733},{"title":"Setup Guide for Ollama","target":"/PromptingTools.jl/dev/examples/readme_examples#setup-guide-for-ollama","line":762},{"title":"​","target":"#Using-MistralAI-API-and-other-OpenAI-compatible-APIs","line":764},{"title":"Fireworks.ai","target":"https://app.fireworks.ai/","line":789},{"title":"Perplexity.ai","target":"https://docs.perplexity.ai/","line":789},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/examples/readme_examples.md","line":804},{"title":"Previous pageHow It Works","target":"/PromptingTools.jl/dev/how_it_works","line":806},{"title":"Next pageUsing AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":808},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":810},{"title":"Icons8","target":"https://icons8.com","line":810},{"title":"**VitePress**","target":"https://vitepress.dev","line":810}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,257],"##Reference#{1}":[209,242],"##Reference#{2}":[243,243],"##Reference#{3}":[244,244],"##Reference#{4}":[245,245],"##Reference#{5}":[246,246],"##Reference#{6}":[247,247],"##Reference#{7}":[248,248],"##Reference#{8}":[249,249],"##Reference#{9}":[250,251],"##Reference#{10}":[252,252],"##Reference#{11}":[253,253],"##Reference#{12}":[254,254],"##Reference#{13}":[255,255],"##Reference#{14}":[256,257],"#Various Examples [​](#Various-Examples)":[258,812],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)":[260,323],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{1}":[262,269],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{2}":[270,271],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{3}":[272,273],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{4}":[274,275],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{5}":[276,277],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{6}":[278,279],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{7}":[280,281],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{8}":[282,284],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{9}":[285,288],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{10}":[289,290],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{11}":[291,292],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{12}":[293,294],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{13}":[295,296],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{14}":[297,299],"#Various Examples [​](#Various-Examples)#`ai*` Functions Overview [​](#ai*-Functions-Overview)#{15}":[300,323],"#Various Examples [​](#Various-Examples)#Seamless Integration Into Your Workflow [​](#Seamless-Integration-Into-Your-Workflow)":[324,348],"#Various Examples [​](#Various-Examples)#Seamless Integration Into Your Workflow [​](#Seamless-Integration-Into-Your-Workflow)#{1}":[326,348],"#Various Examples [​](#Various-Examples)#Advanced Prompts / Conversations [​](#Advanced-Prompts-/-Conversations)":[349,394],"#Various Examples [​](#Various-Examples)#Advanced Prompts / Conversations [​](#Advanced-Prompts-/-Conversations)#{1}":[351,394],"#Various Examples [​](#Various-Examples)#Templated Prompts [​](#Templated-Prompts)":[395,465],"#Various Examples [​](#Various-Examples)#Templated Prompts [​](#Templated-Prompts)#{1}":[397,465],"#Various Examples [​](#Various-Examples)#Asynchronous Execution [​](#Asynchronous-Execution)":[466,478],"#Various Examples [​](#Various-Examples)#Asynchronous Execution [​](#Asynchronous-Execution)#{1}":[468,478],"#Various Examples [​](#Various-Examples)#Model Aliases [​](#Model-Aliases)":[479,495],"#Various Examples [​](#Various-Examples)#Model Aliases [​](#Model-Aliases)#{1}":[481,495],"#Various Examples [​](#Various-Examples)#Embeddings [​](#Embeddings)":[496,519],"#Various Examples [​](#Various-Examples)#Embeddings [​](#Embeddings)#{1}":[498,519],"#Various Examples [​](#Various-Examples)#Classification [​](#Classification)":[520,568],"#Various Examples [​](#Various-Examples)#Classification [​](#Classification)#{1}":[522,545],"#Various Examples [​](#Various-Examples)#Classification [​](#Classification)#Routing to Defined Categories [​](#Routing-to-Defined-Categories)":[546,568],"#Various Examples [​](#Various-Examples)#Classification [​](#Classification)#Routing to Defined Categories [​](#Routing-to-Defined-Categories)#{1}":[548,568],"#Various Examples [​](#Various-Examples)#Data Extraction [​](#Data-Extraction)":[569,622],"#Various Examples [​](#Various-Examples)#Data Extraction [​](#Data-Extraction)#{1}":[571,622],"#Various Examples [​](#Various-Examples)#OCR and Image Comprehension [​](#OCR-and-Image-Comprehension)":[623,659],"#Various Examples [​](#Various-Examples)#OCR and Image Comprehension [​](#OCR-and-Image-Comprehension)#{1}":[625,659],"#Various Examples [​](#Various-Examples)#Experimental Agent Workflows / Output Validation with `airetry!` [​](#Experimental-Agent-Workflows-/-Output-Validation-with-airetry!)":[660,730],"#Various Examples [​](#Various-Examples)#Experimental Agent Workflows / Output Validation with `airetry!` [​](#Experimental-Agent-Workflows-/-Output-Validation-with-airetry!)#{1}":[662,730],"#Various Examples [​](#Various-Examples)#Using Ollama models [​](#Using-Ollama-models)":[731,763],"#Various Examples [​](#Various-Examples)#Using Ollama models [​](#Using-Ollama-models)#{1}":[733,763],"#Various Examples [​](#Various-Examples)#Using MistralAI API and other OpenAI-compatible APIs [​](#Using-MistralAI-API-and-other-OpenAI-compatible-APIs)":[764,812],"#Various Examples [​](#Various-Examples)#Using MistralAI API and other OpenAI-compatible APIs [​](#Using-MistralAI-API-and-other-OpenAI-compatible-APIs)#{1}":[766,812]},"last_import":{"mtime":1712726574776,"size":28979,"at":1740449882805,"hash":"d76899c18eb8529432e5ce2bb6f4f2d8d1840f599d0f20f5f93ed8d367a2286c"},"key":"PromptingTools.jl/Various Examples - PromptingTools.jl.md"},