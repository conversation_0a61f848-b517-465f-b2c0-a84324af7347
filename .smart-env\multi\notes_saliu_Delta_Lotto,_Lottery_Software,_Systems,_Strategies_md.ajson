
"smart_sources:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md": {"path":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0923506,-0.06959207,-0.01090561,0.00121315,-0.03333841,0.03707566,-0.01755568,0.01103967,0.0438506,-0.01545365,-0.01517523,0.00702142,0.03562558,0.00809054,-0.03251003,-0.0458759,0.01291993,0.00105533,-0.04043884,-0.0172768,0.04183606,-0.05501208,-0.0431852,-0.06123237,0.04419865,0.02785279,-0.01710621,-0.06586087,-0.02418705,-0.22518986,0.01218606,0.00484648,-0.00808719,-0.08422806,-0.08921485,-0.02867692,0.00892363,0.03894141,-0.01739169,0.02769,-0.00740273,0.01282423,-0.02781194,-0.03287591,-0.02197621,-0.06072868,0.00493722,0.02230038,0.03295053,-0.02277807,-0.09087659,-0.02521499,0.00649624,0.03511989,0.06337839,0.02351902,0.01910703,0.10050666,0.00029911,0.02994568,0.06118274,0.04597277,-0.20655724,0.05456629,-0.02111661,0.03544333,-0.03182498,-0.01872727,0.00717654,0.05152595,0.00925732,0.02559436,-0.01879672,0.0736699,0.05440797,-0.03149643,-0.04279241,-0.06869139,-0.0617923,-0.00300402,-0.01886788,0.00079394,0.00523837,-0.01855352,0.01749149,0.07048107,0.05596085,0.07459458,0.05862792,-0.06258588,0.00806232,-0.00832055,0.06241536,0.05780543,0.01402534,0.01682405,0.05824418,-0.05392183,0.01154968,0.07682738,0.01269569,0.01269624,-0.03766648,-0.00271811,0.06194407,-0.00238339,-0.0228603,-0.08135432,-0.02345291,0.0507574,0.04187262,-0.00139201,0.05966773,-0.03097287,-0.06510891,0.00766209,0.00123009,-0.01376444,0.05031227,0.0166012,-0.02035406,0.02783597,-0.01969463,0.00057732,-0.02631777,0.02905719,0.03191584,0.06641898,0.00910922,0.01149658,0.0741168,0.01445605,-0.13698603,-0.03606956,-0.02280765,-0.02209771,-0.02661676,-0.02893779,0.01386751,0.04156101,-0.07313874,-0.0095896,0.07299013,-0.09274085,-0.05394306,0.10025439,0.0108172,0.00743798,-0.00627543,0.00181997,0.02146926,-0.00008696,-0.02266036,-0.01866363,0.01908884,-0.00108213,0.0768379,0.12675662,-0.0727224,0.01411887,0.0090048,-0.03407324,-0.03782552,0.12857467,0.01153883,-0.11939013,-0.00466458,0.06348836,-0.01547667,-0.09575414,-0.00142814,0.00325961,-0.06610925,0.02052587,0.07662467,-0.01424768,-0.05076925,-0.03697944,-0.01408182,0.03149655,0.02530094,-0.01643597,-0.06847767,0.0272142,-0.04807232,-0.11060318,0.00755121,-0.04546134,0.02013305,0.0453039,-0.0368983,-0.00366093,-0.00037223,0.01591071,0.00466001,-0.04572825,-0.03396159,-0.02131774,0.07583933,0.00085977,0.00432509,0.00450095,0.03530854,-0.01514367,-0.03581596,0.08395852,-0.02456883,-0.06276036,0.08059996,0.0488995,-0.04950684,0.01740048,0.03900879,0.06828258,-0.04471059,0.06000919,0.01708705,0.02417907,-0.01586795,0.03362371,-0.02973283,0.08287029,-0.02862317,-0.21107914,-0.01445628,-0.05802116,0.02147215,0.01393196,0.0090303,0.02980033,-0.00842367,0.04334506,0.10656271,0.09626642,-0.05759748,-0.00857104,0.05056276,-0.02185549,-0.02010822,-0.07027159,-0.05384188,-0.04280266,0.04220648,-0.0299455,0.01864442,0.00366407,-0.07003606,0.01206806,-0.03306846,0.12753344,0.00862365,0.05895601,-0.02655781,0.05945988,0.00832693,-0.00829514,-0.0346416,0.01518849,0.04281626,-0.05155031,0.01516016,-0.01208758,-0.01962449,-0.09318287,0.04812064,0.00584651,-0.10250825,-0.02141292,0.00569634,0.00572004,-0.00113584,-0.01648014,0.03498214,0.01203839,0.0206324,0.04000065,0.03880263,0.0633789,-0.04625919,-0.05022102,0.00148541,-0.00447107,0.04490516,0.00911742,-0.04678446,0.00266596,-0.05034364,0.04332679,0.00381035,-0.00915656,-0.01440813,0.03339259,-0.03055444,0.00336474,0.06832518,-0.01646879,-0.01909407,0.00601493,-0.00183556,0.08776428,-0.0198295,-0.01649418,0.02174807,-0.00005942,-0.06115247,0.03244579,0.05904824,0.02547958,0.02807227,0.05717804,0.03433535,0.00597838,-0.0187259,-0.02633156,0.00210016,-0.05906042,0.01028676,0.01262437,0.04911991,-0.26178914,0.00132102,-0.00446693,0.00799294,-0.00902157,0.01240058,0.04730877,-0.01793487,0.01948127,-0.05372937,0.01906634,0.05766827,-0.01487091,-0.06044965,-0.06087119,0.03148744,0.0348964,-0.03055179,0.0484723,0.01042562,0.05269678,0.05900822,0.22345243,0.02456492,-0.00149995,0.04587381,0.02151801,0.04208405,-0.0294445,0.06791642,-0.00420693,0.02522938,0.06902643,-0.02927276,-0.10095961,0.07754461,-0.04776863,0.0458047,-0.00897811,0.03380404,-0.02631669,-0.01183952,-0.02102338,0.01379401,0.10160229,0.02269114,-0.01856472,-0.05293917,0.02649577,0.07118696,-0.10454432,-0.07154023,-0.06828885,-0.01780983,0.04142018,0.09262542,-0.00107344,-0.01677828,-0.00405133,-0.00952997,0.02911842,-0.02193031,0.0352847,-0.003725,0.01492466],"last_embed":{"hash":"1g9y8wq","tokens":509}}},"last_read":{"hash":"1g9y8wq","at":1753423453486},"class_name":"SmartSource","last_import":{"mtime":1753363612074,"size":22334,"at":1753423416052,"hash":"1g9y8wq"},"blocks":{"#---frontmatter---":[1,6],"#Delta Lotto, Lottery Software, Systems, Strategies":[8,161],"#Delta Lotto, Lottery Software, Systems, Strategies#{1}":[10,15],"#Delta Lotto, Lottery Software, Systems, Strategies#{2}":[16,16],"#Delta Lotto, Lottery Software, Systems, Strategies#{3}":[17,17],"#Delta Lotto, Lottery Software, Systems, Strategies#{4}":[18,18],"#Delta Lotto, Lottery Software, Systems, Strategies#{5}":[19,19],"#Delta Lotto, Lottery Software, Systems, Strategies#{6}":[20,20],"#Delta Lotto, Lottery Software, Systems, Strategies#{7}":[21,21],"#Delta Lotto, Lottery Software, Systems, Strategies#{8}":[22,22],"#Delta Lotto, Lottery Software, Systems, Strategies#{9}":[23,23],"#Delta Lotto, Lottery Software, Systems, Strategies#{10}":[24,24],"#Delta Lotto, Lottery Software, Systems, Strategies#{11}":[25,26],"#Delta Lotto, Lottery Software, Systems, Strategies#{12}":[27,34],"#Delta Lotto, Lottery Software, Systems, Strategies#{13}":[35,35],"#Delta Lotto, Lottery Software, Systems, Strategies#{14}":[36,36],"#Delta Lotto, Lottery Software, Systems, Strategies#{15}":[37,38],"#Delta Lotto, Lottery Software, Systems, Strategies#{16}":[39,44],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>":[45,84],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{1}":[47,70],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{2}":[71,71],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{3}":[72,84],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{4}":[74,84],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>III. Lottery Strategies, Systems Based on <i>Delta</i> Reports</u>":[85,116],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>III. Lottery Strategies, Systems Based on <i>Delta</i> Reports</u>#{1}":[87,116],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>":[117,139],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{1}":[119,132],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{2}":[133,133],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{3}":[134,134],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{4}":[135,135],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{5}":[136,136],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{6}":[137,137],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{7}":[138,139],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>V. Generate Combinations from <i>Delta</i> Lottery Strategies, Systems</u>":[140,155],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>V. Generate Combinations from <i>Delta</i> Lottery Strategies, Systems</u>#{1}":[142,155],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>VI. <i>Purge</i> Combinations from Output Files Applying Lotto <i>Delta</i> Filters</u>":[156,161],"#Delta Lotto, Lottery Software, Systems, Strategies#<u>VI. <i>Purge</i> Combinations from Output Files Applying Lotto <i>Delta</i> Filters</u>#{1}":[158,161]},"outlinks":[{"title":"Delta Lotto Numbers run from menu two of the Ultimate Lottery Software.","target":"https://saliu.com/images/ultimate-lotto-software-61.gif","line":27},{"title":"The best unique delta lottery software calculates deltas, generates combinations for lotto-6.","target":"https://saliu.com/images/delta-lotto.gif","line":31},{"title":"There is ultimate delta lotto software for all pick-3, pick 4 daily lottery games.","target":"https://saliu.com/images/delta-pick-software.gif","line":33},{"title":"_**Lottery Deltas Can Build Effective Lotto Strategies, Systems, Software**_","target":"https://saliu.com/bbs/messages/648.html","line":35},{"title":"_sum-totals, root sums, average, standard deviation, **deltas**, medians_","target":"https://saliu.com/forum/lottery-sums.html","line":37},{"title":"Generate delta lottery reports to create winning lotto strategies, systems.","target":"https://saliu.com/images/delta-lotto-calculate.gif","line":49},{"title":"The delta lotto number reports require a minimum amount of combinations to analyze.","target":"https://saliu.com/images/delta-analyze-draws.gif","line":53},{"title":"Deltas in lottery can reduce millions of combinations and still win the jackpot.","target":"https://saliu.com/ScreenImgs/lottery-delta-filters.gif","line":57},{"title":"_**Reversed Birthday Paradox**_","target":"https://forums.saliu.com/wikipedia-piracy.html","line":69},{"title":"Lottery deltas and restrictions in the past 1000 lotto drawings.","target":"https://saliu.com/ScreenImgs/delta-lottery-draws.gif","line":89},{"title":"Check if delta lottery strategies hit in past drawings and see the skips.","target":"https://saliu.com/images/delta-lottery-strategy.gif","line":119},{"title":"_**filters in static and dynamic**_","target":"https://saliu.com/bbs/messages/919.html","line":123},{"title":"_**REVERSED lottery strategy**_","target":"https://saliu.com/reverse-strategy.html","line":127},{"title":"_**lottery filters at run-time**_","target":"https://saliu.com/filters.html","line":131},{"title":"_**cross-reference lottery strategies**_","target":"https://saliu.com/cross-lines.html","line":133},{"title":"The two lotto delta combination generators are easy to understand and run.","target":"https://saliu.com/images/delta-lotto-generator.gif","line":144},{"title":"The computer processor is tested with delta filters in 6-number lotto games.","target":"https://saliu.com/images/delta-lottery-filters.gif","line":154}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["delta lotto","lottery","deltas","systems","strategy","software","statistics","drawings","draws","difference","analysis","adjacent","neighboring numbers"],"source":"https://saliu.com/delta-lotto-software.html","author":null}},"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10711874,-0.07036666,-0.03059526,0.00793685,-0.02262043,0.02575518,0.00535457,0.02613958,0.05055928,-0.01196079,-0.01258266,-0.01112777,0.01671005,0.02032777,-0.02094691,-0.02797002,0.00285744,-0.02799901,-0.01610283,0.00810444,0.06535732,-0.05245656,-0.04650313,-0.08698786,0.06327683,0.00477493,-0.0039559,-0.07365856,-0.01228826,-0.18231393,-0.00193232,0.01337635,-0.01212047,-0.07327955,-0.05991678,-0.03467586,0.0223753,0.07109705,0.01304681,0.04254371,0.00248065,0.01904947,-0.04340255,-0.02060706,-0.02071683,-0.04357176,-0.00598461,0.00717383,0.02960781,-0.00983478,-0.0748999,-0.02161626,0.00261261,0.0332748,0.07170504,0.0261059,0.03529727,0.0520321,0.03636589,0.02177712,0.05903077,0.0130934,-0.21576916,0.06710274,-0.02115651,0.03051228,-0.01900961,-0.00878949,-0.00654872,0.04295663,0.03429789,0.0265175,-0.02407292,0.07844344,0.07072889,-0.01179118,-0.05715153,-0.06102962,-0.05163028,-0.0129387,-0.0161213,-0.02736286,-0.0056059,-0.00770228,0.00149466,0.04493796,0.06835635,0.09777034,0.07375228,-0.05008778,0.0123466,-0.00023941,0.06779315,0.05644491,-0.0394192,0.02178336,0.05468884,-0.04534364,0.0128226,0.10581182,-0.0096895,0.02307096,-0.01070124,0.0272181,0.05581965,0.00582223,-0.01703322,-0.06674716,-0.02932355,0.04855628,0.03754007,0.01664302,0.053996,-0.06394041,-0.0202447,-0.03255722,0.0316023,-0.01250944,0.04864428,0.01051637,-0.03691157,0.00842243,-0.02453267,-0.01623946,0.00102676,0.04617033,0.03302235,0.07777887,0.02094105,0.01447849,0.05505436,0.00879942,-0.12072926,-0.03736902,-0.01329847,-0.01221741,-0.0032605,-0.04716724,-0.00129426,0.00033179,-0.04407227,0.00435483,0.04293119,-0.10670773,-0.04519144,0.09016407,0.02160637,0.0133374,0.00869757,0.0102752,-0.02005476,-0.01436103,0.0028109,-0.03289948,0.00447877,0.01778934,0.09616209,0.13302034,-0.04384997,0.02665978,0.03630471,-0.05293505,-0.04971376,0.14440487,-0.00596656,-0.12436204,-0.035543,0.05199983,0.01753869,-0.09805802,-0.00229364,0.01877307,-0.03313276,0.014525,0.10318565,0.00478035,0.00970815,-0.04397652,-0.04619121,0.01898306,0.00350127,-0.02245087,-0.07612465,0.02642341,-0.0516187,-0.10731494,0.00375114,-0.05396445,0.02573261,0.06397709,-0.0180623,-0.02170189,-0.01421075,0.04548136,0.00067881,-0.05595339,-0.03266238,-0.02615708,0.06262513,-0.0148461,0.00994768,-0.00774935,0.00763489,-0.0152262,-0.02190374,0.05508992,-0.02110507,-0.05628972,0.04947783,0.03137787,-0.02817734,0.01874319,0.0457961,0.05731489,-0.05898165,0.03732438,-0.01031206,0.01426375,0.00299081,0.01842083,-0.0371697,0.03990234,-0.05869222,-0.2317979,-0.00452761,-0.05404206,0.01005838,-0.0119402,0.01933786,0.0269874,0.00708038,0.02711472,0.11781286,0.08186378,-0.06863599,0.00034663,0.0117857,-0.0170764,-0.02974158,-0.05464093,-0.06267806,-0.01978744,0.04286794,-0.01162443,0.00668882,-0.02630867,-0.06195588,0.00534047,-0.01765819,0.1429739,0.0377869,0.02880805,-0.01875891,0.08907191,0.01770962,-0.02029703,-0.05318465,0.00520375,0.03492847,-0.0776661,0.03595777,-0.03032952,-0.02341967,-0.10812499,0.04119482,0.00520439,-0.08056504,-0.01429721,-0.01158764,0.01866789,-0.03648122,-0.04565103,0.01930012,-0.00470322,-0.00081907,0.02095291,0.03986254,0.06974036,-0.02643052,-0.07219915,-0.00252627,-0.02218393,0.02238532,0.01484045,-0.02830858,0.01741306,-0.04487319,0.00313679,0.00665767,-0.01118904,-0.04381393,-0.01602886,-0.03203362,0.01662485,0.09064857,0.00491876,-0.04330673,0.01808018,-0.00939643,0.0593744,-0.00631193,-0.01214415,0.01755354,-0.0036029,-0.08945706,0.02875416,0.06038925,0.03357184,0.06845735,0.0378285,-0.01171705,-0.01314491,-0.00589119,-0.01412621,0.01113031,-0.05719999,-0.01083471,0.03490913,0.00715099,-0.2654115,0.04158359,0.02232933,0.02058196,0.01777617,0.02991338,0.01426899,-0.00960496,0.012044,-0.03058362,0.03962562,0.06211004,-0.01666311,-0.02707825,-0.06579069,0.01879632,0.03976218,-0.03342007,0.03981834,0.0338998,0.03333876,0.03405831,0.23836477,0.0422087,-0.00358244,0.02002059,0.0251889,0.02591622,-0.02252835,0.05816783,-0.00531412,0.0262081,0.08199143,-0.0063968,-0.07443489,0.08407699,-0.04024563,0.04746874,-0.01151927,0.03950731,-0.06304102,0.0135759,-0.01281135,0.03795609,0.10706501,0.06165152,-0.0327209,-0.05213678,0.02793583,0.05332203,-0.09773777,-0.08286701,-0.07761624,-0.00187284,0.01868651,0.07707829,0.00400201,-0.02597114,0.01449406,-0.01468328,0.03093494,-0.0028661,0.04103417,-0.0323995,0.03130332],"last_embed":{"hash":"1hjn0oe","tokens":99}}},"text":null,"length":0,"last_read":{"hash":"1hjn0oe","at":1753423451268},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#---frontmatter---","lines":[1,6],"size":247,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08981702,-0.06320208,-0.01636155,-0.01075539,-0.03975173,0.03712757,-0.00876928,-0.00004712,0.03168398,-0.00753213,-0.0087667,0.00587589,0.04120038,0.00127226,-0.02936021,-0.03708535,0.01793586,0.00712212,-0.06079862,-0.01552642,0.04045752,-0.057679,-0.04504267,-0.05903838,0.03665259,0.01695771,-0.02055196,-0.07392374,-0.02391555,-0.22444007,0.00836453,0.00563249,0.00450498,-0.09007967,-0.08292034,-0.02871768,0.0002902,0.0346162,-0.03174116,0.01462382,-0.01013266,0.00714231,-0.01275005,-0.04159905,-0.02178257,-0.06926262,0.00146701,0.02535598,0.03653394,-0.02468318,-0.08348729,-0.00931295,0.00672028,0.03838632,0.05276807,0.02620472,0.01266075,0.10822181,-0.01785568,0.0312469,0.05069881,0.05427109,-0.20263505,0.04645203,-0.02236141,0.03799141,-0.02536175,-0.02165164,0.00689445,0.06129324,-0.00149873,0.03842333,-0.01510157,0.07405829,0.04654591,-0.02630627,-0.04041289,-0.06562706,-0.05251085,0.0093133,-0.02729887,-0.00016967,-0.0101921,-0.01874834,0.01662916,0.07387991,0.05478464,0.06894563,0.05709218,-0.0653716,0.00082199,0.00764502,0.04940727,0.05440727,0.02795291,0.00899356,0.05487801,-0.07010096,0.02125002,0.08183075,0.01159918,0.00361806,-0.04237229,-0.00662431,0.0585009,-0.00032985,-0.03436085,-0.08264398,-0.03851517,0.04568918,0.05072659,0.0021477,0.0694838,-0.02536254,-0.07545233,0.01484825,-0.00802543,-0.0226876,0.04509646,0.01443209,-0.01471803,0.03137369,-0.00984045,-0.00129613,-0.02192881,0.02316389,0.03837807,0.06496269,0.00553364,0.01740106,0.07294575,0.01932869,-0.1379583,-0.03444508,-0.02673662,-0.02103389,-0.02171229,-0.02853532,0.00314998,0.04612341,-0.07727295,-0.02348035,0.07617244,-0.0931379,-0.04468281,0.10191652,0.00254389,0.00227439,-0.01292188,0.00037173,0.03239116,0.00267073,-0.02444955,-0.01940242,0.02688396,0.00395832,0.07763451,0.12042339,-0.07892722,0.01639295,-0.00436781,-0.02375849,-0.02998877,0.12841773,0.0198094,-0.09977838,0.00427355,0.0629202,-0.01662252,-0.08700462,-0.00672193,0.02267057,-0.08156814,0.03200534,0.08031645,-0.00944311,-0.05781698,-0.03827717,-0.00006405,0.03301137,0.02434125,-0.02481927,-0.04936103,0.02678727,-0.03910126,-0.11439883,0.01190394,-0.03795151,0.02234594,0.03650766,-0.0389664,0.00157776,0.00023259,0.00518154,-0.00470962,-0.04447961,-0.0332517,-0.02840088,0.08155491,-0.00342167,0.01929571,0.00688757,0.04758246,-0.00641609,-0.02428338,0.08690395,-0.03002228,-0.07036477,0.0790195,0.0472839,-0.041732,0.02371639,0.03736494,0.06368808,-0.04525425,0.06026698,0.01333579,0.02838711,-0.02107368,0.02748625,-0.0172045,0.09435026,-0.02628831,-0.20239605,-0.0086572,-0.05254063,0.01800574,0.02307572,0.00937636,0.02056187,-0.01366884,0.04282985,0.10394482,0.10250778,-0.07020666,-0.00022592,0.06290337,-0.01893152,-0.01768708,-0.08046933,-0.06373467,-0.04940702,0.0283243,-0.03043717,0.01200833,0.00398599,-0.06801739,0.02308412,-0.0354765,0.12756602,0.00634891,0.0687741,-0.02958374,0.05640061,0.01054284,-0.00786778,-0.0140961,0.01485977,0.0494925,-0.04514018,0.00324884,-0.00562594,-0.00867522,-0.08696885,0.05433438,0.00296063,-0.09778283,-0.00780608,0.00390092,-0.0025328,0.01074253,-0.0042224,0.03532595,0.0208223,0.0276662,0.03688182,0.0346204,0.0612511,-0.04999168,-0.05702487,0.01573174,-0.00099804,0.04979937,-0.00079452,-0.05530597,-0.00356393,-0.04211441,0.04541001,-0.00594988,-0.01859427,-0.00138652,0.03061101,-0.03882624,-0.00275759,0.06176025,-0.01747988,-0.00589249,0.02230674,0.00554195,0.08350375,-0.02922544,-0.02217432,0.00719873,-0.00047888,-0.06031081,0.0228327,0.05902902,0.03315458,0.01214276,0.0630786,0.04678592,0.01084894,-0.0215307,-0.02422117,-0.00033035,-0.05424273,0.01430725,0.01159787,0.05105726,-0.25313523,-0.00417726,-0.01906771,0.01389514,-0.00642572,0.00718552,0.04657073,-0.01458916,0.01970178,-0.05311605,0.01008396,0.04953045,-0.00948153,-0.05979477,-0.05833096,0.03304788,0.03057003,-0.03760797,0.04515656,0.00774986,0.05971755,0.0687113,0.2205911,0.01911471,0.00592986,0.05074578,0.02972771,0.0445639,-0.0348771,0.07047012,0.00674602,0.01586834,0.06663083,-0.0415117,-0.0969867,0.0745233,-0.04776786,0.04380228,-0.01371999,0.04141033,-0.02282659,-0.0082542,-0.02640197,0.00038489,0.09522652,-0.00111746,-0.01192874,-0.06060498,0.03075836,0.08231035,-0.11191622,-0.06730689,-0.06359003,-0.0284376,0.04950611,0.093306,-0.00892755,-0.02531087,-0.00752036,-0.00504214,0.03192741,-0.02275633,0.03460173,-0.00621498,0.0006119],"last_embed":{"hash":"xxlxy9","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"xxlxy9","at":1753423451304},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies","lines":[8,161],"size":22035,"outlinks":[{"title":"Delta Lotto Numbers run from menu two of the Ultimate Lottery Software.","target":"https://saliu.com/images/ultimate-lotto-software-61.gif","line":20},{"title":"The best unique delta lottery software calculates deltas, generates combinations for lotto-6.","target":"https://saliu.com/images/delta-lotto.gif","line":24},{"title":"There is ultimate delta lotto software for all pick-3, pick 4 daily lottery games.","target":"https://saliu.com/images/delta-pick-software.gif","line":26},{"title":"_**Lottery Deltas Can Build Effective Lotto Strategies, Systems, Software**_","target":"https://saliu.com/bbs/messages/648.html","line":28},{"title":"_sum-totals, root sums, average, standard deviation, **deltas**, medians_","target":"https://saliu.com/forum/lottery-sums.html","line":30},{"title":"Generate delta lottery reports to create winning lotto strategies, systems.","target":"https://saliu.com/images/delta-lotto-calculate.gif","line":42},{"title":"The delta lotto number reports require a minimum amount of combinations to analyze.","target":"https://saliu.com/images/delta-analyze-draws.gif","line":46},{"title":"Deltas in lottery can reduce millions of combinations and still win the jackpot.","target":"https://saliu.com/ScreenImgs/lottery-delta-filters.gif","line":50},{"title":"_**Reversed Birthday Paradox**_","target":"https://forums.saliu.com/wikipedia-piracy.html","line":62},{"title":"Lottery deltas and restrictions in the past 1000 lotto drawings.","target":"https://saliu.com/ScreenImgs/delta-lottery-draws.gif","line":82},{"title":"Check if delta lottery strategies hit in past drawings and see the skips.","target":"https://saliu.com/images/delta-lottery-strategy.gif","line":112},{"title":"_**filters in static and dynamic**_","target":"https://saliu.com/bbs/messages/919.html","line":116},{"title":"_**REVERSED lottery strategy**_","target":"https://saliu.com/reverse-strategy.html","line":120},{"title":"_**lottery filters at run-time**_","target":"https://saliu.com/filters.html","line":124},{"title":"_**cross-reference lottery strategies**_","target":"https://saliu.com/cross-lines.html","line":126},{"title":"The two lotto delta combination generators are easy to understand and run.","target":"https://saliu.com/images/delta-lotto-generator.gif","line":137},{"title":"The computer processor is tested with delta filters in 6-number lotto games.","target":"https://saliu.com/images/delta-lottery-filters.gif","line":147}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08954274,-0.03668838,-0.02343898,-0.0105826,-0.03992516,0.01434787,0.02775212,-0.01177615,0.0499167,-0.01847104,-0.01488438,0.00997459,0.05078643,-0.00579765,-0.01712496,-0.03303494,0.01097812,0.01494742,-0.05185321,0.00619652,0.03654438,-0.04930609,-0.05805279,-0.06708238,0.0519256,0.01220735,-0.01103648,-0.08244745,0.0012455,-0.19905998,0.01790986,0.03533414,-0.01131625,-0.10039586,-0.0661145,-0.03897218,-0.00320197,0.05988292,-0.03882454,0.00744065,-0.00500612,0.00798438,-0.01438756,-0.02394787,-0.03475354,-0.07709201,0.00414337,0.0290087,0.03292074,-0.03190349,-0.05443167,-0.00547813,0.00146332,0.0434869,0.05041473,0.03633272,0.02058527,0.10520064,-0.01387337,0.01259425,0.05720686,0.02978497,-0.18369323,0.03382662,0.01679576,0.03822237,-0.01504845,0.01601409,-0.00462181,0.08516824,0.0231924,0.03126351,-0.02351223,0.08312945,0.05971218,-0.02009976,-0.06304401,-0.06101616,-0.05106813,0.03658981,-0.02076931,-0.01830781,-0.01365906,-0.0014376,-0.0211523,0.05122512,0.05253247,0.04818578,0.07638644,-0.06366111,-0.0073787,0.01438436,0.03165142,0.05113212,0.03072308,-0.00454374,0.08143295,-0.08781298,0.04907586,0.11942886,0.00251478,-0.00847514,-0.02218269,-0.02400189,0.05468597,0.00295095,-0.03367427,-0.07856659,-0.02403813,0.0446529,0.05975657,0.00214673,0.04627177,-0.0265264,-0.08172349,-0.02120185,-0.00181621,-0.01113048,0.05150387,-0.00710038,-0.03778316,0.03640077,-0.01815377,-0.02215576,-0.01567596,0.00230965,0.02782538,0.06639541,-0.00005891,0.00116366,0.07921508,-0.00798416,-0.11693551,-0.02785308,-0.02046366,-0.01718538,-0.01946911,-0.03785491,-0.01399026,0.04634668,-0.04723101,0.00982603,0.0718585,-0.11340914,-0.02727529,0.10297808,-0.00152574,0.02382783,-0.01633671,-0.00416887,0.02175661,-0.03032487,-0.02173774,-0.05211488,0.02042865,0.01540135,0.08161116,0.10488421,-0.0435723,0.02077863,0.00993317,-0.01735449,-0.03662029,0.15700758,0.02341312,-0.06666371,-0.01468451,0.04462329,-0.0123728,-0.07940991,-0.00649462,0.02949324,-0.06524736,0.03870614,0.07603522,-0.01993465,-0.05201335,-0.04732665,-0.02598429,0.02213796,0.03559503,-0.03303128,-0.04723467,0.02120479,-0.02488642,-0.11276506,-0.0121291,-0.04624344,0.02490815,0.05141401,-0.02190176,0.03044688,0.00207963,0.01988468,-0.00459211,-0.05433528,-0.04138363,-0.04246689,0.05792235,-0.02047927,-0.01018886,0.00481013,0.02169661,0.00853288,-0.03060354,0.07645084,-0.02669325,-0.07372878,0.0857271,0.05269311,-0.05896131,0.00835387,0.02998283,0.06544001,-0.05825038,0.03843509,0.01460961,0.03090259,-0.01537952,0.03835098,-0.03190866,0.06700285,-0.02896696,-0.21295835,-0.03970978,-0.05055562,0.02827332,0.00450505,0.01752699,-0.00801091,-0.01187801,0.00273675,0.09766636,0.06633446,-0.10325771,0.02176339,0.06414296,-0.00909275,-0.02908305,-0.08175652,-0.0543595,-0.02989305,0.03751079,-0.02935561,0.0130259,0.00208631,-0.08138547,-0.0098769,-0.02109104,0.13174129,-0.01730095,0.07894202,-0.02810467,0.04672838,0.00449644,-0.02398146,0.00125957,0.00811018,0.04336961,-0.05658541,0.01391621,-0.02272051,0.00296683,-0.08460801,0.05005037,-0.01637145,-0.05897565,-0.01583926,-0.02402269,-0.00630042,-0.01764309,-0.00236881,0.05606543,-0.00741117,0.03694074,0.0262009,0.0332473,0.07221132,-0.03067729,-0.07300211,0.03874695,-0.01535981,0.03084695,0.01809816,-0.05633289,0.00325286,-0.02583943,0.05918555,0.00764386,-0.01992496,-0.02132218,0.00681998,-0.04305746,-0.00157163,0.0890004,-0.03593317,-0.03503914,0.03071327,0.00750177,0.06570845,-0.03798273,-0.04203887,0.02048234,0.000752,-0.08404893,0.01676636,0.06232221,0.02479404,0.00419593,0.06297173,0.04417061,-0.00341829,-0.02480388,-0.01137056,0.00500602,-0.05927373,0.01225143,-0.01237435,0.03659776,-0.24289489,0.0301599,-0.01067324,0.01164022,-0.01433124,0.00335342,0.03143134,0.00946257,0.02603001,-0.01299109,0.03473401,0.02001261,-0.01501857,-0.04009459,-0.0532245,0.04354362,0.0748907,-0.04140535,0.0440269,0.032718,0.06088001,0.07359364,0.24015336,0.01798087,0.0241623,0.03066706,0.04930056,0.05466444,-0.03473946,0.06603274,0.03307066,0.0286517,0.07724582,-0.02771933,-0.08489616,0.07413212,-0.04831556,0.0529457,-0.01183017,0.03883548,-0.03491047,-0.00238535,-0.03523901,0.01414555,0.1154359,0.02439453,-0.00653134,-0.07339594,0.04842781,0.05694072,-0.11521181,-0.04776821,-0.06170012,-0.02690621,0.04069335,0.04577307,-0.02762526,-0.04093159,-0.00443179,-0.00603434,0.03374462,0.00277261,0.04948329,-0.00143142,0.00095111],"last_embed":{"hash":"z2nw4i","tokens":89}}},"text":null,"length":0,"last_read":{"hash":"z2nw4i","at":1753423451462},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{1}","lines":[10,15],"size":286,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11439323,-0.06823514,-0.0049079,-0.02166955,-0.016052,0.02688584,-0.02862697,0.01822169,0.00849389,-0.030597,-0.00727209,0.01040775,0.03306048,0.00744354,-0.01283576,-0.00937361,0.03757541,-0.01985683,-0.02251627,0.00582055,0.05000765,-0.01674623,-0.00726296,-0.04085827,0.046411,0.04342274,-0.03881277,-0.08113647,-0.01607621,-0.16225921,-0.01008337,0.0019757,-0.01327135,-0.081421,-0.0329604,-0.04017138,0.03025842,-0.00903858,-0.02688545,0.02116515,0.00750923,0.00804336,-0.02559435,-0.0390604,0.00657696,-0.05752816,0.04742936,0.00626381,0.07598618,-0.03305276,-0.05324589,0.06233871,0.02173771,0.02125221,0.00280616,0.00374496,0.01396647,0.08029815,-0.02712945,0.04735754,0.04019394,0.02201311,-0.21232605,0.07182368,-0.03067333,0.05248562,-0.02732026,0.00126508,0.03771814,0.00771672,-0.00523763,0.0128543,-0.02787383,0.10105193,0.08582675,-0.04994909,-0.00901667,-0.02956593,-0.04238332,0.00289862,-0.0324836,-0.00663791,0.0338612,-0.02392612,-0.00295771,0.05572899,0.09831181,0.07760542,0.05377327,-0.07341533,0.00008229,-0.018288,0.06752106,0.0619775,-0.0169818,0.01508821,0.04872923,-0.05957077,-0.00105471,0.10165422,-0.03467816,-0.02564697,-0.03084512,0.00932276,0.02986643,0.01463929,0.00925225,-0.04995648,-0.04799055,0.02151033,0.02240024,0.02231696,0.05811815,-0.02511822,-0.03894799,-0.00488835,-0.03334661,-0.00442641,0.02310118,-0.01126198,-0.02518289,0.0228514,0.01703477,0.01176521,-0.01403923,-0.01048478,0.00350505,0.0449114,0.02835248,0.03450692,0.07636117,0.01890627,-0.1138275,-0.05223027,0.00690347,-0.00584681,-0.01646643,-0.0313162,-0.00053372,0.08064364,-0.04515755,-0.02279596,0.02809606,-0.07828891,-0.05762144,0.09392777,0.01023577,-0.00417903,-0.02914027,0.02860744,0.02422584,-0.02450625,-0.01672151,-0.03240994,0.03387503,-0.00316557,0.14929554,0.1144581,-0.09481226,0.01833844,0.00158117,-0.03795253,-0.01979579,0.14706369,0.00838134,-0.11178547,0.00042742,0.0342315,0.01251148,-0.08573493,-0.0100836,0.02021855,-0.06901436,0.03633912,0.0872101,-0.00781678,-0.01148369,-0.04997345,-0.01260019,0.02009486,-0.00749877,-0.04892769,-0.03295814,0.04445899,-0.02196966,-0.10112388,-0.03303996,-0.05374959,0.03017963,0.00981095,-0.05265226,0.01491303,-0.00245921,-0.02781928,-0.0055529,-0.01395155,-0.05630655,-0.04968073,0.07608616,-0.01844663,0.01274346,-0.00141935,0.00406198,-0.00478122,0.00422614,0.0878616,-0.04147213,-0.03439264,0.07880268,0.01078306,-0.05932812,0.03974929,0.062705,0.06069405,-0.07483383,0.05335518,0.02859674,0.02284571,-0.01118493,0.02970852,-0.00516621,0.08729988,-0.05568712,-0.1982474,0.03271922,-0.08856068,-0.00406845,0.00712126,0.02029095,0.03945936,-0.00340485,0.03367334,0.11209723,0.14045933,-0.07379869,0.02147399,0.04924988,-0.02729195,-0.0600245,-0.06152172,-0.05230906,-0.03980204,0.0180068,-0.02931116,-0.02884702,-0.00974018,-0.04931133,0.03107446,-0.00887898,0.11213289,0.04515668,0.07455901,-0.02404752,0.04977956,0.02722539,-0.00502899,-0.11726248,-0.03291692,0.05760072,-0.08314818,0.01143863,0.01233029,0.00150837,-0.06905857,0.03455475,0.00688645,-0.07577436,-0.01405418,-0.00347917,0.0165124,0.03248607,-0.02878872,0.025446,0.01108004,0.02393103,0.03946527,0.05808279,0.04600003,-0.05683924,-0.01306323,0.01735245,-0.01732942,0.06577356,0.03649363,-0.02758882,0.0026928,-0.08301561,0.01672656,-0.01623604,-0.00694882,-0.00521739,0.05050004,-0.06474135,-0.02150168,0.05217264,-0.02837016,-0.02065249,0.01531894,-0.0020096,0.07405218,-0.01475505,-0.02312351,-0.01604856,-0.05310459,-0.07225448,0.05403475,0.07050801,0.01067561,0.09040339,0.04531687,0.00910883,-0.04933976,0.01619604,-0.02258408,0.00610601,-0.07521674,0.00352274,0.0308774,0.01614149,-0.24708529,0.00502327,-0.01198115,0.03352848,-0.02060821,0.01373444,0.0451591,-0.04725367,-0.01238813,-0.03500229,0.02471604,0.03439346,-0.07453945,-0.04407553,-0.05270487,0.03397794,0.0685426,-0.03195873,0.01805321,0.00418615,0.02608384,0.06093987,0.22362617,0.05603715,-0.02299762,0.04942864,0.01329208,0.05507549,0.00160467,0.07273607,0.01971145,-0.00196301,0.06170915,-0.02900514,-0.07254515,0.05560445,-0.05332064,0.02902809,0.02750905,0.0053605,-0.01449934,-0.02570205,-0.01514595,0.00894765,0.0705862,0.01764062,-0.03037271,-0.05380175,-0.00716383,0.07976767,-0.08362738,-0.06607949,-0.07306004,0.0035742,0.03577698,0.06125003,0.01836434,-0.03625141,-0.00891494,-0.02406226,0.04900313,-0.02818238,0.02724764,0.01133258,0.02479975],"last_embed":{"hash":"tz48ck","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"tz48ck","at":1753423451493},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{10}","lines":[24,24],"size":485,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07805576,-0.05142255,-0.03918035,-0.01797963,-0.03248636,0.04327497,0.01942561,-0.01541738,0.02855742,-0.00816657,0.00431958,-0.01046801,0.01696706,-0.01266439,-0.00043883,-0.04001535,0.01125874,-0.01122345,-0.08084964,0.00380241,0.09850028,-0.06515158,-0.02449754,-0.08270649,0.03152942,-0.0005723,-0.0333119,-0.09339245,-0.01062649,-0.21893375,0.01223361,0.0121655,0.00355184,-0.09991314,-0.05504411,-0.04896967,0.0282237,0.03500334,-0.04451456,0.00477908,-0.00836438,0.00992687,-0.0070674,-0.03701865,-0.00867052,-0.06798466,-0.01424536,0.01174759,0.07739693,-0.00851771,-0.04843866,0.01138787,0.0112195,0.03554703,0.04083802,0.02876784,0.02324357,0.09624218,-0.0289793,0.02353766,0.03797614,0.05271858,-0.17002513,0.05021467,-0.01170196,0.02300454,-0.0034511,-0.04117629,0.00077639,0.06736391,0.01295578,0.0330488,-0.0134388,0.09200065,0.07452178,-0.04635926,-0.06379592,-0.03700642,-0.03889771,0.00791144,-0.05399281,-0.02082767,-0.01316381,-0.01415708,0.0378759,0.06794219,0.05638502,0.07633652,0.07255536,-0.06219166,0.01625696,0.02144338,0.03141864,0.04794897,0.01507548,0.01043242,0.05758085,-0.07198489,0.06103888,0.10905283,-0.02087882,-0.01646649,-0.00594415,0.00311714,0.04994047,0.00459112,-0.04101011,-0.05365874,-0.06086883,0.05268547,0.06919953,0.01438317,0.06795378,-0.03086036,-0.05706811,0.00035634,-0.01607257,-0.03779693,0.0339577,0.00737295,-0.01460124,0.00873056,0.01555789,-0.03167532,-0.00444083,0.00656872,0.03894698,0.03454048,0.0149534,0.01901432,0.06462964,0.00213367,-0.12125869,-0.03244391,-0.03467448,-0.02922327,0.00535416,-0.0281615,-0.03322963,0.01421185,-0.04407397,-0.00806283,0.08827148,-0.08920165,-0.02075937,0.09442434,-0.02579926,0.0090082,-0.00712921,-0.00197329,0.0257966,-0.01133249,-0.01357798,-0.04540125,0.02247882,0.00838084,0.09771231,0.12792376,-0.07082777,0.02953305,-0.00293747,-0.03566831,-0.02526039,0.09555692,0.00609812,-0.07213383,-0.01354602,0.06543908,-0.01293661,-0.09589175,-0.00229753,0.04689007,-0.07698845,0.00558587,0.08876257,-0.00809695,-0.03302077,-0.05093165,0.00843487,0.03033155,-0.02701253,-0.05841818,-0.01761678,0.02481676,-0.05094542,-0.12176049,0.03191925,-0.0713715,0.02412666,0.03487435,-0.00936611,0.0381497,0.00021346,0.0165306,0.00488027,-0.03250444,-0.05150195,-0.04008619,0.07012808,0.00235684,0.0366143,-0.01389438,0.04031263,-0.02693023,0.01209025,0.07125484,-0.02063067,-0.05233917,0.05418893,0.02427434,-0.03080261,0.02768273,0.04189652,0.06969633,-0.07911586,0.05197971,0.01371122,0.02343606,-0.01592234,0.02543181,0.01931579,0.06890274,-0.04115098,-0.20220806,-0.01383088,-0.05854687,0.01333113,0.03592402,0.01491755,0.01709503,0.00249594,0.04300838,0.10545664,0.09668394,-0.08704399,0.01626173,0.06451646,-0.0118581,-0.00812205,-0.1040879,-0.05040107,-0.05904252,0.04766215,-0.02303527,-0.00060227,-0.01670267,-0.06614346,0.03341247,-0.01816094,0.13625218,0.03001454,0.05096387,-0.03799964,0.06707396,0.025189,-0.02476841,0.00972481,-0.00298628,0.02073516,-0.07306376,-0.03157964,-0.00935305,0.00750043,-0.0843116,0.0437845,0.0050882,-0.09982017,0.01176976,0.01013768,-0.01114826,0.01756385,-0.00687391,0.02371919,0.02020915,0.03095278,0.01113999,0.00477719,0.06269252,-0.056063,-0.05892268,0.02651572,-0.01457029,0.0493856,-0.00902087,-0.05076422,0.01227365,-0.04381988,0.05680247,0.0129927,-0.01122213,-0.02075277,0.02348208,-0.04338259,-0.00656052,0.0733762,-0.00021853,-0.00148632,0.04637082,-0.00651211,0.0874715,-0.03226247,-0.04641385,-0.01904433,-0.00704244,-0.09378097,0.01171831,0.05542576,0.03702524,0.03360471,0.07950659,0.03576189,-0.01567548,-0.02934379,0.00726954,0.01070089,-0.04881932,0.01761743,0.00502864,0.02158104,-0.25933802,-0.00076439,-0.0454763,0.0272374,0.00419379,0.0115371,0.03704225,-0.00586887,0.03818032,-0.02880568,0.02353252,0.03368554,-0.00036014,-0.06475875,-0.03329615,0.01064782,0.02332712,-0.04178898,0.05681797,0.0005988,0.08142018,0.04834032,0.22160552,0.02467917,0.00483983,0.04823025,0.03804338,0.01737482,-0.02041413,0.07911424,0.0130239,0.01288691,0.04015062,-0.04816114,-0.05466331,0.08991793,-0.03475612,0.03343811,-0.03580867,0.02052151,-0.02585038,-0.00719015,-0.03662548,0.00154347,0.10440987,0.00105542,0.00659402,-0.04301601,0.02628972,0.06003057,-0.10740704,-0.05972788,-0.05486048,-0.0415325,0.05099413,0.07360732,-0.00218814,-0.03500029,-0.02140416,0.01868874,0.03768029,0.01840022,0.04026787,-0.02400071,0.01441372],"last_embed":{"hash":"16bkd7i","tokens":177}}},"text":null,"length":0,"last_read":{"hash":"16bkd7i","at":1753423451536},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{12}","lines":[27,34],"size":486,"outlinks":[{"title":"Delta Lotto Numbers run from menu two of the Ultimate Lottery Software.","target":"https://saliu.com/images/ultimate-lotto-software-61.gif","line":1},{"title":"The best unique delta lottery software calculates deltas, generates combinations for lotto-6.","target":"https://saliu.com/images/delta-lotto.gif","line":5},{"title":"There is ultimate delta lotto software for all pick-3, pick 4 daily lottery games.","target":"https://saliu.com/images/delta-pick-software.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07466821,-0.0446497,-0.03419328,-0.00730945,-0.02112305,0.01288679,0.00847917,-0.00593855,0.01181121,-0.00405565,0.00259767,-0.00808629,0.02534495,-0.0235374,-0.00433365,-0.03971298,0.016127,0.02079225,-0.05503074,0.02797546,0.04413572,-0.0127324,-0.04281078,-0.04696183,0.04620736,-0.0346031,-0.00346892,-0.07130588,-0.04433696,-0.2221224,-0.00142643,-0.01640408,-0.04102964,-0.09724797,-0.07388184,-0.02772094,0.01451595,0.05132603,-0.00835663,0.02766481,0.00885712,0.01518897,0.00925974,-0.06096392,-0.03073408,-0.06604938,0.02114035,0.01449586,0.03960532,-0.02322175,-0.07972202,0.02527287,0.02484116,0.0439831,0.02841441,0.0459558,0.01528187,0.11568994,-0.00799519,0.02192782,0.05083044,0.0393934,-0.19900276,0.04166004,-0.04592852,0.02552508,-0.01201723,-0.03408402,0.01411477,0.09523529,0.03150753,0.0294779,-0.03452732,0.07517315,0.07488155,-0.01126574,-0.03646481,-0.04069836,-0.0312621,-0.00641108,-0.00934597,-0.0433376,0.00178949,-0.01641448,0.01066517,0.049255,0.09495308,0.02474167,0.03813257,-0.06391524,-0.01888558,-0.00575204,0.03008273,0.04305539,-0.02177165,0.02411009,0.03825706,-0.0393836,0.00819412,0.09926527,0.02085448,0.00328912,-0.03032673,-0.00215081,0.0350718,-0.04650792,0.01389555,-0.04615207,-0.00875103,0.06689607,0.05252072,-0.01666333,0.05815488,-0.01558674,-0.04644201,0.02915126,-0.03273471,-0.01698906,0.01303625,-0.018389,-0.01221782,0.01670871,0.03394825,-0.00321297,-0.03871537,0.04091308,0.02915701,0.08287466,0.02118516,0.01390171,0.07067539,-0.02243094,-0.10754315,-0.03390941,-0.03002508,-0.00521509,0.00196364,-0.02484971,-0.01087377,0.05666725,-0.10751896,0.00107806,0.04579345,-0.06858239,-0.05185099,0.08274413,0.00309214,0.02256907,-0.00942345,-0.01252728,0.00682648,-0.01960437,-0.01701675,-0.03466132,0.01444986,0.00466195,0.05972574,0.1321907,-0.07818328,0.02298017,-0.01739693,-0.05407149,-0.05127024,0.19871627,-0.02146678,-0.09412986,-0.04120021,0.05053288,0.00580422,-0.0683142,0.03021449,0.01297299,-0.04720453,0.0266919,0.05791103,-0.02055001,-0.02317656,-0.03910622,-0.05519855,0.0079075,-0.00255687,-0.0395157,-0.01687413,0.03001343,-0.01098352,-0.11242582,-0.00570934,-0.0644779,0.03378152,0.03278499,-0.03123384,-0.01893167,0.03778036,0.03843524,0.02802394,-0.02988178,-0.02315276,-0.0443366,0.07431742,-0.01001736,0.00156955,0.00285613,0.01652322,-0.00057965,-0.01132391,0.10323268,-0.01438231,-0.03215503,0.06473298,0.03587595,-0.02920154,0.03162571,0.02694835,0.04002626,-0.02954209,0.07370061,0.01767676,0.02324336,-0.00497825,0.01243916,-0.02184387,0.08202609,-0.03834039,-0.21934545,-0.0426326,-0.10169057,-0.01650597,0.02643122,0.01860609,-0.00093312,0.02504851,0.01259353,0.10672878,0.06388991,-0.06915633,0.00362259,0.06813198,-0.01773732,-0.02403571,-0.04692706,-0.06283909,-0.05890198,0.01606929,-0.05919336,0.02983438,-0.01183472,-0.05920503,0.02702052,-0.03646383,0.13991793,-0.03078262,0.05966401,-0.03158158,0.09067757,0.03698783,0.02127121,-0.02416198,-0.01842701,0.03341044,-0.05676326,-0.02799975,-0.00562018,-0.01294733,-0.07535299,0.03111149,-0.02732908,-0.13010624,-0.0188371,-0.00007766,0.00676118,0.02025076,-0.03680711,0.07330547,0.02268623,-0.01490643,0.00512277,0.04727334,0.09015171,-0.0729795,-0.0719438,0.00420379,-0.01924061,0.034705,0.00334787,-0.01173382,0.01473993,-0.07187173,-0.00773158,0.03733817,0.01612647,-0.02898786,0.00433979,-0.03524582,-0.01353954,0.08948755,-0.0338062,0.00467379,0.00456569,0.02198287,0.00756407,0.03742906,-0.02554592,0.01501348,0.02175609,-0.04337218,0.01919284,0.0816407,-0.00570353,0.04002861,0.04208539,0.00350789,-0.00176218,-0.02306926,-0.01161893,-0.02638158,-0.04005926,0.01944361,0.03245903,-0.00944677,-0.24579345,0.04747576,-0.04229052,-0.02848462,-0.00800079,0.05479607,0.02711811,-0.02156645,0.00048743,-0.01159179,0.01803206,0.07291853,-0.01978885,0.01168601,-0.00443844,-0.00741989,0.01983601,-0.04947903,0.05018608,-0.04206211,0.08968934,0.04474297,0.25211105,0.03347809,0.01716866,0.00931328,0.02613559,0.03844962,-0.04791737,0.02983692,0.00530849,0.03459874,0.06604589,-0.02103947,-0.02947341,0.07872696,-0.04198972,0.05590913,-0.0066573,0.01236977,-0.05348976,0.01220161,0.00573779,0.02615767,0.10058785,0.03888208,0.00142068,-0.05701622,0.07814257,0.07165843,-0.08906359,-0.04771363,-0.05329708,-0.01542596,0.00561278,0.06208467,0.00188477,-0.00971826,-0.01057731,-0.01265726,0.02378267,0.00740415,0.02628667,-0.00172154,0.01890152],"last_embed":{"hash":"aaxhaw","tokens":130}}},"text":null,"length":0,"last_read":{"hash":"aaxhaw","at":1753423451587},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{13}","lines":[35,35],"size":430,"outlinks":[{"title":"_**Lottery Deltas Can Build Effective Lotto Strategies, Systems, Software**_","target":"https://saliu.com/bbs/messages/648.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08354709,-0.04553248,-0.04656966,0.02144701,-0.05813222,0.01517965,0.01495238,-0.04096479,0.03630498,0.00872787,0.00083919,0.00110527,0.07681543,0.02881737,-0.01958118,-0.01715461,0.00155542,-0.01327657,-0.08102779,-0.00527278,0.08659676,-0.03264947,-0.02883938,-0.0842116,0.06211505,-0.00074246,-0.01358486,-0.07594983,0.00374657,-0.24029833,0.00435404,0.02929642,-0.03350973,-0.07803027,-0.06537423,-0.05420371,-0.01303461,0.10379151,-0.02263503,0.03311961,-0.00654934,0.00560447,-0.03827502,-0.01643347,-0.00072559,-0.04369166,0.00667377,0.01942689,0.07072083,-0.01103131,-0.02654911,0.03723259,0.0289845,0.02895391,0.03188666,0.01738076,0.00319606,0.10232247,-0.00581766,0.02581298,0.04681417,0.04499975,-0.18947063,0.03469704,-0.02385649,0.01695994,-0.03791573,-0.00007914,-0.0150738,0.03984374,0.03008316,0.02004932,-0.0192703,0.07595313,0.05372057,-0.02777888,-0.07286642,-0.02955514,-0.06517558,0.06529529,-0.04816829,-0.05581346,-0.01795846,-0.01240184,0.01108171,0.01967336,0.0650187,0.08683124,0.07826861,-0.06812609,-0.01325772,0.05939762,0.02153737,0.04637172,0.00431084,0.02764904,0.07124405,-0.05745475,0.01669204,0.10300502,0.02379659,0.00726637,-0.00613933,0.05404323,0.02028744,-0.01220974,-0.04673271,-0.04815223,-0.03609626,0.02486225,0.04589421,-0.03669479,0.07062558,-0.04702929,-0.10787623,-0.02976767,0.03556648,-0.0200264,0.04300093,-0.00493719,-0.04183572,0.00300291,-0.01968211,-0.01332853,0.00433765,0.00782138,0.0140155,0.09123892,0.02121999,0.01461837,0.06009319,0.00456668,-0.09771968,-0.03771418,-0.03013499,-0.0547086,0.004161,-0.02054767,0.02589156,0.01811248,-0.01850464,-0.0130099,0.06311611,-0.07768028,-0.03558353,0.04593997,-0.00671121,0.04706576,0.00091422,0.01986225,0.0002845,-0.03650103,0.01393739,-0.06423935,0.03584636,0.02218533,0.09249953,0.09976219,-0.05008411,0.03455108,-0.00783543,0.00127439,-0.0441751,0.1393234,-0.0124781,-0.07209108,-0.03237793,0.03592651,0.00286024,-0.0793936,0.02066381,0.02951417,-0.0091527,0.02400248,0.08736705,-0.01255419,-0.05650255,-0.05728352,-0.0061226,0.02566743,0.00928112,-0.02638952,-0.04952658,0.05218749,-0.03586837,-0.1026539,0.01263589,-0.04349584,0.04202541,0.05497483,0.01318822,0.03573071,0.0262179,0.03138511,0.01703351,-0.04725234,-0.05025247,-0.06380925,0.07517064,-0.03709934,-0.02238437,-0.00641783,0.02519985,0.00413752,-0.0282163,0.07354018,-0.02958681,-0.07812665,0.09831247,0.04683419,-0.01868898,-0.01814143,0.00448244,0.06931051,-0.07325397,0.06086763,0.00175734,0.03176,0.02073499,0.00288555,0.01250436,0.04199107,-0.05474961,-0.21346398,-0.03349391,-0.05404944,0.0197373,0.02535442,0.03156583,0.00975353,-0.00139346,-0.01083275,0.06972057,0.06488635,-0.06577592,0.01828676,0.04761623,-0.03240793,-0.02728097,-0.09948475,-0.04123564,-0.01938174,0.05569731,-0.01541752,0.01290142,-0.04396125,-0.04173796,0.03504873,-0.05977647,0.14457256,0.03434503,0.02637668,-0.00800547,0.0622113,0.04262664,-0.00537734,0.03099976,0.0490085,0.02359399,-0.06922632,-0.00519756,0.00614061,0.01986923,-0.06831372,0.03295053,-0.02160378,-0.08792494,-0.01721445,-0.03470802,-0.01498213,0.04641704,-0.03682697,0.03961324,-0.00982123,-0.00700705,0.04364856,0.01348532,0.09751192,-0.05171522,-0.07389455,0.03014879,-0.01718788,0.02717581,0.01029071,-0.00310008,0.03305903,-0.03499239,0.01815672,0.04804267,-0.00422624,-0.00981747,0.02640156,-0.04954078,0.00931278,0.08968706,0.00082601,0.00890511,0.03438048,0.03077869,0.06792913,-0.05463682,-0.02515681,0.01253028,0.00063287,-0.08497873,0.00757234,0.05216643,-0.00245854,0.00849866,0.01610002,0.04829948,0.00114412,0.00808058,-0.03507039,0.01307536,-0.04243296,0.01181625,-0.01583606,0.00654217,-0.23247939,0.03228632,-0.0651435,0.04011197,-0.0418586,0.00048587,0.02779412,-0.00295046,0.03264618,-0.01803119,0.0225835,0.02952433,0.03182534,-0.07196178,-0.05105307,0.05143766,0.04390253,-0.07506504,0.01122237,-0.00145461,0.0991313,0.0309784,0.23069318,0.04291442,-0.00656785,0.04492142,0.03013247,0.03714581,-0.01631382,-0.00037308,-0.00613087,-0.00616564,0.04930184,-0.06393875,-0.03216122,0.08922961,-0.01151551,0.024581,-0.01804985,-0.01840053,-0.06189509,0.00140512,-0.03716411,-0.008934,0.11563511,0.02469637,-0.00315772,-0.04521001,-0.00604919,0.0497264,-0.10284717,-0.05366442,-0.03540659,-0.06640529,0.02216447,0.04625338,-0.01376701,-0.03096193,-0.02660503,-0.02183193,0.02772251,0.05557654,0.04401071,-0.01921618,-0.02326777],"last_embed":{"hash":"192n5ez","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"192n5ez","at":1753423451629},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{14}","lines":[36,36],"size":355,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09398519,-0.06773569,0.01601297,-0.03630902,-0.00773763,0.04439009,0.0469745,0.01356343,0.06986536,0.02448179,-0.01762193,-0.0217636,0.04370712,-0.00896389,-0.04237212,-0.01273342,0.03229728,-0.0118842,-0.04481316,0.01193967,0.06920036,-0.05165326,-0.0374578,-0.07640139,0.08895473,-0.01269868,-0.0485765,-0.071589,-0.03492274,-0.2067828,0.01273658,-0.00314489,-0.02697972,-0.09321397,-0.0405084,-0.05102096,0.03927825,0.05518158,0.01103393,0.00754478,0.00664575,-0.0220672,-0.019958,-0.02684228,-0.00323784,-0.0704015,-0.02559538,0.02435172,0.04999215,0.01054348,-0.05745271,-0.01244241,0.0267581,0.01405583,0.03727438,0.03716272,0.02228104,0.06367946,0.02080551,0.00918379,0.03445704,0.02416614,-0.1808584,0.01378436,0.00859397,0.05044037,-0.00067352,-0.02997767,0.00592202,0.03860556,-0.01000886,0.02320966,-0.01070047,0.0820513,0.07748143,-0.04650388,-0.0597693,-0.05228062,-0.0619521,0.0023473,-0.04746158,-0.04256215,-0.01838771,0.03027521,0.02143213,0.04583314,0.1083876,0.06868748,0.0864719,-0.07256678,0.02452669,0.01141618,0.03837825,0.04918864,0.00853881,0.06840698,0.0634778,-0.07541537,0.04890795,0.13417949,0.02251367,0.01501595,-0.02054114,-0.01812024,0.02931074,-0.02612512,0.00540235,-0.04828674,-0.02166116,0.04221493,0.02841475,-0.02709659,0.01935219,-0.05977591,-0.0275942,-0.04519556,0.00660742,-0.03631426,-0.00310071,-0.00559026,-0.021153,0.03045353,0.00620399,0.00915374,0.0037006,0.02919511,0.06561898,0.06689143,0.03594619,0.04171638,0.04623053,0.01797743,-0.12951736,-0.03318471,-0.01779515,0.0056164,0.01506937,0.01400237,0.00686988,0.07092699,-0.05732898,0.02544589,0.0312654,-0.08093341,-0.02059007,0.09845962,-0.04028831,0.00807946,0.04160577,-0.00166243,0.01241045,-0.00695864,-0.01273608,-0.04870179,-0.00847248,0.02161657,0.06821559,0.09865428,-0.07045616,0.02374514,-0.03790571,-0.06394532,-0.0020734,0.12252371,-0.01043949,-0.0568062,-0.00688375,0.05047746,0.02084197,-0.07783218,0.01056657,-0.02074752,-0.05022354,0.02429203,0.06624166,-0.00866306,0.01796186,-0.05986997,-0.02136263,0.01340328,0.00403331,-0.02542272,-0.07924356,0.04018475,-0.03832075,-0.09779431,-0.06012467,-0.0435019,0.02047607,0.05747637,-0.05213478,0.03070532,-0.03371062,-0.01936574,-0.0090207,-0.05813929,-0.03424145,-0.01767891,0.03646294,0.01004258,0.03449954,0.00515894,0.04695337,-0.03518464,0.01267389,0.0677152,-0.06061074,-0.0243006,0.06614684,0.03163439,-0.04725584,-0.00225416,0.0354196,0.07962072,-0.05978445,0.04818747,0.01463841,0.01035413,-0.02382581,0.02479854,-0.04824748,0.05749986,-0.0718917,-0.21494247,-0.02970634,-0.02619675,0.01065031,0.01288614,0.00808703,0.04406443,0.04396587,0.01169637,0.10142244,0.08383989,-0.05405124,-0.00484841,0.03301926,-0.02862428,-0.04661763,-0.08860759,-0.06044132,-0.03040774,0.05632917,-0.03212464,0.0227526,0.01988808,-0.04004361,0.00677872,-0.02675309,0.15207817,-0.01642593,0.0561096,-0.04025169,0.07921115,0.02422031,-0.01489769,-0.0122768,0.00855325,0.03366439,-0.08401462,0.03702783,-0.06467612,-0.00414047,-0.08227762,0.03339972,-0.00287474,-0.07014723,-0.0155103,0.00315877,0.01455734,-0.05688928,-0.01857792,0.01572406,-0.01327115,0.01682601,0.04284594,0.02416138,0.06271061,-0.03358201,-0.05619954,0.00423902,-0.03858737,0.04298702,0.03662284,-0.03671752,0.01775469,-0.03147855,0.04229936,-0.02408893,0.00969097,-0.03371326,0.00626903,-0.04835737,-0.01197579,0.10712248,-0.04030281,-0.01397232,0.02879973,0.03734164,0.07840235,-0.04984864,-0.03918843,-0.0100435,0.00965437,-0.09474789,-0.02058882,0.01555597,0.01094129,0.04846802,0.04203719,0.01356164,-0.02561519,-0.0057903,-0.03634078,0.01252413,-0.04772327,0.01823566,-0.00698386,0.06233789,-0.25682592,0.01230351,-0.06602837,-0.00344629,-0.00153332,-0.02117125,0.023524,-0.00874996,0.02905726,0.01066159,0.04712094,0.02820235,0.01019027,-0.04979745,-0.02953912,-0.00720914,0.02697453,-0.00731646,0.07757385,0.0117419,0.06965438,0.03913344,0.22697315,-0.00143432,-0.00898965,0.03147431,0.03753754,-0.01575902,0.00164932,0.05071682,0.03297629,0.02453643,0.09186796,0.00063831,-0.05875862,0.12235058,-0.04182431,0.03837215,-0.02709192,0.04968923,-0.00013568,-0.01131807,-0.02073574,0.02085898,0.0912319,-0.00735992,-0.02567081,-0.07577,0.07813665,0.06904908,-0.10875388,-0.05016677,-0.04530998,-0.01290641,0.01593008,0.04948221,-0.02553582,0.02457399,0.00752122,0.00274027,-0.00039903,-0.01209876,0.03266511,0.03888588,-0.0199057],"last_embed":{"hash":"1ftohka","tokens":109}}},"text":null,"length":0,"last_read":{"hash":"1ftohka","at":1753423451666},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{15}","lines":[37,38],"size":287,"outlinks":[{"title":"_sum-totals, root sums, average, standard deviation, **deltas**, medians_","target":"https://saliu.com/forum/lottery-sums.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06669135,-0.0537266,-0.02483418,-0.02840484,-0.06794859,0.04206676,0.05235348,0.01304482,0.06522264,-0.01128706,-0.009967,-0.00128865,0.00920219,-0.00842681,-0.05054933,-0.04368912,0.00789223,-0.04496666,-0.10360189,0.03091655,0.09943163,-0.0875835,-0.03974143,-0.08927444,0.07545248,0.03532321,0.0211455,-0.05621483,-0.01948385,-0.23576003,0.0026348,0.02317814,0.00810368,-0.09350561,-0.0508791,-0.03020209,-0.01309592,0.08951326,-0.03185587,0.01952716,0.02628544,-0.01898173,0.03576741,0.01525804,-0.04205132,-0.05456655,-0.05806696,0.00315884,0.0100153,-0.00616665,-0.02471772,-0.00341488,0.00627685,0.04651438,0.03381757,0.0536595,0.0236024,0.08048148,0.02753358,-0.00334905,0.06637597,0.01300853,-0.17626296,0.04454775,-0.01315774,0.00433843,-0.05067737,-0.02636313,-0.02518458,0.04223027,0.03107909,0.04308253,-0.0251516,0.10067822,0.06592346,-0.0449713,-0.06558325,-0.0599785,-0.04142625,-0.00534542,-0.04693994,-0.02071791,-0.0253157,-0.00544403,-0.01127743,0.06886971,0.05260647,0.04739408,0.05530225,-0.05487945,0.00173847,0.01508587,-0.02181271,0.05253299,-0.00567276,0.0252707,0.05171228,-0.07213242,0.0237524,0.07950434,0.02933053,0.04417751,-0.05537266,0.01161991,0.02710844,-0.01573489,-0.02251203,-0.10711116,-0.02767757,0.02403519,0.05661554,-0.02376956,0.06916387,-0.02849796,-0.06438477,-0.00463856,0.03597347,-0.03442468,0.04552743,-0.01878411,-0.01858246,0.06405726,-0.03170721,-0.01662015,0.00348521,0.04041101,-0.0071314,0.07946867,0.04051773,0.00773593,0.05231617,-0.04628583,-0.08628857,-0.04603127,-0.01216168,0.02628824,0.00110403,-0.00273403,-0.01851121,0.03371482,-0.03973428,-0.02318157,0.05418685,-0.05119687,-0.05206666,0.07887699,-0.02379176,0.02451247,0.02163016,0.03793759,0.00878936,-0.00823817,-0.04882442,-0.05231192,0.0102599,0.01767558,0.05499505,0.10882645,-0.06338704,0.0290837,0.01447074,-0.04689197,-0.00520828,0.12324947,0.00580541,-0.01695904,-0.03643242,0.00891137,0.0058066,-0.10578528,0.02823314,0.04594057,-0.05135524,0.03983609,0.0667932,-0.02239939,-0.04894407,-0.05592299,0.03607853,0.04481202,0.01953196,-0.03959621,-0.05226995,0.01379122,-0.02878318,-0.0913931,-0.0377851,-0.03836997,0.03727992,0.03643265,-0.00299303,0.04705372,0.0096883,0.02734255,0.00829763,-0.06333355,-0.01360899,-0.03510405,0.07923853,0.00438846,0.01034601,-0.00807976,0.03925231,-0.01843132,0.00022885,0.06683636,0.02860479,-0.06236818,0.08584338,0.02749238,-0.04517474,-0.02082257,-0.00228166,0.06361006,-0.08636662,0.04714956,0.02783323,-0.0080845,-0.00751518,0.0210883,-0.00669963,0.03058856,-0.00223985,-0.18876576,-0.04719517,-0.04240043,0.03071769,0.04377679,0.03277143,0.01449616,-0.00681129,0.04684957,0.09743649,0.03879683,-0.05731195,-0.02094912,0.05817767,-0.01939112,-0.0131023,-0.09263524,-0.05082116,-0.04210064,0.04338261,-0.05287562,0.03057213,-0.04181415,-0.06332349,0.04866297,-0.02543154,0.14041635,0.01847009,0.04466403,-0.03799141,0.04509446,0.04009035,-0.00203641,0.01513759,0.02055455,0.00381539,-0.08617594,-0.01971386,-0.04344875,-0.00490383,-0.10677914,0.02988338,-0.04393161,-0.04222283,-0.03405783,-0.01631731,-0.03740802,0.03609607,-0.00913043,0.08549328,-0.01921432,0.05552411,0.04926967,0.03088052,0.05765488,-0.03913856,-0.07565308,0.02807975,-0.00539802,0.06880304,0.00476679,-0.02721804,0.01091234,0.0036836,0.03380572,0.02587062,-0.00337509,-0.02830871,0.04935288,-0.02268991,0.013313,0.10826758,-0.00921314,-0.02906044,0.03178576,0.00178129,0.07293355,0.0073463,0.01003242,-0.00475675,0.03878649,-0.09656605,-0.01917518,0.05206939,0.02023981,0.03050165,0.05601962,0.04863808,-0.04525638,-0.02480451,-0.02217778,0.04985633,-0.03440006,0.00680348,-0.02427892,-0.0088366,-0.23475447,0.04317787,-0.04375644,0.04166187,-0.01554398,0.01536257,0.02232336,0.02330875,0.03181511,-0.05540309,0.01766498,0.04585841,0.00648856,-0.07542359,-0.03495614,0.01268383,0.00066535,-0.05838675,0.01667905,0.01172806,0.10671302,0.04424238,0.20852056,-0.0273097,-0.0193389,0.00381904,0.06085952,0.05837079,-0.03073562,0.03965883,-0.04610946,0.02588489,0.0931867,-0.04635895,-0.08250345,0.05947683,-0.06433003,0.04007849,0.00779196,-0.00697682,-0.01091272,-0.04055212,-0.01640456,0.02681814,0.12261031,0.03974366,0.00889998,-0.05625118,0.06154124,0.06790651,-0.10070886,-0.0415381,-0.0359104,-0.02862752,0.01151869,0.07238257,-0.01385961,-0.02813215,-0.001329,-0.00458391,-0.01537045,0.01474937,0.05409417,-0.02006235,-0.00090686],"last_embed":{"hash":"13qceuu","tokens":379}}},"text":null,"length":0,"last_read":{"hash":"13qceuu","at":1753423451811},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#{16}","lines":[39,44],"size":1351,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03684643,-0.03062808,-0.03530416,-0.02934453,-0.02705502,0.04474809,0.04044106,-0.00174304,0.06702252,0.00661905,-0.01504686,0.01350349,0.02077585,0.00992848,-0.05986382,-0.04630947,0.01728366,-0.03380004,-0.06322005,0.00659384,0.05957102,-0.07713729,-0.05090157,-0.10217047,0.06258886,0.00512346,-0.01428084,-0.06632089,-0.03844545,-0.2512171,0.02079767,-0.00799159,0.01309602,-0.07796883,-0.07373762,-0.04349956,0.00884743,0.07422932,-0.02953273,0.00763952,0.0218987,-0.01028662,0.01138275,-0.01877287,-0.01550232,-0.04728469,-0.02295347,0.00018811,0.04996981,-0.00535329,-0.07091789,-0.03407278,0.02945448,0.03131774,0.05492241,0.01483428,0.02842742,0.08650587,0.02209566,0.00214083,0.05276173,0.02473474,-0.19774361,0.04062488,-0.04950224,0.02429178,-0.02802575,-0.02710688,0.00099887,0.04449061,-0.00779352,0.02470852,-0.03143692,0.07213952,0.08281006,-0.05639917,-0.07480812,-0.04752858,-0.03838748,-0.00825716,-0.03763749,-0.00754369,0.00156965,0.0280357,0.02165664,0.05468598,0.08216207,0.08973695,0.07375094,-0.04648355,0.02450012,0.02850672,-0.00553045,0.0511106,-0.01502391,0.0399164,0.07886261,-0.07542932,-0.00013109,0.0999558,0.022832,0.01364071,-0.00618005,0.00853498,0.03942135,-0.02780274,-0.0360889,-0.05263006,-0.03384092,0.0416917,0.03233275,-0.01661862,0.05040402,-0.02969956,-0.05460495,-0.0033539,0.00687756,-0.00903013,0.00932819,0.04298323,-0.00223176,0.01669663,-0.04589029,-0.01356501,0.00751626,0.02752144,0.03620447,0.06085101,0.03815483,0.03567244,0.06866063,0.01388597,-0.13644025,-0.01523105,-0.02850521,-0.02865004,-0.01057998,0.00583713,-0.01663788,0.00262599,-0.05404126,0.03865642,0.05555835,-0.07765096,-0.01980717,0.10018039,-0.02101002,0.03129748,-0.00149779,-0.01681101,0.01867181,-0.01237952,-0.0371516,-0.04452094,0.00721246,-0.00410531,0.05072838,0.1195434,-0.07575326,0.03624407,-0.01408117,-0.05255468,-0.00244687,0.07433914,0.01424356,-0.03924869,0.00448399,0.06323268,-0.00233531,-0.09904934,0.01431568,0.03490257,-0.04780813,-0.00603513,0.06723687,-0.02294094,-0.03268915,-0.06494114,-0.0065843,0.06447285,0.01594607,-0.02624167,-0.0745677,0.03088679,-0.02552482,-0.1120156,0.01109144,-0.0570522,0.04633165,0.06274537,-0.01469973,0.0271167,0.00577815,0.05625082,-0.00593892,-0.07292529,-0.01335721,-0.03205383,0.04265295,-0.01143625,0.07197607,-0.01137818,0.03281305,-0.01124559,0.00609757,0.05696987,-0.0078564,-0.04430389,0.10242532,0.03607672,-0.05519398,-0.01943973,0.01697631,0.05139149,-0.05577622,0.05359462,0.01295279,0.00931036,-0.0226208,0.00812188,0.00065637,0.05236652,-0.01445675,-0.21088167,-0.01898723,-0.02586071,0.03529875,0.03939582,0.00675616,0.01707565,-0.01005821,0.04816979,0.088935,0.10124379,-0.05455798,-0.01258822,0.05312565,-0.02164863,-0.02181385,-0.10385983,-0.04632792,-0.04189291,0.05636965,-0.02678603,-0.00650907,-0.00779871,-0.04750057,0.01160034,-0.03027054,0.15664443,-0.0223025,0.04860662,-0.03626079,0.05199112,0.02030993,-0.01070369,0.00914053,0.00081431,0.05089637,-0.08932324,-0.0103119,-0.03058832,0.00543576,-0.11403022,0.02665877,0.02040243,-0.08099528,-0.04252108,0.01907208,-0.0066029,-0.02080519,-0.04756379,0.02441096,0.00535056,0.00315233,0.05040359,0.02067129,0.06670783,-0.07519337,-0.07300332,0.01631328,-0.03066363,0.03336209,0.01005797,-0.04388165,0.02389256,-0.03895048,0.04432969,-0.01513915,-0.00702757,-0.01010324,0.03621328,-0.03408984,-0.00897318,0.10566264,-0.0383886,-0.01189929,0.06205222,0.00112995,0.08537527,-0.03499989,-0.02063728,0.01340453,0.0104788,-0.09487499,-0.01952991,0.01836702,-0.0018114,0.05596668,0.07320571,0.02194414,-0.02981944,-0.01187906,-0.00837315,0.00727194,-0.03349422,0.01114953,-0.03025566,0.02228688,-0.24538393,0.00082543,-0.0344315,0.01415332,0.00196563,-0.0076774,0.05814201,0.02897318,0.05352211,-0.04028258,0.01819244,0.04475299,0.01345203,-0.08287094,-0.01495195,-0.00058939,0.00593627,-0.00272523,0.03706048,0.01086593,0.09016158,0.02169371,0.21162137,-0.02113124,-0.0032517,0.0351937,0.04874009,-0.01030962,-0.01360225,0.04236475,-0.00207247,0.01108332,0.08275461,0.00271503,-0.06866663,0.08714017,-0.05078857,0.03571463,-0.02403825,0.02666516,0.00396733,-0.00199871,-0.0004172,0.00888612,0.09263033,0.03671882,-0.00141296,-0.06563287,0.0384666,0.08035025,-0.10515475,-0.05828775,-0.03588229,-0.03958013,0.03233796,0.07030775,0.00346294,-0.00341854,0.01682251,0.00038032,0.0064434,-0.00945355,0.06879601,-0.01904539,0.02837398],"last_embed":{"hash":"j3bcl8","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"j3bcl8","at":1753423451935},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>","lines":[45,84],"size":4905,"outlinks":[{"title":"Generate delta lottery reports to create winning lotto strategies, systems.","target":"https://saliu.com/images/delta-lotto-calculate.gif","line":5},{"title":"The delta lotto number reports require a minimum amount of combinations to analyze.","target":"https://saliu.com/images/delta-analyze-draws.gif","line":9},{"title":"Deltas in lottery can reduce millions of combinations and still win the jackpot.","target":"https://saliu.com/ScreenImgs/lottery-delta-filters.gif","line":13},{"title":"_**Reversed Birthday Paradox**_","target":"https://forums.saliu.com/wikipedia-piracy.html","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03580517,-0.03245689,-0.03438818,-0.02863039,-0.02625568,0.04567255,0.04032988,-0.00154715,0.06757621,0.00718545,-0.01553151,0.01189809,0.02202857,0.01061013,-0.05999032,-0.04539574,0.01704506,-0.03637153,-0.06389098,0.0077156,0.06175232,-0.07681197,-0.04935413,-0.10286683,0.06347205,0.0077236,-0.0154764,-0.06613716,-0.03754647,-0.25134036,0.02120315,-0.00830958,0.01079669,-0.07643996,-0.07420962,-0.04455553,0.01038469,0.07322355,-0.02709604,0.00965444,0.02271988,-0.00982328,0.01077858,-0.01913544,-0.01522395,-0.04663488,-0.02436679,-0.00040001,0.05002567,-0.00309149,-0.06887625,-0.03380706,0.02929572,0.03119678,0.0551175,0.01307284,0.02733128,0.08771449,0.02376821,0.00177913,0.05212371,0.02264492,-0.19872092,0.04004264,-0.05092495,0.02457844,-0.02855048,-0.02887893,0.00123402,0.04330336,-0.0060801,0.02249707,-0.03182486,0.07317583,0.08013317,-0.0595038,-0.07454077,-0.04783532,-0.03489964,-0.00635591,-0.03907693,-0.00766631,0.00374633,0.02894724,0.02353856,0.05375995,0.08056913,0.08879996,0.07336877,-0.04385842,0.02528096,0.02551355,-0.00783376,0.05237237,-0.01588699,0.04046392,0.07920165,-0.07516852,-0.00206448,0.09972233,0.02423321,0.01221747,-0.00611093,0.00844657,0.03844728,-0.02557605,-0.03692195,-0.05348394,-0.03286118,0.04158353,0.03045694,-0.01524134,0.04818284,-0.0299689,-0.05337491,-0.00521263,0.0075441,-0.00896138,0.00707381,0.04469612,-0.00230403,0.01620322,-0.04585491,-0.01217938,0.00789097,0.0267346,0.0365952,0.05953878,0.03768142,0.0359185,0.07026367,0.01249277,-0.1357035,-0.01385478,-0.02686889,-0.02959181,-0.0102977,0.00607794,-0.01882466,0.00093161,-0.05114016,0.04203532,0.05546572,-0.07639533,-0.02012803,0.10031465,-0.01942063,0.03255344,-0.00048899,-0.01722852,0.01728298,-0.01250687,-0.03720033,-0.04512978,0.00746318,-0.00243628,0.05290972,0.11920942,-0.0733414,0.03559726,-0.01682987,-0.05373455,-0.00329777,0.07050446,0.01281966,-0.03884535,0.00509197,0.0652844,-0.00164686,-0.10040578,0.015622,0.03694939,-0.04656248,-0.00741699,0.06980082,-0.0234803,-0.03244005,-0.06474575,-0.00718772,0.06584667,0.01583059,-0.02617659,-0.07479997,0.03051657,-0.02746961,-0.11124272,0.010944,-0.05708889,0.04529155,0.06328174,-0.01435449,0.02910242,0.00490651,0.05613324,-0.00514518,-0.0747876,-0.01394169,-0.03204108,0.03987256,-0.01300414,0.07279402,-0.01375469,0.03112046,-0.01244617,0.00645132,0.05641743,-0.00628503,-0.04051879,0.10042154,0.03593873,-0.05482349,-0.02176299,0.01507416,0.05203197,-0.05580755,0.05446969,0.01172203,0.00935626,-0.02375689,0.00824796,0.00082265,0.0533824,-0.01257704,-0.21191096,-0.01778518,-0.02585051,0.03473006,0.03971179,0.00412037,0.01803902,-0.0087344,0.04711653,0.08806063,0.10188818,-0.05365766,-0.01213449,0.0522816,-0.02314313,-0.02050935,-0.1024357,-0.04590652,-0.0408796,0.05465479,-0.02636475,-0.00503053,-0.00737356,-0.0465041,0.01306266,-0.02900677,0.15712357,-0.02126168,0.05036499,-0.03759233,0.05169591,0.01965397,-0.00999552,0.007029,-0.00052966,0.04932328,-0.09020015,-0.01187292,-0.03109508,0.00421421,-0.11326068,0.02778325,0.02108577,-0.07918053,-0.04043233,0.01853416,-0.00497472,-0.02090147,-0.0481139,0.02245215,0.00469757,0.00301287,0.05093769,0.02115722,0.06590034,-0.07793824,-0.07259966,0.01743601,-0.03039875,0.03270866,0.0122424,-0.04320529,0.02161663,-0.04013218,0.04420455,-0.01482081,-0.00569217,-0.0106323,0.03700504,-0.03405713,-0.00874659,0.10788317,-0.0413794,-0.01090712,0.06281781,-0.00238613,0.08415482,-0.03505615,-0.01980603,0.01371723,0.01114922,-0.093856,-0.01912246,0.01752444,-0.00232731,0.05610146,0.07165048,0.02246478,-0.03119187,-0.01248628,-0.00801584,0.0087731,-0.03313527,0.01302854,-0.03190803,0.02209174,-0.24650095,0.00112978,-0.03580612,0.01212091,0.00296763,-0.00909555,0.05843144,0.02884302,0.05411086,-0.04096407,0.01629252,0.04424382,0.01443474,-0.08155436,-0.01605044,0.00300453,0.00758892,-0.00376566,0.03701579,0.0093476,0.09149712,0.02037856,0.21241082,-0.02297413,-0.0021611,0.03415421,0.04853956,-0.01081566,-0.01299433,0.04179721,-0.00298068,0.01060001,0.08466361,0.00243628,-0.06713434,0.08956537,-0.05068083,0.03734077,-0.02386014,0.02501084,0.0052807,-0.00478981,-0.00040239,0.00949067,0.09107509,0.03609647,-0.00208911,-0.06444845,0.03751966,0.08117668,-0.10505109,-0.05738298,-0.03513144,-0.04044123,0.03504464,0.07064548,0.00236869,-0.00391818,0.01828255,0.00185242,0.00486922,-0.00891453,0.06872064,-0.02091643,0.02929806],"last_embed":{"hash":"1chbwcl","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"1chbwcl","at":1753423452096},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{1}","lines":[47,70],"size":3948,"outlinks":[{"title":"Generate delta lottery reports to create winning lotto strategies, systems.","target":"https://saliu.com/images/delta-lotto-calculate.gif","line":3},{"title":"The delta lotto number reports require a minimum amount of combinations to analyze.","target":"https://saliu.com/images/delta-analyze-draws.gif","line":7},{"title":"Deltas in lottery can reduce millions of combinations and still win the jackpot.","target":"https://saliu.com/ScreenImgs/lottery-delta-filters.gif","line":11},{"title":"_**Reversed Birthday Paradox**_","target":"https://forums.saliu.com/wikipedia-piracy.html","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08608709,-0.0582596,-0.02465202,-0.02547682,-0.03189125,0.05834846,0.04210424,0.00733303,0.08616851,0.01403643,0.00956324,-0.00543211,0.04289265,0.02070461,-0.03637565,-0.04430327,-0.00767303,0.0205791,-0.03822023,-0.01031339,0.05940859,-0.07549775,-0.0522903,-0.08034239,0.07147993,0.04465314,-0.03178174,-0.06646069,0.01057605,-0.24413131,0.01088172,-0.02066901,-0.02840287,-0.08103535,-0.05221088,-0.03216971,0.02889085,0.06722223,-0.01022552,0.03250964,0.03216157,-0.01135706,-0.02500135,-0.03671528,-0.02172292,-0.07130732,-0.02157611,-0.00373561,-0.00701018,0.00313928,-0.0571574,-0.01495281,0.03512234,0.02469887,0.05753833,0.01421494,0.01296076,0.06918451,0.00676765,-0.01540779,0.0815196,0.01013392,-0.21173275,0.03938556,-0.04587092,0.01873741,-0.04045611,-0.01162219,0.02209183,0.11395877,0.01296048,-0.0080552,-0.04791145,0.07764842,0.09002303,-0.04467862,-0.03671749,-0.0347776,-0.03934148,-0.00375312,-0.03258076,-0.040985,0.00384933,0.00550308,0.00388923,0.06811747,0.0934571,0.06654504,0.0489619,-0.04140769,0.03919211,0.01299255,0.02488077,0.02385617,-0.01220231,0.04967843,0.08480939,-0.05746255,0.01631688,0.11712407,0.03680059,0.01197236,-0.03044966,0.00901136,0.02478422,0.01537266,-0.02523525,-0.06080906,-0.03087639,0.03813138,0.02243874,-0.00592135,0.02980822,-0.01083427,-0.0428369,0.00352458,0.02669905,0.02663939,-0.00164456,0.00031072,-0.02793899,0.00888452,0.00213792,0.0479424,-0.01785082,0.01634879,0.03968778,0.06294462,0.04226239,0.01022341,0.08277454,-0.02473092,-0.11675058,-0.02792255,-0.02840746,-0.00466701,-0.01028448,-0.02986519,0.01948152,0.03595213,-0.03582395,0.0568877,0.037641,-0.05255837,-0.0262648,0.1214421,-0.02358214,0.02649069,0.04417237,-0.00368104,0.01263713,-0.01608806,-0.01907732,-0.08248966,0.02874706,0.01893962,0.02970665,0.11511915,-0.0756744,0.02905849,-0.05619291,-0.04339815,-0.04017895,0.13949057,-0.00941593,-0.03705796,-0.00685058,0.04270685,0.03382114,-0.10031477,0.03879638,0.04591248,-0.0409724,0.00255505,0.07770789,-0.02048394,-0.04653092,-0.03529667,-0.01268608,0.04035459,0.04060964,-0.06367546,-0.06868679,0.02186124,-0.03001536,-0.09651586,-0.0309204,-0.0487347,0.01816361,0.05309846,-0.00210922,0.03607945,-0.01012522,0.01422978,-0.01148175,-0.08581585,-0.01576588,-0.01196279,0.03690684,-0.00766024,0.04490834,-0.02658188,0.03007103,-0.00241512,-0.00634383,0.05749675,0.00264695,-0.04685655,0.07618949,0.03482668,-0.03509983,-0.0071106,0.02307826,0.03367031,-0.04813569,0.02918806,0.01010018,0.03517259,-0.02611737,-0.01639047,-0.02551215,0.05664196,-0.01457689,-0.20902021,-0.04254385,-0.03530236,0.02557093,0.00001599,0.00758727,0.01356865,0.00500665,0.0177518,0.08191837,0.06867553,-0.07700704,0.00508031,0.02193755,-0.01619001,-0.01200378,-0.08403766,-0.06136915,-0.03743865,0.04182467,-0.01722776,0.00286085,-0.04682042,-0.07069977,0.05170621,-0.01403958,0.14112802,-0.01185434,0.06869803,-0.01913182,0.06082646,0.02067903,-0.01108503,-0.00797416,0.00653548,0.06149082,-0.08411194,0.00153234,-0.02471641,-0.02311913,-0.1133575,0.0268558,-0.00430531,-0.04670137,-0.04301766,0.0351494,0.0018064,-0.01253333,-0.04491906,0.03382521,-0.00862028,-0.00228273,0.02963093,0.0212315,0.04120071,-0.04165841,-0.04215044,-0.01147354,-0.0162193,0.05426925,0.02301097,-0.02951675,-0.01792872,-0.00735356,0.03255207,-0.04058445,0.01606987,-0.03689095,0.0240782,-0.02450437,-0.00906239,0.12346525,-0.03054859,-0.02773766,0.07187781,-0.02674989,0.03027438,-0.02707628,-0.01522967,-0.0219911,-0.00079912,-0.09875048,-0.00119041,0.03173938,0.02803929,0.02653648,0.05751149,0.00090251,-0.04376807,-0.00627719,-0.04340725,0.00550429,-0.05680904,0.01526666,0.00232627,0.02654273,-0.25298044,0.01064435,-0.04570004,0.01648019,0.00435931,-0.0108554,0.04964998,-0.00038044,0.01640537,-0.01373333,0.00148811,0.06501178,0.00291937,-0.04392637,-0.06876136,-0.01053355,0.02966175,-0.01785343,0.03927425,-0.00004957,0.09273364,0.02474239,0.24188471,0.00779778,0.01920763,0.04948709,0.02322612,-0.00299725,-0.01220083,0.02987934,0.02545064,0.01336013,0.08106543,-0.00190094,-0.03626826,0.07020814,-0.05725572,0.04113626,0.00040242,0.00120123,0.01770277,-0.00975905,0.02333066,0.02290835,0.12060758,0.04404119,0.01699053,-0.05357226,0.0631455,0.07371922,-0.10901026,-0.04347526,-0.04358018,-0.02813602,0.02700649,0.07002559,-0.02402056,-0.03718035,-0.01374102,-0.01815012,0.00390294,0.00094821,0.0532514,-0.00668781,0.0159445],"last_embed":{"hash":"1w92jht","tokens":139}}},"text":null,"length":0,"last_read":{"hash":"1w92jht","at":1753423452253},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{2}","lines":[71,71],"size":332,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07323792,-0.06268022,0.00379156,-0.03861917,-0.02982518,0.06051651,0.07338119,-0.00082408,0.06245278,0.0256314,-0.00961912,-0.01412994,0.01586636,0.0286994,-0.0520342,-0.05623198,-0.00537489,0.0090475,-0.05619378,0.01666127,0.08236866,-0.05600262,-0.05203879,-0.08752394,0.04664025,0.02265518,-0.00939928,-0.08796206,-0.02716889,-0.22617251,-0.00973413,0.01780604,-0.01255914,-0.0783216,-0.0622633,-0.05377423,0.01978101,0.08669879,-0.01934369,0.01273032,0.04572801,-0.01938876,-0.01721847,-0.03316228,-0.02395014,-0.07414243,-0.02897071,0.00987879,0.03223095,-0.02522438,-0.03114021,-0.01138246,0.01752713,0.03503151,0.05939228,0.01416269,0.0357023,0.06574244,0.02467862,-0.01042149,0.06238914,-0.0083485,-0.22608556,0.03901246,-0.05037326,0.02553371,-0.04315309,-0.01129307,0.00246945,0.10158377,0.01421621,0.00721765,-0.02250128,0.0665881,0.07812545,-0.02443772,-0.05899701,-0.06847248,-0.0488231,-0.01325422,-0.05860317,-0.026599,-0.00906562,0.017357,0.03009433,0.04296138,0.07968935,0.08424278,0.07089203,-0.03018385,0.03474769,0.00271641,0.01964897,0.03066877,-0.02813531,0.04351755,0.04314892,-0.04836493,-0.00620062,0.11044104,0.03818696,0.02547739,-0.0181745,0.00960045,0.03528619,-0.00022288,-0.03526596,-0.07404579,-0.04499628,0.02235561,0.00903716,-0.00103357,0.03777744,-0.02274264,-0.07074948,0.01508262,0.03169816,-0.00356637,0.00503324,-0.01709644,0.0004043,0.02594253,-0.04029109,0.02252107,-0.01532703,0.0313071,0.07614813,0.07376532,0.04959297,0.04305437,0.05401464,-0.01463427,-0.11029961,-0.03935089,-0.03301981,-0.01398523,0.00999143,-0.0048387,0.00245591,-0.00631312,-0.04110041,0.01019032,0.04498227,-0.05441825,-0.03148441,0.11449657,-0.01202084,-0.00006079,0.05905758,-0.0054771,-0.00334685,0.0143749,-0.0041365,-0.04438677,0.00355751,0.01584158,0.04544284,0.13219887,-0.05849888,0.06130457,-0.01129038,-0.06056528,-0.00861858,0.11684248,0.00105961,-0.04279181,-0.03563135,0.02844341,0.03743769,-0.07911,0.0022043,0.02587081,-0.05930394,0.01691222,0.07409005,0.0019784,-0.00867428,-0.03583789,-0.02514116,0.04256568,0.02187553,-0.04263652,-0.06951133,0.00841629,-0.06123373,-0.11181385,-0.0205044,-0.05456021,0.05398418,0.05015705,-0.01322233,0.02221952,-0.01784253,0.02999472,-0.00581049,-0.08734903,-0.00258222,-0.01523807,0.03019365,0.0078264,0.06772162,-0.03983823,-0.02314212,-0.00434222,-0.01688522,0.05537549,0.01415928,-0.06393237,0.09003885,0.00184012,-0.05633431,-0.01908336,0.02585949,0.04688432,-0.00386911,0.03607762,0.01468721,0.02558741,-0.02170907,-0.00018424,-0.02675479,0.04895471,-0.01557011,-0.20936076,0.01257423,-0.02021284,0.01213326,-0.00592721,0.0247466,0.02506462,0.00260351,0.05611505,0.10713423,0.07453835,-0.05749995,-0.01347665,0.05183054,-0.01458495,-0.02859493,-0.07723857,-0.05048392,-0.01681054,0.0314154,-0.00842998,0.00971617,-0.02459249,-0.04551354,0.04850648,-0.01379075,0.17184842,0.00514587,0.04114964,-0.0458215,0.05460674,0.03502237,-0.00293145,0.0311693,0.02329387,0.04264784,-0.09975301,0.0008997,-0.01973219,-0.01248854,-0.11143737,0.0263186,-0.0026212,-0.04934208,-0.04875797,0.01014722,-0.00217165,-0.01631184,-0.04600324,0.05589303,0.00776772,-0.02103124,0.053755,0.03670319,0.04996692,-0.03655341,-0.0569187,-0.02525438,-0.03142652,0.02757538,0.02686265,-0.02468566,0.00943043,-0.04546611,0.0314124,0.0042908,-0.02244613,-0.02049865,0.02841417,-0.03726075,-0.00115174,0.08465947,-0.01563309,-0.04810267,0.0835981,-0.0134243,0.04410499,-0.01081613,0.0009459,-0.01367695,0.01179702,-0.08090686,-0.00277753,0.02176182,0.01598755,0.05031428,0.0405607,-0.00758856,0.00740631,-0.01746767,-0.02538483,0.01414444,-0.03973568,0.03216958,-0.02136272,0.00448158,-0.26294762,0.02720003,-0.03181493,-0.00572608,-0.00007757,0.00359085,0.03634708,0.04033503,0.02319883,-0.04291232,0.00894578,0.04940062,0.00844141,-0.05811875,-0.07459237,-0.03192464,0.00701641,-0.05233756,0.02379987,0.0107627,0.07944378,0.00333745,0.22769506,0.01975955,0.01474025,0.0476239,0.05056217,-0.00956324,-0.02511888,0.04662693,0.01795409,-0.00386522,0.11010765,0.01945426,-0.0492,0.10690502,-0.04663143,0.02805433,0.00622574,-0.0016675,0.01936737,-0.01080652,0.03329622,0.01814442,0.09765815,0.04453025,-0.02660641,-0.03860446,0.05845414,0.04374793,-0.10041407,-0.01402458,-0.04750302,-0.04310028,0.00685288,0.06762957,-0.02511946,-0.02533834,-0.00811368,-0.02482818,-0.02157973,-0.00466148,0.06574098,-0.02541357,0.02992999],"last_embed":{"hash":"1ty66lf","tokens":189}}},"text":null,"length":0,"last_read":{"hash":"1ty66lf","at":1753423452296},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{3}","lines":[72,84],"size":540,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10618427,-0.07642878,0.02395561,-0.03233315,-0.03416513,0.0256522,0.07212453,0.01090117,0.05608324,0.00990554,0.00532788,-0.01701429,0.0292805,0.01274205,-0.05425538,-0.04679475,0.00503285,0.01038515,-0.05938108,0.00804576,0.07268376,-0.05173337,-0.06033649,-0.08458012,0.05082606,0.02308054,-0.00553331,-0.07766049,-0.02190624,-0.21661782,-0.00046371,0.0215226,-0.02870912,-0.09582546,-0.05198409,-0.03865621,0.04703025,0.0849336,-0.01387107,0.02383631,0.03706387,-0.02379629,-0.03262674,-0.03542759,-0.01763211,-0.07239545,-0.02006163,0.00929519,0.03442883,0.00373836,-0.04515571,-0.00538709,0.03514767,0.01894095,0.06288652,0.01934146,0.0440277,0.06948812,0.02283941,-0.01227022,0.03932154,0.00203576,-0.22946034,0.05094936,-0.05231467,0.01085617,-0.04343109,-0.0088076,-0.00120793,0.11917361,0.01417889,0.01290549,-0.02371946,0.08517085,0.07616942,-0.02499895,-0.06694668,-0.08516499,-0.04723643,-0.00728725,-0.04699503,-0.028736,-0.01180953,0.01259885,0.02771877,0.05542829,0.06782525,0.0670944,0.0533132,-0.04169599,0.03292813,-0.00718856,0.01319379,0.03104164,-0.04231999,0.03172951,0.05837354,-0.05229129,0.01526726,0.11597323,0.02896778,-0.00007709,0.00013692,0.0096879,0.03965382,-0.00981038,-0.04016542,-0.06711277,-0.04551721,0.02748707,-0.00771426,-0.01455993,0.05106024,-0.01913197,-0.06895704,0.00270336,0.01021498,-0.01109147,-0.007062,-0.01432871,-0.00056566,0.02456726,-0.03200879,0.02275203,-0.021767,0.01778267,0.06172026,0.08412602,0.04382226,0.03817391,0.06054658,0.0052162,-0.07832698,-0.03015551,-0.02060751,-0.00227425,0.00199744,-0.0087744,-0.02209383,-0.0091723,-0.05540623,0.03028934,0.03751616,-0.05359426,-0.02089182,0.10662718,-0.00367097,0.01795068,0.05705575,-0.0000763,0.00628639,0.01208934,0.00872969,-0.04074248,0.02052231,0.01559355,0.0675521,0.12628376,-0.0601574,0.06566583,-0.02705378,-0.05155902,-0.01844533,0.12173864,-0.00455242,-0.056222,-0.0321953,0.00638403,0.03064737,-0.083905,0.00302921,0.02911754,-0.05969903,0.02072704,0.09478873,0.00381795,-0.00925281,-0.03864922,-0.03806811,0.03289136,0.01459358,-0.04976643,-0.07357701,0.00538016,-0.05428105,-0.12517244,-0.0167994,-0.07250754,0.05251876,0.06200537,-0.01207883,0.0191171,-0.01338772,0.01145316,-0.02784554,-0.09930062,-0.02273309,-0.02387665,0.04117901,0.01428797,0.06897517,-0.04432647,-0.01121966,-0.01987916,-0.0083315,0.06402351,-0.00767401,-0.05355212,0.06722462,0.01753622,-0.05539175,-0.00159257,0.03599084,0.04801025,0.01278968,0.04232822,0.02040711,0.02853725,-0.01710782,0.0119674,-0.02299013,0.02419651,-0.03940314,-0.20489272,0.02077799,-0.02537733,-0.0077096,-0.00096901,0.00783382,0.02717808,-0.0022731,0.04441311,0.09554382,0.07963458,-0.06387203,0.00512232,0.05980686,-0.00921903,-0.02721765,-0.06492965,-0.05096295,-0.03127777,0.03199938,-0.02713894,0.02390689,-0.00450291,-0.06592051,0.04432405,0.00570476,0.17021568,-0.00044868,0.04287978,-0.05940854,0.069768,0.04494793,-0.00916752,0.01839788,0.00961751,0.04915291,-0.07511196,0.0038388,-0.01651738,-0.01761481,-0.11018135,0.03006736,-0.01228435,-0.04963104,-0.03790072,0.00473958,0.00216647,-0.00467284,-0.05631451,0.0384318,0.008712,0.0040998,0.05383935,0.03267554,0.04463272,-0.02567194,-0.0562916,-0.02383029,-0.03446225,0.03665174,0.01755463,-0.03244936,-0.005997,-0.05837054,0.03617097,-0.00204663,-0.00080547,-0.02480265,0.01384086,-0.06898116,-0.00884423,0.08072627,-0.01697701,-0.04000815,0.07280602,-0.01819503,0.04688193,-0.01504488,0.00039255,-0.00451698,0.02361304,-0.06613287,0.00660789,0.0219552,0.01692307,0.05960562,0.04814095,-0.01464435,-0.008156,-0.01325062,-0.01601537,0.00320812,-0.02826885,0.04458193,-0.0016062,0.00124279,-0.27448782,0.02291415,-0.02911837,-0.00859525,0.00057726,0.02107594,0.02637968,0.02209759,0.02680678,-0.06226635,0.01163867,0.04734765,0.00590017,-0.05411734,-0.04916396,-0.01888948,0.00415675,-0.03171203,0.01650444,0.01644695,0.05797955,0.00496101,0.22429161,0.01740802,0.02580918,0.05619346,0.0587502,-0.00407362,0.00007846,0.04150489,0.02579436,0.01548569,0.10799287,-0.01168895,-0.05328587,0.10587976,-0.05827764,0.02862819,0.01046748,-0.01073061,0.0221778,-0.00341761,0.03978381,0.03444356,0.09068652,0.02704091,-0.02235358,-0.0349193,0.05631318,0.07136454,-0.08441041,-0.02595659,-0.04928482,-0.0300127,0.02474914,0.07151903,-0.02447031,-0.02486429,-0.00838631,-0.00771133,-0.01574478,0.00766654,0.05010908,-0.01519934,0.01176631],"last_embed":{"hash":"ab62do","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"ab62do","at":1753423452348},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>#{4}","lines":[74,84],"size":403,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>III. Lottery Strategies, Systems Based on <i>Delta</i> Reports</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07692841,-0.03639703,-0.03938299,-0.04398324,-0.00473841,0.03423757,0.05756662,0.00798005,0.05376384,0.01781313,-0.01541637,0.00582384,0.05545659,0.02835598,-0.0356596,-0.02674,0.02812852,-0.02203758,-0.06133513,-0.01935743,0.03777355,-0.05770726,-0.06342805,-0.09107943,0.05089401,-0.00867225,-0.01422705,-0.06473055,-0.0597279,-0.26384851,-0.00162129,-0.01030526,0.02961744,-0.09703232,-0.07030169,-0.04857589,0.00013981,0.07836621,-0.03981363,0.00080735,-0.00645695,-0.01390419,-0.0054065,-0.04101646,-0.03328733,-0.04595257,0.01492621,-0.00433362,0.01179223,-0.02317659,-0.08561065,0.01281698,0.02586887,0.06040194,0.00341136,0.02396405,0.02401969,0.09499809,0.00215064,0.02857174,0.06174988,0.00956505,-0.16306694,0.04369263,-0.04810859,0.02526473,-0.02121882,-0.0480378,-0.01676855,0.04538876,0.02087444,0.06072869,-0.02048726,0.07251687,0.06204915,-0.04395001,-0.0551065,-0.08229119,-0.03841613,0.03275965,-0.05596913,-0.00888367,-0.02536509,0.01452933,0.02344315,0.03424745,0.04272022,0.06038101,0.07945462,-0.0307385,0.0413821,0.00829369,-0.00947412,0.09485279,0.0223761,0.02858161,0.03625891,-0.06166307,-0.00240606,0.10584756,0.04773799,0.02518221,-0.03335677,-0.01750002,0.03327777,-0.02561294,-0.02312982,-0.05414457,-0.03581898,0.04469352,0.0198777,-0.0106137,0.07889351,-0.02405464,-0.05552059,0.04995615,0.02089824,-0.02523418,0.02085353,0.0221331,-0.05333505,-0.00307791,-0.01439272,-0.01998726,0.0052759,0.03535138,0.04293434,0.07414695,0.02768807,0.06524643,0.06071497,0.00346765,-0.14346698,-0.02868328,-0.03199552,-0.04310761,0.00255939,-0.02205543,-0.00328677,0.02463849,-0.07999474,0.00641165,0.03758222,-0.09853397,-0.05427195,0.12334845,-0.01624295,0.00231993,-0.02484906,-0.03998362,0.02177534,-0.00010685,-0.00753773,-0.06618821,0.01072032,-0.00176555,0.08558016,0.10419598,-0.08240604,0.03280716,-0.02362752,-0.02313743,-0.02974767,0.09901287,0.01209617,-0.0649659,-0.02943653,0.03488709,-0.02630798,-0.05443207,-0.00901456,0.03355218,-0.0485261,0.01692135,0.05282827,-0.02112784,-0.04627595,-0.05612919,0.01629452,0.0134046,0.03490319,-0.0271791,-0.05090537,0.01323708,-0.02213221,-0.09781416,0.00183316,-0.04580817,0.03165665,0.02252398,-0.04040094,-0.00611517,0.01778707,0.03859194,0.00638149,-0.06372509,-0.011384,-0.00740851,0.01881218,-0.00992242,0.0178961,-0.02729061,0.05028206,0.02617832,-0.00890433,0.05377547,-0.02477479,-0.04626135,0.10850047,0.02156715,-0.04854326,0.00938703,0.03183172,0.05451709,-0.05212297,0.03236116,0.00417314,0.02600277,-0.01222439,0.00809897,0.01099813,0.06511132,-0.04134208,-0.21451207,-0.0060813,-0.06483193,0.07426778,0.02366576,0.01646528,-0.0030839,-0.00915652,0.02310687,0.12912674,0.09818364,-0.06808617,-0.01050831,0.0638864,0.00134303,-0.04167368,-0.08863376,-0.0469919,-0.05445307,-0.00108174,-0.0033801,-0.00285846,0.01057783,-0.06369821,0.02983133,-0.03715445,0.16422473,-0.013024,0.05116275,0.01332578,0.04521893,0.027604,0.01878265,0.00275196,0.00605452,0.06296566,-0.05265481,-0.00485114,-0.00849933,0.00400602,-0.07750091,0.0113198,0.00010737,-0.10290057,-0.0250379,-0.00022382,-0.00447487,0.03983747,-0.03074069,0.02603942,0.02421593,-0.02618549,0.05680085,0.00399943,0.08590178,-0.05892224,-0.04457468,0.01781155,-0.01891845,0.02214789,0.02738191,-0.04883723,0.01942758,-0.04291951,0.00878281,-0.02291019,0.0057851,0.02312166,0.02293239,-0.02667964,-0.02825396,0.07807849,-0.06367065,0.01683827,0.05642103,0.05347864,0.02356752,-0.00842962,-0.00376841,-0.02673209,0.05282052,-0.05128787,-0.00519135,0.03560521,0.02535122,0.04823456,0.07233346,0.03376058,-0.00977005,0.01445084,-0.00182625,0.0154575,-0.04702744,0.01079817,0.02308685,0.00099634,-0.22742584,0.03202546,-0.04529031,0.01977721,0.02266878,-0.01653834,0.03051078,0.01001198,0.06713395,-0.01173513,-0.00740982,0.0470286,0.02322111,-0.07323278,-0.0411171,0.01996188,-0.00025319,-0.02620701,0.05331615,0.01901091,0.08364394,0.01588035,0.22953175,0.00611095,0.00371742,0.04286996,0.03383706,-0.01099189,-0.07188068,0.03328575,0.03485523,-0.02299757,0.10951404,-0.02280101,-0.05810101,0.05131836,-0.01455526,0.05972353,-0.00911534,0.00570817,-0.01709817,0.03506967,0.00238035,-0.02622294,0.0823347,0.02886919,-0.03616684,-0.07228214,0.02274885,0.07729368,-0.09639809,-0.04703191,-0.03119257,-0.05114604,0.05230636,0.06579325,0.02174571,0.01235151,0.00421029,-0.03438762,0.02180046,-0.01622006,0.05472951,-0.00856839,0.02016024],"last_embed":{"hash":"10gar3d","tokens":410}}},"text":null,"length":0,"last_read":{"hash":"10gar3d","at":1753423452392},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>III. Lottery Strategies, Systems Based on <i>Delta</i> Reports</u>","lines":[85,116],"size":4638,"outlinks":[{"title":"Lottery deltas and restrictions in the past 1000 lotto drawings.","target":"https://saliu.com/ScreenImgs/delta-lottery-draws.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>III. Lottery Strategies, Systems Based on <i>Delta</i> Reports</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07617655,-0.03684538,-0.03935338,-0.04177768,-0.00110769,0.03370295,0.05760656,0.00645318,0.05531424,0.01815013,-0.01595056,0.00552632,0.05670557,0.02781517,-0.03330299,-0.02716501,0.02761379,-0.0198102,-0.06229453,-0.0212864,0.03908964,-0.05719469,-0.06267882,-0.08865211,0.0502254,-0.00783289,-0.01541209,-0.06274719,-0.05938524,-0.26435053,-0.00258917,-0.01140785,0.02958399,-0.09741503,-0.07119577,-0.04934365,0.00013863,0.07751778,-0.03923967,-0.00011846,-0.00650792,-0.01352866,-0.00419979,-0.04206475,-0.0361145,-0.04622177,0.01366192,-0.00336972,0.01109467,-0.02249622,-0.08460908,0.01373688,0.0252167,0.06250009,0.00085351,0.02376806,0.02524129,0.09633363,0.00060873,0.02864293,0.05966188,0.01016837,-0.16346045,0.04462867,-0.05058687,0.02572221,-0.02028833,-0.04935412,-0.01695055,0.0471515,0.01903748,0.06149879,-0.02005442,0.07397823,0.06231479,-0.04586471,-0.05399083,-0.08301292,-0.03684027,0.03350945,-0.05578279,-0.00948704,-0.02514961,0.01626273,0.02510306,0.03227741,0.04277774,0.05996521,0.07729942,-0.02948803,0.03995268,0.00596264,-0.01163451,0.09528215,0.02361782,0.02774458,0.03675646,-0.06022099,-0.00226472,0.1050925,0.04794627,0.02625974,-0.0323683,-0.01857232,0.03232362,-0.0256964,-0.02356945,-0.05443123,-0.03538926,0.04493411,0.01842793,-0.01032193,0.07686034,-0.02392422,-0.05535019,0.05303586,0.02133857,-0.02716172,0.02192165,0.0223266,-0.05228329,-0.00339043,-0.01408503,-0.01981707,0.00589075,0.03397399,0.04327194,0.07450611,0.02654801,0.06361707,0.06124275,0.00177092,-0.14593747,-0.02771711,-0.03391915,-0.04321498,0.00188859,-0.02269902,-0.00596887,0.02448961,-0.07912147,0.00710151,0.03916953,-0.09842969,-0.05243509,0.12530912,-0.01644335,0.0004287,-0.026035,-0.04315792,0.02110863,0.00089859,-0.00674983,-0.06787622,0.00858684,-0.0007789,0.08539978,0.10249621,-0.08196863,0.0347358,-0.02493524,-0.02505477,-0.02882547,0.10067638,0.01081468,-0.06216652,-0.02774931,0.03425378,-0.02786992,-0.05265649,-0.00867739,0.03458147,-0.0490444,0.01748151,0.05410011,-0.02081151,-0.04811033,-0.0545985,0.01587212,0.01428207,0.03473328,-0.0270726,-0.04894937,0.01196935,-0.02298214,-0.09676576,0.00220268,-0.0433738,0.03084014,0.02276681,-0.04345767,-0.00378057,0.01880425,0.03855373,0.00551571,-0.06077896,-0.01010763,-0.00788297,0.01846872,-0.0118389,0.01918787,-0.02556447,0.05127345,0.0262991,-0.00869054,0.05545974,-0.02421216,-0.0437143,0.10912822,0.02058638,-0.04953394,0.00955603,0.03153747,0.05392275,-0.05201715,0.03043709,0.00576116,0.02356478,-0.01462464,0.00868027,0.00958806,0.06484959,-0.04112912,-0.21261469,-0.00600427,-0.06325228,0.07638543,0.02465759,0.0149733,-0.00308005,-0.00965196,0.02248236,0.12868214,0.09826938,-0.06696789,-0.00932879,0.06539135,0.00193879,-0.03943099,-0.08858792,-0.04568944,-0.05556389,-0.00092647,-0.00414708,-0.00170328,0.01165337,-0.06368633,0.03230036,-0.03758681,0.16572078,-0.01187701,0.05270578,0.01379557,0.0446237,0.02674637,0.01874688,0.00101547,0.00601526,0.06468466,-0.05292187,-0.00707489,-0.00682384,0.00146871,-0.07652277,0.00964886,0.00037527,-0.10200191,-0.02435224,-0.00197281,-0.00224561,0.04138003,-0.0304978,0.02538633,0.02508176,-0.02649725,0.05516451,0.00272287,0.08444389,-0.05928138,-0.04694209,0.01794577,-0.02134053,0.02183723,0.0295458,-0.05007495,0.01815868,-0.04111362,0.00878225,-0.02317911,0.00673682,0.02210389,0.02309531,-0.02515853,-0.0285001,0.07521193,-0.06464482,0.0159927,0.05553553,0.05294935,0.02149944,-0.00895223,-0.00225346,-0.02832622,0.05310507,-0.05103462,-0.00597942,0.03535541,0.02717933,0.04696319,0.07248402,0.03298013,-0.00967358,0.01240643,-0.00149082,0.0178597,-0.04783651,0.01098946,0.02361074,0.00035091,-0.22882469,0.03013704,-0.0469523,0.02001379,0.02426077,-0.01811103,0.02948692,0.00966742,0.06646026,-0.01476918,-0.00687258,0.04586068,0.0233203,-0.072795,-0.0394087,0.01997587,0.00044196,-0.02484367,0.05209518,0.01903312,0.08399532,0.01491266,0.23060007,0.00556836,0.00540827,0.04368948,0.03299787,-0.01163073,-0.07517547,0.03286059,0.0329031,-0.023482,0.11077493,-0.02158478,-0.05605062,0.0543777,-0.01439931,0.05979593,-0.01114614,0.00558106,-0.01766614,0.03436208,0.00307404,-0.02618486,0.08136962,0.03100116,-0.03520581,-0.07076778,0.02241343,0.07566801,-0.0963107,-0.04458348,-0.03244003,-0.05120184,0.05411194,0.0649169,0.02040581,0.01073987,0.0033516,-0.03344051,0.02246147,-0.01422584,0.05486632,-0.00769365,0.01941096],"last_embed":{"hash":"12hrxai","tokens":409}}},"text":null,"length":0,"last_read":{"hash":"12hrxai","at":1753423452524},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>III. Lottery Strategies, Systems Based on <i>Delta</i> Reports</u>#{1}","lines":[87,116],"size":4564,"outlinks":[{"title":"Lottery deltas and restrictions in the past 1000 lotto drawings.","target":"https://saliu.com/ScreenImgs/delta-lottery-draws.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10645791,-0.04759972,-0.03333421,-0.02455823,-0.05570051,0.05299294,0.03342363,-0.01616848,0.02713738,-0.0252405,0.00794948,0.0009604,0.06447732,0.00181952,-0.00934381,-0.03891777,0.0258405,-0.02362804,-0.04611734,-0.00215108,0.05747974,-0.00907004,-0.02509883,-0.04429416,0.06143987,-0.03480147,0.00066352,-0.0679432,-0.031721,-0.24285588,0.02369751,-0.01338122,-0.02251005,-0.0831725,-0.07949848,-0.02062466,0.02066854,0.10948374,-0.00991639,0.05346189,0.01666232,0.00290506,-0.00222376,-0.07534248,-0.03722819,-0.04730974,0.01749495,-0.03151825,0.04284165,-0.00871906,-0.07417205,-0.00736919,0.04509681,0.01936797,0.00178711,0.02725809,0.01590642,0.11781668,0.04848906,0.01376499,0.01745641,0.01543132,-0.17262754,0.06787137,-0.04917065,0.02786647,-0.03003728,-0.00512263,0.03121445,0.08538821,0.02779465,0.06551953,-0.05075837,0.06387764,0.0685928,-0.03298471,-0.08989122,-0.06248843,-0.04761723,0.01545757,-0.03332055,-0.05129198,0.0292481,-0.01723213,0.02197286,0.03755078,0.0550785,0.04600656,0.06030965,-0.01623232,0.03107008,-0.00617657,0.01920751,0.06318302,0.01301665,0.01447928,0.07234416,-0.03451215,0.00250078,0.10994428,0.0248698,0.02076131,-0.01814121,0.00761057,0.04558593,-0.04513514,-0.01046021,-0.01472194,-0.04360935,0.04900034,0.03624599,-0.01247122,0.07504926,-0.01127193,-0.04516839,0.01027993,0.00837271,-0.01650153,0.02554966,-0.02528022,-0.02458285,-0.01417509,-0.00790041,-0.022592,-0.00356313,0.03563015,0.03101284,0.08322649,0.0250859,0.02184288,0.04333385,-0.02741871,-0.1279491,-0.06875933,-0.02327386,-0.00920196,-0.00249297,0.01891039,-0.01577157,0.04655711,-0.07405946,0.00480534,0.04620226,-0.08772371,-0.05714559,0.10461518,-0.00932813,0.02111719,-0.00044288,-0.02965664,0.00909991,-0.01626135,-0.0208451,-0.02108345,-0.01539962,0.01894594,0.06570885,0.13672963,-0.09214643,0.00091814,0.00471067,-0.04891055,-0.06748515,0.10156018,-0.01206904,-0.04458199,-0.0378963,-0.00778209,-0.04404526,-0.04854386,-0.02650002,0.02022048,-0.02753888,0.01838307,0.08851378,0.00308214,-0.06643314,-0.04654582,-0.06250843,-0.0055577,-0.00044871,-0.0083168,-0.04978229,0.02113527,-0.0376093,-0.11643038,0.03025431,-0.05361761,0.03820664,0.02983088,-0.09024643,-0.03212119,-0.01820932,0.06944396,0.01139198,-0.0497262,0.01764028,0.00023726,0.0654873,-0.02171366,0.036242,-0.03586604,0.03189331,0.01650215,-0.07220719,0.06573031,-0.02642451,-0.05514609,0.11690988,0.02260079,-0.03209542,0.02830081,0.03414778,0.07448422,-0.00350104,0.01018165,-0.02414139,0.01806377,-0.004138,-0.00006975,0.01795014,0.05505938,-0.05054918,-0.22807437,0.01352371,-0.04624959,0.03908795,0.01349875,0.01418179,-0.00811728,-0.00234559,0.03330099,0.12900484,0.08168404,-0.06698672,-0.00821494,0.04395083,-0.01322741,-0.00226138,-0.08136834,-0.01099394,-0.05309484,0.00823578,0.00339379,0.00369682,-0.0298361,-0.05264496,0.04343683,-0.03185205,0.14280038,0.00351521,0.03117211,-0.02826907,0.06180572,0.00213308,-0.00528954,-0.00420884,-0.00391953,0.04959975,-0.07868242,-0.01419291,-0.01548075,-0.01708484,-0.06528745,-0.03832721,-0.03387792,-0.10891284,-0.02373797,0.02263491,0.00123519,0.05869549,-0.02824858,0.00126894,0.04917286,-0.03191872,0.00719031,0.03562375,0.07856648,-0.08998781,-0.08288574,-0.02065354,-0.01416228,0.04941529,0.00580303,-0.01746523,0.01155742,-0.0425122,0.03681039,0.00000487,0.02164982,0.00101609,0.01965658,-0.00187222,0.00380273,0.08743345,-0.01607827,-0.00354626,0.00266398,0.03294445,0.04382308,0.01444168,-0.02295912,-0.03200855,0.04515293,-0.05738185,0.03466923,0.05516649,0.02961242,0.02974503,0.04212942,-0.01839404,-0.02034635,-0.0262489,-0.03056468,0.01645664,-0.05065785,0.03092002,0.00606242,0.00838425,-0.24216644,0.04270189,-0.00129419,0.01488097,0.01710377,0.00493212,0.02494257,-0.0251776,0.05522267,-0.03944049,0.02131471,0.0587153,0.01719462,-0.06757805,-0.03511753,0.01663709,-0.00273094,-0.02301425,0.07999372,-0.01157795,0.08315667,0.02693654,0.23554488,0.01551113,0.00155012,0.02103997,0.01920939,-0.00053913,-0.05870755,0.0491093,0.04103693,0.00636919,0.11030807,-0.01608125,-0.02907335,0.02299087,-0.02467696,0.04566531,-0.00771655,-0.00339379,-0.00968375,0.03200342,-0.00402826,0.00077598,0.09679276,0.03162076,-0.02649784,-0.03919475,0.01412799,0.08636121,-0.0598504,-0.03817458,-0.0385246,-0.00789501,0.0459609,0.06769587,-0.02124373,0.01779781,-0.0154574,-0.00701503,0.00870987,0.0202664,0.04044604,0.00411952,0.03551887],"last_embed":{"hash":"1m30yp3","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"1m30yp3","at":1753423452658},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>","lines":[117,139],"size":5895,"outlinks":[{"title":"Check if delta lottery strategies hit in past drawings and see the skips.","target":"https://saliu.com/images/delta-lottery-strategy.gif","line":3},{"title":"_**filters in static and dynamic**_","target":"https://saliu.com/bbs/messages/919.html","line":7},{"title":"_**REVERSED lottery strategy**_","target":"https://saliu.com/reverse-strategy.html","line":11},{"title":"_**lottery filters at run-time**_","target":"https://saliu.com/filters.html","line":15},{"title":"_**cross-reference lottery strategies**_","target":"https://saliu.com/cross-lines.html","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10711609,-0.05011519,-0.03173137,-0.02358523,-0.05365497,0.05264715,0.03549381,-0.01478841,0.02577548,-0.02659888,0.00842062,0.00059616,0.0655962,0.0035328,-0.01062591,-0.03796547,0.02405607,-0.02520175,-0.04594898,-0.00371847,0.05905479,-0.00790872,-0.02462718,-0.04474019,0.06096503,-0.03168438,0.00181173,-0.06668819,-0.03279992,-0.24191214,0.02491207,-0.01366445,-0.02512811,-0.08421796,-0.07795103,-0.02194736,0.02180019,0.10608593,-0.0080944,0.05189936,0.01453947,0.00079369,-0.00393479,-0.07501496,-0.03754573,-0.04983591,0.01949444,-0.03032784,0.03851577,-0.00897657,-0.07513346,-0.00643789,0.04546112,0.01914881,0.00079674,0.02717054,0.01342181,0.1177784,0.04930129,0.01305569,0.01833023,0.01714153,-0.17139982,0.06691775,-0.05102603,0.02996943,-0.03050178,-0.00620394,0.03251551,0.08428087,0.02918072,0.06497176,-0.04914734,0.0678469,0.06918853,-0.03134478,-0.08901782,-0.06259469,-0.04785806,0.01623224,-0.03273852,-0.05400973,0.0293787,-0.01701345,0.02266772,0.03857727,0.05634349,0.04520359,0.0615386,-0.01363579,0.0318663,-0.00617766,0.01887903,0.06124861,0.0134011,0.01438298,0.07391402,-0.0360329,0.00114939,0.1095679,0.02439895,0.02142126,-0.02093532,0.00568721,0.04424722,-0.0465906,-0.00950168,-0.0147012,-0.04111024,0.04599108,0.03348163,-0.01165417,0.07681163,-0.00884936,-0.04540166,0.01337079,0.00780977,-0.01736614,0.02725913,-0.02403821,-0.02545675,-0.01532948,-0.00803885,-0.02269204,-0.00343705,0.03573766,0.03119591,0.08284832,0.0230232,0.02154853,0.04504937,-0.03019314,-0.12955409,-0.06757408,-0.02281066,-0.00855185,-0.00329509,0.01808009,-0.01662948,0.04490534,-0.07516322,0.00600284,0.04575977,-0.08825506,-0.0571666,0.10389386,-0.00726015,0.01955701,-0.00063053,-0.02975698,0.01086769,-0.01584897,-0.02126235,-0.02061349,-0.01626153,0.01796975,0.0655444,0.13737553,-0.09388988,-0.00060038,0.00337522,-0.04735833,-0.07096848,0.09793053,-0.01386964,-0.04421158,-0.03826424,-0.00896252,-0.04402069,-0.04863645,-0.02498527,0.02129739,-0.02878579,0.01925529,0.08668329,0.00192325,-0.06514984,-0.04534602,-0.06129176,-0.00275884,0.00095132,-0.00956671,-0.04976296,0.02192279,-0.04019562,-0.1178457,0.02954341,-0.05522528,0.03873919,0.03000184,-0.09088629,-0.03215922,-0.01833661,0.06924599,0.01130241,-0.05084407,0.0171412,0.00075151,0.06430858,-0.02296153,0.03683732,-0.03713937,0.03181014,0.01653969,-0.06999332,0.06732193,-0.02530841,-0.0560308,0.12095924,0.02234847,-0.03265965,0.02791373,0.03718176,0.07469603,-0.00507347,0.00915308,-0.02605929,0.01813281,-0.00640535,0.0004749,0.01328755,0.05724443,-0.05132696,-0.22811775,0.01206872,-0.04666758,0.04251914,0.01350026,0.01575443,-0.0061377,-0.0028724,0.03067746,0.12912628,0.08243538,-0.06646899,-0.00801215,0.04597627,-0.00994081,-0.00176615,-0.08211998,-0.01409248,-0.05171097,0.0081749,0.00267431,0.00197113,-0.03056883,-0.05221917,0.04503719,-0.03145757,0.1429822,0.00170061,0.03031833,-0.02890468,0.06088622,0.00148891,-0.00124657,-0.00700786,-0.00234148,0.05001378,-0.07500581,-0.01559894,-0.01465889,-0.01832929,-0.06303298,-0.03654952,-0.03323329,-0.1098116,-0.02352451,0.02408169,-0.00072144,0.05906483,-0.02966781,0.00124304,0.04924665,-0.03129758,0.00776648,0.03548339,0.07893308,-0.0930359,-0.08063056,-0.01877232,-0.01280846,0.04890786,0.00496322,-0.0192923,0.00819307,-0.0431484,0.03889151,-0.00278632,0.02340829,0.0029157,0.0203462,-0.00197764,0.00347612,0.08759262,-0.01945447,-0.00265711,0.00753928,0.03440826,0.04194004,0.01581273,-0.02388952,-0.03236136,0.04514448,-0.05202426,0.03443934,0.05386456,0.02761867,0.02798567,0.04222158,-0.01745192,-0.02028556,-0.02526342,-0.03366326,0.01798954,-0.04859669,0.02884449,0.00610709,0.00983322,-0.24194682,0.04178621,-0.00072989,0.01259957,0.01750121,0.00284785,0.02598317,-0.02575989,0.05451052,-0.04246099,0.02052373,0.05876297,0.01698462,-0.06657074,-0.03459371,0.01650817,0.00159943,-0.02436575,0.08042797,-0.01332119,0.08183696,0.02633979,0.23696588,0.0168418,0.00523003,0.0221928,0.02009289,-0.00036506,-0.05721725,0.04754929,0.04240851,0.00696082,0.11050728,-0.01622043,-0.02755692,0.02427748,-0.02300342,0.04567411,-0.0077893,-0.00761218,-0.00731857,0.03200937,-0.00546538,0.00399817,0.09344706,0.03123876,-0.02511016,-0.03915961,0.01326973,0.0864326,-0.06010977,-0.03583975,-0.0359807,-0.00964327,0.04558609,0.06700828,-0.02174217,0.01682233,-0.01289333,-0.0080119,0.00645429,0.02095584,0.04349377,0.00645174,0.03559391],"last_embed":{"hash":"1u87dkx","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"1u87dkx","at":1753423452813},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{1}","lines":[119,132],"size":3920,"outlinks":[{"title":"Check if delta lottery strategies hit in past drawings and see the skips.","target":"https://saliu.com/images/delta-lottery-strategy.gif","line":1},{"title":"_**filters in static and dynamic**_","target":"https://saliu.com/bbs/messages/919.html","line":5},{"title":"_**REVERSED lottery strategy**_","target":"https://saliu.com/reverse-strategy.html","line":9},{"title":"_**lottery filters at run-time**_","target":"https://saliu.com/filters.html","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09727691,-0.02909561,0.00495211,0.00183492,-0.02526845,0.04132158,0.06783562,-0.00054824,0.03612142,-0.01426941,-0.03734897,-0.00431393,0.05045995,0.01253919,-0.01300934,-0.0343606,0.00552806,0.02594365,-0.0184257,-0.03221573,0.05795272,0.00024367,-0.0400529,-0.0644788,0.07174797,0.03172283,0.00037343,-0.06233219,-0.03109419,-0.23693913,-0.00610115,0.02848682,-0.05759417,-0.08364178,-0.06270187,-0.03662391,0.01478761,0.05204046,-0.00592129,0.03003407,0.03695312,-0.00533634,-0.00965608,-0.00815248,-0.02720979,-0.07767697,-0.03223693,0.02421796,0.03088408,-0.02538446,-0.05675494,0.00907698,0.01325463,-0.02048171,0.03561281,0.04211335,0.03971607,0.12008348,0.01600445,0.02378302,0.03967512,0.04220734,-0.17692389,0.05248908,-0.05672438,0.02588456,-0.01771246,-0.03055229,0.03160439,0.09867938,0.00075649,-0.01328197,-0.03370534,0.10035956,0.08202337,-0.05548857,-0.05270923,-0.0785125,-0.05816554,0.02258878,-0.04172315,-0.03265324,0.01785503,0.01278972,0.02666563,0.08347204,0.05666877,0.09287521,0.07290094,-0.07315651,0.0480751,0.01335768,0.05865965,0.03742853,-0.00418874,0.00827656,0.0859237,-0.06491606,0.01833605,0.13172416,0.01766136,-0.03149042,0.01471891,0.03916403,0.02615703,-0.01476465,-0.0128671,-0.08813002,-0.04598941,0.00281461,0.00616214,-0.02403323,0.07465308,-0.05323103,-0.03907644,0.016299,-0.02583164,-0.03316731,0.03050252,-0.0060285,-0.00424492,-0.00056383,0.01301209,0.00256878,-0.0228632,-0.01960809,0.04833408,0.03891743,0.01569488,0.01497432,0.0454638,-0.01497531,-0.09722974,-0.06500784,-0.01086925,0.00288621,-0.03255101,-0.02741009,0.0064765,0.03394669,-0.07000954,0.0151731,0.10365763,-0.12460177,-0.00215439,0.0838644,-0.00708439,0.01286123,0.00712071,0.00608499,0.01334143,0.00159904,0.0098242,-0.03871679,0.01515928,-0.00975438,0.08171672,0.10648403,-0.05595214,0.03400112,-0.01532598,0.0250757,-0.03457321,0.13033673,-0.04944908,-0.07244213,0.00685808,0.02271415,-0.01913379,-0.11702308,-0.01157366,0.02200113,-0.04565464,-0.0096875,0.07452518,-0.01367674,-0.05455647,-0.03543881,-0.04831797,0.03073915,0.02174934,-0.05508118,-0.09839131,0.05847241,-0.0157939,-0.07811377,0.01951921,-0.05867954,0.03326873,0.04383319,-0.07510667,0.02122587,-0.00331756,0.03416551,-0.0443121,-0.01956675,-0.04554747,-0.01823209,0.04903958,0.01784451,0.05979438,-0.00320644,0.01986893,-0.02113279,-0.01188276,0.03941266,-0.00568547,-0.04530578,0.0834102,0.03305444,-0.06123215,0.06661218,0.05033299,0.04760592,-0.03634171,0.00237383,0.00656018,0.05317711,-0.0233129,0.02121584,0.0048103,0.04292944,-0.03455421,-0.20201911,-0.04517207,-0.04471805,0.02711759,-0.009713,0.01425926,0.04470646,0.01305847,-0.00831221,0.09879535,0.09482403,-0.01158705,-0.01667826,0.04146079,-0.01825489,-0.01456413,-0.06936286,-0.08894233,-0.04468438,0.0445332,-0.02928006,0.00297707,-0.00151526,-0.051341,0.02364626,-0.04520515,0.14495701,-0.01077054,0.04497927,0.00863976,0.06794874,-0.0140628,0.01354412,-0.02381,0.02518303,0.07151438,-0.04172655,-0.00809808,0.02197002,-0.0268498,-0.05848202,0.02377833,-0.03517582,-0.08288302,-0.00075373,-0.00878257,-0.00503082,-0.0303225,0.00209773,-0.00252347,0.02361236,-0.02829566,-0.03097612,0.05958752,0.03888368,-0.06345836,-0.07594302,-0.03047267,-0.03911957,0.00404422,0.00574996,-0.01987674,0.00565283,-0.03501754,0.06403954,0.01219684,0.01814678,-0.01974031,0.03870879,-0.02608612,-0.03029352,0.08116678,-0.05302922,0.0062942,0.0354893,0.03158539,0.06976866,-0.02204402,-0.06090551,0.01035065,0.04168237,-0.05074824,0.00286134,0.02745742,0.01840748,0.01274198,0.04051216,-0.03335772,-0.00381762,-0.0181419,-0.02396757,-0.01553033,-0.02136238,0.01248587,0.00021671,0.02575138,-0.24098323,-0.00930891,-0.05055401,-0.01969168,-0.02579913,0.02939853,0.01364452,-0.04242941,0.02514123,-0.04934392,0.056874,0.03976884,0.02344579,-0.06467369,-0.01866289,0.02160381,0.08170044,-0.0293574,0.04819204,-0.01176095,0.05545188,0.02667954,0.24912287,-0.00001407,0.02568753,0.07213574,0.03050203,-0.00958281,-0.01215561,0.06381559,0.03150957,0.03765013,0.06667244,-0.03254816,-0.04351932,0.1032154,0.01021306,0.05800641,0.00766058,0.00339254,-0.02301458,-0.01817332,-0.01640291,-0.01321254,0.07586753,0.02612175,-0.02059783,-0.04292533,-0.01470962,0.03874451,-0.07601565,-0.05544704,-0.05424864,-0.02752469,0.04027414,0.06486207,0.0000311,-0.01017775,-0.00733273,-0.02190291,0.04703018,-0.01165762,0.05284031,0.02002212,0.01547569],"last_embed":{"hash":"19nwy02","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"19nwy02","at":1753423452947},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{2}","lines":[133,133],"size":398,"outlinks":[{"title":"_**cross-reference lottery strategies**_","target":"https://saliu.com/cross-lines.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08534984,-0.0583406,-0.03488403,0.01280789,-0.04439965,0.05900719,0.04702637,-0.0463209,0.04648985,-0.0069941,-0.00055143,-0.01704484,0.03929221,-0.00218914,-0.01227143,-0.00402607,-0.02870023,-0.00329027,-0.0139198,-0.02140963,0.09274381,-0.04011528,-0.03553843,-0.03867517,0.0676197,-0.00799019,0.02592297,-0.07473879,-0.02480022,-0.20596838,0.03036184,0.02549013,-0.02602555,-0.07165086,-0.08465581,-0.05543182,-0.02674517,0.11648005,-0.04222701,0.03816565,-0.01166366,0.00254175,0.01794168,-0.01099879,-0.04306267,-0.0422681,-0.01747913,0.00943126,0.02779652,-0.03750777,-0.08938853,0.00354473,0.01285602,-0.00762219,0.04957592,0.03788191,0.04811238,0.07465541,0.01528639,-0.01976052,0.05892767,0.02869083,-0.18560752,0.06068423,-0.04200569,0.03788154,0.00581462,0.02657061,0.00370788,0.0981046,0.03585129,0.01368631,-0.01234549,0.10452097,0.080423,-0.0143368,-0.06343172,-0.05242759,-0.03999048,0.0257972,-0.04033966,-0.01532408,-0.02189844,-0.00909251,-0.02128909,0.06151016,0.04935538,0.05189218,0.05231754,-0.05112823,0.01157945,0.02492378,-0.00242727,0.04822774,-0.02786944,-0.00561741,0.03692776,-0.05623661,0.0048887,0.10123834,0.05509055,0.00305681,-0.03999571,0.04072925,0.03055702,-0.03668527,-0.03655099,-0.08822576,-0.03456376,0.04929201,0.03648327,0.00547939,0.05707368,-0.04057304,-0.05590057,0.01850251,-0.00315935,-0.00696109,0.03802831,-0.0338595,-0.00287319,0.03489393,-0.0097473,0.02588812,-0.02104568,0.00863841,0.03452237,0.06316748,0.03629504,-0.0062148,0.0542364,-0.02545821,-0.12548502,-0.05508597,-0.0313908,-0.00446506,-0.01211061,-0.01028487,-0.01487975,0.00499057,-0.05629597,-0.0087124,0.08443532,-0.0996982,-0.05137186,0.06647963,-0.00854968,0.01226994,0.00037496,0.02561102,0.01110063,-0.00796075,-0.03656185,-0.05615875,-0.00728886,0.01483478,0.08012234,0.10673475,-0.06990666,-0.00158872,-0.03413757,-0.04245267,-0.0282113,0.14292327,-0.02756576,-0.0873195,-0.05067404,0.00533704,-0.0246186,-0.10622153,0.00740101,0.03089927,-0.06233601,0.03041113,0.08552747,0.02741499,-0.04714339,-0.06346739,-0.01310451,0.03784048,0.04077829,-0.03185074,-0.0572622,-0.00462476,-0.02949096,-0.07805391,-0.00249028,-0.0589174,0.03585679,0.02529869,-0.05648654,0.04672161,-0.01693244,0.00419079,-0.02879566,-0.03032462,-0.03241014,-0.03559611,0.07905117,0.00356442,0.04287504,-0.00055828,0.05254745,0.03877663,-0.03522334,0.06474203,0.0265976,-0.06650265,0.08440264,0.02207433,-0.03719454,0.0370124,0.03680039,0.05120295,-0.04538667,0.01193101,0.00970496,0.00940676,-0.02488975,0.00767574,0.0198791,0.05442581,-0.03537354,-0.16959611,-0.00974456,-0.04643051,0.03050732,-0.00923076,0.03948297,0.03491656,-0.00424478,0.02464728,0.06136755,0.06058895,-0.0730409,-0.00824388,0.08930167,-0.01579007,-0.01169831,-0.09939117,-0.08957954,-0.03152,0.04076092,-0.04469314,0.01731203,-0.06890443,-0.03055416,0.04493305,-0.02818078,0.14165047,0.01803084,0.04161024,-0.0097686,0.05579961,0.0157696,0.01669677,-0.00189384,0.00711019,0.0140962,-0.01325928,-0.00730296,-0.03397625,-0.02717366,-0.07634696,0.04531374,-0.02443024,-0.06091357,-0.03551069,-0.01837639,-0.0194643,0.03347473,-0.01317241,0.06615225,0.03083644,0.00208239,0.00316603,0.06324185,0.05155146,-0.03409568,-0.08925625,0.03757783,-0.02209257,0.03591813,0.0170125,-0.01163273,-0.0049387,-0.02375651,0.00619023,0.01182139,-0.00271286,-0.02079989,0.04307233,-0.04332873,0.02346115,0.0873882,-0.00390664,-0.05338147,0.03925962,0.00311299,0.04630607,-0.04158961,-0.02343114,-0.00469598,0.00591436,-0.05232039,0.01893117,0.04069405,0.02480695,0.02083529,0.04940354,-0.00227363,0.01862791,-0.01650133,-0.02942553,0.04311292,-0.00656988,0.0152573,0.02130052,-0.02664779,-0.28026977,0.0586505,-0.04802849,0.02436301,0.00840305,0.00167255,0.00711254,0.00726902,0.01860805,-0.04142731,0.0412091,0.01875744,-0.00475559,-0.01490666,-0.04061766,0.0046024,0.04998624,-0.05138935,0.04693385,-0.040428,0.08557829,0.02388914,0.24762629,0.00740507,0.02888128,0.05412996,0.02714017,0.03921971,-0.06345212,0.04178038,0.01795018,-0.00222749,0.10579267,-0.02723158,-0.03262205,0.06901445,0.00315883,0.04421061,-0.01113184,0.01254151,-0.04611647,0.00540171,0.00392528,0.0268141,0.09817094,0.01722837,-0.02251619,-0.05410025,0.02763896,0.110137,-0.12516229,-0.04207709,-0.08958746,-0.05092761,0.01754801,0.0619804,0.00273681,-0.01731028,0.00713917,0.01608912,-0.01483776,0.01759414,0.0531986,0.01026811,0.00869884],"last_embed":{"hash":"3ujssm","tokens":159}}},"text":null,"length":0,"last_read":{"hash":"3ujssm","at":1753423452990},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{3}","lines":[134,134],"size":352,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08782298,-0.03808543,-0.02378635,0.01583942,-0.0233507,0.05250077,0.00734112,-0.04108066,0.0435621,0.01401035,0.00581982,0.00247556,0.03998461,-0.00222027,-0.03931241,-0.03208851,0.00243485,-0.04197421,-0.01301318,-0.00435945,0.04092048,-0.044678,-0.03500444,-0.04633429,0.06978679,0.00217366,-0.00225904,-0.08311187,-0.04043511,-0.2431456,0.00024877,0.00002732,-0.01566599,-0.0654486,-0.05957323,-0.03115352,-0.00353072,0.12772341,0.01017908,0.01825587,-0.01028729,0.00237755,-0.00597446,-0.01624918,-0.03791611,-0.04428651,-0.01727118,0.01415647,0.08115876,-0.03183892,-0.07288382,-0.02895797,0.03302162,-0.00468809,0.05324172,0.00771693,0.05212446,0.07355952,0.02605891,-0.00148635,0.04346947,0.03252216,-0.21568836,0.02978027,-0.04346784,0.04662101,-0.03489495,0.00592612,-0.02419449,0.10555749,-0.01809276,-0.00895488,-0.02650231,0.07971654,0.0812766,-0.03371242,-0.0707408,-0.06253611,-0.04720625,0.01545617,-0.06025887,-0.0085052,-0.01576596,0.0417427,-0.01630358,0.03459122,0.07254935,0.12372258,0.09809932,-0.04998703,0.04556684,0.01544592,0.0136923,0.0324353,-0.03757299,0.01574398,0.05515095,-0.05426972,0.00248626,0.10081816,0.01464549,0.01748881,-0.00389322,0.04882766,0.02509446,-0.00956481,-0.01676718,-0.06496477,-0.03151013,0.04413501,0.04290513,-0.02086832,0.05797536,-0.05000287,-0.01832471,0.02597545,0.02846722,-0.00877267,-0.01451508,-0.009963,-0.00905401,0.00076842,-0.02327827,-0.00336457,-0.03630112,0.02345353,0.06303303,0.04992298,0.04950338,0.00627659,0.01826267,-0.00330463,-0.13443235,-0.06340074,-0.05581559,-0.00322798,-0.01529251,-0.00956608,-0.01210078,-0.017074,-0.04402099,-0.00955853,0.04217308,-0.08892623,-0.02271252,0.10607377,-0.00626736,0.00566606,0.02288824,-0.01831681,0.01740594,0.00571436,-0.00751035,-0.05668956,0.00270718,-0.0099148,0.06566179,0.13859825,-0.05818686,0.04063382,-0.01589776,-0.03440213,-0.02796623,0.11708284,0.00193031,-0.06078687,-0.03996484,0.02060865,0.01516653,-0.09156048,0.01494905,0.01516104,-0.01248905,0.0028333,0.0670173,0.02210447,-0.02180201,-0.0619351,-0.0217944,0.04734722,0.01972457,-0.02543679,-0.07795388,0.03072703,-0.03201679,-0.0804365,0.00736873,-0.03879489,0.04311498,0.06211542,-0.04853415,0.02348423,0.00552963,0.05114404,-0.01890919,-0.06790283,-0.00074613,0.00213651,0.05160864,-0.0034199,0.09655378,-0.0261005,0.02879047,0.00940133,-0.01266471,0.05222248,-0.01592028,-0.05331411,0.09756904,0.01528975,-0.06215574,0.01684726,0.05315602,0.03178079,-0.03084078,-0.00356403,-0.00786407,0.0182531,-0.02848822,0.01689849,0.00518966,0.048515,-0.03025999,-0.19877848,-0.03560926,-0.04893596,0.04326714,0.0013478,0.02284586,0.03143523,0.02869757,0.03011332,0.09372202,0.07469758,-0.03359935,-0.00003743,0.0515113,-0.01571496,-0.04445561,-0.07636707,-0.05489121,-0.01946919,0.03955544,-0.02649943,-0.01494404,-0.0661947,-0.01212098,0.04290295,-0.05354927,0.16851217,0.0128768,0.01361835,-0.00589851,0.06208972,0.05280122,-0.02286384,-0.03036491,0.04472349,0.03851048,-0.04801878,0.01275392,-0.01364276,-0.01512079,-0.09509716,0.00960569,-0.00227392,-0.07461551,-0.0265153,-0.00959705,0.01327391,-0.01660636,-0.03118937,0.03551584,0.00259315,0.00135114,0.00070984,0.05785783,0.06465527,-0.04333737,-0.08723305,0.00379681,-0.00641695,0.00312619,0.02743444,-0.01301321,0.03347977,-0.04892585,0.02146652,-0.00099943,0.00392193,0.01314725,0.01930624,-0.00057383,0.01740327,0.04991611,-0.03298015,-0.03930533,0.05832469,0.02981277,0.05378529,-0.03963729,-0.02703844,-0.0224511,0.02214426,-0.0680512,0.02926421,0.01122418,0.00422895,0.05477724,0.04094591,-0.04063558,0.02890246,-0.01549244,-0.01616638,0.00281423,-0.02938092,0.00300099,-0.00382363,0.01085963,-0.28155515,0.02224972,-0.05204767,0.03984163,-0.00330785,0.02261904,0.0253273,0.03033743,0.04744797,-0.03096917,0.03101012,0.036308,-0.0090436,-0.05019779,-0.04559448,-0.01533286,0.02653058,-0.02838952,0.08111655,-0.00625131,0.05971373,-0.00678911,0.23167385,0.01177757,0.00246034,0.02032443,0.00702577,0.02592837,-0.02590966,0.04192856,0.00797616,-0.00675717,0.07415225,0.01910211,-0.03936384,0.08819856,-0.0142761,0.04897944,-0.03248334,0.02412854,-0.02445047,0.00752906,-0.00092592,0.01300388,0.07602713,0.02956362,0.00125267,-0.0576131,-0.00573486,0.06742309,-0.11517487,-0.07498481,-0.08248553,-0.02532671,0.04759391,0.10762715,0.01132324,-0.01805759,-0.0118471,-0.02818106,-0.00060215,-0.00714163,0.05863229,0.00251249,0.02354957],"last_embed":{"hash":"mawxdw","tokens":113}}},"text":null,"length":0,"last_read":{"hash":"mawxdw","at":1753423453036},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{4}","lines":[135,135],"size":209,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06101245,-0.02529182,-0.00614537,-0.00000682,-0.05115378,0.03485094,-0.00335308,-0.01799684,0.0244234,-0.01832694,0.00052183,0.01673517,0.05880837,0.01049726,-0.00090752,-0.03141586,0.00118498,0.00494463,-0.07151215,-0.01250007,0.0740784,-0.03105897,-0.04535965,-0.03400984,0.06273521,-0.01006825,0.0198717,-0.06664503,-0.03383727,-0.22170946,0.00024476,0.0327771,-0.05311743,-0.07628636,-0.06858217,-0.02331693,0.02824314,0.08677922,-0.0112998,0.01445125,0.05111497,-0.01891124,0.01036526,-0.01999319,-0.01981432,-0.05949349,0.01992008,-0.00382848,0.06575048,-0.02324014,-0.02286841,-0.01411067,0.02421688,-0.02124288,0.0298836,0.02844465,0.03036982,0.11002164,0.01511357,0.00404176,0.02410121,0.05306114,-0.20090291,0.07246156,-0.07905478,-0.00802075,-0.01334444,0.02789784,0.01863896,0.12928352,0.026296,0.01903488,-0.04326224,0.08244377,0.06325167,-0.04387027,-0.0436583,-0.06492312,-0.02832687,0.00059867,-0.05133427,-0.00689351,0.02493613,0.03225406,0.01219194,0.05310103,0.06474688,0.05479003,0.04277397,-0.05163427,0.04283949,-0.02238851,-0.00020443,0.04604155,-0.01932739,0.00335168,0.04277613,-0.05108083,0.00277925,0.10941026,0.03631374,0.00528289,-0.02090442,0.01444508,0.0259004,-0.04145711,-0.00659816,-0.0510776,-0.06983972,0.04205797,0.01184801,0.01176325,0.03698435,-0.03942018,-0.02540164,0.01051089,-0.03225319,-0.01410193,-0.00493092,-0.01884476,-0.01210028,-0.01561506,0.01068432,0.01084988,-0.0083001,-0.01173241,0.05751899,0.04236947,0.02339804,0.01895119,0.04401145,0.01034417,-0.1039624,-0.06641841,-0.0348249,0.00708539,-0.01012694,-0.03747944,0.00603812,0.01002011,-0.0464193,0.04277955,0.07552216,-0.09230421,0.00998644,0.12717344,-0.00200671,0.01580617,-0.04234311,-0.01475591,-0.02920488,-0.01296436,0.00821894,-0.01449855,0.02778591,-0.00519134,0.08316579,0.12094981,-0.07233649,0.00542404,-0.02363428,-0.00866206,-0.03934713,0.12112528,-0.06485666,-0.07557462,-0.03255318,0.04252163,0.00047077,-0.10034476,-0.02641531,0.06472955,-0.02727252,0.01069745,0.12498028,-0.00845364,-0.03666715,-0.03777732,-0.07176519,0.01690155,0.02340716,-0.04228314,-0.06010211,0.02156001,-0.03013869,-0.09728611,0.05245151,-0.05419994,0.04953063,0.04532532,-0.09731583,0.0206804,-0.00247828,0.04141832,-0.02915766,-0.02653983,-0.04922204,-0.00846379,0.05144985,-0.0411462,0.05036548,-0.02592494,0.02007244,0.01110085,0.03112571,0.08493342,-0.00659439,-0.06850071,0.10549738,0.01720898,-0.07296064,0.05820904,0.08161929,0.06813192,-0.0895775,0.02689089,-0.01069485,0.03861775,-0.02386867,-0.00008615,0.04721723,0.04566061,-0.01781172,-0.18431932,-0.02076242,-0.02734639,0.00486268,0.01055945,0.03701613,0.01234405,0.02124374,0.02511135,0.10261216,0.08582575,-0.07981404,0.02753134,0.04050159,-0.0295371,-0.01582561,-0.06984308,-0.06826392,-0.04544652,0.02123497,-0.01315373,-0.01857399,-0.03790726,-0.04844531,0.03887874,-0.01877658,0.14151958,-0.01981615,0.05228812,-0.04483023,0.04280355,-0.00355465,-0.00375985,-0.02105785,0.02129124,0.0246675,-0.0858862,-0.00233806,0.02120559,-0.03767442,-0.03239606,0.00157059,-0.05310344,-0.09813458,-0.02005621,-0.00064275,-0.00598287,0.04505166,-0.02765973,0.00136332,0.03563847,-0.00146364,-0.01938333,0.0724337,0.05317521,-0.09509262,-0.08436928,0.01533154,-0.02833658,0.0375183,0.00746465,-0.03130743,-0.02058226,-0.03992031,0.02873547,-0.00740307,-0.00636203,-0.04419931,0.00973026,-0.01294018,-0.03340877,0.04427483,-0.02431893,0.02121679,0.03297579,0.01341755,0.06212105,0.01010245,-0.02245964,-0.01522002,0.02838486,-0.07071593,0.03936967,0.04753957,0.0298027,0.05905396,0.04661697,-0.04449568,-0.01197823,-0.01496613,-0.06550511,-0.00656463,-0.03951203,0.03499543,-0.00571075,-0.00206929,-0.24438955,0.02880501,-0.05018234,0.0301342,0.00011402,0.0034289,0.00179538,-0.01546731,0.02070808,-0.01576486,0.00034495,0.05417824,-0.01753042,-0.06501101,-0.04065063,-0.00352897,0.05563001,-0.00631085,0.04398437,-0.00657778,0.08472886,0.01735439,0.25032192,0.02081598,0.00938627,0.07003397,0.00995133,0.00255412,-0.03238719,0.06801777,0.03486216,0.01696995,0.09102705,0.00229907,-0.01451651,0.06295284,-0.03612559,0.03247015,0.00748649,0.02958426,0.00521177,-0.00106427,-0.03183223,0.00856834,0.09281848,0.03234845,-0.02607173,-0.02500866,-0.00668059,0.05376263,-0.10309597,-0.03143448,-0.07469183,-0.00090325,0.01966029,0.07232527,0.01357337,-0.02195663,0.02224715,0.01413771,0.04288142,-0.00872419,0.04466296,-0.02575419,0.01344098],"last_embed":{"hash":"1k3kvep","tokens":158}}},"text":null,"length":0,"last_read":{"hash":"1k3kvep","at":1753423453069},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{6}","lines":[137,137],"size":487,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08418021,-0.00767052,-0.00581327,-0.01076929,-0.01846861,0.04189178,-0.02837141,-0.0659923,0.0256828,0.00416705,0.00305183,-0.02413434,0.08582152,-0.00596985,-0.01827899,-0.04265256,-0.00984926,-0.00588234,-0.03621967,-0.02682515,0.06396888,-0.04259088,-0.03659204,-0.06932484,0.06494106,0.00057443,0.00547887,-0.06885616,-0.02746013,-0.23874307,0.03652348,0.00955464,-0.06175707,-0.07916948,-0.0660629,-0.01598915,-0.00709838,0.11497997,-0.03009608,0.02235871,-0.00666126,0.01322718,-0.03581849,-0.01761462,-0.02895411,-0.04439769,-0.00811848,0.0105798,0.04343436,-0.02325955,-0.03662118,-0.00044261,0.03275256,-0.03237436,0.02594364,0.00061803,0.04557838,0.07872631,-0.02857558,0.01407201,0.04067608,0.0694398,-0.19597624,0.02857098,-0.02926817,0.046421,-0.0313044,0.02588514,-0.00410297,0.11539441,-0.01834656,-0.00346272,-0.0041475,0.10326183,0.05514411,-0.01147457,-0.06378187,-0.04570595,-0.05868969,0.04458312,-0.05048802,-0.02227877,-0.02007851,0.05956089,-0.0088926,0.06227608,0.02758149,0.10494971,0.05587227,-0.0552562,0.04190639,-0.00543166,0.03384277,0.04266483,-0.02921337,0.03602886,0.05437449,-0.06157114,0.03675981,0.10324246,0.04517144,-0.01628435,-0.00442644,0.02834818,0.01672625,-0.04449939,-0.01867665,-0.05324246,-0.03414923,0.01328398,0.03505063,-0.00863189,0.05704396,-0.04885698,-0.03431941,0.00289884,-0.01982792,0.01380379,-0.02381191,-0.01453328,0.00223695,0.00933811,-0.01849067,0.01489677,0.01110797,-0.03311672,0.04106588,0.06127373,0.04012554,0.0203614,0.03128896,-0.00104052,-0.10417247,-0.06228644,-0.03701378,-0.03397961,-0.01507357,-0.02665094,0.00004263,-0.02707718,-0.05691914,-0.00149906,0.05323323,-0.09556458,0.0127717,0.09691413,-0.00323026,0.01280519,-0.00202577,-0.01080789,0.00641601,-0.00306007,-0.0076617,-0.02390498,0.04120325,0.00412531,0.05883829,0.12095466,-0.0732148,0.0349225,-0.06084101,0.00054262,-0.03156561,0.12997507,-0.00755254,-0.07407389,-0.05542618,0.02967015,0.01148671,-0.07854049,0.01369877,0.01452292,-0.02673472,-0.03510769,0.08221008,0.00725834,-0.03014379,-0.05550855,-0.02876003,0.04503548,0.02869756,-0.03376871,-0.06802133,0.0106729,-0.04109687,-0.12270162,0.02257666,-0.06203537,0.0306129,0.05734314,-0.05587521,0.03739662,0.00809313,-0.00093936,-0.00456239,-0.05441763,-0.02925638,-0.00970637,0.05386827,-0.04022024,0.06897406,0.0098247,0.04087163,0.02831142,0.01717914,0.04677072,-0.02781964,-0.06171265,0.12069971,0.02834694,-0.03116681,0.01566244,0.0548185,0.06618287,-0.0761938,0.03613306,-0.01855644,0.00601206,-0.02040235,0.02019933,0.02521486,0.05258182,0.00918921,-0.21078332,-0.00253199,-0.0433244,0.02132388,0.00249243,-0.00572032,-0.00763612,0.0191116,-0.01877112,0.06186891,0.08954351,-0.04186869,0.01462518,0.07774667,-0.01750177,-0.03284208,-0.07165299,-0.06102239,-0.03337004,0.06958289,-0.02383577,-0.01306107,-0.03157427,-0.03890467,0.06169954,-0.05290687,0.14011408,0.00515519,0.09077207,-0.00128349,0.04711746,0.05896681,-0.00568646,-0.01896336,0.03533017,0.01615979,-0.00845224,-0.00227603,0.01665566,-0.00549133,-0.06489193,0.03622949,-0.02038347,-0.10463972,0.00766151,-0.02584174,-0.02069236,0.04322239,-0.02321625,0.06119971,0.01784047,0.01297989,0.02782297,0.04174107,0.07909836,-0.07659453,-0.0820643,0.04562682,-0.01922529,0.00614132,0.03765612,-0.00237035,-0.01692454,-0.02872269,0.00030309,-0.00350596,-0.0015246,-0.00846404,0.03601078,-0.0190093,-0.00087639,0.05844535,-0.02870985,-0.02557,0.03079259,0.01852159,0.0468007,-0.04133616,-0.03585777,-0.03532416,0.02777889,-0.05450181,0.00799942,0.03779655,-0.01906534,0.02266238,0.0391174,-0.01071023,0.02002137,-0.01407498,-0.068294,0.01136905,0.0044382,0.05729792,0.01562494,-0.01190994,-0.27015647,0.02049314,-0.04742623,0.02563559,-0.00606501,0.0174815,0.0069188,-0.00082814,0.01571213,-0.03071831,0.00863515,0.01105772,-0.0035638,-0.0737239,-0.03517684,0.01413276,0.06524139,-0.01928736,0.04400156,-0.02387159,0.07130291,0.02384151,0.23046353,0.03904866,0.015647,0.04633407,-0.0034163,0.04573873,-0.00992054,0.03210992,0.01915372,0.0127933,0.07825141,-0.02902056,-0.02996049,0.07087268,0.01013234,0.0514089,-0.0073133,0.00613248,-0.04206844,0.00779135,0.01024557,0.00137654,0.07373933,0.00437279,-0.00235954,-0.05564935,-0.00456308,0.08019801,-0.10757567,-0.04162653,-0.06265018,-0.0348617,0.07759602,0.08129781,-0.01518265,-0.05263694,0.00954534,-0.00080781,0.05733794,0.01188137,0.08887053,0.00510247,-0.01872391],"last_embed":{"hash":"gchb8","tokens":139}}},"text":null,"length":0,"last_read":{"hash":"gchb8","at":1753423453112},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>#{7}","lines":[138,139],"size":320,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>V. Generate Combinations from <i>Delta</i> Lottery Strategies, Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0477417,-0.02923307,-0.01995177,-0.04750205,-0.04848675,0.02484822,0.02133631,-0.03754526,-0.00001381,0.00760563,-0.03475937,-0.0257379,0.08446086,0.01595366,0.00826229,-0.01652136,-0.00859677,-0.01332935,-0.08242106,0.01154918,0.10963789,-0.03547381,-0.06158352,-0.06893124,0.0408039,-0.04624972,0.02320364,-0.07943626,-0.01348081,-0.24876793,0.04427888,0.04773671,-0.03191181,-0.06350206,-0.06455088,-0.04280485,-0.00112084,0.07388479,-0.07203417,0.00964977,0.00301164,-0.00792826,0.01701863,-0.09412701,-0.02308926,-0.04215239,0.01483449,-0.02103807,0.07226287,-0.02362696,-0.02270379,0.0171744,0.0404495,0.05138023,0.00451686,0.02021149,0.00077883,0.1214889,-0.01342474,0.00094504,0.03068521,0.04361949,-0.18495741,0.05398544,-0.04050039,0.02669044,-0.02169828,-0.03421212,-0.00023459,0.06752931,-0.00761785,0.07013564,-0.018982,0.04983344,0.04680286,-0.01912485,-0.11171604,-0.06184285,-0.02183666,0.03678304,-0.05179945,-0.03721746,-0.01946804,0.01175301,0.02990446,0.05257679,0.05489346,0.05552797,0.04401048,-0.02349186,-0.01216587,-0.01533168,0.01898032,0.08213348,0.01694096,0.02814097,0.04093416,-0.04211074,-0.00926451,0.10700294,0.04854808,0.00330709,-0.02596604,-0.01050415,0.0010516,-0.01949667,-0.04637741,-0.04127204,-0.03288204,0.03547531,0.03356125,-0.01933238,0.05312537,-0.02805325,-0.08740103,0.01394044,0.01687789,0.00902664,0.01429922,-0.02621793,-0.04433538,-0.00688644,-0.02844831,-0.00769934,0.03154052,0.01760278,0.03519113,0.09719985,0.01384119,0.02670268,0.04002947,-0.00303435,-0.10138606,-0.02390998,-0.04517575,-0.03292073,-0.01510436,-0.02067724,-0.01011626,-0.00217852,-0.05801896,-0.02006256,0.06118635,-0.05328212,0.01403533,0.07914798,-0.03553888,0.03610123,0.00420558,-0.0581238,-0.00900419,-0.01821312,0.00619518,-0.05488764,0.04525678,0.0387507,0.08043495,0.10361384,-0.06612722,0.05392142,-0.02098214,0.00473098,-0.05233908,0.10494595,0.03950153,-0.05754176,-0.0617726,0.04269916,-0.02409513,-0.02180482,-0.00377365,0.02588883,-0.06656753,0.01995571,0.07811932,-0.02080954,-0.06041046,-0.03827817,-0.02970616,0.01164898,-0.01404493,-0.04009141,-0.05650619,0.00012125,-0.04290018,-0.11550827,0.03661157,-0.0541767,0.03892519,0.0514082,-0.02401909,0.01728102,0.01999309,-0.01478648,0.03078224,-0.03722017,-0.03367895,-0.05818994,0.06491233,-0.0328771,0.03310896,0.02215144,0.02418958,0.02955156,-0.02513172,0.07764623,-0.03449887,-0.06975766,0.12375518,0.03404481,-0.02224502,0.03227888,0.03959296,0.05965556,-0.05954485,0.04498156,-0.00966965,0.01705062,0.00772976,0.00358572,0.03722918,0.07965265,-0.05498734,-0.19892514,0.02417728,-0.06157624,0.05263893,-0.02185312,-0.01366661,0.01981963,-0.02773457,-0.00133046,0.09610369,0.08534554,-0.05627716,0.01913635,0.07562265,-0.01962329,-0.01839522,-0.07150484,-0.01754827,-0.04259251,-0.01186848,0.00814586,0.01084382,0.02199714,-0.06048916,0.03177481,-0.03756555,0.15666577,-0.02027008,0.07944306,-0.0071499,0.06256866,0.05086192,0.02662354,0.03471299,-0.00950194,0.05468499,-0.06594097,-0.02197007,0.03190585,0.03064425,-0.04228635,0.02440499,-0.04290287,-0.13381857,0.02369766,-0.01755609,-0.01378311,0.07995413,-0.03975473,0.04852308,0.00418931,-0.02209304,0.03920719,0.01306304,0.06409425,-0.06504927,-0.09289853,0.03138424,-0.00563526,0.00693964,-0.00508895,-0.03708544,0.00580861,-0.0594875,-0.00677486,0.01566538,-0.01219257,0.00273496,0.03629896,-0.01540343,-0.01701221,0.08150009,-0.02400739,0.0094133,-0.00362204,0.01997494,0.02238829,-0.02059007,-0.02615475,-0.02868513,0.03644923,-0.03835844,0.01329089,0.06319321,-0.00325231,0.02321248,0.04760264,0.01116832,0.05277594,-0.03670868,-0.03396577,0.03235257,-0.0443958,0.04811611,0.02760508,-0.02535113,-0.24198511,0.00841016,-0.06921811,0.03252151,0.00661797,0.01991189,0.01298178,0.01238896,0.00342356,-0.05556791,-0.04556441,0.00814668,0.01149717,-0.08098944,-0.04193786,0.02258623,0.02752075,-0.03879878,0.05688387,-0.01454398,0.09523591,0.02268761,0.20952651,0.05949266,0.02424713,0.03604963,0.03392834,-0.00020554,-0.03885418,0.03857655,0.02455238,0.03068079,0.11874323,-0.04995133,-0.01958077,0.04294247,-0.02614406,0.01703794,0.00343109,-0.01761083,-0.02342345,0.02912781,0.00883175,-0.02211016,0.0880321,-0.0074636,-0.00522762,-0.03138975,0.03058126,0.07745516,-0.11050772,0.00438004,-0.02796903,-0.00282093,0.06425475,0.03570596,0.00614899,-0.02908646,-0.01077747,0.00933268,0.04146882,0.02554077,0.04983758,0.00505097,-0.00459973],"last_embed":{"hash":"c1kq3v","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"c1kq3v","at":1753423453155},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>V. Generate Combinations from <i>Delta</i> Lottery Strategies, Systems</u>","lines":[140,155],"size":1771,"outlinks":[{"title":"The two lotto delta combination generators are easy to understand and run.","target":"https://saliu.com/images/delta-lotto-generator.gif","line":5},{"title":"The computer processor is tested with delta filters in 6-number lotto games.","target":"https://saliu.com/images/delta-lottery-filters.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>V. Generate Combinations from <i>Delta</i> Lottery Strategies, Systems</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04690605,-0.02783725,-0.01966638,-0.04705909,-0.0458005,0.02371404,0.01786572,-0.03773035,-0.00057617,0.00960416,-0.03570417,-0.02646791,0.08528181,0.01480205,0.00902264,-0.01500793,-0.00906911,-0.01199619,-0.08199715,0.01128396,0.11279651,-0.03500583,-0.06012623,-0.06748248,0.04087572,-0.04698357,0.02317018,-0.08000586,-0.01191703,-0.24881215,0.04551328,0.04777141,-0.03159429,-0.06325024,-0.06394377,-0.0417272,-0.00122127,0.07258654,-0.06986834,0.0099546,0.00218285,-0.00737786,0.01723625,-0.09403154,-0.02268637,-0.04160769,0.01559508,-0.02073755,0.07070717,-0.02334986,-0.02084136,0.02048324,0.04071175,0.05109903,0.0047245,0.01912172,-0.00021687,0.12272366,-0.01608953,0.00064288,0.02921444,0.04534838,-0.18651119,0.0529822,-0.04050061,0.02665246,-0.02004066,-0.03471933,0.00115341,0.06661722,-0.01055078,0.07015899,-0.01981269,0.04950156,0.04653301,-0.01885517,-0.11243121,-0.06268278,-0.01938491,0.03790118,-0.05150633,-0.0383372,-0.02079869,0.01248589,0.02889101,0.05066233,0.0536524,0.05572017,0.04406316,-0.02326003,-0.0150253,-0.01691762,0.01965887,0.08281921,0.0180021,0.02608311,0.03984982,-0.04125299,-0.00864114,0.10638166,0.04782631,0.00328648,-0.02486982,-0.0100361,0.00091933,-0.02032522,-0.04736698,-0.04259321,-0.03286075,0.0357137,0.03261644,-0.01847322,0.04936635,-0.02908213,-0.08465034,0.01475975,0.01696933,0.0082625,0.01306007,-0.02477556,-0.04570531,-0.00772978,-0.02708662,-0.00650714,0.03237006,0.01801181,0.03577819,0.09842974,0.01330054,0.02606589,0.04202378,-0.00456784,-0.10058846,-0.02487841,-0.04572805,-0.03551054,-0.01483083,-0.02119184,-0.00802465,-0.001917,-0.05804814,-0.0187614,0.06006958,-0.05415092,0.01562147,0.08068392,-0.03495715,0.03652352,0.00361523,-0.05626747,-0.01082388,-0.01760884,0.00683559,-0.05563732,0.0462187,0.03878962,0.07968719,0.10411559,-0.0673191,0.05529141,-0.02006827,0.0043756,-0.05193938,0.10761356,0.03869952,-0.05552367,-0.06061435,0.04207235,-0.02495911,-0.02077768,-0.00448731,0.02760544,-0.06371234,0.018282,0.07846949,-0.02061307,-0.06198926,-0.03605898,-0.03212067,0.01281091,-0.01535618,-0.03899924,-0.05587177,-0.00133646,-0.04164252,-0.11548406,0.03746166,-0.05402599,0.03802113,0.04974222,-0.02653834,0.01750717,0.01938744,-0.01555101,0.0319738,-0.03675322,-0.03473405,-0.05818189,0.06605489,-0.03312281,0.03380497,0.02354586,0.02593934,0.02876452,-0.02408622,0.07856645,-0.03444538,-0.06781688,0.12253752,0.03444232,-0.02188607,0.03110276,0.04003834,0.05912348,-0.06087212,0.04534188,-0.01040611,0.01538016,0.00745653,0.00423139,0.03635951,0.08180031,-0.05654908,-0.19834809,0.02075006,-0.06102797,0.05018108,-0.02402799,-0.01502935,0.01852894,-0.02667403,-0.00274886,0.09392486,0.08416282,-0.05480164,0.01782729,0.07688051,-0.01975561,-0.01896697,-0.07092633,-0.0185243,-0.04254203,-0.00977999,0.00819067,0.01092039,0.02298117,-0.06086795,0.02946815,-0.03880413,0.15809624,-0.02128348,0.08068492,-0.00677958,0.0621841,0.04991334,0.02839233,0.03258175,-0.00725914,0.05618061,-0.06683191,-0.02358774,0.03285604,0.03313595,-0.04174496,0.02375424,-0.04233621,-0.13407829,0.02547642,-0.01804216,-0.01384945,0.08015452,-0.03924389,0.04882378,0.00390276,-0.02126206,0.03964395,0.01339476,0.06444418,-0.06594811,-0.09410422,0.03200082,-0.00512479,0.00653372,-0.00490926,-0.03615198,0.00643001,-0.05940853,-0.00636749,0.015041,-0.01001436,0.00382754,0.03751035,-0.01623498,-0.0188577,0.08487508,-0.02547417,0.00961023,-0.00454192,0.01812528,0.02178962,-0.01951035,-0.02903943,-0.02925447,0.03478245,-0.03966423,0.01321696,0.06288882,-0.00414268,0.02077409,0.04787046,0.01077706,0.05367941,-0.03528639,-0.03353825,0.03117638,-0.0431083,0.04761637,0.02919557,-0.02453071,-0.24204259,0.00799289,-0.06813788,0.03201263,0.00571857,0.02180405,0.01200777,0.01163534,-0.00089895,-0.05607765,-0.04692592,0.00797009,0.01259611,-0.08011498,-0.03943525,0.0238066,0.02867427,-0.03971757,0.05754486,-0.0142778,0.09471944,0.01974573,0.21045132,0.057878,0.0233283,0.03741322,0.03177859,0.00196194,-0.0382123,0.03674648,0.02243943,0.0324528,0.1178789,-0.04923738,-0.02055127,0.04597005,-0.02679841,0.01643931,0.00191065,-0.01678713,-0.02549159,0.02863224,0.01002189,-0.02363105,0.08703674,-0.00701222,-0.00412969,-0.03173702,0.02943205,0.07758748,-0.10811882,0.00501994,-0.02731958,-0.0003742,0.066432,0.0352662,0.00713744,-0.03068057,-0.01050189,0.00659903,0.04427069,0.02668109,0.04727255,0.00445564,-0.00545725],"last_embed":{"hash":"yqzwi5","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"yqzwi5","at":1753423453305},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>V. Generate Combinations from <i>Delta</i> Lottery Strategies, Systems</u>#{1}","lines":[142,155],"size":1689,"outlinks":[{"title":"The two lotto delta combination generators are easy to understand and run.","target":"https://saliu.com/images/delta-lotto-generator.gif","line":3},{"title":"The computer processor is tested with delta filters in 6-number lotto games.","target":"https://saliu.com/images/delta-lottery-filters.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>VI. <i>Purge</i> Combinations from Output Files Applying Lotto <i>Delta</i> Filters</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06029227,-0.01195419,0.00211383,-0.02931895,0.02124736,-0.01344677,0.03662061,-0.0333721,0.03012165,-0.00508003,-0.01938468,-0.03231766,0.08187606,-0.00866833,-0.02209767,-0.02186703,0.02382674,-0.00362952,-0.08174192,-0.03086494,0.08034241,-0.05299244,-0.04043803,-0.06427112,0.04690766,0.0094085,-0.02623444,-0.06215361,-0.01019009,-0.24241129,0.03367076,-0.02054837,0.00251273,-0.11427457,-0.02379378,0.0073367,0.00520698,0.08797555,-0.08197172,0.00298569,0.01205912,0.01360142,-0.04174734,-0.04864411,-0.06245681,-0.02648921,-0.00347746,-0.00326905,0.02936547,-0.01298215,-0.02559474,-0.01988995,0.01637225,0.03438661,0.00385821,0.00685727,0.01468148,0.09291018,-0.03815353,0.01701359,0.0618944,0.05285346,-0.17332691,0.02878834,-0.00467476,0.01066785,-0.02859002,-0.03083688,0.02188286,0.10176777,-0.0461414,0.04405167,-0.04389171,0.1022779,0.02875505,-0.01503548,-0.04223698,-0.0748686,-0.07326185,0.02856562,-0.01466474,-0.0290513,-0.01970353,0.04566519,-0.02095757,0.04931351,0.040347,0.06962636,0.01932724,-0.04035206,0.00352285,0.00282115,0.06085257,0.07681651,0.00052056,-0.00687889,0.05175359,-0.00155365,0.0660252,0.12144347,-0.00326553,-0.03611252,0.01040618,-0.03502673,0.03168498,-0.00997865,0.00330264,-0.0096352,-0.01924331,0.02925062,0.05195432,-0.02486338,0.01880101,-0.06992364,-0.02948678,0.022885,0.0149229,0.00853876,-0.03163463,-0.01042881,-0.00110078,0.01272751,-0.01330369,0.02373205,0.03367693,0.02831572,0.05709004,0.08447269,0.04619376,0.01089019,0.02212686,-0.00213594,-0.09571358,-0.04006048,-0.03807329,-0.0090577,-0.01245837,-0.04204292,-0.01649725,-0.01283163,-0.07641287,0.0170274,0.05689342,-0.08704827,0.02562912,0.14662568,-0.01092086,-0.03015425,-0.00633467,-0.01592229,0.02951975,0.02400381,-0.0332719,-0.045035,0.04884053,-0.02795792,0.06357302,0.10595264,-0.09481987,0.03874129,-0.01194533,0.00598008,-0.01273687,0.1250914,0.00476379,-0.03238728,-0.0509952,0.03589543,-0.02540735,-0.01615884,-0.01056461,0.00841597,-0.02083924,-0.00736131,0.06879316,-0.00176682,-0.02791779,-0.02185532,-0.03736067,0.00243662,0.004638,-0.05230947,-0.05104813,-0.00456891,0.01519294,-0.10980681,0.01495524,-0.04137067,0.02910515,0.09840021,-0.05885444,0.00409259,0.03917784,-0.03234964,-0.00015463,-0.03908276,-0.00396298,-0.01036026,-0.00650458,-0.05679036,0.04333092,-0.00011503,0.03145676,0.01765873,0.04108976,0.04955904,-0.08641469,-0.05566907,0.08387158,0.02668012,-0.04860355,0.04283337,0.02718588,0.07658691,-0.02464886,0.0184918,-0.01983481,-0.00912827,0.0107904,-0.00148734,0.02914299,0.02227114,-0.04558435,-0.23598678,0.01327544,-0.05377348,0.0310041,0.02714016,-0.03512707,-0.03655398,0.02198715,0.01332771,0.09942291,0.06452434,-0.0434921,-0.01084538,0.06804591,-0.00871147,-0.04418078,-0.03453288,-0.03339615,-0.06694552,0.03500744,-0.01028547,-0.04863738,0.02342568,-0.0216574,0.06339906,-0.01707264,0.13779499,0.0021312,0.11340985,0.02255362,0.04071476,0.07756182,-0.0016164,-0.03964191,0.03240893,0.03613174,-0.04062643,-0.02834371,0.0244492,-0.01894063,-0.05901282,-0.00437364,-0.01358025,-0.10607007,0.04635106,0.03034721,-0.06958425,0.02058081,-0.08086784,0.03839938,0.03461424,0.01656134,0.02011458,0.03964005,0.11866402,-0.06119768,-0.05642293,0.00962118,-0.01668608,-0.00702403,0.03378503,0.00168123,-0.01723749,-0.05713853,0.02871041,0.02786393,-0.0057193,-0.00528496,-0.02571938,-0.02371802,-0.05351056,0.06663159,-0.06944194,-0.02496184,0.00177005,-0.00603559,0.02524204,-0.00590585,-0.04898591,-0.08101545,0.04926492,-0.03735325,-0.02096807,0.06515631,-0.00442732,0.05536187,0.06073579,-0.0216456,0.01100508,0.00551098,-0.04587985,0.04045574,0.01797788,0.03723205,0.05213531,0.03198321,-0.25647828,-0.00899251,-0.01080906,0.06828471,0.02288572,0.03036474,-0.00134188,0.01100775,0.04144512,-0.0171136,-0.00499332,0.0353328,0.02071303,-0.07512592,-0.03772978,0.00001225,0.03653765,-0.05057536,0.04707894,0.01016762,0.05824112,-0.00043263,0.22983997,-0.00435443,-0.01504656,0.02581515,-0.00140965,0.0174073,-0.01829497,0.04847148,0.04108751,-0.00691748,0.09728009,-0.00058719,-0.0622982,0.0601951,0.00103162,0.04056736,-0.01855158,-0.00781323,-0.05352139,-0.00215163,-0.02200893,-0.02283412,0.08718293,0.01512299,-0.02431384,-0.06362692,0.01973876,0.05278918,-0.09112479,-0.00321477,-0.03476559,-0.03607345,0.05968004,0.07330631,-0.01338128,-0.03552981,0.00802697,0.01276531,0.06700838,0.02676117,0.04295873,0.00339182,-0.0127523],"last_embed":{"hash":"1944nkk","tokens":123}}},"text":null,"length":0,"last_read":{"hash":"1944nkk","at":1753423453452},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>VI. <i>Purge</i> Combinations from Output Files Applying Lotto <i>Delta</i> Filters</u>","lines":[156,161],"size":349,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>VI. <i>Purge</i> Combinations from Output Files Applying Lotto <i>Delta</i> Filters</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05736927,-0.01169027,0.00089,-0.03351346,0.02200485,-0.01233997,0.03534102,-0.03070372,0.03295868,-0.0030995,-0.01965407,-0.03209624,0.0826364,-0.00751413,-0.02040889,-0.02382251,0.02189674,-0.00579703,-0.08299051,-0.0313892,0.08002668,-0.04993167,-0.04135124,-0.06517704,0.04252703,0.00699195,-0.02614667,-0.05944226,-0.01422127,-0.24192014,0.03525649,-0.02013387,0.0022918,-0.11319008,-0.02330638,0.00683362,0.00637813,0.08537839,-0.08483947,0.00353797,0.01069363,0.01320512,-0.04568353,-0.04997534,-0.06324141,-0.02639255,-0.0062764,-0.00461967,0.02899456,-0.01165841,-0.02326171,-0.02002761,0.01475953,0.03936028,0.00198951,0.0060486,0.01636179,0.09516929,-0.03968156,0.01868266,0.05974317,0.05447959,-0.17231067,0.02946408,-0.00440268,0.00974645,-0.02948229,-0.03349124,0.02308233,0.1062649,-0.0490235,0.04381494,-0.04371257,0.10578371,0.02929826,-0.02006286,-0.04123065,-0.07288452,-0.0699546,0.02932494,-0.01496171,-0.02949109,-0.02181329,0.0451603,-0.0240043,0.0472693,0.03914187,0.06928904,0.0178353,-0.03874803,0.00049053,0.00377228,0.06402847,0.07560897,0.00121661,-0.0098419,0.05057269,0.00055805,0.0675217,0.12263733,-0.00565583,-0.03611601,0.0099802,-0.03520845,0.03272972,-0.01031426,0.00236281,-0.01140192,-0.01879833,0.03023331,0.05158071,-0.02338544,0.01494041,-0.07042022,-0.02744004,0.02320281,0.01207943,0.00933082,-0.03361279,-0.00990213,-0.00358153,0.01426814,-0.0107788,0.02373109,0.03572088,0.02754168,0.05688288,0.08559064,0.04566466,0.0071162,0.02120549,-0.00268224,-0.09679884,-0.04145001,-0.0386502,-0.00882851,-0.01373022,-0.04303632,-0.0171352,-0.01292647,-0.07615209,0.02155616,0.05629437,-0.0866063,0.02523271,0.14983939,-0.0073259,-0.03030533,-0.0047708,-0.01809299,0.02841114,0.02282226,-0.03338967,-0.04971237,0.04525428,-0.02792478,0.06498626,0.10395496,-0.0935067,0.0385946,-0.01241303,0.00652755,-0.00871549,0.12625742,0.00060143,-0.03133681,-0.04826482,0.03418311,-0.02462841,-0.01451833,-0.01120306,0.00761411,-0.02171602,-0.0084368,0.06902377,-0.00518016,-0.0325998,-0.02118639,-0.0374054,0.00488091,0.00749268,-0.04905432,-0.04891495,-0.00467041,0.01598268,-0.10831914,0.01580429,-0.04064775,0.02885822,0.09840958,-0.06124353,0.00631616,0.03872843,-0.03250413,0.00155429,-0.03492766,-0.00381009,-0.01129845,-0.00729299,-0.0576114,0.04246358,0.00205961,0.02991239,0.01611622,0.04116851,0.04797888,-0.08522268,-0.05545511,0.08297309,0.02631288,-0.04817079,0.04373125,0.02553358,0.07681648,-0.02452325,0.01605529,-0.01982171,-0.01008801,0.00803615,-0.00183999,0.03228524,0.02478958,-0.04600732,-0.23476635,0.00846601,-0.05484208,0.02715115,0.02429126,-0.03729016,-0.03531298,0.02153617,0.01378004,0.09552306,0.06338979,-0.03893996,-0.01229393,0.06625483,-0.00633595,-0.04126092,-0.03689146,-0.02999941,-0.06780604,0.03494986,-0.00991331,-0.04941061,0.02716905,-0.02012359,0.06504279,-0.01611759,0.13641165,0.0025234,0.11324541,0.02168032,0.03832141,0.07695055,-0.00241379,-0.04397879,0.03467722,0.03741077,-0.04097366,-0.030859,0.02559419,-0.01858473,-0.061368,-0.00285551,-0.01267346,-0.10750961,0.04533537,0.03160352,-0.07006543,0.01957947,-0.07889017,0.04212962,0.03662187,0.01629246,0.01887917,0.04122059,0.11633517,-0.05945783,-0.05491943,0.01179329,-0.01573444,-0.00801341,0.03472317,0.00222234,-0.01699306,-0.05551199,0.03070933,0.02532062,-0.00800322,-0.00741946,-0.02443601,-0.02313951,-0.05355702,0.07029942,-0.07221895,-0.02578815,0.00083391,-0.00706505,0.02592394,-0.00553727,-0.05312124,-0.0819828,0.04958833,-0.03598412,-0.02039381,0.06445343,-0.00451489,0.05457003,0.06241114,-0.01889723,0.0101488,0.00633309,-0.04626089,0.03914476,0.02035689,0.03479533,0.05189783,0.03156817,-0.25586876,-0.00751329,-0.0099015,0.06996061,0.02284602,0.02815587,0.00121839,0.01279814,0.04034265,-0.01701893,-0.00121427,0.03559111,0.02128434,-0.07843881,-0.03469063,-0.00025974,0.03886914,-0.05080661,0.04897223,0.01102979,0.05839765,-0.00393752,0.23160946,-0.00806674,-0.01416355,0.02439104,-0.00263182,0.02032016,-0.01647779,0.04916058,0.04110589,-0.0047147,0.09539074,0.00353625,-0.06211996,0.0649991,0.00134886,0.036312,-0.01879884,-0.00821287,-0.05218725,-0.00563181,-0.02257569,-0.0253828,0.08626753,0.01179961,-0.02221676,-0.06510555,0.01942356,0.05321644,-0.08641071,-0.00315771,-0.03437812,-0.03265378,0.05810854,0.07062795,-0.01455047,-0.0315166,0.01011012,0.01253899,0.06983686,0.0305222,0.04184431,0.00724509,-0.01275227],"last_embed":{"hash":"as6wg4","tokens":122}}},"text":null,"length":0,"last_read":{"hash":"as6wg4","at":1753423453487},"key":"notes/saliu/Delta Lotto, Lottery Software, Systems, Strategies.md#Delta Lotto, Lottery Software, Systems, Strategies#<u>VI. <i>Purge</i> Combinations from Output Files Applying Lotto <i>Delta</i> Filters</u>#{1}","lines":[158,161],"size":254,"outlinks":[],"class_name":"SmartBlock"},
