"smart_sources:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md": {"path":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08269277,-0.03735485,-0.03606546,-0.02943608,-0.00311399,0.04287893,-0.04311218,0.00438281,0.05139717,-0.00184195,0.01559521,0.00780094,0.04393633,-0.030721,-0.02916213,-0.05093033,0.01203237,-0.00237443,-0.05845291,-0.02421482,0.05320362,-0.02476737,-0.07594556,-0.11327078,0.05804406,0.03211185,-0.04009487,-0.10217412,-0.05457636,-0.21330366,0.01602605,0.01981416,0.01224929,-0.04291274,-0.04885369,-0.00705151,-0.00567047,0.01468929,-0.02947002,0.05574932,0.00102016,0.01531052,0.00544695,-0.01172337,0.01699194,-0.03772416,-0.01656054,0.01751028,0.01897806,0.00737492,-0.10894986,-0.04155339,-0.00241857,0.02262861,0.06057352,0.00992503,0.03918708,0.06718329,0.00611517,0.04485177,0.04016429,0.06571422,-0.18966176,0.06020429,0.0339965,0.0250931,-0.01835008,-0.04592194,0.036396,0.01160413,-0.00843136,0.01927927,0.0061351,0.04395578,0.05976008,-0.03343616,-0.0656195,-0.04938645,-0.05707319,-0.00054173,-0.04651668,0.01758753,0.01157971,0.02278493,-0.02059941,0.03862756,0.04974524,0.01903694,0.10216828,-0.0683862,0.01565045,0.02251507,0.03897376,0.03616838,0.00311089,-0.02669694,0.05746627,-0.02711813,-0.00528762,0.11862948,-0.02203979,-0.01811313,0.04539228,0.00130515,0.03553877,-0.04455581,-0.027836,-0.06605096,-0.01237827,-0.00775221,0.02707526,0.00113877,0.02237841,-0.09093304,-0.04472801,-0.02843013,-0.02534965,-0.0387891,0.0396147,0.01104639,-0.02358685,-0.03714396,0.00958632,0.00318771,0.01233144,0.02371705,0.02351753,0.07700992,-0.00369092,0.01006484,0.07023349,0.05712545,-0.14542472,-0.029854,-0.01194741,-0.01550031,0.03725701,-0.0252556,0.01621812,0.0029827,-0.04146109,-0.02845768,0.06860349,-0.10797124,-0.04825221,0.11964107,0.01154535,-0.0051078,0.01050048,-0.03009192,0.00824668,-0.00321299,-0.02355802,-0.06627084,0.03453692,-0.00853591,0.1178043,0.0545244,-0.03507688,0.00622941,-0.01416303,-0.05156231,-0.01887516,0.12821433,-0.02530802,-0.12402951,-0.014899,0.05879805,-0.01160146,-0.07291985,0.01843044,-0.0127747,-0.04245685,0.03852344,0.07393621,-0.00271288,-0.04770447,-0.02978563,0.02908547,0.01021391,0.02257578,-0.04117754,-0.04838591,0.03491284,-0.03929761,-0.10736667,-0.02014387,-0.02403689,-0.00772695,0.05082026,-0.00579256,0.00083801,-0.07337745,0.01788036,-0.05404146,-0.043558,-0.03322604,-0.02767211,0.05982571,-0.01964972,0.00887778,-0.01275874,0.01314689,0.02719157,0.01503348,0.01136624,-0.00558491,-0.05043351,0.05672099,0.03938292,-0.03226037,0.01613664,0.02103302,0.05119675,-0.0351454,0.06584863,0.02724134,0.01143144,-0.00873964,0.06008337,-0.01023804,0.02812298,-0.06241773,-0.18760872,-0.02494802,-0.00384305,-0.00272451,-0.03556376,-0.01968327,0.06638387,-0.03147418,0.00095923,0.11693577,0.10921867,-0.02760371,0.00295779,0.00085668,-0.00346859,-0.0025756,-0.06175227,-0.03840959,-0.04439453,0.06344163,0.01388538,0.00262091,-0.03786304,-0.05196808,0.01645794,-0.03151325,0.12402241,-0.03235007,0.01494409,0.02971384,0.1145927,-0.00924947,-0.02103677,-0.06832371,0.02462769,0.05970048,-0.03129438,0.01952897,-0.03362325,0.03505287,-0.09086215,0.03577179,0.03086407,-0.08123372,-0.04581955,0.02532585,0.01337961,-0.0386887,0.00508696,0.02870952,0.03064198,-0.03131634,0.02650154,0.0548375,0.06608006,-0.01478768,-0.05960398,0.02566704,-0.04025088,0.04065355,-0.03774879,-0.06504962,0.05529942,-0.0146011,0.03086494,0.013816,0.00060757,-0.01991237,-0.00518725,-0.01978992,0.00218737,0.11222888,0.00115711,0.03120323,-0.03024249,0.03755979,0.08430538,-0.04960665,-0.00687869,0.01365094,-0.0333604,-0.07344305,0.05449144,0.05014486,0.04837469,0.06835738,0.06152696,0.03689173,0.00582322,-0.01075435,0.03791552,-0.0350936,-0.0271852,0.0260253,0.04934307,0.03529529,-0.25746468,0.0267367,-0.00057386,0.02974595,0.03926884,-0.03359402,0.01459938,-0.05749518,0.01755594,-0.02791143,0.05810752,0.02836647,0.0091871,-0.064997,0.00568792,-0.01488745,0.04051166,-0.03028858,0.05035131,0.03774152,0.01753478,0.0340725,0.23237678,0.01295356,0.00568809,0.03062322,0.00632179,-0.0028056,0.01732084,0.04921862,0.00300034,-0.00249983,0.07680148,0.00651253,-0.06722917,0.03800358,-0.02565633,0.03007814,-0.01075863,0.03140801,-0.06868449,0.00523463,-0.03227505,-0.02673336,0.12140584,0.03434808,-0.0471664,-0.09645133,0.05552949,0.05296282,-0.0753001,-0.05545571,-0.08904324,-0.00335697,0.01129713,0.03246096,0.0283751,0.03506144,0.03069614,0.01769461,0.05214464,-0.03432195,0.0681472,0.06019453,0.03113138],"last_embed":{"hash":"1xejtn9","tokens":475}}},"last_read":{"hash":"1xejtn9","at":1753423455252},"class_name":"SmartSource","last_import":{"mtime":1735905174000,"size":28051,"at":1753230880657,"hash":"1rj9f5m"},"blocks":{"#---frontmatter---":[1,6],"#Excel Spreadsheets, Lottery Software, Lotto Programming":[8,243],"#Excel Spreadsheets, Lottery Software, Lotto Programming#{1}":[10,15],"#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)":[16,24],"#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)#{1}":[17,24],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>":[25,40],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>#{1}":[27,40],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>":[41,87],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>#{1}":[43,87],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>":[88,97],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>#{1}":[90,97],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>":[98,131],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>#{1}":[100,131],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>":[132,199],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{1}":[134,194],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{2}":[195,195],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{3}":[196,197],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{4}":[198,199],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>":[200,217],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>#{1}":[202,217],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>":[218,243],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{1}":[220,221],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{2}":[222,222],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{3}":[223,223],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{4}":[224,224],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{5}":[225,225],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{6}":[226,226],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{7}":[227,227],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{8}":[228,228],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{9}":[229,229],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{10}":[230,230],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{11}":[231,231],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{12}":[232,232],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{13}":[233,233],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{14}":[234,234],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{15}":[235,235],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{16}":[236,237],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{17}":[238,243]},"outlinks":[{"title":"Powerful lottery strategies, systems can be derived from analyses with an Excel spreadsheet.","target":"https://saliu.com/images/lotto.gif","line":14},{"title":"The Complexity of Lottery Software Programming","target":"https://saliu.com/Newsgroups.htm#Program","line":16},{"title":"Original Pick-3 Strategies, Lottery Software","target":"https://saliu.com/Newsgroups.htm#Strategy","line":17},{"title":"More on Gambling Formula and Lottery Statistics","target":"https://saliu.com/Newsgroups.htm#Formula","line":18},{"title":"Using _Excel Spreadsheets_ to Analyze the Lottery I","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":19},{"title":"Using _Excel Spreadsheets_ to Analyze the Lotto II","target":"https://saliu.com/Newsgroups.htm#SpreadII","line":20},{"title":"Resources in Lottery Software, Excel Spreadsheets, Lotto Wheeling","target":"https://saliu.com/Newsgroups.htm#Resources","line":21},{"title":"Using Excel spreadsheets is relatively easy for simple lotto data analyses.","target":"https://saliu.com/images/lottery.gif","line":23},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-lottery-software.gif","line":35},{"title":"Download lotto software, Excel spreadsheets to analyze and create lottery systems.","target":"https://saliu.com/images/lottery-software.gif","line":39},{"title":"Lottery software spreadsheet generates strategies based on positional limits of the lotto numbers.","target":"https://saliu.com/ScreenImgs/spreadsheet-pick-lottery.gif","line":45},{"title":"Each lotto number in a lottery drawing comes from a limited group of lotto numbers.","target":"https://saliu.com/ScreenImgs/lottery-strategy-spreadsheet.gif","line":49},{"title":"Run specialized lotto software programs, better than any Excel lottery spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":86},{"title":"_**Pick-3 Lottery Method, Strategy, System, Play, Software**_","target":"https://saliu.com/STR30.htm","line":90},{"title":"_**Lotto, Lottery Software, Strategy, Systems, Wheels**_","target":"https://saliu.com/LottoWin.htm","line":92},{"title":"Ion Saliu is the first programmer to use Excel spreadsheets as lottery software.","target":"https://saliu.com/images/lottery-software.gif","line":96},{"title":"Learn about programming the lotto games, lottery software with Excel spreadsheet software.","target":"https://saliu.com/images/lotto-software.gif","line":130},{"title":"_**Lottery Strategy by Lotto Software on <u>Positional Frequency</u>**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":196},{"title":"Analysis of Excel spreadsheets applied to lottery and lotto developing of software started here.","target":"https://saliu.com/images/lottery-software.gif","line":198},{"title":"**Software Utilities for Lottery**","target":"https://saliu.com/software-lotto-lottery.html","line":205},{"title":"Work with Excel spreadsheets for lotto software to generate lottery systems, strategies.","target":"https://saliu.com/HLINE.gif","line":209},{"title":"Theory of Probability Book founded on mathematics of lotto, lottery, Excel spreadsheets.","target":"https://saliu.com/probability-book-Saliu.jpg","line":211},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":211},{"title":"LotWon is lotto software to apply Excel spreadsheets to lottery systems.","target":"https://saliu.com/HLINE.gif","line":214},{"title":"\n\n## <u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>\n\n","target":"https://saliu.com/content/lottery.html","line":216},{"title":"_**Lottery Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":222},{"title":"_**Lotto, Lottery Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":223},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":225},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":227},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":228},{"title":"_**Genuine Powerball, Mega-Millions strategy, system**_","target":"https://saliu.com/powerball-systems.html","line":229},{"title":"**Lottery Skips Systems**","target":"https://saliu.com/skip-strategy.html","line":231},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":232},{"title":"_**Lottery filters, lotto filtering in software**_","target":"https://saliu.com/filters.html","line":233},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":234},{"title":"_**Starting Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":235},{"title":"_**Best Lottery Software, Lotto Software**_","target":"https://saliu.com/infodown.html","line":236},{"title":"Thanks for visiting the site of lotto software, lottery software, Excel spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":238},{"title":"Forums","target":"https://forums.saliu.com/","line":240},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":240},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":240},{"title":"Contents","target":"https://saliu.com/content/index.html","line":240},{"title":"Home","target":"https://saliu.com/index.htm","line":240},{"title":"Search","target":"https://saliu.com/Search.htm","line":240},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":240},{"title":"Lotto players use Excel spreadsheets to analyze or program lottery software.","target":"https://saliu.com/HLINE.gif","line":242}],"metadata":{"created":"2025-01-03T19:52:53 (UTC +08:00)","tags":["spreadsheet","Excel","spreadsheets","free","ranges","limits","median","lotto","lottery","formula","software","data","analysis","programming","statistics","strategy","combinations","rows","columns"],"source":"https://saliu.com/Newsgroups.htm","author":null},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md"},
"smart_sources:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md": {"path":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08269277,-0.03735485,-0.03606546,-0.02943608,-0.00311399,0.04287893,-0.04311218,0.00438281,0.05139717,-0.00184195,0.01559521,0.00780094,0.04393633,-0.030721,-0.02916213,-0.05093033,0.01203237,-0.00237443,-0.05845291,-0.02421482,0.05320362,-0.02476737,-0.07594556,-0.11327078,0.05804406,0.03211185,-0.04009487,-0.10217412,-0.05457636,-0.21330366,0.01602605,0.01981416,0.01224929,-0.04291274,-0.04885369,-0.00705151,-0.00567047,0.01468929,-0.02947002,0.05574932,0.00102016,0.01531052,0.00544695,-0.01172337,0.01699194,-0.03772416,-0.01656054,0.01751028,0.01897806,0.00737492,-0.10894986,-0.04155339,-0.00241857,0.02262861,0.06057352,0.00992503,0.03918708,0.06718329,0.00611517,0.04485177,0.04016429,0.06571422,-0.18966176,0.06020429,0.0339965,0.0250931,-0.01835008,-0.04592194,0.036396,0.01160413,-0.00843136,0.01927927,0.0061351,0.04395578,0.05976008,-0.03343616,-0.0656195,-0.04938645,-0.05707319,-0.00054173,-0.04651668,0.01758753,0.01157971,0.02278493,-0.02059941,0.03862756,0.04974524,0.01903694,0.10216828,-0.0683862,0.01565045,0.02251507,0.03897376,0.03616838,0.00311089,-0.02669694,0.05746627,-0.02711813,-0.00528762,0.11862948,-0.02203979,-0.01811313,0.04539228,0.00130515,0.03553877,-0.04455581,-0.027836,-0.06605096,-0.01237827,-0.00775221,0.02707526,0.00113877,0.02237841,-0.09093304,-0.04472801,-0.02843013,-0.02534965,-0.0387891,0.0396147,0.01104639,-0.02358685,-0.03714396,0.00958632,0.00318771,0.01233144,0.02371705,0.02351753,0.07700992,-0.00369092,0.01006484,0.07023349,0.05712545,-0.14542472,-0.029854,-0.01194741,-0.01550031,0.03725701,-0.0252556,0.01621812,0.0029827,-0.04146109,-0.02845768,0.06860349,-0.10797124,-0.04825221,0.11964107,0.01154535,-0.0051078,0.01050048,-0.03009192,0.00824668,-0.00321299,-0.02355802,-0.06627084,0.03453692,-0.00853591,0.1178043,0.0545244,-0.03507688,0.00622941,-0.01416303,-0.05156231,-0.01887516,0.12821433,-0.02530802,-0.12402951,-0.014899,0.05879805,-0.01160146,-0.07291985,0.01843044,-0.0127747,-0.04245685,0.03852344,0.07393621,-0.00271288,-0.04770447,-0.02978563,0.02908547,0.01021391,0.02257578,-0.04117754,-0.04838591,0.03491284,-0.03929761,-0.10736667,-0.02014387,-0.02403689,-0.00772695,0.05082026,-0.00579256,0.00083801,-0.07337745,0.01788036,-0.05404146,-0.043558,-0.03322604,-0.02767211,0.05982571,-0.01964972,0.00887778,-0.01275874,0.01314689,0.02719157,0.01503348,0.01136624,-0.00558491,-0.05043351,0.05672099,0.03938292,-0.03226037,0.01613664,0.02103302,0.05119675,-0.0351454,0.06584863,0.02724134,0.01143144,-0.00873964,0.06008337,-0.01023804,0.02812298,-0.06241773,-0.18760872,-0.02494802,-0.00384305,-0.00272451,-0.03556376,-0.01968327,0.06638387,-0.03147418,0.00095923,0.11693577,0.10921867,-0.02760371,0.00295779,0.00085668,-0.00346859,-0.0025756,-0.06175227,-0.03840959,-0.04439453,0.06344163,0.01388538,0.00262091,-0.03786304,-0.05196808,0.01645794,-0.03151325,0.12402241,-0.03235007,0.01494409,0.02971384,0.1145927,-0.00924947,-0.02103677,-0.06832371,0.02462769,0.05970048,-0.03129438,0.01952897,-0.03362325,0.03505287,-0.09086215,0.03577179,0.03086407,-0.08123372,-0.04581955,0.02532585,0.01337961,-0.0386887,0.00508696,0.02870952,0.03064198,-0.03131634,0.02650154,0.0548375,0.06608006,-0.01478768,-0.05960398,0.02566704,-0.04025088,0.04065355,-0.03774879,-0.06504962,0.05529942,-0.0146011,0.03086494,0.013816,0.00060757,-0.01991237,-0.00518725,-0.01978992,0.00218737,0.11222888,0.00115711,0.03120323,-0.03024249,0.03755979,0.08430538,-0.04960665,-0.00687869,0.01365094,-0.0333604,-0.07344305,0.05449144,0.05014486,0.04837469,0.06835738,0.06152696,0.03689173,0.00582322,-0.01075435,0.03791552,-0.0350936,-0.0271852,0.0260253,0.04934307,0.03529529,-0.25746468,0.0267367,-0.00057386,0.02974595,0.03926884,-0.03359402,0.01459938,-0.05749518,0.01755594,-0.02791143,0.05810752,0.02836647,0.0091871,-0.064997,0.00568792,-0.01488745,0.04051166,-0.03028858,0.05035131,0.03774152,0.01753478,0.0340725,0.23237678,0.01295356,0.00568809,0.03062322,0.00632179,-0.0028056,0.01732084,0.04921862,0.00300034,-0.00249983,0.07680148,0.00651253,-0.06722917,0.03800358,-0.02565633,0.03007814,-0.01075863,0.03140801,-0.06868449,0.00523463,-0.03227505,-0.02673336,0.12140584,0.03434808,-0.0471664,-0.09645133,0.05552949,0.05296282,-0.0753001,-0.05545571,-0.08904324,-0.00335697,0.01129713,0.03246096,0.0283751,0.03506144,0.03069614,0.01769461,0.05214464,-0.03432195,0.0681472,0.06019453,0.03113138],"last_embed":{"hash":"1xejtn9","tokens":475}}},"last_read":{"hash":"1xejtn9","at":1753495684030},"class_name":"SmartSource","last_import":{"mtime":1753363611696,"size":28114,"at":1753495676934,"hash":"1xejtn9"},"blocks":{"#---frontmatter---":[1,6],"#Excel Spreadsheets, Lottery Software, Lotto Programming":[8,243],"#Excel Spreadsheets, Lottery Software, Lotto Programming#{1}":[10,15],"#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)":[16,24],"#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)#{1}":[17,24],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>":[25,40],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>#{1}":[27,40],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>":[41,87],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>#{1}":[43,87],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>":[88,97],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>#{1}":[90,97],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>":[98,131],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>#{1}":[100,131],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>":[132,199],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{1}":[134,194],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{2}":[195,195],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{3}":[196,197],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{4}":[198,199],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>":[200,217],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>#{1}":[202,217],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>":[218,243],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{1}":[220,221],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{2}":[222,222],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{3}":[223,223],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{4}":[224,224],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{5}":[225,225],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{6}":[226,226],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{7}":[227,227],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{8}":[228,228],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{9}":[229,229],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{10}":[230,230],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{11}":[231,231],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{12}":[232,232],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{13}":[233,233],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{14}":[234,234],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{15}":[235,235],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{16}":[236,237],"#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{17}":[238,243]},"outlinks":[{"title":"Powerful lottery strategies, systems can be derived from analyses with an Excel spreadsheet.","target":"https://saliu.com/images/lotto.gif","line":14},{"title":"The Complexity of Lottery Software Programming","target":"https://saliu.com/Newsgroups.htm#Program","line":16},{"title":"Original Pick-3 Strategies, Lottery Software","target":"https://saliu.com/Newsgroups.htm#Strategy","line":17},{"title":"More on Gambling Formula and Lottery Statistics","target":"https://saliu.com/Newsgroups.htm#Formula","line":18},{"title":"Using _Excel Spreadsheets_ to Analyze the Lottery I","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":19},{"title":"Using _Excel Spreadsheets_ to Analyze the Lotto II","target":"https://saliu.com/Newsgroups.htm#SpreadII","line":20},{"title":"Resources in Lottery Software, Excel Spreadsheets, Lotto Wheeling","target":"https://saliu.com/Newsgroups.htm#Resources","line":21},{"title":"Using Excel spreadsheets is relatively easy for simple lotto data analyses.","target":"https://saliu.com/images/lottery.gif","line":23},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-lottery-software.gif","line":35},{"title":"Download lotto software, Excel spreadsheets to analyze and create lottery systems.","target":"https://saliu.com/images/lottery-software.gif","line":39},{"title":"Lottery software spreadsheet generates strategies based on positional limits of the lotto numbers.","target":"https://saliu.com/ScreenImgs/spreadsheet-pick-lottery.gif","line":45},{"title":"Each lotto number in a lottery drawing comes from a limited group of lotto numbers.","target":"https://saliu.com/ScreenImgs/lottery-strategy-spreadsheet.gif","line":49},{"title":"Run specialized lotto software programs, better than any Excel lottery spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":86},{"title":"_**Pick-3 Lottery Method, Strategy, System, Play, Software**_","target":"https://saliu.com/STR30.htm","line":90},{"title":"_**Lotto, Lottery Software, Strategy, Systems, Wheels**_","target":"https://saliu.com/LottoWin.htm","line":92},{"title":"Ion Saliu is the first programmer to use Excel spreadsheets as lottery software.","target":"https://saliu.com/images/lottery-software.gif","line":96},{"title":"Learn about programming the lotto games, lottery software with Excel spreadsheet software.","target":"https://saliu.com/images/lotto-software.gif","line":130},{"title":"_**Lottery Strategy by Lotto Software on <u>Positional Frequency</u>**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":196},{"title":"Analysis of Excel spreadsheets applied to lottery and lotto developing of software started here.","target":"https://saliu.com/images/lottery-software.gif","line":198},{"title":"**Software Utilities for Lottery**","target":"https://saliu.com/software-lotto-lottery.html","line":205},{"title":"Work with Excel spreadsheets for lotto software to generate lottery systems, strategies.","target":"https://saliu.com/HLINE.gif","line":209},{"title":"Theory of Probability Book founded on mathematics of lotto, lottery, Excel spreadsheets.","target":"https://saliu.com/probability-book-Saliu.jpg","line":211},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":211},{"title":"LotWon is lotto software to apply Excel spreadsheets to lottery systems.","target":"https://saliu.com/HLINE.gif","line":214},{"title":"\n\n## <u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>\n\n","target":"https://saliu.com/content/lottery.html","line":216},{"title":"_**Lottery Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":222},{"title":"_**Lotto, Lottery Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":223},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":225},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":227},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":228},{"title":"_**Genuine Powerball, Mega-Millions strategy, system**_","target":"https://saliu.com/powerball-systems.html","line":229},{"title":"**Lottery Skips Systems**","target":"https://saliu.com/skip-strategy.html","line":231},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":232},{"title":"_**Lottery filters, lotto filtering in software**_","target":"https://saliu.com/filters.html","line":233},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":234},{"title":"_**Starting Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":235},{"title":"_**Best Lottery Software, Lotto Software**_","target":"https://saliu.com/infodown.html","line":236},{"title":"Thanks for visiting the site of lotto software, lottery software, Excel spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":238},{"title":"Forums","target":"https://forums.saliu.com/","line":240},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":240},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":240},{"title":"Contents","target":"https://saliu.com/content/index.html","line":240},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":240},{"title":"Home","target":"https://saliu.com/index.htm","line":240},{"title":"Search","target":"https://saliu.com/Search.htm","line":240},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":240},{"title":"Lotto players use Excel spreadsheets to analyze or program lottery software.","target":"https://saliu.com/HLINE.gif","line":242}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["spreadsheet","Excel","spreadsheets","free","ranges","limits","median","lotto","lottery","formula","software","data","analysis","programming","statistics","strategy","combinations","rows","columns"],"source":"https://saliu.com/Newsgroups.htm#Spreadsheet","author":null},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md"},"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07808792,-0.05473198,-0.0151669,-0.03438524,0.01257913,0.03518016,-0.04715032,0.00950875,0.06455513,-0.00062593,0.03683623,0.00712984,0.02900214,-0.00416332,-0.02426724,-0.02466466,0.00804683,-0.00964937,-0.05935961,-0.01306652,0.06172688,-0.00815726,-0.07131217,-0.1073091,0.06155324,0.04268234,-0.04234697,-0.09470479,-0.05765357,-0.16742499,-0.00173947,0.02432843,0.02095331,-0.02817907,-0.04000989,-0.01229143,-0.00895853,0.01448131,-0.01797262,0.0637619,0.00028904,0.02169196,-0.00724846,0.00273862,0.00990416,-0.02817809,-0.00681743,0.01647307,0.01606252,0.03526877,-0.07529421,-0.03272184,-0.01358711,0.03675082,0.07281093,0.00288141,0.02352618,0.02716335,0.02906094,0.06195387,0.024715,0.06319269,-0.19918378,0.06891437,0.01004747,0.0139967,-0.02371066,-0.02653973,0.00178933,-0.00818135,-0.00440193,0.0249555,0.0142259,0.05486147,0.07424428,-0.02611239,-0.08239473,-0.05734777,-0.05504028,-0.01922647,-0.05534325,0.00248106,-0.0030251,0.00267055,-0.0249093,0.0256497,0.07586262,0.04665364,0.09016591,-0.05820269,-0.00001866,0.03362573,0.04906658,0.03880738,-0.04912819,-0.02036081,0.04226236,0.00154588,-0.03073782,0.122721,-0.0609492,0.00272106,0.03272543,-0.0066276,0.03271477,-0.04507566,-0.01060901,-0.04750796,-0.02079022,-0.01064918,0.0209455,0.02616347,0.03514274,-0.13193899,-0.00672272,-0.05725616,-0.01186016,-0.03611615,0.04738812,0.00646584,-0.04646192,-0.03139659,0.00723974,0.00227217,0.01129789,0.05045513,0.05425886,0.08912269,-0.01441254,-0.0010944,0.06307752,0.05015275,-0.12954707,-0.01106815,0.00312644,-0.00758631,0.06328744,-0.04443748,0.03020728,-0.0211118,-0.03813327,-0.04489845,0.04858134,-0.10982051,-0.04155934,0.11489359,0.0066611,0.00024449,0.02951346,-0.04101458,-0.01332041,-0.00120744,0.01650655,-0.06566611,0.03255199,-0.00466454,0.11803307,0.06219945,-0.02198872,-0.02225625,-0.00411996,-0.0521703,-0.03525891,0.13615806,-0.04679671,-0.14122607,-0.02490965,0.05761832,-0.01259933,-0.06281673,0.02062269,-0.0119502,-0.03394153,0.03853862,0.10219833,0.02680719,-0.00481211,-0.01632533,-0.00560786,0.01331456,-0.01176677,-0.04311152,-0.06451561,0.02593316,-0.05077261,-0.08692976,-0.0378406,-0.0219118,0.02233296,0.06651029,-0.01649241,-0.04491257,-0.06681269,0.01270534,-0.0492173,-0.06212444,-0.02439166,-0.02614551,0.05566742,-0.02662679,0.03380178,-0.00823419,-0.02042045,0.02409827,0.01730298,0.00185026,-0.00450705,-0.05782926,0.06397654,0.02441751,-0.02832516,0.02238392,0.03883024,0.04893151,-0.06297105,0.0514378,-0.01197888,-0.00820254,-0.00323396,0.03305975,-0.01644285,0.0090203,-0.06747878,-0.19592586,0.01144531,0.0003384,-0.01251576,-0.06065778,0.00022599,0.05645951,-0.02072588,0.01816952,0.14326295,0.10758395,-0.04580117,-0.01498907,-0.02894558,-0.01422267,-0.02180426,-0.04351673,-0.04705188,-0.00721724,0.06849194,0.03022125,-0.00302998,-0.06095625,-0.04962309,0.04328801,-0.00672509,0.11384719,0.0129058,0.02156629,-0.00416439,0.12220428,-0.00566176,0.01287689,-0.05971262,0.01915792,0.03937573,-0.06824287,0.03524302,-0.04428225,0.00685014,-0.09114861,0.03280626,0.02755611,-0.08629118,-0.01144411,0.01436578,0.02820006,-0.04596787,-0.00601669,0.02559156,0.00532045,-0.0286382,0.03488906,0.07196456,0.08052015,-0.02526201,-0.05949441,-0.0040082,-0.04589889,0.01318497,-0.02581976,-0.05397404,0.02538156,0.00107511,0.0014239,0.02798701,0.00820653,-0.03787286,-0.01886167,-0.01560061,0.01502607,0.07741439,0.01122436,0.01277911,-0.05079462,-0.00733546,0.07678372,-0.02172551,0.00624597,0.00227031,-0.01947561,-0.04965447,0.05864802,0.03141887,0.0494403,0.07024932,0.05055769,0.032734,0.01374565,-0.00880435,0.04268624,-0.02064687,-0.01935587,0.02471011,0.08300434,0.00519251,-0.26010647,0.04232198,0.02131619,0.0292227,0.035375,-0.00411972,0.00425024,-0.05030929,-0.01016515,-0.01747075,0.06096615,0.06985211,0.01229585,-0.03478779,-0.00596535,-0.01630925,0.01750029,-0.0359042,0.03758398,0.05031322,0.03025909,0.01502235,0.23498692,0.03429744,-0.00249321,0.0287826,0.00038263,-0.01756928,0.03390943,0.03589239,-0.00878025,0.00633472,0.07113721,0.00739021,-0.03773272,0.04986726,-0.02250797,0.05842862,-0.00475165,0.03394244,-0.08885061,0.00308181,-0.04883512,-0.00564616,0.10449827,0.0410128,-0.06550778,-0.07099912,0.03366449,0.01678264,-0.06631769,-0.04626109,-0.09079567,0.00554408,-0.02578294,0.04638741,0.04394542,0.02621626,0.03577295,0.01953665,0.04400677,-0.04320138,0.05311834,0.05684794,0.0269428],"last_embed":{"hash":"1s9czc0","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"1s9czc0","at":1753495681652},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#---frontmatter---","lines":[1,6],"size":280,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06763983,-0.01731287,-0.04206869,-0.03441457,-0.00672717,0.04363304,-0.01440042,0.0024594,0.03533721,-0.00747706,0.01517618,0.0129603,0.05816326,-0.03457338,-0.03171324,-0.05423415,0.00931742,0.00401934,-0.05437964,-0.03000743,0.05024254,-0.02134735,-0.07536694,-0.11058775,0.05914782,0.01970191,-0.04913773,-0.09304636,-0.05053292,-0.21968089,0.0256369,0.01323775,0.0126427,-0.06467263,-0.04573139,0.00118835,-0.01016751,0.01470329,-0.03327717,0.03940663,0.00521791,0.0029081,0.0121175,-0.01121442,0.03006956,-0.04279792,-0.0062902,0.02590991,0.01963482,-0.00023,-0.09610389,-0.02757747,0.024112,0.01879827,0.04207807,0.01291855,0.03538931,0.07806592,0.01366046,0.03847082,0.0323964,0.0668507,-0.19268142,0.05050745,0.03438442,0.02442968,-0.01408909,-0.05060437,0.04056151,0.02546316,-0.00800596,0.02573179,0.00125815,0.04046136,0.05103659,-0.03917857,-0.05013391,-0.05017126,-0.04592114,0.01268453,-0.03720315,0.02724268,0.02156259,0.01593488,-0.00787649,0.04236989,0.03480293,0.00816001,0.10730365,-0.06858892,0.02696365,0.02862666,0.03434304,0.03285091,0.02223487,-0.02418974,0.05803136,-0.04115646,0.02411522,0.1294508,-0.01103944,-0.03427884,0.03886513,-0.00883908,0.02561322,-0.04950577,-0.02836589,-0.05757678,-0.01450736,-0.00352014,0.01692692,-0.01141577,0.01128856,-0.06301328,-0.06383871,-0.01513502,-0.03057796,-0.03352244,0.03419198,0.01653359,-0.01725427,-0.04586044,0.02581959,0.00671847,0.00473762,-0.00289819,0.03522097,0.07217455,-0.00806518,0.00952116,0.06948268,0.05458495,-0.14578083,-0.03725225,-0.01052419,-0.0048572,0.02403833,-0.01500415,0.01481164,0.02029531,-0.03921027,-0.0204786,0.07939518,-0.10045468,-0.03472258,0.10841303,0.01332341,-0.01305665,-0.01221976,-0.02677045,0.01767437,-0.0133641,-0.04299507,-0.05732422,0.02558416,-0.00076875,0.11105173,0.05567513,-0.03227119,0.00936767,-0.02764712,-0.04360503,-0.01909869,0.12253253,-0.0110191,-0.11795481,0.000532,0.03550595,-0.01303195,-0.0961308,0.01817836,-0.01706034,-0.05552644,0.02766431,0.04934324,-0.00643524,-0.05099461,-0.03165451,0.03356996,0.00441417,0.04479455,-0.04711682,-0.05404794,0.0394071,-0.0353225,-0.11300278,-0.0154114,-0.01464256,-0.02739797,0.04152232,0.00124707,0.01241294,-0.07969863,0.01347633,-0.04187209,-0.03837053,-0.02639361,-0.02963231,0.06600504,-0.01915373,0.00451817,-0.01844397,0.03004162,0.01415588,0.00990266,0.02203735,-0.02683724,-0.04205995,0.0611347,0.04028993,-0.0355505,0.01491386,0.03494423,0.0535637,-0.0271164,0.06543809,0.03213083,0.00614343,-0.00471191,0.06798676,0.00393502,0.03222025,-0.04659577,-0.191388,-0.04216624,-0.01266652,0.00760617,-0.01715533,-0.03036805,0.07277907,-0.04263594,0.00298693,0.11337724,0.10290627,-0.03087226,0.01833364,0.01695939,0.0008074,0.0036056,-0.0674678,-0.04897758,-0.06805541,0.04882422,-0.00536293,0.0022723,-0.02115345,-0.06855892,0.01772319,-0.03427084,0.13331322,-0.03404064,0.01208585,0.04350071,0.1013461,-0.01293663,-0.03238401,-0.06685654,0.02004601,0.06166455,-0.00446072,0.01077084,-0.03130189,0.0379071,-0.10538424,0.04160438,0.03207067,-0.07346551,-0.03785457,0.0350634,0.02582484,-0.03208933,0.01401039,0.03903259,0.03136415,-0.03933767,0.02726649,0.0504205,0.06189301,-0.0148723,-0.04584649,0.0406078,-0.02824525,0.03959525,-0.05378065,-0.06332448,0.05010845,-0.01256186,0.04901487,0.00641717,-0.00522431,-0.00496325,0.00197881,-0.01266396,-0.00508166,0.11337394,-0.01745133,0.02589737,-0.01052699,0.05828177,0.08150366,-0.06233123,-0.0144289,0.01302547,-0.028551,-0.08975693,0.05053515,0.05364416,0.0509277,0.05520146,0.0595343,0.03866236,0.00871681,-0.0033047,0.02556122,-0.03573296,-0.02405606,0.03685467,0.01763282,0.05171173,-0.24398951,0.02258465,-0.01911238,0.04197494,0.02639641,-0.05031076,0.02022389,-0.05480782,0.03117139,-0.0293081,0.05528335,0.01190704,0.01728877,-0.07701053,0.00173239,-0.01932684,0.05237401,-0.0311425,0.04966699,0.03378902,0.02817934,0.0436812,0.24136823,0.0123411,0.01460832,0.02575544,0.0038181,-0.00998701,-0.00643877,0.04712009,0.01712088,-0.01200104,0.06874618,-0.00250493,-0.06975268,0.01048995,-0.03345352,0.02705805,-0.00407807,0.02619212,-0.05871075,0.01057461,-0.03468067,-0.04159342,0.12578802,0.04106561,-0.04077163,-0.10679139,0.05480521,0.0568733,-0.07945658,-0.0691924,-0.07854226,-0.02056208,0.01184922,0.01913821,0.00232917,0.02887734,0.03358502,0.01405398,0.06059927,-0.02324607,0.06980102,0.06048364,0.02719969],"last_embed":{"hash":"1dz3xxo","tokens":502}}},"text":null,"length":0,"last_read":{"hash":"1dz3xxo","at":1753495681688},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming","lines":[8,243],"size":27800,"outlinks":[{"title":"Powerful lottery strategies, systems can be derived from analyses with an Excel spreadsheet.","target":"https://saliu.com/images/lotto.gif","line":7},{"title":"The Complexity of Lottery Software Programming","target":"https://saliu.com/Newsgroups.htm#Program","line":9},{"title":"Original Pick-3 Strategies, Lottery Software","target":"https://saliu.com/Newsgroups.htm#Strategy","line":10},{"title":"More on Gambling Formula and Lottery Statistics","target":"https://saliu.com/Newsgroups.htm#Formula","line":11},{"title":"Using _Excel Spreadsheets_ to Analyze the Lottery I","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":12},{"title":"Using _Excel Spreadsheets_ to Analyze the Lotto II","target":"https://saliu.com/Newsgroups.htm#SpreadII","line":13},{"title":"Resources in Lottery Software, Excel Spreadsheets, Lotto Wheeling","target":"https://saliu.com/Newsgroups.htm#Resources","line":14},{"title":"Using Excel spreadsheets is relatively easy for simple lotto data analyses.","target":"https://saliu.com/images/lottery.gif","line":16},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-lottery-software.gif","line":28},{"title":"Download lotto software, Excel spreadsheets to analyze and create lottery systems.","target":"https://saliu.com/images/lottery-software.gif","line":32},{"title":"Lottery software spreadsheet generates strategies based on positional limits of the lotto numbers.","target":"https://saliu.com/ScreenImgs/spreadsheet-pick-lottery.gif","line":38},{"title":"Each lotto number in a lottery drawing comes from a limited group of lotto numbers.","target":"https://saliu.com/ScreenImgs/lottery-strategy-spreadsheet.gif","line":42},{"title":"Run specialized lotto software programs, better than any Excel lottery spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":79},{"title":"_**Pick-3 Lottery Method, Strategy, System, Play, Software**_","target":"https://saliu.com/STR30.htm","line":83},{"title":"_**Lotto, Lottery Software, Strategy, Systems, Wheels**_","target":"https://saliu.com/LottoWin.htm","line":85},{"title":"Ion Saliu is the first programmer to use Excel spreadsheets as lottery software.","target":"https://saliu.com/images/lottery-software.gif","line":89},{"title":"Learn about programming the lotto games, lottery software with Excel spreadsheet software.","target":"https://saliu.com/images/lotto-software.gif","line":123},{"title":"_**Lottery Strategy by Lotto Software on <u>Positional Frequency</u>**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":189},{"title":"Analysis of Excel spreadsheets applied to lottery and lotto developing of software started here.","target":"https://saliu.com/images/lottery-software.gif","line":191},{"title":"**Software Utilities for Lottery**","target":"https://saliu.com/software-lotto-lottery.html","line":198},{"title":"Work with Excel spreadsheets for lotto software to generate lottery systems, strategies.","target":"https://saliu.com/HLINE.gif","line":202},{"title":"Theory of Probability Book founded on mathematics of lotto, lottery, Excel spreadsheets.","target":"https://saliu.com/probability-book-Saliu.jpg","line":204},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":204},{"title":"LotWon is lotto software to apply Excel spreadsheets to lottery systems.","target":"https://saliu.com/HLINE.gif","line":207},{"title":"\n\n## <u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>\n\n","target":"https://saliu.com/content/lottery.html","line":209},{"title":"_**Lottery Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":215},{"title":"_**Lotto, Lottery Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":216},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":218},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":220},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":221},{"title":"_**Genuine Powerball, Mega-Millions strategy, system**_","target":"https://saliu.com/powerball-systems.html","line":222},{"title":"**Lottery Skips Systems**","target":"https://saliu.com/skip-strategy.html","line":224},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":225},{"title":"_**Lottery filters, lotto filtering in software**_","target":"https://saliu.com/filters.html","line":226},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":227},{"title":"_**Starting Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":228},{"title":"_**Best Lottery Software, Lotto Software**_","target":"https://saliu.com/infodown.html","line":229},{"title":"Thanks for visiting the site of lotto software, lottery software, Excel spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":231},{"title":"Forums","target":"https://forums.saliu.com/","line":233},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":233},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":233},{"title":"Contents","target":"https://saliu.com/content/index.html","line":233},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":233},{"title":"Home","target":"https://saliu.com/index.htm","line":233},{"title":"Search","target":"https://saliu.com/Search.htm","line":233},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":233},{"title":"Lotto players use Excel spreadsheets to analyze or program lottery software.","target":"https://saliu.com/HLINE.gif","line":235}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0647025,-0.00599473,-0.02882846,-0.04009447,-0.00765124,0.02856951,0.01114667,0.00930418,0.02502646,-0.01559805,0.02091813,0.00264897,0.05162047,-0.03506132,-0.04648909,-0.04136002,0.00672897,0.03329878,-0.04407803,-0.03320251,0.04968042,-0.01925099,-0.07668862,-0.12042029,0.06425459,0.03206962,-0.05447431,-0.08183965,-0.04256711,-0.1593736,0.0154895,0.01400344,-0.00098683,-0.05165307,-0.03502698,0.00046794,0.00551439,0.0123176,-0.02702935,0.04310015,0.0184839,-0.0040865,-0.0110823,-0.00533765,0.01799763,-0.04392777,-0.00314227,0.02212388,0.02757613,-0.00152499,-0.08136091,-0.01356435,0.01396926,0.01074855,0.03333746,0.02111419,0.02907529,0.06695146,0.01158008,0.04070348,0.03584064,0.06193725,-0.19252032,0.04372709,0.0250388,0.05783831,0.00064292,-0.03548292,0.04237047,0.02135176,-0.02466582,0.02026911,-0.01788832,0.05227935,0.05253157,-0.02499004,-0.05388191,-0.05672545,-0.04633512,0.03250439,-0.0361225,0.00871383,0.02228367,0.02047995,-0.01139547,0.04251669,0.05517393,0.00631267,0.09890229,-0.04739356,0.01314346,0.00832098,0.03269177,0.02809242,0.01760946,-0.02471558,0.05839834,-0.06185788,0.0524375,0.16680531,-0.03805906,-0.04732722,0.05050673,-0.02586866,0.0147638,-0.04602145,-0.02705954,-0.04395476,-0.00303199,-0.0012171,0.01110025,-0.00532135,-0.01687386,-0.07185235,-0.05544265,-0.03595927,-0.0445707,-0.02990698,0.01790434,0.01372401,-0.03234698,-0.02957301,0.03534276,0.00277327,0.0117244,-0.03391771,0.04227919,0.07996693,-0.01790739,0.00863292,0.07159261,0.05308174,-0.13385285,-0.03434182,-0.00843797,-0.00948846,0.04838562,-0.0236131,0.01390926,0.02494925,-0.03137689,-0.00219338,0.09155989,-0.09809011,-0.01757977,0.10542162,0.00968616,0.0007292,-0.01751808,-0.03181422,0.01138168,-0.02660858,-0.02871531,-0.03884111,0.01105387,0.01954424,0.10239627,0.03914206,-0.01512213,-0.0044092,-0.0517414,-0.02887949,-0.03815795,0.13499179,-0.02307685,-0.10231892,-0.00910694,0.02733874,-0.00768703,-0.09118634,0.01906722,-0.02684591,-0.06157577,0.01984058,0.05592385,0.00456967,-0.03810291,-0.01760988,0.01376882,0.00056541,0.05953712,-0.04571533,-0.04772773,0.04701106,-0.03390691,-0.11790221,-0.03148488,-0.01327279,-0.02376153,0.05682012,0.00899989,0.03850841,-0.0718724,-0.00480806,-0.04595631,-0.03906621,-0.04331302,-0.02545837,0.05340509,-0.03341801,0.00591112,-0.01559149,0.01451785,0.01581867,0.000135,0.01147052,-0.03058053,-0.02332046,0.06415705,0.03671386,-0.03644538,0.00582907,0.06293122,0.04319366,-0.04344085,0.05748342,0.02312079,-0.00436323,-0.00859276,0.06209597,-0.00668538,0.01288657,-0.02774001,-0.20539349,-0.03325354,0.000205,-0.01545998,-0.01629567,-0.04136027,0.0644329,-0.02979506,-0.01847278,0.11451637,0.11573508,-0.05088256,0.02840007,0.01629107,-0.00557046,-0.00837017,-0.07180356,-0.0517918,-0.04431109,0.03675571,-0.00073353,-0.01967225,-0.01661212,-0.07555979,0.00685164,-0.0157576,0.12807734,-0.03205056,0.0358602,0.03552352,0.11105441,-0.03017848,-0.02858438,-0.05914264,0.0017574,0.047528,-0.03236201,0.01037996,-0.02440159,0.03031189,-0.09418335,0.04136493,0.0358218,-0.06753094,-0.02923428,0.02500733,0.02694125,-0.05203898,0.03653783,0.0469232,0.01754407,-0.02485821,0.0252459,0.05086247,0.06218338,-0.00539601,-0.0602205,0.03951971,-0.03196098,0.02803527,-0.05451485,-0.06835172,0.02464693,-0.01652226,0.05991769,0.00994793,-0.00374344,-0.03250144,0.01153275,-0.01865533,0.00786257,0.10821328,-0.03224353,0.01984176,-0.0150129,0.0685027,0.09347557,-0.05623056,-0.03171808,0.01440759,-0.02697863,-0.10427671,0.04848899,0.03841961,0.05574774,0.05727411,0.05216095,0.03743071,-0.00495173,-0.01416808,0.0168588,-0.02390414,-0.00655668,0.04267834,0.00633825,0.04651398,-0.24841844,0.02847238,-0.00695788,0.04641877,0.00825167,-0.05893167,0.0178904,-0.06248472,0.01479269,-0.00452274,0.06938367,-0.00059917,0.00413029,-0.06807803,0.01607406,-0.0155709,0.10080811,-0.03069814,0.07757185,0.0324747,0.0333019,0.03756865,0.24776676,0.01703151,0.01779766,0.03244061,0.00694198,-0.02132059,-0.01056997,0.04709047,0.04730726,-0.00277791,0.06862521,-0.01771948,-0.05745755,0.03070159,-0.02309184,0.04974572,0.00972553,0.03341191,-0.06106497,-0.0004261,-0.06429099,-0.03084078,0.1150641,0.03551527,-0.03293113,-0.10366113,0.04865353,0.04299121,-0.07537887,-0.05656928,-0.0728123,-0.03187449,0.01400231,0.00360192,0.00204597,0.02546892,0.04971052,0.03275119,0.08357875,-0.02423512,0.05175396,0.05213355,0.0205673],"last_embed":{"hash":"55n7dj","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"55n7dj","at":1753495681872},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#{1}","lines":[10,15],"size":312,"outlinks":[{"title":"Powerful lottery strategies, systems can be derived from analyses with an Excel spreadsheet.","target":"https://saliu.com/images/lotto.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06413938,-0.01964757,-0.03323143,-0.02221325,-0.00100879,0.06370337,-0.00262845,0.00412807,0.03950502,-0.01074148,0.00185379,0.01266438,0.04265647,-0.02250368,-0.01407467,-0.05129234,0.0111933,-0.01537197,-0.06940909,-0.01236083,0.04681952,-0.00650742,-0.06413671,-0.0829326,0.0498586,0.00685575,-0.03795879,-0.09778008,-0.06247239,-0.22909743,0.0182317,0.02167051,0.01452674,-0.0712277,-0.03743833,-0.0172643,-0.01779625,0.00924859,-0.05310723,0.05979984,0.00847671,0.00990286,0.01507289,-0.02256596,0.01933408,-0.06546614,-0.01281361,0.02268668,0.04708496,-0.00620924,-0.10200132,-0.02786492,0.01273867,0.02250483,0.05807923,0.00433787,0.04645249,0.07616874,-0.00152509,0.02580102,0.02556049,0.07009682,-0.19177364,0.06940995,0.02276681,0.00056749,-0.01770583,-0.06175604,0.00710591,0.04332397,0.00874913,0.02206818,0.01429852,0.03199841,0.05796859,-0.0494048,-0.05755803,-0.05001541,-0.05673004,0.01151437,-0.07586944,0.0374202,-0.00859242,0.00418675,-0.00613886,0.03995281,0.02710088,0.00402953,0.09904502,-0.07768725,0.02113691,0.04915354,0.0188606,0.04349368,0.00876086,-0.03461552,0.07171994,-0.03865458,-0.00359909,0.13525781,-0.00066894,-0.01250186,0.03068698,0.02515769,0.03329882,-0.04335748,-0.03689447,-0.05029598,-0.03254417,0.0015885,0.03736513,-0.00317385,0.03536229,-0.07301577,-0.07397439,-0.00741693,-0.02057642,-0.04289448,0.03448668,0.01839809,-0.00951749,-0.0589329,0.01371303,0.01743062,0.00721844,0.0084668,0.03537043,0.05697289,0.01157205,0.02581589,0.06947198,0.0528109,-0.15317275,-0.04739727,-0.02430736,0.0112243,0.02038825,-0.02015623,-0.01134471,0.0036275,-0.04199517,-0.04525312,0.07327098,-0.10778888,-0.03817457,0.11619771,-0.01627033,-0.00827524,0.01695452,-0.02311898,-0.01234427,-0.00192188,-0.03634771,-0.07471988,0.02406222,-0.04191144,0.11480331,0.05129062,-0.04379476,0.01803886,-0.00415853,-0.06237611,0.00412697,0.14003783,-0.02847981,-0.09821898,-0.00788936,0.03792941,-0.01213065,-0.08401161,0.02096536,0.01084852,-0.0523006,0.04913744,0.065125,-0.00319523,-0.03532064,-0.04084047,0.04290519,0.01085015,0.02473705,-0.06369308,-0.03383206,0.04143624,-0.01742691,-0.08967254,0.00620525,-0.03786557,-0.00339707,0.04217908,-0.01244261,0.00050645,-0.07480042,0.01890352,-0.05349683,-0.01771804,-0.02743715,-0.03646063,0.063662,-0.01049954,0.01386498,-0.01062645,0.03650018,0.04150697,0.02397016,0.02403743,-0.01014024,-0.06682304,0.05650416,0.03196096,-0.04444126,0.03030166,0.0045274,0.06184607,-0.01504827,0.06078245,0.0552767,0.01808622,-0.00707869,0.0646797,0.02985873,0.03532972,-0.05260298,-0.19180682,-0.05244185,-0.02364576,0.01489994,-0.01978358,-0.02570095,0.06322718,-0.02781996,0.01255276,0.11732892,0.09312554,-0.03381626,0.0106413,0.01100135,0.01339023,0.00999024,-0.05717254,-0.04529076,-0.04330169,0.06252883,-0.0162997,0.01388086,-0.04892934,-0.03745675,0.03161398,-0.04975378,0.12538946,-0.02713146,0.01243888,0.01662042,0.09003519,0.01019126,-0.04718295,-0.06583545,0.03780728,0.06771275,-0.00950815,0.01868045,-0.04169313,0.0237087,-0.07473901,0.05536615,0.01193033,-0.05866347,-0.02340777,0.03906563,0.02261604,0.00185971,0.00848489,0.0291671,0.0414824,-0.03537889,0.03183531,0.06680991,0.06771187,-0.01669254,-0.04195501,0.01971375,-0.03776249,0.03289749,-0.05107319,-0.04702733,0.08012591,-0.0115776,0.03314579,0.01595214,-0.01882406,-0.02292759,0.00786384,-0.00726788,-0.00246448,0.10097896,-0.00376533,0.02565856,-0.03841249,0.03816318,0.06751774,-0.0645508,0.00461247,0.00367201,-0.01799573,-0.06909172,0.04116067,0.05233013,0.04375512,0.06664803,0.06384793,0.0341393,0.00511329,0.00598375,0.04038672,-0.03879993,-0.03912411,0.03768932,0.0300194,0.02267138,-0.25124827,0.02016122,-0.02216948,0.03389366,0.04220127,-0.03130876,0.01158368,-0.04199866,0.04218388,-0.03225427,0.07010914,0.01428743,0.01122786,-0.08894383,-0.02631402,-0.00467605,0.02207919,-0.03824674,0.03032822,0.05514584,0.02259665,0.05136587,0.22755557,0.02008361,0.01096658,0.02504507,0.02343719,-0.00360771,-0.00466906,0.04116948,0.00819036,-0.03654218,0.04979711,0.00193578,-0.05118627,0.01330518,-0.02815369,0.00223581,0.00606736,0.03200759,-0.06175758,0.0007691,-0.0455539,-0.04026382,0.11337235,0.03632517,-0.05029476,-0.07138949,0.04256103,0.05196754,-0.0754251,-0.09176311,-0.07834977,-0.00565504,0.00568376,0.02716365,0.00906784,0.01807027,0.01760168,-0.00010692,0.04283294,-0.02710873,0.07176659,0.05650421,0.03743397],"last_embed":{"hash":"cgxyy9","tokens":296}}},"text":null,"length":0,"last_read":{"hash":"cgxyy9","at":1753495681907},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)","lines":[16,24],"size":747,"outlinks":[{"title":"The Complexity of Lottery Software Programming","target":"https://saliu.com/Newsgroups.htm#Program","line":1},{"title":"Original Pick-3 Strategies, Lottery Software","target":"https://saliu.com/Newsgroups.htm#Strategy","line":2},{"title":"More on Gambling Formula and Lottery Statistics","target":"https://saliu.com/Newsgroups.htm#Formula","line":3},{"title":"Using _Excel Spreadsheets_ to Analyze the Lottery I","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":4},{"title":"Using _Excel Spreadsheets_ to Analyze the Lotto II","target":"https://saliu.com/Newsgroups.htm#SpreadII","line":5},{"title":"Resources in Lottery Software, Excel Spreadsheets, Lotto Wheeling","target":"https://saliu.com/Newsgroups.htm#Resources","line":6},{"title":"Using Excel spreadsheets is relatively easy for simple lotto data analyses.","target":"https://saliu.com/images/lottery.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05969183,-0.01960471,-0.03259241,-0.01872768,0.00751881,0.06512266,0.00694715,0.00725459,0.03304768,-0.01391577,0.00952912,0.01229439,0.0336315,-0.02257704,-0.01583606,-0.04895088,0.00797693,-0.01358338,-0.06108269,-0.01652711,0.03620961,-0.01201175,-0.06157517,-0.08167975,0.05789357,0.00822663,-0.05000601,-0.08954329,-0.06887628,-0.22154261,0.01375763,0.02216664,0.00360494,-0.07268049,-0.04902914,-0.00995953,-0.01924814,0.01838177,-0.04576003,0.05596466,0.0060756,0.00079522,0.01532228,-0.01984695,0.02213879,-0.06405695,-0.01047669,0.02551882,0.05044407,-0.00287354,-0.09635824,-0.02077462,0.00978073,0.01514199,0.06530514,0.00462148,0.04942449,0.07546494,0.00298211,0.03441458,0.01263948,0.07812168,-0.19811332,0.06482608,0.01453779,0.00874143,-0.02274404,-0.05091568,0.00538866,0.04654546,0.00053269,0.02564422,0.01397494,0.03957478,0.06312626,-0.04040095,-0.05487854,-0.05013474,-0.056229,0.00666278,-0.06838392,0.04274937,-0.00480285,0.00670772,0.00210454,0.04049449,0.02715001,0.00682005,0.10074809,-0.07853921,0.0326536,0.05428088,0.01209261,0.04159109,0.00242255,-0.03701162,0.07296769,-0.03637588,0.00344567,0.13048717,-0.00322265,-0.01100434,0.03255978,0.02315217,0.03165092,-0.05259913,-0.04290648,-0.04772175,-0.02791627,0.00692486,0.04287479,-0.00016222,0.02844209,-0.07268962,-0.06688337,-0.00880816,-0.02288749,-0.04355898,0.02957078,0.01757615,-0.02056802,-0.06526954,0.02647272,0.01633975,0.00773219,0.00731827,0.04236996,0.06076486,0.01412654,0.02905968,0.06864696,0.04875997,-0.1586701,-0.0444524,-0.0295031,0.00902994,0.02944982,-0.02443695,-0.00796435,0.00619126,-0.04983105,-0.04837668,0.06804501,-0.10932345,-0.03643424,0.12125997,-0.01851265,-0.01313194,0.01603405,-0.02307509,-0.01217144,0.0046698,-0.03317725,-0.06843726,0.02631048,-0.03639585,0.11040998,0.04889469,-0.04264441,0.02161579,-0.01077878,-0.06394661,-0.00231672,0.13022178,-0.02841358,-0.09532873,-0.00797935,0.04234665,-0.01690207,-0.0889226,0.02077609,0.01053657,-0.06007139,0.04169741,0.05942278,0.00165024,-0.03293502,-0.03967792,0.04227481,0.01718542,0.02506374,-0.06538814,-0.04466585,0.04241375,-0.03124361,-0.09226366,0.00432742,-0.03763842,-0.01087332,0.04928626,-0.0259335,0.00096148,-0.07544757,0.01310241,-0.0573633,-0.01866282,-0.03328918,-0.03123042,0.06604674,-0.00695207,0.01966573,-0.01280965,0.03533485,0.03673245,0.01333885,0.025396,-0.0172957,-0.06776068,0.05722285,0.01987241,-0.04274829,0.03044499,0.00829209,0.06047338,-0.01883218,0.05494847,0.05487852,0.01600662,-0.01334976,0.05435099,0.03528988,0.03375645,-0.04928874,-0.19355804,-0.05287032,-0.02623192,0.01814773,-0.01971223,-0.02752418,0.06835733,-0.0314227,0.01750355,0.12362595,0.08896558,-0.03720841,0.01267778,0.01517165,0.01288851,0.00964402,-0.05965726,-0.04984278,-0.04770238,0.06597182,-0.01646038,0.00177932,-0.05517332,-0.04250452,0.04131332,-0.04240895,0.11985966,-0.01895763,0.0140503,0.01401754,0.09327096,0.00761332,-0.03987158,-0.0617004,0.03914104,0.06679471,-0.01460174,0.01751905,-0.049483,0.01379613,-0.07759623,0.05604346,0.01970338,-0.06164755,-0.02311169,0.03492215,0.03204859,0.00128253,0.01511808,0.02845966,0.03293397,-0.02755971,0.03273022,0.06222355,0.06554074,-0.01450247,-0.03843754,0.02782368,-0.04520378,0.02709695,-0.04815734,-0.04790574,0.07034738,-0.00290318,0.03677421,0.0114671,-0.01518603,-0.020116,0.0097874,-0.00013922,0.00051826,0.09327839,-0.00167859,0.0343268,-0.03925372,0.03821761,0.06633606,-0.08025172,0.00263296,0.00119449,-0.01552876,-0.07258173,0.03298021,0.05563578,0.04921405,0.06333306,0.06196531,0.03103014,0.01315876,-0.00060921,0.03832631,-0.03009577,-0.03926175,0.03821847,0.02924261,0.02432956,-0.24663447,0.016608,-0.02171684,0.03505556,0.03926264,-0.04068803,0.01601085,-0.04672948,0.0465392,-0.03827883,0.07004745,0.01718817,0.01277506,-0.08030517,-0.02355038,-0.00470209,0.02437107,-0.03434123,0.03450401,0.05665584,0.02533372,0.04586103,0.23419718,0.02544693,0.00671261,0.02496259,0.01850963,-0.01455968,-0.01336772,0.04060049,0.00930845,-0.03460463,0.05131282,-0.0062054,-0.04797414,0.01546972,-0.03122278,0.01042101,0.00233782,0.03523343,-0.05750628,0.00588415,-0.05841634,-0.04025298,0.11647574,0.03972853,-0.03932478,-0.07137044,0.04324457,0.05304259,-0.07010302,-0.09045927,-0.06939552,-0.00947057,0.00488962,0.03135236,0.00597994,0.01962091,0.02537457,0.0080384,0.04013601,-0.03036006,0.07112132,0.05771087,0.04090543],"last_embed":{"hash":"2kj3fn","tokens":267}}},"text":null,"length":0,"last_read":{"hash":"2kj3fn","at":1753495682012},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming##One: [The Complexity of Lottery Software Programming](https://saliu.com/Newsgroups.htm#Program)#{1}","lines":[17,24],"size":645,"outlinks":[{"title":"Original Pick-3 Strategies, Lottery Software","target":"https://saliu.com/Newsgroups.htm#Strategy","line":1},{"title":"More on Gambling Formula and Lottery Statistics","target":"https://saliu.com/Newsgroups.htm#Formula","line":2},{"title":"Using _Excel Spreadsheets_ to Analyze the Lottery I","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":3},{"title":"Using _Excel Spreadsheets_ to Analyze the Lotto II","target":"https://saliu.com/Newsgroups.htm#SpreadII","line":4},{"title":"Resources in Lottery Software, Excel Spreadsheets, Lotto Wheeling","target":"https://saliu.com/Newsgroups.htm#Resources","line":5},{"title":"Using Excel spreadsheets is relatively easy for simple lotto data analyses.","target":"https://saliu.com/images/lottery.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0682048,-0.01196447,-0.04056123,-0.04272,-0.03289256,0.0483643,-0.01730157,-0.01087395,0.05932894,0.00183864,0.01853331,0.04333339,0.06880451,-0.03604837,-0.04739663,-0.02185405,0.00566656,-0.02029173,-0.06894637,-0.01454678,0.05922278,-0.0261811,-0.08716482,-0.10315666,0.04774845,0.01087915,-0.03746474,-0.09309784,-0.07893611,-0.23430072,0.0463747,0.01676319,0.00616529,-0.06387486,-0.03252271,-0.0132972,0.00610396,0.02212143,-0.04250983,0.03616607,0.01458363,0.01046446,0.02745073,-0.01008791,0.00686399,-0.04093075,0.00087825,0.01166508,0.02685375,0.02622816,-0.07214046,-0.00980581,0.05984463,0.03201468,0.01013729,0.00257789,0.01664971,0.09654544,0.03863461,0.03729201,0.02359815,0.03717176,-0.17358008,0.05777566,-0.00234371,0.01582494,0.00037723,-0.0526431,0.00689472,0.0347346,0.00400859,0.04917929,-0.01118695,0.05030677,0.03724049,-0.07182044,-0.05934465,-0.03838825,-0.03902237,0.03246329,-0.04957533,0.01035158,0.0202787,-0.00524476,-0.04895452,0.0186418,0.02254824,0.02616607,0.10646442,-0.04162754,0.01212847,0.03868818,0.02789818,0.04631323,0.02288895,-0.03215776,0.06539313,-0.01663443,-0.00866299,0.11393107,-0.00561249,-0.0083879,-0.01381816,-0.02312798,0.01566944,-0.0647634,-0.02741838,-0.03578423,-0.04925634,0.01114673,0.0349972,-0.0072458,0.0523763,-0.0444782,-0.0722948,0.02117869,-0.0350673,-0.04645287,0.04074877,0.01953092,-0.0317805,-0.04233807,0.01881113,-0.00549599,-0.00969448,0.00928969,0.04908557,0.06815096,-0.00729527,0.01783813,0.06816847,0.02228195,-0.15450923,-0.00737695,0.01009796,0.00886187,0.0241468,-0.01119818,0.02026375,-0.01036124,-0.03762692,-0.01107191,0.06661962,-0.09371796,-0.03229454,0.10617781,0.02072482,-0.0220284,-0.00221829,-0.06916775,0.02009829,-0.02248609,-0.05175581,-0.06673933,0.01911031,0.00133429,0.102428,0.06058501,-0.03369875,-0.02959133,-0.04499504,-0.02296689,0.00783794,0.11591383,-0.00257568,-0.1008904,-0.02407823,0.0185685,-0.03901982,-0.07424998,0.0083445,0.00020382,-0.07332439,0.04142679,0.04926088,-0.00358498,-0.10038839,-0.03639423,0.0039055,-0.01401233,0.04744626,-0.03142721,-0.02640043,0.0156679,-0.02499258,-0.1011764,-0.01978883,-0.00062906,-0.01365551,0.0402001,0.01297744,-0.01843819,-0.06848379,-0.00767415,-0.00134782,-0.05693231,-0.01148439,-0.02452996,0.06106668,-0.05579612,0.00195705,-0.03091014,0.02592196,0.04033025,0.02458378,0.01988277,-0.02679428,-0.05181312,0.11608768,0.05469943,-0.04224465,-0.01544172,0.04013744,0.0660941,-0.05213413,0.04380842,0.01346876,0.00999871,0.01650491,0.0302797,-0.00716713,0.04709384,-0.04205548,-0.19094436,-0.02487593,-0.03147213,0.01383739,0.01523932,-0.02904484,0.02705041,-0.02181447,0.01621453,0.12438456,0.11433295,-0.04592529,0.00864968,0.01716383,-0.01891534,0.00952255,-0.0595783,-0.04384683,-0.05286,0.03245934,0.00250892,0.01813028,-0.05679809,-0.05116694,0.02603297,-0.046747,0.12825382,-0.04531514,0.01011468,0.02282015,0.10058437,0.00280219,-0.00828928,-0.04696706,-0.00259426,0.08177248,0.02078336,0.0136511,-0.02233206,0.03397702,-0.12205514,0.03204066,0.01515007,-0.07967792,-0.05135522,0.04124042,0.00463467,0.02377331,-0.00068517,0.04794205,0.02706044,-0.04798024,0.02700312,0.04603562,0.08431862,-0.03386983,-0.02621876,0.01666585,0.00895322,0.04613642,-0.03930587,-0.06989237,0.01794298,-0.01526686,0.02212498,0.00432124,0.01141661,-0.02225973,0.02767386,-0.02905306,-0.00113577,0.08648738,-0.03075294,0.01855002,-0.01523391,0.05343304,0.06652834,-0.0311505,-0.01181012,-0.00455704,0.01585792,-0.05105451,0.05122573,0.06063586,0.04175212,0.04933396,0.0773863,0.06856892,0.0074443,0.01694414,0.02041748,-0.03737171,-0.04608514,0.0255889,0.02274759,0.02966285,-0.2273145,0.03290839,-0.04101203,0.06405411,0.01590992,-0.04669347,0.02628348,0.00240544,0.02902725,-0.00056125,0.03174471,0.03738453,0.01023456,-0.07497935,0.00412993,-0.04371078,0.01256401,-0.00640431,0.06020611,0.05012496,0.05791021,0.04179741,0.23986477,-0.00280142,0.03372563,0.01491735,0.01982753,0.00140229,0.00268267,0.03296302,0.0252567,-0.01302173,0.08178764,-0.02956676,-0.0520692,-0.01208949,-0.03469446,0.04933358,0.0127707,0.00533787,-0.0442102,0.0171585,-0.01420892,-0.05843665,0.1271065,0.04816501,-0.03820314,-0.09259685,0.05407111,0.08058853,-0.09516803,-0.05294682,-0.09524438,-0.04531563,0.00739216,0.03588667,-0.01394857,0.0113983,0.0095296,0.01841993,0.05167975,-0.02377778,0.07253663,0.04258066,0.00676028],"last_embed":{"hash":"5gyyap","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"5gyyap","at":1753495682104},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>","lines":[25,40],"size":2878,"outlinks":[{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-lottery-software.gif","line":11},{"title":"Download lotto software, Excel spreadsheets to analyze and create lottery systems.","target":"https://saliu.com/images/lottery-software.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06892837,-0.01005534,-0.0412193,-0.04125304,-0.03283246,0.05341367,-0.01789769,-0.01098336,0.06039801,-0.00004108,0.01755837,0.04439136,0.06933661,-0.03822426,-0.04912686,-0.02051667,0.00494049,-0.02143588,-0.06745671,-0.01493182,0.05963729,-0.02624297,-0.08562246,-0.09884657,0.05247946,0.00910451,-0.03569796,-0.09109241,-0.07919589,-0.23808792,0.04479154,0.01627889,0.00878597,-0.06159969,-0.03105857,-0.00863055,0.00206442,0.02387574,-0.03991914,0.03418839,0.01638393,0.01225957,0.02954132,-0.01063007,0.00644834,-0.04239328,0.00224132,0.01122065,0.02741989,0.02221571,-0.06945359,-0.00561619,0.05907243,0.02815533,0.00995392,0.00086728,0.02044542,0.10108925,0.03789223,0.03911211,0.02337363,0.03344163,-0.17660397,0.05841691,0.00056504,0.01717843,0.00097947,-0.05610368,0.00868248,0.03795997,0.00479289,0.04643472,-0.01192968,0.04832019,0.03691317,-0.07171531,-0.06095166,-0.0376224,-0.03736815,0.03196974,-0.05076198,0.00941752,0.02419843,-0.00426729,-0.04891577,0.01570888,0.0199665,0.02413632,0.10630265,-0.04184555,0.00837611,0.04029958,0.02621953,0.04493212,0.02501082,-0.03598619,0.0657184,-0.01619719,-0.00752046,0.11490042,-0.00324268,-0.0080871,-0.01378299,-0.0233771,0.02030798,-0.06341664,-0.02706736,-0.03747314,-0.04964457,0.01247761,0.03357707,-0.00769748,0.05458504,-0.04496653,-0.07258181,0.0235813,-0.03491532,-0.0454848,0.03999515,0.0202603,-0.02979812,-0.04358515,0.01843675,-0.00890859,-0.00907834,0.0102348,0.04891658,0.0678445,-0.00537671,0.01700809,0.06794816,0.02177624,-0.15398636,-0.00797158,0.01280078,0.00600386,0.02276509,-0.01302837,0.01979386,-0.00766079,-0.0350931,-0.00831105,0.06865265,-0.09608554,-0.03216796,0.10669697,0.02178783,-0.02263426,-0.00068917,-0.06944162,0.01805077,-0.02047483,-0.05274675,-0.06808328,0.02042621,0.00121274,0.10064825,0.05980042,-0.03269844,-0.02517199,-0.04552648,-0.02241866,0.00573647,0.117489,-0.00403475,-0.09962485,-0.02216779,0.01707706,-0.03900117,-0.07281632,0.00410159,0.00108858,-0.07344618,0.04155723,0.0511522,-0.00788961,-0.10470542,-0.04015645,0.00368024,-0.01365294,0.04745713,-0.03218019,-0.02474165,0.0170094,-0.02588343,-0.09864674,-0.01748997,0.00151278,-0.01412192,0.04053311,0.00967444,-0.01853637,-0.06794649,-0.0084901,-0.00268723,-0.05556965,-0.01175001,-0.02665235,0.06165341,-0.05610949,-0.00261212,-0.02930694,0.02784188,0.04026105,0.02512925,0.01895782,-0.02319994,-0.05207459,0.11729286,0.05163066,-0.04500969,-0.01414284,0.0370793,0.06219393,-0.05374964,0.03822086,0.01431666,0.01303687,0.01469312,0.03077865,-0.00553782,0.04610006,-0.04258907,-0.19013633,-0.02697282,-0.0328534,0.01628644,0.01385539,-0.02972858,0.02728955,-0.02273417,0.01432973,0.1223575,0.11090025,-0.04487292,0.0078445,0.01736183,-0.02122527,0.01070877,-0.06238476,-0.03810426,-0.0544329,0.03250513,0.00013737,0.02008154,-0.05750079,-0.05270093,0.02077341,-0.04490617,0.12923165,-0.05092033,0.01101606,0.02680882,0.09831496,0.00261201,-0.00942879,-0.04791432,-0.0026085,0.07873342,0.01792923,0.01106185,-0.02110646,0.03297517,-0.11922766,0.03251787,0.01360446,-0.07984991,-0.05558156,0.04262723,0.00453773,0.02382991,-0.00174754,0.04485433,0.03303034,-0.05019667,0.02613227,0.04460594,0.08294065,-0.03333021,-0.02390557,0.01950303,0.00835906,0.04829294,-0.03635352,-0.06912299,0.01989919,-0.01421384,0.02638878,0.00129812,0.01133982,-0.01811178,0.02878371,-0.03012504,-0.00221771,0.08876127,-0.02855437,0.017085,-0.01080564,0.0536048,0.06286308,-0.03395788,-0.01371465,-0.00523157,0.01610353,-0.05266511,0.05411073,0.06180836,0.04258149,0.04796688,0.07561244,0.06420399,0.00659461,0.01446421,0.02083774,-0.03709358,-0.04858858,0.02368133,0.02517303,0.02863701,-0.22616789,0.03304661,-0.04257037,0.0628226,0.01780016,-0.04601927,0.02447169,0.00588587,0.02628391,-0.00441448,0.03124208,0.03675677,0.01012874,-0.07523553,0.00432975,-0.04759208,0.01463078,-0.00959225,0.05695736,0.050262,0.05902719,0.04221416,0.23983379,-0.00179408,0.03714916,0.01402673,0.02155351,0.00149734,0.00058557,0.03301182,0.0280174,-0.01202423,0.08255329,-0.02907674,-0.05424331,-0.01095288,-0.03295001,0.05068709,0.01206836,0.00369291,-0.04427693,0.02061518,-0.0108655,-0.06050732,0.12706427,0.05120155,-0.03854654,-0.09260626,0.05407734,0.08084842,-0.09631947,-0.05246555,-0.09141396,-0.04305127,0.01169859,0.03497506,-0.01131046,0.01070881,0.00962599,0.01698697,0.05340107,-0.01933121,0.07108901,0.04226247,0.00697411],"last_embed":{"hash":"kjm2xj","tokens":453}}},"text":null,"length":0,"last_read":{"hash":"kjm2xj","at":1753495682239},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>1. The Complexity of Lottery Software Programming</u>#{1}","lines":[27,40],"size":2817,"outlinks":[{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-lottery-software.gif","line":9},{"title":"Download lotto software, Excel spreadsheets to analyze and create lottery systems.","target":"https://saliu.com/images/lottery-software.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08193211,-0.00182089,-0.03640727,-0.04532835,-0.05870103,0.06823722,0.02406746,0.00605401,0.09968977,0.00339421,0.05185641,0.02072064,0.05254431,-0.03873644,-0.05618067,-0.02531526,-0.00914555,-0.05897593,-0.06549476,-0.00941761,0.0626419,-0.05150926,-0.04686688,-0.10351906,0.08414915,0.01097626,-0.02670233,-0.06564578,-0.06113911,-0.24890113,0.00215588,0.01744197,0.00965492,-0.05439897,-0.07073671,-0.00866527,-0.03162247,0.0536999,-0.03445271,0.05866196,0.00286405,-0.00125133,0.01007711,-0.02311429,-0.01010537,-0.03770654,-0.0460454,-0.01077298,0.05872326,-0.01257325,-0.06665062,0.00142524,0.03123122,0.02302934,0.04232141,0.01749345,0.04085698,0.08363891,0.00660179,0.05136116,0.03295609,0.03637174,-0.1880561,0.04402985,-0.02411381,0.03721477,-0.01778,-0.05088007,-0.00003863,0.05202323,0.03105189,0.02447396,-0.03456293,0.05491026,0.0739001,-0.05636369,-0.04864564,-0.02899372,-0.04922675,0.03678604,-0.07374223,0.00261879,0.01158261,0.0051232,0.00969343,0.03748379,0.03773852,0.0528422,0.11161732,-0.05445022,0.04466128,0.04750288,-0.00717902,0.03023617,0.01234451,0.01830496,0.08162365,-0.01749613,-0.03368684,0.10536235,-0.02310893,0.00768697,-0.01098351,0.0325436,0.03938991,-0.0535517,-0.02749321,-0.02586433,-0.04377156,0.0078031,0.02350182,-0.00737999,0.07237756,-0.07254923,-0.02950297,0.01067062,0.00082644,-0.04069302,0.04665948,0.00251035,-0.02470303,-0.00394328,-0.00059052,-0.02576926,-0.00635927,0.02466793,0.06394691,0.08118161,-0.01489945,0.02310586,0.03838228,0.01433579,-0.15757038,-0.04739033,-0.02697587,-0.00780429,-0.0003554,-0.00434623,0.00744828,0.00793496,-0.01208496,-0.01662839,0.06275135,-0.10043824,-0.04111618,0.10705987,0.01085066,0.01353833,0.01132164,-0.03704404,0.02029453,0.00624369,-0.02891851,-0.06273881,-0.00736617,-0.00059274,0.05860204,0.06465439,-0.03470182,-0.01085016,-0.03178962,-0.05254102,-0.0212112,0.08364183,-0.00850317,-0.10621835,-0.01666139,0.008535,-0.04051607,-0.07306901,-0.00902802,-0.00174788,-0.04914825,-0.00791916,0.07001969,0.02843564,-0.06017728,-0.02924593,0.04511884,0.0062055,0.02960841,-0.02549689,-0.0248047,0.03750873,-0.05253614,-0.08094832,0.01416087,-0.03529894,0.01774467,0.04421701,-0.02938161,-0.00484602,-0.0648941,0.04190092,-0.04197092,-0.02759591,-0.02526599,-0.00025626,0.06464301,-0.03707161,0.02015053,-0.03554946,0.01519829,0.00632915,-0.02931959,0.03941129,0.01536269,-0.06893636,0.09715796,0.03331034,-0.05337935,0.0069013,0.03426567,0.06620132,-0.03390362,0.02822045,-0.01967126,0.00422281,-0.01005611,0.01428045,0.01324848,0.04985993,-0.04464535,-0.19785163,-0.00961802,-0.02509654,0.00901853,-0.00920357,0.01171207,0.02784826,-0.01664234,0.01858813,0.08553654,0.1159954,-0.04406689,0.00271433,0.02449457,-0.00670828,-0.02404469,-0.09733865,-0.03922148,-0.05152728,0.04944433,0.00376095,-0.01973374,-0.07546823,-0.05907083,0.05745254,-0.05583501,0.1276937,0.0072174,-0.01646287,0.01573312,0.08451897,-0.01558684,-0.0419508,-0.04774112,0.01854257,0.0580497,-0.032051,0.03721539,-0.06058792,0.03396136,-0.06127091,0.00170455,-0.0013773,-0.08217204,-0.03699064,0.01428379,0.00202953,0.06170923,0.01320425,0.05253704,0.02385904,-0.03412686,0.02339331,0.04426039,0.06833664,-0.06074894,-0.03856122,0.00605284,-0.03813513,0.05302037,-0.0317962,-0.05937533,0.04025877,0.00527844,0.03478137,0.02040239,0.01393431,-0.0026273,0.03845154,0.02527258,0.02351417,0.06970605,0.01633645,0.00810103,-0.02360857,0.06833109,0.1025566,-0.01546652,0.00698877,-0.01797668,0.01032299,-0.06742271,0.0596674,0.02786717,0.050576,0.04519347,0.080736,0.04552616,-0.01192565,-0.00466251,0.01786886,0.00863392,-0.03963532,0.0072021,0.02285534,-0.0074744,-0.24402615,0.01401467,-0.04157647,0.05708408,0.00568622,0.01180202,0.02609107,0.00146802,0.01467722,-0.03198601,0.05836289,0.06214809,0.03194206,-0.09669053,-0.00759708,-0.03686175,-0.00519565,-0.0255393,0.06235879,0.03436036,0.08052075,0.02879688,0.22948085,0.01256561,0.01845488,-0.006965,0.01232919,-0.00366874,-0.01213433,0.00420203,0.0113589,0.01380926,0.06348538,-0.01510154,-0.05076397,-0.00268258,-0.04049123,0.03297242,0.00582398,-0.00056203,-0.06948666,-0.01951132,-0.02695244,-0.02717337,0.13095467,0.02227883,-0.03519839,-0.06584459,0.02722191,0.06682962,-0.0952262,-0.06846824,-0.06073892,-0.04898249,0.02163786,0.05262451,0.0336684,0.00720944,0.02284735,0.01824686,0.03584908,-0.00735371,0.08305872,0.03459581,0.04426393],"last_embed":{"hash":"1opnnyi","tokens":468}}},"text":null,"length":0,"last_read":{"hash":"1opnnyi","at":1753495682393},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>","lines":[41,87],"size":6489,"outlinks":[{"title":"Lottery software spreadsheet generates strategies based on positional limits of the lotto numbers.","target":"https://saliu.com/ScreenImgs/spreadsheet-pick-lottery.gif","line":5},{"title":"Each lotto number in a lottery drawing comes from a limited group of lotto numbers.","target":"https://saliu.com/ScreenImgs/lottery-strategy-spreadsheet.gif","line":9},{"title":"Run specialized lotto software programs, better than any Excel lottery spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":46}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08505222,-0.00050586,-0.0371361,-0.04539486,-0.05844275,0.06877509,0.02278568,0.00643012,0.10032693,0.00361923,0.05293604,0.0201736,0.05281161,-0.04126331,-0.05555799,-0.02229945,-0.00841642,-0.06078107,-0.06600737,-0.01016507,0.05999294,-0.05219067,-0.04551681,-0.10163083,0.08652416,0.00977371,-0.02535143,-0.06300683,-0.06038241,-0.25101259,0.00253313,0.01655652,0.00872509,-0.05524352,-0.07149405,-0.00888522,-0.03287442,0.05452583,-0.03136633,0.05900893,0.001524,-0.00336951,0.00980785,-0.02338998,-0.01067018,-0.03718198,-0.04793033,-0.01217759,0.06009095,-0.01386524,-0.0639431,0.00322922,0.03122501,0.02353324,0.04285445,0.0175158,0.04326661,0.08493452,0.00705394,0.05272504,0.02954487,0.03737437,-0.1904024,0.04532761,-0.02345701,0.03602232,-0.01798711,-0.05051594,-0.00051955,0.05337236,0.03004868,0.02542103,-0.03214423,0.05587912,0.07541148,-0.05614615,-0.04879548,-0.03137068,-0.04733397,0.04202189,-0.07392661,0.00589334,0.01045361,0.00452405,0.01056138,0.03399033,0.03748887,0.05188747,0.11157701,-0.05470963,0.04599532,0.0469234,-0.00917924,0.03088755,0.01221983,0.01802675,0.07993744,-0.01692901,-0.0304005,0.10418125,-0.02262615,0.01046783,-0.00842987,0.03167765,0.04093333,-0.05503116,-0.02571329,-0.0275036,-0.04246323,0.00925658,0.02411635,-0.00618845,0.07213177,-0.07413312,-0.02606549,0.00993157,0.00109704,-0.04045204,0.04664052,0.00299983,-0.02509996,-0.00386572,-0.00054777,-0.02754145,-0.00902049,0.02483245,0.06517137,0.08165108,-0.0145334,0.0226289,0.03731324,0.01262631,-0.15622528,-0.04728753,-0.0292063,-0.00666679,-0.00099535,-0.00429895,0.00908288,0.00863479,-0.00953204,-0.01656642,0.06049921,-0.10135531,-0.04182335,0.10884488,0.01137516,0.01406849,0.00995191,-0.03705103,0.0194464,0.00692663,-0.02916971,-0.06435014,-0.0079177,-0.00112734,0.05824421,0.06328705,-0.03088319,-0.01070623,-0.03280025,-0.05629544,-0.02163178,0.08431123,-0.01090865,-0.10570172,-0.01662822,0.00557325,-0.03809565,-0.0701224,-0.00773211,-0.00246603,-0.04761147,-0.00798821,0.0691776,0.02879239,-0.06036276,-0.0294023,0.04640467,0.00811706,0.02988562,-0.02620294,-0.0241924,0.03890816,-0.05249768,-0.07951441,0.01240701,-0.03444021,0.01844041,0.04408611,-0.03009922,-0.00477443,-0.06592292,0.04218157,-0.04207273,-0.02694272,-0.0252495,0.00052904,0.06620693,-0.04025745,0.01861088,-0.03755052,0.01431186,0.00394583,-0.02991714,0.04056628,0.01760779,-0.06796546,0.09874692,0.03071893,-0.05337584,0.00591824,0.0332888,0.06331789,-0.03390342,0.02858887,-0.01892337,0.003584,-0.01020681,0.01622126,0.01478396,0.0514095,-0.04529567,-0.19708531,-0.00984172,-0.02574285,0.0087351,-0.01052222,0.01221441,0.02590206,-0.01711662,0.02097769,0.08403923,0.11309208,-0.04275389,0.00414227,0.02678099,-0.0065585,-0.02179309,-0.09500491,-0.03757758,-0.05043006,0.05042025,0.00184495,-0.01802709,-0.07547852,-0.05845449,0.05923191,-0.05520202,0.12819703,0.00562641,-0.02023532,0.01382882,0.08289029,-0.01484519,-0.04099075,-0.04822793,0.01927588,0.05733399,-0.03108691,0.03618505,-0.06031303,0.03277025,-0.05983332,0.00182185,-0.00258114,-0.08322459,-0.03716997,0.01437022,0.00339114,0.0639405,0.01343662,0.05183617,0.02226558,-0.03537889,0.02454946,0.04289839,0.0665231,-0.0630957,-0.03808082,0.00462293,-0.03840047,0.05225379,-0.03207472,-0.05780848,0.03825417,0.00552443,0.03331898,0.01920874,0.01388839,-0.00108648,0.03813553,0.02587326,0.02361097,0.06802014,0.01676861,0.00933649,-0.02310854,0.0664001,0.1000801,-0.0159463,0.00711623,-0.0178152,0.01212765,-0.0656476,0.06085378,0.02845458,0.05026891,0.04459986,0.08112705,0.04511314,-0.01329469,-0.00392307,0.01657745,0.00928849,-0.03925257,0.00673992,0.02468503,-0.01051957,-0.2454603,0.01373706,-0.0429455,0.05411037,0.00456527,0.01128869,0.02759306,0.00304042,0.01385116,-0.03249129,0.0570487,0.06199004,0.03478682,-0.09698988,-0.01008395,-0.0360885,-0.00519607,-0.02527235,0.06069944,0.0317229,0.08090495,0.02644909,0.22968946,0.01204057,0.01782351,-0.00868639,0.0116847,-0.00253096,-0.01230097,0.00629251,0.01205804,0.01565272,0.06238533,-0.01490598,-0.04984602,-0.00236861,-0.04214692,0.03459566,0.00516685,-0.00079822,-0.07151957,-0.02119853,-0.02684739,-0.02785244,0.13109793,0.02107076,-0.03612193,-0.06500438,0.02689557,0.06770647,-0.09375543,-0.06811691,-0.05907235,-0.04945562,0.02290449,0.05325971,0.03440522,0.00602224,0.02412318,0.01879071,0.03690283,-0.00333542,0.08425141,0.03431745,0.04286987],"last_embed":{"hash":"1tfcljx","tokens":468}}},"text":null,"length":0,"last_read":{"hash":"1tfcljx","at":1753495682539},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>2. Original Pick-3 Lottery Strategies, Software</u>#{1}","lines":[43,87],"size":6430,"outlinks":[{"title":"Lottery software spreadsheet generates strategies based on positional limits of the lotto numbers.","target":"https://saliu.com/ScreenImgs/spreadsheet-pick-lottery.gif","line":3},{"title":"Each lotto number in a lottery drawing comes from a limited group of lotto numbers.","target":"https://saliu.com/ScreenImgs/lottery-strategy-spreadsheet.gif","line":7},{"title":"Run specialized lotto software programs, better than any Excel lottery spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":44}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06718375,-0.00959462,-0.0494349,-0.02476696,-0.04623631,0.08299735,0.01698882,-0.00780998,0.09309372,0.00200083,0.01313784,0.01513166,0.02573559,-0.04235641,-0.06057609,-0.01488322,-0.02765056,-0.03194731,-0.05478489,-0.01014713,0.04015312,-0.03835065,-0.04497926,-0.10299901,0.0684273,0.00102833,-0.04033709,-0.07346928,-0.06149825,-0.22129796,0.00840489,0.02539931,-0.00285311,-0.05773013,-0.08618655,-0.02084908,-0.02316082,0.05828524,-0.02785898,0.04813043,0.00941711,-0.00492064,0.02183061,-0.0260105,0.01711579,-0.02386574,-0.04955913,-0.01141127,0.0849429,0.00534573,-0.07158778,-0.01569273,0.03409618,0.02289267,0.05763285,-0.01366656,0.04327847,0.09662759,0.01834274,0.05048811,0.02423255,0.06424899,-0.16648342,0.05108493,-0.01487889,0.01731497,-0.03853947,-0.03127547,0.00140815,0.05565335,-0.00645525,0.02582452,-0.01997362,0.06270062,0.0720506,-0.05353002,-0.04900476,-0.04490656,-0.05252304,0.02558022,-0.06623585,0.00475132,0.009567,-0.00887503,-0.00948013,0.04110866,0.03969824,0.04218112,0.09164943,-0.06349345,0.04145857,0.07462422,0.00285759,0.0340346,-0.00853868,-0.01798816,0.09246029,-0.02809693,-0.02451102,0.11306848,-0.00734414,-0.00871221,-0.01010186,0.03374682,0.0327628,-0.04728118,-0.04056115,-0.03791528,-0.05418798,0.01181393,0.02805247,-0.01038264,0.06320442,-0.05636356,-0.07105669,0.01789632,-0.01312474,-0.0550445,0.0348326,0.0268718,-0.0218942,-0.00680958,-0.00703226,-0.01001745,-0.01212931,0.01657054,0.06023335,0.07929672,0.01633886,0.0277338,0.05159912,0.03129133,-0.16052945,-0.04210407,-0.03945266,-0.00657925,0.02359934,-0.01027959,0.01740365,0.00692303,-0.02066516,-0.00043127,0.0816117,-0.10471901,-0.02858279,0.06453043,0.01694977,-0.01048395,0.03930148,-0.03382787,0.03097741,0.00111112,-0.00231575,-0.0395636,0.00178713,-0.0210207,0.0891374,0.0824085,-0.04123675,0.0077543,-0.04190285,-0.06824051,-0.01092058,0.09665918,-0.02288783,-0.09547992,-0.02607614,0.01767811,-0.03418792,-0.10635914,-0.01227904,0.00164159,-0.07524645,-0.00191815,0.06336772,0.03058847,-0.08296889,-0.04638718,0.04171148,0.00070009,0.02298022,-0.03613584,-0.03956267,0.03737194,-0.05465602,-0.09943484,0.01313502,-0.02335585,-0.00520571,0.07031686,-0.01004631,0.00333776,-0.0858628,0.03187007,-0.0504695,-0.03675921,-0.03535711,-0.04525029,0.06715284,-0.0082631,0.02247633,-0.02109703,0.03363219,0.00907759,-0.00532198,0.02908261,-0.00036445,-0.08648894,0.0884583,0.04922392,-0.06017471,0.0138374,0.02426862,0.06637642,-0.03205454,0.0355959,0.00697875,0.01137822,0.0089455,0.01178563,0.017833,0.02393284,-0.04407215,-0.17864297,-0.02505946,-0.03658077,0.00218081,0.00510732,0.008418,0.04255371,-0.02923248,0.01444562,0.08725368,0.08954777,-0.04655288,0.00926916,0.03555447,-0.00552774,-0.01558214,-0.1073516,-0.06263773,-0.04917277,0.06998878,-0.01895357,-0.00505853,-0.05206043,-0.05072774,0.04875153,-0.0430835,0.13803412,0.00801461,-0.02301542,0.00143432,0.1022293,0.00800043,-0.04854939,-0.03051022,0.02854265,0.06247041,-0.02295309,0.02201809,-0.05132837,0.00290053,-0.06649739,0.04794473,0.01405087,-0.06772208,-0.05801813,0.0045928,-0.01704669,0.01881676,0.03283328,0.06607426,0.04881924,-0.00061055,0.01978442,0.04452776,0.05262548,-0.00642203,-0.03152137,0.02180751,-0.01287636,0.05475958,-0.02463747,-0.07716942,0.03431633,0.00656688,0.04484826,0.0264361,0.01387297,-0.03715831,0.03546264,-0.00309854,0.02647547,0.0793025,0.02152045,-0.01056249,-0.00704158,0.04085075,0.08932928,-0.04340525,0.01127869,0.00999004,-0.00501365,-0.08856905,0.03521536,0.04715502,0.07666312,0.04464651,0.07556508,0.06712689,-0.01692166,-0.00168966,0.0146612,-0.00591392,-0.04760389,0.02937467,-0.00254964,0.01522653,-0.23430996,0.01104201,-0.03088071,0.08160369,0.01232499,-0.01170256,0.02800319,-0.01098947,0.0443712,-0.03407378,0.0857285,0.04211395,0.03206542,-0.07585023,0.00643143,-0.04885923,-0.01030746,-0.02971813,0.04338859,0.03470432,0.06395333,0.03711071,0.22449882,0.00548934,0.0139453,0.02404365,0.01640529,-0.0130282,-0.00594772,0.02326424,-0.01440761,-0.00594524,0.06686664,-0.00904897,-0.03443464,0.00187996,-0.02377159,0.02361222,-0.00810917,-0.00288477,-0.05715418,-0.00296378,-0.06198895,-0.02563447,0.1036272,0.04528396,-0.04016908,-0.06566174,0.03981087,0.05988901,-0.07617106,-0.0796,-0.07136814,-0.07163905,-0.00272831,0.05276625,0.00546009,0.01294385,0.00510119,0.03943232,0.02398225,-0.00713431,0.06401942,0.04501478,0.03809079],"last_embed":{"hash":"16j6w","tokens":457}}},"text":null,"length":0,"last_read":{"hash":"16j6w","at":1753495682689},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>","lines":[88,97],"size":1582,"outlinks":[{"title":"_**Pick-3 Lottery Method, Strategy, System, Play, Software**_","target":"https://saliu.com/STR30.htm","line":3},{"title":"_**Lotto, Lottery Software, Strategy, Systems, Wheels**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"Ion Saliu is the first programmer to use Excel spreadsheets as lottery software.","target":"https://saliu.com/images/lottery-software.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06738606,-0.01091955,-0.04785255,-0.02376444,-0.04490082,0.08430079,0.01605484,-0.0078022,0.09397303,0.00237612,0.01414817,0.01271098,0.02700462,-0.04220951,-0.05946622,-0.0152986,-0.02947602,-0.03179708,-0.05471547,-0.01135237,0.0413909,-0.03663888,-0.04451961,-0.10055366,0.07022344,0.00080897,-0.04166288,-0.07355677,-0.06179073,-0.22176975,0.00948208,0.02536077,-0.00383695,-0.05847533,-0.08649786,-0.02359845,-0.02335162,0.05925826,-0.02731897,0.0463568,0.0097423,-0.00396468,0.02084483,-0.02712253,0.01486275,-0.02422549,-0.05059568,-0.01163943,0.08518691,0.00611356,-0.06919928,-0.01233068,0.03155407,0.02293954,0.05826135,-0.01562251,0.04422494,0.09773116,0.01722894,0.05202096,0.02404992,0.06550837,-0.16748138,0.0497224,-0.0166009,0.01600194,-0.03892764,-0.02825346,0.00295223,0.05652912,-0.00866091,0.02435188,-0.01876452,0.06520122,0.07106834,-0.05301819,-0.04951182,-0.0479003,-0.05304735,0.02965315,-0.06608464,0.00589652,0.00956061,-0.00839166,-0.00697246,0.03887881,0.03883927,0.04189922,0.09128336,-0.0637227,0.04131007,0.07367847,0.00186376,0.03291226,-0.00735462,-0.01950289,0.0914192,-0.02817284,-0.02524567,0.11157865,-0.00712988,-0.00768833,-0.00889857,0.03266914,0.03417077,-0.04615209,-0.04082302,-0.03781537,-0.05385374,0.01365631,0.02930441,-0.00976281,0.06114206,-0.0567336,-0.07098532,0.01813869,-0.01262304,-0.05654735,0.03630951,0.02616263,-0.02309937,-0.00729231,-0.00716355,-0.00882329,-0.01271425,0.01610365,0.05995295,0.076782,0.01639128,0.02941551,0.052825,0.02979182,-0.16158433,-0.04274518,-0.04112835,-0.00902537,0.02380495,-0.01188704,0.01917576,0.00799369,-0.01915602,0.0002343,0.08179961,-0.10603516,-0.02897743,0.06597221,0.01860534,-0.01053521,0.03845939,-0.03402317,0.03079285,0.00146133,-0.00167236,-0.04029232,0.00191328,-0.02108324,0.09068898,0.08322306,-0.04088596,0.00872799,-0.04585641,-0.06891143,-0.00949058,0.09659081,-0.02337421,-0.09569871,-0.02513429,0.01799929,-0.03396688,-0.10688239,-0.01161483,0.00363122,-0.07510327,-0.00248201,0.06558284,0.02926967,-0.08280774,-0.04824676,0.04143137,0.00208181,0.02401923,-0.03662938,-0.04003004,0.03877324,-0.05566651,-0.09735362,0.01345461,-0.02148558,-0.00535224,0.06899085,-0.01378739,0.0062855,-0.08522103,0.03220057,-0.05222366,-0.03522066,-0.03597742,-0.04528034,0.06643608,-0.00632629,0.02217856,-0.02321718,0.03235697,0.00827736,-0.00460281,0.02928943,-0.00103167,-0.0862928,0.0885691,0.04660823,-0.05992712,0.01506876,0.0240837,0.06586612,-0.02984549,0.03390737,0.00846026,0.01062153,0.00782643,0.01056229,0.01691061,0.0255711,-0.04521869,-0.17826965,-0.02692677,-0.03858144,0.00016311,0.00448122,0.00883649,0.04033318,-0.03147811,0.01478355,0.08648111,0.08859064,-0.04640286,0.01178532,0.03733136,-0.00446676,-0.01489385,-0.10734555,-0.06116511,-0.04830343,0.07162514,-0.01972432,-0.00681073,-0.05132345,-0.05021755,0.04935785,-0.04154804,0.13850291,0.00895239,-0.02313698,0.00109907,0.10051987,0.00646723,-0.04638643,-0.02939981,0.02857278,0.06260679,-0.02118216,0.02094833,-0.05020353,0.00088026,-0.06633684,0.04719556,0.01545804,-0.06767447,-0.05892009,0.00486757,-0.01574042,0.01932478,0.03367512,0.06656993,0.05134255,0.00107646,0.01977512,0.04402567,0.05368115,-0.00737529,-0.03180677,0.0236645,-0.01327666,0.05485593,-0.02370141,-0.07685743,0.03360784,0.00592446,0.04272983,0.02557061,0.01311625,-0.03655961,0.03391335,-0.00192615,0.02687629,0.07941137,0.02049804,-0.00962491,-0.00551364,0.04041884,0.0885544,-0.04425837,0.00931341,0.01003231,-0.00627653,-0.08700929,0.03626823,0.04718433,0.07613264,0.0438944,0.07542704,0.06581174,-0.01551442,-0.00077997,0.01259611,-0.00687083,-0.04724648,0.02900001,-0.00291586,0.01362931,-0.23535553,0.01119082,-0.03163733,0.08042628,0.01139996,-0.01395888,0.02859125,-0.0099475,0.04182673,-0.03384899,0.08550015,0.04121539,0.03068664,-0.076138,0.00655221,-0.04789629,-0.01005631,-0.0294494,0.04518765,0.03539221,0.06515133,0.03761845,0.22574501,0.00252462,0.01280847,0.02638992,0.01545459,-0.01069911,-0.00738127,0.02165836,-0.01470579,-0.00570347,0.06756435,-0.01030635,-0.03418726,0.00325627,-0.02272806,0.02354227,-0.00716506,-0.00430749,-0.0563853,-0.00292449,-0.06063938,-0.02452374,0.10460693,0.04574908,-0.04015949,-0.06495751,0.04008131,0.05933058,-0.0752748,-0.07891051,-0.0716932,-0.07108397,0.00084624,0.05472048,0.00451977,0.01139038,0.00609559,0.0387027,0.02196452,-0.00572953,0.06309442,0.04406974,0.03795787],"last_embed":{"hash":"1wji616","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"1wji616","at":1753495682839},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>3. More on Gambling Formula, Lottery, Strategies, Software</u>#{1}","lines":[90,97],"size":1512,"outlinks":[{"title":"_**Pick-3 Lottery Method, Strategy, System, Play, Software**_","target":"https://saliu.com/STR30.htm","line":1},{"title":"_**Lotto, Lottery Software, Strategy, Systems, Wheels**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"Ion Saliu is the first programmer to use Excel spreadsheets as lottery software.","target":"https://saliu.com/images/lottery-software.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05247688,-0.03137552,-0.02250783,-0.07320568,-0.0205712,0.05083201,0.01869656,0.01302794,0.03127488,-0.01648976,0.03431601,0.03107147,0.03876429,-0.01193587,-0.06932618,-0.02910155,0.00360307,0.00239909,-0.0595893,-0.02631377,0.04107559,-0.0319825,-0.05139844,-0.11832573,0.04560104,0.01654211,-0.04419271,-0.06898614,-0.07850066,-0.20099701,0.0532251,0.00245614,0.02558379,-0.0450389,-0.03414652,-0.04870778,-0.00897482,0.0178886,-0.03925096,0.01599978,0.03858391,-0.03370356,0.01729155,0.01539855,0.02009426,-0.04656461,-0.0249452,0.01273714,0.05812856,0.02604308,-0.09445874,-0.0187961,0.00662666,0.02140008,0.02608746,-0.00207588,0.05686811,0.07707619,0.03938557,0.05978099,0.05224788,0.06032117,-0.17283799,0.06399534,0.00032599,0.03319171,-0.02312464,-0.0475908,0.03694467,0.00366361,-0.05364024,0.00966097,-0.03237744,0.06518502,0.03844904,-0.05521841,-0.0759744,-0.07492986,-0.05496241,0.04344336,-0.08143232,0.02509158,0.02647826,0.02582259,-0.00833093,0.0429143,0.04282993,0.02341499,0.10142626,-0.01909559,0.0493628,0.0416341,-0.01697682,0.03722889,0.01361032,-0.00211759,0.07564089,-0.04881075,0.00726481,0.13566153,-0.03829091,-0.0337542,-0.00584424,0.02919001,-0.00202895,-0.06615079,-0.03288394,-0.02636679,-0.01910833,0.00797909,0.01615889,-0.01508866,0.00104802,-0.07152554,-0.06666271,-0.00048697,-0.04239451,-0.06698042,0.01715615,0.03617855,-0.04264199,-0.04566743,-0.00610188,0.03397256,0.02962525,-0.01661932,0.06208602,0.06640864,-0.00425395,0.01605113,0.07370701,0.04983845,-0.16031845,-0.02545041,-0.00978908,0.00268283,0.02753473,0.02588169,0.02776951,-0.02756253,-0.01703401,-0.0171643,0.06343149,-0.05883871,-0.01784822,0.10105236,-0.02634724,0.02202033,0.00794269,-0.06374811,0.02518205,0.00253993,-0.05831958,-0.01375355,0.01479929,0.03955368,0.06660607,0.02376559,-0.05427103,-0.00143875,-0.07057325,-0.05180874,0.00764775,0.09813286,-0.02480002,-0.08508826,-0.01942256,0.03680163,-0.02185932,-0.09396724,0.02336083,0.00150898,-0.07174914,0.02374269,0.06338045,-0.01117413,-0.05012345,-0.0556048,0.01256063,0.00533427,0.05136175,-0.03163495,-0.04520344,0.0219785,-0.04189265,-0.09795748,-0.0134614,-0.00764355,0.00431371,0.05039594,-0.00499714,0.00780709,-0.06844444,0.00107854,-0.03110889,-0.06201653,-0.0276179,-0.05048207,0.06347375,-0.03530566,0.07096462,-0.01902023,0.03435468,0.04306748,0.02246569,0.01651198,-0.02918578,-0.04048085,0.10563692,0.01598624,-0.04221487,-0.01776047,0.01665092,0.07095313,-0.04518904,0.04986575,0.03814226,-0.00861865,0.02250632,0.02360293,0.01371862,0.01981088,-0.03188884,-0.18695568,-0.03504807,-0.00227323,0.02791079,-0.00732953,-0.02760948,0.03192762,-0.04093489,-0.00816212,0.13026345,0.11438014,-0.03752341,0.01157842,0.0257942,-0.01576808,0.00101725,-0.08810326,-0.04925925,-0.06531871,0.04264519,0.01240979,-0.02245414,-0.02849846,-0.04223292,0.03819623,-0.00837771,0.11918698,-0.05583273,0.03034582,0.00958608,0.0877825,-0.03719003,0.00642896,-0.04533402,0.00685754,0.05258377,-0.02769408,0.03795772,-0.05834915,0.02779308,-0.08100924,0.0408974,0.04478383,-0.06915987,-0.03069344,0.00583348,0.03306063,-0.03865199,-0.01057334,0.03430334,0.00621608,-0.01336993,0.05204787,0.06498192,0.0476659,-0.01420517,-0.02997834,0.03787157,-0.02264605,0.05074724,-0.02072728,-0.08590334,0.02687113,-0.01447273,0.02442442,0.00331642,0.00380268,-0.00701215,0.05369928,-0.00060688,0.0183724,0.08975933,-0.0283944,0.04034555,0.01673872,0.07539645,0.09479719,-0.02726055,0.01067635,0.0113011,0.0044696,-0.05517995,0.03159676,0.01143098,0.03288638,0.06697156,0.09261262,0.04483576,0.0171163,0.01303394,0.03175394,-0.04435188,-0.0108073,0.03750806,-0.00602638,0.02503975,-0.2360173,0.04945019,-0.04219215,0.0677276,0.00093023,-0.06852347,0.01785832,-0.03563377,0.0148953,-0.0339397,0.05174416,0.00991828,0.03461658,-0.13103564,0.00552337,-0.0514849,0.0519278,-0.01812063,0.05671589,0.03968392,0.07016106,0.00164857,0.21007028,-0.00693232,0.02545326,0.04029084,0.01501437,-0.03763568,0.00928285,0.03676087,0.03919877,-0.02307107,0.09178639,-0.01989883,-0.03245576,0.0263874,-0.02918477,0.0318518,0.02428346,0.00669207,-0.03811403,0.00854492,-0.07085547,-0.03464098,0.10032315,0.04801277,-0.03708701,-0.0817817,0.03583755,0.06833238,-0.06244621,-0.04938712,-0.07672887,-0.03816744,0.00605852,0.03647561,-0.00218228,0.05302383,0.03665527,0.02086635,0.05392122,-0.05447714,0.06709771,0.02948634,-0.00900138],"last_embed":{"hash":"15mfmmi","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"15mfmmi","at":1753495682973},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>","lines":[98,131],"size":4035,"outlinks":[{"title":"Learn about programming the lotto games, lottery software with Excel spreadsheet software.","target":"https://saliu.com/images/lotto-software.gif","line":33}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05198733,-0.03313109,-0.02188288,-0.07389222,-0.01846293,0.05118014,0.0172625,0.01268262,0.0296779,-0.01653516,0.03213812,0.0316472,0.0415249,-0.01257433,-0.06913625,-0.02887654,0.00227522,0.00154417,-0.05924358,-0.02758002,0.04103839,-0.02939878,-0.04938615,-0.11650464,0.04525297,0.01746655,-0.04366583,-0.07144509,-0.07785872,-0.20243096,0.05513929,0.00176353,0.02666439,-0.04528042,-0.0327178,-0.05069389,-0.00869229,0.01641384,-0.03741094,0.01361561,0.03747827,-0.03357156,0.01672177,0.01648813,0.01863083,-0.04847811,-0.02693637,0.01331513,0.05526985,0.02640258,-0.09318345,-0.01841178,0.00444552,0.02238088,0.02425128,-0.00353577,0.05841326,0.07657199,0.03742373,0.05969759,0.05387141,0.06054734,-0.17456047,0.06431786,-0.00148864,0.03323862,-0.02259655,-0.0485078,0.03931693,0.0047245,-0.05576819,0.00774035,-0.03283026,0.06533097,0.03783645,-0.05363006,-0.07573267,-0.07642265,-0.05359266,0.04781212,-0.08109446,0.02620929,0.02730625,0.02527785,-0.00878867,0.0413884,0.04281453,0.02284322,0.10075121,-0.01786978,0.05043156,0.04077382,-0.01901248,0.03830742,0.01363647,-0.00326766,0.07521073,-0.04822936,0.00858775,0.13432311,-0.04005362,-0.03210577,-0.00337091,0.02983499,-0.00119382,-0.06330374,-0.03084582,-0.02758611,-0.01960309,0.00851043,0.01496763,-0.01610232,-0.00172792,-0.07263171,-0.06338329,-0.00036671,-0.04264228,-0.06661386,0.01604866,0.03573278,-0.04291865,-0.04558555,-0.00681694,0.03549764,0.02983498,-0.01650306,0.06202945,0.06618171,-0.00422207,0.01582689,0.07507747,0.04773628,-0.16137226,-0.02422838,-0.01360644,0.00284087,0.02848059,0.02452026,0.02759,-0.02580867,-0.0173403,-0.01480144,0.061944,-0.05814776,-0.01894276,0.10405617,-0.02628603,0.02313455,0.0065553,-0.0631661,0.02499749,0.00315822,-0.05885836,-0.01462805,0.01412157,0.04208136,0.06610904,0.02351175,-0.05487039,-0.00052435,-0.07187489,-0.05280892,0.00859291,0.09648146,-0.02455955,-0.08265568,-0.01895384,0.03823847,-0.02218482,-0.09113676,0.0234255,0.00355855,-0.07231314,0.02232844,0.06265629,-0.012441,-0.04991841,-0.05603843,0.01264807,0.00713784,0.05364652,-0.02965035,-0.0465336,0.02268043,-0.04251529,-0.09792825,-0.01457726,-0.00511514,0.00536409,0.04947124,-0.00824334,0.00733958,-0.06890515,0.00060437,-0.03152027,-0.06260361,-0.02423475,-0.0502438,0.06236896,-0.03707563,0.07239816,-0.01927304,0.03311571,0.04233051,0.02442881,0.01651322,-0.03082469,-0.03856999,0.1059307,0.01511796,-0.04120184,-0.01824583,0.01466519,0.06878914,-0.04487789,0.04854299,0.0405306,-0.00844451,0.02185418,0.02375397,0.01360076,0.02025883,-0.03252952,-0.1879342,-0.03716075,-0.00248056,0.02720292,-0.00824105,-0.02887011,0.02804339,-0.03911415,-0.0095036,0.12923287,0.1123445,-0.03352147,0.01157685,0.02639713,-0.01593624,0.00302234,-0.08878069,-0.04915134,-0.06588491,0.04305973,0.01163304,-0.02132647,-0.02723642,-0.04137843,0.03692267,-0.00683526,0.11866732,-0.05755788,0.03252446,0.00658257,0.08514455,-0.03817449,0.00637921,-0.0455177,0.0082791,0.05240556,-0.02773121,0.03727609,-0.05536289,0.02631676,-0.08142079,0.04127818,0.04752599,-0.06824171,-0.03153938,0.00733986,0.03740035,-0.03980134,-0.01173937,0.03678438,0.00595982,-0.01340806,0.05240501,0.0669317,0.04535972,-0.0155114,-0.03037403,0.03953249,-0.02008582,0.0524505,-0.02078715,-0.08664168,0.02720726,-0.01591741,0.02515163,0.00167861,0.00402685,-0.00458633,0.05264142,0.00006516,0.01686767,0.09275237,-0.02983169,0.04090298,0.01934952,0.07315954,0.09517689,-0.02807048,0.01095517,0.01150345,0.00451048,-0.05484341,0.03074318,0.00862659,0.03353685,0.06773086,0.09390156,0.04208489,0.01839958,0.0133571,0.03038773,-0.04610969,-0.01078237,0.03681275,-0.00547513,0.02478677,-0.23549913,0.04961,-0.04129346,0.06618892,0.00058444,-0.07129814,0.01733793,-0.03729574,0.01152336,-0.03600178,0.05060472,0.00792603,0.03557399,-0.13296239,0.00782868,-0.05017772,0.05252856,-0.01768027,0.0577941,0.03990589,0.0703542,0.00033198,0.21064015,-0.00753212,0.02663574,0.04215262,0.01385058,-0.0362931,0.01090983,0.03919556,0.04014929,-0.02078383,0.09226999,-0.01919187,-0.03293944,0.02918437,-0.02812529,0.03258329,0.02435499,0.00554885,-0.03639463,0.00891649,-0.07086331,-0.03350592,0.10082006,0.04665497,-0.03525535,-0.08232992,0.03420984,0.066447,-0.05975456,-0.04834705,-0.07630847,-0.03786802,0.00883725,0.03612552,-0.00068484,0.05219574,0.03739226,0.02013383,0.05204642,-0.05347831,0.06578708,0.03048233,-0.01133479],"last_embed":{"hash":"1afefsq","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"1afefsq","at":1753495683127},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>4. Using <i>Excel Spreadsheets</i> to Analyze the Lottery I</u>#{1}","lines":[100,131],"size":3964,"outlinks":[{"title":"Learn about programming the lotto games, lottery software with Excel spreadsheet software.","target":"https://saliu.com/images/lotto-software.gif","line":31}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05503536,-0.05793154,-0.02719743,-0.05069846,-0.03762287,0.04107292,0.034575,0.01291396,0.06314104,0.00717987,0.02355077,0.04475345,0.03765955,-0.02402289,-0.07340413,-0.02782364,-0.00921383,-0.01417973,-0.07016366,-0.02213462,0.03786317,-0.03243804,-0.04932748,-0.11964004,0.05997882,0.02395316,-0.07667546,-0.08683907,-0.08641772,-0.22514497,0.03094305,-0.01883324,0.03564176,-0.04180228,-0.03565836,-0.06247245,-0.00120666,0.0461779,-0.02116241,0.02219691,0.01715085,-0.03166493,0.0122194,-0.01563743,0.03348747,-0.04627478,-0.01080953,0.00863718,0.01693864,0.03011645,-0.07721414,-0.00887648,0.03583395,0.03970672,0.02809469,-0.00305708,0.02279423,0.05320653,0.05024127,0.07312094,0.0231566,0.06926615,-0.17844301,0.04680036,0.01028386,0.00855876,-0.03408774,-0.04902351,0.0317849,0.00825026,-0.01321264,0.00579292,-0.04063097,0.08720533,0.06032529,-0.07519563,-0.05237395,-0.06732327,-0.05362562,0.04166947,-0.07328192,0.01804155,0.04105028,0.01349108,-0.02536268,0.0447559,0.06050354,0.01766825,0.10729815,-0.02057089,0.046675,0.03916365,-0.05764204,0.03542583,-0.00823814,0.00450971,0.06545776,-0.02665796,0.02893761,0.11979063,-0.0388543,-0.01367674,0.00615701,0.01740273,0.02477727,-0.04935034,-0.01495114,-0.04471083,-0.01891043,-0.01497166,0.02318678,-0.03155776,-0.00062681,-0.05658343,-0.03458705,0.00579722,-0.00989868,-0.05134104,0.0362799,0.00172813,-0.04427591,-0.02253672,0.00332153,0.00622819,0.02491871,-0.00441875,0.04974494,0.07099587,-0.00242622,-0.0057829,0.08906605,0.00594026,-0.14899938,0.00026452,0.00097355,-0.01453666,0.02095729,0.03559491,0.01663817,-0.0243775,-0.0079236,-0.0080417,0.04119774,-0.05575224,-0.05084882,0.11632378,-0.03853518,0.00225248,0.00212816,-0.07336225,0.05264115,-0.01793309,-0.05595503,-0.02259701,0.01682374,0.02351401,0.07544342,0.04853992,-0.03465662,-0.02316319,-0.05098598,-0.0754355,-0.00977593,0.0962266,-0.02850692,-0.06311075,-0.0220025,0.04428155,-0.02592155,-0.04840792,0.04851554,0.00594268,-0.05718847,0.02224572,0.0307553,0.00597082,-0.0617266,-0.06212609,0.00799521,0.00720659,0.03599599,-0.01627062,-0.04566533,0.02932842,-0.04380165,-0.09717893,-0.01399055,0.00401326,0.00592681,0.07184506,0.00433486,0.00752549,-0.10470356,0.00820991,-0.03296965,-0.06896768,-0.02093063,-0.03269602,0.03414687,-0.06185482,0.06909034,0.00135292,0.01386425,0.01686989,-0.01065094,0.02139905,-0.00175512,-0.04399842,0.08856212,0.01605987,-0.01421668,-0.05001449,-0.03735488,0.06667861,-0.02323752,0.04866579,0.03806741,0.02818769,0.02697544,0.03170826,0.0063743,0.05442293,-0.02542405,-0.19185109,0.01076713,-0.00014534,0.01674453,-0.00624852,0.00559216,0.03482075,-0.02496639,-0.03005772,0.09631279,0.11282524,-0.02086051,0.01351539,0.00155637,-0.01554283,-0.00317179,-0.09798557,-0.02771638,-0.06458809,0.05143734,0.01527445,0.00759296,-0.03827545,-0.07175383,0.04170817,-0.0119237,0.11403807,-0.06065795,0.05456169,-0.00756192,0.07803279,-0.02346961,-0.02107304,-0.03913039,0.01027811,0.06324456,-0.05172998,0.03313311,-0.05458193,0.01940537,-0.08186913,0.00734032,0.03198511,-0.06804095,-0.05960011,0.01023493,0.04543835,-0.03378521,-0.01638051,0.04530426,-0.00520128,-0.0185321,0.04413185,0.06784939,0.06259441,-0.04070377,-0.05075511,0.03212048,-0.02373409,0.0726228,0.00225874,-0.07560793,0.05885389,-0.03388034,0.02560556,-0.01033477,0.01831494,-0.02048834,0.05618881,0.01444987,-0.0011396,0.12743858,-0.03473886,0.01455905,0.0136026,0.05335933,0.10424687,-0.03167227,0.00895004,0.00341016,0.01940371,-0.07447534,0.04892599,0.01157519,0.03493105,0.0557375,0.08620112,0.04667908,0.00244738,0.02247519,0.0108447,-0.04301742,-0.00967242,0.02380201,-0.00078898,0.00732721,-0.22506341,0.0415726,-0.03824663,0.05798103,-0.00216736,-0.04410524,0.04266467,-0.01128303,0.01312723,-0.03461326,0.05784054,0.02768145,0.0319417,-0.08505569,0.01279682,-0.06009549,0.01690195,-0.01546861,0.04865056,0.0303112,0.06980877,0.00531145,0.21189836,0.0028297,0.00918598,0.01285825,0.03172367,-0.0208356,0.03316364,0.05231779,0.03173636,-0.01639054,0.07296821,-0.0417854,-0.01464211,0.0215599,-0.01925799,0.05670391,0.01826553,-0.02106326,-0.05343176,0.01031732,-0.05886665,-0.04814839,0.12454719,0.0460729,-0.03472733,-0.08956835,0.07034712,0.07944755,-0.06470353,-0.04025375,-0.05841549,-0.03404743,0.00120306,0.02479667,0.0146225,0.04305962,0.04296951,0.01269179,0.05754995,-0.04868638,0.08117574,0.03015554,0.00264519],"last_embed":{"hash":"h96pnu","tokens":435}}},"text":null,"length":0,"last_read":{"hash":"h96pnu","at":1753495683261},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>","lines":[132,199],"size":6420,"outlinks":[{"title":"_**Lottery Strategy by Lotto Software on <u>Positional Frequency</u>**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":65},{"title":"Analysis of Excel spreadsheets applied to lottery and lotto developing of software started here.","target":"https://saliu.com/images/lottery-software.gif","line":67}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05483356,-0.0593139,-0.02642572,-0.05173184,-0.03777785,0.04301753,0.03223962,0.01354,0.06371343,0.00678586,0.02385277,0.04592909,0.0378482,-0.02278698,-0.07426306,-0.02813937,-0.00828224,-0.01582849,-0.06998044,-0.02406283,0.03805084,-0.03075825,-0.04876339,-0.1214333,0.06042937,0.02221434,-0.07910351,-0.08578112,-0.08787362,-0.22468753,0.03160693,-0.01996951,0.03776632,-0.04076978,-0.03488299,-0.0637114,0.00037992,0.04574458,-0.0209205,0.02169265,0.01634613,-0.03296622,0.01286198,-0.01553797,0.03289632,-0.04519465,-0.01133066,0.00794526,0.01840349,0.03172413,-0.07598177,-0.00752318,0.03218449,0.03993133,0.0264141,-0.00270772,0.02358495,0.05379744,0.05039405,0.07461039,0.02368871,0.06902254,-0.17840044,0.04590836,0.00939498,0.00718968,-0.03194956,-0.04827357,0.03130178,0.00869182,-0.01424616,0.00412904,-0.04162332,0.08683541,0.061747,-0.07483857,-0.05414628,-0.0699231,-0.05269229,0.04146477,-0.07242568,0.01848354,0.04102294,0.01383751,-0.02459544,0.04544626,0.0611817,0.01681202,0.10738865,-0.0194616,0.04587221,0.03800471,-0.05753493,0.03350924,-0.0072332,0.00310383,0.06485785,-0.02422647,0.02943842,0.11923238,-0.04231837,-0.01219487,0.00544214,0.01753513,0.02586666,-0.04802262,-0.01446711,-0.04339644,-0.01957438,-0.0138043,0.02301321,-0.03203886,-0.00100481,-0.0560741,-0.03208131,0.00593573,-0.00949505,-0.05121695,0.03387472,0.0001467,-0.04325764,-0.02218997,0.00262568,0.00678446,0.02739809,-0.00329468,0.04844175,0.06983542,0.00033453,-0.00554095,0.09045063,0.00378255,-0.14904203,-0.00066687,0.00227089,-0.01617161,0.02151082,0.03402976,0.01586989,-0.02599226,-0.00865451,-0.00798054,0.04068783,-0.054778,-0.05289762,0.12125605,-0.04056258,0.00077683,0.00068223,-0.07370338,0.05131443,-0.016437,-0.05745042,-0.02351343,0.01482791,0.02580921,0.07263916,0.04932368,-0.0364903,-0.02410183,-0.05083829,-0.07656259,-0.01050733,0.09615716,-0.02869985,-0.0612297,-0.0219741,0.04664502,-0.02665426,-0.04791797,0.04954622,0.00552634,-0.05587962,0.02284073,0.03076318,0.00457819,-0.06163191,-0.06390627,0.00675636,0.00608649,0.03594258,-0.01650193,-0.04733268,0.02771153,-0.04362432,-0.09459115,-0.01288023,0.003434,0.00666018,0.07155348,0.00428314,0.00834981,-0.1025933,0.00838342,-0.03405212,-0.06812242,-0.01940229,-0.03219665,0.03358989,-0.06258033,0.06904804,0.00292458,0.01306977,0.01672111,-0.00895508,0.02235805,-0.00212333,-0.04243788,0.0881281,0.01609547,-0.01239524,-0.04988051,-0.03845983,0.06762367,-0.02113658,0.04703071,0.03924802,0.02912698,0.02676339,0.03153764,0.0070371,0.05311253,-0.02579036,-0.19241023,0.01015086,0.00020197,0.0159436,-0.00659003,0.00493463,0.0314326,-0.02360751,-0.03121515,0.0954078,0.11557592,-0.02015027,0.0142509,0.00224989,-0.01529602,-0.00128541,-0.09734987,-0.02631442,-0.06570336,0.05234747,0.01749452,0.00677444,-0.03829458,-0.06955703,0.04381147,-0.01032313,0.11557382,-0.06196861,0.05754003,-0.00639194,0.07671002,-0.02531927,-0.02049585,-0.03876957,0.00757191,0.06147162,-0.05059015,0.03213737,-0.05408169,0.01774407,-0.08288562,0.00509465,0.03143976,-0.06877192,-0.05683824,0.01090563,0.04508188,-0.03441725,-0.01662754,0.04676666,-0.00582234,-0.01945871,0.04467217,0.06930403,0.06098416,-0.04281208,-0.04902474,0.03220635,-0.02408549,0.07441401,0.00355674,-0.07651134,0.05998991,-0.03568092,0.02400653,-0.01168505,0.0162869,-0.01782718,0.05514718,0.01352152,-0.00290375,0.12764631,-0.03452657,0.01660753,0.01576725,0.05340529,0.10420956,-0.03215121,0.00984851,0.00383094,0.01965992,-0.07243233,0.04750005,0.01306197,0.0344959,0.05598334,0.08783502,0.04556661,0.00242048,0.02109527,0.01245571,-0.04384382,-0.01013146,0.02077479,0.00014352,0.00434822,-0.22628641,0.04300261,-0.03890136,0.05867002,-0.00246408,-0.04383171,0.04313267,-0.01067542,0.01058259,-0.03527634,0.05764356,0.02757295,0.03089025,-0.08827817,0.01514704,-0.060004,0.01635502,-0.01601415,0.04850287,0.03027816,0.07014557,0.00349898,0.21057898,0.00287185,0.0095782,0.01346279,0.03213067,-0.02086323,0.03393848,0.05339863,0.03223104,-0.01667935,0.07292985,-0.03988357,-0.01724459,0.02107759,-0.0175165,0.05562091,0.01846437,-0.02054438,-0.05194067,0.0106117,-0.05786458,-0.04720921,0.12294861,0.04548268,-0.03311745,-0.09068193,0.06928441,0.08081856,-0.06059374,-0.03915996,-0.05759068,-0.03309456,0.00322226,0.02603944,0.01512526,0.04291603,0.04328004,0.01170161,0.05522297,-0.04682876,0.07861632,0.03081638,0.00176212],"last_embed":{"hash":"1j05zlz","tokens":445}}},"text":null,"length":0,"last_read":{"hash":"1j05zlz","at":1753495683402},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{1}","lines":[134,194],"size":5774,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06702606,-0.03040995,-0.02263253,-0.06659987,-0.03802696,0.04380294,-0.0209678,0.0117627,0.05478835,-0.02763162,0.02908617,-0.00581572,0.04824596,-0.02461129,-0.04354016,-0.06318027,0.0038077,0.0407591,-0.02936863,-0.03294949,0.05599519,-0.00986672,-0.05050132,-0.07632546,0.03432554,0.02867859,-0.06418919,-0.0802925,-0.05859849,-0.21353471,0.04274559,0.03947052,0.03010038,-0.02335273,-0.05771039,-0.04230201,-0.01960968,0.02816712,-0.04475282,0.03432653,0.02017036,0.00866596,0.0032806,-0.02431363,0.01838687,-0.02409047,-0.00174276,0.02134266,0.00403259,-0.00155863,-0.06474485,-0.01857759,0.01035674,0.04666535,0.04565535,0.03886272,0.03248592,0.03372407,0.04058024,0.05615186,0.03494111,0.03533138,-0.20175712,0.07318962,-0.00902321,0.01106741,0.02029676,-0.03768181,0.02506691,0.01814419,0.00892378,0.03410038,-0.02651323,0.0476945,0.06473751,-0.03633375,-0.06400886,-0.07790681,-0.05122135,0.03433089,-0.06068034,0.00723353,0.01298179,-0.00376547,0.02706188,0.04000739,0.06545015,0.01859932,0.10123409,-0.0035236,0.03739137,0.05314857,-0.019832,0.02221014,0.02467874,-0.03243252,0.03956119,-0.02696986,-0.0014473,0.13253789,-0.01854895,-0.02103575,0.01459979,0.02791204,0.0027781,-0.04792303,-0.05655413,-0.03221819,-0.00941741,-0.0043071,0.04475675,0.05284702,-0.0154335,-0.10169049,-0.0497677,-0.00980753,-0.01668899,-0.01494109,0.03845073,0.01090988,-0.00693249,-0.05327192,0.0168174,0.03629374,0.04804038,-0.01505978,0.08005072,0.05734973,0.01095808,0.00155566,0.07185549,0.01393785,-0.13904822,-0.03749232,-0.01245129,0.00767894,0.02374759,-0.03523646,0.01477525,0.02057055,-0.0136842,-0.04950932,0.08934242,-0.1048483,-0.00679126,0.13904928,-0.0166151,-0.01247218,0.00141583,-0.02629282,0.00713473,-0.02149083,-0.03040114,-0.06229504,-0.02784313,0.01877076,0.08591042,0.05227961,-0.02227314,-0.02273667,-0.05634348,-0.03595106,-0.0340123,0.145237,-0.02711381,-0.09543797,-0.03111875,0.02154352,-0.0297535,-0.11056825,0.01861393,0.01981742,-0.02646177,0.05835152,0.07766459,0.03322037,-0.02774523,-0.01592218,-0.00041228,0.00301589,0.04625472,-0.05115357,-0.02535955,0.0238607,-0.03939893,-0.0609927,-0.0112778,-0.00108231,0.00032774,0.03666494,-0.02732884,-0.0145775,-0.07158751,-0.00106546,-0.0286508,-0.041009,-0.04655325,-0.0089382,0.04817614,-0.01062409,0.0665736,-0.00519338,-0.01040664,0.04170668,0.02180035,0.04064508,0.0378645,-0.04853424,0.07941338,0.02416809,-0.02882187,0.00623603,0.02547564,0.03695508,-0.01811458,0.02817147,0.013425,-0.01194125,0.00950656,0.05006159,0.01090027,0.03330987,-0.05855442,-0.19522829,-0.02051577,0.0097735,0.01391417,-0.03340287,-0.01217796,0.0206058,-0.01835874,0.05264095,0.07280774,0.10568944,-0.04968373,-0.00838025,0.03763456,-0.02261997,-0.0224487,-0.06251935,-0.05808776,-0.04207795,0.04216706,0.0333228,0.01176301,-0.0746581,-0.03752195,0.09062507,-0.03515838,0.1044323,-0.03573923,0.03888042,0.00813501,0.07727064,-0.00924345,0.00325305,-0.01584238,0.00716184,0.05449617,-0.02482379,0.00386591,-0.02017883,0.01122471,-0.09468931,0.0187893,0.03604219,-0.08994391,-0.0348156,0.01782613,0.0227745,-0.01638378,0.00005699,0.00485706,0.07635097,-0.03222787,0.0492897,0.05592073,0.05453012,-0.03702634,-0.04002193,-0.01471663,-0.05021366,0.00438268,-0.05431177,-0.04663362,0.04704981,-0.00284306,0.01214024,0.00991395,-0.02026121,-0.0109997,0.02440007,0.03439882,0.04053061,0.08894452,-0.02828211,0.01614102,-0.01069886,0.03077831,0.07328817,-0.06418054,0.00268563,0.0089363,0.00139196,-0.05144887,0.04196179,0.00988097,0.05314498,0.02000892,0.0730515,0.06341299,0.00888286,-0.01136009,0.02512139,-0.00190337,-0.0210335,0.05976613,0.00067828,-0.00042321,-0.25600186,0.02562977,-0.06280136,0.0607405,-0.01733104,-0.04779752,0.00128023,-0.03380019,-0.00196314,-0.00040153,0.02688571,0.02509023,0.01219192,-0.09648158,-0.02514179,-0.02224046,0.04447356,-0.07522354,0.0399247,0.02089513,0.05284584,0.03146214,0.27325901,0.00312069,0.01527395,0.04342146,0.01301799,-0.01360467,0.00494898,0.03352974,0.03961946,-0.04242774,0.06753139,-0.02508274,-0.07938322,-0.01972624,0.00840114,0.03302457,-0.00451036,0.03076376,-0.03572541,0.00496255,-0.04208357,-0.03692589,0.14993495,0.03854248,-0.04527812,-0.07442684,0.06652473,0.05714184,-0.06459785,-0.05189156,-0.10497189,0.00520364,-0.00016409,0.01421488,-0.01144511,0.01565332,0.03988566,-0.00679887,0.03734678,-0.04156141,0.06144848,0.02940839,0.01781231],"last_embed":{"hash":"tv7uia","tokens":147}}},"text":null,"length":0,"last_read":{"hash":"tv7uia","at":1753495683547},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>5. Using <i>Excel Spreadsheets</i> to Analyze the Lottery II</u>#{3}","lines":[196,197],"size":306,"outlinks":[{"title":"_**Lottery Strategy by Lotto Software on <u>Positional Frequency</u>**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07443202,-0.03322139,-0.00935352,-0.10412145,-0.05813714,0.05345112,-0.00517507,-0.02917305,0.08382647,-0.01611694,0.0452821,0.00535733,0.02801647,-0.03682686,-0.04329639,-0.01482358,0.00336677,-0.01203737,-0.07778466,-0.02075561,0.09525677,-0.0409659,-0.05135786,-0.11074826,0.03449441,0.02520335,-0.03575503,-0.05672745,-0.05028269,-0.22795062,0.05231017,0.03217531,0.03970404,-0.04029759,-0.06500709,-0.04757431,-0.03984543,0.00059864,-0.05730191,0.01203887,0.01797969,0.02743599,0.00636496,-0.02614554,0.03718247,-0.06361547,0.02758203,0.01693784,0.0533524,0.00739197,-0.05433887,-0.00900368,0.01206553,0.06616356,0.07100134,-0.00014544,0.03525132,0.05168139,0.03702259,0.05790024,0.04448133,0.08262132,-0.17675312,0.06816874,0.02881924,-0.00071456,-0.04403093,-0.02903722,0.01978212,-0.00703941,-0.02400778,0.03539461,0.00121153,0.08787227,0.02513674,-0.05396287,-0.07361438,-0.04675616,-0.0622407,-0.00404509,-0.04209303,-0.0295503,0.01333985,-0.02298464,0.00212303,0.02176459,0.09354486,0.01222331,0.08112387,-0.02107679,0.0122933,0.05931019,0.01247588,0.03469693,0.01354013,0.00242773,0.0489771,-0.01876926,-0.01694834,0.12162621,-0.0358422,-0.02396779,-0.0249381,0.00509701,0.03725547,-0.07848724,0.00098997,-0.01597022,-0.04629699,-0.00997733,0.03769233,-0.01443726,0.02011172,-0.04514832,-0.0466784,0.00058009,-0.03717908,-0.03713967,0.01878113,0.01253429,-0.03356256,0.01153334,-0.0169482,0.03180307,0.00679146,0.01595173,0.05198106,0.05627386,0.02172814,0.03205626,0.06477372,0.00927332,-0.12022457,-0.0302113,0.00073728,0.01798856,0.01866988,-0.02805704,-0.01157215,0.00757353,-0.00281628,-0.01151923,0.05068715,-0.1031094,0.00356463,0.10196984,-0.01189394,0.00639468,0.02383205,-0.04348664,0.0128014,-0.0149108,-0.03069325,-0.06098592,0.02674839,0.04861723,0.06062264,0.0661053,-0.0730358,-0.05364741,-0.05971758,-0.02998887,0.01655653,0.09903408,-0.00925779,-0.08364252,-0.0198822,0.01597227,-0.02949701,-0.09264458,-0.01613832,0.03007445,-0.06077607,0.01417856,0.04925997,-0.0149695,-0.04928806,-0.04844955,0.01875188,-0.00334542,0.02560621,-0.01296253,-0.03111557,0.0174389,-0.05419138,-0.06243549,0.00880055,-0.01921959,0.02384599,0.06154791,0.00110458,0.01757706,-0.06812833,-0.02945299,-0.04547542,-0.03919667,-0.04177187,-0.0143006,0.09480176,-0.06789722,0.01582598,0.01544257,0.0167034,0.03620905,0.03036419,0.06066762,-0.0349122,-0.03992948,0.05111452,0.00146865,-0.02663311,-0.02473897,0.06119552,0.07837501,-0.08533458,0.02292997,0.02530033,-0.01678788,0.01670859,0.0166399,0.003068,0.01183429,-0.03078927,-0.20250213,-0.01168083,-0.02538023,0.0042206,0.00847452,-0.00739709,0.01225344,-0.0475006,0.01841193,0.07166885,0.11408477,-0.05499284,-0.02071305,0.0467079,-0.01625955,0.00314579,-0.08817945,-0.06515092,-0.07935053,0.08992446,0.01178876,-0.01067484,-0.06459527,-0.02395769,0.05675619,-0.0340549,0.10058152,-0.01755831,-0.00122418,-0.00775634,0.09921136,-0.03540998,-0.02704894,-0.01154049,0.01520055,0.01948009,-0.03440918,-0.00814261,-0.00779194,0.05247873,-0.09886739,0.0466309,0.00326737,-0.09870133,-0.04123332,0.04218228,0.01762273,0.00285718,0.00109695,0.05621116,0.05829959,-0.00878213,0.0349931,0.02565574,0.09381844,-0.03802105,-0.03983045,0.02143907,-0.04151324,0.05949897,-0.02497346,-0.09798824,0.03073396,0.02352756,0.06237711,0.00771361,0.00922829,0.01800073,0.05088709,-0.01518999,-0.01198596,0.07053588,-0.00887571,0.06281708,-0.0366416,0.01440719,0.10527032,-0.04151003,-0.00358869,-0.00816609,-0.00046816,-0.02643417,0.05563172,0.05865327,0.08031448,0.01009459,0.0804355,0.04840479,0.01622091,-0.00682891,-0.00188349,-0.01501764,-0.02125609,0.0365675,0.00869774,0.02650041,-0.26133826,0.03869637,-0.02352952,0.04487542,-0.0439839,-0.02339198,0.01924238,-0.01894968,0.00768626,0.00224997,0.05206478,0.02065009,0.03706968,-0.06249925,0.02723654,-0.04078688,0.02474437,-0.00627856,0.06408347,0.02426282,0.07121429,0.03794821,0.23232809,-0.02645721,0.02464145,0.03310686,0.03604559,0.00229899,0.03058267,0.01150251,0.01553586,0.01381508,0.07704302,-0.03482328,-0.05615028,0.02662874,-0.01286382,0.01467955,0.01188515,-0.01252412,-0.02726359,-0.00039561,-0.05028318,-0.03474392,0.12744276,0.04892959,-0.0394148,-0.07908832,0.06185981,0.05835916,-0.08592971,-0.02549598,-0.08013529,-0.02548371,0.01096833,0.03585645,0.02650686,0.01758827,0.02014915,0.01402797,0.02885997,-0.00683141,0.03348393,0.03151108,0.01810191],"last_embed":{"hash":"2nz9dl","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"2nz9dl","at":1753495683592},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>","lines":[200,217],"size":2440,"outlinks":[{"title":"**Software Utilities for Lottery**","target":"https://saliu.com/software-lotto-lottery.html","line":6},{"title":"Work with Excel spreadsheets for lotto software to generate lottery systems, strategies.","target":"https://saliu.com/HLINE.gif","line":10},{"title":"Theory of Probability Book founded on mathematics of lotto, lottery, Excel spreadsheets.","target":"https://saliu.com/probability-book-Saliu.jpg","line":12},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":12},{"title":"LotWon is lotto software to apply Excel spreadsheets to lottery systems.","target":"https://saliu.com/HLINE.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07500035,-0.03236357,-0.00914763,-0.10503945,-0.05778553,0.05318642,-0.0052134,-0.02806817,0.08523413,-0.01674567,0.04652349,0.00582076,0.02743598,-0.03748723,-0.04387592,-0.01600753,0.00405003,-0.01113453,-0.07711127,-0.02164684,0.09558288,-0.04138005,-0.05064692,-0.10999531,0.03556399,0.02419671,-0.0335316,-0.05554754,-0.04978259,-0.22967906,0.05342323,0.03108779,0.04001006,-0.03948301,-0.06377588,-0.04686456,-0.04224288,-0.00152546,-0.05727245,0.01036612,0.01849431,0.02856886,0.00658982,-0.02688392,0.03478235,-0.06410199,0.02774727,0.01815351,0.05421738,0.00739075,-0.05390828,-0.0069011,0.01201946,0.06705862,0.07032908,0.00136184,0.03793783,0.05426681,0.03786184,0.05813373,0.04260867,0.08101178,-0.1793576,0.06960896,0.02808895,-0.00099512,-0.04432074,-0.02890992,0.01727341,-0.00781976,-0.02377378,0.03607135,0.00121639,0.08857495,0.02387842,-0.05283614,-0.07378684,-0.04616979,-0.06249484,-0.00299607,-0.04118079,-0.02975703,0.01371601,-0.02258352,0.00367785,0.01992147,0.0917092,0.01149806,0.08198903,-0.01973729,0.01319939,0.05874563,0.01035376,0.03541788,0.01348746,-0.00033232,0.04828846,-0.01825807,-0.01771132,0.11917292,-0.03570696,-0.02349219,-0.0249052,0.00431009,0.03839221,-0.0780376,0.00287275,-0.01628664,-0.04434926,-0.00946159,0.0381304,-0.01312261,0.01907568,-0.04651999,-0.04698591,-0.00050835,-0.03877727,-0.03840097,0.01881585,0.01168824,-0.03574697,0.01257081,-0.01560969,0.03298146,0.00704983,0.01476515,0.05453852,0.05475675,0.02113278,0.03349246,0.06350321,0.00793479,-0.11987722,-0.03188413,-0.00115789,0.0184156,0.01778864,-0.02915544,-0.01076859,0.00861377,-0.00174274,-0.00979263,0.04944096,-0.10500762,0.00382708,0.10312469,-0.01218252,0.00818762,0.02227969,-0.04483148,0.01303667,-0.01228247,-0.0291051,-0.06230321,0.02877266,0.05035279,0.05963245,0.06418838,-0.07414532,-0.05147653,-0.06114103,-0.03175525,0.0174521,0.09830444,-0.01115772,-0.0818007,-0.01917881,0.01574476,-0.0282542,-0.09294847,-0.01666758,0.02991739,-0.06047852,0.01335237,0.05007989,-0.01847913,-0.04921866,-0.04904091,0.01674748,-0.00366555,0.0250448,-0.01172171,-0.02846862,0.0177109,-0.05306546,-0.05956782,0.01061336,-0.01790695,0.02281766,0.06083684,-0.00046986,0.0174839,-0.07019436,-0.0289776,-0.04542951,-0.03801509,-0.04319295,-0.01492111,0.09482416,-0.06718615,0.01654726,0.01558226,0.0173052,0.03584464,0.03112219,0.06049632,-0.03390662,-0.0385737,0.04962221,-0.00129186,-0.02797395,-0.02438931,0.06225675,0.07559191,-0.08478298,0.02001941,0.02658389,-0.0181198,0.01803614,0.01753436,0.00378426,0.01260045,-0.03070033,-0.20233876,-0.01324994,-0.02566884,0.00406872,0.00977044,-0.0069921,0.01007864,-0.0485752,0.01797011,0.07053693,0.11265256,-0.0536084,-0.02098057,0.05036156,-0.01554969,0.00237101,-0.085972,-0.06327518,-0.08105179,0.08906023,0.01041212,-0.01147409,-0.06161566,-0.02301021,0.05758388,-0.03370355,0.09948952,-0.01841187,-0.00076007,-0.0070299,0.09848782,-0.03640452,-0.02701889,-0.01299946,0.01666983,0.0194703,-0.03338243,-0.00911278,-0.00643337,0.05092995,-0.09863521,0.04784995,0.00467274,-0.09928697,-0.04230279,0.04463975,0.01942729,0.00276447,0.00131826,0.05623206,0.05814594,-0.01015445,0.0348998,0.02524634,0.09412431,-0.03811757,-0.03897128,0.02187479,-0.04339864,0.05780274,-0.02583233,-0.09980819,0.03238662,0.02392348,0.06349897,0.00732118,0.00817991,0.01834137,0.04964999,-0.01464441,-0.01198843,0.07169285,-0.00754227,0.06192087,-0.03703125,0.01341363,0.10443088,-0.0418328,-0.00401379,-0.00914418,-0.00051395,-0.02656335,0.05577178,0.05712152,0.08330154,0.01043851,0.08149288,0.04674785,0.01589476,-0.00853219,-0.00024137,-0.01650093,-0.02112691,0.03797021,0.00950803,0.02597947,-0.26264751,0.03786046,-0.02543749,0.04340377,-0.04476316,-0.02269867,0.01999647,-0.01885201,0.00599483,0.00300997,0.05248841,0.0202386,0.03724526,-0.06180048,0.02821343,-0.03868102,0.02477957,-0.00655272,0.06391565,0.02367476,0.07198392,0.0372231,0.23337591,-0.02701932,0.02512185,0.03333904,0.03617205,0.00345819,0.03160997,0.01034036,0.01591896,0.0144843,0.07676606,-0.03540224,-0.05712309,0.02860289,-0.01211911,0.01315441,0.01224134,-0.01347324,-0.02736091,0.00025982,-0.04872907,-0.03235703,0.12680456,0.04757066,-0.03933221,-0.07935388,0.06291368,0.058171,-0.08207506,-0.02407722,-0.07890676,-0.02431712,0.01313833,0.03465861,0.02537717,0.01632238,0.01986144,0.0150941,0.0276947,-0.00621313,0.03076815,0.03341731,0.01802095],"last_embed":{"hash":"xiwiyi","tokens":454}}},"text":null,"length":0,"last_read":{"hash":"xiwiyi","at":1753495683726},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>6. Resources in Lotto, Lottery: Software, Spreadsheets, Systems, Strategies</u>#{1}","lines":[202,217],"size":2353,"outlinks":[{"title":"**Software Utilities for Lottery**","target":"https://saliu.com/software-lotto-lottery.html","line":4},{"title":"Work with Excel spreadsheets for lotto software to generate lottery systems, strategies.","target":"https://saliu.com/HLINE.gif","line":8},{"title":"Theory of Probability Book founded on mathematics of lotto, lottery, Excel spreadsheets.","target":"https://saliu.com/probability-book-Saliu.jpg","line":10},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":10},{"title":"LotWon is lotto software to apply Excel spreadsheets to lottery systems.","target":"https://saliu.com/HLINE.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08303456,-0.01693769,-0.0236333,-0.04858773,-0.05366037,0.05104201,-0.0497079,-0.01708044,0.03891015,-0.00086845,0.02416825,-0.01282112,0.0395964,-0.05007081,-0.03171498,-0.03917767,0.01422745,0.02185808,-0.04686678,-0.01086357,0.06140412,-0.02397845,-0.07482455,-0.10040136,0.0244541,0.03706966,-0.03475117,-0.05722166,-0.02674409,-0.19721559,0.0282475,0.02506702,-0.00352218,-0.04679409,-0.05439052,-0.00702506,-0.02460551,0.02398744,-0.05449089,0.02874839,0.03151051,0.01115978,0.02396835,0.00902816,0.0471568,-0.05815827,0.02507925,0.02694378,0.0712401,-0.00456528,-0.09196219,-0.0188598,0.0226262,0.0222044,0.05771775,0.02222716,0.04591938,0.09223317,0.01357411,0.04683327,0.02587684,0.08960914,-0.18233101,0.09001362,0.0118996,0.01863654,0.00019619,-0.01347316,0.0333161,0.01168818,-0.01325532,0.02006375,-0.00722363,0.07732991,0.03729584,-0.02722731,-0.06277656,-0.05513052,-0.04806675,0.01389691,-0.05729194,0.01141873,0.02078363,0.02507254,-0.02958813,0.06331762,0.06155229,0.03288829,0.11010528,-0.07013386,0.00494062,0.03953868,0.05110736,0.04292294,-0.00400153,-0.02615296,0.07001562,-0.02673801,-0.0109553,0.12004171,-0.0091642,-0.01383657,0.02186662,-0.01311953,0.02225075,-0.07807261,0.00628455,-0.0348006,-0.04824345,0.02717041,0.0517695,-0.00237218,0.00347733,-0.04850611,-0.0503604,-0.03756303,-0.03624107,-0.03821339,0.01756172,0.00056204,-0.04492648,-0.02188763,0.01816379,0.02020983,-0.01574288,-0.00331501,0.02655973,0.06394912,0.00234371,0.01831806,0.05978137,0.06766547,-0.14519732,-0.03930787,0.00125628,-0.00912621,0.03123635,-0.02376277,0.01015494,0.01161166,-0.01595389,-0.03776929,0.04422208,-0.10582141,-0.04780781,0.08245636,-0.00390627,-0.01164972,0.00070851,-0.03480482,0.02813738,-0.0273974,-0.0069088,-0.06004062,0.03556807,0.01655261,0.12201303,0.06622688,-0.04992681,-0.00372204,-0.04669013,-0.04769294,-0.02474794,0.14874405,-0.01051537,-0.12041193,-0.01186066,0.0424489,-0.01973423,-0.10260129,0.00152054,-0.01705016,-0.07398514,0.02982669,0.10645315,0.01799699,-0.04414818,-0.04881487,-0.00007924,-0.01879987,0.02853034,-0.0659891,-0.02410094,0.01993765,-0.03266511,-0.09334265,-0.02735546,-0.02173541,0.00073235,0.06647536,-0.00288322,-0.00917049,-0.04746682,-0.01948779,-0.06023885,-0.02559446,-0.05415483,-0.05258076,0.07103072,-0.03377161,0.02389861,0.00808799,0.00312146,0.02636754,0.01086368,0.01759871,-0.02939759,-0.05654634,0.08015458,0.0254061,-0.0489902,0.01535664,0.05180516,0.05918786,-0.055239,0.05983884,0.01935258,0.00077118,-0.02090079,0.0315536,-0.00349578,0.02023831,-0.07655037,-0.18163039,-0.01578248,-0.0408746,-0.00852819,-0.01824599,-0.01476239,0.04737649,-0.03742998,-0.01631232,0.07546426,0.1302674,-0.0500921,0.01122842,0.03712062,-0.00702448,-0.00903949,-0.06145198,-0.05544898,-0.04778111,0.03711994,0.01191489,-0.01354554,-0.04080475,-0.0732976,0.01413923,-0.0314835,0.10376347,-0.02798849,0.0123378,0.00589643,0.10600524,0.0096286,-0.02839891,-0.04679566,0.00283993,0.04358733,-0.04372819,0.01157135,-0.02378469,0.02018596,-0.07235342,0.03183858,0.01586251,-0.07155766,-0.02767962,0.00797433,0.01661193,-0.00518606,-0.00108197,0.04738897,-0.0006434,0.01377435,0.04698056,0.03558145,0.04765619,-0.02073637,-0.02649389,0.01864147,-0.03966313,0.04022402,-0.04548991,-0.07828371,0.0220684,-0.00665742,0.04741045,0.00059232,0.0000303,-0.0426735,0.03392449,0.01222429,0.01743195,0.08551724,0.01004689,0.05787287,-0.02853099,0.02242006,0.08992977,-0.02882755,0.00823128,-0.00033317,-0.07828016,-0.04882477,0.05606439,0.07588225,0.07607539,0.05668999,0.06660008,0.01753257,0.00587985,0.00845934,0.02631696,-0.00720447,-0.01320423,0.02550968,0.03101261,0.05692896,-0.26577756,0.0320333,-0.02049088,0.05059743,-0.03055353,-0.02478518,0.02479058,-0.03120628,0.02357143,-0.02425507,0.06499869,0.02082438,0.01546913,-0.05253122,-0.00532287,-0.02942675,0.04730291,-0.025273,0.0655647,0.04194136,0.02556799,0.02462565,0.2455285,0.02276018,0.01520874,0.03851677,0.0014633,-0.00002097,0.02404329,0.04065664,0.02124639,0.0242582,0.07001095,0.0125252,-0.04497211,0.03395347,-0.04979958,0.03700403,0.01734392,0.0075381,-0.07104576,0.00755502,-0.03226494,0.00785997,0.09475018,0.04923556,-0.01926485,-0.08766869,0.04156198,0.04360583,-0.06142249,-0.04384154,-0.10809148,-0.01729682,-0.00274662,0.02355103,0.0150291,-0.02304794,0.0176286,0.0107011,0.06900264,-0.03320444,0.06325788,0.02748232,0.0076051],"last_embed":{"hash":"sot73y","tokens":409}}},"text":null,"length":0,"last_read":{"hash":"sot73y","at":1753495683875},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>","lines":[218,243],"size":2830,"outlinks":[{"title":"_**Lottery Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":5},{"title":"_**Lotto, Lottery Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":6},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":8},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":10},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":11},{"title":"_**Genuine Powerball, Mega-Millions strategy, system**_","target":"https://saliu.com/powerball-systems.html","line":12},{"title":"**Lottery Skips Systems**","target":"https://saliu.com/skip-strategy.html","line":14},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":15},{"title":"_**Lottery filters, lotto filtering in software**_","target":"https://saliu.com/filters.html","line":16},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":17},{"title":"_**Starting Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":18},{"title":"_**Best Lottery Software, Lotto Software**_","target":"https://saliu.com/infodown.html","line":19},{"title":"Thanks for visiting the site of lotto software, lottery software, Excel spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":21},{"title":"Forums","target":"https://forums.saliu.com/","line":23},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":23},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":23},{"title":"Contents","target":"https://saliu.com/content/index.html","line":23},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":23},{"title":"Home","target":"https://saliu.com/index.htm","line":23},{"title":"Search","target":"https://saliu.com/Search.htm","line":23},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":23},{"title":"Lotto players use Excel spreadsheets to analyze or program lottery software.","target":"https://saliu.com/HLINE.gif","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05679771,-0.03759819,0.00163431,-0.03637299,-0.04580092,0.01644233,-0.01024383,-0.02139955,0.05385451,0.00089708,0.02104426,0.00411678,0.0483328,-0.03140379,-0.02580292,-0.01993708,0.01779268,0.0477347,-0.04417446,-0.01684175,0.0546708,-0.00021465,-0.09565216,-0.11680391,0.03170127,0.05053385,-0.05705928,-0.0797019,-0.01206222,-0.13565291,0.0205616,0.02051768,-0.01999042,-0.04339958,-0.05345293,-0.02991404,-0.01771958,0.00673709,-0.09172457,0.0332128,0.03190005,0.02817394,0.0199819,0.00045309,0.01420277,-0.04748276,0.031769,0.03196769,0.07338047,0.00999525,-0.04820585,0.01296779,0.0131716,0.0285278,0.02073279,0.01443825,0.03236579,0.08849923,-0.01229403,0.03672764,0.02071177,0.07218503,-0.1583451,0.05896889,0.00914683,0.04630927,0.01222095,-0.01521564,0.03136636,0.03119265,0.00774929,0.01391077,0.00569763,0.06272011,0.04583519,-0.03403023,-0.04955496,-0.05757184,-0.04031471,0.01222992,-0.0738809,0.00418844,0.01767894,0.04333745,-0.04625112,0.04664412,0.05824442,0.00739444,0.13036843,-0.08010314,-0.00761345,0.04870095,0.04240341,0.05175118,0.01744539,-0.04477752,0.06525072,-0.02267733,0.01580166,0.15979916,-0.05405362,-0.02921918,0.03959372,-0.04492968,0.01826177,-0.06979071,-0.0239455,-0.02420514,-0.05828432,0.03807082,0.05565924,0.00256078,0.02344533,-0.06094845,-0.04714137,-0.04812023,-0.04521481,-0.02387884,0.02525379,-0.00740796,-0.04844568,-0.01290701,-0.00319939,0.00858179,-0.00410255,-0.01490575,0.0317122,0.08222181,-0.03047915,0.00418998,0.06425246,0.03016997,-0.13226876,-0.0241059,-0.00119558,-0.03045144,0.03754696,-0.02409049,-0.01382494,0.038825,-0.02182261,-0.00954201,0.08221868,-0.12474551,-0.03271162,0.07869771,-0.00757914,0.00537179,-0.00351181,-0.03076243,0.01502991,-0.02619158,-0.01068913,-0.06974892,0.02477222,0.00508957,0.11347139,0.01884444,-0.03133836,0.01795488,-0.05693491,-0.0283013,-0.02038808,0.13428938,-0.03360976,-0.10216078,-0.02038329,0.04244395,-0.02289821,-0.0844318,-0.00951837,0.00518465,-0.0868756,0.01573632,0.11847178,0.0050614,-0.08656126,-0.03292414,0.00920786,-0.03107948,0.04329998,-0.04750916,-0.01215575,0.01036318,-0.0212039,-0.07074837,-0.03838185,-0.01700153,0.00117626,0.06422312,0.00784182,0.02057017,-0.04755154,-0.03669206,-0.05177926,-0.02556197,-0.07482935,-0.02757651,0.07489433,-0.02122546,0.00620206,0.0057792,0.02290771,0.04293967,0.01436713,0.02015152,-0.01669255,-0.06369271,0.06305473,0.05306534,-0.0741521,0.00054138,0.05877622,0.06019488,-0.0705472,0.04640334,0.02256874,0.01914966,-0.02799062,0.02313013,-0.01555685,0.03387899,-0.06020044,-0.17945854,-0.02178572,-0.00894353,-0.00007947,0.00300809,-0.00111481,0.01606392,-0.02301703,-0.00950283,0.09673213,0.11868887,-0.07621521,0.02762955,0.03913416,-0.01984259,-0.0130116,-0.06074481,-0.04456709,-0.02840961,0.01924193,0.01904904,-0.03692295,-0.00951437,-0.10167132,0.01217074,-0.00661126,0.11651319,-0.04477886,0.04973328,0.00962463,0.07901246,-0.02117948,-0.02560562,-0.05158509,0.01362053,0.05655306,-0.05267739,-0.01562968,-0.00313886,0.03000761,-0.06357295,-0.00386252,0.03008284,-0.0695891,-0.00529868,0.00508128,0.00564001,-0.01956692,0.01659537,0.06021655,-0.00731603,0.0135641,0.05508496,0.05244827,0.05648055,-0.01517245,-0.03200113,0.01882993,-0.03916555,0.04591931,-0.0310635,-0.08614521,-0.00686411,0.01152653,0.06077536,0.00763669,-0.02078849,-0.05400059,0.02543383,-0.02865993,0.01870729,0.07449356,-0.01533172,0.04644099,-0.02641249,0.02846613,0.11836858,-0.05332135,-0.00563205,0.01442723,-0.0533327,-0.06578667,0.06574038,0.06837563,0.06167927,0.05049406,0.07375643,0.05027134,-0.02585351,-0.00689215,0.0303738,-0.00792784,-0.02459894,0.00720202,0.0209896,0.03628611,-0.25072995,0.03608553,-0.01512315,0.0504692,-0.03861138,-0.05997678,0.02211003,-0.01069964,0.01406658,-0.02262344,0.07155303,0.00123677,-0.00233388,-0.04724303,0.02507845,-0.02064829,0.06864483,-0.04120708,0.0689524,0.0653292,0.03598248,0.05124897,0.24907443,0.02500908,0.05075731,0.04038129,-0.01323535,0.00757971,0.01255573,0.03503034,0.03310497,0.00982728,0.0521252,-0.00270003,-0.05272017,0.02759763,-0.02816197,0.03951073,0.00427134,0.03082267,-0.07231718,0.00250479,-0.05803217,0.01030265,0.08939598,0.03248514,-0.02429699,-0.09814951,0.04168226,0.04467939,-0.07900856,-0.01781472,-0.09755249,-0.05003852,0.01545308,0.01769315,0.01687559,-0.02243188,0.02105926,-0.00616009,0.04201608,-0.05330068,0.03990141,0.03575975,-0.00035858],"last_embed":{"hash":"1m22jsz","tokens":88}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753495684003},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{4}","lines":[224,224],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{17}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08383654,-0.0278503,-0.00749162,-0.06221452,-0.002976,0.04049702,-0.01748613,0.00857808,0.04178158,-0.0247665,0.00790725,-0.02313078,0.03344229,-0.03438955,-0.02870312,-0.05628094,-0.00594136,0.02870051,-0.04673014,-0.00666451,0.05221792,0.0091409,-0.0453053,-0.10007833,0.01840874,0.01177194,-0.03779836,-0.07650457,-0.04026155,-0.18523438,0.02932415,0.00800491,-0.00933089,-0.0200325,-0.06720675,0.00329364,0.00711982,0.01129484,-0.02512981,0.05615353,0.03853338,0.0005168,0.00928204,0.00955303,0.02435671,-0.03711627,0.00132012,0.01899772,0.05046946,0.01140273,-0.09327861,-0.01515806,0.03102634,0.00208513,0.050024,0.02207112,0.04058227,0.08027165,0.01721808,0.06007728,0.03754972,0.07501661,-0.2098908,0.0662594,0.01343079,0.01390336,-0.03067247,-0.02651712,0.03126213,0.00130644,-0.0150069,0.02946166,-0.00560792,0.06963602,0.06119177,-0.06663182,-0.06566535,-0.07419099,-0.05203119,0.00919359,-0.06045027,0.0315198,0.00403488,0.0071178,0.00219608,0.05328109,0.04535856,0.03755847,0.11161584,-0.06052631,0.01884148,0.04028553,0.03992791,0.05810675,0.00120421,-0.03850823,0.06086331,-0.0221629,0.00232272,0.14251401,-0.03413719,-0.02232655,0.02322714,0.00503633,0.03936118,-0.05260759,-0.02278739,0.00046119,-0.0279672,0.02480383,0.0413453,0.00258727,-0.01917814,-0.06063093,-0.0717449,-0.04780158,-0.02966219,-0.04137914,0.01447109,0.01584226,-0.07292731,-0.0351228,0.01612389,0.01024926,0.00550782,-0.01725962,0.0502387,0.05912165,0.00616788,0.04116147,0.06317292,0.04494077,-0.15096213,-0.05880843,-0.0164325,0.0015645,0.02444614,-0.04011949,0.00561506,0.0111071,-0.01123255,-0.03380163,0.05472711,-0.08587959,-0.03961951,0.08882343,-0.01011914,0.00389698,0.00203437,-0.04623561,0.02079829,-0.02329492,-0.00092903,-0.03688634,0.03063178,0.01132507,0.1240335,0.07407086,-0.03851952,0.02336627,-0.03923786,-0.064588,0.00025925,0.1305898,0.00012209,-0.09229114,-0.0240387,0.04049432,-0.01395746,-0.09680382,0.02523666,-0.01510777,-0.06395739,0.00931217,0.06864361,0.01253522,-0.03518058,-0.04685568,0.01166284,-0.01390141,0.01875516,-0.05056961,-0.02060553,0.04503553,-0.05719687,-0.11029194,0.00957399,-0.03305087,-0.01465851,0.06030666,-0.00297977,-0.02694324,-0.07454958,-0.02535822,-0.04079298,-0.01855884,-0.06528016,-0.03750388,0.07518928,-0.02030125,0.02162731,0.00611599,0.0189322,0.02648957,0.02003455,0.02467399,-0.03816789,-0.04936052,0.06058395,0.01736161,-0.03746548,0.04255967,0.04797686,0.05281886,-0.05986031,0.04871498,0.02179927,-0.00635315,0.00074736,0.03284205,0.00549509,0.03677025,-0.06462175,-0.20699207,0.00307042,-0.04501703,-0.01525081,-0.03039663,-0.02752263,0.04176449,-0.03662015,-0.00625647,0.08299169,0.12096145,-0.04545039,0.02989722,0.00403107,0.00045994,-0.00368254,-0.05900033,-0.06259183,-0.04511762,0.04707839,0.00106076,-0.02260864,-0.0373418,-0.07082672,0.02328068,-0.03479856,0.10926329,-0.00170526,0.01808199,-0.01044219,0.10489617,0.00131995,-0.01654685,-0.06952008,0.01836175,0.05222593,-0.0587028,0.02125567,-0.01363039,0.02201871,-0.0946345,0.05154611,0.02398849,-0.05185077,-0.01372079,0.02628623,0.03333378,-0.05498081,-0.00215467,0.03849249,0.02406773,-0.01422194,0.03898568,0.05178432,0.06352815,-0.03811132,-0.02367955,0.02489829,-0.04502891,0.03099646,-0.04354792,-0.0742873,0.05539364,-0.01432813,0.04400552,-0.00018607,-0.0044139,-0.05188195,0.03137917,0.01032613,0.01840159,0.08908531,-0.01818351,0.04822666,-0.02676551,0.02997874,0.06367105,-0.04016594,-0.00498119,0.01886679,-0.03852209,-0.08717716,0.06838494,0.02269365,0.07315245,0.05168037,0.06132489,0.0173768,0.02952492,0.01274156,0.03793664,-0.01848005,-0.01308573,0.04031333,0.04076366,0.05402315,-0.24947079,0.02950611,-0.02016809,0.03902298,-0.02064262,-0.02536392,0.03035662,-0.04218468,0.01441468,-0.01494559,0.10634406,0.01253441,-0.00009214,-0.07385336,-0.01593382,-0.00735759,0.05159127,-0.01355918,0.07844398,0.0357065,0.03179479,0.03502819,0.25546333,0.03316606,0.00251709,0.01894053,0.00312999,-0.01526138,0.03757988,0.02995225,0.02178754,0.00935795,0.07331149,-0.01542764,-0.04380348,0.02010646,-0.03433875,0.02393341,0.02583679,-0.00006345,-0.0531879,0.023177,-0.05738427,-0.02132528,0.09420162,0.05444121,-0.01282328,-0.06486802,0.04043918,0.02176255,-0.0546194,-0.06685092,-0.09311645,-0.00908852,0.01032171,0.01585628,-0.0049058,-0.00740102,0.02275106,0.01063078,0.08030482,-0.0207706,0.04345777,0.02088895,0.02485563],"last_embed":{"hash":"1wy5v2n","tokens":293}}},"text":null,"length":0,"last_read":{"hash":"1wy5v2n","at":1753495684030},"key":"notes/saliu/Excel Spreadsheets, Lottery Software, Lotto Programming.md#Excel Spreadsheets, Lottery Software, Lotto Programming#<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>#{17}","lines":[238,243],"size":636,"outlinks":[{"title":"Thanks for visiting the site of lotto software, lottery software, Excel spreadsheets.","target":"https://saliu.com/images/lotto-software.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Lotto players use Excel spreadsheets to analyze or program lottery software.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
