
"smart_sources:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md": {"path":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12600976,-0.0339968,-0.01735707,-0.0043092,-0.01619632,0.06383676,-0.00099707,-0.02027833,0.02978964,0.00899096,0.00506307,-0.00547222,0.0652909,-0.00740483,-0.0382664,-0.02334903,-0.01995971,-0.01026842,-0.04523467,-0.00012911,0.02840276,-0.03047789,-0.01241218,-0.08775487,0.04804374,0.00418361,-0.02456932,-0.05418415,-0.06412158,-0.23929329,0.00029864,-0.00266994,-0.01613995,-0.05078798,-0.07101363,-0.03450973,-0.01029342,0.09851611,-0.04018725,0.0500903,0.04525103,0.00134996,0.02157951,0.02851161,-0.00848013,-0.05786872,-0.04544134,0.02453331,0.03551669,-0.00548432,-0.07235115,-0.0163373,0.01369384,0.00208789,0.02999853,0.06040438,0.05308396,0.08727732,0.01519161,0.0571275,0.04269689,0.05258368,-0.17668828,0.04361359,-0.0456403,0.0194084,-0.03055235,-0.02385102,0.02157843,0.02923642,-0.03858663,-0.00169,-0.00500673,0.10560516,0.05275376,-0.04417562,-0.02249542,-0.11063077,-0.07862467,0.00556415,-0.04215928,0.02076939,-0.01364598,0.01091703,0.01397145,0.1034971,0.07363341,0.0450431,0.06558877,-0.07808376,0.06564275,-0.03916146,0.0469233,0.03684711,0.00741851,-0.01890884,0.08124051,0.00405122,0.01734974,0.12088411,0.00501286,-0.01970501,0.02381745,0.02973935,0.04304451,-0.02485077,-0.00893013,-0.05455152,-0.00402684,0.03689056,0.04462475,-0.00188655,0.05447194,-0.08755994,0.00511897,0.01909286,-0.02789651,-0.02112111,0.01150654,0.00878593,-0.04780735,0.00437171,-0.000141,-0.02481458,-0.04317817,0.01548868,0.01443298,0.03473423,0.02612294,-0.00146267,0.01026958,0.01720906,-0.13610461,-0.07196652,-0.00548414,-0.00886922,-0.02421163,-0.03637378,0.01414896,-0.01266775,0.0127265,-0.0281349,0.06838992,-0.12137903,-0.01664063,0.08504401,-0.00633107,0.01567799,0.03426438,-0.012877,0.03368944,0.01397111,-0.03518392,-0.05256376,0.0013681,0.01135538,0.09546885,0.07827342,-0.01725062,-0.00076039,-0.01290027,0.00478864,-0.03672301,0.14319569,-0.03191597,-0.10653683,-0.00634471,0.06958441,-0.01022457,-0.06311043,-0.02000161,-0.01011979,-0.00147311,-0.00545036,0.09257315,-0.02360263,-0.06254184,-0.05859282,0.00526842,0.00670006,0.01960239,-0.04812463,-0.09217461,0.05622989,-0.04197044,-0.0622721,0.01156454,-0.05082014,0.01021893,0.09572806,-0.04830793,0.02664316,0.00768932,0.01840624,-0.0241064,0.00467045,-0.06408568,-0.02487218,0.05485256,-0.00988854,0.00889693,-0.00037248,0.01042164,0.01247809,-0.08391414,0.01938671,0.01575975,-0.00787086,0.09113287,0.00959956,-0.03595099,0.02261166,0.05561419,0.04673821,-0.05836798,0.04013306,0.02379045,0.02975175,0.02350156,0.04409438,-0.01285281,0.01772237,-0.04846546,-0.20216751,-0.06307182,-0.04818153,-0.00840533,-0.00656475,0.00087582,0.04933926,-0.01639795,-0.00312985,0.06619145,0.13784504,-0.00176828,-0.03783135,0.02692663,-0.01366025,-0.02645612,-0.06296579,-0.05263879,-0.013556,0.05008659,-0.00948994,0.0067528,-0.01784884,-0.02686201,0.027707,-0.06852038,0.09556479,0.0126817,0.00594719,0.02954744,0.08723678,0.03983001,-0.00153904,-0.0662456,-0.00444206,0.06994334,-0.01830197,-0.00148889,-0.02832506,-0.00183813,-0.07056002,0.03126066,0.01275122,-0.08256318,-0.00358085,-0.01270117,-0.02429711,-0.02886949,0.02686305,0.01117616,0.01680515,-0.01896317,-0.00288417,0.0121067,0.04702786,-0.03063963,-0.09349409,-0.02471627,0.02539784,-0.02470191,0.00237212,-0.02081065,0.0488782,-0.03619215,0.01769688,0.01343845,0.01402646,-0.01079215,0.02279586,0.03081382,-0.01105072,0.07863505,-0.03775199,0.04435528,-0.02264389,0.05755869,0.02423446,-0.04181381,-0.04186049,-0.01550675,0.03893187,-0.02668113,0.03188879,0.0850831,0.05817128,0.02951794,0.05453223,-0.00176241,0.04394886,0.00294279,-0.00565916,-0.03080725,-0.02424937,0.00348659,0.01337471,0.0420797,-0.27116701,0.01544878,-0.03016801,0.02331325,-0.01516069,0.01084405,0.03332381,-0.01630763,0.04059853,-0.01320724,0.05994438,0.0382083,0.02837153,-0.06025521,-0.01074743,-0.01227528,0.03662029,-0.02855685,0.0914175,-0.00047358,0.02635334,-0.0064256,0.2667813,0.00766707,-0.01714564,0.02332381,-0.00670563,0.02425439,0.02423277,0.05155309,0.00236174,0.03720512,0.05072922,0.00268146,-0.02795946,0.08822425,0.01155594,0.06967612,-0.01362262,0.00815529,-0.0771106,-0.00997294,-0.01548341,-0.01532196,0.08737839,0.0126309,-0.02271232,-0.06332785,-0.0342825,0.03379993,-0.07089172,-0.07505532,-0.06724229,-0.05137385,-0.00520497,0.02674344,0.03151425,-0.00593495,0.02316769,-0.00095344,0.0671746,-0.02338539,0.05587246,0.03945304,0.00798568],"last_embed":{"hash":"13v3od1","tokens":500}}},"last_read":{"hash":"13v3od1","at":1753423450990},"class_name":"SmartSource","last_import":{"mtime":1753363612439,"size":26539,"at":1753423416052,"hash":"13v3od1"},"blocks":{"#---frontmatter---":[1,6],"#Cross-Reference Lottery Strategies, Lotto Strategy Files":[8,219],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#{1}":[10,13],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u><i>Cross-Reference, Combine</i> Lottery Strategy Files Created by Various Types of Lottery Software</u>":[14,15],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#By Ion Saliu, ★ _Founder of Lottery Strategizing Science_":[16,25],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#By Ion Saliu, ★ _Founder of Lottery Strategizing Science_#{1}":[18,25],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>":[26,56],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{1}":[28,28],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{2}":[29,29],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{3}":[30,30],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{4}":[31,32],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{5}":[33,56],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>":[57,129],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#{1}":[59,66],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>A. Manual Operation: <i>S = Screen Prompt</i></u>":[67,92],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>A. Manual Operation: <i>S = Screen Prompt</i></u>#{1}":[69,92],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>":[93,118],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{1}":[95,100],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{2}":[101,101],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{3}":[102,102],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{4}":[103,103],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{5}":[104,104],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{6}":[105,105],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{7}":[106,106],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{8}":[107,107],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{9}":[108,108],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{10}":[109,110],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{11}":[111,118],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>":[119,129],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{1}":[121,121],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{2}":[122,122],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{3}":[123,123],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{4}":[124,124],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{5}":[125,125],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{6}":[126,126],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{7}":[127,127],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>#{8}":[128,129],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>":[130,148],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{1}":[132,138],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{2}":[139,139],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{3}":[140,140],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{4}":[141,142],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{5}":[143,148],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>":[149,176],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#{1}":[151,152],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#<u>Important Update, February 2011</u>":[153,176],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#<u>Important Update, February 2011</u>#{1}":[155,176],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)":[177,219],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{1}":[179,180],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{2}":[181,181],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{3}":[182,182],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{4}":[183,183],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{5}":[184,184],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{6}":[185,185],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{7}":[186,186],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{8}":[187,188],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{9}":[189,190],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{10}":[191,191],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{11}":[192,192],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{12}":[193,193],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{13}":[194,194],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{14}":[195,195],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{15}":[196,196],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{16}":[197,197],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{17}":[198,198],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{18}":[199,199],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{19}":[200,200],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{20}":[201,201],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{21}":[202,202],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{22}":[203,203],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{23}":[204,204],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{24}":[205,205],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{25}":[206,206],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{26}":[207,207],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{27}":[208,208],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{28}":[209,209],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{29}":[210,210],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{30}":[211,211],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{31}":[212,213],"#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{32}":[214,219]},"outlinks":[{"title":"Analyze advanced tips on creating lotto, lottery strategy files, strategies.","target":"https://saliu.com/HLINE.gif","line":18},{"title":"Combine lottery strategies, lotto strategy files in MDIEditor lotto software and Lotwon.","target":"https://saliu.com/ScreenImgs/lottery-strategies.gif","line":33},{"title":"When multiple lottery strategies hit the odds are reduced tremendously.","target":"https://saliu.com/ScreenImgs/lottery-strategies-hits.gif","line":37},{"title":"Playing the lottery is a lot more effective when combining many strategies.","target":"https://saliu.com/ScreenImgs/lottery-strategies-skips.gif","line":43},{"title":"Cross-reference files show parameters in different strategy files for the same draw.","target":"https://saliu.com/ScreenImgs/cross-reference-data.gif","line":51},{"title":"_**INP-FILE.TXT**_","target":"https://saliu.com/pub/INP-FILE.TXT","line":61},{"title":"All lotto programs and lottery software by Grand Master Programmer work great together.","target":"https://saliu.com/ScreenImgs/cross-reference-input.gif","line":63},{"title":"Software for lotto, lottery to cross-reference strategy files created by MDIEditor, DOS programs.","target":"https://saliu.com/ScreenImgs/reference-strategies-file.gif","line":97},{"title":"_**HeaderFilterFiles.zip**_","target":"https://saliu.com/pub/HeaderFilterFiles.zip","line":99},{"title":"_**Lottery Strategy, Systems, Software Based on _Lotto Number Frequency_**_","target":"https://saliu.com/frequency-lottery.html","line":111},{"title":"Combine many lottery strategies and the player increases the chance to win big.","target":"https://saliu.com/ScreenImgs/cross-reference-report.gif","line":115},{"title":"_**Notepad++ text and programmer's editor**_","target":"https://notepad-plus-plus.org/","line":137},{"title":"Select columns in lottery strategy report files with Notepad++ editor to sort them easily.","target":"https://saliu.com/ScreenImgs/column-select.gif","line":145},{"title":"Lottery software sorts numeric columns in ascending or descending order to discover strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":157},{"title":"The best sorting software for lottery data, results files is named Sorting.","target":"https://saliu.com/ScreenImgs/sort-column.gif","line":161},{"title":"<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":177},{"title":"_**Lotto, Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":181},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":183},{"title":"_**User's Guide to**_ **MDIEditor And Lotto WE**","target":"https://saliu.com/MDI-lotto-guide.html","line":185},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":187},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":191},{"title":"_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge","target":"https://saliu.com/bbs/messages/623.html","line":192},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":193},{"title":"_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_","target":"https://saliu.com/bbs/messages/569.html","line":194},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":195},{"title":"**Vertical or Positional** _**Filters In Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":196},{"title":"_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_","target":"https://saliu.com/bbs/messages/896.html","line":197},{"title":"**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_","target":"https://saliu.com/bbs/messages/919.html","line":198},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":199},{"title":"_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":200},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":201},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":202},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":203},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":204},{"title":"**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_","target":"https://saliu.com/decades.html","line":205},{"title":"_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":206},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":207},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":208},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":209},{"title":"_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_","target":"https://saliu.com/STR30.htm","line":210},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":211},{"title":"**<u>Lottery Strategy Software</u>**","target":"https://saliu.com/infodown.html","line":212},{"title":"Special software checks lottery strategies between several programs, apps.","target":"https://saliu.com/HLINE.gif","line":214},{"title":"Forums","target":"https://forums.saliu.com/","line":216},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":216},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":216},{"title":"Contents","target":"https://saliu.com/content/index.html","line":216},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":216},{"title":"Home","target":"https://saliu.com/index.htm","line":216},{"title":"Search","target":"https://saliu.com/Search.htm","line":216},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":216},{"title":"The lottery cannot be played successfully without software that creates lottery strategies.","target":"https://saliu.com/HLINE.gif","line":218}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["strategy","reference","software","program","programs","lotto","lottery","combine strategies","editor","files"],"source":"https://saliu.com/cross-lines.html","author":null}},"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12944897,-0.05774175,-0.00453616,0.01097144,-0.01986556,0.05389034,-0.0079449,0.00456516,0.02008734,-0.0180352,-0.00224837,-0.00404856,0.0427965,0.00210536,-0.02080837,-0.01966639,-0.00647072,-0.00718382,-0.02378312,0.02143667,0.04735041,-0.00933216,-0.02870741,-0.08648564,0.04889027,-0.00708776,-0.01387414,-0.06791773,-0.0537971,-0.20056231,-0.00471036,0.00183369,-0.03607751,-0.009825,-0.04679826,-0.03351333,-0.00414724,0.10549289,-0.01413984,0.07569551,0.03928433,0.02404245,-0.01998823,0.04376094,-0.0104333,-0.04065572,-0.04277411,0.02098115,0.04827171,2.3e-7,-0.04812897,0.01264234,-0.00519824,-0.00465326,0.04706399,0.04257485,0.06680015,0.0529875,0.03038185,0.05488543,0.00922754,0.03032744,-0.20020089,0.07642863,-0.04693195,0.03387965,-0.02457476,0.00499205,-0.01567671,0.05053712,-0.01456402,0.01178259,-0.00826499,0.08734876,0.07886441,-0.02306413,-0.04705107,-0.08952686,-0.08238537,-0.02099299,-0.0354633,0.00076355,-0.02750538,-0.03127677,0.01276736,0.09820817,0.07597907,0.07323094,0.0658135,-0.05359431,0.05259085,-0.02712882,0.08921069,0.03817296,-0.03545296,-0.02154476,0.07611036,0.00902706,-0.00699127,0.13838658,-0.01885965,-0.0099928,0.03261923,0.04228739,0.03001517,0.00029267,-0.01174656,-0.02707339,-0.00521781,0.03925641,0.02298609,0.00900118,0.06665237,-0.11230794,0.03494425,0.01997539,-0.00050285,-0.02531844,0.02438596,0.01944704,-0.04499092,-0.0176087,-0.01084453,-0.00254331,-0.04583408,0.02393972,0.01554097,0.04047138,0.0280872,0.00050878,0.00135453,0.0208013,-0.11909883,-0.06008335,-0.00870506,-0.00691887,-0.00880702,-0.05150917,0.00435534,-0.03501906,-0.00523558,-0.04926858,0.06024468,-0.113607,-0.02495457,0.07503358,0.03706672,0.01060519,0.03597996,-0.00914806,0.00752481,0.01438096,-0.01089103,-0.03743438,-0.0214221,0.0285286,0.10750652,0.07424419,-0.00199769,0.01515731,-0.00145964,0.00020317,-0.0456968,0.16467017,-0.03726685,-0.13837449,-0.02439087,0.03776945,-0.0056196,-0.08447411,-0.03583638,-0.03168464,-0.02756187,-0.00200969,0.12760092,0.00208263,-0.0470107,-0.04738532,-0.0232465,0.00873201,-0.00638345,-0.03146068,-0.09537702,0.06449545,-0.04705008,-0.05183478,0.00329906,-0.06796825,0.03229769,0.08190735,-0.0596597,-0.01953764,-0.01057578,0.02693911,-0.02665883,0.00961352,-0.02970294,-0.03470473,0.04647217,-0.01087333,0.00282965,0.01312237,-0.01353256,0.00747334,-0.08304711,0.00108873,0.01112132,-0.0330601,0.0602408,0.03372199,-0.01557492,0.04916826,0.05902677,0.01382036,-0.06026868,0.01475067,0.0243181,0.01072015,0.04295918,0.04611023,-0.03489466,0.0067173,-0.05311341,-0.22207789,-0.02741197,-0.0571784,-0.02016229,-0.02291062,-0.00944609,0.03695339,-0.01549133,-0.00657101,0.090537,0.13004634,-0.01034778,-0.0190797,0.00414492,-0.00925209,-0.00429418,-0.04680146,-0.05891911,0.00183888,0.06361387,0.00753087,0.0016566,-0.03381513,-0.04190712,0.02970321,-0.03853675,0.10603245,0.05291545,-0.01610129,0.01531932,0.09780761,0.04001448,-0.0213171,-0.08201699,-0.01276022,0.04152612,-0.01867354,-0.00111338,-0.01808151,-0.02374073,-0.06900907,0.01734491,0.01350921,-0.07328032,-0.00029887,-0.00553435,0.00253604,-0.05778919,0.0277458,0.0126053,0.01967677,-0.03481622,0.00296705,0.03117036,0.06278036,-0.00786236,-0.09184595,-0.03355389,0.00589754,-0.01918964,-0.01000142,0.01252798,0.04151481,-0.03115702,-0.00657256,0.0434005,0.0226917,-0.02853179,-0.01742196,0.02747764,0.02016174,0.04002567,-0.01932843,0.00380057,-0.03443319,0.0198995,0.02251534,-0.01373733,-0.01835685,-0.00389032,0.05722886,-0.04894162,0.0563518,0.07642689,0.04560025,0.05015777,0.00416406,-0.02053638,0.03419734,-0.01778653,0.01184895,-0.00495593,-0.03260516,0.00667546,0.04456013,-0.01305276,-0.26965052,0.01675246,0.00451181,0.03056365,0.01307938,0.03246957,0.01971712,-0.03720407,0.00649161,-0.02863706,0.07991017,0.06865712,0.01782555,-0.03037314,-0.01301898,-0.01050909,0.05576291,-0.03092763,0.05965902,0.03501382,0.02382605,-0.0035276,0.25802255,0.02182789,-0.02254171,-0.0031316,-0.00702163,0.01593576,0.03512408,0.04593184,-0.02306123,0.0216745,0.03798571,-0.00308695,-0.02545108,0.06891771,0.00061183,0.08529146,-0.01469254,0.02449145,-0.11773955,0.00770903,-0.01561698,-0.01994026,0.08234229,0.02051795,-0.03158913,-0.02981541,-0.04890333,0.01054939,-0.05357737,-0.07508239,-0.06374917,-0.02511017,-0.00710032,0.0481989,0.02380397,-0.02861588,0.01628656,0.00080108,0.07264667,-0.02026607,0.04135809,0.01764343,0.03409327],"last_embed":{"hash":"i11l3f","tokens":88}}},"text":null,"length":0,"last_read":{"hash":"i11l3f","at":1753423447718},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#---frontmatter---","lines":[1,6],"size":201,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11868744,-0.02808455,-0.01868249,-0.0067085,-0.0145424,0.05932238,-0.00086409,-0.02715323,0.03412205,0.01284226,0.01027885,-0.00504913,0.07486439,-0.00090423,-0.03127497,-0.01524176,-0.02382513,-0.0116158,-0.04554902,0.00316224,0.02442784,-0.02754122,-0.01622524,-0.08487847,0.04664342,0.00192971,-0.03022955,-0.05961142,-0.06230276,-0.24223933,0.00862184,0.00230503,-0.01914558,-0.0537186,-0.06302748,-0.0437461,-0.00266159,0.09791936,-0.05142697,0.04798594,0.04686536,-0.00640387,0.02251457,0.0255442,-0.01090905,-0.05593709,-0.04259131,0.03890989,0.03414963,-0.00688254,-0.06214679,-0.01858507,0.00611558,-0.00358524,0.01987676,0.05936081,0.04803086,0.09755095,0.0110366,0.0555422,0.0434887,0.06061587,-0.16869137,0.03705877,-0.04584177,0.02135887,-0.0306992,-0.01361316,0.037697,0.02991798,-0.03730202,0.00076374,-0.00580094,0.10826426,0.04573552,-0.04725408,-0.01944988,-0.10752152,-0.07414348,0.02399403,-0.05270622,0.02731558,-0.01277726,0.02226712,0.00585771,0.0972681,0.06949882,0.03246386,0.07134126,-0.07360925,0.06343076,-0.03102311,0.03648812,0.02923382,0.01828556,-0.02461777,0.08320829,-0.00491515,0.03580384,0.13046521,-0.00019207,-0.0224735,0.02606871,0.03108034,0.03308668,-0.03091959,-0.01697817,-0.06188683,-0.01291761,0.03622421,0.0494573,0.001132,0.0470602,-0.08902445,0.00238268,0.01066569,-0.04060471,-0.02493247,0.00781184,0.00474772,-0.05624302,0.01629814,0.0039801,-0.02307224,-0.04142863,0.00856844,0.02374702,0.02923201,0.0222545,0.00202569,0.00914438,0.02499648,-0.1285115,-0.07967602,-0.00707435,-0.00779014,-0.03372198,-0.03309786,0.00907781,-0.0043021,0.01438716,-0.02425868,0.07602069,-0.1201151,-0.00613776,0.07899489,-0.01633872,0.01667047,0.02534581,-0.00455905,0.03873771,0.01114655,-0.04109452,-0.05094448,0.00863537,-0.00243577,0.10117018,0.07600319,-0.02319436,-0.00475152,-0.02121001,0.01185765,-0.02931967,0.14245512,-0.03204231,-0.09671988,0.00192781,0.07331935,-0.01321671,-0.06575297,-0.0145756,-0.00319336,0.00264721,-0.01554696,0.08526044,-0.02794159,-0.07419913,-0.06106755,0.00565176,0.00278582,0.02781822,-0.05381188,-0.08237447,0.05359952,-0.03609564,-0.06424148,0.00765002,-0.04394599,0.00283852,0.09565571,-0.04389418,0.04331002,0.00585829,0.01407706,-0.02688353,0.00622272,-0.06592933,-0.02067687,0.05201412,-0.01449581,0.01785149,-0.00809967,0.0168021,0.02090673,-0.07249913,0.01698842,0.02061954,-0.00726013,0.09156635,0.0115565,-0.04034969,0.01766762,0.04788131,0.05248336,-0.06066004,0.0382313,0.03658676,0.03511035,0.01590097,0.04500634,-0.00138013,0.01252753,-0.04708365,-0.19831325,-0.0666917,-0.04339788,-0.00150078,0.00268628,0.00438393,0.0448056,-0.0123372,-0.0021318,0.05998947,0.13604316,-0.00606749,-0.03011593,0.02669206,-0.01241126,-0.03349854,-0.0592943,-0.06053349,-0.01739534,0.04431613,-0.02041112,-0.00021729,-0.0171141,-0.02407186,0.03797286,-0.06265783,0.08798419,0.01000139,0.00968996,0.02399115,0.0699412,0.03346252,0.00371687,-0.06254357,-0.00809731,0.07212234,-0.00522651,-0.00642753,-0.01673201,0.0020669,-0.07011329,0.02661983,0.00904294,-0.08116102,-0.00165902,-0.00685223,-0.02358187,-0.01974189,0.02753663,0.01625724,0.02115663,-0.01295677,-0.00410806,0.02006998,0.04286603,-0.03591157,-0.1003142,-0.01007537,0.03165926,-0.02419844,0.01029392,-0.02751822,0.03934274,-0.02866922,0.02817355,0.00914157,0.00857276,-0.01319269,0.02499977,0.0253299,-0.01022711,0.08398791,-0.050692,0.04714824,-0.01923336,0.06334428,0.03103768,-0.05122757,-0.0443115,-0.01404126,0.03953627,-0.02355497,0.03464128,0.0875478,0.06008216,0.0208577,0.05960895,0.00108173,0.0416861,0.00304497,-0.01492486,-0.03120045,-0.02395556,0.00849979,0.00335886,0.04311211,-0.27552661,0.01309009,-0.03182422,0.02713077,-0.02827425,0.00811676,0.02525211,-0.01006179,0.04098855,-0.01164823,0.05151882,0.02395607,0.03132007,-0.0652818,-0.00836064,-0.01195891,0.04667818,-0.02162083,0.10109411,-0.00626631,0.03102451,0.00264996,0.2713736,0.00002891,-0.01040316,0.02612119,-0.01192766,0.0255062,0.02597207,0.04542303,0.01323719,0.04106756,0.04174117,-0.00564778,-0.02800542,0.08215852,0.01803175,0.06603192,-0.01411529,0.00715378,-0.06254724,-0.02040417,-0.0224513,-0.01466837,0.087273,-0.00136924,-0.02980131,-0.07056352,-0.02964677,0.03481887,-0.08242172,-0.07010717,-0.07356516,-0.05563919,-0.00020726,0.02962312,0.02689915,-0.01110778,0.02340676,-0.00788939,0.06780347,-0.02033533,0.05434348,0.04276687,-0.00327299],"last_embed":{"hash":"x4chyn","tokens":420}}},"text":null,"length":0,"last_read":{"hash":"x4chyn","at":1753423447747},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files","lines":[8,219],"size":26304,"outlinks":[{"title":"Analyze advanced tips on creating lotto, lottery strategy files, strategies.","target":"https://saliu.com/HLINE.gif","line":11},{"title":"Combine lottery strategies, lotto strategy files in MDIEditor lotto software and Lotwon.","target":"https://saliu.com/ScreenImgs/lottery-strategies.gif","line":26},{"title":"When multiple lottery strategies hit the odds are reduced tremendously.","target":"https://saliu.com/ScreenImgs/lottery-strategies-hits.gif","line":30},{"title":"Playing the lottery is a lot more effective when combining many strategies.","target":"https://saliu.com/ScreenImgs/lottery-strategies-skips.gif","line":36},{"title":"Cross-reference files show parameters in different strategy files for the same draw.","target":"https://saliu.com/ScreenImgs/cross-reference-data.gif","line":44},{"title":"_**INP-FILE.TXT**_","target":"https://saliu.com/pub/INP-FILE.TXT","line":54},{"title":"All lotto programs and lottery software by Grand Master Programmer work great together.","target":"https://saliu.com/ScreenImgs/cross-reference-input.gif","line":56},{"title":"Software for lotto, lottery to cross-reference strategy files created by MDIEditor, DOS programs.","target":"https://saliu.com/ScreenImgs/reference-strategies-file.gif","line":90},{"title":"_**HeaderFilterFiles.zip**_","target":"https://saliu.com/pub/HeaderFilterFiles.zip","line":92},{"title":"_**Lottery Strategy, Systems, Software Based on _Lotto Number Frequency_**_","target":"https://saliu.com/frequency-lottery.html","line":104},{"title":"Combine many lottery strategies and the player increases the chance to win big.","target":"https://saliu.com/ScreenImgs/cross-reference-report.gif","line":108},{"title":"_**Notepad++ text and programmer's editor**_","target":"https://notepad-plus-plus.org/","line":130},{"title":"Select columns in lottery strategy report files with Notepad++ editor to sort them easily.","target":"https://saliu.com/ScreenImgs/column-select.gif","line":138},{"title":"Lottery software sorts numeric columns in ascending or descending order to discover strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":150},{"title":"The best sorting software for lottery data, results files is named Sorting.","target":"https://saliu.com/ScreenImgs/sort-column.gif","line":154},{"title":"<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":170},{"title":"_**Lotto, Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":174},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":176},{"title":"_**User's Guide to**_ **MDIEditor And Lotto WE**","target":"https://saliu.com/MDI-lotto-guide.html","line":178},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":180},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":184},{"title":"_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge","target":"https://saliu.com/bbs/messages/623.html","line":185},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":186},{"title":"_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_","target":"https://saliu.com/bbs/messages/569.html","line":187},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":188},{"title":"**Vertical or Positional** _**Filters In Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":189},{"title":"_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_","target":"https://saliu.com/bbs/messages/896.html","line":190},{"title":"**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_","target":"https://saliu.com/bbs/messages/919.html","line":191},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":192},{"title":"_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":193},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":194},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":195},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":196},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":197},{"title":"**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_","target":"https://saliu.com/decades.html","line":198},{"title":"_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":199},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":200},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":201},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":202},{"title":"_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_","target":"https://saliu.com/STR30.htm","line":203},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":204},{"title":"**<u>Lottery Strategy Software</u>**","target":"https://saliu.com/infodown.html","line":205},{"title":"Special software checks lottery strategies between several programs, apps.","target":"https://saliu.com/HLINE.gif","line":207},{"title":"Forums","target":"https://forums.saliu.com/","line":209},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":209},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":209},{"title":"Contents","target":"https://saliu.com/content/index.html","line":209},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":209},{"title":"Home","target":"https://saliu.com/index.htm","line":209},{"title":"Search","target":"https://saliu.com/Search.htm","line":209},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":209},{"title":"The lottery cannot be played successfully without software that creates lottery strategies.","target":"https://saliu.com/HLINE.gif","line":211}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#By Ion Saliu, ★ _Founder of Lottery Strategizing Science_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10889252,-0.03585845,-0.01249147,-0.03043711,0.0025911,0.06573811,-0.01373382,-0.02627156,0.03161092,0.00697886,0.02326819,-0.00836055,0.0728843,0.01382785,-0.03777419,-0.02904521,-0.00524942,-0.00492568,-0.03494757,-0.01654291,0.02590371,-0.01669733,-0.01410631,-0.08324122,0.0306057,-0.00364964,-0.04918623,-0.06637888,-0.07560855,-0.24652891,-0.00432519,-0.0096336,-0.01270954,-0.02590984,-0.05683153,-0.03503335,0.00639762,0.10132618,-0.0663247,0.0624078,0.04059092,-0.00637582,0.00916083,0.02327009,-0.00435106,-0.05556416,-0.02241873,0.04668363,0.04026291,-0.00790836,-0.07488843,-0.0106676,-0.0044579,0.01655896,0.02722451,0.05053995,0.04066475,0.04766989,0.01313937,0.0543552,0.05830355,0.04345543,-0.18597674,0.04006038,-0.03246447,0.00392054,-0.03518374,-0.02643849,0.00598647,0.02442072,-0.01016111,-0.00677087,-0.00411679,0.08733545,0.06945354,-0.03851802,-0.02559969,-0.10622516,-0.07470333,0.00578419,-0.0551658,0.03112336,-0.01702853,-0.00181868,0.02252262,0.09199895,0.06720241,0.03875267,0.07035854,-0.07692544,0.0719941,-0.01354847,0.04358105,0.05035401,-0.00232129,-0.01639389,0.05757327,-0.00777321,0.02469015,0.14677118,-0.00510884,-0.01543802,-0.00357654,0.06067481,0.01803307,-0.02175361,-0.00939774,-0.05080571,0.01350462,0.052349,0.02750648,0.01705848,0.08320826,-0.07670985,0.01149475,0.03181396,-0.00669776,-0.00769143,0.00589894,0.03405399,-0.06334764,0.00026144,0.01121217,-0.01344071,-0.00568768,0.01123631,0.01831036,0.04101503,0.01817329,0.02219622,0.00310224,0.03416209,-0.15194917,-0.08130467,-0.02894881,-0.00327445,-0.03454072,-0.04252063,0.01150434,-0.00618794,-0.01671594,-0.04820475,0.05624859,-0.11048931,-0.03399391,0.09564772,-0.03098234,0.00919265,0.01180115,-0.0130575,0.03439298,0.01482514,-0.0223294,-0.05587291,0.00425458,-0.00789536,0.08246356,0.0553774,-0.06085544,0.01891592,-0.00803402,0.00028631,-0.024735,0.15300061,-0.01786352,-0.09058371,-0.01734602,0.06510434,-0.00384741,-0.04112203,-0.01152379,-0.01407099,0.00959142,-0.02132916,0.09957142,-0.02223194,-0.04367131,-0.07957045,-0.00088112,-0.00347924,0.02266296,-0.07770437,-0.0718049,0.04588613,-0.03393182,-0.05716417,0.00789451,-0.04542928,0.02849023,0.07555565,-0.04762598,0.03177825,0.01244421,0.02782302,-0.01905158,0.01111451,-0.07155517,-0.02144056,0.04747678,-0.00733394,-0.00357991,0.01748161,0.03181786,0.03402352,-0.08644544,-0.00342824,0.00136271,-0.0184909,0.07178592,0.0118706,-0.04658033,0.01595763,0.05427344,0.0165266,-0.05378788,0.05817376,0.06647629,0.00330069,0.01085708,0.06499141,0.02930588,0.01170216,-0.05227493,-0.20567626,-0.06275487,-0.04196095,-0.00385177,0.01141563,0.02549962,0.04376382,-0.02367065,0.02088825,0.08770116,0.13671458,-0.00887696,-0.04181029,0.04234031,-0.02591008,-0.02657177,-0.03224963,-0.05613175,-0.02570568,0.01379944,-0.01509432,-0.0018467,-0.03233126,-0.02258978,0.04478018,-0.05289489,0.09573428,0.01905841,0.02005188,0.05188132,0.05115264,0.05177549,-0.00370481,-0.08725651,-0.00868616,0.07524793,-0.01113073,-0.01445646,-0.01424608,0.00237209,-0.04949379,0.01948342,0.02194511,-0.07931348,-0.00168923,-0.00461571,-0.01467317,-0.01278887,0.02492276,-0.00634529,0.05196241,-0.00937075,0.01567631,0.029419,0.05026147,-0.01843856,-0.07676328,-0.00975666,0.02446358,-0.05693995,0.01771952,-0.01857772,0.01894579,-0.02968053,-0.00244508,-0.00264644,0.01607285,-0.01065664,0.02401706,0.02983192,-0.01883391,0.05523131,-0.04584713,0.03324171,-0.01750714,0.08305598,0.03210689,-0.03592029,-0.02710245,-0.01668304,0.08739497,-0.02914013,0.0499965,0.05166485,0.06766368,0.04348237,0.06327531,-0.00955899,0.03032336,-0.00065131,0.00262325,-0.00875306,-0.02547259,-0.00410799,0.03207203,0.04108204,-0.27035823,0.03367701,-0.00662184,0.04521495,-0.00535127,0.01089158,0.01615737,-0.00749084,0.04532738,-0.01843145,0.04308139,0.03642434,0.03786228,-0.06031498,-0.00550636,-0.00968212,0.03675032,-0.03959592,0.09691301,-0.00080347,0.03049328,0.00226431,0.26132119,0.00531497,-0.02731654,-0.0090195,-0.02409198,0.01447849,0.01272758,0.02356276,-0.00761721,0.00431059,0.02741852,-0.01323172,-0.02800145,0.06737531,0.01758571,0.07300541,0.00011643,0.02317741,-0.06424829,-0.00861039,-0.04188483,-0.02049661,0.07620309,-0.01500595,-0.05644725,-0.0467478,-0.04429809,0.02439874,-0.08384834,-0.08637485,-0.06215441,-0.03320721,0.00861561,0.03358721,0.03709484,-0.021711,0.00746975,-0.02423755,0.08134963,-0.01874526,0.02037203,0.02791181,0.01483874],"last_embed":{"hash":"d4agq9","tokens":167}}},"text":null,"length":0,"last_read":{"hash":"d4agq9","at":1753423447880},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#By Ion Saliu, ★ _Founder of Lottery Strategizing Science_","lines":[16,25],"size":520,"outlinks":[{"title":"Analyze advanced tips on creating lotto, lottery strategy files, strategies.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#By Ion Saliu, ★ _Founder of Lottery Strategizing Science_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10753348,-0.03257472,-0.0175656,-0.03037235,0.00270184,0.06476192,-0.0116791,-0.02420193,0.0332484,0.00736653,0.02415272,-0.00838994,0.0755303,0.01728987,-0.03556898,-0.03069691,-0.00737941,-0.00743084,-0.03621335,-0.01991461,0.02423456,-0.01664173,-0.01235875,-0.08516765,0.0303476,-0.0062238,-0.04919366,-0.06463138,-0.07424832,-0.24644566,-0.00732567,-0.00966086,-0.01297319,-0.02428191,-0.05831942,-0.03130247,0.0061563,0.09784154,-0.06839325,0.05958175,0.03774934,-0.00961388,0.00943449,0.01853631,-0.00650564,-0.05388724,-0.02032057,0.04921739,0.04321675,-0.00440817,-0.0754499,-0.01173252,-0.00511396,0.01837503,0.02892055,0.05005307,0.03725956,0.04732766,0.01201489,0.0559821,0.05626492,0.03830063,-0.18913008,0.03717463,-0.03497564,0.00265906,-0.0381568,-0.02699344,0.00485511,0.01892951,-0.00897454,-0.0070793,-0.00508331,0.08412342,0.06909669,-0.03812768,-0.02437608,-0.10503152,-0.06932037,0.00846788,-0.05492406,0.02871646,-0.01750763,0.00053694,0.02438918,0.08958278,0.06790491,0.04087937,0.0699736,-0.08107566,0.06845387,-0.01563661,0.04230182,0.04965637,-0.00185982,-0.01252895,0.05524302,-0.00934825,0.02511834,0.14548002,-0.00360354,-0.01478538,-0.00758515,0.0612806,0.02076215,-0.01951351,-0.00778681,-0.04809659,0.01476978,0.04979337,0.02616303,0.01917086,0.08311383,-0.07663932,0.00770226,0.03204321,-0.0042994,-0.00613344,0.00521983,0.03526813,-0.06013103,-0.00048391,0.01188481,-0.01468527,-0.00092949,0.0112503,0.02173957,0.04225107,0.01753932,0.02518263,0.00544736,0.03224819,-0.15560146,-0.08202077,-0.03205137,-0.00510927,-0.03895489,-0.0453601,0.01162022,-0.00094757,-0.02240772,-0.04356945,0.05223045,-0.11005287,-0.03573828,0.09522305,-0.03168737,0.00989009,0.00974787,-0.01320625,0.03288896,0.012178,-0.01857708,-0.05858825,0.00512478,-0.00982756,0.08081727,0.0548636,-0.06308549,0.02077549,-0.00668155,-0.00123027,-0.02646413,0.15557583,-0.01552418,-0.08538975,-0.01799207,0.06608829,-0.00265567,-0.03728957,-0.01089055,-0.00984544,0.01201743,-0.0189395,0.09929784,-0.02344734,-0.04398471,-0.07972173,-0.00314647,-0.0022355,0.02198281,-0.08106811,-0.06962296,0.04232401,-0.03309248,-0.06124998,0.01152207,-0.04562031,0.03037841,0.07471658,-0.04645477,0.03740748,0.01529809,0.02870297,-0.01858257,0.0116615,-0.07163983,-0.02114536,0.04571565,-0.00780751,-0.00825065,0.01826814,0.03125353,0.03396567,-0.08600984,-0.00294278,-0.00200761,-0.01286974,0.07283161,0.01105962,-0.04822331,0.01574856,0.05243778,0.01340094,-0.05307272,0.05832923,0.06749807,0.0024614,0.01274817,0.06529509,0.02991485,0.01272826,-0.05366508,-0.20985432,-0.06265724,-0.04187382,-0.00290517,0.01372807,0.02459853,0.04335702,-0.02685382,0.02487906,0.084285,0.1342563,-0.00771482,-0.03913154,0.04383073,-0.02448689,-0.0274512,-0.02903813,-0.05351162,-0.02474867,0.01441529,-0.0154494,-0.0042644,-0.02793147,-0.01807374,0.04349813,-0.05288982,0.0986902,0.01944789,0.01983263,0.05015207,0.04966808,0.05220032,-0.00474846,-0.08921653,-0.00954385,0.07832605,-0.01211444,-0.01011365,-0.01273369,0.00238068,-0.04716223,0.01937719,0.02315301,-0.07960518,-0.00211239,-0.00585238,-0.01075042,-0.01189987,0.02427748,-0.00968196,0.05601578,-0.00781852,0.01674473,0.02877088,0.05194203,-0.02014019,-0.07562032,-0.00692509,0.02499323,-0.06010245,0.01826331,-0.01867212,0.01605605,-0.03244123,-0.00214778,-0.00291867,0.01439871,-0.00947196,0.02633232,0.02729489,-0.0231728,0.0566986,-0.04655505,0.03623072,-0.01906772,0.0814859,0.0298949,-0.03987575,-0.02671067,-0.01403667,0.0829652,-0.03164489,0.04991042,0.04934293,0.06905025,0.04616665,0.0675468,-0.01087624,0.03039455,0.00247391,0.00245146,-0.00496935,-0.02563643,-0.00238745,0.03383034,0.04303435,-0.2721127,0.03730519,-0.00608212,0.04482015,-0.00273132,0.01082423,0.01493904,-0.00746098,0.04290326,-0.02028063,0.04285379,0.03734586,0.04140116,-0.05820702,-0.00449898,-0.00675197,0.0335528,-0.04375724,0.0953589,-0.00529836,0.03283789,0.00579272,0.26147613,0.00551787,-0.02951446,-0.00793242,-0.02301187,0.01290093,0.01055794,0.02207179,-0.00464268,0.00329914,0.02744946,-0.00910753,-0.02810313,0.06901102,0.01745352,0.06718715,-0.00254496,0.02432639,-0.06116829,-0.00722054,-0.04331056,-0.02001472,0.07778347,-0.01581699,-0.05938534,-0.04616008,-0.04067821,0.02404936,-0.08525114,-0.08701092,-0.06160066,-0.03159602,0.00887241,0.03530898,0.03799947,-0.01968054,0.00792945,-0.0269975,0.07909237,-0.01461276,0.01737626,0.02513135,0.01685749],"last_embed":{"hash":"1b96sqi","tokens":166}}},"text":null,"length":0,"last_read":{"hash":"1b96sqi","at":1753423447932},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#By Ion Saliu, ★ _Founder of Lottery Strategizing Science_#{1}","lines":[18,25],"size":458,"outlinks":[{"title":"Analyze advanced tips on creating lotto, lottery strategy files, strategies.","target":"https://saliu.com/HLINE.gif","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11547188,-0.02799793,0.00745163,-0.01026588,-0.01072051,0.05667334,0.01948604,-0.02839254,0.0245178,-0.01484216,0.0053686,0.00625566,0.0700237,-0.02331485,-0.0267312,-0.02993519,-0.01923145,0.00794006,-0.0500521,-0.00947763,0.04495478,-0.03126132,-0.02998083,-0.071025,0.04842293,0.01991228,-0.01832351,-0.04594963,-0.06986992,-0.2384779,0.01137237,0.00224058,-0.01899503,-0.05503147,-0.05594398,-0.06096344,0.00396568,0.10554378,-0.02140099,0.03639397,0.05428256,-0.00586306,0.03001427,0.02761909,-0.01034089,-0.0552959,-0.04536255,0.00798922,0.02445988,-0.00850294,-0.04935532,-0.01291371,0.02525046,-0.03886876,0.02169394,0.04449024,0.05661809,0.10644946,0.03665378,0.05392165,0.03561072,0.06185234,-0.16759352,0.04159198,-0.05957959,0.00363347,-0.01610864,-0.01606685,0.03874781,0.05835002,-0.03680814,-0.01868496,-0.00833469,0.11564738,0.03363328,-0.05670384,-0.02641672,-0.0861741,-0.0854345,0.03855295,-0.06064068,0.00514516,0.00738463,0.02612445,-0.00001029,0.09058762,0.05666057,0.04496519,0.05359897,-0.05730872,0.06763928,-0.02740954,0.04432188,0.01404998,-0.0115545,-0.0302626,0.08607381,-0.00616039,0.00890706,0.12862061,0.006075,-0.01828708,0.05485767,0.01880527,0.04176073,-0.03755142,-0.02723634,-0.05439228,-0.03864868,0.04306645,0.0442722,-0.02467113,0.04150987,-0.07981789,0.0108225,0.0186434,-0.05666879,-0.02200259,0.00933353,0.01257787,-0.00989799,-0.01518983,0.00477167,-0.02279668,-0.04039586,0.00887789,0.02776525,0.04032412,0.05427661,-0.00660763,0.0076032,-0.00513029,-0.11414464,-0.0616539,0.00129928,0.01559752,-0.01969961,-0.03685619,0.00344986,-0.01522819,0.02114871,-0.02379049,0.09390469,-0.10516176,0.01894441,0.09058373,-0.02147023,0.01341516,0.02279971,-0.0226218,0.03497929,0.0041156,-0.04839157,-0.04164746,-0.00673982,0.02379586,0.07717098,0.07317141,-0.00471716,0.00011944,-0.04009766,0.01991884,-0.04014123,0.13072591,-0.05205285,-0.08562905,0.01422004,0.04290209,-0.01627369,-0.09712203,-0.03685729,-0.00967046,-0.02120647,-0.0215986,0.07471804,-0.03973235,-0.06992603,-0.03654975,-0.02334799,0.00366289,0.0458328,-0.03634823,-0.09129554,0.06150763,-0.02021586,-0.05890718,0.02887777,-0.02268222,0.01718211,0.08866699,-0.06394912,0.0365519,0.01103246,0.01367962,-0.05964348,0.00051818,-0.04845078,-0.01061056,0.03795151,-0.01481437,0.05346479,0.00532465,0.00740554,0.0092928,-0.05165668,0.03503237,0.01385511,-0.03029059,0.11283812,0.00333548,-0.04161293,0.02637111,0.05309587,0.06201372,-0.0642672,0.00053645,0.02063849,0.03750221,-0.00175809,0.01820965,-0.01962052,0.02513074,-0.02629609,-0.20493282,-0.07189545,-0.02750359,-0.00064663,-0.0065008,0.00094758,0.02300059,0.00129694,-0.02081652,0.06904849,0.13573943,0.01341331,-0.03395221,0.02795134,-0.01479087,-0.00054738,-0.07227671,-0.0558912,-0.01915389,0.06233265,-0.00308023,-0.0077471,-0.01522159,-0.03911459,0.05764309,-0.05921478,0.10679938,0.0114949,0.01599224,0.00609908,0.07610994,-0.00006601,0.00509584,-0.03158057,-0.01755364,0.05001958,-0.02110425,0.01305992,-0.0255364,-0.02195781,-0.05424654,0.00754975,-0.01702568,-0.08090975,-0.00844482,0.00476657,-0.01038255,-0.0185112,0.01729846,0.01151126,0.01101673,-0.02462775,-0.0258591,0.02130977,0.05821271,-0.04374304,-0.080029,-0.02351253,0.00612898,-0.01491573,0.0038133,-0.02354051,0.04895635,-0.03557631,0.04701386,0.03158955,0.01436536,-0.01218771,0.04103348,0.04109459,-0.01985254,0.05922816,-0.05125171,0.03662635,-0.03135023,0.05448378,0.04095419,-0.04350723,-0.05260468,-0.02338023,0.05903896,-0.00995191,0.02879491,0.07865329,0.04447414,0.00295047,0.04038934,0.00248667,0.03821203,-0.01801169,-0.02618713,-0.0537893,-0.00409378,-0.00158974,-0.03473995,0.02403173,-0.26919296,-0.01053737,-0.05789677,0.0168413,-0.04246157,-0.00181212,0.04551724,0.01558941,0.0337562,-0.02107265,0.06095083,0.03815973,0.01017338,-0.07304731,-0.02317191,-0.02736962,0.07117031,0.00769629,0.09561524,0.00051915,0.0639855,-0.00880634,0.27649659,-0.00669049,0.00960464,0.03433797,0.0131274,0.00875987,0.01514554,0.05504996,0.01936,0.04682467,0.05269444,-0.01455685,0.00033871,0.07670823,0.02090481,0.08801418,-0.00339789,-0.00565546,-0.06295875,-0.02127689,-0.02431924,-0.0346832,0.08419938,0.02490711,0.0035458,-0.0601701,-0.03237485,0.04118745,-0.05848035,-0.0395957,-0.06198887,-0.04930784,0.01213592,0.03523924,0.00183996,-0.01443304,0.03345189,0.00595421,0.0565009,-0.02232109,0.0790925,0.03255193,-0.00998054],"last_embed":{"hash":"t5rsjb","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"t5rsjb","at":1753423447986},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>","lines":[26,56],"size":4496,"outlinks":[{"title":"Combine lottery strategies, lotto strategy files in MDIEditor lotto software and Lotwon.","target":"https://saliu.com/ScreenImgs/lottery-strategies.gif","line":8},{"title":"When multiple lottery strategies hit the odds are reduced tremendously.","target":"https://saliu.com/ScreenImgs/lottery-strategies-hits.gif","line":12},{"title":"Playing the lottery is a lot more effective when combining many strategies.","target":"https://saliu.com/ScreenImgs/lottery-strategies-skips.gif","line":18},{"title":"Cross-reference files show parameters in different strategy files for the same draw.","target":"https://saliu.com/ScreenImgs/cross-reference-data.gif","line":26}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11757249,-0.02261571,0.02615314,-0.02253812,-0.0241208,0.04817011,0.03206644,-0.02084168,0.022181,-0.02729051,0.00240748,0.00680832,0.0728718,-0.01856332,-0.0255941,-0.04157917,-0.03517023,0.0070615,-0.03551645,0.00681917,0.04908524,-0.03569626,-0.03474214,-0.08870132,0.03933726,0.03820642,-0.02913623,-0.05309587,-0.07316301,-0.22201505,0.01861531,-0.0002672,-0.03120361,-0.04617773,-0.04421937,-0.0594803,0.01170782,0.11071812,-0.05253136,0.03456562,0.04621278,0.0018183,0.01105755,0.03815216,-0.01711919,-0.05329969,-0.03816001,0.00916111,0.03507856,0.010196,-0.04193675,0.03133191,0.01410391,-0.0284863,0.0110413,0.03867334,0.0615898,0.0711817,0.01755404,0.04423458,0.04062339,0.05813285,-0.16642292,0.04531785,-0.0383883,0.03788333,-0.01670852,0.00580999,0.00155259,0.0770842,-0.0186502,-0.01364454,-0.02744691,0.11825977,0.04822037,-0.04938735,-0.06130192,-0.0985692,-0.11096191,0.04036617,-0.07463409,-0.01382034,-0.00062767,0.03075337,-0.00764085,0.09677228,0.0559392,0.02280778,0.07970219,-0.06607814,0.07830182,-0.01310955,0.037545,0.02910898,0.01497492,-0.02699922,0.09153324,-0.00364091,0.02253899,0.1656013,0.00117261,-0.03325473,0.04581876,0.0106434,0.0182592,-0.02432239,-0.01358386,-0.03802428,-0.02329005,0.036444,0.03467345,-0.00737988,0.04272703,-0.06075177,0.01462028,0.0335565,-0.03338868,-0.01812342,0.00501914,0.01211072,-0.0224205,-0.00728979,0.00271111,-0.00733889,-0.0378467,-0.00371088,-0.00159719,0.05097759,0.02348924,-0.00951055,0.00911561,0.00409587,-0.11814982,-0.07020185,-0.00003585,0.00065606,-0.00475025,-0.04902754,0.00851637,-0.01660169,0.03283321,-0.02744159,0.08505581,-0.10302959,0.01548026,0.09130098,0.01818713,0.01403803,0.01935602,-0.00247377,0.04180952,0.00708606,-0.05728293,-0.0476084,-0.00930525,0.01736101,0.06449135,0.04733216,-0.01539662,-0.0128673,-0.05669025,0.00293655,-0.04173591,0.15575317,-0.04779412,-0.09031828,-0.00437938,0.03756773,-0.02577576,-0.08352985,-0.00944356,-0.03526345,-0.02577702,-0.02876246,0.06633559,-0.02129919,-0.06060217,-0.05027495,-0.03542108,0.02280564,0.03461958,-0.02920982,-0.07800006,0.06570687,-0.02216732,-0.06432036,-0.00448828,-0.04259317,0.02063855,0.10193369,-0.03554904,0.02819976,0.01042026,-0.00629243,-0.04174052,-0.00781886,-0.04499974,-0.01146334,0.03940225,-0.03244092,0.03993201,0.02288361,0.01668989,0.03306575,-0.05653133,0.01791996,-0.00177555,-0.02092257,0.07412852,0.01756688,-0.0288086,0.02946462,0.03896046,0.06679412,-0.05573255,0.0268481,0.05218123,0.01227248,0.03249714,0.03562828,-0.01226547,0.0057134,-0.01795051,-0.21688507,-0.06210051,-0.04947348,-0.00479969,-0.00550479,0.00209394,0.02246367,0.0022093,-0.04314359,0.02905008,0.12285113,-0.01836134,-0.02150706,0.06202658,-0.02910916,0.00148207,-0.05884721,-0.05047224,-0.01015549,0.06248796,-0.00246992,0.00591779,-0.04120325,-0.03124158,0.0625272,-0.01707132,0.09452674,0.00280891,0.02385247,0.02542713,0.06282241,0.04560853,-0.00582424,-0.0458855,0.00286541,0.04935165,-0.02189663,-0.02074443,-0.03234481,-0.01434922,-0.07486323,0.01902863,-0.01284265,-0.05336664,-0.00308811,0.00981588,-0.02141374,-0.03012859,0.03771937,0.0414622,0.01146783,-0.0210201,-0.00392346,0.02076633,0.06140798,-0.02793033,-0.07993955,-0.01799466,0.0148814,-0.01898603,0.0172212,-0.02858331,0.04491223,-0.02979482,0.0245386,0.01359217,0.03089636,0.00667214,0.02333252,0.04357116,0.00154254,0.04068921,-0.05640115,0.01729916,-0.04071423,0.0552834,0.05073258,-0.02876148,-0.03586507,-0.04352407,0.06100747,-0.02093381,0.04359331,0.08290666,0.04156107,0.03000067,0.04482109,0.00882859,0.0162249,-0.00259208,-0.01994178,-0.03470819,-0.00875359,0.02264683,-0.01655397,-0.00531021,-0.27784988,0.01548707,-0.05638516,0.0488097,-0.02286615,0.0301848,0.01478768,0.01884267,0.03210243,-0.0062434,0.08324217,0.03807699,-0.00970828,-0.06578542,-0.00752603,-0.02273875,0.07582151,-0.01700517,0.08330254,0.00380288,0.06556511,-0.00308455,0.26612622,-0.00655048,-0.00425789,0.01541365,-0.00647529,0.02645656,0.01196355,0.0393395,0.00358724,0.02025913,0.03214842,0.00417623,-0.02300213,0.06917883,0.03113921,0.09473816,0.01516216,0.01525922,-0.09169178,-0.03419175,-0.01704898,-0.04373827,0.08029126,0.04880374,-0.02524865,-0.05458815,-0.04626952,0.02488693,-0.06829127,-0.05603752,-0.05482817,-0.07420154,0.01323904,0.03224365,-0.00047015,-0.03183171,0.03410077,0.00755021,0.0718321,-0.00870994,0.05948253,0.0178195,-0.03592215],"last_embed":{"hash":"g5q6u5","tokens":103}}},"text":null,"length":0,"last_read":{"hash":"g5q6u5","at":1753423448139},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{2}","lines":[29,29],"size":249,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08752082,-0.01637599,-0.00079173,-0.00804323,-0.01961524,0.05979062,0.00342236,-0.03550607,0.0250344,0.00362612,0.00450671,-0.00550665,0.06421128,-0.02504328,-0.01984601,-0.0125548,-0.01455707,0.01899574,-0.04653983,0.00335259,0.03049335,-0.023726,-0.02360321,-0.07409982,0.04572815,0.00482176,-0.03245927,-0.05759275,-0.06770929,-0.22220033,0.01940531,-0.03151185,-0.025158,-0.05413251,-0.05360952,-0.04359263,-0.00281302,0.10847622,-0.03953658,0.05388595,0.06215272,-0.01079238,0.03204969,0.03970618,-0.01262437,-0.09026119,-0.03204342,0.0221367,0.03572853,0.00706473,-0.02258153,-0.00111464,0.03269242,-0.00493451,0.01243216,0.06359915,0.06557935,0.10435481,0.02857608,0.03931616,0.01903719,0.06871039,-0.17558415,0.03346687,-0.05095147,0.03058887,-0.0030251,-0.0062934,0.01667088,0.05702196,-0.04493107,0.00890146,0.00662359,0.10698695,0.03347829,-0.04749506,-0.0301256,-0.09449662,-0.06372766,0.01545172,-0.04935351,0.01766066,0.00006502,0.00945568,0.00608254,0.07890293,0.04844227,0.02065082,0.06175342,-0.06860945,0.0591838,-0.04478143,0.04198909,0.01919257,-0.01041459,-0.02993871,0.0873756,-0.00785697,0.01016855,0.13802826,0.02196707,-0.01522711,0.02601339,-0.00141947,0.03340824,-0.03497663,-0.03030818,-0.04777709,-0.03266258,0.06917993,0.06650686,-0.02317487,0.03262048,-0.07971218,0.01534492,-0.00313869,-0.06167814,-0.03104174,0.0178414,0.00820608,-0.02344944,-0.02096099,0.03833847,0.00478929,-0.03416586,-0.00081229,0.0188763,0.03002974,0.03805227,-0.00859084,-0.02083952,0.00617912,-0.10984664,-0.06271802,0.00787224,0.00338001,0.02126566,-0.03591212,0.01882824,-0.00873035,0.0253593,-0.013762,0.07573711,-0.11067103,0.01183554,0.07981032,-0.00036889,0.00969843,0.01425203,-0.00822291,0.01872475,0.00601879,-0.03621763,-0.03566647,-0.01361255,0.03172269,0.0874732,0.08806814,0.01077373,-0.01455739,-0.04080223,-0.00254105,-0.02487745,0.15569094,-0.03918193,-0.10470922,0.01762131,0.05121331,-0.01598466,-0.05438171,-0.0384678,-0.00903579,-0.0162496,0.00207825,0.08469626,-0.03750225,-0.06050778,-0.03079141,-0.01785117,-0.02011614,0.01679965,-0.04111817,-0.08988392,0.0604311,-0.02390136,-0.06791214,0.02842056,-0.04050254,-0.01215506,0.09696107,-0.03830128,0.03156793,0.02508893,0.0061824,-0.02053671,0.00799125,-0.07763031,-0.01582565,0.06126498,-0.0271669,0.02854482,-0.00501838,0.00272551,0.00338944,-0.04141344,0.03771872,0.00886083,-0.0228621,0.12030074,-0.00743751,-0.03903852,0.0111289,0.06642633,0.05614173,-0.06368491,0.02362772,0.00520597,0.05533928,-0.00493645,0.0151933,-0.04408135,0.04811997,-0.03072614,-0.22126757,-0.07190125,-0.04318654,-0.01255839,-0.02187696,-0.0070961,0.03421596,0.00383064,-0.01836018,0.08344643,0.13107605,-0.01097032,-0.0279258,0.03976967,-0.03367711,-0.01128353,-0.06536251,-0.05760326,-0.015552,0.05587114,0.01762285,0.00392301,-0.0355603,-0.03610584,0.04875071,-0.06260988,0.09480826,0.01031619,0.01714875,0.0091466,0.07213955,0.02115899,-0.00561405,-0.04901386,-0.01619462,0.05977737,-0.01453707,-0.01082471,-0.01978391,-0.00883585,-0.08351502,0.02106789,-0.00920542,-0.08282749,0.01741426,0.00508874,-0.03014375,-0.00718768,0.01614247,0.03212088,-0.01362246,-0.00953056,-0.01544782,0.01993461,0.05843879,-0.03552395,-0.07982959,-0.01355814,0.03849598,-0.00130868,0.00796243,-0.02053663,0.05449059,-0.05739759,0.0152422,0.01561686,-0.0001086,-0.01993991,0.03409293,0.02646162,-0.02447926,0.0506051,-0.03516862,0.03068648,-0.03981042,0.06168777,0.02627738,-0.04105526,-0.05187157,-0.02117563,0.03100215,0.00096529,0.0326171,0.09908493,0.04985259,0.00594687,0.04246848,0.00570629,0.03539253,-0.01231631,-0.03978429,-0.04804494,-0.00536243,0.01578547,-0.00683443,0.01435942,-0.25999665,0.00380121,-0.04742321,0.03686042,-0.04449079,-0.01937309,0.02506335,0.00022385,0.01976928,-0.01833599,0.07086653,0.03056982,0.01003705,-0.06278238,-0.02624888,-0.03398028,0.06059367,-0.00107906,0.10111733,-0.00565773,0.04853644,0.00691691,0.27129301,0.01233831,-0.00534927,0.03684409,0.0038615,0.01821045,0.01334058,0.05276386,0.02734879,0.0621839,0.03804164,0.000086,-0.01634514,0.07169958,0.00956866,0.06887949,-0.00217813,0.02377098,-0.07325073,-0.02346608,-0.02789829,-0.02384697,0.08501879,0.02243331,0.00309636,-0.07863082,-0.02425019,0.05568106,-0.0843934,-0.06250583,-0.07017341,-0.07296304,0.00522225,0.0130778,0.01085945,-0.02007446,0.04354433,0.00280035,0.05891606,-0.01501451,0.06854473,0.02000686,-0.0113911],"last_embed":{"hash":"1fmuim5","tokens":140}}},"text":null,"length":0,"last_read":{"hash":"1fmuim5","at":1753423448177},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{3}","lines":[30,30],"size":340,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0772624,-0.06879197,0.01394106,-0.0152703,-0.035198,0.07345061,0.01936978,-0.02565102,0.02968524,0.00312647,0.00190903,0.01654898,0.07651507,-0.00543722,-0.02019619,-0.02544965,-0.0168482,0.00814624,-0.06870875,0.00336815,0.04860282,-0.0205875,-0.02287873,-0.06376776,0.0580373,-0.00371173,-0.03782562,-0.06566711,-0.06181551,-0.24242155,0.00360104,0.00070811,-0.03786569,-0.03071267,-0.03798024,-0.07663038,-0.00786267,0.13477927,-0.03077935,0.06196997,0.0413041,-0.00463986,0.02092811,0.05026814,-0.02437526,-0.04227944,-0.05334239,0.03779215,0.04692968,-0.01478689,-0.02203395,0.03214774,-0.00428398,-0.01552209,0.01812513,0.03625688,0.0507852,0.08648741,0.02193936,0.04510237,0.02692189,0.03865126,-0.16717181,0.04223836,-0.04236594,0.02792254,-0.01190569,0.01672984,-0.00875147,0.09503787,-0.01742538,-0.00738632,-0.00777242,0.09657475,0.05524877,-0.05791801,-0.05923766,-0.07680538,-0.09225379,0.03585714,-0.07170892,0.01311334,-0.02610319,-0.01831761,-0.00086224,0.0794541,0.05513806,0.0269607,0.05175379,-0.04535534,0.06711648,-0.00610137,0.03990134,0.04228471,-0.00687496,-0.02741557,0.06587377,-0.02009999,0.03374417,0.14910454,-0.00025826,-0.02292972,0.05100773,0.04544361,0.00016631,-0.02532557,-0.04200089,-0.0362715,-0.00208626,0.06365667,0.05471439,-0.01767604,0.03476153,-0.08488853,0.0179882,0.02699621,-0.04590966,-0.02987357,0.03003777,0.01946134,-0.02936541,-0.03091282,0.00768932,-0.00006251,-0.03380247,0.0113487,0.03866269,0.04469759,0.07255095,0.00199174,-0.00540618,-0.00563428,-0.12138281,-0.06942993,-0.03137862,-0.00780403,0.00061342,-0.00450301,0.01838128,-0.00834397,0.00667838,-0.05807366,0.06937699,-0.09927701,-0.00321898,0.10496134,-0.00017731,0.02087701,0.00990492,0.00823838,0.01980432,0.036519,-0.0388356,-0.04726864,-0.01964223,0.02221573,0.08067586,0.04355394,-0.0066954,0.01684445,-0.03635594,0.00829383,-0.03919068,0.14268753,-0.03779747,-0.09573643,-0.01790765,0.01521524,-0.02904351,-0.08687194,-0.03854965,-0.034056,-0.01006447,-0.02381862,0.08358617,-0.02916217,-0.09646375,-0.05724869,-0.04030351,0.01484966,0.00473763,-0.04928507,-0.07649838,0.04936956,-0.01399991,-0.05587806,-0.00755817,-0.03121328,0.01629377,0.09546327,-0.06403614,0.04416816,-0.00621986,0.00962568,-0.03722231,0.00308945,-0.0345364,-0.01758802,0.03446487,-0.02801187,0.03229496,0.02040056,0.00316552,0.0269223,-0.08494969,0.02751868,0.00627584,-0.03340923,0.10617495,0.00707583,-0.03123077,0.01620258,0.04325319,0.03744437,-0.02636,-0.00213523,0.06739706,0.00153757,0.02260922,0.04051521,-0.01883765,0.02316431,-0.02970463,-0.2045991,-0.07075092,-0.04574564,0.00553309,-0.0008904,0.00429399,0.01839944,0.02377751,-0.04009515,0.09756688,0.14667679,0.0139748,-0.02516533,0.04438738,-0.03379766,0.02533562,-0.06060884,-0.04107048,-0.0156009,0.07139757,0.00197931,0.00044613,-0.05008316,-0.0363872,0.07056615,-0.0306803,0.10042337,-0.00639698,-0.00446317,0.03918213,0.05666304,0.03087027,-0.0025942,-0.02213922,-0.02576227,0.03731555,-0.03071092,-0.0420508,-0.02745668,-0.0001824,-0.05066211,-0.00600733,0.01046995,-0.06224435,-0.01131764,0.00378594,-0.00108752,0.00079802,0.02566075,0.02854973,0.01925748,-0.04308948,0.01280642,0.01484832,0.08809716,-0.00495435,-0.08200313,-0.00795837,0.02487677,-0.02399044,-0.01072797,0.02447373,0.05205991,-0.02964597,0.02327882,0.03956071,0.00849138,0.00719171,0.02739782,0.02910252,-0.00459338,0.02479453,-0.0465804,0.02335536,-0.05018301,0.06496023,0.03094069,-0.02484167,-0.02067608,-0.03152787,0.10051941,-0.03375951,0.04319363,0.07091634,0.03507695,0.0088252,0.03881712,-0.00427087,0.03099487,-0.01461253,-0.01817719,-0.04670587,-0.00680722,0.00989783,-0.01811098,-0.00685091,-0.25577536,-0.00338739,-0.03931339,0.04193984,-0.01340477,0.0003132,0.01324747,0.02442636,0.02888589,-0.03360152,0.07150528,0.05101931,0.03373332,-0.08545429,-0.01080071,-0.02586239,0.07213101,-0.02782418,0.07789946,0.02716433,0.08561165,-0.01093767,0.24995646,0.00335982,0.00509814,-0.02068924,0.01638158,0.00616881,-0.00279412,0.04887107,0.00445654,0.01972313,0.02427691,-0.02525893,-0.02713657,0.05002068,0.02455752,0.10476433,-0.00463186,-0.00740363,-0.0815037,-0.01824344,-0.02682363,-0.05451171,0.08418941,0.02485452,-0.01398093,-0.05237199,-0.05388814,0.01987211,-0.07592776,-0.04820408,-0.05581561,-0.04732038,0.01306931,0.0212126,0.01578206,-0.03253275,0.01302435,-0.00906304,0.07220937,-0.01588079,0.03793737,0.01861145,-0.00704005],"last_embed":{"hash":"ikg317","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"ikg317","at":1753423448217},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{4}","lines":[31,32],"size":277,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11543198,-0.03722828,0.0019548,-0.00758893,-0.02859528,0.08566811,0.03615653,-0.02714243,0.05177957,0.00721406,0.00254693,0.00330252,0.07186624,0.0018842,-0.01540963,-0.02274526,-0.02826552,-0.00846822,-0.04149604,-0.01060644,0.0364419,-0.01927814,0.00864543,-0.07925907,0.07017265,0.0017517,-0.03005338,-0.07152145,-0.05749894,-0.25921422,0.01802816,0.01831172,-0.04107209,-0.05429688,-0.07719481,-0.07020712,-0.02453209,0.13192339,-0.0041908,0.04521056,0.02656703,0.01214524,0.0137461,-0.00181828,-0.02241455,-0.0267059,-0.07059285,0.01527292,0.02895981,0.00916214,-0.04064412,-0.01558774,0.02240348,-0.0332578,0.04286063,0.03071195,0.05305349,0.08479438,0.01494858,0.06983286,0.03550443,0.04801858,-0.15809248,0.05343796,-0.03602862,0.00399389,-0.02721713,0.00649693,0.03022366,0.09062564,-0.03396832,-0.01789409,-0.0178184,0.11749918,0.05631275,-0.04442275,-0.05145359,-0.06282273,-0.08789661,0.04519913,-0.04951372,0.0005759,-0.01505235,-0.00126839,0.00103567,0.08698578,0.03428879,0.03565222,0.0608165,-0.06855357,0.09270134,-0.01798069,0.01397904,0.01848611,0.00136386,0.00043283,0.10026763,-0.03713947,0.00391986,0.12677945,0.00022322,-0.02225413,0.06008084,0.03617289,0.01410401,-0.0386789,-0.03410204,-0.05194928,-0.02315458,0.04659382,0.04149706,-0.02362127,0.0383262,-0.07831246,-0.01174216,0.03793154,-0.03492802,-0.01422712,0.04116181,-0.00887806,-0.01964541,-0.00649738,-0.00888108,-0.01978801,-0.03187051,0.01872917,0.01377299,0.05257691,0.02056755,-0.02938143,0.02995002,-0.01958635,-0.12495282,-0.07196397,-0.02085448,-0.01506278,-0.02720983,-0.00617493,0.01119845,-0.0188149,0.01476665,-0.03118118,0.08972003,-0.11385429,-0.00240507,0.08064534,-0.00910947,0.01983994,0.02627218,-0.04143821,0.02006147,-0.00158092,-0.06856421,-0.02632667,-0.02026097,-0.00066028,0.06289954,0.06435248,-0.00501439,0.02205708,-0.05617354,-0.00420383,-0.04919529,0.11217099,-0.03898846,-0.09495888,0.00725099,0.02346446,-0.04145763,-0.08982597,-0.0219988,-0.0222866,-0.00594489,-0.03026039,0.09028635,-0.01946127,-0.08904824,-0.05567533,-0.01634649,0.01946206,0.0264977,-0.02801278,-0.08207759,0.05150083,-0.03750515,-0.05462031,0.01376718,-0.05919474,0.02725236,0.06633896,-0.07968459,0.02413579,-0.00458302,0.01943724,-0.04217921,-0.00641711,-0.03901815,-0.04069571,0.04661284,0.00436199,0.03587542,0.00380211,0.02214026,0.00627232,-0.04113352,0.03701421,0.02040419,-0.03309961,0.11187002,0.02315726,-0.02353765,0.00889571,0.05329729,0.04063843,-0.01934775,0.01090114,0.03080318,0.02754049,0.01434298,0.02001605,-0.01014726,0.01983146,-0.01609059,-0.20085298,-0.07543392,-0.03437558,0.01129809,-0.00600346,-0.00120793,0.02984505,0.00175113,-0.01890393,0.07429539,0.11700921,0.00632854,-0.04633475,0.05011918,-0.01497472,0.01384113,-0.09254582,-0.02034236,-0.03498019,0.078238,-0.00755509,0.00734039,-0.01975247,-0.03231075,0.05713685,-0.02693311,0.11320534,0.02426903,-0.01471465,0.01428622,0.07983293,0.04694933,0.01214182,-0.02995419,-0.01689984,0.03836032,-0.02355078,-0.01008122,-0.03527181,-0.01309067,-0.04228126,-0.00436228,0.02873579,-0.08470233,-0.01353314,0.010697,-0.00808848,-0.0099132,0.02617991,0.02942253,0.02936868,-0.04370468,-0.00808363,0.01359314,0.05192737,-0.02211093,-0.07101496,0.00585438,0.01217446,-0.00970638,-0.00404233,-0.00945707,0.04545062,-0.0341052,0.0638529,0.03176442,0.01093359,-0.01680859,0.02477279,0.04177197,0.00168591,0.06610341,-0.02196351,0.01734444,-0.04673256,0.04214206,0.03344449,-0.02205979,-0.04326173,-0.01178623,0.0594735,-0.02051065,0.01990444,0.04890365,0.03588698,0.00128266,0.06332089,0.00165421,0.02840081,-0.03219329,-0.01361858,-0.03198095,0.02178556,0.00109513,-0.02975799,-0.00653191,-0.27476403,0.01163765,-0.04282871,0.0158207,-0.02762687,-0.00087149,0.03284522,0.01379079,0.05481932,-0.01374606,0.05330177,0.02734258,0.03841201,-0.07480511,-0.01968148,0.02435903,0.04588736,-0.0123245,0.06524134,-0.01025185,0.08071598,0.00078518,0.27096048,-0.01801232,0.02473803,0.01209864,-0.00209199,0.01161614,0.01254128,0.04330677,-0.02547425,0.03023017,0.0436193,-0.03480759,-0.02576745,0.07878606,0.0306733,0.0963763,0.00076655,-0.0007054,-0.07076432,-0.02070163,-0.01081525,-0.01971317,0.0782568,-0.00232666,-0.00796492,-0.06444443,-0.04611042,0.03911401,-0.06590055,-0.06961955,-0.03494482,-0.05512745,0.03249754,0.04609352,0.00590239,0.00584572,0.01142924,0.02708351,0.05052161,0.01653862,0.05124173,0.0450094,-0.00573772],"last_embed":{"hash":"1ajqzpa","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1ajqzpa","at":1753423448251},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>1. The necessity of combining lotto, lottery strategy files</u>#{5}","lines":[33,56],"size":3414,"outlinks":[{"title":"Combine lottery strategies, lotto strategy files in MDIEditor lotto software and Lotwon.","target":"https://saliu.com/ScreenImgs/lottery-strategies.gif","line":1},{"title":"When multiple lottery strategies hit the odds are reduced tremendously.","target":"https://saliu.com/ScreenImgs/lottery-strategies-hits.gif","line":5},{"title":"Playing the lottery is a lot more effective when combining many strategies.","target":"https://saliu.com/ScreenImgs/lottery-strategies-skips.gif","line":11},{"title":"Cross-reference files show parameters in different strategy files for the same draw.","target":"https://saliu.com/ScreenImgs/cross-reference-data.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08715839,-0.03155107,-0.02979427,-0.02747372,-0.02598747,0.06253129,0.02186868,-0.02437463,0.01831225,-0.00252654,-0.00228487,0.01347239,0.04829241,-0.03167254,-0.04234974,-0.02734325,0.00046674,-0.00498448,-0.04720126,0.00960186,0.06428636,-0.05018381,-0.02927745,-0.0772803,0.04702007,-0.0026886,-0.01773112,-0.05535477,-0.07381633,-0.2493114,-0.01343011,-0.00863427,0.00992817,-0.04486991,-0.07195456,-0.04578609,-0.01862365,0.08314317,-0.02560451,0.04852428,0.06152018,-0.00504492,0.03223277,0.01391136,-0.01898806,-0.04405788,-0.04707865,0.01234656,0.07151642,-0.01121363,-0.06852783,0.00330021,0.0331373,0.01198881,0.01225374,0.03470429,0.08051008,0.10545823,0.03384035,0.03872612,0.00819044,0.06545999,-0.18531463,0.05026953,-0.05123075,0.02348045,-0.02000216,-0.02718427,0.01667467,0.03232159,-0.02656331,0.00370602,-0.01525477,0.09693355,0.05464744,-0.05944053,-0.0264876,-0.06860177,-0.07874929,0.02725238,-0.05381721,-0.01316143,0.01594749,0.0056026,0.02924351,0.08659451,0.06190547,0.0864099,0.0736976,-0.06996857,0.07275729,-0.0216348,0.02599858,0.03295367,0.00290022,-0.0219993,0.09253785,0.00046233,-0.002648,0.11861277,0.02604061,-0.020245,0.01797371,0.02968466,0.03792663,-0.02512903,-0.03641804,-0.0333541,-0.02579569,0.04426521,0.03021576,-0.02113051,0.05552625,-0.07381057,0.00638014,0.04102333,-0.01606285,-0.04122251,-0.01041591,0.0061066,-0.02938117,-0.01738042,0.00334117,-0.01521174,-0.03142548,0.00703973,0.02578727,0.04172002,0.03635099,0.02203012,0.01547903,-0.00100039,-0.16995251,-0.05909866,0.00088219,-0.00535199,0.01475298,-0.02729824,-0.00128189,-0.02269569,0.025588,-0.04375022,0.07179785,-0.08995073,-0.00550085,0.11032607,-0.01887026,0.01350162,0.03057499,-0.05261639,0.01648775,0.02588868,-0.04904807,-0.04866874,-0.01795703,0.04557993,0.0807046,0.0835552,-0.01692338,-0.00694565,-0.01559251,-0.00395818,-0.01257984,0.13370712,-0.00859986,-0.08135326,-0.0180652,0.0367177,-0.03495929,-0.08105672,-0.01889588,-0.00904403,-0.00849888,-0.00977428,0.06847104,-0.03436809,-0.07614148,-0.04541435,-0.01496925,0.01610585,0.00649961,-0.0349443,-0.07617162,0.04465038,-0.01813395,-0.07425737,0.03017266,-0.03652401,0.0260018,0.08764881,-0.05424896,0.01401555,-0.00382713,0.02183145,-0.01569789,0.01260136,-0.04860768,-0.02196563,0.04418469,-0.01833557,0.06543183,0.00547018,-0.00665995,0.0055833,-0.05973456,0.04648809,-0.0196545,-0.0344913,0.08776707,0.01276473,-0.04273266,0.0185291,0.06491103,0.06205008,-0.06537083,0.02100549,0.02648088,0.03278041,0.01775801,0.02759321,-0.02126231,0.01902032,-0.03569734,-0.21971345,-0.05460747,-0.03418418,0.00584564,0.00598222,-0.01412968,0.04385959,-0.03908056,0.00131684,0.0933833,0.13101088,0.01085895,-0.03705658,0.04313559,-0.02775805,-0.01165167,-0.07016049,-0.00729675,-0.01237328,0.05594477,0.01837071,-0.00714815,-0.02759606,-0.01466846,0.05821998,-0.05887388,0.12255886,0.00244487,-0.00187344,0.00418313,0.06985033,0.00161843,0.00319615,-0.04043289,-0.02165008,0.03260581,-0.05557751,0.02219472,-0.00700951,0.01257893,-0.0589289,0.01029725,0.02672164,-0.06692707,-0.0042171,0.03030699,-0.01174789,-0.03025332,0.00758873,0.0183265,0.01865349,-0.01800439,0.00357656,0.01454281,0.05737877,-0.02587964,-0.07596122,-0.02971621,0.01003838,-0.01472057,-0.0036818,-0.03107899,0.05790848,-0.05490942,0.02517258,0.02750234,0.03274688,-0.00369093,0.03807944,0.03356098,0.00627532,0.0346075,-0.03969369,0.05827964,-0.04139622,0.04975788,0.02757643,-0.037132,-0.03697419,-0.02478751,0.04925641,-0.02356846,0.02419594,0.08502247,0.0451909,0.04220631,0.06205256,0.01872171,0.03459794,-0.01017172,0.03096779,-0.03405653,-0.03455526,0.01169488,-0.04341881,0.01595172,-0.26863992,-0.00770405,-0.07281487,0.03902387,-0.02793583,-0.0155674,0.04181264,0.00022637,0.05259946,-0.03637194,0.05687582,0.05860655,0.01379567,-0.07585869,-0.02322651,-0.01980657,0.01564312,-0.00073918,0.07342461,0.01400682,0.07183506,-0.00705605,0.24573626,0.00150599,-0.01343412,0.01613764,0.01246605,-0.01531065,0.01147388,0.02949186,-0.00272654,0.00426235,0.08376756,-0.0184263,0.01001414,0.06524061,-0.01087935,0.07104816,-0.00384356,0.01282454,-0.0636244,-0.02391202,-0.03808389,-0.04708732,0.0951076,0.01588234,0.00027239,-0.05784615,-0.03014653,0.07023529,-0.07077011,-0.05999308,-0.05992069,-0.04783857,0.0049193,0.03207304,-0.01665327,0.00026442,0.02397289,0.01369104,0.04485571,-0.02119179,0.05671324,0.02117124,0.01021553],"last_embed":{"hash":"i4nswr","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"i4nswr","at":1753423448404},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>","lines":[57,129],"size":10686,"outlinks":[{"title":"_**INP-FILE.TXT**_","target":"https://saliu.com/pub/INP-FILE.TXT","line":5},{"title":"All lotto programs and lottery software by Grand Master Programmer work great together.","target":"https://saliu.com/ScreenImgs/cross-reference-input.gif","line":7},{"title":"Software for lotto, lottery to cross-reference strategy files created by MDIEditor, DOS programs.","target":"https://saliu.com/ScreenImgs/reference-strategies-file.gif","line":41},{"title":"_**HeaderFilterFiles.zip**_","target":"https://saliu.com/pub/HeaderFilterFiles.zip","line":43},{"title":"_**Lottery Strategy, Systems, Software Based on _Lotto Number Frequency_**_","target":"https://saliu.com/frequency-lottery.html","line":55},{"title":"Combine many lottery strategies and the player increases the chance to win big.","target":"https://saliu.com/ScreenImgs/cross-reference-report.gif","line":59}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08498526,-0.03816429,-0.03060785,-0.03524956,-0.01887982,0.06275643,0.01375119,-0.02403602,0.0118212,0.00115981,-0.00318785,0.01476132,0.05341875,-0.03112427,-0.0419532,-0.03244974,0.00093086,-0.00679169,-0.05469258,0.0127448,0.06531488,-0.04773708,-0.02534947,-0.077796,0.03786646,-0.00905288,-0.01407048,-0.04994277,-0.08008389,-0.24717201,-0.00868386,-0.00280191,0.0100686,-0.0482557,-0.0783262,-0.04513122,-0.01416845,0.07549057,-0.02711731,0.04406593,0.05806804,-0.00710572,0.03653551,0.00687778,-0.0176611,-0.04452077,-0.04373356,0.00647257,0.06729402,-0.00508722,-0.06482491,0.00318679,0.03649046,0.00846118,0.01312968,0.02650242,0.08003762,0.10942606,0.03742457,0.03848513,0.00440531,0.06716575,-0.18494862,0.05023676,-0.05698572,0.02177949,-0.02222931,-0.02962741,0.02134332,0.02707681,-0.03220798,0.00902762,-0.01861055,0.09690673,0.05545558,-0.06482652,-0.02080093,-0.0701431,-0.07510103,0.03608489,-0.04862264,-0.01142896,0.01247261,0.00992515,0.02528159,0.08409023,0.05874833,0.07981878,0.06246647,-0.06704424,0.06737242,-0.02483144,0.02437856,0.03288218,0.00328244,-0.02424601,0.0906881,0.00357771,-0.0092172,0.11522941,0.02476549,-0.01411961,0.01969585,0.01891837,0.03851099,-0.02610135,-0.04090074,-0.03389192,-0.02798304,0.04265854,0.03325281,-0.02586097,0.05039905,-0.07677785,0.00270219,0.04533537,-0.01844199,-0.03697182,-0.01275286,0.0020085,-0.03418229,-0.01546253,0.00916723,-0.01335531,-0.02878366,0.00786081,0.03013691,0.04308471,0.04201833,0.02392128,0.01672756,-0.00847376,-0.16872753,-0.05999925,0.00204036,-0.00306603,0.00971934,-0.02856501,-0.01248297,-0.02222453,0.02439515,-0.04536172,0.07296278,-0.08953333,-0.00939213,0.10842513,-0.01776857,0.0109083,0.03102139,-0.05369823,0.02122972,0.03059724,-0.0503131,-0.04843181,-0.01223278,0.04732323,0.08421108,0.07701793,-0.02442562,-0.00775208,-0.01816878,-0.01144545,-0.00878151,0.12729938,-0.00906955,-0.08038907,-0.02159716,0.03196462,-0.035884,-0.07603121,-0.01850003,-0.00828588,-0.00701979,-0.00753891,0.06260739,-0.03038161,-0.07531821,-0.04564162,-0.02169113,0.01532789,0.00787125,-0.0333413,-0.0746756,0.04086082,-0.02061024,-0.07462262,0.0320106,-0.03832777,0.0303747,0.08888113,-0.05052645,0.0195215,0.0015919,0.01958549,-0.018447,0.00478511,-0.04751039,-0.02273705,0.03884896,-0.02177817,0.06615677,0.00565674,-0.00692508,0.00528092,-0.05395138,0.04714563,-0.01799096,-0.02919952,0.09142712,0.00914172,-0.04123946,0.01712194,0.06823003,0.06129068,-0.06361605,0.01853846,0.02644735,0.0305415,0.01665468,0.02918034,-0.02500818,0.02327919,-0.0375802,-0.22022913,-0.04918813,-0.03181981,0.00519261,0.00948467,-0.01737959,0.03927033,-0.03810731,0.00470173,0.09492067,0.14072765,0.01331876,-0.03541597,0.04625056,-0.02374973,-0.00552801,-0.06739029,-0.01351529,-0.01589832,0.05719008,0.0172068,-0.00457049,-0.02004595,-0.01504405,0.06210729,-0.05858984,0.12244342,0.00695568,0.0073542,-0.00029584,0.07309996,0.00429286,0.0084853,-0.04613138,-0.01379499,0.02838654,-0.05403073,0.02502936,-0.00766185,0.01188169,-0.06100552,0.00652932,0.02453505,-0.07014339,0.00156729,0.03367439,-0.00942449,-0.02903884,0.00138977,0.01288296,0.02318813,-0.01568595,0.00051594,0.01735153,0.06249821,-0.0286364,-0.07305775,-0.02699813,0.01215393,-0.01963187,-0.00060444,-0.03631589,0.05895753,-0.05716064,0.02098016,0.02354231,0.03058454,0.00030875,0.04029291,0.03325978,0.00775891,0.03762541,-0.04514259,0.06631152,-0.04410613,0.04887445,0.03007177,-0.02832107,-0.03282222,-0.02417856,0.04938793,-0.01018603,0.02061808,0.08501477,0.04135065,0.04071305,0.06279769,0.01978517,0.03834759,-0.01054125,0.02614624,-0.03566508,-0.02917182,0.01174333,-0.04162303,0.01423226,-0.2732833,-0.00832129,-0.06982844,0.04192438,-0.02861733,-0.01839678,0.04497639,0.00275426,0.05152426,-0.03657523,0.05404646,0.0556656,0.01600891,-0.07794976,-0.02569118,-0.02029267,0.01228406,0.00348251,0.07610193,0.00638799,0.07023776,-0.00326504,0.25125313,-0.00644182,-0.01671911,0.01405761,0.00896404,-0.01258224,0.01432633,0.03162517,0.00241956,0.01208419,0.08816487,-0.02008774,0.00675101,0.06221163,-0.0111286,0.07036819,-0.00752943,0.01105591,-0.06076979,-0.02097814,-0.03741855,-0.04759585,0.08901735,0.01523261,0.00012878,-0.06224013,-0.02848251,0.07623689,-0.06178847,-0.05531222,-0.05989898,-0.0486982,0.01036272,0.03411226,-0.01633112,0.00274923,0.02466558,0.01542127,0.04440792,-0.02356601,0.06592368,0.02918668,0.01501005],"last_embed":{"hash":"v36b39","tokens":508}}},"text":null,"length":0,"last_read":{"hash":"v36b39","at":1753423448589},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#{1}","lines":[59,66],"size":1687,"outlinks":[{"title":"_**INP-FILE.TXT**_","target":"https://saliu.com/pub/INP-FILE.TXT","line":3},{"title":"All lotto programs and lottery software by Grand Master Programmer work great together.","target":"https://saliu.com/ScreenImgs/cross-reference-input.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>A. Manual Operation: <i>S = Screen Prompt</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07813936,-0.03843889,-0.01792437,-0.03674607,-0.01206538,0.072821,-0.0101384,-0.0187539,0.00484891,-0.00671202,-0.00032879,-0.00005387,0.06740571,-0.01486536,-0.03841427,-0.03792683,-0.0312705,-0.0122459,-0.05961277,0.02131643,0.05572304,-0.02895986,-0.03217973,-0.05775158,0.03580616,-0.00088056,-0.01009718,-0.05718056,-0.07216159,-0.2486522,0.03804172,-0.02986809,0.00579116,-0.04927934,-0.07176995,-0.05609038,-0.02773752,0.0970295,-0.01355706,0.03289275,0.0594914,-0.02560577,0.03775332,0.02671751,-0.01930486,-0.06831793,-0.00522674,-0.00497077,0.0611978,-0.02944101,-0.06722692,-0.00285803,0.04514551,-0.01183486,0.01095092,0.03371453,0.05760371,0.10748609,0.04890148,0.04986129,0.01529393,0.04561242,-0.18575077,0.07272047,-0.05605297,0.0156018,-0.04124882,-0.01251018,0.00183015,0.03313962,-0.03976407,0.01770499,-0.00605485,0.09783523,0.01981952,-0.04565857,-0.02227818,-0.09092746,-0.07185245,0.01645709,-0.0631116,0.01263743,0.01262511,0.0170645,0.00132127,0.09652331,0.06696507,0.05699621,0.03776187,-0.03035694,0.05424643,-0.01640079,0.00957523,0.05487077,-0.02727736,-0.03433708,0.06505623,0.01562329,-0.00996599,0.10312553,0.02434357,-0.02747316,0.01736678,0.01726531,0.05901864,-0.02447657,-0.03793261,-0.02171985,-0.0162506,0.06505229,0.04691759,-0.02079376,0.03614856,-0.08431988,0.02134022,0.02080729,-0.04721551,-0.03633305,-0.01328766,-0.00719512,-0.03160822,-0.03101853,-0.02786872,0.00525609,-0.03215792,0.03432212,0.05172539,0.03708741,0.06435813,0.03274142,0.01186027,-0.00564873,-0.16647954,-0.04828473,-0.0122755,0.02872361,0.00720813,-0.01915296,-0.00798584,-0.06650499,0.00926603,-0.02860443,0.04684236,-0.08558835,-0.01866209,0.12760797,-0.03045171,0.01522479,0.02021453,-0.06325174,0.02447453,0.02376389,-0.06453936,-0.02066314,-0.00256446,0.04222376,0.05472195,0.09067446,-0.00323513,0.01981966,-0.05715937,-0.01469806,-0.00803924,0.110273,-0.01218634,-0.08784033,-0.02717251,0.0407473,-0.03582241,-0.08056119,-0.02264593,-0.00338963,-0.04745543,0.01810823,0.07560766,-0.03049399,-0.08672545,-0.04420553,-0.02324682,-0.00596163,0.02842531,-0.02564134,-0.08522639,0.03096676,-0.03666443,-0.05780692,0.02571496,-0.03256587,0.00379913,0.09545873,-0.08236375,0.00767469,-0.01113997,0.00905279,-0.00847695,0.00710376,-0.04027024,-0.0278025,0.04915462,-0.02749135,0.06941956,0.0117424,-0.0170338,0.03574891,-0.05764823,0.06566625,-0.00625622,-0.03468385,0.1283062,0.01131508,-0.06596274,0.01644219,0.04314357,0.04004261,-0.06307404,-0.01423845,0.02154949,0.01141281,0.0162926,0.02017579,0.00531128,0.02434489,-0.04690967,-0.1998051,-0.04562461,-0.03026341,-0.03171746,-0.02284521,-0.00908596,0.01708266,-0.0305584,0.01877001,0.08495234,0.13376118,0.00415174,-0.01233039,0.03437896,-0.01996605,-0.00775929,-0.0708571,-0.0274686,-0.03074024,0.05906073,0.03358397,0.00630312,-0.04054972,-0.02465105,0.05725817,-0.046422,0.1224813,-0.00505674,0.03018904,-0.00483038,0.08327302,0.01319088,0.00778504,-0.04071154,-0.00751703,0.02385886,-0.05567393,0.01406415,-0.00954656,-0.00299468,-0.05017591,0.01220833,-0.00016073,-0.0700942,-0.00619242,0.04912619,-0.00382828,0.00829512,-0.00397644,0.0039114,0.0103931,-0.03111024,0.01773563,0.03931377,0.09115113,-0.0540244,-0.05906966,-0.01730259,0.00880809,0.0115724,-0.01325064,-0.00806779,0.02117242,-0.04218014,0.02155042,0.03409602,0.01268654,-0.01781222,0.04723864,0.01539925,-0.02923552,0.0404055,-0.03552487,0.05479989,-0.02132119,0.03498457,0.02170661,-0.02451529,-0.03175449,-0.02581019,0.06844512,-0.01703366,0.03196656,0.08365748,0.03026477,0.04730305,0.03956942,-0.00762504,0.05774972,-0.02728992,0.00440853,-0.01866298,-0.01731119,0.01546081,-0.01057938,0.01065495,-0.27170956,-0.00323047,-0.05870841,0.04490338,-0.00792247,-0.02206055,0.04037627,0.02606347,0.02167039,-0.00930603,0.01274549,0.0555997,0.02101611,-0.06565993,-0.02923873,-0.03959898,0.01944718,0.00243698,0.10428546,0.01814344,0.05307934,-0.01624238,0.25734091,-0.0120384,0.00354085,-0.00078046,0.01213399,-0.00013165,0.01880872,0.04549436,0.01673196,0.02092674,0.11624952,-0.0342607,0.00032293,0.04838632,0.01278794,0.05503335,0.00498337,0.00518805,-0.0486099,-0.01511545,-0.01840009,-0.04615676,0.06477056,0.01493383,0.02206882,-0.04608449,0.00018421,0.06654371,-0.05963985,-0.02476969,-0.07575765,-0.03330391,0.01392175,0.0199203,0.00956944,-0.00049056,0.01327398,0.02787705,0.06049138,-0.02212176,0.07367736,0.05021988,0.00216977],"last_embed":{"hash":"1mttmbk","tokens":510}}},"text":null,"length":0,"last_read":{"hash":"1mttmbk","at":1753423448746},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>A. Manual Operation: <i>S = Screen Prompt</i></u>","lines":[67,92],"size":3615,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>A. Manual Operation: <i>S = Screen Prompt</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07570896,-0.0410743,-0.01690612,-0.03978911,-0.01302838,0.07616894,-0.0113002,-0.01800095,0.00215413,-0.0065213,0.00057002,-0.00028174,0.06860089,-0.01457551,-0.03887323,-0.0416634,-0.03085458,-0.01508659,-0.06036107,0.02387039,0.06000194,-0.02860088,-0.0309987,-0.05811374,0.03466144,0.00035382,-0.01186069,-0.05988908,-0.07045165,-0.24824454,0.03887424,-0.0283636,0.0055391,-0.04938157,-0.07035769,-0.05721337,-0.02785663,0.09668212,-0.01359937,0.03275527,0.05416941,-0.02245417,0.03647713,0.02575463,-0.01747137,-0.06860396,-0.00496496,-0.00404687,0.06175806,-0.02751073,-0.06499444,-0.0029708,0.04537684,-0.00922862,0.01004417,0.02876946,0.05703632,0.10488712,0.04778966,0.05104978,0.01560513,0.04369551,-0.18703702,0.07394678,-0.05498813,0.01611186,-0.03907825,-0.0112973,0.00107369,0.03386075,-0.04281437,0.01779959,-0.0081896,0.09902008,0.01757548,-0.04318422,-0.02467304,-0.09052134,-0.07331808,0.01950302,-0.06345905,0.01315484,0.01156633,0.0159018,-0.00245819,0.09439937,0.06484631,0.05609254,0.03744772,-0.02709027,0.0532073,-0.01980418,0.00977907,0.05902723,-0.02599558,-0.03557223,0.06317405,0.01651992,-0.0098478,0.09979528,0.02360173,-0.02589876,0.01667396,0.01749788,0.05762994,-0.02241083,-0.0388759,-0.01930149,-0.01539073,0.06596132,0.04714192,-0.02023528,0.03412873,-0.08393903,0.02167948,0.02113128,-0.04659849,-0.03913831,-0.01422181,-0.00693791,-0.03186533,-0.03345899,-0.02969289,0.00570096,-0.03112301,0.03484379,0.05100831,0.03815742,0.0621803,0.03206191,0.01371258,-0.00791489,-0.16639748,-0.04833644,-0.01336523,0.02818039,0.00940514,-0.01759381,-0.00770391,-0.06945717,0.00688954,-0.02871837,0.04537597,-0.08340567,-0.01973583,0.12759422,-0.03114045,0.0155818,0.02152882,-0.06718594,0.02440971,0.02555921,-0.06664024,-0.02259943,-0.00059522,0.04125167,0.05303906,0.08979489,-0.00429059,0.02181167,-0.05805748,-0.01387874,-0.00913615,0.11037273,-0.01179497,-0.08762028,-0.030465,0.04065562,-0.03534074,-0.07827675,-0.02156088,-0.00475476,-0.04681319,0.01567785,0.07358396,-0.03131875,-0.08661918,-0.04334077,-0.02519354,-0.00753048,0.02863047,-0.02169689,-0.08256681,0.02891668,-0.03888786,-0.05759029,0.0257532,-0.03424621,0.00561697,0.09282554,-0.08189698,0.00930714,-0.01174548,0.00893498,-0.00563566,0.00743157,-0.03889326,-0.02804705,0.05037001,-0.02947831,0.07077692,0.01169855,-0.01756781,0.03579959,-0.05696498,0.06396212,-0.00956635,-0.03308695,0.12834576,0.01093593,-0.06587731,0.01484325,0.04496615,0.03985045,-0.06289198,-0.01312542,0.02273236,0.00687244,0.01666566,0.02048498,0.00458771,0.0235203,-0.04927068,-0.20056874,-0.04351065,-0.03023984,-0.03111162,-0.02202086,-0.00847482,0.01688438,-0.03003412,0.01732489,0.08586054,0.13495766,0.00582096,-0.01168625,0.03797717,-0.01954453,-0.00677909,-0.07134081,-0.02770834,-0.03096543,0.06061162,0.03272712,0.00837089,-0.04153359,-0.02340161,0.0591903,-0.04329423,0.12390403,-0.00455155,0.03044929,-0.00378193,0.08452933,0.01560215,0.00726725,-0.04139563,-0.00639172,0.0240661,-0.05714615,0.01663316,-0.00859759,-0.00162351,-0.04805636,0.01097196,0.0022466,-0.07044468,-0.0066925,0.0501627,-0.00345895,0.00774101,-0.00347428,0.00387161,0.0105711,-0.03227469,0.02138453,0.03630592,0.09129325,-0.05364579,-0.0585536,-0.01830515,0.00972785,0.01129953,-0.0114023,-0.00701705,0.01808398,-0.04139017,0.01857198,0.03406679,0.01444008,-0.01574311,0.04929591,0.01577372,-0.02784071,0.04021453,-0.03498855,0.05444458,-0.02172579,0.03275671,0.02054571,-0.02263275,-0.0338513,-0.03080913,0.06647029,-0.01675738,0.03404028,0.08283532,0.02803442,0.04880376,0.04153123,-0.00613925,0.05742402,-0.02734248,0.00386953,-0.0182804,-0.01643335,0.01443658,-0.00702948,0.00737747,-0.27232119,-0.00249258,-0.05890739,0.04514482,-0.00784642,-0.02346476,0.03939386,0.02721607,0.01971516,-0.00866004,0.01368921,0.05427314,0.02028312,-0.06705332,-0.02554091,-0.04070084,0.0202625,0.00269369,0.10635094,0.01929278,0.05377945,-0.01790056,0.2561951,-0.0145223,0.00564336,-0.00413778,0.01197323,0.00009859,0.02181083,0.04475433,0.01737539,0.01855192,0.12091399,-0.0366027,0.00033668,0.05017615,0.01507866,0.05502382,0.00359999,0.00368167,-0.04997521,-0.01392631,-0.01823933,-0.04671888,0.0628267,0.01457343,0.02175927,-0.04686707,0.00116568,0.06657418,-0.05730262,-0.0245209,-0.0753748,-0.03243459,0.01781518,0.01935852,0.01098004,0.00177633,0.01030581,0.02898087,0.05922052,-0.02089992,0.07411048,0.0507159,0.0039223],"last_embed":{"hash":"1p0ixho","tokens":510}}},"text":null,"length":0,"last_read":{"hash":"1p0ixho","at":1753423448928},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>A. Manual Operation: <i>S = Screen Prompt</i></u>#{1}","lines":[69,92],"size":3557,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09567714,-0.04541297,-0.00759017,-0.04697935,-0.0156375,0.07941549,0.02297875,-0.02922644,0.00462661,-0.01260926,-0.0089566,0.01362754,0.07248566,-0.00478699,-0.04308275,-0.0336647,-0.01979873,-0.02089642,-0.065136,-0.00962667,0.04991197,-0.02640572,-0.01060511,-0.08350888,0.0179911,-0.01541283,-0.02268717,-0.04913811,-0.08953907,-0.23695323,0.03927075,0.00244113,-0.00291541,-0.02047293,-0.07844853,-0.05775673,-0.02583821,0.10137182,-0.0414355,0.05495675,0.0579121,-0.00940456,0.02505775,0.02650562,-0.01820716,-0.04644791,-0.03712279,-0.00590989,0.05889479,-0.00436353,-0.06956001,0.00496888,0.02857781,-0.00371584,0.01514521,0.02591447,0.0666619,0.0990198,0.04675952,0.03422332,0.02438136,0.06802376,-0.18225864,0.06357521,-0.02539845,0.02261293,-0.01479659,-0.03232792,0.00156268,0.02166716,-0.04114113,0.03131935,-0.00353733,0.0967984,0.05978283,-0.04717162,-0.03109714,-0.08490749,-0.07007177,0.02377494,-0.03974665,0.0026742,0.00170976,-0.00021926,0.02033359,0.0910091,0.05673789,0.06489722,0.06201635,-0.03003367,0.08054812,-0.02151543,0.01463811,0.05763744,0.01689013,-0.01974434,0.05337629,0.0027821,-0.0070558,0.12137508,-0.00431609,-0.03584588,-0.00092266,0.02166065,0.03118834,-0.02043102,-0.02271981,-0.00061474,-0.00150608,0.05460844,0.02892159,-0.01897764,0.06376085,-0.0816838,-0.02098187,0.05703164,-0.03813211,-0.02543743,-0.00507729,0.00017864,-0.03327527,-0.00490129,-0.00651145,0.00662489,-0.02137528,0.02113749,0.0311736,0.05087257,0.04486701,0.02503895,0.0096995,-0.00299738,-0.15928619,-0.06638975,0.01116873,-0.00023413,-0.01539728,-0.03498805,-0.0081207,-0.02284823,0.01927238,-0.0373273,0.05357678,-0.11571381,-0.03448569,0.10055558,0.00176673,0.01168465,0.03292444,-0.0523723,0.02809586,0.03765227,-0.06386592,-0.04383244,-0.01746752,0.02448407,0.09315348,0.0783667,-0.01894641,0.01299476,-0.04570723,-0.01475973,0.00409801,0.10683443,-0.01342686,-0.08942618,-0.06303201,0.02059231,-0.03554532,-0.0618443,-0.00933285,-0.01912027,-0.03506083,-0.0272698,0.08037444,-0.02205898,-0.07508352,-0.05204601,-0.01807107,0.01084447,0.00363464,-0.0397782,-0.07856075,0.03622677,-0.04277518,-0.08286042,0.02259478,-0.05671113,0.01232701,0.09182415,-0.06898949,0.01435277,-0.00473793,0.02239501,-0.00044985,0.00296185,-0.03201029,-0.02719487,0.02618384,-0.02527091,0.03234206,0.02148388,0.0036441,0.03853285,-0.06667553,0.05229933,-0.00304751,-0.04245547,0.11023523,0.03464357,-0.04034745,0.01585531,0.07457513,0.05237122,-0.05663509,0.00912354,0.01649483,-0.00137838,0.02897993,0.0211649,-0.00602356,0.017192,-0.02688348,-0.21497756,-0.02512737,-0.05734594,0.00030204,-0.0051318,-0.0228347,0.03442272,-0.03223286,0.00906687,0.08720256,0.14741823,0.00379759,-0.03181351,0.0657691,-0.01364446,0.00067519,-0.0482753,-0.02105201,-0.02023339,0.05141313,0.00689107,0.00539146,-0.02023618,-0.02101184,0.06564836,-0.03943627,0.13091964,0.00647564,0.02826932,0.02586936,0.08655509,0.03938899,0.03205967,-0.06956152,-0.01783464,0.03373856,-0.03115371,0.00795386,-0.02053576,-0.00574145,-0.07027379,0.00060647,0.02981849,-0.06533559,-0.01102,0.03194121,-0.02131852,0.00022448,0.00321316,0.00470249,0.03808894,-0.0273908,0.02287359,0.01527638,0.07573257,-0.04426233,-0.06466672,-0.02988247,0.01702278,-0.02522486,-0.00135222,-0.0181135,0.02722421,-0.03895479,0.01838812,0.01021363,0.03194349,-0.0166144,0.04191064,0.03722469,-0.006457,0.02986675,-0.03968851,0.06326847,-0.01915157,0.05081051,0.01176542,-0.0214906,-0.03291615,-0.0437901,0.06619927,-0.00572096,0.03429003,0.06418635,0.04234562,0.04656525,0.04224823,0.01409659,0.04312995,-0.02803555,0.0062686,-0.02188646,-0.03982868,0.02152377,-0.01439315,-0.00522116,-0.27123928,-0.00710325,-0.05116313,0.04626111,0.00524411,-0.01197062,0.02673122,0.00563067,0.0573909,-0.01898088,0.04765386,0.05546855,0.01857273,-0.06643925,-0.02947682,-0.02498494,0.00875948,-0.01458159,0.09400781,0.00522325,0.07594644,-0.00840067,0.25680381,-0.00811779,-0.02128616,-0.00084038,-0.00181531,-0.01268537,-0.01371153,0.03175655,-0.01353584,0.00600185,0.08137403,-0.02342404,-0.01770582,0.0234361,0.02097067,0.07676507,0.00160307,0.00683612,-0.06487056,-0.01838046,-0.01524913,-0.02922942,0.0773994,0.01649793,-0.01966941,-0.0510828,-0.02132562,0.0534458,-0.06096736,-0.04851083,-0.05408148,-0.06192214,0.01451154,0.04062087,-0.0131104,0.00542664,0.0357496,0.02330187,0.06298244,-0.01836911,0.06379241,0.02401471,-0.0050442],"last_embed":{"hash":"1el71p2","tokens":402}}},"text":null,"length":0,"last_read":{"hash":"1el71p2","at":1753423449112},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>","lines":[93,118],"size":4485,"outlinks":[{"title":"Software for lotto, lottery to cross-reference strategy files created by MDIEditor, DOS programs.","target":"https://saliu.com/ScreenImgs/reference-strategies-file.gif","line":5},{"title":"_**HeaderFilterFiles.zip**_","target":"https://saliu.com/pub/HeaderFilterFiles.zip","line":7},{"title":"_**Lottery Strategy, Systems, Software Based on _Lotto Number Frequency_**_","target":"https://saliu.com/frequency-lottery.html","line":19},{"title":"Combine many lottery strategies and the player increases the chance to win big.","target":"https://saliu.com/ScreenImgs/cross-reference-report.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09262812,-0.03993498,-0.01084323,-0.04397601,-0.01528733,0.08036081,0.02316697,-0.03229658,0.00903817,-0.01036669,-0.00906633,0.01280158,0.06977611,-0.00823535,-0.03927676,-0.03730164,-0.01725923,-0.01828616,-0.06425363,-0.01425264,0.05077083,-0.0253669,-0.01267381,-0.08457887,0.01973298,-0.01922523,-0.02141839,-0.0474531,-0.08753531,-0.23833556,0.03591737,-0.00036044,-0.0028121,-0.02629084,-0.08022443,-0.05459295,-0.02176539,0.09531517,-0.03896647,0.05332347,0.05283666,-0.00673092,0.02376503,0.02271103,-0.01584108,-0.04592946,-0.03424138,-0.00838298,0.06023544,-0.00427131,-0.07080322,0.00779807,0.0278183,-0.00642887,0.01832291,0.02472723,0.06713118,0.09939371,0.04512856,0.03787972,0.02373686,0.07075148,-0.18007423,0.06229046,-0.02872041,0.01942587,-0.01655901,-0.03607705,0.00092317,0.02091887,-0.04086803,0.02870863,-0.00595787,0.09560931,0.06093337,-0.04606581,-0.03434476,-0.08389722,-0.07009297,0.024794,-0.03981314,-0.00201447,0.00320053,-0.00047673,0.02594434,0.08781537,0.05993601,0.0680048,0.06136051,-0.03421823,0.0806687,-0.02132884,0.01553331,0.05959187,0.01908357,-0.01977996,0.0494914,0.00551285,-0.0041918,0.12418338,-0.00594228,-0.03721505,0.00148564,0.02751201,0.02996282,-0.02166886,-0.02009139,-0.00357615,0.00181563,0.05627753,0.02908755,-0.01841029,0.06171818,-0.08089104,-0.01913478,0.05734612,-0.03939258,-0.02399203,-0.00669813,0.00140663,-0.03235314,-0.00271322,-0.00592525,0.00439372,-0.02346217,0.02026626,0.03681133,0.04933363,0.03995407,0.02725275,0.01135231,-0.00721857,-0.16444759,-0.06699784,0.0075895,-0.00098707,-0.01629551,-0.0353738,-0.01179615,-0.02112888,0.01636766,-0.03420596,0.05724984,-0.11486717,-0.03057874,0.10007744,0.00261138,0.00946339,0.03521215,-0.05212055,0.02611298,0.03474326,-0.06371565,-0.04705252,-0.01886593,0.02090275,0.08951873,0.08115593,-0.01919984,0.00958789,-0.04627026,-0.0149852,0.0036119,0.10528894,-0.01173853,-0.09067439,-0.06173101,0.02339002,-0.03674159,-0.06313727,-0.01022291,-0.0210889,-0.03378936,-0.02661217,0.08037196,-0.02117424,-0.07177886,-0.05421967,-0.02408783,0.01125024,0.00226336,-0.04160619,-0.08108949,0.03541621,-0.04243447,-0.08312568,0.02476482,-0.05437947,0.01688196,0.09313636,-0.06909435,0.01956481,-0.00753486,0.02359025,0.00055624,0.00765326,-0.03308571,-0.02905503,0.02480976,-0.02118691,0.02754326,0.02218905,0.0046839,0.03193972,-0.06221436,0.05112368,-0.00504102,-0.04047628,0.10397187,0.03154437,-0.03838574,0.01353372,0.07788774,0.05351787,-0.05475559,0.01364888,0.01653923,-0.00134312,0.02640097,0.02128779,-0.00324156,0.01861987,-0.027118,-0.21414433,-0.02462395,-0.05804765,-0.00105206,-0.00443841,-0.02559876,0.03519713,-0.02995031,0.01451539,0.08462191,0.14909665,0.00465007,-0.029442,0.06996149,-0.01037331,0.00158055,-0.04349026,-0.02742406,-0.01870006,0.05587287,0.00542604,0.00674402,-0.01546255,-0.0188108,0.06433241,-0.04398039,0.13097414,0.00722717,0.03052377,0.02544236,0.08593909,0.03537606,0.02988002,-0.07157443,-0.01809742,0.0323857,-0.02974924,0.00685822,-0.02186063,-0.00563077,-0.06803307,-0.00213599,0.03034158,-0.06860739,-0.00645249,0.03501393,-0.01860906,0.00014514,0.00355111,0.00285138,0.04073493,-0.02505599,0.021888,0.0183736,0.07642571,-0.04562311,-0.06557918,-0.02815604,0.01962331,-0.02781066,-0.00351973,-0.01896539,0.03166522,-0.04188765,0.02181079,0.01430103,0.0352134,-0.01721164,0.04295763,0.03765682,-0.00819367,0.03130837,-0.03924929,0.0670695,-0.02495486,0.05046829,0.01458732,-0.02280787,-0.0327416,-0.04078567,0.06285216,-0.00920789,0.03427094,0.06265788,0.04230478,0.0475626,0.04655899,0.01223921,0.04084213,-0.02941109,0.00568803,-0.02219086,-0.04131006,0.0234008,-0.01795209,-0.00328423,-0.26996848,-0.00802214,-0.05392741,0.04399906,0.00760285,-0.01080074,0.02800931,0.00209615,0.05617508,-0.02022334,0.04442022,0.05495855,0.02440071,-0.06662148,-0.02718654,-0.02198358,0.01027261,-0.01359697,0.09249963,0.00471323,0.07808083,-0.00752427,0.25794545,-0.00780281,-0.02607806,-0.00040399,-0.00234067,-0.01458868,-0.01486129,0.03317052,-0.01456583,0.0093682,0.08034928,-0.02030249,-0.01432787,0.02646485,0.0190686,0.07644531,-0.00160136,0.00680022,-0.06478535,-0.02187043,-0.01725672,-0.03033325,0.08029196,0.01690756,-0.01993425,-0.05127193,-0.02213581,0.04988082,-0.06132472,-0.05179183,-0.05780678,-0.05845932,0.01605473,0.04234736,-0.01706973,0.00572042,0.03607386,0.02395766,0.06283077,-0.01554115,0.06313354,0.02092019,-0.00465158],"last_embed":{"hash":"1cq1x9","tokens":363}}},"text":null,"length":0,"last_read":{"hash":"1cq1x9","at":1753423449395},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{1}","lines":[95,100],"size":1260,"outlinks":[{"title":"Software for lotto, lottery to cross-reference strategy files created by MDIEditor, DOS programs.","target":"https://saliu.com/ScreenImgs/reference-strategies-file.gif","line":3},{"title":"_**HeaderFilterFiles.zip**_","target":"https://saliu.com/pub/HeaderFilterFiles.zip","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09812167,-0.03443314,0.0083149,-0.023589,-0.00240551,0.06000384,0.00848548,-0.01379336,-0.00245743,-0.0074722,-0.01352013,0.01826807,0.06977203,0.00906166,-0.0615042,-0.00687559,-0.04311145,0.01224338,-0.03869287,0.02603756,0.03995328,-0.01796472,-0.01352982,-0.0691926,0.03200968,0.0159166,-0.00888616,-0.01665895,-0.08300254,-0.23676978,0.01005292,-0.01840179,-0.00597665,-0.03236493,-0.04642654,-0.03216314,-0.03239781,0.09158914,-0.01822562,0.05515357,0.08118197,-0.03280063,0.05581379,0.06126636,-0.03298359,-0.06608201,-0.05260476,0.01888404,0.04475537,-0.02324154,-0.04131447,-0.00338503,0.03162078,-0.03095617,0.02755963,0.01303693,0.05606243,0.10093474,0.01250162,0.02848146,-0.00005848,0.08191793,-0.20604742,0.05489284,-0.04799209,0.0292409,-0.0079393,-0.00600158,-0.0148308,0.06637054,-0.02556842,0.00149101,0.01719225,0.09199321,0.04656107,-0.05817861,-0.01907461,-0.10393644,-0.05014273,0.01310024,-0.04611712,0.01534123,-0.01453736,0.0038694,0.0122752,0.08635829,0.04789886,0.05513237,0.06235607,-0.02827971,0.05964106,-0.02275847,0.01586876,0.02920006,-0.00090611,-0.02783496,0.06462388,-0.00405651,-0.00683659,0.12087443,0.00012621,-0.03146087,0.01262467,-0.0229787,0.03698586,-0.01876056,-0.03887915,-0.02709269,-0.02571042,0.04638292,0.01654645,-0.01855947,0.04621268,-0.1016164,-0.00149057,0.01999884,-0.05151793,-0.0159435,-0.0274218,-0.01738013,-0.0205161,-0.01432152,0.03131056,0.00800113,-0.03728809,0.01196856,0.04692318,0.04915603,0.05005177,-0.01760865,-0.00834725,-0.02410683,-0.13165741,-0.04294185,-0.00172592,0.01661791,-0.00817986,-0.0308611,-0.0044916,-0.00367833,0.0419123,-0.05084674,0.03741698,-0.12733074,-0.01542672,0.09107566,-0.02163,0.00282516,0.01191358,-0.00708939,0.01306666,0.05491922,-0.04922603,-0.04240781,-0.02740745,0.03461969,0.08172864,0.07973082,-0.00071195,0.00318235,-0.02298748,0.01383105,-0.01339028,0.14850079,-0.04821577,-0.08483218,-0.03367861,0.03142601,-0.00745066,-0.05559949,-0.00901726,-0.0143994,-0.01556585,-0.05019768,0.08330186,-0.0185503,-0.04756686,-0.03311094,-0.02167498,0.00632849,0.00485347,-0.07928885,-0.08450473,0.06154447,-0.02089887,-0.04364251,0.01137982,-0.04099725,0.01546805,0.09133897,-0.05898481,0.01007336,-0.02308086,0.00088551,-0.02182922,-0.00825545,-0.02212713,-0.0219687,0.03066492,-0.02107226,0.0730889,0.03968809,-0.01997466,0.01457007,-0.04634361,0.0250004,-0.00429678,-0.03340637,0.09489859,0.04154309,-0.04727171,0.01897163,0.08450575,0.0475341,-0.07107618,-0.02237608,0.01320853,0.03648928,0.01066187,-0.00076806,-0.00489931,0.01263701,-0.01120895,-0.23124596,-0.04996527,-0.03172576,-0.02390782,-0.02991234,-0.01200748,0.03863495,-0.01003887,-0.00642692,0.05197593,0.13148168,0.02474637,-0.0322655,0.01170442,-0.01839587,-0.01199692,-0.04526992,-0.0442875,-0.01298806,0.06390614,0.01882124,-0.00488129,-0.03625301,-0.02873852,0.06456373,-0.0405714,0.10654034,0.03237335,0.04325399,0.01159283,0.08172966,0.044156,0.01937767,-0.06621314,-0.01408227,0.05159064,-0.02508936,0.01017572,-0.01791364,-0.02823838,-0.06899032,0.00571206,0.00696328,-0.05333687,0.02048622,0.0116124,-0.00943479,-0.02956529,0.00865261,0.02887466,0.03873012,-0.02082203,-0.0048433,0.0404857,0.05613423,-0.0283828,-0.07620145,-0.04881603,0.01295127,-0.02627449,0.00584632,0.00129004,0.04154356,-0.03062132,0.03053791,-0.01910535,0.00972285,-0.01604282,0.03219523,0.02924795,-0.01534085,0.01226099,-0.02934541,0.05272215,-0.0133198,0.04873031,0.00035828,-0.04952117,-0.03783482,-0.0162506,0.08671581,0.00936116,0.04618457,0.05533786,0.05676509,0.04845404,0.02421637,-0.00744278,0.05557424,-0.01267573,-0.00012567,-0.03546308,-0.01011942,0.01870658,-0.01124979,0.00540323,-0.2986851,-0.01602035,-0.03127014,0.03126938,-0.01084134,-0.00321074,0.02888719,-0.00948686,0.01405206,-0.01605993,0.05558416,0.06374016,-0.00582004,-0.04159957,-0.05063408,-0.05318882,0.03780813,0.00450749,0.09940829,0.01779922,0.057447,0.00393983,0.25527596,0.00690765,0.0041894,0.00432349,0.00244305,-0.00561939,0.02854243,0.0365248,0.02277756,0.03071337,0.06521165,-0.04426858,-0.01043863,0.06112598,0.02131229,0.08114406,0.00752311,-0.00707137,-0.07597514,0.02875905,0.01367923,-0.04530836,0.07363608,0.000793,0.01348986,-0.0720615,-0.02019065,0.0496913,-0.07229841,-0.03574548,-0.04614476,-0.06588847,0.01727347,0.04786722,0.02133259,-0.0299955,0.04916818,0.01036924,0.05137586,-0.03777695,0.07595011,0.04654548,0.01368204],"last_embed":{"hash":"1pd22z6","tokens":160}}},"text":null,"length":0,"last_read":{"hash":"1pd22z6","at":1753423449534},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{2}","lines":[101,101],"size":219,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10788839,-0.04669522,0.01112467,-0.04209824,-0.03904818,0.06471495,-0.01108688,-0.03673996,-0.00464661,-0.01412511,-0.03749669,0.00021731,0.0721329,0.0166781,-0.03298894,-0.05111565,-0.02957145,0.01632057,-0.04350118,0.00150619,0.06498587,-0.02409962,-0.01466968,-0.08712813,0.00426672,-0.00696363,-0.02273776,-0.05460287,-0.08418252,-0.22791569,0.02316334,-0.02283202,-0.0231523,-0.01932769,-0.04407616,-0.02222593,-0.02072592,0.07980153,-0.06237796,0.06290497,0.04933572,-0.01045495,0.05468794,0.03239883,-0.03158027,-0.06778544,-0.04703795,0.03863681,0.03533828,-0.00683139,-0.04525173,-0.00796324,0.01424944,-0.01273977,0.00565833,0.02097976,0.06054551,0.10270476,0.00924445,0.05317955,0.01431846,0.05235812,-0.18233077,0.04852629,-0.03503172,0.0456922,-0.01617667,-0.01909183,0.00917343,0.04049706,-0.04300265,0.0014253,-0.00053366,0.09879431,0.05790885,-0.05475532,-0.02430082,-0.09398644,-0.05104303,0.01982196,-0.03967213,0.01974015,0.00306257,0.01743989,0.01114521,0.09468941,0.0510976,0.05466011,0.04343408,-0.04364698,0.04858952,-0.03018,0.02697172,0.01490181,0.01173516,-0.03239474,0.05311172,0.00520089,-0.02168864,0.12036429,-0.01440095,-0.03899737,0.02916015,-0.00205214,0.041805,-0.02801396,-0.03722578,-0.02927747,-0.02584982,0.04442447,0.00576281,-0.02026062,0.05094159,-0.08061414,-0.00390327,0.03479907,-0.03457383,-0.03136591,-0.04020366,0.00763971,-0.02074658,-0.00246892,0.00906129,0.03833101,-0.03067528,-0.01782889,0.02253323,0.04707886,0.01361269,0.00613508,-0.009359,-0.00285536,-0.12401617,-0.07425129,-0.01407412,0.02254576,-0.01924833,0.00947118,-0.01944633,-0.03127399,0.02092157,-0.06125393,0.05498786,-0.07206254,-0.00702455,0.0786078,-0.00930387,0.01775554,0.01172937,0.00859373,0.00746255,0.04114821,-0.0544645,-0.03501407,-0.00267907,0.02401372,0.05421257,0.05107275,-0.00661243,0.0069969,-0.0291427,0.03322968,-0.02539188,0.17101593,-0.02873313,-0.08821343,-0.01315485,0.04301671,-0.00563157,-0.03104728,-0.02465414,-0.02120193,-0.00904837,-0.04242181,0.08766419,-0.021003,-0.0548019,-0.04374341,0.00980475,0.02635415,0.01348231,-0.04564093,-0.08360749,0.05724298,-0.00841568,-0.05739532,0.01933585,-0.07927232,0.01421701,0.0985816,-0.0547549,0.00835748,-0.03047292,-0.01342022,-0.00236382,0.01678956,-0.03782644,-0.03382253,0.0389451,-0.00605298,0.0803955,0.03894264,0.00414602,0.00783955,-0.04382084,0.04776685,0.00414185,-0.03863758,0.10245739,0.03727854,-0.04658362,0.02183786,0.08946061,0.04751116,-0.05511388,0.01322829,0.00791258,0.00461122,0.03239454,0.02432146,-0.02744887,0.0111462,-0.01930639,-0.23932087,-0.03094723,-0.03508684,-0.0325845,-0.00384181,-0.01748572,0.02657733,-0.00225055,-0.02927723,0.06259976,0.15025012,0.01384945,-0.02415432,0.04871541,-0.00461576,-0.00560725,-0.05922296,-0.04706577,-0.03119376,0.06959052,0.00811245,-0.00536679,-0.01124843,-0.0555381,0.05247362,-0.01550582,0.10808412,0.01280901,0.0573604,0.02412672,0.10300189,0.0322863,-0.0015175,-0.05586009,-0.00910298,0.0579395,0.00049786,-0.00723105,0.01263551,-0.00325194,-0.06071398,0.00401429,0.00378538,-0.06486807,0.02529932,0.00481148,-0.03513518,-0.02392647,0.03008882,0.04033493,0.0061091,-0.010049,0.0182752,0.02265231,0.06564926,-0.04013397,-0.07765245,-0.02932353,0.01562978,-0.01765835,0.01043773,-0.02142778,0.03024668,-0.03023349,0.00613299,0.00856618,0.02360199,-0.02442414,0.04712781,0.03678361,-0.00818306,0.03861839,-0.00367037,0.02830797,-0.0474548,0.048529,0.01779757,-0.04787304,-0.0462299,-0.03400165,0.05806446,0.00726935,0.05560267,0.0647017,0.02554709,0.04290432,0.02393806,0.00255142,0.05714248,-0.02082217,0.00246342,-0.04198654,-0.03116572,0.04233913,0.01243783,-0.01931766,-0.30408952,-0.00846466,-0.03380666,0.03018441,-0.01172773,0.01265247,0.02433982,-0.02091473,0.01039829,-0.04794453,0.01856564,0.05897224,0.01452426,-0.04699607,-0.02001336,-0.02512402,0.04006127,-0.03759796,0.12229089,0.00926506,0.05206376,0.01334156,0.25789371,0.00203106,0.01280455,0.01505185,-0.00095935,0.03512561,0.03781291,0.0395714,-0.0140952,0.02134534,0.05766135,-0.02211905,-0.00959026,0.03910657,0.00512955,0.0698319,0.01262804,0.02491438,-0.09284426,0.00283182,0.00126119,-0.03764922,0.08362544,0.00205877,0.01473611,-0.04422961,-0.01532492,0.04580108,-0.06992802,-0.03379866,-0.05021076,-0.06292165,0.05278211,0.05647974,0.00939242,-0.03337666,0.03378185,0.01732934,0.0823088,-0.05074564,0.06617387,0.03688093,-0.01040101],"last_embed":{"hash":"1wkr9vt","tokens":182}}},"text":null,"length":0,"last_read":{"hash":"1wkr9vt","at":1753423449580},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{6}","lines":[105,105],"size":280,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11541159,-0.02704212,0.00850636,-0.0491286,-0.0318784,0.072695,0.03643681,-0.02132794,0.0223284,-0.00600753,-0.00600709,0.025228,0.07139396,0.00427882,-0.05770591,-0.03888433,-0.04187236,0.00362951,-0.02720996,-0.00555451,0.05015402,-0.01793456,-0.0282926,-0.07608294,0.01600676,0.01880006,-0.00871245,-0.04097993,-0.07693534,-0.23220494,0.03468064,-0.01076332,0.00770569,-0.06752682,-0.05018898,-0.04383589,-0.03322763,0.10135636,-0.00049465,0.05646435,0.03077907,-0.03421384,0.05415859,0.03824876,-0.01788843,-0.06135368,-0.03451493,0.01760513,0.06912961,-0.02884069,-0.04688014,0.01204003,0.04913355,-0.02985096,0.02390872,0.01193177,0.05704069,0.09084605,0.01871669,0.04323992,-0.0021127,0.06186882,-0.19976117,0.06767978,-0.07438828,0.0323406,-0.01665553,-0.00255149,-0.0220305,0.06262337,-0.0328506,-0.01223548,-0.01208219,0.09785035,0.05976609,-0.03626387,-0.0501317,-0.0940163,-0.0597582,0.018496,-0.04051893,-0.00750154,-0.0055757,0.01451065,0.00451541,0.09968992,0.06148526,0.06181851,0.06611771,-0.03280789,0.07713619,-0.01880619,-0.01212261,0.04958716,-0.01725938,-0.00305317,0.06048239,0.00487128,-0.01323991,0.11994912,0.0138567,-0.03471594,0.01723171,0.01306,0.02310844,-0.01242665,-0.02002806,-0.05358407,0.00580366,0.04815667,0.00685295,-0.01622977,0.03218883,-0.08759661,-0.0019787,0.04118706,-0.03169136,-0.02570898,-0.01978962,-0.01388212,-0.01782717,-0.01877149,-0.00064013,0.01670706,-0.02973173,0.03423724,0.02638626,0.06115488,0.02633854,-0.01608654,0.01970435,-0.01562602,-0.15650964,-0.05119897,-0.01332618,0.01358526,-0.01616571,-0.01663748,-0.01189008,-0.02235167,-0.01219008,-0.04293899,0.03583249,-0.10024146,-0.02360067,0.10134544,-0.00245203,0.00313546,0.01990735,-0.03003455,0.03921138,0.02936443,-0.08276956,-0.03237273,-0.03331566,0.04387822,0.05240502,0.06966369,-0.0140481,-0.01146484,-0.04845899,-0.01522272,-0.0412994,0.15631661,-0.0232521,-0.09357643,-0.05466817,0.02293719,-0.03398751,-0.05764455,-0.00924233,-0.04080195,-0.03067316,-0.01146572,0.07002193,-0.02112503,-0.05617373,-0.0281924,-0.03447942,0.01941447,0.0246564,-0.04540247,-0.0791629,0.03077776,-0.03020632,-0.04673559,0.013278,-0.05173371,0.02834479,0.08647513,-0.05203129,0.02026966,-0.00837853,0.01468623,-0.03549561,-0.00853086,-0.0187341,-0.0260017,0.02964005,-0.02201295,0.08268695,0.0277825,-0.00385759,0.03708244,-0.07079462,0.03470284,-0.0045788,-0.03218783,0.09492972,0.04359098,-0.04136295,0.02814125,0.08390752,0.03998001,-0.0358454,0.00487885,0.01980633,-0.00345745,0.01860446,0.01410168,0.0087274,0.05727381,-0.02546773,-0.21299179,-0.05212786,-0.02578444,-0.00568311,-0.03650304,-0.01067176,0.01492598,-0.01643906,0.01595042,0.06017898,0.1374947,0.01165047,-0.03551239,0.05396128,-0.03188589,0.00797219,-0.05424872,-0.03868068,-0.02243637,0.03562248,0.03535614,-0.00304027,-0.05871388,-0.02381183,0.07716521,-0.03796586,0.1136172,0.01554581,0.053848,0.04011907,0.09171116,0.03535774,-0.0022707,-0.04566652,0.00551712,0.03265457,-0.04182228,-0.00762923,-0.04255839,-0.01667186,-0.04931574,-0.0107548,0.01008713,-0.06592827,0.00742628,0.00710084,-0.00628682,-0.01448434,-0.01149125,0.02199407,0.00743635,-0.01309563,0.01196928,0.03717089,0.0710296,-0.02710732,-0.06493919,-0.03471731,0.00954408,-0.00941816,-0.00592407,0.0004531,0.03807332,-0.04793892,0.01098029,-0.00615381,0.01732842,-0.00953562,0.0455628,0.05326787,0.00699669,0.03117167,-0.02342211,0.05345752,-0.028022,0.02568815,0.00769546,-0.04521828,-0.02092569,-0.03510518,0.07041669,0.02005097,0.04830817,0.05069545,0.04694156,0.05159555,0.04539822,-0.01212711,0.05550384,-0.03952889,0.01001923,-0.03194585,0.00298652,0.01142554,-0.00412055,-0.0136877,-0.2959649,0.00060525,-0.04304765,0.04174349,-0.01116994,0.00904027,0.01610755,0.00443766,0.02506863,-0.01206397,0.05031902,0.05469833,0.01516595,-0.02334287,-0.06005658,-0.04392328,0.03053208,-0.01061907,0.09517216,0.0121734,0.0724187,-0.00595265,0.2428491,-0.0007254,0.00343961,-0.02233169,0.00886179,0.00957573,0.00003982,0.02973377,0.01100722,0.00611634,0.0826153,-0.01910773,-0.00245342,0.05776951,0.01597107,0.06983229,-0.01563735,-0.00521452,-0.07641491,0.00976915,-0.01375816,-0.02705332,0.0787058,0.00917996,0.01374105,-0.04725694,-0.01702252,0.06530553,-0.06993873,-0.03750636,-0.05698604,-0.03907168,0.02657581,0.06317287,0.01598246,-0.00960024,0.04940918,0.01521635,0.04706092,-0.02552727,0.08330363,0.04262375,-0.02293684],"last_embed":{"hash":"ncrvcp","tokens":168}}},"text":null,"length":0,"last_read":{"hash":"ncrvcp","at":1753423449639},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{10}","lines":[109,110],"size":331,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0986337,-0.0577467,-0.01419192,-0.0178909,-0.01256081,0.07392099,0.05449767,-0.03914846,0.0269175,-0.00781041,0.00677762,0.01798534,0.06009228,-0.00210864,-0.04433634,-0.01505846,-0.02301094,0.04361503,-0.03709351,0.00415637,0.04101204,-0.0338311,0.0123658,-0.06903873,0.03126059,-0.00257269,-0.00096984,-0.04948236,-0.10604603,-0.24415267,0.01945213,0.00022727,-0.01858353,-0.01677853,-0.08521933,-0.05971793,-0.03173273,0.09439104,-0.04374402,0.05719433,0.06160562,-0.00524038,0.04031897,0.03940856,-0.02863326,-0.04735626,-0.04305101,0.00510019,0.0676071,0.00747321,-0.06140533,-0.01159438,0.00525329,-0.02619523,0.02821453,0.039974,0.0854262,0.0719539,0.02946977,0.00900894,0.02141315,0.07235964,-0.1756264,0.08231163,-0.05247859,0.02616151,-0.02641446,0.0149223,0.02062386,0.04757561,-0.02201707,0.01429946,-0.00860415,0.11891019,0.07909388,-0.05800248,-0.03742476,-0.07049551,-0.0451865,0.00637082,-0.05112528,0.00107992,-0.02859017,-0.02556956,0.00717754,0.1302823,0.06897885,0.0405877,0.06678926,-0.03246703,0.07905116,-0.02540468,0.02812061,0.03245786,0.00487335,-0.04276362,0.06240498,0.01017997,-0.01772622,0.11897127,-0.0102314,-0.04165642,-0.0276022,0.02615341,0.01834172,-0.04748064,-0.0557124,-0.02126426,0.00063744,0.07056376,0.03895129,-0.01520628,0.0333029,-0.06920802,-0.01527933,0.06048502,-0.02541641,-0.02857837,-0.0000358,-0.0271365,-0.00845399,0.01642958,-0.01203686,0.02682532,-0.04909616,0.00039678,0.03310308,0.02561449,0.03589526,0.01289734,0.01466179,-0.01494579,-0.15599281,-0.06944772,-0.00612864,0.02162965,-0.01325198,-0.00846267,-0.02488547,-0.01860955,0.01936118,-0.05892879,0.06817569,-0.08423693,-0.03370544,0.07334582,-0.00760081,0.01063282,0.03537068,-0.03014154,0.03149967,0.03213723,-0.07262802,-0.04701381,-0.05533419,0.04343331,0.08594861,0.04972451,-0.00472974,-0.01093287,-0.04552355,-0.01090852,-0.00091697,0.12113861,-0.04765825,-0.102961,-0.06243701,0.0250671,-0.04286863,-0.07993919,0.0013868,-0.03504919,-0.0151933,-0.02380178,0.08123229,-0.01007325,-0.05418278,-0.07665984,-0.0138281,0.03221887,0.01742052,-0.0474537,-0.07273009,0.03184719,-0.01455033,-0.05769932,-0.00038289,-0.06267375,0.0321838,0.02781411,-0.06104416,0.0268165,-0.02324832,0.00384205,-0.02301732,0.02621937,-0.03820549,-0.02674649,0.04433171,0.00403994,0.0517213,0.05411221,0.03242787,0.03269823,-0.06393397,0.02104836,0.00183313,-0.03977159,0.11074749,0.0151434,-0.02038747,0.01528951,0.04482724,0.0528382,-0.08450773,0.00115628,0.0173382,0.00643152,0.02713942,0.03717719,0.01176382,0.0266818,-0.02532601,-0.19349541,-0.04014986,-0.06272458,0.0136288,-0.02658763,-0.01235626,0.04386273,-0.01790969,0.0118578,0.05943954,0.12332899,-0.0258455,-0.03954685,0.0540701,-0.01465495,0.00394926,-0.06589282,-0.05085563,-0.01929662,0.06530999,-0.0217271,-0.01191926,-0.04140881,-0.03273554,0.04550361,-0.03039854,0.11839879,0.02542396,0.04456374,0.02776653,0.07389243,0.02783539,0.02512537,-0.06763905,0.01171566,0.02514179,0.00334384,0.00703999,-0.04148792,-0.0119376,-0.05332834,-0.00515905,0.04162274,-0.07750873,0.00044676,0.01415179,-0.02874036,-0.0538823,0.02038389,0.02947115,0.05587978,0.00207597,-0.00081472,0.04311824,0.05072385,-0.02517208,-0.06628085,-0.0059446,0.03204755,-0.0195374,0.00413973,-0.01628884,0.04064421,-0.0448189,0.04182534,0.01332823,0.01762215,-0.00823965,0.04526107,0.01269321,0.01929692,0.05495888,-0.02133117,0.04270493,-0.00561052,0.03727644,0.02625269,-0.03464065,-0.03579221,-0.01142687,0.06502615,0.00107511,0.06637375,0.04987673,0.03636524,0.02875141,0.0501062,0.0170603,0.04263705,-0.02354642,0.00701915,-0.01197875,0.00744261,0.04059737,-0.02885448,-0.00186302,-0.27653742,0.02970141,-0.04906046,0.02446392,-0.01108556,-0.0323638,0.02688602,-0.03744556,0.02804168,-0.00952725,0.06471346,0.03760113,0.00659159,-0.05860801,-0.0329439,-0.00646587,0.01518858,-0.00435051,0.08947773,-0.0023365,0.08304473,-0.00748022,0.26655272,-0.02441213,0.01378276,0.00697223,-0.00576107,0.03867703,0.00965277,0.04430882,-0.00581979,0.00297618,0.03758779,-0.0135723,0.0028642,0.03858582,0.00040245,0.05940846,-0.00092315,-0.01314856,-0.0713811,-0.02851344,-0.0252585,-0.03487012,0.08415087,-0.00855118,-0.01088132,-0.03241953,-0.01636156,0.05986017,-0.07018337,-0.05628853,-0.05576507,-0.05797913,0.01876168,0.0153359,0.00865357,-0.01016695,0.02868682,0.00617483,0.03146964,-0.00798684,0.08053603,0.01925755,0.00213435],"last_embed":{"hash":"c83rm9","tokens":439}}},"text":null,"length":0,"last_read":{"hash":"c83rm9","at":1753423449692},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>B. Script Operation: <i>F = File Input</i></u>#{11}","lines":[111,118],"size":1425,"outlinks":[{"title":"_**Lottery Strategy, Systems, Software Based on _Lotto Number Frequency_**_","target":"https://saliu.com/frequency-lottery.html","line":1},{"title":"Combine many lottery strategies and the player increases the chance to win big.","target":"https://saliu.com/ScreenImgs/cross-reference-report.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08648342,-0.04880915,-0.00904796,-0.01871504,-0.03928966,0.09505655,0.00252146,-0.02447925,0.02019468,0.01740018,0.01957354,0.00689215,0.05160037,0.00426937,-0.0536385,-0.0039133,-0.00897747,0.00531345,-0.05410807,0.01559778,0.0216413,-0.02231507,0.00247864,-0.09126569,0.04348123,0.01130171,-0.03275903,-0.0589994,-0.0801048,-0.2452272,0.00157257,-0.01125832,-0.01339686,-0.02949085,-0.05358608,-0.06472336,-0.03819104,0.1419584,-0.02232526,0.03990775,0.05594041,-0.00736821,0.00654889,0.0347486,-0.0167961,-0.03927354,-0.03605222,0.03419335,0.06822175,-0.02562354,-0.04589143,-0.00587573,0.02454438,-0.0242678,0.02709397,0.04731888,0.09447918,0.06522097,0.01944315,0.03937156,0.01983907,0.05998086,-0.17968039,0.06656096,-0.07105723,0.02303924,-0.02839719,0.01506883,0.00592201,0.05856302,-0.02255974,-0.00078188,0.00922637,0.10313889,0.06725974,-0.0510549,-0.03691496,-0.0808076,-0.06755269,-0.00730057,-0.06693455,0.01312302,-0.02161692,0.01721104,0.04372158,0.10037452,0.04685117,0.06220318,0.07983682,-0.02281561,0.09899939,-0.0042978,0.00423466,0.00801429,-0.03036963,-0.02300358,0.04363319,-0.01793068,-0.00744454,0.13708365,-0.00433255,-0.05401103,0.04195666,0.04523677,0.01662412,-0.02350768,-0.01743948,-0.01891785,-0.01575244,0.05669161,0.04292791,-0.00092561,0.02486813,-0.08804223,-0.02734881,0.05174018,-0.04023904,-0.02867225,-0.01082091,0.01412156,-0.0303207,0.01142644,-0.02204897,-0.01259663,-0.03799637,-0.00248513,0.04207673,0.05254455,0.04163185,0.02481767,0.00422184,0.02887946,-0.16142492,-0.04895058,-0.02483975,-0.01858179,-0.00088396,-0.01762388,0.02705102,-0.03456509,0.00462291,-0.01239731,0.05373767,-0.10835674,0.00011593,0.07820941,-0.01709561,0.02910411,0.0222486,-0.0362363,0.02087829,0.04168369,-0.04703143,-0.03766705,-0.03112478,0.04627853,0.06690911,0.07940494,0.01330332,0.03845331,-0.05183083,-0.00583999,-0.02145169,0.14175905,-0.03620219,-0.09408441,-0.03434087,0.0304404,-0.01525379,-0.07310037,-0.02380993,-0.01808753,-0.02414023,-0.04487327,0.09838627,-0.00283914,-0.0807633,-0.07408808,-0.0044793,0.01644518,0.05769894,-0.05800133,-0.08216465,0.04910257,-0.02061669,-0.06612172,0.01236514,-0.04124868,0.04954021,0.06550547,-0.06578678,-0.00031847,-0.04227326,0.01682106,-0.03252601,-0.0095154,-0.02719945,-0.04974313,0.02580103,-0.01567916,0.07480331,0.00768751,-0.00676505,0.04033278,-0.05238072,0.01496855,0.00701164,-0.05879379,0.12244741,0.02424776,-0.04480267,0.01516039,0.07173394,0.0456109,-0.05825299,-0.00063757,0.01648201,0.0102363,0.02869423,0.05517245,-0.00632328,0.04259511,-0.03565559,-0.22385009,-0.05867109,-0.02414003,0.03124579,-0.01422444,0.00951926,0.02017592,-0.02026087,0.0151638,0.05662168,0.14846595,-0.01546337,-0.01407162,0.0333456,-0.00421316,-0.02744156,-0.06377931,-0.02081019,0.00602654,0.04460149,0.00059896,-0.01687017,-0.01904577,-0.02798739,0.0664947,-0.02436713,0.12777004,-0.00318039,0.01057522,-0.00659351,0.06520826,0.03691623,-0.03246766,-0.06885274,0.00195011,0.06124433,-0.02088767,0.02537931,-0.04927857,-0.01895265,-0.02702524,0.02008857,0.00856592,-0.07487269,-0.03241957,0.00312892,0.01726779,-0.04694589,0.01624138,0.02208479,0.02889781,-0.01402551,0.0446308,0.04785126,0.04378392,-0.03465426,-0.07096198,0.00112297,0.01787279,-0.02205099,0.00042779,0.00636607,0.05187227,-0.03442468,0.0296237,0.02256552,0.00563005,-0.02968277,0.03619684,0.02519838,0.02021783,0.02687336,-0.0285556,0.03221567,0.00509759,0.0472794,-0.00152405,-0.0358813,-0.02359805,-0.01953919,0.04330279,-0.001424,0.06090508,0.06536994,0.0067696,0.03857112,0.03221949,-0.01153741,0.04946809,-0.00810354,-0.02178646,-0.04121663,-0.01641246,0.02772212,-0.01313638,0.003354,-0.25479141,-0.01012614,-0.0531878,0.04069267,-0.00885996,-0.0037033,0.03300799,0.00078875,0.0280447,-0.00998704,0.03964747,0.02725951,0.0254657,-0.07274602,-0.01931547,-0.04877468,0.03570229,-0.01520178,0.05839789,0.00072872,0.05837204,-0.02596799,0.24843152,-0.01865599,-0.01211944,0.00516481,0.01600167,-0.01456321,0.02869543,0.03076864,0.00046709,0.01263974,0.05173291,-0.00198543,-0.02131603,0.04969112,0.0106608,0.07184386,-0.01119152,-0.01499584,-0.04525322,-0.00514757,0.01300746,-0.0249735,0.06641977,0.01011758,-0.01603221,-0.01499304,-0.02478654,0.03148044,-0.07711484,-0.07630271,-0.07529213,-0.05752384,0.01560441,0.06059795,-0.01156728,0.00914848,0.03298432,-0.0045428,0.04211626,-0.03666279,0.05414384,0.01674949,-0.00518525],"last_embed":{"hash":"1g5dj47","tokens":296}}},"text":null,"length":0,"last_read":{"hash":"1g5dj47","at":1753423449845},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>2. Working with the <i>FileLines</i> Application</u>#<u>Recommended Steps in Running <span color=\"#ff0000\"><i>FileLines</i></span></u>","lines":[119,129],"size":836,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14031848,-0.04440221,0.01500481,-0.03871356,-0.02689204,0.06171463,-0.02453852,-0.00468466,-0.00487842,-0.00113395,0.00834643,0.02607767,0.05208074,-0.02498083,-0.01201918,-0.00792189,-0.03303093,-0.01100267,-0.05496134,0.02744397,0.01135463,-0.066473,0.01671367,-0.101689,0.02034824,-0.00938733,-0.01046952,-0.07768006,-0.09240192,-0.24751078,0.02915596,0.03539552,-0.01950851,-0.04790397,-0.04732794,-0.04516445,-0.02516158,0.05633036,-0.05677675,0.02145002,0.03314331,0.01249021,0.00779708,0.03467133,-0.0049172,-0.10696034,-0.02926549,-0.02196623,0.0876669,0.00887174,-0.02165671,0.03276614,0.0330413,0.00693653,0.00926363,0.02305819,0.0307581,0.07630486,-0.00277009,0.03414688,0.04481014,0.04765309,-0.15995078,0.10838615,-0.05841095,0.0221007,-0.00174545,-0.0092101,-0.00282087,0.03101523,-0.02768794,0.02252812,-0.02583472,0.10312218,0.06339012,-0.06776939,-0.05244377,-0.04255959,-0.05166491,0.01963435,-0.0447378,0.01429078,-0.01410442,0.0077233,-0.03727409,0.08169781,0.04828881,0.01649749,0.05729402,-0.04232058,0.04366685,-0.0178422,0.01271076,0.0333664,0.03309844,-0.04410034,0.07692994,0.0043341,-0.06059743,0.10538214,0.01616972,-0.04127361,-0.03065071,0.02546147,0.02409556,-0.03059793,-0.02869029,-0.03782899,-0.04387378,0.04435244,0.02673907,0.00738731,0.02965841,-0.04682114,-0.01719574,0.01663318,-0.03671762,-0.05440089,0.0259702,-0.00631082,-0.06876384,0.00663973,-0.00347176,0.01087795,-0.03493007,0.01266488,0.03063633,0.03849065,0.00807282,0.0098955,0.02554432,0.00275299,-0.12025186,-0.04757998,-0.00556082,0.01576041,0.00309277,-0.03573383,0.02747237,-0.05802736,0.00150208,-0.025941,0.09913545,-0.10546797,-0.00691852,0.1118261,-0.02224981,-0.00073677,0.01358641,-0.01337736,0.04299331,0.00546575,-0.05074116,-0.03931887,-0.00778653,0.01184143,0.12314572,0.07617072,-0.04161169,-0.02866452,-0.03121298,0.02100964,-0.01885485,0.12373211,-0.0118839,-0.09465636,-0.02992775,0.03442342,-0.01340817,-0.0661905,-0.02861373,-0.04387601,-0.03783595,0.03241974,0.1023907,-0.02987299,-0.10825005,-0.04691403,0.01517289,0.0062754,0.0083843,-0.00857247,-0.05472684,0.03367027,-0.00647919,-0.06582308,0.04606921,-0.06200749,0.00973656,0.04863936,-0.00813548,0.04193806,-0.02137757,0.00951348,-0.02788663,0.00146741,-0.04750512,-0.03225991,0.04612416,-0.00919711,0.04855716,0.00396169,-0.01760561,0.01704643,-0.06304711,0.01610748,0.01102555,-0.05273079,0.10968023,0.02622089,-0.02629473,0.03154507,0.06597429,0.04276393,-0.07987297,0.02689228,0.01816225,0.03103743,0.05566829,0.05085631,0.01013024,0.04565638,-0.0357619,-0.21458775,-0.02246707,-0.04811954,0.00873118,-0.01567009,-0.02529145,0.03403996,-0.01587006,0.0017961,0.03071084,0.13347885,-0.04819999,-0.01459835,0.05438739,-0.0140183,0.00934795,-0.04259073,-0.00717389,0.00786802,0.01286364,0.00995251,0.03480478,-0.0553974,-0.01020293,0.06161257,-0.02779356,0.11286614,-0.01209935,0.06285636,0.03346721,0.10545988,0.03830172,0.02808679,-0.06002486,0.00381793,0.09049121,0.01625276,0.00411159,-0.01379266,0.00050879,-0.07830539,0.02953963,-0.01010774,-0.11332144,0.00622229,-0.00344093,0.00123076,-0.03430349,0.00269012,0.05794851,0.03473453,0.01374363,0.00141771,0.05262236,0.04590829,-0.02017467,-0.0670691,-0.00089741,0.02516998,0.00788564,0.01603421,-0.04265596,-0.00036305,-0.02412288,-0.0120361,0.02799417,0.01585456,0.02229435,0.05096709,0.01336152,-0.00175995,0.0058042,-0.02740983,0.06636266,-0.01818513,0.07438398,0.043895,-0.00210593,-0.03034768,-0.03286048,0.05073026,-0.04804707,0.048776,0.07628459,0.01293042,0.04765588,0.06108644,0.03930942,0.03075115,-0.04064314,-0.02541683,-0.01952133,-0.04315117,0.01343903,-0.02044302,-0.00821819,-0.26280835,0.01934557,-0.04714062,0.04427683,-0.00712141,-0.00307519,-0.00543624,-0.0168054,0.02224242,0.0183503,-0.00343802,0.03531991,0.00823861,-0.08219042,-0.04074062,-0.03984319,0.02312241,-0.01199558,0.08231115,-0.02530845,0.04157143,-0.00057758,0.23251684,0.01091751,-0.00405184,-0.00390499,0.02220713,-0.0094354,0.0107321,0.04816798,0.00095201,-0.00319858,0.07953677,-0.02704833,-0.02905498,0.06170371,-0.01754302,0.07306191,0.0140322,0.02634123,-0.05693238,0.01669012,-0.01731816,-0.04910758,0.08235143,0.02268471,0.00901717,-0.04123084,0.01643987,0.0712503,-0.06597687,-0.02202256,-0.09712783,-0.03579349,0.04382554,0.01416593,-0.01510009,-0.01909523,0.00387427,0.03526376,0.07013714,-0.05255214,0.07466041,0.05425439,0.00600169],"last_embed":{"hash":"1x7ssl0","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"1x7ssl0","at":1753423450005},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>","lines":[130,148],"size":2110,"outlinks":[{"title":"_**Notepad++ text and programmer's editor**_","target":"https://notepad-plus-plus.org/","line":8},{"title":"Select columns in lottery strategy report files with Notepad++ editor to sort them easily.","target":"https://saliu.com/ScreenImgs/column-select.gif","line":16}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14701806,-0.0419702,0.01730338,-0.03576179,-0.02411422,0.06720328,-0.02503357,-0.00719253,-0.00522661,-0.00101098,0.01173292,0.02209472,0.05176264,-0.0289785,-0.00768586,-0.00877135,-0.03501446,-0.01673213,-0.05074387,0.02193533,0.01490282,-0.06511822,0.01082463,-0.10108037,0.02664123,-0.00908982,-0.00929102,-0.07776251,-0.09058084,-0.25182971,0.0287302,0.02841145,-0.02127765,-0.04610496,-0.05376652,-0.04328127,-0.02314445,0.06341156,-0.05299627,0.02012539,0.04042147,0.01436282,0.00833743,0.03697332,-0.00758839,-0.10487197,-0.04064333,-0.02746779,0.07542577,0.01528031,-0.02652211,0.03007868,0.02858431,0.00463639,0.01221093,0.0257936,0.03891077,0.08361562,0.00559404,0.0372068,0.04300756,0.05331475,-0.15992431,0.10355077,-0.05894065,0.02700522,-0.00503666,-0.00248967,-0.00073453,0.02892012,-0.02252124,0.01140687,-0.02322932,0.10306577,0.06624874,-0.06683108,-0.05997369,-0.04892587,-0.05275725,0.02635263,-0.04636573,0.01471945,-0.0134654,0.00552455,-0.04508637,0.07889058,0.06111839,0.02113646,0.05530466,-0.03932199,0.05458251,-0.0156541,0.01863794,0.02844166,0.02636048,-0.03909231,0.08098769,-0.0033818,-0.05877702,0.1129789,0.02184813,-0.03560326,-0.0192993,0.03303451,0.02603155,-0.0271866,-0.02390081,-0.03476645,-0.04398879,0.04458796,0.01441172,0.00960627,0.03484577,-0.04366678,-0.00791474,0.01694968,-0.03746206,-0.04542276,0.02232175,-0.00486369,-0.06296955,0.01328426,-0.00870495,0.00438723,-0.02945119,0.0176563,0.03224883,0.04224025,0.00880077,0.00598412,0.02914494,0.01054149,-0.12221544,-0.04922082,-0.00099535,0.00589579,0.00856452,-0.04074363,0.02707299,-0.04999977,-0.00316781,-0.02731468,0.09091801,-0.11013795,-0.00353052,0.11348974,-0.02275123,0.00162325,0.01857916,-0.02079468,0.04339455,0.00938255,-0.05287988,-0.04448662,-0.01197875,0.01217742,0.11581685,0.08133586,-0.04283718,-0.02609964,-0.03300932,0.02216443,-0.02309088,0.13305883,-0.01030986,-0.09021179,-0.02414254,0.02847402,-0.01388446,-0.05814117,-0.03317562,-0.03815475,-0.04153946,0.02223671,0.10428476,-0.02508369,-0.10767972,-0.04923825,0.01280516,0.00261898,0.0157953,-0.01499721,-0.05651207,0.03554863,-0.00849281,-0.06960854,0.04871666,-0.05937511,0.00573439,0.05341213,-0.00629998,0.0481094,-0.02211111,0.00891826,-0.03771557,0.01521084,-0.03814066,-0.03147559,0.05044894,-0.02126356,0.04048077,0.01208771,-0.02166151,0.01975479,-0.07075143,0.0185,0.01134679,-0.04502237,0.11738728,0.02724404,-0.02738709,0.0294009,0.05856941,0.03214969,-0.0773554,0.02276442,0.01900825,0.02975925,0.04840523,0.04909621,0.00785483,0.05171976,-0.04035339,-0.21347961,-0.02371961,-0.0438627,0.00753112,-0.02811629,-0.02501949,0.03422849,-0.01150877,0.01047284,0.02803646,0.13767311,-0.03435951,-0.01600295,0.05529662,-0.0139907,0.01071265,-0.04353781,-0.0027257,0.00368908,0.02051218,0.00578963,0.03374295,-0.05757856,-0.02227996,0.05695763,-0.02029059,0.11013038,-0.01299609,0.06467394,0.02180942,0.10718755,0.04299419,0.01787301,-0.05778776,0.00404995,0.08476972,0.01975741,-0.00413644,-0.02218356,-0.00398064,-0.07882802,0.0235686,-0.00212886,-0.11463049,0.00281448,-0.001764,-0.00438411,-0.02564521,0.00673836,0.05327897,0.03173623,0.01547922,0.00318353,0.0549954,0.05096945,-0.02342476,-0.06488182,0.00063513,0.02643724,0.00539982,0.01354592,-0.03408539,0.00600118,-0.02800448,-0.00406852,0.02521723,0.02137206,0.02374173,0.0533647,0.01339373,-0.01093649,0.00909739,-0.03382668,0.06368835,-0.0259157,0.07730208,0.03948084,-0.00050656,-0.03556489,-0.02720147,0.05563327,-0.04020206,0.04854374,0.08176022,0.01618233,0.04292848,0.05473267,0.04299943,0.03763892,-0.03766531,-0.03267784,-0.02245529,-0.04789238,0.01528829,-0.01916169,-0.01350821,-0.25735757,0.02337359,-0.05109341,0.04313761,-0.01031289,0.00252798,-0.0102768,-0.02029155,0.01996144,0.00872745,-0.00259977,0.0335208,0.01219595,-0.07675143,-0.03457471,-0.03787491,0.0239517,-0.02305164,0.07867233,-0.02530628,0.04345031,0.00378142,0.23566441,0.00987484,0.00026905,-0.009053,0.01689131,-0.00373674,-0.00286999,0.04569162,0.00227717,-0.00168803,0.07568233,-0.01823433,-0.02545761,0.05428759,-0.00922592,0.07675284,0.01023175,0.02889877,-0.06104355,0.01035874,-0.01461086,-0.05130408,0.08391163,0.01876339,0.01359293,-0.04075684,0.00358944,0.06203463,-0.0662518,-0.01880354,-0.09622651,-0.04409762,0.03583741,0.01508043,-0.01249995,-0.02390947,-0.00084877,0.03401476,0.06700288,-0.04741932,0.07071315,0.04687232,-0.00183554],"last_embed":{"hash":"1w1mfjo","tokens":358}}},"text":null,"length":0,"last_read":{"hash":"1w1mfjo","at":1753423450161},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{1}","lines":[132,138],"size":1123,"outlinks":[{"title":"_**Notepad++ text and programmer's editor**_","target":"https://notepad-plus-plus.org/","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11810041,-0.02414794,-0.01026322,-0.02415518,-0.03431125,0.06666309,-0.00204152,0.00183171,0.0167655,0.00371422,0.00312256,0.0125734,0.07574811,0.0067679,-0.01522502,-0.00884794,-0.04650031,0.01790332,-0.06764948,0.01516188,-0.00196586,-0.0463734,-0.02211147,-0.09440227,0.04727798,0.00850676,-0.05944953,-0.07841957,-0.06086265,-0.25390187,0.02762182,0.00783292,-0.01427523,-0.04975077,-0.0544941,-0.05234635,-0.03695447,0.08790711,-0.03104483,0.01970983,0.02924593,-0.00995173,0.01751177,0.03925691,-0.00110495,-0.07056101,-0.02708846,0.01211547,0.08399814,-0.01275842,-0.03878993,0.0332208,0.02574828,-0.00449774,0.02481396,0.03989631,0.04789075,0.07595286,-0.00291263,0.04115025,0.02863033,0.03787702,-0.1592097,0.09835695,-0.03621369,0.01531791,-0.01685733,0.01762804,0.01768067,0.08313533,-0.03072287,0.04565586,-0.00260728,0.08931319,0.07855108,-0.05353696,-0.06399279,-0.0708482,-0.07138722,0.04107067,-0.08709753,0.04002966,-0.03108571,0.02178334,0.01860214,0.08733454,0.04080094,0.00797958,0.05368524,-0.02945749,0.06952632,-0.01268912,-0.00280577,0.02562148,0.00491872,-0.06476261,0.03941986,-0.0155134,-0.00289507,0.1321937,-0.01480681,-0.02686689,0.01396865,0.02144442,-0.00286302,-0.03494895,-0.03147425,-0.02464143,-0.0276693,0.04280058,0.02358748,-0.01768416,0.04045821,-0.06152037,-0.00667552,0.02596072,-0.03057384,-0.03774076,-0.01847553,0.01959641,-0.04919688,0.00311446,-0.0304209,-0.0007521,-0.01672018,0.00013953,0.01444741,0.0372921,0.03966545,0.00499012,0.00614215,0.03645953,-0.13875093,-0.05156138,-0.02666807,-0.01575617,-0.0177493,-0.03453605,0.00209164,-0.06601136,0.01947358,-0.04144106,0.09997341,-0.08040404,-0.0058131,0.11788266,-0.02731118,-0.01865126,0.01190932,-0.01989255,0.038463,0.00505629,-0.00775028,-0.03463302,-0.02289279,0.00655865,0.08300929,0.08633788,-0.0280702,-0.01235625,-0.05701423,0.02182095,0.00266952,0.1541988,-0.02422989,-0.0868528,-0.03649049,0.03379778,-0.01977851,-0.074152,-0.00672103,-0.02585529,-0.00960741,0.01396629,0.08584093,-0.02789638,-0.0814849,-0.09224988,0.04919916,0.01094123,0.03643628,-0.03884641,-0.05379588,0.04864162,-0.01210069,-0.06255644,-0.00420567,-0.07712742,-0.00671833,0.0468259,-0.04901655,0.05575917,-0.01232057,0.02657926,-0.06466072,0.00039954,-0.03270163,-0.06493409,0.02984204,0.0221589,0.04003167,0.01287641,-0.03579338,0.01697991,-0.02662361,0.01645572,0.03722602,-0.0722708,0.10465975,0.02471336,-0.01575025,0.01397378,0.07678048,0.04669364,-0.07549166,0.01483375,0.04211444,0.02257689,0.02287257,0.04250843,0.02440333,0.04135696,-0.03929501,-0.18684019,-0.0481579,-0.04940133,0.00163423,-0.047858,-0.00011531,0.02772856,-0.04927806,-0.01460187,0.02576517,0.12441966,-0.0334521,-0.05109679,0.04036694,-0.01670793,-0.02844238,-0.03788364,-0.00176918,0.0078932,0.0245636,0.00891247,0.00285945,-0.05659321,-0.01519421,0.07212942,-0.04470498,0.12222785,0.01131616,0.04545965,0.04720691,0.09652038,0.03099228,0.01814893,-0.04035962,0.01694963,0.04945862,0.00704912,0.00316078,-0.00917946,-0.01965598,-0.04837016,0.02796877,0.00201781,-0.10230811,0.00008745,-0.00660731,-0.00136504,-0.01933197,0.03812135,0.02438738,0.06173244,0.00883445,0.00518627,0.04081061,0.03790968,-0.00591339,-0.05478404,-0.00359425,0.02508884,-0.03988353,0.00975049,-0.01043145,0.01928764,-0.02337786,0.00459817,0.03781558,0.01185718,-0.00128514,0.02800805,0.03981688,0.00953987,0.03490875,-0.00814855,0.05390396,0.00443061,0.05633364,0.02324178,0.01043406,-0.0145424,-0.03074005,0.05602081,-0.02133627,0.04802124,0.0583469,0.00708039,0.02866505,0.06498981,-0.01866962,0.04092221,-0.02279876,-0.00737688,0.00122254,-0.02891549,0.07098705,-0.0068657,-0.02062386,-0.27001727,0.04392304,-0.03196107,0.02745054,-0.00027706,-0.02480077,0.03053043,-0.00965949,0.00806652,0.01285293,-0.01764197,0.02212228,0.02254446,-0.08731603,-0.03514109,-0.02736652,0.04069848,-0.03276637,0.09145455,-0.01607218,0.07696748,-0.02952543,0.25443926,0.00747784,-0.00488561,-0.00160068,-0.00233873,-0.01520932,-0.01433788,0.05137463,0.00339071,0.00676701,0.03460223,-0.03022127,-0.02801071,0.05941863,0.02029222,0.07840027,0.00434161,-0.0039727,-0.05639667,0.00789037,-0.02923631,-0.03002584,0.06892289,0.01116706,-0.02449796,-0.01349578,-0.00016798,0.03621685,-0.10157579,-0.01206921,-0.09519652,-0.05838219,0.0303453,0.0610457,-0.02904867,0.00046247,0.0370522,0.00799921,0.06704115,-0.05014151,0.0808524,0.02713568,0.00237474],"last_embed":{"hash":"u88bk1","tokens":224}}},"text":null,"length":0,"last_read":{"hash":"u88bk1","at":1753423450275},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>#{5}","lines":[143,148],"size":658,"outlinks":[{"title":"Select columns in lottery strategy report files with Notepad++ editor to sort them easily.","target":"https://saliu.com/ScreenImgs/column-select.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09264645,-0.0595862,-0.01403839,-0.05646876,-0.0380004,0.06001256,-0.00377732,-0.00191216,0.02955702,0.01877169,-0.00694855,0.01297509,0.07534276,0.02385606,-0.03038767,-0.04071657,0.01261725,0.041643,-0.07482579,-0.02574034,0.03757282,-0.02992522,-0.04397333,-0.05173047,0.05997717,-0.00194225,-0.01984741,-0.06145462,-0.09233265,-0.24837793,0.01722082,0.00073137,0.02001081,-0.03266617,-0.07073228,-0.06061909,-0.02025302,0.09320713,-0.06623593,0.01644094,0.02526017,-0.02968003,0.02633118,0.02353889,-0.02299894,-0.0355292,-0.01642246,0.00320217,0.03606596,-0.0250614,-0.06934058,0.01845438,-0.00237297,0.02541433,0.00120397,0.02293933,0.06200759,0.0972449,0.02009657,0.05862693,0.04750921,0.03398541,-0.17233567,0.07908893,-0.06123564,0.02883657,-0.04251514,-0.03511362,-0.00758831,0.02868375,-0.00724387,0.00739283,-0.02257448,0.09775271,0.04683704,-0.07012808,-0.04865543,-0.08396494,-0.05870193,0.03490945,-0.08048537,0.02014565,-0.040051,0.02594724,0.0066493,0.06269891,0.04768414,0.03073492,0.05848879,-0.02885776,0.08707932,-0.03963443,0.01759357,0.05742485,0.03078557,-0.01129264,0.05014911,-0.00871914,-0.00784228,0.14363508,0.01886471,-0.01447061,-0.01974578,0.00906858,0.03307127,-0.0247691,-0.00964206,-0.04214429,-0.04050964,0.04482064,0.00020848,0.01581444,0.05025974,-0.06797271,-0.0024023,0.07371244,-0.0313079,-0.01284853,0.01960018,-0.00816963,-0.06299561,-0.02825089,-0.00586538,-0.03576791,0.01352216,0.02512772,0.02760094,0.05522988,0.03699309,0.03010699,0.04818727,0.01901328,-0.1387372,-0.05027746,-0.01094861,0.01088788,-0.00938181,-0.04407889,0.00256344,-0.03945946,-0.00149929,-0.0403831,0.03676099,-0.11336014,-0.03521938,0.13766713,-0.06366675,0.02109798,-0.01710052,-0.04261482,0.02791773,-0.00569464,-0.0593717,-0.03608564,0.00003653,0.00183611,0.07409405,0.06343129,-0.0383153,-0.02107018,-0.03139982,-0.0085187,-0.00172754,0.10183927,-0.00216225,-0.07027996,-0.03982796,0.04104769,-0.04132043,-0.05495091,-0.0166995,-0.01760643,-0.04878103,0.01971231,0.07279409,-0.00630708,-0.07530943,-0.07527614,0.0299389,-0.01616008,0.03839331,-0.03349689,-0.03951339,0.02430896,-0.04679869,-0.03670159,0.02772663,-0.05349791,0.0121347,0.05119962,-0.03825634,0.0103137,-0.01211409,0.03384745,-0.02472476,-0.00417671,-0.05965035,-0.02523573,0.03846058,-0.02606213,0.01514715,0.02641381,0.01144724,0.02048033,-0.08371901,-0.00200375,-0.00280517,-0.04507581,0.11178373,0.00909442,-0.07077549,0.02393601,0.04444458,0.00907932,-0.05003701,0.01207401,0.02751627,0.01593125,0.01679437,0.0182871,0.01281071,0.04698274,-0.04116528,-0.19532794,-0.04288156,-0.04096843,0.0237245,0.00904555,0.00275261,0.01633377,-0.04316361,-0.01610818,0.11112689,0.13663389,-0.03954336,-0.05024781,0.06994869,-0.01446927,0.02166044,-0.05732146,-0.02257265,-0.0371846,-0.00435859,0.03510625,0.02475427,0.00217712,-0.04498636,0.04278193,-0.02498871,0.11110483,0.02037557,0.03166559,0.0281808,0.08839217,0.01898758,0.00008146,-0.01826904,0.00341594,0.08002695,-0.00453874,-0.01955604,-0.0324492,0.01401453,-0.03577007,0.01681289,0.01791823,-0.09903128,0.00127976,-0.00586632,-0.02392936,0.00420311,0.03204334,0.03318394,0.04039265,-0.04619022,0.03632462,0.00510431,0.04924764,-0.04634966,-0.05416216,-0.02824361,0.01933747,-0.01978397,-0.00794241,-0.0591082,0.05247467,-0.02147793,0.00488057,0.0183559,0.01770156,0.01367906,0.01923118,0.03877094,-0.01281609,0.05548468,-0.03129249,0.0880715,-0.01001543,0.08630759,0.03126617,0.01523709,-0.00168647,-0.03694911,0.05827996,-0.01230503,0.06032474,0.07436644,0.05748482,0.00919581,0.07155784,0.01036073,0.04348543,0.01350704,-0.00921256,0.01439865,-0.05211469,0.05816071,0.01562729,-0.02044432,-0.24714933,0.02878805,-0.046595,0.01689789,0.02862894,-0.02465353,0.03195834,0.00611989,0.06431342,0.00158297,0.01811454,0.07007206,0.04066586,-0.08381472,-0.04157858,-0.01752243,0.00427968,-0.04018518,0.1173551,0.01164218,0.05729275,-0.00522246,0.23538408,-0.00982368,-0.01038598,-0.00772068,0.00758779,-0.01900769,-0.0374598,0.04523702,-0.00806154,-0.00575479,0.07188829,-0.00842773,-0.03001886,0.03636507,0.02270351,0.08349729,-0.01176636,0.03811192,-0.05564513,0.01783442,-0.04477495,-0.06439304,0.08226457,-0.02475009,-0.0449744,-0.03807743,-0.01424375,0.06500392,-0.06209272,-0.03792131,-0.07711211,-0.03557304,0.00415736,0.01728182,0.02961436,0.01854062,0.02803049,0.02246434,0.0674175,-0.03114805,0.04534401,0.05576748,0.01584382],"last_embed":{"hash":"1incmf7","tokens":493}}},"text":null,"length":0,"last_read":{"hash":"1incmf7","at":1753423450334},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>","lines":[149,176],"size":3590,"outlinks":[{"title":"Lottery software sorts numeric columns in ascending or descending order to discover strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":9},{"title":"The best sorting software for lottery data, results files is named Sorting.","target":"https://saliu.com/ScreenImgs/sort-column.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07649187,-0.05625654,0.00052866,-0.04028604,-0.07052635,0.07011136,-0.02193247,0.00479967,0.02517612,0.02177509,-0.00802407,0.00286053,0.08411279,0.00568931,-0.00811662,-0.03341841,-0.01522319,0.04429203,-0.06634153,-0.01541553,0.03431127,-0.02652312,-0.0327827,-0.01589605,0.06219323,-0.0030348,0.00013467,-0.05159412,-0.07465042,-0.24823742,0.0386375,0.01284848,-0.00088784,-0.02129067,-0.06110847,-0.06449337,-0.02429359,0.10843388,-0.03048813,0.03354082,0.0432123,-0.04052943,0.02268624,0.04604275,-0.0269144,-0.03180577,-0.04593934,0.00600373,0.02584524,-0.0295992,-0.04658199,0.02698944,-0.00200645,-0.00155775,0.001887,0.01842334,0.05762562,0.10969054,0.01809206,0.04956809,0.03789937,0.04300333,-0.17202412,0.08589289,-0.08716206,0.05271152,-0.03385316,-0.00254263,-0.00548075,0.06291139,0.00916683,-0.01371039,-0.02942022,0.09821504,0.04991627,-0.05248124,-0.07588647,-0.06840583,-0.0630864,0.03691181,-0.07860608,0.00271742,-0.03557297,0.0296657,-0.02089695,0.05540515,0.05076666,0.00982421,0.04757145,-0.0104958,0.08223549,-0.04627011,0.01887476,0.0394089,0.00115042,-0.01522109,0.05275818,-0.02266304,-0.0212876,0.15895081,0.01171077,-0.00789178,-0.00139084,0.01346188,0.01073848,-0.01164738,-0.00522298,-0.0499614,-0.03614523,0.05344091,-0.02197078,0.01652775,0.02198151,-0.07004774,0.01706561,0.06468107,-0.02664503,-0.0158227,0.01622092,-0.01312412,-0.04346685,-0.0291099,0.00924149,-0.01153881,0.01228926,0.03472422,0.01761576,0.06320608,0.03834057,0.01445611,0.03260222,0.02351964,-0.11531307,-0.03634064,-0.02048896,0.00660991,-0.0108616,-0.04663641,0.01928395,-0.02852675,-0.00084852,-0.01784097,0.03866213,-0.11528485,-0.01715751,0.13920836,-0.02140997,0.03043834,-0.01688683,-0.02991256,0.02923189,0.0034405,-0.06682888,-0.02489521,-0.00948901,0.01617182,0.05781585,0.06245209,-0.02193589,-0.02154892,-0.05113484,-0.0085173,-0.02972951,0.12726845,-0.00352996,-0.07459141,-0.03007817,0.00336811,-0.03887562,-0.06862984,-0.02191829,-0.03620227,-0.05542115,0.01040174,0.09230547,0.01823779,-0.09543588,-0.06336953,0.00766703,0.00598873,0.03881703,-0.03262646,-0.05278692,0.03269592,-0.05779738,-0.02875139,0.0221239,-0.04999467,0.00019286,0.06968987,-0.05258577,0.04261156,-0.03375858,0.06077916,-0.04922276,-0.00109619,-0.04108344,-0.03383154,0.02611604,-0.04973305,0.01761562,0.03095487,-0.03429145,0.01576775,-0.10712701,-0.0194752,0.03218018,-0.04986047,0.08663264,0.0254429,-0.07000833,0.03906399,0.04849823,-0.01227581,-0.04597993,0.00524202,0.04008897,0.01636013,0.02097171,-0.00260398,0.00273045,0.07203592,-0.03409271,-0.19846153,-0.0573514,-0.03785236,-0.00075115,-0.0019038,0.01051919,0.01081207,-0.03490573,-0.04280157,0.09695964,0.11716171,-0.03053915,-0.04472496,0.08483232,-0.0348711,0.03344696,-0.05355904,-0.03074288,-0.00621608,-0.00256284,0.04793539,0.04327803,-0.0191528,-0.03929179,0.05700237,-0.00927118,0.10251994,0.01498231,0.04002453,0.02493635,0.08854362,0.02392277,-0.01467908,-0.02308003,0.0109756,0.06925716,0.0043365,-0.02745917,-0.03416873,-0.01741755,-0.01798917,0.01869042,-0.0139309,-0.08830384,-0.00086406,-0.02267414,-0.02653634,0.0088285,0.02800526,0.03309113,0.05028858,-0.05095832,0.03455605,0.01842434,0.03928377,-0.03924222,-0.07708714,-0.01056434,0.01135821,0.0015945,-0.01526913,-0.03296854,0.04301356,-0.01660614,0.02220372,0.05132132,0.01607359,0.01886309,-0.00565679,0.04311673,-0.01098322,0.05267595,-0.02185776,0.09095538,-0.02356148,0.07400353,0.01328866,-0.00216333,0.00428981,-0.04899504,0.07013462,-0.01596448,0.047181,0.0691545,0.04015347,0.0069292,0.05590779,-0.02407805,0.04342905,-0.01440384,-0.0472061,0.00987787,-0.04961899,0.07496576,0.01478316,-0.0566611,-0.24272749,0.02133711,-0.01133906,0.0152213,0.03358157,-0.00061593,0.00831826,-0.01132855,0.03523903,-0.00926883,0.01181889,0.07912093,0.03104439,-0.05052891,-0.04219253,-0.03308885,0.03239808,-0.03311083,0.11134602,-0.01282959,0.07508425,-0.00930903,0.24431072,-0.00447519,-0.01482689,-0.0070609,0.00592823,-0.00950814,-0.04510016,0.04024633,-0.00635895,0.00111537,0.05019193,-0.00060526,-0.04116698,0.04639344,0.01744647,0.11133514,-0.00442816,0.03200215,-0.05376868,0.01048388,-0.04181127,-0.05608523,0.07564462,-0.03066645,-0.02927965,-0.02840376,-0.02990241,0.05599076,-0.05902421,-0.01019932,-0.08504159,-0.01803663,0.00836695,0.02761937,0.02273904,0.00542888,0.0376532,0.04150093,0.06362627,-0.02551604,0.02976054,0.05707274,0.00199992],"last_embed":{"hash":"1xh32dh","tokens":151}}},"text":null,"length":0,"last_read":{"hash":"1xh32dh","at":1753423450499},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#{1}","lines":[151,152],"size":415,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#<u>Important Update, February 2011</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09836654,-0.06293149,-0.01843424,-0.06782309,-0.02740962,0.07468866,0.01490849,-0.00150492,0.03723358,0.01357473,0.01008836,0.01647738,0.0587009,0.0257523,-0.04592881,-0.02719443,0.02582162,0.03538831,-0.07444461,-0.03032251,0.05684005,-0.03843482,-0.05147918,-0.08818289,0.06109708,-0.00812017,-0.02270069,-0.05981847,-0.08963951,-0.24767148,0.01553899,-0.00913384,0.02700431,-0.0376421,-0.07393038,-0.06552118,-0.01726295,0.08569781,-0.076998,0.03446879,0.0193745,-0.02430131,0.02227027,0.01162227,-0.03242764,-0.03574256,-0.00977753,0.00790461,0.03933002,-0.00800465,-0.06744549,0.0207634,0.0007691,0.02847956,-0.00642828,0.02935268,0.07166115,0.08194771,0.02450767,0.04866229,0.04205511,0.02917229,-0.17065969,0.07462665,-0.05074487,0.02056874,-0.02743637,-0.0392032,0.01081831,0.04150136,-0.0069005,0.0295069,-0.0080134,0.08632477,0.04349451,-0.07018463,-0.04492756,-0.09476548,-0.05053917,0.02600612,-0.0790179,0.02032409,-0.01797966,0.00232165,0.03214383,0.06429639,0.03808431,0.03000837,0.06527695,-0.03791267,0.07897279,-0.03218529,0.0065536,0.05525431,0.02301856,-0.01574437,0.05070669,0.00039183,-0.00305444,0.13614519,0.02833583,-0.0146944,-0.02041742,0.01919702,0.04464843,-0.02877325,-0.02443602,-0.02411818,-0.02426923,0.038419,-0.00055734,0.00691428,0.06186803,-0.06510775,-0.00404088,0.07511267,-0.01762504,-0.02428497,0.02043693,0.00250284,-0.06885544,-0.02189717,-0.01162837,-0.04575067,0.00324589,0.0282959,0.02709345,0.05796646,0.02724176,0.04200847,0.04122726,-0.00187503,-0.14378998,-0.05500506,0.01114053,0.01333093,0.00292216,-0.03570478,-0.00814131,-0.02605297,-0.00254711,-0.05218832,0.04044169,-0.11489354,-0.03297637,0.1311008,-0.06724658,0.00242247,-0.01710038,-0.05580884,0.02711068,-0.01834906,-0.04605491,-0.04767134,-0.01242617,-0.00068625,0.07919309,0.06734759,-0.0507271,-0.02052944,-0.0273746,-0.00994214,0.00945616,0.09353787,-0.00757065,-0.06250033,-0.03776205,0.03903493,-0.06094178,-0.03892945,-0.00999163,-0.00553355,-0.04695894,0.0108805,0.08595214,-0.01639161,-0.08102576,-0.07498381,0.0163161,-0.00875404,0.0302589,-0.03487644,-0.04842718,0.01826279,-0.03388825,-0.03984927,0.03625932,-0.04957214,0.02516273,0.0525073,-0.04811842,-0.00839894,0.00039614,0.01638063,-0.00708784,0.00157913,-0.05562251,-0.01126011,0.04349403,-0.01920381,0.0249113,0.0115759,0.03021784,0.03392999,-0.05444838,0.02155249,-0.01341911,-0.04886844,0.12650828,0.00638309,-0.0569146,0.01428519,0.04633111,0.03145636,-0.03488735,0.00907408,0.01938223,0.01079622,0.01978271,0.02850763,0.03035039,0.02459926,-0.057579,-0.1983681,-0.01756354,-0.03524877,0.03601819,0.02280078,0.00721466,0.01365213,-0.03175903,-0.00425933,0.11119143,0.14429852,-0.04542232,-0.04183353,0.06737578,-0.00956331,0.01289918,-0.07427704,-0.01606815,-0.04302514,0.01339355,0.02877568,0.00209883,-0.00315124,-0.04811057,0.03892122,-0.04027344,0.11703301,0.00745482,0.01193415,0.02350421,0.07451099,0.00372712,0.00784312,-0.00663123,-0.01206757,0.06207465,-0.02372761,-0.01447988,-0.01990084,0.02179617,-0.04525346,0.014502,0.01585071,-0.08702026,-0.00618151,0.00735253,-0.02625349,0.02612858,0.02955093,0.03006507,0.04119411,-0.03908195,0.02467473,0.0129071,0.06780457,-0.06093559,-0.05429378,-0.03778566,0.0156753,-0.0218717,-0.00203052,-0.05463093,0.03958623,-0.03243106,-0.00329208,-0.00005526,0.02463707,-0.00002187,0.03828483,0.0396312,-0.01664226,0.04400508,-0.02518886,0.06865399,-0.00911254,0.07839305,0.0296125,0.02242273,-0.01700013,-0.03053388,0.07004543,-0.0222617,0.06723177,0.0615197,0.06627177,0.02816768,0.06682999,0.02078109,0.0257865,0.02111602,0.01282737,0.01003228,-0.04809527,0.0437354,0.00230998,-0.0217068,-0.25107232,0.03857029,-0.05470299,0.03967318,0.01501528,-0.03717863,0.04345005,0.0131657,0.05571102,-0.01906602,0.02645857,0.05663481,0.04525961,-0.0889096,-0.02851746,-0.0161282,-0.01129908,-0.04098099,0.11330226,0.03118533,0.05709408,0.00067619,0.23957703,-0.01852927,0.0073912,-0.00990651,-0.001564,-0.01660543,-0.04220315,0.04181611,-0.00429273,-0.00072754,0.08540726,-0.01456283,-0.02203299,0.03532259,0.01418532,0.05677056,-0.00242708,0.02240035,-0.05279983,0.01082904,-0.03785698,-0.05515827,0.09304892,-0.01567796,-0.05179601,-0.05686805,-0.01333075,0.06354833,-0.07481418,-0.04472352,-0.0631061,-0.05046003,0.01555182,0.0249624,0.02512479,0.01605279,0.02781316,-0.00440484,0.06376497,-0.02824983,0.05169067,0.03191131,0.02687846],"last_embed":{"hash":"vihgjz","tokens":500}}},"text":null,"length":0,"last_read":{"hash":"vihgjz","at":1753423450544},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#<u>Important Update, February 2011</u>","lines":[153,176],"size":3092,"outlinks":[{"title":"Lottery software sorts numeric columns in ascending or descending order to discover strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":5},{"title":"The best sorting software for lottery data, results files is named Sorting.","target":"https://saliu.com/ScreenImgs/sort-column.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#<u>Important Update, February 2011</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09700733,-0.06265388,-0.0191849,-0.06811815,-0.02692435,0.07446193,0.01482642,-0.00164417,0.03671996,0.01258617,0.00983325,0.01617366,0.06011495,0.02442111,-0.04514353,-0.0252094,0.02500806,0.03575992,-0.07412219,-0.02918492,0.05797434,-0.03815875,-0.05010609,-0.08779906,0.06107976,-0.00713715,-0.0230289,-0.06022483,-0.08824462,-0.24926612,0.01672671,-0.00781839,0.02864817,-0.03903482,-0.07654142,-0.06532756,-0.01761815,0.08447123,-0.07611018,0.03593324,0.02024148,-0.02457208,0.02191924,0.01077625,-0.0335461,-0.03425276,-0.00949832,0.0075929,0.04005877,-0.00699636,-0.06681661,0.01806318,0.00054979,0.02718711,-0.00725064,0.02889295,0.07078837,0.08365396,0.02261275,0.04891879,0.03977484,0.03126968,-0.17137496,0.07408705,-0.05299793,0.02093591,-0.02986797,-0.04117577,0.01171251,0.04224624,-0.00814598,0.02806567,-0.0083069,0.08817717,0.04462374,-0.07036848,-0.04469271,-0.09342256,-0.04841395,0.02682349,-0.08064351,0.01871954,-0.0170994,0.00114505,0.03346806,0.06445086,0.03787108,0.02917782,0.06545065,-0.03749492,0.07829814,-0.0339216,0.00762624,0.05566807,0.02479416,-0.0152386,0.05180464,0.00096078,-0.00418348,0.13557801,0.02880961,-0.01422367,-0.02049389,0.01721698,0.04505111,-0.02846008,-0.02188192,-0.02519926,-0.02466969,0.03794875,-0.00141203,0.00518737,0.059753,-0.06599674,-0.00451057,0.07536088,-0.01684717,-0.02470734,0.02051904,0.00186583,-0.070738,-0.02139635,-0.01167151,-0.04406375,0.00253132,0.02913842,0.02839979,0.0567433,0.02571526,0.04262802,0.03969348,-0.00311314,-0.1431395,-0.05551243,0.01075881,0.01324179,0.00378122,-0.03600482,-0.00826875,-0.0244519,-0.00277488,-0.04824488,0.04289988,-0.11496744,-0.03227325,0.13268586,-0.06882692,0.00130778,-0.01784704,-0.05763226,0.02688157,-0.01875005,-0.04518554,-0.04865534,-0.01218451,-0.00211447,0.07983244,0.06873801,-0.05058896,-0.0200959,-0.0266156,-0.01006826,0.01087969,0.09244905,-0.00977889,-0.06009969,-0.03873416,0.03943663,-0.06089235,-0.03813393,-0.01134464,-0.00753738,-0.04571835,0.011929,0.08644099,-0.01705403,-0.08189874,-0.07289815,0.01486926,-0.00983926,0.02976313,-0.03473739,-0.04757306,0.01831759,-0.03488485,-0.04137724,0.03798238,-0.04857368,0.02485281,0.05144912,-0.04969269,-0.00466752,-0.00002065,0.01660548,-0.00873666,0.00218608,-0.05388046,-0.01069661,0.04280228,-0.01949925,0.0261211,0.01159709,0.02947477,0.03240424,-0.05408679,0.02117381,-0.01353823,-0.04781391,0.12541761,0.0063834,-0.05707082,0.01448911,0.04687057,0.03407977,-0.03587982,0.00839743,0.01890213,0.01013897,0.02027135,0.02895831,0.03170069,0.02685864,-0.05928111,-0.19774354,-0.01698831,-0.03511245,0.03567349,0.02299437,0.00720515,0.01254233,-0.032483,-0.0049594,0.11050148,0.14230476,-0.04444678,-0.04119559,0.06817982,-0.00996743,0.01332513,-0.07525503,-0.01605644,-0.04442449,0.01429336,0.02770265,0.00029971,-0.00045666,-0.04844953,0.03806357,-0.04008874,0.11780791,0.00646651,0.01126974,0.02421325,0.07374955,0.00065829,0.00687253,-0.00733373,-0.00974821,0.0622177,-0.02431833,-0.01365593,-0.0182342,0.01983726,-0.04470493,0.01433772,0.01561748,-0.08783431,-0.00663923,0.00807858,-0.02618754,0.02696871,0.02849494,0.03096303,0.0425086,-0.03711228,0.02413131,0.01268683,0.06877185,-0.06276272,-0.05563469,-0.03540459,0.01473776,-0.02206202,-0.00183887,-0.05426126,0.03925866,-0.03217383,-0.00404079,-0.00150551,0.02474493,0.0015381,0.03699359,0.03856412,-0.01608041,0.04442065,-0.02733313,0.07084884,-0.01019193,0.07830452,0.03112424,0.02413778,-0.01745403,-0.03256634,0.07075675,-0.02319685,0.06856847,0.0614137,0.06634747,0.02846158,0.0686558,0.02004975,0.02595338,0.01922254,0.01198696,0.00939166,-0.04725987,0.04383129,0.00321059,-0.02082715,-0.25125989,0.03969174,-0.05676071,0.0394467,0.01580121,-0.03555672,0.04176146,0.00998606,0.05397378,-0.01865723,0.02720821,0.05654902,0.04489471,-0.08743184,-0.02806563,-0.0169513,-0.01025813,-0.04026514,0.11095969,0.03064081,0.05718089,0.0011664,0.23893775,-0.01883505,0.00678908,-0.00881076,-0.00123198,-0.01651455,-0.04222645,0.04040503,-0.00323338,-0.00108923,0.08658011,-0.01449419,-0.02091456,0.03717113,0.01271435,0.05491291,-0.00215892,0.02208737,-0.05159615,0.01151892,-0.03909592,-0.05412063,0.0938241,-0.01661013,-0.05240789,-0.05585118,-0.01274553,0.06270375,-0.07482783,-0.04312551,-0.06376152,-0.04985823,0.01675797,0.02582343,0.02724046,0.01827386,0.0263721,-0.00474241,0.06431716,-0.02769266,0.05304504,0.03199358,0.02708289],"last_embed":{"hash":"15k9ogt","tokens":501}}},"text":null,"length":0,"last_read":{"hash":"15k9ogt","at":1753423450694},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#<u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>#<u>Important Update, February 2011</u>#{1}","lines":[155,176],"size":3048,"outlinks":[{"title":"Lottery software sorts numeric columns in ascending or descending order to discover strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":3},{"title":"The best sorting software for lottery data, results files is named Sorting.","target":"https://saliu.com/ScreenImgs/sort-column.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10719118,-0.0276919,-0.01512334,-0.01469249,-0.03204976,0.06797757,-0.01035682,-0.01724541,0.01907992,-0.00867486,0.00998568,-0.00732981,0.05172003,-0.02890887,-0.0172585,-0.02649508,0.00058543,0.02507399,-0.02173094,-0.01140534,0.04816941,-0.01264983,-0.03273644,-0.10084366,0.04922565,0.00931668,-0.02941624,-0.05323339,-0.0646627,-0.20647973,0.01548781,-0.00278603,-0.01804125,-0.03114114,-0.07944792,-0.02821169,-0.01476179,0.07443155,-0.05202632,0.05922945,0.05070462,0.00783691,0.01974927,0.05056602,0.01281534,-0.05359903,-0.01300004,0.02082465,0.05768979,-0.03309757,-0.0791323,0.01966588,0.01076187,-0.01251569,0.03144661,0.05589335,0.07581063,0.09487989,0.02109763,0.01508263,0.01537112,0.08573948,-0.18716699,0.08710907,-0.02430584,0.04793563,-0.00261219,-0.00516979,0.03388439,0.02513396,-0.01715037,0.00155611,0.01376684,0.09933107,0.06938322,-0.03413071,-0.05030099,-0.08733933,-0.0784741,-0.00343041,-0.04374206,0.02711398,0.02225063,0.00043805,0.01543283,0.10481329,0.07680794,0.03398611,0.08836281,-0.07868117,0.03789188,0.00111688,0.0525643,0.04692237,-0.01898354,-0.0327742,0.1049229,-0.00549676,-0.00247571,0.13152611,0.01021198,-0.01700776,0.03848659,0.02395449,0.01512808,-0.04654865,-0.00196855,-0.02879601,-0.00985802,0.06226653,0.04250947,-0.01209902,0.02778311,-0.06447504,0.00033822,0.00137292,-0.03095093,-0.04380996,0.01384208,-0.00249468,-0.04902226,-0.0158904,0.01502931,0.01004366,-0.03435426,0.00274066,-0.00544147,0.04324768,0.01299528,0.0024166,0.02921327,0.05058879,-0.13843788,-0.06299355,0.0098147,-0.00449736,0.01445358,-0.0389349,0.01343833,0.02102391,0.00788954,-0.05776607,0.06466988,-0.12423596,-0.03834014,0.06678324,0.02282151,0.00506009,0.02182566,-0.00603308,0.0384788,0.00404302,-0.02658158,-0.04116411,-0.00755061,0.01551142,0.10610846,0.06813063,-0.03031291,0.00120585,-0.02030847,-0.01007708,-0.03986765,0.16694631,-0.02952457,-0.12685722,-0.01124394,0.044664,-0.01414223,-0.08096016,-0.02195978,-0.01935686,-0.0624427,-0.01052346,0.11779275,-0.005474,-0.07255679,-0.04745094,-0.01159174,0.00582916,-0.0030426,-0.05537988,-0.06532422,0.05732466,-0.03310797,-0.05722179,-0.01142405,-0.04972682,0.00855697,0.07288362,-0.03673354,0.00173463,-0.03139699,0.00728361,-0.05708096,0.01287946,-0.05209575,-0.04600061,0.0719825,-0.00151001,0.0187217,0.04359469,-0.0030883,0.0111529,-0.0637344,0.02518811,-0.00867255,-0.05638557,0.08312429,0.03975165,-0.05008445,0.03608232,0.05064572,0.02251473,-0.04561364,0.03797796,0.04661791,0.01827331,0.00355654,0.04998913,-0.0069103,0.0173785,-0.06354377,-0.19480096,-0.04905059,-0.07272762,-0.00207058,-0.01135076,0.0003319,0.04396446,-0.0472093,-0.02980949,0.06254561,0.13982973,-0.03273317,-0.0199255,0.04793597,-0.01256581,-0.01712549,-0.05586325,-0.03741272,-0.02583675,0.04944122,0.0154602,-0.00955117,-0.02559101,-0.05178559,0.01424578,-0.04631219,0.0996341,-0.01476133,0.00795656,0.03147997,0.09225374,0.02223514,-0.01069248,-0.06132746,-0.02357889,0.05552606,-0.01987805,-0.02697708,-0.01084982,-0.00413888,-0.06473312,0.02664862,0.01337593,-0.06352339,-0.01509322,0.00234515,-0.00801139,-0.03198598,0.03311285,0.03892848,0.00387162,-0.00060903,0.00808531,0.02449003,0.03805949,-0.02397451,-0.05807001,-0.02922863,-0.02074022,0.01107318,-0.02679308,-0.02987132,0.03458578,-0.03217933,0.02934778,0.00463323,0.01073369,-0.03859681,0.01361898,0.03876881,0.01560862,0.06039503,-0.00753078,0.05488488,-0.01719515,0.02864158,0.04958095,-0.02985965,-0.02955501,-0.00888602,-0.00727534,-0.03654569,0.05219838,0.08778661,0.06266078,0.05805562,0.04743218,-0.01627226,0.00830433,-0.02570441,0.00877516,-0.03031227,-0.03736705,0.01210831,0.0151817,0.03108067,-0.27514824,0.01808276,-0.02688528,0.0441447,-0.01398075,-0.01519106,0.03258217,-0.0440042,0.04171228,-0.02983773,0.07757149,0.04141895,0.02188559,-0.03962616,-0.00337059,-0.03244838,0.05063099,-0.02838007,0.06944212,0.03961116,0.02783883,0.01183663,0.24947265,0.00773716,-0.00552233,0.01370423,-0.01092391,0.00359897,0.01409625,0.04550222,-0.00489934,0.03779867,0.03499733,0.0072913,-0.0392947,0.06057423,-0.02996603,0.06344104,0.02639393,0.01443744,-0.07079585,-0.00770892,-0.02690378,-0.00091457,0.09307973,0.02420543,-0.00700809,-0.06751949,-0.00751384,0.04357483,-0.05665788,-0.05342299,-0.09250317,-0.03996692,0.00853915,0.03081496,-0.00341531,-0.04072627,0.02029325,-0.01419845,0.07837924,-0.02260325,0.03097553,0.02522433,0.01230952],"last_embed":{"hash":"o86f8i","tokens":377}}},"text":null,"length":0,"last_read":{"hash":"o86f8i","at":1753423450868},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)","lines":[177,219],"size":4553,"outlinks":[{"title":"<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**User's Guide to**_ **MDIEditor And Lotto WE**","target":"https://saliu.com/MDI-lotto-guide.html","line":9},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":11},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":15},{"title":"_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge","target":"https://saliu.com/bbs/messages/623.html","line":16},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":17},{"title":"_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_","target":"https://saliu.com/bbs/messages/569.html","line":18},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":19},{"title":"**Vertical or Positional** _**Filters In Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":20},{"title":"_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_","target":"https://saliu.com/bbs/messages/896.html","line":21},{"title":"**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_","target":"https://saliu.com/bbs/messages/919.html","line":22},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":23},{"title":"_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":24},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":25},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":26},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":27},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":28},{"title":"**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_","target":"https://saliu.com/decades.html","line":29},{"title":"_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":30},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":31},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":32},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":33},{"title":"_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_","target":"https://saliu.com/STR30.htm","line":34},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":35},{"title":"**<u>Lottery Strategy Software</u>**","target":"https://saliu.com/infodown.html","line":36},{"title":"Special software checks lottery strategies between several programs, apps.","target":"https://saliu.com/HLINE.gif","line":38},{"title":"Forums","target":"https://forums.saliu.com/","line":40},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":40},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":40},{"title":"Contents","target":"https://saliu.com/content/index.html","line":40},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":40},{"title":"Home","target":"https://saliu.com/index.htm","line":40},{"title":"Search","target":"https://saliu.com/Search.htm","line":40},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":40},{"title":"The lottery cannot be played successfully without software that creates lottery strategies.","target":"https://saliu.com/HLINE.gif","line":42}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{32}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12319101,-0.04472706,-0.00948704,-0.01547467,-0.02461218,0.06600346,0.00125475,-0.00429867,0.01419741,-0.00324727,-0.02327308,-0.01109075,0.06810303,-0.00879373,-0.02395778,-0.02854489,0.00079658,0.02514891,-0.02163582,0.01407825,0.06984181,0.00795145,-0.02400717,-0.09636,0.05247243,0.01022837,-0.04517397,-0.05998715,-0.05495899,-0.2224237,0.00051751,-0.01660176,-0.03241246,-0.02126194,-0.06918664,-0.02134095,-0.01249433,0.0865512,-0.03972551,0.05130433,0.02350393,0.00940185,0.01709407,0.04115692,-0.01226917,-0.05162288,-0.02456047,0.02236635,0.06050722,-0.01001845,-0.0639476,0.03401514,-0.00507886,-0.02718348,0.01547388,0.07342596,0.06527364,0.08548589,0.01096589,0.0279869,0.01662696,0.07090105,-0.18623777,0.05527572,-0.02690667,0.05234044,-0.01332989,-0.015959,0.03911234,0.03434074,-0.00165008,0.00641384,-0.00582046,0.10139045,0.08375223,-0.0568247,-0.03874401,-0.08810151,-0.06358236,0.00211643,-0.06537914,0.00261021,-0.01479198,-0.00255958,0.02245691,0.09515329,0.0482293,0.05236061,0.07891061,-0.08330049,0.04605586,-0.00247317,0.0653052,0.03034016,-0.00235895,-0.03145867,0.0868436,-0.01601241,-0.01481545,0.14704739,-0.00229228,-0.02281902,0.00611382,0.05348558,0.04004069,-0.02699416,-0.02183576,-0.02279512,-0.00923889,0.05054156,0.04970661,0.00355255,0.04745283,-0.06365369,-0.02479825,0.010854,-0.01080534,-0.02845466,0.01171341,-0.00310611,-0.05969812,-0.00149305,0.02036958,-0.01092494,-0.02232615,-0.01781253,-0.01685273,0.03195154,0.01308488,0.02154648,0.02304612,0.00812582,-0.13039838,-0.08427943,-0.00969373,-0.01457908,-0.0123376,-0.02159466,-0.0127818,0.01248617,-0.00451176,-0.06526723,0.07849983,-0.09862577,-0.01289794,0.03786369,0.02230002,0.015605,0.00359234,0.00977817,-0.00276591,-0.0040072,-0.03233059,-0.02618318,-0.00154665,-0.00178697,0.11331869,0.06873944,-0.03169537,0.01952809,-0.01509037,-0.01016188,-0.01513561,0.16411176,-0.02737245,-0.08009281,-0.00489902,0.05544198,-0.01742137,-0.07251204,-0.01023216,-0.01208168,-0.05197588,-0.02305797,0.097327,-0.03621917,-0.08868866,-0.04733641,-0.01375562,0.0195945,-0.01047207,-0.05158415,-0.0374669,0.06836132,-0.05519121,-0.06587472,0.03238827,-0.08363462,0.00708464,0.06562319,-0.06418186,-0.01020836,-0.04539263,0.00167123,-0.04602399,0.03725784,-0.05102818,-0.03906263,0.08380827,0.00950074,0.00581639,0.03769079,0.02861312,0.01421592,-0.07970633,0.03314358,-0.01962339,-0.04921326,0.07468387,0.02998759,-0.03470428,0.05676347,0.03864068,0.0237786,-0.0519888,0.02158939,0.03656634,0.03440337,0.02165595,0.03473276,-0.01081917,0.03514492,-0.060388,-0.20718962,-0.03852247,-0.08729634,-0.00268546,-0.01019876,-0.0149501,0.03676887,-0.03214759,-0.03641917,0.05959561,0.13357112,-0.01819593,-0.03070193,0.05535224,-0.00368148,-0.00758869,-0.07563085,-0.03953009,-0.03016109,0.05565323,-0.00805503,-0.01217018,-0.02438731,-0.04026888,0.03242904,-0.06868059,0.11752959,0.01021123,0.02058057,0.02323833,0.07853037,0.0288761,-0.02104334,-0.06489723,0.0019368,0.06359462,-0.03455329,-0.03072707,0.00591244,-0.01137932,-0.05827769,0.04280721,-0.00412729,-0.04904822,-0.00984176,0.02221495,-0.01241372,-0.03878741,0.0324505,0.05045138,0.02944101,-0.00706595,-0.00545222,0.02594502,0.0508444,-0.03083137,-0.08226983,-0.02595769,-0.01244494,0.00736134,-0.01837266,-0.00396279,0.04914202,-0.03256449,0.04630805,0.00911008,0.01909575,-0.05793091,0.02450896,0.04357345,0.01074627,0.09073022,-0.0116846,0.04954966,-0.00178485,0.02411262,0.0377771,-0.046361,-0.02444824,0.00817108,0.01233211,-0.04694682,0.07504658,0.08400126,0.0668461,0.04155028,0.03645159,-0.02694912,0.02133034,-0.01669368,0.02725998,-0.00705735,-0.04855359,0.01738888,0.02402733,0.01565032,-0.24741742,-0.00584834,-0.01847177,0.026534,-0.02152984,-0.02175358,0.05025658,-0.03985304,0.00262454,-0.03574927,0.10168639,0.02404976,0.01204007,-0.06904747,0.00266381,-0.01879663,0.05461695,-0.02747211,0.09029248,0.02084998,0.02994274,0.01015893,0.25756219,-0.00523958,-0.00356624,0.00265283,0.00753917,0.01351444,0.02054111,0.05063749,-0.0176761,0.0379176,0.04812636,-0.00942556,-0.0509788,0.06029007,0.01283777,0.05109812,0.01928417,-0.00947418,-0.07659557,-0.008146,-0.00871775,0.00104099,0.07662222,-0.00703046,0.00043242,-0.03560969,-0.00788978,0.03158859,-0.05377363,-0.0726248,-0.05328848,-0.01492057,0.03688573,0.03178503,0.00910125,-0.02875355,0.02311872,-0.03411081,0.05793444,0.00153457,0.03881416,0.02397696,0.00490198],"last_embed":{"hash":"6mcznv","tokens":297}}},"text":null,"length":0,"last_read":{"hash":"6mcznv","at":1753423450990},"key":"notes/saliu/Cross-Reference Lottery Strategies, Lotto Strategy Files.md#Cross-Reference Lottery Strategies, Lotto Strategy Files#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{32}","lines":[214,219],"size":624,"outlinks":[{"title":"Special software checks lottery strategies between several programs, apps.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"The lottery cannot be played successfully without software that creates lottery strategies.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
