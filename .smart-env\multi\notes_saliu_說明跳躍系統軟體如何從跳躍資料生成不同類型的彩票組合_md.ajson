
"smart_sources:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md": {"path":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08560973,0.00200051,0.02761604,-0.00569536,-0.00180434,0.08890856,0.02937439,-0.00499418,0.10636605,0.00125132,-0.02739059,-0.09493607,0.04949058,0.01146299,0.0121838,-0.03970306,0.00025878,-0.047341,-0.04522992,-0.03556107,0.08365732,-0.00880898,-0.03233583,-0.08434901,0.04698396,0.02630866,-0.00594454,-0.04138102,-0.01870483,-0.15445605,-0.01259955,0.02617272,-0.01791862,-0.0423678,-0.07054384,-0.04414717,-0.02421911,0.0601069,-0.03549907,0.04705777,0.01386073,0.03140928,0.02366306,-0.00533426,0.04903246,-0.00493882,-0.04980171,-0.02032685,0.05090616,-0.03598653,-0.0447049,-0.00101579,-0.00530242,0.01693715,0.02406499,0.00391008,0.03994434,0.05155199,0.03411737,0.04373823,-0.00664596,0.03179228,-0.20591521,0.0102338,-0.00741536,-0.02120912,-0.01885442,-0.01032839,0.05327927,0.05910068,-0.07094633,0.03484004,-0.00423561,0.05374445,0.06967848,-0.00971762,0.00522324,-0.04341809,-0.0870339,0.04179577,-0.0142748,0.06316464,-0.02927198,-0.01615102,0.00539738,0.03670558,-0.01210291,-0.04538491,0.01551604,0.00273584,0.05252412,-0.02508267,-0.02497611,0.0363808,-0.01729661,-0.0247916,0.04803402,-0.00041362,-0.07225055,0.086031,0.00594506,0.03251162,-0.02960503,-0.02445302,0.03442614,-0.06832162,-0.01353503,0.00713899,-0.01616403,-0.04303228,-0.01000894,0.03378863,0.05462408,-0.04439713,-0.06576497,0.00880206,0.02496334,-0.02726943,0.00259538,0.01040069,0.0414836,0.0334173,-0.0129474,-0.00669539,0.02159416,-0.00627518,-0.02150281,0.03480119,0.06514145,0.02016504,0.05919657,-0.01327724,-0.02439583,-0.05107564,-0.01478024,0.01255323,-0.06192953,0.06375287,0.03280451,-0.01018895,-0.00828626,-0.0536529,0.02958042,-0.10948323,-0.05493601,0.07856596,-0.02294976,-0.0068744,-0.01304395,-0.02958992,0.01166312,0.02195513,-0.01473917,0.00472123,0.01116709,-0.04569593,0.07074998,0.15198523,-0.02201364,-0.01761981,-0.08522959,-0.06246747,-0.03382404,0.12541819,0.01822702,-0.06488147,-0.02287861,0.02012231,-0.02603506,-0.09252261,0.04474698,-0.03187392,-0.08800111,-0.00414449,0.1042132,-0.02172088,0.04710665,-0.00597133,-0.00728853,0.01494912,0.01840895,-0.07098538,-0.08410548,-0.00270637,-0.0524667,-0.10387858,-0.00719017,-0.0608805,-0.04164344,-0.0378718,-0.05739063,-0.00221281,-0.03310719,0.04506394,-0.07179252,-0.04387806,-0.03167198,-0.00567426,0.02461847,-0.03060979,0.09513003,-0.00340379,0.02476015,-0.02893523,-0.04016093,0.0408239,0.02937817,-0.02472088,0.02917685,0.05165483,-0.03407897,0.00916784,-0.01834764,0.00845582,0.01900111,0.03160748,-0.02806085,0.04437642,-0.022327,0.02409186,0.0426929,0.01633728,-0.0799676,-0.24364311,-0.0409835,-0.05564421,-0.0136959,0.02203582,0.00541789,0.02266288,-0.01629278,0.07033009,0.07157662,0.05421823,0.05660221,-0.09101658,0.03888474,-0.02627151,-0.00188881,-0.0318803,-0.0138232,-0.01081736,0.06950906,-0.00083156,0.0455937,-0.0156504,0.02584651,0.02919185,-0.0238358,0.15925334,-0.01218769,0.00429735,-0.02493818,0.08424281,0.01945745,-0.00996929,-0.04838565,0.02832852,0.03267972,-0.07879406,0.02258774,-0.02916856,-0.04828505,0.00791882,0.04323124,0.01340625,-0.0753645,-0.02626212,-0.01588209,-0.03118167,-0.0467729,-0.00682953,0.10511536,0.05856172,-0.00986684,0.07104287,0.04058056,0.02110538,-0.02407069,-0.02265116,-0.00521543,-0.06245778,0.00453097,-0.02071642,-0.01725234,0.0488617,0.02924982,-0.00751408,0.02419717,-0.01218685,-0.00842422,0.01956781,0.04812912,-0.0478787,0.15391906,0.00636815,-0.04666923,0.03593732,0.0387929,-0.04976087,-0.05972897,0.00573557,-0.01287334,0.05907477,0.02357308,0.04795668,0.03267371,0.02347533,-0.00795786,0.105106,0.03371412,0.02516325,-0.01796114,-0.0669845,0.02229845,-0.03647238,0.0002342,0.04488497,-0.00362249,-0.31996107,-0.01335583,0.01956995,0.00323844,0.0426468,-0.02762543,-0.00455648,-0.02344496,-0.07634991,0.02152659,0.04796146,0.0425541,0.01990964,-0.03853185,-0.00605506,0.02119509,0.00699138,0.0002502,0.05051022,-0.01534995,-0.00419376,0.01706183,0.23871313,0.00895028,0.00909003,0.00106384,0.01555067,0.02935566,0.07719661,0.0146423,-0.01825878,0.00053795,0.10536759,0.0087102,0.02353219,0.03830402,-0.00965804,0.0705872,0.03678191,-0.02374323,-0.00414442,0.02994977,-0.05933132,0.02283762,0.08674027,-0.01943724,-0.01529177,-0.04391249,-0.02770627,0.03161773,-0.04410236,0.00032837,0.02261713,0.0087428,0.02250585,-0.01423044,-0.02840524,0.01713363,-0.00497553,0.04457602,-0.00885291,0.02259108,0.02053219,0.08897893,0.04289623],"last_embed":{"hash":"1p7mtr8","tokens":464}}},"last_read":{"hash":"1p7mtr8","at":1753495721015},"class_name":"SmartSource","last_import":{"mtime":1753430667914,"size":4702,"at":1753495676981,"hash":"1p7mtr8"},"blocks":{"#":[1,4],"###1. 跳躍資料的來源與定義":[5,11],"###1. 跳躍資料的來源與定義#{1}":[7,8],"###1. 跳躍資料的來源與定義#{2}":[9,9],"###1. 跳躍資料的來源與定義#{3}":[10,11],"###2. 基於 FFG 中位數的號碼池選擇":[12,18],"###2. 基於 FFG 中位數的號碼池選擇#{1}":[14,15],"###2. 基於 FFG 中位數的號碼池選擇#{2}":[16,16],"###2. 基於 FFG 中位數的號碼池選擇#{3}":[17,18],"###3. 生成不同類型的彩票組合":[19,45],"###3. 生成不同類型的彩票組合#{1}":[21,22],"###3. 生成不同類型的彩票組合#{2}":[23,26],"###3. 生成不同類型的彩票組合#{3}":[27,33],"###3. 生成不同類型的彩票組合#{4}":[34,37],"###3. 生成不同類型的彩票組合#{5}":[38,44],"###3. 生成不同類型的彩票組合#{6}":[45,45]},"outlinks":[]},"smart_blocks:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###1. 跳躍資料的來源與定義": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12835854,-0.00041734,0.02981512,0.00267017,0.02800046,0.03391089,0.0167298,-0.02343115,0.0841383,-0.01029196,-0.00414642,-0.07582995,0.05227684,0.01697441,0.04922052,-0.02917659,0.01660549,-0.05009395,-0.04916888,-0.03872921,0.07866514,-0.01955006,-0.01544951,-0.09528204,0.04378182,0.02595018,0.01167541,-0.05287407,-0.01255535,-0.14993709,-0.03114341,0.01547102,0.00580732,-0.04488828,-0.04894894,0.00968485,-0.00888822,0.05478366,-0.02068304,0.03945294,-0.01396414,0.03937326,0.04392039,-0.01203676,0.02699783,-0.0152795,-0.03210456,-0.05402127,0.04334157,-0.02030595,-0.09369116,-0.00125852,0.00811855,0.0068503,0.03031153,0.02830928,0.04000771,0.056162,-0.0140781,0.00553355,0.00771551,-0.0005868,-0.19890425,0.02961206,-0.00994592,-0.01463328,-0.00327256,-0.03611502,0.0342269,0.04180846,-0.07411092,0.02490159,-0.05305644,0.07705234,0.05315731,-0.02942986,0.02216797,-0.00779076,-0.05855181,0.01070111,-0.00772528,0.04278871,-0.0492146,0.01834015,-0.00754625,0.05279081,-0.00548829,-0.03048832,-0.0086017,-0.00190722,0.01955484,-0.08950005,-0.03636454,0.04723532,-0.03024991,-0.00409705,0.04309831,0.0410137,-0.08874507,0.08212443,-0.01116685,0.03300368,-0.00582886,-0.02732827,0.03198456,-0.0552323,0.00878619,-0.01444636,-0.0050675,-0.02441283,-0.01030552,0.01567252,0.0245656,-0.06942944,-0.03974783,0.02573147,0.0454361,0.00800464,-0.02182067,-0.01492417,0.01017268,0.04103248,0.00299203,-0.01735008,0.00612028,-0.03413777,0.02248023,0.03271218,0.06891502,0.03664603,0.04747842,-0.00383779,-0.02641809,-0.04970203,-0.02053311,-0.00754106,-0.05901253,0.04341784,0.02681831,-0.04912065,0.01399777,-0.06866028,0.00766681,-0.11364651,-0.06623409,0.07524699,-0.06561743,0.02377154,0.00501174,-0.04670812,0.00674239,0.03630676,-0.00715795,-0.04023676,0.02198053,-0.0240644,0.07727665,0.14452989,-0.02108511,-0.00554133,-0.04940557,-0.04932787,-0.06895114,0.1149957,0.02833625,-0.03196136,-0.0189017,0.0261674,0.00663842,-0.07021994,0.03458209,-0.04393132,-0.04332217,-0.02440185,0.11326025,-0.00898964,0.05044074,-0.03839708,-0.04608325,0.03613961,-0.02549581,-0.05421032,-0.07701398,0.03163242,-0.01655944,-0.12600906,0.00499782,-0.03297502,-0.00381591,0.00042809,-0.05253595,0.01580194,0.00852464,0.03264594,-0.08606815,-0.03804455,-0.01700638,-0.01438724,0.00331316,-0.04056987,0.11151284,0.00170974,-0.01264668,-0.02118849,-0.02625345,0.00678299,0.02764218,0.00783463,0.02372247,0.067262,-0.03714758,0.02921179,0.02817491,-0.02058475,0.01239729,0.0240803,-0.02993818,0.03843921,0.00910989,0.03172486,0.01894886,-0.01347669,-0.09694418,-0.23921083,-0.05037527,-0.02501294,-0.0202094,0.04052228,-0.02838024,0.02173145,0.02650603,0.07469444,0.05024442,0.07557555,0.05156749,-0.08552725,0.04202524,0.00718764,0.0068185,0.01485213,0.01399384,0.01225068,0.06618778,0.00029283,0.04039589,0.01117225,0.03592186,0.02591609,-0.04918241,0.13354778,0.00489599,0.02052546,0.00173751,0.07106531,0.03451373,0.01925458,-0.09460418,0.01916871,0.07465401,-0.11899868,0.00855853,-0.03578429,-0.03906333,0.00715426,0.03751451,0.00025113,-0.06107217,0.00518418,-0.06374295,-0.00990005,-0.06490271,-0.01605098,0.0733151,0.02943078,-0.01089275,0.04251993,0.05518806,0.01266145,-0.02208644,-0.05079023,-0.03062604,-0.04346435,-0.02158585,-0.02730031,-0.00991256,0.04126734,0.00881693,-0.02130778,0.04479622,-0.01727748,0.02756326,-0.0082323,0.06284902,-0.05253389,0.14520982,0.00840252,-0.0391573,0.00217209,0.03330707,-0.06205081,-0.05510858,0.00595227,-0.01839403,0.07124015,0.02701423,0.02524006,0.01458939,0.03100029,0.01173985,0.08208334,0.00356536,0.03623513,-0.01646237,-0.04001486,0.00588523,-0.01895761,0.00802218,0.08798842,-0.01076325,-0.31805608,-0.02076007,0.01708296,-0.00989854,0.04684171,0.01086875,0.00221113,-0.06362794,-0.05157091,0.04042254,0.03165212,0.04008941,0.00418941,-0.0439293,0.0017052,0.01513818,0.03442517,0.00738713,0.03807599,-0.05234579,-0.02324763,-0.01701571,0.24713296,0.0481215,0.02193948,-0.026783,-0.00434993,0.00964969,0.04584047,0.02664887,-0.00369012,-0.00226951,0.07941681,0.02501851,-0.0109522,0.06999268,-0.02458843,0.0755911,0.01308697,0.00465174,-0.01367207,0.02834266,-0.07176204,0.03107253,0.11450949,-0.02518893,-0.00705004,-0.0412816,-0.00077926,0.063003,-0.0110963,0.02699989,0.0109665,0.02771778,0.02689883,0.005768,-0.02947652,-0.02619774,0.00560828,0.01039583,0.03817064,0.0174482,0.03996462,0.08842955,0.08633573],"last_embed":{"hash":"1k723q1","tokens":348}}},"text":null,"length":0,"last_read":{"hash":"1k723q1","at":1753495720444},"key":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###1. 跳躍資料的來源與定義","lines":[5,11],"size":365,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###2. 基於 FFG 中位數的號碼池選擇": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08626552,0.00279305,0.0193504,-0.01268163,-0.00997711,0.10354163,0.04517225,0.01114786,0.10110891,-0.00554999,-0.01601733,-0.07654681,0.01826943,0.02028852,-0.00917256,-0.01848647,0.01058415,-0.0713307,-0.02604476,-0.03101811,0.07889556,-0.01737333,-0.03923762,-0.09224809,0.05181734,0.01467795,0.00290484,-0.0533577,-0.02555994,-0.14799719,-0.00713687,0.01999914,-0.00716421,-0.04159586,-0.06947809,-0.05408838,-0.02781282,0.04602323,-0.03359367,0.03818296,0.0134417,0.00410334,0.03225555,-0.00120674,0.04926597,-0.02681417,-0.03555124,-0.03553451,0.05927299,-0.0352281,-0.02889502,0.01822108,0.00823907,-0.03137621,0.02314763,0.00141549,0.04246221,0.05671823,0.0604452,0.03474969,-0.00651381,0.02962775,-0.21046793,0.01241165,-0.02497,-0.01734203,-0.00197792,-0.02371417,0.04030385,0.06333356,-0.05580451,0.0464016,-0.00026439,0.03507573,0.06620942,-0.03556961,0.00619391,-0.05076738,-0.08476086,0.03817252,-0.00750785,0.05978442,-0.01720832,-0.01736566,0.01650261,0.05446466,-0.00854293,-0.04275356,0.03116375,0.00123108,0.05874908,0.00446464,-0.03066542,0.02942105,-0.0413043,-0.01163915,0.06812281,0.01303451,-0.08769163,0.08658963,0.01952927,0.02574784,-0.04718473,-0.02391673,0.04909561,-0.07015906,0.00219744,0.0147737,-0.01663074,-0.03687358,-0.03080411,0.04848349,0.04719086,-0.0407403,-0.03324836,0.02865987,0.01992643,0.00331354,-0.01989016,0.0121338,0.03518352,0.03217795,-0.02380663,-0.01877519,0.03879332,-0.01987798,-0.01240153,0.05526788,0.03315146,0.02576466,0.04494482,-0.01701344,-0.03884905,-0.04042429,-0.01943585,-0.01853196,-0.05135576,0.06521474,0.03550807,0.00113015,-0.01240873,-0.05128967,0.00539239,-0.10328951,-0.05283087,0.09110477,-0.04115222,-0.02443432,-0.01189397,-0.02298286,0.03416911,0.0413967,-0.01491676,-0.00917578,0.02020836,-0.05156552,0.08014285,0.14585334,0.0010386,-0.03717764,-0.06549314,-0.06447107,-0.01993958,0.13072178,-0.00127207,-0.0639176,-0.02679415,0.01801523,-0.01612831,-0.08506411,0.01993474,-0.05618394,-0.09128902,-0.02306497,0.09425879,0.00929195,0.02451205,-0.00857823,-0.02239955,0.02016492,0.03614361,-0.05903471,-0.07435332,0.00117972,-0.05675993,-0.10140987,-0.01291749,-0.07060417,-0.01697139,-0.0333166,-0.07908537,-0.00269675,-0.03379756,0.04340576,-0.06484996,-0.04942121,-0.02832788,-0.01885668,0.03427643,-0.04493589,0.08152726,-0.0234677,0.02028608,-0.02008988,-0.03053922,0.04738661,0.02195031,-0.05873587,0.02247022,0.03679637,-0.01155518,0.03864865,-0.00926741,0.00936193,0.01057515,0.04067157,-0.02053031,0.05779201,-0.03507495,0.01761857,0.05550531,0.00867039,-0.09121437,-0.23688865,-0.0570582,-0.03548656,-0.02824874,0.01269788,0.01754162,0.02506137,-0.01176924,0.07214734,0.0813271,0.0393558,0.03977708,-0.09411775,0.02898945,-0.00623425,-0.02206342,-0.02361754,-0.02238878,-0.00508399,0.04591325,0.02877067,0.04737229,-0.02576168,0.0193271,0.05448808,-0.01884195,0.14885737,-0.01735137,0.01117451,-0.01659586,0.07809355,0.00183231,-0.02383498,-0.03851321,0.03091635,0.0143051,-0.08511432,0.00655157,-0.05566709,-0.03540096,0.01510939,0.04899118,0.00217532,-0.09043922,-0.0332954,-0.02067821,-0.03518801,-0.0242645,-0.0215268,0.08461946,0.0666523,-0.01440945,0.0664399,0.03285547,0.03131471,-0.01293472,-0.01811018,-0.02909216,-0.08450156,0.0325417,-0.0313349,-0.01503528,0.06035103,0.01070366,0.00446315,0.02722427,-0.00477849,-0.00300514,0.03262011,0.06668012,-0.07383019,0.13963675,0.01985637,-0.04966102,0.0638447,0.02330417,-0.05097158,-0.06423243,0.0000878,-0.01419805,0.08655264,0.02229117,0.04641838,0.02918112,0.02835364,-0.00010168,0.09593514,0.04518246,0.00402531,-0.0257935,-0.07586829,0.01051153,-0.03733935,-0.01219928,0.0356968,0.00028326,-0.31005624,-0.02036182,0.01205775,0.01500431,0.0511633,-0.01186725,0.01740204,0.00134386,-0.07738309,0.01793215,0.03543116,0.04544342,0.00753892,-0.03991948,-0.0051208,-0.00479986,0.00942611,-0.00916764,0.03220512,0.0087361,0.00188961,0.02830301,0.23325279,0.00409347,0.00968233,-0.0115043,0.01645126,0.03466726,0.07958958,-0.00071667,-0.02260179,0.01837286,0.09707936,0.01385588,0.01988205,0.0428144,-0.00542284,0.05521945,0.0430346,-0.02092209,0.02076359,0.02531094,-0.05128164,0.01582702,0.09227583,0.00423755,-0.00796895,-0.040808,-0.00181744,0.03749899,-0.04612757,-0.0004706,0.03298812,-0.01383524,0.01107313,-0.01214543,-0.01012484,0.00254983,0.02071593,0.05148207,-0.00278168,0.0445512,0.01434087,0.09505197,0.03653167],"last_embed":{"hash":"1evlzu6","tokens":330}}},"text":null,"length":0,"last_read":{"hash":"1evlzu6","at":1753495720541},"key":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###2. 基於 FFG 中位數的號碼池選擇","lines":[12,18],"size":369,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10523462,0.00088219,0.00449062,-0.03989682,-0.00271394,0.06592578,-0.0340599,-0.02946029,0.08086064,0.00916523,0.026739,-0.06237116,0.07756305,-0.01259584,0.03585923,-0.02360768,0.02338407,-0.0459841,-0.05079325,-0.05207202,0.07924438,-0.05125462,-0.03097719,-0.1217066,0.01635207,0.01391221,0.02618428,-0.01517295,-0.00900453,-0.19765909,-0.01518571,0.00541703,-0.00221904,-0.04926118,-0.05109121,-0.03936155,-0.04009374,0.04463561,-0.05317535,0.03016699,-0.02735116,0.03334473,0.02550517,-0.03159621,0.03441103,-0.01777224,-0.03016286,-0.00763298,0.03635646,-0.01738411,-0.05962902,-0.00921163,-0.01255465,0.05676974,0.04530471,0.03112426,0.05341537,0.06178934,-0.01514178,0.02578237,0.05402974,0.01393404,-0.17507593,0.01779943,0.02463528,-0.00342983,0.04054608,-0.00902972,0.07305411,0.06658912,-0.05370513,0.05015142,-0.01986087,0.10608,0.01832129,0.02723548,-0.01681817,-0.02922934,-0.06409753,0.04076415,-0.02077051,0.01649492,-0.05841878,0.02240708,-0.00033926,0.04739544,-0.00751351,-0.01556914,0.01252582,-0.02751926,-0.00049392,-0.06040782,-0.02705197,0.03666481,0.00372413,-0.00655469,0.04686264,-0.00808358,-0.0767831,0.09675507,0.01337998,0.03656843,0.00269796,-0.00093136,0.00380895,-0.03848941,-0.0589586,-0.0295348,-0.03249475,-0.04302396,-0.00397354,0.00652017,0.01839286,-0.05219838,-0.05818943,0.01411202,0.05233526,0.00278044,-0.00760299,-0.01540115,-0.04696441,0.05872997,0.0075789,-0.00499757,-0.00506356,-0.02002698,0.00463057,0.06516059,0.06734053,0.04309071,0.02921431,0.02468074,-0.05300179,-0.08564188,0.02844642,-0.05243494,-0.03182841,0.02950042,0.04047009,-0.04494497,0.00564181,-0.05632985,0.03430839,-0.12000966,-0.04089471,0.09388605,-0.05559044,0.04374541,-0.01374026,-0.02542926,0.01307392,0.01460374,0.00339477,-0.03665042,0.01092554,0.03837165,0.08797378,0.09913641,-0.04950744,-0.00143591,-0.03919519,0.0004123,-0.06119432,0.12495822,0.03817021,-0.08886954,-0.01319529,0.03210745,0.01537509,-0.05209015,-0.0346747,0.00738929,-0.04595018,-0.03570739,0.09432824,-0.02296083,-0.0044362,-0.03646022,-0.05004362,0.04736185,0.01928722,-0.01191476,-0.04006596,0.01359855,-0.0463174,-0.13594212,-0.00672443,-0.0322581,-0.00476413,-0.02789058,-0.0606124,0.01679699,0.00896713,0.04185946,-0.03727226,-0.05570149,-0.00556406,-0.0298467,0.06458224,-0.0497735,0.10219298,0.01567745,-0.01002156,-0.00087186,-0.00385457,0.01173334,0.04164787,-0.02873006,0.02988027,0.03889032,-0.00086154,-0.02668766,0.04339619,0.02547356,-0.01765939,0.04127404,-0.03928911,0.0185085,0.01464887,-0.01624574,0.01172887,0.03517015,-0.10509494,-0.23009068,-0.04342805,-0.03539254,-0.01950903,-0.00205909,-0.02597396,0.024265,-0.0076037,0.06036946,0.05217908,0.08640362,0.00781881,-0.03795628,0.06807433,-0.03321612,0.00262107,-0.05577698,0.00724498,0.00704718,0.07062875,0.03506393,0.03413362,0.05363229,-0.0127863,0.04154775,-0.06284792,0.15123455,0.01742856,-0.00537465,0.01327594,0.09623244,0.01879566,0.01853473,-0.07744871,0.03480992,0.05590468,-0.08021002,-0.0336507,-0.03434272,-0.02285251,-0.02632162,0.02813017,-0.01573118,-0.09912472,0.00183901,-0.06029382,-0.0393424,-0.06061462,-0.01660759,0.06554216,0.04516806,-0.06322259,0.05875291,0.01315416,0.01396738,-0.02691872,-0.04012284,0.02634935,-0.06240075,0.00168133,-0.01319254,-0.00391789,0.00254747,0.01701692,-0.03983318,0.00664228,-0.01638126,0.03136622,0.02551757,0.02072393,-0.02989303,0.15178239,0.02289474,-0.00909861,0.04267896,-0.00021328,-0.03202796,-0.05955096,-0.00634073,-0.00620294,0.04252633,0.04589844,0.01342138,0.04610675,0.0536311,-0.03405368,0.06273321,0.00387351,0.01207034,-0.03005895,-0.08785371,0.02132754,-0.02879513,0.01865698,0.04648424,-0.01046862,-0.28740197,0.02077044,-0.01436171,0.00649657,0.02688918,-0.04324071,-0.00465059,-0.04210874,-0.02512976,0.08257212,0.02514508,0.03436846,0.03969276,-0.03105967,0.01867076,0.02693745,0.00846255,0.00907027,0.04987166,-0.04208409,0.01543445,0.00918232,0.25408354,0.04432772,0.02442054,0.03455444,-0.01673647,0.01789035,0.02920956,0.04498855,0.00794187,0.01497557,0.08920544,0.01250656,-0.00052841,0.07394813,-0.02301196,0.0693891,-0.00920833,0.0180289,-0.05454805,0.00266066,-0.02021055,0.04786813,0.12308418,-0.00629125,-0.0108616,-0.08140974,0.00740286,0.06508019,-0.04631767,0.03887495,-0.00021392,0.01964883,0.00672541,0.0373913,-0.02080205,0.00022756,-0.01211135,-0.01790253,0.05363406,0.02224739,0.02654678,0.04597275,0.0302102],"last_embed":{"hash":"1dpi126","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"1dpi126","at":1753495720645},"key":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合","lines":[19,45],"size":1321,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08449944,-0.0137425,-0.01913312,-0.04965493,-0.01050565,0.07674672,-0.02955624,-0.02538811,0.03829835,-0.02745071,0.04888013,-0.09052095,0.03143041,-0.03030326,0.03608202,-0.04316805,0.01702097,-0.05445087,-0.06945516,-0.02930587,0.10945823,-0.06012761,0.00347308,-0.0862869,0.05229758,-0.00323078,-0.00097534,-0.00363319,0.01829095,-0.19529524,-0.00928041,0.02038736,0.0239942,-0.0429379,-0.01750036,-0.01928465,-0.06145669,0.05342485,-0.0380384,0.02432838,0.00363551,0.04371913,0.03359636,-0.04104113,0.05402652,-0.0411435,0.0041493,-0.00785039,0.07182707,0.00429955,-0.01466794,-0.00367598,-0.0334229,0.06882392,0.05274928,0.01458141,0.0768381,0.05284177,-0.00460798,0.05694794,0.06807609,0.04553549,-0.19647652,0.02205487,0.03300513,-0.02857779,0.03946483,-0.00452393,0.04650673,0.06382813,-0.02509111,0.03324261,-0.01066362,0.06182799,0.00975583,-0.00590934,-0.02814811,-0.03568717,-0.08919782,0.03381026,-0.05216905,0.03551569,-0.02782424,0.00833901,0.01023975,0.04211153,0.02330925,-0.01242431,0.00683404,-0.01569696,-0.01479573,-0.03599133,-0.03029257,0.03225169,-0.01681154,0.01985298,0.0387422,0.03917114,-0.09456182,0.1160969,-0.0029956,0.02743818,-0.03696223,-0.00411531,0.01752148,-0.01163882,-0.0468642,-0.02654017,-0.01958183,-0.032354,-0.0090876,0.02948492,0.00309881,-0.02979617,-0.06110046,0.02136434,0.04070576,-0.03765499,-0.02794445,-0.01852257,-0.04409684,0.06054524,0.00110429,-0.00441365,-0.01722947,-0.03534766,0.04207247,0.03862548,0.07251471,0.03713338,0.03880704,0.02834903,-0.09102092,-0.083162,0.02281906,-0.07537854,-0.02013522,0.02118109,0.03900158,-0.03516936,-0.01984648,-0.08504634,0.01828988,-0.11962231,-0.02564451,0.0905953,-0.04944054,0.01177454,0.01598732,-0.02214334,0.01536104,0.02882841,-0.02080565,-0.04902798,0.02195532,0.04384875,0.08138224,0.08810162,-0.05960819,-0.01939024,-0.00801197,-0.00989547,-0.0429088,0.1042928,0.01789617,-0.09757101,-0.01533563,0.02057442,0.00698825,-0.05608774,-0.02070052,-0.00680061,-0.04093401,-0.03433034,0.08515662,-0.03437429,0.00377766,-0.04133627,-0.06580632,0.05365046,-0.00437924,-0.02588275,-0.04195309,0.01134992,-0.05070946,-0.08188288,0.00733086,-0.01732488,0.06642219,-0.00316722,-0.06773797,0.02976093,-0.02502783,0.05490759,-0.04430634,-0.05666663,-0.00839246,-0.04782205,0.06272315,-0.04449844,0.07715184,0.03114464,-0.04981968,0.00530139,0.00208861,0.01239156,0.03603185,-0.01403573,0.01645925,0.04715403,0.02568198,-0.00076918,0.03411834,0.05307686,-0.02979362,0.02599255,-0.00725896,0.00314435,0.02656885,-0.00475833,0.04297453,0.00798155,-0.08979214,-0.21371415,-0.01382442,-0.032465,-0.05302358,-0.00096783,-0.02032872,0.03619202,-0.00755001,0.0702865,0.02692268,0.09474608,-0.00477401,-0.01508093,0.06369048,-0.0428548,0.0142709,-0.04687531,0.01452157,0.02832486,0.06977546,0.04526654,0.02673638,0.01210973,0.01935246,0.09108564,-0.0753611,0.14508367,0.03360132,0.00105772,0.03563341,0.09287778,-0.03038033,-0.00714592,-0.12928808,0.02176507,0.00416561,-0.11186413,-0.00862333,-0.07251818,-0.01737539,-0.02309284,0.01987519,0.0118885,-0.07727516,0.0093523,-0.04636131,-0.02359924,-0.02967941,-0.02751946,0.03859283,0.07771656,-0.04719174,0.04037126,0.02686682,-0.0064092,-0.05770458,-0.02499979,0.00254249,-0.10102587,0.01290961,0.00087223,0.00050437,0.02965051,0.01837712,-0.04464184,0.01363101,-0.00920396,0.02489788,0.0173023,0.04043599,-0.03344488,0.12988564,0.03337084,-0.01324089,0.05674582,-0.01922874,0.0059707,-0.07442873,0.02964677,-0.0136427,0.01049166,0.03144974,0.03633965,0.0460621,0.06651598,-0.00045777,0.0190907,-0.0097423,-0.00251277,-0.02725787,-0.05769226,0.01156713,-0.03402973,0.00056801,0.01771562,-0.01708736,-0.29722223,0.03685481,-0.0395003,0.00326997,0.00492989,-0.01576501,-0.01240552,-0.03031836,-0.07052371,0.06470569,0.0219202,0.05337304,0.05262985,-0.03158798,0.01445617,-0.00865226,0.01018379,-0.01746194,0.04846785,0.01346275,0.02631238,0.00874532,0.24106857,0.05190537,0.0169616,0.0428458,0.00730984,-0.01716541,0.02240589,0.03897139,0.01478552,-0.01565865,0.07875776,0.03018024,0.0050356,0.08366971,-0.0288046,0.0492571,0.00239307,0.02664839,-0.03347203,0.01354563,0.0018743,0.00498457,0.14230074,0.04567151,-0.02707118,-0.04930849,-0.00784369,0.02728605,-0.06395239,0.01008246,-0.01574195,0.02685835,-0.01281507,0.04873208,0.00642949,0.00226618,-0.00550521,-0.03394374,0.05505933,0.05209402,-0.00205245,0.02468036,0.03250452],"last_embed":{"hash":"1keuhfz","tokens":224}}},"text":null,"length":0,"last_read":{"hash":"1keuhfz","at":1753495720819},"key":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{2}","lines":[23,26],"size":241,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09946398,0.00682088,0.01959875,-0.01920194,0.01354222,0.02902902,-0.01335706,-0.02197508,0.09858882,0.01694923,-0.01253626,-0.06969707,0.08870766,0.00528136,0.03494025,-0.01817553,0.01304441,-0.01986351,-0.0490505,-0.05696408,0.04741566,-0.00459525,-0.05400193,-0.13119067,-0.01084812,0.02111939,0.02271784,-0.04101425,-0.01574296,-0.18212324,-0.02758478,0.01652712,-0.02693317,-0.05049802,-0.06822177,-0.01763891,-0.01609138,0.06128195,-0.070323,0.03268317,-0.02315507,0.02380802,-0.00070848,-0.0204388,0.01476186,0.00790725,-0.06658165,-0.00512666,0.00974671,-0.02846147,-0.07207614,-0.00386087,-0.00932219,0.0162142,0.02226365,0.03360927,0.01460154,0.0577552,-0.0389993,0.00652025,0.03922854,-0.00990671,-0.1635906,0.0200183,0.00275172,0.01235176,0.01857115,0.00833574,0.06372585,0.08946152,-0.07247588,0.04554547,-0.03911312,0.11114324,0.03138436,0.04405963,0.00365893,-0.02202052,-0.0486314,0.04499222,0.00006708,-0.00079277,-0.06982563,0.03300631,-0.01239732,0.01811466,-0.01625327,-0.00846973,0.01726175,-0.01501192,-0.0047156,-0.06310668,-0.00442366,0.02155167,0.02281309,-0.00271882,0.03733005,-0.0376895,-0.04967129,0.10059724,-0.00847039,0.03114811,0.04557578,0.0188507,-0.00431955,-0.04509778,-0.05373715,-0.02956575,-0.04183204,-0.04386297,0.01475605,0.00521898,0.04679693,-0.07989783,-0.05390782,-0.00708265,0.05009801,0.02024832,0.00253048,-0.01775142,-0.0157366,0.05956515,0.01677691,-0.02580713,0.02913564,-0.01634401,-0.01326043,0.08417965,0.04682735,0.04036949,0.02696561,0.01515143,-0.0375116,-0.05549834,0.00875569,-0.04530668,-0.04187649,0.05038246,0.03120682,-0.03513562,0.02585991,-0.02935956,0.03634271,-0.09644544,-0.02787981,0.09946711,-0.06200046,0.05394034,-0.03794137,-0.01512143,0.01195365,-0.001508,0.02832703,-0.05048895,-0.00186971,0.00957581,0.09106682,0.10959067,-0.02349996,0.02831228,-0.05753492,0.00629566,-0.05964243,0.13135581,0.02966598,-0.06381799,-0.02336188,0.05950429,0.01982301,-0.04267111,-0.00202887,-0.00162307,-0.04193186,-0.00748821,0.10468712,-0.02200392,0.00021724,-0.00552173,-0.02222617,0.01725659,0.01668959,-0.02513457,-0.06350477,0.03853728,-0.01948673,-0.15169594,-0.01050645,-0.03868403,-0.05357823,-0.00931544,-0.06205635,-0.00240624,0.01774453,0.04487568,-0.02595985,-0.03895333,-0.02011229,-0.02481625,0.04514968,-0.03383232,0.1116654,-0.00333357,0.01639636,-0.01579664,-0.01411441,0.01821275,0.05051052,-0.02323195,0.04115615,0.05661534,-0.00671917,-0.02100298,0.03778293,0.01989505,-0.0080412,0.04019776,-0.04744408,0.04870681,-0.00125402,-0.01151275,-0.00731562,0.00635712,-0.08872913,-0.23111059,-0.02489491,-0.02547873,0.00743697,0.00771716,-0.03367391,0.0032797,0.00846661,0.04293749,0.05893443,0.07473991,0.03987114,-0.03979662,0.04997304,-0.02084405,0.01227904,-0.03789524,-0.00023311,0.01073352,0.05122469,-0.01049867,0.03695589,0.05707221,-0.03836795,0.00731247,-0.04072451,0.16633902,0.01916455,0.01982774,-0.01335259,0.08693544,0.06388778,0.01547237,-0.06614917,0.05715998,0.09428936,-0.06132223,-0.02730231,-0.00605447,-0.03900822,0.0073344,0.0246035,-0.03175982,-0.1120996,0.00876981,-0.07306363,-0.0512097,-0.07002237,-0.00713721,0.06400354,0.0045943,-0.04041708,0.06457036,0.03168696,0.01250096,-0.00172077,-0.06824189,0.03773991,-0.03928667,0.00602539,-0.02110389,-0.00318244,-0.00042332,0.01821712,-0.01816726,0.00605326,-0.03716981,0.00327123,0.01804057,0.00498887,-0.03694298,0.14926001,-0.00009774,-0.03663105,0.00314293,0.02724328,-0.07320764,-0.06013397,-0.03499651,-0.01075639,0.03470578,0.00137185,0.01196477,0.01708399,0.02955332,-0.03342054,0.07622736,0.0166408,0.02703447,-0.02740376,-0.07211041,0.02967931,-0.00862443,0.0098843,0.07676134,-0.0022116,-0.28567728,-0.00138191,-0.00155496,0.01600925,0.04203933,-0.03032166,0.01145543,-0.05821094,-0.02324658,0.05322192,0.04071287,0.0226313,0.01153757,-0.01609206,0.00999556,0.07623093,0.03147202,-0.01333478,0.04684576,-0.07700833,0.01271894,0.00912236,0.26280558,0.05213405,0.02695416,0.0180203,-0.01370406,0.04080341,0.04096982,0.0433427,-0.01066669,0.02335208,0.08566903,-0.01721542,0.01530599,0.06594802,-0.03002819,0.05677549,-0.00178155,0.02486868,-0.05109217,-0.00275641,-0.03077815,0.06089117,0.09618576,-0.05366413,-0.00911887,-0.09297374,-0.00952411,0.06435823,-0.03408074,0.03589236,-0.00238285,-0.01493702,0.03047181,0.01939505,-0.0337126,0.00085932,-0.03474421,0.02552886,0.03943642,-0.00666675,0.04893539,0.07566008,0.01413819],"last_embed":{"hash":"16vokyy","tokens":215}}},"text":null,"length":0,"last_read":{"hash":"16vokyy","at":1753495720888},"key":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{3}","lines":[27,33],"size":315,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10617314,-0.00337833,0.01272075,-0.03285901,0.01697579,0.02288249,-0.01201865,-0.0509041,0.06109974,0.00234045,-0.00346662,-0.09441268,0.07298099,-0.00926986,0.04373302,-0.02001215,0.0136256,0.00157222,-0.05304887,-0.03411653,0.07922533,0.00012378,-0.03086678,-0.11557405,0.01923629,0.03454331,0.01624275,-0.03986235,-0.00661364,-0.19223912,-0.03686021,0.04196412,0.00387431,-0.03041451,-0.06732579,-0.0037437,-0.01961075,0.04707616,-0.04767041,0.00583424,-0.02991831,0.05156523,0.03425011,-0.019274,0.00142726,-0.00142809,-0.04587357,-0.00315612,0.01704134,-0.04228251,-0.08718654,-0.00692231,-0.03816189,0.04143671,0.02156533,0.00927781,0.02961948,0.05999779,-0.0592659,0.01220481,0.04652375,0.00964667,-0.17467317,0.0154376,0.03062251,-0.01915494,0.0134434,-0.00579096,0.0570264,0.07518777,-0.05336849,0.01620994,-0.04749105,0.08834358,0.02819809,0.01986587,-0.00530864,-0.02646287,-0.05809503,0.01540279,-0.03119344,-0.00554326,-0.05621831,0.04192697,0.00687675,0.02864959,0.02004522,0.00869494,0.02321702,-0.037311,-0.01954157,-0.0657244,-0.01270969,0.03774609,-0.00450066,-0.02367344,0.0074771,-0.00859286,-0.0688612,0.10803665,-0.02559384,0.03866368,0.01690548,0.01243381,0.00973714,-0.03814778,-0.06252944,-0.02407145,-0.03509625,-0.04603474,0.0167819,0.01609339,0.01545264,-0.10263817,-0.04575928,0.00636494,0.03991247,0.01391082,-0.01878924,-0.02338999,-0.01076228,0.0641553,0.02123069,-0.00160121,0.03810795,-0.02402283,0.01951654,0.07129118,0.03239472,0.02927574,0.01715007,0.02594703,-0.05094174,-0.03491085,0.00178014,-0.05899028,-0.04646428,0.02292004,0.03730124,-0.01197103,0.00979074,-0.04108979,0.06405617,-0.11241358,0.00469007,0.11135737,-0.06678899,0.0484609,-0.01872118,-0.02312672,0.01081666,0.01098236,0.02913812,-0.04750337,0.0185295,0.01548002,0.07931173,0.08829094,-0.04670225,0.03474136,-0.04046299,0.00550957,-0.07027635,0.12930205,0.02259686,-0.06361587,-0.0185174,0.07338375,-0.00124701,-0.01439878,0.01202347,0.01603001,0.00171131,-0.03035918,0.11297978,-0.03965174,-0.01585235,-0.00756113,-0.03582838,0.03200315,-0.00666004,-0.01322673,-0.08149092,0.03530653,-0.02579099,-0.13147534,0.00983099,-0.02033645,-0.00727857,0.00469383,-0.07743874,-0.00347293,0.00936949,0.03911994,-0.03731667,-0.03841205,-0.01155963,-0.04222608,0.03664457,-0.01512892,0.11588231,0.01395096,-0.03178947,0.00071092,-0.02448743,0.00908559,0.03548354,-0.01932354,0.03359357,0.0710441,-0.00955455,0.01020481,0.05445817,0.01509927,-0.01535062,0.01407198,-0.02271597,0.03019012,0.00252973,0.01763247,0.00398833,-0.01602497,-0.09005198,-0.24494937,0.00615311,-0.02765826,-0.00817472,0.02541764,-0.01426523,-0.01785004,0.01156112,0.03673867,0.01374402,0.10318756,0.05533677,-0.0496288,0.04282323,-0.01607933,0.00018892,-0.01063292,0.01485267,0.00244983,0.04732177,0.00823645,0.04379703,0.02206984,-0.00142602,0.0496998,-0.05068365,0.13816419,0.00821246,0.03485839,0.01156302,0.08241332,0.02911767,0.02765536,-0.09454125,0.06215835,0.07219077,-0.08472715,-0.02635958,0.00520316,-0.04757546,0.00135098,0.01803896,-0.01677537,-0.08745965,0.03842448,-0.07389393,-0.03915738,-0.03679103,0.00429914,0.03822676,0.03309448,-0.04115738,0.03800236,0.04126772,-0.00244894,-0.02683836,-0.0670753,0.02148253,-0.04561501,-0.0106226,0.00394514,0.01337104,0.03860525,0.02574006,-0.02985514,0.02314323,-0.03122946,0.03240605,-0.01856187,0.02682125,-0.037359,0.12567322,0.01161115,-0.02232656,-0.00161615,0.00832783,-0.06183849,-0.07532667,-0.00174119,-0.02616306,0.01061954,0.00476533,0.01005072,0.01995587,0.03322031,-0.02199198,0.05008487,-0.00022769,0.05048138,-0.03092054,-0.0465707,0.01872187,-0.01112087,0.00739835,0.09348252,-0.00791146,-0.30366707,0.02234822,-0.00163781,-0.00836859,0.02281043,-0.02552035,0.01141421,-0.09240103,-0.04464199,0.04388422,0.03548511,0.04953627,-0.00017064,-0.02742857,0.02847587,0.04487326,0.05521342,-0.01523458,0.04946897,-0.05623079,0.01189444,0.02999204,0.27948394,0.06582425,0.03875125,0.02321286,-0.03137062,0.00921519,0.03817856,0.04859113,-0.01192141,0.01174927,0.07377849,0.00279236,0.01326337,0.08694341,0.00324651,0.06554197,-0.00801359,0.01638941,-0.04631695,0.02049893,-0.03130226,0.0420948,0.13005979,-0.03545906,-0.01626358,-0.09418937,0.01538776,0.02055579,-0.05092414,0.02176616,0.0060678,0.00697787,0.02694982,0.03012227,-0.02436452,-0.02242975,-0.03122159,0.00032149,0.06504126,0.0022701,0.03019222,0.03445398,0.04297869],"last_embed":{"hash":"n9enyx","tokens":201}}},"text":null,"length":0,"last_read":{"hash":"n9enyx","at":1753495720954},"key":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{4}","lines":[34,37],"size":289,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09050533,-0.01736058,0.00105399,0.01722347,0.03047578,0.01042761,0.03099589,-0.03246763,0.0788657,-0.04050343,-0.01226521,-0.1109579,0.07270878,-0.00563313,0.07573698,-0.02799868,0.01658435,-0.00092487,-0.04056938,-0.04679959,0.06266277,0.00049715,-0.02629201,-0.0606573,0.03443551,0.02831138,0.00155206,-0.05278609,-0.01305759,-0.17451192,-0.0191222,0.00737167,0.01359564,-0.05807199,0.00968693,0.03670178,-0.0168051,0.03490504,-0.05973319,0.03585729,0.02737173,0.04298961,0.02973672,-0.04362435,0.01962109,0.01270138,-0.05434071,0.00181998,0.0326953,-0.01442269,-0.01955337,-0.0061072,-0.03586709,0.05335618,0.00384728,0.04628263,0.05256994,0.0600582,-0.02422946,0.03981213,0.03027545,0.02796398,-0.16631533,0.00762931,0.01408752,-0.02099007,0.00188782,-0.06489398,0.06771842,0.06479086,-0.08205628,0.00656346,-0.03319344,0.06259248,-0.00145072,-0.01370984,0.01723834,-0.03758304,-0.06093822,0.0149186,-0.03735065,0.05152351,-0.01708248,0.00507493,0.0099464,0.02823477,-0.00236896,-0.01162794,-0.01469061,-0.02659115,0.00194304,-0.03750743,0.01630236,0.02773332,0.00348616,-0.00484729,0.01472367,0.06659441,-0.04734569,0.113714,-0.03860809,0.0241549,-0.03101456,-0.0846386,0.03977922,-0.02891318,0.00412126,-0.01707772,-0.02731237,-0.02611968,0.00896699,-0.00856414,0.00902254,-0.07059139,-0.01181461,0.03785655,0.07458548,-0.00126666,-0.06413177,-0.02070069,0.03288386,0.03462957,0.01732612,-0.03122313,-0.00292684,-0.03949298,0.00908065,0.05910087,0.02947655,0.00404256,0.03790759,-0.02099914,-0.06404089,-0.06804333,-0.02088854,-0.04173703,-0.04120138,0.06013308,0.04225411,-0.0247436,-0.02644991,-0.07963774,0.02091267,-0.08613563,-0.03865749,0.09649814,-0.06429221,-0.0200569,-0.01959854,-0.01442156,0.01430264,0.06037707,-0.01153039,-0.0713518,0.03121316,-0.04267994,0.07239249,0.12824933,0.01003652,0.00791455,-0.01267324,-0.04558054,-0.05958971,0.12319407,-0.02828078,-0.04105007,0.01251183,0.05633761,0.01050281,-0.02836483,0.0050227,-0.07834569,-0.03259701,-0.02288531,0.07905294,-0.01995624,0.04535054,0.00781034,-0.0244062,0.03034854,-0.0431402,-0.04791573,-0.10003447,0.03790656,-0.00732851,-0.08824718,0.04300311,-0.03758113,0.03235708,0.01978301,-0.1272829,0.01942353,-0.01987985,0.07299728,-0.06065187,-0.02333113,-0.03556481,-0.05463966,0.01207467,-0.0155304,0.0952745,0.03121499,-0.02718647,-0.04116483,-0.00235863,0.0225453,0.02304485,0.00954105,0.0050067,0.06835861,-0.02802071,0.07109857,-0.00793279,0.01074852,0.06849491,0.01510198,-0.00483699,0.03467055,-0.00513705,0.00771611,0.02075054,-0.02989683,-0.09074325,-0.2459237,0.01382188,-0.02042526,-0.02815279,0.03576351,-0.05280141,0.01261009,0.00552613,0.0310759,0.06303161,0.05614896,0.06050037,-0.04007301,0.03905631,0.01432716,0.00261022,0.03176126,0.03947161,-0.02183044,0.04637829,-0.01054596,0.022121,0.04301404,0.036529,0.03696186,-0.02030472,0.14225152,0.00067659,0.04867744,0.04903277,0.05736802,-0.00603082,-0.00261187,-0.10237092,0.01635161,0.0366681,-0.12673655,-0.04739917,-0.03386,-0.04272073,-0.00913007,0.01754308,-0.00553952,-0.1112162,0.06275186,-0.063288,-0.06131576,-0.02156448,-0.03420128,0.03349606,0.03894481,0.01289485,0.04565472,0.055611,0.0020618,-0.02946054,-0.05154437,-0.03778801,-0.08278671,0.01072369,-0.0166311,0.00527589,0.0268532,0.02438423,0.00730605,0.04969627,-0.02598207,0.00809715,0.01198711,0.04919006,-0.09021171,0.15158981,0.02018493,-0.06332583,-0.00645992,0.0374582,-0.07729968,-0.09424746,0.00110561,-0.03396379,0.04646493,-0.03946853,0.03241189,0.01050546,0.0329184,0.06008988,0.03306834,0.00241744,0.03199088,-0.02444161,-0.00793057,0.02533469,-0.01532983,-0.00415745,0.04375422,0.03048472,-0.30617523,-0.02300945,0.00314711,0.00431161,0.04674091,0.01161176,0.02128867,-0.01457307,-0.04262082,0.00155748,0.01239546,0.0572131,0.01070809,-0.01301907,0.01741975,0.02740194,0.01467855,-0.05698802,0.04107355,-0.00247947,-0.01041059,-0.04450497,0.22422053,0.04063326,0.01132019,0.02792551,0.01789824,0.01406817,0.03014401,0.01775521,-0.00750823,-0.0392247,0.06897517,0.04381258,0.00204877,0.09723631,-0.03373921,0.04281571,0.02700653,0.02788821,-0.03521255,0.00664555,-0.02562826,0.01137283,0.12757768,-0.0188079,-0.04481428,-0.05589546,-0.00493964,0.01763422,-0.01208371,0.02060007,-0.00461459,0.00619249,0.0256668,0.07010522,-0.00005475,-0.00509923,0.00423218,0.02290091,0.04697249,0.05910679,0.00071543,0.08961211,0.0422232],"last_embed":{"hash":"1lh8wf3","tokens":268}}},"text":null,"length":0,"last_read":{"hash":"1lh8wf3","at":1753495721015},"key":"notes/saliu/說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合.md###3. 生成不同類型的彩票組合#{5}","lines":[38,44],"size":293,"outlinks":[],"class_name":"SmartBlock"},
