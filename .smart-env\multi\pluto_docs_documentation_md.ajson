"smart_sources:pluto/docs/documentation.md": {"path":"pluto/docs/documentation.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05112974,-0.05155523,-0.00192856,-0.03399416,-0.00639177,0.07242335,0.0146435,0.03357243,0.01943531,0.03524654,0.03139965,-0.02958656,0.02391604,0.07005396,-0.0226188,-0.00231475,-0.02405347,0.02859498,-0.01319312,0.01524546,0.05890432,-0.05785012,-0.02110112,-0.05500242,0.07059669,0.07247693,-0.05823319,-0.06769141,-0.03440799,-0.24485482,0.10553302,-0.00947203,-0.00199124,-0.06969996,-0.06165578,-0.07084965,-0.00439408,0.08926129,-0.03965525,0.00897215,0.03066777,-0.01919064,-0.06057975,-0.00474312,-0.06169127,-0.05525354,-0.06349929,-0.01397419,0.02508697,0.0074576,-0.0959367,-0.02078422,0.07232935,0.02183753,0.00823165,0.03667805,0.08749392,0.02045101,0.01677811,0.05134195,0.04732529,0.02359945,-0.17775631,0.08312862,0.02273244,-0.00149241,-0.02510642,0.02089716,0.04336241,0.03734649,-0.01699978,0.00873586,-0.01786683,0.05549922,0.00471025,-0.08750592,-0.00374167,-0.0733203,-0.05218175,0.03161795,-0.06432231,0.01540678,-0.01966299,0.03361323,0.0306073,-0.00706025,0.0514161,0.03107303,0.09515141,0.00914409,-0.01228451,0.01712513,-0.00279701,0.07062846,-0.02716086,0.03712907,0.07395352,-0.0530715,0.05366532,0.13806336,-0.01088198,0.00485639,0.02177038,-0.01411583,0.00204582,-0.0628609,-0.05131845,-0.04618948,-0.03042873,0.01629926,0.04809851,-0.00195247,0.03833325,-0.08530645,-0.02715093,-0.00982349,-0.04981895,0.02037021,0.0283816,0.02031515,-0.02878695,-0.01493102,0.01067822,0.03019778,0.03347335,0.050252,0.05585667,0.09747436,0.03058589,0.0191426,0.12734179,0.05185955,-0.15394038,0.00428706,-0.01899386,-0.00893495,0.00558735,-0.00822241,-0.01846336,-0.00475714,-0.03183715,-0.01658847,0.06814282,-0.0823184,0.00932908,0.11223353,-0.02493714,0.02142246,0.04151898,-0.07293401,0.01654726,0.00991375,-0.03849628,-0.02867189,0.01724906,0.00912242,0.07113387,0.03358942,-0.03057342,0.03245564,-0.02170281,-0.07258658,0.02274853,0.11152189,-0.00755605,-0.10299028,0.00216615,0.06057994,-0.04109519,-0.07166108,0.03190532,0.03562379,-0.06006101,0.01001405,0.0991357,-0.00096067,-0.06551889,-0.01459759,0.01779371,0.02807185,0.0285284,-0.02724216,-0.08088434,0.05303585,-0.02495996,-0.07780107,-0.01203068,0.01722369,-0.03078694,0.03361609,-0.02340056,-0.05569952,-0.03953684,0.0347423,-0.01971924,-0.05330084,-0.02323564,0.01123184,0.05357774,-0.04741907,0.04514765,-0.03708103,-0.01415762,0.04498063,0.00968817,0.00122993,-0.01599835,-0.03471644,0.03011693,0.01536212,-0.02624831,0.00541515,0.0280655,-0.00065853,-0.05271838,0.01686569,0.03172892,-0.02982207,-0.00361819,0.03371609,0.01892711,0.00825963,-0.03716125,-0.2006005,-0.06008088,0.02683179,0.03766251,0.02117812,-0.00067456,0.03217774,-0.04314654,0.02717679,0.07864073,0.11072788,-0.01871463,-0.03522213,0.01370566,-0.03023626,-0.03991401,-0.06905261,-0.04402045,-0.07831451,0.01208955,-0.01171733,0.00675447,-0.03414707,-0.04154066,0.00599174,-0.01656178,0.15174133,-0.0115816,-0.02055704,-0.00835636,0.03112071,-0.00945017,-0.00191907,-0.01442168,0.00793515,0.01477241,-0.02548973,0.08819129,-0.05760964,-0.02010181,-0.0681354,0.04841526,0.05237516,-0.08482737,0.0040902,-0.01530137,0.00136648,0.00970151,-0.0012896,0.01359556,0.06410644,-0.03439222,0.01600809,0.0438964,0.04864611,-0.02119512,-0.02640272,0.01985726,-0.00170812,0.00429109,-0.02222328,-0.05782473,0.02585324,-0.02354246,0.03887749,-0.00967949,-0.00983383,-0.01933646,0.0060851,-0.0573781,-0.00247349,0.10305765,-0.02670403,0.03783296,0.03851766,0.03079981,0.01239705,-0.07450028,0.03836711,0.00207066,0.0386268,-0.01789341,-0.0211092,0.05666583,0.00313748,0.02598306,0.03876885,0.06396409,0.03602808,0.02182972,-0.0099328,0.02076268,-0.02127109,0.02274115,0.01654586,0.01170264,-0.26218611,0.0603228,-0.04879173,0.02921566,-0.02533334,-0.05457921,0.03639651,-0.00102429,0.01186587,-0.00551455,-0.03627794,0.01148395,0.05373446,-0.07660765,-0.02353029,0.01902422,0.04435202,-0.00044962,0.05278275,0.00555584,0.04595892,0.02024108,0.25618464,-0.03810168,0.01622217,-0.01649352,0.01899374,-0.0048291,0.0318003,-0.03196621,-0.03691247,-0.05025123,0.08099194,0.0034331,-0.04648196,0.02776759,0.01704482,0.03478871,-0.00419014,0.0074262,-0.01894792,0.02357327,-0.01216283,0.00351,0.10847967,0.00707379,-0.0080044,-0.0896754,0.00568821,0.03755591,-0.10891629,-0.03558147,-0.01602203,-0.04380612,-0.0255043,0.02722826,-0.01188792,0.01422607,0.0309006,0.01254949,0.02068134,-0.01777204,0.04201445,-0.02372895,-0.04555548],"last_embed":{"hash":"14c905ee0d9e10dd5fa054bd833187f5d022276bc6e6c33493c168bba88d4aee","tokens":448}}},"last_read":{"hash":"14c905ee0d9e10dd5fa054bd833187f5d022276bc6e6c33493c168bba88d4aee","at":1745995238884},"class_name":"SmartSource2","outlinks":[],"blocks":{"#Lottery Data Analysis and Visualization Tool":[1,119],"#Lottery Data Analysis and Visualization Tool#Overview":[3,5],"#Lottery Data Analysis and Visualization Tool#Overview#{1}":[4,5],"#Lottery Data Analysis and Visualization Tool#Key Components":[6,68],"#Lottery Data Analysis and Visualization Tool#Key Components#Data Structure":[8,15],"#Lottery Data Analysis and Visualization Tool#Key Components#Data Structure#{1}":[9,9],"#Lottery Data Analysis and Visualization Tool#Key Components#Data Structure#{2}":[10,10],"#Lottery Data Analysis and Visualization Tool#Key Components#Data Structure#{3}":[11,11],"#Lottery Data Analysis and Visualization Tool#Key Components#Data Structure#{4}":[12,12],"#Lottery Data Analysis and Visualization Tool#Key Components#Data Structure#{5}":[13,13],"#Lottery Data Analysis and Visualization Tool#Key Components#Data Structure#{6}":[14,15],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions":[16,59],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#1. Data Loading":[18,25],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#1. Data Loading#{1}":[19,21],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#1. Data Loading#{2}":[22,22],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#1. Data Loading#{3}":[23,23],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#1. Data Loading#{4}":[24,25],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#2. Statistical Analysis":[26,33],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#2. Statistical Analysis#{1}":[27,29],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#2. Statistical Analysis#{2}":[30,30],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#2. Statistical Analysis#{3}":[31,31],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#2. Statistical Analysis#{4}":[32,33],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#3. List Filtering":[34,41],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#3. List Filtering#{1}":[35,37],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#3. List Filtering#{2}":[38,38],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#3. List Filtering#{3}":[39,39],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#3. List Filtering#{4}":[40,41],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization":[42,59],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{1}":[43,46],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{2}":[47,47],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{3}":[48,48],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{4}":[49,49],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{5}":[50,54],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{6}":[55,55],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{7}":[56,56],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{8}":[57,57],"#Lottery Data Analysis and Visualization Tool#Key Components#Main Functions#4. Visualization#{9}":[58,59],"#Lottery Data Analysis and Visualization Tool#Key Components#Chart Saving":[60,68],"#Lottery Data Analysis and Visualization Tool#Key Components#Chart Saving#{1}":[61,61],"#Lottery Data Analysis and Visualization Tool#Key Components#Chart Saving#{2}":[62,62],"#Lottery Data Analysis and Visualization Tool#Key Components#Chart Saving#{3}":[63,68],"#Lottery Data Analysis and Visualization Tool#Helper Functions":[69,73],"#Lottery Data Analysis and Visualization Tool#Helper Functions#{1}":[70,70],"#Lottery Data Analysis and Visualization Tool#Helper Functions#{2}":[71,71],"#Lottery Data Analysis and Visualization Tool#Helper Functions#{3}":[72,73],"#Lottery Data Analysis and Visualization Tool#Dependencies":[74,80],"#Lottery Data Analysis and Visualization Tool#Dependencies#{1}":[75,75],"#Lottery Data Analysis and Visualization Tool#Dependencies#{2}":[76,76],"#Lottery Data Analysis and Visualization Tool#Dependencies#{3}":[77,77],"#Lottery Data Analysis and Visualization Tool#Dependencies#{4}":[78,78],"#Lottery Data Analysis and Visualization Tool#Dependencies#{5}":[79,80],"#Lottery Data Analysis and Visualization Tool#Usage Examples":[81,119],"#Lottery Data Analysis and Visualization Tool#Usage Examples#{1}":[82,119]},"last_import":{"mtime":1733377522526,"size":4103,"at":1740449883920,"hash":"14c905ee0d9e10dd5fa054bd833187f5d022276bc6e6c33493c168bba88d4aee"},"key":"pluto/docs/documentation.md"},