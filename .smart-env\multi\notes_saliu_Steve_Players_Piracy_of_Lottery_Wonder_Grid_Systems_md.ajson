
"smart_sources:notes/saliu/<PERSON> Players Piracy of Lottery Wonder Grid Systems.md": {"path":"notes/saliu/<PERSON> Players Piracy of Lottery Wonder Grid Systems.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"12jpp0w","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753266101265,"size":9885,"at":1753423416500,"hash":"12jpp0w"},"blocks":{"#---frontmatter---":[1,6],"#<PERSON> <PERSON> Piracy of Lottery Wonder Grid Systems":[8,116],"#<PERSON> Piracy of Lottery Wonder Grid Systems#{1}":[10,15],"#Steve Players Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)":[16,21],"#<PERSON> Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)#{1}":[17,21],"#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy":[22,41],"#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy#{1}":[24,41],"#<PERSON> Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_":[42,67],"#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_#{1}":[44,65],"#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_#{2}":[66,67],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate":[68,116],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{1}":[70,85],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{2}":[86,87],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{3}":[88,106],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{4}":[107,107],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{5}":[108,108],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{6}":[109,110],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{7}":[111,116]},"outlinks":[{"title":"Lottery player asks questions about wonder grid for pick 3 digit lotteries.","target":"https://forums.saliu.com/HLINE.gif","line":14},{"title":"Questions on Pick 3 _Wonder Grid_ Lottery Strategy","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3","line":16},{"title":"Answer from Ion Saliu, Creator of _Lottery Wonder Grid_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#WonderGrid","line":17},{"title":"Criminally-Incorrigible Steve Player, Lottery System Scammer, Pirate","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Scam","line":18},{"title":"Steve Player' a notorious scammer of lottery systems, also pirates Ion Saliu's lottery strategies.","target":"https://forums.saliu.com/HLINE.gif","line":20},{"title":"![View screen shot of the wonder grid for pick-3 with winning records.","target":"https://saliu.com/ScreenImgs/wonder-grid-pick-lottery.gif","line":60},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":66},{"title":"Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player","target":"https://saliu.com/bbs/messages/188.html","line":86},{"title":"![Run the best 3-digit lotto software named Bright pick-3 programs.","target":"https://saliu.com/ScreenImgs/pick31.gif","line":105},{"title":"Australian roulette system commits piracy of Ion Saliu's roulette system based on wheel half, sector, hemisphere","target":"https://saliu.com/roulette-millions.html","line":107},{"title":"Piracy, scams on eBay regarding lottery and gambling systems","target":"https://download.saliu.com/eBay-scams.html","line":108},{"title":"Piracy: Unauthorized Software Offering, Distribution","target":"https://saliu.com/bbs/software-piracy.html","line":109},{"title":"Run legitimate lottery software to create lotto systems based on pairs, not Steve Player piracy.","target":"https://forums.saliu.com/HLINE.gif","line":111},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":113},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":113},{"title":"Search","target":"https://saliu.com/Search.htm","line":113},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto last digits.","target":"https://forums.saliu.com/HLINE.gif","line":115}],"metadata":{"created":"2025-07-23T18:21:40 (UTC +08:00)","tags":["lotto","software","lottery","wonder grid","strategy","systems","Steve Player","piracy","fraud","lottery players"],"source":"https://forums.saliu.com/steve-player-lottery-piracy.html","author":null}},
"smart_sources:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md": {"path":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13738032,0.01184892,-0.04857991,-0.04000897,0.01893903,0.03782708,0.02276767,-0.00087966,0.01235288,0.05748327,0.03624399,-0.00712937,0.00388291,0.03116685,-0.01712191,-0.02904069,-0.00892718,-0.0416976,0.00357129,0.04814066,0.04255429,-0.0748814,-0.02560256,-0.07723656,0.03488621,-0.00289445,-0.04142074,-0.04340084,-0.0685901,-0.20489395,0.00237436,0.00607317,-0.01100503,-0.04682782,-0.05644407,-0.05451234,-0.03802532,0.05303406,-0.01293342,0.01498954,0.0187483,0.02976011,-0.02559997,-0.01185998,-0.02032747,-0.05675278,-0.01965473,-0.02357495,0.05062541,0.0215119,-0.10559982,0.03083593,-0.02774819,0.0387986,0.0275625,0.000252,0.06652206,0.05374311,-0.01018531,0.02426054,0.04850134,0.01726072,-0.19636992,0.04155778,0.04246722,0.02394173,-0.02000253,0.00717977,-0.00802458,-0.01050908,-0.0127694,0.04102236,-0.04890112,0.03692316,0.01116872,-0.00039265,-0.02512858,-0.02521689,-0.05436062,0.00187055,-0.04093385,-0.03790758,-0.03987392,-0.00664524,-0.00873215,0.05901013,0.03137495,0.0539626,0.04390831,-0.08266429,0.02808466,0.0338247,0.07061417,0.03754586,-0.04139893,0.04511958,-0.00549185,0.00293168,-0.06869285,0.12830646,0.02376278,-0.02242715,-0.00281578,0.01020789,0.0506987,0.0012224,-0.01527337,-0.02211198,-0.02003657,0.03241226,0.02183071,0.00533593,0.05318144,-0.01898318,-0.02914444,0.03161337,-0.01163324,0.02580552,0.00454669,0.0085702,-0.05460835,0.04561427,0.05310543,-0.01130532,0.00517458,0.00364752,0.03001063,0.0528894,0.03066703,0.03758966,-0.02658354,-0.03141528,-0.08694516,-0.05599571,-0.01994233,-0.01939921,-0.00706863,-0.01556011,0.01474362,0.01751213,-0.03792448,-0.01192231,0.04538412,-0.094712,-0.0183711,0.00825888,-0.00266995,-0.02464333,0.00907615,0.02600349,0.01514115,0.02102724,0.00102717,-0.08030309,-0.01488825,-0.01842153,0.15631321,0.097359,-0.07232388,0.06005481,0.02428976,-0.02047322,0.00063745,0.16854906,0.04402705,-0.10529856,0.00542971,0.0123456,0.00020494,-0.08840835,-0.02544764,0.02325841,-0.03091164,-0.02073297,0.04464094,-0.04950335,-0.05039344,-0.0146146,-0.01699568,0.00501631,-0.01031964,0.00123637,-0.00698132,-0.02918286,-0.03414978,-0.12457272,0.0023991,-0.00814785,-0.03439571,0.03161649,-0.03525951,0.00644583,-0.03762441,0.05113147,-0.03293511,-0.0453942,-0.00995928,-0.07128473,0.05361766,-0.01675749,-0.02762075,-0.00257296,0.0069673,-0.00953892,-0.05764284,0.06558727,0.01002701,-0.0741853,0.04258545,0.03459275,-0.03761249,0.01463653,0.00683364,0.03313192,-0.05506168,0.0479971,0.03139029,-0.02006818,0.02342746,-0.00929865,-0.01647917,0.0335843,-0.08421741,-0.1764379,-0.00401742,-0.07893576,-0.02422263,0.00600786,-0.04223353,0.08359764,-0.00612071,0.08334217,0.04801828,0.07153649,-0.08136005,0.07780905,0.05581326,-0.00303416,-0.02021974,-0.02462262,-0.03271224,-0.01037638,0.05543508,-0.02510711,0.06732493,-0.03967766,-0.043507,0.05105809,-0.03276244,0.17266801,0.12466094,-0.01197693,0.01361328,0.07746813,0.03402874,0.00766118,-0.14318115,-0.00217722,0.03705743,-0.0074519,-0.00254499,-0.02359185,-0.01898005,-0.11630606,0.01314105,-0.00554254,-0.05414684,-0.0450852,0.00735444,-0.02026864,0.042893,-0.02922725,0.05836493,0.06818252,-0.00071054,0.01677992,-0.00908914,0.0599601,-0.01947114,-0.06214924,0.04925308,0.02910832,0.05649094,0.00649705,0.0002938,0.04940465,-0.02441908,0.00550309,0.00906636,-0.02091947,0.03085427,0.0308124,-0.02998139,0.00647654,0.08840378,0.00138548,0.03431815,0.01553531,0.04436491,0.04554893,-0.11617295,0.01815839,-0.02138336,0.01192329,0.00490283,0.06547694,0.04519391,0.08358493,0.00710824,0.00648513,0.04559988,0.0093023,-0.00928544,-0.02460597,0.01774065,-0.03179502,-0.01730584,0.06514759,0.04702589,-0.22606276,-0.00065872,0.01157038,0.10050151,0.02152835,0.00002769,0.03157385,0.00249254,0.01586253,0.01913839,0.03042501,0.03214013,-0.01430912,-0.02687699,-0.02363207,-0.00397088,0.00753193,-0.02054256,0.09620685,-0.01222884,0.02753289,0.03134923,0.23579474,0.00488326,-0.01448732,-0.01271686,0.05659647,0.04096252,-0.07097044,0.00613578,-0.02068443,0.01772951,0.01639252,0.01185258,-0.01187628,0.04063432,-0.0585768,-0.02610021,-0.04191129,-0.0087907,-0.10067809,0.00260539,-0.03805155,0.0315015,0.10699956,0.01907056,-0.0222963,-0.11410689,0.0336838,0.06660236,-0.08833168,-0.07306875,-0.01636537,-0.05476097,0.00876188,0.08308908,0.03164886,-0.03204573,0.0092205,-0.02637072,0.02011783,0.01160597,0.07650305,0.00101099,0.02845731],"last_embed":{"hash":"12jpp0w","tokens":478}}},"last_read":{"hash":"12jpp0w","at":1753423626884},"class_name":"SmartSource","last_import":{"mtime":1753266101265,"size":9885,"at":1753423416500,"hash":"12jpp0w"},"blocks":{"#---frontmatter---":[1,6],"#Steve Players Piracy of Lottery Wonder Grid Systems":[8,116],"#Steve Players Piracy of Lottery Wonder Grid Systems#{1}":[10,15],"#Steve Players Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)":[16,21],"#Steve Players Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)#{1}":[17,21],"#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy":[22,41],"#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy#{1}":[24,41],"#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_":[42,67],"#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_#{1}":[44,65],"#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_#{2}":[66,67],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate":[68,116],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{1}":[70,85],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{2}":[86,87],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{3}":[88,106],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{4}":[107,107],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{5}":[108,108],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{6}":[109,110],"#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{7}":[111,116]},"outlinks":[{"title":"Lottery player asks questions about wonder grid for pick 3 digit lotteries.","target":"https://forums.saliu.com/HLINE.gif","line":14},{"title":"Questions on Pick 3 _Wonder Grid_ Lottery Strategy","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3","line":16},{"title":"Answer from Ion Saliu, Creator of _Lottery Wonder Grid_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#WonderGrid","line":17},{"title":"Criminally-Incorrigible Steve Player, Lottery System Scammer, Pirate","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Scam","line":18},{"title":"Steve Player' a notorious scammer of lottery systems, also pirates Ion Saliu's lottery strategies.","target":"https://forums.saliu.com/HLINE.gif","line":20},{"title":"![View screen shot of the wonder grid for pick-3 with winning records.","target":"https://saliu.com/ScreenImgs/wonder-grid-pick-lottery.gif","line":60},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":66},{"title":"Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player","target":"https://saliu.com/bbs/messages/188.html","line":86},{"title":"![Run the best 3-digit lotto software named Bright pick-3 programs.","target":"https://saliu.com/ScreenImgs/pick31.gif","line":105},{"title":"Australian roulette system commits piracy of Ion Saliu's roulette system based on wheel half, sector, hemisphere","target":"https://saliu.com/roulette-millions.html","line":107},{"title":"Piracy, scams on eBay regarding lottery and gambling systems","target":"https://download.saliu.com/eBay-scams.html","line":108},{"title":"Piracy: Unauthorized Software Offering, Distribution","target":"https://saliu.com/bbs/software-piracy.html","line":109},{"title":"Run legitimate lottery software to create lotto systems based on pairs, not Steve Player piracy.","target":"https://forums.saliu.com/HLINE.gif","line":111},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":113},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":113},{"title":"Search","target":"https://saliu.com/Search.htm","line":113},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto last digits.","target":"https://forums.saliu.com/HLINE.gif","line":115}],"metadata":{"created":"2025-07-23T18:21:40 (UTC +08:00)","tags":["lotto","software","lottery","wonder grid","strategy","systems","Steve Player","piracy","fraud","lottery players"],"source":"https://forums.saliu.com/steve-player-lottery-piracy.html","author":null}},"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14936297,-0.00054132,-0.0261881,-0.053544,0.01919151,0.03705334,-0.0121241,0.02186045,0.02361736,0.05828721,0.03084412,-0.00773912,0.01492331,0.04164594,-0.02776999,0.00191966,-0.01384776,-0.03786182,0.01142981,0.04871502,0.04766248,-0.04399021,-0.02699327,-0.09363725,0.00563861,-0.00557774,-0.05089211,-0.0486472,-0.05049941,-0.15374252,-0.00399069,-0.00822857,-0.00991192,-0.01491385,-0.03388821,-0.07549296,-0.02159994,0.05498656,-0.012561,0.03162863,0.02646572,0.03932158,-0.06890242,-0.00752124,-0.00218836,-0.04877305,-0.00718682,-0.01607308,0.03890835,0.03291769,-0.07057484,0.04424438,-0.04986216,0.02856376,0.03706668,-0.01995352,0.06263436,0.03829634,0.01983509,0.02944164,0.04252415,-0.00917115,-0.21026002,0.03965113,0.06054778,0.01029996,-0.00805393,-0.00333751,-0.017797,-0.0113098,-0.00758806,0.03283294,-0.05345481,0.03094435,0.00491694,0.00729762,-0.02701881,-0.0505252,-0.05632431,-0.01064869,-0.03116834,-0.04244459,-0.03824968,-0.02287374,-0.02223937,0.03427802,0.03040229,0.07981259,0.05561087,-0.05536398,-0.01073892,0.03413273,0.09293687,0.04002979,-0.0589282,0.05568203,-0.00509725,0.00406751,-0.03562316,0.1460882,-0.01797076,-0.0200088,0.00226501,0.02087711,0.0465375,-0.00406406,-0.005854,-0.00273923,-0.02295626,0.00421422,0.02663318,0.01386794,0.07376108,-0.02499515,0.01187179,0.00091597,0.00167809,0.05334645,0.01415432,0.0139428,-0.07523066,0.04753377,0.0491232,-0.00759939,0.01720772,0.04430351,0.0214515,0.06638233,0.01423239,0.01184019,-0.02213409,-0.02750851,-0.03719081,-0.04696978,0.00627605,-0.0051591,0.00629966,-0.0332832,0.01266546,0.00481478,-0.02001196,-0.01058157,0.02527576,-0.10185128,-0.02361824,0.01662522,0.00579573,-0.03652487,0.0169641,0.03540196,0.00068635,-0.00656405,-0.00050527,-0.06467183,-0.01767046,0.00081522,0.16405174,0.09904164,-0.04437057,0.04309018,0.04138343,-0.01945649,-0.01271618,0.1843918,0.0214625,-0.12433518,0.01571468,0.01668356,-0.0062444,-0.09347907,-0.03722216,0.01524549,-0.01976966,0.00175198,0.0933558,-0.05543157,-0.01451023,-0.02917364,-0.06826802,-0.01142886,0.00178326,0.00642008,-0.03841095,-0.0310108,-0.03425371,-0.10432157,-0.01851451,-0.0158078,-0.02789127,0.04391574,-0.03846776,-0.03985474,-0.02901733,0.04292428,0.00677724,-0.03598174,-0.02507405,-0.06086404,0.061068,-0.03074007,-0.02535763,-0.02248421,-0.00955986,-0.00761711,-0.05694251,0.04098843,-0.0116878,-0.06707341,0.02226888,0.01468975,-0.03474933,0.01841424,0.01928579,0.04226379,-0.08013992,0.0494443,0.0154404,-0.02637102,0.02875777,-0.00082064,-0.02605019,0.02524948,-0.1030475,-0.19561942,0.0258755,-0.06475257,-0.0444462,-0.01753951,-0.04252528,0.0712728,0.00019707,0.07527474,0.08823422,0.07495296,-0.07799508,0.06104131,0.0073866,-0.0098732,-0.0275995,-0.01724516,-0.03725706,-0.0001977,0.04675933,-0.01098493,0.05537304,-0.03576296,-0.06189423,0.03898596,-0.02881517,0.16322896,0.14642429,-0.00479947,0.0040106,0.07943963,0.0452261,0.00445018,-0.13521127,-0.02417992,0.02423359,-0.01384695,0.01707914,-0.03713585,-0.02947419,-0.12381522,0.00781307,-0.00687314,-0.06385659,-0.00144773,0.01733369,-0.00558765,0.01300783,-0.03027699,0.03861453,0.05234762,-0.01961228,0.01866924,0.01517163,0.05867949,-0.0022417,-0.06257459,0.01978623,0.04694616,0.0509331,0.01579825,-0.00729634,0.03949597,-0.03975194,-0.01035289,0.00466678,-0.018383,-0.00314711,0.01249526,-0.03504271,-0.0024545,0.06966439,0.0049205,0.00573819,-0.0072405,0.01567745,0.04434644,-0.0690971,0.02550225,-0.02487513,0.01098184,0.01595155,0.08571868,0.06915285,0.08519505,0.0097275,-0.00904217,0.04002922,0.02659406,-0.00913548,-0.0101955,0.0169209,-0.03078963,-0.00307721,0.07813933,0.02644599,-0.2280488,0.04219877,0.05476083,0.09582947,0.00258969,0.03496797,0.00674198,0.02069446,-0.01910533,0.01997245,0.03738237,0.03698641,-0.03757647,-0.01309858,-0.06815486,-0.00116933,0.03466851,-0.03824563,0.08299217,-0.01074609,0.00986415,0.04160884,0.24655081,0.0157652,-0.00158142,-0.01474197,0.03868631,0.04202752,-0.05749208,0.0178767,-0.02964823,0.02130375,0.03239648,0.01560483,-0.0098815,0.06095811,-0.04308449,-0.00648485,-0.03281503,0.00995172,-0.11977768,0.02023493,-0.03576597,0.04622721,0.08838836,0.04634645,-0.0221327,-0.11203383,-0.00787305,0.03206136,-0.07469088,-0.07354727,-0.02088147,-0.01904758,0.00115828,0.06188072,0.03775232,-0.0467486,0.0145495,-0.01462699,0.03170958,0.00713109,0.05921793,-0.00487217,0.01486945],"last_embed":{"hash":"1bisy6m","tokens":94}}},"text":null,"length":0,"last_read":{"hash":"1bisy6m","at":1753423625280},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#---frontmatter---","lines":[1,6],"size":227,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11725898,0.01846418,-0.05455803,-0.04070218,-0.00390314,0.05522139,0.02685206,-0.00071216,0.01936381,0.07103139,0.03076166,-0.00384324,0.0058547,0.03682302,-0.01677367,-0.02056552,-0.01102121,-0.02572966,-0.01849969,0.04475039,0.04006481,-0.08170167,-0.03110283,-0.08810955,0.03151231,-0.00979265,-0.03813445,-0.04898138,-0.06762335,-0.22409202,0.01336143,0.00638587,-0.00955668,-0.04534803,-0.05831313,-0.04923089,-0.04302599,0.05603254,-0.00872526,0.01520304,0.0208961,0.02161998,-0.01104386,-0.01352259,-0.0275747,-0.05041223,-0.01759926,-0.00959902,0.05950389,0.0271542,-0.0968264,0.02186333,-0.0211216,0.04097256,0.02287475,0.00256248,0.0649313,0.06304646,-0.01921483,0.01594593,0.04406691,0.02439924,-0.194208,0.02743957,0.03552626,0.02844392,-0.02353227,0.02534648,0.00073478,-0.00813598,-0.01843353,0.03113315,-0.05505007,0.03759317,0.01478638,-0.00705028,-0.04284858,-0.01496154,-0.04622475,0.01587337,-0.06535684,-0.02467304,-0.03692436,0.00381534,-0.00365944,0.05587191,0.03579099,0.05470456,0.04180798,-0.07969941,0.02561749,0.03393375,0.03908388,0.03064755,-0.04307604,0.04870014,0.00028949,-0.00978694,-0.06123728,0.14199579,0.02978278,-0.02288966,-0.00059255,0.00836872,0.03450797,-0.01402016,-0.01905088,-0.01796489,-0.01874245,0.02578685,0.01931438,-0.00378612,0.031335,-0.0290055,-0.03957047,0.02890452,-0.01907292,0.03545065,-0.0136727,0.00614644,-0.05650888,0.04783912,0.04837286,-0.01390979,0.00103801,0.00474995,0.03687093,0.06064899,0.03693018,0.02893514,-0.02744012,-0.02992886,-0.0817982,-0.06231651,-0.02280111,-0.02452455,-0.00551544,0.00199324,0.01749058,0.02121506,-0.01545692,0.00790134,0.0529114,-0.08960822,0.00545614,0.01421085,-0.00215628,-0.01023784,0.00148515,0.02180042,0.0187826,0.00714211,-0.0062627,-0.0840113,-0.01774754,-0.01407741,0.14142565,0.09942125,-0.06008552,0.06857866,0.01833148,-0.01696228,0.00466373,0.17085908,0.03741334,-0.0794999,0.00595189,0.01182441,-0.00054286,-0.0885819,-0.03251573,0.03295318,-0.031798,-0.02533077,0.04161651,-0.06001663,-0.08444811,-0.00458929,-0.00927127,0.01048411,0.0004998,0.01468471,-0.01773924,-0.02228776,-0.03123317,-0.12853763,0.00624734,-0.00830981,-0.03665356,0.04143706,-0.02802477,0.01378355,-0.03520048,0.06911322,-0.02622122,-0.05672046,-0.00946456,-0.07453451,0.0486547,-0.01667837,-0.02214667,-0.00498451,0.00396116,-0.00918793,-0.04653366,0.06827159,0.01752899,-0.06190997,0.06032128,0.04266208,-0.04900034,0.00774814,0.00328088,0.0262782,-0.0563524,0.06066273,0.02976309,-0.01450791,0.02663106,-0.01235037,-0.00370471,0.01809481,-0.07914802,-0.18301633,-0.01583555,-0.06964836,-0.01970986,0.0098863,-0.05182444,0.07483415,-0.01043343,0.07029415,0.03761736,0.07323316,-0.08228447,0.07759642,0.0651806,-0.00985926,-0.02427642,-0.04277182,-0.02953393,-0.00005121,0.0615603,-0.01917789,0.07001837,-0.02814164,-0.04486603,0.0534665,-0.02336741,0.17029756,0.1105292,-0.01116827,0.00842608,0.07324445,0.02580346,0.00572818,-0.12767406,0.00235568,0.03677766,-0.01559343,-0.00934756,-0.02651967,-0.01749514,-0.10057534,0.00879578,-0.00907355,-0.0496094,-0.04487919,-0.0083879,-0.03153307,0.03797333,-0.01799615,0.057522,0.07266917,-0.01200335,0.02356701,-0.0198455,0.0486973,-0.02047015,-0.07040149,0.06031161,0.02741576,0.04635398,0.00704714,-0.00510281,0.06189713,-0.0174318,-0.00222852,0.00761706,-0.02118754,0.02730786,0.03683469,-0.02529012,0.0137078,0.09224825,0.00708163,0.04490251,0.03549976,0.03927353,0.03034416,-0.12121239,0.00659026,-0.01848041,0.00800508,-0.01243868,0.04525208,0.04999213,0.07420672,-0.00179343,0.01131128,0.0547954,0.00109704,-0.01292202,-0.02630408,0.02699769,-0.03003167,-0.00572575,0.05250996,0.03921274,-0.22064668,0.01036641,-0.01162158,0.10322203,0.02511198,-0.01651404,0.0406718,0.00927305,0.02588689,0.01828085,0.03637965,0.02779949,0.00026002,-0.04700058,-0.02064374,0.00282714,0.00845699,-0.02973557,0.09370281,-0.02704302,0.04374975,0.02714153,0.24315225,0.00361492,-0.01032329,-0.01530303,0.05459958,0.03190792,-0.07046516,0.00184837,-0.01968821,0.01949051,0.01617473,0.01058563,-0.00385557,0.05014176,-0.063435,-0.02673672,-0.04446032,-0.01141888,-0.10025772,0.00381692,-0.04801347,0.03519265,0.10239044,0.01238523,-0.01723067,-0.11354335,0.03538414,0.07683379,-0.09474356,-0.05785718,-0.01095924,-0.05751644,0.00231604,0.08026245,0.02214166,-0.03117871,0.02078879,-0.02434946,0.02514885,0.01373827,0.08741673,-0.01409349,0.02693387],"last_embed":{"hash":"oupmwz","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"oupmwz","at":1753423625362},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems","lines":[8,116],"size":9621,"outlinks":[{"title":"Lottery player asks questions about wonder grid for pick 3 digit lotteries.","target":"https://forums.saliu.com/HLINE.gif","line":7},{"title":"Questions on Pick 3 _Wonder Grid_ Lottery Strategy","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3","line":9},{"title":"Answer from Ion Saliu, Creator of _Lottery Wonder Grid_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#WonderGrid","line":10},{"title":"Criminally-Incorrigible Steve Player, Lottery System Scammer, Pirate","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Scam","line":11},{"title":"Steve Player' a notorious scammer of lottery systems, also pirates Ion Saliu's lottery strategies.","target":"https://forums.saliu.com/HLINE.gif","line":13},{"title":"![View screen shot of the wonder grid for pick-3 with winning records.","target":"https://saliu.com/ScreenImgs/wonder-grid-pick-lottery.gif","line":53},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":59},{"title":"Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player","target":"https://saliu.com/bbs/messages/188.html","line":79},{"title":"![Run the best 3-digit lotto software named Bright pick-3 programs.","target":"https://saliu.com/ScreenImgs/pick31.gif","line":98},{"title":"Australian roulette system commits piracy of Ion Saliu's roulette system based on wheel half, sector, hemisphere","target":"https://saliu.com/roulette-millions.html","line":100},{"title":"Piracy, scams on eBay regarding lottery and gambling systems","target":"https://download.saliu.com/eBay-scams.html","line":101},{"title":"Piracy: Unauthorized Software Offering, Distribution","target":"https://saliu.com/bbs/software-piracy.html","line":102},{"title":"Run legitimate lottery software to create lotto systems based on pairs, not Steve Player piracy.","target":"https://forums.saliu.com/HLINE.gif","line":104},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":106},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":106},{"title":"Search","target":"https://saliu.com/Search.htm","line":106},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto last digits.","target":"https://forums.saliu.com/HLINE.gif","line":108}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12521638,0.02966717,-0.03441146,-0.02797949,0.00628801,0.0548295,0.03117367,0.00723047,0.01686969,0.06793453,0.03773338,-0.00477251,0.01070603,0.02385526,-0.02780555,-0.02570902,-0.01357219,-0.02386823,-0.00283805,0.05478678,0.04833228,-0.09072827,-0.02589781,-0.10052133,0.00657555,-0.00519149,-0.05146223,-0.04502906,-0.06508227,-0.18363543,0.0011343,-0.00319885,0.01157012,-0.04337201,-0.04582738,-0.07210037,-0.02156633,0.0359372,-0.02715469,0.01158397,0.01959279,0.01074556,-0.01907195,-0.00846206,-0.01082029,-0.04434215,-0.00407053,-0.00892352,0.05081598,0.02176367,-0.09183342,0.04313224,-0.02858009,0.02717382,0.03520751,0.0033469,0.05771292,0.05194585,-0.01353853,0.00819656,0.04605189,-0.01313026,-0.20915022,0.00295542,0.0540815,0.02788019,-0.02049946,-0.00667253,-0.0142081,-0.03083661,-0.01550794,0.03028355,-0.04851209,0.02687504,0.00437968,-0.00416926,-0.03162263,-0.02716237,-0.04050514,0.02135253,-0.03448093,-0.02896196,-0.03475812,-0.00301848,-0.025706,0.04334443,0.03294927,0.04532788,0.05007315,-0.09369098,-0.00072297,0.0194278,0.05890781,0.04774801,-0.05109271,0.04112156,-0.01191358,-0.02476821,-0.02564063,0.16106609,0.01368313,-0.03303488,0.01198315,0.00235596,0.03177001,-0.00235862,-0.0095731,-0.01812994,-0.00523813,0.01915007,0.03425314,0.00442536,0.04607268,-0.01241363,-0.02230873,0.01567159,-0.01674606,0.02699448,-0.00106687,0.01415969,-0.08068032,0.06310161,0.06153329,0.00203182,-0.00629747,0.02618581,0.03611502,0.06587972,0.02495381,0.0238824,-0.03246216,-0.03288972,-0.06750865,-0.05484829,-0.00885002,-0.0209994,0.00381765,-0.02405722,0.0213358,0.0253974,-0.03397238,0.01116772,0.02340119,-0.09421704,-0.02311545,0.0039579,0.01048178,-0.03170879,0.0117506,0.04266485,0.02156242,-0.00352631,-0.00708935,-0.07948321,-0.01253646,0.0015917,0.13538913,0.10494826,-0.04705307,0.06690288,0.03525363,-0.01439247,-0.00959786,0.19925092,0.02162921,-0.08270981,0.00470332,0.00826647,0.01095817,-0.09405041,-0.02979913,0.03189627,-0.03019646,-0.00075006,0.03998646,-0.05383645,-0.05842029,-0.00748906,-0.05689143,0.00183655,0.00136022,-0.0022814,-0.02647484,-0.02488061,-0.02771005,-0.123071,-0.00673007,-0.00998229,-0.0316465,0.06020516,-0.01771885,0.05328465,-0.03084277,0.04027559,-0.0185032,-0.04993137,-0.01706645,-0.05780548,0.02168574,-0.02305555,-0.04149605,-0.00299952,-0.01435065,0.01621978,-0.05338091,0.04243041,-0.00042117,-0.03766322,0.04894033,0.02743428,-0.03867805,0.0075567,-0.01136303,0.02454173,-0.05607677,0.06866203,0.03702956,-0.01717032,0.01538705,-0.00522792,0.00215974,0.02610423,-0.10672022,-0.18960863,-0.01484712,-0.06573229,-0.02692415,0.00311085,-0.05834587,0.08312268,-0.01498272,0.08011075,0.0620924,0.07989882,-0.08155345,0.07794929,0.05445175,-0.00880131,-0.01252491,-0.02127134,-0.03316358,-0.00096811,0.05577815,-0.01898137,0.07329606,-0.02767945,-0.03806982,0.04816538,-0.03953939,0.16606659,0.11454968,-0.01060271,0.01081848,0.08940861,0.04907992,0.01768651,-0.10554644,-0.00307658,0.03414319,-0.02574624,-0.00273941,-0.05374877,-0.02809492,-0.11430979,-0.00085345,0.00238249,-0.03122539,-0.02429063,0.03223667,-0.0158633,0.01266357,-0.01478556,0.03429926,0.06983652,-0.01511403,0.02175166,-0.00620284,0.04956269,-0.01092834,-0.06603754,0.05360573,0.03948231,0.04841228,0.00367206,-0.00910528,0.03357083,-0.02625225,0.00588237,0.00508869,-0.03808466,0.00850216,0.04572355,-0.03673105,-0.00275452,0.08826277,-0.00175957,0.02926575,0.01133271,0.03468614,0.03403221,-0.12795009,0.00844257,-0.02141055,0.01478604,0.00160189,0.05200693,0.05642815,0.06801155,0.00403653,0.01052247,0.0517476,0.00683881,-0.01603359,-0.00567799,0.02798941,-0.03332189,-0.00586084,0.05793261,0.04763749,-0.21956363,0.00117496,0.00168695,0.09953171,0.00931983,0.00802726,0.0240805,0.03735269,0.04675139,0.01879489,0.05180759,0.02035451,-0.00586295,-0.0190576,-0.0277425,0.02279297,0.02076956,-0.04315924,0.09161995,-0.02545375,0.04946266,0.03842126,0.2530928,-0.00450834,-0.01563559,-0.00466677,0.04408443,0.03092438,-0.09140036,0.00820393,-0.0085212,0.01642275,0.01927922,0.01804339,-0.01465644,0.06751001,-0.05452687,-0.00874176,-0.04784578,-0.00068809,-0.09946799,-0.01601495,-0.04681922,0.0327975,0.09767922,0.03419726,-0.02061239,-0.12780677,0.02618988,0.06111009,-0.0807254,-0.05533842,-0.01986559,-0.05176353,0.0016409,0.06682526,0.02287641,-0.03814786,0.02289426,-0.01994606,0.00893653,0.01329306,0.06983066,-0.03386669,0.01775714],"last_embed":{"hash":"vh09cx","tokens":97}}},"text":null,"length":0,"last_read":{"hash":"vh09cx","at":1753423625508},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems#{1}","lines":[10,15],"size":284,"outlinks":[{"title":"Lottery player asks questions about wonder grid for pick 3 digit lotteries.","target":"https://forums.saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13628802,0.0240596,-0.06405357,-0.03800781,0.02607638,0.04364689,0.03907236,-0.00095954,-0.00238513,0.05675637,0.02244924,-0.01266349,0.00138788,0.01141641,-0.00127289,-0.0090889,0.00553645,-0.00948263,-0.01700604,0.05809034,0.03415892,-0.07716509,-0.01581366,-0.07135297,0.02663347,-0.02970364,-0.0446845,-0.05668652,-0.06782245,-0.19216183,0.01181833,-0.01009265,-0.03018833,-0.05119919,-0.04942886,-0.05864031,-0.02848312,0.05210042,-0.00215023,0.02590715,0.03081198,0.03622735,-0.03748368,-0.01222261,-0.02215547,-0.05167941,0.01019424,-0.01938555,0.06089161,0.01376683,-0.09289349,0.04964462,-0.02999255,0.00322655,0.02825518,0.00010106,0.08941927,0.06305049,-0.03344167,0.01802265,0.0310373,0.01684661,-0.2085617,0.05489975,0.06922685,0.02064188,-0.01923163,0.01469726,-0.03339034,0.01323461,-0.01841821,0.03553174,-0.06981472,0.01629461,0.00380822,-0.01420784,-0.03593997,-0.01479037,-0.03598456,0.01670764,-0.04948653,-0.02843829,-0.03640953,-0.01218929,-0.00543196,0.03862364,0.00939947,0.05332642,0.04877015,-0.09326987,0.00888212,0.02511085,0.06457775,0.02803369,-0.04257307,0.0368109,-0.00108436,0.00920279,-0.04727921,0.1417827,0.02470268,-0.03730877,-0.0065797,0.02405641,0.05827698,-0.01355814,-0.02319435,-0.01485136,-0.01672893,0.02154889,0.02813874,0.00738973,0.05590144,-0.00952267,-0.02662743,0.0454504,-0.00921491,0.02130098,-0.00066282,-0.0058913,-0.05680989,0.05173484,0.08546371,0.00242658,0.01997633,-0.00678521,0.03105788,0.04462263,0.02235501,0.02778459,-0.03088299,-0.06321622,-0.05850855,-0.05903326,-0.03116506,-0.01300475,0.00348739,-0.01794753,0.01197395,0.018032,-0.02125533,-0.04675784,0.04231571,-0.09053968,-0.00270345,0.01874909,-0.00663157,-0.02824457,0.01433684,0.03010798,0.0176315,0.01068125,0.01808131,-0.07059843,-0.02275721,-0.01625668,0.16049862,0.08057692,-0.07832666,0.06575752,0.03034597,-0.03711591,0.01502517,0.19567563,0.04332843,-0.0548512,-0.01178475,-0.01282056,-0.0073618,-0.08753229,-0.03175531,0.05124869,-0.03901957,-0.00181131,0.05687688,-0.05032745,-0.04933,-0.01401293,-0.02739869,0.01875195,-0.03827395,0.00757773,-0.00647942,-0.01715416,-0.03252728,-0.11301281,0.00182369,-0.00954506,-0.03696404,0.04018958,-0.04844692,-0.01029781,-0.03953508,0.0401313,-0.02892071,-0.00691091,-0.01378119,-0.06989682,0.05143564,-0.00370877,-0.02044067,0.00910768,0.00330801,-0.01018331,-0.07580098,0.07502779,0.00725813,-0.07658431,0.03896143,0.02259082,-0.0460883,0.02395071,-0.03091352,0.02413505,-0.0510605,0.03090093,0.05187448,-0.01905234,0.02766602,-0.00578202,-0.00491267,0.03111557,-0.0858102,-0.18376818,-0.01526,-0.08877864,-0.04912094,0.01428047,-0.05023525,0.0972145,-0.01094226,0.07539272,0.05130905,0.06833731,-0.07827171,0.07994595,0.04512312,0.00874129,-0.00652796,-0.02218281,-0.03860151,-0.00020366,0.05567261,-0.03902951,0.07720575,-0.03408485,-0.03095709,0.05143095,-0.04515221,0.17831899,0.1246386,0.00464636,0.00797549,0.06990463,0.04009152,-0.01400431,-0.12985492,0.01705745,0.02062585,-0.01705564,-0.01114475,-0.02022428,-0.03198237,-0.09993955,0.01202852,-0.00462523,-0.04534386,-0.02977512,-0.0026767,0.00072922,0.04540718,-0.02112997,0.04961516,0.08897448,0.01290543,0.00078441,0.0038392,0.06442824,0.00278231,-0.07593978,0.03474748,0.01531992,0.05523414,-0.00964319,0.0050754,0.04078329,-0.02468411,-0.00201552,0.00672242,-0.01768477,0.01081647,0.0300513,-0.03306378,0.0027523,0.06000395,0.00726514,0.03876422,0.00725792,0.03751421,0.05103327,-0.10120658,0.02997663,-0.01888537,0.01340238,0.01360442,0.05717973,0.03320025,0.07526722,0.01057125,-0.00728185,0.02972119,0.00268472,-0.00297748,-0.01068457,0.02468014,-0.03624237,-0.00775123,0.05851913,0.03401928,-0.21974939,-0.00195911,0.01870531,0.09562219,0.02922776,0.01032136,0.01524804,-0.02841189,-0.00176127,0.02259498,0.0449727,0.047593,-0.02197462,-0.03295346,-0.05014084,0.00172223,0.01514655,-0.01631307,0.10758276,-0.01362019,0.02312107,0.03962267,0.22665285,0.02367881,0.01225593,-0.01411737,0.0394075,0.03968661,-0.09525447,-0.00892329,-0.00516497,0.01275529,0.0173092,0.0046805,0.01243073,0.04270684,-0.04778325,-0.02407311,-0.0349529,-0.01796995,-0.0875928,0.00290081,-0.06252768,0.04270127,0.10302532,0.01968124,-0.01130452,-0.09657245,0.0213101,0.05774609,-0.08390972,-0.06365641,-0.00415583,-0.0428701,0.0144524,0.07428965,0.02044621,-0.05844167,0.01577158,-0.01480973,0.02425857,0.0084919,0.06249744,0.01132888,0.03228735],"last_embed":{"hash":"13zx82d","tokens":235}}},"text":null,"length":0,"last_read":{"hash":"13zx82d","at":1753423625542},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)","lines":[16,21],"size":540,"outlinks":[{"title":"Questions on Pick 3 _Wonder Grid_ Lottery Strategy","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3","line":1},{"title":"Answer from Ion Saliu, Creator of _Lottery Wonder Grid_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#WonderGrid","line":2},{"title":"Criminally-Incorrigible Steve Player, Lottery System Scammer, Pirate","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Scam","line":3},{"title":"Steve Player' a notorious scammer of lottery systems, also pirates Ion Saliu's lottery strategies.","target":"https://forums.saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13108097,0.02205671,-0.06404693,-0.03765494,0.02364618,0.03555946,0.04252907,-0.00109003,0.00037818,0.06121848,0.02177852,-0.01298357,-0.00021858,0.01443708,0.00021883,-0.01683615,0.00822628,-0.0041984,-0.0140744,0.05456851,0.03972329,-0.0749105,-0.01610535,-0.07970916,0.02880061,-0.02431677,-0.04494004,-0.05181228,-0.06365453,-0.1901864,0.00857621,-0.01018925,-0.03318179,-0.04897119,-0.04578119,-0.06281723,-0.02883666,0.05093341,-0.00489193,0.02468909,0.02873417,0.03645087,-0.03614734,-0.01345595,-0.02572075,-0.04930071,0.00622847,-0.01383931,0.05825746,0.01761665,-0.08651868,0.05122582,-0.0291436,0.00730422,0.02794577,-0.002295,0.08266148,0.06485998,-0.0349217,0.0153476,0.03321406,0.01714262,-0.20456435,0.04890389,0.06424827,0.02211419,-0.01779723,0.01177379,-0.03120808,0.00991239,-0.01297187,0.03617568,-0.07732667,0.01623704,0.00536031,-0.01184962,-0.03655217,-0.01490291,-0.03354385,0.02323703,-0.0454689,-0.03672272,-0.04031706,-0.01140692,-0.0058763,0.03776016,0.01393337,0.05267006,0.05435205,-0.09412282,0.00294219,0.01663711,0.06823201,0.0251823,-0.04835989,0.03871038,-0.00396905,0.01359123,-0.04521157,0.13997027,0.02582603,-0.03767505,-0.0075793,0.02118994,0.06357462,-0.01539102,-0.03089968,-0.01028024,-0.01746664,0.01902968,0.02471795,0.00283235,0.05928713,-0.01070744,-0.02422254,0.04021334,-0.0112764,0.02043639,0.00421005,-0.00583413,-0.05807148,0.05263965,0.084876,0.00306513,0.01707418,-0.00822251,0.02612213,0.04566582,0.0164735,0.02736424,-0.02884388,-0.0597649,-0.05581292,-0.06066435,-0.02897621,-0.01571135,-0.00290121,-0.01741027,0.00821539,0.01523811,-0.01989412,-0.04208971,0.03605565,-0.08716789,-0.00438129,0.01299443,-0.00154351,-0.03077934,0.00975554,0.03209014,0.01639919,0.00547379,0.02064077,-0.06913113,-0.01956675,-0.0197814,0.1614536,0.08254492,-0.07904432,0.06551599,0.03149047,-0.03836205,0.01091711,0.20034002,0.04199535,-0.05498102,-0.0115629,-0.01237638,-0.00791952,-0.0895362,-0.03335197,0.05188164,-0.03934212,0.0018075,0.05346642,-0.05360508,-0.05245153,-0.01602857,-0.03398758,0.02244261,-0.03660154,0.00338732,-0.00724848,-0.01597949,-0.0340743,-0.11417204,-0.00160601,-0.00356031,-0.03850197,0.04007336,-0.04400412,-0.00135536,-0.04157481,0.03634597,-0.029343,-0.00611359,-0.01513557,-0.06648993,0.05052197,-0.00600963,-0.02561629,0.01174143,-0.00262186,-0.00957444,-0.06814604,0.07203685,0.00762292,-0.07341248,0.03949644,0.02069894,-0.04463524,0.02104768,-0.02917062,0.02317561,-0.05042001,0.03385235,0.05194573,-0.02056029,0.02834846,-0.0075204,-0.00770013,0.02928481,-0.08810765,-0.18624732,-0.01255882,-0.08599914,-0.05196431,0.01472977,-0.05147016,0.09465589,-0.01060635,0.07589856,0.05041124,0.0618481,-0.07572871,0.08079068,0.04855511,0.00897555,-0.0085739,-0.0203574,-0.04052118,0.00080536,0.05575809,-0.04012758,0.07888868,-0.02736235,-0.02806905,0.04618589,-0.04140661,0.1795156,0.13464707,0.00864043,0.00886719,0.06641861,0.04630515,-0.01095761,-0.1288705,0.01608573,0.01937318,-0.01882445,-0.01108285,-0.01504861,-0.03287181,-0.10459704,0.01255655,-0.00458544,-0.04748424,-0.02351501,-0.00508209,-0.00118183,0.0407355,-0.02098742,0.05219577,0.09195323,0.01745242,0.0017361,0.00084829,0.06465787,0.00477812,-0.07749877,0.0334495,0.01830852,0.05210797,-0.00841295,-0.00327956,0.04105337,-0.02522439,0.0019865,0.00911795,-0.01860366,0.01058209,0.03402193,-0.03227935,0.00331534,0.06746803,0.00654894,0.03462155,0.00262391,0.03929916,0.05063974,-0.10375424,0.02943438,-0.0120231,0.0144719,0.01744239,0.05836172,0.0373137,0.07277896,0.0081047,-0.00659793,0.03669554,0.00370468,-0.00309817,-0.01374186,0.03041511,-0.03588073,0.00008296,0.05892816,0.03315233,-0.22133362,-0.00580221,0.01514205,0.09374952,0.02819229,0.01082333,0.01226029,-0.02860725,0.00019887,0.0204985,0.04174184,0.0437129,-0.01679106,-0.02607261,-0.04925087,0.00652354,0.01171127,-0.02086305,0.11098425,-0.01280478,0.02235592,0.03847707,0.23247576,0.02197637,0.01087964,-0.01494777,0.0402445,0.03599575,-0.09064956,-0.00746572,-0.00619398,0.01110697,0.01617409,0.00544545,0.01049142,0.04541808,-0.05090665,-0.01628682,-0.03945602,-0.01781595,-0.09057585,0.00386855,-0.05836203,0.04306365,0.10453825,0.02156118,-0.01697354,-0.09739142,0.02624662,0.06150988,-0.08726691,-0.06083337,-0.0001588,-0.04371422,0.01627809,0.07291667,0.01791305,-0.0557374,0.01716086,-0.01855407,0.02254516,0.01198551,0.06147123,0.01061598,0.02930312],"last_embed":{"hash":"1mxzozu","tokens":197}}},"text":null,"length":0,"last_read":{"hash":"1mxzozu","at":1753423625612},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)#{1}","lines":[17,21],"size":413,"outlinks":[{"title":"Answer from Ion Saliu, Creator of _Lottery Wonder Grid_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#WonderGrid","line":1},{"title":"Criminally-Incorrigible Steve Player, Lottery System Scammer, Pirate","target":"https://forums.saliu.com/steve-player-lottery-piracy.html#Scam","line":2},{"title":"Steve Player' a notorious scammer of lottery systems, also pirates Ion Saliu's lottery strategies.","target":"https://forums.saliu.com/HLINE.gif","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09300439,-0.00247253,-0.04751518,-0.0278879,-0.01516016,0.05644283,0.02981954,0.0004837,0.04284395,0.07287691,0.01862763,-0.01146092,0.00386619,0.05597847,-0.0157306,0.00716929,-0.03854721,-0.0473358,-0.036185,0.04157416,0.03708374,-0.09632491,-0.05167707,-0.07695872,0.06321342,-0.03067025,-0.03489326,-0.05300739,-0.0868399,-0.24578652,0.01801419,0.01315257,-0.04270434,-0.05881109,-0.07110737,-0.0220275,-0.06465247,0.04492619,-0.00449659,0.02379938,0.03333724,0.02313226,-0.02112145,-0.01338471,-0.03306718,-0.0674606,-0.04415981,-0.01177161,0.05564837,0.03888034,-0.07394294,0.02944508,-0.00313692,0.03831612,0.03050954,0.03358962,0.0467818,0.04786896,-0.03154843,0.0183088,0.02662821,0.0407518,-0.19805869,0.04044446,0.0143292,0.01474803,-0.02700079,0.05743609,-0.00925867,0.0370889,0.00086268,0.05596944,-0.0320793,0.02077063,0.00196595,-0.00106572,-0.03407761,-0.02283501,-0.05387028,0.02878047,-0.06736442,-0.00824597,-0.0353543,-0.01191511,0.01673802,0.04367068,0.02973608,0.03562476,0.00479311,-0.04707733,0.05045452,0.06646527,-0.00781487,0.01755197,-0.03082661,0.03994042,0.04372932,-0.02524301,-0.07833844,0.13515571,0.03770085,0.00548388,-0.02312886,0.00452532,0.02817857,-0.02665018,-0.03705162,-0.02314915,-0.05762105,0.01880915,0.01280958,-0.00214521,0.05280507,-0.03049706,-0.03324116,0.04530138,-0.00688137,0.03389113,0.02033442,-0.00386697,-0.04310129,0.05428927,0.0454858,-0.01482027,0.00412454,-0.01416308,0.02160333,0.06294577,0.03655784,0.04014744,0.02681752,-0.03658944,-0.10194746,-0.05985937,-0.00420114,-0.01626245,0.01122288,0.01076136,0.01213208,0.03431891,-0.00046968,-0.01071945,0.05317406,-0.11385494,0.00244716,0.04589241,-0.01118935,-0.0039336,0.00775342,0.00151627,0.01816453,-0.0042868,-0.00211487,-0.08065185,-0.01452858,-0.0134713,0.11908296,0.07997765,-0.05268687,0.03724639,-0.02409619,-0.01398794,-0.00812924,0.1235758,0.02633125,-0.10073945,-0.00157385,-0.01728842,-0.01373826,-0.06151536,-0.01707144,0.04610971,-0.02926352,-0.03261854,0.05637266,-0.04605766,-0.08696854,-0.03221837,0.03438322,0.00305019,0.01412484,0.03582464,0.01965864,-0.02690195,-0.02563072,-0.09724618,0.00039417,-0.02464701,-0.0434401,-0.0001556,-0.03689712,-0.01008078,-0.05686324,0.10459851,-0.01956683,-0.05491897,-0.02658868,-0.09120671,0.07730616,-0.0376152,-0.01211333,-0.02871755,0.007624,-0.01590109,-0.01317017,0.0720991,0.02732786,-0.05406655,0.07257864,0.04126085,-0.02947001,0.00615574,0.05153855,0.03460041,-0.07035158,0.05082348,0.02056293,-0.00515857,0.04002162,-0.01664659,0.00349113,0.01324226,-0.03878595,-0.17973086,0.00664786,-0.06776552,-0.01835091,0.05393809,-0.03938543,0.06893013,0.00186897,0.07654398,0.06226453,0.08221008,-0.0852287,0.08016937,0.06225928,-0.00980622,-0.01754875,-0.05970902,-0.01847217,-0.01316471,0.05550643,-0.00908915,0.07324392,-0.03289322,-0.05649787,0.07762998,0.0065263,0.15004943,0.09624438,-0.01234252,-0.02279769,0.06324933,0.00671169,0.00145698,-0.10238721,-0.00191335,0.06345694,0.00999978,-0.01414005,-0.04541459,-0.02169107,-0.07556284,0.00283536,-0.0383794,-0.07194432,-0.04565438,-0.01804319,-0.047973,0.08094033,-0.02051455,0.09559411,0.04884318,-0.0395592,0.03991777,-0.02917431,0.0625397,-0.01808127,-0.07097378,0.09567054,0.0223474,0.05231946,0.02255831,-0.02014676,0.06344452,-0.00873078,-0.00835858,0.00115713,-0.00336177,0.02330284,0.01047134,-0.01448607,0.02403061,0.02370984,0.01729679,0.06805672,0.0429714,0.06426248,0.04404344,-0.05297153,0.01007548,-0.01838256,0.01278445,-0.02406325,0.01772552,0.05082715,0.08111692,0.02230568,0.03534069,0.06286568,0.00315016,-0.01327718,-0.0392283,0.02782495,-0.05768998,-0.00147225,0.05384237,-0.01196391,-0.22924529,0.05778736,-0.01249439,0.08383425,0.02800981,-0.01901254,0.05477221,-0.00934054,0.00559634,0.01078225,0.05143496,0.04285419,-0.00209069,-0.08138484,-0.0101523,-0.00900987,-0.0052679,-0.03112537,0.07257152,-0.03696915,0.0482317,0.02690457,0.21156175,0.01218139,-0.00666009,-0.02297921,0.04885744,0.03979992,-0.04432714,-0.02112629,-0.03074866,0.01452669,-0.00475598,-0.0212036,0.02056923,0.03430845,-0.06284074,-0.01927671,-0.00871179,-0.02200777,-0.0958269,0.00742654,-0.05343017,0.02060649,0.09850844,0.00748032,-0.01672752,-0.07811662,0.02578279,0.07182039,-0.07876094,-0.05021219,-0.00491319,-0.05817764,-0.00312426,0.06262515,0.02050654,-0.04457574,0.01982746,-0.02262275,0.04517305,-0.00977988,0.06997268,-0.01079535,0.01317389],"last_embed":{"hash":"18nj2j0","tokens":490}}},"text":null,"length":0,"last_read":{"hash":"18nj2j0","at":1753423625677},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy","lines":[22,41],"size":1880,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09484781,-0.0042362,-0.0480571,-0.02790401,-0.0164687,0.05444802,0.03074519,-0.00224055,0.04400771,0.06814335,0.01970523,-0.01119268,0.00510997,0.05466296,-0.0147729,0.00931525,-0.03856022,-0.04965783,-0.03679387,0.03909274,0.03958808,-0.09484607,-0.05128567,-0.07426785,0.06481558,-0.02979293,-0.03255453,-0.0529024,-0.08648662,-0.24630843,0.01934266,0.01265128,-0.04361261,-0.0587575,-0.07301734,-0.02163411,-0.0656352,0.04596044,-0.00405574,0.02419297,0.03304139,0.02241592,-0.01914101,-0.01488925,-0.03199409,-0.06870432,-0.0447237,-0.0103733,0.05784609,0.03657084,-0.07365061,0.03112313,-0.00173606,0.03881709,0.03029368,0.03539828,0.04881921,0.05213733,-0.03085234,0.01812498,0.02732261,0.0436843,-0.19841312,0.04087387,0.01173226,0.01477329,-0.02738786,0.05715697,-0.00970367,0.03811037,0.00090243,0.05519192,-0.03133013,0.02404087,0.00433968,-0.00399233,-0.03510931,-0.0213403,-0.05429978,0.03335851,-0.06833906,-0.00736013,-0.03641836,-0.01310069,0.01741311,0.04353803,0.02935771,0.03588745,0.00496507,-0.04781969,0.05073207,0.06522129,-0.01017991,0.01649488,-0.02816379,0.03795023,0.04266532,-0.02568474,-0.07774504,0.13341486,0.0403377,0.00583523,-0.02620554,0.00418842,0.02513488,-0.02585235,-0.03772933,-0.02434499,-0.05750246,0.01874351,0.00955988,-0.00100616,0.05154898,-0.03332664,-0.03341541,0.04619944,-0.00916006,0.03515453,0.01925407,-0.00496085,-0.03889536,0.05558629,0.04367626,-0.01644271,0.0011758,-0.01580624,0.02242885,0.0618966,0.03613077,0.0395161,0.02814405,-0.03531427,-0.10726838,-0.06176464,-0.00502937,-0.01789884,0.00957286,0.00803331,0.01154277,0.03577564,-0.0017812,-0.01105985,0.05518977,-0.11236698,0.00199808,0.04758324,-0.00903175,-0.00154167,0.01001093,0.00154723,0.017011,-0.00523033,-0.00144781,-0.08146459,-0.01423714,-0.01316385,0.11857209,0.07994685,-0.05069652,0.03734619,-0.0279692,-0.01503347,-0.00666287,0.12188854,0.0240395,-0.10013008,-0.00205034,-0.01631236,-0.01339013,-0.05893866,-0.01617536,0.04654382,-0.02848064,-0.03301659,0.0571613,-0.04620913,-0.08837822,-0.03288732,0.03568806,0.00505943,0.01254098,0.03274554,0.02022556,-0.02462548,-0.02609294,-0.09584874,0.00264048,-0.02472269,-0.04064929,-0.00120646,-0.03757,-0.00676563,-0.06178949,0.10513517,-0.02100609,-0.05511197,-0.02575685,-0.09048352,0.07748721,-0.03566084,-0.01103658,-0.02914452,0.00694619,-0.01548258,-0.01482855,0.07078569,0.02690495,-0.0531713,0.07580624,0.04222733,-0.0310479,0.00429294,0.05209685,0.03540976,-0.06919692,0.04965615,0.02070626,-0.00531226,0.0376549,-0.01649093,0.00534661,0.01286262,-0.03804215,-0.17928214,0.00647942,-0.06831763,-0.01818448,0.05171899,-0.03895073,0.0692378,0.00052549,0.07539829,0.05926003,0.08443379,-0.08459572,0.07711125,0.06432705,-0.00979621,-0.01690704,-0.06080528,-0.01839648,-0.01328923,0.05649697,-0.00934725,0.06773405,-0.03177999,-0.05523859,0.07718576,0.00747322,0.14940245,0.09429476,-0.0129879,-0.02377584,0.06422582,0.00416876,-0.00027017,-0.10364445,-0.00059859,0.06362735,0.00938334,-0.01413521,-0.04552978,-0.02007538,-0.07599201,0.00269808,-0.03740907,-0.07214688,-0.0454757,-0.01817374,-0.04838354,0.0805536,-0.01885219,0.09576096,0.05163117,-0.03858678,0.03906669,-0.02722409,0.05977034,-0.02073861,-0.06911764,0.09522485,0.02078655,0.05399858,0.02332753,-0.02096647,0.06429197,-0.00683001,-0.00492056,0.00304057,-0.00449999,0.02309107,0.0107452,-0.01245877,0.02485912,0.02552592,0.01719784,0.06860216,0.04470282,0.0624272,0.04263163,-0.05268398,0.01013502,-0.01996478,0.01273799,-0.02579507,0.01860081,0.051808,0.07653175,0.02168336,0.03473822,0.06076366,0.00602092,-0.01322976,-0.03741877,0.02904096,-0.05756862,0.00060174,0.05127128,-0.01286215,-0.23091733,0.05960866,-0.01591091,0.08307988,0.02792507,-0.01788995,0.05498049,-0.01157834,0.00629873,0.01055089,0.05168656,0.04405237,-0.00044649,-0.07931411,-0.00910768,-0.00964592,-0.00568635,-0.03053425,0.07228161,-0.03840994,0.05089806,0.02796993,0.21346493,0.01262508,-0.00822826,-0.02205653,0.05011648,0.03910577,-0.04401087,-0.02123608,-0.03031253,0.01229798,-0.00541928,-0.0205054,0.01935073,0.03448257,-0.06047295,-0.01755522,-0.00937868,-0.0223845,-0.0955734,0.00736525,-0.05234446,0.01914123,0.09981512,0.00816045,-0.01689593,-0.07601988,0.0276601,0.07282077,-0.07831881,-0.05137318,-0.00519921,-0.0604726,-0.00229124,0.06340525,0.02157935,-0.04333309,0.01921045,-0.02172289,0.04520059,-0.00900282,0.07194351,-0.01250048,0.0113521],"last_embed":{"hash":"6g8uo8","tokens":488}}},"text":null,"length":0,"last_read":{"hash":"6g8uo8","at":1753423625854},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy#{1}","lines":[24,41],"size":1821,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11174721,0.01418241,-0.08654255,-0.0534666,-0.0247068,0.04898948,0.05727743,-0.00490797,0.06170649,0.06314043,0.00099642,-0.03558399,0.0292388,0.01786102,-0.02388785,0.01243177,-0.01541237,0.00968204,-0.05819688,0.05442904,0.05119263,-0.08175197,-0.03967089,-0.09481095,0.05235162,-0.01848462,-0.05308449,-0.0705215,-0.06933638,-0.22889413,-0.00217046,-0.01182262,-0.02956016,-0.01754805,-0.07725473,-0.03782314,-0.02469606,0.03627618,0.01778232,-0.00435716,0.02967818,0.02802514,-0.00991441,-0.010501,-0.03737897,-0.04282173,-0.03794803,-0.01865263,0.0618838,0.04824016,-0.06279159,0.02198029,-0.03327414,0.032674,0.03414925,0.03597846,0.07268058,0.05280073,-0.02923036,0.02963576,0.018151,0.03023786,-0.19729123,0.0193995,0.06144709,-0.01401787,-0.03736239,0.01133122,-0.03027133,0.038574,-0.00616437,0.04533996,-0.05772567,0.02360366,0.00592208,-0.02690991,-0.05352286,-0.01692722,-0.06122038,0.03292832,-0.0749383,-0.02338559,-0.02098768,0.00940083,-0.00617102,0.05463361,0.02480965,0.02726002,0.02115774,-0.06625509,-0.0011607,0.02439512,0.03475657,0.0054359,-0.02491718,0.07194214,0.0306708,-0.0137909,-0.024354,0.12080604,0.00267512,-0.03445242,-0.00187729,0.03577494,0.02936193,-0.05749174,-0.02263596,0.00524077,-0.02787982,0.00622058,0.01746406,-0.01880465,0.03338375,-0.01062653,-0.01905242,0.05411493,0.00626124,0.04014956,0.00762024,-0.0407659,-0.06490522,0.05837695,0.04655465,-0.01187268,0.00794411,0.01496165,0.04115404,0.049163,0.03061759,0.03131047,-0.00829256,-0.01094342,-0.05299738,-0.0558392,-0.02771209,-0.00513568,0.01517756,0.00510694,0.02423745,0.04868197,0.03941676,-0.01458142,0.04311702,-0.05326676,0.0333001,0.0372675,-0.02387711,-0.02634153,0.01893273,0.03484444,-0.00935555,-0.00769483,-0.00339801,-0.06720654,-0.01535701,-0.01052275,0.13215317,0.0795055,-0.05373143,0.06834067,0.00597546,-0.04939374,-0.01203678,0.16452982,0.03598022,-0.06739653,0.00840348,0.00892128,-0.02390195,-0.07270686,-0.00679702,0.03689582,-0.04329995,-0.00485905,0.04945309,-0.07627495,-0.11225621,-0.01639873,0.01070418,0.00979132,-0.005107,0.02170673,-0.01999618,-0.01858717,-0.01751697,-0.08975033,-0.02003201,-0.03009132,-0.02901743,0.04441218,-0.0340944,0.02060001,-0.03779912,0.07570533,-0.03811855,-0.02031781,-0.00441095,-0.06299333,0.06097331,-0.01633612,-0.04276485,-0.0105025,-0.00957559,-0.05025898,-0.05009667,0.05803509,-0.0088834,-0.04634523,0.06958884,0.03978077,-0.04903714,-0.02013252,-0.03467292,0.02241662,-0.05409733,0.04791932,0.03044489,-0.00390334,0.02994023,0.01249158,-0.02006382,-0.00280683,-0.05421053,-0.20547134,-0.01560811,-0.0687309,-0.05187056,0.0494698,-0.03589764,0.07730573,0.00089049,0.0476677,0.04887997,0.09296224,-0.07954711,0.04783798,0.03666437,-0.02389378,0.00442962,-0.04883119,0.00513782,-0.01153742,0.06438108,-0.01243623,0.08915146,-0.03312004,-0.03561118,0.06804334,-0.01655484,0.17133898,0.13808997,-0.00611625,0.00830687,0.06833757,0.0285778,-0.0205808,-0.12220671,-0.00788358,0.04858133,-0.01780375,-0.02843342,-0.03665476,-0.02606463,-0.10958607,-0.01858429,0.01432181,-0.06951624,-0.02540703,-0.00880312,-0.02955335,0.01358705,-0.01144462,0.07361821,0.07264207,-0.0280161,0.03617235,-0.01668228,0.02770181,0.00184944,-0.08397269,0.04324531,0.0063829,0.06213004,0.0138042,-0.01680808,0.05092046,-0.0206796,-0.01139706,0.00050583,-0.01577713,0.01493827,0.04075241,-0.01463912,0.01653427,0.02453149,0.02366669,0.05496124,0.03631083,0.04268842,0.03242298,-0.08198495,0.02679896,-0.00190172,-0.02052622,-0.02152925,0.05668082,0.0563583,0.07284349,-0.00065403,0.01991965,0.07895562,0.00696725,-0.02588436,-0.00315765,0.02394888,-0.05133739,0.04049692,0.05553612,0.01210571,-0.23404403,0.02766468,0.0017858,0.08512897,0.00717018,-0.00776294,0.04975849,-0.0242265,0.00869624,0.00141307,0.06517634,0.03605862,-0.009632,-0.05043961,-0.04141866,0.03943605,-0.00507965,-0.0113619,0.07657336,-0.02851321,0.05489235,0.01963533,0.24822052,0.00388625,0.0193317,-0.00787672,0.02126274,0.05390168,-0.03669255,-0.02913719,-0.02197518,0.0106385,0.02097777,-0.01175944,0.02674994,0.06367086,-0.07348883,-0.04718359,0.00615789,-0.02236074,-0.06476726,-0.00121997,-0.05384925,0.01610153,0.10475974,0.01260682,-0.01264594,-0.06712717,0.01158778,0.06349781,-0.08532152,-0.05489081,-0.00036801,-0.03890332,0.00305947,0.03922644,0.01585959,-0.02483315,0.02041161,-0.00118795,0.0188095,0.03152952,0.06690535,-0.00238195,0.04056881],"last_embed":{"hash":"1cdxvh5","tokens":505}}},"text":null,"length":0,"last_read":{"hash":"1cdxvh5","at":1753423626004},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_","lines":[42,67],"size":1763,"outlinks":[{"title":"![View screen shot of the wonder grid for pick-3 with winning records.","target":"https://saliu.com/ScreenImgs/wonder-grid-pick-lottery.gif","line":19},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11141971,0.01673611,-0.08717422,-0.05186914,-0.02753794,0.04881941,0.06461473,-0.00312147,0.05888265,0.06666065,0.00163926,-0.03750234,0.02886062,0.01992425,-0.02292028,0.01643714,-0.01701537,0.0103607,-0.05905264,0.05556482,0.04993793,-0.07939295,-0.03816742,-0.09517912,0.05377023,-0.02191838,-0.04884106,-0.06969756,-0.06989586,-0.22998777,-0.00541097,-0.00902697,-0.03094509,-0.01602231,-0.07491933,-0.03371838,-0.02107652,0.02896951,0.01640264,-0.00488664,0.03146147,0.02901385,-0.01215235,-0.01045885,-0.03481884,-0.04121835,-0.03883126,-0.01850396,0.05928133,0.05044867,-0.06178112,0.0218244,-0.02874692,0.03672776,0.03282403,0.0387118,0.07431835,0.05098881,-0.0269393,0.02901836,0.01537117,0.02899101,-0.19675538,0.0159769,0.06090437,-0.0134831,-0.03932521,0.00970061,-0.02938321,0.03667958,-0.00947026,0.04599295,-0.05912769,0.0217335,0.00702722,-0.03154867,-0.05138935,-0.01588742,-0.06146717,0.03819757,-0.07292623,-0.02236097,-0.02424071,0.01061211,-0.01067132,0.05618792,0.02124995,0.02554471,0.02069866,-0.06167477,-0.00300494,0.01811486,0.03820081,0.00745975,-0.02492497,0.07150812,0.03379929,-0.01232779,-0.02639812,0.12234262,0.00025761,-0.03365088,0.00005206,0.03645454,0.02836132,-0.05563253,-0.0224799,0.00397876,-0.02816255,0.00682728,0.01504678,-0.01751554,0.02979637,-0.0070033,-0.01582653,0.05518594,0.0053881,0.04317326,0.00364297,-0.04293031,-0.06385203,0.06254634,0.04297793,-0.01389488,0.00286063,0.01050461,0.04266383,0.0475379,0.03110819,0.02906388,-0.00654633,-0.01508565,-0.05169182,-0.05423899,-0.0247193,-0.002668,0.01279287,0.00643896,0.02804359,0.04825493,0.040716,-0.0117369,0.03566783,-0.05131456,0.03399528,0.0314165,-0.02285989,-0.02678396,0.01800511,0.0351503,-0.00913015,-0.00857611,-0.00138036,-0.06574301,-0.01258387,-0.01110146,0.13396916,0.08058544,-0.05337188,0.06728188,0.00724632,-0.05194852,-0.01138408,0.16406767,0.03621422,-0.06571589,0.0064371,0.00937241,-0.0232305,-0.06697601,-0.00779799,0.03878912,-0.04497268,-0.01050226,0.04578402,-0.07705378,-0.1123409,-0.0124799,0.01094144,0.00613634,-0.00491878,0.01938511,-0.02049334,-0.01864184,-0.01706638,-0.09042116,-0.01925,-0.0305652,-0.02760261,0.04688019,-0.032322,0.02432842,-0.04062353,0.07466123,-0.03601732,-0.02311614,-0.00198144,-0.06400444,0.05939947,-0.0212603,-0.0467534,-0.00719124,-0.0074382,-0.05210505,-0.04782707,0.05377149,-0.01042132,-0.04416374,0.06907326,0.04297543,-0.05223025,-0.02411688,-0.03903534,0.0216272,-0.05444272,0.04901608,0.03033264,-0.00106161,0.03064177,0.00686038,-0.02049846,-0.00284213,-0.05493284,-0.2094797,-0.01086891,-0.07169087,-0.04689276,0.05206033,-0.0351816,0.07529932,0.00106626,0.04403619,0.04909377,0.09194206,-0.07604243,0.05041683,0.03586516,-0.0213615,0.00909416,-0.04489015,0.00353424,-0.00787482,0.06021891,-0.01669728,0.09331658,-0.0346861,-0.03148453,0.06859412,-0.01865093,0.17274393,0.14347114,-0.00162191,0.00578506,0.06723353,0.02873671,-0.02063517,-0.12636548,-0.01065207,0.052817,-0.01379317,-0.02583377,-0.03459888,-0.03091051,-0.1049822,-0.01926553,0.00990112,-0.07050252,-0.02484473,-0.0108441,-0.02905484,0.0105703,-0.01225344,0.07787006,0.07297926,-0.03282843,0.03659449,-0.01803706,0.02542179,-0.00166195,-0.08495048,0.04465074,0.00717784,0.05996806,0.01885443,-0.01633818,0.05471514,-0.02337052,-0.00921347,0.00111345,-0.01780757,0.01769157,0.04186986,-0.01481953,0.02079161,0.02339632,0.02581671,0.05500978,0.03927808,0.04119929,0.03313908,-0.08057532,0.02870998,-0.00054879,-0.02349556,-0.02354661,0.05788285,0.05928029,0.07071663,0.00098175,0.01624413,0.07815215,0.00182076,-0.02519319,-0.00449419,0.02902712,-0.05536729,0.04030441,0.05482683,0.01028839,-0.2327285,0.02754711,0.00641289,0.08371028,0.00636277,-0.0052253,0.04761384,-0.02491362,0.00657174,0.00207894,0.06520528,0.03519653,-0.00618614,-0.04687508,-0.04318916,0.04453079,-0.00250258,-0.01293859,0.07999565,-0.02975878,0.05244326,0.01961491,0.24654648,0.00007183,0.01895412,-0.01071962,0.02204784,0.04793301,-0.03631706,-0.03055576,-0.02267282,0.01017818,0.01862171,-0.0079561,0.03034464,0.06069056,-0.07906811,-0.04539644,0.00322542,-0.02496667,-0.06548504,0.00282469,-0.05445892,0.01332713,0.09954451,0.01254774,-0.01424144,-0.06374387,0.01001394,0.06382091,-0.07876202,-0.05459854,0.0018205,-0.03773198,0.00025431,0.03755004,0.01610161,-0.02648751,0.01869446,-0.00294215,0.01818084,0.03557077,0.07078233,-0.00548895,0.04190708],"last_embed":{"hash":"3sl6qp","tokens":459}}},"text":null,"length":0,"last_read":{"hash":"3sl6qp","at":1753423626180},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_#{1}","lines":[44,65],"size":1572,"outlinks":[{"title":"![View screen shot of the wonder grid for pick-3 with winning records.","target":"https://saliu.com/ScreenImgs/wonder-grid-pick-lottery.gif","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10925242,-0.01188226,-0.05917267,-0.03194906,0.00330484,0.04013625,0.04572521,-0.00371191,0.06529524,0.07191761,0.04922615,0.01919003,0.05625452,0.02643246,-0.02514815,0.01066424,-0.01228246,-0.00961135,-0.01034521,0.06829686,0.03931823,-0.09456729,-0.0496332,-0.08768719,0.030967,-0.00826202,-0.09739012,-0.0452315,-0.09878463,-0.24133623,0.01096688,-0.00061993,0.0029847,-0.03665515,-0.05885586,-0.07115534,-0.04041789,0.00557761,-0.01905523,-0.00302014,0.01971333,0.03746476,-0.02236904,-0.00202151,-0.03134029,-0.08046018,-0.03727892,-0.01291658,0.05699858,0.01345824,-0.09026324,0.02936328,-0.03794569,0.03984458,0.02063409,0.02337477,0.06686652,0.06084266,-0.01550027,0.0220203,0.02615131,0.00261432,-0.14784178,0.01991163,0.09030396,-0.02025736,-0.00664781,-0.03169819,-0.01483115,0.02721196,0.00562338,0.06372573,-0.04633816,0.03098685,0.00429265,-0.00906306,-0.03371838,-0.03542922,-0.04233059,0.06062184,-0.05493179,-0.04469912,-0.03014272,-0.01485772,-0.01417859,0.0218425,0.01779089,0.02407649,0.04329612,-0.06240237,-0.00842443,0.05313008,0.04018243,0.00806152,-0.00925132,0.04926989,0.02643458,0.00116323,-0.05460468,0.10385797,0.01657865,-0.01707282,-0.00439499,-0.00485338,0.05986869,-0.04742571,-0.0242143,-0.00184202,-0.02833491,-0.01828094,0.03030827,0.00201483,0.06669582,0.0163043,0.0096129,0.02590288,0.0167479,0.03308512,0.03696074,0.00038923,-0.09547123,0.07691374,0.06359448,-0.01251515,-0.01467232,-0.00692768,0.02644327,0.07504844,0.02815006,0.04019136,-0.01528431,-0.05695882,-0.03657448,-0.03319659,-0.00946164,0.00923542,-0.00297196,0.01977791,0.03478415,0.03302754,-0.01549721,-0.00073231,0.02156636,-0.07591925,0.01085096,0.00826695,-0.01873096,-0.01770242,0.00858945,0.02718421,0.01527108,-0.01127226,0.00556933,-0.08357681,-0.02602123,-0.03149667,0.15455009,0.07526921,-0.07072298,0.05109918,0.02555097,-0.00897606,-0.00496626,0.13661389,0.04012759,-0.05682193,0.02605634,-0.02978602,-0.0388017,-0.07587366,-0.01234248,0.03756534,-0.04711508,0.01833967,0.02814834,-0.06705144,-0.08255018,-0.00012459,0.01409463,0.00758577,0.01534073,0.01599762,-0.01806849,-0.02667974,-0.00730064,-0.08027484,-0.03333065,0.02232734,-0.04026613,0.03348041,-0.04053641,-0.00682524,-0.04227652,0.04108145,-0.02844119,-0.03176561,-0.01427155,-0.06450333,0.04442048,-0.0344925,-0.0383701,-0.02544304,0.01132346,-0.01582767,-0.01372791,0.06599848,0.01204826,-0.07056037,0.05777485,0.01787398,-0.0430756,-0.02997111,-0.01831328,0.04510569,-0.0894406,0.03751267,0.05319277,-0.02060859,0.02538089,-0.01580588,-0.02807859,0.00957322,-0.04917752,-0.18785527,-0.007345,-0.06705739,-0.01193877,0.06399136,-0.04282451,0.06904527,-0.02298798,0.05717826,0.09725163,0.0572868,-0.08804337,0.05935647,0.03346026,-0.00487913,-0.01128243,-0.04080482,-0.02235206,-0.02439438,0.04517151,-0.02305911,0.10648673,-0.06556162,-0.03976639,0.08567612,-0.03057622,0.18365453,0.10990899,0.00624092,0.02378525,0.04305767,0.05002336,0.01500654,-0.09779447,-0.01095294,0.05816314,0.03575135,-0.02605079,-0.0507935,-0.03879952,-0.13932864,0.00429764,-0.01312953,-0.08650216,-0.03429292,0.03246085,-0.03538907,0.03083719,-0.01793524,0.09464646,0.0792303,-0.0401978,0.01123578,-0.03255872,0.05632587,-0.00588196,-0.06576075,0.07208542,0.02608755,0.04561588,0.00861228,-0.04490351,0.05592869,-0.03528984,-0.00618034,-0.00216654,-0.00051809,0.03777983,0.02232115,-0.00999638,0.01507435,0.07384735,-0.00453409,0.04883942,-0.00448851,0.05357512,0.04987955,-0.07855353,0.01426833,-0.00525719,0.02001245,0.07717621,0.02432974,0.05164563,0.08344316,-0.03146718,0.01486496,0.07828891,-0.00741133,-0.02415465,-0.02094375,0.03088937,-0.03118314,-0.01174398,0.07784916,0.01141826,-0.22701727,0.01811519,-0.01359953,0.07379302,-0.02694683,-0.00495304,-0.00472213,0.01115994,0.01758312,-0.00129741,0.04520803,0.01293061,-0.02933838,-0.04042158,-0.02939782,0.01786563,-0.00297099,-0.02138869,0.08122907,-0.01179254,0.03391446,0.01485288,0.2332529,0.00460124,0.0062201,0.00732895,0.04443707,0.05162938,-0.03315669,-0.01375639,-0.04023786,0.0013764,0.01508464,-0.00809664,0.02010911,0.03779708,-0.06325523,-0.03054888,-0.01774814,-0.02047666,-0.08007849,0.00938113,-0.0376531,0.01666781,0.09839953,0.02080262,0.0076179,-0.09302551,0.02799298,0.08129723,-0.06368831,-0.05469437,0.00018402,-0.0261784,0.01110995,0.04623822,0.00507453,-0.02522602,0.03024515,-0.0145408,0.00680899,-0.00284069,0.06099285,0.02272147,-0.00692],"last_embed":{"hash":"1s20ikb","tokens":442}}},"text":null,"length":0,"last_read":{"hash":"1s20ikb","at":1753423626338},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate","lines":[68,116],"size":5095,"outlinks":[{"title":"Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player","target":"https://saliu.com/bbs/messages/188.html","line":19},{"title":"![Run the best 3-digit lotto software named Bright pick-3 programs.","target":"https://saliu.com/ScreenImgs/pick31.gif","line":38},{"title":"Australian roulette system commits piracy of Ion Saliu's roulette system based on wheel half, sector, hemisphere","target":"https://saliu.com/roulette-millions.html","line":40},{"title":"Piracy, scams on eBay regarding lottery and gambling systems","target":"https://download.saliu.com/eBay-scams.html","line":41},{"title":"Piracy: Unauthorized Software Offering, Distribution","target":"https://saliu.com/bbs/software-piracy.html","line":42},{"title":"Run legitimate lottery software to create lotto systems based on pairs, not Steve Player piracy.","target":"https://forums.saliu.com/HLINE.gif","line":44},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":46},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":46},{"title":"Search","target":"https://saliu.com/Search.htm","line":46},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto last digits.","target":"https://forums.saliu.com/HLINE.gif","line":48}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10378386,-0.01428907,-0.05583727,-0.0308701,-0.00382979,0.03922916,0.04975795,-0.00642895,0.06174961,0.07483325,0.0484597,0.02254087,0.0514342,0.02731233,-0.02308204,0.01057063,-0.01438842,-0.00848594,-0.01312625,0.06585129,0.03685993,-0.09168549,-0.04598016,-0.08365723,0.02945285,-0.01025884,-0.09756934,-0.04689788,-0.1011289,-0.24243364,0.01447415,-0.00398134,0.00113092,-0.03362907,-0.06389196,-0.0747994,-0.04426268,0.0083851,-0.02176242,-0.00404432,0.0198193,0.03579436,-0.02101296,-0.00256557,-0.03433996,-0.08203921,-0.03416559,-0.01281427,0.05715497,0.0117636,-0.08805097,0.02799377,-0.03622993,0.04467142,0.01922156,0.02038926,0.06617516,0.06321187,-0.01669079,0.02254452,0.025711,0.0028914,-0.14180909,0.01632646,0.08769517,-0.0144123,-0.01133272,-0.0292129,-0.01768484,0.02604297,0.00808293,0.0643491,-0.04614228,0.03284807,0.00591989,-0.00721784,-0.03478832,-0.03552207,-0.04311632,0.06484395,-0.06183641,-0.04761849,-0.02986094,-0.01775574,-0.01570765,0.01913316,0.01376517,0.01895838,0.04153528,-0.06144536,-0.00739012,0.05051771,0.03540311,0.00547368,-0.0101171,0.04771923,0.02471071,-0.00014157,-0.05878023,0.10240187,0.01757035,-0.01785043,-0.0121902,-0.00588471,0.06078978,-0.04548326,-0.02875638,-0.00272772,-0.02896782,-0.01779288,0.02862708,0.00332956,0.0670905,0.01755179,0.00777469,0.0352053,0.01640923,0.03451138,0.03760285,-0.00237304,-0.09293614,0.07656659,0.06513106,-0.01091982,-0.01800622,-0.00596762,0.0277539,0.07606795,0.02719514,0.04142473,-0.01634151,-0.05490695,-0.03916219,-0.031104,-0.00926162,0.01011403,-0.00835297,0.0266479,0.03406715,0.03149782,-0.01990723,0.00143516,0.02266962,-0.07995549,0.00999208,0.00227505,-0.015623,-0.01835128,0.00788862,0.02879091,0.01669257,-0.01453339,0.00257709,-0.08573393,-0.02594681,-0.03188778,0.15423188,0.07461584,-0.07090216,0.05018338,0.02280681,-0.00922282,-0.00192783,0.13778967,0.03775967,-0.05418552,0.02339687,-0.02690008,-0.0375888,-0.07697339,-0.01142966,0.03748385,-0.05055734,0.02181017,0.0252235,-0.06862956,-0.08460369,-0.00337634,0.01502537,0.00834394,0.01731349,0.01677725,-0.01742449,-0.02436244,-0.01122701,-0.08009763,-0.02899405,0.02341481,-0.03709366,0.03371573,-0.04148542,-0.00399162,-0.04563859,0.03807827,-0.02674947,-0.03127856,-0.01271115,-0.06347921,0.04053915,-0.03219693,-0.03381582,-0.02449161,0.01098148,-0.01426999,-0.0133442,0.06605174,0.01621807,-0.07569289,0.06007958,0.01174492,-0.04329626,-0.02639651,-0.02005823,0.04795858,-0.08539236,0.04201996,0.0539841,-0.02185093,0.02571167,-0.01769704,-0.02826725,0.01027675,-0.04678429,-0.18673821,-0.00823014,-0.06698062,-0.00871638,0.05740016,-0.04030446,0.0678112,-0.02453165,0.05967009,0.09656156,0.06012496,-0.09157901,0.05906702,0.04100308,0.00086549,-0.0063691,-0.04343442,-0.02098313,-0.02386341,0.04716775,-0.02517329,0.10497556,-0.05895683,-0.04133498,0.08508474,-0.03283321,0.18524846,0.10463545,0.00391049,0.02551082,0.04014948,0.04820251,0.00978928,-0.0911549,-0.00712622,0.05974928,0.0316117,-0.02613825,-0.04874706,-0.03616453,-0.14029661,0.00191962,-0.01213994,-0.08844413,-0.03182007,0.03090574,-0.03700598,0.03547805,-0.02042184,0.09360432,0.08173719,-0.03716554,0.01369843,-0.03099629,0.05887808,-0.00956247,-0.06098223,0.06843214,0.02866887,0.04970445,0.01009185,-0.04890136,0.05507363,-0.03521603,-0.00715883,-0.00091075,-0.00323343,0.03760316,0.02399113,-0.0098893,0.01606843,0.07359587,-0.00256566,0.04861289,-0.00392391,0.05406779,0.04733729,-0.08097702,0.01344641,-0.00340527,0.02328315,0.07978567,0.02023286,0.05118739,0.07884549,-0.03438156,0.01570257,0.08278395,-0.00289259,-0.02355298,-0.01987733,0.03319869,-0.03092165,-0.00719573,0.07446496,0.00980692,-0.23022343,0.01385423,-0.0167584,0.0713312,-0.0283377,-0.00206014,-0.00361271,0.01730283,0.01861564,-0.00661114,0.04348478,0.01034533,-0.0273438,-0.03939975,-0.03093247,0.01490966,-0.00146963,-0.0239606,0.08488853,-0.0151727,0.03664066,0.02103863,0.23726042,0.00587118,0.00272509,0.0080824,0.04903606,0.04973178,-0.03484581,-0.01480752,-0.0382172,0.00087482,0.01661412,-0.00573554,0.02068844,0.03685303,-0.06317966,-0.02938663,-0.01856155,-0.02397735,-0.08321381,0.00993817,-0.04034888,0.02043569,0.09683698,0.02088391,0.00783062,-0.09206692,0.03551275,0.08016634,-0.05769832,-0.05091528,0.00130129,-0.03028907,0.01390102,0.04473681,0.00380949,-0.02346312,0.03051802,-0.01085161,0.00668326,-0.0024729,0.06177314,0.02178408,-0.00401698],"last_embed":{"hash":"1msx500","tokens":467}}},"text":null,"length":0,"last_read":{"hash":"1msx500","at":1753423626484},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{1}","lines":[70,85],"size":1914,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10003611,-0.02646811,-0.04643008,-0.02864945,-0.00060552,0.02715705,0.04018467,-0.01701904,0.0612196,0.05494002,0.02458089,0.04838222,0.02218358,0.00350867,-0.02591158,0.0221011,0.01136368,-0.02867753,-0.04658101,0.06060342,0.04384572,-0.09563612,-0.0567822,-0.06222083,0.02519458,-0.0080172,-0.08752384,-0.05351473,-0.10797855,-0.23106518,0.03556025,0.01718588,0.00322888,-0.05763009,-0.09926914,-0.07479045,-0.03597331,0.02312059,-0.03490759,0.00715052,0.03446579,0.03302522,-0.01944319,-0.01134927,-0.02540036,-0.07034803,-0.04277256,0.01068282,0.063124,0.01411306,-0.07071333,0.03006454,-0.00620343,0.03434227,-0.00398016,-0.00859955,0.05880629,0.09880254,-0.00093562,0.01976647,0.07687888,0.02677162,-0.14037956,0.03871523,0.05525615,0.02166401,-0.02098992,-0.02374084,-0.00227271,0.03270839,0.00260114,0.06335705,-0.06695656,0.05396115,-0.03344313,-0.038861,-0.00278491,-0.01053389,-0.02743786,0.03393301,-0.06076254,-0.04599652,-0.04407951,-0.01063367,0.00326471,0.0192272,0.01174532,0.03451981,0.03024597,-0.04886379,0.02061512,0.02870654,0.03753363,0.02948515,-0.00008172,0.01664552,0.01864303,-0.02674626,-0.01653448,0.1113157,0.0376536,-0.01742441,-0.00795966,-0.008536,0.04551043,-0.02491628,-0.04865272,-0.04784451,-0.04129642,-0.00378072,0.03501154,0.01650152,0.09997428,-0.00295816,-0.02174952,0.0204758,-0.02930641,0.00142553,0.06640974,-0.00776891,-0.09513023,0.0595132,0.05526161,0.00922108,0.00703628,-0.00521182,0.03910228,0.05497724,0.01821274,0.03674804,-0.01817135,-0.03171929,-0.06642164,-0.02906526,0.01044699,0.00735076,-0.02630163,0.04179199,0.01694115,0.05461196,-0.00690878,-0.01148317,0.0750984,-0.10005937,0.0189211,0.02680968,-0.01276009,-0.01368024,-0.02160543,0.00626004,0.02118632,0.00197734,-0.01414997,-0.0695254,-0.0262092,-0.02929038,0.16804147,0.06264544,-0.09164796,0.03502386,-0.00124347,0.00471703,-0.00266586,0.11927265,0.03385546,-0.08795133,0.01136753,0.0041861,-0.01982346,-0.04893541,-0.0418334,0.04441143,-0.07514258,0.02930203,0.05899468,-0.07885416,-0.08673225,-0.00365312,0.01047356,0.01111007,0.00798183,-0.00074572,-0.03813775,-0.01606107,-0.02993838,-0.09005643,-0.00638033,0.01359251,-0.04798306,0.04396415,-0.04454841,0.02885921,-0.02636846,0.00177364,-0.0173435,-0.0284937,-0.04892564,-0.03210342,0.06776935,-0.0242171,-0.0185781,-0.02261167,0.02124665,-0.01751219,-0.00358047,0.07045331,0.01041651,-0.09166417,0.06256273,-0.01380618,-0.02519268,-0.00792928,0.00504416,0.04737901,-0.06854084,0.03514346,0.04090608,0.01590632,0.01744444,-0.01630397,0.01503549,0.04093149,-0.0761229,-0.19090481,-0.0024808,-0.06415877,-0.02225848,0.05660931,-0.03760608,0.06284914,-0.00529022,0.06191766,0.14175501,0.08502886,-0.07933146,0.03528331,0.05940757,-0.02780874,0.00321244,-0.0665184,-0.02323742,-0.03444357,0.04093519,-0.003375,0.05937613,-0.03591357,-0.06633381,0.07493798,-0.03813059,0.15986288,0.0790863,-0.03497274,-0.0112341,0.05401431,0.03270704,-0.00996898,-0.08372316,0.00554725,0.04723776,0.03076472,-0.03340751,-0.01330555,-0.01754023,-0.11953279,0.00381531,-0.04293712,-0.08243775,-0.01333238,0.02752267,-0.00995486,0.04724884,-0.00016397,0.10449787,0.07957377,-0.03715317,0.01987686,0.00496243,0.05712981,-0.01987349,-0.07244711,0.07619983,0.03533305,0.08489309,0.00973285,-0.04190772,0.01328558,-0.04339229,0.02609452,-0.01078336,-0.01914223,0.03019968,0.03885079,-0.04891653,-0.01149483,0.06658875,-0.02373508,0.04030348,-0.00439646,0.05901282,0.0577203,-0.0711145,0.02723907,0.00841897,0.00194965,0.00797021,0.02699793,0.0561525,0.06579021,-0.01866997,0.00354085,0.07464688,-0.02834179,-0.00952548,-0.01983163,0.02662086,-0.04267414,-0.01969661,0.05858822,0.02836844,-0.21303689,0.02275453,-0.02757914,0.06086011,-0.0141995,-0.01987538,0.01078508,0.00526555,0.01769026,-0.01062424,0.04044559,0.03848205,0.00081601,-0.06356631,-0.04479295,0.018652,0.01128589,0.00144984,0.073826,-0.00914018,0.04937273,0.04207905,0.23004921,0.00509001,0.00011365,0.01903892,0.01252553,0.05948326,-0.06685614,-0.00181084,-0.00300878,0.00724581,0.01432259,-0.02412208,-0.02130144,0.02481714,-0.03141002,-0.02787743,-0.01196931,0.01293561,-0.07026933,-0.00298422,-0.05005802,0.01025769,0.101513,0.01103389,-0.00034081,-0.11661007,0.02672325,0.07690443,-0.09102563,-0.04257974,-0.02865575,-0.04590644,0.02798019,0.04683236,0.01887523,-0.01333069,0.02877079,0.00749698,0.00738093,-0.00390196,0.05735719,0.01972923,0.00686649],"last_embed":{"hash":"1wbkt74","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1wbkt74","at":1753423626693},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{3}","lines":[88,106],"size":2023,"outlinks":[{"title":"![Run the best 3-digit lotto software named Bright pick-3 programs.","target":"https://saliu.com/ScreenImgs/pick31.gif","line":18}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12725368,-0.00260757,-0.05940381,-0.0400674,-0.01732795,0.04460015,0.0116478,-0.00273996,0.00370267,0.05175613,0.02920776,0.01263524,0.01037331,0.02746745,-0.02822984,-0.02578696,0.01039092,0.02233569,0.02364174,0.07448345,0.04569766,-0.06167965,-0.04022707,-0.07442199,-0.00079201,0.00287692,-0.07206317,-0.0532583,-0.05797216,-0.1729975,0.0171787,0.00688638,-0.03011048,-0.02686253,-0.05153809,-0.0803828,-0.03678184,0.05765426,-0.04380278,0.02162641,0.03005232,0.04785816,-0.00148371,0.00670271,-0.00887849,-0.04877618,0.00505906,0.00859092,0.06121607,0.02567176,-0.06923453,0.04910477,-0.03035581,0.01755602,0.01998499,-0.00879683,0.05140441,0.07941948,-0.00652526,-0.00693629,0.04410642,0.0205553,-0.20334475,0.02327529,0.06394085,0.02139726,-0.01147337,-0.00487703,-0.00352442,0.01194559,-0.00920811,0.03272001,-0.07534762,0.04166191,0.01317838,-0.01296372,-0.03860517,-0.03153794,-0.03208413,0.01944258,-0.04462902,-0.06466036,-0.03137694,0.00332039,-0.02578688,0.03354945,0.01432726,0.04842642,0.06462991,-0.07384182,-0.01341791,0.03489733,0.07470432,0.0171062,-0.02017011,0.02776666,-0.00046432,0.00190045,-0.01564161,0.11621417,0.02702217,-0.04109692,-0.01473404,-0.0007289,0.05840669,-0.02383152,-0.02652327,0.00368308,-0.02055744,0.00588082,0.02811698,0.02698201,0.07150774,-0.00625707,-0.03008619,0.01666896,-0.02298885,0.03641824,0.01606952,-0.00036897,-0.0899433,0.0627476,0.06067884,0.01290594,0.01731667,-0.01099794,-0.00981539,0.0557081,0.02379844,0.03403739,-0.03853579,-0.04229784,-0.05202268,-0.02968531,-0.01262284,-0.01546698,-0.01931776,0.02365042,0.01709218,0.01436026,-0.03466556,-0.0324654,0.04752414,-0.07747214,0.01199529,-0.00734489,-0.00891072,-0.03641585,-0.00828262,0.03774462,0.01552716,-0.01843798,-0.00899945,-0.06546699,-0.03987901,-0.02756212,0.16917858,0.07902547,-0.08152204,0.03869689,0.02199826,-0.02366196,0.01651457,0.20770475,0.03461128,-0.06428871,-0.01330551,0.03377491,-0.01726819,-0.09086953,-0.01821783,0.01102351,-0.04962728,0.01241608,0.0479992,-0.06228918,-0.07528917,-0.00944879,-0.04016916,0.006512,-0.01747995,-0.00574871,-0.01326253,-0.01575375,-0.03970782,-0.09145968,-0.00559631,-0.00298759,-0.03661053,0.04188037,-0.03784237,-0.01148433,-0.06612571,0.02292815,-0.00916229,-0.00704456,-0.01579429,-0.06941664,0.06644414,-0.00570114,-0.00769973,0.00015884,0.0127196,0.00155976,-0.04692817,0.06674731,-0.00695352,-0.1070995,0.02131081,0.01874873,-0.02937376,0.00780585,-0.00808952,0.03647639,-0.05336781,0.04123255,0.05336468,-0.01453972,-0.00146027,-0.00151526,-0.01557192,0.03337302,-0.08460972,-0.17573522,-0.00655934,-0.08350569,-0.04012204,0.02511929,-0.06304417,0.07036637,-0.01259014,0.06144308,0.08625267,0.05961533,-0.10112801,0.06721821,0.07753948,0.00047277,-0.00666244,-0.04358488,-0.04456824,-0.00748482,0.03001758,-0.03745304,0.08997089,-0.04702473,-0.05999532,0.05403297,-0.03651365,0.17247732,0.08757386,0.01815798,0.01925351,0.05169733,0.06113108,-0.00116476,-0.12231503,0.0099581,0.01453723,0.00715157,-0.02301805,-0.01664524,-0.01066739,-0.11672938,0.00096476,-0.00200204,-0.07204708,-0.03425343,0.01491307,-0.02019768,0.02950227,-0.03755534,0.073627,0.06883166,-0.00494893,0.02232135,-0.00143926,0.03916629,-0.01835609,-0.0733132,0.02786439,0.01410896,0.07176576,-0.00390587,-0.03367895,0.02933853,-0.02902483,0.02177467,0.00982703,-0.02045979,-0.01461204,0.05873927,-0.02026556,-0.00862066,0.07304899,-0.01350325,0.02409564,0.0379883,0.04051474,0.04874525,-0.09817892,0.01368976,0.01436637,0.00484958,0.00651882,0.05967663,0.0744957,0.07282766,-0.01057926,-0.00284767,0.0523423,0.01025183,-0.01031797,0.01278445,0.01667421,-0.03804244,0.01441073,0.04788806,0.04143468,-0.22471024,-0.00307366,0.00889276,0.09576711,-0.00698066,-0.01326847,0.01438538,0.00670364,-0.01250717,0.01849722,0.06000026,0.02243569,-0.00382681,-0.04119056,-0.04181186,0.00049203,-0.01570034,-0.03076739,0.11561646,-0.00294177,0.03006536,0.03846442,0.24300461,0.01018079,0.01418867,-0.01182257,0.05046822,0.0476343,-0.07176131,0.00834158,-0.00165315,0.01567639,0.01719714,0.0073494,0.00020814,0.0448183,-0.03490123,-0.00762643,-0.03042911,-0.02457445,-0.12339588,-0.00644709,-0.03909159,0.05435991,0.0929388,0.04798764,0.002221,-0.11001964,0.04492604,0.07494479,-0.0759399,-0.05544627,-0.00193696,-0.03180386,0.01988002,0.04130571,0.01050987,-0.02764898,0.03357086,-0.00928907,0.00892633,0.01279419,0.08150734,0.01702463,0.00064646],"last_embed":{"hash":"qz0e5w","tokens":227}}},"text":null,"length":0,"last_read":{"hash":"qz0e5w","at":1753423626884},"key":"notes/saliu/Steve Players Piracy of Lottery Wonder Grid Systems.md#Steve Players Piracy of Lottery Wonder Grid Systems##III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate#{7}","lines":[111,116],"size":563,"outlinks":[{"title":"Run legitimate lottery software to create lotto systems based on pairs, not Steve Player piracy.","target":"https://forums.saliu.com/HLINE.gif","line":1},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":3},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto last digits.","target":"https://forums.saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
