# src/HighPerformanceCombinationGenerator.jl

module HighPerformanceCombinationGenerator

using Dates
using Statistics
using Printf
using Random
using ..SaliuLottery: Drawing, GameConfiguration, GAME_CONFIGURATIONS
using ..WonderGridEngine: WonderGridConfig, WonderGridError

export GenerationStrategy, GenerationResult, CombinationConfig, StrategySelector,
       create_strategy_selector, generate_combinations_parallel, 
       apply_purge_filter, apply_lie_elimination, evaluate_strategy_performance,
       switch_strategy_dynamically, get_strategy_recommendations

# 生成策略枚舉
@enum StrategyMethod begin
    HOT_NUMBERS      # 熱號策略
    COLD_NUMBERS     # 冷號策略
    BALANCED_MIX     # 平衡混合策略
    PAIR_BASED       # 配對基礎策略
    MARKOV_CHAIN     # 馬可夫鏈策略
    WONDER_GRID      # Wonder Grid策略
    FREQUENCY_BASED  # 頻率基礎策略
    TREND_FOLLOWING  # 趨勢跟隨策略
end

# 生成策略結構
struct GenerationStrategy
    method::StrategyMethod
    filter_settings::Dict{String, Any}
    optimization_level::Int      # 1-5 優化等級
    parallel_processing::Bool    # 並行處理
    memory_efficient::Bool       # 記憶體效率模式
    cache_enabled::Bool          # 快取啟用
    
    function GenerationStrategy(
        method::StrategyMethod = HOT_NUMBERS,
        filter_settings::Dict{String, Any} = Dict{String, Any}(),
        optimization_level::Int = 3,
        parallel_processing::Bool = true,
        memory_efficient::Bool = true,
        cache_enabled::Bool = true
    )
        new(method, filter_settings, optimization_level, 
            parallel_processing, memory_efficient, cache_enabled)
    end
end

# 生成結果結構
struct GenerationResult
    combinations::Vector{Vector{Int}}
    generation_stats::Dict{String, Any}
    filter_efficiency::Float64
    memory_usage::Float64
    execution_time::Float64
    strategy_used::StrategyMethod
    quality_score::Float64
    
    function GenerationResult(
        combinations::Vector{Vector{Int}},
        generation_stats::Dict{String, Any},
        filter_efficiency::Float64,
        memory_usage::Float64,
        execution_time::Float64,
        strategy_used::StrategyMethod,
        quality_score::Float64 = 0.0
    )
        new(combinations, generation_stats, filter_efficiency, 
            memory_usage, execution_time, strategy_used, quality_score)
    end
end

# 組合配置結構
struct CombinationConfig
    max_combinations::Int
    min_quality_score::Float64
    enable_filtering::Bool
    enable_optimization::Bool
    target_coverage::Float64
    diversity_weight::Float64
    
    function CombinationConfig(;
        max_combinations::Int = 1000,
        min_quality_score::Float64 = 0.3,
        enable_filtering::Bool = true,
        enable_optimization::Bool = true,
        target_coverage::Float64 = 0.8,
        diversity_weight::Float64 = 0.3
    )
        new(max_combinations, min_quality_score, enable_filtering,
            enable_optimization, target_coverage, diversity_weight)
    end
end

# 策略選擇器結構
mutable struct StrategySelector
    available_strategies::Vector{GenerationStrategy}
    performance_history::Dict{StrategyMethod, Vector{Float64}}
    current_strategy::GenerationStrategy
    auto_switch_enabled::Bool
    switch_threshold::Float64
    evaluation_window::Int
    
    function StrategySelector(
        strategies::Vector{GenerationStrategy} = create_default_strategies(),
        auto_switch_enabled::Bool = true,
        switch_threshold::Float64 = 0.1,
        evaluation_window::Int = 10
    )
        performance_history = Dict{StrategyMethod, Vector{Float64}}()
        for strategy in strategies
            performance_history[strategy.method] = Float64[]
        end
        
        new(strategies, performance_history, strategies[1], 
            auto_switch_enabled, switch_threshold, evaluation_window)
    end
end

"""
創建策略選擇器
"""
function create_strategy_selector(
    auto_switch::Bool = true,
    switch_threshold::Float64 = 0.1
)::StrategySelector
    
    strategies = create_default_strategies()
    selector = StrategySelector(strategies, auto_switch, switch_threshold)
    
    println("🎯 策略選擇器已創建")
    println("  - 可用策略: $(length(strategies)) 種")
    println("  - 自動切換: $(auto_switch ? "啟用" : "停用")")
    println("  - 切換閾值: $switch_threshold")
    
    return selector
end

"""
並行生成組合
"""
function generate_combinations_parallel(
    strategy::GenerationStrategy,
    data_context::Dict{String, Any},
    config::CombinationConfig = CombinationConfig()
)::GenerationResult
    
    start_time = time()
    println("🚀 開始並行組合生成...")
    println("  - 策略: $(strategy.method)")
    println("  - 並行處理: $(strategy.parallel_processing)")
    println("  - 優化等級: $(strategy.optimization_level)")
    
    try
        # 根據策略方法選擇生成函數
        combinations = if strategy.method == HOT_NUMBERS
            generate_hot_combinations(data_context, config, strategy)
        elseif strategy.method == COLD_NUMBERS
            generate_cold_combinations(data_context, config, strategy)
        elseif strategy.method == BALANCED_MIX
            generate_balanced_combinations(data_context, config, strategy)
        elseif strategy.method == PAIR_BASED
            generate_pair_based_combinations(data_context, config, strategy)
        elseif strategy.method == MARKOV_CHAIN
            generate_markov_combinations(data_context, config, strategy)
        elseif strategy.method == WONDER_GRID
            generate_wonder_grid_combinations(data_context, config, strategy)
        elseif strategy.method == FREQUENCY_BASED
            generate_frequency_combinations(data_context, config, strategy)
        elseif strategy.method == TREND_FOLLOWING
            generate_trend_combinations(data_context, config, strategy)
        else
            generate_default_combinations(data_context, config, strategy)
        end
        
        # 應用過濾器
        filtered_combinations = if config.enable_filtering
            apply_advanced_filters(combinations, strategy, config)
        else
            combinations
        end
        
        # 計算統計信息
        execution_time = time() - start_time
        memory_usage = estimate_memory_usage(filtered_combinations)
        filter_efficiency = length(filtered_combinations) / max(1, length(combinations))
        quality_score = calculate_quality_score(filtered_combinations, data_context)
        
        generation_stats = Dict{String, Any}(
            "original_count" => length(combinations),
            "filtered_count" => length(filtered_combinations),
            "filter_efficiency" => filter_efficiency,
            "generation_method" => string(strategy.method),
            "optimization_level" => strategy.optimization_level,
            "parallel_used" => strategy.parallel_processing
        )
        
        result = GenerationResult(
            filtered_combinations,
            generation_stats,
            filter_efficiency,
            memory_usage,
            execution_time,
            strategy.method,
            quality_score
        )
        
        println("✅ 組合生成完成")
        println("  - 生成數量: $(length(filtered_combinations))")
        println("  - 執行時間: $(round(execution_time, digits=2)) 秒")
        println("  - 記憶體使用: $(round(memory_usage, digits=1)) MB")
        println("  - 品質分數: $(round(quality_score, digits=3))")
        
        return result
        
    catch e
        println("❌ 組合生成失敗: $e")
        throw(WonderGridError("組合生成失敗", :generation_failed, Dict("error" => string(e))))
    end
end

"""
評估策略效能
"""
function evaluate_strategy_performance(
    selector::StrategySelector,
    strategy_method::StrategyMethod,
    performance_score::Float64
)::Nothing
    
    # 記錄效能分數
    if !haskey(selector.performance_history, strategy_method)
        selector.performance_history[strategy_method] = Float64[]
    end
    
    push!(selector.performance_history[strategy_method], performance_score)
    
    # 限制歷史長度
    max_history = selector.evaluation_window * 2
    if length(selector.performance_history[strategy_method]) > max_history
        selector.performance_history[strategy_method] = 
            selector.performance_history[strategy_method][end-max_history+1:end]
    end
    
    println("📊 策略效能已記錄: $(strategy_method) = $(round(performance_score, digits=3))")
    
    return nothing
end

"""
動態切換策略
"""
function switch_strategy_dynamically(
    selector::StrategySelector,
    current_performance::Float64
)::GenerationStrategy
    
    if !selector.auto_switch_enabled
        return selector.current_strategy
    end
    
    current_method = selector.current_strategy.method
    current_history = get(selector.performance_history, current_method, Float64[])
    
    # 檢查是否需要切換
    if length(current_history) >= selector.evaluation_window
        recent_avg = mean(current_history[end-selector.evaluation_window+1:end])
        
        if current_performance < recent_avg - selector.switch_threshold
            # 尋找更好的策略
            best_strategy = find_best_strategy(selector)
            
            if best_strategy.method != current_method
                println("🔄 策略切換: $(current_method) → $(best_strategy.method)")
                selector.current_strategy = best_strategy
                return best_strategy
            end
        end
    end
    
    return selector.current_strategy
end

"""
獲取策略建議
"""
function get_strategy_recommendations(
    selector::StrategySelector,
    data_context::Dict{String, Any}
)::Vector{Dict{String, Any}}
    
    recommendations = Vector{Dict{String, Any}}()
    
    # 分析各策略效能
    for (method, history) in selector.performance_history
        if !isempty(history)
            avg_performance = mean(history)
            recent_performance = length(history) >= 3 ? mean(history[end-2:end]) : avg_performance
            trend = recent_performance - avg_performance
            
            recommendation = Dict{String, Any}(
                "strategy" => string(method),
                "avg_performance" => avg_performance,
                "recent_performance" => recent_performance,
                "trend" => trend,
                "recommendation" => generate_strategy_advice(method, avg_performance, trend),
                "confidence" => calculate_confidence_score(history)
            )
            
            push!(recommendations, recommendation)
        end
    end
    
    # 按效能排序
    sort!(recommendations, by = x -> x["recent_performance"], rev = true)
    
    return recommendations
end

"""
應用Purge過濾器
"""
function apply_purge_filter(
    combinations::Vector{Vector{Int}},
    purge_settings::Dict{String, Any}
)::Vector{Vector{Int}}
    
    if isempty(combinations)
        return combinations
    end
    
    println("🔍 應用Purge過濾器...")
    
    filtered = Vector{Vector{Int}}()
    
    # 獲取過濾設置
    max_consecutive = get(purge_settings, "max_consecutive", 3)
    min_sum = get(purge_settings, "min_sum", 0)
    max_sum = get(purge_settings, "max_sum", 1000)
    odd_even_ratio_min = get(purge_settings, "odd_even_ratio_min", 0.2)
    odd_even_ratio_max = get(purge_settings, "odd_even_ratio_max", 0.8)
    
    for combo in combinations
        # 檢查連續號碼
        if count_consecutive_numbers(combo) <= max_consecutive &&
           # 檢查和值範圍
           min_sum <= sum(combo) <= max_sum &&
           # 檢查奇偶比例
           check_odd_even_ratio(combo, odd_even_ratio_min, odd_even_ratio_max)
            
            push!(filtered, combo)
        end
    end
    
    println("  - 原始數量: $(length(combinations))")
    println("  - 過濾後: $(length(filtered))")
    println("  - 過濾率: $(round((1 - length(filtered)/length(combinations)) * 100, digits=1))%")
    
    return filtered
end

"""
應用LIE消除
"""
function apply_lie_elimination(
    combinations::Vector{Vector{Int}},
    lie_patterns::Vector{Vector{Int}}
)::Vector{Vector{Int}}
    
    if isempty(combinations) || isempty(lie_patterns)
        return combinations
    end
    
    println("🚫 應用LIE消除...")
    
    filtered = Vector{Vector{Int}}()
    
    for combo in combinations
        is_lie = false
        
        for pattern in lie_patterns
            if is_subset_match(pattern, combo)
                is_lie = true
                break
            end
        end
        
        if !is_lie
            push!(filtered, combo)
        end
    end
    
    println("  - 原始數量: $(length(combinations))")
    println("  - 消除後: $(length(filtered))")
    println("  - 消除率: $(round((1 - length(filtered)/length(combinations)) * 100, digits=1))%")
    
    return filtered
end

# 輔助函數
function create_default_strategies()::Vector{GenerationStrategy}
    return [
        GenerationStrategy(HOT_NUMBERS, Dict("temperature" => "high"), 3, true, true, true),
        GenerationStrategy(COLD_NUMBERS, Dict("temperature" => "low"), 3, true, true, true),
        GenerationStrategy(BALANCED_MIX, Dict("balance_ratio" => 0.5), 4, true, true, true),
        GenerationStrategy(PAIR_BASED, Dict("pair_strength" => "strong"), 4, true, true, true),
        GenerationStrategy(WONDER_GRID, Dict("grid_size" => "standard"), 5, true, true, true),
        GenerationStrategy(FREQUENCY_BASED, Dict("frequency_window" => 50), 3, true, true, true),
        GenerationStrategy(TREND_FOLLOWING, Dict("trend_sensitivity" => 0.3), 4, true, true, true)
    ]
end

function find_best_strategy(selector::StrategySelector)::GenerationStrategy
    best_method = HOT_NUMBERS
    best_score = -1.0
    
    for (method, history) in selector.performance_history
        if !isempty(history)
            recent_score = length(history) >= 3 ? mean(history[end-2:end]) : mean(history)
            if recent_score > best_score
                best_score = recent_score
                best_method = method
            end
        end
    end
    
    # 找到對應的策略
    for strategy in selector.available_strategies
        if strategy.method == best_method
            return strategy
        end
    end
    
    return selector.available_strategies[1]  # 默認返回第一個策略
end

function generate_strategy_advice(method::StrategyMethod, avg_performance::Float64, trend::Float64)::String
    if avg_performance > 0.7
        if trend > 0.05
            return "表現優秀且持續改善，建議繼續使用"
        elseif trend < -0.05
            return "表現優秀但有下降趨勢，建議監控"
        else
            return "表現穩定優秀，建議保持"
        end
    elseif avg_performance > 0.5
        if trend > 0.05
            return "表現中等但在改善，可考慮使用"
        elseif trend < -0.05
            return "表現中等且在下降，建議謹慎使用"
        else
            return "表現中等穩定，可作為備選"
        end
    else
        if trend > 0.05
            return "表現較差但在改善，可觀察後決定"
        else
            return "表現較差，不建議使用"
        end
    end
end

function calculate_confidence_score(history::Vector{Float64})::Float64
    if length(history) < 3
        return 0.3
    end
    
    # 基於歷史數據的穩定性計算信心分數
    variance = var(history)
    stability_score = 1.0 / (1.0 + variance)
    sample_size_factor = min(1.0, length(history) / 10.0)
    
    return stability_score * sample_size_factor
end

# 具體生成策略實現
function generate_hot_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    combinations = Vector{Vector{Int}}()
    historical_data = get(data_context, "historical_data", Vector{Drawing}())

    if isempty(historical_data)
        return generate_default_combinations(data_context, config, strategy)
    end

    # 計算號碼頻率
    frequencies = calculate_number_frequencies(historical_data)
    hot_numbers = get_hot_numbers(frequencies, 20)  # 取前20個熱號

    # 生成組合
    game_type = get(data_context, "game_type", :Lotto6_49)
    game_config = GAME_CONFIGURATIONS[game_type]

    for _ in 1:config.max_combinations
        combo = generate_hot_combination(hot_numbers, game_config.numbers_per_draw)
        if !isempty(combo)
            push!(combinations, sort(combo))
        end
    end

    return unique(combinations)
end

function generate_cold_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    combinations = Vector{Vector{Int}}()
    historical_data = get(data_context, "historical_data", Vector{Drawing}())

    if isempty(historical_data)
        return generate_default_combinations(data_context, config, strategy)
    end

    # 計算號碼頻率
    frequencies = calculate_number_frequencies(historical_data)
    cold_numbers = get_cold_numbers(frequencies, 20)  # 取前20個冷號

    # 生成組合
    game_type = get(data_context, "game_type", :Lotto6_49)
    game_config = GAME_CONFIGURATIONS[game_type]

    for _ in 1:config.max_combinations
        combo = generate_cold_combination(cold_numbers, game_config.numbers_per_draw)
        if !isempty(combo)
            push!(combinations, sort(combo))
        end
    end

    return unique(combinations)
end

function generate_balanced_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    combinations = Vector{Vector{Int}}()
    historical_data = get(data_context, "historical_data", Vector{Drawing}())

    game_type = get(data_context, "game_type", :Lotto6_49)
    game_config = GAME_CONFIGURATIONS[game_type]

    if !isempty(historical_data)
        frequencies = calculate_number_frequencies(historical_data)
        hot_numbers = get_hot_numbers(frequencies, 15)
        cold_numbers = get_cold_numbers(frequencies, 15)

        for _ in 1:config.max_combinations
            combo = generate_balanced_combination(hot_numbers, cold_numbers, game_config.numbers_per_draw)
            if !isempty(combo)
                push!(combinations, sort(combo))
            end
        end
    else
        # 沒有歷史數據時使用隨機平衡策略
        for _ in 1:config.max_combinations
            combo = generate_random_balanced_combination(game_config)
            push!(combinations, sort(combo))
        end
    end

    return unique(combinations)
end

function generate_pair_based_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    combinations = Vector{Vector{Int}}()
    historical_data = get(data_context, "historical_data", Vector{Drawing}())

    if isempty(historical_data)
        return generate_default_combinations(data_context, config, strategy)
    end

    # 計算配對頻率
    pair_frequencies = calculate_pair_frequencies(historical_data)
    top_pairs = get_top_pairs(pair_frequencies, 50)

    game_type = get(data_context, "game_type", :Lotto6_49)
    game_config = GAME_CONFIGURATIONS[game_type]

    for _ in 1:config.max_combinations
        combo = generate_pair_combination(top_pairs, game_config.numbers_per_draw, game_config.max_number)
        if !isempty(combo)
            push!(combinations, sort(combo))
        end
    end

    return unique(combinations)
end

function generate_wonder_grid_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    # 使用現有的 Wonder Grid 生成邏輯
    historical_data = get(data_context, "historical_data", Vector{Drawing}())
    wonder_config = get(data_context, "wonder_config", WonderGridConfig())

    if isempty(historical_data)
        return generate_default_combinations(data_context, config, strategy)
    end

    try
        # 這裡應該調用 WonderGridEngine 的生成函數
        # 為了簡化，我們生成一些基於 Wonder Grid 概念的組合
        combinations = Vector{Vector{Int}}()

        game_type = get(data_context, "game_type", :Lotto6_49)
        game_config = GAME_CONFIGURATIONS[game_type]

        # 簡化的 Wonder Grid 實現
        for _ in 1:min(config.max_combinations, 100)
            combo = generate_wonder_style_combination(historical_data, game_config)
            if !isempty(combo)
                push!(combinations, sort(combo))
            end
        end

        return unique(combinations)

    catch e
        println("⚠️  Wonder Grid 生成失敗，使用默認策略: $e")
        return generate_default_combinations(data_context, config, strategy)
    end
end

function generate_frequency_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    return generate_hot_combinations(data_context, config, strategy)  # 頻率策略類似熱號策略
end

function generate_trend_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    combinations = Vector{Vector{Int}}()
    historical_data = get(data_context, "historical_data", Vector{Drawing}())

    if length(historical_data) < 10
        return generate_default_combinations(data_context, config, strategy)
    end

    # 分析趨勢
    recent_data = historical_data[1:min(20, length(historical_data))]
    trend_numbers = analyze_trend_numbers(recent_data)

    game_type = get(data_context, "game_type", :Lotto6_49)
    game_config = GAME_CONFIGURATIONS[game_type]

    for _ in 1:config.max_combinations
        combo = generate_trend_combination(trend_numbers, game_config.numbers_per_draw, game_config.max_number)
        if !isempty(combo)
            push!(combinations, sort(combo))
        end
    end

    return unique(combinations)
end

function generate_markov_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    # 簡化的馬可夫鏈實現
    return generate_trend_combinations(data_context, config, strategy)
end

function generate_default_combinations(
    data_context::Dict{String, Any},
    config::CombinationConfig,
    strategy::GenerationStrategy
)::Vector{Vector{Int}}

    combinations = Vector{Vector{Int}}()
    game_type = get(data_context, "game_type", :Lotto6_49)
    game_config = GAME_CONFIGURATIONS[game_type]

    for _ in 1:config.max_combinations
        combo = sort(rand(1:game_config.max_number, game_config.numbers_per_draw))
        push!(combinations, combo)
    end

    return unique(combinations)
end

# 輔助計算函數
function calculate_number_frequencies(historical_data::Vector{Drawing})::Dict{Int, Int}
    frequencies = Dict{Int, Int}()

    for drawing in historical_data
        for number in drawing.numbers
            frequencies[number] = get(frequencies, number, 0) + 1
        end
    end

    return frequencies
end

function calculate_pair_frequencies(historical_data::Vector{Drawing})::Dict{Tuple{Int,Int}, Int}
    frequencies = Dict{Tuple{Int,Int}, Int}()

    for drawing in historical_data
        numbers = drawing.numbers
        for i in 1:length(numbers)
            for j in (i+1):length(numbers)
                pair = (min(numbers[i], numbers[j]), max(numbers[i], numbers[j]))
                frequencies[pair] = get(frequencies, pair, 0) + 1
            end
        end
    end

    return frequencies
end

function get_hot_numbers(frequencies::Dict{Int, Int}, count::Int)::Vector{Int}
    sorted_numbers = sort(collect(keys(frequencies)), by = x -> frequencies[x], rev = true)
    return sorted_numbers[1:min(count, length(sorted_numbers))]
end

function get_cold_numbers(frequencies::Dict{Int, Int}, count::Int)::Vector{Int}
    sorted_numbers = sort(collect(keys(frequencies)), by = x -> frequencies[x])
    return sorted_numbers[1:min(count, length(sorted_numbers))]
end

function get_top_pairs(frequencies::Dict{Tuple{Int,Int}, Int}, count::Int)::Vector{Tuple{Int,Int}}
    sorted_pairs = sort(collect(keys(frequencies)), by = x -> frequencies[x], rev = true)
    return sorted_pairs[1:min(count, length(sorted_pairs))]
end

function generate_hot_combination(hot_numbers::Vector{Int}, needed::Int)::Vector{Int}
    if length(hot_numbers) < needed
        return Int[]
    end

    # 隨機選擇熱號，但偏向頻率更高的號碼
    weights = [1.0 / (i + 1) for i in 0:(length(hot_numbers)-1)]
    selected = Int[]

    while length(selected) < needed && !isempty(hot_numbers)
        # 加權隨機選擇
        idx = weighted_random_select(weights)
        if idx <= length(hot_numbers)
            number = hot_numbers[idx]
            if !(number in selected)
                push!(selected, number)
            end
        end
    end

    return selected
end

function generate_cold_combination(cold_numbers::Vector{Int}, needed::Int)::Vector{Int}
    if length(cold_numbers) < needed
        return Int[]
    end

    return cold_numbers[1:needed]
end

function generate_balanced_combination(hot_numbers::Vector{Int}, cold_numbers::Vector{Int}, needed::Int)::Vector{Int}
    combo = Int[]
    hot_count = needed ÷ 2
    cold_count = needed - hot_count

    # 選擇熱號
    for i in 1:min(hot_count, length(hot_numbers))
        push!(combo, hot_numbers[i])
    end

    # 選擇冷號
    for i in 1:min(cold_count, length(cold_numbers))
        if !(cold_numbers[i] in combo)
            push!(combo, cold_numbers[i])
        end
    end

    # 如果不夠，隨機補充
    while length(combo) < needed
        number = rand(1:49)  # 假設最大號碼是49
        if !(number in combo)
            push!(combo, number)
        end
    end

    return combo
end

function weighted_random_select(weights::Vector{Float64})::Int
    total = sum(weights)
    r = rand() * total
    cumsum = 0.0

    for (i, weight) in enumerate(weights)
        cumsum += weight
        if r <= cumsum
            return i
        end
    end

    return length(weights)
end

function apply_advanced_filters(
    combinations::Vector{Vector{Int}},
    strategy::GenerationStrategy,
    config::CombinationConfig
)::Vector{Vector{Int}}

    if !config.enable_filtering
        return combinations
    end

    filtered = combinations

    # 應用基本過濾器
    if haskey(strategy.filter_settings, "purge")
        filtered = apply_purge_filter(filtered, strategy.filter_settings["purge"])
    end

    # 應用品質過濾器
    if config.min_quality_score > 0
        filtered = filter(combo -> calculate_combination_quality(combo) >= config.min_quality_score, filtered)
    end

    return filtered
end

function calculate_combination_quality(combination::Vector{Int})::Float64
    # 簡化的品質計算
    if isempty(combination)
        return 0.0
    end

    # 檢查分佈均勻性
    distribution_score = calculate_distribution_score(combination)

    # 檢查奇偶平衡
    odd_even_score = calculate_odd_even_score(combination)

    # 檢查和值合理性
    sum_score = calculate_sum_score(combination)

    return (distribution_score + odd_even_score + sum_score) / 3.0
end

function calculate_distribution_score(combination::Vector{Int})::Float64
    if length(combination) < 2
        return 0.5
    end

    # 計算號碼間距的標準差
    sorted_combo = sort(combination)
    gaps = [sorted_combo[i+1] - sorted_combo[i] for i in 1:(length(sorted_combo)-1)]

    if isempty(gaps)
        return 0.5
    end

    gap_std = std(gaps)
    ideal_gap = 49 / length(combination)  # 假設最大號碼是49

    # 標準差越接近理想值，分數越高
    score = 1.0 - abs(gap_std - ideal_gap) / ideal_gap
    return clamp(score, 0.0, 1.0)
end

function calculate_odd_even_score(combination::Vector{Int})::Float64
    odd_count = count(x -> x % 2 == 1, combination)
    even_count = length(combination) - odd_count

    # 理想比例是接近 50:50
    ideal_ratio = 0.5
    actual_ratio = odd_count / length(combination)

    score = 1.0 - abs(actual_ratio - ideal_ratio) * 2
    return clamp(score, 0.0, 1.0)
end

function calculate_sum_score(combination::Vector{Int})::Float64
    total_sum = sum(combination)

    # 假設理想和值範圍（基於6/49遊戲）
    min_ideal = 90
    max_ideal = 200

    if min_ideal <= total_sum <= max_ideal
        return 1.0
    elseif total_sum < min_ideal
        return max(0.0, 1.0 - (min_ideal - total_sum) / min_ideal)
    else
        return max(0.0, 1.0 - (total_sum - max_ideal) / max_ideal)
    end
end

function estimate_memory_usage(combinations::Vector{Vector{Int}})::Float64
    # 估算記憶體使用量（MB）
    if isempty(combinations)
        return 0.0
    end

    # 每個整數約4字節，每個向量有額外開銷
    bytes_per_int = 4
    vector_overhead = 24  # 估算的向量開銷

    total_numbers = sum(length(combo) for combo in combinations)
    total_bytes = total_numbers * bytes_per_int + length(combinations) * vector_overhead

    return total_bytes / (1024 * 1024)  # 轉換為MB
end

function calculate_quality_score(combinations::Vector{Vector{Int}}, data_context::Dict{String, Any})::Float64
    if isempty(combinations)
        return 0.0
    end

    # 計算平均品質分數
    quality_scores = [calculate_combination_quality(combo) for combo in combinations]
    avg_quality = mean(quality_scores)

    # 計算多樣性分數
    diversity_score = calculate_diversity_score(combinations)

    # 綜合分數
    return 0.7 * avg_quality + 0.3 * diversity_score
end

function calculate_diversity_score(combinations::Vector{Vector{Int}})::Float64
    if length(combinations) < 2
        return 1.0
    end

    # 計算組合間的平均差異度
    total_similarity = 0.0
    comparison_count = 0

    for i in 1:length(combinations)
        for j in (i+1):length(combinations)
            similarity = calculate_combination_similarity(combinations[i], combinations[j])
            total_similarity += similarity
            comparison_count += 1
        end
    end

    avg_similarity = comparison_count > 0 ? total_similarity / comparison_count : 0.0
    return 1.0 - avg_similarity  # 相似度越低，多樣性越高
end

function calculate_combination_similarity(combo1::Vector{Int}, combo2::Vector{Int})::Float64
    if isempty(combo1) || isempty(combo2)
        return 0.0
    end

    common_numbers = length(intersect(combo1, combo2))
    max_length = max(length(combo1), length(combo2))

    return common_numbers / max_length
end

# 更多輔助函數
function count_consecutive_numbers(combination::Vector{Int})::Int
    if length(combination) < 2
        return 0
    end

    sorted_combo = sort(combination)
    max_consecutive = 1
    current_consecutive = 1

    for i in 2:length(sorted_combo)
        if sorted_combo[i] == sorted_combo[i-1] + 1
            current_consecutive += 1
            max_consecutive = max(max_consecutive, current_consecutive)
        else
            current_consecutive = 1
        end
    end

    return max_consecutive
end

function check_odd_even_ratio(combination::Vector{Int}, min_ratio::Float64, max_ratio::Float64)::Bool
    odd_count = count(x -> x % 2 == 1, combination)
    ratio = odd_count / length(combination)
    return min_ratio <= ratio <= max_ratio
end

function is_subset_match(pattern::Vector{Int}, combination::Vector{Int})::Bool
    return all(num -> num in combination, pattern)
end

function generate_random_balanced_combination(game_config::GameConfiguration)::Vector{Int}
    combo = Int[]
    max_number = game_config.max_number
    needed = game_config.numbers_per_draw

    # 嘗試平衡奇偶數
    odd_needed = needed ÷ 2
    even_needed = needed - odd_needed

    # 選擇奇數
    odd_numbers = collect(1:2:max_number)
    for _ in 1:odd_needed
        if !isempty(odd_numbers)
            idx = rand(1:length(odd_numbers))
            push!(combo, odd_numbers[idx])
            deleteat!(odd_numbers, idx)
        end
    end

    # 選擇偶數
    even_numbers = collect(2:2:max_number)
    for _ in 1:even_needed
        if !isempty(even_numbers)
            idx = rand(1:length(even_numbers))
            push!(combo, even_numbers[idx])
            deleteat!(even_numbers, idx)
        end
    end

    return combo
end

function generate_pair_combination(top_pairs::Vector{Tuple{Int,Int}}, needed::Int, max_number::Int)::Vector{Int}
    combo = Int[]
    used_numbers = Set{Int}()

    # 從頂級配對中選擇
    for pair in top_pairs
        if length(combo) >= needed
            break
        end

        num1, num2 = pair
        if !(num1 in used_numbers) && !(num2 in used_numbers)
            push!(combo, num1)
            push!(combo, num2)
            push!(used_numbers, num1)
            push!(used_numbers, num2)
        end
    end

    # 如果不夠，隨機補充
    while length(combo) < needed
        number = rand(1:max_number)
        if !(number in used_numbers)
            push!(combo, number)
            push!(used_numbers, number)
        end
    end

    return combo[1:needed]
end

function generate_wonder_style_combination(historical_data::Vector{Drawing}, game_config::GameConfiguration)::Vector{Int}
    # 簡化的 Wonder Grid 風格生成
    if isempty(historical_data)
        return sort(rand(1:game_config.max_number, game_config.numbers_per_draw))
    end

    # 分析最近的配對
    recent_data = historical_data[1:min(20, length(historical_data))]
    pair_freq = calculate_pair_frequencies(recent_data)

    if isempty(pair_freq)
        return sort(rand(1:game_config.max_number, game_config.numbers_per_draw))
    end

    # 選擇一個高頻配對作為基礎
    top_pairs = get_top_pairs(pair_freq, 10)
    if !isempty(top_pairs)
        base_pair = top_pairs[1]
        combo = [base_pair[1], base_pair[2]]

        # 補充其他號碼
        while length(combo) < game_config.numbers_per_draw
            number = rand(1:game_config.max_number)
            if !(number in combo)
                push!(combo, number)
            end
        end

        return combo
    end

    return sort(rand(1:game_config.max_number, game_config.numbers_per_draw))
end

function analyze_trend_numbers(recent_data::Vector{Drawing})::Vector{Int}
    if isempty(recent_data)
        return Int[]
    end

    # 分析最近出現頻率上升的號碼
    frequencies = calculate_number_frequencies(recent_data)

    # 簡化：返回最近頻率較高的號碼
    sorted_numbers = sort(collect(keys(frequencies)), by = x -> frequencies[x], rev = true)
    return sorted_numbers[1:min(20, length(sorted_numbers))]
end

function generate_trend_combination(trend_numbers::Vector{Int}, needed::Int, max_number::Int)::Vector{Int}
    combo = Int[]

    # 優先選擇趨勢號碼
    trend_count = min(needed ÷ 2, length(trend_numbers))
    for i in 1:trend_count
        push!(combo, trend_numbers[i])
    end

    # 隨機補充其他號碼
    while length(combo) < needed
        number = rand(1:max_number)
        if !(number in combo)
            push!(combo, number)
        end
    end

    return combo
end

end # module HighPerformanceCombinationGenerator
