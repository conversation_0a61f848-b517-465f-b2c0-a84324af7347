
"smart_sources:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md": {"path":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06787887,-0.05485712,-0.00405006,-0.05788785,-0.04089828,0.05088799,-0.04867647,0.00325553,0.06421589,-0.00971632,0.03491637,-0.01303161,0.04722643,-0.00653915,-0.04623654,-0.01446905,-0.03312862,-0.04902266,-0.07143595,0.01045,0.13045029,-0.05395598,-0.03250075,-0.09849724,0.10426982,0.02034173,-0.0543138,-0.06272889,-0.03276836,-0.23260972,0.02849758,0.05204588,0.03660601,-0.02954258,-0.04216999,-0.03471009,-0.01483135,0.04539614,-0.0423983,0.00878563,0.01110068,-0.00428424,-0.00012897,-0.02344628,0.03325883,-0.05282649,-0.03345433,0.02806706,0.0567432,0.02710905,-0.02653753,-0.01379399,-0.0098038,0.05316958,0.03957654,0.00442443,0.04703289,0.04993449,0.03373747,0.06501517,0.04511768,0.06282176,-0.17801628,0.01888488,0.02673008,0.04796994,0.0104156,-0.02895991,-0.00395394,0.0206631,0.02088893,0.02809804,-0.02624409,0.01630493,0.03332812,-0.05963349,-0.05593609,-0.00327588,-0.06393151,0.0247573,-0.05664547,-0.02632701,-0.02987497,-0.01355816,-0.00577685,0.02963374,0.05036022,0.0381348,0.10780276,-0.05400789,0.00873836,0.02558006,0.06691446,0.0524325,0.00337434,0.02749984,0.05786026,-0.00541661,0.01799808,0.10683935,0.00682404,-0.01094689,-0.01562327,0.00417417,0.03400035,-0.04621389,0.00399151,-0.04558757,-0.01405927,0.01942907,0.02914958,-0.00150372,0.02898452,-0.08567063,-0.02609465,-0.06765598,0.0086825,-0.02514436,0.00541795,0.01773987,-0.0510719,0.02207026,0.02340937,0.00886072,0.0330907,0.02686864,0.02744072,0.0557636,0.0537761,0.04654402,0.04253925,0.02400255,-0.14454368,-0.04243555,-0.00061255,-0.0094109,0.05734307,-0.00558654,0.02610876,0.03338856,-0.02025506,-0.01033848,0.04171921,-0.07802166,-0.02642719,0.06681545,0.02941296,-0.04610319,0.04791418,-0.01777499,-0.01178904,-0.00642631,-0.03719382,-0.07520006,-0.00688401,0.0523519,0.12727366,0.06207135,-0.06556248,-0.01800461,0.00296501,-0.05002433,-0.01386625,0.09068479,0.00528759,-0.11991622,-0.00007081,0.05626681,-0.01861288,-0.11313137,-0.03152324,-0.00470615,-0.0352447,0.00005591,0.08246803,-0.01358879,-0.04195183,-0.05035992,0.00865461,0.00120812,-0.00670209,-0.01135922,-0.01453995,0.01212421,-0.07518044,-0.07444272,-0.0292841,-0.02885515,-0.02598528,0.03749212,-0.03627283,-0.00640584,-0.07113709,0.00205456,-0.01385294,-0.02512742,-0.03036567,-0.03184521,0.05969185,-0.03877422,-0.02796836,-0.05010369,0.04814113,0.0013004,0.03129362,0.05496487,-0.02436639,-0.04241639,0.08733484,0.00466461,-0.02783708,-0.0104278,0.03563602,0.07775882,-0.10772856,0.05570305,0.01975939,0.01785736,0.01880489,0.02909788,-0.03606136,0.02641843,-0.09380531,-0.19231923,-0.0475643,-0.02373628,0.00292729,-0.00985599,-0.03213499,0.04535741,0.027418,0.00359255,0.07442424,0.11117005,-0.05022736,-0.04082123,-0.03206273,-0.05316676,-0.05135668,-0.10709671,-0.03108593,-0.02488602,0.06433684,-0.02121661,0.00809508,0.00427683,-0.04298202,-0.01295201,-0.05729227,0.14569516,0.04357724,-0.01241008,-0.01556306,0.06868284,-0.00780438,-0.03437411,-0.05250409,-0.01629732,0.01762719,-0.0312074,0.01382189,-0.07411885,-0.01608739,-0.08404534,-0.0078004,-0.00249699,-0.07396119,-0.0293656,0.01706025,0.00549052,0.00869663,0.01857651,0.0733149,-0.00196983,-0.03006753,0.06034382,0.03222297,0.05209444,-0.02424462,-0.04547013,0.00069822,-0.00052426,0.06245268,0.00774983,-0.0373386,0.03327302,-0.02973315,0.05326297,0.03206675,0.00353744,-0.03073679,0.03424143,-0.00740897,0.00654575,0.11635733,0.01543937,0.0264496,0.01641655,0.04862326,0.07684559,-0.0274998,0.00231803,-0.03183232,-0.02879041,-0.07602072,0.03716647,0.07912586,0.05637404,0.06796719,0.04706572,0.0326066,-0.01392266,0.00982847,-0.00794283,0.02397859,-0.02299179,0.01446839,0.0131515,0.05877526,-0.24216354,0.03088768,-0.01787666,0.06472203,0.0053082,-0.01290592,0.049073,-0.00438289,0.00986347,-0.008202,0.07082319,0.02727258,0.04264057,-0.08320772,0.01468388,-0.03182236,0.03301862,-0.0195118,0.05716559,0.01694353,0.08796255,0.02433978,0.22752285,0.02686987,-0.0240634,0.03620998,0.02467749,0.00316304,0.04285279,0.04277936,-0.02091009,-0.01696043,0.06459688,-0.00283243,-0.03246025,0.03147506,-0.02646144,0.01159389,0.00672481,0.03757223,-0.03093022,-0.00698566,-0.03993966,0.00022523,0.12468422,0.02699507,-0.06257365,-0.09038296,0.06180993,0.0319609,-0.08117259,-0.04894819,-0.06346617,0.00198364,0.0130482,0.03620637,-0.00187686,0.03010485,0.00757284,-0.02554453,0.01064757,-0.00730062,0.03845224,0.04817832,-0.02021381],"last_embed":{"hash":"1lsinj4","tokens":492}}},"last_read":{"hash":"1lsinj4","at":1753423497175},"class_name":"SmartSource","last_import":{"mtime":1753363611081,"size":12084,"at":1753423416052,"hash":"1lsinj4"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Sums, Lotto Root-Sum, Charts, Software":[8,105],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{1}":[10,25],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{2}":[26,27],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{3}":[28,28],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{4}":[29,29],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{5}":[30,31],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{6}":[32,39],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{7}":[40,40],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{8}":[41,41],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{9}":[42,42],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{10}":[43,43],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{11}":[44,44],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{12}":[45,45],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{13}":[46,46],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{14}":[47,47],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{15}":[48,48],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{16}":[49,49],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{17}":[50,50],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{18}":[51,51],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{19}":[52,52],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{20}":[53,53],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{21}":[54,54],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{22}":[55,55],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{23}":[56,56],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{24}":[57,58],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{25}":[59,62],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{26}":[63,64],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{27}":[65,70],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{28}":[71,72],"#Lottery Sums, Lotto Root-Sum, Charts, Software#{29}":[73,80],"#Lottery Sums, Lotto Root-Sum, Charts, Software#[<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>](https://saliu.com/content/lottery.html)":[81,105],"#Lottery Sums, Lotto Root-Sum, Charts, Software#[<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>](https://saliu.com/content/lottery.html)#{1}":[83,105]},"outlinks":[{"title":"_**Software to Calculate Lottery Sums, Lotto Sum-Totals, Pick Digit Lotteries, Powerball Mega Millions**_","target":"https://saliu.com/bbs/messages/626.html","line":28},{"title":"_**Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_","target":"https://saliu.com/strategy.html","line":30},{"title":"Lotto sums software has four functions for most type of lottery games, from pick 2 to Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-sums-combinations.gif","line":32},{"title":"**Lottery deltas**","target":"https://saliu.com/delta-lotto-software.html","line":34},{"title":"_**Pick 2 Lottery Sums, Root Sum Chart**_","target":"https://saliu.com/forum/pick-2-lottery-sums-chart.html","line":40},{"title":"_**Pick 3 Lottery Sum-Total, Root Sums Chart**_","target":"https://saliu.com/forum/pick3-sums.html","line":41},{"title":"_**Pick 4 Lottery Sum-Total, Root Sum Charts**_","target":"https://saliu.com/forum/pick4-sums.html","line":42},{"title":"_**Pick 5 (formerly _Quinto_ in Pennsylvania) Lottery Root, Sums Chart**_","target":"https://saliu.com/forum/quinto-sums.html","line":43},{"title":"_**Lotto 5/39 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto539-sums.html","line":44},{"title":"_**Lotto 5/43 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto543-sums.html","line":45},{"title":"_**Lotto 6/49 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto649-sums.html","line":46},{"title":"_**Lotto 6/51 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto651-sums.html","line":47},{"title":"_**Lotto 6/54 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto654-sums.html","line":48},{"title":"_**Lotto 6/59 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto659-sums.html","line":49},{"title":"_**Euromillions Sums, Root Sum Chart**_","target":"https://saliu.com/forum/euromillions-sums.html","line":50},{"title":"_**Euromillions 5/50 & 2/11 Sum-Totals**_","target":"https://saliu.com/forum/euromillions-sums50211.html","line":51},{"title":"_**Mega Millions Sums, Root Sum Chart**_","target":"https://saliu.com/forum/megamillions-sums.html","line":52},{"title":"_**Sums, Sum Chart: <u>5/75 &amp; 1/15</u> Sum-Totals**_","target":"https://saliu.com/forum/megamillions-sums-75-15.html","line":53},{"title":"_**Powerball Sums, Root Sum Chart**_","target":"https://saliu.com/forum/powerball-sums.html","line":54},{"title":"_**Powerball 5/59 & 1/35 Sum-Totals**_","target":"https://saliu.com/forum/powerball-sums-59-35.html","line":55},{"title":"_**SuperLotto-Plus Sums, Root Sum Chart**_","target":"https://saliu.com/forum/superlotto-sums.html","line":56},{"title":"_**Thunderball Sums, Root Sum Chart**_","target":"https://saliu.com/forum/thunderball-sums.html","line":57},{"title":"Analyze a Powerball or Megamillion results file to show sums, average, standard deviation, delta.","target":"https://saliu.com/ScreenImgs/sum-lottery-draws.gif","line":61},{"title":"Read or print the statistics of sums for Powerball, Mega Millions, Euromillions lottery games.","target":"https://saliu.com/ScreenImgs/sum-stats.gif","line":65},{"title":"_**lotto deltas**_","target":"https://saliu.com/bbs/messages/648.html","line":67},{"title":"Read or print the statistics of sums for regular lotto games like 6 from 49.","target":"https://saliu.com/ScreenImgs/lotto-sums-stats.gif","line":69},{"title":"_**1 2 3 4 5 6 Lotto Combination Probability, Odds**_","target":"https://saliu.com/bbs/messages/961.html","line":71},{"title":"Lottery sums software calculates sum-totals in any lotto game and generates the combinations.","target":"https://saliu.com/forum/HLINE.gif","line":77},{"title":"_**Back to index: Forum Lotto, Lottery, Sums, Wheeling, Software**_","target":"https://saliu.com/forum/index.html","line":79},{"title":"<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>","target":"https://saliu.com/content/lottery.html","line":81},{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":83},{"title":"_**Lotto Lottery Software Systems**_","target":"https://saliu.com/LottoWin.htm","line":84},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":86},{"title":"_Users' Guide to_ _**MDIEditor And Lotto WE**_","target":"https://saliu.com/MDI-lotto-guide.html","line":88},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":90},{"title":"**Standard Deviation**","target":"https://saliu.com/deviation.html","line":91},{"title":"_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd/Even; Low/High Numbers**_","target":"https://saliu.com/strategy.html","line":92},{"title":"_**Software to Calculate Lottery Sums, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":93},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":94},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":95},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":96},{"title":"_**Lottery: Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":97},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":98},{"title":"Download all root sums for pick 2 3 4 5 lotteries, lotto, Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/forum/HLINE.gif","line":100},{"title":"Forums","target":"https://forums.saliu.com/","line":102},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":102},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":102},{"title":"Contents","target":"https://saliu.com/content/index.html","line":102},{"title":"Help","target":"https://saliu.com/Help.htm","line":102},{"title":"Home","target":"https://saliu.com/index.htm","line":102},{"title":"Software","target":"https://saliu.com/infodown.html","line":102},{"title":"Search","target":"https://saliu.com/Search.htm","line":102},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":102},{"title":"View root sums, sum-totals in lottery lotto: Powerball, Mega Millions, Euromillions, Pick-5 Quinto.","target":"https://saliu.com/forum/HLINE.gif","line":104}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lottery","lotto","sum","sums","sum-totals","software","lottery drawings","pick digit lotteries","lotto-5 6","Powerball","Mega Millions","Euromillions","chart"],"source":"https://saliu.com/forum/lottery-sums.html","author":null}},"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09146249,-0.06993164,-0.00256944,-0.04524725,-0.03718815,0.06444869,-0.0477407,0.02453772,0.06050415,-0.00545915,0.03407055,-0.04531892,0.03543409,0.00473419,-0.02388398,-0.01555183,-0.02750072,-0.04519229,-0.06454705,0.01184129,0.14736812,-0.06743057,-0.04513203,-0.1028273,0.10908902,0.00506655,-0.0232992,-0.04023789,-0.01603161,-0.18448322,0.02146073,0.05310927,0.02315507,-0.0356593,-0.04576479,-0.04127211,-0.03144538,0.05783496,-0.00813878,0.0304053,0.02607122,0.00118368,-0.0326306,-0.03725932,0.03469778,-0.06847085,-0.02702038,0.0075093,0.06136961,0.03573319,-0.01069157,-0.0242831,-0.00215703,0.07111283,0.07638149,0.00106401,0.07379051,0.01509052,0.04799065,0.05496113,0.02785189,0.05726439,-0.21558751,0.02658611,0.02656113,0.03993075,0.00660276,-0.00933965,-0.02202674,0.02773044,0.0387287,0.04109776,-0.02387906,0.00980718,0.02397544,-0.02617485,-0.06230793,-0.02934078,-0.05591865,0.00301031,-0.05776649,-0.01000773,-0.04492593,-0.02918927,-0.00994977,0.03677773,0.05471939,0.0617143,0.10435437,-0.03362063,0.0053742,-0.00769695,0.07830899,0.06017792,-0.02683671,0.04106237,0.04435758,-0.00902319,0.02216906,0.11720977,0.00206807,0.02589069,-0.01749713,-0.00109396,0.04508804,-0.02484451,-0.00321625,-0.03899055,-0.00893265,0.0135587,0.03274221,0.01307423,0.04209654,-0.08870901,0.00355951,-0.06696641,0.01457387,-0.01827563,0.01124547,0.02070968,-0.05649223,0.00725211,0.02615726,0.0098159,0.03392224,0.05072766,0.04160128,0.05716279,0.03150928,0.06877344,0.0351944,0.02571199,-0.1261428,-0.03441222,0.00330966,0.0050874,0.06791096,-0.03045675,0.01404859,0.01138112,-0.01975846,-0.04313284,0.0149336,-0.08467692,-0.01984486,0.06913856,0.00810783,-0.04157102,0.05813936,-0.01357139,-0.04060435,0.00560053,0.00299122,-0.06833353,-0.01626377,0.05839127,0.11305174,0.08175887,-0.0428497,-0.00444191,0.01908302,-0.06547677,-0.04015794,0.11392237,-0.00059751,-0.13643605,-0.00368067,0.05361643,-0.00826558,-0.10572515,-0.02809461,-0.00776992,-0.02637211,-0.00324702,0.11630694,-0.00887644,0.03494064,-0.05471691,-0.04807443,-0.00012454,-0.03397186,-0.00549884,-0.04608036,0.02366671,-0.07816557,-0.05060025,-0.01651027,-0.04900528,0.00559754,0.03829132,-0.03469073,-0.02973968,-0.07287765,0.00983768,-0.00980578,-0.02197645,-0.03866824,-0.02207911,0.04847435,-0.03936917,0.00933061,-0.02115778,0.02498894,-0.01747816,0.03132357,0.04878018,-0.03469399,-0.02840072,0.06018888,0.01042264,-0.0038119,0.000139,0.07637057,0.06707096,-0.10589435,0.03353577,0.01076326,0.00652262,0.02552405,0.01863613,-0.03542424,0.00831923,-0.11808191,-0.19684282,-0.03081363,0.00182189,-0.00324467,-0.01130481,-0.02726449,0.05178522,0.02051424,0.03923059,0.06598552,0.09711085,-0.04130279,-0.02644053,-0.03156583,-0.04983087,-0.0397425,-0.07396057,-0.04573688,0.00514185,0.0720178,-0.00238799,0.01266647,0.00633158,-0.05085654,0.01361334,-0.04019105,0.14000449,0.07182226,-0.04944284,-0.0233955,0.09317508,0.01263159,-0.02951579,-0.0592595,-0.02257109,-0.00116368,-0.05676719,0.03365578,-0.08184183,-0.03623541,-0.071017,0.00542879,-0.0065735,-0.07691918,-0.01267575,-0.00376378,0.01367242,-0.01667076,0.01023819,0.03689577,0.01707819,-0.01769908,0.05259963,0.02671727,0.06314551,-0.01290273,-0.04987251,-0.02769058,-0.00274604,0.03964015,0.00002027,-0.03645606,0.02531041,-0.02547741,0.01376933,0.0182033,0.00263833,-0.05092084,-0.01151181,-0.00308612,0.02601611,0.08609361,0.02959321,0.01399227,-0.00383312,0.01921392,0.05140429,-0.01045803,0.02154969,-0.02650043,-0.02612231,-0.0766829,0.02320904,0.0632596,0.05306828,0.08265434,0.03399521,0.0079775,-0.01201833,0.0077252,-0.0227019,0.02214819,-0.03272401,0.00641972,0.02494235,0.03850444,-0.27007708,0.05425147,0.00004592,0.04746115,0.02533641,0.00633073,0.03737333,-0.01544498,-0.02531458,0.01012102,0.0749436,0.04439381,0.03191968,-0.06903002,-0.00501122,-0.00243967,0.00616486,-0.02977637,0.0440636,0.02605465,0.0612299,0.02102088,0.22343975,0.03855953,-0.03525398,0.02092113,0.00687388,-0.01037971,0.03911978,0.05470282,-0.01348307,-0.00936238,0.06854764,0.00682065,-0.03334992,0.05574431,-0.03421351,0.00011041,0.00538344,0.02972163,-0.05066798,0.00478541,-0.03610666,0.01133216,0.10035326,0.01836615,-0.07032035,-0.07124944,0.06204702,0.02856824,-0.0637878,-0.05106848,-0.06588475,0.01616849,0.00114099,0.05766322,0.01683528,0.01208286,0.01678394,-0.03449939,-0.00051855,0.00708097,0.02527716,0.00342956,-0.0003749],"last_embed":{"hash":"ucclzq","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"ucclzq","at":1753423496327},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#---frontmatter---","lines":[1,6],"size":251,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06626959,-0.05127833,0.00564862,-0.06409752,-0.0353223,0.04973637,-0.02386759,-0.00724771,0.05998953,-0.01398304,0.03580874,-0.01263183,0.04395071,-0.00430148,-0.04362346,-0.00361847,-0.02532679,-0.05340891,-0.06762722,0.01151369,0.11751816,-0.04828792,-0.03745445,-0.08426441,0.10382627,0.01561939,-0.06331246,-0.06619944,-0.04333853,-0.23442566,0.03531171,0.0439577,0.03684247,-0.02774119,-0.03794031,-0.03607127,-0.01551847,0.05040122,-0.04857844,0.00517006,0.02121954,-0.00478037,0.00409755,-0.03241291,0.03006361,-0.0547457,-0.02797768,0.03439588,0.04699,0.02097542,-0.0224764,0.0026267,-0.003736,0.04832061,0.03509195,-0.00479627,0.04120986,0.05819416,0.04636954,0.05651149,0.04108176,0.05738756,-0.16513744,0.0172867,0.02852038,0.05509219,0.00476853,-0.02474675,-0.00238962,0.02491935,0.03612762,0.03189976,-0.0233099,0.02445283,0.02177208,-0.06217882,-0.04817399,0.00916375,-0.06886014,0.0316853,-0.05619082,-0.02913613,-0.02315827,-0.01099129,-0.0116595,0.0227391,0.05220755,0.04693495,0.105802,-0.05500593,0.01439435,0.02956384,0.06217562,0.05433213,0.01130955,0.02178071,0.05150604,-0.00049719,0.02279068,0.11600115,0.01616383,-0.01858143,-0.0131033,0.00263061,0.02947058,-0.04110184,-0.001939,-0.05071022,-0.02405128,0.02757504,0.03118432,0.00515867,0.03215969,-0.0714355,-0.02320051,-0.06306763,0.01464187,-0.02053728,0.00613856,0.02027897,-0.05672732,0.0238726,0.01850875,0.01134669,0.02263168,0.02021868,0.03134041,0.06101255,0.05569717,0.04484864,0.0298774,0.03192139,-0.15575016,-0.04476599,0.00500409,-0.011642,0.05697497,0.00084518,0.02929923,0.04382588,-0.00878565,-0.01330579,0.04433061,-0.07982417,-0.02619209,0.07189697,0.03306393,-0.04787945,0.04559157,-0.01932658,-0.00051086,-0.00540704,-0.04957436,-0.07961945,-0.00688205,0.04269532,0.13738707,0.05599928,-0.06878901,-0.01683438,-0.0073343,-0.03362571,-0.00316575,0.09216454,0.00712784,-0.11850692,-0.00613876,0.04692881,-0.02105118,-0.1064897,-0.02914975,0.0013813,-0.053328,0.00270765,0.07283181,-0.01677912,-0.05949474,-0.05429698,0.01317153,-0.00558292,-0.00330684,-0.01294524,-0.00950201,0.00863143,-0.06343605,-0.07856675,-0.03660526,-0.02282567,-0.02682876,0.03762454,-0.03895014,-0.00482884,-0.06941009,-0.00801664,-0.00986081,-0.03039894,-0.0353244,-0.03542996,0.06475712,-0.03760096,-0.03883116,-0.05211003,0.05020398,0.01541071,0.03511562,0.05348675,-0.02587169,-0.04335569,0.09313667,0.00166019,-0.03552122,-0.02076032,0.03262153,0.07925837,-0.10590467,0.04990062,0.02247723,0.02040224,0.00827713,0.02892761,-0.03633428,0.03459907,-0.08352131,-0.19048332,-0.04681041,-0.02380092,0.00458755,-0.01399397,-0.03204006,0.04880487,0.02659535,0.00577451,0.07856972,0.11094599,-0.05846808,-0.03975451,-0.0273436,-0.05550517,-0.05019813,-0.10955793,-0.03532709,-0.02570317,0.0569636,-0.02140903,0.00392335,-0.01323969,-0.03870545,-0.00312016,-0.06429172,0.14020756,0.05464922,-0.01182166,-0.01816005,0.0665234,-0.00874103,-0.03337117,-0.03969543,-0.01953851,0.01091815,-0.0273925,0.00420861,-0.07587475,-0.01241256,-0.08461379,-0.00855541,-0.00916109,-0.07015873,-0.03087444,0.02078106,-0.0012344,0.01506922,0.02148098,0.07969675,0.00270397,-0.03061348,0.05995421,0.0273847,0.05653796,-0.01815734,-0.04949449,0.01430573,0.00914711,0.0815914,0.01743358,-0.03564853,0.02249668,-0.03116994,0.0607245,0.03460256,0.00365405,-0.03295759,0.03555228,-0.01549659,0.00541829,0.10268242,0.00666312,0.02220386,0.01910776,0.05351857,0.07221819,-0.03534514,0.00804106,-0.03624358,-0.02453057,-0.07500385,0.03685179,0.08125244,0.05400368,0.06651778,0.04312197,0.04382101,-0.01991522,0.010966,-0.00549439,0.03600106,-0.03544911,0.01666408,-0.00272854,0.0598818,-0.23158689,0.03288927,-0.02089491,0.07718562,0.00796971,-0.01762222,0.04747489,-0.00139048,0.00907962,-0.00173927,0.07679127,0.02019862,0.03947725,-0.08696155,0.00557177,-0.03569789,0.03426294,-0.01619202,0.06045244,0.01473807,0.09293289,0.02087467,0.22529027,0.01992853,-0.01978674,0.03766751,0.01625061,0.00988354,0.02748125,0.02721917,-0.01323706,-0.01974553,0.06402656,-0.00561958,-0.03483139,0.01869751,-0.02780564,0.014055,0.0058036,0.03526898,-0.02344118,-0.01140104,-0.04143917,-0.01060255,0.1300091,0.02935238,-0.06211316,-0.09077205,0.06230653,0.04295136,-0.0944188,-0.05346118,-0.06844119,-0.00227887,0.02092353,0.0386558,-0.00825266,0.02160384,0.0091993,-0.02441684,0.0027242,-0.00749916,0.03319188,0.03952689,-0.03039029],"last_embed":{"hash":"171y0sm","tokens":378}}},"text":null,"length":0,"last_read":{"hash":"171y0sm","at":1753423496361},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software","lines":[8,105],"size":11819,"outlinks":[{"title":"_**Software to Calculate Lottery Sums, Lotto Sum-Totals, Pick Digit Lotteries, Powerball Mega Millions**_","target":"https://saliu.com/bbs/messages/626.html","line":21},{"title":"_**Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_","target":"https://saliu.com/strategy.html","line":23},{"title":"Lotto sums software has four functions for most type of lottery games, from pick 2 to Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-sums-combinations.gif","line":25},{"title":"**Lottery deltas**","target":"https://saliu.com/delta-lotto-software.html","line":27},{"title":"_**Pick 2 Lottery Sums, Root Sum Chart**_","target":"https://saliu.com/forum/pick-2-lottery-sums-chart.html","line":33},{"title":"_**Pick 3 Lottery Sum-Total, Root Sums Chart**_","target":"https://saliu.com/forum/pick3-sums.html","line":34},{"title":"_**Pick 4 Lottery Sum-Total, Root Sum Charts**_","target":"https://saliu.com/forum/pick4-sums.html","line":35},{"title":"_**Pick 5 (formerly _Quinto_ in Pennsylvania) Lottery Root, Sums Chart**_","target":"https://saliu.com/forum/quinto-sums.html","line":36},{"title":"_**Lotto 5/39 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto539-sums.html","line":37},{"title":"_**Lotto 5/43 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto543-sums.html","line":38},{"title":"_**Lotto 6/49 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto649-sums.html","line":39},{"title":"_**Lotto 6/51 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto651-sums.html","line":40},{"title":"_**Lotto 6/54 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto654-sums.html","line":41},{"title":"_**Lotto 6/59 Sums, Root Sum Chart**_","target":"https://saliu.com/forum/lotto659-sums.html","line":42},{"title":"_**Euromillions Sums, Root Sum Chart**_","target":"https://saliu.com/forum/euromillions-sums.html","line":43},{"title":"_**Euromillions 5/50 & 2/11 Sum-Totals**_","target":"https://saliu.com/forum/euromillions-sums50211.html","line":44},{"title":"_**Mega Millions Sums, Root Sum Chart**_","target":"https://saliu.com/forum/megamillions-sums.html","line":45},{"title":"_**Sums, Sum Chart: <u>5/75 &amp; 1/15</u> Sum-Totals**_","target":"https://saliu.com/forum/megamillions-sums-75-15.html","line":46},{"title":"_**Powerball Sums, Root Sum Chart**_","target":"https://saliu.com/forum/powerball-sums.html","line":47},{"title":"_**Powerball 5/59 & 1/35 Sum-Totals**_","target":"https://saliu.com/forum/powerball-sums-59-35.html","line":48},{"title":"_**SuperLotto-Plus Sums, Root Sum Chart**_","target":"https://saliu.com/forum/superlotto-sums.html","line":49},{"title":"_**Thunderball Sums, Root Sum Chart**_","target":"https://saliu.com/forum/thunderball-sums.html","line":50},{"title":"Analyze a Powerball or Megamillion results file to show sums, average, standard deviation, delta.","target":"https://saliu.com/ScreenImgs/sum-lottery-draws.gif","line":54},{"title":"Read or print the statistics of sums for Powerball, Mega Millions, Euromillions lottery games.","target":"https://saliu.com/ScreenImgs/sum-stats.gif","line":58},{"title":"_**lotto deltas**_","target":"https://saliu.com/bbs/messages/648.html","line":60},{"title":"Read or print the statistics of sums for regular lotto games like 6 from 49.","target":"https://saliu.com/ScreenImgs/lotto-sums-stats.gif","line":62},{"title":"_**1 2 3 4 5 6 Lotto Combination Probability, Odds**_","target":"https://saliu.com/bbs/messages/961.html","line":64},{"title":"Lottery sums software calculates sum-totals in any lotto game and generates the combinations.","target":"https://saliu.com/forum/HLINE.gif","line":70},{"title":"_**Back to index: Forum Lotto, Lottery, Sums, Wheeling, Software**_","target":"https://saliu.com/forum/index.html","line":72},{"title":"<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>","target":"https://saliu.com/content/lottery.html","line":74},{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":76},{"title":"_**Lotto Lottery Software Systems**_","target":"https://saliu.com/LottoWin.htm","line":77},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":79},{"title":"_Users' Guide to_ _**MDIEditor And Lotto WE**_","target":"https://saliu.com/MDI-lotto-guide.html","line":81},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":83},{"title":"**Standard Deviation**","target":"https://saliu.com/deviation.html","line":84},{"title":"_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd/Even; Low/High Numbers**_","target":"https://saliu.com/strategy.html","line":85},{"title":"_**Software to Calculate Lottery Sums, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":86},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":87},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":88},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":89},{"title":"_**Lottery: Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":90},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":91},{"title":"Download all root sums for pick 2 3 4 5 lotteries, lotto, Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/forum/HLINE.gif","line":93},{"title":"Forums","target":"https://forums.saliu.com/","line":95},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":95},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":95},{"title":"Contents","target":"https://saliu.com/content/index.html","line":95},{"title":"Help","target":"https://saliu.com/Help.htm","line":95},{"title":"Home","target":"https://saliu.com/index.htm","line":95},{"title":"Software","target":"https://saliu.com/infodown.html","line":95},{"title":"Search","target":"https://saliu.com/Search.htm","line":95},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":95},{"title":"View root sums, sum-totals in lottery lotto: Powerball, Mega Millions, Euromillions, Pick-5 Quinto.","target":"https://saliu.com/forum/HLINE.gif","line":97}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06204225,-0.04208116,0.00916269,-0.06833384,-0.03424035,0.05431625,-0.02706902,-0.0103601,0.06680454,-0.00881581,0.03646566,-0.00793434,0.05059768,-0.00442001,-0.04813613,-0.00456793,-0.02465993,-0.04996791,-0.0712475,0.007422,0.10856331,-0.04360561,-0.03393082,-0.09050974,0.09756187,0.01665649,-0.06856616,-0.06828266,-0.04434685,-0.23935905,0.03961437,0.03648766,0.03359707,-0.03301001,-0.03808203,-0.04403038,-0.01748467,0.04960554,-0.04706008,-0.00154453,0.01677205,-0.00872729,0.01005761,-0.02135857,0.02942826,-0.04991647,-0.03308065,0.03669974,0.0472951,0.02220054,-0.02991741,-0.00540328,0.0010187,0.04225918,0.03450859,-0.00001652,0.038257,0.06460719,0.0409301,0.06052919,0.04384352,0.06162453,-0.16382614,0.01248509,0.03011784,0.05151064,0.00312645,-0.0294147,0.00639269,0.01297552,0.01829404,0.02889517,-0.00853757,0.03141183,0.02903038,-0.05704751,-0.05969129,-0.00018666,-0.07326116,0.03654363,-0.05987685,-0.02343217,-0.01563437,-0.00641721,-0.00148176,0.02787431,0.05544604,0.03730512,0.10809787,-0.05781035,0.02051954,0.04178806,0.0462159,0.05166161,0.01098022,0.02114708,0.05377153,-0.01144165,0.03000281,0.12130378,0.00501873,-0.01704483,-0.01755165,0.00527172,0.0257233,-0.04728983,-0.0044645,-0.05001261,-0.02782474,0.0347979,0.03033521,0.00488546,0.02460641,-0.07536323,-0.03265446,-0.06338669,0.00783598,-0.02566366,0.00589822,0.02139696,-0.05560466,0.02066302,0.01494366,0.01257848,0.02546344,0.019474,0.03539333,0.05987168,0.04917201,0.04153821,0.03775335,0.0297363,-0.15647095,-0.04215665,0.00336626,-0.01810303,0.05054457,0.0012416,0.02831598,0.03985905,-0.00937928,-0.00627825,0.05211425,-0.08045703,-0.02535471,0.07945694,0.02388081,-0.04335567,0.042836,-0.02976335,0.00635916,-0.00290958,-0.04913503,-0.07518462,-0.00872133,0.04328079,0.13072513,0.05278321,-0.06316911,-0.01766363,-0.02003573,-0.03471876,0.0034476,0.08948117,0.0012084,-0.11564061,-0.00178991,0.05203488,-0.0208458,-0.1048608,-0.02288517,0.00031618,-0.05492844,0.00408201,0.0671587,-0.01722043,-0.05189018,-0.04995232,0.02270975,-0.00713686,0.00794051,-0.01484694,-0.01754121,0.01164893,-0.06727744,-0.07424431,-0.03452612,-0.01801989,-0.03012817,0.0411514,-0.04378269,-0.00152307,-0.06864986,-0.00776462,-0.00972024,-0.03389085,-0.03622679,-0.03886137,0.06809973,-0.04040685,-0.0221357,-0.04895672,0.0518325,0.01936531,0.03777637,0.04818928,-0.03065087,-0.04683471,0.09179127,0.00028871,-0.03932082,-0.01971598,0.02777187,0.07985526,-0.10243917,0.05644062,0.01783587,0.02030111,0.00861787,0.03210686,-0.03518834,0.03475318,-0.08185888,-0.18868689,-0.05337859,-0.02170641,0.00590584,-0.01346578,-0.0255078,0.04068893,0.01767766,0.00379825,0.08331981,0.11198005,-0.05612675,-0.04229118,-0.01867514,-0.0486193,-0.04473665,-0.11343962,-0.0291645,-0.03515537,0.06187786,-0.01752637,0.00094779,-0.00230202,-0.03570046,-0.00559134,-0.05914636,0.13681979,0.03027259,-0.00792592,-0.0204075,0.06241183,-0.01139899,-0.03172681,-0.03641912,-0.01252861,0.01293384,-0.02732949,0.01005229,-0.07328312,-0.00790287,-0.08546129,-0.00009241,-0.00218646,-0.07396366,-0.02452222,0.02563057,0.01178616,0.01374257,0.01904981,0.0812168,0.00272939,-0.02732687,0.06328012,0.03258939,0.06003221,-0.02216098,-0.04266042,0.02034116,-0.00023091,0.07656655,0.01444443,-0.04715469,0.0240923,-0.02645651,0.05707555,0.03082656,0.00589329,-0.03312746,0.03509557,-0.01535188,0.00322367,0.1080229,0.00530337,0.03120927,0.01742006,0.05652121,0.08117741,-0.04342292,0.00205769,-0.03452843,-0.01978569,-0.06949142,0.03901198,0.08033394,0.05464446,0.05770816,0.05152099,0.0455591,-0.01470012,0.00938395,-0.00743286,0.02689466,-0.0310702,0.0224042,-0.01102946,0.0673136,-0.23393241,0.02867892,-0.03130874,0.07016025,0.00319234,-0.02917959,0.04136313,-0.0002404,0.01290675,-0.0048518,0.06809118,0.01308455,0.04317821,-0.09385232,0.00296589,-0.04294046,0.04658711,-0.01430122,0.05612414,0.01701982,0.08956015,0.02161034,0.22234116,0.01369026,-0.01207988,0.03879323,0.01906499,0.00199298,0.03421363,0.02599317,-0.0102243,-0.02006646,0.07032608,-0.0097562,-0.03341574,0.0277005,-0.02952747,0.00864187,0.00458969,0.03279379,-0.01780676,-0.01143626,-0.04500941,-0.00799454,0.1313118,0.02505858,-0.05933312,-0.09361732,0.06662312,0.0415069,-0.10012506,-0.04898926,-0.07055268,-0.007725,0.02576215,0.03181625,-0.00961869,0.03286898,0.01036293,-0.02081867,0.00999803,-0.01110315,0.0391356,0.04647356,-0.02857965],"last_embed":{"hash":"i42zyk","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"i42zyk","at":1753423496484},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{1}","lines":[10,25],"size":2062,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06626642,-0.06927492,-0.01210253,-0.04381459,-0.04928682,0.06039169,0.01863907,0.00140346,0.06499462,-0.02246336,-0.01847733,-0.00757599,0.05226183,0.02051482,-0.0306187,-0.01764543,-0.01398514,-0.0407792,-0.10041497,0.00713964,0.1370607,-0.07577542,-0.03789968,-0.09695059,0.08470049,0.02876743,-0.04615941,-0.03218608,-0.0104074,-0.21657255,0.04379248,0.04553463,-0.01428758,-0.06658984,-0.08319155,-0.04609906,-0.00228872,0.04398664,-0.04436154,0.0226787,0.02092402,-0.02721235,0.01858687,-0.02294962,0.00051606,-0.06723864,-0.02729142,0.01181199,0.03947774,0.02629907,-0.02566074,0.00419241,0.00841496,0.04319968,0.02927071,0.00951593,0.03172638,0.09119207,0.02586842,0.03806853,0.0548834,0.04644381,-0.1953716,0.05114363,-0.00483986,0.03861602,-0.00385666,-0.03873064,-0.02443726,0.06166677,-0.00271627,0.05483439,-0.02733392,0.0270909,0.02765349,-0.04582646,-0.07486304,-0.05105542,-0.05684587,-0.01007359,-0.0541599,-0.02859102,-0.04156555,-0.02511973,0.02005977,0.05404026,0.0711102,0.04106169,0.09207596,-0.05760907,0.00582747,0.01837173,0.04919355,0.06376045,0.03217993,0.05868693,0.04812026,-0.03359402,0.0416565,0.10226248,0.03644443,0.00293306,-0.03422743,-0.00406569,0.06300368,-0.02734338,-0.02247801,-0.05919893,-0.01461787,0.04559332,0.02253921,-0.02846726,0.05945475,-0.04671738,-0.06178411,-0.05741679,0.00222498,-0.03892276,0.04622986,0.00703166,-0.04404759,0.00540022,0.01480495,0.00665782,0.01661534,0.02476726,0.03377295,0.04796429,0.04591391,0.04588822,0.07287093,0.00519428,-0.13509616,-0.01544471,-0.00990528,0.00647706,0.01718944,0.00525028,0.0053076,0.05308662,-0.03733959,-0.0136891,0.06262483,-0.0787487,-0.03126608,0.09060325,-0.01760048,-0.00374362,0.05564675,0.02592365,-0.00700452,0.01367796,-0.02403931,-0.06659269,-0.00957923,0.02801847,0.10459073,0.07315252,-0.08470956,-0.00006041,-0.00960008,-0.05380207,-0.01627053,0.10697301,0.0060574,-0.07379042,-0.0046145,0.03405552,-0.01716249,-0.11462931,0.00856833,0.01141567,-0.03229439,0.00616796,0.07790498,-0.01423943,-0.01381658,-0.06694503,0.00414299,0.03017533,0.00565153,-0.01946167,-0.04700188,0.03216115,-0.06593744,-0.09719541,-0.02348531,-0.02676413,0.01819975,0.03014336,-0.02705856,0.01283793,-0.03225497,-0.0186887,0.00636386,-0.03823558,-0.03863496,-0.03059932,0.05449867,0.01211946,-0.00554373,0.00011368,0.07063813,-0.00469924,0.0319315,0.07366266,-0.03529533,-0.04247479,0.06087567,0.00529842,-0.03132293,0.00425101,0.01505688,0.09532161,-0.09089632,0.0532434,0.04389409,0.01045728,0.01599431,0.03341787,-0.05403851,0.02727573,-0.08015049,-0.18892594,-0.0624632,-0.02611039,-0.00120256,0.02752908,0.00141385,0.01226901,0.03500383,0.01641399,0.08516449,0.0895248,-0.06985058,-0.02078077,0.01146836,-0.03500416,-0.06026311,-0.08372402,-0.04227767,-0.03885085,0.03722093,-0.01829566,-0.00131139,0.02237237,-0.04559632,0.00634484,-0.05472923,0.141728,0.02573254,0.00183458,-0.04097073,0.05224215,0.01843669,-0.00281504,0.00507491,0.00127603,-0.00276318,-0.05286391,-0.03043752,-0.06583658,-0.00772432,-0.0888067,0.01388559,-0.00793454,-0.06271645,-0.02175869,-0.00994538,0.00438684,-0.00835578,-0.01034448,0.04528338,0.00329584,0.02882897,0.05410986,0.03539172,0.04907691,-0.04397918,-0.04302413,-0.01267913,0.00084893,0.04808944,0.0140631,-0.06408285,0.01511996,-0.0029866,0.0360772,0.01608175,0.00791421,-0.03815517,0.03449763,-0.02245222,-0.01449286,0.12635529,-0.01472092,-0.00302145,0.02697376,0.01637968,0.09341221,-0.00530593,-0.00501325,-0.01303708,-0.00762337,-0.08787145,0.00961235,0.04104478,0.01606395,0.03236452,0.07720415,0.01138166,-0.0349031,0.01814924,-0.04400152,0.03002715,-0.01772369,0.02265857,-0.01471595,0.04064783,-0.24659617,0.03510902,-0.03347877,0.05502958,-0.00467585,-0.02728475,0.02710492,-0.02433026,-0.01115372,-0.00580256,0.05447948,0.01960079,0.04678382,-0.07364441,-0.01594492,0.00909603,0.04330248,-0.02823083,0.03906102,0.01192691,0.12262744,0.05545197,0.21906209,0.0013527,-0.01758583,0.04583606,0.04179697,0.03902382,0.00634022,0.04459683,-0.00712149,-0.01432748,0.08235113,-0.04751863,-0.04818753,0.06470858,-0.02811711,0.02835889,-0.00270407,0.02118153,0.00172286,-0.01472459,-0.0468905,0.00832689,0.11423592,0.01762382,-0.02680267,-0.07928418,0.07805261,0.05670946,-0.10143068,-0.03303144,-0.065084,0.01107431,0.00826007,0.05500615,-0.02952752,-0.00096008,-0.01740441,-0.01728685,0.01053626,0.00633171,0.03594739,0.02901368,-0.02387203],"last_embed":{"hash":"nj151t","tokens":415}}},"text":null,"length":0,"last_read":{"hash":"nj151t","at":1753423496637},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{6}","lines":[32,39],"size":1448,"outlinks":[{"title":"Lotto sums software has four functions for most type of lottery games, from pick 2 to Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-sums-combinations.gif","line":1},{"title":"**Lottery deltas**","target":"https://saliu.com/delta-lotto-software.html","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{25}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07473262,-0.0192357,0.02179685,-0.06035813,-0.01617092,0.08106416,0.00285134,0.00358427,0.03441731,-0.00889525,0.03527208,-0.04070652,0.03767954,-0.00608851,-0.03950555,-0.00667262,0.00768393,-0.03571938,-0.06388549,0.00619466,0.12567094,-0.05864171,-0.04117046,-0.09358524,0.09498456,0.01631605,-0.05593393,-0.04828852,-0.02945942,-0.20696628,0.044087,0.02788307,0.03875882,-0.04240395,-0.03338039,-0.03249113,-0.03642767,0.05278767,-0.00499862,0.01241199,0.04315485,-0.03615955,-0.01336559,-0.03783349,0.02152302,-0.09035004,-0.00164153,-0.00210943,0.08306734,0.02039703,-0.01227053,-0.00448479,0.02826959,0.03808044,0.0601934,-0.01163715,0.08791444,0.04047751,0.04420836,0.04576907,0.03363219,0.08091231,-0.20642729,0.00613231,0.05683101,0.04917386,0.00455835,-0.01773011,-0.00932785,0.01051259,-0.0039503,0.03131486,-0.01541491,0.02986775,0.02037649,-0.02842513,-0.06821842,-0.02434185,-0.08246468,0.01517101,-0.06339612,-0.0165625,-0.02136678,0.02160769,0.02391717,0.02579866,0.05528333,0.06502019,0.12671193,-0.02421557,0.01696881,0.00510926,0.01046337,0.04549806,0.01152892,0.05191271,0.05189385,-0.03640605,0.04900507,0.14626268,0.01953593,0.00101205,-0.01106194,0.0113544,0.02195401,-0.04269141,-0.02688809,-0.00657385,0.02009644,0.05487904,0.00955409,-0.00485084,-0.01008594,-0.06687731,-0.02876006,-0.0642074,0.00287425,-0.03087267,-0.02582728,-0.00151587,-0.06619059,0.00708366,0.01392171,0.01328419,0.03830786,0.00520108,0.05718455,0.05864122,0.04155336,0.05037776,0.02516318,0.00595734,-0.15235195,-0.05092419,-0.00272698,0.00948625,0.07405034,0.03978618,0.00847551,0.03572663,-0.02341182,-0.03130283,0.04322901,-0.07911244,-0.0303628,0.08455908,-0.01718356,-0.02075135,0.06008555,-0.03145581,-0.00168835,0.00309682,-0.05476193,-0.04820959,-0.01734659,0.04796648,0.08792903,0.07280666,-0.06898393,-0.00393444,-0.02955314,-0.06255609,-0.00045709,0.08994746,0.01317095,-0.07416767,0.01254949,0.01814686,-0.000596,-0.10753819,0.00062459,-0.00979621,-0.02747195,-0.03380418,0.03705228,-0.02882509,0.01018362,-0.08334369,-0.03101628,0.0117344,-0.00181178,0.01101035,-0.05684825,0.03688189,-0.05162332,-0.05150852,-0.02505418,-0.04538079,0.02736172,0.04009845,-0.05525418,-0.01663296,-0.10099459,0.0036705,-0.0065959,-0.02748429,-0.01481925,-0.03803843,0.03362119,-0.03142417,0.02128278,-0.00795595,0.0493308,-0.00884672,0.02105142,0.08156242,-0.06387048,-0.00821263,0.05553083,-0.00558807,-0.03475836,-0.02891799,0.06297613,0.07379709,-0.0827118,0.03683507,0.04559115,-0.00802855,0.0143074,0.03672975,-0.02606103,0.01024137,-0.08127356,-0.20420401,-0.08052392,-0.02331415,0.00187421,-0.0043605,-0.03439885,0.06341846,0.02120511,0.02941752,0.05726833,0.11424909,-0.02436526,-0.02781491,0.01670904,-0.04893378,-0.03399017,-0.09768251,-0.02166796,-0.02963104,0.0763983,0.01461528,0.00180057,0.03080544,-0.01762747,0.00126153,-0.06509786,0.14401533,0.04095189,-0.01305135,-0.01804726,0.08008203,0.00471945,-0.02675496,-0.03797973,-0.00604971,-0.03414126,-0.07787028,0.03363736,-0.1166666,-0.01114423,-0.07950688,-0.00888339,0.02808972,-0.06601141,-0.00366019,0.01215239,0.03929344,-0.03633349,-0.02322358,0.0362903,0.03373478,-0.02491247,0.05143515,0.00511844,0.04855922,-0.00682963,-0.03909739,0.0057595,-0.02787614,0.0221539,0.02009354,-0.02396159,0.01874744,-0.02478495,0.04803358,-0.0146568,0.01351293,-0.03275875,0.01410303,0.02183064,0.00964922,0.08002615,0.01650226,0.02326924,0.0285735,0.0522324,0.0926777,-0.04690557,-0.00496291,-0.01167368,-0.00431248,-0.04814871,0.02126737,0.03632241,0.04327558,0.06445732,0.04118759,-0.01651747,0.0086892,-0.0162935,-0.02992469,0.01488912,-0.02332121,0.01397745,-0.03742677,0.05711535,-0.25959557,0.03991169,-0.03989252,0.03606497,0.00249569,-0.04196527,0.04017542,-0.01868337,-0.02345638,0.0125546,0.06811346,0.01364095,0.04494926,-0.10100837,-0.01420004,-0.02799318,0.03904238,-0.00389695,0.08305804,0.01115179,0.10121297,0.00920768,0.20814092,0.00868608,-0.02914453,0.02619321,0.00745529,-0.04968665,0.02021842,0.05443157,0.01128875,-0.02065495,0.07751028,0.02488137,0.00969368,0.06898825,-0.02835239,0.03146207,0.00868774,0.02985288,0.00834454,-0.00767257,-0.06272861,-0.02248378,0.10053218,-0.00420563,-0.04633514,-0.04997413,0.0577686,0.04410636,-0.09509731,-0.028054,-0.05451072,-0.00390562,0.00313167,0.0428538,-0.01533552,0.02409991,0.0418646,-0.04075842,0.00898904,0.00697623,0.03699777,-0.0069942,0.00292471],"last_embed":{"hash":"9rmny8","tokens":110}}},"text":null,"length":0,"last_read":{"hash":"9rmny8","at":1753423496772},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{25}","lines":[59,62],"size":270,"outlinks":[{"title":"Analyze a Powerball or Megamillion results file to show sums, average, standard deviation, delta.","target":"https://saliu.com/ScreenImgs/sum-lottery-draws.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{26}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08014968,-0.07563341,0.01653353,-0.04889848,-0.00247004,0.03495308,0.02228961,0.01877295,0.06194985,0.02450758,0.00761035,-0.03527982,0.06373448,-0.01001658,-0.05748831,-0.00864359,0.01416348,-0.01984966,-0.04350214,0.0229198,0.10651388,-0.03428249,-0.05421718,-0.1170103,0.12406698,-0.00609556,-0.06470461,-0.07835081,-0.06082423,-0.20282046,0.04419646,0.01180671,0.01932861,-0.03842825,-0.02710714,-0.03615662,0.00614737,0.06728339,0.0317263,0.00511013,0.01139663,-0.02736826,-0.01793639,-0.05435867,0.02336906,-0.0672827,-0.05038341,0.01606525,0.05756255,0.04037205,-0.0263897,-0.01403639,0.04872034,0.03718244,0.01174135,0.00802892,0.05481676,0.03684999,0.04197662,0.03878022,0.01583081,0.05517076,-0.18721846,-0.00379578,0.00833797,0.04185298,0.01001286,-0.04108389,0.00548233,0.04039736,0.00354399,0.01896688,-0.04269583,0.04577773,0.04912096,-0.07443101,-0.03382319,-0.04438977,-0.06213306,-0.00886148,-0.07675196,-0.02737758,-0.02175073,0.03644399,0.00121441,0.05193847,0.0769535,0.03118658,0.11953023,-0.01282885,0.02423334,-0.00371836,0.0374601,0.03147602,0.00054821,0.04599918,0.07914761,-0.04101413,0.03252721,0.12980574,0.00806085,0.0284766,-0.01793831,-0.01199781,0.03190463,-0.06103196,-0.00674828,-0.01512885,0.00934064,0.03813834,0.02453273,-0.03450708,0.0155483,-0.06768375,-0.01518285,-0.07039417,0.00811748,-0.02666562,-0.0078555,-0.00512942,-0.06724199,0.02048055,0.01390914,0.00130588,0.04387125,-0.00113587,0.04827689,0.07843447,0.02479492,0.05866499,0.05747849,0.02689965,-0.11770641,-0.0309188,-0.01478399,0.0429686,0.0483131,0.05739618,0.01952728,0.0556432,-0.02792411,0.01335065,0.02895667,-0.03397916,-0.01955647,0.06132047,-0.04310443,0.00296043,0.07904157,-0.02002804,-0.00394938,0.00397418,-0.0503752,-0.02108368,-0.01192412,0.012767,0.07784566,0.06295829,-0.06258009,-0.00625827,-0.02044045,-0.07838921,0.0062742,0.10044212,-0.01432675,-0.06334389,0.00428466,0.04917058,0.01275613,-0.07994001,0.02329789,-0.02324804,-0.01899464,0.0083417,0.05882197,-0.03800864,-0.02772049,-0.06356243,-0.00737792,-0.00989006,-0.00977292,0.01220191,-0.07057938,0.04588745,-0.03397703,-0.0666804,-0.03155302,-0.04054474,-0.00026213,0.05104457,-0.0427118,-0.00096624,-0.1028883,-0.0085109,-0.01488609,-0.03841602,-0.03918399,-0.01258326,0.01971213,-0.01185375,0.03701363,-0.02766735,0.05405878,-0.05206778,0.00478967,0.08497451,-0.04911144,-0.02451331,0.08318403,0.03619346,-0.05638987,-0.04130042,0.02897912,0.06298881,-0.05209668,0.03462697,0.04923437,0.02053323,0.01458791,0.03319377,-0.03748975,0.01859077,-0.08800449,-0.20469952,-0.06193131,-0.00420006,0.03500424,0.03406895,-0.02296274,0.04469115,0.05437362,0.01649085,0.10338437,0.12096754,-0.02698407,-0.02661945,-0.0086228,-0.05798468,-0.04292078,-0.10165852,-0.02760001,-0.02913463,0.05882486,-0.02853035,0.01271574,0.01358991,-0.03297677,-0.00154018,-0.02151335,0.14436419,0.00340655,0.01171636,-0.02229189,0.06549412,0.00962646,-0.02163982,-0.05134604,0.00214427,0.03245596,-0.0746594,0.02954196,-0.07366436,-0.0045099,-0.08216033,0.0041296,0.01697235,-0.07557543,-0.04556938,-0.01163694,0.00769704,-0.03641154,0.00315524,0.02253602,-0.01170129,0.00899766,0.05385824,0.02332562,0.05557316,-0.01566076,-0.05900734,-0.006284,-0.01511952,0.06425076,0.03636424,-0.04701181,0.06127387,-0.03604667,0.05474693,-0.00228455,0.01780502,-0.05148351,0.01232931,0.00894397,0.00649565,0.12811109,-0.02075784,-0.01236598,0.01423202,0.07822325,0.09096782,-0.03162582,-0.00789468,-0.02651514,-0.0142687,-0.11478879,0.0073636,0.01014891,0.01172917,0.06976778,0.01902724,0.00768403,-0.02408188,0.01473172,-0.01544533,0.00920629,-0.05106674,-0.00533341,-0.00279448,0.06980709,-0.24178039,0.01072137,-0.07100195,0.03212438,-0.01144924,-0.04567913,0.03239872,-0.00771623,-0.0029592,0.0082079,0.05940176,0.0329372,0.01313787,-0.09959099,0.00347915,-0.01574437,0.03359425,-0.00418469,0.08419926,0.00197837,0.08090521,0.02643075,0.21399014,0.00084963,-0.03167695,0.02618425,0.01411481,-0.01414456,0.01334933,0.04538395,0.00135533,-0.00649182,0.07056182,0.01217768,-0.00982528,0.0774406,-0.02294003,0.01159116,-0.01496133,0.02390185,-0.00658098,-0.01752878,-0.05302318,0.00627914,0.09262718,-0.02384309,-0.04973637,-0.04938954,0.09417733,0.07299124,-0.0629788,-0.04845961,-0.06313433,-0.00481047,0.01966494,0.04585903,-0.02928117,0.03218183,0.00301517,-0.02447551,-0.01100329,-0.01713049,0.0622479,0.03293056,-0.01413114],"last_embed":{"hash":"1ljv38h","tokens":131}}},"text":null,"length":0,"last_read":{"hash":"1ljv38h","at":1753423496809},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{26}","lines":[63,64],"size":406,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{27}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03485103,-0.0747854,0.00744898,-0.03600183,-0.02247522,0.08294471,0.0328153,0.01332704,0.0596323,-0.02629219,0.03352181,-0.04296764,0.05337102,-0.00414388,-0.01952337,-0.03597052,-0.00414374,-0.01478262,-0.06807838,0.01765737,0.16151194,-0.08616001,-0.03580283,-0.08541448,0.107293,0.02706009,-0.04213546,-0.04272509,-0.03426119,-0.23551235,0.02994703,0.03989602,0.00253983,-0.07406411,-0.04280389,-0.07088469,-0.01110275,0.06597038,-0.02014155,0.02984737,0.00476274,-0.0226506,0.01843514,-0.00750372,-0.00256601,-0.05358136,-0.03396033,0.0042494,0.04839994,0.03391302,-0.02341382,-0.0005573,-0.01169005,0.02181427,0.06989804,-0.00540407,0.08492016,0.04980991,0.06921076,0.05917808,0.01725561,0.05001307,-0.23145309,0.02494276,0.04991632,0.01146863,-0.01092965,-0.0421697,-0.02921448,0.04752345,-0.00634059,0.03231044,-0.00262998,0.03919926,0.01541945,-0.04274249,-0.07741523,-0.02506149,-0.04808296,0.00265119,-0.06059934,-0.03411939,-0.04739644,-0.00885972,0.00427357,0.04439106,0.0760133,0.05221846,0.08026876,-0.05247628,0.0113518,-0.0025163,-0.00526221,0.02302561,0.01848316,0.05307318,0.01630936,-0.01493587,0.06222488,0.10690989,0.04746732,-0.00358505,-0.06094382,0.01118506,0.04140582,-0.04518122,-0.04088622,-0.035847,0.01327429,0.0726195,0.01500068,-0.0407323,0.01145779,-0.05971621,-0.02615564,-0.02766003,0.0062466,-0.01744157,0.03859067,-0.03308056,-0.05694018,0.02008251,0.03112155,-0.01426738,0.04069804,0.02909836,0.01218655,0.05433077,0.06135875,0.06019016,0.06078586,-0.00773494,-0.13414286,-0.04656268,-0.00388689,0.01430124,0.06383816,0.067926,0.00051023,0.0446468,-0.01933544,-0.07341028,0.02685826,-0.03688801,-0.04957694,0.11606453,-0.0467102,-0.01892787,0.08089776,-0.03017716,0.00110542,0.00216676,-0.02427272,-0.05777032,-0.01795521,0.03854876,0.07249658,0.05082383,-0.03022041,-0.01053023,-0.02976359,-0.05573333,0.01616666,0.08621269,0.00331054,-0.03690595,-0.02960734,-0.00371257,0.00233978,-0.06384595,0.04080153,-0.0281386,-0.04314247,0.00131731,0.03178108,-0.02502003,-0.00310047,-0.06093258,-0.02290646,0.05133238,-0.02022808,-0.01154703,-0.0591905,0.00563068,-0.05853542,-0.06390576,-0.03781116,-0.03086836,0.03996181,0.03312684,-0.03552245,0.00490509,-0.06663725,0.01641025,-0.00000768,0.0053557,-0.02819067,-0.01810842,0.05523489,-0.00394689,-0.00480277,0.02621166,0.03000331,-0.00528886,-0.03621636,0.06792818,-0.04338081,-0.03004938,0.06206611,-0.00302157,-0.02271481,-0.0240322,0.01191718,0.06806778,-0.06196379,0.06056005,0.04844579,-0.00040509,0.00862855,0.04455996,-0.0302285,-0.0012599,-0.06174957,-0.18652213,-0.0437812,0.00907379,-0.01511818,0.03349775,-0.00895655,0.03444107,0.01900451,0.03398608,0.08861823,0.09912965,-0.03495014,-0.01850239,0.02523773,-0.04245552,-0.01864518,-0.10939923,-0.00675755,-0.05424372,0.06535631,-0.02141298,0.03675476,-0.00024348,-0.02801904,-0.00173943,-0.03831488,0.12510268,0.00473183,0.0163954,-0.01920486,0.03354932,0.0256026,0.00519218,0.01781298,-0.02441421,-0.02377313,-0.05455966,-0.02018837,-0.10692187,-0.01866205,-0.05280346,0.02820279,0.02372066,-0.06289437,-0.04034935,0.00545211,0.01774126,-0.01806027,-0.00051662,0.07664995,-0.01252614,-0.01732912,0.09168953,-0.0110896,0.07085232,-0.01296314,-0.08962576,-0.01591158,0.01009644,0.03691217,-0.01498784,-0.03376349,0.0397428,-0.03569046,0.04641255,0.03170792,0.02044537,-0.05998144,0.03214522,0.00091667,-0.00582566,0.09499823,0.00294394,0.01849874,-0.01147527,0.02787399,0.07275419,-0.00053084,0.01231666,0.00554475,0.031936,-0.04471496,0.03404337,0.03156051,0.03306427,-0.02673598,0.05616368,0.03652284,0.01149543,0.02393397,-0.06013931,0.00914649,-0.04212583,0.01001839,-0.03560527,0.04114702,-0.26632705,0.03096765,-0.05212632,0.00839961,-0.00689229,-0.0353537,0.03302921,0.00123398,-0.01221908,-0.01890345,0.05067704,0.03117477,0.06027246,-0.07039455,-0.0198484,-0.00701012,-0.01086849,-0.0570313,0.07344472,-0.01127761,0.11719814,-0.00945604,0.19958779,-0.01651674,-0.01803999,0.01107036,0.05810889,-0.01073629,0.02262664,0.03479792,-0.02443261,0.00006812,0.08062927,0.01163833,0.02004836,0.08202077,-0.05218126,0.04468216,-0.00771813,0.00566941,0.01722093,-0.04244738,-0.02631657,-0.00589733,0.13964328,0.00248644,-0.06272864,-0.08927545,0.09587503,0.07285062,-0.08423281,-0.00963217,-0.04809241,-0.00029027,0.01272813,0.03543544,-0.01752574,-0.00411047,0.03235351,-0.01218622,0.00033099,0.02984267,0.0088836,0.00190745,0.00145188],"last_embed":{"hash":"1y5ym5s","tokens":255}}},"text":null,"length":0,"last_read":{"hash":"1y5ym5s","at":1753423496853},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{27}","lines":[65,70],"size":809,"outlinks":[{"title":"Read or print the statistics of sums for Powerball, Mega Millions, Euromillions lottery games.","target":"https://saliu.com/ScreenImgs/sum-stats.gif","line":1},{"title":"_**lotto deltas**_","target":"https://saliu.com/bbs/messages/648.html","line":3},{"title":"Read or print the statistics of sums for regular lotto games like 6 from 49.","target":"https://saliu.com/ScreenImgs/lotto-sums-stats.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{28}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06024659,-0.06869604,-0.01971344,-0.03865399,-0.02859919,0.05506121,0.03058892,0.01126568,0.05833924,-0.01414881,-0.0004843,-0.00303099,0.03850853,0.02098467,-0.00210013,-0.00911448,-0.03495813,-0.02075355,-0.09123225,0.03727601,0.10848261,-0.0565022,-0.01234898,-0.10845507,0.08551712,0.00113255,-0.0610296,-0.05866626,-0.03809333,-0.20193283,0.01535624,0.0300811,0.01412528,-0.05142368,-0.05266106,-0.03293198,-0.02560543,0.07480849,-0.01586172,0.01766796,0.02121405,0.00239962,-0.0099058,-0.02878223,0.00431751,-0.06166986,-0.00966719,0.00708542,0.06322741,0.0414707,0.00335901,-0.0258302,0.0049959,0.05415077,0.04738576,0.00753523,0.07041669,0.05101017,0.02086428,0.09689631,0.0322361,0.05382133,-0.18346936,0.02107025,0.02814472,0.03851812,0.00885338,-0.01469978,-0.01273351,0.07236208,0.01591706,0.05626753,-0.02672903,0.02876398,0.04345933,-0.05597309,-0.04980431,-0.04072447,-0.03735602,0.0245222,-0.08262338,-0.00114949,-0.06019082,-0.03988687,0.00295766,0.00482601,0.05310977,0.04760313,0.05923254,-0.05884902,0.02847772,0.05290375,0.03254821,0.04114311,0.03165751,0.00921701,0.05181624,-0.06236799,0.05075794,0.14293145,0.00096266,0.00914064,-0.02787763,-0.00071743,0.02523194,-0.04050465,-0.0399344,-0.01440676,-0.00486993,0.02820533,0.05125242,-0.00849406,0.03290188,-0.11253665,-0.05518766,-0.03614947,0.02826388,-0.01219915,0.0127048,0.01016,-0.07036407,0.00386121,0.06585463,0.01182199,0.02034945,0.0146364,0.03637362,0.07241788,0.03986076,0.04214254,0.03150171,-0.00281431,-0.10533327,-0.02752084,-0.02534467,0.01666045,0.05035238,-0.0084299,-0.01290549,0.02804396,-0.02655859,-0.0714296,0.06629613,-0.0901806,0.00973212,0.0710849,-0.00904785,-0.01291504,0.08800445,0.00440112,-0.06597128,-0.01649588,-0.0250144,-0.05324363,-0.01255015,-0.00267,0.08419143,0.04738305,-0.04056262,0.01166978,-0.02271526,-0.06399292,-0.00383608,0.12206658,0.00072724,-0.0785467,0.02372115,0.02761712,-0.01561517,-0.10261981,0.02256083,-0.01839733,0.00325521,-0.00091587,0.08862707,-0.00930745,-0.02061771,-0.0643387,-0.00220511,-0.01318981,-0.02683693,0.01737607,-0.0360099,0.04732711,-0.03337021,-0.05775798,-0.0040014,-0.01392567,-0.01831677,0.01709097,-0.03216606,-0.03917198,-0.07597451,0.00376559,0.02483626,-0.02797,-0.0592778,-0.05331271,0.05516749,-0.00523757,-0.02352185,-0.04487171,0.03612877,-0.01168435,0.01849187,0.08479114,-0.04490208,-0.0188305,0.05108082,-0.01091207,-0.02760767,0.01079905,0.03410198,0.07214396,-0.07412869,0.03353489,0.03679141,0.00563107,0.04139555,0.0388191,-0.0259719,0.00936915,-0.04915119,-0.20606932,-0.08239083,-0.00697042,0.01114007,0.03171514,-0.00324444,0.00529121,0.0295427,0.06445856,0.08559442,0.10879207,-0.04210847,-0.03176385,0.0027221,-0.03732693,-0.06780387,-0.11838987,-0.03902942,-0.04195118,0.10723861,-0.01562933,0.03682786,0.0030818,-0.04837663,0.01712408,-0.0183579,0.13237929,0.06018183,-0.04824997,-0.02769919,0.05640218,0.00006809,-0.02932656,-0.01253641,0.0185922,0.01349095,-0.04990377,-0.03126599,-0.09187158,-0.00388869,-0.07625106,0.02200166,-0.01119434,-0.09399859,-0.03381542,-0.00021479,-0.00364679,0.04978731,0.02781725,0.05819488,0.00731817,-0.00128929,0.07455717,0.02170523,0.07437393,-0.02890906,-0.06155048,0.01404923,0.02090111,0.03198375,-0.01258696,-0.03118372,0.04181591,-0.01999046,0.02600333,0.02231996,-0.00725233,-0.05899509,-0.0101407,-0.00599947,-0.01663012,0.08458332,0.0072054,0.02164066,-0.00097443,0.02055766,0.08636052,-0.06607728,-0.00462265,-0.00994891,0.02936138,-0.08535776,-0.01383774,0.06450315,0.03960647,0.01587853,0.03762844,0.04324552,0.00566631,0.01118908,-0.02821952,-0.01428338,-0.01839744,0.01437316,-0.00027218,0.03377457,-0.25635505,0.02493333,-0.03185638,0.09226924,-0.01636786,-0.00938412,0.02723451,-0.00538845,-0.02641397,0.00340649,0.09879757,0.03040456,0.00954066,-0.08201028,0.00464016,-0.02639893,0.01622679,-0.01580742,0.04460566,0.00339005,0.09852678,0.04482377,0.22061729,0.04844536,-0.00064064,0.01405997,0.01569268,0.023525,0.00431844,0.04923011,-0.01062476,-0.04916382,0.03773982,-0.02909501,-0.03271671,0.03703595,0.02652202,0.02766201,-0.01545455,0.03279645,-0.05295471,0.01242536,-0.05145462,0.01038512,0.12619253,0.01155325,-0.05230072,-0.07073866,0.0784222,0.04627781,-0.06424086,-0.06647056,-0.05417057,-0.02606296,-0.01295959,0.02466013,0.02310141,0.01313773,0.00623889,-0.02185338,0.02370933,0.00041697,0.03264853,0.02197949,-0.03240192],"last_embed":{"hash":"lv31q5","tokens":95}}},"text":null,"length":0,"last_read":{"hash":"lv31q5","at":1753423496924},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{28}","lines":[71,72],"size":215,"outlinks":[{"title":"_**1 2 3 4 5 6 Lotto Combination Probability, Odds**_","target":"https://saliu.com/bbs/messages/961.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{29}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05298762,-0.07453801,0.02037306,-0.03167282,-0.03460354,0.02454527,0.00383206,0.02049407,0.07827724,-0.0102115,-0.00640652,-0.03018557,0.06804886,0.00254127,-0.02456786,0.00674708,-0.00154584,-0.01487579,-0.08595774,0.02883402,0.10329445,-0.03531231,-0.03283149,-0.10020974,0.09486723,0.01124922,-0.06182529,-0.09817035,-0.03549689,-0.22748445,0.04586702,0.03487809,0.01310736,-0.06794329,-0.05771384,-0.02254355,-0.0249352,0.02948394,-0.02099883,0.01197507,0.03060227,0.01591196,0.01155377,-0.02838445,0.03005896,-0.05288072,-0.02527285,0.00644493,0.02273346,0.04602846,-0.06493624,-0.00437006,0.0092444,0.04129016,0.03864457,0.04116165,0.01694204,0.05235867,0.01839108,0.0439559,0.04603153,0.07154321,-0.20017348,0.00698466,0.04925527,-0.00501876,0.01057125,-0.04643495,-0.02313912,0.05569892,0.0069117,0.02348896,-0.0059929,0.00779639,0.03028309,-0.05591402,-0.04433968,-0.0324099,-0.01766028,-0.003078,-0.05465102,0.00072399,-0.02442808,-0.01014539,-0.00987452,0.02171014,0.11697261,0.04775896,0.08249499,-0.07711687,0.02617249,0.04474394,-0.001005,0.03452795,0.04715136,0.05185326,0.05646605,-0.03886252,0.02380228,0.09855731,0.01929504,-0.00933965,-0.0479688,-0.0128848,0.01066167,-0.03675961,-0.02689235,-0.02457412,0.02415268,0.04882839,0.04520308,-0.01338781,0.0358294,-0.07723571,-0.00870305,-0.04340478,0.05412605,-0.02729644,0.00702534,-0.02301467,-0.02511495,0.03210863,0.01953236,-0.00573968,0.02348116,0.01501711,0.03552867,0.08113708,0.02404236,0.05759296,0.03579519,-0.02659431,-0.08922955,-0.01045249,0.00979521,-0.01642527,0.03277215,0.05208121,0.02503249,0.02055846,-0.05458114,-0.06657484,0.03545293,-0.0721481,-0.02406693,0.09640651,-0.0554143,-0.01826672,0.08314663,-0.03202068,0.00682502,-0.04044214,-0.03552215,-0.07403948,0.00987902,0.00929053,0.09954703,0.0859483,-0.05910752,0.0136685,-0.01499241,-0.06075672,-0.03190657,0.07575274,-0.0070037,-0.04864867,0.01464043,0.05366715,-0.0067408,-0.08327664,0.02587566,-0.01198971,0.01297141,0.00791809,0.07866663,-0.02440169,-0.02364347,-0.04336596,-0.00076932,0.01232538,-0.01408757,-0.01149586,-0.06034842,0.04516655,-0.06080397,-0.05172135,-0.03599874,-0.01472201,0.0003571,0.01987943,-0.06329829,-0.03156649,-0.0671592,0.0471492,0.02768841,-0.01644262,-0.02828605,-0.04626873,0.05352917,0.03170007,-0.0218576,-0.01568662,0.05656782,-0.00180814,0.00698837,0.1135151,-0.02947188,-0.04613115,0.10144499,0.03977618,-0.01388808,-0.03355213,0.01078931,0.09603521,-0.05861259,0.0766133,0.04877106,0.04074517,0.02234038,0.05939801,-0.03005236,0.01590597,-0.07768305,-0.20581619,-0.05191254,-0.02350107,0.00401106,0.07028113,-0.01262948,0.0507948,0.02926319,0.04887861,0.04826214,0.06886049,-0.02644418,-0.01962312,0.00060487,-0.05195228,-0.03267069,-0.09847626,0.02177672,-0.03587373,0.05530109,-0.03683411,0.04574012,-0.00780398,-0.04276481,0.00070604,-0.04876666,0.15383333,-0.02618143,0.00985158,-0.04263315,0.03200823,0.05082929,-0.01125509,0.01037474,0.01289961,-0.00724794,-0.04296577,-0.0255979,-0.12901957,-0.02371079,-0.09324303,0.01029655,0.00396136,-0.09421379,-0.04627402,0.02050734,-0.01613891,-0.0088231,-0.01371668,0.04393655,0.00650478,-0.02086195,0.06111294,0.02143237,0.06119401,-0.05196671,-0.08138355,0.00872225,-0.01980855,0.04283224,-0.01798883,-0.00563961,0.04116743,-0.01687842,0.01375994,0.02101831,0.04050435,-0.03382228,-0.00276613,0.00122402,-0.02067883,0.09857384,-0.03359012,0.02374295,0.01231217,0.025114,0.07665882,0.00152103,-0.00954542,-0.00575106,0.03563743,-0.09526768,0.01627668,0.04430999,0.00269109,0.01118102,0.06934072,0.03909331,-0.00225908,0.02556937,-0.00661126,-0.00939647,-0.03237975,0.00608902,-0.03695456,0.03824325,-0.25696006,0.01689976,-0.09033668,0.04537402,-0.01087974,-0.02107423,0.05390173,-0.01727682,-0.02601732,-0.0031481,0.0721707,0.05205589,0.03449218,-0.0820141,0.00841169,-0.0266296,-0.01394464,-0.04598546,0.02978815,-0.00097395,0.11195648,0.02126428,0.19469503,-0.00851593,-0.01992401,0.06442665,0.00436327,0.00607357,-0.00015742,0.03279905,-0.02027245,-0.01521823,0.10868087,0.00271138,0.01496758,0.01548936,-0.01526717,0.03471431,0.00992847,0.01369778,0.02226991,-0.05584506,-0.02134636,0.02249589,0.12694782,0.01254784,-0.02464741,-0.0578022,0.10020071,0.02768449,-0.08045606,-0.01666607,-0.05967934,-0.00284677,0.00333653,0.03313787,-0.04249881,0.01496492,-0.01196856,-0.03112854,0.01018454,-0.01845285,0.0264355,0.02370626,-0.03621167],"last_embed":{"hash":"lcc0bl","tokens":291}}},"text":null,"length":0,"last_read":{"hash":"lcc0bl","at":1753423496956},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#{29}","lines":[73,80],"size":920,"outlinks":[{"title":"Lottery sums software calculates sum-totals in any lotto game and generates the combinations.","target":"https://saliu.com/forum/HLINE.gif","line":5},{"title":"_**Back to index: Forum Lotto, Lottery, Sums, Wheeling, Software**_","target":"https://saliu.com/forum/index.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#[<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09109758,-0.03122609,-0.02031412,-0.04118024,-0.05675041,0.06177202,-0.02289241,-0.00597466,0.04756868,-0.00835236,0.02412438,-0.0361682,0.06335026,-0.02650807,-0.03319347,-0.02605351,-0.00437415,0.02525574,-0.03593199,-0.01034179,0.08576453,-0.06152347,-0.08216354,-0.10357691,0.08658524,0.04214426,-0.05274854,-0.0515877,-0.00781035,-0.20530742,0.02991098,0.03025129,0.00534878,-0.04989472,-0.07621883,-0.00791398,-0.02662454,0.04325272,-0.0406041,0.02352799,0.0265533,-0.00800948,0.00360301,-0.00378623,0.05161764,-0.05477559,0.0164281,0.04047073,0.04979821,0.01490277,-0.06604542,-0.01886116,0.00909906,0.02755319,0.05013821,0.02864282,0.06159058,0.09124042,0.01098525,0.04005161,0.02440213,0.08365712,-0.20512858,0.05274358,0.02918714,0.04880647,0.00581738,-0.0054189,0.0223347,0.04011087,-0.00205067,0.04014745,-0.00258864,0.04068908,0.0281706,-0.01811378,-0.05880032,-0.03306597,-0.029587,0.01769785,-0.05385013,0.02096821,0.00080224,0.02215028,-0.02577342,0.06092558,0.04489609,0.03209264,0.11292823,-0.06620443,0.01981021,0.02482284,0.04849257,0.0443411,0.02398939,-0.0106838,0.0472222,-0.03181994,0.02089303,0.11181346,0.01572113,-0.00401462,-0.00268524,-0.00856159,0.03866693,-0.07237255,-0.01856521,-0.03876665,-0.01187606,0.03310055,0.0498722,-0.01677573,0.00238056,-0.07991498,-0.02513024,-0.0551755,-0.01787301,-0.02968842,0.00982569,-0.01270719,-0.05206142,-0.0071257,0.02868257,0.01899325,0.0049712,0.01072727,0.01476295,0.08571296,0.01565014,0.05668453,0.06227604,0.04849349,-0.13785413,-0.04361505,-0.01237204,-0.00758542,0.04970367,-0.00735678,0.00941608,0.04788918,-0.01569356,-0.04329775,0.04050698,-0.09575964,-0.03568625,0.0933577,0.0021618,-0.03239898,0.02932168,-0.01392744,-0.00389131,-0.01317323,-0.03112885,-0.0705447,0.01021912,0.0172307,0.12374931,0.06615327,-0.04207735,0.01372866,-0.05294509,-0.06320459,-0.03261696,0.126463,-0.00105973,-0.11116286,0.00250015,0.06078835,-0.01675521,-0.10674575,-0.0001468,-0.02305917,-0.05329183,0.00877552,0.10163752,-0.01552249,-0.04354958,-0.0579632,-0.02194406,-0.00405337,-0.01104772,-0.04658182,-0.03658265,0.01808497,-0.05252278,-0.075615,-0.02480977,-0.03942863,-0.01971264,0.04228988,-0.03692247,-0.00949422,-0.07433739,-0.0185862,-0.03775886,-0.01763571,-0.0593132,-0.04262882,0.0564714,-0.03100837,0.01323235,-0.00264867,0.0207012,0.0012945,0.0104113,0.05767724,-0.03750961,-0.03341247,0.0823328,0.02461373,-0.03630565,0.02004308,0.04264323,0.04655148,-0.05465866,0.04517669,0.03276563,0.02513478,-0.00097835,0.05242208,-0.01973566,0.02769258,-0.09528147,-0.17275651,-0.04554624,-0.02644847,0.01570659,-0.01639223,-0.0221131,0.05176749,-0.0090188,0.01203649,0.05268684,0.12121213,-0.03363117,0.00147843,0.01712086,-0.02319029,-0.04973865,-0.07850253,-0.0279771,-0.03085023,0.04631591,0.00395635,-0.00249901,0.01315466,-0.06580862,-0.015137,-0.03278967,0.1149524,-0.0113336,-0.00604963,-0.00408462,0.07519046,-0.00114441,-0.02357461,-0.03606477,-0.00764242,0.04550053,-0.02946959,-0.00067069,-0.05947077,-0.00229579,-0.0738359,0.01213285,0.02438572,-0.07277427,-0.03831276,0.00164328,0.01661383,0.00861703,0.00977888,0.05254003,0.01530664,0.02803942,0.05299987,0.0290572,0.04284614,-0.03268198,-0.03119381,0.01501205,-0.02201064,0.04539789,-0.03335821,-0.05497729,0.02861194,-0.01139084,0.05486878,0.00858818,-0.00958611,-0.04962859,0.01080774,0.0085714,-0.00025818,0.08107123,0.00182527,0.05343252,-0.013993,0.0406826,0.08059681,-0.05205932,0.00610875,-0.01237359,-0.04943867,-0.09285379,0.04943185,0.07820538,0.06040281,0.08012991,0.07619952,0.01744312,-0.00306153,0.01203389,-0.00464891,0.00131062,-0.03517542,0.02715597,-0.00253398,0.07088924,-0.27467126,0.0262144,-0.03803711,0.0480644,0.00655921,-0.03940201,0.04890619,-0.04109123,-0.00855242,-0.03490738,0.06369515,0.00990306,0.02373953,-0.07544615,0.00398816,-0.01815255,0.04070346,-0.0338933,0.048501,0.0135865,0.05170364,0.03962621,0.24205765,0.01981008,0.00775455,0.02981494,0.0081058,0.00014093,0.0176497,0.05295794,0.01095766,0.00658595,0.0829195,0.00693,-0.0416566,0.04362558,-0.03753027,0.02595298,0.02006771,0.02223585,-0.03330189,-0.00610323,-0.02310852,0.02383223,0.10444108,0.00616452,-0.02974747,-0.10165868,0.07694551,0.04910024,-0.06955298,-0.04831189,-0.09425285,0.00194009,0.01672945,0.01729689,0.00105263,0.00051504,0.01499106,-0.02999627,0.02765925,-0.01357774,0.0539441,0.03848622,-0.02307741],"last_embed":{"hash":"2q6jnb","tokens":389}}},"text":null,"length":0,"last_read":{"hash":"2q6jnb","at":1753423497043},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#[<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>](https://saliu.com/content/lottery.html)","lines":[81,105],"size":3146,"outlinks":[{"title":"<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":3},{"title":"_**Lotto Lottery Software Systems**_","target":"https://saliu.com/LottoWin.htm","line":4},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":6},{"title":"_Users' Guide to_ _**MDIEditor And Lotto WE**_","target":"https://saliu.com/MDI-lotto-guide.html","line":8},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":10},{"title":"**Standard Deviation**","target":"https://saliu.com/deviation.html","line":11},{"title":"_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd/Even; Low/High Numbers**_","target":"https://saliu.com/strategy.html","line":12},{"title":"_**Software to Calculate Lottery Sums, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":13},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":14},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":15},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":16},{"title":"_**Lottery: Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":17},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":18},{"title":"Download all root sums for pick 2 3 4 5 lotteries, lotto, Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/forum/HLINE.gif","line":20},{"title":"Forums","target":"https://forums.saliu.com/","line":22},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":22},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":22},{"title":"Contents","target":"https://saliu.com/content/index.html","line":22},{"title":"Help","target":"https://saliu.com/Help.htm","line":22},{"title":"Home","target":"https://saliu.com/index.htm","line":22},{"title":"Software","target":"https://saliu.com/infodown.html","line":22},{"title":"Search","target":"https://saliu.com/Search.htm","line":22},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":22},{"title":"View root sums, sum-totals in lottery lotto: Powerball, Mega Millions, Euromillions, Pick-5 Quinto.","target":"https://saliu.com/forum/HLINE.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#[<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>](https://saliu.com/content/lottery.html)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09177143,-0.03313291,-0.02395959,-0.04238281,-0.05692204,0.06278782,-0.02297243,-0.00679744,0.04699399,-0.00715623,0.02499285,-0.03510354,0.0610239,-0.02836327,-0.03145523,-0.0281264,-0.00396791,0.02526043,-0.0363156,-0.01235935,0.08370416,-0.05836445,-0.08355311,-0.10215083,0.08475737,0.04251508,-0.05086637,-0.05100659,-0.00659713,-0.2031686,0.03005097,0.02984576,0.00434116,-0.04996851,-0.07696302,-0.00892933,-0.02490304,0.04336802,-0.04070228,0.02382451,0.02930461,-0.0096698,0.00394472,-0.00357627,0.05108293,-0.05192555,0.01526747,0.04057009,0.04842405,0.01645012,-0.06668875,-0.01550051,0.00832906,0.02719812,0.05027049,0.02973609,0.06395882,0.09432074,0.00860577,0.03984198,0.02356103,0.08246174,-0.20278367,0.05592867,0.02679337,0.0482231,0.00688525,-0.00551751,0.02573542,0.03788616,-0.00450595,0.04053781,-0.00110076,0.04291969,0.03024843,-0.01616284,-0.0610162,-0.03602936,-0.02808191,0.02049305,-0.05379767,0.02441945,0.00259106,0.02267316,-0.02575023,0.06044337,0.04454127,0.03076978,0.11035342,-0.068097,0.02135317,0.02737465,0.04408946,0.04499853,0.02420277,-0.01115418,0.04553328,-0.0321073,0.02223466,0.11057769,0.0152044,-0.00454269,0.00157398,-0.00772087,0.03967671,-0.07107881,-0.02261539,-0.03789053,-0.00814482,0.03229265,0.04830716,-0.0178029,-0.0015674,-0.07864772,-0.02606019,-0.05474385,-0.02227044,-0.03105573,0.0120664,-0.01322451,-0.05038685,-0.00813125,0.02577822,0.0185055,0.00053166,0.00964468,0.01534672,0.0884577,0.01480513,0.05433088,0.06305493,0.04861041,-0.14009109,-0.04386524,-0.01327374,-0.00789313,0.04773448,-0.0082366,0.00872214,0.04965267,-0.01634086,-0.04346624,0.04244313,-0.09602942,-0.03782892,0.09332467,0.00118368,-0.03244551,0.02955278,-0.01415891,-0.00269874,-0.01419133,-0.03128395,-0.07157485,0.01039333,0.01563982,0.12078916,0.06619053,-0.04109376,0.01494247,-0.0553405,-0.06469157,-0.03210993,0.12815638,-0.00329572,-0.10998617,0.00281953,0.05720183,-0.0169713,-0.10786762,-0.00119755,-0.02336494,-0.05585276,0.01321597,0.10491672,-0.01514984,-0.04561589,-0.05648056,-0.02211617,-0.00466518,-0.010995,-0.05216815,-0.03605282,0.01709436,-0.0550399,-0.0766372,-0.02447559,-0.03634372,-0.02234235,0.0422905,-0.03845686,-0.00665958,-0.07367393,-0.01890148,-0.038981,-0.01754867,-0.05878266,-0.0429524,0.05746222,-0.03160214,0.01225506,-0.00061344,0.01863862,0.00319896,0.01008042,0.05594281,-0.03495611,-0.03223783,0.08382922,0.02491124,-0.03586306,0.02084414,0.03960828,0.04586571,-0.04936843,0.04446097,0.03545226,0.02359999,-0.00111097,0.05264063,-0.01810782,0.02738931,-0.09401085,-0.17174727,-0.04264582,-0.0281744,0.01286448,-0.01622116,-0.02327479,0.04978351,-0.01158867,0.01191226,0.05500238,0.12136351,-0.03486769,0.00387715,0.0212639,-0.02224524,-0.0477002,-0.07719403,-0.02881931,-0.0324216,0.04591508,0.0046732,-0.00174949,0.01338227,-0.06669766,-0.01530118,-0.03244006,0.11380221,-0.01481846,-0.00531757,-0.0048731,0.07510579,-0.00277803,-0.02347055,-0.03774428,-0.00743241,0.0452239,-0.03052168,-0.001229,-0.05695684,-0.00250331,-0.07583002,0.01037543,0.02506328,-0.07096574,-0.03747717,0.00313768,0.01590313,0.01103874,0.00739312,0.05018886,0.01466218,0.0291713,0.05507433,0.03192502,0.04165165,-0.03550118,-0.03029199,0.01570397,-0.02221925,0.04647221,-0.03428046,-0.05422099,0.02571514,-0.01243886,0.0536763,0.00947485,-0.01135115,-0.04985016,0.01234203,0.00884384,-0.00121993,0.07925057,0.00338978,0.05335157,-0.01620832,0.03902148,0.08274347,-0.054527,0.00718958,-0.01118311,-0.04912149,-0.09363494,0.04874707,0.07858918,0.06086538,0.0796502,0.07726877,0.01926489,-0.00479593,0.01106819,-0.00308459,0.00118981,-0.03432529,0.02891369,-0.00457235,0.06749643,-0.27490574,0.02771512,-0.03994018,0.0488461,0.00625679,-0.04245699,0.04833785,-0.04030513,-0.00617058,-0.03955545,0.06609904,0.00768552,0.0236341,-0.07168546,0.00429688,-0.01738126,0.03959878,-0.03261353,0.0486671,0.01619798,0.05269604,0.03827193,0.24539405,0.01644905,0.00777698,0.03096999,0.00797065,-0.00157234,0.01560375,0.05202121,0.0125752,0.00504186,0.08321364,0.00616745,-0.04205078,0.04582721,-0.0372521,0.02999155,0.02077949,0.02127719,-0.03278909,-0.00838547,-0.02138579,0.02375757,0.10423081,0.01058382,-0.03138718,-0.10266767,0.07549315,0.04796733,-0.06552538,-0.04680562,-0.09340729,0.00216249,0.01603559,0.01658472,0.00201,0.00182155,0.0172936,-0.02537147,0.02936529,-0.01165167,0.05413572,0.04034123,-0.02236928],"last_embed":{"hash":"1l8pos8","tokens":389}}},"text":null,"length":0,"last_read":{"hash":"1l8pos8","at":1753423497176},"key":"notes/saliu/Lottery Sums, Lotto Root-Sum, Charts, Software.md#Lottery Sums, Lotto Root-Sum, Charts, Software#[<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>](https://saliu.com/content/lottery.html)#{1}","lines":[83,105],"size":3025,"outlinks":[{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":1},{"title":"_**Lotto Lottery Software Systems**_","target":"https://saliu.com/LottoWin.htm","line":2},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":4},{"title":"_Users' Guide to_ _**MDIEditor And Lotto WE**_","target":"https://saliu.com/MDI-lotto-guide.html","line":6},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":8},{"title":"**Standard Deviation**","target":"https://saliu.com/deviation.html","line":9},{"title":"_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd/Even; Low/High Numbers**_","target":"https://saliu.com/strategy.html","line":10},{"title":"_**Software to Calculate Lottery Sums, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":11},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":13},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":14},{"title":"_**Lottery: Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":15},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":16},{"title":"Download all root sums for pick 2 3 4 5 lotteries, lotto, Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/forum/HLINE.gif","line":18},{"title":"Forums","target":"https://forums.saliu.com/","line":20},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":20},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":20},{"title":"Contents","target":"https://saliu.com/content/index.html","line":20},{"title":"Help","target":"https://saliu.com/Help.htm","line":20},{"title":"Home","target":"https://saliu.com/index.htm","line":20},{"title":"Software","target":"https://saliu.com/infodown.html","line":20},{"title":"Search","target":"https://saliu.com/Search.htm","line":20},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":20},{"title":"View root sums, sum-totals in lottery lotto: Powerball, Mega Millions, Euromillions, Pick-5 Quinto.","target":"https://saliu.com/forum/HLINE.gif","line":22}],"class_name":"SmartBlock"},
