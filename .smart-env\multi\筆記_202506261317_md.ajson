"smart_sources:筆記/202506261317.md": {"path":"筆記/202506261317.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"18xbisz","at":1750924942761},"class_name":"SmartSource","last_import":{"mtime":1750919779010,"size":47595,"at":1750924942767,"hash":"18xbisz"},"blocks":{"###圖表解讀":[2,403],"###圖表解讀#{1}":[4,5],"###圖表解讀#{2}":[6,12],"###圖表解讀#{3}":[13,19],"###圖表解讀#{4}":[20,403],"#---frontmatter---":[22,398],"#Gemini":[404,734],"#Gemini#{1}":[406,411],"#Gemini#{2}":[412,412],"#Gemini#{3}":[413,413],"#Gemini#{4}":[414,414],"#Gemini#{5}":[415,415],"#Gemini#{6}":[416,417],"#Gemini#{7}":[418,419],"#Gemini##程式碼變更":[420,577],"#Gemini##程式碼變更#{1}":[422,577],"#Gemini##如何使用":[578,734],"#Gemini##如何使用#{1}":[580,580],"#Gemini##如何使用#{2}":[581,583],"#Gemini##如何使用#{3}":[584,588],"#Gemini##如何使用#{4}":[589,734],"#helay Linlady":[735,740],"#helay Linlady#{1}":[737,740],"#Gemini[2]":[741,899],"#Gemini[2]#{1}":[743,748],"#Gemini[2]#{2}":[749,749],"#Gemini[2]#{3}":[750,750],"#Gemini[2]#{4}":[751,752],"#Gemini[2]##解決方案":[753,765],"#Gemini[2]##解決方案#{1}":[755,756],"#Gemini[2]##解決方案#{2}":[757,757],"#Gemini[2]##解決方案#{3}":[758,759],"#Gemini[2]##解決方案#{4}":[760,761],"#Gemini[2]##解決方案#{5}":[762,762],"#Gemini[2]##解決方案#{6}":[763,763],"#Gemini[2]##解決方案#{7}":[764,765],"#Gemini[2]##程式碼修改建議":[766,853],"#Gemini[2]##程式碼修改建議#{1}":[768,853],"#Gemini[2]##說明修改內容":[854,870],"#Gemini[2]##說明修改內容#{1}":[856,858],"#Gemini[2]##說明修改內容#{2}":[859,862],"#Gemini[2]##說明修改內容#{3}":[863,866],"#Gemini[2]##說明修改內容#{4}":[867,870],"#Gemini[2]##如何使用這些修改":[871,899],"#Gemini[2]##如何使用這些修改#{1}":[873,873],"#Gemini[2]##如何使用這些修改#{2}":[874,874],"#Gemini[2]##如何使用這些修改#{3}":[875,875],"#Gemini[2]##如何使用這些修改#{4}":[876,877],"#Gemini[2]##如何使用這些修改#{5}":[878,899]},"outlinks":[{"title":"A0032_0.0.0.3_A0015_0007_05_0.0.0.8_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\G3winscumsPatterns\\src\\A0032_0.0.0.3_A0015_0007_05_0.0.0.8_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl \"f:\\work\\JuliaProject\\G3winscumsPatterns\\src\\A0032_0.0.0.3_A0015_0007_05_0.0.0.8_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl\"","line":422},{"title":" # 排名 5 (OOS 獲利率 132.46%, 成本最低) (gx_size = 3, hit_rate = 0.280, min_num_instances = 10, max_num_instances = 9999), 從此參數組合名單中，發現可能的缺失，因為我們沒有將const ASSOCIATED_GX_WINS_LOOKBACK = 8和const ASSOCIATED_PATTERN_LOOKBACK = 9當作參數列入考慮和紀錄，他日如果這2個變數有更改則我們所記錄的accepted_params將產生錯誤結果，是否應調整相關代碼將此情形列入考慮\n\n![","target":"https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/scoop/apps/vscodium/1.98.2.25072/data/extensions/google.geminicodeassist-2.37.0-universal/images/gemini_color.svg","line":737},{"title":"A0032_0.0.0.3_A0015_0007_05_0.0.0.9_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\G3winscumsPatterns\\src\\A0032_0.0.0.3_A0015_0007_05_0.0.0.9_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl \"f:\\work\\JuliaProject\\G3winscumsPatterns\\src\\A0032_0.0.0.3_A0015_0007_05_0.0.0.9_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl\"","line":768}],"key":"筆記/202506261317.md"},