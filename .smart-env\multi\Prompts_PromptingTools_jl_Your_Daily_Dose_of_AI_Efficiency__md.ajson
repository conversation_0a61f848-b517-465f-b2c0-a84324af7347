"smart_sources:Prompts/PromptingTools.jl Your Daily Dose of AI Efficiency..md": {"path":"Prompts/PromptingTools.jl Your Daily Dose of AI Efficiency..md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08992464,-0.00453997,-0.03015161,-0.01423918,-0.02804414,0.01859217,-0.02250662,0.04061864,0.04162338,-0.01043562,-0.00228535,-0.00604259,0.02648568,0.09417358,0.01205247,0.00078858,-0.02837974,-0.01114004,-0.0967266,-0.01549096,0.09770115,-0.01013418,0.02096435,-0.05488065,-0.01160223,0.03204443,0.0139669,-0.04250998,-0.02238145,-0.21027373,0.03098463,-0.01960773,0.03483203,0.01124594,-0.08298589,0.00992512,-0.0094003,0.04969605,-0.01764211,-0.0022205,0.02145416,0.00728373,-0.00016032,-0.0411277,-0.00923619,-0.09091958,-0.03760201,0.02783829,-0.00885065,-0.01489236,-0.01341936,-0.02620477,0.06323816,0.00394641,0.02823139,-0.01973304,0.02045869,0.08115441,0.07277098,-0.00484855,-0.00134028,0.02346787,-0.16528144,0.13628489,-0.04365265,-0.01297269,-0.00978208,0.00394163,0.03774408,0.04760291,-0.0404281,0.00965104,0.02671009,0.02851766,-0.00417276,-0.04581491,0.00518783,-0.01329293,0.02489968,-0.05850051,-0.01957209,-0.04781985,-0.02842274,-0.02339762,-0.0325499,0.01762805,-0.02003759,-0.01116169,0.08771204,0.0075465,-0.02088447,-0.07267313,0.02728957,0.04103964,-0.03246368,-0.00894043,-0.00643321,-0.03037925,-0.14245787,0.13285792,-0.00977928,0.00153403,-0.01533678,-0.02626375,0.02295428,-0.09214682,-0.02547532,-0.02190576,-0.02463917,0.0147277,-0.02117451,-0.03207871,0.01206631,-0.05370358,-0.01501541,0.04684803,0.07963967,0.00568968,0.00834394,-0.01019875,0.01074311,-0.01765121,0.03632748,0.03167461,0.00591307,0.01528471,0.04253833,0.05177441,-0.0123026,0.03416435,0.0685083,0.01597273,-0.07098383,-0.01706929,0.01652335,0.05253658,0.00747534,-0.06157932,0.02528634,-0.03878819,0.00545293,0.00124053,-0.00148565,-0.09342058,0.03387329,0.09361386,0.00848413,0.00143239,0.01849932,-0.10083592,0.02811516,0.05327211,-0.00841983,-0.04833832,0.06031647,0.04837606,0.1052852,0.09415254,0.00519095,0.00450638,-0.09849632,-0.03270851,-0.02443022,0.08678365,0.01817202,-0.06083922,-0.07003584,-0.0241423,-0.04742333,-0.0285356,0.01966948,0.03608758,-0.06273599,0.03767008,0.10041451,0.01434343,-0.03082042,0.03279315,0.01812533,0.02429822,0.04069932,-0.04595618,-0.01316691,0.00399813,-0.00266343,-0.02313453,0.01343129,-0.06212774,-0.03391792,0.00724528,-0.08241033,0.04370814,0.04891082,-0.00227163,0.00308356,-0.02109655,0.0213585,-0.04487627,0.02959949,-0.0239203,0.07630544,-0.00368804,0.01833223,0.00943252,-0.00807337,-0.01426038,0.07249622,-0.04241077,0.09182804,0.05013856,-0.09151465,-0.03650327,0.02634673,-0.01221783,-0.04275487,0.00913425,-0.01121567,-0.02049736,-0.00912109,0.0523046,0.0086569,0.00762882,-0.04620356,-0.23350146,0.00323514,0.0405373,-0.08137661,0.07168057,-0.10873049,0.05081772,-0.07786694,0.00045679,0.09357219,0.09196765,-0.04252641,0.03478478,-0.00929232,0.02796934,0.00849057,-0.0018231,0.03640531,-0.02581453,0.00099811,0.03100283,0.01776679,0.02844426,-0.09042128,0.01617145,-0.01689275,0.12938859,0.00294173,0.05603209,-0.06593312,0.00767297,-0.00307237,-0.00030742,-0.14407107,-0.00417431,0.01916986,-0.00833275,0.04786461,0.04781658,0.02730139,-0.03463349,0.02914336,-0.02639206,-0.11455002,-0.02306137,-0.03084943,-0.02498586,-0.04412245,-0.07279845,-0.03333392,0.02587154,-0.03395748,0.04502661,0.0389386,0.03556751,-0.06260157,-0.04557676,-0.04011743,-0.01585987,-0.00625148,0.04356443,-0.00905855,-0.0157928,-0.03035459,0.03001499,0.00906908,0.01093268,-0.00251655,0.04775099,0.0176845,-0.03685172,0.13090362,-0.00611487,0.09573667,0.04636304,0.01959921,-0.03262491,-0.09202175,0.0174208,-0.03806142,0.00424766,0.00266401,0.02122108,0.07297785,0.02725824,0.05119795,0.02388853,0.00085162,0.06543994,0.01231715,-0.01800876,0.0259868,-0.01910244,-0.02353778,0.03984715,-0.04136778,-0.25527352,0.0636407,-0.01237222,0.0532673,-0.0022877,0.05348624,0.02941103,-0.00612635,-0.0385109,0.06878657,-0.07714313,0.05824911,0.00789846,0.01103831,0.06570518,-0.01721064,0.03176117,0.00566957,0.01664515,-0.02639107,0.00818049,0.02108334,0.21724452,-0.01701357,0.02286599,0.02221118,0.00367496,-0.05420406,0.10034107,-0.02916342,0.00219959,0.01633874,0.09434937,0.00217546,0.07146506,0.00467089,-0.05083352,-0.03624249,0.00923159,0.00953398,-0.01243858,0.02954261,-0.03729097,0.00550812,0.04813079,-0.02315556,-0.02377048,-0.05431502,-0.03456606,0.06774668,-0.0124826,-0.02145129,-0.02499375,0.02934199,-0.01768048,0.00793497,0.08717261,0.01628663,-0.05382781,0.00004744,0.04005387,-0.04082794,0.08056564,0.00503259,-0.05539581],"last_embed":{"hash":"5d51044644a4c9b6374c480c84d7501c3f9d02becdfa1780a9d998467f982daa","tokens":494}}},"last_read":{"hash":"5d51044644a4c9b6374c480c84d7501c3f9d02becdfa1780a9d998467f982daa","at":1745995227578},"class_name":"SmartSource2","outlinks":[{"title":"docs","target":"https://svilupp.github.io/PromptingTools.jl/dev/ \"https://svilupp.github.io/PromptingTools.jl/dev/\"","line":13},{"title":"Creating OpenAI API Key","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#creating-openai-api-key \"#creating-openai-api-key\"","line":19},{"title":"Advanced Examples","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#advanced-examples \"#advanced-examples\"","line":69},{"title":"PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#promptingtoolsjl-your-daily-dose-of-ai-efficiency \"#promptingtoolsjl-your-daily-dose-of-ai-efficiency\"","line":73},{"title":"Quick Start with `@ai_str` and Easy Templating","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#quick-start-with-ai_str-and-easy-templating \"#quick-start-with-ai_str-and-easy-templating\"","line":74},{"title":"Table of Contents","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#table-of-contents \"#table-of-contents\"","line":75},{"title":"Why PromptingTools.jl","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#why-promptingtoolsjl \"#why-promptingtoolsjl\"","line":76},{"title":"Advanced Examples","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#advanced-examples \"#advanced-examples\"","line":77},{"title":"`ai*` Functions Overview","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#ai-functions-overview \"#ai-functions-overview\"","line":78},{"title":"Seamless Integration Into Your Workflow","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#seamless-integration-into-your-workflow \"#seamless-integration-into-your-workflow\"","line":79},{"title":"Advanced Prompts / Conversations","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#advanced-prompts--conversations \"#advanced-prompts--conversations\"","line":80},{"title":"Templated Prompts","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#templated-prompts \"#templated-prompts\"","line":81},{"title":"Asynchronous Execution","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#asynchronous-execution \"#asynchronous-execution\"","line":82},{"title":"Model Aliases","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#model-aliases \"#model-aliases\"","line":83},{"title":"Embeddings","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#embeddings \"#embeddings\"","line":84},{"title":"Classification","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#classification \"#classification\"","line":85},{"title":"Routing to Defined Categories","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#routing-to-defined-categories \"#routing-to-defined-categories\"","line":86},{"title":"Data Extraction","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#data-extraction \"#data-extraction\"","line":87},{"title":"OCR and Image Comprehension","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#ocr-and-image-comprehension \"#ocr-and-image-comprehension\"","line":88},{"title":"Experimental Agent Workflows / Output Validation with `airetry!`","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#experimental-agent-workflows--output-validation-with-airetry \"#experimental-agent-workflows--output-validation-with-airetry\"","line":89},{"title":"Using Ollama models","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#using-ollama-models \"#using-ollama-models\"","line":90},{"title":"Using MistralAI API and other OpenAI-compatible APIs","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#using-mistralai-api-and-other-openai-compatible-apis \"#using-mistralai-api-and-other-openai-compatible-apis\"","line":91},{"title":"Using Anthropic Models","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#using-anthropic-models \"#using-anthropic-models\"","line":92},{"title":"More Examples","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#more-examples \"#more-examples\"","line":93},{"title":"Package Interface","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#package-interface \"#package-interface\"","line":94},{"title":"Frequently Asked Questions","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#frequently-asked-questions \"#frequently-asked-questions\"","line":95},{"title":"Why OpenAI","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#why-openai \"#why-openai\"","line":96},{"title":"What if I cannot access OpenAI?","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#what-if-i-cannot-access-openai \"#what-if-i-cannot-access-openai\"","line":97},{"title":"Data Privacy and OpenAI","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#data-privacy-and-openai \"#data-privacy-and-openai\"","line":98},{"title":"Creating OpenAI API Key","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#creating-openai-api-key \"#creating-openai-api-key\"","line":99},{"title":"Setting OpenAI Spending Limits","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#setting-openai-spending-limits \"#setting-openai-spending-limits\"","line":100},{"title":"How much does it cost? Is it worth paying for?","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#how-much-does-it-cost-is-it-worth-paying-for \"#how-much-does-it-cost-is-it-worth-paying-for\"","line":101},{"title":"Configuring the Environment Variable for API Key","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#configuring-the-environment-variable-for-api-key \"#configuring-the-environment-variable-for-api-key\"","line":102},{"title":"Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`)","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#understanding-the-api-keyword-arguments-in-aigenerate-api_kwargs \"#understanding-the-api-keyword-arguments-in-aigenerate-api_kwargs\"","line":103},{"title":"Instant Access from Anywhere","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#instant-access-from-anywhere \"#instant-access-from-anywhere\"","line":104},{"title":"Open Source Alternatives","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#open-source-alternatives \"#open-source-alternatives\"","line":105},{"title":"Setup Guide for Ollama","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#setup-guide-for-ollama \"#setup-guide-for-ollama\"","line":106},{"title":"How would I fine-tune a model?","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#how-would-i-fine-tune-a-model \"#how-would-i-fine-tune-a-model\"","line":107},{"title":"Roadmap","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#roadmap \"#roadmap\"","line":108},{"title":"examples/","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/examples/ \"examples/\"","line":278},{"title":"Templated Prompts","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#templated-prompts \"#templated-prompts\"","line":346},{"title":" Info: Tokens: 1141 @ Cost: \\$0.0117 in 2.2 seconds\n# AIMessage(\"The image shows a logo consisting of the word \"julia\" written in lowercase\")\n```\n\nOr you can do an OCR of a screenshot. Let's transcribe some SQL code from a screenshot (no more re-typing!), we use a template `:OCRTask`:\n\n```julia\n# Screenshot of some SQL code\nimage_url = \"https://www.sqlservercentral.com/wp-content/uploads/legacy/8755f69180b7ac7ee76a69ae68ec36872a116ad4/24622.png\"\nmsg = aiscan(:OCRTask; image_url, model=\"gpt4v\", task=\"Transcribe the SQL code in the image.\", api_kwargs=(; max_tokens=2500))\n\n# [ Info: Tokens: 362 @ Cost: \\$0.0045 in 2.5 seconds\n# AIMessage(\"```sql\n# update Orders <continue>\n```\n\nYou can add syntax highlighting of the outputs via Markdown\n\n```julia\nusing Markdown\nmsg.content |> Markdown.parse\n```\n\n## Experimental Agent Workflows / Output Validation with `airetry!`\n\nThis is an experimental feature, so you have to import it explicitly:\n\n```julia\nusing PromptingTools.Experimental.AgentTools\n```\n\nThis module offers \"lazy\" counterparts to the `ai...` functions, so you can use them in a more controlled way, eg, `aigenerate` -> `AIGenerate` (notice the CamelCase), which has exactly the same arguments except it generates only when `run!` is called.\n\nFor example:\n\n```julia\nout = AIGenerate(\"Say hi!\"; model=\"gpt4t\")\nrun!(out)\n```\n\nHow is it useful? We can use the same \"inputs\" for repeated calls, eg, when we want to validate or regenerate some outputs. We have a function `airetry` to help us with that.\n\nThe signature of `airetry!` is `airetry!(condition_function, aicall::AICall, feedback_function)`. It evaluates the condition `condition_function` on the `aicall` object (eg, we evaluate `f_cond(aicall) -> Bool`). If it fails, we call `feedback_function` on the `aicall` object to provide feedback for the AI model (eg, `f_feedback(aicall) -> String`) and repeat the process until it passes or until `max_retries` value is exceeded.\n\nWe can catch API failures (no feedback needed, so none is provided)\n\n```julia\n# API failure because of a non-existent model\n# RetryConfig allows us to change the \"retry\" behaviour of any lazy call\nout = AIGenerate(\"say hi!\"; config = RetryConfig(; catch_errors = true),\n    model = \"NOTEXIST\")\nrun!(out) # fails\n\n# we ask to wait 2s between retries and retry 2 times (can be set in `config` in aicall as well)\nairetry!(isvalid, out; retry_delay = 2, max_retries = 2)\n```\n\nOr we can validate some outputs (eg, its format, its content, etc.)\n\nWe'll play a color guessing game (I'm thinking \"yellow\"):\n\n```julia\n# Notice that we ask for two samples (`n_samples=2`) at each attempt (to improve our chances). \n# Both guesses are scored at each time step, and the best one is chosen for the next step.\n# And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!\nout = AIGenerate(\n    \"Guess what color I'm thinking. It could be: blue, red, black, white, yellow. Answer with 1 word only\";\n    verbose = false,\n    config = RetryConfig(; n_samples = 2), api_kwargs = (; n = 2))\nrun!(out)\n\n## Check that the output is 1 word only, third argument is the feedback that will be provided if the condition fails\n## Notice: functions operate on `aicall` as the only argument. We can use utilities like `last_output` and `last_message` to access the last message and output in the conversation.\nairetry!(x -> length(split(last_output(x), r\" |\\\\.\")) == 1, out,\n    \"You must answer with 1 word only.\")\n\n# Note: you could also use the do-syntax, eg, \nairetry!(out, \"You must answer with 1 word only.\") do aicall\n    length(split(last_output(aicall), r\" |\\\\.\")) == 1\nend\n```\n\nYou can place multiple `airetry!` calls in a sequence. They will keep retrying until they run out of maximum AI calls allowed (`max_calls`) or maximum retries (`max_retries`).\n\nSee the docs for more complex examples and usage tips (`?airetry`). We leverage Monte Carlo Tree Search (MCTS) to optimize the sequence of retries, so it's a very powerful tool for building robust AI workflows (inspired by [Language Agent Tree Search paper","target":"https://arxiv.org/abs/2310.04406 \"https://arxiv.org/abs/2310.04406\"","line":427},{"title":"DSPy Assertions paper","target":"https://arxiv.org/abs/2312.13382 \"https://arxiv.org/abs/2312.13382\"","line":511},{"title":"Ollama.ai","target":"https://ollama.ai/ \"https://ollama.ai/\"","line":515},{"title":"Setup Guide for Ollama","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#setup-guide-for-ollama \"#setup-guide-for-ollama\"","line":548},{"title":"Fireworks.ai","target":"https://app.fireworks.ai/ \"https://app.fireworks.ai/\"","line":569},{"title":"Perplexity.ai","target":"https://docs.perplexity.ai/ \"https://docs.perplexity.ai/\"","line":569},{"title":"examples/","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/examples/ \"examples/\"","line":608},{"title":"Instructor","target":"https://github.com/jxnl/instructor \"https://github.com/jxnl/instructor\"","line":620},{"title":"Setup Guide for Ollama","target":"https://file+.vscode-resource.vscode-cdn.net/f%3A/work/PromptingTools.jl%20-%20%E8%A4%87%E8%A3%BD/README.md#setup-guide-for-ollama \"#setup-guide-for-ollama\"","line":673},{"title":"Ollama.ai","target":"https://ollama.ai/ \"https://ollama.ai/\"","line":673},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance \"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance\"","line":688},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data \"https://platform.openai.com/docs/models/how-we-use-your-data\"","line":690},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data \"https://platform.openai.com/docs/models/how-we-use-your-data\"","line":694},{"title":"Data usage for consumer services FAQ","target":"https://help.openai.com/en/articles/7039943-data-usage-for-consumer-services-faq \"https://help.openai.com/en/articles/7039943-data-usage-for-consumer-services-faq\"","line":695},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance \"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance\"","line":696},{"title":"OpenAI","target":"https://platform.openai.com/signup \"https://platform.openai.com/signup\"","line":702},{"title":"API Key page","target":"https://platform.openai.com/account/api-keys \"https://platform.openai.com/account/api-keys\"","line":703},{"title":"OpenAI Documentation","target":"https://platform.openai.com/docs/quickstart?context=python \"https://platform.openai.com/docs/quickstart?context=python\"","line":708},{"title":"Visual tutorial","target":"https://www.maisieai.com/help/how-to-get-an-openai-api-key-for-chatgpt \"https://www.maisieai.com/help/how-to-get-an-openai-api-key-for-chatgpt\"","line":709},{"title":"OpenAI Billing","target":"https://platform.openai.com/account/billing \"https://platform.openai.com/account/billing\"","line":717},{"title":"OpenAI Forum","target":"https://community.openai.com/t/how-to-set-a-price-limit/13086 \"https://community.openai.com/t/how-to-set-a-price-limit/13086\"","line":724},{"title":"OpenAI Pricing per 1000 tokens","target":"https://openai.com/pricing \"https://openai.com/pricing\"","line":740},{"title":"OpenAI Guide","target":"https://platform.openai.com/docs/quickstart?context=python \"https://platform.openai.com/docs/quickstart?context=python\"","line":770},{"title":"OpenAI API reference","target":"https://platform.openai.com/docs/guides/text-generation/chat-completions-api \"https://platform.openai.com/docs/guides/text-generation/chat-completions-api\"","line":774},{"title":"Ollama.ai","target":"https://ollama.ai/ \"https://ollama.ai/\"","line":791},{"title":"here","target":"https://ollama.ai/download \"https://ollama.ai/download\"","line":797},{"title":"Ollama Library","target":"https://ollama.ai/library \"https://ollama.ai/library\"","line":803},{"title":"Ollama.ai","target":"https://ollama.ai/ \"https://ollama.ai/\"","line":809},{"title":"Axolotl","target":"https://github.com/OpenAccess-AI-Collective/axolotl \"https://github.com/OpenAccess-AI-Collective/axolotl\"","line":820},{"title":"JuliaLLMLeaderboard Finetuning experiment","target":"https://github.com/svilupp/Julia-LLM-Leaderboard/blob/main/experiments/cheater-7b-finetune/README.md \"https://github.com/svilupp/Julia-LLM-Leaderboard/blob/main/experiments/cheater-7b-finetune/README.md\"","line":820},{"title":"Jarvislabs.ai","target":"https://jarvislabs.ai/templates/axolotl \"https://jarvislabs.ai/templates/axolotl\"","line":820},{"title":"PromptingTools.jl GitHub repository","target":"https://github.com/svilupp/PromptingTools.jl \"https://github.com/svilupp/PromptingTools.jl\"","line":834}],"blocks":{"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"":[1,840],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#{1}":[3,14],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Quick Start with `@ai_str` and Easy Templating":[15,70],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Quick Start with `@ai_str` and Easy Templating#{1}":[17,70],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Table of Contents":[71,109],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Table of Contents#{1}":[73,109],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl":[110,122],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl#{1}":[112,115],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl#{2}":[116,116],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl#{3}":[117,117],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl#{4}":[118,118],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl#{5}":[119,119],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl#{6}":[120,120],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Why PromptingTools.jl#{7}":[121,122],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples":[123,449],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview":[125,174],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{1}":[127,134],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{2}":[135,135],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{3}":[136,136],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{4}":[137,137],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{5}":[138,138],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{6}":[139,139],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{7}":[140,140],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{8}":[141,142],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{9}":[143,146],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{10}":[147,147],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{11}":[148,148],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{12}":[149,149],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{13}":[150,150],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{14}":[151,152],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#`ai*` Functions Overview#{15}":[153,174],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Seamless Integration Into Your Workflow":[175,188],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Seamless Integration Into Your Workflow#{1}":[177,188],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Advanced Prompts / Conversations":[189,220],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Advanced Prompts / Conversations#{1}":[191,220],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Templated Prompts":[221,279],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Templated Prompts#{1}":[223,279],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Asynchronous Execution":[280,290],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Asynchronous Execution#{1}":[282,290],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Model Aliases":[291,305],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Model Aliases#{1}":[293,305],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Embeddings":[306,325],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Embeddings#{1}":[308,325],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Classification":[326,347],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Classification#{1}":[328,347],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Routing to Defined Categories":[348,368],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Routing to Defined Categories#{1}":[350,368],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Data Extraction":[369,418],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#Data Extraction#{1}":[371,418],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#OCR and Image Comprehension":[419,449],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Advanced Examples#OCR and Image Comprehension#{1}":[421,449],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`":[450,609],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#{1}":[452,512],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#Using Ollama models":[513,549],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#Using Ollama models#{1}":[515,549],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#Using MistralAI API and other OpenAI-compatible APIs":[550,581],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#Using MistralAI API and other OpenAI-compatible APIs#{1}":[552,581],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#Using Anthropic Models":[582,603],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#Using Anthropic Models#{1}":[584,603],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#More Examples":[604,609],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Experimental Agent Workflows / Output Validation with `airetry!`#More Examples#{1}":[606,609],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Package Interface":[610,664],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Package Interface#{1}":[612,613],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Package Interface#{2}":[614,614],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Package Interface#{3}":[615,615],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Package Interface#{4}":[616,617],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Package Interface#{5}":[618,664],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions":[665,821],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Why OpenAI":[667,674],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Why OpenAI#{1}":[669,674],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#What if I cannot access OpenAI?":[675,681],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#What if I cannot access OpenAI?#{1}":[677,678],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#What if I cannot access OpenAI?#{2}":[679,679],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#What if I cannot access OpenAI?#{3}":[680,681],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Data Privacy and OpenAI":[682,697],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Data Privacy and OpenAI#{1}":[684,693],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Data Privacy and OpenAI#{2}":[694,694],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Data Privacy and OpenAI#{3}":[695,695],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Data Privacy and OpenAI#{4}":[696,697],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key":[698,712],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{1}":[700,701],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{2}":[702,702],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{3}":[703,703],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{4}":[704,705],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{5}":[706,707],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{6}":[708,708],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{7}":[709,710],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Creating OpenAI API Key#{8}":[711,712],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setting OpenAI Spending Limits":[713,725],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setting OpenAI Spending Limits#{1}":[715,716],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setting OpenAI Spending Limits#{2}":[717,717],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setting OpenAI Spending Limits#{3}":[718,719],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setting OpenAI Spending Limits#{4}":[720,723],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setting OpenAI Spending Limits#{5}":[724,725],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How much does it cost? Is it worth paying for?":[726,741],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How much does it cost? Is it worth paying for?#{1}":[728,739],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How much does it cost? Is it worth paying for?#{2}":[740,741],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key":[742,771],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{1}":[744,753],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{2}":[754,754],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{3}":[755,756],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{4}":[757,760],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{5}":[761,761],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{6}":[762,763],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{7}":[764,769],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Configuring the Environment Variable for API Key#{8}":[770,771],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`)":[772,775],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`)#{1}":[774,775],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Instant Access from Anywhere":[776,788],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Instant Access from Anywhere#{1}":[778,788],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Open Source Alternatives":[789,792],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Open Source Alternatives#{1}":[791,792],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setup Guide for Ollama":[793,810],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#Setup Guide for Ollama#{1}":[795,810],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How would I fine-tune a model?":[811,821],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How would I fine-tune a model?#{1}":[813,814],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How would I fine-tune a model?#{2}":[815,816],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How would I fine-tune a model?#{3}":[817,819],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Frequently Asked Questions#How would I fine-tune a model?#{4}":[820,821],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap":[822,840],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{1}":[824,825],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{2}":[826,826],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{3}":[827,827],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{4}":[828,828],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{5}":[829,829],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{6}":[830,830],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{7}":[831,831],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{8}":[832,833],"#PromptingTools.jl: \"Your Daily Dose of AI Efficiency.\"#Roadmap#{9}":[834,840],"#---frontmatter---":[838,null]},"last_import":{"mtime":1719305337019,"size":50237,"at":1740449882655,"hash":"5d51044644a4c9b6374c480c84d7501c3f9d02becdfa1780a9d998467f982daa"},"key":"Prompts/PromptingTools.jl Your Daily Dose of AI Efficiency..md"},