/** GENERATED BY OBSIDIAN-PLUGIN-CLI, DO NOT EDIT */
var $=Object.create;var v=Object.defineProperty;var H=Object.getOwnPropertyDescriptor;var R=Object.getOwnPropertyNames,L=Object.getOwnPropertySymbols,W=Object.getPrototypeOf,j=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;var M=(i,e,t)=>e in i?v(i,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):i[e]=t,x=(i,e)=>{for(var t in e||(e={}))j.call(e,t)&&M(i,t,e[t]);if(L)for(var t of L(e))G.call(e,t)&&M(i,t,e[t]);return i};var I=i=>v(i,"__esModule",{value:!0});var U=(i,e)=>{I(i);for(var t in e)v(i,t,{get:e[t],enumerable:!0})},K=(i,e,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of R(e))!j.call(i,s)&&s!=="default"&&v(i,s,{get:()=>e[s],enumerable:!(t=H(e,s))||t.enumerable});return i},w=i=>K(I(v(i!=null?$(W(i)):{},"default",i&&i.__esModule&&"default"in i?{get:()=>i.default,enumerable:!0}:{value:i,enumerable:!0})),i);var o=(i,e,t)=>new Promise((s,a)=>{var n=r=>{try{m(t.next(r))}catch(l){a(l)}},g=r=>{try{m(t.throw(r))}catch(l){a(l)}},m=r=>r.done?s(r.value):Promise.resolve(r.value).then(n,g);m((t=t.apply(i,e)).next())});U(exports,{default:()=>Y});var A=w(require("obsidian"));var B={extensions:["ts","tsx","js","jsx","py"],folding:!0,lineNumbers:!0,minimap:!0,semanticValidation:!0,syntaxValidation:!0,theme:"default",overwriteBg:!0},y="code-editor";var O=w(require("obsidian"));var F=(i,e,t,s,a)=>{let n=t,g=document.body.classList.contains("theme-dark")?"vs-dark":"vs",m=i.settings.theme==="default"?g:i.settings.theme,r=new URLSearchParams;r.append("context",s),r.append("lang",e),r.append("theme",m),i.settings.overwriteBg&&r.append("background","transparent"),r.append("folding",i.settings.folding?"true":"false"),r.append("lineNumbers",i.settings.lineNumbers?"on":"off"),r.append("minimap",i.settings.minimap?"true":"false"),r.append("javascriptDefaults","true"),r.append("typescriptDefaults","true"),r.append("javascriptDefaultsNoSemanticValidation",i.settings.semanticValidation?"false":"true"),r.append("typescriptDefaultsNoSemanticValidation",i.settings.semanticValidation?"false":"true"),r.append("javascriptDefaultsNoSyntaxValidation",i.settings.syntaxValidation?"false":"true"),r.append("typescriptDefaultsNoSyntaxValidation",i.settings.syntaxValidation?"false":"true");let l=document.createElement("iframe");l.src=`https://embeddable-monaco.lukasbach.com?${r.toString()}`,l.style.width="100%",l.style.height="100%";let f=(p,z)=>{var N;(N=l==null?void 0:l.contentWindow)==null||N.postMessage(x({type:p},z),"*")};return window.addEventListener("message",({data:p})=>{switch(p.type){case"ready":{f("change-value",{value:n}),f("change-language",{language:e}),i.settings.overwriteBg&&f("change-background",{background:"transparent",theme:m});break}case"change":{p.context===s&&(n=p.value,a==null||a());break}default:break}}),{iframe:l,send:f,clear:()=>{f("change-value",{value:""}),n=""},getValue:()=>n,setValue:p=>{n=p,f("change-value",{value:p})},destroy:()=>{l.remove()}}};var k=i=>{switch(i){case"js":case"es6":case"jsx":case"cjs":case"mjs":case"javascript":return"javascript";case"ts":case"tsx":case"cts":case"mts":case"typescript":return"typescript";case"json":return"json";case"py":case"rpy":case"pyu":case"cpy":case"gyp":case"gypi":case"python":return"python";case"css":return"css";case"html":case"htm":case"shtml":case"xhtml":case"mdoc":case"jsp":case"asp":case"aspx":case"jshtm":return"html";case"cpp":case"cc":case"cxx":case"hpp":case"hh":case"hxx":return"cpp";case"graphql":case"gql":return"graphql";case"java":case"jav":return"java";case"php":case"php4":case"php5":case"phtml":case"ctp":return"php";case"sql":return"sql";case"yaml":case"yml":return"yaml";case"bat":case"batch":return"bat";case"lua":return"lua";case"rb":case"rbx":case"rjs":case"gemspec":case"ruby":return"ruby";case"markdown":case"mdown":case"mkdn":case"mkd":case"mdwn":case"mdtxt":case"mdtext":case"mdx":case"md":return"markdown";case"r":case"rhistory":case"rmd":case"rprofile":case"rt":return"r";case"ftl":case"ftlh":case"ftlx":return"freemarker2";case"rst":return"restructuredtext";case"hcl":case"tf":case"tfvars":return"hcl";case"ini":case"properties":case"gitconfig":return"ini";case"pug":case"jade":return"pug";case"dart":return"dart";case"rs":case"rlib":return"rust";case"less":return"less";case"cls":return"apex";case"tcl":return"tcl";case"abap":return"abap";case"ecl":return"ecl";case"pla":return"pla";case"cmd":return"bat";case"vb":return"vb";case"sb":return"sb";case"m3":case"i3":case"mg":case"ig":return"m3";case"go":return"go";case"s":return"mips";case"pl":case"pm":return"perl";case"wgsl":return"wgsl";case"twig":return"twig";case"scss":return"scss";case"redis":return"redis";case"sh":case"bash":return"shell";case"scala":case"sc":case"sbt":return"scala";case"jl":return"julia";case"dax":case"msdax":return"msdax";case"lex":return"lexon";case"cshtml":return"razor";case"bicep":return"bicep";case"azcli":return"azcli";case"swift":case"Swift":return"swift";case"flow":return"flow9";case"xml":case"xsd":case"dtd":case"ascx":case"csproj":case"config":case"props":case"targets":case"wxi":case"wxl":case"wxs":case"xaml":case"svgz":case"opf":case"xslt":case"xsl":return"xml";case"kt":case"kts":return"kotlin";case"cypher":case"cyp":return"cypher";case"coffee":return"coffeescript";case"fs":case"fsi":case"ml":case"mli":case"fsx":case"fsscript":return"fsharp";case"scm":case"ss":case"sch":case"rkt":return"scheme";case"rq":return"sparql";case"aes":return"aes";case"liquid":case"html.liquid":return"liquid";case"pas":case"p":case"pp":return"pascal";case"ex":case"exs":return"elixir";case"qs":return"qsharp";case"cs":case"csx":case"cake":return"csharp";case"clj":case"cljs":case"cljc":case"edn":return"clojure";case"mligo":return"cameligo";case"sol":return"sol";case"proto":return"proto";case"dats":case"sats":case"hats":return"postiats";case"ligo":return"pascaligo";case"dockerfile":return"dockerfile";case"handlebars":case"hbs":return"handlebars";case"pq":case"pqm":return"powerquery";case"m":return"objective-c";case"sv":case"svh":return"systemverilog";case"v":case"vh":return"verilog";case"st":case"iecst":case"iecplc":case"lc3lib":return"st";case"c":case"h":return"c";default:return"plaintext"}};var T=class extends O.TextFileView{constructor(e,t){super(e);this.plugin=t;this.id=T.i++}getDisplayText(){var e,t;return(t=(e=this.file)==null?void 0:e.name)!=null?t:"Code Editor"}getViewType(){return y}getContext(e){var t,s,a;return(a=(s=e==null?void 0:e.path)!=null?s:(t=this.file)==null?void 0:t.path)!=null?a:""}onClose(){var e=t=>super[t];return o(this,null,function*(){yield e("onClose").call(this),this.codeEditor.destroy()})}onLoadFile(e){var t=s=>super[s];return o(this,null,function*(){yield t("onLoadFile").call(this,e),this.codeEditor=F(this.plugin,k(e.extension),this.initialValue,this.getContext(e),()=>this.requestSave()),this.contentEl.style.overflow="hidden",this.contentEl.append(this.codeEditor.iframe)})}onUnloadFile(e){var t=s=>super[s];return o(this,null,function*(){yield t("onUnloadFile").call(this,e),this.codeEditor.destroy()})}onOpen(){var e=t=>super[t];return o(this,null,function*(){yield e("onOpen").call(this)})}clear(){this.codeEditor.clear()}getViewData(){return this.codeEditor.getValue()}setViewData(e){var t;this.initialValue=e,(t=this.codeEditor)==null||t.setValue(e)}static openFile(e,t){let s=t.app.workspace.getLeaf(!0),a=new T(s,t);a.file=e,a.onLoadFile(e),s.open(a),a.load(),t.app.workspace.revealLeaf(s)}},u=T;u.i=0;var c=w(require("obsidian")),C=class extends c.Modal{constructor(e,t){super(e.app);this.plugin=e;this.fileName="My Code File";this.fileExtension=this.plugin.settings.extensions[0];this.parent=t!=null?t:this.plugin.app.vault.getRoot()}onOpen(){let{contentEl:e}=this;e.style.display="flex",e.style.alignItems="center";let t=new c.TextComponent(e);t.inputEl.style.flexGrow="1",t.inputEl.style.marginRight="10px",t.setValue(this.fileName),t.inputEl.addEventListener("keypress",n=>{n.key==="Enter"&&this.complete()}),t.onChange(n=>{this.fileName=n});let s=new c.DropdownComponent(e);s.selectEl.style.marginRight="10px",s.addOptions(this.plugin.settings.extensions.reduce((n,g)=>(n[g]=g,n),{})),s.setValue(this.fileExtension),s.onChange(n=>{this.fileExtension=n}),s.selectEl.addEventListener("keypress",n=>{n.key==="Enter"&&this.complete()});let a=new c.ButtonComponent(e);a.setCta(),a.setButtonText("Create"),a.onClick(()=>this.complete()),t.inputEl.focus()}complete(){return o(this,null,function*(){this.close();let t=`${(this.parent instanceof c.TFile?this.parent.parent:this.parent).path}/${this.fileName}.${this.fileExtension}`,s=this.app.vault.getAbstractFileByPath((0,c.normalizePath)(t));if(s&&s instanceof c.TFile){new c.Notice("File already exists"),this.app.workspace.getLeaf(!0).openFile(s);return}let a=yield this.app.vault.create(t,"",{});this.app.workspace.getLeaf(!0).openFile(a)})}onClose(){let{contentEl:e}=this;e.empty()}};var d=w(require("obsidian"));var q=Object.values({active4d:"Active4D","all-hallows-eve":"All Hallows Eve",amy:"Amy","birds-of-paradise":"Birds of Paradise",blackboard:"Blackboard","brilliance-black":"Brilliance Black","brilliance-dull":"Brilliance Dull","chrome-devtools":"Chrome DevTools","clouds-midnight":"Clouds Midnight",clouds:"Clouds",cobalt:"Cobalt",cobalt2:"Cobalt2",dawn:"Dawn",dracula:"Dracula",dreamweaver:"Dreamweaver",eiffel:"Eiffel","espresso-libre":"Espresso Libre","github-dark":"GitHub Dark","github-light":"GitHub Light",github:"GitHub",idle:"IDLE",katzenmilch:"Katzenmilch","kuroir-theme":"Kuroir Theme",lazy:"LAZY","magicwb--amiga-":"MagicWB (Amiga)","merbivore-soft":"Merbivore Soft",merbivore:"Merbivore","monokai-bright":"Monokai Bright",monokai:"Monokai","night-owl":"Night Owl",nord:"Nord","oceanic-next":"Oceanic Next","pastels-on-dark":"Pastels on Dark","slush-and-poppies":"Slush and Poppies","solarized-dark":"Solarized-dark","solarized-light":"Solarized-light",spacecadet:"SpaceCadet",sunburst:"Sunburst","textmate--mac-classic-":"Textmate (Mac Classic)","tomorrow-night-blue":"Tomorrow-Night-Blue","tomorrow-night-bright":"Tomorrow-Night-Bright","tomorrow-night-eighties":"Tomorrow-Night-Eighties","tomorrow-night":"Tomorrow-Night",tomorrow:"Tomorrow",twilight:"Twilight","upstream-sunburst":"Upstream Sunburst","vibrant-ink":"Vibrant Ink","xcode-default":"Xcode_default",zenburnesque:"Zenburnesque",iplastic:"iPlastic",idlefingers:"idleFingers",krtheme:"krTheme",monoindustrial:"monoindustrial"});var P=class extends d.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e}=this;e.empty(),e.createEl("h2",{text:"Code Files Settings"}),e.createEl("p",{text:"If you change any settings, you need to reopen already opened files for the changes to take effect."}),new d.Setting(e).setName("Theme").setDesc("Theme of the editor, defaults to dark or light based on the current editor theme.").addDropdown(t=>{t.addOption("default","Default");for(let s of q)t.addOption(s,s);return t.setValue(this.plugin.settings.theme).onChange(s=>o(this,null,function*(){this.plugin.settings.theme=s,yield this.plugin.saveSettings()}))}),new d.Setting(e).setName("Overwrite background with Obsidian background").setDesc("Always use the background of Obsidian as background, instead of the theme default background. It's recommended to turn this off if you are using custom themes. Disable this if the text colors are illegible on Obsidians background.").addToggle(t=>t.setValue(this.plugin.settings.overwriteBg).onChange(s=>{this.plugin.settings.overwriteBg=s,this.plugin.saveSettings()})),new d.Setting(e).setName("File Extensions").setDesc("Files with these extensions will show up in the sidebar, and will be available to create new files from. Seperated by commas. Changes to the file extensions need a restart to take effect.").addText(t=>t.setPlaceholder("js,ts").setValue(this.plugin.settings.extensions.join(",")).onChange(s=>o(this,null,function*(){this.plugin.settings.extensions=s.split(","),yield this.plugin.saveSettings()}))),new d.Setting(e).setName("Folding").setDesc("Editor will support code block folding.").addToggle(t=>t.setValue(this.plugin.settings.folding).onChange(s=>o(this,null,function*(){this.plugin.settings.folding=s,yield this.plugin.saveSettings()}))),new d.Setting(e).setName("Line Numbers").setDesc("Editor will show line numbers.").addToggle(t=>t.setValue(this.plugin.settings.lineNumbers).onChange(s=>o(this,null,function*(){this.plugin.settings.lineNumbers=s,yield this.plugin.saveSettings()}))),new d.Setting(e).setName("Minimap").setDesc("Editor will show a minimap.").addToggle(t=>t.setValue(this.plugin.settings.minimap).onChange(s=>o(this,null,function*(){this.plugin.settings.minimap=s,yield this.plugin.saveSettings()}))),new d.Setting(e).setName("Semantic Validation").setDesc("Editor will show semantic validation errors.").addToggle(t=>t.setValue(this.plugin.settings.semanticValidation).onChange(s=>o(this,null,function*(){this.plugin.settings.semanticValidation=s,yield this.plugin.saveSettings()}))),new d.Setting(e).setName("Syntax Validation").setDesc("Editor will show syntax validation errors.").addToggle(t=>t.setValue(this.plugin.settings.syntaxValidation).onChange(s=>o(this,null,function*(){this.plugin.settings.syntaxValidation=s,yield this.plugin.saveSettings()})))}};var S=w(require("obsidian"));var h=class{constructor(e){this.plugin=e;this.start=0;this.end=0;this.isInValidFence=!1;this.initializeStartAndEnd(),this.validateFence()}static create(e){return new h(e)}initializeStartAndEnd(){var t,s;this.editor=(t=this.plugin.app.workspace.activeEditor)==null?void 0:t.editor;let e=(s=this.editor)==null?void 0:s.getCursor();if(!(!this.editor||!e)){this.start=e.line,this.end=e.line;do this.start--;while(this.start>=0&&!this.editor.getLine(this.start).startsWith("```"));do this.end++;while(this.end<this.editor.lineCount()&&!this.editor.getLine(this.end).startsWith("```"))}}validateFence(){if(!this.editor||this.start<0||this.end>=this.editor.lineCount())return;let e=0;for(let t=0;t<this.start;t++)this.editor.getLine(t).startsWith("```")&&e++;e%2!=1&&(this.isInValidFence=!0)}isInFence(){return this.isInValidFence}getFenceData(){if(!this.editor||!this.isInValidFence)return null;let e="";for(let n=this.start+1;n<this.end;n++)e+=`${this.editor.getLine(n)}
`;let t=e.slice(0,e.length-1),s=this.editor.getLine(this.start).slice(3).trim().split(" ")[0],a=k(s);return{content:t,language:a}}getEditor(){return this.editor}getBounds(){return[this.start,this.end]}replaceFenceContent(e){var t;(t=this.editor)==null||t.replaceRange(`${e}
`,{line:this.start+1,ch:0},{line:this.end,ch:0})}};var b=class extends S.Modal{constructor(e,t,s,a){super(e.app);this.plugin=e;this.code=t;this.language=s;this.onSave=a}onOpen(){super.onOpen(),this.codeEditor=F(this.plugin,this.language,this.code,"modal-editor"),this.contentEl.append(this.codeEditor.iframe),this.modalEl.setCssProps({"--dialog-width":"90vw","--dialog-height":"90vh"}),this.modalEl.style.height="var(--dialog-height)",this.modalEl.querySelector(".modal-close-button").style.background="var(--modal-background)"}onClose(){super.onClose(),this.onSave(this.codeEditor.getValue())}static openOnCurrentCode(e){let t=h.create(e);if(!t.isInFence()){new S.Notice("Your cursor is currently not in a valid code block.");return}let s=t.getFenceData();!s||new b(e,s.content,s.language,a=>t.replaceFenceContent(a)).open()}};var E=w(require("obsidian"));var D=class extends E.SuggestModal{constructor(e,t){super(e.app);this.plugin=e;this.cssFiles=t}getSuggestions(e){let t=this.cssFiles.filter(s=>s.toLowerCase().includes(e.toLowerCase()));return e?[...t,`Create new snippet "${e}.css"`]:t}onChooseSuggestion(e){return o(this,null,function*(){let t=e.match(/Create new snippet "(.*)\.css"/);if(t==null?void 0:t[1]){let s=`${this.app.vault.configDir}/snippets/${t[1]}.css`;yield this.plugin.app.vault.adapter.write(s,""),u.openFile(new E.TFile(this.app.vault,s),this.plugin),new Notification("Make sure to enable new snippet in options.");return}u.openFile(new E.TFile(this.app.vault,e),this.plugin)})}renderSuggestion(e,t){return t.innerHTML=e,t}};var V=class extends A.Plugin{onload(){return o(this,null,function*(){yield this.loadSettings(),this.registerView(y,e=>new u(e,this));try{this.registerExtensions(this.settings.extensions,y)}catch(e){console.log("code-files plugin error:",e),new Notification("Code Files Plugin Error",{body:`Could not register extensions ${this.settings.extensions.join(", ")}; there are probably some other extensions that already registered them. Please change code-files's extensions in the plugin settings or remove conflicting plugins.`})}this.addCommand({id:"open-codeblock-in-monaco",name:"Open current code block in Monaco Editor",callback:()=>{b.openOnCurrentCode(this)}}),this.addCommand({id:"open-current-file-in-monaco",name:"Open current file in Monaco Editor",callback:()=>{var t;let e=(t=this.app.workspace.activeEditor)==null?void 0:t.file;if(!e){new Notification("No viable file open");return}u.openFile(e,this)}}),this.addCommand({id:"open-css-snippet",name:"Edit CSS Snippet",callback:()=>o(this,null,function*(){let e=(yield this.app.vault.adapter.list(`${this.app.vault.configDir}/snippets`)).files;new D(this,e).open()})}),this.registerEvent(this.app.workspace.on("file-menu",(e,t)=>{e.addItem(s=>{s.setTitle("Create Code File").setIcon("file-json").onClick(()=>{new C(this,t).open()})})})),this.registerEvent(this.app.workspace.on("editor-menu",e=>{!h.create(this).isInFence()||e.addItem(t=>{t.setTitle("Edit Code Block in Monaco Editor").setIcon("code").onClick(()=>{b.openOnCurrentCode(this)})})})),this.addRibbonIcon("file-json","Create Code File",()=>{new C(this).open()}),this.addCommand({id:"create",name:"Create new Code File",callback:()=>{new C(this).open()}}),this.addSettingTab(new P(this.app,this))})}onunload(){}loadSettings(){return o(this,null,function*(){this.settings=x(x({},B),yield this.loadData())})}saveSettings(){return o(this,null,function*(){yield this.saveData(this.settings)})}};var Y=V;0&&(module.exports={});

/* nosourcemap */