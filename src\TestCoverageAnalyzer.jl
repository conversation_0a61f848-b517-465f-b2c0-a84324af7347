# src/TestCoverageAnalyzer.jl

module TestCoverageAnalyzer

using Dates
using Statistics
using Printf

export CoverageConfig, CoverageResult, CoverageReport,
       analyze_test_coverage, generate_coverage_report,
       track_function_coverage, identify_untested_code,
       calculate_coverage_metrics, export_coverage_data

# 覆蓋率配置
struct CoverageConfig
    source_directories::Vector{String}
    test_directories::Vector{String}
    exclude_patterns::Vector{String}
    minimum_coverage_threshold::Float64
    report_format::Symbol  # :html, :text, :json
    
    function CoverageConfig(;
        source_directories::Vector{String} = ["src"],
        test_directories::Vector{String} = ["test"],
        exclude_patterns::Vector{String} = ["test_", "benchmark_"],
        minimum_coverage_threshold::Float64 = 80.0,
        report_format::Symbol = :html
    )
        new(source_directories, test_directories, exclude_patterns,
            minimum_coverage_threshold, report_format)
    end
end

# 函數覆蓋率信息
struct FunctionCoverage
    name::String
    file::String
    line_start::Int
    line_end::Int
    lines_covered::Int
    lines_total::Int
    coverage_percentage::Float64
    times_called::Int
    
    function FunctionCoverage(name, file, line_start, line_end, 
                            lines_covered, lines_total, times_called)
        coverage_percentage = lines_total > 0 ? (lines_covered / lines_total) * 100 : 0.0
        new(name, file, line_start, line_end, lines_covered, lines_total,
            coverage_percentage, times_called)
    end
end

# 文件覆蓋率信息
struct FileCoverage
    filename::String
    lines_covered::Int
    lines_total::Int
    coverage_percentage::Float64
    functions::Vector{FunctionCoverage}
    
    function FileCoverage(filename, lines_covered, lines_total, functions)
        coverage_percentage = lines_total > 0 ? (lines_covered / lines_total) * 100 : 0.0
        new(filename, lines_covered, lines_total, coverage_percentage, functions)
    end
end

# 覆蓋率結果
struct CoverageResult
    total_lines_covered::Int
    total_lines::Int
    overall_coverage::Float64
    file_coverages::Vector{FileCoverage}
    untested_functions::Vector{String}
    timestamp::DateTime
    
    function CoverageResult(total_lines_covered, total_lines, file_coverages, untested_functions)
        overall_coverage = total_lines > 0 ? (total_lines_covered / total_lines) * 100 : 0.0
        new(total_lines_covered, total_lines, overall_coverage, file_coverages,
            untested_functions, now())
    end
end

# 覆蓋率報告
struct CoverageReport
    config::CoverageConfig
    result::CoverageResult
    metrics::Dict{String, Float64}
    recommendations::Vector{String}
    
    function CoverageReport(config, result, metrics, recommendations)
        new(config, result, metrics, recommendations)
    end
end

"""
分析測試覆蓋率
"""
function analyze_test_coverage(config::CoverageConfig)::CoverageResult
    println("🔍 開始分析測試覆蓋率...")
    println("  源代碼目錄: $(config.source_directories)")
    println("  測試目錄: $(config.test_directories)")
    
    file_coverages = Vector{FileCoverage}()
    total_lines_covered = 0
    total_lines = 0
    untested_functions = Vector{String}()
    
    # 分析每個源代碼目錄
    for src_dir in config.source_directories
        if isdir(src_dir)
            analyze_directory!(src_dir, file_coverages, config)
        else
            println("⚠️  目錄不存在: $src_dir")
        end
    end
    
    # 計算總覆蓋率
    for file_coverage in file_coverages
        total_lines_covered += file_coverage.lines_covered
        total_lines += file_coverage.lines_total
        
        # 收集未測試的函數
        for func in file_coverage.functions
            if func.coverage_percentage == 0.0
                push!(untested_functions, "$(file_coverage.filename):$(func.name)")
            end
        end
    end
    
    result = CoverageResult(total_lines_covered, total_lines, file_coverages, untested_functions)
    
    println("✓ 覆蓋率分析完成")
    println("  總覆蓋率: $(round(result.overall_coverage, digits=1))%")
    println("  未測試函數: $(length(untested_functions)) 個")
    
    return result
end

"""
分析目錄中的文件
"""
function analyze_directory!(directory::String, file_coverages::Vector{FileCoverage}, config::CoverageConfig)
    for (root, dirs, files) in walkdir(directory)
        for file in files
            if endswith(file, ".jl") && !should_exclude_file(file, config.exclude_patterns)
                filepath = joinpath(root, file)
                file_coverage = analyze_file_coverage(filepath)
                if file_coverage !== nothing
                    push!(file_coverages, file_coverage)
                end
            end
        end
    end
end

"""
檢查文件是否應該被排除
"""
function should_exclude_file(filename::String, exclude_patterns::Vector{String})::Bool
    for pattern in exclude_patterns
        if occursin(pattern, filename)
            return true
        end
    end
    return false
end

"""
分析單個文件的覆蓋率
"""
function analyze_file_coverage(filepath::String)::Union{FileCoverage, Nothing}
    try
        content = read(filepath, String)
        lines = split(content, '\n')
        
        # 簡化的覆蓋率分析（實際實現需要更複雜的解析）
        functions = extract_functions(lines, filepath)
        
        # 模擬覆蓋率數據（實際應該從覆蓋率工具獲取）
        lines_covered = simulate_coverage(length(lines))
        lines_total = count_executable_lines(lines)
        
        return FileCoverage(basename(filepath), lines_covered, lines_total, functions)
        
    catch e
        println("❌ 分析文件失敗: $filepath - $e")
        return nothing
    end
end

"""
提取函數信息
"""
function extract_functions(lines::Vector{SubString{String}}, filepath::String)::Vector{FunctionCoverage}
    functions = Vector{FunctionCoverage}()
    
    for (i, line) in enumerate(lines)
        # 簡化的函數檢測（實際需要更複雜的解析）
        if occursin(r"^function\s+(\w+)", line)
            match_result = match(r"^function\s+(\w+)", line)
            if match_result !== nothing
                func_name = match_result.captures[1]
                
                # 找到函數結束位置（簡化實現）
                end_line = find_function_end(lines, i)
                
                # 模擬覆蓋率數據
                lines_total = end_line - i + 1
                lines_covered = simulate_function_coverage(lines_total)
                times_called = rand(0:10)
                
                func_coverage = FunctionCoverage(
                    func_name, basename(filepath), i, end_line,
                    lines_covered, lines_total, times_called
                )
                
                push!(functions, func_coverage)
            end
        end
    end
    
    return functions
end

"""
找到函數結束位置
"""
function find_function_end(lines::Vector{SubString{String}}, start_line::Int)::Int
    depth = 0
    for i in start_line:length(lines)
        line = strip(lines[i])
        if occursin("function", line) || occursin("if", line) || occursin("for", line) || occursin("while", line)
            depth += 1
        elseif occursin("end", line)
            depth -= 1
            if depth == 0
                return i
            end
        end
    end
    return length(lines)
end

"""
計算可執行行數
"""
function count_executable_lines(lines::Vector{SubString{String}})::Int
    count = 0
    for line in lines
        stripped = strip(line)
        if !isempty(stripped) && !startswith(stripped, "#") && !startswith(stripped, "using") && !startswith(stripped, "import")
            count += 1
        end
    end
    return count
end

"""
模擬覆蓋率數據
"""
function simulate_coverage(total_lines::Int)::Int
    # 模擬 60-90% 的覆蓋率
    coverage_rate = 0.6 + rand() * 0.3
    return Int(round(total_lines * coverage_rate))
end

"""
模擬函數覆蓋率數據
"""
function simulate_function_coverage(total_lines::Int)::Int
    # 模擬 50-100% 的函數覆蓋率
    coverage_rate = 0.5 + rand() * 0.5
    return Int(round(total_lines * coverage_rate))
end

"""
計算覆蓋率指標
"""
function calculate_coverage_metrics(result::CoverageResult)::Dict{String, Float64}
    metrics = Dict{String, Float64}()
    
    # 基本指標
    metrics["overall_coverage"] = result.overall_coverage
    metrics["total_lines"] = Float64(result.total_lines)
    metrics["covered_lines"] = Float64(result.total_lines_covered)
    
    # 文件級指標
    if !isempty(result.file_coverages)
        file_coverages = [fc.coverage_percentage for fc in result.file_coverages]
        metrics["avg_file_coverage"] = mean(file_coverages)
        metrics["min_file_coverage"] = minimum(file_coverages)
        metrics["max_file_coverage"] = maximum(file_coverages)
        metrics["std_file_coverage"] = std(file_coverages)
    end
    
    # 函數級指標
    all_functions = vcat([fc.functions for fc in result.file_coverages]...)
    if !isempty(all_functions)
        func_coverages = [fc.coverage_percentage for fc in all_functions]
        metrics["avg_function_coverage"] = mean(func_coverages)
        metrics["untested_functions_count"] = Float64(count(fc -> fc.coverage_percentage == 0.0, all_functions))
        metrics["fully_tested_functions_count"] = Float64(count(fc -> fc.coverage_percentage == 100.0, all_functions))
    end
    
    return metrics
end

"""
生成覆蓋率報告
"""
function generate_coverage_report(config::CoverageConfig, result::CoverageResult)::CoverageReport
    println("📋 生成覆蓋率報告...")

    metrics = calculate_coverage_metrics(result)
    recommendations = generate_recommendations(config, result, metrics)

    report = CoverageReport(config, result, metrics, recommendations)

    # 根據配置格式生成報告
    if config.report_format == :html
        export_html_report(report, "coverage_report.html")
    elseif config.report_format == :text
        export_text_report(report, "coverage_report.txt")
    elseif config.report_format == :json
        export_json_report(report, "coverage_report.json")
    end

    return report
end

"""
生成優化建議
"""
function generate_recommendations(
    config::CoverageConfig,
    result::CoverageResult,
    metrics::Dict{String, Float64}
)::Vector{String}

    recommendations = Vector{String}()

    # 總覆蓋率建議
    if result.overall_coverage < config.minimum_coverage_threshold
        push!(recommendations, "總覆蓋率 $(round(result.overall_coverage, digits=1))% 低於目標 $(config.minimum_coverage_threshold)%，需要增加測試")
    end

    # 未測試函數建議
    if !isempty(result.untested_functions)
        push!(recommendations, "發現 $(length(result.untested_functions)) 個未測試函數，建議添加單元測試")

        # 列出前5個未測試函數
        for (i, func) in enumerate(result.untested_functions[1:min(5, end)])
            push!(recommendations, "  - 未測試函數: $func")
        end
    end

    # 文件覆蓋率建議
    low_coverage_files = filter(fc -> fc.coverage_percentage < 50.0, result.file_coverages)
    if !isempty(low_coverage_files)
        push!(recommendations, "以下文件覆蓋率較低，建議優先添加測試:")
        for file in low_coverage_files[1:min(3, end)]
            push!(recommendations, "  - $(file.filename): $(round(file.coverage_percentage, digits=1))%")
        end
    end

    # 一般建議
    push!(recommendations, "建議使用測試驅動開發(TDD)提升代碼質量")
    push!(recommendations, "考慮添加集成測試和端到端測試")
    push!(recommendations, "定期運行覆蓋率分析並設置CI/CD檢查")

    return recommendations
end

"""
導出HTML報告
"""
function export_html_report(report::CoverageReport, filename::String)
    open(filename, "w") do io
        println(io, "<!DOCTYPE html>")
        println(io, "<html><head><title>測試覆蓋率報告</title>")
        println(io, "<style>")
        println(io, "body { font-family: Arial, sans-serif; margin: 20px; }")
        println(io, "table { border-collapse: collapse; width: 100%; }")
        println(io, "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }")
        println(io, "th { background-color: #f2f2f2; }")
        println(io, ".high { background-color: #d4edda; }")
        println(io, ".medium { background-color: #fff3cd; }")
        println(io, ".low { background-color: #f8d7da; }")
        println(io, "</style></head><body>")

        println(io, "<h1>測試覆蓋率報告</h1>")
        println(io, "<p><strong>生成時間:</strong> $(report.result.timestamp)</p>")
        println(io, "<p><strong>總覆蓋率:</strong> $(round(report.result.overall_coverage, digits=1))%</p>")

        # 摘要表格
        println(io, "<h2>覆蓋率摘要</h2>")
        println(io, "<table>")
        println(io, "<tr><th>指標</th><th>值</th></tr>")
        for (key, value) in report.metrics
            println(io, "<tr><td>$(key)</td><td>$(round(value, digits=2))</td></tr>")
        end
        println(io, "</table>")

        # 文件覆蓋率表格
        println(io, "<h2>文件覆蓋率</h2>")
        println(io, "<table>")
        println(io, "<tr><th>文件</th><th>覆蓋率</th><th>已覆蓋行數</th><th>總行數</th></tr>")
        for file in report.result.file_coverages
            css_class = get_coverage_css_class(file.coverage_percentage)
            println(io, "<tr class=\"$css_class\">")
            println(io, "<td>$(file.filename)</td>")
            println(io, "<td>$(round(file.coverage_percentage, digits=1))%</td>")
            println(io, "<td>$(file.lines_covered)</td>")
            println(io, "<td>$(file.lines_total)</td>")
            println(io, "</tr>")
        end
        println(io, "</table>")

        # 建議
        println(io, "<h2>優化建議</h2>")
        println(io, "<ul>")
        for rec in report.recommendations
            println(io, "<li>$rec</li>")
        end
        println(io, "</ul>")

        println(io, "</body></html>")
    end

    println("✓ HTML報告已保存到: $filename")
end

"""
導出文本報告
"""
function export_text_report(report::CoverageReport, filename::String)
    open(filename, "w") do io
        println(io, "測試覆蓋率報告")
        println(io, "=" ^ 50)
        println(io, "生成時間: $(report.result.timestamp)")
        println(io, "總覆蓋率: $(round(report.result.overall_coverage, digits=1))%")
        println(io, "")

        println(io, "覆蓋率指標:")
        println(io, "-" ^ 30)
        for (key, value) in report.metrics
            println(io, "$key: $(round(value, digits=2))")
        end
        println(io, "")

        println(io, "文件覆蓋率:")
        println(io, "-" ^ 30)
        for file in report.result.file_coverages
            println(io, "$(file.filename): $(round(file.coverage_percentage, digits=1))% ($(file.lines_covered)/$(file.lines_total))")
        end
        println(io, "")

        println(io, "優化建議:")
        println(io, "-" ^ 30)
        for (i, rec) in enumerate(report.recommendations)
            println(io, "$i. $rec")
        end
    end

    println("✓ 文本報告已保存到: $filename")
end

"""
獲取覆蓋率CSS類別
"""
function get_coverage_css_class(coverage::Float64)::String
    if coverage >= 80.0
        return "high"
    elseif coverage >= 50.0
        return "medium"
    else
        return "low"
    end
end

"""
識別未測試代碼
"""
function identify_untested_code(result::CoverageResult)::Dict{String, Vector{String}}
    untested_code = Dict{String, Vector{String}}()

    for file_coverage in result.file_coverages
        untested_functions = Vector{String}()

        for func in file_coverage.functions
            if func.coverage_percentage == 0.0
                push!(untested_functions, func.name)
            end
        end

        if !isempty(untested_functions)
            untested_code[file_coverage.filename] = untested_functions
        end
    end

    return untested_code
end

"""
追蹤函數覆蓋率
"""
function track_function_coverage(result::CoverageResult, function_name::String)::Vector{FunctionCoverage}
    matching_functions = Vector{FunctionCoverage}()

    for file_coverage in result.file_coverages
        for func in file_coverage.functions
            if func.name == function_name
                push!(matching_functions, func)
            end
        end
    end

    return matching_functions
end

"""
導出覆蓋率數據
"""
function export_coverage_data(result::CoverageResult, format::Symbol, filename::String)
    if format == :csv
        export_csv_data(result, filename)
    elseif format == :json
        export_json_data(result, filename)
    else
        throw(ArgumentError("不支援的格式: $format"))
    end
end

"""
導出CSV數據
"""
function export_csv_data(result::CoverageResult, filename::String)
    open(filename, "w") do io
        println(io, "文件,函數,覆蓋率,已覆蓋行數,總行數,調用次數")

        for file_coverage in result.file_coverages
            for func in file_coverage.functions
                println(io, "$(file_coverage.filename),$(func.name),$(func.coverage_percentage),$(func.lines_covered),$(func.lines_total),$(func.times_called)")
            end
        end
    end

    println("✓ CSV數據已保存到: $filename")
end

end # module TestCoverageAnalyzer
