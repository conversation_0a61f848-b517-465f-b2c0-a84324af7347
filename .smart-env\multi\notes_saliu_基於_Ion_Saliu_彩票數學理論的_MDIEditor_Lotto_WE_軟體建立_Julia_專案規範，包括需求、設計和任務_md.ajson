
"smart_sources:notes/saliu/基於 <PERSON> 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md": {"path":"notes/saliu/基於 Ion <PERSON> 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10040358,0.02220548,-0.01792104,-0.0192532,-0.03257492,0.04670025,0.02390233,0.01289111,0.04524162,-0.03280229,0.01687397,-0.07874462,0.01405209,-0.00357497,0.05259227,-0.054954,0.02765559,-0.05069913,-0.09508581,-0.00944731,0.07961645,-0.069941,0.00007944,-0.0724742,0.06754602,-0.01141312,0.01998207,-0.00702255,0.00286973,-0.1968099,-0.00433553,0.02930705,0.03118668,-0.04588081,-0.02474234,0.00607909,-0.01357992,0.03014996,-0.04646288,0.03479709,0.01871771,-0.02276553,0.03019681,-0.03806671,0.01790314,-0.03687915,-0.02558385,-0.04071654,0.07236984,0.02421087,-0.05733659,-0.0226168,0.02762899,0.05052414,0.03675609,0.03866935,0.07885805,0.05608045,0.04438133,-0.00682239,0.0244104,0.03391621,-0.20614974,0.05085469,-0.03697681,-0.05492507,-0.00972888,-0.03120522,-0.00307035,0.02900765,-0.03493284,0.00804265,-0.04606952,0.04420299,0.04491917,-0.0632368,0.00557521,-0.04099201,-0.04518551,-0.01251503,-0.01162468,-0.01159467,-0.01393809,-0.01132122,0.01484525,0.06706739,0.02143773,0.01584759,0.01353755,-0.0839255,-0.0080593,-0.03169357,0.00170424,0.10863849,-0.0465089,0.01861217,0.03492465,0.04250938,-0.08997823,0.12264139,0.04422766,0.02790345,0.00617252,-0.02452164,0.03696235,0.02124422,0.0344419,-0.05305813,-0.02431045,-0.0090658,0.01905163,-0.0020097,0.02285275,-0.03795501,-0.02431588,0.01974515,0.05626556,-0.02052683,-0.02096165,0.01842323,0.01298361,0.01564813,0.02954723,-0.00287816,0.03955365,-0.02865455,0.04146526,0.04113061,0.03884577,0.02238745,0.03025702,0.01547766,-0.11100794,-0.03905412,-0.02265505,-0.0264059,-0.02019561,-0.07328563,0.01975616,-0.0351991,-0.03270929,-0.05126236,0.02823482,-0.11582039,-0.04724901,0.07401538,0.00447193,-0.00412331,0.0189628,-0.04467634,-0.00631589,0.04031785,0.00239473,-0.04488902,0.05215802,0.01611615,0.06494512,0.10327346,-0.02547489,-0.01001587,-0.00934832,-0.05924115,-0.04219795,0.17037256,0.01649027,-0.05741083,-0.03353943,0.0474775,0.03434412,-0.04471318,0.03728464,-0.00079312,-0.04638086,0.04966454,0.10419036,-0.02765019,0.01195278,-0.04929426,-0.10022221,-0.00195164,-0.0301185,0.03231043,-0.08738258,0.03935704,-0.00940962,-0.11999341,0.04116172,-0.0028178,0.03508423,0.01176288,-0.00313109,0.06191893,0.04075548,0.07108562,-0.04472509,-0.03303199,-0.01716935,-0.04418075,0.04204627,-0.01172314,0.05594015,0.03307867,-0.0358765,-0.0155581,-0.01404317,-0.01107135,-0.00181301,-0.00993572,0.00490674,0.04011299,-0.02541217,0.06012769,0.01164568,-0.00547295,-0.02070394,0.03990155,0.00141791,0.00129532,0.03468479,0.0405204,0.06657796,0.0501901,-0.10576924,-0.2506969,-0.00853652,-0.02983958,-0.06385689,0.02138974,-0.0448506,0.04564642,-0.0344177,0.08639735,0.04350876,0.08123092,0.02246153,-0.0224331,0.03760495,0.00675724,0.00567727,0.02276014,-0.00703368,-0.03813776,0.01548197,0.01420664,0.05814027,0.00450128,0.0304223,0.07022179,-0.04824294,0.13382716,-0.00399024,0.01653593,0.0293359,0.09635757,0.04618647,-0.01126948,-0.1025,-0.01128278,0.00798675,-0.0952436,0.02263904,-0.07601609,-0.02283286,-0.0445003,0.02903075,-0.03967317,-0.07622483,-0.00473859,-0.01652052,-0.02412009,0.0002626,-0.06116925,0.00014539,0.02990847,0.01052915,0.01168113,0.03122662,0.02780843,-0.04640717,-0.06656918,-0.02295196,-0.07059193,0.00852901,-0.0212651,0.00223728,0.04068737,-0.03048771,-0.02378611,-0.0185704,-0.00529813,0.01679984,-0.01826418,0.01612452,-0.02694834,0.11949516,0.00808209,0.03746596,0.00894604,-0.03571418,-0.04067176,-0.06148177,0.01439728,-0.01939102,0.01500293,0.00291053,-0.01274442,0.05146558,0.05132276,0.07313152,0.03957304,0.02891133,-0.00832494,-0.02464378,-0.02062889,-0.0192853,-0.03523884,0.00646627,0.06115913,-0.01895578,-0.27464515,0.03625944,-0.03754979,0.01445666,-0.02709037,-0.00625991,-0.00910627,-0.02114338,0.0225736,0.05479368,0.02490147,0.05080523,0.01880978,-0.04256644,-0.02246333,0.00176114,-0.03806879,0.01524359,0.06208268,-0.03119549,-0.02169174,-0.01875723,0.23815843,0.02432748,-0.01040479,-0.01038981,0.01357942,-0.00673296,0.01119625,-0.01141202,-0.00657453,0.01794803,0.06516761,0.03208694,0.02323059,0.07259447,-0.03651663,0.03289568,-0.02810631,0.03292835,-0.04122249,0.04625411,-0.08470349,0.01409989,0.11404692,0.03770033,-0.00217408,-0.07863916,0.00455099,0.09574343,-0.03593913,-0.0217177,-0.00602804,0.01884874,-0.01378438,0.01883162,0.02277177,-0.0426076,0.041466,0.05294606,0.04181389,0.06940727,0.02109897,0.04016576,0.03198682],"last_embed":{"hash":"18zcgzk","tokens":430}}},"last_read":{"hash":"18zcgzk","at":1753495701408},"class_name":"SmartSource","last_import":{"mtime":1753432785395,"size":14238,"at":1753495676981,"hash":"18zcgzk"},"blocks":{"#":[1,2],"###MDIEditor Lotto WE Julia 專案規範":[3,83],"###MDIEditor Lotto WE Julia 專案規範#{1}":[5,83],"#---frontmatter---":[7,82],"###二、設計 (Design)":[84,121],"###二、設計 (Design)#{1}":[86,87],"###二、設計 (Design)#{2}":[88,93],"###二、設計 (Design)#{3}":[94,102],"###二、設計 (Design)#{4}":[103,106],"###二、設計 (Design)#{5}":[107,110],"###二、設計 (Design)#{6}":[111,114],"###二、設計 (Design)#{7}":[115,119],"###二、設計 (Design)#{8}":[120,121],"###三、任務 (Tasks)":[122,172],"###三、任務 (Tasks)#{1}":[124,125],"###三、任務 (Tasks)#{2}":[126,130],"###三、任務 (Tasks)#{3}":[131,136],"###三、任務 (Tasks)#{4}":[137,143],"###三、任務 (Tasks)#{5}":[144,150],"###三、任務 (Tasks)#{6}":[151,156],"###三、任務 (Tasks)#{7}":[157,161],"###三、任務 (Tasks)#{8}":[162,166],"###三、任務 (Tasks)#{9}":[167,171],"###三、任務 (Tasks)#{10}":[172,172]},"outlinks":[]},"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###MDIEditor Lotto WE Julia 專案規範": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0970287,0.01380309,-0.02534536,-0.03331282,-0.0286902,0.05348767,0.01891869,0.00562135,0.04525151,-0.02572128,0.02167679,-0.08357797,0.02197084,-0.00133052,0.05049436,-0.05485065,0.02607252,-0.05833529,-0.10142066,-0.01862863,0.085876,-0.07365707,-0.00067204,-0.07741123,0.0636384,-0.02165138,0.02697991,-0.00390031,0.0069642,-0.19956112,0.00354277,0.01891089,0.03073807,-0.05434535,-0.02174741,0.00218621,-0.01478716,0.02653111,-0.05797212,0.02766988,0.00855936,-0.02130863,0.03883162,-0.03842759,0.03261784,-0.03006483,-0.02850488,-0.028798,0.08018158,0.01906773,-0.04832743,-0.0212785,0.02440083,0.04675904,0.05013341,0.03935737,0.07858706,0.05589736,0.05237563,-0.00061178,0.02606311,0.04385359,-0.20025761,0.04515798,-0.03298111,-0.04872135,-0.00467097,-0.0237874,-0.00182099,0.03213144,-0.03215763,0.01373581,-0.03593828,0.04635477,0.03459328,-0.05017575,-0.01465145,-0.04190115,-0.05216904,0.0000342,-0.01256862,0.00214991,-0.01014809,-0.01571956,0.00970778,0.06926087,0.01791704,0.01630898,0.00660995,-0.06908232,-0.01006893,-0.02649111,-0.00618942,0.10354221,-0.05053207,0.00900926,0.0354044,0.04076619,-0.0978045,0.11752141,0.0509448,0.02538862,-0.00464678,-0.03182667,0.03005476,0.02056226,0.02850001,-0.04768023,-0.02193808,-0.01589443,0.00908098,0.00126227,0.01480648,-0.03717878,-0.03052121,0.01586804,0.04546924,-0.03339352,-0.0161398,0.02591459,0.00256828,0.02431379,0.02470823,-0.0010042,0.0294673,-0.02801498,0.04661648,0.04139634,0.05246001,0.02539327,0.02871436,0.00920315,-0.12230008,-0.05431056,-0.01780705,-0.02671467,-0.0120704,-0.06873159,0.02586686,-0.03495056,-0.03477398,-0.05226902,0.03450172,-0.12298291,-0.05072667,0.07499778,0.00612144,-0.01300711,0.00787365,-0.04413739,-0.00309613,0.03021342,0.00191557,-0.03839757,0.03811168,0.02263822,0.06578302,0.11150531,-0.03388041,-0.01396095,-0.01087073,-0.05156018,-0.048019,0.1671192,0.02302547,-0.06568003,-0.03297285,0.04137059,0.02889846,-0.04663182,0.01697005,0.00914122,-0.06026101,0.04484751,0.09851264,-0.03194087,0.00789156,-0.0476555,-0.10080417,0.00043827,-0.01490915,0.0272417,-0.07796051,0.02914634,-0.0083357,-0.12352167,0.03507985,-0.00635704,0.04412416,0.01251249,-0.01658927,0.07428816,0.04583708,0.06918114,-0.0495408,-0.03415825,-0.02552427,-0.04268413,0.05486843,-0.0193383,0.06363799,0.0378841,-0.03068631,-0.01128978,-0.00703213,0.00238604,-0.0066547,-0.0131942,0.01063519,0.03965807,-0.00881432,0.05613578,0.01835503,0.01165905,-0.029025,0.03436763,0.0064494,0.00794441,0.02635674,0.03258339,0.07030705,0.05210198,-0.10294586,-0.24890888,-0.00987627,-0.03479059,-0.06663675,0.02248596,-0.0285274,0.04437191,-0.0467692,0.08323005,0.04792107,0.07844366,0.02074062,-0.02601253,0.03880085,0.00063795,0.00302208,-0.00336627,-0.00734376,-0.03630204,0.0250037,0.03142915,0.06598616,0.00328596,0.03592626,0.07376277,-0.0536715,0.13308835,0.00104748,0.01095172,0.02389808,0.10214131,0.04639621,-0.00876638,-0.09531368,-0.00473415,0.00288641,-0.0912064,0.01115318,-0.0789752,-0.02186241,-0.04492199,0.02344049,-0.04295599,-0.08221141,-0.00694253,-0.01318505,-0.02157046,-0.00123173,-0.04584449,0.00617965,0.03336618,0.00875282,0.01687959,0.0278884,0.03824433,-0.0461746,-0.06501953,-0.01531168,-0.05921807,0.01603888,-0.01928292,0.00117365,0.03719455,-0.02412443,-0.02945889,-0.01280959,-0.00872033,0.01741102,-0.01556214,0.00967728,-0.02962264,0.11458573,0.01347383,0.03315703,0.02077057,-0.03363359,-0.03773611,-0.0499827,0.0134042,-0.01808153,0.01397148,0.01189531,-0.00928209,0.04693705,0.04974791,0.07186743,0.03605347,0.02617342,-0.01100921,-0.02369508,-0.02517976,-0.02188945,-0.03606796,0.00925053,0.0552511,-0.01559268,-0.28311017,0.0366042,-0.03019347,0.0140216,-0.02667728,-0.01063861,-0.01729997,-0.01332975,0.02031527,0.04766593,0.02698339,0.05018578,0.02014131,-0.03995917,-0.01823442,-0.00199522,-0.04246334,0.01117323,0.06722753,-0.0257527,-0.00699338,-0.01752581,0.23938508,0.0247395,-0.00564615,0.00347091,0.01561558,-0.00904663,0.01680984,-0.01084163,-0.00418354,0.02210824,0.06896449,0.02824498,0.02020097,0.07290062,-0.03751429,0.03746045,-0.02759329,0.02890045,-0.04057416,0.04123547,-0.07297104,0.00955049,0.1094188,0.0411711,-0.00155047,-0.08299394,0.01256585,0.09865322,-0.04044876,-0.01737121,-0.01010771,0.01966758,-0.01177587,0.03015473,0.021367,-0.04125242,0.04249427,0.04748879,0.04017935,0.06882051,0.01315126,0.03791275,0.02813901],"last_embed":{"hash":"2c2i93","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"2c2i93","at":1753495700152},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###MDIEditor Lotto WE Julia 專案規範","lines":[3,83],"size":3238,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###MDIEditor Lotto WE Julia 專案規範#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09836847,0.01814112,-0.02617477,-0.03763054,-0.03010458,0.05387921,0.0164787,0.00520439,0.04552929,-0.02550476,0.02207919,-0.08126511,0.02157769,-0.00291353,0.05115062,-0.05353079,0.02772465,-0.05731755,-0.09958042,-0.01915065,0.08386868,-0.07215183,-0.0017772,-0.07867563,0.06511526,-0.02041903,0.02718663,-0.00367617,0.00528441,-0.19795424,0.00145214,0.01782908,0.0309867,-0.05772703,-0.02264358,0.00617642,-0.01208103,0.02770813,-0.05699425,0.02689173,0.0070765,-0.02307024,0.04136344,-0.04012458,0.02977614,-0.03339633,-0.02978432,-0.03155448,0.07820465,0.01862865,-0.04802863,-0.02354483,0.02509525,0.04628862,0.04887996,0.03882777,0.07413093,0.05609312,0.05175692,0.00018142,0.02307599,0.04153031,-0.20031047,0.04494645,-0.03264967,-0.04903267,-0.00456426,-0.02613978,-0.00082758,0.0320719,-0.03615459,0.01162463,-0.03916924,0.04519305,0.03699943,-0.04997353,-0.0125064,-0.04086264,-0.05115119,-0.00043626,-0.01238351,-0.00432838,-0.01034324,-0.01409774,0.00939407,0.06693114,0.02012122,0.01613328,0.00698773,-0.06751593,-0.00996705,-0.02784825,-0.00412945,0.10461702,-0.05328923,0.01108208,0.03698055,0.04279271,-0.09409593,0.11809001,0.05522804,0.02582714,-0.00203951,-0.0333221,0.03460775,0.0186825,0.03047207,-0.04998025,-0.02332776,-0.0203864,0.01054852,0.0013525,0.01542636,-0.03990446,-0.02965417,0.01744017,0.04779159,-0.03246088,-0.01879257,0.02564178,0.00446474,0.02217454,0.02367094,-0.00077771,0.02996238,-0.02555509,0.04595185,0.04436512,0.05172065,0.02087082,0.02796623,0.00883583,-0.12285008,-0.05191679,-0.01421023,-0.0218496,-0.01199352,-0.06985471,0.0231893,-0.03366051,-0.03650731,-0.05075684,0.03245027,-0.12123383,-0.04920098,0.0740647,0.00687895,-0.01127922,0.00900703,-0.04389052,-0.0038277,0.02866697,-0.00122801,-0.03610844,0.03786328,0.02201337,0.06480131,0.11096828,-0.03259413,-0.01775949,-0.01149935,-0.05188373,-0.04528446,0.16815892,0.022698,-0.06657833,-0.03172951,0.04451887,0.02844628,-0.04554208,0.01847434,0.00913025,-0.0577633,0.04789571,0.09669799,-0.03462004,0.00535548,-0.04518139,-0.10161686,-0.0026309,-0.01752567,0.03061069,-0.07788911,0.03274529,-0.00610646,-0.1216323,0.03914389,-0.00251264,0.04070564,0.010495,-0.01277257,0.07187451,0.04683524,0.07100919,-0.04821507,-0.03423033,-0.02684841,-0.04056414,0.05363296,-0.01769093,0.06417868,0.03885973,-0.032328,-0.01528526,-0.00446123,0.00155801,-0.00944221,-0.01184265,0.01288389,0.03882312,-0.0103861,0.05773966,0.01701706,0.01149555,-0.0263128,0.03513452,0.00685898,0.00607387,0.02713995,0.03257709,0.07039976,0.05311753,-0.10381927,-0.25074118,-0.00914916,-0.03509467,-0.06746576,0.02380849,-0.03051296,0.04283998,-0.04619177,0.08277258,0.04751414,0.07688238,0.02340181,-0.02601535,0.03796471,0.00038079,0.00136854,-0.00203789,-0.00577219,-0.03835263,0.02436734,0.02979798,0.06413541,0.0021644,0.03535056,0.07174067,-0.05054902,0.1314705,0.00071482,0.01107464,0.02445633,0.10193549,0.04886439,-0.00896646,-0.09559089,-0.00700226,0.00011402,-0.08984129,0.01270964,-0.07934701,-0.02026614,-0.04611035,0.02411551,-0.04429393,-0.08183869,-0.00721215,-0.00986132,-0.0240106,0.00166618,-0.04984055,0.00524891,0.0278215,0.00850122,0.01403288,0.02669469,0.03517237,-0.04599746,-0.06455163,-0.01707543,-0.06221887,0.01632302,-0.02071805,-0.00149792,0.03607699,-0.02580086,-0.02980667,-0.01173836,-0.00759972,0.01739315,-0.01449843,0.01064093,-0.02877539,0.11543231,0.01072969,0.03593216,0.01951596,-0.03467874,-0.03520706,-0.05133989,0.01195984,-0.01924155,0.01224991,0.01462194,-0.01116565,0.05001999,0.05219719,0.07098426,0.04060019,0.02692302,-0.00982712,-0.02338635,-0.02821678,-0.02327174,-0.03641155,0.01098217,0.05264829,-0.01820067,-0.28109699,0.03857033,-0.03189323,0.01421684,-0.02916835,-0.01258601,-0.01537093,-0.01344885,0.01994609,0.05084289,0.02652807,0.05091002,0.021256,-0.0379696,-0.01589554,-0.00482823,-0.04323312,0.01324505,0.06898903,-0.02591896,-0.00906846,-0.01969879,0.23897338,0.02204726,-0.00733311,0.00319369,0.01710256,-0.00798813,0.01672639,-0.01191445,-0.00065683,0.02126532,0.06994683,0.02618543,0.02218346,0.07221501,-0.0378577,0.03843143,-0.02422391,0.03150674,-0.0388717,0.04220601,-0.07315851,0.00909732,0.11224967,0.04321086,-0.00069104,-0.0807226,0.01218068,0.10071728,-0.03861117,-0.01617773,-0.01087663,0.01921901,-0.01139663,0.02987324,0.02336658,-0.04113011,0.04455215,0.04866078,0.04113758,0.07026817,0.01257689,0.0382413,0.0245657],"last_embed":{"hash":"8tz6cz","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"8tz6cz","at":1753495700274},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###MDIEditor Lotto WE Julia 專案規範#{1}","lines":[5,83],"size":3203,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10033882,0.01006571,-0.02624736,-0.04039543,-0.02343761,0.051002,0.0182792,0.00403222,0.04673317,-0.01682694,0.02821442,-0.07880524,0.02825395,0.01029923,0.04382337,-0.03584841,0.03012004,-0.06159557,-0.0876973,-0.01185875,0.09227459,-0.06756863,-0.01099506,-0.06722,0.05972201,-0.01859663,0.02249154,-0.00779319,0.00017884,-0.19513243,-0.01574636,0.01973869,0.04458509,-0.04048184,-0.01857792,-0.00094861,-0.01467539,0.01797003,-0.06287798,0.02154127,-0.00376547,-0.01240717,0.04792469,-0.04096103,0.03509353,-0.03318205,-0.03728625,-0.02233515,0.08592771,0.00551722,-0.04995332,-0.0182509,0.01697952,0.04107474,0.04969061,0.0334189,0.08005595,0.06131802,0.05148933,-0.00702899,0.0302383,0.03880891,-0.20526168,0.04315997,-0.02179162,-0.03579231,-0.01349603,-0.03227668,-0.00650618,0.01774358,-0.05336835,0.02510179,-0.03401639,0.0358264,0.03417295,-0.05508159,-0.01779786,-0.03298851,-0.05355861,-0.01309114,-0.01451429,0.02986334,-0.01050008,-0.01939517,0.01887743,0.06892934,0.0062237,0.0037778,0.01107216,-0.07779825,-0.0177218,-0.01700854,0.00220788,0.09323344,-0.04413106,0.00070199,0.03589265,0.04188023,-0.09824237,0.107216,0.04847032,0.02663584,-0.01844921,-0.0311256,0.03604615,0.015827,0.01488295,-0.04771211,-0.02993103,-0.01717933,0.0128557,-0.00422928,0.01102999,-0.03451994,-0.01763852,0.02058533,0.04767473,-0.03795828,-0.01027443,0.01979773,-0.00379488,0.02257758,0.03542283,-0.00021625,0.02936422,-0.03016684,0.0598529,0.02876232,0.05235715,0.02298986,0.02710291,0.01048318,-0.13126706,-0.05748813,-0.03331732,-0.02533677,-0.00503304,-0.05641351,0.02402632,-0.0261608,-0.03282899,-0.06662819,0.03183387,-0.12434404,-0.05574588,0.08769289,0.00368363,-0.01998133,-0.00805272,-0.05042709,-0.0016685,0.03087691,0.01186953,-0.04267162,0.0392073,0.01839339,0.06869072,0.1190893,-0.0390571,-0.01222798,-0.00976758,-0.05725088,-0.04157415,0.16962902,0.03497678,-0.06307939,-0.02061087,0.03702867,0.0220328,-0.04401359,0.01421407,0.00575689,-0.05751973,0.03474852,0.10248627,-0.02878032,0.01846042,-0.05031259,-0.11109446,0.00355641,-0.00807612,0.02594262,-0.07584408,0.03731398,-0.00516873,-0.11763383,0.02697371,-0.00398738,0.045153,0.00763797,-0.02658546,0.08128878,0.03857589,0.06427221,-0.05062307,-0.03300833,-0.0230851,-0.04612399,0.0456939,-0.02262827,0.06771415,0.03275622,-0.03256787,-0.01176995,-0.00988803,0.00335105,-0.01854983,-0.01135924,0.01292789,0.03602829,-0.00103018,0.06029962,0.02841802,0.00707692,-0.02583086,0.02420234,0.00541631,0.01507498,0.01708411,0.04197034,0.06100971,0.04001926,-0.10943506,-0.248032,-0.01138932,-0.02690809,-0.0625167,0.02679072,-0.02662046,0.04604967,-0.04091543,0.07932535,0.05659979,0.07481921,0.02755856,-0.02392547,0.0382434,-0.00239902,0.00054111,0.00568282,-0.01657282,-0.03857474,0.02925964,0.03491967,0.06408213,0.00286615,0.03985151,0.06886935,-0.06349327,0.12920332,-0.0152652,0.00300801,0.0260534,0.09417351,0.03693356,-0.00994735,-0.09397236,0.01113041,-0.0030135,-0.10046418,0.02002393,-0.06508832,-0.02659768,-0.04408361,0.02964834,-0.04350326,-0.07519326,-0.01738176,-0.01028223,-0.02653813,-0.00546886,-0.03526836,0.01155143,0.04127008,0.00319358,0.02393975,0.04278179,0.0380505,-0.04396431,-0.0681069,-0.00913632,-0.05192453,0.01187273,-0.0223501,-0.00253923,0.03334438,-0.0171583,-0.03507754,-0.01184447,-0.00633953,0.03388035,-0.03185056,0.01029762,-0.03095587,0.11727402,0.0235376,0.02398534,0.02506216,-0.03010255,-0.03251161,-0.05651697,0.0131923,-0.01616871,0.02021345,0.00026941,-0.00645575,0.04510182,0.04916809,0.08394302,0.05125308,0.02232711,-0.01141754,-0.02511325,-0.02698334,-0.02312901,-0.04623498,0.02333617,0.06347803,-0.00974973,-0.28677189,0.03587137,-0.03119083,0.01655592,-0.01222427,-0.00769487,-0.01128006,-0.01390322,0.01232828,0.04822455,0.02742728,0.0429257,0.01835617,-0.05262136,-0.0161934,-0.00555642,-0.03972398,0.00329665,0.04854755,-0.02224836,-0.01219716,-0.00139834,0.22966285,0.03456553,-0.00634941,0.00393084,0.0073904,-0.01002338,0.02881907,0.00255812,-0.00373096,0.0107909,0.07559384,0.0376509,0.01236582,0.07877087,-0.0328503,0.03932915,-0.02432067,0.03431609,-0.04050085,0.03691252,-0.06763517,0.00006332,0.10878441,0.03293526,-0.01092233,-0.09327149,0.02629746,0.08324753,-0.04227021,-0.01263282,0.00147729,0.02893302,0.00739059,0.03500936,0.02402964,-0.03413477,0.03607697,0.03548669,0.04344078,0.07398859,0.01097811,0.03463934,0.04493303],"last_embed":{"hash":"p0nsnj","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"p0nsnj","at":1753495700422},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md#---frontmatter---","lines":[7,82],"size":3126,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###二、設計 (Design)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09872225,0.02284127,-0.00923902,-0.03059714,-0.02028519,0.03558406,0.00778491,0.02077572,0.03734547,0.00018943,0.01970853,-0.06434941,0.03805879,0.00953346,0.06370737,-0.04830692,0.02031017,-0.01502628,-0.05312625,-0.04110665,0.08139431,-0.05543109,-0.01707602,-0.07831355,0.05459725,0.00770013,0.01736383,-0.01650342,-0.00089187,-0.1925514,-0.01129084,0.01788126,0.02600703,-0.04268764,-0.03385547,-0.00874489,-0.01845447,0.05971546,-0.03826911,0.0108725,-0.00426675,-0.01473304,0.04045442,-0.04097389,0.00520848,-0.03776614,-0.02822624,-0.05941382,0.04622835,-0.02602332,-0.06153556,-0.02484477,0.03884289,0.07338182,0.01583379,0.03282141,0.04141071,0.06610573,0.03331601,-0.02083974,0.02597919,0.01428273,-0.20776151,0.07896431,-0.03672862,-0.04561471,-0.0117857,-0.04586026,0.03580409,0.05095998,-0.05493864,0.00079904,-0.05750735,0.03040481,0.03687884,-0.0640871,0.01878065,-0.04097066,-0.03499992,0.00253938,-0.00268372,-0.04265292,-0.01847282,-0.01600914,0.00529153,0.07570785,0.00768483,0.00466226,-0.00288973,-0.05249545,-0.03618516,-0.02774695,0.01653692,0.08581617,-0.02520592,0.02744616,0.02938473,0.04499363,-0.08212239,0.10567688,0.05732426,0.02072408,0.02475964,-0.02850978,0.06443615,0.01258649,0.01968956,-0.03634366,-0.05254883,-0.01452478,0.02938563,-0.01081184,0.03093771,-0.05197592,-0.02566633,0.01010663,0.04164621,0.00508708,-0.02168979,-0.00823341,0.00276093,-0.01693373,0.04647689,0.00792646,0.05396451,-0.00436722,0.02197554,0.04683783,0.00705719,0.01839138,0.02925001,0.03056236,-0.08186094,-0.02831591,0.01699126,0.00085525,-0.01613491,-0.07319897,0.0311406,-0.0295307,-0.0712964,-0.02595453,0.00856484,-0.12497495,-0.05897781,0.08358299,-0.01115647,0.00726365,-0.02044346,-0.0485987,0.01606691,0.06806958,-0.01563878,-0.02099635,0.04514476,0.0130057,0.04900313,0.09914335,-0.00831724,-0.02012064,0.00299293,-0.05125722,-0.05347858,0.1774167,0.02136426,-0.05212808,-0.0327569,0.05863431,0.03627534,-0.0384475,0.00891388,0.00456692,-0.03557863,0.04624569,0.09423028,-0.03481276,-0.00879911,-0.01730975,-0.08006679,-0.0285285,-0.01139631,0.03165913,-0.08396425,0.04475169,0.01298397,-0.11189549,0.04234292,0.00003477,0.00649798,0.01247985,-0.0102172,0.05857347,0.04988425,0.07044455,-0.06707364,-0.04432267,-0.01788738,-0.03107764,0.03050633,-0.03474076,0.08504272,0.01377213,-0.05816311,-0.01815777,-0.02297864,0.00095778,-0.01565037,0.00504519,0.01455455,0.03475435,-0.04154059,0.06520461,0.01560238,-0.02430822,0.00659751,0.02677086,0.00574975,0.00836212,0.03142326,0.01270427,0.04614865,0.05161401,-0.10937031,-0.23320015,-0.00468206,-0.03184201,-0.04599124,0.02087135,-0.04654104,0.0248412,-0.04790886,0.09223443,0.04416845,0.08673384,0.04675679,-0.00782679,0.04982788,-0.0065884,0.01409344,0.06154072,0.02394247,-0.06794848,-0.01510107,0.03041787,0.07533519,0.01763209,0.0137831,0.04819444,-0.03903687,0.1462488,-0.02633837,0.0215764,0.07384982,0.09740454,0.0641321,-0.0063676,-0.08269423,0.00182729,0.04723119,-0.09228562,0.01497814,-0.07148848,-0.03434125,-0.03611079,0.03326266,-0.04003907,-0.09373318,-0.01092457,-0.02060194,-0.03773408,-0.02275973,-0.05973361,-0.01037747,-0.02338318,-0.00306,-0.00579502,0.02023767,0.04841679,-0.03396166,-0.06630046,-0.03041904,-0.05541092,0.01688561,-0.03899702,0.00075881,0.04425358,-0.0343688,-0.03236641,-0.0124662,-0.01171229,0.00256268,-0.00049375,0.0035012,-0.04624937,0.12652779,0.00905665,0.02510502,0.02173853,-0.0053577,-0.0469895,-0.04929263,0.00635369,-0.04085916,0.00250564,-0.00758291,-0.01878314,0.05433029,0.07364511,0.05009647,0.02778931,0.03281088,0.00983718,-0.03855292,-0.02207447,-0.02130951,-0.03833964,0.00071564,0.05828746,-0.02265161,-0.27699128,0.02282543,-0.02639671,0.01991459,-0.04834726,0.00273901,-0.00907567,-0.02365196,0.03118747,0.03713423,-0.00952506,0.06615556,0.00840182,-0.0214711,-0.03025899,0.00000475,-0.01700166,0.00159937,0.07010156,-0.03882904,-0.03308847,-0.01031386,0.23906171,0.01831037,0.00854063,-0.01915534,0.00460843,-0.01099438,-0.02634283,0.00592205,-0.01866841,-0.00394158,0.10573222,0.02262705,0.03582682,0.07149004,-0.0145918,0.01836556,-0.01862341,0.05106262,-0.04097479,0.049828,-0.08311953,0.01651853,0.11532822,0.030498,0.01655314,-0.06598177,0.02375749,0.10660054,-0.00671733,-0.00319168,-0.00515533,-0.00387532,-0.01245319,0.00639858,0.01625245,-0.03210878,0.03439388,0.0410497,0.04589121,0.06155923,0.04221007,0.03775889,0.05556754],"last_embed":{"hash":"1fs87nc","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"1fs87nc","at":1753495700553},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###二、設計 (Design)","lines":[84,121],"size":1507,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###二、設計 (Design)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11765689,0.02283661,-0.0001566,-0.01889462,-0.02317183,0.02551812,0.00900023,0.03034494,0.0539818,-0.01848601,0.01172863,-0.05824548,0.03526237,0.00502651,0.05595712,-0.06228824,0.03220399,-0.01089554,-0.04720108,-0.01310572,0.09201231,-0.04804802,-0.00740329,-0.08666048,0.04006991,-0.00481599,0.02499277,-0.0281136,0.01764845,-0.17508912,-0.01360495,0.02953303,0.0299021,-0.02720939,-0.04790598,-0.00896503,-0.01478062,0.0377197,-0.04290954,0.03827964,-0.00362172,-0.00644555,0.033623,-0.0290329,0.01338474,-0.02670592,-0.02985686,-0.05234464,0.06454235,-0.00913156,-0.05803612,-0.01817849,0.02569443,0.0532744,0.01147019,0.03045817,0.04798877,0.0547346,0.02798546,-0.03021826,0.03274811,-0.00005953,-0.21780995,0.06597083,-0.01771007,-0.0375593,-0.01320406,-0.03345796,0.03209222,0.0413782,-0.04554148,0.00982177,-0.05302497,0.02966378,0.05339934,-0.06190887,0.03479894,-0.03282504,-0.02432009,-0.01877519,-0.00278715,-0.04242548,-0.02029993,-0.00037238,-0.00399531,0.0646359,0.01044994,0.01582531,0.01297707,-0.08599052,-0.03471375,-0.04205842,0.01515768,0.0762502,-0.02265172,0.03401455,0.00264857,0.03039621,-0.09775092,0.11071184,0.04167383,0.0125554,0.03500329,-0.01622243,0.06052238,0.01533124,0.02059235,-0.03494057,-0.06816287,-0.01249976,0.02305366,0.00023777,0.0062856,-0.02600954,-0.01996781,0.00697976,0.05078653,0.01462849,-0.01472151,-0.02224931,0.01410631,0.02072221,0.03173018,-0.0077819,0.02225828,-0.03045535,0.03010778,0.03360509,0.00797114,0.03070245,0.01335451,0.03834475,-0.06835821,-0.04137537,-0.01455642,-0.00453411,-0.02654702,-0.06275132,0.02014007,-0.03577771,-0.0367706,-0.03493484,0.03606195,-0.12634702,-0.05155747,0.07041186,-0.0082769,0.01514342,-0.00878372,-0.02072711,0.01378897,0.04676849,-0.01734176,-0.02551245,0.03702877,-0.00420811,0.06787909,0.10315926,0.0075181,-0.0001984,0.00101397,-0.04060582,-0.05532234,0.17315741,0.02653989,-0.04798238,-0.0399776,0.04988375,0.04826109,-0.04198855,0.01314949,-0.00971913,-0.03330667,0.03632041,0.11237861,-0.02000976,0.00366672,-0.0319859,-0.07788704,-0.0220889,-0.01043095,0.03322767,-0.08674633,0.05005502,0.01598127,-0.12034774,0.01941871,-0.02596781,0.02683559,0.02454059,-0.01327503,0.08497078,0.01324381,0.07488598,-0.05749005,-0.01751613,-0.01483998,-0.04680623,0.01991567,-0.0019654,0.06774257,0.01856415,-0.07435384,-0.0375345,-0.0166029,-0.01150001,0.00019095,-0.00123322,-0.01484705,0.04446626,-0.04178691,0.06791478,0.0243464,-0.01979949,0.00855522,0.04085337,-0.00174625,0.00758223,0.04187203,0.04146188,0.04601292,0.03080196,-0.10595264,-0.23533513,-0.01149744,-0.03380099,-0.06497968,0.02397434,-0.06182963,0.04131645,-0.04520143,0.06579957,0.01016642,0.09437204,0.04861262,-0.00106991,0.04236938,-0.01066988,0.01487591,0.07464945,-0.0069149,-0.03754214,-0.02075391,0.02415311,0.06680868,0.02580552,0.01163501,0.04831582,-0.03324469,0.14609081,-0.02576012,0.03330516,0.07229003,0.09172984,0.05149204,-0.01121688,-0.11645444,0.00933974,0.04308979,-0.08091123,0.02459571,-0.05947761,-0.02906121,-0.04067231,0.05211236,-0.04158315,-0.08176407,-0.02572531,-0.03823358,-0.03102492,-0.04115106,-0.0592032,-0.014567,0.0018655,0.00880809,-0.00341321,0.03727766,0.03702322,-0.02562627,-0.06710848,-0.01999811,-0.05841513,0.01573015,-0.02648621,0.00762327,0.03949186,-0.04524018,-0.01361829,-0.00380262,-0.01441131,0.01776799,-0.00059855,0.01789976,-0.04059618,0.13664232,0.01242305,0.00388829,0.03065162,-0.01369761,-0.03772481,-0.05094892,0.00834943,-0.01336176,-0.01458671,-0.04506747,-0.01503128,0.02991193,0.07672066,0.07200844,0.01104987,0.0160958,-0.00258353,-0.04911222,-0.02195322,-0.00621045,-0.03793481,0.01025159,0.07168829,-0.01585271,-0.28408983,0.03293509,-0.00906287,0.01940266,-0.04581536,-0.00162892,-0.0009938,-0.04463029,0.01819037,0.06353084,0.00593239,0.04455347,0.00567393,-0.01760971,-0.02654561,-0.00443567,0.00716547,-0.0049439,0.05229034,-0.05577337,-0.0563346,0.00183404,0.23528084,0.03924771,0.0048925,-0.03558106,-0.00857751,-0.01038331,-0.02500471,0.026071,-0.00759639,0.01073845,0.06675293,0.03049134,0.01622252,0.07591961,-0.02899148,0.02618973,-0.03186321,0.05674409,-0.06194985,0.04553616,-0.09137637,0.0271992,0.1241202,0.02774876,0.00256573,-0.05177504,0.03654836,0.08382538,-0.02344371,0.00565169,-0.00767824,-0.0064095,0.00072331,0.00298011,0.00312109,-0.04848893,0.02179909,0.04936912,0.05538297,0.09033792,0.06017568,0.02547541,0.0651444],"last_embed":{"hash":"111733p","tokens":229}}},"text":null,"length":0,"last_read":{"hash":"111733p","at":1753495700687},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###二、設計 (Design)#{2}","lines":[88,93],"size":222,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###二、設計 (Design)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08556586,0.03281482,-0.00065726,-0.00474893,-0.00672049,0.05167515,0.00678951,0.00620272,0.03247724,0.00212077,0.02612858,-0.07517691,0.06883647,0.03701388,0.05103136,-0.02148352,0.00851234,-0.01343852,-0.05777357,-0.0642655,0.07454819,-0.0588934,0.00946127,-0.09378183,0.04870459,0.00442493,-0.01547874,-0.01365485,-0.00599302,-0.21047686,-0.02136001,0.00954515,0.01512888,-0.05441172,-0.0357268,-0.00234135,-0.03790886,0.06268083,-0.05114589,0.00140578,0.00323203,-0.02684702,0.00276355,-0.03483203,-0.00163157,-0.045302,-0.00285531,-0.05535354,0.0296441,-0.03042146,-0.07109155,-0.03115997,0.00515807,0.06482934,0.04464435,0.05067873,0.0271562,0.0347945,0.03699333,0.01161781,0.00974677,0.00078116,-0.21023984,0.06463912,-0.03903303,-0.04739477,-0.01062116,-0.05065189,0.02241203,0.03899421,-0.0521634,-0.00368195,-0.05303514,0.02008774,0.03700874,-0.03814168,-0.00531992,-0.03212597,-0.04298757,0.02462459,-0.00885492,-0.01244942,-0.00291848,-0.02064742,0.02246308,0.0624794,0.01356183,0.00987889,-0.00129028,-0.0229119,-0.01644791,-0.02537563,0.00587334,0.06143049,-0.01649713,0.03150584,0.04628113,0.05451939,-0.09652926,0.11217836,0.04366766,0.02224314,0.01066341,-0.01925196,0.04556988,-0.02337533,0.02236448,-0.03880299,-0.02133563,-0.01430302,0.01647221,0.01301885,0.01415536,-0.05203984,-0.00456885,0.00341555,0.03622402,0.00373575,-0.03360341,0.02339009,-0.01811011,-0.01758537,0.03422527,0.00444125,0.08146741,-0.00400832,0.0226627,0.04407474,0.02609756,-0.00802064,0.02593193,-0.00509219,-0.08796784,-0.03167209,0.01390559,-0.01757595,0.01025296,-0.05374726,0.03384773,-0.02254733,-0.05690928,-0.01425466,0.0147255,-0.11913409,-0.06483969,0.09696497,-0.02590685,-0.01549329,-0.00310664,-0.05839511,0.01627856,0.07820947,0.01560783,-0.03739681,0.06394497,0.01113992,0.07613964,0.1214022,-0.02301049,-0.01025476,-0.0020495,-0.03871329,-0.04723248,0.16794692,-0.00617637,-0.06116581,-0.03699587,0.05366535,0.00774732,-0.02812151,-0.00366481,-0.00319965,-0.02814249,0.02839773,0.09446015,-0.02540021,-0.00464751,-0.04280163,-0.06577723,-0.01892779,-0.01843724,0.02596673,-0.04483843,0.01233969,0.00256895,-0.11356725,0.01971959,-0.00521167,-0.01932319,0.02241107,-0.04300213,0.03478687,0.05770676,0.0961886,-0.03658905,-0.05900472,-0.01781218,-0.04088301,0.03356712,-0.07473689,0.08642815,-0.00319162,-0.03330541,-0.00522241,-0.04446495,0.00131832,-0.01854156,0.0005465,0.04050416,0.03513938,-0.03916578,0.03547363,0.02115866,-0.02497603,-0.0019605,0.03048364,-0.0033828,-0.00659772,0.02224331,0.01836989,0.04466899,0.04464689,-0.12036998,-0.24379805,-0.00411142,-0.03995948,-0.02234284,0.00544968,-0.04440441,0.04224244,-0.03329258,0.08697562,0.09526367,0.06091079,0.0412093,-0.02467853,0.02085977,-0.00065918,-0.01853373,0.06378467,0.01647268,-0.06944805,-0.01415179,0.04179621,0.07465832,0.02578884,0.01879331,0.04121089,-0.05253645,0.15562254,0.00978091,0.0313234,0.04471158,0.09008192,0.08197559,0.01478749,-0.05920028,0.00141031,0.031079,-0.09391871,0.00427274,-0.06635642,-0.04087326,-0.0144446,0.04658853,-0.03115354,-0.10074468,0.00376901,-0.02548379,-0.02029798,-0.00778734,-0.06187814,0.00054096,-0.00311547,-0.03488393,0.03069747,0.02200549,0.05442556,-0.02930649,-0.07554048,-0.03307991,-0.04966281,-0.01706759,-0.02719538,-0.00277714,0.05434005,-0.02370801,-0.04254358,-0.00124443,-0.00906191,0.01734255,0.00608309,0.00990175,-0.03610565,0.11623028,0.02084861,0.0302994,0.01893424,0.02791083,-0.04896592,-0.05124643,-0.00383544,-0.02780567,0.01882579,0.03354657,0.00176331,0.03735831,0.07175997,0.02421709,0.03652136,0.02764207,0.03462569,-0.04044373,-0.02725633,-0.00011815,-0.02982574,0.01843875,0.06808496,-0.00893272,-0.27666363,0.02363934,-0.02619991,0.01966517,-0.01941429,0.00479952,-0.01345761,-0.03286817,0.03227171,0.01118316,-0.0168795,0.05810851,0.02127013,-0.03764312,-0.05364968,0.01227312,-0.01504884,-0.01415408,0.08957551,-0.02751157,-0.01043781,-0.00372888,0.23992562,0.03807106,-0.00255726,-0.02212222,0.00177606,-0.032876,-0.00655455,0.00080504,-0.03508561,-0.00013297,0.13391654,0.02855177,0.04964258,0.08175934,-0.01832776,0.01622824,0.0013857,0.02079803,-0.02770134,0.0707259,-0.06524177,0.02399897,0.1023195,0.00526973,0.01164483,-0.08263642,-0.00991465,0.09538891,-0.01194582,-0.01041919,-0.01132286,0.01111301,-0.02277544,0.00168903,0.0291253,-0.02478737,0.03503919,0.03178059,0.03788026,0.03625426,0.0206945,0.03196066,0.06300215],"last_embed":{"hash":"c54e6a","tokens":503}}},"text":null,"length":0,"last_read":{"hash":"c54e6a","at":1753495700753},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###二、設計 (Design)#{3}","lines":[94,102],"size":666,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10954759,0.00407842,-0.01750921,-0.02991287,-0.03948061,0.00910069,0.00986647,0.00915973,0.04063717,0.00083991,0.01908161,-0.05620816,0.03159627,0.03065934,0.0404818,-0.03480932,0.02397603,-0.01880343,-0.0573673,0.01541243,0.05900547,-0.07572783,-0.01690239,-0.11893875,0.04639972,0.0195647,0.00625272,-0.05758643,-0.02866428,-0.20911999,-0.02018558,0.02498287,0.01505038,-0.03284798,-0.02689847,-0.03171149,-0.01395763,0.03270782,-0.06951652,0.00927417,0.03725131,-0.01503159,0.00991055,-0.00606465,0.00215005,-0.09194278,-0.0359651,-0.04503876,0.04535634,-0.00361378,-0.06168562,0.02421694,0.02311193,0.02098836,0.01087536,0.03840615,0.05721228,0.0869777,0.01119582,0.03564889,0.01447859,0.00713014,-0.19184412,0.08316564,-0.03237602,-0.02455307,-0.00108495,-0.04313711,0.03040128,0.03139272,-0.05091868,0.02809562,-0.03578674,0.04333499,0.05519924,-0.07921983,0.02803991,-0.03290558,-0.03771963,-0.01135219,-0.01554792,-0.022547,-0.01067118,0.00048648,0.00598711,0.04490935,0.00492962,0.01570364,0.01334585,-0.07097106,-0.02733705,-0.02825816,-0.00300664,0.08621927,-0.01529038,0.01808267,0.01850332,0.03360949,-0.058368,0.1187612,0.01986499,0.0339289,-0.00502218,-0.01358389,0.05057548,-0.01849151,0.026544,-0.05714126,-0.02978839,-0.03319218,-0.00202386,-0.04023187,-0.00490813,-0.06676019,-0.0346199,0.03441908,0.03021597,-0.00905551,-0.0435804,0.00000112,0.04013523,0.03728927,0.0097355,-0.0058541,0.03665385,-0.03047282,0.02246183,0.01849103,0.00397268,0.02790698,0.05623075,0.02396891,-0.08697093,-0.02831717,-0.03882021,-0.01986113,-0.00741942,-0.07580167,0.02431948,-0.03760349,-0.0440056,-0.05948503,0.04348235,-0.09371464,-0.06925696,0.10658506,0.00467926,0.03703468,0.03324707,-0.05005092,-0.01131071,0.0864004,0.01025505,-0.02810809,0.0627303,0.00930135,0.05164144,0.09299466,-0.00962187,-0.04097124,-0.04709958,-0.07338618,-0.01096678,0.18722194,-0.00967368,-0.06328513,-0.03772649,0.03128263,0.00939699,-0.03068794,0.04222742,-0.00317422,-0.04119059,0.0109249,0.11056164,-0.01648019,0.00545737,-0.03940875,-0.05191881,-0.00158562,-0.01715923,0.03146318,-0.07472949,0.04655172,-0.02193582,-0.07419475,0.01488035,-0.00119912,0.03251564,-0.0006592,-0.02987976,0.04375964,0.04364931,0.04078686,-0.02666929,-0.04007259,0.01999294,-0.01962506,0.03221537,-0.03349361,0.10796152,0.03392674,-0.02727484,0.02444739,-0.04127665,-0.02359609,-0.0126403,-0.01727165,0.00043446,0.03458818,-0.02499408,0.04420206,-0.01117449,0.00121261,-0.03153653,0.02863016,-0.00335117,-0.01564813,0.02021662,0.08247317,0.02118692,0.0415364,-0.06759696,-0.24111369,0.00920767,-0.03746315,-0.06659079,-0.0253927,-0.03640699,0.0321751,-0.02891954,0.05440116,0.05487058,0.1246179,0.02057909,-0.01392793,0.05590621,0.02686829,0.00038496,0.02538777,-0.03152699,-0.05267192,-0.04216664,-0.00225555,0.05181723,0.00769094,0.01819178,0.06235225,-0.02225447,0.14411013,-0.01062379,0.01727848,0.00330627,0.09655546,0.01899377,0.01169733,-0.10100204,0.00731331,0.00359137,-0.06434343,0.04359568,-0.07080556,-0.00371795,-0.04096292,0.0378301,0.00487156,-0.11022524,0.0220945,-0.01323944,-0.02929285,-0.02910094,-0.04566527,0.02199933,0.01712302,0.01212133,0.00753696,0.0766141,0.02689142,-0.02688728,-0.07847665,-0.04216177,-0.07456941,-0.00767004,-0.01637827,0.00304888,0.02125253,-0.02424171,0.01253589,-0.01413892,-0.01132475,0.02143456,-0.00825596,0.00593816,-0.04659646,0.11888874,-0.01195996,0.03394357,0.01626767,-0.00526274,-0.05233171,-0.04778633,0.04059757,-0.01132238,0.01961643,0.02690477,0.00346562,0.05825255,0.04229468,0.08445707,0.07210712,0.04069815,0.03998249,0.00641647,-0.02039423,-0.01303886,-0.04846524,0.00004076,0.08566343,-0.01707186,-0.28016308,0.04758986,-0.0325043,-0.02161788,-0.02522996,-0.00089391,0.00987989,-0.03721554,0.01585008,0.05760986,0.00423149,0.05867465,0.01306259,-0.03392789,-0.03051195,-0.0110789,-0.01481866,-0.0004367,0.08511222,-0.01725271,-0.03420183,-0.00073389,0.25108445,0.01819065,0.03579504,-0.00203331,0.02828121,-0.00811897,0.00473865,0.02714115,-0.03362757,0.00622973,0.07549511,0.04452072,0.00526331,0.06848625,-0.00323995,0.05246725,-0.01098469,0.02410603,-0.04186982,0.05713575,-0.08618748,-0.00819557,0.09953531,0.02400207,-0.03577411,-0.04644759,0.02488071,0.05847579,-0.02844058,-0.00103794,0.00406849,-0.02083685,-0.01263716,0.00581097,0.06278475,-0.02174755,0.01528452,0.06274456,0.05131393,0.0463657,0.03424772,0.05147135,0.02048298],"last_embed":{"hash":"187zwzb","tokens":424}}},"text":null,"length":0,"last_read":{"hash":"187zwzb","at":1753495700924},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)","lines":[122,172],"size":2273,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11434047,-0.0006776,-0.01338464,-0.02809825,-0.04897908,0.01574785,0.00148774,0.00226289,0.03098423,-0.0108513,0.01359294,-0.05844104,0.04036602,0.03720467,0.03904059,-0.03921171,0.01620347,-0.00701726,-0.05473807,0.02182313,0.06026295,-0.07435098,-0.03454234,-0.10576003,0.05180606,0.03033426,-0.00897193,-0.05758011,-0.00948117,-0.19797876,-0.002562,0.04197101,0.01284583,-0.03263273,-0.02369395,-0.02847132,-0.03115929,0.03006444,-0.08278365,0.01089886,0.03776254,-0.02132264,0.01759941,-0.00832671,0.01067885,-0.09031981,-0.03783714,-0.04067738,0.04569571,-0.00282465,-0.07192345,0.02034323,0.01792331,0.0211165,0.01984048,0.04468567,0.05034531,0.09099412,0.01680869,0.03435499,0.03304692,0.00841231,-0.17724143,0.07457189,-0.02695269,-0.00132576,-0.00237401,-0.04387181,0.02251798,0.04074834,-0.03470363,0.04814404,-0.0350916,0.0539928,0.06463324,-0.05991454,0.01759707,-0.04345421,-0.03684466,-0.0130761,-0.02091585,-0.03357059,-0.01563982,-0.0016439,-0.01815225,0.05394115,0.00031562,0.02145117,0.01398875,-0.05742233,-0.03428615,-0.03215891,0.00065217,0.08751189,-0.01887331,0.03454139,0.00278413,0.01688351,-0.0441637,0.12469108,0.02289525,0.03203855,0.00942442,-0.00370219,0.03477002,-0.01324914,0.01702049,-0.06826156,-0.03751535,-0.01729505,0.01702449,-0.05043871,-0.0102752,-0.07521369,-0.02705896,0.01443389,0.03174496,0.00093232,-0.04463217,0.00266049,0.0227179,0.03463998,-0.00250754,0.01354778,0.02555553,-0.01921299,0.02085212,0.0121156,0.00609825,0.04200304,0.03154566,0.04046718,-0.10850518,-0.03638561,-0.04397076,-0.00859099,-0.0047881,-0.07630675,0.01660101,-0.02731795,-0.05576569,-0.05718118,0.05108633,-0.08883582,-0.07015443,0.11263368,0.01570884,0.03132562,0.02537119,-0.04251174,-0.03291169,0.08153919,0.00608319,-0.02467986,0.04119689,0.01004084,0.04750489,0.09663978,-0.01427689,-0.0457846,-0.05023079,-0.07921945,-0.0203163,0.19019547,-0.0156295,-0.07370555,-0.04147066,0.02959517,-0.00640553,-0.02136561,0.04680171,0.01650149,-0.04239836,0.00732953,0.10720299,0.00091431,0.00849211,-0.03111601,-0.05119971,-0.00080234,-0.01036573,0.03443266,-0.06774382,0.02874795,-0.02417582,-0.08175007,0.01473144,-0.00105571,0.02655584,0.02100346,-0.02338834,0.04352095,0.04706227,0.04538411,-0.02383412,-0.04745338,0.02082616,-0.01641899,0.04416515,-0.02373222,0.10709886,0.05260959,-0.02753577,0.03816935,-0.03247068,-0.02366875,-0.01510083,-0.01443778,0.00579578,0.03444271,-0.0330827,0.03646383,-0.01198134,0.03302398,-0.04890891,0.02370911,0.01490229,-0.00030975,0.03877432,0.06422943,0.01306828,0.04522307,-0.04377418,-0.24013782,0.0175549,-0.0494031,-0.05958452,-0.02905624,-0.04288241,0.02837691,-0.02550703,0.03108722,0.03925966,0.11593083,-0.00591199,-0.00905582,0.06371924,0.02395137,0.0194477,0.00572678,-0.05449929,-0.04958098,-0.04864429,-0.00194528,0.04054272,-0.00589208,0.01045954,0.06024314,-0.01648992,0.14882675,-0.01466471,0.01510032,-0.01076789,0.09470467,0.01474529,0.01191147,-0.10268717,0.00259425,0.00937954,-0.05760156,0.04513718,-0.07347167,0.00290013,-0.03649453,0.03813171,0.0059164,-0.1277363,0.01364155,-0.00855127,-0.0257376,-0.01871216,-0.03490973,0.02549518,0.00861353,0.01176216,0.01077532,0.07842355,0.0163199,-0.02095618,-0.08582526,-0.03506918,-0.07603126,0.01229806,0.00134457,0.01308669,0.01623531,-0.03161832,0.01652064,-0.01050939,-0.00978493,0.03154844,-0.00863769,-0.00649465,-0.03321413,0.10030832,-0.01397909,0.03738497,0.00695005,-0.00062185,-0.04884375,-0.03238764,0.04164058,-0.01192149,0.02194119,0.00459719,0.00855921,0.07534458,0.0370466,0.07572229,0.06586021,0.03262518,0.04663113,0.01530435,-0.0151238,-0.00705148,-0.04264497,-0.00121854,0.09344961,-0.02540499,-0.27985176,0.05911631,-0.04011553,-0.00290464,-0.03266128,0.00051748,0.00182842,-0.04411596,-0.00394935,0.03939632,0.02656701,0.05120847,0.01232849,-0.04873085,-0.03754896,-0.02100599,0.005325,-0.00590911,0.09338927,-0.0358559,-0.02749902,0.01577753,0.25131983,0.02334736,0.02664794,-0.00161528,0.01862298,-0.01414777,-0.00415282,0.03716966,-0.02395005,0.01320209,0.0743464,0.03618937,-0.00271187,0.06572431,0.00033886,0.06347354,-0.00624025,0.02610257,-0.05028183,0.04824773,-0.0922647,-0.00317187,0.10319706,0.03264194,-0.04135459,-0.05297089,0.03464076,0.05352104,-0.03765977,-0.0046864,-0.00424484,-0.02943625,-0.00292838,-0.00947071,0.0641799,-0.02340657,-0.00349473,0.0661976,0.05778136,0.04551135,0.05555276,0.02587349,0.01223465],"last_embed":{"hash":"6omw98","tokens":183}}},"text":null,"length":0,"last_read":{"hash":"6omw98","at":1753495701049},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{2}","lines":[126,130],"size":291,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10564279,0.01308765,-0.00737848,-0.03679208,-0.02425151,-0.00030544,0.03893093,0.01259495,0.03959269,0.00832969,0.03805645,-0.07081985,0.02559952,0.00869035,0.06087024,-0.04008351,0.02765731,-0.01661161,-0.05791605,-0.01679444,0.05561293,-0.05399358,-0.01664614,-0.08921352,0.04902799,0.01044999,0.01528205,-0.04537148,-0.02926513,-0.20920712,-0.04653217,-0.0018179,0.02983396,-0.0252406,-0.01762024,-0.00550644,-0.00881237,0.01959824,-0.04322459,-0.00846033,0.01137158,-0.02550708,-0.00665941,-0.02497726,-0.039673,-0.08889037,-0.03018757,-0.04426294,0.0524611,0.00185426,-0.04919282,0.00949058,0.0234897,0.04087939,0.03044083,0.04109788,0.05719218,0.05726189,0.02176474,0.03092169,0.00210838,0.02304241,-0.20334291,0.08864435,-0.04236513,-0.04490113,-0.01635996,-0.03810158,0.01104062,0.02337569,-0.07794674,-0.01894852,-0.05854937,0.06221145,0.03274733,-0.07815969,-0.00677201,-0.02577557,-0.03237902,-0.013246,-0.0086705,-0.00384691,0.00348474,0.01300702,0.02321566,0.05799546,-0.00704269,0.01702481,0.02227523,-0.08000379,-0.00581525,-0.01987628,0.0092468,0.07060714,-0.03419513,-0.00292154,0.05841399,0.08259699,-0.07666791,0.10835361,0.01196543,0.03531172,-0.00934544,-0.04077224,0.05747731,0.01034894,0.03377012,-0.05637583,-0.02650422,-0.0315667,-0.0066022,0.00187488,0.00838378,-0.05719353,-0.01485058,0.03694312,0.02946932,-0.01450404,-0.03998522,0.01461758,0.06175055,0.01266715,0.04760551,-0.01087317,0.05797058,-0.03389291,0.070225,0.03241901,0.00252098,0.00733243,0.07975973,0.0080385,-0.09594139,-0.02229981,-0.00939361,-0.02797243,-0.00370483,-0.0781344,0.01736428,-0.04441882,-0.02930004,-0.04049469,0.01379945,-0.10732972,-0.03785411,0.09517213,-0.0043238,-0.00272608,0.03361835,-0.07680092,0.00925868,0.07834946,0.01257455,-0.03597285,0.05460485,0.01553523,0.06433962,0.08307386,-0.03453642,-0.01804955,-0.01759345,-0.04988906,-0.02090121,0.19692166,-0.00242437,-0.05484046,-0.04087003,0.03247457,0.02210107,-0.03195067,0.0169048,-0.02787939,-0.01968817,0.02077515,0.10896567,-0.028859,-0.00140005,-0.04288911,-0.07187112,-0.01111288,0.00021093,0.0013908,-0.0620398,0.05283817,-0.01542656,-0.07080106,0.020264,-0.00957117,0.04221186,-0.02095314,-0.03427283,0.0422876,0.04169318,0.02177092,-0.03690348,-0.02494954,-0.00230035,-0.0325685,0.01632695,-0.03604563,0.08405468,0.01969254,-0.02935228,-0.00112015,-0.03339482,-0.02016042,-0.01380302,-0.00672957,0.01575192,0.045704,0.00231357,0.04951737,0.02148498,-0.04904347,0.01371032,0.01522302,-0.03161399,0.01211543,0.00268546,0.08559574,0.04030021,0.05847432,-0.08729602,-0.23765494,-0.00253052,-0.01213339,-0.06302826,-0.00473341,-0.01767107,0.02540954,-0.02755551,0.07579932,0.04779481,0.11761165,0.05181089,-0.02071576,0.00798946,0.01546196,-0.02354519,0.06880951,0.00397714,-0.04833961,-0.02186912,0.0215684,0.04769141,0.0185921,0.03660339,0.05422861,-0.05274632,0.12226965,0.00439203,0.04734069,0.02753598,0.08522876,0.06247827,0.00880086,-0.10519438,-0.01063611,-0.01262298,-0.08182062,0.03291242,-0.05641579,-0.02562302,-0.03279859,0.02011602,-0.01242979,-0.07196052,0.01609504,-0.0305689,-0.01618037,-0.04808252,-0.04550954,-0.00889889,0.01018875,0.0141503,0.01433584,0.0646804,0.04307054,-0.03491589,-0.0739354,-0.03782737,-0.06313011,-0.04424814,-0.03615527,-0.01249258,0.02550668,-0.01314205,0.0066989,0.00009974,0.01580776,-0.0214602,-0.01605163,0.01799093,-0.05086846,0.12842819,0.00780401,0.01077592,-0.00583633,-0.01073036,-0.04590461,-0.03675998,0.02177122,-0.02848788,0.01345686,0.05085696,-0.00105265,0.02643955,0.04976659,0.06915683,0.06509168,0.04048645,0.02023972,-0.02080086,-0.00917822,-0.03595956,-0.02319343,0.01631182,0.08209638,0.00712546,-0.28286591,0.03386154,-0.03283265,-0.02888809,-0.01602187,0.01435049,0.00377312,-0.03918552,0.02717624,0.07803372,-0.015158,0.06392515,0.01600302,-0.01690582,-0.03397037,0.01401771,-0.00786652,0.00637324,0.06665079,-0.02062319,-0.01593967,0.00514609,0.25428283,0.00189995,0.02424929,0.01159972,0.00220056,-0.02130833,0.03771731,0.02724255,-0.02494781,-0.01616298,0.08385631,0.0443223,0.03077139,0.08733127,-0.01223992,0.04799139,-0.00366186,0.04391842,-0.04734902,0.05725448,-0.08679406,-0.0247728,0.09396286,0.01278134,-0.03735872,-0.07339235,0.01722857,0.05782145,-0.01559252,-0.02811032,-0.00168598,0.00766591,-0.0250431,0.03361232,0.01519978,-0.01309103,0.02883757,0.03026106,0.03820747,0.04877614,0.03144509,0.05575361,0.03133515],"last_embed":{"hash":"1845ph3","tokens":273}}},"text":null,"length":0,"last_read":{"hash":"1845ph3","at":1753495701099},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{3}","lines":[131,136],"size":315,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08172793,0.02183542,-0.02628798,-0.0146701,-0.00397378,0.02269806,0.02653378,0.03625478,0.03863114,-0.00365869,0.03126108,-0.05989137,0.01434761,0.00433891,0.03599329,-0.03025423,0.01806023,-0.03436445,-0.07958344,-0.02810556,0.07548964,-0.0595517,0.02694144,-0.08390964,0.06181241,0.00692453,-0.0001781,-0.05643513,-0.0098448,-0.20599937,-0.03018383,-0.00433012,0.05399264,-0.03878702,-0.02174739,-0.01142256,-0.00689706,0.05424798,-0.02315864,-0.00715706,0.01260726,-0.00445057,0.03138165,-0.03061456,-0.00921505,-0.046362,-0.03281853,-0.04525811,0.04444144,-0.0072839,-0.0801897,-0.03141871,0.02863055,0.04957563,0.00936355,0.02355042,0.08547284,0.03362284,0.03755196,0.02670449,0.03164642,0.05456346,-0.21926332,0.05774813,-0.02763382,-0.03364013,-0.04746576,-0.03235568,-0.01100901,0.02758222,-0.0767321,-0.00447183,-0.03159687,0.05471952,0.05455774,-0.04932188,0.00219519,-0.01921413,-0.02867515,-0.01494587,0.00009877,-0.00300413,-0.01755531,0.02946385,0.01247384,0.04532717,0.03919244,0.04090751,0.04240056,-0.06743529,0.00165804,-0.01915413,-0.02601836,0.09173875,-0.0470508,0.01997191,0.04916985,0.03733637,-0.0622673,0.11176146,0.01118229,0.0192499,0.01877745,-0.03688813,0.0297728,0.01222291,0.02567409,-0.04352875,-0.03375349,-0.01310944,0.01278732,0.00038349,-0.01077432,-0.08771782,-0.04587944,0.01326404,0.05000802,-0.00650757,-0.03691727,0.0153442,0.04493703,0.00753336,0.03854454,0.00543331,0.0585597,-0.02126705,0.0526895,0.06927872,0.01962156,0.0103693,0.05095629,0.03139611,-0.1280622,-0.03306236,-0.01792336,-0.06353474,-0.02978086,-0.04768679,0.02898574,-0.0173097,-0.02364476,-0.02336892,-0.00515732,-0.1029385,-0.05366978,0.09351043,0.00642881,0.00610449,0.05155128,-0.05121176,0.01897785,0.07357903,-0.00536391,-0.04429334,0.05956578,0.00640223,0.04970828,0.08160766,-0.0146672,0.03303708,-0.04834395,-0.07280664,-0.00262719,0.17275815,0.02508738,-0.04137164,-0.02769838,0.05553821,0.02345116,-0.0468748,0.01213206,-0.01745696,-0.01377951,0.0036324,0.08871157,-0.01403942,0.00598305,-0.00686952,-0.04573542,0.01277623,-0.00104308,0.01673178,-0.10578599,0.05826638,-0.02693857,-0.08975461,0.04191548,-0.00302245,0.03939309,0.03850589,-0.01524859,0.03252041,0.05074418,0.04763157,-0.03642314,-0.03770565,-0.00132088,-0.0417017,0.0245758,-0.03901664,0.06435308,-0.0036062,-0.02150253,-0.01507284,-0.01026929,-0.01828319,-0.01473804,-0.0007148,0.03642697,0.05748238,-0.01671122,0.05503644,0.02747064,-0.0338981,-0.04043198,0.01320711,-0.04544163,0.01338964,0.00739423,0.0456198,0.03161971,0.0325112,-0.094943,-0.24583191,-0.00463535,-0.00623332,-0.03277158,0.01341006,-0.0461866,0.01321531,-0.01929885,0.0721229,0.06514163,0.0933188,0.04444189,-0.01568276,0.02014661,0.01288531,-0.02864675,0.02801744,-0.0187816,-0.03914022,0.014987,0.03443129,0.02589912,0.01936784,0.05202189,0.05812069,-0.04708857,0.12569055,-0.01051931,0.05361492,0.03138879,0.0640775,0.04879467,0.01688407,-0.09361814,-0.01780088,0.0336824,-0.09435041,-0.00859243,-0.08646815,-0.00136129,-0.00007703,0.06707781,0.01714125,-0.07297862,-0.01920377,-0.04833502,-0.00744978,-0.02510874,-0.04692339,0.00489634,-0.01341602,-0.00035043,0.02311843,0.05620915,0.00408088,-0.01872998,-0.07061166,-0.01604338,-0.08295367,-0.03481417,-0.02036026,-0.00396757,0.06328843,-0.02169212,-0.0228817,0.01071415,-0.01226131,-0.01755784,-0.00439466,0.00794458,-0.04577212,0.14760898,-0.01391659,0.01746669,0.00105493,0.00008655,-0.02932132,-0.09549091,0.01556467,-0.04371407,0.03637756,0.02812368,-0.00314888,0.02598478,0.04130032,0.03805923,0.03129658,0.03344556,0.01208437,-0.0102212,-0.02099415,-0.02238898,-0.01121976,0.01683081,0.06733276,0.01836486,-0.28407314,0.0042624,-0.04378258,0.00822496,-0.00301555,-0.00471557,-0.01126045,-0.04009994,0.01841143,0.06724573,0.00637704,0.05952543,0.00593949,-0.0266091,-0.02476704,-0.01359228,-0.0063915,-0.01120853,0.07373992,-0.02232885,-0.02912688,-0.03584774,0.24604745,0.02022309,-0.01350873,-0.0054976,0.00392344,-0.03717999,0.00810655,0.01358378,-0.02708927,-0.01393622,0.08117974,0.0633672,0.02381167,0.09104637,-0.01718298,0.00846749,-0.02321973,0.01738869,-0.02760426,0.02330226,-0.09151413,-0.00239471,0.11382265,0.04819563,-0.02399457,-0.09319191,-0.00885579,0.0736456,-0.04485063,-0.03944949,-0.02729298,-0.01097721,-0.00907062,0.03892823,0.03558972,-0.02530073,0.0266745,0.02474876,0.03352196,0.02002767,0.06680012,0.0796351,0.03277713],"last_embed":{"hash":"1chgmc1","tokens":292}}},"text":null,"length":0,"last_read":{"hash":"1chgmc1","at":1753495701178},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{4}","lines":[137,143],"size":355,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11720416,0.02358352,-0.0035894,-0.00913451,-0.01883481,0.01852173,0.04030222,0.0176144,0.03976282,-0.02825702,0.02760864,-0.07494617,0.03975049,0.03600952,0.06740188,-0.04280087,0.02329004,-0.00704374,-0.0398474,-0.04002715,0.08186988,-0.04598695,-0.00453032,-0.06421737,0.0586443,0.00379492,0.0081461,-0.03027601,0.00294204,-0.19214904,-0.01211007,0.00618754,0.02941679,-0.04734874,-0.02489071,0.03383383,-0.03916752,0.0537275,-0.06214849,0.00816107,0.01683482,-0.00136174,0.03975832,-0.05625109,-0.01483466,-0.05503278,-0.02943606,-0.04472981,0.03770091,-0.00904095,-0.0495524,-0.03181287,0.00800101,0.05146967,0.03995578,0.03879441,0.04139036,0.06374258,0.03135202,0.02381577,0.01845017,0.01943622,-0.2089527,0.05824661,-0.0581507,-0.03274332,-0.01979501,-0.02198058,0.00878399,0.04912933,-0.06570654,0.00179337,-0.03325496,0.03613823,0.02012528,-0.05471666,0.024648,-0.01595809,-0.05072032,-0.00922828,-0.01134016,-0.02621109,-0.01239077,-0.0060198,0.03365201,0.07499277,0.00734598,0.02639866,0.03836678,-0.05908681,-0.02059882,-0.0227903,-0.00477189,0.06406295,-0.02611059,0.02621287,0.0244344,0.06601074,-0.10373732,0.10976087,0.03616594,0.05142815,-0.00311878,-0.04598163,0.05943962,0.01320646,0.00798327,-0.03431439,-0.03341992,-0.0059564,0.02409461,-0.01891177,0.02828611,-0.07315925,-0.01007792,0.0254606,0.05928381,0.01982339,-0.0503071,0.00414406,0.03535716,0.00432641,0.04399161,-0.0003401,0.03879201,-0.00673908,0.03769445,0.05268614,0.01080512,-0.00359412,0.04541489,0.012826,-0.1008682,-0.03621479,-0.01115283,-0.03260623,-0.02599868,-0.06198341,0.03959622,-0.02599932,-0.07008195,-0.03111041,0.01354175,-0.11734121,-0.04856898,0.10435181,0.00234045,-0.03255035,0.0181893,-0.0506449,0.01696168,0.07259043,0.00795727,-0.04730339,0.04270498,-0.00072441,0.05365589,0.11898676,-0.02528062,0.00409543,-0.02062061,-0.04901595,-0.03094789,0.16167119,-0.00194933,-0.0503336,-0.03846243,0.04059925,-0.00585014,-0.05268054,0.01313205,-0.04580541,-0.01119673,0.00803332,0.09833197,-0.01865393,0.00201428,-0.01878374,-0.07519083,-0.01618196,0.00850136,-0.01119513,-0.09358542,0.02588567,-0.00407752,-0.08573216,0.05384799,-0.02653067,0.02190585,0.0091418,-0.06703049,0.04594786,0.04007647,0.04650416,-0.03618451,-0.03734347,-0.02005481,-0.03208271,0.01378901,-0.02982788,0.08079414,0.01914326,-0.02821643,0.00751437,-0.02615685,-0.01455409,-0.0267797,0.00098888,0.02191691,0.07294796,-0.03554238,0.07330193,0.00812147,-0.01996145,0.03268299,0.01066339,-0.02071999,0.01448174,0.00422626,0.02176678,0.05352122,0.06020278,-0.09331961,-0.24575873,0.01553832,-0.04681516,-0.04510634,0.00561659,-0.04261459,0.03070518,-0.02977162,0.07080678,0.04609596,0.09202273,0.03215697,-0.00580525,0.05145446,0.01557327,-0.00555976,0.04587333,0.01788984,-0.0460449,-0.0264971,0.03648776,-0.00422012,0.02762448,0.02508163,0.04967284,-0.04949433,0.13673292,-0.00220598,0.04664402,0.04183811,0.09186062,0.04997849,0.01592914,-0.0921039,-0.01537295,0.01053781,-0.09341342,-0.01484464,-0.05711601,-0.01979828,-0.02153363,0.02520553,-0.02079004,-0.09566443,0.00198724,-0.00434548,-0.05118144,0.00971173,-0.06943221,-0.01873169,0.00373721,0.01105658,0.00839403,0.03107193,0.01109533,-0.04691587,-0.06586776,-0.05318222,-0.09115107,-0.01740965,-0.05233691,0.01959785,0.03767926,-0.01956527,0.00321856,0.00296109,0.01006569,-0.01216492,-0.01417611,0.02115638,-0.04838825,0.10343888,0.01289528,0.01191946,-0.0234834,-0.00923147,-0.04203276,-0.05557446,0.0011104,-0.0619122,0.04733567,0.0301912,-0.00201549,0.02330503,0.04666502,0.05300015,0.01667652,0.02174654,0.03222542,-0.00334035,-0.02205839,-0.00720286,-0.01738801,0.0108625,0.0854018,-0.02215483,-0.28519365,0.03456677,-0.02087335,0.01842937,-0.01254143,0.00541454,0.0019885,-0.04412979,0.02012109,0.05112088,0.00984819,0.05898128,-0.00792387,-0.03444966,-0.0296317,0.00921195,-0.02458441,-0.02485422,0.08313946,-0.02524058,-0.02249494,-0.01467193,0.25453326,0.02073001,0.03013309,0.01040081,-0.00466373,-0.02459988,-0.00468417,0.01431483,0.00485461,-0.00792278,0.09031138,0.04249627,0.02684843,0.07382318,-0.01344078,0.06129456,-0.00255118,0.04783668,-0.03732093,0.04371677,-0.07358462,0.01002842,0.1139937,0.00312911,-0.02052757,-0.07274657,-0.00207738,0.0844605,-0.02442186,-0.00667427,-0.01113071,-0.00140967,-0.01626301,0.047754,0.02798811,-0.03549078,0.02332922,0.04898556,0.06464125,0.05993734,0.04144564,0.04927636,0.04353223],"last_embed":{"hash":"luuw33","tokens":300}}},"text":null,"length":0,"last_read":{"hash":"luuw33","at":1753495701257},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{5}","lines":[144,150],"size":341,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12712841,0.02885843,-0.00816241,-0.01800725,-0.04929696,0.01456132,0.00149209,0.02565824,0.04201026,-0.03771522,0.03452235,-0.08273632,0.04955655,0.0213667,0.06984193,-0.0536325,0.02369662,-0.04122765,-0.08808643,-0.03326387,0.05419674,-0.04014619,-0.00194164,-0.10402234,0.04912277,-0.00520226,0.01339478,-0.04603017,0.00426759,-0.20629486,-0.00598477,0.06337053,0.0109267,-0.03720746,-0.05113193,0.03944511,-0.0194233,0.03716706,-0.06297366,0.01539743,0.0151951,0.00872155,0.00788132,-0.03434397,-0.01548954,-0.04065892,-0.05109359,-0.01760353,0.04333286,0.02152213,-0.02637346,-0.03068082,0.00260523,0.04818118,0.02118818,0.06135711,0.03997289,0.0764962,0.02257816,0.03565656,0.02804132,0.04019615,-0.19244994,0.05045889,-0.0353022,-0.04298237,-0.00232271,-0.00794532,0.01316084,0.06413867,-0.05599432,0.02200314,-0.02199046,0.05841444,0.02961849,-0.0212096,-0.00071664,-0.04675195,-0.03650769,0.02404606,-0.01882628,-0.00043824,-0.05508678,0.03035213,0.00000929,0.06517838,-0.00687323,0.0051132,0.05318754,-0.05600372,-0.03115162,-0.04286341,-0.01615099,0.07828716,-0.02726945,0.05259601,0.04203901,-0.00782383,-0.08374207,0.11327699,-0.00736354,0.05465939,0.04281361,-0.04480119,0.01935683,0.00085418,-0.00994825,-0.05217196,-0.04747973,-0.03912008,0.03208992,-0.00906896,-0.00904428,-0.08149762,-0.0581587,-0.00320906,0.04709966,0.02061158,-0.03920199,-0.00738162,0.03051466,0.01055858,0.04794379,0.00664209,0.0310839,-0.02535333,0.0364759,0.05235158,0.00958016,0.02895854,0.05569302,0.04655958,-0.06338947,-0.04856504,-0.01359547,-0.07862284,-0.03845784,-0.04765963,0.04721956,-0.04389506,-0.04394927,-0.04347672,0.03180339,-0.09535208,-0.02152295,0.08647775,0.01264875,0.01198702,0.04650286,-0.00285219,0.0127692,0.06503691,0.03083386,-0.02117862,0.05058061,0.02466145,0.09111714,0.07924945,0.00946676,-0.00206172,-0.01884944,-0.02986019,-0.0408917,0.16635191,-0.01741738,-0.06823714,-0.02237204,0.06721009,0.00489,-0.05206349,0.01616508,-0.00647441,-0.00352337,0.01318625,0.09524495,-0.03518438,-0.0338164,0.01011182,-0.02951678,0.0047991,0.00727102,-0.01422462,-0.09454171,0.03767006,-0.01466798,-0.10471589,0.0267259,-0.04037508,0.01642057,0.01654315,-0.05228717,0.04469803,0.07208977,0.0694332,0.000269,-0.04511982,-0.03799644,-0.03705805,0.04210865,-0.02431756,0.0660941,0.03011937,-0.00916478,-0.0061033,0.00228452,-0.0210562,0.00859042,0.00622279,0.03098655,0.05608389,0.00520132,0.04780087,0.02380542,0.01474307,-0.02405806,0.02852333,-0.02385677,0.0300276,0.02261336,0.03458124,0.01867332,-0.00668605,-0.06415347,-0.22810201,0.04272071,-0.04226248,-0.02630218,0.0008485,-0.06542597,0.00863507,-0.04304979,0.02694551,0.00933408,0.08759575,0.03705402,0.01069283,0.0408172,0.00095318,-0.01029244,0.01542607,0.00374391,-0.00289481,0.00125742,0.04212821,0.02085988,0.03547576,0.00558754,0.06027585,-0.05759261,0.14773573,0.00483887,0.06463257,0.03056417,0.09581144,0.06869324,0.04064137,-0.09079273,-0.01465145,0.05038092,-0.06842498,-0.03796137,-0.07750528,0.00142542,-0.01628671,0.02131776,-0.0122251,-0.14389226,0.0174495,-0.05491159,-0.07253005,-0.00824715,-0.03278115,-0.01762101,0.0193713,-0.00659114,0.01788379,0.04083682,-0.0122764,-0.05200883,-0.08047834,0.00889897,-0.08257667,-0.04174708,-0.01540141,0.02755427,0.03311612,-0.01049909,-0.03401255,0.00036402,0.00610058,-0.02791629,-0.01478811,0.00199321,-0.02621667,0.12703151,-0.01734718,0.01902152,-0.02137847,-0.01310406,-0.0550004,-0.07083733,0.02188491,-0.03612188,0.02091022,-0.0119715,0.00157053,0.02274928,0.04729139,0.01413482,0.03513603,0.04125943,0.01920992,-0.0301218,-0.03381985,-0.00520123,0.02148261,0.00905491,0.04946901,-0.02458484,-0.28613684,0.03934323,-0.03125086,0.01160178,-0.03736528,0.01477764,0.00763667,-0.06803407,0.01285437,0.05761441,0.01043664,0.03242195,-0.00249481,-0.02516557,-0.0189765,0.05496335,0.01041523,-0.03693312,0.04763303,-0.02949837,0.00109839,-0.00995753,0.26099429,0.02903512,0.00778742,0.02591228,-0.02344516,-0.03231408,0.01716471,0.00289515,-0.00878615,0.00307968,0.05506099,-0.00394655,0.01814744,0.08767965,-0.01356908,0.00782948,-0.02172492,0.05543957,-0.03252587,0.03019937,-0.06160937,0.01544651,0.09263208,-0.00914098,-0.02883248,-0.08090523,-0.01777054,0.08527702,-0.02436105,-0.0071526,0.00168808,-0.01283358,-0.00850675,0.03836219,0.04381472,-0.01635673,0.00480437,0.04201213,0.06070764,0.07286473,0.06921107,0.06273255,0.01385562],"last_embed":{"hash":"23a9b2","tokens":233}}},"text":null,"length":0,"last_read":{"hash":"23a9b2","at":1753495701339},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{6}","lines":[151,156],"size":264,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13425212,0.03304271,-0.03425465,0.00937258,-0.02961772,0.07167264,0.0024893,0.01751104,0.04387222,0.02298072,0.0344564,-0.07635323,0.0545424,0.05794624,0.04090064,-0.02530101,-0.00571623,0.01315221,-0.05828937,-0.05334172,0.08294193,-0.07455896,0.00281094,-0.08216637,0.0546296,-0.00964767,-0.01468484,-0.01117211,0.0203784,-0.20513602,0.02411783,0.00491576,0.02667334,-0.03088387,-0.03004111,0.022228,-0.03857762,0.02188581,-0.06327859,0.0134355,0.04600609,-0.01964691,0.00187653,-0.01162842,-0.0150361,-0.08240607,-0.02107362,-0.0533878,0.03780547,-0.02530666,-0.08424756,-0.02463664,-0.0051332,0.0390659,0.05387623,0.05326901,0.03741435,0.01337479,0.01799163,0.00704558,0.00439506,0.01588342,-0.20579843,0.06047225,-0.02004701,-0.05156994,-0.01474715,-0.01264328,0.01649625,0.04293879,-0.0407777,0.00899817,-0.02732062,0.04651803,0.04034019,-0.01025337,-0.01966243,-0.03568301,-0.03391636,-0.0009871,-0.03164307,-0.01553376,0.01150673,-0.01939882,0.00210143,0.07899936,0.01078458,0.01315599,0.02065019,-0.02722478,-0.03928905,0.0223585,-0.02281543,0.04539008,-0.02511188,0.05055185,0.03408466,0.00628937,-0.09298207,0.13045759,0.01843624,0.02690027,0.01218328,-0.03087847,0.03829425,0.01099632,0.01964709,-0.04486283,-0.02230309,-0.00927987,0.00890268,-0.01491954,-0.01812024,-0.05244971,-0.00625713,0.01199991,0.02131901,0.03270087,-0.0612414,0.01242306,-0.00106362,0.02955128,0.04226049,-0.00011613,0.01915348,-0.00622792,0.01530151,0.05921791,0.03464052,-0.03210252,0.02500945,0.0071601,-0.07809816,-0.02427393,-0.01641022,-0.03242033,0.00435459,-0.06240186,0.02370385,-0.00430125,-0.03375385,-0.00296821,0.0138565,-0.12354606,-0.03841759,0.0938267,-0.01764124,-0.00808409,-0.00436469,-0.0232656,0.036342,0.06865538,0.02542625,-0.04423274,0.03891581,0.02809211,0.06938689,0.11609898,-0.00392567,0.01540031,-0.01740032,-0.02051332,-0.05666674,0.20201978,0.01453451,-0.08693129,-0.02034396,0.02208835,0.00690154,-0.07023668,0.03257184,0.00955783,-0.00418746,0.02673854,0.10112492,-0.03956079,-0.05228842,-0.00876426,-0.03201241,-0.01100057,-0.00139636,0.00578762,-0.0396557,0.02010033,-0.01192868,-0.08916967,0.02884482,-0.00262877,-0.00128037,-0.00114091,-0.04512545,0.01468785,0.08121105,0.08028397,-0.02090628,-0.06143844,-0.02446765,-0.0611945,0.0555018,-0.04649263,0.06774836,0.01010806,-0.03972933,-0.01302249,-0.04929299,-0.02971774,-0.00101138,0.00601081,0.02699255,0.07669989,-0.02323147,0.04115615,0.03027784,0.01907651,-0.04713784,0.03131395,0.01297072,-0.0012125,0.02818012,0.00945468,0.03461433,0.02687422,-0.08911767,-0.20820411,0.01459034,-0.03679106,-0.04117268,0.01617428,-0.06306565,0.03482552,-0.0404919,0.0838882,0.01814353,0.07388005,0.00026746,0.00920292,-0.00402845,-0.0030638,-0.01051585,0.00789004,-0.02401486,-0.02940962,-0.00009821,0.04486761,0.06190725,0.0218643,-0.01122216,0.0459028,-0.03581804,0.13264447,0.04037376,0.05948731,0.03569555,0.07844786,0.04042842,0.02639241,-0.10272089,-0.01446401,0.03966492,-0.04234998,-0.00696895,-0.07872932,-0.04060909,-0.01300503,0.04474878,-0.02551885,-0.12291531,-0.00115926,-0.01229842,-0.03297222,0.01639532,-0.0308808,-0.02566738,0.00485307,-0.01958587,-0.0030873,0.01346673,0.01693091,-0.02659649,-0.07008353,-0.03272156,-0.03269509,-0.01682623,-0.03132388,-0.00120657,0.03997451,0.01767665,-0.01447485,-0.02759264,0.01828677,-0.00762759,-0.01369649,0.00491284,-0.03010907,0.10914408,0.02635911,0.02900464,0.01386457,0.01538565,-0.02930597,-0.07690925,0.00510707,-0.05385243,0.0164566,0.03352034,0.01611356,0.02270699,0.0941859,0.01129481,0.00990307,0.0221975,0.0320812,-0.03999429,-0.02618016,-0.0170107,0.01049573,0.02656667,0.09727903,0.00458601,-0.30568957,0.06442703,-0.04932567,0.01679867,-0.03274038,0.03449448,0.01851691,-0.03217171,0.01303458,0.03158065,0.00545922,0.047264,0.01900431,-0.0213205,-0.06629653,0.00225945,0.00369993,-0.0229466,0.09685557,-0.03569613,-0.0258367,0.00948839,0.25973743,0.01638358,0.02165426,-0.01581997,-0.01206698,-0.01759326,0.01456347,0.00930872,-0.00667134,0.01603255,0.05157071,0.01010496,0.06291212,0.0847698,-0.01511268,0.01303638,-0.00118802,0.02571431,-0.0547903,0.05534562,-0.04655639,0.0076191,0.11915895,0.02375048,0.00743308,-0.11752012,-0.00637852,0.09383743,-0.00711161,-0.04933479,-0.00977817,0.00300586,-0.03201836,0.02404311,0.05731616,-0.05812786,-0.00141428,0.02988684,0.06098783,0.01564909,0.05212877,0.02747832,-0.01008049],"last_embed":{"hash":"1qst1n5","tokens":183}}},"text":null,"length":0,"last_read":{"hash":"1qst1n5","at":1753495701408},"key":"notes/saliu/基於 Ion Saliu 彩票數學理論的 MDIEditor Lotto WE 軟體建立 Julia 專案規範，包括需求、設計和任務.md###三、任務 (Tasks)#{7}","lines":[157,161],"size":236,"outlinks":[],"class_name":"SmartBlock"},
