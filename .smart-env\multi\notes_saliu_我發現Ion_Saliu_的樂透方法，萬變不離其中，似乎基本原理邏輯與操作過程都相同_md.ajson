
"smart_sources:notes/saliu/我發現<PERSON> 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md": {"path":"notes/saliu/我發現Ion <PERSON> 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1215984,-0.01967336,-0.00677425,0.00592336,0.01076537,0.04294541,0.08386051,0.00511499,0.10533585,-0.00447002,-0.00798771,-0.08360028,0.0090661,0.03104369,0.00910094,-0.02709911,0.02592572,-0.04070034,-0.04497629,-0.03298907,0.09547916,-0.05746103,-0.01123286,-0.08763464,0.07348801,-0.01379905,0.00236663,-0.06523435,0.00042444,-0.16444995,-0.03545222,0.04376058,-0.02313019,-0.02390074,-0.06775465,-0.00327253,-0.00342381,0.01383641,-0.04198831,0.01805851,0.03372575,-0.00121603,0.05931412,-0.04408792,-0.01382804,-0.01352703,-0.02773159,-0.041474,0.04120464,0.01827632,-0.0361736,-0.00734497,0.00803552,0.002535,0.0283742,0.01451872,0.07642145,0.04755138,0.02672887,0.05102992,-0.01998386,-0.00198041,-0.2283802,0.035817,-0.00523147,-0.04122247,0.00375051,-0.00306367,0.01348302,0.04210868,-0.04232449,0.03565339,-0.0234598,0.03828653,0.09038509,-0.06910691,0.01157088,-0.04156147,-0.06301277,0.01222411,-0.00831288,0.04332636,-0.0193701,-0.03186022,0.03067405,0.06089348,-0.01388325,-0.04563954,0.00593748,-0.05419814,0.01123232,-0.03701868,-0.01722828,0.02647482,-0.02202994,0.00737883,0.05046837,0.00582198,-0.07847633,0.08185016,0.03837149,0.01369993,-0.03821569,-0.02269691,0.06860674,-0.03337944,0.02315483,-0.01374771,-0.00624215,-0.00744598,-0.00411433,0.01646828,0.03633783,-0.04291965,-0.0373028,0.05296878,0.03634083,-0.00460451,-0.00496274,-0.01496295,0.00706187,0.0221933,0.00098918,-0.02220098,0.02192138,-0.04138987,-0.00771393,0.04538837,0.03613793,0.01874218,0.06158435,-0.04486524,-0.03240991,-0.07054724,-0.05742903,-0.00149332,-0.03539919,-0.01449416,0.03300964,0.01315871,0.00475289,-0.07526682,0.03160782,-0.1187769,-0.08995665,0.06988732,-0.00811113,-0.03283568,-0.01317397,-0.02453202,-0.00015611,0.0330828,0.00531102,-0.03190295,0.04686916,-0.03171962,0.07907904,0.15148525,-0.01255106,-0.0103348,-0.02274904,-0.06692641,-0.03993388,0.14587376,0.01762918,-0.01638485,-0.03409081,0.00784997,-0.01224604,-0.08060696,0.02257521,-0.03758736,-0.0466417,0.02356562,0.08251057,-0.00100044,0.06875636,-0.05467117,-0.07365208,0.03372236,-0.01749068,-0.04309649,-0.04337762,0.01149115,-0.03104828,-0.09893796,-0.01197012,-0.06790696,0.03796951,-0.00711593,-0.06919276,0.02536098,-0.01496609,0.03468747,-0.0816476,-0.04453021,-0.02269438,-0.06147591,-0.00029878,-0.00180651,0.05388727,-0.01467885,-0.01302851,-0.04459332,-0.01607026,0.04616327,-0.00758094,-0.00942422,0.00063551,0.07133997,-0.00769248,0.03954766,-0.00612365,0.00585861,0.02308826,0.03394184,-0.02344435,0.03056452,-0.02523913,0.04696875,0.04555587,0.01159749,-0.09452166,-0.23235509,-0.02888394,-0.04193118,-0.00661833,0.03631472,-0.02194444,0.05853793,-0.014011,0.08384302,0.07407915,0.05743355,0.03008115,-0.06864505,0.03169521,0.00751513,0.00503744,0.01454831,-0.0301305,-0.02418206,0.03762103,0.00156475,0.04963231,-0.02434952,0.03191044,0.04633402,-0.01109144,0.15141422,-0.02192174,0.02179304,0.02504926,0.08222549,0.03452694,-0.00729226,-0.06989338,0.04154193,0.02008431,-0.10369256,0.03435634,-0.04351629,-0.04540296,-0.0284702,0.07867879,-0.00361281,-0.04054889,-0.05195354,-0.04261246,-0.02002885,-0.03735516,-0.03360067,0.03163963,0.07522283,0.01355419,0.01935887,0.03821367,0.03612534,-0.00833347,-0.05253199,-0.02280672,-0.03870973,-0.02779889,-0.03545999,0.00146418,0.07910854,0.01258861,-0.00832948,0.00763569,0.03203854,0.0438986,-0.00697048,0.0608186,-0.08375026,0.13400686,0.00590466,-0.02447415,0.07314631,0.01893879,-0.0332573,-0.05030847,-0.00208657,0.01336526,0.06453458,-0.00524822,0.03397923,0.01245922,0.03297516,0.02776794,0.08005686,0.02465923,-0.00699564,-0.01450803,-0.03656412,0.00013635,-0.0444956,-0.02596758,0.05493277,0.00980075,-0.31611943,-0.00562431,0.0165114,-0.00293839,0.01324284,-0.00367982,0.03033807,-0.0317169,-0.05300061,0.05114128,0.00055534,0.06981086,0.02332216,-0.03171706,-0.03674227,-0.00709311,0.01922988,-0.02210583,0.04801239,0.00207868,0.00333626,0.00870231,0.23610052,0.00625477,0.03604537,-0.02830079,0.02604371,0.00306163,0.02180214,0.02529315,-0.01981574,0.00513222,0.07751938,0.03306544,0.0327016,0.07086171,-0.02281116,0.03275939,0.02022453,0.00782197,-0.02609467,0.03085916,-0.05630914,0.02700893,0.09925649,0.01567449,-0.03184477,-0.04735599,0.03431099,0.02333479,-0.05008004,-0.0204914,0.02177792,0.02853197,-0.00630043,-0.00920986,-0.00883238,-0.01531607,0.02814454,0.01748809,0.04058007,0.08185789,0.00372042,0.06710274,0.0876084],"last_embed":{"hash":"1pkdh5d","tokens":399}}},"last_read":{"hash":"1pkdh5d","at":1753495709201},"class_name":"SmartSource","last_import":{"mtime":1753491724968,"size":3832,"at":1753495676981,"hash":"1pkdh5d"},"blocks":{"#":[1,18],"##{1}":[5,6],"##{2}":[7,8],"##{3}":[9,18]},"outlinks":[]},"smart_blocks:notes/saliu/我發現Ion Saliu 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12232158,-0.01960297,-0.00570056,0.00866377,0.00789709,0.04436199,0.08647728,0.00464229,0.10743364,-0.00504947,-0.00968933,-0.08362296,0.0107049,0.03131265,0.00854573,-0.02642647,0.02443177,-0.03875094,-0.04549203,-0.03457452,0.0967145,-0.05812624,-0.00770083,-0.08672093,0.07280349,-0.01389947,0.00431642,-0.06143272,0.00243918,-0.16376835,-0.03553352,0.04122393,-0.02144296,-0.02449355,-0.06767333,-0.00188462,-0.00149073,0.01308999,-0.04107782,0.01794829,0.03362548,0.00098938,0.06095763,-0.04502142,-0.01578974,-0.0127009,-0.02706163,-0.04067931,0.03883598,0.02034793,-0.03509701,-0.00882651,0.00907966,0.00545024,0.02673078,0.01658626,0.07542509,0.04708058,0.02472568,0.05166066,-0.02138961,-0.00449699,-0.22655649,0.0364299,-0.00648436,-0.04389978,0.00200896,-0.0026413,0.01341127,0.04184784,-0.04102399,0.03608293,-0.02174489,0.03935208,0.09031929,-0.06906576,0.01034446,-0.04167432,-0.06431466,0.01228024,-0.00685403,0.04215249,-0.01650531,-0.03194547,0.0324055,0.061516,-0.01403944,-0.04568689,0.00553784,-0.05328438,0.00923879,-0.03718212,-0.02078456,0.02918597,-0.01911636,0.00774685,0.04758608,0.00675941,-0.07976268,0.08119623,0.03642836,0.00993662,-0.04040292,-0.02359551,0.06881434,-0.03198583,0.02335885,-0.01342176,-0.00612289,-0.00951355,-0.00430037,0.01248019,0.03596513,-0.04335386,-0.03963834,0.05434129,0.03650843,-0.00541521,-0.005328,-0.01444294,0.00650909,0.02378555,0.00300056,-0.02245448,0.01792877,-0.0426631,-0.00674889,0.04694254,0.03524828,0.02074737,0.06288084,-0.04785819,-0.03289584,-0.07260989,-0.05669748,-0.00247975,-0.03707534,-0.01539236,0.03264805,0.01301879,0.00658608,-0.07195991,0.0339151,-0.12078112,-0.09244906,0.06844173,-0.0084662,-0.03343277,-0.01256311,-0.02371887,-0.00224671,0.03234466,0.00745839,-0.03191881,0.04594615,-0.0301233,0.08018672,0.15241455,-0.01416458,-0.01052723,-0.02357287,-0.06505673,-0.03796674,0.14564122,0.01767417,-0.01391445,-0.03512597,0.00798294,-0.0134102,-0.08006801,0.02209128,-0.03554758,-0.04458364,0.0273329,0.0844338,-0.00166837,0.06798304,-0.05454807,-0.0761148,0.03554287,-0.01665492,-0.04501117,-0.04384077,0.01112274,-0.02974814,-0.09735117,-0.01006223,-0.06803188,0.03829451,-0.00676539,-0.06855036,0.0255237,-0.01086832,0.03471791,-0.08186283,-0.04691438,-0.0227055,-0.06092681,-0.00232229,-0.00278343,0.05457046,-0.016247,-0.01516753,-0.04295807,-0.0142394,0.04534209,-0.00608602,-0.00580284,-0.00204639,0.07230695,-0.00595589,0.03968713,-0.00529414,0.00509789,0.02473026,0.03536862,-0.02419556,0.02985802,-0.02429263,0.04608914,0.04463017,0.01026673,-0.09389204,-0.23302442,-0.02787637,-0.04131595,-0.00475109,0.03640114,-0.02332062,0.0583168,-0.01301988,0.08657026,0.07115579,0.05737784,0.0317748,-0.06833423,0.03097047,0.00568517,0.00689142,0.01594857,-0.02844523,-0.02288272,0.0368023,0.00059489,0.05051056,-0.02340021,0.03250773,0.04679607,-0.01391664,0.14823408,-0.02353543,0.02154661,0.02667279,0.08134224,0.03587237,-0.00531366,-0.06781901,0.04241241,0.02407676,-0.10547476,0.03653825,-0.043433,-0.04913447,-0.02984819,0.08151647,-0.00345663,-0.04094026,-0.04845021,-0.04455118,-0.01919186,-0.03602489,-0.03426717,0.02565938,0.07688838,0.01415745,0.01899276,0.03935055,0.03656248,-0.01049694,-0.05398002,-0.0210617,-0.03888733,-0.03031908,-0.03404336,0.00146672,0.07631988,0.01224927,-0.01152507,0.00846955,0.0332863,0.04473119,-0.00541947,0.05591521,-0.08470019,0.13472196,0.00880205,-0.02469426,0.07275829,0.01724445,-0.03429328,-0.05257039,0.00050811,0.01304922,0.06328032,-0.00381862,0.03323198,0.01395813,0.03176855,0.0280548,0.07761104,0.02522912,-0.00986754,-0.0126259,-0.03551937,0.00113488,-0.04441007,-0.02559632,0.05514243,0.00853152,-0.31718975,-0.00474093,0.01319216,-0.00338832,0.01308927,-0.00222829,0.0285571,-0.03379402,-0.05464032,0.05311911,-0.00300694,0.06924371,0.02430896,-0.03287113,-0.03481372,-0.00449458,0.01814717,-0.02307452,0.04365744,0.00299181,0.0041076,0.00965015,0.2359755,0.00873309,0.03648671,-0.03045892,0.02632092,0.00151966,0.02003844,0.02430542,-0.01846715,0.00469636,0.07663324,0.03192411,0.03172604,0.07294187,-0.02117692,0.03178628,0.01722953,0.005708,-0.02428674,0.03101296,-0.05355236,0.02759329,0.10026091,0.01426968,-0.03122445,-0.04562215,0.03306018,0.02274227,-0.05008241,-0.01980141,0.02020889,0.03009334,-0.00474428,-0.00689226,-0.01073228,-0.01718792,0.02938084,0.01486441,0.038731,0.08184263,0.00407316,0.06508162,0.08842856],"last_embed":{"hash":"1pkdh5d","tokens":399}}},"text":null,"length":0,"last_read":{"hash":"1pkdh5d","at":1753495708987},"key":"notes/saliu/我發現Ion Saliu 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md#","lines":[1,18],"size":1606,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/我發現Ion Saliu 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md##{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10660632,-0.02116287,0.00453328,-0.00859013,0.01065185,0.06341531,0.09284971,0.01812951,0.10957281,-0.00221237,-0.00149297,-0.07515416,0.01310691,0.02940271,-0.00483664,-0.01593965,0.02211544,-0.0442594,-0.04575,-0.03381201,0.11397193,-0.06998095,-0.01312724,-0.10166361,0.08451438,-0.01714875,0.01361925,-0.06622628,-0.00182928,-0.1556755,-0.03161553,0.03480906,-0.01036184,-0.02445096,-0.06930906,-0.00182179,-0.00092959,0.02753555,-0.0371632,0.00786216,0.03477836,-0.01983256,0.06150623,-0.03077661,-0.01219377,-0.03100738,-0.01903159,-0.03896996,0.0301686,0.00176301,-0.03034946,0.0076187,0.00439002,-0.01364456,0.01575696,0.01205247,0.06746665,0.04975208,0.04296705,0.05657141,-0.02382513,0.00892682,-0.22438917,0.03460884,-0.01612595,-0.03020841,-0.00261453,-0.00586725,0.02852491,0.05467715,-0.05091473,0.04358835,-0.00701547,0.04088642,0.08796408,-0.07452579,0.01475734,-0.05403783,-0.08866858,0.02462768,0.00016178,0.03414912,-0.00562745,-0.03682182,0.03387543,0.06017419,-0.01248938,-0.0496506,-0.00011199,-0.02716093,0.00803576,-0.0142064,-0.01971189,0.02068738,-0.04315391,0.00448682,0.05687772,-0.00530915,-0.07553785,0.08348871,0.03597685,0.0025921,-0.05133205,-0.03004508,0.07914747,-0.03858689,0.02592732,0.00113072,-0.01441592,-0.01927787,-0.01425254,0.01817462,0.03954221,-0.03979032,-0.03354793,0.04916824,0.03953949,0.00558799,-0.00013284,-0.01283142,0.01221777,0.02695193,0.01144311,-0.0263379,0.02422083,-0.03156476,-0.0071847,0.04942755,0.03540054,0.01455126,0.04954188,-0.05517964,-0.03985017,-0.06598368,-0.04039995,0.01788819,-0.03268496,0.00082397,0.03494007,0.02826258,0.00108897,-0.0577805,0.02240803,-0.1152263,-0.0873854,0.06645092,-0.01118245,-0.03826449,-0.01302121,-0.02003492,0.0058277,0.04057721,0.00789687,-0.02368035,0.04825974,-0.03690317,0.09025242,0.15949135,-0.00673089,-0.02908452,-0.04400173,-0.0627607,-0.02933947,0.14479561,0.01371313,-0.01426661,-0.03753746,0.01462602,-0.01503908,-0.08902843,0.00592561,-0.05097839,-0.05706873,0.0296487,0.07638598,0.00196348,0.06982446,-0.03185745,-0.06464578,0.02496305,-0.00158788,-0.0525057,-0.02217842,0.01010294,-0.03374184,-0.10355889,-0.02528755,-0.0760617,0.03448933,-0.01508011,-0.08387024,0.02125579,-0.01515347,0.03601645,-0.0822758,-0.04487475,-0.02399767,-0.04461592,0.00474671,-0.02125183,0.05542766,-0.01546125,-0.00074825,-0.03232235,-0.00538573,0.05409892,-0.00502419,-0.00269765,0.00234922,0.06657077,-0.01083849,0.03299313,0.00224056,0.00131719,0.03089852,0.0428951,-0.01721993,0.04024286,-0.02922222,0.02405195,0.05362944,0.00279335,-0.08824243,-0.22797608,-0.03777813,-0.03166491,-0.02124799,0.03446393,-0.01144455,0.05708307,-0.01204263,0.0903253,0.08008368,0.03879889,0.00712016,-0.0817755,0.0371045,-0.00088514,0.00246781,0.00778974,-0.04384502,-0.02237948,0.02482114,0.00643563,0.06173934,-0.05321293,0.03156497,0.04668484,-0.0140623,0.14911333,-0.01164448,0.03737717,0.00571584,0.0936048,0.02353456,-0.0035277,-0.04793787,0.04026394,0.01899358,-0.09955482,0.03149512,-0.05031437,-0.04885316,-0.0127257,0.07268548,-0.01367223,-0.04730317,-0.06410755,-0.03322068,-0.02834933,-0.0221452,-0.02074234,0.03893156,0.06718195,0.01502588,0.0253112,0.0415118,0.04374878,-0.00696835,-0.05244954,-0.00964315,-0.04791005,-0.00491481,-0.03033787,0.00768117,0.06586491,0.01000512,0.0049531,0.01124141,0.02542529,0.02941676,0.01124765,0.04147139,-0.08331409,0.13318497,0.00516274,-0.03058689,0.06822082,0.02604157,-0.04366615,-0.06130672,-0.01566213,0.00349451,0.09032612,0.0140018,0.02874032,0.00963068,0.02619496,0.01882439,0.08482824,0.0425561,0.00063428,-0.00950028,-0.04740731,-0.00496193,-0.04978774,-0.03982461,0.04671771,-0.00695577,-0.31315678,0.00029838,0.00884162,0.00940055,0.01823541,-0.00020029,0.0242858,-0.03009757,-0.06431542,0.06113817,-0.01475495,0.05937397,0.01760852,-0.03408314,-0.03600406,-0.00780445,0.00798301,-0.01153484,0.03855277,-0.00952563,0.0169887,0.0116687,0.23292437,0.01332705,0.03654118,-0.02535165,0.03278368,0.00690339,0.00525436,0.01672205,-0.01395549,0.00122749,0.07112204,0.02252493,0.0271581,0.06176637,-0.02056896,0.04072165,0.01920005,0.001681,-0.0104959,0.03077322,-0.05435821,0.01663366,0.09535114,0.00074587,-0.02443365,-0.03567249,0.03377133,0.02524561,-0.06055642,-0.00886936,0.01442007,-0.00369041,-0.00459596,-0.00905094,-0.00599658,-0.01824418,0.0173768,0.02155406,0.02466603,0.08878417,0.01146716,0.05796849,0.06862801],"last_embed":{"hash":"w760u5","tokens":243}}},"text":null,"length":0,"last_read":{"hash":"w760u5","at":1753495709123},"key":"notes/saliu/我發現Ion Saliu 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md##{1}","lines":[5,6],"size":305,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/我發現Ion Saliu 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md##{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11190978,0.01255206,-0.02180626,0.03158873,0.02937618,-0.04897286,0.00647995,-0.00598359,0.06236467,-0.02157126,0.01178278,-0.07675275,0.03323888,-0.00758481,0.03091764,-0.01308674,0.01141678,-0.00016429,-0.08261258,-0.0428673,0.07801839,-0.03066506,0.00865213,-0.0475826,0.05689383,-0.00988689,0.00516428,-0.02231378,0.01599701,-0.16946195,-0.05852333,0.05503214,0.03355695,-0.00480009,-0.03718301,0.01551581,0.0008626,-0.03644116,-0.03724462,-0.00145915,0.05448204,0.0199561,0.06109777,-0.01778306,-0.01089971,-0.02880591,-0.03248963,-0.02253787,0.03041584,-0.01965127,-0.02124489,-0.02334055,0.00151117,0.01496653,0.01053013,0.02245287,0.08039992,0.11451843,0.01256741,0.01739304,-0.01013936,0.03926162,-0.21386483,0.02634413,0.00066171,-0.05825809,-0.01156888,-0.05853158,0.0438056,-0.01201247,-0.05753256,0.00744891,-0.01504262,0.04182544,0.03429678,-0.05720089,0.03580927,-0.01573341,0.00835878,-0.01281224,-0.00419332,0.02604918,-0.01796131,0.01662969,0.04034662,0.03419815,-0.00430502,-0.01528039,-0.00144173,-0.07506401,-0.02699949,-0.04497548,0.01017804,0.03695038,-0.02689078,0.00793376,0.05429331,0.04663809,-0.10537963,0.12373312,-0.00096586,0.01098535,-0.00429393,-0.05406752,0.06733816,-0.0096491,-0.00255624,-0.0322691,-0.04529545,0.00115222,0.01287259,0.02135052,-0.00338169,-0.06558428,-0.01707425,0.01404359,0.00385134,-0.02990095,-0.01534312,-0.03491216,0.00222942,0.03993455,0.04846297,-0.04369354,0.01863207,-0.05862814,0.04414909,0.02905945,0.03180484,0.03647792,0.0729116,-0.0168139,-0.04437517,-0.05803502,-0.02425422,-0.02353404,-0.03990937,-0.05463523,-0.00405542,-0.00272382,0.01388804,-0.04399334,0.04949791,-0.11620614,-0.03841678,0.06855393,-0.01983664,-0.01318963,-0.02068187,-0.02609625,-0.01106304,0.02971082,0.04841777,-0.02311237,0.06188381,-0.0060144,0.10024629,0.14309467,-0.04816071,0.00843427,0.02490113,-0.03173297,-0.03202191,0.13579376,-0.00455969,-0.01935113,0.00710213,0.04414833,-0.00072148,-0.04999,-0.00211265,0.01684633,-0.01986678,-0.02303789,0.09297502,-0.04366253,0.04068524,-0.05507007,-0.04830223,0.00065983,-0.01441445,-0.02860186,-0.07664382,0.04316323,-0.01372842,-0.09180816,0.05277183,0.02930469,0.05456086,0.0235692,-0.06894182,0.09095878,0.00224253,0.02973642,-0.08934325,0.00386964,-0.03440427,-0.0664791,0.01344829,-0.03208144,0.07982652,-0.02059135,-0.04140011,-0.03733762,0.00414004,0.03206058,-0.00373594,-0.01251999,0.00435952,0.04315571,-0.0346341,0.08538008,0.03680155,0.03221696,-0.03725762,-0.00922684,-0.04588291,0.07206965,-0.02912589,0.03730121,0.03537715,0.00129815,-0.10942706,-0.22625951,0.01387284,-0.01495895,-0.0626168,0.03496245,-0.02107089,0.02266283,0.00627331,0.08509911,0.01767461,0.07999264,0.04416396,-0.00804352,-0.01219839,0.00847494,-0.0128371,0.05486345,0.00279692,-0.00953118,0.02116733,0.01483041,-0.00092593,0.04352862,0.01866951,0.0237531,-0.04988918,0.10647925,0.00033079,0.01609282,0.00056771,0.04776698,-0.02342175,0.01753939,-0.12841506,0.01454224,0.04248507,-0.08767154,0.04161642,-0.02922598,-0.07105641,-0.0150905,0.04889296,-0.03190693,-0.0541915,0.02711377,-0.04505927,0.00292765,-0.04332697,-0.01402962,0.00832038,0.05642485,0.02816466,-0.0296768,0.08828176,0.00909065,-0.05970224,-0.08154319,0.00177187,-0.03262991,-0.03866163,-0.0217839,-0.02911166,0.06527126,-0.00964332,-0.00041772,0.01208822,0.00122971,0.00356055,-0.00919548,0.03966642,-0.09759212,0.15298074,0.01295195,0.0141619,0.05372039,0.00184783,-0.00231081,-0.07786902,0.0067714,0.01390118,0.04700689,-0.01252929,0.05982605,0.02221983,0.05777789,0.03175941,0.03283514,0.00202932,-0.03117001,-0.0317658,0.00620494,-0.01979499,-0.0405344,-0.03662468,0.03707425,0.03818665,-0.30234066,-0.0103048,-0.0209181,-0.0228706,-0.02308624,0.01663912,0.05244759,-0.05724193,-0.05003329,0.03492854,-0.01351202,0.05381573,0.03015245,-0.06086641,-0.01631679,-0.01873752,0.06729659,-0.00366797,0.0228385,-0.00115474,-0.03049629,0.03551224,0.23420922,0.03029995,0.00145826,0.03712147,-0.01163397,-0.03955388,0.02802428,0.03947814,-0.01914073,0.04854278,0.0552893,0.04490853,0.05174765,0.0898148,-0.04509023,-0.04431735,-0.01129268,0.02536079,-0.00468495,0.02826498,-0.08166587,0.02882432,0.10997362,0.02760504,-0.01423259,-0.05545789,-0.00608328,0.01690472,-0.03279703,-0.02736477,0.02469482,0.04278896,0.01123598,0.02078371,0.00265994,-0.02631165,0.0453124,-0.00654163,0.06937391,0.0413497,0.01682107,0.07256099,0.09692924],"last_embed":{"hash":"frufud","tokens":450}}},"text":null,"length":0,"last_read":{"hash":"frufud","at":1753495709201},"key":"notes/saliu/我發現Ion Saliu 的樂透方法，萬變不離其中，似乎基本原理邏輯與操作過程都相同.md##{3}","lines":[9,18],"size":981,"outlinks":[],"class_name":"SmartBlock"},
