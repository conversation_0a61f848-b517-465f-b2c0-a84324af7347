
"smart_sources:notes/saliu/Powerball, Mega Millions Strategies, Systems.md": {"path":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"ij6ili","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363606862,"size":18205,"at":1753423416500,"hash":"ij6ili"},"blocks":{"#---frontmatter---":[1,6],"#Powerball, Mega Millions: Strategies, Systems":[8,185],"#Powerball, Mega Millions: Strategies, Systems#{1}":[10,15],"#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>":[16,125],"#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>#{1}":[18,125],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>":[126,148],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{1}":[128,139],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{2}":[140,140],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{3}":[141,141],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{4}":[142,142],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{5}":[143,143],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{6}":[144,144],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{7}":[145,146],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{8}":[147,148],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)":[149,185],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{1}":[151,152],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{2}":[153,153],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{3}":[154,154],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{4}":[155,155],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{5}":[156,156],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{6}":[157,157],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{7}":[158,158],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{8}":[159,159],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{9}":[160,160],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{10}":[161,161],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{11}":[162,162],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{12}":[163,163],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{13}":[164,164],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{14}":[165,165],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{15}":[166,166],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{16}":[167,167],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{17}":[168,168],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{18}":[169,169],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{19}":[170,170],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{20}":[171,171],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{21}":[172,172],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{22}":[173,173],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{23}":[174,175],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{24}":[176,185]},"outlinks":[{"title":"Lottery software creates skip systems for lotto, including Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":24},{"title":"The lottery software function creates skip systems for Powerball, Mega Millions, CA Super Lotto.","target":"https://saliu.com/ScreenImgs/powerball-system-skips.gif","line":56},{"title":"Win Powerball with lottery strategy and systems based on the SKIPS or gaps of lotto numbers.","target":"https://saliu.com/images/lottery-software.gif","line":76},{"title":"Generate first the skip report, then create the Powerball strategy system.","target":"https://saliu.com/ScreenImgs/powerball-skips.gif","line":88},{"title":"The lottery software calculates range of analysis for winning Powerball strategy, systems.","target":"https://saliu.com/images/lottery-software.gif","line":94},{"title":"Powerball Strategy and winning powerfall systems, software.","target":"https://saliu.com/images/lottery-software.gif","line":124},{"title":"The winning combinations of the Powerball system for 55 draws.","target":"https://saliu.com/ScreenImgs/powerball-55.gif","line":128},{"title":"The winning combinations of the Powerball system for 28 drawings.","target":"https://saliu.com/ScreenImgs/powerball-28.gif","line":130},{"title":"The winning combinations of the Powerball system for 42 past draws.","target":"https://saliu.com/ScreenImgs/powerball-42.gif","line":132},{"title":"_**The best online Powerball odds calculator: Lottery, lotto, Powerball, combination, random number generator**_","target":"https://saliu.com/calculator_generator.html","line":140},{"title":"_**Combinatorial software to calculate, generate permutations, combinations**_","target":"https://saliu.com/permutations.html","line":141},{"title":"_**combinations generator for lotto, Keno, Powerball**_","target":"https://saliu.com/combinations.html","line":142},{"title":"_**Median, Gauss, Bell, Curve: Random Number, Combination Generator**_","target":"https://saliu.com/median_bell.html","line":143},{"title":"_**Results file, drawings, winning numbers in Powerball**_","target":"https://saliu.com/powerball_results.html","line":144},{"title":"_**Powerball wheels**_","target":"https://saliu.com/powerball_wheels.html","line":145},{"title":"Powerball software generates systems, winning combinations, for Mega Millions, Euromillions too.","target":"https://saliu.com/HLINE.gif","line":147},{"title":"Resources in Powerball Mega Millions, Software, Wheels, Systems","target":"https://saliu.com/content/lottery.html","line":149},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":153},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":155},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":157},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":159},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":160},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":161},{"title":"_**Mega Millions**_","target":"https://saliu.com/mega-millions.html","line":162},{"title":"**_Powerball_**","target":"https://saliu.com/powerball.html","line":164},{"title":"**_Euromillions_**","target":"https://saliu.com/euro_millions.html","line":166},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":168},{"title":"_**The Lottery – Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":169},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":170},{"title":"_**Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":171},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":172},{"title":"**Software**","target":"https://saliu.com/infodown.html","line":173},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/free-lotto-lottery.html","line":174},{"title":"Software, systems, strategies for Powerball, Mega Millions are harder to find and obtain.","target":"https://saliu.com/HLINE.gif","line":176},{"title":"Read carefully these Powerball strategies, systems of skips and understand the lottery software.","target":"https://saliu.com/HLINE.gif","line":180},{"title":"Forums","target":"https://forums.saliu.com/","line":182},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":182},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":182},{"title":"Contents","target":"https://saliu.com/content/index.html","line":182},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":182},{"title":"Home","target":"https://saliu.com/index.htm","line":182},{"title":"Search","target":"https://saliu.com/Search.htm","line":182},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":182},{"title":"Thanks for visiting the site of lottery, lotto, Powerball, Mega Millions, software, systems.","target":"https://saliu.com/HLINE.gif","line":184}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["Powerball","power ball","software","strategy","strategies","system","systems","numbers","skips","winning","combinations","draws","results","drawings","pool"],"source":"https://saliu.com/powerball-systems.html","author":null}},
"smart_sources:notes/saliu/Powerball, Mega Millions Strategies, Systems.md": {"path":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10058223,-0.01569533,-0.02264771,-0.03329945,-0.03198262,0.05800192,-0.02189798,-0.00998495,0.06205037,0.02116541,0.02298997,-0.00336844,0.08395457,-0.02316187,0.01381003,-0.02711458,0.0019191,-0.05254349,-0.03837461,0.00095317,0.07933512,-0.06457486,-0.03446407,-0.09967712,0.03261498,0.03053435,-0.00908706,-0.02898037,-0.03068343,-0.25442439,0.01267772,0.03101388,0.00158942,-0.06977164,-0.07560693,-0.0480818,-0.03202239,0.04792098,-0.04587611,0.03516379,-0.00839363,0.03315233,0.01146374,-0.05395865,-0.004701,-0.00837567,-0.00927124,0.01356379,0.05988211,-0.00816681,-0.04595025,-0.00656351,-0.01584458,0.04794946,0.08355758,0.00948173,0.07079088,0.07405614,-0.00442985,0.04071598,0.05036753,0.06688064,-0.183943,0.0594194,0.02829706,0.03079096,-0.01326893,0.00568252,0.05068934,0.04363637,-0.00329967,0.04616902,-0.02380765,0.08034188,0.02479408,0.01174839,-0.04680203,-0.03753676,-0.06439412,0.03655752,-0.02214686,-0.03780824,-0.02426018,-0.01592766,0.00248007,0.0356876,0.00214264,0.02867896,0.0458441,-0.06835546,0.00559445,-0.00291445,0.04575409,0.01776698,0.02769314,0.01041739,0.0551982,-0.04361594,-0.01730706,0.1084052,0.02407288,0.00490166,0.01005077,0.03936786,0.01369754,-0.04005076,-0.02494216,-0.0500454,-0.04786717,0.00065437,0.04357082,-0.00668497,0.06120636,-0.02326845,-0.03186663,0.00642389,0.01782013,0.00181049,0.04493516,-0.00861089,-0.05197583,0.0341877,0.00749016,0.02793167,-0.00559605,0.0092546,0.00844974,0.05331125,0.04450534,0.0323985,-0.001895,-0.00282665,-0.12326147,-0.09940194,0.0224715,-0.02085176,0.03219706,0.00488905,-0.0028362,0.01762128,-0.01443081,-0.01215413,0.06956013,-0.13906781,-0.01251939,0.0679158,0.03524394,0.01248318,0.00033094,0.01237758,-0.01354144,-0.02467218,-0.0338241,-0.0556526,-0.01450549,0.02610162,0.09155682,0.05763899,-0.07314239,-0.0150445,-0.04313478,-0.00372837,-0.03933983,0.12859282,0.03530567,-0.1072437,-0.0170635,0.00388088,-0.02748033,-0.07422694,-0.06534334,0.03983759,-0.03797158,-0.02708386,0.06818724,-0.0462499,-0.06343973,-0.09009787,-0.01344286,0.01324138,0.04827449,0.00551249,-0.01607524,-0.00947083,-0.04791039,-0.10965398,-0.00569934,-0.06225789,0.03036724,0.03449973,-0.05239198,0.00213428,-0.03292835,0.04095555,-0.04624756,-0.03254912,0.00640955,-0.02960286,0.07521091,-0.01727357,-0.04353955,-0.00416568,0.04531262,-0.00865735,0.01946688,0.04125084,-0.00090697,-0.04488532,0.070072,0.01746812,-0.02822487,-0.05782047,0.06503078,0.08317596,-0.06610595,0.05735265,0.00645416,0.00393434,-0.00687686,-0.00330984,-0.00741711,0.0333929,-0.07155843,-0.1837939,-0.05473328,-0.06592958,-0.00307541,0.0090947,-0.03495042,0.04518207,-0.0444631,-0.01719356,0.07412533,0.06758913,-0.059915,-0.02114853,0.05522077,-0.03068038,0.00061693,-0.12496773,-0.04801758,-0.04938642,0.08286202,0.02202995,0.00047788,-0.0126226,-0.05250558,0.01653256,-0.06323434,0.15591381,0.05670198,-0.05023333,0.03670022,0.07814869,0.02077643,-0.01984991,-0.04204983,0.00437049,0.00772324,0.00383938,-0.00477606,-0.01291675,-0.00971683,-0.08319579,0.00942339,-0.03320051,-0.0927874,-0.02142401,0.0072383,-0.00861012,-0.01766071,-0.01372043,0.03717874,0.06762979,-0.05814086,0.01886795,-0.01962979,0.05471336,-0.02356439,-0.08138516,0.05089177,-0.02270574,0.04164454,-0.00266103,-0.03344715,0.00878007,-0.01270307,0.01997824,0.01130107,0.00864347,0.03526494,0.01002321,-0.01076311,0.00753007,0.09707502,0.04361315,0.02451174,0.01157459,0.03823379,0.06979159,-0.02140348,-0.01666608,-0.00196876,-0.01287127,-0.01499755,0.04775012,0.09090768,0.06412025,-0.02229269,0.0564689,-0.0007297,0.02036505,-0.03009604,-0.07398048,0.03355986,-0.01892367,0.03067572,0.01728315,-0.00479268,-0.24111983,0.06863939,0.00734466,0.0266935,-0.02239186,-0.02206059,0.01150916,-0.02698844,0.02432789,0.01107048,0.06287649,0.02499329,0.06711905,-0.03625936,0.00458588,0.00855999,0.0138504,0.00837501,0.09205232,0.00983983,0.06975202,0.04179044,0.24492399,-0.02849652,-0.00680873,0.02009619,0.00409845,0.02750765,-0.00242314,0.06324194,0.0011199,0.04109433,0.08921333,0.00767616,-0.03987839,0.02287803,-0.01340212,0.05479538,-0.02666523,-0.00450224,-0.10113005,0.00049432,0.00683352,0.01842131,0.10499358,0.03503207,-0.01902865,-0.08337829,0.01985239,0.04922511,-0.10081792,-0.01688591,-0.0138035,-0.01027396,0.04240474,0.05196619,0.00819131,-0.01939615,-0.00344114,-0.0153116,0.03218205,0.02554059,0.04290466,0.00193441,0.03119105],"last_embed":{"hash":"ij6ili","tokens":473}}},"last_read":{"hash":"ij6ili","at":1753423603540},"class_name":"SmartSource","last_import":{"mtime":1753363606862,"size":18205,"at":1753423416500,"hash":"ij6ili"},"blocks":{"#---frontmatter---":[1,6],"#Powerball, Mega Millions: Strategies, Systems":[8,185],"#Powerball, Mega Millions: Strategies, Systems#{1}":[10,15],"#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>":[16,125],"#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>#{1}":[18,125],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>":[126,148],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{1}":[128,139],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{2}":[140,140],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{3}":[141,141],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{4}":[142,142],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{5}":[143,143],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{6}":[144,144],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{7}":[145,146],"#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{8}":[147,148],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)":[149,185],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{1}":[151,152],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{2}":[153,153],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{3}":[154,154],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{4}":[155,155],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{5}":[156,156],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{6}":[157,157],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{7}":[158,158],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{8}":[159,159],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{9}":[160,160],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{10}":[161,161],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{11}":[162,162],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{12}":[163,163],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{13}":[164,164],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{14}":[165,165],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{15}":[166,166],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{16}":[167,167],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{17}":[168,168],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{18}":[169,169],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{19}":[170,170],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{20}":[171,171],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{21}":[172,172],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{22}":[173,173],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{23}":[174,175],"#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{24}":[176,185]},"outlinks":[{"title":"Lottery software creates skip systems for lotto, including Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":24},{"title":"The lottery software function creates skip systems for Powerball, Mega Millions, CA Super Lotto.","target":"https://saliu.com/ScreenImgs/powerball-system-skips.gif","line":56},{"title":"Win Powerball with lottery strategy and systems based on the SKIPS or gaps of lotto numbers.","target":"https://saliu.com/images/lottery-software.gif","line":76},{"title":"Generate first the skip report, then create the Powerball strategy system.","target":"https://saliu.com/ScreenImgs/powerball-skips.gif","line":88},{"title":"The lottery software calculates range of analysis for winning Powerball strategy, systems.","target":"https://saliu.com/images/lottery-software.gif","line":94},{"title":"Powerball Strategy and winning powerfall systems, software.","target":"https://saliu.com/images/lottery-software.gif","line":124},{"title":"The winning combinations of the Powerball system for 55 draws.","target":"https://saliu.com/ScreenImgs/powerball-55.gif","line":128},{"title":"The winning combinations of the Powerball system for 28 drawings.","target":"https://saliu.com/ScreenImgs/powerball-28.gif","line":130},{"title":"The winning combinations of the Powerball system for 42 past draws.","target":"https://saliu.com/ScreenImgs/powerball-42.gif","line":132},{"title":"_**The best online Powerball odds calculator: Lottery, lotto, Powerball, combination, random number generator**_","target":"https://saliu.com/calculator_generator.html","line":140},{"title":"_**Combinatorial software to calculate, generate permutations, combinations**_","target":"https://saliu.com/permutations.html","line":141},{"title":"_**combinations generator for lotto, Keno, Powerball**_","target":"https://saliu.com/combinations.html","line":142},{"title":"_**Median, Gauss, Bell, Curve: Random Number, Combination Generator**_","target":"https://saliu.com/median_bell.html","line":143},{"title":"_**Results file, drawings, winning numbers in Powerball**_","target":"https://saliu.com/powerball_results.html","line":144},{"title":"_**Powerball wheels**_","target":"https://saliu.com/powerball_wheels.html","line":145},{"title":"Powerball software generates systems, winning combinations, for Mega Millions, Euromillions too.","target":"https://saliu.com/HLINE.gif","line":147},{"title":"Resources in Powerball Mega Millions, Software, Wheels, Systems","target":"https://saliu.com/content/lottery.html","line":149},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":153},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":155},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":157},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":159},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":160},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":161},{"title":"_**Mega Millions**_","target":"https://saliu.com/mega-millions.html","line":162},{"title":"**_Powerball_**","target":"https://saliu.com/powerball.html","line":164},{"title":"**_Euromillions_**","target":"https://saliu.com/euro_millions.html","line":166},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":168},{"title":"_**The Lottery – Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":169},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":170},{"title":"_**Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":171},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":172},{"title":"**Software**","target":"https://saliu.com/infodown.html","line":173},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/free-lotto-lottery.html","line":174},{"title":"Software, systems, strategies for Powerball, Mega Millions are harder to find and obtain.","target":"https://saliu.com/HLINE.gif","line":176},{"title":"Read carefully these Powerball strategies, systems of skips and understand the lottery software.","target":"https://saliu.com/HLINE.gif","line":180},{"title":"Forums","target":"https://forums.saliu.com/","line":182},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":182},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":182},{"title":"Contents","target":"https://saliu.com/content/index.html","line":182},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":182},{"title":"Home","target":"https://saliu.com/index.htm","line":182},{"title":"Search","target":"https://saliu.com/Search.htm","line":182},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":182},{"title":"Thanks for visiting the site of lottery, lotto, Powerball, Mega Millions, software, systems.","target":"https://saliu.com/HLINE.gif","line":184}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["Powerball","power ball","software","strategy","strategies","system","systems","numbers","skips","winning","combinations","draws","results","drawings","pool"],"source":"https://saliu.com/powerball-systems.html","author":null}},"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11732528,-0.03810087,-0.04194415,-0.03118669,-0.01193497,0.05888404,-0.03293802,0.02459212,0.03776158,0.01337163,0.0322931,-0.02870253,0.03690103,-0.00526372,0.01758573,-0.01020431,0.00555062,-0.04039877,-0.01819445,-0.00307257,0.11633769,-0.04850924,-0.02955659,-0.06903778,0.0470846,0.00792089,0.02248524,-0.02933487,-0.02004054,-0.20396397,-0.01445622,0.00936927,0.00626235,-0.04415917,-0.02568889,-0.03701831,-0.02058076,0.05420111,0.00013956,0.04089489,-0.01119417,0.0358884,-0.02000183,-0.02689452,0.00167984,-0.02043078,-0.01966007,0.01885028,0.03930968,-0.01173349,-0.02049801,-0.0093312,-0.04703034,0.05525276,0.10406339,0.00044923,0.09899257,0.04796965,0.02785235,0.01595226,0.05593982,0.0641536,-0.21067183,0.08039273,0.01063298,0.02533493,-0.01906397,0.03423579,0.02883446,0.0511416,0.02721836,0.03509869,-0.02395847,0.04611026,0.01137517,0.04783163,-0.03958255,-0.05705125,-0.05934487,0.02756919,-0.00766949,-0.0269913,-0.04204604,-0.04964324,-0.01345993,0.01154519,0.00157258,0.05327072,0.05174929,-0.03730454,-0.02377136,0.00605781,0.08320949,0.01913925,-0.03702208,0.01139409,0.03589465,-0.01563851,-0.02378138,0.12144199,-0.00466012,0.01950467,0.02089898,0.02055935,0.02939248,-0.00976208,-0.02448703,-0.04263357,-0.04373154,-0.00549391,0.02126247,0.00680786,0.05065482,-0.03707802,0.02975128,-0.00811353,0.04139438,0.00996324,0.02890372,-0.01095111,-0.07298527,0.03161267,0.02049463,0.01472147,0.00995937,0.01108539,0.03127188,0.04967376,0.01582254,0.03536088,0.00178519,0.03514567,-0.11225171,-0.07541745,0.01371338,-0.02078514,0.06110275,-0.02492657,0.01052509,0.00532005,-0.00879936,-0.03151865,0.04384456,-0.1492932,-0.02080193,0.09500802,0.02791907,-0.01256953,-0.00628277,0.01153357,-0.02574385,-0.00425466,0.00561657,-0.05883998,-0.00350153,0.04094225,0.11487687,0.05807808,-0.05699145,-0.02235815,-0.00556277,-0.01471163,-0.05408634,0.12589402,0.0335949,-0.14709699,-0.00372882,-0.00178047,-0.01323095,-0.08558785,-0.07177041,0.01708826,-0.02656129,-0.04981244,0.08844259,-0.03173474,-0.00626052,-0.08044326,-0.04741408,0.01097787,0.02354334,0.01523696,-0.01816148,-0.00287121,-0.0439919,-0.06150846,-0.01522154,-0.06388588,0.08034643,0.04274206,-0.06444275,-0.01953673,-0.04080386,0.06389765,-0.05311251,-0.02603647,0.00014505,-0.03964281,0.06725878,-0.0413345,-0.05392963,-0.00037612,-0.0108954,-0.0096309,0.01694587,0.01543387,-0.0190169,-0.04704745,0.02591208,0.00764643,-0.01755295,-0.03433333,0.07103504,0.04896315,-0.0812127,0.03310583,-0.01351567,0.00626713,-0.01822218,-0.015414,-0.00619738,0.00539458,-0.09935962,-0.21038152,-0.01680723,-0.04216114,-0.04905986,-0.00551597,-0.03213925,0.06219522,-0.04440693,-0.01516219,0.08289125,0.03971294,-0.04483697,-0.00460281,-0.00894429,-0.01632019,-0.00030851,-0.08434951,-0.0435503,-0.016397,0.07194448,0.04712616,0.00168995,0.00794393,-0.03804373,0.0021668,-0.04468065,0.1384366,0.11190017,-0.03589568,0.03304257,0.10383176,0.00878139,-0.04820265,-0.07592624,0.00916199,-0.01230265,-0.02202941,0.02347476,-0.0337859,-0.01736852,-0.08013644,0.03556743,-0.01989023,-0.10100128,-0.0014242,0.00567208,0.00239132,-0.05971669,-0.02836594,0.02859088,0.07306608,-0.03804021,0.01179762,-0.00377154,0.0485106,0.00497722,-0.09606059,0.01836436,-0.05377703,0.04880775,0.00060876,-0.03117957,-0.00702731,-0.02449286,0.01542956,0.00787519,0.00503447,-0.00091778,-0.02582602,-0.00025131,0.01776691,0.09812611,0.07806049,0.01762584,0.03974899,-0.01906998,0.05661088,-0.02803429,-0.00200094,-0.01199374,-0.01725321,-0.02159286,0.05528395,0.0841262,0.06386065,0.02986009,0.02252256,-0.03182137,0.02207656,-0.046786,-0.04634214,0.03438303,-0.01717388,0.02335092,0.03085778,-0.01864096,-0.25737461,0.08645841,0.06996188,0.01778482,-0.01244016,0.01733388,0.02755267,-0.02443316,-0.01354396,0.04346262,0.0456696,0.04202272,0.0162962,0.01609125,-0.0149984,-0.00233518,0.01226605,0.00515577,0.09062414,0.04341681,0.02952364,0.0195716,0.23332961,0.00099664,0.00835366,0.00900429,-0.00191968,0.01638198,0.00068215,0.05990658,0.01097806,0.03028721,0.07293943,0.00723692,-0.03430777,0.09048747,-0.01373803,0.05150115,-0.02648581,0.01259989,-0.11805511,0.0230987,0.01003428,0.01330234,0.10692214,0.03633193,-0.020565,-0.05885782,-0.01645097,0.00543355,-0.0871087,-0.03707689,-0.02314227,-0.0129076,0.0157645,0.06081401,0.0423696,-0.05104193,0.03045522,-0.02800102,0.04389644,0.03399702,0.02452482,0.00095221,0.04456539],"last_embed":{"hash":"57wzeh","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"57wzeh","at":1753423602491},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#---frontmatter---","lines":[1,6],"size":244,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10302565,-0.00050496,-0.00866219,-0.03005002,-0.03417822,0.05741321,-0.01933499,-0.0228549,0.06059673,0.01214982,0.03084523,0.00253745,0.09577667,-0.02534391,0.01318553,-0.0322459,0.00074118,-0.05542142,-0.03388948,-0.00458492,0.07846192,-0.0642345,-0.04081468,-0.10189103,0.02906593,0.03447766,-0.0137606,-0.02625826,-0.03001316,-0.26429746,0.01490945,0.02618874,0.00910244,-0.07123482,-0.0762431,-0.05167632,-0.04093779,0.04061818,-0.05629053,0.03933005,-0.00641956,0.02651036,0.0163117,-0.06142826,-0.00394555,-0.0119197,-0.00576188,0.00901424,0.06271145,-0.00631847,-0.05915423,0.00807615,-0.01363152,0.03890563,0.07333272,0.01390519,0.07058489,0.08378947,-0.00993401,0.04837224,0.04445808,0.06258506,-0.1870088,0.05280583,0.03671036,0.03694319,0.00128858,-0.00211498,0.04721623,0.05784126,-0.00599789,0.04710985,-0.02719597,0.0854232,0.02941834,0.0041143,-0.04784846,-0.02372076,-0.04758816,0.04434616,-0.02686159,-0.04401259,-0.0198954,-0.01528568,0.00735029,0.03230396,0.00495638,0.03006688,0.04839812,-0.07640024,0.01903522,-0.00640666,0.03393228,0.01408505,0.03567101,0.0077975,0.05796764,-0.04185373,-0.00722046,0.11082656,0.03220446,-0.00327292,0.00916896,0.04499491,0.00020056,-0.0458234,-0.02785981,-0.04780615,-0.04828275,0.00311993,0.04036622,-0.00665729,0.06518172,-0.01399706,-0.03228743,0.01033659,0.01108959,0.00015146,0.03708872,-0.00082238,-0.04785772,0.03774686,0.00729149,0.01618426,-0.00989161,0.00167804,0.01122454,0.057158,0.04067385,0.02718169,-0.00021686,-0.0104775,-0.12777989,-0.10137761,0.01241189,-0.02077769,0.0315355,0.01094606,-0.00084059,0.01619619,-0.02042537,-0.01607289,0.0601187,-0.12471146,-0.01605099,0.06359906,0.03441983,0.0221866,0.00023839,0.01065593,-0.01000695,-0.02856352,-0.04013136,-0.06299643,-0.01547536,0.02069127,0.0893992,0.05720184,-0.07488266,-0.01014424,-0.05506643,-0.0005901,-0.03936955,0.12629348,0.04017544,-0.08036938,-0.02088876,0.0000028,-0.02681791,-0.07375459,-0.0536944,0.04200839,-0.05033484,-0.01872461,0.06127307,-0.04740012,-0.0704825,-0.074362,-0.01295714,0.01775956,0.04856541,-0.00904295,-0.00714813,-0.0010967,-0.04132052,-0.11407154,0.00210768,-0.05847869,0.02706631,0.03041439,-0.04078427,0.0062046,-0.03642491,0.03225566,-0.0464603,-0.03366863,0.00263968,-0.02971892,0.0772365,-0.01666423,-0.03230619,-0.00685611,0.05328131,-0.00292675,0.02204639,0.04901111,0.00202501,-0.04281349,0.07899968,0.02288748,-0.02797011,-0.06047751,0.06346317,0.08308321,-0.04973114,0.05760274,0.00662607,0.0016205,-0.00426119,0.00422835,-0.00084257,0.03500585,-0.06036475,-0.18211265,-0.04951866,-0.06741606,0.00514839,0.01010172,-0.04612811,0.03762682,-0.038781,-0.01067945,0.06584579,0.06164469,-0.06619462,-0.01342926,0.07439347,-0.03260271,-0.00471381,-0.12416305,-0.05210685,-0.04881595,0.07585169,0.02037203,-0.01170983,-0.02307281,-0.06768346,0.02533268,-0.06091963,0.16450012,0.04776271,-0.03612447,0.03007805,0.06488328,0.02505518,-0.00746126,-0.03557607,-0.00055645,0.01288195,0.01033459,-0.01447816,-0.01392812,-0.0101062,-0.08175979,0.01017479,-0.03585936,-0.08596367,-0.02158386,0.00993059,-0.01438595,-0.00511443,-0.00713158,0.04033548,0.06351761,-0.06994717,0.0182812,-0.01880135,0.05903005,-0.02963619,-0.08086114,0.05169424,-0.02166003,0.03963024,-0.00635034,-0.02876098,0.00918671,-0.00992192,0.0178483,0.01409286,0.00922102,0.03681544,0.02932233,-0.01583809,0.00124256,0.09576672,0.03464307,0.02241278,0.01101451,0.05527597,0.0725785,-0.03180898,-0.02215744,-0.00267409,-0.01410598,-0.01048043,0.04530292,0.07075494,0.0584182,-0.03337825,0.06882905,0.00454425,0.02326041,-0.02295671,-0.06884419,0.03842083,-0.01981482,0.0244469,0.0149499,-0.00598717,-0.23862709,0.06387123,-0.01742951,0.02886337,-0.02205572,-0.02949324,0.00568834,-0.01662487,0.0230229,0.00817921,0.06067525,0.02253275,0.08542398,-0.04296053,0.01473521,0.01445551,0.00612212,0.00770148,0.08745893,-0.00319828,0.07504188,0.03824304,0.24374197,-0.03543575,-0.01182696,0.02320023,0.00813401,0.02448073,-0.00659043,0.05599736,0.00265617,0.0400296,0.08567355,0.00479205,-0.03384348,0.0061553,-0.01349663,0.06588537,-0.01486095,0.00065108,-0.08720469,-0.01594332,0.01167907,0.02210796,0.10899527,0.03526739,-0.01533863,-0.09341012,0.02137745,0.05748402,-0.09581577,-0.01356776,-0.01378392,-0.00375548,0.04973469,0.05199333,-0.01499142,-0.01949209,-0.00970235,-0.02156179,0.02886513,0.02211695,0.04384042,-0.00092701,0.01693915],"last_embed":{"hash":"f7p1au","tokens":402}}},"text":null,"length":0,"last_read":{"hash":"f7p1au","at":1753423602526},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems","lines":[8,185],"size":17916,"outlinks":[{"title":"Lottery software creates skip systems for lotto, including Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":17},{"title":"The lottery software function creates skip systems for Powerball, Mega Millions, CA Super Lotto.","target":"https://saliu.com/ScreenImgs/powerball-system-skips.gif","line":49},{"title":"Win Powerball with lottery strategy and systems based on the SKIPS or gaps of lotto numbers.","target":"https://saliu.com/images/lottery-software.gif","line":69},{"title":"Generate first the skip report, then create the Powerball strategy system.","target":"https://saliu.com/ScreenImgs/powerball-skips.gif","line":81},{"title":"The lottery software calculates range of analysis for winning Powerball strategy, systems.","target":"https://saliu.com/images/lottery-software.gif","line":87},{"title":"Powerball Strategy and winning powerfall systems, software.","target":"https://saliu.com/images/lottery-software.gif","line":117},{"title":"The winning combinations of the Powerball system for 55 draws.","target":"https://saliu.com/ScreenImgs/powerball-55.gif","line":121},{"title":"The winning combinations of the Powerball system for 28 drawings.","target":"https://saliu.com/ScreenImgs/powerball-28.gif","line":123},{"title":"The winning combinations of the Powerball system for 42 past draws.","target":"https://saliu.com/ScreenImgs/powerball-42.gif","line":125},{"title":"_**The best online Powerball odds calculator: Lottery, lotto, Powerball, combination, random number generator**_","target":"https://saliu.com/calculator_generator.html","line":133},{"title":"_**Combinatorial software to calculate, generate permutations, combinations**_","target":"https://saliu.com/permutations.html","line":134},{"title":"_**combinations generator for lotto, Keno, Powerball**_","target":"https://saliu.com/combinations.html","line":135},{"title":"_**Median, Gauss, Bell, Curve: Random Number, Combination Generator**_","target":"https://saliu.com/median_bell.html","line":136},{"title":"_**Results file, drawings, winning numbers in Powerball**_","target":"https://saliu.com/powerball_results.html","line":137},{"title":"_**Powerball wheels**_","target":"https://saliu.com/powerball_wheels.html","line":138},{"title":"Powerball software generates systems, winning combinations, for Mega Millions, Euromillions too.","target":"https://saliu.com/HLINE.gif","line":140},{"title":"Resources in Powerball Mega Millions, Software, Wheels, Systems","target":"https://saliu.com/content/lottery.html","line":142},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":146},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":148},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":150},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":152},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":153},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":154},{"title":"_**Mega Millions**_","target":"https://saliu.com/mega-millions.html","line":155},{"title":"**_Powerball_**","target":"https://saliu.com/powerball.html","line":157},{"title":"**_Euromillions_**","target":"https://saliu.com/euro_millions.html","line":159},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":161},{"title":"_**The Lottery – Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":162},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":163},{"title":"_**Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":164},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":165},{"title":"**Software**","target":"https://saliu.com/infodown.html","line":166},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/free-lotto-lottery.html","line":167},{"title":"Software, systems, strategies for Powerball, Mega Millions are harder to find and obtain.","target":"https://saliu.com/HLINE.gif","line":169},{"title":"Read carefully these Powerball strategies, systems of skips and understand the lottery software.","target":"https://saliu.com/HLINE.gif","line":173},{"title":"Forums","target":"https://forums.saliu.com/","line":175},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":175},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":175},{"title":"Contents","target":"https://saliu.com/content/index.html","line":175},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":175},{"title":"Home","target":"https://saliu.com/index.htm","line":175},{"title":"Search","target":"https://saliu.com/Search.htm","line":175},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":175},{"title":"Thanks for visiting the site of lottery, lotto, Powerball, Mega Millions, software, systems.","target":"https://saliu.com/HLINE.gif","line":177}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11956029,-0.0113117,-0.02346766,-0.04680397,-0.0384691,0.05456221,-0.04270086,-0.00864951,0.04152316,-0.00145936,0.05120246,0.00448052,0.0661394,-0.00627176,0.00645573,-0.02041567,-0.01667427,-0.04500379,-0.01382673,-0.03579066,0.11437785,-0.07902867,-0.02887414,-0.07170279,0.04726961,0.02360892,-0.00149567,-0.01377751,-0.027824,-0.23641181,-0.0163551,0.02381953,0.04309228,-0.05995136,-0.04328622,-0.04736145,-0.03772799,0.05020956,-0.01741839,0.0463508,0.01038184,0.02039743,0.03123889,-0.04022828,-0.01549579,-0.0236191,-0.01295227,0.00982463,0.05155019,-0.03126713,-0.03360943,0.02079546,-0.0286747,0.04141454,0.07528755,0.0087522,0.10001539,0.07032147,0.0093959,0.03839986,0.02329266,0.04191528,-0.23331933,0.04554983,0.02074771,0.0208906,0.0051817,0.0032073,0.01069636,0.03317586,-0.01461272,0.02350841,-0.01613475,0.05510982,0.03662445,-0.00213259,-0.04007167,-0.02201708,-0.0493319,0.03456531,-0.0124714,-0.04194619,-0.03716446,-0.01707488,0.01173257,0.02210783,0.00264982,0.007661,0.04739596,-0.08061157,0.01192638,-0.00506988,0.03992034,0.0144711,-0.00918444,0.02836234,0.0365279,-0.02089049,0.00090246,0.12296131,0.02640804,-0.01743451,0.00281082,0.03487701,0.0038391,-0.01265199,-0.03690686,-0.05095619,-0.03305487,0.00684293,0.0224742,-0.01365206,0.0319101,-0.02270607,0.00067312,0.00681595,0.01183158,-0.01298627,0.02790199,-0.02254504,-0.04246121,0.06185831,0.01911812,0.01666785,-0.01684291,0.01674768,0.02517178,0.05854199,0.01276748,0.03420582,-0.0151512,0.00818329,-0.12825203,-0.10225581,0.00683314,-0.00828252,0.03832579,-0.02209896,-0.0046442,0.03530665,-0.02045566,-0.02650774,0.01597174,-0.13432971,-0.00859816,0.08648497,0.02621646,0.0253541,0.00105791,0.01592012,-0.00747319,-0.01448797,-0.00962534,-0.05361125,-0.00044429,0.03876953,0.0868808,0.04769317,-0.06173803,-0.01727516,-0.0362474,-0.01699693,-0.05992574,0.16310073,0.03835769,-0.08238623,-0.0057849,0.02134467,0.02166312,-0.06242367,-0.04365351,0.03628622,-0.01409956,-0.0209109,0.04897035,-0.03918022,-0.01580664,-0.08194743,-0.029799,0.05486144,0.01226913,-0.02448006,0.0031438,0.02622255,-0.04057696,-0.08261384,0.0035158,-0.06227413,0.07269934,0.04522302,-0.03413203,0.03948442,-0.04787746,0.02664251,-0.05874847,-0.05167006,-0.00737618,-0.03906024,0.06740857,-0.03528441,-0.05433059,0.01217373,0.01597423,-0.00622125,0.0339877,0.02177919,-0.02559368,-0.01040043,0.03176904,0.02084547,-0.02367472,-0.04278145,0.06689657,0.0521262,-0.06480298,0.06214308,-0.00597777,-0.00854015,-0.0103172,0.00118891,-0.0017947,0.02266342,-0.07939851,-0.20071557,-0.03636875,-0.05724847,-0.05971066,-0.02230188,-0.05253158,0.0479994,-0.05565725,0.00146594,0.06467158,0.05061334,-0.03959074,0.00806968,0.05208731,-0.03333364,-0.01580927,-0.08403085,-0.04167128,-0.03646272,0.06409359,0.05767869,-0.03268584,-0.03717959,-0.04874759,0.02406777,-0.02738328,0.16598593,0.08620939,-0.0321835,0.01729818,0.07667206,0.01324737,-0.0286638,-0.03063816,-0.01148043,-0.00049116,-0.00957169,0.00682705,-0.01698777,-0.01304251,-0.09071913,0.03229496,-0.02933176,-0.0724166,0.00848644,-0.00646862,0.0049374,-0.02966814,-0.00704108,0.04630994,0.07919593,-0.05968692,-0.00120025,-0.01879131,0.07017361,-0.02499195,-0.09309923,0.04908946,-0.05320492,0.0424743,-0.01504273,-0.00375847,-0.01214342,-0.00988245,0.00025569,0.03964752,0.01269477,0.03657194,0.0018609,-0.00324351,-0.00577766,0.11382924,0.05524708,0.03977907,0.04701449,0.01627076,0.06310104,-0.05974214,-0.03865605,-0.00476074,-0.00189205,-0.00036593,0.06845786,0.04181028,0.063852,-0.01536057,0.03302692,-0.00395052,0.00897428,-0.03764834,-0.05635193,0.03302147,-0.02592072,0.03054825,0.01501682,-0.01790269,-0.25732815,0.08292457,-0.01392106,0.02540622,-0.01656038,-0.00805862,0.01984936,-0.01545916,0.03128093,0.02210728,0.03303657,0.03135256,0.06925831,-0.01722676,0.01246838,0.02684066,0.00137052,-0.00092367,0.09389032,-0.00495468,0.06568275,0.039013,0.24186428,-0.02621491,-0.0091646,-0.0000931,0.01177465,-0.00157609,0.00629334,0.05331131,0.01565801,0.04137244,0.07278821,0.0160929,-0.02312439,0.05198434,-0.02376001,0.08914276,-0.01915544,0.00689408,-0.11451005,-0.01002685,0.02554618,0.02445567,0.12489863,0.02192048,-0.02487617,-0.09869987,0.04229208,0.0219053,-0.09000448,-0.01296651,-0.00445622,0.01349102,0.02143774,0.05604056,0.00222953,-0.02119952,0.01003241,-0.03926164,0.03565798,0.06223109,0.00372491,-0.01620451,0.03146527],"last_embed":{"hash":"ok55un","tokens":93}}},"text":null,"length":0,"last_read":{"hash":"ok55un","at":1753423602674},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#{1}","lines":[10,15],"size":280,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08611567,-0.00692651,-0.00737596,-0.02726706,-0.02485952,0.07894194,-0.01764613,-0.01673634,0.06534352,0.00861971,0.02323644,0.00209689,0.08707424,-0.03123995,0.01493586,-0.02748935,-0.00094922,-0.06959394,-0.03920192,0.00220048,0.08080814,-0.05659317,-0.03175586,-0.10007022,0.02444451,0.03126347,-0.01995899,-0.02878822,-0.03077483,-0.26951468,0.02941479,0.02777965,-0.00328048,-0.06559415,-0.07764076,-0.05217155,-0.03169619,0.04364647,-0.0540968,0.03693082,-0.01001468,0.03928943,0.00649471,-0.07108822,-0.00812418,-0.01461597,-0.00117391,0.01571183,0.06431475,-0.00682212,-0.05095898,0.00172086,-0.00747724,0.03907361,0.0846528,-0.00076456,0.06651556,0.07883856,-0.00612702,0.05091069,0.04838779,0.07151144,-0.1772119,0.05734825,0.03469568,0.02782975,-0.01484776,0.00204021,0.05235923,0.05988945,-0.00391342,0.04654624,-0.02830117,0.06876743,0.02678676,0.00715159,-0.0515007,-0.02779322,-0.06133084,0.04593251,-0.02273686,-0.04343744,-0.01284191,-0.02279608,0.01278702,0.02565649,-0.00029943,0.0348864,0.05238746,-0.07347599,0.0266535,0.01561282,0.0306291,0.0222219,0.03719049,0.00786029,0.05978187,-0.04415467,-0.00574229,0.1147452,0.03266096,0.00780937,0.00503752,0.04765963,0.00522234,-0.05265172,-0.0262304,-0.05115641,-0.04414776,0.00515155,0.0322874,-0.00499199,0.062752,-0.01052205,-0.03479687,0.01178564,-0.00053348,0.00032001,0.04293079,0.01636182,-0.04339834,0.02233464,0.00626031,0.02140935,-0.00267698,-0.00777921,0.01068983,0.05081656,0.04790559,0.02662903,0.01243964,-0.00432413,-0.13469668,-0.10079075,0.01340155,-0.00872313,0.04638603,0.01781498,0.00242201,0.0201489,-0.01768868,-0.00992039,0.06754167,-0.12953448,-0.01299798,0.08963399,0.03437515,0.00542937,-0.00454428,0.019092,-0.01170753,-0.02745207,-0.04426863,-0.06896959,-0.02468456,0.01935011,0.08504649,0.05389012,-0.07743616,-0.01799419,-0.06020323,0.00591714,-0.02416385,0.11950311,0.03296658,-0.0895103,-0.01559711,-0.02143637,-0.03506288,-0.07603695,-0.05461324,0.04629103,-0.04775928,-0.01782455,0.06677742,-0.0448455,-0.08171966,-0.08951529,-0.00334931,0.00270863,0.05841225,0.01573526,-0.01847398,-0.00530819,-0.04539702,-0.11465567,0.00603058,-0.05109546,0.01643087,0.02855733,-0.06064871,0.00004058,-0.03367564,0.03467422,-0.04182542,-0.0236387,0.01703819,-0.01290969,0.06962559,-0.01576293,-0.0432065,-0.00102934,0.05423411,-0.0016349,0.008209,0.04880597,0.00529688,-0.05752278,0.07920154,0.01950503,-0.02859291,-0.05917295,0.05291792,0.07925023,-0.04827835,0.04677339,0.00671649,-0.00396389,-0.00138707,0.00317196,0.00960165,0.02847131,-0.0496738,-0.17780931,-0.05213352,-0.06448658,0.00290349,0.02063743,-0.02764806,0.03785229,-0.03525343,-0.01410912,0.07589413,0.0550476,-0.06553764,-0.020394,0.07099065,-0.0479824,0.0015715,-0.12447105,-0.05294372,-0.04664256,0.07537115,0.00885837,0.00555061,-0.01601972,-0.06052703,0.0277676,-0.05907428,0.16063961,0.02712423,-0.05066635,0.03812765,0.06269476,0.01256366,-0.01571625,-0.03052609,0.00356692,-0.0028138,0.00363751,-0.02319564,-0.02333586,-0.00482308,-0.06751096,-0.00149149,-0.02838448,-0.09501742,-0.03553215,0.02481652,-0.01391907,0.00019141,-0.00540056,0.03562012,0.0626846,-0.06271828,0.02567659,-0.01731983,0.03927921,-0.03445256,-0.06931532,0.05849817,-0.00688977,0.03295422,-0.00441937,-0.03001291,0.00731503,-0.00950792,0.02594066,0.00031517,0.00488273,0.02718814,0.01375879,-0.02423089,0.00024691,0.08862221,0.04711859,0.00906841,0.0044341,0.05219064,0.07344935,-0.00986703,-0.01803527,-0.00900033,-0.01322262,-0.01590471,0.04147321,0.08045705,0.04756691,-0.03120331,0.07393,-0.00003083,0.01289241,-0.01719268,-0.07927127,0.02365844,-0.0176363,0.02728586,-0.0027123,-0.00057471,-0.23691314,0.06364235,-0.00794287,0.0330779,-0.02256818,-0.02413288,0.00281253,-0.00174496,0.01869804,0.00754839,0.07713775,0.02235618,0.08528735,-0.03085142,0.00973513,-0.00937785,0.0209476,-0.00330601,0.08184428,0.00751481,0.09090294,0.03170931,0.24537964,-0.02962589,0.0020276,0.02414707,0.00729983,0.02939946,-0.00985807,0.05590928,0.00484023,0.02622441,0.09311499,0.00171858,-0.03051401,0.00655365,-0.01332605,0.04850901,-0.02009047,-0.01486318,-0.07952014,-0.00824805,-0.00306746,0.0134644,0.11702487,0.03459165,-0.0182867,-0.081623,0.01267901,0.06517829,-0.10317674,-0.01651658,-0.01704854,-0.00391058,0.04896136,0.0522537,-0.0121676,-0.01212201,-0.00688903,-0.00785294,0.03413479,0.0198794,0.03296032,0.00220859,0.01984499],"last_embed":{"hash":"127g237","tokens":431}}},"text":null,"length":0,"last_read":{"hash":"127g237","at":1753423602709},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>","lines":[16,125],"size":11382,"outlinks":[{"title":"Lottery software creates skip systems for lotto, including Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":9},{"title":"The lottery software function creates skip systems for Powerball, Mega Millions, CA Super Lotto.","target":"https://saliu.com/ScreenImgs/powerball-system-skips.gif","line":41},{"title":"Win Powerball with lottery strategy and systems based on the SKIPS or gaps of lotto numbers.","target":"https://saliu.com/images/lottery-software.gif","line":61},{"title":"Generate first the skip report, then create the Powerball strategy system.","target":"https://saliu.com/ScreenImgs/powerball-skips.gif","line":73},{"title":"The lottery software calculates range of analysis for winning Powerball strategy, systems.","target":"https://saliu.com/images/lottery-software.gif","line":79},{"title":"Powerball Strategy and winning powerfall systems, software.","target":"https://saliu.com/images/lottery-software.gif","line":109}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0844819,-0.00833863,-0.00618684,-0.02909326,-0.02482477,0.07794042,-0.01680214,-0.01721986,0.0682986,0.00703738,0.02326411,0.00567925,0.08727883,-0.03231634,0.01343682,-0.02698388,-0.00406559,-0.06999532,-0.04213027,0.0059559,0.07883102,-0.05803295,-0.0298064,-0.09972964,0.02739703,0.0303511,-0.01945222,-0.02798228,-0.02858745,-0.26978928,0.03139053,0.02604176,-0.00454888,-0.06642991,-0.07923558,-0.05185558,-0.02962735,0.0430792,-0.05519841,0.03622187,-0.00765493,0.03852532,0.00794535,-0.07011353,-0.00852724,-0.01253727,-0.00142936,0.0145531,0.06450283,-0.00475179,-0.04884374,0.00369779,-0.00911031,0.03490105,0.08484836,-0.00398539,0.06337517,0.08202624,-0.00652379,0.0522772,0.0474684,0.07413041,-0.17784952,0.05497764,0.03272759,0.02771749,-0.01483053,0.00180999,0.0525291,0.05969094,-0.00125131,0.04351883,-0.02588734,0.07121597,0.02430305,0.00733817,-0.05268556,-0.03048896,-0.06163326,0.04684028,-0.02852294,-0.04103359,-0.01307474,-0.02223289,0.01108346,0.02461032,-0.00145358,0.03560801,0.05068809,-0.0697554,0.03010584,0.01410868,0.02545107,0.02232193,0.03697256,0.00696902,0.06119174,-0.04485734,-0.00454627,0.11553722,0.03112854,0.00602085,0.00609014,0.04842199,0.00669523,-0.05209697,-0.02717871,-0.05229434,-0.04162826,0.00663288,0.0352134,-0.00080121,0.05891946,-0.01190943,-0.03347431,0.01179657,-0.00108296,-0.00154044,0.04358592,0.01666226,-0.04327005,0.0219489,0.0034349,0.02025637,-0.00358603,-0.0090905,0.01263111,0.048452,0.04831449,0.02600846,0.01403153,-0.00791158,-0.13418579,-0.10179347,0.01326254,-0.00950887,0.04383489,0.01960582,0.00276708,0.02019412,-0.01495727,-0.00884167,0.06823056,-0.12925953,-0.01231817,0.09036292,0.03775286,0.00598456,-0.00415714,0.01845017,-0.01304281,-0.0275485,-0.04435674,-0.06821156,-0.02481823,0.0191192,0.08554076,0.05514107,-0.0752042,-0.01696066,-0.06123416,0.00209458,-0.02400848,0.11747201,0.03043785,-0.08921117,-0.01498006,-0.02153007,-0.03634348,-0.07405716,-0.05241357,0.0488681,-0.04866241,-0.01811742,0.06679221,-0.04457457,-0.08237203,-0.08912865,-0.00301445,0.00405864,0.05949539,0.01420241,-0.01737599,-0.00216029,-0.04443021,-0.11288117,0.00578377,-0.05057114,0.01624526,0.02785706,-0.06061333,0.00097803,-0.03428137,0.03443261,-0.03882004,-0.02357804,0.01619193,-0.01507699,0.0705271,-0.01565218,-0.04207953,-0.0025212,0.05468831,-0.0028598,0.00794187,0.0496384,0.00516858,-0.05786958,0.08015213,0.01936397,-0.03198438,-0.05879853,0.04953779,0.07903668,-0.0489909,0.04588128,0.00870033,-0.0036392,-0.00003791,0.00591639,0.00693469,0.02968247,-0.04745394,-0.1789179,-0.05537453,-0.0625752,0.00096642,0.01921467,-0.02844759,0.03786828,-0.03382411,-0.0152704,0.07564068,0.05608216,-0.06447582,-0.02293093,0.07231645,-0.0481054,0.00133327,-0.12468934,-0.0500965,-0.04594552,0.07631806,0.00721255,0.00353934,-0.01780509,-0.06198111,0.03000957,-0.06117067,0.15962584,0.02724405,-0.04983428,0.03538332,0.06093622,0.01026637,-0.01485399,-0.03383869,0.00184011,-0.00179609,0.00395843,-0.02742252,-0.02563971,-0.00437,-0.06564397,-0.0046064,-0.02703592,-0.0969656,-0.03579208,0.02295724,-0.01324121,0.00009177,-0.00222168,0.03975372,0.06307818,-0.06215439,0.0257198,-0.01378057,0.04031607,-0.03743958,-0.06566565,0.0597996,-0.00330928,0.03572943,-0.00509326,-0.03046657,0.01090313,-0.01117442,0.028818,0.0016193,0.00594076,0.02166769,0.01529655,-0.02549968,0.00081876,0.08836471,0.04455466,0.00934298,0.00333744,0.05330423,0.07310539,-0.00817996,-0.01752736,-0.00792153,-0.01383267,-0.01784353,0.04537939,0.07943457,0.04811337,-0.03191532,0.07529401,0.00173199,0.01170722,-0.01642126,-0.08005679,0.02299938,-0.01573798,0.02453143,-0.00591,0.00059878,-0.23811547,0.06391859,-0.01147806,0.03278348,-0.02078695,-0.02454708,0.00313094,-0.00085653,0.0172085,0.00344201,0.07802739,0.02124705,0.08668226,-0.02985331,0.00909198,-0.0105583,0.02322007,-0.00592541,0.08438401,0.00523151,0.09252712,0.03105615,0.24580251,-0.02563228,0.00090774,0.02320991,0.00953427,0.02892682,-0.00966568,0.05575168,0.00194394,0.02786795,0.09418247,-0.00003032,-0.03033614,0.01174104,-0.01331741,0.04487873,-0.02109259,-0.01623249,-0.07715148,-0.00692306,-0.00443358,0.01283166,0.11803545,0.0384301,-0.02035884,-0.07908342,0.01295906,0.06124011,-0.10178596,-0.0182277,-0.01599738,-0.00318794,0.04893172,0.05481197,-0.01313763,-0.01039634,-0.00874565,-0.00763647,0.03405933,0.01724627,0.03553466,0.00252581,0.02208808],"last_embed":{"hash":"o0n4bo","tokens":429}}},"text":null,"length":0,"last_read":{"hash":"o0n4bo","at":1753423602855},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>1. Powerball Strategy Based on Two Pools of Numbers Derived from Skips</u>#{1}","lines":[18,125],"size":11300,"outlinks":[{"title":"Lottery software creates skip systems for lotto, including Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":7},{"title":"The lottery software function creates skip systems for Powerball, Mega Millions, CA Super Lotto.","target":"https://saliu.com/ScreenImgs/powerball-system-skips.gif","line":39},{"title":"Win Powerball with lottery strategy and systems based on the SKIPS or gaps of lotto numbers.","target":"https://saliu.com/images/lottery-software.gif","line":59},{"title":"Generate first the skip report, then create the Powerball strategy system.","target":"https://saliu.com/ScreenImgs/powerball-skips.gif","line":71},{"title":"The lottery software calculates range of analysis for winning Powerball strategy, systems.","target":"https://saliu.com/images/lottery-software.gif","line":77},{"title":"Powerball Strategy and winning powerfall systems, software.","target":"https://saliu.com/images/lottery-software.gif","line":107}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07972707,-0.0231296,-0.04663754,-0.02313027,-0.05785049,0.06060892,0.01985027,-0.00276469,0.02347676,-0.01091629,0.00930447,-0.01530874,0.04011349,-0.01887462,0.00064597,-0.01744948,-0.00166697,-0.01894813,-0.05132041,0.00727195,0.12859215,-0.06103805,-0.04033744,-0.08195357,0.04951529,-0.01705498,-0.02468163,-0.04049258,-0.05346356,-0.24917722,0.01921218,0.00518878,0.00895315,-0.07779185,-0.04655667,-0.04388762,-0.03560625,0.04827853,-0.04654634,0.02649394,0.01845986,0.02373806,0.0276637,-0.03729343,0.01686749,-0.02482755,-0.00490676,0.02638137,0.08066761,0.00657024,-0.02011316,0.03790298,-0.02136921,0.03525476,0.08187732,-0.010098,0.09152494,0.08965394,-0.00096351,0.03417663,0.06692822,0.11208437,-0.17603306,0.0715076,-0.00188237,0.02418385,-0.00086425,0.01100602,0.02307816,0.05216284,-0.00558931,0.04865351,-0.04609756,0.04854657,0.00960628,0.0112809,-0.06059769,-0.05075347,-0.07299738,0.04843114,-0.03308427,-0.02878577,-0.02687925,-0.02400716,0.02188825,0.02915011,0.02731675,0.04390679,0.06138517,-0.00689872,-0.02111997,0.03375053,0.01710038,0.02031115,0.02374766,0.00310689,0.08856012,-0.02510384,0.0062213,0.11421748,0.0234797,0.00753852,0.02483181,0.03592117,0.04801344,-0.0534175,-0.0468333,-0.03894451,-0.05424744,-0.00460954,0.02386743,-0.03206835,0.06707856,-0.01476198,-0.03534266,0.01970395,0.01360272,-0.01760487,0.02807179,-0.02863372,-0.06806266,0.02749552,0.00994539,0.00843019,0.03156941,-0.0272138,0.02554298,0.03321477,0.01907201,0.04649132,0.04190168,0.00384208,-0.13800666,-0.09687392,0.01828787,-0.03234655,0.05971873,0.02616973,0.01150862,-0.00060034,-0.00959458,-0.02589698,0.08261958,-0.13029815,-0.00847103,0.08434578,-0.0303384,0.01771025,-0.00658623,-0.03467901,0.00402611,-0.01094239,-0.03959225,-0.07114889,-0.01025534,0.05044558,0.10234132,0.05358894,-0.08704156,-0.04272713,-0.03995682,0.01938937,-0.00906931,0.0747878,0.03388771,-0.1115718,0.00259084,-0.00361245,-0.03656287,-0.093373,-0.03581912,0.00263974,-0.04367893,-0.02858752,0.05368098,-0.04041326,-0.06402031,-0.05309131,-0.0091871,0.01353536,0.00246594,0.00674215,-0.01369903,-0.00348582,-0.03025214,-0.06335512,0.03089328,-0.07631225,0.06491394,0.04789975,-0.04981693,0.01492174,-0.04028751,0.05826588,-0.04041391,-0.0017202,-0.04581632,-0.05798102,0.0847955,-0.00765611,-0.02601939,-0.01723481,0.0043964,-0.02353427,0.01995976,0.02810537,-0.01490328,-0.04513827,0.05383006,0.0043499,-0.0134656,-0.05087683,0.04029647,0.07019211,-0.06898427,0.04918494,0.01301345,-0.00115785,-0.01064751,-0.01605177,0.05433731,0.03607145,-0.07208539,-0.18048336,-0.05744523,-0.05854144,-0.03605117,0.04004396,-0.05045462,0.06483874,-0.05961074,-0.00348694,0.07904392,0.05323021,-0.0399733,0.0062939,0.05501281,-0.00540351,0.01901966,-0.10784784,-0.03348003,-0.05732979,0.07378369,0.05077673,0.01183473,0.01355921,-0.01283602,0.02570814,-0.05521046,0.13207982,0.05764129,-0.0104355,0.01797331,0.07652019,0.00904719,-0.03437023,0.01466824,0.03559354,-0.02794824,-0.01989157,-0.03596244,-0.03193995,0.03575962,-0.06797619,0.00695909,-0.0251205,-0.10541558,-0.04107273,-0.00057083,-0.03165072,-0.0249634,-0.01799128,0.03051911,0.08303703,-0.0276971,0.02794775,-0.03314835,0.04958288,-0.01755638,-0.06809352,0.0409847,-0.04030297,0.04825966,0.00481217,-0.0632935,-0.0184099,-0.02169521,0.04072187,-0.00577783,-0.00149415,0.00206017,0.01956382,-0.00559192,-0.00481298,0.08836607,0.05621335,0.08265249,0.05163424,0.00930334,0.09477056,-0.03526155,0.01411452,0.00276669,-0.01086325,-0.01082849,0.0434064,0.0842835,0.03301204,-0.01048826,0.05948343,0.03323538,0.00666147,-0.01038876,-0.04977377,0.01917034,-0.02847382,0.02108972,-0.0221622,0.02037665,-0.25097552,0.06705569,-0.02316697,0.01791381,-0.04728162,-0.03608353,0.05244198,-0.02631881,0.00828736,0.00911241,0.05502827,0.04243121,0.04428716,-0.0649679,0.0048582,-0.02443754,-0.01575843,-0.01917061,0.11381508,0.00837031,0.06494356,0.02591256,0.20643461,-0.03213634,0.03996652,0.0135916,0.01258281,0.00008017,0.00408159,0.02412692,0.02535206,0.03025049,0.07487359,-0.03217039,-0.02263732,0.06104283,0.00908469,0.0290931,-0.0137206,-0.01200606,-0.07251459,0.01695375,-0.01057224,-0.01468487,0.12431032,0.02330085,-0.02755643,-0.05748019,0.0235045,0.05348181,-0.08351006,-0.01381044,-0.02547837,-0.0366496,0.00691864,0.04067123,0.02461695,-0.04452768,0.00113061,-0.0198445,0.032673,0.06435346,0.04091334,0.00536665,0.0334929],"last_embed":{"hash":"18ul1yx","tokens":443}}},"text":null,"length":0,"last_read":{"hash":"18ul1yx","at":1753423603003},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>","lines":[126,148],"size":2408,"outlinks":[{"title":"The winning combinations of the Powerball system for 55 draws.","target":"https://saliu.com/ScreenImgs/powerball-55.gif","line":3},{"title":"The winning combinations of the Powerball system for 28 drawings.","target":"https://saliu.com/ScreenImgs/powerball-28.gif","line":5},{"title":"The winning combinations of the Powerball system for 42 past draws.","target":"https://saliu.com/ScreenImgs/powerball-42.gif","line":7},{"title":"_**The best online Powerball odds calculator: Lottery, lotto, Powerball, combination, random number generator**_","target":"https://saliu.com/calculator_generator.html","line":15},{"title":"_**Combinatorial software to calculate, generate permutations, combinations**_","target":"https://saliu.com/permutations.html","line":16},{"title":"_**combinations generator for lotto, Keno, Powerball**_","target":"https://saliu.com/combinations.html","line":17},{"title":"_**Median, Gauss, Bell, Curve: Random Number, Combination Generator**_","target":"https://saliu.com/median_bell.html","line":18},{"title":"_**Results file, drawings, winning numbers in Powerball**_","target":"https://saliu.com/powerball_results.html","line":19},{"title":"_**Powerball wheels**_","target":"https://saliu.com/powerball_wheels.html","line":20},{"title":"Powerball software generates systems, winning combinations, for Mega Millions, Euromillions too.","target":"https://saliu.com/HLINE.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0765997,-0.02242887,-0.04365043,-0.02797853,-0.05635495,0.06565615,0.02550285,-0.00371227,0.02587819,-0.00709187,0.01697169,-0.01350924,0.03937494,-0.02533557,-0.00347114,-0.01895847,0.00083885,-0.02033935,-0.05056876,0.00716931,0.12557986,-0.06653965,-0.04303278,-0.07993487,0.05049371,-0.01602684,-0.02409666,-0.03764738,-0.05760528,-0.25671902,0.02621511,0.00782509,0.00948688,-0.07653343,-0.03907197,-0.04972491,-0.03694335,0.04232941,-0.04749789,0.02509511,0.02109006,0.0246029,0.03261365,-0.04617512,0.01446184,-0.02696819,-0.00961476,0.02301119,0.07757744,0.00544182,-0.02215065,0.03405896,-0.02167642,0.03328982,0.0835529,-0.0080352,0.08645986,0.08899809,-0.00102924,0.03089548,0.06930637,0.11215939,-0.17340897,0.06951702,-0.0024319,0.02241151,-0.00313838,0.00892126,0.02368504,0.05212743,-0.00137786,0.04467086,-0.04808987,0.04384305,0.006078,0.01136487,-0.06230207,-0.05278456,-0.07550963,0.04495107,-0.03478676,-0.03465999,-0.01957936,-0.02342239,0.01593504,0.03084175,0.03183078,0.044724,0.05649193,-0.0047852,-0.0215091,0.0309366,0.01497316,0.0253159,0.02348478,-0.00319535,0.08914902,-0.02111939,0.00714546,0.11596429,0.02138306,0.00680986,0.0287491,0.03477586,0.04578493,-0.05089121,-0.04282812,-0.03248694,-0.0599989,-0.000721,0.02636869,-0.02877733,0.06580225,-0.01282508,-0.02835707,0.02058966,0.01278313,-0.01882919,0.02109086,-0.0271957,-0.06616753,0.02482003,0.00891882,0.00608036,0.02221169,-0.02868717,0.02772573,0.03162205,0.01931699,0.04029192,0.04118554,0.00744806,-0.13959502,-0.09707183,0.01985141,-0.02767973,0.05949413,0.03204735,0.01247474,0.00091148,-0.01325168,-0.0196833,0.0923562,-0.1344789,-0.01215605,0.08692674,-0.02605378,0.00863494,-0.00102004,-0.02493514,0.00284915,-0.01444809,-0.04696821,-0.07253747,-0.01407182,0.05011516,0.10195624,0.05746564,-0.09000317,-0.03750039,-0.04088302,0.01631616,-0.01161566,0.08201203,0.03320882,-0.11014757,0.00404237,-0.00825543,-0.0377043,-0.09203751,-0.04118057,0.00788939,-0.04325328,-0.02800357,0.05341005,-0.04299385,-0.07367603,-0.05549448,-0.01002857,0.00786646,0.00603711,0.00846878,-0.00896246,-0.00142925,-0.02429586,-0.06556461,0.0256308,-0.07521444,0.0676617,0.04201114,-0.05920023,0.01931784,-0.03866714,0.06172594,-0.03823163,-0.0023648,-0.04171536,-0.05321733,0.08428363,-0.00558065,-0.02651162,-0.01679346,0.00574148,-0.01874124,0.02420712,0.02793649,-0.01588635,-0.04093787,0.04935101,0.0023832,-0.01729217,-0.0499717,0.03505953,0.07111032,-0.06294662,0.04887308,0.00867846,-0.00182525,-0.0098358,-0.01559613,0.05031896,0.03879518,-0.07199774,-0.18097892,-0.06147543,-0.05879961,-0.04161609,0.04224308,-0.04700909,0.06192914,-0.05630267,-0.00063171,0.07597918,0.05472863,-0.03860718,0.002802,0.0594665,-0.00691101,0.02144655,-0.11003625,-0.02651014,-0.05516434,0.07239217,0.05274266,0.01114499,0.01225166,-0.00782024,0.02822327,-0.05374124,0.13377297,0.05900344,-0.01461607,0.02173244,0.0737582,0.01133318,-0.03600422,0.01366922,0.02905757,-0.02587118,-0.01823428,-0.03996783,-0.02950411,0.03515645,-0.06751123,0.0015059,-0.02713255,-0.1051992,-0.03615743,0.00777469,-0.02821202,-0.02871544,-0.0209134,0.02658727,0.08415013,-0.02899735,0.01977464,-0.03226038,0.04745746,-0.01770052,-0.06792581,0.03915909,-0.04305061,0.0567484,0.00455665,-0.06193925,-0.02406092,-0.02096374,0.04356961,-0.00201149,-0.00410684,0.00682143,0.0150318,-0.00563053,-0.00177389,0.08576551,0.0557099,0.07563087,0.05077047,0.01313632,0.09935402,-0.03000094,0.01186415,0.00325632,-0.01004382,-0.01097389,0.03911115,0.08828969,0.03974665,-0.00524862,0.05615377,0.02625638,0.0061711,-0.01478633,-0.04780893,0.02674811,-0.02574586,0.02069506,-0.02665794,0.01988654,-0.24846801,0.06479633,-0.01773314,0.01589487,-0.05067478,-0.03064667,0.05304512,-0.02534632,0.00973794,0.0123638,0.05937326,0.04498263,0.04513333,-0.06099626,0.00468434,-0.02540516,-0.00884486,-0.0185244,0.11487783,0.00647065,0.06836238,0.02676593,0.20644173,-0.02900386,0.03306028,0.01138185,0.00659362,-0.00317004,-0.00698629,0.02944596,0.02699912,0.02673575,0.0778263,-0.03124103,-0.02013417,0.06086602,0.01029339,0.03221688,-0.00869669,-0.01033514,-0.07561256,0.01196759,-0.0108894,-0.01759757,0.12095799,0.02568845,-0.02892364,-0.0596321,0.02147679,0.06002526,-0.07529307,-0.01033614,-0.02314922,-0.03554876,0.01055755,0.03914206,0.02251028,-0.04576952,0.00170841,-0.01352715,0.02699416,0.06381418,0.03943488,0.00129321,0.03471877],"last_embed":{"hash":"12rfq7k","tokens":388}}},"text":null,"length":0,"last_read":{"hash":"12rfq7k","at":1753423603153},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{1}","lines":[128,139],"size":1237,"outlinks":[{"title":"The winning combinations of the Powerball system for 55 draws.","target":"https://saliu.com/ScreenImgs/powerball-55.gif","line":1},{"title":"The winning combinations of the Powerball system for 28 drawings.","target":"https://saliu.com/ScreenImgs/powerball-28.gif","line":3},{"title":"The winning combinations of the Powerball system for 42 past draws.","target":"https://saliu.com/ScreenImgs/powerball-42.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08271182,-0.00394294,-0.01276718,-0.0323387,-0.00857336,0.09313998,-0.00944997,-0.01961759,0.02578347,-0.0247058,0.04334857,-0.01823935,0.02449894,-0.0473442,-0.01687253,-0.03213408,-0.0009194,-0.01521804,-0.02941803,-0.0030833,0.08896599,-0.04616637,-0.03613514,-0.04035487,0.04026125,0.00351337,0.01977794,-0.00148809,-0.02966281,-0.2428679,0.0031316,-0.0089636,-0.01403,-0.07604798,-0.02059845,-0.06904028,-0.01857057,0.0450694,-0.01937355,0.03426316,0.02234595,0.00729402,0.01702172,-0.0460193,0.02262766,-0.04571564,0.01014122,0.0142284,0.09017848,-0.01624193,-0.01512549,0.024409,-0.00590784,0.01121216,0.11316711,-0.00600992,0.11695267,0.08542094,0.0074972,-0.00155007,0.02691675,0.10712565,-0.20657486,0.06330349,-0.00352186,0.01485056,-0.0274009,0.04352318,-0.00415108,0.06038773,0.01393887,0.00588202,-0.03180331,0.03333587,0.00455108,0.02670509,-0.03816729,-0.05304621,-0.05293543,0.04499017,-0.04019213,-0.01306017,-0.00166135,-0.02902383,0.01024559,0.05208986,0.01353459,0.07469633,0.0730383,-0.04261053,0.04911165,0.01829688,0.0007295,0.03519173,-0.01601861,-0.0004569,0.04363685,0.0175541,0.03888969,0.13319795,0.02187373,0.01452124,0.00983726,0.03117857,0.02457321,-0.05665899,-0.03114773,-0.0143314,-0.0378468,0.02275572,0.01308457,-0.00287278,0.04368525,-0.0013513,0.02256164,0.01993595,-0.00271229,0.00759476,0.0131354,-0.01208274,-0.04603697,0.02965304,0.00817975,0.00906582,-0.02932856,-0.02041624,0.07764482,0.01528295,0.02666075,0.03063668,-0.00154496,0.0189907,-0.18505231,-0.07957274,-0.02284439,-0.00777138,0.07784487,0.00245088,0.0145516,0.01346695,-0.01052936,-0.02146093,0.04043471,-0.14533935,-0.02329413,0.11522277,-0.05006526,0.01021292,-0.00498822,0.00952836,0.00359241,0.01047183,-0.02298953,-0.09473163,0.00325215,0.03002139,0.04543598,0.04340798,-0.04737655,-0.05365938,-0.03979657,-0.0042907,-0.0592868,0.11384565,0.01993107,-0.07745784,-0.01810056,-0.03454417,0.01209691,-0.09822036,-0.04185533,0.01274475,-0.03772737,-0.04680478,0.05915555,-0.03435454,-0.04916275,-0.07995667,-0.06337098,0.00405693,0.00579021,0.03242641,-0.0047232,0.0287499,-0.02321858,-0.06311677,0.02191116,-0.0751237,0.0805956,0.06693047,-0.09187724,-0.01592097,-0.07467394,0.05033584,-0.03343611,-0.02393317,-0.02957235,-0.04354193,0.06472522,-0.03120356,-0.03083935,0.02604559,-0.00707389,-0.00905822,-0.00611003,0.01794726,-0.01186595,-0.05558703,0.05139666,0.00708538,-0.04581578,-0.05302018,0.04907778,0.04261529,-0.04879279,0.01325373,0.02317784,-0.00513063,-0.02086006,-0.00431537,0.02792863,0.00240428,-0.05945895,-0.18663293,-0.05010947,-0.04989521,-0.04893731,-0.02014632,-0.05943175,0.05592105,-0.02656491,0.00805791,0.06470795,0.05776358,-0.02750501,0.00324804,0.06744523,-0.0254717,0.00336551,-0.07775954,-0.03546535,0.00896781,0.08204816,0.04857077,0.0094281,-0.00272368,-0.02471886,0.04884217,-0.06988016,0.14021853,0.07862405,-0.02264109,0.02066932,0.09049011,0.02147566,-0.06632081,-0.04511552,0.03292887,-0.03792794,-0.03436948,-0.00067601,-0.0536802,-0.01165448,-0.04782036,0.00382892,-0.01822717,-0.08017666,-0.01970493,0.00624887,-0.00722106,-0.00620637,-0.03497132,0.02214575,0.0771346,-0.00192095,0.01123388,-0.00209164,0.02927912,0.00475217,-0.07069813,-0.00801467,-0.02601728,0.02893097,-0.00886032,-0.0383762,-0.02239643,-0.00363876,0.03112622,0.0009764,-0.00443518,0.00604072,0.01746768,0.01456269,0.02350502,0.06199124,0.04454843,0.0482846,0.04181205,0.0279559,0.08392026,-0.070901,0.02266679,0.00273704,-0.01418441,-0.01054474,0.06575868,0.05935284,0.06621241,0.02273577,0.0131494,-0.00252209,-0.01578331,-0.04879502,-0.06383912,0.03995093,-0.03365811,0.03372204,-0.03866039,0.02767024,-0.25280982,0.04999482,-0.01505235,0.02839511,-0.03828155,-0.01878519,0.06494052,0.00018267,-0.00210062,0.03053879,0.07074991,0.02310677,0.03011907,-0.03469098,-0.00938696,-0.02123719,0.0261401,-0.00203076,0.12822084,0.011499,0.08136588,0.00819757,0.24239311,0.00234359,0.00722451,-0.00058962,-0.00777089,-0.02158908,-0.00260146,0.04984533,0.04420399,0.03633802,0.07426439,-0.01272554,-0.02700235,0.07598938,-0.02266232,0.05249955,0.00739697,-0.00325357,-0.07098381,0.00316773,0.00272551,-0.01182646,0.10131705,0.01424496,-0.01085243,-0.06276838,0.00688526,0.03927787,-0.07161698,-0.01843446,-0.03929008,-0.0238429,0.00874579,0.05351627,-0.00601837,-0.05262659,0.03329656,-0.00846926,0.02418739,0.05779461,0.0603671,0.01021492,0.04127606],"last_embed":{"hash":"1ap1nyv","tokens":118}}},"text":null,"length":0,"last_read":{"hash":"1ap1nyv","at":1753423603266},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#<u>2. How The Powerball Strategy Fared In The Past: Checking For Winners In The Pools Directly</u>#{6}","lines":[144,144],"size":218,"outlinks":[{"title":"_**Results file, drawings, winning numbers in Powerball**_","target":"https://saliu.com/powerball_results.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09324215,-0.02232254,-0.02311572,-0.0540118,-0.0416016,0.04879785,-0.03822207,-0.00392316,0.02508174,-0.01091652,0.02829397,-0.0213757,0.05179254,-0.04818235,0.01042958,-0.04260758,0.02778266,-0.00638792,-0.03960242,-0.02695801,0.08087932,-0.05479206,-0.06451721,-0.08130176,0.04613938,0.01305736,-0.01769472,-0.01159307,-0.0169161,-0.21278419,0.01728952,0.01788503,0.00870671,-0.06561319,-0.0543123,-0.0134712,-0.04544058,0.03167979,-0.04319741,0.02811867,0.01926461,0.01657654,0.01727241,-0.00500979,0.04959193,-0.04714163,0.04019364,0.0244711,0.07303365,-0.03180223,-0.06769312,0.00441425,-0.01702432,0.01759667,0.08190516,0.0159035,0.07876558,0.0969499,0.02615769,0.01589052,0.0407835,0.10587238,-0.20490491,0.08339394,0.04106614,0.03450708,0.00555502,0.01741008,0.03284026,0.01636026,0.00158782,0.03403935,0.00809545,0.06353857,0.00915799,0.00033209,-0.06584661,-0.0502875,-0.05444781,0.02620241,-0.03625985,0.00443728,0.0181297,0.00385279,-0.00896176,0.04927607,0.02807306,0.03422587,0.08407491,-0.07352777,0.01907661,0.02901562,0.05713782,0.02576831,-0.00065758,-0.00514144,0.0607507,-0.02010477,-0.00857013,0.12334709,0.01569569,-0.01001451,0.02955428,0.00362802,0.0166604,-0.04687063,-0.01621998,-0.03847191,-0.02535996,0.03279922,0.02258224,-0.01574573,-0.00331609,-0.0138472,-0.031267,-0.02132178,-0.00177513,-0.01555584,0.00746946,-0.01873405,-0.07690317,0.00171634,0.01674978,0.03344271,-0.00637635,-0.02072524,0.01335414,0.04692702,0.02459344,0.01695434,0.01349456,0.05441507,-0.14585409,-0.07830022,0.00740431,-0.02666058,0.05463398,-0.02253645,0.0103021,0.03667968,-0.02480297,-0.06809117,0.03231543,-0.12959898,-0.02520852,0.07919323,0.01822817,-0.00576353,-0.0188239,0.00244929,0.00501004,-0.02775582,-0.01451494,-0.06750512,0.02328288,0.03433629,0.13587122,0.05009294,-0.05024175,-0.01645373,-0.04676134,-0.01271813,-0.04053553,0.14260325,0.00553546,-0.13272199,0.00803515,0.02168782,-0.00956857,-0.10422955,-0.0356613,0.01053176,-0.06048905,-0.0079387,0.09261765,-0.03692659,-0.03663109,-0.05540181,-0.0279966,0.01225382,0.02115661,-0.03256807,-0.02024043,0.01215954,-0.02883307,-0.08732875,-0.02896173,-0.03390986,0.03596028,0.05317834,-0.03459042,0.01460436,-0.0427052,0.00875203,-0.06867542,-0.0207543,-0.03439537,-0.04877882,0.07231851,-0.02998441,-0.01712735,0.02809856,-0.00720579,0.0089484,0.00523095,0.04009311,-0.03033201,-0.04596507,0.05673986,0.01682827,-0.03064955,-0.00392935,0.07461165,0.06212006,-0.06787256,0.06025566,0.01915667,0.01494213,-0.03979571,0.01749969,0.00345217,0.01953259,-0.09153858,-0.1873225,-0.04389622,-0.04936182,-0.02447151,-0.01441765,-0.02834679,0.07437469,-0.06512121,-0.00914756,0.07239959,0.10128719,-0.06801,0.01847751,0.05711138,-0.02032742,-0.00830983,-0.08978829,-0.05350191,-0.047486,0.0614974,0.04621083,-0.01262337,-0.00296886,-0.06440983,-0.00288611,-0.03933901,0.12697794,0.00885447,-0.00313057,0.03742747,0.09307581,-0.0234309,-0.02852827,-0.05582212,-0.00712188,0.00947965,-0.02978977,0.0033616,-0.01572165,0.01263439,-0.07551721,0.02015677,-0.00218805,-0.06668655,-0.04010297,0.01384467,0.01639943,0.00405123,-0.01194544,0.05468473,0.04480346,0.00336334,0.04508919,-0.00162257,0.03550464,-0.00694658,-0.04458949,0.01955957,-0.06029498,0.05950182,-0.02641899,-0.05045247,0.00037188,-0.01016572,0.04435395,-0.01073473,-0.00706471,-0.00448538,0.01364351,0.00447479,-0.00366342,0.08340305,0.03226453,0.07449941,0.02869708,0.01480755,0.09115537,-0.04056126,0.00762211,-0.00797036,-0.06688964,-0.03411087,0.05300384,0.09381201,0.06285345,0.03363344,0.06627803,-0.00768941,-0.01477853,-0.02122436,-0.03810433,-0.00250652,-0.01286673,0.03529848,0.00913572,0.04291054,-0.26015416,0.05853338,-0.00637723,0.03960124,-0.02574103,-0.03990385,0.02827379,-0.04485845,0.02721739,0.00059108,0.06282274,0.02243117,0.03266624,-0.03421008,0.02082065,-0.03440097,0.03696505,-0.00692982,0.07163507,0.03198961,0.04673762,0.02593665,0.23448715,0.01868754,0.0053413,0.02756213,-0.0061106,0.00160635,-0.00019583,0.0556123,0.03518702,0.03807939,0.054457,0.01469326,-0.04719895,0.04241088,-0.03623503,0.03977277,0.00478511,0.0192874,-0.06862471,0.01829583,0.00012511,0.0040721,0.10990028,0.03829463,-0.0297839,-0.09988058,0.03932003,0.04822073,-0.09091522,-0.01507921,-0.06606141,-0.0141441,0.01062468,0.04076136,0.02411615,-0.0248234,0.02945929,-0.03823642,0.05715907,0.01051651,0.03394957,0.00424698,0.01314791],"last_embed":{"hash":"11g6sgp","tokens":413}}},"text":null,"length":0,"last_read":{"hash":"11g6sgp","at":1753423603301},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)","lines":[149,185],"size":3794,"outlinks":[{"title":"Resources in Powerball Mega Millions, Software, Wheels, Systems","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":9},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":11},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":12},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":13},{"title":"_**Mega Millions**_","target":"https://saliu.com/mega-millions.html","line":14},{"title":"**_Powerball_**","target":"https://saliu.com/powerball.html","line":16},{"title":"**_Euromillions_**","target":"https://saliu.com/euro_millions.html","line":18},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":20},{"title":"_**The Lottery – Mathematics, Social Purpose, History, Software, Systems**_","target":"https://saliu.com/lottery.html","line":21},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":22},{"title":"_**Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":23},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":24},{"title":"**Software**","target":"https://saliu.com/infodown.html","line":25},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/free-lotto-lottery.html","line":26},{"title":"Software, systems, strategies for Powerball, Mega Millions are harder to find and obtain.","target":"https://saliu.com/HLINE.gif","line":28},{"title":"Read carefully these Powerball strategies, systems of skips and understand the lottery software.","target":"https://saliu.com/HLINE.gif","line":32},{"title":"Forums","target":"https://forums.saliu.com/","line":34},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":34},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":34},{"title":"Contents","target":"https://saliu.com/content/index.html","line":34},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":34},{"title":"Home","target":"https://saliu.com/index.htm","line":34},{"title":"Search","target":"https://saliu.com/Search.htm","line":34},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":34},{"title":"Thanks for visiting the site of lottery, lotto, Powerball, Mega Millions, software, systems.","target":"https://saliu.com/HLINE.gif","line":36}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07311484,-0.0504668,-0.02620606,-0.0358761,-0.04850304,0.04845935,-0.01666299,-0.01932486,0.03029832,-0.01244447,0.03125374,-0.02137106,0.04926419,-0.02212204,0.01757964,-0.02244204,0.03694252,0.01524592,-0.03637304,-0.01985411,0.10043548,-0.05563282,-0.08035485,-0.08628234,0.03271332,0.03830125,-0.01872705,-0.02198351,0.01285494,-0.17394967,0.0124083,0.01096871,-0.00281609,-0.06508386,-0.05428103,-0.04584999,-0.04916244,0.0359065,-0.07685144,0.03768339,0.02025775,0.02515337,0.00763298,-0.01371274,0.01857363,-0.05095817,0.05026366,0.02530161,0.08712982,-0.02295948,-0.0220826,0.02734194,-0.03166452,0.04288824,0.05468671,0.01563706,0.07919214,0.09228978,-0.00223419,0.00822744,0.0248606,0.10583709,-0.17774795,0.06174887,0.02436314,0.05681941,0.01899835,0.01982956,0.03124341,0.02983382,0.02181644,0.03464689,0.00996708,0.06528914,0.01816283,0.00736874,-0.05022093,-0.03925424,-0.05115547,0.02598406,-0.04566639,-0.03206976,0.00142169,0.02115206,-0.02154671,0.04028416,0.0338794,0.03648384,0.09287305,-0.07756709,-0.00842779,0.02809528,0.05926874,0.03573749,0.01045765,-0.00802473,0.05985973,-0.01527334,0.01383513,0.14776248,-0.00217091,-0.00371884,0.02039434,-0.01449017,0.01245527,-0.03058226,-0.02291233,-0.04062857,-0.03842976,0.05053069,0.03450823,0.00417887,0.03722084,-0.00103727,-0.02049333,-0.02079533,-0.02132325,0.00611677,-0.00385575,-0.02473461,-0.08225115,0.02508517,0.00220013,0.02129757,0.00072743,-0.01909092,0.00775121,0.06524323,0.0117327,0.01772768,0.003986,0.02311282,-0.13483661,-0.06532492,0.01731096,-0.03566007,0.06504138,-0.00898026,-0.02198862,0.05821871,-0.02632438,-0.03814252,0.05075346,-0.14980577,-0.01353475,0.0713133,0.01302612,0.004957,-0.02275535,0.01345991,-0.01432233,-0.01772178,-0.01085673,-0.07736771,0.00814536,0.02961775,0.13019648,0.01100266,-0.05401361,-0.00054565,-0.04753304,-0.00114699,-0.02904641,0.13790846,-0.00278058,-0.11145888,-0.00828914,0.02335312,-0.01729485,-0.09008925,-0.05702449,0.02069081,-0.06817701,-0.02624677,0.11021771,-0.036652,-0.07841211,-0.06810322,-0.03680277,0.00514124,0.02095968,-0.02855722,-0.00035748,-0.00154515,-0.0325895,-0.06778479,-0.02045452,-0.04680964,0.04760175,0.0476721,-0.03896429,0.034535,-0.03818348,-0.01723913,-0.07364379,-0.00970128,-0.04439083,-0.04083296,0.08851063,-0.02665038,-0.05084626,0.03269502,-0.00681875,0.01573224,-0.01856714,0.03118207,-0.01778834,-0.04979928,0.04402789,0.03119099,-0.04092645,-0.01468234,0.07313763,0.06211311,-0.08868408,0.04779075,0.02036786,0.02092087,-0.03465098,-0.00208372,-0.00351324,0.01940246,-0.10924275,-0.17876783,-0.04264928,-0.04194568,-0.02609943,0.00463918,-0.02195708,0.04073166,-0.05613077,-0.01911901,0.08188434,0.09464458,-0.08339208,0.02099522,0.07021211,-0.0236325,-0.00735037,-0.08373641,-0.04682608,-0.02936761,0.04120224,0.04557935,-0.01872911,0.02710456,-0.08326175,-0.00031686,-0.02638757,0.14418852,0.01482446,0.01007318,0.04706871,0.07308728,-0.03391018,-0.04370571,-0.04502469,-0.010643,0.00487886,-0.03959218,-0.01507027,-0.01118786,0.01189212,-0.05392119,-0.0143014,0.0035807,-0.07233284,-0.00876495,0.00661131,-0.01395501,0.00700218,-0.00214255,0.076263,0.04040862,0.00678725,0.04702021,-0.00183575,0.03491418,-0.00281507,-0.04977732,0.01468173,-0.05763136,0.05406047,-0.00314514,-0.04631095,-0.02838801,-0.00778411,0.05374196,0.00257735,-0.01700138,-0.02553886,0.00394874,-0.02038576,0.00666357,0.08263805,0.0448397,0.06968088,0.04106832,0.01011279,0.10243956,-0.05416627,0.00169593,0.00476864,-0.0504286,-0.02138006,0.05191464,0.11881226,0.06221427,0.02719013,0.05029333,0.01198128,-0.03375588,-0.03082125,-0.02888462,0.01978376,-0.03151915,0.02054878,0.00273913,0.01746162,-0.25067982,0.06880601,-0.0028866,0.03111966,-0.03262709,-0.05189372,0.02975765,-0.01336502,0.02235407,-0.00842683,0.05756377,0.02023799,0.03217176,-0.02057561,0.04007556,-0.02857884,0.04206513,-0.02174016,0.09080486,0.04106925,0.05382336,0.02574292,0.24826479,0.01454451,0.03238336,0.02116098,-0.01648201,0.00017404,-0.01773552,0.05585396,0.03823821,0.01675702,0.04027909,0.01416366,-0.04056539,0.05651758,-0.01590796,0.04157901,-0.01031139,0.01708263,-0.08714483,0.00520012,-0.02035655,0.02616891,0.10498069,0.01185064,-0.02845728,-0.08617417,0.01918183,0.04186638,-0.10089293,0.00099003,-0.04375314,-0.03064451,0.02220461,0.03383185,0.03979836,-0.03158999,0.03514071,-0.05204123,0.03095944,-0.00261599,0.01612368,-0.01115535,0.00676842],"last_embed":{"hash":"1m22jsz","tokens":94}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423603433},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{3}","lines":[154,154],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06450693,-0.00573028,-0.01499163,-0.08254302,-0.01338728,0.04999661,-0.00051229,-0.00284514,0.04018225,-0.03149116,0.05357864,-0.01946889,0.06278741,-0.05720231,-0.00349446,-0.03973161,0.01301036,-0.00623865,-0.0328972,-0.04764226,0.090803,-0.03907025,-0.06298275,-0.08957606,0.04846893,0.01025416,-0.03411616,-0.02607849,-0.02952059,-0.21018705,0.02413407,0.01402144,0.01488397,-0.05464136,-0.04286775,-0.00100039,-0.02980658,0.0118706,0.00005582,0.03409564,0.02325999,0.0034497,0.00060504,0.01520844,0.03115604,-0.0463512,0.02207863,0.02408239,0.036614,-0.02300184,-0.06631898,0.00267278,-0.00011558,0.02488155,0.04868755,0.0191958,0.0798944,0.08016067,0.03435371,0.04623784,0.04819326,0.09592026,-0.20356609,0.06575985,0.075101,0.03967786,0.00319938,-0.00420707,0.00980048,0.00975682,-0.01357582,0.01813184,0.00764514,0.06260832,0.02971691,-0.00204492,-0.08203361,-0.04634114,-0.07077791,0.03458756,-0.02339989,-0.00224256,0.00975351,0.01114375,-0.02511225,0.03193115,0.03831456,0.02388439,0.10248395,-0.06523777,0.02018176,0.04629894,0.04157121,0.02212518,-0.00022647,-0.01113479,0.04606786,-0.04048978,0.02602083,0.14647581,-0.01238968,-0.01534234,0.01644092,-0.00865544,0.00110659,-0.03296458,-0.01765597,-0.02732678,-0.00769632,0.01499624,-0.00454769,0.00188903,-0.01253717,-0.00620474,-0.03335337,-0.01056382,-0.01456433,-0.02672748,-0.01623302,-0.00944632,-0.0606423,-0.01895758,0.01419908,0.03216453,-0.00707169,-0.03181185,0.0534464,0.0606687,-0.00595112,0.00570115,0.00895946,0.0227256,-0.15516552,-0.07077157,0.01551175,0.00082365,0.06902154,-0.01419315,0.01563642,0.0622802,-0.02701,-0.03463829,0.03960374,-0.13192011,-0.01529875,0.10415894,0.02302103,-0.01209532,-0.0251142,-0.00043929,0.00774168,-0.03375773,-0.00983584,-0.04926249,0.01545712,0.0408375,0.12638795,0.03774325,-0.03494031,-0.02125336,-0.036889,-0.03363311,-0.03138296,0.15386617,0.00792896,-0.11607847,0.01731969,-0.00177405,-0.01140592,-0.10377949,-0.00723877,-0.00557073,-0.06712651,-0.01912113,0.03611284,-0.02613798,-0.03058909,-0.06539728,-0.02999279,0.01413011,0.03641748,-0.02952866,-0.01651329,0.02723959,-0.04980445,-0.07583922,-0.0276454,-0.00643097,0.02010063,0.04403292,-0.03260452,0.02627938,-0.06141508,0.00660667,-0.04616514,-0.02257291,-0.01617875,-0.03784495,0.08208443,-0.06591292,-0.03056647,0.02622212,-0.0045941,0.01385853,0.0431762,0.04414743,-0.0624707,-0.02569043,0.03119183,0.00742531,-0.02867381,-0.00818914,0.05544452,0.04646628,-0.05876121,0.06031756,0.02150828,-0.01466524,-0.00316035,0.02350476,0.00988937,0.02068748,-0.06874591,-0.20291145,-0.03802254,-0.03922182,-0.03640819,-0.0153613,-0.03003633,0.05932273,-0.06566804,-0.033347,0.09748419,0.07411029,-0.03291412,0.01995279,0.04718492,-0.01171724,0.00155642,-0.09391637,-0.06169564,-0.07809097,0.08425321,0.03302206,-0.01616959,-0.01960172,-0.04621196,0.00232899,-0.03577118,0.12568086,0.00601094,-0.01765494,0.04576135,0.09112293,-0.03332246,-0.03184915,-0.05811162,-0.01131222,0.00312901,-0.01853431,0.00039739,-0.01617426,0.01933356,-0.10153321,0.04688023,-0.0028074,-0.07089073,-0.01115917,0.03945605,0.04741003,-0.04309206,-0.0120332,0.03745805,0.03720361,-0.02568642,0.03499677,0.01584827,0.05451459,-0.01934898,-0.03782277,0.05092136,-0.06000557,0.04252968,-0.05208151,-0.06322667,0.00831308,0.0026955,0.04683122,-0.00679128,-0.00031885,-0.00148982,0.00848261,-0.00163764,0.00202444,0.09461597,0.03309718,0.05398524,0.03441345,0.03165046,0.09621269,-0.04914483,-0.00513904,0.01104084,-0.03034688,-0.03877171,0.05244948,0.0704559,0.04411492,0.01833383,0.05379945,-0.02847097,-0.005699,-0.01951495,-0.02227689,-0.01490371,-0.00588366,0.04107694,0.00565527,0.02166692,-0.24075685,0.07544286,-0.00633535,0.03073574,-0.02137169,-0.03733642,0.02698167,-0.03577857,0.03354196,0.01484598,0.0587773,0.02324271,0.03664775,-0.03417232,0.01846526,-0.03350076,0.08041694,-0.02992839,0.07464442,0.04084,0.04646417,0.02005282,0.25593248,0.01672657,0.01251711,0.02920262,-0.01720983,-0.01255738,-0.02504889,0.04897818,0.05512154,0.01355031,0.04833108,0.01111114,-0.06051611,0.02745469,-0.0261995,0.07472061,0.02789477,0.02177797,-0.07342651,0.00895067,-0.02970877,-0.03753519,0.11083664,0.0451573,-0.02832098,-0.0871265,0.01188341,0.03096974,-0.07860523,-0.02088806,-0.06509989,-0.02571212,0.01208503,0.02971435,0.00350702,-0.00473077,0.06202118,-0.04050811,0.07725094,0.02263818,0.00737152,0.00640841,0.03074767],"last_embed":{"hash":"1sdoqa9","tokens":101}}},"text":null,"length":0,"last_read":{"hash":"1sdoqa9","at":1753423603464},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{5}","lines":[156,156],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{23}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08873991,-0.05090941,-0.02465662,-0.04828279,-0.03392234,0.0494673,-0.04329982,0.00047163,0.02920451,-0.01298536,0.03161817,-0.04105016,0.03556043,-0.02487054,0.02929946,-0.02577651,0.03188425,-0.03257604,-0.05790606,-0.01958141,0.12208875,-0.06194825,-0.03774024,-0.05824412,0.01119369,0.00317,0.0020042,-0.01010363,0.0034339,-0.2040295,0.01181576,0.02558876,-0.0014791,-0.05632963,-0.0414706,-0.02797906,-0.03219598,0.01275088,-0.05403812,0.04891093,0.01505989,0.03828571,-0.01219847,-0.01625043,0.01648946,-0.0529114,0.03468411,0.01604304,0.08807718,-0.02038042,-0.02669161,0.03133394,-0.03015315,0.03831143,0.0837227,0.00987254,0.10764179,0.05719699,0.02027947,0.020525,0.04087383,0.12065554,-0.2036294,0.07487845,0.03564732,0.03993222,0.00636624,0.02961634,0.00943122,0.01889364,0.00380918,0.04755154,0.0071646,0.0518371,0.01219018,-0.00578933,-0.07387617,-0.03387425,-0.05985095,0.03217808,-0.02699916,-0.00283959,0.01277448,-0.0150295,0.02656481,0.06014895,0.01309407,0.05820378,0.07374875,-0.07220598,-0.01599089,0.04037115,0.06165592,0.06425177,-0.03319434,0.00201301,0.03769342,-0.01247483,-0.01875183,0.1186877,0.01566563,-0.00580979,-0.00307511,0.0153576,0.0154567,-0.01196399,-0.02216885,-0.03330176,-0.01545799,0.03316844,0.02535841,-0.00657045,0.05745047,-0.0062773,-0.01348491,-0.01112359,0.00745502,-0.02340326,-0.00305595,-0.00015712,-0.06972238,0.00957862,0.05224263,0.03598844,0.02236486,-0.0270372,0.02363975,0.03558387,0.04085198,0.03293161,0.00554454,0.02192956,-0.13061507,-0.10022014,0.00557321,-0.01956616,0.0568562,-0.03397458,-0.0088049,0.02995498,-0.02460751,-0.08771336,0.04702094,-0.14212567,-0.01996529,0.07929595,0.03935291,-0.00142982,-0.01595666,0.01217097,-0.01525974,0.01501651,0.01446084,-0.05548039,-0.00499144,0.04490191,0.10530502,0.05305789,-0.08837983,0.00107994,-0.02926342,-0.04047942,-0.01611231,0.14529759,0.01293575,-0.11635028,-0.01149723,0.02129599,-0.02179734,-0.09398258,-0.04258056,0.03610167,-0.06945873,-0.0213775,0.10534503,-0.0440738,-0.03876171,-0.0684962,-0.04413669,0.01103225,-0.00390628,-0.02740736,-0.00436042,0.01666119,-0.04247193,-0.08762069,-0.00724878,-0.04742433,0.06100502,0.05233356,-0.05407333,0.01400905,-0.01322499,-0.01085714,-0.07660003,0.0074938,-0.04065298,-0.04356822,0.09162806,-0.0127877,0.00713966,0.04634451,0.00695009,-0.00495421,-0.00490705,0.04795381,-0.0289016,-0.03299553,0.03532858,-0.00073462,-0.02709168,0.00792202,0.08924967,0.07785255,-0.09649025,0.04387975,0.0323302,0.01737451,-0.0195611,-0.00248144,0.01759172,0.02435351,-0.1141293,-0.19914821,-0.01457386,-0.05612543,-0.03462234,0.00376592,-0.01235627,0.07155868,-0.06723671,0.01866153,0.08928113,0.06834224,-0.081212,0.00157176,0.06767002,-0.02507549,0.0040816,-0.06169394,-0.06910813,-0.05029149,0.06832381,0.02743128,0.01085752,-0.03118746,-0.06635979,0.04148813,-0.03574425,0.14378607,0.04818764,-0.03416495,0.00137316,0.08896459,-0.02219282,-0.02321889,-0.05988307,-0.00074295,0.00641911,-0.0315544,-0.01400727,-0.0069822,0.00465113,-0.05549236,0.01040728,-0.01496262,-0.05288178,-0.02400495,0.00596617,-0.00827538,0.00449032,-0.00422937,0.03888327,0.05164747,0.0202274,0.04376915,0.00734679,0.05808058,-0.00307569,-0.05496803,0.03415193,-0.04721659,0.05943326,0.00412164,-0.02816719,0.00942408,-0.01537004,0.03095508,-0.00642599,-0.01023272,-0.00526294,0.00160082,-0.01622974,0.00275998,0.0548684,0.06253397,0.07195237,0.00004606,-0.01029603,0.0761167,-0.02170464,0.00547302,-0.02028656,-0.0222847,-0.01303876,0.03541992,0.07725419,0.02851487,0.03710545,0.04428047,-0.0240365,-0.01798549,-0.0271363,-0.02130563,0.01928581,-0.03894449,0.03889712,0.0420596,0.0083185,-0.26724565,0.05714646,0.01585286,0.0194176,-0.00982589,-0.00814809,0.01514192,-0.05435185,0.02935377,-0.01809158,0.07024617,0.01519025,0.02538045,-0.03512513,0.01086957,-0.01635973,0.04323461,-0.01065061,0.09773503,0.02648647,0.03134543,0.02171831,0.2300982,-0.00425117,-0.00502323,0.01547841,-0.02921014,-0.00519701,-0.01016827,0.02840351,0.01335153,0.0254337,0.04933845,-0.00764005,-0.04446261,0.05193062,-0.02440568,0.03342404,-0.01687958,0.00318045,-0.08978504,0.01316178,-0.02157978,-0.00803156,0.09578134,0.01769576,-0.04068151,-0.08143689,0.00947104,0.04829581,-0.08466946,-0.02596766,-0.05267878,-0.00897142,0.04844905,0.05359497,0.01861161,-0.04610381,0.01452929,-0.0363946,0.03048331,0.0252923,-0.00663653,-0.00748158,0.01614818],"last_embed":{"hash":"5vnhca","tokens":136}}},"text":null,"length":0,"last_read":{"hash":"5vnhca","at":1753423603499},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{23}","lines":[174,175],"size":202,"outlinks":[{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/free-lotto-lottery.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{24}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12268248,-0.02431095,-0.02934656,-0.03594469,0.00805993,0.08438189,-0.03363056,0.01731345,0.01375147,-0.02712797,0.00604409,-0.04100075,0.05939188,-0.04979539,0.03665622,-0.03470402,0.02240168,-0.00590139,-0.02945517,-0.00119637,0.09689838,-0.04537196,-0.02868543,-0.06917299,0.04003791,-0.0064378,-0.0173081,-0.00133076,-0.01199657,-0.22890058,0.00697005,0.00853581,0.01136993,-0.07074834,-0.05854699,0.00317368,-0.01572193,0.05208456,-0.00996885,0.06588231,0.006989,0.00646567,-0.00551012,-0.0383259,0.01079497,-0.03727486,0.02532271,0.02385517,0.06392216,-0.03759466,-0.10105333,0.03425605,-0.03160662,0.0207599,0.07915749,0.01338337,0.09574841,0.08982389,-0.00491211,0.02438866,0.04267393,0.07722101,-0.22234318,0.07257327,0.04941758,0.01631115,-0.01153477,0.00995905,0.04029952,0.03961509,0.02680313,0.02272242,-0.00236297,0.05778409,0.00576132,-0.00798788,-0.03724086,-0.04010113,-0.03286359,0.02456094,-0.01851476,-0.00219688,-0.03330216,-0.01381441,0.01599856,0.01969187,-0.00824274,0.04555899,0.06620394,-0.07248016,0.01439329,0.00680535,0.04420788,0.02891202,-0.01588134,-0.01429967,0.05091245,-0.02483578,-0.0114015,0.11640237,0.04497188,0.00439713,0.0163348,0.04786958,0.03277263,-0.01976601,-0.03848432,-0.02294567,-0.02008185,0.02523713,0.01514833,0.00285733,0.01388055,0.01245181,-0.02666528,-0.0078256,0.02980022,0.0042719,-0.00359582,-0.01258454,-0.09442916,0.00869514,0.04770028,0.03274823,-0.00541971,-0.01970082,0.0053336,0.03145717,0.04251056,0.05574507,-0.00082169,0.01311107,-0.13773924,-0.08186528,0.0042638,-0.01435507,0.02656027,-0.01154969,-0.00971503,0.04598184,-0.01064019,-0.08432183,0.03413371,-0.11209207,-0.03212548,0.0979211,-0.00590043,0.00531916,-0.03099145,-0.0095478,-0.03909476,-0.01805912,0.00681731,-0.0492336,0.00217434,0.03295795,0.13898276,0.04122572,-0.07640792,0.02390542,-0.02092352,-0.02287934,-0.02767362,0.14903995,0.07871523,-0.08187874,-0.01346197,-0.00815398,0.00694503,-0.09951453,-0.03853887,0.02064166,-0.04952515,-0.0230053,0.07038454,-0.05157409,-0.06123513,-0.08296597,-0.04041781,0.03517747,0.01318306,-0.00872597,-0.00416024,-0.00920643,-0.04416953,-0.11005862,-0.00061784,-0.07112384,0.03057429,0.04390346,-0.07301848,-0.00959407,-0.0452581,0.00065375,-0.05686208,-0.00318133,-0.01733053,-0.03889629,0.0752327,-0.01912209,-0.04072471,0.01173346,0.02074673,-0.00292403,-0.01245414,0.03498473,-0.02188776,-0.05025395,0.04178445,-0.00619884,-0.00314572,-0.0152305,0.07363047,0.05025312,-0.0609902,0.05286828,0.03472507,0.0279788,-0.0151385,-0.01143897,0.01522498,0.02721237,-0.09477516,-0.2005579,-0.02507588,-0.0809908,-0.04246448,0.00326075,-0.04813203,0.07970669,-0.06361677,-0.03203934,0.0757535,0.0842952,-0.06818016,0.02042069,0.03739264,-0.00491692,0.00177952,-0.07695756,-0.04602284,-0.02624052,0.07164025,0.02803288,0.00021193,-0.01258177,-0.04973368,-0.00370676,-0.04304034,0.15221649,0.03980239,-0.02683333,0.01133559,0.06730094,-0.02397347,-0.05009587,-0.05791587,0.00828448,-0.00610057,-0.03204327,0.00764078,0.00036795,-0.0050227,-0.06291647,0.04611228,-0.02385475,-0.03172981,-0.05905336,0.03078755,0.01398344,-0.02621399,-0.01018568,0.05905542,0.06164729,0.0053539,0.03074147,-0.02877808,0.0237607,-0.00758259,-0.06650287,0.01709829,-0.05382054,0.07893571,-0.01324863,-0.01657378,0.00914214,-0.03572785,0.02782689,-0.01520167,0.00940689,0.00425678,0.00487507,0.00879431,0.0134903,0.09903414,0.02937967,0.06587829,0.03084715,-0.00282589,0.03978253,-0.04869536,0.00141088,-0.01045092,-0.01253835,-0.0330594,0.05824404,0.07331457,0.06968252,0.01308382,0.04624789,-0.05730312,-0.00646095,-0.0032738,-0.04396046,0.01110817,-0.03967154,0.02467275,0.02995975,0.00549456,-0.24310248,0.06891651,0.02556052,0.00491366,-0.03387877,-0.03471347,0.04100695,-0.04410233,0.0287023,0.01591129,0.0896908,0.02892192,0.02174756,-0.04077824,0.00879747,0.00313467,0.01981612,0.02372623,0.08348306,0.01272479,0.04535708,0.02551737,0.2374471,0.01690156,-0.00774872,0.0058665,0.01387797,-0.00968112,0.00561894,0.03886841,0.02961859,0.01541159,0.05078114,0.02399259,-0.03187755,0.06163799,-0.01871897,0.05022683,0.0205321,-0.01592292,-0.06972885,0.02549899,-0.00247504,0.02174887,0.11516897,0.01018627,0.00113934,-0.08602034,0.01786671,0.04775534,-0.08219492,-0.02787739,-0.02227001,0.02938436,0.04532282,0.04030306,0.00289517,-0.03287695,0.01782524,-0.0344049,0.05160569,0.03808138,0.02272701,-0.01461472,0.02976205],"last_embed":{"hash":"1kouakj","tokens":339}}},"text":null,"length":0,"last_read":{"hash":"1kouakj","at":1753423603540},"key":"notes/saliu/Powerball, Mega Millions Strategies, Systems.md#Powerball, Mega Millions: Strategies, Systems#[Resources in Powerball Mega Millions, Software, Wheels, Systems](https://saliu.com/content/lottery.html)#{24}","lines":[176,185],"size":783,"outlinks":[{"title":"Software, systems, strategies for Powerball, Mega Millions are harder to find and obtain.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Read carefully these Powerball strategies, systems of skips and understand the lottery software.","target":"https://saliu.com/HLINE.gif","line":5},{"title":"Forums","target":"https://forums.saliu.com/","line":7},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":7},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":7},{"title":"Contents","target":"https://saliu.com/content/index.html","line":7},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":7},{"title":"Home","target":"https://saliu.com/index.htm","line":7},{"title":"Search","target":"https://saliu.com/Search.htm","line":7},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":7},{"title":"Thanks for visiting the site of lottery, lotto, Powerball, Mega Millions, software, systems.","target":"https://saliu.com/HLINE.gif","line":9}],"class_name":"SmartBlock"},
