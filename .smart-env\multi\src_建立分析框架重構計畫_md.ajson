"smart_sources:src/建立分析框架重構計畫.md": {"path":"src/建立分析框架重構計畫.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"80ms1a","at":1752538744931},"class_name":"SmartSource","last_import":{"mtime":1752538737788,"size":6597,"at":1752538744936,"hash":"80ms1a"},"blocks":{"#":[1,2],"#Gemini":[3,78],"#Gemini#{1}":[5,10],"#Gemini##核心理念：打造統一的分析引擎":[11,23],"#Gemini##核心理念：打造統一的分析引擎#{1}":[13,18],"#Gemini##核心理念：打造統一的分析引擎#{2}":[19,19],"#Gemini##核心理念：打造統一的分析引擎#{3}":[20,20],"#Gemini##核心理念：打造統一的分析引擎#{4}":[21,21],"#Gemini##核心理念：打造統一的分析引擎#{5}":[22,23],"#Gemini##行動計畫：分階段重構":[24,73],"#Gemini##行動計畫：分階段重構#{1}":[26,42],"#---frontmatter---":[28,41],"#Gemini##行動計畫：分階段重構#**第二階段：將現有功能逐一遷移至新引擎**":[43,67],"#Gemini##行動計畫：分階段重構#**第二階段：將現有功能逐一遷移至新引擎**#{1}":[45,48],"#Gemini##行動計畫：分階段重構#**第二階段：將現有功能逐一遷移至新引擎**#{2}":[49,52],"#Gemini##行動計畫：分階段重構#**第二階段：將現有功能逐一遷移至新引擎**#{3}":[53,56],"#Gemini##行動計畫：分階段重構#**第二階段：將現有功能逐一遷移至新引擎**#{4}":[57,60],"#Gemini##行動計畫：分階段重構#**第二階段：將現有功能逐一遷移至新引擎**#{5}":[61,65],"#Gemini##行動計畫：分階段重構#**第二階段：將現有功能逐一遷移至新引擎**#{6}":[66,67],"#Gemini##行動計畫：分階段重構#**第三階段：清理與優化**":[68,73],"#Gemini##行動計畫：分階段重構#**第三階段：清理與優化**#{1}":[70,70],"#Gemini##行動計畫：分階段重構#**第三階段：清理與優化**#{2}":[71,71],"#Gemini##行動計畫：分階段重構#**第三階段：清理與優化**#{3}":[72,73],"#Gemini##結論":[74,78],"#Gemini##結論#{1}":[76,78]},"outlinks":[{"title":"run_example_8_guide.md","target":"code-assist-path:e:\\obsidian\\Julia\\JuliaProject\\StandAlone\\run_example_8_guide.md \"e:\\obsidian\\Julia\\JuliaProject\\StandAlone\\run_example_8_guide.md\"","line":72}],"key":"src/建立分析框架重構計畫.md"},