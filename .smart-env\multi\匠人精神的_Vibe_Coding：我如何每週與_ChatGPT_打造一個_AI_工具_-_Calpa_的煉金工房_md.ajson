"smart_sources:匠人精神的 Vibe Coding：我如何每週與 ChatGPT 打造一個 AI 工具 - Calpa 的煉金工房.md": {"path":"匠人精神的 Vibe Coding：我如何每週與 ChatGPT 打造一個 AI 工具 - Calpa 的煉金工房.md","last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"fdee1adca2de1b7f07f85b89c05f535d50e95e9fccef155e210751820ec7cf31"}}},"last_read":{"hash":"fdee1adca2de1b7f07f85b89c05f535d50e95e9fccef155e210751820ec7cf31","at":1750403577462},"class_name":"SmartSource","last_import":{"mtime":1750381198520,"size":21180,"at":1750384454489,"hash":"fdee1adca2de1b7f07f85b89c05f535d50e95e9fccef155e210751820ec7cf31"},"blocks":{"#":[1,305]},"outlinks":[],"key":"匠人精神的 Vibe Coding：我如何每週與 ChatGPT 打造一個 AI 工具 - Calpa 的煉金工房.md"},
"smart_blocks:匠人精神的 Vibe Coding：我如何每週與 ChatGPT 打造一個 AI 工具 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"fdee1adca2de1b7f07f85b89c05f535d50e95e9fccef155e210751820ec7cf31"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"fdee1adca2de1b7f07f85b89c05f535d50e95e9fccef155e210751820ec7cf31","at":1750403577466},"key":"匠人精神的 Vibe Coding：我如何每週與 ChatGPT 打造一個 AI 工具 - Calpa 的煉金工房.md#","lines":[1,305],"size":8854,"outlinks":[],"class_name":"SmartBlock"},"smart_blocks:匠人精神的 Vibe Coding：我如何每週與 ChatGPT 打造一個 AI 工具 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"fdee1adca2de1b7f07f85b89c05f535d50e95e9fccef155e210751820ec7cf31"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"fdee1adca2de1b7f07f85b89c05f535d50e95e9fccef155e210751820ec7cf31","at":1750403577466},"key":"匠人精神的 Vibe Coding：我如何每週與 ChatGPT 打造一個 AI 工具 - Calpa 的煉金工房.md#","lines":[1,305],"size":8854,"outlinks":[],"class_name":"SmartBlock"},
