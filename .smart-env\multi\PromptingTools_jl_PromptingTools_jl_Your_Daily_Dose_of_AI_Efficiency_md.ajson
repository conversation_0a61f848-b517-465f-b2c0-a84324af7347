"smart_sources:PromptingTools.jl/PromptingTools.jl Your Daily Dose of AI Efficiency.md": {"path":"PromptingTools.jl/PromptingTools.jl Your Daily Dose of AI Efficiency.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07014158,0.01885728,-0.04345385,-0.02474168,-0.00001439,0.00349697,-0.02034608,0.06231578,0.06290009,-0.00988412,-0.00407973,-0.01270048,0.00696163,0.08037788,-0.00677147,-0.00202112,-0.03365398,-0.00769092,-0.08381991,-0.01335069,0.07670034,0.00219699,0.02463341,-0.04097588,-0.01759955,0.02861852,0.00430762,-0.03629676,-0.0211556,-0.22974323,0.04343319,-0.03090316,0.01178216,-0.00337438,-0.08216748,0.01506411,-0.00726474,0.0222233,0.00211234,-0.001461,0.02297241,0.01339623,0.00945683,-0.05664153,-0.01519221,-0.08121636,-0.04603897,0.03595356,-0.03126029,-0.02754077,0.01469928,-0.02018203,0.05728434,-0.02346874,0.01335249,-0.01478217,0.01069446,0.08186,0.07458451,0.00560291,0.00764208,0.02406602,-0.18521115,0.13588783,-0.05199595,0.00469684,-0.00069533,0.01187035,0.01852258,0.04983157,-0.01582178,0.00012695,0.01856901,0.02673869,0.01747482,-0.02482621,0.00191964,-0.01197619,0.03266543,-0.0540434,-0.0028666,-0.05911689,-0.04935471,-0.00859159,-0.03052502,0.01310831,-0.00299344,0.00802093,0.08542555,0.00965816,-0.02671457,-0.05293995,0.04713474,0.04271906,-0.05697987,-0.00627781,0.00048019,-0.0081973,-0.14644869,0.12523313,0.00176965,-0.01159204,-0.01903585,-0.0287845,0.03068735,-0.08274961,-0.04132058,-0.0174969,-0.01320226,0.00890539,-0.02213001,-0.05277053,0.02406463,-0.07637601,-0.02359943,0.03468163,0.07275371,-0.01786971,0.032895,-0.0119681,0.0007014,-0.00601249,0.03953568,0.04337347,0.03032643,0.03736177,0.04910793,0.04818217,0.0077829,0.05217637,0.06533798,0.02837293,-0.07892995,-0.02186261,0.01695639,0.03198065,0.02006525,-0.04809204,0.0235583,-0.04337151,0.01582777,-0.00482941,0.00868972,-0.09883881,0.01318046,0.0873758,0.00517334,-0.00789275,-0.01064638,-0.09851693,-0.01005321,0.05567292,-0.02148176,-0.0489919,0.0525725,0.07692802,0.09085033,0.08428012,0.00736792,0.00727545,-0.09208953,-0.0370249,-0.00800063,0.12128325,0.00775734,-0.07492403,-0.09441309,-0.02669158,-0.04710169,-0.01457113,0.01026186,0.04593846,-0.07672826,0.01829911,0.08386602,0.01061076,-0.04831885,0.04851274,0.01480869,0.01752278,0.04470202,-0.04960137,-0.00922857,-0.0094916,-0.00121092,-0.02364549,-0.01356521,-0.05517829,-0.01619797,-0.00266587,-0.09618159,0.05009637,0.0401452,0.00217433,-0.00686653,-0.00024677,0.01339767,-0.03907818,0.04141077,-0.01601774,0.0655464,-0.00083504,0.03461323,0.00782724,-0.00666466,0.01357506,0.07351524,-0.02678752,0.09927668,0.05534469,-0.0772282,-0.04168705,0.03594284,-0.02652942,-0.04434951,0.01801641,-0.01223183,-0.01542277,-0.02601559,0.0431334,0.01099728,0.02502448,-0.02895143,-0.223179,0.00293676,0.05736057,-0.07230361,0.09881397,-0.09259161,0.08125877,-0.07753249,-0.0060132,0.09090181,0.06117053,-0.07433109,0.01290144,-0.01571078,0.00957959,0.00065353,-0.01792817,0.01001564,-0.0470243,0.00163844,0.01387685,0.00246197,0.00998224,-0.08831193,0.01297272,-0.0197101,0.13629812,-0.00052512,0.03283399,-0.06623177,0.00922973,-0.0122443,0.04359039,-0.12161261,-0.01129314,-0.0086042,0.03506399,0.07004432,0.06023008,0.05230888,-0.03230628,0.01588792,-0.02702524,-0.09803083,-0.02713059,-0.01910318,-0.01299211,-0.04540541,-0.08800929,-0.01009612,0.01709316,-0.04828732,0.04974889,0.02417684,0.04084523,-0.0642994,-0.02254292,-0.05202717,-0.00248502,-0.0015674,0.04699222,0.0006402,-0.01494863,-0.01121468,0.04248289,0.0076256,0.00659201,-0.02147589,0.05144804,0.00727216,-0.02795806,0.13143399,0.00457204,0.07517364,0.03076064,0.02456016,-0.01864435,-0.05029901,0.01292806,-0.04020939,0.01297383,0.00700125,0.01966413,0.04937264,0.01489373,0.03902998,0.02927052,-0.02650445,0.07133475,0.01555746,-0.03551777,0.02660113,-0.01704323,-0.02092556,0.03600151,-0.03793464,-0.25823957,0.05795642,0.01357676,0.06030371,0.00380816,0.07427974,0.04457827,0.01292881,-0.02083552,0.05369068,-0.03896028,0.04880463,0.01453304,0.02791061,0.03296996,-0.01990383,0.0042382,-0.00566242,0.02593303,-0.05676799,0.02196315,0.01839915,0.21225461,-0.01466652,-0.00269964,0.03378147,-0.00510074,-0.05375598,0.12902348,-0.00779709,-0.0122587,0.02245282,0.0889502,-0.03734743,0.04095511,-0.00499156,-0.07686159,-0.02804582,0.01511245,0.02195604,-0.00672229,0.03904812,-0.00303379,0.01190054,0.02107669,-0.03230268,-0.02310866,-0.04974916,-0.0192319,0.07327118,-0.0109117,-0.00643744,-0.0457584,0.01275449,-0.00162596,-0.00490967,0.07968921,0.00049949,-0.06492316,0.00558006,0.04201776,-0.05688412,0.07118721,0.01131936,-0.05129203],"last_embed":{"hash":"cefb4ab15b7154e6379cc83b6a178320eb50d09af3805000cf7c3d889e1f016c","tokens":462}}},"last_read":{"hash":"cefb4ab15b7154e6379cc83b6a178320eb50d09af3805000cf7c3d889e1f016c","at":1745995220720},"class_name":"SmartSource2","outlinks":[{"title":"Ollama.ai","target":"https://ollama.ai/","line":685},{"title":"Setup Guide for Ollama","target":"https://github.com/svilupp/PromptingTools.jl?tab=readme-ov-file#setup-guide-for-ollama","line":718}],"blocks":{"#":[1,27],"#[ Info: Tokens: 31 @ Cost: $0.0 in 1.5 seconds --> Be in control of your spending!":[28,28],"#AIMessage(\"The capital of France is Paris.\")":[29,40],"#AIMessage(\"The capital of France is Paris.\")#{1}":[30,40],"#[ Info: Tokens: 32 @ Cost: $0.0001 in 0.5 seconds":[41,41],"#AIMessage(\"The capital of Spain is Madrid.\")":[42,49],"#AIMessage(\"The capital of Spain is Madrid.\")#{1}":[43,49],"#[ Info: Tokens: 74 @ Cost: $0.0001 in 1.3 seconds":[50,50],"#AIMessage(\"The capital of Spain is Madrid. And yes, the population of Madrid is larger than 1 million. As of 2020, the estimated population of Madrid is around 3.3 million people.\")":[51,201],"#AIMessage(\"The capital of Spain is Madrid. And yes, the population of Madrid is larger than 1 million. As of 2020, the estimated population of Madrid is around 3.3 million people.\")#{1}":[52,201],"#Will surface one specific template":[202,202],"#1-element Vector{AITemplateMetadata}:":[203,203],"#PromptingTools.AITemplateMetadata":[204,204],"#name: Symbol JuliaExpertAsk":[205,205],"#description: String \"For asking questions about Julia language. Placeholders: `ask`\"":[206,206],"#version: String \"1\"":[207,207],"#wordcount: Int64 237":[208,208],"#variables: Array{Symbol}((1,))":[209,209],"#system_preview: String \"You are a world-class Julia language programmer with the knowledge of the latest syntax. Your commun\"":[210,210],"#user_preview: String \"# Question\\n\\n{{ask}}\"":[211,211],"#source: String \"\"":[212,217],"#source: String \"\"#{1}":[213,217],"#2-element Vector{AITemplateMetadata}... -> more to come later!":[218,260],"#2-element Vector{AITemplateMetadata}... -> more to come later!#{1}":[219,260],"#calculate cosine distance between the two normalized embeddings as a simple dot product":[261,266],"#calculate cosine distance between the two normalized embeddings as a simple dot product#{1}":[262,266],"#true":[267,270],"#true#{1}":[268,270],"#unknown":[271,289],"#unknown#{1}":[272,289],"#Try also with:":[290,306],"#Try also with:#{1}":[291,306],"#Arguments":[307,315],"#Arguments#{1}":[308,308],"#Arguments#{2}":[309,309],"#Arguments#{3}":[310,315],"#Note that we provide the TYPE itself, not an instance of it!":[316,318],"#Note that we provide the TYPE itself, not an instance of it!#{1}":[317,318],"#CurrentWeather(\"Salt Lake City, UT\", celsius)":[319,332],"#CurrentWeather(\"Salt Lake City, UT\", celsius)#{1}":[320,332],"#2-element Vector{MyMeasurement}:":[333,333],"#MyMeasurement(30, 180, 80.0)":[334,334],"#MyMeasurement(19, 190, nothing)":[335,343],"#MyMeasurement(19, 190, nothing)#{1}":[336,343],"#[ Info: Tokens: 1141 @ Cost: \\$0.0117 in 2.2 seconds":[344,344],"#AIMessage(\"The image shows a logo consisting of the word \"julia\" written in lowercase\")":[345,347],"#AIMessage(\"The image shows a logo consisting of the word \"julia\" written in lowercase\")#{1}":[346,347],"#Screenshot of some SQL code":[348,351],"#Screenshot of some SQL code#{1}":[349,351],"#[ Info: Tokens: 362 @ Cost: \\$0.0045 in 2.5 seconds":[352,352],"#AIMessage(\"```sql":[353,353],"#update Orders <continue>":[354,374],"#update Orders <continue>#{1}":[355,374],"#API failure because of a non-existent model":[375,375],"#RetryConfig allows us to change the \"retry\" behaviour of any lazy call":[376,380],"#RetryConfig allows us to change the \"retry\" behaviour of any lazy call#{1}":[377,380],"#we ask to wait 2s between retries and retry 2 times (can be set in `config` in aicall as well)":[381,386],"#we ask to wait 2s between retries and retry 2 times (can be set in `config` in aicall as well)#{1}":[382,386],"#Notice that we ask for two samples (`n_samples=2`) at each attempt (to improve our chances).":[387,387],"#Both guesses are scored at each time step, and the best one is chosen for the next step.":[388,388],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!":[389,400],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#{1}":[390,395],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Check that the output is 1 word only, third argument is the feedback that will be provided if the condition fails":[396,396],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Notice: functions operate on `aicall` as the only argument. We can use utilities like `last_output` and `last_message` to access the last message and output in the conversation.":[397,400],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Notice: functions operate on `aicall` as the only argument. We can use utilities like `last_output` and `last_message` to access the last message and output in the conversation.#{1}":[398,400],"#Note: you could also use the do-syntax, eg,":[401,419],"#Note: you could also use the do-syntax, eg,#{1}":[402,419],"#[ Info: Tokens: 69 in 0.9 seconds":[420,420],"#AIMessage(\"Hello! How can I assist you today?\")":[421,449],"#AIMessage(\"Hello! How can I assist you today?\")#{1}":[422,449],"#Set your API key and the necessary base URL for the API":[450,460],"#Set your API key and the necessary base URL for the API#{1}":[451,460],"#cladeuh is alias for Claude 3 Haiku":[461,468],"#cladeuh is alias for Claude 3 Haiku#{1}":[462,468],"#cladeo is alias for Claude 3 Opus":[469,514],"#cladeo is alias for Claude 3 Opus#{1}":[470,514],"#2-element Vector{Dict{String, String}}:":[515,515],"#Dict(\"role\" => \"system\", \"content\" => \"Act as a helpful AI assistant. Provide only the information that is requested.\")":[516,516],"#Dict(\"role\" => \"user\", \"content\" => \"What is the capital of France?\")":[517,719],"#Dict(\"role\" => \"user\", \"content\" => \"What is the capital of France?\")#{1}":[518,719],"#---frontmatter---":[679,null]},"last_import":{"mtime":1712726324625,"size":39480,"at":1740449882778,"hash":"cefb4ab15b7154e6379cc83b6a178320eb50d09af3805000cf7c3d889e1f016c"},"key":"PromptingTools.jl/PromptingTools.jl Your Daily Dose of AI Efficiency.md"},