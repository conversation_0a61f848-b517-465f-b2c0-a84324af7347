#!/usr/bin/env julia

# 詳細調試測試
println("開始詳細調試測試...")

try
    using Dates
    include("src/WonderGridEngine.jl")
    using .WonderGridEngine
    
    println("✓ 模組載入成功")
    
    # 創建測試數據
    test_drawings = [
        WonderGridEngine.Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
        WonderGridEngine.Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
        WonderGridEngine.Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47]),
        WonderGridEngine.Drawing(4, :Lotto6_49, Date(2024, 1, 4), [4, 10, 17, 30, 37, 48]),
        WonderGridEngine.Drawing(5, :Lotto6_49, Date(2024, 1, 5), [1, 11, 18, 31, 38, 49])
    ]
    
    config = WonderGridEngine.WonderGridConfig(
        analysis_range = 10,
        top_pair_percentage = 0.25,
        key_number_strategy = :ffg,
        combination_limit = 10,
        enable_purge = true,
        enable_lie = false,
        game_type = :Lotto6_49
    )
    
    println("✓ 測試數據和配置創建成功")
    
    # 手動執行每個步驟來找出問題
    println("\n手動執行策略步驟...")
    
    # 步驟 1
    println("步驟 1: 計算配對統計...")
    pair_statistics = WonderGridEngine.calculate_advanced_pair_statistics(test_drawings, config)
    println("✓ 配對統計計算完成")
    
    # 步驟 2
    println("步驟 2: 選擇關鍵號碼...")
    key_result = WonderGridEngine.select_key_number(test_drawings, config.key_number_strategy)
    key_number = key_result.number
    println("✓ 關鍵號碼選擇完成: $key_number")
    
    # 步驟 3
    println("步驟 3: 篩選頂級配對...")
    top_pairs = WonderGridEngine.select_top_pairs_advanced(pair_statistics, key_number, config)
    println("✓ 頂級配對篩選完成，選出 $(length(top_pairs)) 個配對")
    
    # 步驟 4
    println("步驟 4: 生成組合...")
    game_config = WonderGridEngine.GAME_CONFIGURATIONS[:Lotto6_49]
    combinations = WonderGridEngine.generate_wonder_combinations(key_number, top_pairs, config, game_config)
    println("✓ 組合生成完成，生成 $(length(combinations)) 個組合")
    
    # 步驟 5
    println("步驟 5: 計算效率指標...")
    combinations_vectors = [combo.numbers for combo in combinations]
    println("  轉換組合向量完成")
    
    println("  調用 calculate_efficiency_metrics...")
    efficiency_metrics_struct = WonderGridEngine.calculate_efficiency_metrics(combinations_vectors, test_drawings, config, pair_statistics)
    println("  ✓ 效率指標計算完成")
    
    println("  轉換為字典...")
    efficiency_metrics = WonderGridEngine.convert_efficiency_metrics_to_dict(efficiency_metrics_struct)
    println("  ✓ 字典轉換完成")
    
    # 步驟 6
    println("步驟 6: 創建結果...")
    top_pairs_numbers = WonderGridEngine.extract_partner_numbers(key_number, top_pairs)
    println("  ✓ 配對夥伴號碼提取完成")
    
    println("  創建 WonderGridResult...")
    result = WonderGridEngine.WonderGridResult(
        key_number, top_pairs_numbers, combinations_vectors,
        efficiency_metrics, 1.0, config, pair_statistics
    )
    println("  ✓ 結果創建完成")
    
    println("\n✓ 所有步驟手動執行成功！")
    println("  - 關鍵號碼: $(result.key_number)")
    println("  - 頂級配對數量: $(length(result.top_pairs))")
    println("  - 生成組合數量: $(length(result.combinations))")
    
catch e
    println("✗ 測試失敗: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

println("\n詳細調試測試完成")
