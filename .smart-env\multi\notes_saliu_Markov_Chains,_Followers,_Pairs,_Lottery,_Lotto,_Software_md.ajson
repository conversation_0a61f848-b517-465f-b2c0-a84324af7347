"smart_sources:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md": {"path":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14855289,-0.04828813,-0.08685124,-0.00892164,-0.0443534,0.06402148,-0.01531054,0.01767327,0.06171016,0.00767055,0.02197254,-0.04967416,0.07939756,0.04077176,0.00498012,-0.01987525,-0.05795887,0.02448272,-0.04918134,-0.03218208,0.05230238,-0.08750285,-0.0396717,-0.07199381,0.04257004,0.05429487,0.00975596,-0.05990998,-0.00765366,-0.20283231,0.02448457,-0.02174345,0.03007458,-0.05467706,-0.06023943,0.00800822,-0.01256432,0.03951,-0.03286202,0.0238487,-0.00144906,0.01966973,0.01285916,-0.03636892,0.02786095,-0.06007044,-0.00955282,-0.02799595,-0.02897094,-0.02199829,-0.10942891,-0.01057161,0.01403822,0.02416536,0.08117177,0.03547608,0.0358209,0.07202169,0.00619726,0.01858338,0.0384252,0.07868442,-0.18056831,0.05263497,-0.00009846,0.03581675,-0.01931565,0.00533246,0.02711623,0.04872475,0.04251674,0.0256359,-0.03258777,0.04593435,0.03756303,-0.01937902,0.0022672,-0.02976916,-0.04021289,-0.01261438,-0.03714983,-0.02993259,-0.02175356,-0.01940919,-0.04319926,0.06307366,0.06663811,-0.01188484,-0.00181197,-0.04593369,-0.02414967,0.04162177,0.05325131,0.05344136,0.01007571,0.02643239,0.05246339,-0.00802097,0.04716558,0.11120199,0.01190608,0.02575936,0.00009788,0.02695727,0.06909143,-0.0190531,-0.00922635,-0.05429007,0.01419682,0.02127487,0.04552711,-0.0039985,0.0447781,-0.0906693,-0.0128336,-0.00349685,0.01094326,0.06098712,0.01568821,0.03233989,-0.02689555,-0.00200133,0.06436409,0.02169583,-0.01568959,0.04373188,-0.0014687,0.0731428,0.03994533,0.01380913,0.04721588,0.03445735,-0.12005972,-0.00444857,-0.01345826,-0.02232473,-0.01687656,-0.08601214,0.00087618,0.00688197,-0.01327705,-0.02178047,0.0469113,-0.09143455,-0.03532145,0.10325204,0.01583944,-0.00284412,-0.00411269,-0.00959038,0.01810777,0.00092978,-0.01438322,-0.06500338,0.00816446,0.02436272,0.07409498,0.06436638,-0.00119839,0.02708885,-0.01393641,-0.05722789,-0.03238356,0.18047921,0.00589278,-0.11972781,0.01162797,0.05210288,-0.01149736,-0.06329764,0.03934961,0.06127102,-0.05925394,0.05519308,0.06875531,-0.03811593,-0.09885613,-0.02994799,0.02636274,0.00673174,-0.01962694,-0.04461198,-0.04771331,0.00061173,-0.0439094,-0.06112238,0.01504242,-0.0065905,0.00033655,0.04806042,0.02692696,-0.02469615,-0.01571759,-0.02471503,-0.03931008,-0.03183892,-0.03689378,-0.03072416,0.03718033,-0.048048,-0.05766824,-0.0019081,-0.00856899,0.0131624,-0.06021671,-0.01058476,-0.01487591,-0.02495942,0.03207384,0.01708474,-0.00447069,0.02112583,-0.00699944,0.06187195,-0.06061333,0.01219248,0.02401245,0.03269923,0.03497203,0.00654767,-0.02260611,0.00595924,-0.07805277,-0.18354823,-0.03007836,-0.04387603,0.02786094,0.02386849,-0.03139231,-0.00576618,-0.04984072,0.03440737,0.05996189,0.10243969,-0.06911463,-0.01234281,-0.00935642,0.01556998,0.04041121,-0.08475706,0.00405369,-0.02073316,0.06079108,-0.02181651,0.03062508,-0.0586716,-0.08995652,0.03284356,-0.01979269,0.12425593,0.0763184,-0.0395771,0.02501112,0.04423624,0.02693365,-0.06301708,-0.07933224,-0.00133982,0.03563596,-0.00212534,0.0053809,-0.02516647,-0.03128037,-0.06201843,0.03414752,0.03029869,-0.06472878,-0.03737387,-0.00601952,0.00462904,0.01900796,0.00022832,0.04008482,-0.01480015,0.02824552,0.04062355,-0.00443601,0.01786307,-0.03770515,-0.0483495,-0.0253874,-0.03308628,0.02999362,0.00947835,-0.04436991,0.03335116,-0.00280616,0.0374781,-0.02844779,-0.01423184,-0.04070656,-0.00103178,0.00741945,-0.00712745,0.11027202,0.02970923,0.02257666,-0.00319445,-0.00957185,-0.00329409,-0.09435482,-0.00747879,-0.04085167,0.01056101,-0.07329688,0.05043068,0.08834441,0.06750543,-0.00255041,0.08155721,0.05041926,0.02443177,0.03766064,0.010257,-0.00016599,0.00722309,0.01256364,0.06912,0.04091641,-0.28097486,0.02762103,-0.00386005,0.07034785,0.00321707,-0.00443145,0.06059673,-0.03386889,0.01602785,-0.09915221,0.05366587,0.05348215,0.04388768,-0.03374727,-0.03033321,0.02530514,-0.00182134,-0.0450457,0.04090592,0.03007654,0.02922151,-0.00365347,0.2351741,-0.02382376,0.03173779,0.00836281,-0.01491735,0.01672579,0.0066931,0.02502449,-0.01905859,0.00916109,0.06932995,-0.03048545,0.00750327,0.05189343,-0.01255229,0.01562729,-0.00417638,0.01087492,-0.04761967,-0.02489389,-0.0369976,0.02007902,0.14915681,0.03549873,0.01236221,-0.08989358,0.034397,0.09733533,-0.06345419,-0.09898084,-0.05185964,0.0199277,-0.00919583,0.02345353,0.01453185,-0.02705854,-0.00458259,0.0053743,0.03279101,-0.04540956,0.05170584,0.03270105,-0.01023647],"last_embed":{"hash":"15p8fq8","tokens":477}}},"last_read":{"hash":"15p8fq8","at":1753423554043},"class_name":"SmartSource","last_import":{"mtime":1753363611993,"size":24934,"at":1753423416500,"hash":"15p8fq8"},"blocks":{"#---frontmatter---":[1,6],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software":[8,223],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#{1}":[10,15],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software##I. [Introduction to _Markov Chains_, Applications to Random Events, Lottery](https://saliu.com/markov-chains-lottery.html#MarkovLottery)":[16,24],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software##I. [Introduction to _Markov Chains_, Applications to Random Events, Lottery](https://saliu.com/markov-chains-lottery.html#MarkovLottery)#{1}":[17,24],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>1. Introduction to <i>Markov Chains</i>, Applications to Random Events, Lottery</u>":[25,46],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>1. Introduction to <i>Markov Chains</i>, Applications to Random Events, Lottery</u>#{1}":[27,46],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>":[47,64],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{1}":[49,58],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{2}":[59,60],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{3}":[61,62],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{4}":[63,64],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>3. Reports in <i>Markov Chains</i> Software</u>":[65,151],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>3. Reports in <i>Markov Chains</i> Software</u>#{1}":[67,68],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>3. Reports in <i>Markov Chains</i> Software</u>#{2}":[69,151],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>":[152,178],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{1}":[154,155],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{2}":[156,157],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{3}":[158,161],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{4}":[162,163],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{5}":[164,165],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{6}":[166,167],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{7}":[168,169],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{8}":[170,171],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{9}":[172,173],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{10}":[174,174],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{11}":[175,176],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{12}":[177,178],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>5. Strategies for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>":[179,190],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>5. Strategies for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{1}":[181,190],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#[<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>](https://saliu.com/content/lottery.html)":[191,223],"#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#[<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>](https://saliu.com/content/lottery.html)#{1}":[193,223]},"outlinks":[{"title":"Read an introduction to Markov Chains mathematics, randomness, random events, prediction.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Introduction to _Markov Chains_, Applications to Random Events, Lottery","target":"https://saliu.com/markov-chains-lottery.html#MarkovLottery","line":16},{"title":"Ion Saliu's Software for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Software","line":17},{"title":"Reports in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Reports","line":18},{"title":"Generate Combinations in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Combinations","line":19},{"title":"Strategies for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Strategies","line":20},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/markov-chains-lottery.html#Resources","line":21},{"title":"Access the best resources in Markov chain applications to lottery, lotto, random events.","target":"https://saliu.com/HLINE.gif","line":23},{"title":"_**The Universe itself is random beyond mathematical doubt.**_","target":"https://saliu.com/bbs/messages/683.html","line":29},{"title":"_**perfect shape**_","target":"https://saliu.com/bbs/messages/636.html","line":33},{"title":"_**Fundamental Formula of Gambling: Degree of Certainty**_","target":"https://saliu.com/Saliu2.htm","line":35},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Program**_","target":"https://saliu.com/Markov_Chains.html","line":41},{"title":"Ion Saliu's improved Markov-chains software is based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":45},{"title":"The ultimate lottery software has Markov Chains specialized programs.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":59},{"title":"The Markov chains lotto software is based on followers and lottery pairings as well.","target":"https://saliu.com/images/markov-pick-lottery.gif","line":63},{"title":"Markov chains, followers, pairings apply also to pick-3, pick 4 daily lottery games.","target":"https://saliu.com/images/markov-report.gif","line":71},{"title":"All lotto numbers are listed in Markov chains as followers and pairings.","target":"https://saliu.com/images/markov-chains-lotto.gif","line":75},{"title":"The special Markov software offers defaults for lotto draws analyses.","target":"https://saliu.com/images/markov-draws-analysis.gif","line":150},{"title":"Markov chains generates lottery combinations from hot and cold numbers.","target":"https://saliu.com/images/markov-hot-numbers.gif","line":160},{"title":"Generate Markov chains combinations from lotto pairs with a pivot number.","target":"https://saliu.com/images/markov-pairs-pivot.gif","line":164},{"title":"Generate Markov chains combinations from lotto pairs without pivot numbers.","target":"https://saliu.com/images/markov-pairs-pivotless.gif","line":168},{"title":"Instead of Markov chains number follower, use lottery pairs to generate combinations.","target":"https://saliu.com/images/markov-chains-pairs.gif","line":172},{"title":"The traditional Markov chains software generates random combinations from number followers.","target":"https://saliu.com/images/markov-chains-followers.gif","line":177},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":187},{"title":"Generate thousands of random lotto combinations from Markov chains follower list.","target":"https://saliu.com/HLINE.gif","line":189},{"title":"<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>","target":"https://saliu.com/content/lottery.html","line":191},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":195},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":197},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":199},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":201},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":202},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":203},{"title":"_**Lottery <u>Skip</u> Systems, Software**_","target":"https://saliu.com/skip-strategy.html","line":204},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":205},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":206},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":207},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":208},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":209},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":210},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":211},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":212},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":213},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":214},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":215},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":216},{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":218},{"title":"Forums","target":"https://forums.saliu.com/","line":220},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":220},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":220},{"title":"Contents","target":"https://saliu.com/content/index.html","line":220},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":220},{"title":"Home","target":"https://saliu.com/index.htm","line":220},{"title":"Search","target":"https://saliu.com/Search.htm","line":220},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":220},{"title":"Read the most thorough article on mathematics of Markov chains in lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":222}],"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md","metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["Markov Chains","lottery","lotto","mathematics","software","formula","probability","random","algorithm","number","followers","pairs"],"source":"https://saliu.com/markov-chains-lottery.html","author":null}},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.15288746,-0.06581823,-0.07899364,-0.01139524,-0.03892994,0.04721481,-0.00716409,0.03646968,0.07479524,0.00499322,0.02978706,-0.04172205,0.06560242,0.04662623,0.01022719,0.00568603,-0.05143815,0.00561889,-0.04301134,-0.02690837,0.06266636,-0.08616047,-0.0577038,-0.07690318,0.04837687,0.04342162,0.00624724,-0.07061479,-0.00465511,-0.16488516,0.01024306,-0.01735257,0.02825889,-0.0440768,-0.0449211,-0.00982397,-0.02176755,0.04052695,-0.01462646,0.0251903,0.00364266,0.03763763,-0.02108676,-0.03389386,0.0225267,-0.04545554,-0.02219222,-0.02940054,-0.03315724,-0.01187584,-0.08745128,-0.00247914,0.01654686,0.04879864,0.07582959,0.00799979,0.0439168,0.04572996,0.02171172,0.02665338,0.03515012,0.04853806,-0.17594078,0.05851015,-0.00377708,0.03131961,-0.02413178,-0.00712107,0.00277348,0.06165416,0.04961271,0.03365901,-0.03490932,0.05909743,0.04961167,-0.01605153,-0.00758305,-0.03853508,-0.03858232,-0.00673225,-0.03611229,-0.0391537,-0.04012693,-0.03777878,-0.03781032,0.04441465,0.06855533,0.00830189,0.00192271,-0.03139695,-0.02359687,0.04676418,0.07980105,0.0544682,-0.01599639,0.03315914,0.04897521,0.00397758,0.04811831,0.11971191,-0.01656917,0.03542739,0.01406063,0.02250064,0.06252339,-0.01750104,-0.00957567,-0.04995506,-0.01311107,0.01830539,0.02909945,0.00766749,0.06673063,-0.10412792,0.01291525,-0.01719495,0.03912163,0.06642189,0.0174836,0.01669951,-0.03432382,-0.00904338,0.06273205,0.01750819,-0.0019265,0.05949097,-0.00075117,0.07742022,0.04601267,0.02847043,0.0511852,0.02066379,-0.10331699,0.00602257,-0.00020287,-0.01489196,0.00281911,-0.08787198,0.0122755,-0.01097325,-0.00841834,-0.00927246,0.02122574,-0.0997192,-0.02284179,0.113396,0.01551806,-0.00073121,0.00642359,-0.0156289,0.00867492,-0.00504007,0.00360861,-0.07201903,0.00938791,0.02578254,0.0936204,0.05813364,0.00737435,0.02398082,0.00513119,-0.06623883,-0.03889529,0.18097264,-0.00556005,-0.13151409,0.00297132,0.04393654,0.00261881,-0.06469658,0.04398084,0.06182958,-0.05233287,0.06201158,0.08106878,-0.02924596,-0.06111164,-0.01452099,0.01900445,-0.00482357,-0.03188251,-0.03524687,-0.05777478,0.00714614,-0.05280076,-0.04060496,-0.00854965,0.00573408,0.00869998,0.04229486,0.03706357,-0.05038039,-0.03120053,-0.01114311,-0.03854336,-0.03616375,-0.03749889,-0.03425569,0.02427603,-0.05247452,-0.04358434,-0.00741065,-0.02544344,-0.00501503,-0.04360207,-0.01226793,-0.01180372,-0.0138627,0.02510372,0.02329869,0.00432644,0.00879371,0.01085834,0.05488464,-0.06796928,-0.00237983,0.00463015,0.02295334,0.03348472,-0.00267441,-0.04329024,-0.00473323,-0.07718813,-0.19866173,-0.01026756,-0.02743124,0.02322835,0.0073609,-0.03528373,-0.01340981,-0.04936342,0.03440272,0.08799654,0.08395363,-0.06798554,-0.01086355,-0.04745093,0.00272347,0.0344181,-0.07429113,-0.00888142,-0.01098233,0.05940431,-0.01666323,0.02439116,-0.0666238,-0.10183949,0.03922216,-0.02154341,0.13764325,0.09931777,-0.04204567,0.01649338,0.05764622,0.02634662,-0.055332,-0.08094458,-0.01131909,0.02214475,-0.01894623,0.01164905,-0.02738314,-0.05220609,-0.07341805,0.02734851,0.02783901,-0.06802224,-0.01973111,-0.01836869,0.00682757,0.0069948,-0.00431108,0.02583566,-0.02185339,0.01077071,0.02712103,-0.00834658,0.02647204,-0.03566529,-0.06018639,-0.03944815,-0.02915071,0.02581729,0.00570805,-0.04299452,0.03261604,-0.00398903,0.00976031,-0.01441296,-0.00934837,-0.05575281,-0.01828254,-0.00904131,0.01303194,0.11409809,0.03272701,0.00050077,-0.01320191,-0.01607415,-0.01828822,-0.05936252,-0.01069485,-0.04299789,0.02657607,-0.06917296,0.04619084,0.09002077,0.06211215,0.00655967,0.06748343,0.05461604,0.01980254,0.03628591,0.02377679,0.0059091,-0.00530605,0.00211724,0.07828997,0.00661961,-0.28085262,0.04529754,0.02109119,0.07465727,0.01516418,0.01203478,0.04503629,-0.01665448,-0.01589303,-0.0688911,0.0664562,0.07350407,0.04311997,-0.00958611,-0.03089778,0.0152826,-0.0086249,-0.0314597,0.04254055,0.03364071,0.02650432,-0.00925998,0.2507112,-0.00785519,0.02974356,0.00833047,-0.00443832,0.0269846,0.00774032,0.02588611,-0.01702529,0.01996402,0.06221037,-0.02077779,0.02585436,0.04357574,-0.01749104,0.01546416,-0.01465783,0.01374761,-0.0640455,-0.02364915,-0.03142603,0.03448414,0.15109283,0.05535277,-0.00218456,-0.08383384,0.01000794,0.07933753,-0.06429219,-0.09717111,-0.04788949,0.01854079,-0.01109532,0.0313274,0.02774779,-0.03527983,-0.00955708,-0.00192275,0.01545714,-0.04641317,0.05872959,0.02084749,-0.00861495],"last_embed":{"hash":"10k0eth","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"10k0eth","at":1753423550227},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#---frontmatter---","lines":[1,6],"size":229,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1356333,-0.03848709,-0.08544005,-0.01659087,-0.04822969,0.05684343,0.00674069,0.00637705,0.06106735,-0.00388187,0.03393517,-0.03768343,0.06531969,0.03856513,0.00030311,-0.01330633,-0.06048688,0.0177632,-0.06490903,-0.03522044,0.05678042,-0.07120674,-0.04969521,-0.07000767,0.04696374,0.05295911,0.01325631,-0.07037333,-0.00819661,-0.21383943,0.03516752,-0.01316913,0.04193695,-0.03975483,-0.06560872,0.00054013,-0.00192228,0.02861205,-0.04887013,0.03282828,0.00184652,0.02222344,0.01373974,-0.03069519,0.02460302,-0.05792058,-0.00302612,-0.01975154,-0.03401154,-0.01286189,-0.10046902,0.00198528,0.00556562,0.0079054,0.07089863,0.04158149,0.03905979,0.08980621,0.00096749,0.01734625,0.0455074,0.07828051,-0.17401312,0.05129784,0.01188979,0.03035675,-0.01298801,0.01459755,0.03162296,0.04434841,0.04395525,0.02994161,-0.03751421,0.05236715,0.03881808,-0.03978694,0.0025056,-0.02234407,-0.03897648,0.00033529,-0.02856102,-0.02999925,-0.00435667,-0.01617974,-0.05451567,0.06158868,0.06120674,-0.02764067,0.00925061,-0.04761619,-0.02605032,0.04223047,0.03600099,0.04743529,0.0381084,0.02449706,0.0616873,-0.01518238,0.05565468,0.10386322,0.02397628,0.035615,-0.00980812,0.03737266,0.06024147,-0.01116563,-0.01742503,-0.06428821,0.0116376,0.02556855,0.05060841,-0.00857622,0.04002531,-0.07628598,-0.01727524,-0.00784417,0.00341348,0.06612677,0.01910769,0.02378364,-0.02525468,-0.00623657,0.05099615,0.0131512,-0.01827825,0.02208078,-0.00177223,0.07710902,0.03908344,0.00387502,0.0464755,0.02766054,-0.13253544,-0.01126611,-0.01525135,-0.02423201,-0.0122838,-0.07278714,-0.00476266,0.01969537,-0.00787407,-0.02539815,0.05738105,-0.09274568,-0.04203462,0.10072383,0.02580005,-0.0076093,-0.01160282,-0.00019237,0.01900219,0.00235882,-0.03600768,-0.06049649,0.01318633,0.02250765,0.06869304,0.05550636,0.00090506,0.03233105,-0.03523316,-0.04807948,-0.02237495,0.15409882,0.0009368,-0.11672036,0.01733402,0.04710893,-0.01770555,-0.07534169,0.02497085,0.055341,-0.05501903,0.05620671,0.05651962,-0.03442723,-0.13730101,-0.03773483,0.02084917,0.00805486,-0.01831771,-0.03714962,-0.04012317,0.0182724,-0.03152808,-0.06608444,0.02733081,-0.0017913,0.00351608,0.04617112,0.02783761,-0.01535074,-0.00747018,-0.02311639,-0.03852063,-0.02699684,-0.02742247,-0.04099424,0.04663912,-0.03332914,-0.07796541,0.00530513,-0.00604588,0.02300835,-0.07187793,-0.01219767,-0.0160758,-0.0192403,0.03320535,0.03066533,-0.00886819,0.02163168,-0.02143212,0.05886583,-0.05530198,0.02405512,0.03094103,0.042158,0.05136419,0.0018442,-0.00875071,0.01029352,-0.07977711,-0.18064371,-0.03452957,-0.03678778,0.02265738,0.03784973,-0.03055339,-0.00617004,-0.043655,0.01843202,0.05406462,0.09795885,-0.07407328,-0.00629208,-0.00317122,0.00801688,0.04019333,-0.10695805,-0.00495258,-0.03786839,0.05812343,-0.02668978,0.03848131,-0.0430517,-0.08934215,0.02739818,-0.01817436,0.11678326,0.05551163,-0.0299887,0.04633627,0.03535634,0.02462034,-0.06604709,-0.07603432,0.00091486,0.03836907,-0.00083625,-0.01533984,-0.01432757,-0.01393527,-0.06222288,0.03224814,0.02995924,-0.06966677,-0.04963245,0.00290391,0.00648945,0.01526138,-0.00526679,0.03624567,0.00098962,0.01144792,0.05319212,0.00854147,0.0187938,-0.04333,-0.03932365,-0.01120346,-0.02144731,0.04869375,0.01027657,-0.03278976,0.03322185,-0.00772077,0.048784,-0.02312952,-0.0098579,-0.05478165,-0.00809374,0.00037243,-0.02728613,0.12520392,0.01891631,0.02914898,-0.00254522,0.00667579,-0.00386621,-0.09544603,-0.00964699,-0.03414099,0.01867834,-0.07402378,0.03913404,0.09239495,0.04200272,-0.01254588,0.08748858,0.04559324,0.01486524,0.0233371,0.01236428,0.00252098,0.01210716,0.01227254,0.04631699,0.03793399,-0.27322492,0.03676802,-0.03150108,0.08468682,-0.0027854,0.00242206,0.0549339,-0.03178798,0.02077714,-0.09787948,0.05816055,0.05874066,0.05318417,-0.04892551,-0.02591297,0.02109836,0.00765514,-0.06185833,0.04352974,0.01293933,0.04570704,0.0158698,0.2382839,-0.03033139,0.03236382,0.01000199,-0.01825784,0.0120596,-0.00619504,0.00968695,-0.02075191,-0.00289887,0.06905273,-0.04884051,-0.00142659,0.04042114,-0.01053467,0.01669312,0.00276216,0.00551613,-0.04589563,-0.01908398,-0.03169335,-0.00201805,0.14876354,0.0341204,0.01057766,-0.09883758,0.04447095,0.1169076,-0.05532546,-0.09190711,-0.048931,0.01054215,-0.00201807,0.01781281,0.00432362,-0.01247151,-0.00044182,0.01403667,0.03892399,-0.02819013,0.04975657,0.03363766,-0.00847692],"last_embed":{"hash":"kr7kej","tokens":490}}},"text":null,"length":0,"last_read":{"hash":"kr7kej","at":1753423550275},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software","lines":[8,223],"size":24689,"outlinks":[{"title":"Read an introduction to Markov Chains mathematics, randomness, random events, prediction.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"Introduction to _Markov Chains_, Applications to Random Events, Lottery","target":"https://saliu.com/markov-chains-lottery.html#MarkovLottery","line":9},{"title":"Ion Saliu's Software for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Software","line":10},{"title":"Reports in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Reports","line":11},{"title":"Generate Combinations in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Combinations","line":12},{"title":"Strategies for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Strategies","line":13},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/markov-chains-lottery.html#Resources","line":14},{"title":"Access the best resources in Markov chain applications to lottery, lotto, random events.","target":"https://saliu.com/HLINE.gif","line":16},{"title":"_**The Universe itself is random beyond mathematical doubt.**_","target":"https://saliu.com/bbs/messages/683.html","line":22},{"title":"_**perfect shape**_","target":"https://saliu.com/bbs/messages/636.html","line":26},{"title":"_**Fundamental Formula of Gambling: Degree of Certainty**_","target":"https://saliu.com/Saliu2.htm","line":28},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Program**_","target":"https://saliu.com/Markov_Chains.html","line":34},{"title":"Ion Saliu's improved Markov-chains software is based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":38},{"title":"The ultimate lottery software has Markov Chains specialized programs.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":52},{"title":"The Markov chains lotto software is based on followers and lottery pairings as well.","target":"https://saliu.com/images/markov-pick-lottery.gif","line":56},{"title":"Markov chains, followers, pairings apply also to pick-3, pick 4 daily lottery games.","target":"https://saliu.com/images/markov-report.gif","line":64},{"title":"All lotto numbers are listed in Markov chains as followers and pairings.","target":"https://saliu.com/images/markov-chains-lotto.gif","line":68},{"title":"The special Markov software offers defaults for lotto draws analyses.","target":"https://saliu.com/images/markov-draws-analysis.gif","line":143},{"title":"Markov chains generates lottery combinations from hot and cold numbers.","target":"https://saliu.com/images/markov-hot-numbers.gif","line":153},{"title":"Generate Markov chains combinations from lotto pairs with a pivot number.","target":"https://saliu.com/images/markov-pairs-pivot.gif","line":157},{"title":"Generate Markov chains combinations from lotto pairs without pivot numbers.","target":"https://saliu.com/images/markov-pairs-pivotless.gif","line":161},{"title":"Instead of Markov chains number follower, use lottery pairs to generate combinations.","target":"https://saliu.com/images/markov-chains-pairs.gif","line":165},{"title":"The traditional Markov chains software generates random combinations from number followers.","target":"https://saliu.com/images/markov-chains-followers.gif","line":170},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":180},{"title":"Generate thousands of random lotto combinations from Markov chains follower list.","target":"https://saliu.com/HLINE.gif","line":182},{"title":"<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>","target":"https://saliu.com/content/lottery.html","line":184},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":188},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":190},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":192},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":194},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":195},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":196},{"title":"_**Lottery <u>Skip</u> Systems, Software**_","target":"https://saliu.com/skip-strategy.html","line":197},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":198},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":199},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":200},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":201},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":202},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":203},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":204},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":205},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":206},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":207},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":208},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":209},{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":211},{"title":"Forums","target":"https://forums.saliu.com/","line":213},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":213},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":213},{"title":"Contents","target":"https://saliu.com/content/index.html","line":213},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":213},{"title":"Home","target":"https://saliu.com/index.htm","line":213},{"title":"Search","target":"https://saliu.com/Search.htm","line":213},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":213},{"title":"Read the most thorough article on mathematics of Markov chains in lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":215}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13043864,-0.04485222,-0.0645583,-0.01595919,-0.0573564,0.056778,0.02809422,0.00825967,0.06159437,-0.01420329,0.04637795,-0.03633795,0.07134751,0.03712067,-0.0097257,-0.02267842,-0.05579547,0.01757762,-0.06215731,-0.03270259,0.07824303,-0.09824706,-0.04524183,-0.07652712,0.05317423,0.05443199,0.00849887,-0.06143143,-0.00742439,-0.17752597,0.01936557,-0.00865093,0.05076433,-0.04933156,-0.05409542,-0.01550641,-0.01196843,0.04530238,-0.05144652,0.02691679,0.00740738,0.00644697,0.01251051,-0.04185089,0.02840951,-0.06107584,0.01154811,-0.01929368,-0.02343807,-0.0192391,-0.09493652,0.00321758,0.01234366,0.02781556,0.07531085,0.02900249,0.03360701,0.07757092,0.01832392,0.02187845,0.04906146,0.04862809,-0.16962358,0.03076266,-0.0150288,0.03665078,-0.01517049,-0.01277849,0.01789307,0.0409405,0.06309184,0.03078665,-0.0363146,0.04413936,0.04099578,-0.0248857,0.00024408,-0.00987328,-0.03564845,0.00109942,-0.03357079,-0.04874732,-0.00375364,-0.0156112,-0.04913864,0.06272306,0.0689322,-0.01973853,0.00527558,-0.05964915,-0.0266938,0.03665409,0.03149585,0.05325977,0.01732552,0.02147159,0.04726978,-0.02926929,0.05899329,0.13657372,0.01072977,0.02894925,0.00546333,0.0423492,0.05357176,-0.01013285,-0.01670335,-0.04976171,0.01015593,0.02106691,0.03954312,-0.00018893,0.04709861,-0.08712476,-0.01213266,-0.0179206,0.02259242,0.06216822,0.0195199,0.02867079,-0.02391334,-0.00593077,0.06048438,0.00306042,-0.01498889,0.0482702,0.00561461,0.07376025,0.04481376,0.01210068,0.03981312,0.04919772,-0.11915115,-0.00316407,-0.01597314,-0.00711106,-0.00393089,-0.09360573,-0.01216651,0.00986286,-0.01118749,0.01180441,0.04778577,-0.08615307,-0.03839854,0.09494647,0.01196691,-0.00324137,0.00706492,-0.00383074,0.02382459,-0.0125593,-0.01353889,-0.0789106,-0.00171636,0.0157705,0.07499392,0.060678,-0.00681293,0.03403937,-0.01931381,-0.04773384,-0.03409233,0.17187768,-0.00052498,-0.10403359,0.02188645,0.04258632,-0.00787797,-0.07825822,0.03397353,0.06337974,-0.07997265,0.05604801,0.05821611,-0.02333787,-0.0948006,-0.04355212,0.00491994,-0.00556864,-0.03059697,-0.05257856,-0.03737842,0.00629427,-0.03867337,-0.06754459,0.01836148,-0.00967085,0.00553855,0.06792796,0.04521735,0.01032193,-0.02819577,-0.02162725,-0.02561437,-0.0494091,-0.04372691,-0.02398283,0.01976857,-0.04634812,-0.06798203,0.00664783,-0.0093138,0.02117606,-0.0748572,-0.00801783,-0.0164896,-0.00127336,0.02924537,0.0182395,-0.01137418,0.0217263,-0.01642764,0.05387491,-0.04776,0.00928254,0.02876232,0.03325028,0.02138702,0.01221424,-0.01918935,-0.00473768,-0.07295927,-0.18906821,-0.04090843,-0.03067211,0.02368392,0.02358147,-0.04074791,-0.00304549,-0.04913524,0.03485653,0.05909876,0.1066684,-0.08032034,0.00189496,0.00986419,-0.00531897,0.03901473,-0.0848588,-0.01460125,-0.01543316,0.05414987,-0.02703504,0.02999373,-0.06347973,-0.09921697,0.03788322,-0.0196093,0.11731113,0.08277381,-0.02688057,0.02019279,0.05200705,0.03119245,-0.05904034,-0.06381727,-0.01305768,0.03483818,-0.01238374,-0.01498375,-0.03968231,-0.02707221,-0.07236119,0.02799079,0.0214515,-0.05590951,-0.03466992,0.00452514,0.00643915,0.02926522,-0.00144344,0.03064136,-0.00316195,0.01457808,0.03696177,0.00686068,0.02083693,-0.04184151,-0.0500935,-0.01062345,-0.02729135,0.03604374,0.00849988,-0.03704188,0.02172617,0.00050579,0.04072838,-0.01762032,-0.02796867,-0.04394008,0.01387772,-0.01885198,0.00221383,0.11188256,0.00985126,0.02191694,-0.01649437,0.00303908,0.00424285,-0.1097463,-0.02906722,-0.02777016,0.03712726,-0.08005008,0.03968994,0.08043323,0.05338168,-0.00572557,0.08227862,0.05470348,0.00211717,0.03583196,0.02501213,0.0156786,0.00215069,0.00612889,0.03555503,0.02340833,-0.2719698,0.02525824,-0.04247938,0.0966593,0.001564,-0.01651176,0.04979025,-0.01347219,0.0232762,-0.07978325,0.07750387,0.04998706,0.05789376,-0.0540485,-0.02592141,0.02700039,0.01214923,-0.05824163,0.05300908,0.01700706,0.05316871,0.0087984,0.25691706,-0.02264279,0.03573013,0.01385425,-0.00431279,-0.00130736,-0.02433272,0.03646745,0.00200893,-0.00751705,0.06445497,-0.01635645,0.00998786,0.03750002,-0.01195296,0.03379947,-0.00127314,0.00738171,-0.03491358,-0.03559965,-0.05120459,0.02069102,0.15101482,0.04010416,-0.010294,-0.07953843,0.04833179,0.10338955,-0.05268404,-0.0979858,-0.06181695,0.01817944,-0.00471102,0.0056188,-0.00411398,-0.02240505,-0.01469434,0.01307016,0.02111308,-0.02604474,0.0469309,0.01085587,-0.02231986],"last_embed":{"hash":"fvt4hn","tokens":110}}},"text":null,"length":0,"last_read":{"hash":"fvt4hn","at":1753423550777},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#{1}","lines":[10,15],"size":291,"outlinks":[{"title":"Read an introduction to Markov Chains mathematics, randomness, random events, prediction.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software##I. [Introduction to _Markov Chains_, Applications to Random Events, Lottery](https://saliu.com/markov-chains-lottery.html#MarkovLottery)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13993627,-0.04567984,-0.09433174,-0.01827031,-0.03737804,0.07098366,-0.01424064,0.01414605,0.04051979,0.00356998,0.01165223,-0.04266158,0.06676494,0.03291728,0.01801771,-0.01521604,-0.0430015,0.02903984,-0.04674498,-0.03726091,0.03155768,-0.05447732,-0.02820634,-0.05604908,0.03590214,0.0412423,0.00443824,-0.05877287,-0.00984486,-0.18939,0.03880308,-0.02730502,-0.0011148,-0.05521024,-0.07054333,0.00348641,-0.02507469,0.03761362,-0.05091467,0.03567075,0.01390725,0.01273732,0.00866965,-0.0299396,0.02604442,-0.08237358,-0.0006787,-0.03445245,-0.01895604,-0.00976754,-0.10906537,-0.00372598,0.01313819,0.00123876,0.08339423,0.03439558,0.04844178,0.0957702,-0.00421512,0.0068664,0.02641386,0.08005794,-0.17878897,0.06206783,0.00790086,0.03991249,-0.01042086,0.0355357,0.03245974,0.05424227,0.01939736,0.02630325,-0.04327026,0.06156647,0.03915391,-0.04157689,-0.0067758,-0.02739595,-0.04061113,-0.0049184,-0.04219624,-0.0250796,-0.01168039,-0.02385127,-0.03652951,0.06725662,0.06549076,-0.00367918,-0.0016741,-0.04197873,-0.02552219,0.04601359,0.04862828,0.04994427,0.00784971,0.02396931,0.05689903,-0.02082017,0.05337295,0.11557975,0.02636644,0.02755309,-0.00257249,0.03542678,0.07757232,-0.01879,0.00020019,-0.05374684,0.01818018,0.03462144,0.04783215,-0.00865652,0.03254351,-0.09246684,-0.0099727,0.00894275,-0.01633079,0.06321994,-0.00390081,0.01967891,-0.01524363,-0.01467247,0.06288514,0.03099266,-0.01286878,0.01712861,0.01953573,0.06229704,0.03324395,0.00271711,0.03730804,0.0330678,-0.13785674,-0.00854794,-0.01417691,-0.04051648,-0.03547868,-0.08113156,0.01000684,0.00974706,-0.01102844,-0.04287833,0.05688162,-0.09898368,-0.01930304,0.11801622,0.00619473,0.00162367,-0.01886604,-0.01502907,-0.00187557,0.00761342,-0.00677985,-0.04267143,0.02219825,0.01948966,0.06178981,0.05327799,0.00269766,0.02278616,-0.03430475,-0.07005434,-0.00509237,0.20079553,-0.00120063,-0.1155393,0.01035018,0.06190061,-0.01859926,-0.04831439,0.031829,0.04136918,-0.04517717,0.0435699,0.07214574,-0.04555882,-0.10167129,-0.04203612,0.01332058,0.01138967,-0.02300444,-0.05718689,-0.04938637,0.00185356,-0.02222626,-0.08004763,0.01482883,-0.00711983,0.01356054,0.04738547,-0.00383541,-0.04419548,-0.00408717,-0.03079455,-0.04061517,-0.01176504,-0.03531114,-0.05087987,0.05242774,-0.0633273,-0.04488696,0.0064834,-0.00163388,0.03361992,-0.05204527,-0.00262986,-0.04163061,-0.04251516,0.03585109,0.01850778,-0.01870772,0.03548201,0.02019796,0.05141534,-0.07422408,0.01648327,0.0199588,0.04373628,0.0441209,0.00734494,-0.00996069,0.01731481,-0.06670953,-0.19524273,-0.02009651,-0.04600158,0.02587829,0.0281488,-0.0312,-0.01190686,-0.04810691,0.04319989,0.06018864,0.1137056,-0.07608105,-0.00374602,0.00562184,0.02975288,0.0421622,-0.08385091,0.00032808,-0.02402357,0.076638,-0.01583454,0.02594414,-0.04118572,-0.08003937,0.03288803,-0.01190426,0.12497394,0.0484379,-0.03063311,0.02553423,0.03971236,0.02830009,-0.05340654,-0.08577269,-0.00111746,0.04268967,-0.01497563,0.02435905,-0.00950715,-0.01961398,-0.0491984,0.03565959,0.03493205,-0.06604979,-0.02323069,-0.01877814,0.00065164,0.01948637,0.00975084,0.02907247,-0.02318981,0.03161053,0.05999732,0.00775474,0.03775078,-0.03291947,-0.05328778,-0.02211036,-0.03542735,0.01297784,0.00514991,-0.04867258,0.04224296,-0.00382448,0.04065422,-0.02775135,0.0001457,-0.04826793,-0.00916181,0.01824941,-0.03013449,0.09296053,0.0238075,0.03427836,0.00178474,0.0027833,-0.02643318,-0.09714756,-0.00429493,-0.04005162,-0.00574546,-0.07351109,0.04389188,0.08346468,0.06778298,0.01103676,0.08348029,0.02891513,0.03101326,0.03039648,0.00955006,0.00373865,0.00424591,0.03443358,0.05078891,0.03517838,-0.27753732,0.02188071,0.0126546,0.06379607,0.01277807,-0.01146173,0.05318822,-0.05159059,0.00211602,-0.09012523,0.04693708,0.036701,0.04382621,-0.03254898,-0.0370661,0.05322276,0.0230885,-0.04178386,0.03954388,0.01616782,0.03240098,-0.0011975,0.21962957,-0.01879655,0.03093959,0.0035155,-0.04102791,0.01560721,0.00980772,0.01390477,0.00043296,-0.00620327,0.06051529,-0.0265877,0.01575887,0.05344106,-0.00009062,0.01696792,0.00325055,0.01922158,-0.05920668,-0.01031691,-0.03539316,0.00902557,0.13582809,0.02354486,0.00225731,-0.10034208,0.02965949,0.09585801,-0.069356,-0.10226553,-0.03582278,0.01562619,-0.00005257,0.02988932,0.01116671,-0.02878281,0.0072218,0.01382066,0.06702294,-0.03871003,0.03899913,0.02829611,-0.0046914],"last_embed":{"hash":"wpb1sm","tokens":343}}},"text":null,"length":0,"last_read":{"hash":"wpb1sm","at":1753423550859},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software##I. [Introduction to _Markov Chains_, Applications to Random Events, Lottery](https://saliu.com/markov-chains-lottery.html#MarkovLottery)","lines":[16,24],"size":863,"outlinks":[{"title":"Introduction to _Markov Chains_, Applications to Random Events, Lottery","target":"https://saliu.com/markov-chains-lottery.html#MarkovLottery","line":1},{"title":"Ion Saliu's Software for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Software","line":2},{"title":"Reports in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Reports","line":3},{"title":"Generate Combinations in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Combinations","line":4},{"title":"Strategies for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Strategies","line":5},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/markov-chains-lottery.html#Resources","line":6},{"title":"Access the best resources in Markov chain applications to lottery, lotto, random events.","target":"https://saliu.com/HLINE.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software##I. [Introduction to _Markov Chains_, Applications to Random Events, Lottery](https://saliu.com/markov-chains-lottery.html#MarkovLottery)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13399784,-0.04633204,-0.08945306,-0.00971383,-0.03249324,0.06635549,-0.02174918,0.016486,0.04035233,0.00959339,0.0134104,-0.04638097,0.06922562,0.03265693,0.01697632,-0.01693798,-0.03617534,0.03166395,-0.04973911,-0.03575876,0.02942542,-0.05481842,-0.02790201,-0.06067441,0.03908157,0.0351532,-0.00091008,-0.05617609,-0.01192397,-0.19337815,0.03130492,-0.0269006,-0.00423031,-0.05417009,-0.07492865,0.00712259,-0.02374763,0.03328723,-0.04166826,0.03698317,0.00830813,0.01628476,0.00551309,-0.03167986,0.02515966,-0.08041579,0.00465534,-0.03258507,-0.00980394,-0.01054268,-0.10695557,-0.00597515,0.01307109,0.0028832,0.08493834,0.03158819,0.04565843,0.09833413,-0.00695,0.00590421,0.02336063,0.0894713,-0.18384054,0.05791959,0.00088635,0.04181089,-0.01478496,0.03041097,0.02307861,0.05748631,0.02589154,0.02157938,-0.046056,0.05970057,0.03788353,-0.04069111,-0.0051934,-0.02751624,-0.03872926,-0.00761017,-0.04732949,-0.02405226,-0.01698616,-0.02623471,-0.02333219,0.06886248,0.06393537,0.00269273,0.00627131,-0.04419244,-0.02359663,0.03992927,0.05386894,0.04965099,0.00679834,0.02754555,0.05394331,-0.0234062,0.05280571,0.1165003,0.02677145,0.02149421,0.00377989,0.02820788,0.07620925,-0.01924357,0.00025797,-0.0472205,0.02028769,0.03413437,0.04554701,-0.00717897,0.03509505,-0.08838212,-0.00971353,0.01254237,-0.01361019,0.05634277,-0.01554295,0.02810466,-0.0207513,-0.0144664,0.06017203,0.03193656,-0.00939957,0.01151454,0.0248795,0.05973703,0.03035359,0.0060083,0.03366724,0.03711597,-0.1435737,-0.00634156,-0.02178661,-0.04501758,-0.03092188,-0.08046571,0.00551676,0.01377849,-0.01234092,-0.04156243,0.05913611,-0.10156134,-0.01505811,0.11623076,0.00780615,0.00183496,-0.01919038,-0.01421986,-0.00523235,0.00875117,-0.00150773,-0.04759676,0.0236447,0.02271282,0.06215502,0.05434646,0.0008293,0.0254841,-0.02988825,-0.06633603,-0.00582222,0.2007909,-0.00334072,-0.11664034,-0.00093769,0.06390577,-0.01988252,-0.04874245,0.03529342,0.04147602,-0.04742634,0.04715937,0.07327616,-0.05270419,-0.1063726,-0.04054476,0.01504963,0.01370495,-0.0185382,-0.05111039,-0.04718098,0.00511158,-0.02547303,-0.0767173,0.01734717,-0.00449142,0.01825605,0.04963867,-0.00076221,-0.03786648,-0.00294319,-0.02968673,-0.03978911,-0.00809209,-0.03366579,-0.04491667,0.05505342,-0.05654879,-0.04460175,0.00709973,-0.00123452,0.02858964,-0.05309148,0.00121015,-0.04301385,-0.04013791,0.03289918,0.0180683,-0.02133013,0.0306312,0.01881069,0.05555357,-0.07479458,0.01771901,0.0223481,0.044838,0.03595283,0.00723333,-0.0069879,0.01702681,-0.06811531,-0.20183983,-0.02034176,-0.04904047,0.03061138,0.03219884,-0.03714515,-0.00798792,-0.04463106,0.0391317,0.0598883,0.10958017,-0.07214943,-0.00697572,0.0030854,0.02904105,0.04370882,-0.07954206,-0.00433509,-0.02444432,0.06974865,-0.01671659,0.0218847,-0.03989437,-0.07735568,0.03780359,-0.01911872,0.12942368,0.05020014,-0.02658921,0.02589257,0.0378488,0.0285427,-0.06223888,-0.08390567,0.00373073,0.03853392,-0.00877255,0.02296922,-0.00501583,-0.02436175,-0.04363966,0.03425709,0.0310614,-0.07443833,-0.01975522,-0.01858063,0.00353109,0.01753442,0.00897739,0.02774095,-0.0198517,0.03374401,0.05688437,0.00924165,0.03848663,-0.03293853,-0.06045027,-0.0199737,-0.03454297,0.01832034,0.00868471,-0.0509097,0.04618338,-0.00572862,0.04360325,-0.02950665,-0.00342851,-0.04077596,-0.00826745,0.02005102,-0.02424135,0.08821759,0.02361914,0.0355299,0.00443848,0.00403285,-0.01842976,-0.10250954,-0.01024509,-0.03698367,-0.0035474,-0.07663644,0.0501665,0.0848592,0.0627778,0.0135838,0.08137096,0.02603624,0.03277859,0.02787491,0.01259715,0.0013485,-0.00074299,0.0304843,0.05811805,0.04207436,-0.27477694,0.01519206,0.0084681,0.06183612,0.00703487,-0.00729008,0.05630144,-0.05309495,0.00727359,-0.09166744,0.04878354,0.04361345,0.04311389,-0.03488407,-0.0379659,0.05022112,0.02349572,-0.04458689,0.03765772,0.01938646,0.0267415,0.00329312,0.22340821,-0.01136529,0.02892131,0.00135082,-0.04404652,0.01502919,0.00310438,0.01076817,-0.0016273,-0.00288318,0.05342561,-0.01850126,0.0072281,0.05717893,0.00153455,0.01126177,0.00004959,0.02127161,-0.06413211,-0.00517929,-0.04262834,0.00950533,0.13262936,0.0218078,0.00336131,-0.09604703,0.03051374,0.08841265,-0.07951468,-0.10385218,-0.03658921,0.01609692,0.00065275,0.03381127,0.01879819,-0.03253469,0.00893119,0.01132802,0.0648954,-0.03404122,0.03852659,0.02775787,-0.00712719],"last_embed":{"hash":"1porqfn","tokens":304}}},"text":null,"length":0,"last_read":{"hash":"1porqfn","at":1753423551073},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software##I. [Introduction to _Markov Chains_, Applications to Random Events, Lottery](https://saliu.com/markov-chains-lottery.html#MarkovLottery)#{1}","lines":[17,24],"size":720,"outlinks":[{"title":"Ion Saliu's Software for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Software","line":1},{"title":"Reports in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Reports","line":2},{"title":"Generate Combinations in _Markov Chains_ Software","target":"https://saliu.com/markov-chains-lottery.html#Combinations","line":3},{"title":"Strategies for _Markov Chains_ in Lottery, Lotto, Horse Racing","target":"https://saliu.com/markov-chains-lottery.html#Strategies","line":4},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/markov-chains-lottery.html#Resources","line":5},{"title":"Access the best resources in Markov chain applications to lottery, lotto, random events.","target":"https://saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>1. Introduction to <i>Markov Chains</i>, Applications to Random Events, Lottery</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12939054,-0.05914077,-0.08213927,-0.00463475,-0.01141149,0.04670102,0.02727079,0.00642348,0.0771137,0.00766145,0.02193183,-0.01527124,0.03784547,0.05570029,-0.00287836,0.01416868,-0.04464701,-0.02202822,-0.04257174,-0.01836409,0.07526593,-0.04538526,-0.07575225,-0.06568298,0.05369484,0.04310549,0.01948864,-0.04569838,-0.03318791,-0.22040993,0.03460133,-0.03146151,0.02144402,-0.01930397,-0.05249901,-0.01148133,0.01288871,0.02239435,-0.03508889,0.04448013,0.00776301,0.04293043,-0.01228857,-0.0313499,0.0092592,-0.07175484,-0.01154747,-0.01368713,-0.08691467,-0.01207199,-0.07343479,0.01902156,0.00079084,0.00746436,0.04009859,0.04359764,0.04888737,0.07724248,0.00105577,0.01915306,0.02436466,0.03544149,-0.15765773,0.06606281,0.05758647,0.01614323,-0.02035307,-0.00882539,0.04602758,0.0441786,0.03519382,0.03873057,-0.02345442,0.08500297,0.04620253,-0.0737694,-0.00091727,-0.01143491,-0.02793211,0.03518063,-0.02198927,-0.04758808,0.01135681,-0.0194382,-0.0705559,0.05161084,0.02880596,-0.04180362,0.01641417,-0.03380293,-0.02436207,0.05662992,0.01900927,0.03759966,0.04501579,0.03244323,0.07554277,0.00203086,0.05755429,0.08785751,-0.00667395,0.03105869,0.00233954,0.05669768,0.06492174,0.00472668,-0.04107097,-0.09137155,-0.00413643,-0.00952419,0.03758818,-0.03028876,0.07092375,-0.05447133,-0.0036654,-0.04802991,0.0224164,0.09688292,0.02392883,-0.01149972,-0.0396377,0.02661965,0.05670433,0.00934539,-0.01041589,0.00617342,-0.0453879,0.09256447,0.03174167,-0.01860154,0.07590812,-0.05604959,-0.09981242,0.0006563,0.02994764,-0.01231403,-0.02288894,-0.04259638,0.03108371,0.0007834,0.01750316,-0.02487685,0.02219427,-0.10869009,-0.08347264,0.08761115,0.01030126,0.00206228,-0.00212329,-0.01153276,-0.01510437,-0.00486154,-0.0493665,-0.06426124,0.03331843,0.02543038,0.05914485,0.0245387,-0.00442691,0.02640878,-0.05020815,-0.05809017,-0.00834899,0.11914438,-0.02441676,-0.07295007,0.02028523,0.03794645,0.00275166,-0.07756645,0.03034066,0.04095689,-0.03334833,0.05242426,0.07055417,-0.04958466,-0.11807965,-0.02384205,0.0058737,0.01234687,-0.03265058,-0.0406732,-0.02104119,-0.0086329,-0.00694591,-0.08472443,-0.00522416,0.02144731,-0.00514476,-0.00941689,0.00428281,-0.04057739,0.01411854,-0.00309066,-0.05640616,-0.03269161,0.0101937,-0.04603424,0.03595682,-0.0423682,-0.10052277,-0.01776635,0.00130638,0.01486709,-0.05192408,-0.00771784,0.02087032,-0.03500186,0.04180519,0.03646589,0.0182835,-0.00683648,-0.0172267,0.03708143,-0.04580605,0.05344598,0.03328339,0.03692863,0.03777115,-0.03408847,-0.01968326,0.04093494,-0.06320498,-0.19120634,-0.05169893,-0.03062197,-0.00050279,0.10382749,-0.03060755,-0.00889189,-0.02737212,0.05209101,0.07517795,0.07431932,-0.06314296,0.01462159,-0.03088518,0.00170242,0.02606153,-0.10543383,0.01147132,-0.03221992,0.06957444,-0.00875097,0.05856484,-0.05654823,-0.10323972,0.02342761,-0.01050967,0.12400642,0.05326239,0.01803181,0.07919038,0.02665429,0.01057925,-0.03237455,-0.09632564,0.00400821,0.03773721,0.01192394,-0.02397916,-0.03034471,-0.04415489,-0.03857906,0.02277285,0.03583009,-0.03705634,-0.06724782,-0.02585036,0.0048297,0.03097311,-0.02379641,0.03353975,-0.01199159,-0.02038169,0.05148165,-0.00530738,0.03736831,-0.03068532,-0.02439699,-0.0285416,-0.00809777,0.07193159,0.02198729,-0.01194263,0.03914943,-0.01294193,0.01089857,-0.02045051,-0.00439525,-0.09127809,-0.00267747,-0.00525776,-0.04562607,0.1345911,0.00691252,-0.00086439,0.05015913,0.01059235,-0.00270479,-0.03361286,0.02288449,-0.02806584,0.04843066,-0.08153353,0.04468926,0.09644005,0.005066,-0.02321356,0.06630793,0.01543993,-0.02074652,0.00164616,-0.00340246,0.00193316,-0.01491237,0.02420429,0.05824387,-0.00318101,-0.26672938,0.05954037,-0.03084186,0.06693289,0.02677986,-0.01025202,0.03968136,-0.00600886,-0.01262638,-0.07776411,0.02500974,0.04601983,0.06651465,-0.05279625,-0.0139571,0.01260033,0.01033513,-0.05715181,0.04743295,-0.00294102,0.05283987,0.03370354,0.2290972,-0.00268,0.0284177,0.03089118,-0.00239054,0.04459131,-0.00207075,-0.00709683,0.00217434,0.01486799,0.01877267,-0.08385867,0.03162006,-0.00747263,-0.00983659,0.04147895,0.02021941,-0.01838523,-0.03366761,-0.00414613,-0.00293361,-0.00063021,0.14793119,0.05545562,0.01262211,-0.10588097,0.02528953,0.15106511,-0.01723033,-0.06058107,-0.01140876,-0.00830227,-0.03782545,0.02460496,-0.02074858,0.00034488,0.02934245,-0.01329466,0.03324754,-0.03119749,0.04467914,0.02763606,-0.00146251],"last_embed":{"hash":"1o3uavf","tokens":475}}},"text":null,"length":0,"last_read":{"hash":"1o3uavf","at":1753423551505},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>1. Introduction to <i>Markov Chains</i>, Applications to Random Events, Lottery</u>","lines":[25,46],"size":5664,"outlinks":[{"title":"_**The Universe itself is random beyond mathematical doubt.**_","target":"https://saliu.com/bbs/messages/683.html","line":5},{"title":"_**perfect shape**_","target":"https://saliu.com/bbs/messages/636.html","line":9},{"title":"_**Fundamental Formula of Gambling: Degree of Certainty**_","target":"https://saliu.com/Saliu2.htm","line":11},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Program**_","target":"https://saliu.com/Markov_Chains.html","line":17},{"title":"Ion Saliu's improved Markov-chains software is based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>1. Introduction to <i>Markov Chains</i>, Applications to Random Events, Lottery</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12972479,-0.05933797,-0.0825402,-0.0039448,-0.00922269,0.04959417,0.02504937,0.00644414,0.07692941,0.00936835,0.01951197,-0.0157544,0.03760768,0.05521747,-0.00360791,0.0135681,-0.04329612,-0.02390647,-0.0415671,-0.01691655,0.07654039,-0.04552163,-0.0750847,-0.06511142,0.056228,0.04195185,0.02137175,-0.04546582,-0.03415618,-0.21985616,0.03341326,-0.03274341,0.02046672,-0.01727576,-0.05266375,-0.01069126,0.01169824,0.02241355,-0.0343003,0.04584682,0.00924787,0.04191791,-0.01152257,-0.03162859,0.00888277,-0.07027431,-0.01334352,-0.01289148,-0.08781769,-0.01440732,-0.0727497,0.01765777,0.00072324,0.00666471,0.04009448,0.04459828,0.04990715,0.07705462,0.00128555,0.02024476,0.02331032,0.03419536,-0.15800324,0.06680535,0.05489007,0.01575883,-0.01981324,-0.00879021,0.04619112,0.04422775,0.03359798,0.03727417,-0.02313115,0.08402833,0.04657864,-0.07510934,-0.00112128,-0.01052427,-0.02778381,0.0362222,-0.02030583,-0.04785138,0.01268757,-0.01982216,-0.07058541,0.05139559,0.02894737,-0.04212105,0.0168862,-0.03406571,-0.02296317,0.05442999,0.01943362,0.03950819,0.04372209,0.03270878,0.07223076,0.0018405,0.05800093,0.08851987,-0.00624372,0.03171891,0.00505513,0.05614362,0.06516688,0.00472479,-0.04099498,-0.09306634,-0.00577498,-0.0098872,0.03741948,-0.03164444,0.06887382,-0.0537529,-0.00272768,-0.04812814,0.02150684,0.10011432,0.02512458,-0.01050659,-0.03879385,0.02647359,0.05747253,0.00830461,-0.0111168,0.00674433,-0.04694255,0.09444957,0.03185181,-0.01884983,0.07540585,-0.05551028,-0.09945621,-0.00163409,0.03053324,-0.01159591,-0.02270046,-0.04241941,0.02981146,-0.00017782,0.01681405,-0.02454527,0.02182051,-0.10952583,-0.08563798,0.08882052,0.00922162,0.00295739,-0.00348481,-0.01139839,-0.01615371,-0.00611687,-0.04984642,-0.06448932,0.0338005,0.02624267,0.05683182,0.02573463,-0.00495685,0.02643666,-0.05054002,-0.05862708,-0.00897307,0.11965635,-0.02396669,-0.07335555,0.01985982,0.03812884,0.00230202,-0.07767739,0.03140787,0.03954651,-0.03481574,0.05176933,0.07135969,-0.05059681,-0.11878823,-0.02359485,0.00372032,0.01213854,-0.03238053,-0.04208586,-0.01928213,-0.01052729,-0.00535835,-0.08520969,-0.00603264,0.02236021,-0.00524327,-0.00686167,0.00147232,-0.0394224,0.01451037,-0.00437457,-0.05628404,-0.03243852,0.00963939,-0.04582728,0.03353415,-0.04462595,-0.10167276,-0.01778282,0.00134466,0.01491699,-0.05160221,-0.00975791,0.02138911,-0.03536012,0.04274517,0.036949,0.01698277,-0.0065801,-0.01694324,0.03450365,-0.04397851,0.05462972,0.03334653,0.03737612,0.03713583,-0.03221726,-0.02051309,0.04322223,-0.06395122,-0.1926388,-0.05133254,-0.02873135,-0.00166984,0.10439774,-0.03037959,-0.00660241,-0.02678389,0.05401985,0.07331853,0.0738899,-0.06177178,0.01360945,-0.02980769,0.00152597,0.02657175,-0.1026658,0.01350776,-0.03139764,0.0702808,-0.00837102,0.05718219,-0.05556238,-0.10103561,0.02486782,-0.0104647,0.12332601,0.05136334,0.01844632,0.08007399,0.02436808,0.0109408,-0.03199169,-0.09607888,0.00559044,0.03714497,0.01076228,-0.0250367,-0.03114394,-0.04512553,-0.03596684,0.0236,0.03407384,-0.0370474,-0.06741714,-0.02868313,0.00236446,0.03198768,-0.02289708,0.03135227,-0.01079327,-0.02094053,0.05271407,-0.00435677,0.03793493,-0.02992457,-0.02432918,-0.02724645,-0.00693084,0.0730717,0.02254403,-0.01142625,0.03909845,-0.01243069,0.01042168,-0.02317994,-0.00491239,-0.08780149,0.00100719,-0.00512026,-0.04664025,0.13379528,0.00886642,-0.00064872,0.05248071,0.00961476,-0.00592335,-0.03456913,0.02497085,-0.02786522,0.04873112,-0.08231487,0.04576819,0.09589457,0.00444878,-0.02037755,0.06665009,0.01519828,-0.02246939,0.00181252,-0.00367276,0.00185935,-0.01622157,0.02501498,0.05928322,-0.00465003,-0.26884985,0.06026134,-0.02978219,0.06579739,0.02776387,-0.00982504,0.03962045,-0.00506912,-0.01139103,-0.07960123,0.02521233,0.04463549,0.06654981,-0.05348467,-0.01434739,0.01293196,0.00856033,-0.05695948,0.04575865,-0.00210717,0.05308612,0.03633732,0.22783478,-0.00092043,0.02754193,0.03101609,-0.00329803,0.04460236,-0.00198255,-0.00675381,0.00153199,0.01572398,0.01737556,-0.08327011,0.03121103,-0.00689925,-0.01107884,0.04162117,0.02078492,-0.01939877,-0.0329309,-0.00382561,-0.00231396,0.0003438,0.14703058,0.05326825,0.01062859,-0.10603446,0.02579501,0.15171337,-0.01816731,-0.05761681,-0.01195462,-0.00793486,-0.03701394,0.02345173,-0.01860735,0.00201985,0.02922734,-0.01447227,0.03294558,-0.02927685,0.04346165,0.02909904,0.00040532],"last_embed":{"hash":"9ldjfg","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"9ldjfg","at":1753423551745},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>1. Introduction to <i>Markov Chains</i>, Applications to Random Events, Lottery</u>#{1}","lines":[27,46],"size":5573,"outlinks":[{"title":"_**The Universe itself is random beyond mathematical doubt.**_","target":"https://saliu.com/bbs/messages/683.html","line":3},{"title":"_**perfect shape**_","target":"https://saliu.com/bbs/messages/636.html","line":7},{"title":"_**Fundamental Formula of Gambling: Degree of Certainty**_","target":"https://saliu.com/Saliu2.htm","line":9},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Program**_","target":"https://saliu.com/Markov_Chains.html","line":15},{"title":"Ion Saliu's improved Markov-chains software is based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14221148,-0.05463541,-0.07004791,-0.00541237,-0.03221321,0.07955271,-0.03598612,0.02708965,0.06222165,-0.00094391,0.02056678,-0.02915533,0.05993341,0.02687446,-0.00266356,-0.0147618,-0.0100288,0.03438717,-0.04964145,-0.02675657,0.04925723,-0.07773067,-0.01456653,-0.0633707,0.03750772,0.03926287,-0.02628958,-0.05986521,-0.02814143,-0.19355473,0.01167319,-0.01280323,0.0335773,-0.04733941,-0.07757555,0.00191499,-0.00577348,-0.03300682,-0.03175611,0.0298835,-0.00887803,0.02249726,-0.00010526,-0.05348972,0.01208358,-0.06931049,-0.02793811,-0.02322627,0.0289882,-0.01568671,-0.08973525,-0.00149157,0.01530365,0.02581507,0.05787481,0.00551198,0.0487075,0.09044571,-0.01963826,0.03252986,0.01494878,0.07559016,-0.18433894,0.05633414,-0.01736579,0.0256458,-0.02352227,-0.02194878,0.03076156,0.03892061,0.01493082,0.02800914,-0.05453877,0.05648025,0.01829395,-0.0479994,0.01928121,-0.03179165,-0.04663867,-0.00991423,-0.05012281,-0.03939717,-0.02234801,-0.07462732,0.00121157,0.09628606,0.04616239,0.0052932,0.00337027,-0.05212486,-0.0354663,0.00214065,0.07199562,0.0507733,0.00439955,0.03966447,0.06038589,-0.03159425,0.03327116,0.10599169,0.01212106,0.00677597,-0.0124133,0.03181999,0.07667281,0.00031713,-0.01153909,-0.04584237,-0.02171179,0.03930777,0.03867792,0.01579461,0.05559598,-0.06313743,-0.01636931,0.03368142,-0.02541253,0.03975539,0.01802891,0.03718043,-0.02620813,-0.00261119,0.05747945,0.03145425,-0.00920249,0.01966121,0.03140821,0.04119177,0.0375655,0.0185083,0.0472611,0.03657447,-0.13680075,-0.00625243,-0.01355051,-0.01579309,-0.02487296,-0.07732844,-0.00308382,0.04233913,-0.02769257,-0.03956666,0.07143694,-0.08765298,-0.03892896,0.08312819,0.01714662,-0.01360077,-0.00281974,0.00285343,0.02727613,0.01009784,-0.01243578,-0.03128634,0.00890295,0.01843994,0.08166558,0.06945132,-0.03502315,-0.00046911,-0.0281989,-0.06657855,-0.02760669,0.16718231,-0.00206664,-0.12114483,0.00776475,0.05939584,-0.02606128,-0.05791841,0.0220843,0.04892438,-0.07337004,0.04334526,0.0672117,-0.04298034,-0.11215454,-0.01570219,0.03320505,0.00019139,-0.02517963,-0.01397965,-0.02439686,0.02378136,-0.03615971,-0.07009092,0.01193959,-0.00928991,0.01193521,0.02644643,0.01706921,-0.00910061,-0.0059842,-0.04274829,-0.03924949,-0.02173409,-0.04564861,-0.03864805,0.04389099,-0.03221552,-0.03340265,0.0045897,0.02300113,0.00818357,-0.0287436,0.03807803,-0.05311291,-0.02261888,0.02970646,0.02183333,-0.02239686,0.02465044,0.00796936,0.06592097,-0.06834951,0.03188593,0.01944942,0.0335195,0.02645459,0.01787688,-0.01802448,0.04115962,-0.05129428,-0.19878753,0.01243836,-0.06226616,0.03627242,0.0488107,-0.04012941,0.01255111,-0.03742548,0.06630266,0.07226886,0.13694298,-0.06689021,-0.01986017,0.00219252,0.00991625,0.05011902,-0.07957493,-0.00636114,-0.02318708,0.05755287,-0.02091135,0.02150324,-0.05345536,-0.06110339,0.08189117,-0.03621854,0.13697894,0.06780969,-0.00668351,0.01229182,0.05826555,0.01640883,-0.05580394,-0.09969572,-0.01725721,0.04255247,-0.00669318,0.01102174,-0.01910473,-0.03956654,-0.07142472,0.03408158,0.0311131,-0.09417649,-0.00043701,0.01620358,0.01027979,0.03087006,0.01513218,0.02200532,0.01367726,0.02549072,0.0332522,0.01126907,0.03334398,-0.03589609,-0.04469933,-0.02178578,-0.00665525,0.04920511,0.01624267,-0.05802781,0.05148562,-0.02307948,0.04060441,-0.02318156,-0.01718263,-0.02128802,0.03563025,0.006283,-0.00712307,0.05959253,0.04031212,0.03078231,-0.02925811,0.00664415,0.00808243,-0.07655109,-0.0025783,-0.02327677,-0.00832041,-0.062965,0.05490481,0.09553307,0.08560835,0.01053037,0.08810163,0.05462693,0.02060988,0.03120789,0.02542927,0.0147534,-0.01715629,0.01931848,0.07159822,0.04142902,-0.27698874,-0.0065261,-0.02338711,0.07349393,-0.00503025,-0.00740216,0.06042227,-0.05491201,-0.00026732,-0.10377821,0.04441927,0.05853182,0.01927458,-0.05735647,-0.03547367,0.03723313,0.02201726,-0.0395725,0.04544649,0.00828907,0.0230126,0.01290227,0.22003734,-0.01240686,0.01906156,0.01880401,-0.02689659,0.02135544,0.00636671,0.03081264,-0.02192245,-0.00238987,0.05656121,-0.04086373,-0.02328695,0.05686595,-0.01265636,0.00069631,-0.0090158,0.0244305,-0.04130181,-0.01242187,-0.04715536,-0.01179143,0.11108752,0.01094656,-0.00976672,-0.07726957,0.00483415,0.09829367,-0.06519924,-0.10695967,-0.05303123,-0.00311322,0.015257,0.03767488,0.01950062,-0.03015493,-0.01106074,0.01991439,0.0351505,-0.03762635,0.02763203,0.02824338,-0.00780504],"last_embed":{"hash":"kwo9cx","tokens":430}}},"text":null,"length":0,"last_read":{"hash":"kwo9cx","at":1753423552142},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>","lines":[47,64],"size":1473,"outlinks":[{"title":"The ultimate lottery software has Markov Chains specialized programs.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":13},{"title":"The Markov chains lotto software is based on followers and lottery pairings as well.","target":"https://saliu.com/images/markov-pick-lottery.gif","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14073041,-0.05827991,-0.0595667,-0.00412341,-0.03048221,0.08109429,-0.04799643,0.0302037,0.0624953,0.00199524,0.01316412,-0.03060449,0.05575152,0.03037236,-0.00305947,-0.00904472,-0.0096012,0.03255698,-0.04152828,-0.03736538,0.04911464,-0.06575636,-0.01716986,-0.05912137,0.03878815,0.04442232,-0.02862594,-0.05192585,-0.02761989,-0.19658253,0.01282992,-0.00933326,0.03565406,-0.04074161,-0.07695673,-0.00050498,-0.01140938,-0.03342151,-0.02794068,0.03521677,-0.00659622,0.0237786,-0.00333698,-0.05366803,0.00804914,-0.06964473,-0.02245843,-0.01907647,0.03022982,-0.01543926,-0.09044662,0.00471839,0.01971621,0.02634978,0.05569864,-0.00847235,0.05106113,0.09392939,-0.02717724,0.037866,0.01495459,0.07117741,-0.19093201,0.06285813,-0.01640709,0.02466073,-0.03260289,-0.01472705,0.02788855,0.0414157,0.00662487,0.02024389,-0.04417364,0.0580166,0.02005761,-0.0436416,0.02057615,-0.03501325,-0.03525386,-0.01415062,-0.04967709,-0.04191773,-0.0213702,-0.07894032,-0.00205949,0.09190442,0.045949,0.01401594,0.00776536,-0.04727512,-0.03861349,0.00211973,0.07457028,0.04857787,-0.00549053,0.04198193,0.05506521,-0.02501608,0.02629848,0.09874059,0.01735024,-0.0010617,-0.02233273,0.02417034,0.07067353,0.00053674,-0.01352715,-0.05088909,-0.02445374,0.0358402,0.03538227,0.01986008,0.05506788,-0.06345958,-0.01458295,0.03884201,-0.02347802,0.03881627,0.01356383,0.03334816,-0.0255129,0.00080684,0.05840621,0.04061249,-0.00432669,0.01922367,0.04166806,0.05018551,0.02986594,0.01916446,0.04189782,0.0393337,-0.13978972,-0.00907794,-0.00481048,-0.01818237,-0.02876241,-0.0762097,-0.00186745,0.05154148,-0.02908089,-0.03699723,0.06600709,-0.08717915,-0.04823744,0.08612427,0.02563454,-0.01369398,-0.00540707,0.00044651,0.02157022,0.01827585,-0.00356786,-0.03612553,0.01960924,0.02171287,0.08650751,0.06367724,-0.04040369,-0.00257574,-0.03080523,-0.06564616,-0.0267442,0.18497887,-0.00698891,-0.12011756,0.008094,0.05529661,-0.02588223,-0.0578704,0.01936647,0.04677903,-0.0701872,0.04951504,0.07402235,-0.04521091,-0.11367498,-0.01532801,0.02807814,0.0010188,-0.01953902,-0.00762543,-0.02267731,0.02667732,-0.03085754,-0.06795262,0.00456768,-0.00406234,0.011824,0.02896199,0.0096928,-0.01196623,-0.00889134,-0.0467068,-0.04407176,-0.02086896,-0.04280819,-0.03426602,0.04413203,-0.03757367,-0.03312164,0.00851813,0.02472388,0.01361581,-0.01844342,0.04526963,-0.05249659,-0.02054494,0.0305844,0.02436644,-0.02251001,0.02119981,0.01306763,0.05744313,-0.06140636,0.03060771,0.02180842,0.03607135,0.02770537,0.0199826,-0.02013606,0.04117585,-0.05199637,-0.20381251,0.01900488,-0.05605743,0.03535262,0.05301817,-0.04110934,0.0203216,-0.03783371,0.06896244,0.07360802,0.12211423,-0.06180939,-0.02580888,-0.00139987,0.00533049,0.04075589,-0.07791211,-0.01156116,-0.01888549,0.05998449,-0.01475685,0.01402379,-0.04398554,-0.05833336,0.08447984,-0.03670169,0.13607676,0.06011411,-0.00263409,0.00986937,0.05746878,0.01207769,-0.05326011,-0.10621988,-0.01250919,0.04564939,-0.0043679,0.02289369,-0.01273444,-0.04784566,-0.06807815,0.03503411,0.02783345,-0.09070574,-0.00610337,0.01134499,0.00983843,0.0329484,0.01802052,0.01075262,0.00855622,0.02711671,0.04097024,0.00984836,0.02710897,-0.03790475,-0.04402428,-0.01933578,-0.00353887,0.04824489,0.01610577,-0.06301206,0.05391716,-0.02907737,0.03709087,-0.01913514,-0.01296896,-0.01817139,0.03748761,0.00326755,-0.01304647,0.05422987,0.03587829,0.02418275,-0.03066313,0.00212874,0.00507029,-0.08169819,0.00179927,-0.01597003,-0.01678093,-0.05879389,0.05551467,0.09598711,0.08425073,0.0141324,0.08155279,0.05715378,0.01831901,0.03305594,0.01627239,0.0157519,-0.01747058,0.02398185,0.07385796,0.03602946,-0.2788007,-0.00126121,-0.01590687,0.07599512,-0.00315982,-0.00405526,0.05835516,-0.06265305,-0.00407929,-0.10135154,0.03325307,0.05916358,0.01962918,-0.05169407,-0.03415843,0.0436488,0.02811591,-0.04419259,0.04270166,-0.00078598,0.01875378,0.01890163,0.22165307,-0.01052151,0.01810488,0.02242669,-0.03501565,0.03026761,0.01024839,0.02926733,-0.02197573,-0.00757521,0.05951554,-0.03786478,-0.02577899,0.05666874,-0.01392808,-0.00669955,-0.00380289,0.02704518,-0.03706861,-0.01898002,-0.04397148,-0.00782993,0.09950951,0.01633525,-0.02057717,-0.08188807,-0.00006737,0.09669249,-0.06110115,-0.10346647,-0.05313119,-0.00532353,0.01084155,0.04241321,0.0234493,-0.02958811,-0.0128592,0.00423547,0.0353489,-0.04968572,0.02403667,0.02533484,-0.0075905],"last_embed":{"hash":"182eak8","tokens":335}}},"text":null,"length":0,"last_read":{"hash":"182eak8","at":1753423552335},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{1}","lines":[49,58],"size":1026,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>3. Reports in <i>Markov Chains</i> Software</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10129157,-0.05708324,-0.06348372,-0.02093329,0.00657238,0.05916974,0.00216209,0.0122145,0.07208852,-0.00712311,0.01539144,-0.04438199,0.10312215,0.03399325,0.02177973,0.01892479,-0.02884793,0.00126739,-0.06025789,-0.02909038,0.04216872,-0.10500886,-0.03708791,-0.06568028,0.07002333,0.03707657,-0.01721862,-0.0541749,-0.04262248,-0.2355518,0.038301,0.00344638,0.04613923,-0.05941911,-0.04046525,-0.01266433,-0.02754942,0.00607136,-0.04014834,-0.00014459,-0.00654114,0.02335132,0.03924521,-0.04615704,-0.00616829,-0.03476654,-0.04881674,-0.01176114,-0.00606894,-0.024898,-0.08492983,-0.02671847,0.01191348,0.04529393,0.06327175,0.04390606,0.07114308,0.05849565,0.01658817,0.02685318,0.04158984,0.05624061,-0.19004488,0.02792556,-0.00090151,0.00950963,-0.01828861,-0.03093821,0.01378881,0.06142458,-0.00509925,0.03634505,-0.06868628,0.05680711,0.0449839,-0.05752172,-0.02316562,-0.01466175,-0.03671331,-0.00720186,-0.04047128,-0.02336778,-0.02800842,-0.02694799,-0.00283922,0.0354408,0.06378573,-0.00389186,-0.02393938,-0.03920868,-0.0385884,0.04829395,0.06362774,0.03126235,-0.02116008,0.05201247,0.04439002,0.0001955,0.01283164,0.12216604,0.01580238,0.007889,-0.02320497,0.05022256,0.0627074,-0.00889968,0.01185392,-0.03861116,-0.01880779,0.02809035,0.03181126,-0.03224963,0.04862014,-0.08362835,0.02080517,0.01267083,0.02400151,0.0491417,0.01623381,0.02401512,-0.00343288,-0.01116367,0.10332063,0.00882626,0.00337041,0.03896704,0.03161205,0.07248967,0.09345179,0.03198177,0.01304143,0.03700502,-0.1118561,0.02040527,-0.03696902,-0.02979692,-0.00976999,-0.05536112,0.0202241,0.01405914,0.00184021,-0.0303619,0.0076513,-0.10953031,0.04122375,0.1188197,-0.04304978,0.01865282,-0.01552121,0.00379526,0.02312042,0.01169669,-0.02426965,-0.08401676,0.02531761,0.01269318,0.07156386,0.04137333,-0.00895117,0.0135181,-0.03789473,-0.05873063,-0.01244237,0.11093273,0.00100751,-0.09072717,0.00689447,0.066887,-0.03707662,-0.04241202,0.0389962,0.04896112,-0.03653092,0.03890081,0.03751919,-0.04071957,-0.05835743,-0.00447521,0.04073977,0.02839139,-0.03401306,-0.04536974,-0.03687334,-0.00848606,-0.01303502,-0.06739423,-0.01216216,-0.0002242,0.02693896,0.04161381,0.00782371,-0.01395339,-0.01447864,-0.0146313,-0.03343706,-0.04897355,-0.01476945,0.0140507,0.0061642,-0.05410465,0.00784357,0.01423566,0.02508346,-0.00036681,-0.01625567,-0.00153104,-0.04522005,-0.00716877,0.03750623,-0.0116998,-0.00786502,0.00553605,0.02377491,0.06248636,-0.08661863,-0.00453338,0.00667965,0.04892996,0.02605005,0.01634421,-0.02698024,0.02154448,-0.02478691,-0.24175832,-0.01720299,0.00773405,0.02542668,0.03119357,-0.04261006,0.01559564,-0.04766838,0.01518753,0.07137538,0.10348049,-0.03463488,-0.04465991,-0.03022805,0.02431696,0.03565913,-0.0669845,-0.01385253,-0.02832722,0.06685334,-0.04062472,0.00786522,-0.08520418,-0.0526761,0.08905789,-0.0417447,0.17271999,0.08568644,-0.04584576,-0.00909236,0.05263533,0.02207278,-0.04176892,-0.07899033,-0.00559721,0.03391192,0.00979679,0.0079221,-0.02176031,-0.05393847,-0.06341023,0.02035324,0.0247419,-0.0569062,-0.02285032,0.00880725,-0.00577021,-0.01264329,-0.00127026,0.04022929,-0.0139929,-0.01688137,0.00738185,0.00078077,0.02624407,-0.05794068,-0.07058608,-0.0150065,-0.01190705,0.00911169,0.01808705,-0.04276235,0.08142547,-0.02571659,0.02523821,-0.02319342,-0.02798368,0.01684456,0.0278406,0.01845596,-0.00788108,0.09143964,0.00532118,0.03902443,-0.00807396,-0.01757915,-0.06000215,-0.09228628,-0.01259745,-0.04528461,0.02607761,-0.0464383,0.03551606,0.05393315,0.05901891,-0.00199885,0.06867719,0.06819656,0.03799695,0.01768015,0.00374614,0.00090856,0.0246059,0.02159501,0.10043941,0.0333792,-0.28352857,-0.02180702,-0.02667389,0.05790004,-0.02713031,-0.01269114,0.04847982,-0.01718977,-0.0138194,-0.03128946,0.04067787,0.06235727,0.0424093,-0.02363249,-0.01154072,0.01178778,-0.02692732,-0.02287259,0.05805843,0.00881177,0.040018,-0.01594507,0.23019131,-0.04611907,0.03286537,0.02242352,-0.00358698,0.00246903,0.01826046,0.0214016,-0.01222695,-0.00248633,0.06144188,0.00045299,0.03907483,0.05765704,0.02057184,0.00860148,-0.01315053,0.00985514,-0.06307945,-0.02219803,-0.00380904,-0.02550174,0.14991088,0.00204007,0.00108902,-0.08454674,-0.02739317,0.09903862,-0.06739786,-0.08045809,-0.02947477,0.03444014,-0.0167108,0.02674448,0.03177087,-0.011986,-0.00899772,0.00794699,0.0067081,-0.03633977,0.05610424,0.04779044,-0.0220332],"last_embed":{"hash":"10fpz3i","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"10fpz3i","at":1753423552491},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>3. Reports in <i>Markov Chains</i> Software</u>","lines":[65,151],"size":6140,"outlinks":[{"title":"Markov chains, followers, pairings apply also to pick-3, pick 4 daily lottery games.","target":"https://saliu.com/images/markov-report.gif","line":7},{"title":"All lotto numbers are listed in Markov chains as followers and pairings.","target":"https://saliu.com/images/markov-chains-lotto.gif","line":11},{"title":"The special Markov software offers defaults for lotto draws analyses.","target":"https://saliu.com/images/markov-draws-analysis.gif","line":86}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>3. Reports in <i>Markov Chains</i> Software</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10296943,-0.04585608,-0.07339338,-0.01127967,0.00075995,0.06370399,0.01736334,0.01122663,0.05222982,0.00772322,0.0152289,-0.03836318,0.08734604,0.02964265,0.03008736,0.01565131,-0.03910114,-0.0132688,-0.06877673,-0.02790761,0.03049073,-0.11611829,-0.03469766,-0.04709489,0.06205908,0.03625041,-0.01051026,-0.06566447,-0.03697663,-0.21226633,0.03215624,-0.0037515,0.04887679,-0.05482119,-0.0494772,-0.01226402,-0.03576206,0.01937122,-0.03333255,-0.00994345,0.00129103,0.02287279,0.0336106,-0.03561508,-0.00420834,-0.04940927,-0.04162934,-0.02373052,-0.0041284,-0.02482996,-0.08704755,-0.03525618,0.02433068,0.05656525,0.06799803,0.03861716,0.06726363,0.05253647,0.00530915,0.02349331,0.04359989,0.05676574,-0.18427518,0.02494352,-0.0121116,0.0243814,-0.02756785,-0.02510748,0.01716384,0.04725745,0.01413504,0.0417334,-0.06672385,0.05416076,0.0341722,-0.04815456,-0.00549253,-0.0229251,-0.04659078,-0.00798701,-0.0471529,-0.01714802,-0.02543653,-0.03247754,-0.01077232,0.04497199,0.0609666,-0.00307843,-0.02637955,-0.04121275,-0.03523633,0.05799637,0.04376869,0.03441052,-0.02426964,0.03588788,0.04409155,-0.0178788,0.02216481,0.12261946,-0.00148997,0.01837544,-0.00070315,0.0283585,0.05918452,-0.00071746,0.00861313,-0.05410767,-0.00904167,0.04392981,0.02512158,-0.01282047,0.03745493,-0.09194542,0.00640307,0.01186704,0.03175208,0.06257784,0.0126511,0.0328487,-0.02108556,-0.01523341,0.09037348,0.01599052,0.00479491,0.05006435,0.0279686,0.06982464,0.09343759,0.03438604,0.01506995,0.04216074,-0.12366571,0.01576685,-0.03519405,-0.02657353,-0.00772685,-0.0732076,0.02346486,0.01347051,0.00228149,-0.03415253,0.01490115,-0.11162739,0.02585349,0.1209119,-0.03182713,0.01864289,-0.00028705,0.01253336,0.02722489,0.02708624,-0.01307537,-0.08216855,0.01035047,0.01960496,0.06659842,0.04787755,-0.00641454,0.03744148,-0.01217398,-0.06414419,-0.01452934,0.13340721,0.01388952,-0.1080669,-0.00105159,0.06328274,-0.02011989,-0.0373696,0.0453349,0.06802183,-0.04071772,0.03653396,0.05068119,-0.03364946,-0.05931351,-0.0095631,0.05779753,0.02298312,-0.04337762,-0.03613034,-0.04679799,0.00686148,-0.03847151,-0.05902881,-0.00202759,0.01967828,0.01306239,0.03222824,0.01664299,-0.02134721,-0.02697829,0.00115754,-0.02367672,-0.04349008,-0.01219153,-0.0016796,0.0095633,-0.04884558,-0.00182535,0.00450645,0.00312172,-0.01667225,-0.02443932,0.00618818,-0.04097964,-0.01152782,0.05957586,-0.02183154,-0.02260068,0.00499946,0.04036865,0.05771222,-0.08503705,-0.00795421,0.00888118,0.02567737,0.0258667,0.01290899,-0.03896604,-0.00265614,-0.03602925,-0.22483984,-0.01657098,-0.00549837,0.02040942,0.02205856,-0.06073099,0.00582865,-0.04238282,0.05184494,0.07872257,0.09900805,-0.04732885,-0.03460759,-0.02119312,0.02895197,0.03492334,-0.0718088,0.01054758,-0.01667983,0.06797383,-0.04099014,0.01626147,-0.0823788,-0.04431402,0.07707466,-0.05017723,0.1780135,0.08425675,-0.06554493,0.00063833,0.053359,0.03358191,-0.04863781,-0.09354642,-0.00115288,0.03685673,-0.00491321,-0.01245088,-0.03536299,-0.05348873,-0.07521015,0.02902004,0.02659844,-0.06843721,-0.02472232,-0.00703141,0.0081519,-0.00031867,0.00421985,0.04633325,-0.02398718,-0.01797349,0.02076495,-0.0163475,0.03082092,-0.05342887,-0.05661349,-0.01914513,-0.0110681,0.02161012,0.01589352,-0.04120519,0.05686301,-0.01227809,0.02379669,-0.03270371,-0.04148282,-0.00022442,0.00914705,0.01295372,-0.00350397,0.0693698,0.01566055,0.03746605,-0.00091852,-0.0112504,-0.05009715,-0.09154212,-0.01071216,-0.05895637,0.02589363,-0.06028634,0.02940467,0.0648158,0.06841762,0.00129163,0.07446995,0.05134462,0.04589304,0.02357734,0.01745593,-0.00533082,0.03072908,0.01476253,0.07242413,0.03967604,-0.28844765,-0.01374468,-0.031111,0.05792233,-0.0046375,-0.00564113,0.04973942,0.00125103,0.01175399,-0.04742904,0.06499513,0.06468894,0.02993876,-0.04147179,-0.02298022,0.01557544,-0.01453015,-0.03333914,0.05696635,0.0137073,0.03324421,-0.02679356,0.23298998,-0.01549,0.03207726,0.02026766,-0.00283911,0.0165474,-0.00009291,0.01842061,-0.0128183,-0.01447062,0.06679093,0.00206715,0.03210261,0.06065312,0.00266486,0.01615713,-0.03031165,0.01289032,-0.05453549,-0.02024942,-0.03053507,-0.00917965,0.15650749,0.0160252,0.00439652,-0.08491199,-0.005771,0.09314905,-0.05754185,-0.0875481,-0.01880354,0.01885369,-0.00745299,0.02606407,0.01757461,-0.01603389,-0.0005341,0.01666826,-0.01239092,-0.04075199,0.04294034,0.04222334,-0.00182344],"last_embed":{"hash":"17dmdhi","tokens":480}}},"text":null,"length":0,"last_read":{"hash":"17dmdhi","at":1753423552707},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>3. Reports in <i>Markov Chains</i> Software</u>#{2}","lines":[69,151],"size":5952,"outlinks":[{"title":"Markov chains, followers, pairings apply also to pick-3, pick 4 daily lottery games.","target":"https://saliu.com/images/markov-report.gif","line":3},{"title":"All lotto numbers are listed in Markov chains as followers and pairings.","target":"https://saliu.com/images/markov-chains-lotto.gif","line":7},{"title":"The special Markov software offers defaults for lotto draws analyses.","target":"https://saliu.com/images/markov-draws-analysis.gif","line":82}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09673648,-0.01827294,-0.06551589,-0.04587904,-0.03210279,0.06360363,-0.00232777,-0.00080209,0.04107521,0.01848128,-0.0098593,-0.03469439,0.10703306,0.01868896,0.01233325,-0.00126355,-0.0182024,0.0147206,-0.0820803,-0.04879046,0.04731373,-0.06322236,-0.04482384,-0.08160684,0.06665663,0.01850929,0.00586011,-0.03913596,-0.03188629,-0.24634439,0.06326672,0.01180144,0.02198543,-0.06043765,-0.05756161,-0.01349744,-0.02444627,0.04605067,-0.07374053,0.01302506,0.01134982,0.01908711,0.01806116,-0.04026211,0.00244977,-0.018143,-0.03996005,0.00466022,0.01350347,-0.00329582,-0.07180689,-0.04544614,0.00009817,0.05600803,0.05144069,0.01896743,0.05284539,0.08370671,-0.01112533,0.04624299,0.04763004,0.10732017,-0.16800867,0.00408926,-0.00045855,0.02549672,-0.03632831,-0.01163192,0.0313651,0.06560529,-0.01147806,0.01212608,-0.0816748,0.03888479,0.0320699,-0.0494377,-0.05574983,-0.03308553,-0.02106565,0.02821554,-0.07418643,-0.00258077,-0.01874039,-0.00853467,0.00265941,0.03329221,0.07631479,-0.00728065,0.00749594,-0.04065517,-0.02637361,0.04403728,0.01074987,0.01274727,0.02615579,0.0451433,0.07615948,-0.02553734,0.0424592,0.12043816,-0.00894361,0.00731419,0.02438273,-0.01014788,0.02694689,-0.03891352,-0.00040912,-0.04494414,-0.04336137,0.00452304,0.0267041,-0.02776858,0.026908,-0.08989022,-0.01329551,-0.00156118,-0.00591793,0.06969314,-0.03227159,0.00622954,-0.01160074,0.00058124,0.05152298,0.00683552,0.02995257,0.02306649,-0.00017191,0.09994543,0.07098873,0.00640839,0.04136627,0.04061516,-0.09840295,-0.02584308,-0.02328253,-0.0469624,-0.02822899,-0.01805378,0.00782536,-0.00424197,0.00853938,-0.00613074,0.0485945,-0.10245249,0.03693071,0.11679354,-0.03164471,0.04698602,-0.03057354,0.00755053,0.02223969,0.01476358,-0.02443541,-0.0846623,0.05923298,0.02711627,0.04356382,0.03889909,0.00404218,0.02295101,-0.00695765,-0.02888968,-0.02103262,0.13324472,-0.01944453,-0.08695239,0.02318575,0.06727647,-0.02739084,-0.05243761,0.01829239,0.05586722,-0.0359391,0.01961724,0.08098685,-0.07814454,-0.09928151,-0.02959074,0.04608892,0.00628228,-0.01586763,-0.02867709,-0.06615434,-0.00100142,-0.0091499,-0.06618679,0.01506617,-0.00948988,0.01816175,0.04564249,-0.00406415,0.0034108,-0.01889447,0.0229825,-0.02605329,-0.03741011,-0.03241333,0.00361924,0.06973617,-0.03727073,0.01291138,-0.0025927,0.00008025,-0.022852,-0.00320149,0.00912852,0.00481016,-0.03423541,0.0630084,0.03421146,-0.043475,-0.00801682,0.03283525,0.09820643,-0.0526755,0.02480669,0.01420004,0.04083643,-0.00600233,-0.02368704,-0.01550295,-0.00685832,-0.04829055,-0.20847155,-0.01705924,0.00620976,0.01276366,0.04539545,-0.05146101,-0.00173339,-0.04751474,0.00067908,0.05192399,0.10331942,-0.03584877,-0.0458946,0.00872579,0.01031606,0.02775778,-0.09392281,-0.00582279,-0.02408813,0.1013564,-0.00883214,0.00174212,-0.02242878,-0.0851982,0.06020304,-0.01397415,0.13404763,0.03146679,-0.02075925,0.03042285,0.0697063,-0.00765292,-0.04617291,-0.03811985,0.03903917,0.03222445,0.00654497,0.00641357,0.00273921,-0.01883645,-0.01773187,-0.00000798,-0.00261764,-0.08508601,0.01590102,-0.046974,-0.04712015,-0.00067396,0.01744436,0.0378459,-0.01926739,-0.00033543,0.03291462,-0.0317689,0.00103213,-0.08228332,-0.06210602,-0.00358109,-0.02998516,0.02280309,0.03798693,-0.07914845,0.06837597,0.00651799,0.02715957,-0.01336639,-0.02271743,0.01726658,0.02333078,0.00860605,-0.01304406,0.10645999,0.03515342,0.06818166,0.02489274,0.00875493,-0.0142299,-0.0725378,-0.01100678,-0.05795176,0.01593535,-0.05593666,0.01789159,0.07740859,0.02885683,-0.01601377,0.06198145,0.06926898,0.02072719,0.01204759,-0.02566178,-0.00319729,0.02188524,0.02481767,0.02873547,0.02528748,-0.27091491,0.03493086,-0.04670653,0.04893501,-0.01900538,-0.03680971,0.03119614,-0.01855232,-0.00685967,-0.05271978,0.02486235,0.04635005,0.07692748,-0.06764934,-0.01037154,0.00682098,0.0007658,-0.07289857,0.06990523,0.01287296,0.05764033,-0.01383281,0.26205289,-0.03722076,0.05797267,0.03314995,-0.02286909,0.0237152,0.02181249,0.00604445,-0.00822616,-0.01675881,0.03963387,0.01844234,0.01253615,0.0744814,0.0077231,-0.00465946,-0.01816251,0.03781274,-0.07286086,-0.01162986,-0.04190047,-0.01601459,0.13325754,0.00846508,-0.00876636,-0.08098458,-0.00511567,0.09288403,-0.06349801,-0.06419431,-0.01396357,-0.0203502,-0.0035034,0.0375278,0.03083148,-0.02111352,-0.01005599,0.01327457,0.03567604,-0.02513112,0.05389812,0.05341855,-0.02139504],"last_embed":{"hash":"1frbjt5","tokens":479}}},"text":null,"length":0,"last_read":{"hash":"1frbjt5","at":1753423552890},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>","lines":[152,178],"size":4024,"outlinks":[{"title":"Markov chains generates lottery combinations from hot and cold numbers.","target":"https://saliu.com/images/markov-hot-numbers.gif","line":9},{"title":"Generate Markov chains combinations from lotto pairs with a pivot number.","target":"https://saliu.com/images/markov-pairs-pivot.gif","line":13},{"title":"Generate Markov chains combinations from lotto pairs without pivot numbers.","target":"https://saliu.com/images/markov-pairs-pivotless.gif","line":17},{"title":"Instead of Markov chains number follower, use lottery pairs to generate combinations.","target":"https://saliu.com/images/markov-chains-pairs.gif","line":21},{"title":"The traditional Markov chains software generates random combinations from number followers.","target":"https://saliu.com/images/markov-chains-followers.gif","line":26}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10009175,-0.01597337,-0.04347795,-0.05156204,-0.0103042,0.05089639,0.0142464,-0.001301,0.03109737,0.02075185,-0.01519581,-0.03892269,0.11658373,0.02233719,0.00086653,0.01009275,-0.02562924,-0.00319969,-0.08225744,-0.04944082,0.0416808,-0.0606643,-0.04772262,-0.08104926,0.06007406,0.03351312,-0.01023285,-0.02011721,-0.02926038,-0.23888327,0.07110059,-0.00077642,0.03688456,-0.07312724,-0.05454135,-0.02295791,-0.02455785,0.04004752,-0.0713931,0.01261564,-0.00112025,0.02156682,0.00503606,-0.03897865,-0.00496313,-0.02250907,-0.03407738,0.01390728,0.00304881,0.00390755,-0.05935366,-0.02870021,0.0056339,0.0518462,0.06671298,0.01774811,0.045822,0.08223139,-0.01358677,0.05143715,0.04117151,0.1035542,-0.16084526,-0.01868075,0.01461446,0.02342051,-0.04355373,-0.01913622,0.01525126,0.08630084,-0.00827945,0.01747533,-0.06558487,0.05874048,0.03286041,-0.04605721,-0.06486773,-0.02691711,-0.01699655,0.03485148,-0.06387028,-0.00657839,-0.02013066,-0.00090979,-0.00606471,0.03119854,0.08982772,-0.01353923,-0.00668034,-0.05034322,-0.02470432,0.04267336,0.02893854,0.01386409,0.02835772,0.04007855,0.06279448,-0.02882846,0.04295649,0.12495704,0.00489424,-0.00230811,0.02111775,-0.01587766,0.0200371,-0.02077042,0.01046838,-0.04753685,-0.04938209,0.00821045,0.02087113,-0.02455325,0.02989784,-0.08936635,0.01713454,-0.0068884,0.00891152,0.06585094,-0.03386562,0.01051992,-0.01215886,0.00885906,0.06264508,0.01501106,0.01492014,0.03738385,0.01019201,0.10858483,0.06433189,-0.00674067,0.02879876,0.03256419,-0.09376819,-0.01823745,-0.02709648,-0.05148358,-0.01983578,-0.02504933,0.01482154,-0.00432471,0.00327368,0.00172282,0.03453879,-0.11210682,0.04316088,0.1153203,-0.03805702,0.02557797,-0.03247522,0.0037078,0.02085449,0.01432307,-0.0115506,-0.09996197,0.0697012,0.03127244,0.05462295,0.03761002,0.01452885,0.02955479,-0.01245395,-0.03044722,-0.00609276,0.14500821,-0.0379353,-0.06071431,0.0293117,0.08960424,-0.01928058,-0.03901166,0.02152776,0.04911536,-0.03545042,0.03954887,0.08915243,-0.0718254,-0.09407208,-0.02486262,0.02616849,0.00836162,-0.00405172,-0.04430515,-0.06669003,-0.00239644,-0.00235532,-0.07871692,-0.00974795,-0.00546588,0.01820719,0.06164169,-0.02177032,0.02923667,-0.02132238,-0.00280238,-0.03151667,-0.03653297,-0.0114618,0.02533199,0.05672598,-0.02907388,0.01785255,-0.00298202,-0.00632041,-0.01051855,-0.00944141,0.01564527,0.00550504,-0.04336197,0.06227359,0.01891942,-0.03200551,-0.01199085,0.03085984,0.09670875,-0.04225479,0.01232153,0.00717992,0.03956339,-0.0060747,-0.026159,-0.01073906,-0.0227432,-0.04831067,-0.21742021,-0.02927765,0.0114272,-0.00183813,0.02962388,-0.04082369,0.00894659,-0.04325683,0.01482327,0.04405225,0.10257252,-0.04927231,-0.05894283,-0.01066029,0.00549368,0.01196256,-0.08949236,-0.02203092,-0.01970294,0.10239807,-0.01605101,-0.00172093,-0.03828999,-0.09257803,0.04291581,-0.00880263,0.14245114,0.02014231,-0.00285414,0.02230286,0.06448418,-0.02016199,-0.03010188,-0.03485354,0.03851961,0.01898514,-0.00213731,0.01288164,0.01513716,-0.02579326,-0.01355485,0.01174217,-0.01589775,-0.07004199,0.00999237,-0.0439982,-0.04163508,-0.01436411,0.00203572,0.02535856,-0.02358306,-0.01862888,0.02086509,-0.00287541,0.01432253,-0.07466661,-0.06331945,0.0023554,-0.02777487,0.01623232,0.04204967,-0.08250373,0.06800907,0.01022138,0.02212258,-0.02553829,-0.02927385,0.01202092,0.02698082,-0.00383921,-0.02432621,0.08792359,0.01825276,0.06578272,0.01156228,-0.001539,-0.01367112,-0.07939669,-0.02110683,-0.06772117,0.02677529,-0.05723893,0.01341494,0.06063643,0.03821763,-0.0158528,0.05586759,0.05658138,0.02893334,0.00906595,-0.02611504,0.00428252,0.02197768,0.0112071,0.03428345,0.02552269,-0.26740497,0.03134003,-0.03990674,0.06128756,-0.02432228,-0.03242841,0.01912892,-0.00664836,-0.00760718,-0.03330955,0.02552898,0.0409061,0.07406021,-0.05336442,-0.01095072,-0.00061104,0.00040955,-0.06870243,0.06180069,0.01582221,0.05879078,-0.0043848,0.274479,-0.03281072,0.05269838,0.04025594,-0.04056493,0.03818097,0.0310892,0.02166687,0.00150168,-0.00675654,0.04508038,0.01975031,0.01037699,0.08109048,0.02094978,-0.00633819,-0.02609866,0.03198956,-0.07986943,-0.01642836,-0.03627378,-0.0276848,0.12557125,0.02083428,-0.01866611,-0.08458408,-0.01242492,0.09236214,-0.06622446,-0.0626924,-0.01571052,-0.0157624,-0.01238161,0.04513145,0.04315582,-0.01957161,-0.01469346,0.00095127,0.03234382,-0.00764582,0.07445956,0.05355828,-0.03208725],"last_embed":{"hash":"1oe0ed7","tokens":172}}},"text":null,"length":0,"last_read":{"hash":"1oe0ed7","at":1753423553105},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{2}","lines":[156,157],"size":514,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10126381,-0.03659907,-0.0779833,-0.012189,-0.04115713,0.07684676,0.0049634,-0.00491113,0.04129356,0.00682212,0.01004359,-0.03577193,0.07894868,0.02221879,0.00317271,0.00329213,-0.05036431,-0.00052872,-0.08221927,-0.03998949,0.06730234,-0.07271664,-0.0294177,-0.08887953,0.06698499,0.02821009,0.01654237,-0.06916064,-0.02483433,-0.23151013,0.0425537,0.00404521,0.00559823,-0.06714101,-0.05250648,-0.01449755,-0.05034064,0.04855433,-0.04593579,0.02958252,0.00632047,0.03323064,0.00346205,-0.0542297,0.00637425,-0.03725101,-0.03385942,-0.00387838,0.02004676,-0.01004827,-0.05800366,-0.03388076,0.01627497,0.05376667,0.06523915,0.00033523,0.05819804,0.0749639,-0.02614624,0.02830866,0.03960265,0.09767636,-0.16284305,0.02174095,-0.03047264,0.03102497,-0.0278223,-0.01235318,0.02511085,0.08727824,0.02040903,0.00429273,-0.09060403,0.05213067,0.03790521,-0.0422933,-0.0398678,-0.04159702,-0.0296674,0.03451462,-0.06604077,-0.03695021,0.00106345,-0.02979522,-0.02334867,0.04362554,0.05826672,-0.0088038,0.00924672,-0.02703966,-0.03641628,0.03589372,0.01267825,0.02393464,0.0085966,0.04238651,0.07543156,-0.02371992,0.04548041,0.11750352,0.01414696,0.02506102,0.01794135,0.02270703,0.04783515,-0.02252905,-0.01001301,-0.03737954,-0.03082036,0.01889342,0.02585423,-0.04035569,0.05357314,-0.05823439,-0.00786199,0.00616438,0.01328306,0.06041424,-0.01755376,0.01757928,-0.02378042,0.00378886,0.05537668,0.01578053,0.02190482,0.00860314,0.00130436,0.08407635,0.03909177,0.00858928,0.04695039,0.03079299,-0.103466,-0.01594804,-0.01054671,-0.03340746,-0.02015371,-0.02848461,0.00091067,-0.00434129,0.01718224,0.01090791,0.03264624,-0.0992432,0.02646505,0.09959914,0.00434796,0.04018332,-0.02638499,-0.00295599,0.01127819,0.00871911,-0.01879269,-0.0636618,0.02814489,0.02317977,0.04678378,0.03585725,-0.01513579,0.03388462,0.00670294,-0.05966873,-0.03893159,0.14898019,-0.02618701,-0.07735652,0.02291564,0.0425646,-0.01244752,-0.08001507,0.02307201,0.07001565,-0.04380195,0.01154593,0.08594876,-0.06495476,-0.13002315,-0.03503415,0.0328283,0.00039708,-0.03060796,-0.01411907,-0.04595953,-0.00202493,-0.02392884,-0.06548181,0.03237847,-0.00168561,0.02298106,0.04681917,0.01554778,-0.00341291,-0.04553748,0.04968971,-0.02495147,-0.03778351,-0.02664134,-0.01994587,0.0603289,-0.05333775,0.00242961,-0.01940695,0.00676739,-0.01674382,-0.00798615,0.01478979,-0.00298993,-0.02846985,0.07253371,0.028292,-0.05837075,-0.01364688,0.03454337,0.0752405,-0.03970497,0.02995241,0.01545545,0.03284768,0.02372056,-0.02320448,-0.01111908,-0.00060747,-0.03688557,-0.21016601,-0.01487517,-0.00614496,0.01246273,0.05546213,-0.03531063,-0.01434652,-0.02360349,0.02188472,0.06417715,0.08648057,-0.04528449,-0.01959878,0.0190639,0.00498457,0.04247834,-0.10032558,0.00949841,-0.02068155,0.10442147,-0.03538645,0.02194037,-0.04786987,-0.08657685,0.06608523,-0.01266127,0.1456688,0.04976538,-0.04327799,0.02598605,0.05552903,0.01140355,-0.07962951,-0.05592876,0.02556665,0.0484399,-0.02954899,-0.01222678,-0.01638405,-0.0430408,-0.0150271,0.0038511,-0.01467547,-0.06818727,-0.00650109,-0.02719274,-0.05387465,0.02019793,0.03970961,0.05054395,-0.01778705,-0.00143613,0.03243335,-0.01676913,0.01684197,-0.06063354,-0.08411354,-0.01032769,-0.03088671,0.061922,0.03229793,-0.06323419,0.04857015,0.01163421,0.04693272,-0.01708977,-0.02059742,-0.02632893,0.01678334,0.00907191,0.00953199,0.10581519,0.05691276,0.0178131,0.00596465,0.00348789,-0.01250239,-0.06448445,-0.00583367,-0.0424185,0.01750649,-0.07545378,0.02349116,0.06846988,0.05120272,0.02626263,0.05256182,0.07281634,-0.00034631,0.01416526,0.00270607,0.0061175,-0.00377371,0.02332349,0.00970087,0.00242034,-0.27428365,0.0403401,-0.0207615,0.06281691,-0.01096591,-0.02252258,0.04893854,-0.01238799,-0.00861646,-0.08101896,0.04172998,0.08136065,0.06455501,-0.0551068,-0.01997937,0.00952707,0.00349988,-0.08101583,0.06833324,0.02261736,0.04335521,0.00801679,0.26755679,-0.02878029,0.04923913,0.01201528,-0.00714891,0.04339103,0.00200369,0.0079221,-0.00893499,-0.01368228,0.03699218,0.00715047,0.02326635,0.04983097,-0.01012477,-0.00123826,-0.0063927,0.02932407,-0.0740198,-0.01751912,-0.04778311,-0.00217873,0.13304001,0.04205154,-0.00914125,-0.07337437,-0.0113182,0.1002239,-0.03786499,-0.07551391,-0.00240463,-0.03665866,-0.00267943,0.01668215,0.0045126,-0.02988293,-0.00454918,0.01375149,0.02849956,-0.04050392,0.03659352,0.01590322,-0.00435805],"last_embed":{"hash":"1nh3lcx","tokens":166}}},"text":null,"length":0,"last_read":{"hash":"1nh3lcx","at":1753423553160},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{3}","lines":[158,161],"size":406,"outlinks":[{"title":"Markov chains generates lottery combinations from hot and cold numbers.","target":"https://saliu.com/images/markov-hot-numbers.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10493375,-0.02693365,-0.05665509,-0.05936864,-0.03870705,0.05573732,-0.00555106,0.00642649,0.05683256,0.01195404,0.01401126,-0.04013832,0.11737312,0.02227529,0.0158343,0.0104038,-0.02116494,0.02722398,-0.08410292,-0.03755805,0.04698124,-0.07373428,-0.05321072,-0.07653419,0.04958364,0.03582552,0.00984374,-0.043379,-0.01230731,-0.24080281,0.06017413,0.00649331,0.04216143,-0.03850221,-0.03932186,-0.01371828,-0.01595626,0.0264015,-0.0644941,0.00636829,0.00023213,0.02209365,0.02288738,-0.01565474,0.00121563,-0.0271626,-0.04600814,-0.00085768,0.00610141,-0.00637892,-0.08770263,-0.04353772,-0.00855186,0.06537272,0.05130583,0.03798055,0.05065719,0.07670577,-0.01529238,0.05024127,0.05199581,0.08775517,-0.17719668,0.00589989,0.00836161,0.02625573,-0.04258095,0.00485914,0.00982262,0.0671827,-0.00437793,0.03595228,-0.05900428,0.02998878,0.0253274,-0.03832424,-0.06235922,-0.03037532,-0.0266754,0.02897449,-0.07216938,0.00751806,-0.04811484,-0.02758217,0.00094346,0.0265796,0.08502494,-0.0092341,-0.01389955,-0.02912426,-0.03589666,0.05949397,0.00297866,0.01391267,0.01136564,0.04113774,0.05157436,-0.0167078,0.04733536,0.12674475,-0.04540934,0.0078679,0.00987607,-0.00030263,0.00558072,-0.02330847,-0.0008253,-0.04287125,-0.04123843,-0.0017593,0.03523084,-0.01625148,0.00891758,-0.11733421,-0.00243451,-0.00026036,-0.02326942,0.07620982,-0.03142849,-0.00111427,-0.00604282,-0.00443199,0.07824244,0.01617729,0.035987,0.02662536,0.00507649,0.09362981,0.08429816,0.02107579,0.03848881,0.03870188,-0.08144179,-0.01820469,-0.03205847,-0.04392766,-0.01884371,-0.05229232,0.01092292,0.02028295,-0.00142084,-0.02934995,0.04253922,-0.09780658,0.05239973,0.13611981,-0.03684516,0.03862026,-0.01648188,0.02639009,0.01854343,0.00877184,-0.01498997,-0.09307601,0.05824651,0.03386972,0.04520631,0.03887521,0.01058494,0.02559491,-0.04003692,-0.02550975,-0.0068771,0.15027519,0.00144712,-0.08791651,0.01171653,0.05331051,-0.0324387,-0.0526954,0.03567215,0.03399691,-0.03783681,0.04641793,0.07088868,-0.06050897,-0.07243029,-0.01678371,0.03891633,0.00404567,-0.01239367,-0.03254819,-0.05116777,0.01359882,-0.02934171,-0.03921194,-0.01671928,0.00578778,0.00552987,0.01774203,0.00945729,0.00566465,-0.02323656,0.01077546,-0.03103944,-0.0443515,-0.0304084,0.00176537,0.04736499,-0.05275561,0.00937419,-0.00561136,-0.02241869,-0.00189712,-0.01069375,0.00825673,-0.01179766,-0.01540805,0.03677031,0.03190956,-0.02278968,-0.01007713,0.04291469,0.07455841,-0.07889944,0.00807642,-0.00596389,0.03455225,-0.01093159,-0.01904341,-0.03138348,0.01242557,-0.02375489,-0.20797375,-0.00252286,0.00611121,0.03589279,0.04593313,-0.05337073,0.00498292,-0.03705941,0.00760498,0.06019096,0.09553994,-0.03169088,-0.02679601,0.01913748,0.00179232,0.02466166,-0.08146068,-0.0041108,-0.03112565,0.07520121,-0.01509211,0.00838341,-0.03215877,-0.0670202,0.08933211,-0.02392952,0.13224795,0.05332281,-0.04193892,0.02541373,0.0668934,0.00025442,-0.05091426,-0.03901277,0.04524084,0.03622076,0.02956527,-0.01471204,0.00024079,-0.02218222,-0.02381052,0.01737826,0.00187365,-0.08316603,0.00250087,-0.04972392,-0.03444958,0.00569211,0.01584254,0.07081827,-0.02224206,0.00286559,0.04552958,-0.02480585,0.01800605,-0.09189694,-0.05558343,0.00059579,-0.01248447,0.00583344,0.02029038,-0.06711835,0.07285888,0.00227439,0.02625808,-0.00138096,-0.02163038,0.01165661,0.01347732,0.01120213,-0.00775049,0.07741603,0.01531669,0.06379104,0.01984666,-0.01954239,-0.03486605,-0.08361235,-0.01104381,-0.06913341,0.02447529,-0.0511613,0.01964891,0.05943369,0.04314775,-0.04576477,0.07685617,0.07559772,0.03425806,0.01404739,-0.01813366,-0.00466901,0.03459856,0.03912275,0.05183417,0.02268818,-0.27993992,0.02018567,-0.0409442,0.06437881,-0.03108106,-0.0434844,0.03525444,-0.02464723,-0.03267942,-0.03179857,0.03895006,0.01867515,0.05704145,-0.05969394,0.00606536,0.01088452,0.01441092,-0.06334159,0.05938117,-0.00210045,0.07368197,-0.01866004,0.25263858,-0.03092666,0.06156376,0.02860236,-0.0201585,0.02108509,0.02100318,0.00284522,-0.00733669,-0.01835196,0.01203964,0.00279135,0.01590176,0.08664398,0.0293986,0.00367693,-0.02616233,0.03703647,-0.05428995,-0.02958679,-0.03692209,-0.02250627,0.14890857,-0.00266709,0.00587947,-0.07113494,-0.01272397,0.07888542,-0.070305,-0.06677911,-0.03657134,0.00888783,-0.01167971,0.04023619,0.03226561,-0.02633416,-0.00400598,0.01082638,0.02829856,-0.0173083,0.04439038,0.05298533,-0.04532316],"last_embed":{"hash":"1e0owdk","tokens":186}}},"text":null,"length":0,"last_read":{"hash":"1e0owdk","at":1753423553219},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{4}","lines":[162,163],"size":536,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13135959,-0.02244697,-0.05070083,-0.04608559,-0.01108454,0.05588826,0.0038349,-0.01120629,0.04308758,-0.01631023,0.00374987,-0.03796976,0.12042413,0.02094869,0.01901293,0.01673952,-0.02545458,0.01183702,-0.08276711,-0.03500189,0.05042396,-0.07770239,-0.04237976,-0.09057954,0.04543175,0.03224891,0.0005556,-0.03405767,-0.01085009,-0.23347989,0.07328225,0.00349953,0.03296607,-0.05645133,-0.04191002,-0.01642493,-0.01415181,0.01413537,-0.06760925,-0.00194955,0.00584625,0.03845897,0.020631,-0.03346125,0.0083594,-0.02729873,-0.0445732,0.00436677,0.00309712,-0.00549023,-0.07502443,-0.04397672,-0.01331673,0.05596836,0.0675714,0.0472693,0.04847036,0.08183374,-0.00899659,0.06479104,0.03315749,0.09174202,-0.16223872,0.00237331,0.02442232,0.01202095,-0.03939067,-0.00485827,0.00790421,0.08097861,0.0078394,0.04584818,-0.04537729,0.04510024,0.0207924,-0.02845751,-0.05063164,-0.02434396,-0.02799158,0.0277237,-0.06618292,-0.00480111,-0.01934412,-0.03229474,0.00455931,0.03766377,0.08573171,-0.02373939,-0.03441318,-0.03640948,-0.05707158,0.07092293,0.01205946,0.00368879,0.00559311,0.03936808,0.05855713,-0.00895375,0.0584867,0.11698254,-0.02463039,0.00441218,0.00384571,0.00259213,0.01945401,-0.02043235,-0.0060976,-0.04862365,-0.03016288,0.0026284,0.03997375,-0.02675262,0.02444215,-0.11162098,0.00485745,0.00446323,-0.00772415,0.07089257,-0.0242567,0.00327479,-0.01606655,0.0006745,0.07242117,0.02060488,0.02103384,0.02137082,-0.00371704,0.09312323,0.06308032,0.02324498,0.03168378,0.03923331,-0.06771056,-0.02175853,-0.02961511,-0.03779585,-0.0251123,-0.04419784,-0.00548473,0.01793743,0.00144076,-0.02963035,0.04578196,-0.09391773,0.06197225,0.11196365,-0.03834195,0.03504764,-0.03295597,0.02769249,0.02869078,0.01210183,0.00289594,-0.0929284,0.06738206,0.02391355,0.05549177,0.02940328,0.00084076,0.03731138,-0.03104853,-0.03118259,-0.00459162,0.14524622,-0.01535575,-0.08491297,0.03343096,0.05918661,-0.0388344,-0.0556205,0.03560633,0.04230108,-0.03181588,0.05627317,0.08323088,-0.06107644,-0.07331542,-0.02529509,0.03512642,0.00022236,-0.0388251,-0.05002191,-0.05541861,0.0082707,-0.01228276,-0.05840139,-0.00860727,0.00160198,-0.0022199,0.02712029,-0.00599814,0.00641657,-0.01241275,-0.00078941,-0.0467925,-0.03390262,-0.01952248,0.00823618,0.05041351,-0.03364152,0.01535483,-0.00442995,-0.01072132,-0.00934344,-0.01274331,0.01724865,-0.00503683,-0.03595668,0.03833435,0.0327512,-0.02601495,-0.01120488,0.04073795,0.08390042,-0.06149849,0.00491372,0.00385863,0.03330689,-0.00633904,-0.02054185,-0.0172868,-0.01870203,-0.02802344,-0.21533751,0.00509354,-0.00068604,0.00906435,0.03781139,-0.05104347,0.00872032,-0.04181878,0.01646656,0.05397017,0.08703609,-0.04606887,-0.02962206,0.01175199,0.00858648,0.01688997,-0.08119012,-0.01272333,-0.02812947,0.09605505,-0.0241686,-0.00308151,-0.04458459,-0.08104737,0.0637089,-0.0153387,0.14541213,0.03376089,-0.03006224,0.01446505,0.07784943,0.00717867,-0.04524239,-0.04352141,0.03340796,0.02369912,0.03472424,-0.00213459,0.01052829,-0.02968861,-0.0098144,0.02465008,0.00507757,-0.07528742,0.00691439,-0.03793289,-0.04594405,-0.00171644,0.01265904,0.04561396,-0.02388177,0.02197119,0.03083463,-0.01717236,0.00251072,-0.08892652,-0.05972374,0.00143606,-0.02123465,0.01784895,0.03872301,-0.07439216,0.06195343,-0.0039709,0.02847867,-0.01159484,-0.01303606,0.00116982,0.02491494,-0.00214937,-0.01605804,0.0674136,0.01386547,0.05955915,0.01684844,-0.0179389,-0.02917404,-0.09587853,-0.03605334,-0.05916897,0.02824324,-0.04523611,0.01657907,0.04969302,0.04053664,-0.02442362,0.07478429,0.08474509,0.02506447,0.00821601,-0.01899281,-0.0027005,0.03871283,0.02605265,0.05507431,0.02352127,-0.28607613,0.02982516,-0.03004082,0.05625718,-0.04145152,-0.04336669,0.02724923,-0.03015068,-0.04336668,-0.0480955,0.02011599,0.03306801,0.07089772,-0.04726249,0.00847127,0.00948751,0.01858715,-0.05688386,0.0624911,0.01054775,0.06812356,-0.00716725,0.27002618,-0.03586198,0.06909204,0.04708328,-0.03610493,0.04240383,0.0359787,-0.00081827,0.00888231,-0.0079171,0.0087934,-0.00489914,0.02525568,0.08601175,0.02731573,0.00378971,-0.01972737,0.0319021,-0.05873501,-0.03217387,-0.02731518,-0.0231488,0.13858244,0.00778403,0.00373983,-0.0603342,-0.02948166,0.08119759,-0.0627168,-0.05805216,-0.01396221,-0.00235275,-0.00414486,0.03612566,0.02838909,-0.03006994,-0.01596129,0.00805168,0.0293948,-0.02408037,0.04317036,0.04881524,-0.04354529],"last_embed":{"hash":"11vgfoe","tokens":171}}},"text":null,"length":0,"last_read":{"hash":"11vgfoe","at":1753423553278},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{6}","lines":[166,167],"size":460,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11589079,-0.03850282,-0.06851407,-0.04182922,-0.03995655,0.05210613,-0.00377733,0.00783232,0.05420556,0.0131931,0.00975736,-0.03049406,0.10014243,0.02143225,0.01215172,0.01549861,-0.03655738,0.00971698,-0.08921186,-0.04916465,0.04044455,-0.06424662,-0.03166939,-0.09004537,0.03916779,0.02148788,0.01491955,-0.055946,-0.00107982,-0.22545366,0.05337398,-0.0153053,0.03258337,-0.06324635,-0.04107647,0.00045176,-0.03724941,0.02034815,-0.0568652,0.02245134,0.0022075,0.02419066,0.03159451,-0.0428452,0.01133648,-0.04203754,-0.03197839,-0.00409045,-0.01148879,0.01263684,-0.09022672,-0.02663862,0.01863831,0.03931567,0.07008147,0.03165941,0.03653632,0.08618872,-0.02231236,0.06133972,0.03964431,0.09439212,-0.13896532,0.01091333,-0.01509213,0.03654176,-0.0348542,-0.01070085,0.0219515,0.08153726,0.03588293,0.04730293,-0.05570929,0.04439557,0.02891469,-0.03118612,-0.02706038,-0.01717141,-0.03061585,0.0477724,-0.06281868,-0.01835253,-0.03117749,-0.02486965,-0.01789881,0.04575977,0.06706715,-0.02213177,-0.01887742,-0.02178655,-0.04175413,0.05298255,0.02254855,0.03351919,0.01851767,0.01531816,0.05624668,-0.02150458,0.04133554,0.1241147,-0.02751594,0.03241497,0.01585045,0.00335742,0.028376,-0.03590631,-0.02546453,-0.04870458,-0.01812808,-0.00590397,0.03489456,-0.0182091,0.04693343,-0.11645843,-0.0254511,0.01027798,0.01148638,0.05686882,-0.01343622,0.0009924,-0.00278445,-0.0064924,0.0648839,0.01508864,0.01874725,0.03463073,0.00370316,0.08488804,0.07121847,0.02988798,0.01577994,0.04832049,-0.09935006,-0.01014423,-0.02381837,-0.04467814,-0.04216102,-0.06609278,-0.00551352,0.00804827,-0.00672902,0.00188638,0.0357177,-0.08899204,0.03272491,0.10609601,-0.0066419,0.04159492,-0.00054896,0.00231692,0.02838547,0.00979885,0.00861367,-0.07243225,0.03212383,0.04420567,0.07362968,0.03926499,0.0051941,0.04737357,-0.03477187,-0.03088434,-0.00087689,0.16385387,-0.00471844,-0.09257086,0.00365621,0.05453656,-0.00724852,-0.05319566,0.04593145,0.0719974,-0.03842635,0.06744798,0.07176721,-0.04755895,-0.11919698,-0.02245616,0.04417406,-0.00120058,-0.00952344,-0.03558769,-0.06167398,0.00989815,-0.05031159,-0.06895374,-0.00089724,0.01302223,0.00454993,0.05636146,0.02597329,-0.01018354,0.00300128,-0.03138137,-0.02982599,-0.04865736,-0.03088988,-0.04034587,0.0336673,-0.04301899,-0.01319865,-0.00385957,0.00490469,-0.01351123,-0.0370337,-0.00761829,-0.01243415,-0.02666346,0.04713639,0.03415132,-0.02441357,-0.01317686,0.02353682,0.08257666,-0.06070995,0.00440203,0.01091586,0.03776414,0.0284816,-0.01993926,-0.01800195,0.00193025,-0.04246795,-0.20381252,-0.02443274,-0.01460609,0.03950117,0.01647371,-0.05082572,-0.01263276,-0.03812772,0.01885713,0.04785995,0.07531617,-0.05575772,-0.02046152,0.02058783,0.0046391,0.04311876,-0.08545963,-0.01193439,-0.02051325,0.06269006,-0.03224114,0.02542393,-0.03906152,-0.09276742,0.05966577,-0.02122294,0.13842784,0.05808407,-0.03931907,0.0264935,0.05449641,0.04121774,-0.04771988,-0.04782118,0.03779289,0.04257922,0.02240637,-0.02877694,0.00847425,-0.03263674,-0.01335897,0.01928661,0.00547326,-0.08883294,-0.00972272,-0.03781752,-0.04786501,0.02779854,0.00996488,0.06075035,-0.03098587,0.01466848,0.0522394,-0.01421137,0.0217714,-0.06686793,-0.07514811,-0.0063806,-0.02347443,0.02159619,0.021856,-0.05836028,0.04663002,-0.0127919,0.02174842,-0.01360348,-0.03316022,-0.02426377,0.0096297,0.00744324,0.00279589,0.08204327,0.0275726,0.04419367,-0.00139009,-0.00839081,-0.04050782,-0.07822476,-0.02769491,-0.055514,0.03344461,-0.04659056,0.01114095,0.07157879,0.03330841,-0.0384746,0.06957803,0.06637178,0.03621282,0.0129245,-0.00471049,0.00263361,0.01915605,0.02339705,0.0603796,0.00522192,-0.28412545,0.01774203,-0.02157734,0.09069031,-0.00594262,-0.01572662,0.05749921,-0.03083338,-0.01995787,-0.05533707,0.05455124,0.02702276,0.04782808,-0.06243506,0.00627333,0.02905097,0.01833456,-0.06781226,0.04848878,-0.01037531,0.05895461,-0.0019162,0.26060253,-0.02976388,0.06291291,0.03418251,-0.01980399,0.02464713,0.02833568,0.00142352,0.0041885,0.00647738,0.06157126,-0.03171078,0.01267448,0.04818618,0.0079216,0.00238709,-0.01749141,-0.0075859,-0.07151756,-0.02909547,-0.04437591,-0.00548369,0.14167188,-0.00158002,0.02422844,-0.07446665,0.00470206,0.08963192,-0.07188927,-0.05531308,-0.0261034,-0.01943065,0.01651434,0.03560704,0.00409899,-0.02572222,-0.01438713,0.03436233,0.03041676,-0.03032984,0.06314231,0.04364236,-0.0470578],"last_embed":{"hash":"70z4wt","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"70z4wt","at":1753423553340},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{8}","lines":[170,171],"size":447,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10029048,-0.03432573,-0.06912427,-0.03532965,-0.03610952,0.06377908,0.01475385,0.01622911,0.06831419,0.01195537,0.00704315,-0.01526533,0.07185037,0.02947167,-0.00606711,0.01434322,-0.04268266,0.01540525,-0.07542437,-0.04506155,0.05102447,-0.05778307,-0.04201385,-0.08929918,0.05154812,0.01852098,0.01473354,-0.07051285,-0.01019789,-0.22921938,0.0455033,-0.01279077,0.01569774,-0.06486624,-0.03657926,-0.01488147,-0.03902606,0.03847942,-0.06050323,0.02859395,0.00361265,0.03558666,0.01065654,-0.03657885,0.02084796,-0.04702067,-0.03312596,-0.00496015,-0.02231469,0.02301961,-0.08790293,-0.02628706,0.02229135,0.04000693,0.07137307,0.00978547,0.04114987,0.07907234,0.01118419,0.04407428,0.05054914,0.09385403,-0.15421593,0.01831436,-0.03095393,0.03901909,-0.04227477,-0.00142385,0.03188543,0.10110526,0.02668819,0.02377912,-0.06633464,0.04086411,0.03550031,-0.04334843,-0.03535835,-0.02989188,-0.03967749,0.03554221,-0.05273153,-0.01253361,-0.02593879,-0.03466916,-0.02202726,0.04814801,0.0648319,-0.01255663,0.00044775,-0.0236453,-0.01092555,0.05632982,0.01549972,0.04156013,0.00284303,0.02423652,0.07685476,-0.03218121,0.03964512,0.12467025,-0.01289482,0.03831521,0.02153917,0.00321015,0.04116298,-0.05145641,-0.02820263,-0.04928192,-0.02220739,0.01359352,0.03854263,-0.01628924,0.05781458,-0.09556873,-0.0326645,0.00803099,0.01669595,0.05478006,-0.01107258,0.01267484,-0.01111093,0.00708158,0.04437729,0.01276947,0.00564711,0.03062238,0.01697572,0.08361077,0.06047021,0.01740098,0.03498213,0.04719312,-0.10341106,0.00684201,-0.00594312,-0.04234708,-0.03509309,-0.04488168,-0.00682181,0.00721962,-0.00491759,0.02795243,0.03899142,-0.0978881,0.0164067,0.09011451,0.00327387,0.03945445,0.00548568,-0.00270464,0.0420907,0.01096119,0.01350305,-0.07103446,0.03801569,0.04553738,0.04920146,0.04445223,-0.00282158,0.02442193,-0.03398878,-0.03693787,-0.00949039,0.1633603,-0.02357543,-0.08583612,0.01134847,0.0410127,-0.00226379,-0.07017142,0.02690253,0.06070787,-0.05350442,0.02698445,0.0735349,-0.04512255,-0.12427179,-0.03297678,0.0413573,-0.01299262,0.00146542,-0.02570714,-0.07651361,0.01833806,-0.03901991,-0.07224997,0.00706607,-0.00009697,0.01429318,0.06104169,0.01961713,-0.00001613,-0.0109102,0.00453113,-0.02872477,-0.06086503,-0.03940644,-0.0345967,0.04647636,-0.03986029,-0.00445762,-0.00781774,0.01498203,-0.02206752,-0.03525751,-0.00623551,0.00566521,-0.04388809,0.06708662,0.03818354,-0.03564425,-0.0122173,0.025994,0.08484913,-0.06466585,0.0106485,0.00578946,0.03209218,0.02803666,-0.02750997,-0.01185444,0.0257805,-0.03747405,-0.20409995,-0.03160794,-0.01566156,0.043228,0.04148633,-0.04385634,-0.02196763,-0.03574422,0.0174839,0.05673347,0.07949011,-0.04300908,-0.02323379,0.02293327,0.00897886,0.04253372,-0.08946102,-0.03591687,-0.01191817,0.06954497,-0.03196288,0.03276345,-0.0373454,-0.10440633,0.05327188,-0.01813424,0.11938079,0.03944722,-0.03280998,0.02081564,0.05326051,0.02049094,-0.04636056,-0.03009669,0.02950016,0.06318132,0.01883217,-0.0183694,0.00159619,-0.03482619,-0.00448041,0.01935609,-0.03471034,-0.09379408,-0.01711893,-0.03586496,-0.05347475,0.03953872,0.02462122,0.06402606,-0.02001982,0.01403564,0.04255048,-0.00183072,0.03162276,-0.0652829,-0.07097758,-0.01127175,-0.0259747,0.05528007,0.01726893,-0.06184281,0.03559146,0.00036323,0.04028391,-0.01625251,-0.02250804,-0.03252358,0.01579455,-0.00262876,-0.0069396,0.08935521,0.02881361,0.02642076,-0.00324365,0.0028966,-0.02718378,-0.07838561,-0.01363849,-0.04695912,0.04114229,-0.04491621,0.01193458,0.06517068,0.03087595,-0.01952717,0.07210447,0.06548456,0.00993255,0.00428834,-0.00950055,0.00776573,0.00876175,0.00476066,0.03828566,0.02352607,-0.28363478,0.01637398,-0.03402711,0.08020584,-0.00144732,-0.01720714,0.06692597,-0.02397494,-0.01688004,-0.05877674,0.04679019,0.04703137,0.04959228,-0.06845462,0.00443854,0.01776543,0.01253723,-0.0753764,0.04591757,0.0030944,0.06282318,0.01966451,0.25747678,-0.03302433,0.05764122,0.02402967,-0.01639179,0.01083919,0.01358758,-0.00421851,0.00442966,0.01536407,0.04874501,-0.03043894,0.00170309,0.02896879,0.00278588,-0.00034686,-0.022246,-0.01193907,-0.08290195,-0.02177833,-0.04084502,-0.00898729,0.14620674,0.01206184,0.00324093,-0.08702999,0.01454713,0.09480913,-0.06884202,-0.06600975,-0.01535974,-0.05238157,0.01193777,0.04016765,0.01615292,-0.02644638,-0.01761947,0.02678095,0.02874488,-0.0345911,0.06782086,0.044935,-0.03109109],"last_embed":{"hash":"1diouu7","tokens":174}}},"text":null,"length":0,"last_read":{"hash":"1diouu7","at":1753423553396},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{10}","lines":[174,174],"size":521,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10836017,-0.01714472,-0.06726956,-0.01939829,-0.03145355,0.05377938,-0.0102377,-0.01117061,0.07829311,0.03107855,0.02397491,-0.03117614,0.05759504,0.00811908,0.01625231,0.01272844,-0.06536483,-0.0158588,-0.07778425,-0.04041607,0.04177244,-0.05214699,-0.05486238,-0.09121174,0.03884765,0.0236465,-0.00382533,-0.07339089,-0.00271086,-0.22578299,0.05029024,0.00582822,-0.00818753,-0.04615185,-0.05032435,-0.0291263,-0.0415366,0.06237556,-0.03879401,0.02263068,-0.01881556,0.04755951,0.00210728,-0.05668526,0.01403109,-0.0378426,-0.05186351,-0.00125306,0.02443099,0.00887713,-0.05448875,-0.01836722,0.03569189,0.04630096,0.06479944,-0.01100106,0.04433272,0.10704684,-0.03743616,0.02134202,0.02497493,0.07821254,-0.14602925,0.01778104,0.00505805,0.04387288,-0.03819119,0.00820469,0.01469245,0.08702915,0.02263974,0.02537232,-0.02725082,0.04965211,0.0349523,-0.03009296,-0.03445235,-0.02736034,-0.05423722,0.06373621,-0.07213453,-0.05373135,-0.0124671,-0.04148014,-0.00935426,0.03747933,0.04386517,0.00786625,0.01064799,-0.01915437,-0.02851168,0.05174556,0.05055509,0.02317726,0.01617077,0.02331039,0.05235334,-0.01553465,0.04036947,0.12424427,-0.00647152,0.01144868,0.02526511,0.04424818,0.01987008,-0.00294704,-0.04291993,-0.05516326,-0.02067288,0.0057506,0.01384779,-0.00003847,0.04274717,-0.0911443,-0.03556287,-0.00159438,-0.00896181,0.08059475,-0.00616022,0.01549421,-0.02733362,0.01069638,0.03515428,0.00579881,0.01141591,0.02229305,0.0052427,0.08021432,0.06109303,0.01779673,0.05468335,0.03036297,-0.11979344,-0.00493178,-0.02777562,-0.04643681,-0.02587388,-0.04886798,0.01413467,0.03333684,0.0159489,0.02375053,0.04384254,-0.09153003,0.02313612,0.10019467,0.00142195,0.04284622,0.00144357,0.01529365,0.00114768,0.00129596,0.01415149,-0.06965429,0.01372053,0.0608623,0.09264656,0.02995274,-0.0018717,0.02347764,-0.0015392,-0.04989808,-0.03018167,0.15953135,-0.00569835,-0.10183633,0.00181686,0.05051274,-0.00245682,-0.05111064,0.03740881,0.05406119,-0.02636341,0.03434975,0.06041481,-0.06269503,-0.14479467,-0.03619593,0.04225151,0.02509132,-0.01405399,-0.02395238,-0.05709305,0.01062388,-0.02961681,-0.08867466,0.03042695,0.01158964,0.01428431,0.06200923,0.01820273,0.01168976,-0.00554907,0.01549963,-0.02389066,-0.03164966,-0.01809073,-0.0311439,0.0577066,-0.0771854,-0.00774224,-0.00213115,0.02174128,0.00055557,-0.04153113,0.01276036,0.01761802,-0.04577043,0.05950609,0.02262853,-0.03335482,-0.01175565,0.01481523,0.1019601,-0.0784964,0.01107863,0.00595043,0.04748349,0.04897035,-0.00979698,-0.00427445,-0.00457152,-0.04500056,-0.18668471,-0.0289504,-0.02132519,0.03581076,0.03697452,-0.02114363,-0.02436684,-0.02527502,0.01431365,0.05595257,0.08237198,-0.04412618,-0.02547392,0.00689252,0.0012398,0.04615626,-0.10532947,0.01768894,-0.00691896,0.06608491,-0.01820785,0.02573695,-0.03000361,-0.1068615,0.0553016,-0.04801676,0.15155056,0.03896848,-0.03899465,0.02276357,0.02791817,0.02995076,-0.0707997,-0.06570192,0.01524273,0.06447097,-0.0187761,-0.02249918,0.00769406,-0.03298226,-0.02187674,0.00816876,0.00235165,-0.09399503,0.0163871,-0.04981337,-0.04423994,0.02816082,0.01850461,0.05669954,-0.01388769,-0.00844988,0.02964208,-0.0331253,0.03621282,-0.04966962,-0.08623157,0.0112544,0.00519301,0.05092049,0.01805565,-0.07591066,0.06353576,-0.02072512,0.02794642,-0.01495892,-0.01074153,-0.03012482,0.01259499,-0.00657541,0.00887904,0.0884244,0.04959979,0.014281,-0.00285077,0.00548369,-0.01882639,-0.07137827,-0.02588291,-0.04969315,0.00436178,-0.07034755,0.03308689,0.09195651,0.02330132,-0.01119906,0.05284042,0.06571681,0.0314315,0.00261376,0.01175082,0.00398042,0.00272445,0.03141667,0.00353741,0.00533088,-0.27371475,0.02742615,-0.03634194,0.08892248,0.03087977,0.01283397,0.05114848,-0.03533631,0.0069026,-0.06551848,0.04877105,0.04934641,0.03293112,-0.07678601,0.0015911,0.02649267,0.03851197,-0.06491987,0.06148408,-0.01969346,0.04337123,0.01507741,0.25888103,0.00203037,0.05099957,0.01226038,-0.00022109,0.03022216,0.00240946,0.0114715,-0.01594356,0.02426997,0.0358953,-0.03228062,0.02206797,0.04721375,-0.01263125,-0.02720115,-0.02269185,-0.01312693,-0.07782507,-0.02660707,-0.0688178,-0.00698004,0.12667677,0.01318048,0.00354401,-0.05906107,-0.01452416,0.07208175,-0.06046262,-0.06686289,-0.03028728,-0.03150798,0.02278773,0.00574781,-0.01329398,-0.03156826,-0.01077447,0.01061096,0.03856056,-0.0035478,0.03543827,0.01551751,-0.02967579],"last_embed":{"hash":"g0vk4s","tokens":131}}},"text":null,"length":0,"last_read":{"hash":"g0vk4s","at":1753423553460},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>4. Generate Combinations in <i>Markov Chains</i> Software</u>#{11}","lines":[175,176],"size":353,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>5. Strategies for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09578177,-0.01812854,-0.04886627,-0.01839828,-0.03959529,0.07744354,-0.02098875,0.01323002,0.07192964,0.02001469,0.01048452,-0.04078683,0.08079004,0.03034431,-0.02065034,-0.01561146,-0.02547274,0.02843221,-0.04793556,-0.01067488,0.04104889,-0.04835024,-0.04469151,-0.08818045,0.06770866,0.02110367,-0.01699301,-0.0532094,-0.03902601,-0.23025392,0.04914753,0.01538454,-0.00312548,-0.04674734,-0.06456858,0.00039995,-0.04149351,0.07800826,-0.03314947,0.01509454,0.02250866,0.00026899,-0.01061807,-0.03851226,0.00327864,-0.02212017,-0.02958784,0.00350947,0.04702844,-0.03436611,-0.08332484,-0.02623553,0.02381572,0.00184202,0.05238718,-0.00017102,0.05390154,0.11412709,-0.00995841,-0.0043875,0.03335906,0.07836162,-0.17698592,0.04302657,-0.01576221,0.03040002,-0.04944283,0.03384832,0.03577907,0.07344518,-0.01898196,-0.01570416,-0.08033856,0.03309412,0.02308276,-0.06051646,-0.02139954,-0.05722166,-0.04889433,0.02336079,-0.08737831,-0.01857095,-0.0305943,-0.00603316,-0.0009932,0.05690118,0.02708038,0.02309669,0.03344947,-0.03623758,-0.00231417,0.03920405,0.02949378,0.02752194,-0.00040535,0.04199658,0.07251714,-0.03970483,0.04941034,0.10899793,0.01699112,-0.00286299,0.02255425,-0.00262786,0.03842045,-0.06366894,-0.02259451,-0.06805305,-0.02488448,0.00881759,0.05332862,-0.00882599,0.05990936,-0.0804586,-0.00125928,0.01690363,-0.0378806,0.06458059,-0.04185094,0.0066212,-0.03327861,0.0131983,0.01143981,0.03817979,0.00532215,0.00183088,0.03137758,0.07067978,0.03171542,-0.01777543,0.03167598,0.06214539,-0.13809736,-0.02766144,-0.00897413,-0.01770438,-0.01780467,-0.01554036,-0.00055249,0.00619163,-0.01748623,0.01370592,0.08117826,-0.10956242,-0.00446743,0.08170512,0.03368058,0.02287155,-0.00663627,0.01221541,-0.01120081,0.00115369,-0.02585139,-0.06040988,0.00291013,0.04110312,0.03896359,0.05880427,-0.03690184,-0.01035163,-0.05225318,-0.02198112,-0.04498623,0.14433305,-0.01623458,-0.10033844,0.01576816,0.06432357,-0.0284345,-0.0796677,0.00827384,0.0093077,-0.03102864,-0.0101514,0.09662142,-0.0517805,-0.11669119,-0.04501752,0.03897152,0.00174601,0.04153932,0.01324859,-0.04997104,0.02902829,-0.00252221,-0.05337816,0.00535501,-0.01173227,0.05353102,0.03377906,-0.0098542,-0.01289259,-0.049529,0.00931729,-0.06028238,-0.05959079,-0.02432049,-0.0348382,0.0444709,-0.02847273,-0.00533889,-0.00977,0.0004205,0.01598981,-0.01826265,0.02463471,-0.01790912,-0.02503076,0.11492689,0.01315442,-0.03741071,-0.00893915,-0.01243166,0.0823276,-0.05517968,0.01903163,0.02594462,0.02985268,0.01190095,0.00198044,-0.00552479,-0.01623667,-0.03128458,-0.19775461,-0.0423165,-0.01372064,0.02192386,0.0541609,-0.00894607,-0.00925758,-0.0316761,0.01732775,0.02519796,0.0998114,-0.04660103,-0.03181119,0.0178634,-0.00337016,-0.00174054,-0.09974744,-0.00143542,-0.0333187,0.07998015,0.00850028,0.01263888,-0.02854741,-0.08816709,0.04031254,-0.04679555,0.15770707,0.03291668,0.00352123,0.01902829,0.01690285,0.01973446,-0.05001874,-0.03633607,0.00211562,0.02242871,0.00042861,0.01822148,-0.01979358,-0.02356883,-0.02824673,-0.00175547,-0.01191948,-0.0850502,-0.01674476,-0.03073555,-0.03302111,0.02871072,0.00595507,0.03820782,0.01368337,0.01240602,0.06060008,-0.00105039,0.03042448,-0.06288207,-0.07729451,-0.0026411,-0.00333415,0.02941983,0.0402276,-0.05780612,0.04680168,0.03586484,0.04790312,-0.00808859,-0.01540332,-0.0296592,0.01250733,0.00368011,0.01990748,0.09311249,0.03591476,0.00072317,0.02447403,0.04298136,0.01706534,-0.08364251,-0.02508489,-0.02854254,-0.02643021,-0.0523307,0.04177308,0.10041014,0.01113174,-0.00066931,0.07593099,0.03070235,0.04107618,0.01085034,0.00480549,-0.00069924,-0.00549931,0.04284216,0.01392191,0.03536168,-0.28347421,0.04794856,-0.02967352,0.05662036,-0.01173912,-0.00590877,0.07907452,0.00340021,0.01760175,-0.06811642,0.04550109,0.05418118,0.06524678,-0.07232004,-0.01919422,0.00628735,0.03878034,-0.04706246,0.08336872,0.00710115,0.06232017,0.01290713,0.23544326,-0.0551733,0.03713456,0.0064567,-0.02445095,0.03429182,0.0248957,0.03137098,0.00285444,-0.00278967,0.06873035,0.01062708,0.00435938,0.03901266,0.00522958,0.01122169,0.01797853,0.00569262,-0.06129535,-0.00713321,-0.03024442,0.00616754,0.12420312,0.01097522,-0.00196525,-0.06926326,0.01969842,0.05312981,-0.0924104,-0.09405521,-0.02837206,-0.03753358,0.01087049,0.02162125,0.01047303,0.00699113,0.00194577,0.01479303,0.06452024,-0.03572471,0.07814641,0.05672856,-0.02042721],"last_embed":{"hash":"1dbv0xc","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"1dbv0xc","at":1753423553511},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>5. Strategies for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>","lines":[179,190],"size":2120,"outlinks":[{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":9},{"title":"Generate thousands of random lotto combinations from Markov chains follower list.","target":"https://saliu.com/HLINE.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>5. Strategies for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09498084,-0.01940196,-0.04780317,-0.02093364,-0.03885101,0.07649425,-0.02199973,0.01483219,0.07477266,0.01915234,0.01024661,-0.04153449,0.08037139,0.03176977,-0.02285269,-0.01752168,-0.02537744,0.02586936,-0.04723543,-0.0096608,0.04363734,-0.05094447,-0.04237634,-0.08891119,0.06880514,0.02068662,-0.01792116,-0.05090169,-0.03874672,-0.23180029,0.04985981,0.0185617,-0.00496139,-0.04462862,-0.06655263,-0.00015566,-0.04253852,0.07797727,-0.03098473,0.01782608,0.02409291,0.00150545,-0.01056909,-0.04019019,0.00386591,-0.01979916,-0.03052678,0.00197738,0.04706311,-0.03530069,-0.08031423,-0.02574167,0.02205914,0.0007788,0.04970997,-0.0002436,0.05522457,0.11421167,-0.01061304,-0.00489784,0.03332311,0.07664233,-0.17736955,0.0441213,-0.01616603,0.02981986,-0.04803933,0.03483238,0.03598212,0.0732728,-0.01869598,-0.01520332,-0.08007575,0.03209849,0.02299947,-0.06369433,-0.020727,-0.05885658,-0.04903431,0.02249403,-0.0880413,-0.01850714,-0.03051699,-0.00747425,-0.00235967,0.05525211,0.02555601,0.02040805,0.03400229,-0.03441723,-0.00067049,0.04027617,0.02986334,0.02710233,-0.00091908,0.04237154,0.07019813,-0.04114342,0.04977875,0.10908467,0.01773934,-0.00109732,0.02422823,-0.0038151,0.03791394,-0.06343409,-0.02365501,-0.06965461,-0.02655648,0.00952437,0.05277551,-0.00993937,0.06068794,-0.08125784,-0.00060022,0.01608273,-0.0365839,0.06594638,-0.04352771,0.00689783,-0.03387442,0.01520734,0.01215073,0.03517385,0.00418395,0.00165778,0.03119768,0.07117019,0.03106539,-0.01854832,0.03360968,0.06246892,-0.13675821,-0.02922886,-0.00750814,-0.01881623,-0.01671443,-0.01607248,-0.00066425,0.00651371,-0.01630566,0.01408821,0.0827128,-0.11008675,-0.00445965,0.08076786,0.03367635,0.02215958,-0.00535303,0.01215518,-0.01371353,-0.00010245,-0.02506254,-0.06174769,0.00201731,0.04249572,0.03866735,0.05881706,-0.03548573,-0.0085882,-0.05524191,-0.02259518,-0.04562454,0.14362067,-0.01792546,-0.10105735,0.01511133,0.0630714,-0.02850861,-0.07995149,0.0083466,0.00887883,-0.03148118,-0.00958386,0.09894711,-0.05124413,-0.11793173,-0.04508263,0.03677776,0.00377944,0.04160452,0.01333484,-0.04799214,0.02750968,-0.00308133,-0.0509613,0.00395987,-0.0118257,0.05458882,0.03387724,-0.01001275,-0.01208257,-0.0497732,0.01150415,-0.06062311,-0.05895647,-0.0243999,-0.03455188,0.04480621,-0.02965347,-0.00337725,-0.00835779,-0.00108414,0.01703278,-0.01789903,0.02623152,-0.02008034,-0.02408085,0.11602768,0.01298435,-0.03757498,-0.0087569,-0.01376665,0.0812061,-0.05473674,0.02001513,0.02685308,0.02926372,0.01213884,0.00130529,-0.00478734,-0.01622228,-0.02988935,-0.19659667,-0.04093342,-0.01269157,0.02242477,0.05500323,-0.00970912,-0.00995288,-0.03171434,0.01926338,0.02339063,0.09690123,-0.04557206,-0.03218915,0.01711348,-0.0030964,-0.00142122,-0.09972978,-0.00174029,-0.03323964,0.0804563,0.00926785,0.01341954,-0.02673634,-0.08674918,0.04252616,-0.04438912,0.15961252,0.0307954,0.00193919,0.01892407,0.01549221,0.02005215,-0.04871327,-0.03590243,0.00235617,0.02043521,-0.00252269,0.01677378,-0.02093426,-0.02374429,-0.02840612,-0.00184103,-0.01272319,-0.0829151,-0.01667983,-0.02982456,-0.03349105,0.02841268,0.00434083,0.03745826,0.01454558,0.01369092,0.06239215,-0.00230971,0.03007907,-0.06471521,-0.07747423,-0.0027411,-0.00514105,0.03147817,0.04053627,-0.05771195,0.04618057,0.03653158,0.04884769,-0.0097241,-0.0158326,-0.02892266,0.013572,0.00393353,0.01985537,0.09339236,0.03439126,-0.00044404,0.02576783,0.04249182,0.01524798,-0.08326594,-0.02621418,-0.02761494,-0.02799429,-0.05301931,0.04127943,0.10045588,0.00851668,-0.00047922,0.07600389,0.03157181,0.0399339,0.01161411,0.00890592,-0.00056523,-0.00713395,0.04243585,0.01446287,0.03377268,-0.2843914,0.05046907,-0.03061526,0.05664774,-0.01083499,-0.00640125,0.07903996,0.00453722,0.01965178,-0.0659223,0.04566518,0.05260805,0.06613078,-0.07073676,-0.01915971,0.00728419,0.03737555,-0.04823362,0.08266916,0.00762092,0.06393284,0.01193085,0.23416443,-0.05554987,0.03869173,0.00506645,-0.02510314,0.0343125,0.0238541,0.03332484,0.00011577,-0.0005519,0.06805851,0.01164389,0.00436132,0.03884767,0.00514121,0.01114778,0.02044146,0.00538436,-0.06049308,-0.00795835,-0.02889496,0.00663897,0.1225374,0.01103873,-0.0033541,-0.06805263,0.01979876,0.05158002,-0.08915456,-0.09418754,-0.02867952,-0.0374514,0.01038557,0.02097527,0.01065682,0.00909185,0.00161481,0.01517975,0.06623949,-0.03368266,0.0765547,0.0580992,-0.02039311],"last_embed":{"hash":"13d9b2g","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"13d9b2g","at":1753423553709},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#<u>5. Strategies for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>#{1}","lines":[181,190],"size":2030,"outlinks":[{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":7},{"title":"Generate thousands of random lotto combinations from Markov chains follower list.","target":"https://saliu.com/HLINE.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#[<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12054313,-0.02720249,-0.0657856,-0.03572273,-0.05309072,0.04793993,-0.02992199,0.00906411,0.04284913,0.00068509,0.02299633,-0.02372095,0.067625,-0.00562096,0.01580594,-0.03618952,0.00468851,0.04405111,-0.03440347,-0.04001574,0.05117952,-0.0615995,-0.0732775,-0.07927445,0.04786009,0.04484352,-0.01732656,-0.05897997,-0.01641537,-0.19166823,0.02958238,-0.00687009,0.01906927,-0.06282116,-0.06795951,-0.00063643,-0.0146807,0.02662129,-0.0446724,0.00741451,0.00961274,0.02967837,0.02331807,-0.00685918,0.0345228,-0.05872102,0.01496266,0.00202242,0.03810454,-0.0313577,-0.11313995,-0.00678382,0.02596401,0.0251924,0.06351101,0.03835283,0.05544551,0.09694873,-0.00201236,0.00603569,0.03541129,0.08442964,-0.19249928,0.07172357,0.01392722,0.04217021,-0.01704159,0.00098165,0.0245181,0.03882353,0.00085156,0.01598049,-0.01789749,0.07154626,0.05427907,-0.03449419,-0.03706073,-0.03809931,-0.03728816,0.0002319,-0.05382205,-0.00987676,0.01355059,-0.00398702,-0.02838567,0.08060952,0.06098361,0.00404172,0.05687622,-0.06576966,-0.00915198,0.05425608,0.06549605,0.04499229,0.01042055,0.00314324,0.06801911,-0.03033605,0.03520069,0.11382519,0.01782932,0.01340302,0.03423832,-0.00531299,0.03845075,-0.05756092,0.0005806,-0.03487583,-0.01168371,0.03526789,0.05007548,-0.00025438,0.00520936,-0.06558308,-0.00703298,-0.01762125,-0.01394493,0.03334266,0.00485846,0.00744431,-0.03705693,-0.01625829,0.05191879,0.02998918,-0.0026155,-0.00233559,-0.00978669,0.07016322,0.03406297,0.01522551,0.05318787,0.06261288,-0.1354022,-0.02316466,-0.00785771,-0.0110571,0.01488833,-0.05832713,0.00729973,0.03954475,-0.02968683,-0.03040165,0.02993284,-0.10940396,-0.03566719,0.10833544,0.00589761,0.00071588,-0.01713529,-0.00499408,0.01773331,-0.01677117,-0.01759896,-0.05474865,0.01067403,0.02716912,0.0924717,0.04232661,-0.02163915,0.00389511,-0.04155407,-0.04013674,-0.04751797,0.18906015,0.00294117,-0.13260211,0.01574351,0.05467255,-0.01574696,-0.08203905,0.02149596,0.02231682,-0.06607717,0.0398717,0.10023743,-0.05053767,-0.07313336,-0.01647564,0.01829978,-0.01148626,-0.00499837,-0.04670499,-0.03514035,0.01587286,-0.02473876,-0.07168218,-0.0095742,0.00160274,-0.00055355,0.04528183,0.02022543,-0.00539639,-0.03905743,-0.02042529,-0.06524181,-0.0219824,-0.04853191,-0.04675391,0.0487608,-0.04973978,-0.00091095,0.01680303,-0.00319011,-0.00033767,-0.0159581,0.01346141,-0.03675968,-0.03747593,0.06235101,0.03863209,-0.03216273,0.01834986,0.04237202,0.05478697,-0.05564167,0.0303621,0.04210787,0.03293663,-0.01582378,0.02953105,-0.02352697,0.0148336,-0.08144058,-0.1798799,-0.03263275,-0.04155484,0.00888797,0.00908683,-0.03178338,0.00936132,-0.06640304,0.01284209,0.06489373,0.12082785,-0.0647475,0.00762514,0.01685893,-0.01160744,0.00461114,-0.07870281,-0.01550664,-0.0608637,0.06229103,-0.00817072,0.00060806,-0.04052642,-0.08979081,0.02039224,-0.03140869,0.12653445,0.0102179,-0.00904414,0.03528458,0.07049841,0.01209122,-0.04587039,-0.06960611,-0.01478782,0.04862575,-0.0230985,-0.0060115,-0.01392462,-0.01174342,-0.07413023,0.00787145,0.02690674,-0.05991548,-0.03778806,-0.00715775,0.00634745,0.0145593,0.00152861,0.04285111,-0.02107875,0.02251046,0.0371563,-0.0017539,0.02504885,-0.02616396,-0.04061501,-0.01171222,-0.04626806,0.03096546,-0.02931153,-0.07269693,0.03335565,0.00764457,0.05469073,-0.02378976,-0.02478842,-0.03742257,0.01553442,-0.00338698,0.00114351,0.08104986,0.0099196,0.04401542,-0.00875644,0.02015297,0.04965651,-0.06348736,-0.01682493,-0.02675292,-0.05367411,-0.06613331,0.04490424,0.0959143,0.07377181,0.0255153,0.08705026,0.02960108,-0.0000205,0.00944735,0.01830056,-0.01579707,-0.0079677,0.02953879,0.05028202,0.057997,-0.27396694,0.02918999,-0.00259011,0.06169056,-0.03195246,-0.0268138,0.05144196,-0.03497477,0.03039618,-0.05999259,0.0695853,0.02963286,0.03083291,-0.04835971,0.00152252,-0.00392582,0.0349285,-0.02197886,0.06665392,0.03212756,0.01971018,0.01493548,0.24845973,-0.0021119,0.0305349,0.02639461,-0.01332041,0.02094349,0.00901219,0.03107397,0.02476204,0.02598277,0.05449197,-0.00420146,-0.02274719,0.03314128,-0.03275525,0.02880275,0.0028892,0.02905277,-0.04999971,-0.00829221,-0.04906808,0.02193116,0.13778667,0.03828738,-0.00257283,-0.12024007,0.03666837,0.08906133,-0.06004691,-0.0557305,-0.06223889,0.00193742,0.00039728,0.01562496,0.0232724,-0.03538787,-0.00110151,-0.0018368,0.03660431,-0.05316237,0.04925236,0.02771107,-0.00519812],"last_embed":{"hash":"nkol0","tokens":393}}},"text":null,"length":0,"last_read":{"hash":"nkol0","at":1753423553890},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#[<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>](https://saliu.com/content/lottery.html)","lines":[191,223],"size":4046,"outlinks":[{"title":"<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":9},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":11},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":12},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":13},{"title":"_**Lottery <u>Skip</u> Systems, Software**_","target":"https://saliu.com/skip-strategy.html","line":14},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":15},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":16},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":17},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":18},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":19},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":20},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":21},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":22},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":23},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":24},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":25},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":26},{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":28},{"title":"Forums","target":"https://forums.saliu.com/","line":30},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":30},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":30},{"title":"Contents","target":"https://saliu.com/content/index.html","line":30},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":30},{"title":"Home","target":"https://saliu.com/index.htm","line":30},{"title":"Search","target":"https://saliu.com/Search.htm","line":30},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":30},{"title":"Read the most thorough article on mathematics of Markov chains in lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":32}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#[<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>](https://saliu.com/content/lottery.html)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11640216,-0.02746615,-0.06171881,-0.03300807,-0.05180002,0.05007312,-0.03149725,0.00992919,0.04345369,-0.00081735,0.02080607,-0.02250116,0.06461067,-0.00577415,0.01651665,-0.03759812,0.00546934,0.04213621,-0.03479684,-0.03971999,0.05328699,-0.05861232,-0.07295613,-0.080134,0.04988074,0.04057477,-0.01982642,-0.05835453,-0.01724386,-0.19105841,0.02957537,-0.00668629,0.01928815,-0.06342669,-0.06713133,-0.00283115,-0.01397809,0.026929,-0.0420361,0.00775311,0.01132223,0.02856592,0.02159168,-0.00438983,0.0336093,-0.05761761,0.01554251,0.00302702,0.03977618,-0.02960985,-0.11322129,-0.0058845,0.02533616,0.02492922,0.06388998,0.03876606,0.05674569,0.09895079,-0.00222865,0.01008659,0.03432949,0.08339744,-0.19361047,0.07144495,0.01419092,0.04082498,-0.01666342,0.00032068,0.02501368,0.0354562,0.00177278,0.01463599,-0.01640522,0.07516674,0.05414547,-0.03498069,-0.03866252,-0.03950712,-0.03708467,0.00214495,-0.05228202,-0.0067691,0.01429858,-0.00569773,-0.02643073,0.07818384,0.06306448,0.00728694,0.05886031,-0.06757765,-0.00548569,0.05010748,0.06258036,0.04552536,0.01149016,0.00197065,0.06679377,-0.03046143,0.03708757,0.11201494,0.0160738,0.01328061,0.03595815,-0.00610603,0.03698811,-0.05582555,0.00099477,-0.0346826,-0.01298635,0.0381573,0.0500441,0.00136404,0.00205474,-0.06635217,-0.00892004,-0.01795317,-0.01740257,0.03007562,0.00472901,0.00697766,-0.03886471,-0.0153018,0.04794275,0.02833975,-0.00277478,-0.00197305,-0.00741419,0.07011476,0.03173946,0.01518254,0.0518737,0.0634573,-0.13922977,-0.02623272,-0.00896696,-0.0107212,0.01502348,-0.05811796,0.00499884,0.03945161,-0.02769963,-0.02987422,0.02976596,-0.10973916,-0.03731396,0.10748226,0.00807161,0.00060229,-0.01621142,-0.00470266,0.01503499,-0.01792331,-0.01612174,-0.05488123,0.0104923,0.02513167,0.09304261,0.04389173,-0.02230821,0.00390258,-0.04234124,-0.0380463,-0.04816436,0.18906558,-0.00158437,-0.13247992,0.01253653,0.05618022,-0.01899702,-0.08415909,0.01946596,0.01819573,-0.06551716,0.041574,0.10223189,-0.04836985,-0.07108393,-0.01612644,0.01576759,-0.01123642,-0.00648737,-0.04770134,-0.03398619,0.01736857,-0.02686895,-0.07343148,-0.01168668,0.00015848,-0.00117766,0.04817696,0.01623339,-0.00263457,-0.03911996,-0.01977277,-0.06416443,-0.02193606,-0.04805876,-0.04867186,0.04945752,-0.04794148,-0.00238354,0.01645124,-0.00418912,-0.00029944,-0.0173539,0.01454411,-0.03787363,-0.03921421,0.06340542,0.03782982,-0.03360005,0.01941692,0.04217143,0.05507355,-0.05483101,0.03047892,0.04222414,0.02907989,-0.015907,0.03129346,-0.02324931,0.01201649,-0.08025531,-0.18139692,-0.03526643,-0.04293862,0.0065659,0.0089563,-0.0315478,0.01136236,-0.06285551,0.01215467,0.06652418,0.12269553,-0.06501541,0.00744326,0.02085563,-0.01298368,0.0006575,-0.07821317,-0.01838933,-0.06094494,0.06333875,-0.00688587,0.00040186,-0.03571662,-0.08756953,0.01780546,-0.03016573,0.12610157,0.00780017,-0.00791651,0.03183318,0.07174065,0.01044599,-0.0450559,-0.06805442,-0.01534419,0.04835723,-0.02578153,-0.00655858,-0.0159875,-0.01253531,-0.07426371,0.00664924,0.02721788,-0.05997482,-0.03591762,-0.00630654,0.00882902,0.01356146,0.00185448,0.04334683,-0.01773691,0.02282166,0.03754391,0.00147315,0.02383424,-0.02768992,-0.03872765,-0.01012492,-0.04442033,0.03191035,-0.03086729,-0.07283866,0.03541206,0.00595178,0.05524017,-0.02094689,-0.02563427,-0.03781613,0.01593368,-0.00307033,0.00064155,0.07996212,0.00751207,0.04498972,-0.00847437,0.01816036,0.05171576,-0.06237055,-0.01361439,-0.02376798,-0.05397202,-0.06794316,0.04631888,0.09801884,0.07351138,0.02729045,0.08465755,0.02899924,-0.00041007,0.00925936,0.01849158,-0.01421413,-0.0106311,0.03184034,0.04827631,0.05857414,-0.27446428,0.03011416,-0.00585669,0.06447563,-0.0295042,-0.02915845,0.05063585,-0.03692744,0.03224171,-0.0580292,0.07052702,0.02993283,0.03161489,-0.05168378,0.00082932,-0.00424217,0.03633294,-0.02318282,0.06547754,0.03387888,0.02084021,0.0180626,0.25162256,-0.00002854,0.02722257,0.02815161,-0.01128038,0.01824585,0.00570851,0.0335208,0.02612592,0.02444127,0.05407107,-0.00269728,-0.02671442,0.03453844,-0.03329743,0.02941424,0.00420259,0.02772935,-0.04967684,-0.00889504,-0.04812834,0.02085638,0.13765176,0.03996097,-0.0065593,-0.11968861,0.03832151,0.08568871,-0.05955393,-0.05460891,-0.06302767,0.00177444,0.00154591,0.01395964,0.02496536,-0.03426667,0.00103324,-0.00103226,0.03854807,-0.04933049,0.04827418,0.02730135,-0.00367731],"last_embed":{"hash":"1f5a849","tokens":393}}},"text":null,"length":0,"last_read":{"hash":"1f5a849","at":1753423554043},"key":"notes/saliu/Markov Chains, Followers, Pairs, Lottery, Lotto, Software.md#Markov Chains, Followers, Pairs, Lottery, Lotto, Software#[<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>](https://saliu.com/content/lottery.html)#{1}","lines":[193,223],"size":3925,"outlinks":[{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":5},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":7},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":9},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":10},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":11},{"title":"_**Lottery <u>Skip</u> Systems, Software**_","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":13},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":14},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":15},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":16},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":17},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":18},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":19},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":20},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":21},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":22},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":23},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":24},{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":26},{"title":"Forums","target":"https://forums.saliu.com/","line":28},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":28},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":28},{"title":"Contents","target":"https://saliu.com/content/index.html","line":28},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":28},{"title":"Home","target":"https://saliu.com/index.htm","line":28},{"title":"Search","target":"https://saliu.com/Search.htm","line":28},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":28},{"title":"Read the most thorough article on mathematics of Markov chains in lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":30}],"class_name":"SmartBlock"},