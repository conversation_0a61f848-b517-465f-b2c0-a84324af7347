module SaliuSystem0007

# 導出 WonderGridEngine 模組
include("WonderGridEngine.jl")
using .WonderGridEngine

# 為了向後兼容，創建一個 SaliuLottery 別名
module SaliuLottery
    # 重新導出 WonderGridEngine 的主要功能
    using ..WonderGridEngine

    # 導出主要類型和函數
    export Drawing, WonderGridConfig, WonderGridResult
    export execute_wonder_grid_strategy, select_key_number
    export calculate_advanced_pair_statistics, select_top_pairs_advanced
    export generate_wonder_combinations

    # 為了兼容性，創建一些別名
    const GAME_CONFIGURATIONS = WonderGridEngine.GAME_CONFIGURATIONS
end

greet() = print("Hello World!")

end # module SaliuSystem0007
