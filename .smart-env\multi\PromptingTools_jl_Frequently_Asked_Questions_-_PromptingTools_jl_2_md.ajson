"smart_sources:PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl 2.md": {"path":"PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl 2.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11311504,-0.00186189,-0.02225232,-0.05534084,-0.0179616,0.02978571,-0.023139,0.03702061,0.02476543,-0.03900693,-0.0255779,0.00722105,0.001527,0.04176361,0.02075916,0.02679388,-0.0292016,-0.00058655,-0.10039015,-0.00869399,0.07918954,0.04020975,0.02058717,-0.04061195,-0.00353709,0.03518749,0.00710412,-0.04065516,-0.00571706,-0.19501761,0.06039511,-0.02072434,0.00170792,-0.01814281,-0.0319417,-0.00503688,-0.0216487,0.0627848,-0.02026308,-0.00987213,0.02664174,0.00893332,-0.03747769,-0.03460512,-0.04529159,-0.09215549,-0.00628827,-0.0345831,0.01309461,-0.05265936,-0.0199301,-0.04640893,0.05464376,-0.04054029,0.01969563,0.00105915,0.00633608,0.08075585,0.036768,0.02280824,0.03186606,-0.00016144,-0.1923061,0.18067057,-0.00137846,0.03086225,0.00025513,0.00397243,0.01676884,0.04616753,-0.03272965,0.02468538,0.00842907,0.0638977,0.01698958,-0.02405394,-0.0285911,-0.00844925,0.00155953,-0.10915496,-0.05418093,-0.01474552,-0.02885015,-0.00947594,-0.04934157,-0.02228516,0.00330841,0.01196169,0.06004079,0.00759669,0.00837249,-0.05934969,0.01852603,0.02206189,-0.05555173,-0.03378415,0.01526631,-0.03024972,-0.10541425,0.14026614,-0.0046237,-0.00256042,0.03431329,-0.01844449,0.01990695,-0.05298574,0.01067447,-0.04039345,-0.02210988,0.03411546,0.01102492,-0.02150032,-0.01157385,-0.07088096,-0.02828111,-0.00999659,0.01819145,-0.01318627,-0.01291231,-0.03697992,0.01399376,0.00932499,0.03167372,0.00563656,0.00671276,0.0205879,0.04119813,0.02065142,-0.00133404,0.01979306,0.05805084,0.00550628,-0.06495753,-0.02682679,-0.01814592,0.05198849,0.06725474,-0.05986001,0.04964294,-0.0001059,0.0158239,-0.01866893,0.02520494,-0.11228968,0.02409362,0.04916437,-0.04044233,-0.01457716,-0.00057768,-0.05376649,0.03133488,0.03843654,-0.05155131,-0.02383042,0.0987427,0.05753567,0.07963546,0.06323778,-0.00141578,0.00319738,-0.04651561,-0.04069582,0.00155433,0.16657543,0.04638225,-0.06598634,-0.10858832,-0.04258035,-0.04987318,-0.03249636,0.01347824,0.05790552,-0.05648125,0.00463425,0.08279797,0.03364911,-0.07597914,0.04164919,0.00241152,0.02927475,0.02820967,-0.03606093,-0.02351278,0.00763589,-0.03085475,-0.03997789,-0.00824795,-0.07418749,-0.01588607,0.03696698,-0.06935542,-0.01268117,0.03775763,-0.01786631,-0.00469401,-0.00303255,0.01271372,-0.06921944,0.05281651,-0.04679811,0.05111389,0.02690939,0.04466735,0.03679915,-0.01436302,0.0498716,0.07747253,-0.00224428,0.12152599,0.04006127,-0.08975697,-0.061289,0.04602588,-0.00886957,-0.00673355,-0.04247676,-0.05833311,0.00981283,0.00913834,0.06309655,-0.02252282,-0.00148034,-0.02726553,-0.21897438,0.01989838,0.02740609,-0.03627302,0.0632323,-0.09972111,0.08605988,-0.05610046,-0.01122404,0.07566205,0.09127347,-0.03215474,0.03157581,-0.01946293,0.00014212,-0.00150472,-0.02794701,-0.00037183,-0.0001848,0.02905244,0.0219431,-0.0086603,0.0413408,-0.08416545,0.01193968,-0.0357113,0.10463168,0.04847895,0.02243459,-0.11057644,0.03357905,-0.00803061,0.05453011,-0.11720336,0.00922954,0.00461496,-0.05310462,0.0515361,0.07970204,0.02368798,-0.0341286,0.03875239,-0.03720881,-0.0269082,-0.02801934,-0.01243309,-0.03375307,-0.0484129,-0.04041019,0.01767781,0.0122083,-0.04972661,0.01205783,-0.00659584,0.02123309,-0.02745038,-0.04739927,-0.07224388,0.01786782,0.00310421,0.0190695,-0.00077135,-0.02642226,-0.03235988,0.04796494,0.03484543,0.00202617,0.00504398,0.05747766,-0.00966316,-0.02263858,0.11521805,0.01493218,0.08449397,-0.01976578,0.07006757,0.00913428,-0.05390859,0.00483233,-0.05224456,-0.03211489,0.02890957,0.00639227,0.01375547,0.03996923,0.06042991,0.02025446,-0.03494907,0.08492165,-0.04498511,-0.02158394,0.01073206,0.02349555,-0.05773548,0.01332244,-0.00704659,-0.23302406,0.02457891,-0.00280055,0.02723092,-0.01131989,0.07857711,0.0411104,0.01896289,-0.01150637,0.06525987,-0.02782663,0.02963212,0.01597066,-0.0430381,0.05457464,0.05200592,0.04137582,0.01666355,0.03915977,-0.09130759,0.03556624,0.01178611,0.21430223,-0.00282128,0.02805475,-0.00977076,0.0073308,-0.03870535,0.06725657,0.04109114,0.03591834,0.04342296,0.14381754,-0.00971096,0.04029362,-0.00392079,-0.04588005,-0.01336141,0.01930281,0.00585898,-0.02521453,0.0247195,-0.06191523,-0.00143444,0.06976959,-0.01418477,-0.00508877,-0.07624849,-0.03196213,0.03641751,-0.00871665,-0.01855739,-0.02371429,0.00171913,-0.0014115,0.00231384,0.07048514,0.0122029,-0.06765028,0.0099753,0.04947418,-0.02563131,0.1099322,-0.00500454,-0.00017369],"last_embed":{"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b","tokens":462}}},"last_read":{"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b","at":1745995218554},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/frequently_asked_questions/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"Why OpenAI","target":"#Why-OpenAI \"Why OpenAI\"","line":243},{"title":"What if I cannot access OpenAI?","target":"#What-if-I-cannot-access-OpenAI? \"What if I cannot access OpenAI?\"","line":244},{"title":"Data Privacy and OpenAI","target":"#Data-Privacy-and-OpenAI \"Data Privacy and OpenAI\"","line":245},{"title":"Creating OpenAI API Key","target":"#Creating-OpenAI-API-Key \"Creating OpenAI API Key\"","line":246},{"title":"Getting an error \"ArgumentError: api_key cannot be empty\" despite having set OPENAI_API_KEY? {#Getting-an-error-\"ArgumentError:-apikey-cannot-be-empty\"-despite-having-set-OPENAIAPI_KEY?}","target":"#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key \"Getting an error \"ArgumentError: api_key cannot be empty\" despite having set OPENAI_API_KEY? {#Getting-an-error-\"ArgumentError:-apikey-cannot-be-empty\"-despite-having-set-OPENAIAPI_KEY?}\"","line":247},{"title":"Getting an error \"Rate limit exceeded\" from OpenAI?","target":"#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI? \"Getting an error \"Rate limit exceeded\" from OpenAI?\"","line":248},{"title":"Setting OpenAI Spending Limits","target":"#Setting-OpenAI-Spending-Limits \"Setting OpenAI Spending Limits\"","line":249},{"title":"How much does it cost? Is it worth paying for?","target":"#How-much-does-it-cost?-Is-it-worth-paying-for? \"How much does it cost? Is it worth paying for?\"","line":250},{"title":"Configuring the Environment Variable for API Key","target":"#Configuring-the-Environment-Variable-for-API-Key \"Configuring the Environment Variable for API Key\"","line":251},{"title":"Setting the API Key via Preferences.jl","target":"#Setting-the-API-Key-via-Preferences.jl \"Setting the API Key via Preferences.jl\"","line":252},{"title":"Understanding the API Keyword Arguments in aigenerate (api_kwargs)","target":"#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs","line":253},{"title":"Instant Access from Anywhere","target":"#Instant-Access-from-Anywhere \"Instant Access from Anywhere\"","line":254},{"title":"Open Source Alternatives","target":"#Open-Source-Alternatives \"Open Source Alternatives\"","line":255},{"title":"Setup Guide for Ollama","target":"#Setup-Guide-for-Ollama \"Setup Guide for Ollama\"","line":256},{"title":"Changing the Default Model or Schema","target":"#Changing-the-Default-Model-or-Schema \"Changing the Default Model or Schema\"","line":257},{"title":"How to have Multi-turn Conversations?","target":"#How-to-have-Multi-turn-Conversations? \"How to have Multi-turn Conversations?\"","line":258},{"title":"How to have typed responses?","target":"#How-to-have-typed-responses? \"How to have typed responses?\"","line":259},{"title":"How to quickly create a prompt template?","target":"#How-to-quickly-create-a-prompt-template? \"How to quickly create a prompt template?\"","line":260},{"title":"Do we have a RecursiveCharacterTextSplitter like Langchain?","target":"#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain? \"Do we have a RecursiveCharacterTextSplitter like Langchain?\"","line":261},{"title":"How would I fine-tune a model?","target":"#How-would-I-fine-tune-a-model? \"How would I fine-tune a model?\"","line":262},{"title":"​","target":"#Frequently-Asked-Questions","line":264},{"title":"​","target":"#Why-OpenAI","line":266},{"title":"Setup Guide for Ollama","target":"/PromptingTools.jl/dev/frequently_asked_questions#setup-guide-for-ollama","line":272},{"title":"Ollama.ai","target":"https://ollama.ai/","line":272},{"title":"​","target":"#What-if-I-cannot-access-OpenAI?","line":274},{"title":"​","target":"#Data-Privacy-and-OpenAI","line":283},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance","line":289},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data","line":291},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data","line":295},{"title":"Data usage for consumer services FAQ","target":"https://help.openai.com/en/articles/7039943-data-usage-for-consumer-services-faq","line":297},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance","line":299},{"title":"​","target":"#Creating-OpenAI-API-Key","line":302},{"title":"OpenAI","target":"https://platform.openai.com/signup","line":306},{"title":"API Key page","target":"https://platform.openai.com/account/api-keys","line":308},{"title":"OpenAI Documentation","target":"https://platform.openai.com/docs/quickstart?context=python","line":317},{"title":"Visual tutorial","target":"https://www.maisieai.com/help/how-to-get-an-openai-api-key-for-chatgpt","line":319},{"title":"​","target":"#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key","line":324},{"title":"​","target":"#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?","line":343},{"title":"OpenAI Rate Limits","target":"https://platform.openai.com/docs/guides/rate-limits/usage-tiers?context=tier-free","line":347},{"title":"​","target":"#Setting-OpenAI-Spending-Limits","line":367},{"title":"OpenAI Billing","target":"https://platform.openai.com/account/billing","line":371},{"title":"OpenAI Forum","target":"https://community.openai.com/t/how-to-set-a-price-limit/13086","line":380},{"title":"​","target":"#How-much-does-it-cost?-Is-it-worth-paying-for?","line":382},{"title":"OpenAI Pricing per 1000 tokens","target":"https://openai.com/pricing","line":396},{"title":"​","target":"#Configuring-the-Environment-Variable-for-API-Key","line":398},{"title":"OpenAI Guide","target":"https://platform.openai.com/docs/quickstart?context=python","line":428},{"title":"​","target":"#Setting-the-API-Key-via-Preferences.jl","line":430},{"title":"​","target":"#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs","line":440},{"title":"OpenAI API reference","target":"https://platform.openai.com/docs/guides/text-generation/chat-completions-api","line":442},{"title":"​","target":"#Instant-Access-from-Anywhere","line":444},{"title":"​","target":"#Open-Source-Alternatives","line":457},{"title":"Ollama.ai","target":"https://ollama.ai/","line":459},{"title":"​","target":"#Setup-Guide-for-Ollama","line":461},{"title":"here","target":"https://ollama.ai/download","line":465},{"title":"Ollama Library","target":"https://ollama.ai/library","line":471},{"title":"Ollama.ai","target":"https://ollama.ai/","line":477},{"title":"​","target":"#Changing-the-Default-Model-or-Schema","line":479},{"title":"Working with Ollama","target":"/PromptingTools.jl/dev/frequently_asked_questions#working-with-ollama","line":487},{"title":"​","target":"#How-to-have-Multi-turn-Conversations?","line":492},{"title":" Info: Tokens: 50 @ Cost: $0.0 in 1.0 seconds\n## 5-element Vector{PromptingTools.AbstractMessage}:\n##  PromptingTools.SystemMessage(\"Act as a helpful AI assistant\")\n##  PromptingTools.UserMessage(\"Hi! I'm John\")\n##  AIMessage(\"Hello John! How can I assist you today?\")\n##  PromptingTools.UserMessage(\"What's my name?\")\n##  AIMessage(\"Your name is John.\")\n```\n\nNotice that the last message is the response to the second request, but with `return_all=true` we can see the whole conversation from the beginning.\n\n## How to have typed responses? [​","target":"#How-to-have-typed-responses?","line":524},{"title":" Info: Condition not met. Retrying...\n## [ Info: Condition not met. Retrying...\n## SmallInt(2)\n```\n\nWe ultimately received our custom type `SmallInt` with the number of car seats in the Porsche 911T (I hope it's correct!).\n\nIf you want to access the full conversation history (all the attempts and feedback), simply output the `response` object and explore `response.conversation`.\n\n## How to quickly create a prompt template? [​","target":"#How-to-quickly-create-a-prompt-template?","line":628},{"title":"​","target":"#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?","line":719},{"title":"`RecursiveCharacterTextSplitter`","target":"https://python.langchain.com/docs/modules/data_connection/document_transformers/recursive_text_splitter","line":723},{"title":"​","target":"#How-would-I-fine-tune-a-model?","line":739},{"title":"Axolotl","target":"https://github.com/OpenAccess-AI-Collective/axolotl","line":748},{"title":"JuliaLLMLeaderboard Finetuning experiment","target":"https://github.com/svilupp/Julia-LLM-Leaderboard/blob/main/experiments/cheater-7b-finetune/README.md","line":748},{"title":"JarvisLabs.ai","target":"https://jarvislabs.ai/templates/axolotl","line":748},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/frequently_asked_questions.md","line":750},{"title":"Previous pageAPITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":752},{"title":"Next pageGeneral","target":"/PromptingTools.jl/dev/prompts/general","line":754},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":756},{"title":"Icons8","target":"https://icons8.com","line":756},{"title":"**VitePress**","target":"https://vitepress.dev","line":756}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,263],"##Reference#{1}":[209,242],"##Reference#{2}":[243,244],"##Reference#{3}":[245,245],"##Reference#{4}":[246,246],"##Reference#{5}":[247,247],"##Reference#{6}":[248,248],"##Reference#{7}":[249,249],"##Reference#{8}":[250,250],"##Reference#{9}":[251,251],"##Reference#{10}":[252,252],"##Reference#{11}":[253,253],"##Reference#{12}":[254,254],"##Reference#{13}":[255,255],"##Reference#{14}":[256,256],"##Reference#{15}":[257,257],"##Reference#{16}":[258,258],"##Reference#{17}":[259,259],"##Reference#{18}":[260,260],"##Reference#{19}":[261,261],"##Reference#{20}":[262,263],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)":[264,758],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)":[266,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#{1}":[268,273],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)":[274,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{1}":[276,277],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{2}":[278,279],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{3}":[280,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)":[283,301],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{1}":[285,294],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{2}":[295,296],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{3}":[297,298],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{4}":[299,301],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)":[302,323],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{1}":[304,305],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{2}":[306,307],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{3}":[308,309],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{4}":[310,312],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{5}":[313,316],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{6}":[317,318],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{7}":[319,321],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{8}":[322,323],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)":[324,342],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{1}":[326,335],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{2}":[336,337],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{3}":[338,339],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{4}":[340,342],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)":[343,366],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{1}":[345,352],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{2}":[353,354],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{3}":[355,357],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{4}":[358,366],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)":[367,381],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{1}":[369,370],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{2}":[371,372],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{3}":[373,375],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{4}":[376,379],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{5}":[380,381],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)":[382,397],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)#{1}":[384,395],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)#{2}":[396,397],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)":[398,429],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{1}":[400,411],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{2}":[412,413],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{3}":[414,416],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{4}":[417,420],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{5}":[421,422],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{6}":[423,425],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{7}":[426,427],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{8}":[428,429],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting the API Key via Preferences.jl [​](#Setting-the-API-Key-via-Preferences.jl)":[430,439],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting the API Key via Preferences.jl [​](#Setting-the-API-Key-via-Preferences.jl)#{1}":[432,439],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`) [​](#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs))":[440,443],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`) [​](#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs))#{1}":[442,443],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Instant Access from Anywhere [​](#Instant-Access-from-Anywhere)":[444,456],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Instant Access from Anywhere [​](#Instant-Access-from-Anywhere)#{1}":[446,456],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Open Source Alternatives [​](#Open-Source-Alternatives)":[457,460],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Open Source Alternatives [​](#Open-Source-Alternatives)#{1}":[459,460],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setup Guide for Ollama [​](#Setup-Guide-for-Ollama)":[461,478],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setup Guide for Ollama [​](#Setup-Guide-for-Ollama)#{1}":[463,478],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)":[479,491],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{1}":[481,484],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{2}":[485,486],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{3}":[487,488],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{4}":[489,491],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)":[492,534],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{1}":[494,495],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{2}":[496,497],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{3}":[498,508],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{4}":[509,510],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{5}":[511,534],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)":[535,636],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{1}":[537,577],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{2}":[578,579],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{3}":[580,582],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{4}":[583,636],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to quickly create a prompt template? [​](#How-to-quickly-create-a-prompt-template?)":[637,718],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to quickly create a prompt template? [​](#How-to-quickly-create-a-prompt-template?)#{1}":[639,718],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Do we have a RecursiveCharacterTextSplitter like Langchain? [​](#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?)":[719,738],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Do we have a RecursiveCharacterTextSplitter like Langchain? [​](#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?)#{1}":[721,738],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)":[739,758],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{1}":[741,742],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{2}":[743,744],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{3}":[745,747],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{4}":[748,758]},"last_import":{"mtime":1712727848365,"size":29627,"at":1740449882758,"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b"},"key":"PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl 2.md"},