
"smart_sources:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md": {"path":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12462052,-0.01701361,-0.01037548,0.00559019,-0.07935473,0.03892091,0.00504056,0.01135017,0.04899505,-0.00070982,-0.01043308,0.00067157,0.02777604,-0.00634015,-0.03496898,-0.03927879,-0.01853378,-0.01327695,-0.01421516,0.00186335,0.04285183,-0.06480516,-0.05648843,-0.05778453,0.05657874,0.01129268,-0.01811886,-0.04018733,-0.04704988,-0.2334249,0.02742094,0.03278071,0.00134326,-0.07283312,-0.0510258,-0.03244847,-0.02584735,0.0696049,-0.03686293,0.0698794,-0.00356771,0.02499811,-0.00033768,-0.02051705,0.01111921,-0.02601317,-0.00558571,0.00720225,0.02083687,0.00053664,-0.07181869,0.03394726,0.00480612,0.04151957,0.06007341,0.02679052,0.05670377,0.09491459,-0.01265177,0.02001502,0.04542248,0.03118857,-0.22930214,0.05488176,0.02253999,0.04378456,-0.01918563,-0.03338096,0.00833116,0.0275226,0.02061269,0.02222197,-0.03464198,0.01674767,0.03449135,-0.00409426,-0.0433192,-0.03048036,-0.03178408,0.02539283,0.00674303,0.00593528,-0.01200503,-0.02707626,-0.01651694,0.04080059,0.03561531,0.06369603,0.06492291,-0.10942049,0.00198059,0.02436271,0.06691538,0.05615361,0.04002314,0.01390236,0.04103009,0.03606307,0.0206484,0.08450311,-0.00647681,0.00151412,-0.01701796,0.01497222,0.04894093,-0.02808218,-0.05166207,-0.0449714,-0.0395701,0.03256748,0.05503253,0.03060731,0.11616917,-0.06182407,0.00321685,-0.00897033,-0.01004328,-0.00418635,0.04068986,0.01661033,-0.04662364,0.04482589,0.01346941,-0.01747622,-0.01445226,-0.0105365,0.03811176,0.06802789,-0.00644256,-0.01878124,0.03834636,0.03269327,-0.13528945,-0.04906611,-0.01781164,-0.01175114,0.01908229,-0.00289001,0.00411698,0.00461113,-0.01750662,-0.04792153,0.06090592,-0.08744796,-0.06295089,0.03394295,0.05279423,0.00261375,0.05251276,0.05391154,-0.0206448,-0.03807524,-0.02658267,-0.0706789,-0.03853709,-0.01683148,0.08346226,0.11915821,-0.02653178,-0.00872143,-0.01760464,-0.03070301,-0.01846558,0.12030365,0.00572204,-0.12138829,-0.02123125,0.0606221,0.00003174,-0.0903062,0.00378981,-0.00022598,-0.02156133,0.0272498,0.08046236,-0.00207399,-0.04866379,-0.02546404,-0.06177967,0.00041918,-0.00655379,-0.02773609,-0.01373166,0.01601885,-0.03284134,-0.09868522,-0.00116559,-0.05020042,0.01341054,0.03944923,-0.00732723,0.00597777,-0.08211068,0.04836386,-0.02320906,0.00200136,-0.02435897,-0.03748547,0.06958196,-0.01469649,-0.08406574,-0.02890917,-0.01783729,-0.01125258,-0.0239582,0.04111727,-0.00258911,-0.05446277,0.04080769,0.02243241,-0.00259467,-0.00558524,0.03127815,0.05332045,-0.04180969,0.01683527,0.02369902,0.02339138,-0.01771162,0.01365872,-0.01726044,0.00373534,-0.08206472,-0.18476363,-0.07347734,-0.08161639,-0.03929221,0.00225958,-0.02182522,0.0242715,-0.02665858,0.04310483,0.0584893,0.05270884,-0.04051765,0.00913679,0.0404741,-0.00038991,-0.04057894,-0.04876651,-0.00351332,-0.04414727,0.04023787,-0.01268051,0.02280638,-0.0088997,-0.06653975,0.00862734,-0.0519353,0.13546056,0.05792399,-0.02682831,0.00781402,0.07912124,0.0317634,0.00170011,-0.04301001,0.01481134,0.03847067,-0.00773379,0.02139137,-0.04393269,0.01277573,-0.09517877,-0.00820158,0.01422244,-0.06304901,-0.05717519,0.01238445,-0.0063003,0.00020912,-0.0168297,0.0867584,0.05652383,-0.00208919,0.04711685,0.03258619,0.05941696,-0.0192872,-0.07760716,-0.01324146,0.01125252,0.08254641,-0.0400044,-0.03885664,0.03809625,-0.01251301,0.04521798,0.06921291,-0.01710124,-0.02648678,0.00871109,-0.05093245,0.00251416,0.13219871,-0.01157383,-0.01093691,0.01439507,0.03222856,0.0766203,-0.05106222,0.0456803,0.02195616,-0.00693873,-0.07514898,0.03575377,0.04841411,0.05893698,-0.0051826,0.0524951,0.05730066,-0.01222357,-0.00043402,0.00013405,0.01830175,-0.04322325,0.02500047,0.00937537,0.0246685,-0.24663407,0.03418404,0.00640734,0.03045476,-0.00351384,-0.01441947,0.02689672,-0.02325507,0.02522727,-0.0215818,0.09485552,0.01583175,0.02653013,-0.0674006,0.02522461,0.01301771,-0.06405783,-0.00247639,0.05457803,0.01895482,0.06205139,0.08787765,0.22360206,0.01386105,0.00138507,-0.02304628,0.01075704,0.04719121,-0.03561616,0.04949195,0.01565249,0.0055289,0.05548463,-0.01989099,-0.05797906,0.01364882,-0.01695158,0.02639747,-0.03201872,0.03755985,-0.09571958,-0.00900869,0.00978722,0.02481294,0.14178906,0.0498367,-0.02688218,-0.12746939,0.07609525,0.06167405,-0.06234355,-0.10392302,-0.03382395,-0.01386076,0.00137822,0.05354841,-0.01248093,-0.02235953,0.01526053,0.00409996,0.01371033,0.01740619,0.05317295,0.01895087,-0.00490015],"last_embed":{"hash":"1slhhkm","tokens":473}}},"last_read":{"hash":"1slhhkm","at":1753423482682},"class_name":"SmartSource","last_import":{"mtime":1753366216211,"size":11024,"at":1753423416052,"hash":"1slhhkm"},"blocks":{"#---frontmatter---":[1,6],"#Lottery History, Lotto Software, Systems, Strategies":[8,95],"#Lottery History, Lotto Software, Systems, Strategies#{1}":[10,17],"#Lottery History, Lotto Software, Systems, Strategies#<u>A Brief History of My Lottery, Lotto Experience with Software, Systems, Strategies</u>":[18,20],"#Lottery History, Lotto Software, Systems, Strategies#<u>A Brief History of My Lottery, Lotto Experience with Software, Systems, Strategies</u>#{1}":[19,20],"#Lottery History, Lotto Software, Systems, Strategies#Is Lottery Rigged or Government-Run Rip-Off?":[21,22],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_":[23,63],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{1}":[25,36],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{2}":[37,37],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{3}":[38,39],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{4}":[40,49],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{5}":[50,51],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{6}":[52,53],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{7}":[54,54],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{8}":[55,55],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{9}":[56,56],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{10}":[57,57],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{11}":[58,59],"#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{12}":[60,63],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>":[64,95],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{1}":[66,67],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{2}":[68,68],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{3}":[69,69],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{4}":[70,70],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{5}":[71,71],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{6}":[72,72],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{7}":[73,73],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{8}":[74,74],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{9}":[75,75],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{10}":[76,76],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{11}":[77,77],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{12}":[78,78],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{13}":[79,79],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{14}":[80,80],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{15}":[81,81],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{16}":[82,82],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{17}":[83,84],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{18}":[85,86],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{19}":[87,87],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{20}":[88,89],"#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{21}":[90,95]},"outlinks":[{"title":"![Ion Saliu Parpaluck has won the lottery multiple times since 1980s in Romania and United States.","target":"https://saliu.com/AxiomIon.jpg","line":16},{"title":"My lottery, lotto experience, history using systems, strategies, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":25},{"title":"_**Lottery player thinks the lotteries are rigged**_","target":"https://saliu.com/bbs/messages/528.html","line":29},{"title":"_**The Best Lotto Wheels for 9, 12, 18, 21 Numbers; Super Strategies Based on Lotto Wheeling**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":38},{"title":"_**singing in Spanish**_","target":"https://www.youtube.com/watch?v=pg10ONophXY","line":42},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":46},{"title":"_**Bookie Lotteries: Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":55},{"title":"_**Online Betting Sites Survive Only by Cheating the Gamblers**_","target":"https://saliu.com/bbs/messages/844.html","line":57},{"title":"_**Customer Service Reviews of Bet365**_","target":"https://www.trustpilot.com/review/www.bet365.com","line":58},{"title":"Read Ion's lottery, lotto experiences, history using systems, strategies, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":60},{"title":"\n\n## <u>Resources in Lottery Software, Systems, Lotto Wheeling</u>\n\n","target":"https://saliu.com/content/lottery.html","line":62},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":68},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":70},{"title":"_**Lotto, Lottery Strategy Tutorial**_","target":"https://saliu.com/bbs/messages/818.html","line":71},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":72},{"title":"_**<u>Lottery Skip System Software</u>**_","target":"https://saliu.com/skip-strategy.html","line":73},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":74},{"title":"_**Practical lottery, lotto filtering in software**_","target":"https://saliu.com/filters.html","line":75},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":76},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":77},{"title":"_**Lotto wheels**_","target":"https://saliu.com/lotto_wheels.html","line":78},{"title":"_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_","target":"https://saliu.com/lotto-triples.html","line":81},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":82},{"title":"**lotto software, lottery software**","target":"https://saliu.com/free-lotto-lottery.html","line":83},{"title":"_**Lottery and Powerball rigged: Prize money distributed evenly in all regions**_","target":"https://saliu.com/bbs/messages/534.html","line":87},{"title":"_**Lottery and Gambling Experience: Robbed of Prize Money**_","target":"https://saliu.com/bbs/messages/535.html","line":88},{"title":"Historical presentation of lottery, lotto experience using systems, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":90},{"title":"Forums","target":"https://forums.saliu.com/","line":92},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":92},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":92},{"title":"Contents","target":"https://saliu.com/content/index.html","line":92},{"title":"Home","target":"https://saliu.com/index.htm","line":92},{"title":"Software","target":"https://saliu.com/infodown.html","line":92},{"title":"Search","target":"https://saliu.com/Search.htm","line":92},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":92},{"title":"Read the presentation of that lottery strategy, software: 12-number combinations in lotto 6 games.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":94}],"metadata":{"created":"2025-07-24T22:10:02 (UTC +08:00)","tags":["lotto","lottery","software","experience","history","programs","computer","strategy","strategies","system","systems","win","winning","combinations","formula","mathematics","math"],"source":"https://saliu.com/bbs/messages/532.html","author":null}},"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12307151,-0.03456929,-0.01278457,-0.00131427,-0.06462782,0.03402149,-0.00284199,0.0199841,0.04975881,-0.00964206,-0.02300797,-0.02613694,0.03026432,0.00227193,-0.00224149,-0.01901652,-0.01698126,-0.00824046,-0.03035981,0.01525483,0.05980842,-0.04373589,-0.07631613,-0.06695912,0.05882218,0.02102198,-0.02316604,-0.04886609,-0.02949506,-0.15768123,0.00433164,0.0307193,0.00818781,-0.05445806,-0.03568752,-0.04739256,-0.01767333,0.09079827,-0.02471266,0.06417004,0.0014193,0.03914586,-0.02146291,-0.00590954,-0.00059011,-0.03507544,0.00199124,0.01610041,0.01435298,-0.00063413,-0.07572094,0.00576333,-0.01486961,0.03188343,0.0751755,0.02758612,0.05565003,0.05254897,0.02422425,0.02617993,0.0449295,0.04328351,-0.22325057,0.05948099,-0.00345606,0.03398673,-0.01780063,-0.01114912,0.00676106,0.04198344,0.01881336,0.04084199,-0.03108952,0.04902414,0.04017548,0.01059135,-0.03882902,-0.06987102,-0.04736638,-0.00155907,-0.0266018,-0.00510384,-0.03454389,-0.04529626,-0.02405426,0.02971252,0.0358749,0.0829907,0.07097718,-0.05808247,0.01346576,0.02294365,0.07851827,0.05228074,-0.00716456,0.00556506,0.03653701,-0.0121686,0.01705843,0.11417893,-0.01974297,0.01837718,-0.0093887,0.0028527,0.06386034,-0.01492244,-0.04633703,-0.04877292,-0.05808361,0.01212486,0.05825321,0.04006752,0.09326225,-0.07492764,0.00317703,-0.01278941,0.00346539,-0.01114771,0.06594082,-0.00207428,-0.04729817,0.01215157,0.01992389,-0.01249067,-0.01267563,0.02342184,0.03028106,0.08340848,-0.00767183,-0.00961484,0.02176762,0.04451464,-0.14004983,-0.02450147,-0.01885242,-0.01070817,0.03462984,-0.0438109,0.00008095,-0.00348294,-0.01900984,-0.03276371,0.06586488,-0.11920094,-0.06122859,0.06160385,0.06149034,0.00021338,0.03268541,0.02076807,-0.01647453,-0.02329946,0.00403161,-0.04657968,-0.01815668,-0.00257544,0.1052544,0.09182818,-0.02543408,-0.01655088,-0.01928377,-0.03917145,-0.03972889,0.16072078,-0.00560955,-0.15704137,-0.02035192,0.06566858,-0.00063383,-0.09185394,-0.00293425,0.00670685,-0.04538905,0.01667853,0.11708159,0.00602016,0.00479833,-0.03488795,-0.03994671,-0.01509134,0.01891175,-0.05147653,-0.04934787,0.01487948,-0.03504697,-0.09280208,-0.01357523,-0.04879029,0.02246812,0.06155558,-0.00108481,-0.03049888,-0.04138872,0.02410536,-0.01781303,-0.01743569,-0.03868967,-0.03846279,0.06113116,-0.02449629,-0.04367087,-0.02930511,-0.01853675,0.00083864,-0.01423674,0.02927036,-0.00239916,-0.05958911,0.05058501,0.01087561,-0.0045113,0.01634278,0.0352439,0.05805218,-0.04884043,0.01733428,0.00868198,0.02850921,-0.01179614,0.00634176,-0.03299706,0.02149123,-0.08665882,-0.21558362,-0.03291918,-0.04816753,-0.01832038,-0.02128986,0.00574935,0.01798528,-0.01965842,0.01812542,0.10321955,0.07325131,-0.05278286,-0.00651009,-0.0011443,-0.00823633,-0.0477946,-0.06152008,-0.06025559,-0.02359179,0.04859025,0.00400629,0.0008759,-0.01602532,-0.08809043,0.00454997,-0.02948881,0.12461416,0.05974283,-0.02192571,-0.01743009,0.09981166,0.02965307,-0.02474241,-0.04470668,0.00334943,0.03651298,-0.03705905,0.05704262,-0.03773357,-0.00780992,-0.08978214,0.03011726,0.00630121,-0.071757,-0.02496,0.00061565,0.01391868,-0.0225665,0.00097839,0.0559524,0.03530899,0.004342,0.0391375,0.04448773,0.07314432,0.00968557,-0.06870889,-0.00075075,0.00204811,0.03559041,-0.02295746,-0.03744557,0.02288037,-0.02581952,0.00265549,0.04040035,-0.02681559,-0.0590279,-0.01993795,-0.0433571,0.01079139,0.08814004,0.00514955,-0.03479513,0.00616166,0.0166066,0.06334662,-0.04135655,0.03103361,0.00191017,-0.01629087,-0.0864735,0.05305035,0.08099882,0.06951798,0.03307366,0.04406285,0.03920046,0.02421072,-0.01534396,-0.01380697,0.00176132,-0.02701321,0.00061718,0.03526467,0.01213878,-0.26122594,0.05461436,0.03566582,0.05718237,-0.00360651,-0.00200258,0.01149346,-0.0098279,-0.00882794,-0.00909787,0.07881384,0.03575011,-0.00016397,-0.0433366,-0.03051882,0.006399,0.00503182,-0.02692133,0.04363858,0.04472402,0.0238707,0.06013474,0.23889591,0.04334069,-0.00412915,0.00762187,0.0163923,0.03668328,-0.0068609,0.06142198,0.02469607,0.00062571,0.0655949,0.00116063,-0.05883676,0.0412635,-0.00578743,0.03260698,-0.01423666,0.0535481,-0.11925679,0.01625146,-0.01900447,0.01945061,0.12662311,0.04119217,-0.03223076,-0.10085806,0.03273504,0.03919626,-0.07410311,-0.09889176,-0.03726974,0.00389149,0.00081534,0.0565398,0.03474226,-0.03200901,0.0139756,0.00831928,0.01360456,-0.01496505,0.0801295,0.00677563,0.0132964],"last_embed":{"hash":"ljcgj9","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"ljcgj9","at":1753423481706},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#---frontmatter---","lines":[1,6],"size":260,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10553661,-0.01403979,0.00067424,-0.00014594,-0.07500946,0.03996404,0.03550604,0.00680807,0.04282901,0.00114552,-0.01303596,0.00076144,0.02488567,0.00091855,-0.03902113,-0.03241875,-0.0120594,-0.00166365,-0.02373528,-0.002059,0.02979949,-0.07253864,-0.05177895,-0.06245669,0.05006289,0.00686111,-0.02567171,-0.04571258,-0.04716054,-0.2306575,0.0239631,0.03232873,-0.00235161,-0.07262656,-0.05428922,-0.03219512,-0.03085475,0.05869678,-0.04094292,0.06248242,0.00958974,0.01076609,0.00098051,-0.02681438,0.02198282,-0.02608674,0.01099993,-0.00607088,0.0286228,0.00373932,-0.06823102,0.04333923,0.00753056,0.03923518,0.04973092,0.02821652,0.05427926,0.10800672,-0.0167147,0.01440905,0.031019,0.02744283,-0.22526184,0.05111301,0.03588829,0.04103452,-0.02383488,-0.02978183,0.00213766,0.02442487,0.03139701,0.01820094,-0.02997072,0.0267847,0.03923158,-0.01753802,-0.04731458,-0.01185974,-0.02897119,0.04150701,0.00153726,-0.00848779,-0.0079454,-0.02459239,-0.00871477,0.04877424,0.03354083,0.06014455,0.05255121,-0.10991974,0.00726462,0.02231055,0.0498571,0.05108485,0.04057703,0.02287067,0.04734379,0.03169896,0.01455313,0.09843035,0.00587471,-0.01575853,-0.01973913,0.02526138,0.03739156,-0.02078101,-0.05637261,-0.04702095,-0.02688523,0.0411177,0.05918364,0.03222582,0.11111206,-0.03901074,-0.01291417,0.00176242,-0.01789384,-0.00305932,0.02465471,0.02341321,-0.05781952,0.04539249,0.005186,-0.02015103,-0.02125934,-0.0294183,0.04333212,0.06119685,-0.00330157,-0.00331658,0.0431816,0.02726042,-0.13967499,-0.06399872,-0.01663172,-0.01685147,0.01141027,-0.00481024,0.00276252,0.01276001,-0.00329066,-0.04333874,0.05593538,-0.07504795,-0.06634506,0.01771984,0.04871425,0.00596529,0.05666407,0.04845155,-0.02033496,-0.04398581,-0.03720424,-0.07553427,-0.02984313,-0.0195539,0.0858056,0.12924652,-0.02399075,0.01165086,-0.02452469,-0.03248468,-0.00235411,0.11995173,0.00229103,-0.1057243,-0.02130968,0.05173193,0.00325602,-0.08439443,0.01111988,0.00205839,-0.02427306,0.0304222,0.07913363,-0.00889062,-0.05289359,-0.01821882,-0.07809836,0.0069819,-0.01646347,-0.03083929,-0.00226324,0.01443038,-0.0327601,-0.10437561,0.00790962,-0.04489518,0.00942389,0.04203879,0.00159164,0.02007381,-0.09629107,0.03953184,-0.02065576,0.00327027,-0.02681891,-0.04603944,0.06075703,-0.0128168,-0.08436395,-0.01736736,-0.01223431,0.003767,-0.02581115,0.04826209,-0.01566207,-0.04280838,0.04258307,0.03188525,-0.00367143,-0.0050195,0.02974867,0.06071384,-0.03202951,0.01505943,0.02576176,0.0233489,-0.00499014,0.01623889,-0.00403077,-0.00576226,-0.08580217,-0.19017066,-0.07734722,-0.09246159,-0.0287947,-0.00044394,-0.02620355,0.02382536,-0.03594169,0.04323625,0.04505857,0.06277278,-0.06089042,0.00758886,0.06085754,-0.00667038,-0.02663959,-0.037681,-0.00415308,-0.04092544,0.0340191,-0.01576318,0.01953003,-0.0070223,-0.07186779,0.01159356,-0.04717298,0.14273193,0.06005335,-0.02114823,-0.00038832,0.06585679,0.01196413,0.01721328,-0.0515341,0.00550304,0.04437419,-0.00690908,0.00384525,-0.04568461,0.01970299,-0.09867062,-0.01456576,0.0074289,-0.04310925,-0.06858159,0.012048,-0.00858506,0.00459972,-0.00887981,0.08984546,0.0612154,-0.00895902,0.04935006,0.03055423,0.05300498,-0.0285834,-0.07590111,-0.00483372,0.0067995,0.09061486,-0.03574883,-0.03418986,0.04523005,-0.00879258,0.05833356,0.07839796,-0.02259498,-0.02685855,0.01771975,-0.05458676,-0.00373627,0.12845594,-0.01404953,-0.0178367,0.0137279,0.03334749,0.06808159,-0.05487676,0.04305963,0.03062999,-0.00455269,-0.07723271,0.02558733,0.04607851,0.05020005,0.00500601,0.05501065,0.05156539,-0.02555107,0.01399325,0.02088946,0.02860797,-0.0436202,0.03423083,-0.00264537,0.0272599,-0.23226449,0.03634728,-0.01682219,0.03389728,0.00776797,-0.01490711,0.03034337,-0.0242717,0.04153257,-0.02984597,0.11076685,0.01632874,0.04178976,-0.06416648,0.03215276,0.01759725,-0.06312182,-0.00177393,0.04812394,0.00708272,0.08377305,0.1004305,0.2197766,0.00269514,0.00675495,-0.02307239,0.00515042,0.0402787,-0.04229501,0.02851313,0.01457095,-0.0053683,0.04238367,-0.01715107,-0.04768457,0.00993024,-0.02818484,0.02982367,-0.03021573,0.03181669,-0.07720229,-0.02387998,0.00282029,0.01952006,0.14044584,0.04961032,-0.04458275,-0.12267952,0.08480398,0.07714763,-0.05572338,-0.1089783,-0.04212271,-0.00476867,-0.00924878,0.04006164,-0.02784741,-0.02278803,0.00940885,0.00217685,0.01899807,0.01890258,0.03831626,0.00419808,-0.00561595],"last_embed":{"hash":"1dauccc","tokens":436}}},"text":null,"length":0,"last_read":{"hash":"1dauccc","at":1753423481740},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies","lines":[8,95],"size":10742,"outlinks":[{"title":"![Ion Saliu Parpaluck has won the lottery multiple times since 1980s in Romania and United States.","target":"https://saliu.com/AxiomIon.jpg","line":9},{"title":"My lottery, lotto experience, history using systems, strategies, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":18},{"title":"_**Lottery player thinks the lotteries are rigged**_","target":"https://saliu.com/bbs/messages/528.html","line":22},{"title":"_**The Best Lotto Wheels for 9, 12, 18, 21 Numbers; Super Strategies Based on Lotto Wheeling**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":31},{"title":"_**singing in Spanish**_","target":"https://www.youtube.com/watch?v=pg10ONophXY","line":35},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":39},{"title":"_**Bookie Lotteries: Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":48},{"title":"_**Online Betting Sites Survive Only by Cheating the Gamblers**_","target":"https://saliu.com/bbs/messages/844.html","line":50},{"title":"_**Customer Service Reviews of Bet365**_","target":"https://www.trustpilot.com/review/www.bet365.com","line":51},{"title":"Read Ion's lottery, lotto experiences, history using systems, strategies, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":53},{"title":"\n\n## <u>Resources in Lottery Software, Systems, Lotto Wheeling</u>\n\n","target":"https://saliu.com/content/lottery.html","line":55},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":61},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":63},{"title":"_**Lotto, Lottery Strategy Tutorial**_","target":"https://saliu.com/bbs/messages/818.html","line":64},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":65},{"title":"_**<u>Lottery Skip System Software</u>**_","target":"https://saliu.com/skip-strategy.html","line":66},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":67},{"title":"_**Practical lottery, lotto filtering in software**_","target":"https://saliu.com/filters.html","line":68},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":69},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":70},{"title":"_**Lotto wheels**_","target":"https://saliu.com/lotto_wheels.html","line":71},{"title":"_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_","target":"https://saliu.com/lotto-triples.html","line":74},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":75},{"title":"**lotto software, lottery software**","target":"https://saliu.com/free-lotto-lottery.html","line":76},{"title":"_**Lottery and Powerball rigged: Prize money distributed evenly in all regions**_","target":"https://saliu.com/bbs/messages/534.html","line":80},{"title":"_**Lottery and Gambling Experience: Robbed of Prize Money**_","target":"https://saliu.com/bbs/messages/535.html","line":81},{"title":"Historical presentation of lottery, lotto experience using systems, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":83},{"title":"Forums","target":"https://forums.saliu.com/","line":85},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":85},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":85},{"title":"Contents","target":"https://saliu.com/content/index.html","line":85},{"title":"Home","target":"https://saliu.com/index.htm","line":85},{"title":"Software","target":"https://saliu.com/infodown.html","line":85},{"title":"Search","target":"https://saliu.com/Search.htm","line":85},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":85},{"title":"Read the presentation of that lottery strategy, software: 12-number combinations in lotto 6 games.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":87}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10217192,-0.00364961,-0.01421879,0.01124214,-0.08511233,0.02407287,0.03265238,0.00232914,0.0323399,-0.01953256,-0.01154868,-0.01735763,0.04853086,0.00905102,-0.03047058,-0.01692097,-0.00515572,-0.0002775,-0.01777922,-0.01566094,0.06648123,-0.05504299,-0.03392072,-0.09217806,0.04941416,0.01545848,-0.01853807,-0.04610255,-0.03219543,-0.21483085,0.01504323,0.03493124,-0.01542013,-0.08902551,-0.04946309,-0.02874381,-0.0325909,0.06161369,-0.0369534,0.06734003,0.00467252,0.02526852,-0.01364328,-0.03562886,0.01198338,-0.05003829,-0.00236481,-0.00244216,0.02238458,0.00281231,-0.07949031,0.04506506,0.01162956,0.04009303,0.06063788,0.02585851,0.03903546,0.09571511,-0.01959353,0.00762107,0.03462781,0.02753116,-0.23378119,0.04355505,0.01047271,0.02060039,-0.02824548,-0.02359226,-0.01942018,0.03430996,0.03005768,0.02406754,-0.04003607,0.03198576,0.04552354,-0.023365,-0.05259246,-0.02574276,-0.03100482,0.05083508,0.00354803,-0.00390039,-0.01043135,-0.02642023,-0.0083569,0.05894807,0.04084845,0.0511847,0.0598974,-0.10097593,0.0025453,0.02217029,0.047405,0.04876941,0.02489303,0.03942962,0.06664972,0.00275875,0.02868883,0.12324704,-0.00361074,-0.00584533,-0.03110387,0.00694326,0.03622219,-0.01305325,-0.04376374,-0.0460139,-0.02938257,0.05901364,0.0303551,0.01995495,0.10978901,-0.03829813,-0.03102632,0.00368265,-0.01698943,-0.01769721,0.03664441,0.00570571,-0.04341277,0.03294538,0.01757075,-0.02181692,-0.007056,-0.02507341,0.02947629,0.07473319,-0.00795438,-0.00255933,0.06560919,0.04674318,-0.13469079,-0.05400114,0.00026392,-0.02314493,0.00643982,-0.01972011,-0.00509894,0.01983262,0.01223572,-0.04071889,0.07022212,-0.08321581,-0.06356435,0.05539052,0.05072193,-0.00795011,0.04571224,0.04550415,-0.01818842,-0.0460179,-0.01210349,-0.05984928,-0.02246098,-0.0082961,0.09404445,0.11358402,-0.01506133,-0.02030601,-0.0320763,-0.03991264,-0.00508375,0.18080853,-0.012148,-0.09793875,-0.008415,0.05140077,0.00490867,-0.06914674,0.01227179,0.00164248,-0.04002644,0.01442278,0.08527718,-0.00940199,-0.0161351,-0.03680302,-0.06758855,0.01002753,-0.02205952,-0.03812474,-0.02351869,0.02869518,-0.02767352,-0.0951864,0.01269698,-0.05851658,0.02335018,0.04897013,-0.01077838,0.0410746,-0.07099732,0.02707173,-0.00825454,-0.00943176,-0.03247116,-0.06383913,0.05875285,-0.02628087,-0.06613227,-0.00020787,-0.00839375,-0.00409842,-0.00660345,0.02371074,-0.02548073,-0.02636413,0.01992325,0.04197476,-0.00971986,-0.00349979,0.02022312,0.059398,-0.04296495,0.0148587,-0.00037299,0.02213723,-0.0150444,0.00893263,0.00608776,0.00915647,-0.09616191,-0.19681317,-0.06293833,-0.0963547,-0.04573543,-0.00387487,-0.01749179,0.0367148,-0.03650113,0.05584652,0.04755548,0.07993907,-0.07273934,0.01199099,0.06504409,0.00687767,-0.02818337,-0.0242448,-0.01572412,-0.02430826,0.02555425,-0.01614743,0.01736126,-0.00091635,-0.05155391,0.00715975,-0.0563206,0.13712503,0.0635676,-0.02041773,-0.0197465,0.06488501,0.02300814,-0.00131697,-0.05224678,0.00745551,0.05888863,-0.01421182,0.01399054,-0.0305978,0.00505099,-0.05513446,0.0159635,0.01349693,-0.0180105,-0.0565427,-0.01093688,0.0111428,-0.00074951,0.00640118,0.07276424,0.07264047,0.01070287,0.04930131,0.0484307,0.03760295,-0.01298469,-0.07371542,-0.0047854,0.00849212,0.06713293,-0.03058624,-0.04142242,0.03843145,0.0039361,0.07328335,0.05865838,-0.03715198,-0.04069433,0.01408382,-0.05850134,0.01310488,0.10189353,-0.02640805,-0.02079481,0.00508531,0.04806798,0.06704134,-0.06704061,0.02122775,0.02916673,0.0000379,-0.0776179,0.02375125,0.03096791,0.05571734,0.01188196,0.06131849,0.04622005,-0.02774431,0.01086394,0.01736446,0.01024683,-0.05259847,0.03028996,-0.00921697,0.03933048,-0.25116867,0.03447534,0.0071546,0.00882558,-0.00292321,0.00045715,0.03110887,-0.05644938,0.02800448,-0.03201105,0.09998054,0.03945248,0.03327903,-0.05565278,0.01843655,0.03112918,-0.02108617,-0.02028503,0.06764539,0.00640324,0.05523802,0.09470214,0.22772628,0.01352418,0.00134576,0.0017595,0.01146554,0.01965579,-0.03791085,0.03327788,0.02086151,-0.0066831,0.06808273,-0.02387578,-0.06317553,0.04703829,-0.02866716,0.01719947,-0.02338742,0.03540646,-0.09016474,-0.01037793,-0.01269044,0.01698844,0.13390239,0.03381398,-0.0533292,-0.09573372,0.06388982,0.04992509,-0.06988935,-0.10668727,-0.03523459,-0.01300971,-0.01119876,0.02018064,-0.01564263,-0.03652416,0.00652566,-0.01862963,0.00598844,0.02681347,0.03463451,0.01523163,0.01921226],"last_embed":{"hash":"9yi4ox","tokens":143}}},"text":null,"length":0,"last_read":{"hash":"9yi4ox","at":1753423481870},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#{1}","lines":[10,17],"size":408,"outlinks":[{"title":"![Ion Saliu Parpaluck has won the lottery multiple times since 1980s in Romania and United States.","target":"https://saliu.com/AxiomIon.jpg","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09833162,-0.04097243,0.00173127,-0.01343873,-0.0408888,0.0485688,0.03681264,0.01969591,0.05490645,-0.00057404,0.00095222,-0.00140109,-0.00509842,0.01312879,-0.03662954,-0.03938431,-0.02028676,0.0150061,-0.0375645,0.00956828,0.03512345,-0.08809687,-0.07244625,-0.04750606,0.04913479,-0.01009415,-0.02695524,-0.04207737,-0.05008144,-0.22643137,0.03889144,0.02896797,0.01048032,-0.04322563,-0.07589487,-0.04015374,-0.01481486,0.05216185,-0.040401,0.06034759,0.01133715,0.01047994,0.00445763,-0.0202387,0.02140949,-0.0094683,0.02483992,0.00009267,0.04279734,0.00709085,-0.06604962,0.03230747,0.00283903,0.00747448,0.05215301,0.03274712,0.06150638,0.09134195,0.00935435,0.00974153,0.02503742,0.03328356,-0.21774459,0.05726631,0.01776746,0.02040846,-0.00431767,-0.02396413,0.01595402,0.03980615,0.04805195,0.02589526,-0.02624681,0.0393718,0.03723139,-0.02429835,-0.03720816,-0.03135733,-0.01271155,0.01609262,-0.00633762,-0.01763178,-0.00709368,-0.03855357,-0.00035808,0.04702149,0.01063689,0.051158,0.04536786,-0.08610768,0.01794557,0.03027425,0.04256664,0.0507294,0.01821456,-0.00981775,0.02379553,0.02501391,0.01442427,0.09383155,0.02853514,-0.02058572,-0.00916599,0.02647013,0.02975275,-0.00978886,-0.04260793,-0.04702887,-0.01357976,0.05381851,0.06348582,0.03970417,0.08680685,-0.02693529,-0.02964908,-0.0031806,-0.02558612,0.01322242,0.02623705,0.01635519,-0.07831368,0.03184218,0.00704738,-0.00134754,-0.04423362,-0.01823674,0.05184812,0.07273778,-0.00171467,0.00808796,0.01281775,-0.00961212,-0.14334585,-0.05914351,-0.02335724,0.0012796,0.00118772,-0.0002135,0.00357575,0.02537118,-0.01594104,-0.05232875,0.06103176,-0.08971158,-0.06433059,0.00836883,0.02969547,0.0038375,0.05243799,0.037581,-0.00974898,-0.04119888,-0.04885257,-0.07091947,-0.03892604,-0.01695539,0.10175312,0.10744303,-0.03796003,0.01738661,-0.04140221,-0.04527105,-0.01679217,0.09727453,-0.00771651,-0.12452491,-0.02996488,0.0418964,0.00293846,-0.09249406,0.00857239,-0.01187186,-0.03186626,0.03890209,0.07788824,-0.00834689,-0.05379245,-0.02491127,-0.07220571,0.00624179,-0.00206027,-0.04813304,-0.01301331,0.00440033,-0.02634538,-0.08733344,0.00504724,-0.03337865,0.00873858,0.04912571,-0.00008467,0.01869758,-0.10295396,0.03223493,-0.01142709,-0.01527893,-0.02595122,-0.02558452,0.0616571,-0.00517621,-0.08200449,-0.00998261,0.00817509,0.01506083,-0.03351283,0.07140282,-0.0108941,-0.0603025,0.08641518,0.02973554,0.00085126,0.01540582,0.03063276,0.08360576,-0.03646278,0.03566841,0.03280402,0.01540821,0.00256994,0.00764171,0.00860587,0.02242488,-0.08245103,-0.18813752,-0.06876467,-0.06163651,-0.00137219,0.0180454,-0.00659087,0.02669775,-0.02849006,0.04322159,0.07568343,0.05799071,-0.0695596,-0.00048235,0.05897352,-0.01512848,-0.02029605,-0.07366078,-0.02874577,-0.04462807,0.03772085,0.00175291,0.01004003,-0.01838979,-0.08467551,0.02059351,-0.04031823,0.1578923,0.04208596,-0.03434771,0.00143885,0.06633079,-0.01146863,0.02684334,-0.03642762,-0.00847741,0.02880017,-0.00396803,-0.0325379,-0.06133891,0.02246958,-0.10334112,-0.00263895,-0.01050954,-0.06191634,-0.07675356,0.01370272,-0.01295864,0.01047015,-0.0173969,0.08172231,0.05129339,-0.00504453,0.0341026,0.00489944,0.06185349,-0.02719043,-0.06440619,-0.00794521,0.00061848,0.10476782,-0.04830477,-0.03141515,0.04622317,-0.01836137,0.02188815,0.06268483,-0.01458605,-0.04432168,0.0297545,-0.03843188,-0.02219273,0.113985,0.00883155,0.00583536,0.04286442,0.0168092,0.06087995,-0.03177294,0.0492065,0.01869708,-0.00124041,-0.08281408,0.03542263,0.08142459,0.05010436,0.01578723,0.05241738,0.04112714,-0.02192731,0.01369359,0.0130726,0.02377312,-0.05226639,0.04650408,0.01251683,0.01355131,-0.22200994,0.04417767,-0.02243646,0.05084109,0.00700805,-0.01457296,0.02140474,0.00602611,0.04366524,-0.01607094,0.11511672,0.02060139,0.0287447,-0.06149986,0.02661006,-0.00456969,-0.05484682,-0.01180509,0.06350034,0.00011796,0.0842286,0.07697835,0.21729252,0.00729778,-0.02129961,0.00078126,0.00995662,0.04180247,-0.06305864,0.02823066,0.01229686,0.00409267,0.01600906,-0.00071836,-0.03753035,-0.01275507,-0.03588308,0.03341194,-0.03158631,0.02675436,-0.09539945,-0.01427426,0.00307758,0.00365136,0.15026438,0.06437527,-0.02995686,-0.1204769,0.1030537,0.08833,-0.05340678,-0.10406289,-0.0345401,-0.00684857,-0.00357289,0.0360598,-0.03391895,-0.01121838,0.00777525,-0.00785658,0.02882667,0.00891353,0.05727183,-0.00830303,-0.00189683],"last_embed":{"hash":"1e0geqk","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"1e0geqk","at":1753423481913},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_","lines":[23,63],"size":7001,"outlinks":[{"title":"My lottery, lotto experience, history using systems, strategies, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":3},{"title":"_**Lottery player thinks the lotteries are rigged**_","target":"https://saliu.com/bbs/messages/528.html","line":7},{"title":"_**The Best Lotto Wheels for 9, 12, 18, 21 Numbers; Super Strategies Based on Lotto Wheeling**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":16},{"title":"_**singing in Spanish**_","target":"https://www.youtube.com/watch?v=pg10ONophXY","line":20},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":24},{"title":"_**Bookie Lotteries: Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":33},{"title":"_**Online Betting Sites Survive Only by Cheating the Gamblers**_","target":"https://saliu.com/bbs/messages/844.html","line":35},{"title":"_**Customer Service Reviews of Bet365**_","target":"https://www.trustpilot.com/review/www.bet365.com","line":36},{"title":"Read Ion's lottery, lotto experiences, history using systems, strategies, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":38}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10196926,-0.0333549,0.0006741,-0.0099672,-0.03865628,0.04254897,0.04040991,0.02120017,0.05442708,0.00346191,0.00073992,-0.0114239,-0.00659124,0.0141633,-0.04026776,-0.03577846,-0.03014131,-0.0004326,-0.03637317,0.01403949,0.02316285,-0.09035096,-0.06483665,-0.05420281,0.05863155,-0.01139493,-0.02224513,-0.03768543,-0.04616669,-0.22358176,0.03018509,0.03508823,0.01998522,-0.04577701,-0.06621293,-0.0394763,-0.00980484,0.04807811,-0.03379829,0.06065771,0.01457021,0.00087395,0.00296746,-0.03097247,0.01998304,-0.00939398,0.02025151,0.00263097,0.04154297,0.00921211,-0.06294739,0.03721131,0.00602967,0.01048885,0.04953961,0.02708197,0.06337642,0.08884807,0.00326127,0.01323019,0.02169675,0.025511,-0.22677854,0.05460943,0.0147653,0.02411394,-0.00889248,-0.02761595,0.00842963,0.02336047,0.03875373,0.02259815,-0.02535117,0.02714995,0.05041988,-0.02530208,-0.03615168,-0.01797891,-0.01214852,0.02578988,-0.00524137,-0.0193464,-0.00670794,-0.03072082,-0.00059003,0.05344528,0.01607697,0.05843034,0.04168453,-0.08610663,0.01662377,0.02305881,0.03411433,0.05555903,0.02817883,0.00122249,0.03116168,0.03474831,0.00080027,0.09189313,0.03209383,-0.02677498,-0.00567983,0.02773332,0.03424654,-0.01139208,-0.04148611,-0.04684146,-0.00963469,0.03767612,0.06119883,0.04120537,0.0869612,-0.03702637,-0.02363956,-0.00177868,-0.03101072,0.00430825,0.02134851,0.02461768,-0.06923127,0.03297678,-0.009179,-0.00066497,-0.05121952,-0.0140765,0.05569186,0.07655077,-0.00859604,0.00498736,0.01790063,0.00100503,-0.1460474,-0.06437299,-0.03745447,0.0103861,-0.0030607,-0.01179638,-0.00086283,0.02211579,-0.01222022,-0.04169854,0.04835686,-0.08386252,-0.06298562,0.00093344,0.03658506,0.00685867,0.05847619,0.03502757,-0.00912785,-0.04145805,-0.04150532,-0.07668395,-0.04118571,-0.0262092,0.09346049,0.1155134,-0.03497592,0.02306228,-0.0300295,-0.04352942,-0.0148293,0.09575799,0.00366187,-0.11698547,-0.02630799,0.0352798,0.00961267,-0.08588327,0.0151512,-0.00452356,-0.02301553,0.04182009,0.07355911,-0.0055894,-0.04362052,-0.02378557,-0.07644225,0.00797405,0.00085008,-0.04602648,-0.00262964,0.00895994,-0.04159434,-0.09432341,0.00998887,-0.04128356,0.00294202,0.06489324,0.00513881,0.02590779,-0.11042359,0.02901586,-0.01145181,-0.01570921,-0.01893364,-0.02484153,0.05984834,-0.01495131,-0.0980269,-0.01376456,0.00557873,0.01033199,-0.03258024,0.06615502,-0.00265332,-0.04764032,0.08068882,0.02565118,0.00321181,0.01651238,0.0223648,0.07547568,-0.03560359,0.03406657,0.03691208,0.0169406,0.013157,0.01393967,0.00557546,0.01923733,-0.08621136,-0.18660612,-0.06999573,-0.07089831,-0.00535598,0.00805157,-0.01286001,0.03117745,-0.02563955,0.03929926,0.07028149,0.06011759,-0.06356314,0.0072711,0.0596262,-0.01299376,-0.02425119,-0.06362659,-0.01927305,-0.03378211,0.03726882,-0.00628589,0.01160645,-0.02706224,-0.08089828,0.0183503,-0.04151136,0.15226005,0.0580426,-0.0379074,-0.01418753,0.0685908,-0.00810682,0.02967405,-0.0535122,-0.00875794,0.03774923,-0.00051847,-0.01505501,-0.06113621,0.01857043,-0.10622517,-0.00179487,-0.00619796,-0.0491048,-0.08113624,0.01382549,-0.0106571,0.00440222,-0.01947535,0.07515196,0.04794532,-0.00726197,0.037072,0.0052115,0.06501813,-0.03043257,-0.06389419,-0.0033951,0.00843409,0.09943818,-0.0473787,-0.03432535,0.04931429,-0.00768268,0.02289092,0.07238991,-0.00790982,-0.04334984,0.03404252,-0.03814558,-0.01780238,0.12207081,0.00645664,-0.0057836,0.04040927,0.01299071,0.05607914,-0.05211999,0.05071391,0.02560351,0.00565005,-0.0962273,0.03685207,0.07477798,0.05676056,0.02341037,0.05610717,0.04550421,-0.02144844,0.01645281,0.01110421,0.03212671,-0.05179709,0.04422528,0.00539363,0.01445359,-0.22090036,0.0369786,-0.03581797,0.05103871,0.01639844,-0.01001709,0.01928481,-0.003381,0.03366184,-0.01529678,0.11501444,0.01033125,0.03499898,-0.07002026,0.03101793,0.00567993,-0.07484583,-0.00991665,0.05571138,0.00179861,0.08666734,0.08723088,0.21383959,-0.00020486,-0.02512114,-0.00290961,0.01686386,0.03557374,-0.05857008,0.0264408,0.01448086,0.00279343,0.02497551,0.00248638,-0.02882442,-0.00904034,-0.03511107,0.02776756,-0.03160602,0.01871084,-0.08736648,-0.02115062,0.00597907,0.00339174,0.14331475,0.07228635,-0.03046563,-0.1167659,0.09924813,0.08990434,-0.04844496,-0.10880163,-0.0334755,0.00678275,-0.0074235,0.03607896,-0.05173935,-0.013147,0.00547518,-0.00147337,0.02336223,0.01201629,0.05680709,-0.01862988,-0.00530101],"last_embed":{"hash":"3a77cy","tokens":400}}},"text":null,"length":0,"last_read":{"hash":"3a77cy","at":1753423482075},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{1}","lines":[25,36],"size":1432,"outlinks":[{"title":"My lottery, lotto experience, history using systems, strategies, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"_**Lottery player thinks the lotteries are rigged**_","target":"https://saliu.com/bbs/messages/528.html","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08148844,-0.01957856,-0.00160905,-0.03262911,-0.06436183,0.05305632,0.03674081,0.01019817,0.05990859,-0.03096306,0.0037795,0.00441821,0.02535596,0.02101412,-0.01653515,-0.02448082,-0.03514874,-0.01008482,-0.06713879,0.02225196,0.03098847,-0.07433188,-0.07793631,-0.07438482,0.04062292,0.01023351,-0.00979531,-0.04827371,-0.05189433,-0.22956,0.03139703,0.037307,0.05268044,-0.05851877,-0.08138932,-0.04101533,-0.030736,0.05342658,-0.04098223,0.04932951,-0.02072524,-0.00304999,0.01473572,-0.02231857,0.01436401,-0.04709974,0.01295553,0.00888664,0.05663534,0.03921751,-0.06443308,0.00831034,0.01823719,0.01797327,0.04735293,0.05369528,0.05122091,0.07769848,0.02250623,0.03125397,0.03263009,0.05578364,-0.20226002,0.0501143,-0.01167033,0.02311706,-0.01621994,-0.01291214,-0.00931895,0.04327687,0.02917995,0.03154272,-0.00854782,0.05518369,0.04789605,-0.02529391,-0.05097653,-0.02702166,-0.04491774,0.03809502,-0.00436946,-0.03526119,-0.02030115,-0.02573963,-0.01658836,0.05105385,0.0251868,0.05728142,0.02763614,-0.05983555,0.00408093,0.03492905,0.02496908,0.03546074,0.0249379,-0.01272229,0.04718289,-0.00142404,0.00416693,0.10590273,0.05720187,-0.03292718,-0.00139077,0.02104015,0.03799101,0.00342344,-0.0342143,-0.05344036,-0.00238657,0.03268158,0.05185228,0.03467709,0.06111608,-0.03584372,-0.0500217,0.00051361,-0.02205059,0.00574197,0.03338869,0.01242105,-0.05275295,0.0060772,-0.01165967,-0.02995773,-0.03516662,-0.01208607,0.04463861,0.07564618,0.01798383,0.01399151,0.03673078,0.01593243,-0.13415621,-0.0588523,-0.01506192,-0.00715729,-0.01264817,-0.01943411,-0.00473225,0.01712044,-0.01883645,-0.02050013,0.08818582,-0.08214547,-0.03476247,0.01251456,0.03223312,0.01018058,0.05508735,0.04380705,0.01627436,-0.04090017,-0.03635772,-0.07358512,-0.02412003,-0.03404849,0.11598634,0.09313334,-0.02565935,0.0127376,-0.03011407,-0.04611371,0.00254979,0.09518324,0.00554412,-0.1095618,-0.03949488,0.0388013,-0.00512616,-0.08598989,0.01584,-0.02016311,-0.03915868,0.02070561,0.0867771,-0.01065697,-0.05437435,-0.02782598,-0.04857318,0.00534968,0.00911734,-0.05143301,-0.0285949,0.00195032,-0.03946916,-0.09246183,0.02125236,-0.03604065,0.00289685,0.05956002,0.02481987,0.06008916,-0.08697011,0.01892632,0.024588,-0.01690704,0.000907,-0.02198667,0.04912793,-0.00687268,-0.07288402,-0.03703592,0.01891174,0.01780401,-0.039205,0.06429013,-0.00380038,-0.02090248,0.08754177,0.04877286,-0.01069236,0.01205212,0.03349827,0.07654397,-0.05310826,0.04694133,0.00260763,0.01799528,0.01149139,0.0308307,0.02019662,0.01758488,-0.06976012,-0.18021385,-0.03882336,-0.06925024,0.01797009,-0.01379224,-0.03431275,0.03516038,-0.02588468,0.06023177,0.07008463,0.08493206,-0.08344055,-0.00806007,0.05668312,-0.055121,-0.03849882,-0.05101692,-0.01670111,-0.02975003,0.02295934,-0.01981359,0.02300756,-0.01660321,-0.06753702,0.00958843,-0.03422336,0.16090442,0.05795492,-0.04151185,-0.03081841,0.08447105,0.00013334,0.00684251,-0.03105832,0.00016602,0.03772188,-0.00385056,-0.03216355,-0.03771396,0.01598612,-0.11244293,-0.01111635,-0.00052561,-0.06121793,-0.06637184,0.01650686,-0.01411207,0.00155702,-0.00008086,0.06267599,0.06409753,-0.0124857,0.03988942,-0.00822478,0.06192194,-0.02938515,-0.06299651,-0.01113719,0.01922095,0.0700018,-0.04080862,-0.03429226,0.02608269,-0.01613017,0.03388382,0.02597486,-0.00492873,-0.03521078,0.02563908,-0.03548169,0.01021721,0.09826637,-0.0068519,-0.01957534,0.03489493,-0.00204194,0.03405984,-0.05505921,0.04385068,0.01049006,0.03296254,-0.11838372,-0.00427817,0.07045795,0.03968492,0.01844869,0.08907571,0.05403736,-0.02354512,0.00776891,0.00359926,0.03464881,-0.03900326,0.03906171,-0.01164846,0.00958576,-0.22477072,0.02833392,-0.05260219,0.05934491,0.00791381,-0.01298523,0.02337085,0.00543577,0.05088511,-0.02057792,0.10276317,0.03254327,0.04734829,-0.06914771,0.01981271,-0.00908314,-0.05295774,-0.00247161,0.05311145,-0.00305326,0.07466845,0.04517811,0.22769415,-0.01213553,-0.02915694,0.03292396,0.01106151,0.03215063,-0.05169397,0.02864432,-0.01398506,0.00188255,0.05242711,-0.0270729,-0.0499444,-0.0151312,-0.04524327,0.04775432,-0.03006035,0.03682103,-0.07780399,-0.02821169,-0.04608829,-0.008865,0.16053091,0.07526014,-0.01707566,-0.0977566,0.08725833,0.0776618,-0.06762318,-0.09846792,-0.04273434,-0.00468952,0.00317398,0.04170713,-0.05735753,0.00893615,0.02250225,-0.01839418,0.03094644,0.01524446,0.07094361,-0.01005786,-0.01725353],"last_embed":{"hash":"12uo5go","tokens":470}}},"text":null,"length":0,"last_read":{"hash":"12uo5go","at":1753423482187},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{4}","lines":[40,49],"size":2997,"outlinks":[{"title":"_**singing in Spanish**_","target":"https://www.youtube.com/watch?v=pg10ONophXY","line":3},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07252639,-0.01788452,0.00312903,-0.01508833,-0.10072036,0.08293422,0.02665728,0.02075443,0.02699621,0.00762697,0.01448172,-0.05351142,0.06492949,0.02626182,-0.02478646,0.00539621,-0.05373187,0.05739221,-0.05612608,0.04499326,0.06743794,-0.08907457,-0.01975111,-0.04038585,0.06951918,0.01447648,0.00807692,-0.06797641,-0.02060269,-0.17728505,-0.0101573,0.01573499,0.01995477,-0.05924812,-0.06436167,-0.044533,-0.02503839,0.07719457,-0.05344207,0.03206178,0.0547066,-0.00081574,-0.00796583,-0.02351161,-0.0030617,-0.04187559,0.00559667,0.00426549,0.05366879,-0.00139767,-0.10410712,-0.01157568,-0.04528802,0.03498182,0.0696424,0.07832932,0.02767079,0.08459727,0.04985801,0.00616571,0.03506223,0.01196296,-0.22383027,0.02566123,-0.00330985,0.01829134,-0.02815789,0.00417249,-0.02965447,0.05102664,0.00466202,-0.01830209,-0.0045285,0.05124567,0.04240676,-0.00404506,-0.00771119,-0.02194236,-0.01026153,0.0344012,-0.0226554,-0.01925582,-0.00624123,-0.04286115,-0.05280156,0.04794497,0.03647207,0.00844642,0.05927568,-0.06104761,0.02191723,-0.00363709,0.00990425,0.02155177,0.0248818,-0.00029452,0.04201629,-0.02532094,-0.01363453,0.13009688,0.03745391,0.00825051,0.00833284,0.01691275,0.06678015,-0.00950907,-0.05272391,-0.03961932,0.00968584,0.01270308,0.01510848,-0.00164735,0.05012371,-0.06072113,0.00724704,-0.00362862,-0.05249202,0.0046168,0.0050683,-0.00710869,-0.01973871,0.02416894,0.02155342,-0.00615907,-0.07212494,-0.02747701,0.04431821,0.06243175,-0.01824925,0.02581182,0.02854949,0.00427033,-0.1206034,-0.01492662,-0.04694188,0.00486,0.02394089,-0.0481175,-0.02919457,-0.00032833,0.01396509,0.02300209,0.07456876,-0.11754594,-0.00497073,0.09585954,0.05578318,0.00564398,0.01653897,0.02340062,-0.01914752,-0.04689719,0.00019591,-0.04235247,-0.02771266,-0.01152994,0.05207941,0.06295871,-0.03188628,-0.00155121,-0.03162003,-0.03585023,-0.06007521,0.15149963,-0.00994984,-0.04128214,-0.01047933,0.0312959,-0.01525265,-0.07876179,-0.03118723,0.01786422,-0.05187272,0.03191149,0.11150248,0.00342129,-0.04291654,-0.02994855,-0.09511948,-0.01819069,0.01501678,-0.10794381,-0.00500262,-0.01361548,0.00407826,-0.10898149,0.05315524,-0.02726538,0.0257788,0.04241869,-0.0192188,0.13389216,-0.05602637,-0.02201009,-0.02959746,-0.02777866,-0.02775334,-0.02532963,0.01024526,-0.05849187,-0.10750992,-0.01115673,0.01852169,0.03594236,-0.02643056,0.02443368,0.04596227,-0.00648253,0.07728796,0.01811596,-0.04791391,0.02638772,0.01863494,0.05582164,-0.03451383,0.02533601,0.02466623,0.03230302,0.00890731,0.01912941,0.02303283,0.06900224,-0.06660157,-0.19653787,0.00673514,-0.05408313,-0.01258535,-0.00846392,-0.02532875,-0.01711494,-0.02433901,-0.02427192,0.04976504,0.09655796,-0.07595955,0.03525613,0.0593807,-0.0390501,-0.06072583,-0.07349367,-0.05543223,-0.00883563,0.03444323,-0.01806011,0.01208637,-0.03269733,-0.08319023,-0.00813063,-0.01932832,0.13742286,0.05185034,-0.00597436,0.02129466,0.09771328,0.02825905,-0.04119677,-0.07573567,-0.00330308,0.01507663,-0.03650127,-0.00652197,-0.05297583,0.0185437,-0.05814634,0.02463699,-0.06463088,-0.02585251,-0.06627366,0.0047743,-0.03413337,0.04202888,0.02073788,0.06223112,0.07719667,-0.01357147,0.03990895,0.01811637,0.04121936,-0.00232057,-0.10255612,0.00624612,-0.00005549,0.07255501,-0.05744712,0.02683111,0.02406282,0.00050924,0.08354324,0.03906344,-0.00831883,-0.03927073,0.02234538,-0.0185343,0.01365537,0.07641523,-0.00402603,-0.06209558,0.04580437,-0.01041357,0.01831169,-0.0565643,0.03650335,-0.01809176,-0.01344964,-0.09063314,0.01883308,0.01714764,0.06990387,0.05312287,0.04925063,0.03601757,-0.06423469,0.02224464,0.00417307,0.01118507,-0.01352337,0.04730276,0.03092621,-0.00295708,-0.23396459,0.01413585,-0.04561188,0.0485431,0.02822171,-0.0277654,0.03457613,-0.00762037,-0.01249701,0.0544729,0.07101794,0.00446098,0.03581972,-0.02615637,-0.00835961,-0.01269957,0.0190236,-0.03050203,0.05354269,0.00193297,0.02657613,0.0764218,0.23239133,-0.00768843,0.00544795,0.0118591,0.04217869,-0.0073592,-0.01873017,0.07808479,0.06096694,-0.0157278,0.05141314,0.03869329,-0.03309582,0.04959546,-0.00238243,0.05866543,-0.0012837,0.01623712,-0.06502694,-0.04489461,0.03163191,0.03587997,0.1408405,0.01123075,-0.03394155,-0.05404487,0.02350269,0.0679678,-0.07397866,-0.07136242,-0.04745528,0.00953595,-0.05266691,0.02714955,0.01197565,-0.00527588,-0.02029113,0.00850074,0.00994339,0.01079242,0.04873631,-0.00892008,-0.0200054],"last_embed":{"hash":"18qcn7u","tokens":97}}},"text":null,"length":0,"last_read":{"hash":"18qcn7u","at":1753423482346},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{5}","lines":[50,51],"size":230,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08825041,0.0080363,-0.03388385,0.00301476,-0.03363093,0.05185727,0.06150476,0.01459369,0.01049974,0.01354737,0.00732835,0.01030747,0.01685953,0.0206319,-0.03727734,-0.05495665,0.00699383,-0.02102671,-0.03493456,0.0374611,0.05036864,-0.06388014,-0.03083392,-0.0443803,0.05542186,-0.01045263,0.00317321,-0.00921234,-0.07765255,-0.20846267,0.02248169,0.04531516,-0.00548329,-0.01582652,-0.0039524,-0.03828999,-0.0139952,0.05666446,-0.03074859,0.06055462,-0.00905007,-0.00545731,-0.01404708,-0.04230044,0.00717552,-0.03625791,-0.01325503,0.00165703,-0.00107083,0.00993261,-0.04746307,-0.02060311,0.02832243,0.03278225,0.00624984,0.01903079,0.02627798,0.09903967,0.03829567,-0.0030345,0.04846363,0.02836157,-0.20017432,0.03432536,0.01068418,0.06668203,-0.02326857,-0.04733384,0.0139105,-0.0068942,-0.01775328,0.02027399,-0.03089706,0.02481716,0.0023045,-0.02434879,-0.04459658,-0.00474219,-0.03010494,0.0238101,0.00212661,-0.02014863,-0.03462561,-0.00487711,-0.00033138,0.03352939,0.01046702,0.07634345,0.06993279,-0.0547915,0.04649764,-0.00623139,0.05998782,0.03031925,0.01259722,0.06053049,0.06983289,-0.00563861,-0.07171907,0.0799161,0.05115213,-0.02448136,0.01000225,0.04317785,0.03025752,-0.02600202,-0.04536135,-0.02145866,-0.01279691,-0.02899656,0.07548775,0.0314568,0.08594815,-0.04190886,-0.00285769,0.00005,-0.00586364,0.0321655,-0.0026133,-0.00890614,-0.00480452,0.07785398,0.00569286,-0.0044794,-0.04455303,-0.0638418,0.0475588,0.09374172,0.00215567,-0.02899192,0.01539578,-0.05169158,-0.0767997,-0.02316462,-0.031152,0.04908361,0.02269028,0.01701567,0.02848154,-0.00484101,-0.05341683,-0.05368916,-0.05037813,-0.06960238,-0.02726337,0.01070267,0.02517479,0.0110529,0.06973244,0.04284685,-0.02802592,-0.04528476,-0.0653015,-0.05953262,-0.0262706,-0.02494541,0.0543134,0.14499861,-0.03159266,0.04288234,0.01648634,-0.038592,-0.01979772,0.09589725,0.00409878,-0.07428768,-0.00971543,-0.02364283,0.01458582,-0.08072454,-0.02668042,-0.00276834,0.01789326,-0.00268783,0.05690443,-0.00109854,-0.06803397,-0.02818991,-0.04015367,0.00321112,0.03788757,-0.05888249,-0.0335659,0.00605002,-0.00849129,-0.09795977,-0.00699123,-0.05525379,0.03387856,0.04982635,-0.05787747,0.02366829,-0.09200203,0.03563592,-0.00574929,-0.05472409,-0.01603488,0.02024852,0.04150762,-0.02658126,-0.11881572,-0.02035547,-0.02000417,0.00816767,-0.03902231,0.11831453,0.01343673,-0.02149275,0.08234439,0.03773912,-0.02721756,-0.02116006,-0.00558769,0.04124888,-0.0026728,0.03752507,0.02343474,-0.00348563,0.02476193,-0.01143908,0.03792628,0.0392595,-0.04058615,-0.20542638,-0.07891099,-0.07385252,-0.01239417,0.01436878,-0.05486368,-0.01080169,0.00430809,0.02171122,0.12180548,0.02216768,-0.02761391,0.00489618,0.00956228,0.02048632,-0.0427165,-0.11251127,-0.01879129,-0.03115012,0.04086601,-0.05580297,0.03621741,-0.06613816,-0.08287656,-0.01233782,-0.03078657,0.16499093,0.12276661,-0.03739178,-0.00812433,0.06516134,0.02993861,-0.04139214,-0.07890658,0.0048226,0.03771273,-0.00494268,0.04918825,-0.06021755,0.02876677,-0.0890198,-0.01734844,-0.01417638,-0.05867359,-0.04690422,0.03123482,-0.05431577,0.04429005,0.03581636,0.10330861,0.04117691,-0.04614798,0.04021462,0.03487921,0.05897833,-0.02235069,-0.04222014,0.00604348,0.01505096,0.11543617,-0.00932125,-0.00892963,0.06988397,0.01157893,0.02467031,0.06248454,-0.0061395,-0.01097893,0.02404873,0.00816766,0.00948936,0.14898841,0.02369666,-0.02784819,0.03346487,0.06134281,0.1024227,-0.02934502,0.06240015,-0.03352084,0.02330858,-0.03953905,-0.01867671,0.0288633,0.02857192,-0.01358617,0.01957488,0.02113789,-0.03637498,-0.02032664,-0.0334401,0.02419805,-0.07285304,0.00903736,-0.00104037,-0.00687724,-0.23816256,0.0086872,-0.03510276,0.06843125,-0.00235237,-0.00467914,0.01893984,0.00482938,0.0134279,-0.03983096,0.08275156,0.03959644,0.04731676,-0.02558254,0.01273171,-0.0406103,0.00042536,-0.07775585,0.05572711,-0.00466598,0.05715645,0.06014338,0.20792769,0.00288271,-0.05555229,-0.01443643,0.05758124,0.03937422,-0.02655574,-0.01050597,-0.03897571,0.00419915,-0.05958227,-0.03155582,0.00816211,-0.02511913,-0.05551157,0.04099563,0.01368474,0.02054974,-0.06965964,-0.03458588,-0.00596468,-0.02073978,0.12587708,0.07194962,-0.01423112,-0.03145405,0.09252024,0.08225439,-0.02115732,-0.0851495,-0.02191956,-0.03569869,-0.00647502,0.04830637,-0.00831401,0.03357081,0.05189133,0.01677239,0.01427005,-0.00542245,0.06206469,-0.00205014,0.00653422],"last_embed":{"hash":"1pf5mji","tokens":168}}},"text":null,"length":0,"last_read":{"hash":"1pf5mji","at":1753423482378},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{6}","lines":[52,53],"size":581,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07533167,-0.03191213,-0.00654725,0.00988217,-0.03626418,0.03335172,0.0268755,-0.010146,0.07202149,-0.0075544,-0.00935438,-0.00453067,0.00291185,-0.00410233,-0.01220824,-0.03048569,-0.01041483,-0.00223663,-0.06046689,0.05090657,0.05046892,-0.06493799,-0.04372041,-0.06247448,0.06301715,-0.00664368,-0.0130033,-0.06133995,-0.04544149,-0.2344055,-0.02176257,-0.00459304,0.02349667,-0.04327983,-0.02141665,-0.0870655,-0.00884907,0.03659237,-0.02549313,0.05345549,-0.02015438,0.03427716,0.00726958,0.00284866,0.023964,-0.02904091,-0.00342762,0.04741523,-0.00724828,-0.02356471,-0.05959763,0.00719647,-0.00525208,0.01810003,0.05388234,0.02891391,0.0735833,0.04813668,0.02827379,0.0070099,0.03978924,0.02091511,-0.20377763,0.0564856,0.05343968,0.00242258,-0.00837758,-0.01014686,-0.02490674,0.01344516,0.02403769,0.04788365,0.00446761,0.0504422,0.01492119,-0.04625569,-0.01582552,-0.04541392,-0.03346776,0.00582197,-0.02737239,-0.00815378,-0.00422248,-0.0447851,0.00540516,0.04899847,0.03303015,0.03309605,0.06906132,-0.06459627,0.04031427,0.0360707,0.06744629,0.03246944,0.04366829,0.02708915,0.01025725,-0.01021565,0.02063275,0.09653817,-0.00697321,0.01797938,-0.05400922,0.04003945,0.09696339,-0.01078407,-0.01132032,-0.06811962,-0.03043787,0.03962958,0.04840747,0.01252531,0.06889164,-0.01914418,-0.01245606,-0.00390578,-0.00010431,-0.01204524,0.0469948,0.01379957,-0.0348294,0.06015709,-0.01564127,-0.03990595,-0.02527005,-0.00489087,0.0380939,0.07116102,-0.01016618,-0.00671134,0.02788495,-0.0430086,-0.14553858,-0.02357664,0.01811196,0.02918231,-0.0254008,-0.01783488,-0.01525747,0.05206238,-0.01479131,-0.03493597,0.02636942,-0.06002618,-0.07123003,-0.00405545,-0.01083852,0.0292026,0.0571392,0.03302239,0.01885824,-0.03480386,-0.02710949,-0.05434049,0.00415941,0.01433916,0.045393,0.10528848,-0.03981676,-0.0092945,0.00816506,-0.02891711,-0.05693682,0.16204195,-0.06112185,-0.10035609,-0.02483707,0.04960829,-0.01856709,-0.06543455,0.04137479,-0.05215826,-0.05711799,0.03304067,0.08785436,-0.01846482,-0.05076766,-0.05352601,-0.06818604,-0.01851531,0.01506183,-0.07674395,-0.04618141,0.00017437,0.01226691,-0.0707064,0.02352578,-0.03218771,0.05529976,0.10114491,-0.00681688,0.04843053,-0.08115864,0.03608953,0.03316246,-0.00027727,-0.00930948,-0.05948542,0.08108585,0.01045241,-0.07212514,-0.00477534,0.00149782,0.01741663,-0.02272243,0.05802847,0.00973513,-0.05582853,0.0837497,0.03846246,-0.04293516,-0.03404177,-0.04599295,0.04545334,-0.01660967,0.04457883,0.03602247,0.00479408,-0.00955392,0.04853708,-0.02557058,0.03238142,-0.0805863,-0.20456214,-0.03750134,-0.07052782,-0.02331035,0.04392665,-0.00038658,-0.00812457,-0.03730768,0.02828875,0.10214674,0.09237306,-0.05233803,-0.00187226,0.06520148,-0.01319669,-0.00475803,-0.04108319,-0.01212227,-0.02696626,0.0274524,-0.04500572,0.02154743,-0.01669622,-0.0826595,0.0114638,-0.02466413,0.14870715,-0.02009163,-0.0323065,-0.02272459,0.04379499,0.04069329,-0.03040176,-0.02936883,-0.03204319,0.02985287,-0.01914239,-0.01196525,-0.06672864,-0.02117156,-0.0814096,0.03736242,0.00901176,-0.08471779,-0.05902348,0.03187402,0.03090334,0.00076697,0.02685891,0.06199034,0.04238979,-0.02281389,0.02545557,0.0615201,0.08200406,-0.02486894,-0.07922689,0.01520649,-0.00023328,0.06844307,0.02621653,-0.06266888,0.03250182,0.00910649,0.05566727,0.05970685,-0.04258109,-0.04345562,0.00983242,-0.00860611,-0.01016321,0.06957096,-0.00851232,-0.04031702,-0.00541271,0.03674625,0.08231337,-0.01635307,0.01297243,0.00569212,0.02157151,-0.0594287,0.06400947,0.06935988,0.04546138,-0.01444169,0.01944185,0.04795941,-0.01814306,-0.03990041,-0.01981921,0.01943791,-0.0739257,0.03285806,-0.03941844,0.05324642,-0.24296908,0.03773774,-0.05468026,0.06240415,-0.00403013,-0.01923561,0.0406122,-0.01834902,0.00632733,-0.06601099,0.10021482,0.06007415,0.02227319,-0.08048024,-0.01363985,-0.02332378,0.01810705,-0.05448405,0.06262345,0.0521793,0.09715354,0.05988723,0.2204881,0.01018812,-0.01052039,-0.02425602,0.05777243,0.01939563,-0.03029056,0.05038908,-0.00852674,0.01413806,0.04860292,0.0201975,-0.00367656,0.01014571,-0.01227483,0.02091727,-0.01058166,0.00458936,-0.08075736,0.00067359,-0.04026249,0.04231202,0.12213878,0.04339482,-0.04988075,-0.05766878,0.07649179,0.05429271,-0.05897429,-0.04617814,-0.04245682,-0.0077778,-0.00431101,0.05380972,-0.03660311,-0.01105957,-0.02531863,-0.02494934,0.00070836,0.02337001,0.03531929,-0.00275598,0.01389778],"last_embed":{"hash":"1eh9dcp","tokens":137}}},"text":null,"length":0,"last_read":{"hash":"1eh9dcp","at":1753423482426},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{7}","lines":[54,54],"size":356,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07895935,-0.03622682,-0.01621827,0.00302865,-0.05889866,0.05053075,0.00587288,0.01573757,0.02411905,-0.00076283,-0.00532923,0.0028376,0.01237662,0.03691454,-0.00694352,-0.0275305,-0.03165948,-0.02212466,0.01799172,0.0202425,0.05639645,-0.04395686,-0.01561302,-0.07338531,0.03677487,-0.00817278,-0.00712261,-0.04801053,-0.04567569,-0.1811069,0.00014808,-0.01204287,0.00287985,-0.04450377,-0.06144546,-0.04248889,0.01309129,0.07766791,-0.02412256,0.02493214,-0.00832386,0.01418939,0.02029416,-0.04009809,0.01485024,-0.02563449,0.03548742,0.00603241,0.05350401,0.00448796,-0.06261412,0.03147849,0.0089562,0.03291131,0.05336362,-0.02115709,0.08085541,0.07598244,-0.02294298,0.05267232,0.02921905,-0.01541279,-0.20867519,0.03327814,-0.02490243,0.04807278,-0.00973189,0.00486488,0.01380524,0.02169479,0.01308581,0.0347409,-0.05091297,0.05063056,0.04750511,-0.01002058,-0.00448687,-0.03612391,-0.0286998,0.01602968,0.01082889,-0.00469777,0.01451915,-0.05710701,-0.02257751,-0.01080703,0.05302805,0.02273124,0.05096536,-0.09159812,0.02511467,0.01336094,0.08264926,0.04024416,-0.00102,0.01481067,0.01791108,-0.00762178,-0.05782472,0.0990297,-0.01506369,0.02010519,-0.03908606,0.07708139,0.08560744,-0.01373137,-0.01571767,-0.05661291,-0.01961922,-0.01571475,0.04174097,0.00337349,0.05391998,-0.02015634,-0.01687521,0.00420617,-0.0335279,-0.00324507,0.05184327,0.01364713,-0.02177261,0.04596632,0.04638939,0.02136916,-0.01417938,-0.02207791,0.01836294,0.07953265,-0.01509176,0.03769082,0.02374427,0.00120815,-0.08758512,-0.01581009,-0.02305163,0.00617117,-0.00959525,-0.00625469,0.03676055,0.01369904,-0.04520513,-0.05933316,0.01777459,-0.07010266,-0.01025751,0.00858985,0.05517856,-0.00009787,0.03582127,0.00657273,0.01013398,-0.06066917,-0.03782988,-0.05439361,-0.01480985,-0.02197923,0.05296458,0.07327813,-0.05503466,-0.0288773,-0.04611794,-0.06176156,-0.02981733,0.15947032,0.00000299,-0.07830781,-0.02213367,0.02649044,-0.0005355,-0.08890516,0.00037999,-0.00819112,-0.01949521,0.00067375,0.05210717,-0.00390584,-0.02726992,-0.05825377,-0.06255372,0.02187871,-0.00237202,-0.06622972,-0.02037564,0.00057224,-0.02123366,-0.0806583,0.01340497,-0.04793293,0.06772121,0.04651447,-0.03673473,0.0092455,-0.10987115,-0.01413411,-0.03536119,0.00242752,-0.04482606,-0.01342599,0.05685025,-0.02218998,-0.04480963,-0.00703022,-0.00063688,0.00928823,-0.05867262,0.0743262,0.02493427,-0.04880238,0.08282494,0.0038078,-0.02720537,0.01875754,-0.0170959,0.0160434,-0.01157234,0.02175694,0.06128065,0.03202482,-0.00502503,0.01715151,0.06444696,0.04657504,-0.05391868,-0.2253499,-0.06231599,-0.09659962,0.00763641,0.01242003,-0.03989226,0.03059582,-0.04293545,0.04014564,0.0915966,0.09055871,-0.05496494,0.01095079,0.02553833,-0.00902149,-0.0100647,-0.05164691,-0.09861095,-0.04239158,0.04483826,-0.08206865,0.02413863,-0.05077958,-0.08263709,0.03162432,-0.0114646,0.16564824,0.06441253,-0.02873659,-0.03753193,0.10926417,0.02486524,-0.04186357,-0.07293447,0.06484011,0.01142692,-0.01986057,0.01510275,-0.05651627,-0.0184782,-0.08766872,0.01383787,-0.01569016,-0.0521955,-0.07993511,-0.00308233,-0.01021189,0.04755331,-0.01252256,0.0865262,0.07857054,0.03151086,0.05371083,0.0521846,0.07902658,-0.04600214,-0.06125888,0.01252475,0.00691605,0.06691746,-0.02134779,-0.00859724,0.02634448,-0.05027075,0.04573838,0.02713508,-0.03241528,-0.01643588,0.07037646,-0.00712143,-0.03780542,0.12292124,-0.02942861,-0.02794738,0.02849466,0.01208085,0.02162433,-0.09473666,0.00480849,0.04040119,0.00004515,-0.08336873,0.07790856,0.07614713,0.05375827,0.03523684,0.03170471,0.06269036,0.00397188,0.00392803,0.0183115,-0.03791803,-0.08183467,0.01719039,0.03263893,0.03973438,-0.24266039,0.00757745,-0.02623928,0.09214706,0.01222415,-0.03261236,0.04454279,0.00467276,0.00671417,-0.01445248,0.08217505,0.02316429,0.04156603,-0.03153428,0.00132653,0.03306695,-0.04783695,-0.0499684,0.03218143,0.03335522,0.05108662,0.08928502,0.23508416,-0.00037538,-0.00355222,0.00071606,0.03252112,0.01844455,-0.0412414,0.05180689,0.00989491,0.00576259,0.01131335,-0.00501477,-0.01947302,0.01779225,-0.02975713,0.02034573,-0.00547046,0.0021874,-0.05464528,-0.01474163,-0.02555303,0.04609675,0.12130176,0.07899059,-0.03047115,-0.04865151,0.07789535,0.01688348,-0.05153796,-0.12008415,-0.01719316,0.00719245,-0.03132797,0.05100719,-0.00584913,-0.02475685,0.00011133,-0.00542705,0.04892223,0.04608204,0.01401208,0.00043005,-0.00476525],"last_embed":{"hash":"1i68dv5","tokens":127}}},"text":null,"length":0,"last_read":{"hash":"1i68dv5","at":1753423482470},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{10}","lines":[57,57],"size":311,"outlinks":[{"title":"_**Online Betting Sites Survive Only by Cheating the Gamblers**_","target":"https://saliu.com/bbs/messages/844.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06926826,-0.04905568,-0.00731323,0.00079512,-0.05083938,0.03480212,0.02172817,0.02465868,0.01704424,-0.00230099,-0.001949,0.01062741,0.01278788,0.01990364,-0.02220308,-0.08116934,0.01573363,-0.04810528,-0.00439573,-0.00849837,0.09614164,-0.03861472,-0.01319857,-0.07911804,0.02871101,-0.02618804,-0.03815484,-0.05440149,-0.05319184,-0.18940483,-0.0284219,-0.00086963,0.0219062,-0.01149343,-0.05959339,-0.02858689,0.03161141,0.04059785,-0.03123421,0.03020795,0.00445647,0.01990569,0.013831,-0.00307366,0.03148715,-0.0254024,0.04942592,0.01754667,0.0333358,0.0287678,-0.04975303,0.05537769,0.00567887,0.00142987,0.08418053,-0.0301517,0.05806313,0.05807147,0.02231442,0.02235187,0.04901239,-0.00639434,-0.21276985,0.0416184,-0.03342111,0.03278733,-0.00012413,-0.00660896,-0.05300058,0.01475565,0.04504038,0.00491496,-0.05536776,0.05967753,0.04353577,-0.01002281,0.00388746,-0.02372736,-0.05116626,0.05135844,0.00636384,-0.00988319,-0.00375846,-0.06323509,0.00799747,0.0206058,0.02474568,0.05486248,0.08541006,-0.07795794,0.06210906,0.03334563,0.06113664,0.02885161,0.01266243,-0.00200348,0.04922504,-0.0464883,-0.02901069,0.09181748,-0.00385067,0.03195879,-0.04134933,0.01042252,0.09734477,-0.03337206,-0.01427136,-0.02888931,0.01249624,-0.00833609,0.00075709,0.01304563,0.08006907,-0.00237242,-0.03287844,-0.0146434,-0.00254541,0.00028608,0.02375678,-0.00363338,-0.03375982,0.04313147,0.01321604,0.02201106,0.02842424,-0.02235356,0.01580858,0.07843453,-0.02430573,0.07769933,0.01550552,0.01058944,-0.13832657,-0.01798965,-0.03085121,-0.0066483,-0.00081222,-0.037478,0.03373618,0.03941274,-0.02800791,-0.03277822,0.0021599,-0.06495649,-0.03482509,0.04162007,0.00159014,0.03026401,0.02402681,0.02931119,0.04184124,-0.02119577,-0.03495735,-0.04233251,-0.00597479,0.00080866,0.07112635,0.08322805,-0.06688122,-0.0213908,-0.03788197,-0.03214522,-0.02014362,0.12897468,0.00257418,-0.12399044,-0.04319993,0.00437271,-0.00700997,-0.07738318,0.00926837,-0.01856366,-0.08245958,0.0043827,0.04220273,-0.00165035,-0.00938178,-0.07160142,-0.05357359,-0.01590439,-0.00934321,-0.03966282,-0.02011031,-0.00221551,-0.02044456,-0.04945182,0.040852,-0.07822295,0.05032177,0.03904769,-0.00830225,0.00292256,-0.07352076,0.0096906,-0.02162324,-0.03362791,-0.06488468,-0.03206967,0.07408363,-0.01841981,-0.02312403,-0.00444448,-0.01995705,-0.01406744,-0.05578483,0.08809538,-0.01476819,-0.03233105,0.08120388,0.0314705,-0.02267263,-0.00032527,-0.0135806,0.04797407,-0.03283821,0.03972816,0.06522823,0.00827817,-0.01599964,0.0334347,0.06333315,0.03289236,-0.09524584,-0.21785581,-0.04961865,-0.07253885,-0.00742048,0.03019908,-0.05423098,0.06259793,-0.03093236,0.06849242,0.12353484,0.08560679,-0.01406423,0.03002199,0.03305444,0.00454671,0.02312419,-0.08698123,-0.03106602,-0.06015095,0.02429823,-0.04889259,0.05809055,-0.05146066,-0.04110136,0.02433763,-0.02244911,0.14238538,0.03838076,-0.01213507,-0.01866601,0.11701695,0.02388534,-0.01106066,-0.06076697,0.03099201,-0.00414123,0.01700383,0.00269209,-0.04848514,-0.0116888,-0.05877113,0.0312067,0.00424746,-0.05341716,-0.02926287,0.00216601,-0.00433103,0.05615466,-0.03456808,0.06668419,0.03552293,0.01007891,0.04483995,0.04223576,0.06306786,-0.02523682,-0.06791665,0.02610898,-0.03140853,0.06461302,-0.01246706,-0.02865165,0.0178846,-0.02730685,0.02495448,0.0200513,-0.03462137,0.00081888,0.05249734,-0.03677476,-0.03028672,0.08124611,-0.04167837,0.01508739,0.01942585,0.03838946,0.09202114,-0.11186095,0.00640308,-0.03091395,-0.02070066,-0.03067125,0.06360669,0.06713189,0.0688367,0.02865137,0.02219017,0.05252539,-0.03478269,0.01062269,0.00933757,-0.0160854,-0.08484682,-0.02459047,-0.0061783,0.01554572,-0.25676274,0.02380009,-0.02503606,0.10293353,0.0034408,-0.03691657,0.00154692,-0.01820266,0.0244097,-0.04038832,0.0763919,0.05434982,0.0000272,-0.05407629,-0.02417985,0.03128469,-0.05196473,-0.06200267,0.07459688,0.03599392,0.04494644,0.06078629,0.21263234,-0.02105308,-0.00737742,0.02160173,-0.00832436,-0.00641121,-0.07305267,0.02809306,0.03769246,-0.00101933,0.06154746,-0.00244133,-0.04868382,0.03481799,-0.04005711,0.05675491,0.00087259,0.01991408,-0.02778743,-0.04457918,-0.01626061,0.05814846,0.14439523,0.07061488,-0.04051721,-0.04996217,0.05346097,0.060991,-0.07268269,-0.10643109,-0.00778741,0.02497238,-0.00785998,0.05301246,-0.01321487,-0.05280909,-0.00956972,-0.02012411,0.02426557,-0.01187431,0.03977007,0.01111953,0.01612584],"last_embed":{"hash":"r3s8fe","tokens":130}}},"text":null,"length":0,"last_read":{"hash":"r3s8fe","at":1753423482506},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#By Ion Saliu, ★ _Founder of Lottery Mathematics_#{11}","lines":[58,59],"size":335,"outlinks":[{"title":"_**Customer Service Reviews of Bet365**_","target":"https://www.trustpilot.com/review/www.bet365.com","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11216923,-0.02222771,-0.00237123,-0.04328216,-0.02951302,0.03605185,-0.01188847,-0.02677035,0.02770035,-0.01457153,0.00883927,-0.03087335,0.03651677,-0.01174223,-0.0138324,-0.02640064,-0.00269623,0.00844875,-0.03757567,-0.02653996,0.08201879,-0.02148687,-0.04438087,-0.04828367,0.00992763,0.01142018,-0.00466096,-0.02451665,-0.03168181,-0.20925882,0.0422377,0.01671605,0.00181048,-0.05468912,-0.08296936,-0.01774209,-0.03341485,0.03950948,-0.05788848,0.04686361,0.01732623,0.02124828,0.01911914,-0.053383,0.03317244,-0.06062426,0.02504167,-0.00737746,0.03168912,-0.02476294,-0.09775345,0.00868378,0.03960446,0.0123458,0.08272487,0.04596782,0.06487756,0.10733797,0.03852758,0.02937119,0.03888963,0.07908758,-0.20840706,0.08541681,-0.01273437,0.00791123,-0.01180248,0.00114126,0.03328343,0.03159684,0.0042715,0.03624187,-0.01861691,0.07445135,0.0086721,0.00883008,-0.02276625,-0.06575196,-0.04463806,-0.01386946,-0.02337907,0.00341385,0.0041869,-0.01958791,-0.00004806,0.07827776,0.02211889,0.02936515,0.05214644,-0.05762562,0.018238,0.01212777,0.0444332,0.04935725,-0.00706828,0.01262371,0.04925114,-0.01370873,-0.00442688,0.10699239,0.05568739,-0.0196553,-0.02072074,0.01950486,0.08236082,-0.04306126,-0.03018463,-0.04239243,-0.03794387,0.06066244,0.04330099,-0.00484885,0.03136108,-0.0256772,-0.02075566,-0.01683914,-0.00518714,0.00059212,0.03777609,0.00391165,-0.04781132,-0.01748806,0.03262582,0.04277745,-0.00289464,0.00593271,0.02353401,0.05435981,0.03934249,0.00886346,0.05022197,0.02991516,-0.1534971,-0.05742979,0.01801236,0.00319027,-0.00660762,-0.04641368,-0.02160227,0.02242047,-0.04326873,-0.03653048,0.05256374,-0.09290175,-0.0732137,0.0703398,0.02861579,-0.00463895,0.01031985,-0.02534183,0.01886447,-0.02373291,-0.0435725,-0.04010476,0.00591708,0.02811266,0.0931153,0.09659105,-0.0695947,-0.00649868,-0.03246504,-0.05431601,-0.03472686,0.15283075,0.0189815,-0.12673143,-0.02139917,0.04872904,-0.00984906,-0.09015351,-0.0122737,0.00879205,-0.05182213,0.0468073,0.07810707,-0.01384954,-0.0372551,-0.03946523,-0.05347394,-0.0170239,0.00134034,-0.07338648,-0.02471648,-0.00089203,-0.01962696,-0.11168134,0.01441651,-0.05009722,0.0191627,0.05218345,-0.01861156,0.0003464,-0.0041461,-0.00671829,-0.03650799,-0.00485327,-0.0535805,-0.03966269,0.0528424,-0.01889601,-0.00821666,0.01120077,0.0120293,0.00675326,-0.03215655,0.05099133,-0.03616747,-0.0368967,0.08686996,-0.00293362,-0.03155667,0.02096286,0.06172997,0.05855519,-0.02913971,0.03617344,0.01977872,0.0221936,-0.02605847,0.02362753,0.00392542,0.05097473,-0.10333791,-0.2054887,-0.02477163,-0.0701854,0.00998467,0.00087995,-0.03114085,0.04565581,-0.03138733,0.03529324,0.07496059,0.0878828,-0.0562854,-0.01063742,0.06606926,-0.00411927,0.00495999,-0.04359392,-0.05372471,-0.08393351,0.03187647,0.01388571,0.02848407,-0.01792597,-0.08032811,-0.00691038,-0.03638489,0.11637163,-0.02196101,-0.01202291,-0.00892505,0.09837467,0.05862892,-0.01619382,-0.05293712,-0.0111721,0.04839855,-0.04623241,0.01966147,-0.02010739,-0.00524699,-0.08050294,0.03976656,-0.00604277,-0.06326517,-0.04877164,0.02348243,0.02305562,-0.01253662,-0.01777661,0.02654666,0.00914878,0.02246646,0.02378705,0.03817622,0.04531341,-0.02649767,-0.05871533,0.0251423,-0.01330165,0.06348297,-0.01839413,-0.04977707,0.02750124,-0.03343177,0.04977746,-0.01660489,-0.00654941,-0.01020754,0.03774589,0.00542481,-0.03079136,0.10985588,-0.00285513,0.05056381,-0.0105691,0.03508805,0.04595258,-0.01388903,-0.00092943,-0.01825573,-0.03261825,-0.03070658,0.04265166,0.08253878,0.05843708,0.02825917,0.07013019,-0.00977435,0.03646342,-0.00015608,-0.0218208,0.00901505,-0.03630462,0.01612028,0.05110914,0.04960434,-0.26431337,0.05230609,-0.00729253,0.06414501,-0.03258167,-0.02425007,0.01443923,-0.04467638,0.04094432,-0.03037219,0.09237678,0.01559721,0.02213656,-0.05355395,-0.05079637,0.00314818,0.03007478,-0.02367737,0.06219646,-0.00646832,0.03357404,0.05628763,0.23731054,0.0062207,-0.02373669,0.02381067,-0.00063149,0.02426712,-0.00698345,0.04001144,0.01551948,0.02659018,0.07318003,0.01316787,-0.02980015,0.03032023,-0.0319433,0.01840602,0.00691479,0.01546839,-0.08061015,0.02912967,-0.02460083,0.00455482,0.1105472,0.05475641,-0.02791402,-0.10942066,0.03547115,0.09066242,-0.06177808,-0.06366294,-0.06228099,-0.01006891,0.01379967,0.0461609,0.01456471,-0.02623233,-0.02784556,-0.03186095,0.04553118,-0.02425961,0.06338418,0.00519495,-0.0084351],"last_embed":{"hash":"31evsd","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"31evsd","at":1753423482549},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","lines":[64,95],"size":3120,"outlinks":[{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":5},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":7},{"title":"_**Lotto, Lottery Strategy Tutorial**_","target":"https://saliu.com/bbs/messages/818.html","line":8},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":9},{"title":"_**<u>Lottery Skip System Software</u>**_","target":"https://saliu.com/skip-strategy.html","line":10},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":11},{"title":"_**Practical lottery, lotto filtering in software**_","target":"https://saliu.com/filters.html","line":12},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":13},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":14},{"title":"_**Lotto wheels**_","target":"https://saliu.com/lotto_wheels.html","line":15},{"title":"_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_","target":"https://saliu.com/lotto-triples.html","line":18},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":19},{"title":"**lotto software, lottery software**","target":"https://saliu.com/free-lotto-lottery.html","line":20},{"title":"_**Lottery and Powerball rigged: Prize money distributed evenly in all regions**_","target":"https://saliu.com/bbs/messages/534.html","line":24},{"title":"_**Lottery and Gambling Experience: Robbed of Prize Money**_","target":"https://saliu.com/bbs/messages/535.html","line":25},{"title":"Historical presentation of lottery, lotto experience using systems, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":27},{"title":"Forums","target":"https://forums.saliu.com/","line":29},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":29},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":29},{"title":"Contents","target":"https://saliu.com/content/index.html","line":29},{"title":"Home","target":"https://saliu.com/index.htm","line":29},{"title":"Software","target":"https://saliu.com/infodown.html","line":29},{"title":"Search","target":"https://saliu.com/Search.htm","line":29},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":29},{"title":"Read the presentation of that lottery strategy, software: 12-number combinations in lotto 6 games.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":31}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{21}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10422118,-0.04241731,0.01577496,-0.00876866,-0.03454761,0.04673152,-0.00871017,0.01228406,0.00983567,-0.0366812,-0.04674572,-0.02950852,0.01331943,-0.00191153,0.01741382,-0.0319192,-0.01041263,0.01725146,-0.01976842,0.01373476,0.06107045,-0.01337496,-0.04432874,-0.05738809,0.02393928,0.00710543,-0.03854216,-0.0610422,-0.01130843,-0.19585025,0.05303021,0.01509695,-0.0091114,-0.02362919,-0.07123943,-0.0190747,-0.01742266,0.06867441,-0.05343628,0.06378306,0.02272737,0.00838916,0.01433906,-0.02567627,-0.00739727,-0.05508631,0.03494008,0.01107542,0.04517513,-0.00254036,-0.08810399,0.03130611,0.01457758,0.01084673,0.0611653,0.06163769,0.06829406,0.08809201,-0.01821864,0.02415056,0.04770255,0.05271414,-0.23426896,0.062511,0.0193068,0.02559557,-0.02543037,0.00806886,0.03496943,0.02242859,-0.00903294,0.02914851,-0.01637182,0.06853229,0.02474035,-0.02287191,-0.05061916,-0.05195422,-0.02637197,-0.03194352,-0.03596621,0.01992186,-0.02178627,-0.02515309,-0.00662096,0.03175749,0.04394052,0.05353536,0.06445589,-0.08859308,0.01264626,0.02080876,0.04642491,0.05313362,-0.00106672,-0.01426899,0.04342068,-0.02208771,0.04230402,0.11265661,-0.00533001,-0.00846011,-0.0005705,0.01438322,0.08662476,-0.00467013,-0.05906462,-0.03152677,-0.04694438,0.06459651,0.06996745,0.02778051,0.02679335,-0.04762471,-0.03280982,-0.0292052,-0.01473456,-0.01969112,0.04891737,-0.0121207,-0.07500914,-0.00678415,0.02911487,0.00659175,0.00159943,-0.00648464,0.04402508,0.06402768,0.04182315,0.01367877,0.01090596,0.0310674,-0.139998,-0.06528726,-0.02118042,-0.0227473,0.01277769,-0.0244509,-0.01354684,0.03863994,-0.01159532,-0.06287532,0.05132649,-0.09706818,-0.07065485,0.08203566,0.00971347,0.00816414,0.00648802,0.02254234,0.00995238,-0.03766971,0.00213778,-0.03916523,-0.00462899,-0.01362523,0.11538851,0.09964355,-0.03735621,0.04415863,-0.0256129,-0.05353375,-0.00234693,0.15609582,0.01245757,-0.10694481,-0.03056143,0.05441624,0.0212722,-0.09728695,-0.02189602,0.00537201,-0.03785586,0.01799961,0.08291328,-0.01090418,-0.0550525,-0.03801778,-0.03381823,-0.01642811,-0.01195196,-0.04225243,-0.01215522,0.02629241,-0.03001619,-0.12331624,0.02631048,-0.05298713,0.00973223,0.07822394,-0.0489838,-0.03551277,-0.02628288,-0.01170428,-0.01143268,0.01071589,-0.05242606,-0.03264876,0.06866045,0.00723934,-0.03458756,0.00381194,-0.0019354,0.01918746,-0.02260754,0.06333506,-0.03796322,-0.06696737,0.03650699,-0.01514689,-0.0264318,0.0402011,0.06193471,0.07750535,-0.02141321,0.0405267,0.04227259,0.02512409,-0.01883572,0.02323291,0.00625712,0.02537827,-0.07906167,-0.20678852,-0.0229524,-0.03791266,-0.03865194,0.0087051,-0.01055769,0.0119266,-0.01997738,0.029674,0.05544141,0.09388602,-0.04986049,0.01406706,0.02691149,-0.00106101,-0.02933207,-0.05953332,-0.03157202,-0.0506774,0.05667693,-0.01884024,-0.01474644,0.00189864,-0.09254083,-0.00696552,-0.03064516,0.12666024,0.00177334,-0.01399824,-0.01960694,0.08894003,0.0297507,-0.03924511,-0.04071688,0.04209894,0.04940926,-0.07107809,0.0135528,0.00104522,0.02050649,-0.09436668,0.02655503,-0.00132161,-0.05744762,-0.03619052,0.0196772,0.00230867,-0.03708257,0.00835195,0.04950471,0.03175105,0.01424446,0.04821887,0.03549737,0.06461885,-0.02024995,-0.05887847,0.02359334,-0.01599221,0.0508231,-0.04046557,-0.03043338,0.02220687,-0.03045135,0.04110828,0.02396214,-0.03799036,-0.0379346,0.00563983,-0.03720543,-0.0352709,0.0791247,-0.01479256,0.02417228,0.00484466,0.02120956,0.05032707,-0.05806543,0.00275521,0.02137893,-0.03008061,-0.0990447,0.04839434,0.08512152,0.0588192,0.02897642,0.04862801,0.00489925,0.03553346,0.00564454,-0.02618255,0.00087104,-0.04034779,0.02502519,0.03076776,0.03670255,-0.26218578,0.04538293,0.0016977,0.04549485,-0.0438424,-0.01716573,0.02827572,-0.02405698,0.02204508,-0.01604801,0.11324125,-0.00179957,0.00358364,-0.06918781,-0.01105482,0.03476698,0.0246529,-0.02396481,0.04897527,0.01396482,0.03317368,0.06680924,0.21604688,0.02934114,0.00502972,0.01960019,-0.01627082,0.0430617,-0.0097629,0.02698667,0.02960237,-0.01665604,0.08018592,0.01238656,-0.06913523,0.04197345,-0.00993191,0.00587936,-0.00609506,0.04449634,-0.08328699,0.01311359,-0.02199042,-0.00086314,0.13721766,0.0379752,0.00672741,-0.11766813,0.05867064,0.06144258,-0.05940445,-0.06496108,-0.03699594,0.00433853,0.03881446,0.03259512,0.04230913,-0.03142152,-0.0031219,-0.02014933,0.03642757,-0.00968964,0.03540904,-0.02778797,0.0108098],"last_embed":{"hash":"49tt5z","tokens":294}}},"text":null,"length":0,"last_read":{"hash":"49tt5z","at":1753423482682},"key":"notes/saliu/Lottery History, Lotto Software, Systems, Strategies.md#Lottery History, Lotto Software, Systems, Strategies#<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>#{21}","lines":[90,95],"size":651,"outlinks":[{"title":"Historical presentation of lottery, lotto experience using systems, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Software","target":"https://saliu.com/infodown.html","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Read the presentation of that lottery strategy, software: 12-number combinations in lotto 6 games.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":5}],"class_name":"SmartBlock"},
