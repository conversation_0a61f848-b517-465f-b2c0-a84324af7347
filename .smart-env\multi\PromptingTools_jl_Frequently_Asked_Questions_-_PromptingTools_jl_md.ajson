"smart_sources:PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl.md": {"path":"PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1104691,-0.00396106,-0.02242541,-0.0527148,-0.0189763,0.02891655,-0.01922419,0.03928866,0.02792168,-0.03788215,-0.02806778,0.00935166,-0.00112668,0.04470638,0.01898334,0.02590216,-0.02720261,-0.00431445,-0.09285858,-0.00582253,0.08489738,0.0430509,0.01809405,-0.03896898,-0.00066605,0.03217248,0.0155825,-0.04386524,-0.00528091,-0.19187775,0.05958011,-0.01969072,0.0071285,-0.02054892,-0.03345134,-0.00020633,-0.02228986,0.06627636,-0.01617621,-0.01034684,0.02749066,0.01148534,-0.04077703,-0.02444164,-0.05293446,-0.09292224,-0.00538878,-0.03574308,0.01608815,-0.05369386,-0.01866709,-0.04948018,0.05912755,-0.03925523,0.01211752,0.00406061,0.00876654,0.07999182,0.03611066,0.01957595,0.03229565,0.00253279,-0.19058178,0.17781466,-0.00083764,0.02594641,0.00278703,0.00385894,0.01906277,0.04510787,-0.03730084,0.02416225,0.00415724,0.06741387,0.01314028,-0.02773035,-0.03291172,-0.00490794,-0.00137896,-0.10733099,-0.05099208,-0.01355145,-0.0245087,-0.00930535,-0.04271645,-0.02569452,0.00269232,0.01185517,0.05924562,0.00699401,0.00476886,-0.05862399,0.01516264,0.02783223,-0.05472356,-0.03694871,0.01560515,-0.03685358,-0.11101868,0.14366028,-0.00806032,0.00149773,0.02650744,-0.02231484,0.02047027,-0.04869633,0.01655199,-0.04453835,-0.02086486,0.02933431,0.01425921,-0.0305744,-0.01088658,-0.0744856,-0.03404127,-0.01164507,0.0177669,-0.0175321,-0.00722654,-0.04128259,0.0131434,0.01170465,0.02404838,0.00212325,0.005567,0.02219643,0.034693,0.02686923,-0.00203351,0.02129903,0.06423672,0.00440509,-0.06889401,-0.0249079,-0.01503163,0.04883975,0.0663597,-0.0673164,0.04904368,-0.00317314,0.00954531,-0.02092583,0.03087399,-0.1051937,0.02169957,0.05126013,-0.04938325,-0.01180566,-0.00100142,-0.05162294,0.03246247,0.03886467,-0.04919389,-0.02299517,0.09076506,0.06227088,0.07821237,0.06589241,0.0078169,0.00384102,-0.04766283,-0.03788536,0.00762869,0.16554688,0.04808636,-0.06457792,-0.10408642,-0.03748737,-0.05000849,-0.03491134,0.01637937,0.05668265,-0.059987,0.01095946,0.08036761,0.03148569,-0.0674012,0.04447167,-0.00179786,0.02807112,0.02746682,-0.03790997,-0.02824383,0.0122291,-0.03056564,-0.03108456,-0.00614646,-0.07367113,-0.01232086,0.03263144,-0.06266676,-0.01641445,0.04350094,-0.02048602,-0.00842785,-0.00249479,0.01390597,-0.07909136,0.05364472,-0.04409231,0.04819343,0.0229494,0.04315567,0.04294068,-0.01249419,0.05377277,0.07169132,-0.00555841,0.12571423,0.04120646,-0.09073206,-0.05516644,0.05100572,-0.00590939,-0.01198205,-0.0469039,-0.06049908,0.00855991,0.01038857,0.05630883,-0.02247842,-0.00131276,-0.02713086,-0.21601599,0.01534823,0.02738452,-0.03905753,0.06331886,-0.10015734,0.08715268,-0.05219913,-0.01236164,0.0795588,0.08873908,-0.03326462,0.03667757,-0.01872915,0.00467426,-0.00067392,-0.02790593,-0.00310427,0.00218368,0.02846526,0.01740723,-0.00475039,0.04344111,-0.08785014,0.00584814,-0.03413361,0.10272868,0.04764029,0.02107681,-0.10940669,0.03098667,-0.0142049,0.05496424,-0.11491438,0.01072134,0.00671949,-0.05607804,0.04800964,0.07755203,0.02260468,-0.04171978,0.03679433,-0.03566119,-0.02503451,-0.03069497,-0.00997884,-0.03840516,-0.04645582,-0.0421741,0.02095162,0.01192239,-0.05851104,0.00948922,-0.00200374,0.02297079,-0.02877188,-0.04428583,-0.0716746,0.01631858,0.00902457,0.02192115,-0.00159834,-0.02155016,-0.03188372,0.05062427,0.03561149,0.00151684,0.00709566,0.05733947,-0.00290202,-0.03174395,0.11373079,0.02101287,0.08844494,-0.02144621,0.0681727,0.01541596,-0.05647024,0.00662825,-0.0517351,-0.03370416,0.02788564,0.00518479,0.02044653,0.04183701,0.06611028,0.02190352,-0.02921711,0.08264353,-0.04471459,-0.01549091,0.00882747,0.01613873,-0.05544372,0.01158093,-0.0085081,-0.22942978,0.02738623,-0.00123939,0.03042916,-0.00986182,0.08260858,0.03948737,0.01539486,-0.01268983,0.07018555,-0.03116808,0.02798135,0.00954288,-0.04642093,0.05928803,0.04723017,0.03973429,0.01534127,0.03794211,-0.0929823,0.03573946,0.01259927,0.21297739,-0.00742665,0.02515861,-0.00745973,0.00791114,-0.04217412,0.06960908,0.03523616,0.03169865,0.04372168,0.14452523,-0.004293,0.04787702,0.00047146,-0.04082641,-0.01996277,0.01353163,0.0053157,-0.02370002,0.02701527,-0.05719007,-0.00713551,0.07268725,-0.01500981,-0.00098406,-0.08171644,-0.03702002,0.03504445,-0.01200422,-0.02141672,-0.02323389,-0.00666206,0.00366511,-0.00038749,0.07284394,0.01193394,-0.06865703,0.0051654,0.04936332,-0.02863066,0.10911174,-0.00685665,-0.00571302],"last_embed":{"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b","tokens":461}}},"last_read":{"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b","at":1745995218812},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/frequently_asked_questions/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"Why OpenAI","target":"#Why-OpenAI \"Why OpenAI\"","line":243},{"title":"What if I cannot access OpenAI?","target":"#What-if-I-cannot-access-OpenAI? \"What if I cannot access OpenAI?\"","line":244},{"title":"Data Privacy and OpenAI","target":"#Data-Privacy-and-OpenAI \"Data Privacy and OpenAI\"","line":245},{"title":"Creating OpenAI API Key","target":"#Creating-OpenAI-API-Key \"Creating OpenAI API Key\"","line":246},{"title":"Getting an error \"ArgumentError: api_key cannot be empty\" despite having set OPENAI_API_KEY? {#Getting-an-error-\"ArgumentError:-apikey-cannot-be-empty\"-despite-having-set-OPENAIAPI_KEY?}","target":"#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key \"Getting an error \"ArgumentError: api_key cannot be empty\" despite having set OPENAI_API_KEY? {#Getting-an-error-\"ArgumentError:-apikey-cannot-be-empty\"-despite-having-set-OPENAIAPI_KEY?}\"","line":247},{"title":"Getting an error \"Rate limit exceeded\" from OpenAI?","target":"#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI? \"Getting an error \"Rate limit exceeded\" from OpenAI?\"","line":248},{"title":"Setting OpenAI Spending Limits","target":"#Setting-OpenAI-Spending-Limits \"Setting OpenAI Spending Limits\"","line":249},{"title":"How much does it cost? Is it worth paying for?","target":"#How-much-does-it-cost?-Is-it-worth-paying-for? \"How much does it cost? Is it worth paying for?\"","line":250},{"title":"Configuring the Environment Variable for API Key","target":"#Configuring-the-Environment-Variable-for-API-Key \"Configuring the Environment Variable for API Key\"","line":251},{"title":"Setting the API Key via Preferences.jl","target":"#Setting-the-API-Key-via-Preferences.jl \"Setting the API Key via Preferences.jl\"","line":252},{"title":"Understanding the API Keyword Arguments in aigenerate (api_kwargs)","target":"#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs","line":253},{"title":"Instant Access from Anywhere","target":"#Instant-Access-from-Anywhere \"Instant Access from Anywhere\"","line":254},{"title":"Open Source Alternatives","target":"#Open-Source-Alternatives \"Open Source Alternatives\"","line":255},{"title":"Setup Guide for Ollama","target":"#Setup-Guide-for-Ollama \"Setup Guide for Ollama\"","line":256},{"title":"Changing the Default Model or Schema","target":"#Changing-the-Default-Model-or-Schema \"Changing the Default Model or Schema\"","line":257},{"title":"How to have Multi-turn Conversations?","target":"#How-to-have-Multi-turn-Conversations? \"How to have Multi-turn Conversations?\"","line":258},{"title":"How to have typed responses?","target":"#How-to-have-typed-responses? \"How to have typed responses?\"","line":259},{"title":"How to quickly create a prompt template?","target":"#How-to-quickly-create-a-prompt-template? \"How to quickly create a prompt template?\"","line":260},{"title":"Do we have a RecursiveCharacterTextSplitter like Langchain?","target":"#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain? \"Do we have a RecursiveCharacterTextSplitter like Langchain?\"","line":261},{"title":"How would I fine-tune a model?","target":"#How-would-I-fine-tune-a-model? \"How would I fine-tune a model?\"","line":262},{"title":"​","target":"#Frequently-Asked-Questions","line":264},{"title":"​","target":"#Why-OpenAI","line":266},{"title":"Setup Guide for Ollama","target":"/PromptingTools.jl/dev/frequently_asked_questions#setup-guide-for-ollama","line":272},{"title":"Ollama.ai","target":"https://ollama.ai/","line":272},{"title":"​","target":"#What-if-I-cannot-access-OpenAI?","line":274},{"title":"​","target":"#Data-Privacy-and-OpenAI","line":283},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance","line":289},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data","line":291},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data","line":295},{"title":"Data usage for consumer services FAQ","target":"https://help.openai.com/en/articles/7039943-data-usage-for-consumer-services-faq","line":297},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance","line":299},{"title":"​","target":"#Creating-OpenAI-API-Key","line":302},{"title":"OpenAI","target":"https://platform.openai.com/signup","line":306},{"title":"API Key page","target":"https://platform.openai.com/account/api-keys","line":308},{"title":"OpenAI Documentation","target":"https://platform.openai.com/docs/quickstart?context=python","line":317},{"title":"Visual tutorial","target":"https://www.maisieai.com/help/how-to-get-an-openai-api-key-for-chatgpt","line":319},{"title":"​","target":"#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key","line":324},{"title":"​","target":"#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?","line":343},{"title":"OpenAI Rate Limits","target":"https://platform.openai.com/docs/guides/rate-limits/usage-tiers?context=tier-free","line":347},{"title":"​","target":"#Setting-OpenAI-Spending-Limits","line":367},{"title":"OpenAI Billing","target":"https://platform.openai.com/account/billing","line":371},{"title":"OpenAI Forum","target":"https://community.openai.com/t/how-to-set-a-price-limit/13086","line":380},{"title":"​","target":"#How-much-does-it-cost?-Is-it-worth-paying-for?","line":382},{"title":"OpenAI Pricing per 1000 tokens","target":"https://openai.com/pricing","line":396},{"title":"​","target":"#Configuring-the-Environment-Variable-for-API-Key","line":398},{"title":"OpenAI Guide","target":"https://platform.openai.com/docs/quickstart?context=python","line":428},{"title":"​","target":"#Setting-the-API-Key-via-Preferences.jl","line":430},{"title":"​","target":"#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs","line":440},{"title":"OpenAI API reference","target":"https://platform.openai.com/docs/guides/text-generation/chat-completions-api","line":442},{"title":"​","target":"#Instant-Access-from-Anywhere","line":444},{"title":"​","target":"#Open-Source-Alternatives","line":457},{"title":"Ollama.ai","target":"https://ollama.ai/","line":459},{"title":"​","target":"#Setup-Guide-for-Ollama","line":461},{"title":"here","target":"https://ollama.ai/download","line":465},{"title":"Ollama Library","target":"https://ollama.ai/library","line":471},{"title":"Ollama.ai","target":"https://ollama.ai/","line":477},{"title":"​","target":"#Changing-the-Default-Model-or-Schema","line":479},{"title":"Working with Ollama","target":"/PromptingTools.jl/dev/frequently_asked_questions#working-with-ollama","line":487},{"title":"​","target":"#How-to-have-Multi-turn-Conversations?","line":492},{"title":" Info: Tokens: 50 @ Cost: $0.0 in 1.0 seconds\n## 5-element Vector{PromptingTools.AbstractMessage}:\n##  PromptingTools.SystemMessage(\"Act as a helpful AI assistant\")\n##  PromptingTools.UserMessage(\"Hi! I'm John\")\n##  AIMessage(\"Hello John! How can I assist you today?\")\n##  PromptingTools.UserMessage(\"What's my name?\")\n##  AIMessage(\"Your name is John.\")\n```\n\nNotice that the last message is the response to the second request, but with `return_all=true` we can see the whole conversation from the beginning.\n\n## How to have typed responses? [​","target":"#How-to-have-typed-responses?","line":524},{"title":" Info: Condition not met. Retrying...\n## [ Info: Condition not met. Retrying...\n## SmallInt(2)\n```\n\nWe ultimately received our custom type `SmallInt` with the number of car seats in the Porsche 911T (I hope it's correct!).\n\nIf you want to access the full conversation history (all the attempts and feedback), simply output the `response` object and explore `response.conversation`.\n\n## How to quickly create a prompt template? [​","target":"#How-to-quickly-create-a-prompt-template?","line":628},{"title":"​","target":"#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?","line":719},{"title":"`RecursiveCharacterTextSplitter`","target":"https://python.langchain.com/docs/modules/data_connection/document_transformers/recursive_text_splitter","line":723},{"title":"​","target":"#How-would-I-fine-tune-a-model?","line":739},{"title":"Axolotl","target":"https://github.com/OpenAccess-AI-Collective/axolotl","line":748},{"title":"JuliaLLMLeaderboard Finetuning experiment","target":"https://github.com/svilupp/Julia-LLM-Leaderboard/blob/main/experiments/cheater-7b-finetune/README.md","line":748},{"title":"JarvisLabs.ai","target":"https://jarvislabs.ai/templates/axolotl","line":748},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/frequently_asked_questions.md","line":750},{"title":"Previous pageAPITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":752},{"title":"Next pageGeneral","target":"/PromptingTools.jl/dev/prompts/general","line":754},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":756},{"title":"Icons8","target":"https://icons8.com","line":756},{"title":"**VitePress**","target":"https://vitepress.dev","line":756}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,263],"##Reference#{1}":[209,242],"##Reference#{2}":[243,244],"##Reference#{3}":[245,245],"##Reference#{4}":[246,246],"##Reference#{5}":[247,247],"##Reference#{6}":[248,248],"##Reference#{7}":[249,249],"##Reference#{8}":[250,250],"##Reference#{9}":[251,251],"##Reference#{10}":[252,252],"##Reference#{11}":[253,253],"##Reference#{12}":[254,254],"##Reference#{13}":[255,255],"##Reference#{14}":[256,256],"##Reference#{15}":[257,257],"##Reference#{16}":[258,258],"##Reference#{17}":[259,259],"##Reference#{18}":[260,260],"##Reference#{19}":[261,261],"##Reference#{20}":[262,263],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)":[264,758],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)":[266,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#{1}":[268,273],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)":[274,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{1}":[276,277],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{2}":[278,279],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{3}":[280,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)":[283,301],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{1}":[285,294],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{2}":[295,296],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{3}":[297,298],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{4}":[299,301],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)":[302,323],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{1}":[304,305],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{2}":[306,307],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{3}":[308,309],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{4}":[310,312],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{5}":[313,316],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{6}":[317,318],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{7}":[319,321],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{8}":[322,323],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)":[324,342],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{1}":[326,335],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{2}":[336,337],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{3}":[338,339],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{4}":[340,342],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)":[343,366],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{1}":[345,352],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{2}":[353,354],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{3}":[355,357],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{4}":[358,366],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)":[367,381],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{1}":[369,370],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{2}":[371,372],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{3}":[373,375],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{4}":[376,379],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{5}":[380,381],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)":[382,397],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)#{1}":[384,395],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)#{2}":[396,397],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)":[398,429],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{1}":[400,411],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{2}":[412,413],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{3}":[414,416],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{4}":[417,420],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{5}":[421,422],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{6}":[423,425],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{7}":[426,427],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{8}":[428,429],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting the API Key via Preferences.jl [​](#Setting-the-API-Key-via-Preferences.jl)":[430,439],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting the API Key via Preferences.jl [​](#Setting-the-API-Key-via-Preferences.jl)#{1}":[432,439],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`) [​](#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs))":[440,443],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`) [​](#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs))#{1}":[442,443],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Instant Access from Anywhere [​](#Instant-Access-from-Anywhere)":[444,456],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Instant Access from Anywhere [​](#Instant-Access-from-Anywhere)#{1}":[446,456],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Open Source Alternatives [​](#Open-Source-Alternatives)":[457,460],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Open Source Alternatives [​](#Open-Source-Alternatives)#{1}":[459,460],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setup Guide for Ollama [​](#Setup-Guide-for-Ollama)":[461,478],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setup Guide for Ollama [​](#Setup-Guide-for-Ollama)#{1}":[463,478],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)":[479,491],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{1}":[481,484],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{2}":[485,486],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{3}":[487,488],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{4}":[489,491],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)":[492,534],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{1}":[494,495],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{2}":[496,497],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{3}":[498,508],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{4}":[509,510],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{5}":[511,534],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)":[535,636],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{1}":[537,577],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{2}":[578,579],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{3}":[580,582],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{4}":[583,636],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to quickly create a prompt template? [​](#How-to-quickly-create-a-prompt-template?)":[637,718],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to quickly create a prompt template? [​](#How-to-quickly-create-a-prompt-template?)#{1}":[639,718],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Do we have a RecursiveCharacterTextSplitter like Langchain? [​](#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?)":[719,738],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Do we have a RecursiveCharacterTextSplitter like Langchain? [​](#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?)#{1}":[721,738],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)":[739,758],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{1}":[741,742],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{2}":[743,744],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{3}":[745,747],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{4}":[748,758]},"last_import":{"mtime":1712727568469,"size":29627,"at":1740449882767,"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b"},"key":"PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl.md"},