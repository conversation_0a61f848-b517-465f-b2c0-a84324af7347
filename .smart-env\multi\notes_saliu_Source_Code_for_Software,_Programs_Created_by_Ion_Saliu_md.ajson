
"smart_sources:notes/saliu/Source Code for Software, Programs Created by <PERSON>.md": {"path":"notes/saliu/Source Code for Software, Programs Created by <PERSON>.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"2ztw1d","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753366177315,"size":11178,"at":1753423416500,"hash":"2ztw1d"},"blocks":{"#---frontmatter---":[1,6],"#Source Code for Software, Programs Created by <PERSON>":[8,94],"#Source Code for Software, Programs Created by <PERSON>#{1}":[10,94]},"outlinks":[{"title":"Software Download","target":"https://saliu.com/infodown.html","line":14},{"title":"Download Great Free Software: Paid Membership Required","target":"https://saliu.com/membership.html","line":18},{"title":"BJODDS","target":"https://saliu.com/BjOdds/BJODDS","line":22},{"title":"BjOdds: Blackjack Software to Calculate Precisely the Bust Odds, House Edge, House Advantage","target":"https://saliu.com/blackjackodds-software.html","line":27},{"title":"BreakDownNumbers","target":"https://saliu.com/code/BreakDownNumbers","line":31},{"title":"BreakDownNumbers.BAS","target":"https://saliu.com/code/BreakDownNumbers.BAS","line":38},{"title":"LexicographicAlgorithms.bas","target":"https://saliu.com/code/LexicographicAlgorithms.bas","line":41},{"title":"Algorithms, Software to Calculate Combination Lexicographical Order, Rank","target":"https://saliu.com/bbs/messages/348.html","line":42},{"title":"LexicographicAlgorithms","target":"https://saliu.com/code/LexicographicAlgorithms","line":42},{"title":"Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations","target":"https://saliu.com/lexicographic.html","line":42},{"title":"Fibonacci.bas","target":"https://saliu.com/code/Fibonacci.bas","line":46},{"title":"Pi Day, Pi, Divine Proportion, Golden Proportion, Golden Number, Phi, Fibonacci Series","target":"https://saliu.com/bbs/messages/958.html","line":47},{"title":"Fibonacci Progressions: Mathematics, Gambling, Software, Golden Number","target":"https://saliu.com/Fibonacci.html","line":47},{"title":"FileLines.BAS","target":"https://saliu.com/code/FileLines.BAS","line":50},{"title":"\"Cross-reference strategy files created by LotWon and MDIEditor & Lotto WE\"","target":"https://saliu.com/cross-lines.html","line":51},{"title":"INP-FILE.TXT","target":"https://saliu.com/pub/INP-FILE.TXT","line":51},{"title":"PARSEL.BAS","target":"https://saliu.com/code/PARSEL.BAS","line":54},{"title":"Software to correct most errors in lottery, lotto data files","target":"https://saliu.com/bbs/messages/2.html","line":55},{"title":"UpDown.bas","target":"https://saliu.com/code/UPDOWN.BAS","line":58},{"title":"Software to reverse order in lottery, lotto results, drawings files","target":"https://saliu.com/bbs/messages/539.html","line":59},{"title":"Writer.bas","target":"https://saliu.com/code/Writer.bas","line":62},{"title":"Computer program that writes random words, sentences, passwords, and...books!","target":"https://saliu.com/writer.html","line":63},{"title":"SuperRoulette.BAS","target":"https://saliu.com/code/SuperRoulette.BAS","line":66},{"title":"The Super Roulette Strategy","target":"https://saliu.com/best-roulette-systems.html","line":67},{"title":"SPINS.BAS","target":"https://saliu.com/code/SPINS.BAS","line":70},{"title":"_**Theory, Mathematics of Roulette Systems, Strategies, Software**_","target":"https://saliu.com/Roulette.htm","line":71},{"title":"Streaks.bas","target":"https://saliu.com/code/Streaks.bas","line":74},{"title":"Gambling Mathematics: Reaction And Legislation Regarding Online Gambling, Internet Casinos","target":"https://saliu.com/gambling-mathematics.html","line":76},{"title":"SORTING.BAS","target":"https://saliu.com/code/SORTING.BAS","line":79},{"title":"RandomNumbers.bas","target":"https://saliu.com/code/RandomNumbers.bas","line":83},{"title":"True random numbers generator: BASIC programming source code, algorithm","target":"https://saliu.com/random-numbers.html","line":84},{"title":" The software source code you download, however, is free to use for an unlimited period of time.","target":"https://saliu.com/HLINE.gif","line":87},{"title":"Download your software, source code, gambling systems, strategy for casino roulette, baccarat, and blackjack.","target":"https://saliu.com/HLINE.gif","line":89},{"title":"Forums","target":"https://forums.saliu.com/","line":91},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":91},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":91},{"title":"Contents","target":"https://saliu.com/content/index.html","line":91},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":91},{"title":"Home","target":"https://saliu.com/index.htm","line":91},{"title":"Search","target":"https://saliu.com/Search.htm","line":91},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":91},{"title":"Exit the best site of software downloads for lottery, gambling, science, and chance!","target":"https://saliu.com/HLINE.gif","line":93}],"metadata":{"created":"2025-07-24T22:09:35 (UTC +08:00)","tags":["source code","software","programs","Basic","language","PowerBasic","compiler","permanent membership"],"source":"https://saliu.com/software-code.html","author":"Ion Saliu"}},
"smart_sources:notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md": {"path":"notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12185236,-0.03450608,-0.06545053,-0.05131585,-0.02488535,0.0268635,-0.06413176,-0.00906695,-0.00617608,-0.02741327,0.02123508,-0.02233924,0.01495422,-0.01749822,0.00140191,-0.06191181,0.01991335,-0.03551147,-0.04842441,-0.00854239,0.0828248,-0.08525957,0.00752985,-0.07988312,0.0139108,0.05321866,0.00175132,-0.03371924,-0.00536144,-0.18729404,0.01164609,0.04897334,0.03424982,-0.02282392,-0.05889997,-0.02428761,-0.00672404,-0.02987921,-0.10742875,0.04161756,0.01638903,0.01437404,0.00529989,-0.03647492,0.02177985,-0.03550782,0.01597813,0.00837229,0.06365057,0.03582227,-0.00946242,0.02007049,-0.01596604,0.06407817,0.01660801,0.0269475,0.0374172,0.08787291,-0.01637313,0.04705526,-0.00987238,0.04891414,-0.21358959,0.04996243,0.00512663,0.05800695,0.00579438,-0.01181193,0.03854933,-0.05488018,-0.02532817,0.03147969,-0.01395413,0.03324461,0.03109042,-0.04549034,-0.01201737,0.03222704,-0.04009493,0.01677477,-0.0172447,-0.02369197,0.01266791,0.03657318,-0.00137286,0.03980776,0.0235338,0.07738656,0.06625529,-0.06647865,-0.00803113,-0.04107613,0.09121365,0.05609953,-0.01970346,0.03693778,0.05456593,0.04226727,-0.05928486,0.11155312,0.00450291,-0.03009948,-0.04302536,0.0294062,0.07020221,-0.0058962,0.06918642,0.00687448,-0.02817679,-0.00725217,-0.03435607,0.01065813,0.04033962,-0.04131309,-0.01251988,-0.01338592,-0.00175384,-0.05155913,0.00161084,0.04451181,-0.04836866,0.01118458,-0.00665593,-0.01015679,0.02458924,-0.01121277,0.00052196,0.04297964,0.06242345,0.05528275,0.07916935,0.0270447,-0.11543002,-0.06544106,0.01757509,-0.03280751,0.03046091,0.02072272,-0.00746123,-0.00434264,-0.03017093,-0.00139196,0.02872469,-0.06826884,-0.05541502,0.02827892,0.01571466,-0.00278853,0.01819327,0.01061954,-0.00625202,0.00728255,-0.01885187,-0.04288966,0.03840471,0.00352055,0.13194957,0.03574144,-0.06519209,-0.02942072,0.03628984,-0.07175886,-0.00962192,0.11911617,-0.00243593,-0.08744702,-0.00873462,0.02794595,0.00512011,-0.06915856,-0.04685326,-0.00541679,-0.02663564,0.01717849,0.0584799,-0.01905217,-0.0505421,-0.05363199,-0.01659856,0.02848789,-0.0152816,-0.03292024,-0.03378196,0.00137449,-0.03692862,-0.09590065,0.00309265,-0.05784184,0.0319556,0.06163965,-0.03534677,0.05780123,-0.03348385,0.04022634,-0.02646654,-0.00903156,-0.02779269,-0.04143389,0.05456284,-0.02511454,-0.0384596,0.00591865,-0.00785496,0.00050994,-0.01390437,0.01246461,-0.02550258,-0.00851533,0.02403675,-0.00943991,-0.01931042,0.02375549,0.07546283,0.05083442,-0.08161006,0.03913169,-0.00006201,0.00193218,0.02473165,0.04728571,-0.05211665,-0.0015877,-0.06737804,-0.2050359,0.04536823,-0.07355542,-0.05652524,-0.00314587,-0.07153673,0.05334484,-0.07022847,0.00987056,0.05529974,0.17170705,-0.03648011,0.0038409,0.01847163,-0.0305458,0.04029577,-0.01583976,-0.02447285,-0.02927788,0.03639255,-0.0062117,-0.00607965,0.02336271,-0.04183141,0.01374995,-0.03307509,0.13775033,0.04131927,0.0412234,-0.00151545,0.09777797,0.00374222,-0.00439077,-0.1529994,-0.00216879,0.04982596,-0.03401668,0.04583035,0.01510654,0.0312274,-0.05997412,0.00489451,0.00428624,-0.05733696,-0.00895647,-0.03785389,-0.00180849,-0.04893462,-0.04708777,0.01112004,0.0871153,-0.02227306,0.06099233,0.05348988,0.04499699,-0.02165403,-0.03788946,-0.00238077,-0.01404151,0.05462568,0.01987009,-0.01940285,0.03674633,-0.06772186,0.042025,-0.00615183,0.01660113,-0.00418405,0.07956786,-0.01452373,0.00143451,0.10438925,0.03763625,0.00263244,-0.00679463,-0.03077539,0.06033202,-0.03318375,-0.00004027,0.00189683,-0.04724137,-0.03744936,0.04907595,0.06573574,0.04582317,0.08441195,0.05996137,0.03550414,-0.00386971,0.01201093,-0.02557531,0.04883354,-0.052795,0.01755353,0.05627991,0.08111925,-0.28639725,0.04423786,0.00332734,0.00456373,-0.02149901,-0.01668083,0.0748587,-0.10097907,-0.03616208,-0.05408641,0.03763357,0.01947082,0.00123228,-0.07021281,0.05237089,0.01940087,0.02914949,-0.01761968,0.05588409,0.00027386,0.01507603,0.02099047,0.20598462,-0.02228542,-0.01474592,0.04436055,-0.00353098,0.0180819,0.03248513,0.06155044,0.00125327,0.04435961,0.12633893,0.01142557,-0.0602091,0.04685856,-0.04343188,-0.00957329,-0.01721202,-0.01785558,-0.04549208,-0.01743853,0.01616206,-0.00140592,0.0726197,0.03379117,-0.05248003,-0.07772362,0.00355009,0.06898921,-0.08106024,-0.01503812,-0.02072517,-0.01789949,-0.00943607,0.02021499,0.00543899,-0.03201123,0.01262714,-0.03329265,0.03485619,-0.00750264,0.03132049,0.01793482,0.03850311],"last_embed":{"hash":"2ztw1d","tokens":492}}},"last_read":{"hash":"2ztw1d","at":1753423622950},"class_name":"SmartSource","last_import":{"mtime":1753366177315,"size":11178,"at":1753423416500,"hash":"2ztw1d"},"blocks":{"#---frontmatter---":[1,6],"#Source Code for Software, Programs Created by Ion Saliu":[8,94],"#Source Code for Software, Programs Created by Ion Saliu#{1}":[10,94]},"outlinks":[{"title":"Software Download","target":"https://saliu.com/infodown.html","line":14},{"title":"Download Great Free Software: Paid Membership Required","target":"https://saliu.com/membership.html","line":18},{"title":"BJODDS","target":"https://saliu.com/BjOdds/BJODDS","line":22},{"title":"BjOdds: Blackjack Software to Calculate Precisely the Bust Odds, House Edge, House Advantage","target":"https://saliu.com/blackjackodds-software.html","line":27},{"title":"BreakDownNumbers","target":"https://saliu.com/code/BreakDownNumbers","line":31},{"title":"BreakDownNumbers.BAS","target":"https://saliu.com/code/BreakDownNumbers.BAS","line":38},{"title":"LexicographicAlgorithms.bas","target":"https://saliu.com/code/LexicographicAlgorithms.bas","line":41},{"title":"Algorithms, Software to Calculate Combination Lexicographical Order, Rank","target":"https://saliu.com/bbs/messages/348.html","line":42},{"title":"LexicographicAlgorithms","target":"https://saliu.com/code/LexicographicAlgorithms","line":42},{"title":"Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations","target":"https://saliu.com/lexicographic.html","line":42},{"title":"Fibonacci.bas","target":"https://saliu.com/code/Fibonacci.bas","line":46},{"title":"Pi Day, Pi, Divine Proportion, Golden Proportion, Golden Number, Phi, Fibonacci Series","target":"https://saliu.com/bbs/messages/958.html","line":47},{"title":"Fibonacci Progressions: Mathematics, Gambling, Software, Golden Number","target":"https://saliu.com/Fibonacci.html","line":47},{"title":"FileLines.BAS","target":"https://saliu.com/code/FileLines.BAS","line":50},{"title":"\"Cross-reference strategy files created by LotWon and MDIEditor & Lotto WE\"","target":"https://saliu.com/cross-lines.html","line":51},{"title":"INP-FILE.TXT","target":"https://saliu.com/pub/INP-FILE.TXT","line":51},{"title":"PARSEL.BAS","target":"https://saliu.com/code/PARSEL.BAS","line":54},{"title":"Software to correct most errors in lottery, lotto data files","target":"https://saliu.com/bbs/messages/2.html","line":55},{"title":"UpDown.bas","target":"https://saliu.com/code/UPDOWN.BAS","line":58},{"title":"Software to reverse order in lottery, lotto results, drawings files","target":"https://saliu.com/bbs/messages/539.html","line":59},{"title":"Writer.bas","target":"https://saliu.com/code/Writer.bas","line":62},{"title":"Computer program that writes random words, sentences, passwords, and...books!","target":"https://saliu.com/writer.html","line":63},{"title":"SuperRoulette.BAS","target":"https://saliu.com/code/SuperRoulette.BAS","line":66},{"title":"The Super Roulette Strategy","target":"https://saliu.com/best-roulette-systems.html","line":67},{"title":"SPINS.BAS","target":"https://saliu.com/code/SPINS.BAS","line":70},{"title":"_**Theory, Mathematics of Roulette Systems, Strategies, Software**_","target":"https://saliu.com/Roulette.htm","line":71},{"title":"Streaks.bas","target":"https://saliu.com/code/Streaks.bas","line":74},{"title":"Gambling Mathematics: Reaction And Legislation Regarding Online Gambling, Internet Casinos","target":"https://saliu.com/gambling-mathematics.html","line":76},{"title":"SORTING.BAS","target":"https://saliu.com/code/SORTING.BAS","line":79},{"title":"RandomNumbers.bas","target":"https://saliu.com/code/RandomNumbers.bas","line":83},{"title":"True random numbers generator: BASIC programming source code, algorithm","target":"https://saliu.com/random-numbers.html","line":84},{"title":" The software source code you download, however, is free to use for an unlimited period of time.","target":"https://saliu.com/HLINE.gif","line":87},{"title":"Download your software, source code, gambling systems, strategy for casino roulette, baccarat, and blackjack.","target":"https://saliu.com/HLINE.gif","line":89},{"title":"Forums","target":"https://forums.saliu.com/","line":91},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":91},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":91},{"title":"Contents","target":"https://saliu.com/content/index.html","line":91},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":91},{"title":"Home","target":"https://saliu.com/index.htm","line":91},{"title":"Search","target":"https://saliu.com/Search.htm","line":91},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":91},{"title":"Exit the best site of software downloads for lottery, gambling, science, and chance!","target":"https://saliu.com/HLINE.gif","line":93}],"metadata":{"created":"2025-07-24T22:09:35 (UTC +08:00)","tags":["source code","software","programs","Basic","language","PowerBasic","compiler","permanent membership"],"source":"https://saliu.com/software-code.html","author":"Ion Saliu"}},"smart_blocks:notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13662004,-0.01525398,-0.03283878,-0.03900689,0.00725672,0.00494347,-0.07021774,0.0251155,-0.01233154,-0.01999758,0.01505553,-0.03574001,0.01483419,-0.01308421,0.03791015,-0.03371331,-0.01616226,0.00288591,-0.00753675,-0.02599949,0.0986167,-0.03537314,0.00138038,-0.06617437,0.01056191,0.08139823,0.01452109,-0.0473576,0.02215615,-0.13277125,-0.05387337,0.04265646,0.0682259,0.0027286,-0.03510537,0.00221035,-0.00483586,-0.02452783,-0.06671333,0.04800444,-0.01485648,0.01768453,-0.00433654,-0.0357497,-0.00205539,-0.03023693,-0.00144467,0.02344107,0.03238997,0.02157003,0.00474894,0.02344593,-0.04722743,0.05967862,0.01013439,0.04389281,0.05306625,0.0637639,-0.03318159,0.04561028,-0.01320291,0.01124963,-0.2370477,0.06942205,0.01244044,0.04841549,0.01245461,-0.00450393,0.01518889,-0.03579918,-0.05852128,0.01418542,-0.03454404,0.02178381,0.04048652,-0.01619498,0.02121514,0.02535664,-0.03047078,-0.02949945,0.00104307,-0.02728571,-0.04102079,0.03716163,0.00758258,0.01424896,0.04362709,0.08882503,0.0841303,-0.06257825,-0.03337147,-0.06478804,0.12552778,0.06735101,-0.02934904,0.03174825,0.03666254,0.06061564,-0.070485,0.14441915,-0.05942003,-0.00203536,-0.00179488,-0.00813491,0.08339406,0.0119791,0.06718556,0.01882925,-0.03116797,-0.02796238,-0.02201742,0.04582135,0.0096059,-0.09337737,0.05723453,-0.01629802,0.01913404,-0.04009364,0.00686493,0.04658857,-0.02510243,-0.01880498,0.01863928,-0.02550005,0.04946622,-0.01833439,-0.00463316,0.01796489,0.02373876,0.05665886,0.08826847,0.0295426,-0.08209061,-0.03267949,0.00935421,-0.01045083,0.04030118,-0.03594184,-0.01346269,-0.04211038,-0.03802964,0.00026892,0.00962815,-0.03944958,-0.05453596,0.0571999,-0.00524918,0.00489113,-0.02178961,0.00501198,-0.03502849,0.02438794,0.01127195,-0.02460235,0.08416802,0.02667194,0.11586394,0.0100812,-0.04128535,-0.02263119,0.06861655,-0.04008734,-0.05283406,0.13879025,-0.00628531,-0.06751692,-0.01365469,0.07302986,0.04383903,-0.03235363,-0.0211007,-0.00728453,0.02783275,0.00924298,0.06088505,-0.00286169,0.01400965,-0.01109693,-0.00904654,0.04542172,0.00209848,-0.01624075,-0.03138347,0.01805777,-0.02980425,-0.05901505,-0.00889989,-0.07232379,0.03119279,0.06885796,-0.05905729,0.01857169,-0.01025412,0.01801241,-0.02201094,0.00234558,-0.00186927,-0.0296231,0.0148403,-0.02381692,-0.04491203,0.00980224,-0.00789068,-0.01010361,-0.03000693,-0.02299035,-0.03727904,0.00157355,-0.00900805,0.01492487,-0.04161947,0.04599777,0.08952677,0.00471113,-0.06919304,-0.00796731,-0.03893875,0.03169616,0.03983644,0.03465215,-0.075079,-0.034115,-0.08741179,-0.21456343,0.08223701,-0.041472,-0.05617947,-0.02270369,-0.08932975,0.0310159,-0.02830548,-0.02511228,0.01232582,0.1283849,-0.00424176,-0.00748301,-0.03088746,0.00114688,0.03375577,0.05115165,-0.03021318,-0.01775299,0.01504509,-0.00606945,-0.02176718,0.02136651,-0.0561853,-0.00023393,-0.00912836,0.10411049,0.07302915,0.07809366,0.00312623,0.09787425,-0.02357915,-0.01482391,-0.16289754,0.00797399,0.01886956,-0.03430234,0.04144083,0.02918264,0.00628854,-0.0263558,0.01358433,0.00517451,-0.04883259,0.01066269,-0.05567051,-0.01087358,-0.09719081,-0.01721689,-0.01746617,0.0473475,-0.02480952,0.04392619,0.06578673,0.00377739,-0.01603032,-0.05728517,-0.02651284,-0.01628139,0.01973768,0.01120729,-0.02557299,0.02265997,-0.0574404,0.01230952,0.01518279,0.00319602,-0.02237742,0.04812249,0.00604003,0.00467854,0.13231236,0.03491486,-0.02786252,0.00819364,-0.0738718,0.00717902,-0.04193259,0.00102791,-0.02008257,-0.05932411,-0.0609023,0.07362713,0.04109145,0.05323458,0.08404028,0.02445903,0.01263052,0.01633627,-0.00378852,-0.00640734,0.02521002,-0.02211576,-0.02872482,0.09983691,0.04993099,-0.27238733,0.03532923,0.07141107,-0.02797903,-0.00359877,0.01961942,0.07643243,-0.1118279,-0.08588055,-0.04338498,0.01524576,0.02599756,-0.0103264,-0.03636678,0.04413714,0.06427243,0.06275641,-0.03353272,0.03921982,0.01182257,-0.02681166,0.02390846,0.18711549,-0.00196366,-0.00139064,0.04065556,-0.01027447,0.00657924,0.06089648,0.06162222,-0.01233488,0.04131892,0.11457253,0.01165749,-0.06390281,0.09642383,-0.04966988,-0.01686095,-0.01951614,-0.00105819,-0.05861303,-0.00149393,-0.00939223,0.00539745,0.08068613,-0.01170573,-0.05143902,-0.07707679,-0.03011827,0.02015439,-0.07813059,-0.01272587,-0.01714611,0.02233306,0.01314531,0.01874542,0.01481368,-0.03812182,-0.00700418,-0.02825187,0.05645217,-0.01994645,0.04951588,0.0271688,0.06118103],"last_embed":{"hash":"1q8xwfb","tokens":89}}},"text":null,"length":0,"last_read":{"hash":"1q8xwfb","at":1753423622796},"key":"notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md#---frontmatter---","lines":[1,6],"size":206,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md#Source Code for Software, Programs Created by Ion Saliu": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1126576,-0.03295538,-0.0775766,-0.04562203,-0.02551512,0.03094459,-0.05444672,-0.01452859,-0.01277662,-0.03543145,0.02498896,-0.01717741,0.01357594,-0.01515014,-0.00438739,-0.06202967,0.02844884,-0.03363293,-0.05357353,-0.01072166,0.07888893,-0.08540657,-0.0031406,-0.07833226,0.00792295,0.05406084,-0.0056307,-0.03978708,-0.00716296,-0.17909688,0.01057102,0.03864312,0.02618277,-0.02133496,-0.06189086,-0.02200326,-0.00481235,-0.03771926,-0.1117178,0.04406429,0.02074429,0.01809757,0.00111442,-0.03449705,0.02017635,-0.04250751,0.02572833,0.01776543,0.07132803,0.03488447,-0.01475486,0.02690656,-0.0206679,0.06507909,0.01855769,0.01640419,0.04346855,0.09087256,-0.0272298,0.04110508,-0.01766811,0.05513655,-0.21122281,0.04817082,0.00240656,0.06332153,0.00387635,-0.01655923,0.042569,-0.05964587,-0.02957628,0.03545132,-0.02152298,0.02875987,0.02745318,-0.04873206,-0.0169176,0.03430822,-0.03449052,0.02363263,-0.01906351,-0.02735216,0.02075058,0.03986115,-0.00335584,0.03884089,0.01892648,0.07351904,0.06850014,-0.05872775,-0.01141935,-0.02947164,0.08536331,0.05608954,-0.01179616,0.03056461,0.05310502,0.04224389,-0.06652085,0.11429626,0.01571235,-0.03344226,-0.04647049,0.02977551,0.06814519,-0.01398198,0.06365959,0.00389694,-0.02550721,-0.00592557,-0.02826149,0.01288556,0.04374338,-0.02947665,-0.01687919,-0.01556218,-0.02150535,-0.06115479,-0.00109358,0.04025843,-0.04262199,0.01491174,-0.00134854,-0.00681753,0.0173371,-0.01984656,0.00349995,0.03987921,0.05296374,0.06012114,0.07316352,0.0218366,-0.11484203,-0.06427057,0.01828066,-0.03989505,0.0279712,0.02625647,-0.01609992,0.00205572,-0.03564315,-0.00904958,0.03254066,-0.07321168,-0.04650842,0.02522828,0.01236335,-0.00020017,0.0181607,0.01224738,-0.00767504,0.00051898,-0.02389101,-0.04764729,0.03785812,-0.00688622,0.12870482,0.03711678,-0.06771754,-0.02896112,0.02279619,-0.07638171,-0.00167742,0.12375809,0.00306543,-0.08252883,-0.01089728,0.02560138,0.00589107,-0.06920615,-0.049175,-0.00181623,-0.0277279,0.01488063,0.05711932,-0.02277101,-0.06299414,-0.05329277,-0.0147714,0.01951433,-0.02178162,-0.04020341,-0.02663681,-0.0052571,-0.02955531,-0.09810807,-0.00058503,-0.05305371,0.03034321,0.05744828,-0.03126689,0.05983005,-0.02713289,0.03188304,-0.02350402,-0.00737398,-0.0306774,-0.04077304,0.0552161,-0.02880159,-0.03769846,0.00386068,0.00352951,0.01809311,-0.02901287,0.01977444,-0.03250413,-0.01292961,0.0235606,-0.00566082,-0.01347206,0.02248436,0.07231357,0.0585394,-0.08192551,0.05114015,0.00711709,0.00571717,0.01511578,0.06110384,-0.0415757,0.00551506,-0.05595888,-0.20403704,0.04656436,-0.07317961,-0.05461392,-0.00186734,-0.07616495,0.04689185,-0.0738896,0.0129319,0.06122403,0.17433815,-0.03987978,0.00440792,0.0199784,-0.02650511,0.0356287,-0.01600491,-0.02310351,-0.03309418,0.03775686,-0.01586187,-0.00995179,0.0153483,-0.04276317,0.01795035,-0.03190181,0.13658753,0.03939297,0.05218229,-0.00451054,0.08641174,0.00262362,-0.00102584,-0.15504208,-0.00116867,0.04753447,-0.02734517,0.0396211,0.01802588,0.03615877,-0.05760193,0.00435931,0.00316293,-0.05674364,-0.00509522,-0.03284579,-0.00026734,-0.04282457,-0.03891389,0.01058382,0.0921312,-0.0166611,0.06563593,0.0530436,0.04657724,-0.02050109,-0.03456755,0.00112189,-0.01946402,0.05666239,0.01688311,-0.01112829,0.03291125,-0.07392305,0.04886796,-0.00701722,0.02198757,-0.00142281,0.089089,-0.01992691,0.00282449,0.10192662,0.03879482,0.01329669,-0.0113963,-0.02798591,0.06104206,-0.04480524,0.00641648,0.00531971,-0.04414897,-0.04200713,0.04851555,0.06446327,0.04366816,0.08541942,0.06758648,0.03905616,-0.01227181,0.01091675,-0.02310979,0.04765457,-0.06002184,0.01911364,0.04734965,0.08190396,-0.28680429,0.04315104,-0.00378964,0.0142314,-0.01477201,-0.02525268,0.08221128,-0.09186681,-0.03716272,-0.05356343,0.04217234,0.01401267,0.00219155,-0.067367,0.06192534,0.02017752,0.03002955,-0.01883124,0.05191977,0.00632943,0.02170105,0.03378157,0.20344616,-0.02919909,-0.0108449,0.049903,-0.00279078,0.01048691,0.02688787,0.06390373,0.0049751,0.04413157,0.11379593,0.0058638,-0.0509805,0.04916893,-0.03696552,-0.00631117,-0.00995248,-0.01895539,-0.04581776,-0.02017509,0.01325002,-0.00613659,0.0721141,0.03523012,-0.0555255,-0.07610436,0.00586987,0.07157303,-0.07779368,-0.00548294,-0.01741274,-0.01560223,-0.00637212,0.02029401,0.0037933,-0.03184411,0.00735918,-0.03311136,0.04165686,-0.00633454,0.02757746,0.0191848,0.03588426],"last_embed":{"hash":"4f6zrh","tokens":428}}},"text":null,"length":0,"last_read":{"hash":"4f6zrh","at":1753423622825},"key":"notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md#Source Code for Software, Programs Created by Ion Saliu","lines":[8,94],"size":10954,"outlinks":[{"title":"Software Download","target":"https://saliu.com/infodown.html","line":7},{"title":"Download Great Free Software: Paid Membership Required","target":"https://saliu.com/membership.html","line":11},{"title":"BJODDS","target":"https://saliu.com/BjOdds/BJODDS","line":15},{"title":"BjOdds: Blackjack Software to Calculate Precisely the Bust Odds, House Edge, House Advantage","target":"https://saliu.com/blackjackodds-software.html","line":20},{"title":"BreakDownNumbers","target":"https://saliu.com/code/BreakDownNumbers","line":24},{"title":"BreakDownNumbers.BAS","target":"https://saliu.com/code/BreakDownNumbers.BAS","line":31},{"title":"LexicographicAlgorithms.bas","target":"https://saliu.com/code/LexicographicAlgorithms.bas","line":34},{"title":"Algorithms, Software to Calculate Combination Lexicographical Order, Rank","target":"https://saliu.com/bbs/messages/348.html","line":35},{"title":"LexicographicAlgorithms","target":"https://saliu.com/code/LexicographicAlgorithms","line":35},{"title":"Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations","target":"https://saliu.com/lexicographic.html","line":35},{"title":"Fibonacci.bas","target":"https://saliu.com/code/Fibonacci.bas","line":39},{"title":"Pi Day, Pi, Divine Proportion, Golden Proportion, Golden Number, Phi, Fibonacci Series","target":"https://saliu.com/bbs/messages/958.html","line":40},{"title":"Fibonacci Progressions: Mathematics, Gambling, Software, Golden Number","target":"https://saliu.com/Fibonacci.html","line":40},{"title":"FileLines.BAS","target":"https://saliu.com/code/FileLines.BAS","line":43},{"title":"\"Cross-reference strategy files created by LotWon and MDIEditor & Lotto WE\"","target":"https://saliu.com/cross-lines.html","line":44},{"title":"INP-FILE.TXT","target":"https://saliu.com/pub/INP-FILE.TXT","line":44},{"title":"PARSEL.BAS","target":"https://saliu.com/code/PARSEL.BAS","line":47},{"title":"Software to correct most errors in lottery, lotto data files","target":"https://saliu.com/bbs/messages/2.html","line":48},{"title":"UpDown.bas","target":"https://saliu.com/code/UPDOWN.BAS","line":51},{"title":"Software to reverse order in lottery, lotto results, drawings files","target":"https://saliu.com/bbs/messages/539.html","line":52},{"title":"Writer.bas","target":"https://saliu.com/code/Writer.bas","line":55},{"title":"Computer program that writes random words, sentences, passwords, and...books!","target":"https://saliu.com/writer.html","line":56},{"title":"SuperRoulette.BAS","target":"https://saliu.com/code/SuperRoulette.BAS","line":59},{"title":"The Super Roulette Strategy","target":"https://saliu.com/best-roulette-systems.html","line":60},{"title":"SPINS.BAS","target":"https://saliu.com/code/SPINS.BAS","line":63},{"title":"_**Theory, Mathematics of Roulette Systems, Strategies, Software**_","target":"https://saliu.com/Roulette.htm","line":64},{"title":"Streaks.bas","target":"https://saliu.com/code/Streaks.bas","line":67},{"title":"Gambling Mathematics: Reaction And Legislation Regarding Online Gambling, Internet Casinos","target":"https://saliu.com/gambling-mathematics.html","line":69},{"title":"SORTING.BAS","target":"https://saliu.com/code/SORTING.BAS","line":72},{"title":"RandomNumbers.bas","target":"https://saliu.com/code/RandomNumbers.bas","line":76},{"title":"True random numbers generator: BASIC programming source code, algorithm","target":"https://saliu.com/random-numbers.html","line":77},{"title":" The software source code you download, however, is free to use for an unlimited period of time.","target":"https://saliu.com/HLINE.gif","line":80},{"title":"Download your software, source code, gambling systems, strategy for casino roulette, baccarat, and blackjack.","target":"https://saliu.com/HLINE.gif","line":82},{"title":"Forums","target":"https://forums.saliu.com/","line":84},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":84},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":84},{"title":"Contents","target":"https://saliu.com/content/index.html","line":84},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":84},{"title":"Home","target":"https://saliu.com/index.htm","line":84},{"title":"Search","target":"https://saliu.com/Search.htm","line":84},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":84},{"title":"Exit the best site of software downloads for lottery, gambling, science, and chance!","target":"https://saliu.com/HLINE.gif","line":86}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md#Source Code for Software, Programs Created by Ion Saliu#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11242253,-0.03417797,-0.07591062,-0.04724903,-0.02630226,0.0304892,-0.05592753,-0.01669687,-0.01215123,-0.03365755,0.02507768,-0.01729074,0.01395829,-0.01514005,-0.00557532,-0.06090734,0.02960949,-0.03599415,-0.05209469,-0.00937981,0.08018606,-0.08414096,-0.00319632,-0.0782838,0.00663801,0.05635287,-0.00410772,-0.03895958,-0.00739847,-0.17971678,0.01231009,0.03907616,0.02597538,-0.02289831,-0.06031757,-0.02186049,-0.00587936,-0.0360398,-0.11265153,0.0443867,0.0204463,0.01970513,0.00134355,-0.03507021,0.01988406,-0.0426415,0.02477,0.0173264,0.07009166,0.03251437,-0.0131033,0.02732152,-0.02198048,0.06535747,0.01751483,0.01704236,0.04332036,0.09194619,-0.02655588,0.04210099,-0.01650623,0.05618244,-0.21082775,0.04678491,0.00178833,0.06464092,0.00388695,-0.01588351,0.04421209,-0.06033433,-0.02743863,0.03636246,-0.02031446,0.02783802,0.02758008,-0.04910105,-0.01820092,0.03467238,-0.03649131,0.0242956,-0.02084795,-0.02443697,0.02278269,0.04042866,-0.00525041,0.03826756,0.01947353,0.0726435,0.06834821,-0.05525948,-0.01003087,-0.03014689,0.08244331,0.05682082,-0.0118464,0.02908057,0.05126659,0.04126395,-0.063865,0.11447473,0.01565847,-0.03244608,-0.04503472,0.02998658,0.0667918,-0.01436794,0.06260575,0.00419461,-0.02538799,-0.0057975,-0.02973846,0.01326302,0.04186911,-0.02972661,-0.0175932,-0.01707049,-0.02274412,-0.06188558,-0.00274515,0.04053588,-0.04288598,0.01573032,0.00135496,-0.00759694,0.01842109,-0.01874782,0.00198022,0.04145442,0.05391296,0.05963964,0.07314477,0.02181417,-0.11410792,-0.06443039,0.01860281,-0.04102856,0.02879424,0.02698881,-0.01701376,0.00159164,-0.035247,-0.00725137,0.03301519,-0.07221821,-0.04500742,0.02649525,0.01308921,0.00148901,0.01789317,0.01267522,-0.00780461,-0.000408,-0.0251636,-0.04746762,0.03868154,-0.0076147,0.12826525,0.03805742,-0.06765989,-0.02990207,0.02304395,-0.0751499,-0.00051948,0.12449852,0.00252806,-0.08088039,-0.00813753,0.02795123,0.005985,-0.07094429,-0.05056147,-0.00182439,-0.02898638,0.01565115,0.0582486,-0.02139321,-0.06350593,-0.05357758,-0.01178993,0.01857639,-0.02040488,-0.04025533,-0.02565309,-0.00654047,-0.03170869,-0.09617481,-0.00036321,-0.05415756,0.02914632,0.05837701,-0.03236334,0.05864682,-0.02633875,0.03261029,-0.02331169,-0.00727671,-0.032416,-0.0401152,0.05616399,-0.03066849,-0.03532985,0.00313199,0.00262942,0.01516867,-0.02785672,0.02028024,-0.02904006,-0.01174355,0.02364618,-0.00779154,-0.01558908,0.02367431,0.0700206,0.05955944,-0.08181445,0.05087335,0.00681016,0.00462151,0.01333993,0.06014605,-0.04283394,0.0046388,-0.05477972,-0.20379183,0.04522601,-0.072255,-0.05496917,-0.0009504,-0.07610456,0.04673469,-0.07455858,0.01009347,0.06136514,0.17523247,-0.04217329,0.00480375,0.02080425,-0.02742781,0.03469675,-0.01801295,-0.02474717,-0.03145197,0.03738065,-0.01607418,-0.01063259,0.01554694,-0.04426328,0.01853807,-0.03179843,0.13601449,0.03741951,0.05145327,-0.00645741,0.0870225,-0.00006167,-0.00197481,-0.1548882,0.00005697,0.04790169,-0.02644777,0.03808827,0.01840628,0.03587743,-0.05523963,0.00310453,0.00347729,-0.05683575,-0.00365679,-0.03316642,-0.00036103,-0.04306963,-0.0390922,0.01162978,0.09217173,-0.01675623,0.06638523,0.05321747,0.04468665,-0.02070275,-0.03580586,0.00232647,-0.01864081,0.05801259,0.01806475,-0.01189527,0.03247289,-0.07544375,0.05052844,-0.0060497,0.01946031,-0.00279864,0.08929121,-0.01990944,0.00217966,0.10206351,0.03683827,0.01283947,-0.01192438,-0.02898125,0.06148314,-0.0439964,0.00649572,0.00453352,-0.04523806,-0.04206383,0.04884968,0.06442644,0.0437228,0.08463143,0.06677379,0.03835869,-0.01140026,0.00985828,-0.02387879,0.04787607,-0.05938783,0.01961525,0.04776466,0.08190006,-0.28727785,0.04335288,-0.00527522,0.0147356,-0.01570655,-0.02455205,0.0815367,-0.08888278,-0.03640801,-0.05343523,0.04068926,0.01308926,0.00168145,-0.06659938,0.062204,0.02009347,0.0290007,-0.02052912,0.05516217,0.00576246,0.02196956,0.03433032,0.20484728,-0.02975342,-0.01004047,0.04876909,-0.00382063,0.01255667,0.02622346,0.06555104,0.00574482,0.04451126,0.115972,0.00622481,-0.05242335,0.04956573,-0.03535843,-0.00420821,-0.00990669,-0.02095431,-0.04534667,-0.0210766,0.01447614,-0.00470149,0.07183944,0.03448811,-0.05567634,-0.07694857,0.00475576,0.07169867,-0.07949118,-0.00561486,-0.01871905,-0.01467641,-0.00642092,0.02044195,0.00489908,-0.0321897,0.00627284,-0.03246864,0.04076631,-0.00828239,0.02907862,0.01961489,0.03550114],"last_embed":{"hash":"1jogc8w","tokens":427}}},"text":null,"length":0,"last_read":{"hash":"1jogc8w","at":1753423622950},"key":"notes/saliu/Source Code for Software, Programs Created by Ion Saliu.md#Source Code for Software, Programs Created by Ion Saliu#{1}","lines":[10,94],"size":10895,"outlinks":[{"title":"Software Download","target":"https://saliu.com/infodown.html","line":5},{"title":"Download Great Free Software: Paid Membership Required","target":"https://saliu.com/membership.html","line":9},{"title":"BJODDS","target":"https://saliu.com/BjOdds/BJODDS","line":13},{"title":"BjOdds: Blackjack Software to Calculate Precisely the Bust Odds, House Edge, House Advantage","target":"https://saliu.com/blackjackodds-software.html","line":18},{"title":"BreakDownNumbers","target":"https://saliu.com/code/BreakDownNumbers","line":22},{"title":"BreakDownNumbers.BAS","target":"https://saliu.com/code/BreakDownNumbers.BAS","line":29},{"title":"LexicographicAlgorithms.bas","target":"https://saliu.com/code/LexicographicAlgorithms.bas","line":32},{"title":"Algorithms, Software to Calculate Combination Lexicographical Order, Rank","target":"https://saliu.com/bbs/messages/348.html","line":33},{"title":"LexicographicAlgorithms","target":"https://saliu.com/code/LexicographicAlgorithms","line":33},{"title":"Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations","target":"https://saliu.com/lexicographic.html","line":33},{"title":"Fibonacci.bas","target":"https://saliu.com/code/Fibonacci.bas","line":37},{"title":"Pi Day, Pi, Divine Proportion, Golden Proportion, Golden Number, Phi, Fibonacci Series","target":"https://saliu.com/bbs/messages/958.html","line":38},{"title":"Fibonacci Progressions: Mathematics, Gambling, Software, Golden Number","target":"https://saliu.com/Fibonacci.html","line":38},{"title":"FileLines.BAS","target":"https://saliu.com/code/FileLines.BAS","line":41},{"title":"\"Cross-reference strategy files created by LotWon and MDIEditor & Lotto WE\"","target":"https://saliu.com/cross-lines.html","line":42},{"title":"INP-FILE.TXT","target":"https://saliu.com/pub/INP-FILE.TXT","line":42},{"title":"PARSEL.BAS","target":"https://saliu.com/code/PARSEL.BAS","line":45},{"title":"Software to correct most errors in lottery, lotto data files","target":"https://saliu.com/bbs/messages/2.html","line":46},{"title":"UpDown.bas","target":"https://saliu.com/code/UPDOWN.BAS","line":49},{"title":"Software to reverse order in lottery, lotto results, drawings files","target":"https://saliu.com/bbs/messages/539.html","line":50},{"title":"Writer.bas","target":"https://saliu.com/code/Writer.bas","line":53},{"title":"Computer program that writes random words, sentences, passwords, and...books!","target":"https://saliu.com/writer.html","line":54},{"title":"SuperRoulette.BAS","target":"https://saliu.com/code/SuperRoulette.BAS","line":57},{"title":"The Super Roulette Strategy","target":"https://saliu.com/best-roulette-systems.html","line":58},{"title":"SPINS.BAS","target":"https://saliu.com/code/SPINS.BAS","line":61},{"title":"_**Theory, Mathematics of Roulette Systems, Strategies, Software**_","target":"https://saliu.com/Roulette.htm","line":62},{"title":"Streaks.bas","target":"https://saliu.com/code/Streaks.bas","line":65},{"title":"Gambling Mathematics: Reaction And Legislation Regarding Online Gambling, Internet Casinos","target":"https://saliu.com/gambling-mathematics.html","line":67},{"title":"SORTING.BAS","target":"https://saliu.com/code/SORTING.BAS","line":70},{"title":"RandomNumbers.bas","target":"https://saliu.com/code/RandomNumbers.bas","line":74},{"title":"True random numbers generator: BASIC programming source code, algorithm","target":"https://saliu.com/random-numbers.html","line":75},{"title":" The software source code you download, however, is free to use for an unlimited period of time.","target":"https://saliu.com/HLINE.gif","line":78},{"title":"Download your software, source code, gambling systems, strategy for casino roulette, baccarat, and blackjack.","target":"https://saliu.com/HLINE.gif","line":80},{"title":"Forums","target":"https://forums.saliu.com/","line":82},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":82},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":82},{"title":"Contents","target":"https://saliu.com/content/index.html","line":82},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":82},{"title":"Home","target":"https://saliu.com/index.htm","line":82},{"title":"Search","target":"https://saliu.com/Search.htm","line":82},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":82},{"title":"Exit the best site of software downloads for lottery, gambling, science, and chance!","target":"https://saliu.com/HLINE.gif","line":84}],"class_name":"SmartBlock"},
