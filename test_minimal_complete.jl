#!/usr/bin/env julia

# 最小完整測試
println("開始最小完整測試...")

try
    using Dates
    include("src/WonderGridEngine.jl")
    using .WonderGridEngine
    
    println("✓ 模組載入成功")
    
    # 創建最小測試數據
    test_drawings = [
        WonderGridEngine.Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
        WonderGridEngine.Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
        WonderGridEngine.Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47]),
        WonderGridEngine.Drawing(4, :Lotto6_49, Date(2024, 1, 4), [4, 10, 17, 30, 37, 48]),
        WonderGridEngine.Drawing(5, :Lotto6_49, Date(2024, 1, 5), [1, 11, 18, 31, 38, 49])
    ]
    
    println("✓ 測試數據創建成功 ($(length(test_drawings)) 期)")
    
    # 測試配置創建
    config = WonderGridEngine.WonderGridConfig(
        analysis_range = 10,
        top_pair_percentage = 0.25,
        key_number_strategy = :ffg,
        combination_limit = 10,
        enable_purge = true,
        enable_lie = false,
        game_type = :Lotto6_49
    )
    println("✓ 配置創建成功")
    
    # 測試完整的 Wonder Grid 策略執行
    println("\n開始執行完整的 Wonder Grid 策略...")
    result = WonderGridEngine.execute_wonder_grid_strategy(test_drawings, config)
    
    println("✓ Wonder Grid 策略執行成功")
    println("  - 關鍵號碼: $(result.key_number)")
    println("  - 頂級配對數量: $(length(result.top_pairs))")
    println("  - 生成組合數量: $(length(result.combinations))")
    println("  - 執行時間: $(round(result.generation_time, digits=2)) 秒")
    
    println("\n✓ 最小完整測試通過！")
    
catch e
    println("✗ 測試失敗: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

println("\n最小完整測試完成")
