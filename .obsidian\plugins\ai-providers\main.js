/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Jn=Object.create;var dr=Object.defineProperty;var Xn=Object.getOwnPropertyDescriptor;var Gn=Object.getOwnPropertyNames;var Qn=Object.getPrototypeOf,Yn=Object.prototype.hasOwnProperty;var Zn=(s,e)=>{for(var t in e)dr(s,t,{get:e[t],enumerable:!0})},Ds=(s,e,t,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of Gn(e))!Yn.call(s,n)&&n!==t&&dr(s,n,{get:()=>e[n],enumerable:!(r=Xn(e,n))||r.enumerable});return s};var ei=(s,e,t)=>(t=s!=null?Jn(Qn(s)):{},Ds(e||!s||!s.__esModule?dr(t,"default",{value:s,enumerable:!0}):t,s)),ti=s=>Ds(dr({},"__esModule",{value:!0}),s);var To={};Zn(To,{default:()=>qr});module.exports=ti(To);var Ce=require("obsidian");var se=require("obsidian");var $s={settings:{notice:'Dieses Plugin ist eine Konfigurationszentrale f\xFCr KI-Anbieter. Es macht selbst nichts, aber <a href="https://github.com/pfrankov/obsidian-ai-providers#required-by-plugins">andere Plugins</a> k\xF6nnen es nutzen, um KI-Einstellungen nicht mehrfach konfigurieren zu m\xFCssen.',configuredProviders:"KI-anbieter",addNewProvider:"Neuen anbieter hinzuf\xFCgen",addProvider:"Anbieter hinzuf\xFCgen",providerName:"Anbietername",providerNameDesc:"Geben Sie einen Namen f\xFCr diesen Anbieter ein",providerNamePlaceholder:"Mein anbieter",providerType:"Anbietertyp",providerTypeDesc:"W\xE4hlen Sie den KI-anbietertyp aus",providerUrl:"Anbieter-URL",providerUrlDesc:"Geben Sie die API-Endpunkt-URL ein",providerUrlPlaceholder:"https://...",apiKey:"API-Schl\xFCssel",apiKeyDesc:"Geben Sie Ihren API-Schl\xFCssel ein (falls erforderlich)",apiKeyPlaceholder:"sk-...",defaultModel:"Standardmodell",defaultModelDesc:"W\xE4hlen Sie das Standard-KI-Modell f\xFCr diesen Anbieter",save:"Speichern",cancel:"Abbrechen",options:"Optionen",delete:"L\xF6schen",duplicate:"Duplizieren",deleteConfirmation:'M\xF6chten Sie den Anbieter "{{name}}" wirklich l\xF6schen?',modelsUpdated:"Modellliste aktualisiert",refreshModelsList:"Modellliste aktualisieren",model:"Modell",modelDesc:"W\xE4hlen Sie das KI-Modell f\xFCr diesen Anbieter. <br>Oder <a>in den Textmodus wechseln</a>",modelTextDesc:"Geben Sie den KI-Modellnamen ein. <br>Oder <a>in den Dropdown-Modus wechseln</a>",loadingModels:"Modelle werden geladen...",noModelsAvailable:"Keine Modelle verf\xFCgbar",editProvider:"Anbieter bearbeiten",developerSettings:"F\xFCr entwickler",developerSettingsDesc:"Zus\xE4tzliche Einstellungen f\xFCr Entwicklung und Debugging aktivieren",debugLogging:"Debug-protokollierung",debugLoggingDesc:"Detaillierte Protokollierung in der Konsole aktivieren",useNativeFetch:"Nativen fetch verwenden",useNativeFetchDesc:"Verwendet Fetch API anstelle von Obsidian f\xFCr besseres Debugging der Anfragen in DevTools"},modals:{confirm:"Best\xE4tigen",cancel:"Abbrechen"},errors:{failedToFetchModels:"Modelle konnten nicht abgerufen werden",failedToExecuteRequest:"Anfrage konnte nicht ausgef\xFChrt werden",missingApiKey:"API-Schl\xFCssel ist erforderlich",missingUrl:"Anbieter-URL ist erforderlich",missingModel:"Modell muss ausgew\xE4hlt werden",pluginMustBeUpdated:"Plugin muss aktualisiert werden",pluginMustBeUpdatedFormatted:"Das Plugin muss aktualisiert werden, um mit dieser Version von AI Providers zu funktionieren"}};var Bs={settings:{notice:`This plugin is a configuration hub for AI providers. It doesn't do anything on its own, but <a href="https://github.com/pfrankov/obsidian-ai-providers#required-by-plugins">other plugins</a> can use it to avoid configuring AI settings repeatedly.`,configuredProviders:"AI providers",addNewProvider:"Add new provider",addProvider:"Add provider",providerName:"Provider name",providerNameDesc:"Enter a name for this provider",providerNamePlaceholder:"My provider",providerType:"Provider type",providerTypeDesc:"Select the type of AI provider",providerUrl:"Provider URL",providerUrlDesc:"Enter the API endpoint URL",providerUrlPlaceholder:"https://...",apiKey:"API key",apiKeyDesc:"Enter your API key (if required)",apiKeyPlaceholder:"sk-...",defaultModel:"Default model",defaultModelDesc:"Select the default AI model for this provider",save:"Save",cancel:"Cancel",options:"Options",delete:"Delete",duplicate:"Duplicate",deleteConfirmation:'Are you sure you want to delete provider "{{name}}"?',modelsUpdated:"Models list updated",refreshModelsList:"Refresh models list",model:"Model",modelDesc:"Select the AI model for this provider. <br>Or <a>switch to text mode</a>",modelTextDesc:"Enter the AI model name. <br>Or <a>switch to dropdown mode</a>",loadingModels:"Loading models...",noModelsAvailable:"No models available",editProvider:"Edit provider",developerSettings:"For developers",developerSettingsDesc:"Enable additional settings for development and debugging",debugLogging:"Debug logging",debugLoggingDesc:"Enable detailed logging in the console",useNativeFetch:"Use native fetch",useNativeFetchDesc:"Use fetch API instead of Obsidian's for better request debugging in DevTools"},modals:{confirm:"Confirm",cancel:"Cancel"},errors:{failedToFetchModels:"Failed to fetch models",failedToExecuteRequest:"Failed to execute request",missingApiKey:"API key is required",missingUrl:"Provider URL is required",missingModel:"Model must be selected",pluginMustBeUpdated:"Plugin must be updated",pluginMustBeUpdatedFormatted:"Plugin must be updated to work with this version of AI Providers"}};var Ls={settings:{notice:'\u042D\u0442\u043E\u0442 \u043F\u043B\u0430\u0433\u0438\u043D \u043F\u043E\u043C\u043E\u0433\u0430\u0435\u0442 \u043D\u0430\u0441\u0442\u0440\u0430\u0438\u0432\u0430\u0442\u044C AI-\u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u044B. \u0421\u0430\u043C \u043F\u043E \u0441\u0435\u0431\u0435 \u043E\u043D \u043D\u0438\u0447\u0435\u0433\u043E \u043D\u0435 \u0434\u0435\u043B\u0430\u0435\u0442, \u043D\u043E \u0441\u043B\u0443\u0436\u0438\u0442 \u043E\u0431\u0449\u0438\u043C \u0445\u0440\u0430\u043D\u0438\u043B\u0438\u0449\u0435\u043C \u043D\u0430\u0441\u0442\u0440\u043E\u0435\u043A, \u043A\u043E\u0442\u043E\u0440\u044B\u043C \u043C\u043E\u0433\u0443\u0442 \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C\u0441\u044F <a href="https://github.com/pfrankov/obsidian-ai-providers#required-by-plugins">\u0434\u0440\u0443\u0433\u0438\u0435 \u043F\u043B\u0430\u0433\u0438\u043D\u044B</a> \u0432\u043C\u0435\u0441\u0442\u043E \u043F\u043E\u0432\u0442\u043E\u0440\u043D\u043E\u0439 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438.',configuredProviders:"AI \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u044B",addNewProvider:"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043D\u043E\u0432\u044B\u0439 \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440",addProvider:"\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440",providerName:"\u0418\u043C\u044F \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",providerNameDesc:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0438\u043C\u044F \u0434\u043B\u044F \u044D\u0442\u043E\u0433\u043E \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",providerNamePlaceholder:"\u041C\u043E\u0439 \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440",providerType:"\u0422\u0438\u043F \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",providerTypeDesc:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0442\u0438\u043F AI-\u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",providerUrl:"URL \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",providerUrlDesc:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 URL \u043A\u043E\u043D\u0435\u0447\u043D\u043E\u0439 \u0442\u043E\u0447\u043A\u0438 API",providerUrlPlaceholder:"https://...",apiKey:"API-\u043A\u043B\u044E\u0447",apiKeyDesc:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0432\u0430\u0448 API \u043A\u043B\u044E\u0447 (\u0435\u0441\u043B\u0438 \u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F)",apiKeyPlaceholder:"sk-...",defaultModel:"\u041C\u043E\u0434\u0435\u043B\u044C \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",defaultModelDesc:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u043C\u043E\u0434\u0435\u043B\u044C AI \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E \u0434\u043B\u044F \u044D\u0442\u043E\u0433\u043E \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0430",options:"\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",duplicate:"\u0414\u0443\u0431\u043B\u0438\u0440\u043E\u0432\u0430\u0442\u044C",deleteConfirmation:'\u0412\u044B \u0443\u0432\u0435\u0440\u0435\u043D\u044B, \u0447\u0442\u043E \u0445\u043E\u0442\u0438\u0442\u0435 \u0443\u0434\u0430\u043B\u0438\u0442\u044C \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430 "{{name}}"?',modelsUpdated:"\u0421\u043F\u0438\u0441\u043E\u043A \u043C\u043E\u0434\u0435\u043B\u0435\u0439 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D",refreshModelsList:"\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C \u0441\u043F\u0438\u0441\u043E\u043A \u043C\u043E\u0434\u0435\u043B\u0435\u0439",model:"\u041C\u043E\u0434\u0435\u043B\u044C",modelDesc:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 AI \u043C\u043E\u0434\u0435\u043B\u044C \u0434\u043B\u044F \u044D\u0442\u043E\u0433\u043E \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430. <br>\u0418\u043B\u0438 <a>\u043F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C\u0441\u044F \u0432 \u0442\u0435\u043A\u0441\u0442\u043E\u0432\u044B\u0439 \u0440\u0435\u0436\u0438\u043C</a>",modelTextDesc:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 AI \u043C\u043E\u0434\u0435\u043B\u0438. <br>\u0418\u043B\u0438 <a>\u043F\u0435\u0440\u0435\u043A\u043B\u044E\u0447\u0438\u0442\u044C\u0441\u044F \u0432 \u0440\u0435\u0436\u0438\u043C \u0432\u044B\u043F\u0430\u0434\u0430\u044E\u0449\u0435\u0433\u043E \u0441\u043F\u0438\u0441\u043A\u0430</a>",loadingModels:"\u0417\u0430\u0433\u0440\u0443\u0437\u043A\u0430 \u043C\u043E\u0434\u0435\u043B\u0435\u0439...",noModelsAvailable:"\u041D\u0435\u0442 \u0434\u043E\u0441\u0442\u0443\u043F\u043D\u044B\u0445 \u043C\u043E\u0434\u0435\u043B\u0435\u0439",editProvider:"\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",developerSettings:"\u0414\u043B\u044F \u0440\u0430\u0437\u0440\u0430\u0431\u043E\u0442\u0447\u0438\u043A\u043E\u0432",developerSettingsDesc:"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u0434\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0435 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0434\u043B\u044F \u0440\u0430\u0437\u0440\u0430\u0431\u043E\u0442\u043A\u0438 \u0438 \u043E\u0442\u043B\u0430\u0434\u043A\u0438",debugLogging:"\u041E\u0442\u043B\u0430\u0434\u043E\u0447\u043D\u043E\u0435 \u043B\u043E\u0433\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435",debugLoggingDesc:"\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043F\u043E\u0434\u0440\u043E\u0431\u043D\u043E\u0435 \u043B\u043E\u0433\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u0432 \u043A\u043E\u043D\u0441\u043E\u043B\u0438",useNativeFetch:"\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u043D\u0430\u0442\u0438\u0432\u043D\u044B\u0439 Fetch",useNativeFetchDesc:"\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C Fetch API \u0432\u043C\u0435\u0441\u0442\u043E Obsidian \u0434\u043B\u044F \u0443\u0434\u043E\u0431\u043D\u043E\u0439 \u043E\u0442\u043B\u0430\u0434\u043A\u0438 \u0437\u0430\u043F\u0440\u043E\u0441\u043E\u0432 \u0432 DevTools"},modals:{confirm:"\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0430"},errors:{failedToFetchModels:"\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u043F\u043E\u043B\u0443\u0447\u0438\u0442\u044C \u0441\u043F\u0438\u0441\u043E\u043A \u043C\u043E\u0434\u0435\u043B\u0435\u0439",failedToExecuteRequest:"\u041D\u0435 \u0443\u0434\u0430\u043B\u043E\u0441\u044C \u0432\u044B\u043F\u043E\u043B\u043D\u0438\u0442\u044C \u0437\u0430\u043F\u0440\u043E\u0441",missingApiKey:"\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F API \u043A\u043B\u044E\u0447",missingUrl:"\u0422\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F URL \u043F\u0440\u043E\u0432\u0430\u0439\u0434\u0435\u0440\u0430",missingModel:"\u041D\u0435\u043E\u0431\u0445\u043E\u0434\u0438\u043C\u043E \u0432\u044B\u0431\u0440\u0430\u0442\u044C \u043C\u043E\u0434\u0435\u043B\u044C",pluginMustBeUpdated:"\u041F\u043B\u0430\u0433\u0438\u043D \u0442\u0440\u0435\u0431\u0443\u0435\u0442 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u044F",pluginMustBeUpdatedFormatted:"\u0414\u043B\u044F \u0440\u0430\u0431\u043E\u0442\u044B \u0441 \u044D\u0442\u043E\u0439 \u0432\u0435\u0440\u0441\u0438\u0435\u0439 AI Providers \u0442\u0440\u0435\u0431\u0443\u0435\u0442\u0441\u044F \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u0435 \u043F\u043B\u0430\u0433\u0438\u043D\u0430"}};var Us={settings:{notice:'\u6B64\u63D2\u4EF6\u662F AI \u63D0\u4F9B\u5546\u7684\u914D\u7F6E\u4E2D\u5FC3\u3002\u5B83\u672C\u8EAB\u4E0D\u63D0\u4F9B\u4EFB\u4F55\u529F\u80FD\uFF0C\u4F46<a href="https://github.com/pfrankov/obsidian-ai-providers#required-by-plugins">\u5176\u4ED6\u63D2\u4EF6</a>\u53EF\u4EE5\u4F7F\u7528\u5B83\u6765\u907F\u514D\u91CD\u590D\u914D\u7F6E AI \u8BBE\u7F6E\u3002',configuredProviders:"AI \u670D\u52A1\u63D0\u4F9B\u5546",addNewProvider:"\u6DFB\u52A0\u65B0\u7684\u670D\u52A1\u63D0\u4F9B\u5546",addProvider:"\u6DFB\u52A0\u63D0\u4F9B\u5546",providerName:"\u63D0\u4F9B\u5546\u540D\u79F0",providerNameDesc:"\u4E3A\u6B64\u670D\u52A1\u63D0\u4F9B\u5546\u8F93\u5165\u540D\u79F0",providerNamePlaceholder:"\u6211\u7684\u670D\u52A1\u63D0\u4F9B\u5546",providerType:"\u63D0\u4F9B\u5546\u7C7B\u578B",providerTypeDesc:"\u9009\u62E9 AI \u670D\u52A1\u63D0\u4F9B\u5546\u7C7B\u578B",providerUrl:"\u670D\u52A1\u5730\u5740",providerUrlDesc:"\u8F93\u5165 API \u63A5\u53E3\u5730\u5740",providerUrlPlaceholder:"https://...",apiKey:"API \u5BC6\u94A5",apiKeyDesc:"\u8F93\u5165\u60A8\u7684 API \u5BC6\u94A5\uFF08\u5982\u9700\u8981\uFF09",apiKeyPlaceholder:"sk-...",defaultModel:"\u9ED8\u8BA4\u6A21\u578B",defaultModelDesc:"\u9009\u62E9\u6B64\u63D0\u4F9B\u5546\u7684\u9ED8\u8BA4 AI \u6A21\u578B",save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",options:"\u9009\u9879",delete:"\u5220\u9664",duplicate:"\u590D\u5236",deleteConfirmation:'\u786E\u5B9A\u8981\u5220\u9664\u63D0\u4F9B\u5546"{{name}}"\u5417\uFF1F',modelsUpdated:"\u6A21\u578B\u5217\u8868\u5DF2\u66F4\u65B0",refreshModelsList:"\u5237\u65B0\u6A21\u578B\u5217\u8868",model:"\u6A21\u578B",modelDesc:"\u9009\u62E9\u6B64\u63D0\u4F9B\u5546\u7684 AI \u6A21\u578B\u3002<br>\u6216<a>\u5207\u6362\u5230\u6587\u672C\u6A21\u5F0F</a>",modelTextDesc:"\u8F93\u5165 AI \u6A21\u578B\u540D\u79F0\u3002<br>\u6216<a>\u5207\u6362\u5230\u4E0B\u62C9\u6A21\u5F0F</a>",loadingModels:"\u6B63\u5728\u52A0\u8F7D\u6A21\u578B...",noModelsAvailable:"\u6CA1\u6709\u53EF\u7528\u7684\u6A21\u578B",editProvider:"\u7F16\u8F91\u63D0\u4F9B\u5546",developerSettings:"\u5F00\u53D1\u8005\u9009\u9879",developerSettingsDesc:"\u542F\u7528\u989D\u5916\u7684\u5F00\u53D1\u548C\u8C03\u8BD5\u8BBE\u7F6E",debugLogging:"\u8C03\u8BD5\u65E5\u5FD7",debugLoggingDesc:"\u5728\u63A7\u5236\u53F0\u542F\u7528\u8BE6\u7EC6\u65E5\u5FD7\u8BB0\u5F55",useNativeFetch:"\u4F7F\u7528\u539F\u751F Fetch",useNativeFetchDesc:"\u4F7F\u7528 Fetch API \u66FF\u4EE3 Obsidian\uFF0C\u4EE5\u4FBF\u5728 DevTools \u4E2D\u66F4\u597D\u5730\u8C03\u8BD5\u8BF7\u6C42"},modals:{confirm:"\u786E\u8BA4",cancel:"\u53D6\u6D88"},errors:{failedToFetchModels:"\u83B7\u53D6\u6A21\u578B\u5217\u8868\u5931\u8D25",failedToExecuteRequest:"\u8BF7\u6C42\u6267\u884C\u5931\u8D25",missingApiKey:"\u9700\u8981\u63D0\u4F9B API \u5BC6\u94A5",missingUrl:"\u9700\u8981\u63D0\u4F9B\u670D\u52A1\u5730\u5740",missingModel:"\u9700\u8981\u9009\u62E9\u6A21\u578B",pluginMustBeUpdated:"\u63D2\u4EF6\u9700\u8981\u66F4\u65B0",pluginMustBeUpdatedFormatted:"\u63D2\u4EF6\u9700\u8981\u66F4\u65B0\u624D\u80FD\u4E0E\u6B64\u7248\u672C\u7684 AI Providers \u4E00\u8D77\u4F7F\u7528"}};var Hr=class{constructor(){this.enabled=!1}isEnabled(){return this.enabled}setEnabled(e){this.enabled=e}log(e,...t){if(!this.enabled)return;let r=new Date().toISOString(),n=`[AI Providers ${e.toUpperCase()}] ${r}:`;switch(e){case"debug":console.log(n,...t);break;case"info":console.info(n,...t);break;case"warn":console.warn(n,...t);break;case"error":console.error(n,...t);break}}debug(...e){this.log("debug",...e)}info(...e){this.log("info",...e)}warn(...e){this.log("warn",...e)}error(...e){this.log("error",...e)}time(e){this.enabled&&console.time(`[AI Providers] ${e}`)}timeEnd(e){this.enabled&&console.timeEnd(`[AI Providers] ${e}`)}},w=new Hr;var Wr={en:Bs,ru:Ls,de:$s,zh:Us},_=class{static t(e,t){let r=window.localStorage.getItem("language")||"en",n=e.split("."),i=Wr[r]||Wr.en;for(let a of n){if((i==null?void 0:i[a])===void 0){w.warn(`Translation missing: ${e}`),i=Wr.en;let l=i;for(let c of n)l=l==null?void 0:l[c];return l||e}i=i[a]}let o=i;return t&&Object.entries(t).forEach(([a,l])=>{o=o.replace(`{{${a}}}`,l)}),o}};var ur=require("obsidian");var ft=class extends ur.Modal{constructor(t,r,n,i=()=>{}){super(t);this.message=r;this.onConfirm=n,this.onCancel=i}onOpen(){let{contentEl:t}=this;t.createEl("p",{text:this.message}),new ur.Setting(t).addButton(r=>r.setButtonText(_.t("modals.confirm")).setWarning().onClick(()=>{this.onConfirm(),this.close()})).addButton(r=>r.setButtonText(_.t("modals.cancel")).onClick(()=>{this.onCancel(),this.close()}))}onClose(){let{contentEl:t}=this;t.empty()}};var Q=require("obsidian");var hr=class extends Q.Modal{constructor(t,r,n,i,o=!1){super(t);this.plugin=r;this.provider=n;this.onSave=i;this.isAddingNew=o;this.isLoadingModels=!1;this.isTextMode=!1;this.defaultProvidersUrls={openai:"https://api.openai.com/v1",ollama:"http://localhost:11434",gemini:"https://generativelanguage.googleapis.com/v1beta/openai",openrouter:"https://openrouter.ai/api/v1",lmstudio:"http://localhost:1234/v1",groq:"https://api.groq.com/openai/v1"}}createModelSetting(t){let r=new Q.Setting(t).setName(_.t("settings.model")).setDesc(this.isTextMode?_.t("settings.modelTextDesc"):_.t("settings.modelDesc"));this.isTextMode?r.addText(o=>(o.setValue(this.provider.model||"").onChange(a=>{this.provider.model=a}),o.inputEl.setAttribute("data-testid","model-input"),o)):(r.addDropdown(o=>{var a;if(this.isLoadingModels)o.addOption("loading",_.t("settings.loadingModels")),o.setDisabled(!0);else{let l=this.provider.availableModels;!l||l.length===0?(o.addOption("none",_.t("settings.noModelsAvailable")),o.setDisabled(!0)):(l.forEach(c=>{o.addOption(c,c);let d=o.selectEl.options,u=d[d.length-1];u.title=c}),o.setDisabled(!1))}return o.setValue(this.provider.model||"").onChange(l=>{this.provider.model=l,o.selectEl.title=l}),o.selectEl.setAttribute("data-testid","model-dropdown"),o.selectEl.title=this.provider.model||"",(a=o.selectEl.parentElement)==null||a.addClass("ai-providers-model-dropdown"),o}),this.isTextMode||r.addButton(o=>{o.setIcon("refresh-cw").setTooltip(_.t("settings.refreshModelsList")),o.buttonEl.setAttribute("data-testid","refresh-models-button"),this.isLoadingModels&&(o.setDisabled(!0),o.buttonEl.addClass("loading")),o.onClick(async()=>{try{this.isLoadingModels=!0,this.display();let a=await this.plugin.aiProviders.fetchModels(this.provider);this.provider.availableModels=a,a.length>0&&(this.provider.model=a[0]||""),new Q.Notice(_.t("settings.modelsUpdated"))}catch(a){w.error("Failed to fetch models:",a),new Q.Notice(_.t("errors.failedToFetchModels"))}finally{this.isLoadingModels=!1,this.display()}})}));let n=r.descEl;n.empty(),n.appendChild((0,Q.sanitizeHTMLToDom)(this.isTextMode?_.t("settings.modelTextDesc"):_.t("settings.modelDesc")));let i=n.querySelector("a");return i&&i.addEventListener("click",o=>{o.preventDefault(),this.isTextMode=!this.isTextMode,this.display()}),r}onOpen(){let{contentEl:t}=this;t.createEl("h2",{text:this.isAddingNew?_.t("settings.addNewProvider"):_.t("settings.editProvider")}).setAttribute("data-testid","provider-form-title"),new Q.Setting(t).setName(_.t("settings.providerType")).setDesc(_.t("settings.providerTypeDesc")).addDropdown(r=>(r.addOptions({openai:"OpenAI",ollama:"Ollama",openrouter:"OpenRouter",gemini:"Google Gemini",lmstudio:"LM Studio",groq:"Groq"}).setValue(this.provider.type).onChange(n=>{this.provider.type=n,this.provider.url=this.defaultProvidersUrls[n],this.provider.availableModels=void 0,this.provider.model=void 0,this.display()}),r.selectEl.setAttribute("data-testid","provider-type-dropdown"),r)),new Q.Setting(t).setName(_.t("settings.providerName")).setDesc(_.t("settings.providerNameDesc")).addText(r=>r.setPlaceholder(_.t("settings.providerNamePlaceholder")).setValue(this.provider.name).onChange(n=>this.provider.name=n)),new Q.Setting(t).setName(_.t("settings.providerUrl")).setDesc(_.t("settings.providerUrlDesc")).addText(r=>r.setPlaceholder(_.t("settings.providerUrlPlaceholder")).setValue(this.provider.url||"").onChange(n=>this.provider.url=n)),new Q.Setting(t).setName(_.t("settings.apiKey")).setDesc(_.t("settings.apiKeyDesc")).addText(r=>r.setPlaceholder(_.t("settings.apiKeyPlaceholder")).setValue(this.provider.apiKey||"").onChange(n=>this.provider.apiKey=n)),this.createModelSetting(t),new Q.Setting(t).addButton(r=>r.setButtonText(_.t("settings.save")).setCta().onClick(async()=>{await this.onSave(this.provider),this.close()})).addButton(r=>(r.setButtonText(_.t("settings.cancel")).onClick(()=>{this.close()}),r.buttonEl.setAttribute("data-testid","cancel-button"),r))}onClose(){let{contentEl:t}=this;t.empty()}display(){let{contentEl:t}=this;t.empty(),this.onOpen()}};var js={_version:1,debugLogging:!1,useNativeFetch:!1},fr=class extends se.PluginSettingTab{constructor(t,r){super(t,r);this.isFormOpen=!1;this.isDeveloperMode=!1;this.plugin=r}openForm(t,r){let n=r||{id:`id-${Date.now().toString()}`,name:"",apiKey:"",url:"",type:"openai",model:""};new hr(this.app,this.plugin,n,async i=>{await this.saveProvider(i)},t).open()}closeForm(){this.isFormOpen=!1,this.display()}validateProvider(t){if(!t.id||!t.name||!t.type)return!1;if(t.url)try{new URL(t.url)}catch(i){return!1}return!(this.plugin.settings.providers||[]).find(i=>i.name===t.name&&i.id!==t.id)}async saveProvider(t){if(!this.validateProvider(t))return;let r=this.plugin.settings.providers||[],n=r.findIndex(i=>i.id===t.id);n!==-1?r[n]=t:r.push(t),this.plugin.settings.providers=r,await this.plugin.saveSettings(),this.closeForm()}async deleteProvider(t){let r=this.plugin.settings.providers||[],n=r.findIndex(i=>i.id===t.id);n!==-1&&(r.splice(n,1),this.plugin.settings.providers=r,await this.plugin.saveSettings(),this.display())}async duplicateProvider(t){let r={...t,id:`id-${Date.now().toString()}`,name:`${t.name} (${_.t("settings.duplicate")})`},n=this.plugin.settings.providers||[];n.push(r),this.plugin.settings.providers=n,await this.plugin.saveSettings(),this.display()}display(){let{containerEl:t}=this;t.empty();let r=t.createDiv("ai-providers-main-interface");r.setAttribute("data-testid","main-interface"),r.createDiv("ai-providers-notice").createDiv("ai-providers-notice-content").appendChild((0,se.sanitizeHTMLToDom)(`${_.t("settings.notice")}`)),new se.Setting(r).setHeading().setName(_.t("settings.configuredProviders")).addButton(a=>{let l=a.setIcon("plus").setTooltip(_.t("settings.addProvider")).onClick(()=>{this.isFormOpen||this.openForm(!0)});return l.buttonEl.setAttribute("aria-label",_.t("settings.addProvider")),l.buttonEl.setAttribute("data-testid","add-provider-button"),l});let o=this.plugin.settings.providers||[];if(o.length>0&&o.forEach(a=>{let l=new se.Setting(r).setName(a.name).setDesc(a.url||""),c=l.nameEl.createSpan("ai-providers-provider-icon");if((0,se.setIcon)(c,`ai-providers-${a.type}`),l.nameEl.prepend(c),a.model){let d=l.settingEl.createDiv("ai-providers-model-pill");d.textContent=a.model,d.setAttribute("data-testid","model-pill"),l.nameEl.after(d)}l.addExtraButton(d=>{d.setIcon("gear").setTooltip(_.t("settings.options")).onClick(()=>{this.isFormOpen||this.openForm(!1,{...a})}),d.extraSettingsEl.setAttribute("data-testid","edit-provider")}).addExtraButton(d=>{d.setIcon("copy").setTooltip(_.t("settings.duplicate")).onClick(async()=>{await this.duplicateProvider(a)}),d.extraSettingsEl.setAttribute("data-testid","duplicate-provider")}).addExtraButton(d=>{d.setIcon("lucide-trash-2").setTooltip(_.t("settings.delete")).onClick(async()=>{new ft(this.app,_.t("settings.deleteConfirmation",{name:a.name}),async()=>{await this.deleteProvider(a)}).open()}),d.extraSettingsEl.setAttribute("data-testid","delete-provider")})}),new se.Setting(r).setHeading().setName(_.t("settings.developerSettings")).setDesc(_.t("settings.developerSettingsDesc")).setClass("ai-providers-developer-settings-toggle").addToggle(a=>a.setValue(this.isDeveloperMode).onChange(l=>{this.isDeveloperMode=l,this.display()})),this.isDeveloperMode){let a=r.createDiv("ai-providers-developer-settings");new se.Setting(a).setName(_.t("settings.debugLogging")).setDesc(_.t("settings.debugLoggingDesc")).addToggle(l=>{var c;return l.setValue((c=this.plugin.settings.debugLogging)!=null?c:!1).onChange(async d=>{this.plugin.settings.debugLogging=d,w.setEnabled(d),await this.plugin.saveSettings()})}),new se.Setting(a).setName(_.t("settings.useNativeFetch")).setDesc(_.t("settings.useNativeFetchDesc")).addToggle(l=>{var c;return l.setValue((c=this.plugin.settings.useNativeFetch)!=null?c:!1).onChange(async d=>{this.plugin.settings.useNativeFetch=d,await this.plugin.saveSettings()})})}}};var cr=require("obsidian");var qs=require("electron"),zr=require("obsidian");async function ce(s,e={}){var r;delete e.headers["content-length"];let t={};return this&&"controller"in this&&(t.controller=this.controller),w.debug("electronFetch request:",{url:s,method:e.method||"GET",headers:e.headers,hasBody:!!e.body,platform:zr.Platform.isMobileApp?"mobile":"desktop"}),zr.Platform.isMobileApp?(w.debug("Using native fetch (mobile platform)"),fetch(s,{...e,signal:((r=t.controller)==null?void 0:r.signal)||e.signal})):new Promise((n,i)=>{var l,c;let o=()=>{a.removeAllListeners(),w.debug("Request cleanup completed")},a=qs.remote.net.request({url:s,method:e.method||"GET"});if(e.headers&&Object.entries(e.headers).forEach(([d,u])=>{a.setHeader(d,u)}),(l=t.controller)!=null&&l.signal.aborted){w.debug("Request aborted before start"),a.abort(),i(new Error("Aborted"));return}(c=t.controller)==null||c.signal.addEventListener("abort",()=>{w.debug("Request aborted by controller"),o(),a.abort(),i(new Error("Aborted"))}),a.on("response",d=>{var x;if((x=t.controller)!=null&&x.signal.aborted){w.debug("Request aborted during response");return}w.debug("Response received:",{status:d.statusCode,headers:d.headers});let{readable:u,writable:h}=new TransformStream({transform(p,E){E.enqueue(new Uint8Array(p))}}),f=h.getWriter(),m={status:d.statusCode||200,headers:d.headers};n(new Response(u,m)),d.on("data",async p=>{try{await f.ready,await f.write(p),w.debug("Chunk received:",{size:p.length})}catch(E){w.error("Error writing chunk:",E),o(),f.abort(E)}}),d.on("end",async()=>{try{await f.ready,await f.close(),w.debug("Response stream completed")}catch(p){w.error("Error closing writer:",p)}finally{o()}}),d.on("error",p=>{w.error("Response error:",p),o(),f.abort(p),i(p)})}),a.on("error",d=>{w.error("Request error:",d),o(),i(d)}),e.body&&a.write(e.body),a.end(),w.debug("Request sent")})}var mr="RFC3986",pr={RFC1738:s=>String(s).replace(/%20/g,"+"),RFC3986:s=>String(s)},Hs="RFC1738";var oi=Array.isArray,de=(()=>{let s=[];for(let e=0;e<256;++e)s.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return s})();var Kr=1024,Ws=(s,e,t,r,n)=>{if(s.length===0)return s;let i=s;if(typeof s=="symbol"?i=Symbol.prototype.toString.call(s):typeof s!="string"&&(i=String(s)),t==="iso-8859-1")return escape(i).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let o="";for(let a=0;a<i.length;a+=Kr){let l=i.length>=Kr?i.slice(a,a+Kr):i,c=[];for(let d=0;d<l.length;++d){let u=l.charCodeAt(d);if(u===45||u===46||u===95||u===126||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||n===Hs&&(u===40||u===41)){c[c.length]=l.charAt(d);continue}if(u<128){c[c.length]=de[u];continue}if(u<2048){c[c.length]=de[192|u>>6]+de[128|u&63];continue}if(u<55296||u>=57344){c[c.length]=de[224|u>>12]+de[128|u>>6&63]+de[128|u&63];continue}d+=1,u=65536+((u&1023)<<10|l.charCodeAt(d)&1023),c[c.length]=de[240|u>>18]+de[128|u>>12&63]+de[128|u>>6&63]+de[128|u&63]}o+=c.join("")}return o};function zs(s){return!s||typeof s!="object"?!1:!!(s.constructor&&s.constructor.isBuffer&&s.constructor.isBuffer(s))}function Vr(s,e){if(oi(s)){let t=[];for(let r=0;r<s.length;r+=1)t.push(e(s[r]));return t}return e(s)}var ai=Object.prototype.hasOwnProperty,Ks={brackets(s){return String(s)+"[]"},comma:"comma",indices(s,e){return String(s)+"["+e+"]"},repeat(s){return String(s)}},ue=Array.isArray,li=Array.prototype.push,Vs=function(s,e){li.apply(s,ue(e)?e:[e])},ci=Date.prototype.toISOString,U={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Ws,encodeValuesOnly:!1,format:mr,formatter:pr[mr],indices:!1,serializeDate(s){return ci.call(s)},skipNulls:!1,strictNullHandling:!1};function di(s){return typeof s=="string"||typeof s=="number"||typeof s=="boolean"||typeof s=="symbol"||typeof s=="bigint"}var Jr={};function Js(s,e,t,r,n,i,o,a,l,c,d,u,h,f,m,x,p,E){let b=s,S=E,D=0,A=!1;for(;(S=S.get(Jr))!==void 0&&!A;){let C=S.get(s);if(D+=1,typeof C!="undefined"){if(C===D)throw new RangeError("Cyclic object value");A=!0}typeof S.get(Jr)=="undefined"&&(D=0)}if(typeof c=="function"?b=c(e,b):b instanceof Date?b=h==null?void 0:h(b):t==="comma"&&ue(b)&&(b=Vr(b,function(C){return C instanceof Date?h==null?void 0:h(C):C})),b===null){if(i)return l&&!x?l(e,U.encoder,p,"key",f):e;b=""}if(di(b)||zs(b)){if(l){let C=x?e:l(e,U.encoder,p,"key",f);return[(m==null?void 0:m(C))+"="+(m==null?void 0:m(l(b,U.encoder,p,"value",f)))]}return[(m==null?void 0:m(e))+"="+(m==null?void 0:m(String(b)))]}let N=[];if(typeof b=="undefined")return N;let M;if(t==="comma"&&ue(b))x&&l&&(b=Vr(b,l)),M=[{value:b.length>0?b.join(",")||null:void 0}];else if(ue(c))M=c;else{let C=Object.keys(b);M=d?C.sort(d):C}let T=a?String(e).replace(/\./g,"%2E"):String(e),P=r&&ue(b)&&b.length===1?T+"[]":T;if(n&&ue(b)&&b.length===0)return P+"[]";for(let C=0;C<M.length;++C){let F=M[C],R=typeof F=="object"&&typeof F.value!="undefined"?F.value:b[F];if(o&&R===null)continue;let re=u&&a?F.replace(/\./g,"%2E"):F,_e=ue(b)?typeof t=="function"?t(P,re):P:P+(u?"."+re:"["+re+"]");E.set(s,D);let Z=new WeakMap;Z.set(Jr,E),Vs(N,Js(R,_e,t,r,n,i,o,a,t==="comma"&&x&&ue(b)?null:l,c,d,u,h,f,m,x,p,Z))}return N}function ui(s=U){if(typeof s.allowEmptyArrays!="undefined"&&typeof s.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof s.encodeDotInKeys!="undefined"&&typeof s.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(s.encoder!==null&&typeof s.encoder!="undefined"&&typeof s.encoder!="function")throw new TypeError("Encoder has to be a function.");let e=s.charset||U.charset;if(typeof s.charset!="undefined"&&s.charset!=="utf-8"&&s.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let t=mr;if(typeof s.format!="undefined"){if(!ai.call(pr,s.format))throw new TypeError("Unknown format option provided.");t=s.format}let r=pr[t],n=U.filter;(typeof s.filter=="function"||ue(s.filter))&&(n=s.filter);let i;if(s.arrayFormat&&s.arrayFormat in Ks?i=s.arrayFormat:"indices"in s?i=s.indices?"indices":"repeat":i=U.arrayFormat,"commaRoundTrip"in s&&typeof s.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");let o=typeof s.allowDots=="undefined"?s.encodeDotInKeys?!0:U.allowDots:!!s.allowDots;return{addQueryPrefix:typeof s.addQueryPrefix=="boolean"?s.addQueryPrefix:U.addQueryPrefix,allowDots:o,allowEmptyArrays:typeof s.allowEmptyArrays=="boolean"?!!s.allowEmptyArrays:U.allowEmptyArrays,arrayFormat:i,charset:e,charsetSentinel:typeof s.charsetSentinel=="boolean"?s.charsetSentinel:U.charsetSentinel,commaRoundTrip:!!s.commaRoundTrip,delimiter:typeof s.delimiter=="undefined"?U.delimiter:s.delimiter,encode:typeof s.encode=="boolean"?s.encode:U.encode,encodeDotInKeys:typeof s.encodeDotInKeys=="boolean"?s.encodeDotInKeys:U.encodeDotInKeys,encoder:typeof s.encoder=="function"?s.encoder:U.encoder,encodeValuesOnly:typeof s.encodeValuesOnly=="boolean"?s.encodeValuesOnly:U.encodeValuesOnly,filter:n,format:t,formatter:r,serializeDate:typeof s.serializeDate=="function"?s.serializeDate:U.serializeDate,skipNulls:typeof s.skipNulls=="boolean"?s.skipNulls:U.skipNulls,sort:typeof s.sort=="function"?s.sort:null,strictNullHandling:typeof s.strictNullHandling=="boolean"?s.strictNullHandling:U.strictNullHandling}}function Xr(s,e={}){let t=s,r=ui(e),n,i;typeof r.filter=="function"?(i=r.filter,t=i("",t)):ue(r.filter)&&(i=r.filter,n=i);let o=[];if(typeof t!="object"||t===null)return"";let a=Ks[r.arrayFormat],l=a==="comma"&&r.commaRoundTrip;n||(n=Object.keys(t)),r.sort&&n.sort(r.sort);let c=new WeakMap;for(let h=0;h<n.length;++h){let f=n[h];r.skipNulls&&t[f]===null||Vs(o,Js(t[f],f,a,l,r.allowEmptyArrays,r.strictNullHandling,r.skipNulls,r.encodeDotInKeys,r.encode?r.encoder:null,r.filter,r.sort,r.allowDots,r.serializeDate,r.format,r.formatter,r.encodeValuesOnly,r.charset,c))}let d=o.join(r.delimiter),u=r.addQueryPrefix===!0?"?":"";return r.charsetSentinel&&(r.charset==="iso-8859-1"?u+="utf8=%26%2310003%3B&":u+="utf8=%E2%9C%93&"),d.length>0?u+d:""}var ze="4.73.1";var Xs=!1,Ke,Gr,fi,mi,pi,Qr,gi,gr,Yr,Zr,es,yr,ts;function Gs(s,e={auto:!1}){if(Xs)throw new Error(`you must \`import 'openai/shims/${s.kind}'\` before importing anything else from openai`);if(Ke)throw new Error(`can't \`import 'openai/shims/${s.kind}'\` after \`import 'openai/shims/${Ke}'\``);Xs=e.auto,Ke=s.kind,Gr=s.fetch,fi=s.Request,mi=s.Response,pi=s.Headers,Qr=s.FormData,gi=s.Blob,gr=s.File,Yr=s.ReadableStream,Zr=s.getMultipartRequestOptions,es=s.getDefaultAgent,yr=s.fileFromPath,ts=s.isFsReadStream}var br=class{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}};function Qs({manuallyImported:s}={}){let e=s?"You may need to use polyfills":"Add one of these imports before your first `import \u2026 from 'openai'`:\n- `import 'openai/shims/node'` (if you're running on Node)\n- `import 'openai/shims/web'` (otherwise)\n",t,r,n,i;try{t=fetch,r=Request,n=Response,i=Headers}catch(o){throw new Error(`this environment is missing the following Web Fetch API type: ${o.message}. ${e}`)}return{kind:"web",fetch:t,Request:r,Response:n,Headers:i,FormData:typeof FormData!="undefined"?FormData:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'FormData' is undefined. ${e}`)}},Blob:typeof Blob!="undefined"?Blob:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'Blob' is undefined. ${e}`)}},File:typeof File!="undefined"?File:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'File' is undefined. ${e}`)}},ReadableStream:typeof ReadableStream!="undefined"?ReadableStream:class{constructor(){throw new Error(`streaming isn't supported in this environment yet as 'ReadableStream' is undefined. ${e}`)}},getMultipartRequestOptions:async(o,a)=>({...a,body:new br(o)}),getDefaultAgent:o=>{},fileFromPath:()=>{throw new Error("The `fileFromPath` function is only supported in Node. See the README for more details: https://www.github.com/openai/openai-node#file-uploads")},isFsReadStream:o=>!1}}Ke||Gs(Qs(),{auto:!0});var v=class extends Error{},L=class extends v{constructor(e,t,r,n){super(`${L.makeMessage(e,t,r)}`),this.status=e,this.headers=n,this.request_id=n==null?void 0:n["x-request-id"];let i=t;this.error=i,this.code=i==null?void 0:i.code,this.param=i==null?void 0:i.param,this.type=i==null?void 0:i.type}static makeMessage(e,t,r){let n=t!=null&&t.message?typeof t.message=="string"?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,r,n){if(!e)return new Se({message:r,cause:wr(t)});let i=t==null?void 0:t.error;return e===400?new Ut(e,i,r,n):e===401?new jt(e,i,r,n):e===403?new qt(e,i,r,n):e===404?new Ht(e,i,r,n):e===409?new Wt(e,i,r,n):e===422?new zt(e,i,r,n):e===429?new Kt(e,i,r,n):e>=500?new Vt(e,i,r,n):new L(e,i,r,n)}},H=class extends L{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0),this.status=void 0}},Se=class extends L{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),this.status=void 0,t&&(this.cause=t)}},Re=class extends Se{constructor({message:e}={}){super({message:e!=null?e:"Request timed out."})}},Ut=class extends L{constructor(){super(...arguments),this.status=400}},jt=class extends L{constructor(){super(...arguments),this.status=401}},qt=class extends L{constructor(){super(...arguments),this.status=403}},Ht=class extends L{constructor(){super(...arguments),this.status=404}},Wt=class extends L{constructor(){super(...arguments),this.status=409}},zt=class extends L{constructor(){super(...arguments),this.status=422}},Kt=class extends L{constructor(){super(...arguments),this.status=429}},Vt=class extends L{},mt=class extends v{constructor(){super("Could not parse response content as the length limit was reached")}},pt=class extends v{constructor(){super("Could not parse response content as the request was rejected by the content filter")}};var he=class{constructor(){this.buffer=[],this.trailingCR=!1}decode(e){let t=this.decodeText(e);if(this.trailingCR&&(t="\r"+t,this.trailingCR=!1),t.endsWith("\r")&&(this.trailingCR=!0,t=t.slice(0,-1)),!t)return[];let r=he.NEWLINE_CHARS.has(t[t.length-1]||""),n=t.split(he.NEWLINE_REGEXP);return r&&n.pop(),n.length===1&&!r?(this.buffer.push(n[0]),[]):(this.buffer.length>0&&(n=[this.buffer.join("")+n[0],...n.slice(1)],this.buffer=[]),r||(this.buffer=[n.pop()||""]),n)}decodeText(e){var t;if(e==null)return"";if(typeof e=="string")return e;if(typeof Buffer!="undefined"){if(e instanceof Buffer)return e.toString();if(e instanceof Uint8Array)return Buffer.from(e).toString();throw new v(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if(typeof TextDecoder!="undefined"){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return(t=this.textDecoder)!=null||(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new v(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new v("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){if(!this.buffer.length&&!this.trailingCR)return[];let e=[this.buffer.join("")];return this.buffer=[],this.trailingCR=!1,e}};he.NEWLINE_CHARS=new Set([`
`,"\r"]);he.NEWLINE_REGEXP=/\r\n|[\n\r]/g;var J=class{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1;async function*n(){if(r)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let i=!1;try{for await(let o of wi(e,t))if(!i){if(o.data.startsWith("[DONE]")){i=!0;continue}if(o.event===null){let a;try{a=JSON.parse(o.data)}catch(l){throw console.error("Could not parse message into JSON:",o.data),console.error("From chunk:",o.raw),l}if(a&&a.error)throw new L(void 0,a.error,void 0,void 0);yield a}else{let a;try{a=JSON.parse(o.data)}catch(l){throw console.error("Could not parse message into JSON:",o.data),console.error("From chunk:",o.raw),l}if(o.event=="error")throw new L(void 0,a.error,a.message,void 0);yield{event:o.event,data:a}}}i=!0}catch(o){if(o instanceof Error&&o.name==="AbortError")return;throw o}finally{i||t.abort()}}return new J(n,t)}static fromReadableStream(e,t){let r=!1;async function*n(){let o=new he,a=Ys(e);for await(let l of a)for(let c of o.decode(l))yield c;for(let l of o.flush())yield l}async function*i(){if(r)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let o=!1;try{for await(let a of n())o||a&&(yield JSON.parse(a));o=!0}catch(a){if(a instanceof Error&&a.name==="AbortError")return;throw a}finally{o||t.abort()}}return new J(i,t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),n=i=>({next:()=>{if(i.length===0){let o=r.next();e.push(o),t.push(o)}return i.shift()}});return[new J(()=>n(e),this.controller),new J(()=>n(t),this.controller)]}toReadableStream(){let e=this,t,r=new TextEncoder;return new Yr({async start(){t=e[Symbol.asyncIterator]()},async pull(n){try{let{value:i,done:o}=await t.next();if(o)return n.close();let a=r.encode(JSON.stringify(i)+`
`);n.enqueue(a)}catch(i){n.error(i)}},async cancel(){var n;await((n=t.return)==null?void 0:n.call(t))}})}};async function*wi(s,e){if(!s.body)throw e.abort(),new v("Attempted to iterate over a response with no body");let t=new rs,r=new he,n=Ys(s.body);for await(let i of _i(n))for(let o of r.decode(i)){let a=t.decode(o);a&&(yield a)}for(let i of r.flush()){let o=t.decode(i);o&&(yield o)}}async function*_i(s){let e=new Uint8Array;for await(let t of s){if(t==null)continue;let r=t instanceof ArrayBuffer?new Uint8Array(t):typeof t=="string"?new TextEncoder().encode(t):t,n=new Uint8Array(e.length+r.length);n.set(e),n.set(r,e.length),e=n;let i;for(;(i=vi(e))!==-1;)yield e.slice(0,i),e=e.slice(i)}e.length>0&&(yield e)}function vi(s){for(let r=0;r<s.length-2;r++){if(s[r]===10&&s[r+1]===10||s[r]===13&&s[r+1]===13)return r+2;if(s[r]===13&&s[r+1]===10&&r+3<s.length&&s[r+2]===13&&s[r+3]===10)return r+4}return-1}var rs=class{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let i={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],i}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,n]=Ai(e,":");return n.startsWith(" ")&&(n=n.substring(1)),t==="event"?this.event=n:t==="data"&&this.data.push(n),null}};function Ai(s,e){let t=s.indexOf(e);return t!==-1?[s.substring(0,t),e,s.substring(t+e.length)]:[s,"",""]}function Ys(s){if(s[Symbol.asyncIterator])return s;let e=s.getReader();return{async next(){try{let t=await e.read();return t!=null&&t.done&&e.releaseLock(),t}catch(t){throw e.releaseLock(),t}},async return(){let t=e.cancel();return e.releaseLock(),await t,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}var Zs=s=>s!=null&&typeof s=="object"&&typeof s.url=="string"&&typeof s.blob=="function",en=s=>s!=null&&typeof s=="object"&&typeof s.name=="string"&&typeof s.lastModified=="number"&&Jt(s),Jt=s=>s!=null&&typeof s=="object"&&typeof s.size=="number"&&typeof s.type=="string"&&typeof s.text=="function"&&typeof s.slice=="function"&&typeof s.arrayBuffer=="function",xi=s=>en(s)||Zs(s)||ts(s);async function is(s,e,t){var n,i,o;if(s=await s,en(s))return s;if(Zs(s)){let a=await s.blob();e||(e=(n=new URL(s.url).pathname.split(/[\\/]/).pop())!=null?n:"unknown_file");let l=Jt(a)?[await a.arrayBuffer()]:[a];return new gr(l,e,t)}let r=await Ii(s);if(e||(e=(i=Ei(s))!=null?i:"unknown_file"),!(t!=null&&t.type)){let a=(o=r[0])==null?void 0:o.type;typeof a=="string"&&(t={...t,type:a})}return new gr(r,e,t)}async function Ii(s){var t;let e=[];if(typeof s=="string"||ArrayBuffer.isView(s)||s instanceof ArrayBuffer)e.push(s);else if(Jt(s))e.push(await s.arrayBuffer());else if(Ci(s))for await(let r of s)e.push(r);else throw new Error(`Unexpected data type: ${typeof s}; constructor: ${(t=s==null?void 0:s.constructor)==null?void 0:t.name}; props: ${Pi(s)}`);return e}function Pi(s){return`[${Object.getOwnPropertyNames(s).map(t=>`"${t}"`).join(", ")}]`}function Ei(s){var e;return ss(s.name)||ss(s.filename)||((e=ss(s.path))==null?void 0:e.split(/[\\/]/).pop())}var ss=s=>{if(typeof s=="string")return s;if(typeof Buffer!="undefined"&&s instanceof Buffer)return String(s)},Ci=s=>s!=null&&typeof s=="object"&&typeof s[Symbol.asyncIterator]=="function",os=s=>s&&typeof s=="object"&&s.body&&s[Symbol.toStringTag]==="MultipartBody";var ne=async s=>{let e=await tn(s.body);return Zr(e,s)},tn=async s=>{let e=new Qr;return await Promise.all(Object.entries(s||{}).map(([t,r])=>ns(e,t,r))),e};var ns=async(s,e,t)=>{if(t!==void 0){if(t==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof t=="string"||typeof t=="number"||typeof t=="boolean")s.append(e,String(t));else if(xi(t)){let r=await is(t);s.append(e,r)}else if(Array.isArray(t))await Promise.all(t.map(r=>ns(s,e+"[]",r)));else if(typeof t=="object")await Promise.all(Object.entries(t).map(([r,n])=>ns(s,`${e}[${r}]`,n)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${t} instead`)}};var Ri=function(s,e,t,r,n){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?n.call(s,t):n?n.value=t:e.set(s,t),t},Oi=function(s,e,t,r){if(t==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?r:t==="a"?r.call(s):r?r.value:e.get(s)},_r;async function an(s){let{response:e}=s;if(s.options.stream)return gt("response",e.status,e.url,e.headers,e.body),s.options.__streamClass?s.options.__streamClass.fromSSEResponse(e,s.controller):J.fromSSEResponse(e,s.controller);if(e.status===204)return null;if(s.options.__binaryResponse)return e;let t=e.headers.get("content-type");if((t==null?void 0:t.includes("application/json"))||(t==null?void 0:t.includes("application/vnd.api+json"))){let i=await e.json();return gt("response",e.status,e.url,e.headers,i),ln(i,e)}let n=await e.text();return gt("response",e.status,e.url,e.headers,n),n}function ln(s,e){return!s||typeof s!="object"||Array.isArray(s)?s:Object.defineProperty(s,"_request_id",{value:e.headers.get("x-request-id"),enumerable:!1})}var yt=class extends Promise{constructor(e,t=an){super(r=>{r(null)}),this.responsePromise=e,this.parseResponse=t}_thenUnwrap(e){return new yt(this.responsePromise,async t=>ln(e(await this.parseResponse(t),t),t.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}},Ar=class{constructor({baseURL:e,maxRetries:t=2,timeout:r=6e5,httpAgent:n,fetch:i}){this.baseURL=e,this.maxRetries=as("maxRetries",t),this.timeout=as("timeout",r),this.httpAgent=n,this.fetch=i!=null?i:Gr}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...Ni(),...this.authHeaders(e)}}validateHeaders(e,t){}defaultIdempotencyKey(){return`stainless-node-retry-${Li()}`}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(async n=>{let i=n&&Jt(n==null?void 0:n.body)?new DataView(await n.body.arrayBuffer()):(n==null?void 0:n.body)instanceof DataView?n.body:(n==null?void 0:n.body)instanceof ArrayBuffer?new DataView(n.body):n&&ArrayBuffer.isView(n==null?void 0:n.body)?new DataView(n.body.buffer):n==null?void 0:n.body;return{method:e,path:t,...n,body:i}}))}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}calculateContentLength(e){if(typeof e=="string"){if(typeof Buffer!="undefined")return Buffer.byteLength(e,"utf8").toString();if(typeof TextEncoder!="undefined")return new TextEncoder().encode(e).length.toString()}else if(ArrayBuffer.isView(e))return e.byteLength.toString();return null}buildRequest(e,{retryCount:t=0}={}){var x,p,E,b,S,D;let{method:r,path:n,query:i,headers:o={}}=e,a=ArrayBuffer.isView(e.body)||e.__binaryRequest&&typeof e.body=="string"?e.body:os(e.body)?e.body.body:e.body?JSON.stringify(e.body,null,2):null,l=this.calculateContentLength(a),c=this.buildURL(n,i);"timeout"in e&&as("timeout",e.timeout);let d=(x=e.timeout)!=null?x:this.timeout,u=(E=(p=e.httpAgent)!=null?p:this.httpAgent)!=null?E:es(c),h=d+1e3;typeof((b=u==null?void 0:u.options)==null?void 0:b.timeout)=="number"&&h>((S=u.options.timeout)!=null?S:0)&&(u.options.timeout=h),this.idempotencyHeader&&r!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),o[this.idempotencyHeader]=e.idempotencyKey);let f=this.buildHeaders({options:e,headers:o,contentLength:l,retryCount:t});return{req:{method:r,...a&&{body:a},headers:f,...u&&{agent:u},signal:(D=e.signal)!=null?D:null},url:c,timeout:d}}buildHeaders({options:e,headers:t,contentLength:r,retryCount:n}){let i={};r&&(i["content-length"]=r);let o=this.defaultHeaders(e);return nn(i,o),nn(i,t),os(e.body)&&Ke!=="node"&&delete i["content-type"],on(o,"x-stainless-retry-count")===void 0&&on(t,"x-stainless-retry-count")===void 0&&(i["x-stainless-retry-count"]=String(n)),this.validateHeaders(i,t),i}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map(t=>[...t])):{...e}:{}}makeStatusError(e,t,r,n){return L.generate(e,t,r,n)}request(e,t=null){return new yt(this.makeRequest(e,t))}async makeRequest(e,t){var u,h,f;let r=await e,n=(u=r.maxRetries)!=null?u:this.maxRetries;t==null&&(t=n),await this.prepareOptions(r);let{req:i,url:o,timeout:a}=this.buildRequest(r,{retryCount:n-t});if(await this.prepareRequest(i,{url:o,options:r}),gt("request",o,r,i.headers),(h=r.signal)!=null&&h.aborted)throw new H;let l=new AbortController,c=await this.fetchWithTimeout(o,i,a,l).catch(wr);if(c instanceof Error){if((f=r.signal)!=null&&f.aborted)throw new H;if(t)return this.retryRequest(r,t);throw c.name==="AbortError"?new Re:new Se({cause:c})}let d=Ti(c.headers);if(!c.ok){if(t&&this.shouldRetry(c)){let S=`retrying, ${t} attempts remaining`;return gt(`response (error; ${S})`,c.status,o,d),this.retryRequest(r,t,d)}let m=await c.text().catch(S=>wr(S).message),x=Di(m),p=x?void 0:m;throw gt(`response (error; ${t?"(error; no more retries left)":"(error; not retryable)"})`,c.status,o,d,p),this.makeStatusError(c.status,x,p,d)}return{response:c,options:r,controller:l}}requestAPIList(e,t){let r=this.makeRequest(t,null);return new ls(this,r,e)}buildURL(e,t){let r=Bi(e)?new URL(e):new URL(this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return cn(n)||(t={...n,...t}),typeof t=="object"&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}stringifyQuery(e){return Object.entries(e).filter(([t,r])=>typeof r!="undefined").map(([t,r])=>{if(typeof r=="string"||typeof r=="number"||typeof r=="boolean")return`${encodeURIComponent(t)}=${encodeURIComponent(r)}`;if(r===null)return`${encodeURIComponent(t)}=`;throw new v(`Cannot stringify type ${typeof r}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}async fetchWithTimeout(e,t,r,n){let{signal:i,...o}=t||{};i&&i.addEventListener("abort",()=>n.abort());let a=setTimeout(()=>n.abort(),r);return this.getRequestClient().fetch.call(void 0,e,{signal:n.signal,...o}).finally(()=>{clearTimeout(a)})}getRequestClient(){return{fetch:this.fetch}}shouldRetry(e){let t=e.headers.get("x-should-retry");return t==="true"?!0:t==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,t,r){var a;let n,i=r==null?void 0:r["retry-after-ms"];if(i){let l=parseFloat(i);Number.isNaN(l)||(n=l)}let o=r==null?void 0:r["retry-after"];if(o&&!n){let l=parseFloat(o);Number.isNaN(l)?n=Date.parse(o)-Date.now():n=l*1e3}if(!(n&&0<=n&&n<60*1e3)){let l=(a=e.maxRetries)!=null?a:this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,l)}return await ve(n),this.makeRequest(e,t-1)}calculateDefaultRetryTimeoutMillis(e,t){let i=t-e,o=Math.min(.5*Math.pow(2,i),8),a=1-Math.random()*.25;return o*a*1e3}getUserAgent(){return`${this.constructor.name}/JS ${ze}`}},Xt=class{constructor(e,t,r,n){_r.set(this,void 0),Ri(this,_r,e,"f"),this.options=n,this.response=t,this.body=r}hasNextPage(){return this.getPaginatedItems().length?this.nextPageInfo()!=null:!1}async getNextPage(){let e=this.nextPageInfo();if(!e)throw new v("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");let t={...this.options};if("params"in e&&typeof t.query=="object")t.query={...t.query,...e.params};else if("url"in e){let r=[...Object.entries(t.query||{}),...e.url.searchParams.entries()];for(let[n,i]of r)e.url.searchParams.set(n,i);t.query=void 0,t.path=e.url.toString()}return await Oi(this,_r,"f").requestAPIList(this.constructor,t)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(_r=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}},ls=class extends yt{constructor(e,t,r){super(t,async n=>new r(e,n.response,await an(n),n.options))}async*[Symbol.asyncIterator](){let e=await this;for await(let t of e)yield t}},Ti=s=>new Proxy(Object.fromEntries(s.entries()),{get(e,t){let r=t.toString();return e[r.toLowerCase()]||e[r]}}),Mi={method:!0,path:!0,query:!0,body:!0,headers:!0,maxRetries:!0,stream:!0,timeout:!0,httpAgent:!0,signal:!0,idempotencyKey:!0,__binaryRequest:!0,__binaryResponse:!0,__streamClass:!0},k=s=>typeof s=="object"&&s!==null&&!cn(s)&&Object.keys(s).every(e=>dn(Mi,e)),Fi=()=>{var e,t;if(typeof Deno!="undefined"&&Deno.build!=null)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ze,"X-Stainless-OS":sn(Deno.build.os),"X-Stainless-Arch":rn(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:(t=(e=Deno.version)==null?void 0:e.deno)!=null?t:"unknown"};if(typeof EdgeRuntime!="undefined")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ze,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":process.version};if(Object.prototype.toString.call(typeof process!="undefined"?process:0)==="[object process]")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ze,"X-Stainless-OS":sn(process.platform),"X-Stainless-Arch":rn(process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":process.version};let s=ki();return s?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ze,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${s.browser}`,"X-Stainless-Runtime-Version":s.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ze,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function ki(){if(typeof navigator=="undefined"||!navigator)return null;let s=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(let{key:e,pattern:t}of s){let r=t.exec(navigator.userAgent);if(r){let n=r[1]||0,i=r[2]||0,o=r[3]||0;return{browser:e,version:`${n}.${i}.${o}`}}}return null}var rn=s=>s==="x32"?"x32":s==="x86_64"||s==="x64"?"x64":s==="arm"?"arm":s==="aarch64"||s==="arm64"?"arm64":s?`other:${s}`:"unknown",sn=s=>(s=s.toLowerCase(),s.includes("ios")?"iOS":s==="android"?"Android":s==="darwin"?"MacOS":s==="win32"?"Windows":s==="freebsd"?"FreeBSD":s==="openbsd"?"OpenBSD":s==="linux"?"Linux":s?`Other:${s}`:"Unknown"),vr,Ni=()=>vr!=null?vr:vr=Fi(),Di=s=>{try{return JSON.parse(s)}catch(e){return}},$i=new RegExp("^(?:[a-z]+:)?//","i"),Bi=s=>$i.test(s),ve=s=>new Promise(e=>setTimeout(e,s)),as=(s,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new v(`${s} must be an integer`);if(e<0)throw new v(`${s} must be a positive integer`);return e},wr=s=>{if(s instanceof Error)return s;if(typeof s=="object"&&s!==null)try{return new Error(JSON.stringify(s))}catch(e){}return new Error(s)};var Gt=s=>{var e,t,r,n,i,o;if(typeof process!="undefined")return(r=(t=(e=process.env)==null?void 0:e[s])==null?void 0:t.trim())!=null?r:void 0;if(typeof Deno!="undefined")return(o=(i=(n=Deno.env)==null?void 0:n.get)==null?void 0:i.call(n,s))==null?void 0:o.trim()};function cn(s){if(!s)return!0;for(let e in s)return!1;return!0}function dn(s,e){return Object.prototype.hasOwnProperty.call(s,e)}function nn(s,e){for(let t in e){if(!dn(e,t))continue;let r=t.toLowerCase();if(!r)continue;let n=e[t];n===null?delete s[r]:n!==void 0&&(s[r]=n)}}function gt(s,...e){var t;typeof process!="undefined"&&((t=process==null?void 0:process.env)==null?void 0:t.DEBUG)==="true"&&console.log(`OpenAI:DEBUG:${s}`,...e)}var Li=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,s=>{let e=Math.random()*16|0;return(s==="x"?e:e&3|8).toString(16)}),un=()=>typeof window!="undefined"&&typeof window.document!="undefined"&&typeof navigator!="undefined",Ui=s=>typeof(s==null?void 0:s.get)=="function";var on=(s,e)=>{var r;let t=e.toLowerCase();if(Ui(s)){let n=((r=e[0])==null?void 0:r.toUpperCase())+e.substring(1).replace(/([^\w])(\w)/g,(i,o,a)=>o+a.toUpperCase());for(let i of[e,t,e.toUpperCase(),n]){let o=s.get(i);if(o)return o}}for(let[n,i]of Object.entries(s))if(n.toLowerCase()===t)return Array.isArray(i)?(i.length<=1||console.warn(`Received ${i.length} entries for the ${e} header, using the first entry.`),i[0]):i};function Qt(s){return s!=null&&typeof s=="object"&&!Array.isArray(s)}var xr=class extends Xt{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.object=r.object}getPaginatedItems(){var e;return(e=this.data)!=null?e:[]}nextPageParams(){return null}nextPageInfo(){return null}},$=class extends Xt{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[]}getPaginatedItems(){var e;return(e=this.data)!=null?e:[]}nextPageParams(){let e=this.nextPageInfo();if(!e)return null;if("params"in e)return e.params;let t=Object.fromEntries(e.url.searchParams);return Object.keys(t).length?t:null}nextPageInfo(){var r;let e=this.getPaginatedItems();if(!e.length)return null;let t=(r=e[e.length-1])==null?void 0:r.id;return t?{params:{after:t}}:null}};var g=class{constructor(e){this._client=e}};var bt=class extends g{create(e,t){var r;return this._client.post("/chat/completions",{body:e,...t,stream:(r=e.stream)!=null?r:!1})}};var Oe=class extends g{constructor(){super(...arguments),this.completions=new bt(this._client)}};Oe.Completions=bt;var wt=class extends g{create(e,t){return this._client.post("/audio/speech",{body:e,...t,__binaryResponse:!0})}};var _t=class extends g{create(e,t){return this._client.post("/audio/transcriptions",ne({body:e,...t}))}};var vt=class extends g{create(e,t){return this._client.post("/audio/translations",ne({body:e,...t}))}};var fe=class extends g{constructor(){super(...arguments),this.transcriptions=new _t(this._client),this.translations=new vt(this._client),this.speech=new wt(this._client)}};fe.Transcriptions=_t;fe.Translations=vt;fe.Speech=wt;var Te=class extends g{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(`/batches/${e}`,t)}list(e={},t){return k(e)?this.list({},e):this._client.getAPIList("/batches",Je,{query:e,...t})}cancel(e,t){return this._client.post(`/batches/${e}/cancel`,t)}},Je=class extends ${};Te.BatchesPage=Je;var Xe=class extends g{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}update(e,t,r){return this._client.post(`/assistants/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}list(e={},t){return k(e)?this.list({},e):this._client.getAPIList("/assistants",At,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}del(e,t){return this._client.delete(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}},At=class extends ${};Xe.AssistantsPage=At;function cs(s){return typeof s.parse=="function"}var Me=s=>(s==null?void 0:s.role)==="assistant",ds=s=>(s==null?void 0:s.role)==="function",us=s=>(s==null?void 0:s.role)==="tool";var oe=function(s,e,t,r,n){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?n.call(s,t):n?n.value=t:e.set(s,t),t},B=function(s,e,t,r){if(t==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?r:t==="a"?r.call(s):r?r.value:e.get(s)},hs,Ir,Pr,Yt,Zt,Er,er,Ae,tr,Cr,Sr,xt,hn,It=class{constructor(){hs.add(this),this.controller=new AbortController,Ir.set(this,void 0),Pr.set(this,()=>{}),Yt.set(this,()=>{}),Zt.set(this,void 0),Er.set(this,()=>{}),er.set(this,()=>{}),Ae.set(this,{}),tr.set(this,!1),Cr.set(this,!1),Sr.set(this,!1),xt.set(this,!1),oe(this,Ir,new Promise((e,t)=>{oe(this,Pr,e,"f"),oe(this,Yt,t,"f")}),"f"),oe(this,Zt,new Promise((e,t)=>{oe(this,Er,e,"f"),oe(this,er,t,"f")}),"f"),B(this,Ir,"f").catch(()=>{}),B(this,Zt,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},B(this,hs,"m",hn).bind(this))},0)}_connected(){this.ended||(B(this,Pr,"f").call(this),this._emit("connect"))}get ended(){return B(this,tr,"f")}get errored(){return B(this,Cr,"f")}get aborted(){return B(this,Sr,"f")}abort(){this.controller.abort()}on(e,t){return(B(this,Ae,"f")[e]||(B(this,Ae,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=B(this,Ae,"f")[e];if(!r)return this;let n=r.findIndex(i=>i.listener===t);return n>=0&&r.splice(n,1),this}once(e,t){return(B(this,Ae,"f")[e]||(B(this,Ae,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{oe(this,xt,!0,"f"),e!=="error"&&this.once("error",r),this.once(e,t)})}async done(){oe(this,xt,!0,"f"),await B(this,Zt,"f")}_emit(e,...t){if(B(this,tr,"f"))return;e==="end"&&(oe(this,tr,!0,"f"),B(this,Er,"f").call(this));let r=B(this,Ae,"f")[e];if(r&&(B(this,Ae,"f")[e]=r.filter(n=>!n.once),r.forEach(({listener:n})=>n(...t))),e==="abort"){let n=t[0];!B(this,xt,"f")&&!(r!=null&&r.length)&&Promise.reject(n),B(this,Yt,"f").call(this,n),B(this,er,"f").call(this,n),this._emit("end");return}if(e==="error"){let n=t[0];!B(this,xt,"f")&&!(r!=null&&r.length)&&Promise.reject(n),B(this,Yt,"f").call(this,n),B(this,er,"f").call(this,n),this._emit("end")}}_emitFinal(){}};Ir=new WeakMap,Pr=new WeakMap,Yt=new WeakMap,Zt=new WeakMap,Er=new WeakMap,er=new WeakMap,Ae=new WeakMap,tr=new WeakMap,Cr=new WeakMap,Sr=new WeakMap,xt=new WeakMap,hs=new WeakSet,hn=function(e){if(oe(this,Cr,!0,"f"),e instanceof Error&&e.name==="AbortError"&&(e=new H),e instanceof H)return oe(this,Sr,!0,"f"),this._emit("abort",e);if(e instanceof v)return this._emit("error",e);if(e instanceof Error){let t=new v(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new v(String(e)))};function fs(s){return(s==null?void 0:s.$brand)==="auto-parseable-response-format"}function Ge(s){return(s==null?void 0:s.$brand)==="auto-parseable-tool"}function fn(s,e){return!e||!ms(e)?{...s,choices:s.choices.map(t=>{var r;return{...t,message:{...t.message,parsed:null,tool_calls:(r=t.message.tool_calls)!=null?r:[]}}})}:rr(s,e)}function rr(s,e){let t=s.choices.map(r=>{var n,i;if(r.finish_reason==="length")throw new mt;if(r.finish_reason==="content_filter")throw new pt;return{...r,message:{...r.message,tool_calls:(i=(n=r.message.tool_calls)==null?void 0:n.map(o=>Vi(e,o)))!=null?i:[],parsed:r.message.content&&!r.message.refusal?Ki(e,r.message.content):null}}});return{...s,choices:t}}function Ki(s,e){var t,r;return((t=s.response_format)==null?void 0:t.type)!=="json_schema"?null:((r=s.response_format)==null?void 0:r.type)==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(e):JSON.parse(e):null}function Vi(s,e){var r;let t=(r=s.tools)==null?void 0:r.find(n=>{var i;return((i=n.function)==null?void 0:i.name)===e.function.name});return{...e,function:{...e.function,parsed_arguments:Ge(t)?t.$parseRaw(e.function.arguments):t!=null&&t.function.strict?JSON.parse(e.function.arguments):null}}}function mn(s,e){var r;if(!s)return!1;let t=(r=s.tools)==null?void 0:r.find(n=>{var i;return((i=n.function)==null?void 0:i.name)===e.function.name});return Ge(t)||(t==null?void 0:t.function.strict)||!1}function ms(s){var e,t;return fs(s.response_format)?!0:(t=(e=s.tools)==null?void 0:e.some(r=>Ge(r)||r.type==="function"&&r.function.strict===!0))!=null?t:!1}function pn(s){for(let e of s!=null?s:[]){if(e.type!=="function")throw new v(`Currently only \`function\` tool types support auto-parsing; Received \`${e.type}\``);if(e.function.strict!==!0)throw new v(`The \`${e.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var Y=function(s,e,t,r){if(t==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?r:t==="a"?r.call(s):r?r.value:e.get(s)},V,ps,Rr,gs,ys,bs,yn,ws,gn=10,Pt=class extends It{constructor(){super(...arguments),V.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var r;this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=(r=e.choices[0])==null?void 0:r.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),(ds(e)||us(e))&&e.content)this._emit("functionCallResult",e.content);else if(Me(e)&&e.function_call)this._emit("functionCall",e.function_call);else if(Me(e)&&e.tool_calls)for(let r of e.tool_calls)r.type==="function"&&this._emit("functionCall",r.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new v("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),Y(this,V,"m",ps).call(this)}async finalMessage(){return await this.done(),Y(this,V,"m",Rr).call(this)}async finalFunctionCall(){return await this.done(),Y(this,V,"m",gs).call(this)}async finalFunctionCallResult(){return await this.done(),Y(this,V,"m",ys).call(this)}async totalUsage(){return await this.done(),Y(this,V,"m",bs).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=Y(this,V,"m",Rr).call(this);t&&this._emit("finalMessage",t);let r=Y(this,V,"m",ps).call(this);r&&this._emit("finalContent",r);let n=Y(this,V,"m",gs).call(this);n&&this._emit("finalFunctionCall",n);let i=Y(this,V,"m",ys).call(this);i!=null&&this._emit("finalFunctionCallResult",i),this._chatCompletions.some(o=>o.usage)&&this._emit("totalUsage",Y(this,V,"m",bs).call(this))}async _createChatCompletion(e,t,r){let n=r==null?void 0:r.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),Y(this,V,"m",yn).call(this,t);let i=await e.chat.completions.create({...t,stream:!1},{...r,signal:this.controller.signal});return this._connected(),this._addChatCompletion(rr(i,t))}async _runChatCompletion(e,t,r){for(let n of t.messages)this._addMessage(n,!1);return await this._createChatCompletion(e,t,r)}async _runFunctions(e,t,r){var h;let n="function",{function_call:i="auto",stream:o,...a}=t,l=typeof i!="string"&&(i==null?void 0:i.name),{maxChatCompletions:c=gn}=r||{},d={};for(let f of t.functions)d[f.name||f.function.name]=f;let u=t.functions.map(f=>({name:f.name||f.function.name,parameters:f.parameters,description:f.description}));for(let f of t.messages)this._addMessage(f,!1);for(let f=0;f<c;++f){let x=(h=(await this._createChatCompletion(e,{...a,function_call:i,functions:u,messages:[...this.messages]},r)).choices[0])==null?void 0:h.message;if(!x)throw new v("missing message in ChatCompletion response");if(!x.function_call)return;let{name:p,arguments:E}=x.function_call,b=d[p];if(b){if(l&&l!==p){let N=`Invalid function_call: ${JSON.stringify(p)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:n,name:p,content:N});continue}}else{let N=`Invalid function_call: ${JSON.stringify(p)}. Available options are: ${u.map(M=>JSON.stringify(M.name)).join(", ")}. Please try again`;this._addMessage({role:n,name:p,content:N});continue}let S;try{S=cs(b)?await b.parse(E):E}catch(N){this._addMessage({role:n,name:p,content:N instanceof Error?N.message:String(N)});continue}let D=await b.function(S,this),A=Y(this,V,"m",ws).call(this,D);if(this._addMessage({role:n,name:p,content:A}),l)return}}async _runTools(e,t,r){var f,m,x;let n="tool",{tool_choice:i="auto",stream:o,...a}=t,l=typeof i!="string"&&((f=i==null?void 0:i.function)==null?void 0:f.name),{maxChatCompletions:c=gn}=r||{},d=t.tools.map(p=>{if(Ge(p)){if(!p.$callback)throw new v("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:p.$callback,name:p.function.name,description:p.function.description||"",parameters:p.function.parameters,parse:p.$parseRaw,strict:!0}}}return p}),u={};for(let p of d)p.type==="function"&&(u[p.function.name||p.function.function.name]=p.function);let h="tools"in t?d.map(p=>p.type==="function"?{type:"function",function:{name:p.function.name||p.function.function.name,parameters:p.function.parameters,description:p.function.description,strict:p.function.strict}}:p):void 0;for(let p of t.messages)this._addMessage(p,!1);for(let p=0;p<c;++p){let b=(m=(await this._createChatCompletion(e,{...a,tool_choice:i,tools:h,messages:[...this.messages]},r)).choices[0])==null?void 0:m.message;if(!b)throw new v("missing message in ChatCompletion response");if(!((x=b.tool_calls)!=null&&x.length))return;for(let S of b.tool_calls){if(S.type!=="function")continue;let D=S.id,{name:A,arguments:N}=S.function,M=u[A];if(M){if(l&&l!==A){let F=`Invalid tool_call: ${JSON.stringify(A)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:n,tool_call_id:D,content:F});continue}}else{let F=`Invalid tool_call: ${JSON.stringify(A)}. Available options are: ${Object.keys(u).map(R=>JSON.stringify(R)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:D,content:F});continue}let T;try{T=cs(M)?await M.parse(N):N}catch(F){let R=F instanceof Error?F.message:String(F);this._addMessage({role:n,tool_call_id:D,content:R});continue}let P=await M.function(T,this),C=Y(this,V,"m",ws).call(this,P);if(this._addMessage({role:n,tool_call_id:D,content:C}),l)return}}}};V=new WeakSet,ps=function(){var e;return(e=Y(this,V,"m",Rr).call(this).content)!=null?e:null},Rr=function(){var t,r;let e=this.messages.length;for(;e-- >0;){let n=this.messages[e];if(Me(n)){let{function_call:i,...o}=n,a={...o,content:(t=n.content)!=null?t:null,refusal:(r=n.refusal)!=null?r:null};return i&&(a.function_call=i),a}}throw new v("stream ended without producing a ChatCompletionMessage with role=assistant")},gs=function(){var e,t;for(let r=this.messages.length-1;r>=0;r--){let n=this.messages[r];if(Me(n)&&(n!=null&&n.function_call))return n.function_call;if(Me(n)&&((e=n==null?void 0:n.tool_calls)!=null&&e.length))return(t=n.tool_calls.at(-1))==null?void 0:t.function}},ys=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(ds(t)&&t.content!=null||us(t)&&t.content!=null&&typeof t.content=="string"&&this.messages.some(r=>{var n;return r.role==="assistant"&&((n=r.tool_calls)==null?void 0:n.some(i=>i.type==="function"&&i.id===t.tool_call_id))}))return t.content}},bs=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},yn=function(e){if(e.n!=null&&e.n>1)throw new v("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},ws=function(e){return typeof e=="string"?e:e===void 0?"undefined":JSON.stringify(e)};var Fe=class extends Pt{static runFunctions(e,t,r){let n=new Fe,i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,i)),n}static runTools(e,t,r){let n=new Fe,i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,i)),n}_addMessage(e,t=!0){super._addMessage(e,t),Me(e)&&e.content&&this._emit("content",e.content)}};var W={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,SPECIAL:496,ATOM:499,COLLECTION:12,ALL:511},_s=class extends Error{},vs=class extends Error{};function Ji(s,e=W.ALL){if(typeof s!="string")throw new TypeError(`expecting str, got ${typeof s}`);if(!s.trim())throw new Error(`${s} is empty`);return Xi(s.trim(),e)}var Xi=(s,e)=>{let t=s.length,r=0,n=h=>{throw new _s(`${h} at position ${r}`)},i=h=>{throw new vs(`${h} at position ${r}`)},o=()=>(u(),r>=t&&n("Unexpected end of input"),s[r]==='"'?a():s[r]==="{"?l():s[r]==="["?c():s.substring(r,r+4)==="null"||W.NULL&e&&t-r<4&&"null".startsWith(s.substring(r))?(r+=4,null):s.substring(r,r+4)==="true"||W.BOOL&e&&t-r<4&&"true".startsWith(s.substring(r))?(r+=4,!0):s.substring(r,r+5)==="false"||W.BOOL&e&&t-r<5&&"false".startsWith(s.substring(r))?(r+=5,!1):s.substring(r,r+8)==="Infinity"||W.INFINITY&e&&t-r<8&&"Infinity".startsWith(s.substring(r))?(r+=8,1/0):s.substring(r,r+9)==="-Infinity"||W.MINUS_INFINITY&e&&1<t-r&&t-r<9&&"-Infinity".startsWith(s.substring(r))?(r+=9,-1/0):s.substring(r,r+3)==="NaN"||W.NAN&e&&t-r<3&&"NaN".startsWith(s.substring(r))?(r+=3,NaN):d()),a=()=>{let h=r,f=!1;for(r++;r<t&&(s[r]!=='"'||f&&s[r-1]==="\\");)f=s[r]==="\\"?!f:!1,r++;if(s.charAt(r)=='"')try{return JSON.parse(s.substring(h,++r-Number(f)))}catch(m){i(String(m))}else if(W.STR&e)try{return JSON.parse(s.substring(h,r-Number(f))+'"')}catch(m){return JSON.parse(s.substring(h,s.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},l=()=>{r++,u();let h={};try{for(;s[r]!=="}";){if(u(),r>=t&&W.OBJ&e)return h;let f=a();u(),r++;try{let m=o();Object.defineProperty(h,f,{value:m,writable:!0,enumerable:!0,configurable:!0})}catch(m){if(W.OBJ&e)return h;throw m}u(),s[r]===","&&r++}}catch(f){if(W.OBJ&e)return h;n("Expected '}' at end of object")}return r++,h},c=()=>{r++;let h=[];try{for(;s[r]!=="]";)h.push(o()),u(),s[r]===","&&r++}catch(f){if(W.ARR&e)return h;n("Expected ']' at end of array")}return r++,h},d=()=>{if(r===0){s==="-"&&W.NUM&e&&n("Not sure what '-' is");try{return JSON.parse(s)}catch(f){if(W.NUM&e)try{return s[s.length-1]==="."?JSON.parse(s.substring(0,s.lastIndexOf("."))):JSON.parse(s.substring(0,s.lastIndexOf("e")))}catch(m){}i(String(f))}}let h=r;for(s[r]==="-"&&r++;s[r]&&!",]}".includes(s[r]);)r++;r==t&&!(W.NUM&e)&&n("Unterminated number literal");try{return JSON.parse(s.substring(h,r))}catch(f){s.substring(h,r)==="-"&&W.NUM&e&&n("Not sure what '-' is");try{return JSON.parse(s.substring(h,s.lastIndexOf("e")))}catch(m){i(String(m))}}},u=()=>{for(;r<t&&` 
\r	`.includes(s[r]);)r++};return o()},As=s=>Ji(s,W.ALL^W.NUM);var Et=function(s,e,t,r,n){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?n.call(s,t):n?n.value=t:e.set(s,t),t},O=function(s,e,t,r){if(t==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?r:t==="a"?r.call(s):r?r.value:e.get(s)},j,xe,Ct,ke,xs,Or,Is,Ps,Es,Tr,Cs,bn,Ie=class extends Pt{constructor(e){super(),j.add(this),xe.set(this,void 0),Ct.set(this,void 0),ke.set(this,void 0),Et(this,xe,e,"f"),Et(this,Ct,[],"f")}get currentChatCompletionSnapshot(){return O(this,ke,"f")}static fromReadableStream(e){let t=new Ie(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,r){let n=new Ie(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,r){var o;super._createChatCompletion;let n=r==null?void 0:r.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),O(this,j,"m",xs).call(this);let i=await e.chat.completions.create({...t,stream:!0},{...r,signal:this.controller.signal});this._connected();for await(let a of i)O(this,j,"m",Is).call(this,a);if((o=i.controller.signal)!=null&&o.aborted)throw new H;return this._addChatCompletion(O(this,j,"m",Tr).call(this))}async _fromReadableStream(e,t){var o;let r=t==null?void 0:t.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),O(this,j,"m",xs).call(this),this._connected();let n=J.fromReadableStream(e,this.controller),i;for await(let a of n)i&&i!==a.id&&this._addChatCompletion(O(this,j,"m",Tr).call(this)),O(this,j,"m",Is).call(this,a),i=a.id;if((o=n.controller.signal)!=null&&o.aborted)throw new H;return this._addChatCompletion(O(this,j,"m",Tr).call(this))}[(xe=new WeakMap,Ct=new WeakMap,ke=new WeakMap,j=new WeakSet,xs=function(){this.ended||Et(this,ke,void 0,"f")},Or=function(t){let r=O(this,Ct,"f")[t.index];return r||(r={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},O(this,Ct,"f")[t.index]=r,r)},Is=function(t){var n,i,o,a,l,c,d,u,h,f,m,x,p,E,b,S,D,A,N,M;if(this.ended)return;let r=O(this,j,"m",bn).call(this,t);this._emit("chunk",t,r);for(let T of t.choices){let P=r.choices[T.index];T.delta.content!=null&&((n=P.message)==null?void 0:n.role)==="assistant"&&((i=P.message)!=null&&i.content)&&(this._emit("content",T.delta.content,P.message.content),this._emit("content.delta",{delta:T.delta.content,snapshot:P.message.content,parsed:P.message.parsed})),T.delta.refusal!=null&&((o=P.message)==null?void 0:o.role)==="assistant"&&((a=P.message)!=null&&a.refusal)&&this._emit("refusal.delta",{delta:T.delta.refusal,snapshot:P.message.refusal}),((l=T.logprobs)==null?void 0:l.content)!=null&&((c=P.message)==null?void 0:c.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(d=T.logprobs)==null?void 0:d.content,snapshot:(h=(u=P.logprobs)==null?void 0:u.content)!=null?h:[]}),((f=T.logprobs)==null?void 0:f.refusal)!=null&&((m=P.message)==null?void 0:m.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(x=T.logprobs)==null?void 0:x.refusal,snapshot:(E=(p=P.logprobs)==null?void 0:p.refusal)!=null?E:[]});let C=O(this,j,"m",Or).call(this,P);P.finish_reason&&(O(this,j,"m",Es).call(this,P),C.current_tool_call_index!=null&&O(this,j,"m",Ps).call(this,P,C.current_tool_call_index));for(let F of(b=T.delta.tool_calls)!=null?b:[])C.current_tool_call_index!==F.index&&(O(this,j,"m",Es).call(this,P),C.current_tool_call_index!=null&&O(this,j,"m",Ps).call(this,P,C.current_tool_call_index)),C.current_tool_call_index=F.index;for(let F of(S=T.delta.tool_calls)!=null?S:[]){let R=(D=P.message.tool_calls)==null?void 0:D[F.index];R!=null&&R.type&&((R==null?void 0:R.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(A=R.function)==null?void 0:A.name,index:F.index,arguments:R.function.arguments,parsed_arguments:R.function.parsed_arguments,arguments_delta:(M=(N=F.function)==null?void 0:N.arguments)!=null?M:""}):(R==null||R.type,void 0))}}},Ps=function(t,r){var o,a,l;if(O(this,j,"m",Or).call(this,t).done_tool_calls.has(r))return;let i=(o=t.message.tool_calls)==null?void 0:o[r];if(!i)throw new Error("no tool call snapshot");if(!i.type)throw new Error("tool call snapshot missing `type`");if(i.type==="function"){let c=(l=(a=O(this,xe,"f"))==null?void 0:a.tools)==null?void 0:l.find(d=>d.type==="function"&&d.function.name===i.function.name);this._emit("tool_calls.function.arguments.done",{name:i.function.name,index:r,arguments:i.function.arguments,parsed_arguments:Ge(c)?c.$parseRaw(i.function.arguments):c!=null&&c.function.strict?JSON.parse(i.function.arguments):null})}else i.type},Es=function(t){var n,i;let r=O(this,j,"m",Or).call(this,t);if(t.message.content&&!r.content_done){r.content_done=!0;let o=O(this,j,"m",Cs).call(this);this._emit("content.done",{content:t.message.content,parsed:o?o.$parseRaw(t.message.content):null})}t.message.refusal&&!r.refusal_done&&(r.refusal_done=!0,this._emit("refusal.done",{refusal:t.message.refusal})),(n=t.logprobs)!=null&&n.content&&!r.logprobs_content_done&&(r.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:t.logprobs.content})),(i=t.logprobs)!=null&&i.refusal&&!r.logprobs_refusal_done&&(r.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:t.logprobs.refusal}))},Tr=function(){if(this.ended)throw new v("stream has ended, this shouldn't happen");let t=O(this,ke,"f");if(!t)throw new v("request ended without sending any chunks");return Et(this,ke,void 0,"f"),Et(this,Ct,[],"f"),Gi(t,O(this,xe,"f"))},Cs=function(){var r;let t=(r=O(this,xe,"f"))==null?void 0:r.response_format;return fs(t)?t:null},bn=function(t){var d,u,h,f,m,x;var r,n,i,o;let a=O(this,ke,"f"),{choices:l,...c}=t;a?Object.assign(a,c):a=Et(this,ke,{...c,choices:[]},"f");for(let{delta:p,finish_reason:E,index:b,logprobs:S=null,...D}of t.choices){let A=a.choices[b];if(A||(A=a.choices[b]={finish_reason:E,index:b,message:{},logprobs:S,...D}),S)if(!A.logprobs)A.logprobs=Object.assign({},S);else{let{content:R,refusal:re,..._e}=S;Object.assign(A.logprobs,_e),R&&((d=(r=A.logprobs).content)!=null||(r.content=[]),A.logprobs.content.push(...R)),re&&((u=(n=A.logprobs).refusal)!=null||(n.refusal=[]),A.logprobs.refusal.push(...re))}if(E&&(A.finish_reason=E,O(this,xe,"f")&&ms(O(this,xe,"f")))){if(E==="length")throw new mt;if(E==="content_filter")throw new pt}if(Object.assign(A,D),!p)continue;let{content:N,refusal:M,function_call:T,role:P,tool_calls:C,...F}=p;if(Object.assign(A.message,F),M&&(A.message.refusal=(A.message.refusal||"")+M),P&&(A.message.role=P),T&&(A.message.function_call?(T.name&&(A.message.function_call.name=T.name),T.arguments&&((h=(i=A.message.function_call).arguments)!=null||(i.arguments=""),A.message.function_call.arguments+=T.arguments)):A.message.function_call=T),N&&(A.message.content=(A.message.content||"")+N,!A.message.refusal&&O(this,j,"m",Cs).call(this)&&(A.message.parsed=As(A.message.content))),C){A.message.tool_calls||(A.message.tool_calls=[]);for(let{index:R,id:re,type:_e,function:Z,...Vn}of C){let le=(f=(o=A.message.tool_calls)[R])!=null?f:o[R]={};Object.assign(le,Vn),re&&(le.id=re),_e&&(le.type=_e),Z&&((x=le.function)!=null||(le.function={name:(m=Z.name)!=null?m:"",arguments:""})),Z!=null&&Z.name&&(le.function.name=Z.name),Z!=null&&Z.arguments&&(le.function.arguments+=Z.arguments,mn(O(this,xe,"f"),le)&&(le.function.parsed_arguments=As(le.function.arguments)))}}}return a},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("chunk",n=>{let i=t.shift();i?i.resolve(n):e.push(n)}),this.on("end",()=>{r=!0;for(let n of t)n.resolve(void 0);t.length=0}),this.on("abort",n=>{r=!0;for(let i of t)i.reject(n);t.length=0}),this.on("error",n=>{r=!0;for(let i of t)i.reject(n);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((i,o)=>t.push({resolve:i,reject:o})).then(i=>i?{value:i,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new J(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}};function Gi(s,e){let{id:t,choices:r,created:n,model:i,system_fingerprint:o,...a}=s,l={...a,id:t,choices:r.map(({message:c,finish_reason:d,index:u,logprobs:h,...f})=>{var S,D,A;if(!d)throw new v(`missing finish_reason for choice ${u}`);let{content:m=null,function_call:x,tool_calls:p,...E}=c,b=c.role;if(!b)throw new v(`missing role for choice ${u}`);if(x){let{arguments:N,name:M}=x;if(N==null)throw new v(`missing function_call.arguments for choice ${u}`);if(!M)throw new v(`missing function_call.name for choice ${u}`);return{...f,message:{content:m,function_call:{arguments:N,name:M},role:b,refusal:(S=c.refusal)!=null?S:null},finish_reason:d,index:u,logprobs:h}}return p?{...f,index:u,finish_reason:d,logprobs:h,message:{...E,role:b,content:m,refusal:(D=c.refusal)!=null?D:null,tool_calls:p.map((N,M)=>{let{function:T,type:P,id:C,...F}=N,{arguments:R,name:re,..._e}=T||{};if(C==null)throw new v(`missing choices[${u}].tool_calls[${M}].id
${Mr(s)}`);if(P==null)throw new v(`missing choices[${u}].tool_calls[${M}].type
${Mr(s)}`);if(re==null)throw new v(`missing choices[${u}].tool_calls[${M}].function.name
${Mr(s)}`);if(R==null)throw new v(`missing choices[${u}].tool_calls[${M}].function.arguments
${Mr(s)}`);return{...F,id:C,type:P,function:{..._e,name:re,arguments:R}}})}}:{...f,message:{...E,content:m,role:b,refusal:(A=c.refusal)!=null?A:null},finish_reason:d,index:u,logprobs:h}}),created:n,model:i,object:"chat.completion",...o?{system_fingerprint:o}:{}};return fn(l,e)}function Mr(s){return JSON.stringify(s)}var Pe=class extends Ie{static fromReadableStream(e){let t=new Pe(null);return t._run(()=>t._fromReadableStream(e)),t}static runFunctions(e,t,r){let n=new Pe(null),i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run(()=>n._runFunctions(e,t,i)),n}static runTools(e,t,r){let n=new Pe(t),i={...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,i)),n}};var sr=class extends g{parse(e,t){return pn(e.tools),this._client.chat.completions.create(e,{...t,headers:{...t==null?void 0:t.headers,"X-Stainless-Helper-Method":"beta.chat.completions.parse"}})._thenUnwrap(r=>rr(r,e))}runFunctions(e,t){return e.stream?Pe.runFunctions(this._client,e,t):Fe.runFunctions(this._client,e,t)}runTools(e,t){return e.stream?Pe.runTools(this._client,e,t):Fe.runTools(this._client,e,t)}stream(e,t){return Ie.createChatCompletion(this._client,e,t)}};var St=class extends g{constructor(){super(...arguments),this.completions=new sr(this._client)}};(function(s){s.Completions=sr})(St||(St={}));var y=function(s,e,t,r){if(t==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?r:t==="a"?r.call(s):r?r.value:e.get(s)},ee=function(s,e,t,r,n){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?n.call(s,t):n?n.value=t:e.set(s,t),t},z,Ss,me,Fr,ae,Ye,Rt,Qe,Dr,te,kr,Nr,or,nr,ir,wn,_n,vn,An,xn,In,Pn,X=class extends It{constructor(){super(...arguments),z.add(this),Ss.set(this,[]),me.set(this,{}),Fr.set(this,{}),ae.set(this,void 0),Ye.set(this,void 0),Rt.set(this,void 0),Qe.set(this,void 0),Dr.set(this,void 0),te.set(this,void 0),kr.set(this,void 0),Nr.set(this,void 0),or.set(this,void 0)}[(Ss=new WeakMap,me=new WeakMap,Fr=new WeakMap,ae=new WeakMap,Ye=new WeakMap,Rt=new WeakMap,Qe=new WeakMap,Dr=new WeakMap,te=new WeakMap,kr=new WeakMap,Nr=new WeakMap,or=new WeakMap,z=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",n=>{let i=t.shift();i?i.resolve(n):e.push(n)}),this.on("end",()=>{r=!0;for(let n of t)n.resolve(void 0);t.length=0}),this.on("abort",n=>{r=!0;for(let i of t)i.reject(n);t.length=0}),this.on("error",n=>{r=!0;for(let i of t)i.reject(n);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((i,o)=>t.push({resolve:i,reject:o})).then(i=>i?{value:i,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new X;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){var i;let r=t==null?void 0:t.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=J.fromReadableStream(e,this.controller);for await(let o of n)y(this,z,"m",nr).call(this,o);if((i=n.controller.signal)!=null&&i.aborted)throw new H;return this._addRun(y(this,z,"m",ir).call(this))}toReadableStream(){return new J(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,r,n,i){let o=new X;return o._run(()=>o._runToolAssistantStream(e,t,r,n,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),o}async _createToolAssistantStream(e,t,r,n,i){var c;let o=i==null?void 0:i.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort()));let a={...n,stream:!0},l=await e.submitToolOutputs(t,r,a,{...i,signal:this.controller.signal});this._connected();for await(let d of l)y(this,z,"m",nr).call(this,d);if((c=l.controller.signal)!=null&&c.aborted)throw new H;return this._addRun(y(this,z,"m",ir).call(this))}static createThreadAssistantStream(e,t,r){let n=new X;return n._run(()=>n._threadAssistantStream(e,t,{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,r,n){let i=new X;return i._run(()=>i._runAssistantStream(e,t,r,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),i}currentEvent(){return y(this,kr,"f")}currentRun(){return y(this,Nr,"f")}currentMessageSnapshot(){return y(this,ae,"f")}currentRunStepSnapshot(){return y(this,or,"f")}async finalRunSteps(){return await this.done(),Object.values(y(this,me,"f"))}async finalMessages(){return await this.done(),Object.values(y(this,Fr,"f"))}async finalRun(){if(await this.done(),!y(this,Ye,"f"))throw Error("Final run was not received.");return y(this,Ye,"f")}async _createThreadAssistantStream(e,t,r){var a;let n=r==null?void 0:r.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let i={...t,stream:!0},o=await e.createAndRun(i,{...r,signal:this.controller.signal});this._connected();for await(let l of o)y(this,z,"m",nr).call(this,l);if((a=o.controller.signal)!=null&&a.aborted)throw new H;return this._addRun(y(this,z,"m",ir).call(this))}async _createAssistantStream(e,t,r,n){var l;let i=n==null?void 0:n.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort()));let o={...r,stream:!0},a=await e.create(t,o,{...n,signal:this.controller.signal});this._connected();for await(let c of a)y(this,z,"m",nr).call(this,c);if((l=a.controller.signal)!=null&&l.aborted)throw new H;return this._addRun(y(this,z,"m",ir).call(this))}static accumulateDelta(e,t){for(let[r,n]of Object.entries(t)){if(!e.hasOwnProperty(r)){e[r]=n;continue}let i=e[r];if(i==null){e[r]=n;continue}if(r==="index"||r==="type"){e[r]=n;continue}if(typeof i=="string"&&typeof n=="string")i+=n;else if(typeof i=="number"&&typeof n=="number")i+=n;else if(Qt(i)&&Qt(n))i=this.accumulateDelta(i,n);else if(Array.isArray(i)&&Array.isArray(n)){if(i.every(o=>typeof o=="string"||typeof o=="number")){i.push(...n);continue}for(let o of n){if(!Qt(o))throw new Error(`Expected array delta entry to be an object but got: ${o}`);let a=o.index;if(a==null)throw console.error(o),new Error("Expected array delta entry to have an `index` property");if(typeof a!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${a}`);let l=i[a];l==null?i.push(o):i[a]=this.accumulateDelta(l,o)}continue}else throw Error(`Unhandled record type: ${r}, deltaValue: ${n}, accValue: ${i}`);e[r]=i}return e}_addRun(e){return e}async _threadAssistantStream(e,t,r){return await this._createThreadAssistantStream(t,e,r)}async _runAssistantStream(e,t,r,n){return await this._createAssistantStream(t,e,r,n)}async _runToolAssistantStream(e,t,r,n,i){return await this._createToolAssistantStream(r,e,t,n,i)}};nr=function(e){if(!this.ended)switch(ee(this,kr,e,"f"),y(this,z,"m",vn).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":y(this,z,"m",Pn).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":y(this,z,"m",_n).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":y(this,z,"m",wn).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},ir=function(){if(this.ended)throw new v("stream has ended, this shouldn't happen");if(!y(this,Ye,"f"))throw Error("Final run has not been received");return y(this,Ye,"f")},wn=function(e){let[t,r]=y(this,z,"m",xn).call(this,e,y(this,ae,"f"));ee(this,ae,t,"f"),y(this,Fr,"f")[t.id]=t;for(let n of r){let i=t.content[n.index];(i==null?void 0:i.type)=="text"&&this._emit("textCreated",i.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let n of e.data.delta.content){if(n.type=="text"&&n.text){let i=n.text,o=t.content[n.index];if(o&&o.type=="text")this._emit("textDelta",i,o.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(n.index!=y(this,Rt,"f")){if(y(this,Qe,"f"))switch(y(this,Qe,"f").type){case"text":this._emit("textDone",y(this,Qe,"f").text,y(this,ae,"f"));break;case"image_file":this._emit("imageFileDone",y(this,Qe,"f").image_file,y(this,ae,"f"));break}ee(this,Rt,n.index,"f")}ee(this,Qe,t.content[n.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(y(this,Rt,"f")!==void 0){let n=e.data.content[y(this,Rt,"f")];if(n)switch(n.type){case"image_file":this._emit("imageFileDone",n.image_file,y(this,ae,"f"));break;case"text":this._emit("textDone",n.text,y(this,ae,"f"));break}}y(this,ae,"f")&&this._emit("messageDone",e.data),ee(this,ae,void 0,"f")}},_n=function(e){let t=y(this,z,"m",An).call(this,e);switch(ee(this,or,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let r=e.data.delta;if(r.step_details&&r.step_details.type=="tool_calls"&&r.step_details.tool_calls&&t.step_details.type=="tool_calls")for(let i of r.step_details.tool_calls)i.index==y(this,Dr,"f")?this._emit("toolCallDelta",i,t.step_details.tool_calls[i.index]):(y(this,te,"f")&&this._emit("toolCallDone",y(this,te,"f")),ee(this,Dr,i.index,"f"),ee(this,te,t.step_details.tool_calls[i.index],"f"),y(this,te,"f")&&this._emit("toolCallCreated",y(this,te,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ee(this,or,void 0,"f"),e.data.step_details.type=="tool_calls"&&y(this,te,"f")&&(this._emit("toolCallDone",y(this,te,"f")),ee(this,te,void 0,"f")),this._emit("runStepDone",e.data,t);break;case"thread.run.step.in_progress":break}},vn=function(e){y(this,Ss,"f").push(e),this._emit("event",e)},An=function(e){switch(e.event){case"thread.run.step.created":return y(this,me,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=y(this,me,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){let n=X.accumulateDelta(t,r.delta);y(this,me,"f")[e.data.id]=n}return y(this,me,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":y(this,me,"f")[e.data.id]=e.data;break}if(y(this,me,"f")[e.data.id])return y(this,me,"f")[e.data.id];throw new Error("No snapshot available")},xn=function(e,t){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let i of n.delta.content)if(i.index in t.content){let o=t.content[i.index];t.content[i.index]=y(this,z,"m",In).call(this,i,o)}else t.content[i.index]=i,r.push(i);return[t,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},In=function(e,t){return X.accumulateDelta(t,e)},Pn=function(e){switch(ee(this,Nr,e.data,"f"),e.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":ee(this,Ye,e.data,"f"),y(this,te,"f")&&(this._emit("toolCallDone",y(this,te,"f")),ee(this,te,void 0,"f"));break;case"thread.run.cancelling":break}};var Ze=class extends g{create(e,t,r){return this._client.post(`/threads/${e}/messages`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}retrieve(e,t,r){return this._client.get(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/messages/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}list(e,t={},r){return k(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/messages`,Ot,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}del(e,t,r){return this._client.delete(`/threads/${e}/messages/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}},Ot=class extends ${};Ze.MessagesPage=Ot;var et=class extends g{retrieve(e,t,r,n={},i){return k(n)?this.retrieve(e,t,r,{},n):this._client.get(`/threads/${e}/runs/${t}/steps/${r}`,{query:n,...i,headers:{"OpenAI-Beta":"assistants=v2",...i==null?void 0:i.headers}})}list(e,t,r={},n){return k(r)?this.list(e,t,{},r):this._client.getAPIList(`/threads/${e}/runs/${t}/steps`,Tt,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}},Tt=class extends ${};et.RunStepsPage=Tt;var Ee=class extends g{constructor(){super(...arguments),this.steps=new et(this._client)}create(e,t,r){var o;let{include:n,...i}=t;return this._client.post(`/threads/${e}/runs`,{query:{include:n},body:i,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers},stream:(o=t.stream)!=null?o:!1})}retrieve(e,t,r){return this._client.get(`/threads/${e}/runs/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}update(e,t,r,n){return this._client.post(`/threads/${e}/runs/${t}`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}list(e,t={},r){return k(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/runs`,Mt,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}cancel(e,t,r){return this._client.post(`/threads/${e}/runs/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(e,n.id,r)}createAndStream(e,t,r){return X.createAssistantStream(e,this._client.beta.threads.runs,t,r)}async poll(e,t,r){let n={...r==null?void 0:r.headers,"X-Stainless-Poll-Helper":"true"};for(r!=null&&r.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let{data:i,response:o}=await this.retrieve(e,t,{...r,headers:{...r==null?void 0:r.headers,...n}}).withResponse();switch(i.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(r!=null&&r.pollIntervalMs)a=r.pollIntervalMs;else{let l=o.headers.get("openai-poll-after-ms");if(l){let c=parseInt(l);isNaN(c)||(a=c)}}await ve(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return i}}}stream(e,t,r){return X.createAssistantStream(e,this._client.beta.threads.runs,t,r)}submitToolOutputs(e,t,r,n){var i;return this._client.post(`/threads/${e}/runs/${t}/submit_tool_outputs`,{body:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers},stream:(i=r.stream)!=null?i:!1})}async submitToolOutputsAndPoll(e,t,r,n){let i=await this.submitToolOutputs(e,t,r,n);return await this.poll(e,i.id,n)}submitToolOutputsStream(e,t,r,n){return X.createToolAssistantStream(e,t,this._client.beta.threads.runs,r,n)}},Mt=class extends ${};Ee.RunsPage=Mt;Ee.Steps=et;Ee.RunStepsPage=Tt;var pe=class extends g{constructor(){super(...arguments),this.runs=new Ee(this._client),this.messages=new Ze(this._client)}create(e={},t){return k(e)?this.create({},e):this._client.post("/threads",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}update(e,t,r){return this._client.post(`/threads/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}del(e,t){return this._client.delete(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}createAndRun(e,t){var r;return this._client.post("/threads/runs",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers},stream:(r=e.stream)!=null?r:!1})}async createAndRunPoll(e,t){let r=await this.createAndRun(e,t);return await this.runs.poll(r.thread_id,r.id,t)}createAndRunStream(e,t){return X.createThreadAssistantStream(e,this._client.beta.threads,t)}};pe.Runs=Ee;pe.RunsPage=Mt;pe.Messages=Ze;pe.MessagesPage=Ot;var En=async s=>{let e=await Promise.allSettled(s),t=e.filter(n=>n.status==="rejected");if(t.length){for(let n of t)console.error(n.reason);throw new Error(`${t.length} promise(s) failed - see the above errors`)}let r=[];for(let n of e)n.status==="fulfilled"&&r.push(n.value);return r};var tt=class extends g{create(e,t,r){return this._client.post(`/vector_stores/${e}/files`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}list(e,t={},r){return k(t)?this.list(e,{},t):this._client.getAPIList(`/vector_stores/${e}/files`,Ne,{query:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}del(e,t,r){return this._client.delete(`/vector_stores/${e}/files/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(e,n.id,r)}async poll(e,t,r){let n={...r==null?void 0:r.headers,"X-Stainless-Poll-Helper":"true"};for(r!=null&&r.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let i=await this.retrieve(e,t,{...r,headers:n}).withResponse(),o=i.data;switch(o.status){case"in_progress":let a=5e3;if(r!=null&&r.pollIntervalMs)a=r.pollIntervalMs;else{let l=i.response.headers.get("openai-poll-after-ms");if(l){let c=parseInt(l);isNaN(c)||(a=c)}}await ve(a);break;case"failed":case"completed":return o}}}async upload(e,t,r){let n=await this._client.files.create({file:t,purpose:"assistants"},r);return this.create(e,{file_id:n.id},r)}async uploadAndPoll(e,t,r){let n=await this.upload(e,t,r);return await this.poll(e,n.id,r)}},Ne=class extends ${};tt.VectorStoreFilesPage=Ne;var Ft=class extends g{create(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}retrieve(e,t,r){return this._client.get(`/vector_stores/${e}/file_batches/${t}`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}cancel(e,t,r){return this._client.post(`/vector_stores/${e}/file_batches/${t}/cancel`,{...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}async createAndPoll(e,t,r){let n=await this.create(e,t);return await this.poll(e,n.id,r)}listFiles(e,t,r={},n){return k(r)?this.listFiles(e,t,{},r):this._client.getAPIList(`/vector_stores/${e}/file_batches/${t}/files`,Ne,{query:r,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}async poll(e,t,r){let n={...r==null?void 0:r.headers,"X-Stainless-Poll-Helper":"true"};for(r!=null&&r.pollIntervalMs&&(n["X-Stainless-Custom-Poll-Interval"]=r.pollIntervalMs.toString());;){let{data:i,response:o}=await this.retrieve(e,t,{...r,headers:n}).withResponse();switch(i.status){case"in_progress":let a=5e3;if(r!=null&&r.pollIntervalMs)a=r.pollIntervalMs;else{let l=o.headers.get("openai-poll-after-ms");if(l){let c=parseInt(l);isNaN(c)||(a=c)}}await ve(a);break;case"failed":case"cancelled":case"completed":return i}}}async uploadAndPoll(e,{files:t,fileIds:r=[]},n){var h;if(t==null||t.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let i=(h=n==null?void 0:n.maxConcurrency)!=null?h:5,o=Math.min(i,t.length),a=this._client,l=t.values(),c=[...r];async function d(f){for(let m of f){let x=await a.files.create({file:m,purpose:"assistants"},n);c.push(x.id)}}let u=Array(o).fill(l).map(d);return await En(u),await this.createAndPoll(e,{file_ids:c})}};var ge=class extends g{constructor(){super(...arguments),this.files=new tt(this._client),this.fileBatches=new Ft(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}update(e,t,r){return this._client.post(`/vector_stores/${e}`,{body:t,...r,headers:{"OpenAI-Beta":"assistants=v2",...r==null?void 0:r.headers}})}list(e={},t){return k(e)?this.list({},e):this._client.getAPIList("/vector_stores",kt,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}del(e,t){return this._client.delete(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}},kt=class extends ${};ge.VectorStoresPage=kt;ge.Files=tt;ge.VectorStoreFilesPage=Ne;ge.FileBatches=Ft;var ie=class extends g{constructor(){super(...arguments),this.vectorStores=new ge(this._client),this.chat=new St(this._client),this.assistants=new Xe(this._client),this.threads=new pe(this._client)}};ie.VectorStores=ge;ie.VectorStoresPage=kt;ie.Assistants=Xe;ie.AssistantsPage=At;ie.Threads=pe;var rt=class extends g{create(e,t){var r;return this._client.post("/completions",{body:e,...t,stream:(r=e.stream)!=null?r:!1})}};var st=class extends g{create(e,t){return this._client.post("/embeddings",{body:e,...t})}};var De=class extends g{create(e,t){return this._client.post("/files",ne({body:e,...t}))}retrieve(e,t){return this._client.get(`/files/${e}`,t)}list(e={},t){return k(e)?this.list({},e):this._client.getAPIList("/files",nt,{query:e,...t})}del(e,t){return this._client.delete(`/files/${e}`,t)}content(e,t){return this._client.get(`/files/${e}/content`,{...t,__binaryResponse:!0})}retrieveContent(e,t){return this._client.get(`/files/${e}/content`,{...t,headers:{Accept:"application/json",...t==null?void 0:t.headers}})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:r=30*60*1e3}={}){let n=new Set(["processed","error","deleted"]),i=Date.now(),o=await this.retrieve(e);for(;!o.status||!n.has(o.status);)if(await ve(t),o=await this.retrieve(e),Date.now()-i>r)throw new Re({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return o}},nt=class extends ${};De.FileObjectsPage=nt;var it=class extends g{list(e,t={},r){return k(t)?this.list(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/checkpoints`,Nt,{query:t,...r})}},Nt=class extends ${};it.FineTuningJobCheckpointsPage=Nt;var ye=class extends g{constructor(){super(...arguments),this.checkpoints=new it(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(`/fine_tuning/jobs/${e}`,t)}list(e={},t){return k(e)?this.list({},e):this._client.getAPIList("/fine_tuning/jobs",Dt,{query:e,...t})}cancel(e,t){return this._client.post(`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},r){return k(t)?this.listEvents(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/events`,$t,{query:t,...r})}},Dt=class extends ${},$t=class extends ${};ye.FineTuningJobsPage=Dt;ye.FineTuningJobEventsPage=$t;ye.Checkpoints=it;ye.FineTuningJobCheckpointsPage=Nt;var be=class extends g{constructor(){super(...arguments),this.jobs=new ye(this._client)}};be.Jobs=ye;be.FineTuningJobsPage=Dt;be.FineTuningJobEventsPage=$t;var ot=class extends g{createVariation(e,t){return this._client.post("/images/variations",ne({body:e,...t}))}edit(e,t){return this._client.post("/images/edits",ne({body:e,...t}))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}};var $e=class extends g{retrieve(e,t){return this._client.get(`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",at,e)}del(e,t){return this._client.delete(`/models/${e}`,t)}},at=class extends xr{};$e.ModelsPage=at;var lt=class extends g{create(e,t){return this._client.post("/moderations",{body:e,...t})}};var Bt=class extends g{create(e,t,r){return this._client.post(`/uploads/${e}/parts`,ne({body:t,...r}))}};var Be=class extends g{constructor(){super(...arguments),this.parts=new Bt(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(`/uploads/${e}/cancel`,t)}complete(e,t,r){return this._client.post(`/uploads/${e}/complete`,{body:t,...r})}};Be.Parts=Bt;var Cn,I=class extends Ar{constructor({baseURL:e=Gt("OPENAI_BASE_URL"),apiKey:t=Gt("OPENAI_API_KEY"),organization:r=(o=>(o=Gt("OPENAI_ORG_ID"))!=null?o:null)(),project:n=(a=>(a=Gt("OPENAI_PROJECT_ID"))!=null?a:null)(),...i}={}){var c;if(t===void 0)throw new v("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let l={apiKey:t,organization:r,project:n,...i,baseURL:e||"https://api.openai.com/v1"};if(!l.dangerouslyAllowBrowser&&un())throw new v(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);super({baseURL:l.baseURL,timeout:(c=l.timeout)!=null?c:6e5,httpAgent:l.httpAgent,maxRetries:l.maxRetries,fetch:l.fetch}),this.completions=new rt(this),this.chat=new Oe(this),this.embeddings=new st(this),this.files=new De(this),this.images=new ot(this),this.audio=new fe(this),this.moderations=new lt(this),this.models=new $e(this),this.fineTuning=new be(this),this.beta=new ie(this),this.batches=new Te(this),this.uploads=new Be(this),this._options=l,this.apiKey=t,this.organization=r,this.project=n}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project,...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}stringifyQuery(e){return Xr(e,{arrayFormat:"brackets"})}};Cn=I;I.OpenAI=Cn;I.DEFAULT_TIMEOUT=6e5;I.OpenAIError=v;I.APIError=L;I.APIConnectionError=Se;I.APIConnectionTimeoutError=Re;I.APIUserAbortError=H;I.NotFoundError=Ht;I.ConflictError=Wt;I.RateLimitError=Kt;I.BadRequestError=Ut;I.AuthenticationError=jt;I.InternalServerError=Vt;I.PermissionDeniedError=qt;I.UnprocessableEntityError=zt;I.toFile=is;I.fileFromPath=yr;I.Completions=rt;I.Chat=Oe;I.Embeddings=st;I.Files=De;I.FileObjectsPage=nt;I.Images=ot;I.Audio=fe;I.Moderations=lt;I.Models=$e;I.ModelsPage=at;I.FineTuning=be;I.Beta=ie;I.Batches=Te;I.BatchesPage=Je;I.Uploads=Be;var Sn=I;var Rn=require("obsidian");var Le=async(s,e={})=>{delete e.headers["content-length"],w.debug("obsidianFetch request:",{url:s,method:e.method||"GET",headers:e.headers,hasBody:!!e.body});let t={url:s,method:e.method||"GET",headers:e.headers};e.body&&(t.body=e.body,w.debug("Request body prepared:",t.body));try{w.debug("Sending request via requestUrl");let r=await(0,Rn.requestUrl)(t);w.debug("Response received:",{status:r.status,headers:r.headers,contentLength:r.text.length});let n={status:r.status,headers:r.headers};return new Response(r.text,n)}catch(r){throw w.error("Request failed:",r),r}};var Ue=class{constructor(){this.corsProviders=new Set}static getInstance(){return Ue.instance||(Ue.instance=new Ue),Ue.instance}getProviderKey(e){return`${e.url||"unknown"}:${e.type}`}shouldUseFallback(e){let t=this.getProviderKey(e);return this.corsProviders.has(t)}markProviderAsCorsBlocked(e){let t=this.getProviderKey(e);this.corsProviders.add(t),w.debug("Provider marked as CORS blocked:",{key:t,provider:e.name})}isCorsError(e){var o;let t=e.message.toLowerCase(),r=((o=e.name)==null?void 0:o.toLowerCase())||"";w.debug("Checking CORS error:",{message:e.message,name:e.name});let i=["cors policy","cors error","blocked by cors","cross-origin","access-control-allow-origin","access-control-allow-methods","access-control-allow-headers","preflight request","connection error","network error","failed to fetch","typeerror: failed to fetch","net::err_failed","fetch error","cors"].some(a=>t.includes(a)||r.includes(a));return w.debug("CORS error detection result:",{isCors:i}),i}clearAll(){this.corsProviders.clear(),w.debug("All CORS blocked providers cleared")}getBlockedProviderCount(){return this.corsProviders.size}};async function je(s,e,t,r){if(w.debug(`Starting ${r} for provider:`,s.name),ct.shouldUseFallback(s))return w.debug(`${r}: Provider already marked for CORS, using obsidianFetch directly.`),e(Le);try{let n=await e(t);return w.debug(`${r} completed successfully with default fetch.`),n}catch(n){if(ct.isCorsError(n)){w.debug(`CORS error detected in ${r}, retrying with obsidianFetch`),ct.markProviderAsCorsBlocked(s);try{let i=await e(Le);return w.debug(`${r} succeeded on retry with obsidianFetch.`),i}catch(i){throw w.error(`${r} failed on retry with obsidianFetch:`,i),i}}throw n}}var ct=Ue.getInstance();var qe=class{constructor(e){this.settings=e}getClient(e,t){let r;return ct.shouldUseFallback(e)?(r=Le,w.debug("Using obsidianFetch for CORS-blocked provider:",e.name)):t?r=t:r=this.settings.useNativeFetch?globalThis.fetch:ce,new Sn({apiKey:e.apiKey,baseURL:e.url,dangerouslyAllowBrowser:!0,fetch:r})}async fetchModels(e){return je(e,async r=>(await this.getClient(e,r).models.list()).data.map(o=>o.id),this.settings.useNativeFetch?fetch:ce,"fetchModels")}async embed(e){var n;let t=(n=e.input)!=null?n:e.text;if(!t)throw new Error("Either input or text parameter must be provided");let r=async i=>{let a=await this.getClient(e.provider,i).embeddings.create({model:e.provider.model||"",input:t});return w.debug("Embed response:",a),a.data.map(l=>l.embedding)};return je(e.provider,r,this.settings.useNativeFetch?fetch:ce,"embed")}async executeOpenAIGeneration(e,t,r,n,i){var c,d,u;let o=[];if("messages"in e&&e.messages)o=e.messages.map(h=>{if(typeof h.content=="string")return{role:h.role,content:h.content};let f=[];return h.content.forEach(m=>{m.type==="text"?f.push({type:"text",text:m.text}):m.type==="image_url"&&f.push({type:"image_url",image_url:{url:m.image_url.url}})}),{role:h.role,content:f}});else if("prompt"in e)if(e.systemPrompt&&o.push({role:"system",content:e.systemPrompt}),(c=e.images)!=null&&c.length){let h=[{type:"text",text:e.prompt}];e.images.forEach(f=>{h.push({type:"image_url",image_url:{url:f}})}),o.push({role:"user",content:h})}else o.push({role:"user",content:e.prompt});else throw new Error("Either messages or prompt must be provided");w.debug("Sending chat request to OpenAI");let a=await t.chat.completions.create({model:e.provider.model||"",messages:o,stream:!0,...e.options},{signal:i.signal}),l="";for await(let h of a){if(n()){w.debug("Generation aborted");break}let f=(u=(d=h.choices[0])==null?void 0:d.delta)==null?void 0:u.content;f&&(l+=f,r.data.forEach(m=>m(f,l)))}n()||(w.debug("Generation completed successfully:",{totalLength:l.length}),r.end.forEach(h=>h(l)))}async execute(e){var i,o,a,l;w.debug("Starting execute process with params:",{model:e.provider.model,messagesCount:((i=e.messages)==null?void 0:i.length)||0,promptLength:((o=e.prompt)==null?void 0:o.length)||0,systemPromptLength:((a=e.systemPrompt)==null?void 0:a.length)||0,hasImages:!!((l=e.images)!=null&&l.length)});let t=new AbortController,r=!1,n={data:[],end:[],error:[]};return(async()=>{if(!r)try{let c=async d=>{let u=this.getClient(e.provider,d);await this.executeOpenAIGeneration(e,u,n,()=>r,t)};await je(e.provider,c,this.settings.useNativeFetch?fetch:ce,"execute")}catch(c){n.error.forEach(d=>d(c))}})(),{onData(c){n.data.push(c)},onEnd(c){n.end.push(c)},onError(c){n.error.push(c)},abort(){w.debug("Request aborted"),r=!0,t.abort()}}}};var K=typeof globalThis!="undefined"&&globalThis||typeof self!="undefined"&&self||typeof global!="undefined"&&global||{},G={searchParams:"URLSearchParams"in K,iterable:"Symbol"in K&&"iterator"in Symbol,blob:"FileReader"in K&&"Blob"in K&&function(){try{return new Blob,!0}catch(s){return!1}}(),formData:"FormData"in K,arrayBuffer:"ArrayBuffer"in K};function uo(s){return s&&DataView.prototype.isPrototypeOf(s)}G.arrayBuffer&&(On=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],Mn=ArrayBuffer.isView||function(s){return s&&On.indexOf(Object.prototype.toString.call(s))>-1});var On,Mn;function Lt(s){if(typeof s!="string"&&(s=String(s)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(s)||s==="")throw new TypeError('Invalid character in header field name: "'+s+'"');return s.toLowerCase()}function Os(s){return typeof s!="string"&&(s=String(s)),s}function Ts(s){var e={next:function(){var t=s.shift();return{done:t===void 0,value:t}}};return G.iterable&&(e[Symbol.iterator]=function(){return e}),e}function q(s){this.map={},s instanceof q?s.forEach(function(e,t){this.append(t,e)},this):Array.isArray(s)?s.forEach(function(e){if(e.length!=2)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+e.length);this.append(e[0],e[1])},this):s&&Object.getOwnPropertyNames(s).forEach(function(e){this.append(e,s[e])},this)}q.prototype.append=function(s,e){s=Lt(s),e=Os(e);var t=this.map[s];this.map[s]=t?t+", "+e:e};q.prototype.delete=function(s){delete this.map[Lt(s)]};q.prototype.get=function(s){return s=Lt(s),this.has(s)?this.map[s]:null};q.prototype.has=function(s){return this.map.hasOwnProperty(Lt(s))};q.prototype.set=function(s,e){this.map[Lt(s)]=Os(e)};q.prototype.forEach=function(s,e){for(var t in this.map)this.map.hasOwnProperty(t)&&s.call(e,this.map[t],t,this)};q.prototype.keys=function(){var s=[];return this.forEach(function(e,t){s.push(t)}),Ts(s)};q.prototype.values=function(){var s=[];return this.forEach(function(e){s.push(e)}),Ts(s)};q.prototype.entries=function(){var s=[];return this.forEach(function(e,t){s.push([t,e])}),Ts(s)};G.iterable&&(q.prototype[Symbol.iterator]=q.prototype.entries);function Rs(s){if(!s._noBody){if(s.bodyUsed)return Promise.reject(new TypeError("Already read"));s.bodyUsed=!0}}function Fn(s){return new Promise(function(e,t){s.onload=function(){e(s.result)},s.onerror=function(){t(s.error)}})}function ho(s){var e=new FileReader,t=Fn(e);return e.readAsArrayBuffer(s),t}function fo(s){var e=new FileReader,t=Fn(e),r=/charset=([A-Za-z0-9_-]+)/.exec(s.type),n=r?r[1]:"utf-8";return e.readAsText(s,n),t}function mo(s){for(var e=new Uint8Array(s),t=new Array(e.length),r=0;r<e.length;r++)t[r]=String.fromCharCode(e[r]);return t.join("")}function Tn(s){if(s.slice)return s.slice(0);var e=new Uint8Array(s.byteLength);return e.set(new Uint8Array(s)),e.buffer}function kn(){return this.bodyUsed=!1,this._initBody=function(s){this.bodyUsed=this.bodyUsed,this._bodyInit=s,s?typeof s=="string"?this._bodyText=s:G.blob&&Blob.prototype.isPrototypeOf(s)?this._bodyBlob=s:G.formData&&FormData.prototype.isPrototypeOf(s)?this._bodyFormData=s:G.searchParams&&URLSearchParams.prototype.isPrototypeOf(s)?this._bodyText=s.toString():G.arrayBuffer&&G.blob&&uo(s)?(this._bodyArrayBuffer=Tn(s.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):G.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(s)||Mn(s))?this._bodyArrayBuffer=Tn(s):this._bodyText=s=Object.prototype.toString.call(s):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||(typeof s=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):G.searchParams&&URLSearchParams.prototype.isPrototypeOf(s)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},G.blob&&(this.blob=function(){var s=Rs(this);if(s)return s;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer){var s=Rs(this);return s||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else{if(G.blob)return this.blob().then(ho);throw new Error("could not read as ArrayBuffer")}},this.text=function(){var s=Rs(this);if(s)return s;if(this._bodyBlob)return fo(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(mo(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},G.formData&&(this.formData=function(){return this.text().then(yo)}),this.json=function(){return this.text().then(JSON.parse)},this}var po=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function go(s){var e=s.toUpperCase();return po.indexOf(e)>-1?e:s}function ut(s,e){if(!(this instanceof ut))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');e=e||{};var t=e.body;if(s instanceof ut){if(s.bodyUsed)throw new TypeError("Already read");this.url=s.url,this.credentials=s.credentials,e.headers||(this.headers=new q(s.headers)),this.method=s.method,this.mode=s.mode,this.signal=s.signal,!t&&s._bodyInit!=null&&(t=s._bodyInit,s.bodyUsed=!0)}else this.url=String(s);if(this.credentials=e.credentials||this.credentials||"same-origin",(e.headers||!this.headers)&&(this.headers=new q(e.headers)),this.method=go(e.method||this.method||"GET"),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal||function(){if("AbortController"in K){var i=new AbortController;return i.signal}}(),this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&t)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(t),(this.method==="GET"||this.method==="HEAD")&&(e.cache==="no-store"||e.cache==="no-cache")){var r=/([?&])_=[^&]*/;if(r.test(this.url))this.url=this.url.replace(r,"$1_="+new Date().getTime());else{var n=/\?/;this.url+=(n.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}ut.prototype.clone=function(){return new ut(this,{body:this._bodyInit})};function yo(s){var e=new FormData;return s.trim().split("&").forEach(function(t){if(t){var r=t.split("="),n=r.shift().replace(/\+/g," "),i=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(n),decodeURIComponent(i))}}),e}function bo(s){var e=new q,t=s.replace(/\r?\n[\t ]+/g," ");return t.split("\r").map(function(r){return r.indexOf(`
`)===0?r.substr(1,r.length):r}).forEach(function(r){var n=r.split(":"),i=n.shift().trim();if(i){var o=n.join(":").trim();try{e.append(i,o)}catch(a){console.warn("Response "+a.message)}}}),e}kn.call(ut.prototype);function we(s,e){if(!(this instanceof we))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(e||(e={}),this.type="default",this.status=e.status===void 0?200:e.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=e.statusText===void 0?"":""+e.statusText,this.headers=new q(e.headers),this.url=e.url||"",this._initBody(s)}kn.call(we.prototype);we.prototype.clone=function(){return new we(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new q(this.headers),url:this.url})};we.error=function(){var s=new we(null,{status:200,statusText:""});return s.ok=!1,s.status=0,s.type="error",s};var wo=[301,302,303,307,308];we.redirect=function(s,e){if(wo.indexOf(e)===-1)throw new RangeError("Invalid status code");return new we(null,{status:e,headers:{location:s}})};var dt=K.DOMException;try{new dt}catch(s){dt=function(e,t){this.message=e,this.name=t;var r=Error(e);this.stack=r.stack},dt.prototype=Object.create(Error.prototype),dt.prototype.constructor=dt}function Nn(s,e){return new Promise(function(t,r){var n=new ut(s,e);if(n.signal&&n.signal.aborted)return r(new dt("Aborted","AbortError"));var i=new XMLHttpRequest;function o(){i.abort()}i.onload=function(){var c={statusText:i.statusText,headers:bo(i.getAllResponseHeaders()||"")};n.url.indexOf("file://")===0&&(i.status<200||i.status>599)?c.status=200:c.status=i.status,c.url="responseURL"in i?i.responseURL:c.headers.get("X-Request-URL");var d="response"in i?i.response:i.responseText;setTimeout(function(){t(new we(d,c))},0)},i.onerror=function(){setTimeout(function(){r(new TypeError("Network request failed"))},0)},i.ontimeout=function(){setTimeout(function(){r(new TypeError("Network request timed out"))},0)},i.onabort=function(){setTimeout(function(){r(new dt("Aborted","AbortError"))},0)};function a(c){try{return c===""&&K.location.href?K.location.href:c}catch(d){return c}}if(i.open(n.method,a(n.url),!0),n.credentials==="include"?i.withCredentials=!0:n.credentials==="omit"&&(i.withCredentials=!1),"responseType"in i&&(G.blob?i.responseType="blob":G.arrayBuffer&&(i.responseType="arraybuffer")),e&&typeof e.headers=="object"&&!(e.headers instanceof q||K.Headers&&e.headers instanceof K.Headers)){var l=[];Object.getOwnPropertyNames(e.headers).forEach(function(c){l.push(Lt(c)),i.setRequestHeader(c,Os(e.headers[c]))}),n.headers.forEach(function(c,d){l.indexOf(d)===-1&&i.setRequestHeader(d,c)})}else n.headers.forEach(function(c,d){i.setRequestHeader(d,c)});n.signal&&(n.signal.addEventListener("abort",o),i.onreadystatechange=function(){i.readyState===4&&n.signal.removeEventListener("abort",o)}),i.send(typeof n._bodyInit=="undefined"?null:n._bodyInit)})}Nn.polyfill=!0;K.fetch||(K.fetch=Nn,K.Headers=q,K.Request=ut,K.Response=we);var _o="0.5.10",vo=Object.defineProperty,Ao=(s,e,t)=>e in s?vo(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,Ms=(s,e,t)=>(Ao(s,typeof e!="symbol"?e+"":e,t),t),ar=class extends Error{constructor(e,t){super(e),this.error=e,this.status_code=t,this.name="ResponseError",Error.captureStackTrace&&Error.captureStackTrace(this,ar)}},ks=class{constructor(e,t,r){Ms(this,"abortController"),Ms(this,"itr"),Ms(this,"doneCallback"),this.abortController=e,this.itr=t,this.doneCallback=r}abort(){this.abortController.abort()}async*[Symbol.asyncIterator](){for await(let e of this.itr){if("error"in e)throw new Error(e.error);if(yield e,e.done||e.status==="success"){this.doneCallback();return}}throw new Error("Did not receive done or success response in stream.")}},$r=async s=>{var r;if(s.ok)return;let e=`Error ${s.status}: ${s.statusText}`,t=null;if((r=s.headers.get("content-type"))!=null&&r.includes("application/json"))try{t=await s.json(),e=t.error||e}catch(n){console.log("Failed to parse error response as JSON")}else try{console.log("Getting text from response"),e=await s.text()||e}catch(n){console.log("Failed to get text from error response")}throw new ar(e,s.status)};function xo(){return typeof window!="undefined"&&window.navigator?`${window.navigator.platform.toLowerCase()} Browser/${navigator.userAgent};`:typeof process!="undefined"?`${process.arch} ${process.platform} Node.js/${process.version}`:""}var Br=async(s,e,t={})=>{let r={"Content-Type":"application/json",Accept:"application/json","User-Agent":`ollama-js/${_o} (${xo()})`};t.headers||(t.headers={});let n=Object.fromEntries(Object.entries(t.headers).filter(([i])=>!Object.keys(r).some(o=>o.toLowerCase()===i.toLowerCase())));return t.headers={...r,...n},s(e,t)},Dn=async(s,e,t)=>{let r=await Br(s,e,{headers:t==null?void 0:t.headers});return await $r(r),r},$n=async(s,e)=>{let t=await Br(s,e,{method:"HEAD"});return await $r(t),t},He=async(s,e,t,r)=>{let i=(a=>a!==null&&typeof a=="object"&&!Array.isArray(a))(t)?JSON.stringify(t):t,o=await Br(s,e,{method:"POST",body:i,signal:r==null?void 0:r.signal,headers:r==null?void 0:r.headers});return await $r(o),o},Io=async(s,e,t,r)=>{let n=await Br(s,e,{method:"DELETE",body:JSON.stringify(t),headers:r==null?void 0:r.headers});return await $r(n),n},Po=async function*(s){var n;let e=new TextDecoder("utf-8"),t="",r=s.getReader();for(;;){let{done:i,value:o}=await r.read();if(i)break;t+=e.decode(o);let a=t.split(`
`);t=(n=a.pop())!=null?n:"";for(let l of a)try{yield JSON.parse(l)}catch(c){console.warn("invalid json: ",l)}}for(let i of t.split(`
`).filter(o=>o!==""))try{yield JSON.parse(i)}catch(o){console.warn("invalid json: ",i)}},Eo=s=>{if(!s)return"http://127.0.0.1:11434";let e=s.includes("://");s.startsWith(":")&&(s=`http://127.0.0.1${s}`,e=!0),e||(s=`http://${s}`);let t=new URL(s),r=t.port;r||(e?r=t.protocol==="https:"?"443":"80":r="11434");let n=`${t.protocol}//${t.hostname}:${r}${t.pathname}`;return n.endsWith("/")&&(n=n.slice(0,-1)),n},Co=Object.defineProperty,So=(s,e,t)=>e in s?Co(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,Fs=(s,e,t)=>(So(s,typeof e!="symbol"?e+"":e,t),t),Ns=class{constructor(e){var t,r;Fs(this,"config"),Fs(this,"fetch"),Fs(this,"ongoingStreamedRequests",[]),this.config={host:"",headers:e==null?void 0:e.headers},e!=null&&e.proxy||(this.config.host=Eo((t=e==null?void 0:e.host)!=null?t:"http://127.0.0.1:11434")),this.fetch=(r=e==null?void 0:e.fetch)!=null?r:fetch}abort(){for(let e of this.ongoingStreamedRequests)e.abort();this.ongoingStreamedRequests.length=0}async processStreamableRequest(e,t){var i;t.stream=(i=t.stream)!=null?i:!1;let r=`${this.config.host}/api/${e}`;if(t.stream){let o=new AbortController,a=await He(this.fetch,r,t,{signal:o.signal,headers:this.config.headers});if(!a.body)throw new Error("Missing body");let l=Po(a.body),c=new ks(o,l,()=>{let d=this.ongoingStreamedRequests.indexOf(c);d>-1&&this.ongoingStreamedRequests.splice(d,1)});return this.ongoingStreamedRequests.push(c),c}return await(await He(this.fetch,r,t,{headers:this.config.headers})).json()}async encodeImage(e){if(typeof e!="string"){let t=new Uint8Array(e),r="",n=t.byteLength;for(let i=0;i<n;i++)r+=String.fromCharCode(t[i]);return btoa(r)}return e}async generate(e){return e.images&&(e.images=await Promise.all(e.images.map(this.encodeImage.bind(this)))),this.processStreamableRequest("generate",e)}async chat(e){if(e.messages)for(let t of e.messages)t.images&&(t.images=await Promise.all(t.images.map(this.encodeImage.bind(this))));return this.processStreamableRequest("chat",e)}async create(e){return this.processStreamableRequest("create",{name:e.model,stream:e.stream,modelfile:e.modelfile,quantize:e.quantize})}async pull(e){return this.processStreamableRequest("pull",{name:e.model,stream:e.stream,insecure:e.insecure})}async push(e){return this.processStreamableRequest("push",{name:e.model,stream:e.stream,insecure:e.insecure})}async delete(e){return await Io(this.fetch,`${this.config.host}/api/delete`,{name:e.model},{headers:this.config.headers}),{status:"success"}}async copy(e){return await He(this.fetch,`${this.config.host}/api/copy`,{...e},{headers:this.config.headers}),{status:"success"}}async list(){return await(await Dn(this.fetch,`${this.config.host}/api/tags`,{headers:this.config.headers})).json()}async show(e){return await(await He(this.fetch,`${this.config.host}/api/show`,{...e},{headers:this.config.headers})).json()}async embed(e){return await(await He(this.fetch,`${this.config.host}/api/embed`,{...e},{headers:this.config.headers})).json()}async embeddings(e){return await(await He(this.fetch,`${this.config.host}/api/embeddings`,{...e},{headers:this.config.headers})).json()}async ps(){return await(await Dn(this.fetch,`${this.config.host}/api/ps`,{headers:this.config.headers})).json()}},Kd=new Ns;var We=ei(require("fs"),1),ht=require("path"),Bn=require("crypto"),Ln=require("os");var lr=class extends Ns{async encodeImage(e){if(typeof e!="string")return Buffer.from(e).toString("base64");try{if(We.default.existsSync(e)){let t=await We.promises.readFile((0,ht.resolve)(e));return Buffer.from(t).toString("base64")}}catch(t){}return e}async parseModelfile(e,t=process.cwd()){let r=[],n=e.split(`
`);for(let i of n){let[o,a]=i.split(" ",2);if(["FROM","ADAPTER"].includes(o.toUpperCase())){let l=this.resolvePath(a.trim(),t);await this.fileExists(l)?r.push(`${o} @${await this.createBlob(l)}`):r.push(`${o} ${a}`)}else r.push(i)}return r.join(`
`)}resolvePath(e,t){return e.startsWith("~")?(0,ht.join)((0,Ln.homedir)(),e.slice(1)):(0,ht.resolve)(t,e)}async fileExists(e){try{return await We.promises.access(e),!0}catch(t){return!1}}async createBlob(e){if(typeof ReadableStream=="undefined")throw new Error("Streaming uploads are not supported in this environment.");let t=(0,We.createReadStream)(e),n=`sha256:${await new Promise((i,o)=>{let a=(0,Bn.createHash)("sha256");t.on("data",l=>a.update(l)),t.on("end",()=>i(a.digest("hex"))),t.on("error",o)})}`;try{await $n(this.fetch,`${this.config.host}/api/blobs/${n}`)}catch(i){if(i instanceof Error&&i.message.includes("404")){let o=new ReadableStream({start(a){t.on("data",l=>{a.enqueue(l)}),t.on("end",()=>{a.close()}),t.on("error",l=>{a.error(l)})}});await He(this.fetch,`${this.config.host}/api/blobs/${n}`,o)}else throw i}return n}async create(e){let t="";if(e.path)t=await We.promises.readFile(e.path,{encoding:"utf8"}),t=await this.parseModelfile(t,(0,ht.dirname)(e.path));else if(e.modelfile)t=await this.parseModelfile(e.modelfile);else throw new Error("Must provide either path or modelfile to create a model");return e.modelfile=t,e.stream?super.create(e):super.create(e)}},Gd=new lr;var Ro=2.5,Lr=2048,Un=2048,Oo=1.2,Ur=class{constructor(e){this.settings=e;this.modelInfoCache=new Map}dispose(){this.modelInfoCache.clear()}getClient(e,t){let r;return ct.shouldUseFallback(e)?(r=Le,w.debug("Using obsidianFetch for CORS-blocked provider:",e.name)):t?r=t:r=this.settings.useNativeFetch?globalThis.fetch:ce,new lr({host:e.url||"",fetch:r})}getDefaultModelInfo(){return{contextLength:0,lastContextLength:Lr}}async getCachedModelInfo(e,t){let r=`${e.url}_${t}`,n=this.modelInfoCache.get(r);if(n)return n;let i=this.getClient(e,this.settings.useNativeFetch?fetch:Le);try{let o=await i.show({model:t}),a=this.getDefaultModelInfo(),l=Object.entries(o.model_info).find(([c,d])=>(c.endsWith(".context_length")||c==="num_ctx")&&typeof d=="number"&&d>0);return l&&typeof l[1]=="number"&&(a.contextLength=l[1]),this.modelInfoCache.set(r,a),a}catch(o){return w.error("Failed to fetch model info:",o),this.getDefaultModelInfo()}}setModelInfoLastContextLength(e,t,r){let n=`${e.url}_${t}`,i=this.modelInfoCache.get(n);i&&this.modelInfoCache.set(n,{...i,lastContextLength:r||i.lastContextLength})}async fetchModels(e){return je(e,async r=>(await this.getClient(e,r).list()).models.map(o=>o.name),this.settings.useNativeFetch?fetch:ce,"fetchModels")}optimizeContext(e,t,r,n){let i=Math.ceil(e/Ro);if(i<=t)return{num_ctx:t>r?t:void 0,shouldUpdate:!1};let o=Math.min(Math.ceil(Math.max(i,r)*Oo),n),a=o>t;return{num_ctx:o,shouldUpdate:a}}prepareChatMessages(e){var n;let t=[],r=[];if("messages"in e&&e.messages)e.messages.forEach(i=>{var o;if(typeof i.content=="string")t.push({role:i.role,content:i.content});else{let a=i.content.filter(l=>l.type==="text").map(l=>l.type==="text"?l.text:"").join(`
`);i.content.filter(l=>l.type==="image_url").forEach(l=>{var c;l.type==="image_url"&&((c=l.image_url)!=null&&c.url)&&r.push(l.image_url.url)}),t.push({role:i.role,content:a})}(o=i.images)!=null&&o.length&&r.push(...i.images)});else if("prompt"in e)e.systemPrompt&&t.push({role:"system",content:e.systemPrompt}),t.push({role:"user",content:e.prompt}),(n=e.images)!=null&&n.length&&r.push(...e.images);else throw new Error("Either messages or prompt must be provided");return{chatMessages:t,extractedImages:r}}async executeOllamaGeneration(e,t,r,n){var h,f;let i=await this.getCachedModelInfo(e.provider,e.provider.model||"").catch(m=>(w.error("Failed to get model info:",m),null)),{chatMessages:o,extractedImages:a}=this.prepareChatMessages(e),l=a.length>0?a.map(m=>m.replace(/^data:image\/(.*?);base64,/,"")):void 0,c={};if(!(l!=null&&l.length)){let m=o.reduce((E,b)=>E+b.content.length,0),{num_ctx:x,shouldUpdate:p}=this.optimizeContext(m,(i==null?void 0:i.lastContextLength)||Lr,Lr,(i==null?void 0:i.contextLength)||Lr);x&&(c.num_ctx=x),p&&this.setModelInfoLastContextLength(e.provider,e.provider.model||"",x)}if(e.options&&Object.assign(c,e.options),l!=null&&l.length){let m=o.map(x=>x.role).lastIndexOf("user");m!==-1?o[m]={...o[m],images:l}:o.length>0?o[o.length-1]={...o[o.length-1],images:l}:o.push({role:"user",content:"",images:l})}w.debug("Sending chat request to Ollama");let d=await t.chat({model:e.provider.model||"",messages:o,stream:!0,options:{...c,...e.options}}),u="";for await(let m of d){if(n())break;let x=(h=m.message)==null?void 0:h.content;x&&(u+=x,r.data.forEach(p=>p(x,u))),m.done&&m.total_duration>0&&this.setModelInfoLastContextLength(e.provider,e.provider.model||"",(f=m.context)==null?void 0:f.length)}n()||r.end.forEach(m=>m(u))}async embed(e){var l;let t=(l=e.input)!=null?l:e.text;if(!t)throw new Error("Either input or text parameter must be provided");let r=await this.getCachedModelInfo(e.provider,e.provider.model||""),n=Array.isArray(t)?Math.max(...t.map(c=>c.length)):t.length,{num_ctx:i,shouldUpdate:o}=this.optimizeContext(n,r.lastContextLength||Un,Un,r.contextLength);o&&this.setModelInfoLastContextLength(e.provider,e.provider.model||"",i);let a=async c=>{let d=this.getClient(e.provider,c),u=Array.isArray(t)?t:[t],h=[];for(let f of u){let m=await d.embed({model:e.provider.model||"",input:f,options:{num_ctx:i}});h.push(m.embeddings[0])}return h};return je(e.provider,a,this.settings.useNativeFetch?fetch:ce,"embed")}async execute(e){let t=new AbortController,r=!1,n={data:[],end:[],error:[]};return(async()=>{if(!r)try{let i=async o=>{let a=this.getClient(e.provider,o);await this.executeOllamaGeneration(e,a,n,()=>r)};await je(e.provider,i,this.settings.useNativeFetch?fetch:ce,"execute")}catch(i){n.error.forEach(o=>o(i))}})(),{onData(i){n.data.push(i)},onEnd(i){n.end.push(i)},onError(i){n.error.push(i)},abort(){r=!0,t.abort()}}}};var jr=class{constructor(e,t){this.providers=[];this.version=1;this.plugin=t,this.providers=t.settings.providers||[],this.app=e,this.handlers={openai:new qe(t.settings),openrouter:new qe(t.settings),ollama:new Ur(t.settings),gemini:new qe(t.settings),lmstudio:new qe(t.settings),groq:new qe(t.settings)}}getHandler(e){return this.handlers[e]}async embed(e){try{return await this.getHandler(e.provider.type).embed(e)}catch(t){let r=t instanceof Error?t.message:_.t("errors.failedToEmbed");throw new cr.Notice(r),t}}async fetchModels(e){try{return await this.getHandler(e.type).fetchModels(e)}catch(t){let r=t instanceof Error?t.message:_.t("errors.failedToFetchModels");throw new cr.Notice(r),t}}async execute(e){try{return await this.getHandler(e.provider.type).execute(e)}catch(t){let r=t instanceof Error?t.message:_.t("errors.failedToExecuteRequest");throw new cr.Notice(r),t}}async migrateProvider(e){let t=["type","apiKey","url","model"];this.plugin.settings.providers=this.plugin.settings.providers||[];let r=this.plugin.settings.providers.find(n=>t.every(i=>n[i]===e[i]));return r?Promise.resolve(r):new Promise(n=>{new ft(this.app,`Migrate provider ${e.name}?`,async()=>{var i;(i=this.plugin.settings.providers)==null||i.push(e),await this.plugin.saveSettings(),n(e)},()=>{n(!1)}).open()})}checkCompatibility(e){if(e>this.version)throw new cr.Notice(_.t("errors.pluginMustBeUpdatedFormatted")),new Error(_.t("errors.pluginMustBeUpdated"))}};var jn='<g transform="translate(0,0) scale(4.1667)"><path d="M21.55 10.004a5.416 5.416 0 00-.478-4.501c-1.217-2.09-3.662-3.166-6.05-2.66A5.59 5.59 0 0010.831 1C8.39.995 6.224 2.546 5.473 4.838A5.553 5.553 0 001.76 7.496a5.487 5.487 0 00.691 6.5 5.416 5.416 0 00.477 4.502c1.217 2.09 3.662 3.165 6.05 2.66A5.586 5.586 0 0013.168 23c2.443.006 4.61-1.546 5.361-3.84a5.553 5.553 0 003.715-2.66 5.488 5.488 0 00-.693-6.497v.001zm-8.381 11.558a4.199 4.199 0 01-2.675-.954c.034-.018.093-.05.132-.074l4.44-2.53a.71.71 0 00.364-.623v-6.176l1.877 1.069c.02.01.033.029.036.05v5.115c-.003 2.274-1.87 4.118-4.174 4.123zM4.192 17.78a4.059 4.059 0 01-.498-2.763c.032.02.09.055.131.078l4.44 2.53c.225.13.504.13.73 0l5.42-3.088v2.138a.068.068 0 01-.027.057L9.9 19.288c-1.999 1.136-4.552.46-5.707-1.51h-.001zM3.023 8.216A4.15 4.15 0 015.198 6.41l-.002.151v5.06a.711.711 0 00.364.624l5.42 3.087-1.876 1.07a.067.067 0 01-.063.005l-4.489-2.559c-1.995-1.14-2.679-3.658-1.53-5.63h.001zm15.417 3.54l-5.42-3.088L14.896 7.6a.067.067 0 01.063-.006l4.489 2.557c1.998 1.14 2.683 3.662 1.529 5.633a4.163 4.163 0 01-2.174 1.807V12.38a.71.71 0 00-.363-.623zm1.867-2.773a6.04 6.04 0 00-.132-.078l-4.44-2.53a.731.731 0 00-.729 0l-5.42 3.088V7.325a.068.068 0 01.027-.057L14.1 4.713c2-1.137 4.555-.46 5.707 1.513.487.833.664 1.809.499 2.757h.001zm-11.741 3.81l-1.877-1.068a.065.065 0 01-.036-.051V6.559c.001-2.277 1.873-4.122 4.181-4.12.976 0 1.92.338 2.671.954-.034.018-.092.05-.131.073l-4.44 2.53a.71.71 0 00-.365.623l-.003 6.173v.002zm1.02-2.168L12 9.25l2.414 1.375v2.75L12 14.75l-2.415-1.375v-2.75z"></path></g>',qn='<g transform="translate(0,0) scale(4.1667)"><path d="M16.804 1.957l7.22 4.105v.087L16.73 10.21l.017-2.117-.821-.03c-1.059-.028-1.611.002-2.268.11-1.064.175-2.038.577-3.147 1.352L8.345 11.03c-.284.195-.495.336-.68.455l-.515.322-.397.234.385.23.53.338c.476.314 1.17.796 2.701 1.866 1.11.775 2.083 1.177 3.147 1.352l.3.045c.694.091 1.375.094 2.825.033l.022-2.159 7.22 4.105v.087L16.589 22l.014-1.862-.635.022c-1.386.042-2.137.002-3.138-.162-1.694-.28-3.26-.926-4.881-2.059l-2.158-1.5a21.997 21.997 0 00-.755-.498l-.467-.28a55.927 55.927 0 00-.76-.43C2.908 14.73.563 14.116 0 14.116V9.888l.14.004c.564-.007 2.91-.622 3.809-1.124l1.016-.58.438-.274c.428-.28 1.072-.726 2.686-1.853 1.621-1.133 3.186-1.78 4.881-2.059 1.152-.19 1.974-.213 3.814-.138l.02-1.907z"></path></g>',Hn='<g transform="translate(0,0) scale(4.1667)"><path d="M12.036 2c-3.853-.035-7 3-7.036 6.781-.035 3.782 3.055 6.872 6.908 6.907h2.42v-2.566h-2.292c-2.407.028-4.38-1.866-4.408-4.23-.029-2.362 1.901-4.298 4.308-4.326h.1c2.407 0 4.358 1.915 4.365 4.278v6.305c0 2.342-1.944 4.25-4.323 4.279a4.375 4.375 0 01-3.033-1.252l-1.851 1.818A7 7 0 0012.029 22h.092c3.803-.056 6.858-3.083 6.879-6.816v-6.5C18.907 4.963 15.817 2 12.036 2z"></path></g>',Wn='<g transform="translate(0,0) scale(4.1667)"><path d="M2.84 2a1.273 1.273 0 100 2.547h14.107a1.273 1.273 0 100-2.547H2.84zM7.935 5.33a1.273 1.273 0 000 2.548H22.04a1.274 1.274 0 000-2.547H7.935zM3.624 9.935c0-.704.57-1.274 1.274-1.274h14.106a1.274 1.274 0 010 2.547H4.898c-.703 0-1.274-.57-1.274-1.273zM1.273 12.188a1.273 1.273 0 100 2.547H15.38a1.274 1.274 0 000-2.547H1.273zM3.624 16.792c0-.704.57-1.274 1.274-1.274h14.106a1.273 1.273 0 110 2.547H4.898c-.703 0-1.274-.57-1.274-1.273zM13.029 18.849a1.273 1.273 0 100 2.547h9.698a1.273 1.273 0 100-2.547h-9.698z" fill-opacity=".3"></path><path d="M2.84 2a1.273 1.273 0 100 2.547h10.287a1.274 1.274 0 000-2.547H2.84zM7.935 5.33a1.273 1.273 0 000 2.548H18.22a1.274 1.274 0 000-2.547H7.935zM3.624 9.935c0-.704.57-1.274 1.274-1.274h10.286a1.273 1.273 0 010 2.547H4.898c-.703 0-1.274-.57-1.274-1.273zM1.273 12.188a1.273 1.273 0 100 2.547H11.56a1.274 1.274 0 000-2.547H1.273zM3.624 16.792c0-.704.57-1.274 1.274-1.274h10.286a1.273 1.273 0 110 2.547H4.898c-.703 0-1.274-.57-1.274-1.273zM13.029 18.849a1.273 1.273 0 100 2.547h5.78a1.273 1.273 0 100-2.547h-5.78z"></path></g>',zn='<g transform="translate(0,0) scale(4.1667)"><path d="M12 24A14.304 14.304 0 000 12 14.304 14.304 0 0012 0a14.305 14.305 0 0012 12 14.305 14.305 0 00-12 12"></path></g>',Kn='<g transform="translate(0,0) scale(4.1667)"><path d="M7.905 1.09c.216.085.411.225.588.41.295.306.544.744.734 1.263.191.522.315 1.1.362 1.68a5.054 5.054 0 012.049-.636l.051-.004c.87-.07 1.73.087 2.48.474.101.053.2.11.297.17.05-.569.172-1.134.36-1.644.19-.52.439-.957.733-1.264a1.67 1.67 0 01.589-.41c.257-.1.53-.118.796-.042.401.114.745.368 1.016.737.248.337.434.769.561 1.287.23.934.27 2.163.115 3.645l.053.04.026.019c.757.576 1.284 1.397 1.563 2.35.435 1.487.216 3.155-.534 4.088l-.018.021.002.003c.417.762.67 1.567.724 2.4l.002.03c.064 1.065-.2 2.137-.814 3.19l-.007.01.01.024c.472 1.157.62 2.322.438 3.486l-.006.039a.651.651 0 01-.747.536.648.648 0 01-.54-.742c.167-1.033.01-2.069-.48-3.123a.643.643 0 01.04-.617l.004-.006c.604-.924.854-1.83.8-2.72-.046-.779-.325-1.544-.8-2.273a.644.644 0 01.18-.886l.009-.006c.243-.159.467-.565.58-1.12a4.229 4.229 0 00-.095-1.974c-.205-.7-.58-1.284-1.105-1.683-.595-.454-1.383-.673-2.38-.61a.653.653 0 01-.632-.371c-.314-.665-.772-1.141-1.343-1.436a3.288 3.288 0 00-1.772-.332c-1.245.099-2.343.801-2.67 1.686a.652.652 0 01-.61.425c-1.067.002-1.893.252-2.497.703-.522.39-.878.935-1.066 1.588a4.07 4.07 0 00-.068 1.886c.112.558.331 1.02.582 1.269l.008.007c.212.207.257.53.109.785-.36.622-.629 1.549-.673 2.44-.05 1.018.186 1.902.719 2.536l.016.019a.643.643 0 01.095.69c-.576 1.236-.753 2.252-.562 3.052a.652.652 0 01-1.269.298c-.243-1.018-.078-2.184.473-3.498l.014-.035-.008-.012a4.339 4.339 0 01-.598-1.309l-.005-.019a5.764 5.764 0 01-.177-1.785c.044-.91.278-1.842.622-2.59l.012-.026-.002-.002c-.293-.418-.51-.953-.63-1.545l-.005-.024a5.352 5.352 0 01.093-2.49c.262-.915.777-1.701 1.536-2.269.06-.045.123-.09.186-.132-.159-1.493-.119-2.73.112-3.67.127-.518.314-.95.562-1.287.27-.368.614-.622 1.015-.737.266-.076.54-.059.797.042zm4.116 9.09c.936 0 1.8.313 2.446.855.63.527 1.005 1.235 1.005 1.94 0 .888-.406 1.58-1.133 2.022-.62.375-1.451.557-2.403.557-1.009 0-1.871-.259-2.493-.734-.617-.47-.963-1.13-.963-1.845 0-.707.398-1.417 1.056-1.946.668-.537 1.55-.849 2.485-.849zm0 .896a3.07 3.07 0 00-1.916.65c-.461.37-.722.835-.722 1.25 0 .428.21.829.61 1.134.455.347 1.124.548 1.943.548.799 0 1.473-.147 1.932-.426.463-.28.7-.686.7-1.257 0-.423-.246-.89-.683-1.256-.484-.405-1.14-.643-1.864-.643zm.662 1.21l.004.004c.12.151.095.37-.056.49l-.292.23v.446a.375.375 0 01-.376.373.375.375 0 01-.376-.373v-.46l-.271-.218a.347.347 0 01-.052-.49.353.353 0 01.494-.051l.215.172.22-.174a.353.353 0 01.49.051zm-5.04-1.919c.478 0 .867.39.867.871a.87.87 0 01-.868.871.87.87 0 01-.867-.87.87.87 0 01.867-.872zm8.706 0c.48 0 .868.39.868.871a.87.87 0 01-.868.871.87.87 0 01-.867-.87.87.87 0 01.867-.872zM7.44 2.3l-.003.002a.659.659 0 00-.285.238l-.005.006c-.138.189-.258.467-.348.832-.17.692-.216 1.631-.124 2.782.43-.128.899-.208 1.404-.237l.01-.001.019-.034c.046-.082.095-.161.148-.239.123-.771.022-1.692-.253-2.444-.134-.364-.297-.65-.453-.813a.628.628 0 00-.107-.09L7.44 2.3zm9.174.04l-.002.001a.628.628 0 00-.107.09c-.156.163-.32.45-.453.814-.29.794-.387 1.776-.23 2.572l.058.097.008.014h.03a5.184 5.184 0 011.466.212c.086-1.124.038-2.043-.128-2.722-.09-.365-.21-.643-.349-.832l-.004-.006a.659.659 0 00-.285-.239h-.004z"></path></g>';var qr=class extends Ce.Plugin{async onload(){await this.loadSettings(),(0,Ce.addIcon)("ai-providers-openai",jn),(0,Ce.addIcon)("ai-providers-ollama",Kn),(0,Ce.addIcon)("ai-providers-gemini",zn),(0,Ce.addIcon)("ai-providers-openrouter",qn),(0,Ce.addIcon)("ai-providers-lmstudio",Wn),(0,Ce.addIcon)("ai-providers-groq",Hn);let t=new fr(this.app,this);this.exposeAIProviders(),this.app.workspace.trigger("ai-providers-ready"),this.addSettingTab(t)}onunload(){delete this.app.aiProviders}async loadSettings(){var t;this.settings=Object.assign({},js,await this.loadData()),w.setEnabled((t=this.settings.debugLogging)!=null?t:!1)}async saveSettings(){await this.saveData(this.settings),this.exposeAIProviders()}exposeAIProviders(){this.aiProviders=new jr(this.app,this),this.app.aiProviders=this.aiProviders}};

/* nosourcemap */