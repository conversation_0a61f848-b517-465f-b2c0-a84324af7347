
"smart_sources:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md": {"path":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08810501,-0.05811901,-0.0255399,-0.01242829,-0.07479512,0.04129943,-0.02319564,0.01087786,0.04469432,-0.00918789,-0.00028544,-0.04400628,0.05264558,0.01583138,-0.01453939,-0.03102538,0.0160871,-0.00616897,-0.0688981,-0.0272712,0.1329474,-0.04915164,-0.06459996,-0.07445683,0.0295258,0.05151756,-0.02445696,-0.0236985,-0.00131138,-0.22421831,0.01876034,0.02713432,0.03157428,-0.01535673,-0.08190368,-0.01281264,-0.06042446,0.07756382,-0.08424825,0.0025254,-0.01616625,0.03025688,-0.02895774,-0.00891358,0.03780458,-0.05548295,-0.02747406,0.03705364,0.01945436,0.01168116,-0.0636143,-0.05817448,-0.01621469,0.03733343,0.03920847,0.03555378,0.02945081,0.05949912,0.03320947,0.05521631,0.11373597,0.0703517,-0.2065683,0.03555774,-0.00476799,0.0079063,-0.03033533,-0.06105577,-0.00451193,0.05585716,0.04572254,0.02163407,-0.0043945,0.06846617,0.053457,-0.02156897,-0.03791063,-0.09046264,-0.07965405,0.01405438,-0.04434198,0.00494039,0.00347122,0.0205303,0.00925492,0.02901022,0.0581055,0.01079717,0.05295674,-0.03703982,0.00636645,-0.02748295,0.06172676,0.04222585,0.00740005,0.01383412,0.01044317,-0.01126722,0.0247293,0.11249708,0.00882044,0.03562832,0.00969572,-0.00091868,0.00849594,0.0162787,0.00789964,-0.05490029,-0.02295426,0.02750744,0.06631894,0.02482408,0.021396,-0.03619437,-0.05853262,-0.02646828,0.01416662,-0.01638671,0.02396541,0.04576935,-0.01838891,0.03317109,-0.00731276,-0.03237925,-0.00669505,0.04653621,0.02791779,0.0669552,0.00994253,0.00333134,0.02216008,0.03929362,-0.11664282,-0.03310873,-0.00053387,-0.02681126,0.01082906,-0.01384431,0.00304259,0.02418965,0.00469921,-0.04161244,0.04148582,-0.06466062,-0.0564681,0.09952705,-0.02932468,-0.01515607,0.00721054,0.02549445,0.00660017,-0.00722196,-0.02977643,-0.04527963,-0.00095676,0.04953776,0.11559159,0.09120193,-0.03788299,0.02092079,0.0038406,-0.02833782,-0.05786754,0.12168032,-0.04940264,-0.11282869,-0.01177016,0.1070977,-0.01255158,-0.07587662,0.00255866,0.02408088,-0.04864652,0.04493128,0.06854326,0.01315423,-0.05284096,-0.02108536,0.00496459,-0.02333524,0.0252415,-0.04453381,-0.04853404,0.01650504,-0.02529651,-0.0687879,-0.0104937,-0.0760313,0.04109006,0.06852417,-0.00451337,0.02870172,0.02287119,0.02079066,-0.04072394,-0.05111495,-0.03154548,-0.02819535,0.02583201,-0.02341073,0.00726178,0.01544643,0.02313876,0.02060011,-0.0148005,-0.00401281,-0.02277233,-0.04283275,0.04211842,0.03531843,-0.0553712,0.00028379,0.02888126,0.08697041,-0.06892911,0.05355475,0.00279394,0.01927893,-0.01050547,0.01398808,-0.02282742,-0.03094735,-0.08918835,-0.18115127,-0.03749806,-0.02779035,0.03224393,0.00107061,-0.01630156,0.03851673,-0.02036175,0.00347796,0.05399537,0.09483334,-0.04212365,-0.00957809,0.00393556,-0.03119008,0.04930335,-0.09155422,-0.01663057,-0.00034134,0.05657813,0.02309367,0.002777,-0.01764923,-0.11363021,0.01208123,-0.01181157,0.12184225,0.00859821,-0.01355272,-0.0101292,0.09544382,-0.03865097,-0.02005546,-0.01592248,0.03010627,0.0199133,-0.09373754,-0.0167625,-0.05277641,-0.01423507,-0.07231254,0.00600648,0.03773938,-0.10091256,0.00521965,-0.01433794,0.02055432,-0.03146815,-0.01926245,0.04476755,0.00049014,0.01081445,0.02386858,0.018177,0.03618651,-0.03129346,-0.04564046,-0.0055385,-0.01770233,-0.0063666,0.00552442,-0.03704501,0.07350225,-0.02875371,0.02614829,0.00918975,-0.02019804,-0.04675441,0.00209457,0.00557621,-0.00504531,0.12155893,0.02607499,0.03637201,-0.00911384,0.03815686,0.04084061,-0.02041603,0.04081295,-0.0062107,-0.02938475,-0.06837852,0.05218733,0.06699483,0.04262882,0.04629419,0.07097856,0.02632132,-0.00706294,0.04527469,0.01162985,0.04478248,-0.02460736,0.01511483,0.02548362,0.06421158,-0.26435146,0.04978405,-0.0185927,0.06046141,0.02037188,-0.00510828,0.06485595,-0.00455141,0.03756693,-0.03993753,0.04283112,0.06887978,0.01412537,-0.07538025,-0.01357536,-0.02222498,0.04326807,-0.05700225,0.07577659,-0.00997232,0.00951383,-0.00620111,0.24294071,0.01821682,-0.00000992,0.0137339,-0.00828577,-0.00442943,-0.00936101,0.05274407,-0.02531198,0.00819027,0.08902434,0.03228088,-0.03421802,0.06488054,-0.04052546,0.01011855,-0.02826735,0.03835976,-0.0607781,0.02079574,-0.03216621,0.02888283,0.11549629,0.02814402,-0.03331514,-0.09267836,0.04572735,0.00962704,-0.06122495,-0.05982218,-0.11394964,-0.02692288,-0.01144411,0.01450025,0.01080061,0.03106952,-0.01572493,-0.03194682,-0.00527806,-0.04775331,0.05665113,0.00287586,0.01035631],"last_embed":{"hash":"1hl675i","tokens":464}}},"last_read":{"hash":"1hl675i","at":1753423480531},"class_name":"SmartSource","last_import":{"mtime":1753366216659,"size":10359,"at":1753423416052,"hash":"1hl675i"},"blocks":{"#---frontmatter---":[1,6],"#Lexicographical Lotto Wheels, Lottery Wheels Software":[8,142],"#Lexicographical Lotto Wheels, Lottery Wheels Software#{1}":[10,15],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Software for Balanced Lotto Wheels Based on Lexicographic Order, Indexes":[16,17],"#Lexicographical Lotto Wheels, Lottery Wheels Software#By Ion Saliu, ★ _Founder of Lottery Wheeling Mathematics_":[18,104],"#Lexicographical Lotto Wheels, Lottery Wheels Software#By Ion Saliu, ★ _Founder of Lottery Wheeling Mathematics_#{1}":[20,104],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies":[105,142],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{1}":[107,108],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{2}":[109,109],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{3}":[110,110],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{4}":[111,111],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{5}":[112,113],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{6}":[114,115],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{7}":[116,117],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{8}":[118,119],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{9}":[120,120],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{10}":[121,121],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{11}":[122,122],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{12}":[123,123],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{13}":[124,124],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{14}":[125,125],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{15}":[126,126],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{16}":[127,127],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{17}":[128,128],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{18}":[129,129],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{19}":[130,130],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{20}":[131,131],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{21}":[132,132],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{22}":[133,133],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{23}":[134,134],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{24}":[135,136],"#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{25}":[137,142]},"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lotto-wheels.gif","line":14},{"title":"Generate loto wheels inside the median Gauss bell.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":20},{"title":"The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.","target":"https://saliu.com/ScreenImgs/lexico-lottowheels.gif","line":28},{"title":"Dynamic versus Static in Lotto Analysis","target":"https://saliu.com/bbs/messages/919.html","line":33},{"title":"Create lottery wheels as symmetrical lotto wheels in software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":88},{"title":"lotto wheels","target":"https://saliu.com/lotto_wheels.html","line":95},{"title":"Register for the best software to generate well balanced lotto wheels, lottery wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":101},{"title":"\n\n## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies\n\n","target":"https://saliu.com/content/lottery.html","line":103},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":109},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":122},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":124},{"title":"The myth of lotto wheels or abbreviated lotto systems","target":"https://saliu.com/bbs/messages/11.html","line":126},{"title":"WHEEL-632 available as lotto wheeling software","target":"https://saliu.com/bbs/messages/wheel.html","line":128},{"title":"lottery software for players of lotto wheels","target":"https://saliu.com/bbs/messages/857.html","line":129},{"title":"Software to verify lotto wheels","target":"https://saliu.com/check-wheels.html","line":130},{"title":"Check WHEEL and lotto wheels for winners","target":"https://saliu.com/bbs/messages/90.html","line":131},{"title":"_**The Best Lotto Wheels for 18 Numbers, 4 in 6 Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":132},{"title":"Genuine Powerball wheels","target":"https://saliu.com/powerball_wheels.html","line":133},{"title":"Genuine Mega Millions wheels","target":"https://saliu.com/megamillions_wheels.html","line":134},{"title":"Genuine Euromillions wheels","target":"https://saliu.com/euro_millions_wheels.html","line":135},{"title":"Lotto wheels, lottery wheels are lexicographically balanced and randomized.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":137},{"title":"Forums","target":"https://forums.saliu.com/","line":139},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":139},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":139},{"title":"Contents","target":"https://saliu.com/content/index.html","line":139},{"title":"Home","target":"https://saliu.com/index.htm","line":139},{"title":"Software","target":"https://saliu.com/infodown.html","line":139},{"title":"Search","target":"https://saliu.com/Search.htm","line":139},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":139},{"title":"The lotto wheels consist of balanced lexicographic indexes or ranks; software to generate best lotto wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":141}],"metadata":{"created":"2025-07-24T22:10:03 (UTC +08:00)","tags":["lotto wheels","combinations","numbers","lotto","systems","wheel","lottery","lexicographic","lexicographical","order","index","balanced","randomized","loto","symmetrical"],"source":"https://saliu.com/bbs/messages/772.html","author":"Ion Saliu"}},"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09267964,-0.0701776,-0.01429007,-0.00446098,-0.0459461,0.02650889,-0.01672776,0.03109534,0.02571423,-0.01425468,-0.01948985,-0.03845165,0.0328602,0.01777416,0.00730722,0.00435928,0.02698693,-0.00068401,-0.05861503,-0.01407313,0.1260922,-0.03455339,-0.06679378,-0.07492228,0.03040634,0.03828527,-0.0257308,-0.03254909,-0.01458442,-0.19172913,0.00554216,0.03462691,0.01337919,-0.03002227,-0.04701712,-0.03439783,-0.06123668,0.081359,-0.03623823,0.02306402,-0.00532289,0.02525477,-0.04919445,0.0124393,0.02075772,-0.04596661,-0.02570344,0.00947407,0.02456781,0.01250437,-0.0593762,-0.03413625,-0.00018369,0.03574928,0.03275094,0.0478063,0.03848664,0.05195681,0.05459293,0.05203376,0.07920288,0.0446698,-0.2058419,0.05135929,0.01518463,0.00168432,-0.02516144,-0.0525434,-0.02725678,0.06607319,0.07642215,0.04423222,0.00013678,0.06269295,0.04905914,-0.00668794,-0.04017347,-0.1004119,-0.06441792,-0.00869194,-0.04590299,-0.00437282,-0.02542114,-0.03064445,-0.01811225,-0.00105163,0.04226856,0.04325319,0.03877121,-0.03694832,0.0094185,-0.00450935,0.0852304,0.06477436,-0.04040818,0.0008604,0.00547698,-0.01439229,0.01287604,0.11124867,-0.00861264,0.03935935,0.01190772,0.00146489,0.01961054,0.02980235,0.00401057,-0.05575074,-0.02722535,0.03048187,0.06771486,0.03516268,0.05767078,-0.0731902,-0.04330041,-0.03576571,0.03134365,-0.00904649,0.03780522,0.03263843,-0.04540401,0.01162956,0.0179123,-0.0073394,-0.00630435,0.04580693,0.04017865,0.07729139,0.01204331,0.00577222,0.01310576,0.04076521,-0.10603375,-0.02063907,-0.00020417,-0.00798851,0.03399774,-0.0314331,0.0286973,0.00297141,-0.00645223,-0.07599426,0.04816849,-0.08042232,-0.06415907,0.07410728,-0.00441479,-0.01997546,0.01641529,0.03374301,-0.02317679,0.0018343,-0.01153899,-0.00991981,0.00619118,0.02446554,0.14549893,0.09462449,-0.01359022,0.02353631,0.01425413,-0.05064626,-0.05364764,0.14411083,-0.06170279,-0.14475904,-0.01790353,0.06475983,-0.01390731,-0.09323648,0.00149587,0.00918433,-0.03631091,0.06633855,0.09664845,0.0338853,-0.03314055,-0.01742765,-0.0413669,-0.01290515,-0.01657752,-0.0548346,-0.05825348,0.03079856,-0.02934821,-0.08269066,-0.0246864,-0.0510437,0.04389992,0.0622869,-0.00238526,-0.01489747,0.00002306,0.02720312,-0.03916612,-0.04134681,-0.01998052,-0.0453638,0.02003379,-0.03656407,0.01502471,0.02325482,0.01139804,0.03821515,0.01307828,-0.00235281,-0.02175565,-0.05337861,0.01591167,0.03384481,-0.037021,0.0261437,0.02619314,0.07953636,-0.07397241,0.04711774,-0.00796862,0.01862236,0.00209562,-0.00316395,-0.03500856,-0.02064734,-0.09074995,-0.20839235,-0.0219383,-0.02302337,0.02411762,0.0022087,-0.0167041,0.01397719,-0.0126865,0.02620474,0.06752406,0.06555764,-0.05412538,-0.01326791,-0.02093603,-0.03417291,0.04543523,-0.06330327,-0.02956031,0.00899339,0.0598575,0.02257344,-0.00137932,-0.02442088,-0.1041321,0.0221596,-0.001441,0.13233618,0.03909735,-0.03501078,-0.0152794,0.10567141,-0.02610698,0.00184902,-0.01928459,0.03017272,0.01010065,-0.09290245,0.00140997,-0.0606716,-0.03829036,-0.07882293,0.0169734,0.03405746,-0.08328976,0.00499823,-0.02191911,0.01677292,-0.04122219,-0.01887748,0.03706967,-0.00852139,0.02476326,0.00339573,0.03689125,0.06449334,0.00574894,-0.03819763,-0.02610311,-0.01836446,-0.02313067,-0.01682403,-0.02115178,0.06581347,-0.0491384,-0.00319989,0.048235,-0.0344165,-0.06690133,-0.03255792,-0.00614171,0.00741485,0.11027853,0.02474796,0.00724348,-0.01383789,0.02375987,0.01362179,0.00712747,0.04443241,-0.00129046,-0.00987482,-0.06109538,0.04883391,0.07984317,0.05233162,0.0604715,0.04326346,0.00827943,0.01127349,0.03872449,0.01462668,0.05465738,-0.02303398,0.00726515,0.06962838,0.02059777,-0.25782984,0.05541677,0.00753694,0.06952873,0.0212156,0.01799714,0.05104161,-0.01478036,0.00611684,-0.02642044,0.0472867,0.07411136,0.00487937,-0.04680181,-0.02913626,-0.01376244,0.05299966,-0.0449087,0.04572723,0.00567635,0.00927118,-0.00616831,0.24014956,0.04147402,-0.00521242,0.00130705,-0.01023202,0.01301826,-0.01585307,0.06215973,-0.02114428,0.00402059,0.05589087,0.01423627,-0.0401695,0.05472631,-0.03664181,0.00102444,-0.00191883,0.05307833,-0.0955836,0.02223798,-0.04324818,0.02173262,0.11206959,0.04381067,-0.04977391,-0.09552339,0.0377588,0.01030448,-0.04592114,-0.07358146,-0.11519387,-0.00481707,-0.00817388,0.0355548,0.03340348,0.00473885,0.00657172,-0.02487749,0.00657971,-0.03914464,0.06093586,-0.00986884,0.00095291],"last_embed":{"hash":"1cxkwof","tokens":113}}},"text":null,"length":0,"last_read":{"hash":"1cxkwof","at":1753423479800},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#---frontmatter---","lines":[1,6],"size":264,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0809801,-0.05669942,-0.01791601,-0.03422338,-0.08292065,0.05065282,-0.01645132,0.00495204,0.05159064,-0.01224556,0.00813015,-0.03749828,0.05153027,0.01289643,-0.02222941,-0.01451259,0.02354709,-0.00638746,-0.08417985,-0.03036357,0.12635121,-0.04878362,-0.05750411,-0.07869951,0.01589373,0.05009937,-0.02559608,-0.03240496,0.00188211,-0.23743729,0.02562375,0.02737432,0.03508319,-0.01900131,-0.08192468,-0.00336018,-0.06679127,0.07406749,-0.09713793,-0.01485581,-0.01943987,0.03483735,-0.01559283,-0.01832196,0.03920447,-0.06951026,-0.0208459,0.04149476,0.03380782,0.00941951,-0.06727125,-0.04862351,-0.01298444,0.04270353,0.03929264,0.02267396,0.01988039,0.05791246,0.03983993,0.05889436,0.10897805,0.06950646,-0.21358316,0.0250884,-0.00072832,-0.00180022,-0.03793751,-0.05585818,-0.0119276,0.05480033,0.03797103,0.02886917,-0.00202685,0.06709459,0.05724958,-0.01693161,-0.03773786,-0.08984411,-0.08415856,0.01701084,-0.05478564,0.00056563,0.01050972,0.01797504,0.01016179,0.02912547,0.06755639,0.00957861,0.05505585,-0.02510636,0.00406579,-0.01837475,0.04622108,0.02935295,0.00039245,0.00804465,0.01362677,-0.02354174,0.03316599,0.11243301,0.02056092,0.02398183,-0.00169445,-0.00167546,0.00108283,0.01585254,0.00526436,-0.04992402,-0.02287259,0.03940025,0.0708871,0.03282816,0.02206255,-0.01795748,-0.05905854,-0.02541737,0.0050424,-0.01530196,0.01109832,0.04645383,-0.01385361,0.03600096,-0.00512285,-0.03444806,-0.01689921,0.04964011,0.02896643,0.06751012,0.01516026,0.00320973,0.02050305,0.03489764,-0.11132265,-0.02435542,0.00182751,-0.03526245,0.00835085,-0.00517872,-0.00012198,0.0281494,-0.0009984,-0.05121597,0.04048871,-0.06265456,-0.04813831,0.08804335,-0.03672945,-0.01670465,0.00226853,0.02599512,0.02320073,-0.00358802,-0.03039072,-0.05558735,-0.00454834,0.04672026,0.12013301,0.09093221,-0.05291439,0.01793967,-0.00182649,-0.02682945,-0.04900893,0.11876039,-0.04331747,-0.10633399,-0.00912268,0.10346572,-0.01537292,-0.07056893,-0.00220624,0.02869471,-0.07240643,0.05861746,0.06322899,0.00545623,-0.06224092,-0.01853771,0.0226982,-0.02538026,0.02993693,-0.04400746,-0.03987936,0.0186752,-0.01813601,-0.05150215,0.00568183,-0.06145737,0.04095513,0.05808939,-0.01389336,0.0355944,0.02579875,0.01475547,-0.0440057,-0.05171233,-0.03369867,-0.0350363,0.0390934,-0.02581883,0.01600746,0.01150864,0.0292822,0.02171877,-0.01100482,-0.0027292,-0.03525282,-0.03920944,0.05510923,0.03461907,-0.06616993,-0.01535586,0.0283314,0.08502825,-0.07287119,0.05738582,0.00932359,0.01702872,-0.00855788,0.01922797,-0.00111208,-0.03352556,-0.07638443,-0.16951278,-0.03875437,-0.03524177,0.02691847,0.00108024,-0.00564202,0.03108881,-0.02627296,0.00597758,0.05214446,0.0941616,-0.03879669,0.00294932,0.01745369,-0.04163606,0.0504994,-0.10800928,-0.01753344,-0.0093452,0.05510325,0.03315221,-0.00232449,-0.03500771,-0.10922282,0.01068405,-0.02111374,0.13369527,0.0006185,-0.019558,-0.0161394,0.08378342,-0.04551431,-0.02440125,0.01229441,0.01989854,0.01566832,-0.08611526,-0.03130621,-0.04286144,-0.00729821,-0.06467468,0.01417661,0.02193591,-0.10310242,0.01111185,-0.0108669,0.01281546,-0.01915192,-0.01039903,0.05127344,0.00119746,0.0121034,0.02677449,0.01522032,0.03108576,-0.03023012,-0.04558318,-0.00148192,-0.01535355,0.0028063,0.00841436,-0.03741207,0.07167959,-0.02488976,0.02960748,0.0114638,-0.02457187,-0.0450456,0.01469369,-0.00528923,-0.00036428,0.09841704,0.03467504,0.04529471,-0.00771355,0.04288159,0.05126672,-0.01381874,0.02879639,-0.00720694,-0.03341511,-0.05052217,0.03989471,0.07266844,0.04347048,0.03088992,0.07848212,0.03991164,-0.00942941,0.04944025,0.01995629,0.04796887,-0.02728594,0.01970957,0.0085762,0.06607632,-0.25893429,0.05279122,-0.02698559,0.0644976,0.02182753,-0.01350538,0.0735982,0.01376033,0.03969499,-0.03991516,0.05067079,0.07729901,0.01721694,-0.08618192,0.00025441,-0.03561465,0.04895972,-0.05968112,0.08301938,-0.00682141,0.02307072,-0.00442491,0.22641304,0.00871896,0.00115132,0.00953652,-0.00630105,0.00183605,-0.00169049,0.03979309,-0.02429824,0.01195015,0.09835324,0.02626638,-0.02340627,0.06115517,-0.02671494,0.01079065,-0.03607211,0.02893483,-0.05306708,0.01742469,-0.04315284,0.02263924,0.11258516,0.02781046,-0.02753485,-0.10060661,0.0360067,0.0096706,-0.05522452,-0.05575794,-0.10488016,-0.04100623,-0.0113175,0.01091222,0.013343,0.03662081,-0.02701538,-0.04164897,-0.00867682,-0.05351208,0.04522226,-0.00481915,0.00822731],"last_embed":{"hash":"1ygfjsi","tokens":457}}},"text":null,"length":0,"last_read":{"hash":"1ygfjsi","at":1753423479835},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software","lines":[8,142],"size":10077,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lotto-wheels.gif","line":7},{"title":"Generate loto wheels inside the median Gauss bell.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":13},{"title":"The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.","target":"https://saliu.com/ScreenImgs/lexico-lottowheels.gif","line":21},{"title":"Dynamic versus Static in Lotto Analysis","target":"https://saliu.com/bbs/messages/919.html","line":26},{"title":"Create lottery wheels as symmetrical lotto wheels in software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":81},{"title":"lotto wheels","target":"https://saliu.com/lotto_wheels.html","line":88},{"title":"Register for the best software to generate well balanced lotto wheels, lottery wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":94},{"title":"\n\n## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies\n\n","target":"https://saliu.com/content/lottery.html","line":96},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":102},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":115},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":117},{"title":"The myth of lotto wheels or abbreviated lotto systems","target":"https://saliu.com/bbs/messages/11.html","line":119},{"title":"WHEEL-632 available as lotto wheeling software","target":"https://saliu.com/bbs/messages/wheel.html","line":121},{"title":"lottery software for players of lotto wheels","target":"https://saliu.com/bbs/messages/857.html","line":122},{"title":"Software to verify lotto wheels","target":"https://saliu.com/check-wheels.html","line":123},{"title":"Check WHEEL and lotto wheels for winners","target":"https://saliu.com/bbs/messages/90.html","line":124},{"title":"_**The Best Lotto Wheels for 18 Numbers, 4 in 6 Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":125},{"title":"Genuine Powerball wheels","target":"https://saliu.com/powerball_wheels.html","line":126},{"title":"Genuine Mega Millions wheels","target":"https://saliu.com/megamillions_wheels.html","line":127},{"title":"Genuine Euromillions wheels","target":"https://saliu.com/euro_millions_wheels.html","line":128},{"title":"Lotto wheels, lottery wheels are lexicographically balanced and randomized.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":130},{"title":"Forums","target":"https://forums.saliu.com/","line":132},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":132},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":132},{"title":"Contents","target":"https://saliu.com/content/index.html","line":132},{"title":"Home","target":"https://saliu.com/index.htm","line":132},{"title":"Software","target":"https://saliu.com/infodown.html","line":132},{"title":"Search","target":"https://saliu.com/Search.htm","line":132},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":132},{"title":"The lotto wheels consist of balanced lexicographic indexes or ranks; software to generate best lotto wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":134}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08518772,-0.05378362,-0.00760738,-0.01516751,-0.06486017,0.0460409,-0.0037313,0.00350972,0.03515222,-0.02863315,0.01773826,-0.03344769,0.06327891,0.02003227,-0.02199019,-0.01711976,0.02458676,-0.00308338,-0.07104428,-0.02054822,0.13855888,-0.04071103,-0.04601541,-0.06063661,0.01731407,0.03689306,-0.02095754,-0.02569472,0.00894058,-0.18846627,0.01920768,0.02652358,0.02131186,-0.02874676,-0.06038965,-0.026269,-0.06569599,0.06795267,-0.10098635,0.00314954,-0.00969676,0.02493833,-0.03558167,-0.01230377,0.05353705,-0.08287164,-0.00489662,0.03509793,0.04563836,-0.01105503,-0.04937379,-0.02615544,-0.00951441,0.03664822,0.05206059,0.05151816,0.02281129,0.06474469,0.06202998,0.04108462,0.11170855,0.07129593,-0.20371036,0.04323145,-0.0037654,0.01475981,-0.0146208,-0.04421194,-0.01475206,0.04727786,0.0573469,0.03955438,-0.00251146,0.08262939,0.04419582,-0.03810712,-0.03563394,-0.08535378,-0.0928962,0.02092686,-0.05836799,-0.00037586,0.00368757,-0.00488127,-0.01534214,0.02039537,0.05749812,0.00112569,0.06102017,-0.02547562,0.00873306,-0.03269482,0.04649075,0.05584852,-0.00659586,0.00317089,0.02791303,-0.02626062,0.04140006,0.13658774,0.00827908,0.02283278,-0.00994288,-0.00714504,-0.0031137,0.02027587,0.02489919,-0.03865048,-0.01926366,0.04762795,0.0559647,0.04610812,0.01219717,-0.02604158,-0.0554643,-0.04118596,-0.00756878,-0.02188452,0.01601948,0.0397972,-0.02947453,0.03593773,-0.00148069,-0.01357197,-0.02028495,0.02698995,0.04154249,0.04866016,0.01432887,-0.00340335,0.01389616,0.05277258,-0.11411434,-0.03517796,0.0059182,-0.00448373,0.02082736,-0.03153324,0.00078694,0.03976466,-0.00361781,-0.04925385,0.0410759,-0.07680944,-0.05549617,0.0853162,-0.03439645,-0.02857735,-0.02067033,0.04468639,-0.00056069,-0.00326194,-0.02649958,-0.04784309,-0.00792119,0.04470798,0.12084059,0.07999609,-0.0404254,0.00066163,0.00884865,-0.02749703,-0.05226032,0.13238409,-0.0546865,-0.11993961,-0.01169457,0.07708372,-0.01209996,-0.07244304,-0.02233512,0.03613637,-0.07992696,0.06028219,0.06553065,0.02400742,-0.04848141,-0.02422531,-0.00629319,-0.03755604,0.02336209,-0.06285098,-0.02942974,0.01571921,-0.03298765,-0.06403794,-0.01806247,-0.07348918,0.05329171,0.07016598,-0.00710359,0.04013692,0.01754817,-0.01420157,-0.05640466,-0.0572584,-0.04183295,-0.03216659,0.04344303,-0.0505231,-0.00269148,0.04650389,0.03072895,0.04670724,-0.01078494,-0.00196549,-0.03474722,-0.0379468,0.0480058,0.02412609,-0.08292361,0.0137847,0.03581139,0.08765461,-0.08414479,0.04487829,0.00001448,0.01818695,-0.00969444,0.00466187,-0.00160427,-0.02751197,-0.08064309,-0.18348254,-0.01877976,-0.05293994,0.02169808,-0.00024058,-0.01266521,0.03482828,-0.01609066,0.00743057,0.05419957,0.08940812,-0.08667616,0.00585366,0.02419195,-0.0411286,0.0484314,-0.08433607,-0.04062938,-0.00479788,0.04218383,0.01625764,-0.00815427,-0.02864488,-0.11159613,0.02164619,-0.01877008,0.12633136,0.02451989,0.00248671,-0.0197734,0.09776876,-0.05138416,-0.0173486,-0.01441958,-0.0006597,0.01548751,-0.06718367,-0.0323614,-0.05932546,-0.02065382,-0.06154364,0.01181089,0.02629808,-0.08805047,0.01716997,-0.00299417,0.0102402,0.00119064,-0.00533939,0.06837871,0.00035006,0.02099424,0.01909882,0.03069366,0.03116607,-0.00438376,-0.02822708,-0.00346678,-0.01428236,0.01052181,0.00788138,-0.04100071,0.06793898,-0.03429848,0.04233402,0.0109522,-0.03822678,-0.04604832,0.00880509,-0.01376398,-0.00747242,0.10548334,0.02408054,0.03880766,-0.00162087,0.05445018,0.04442924,-0.02382239,0.03334891,-0.01696164,-0.02962111,-0.04513049,0.04473501,0.07125857,0.05272686,0.04779429,0.05753739,0.03040153,-0.00542707,0.03934795,0.01230417,0.05777356,-0.03390579,0.03349613,0.02101251,0.05422839,-0.25606775,0.05133726,-0.00173125,0.07569285,0.02744477,-0.00752432,0.05492303,0.00379039,0.04063361,-0.02750354,0.05037666,0.05241066,0.01799915,-0.06340615,-0.02772408,-0.03000749,0.07849003,-0.06363172,0.08957503,-0.00040014,0.00214679,-0.00411667,0.23133229,0.01466814,0.00078189,0.00641743,-0.00279968,-0.0154544,-0.01517113,0.04323194,-0.00838851,0.02043874,0.07711243,0.03168543,-0.0384304,0.04805132,-0.03536664,0.00308573,-0.01559248,0.02773956,-0.05413617,0.02003239,-0.04735024,0.01362595,0.10437629,0.02104256,-0.04332479,-0.09831879,0.02930284,0.00757941,-0.05068507,-0.07026287,-0.12284152,-0.01651371,0.00837685,0.00337396,0.0185589,0.02507096,-0.01405861,-0.03369403,-0.00361945,-0.04362183,0.03546594,0.00028446,0.01241385],"last_embed":{"hash":"9vca4t","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"9vca4t","at":1753423479989},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#{1}","lines":[10,15],"size":343,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lotto-wheels.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#By Ion Saliu, ★ _Founder of Lottery Wheeling Mathematics_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06070025,-0.06930305,-0.0240432,-0.03361759,-0.074124,0.05432952,-0.00842195,0.01530108,0.02517912,-0.00543212,-0.00871218,-0.02972762,0.03073673,0.01605766,-0.01952061,-0.00124884,0.02645953,-0.01216218,-0.09248881,-0.039822,0.11337514,-0.04390997,-0.06868647,-0.1022129,0.02011005,0.05334418,-0.02696104,-0.05503501,-0.02790201,-0.24835546,0.02474025,0.04163362,0.03334032,-0.01675338,-0.0893162,0.00710413,-0.04631114,0.10969976,-0.0851374,-0.00777039,-0.01316556,0.02179413,-0.019394,-0.01097612,0.02302908,-0.04615198,-0.03303387,0.02076133,0.01075068,0.02518438,-0.0666791,-0.05665159,-0.00152142,0.0360276,0.02102121,0.01915851,0.02059728,0.0428424,0.05086033,0.05285515,0.09626261,0.07696994,-0.20473188,0.00848473,0.00551805,-0.00946396,-0.03938911,-0.06489339,-0.0197626,0.06149964,0.03028233,0.03599945,-0.00836988,0.04815681,0.08272545,-0.02320446,-0.03976063,-0.08390778,-0.07802353,0.02598806,-0.05081383,-0.00029559,-0.00834864,0.02039603,0.01369701,0.0214503,0.06988593,0.01420358,0.05393049,-0.00892121,0.00907677,0.00375867,0.04312058,0.01577824,-0.00713891,0.00183857,0.01909973,-0.03887574,0.02389116,0.1070094,0.02222666,0.03431923,0.01966205,-0.00691553,0.00612878,-0.00009747,0.01156751,-0.04238058,-0.02094571,0.0243742,0.06331633,0.03020552,0.0424447,-0.02436659,-0.0671986,-0.02802332,0.00283166,-0.00078779,0.00607932,0.04575399,-0.01439385,0.02638615,-0.00652111,-0.03936159,0.02383554,0.06023604,0.00182464,0.08602326,0.03715403,-0.01130162,0.02458079,0.02862466,-0.10692002,-0.027163,-0.00762043,-0.0376727,0.00797273,0.03319636,0.00404339,0.02714758,0.00979418,-0.05235798,0.0536489,-0.06374106,-0.04705605,0.10030092,-0.05151157,-0.01129584,0.02215186,0.01697111,0.02476191,-0.01547643,-0.03631394,-0.04411532,0.00490474,0.01474255,0.1153954,0.10272987,-0.04063928,0.01518596,-0.02121489,-0.02069062,-0.03803453,0.12340771,-0.04214633,-0.10524333,-0.0231789,0.07942549,-0.01975466,-0.06857415,0.01385028,0.01845266,-0.05540721,0.05420684,0.07267261,0.00130583,-0.07146405,-0.00852708,0.01138918,-0.01073466,0.04117345,-0.02602707,-0.03716442,0.01417255,-0.01105003,-0.06551209,0.012404,-0.05067319,0.02768534,0.04597663,-0.0192465,0.02282493,0.0148473,0.03975715,-0.03668875,-0.05184772,-0.02359293,-0.03145717,0.03531003,-0.03488948,0.02727499,0.00345229,0.03153686,-0.00260464,-0.00048181,0.01571621,-0.02402596,-0.05205474,0.0690701,0.04085018,-0.05264977,-0.02756873,0.03453548,0.09686878,-0.05483396,0.06831472,0.01578893,0.01820094,-0.01280607,0.03167281,0.00510449,-0.02063021,-0.08610328,-0.18185785,-0.05465733,-0.01336895,0.03216386,0.00951062,-0.00059435,0.02984118,-0.02166347,0.01727136,0.07267493,0.08411215,-0.02499438,-0.01334554,0.01743063,-0.04485118,0.04136514,-0.12852803,0.00182474,-0.02527622,0.05741004,0.03304024,0.02262131,-0.06291441,-0.09307076,0.01813649,-0.01219882,0.14690702,-0.00415043,-0.00986172,-0.02514474,0.08177304,-0.02789019,-0.02484375,0.03080351,0.03230701,0.02595508,-0.08848353,-0.02466046,-0.03016548,-0.00695203,-0.07260086,0.00619218,0.01961908,-0.1097294,-0.0062573,-0.03341167,0.0078257,-0.03613527,0.00500738,0.04861902,-0.01330523,0.0009076,0.03223705,0.01084743,0.03569422,-0.03728099,-0.06044613,-0.00271103,-0.02008765,0.00632775,0.02977281,-0.03391229,0.07670254,-0.02814933,0.03818797,0.02086877,-0.02625365,-0.03498435,0.01051657,0.01276304,-0.00150728,0.09639232,0.02895796,0.02614485,-0.01361091,0.04752076,0.04006864,-0.00344138,0.03024294,-0.00837049,-0.00113089,-0.05893943,0.02639204,0.05512289,0.02294207,0.02259982,0.07080763,0.0397733,-0.0192945,0.0422701,0.01162005,0.04607476,-0.0284642,0.01338089,0.00851642,0.04143445,-0.25377202,0.05260051,-0.02865989,0.05520089,-0.00153568,-0.00496134,0.06220591,0.02257835,0.04144011,-0.0390591,0.05315785,0.07991292,0.0193295,-0.09910088,0.01039625,-0.02728021,0.0264005,-0.04165811,0.06360745,-0.02190542,0.02994067,-0.0006185,0.23361045,0.00700132,0.0031312,0.00368815,-0.01016065,0.01143786,-0.00578667,0.02741357,-0.03925734,-0.00342326,0.08059311,0.01716966,-0.00923339,0.05311989,-0.0086811,-0.00257377,-0.02146496,0.03615833,-0.05926879,0.02448296,-0.05923115,0.01515185,0.10928511,0.02533148,-0.02736842,-0.09377407,0.04957857,0.03946697,-0.05406421,-0.04664354,-0.10044847,-0.06682579,-0.02740801,0.00910934,0.01735165,0.03374765,-0.02457131,-0.03329187,-0.00549525,-0.03532612,0.05287995,-0.00129917,0.00013283],"last_embed":{"hash":"11uvtta","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"11uvtta","at":1753423480034},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#By Ion Saliu, ★ _Founder of Lottery Wheeling Mathematics_","lines":[18,104],"size":5662,"outlinks":[{"title":"Generate loto wheels inside the median Gauss bell.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":3},{"title":"The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.","target":"https://saliu.com/ScreenImgs/lexico-lottowheels.gif","line":11},{"title":"Dynamic versus Static in Lotto Analysis","target":"https://saliu.com/bbs/messages/919.html","line":16},{"title":"Create lottery wheels as symmetrical lotto wheels in software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":71},{"title":"lotto wheels","target":"https://saliu.com/lotto_wheels.html","line":78},{"title":"Register for the best software to generate well balanced lotto wheels, lottery wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":84}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#By Ion Saliu, ★ _Founder of Lottery Wheeling Mathematics_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05817438,-0.06789723,-0.0246397,-0.03047123,-0.07389748,0.05767158,-0.00712891,0.01595016,0.02551974,-0.00570425,-0.00940845,-0.03140061,0.0295176,0.01619564,-0.01902959,-0.00082467,0.02455775,-0.01134123,-0.09597764,-0.04451116,0.11261612,-0.04649826,-0.06842119,-0.10066029,0.02387171,0.05359127,-0.02620228,-0.05609331,-0.0283816,-0.24946569,0.02479853,0.04156767,0.03257506,-0.01479474,-0.09126923,0.00772008,-0.04945679,0.11191976,-0.08601132,-0.00913187,-0.01090127,0.02075239,-0.01549931,-0.01091531,0.01944871,-0.04476584,-0.03287256,0.02161287,0.01300894,0.0258338,-0.06521175,-0.05714769,-0.00180047,0.03457922,0.02190094,0.01760938,0.02027274,0.04236815,0.0518899,0.05446143,0.09799907,0.07284454,-0.20688739,0.00915621,0.00588245,-0.01242713,-0.04019435,-0.06519008,-0.02066734,0.0623753,0.03082346,0.03299476,-0.00700828,0.04930379,0.08531462,-0.02302696,-0.04027082,-0.08414835,-0.07869734,0.02725499,-0.04901551,-0.00087459,-0.00786736,0.02124394,0.01148463,0.02093976,0.0685026,0.01131701,0.05129624,-0.00862514,0.00937564,0.00366139,0.04071211,0.01555773,-0.00500807,0.00375105,0.01856772,-0.03620111,0.02516458,0.10482433,0.02401185,0.03569807,0.02034887,-0.00690476,0.00669423,0.00166092,0.00980341,-0.04314176,-0.02109475,0.02352767,0.06099381,0.03034523,0.04240321,-0.02207711,-0.06612736,-0.02632994,0.00411736,-0.00081686,0.00598806,0.04675477,-0.013879,0.02816801,-0.00449727,-0.04172395,0.02325124,0.06335617,0.0010896,0.08721295,0.03525405,-0.01168237,0.0233365,0.02709546,-0.10715578,-0.02800713,-0.01280415,-0.03816811,0.00606193,0.03200229,0.00235181,0.02902099,0.00987445,-0.05212948,0.05262935,-0.06298774,-0.04704419,0.09870117,-0.05146417,-0.01187458,0.02205514,0.01667227,0.02337007,-0.01716993,-0.03656872,-0.04578001,0.00546887,0.01362347,0.10998099,0.10219137,-0.03958009,0.01653049,-0.02430862,-0.02187056,-0.04162018,0.12367645,-0.04286763,-0.10025792,-0.02376885,0.07921034,-0.01878058,-0.06720238,0.01512827,0.01696296,-0.05695067,0.05691804,0.0716838,0.00073756,-0.06991512,-0.00951271,0.01069045,-0.0094992,0.04194282,-0.02769703,-0.0388126,0.01489735,-0.01069024,-0.06305667,0.01434936,-0.04864428,0.02841241,0.04686121,-0.01858737,0.02686541,0.01690565,0.03931633,-0.03721533,-0.05196486,-0.02289432,-0.03265052,0.03216785,-0.03341969,0.02696521,0.0040807,0.03086546,-0.00352886,-0.00076402,0.01395208,-0.02090334,-0.05089059,0.07008665,0.04096868,-0.05229466,-0.02973146,0.03386405,0.09242816,-0.05491192,0.066889,0.01718265,0.01725181,-0.01245061,0.02972602,0.0073198,-0.02289147,-0.08778878,-0.18266585,-0.05690867,-0.01235536,0.03065194,0.01076459,-0.00217758,0.03041522,-0.02147684,0.01636265,0.07112832,0.08314424,-0.02264999,-0.01251638,0.01806847,-0.04436836,0.04495757,-0.1289157,0.0044073,-0.02476265,0.05964164,0.03394169,0.0222299,-0.06467585,-0.09201624,0.01825352,-0.00994289,0.1480069,-0.00683335,-0.00987298,-0.02665337,0.08356875,-0.02607973,-0.02490223,0.03603619,0.03090845,0.02378839,-0.09132881,-0.02481811,-0.03009099,-0.00793224,-0.07238909,0.00584399,0.02240222,-0.10916596,-0.00594579,-0.03314523,0.00795445,-0.03768348,0.00409456,0.04534224,-0.00959094,0.00010237,0.03211055,0.00977012,0.03617799,-0.03856854,-0.06099183,-0.00085332,-0.02162755,0.00780665,0.02957998,-0.03185939,0.07487427,-0.02684392,0.03571255,0.02186578,-0.0260462,-0.03513262,0.00972347,0.01389722,-0.00189538,0.09358607,0.03016509,0.02864691,-0.01473231,0.04821334,0.03958938,-0.00238628,0.02881335,-0.00706282,-0.00006461,-0.05876003,0.02563661,0.05293959,0.02090037,0.02301624,0.07031976,0.03863734,-0.01707793,0.04188547,0.01277862,0.04671251,-0.02780196,0.0119257,0.00766304,0.0385772,-0.25482309,0.05617778,-0.03110774,0.05218304,-0.00156118,-0.00399243,0.06097339,0.02247965,0.04335127,-0.03780429,0.05373883,0.08179276,0.01778212,-0.09913544,0.00859607,-0.02618501,0.02458042,-0.04175046,0.06480438,-0.01982725,0.0314137,-0.00186788,0.23460938,0.00427806,0.00430816,0.0054361,-0.01175608,0.01068501,-0.00763931,0.02455346,-0.0371702,-0.00440954,0.08279514,0.01831656,-0.00664492,0.05523546,-0.00802684,-0.00018306,-0.02338855,0.03492961,-0.05840194,0.02390108,-0.05807379,0.01552183,0.11091188,0.02746322,-0.0263694,-0.09202531,0.05171954,0.03805666,-0.05412775,-0.04355591,-0.09979142,-0.06576712,-0.02987063,0.00855051,0.01580153,0.03523443,-0.02438827,-0.03142789,-0.0050879,-0.0351625,0.05129372,-0.00333854,-0.00033963],"last_embed":{"hash":"w0yw9e","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"w0yw9e","at":1753423480172},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#By Ion Saliu, ★ _Founder of Lottery Wheeling Mathematics_#{1}","lines":[20,104],"size":5600,"outlinks":[{"title":"Generate loto wheels inside the median Gauss bell.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.","target":"https://saliu.com/ScreenImgs/lexico-lottowheels.gif","line":9},{"title":"Dynamic versus Static in Lotto Analysis","target":"https://saliu.com/bbs/messages/919.html","line":14},{"title":"Create lottery wheels as symmetrical lotto wheels in software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":69},{"title":"lotto wheels","target":"https://saliu.com/lotto_wheels.html","line":76},{"title":"Register for the best software to generate well balanced lotto wheels, lottery wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":82}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07948875,-0.06267607,-0.00627122,-0.02508828,-0.05628321,0.02672328,0.00504795,-0.00761479,0.01582157,-0.02828353,0.00756171,-0.01124033,0.0498133,0.00384203,-0.00144625,0.01506588,0.0428377,0.01414323,-0.08214563,-0.03749212,0.11086419,-0.03627024,-0.05648223,-0.06893645,0.00352326,0.02106502,-0.04376457,-0.0417655,-0.02063325,-0.2114179,0.03605877,0.02830797,-0.02275288,-0.02521036,-0.0705696,-0.02164537,-0.05883944,0.04567385,-0.09343977,0.00076085,-0.00025285,0.04089943,-0.00728027,-0.00516035,0.04197071,-0.06184581,0.01346256,0.00211739,0.07166953,0.02383285,-0.06376655,-0.01514525,0.03235416,0.03697928,0.02948532,0.02391853,0.0364213,0.09333254,0.05004511,0.04356103,0.06656989,0.09200556,-0.17151086,0.06775129,0.00126651,0.0195719,-0.00017791,-0.07921188,0.0023533,0.06680999,0.04791023,0.05661606,0.0034442,0.08676466,0.05252693,-0.02512981,-0.05616516,-0.08876166,-0.07567493,0.030166,-0.07328073,0.0087118,0.00362786,-0.01015277,0.00013799,0.02416693,0.03185483,0.01481418,0.06886138,-0.05750437,0.00992618,0.02970709,0.07352486,0.05805125,-0.01899884,0.0120849,0.04698869,-0.03387154,0.04132095,0.12294421,-0.0155481,0.01512306,-0.01598594,-0.0209549,0.00616255,-0.0293333,-0.00988005,-0.01743218,-0.02047728,0.04113837,0.06584016,0.00374889,0.07342321,-0.00132309,-0.06484447,-0.02304401,0.00505958,-0.0162157,0.01291861,0.02833951,-0.05369894,0.0022989,0.03362091,0.01801924,-0.00680446,0.00794749,0.04778289,0.0542345,0.01979546,0.02456108,0.03529418,0.04307357,-0.11283098,-0.03614436,0.02404568,-0.03584356,0.01177543,0.00563885,-0.01089867,0.0284182,-0.00857506,-0.04891111,0.07308371,-0.07348416,-0.0572477,0.05467702,-0.03423221,-0.02024749,-0.00724553,-0.00077444,0.00117775,0.01212977,-0.01299569,-0.02510232,0.02983941,0.02790701,0.15730543,0.07784993,-0.054679,0.03609908,-0.04019998,-0.04381734,-0.04148224,0.10927556,-0.03297511,-0.10182189,0.00826202,0.06171859,-0.02854649,-0.07124034,-0.01956729,0.04422824,-0.06316614,0.04093214,0.08377796,0.01638224,-0.0731198,-0.01116974,-0.01618437,-0.03384437,0.01958043,-0.05386651,-0.03552582,0.01609871,-0.01969886,-0.08842816,0.01135975,-0.04139217,0.03325089,0.04891098,-0.02580284,0.02158066,0.0295905,-0.00418006,-0.03273815,-0.03384858,-0.06366223,-0.01673174,0.05428138,-0.05053388,0.01475757,0.02305241,0.05588868,0.03572477,0.01730098,0.01417064,-0.04324123,-0.09007698,0.06005173,0.02138677,-0.06857518,-0.01785892,0.04713659,0.08960739,-0.09596296,0.05089302,0.00790643,0.02827683,-0.03404223,0.01902764,0.00288605,-0.00445244,-0.08575226,-0.1926661,-0.03114224,-0.04357102,0.08100283,0.03230938,-0.03419969,-0.00059331,-0.04217635,0.01751695,0.07659654,0.04846147,-0.07982319,0.00691683,0.0418612,-0.02045486,0.04419927,-0.1041922,-0.02951216,-0.04584286,0.05869045,0.04763913,0.00498078,-0.00346579,-0.11241872,0.03078467,-0.02793144,0.13378675,-0.0087639,0.00430427,-0.02605006,0.08496387,-0.03267644,-0.0054288,-0.00233357,0.03224625,0.02600061,-0.04871342,-0.04203216,-0.00264936,-0.02597547,-0.06823459,0.02269172,0.01049248,-0.1152271,0.02152554,-0.02070219,0.0044269,-0.02871536,0.00251616,0.04853168,-0.00849924,0.04419829,0.02329131,0.00757867,0.05694152,-0.02625753,-0.02057908,0.01410027,-0.01713877,0.01514972,-0.01398714,-0.04419964,0.05733558,-0.06150844,0.05632447,0.00706141,-0.01273651,-0.03787404,0.01509608,-0.00906487,-0.00629381,0.08815141,0.00430452,0.07979079,-0.02994604,0.04576029,0.05087553,-0.00016353,0.00994087,-0.00903583,-0.02577175,-0.02828892,0.04444478,0.10282428,0.01093015,0.0243548,0.07275475,0.02253019,-0.00365412,0.04166345,0.0204403,0.04644549,-0.03765633,0.02776638,0.03267372,0.05983188,-0.23991272,0.02167446,-0.02718052,0.0525481,-0.01408858,-0.02882085,0.03418386,-0.00727432,0.04409893,-0.04941661,0.02116305,0.04029948,0.02927259,-0.05085548,0.00457776,0.00282637,0.07958415,-0.03511326,0.05418039,-0.02245204,0.02556144,-0.00706838,0.2314589,0.00339867,0.01272091,0.004477,-0.03408103,0.00309073,-0.02425202,0.00962857,-0.01799929,0.02369393,0.06385415,-0.01771618,-0.05351377,0.01428626,-0.05093116,-0.00113969,-0.01523039,0.02186893,-0.05876441,0.01096146,-0.04236533,0.00582326,0.09615029,0.02504156,-0.00676171,-0.11381223,0.02347976,0.03993243,-0.06184447,-0.05194014,-0.0969927,-0.03385008,0.0245968,0.03022256,0.01275091,-0.01557331,-0.01436535,-0.04494192,-0.00070689,-0.05638788,0.05374137,-0.0016599,0.00884076],"last_embed":{"hash":"25a9pp","tokens":390}}},"text":null,"length":0,"last_read":{"hash":"25a9pp","at":1753423480308},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","lines":[105,142],"size":3936,"outlinks":[{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":5},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":18},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":20},{"title":"The myth of lotto wheels or abbreviated lotto systems","target":"https://saliu.com/bbs/messages/11.html","line":22},{"title":"WHEEL-632 available as lotto wheeling software","target":"https://saliu.com/bbs/messages/wheel.html","line":24},{"title":"lottery software for players of lotto wheels","target":"https://saliu.com/bbs/messages/857.html","line":25},{"title":"Software to verify lotto wheels","target":"https://saliu.com/check-wheels.html","line":26},{"title":"Check WHEEL and lotto wheels for winners","target":"https://saliu.com/bbs/messages/90.html","line":27},{"title":"_**The Best Lotto Wheels for 18 Numbers, 4 in 6 Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":28},{"title":"Genuine Powerball wheels","target":"https://saliu.com/powerball_wheels.html","line":29},{"title":"Genuine Mega Millions wheels","target":"https://saliu.com/megamillions_wheels.html","line":30},{"title":"Genuine Euromillions wheels","target":"https://saliu.com/euro_millions_wheels.html","line":31},{"title":"Lotto wheels, lottery wheels are lexicographically balanced and randomized.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":33},{"title":"Forums","target":"https://forums.saliu.com/","line":35},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":35},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":35},{"title":"Contents","target":"https://saliu.com/content/index.html","line":35},{"title":"Home","target":"https://saliu.com/index.htm","line":35},{"title":"Software","target":"https://saliu.com/infodown.html","line":35},{"title":"Search","target":"https://saliu.com/Search.htm","line":35},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":35},{"title":"The lotto wheels consist of balanced lexicographic indexes or ranks; software to generate best lotto wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":37}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0649633,-0.07432059,0.01214357,-0.03724209,-0.06547089,0.01763162,0.01528086,-0.02100402,0.02901392,-0.01733691,0.00613679,0.00452468,0.04538228,0.00162887,0.00151375,0.03199286,0.04076242,0.03593997,-0.06631545,-0.01581638,0.06965949,-0.03775985,-0.05805074,-0.06623491,0.01001583,0.06198728,-0.05605698,-0.06618246,-0.01143814,-0.18563449,0.03603087,0.06166377,-0.0359588,-0.00450096,-0.07109996,-0.03465308,-0.08463003,0.05224614,-0.08877271,0.01870122,0.00256338,0.02428516,-0.01010324,0.02152011,0.01383101,-0.05497129,0.02946075,0.00285376,0.0794481,0.01326221,-0.02640832,-0.02946491,0.03310932,0.03893008,0.02343494,0.03161456,0.02240337,0.06610779,0.04121383,0.03994928,0.07449593,0.08546565,-0.15441598,0.06361801,0.01951979,0.02965288,0.00157694,-0.0557365,-0.00305403,0.08984769,0.03030155,0.03591308,-0.00083391,0.05476734,0.04519602,-0.0312473,-0.03511152,-0.09104861,-0.06224582,0.02384391,-0.05350859,-0.00874982,0.01721829,-0.00176716,-0.05003183,0.00304331,0.04581489,-0.00632715,0.06553318,-0.06289726,-0.001927,0.05578015,0.07940388,0.05387735,-0.03004484,0.01988535,0.05580236,-0.01380216,0.03431514,0.15051736,-0.04784973,0.00554862,-0.00416149,-0.02649269,0.02380808,-0.01121711,-0.00948988,-0.01011687,-0.04251138,0.06594739,0.06713685,0.00679621,0.07362156,0.00618113,-0.05986309,-0.04524732,-0.01381776,0.01392294,0.01227192,0.03926258,-0.04065287,-0.02076762,0.00727827,0.00616602,-0.01548304,-0.00217197,0.04575584,0.06750397,-0.01452575,0.03073863,0.03212427,0.01712077,-0.0985492,-0.00204717,0.0281451,-0.0241363,0.03009733,0.02852417,-0.03095989,0.03409602,-0.00939503,-0.06657846,0.04893402,-0.07634916,-0.04332192,0.0649686,-0.02778016,-0.00323645,0.01188457,0.01181348,-0.0018537,0.01588215,-0.04246216,-0.0216529,0.03371378,0.01944265,0.14124714,0.04918122,-0.04498265,0.03569104,-0.04785233,-0.05036269,-0.03680335,0.09980007,-0.04203257,-0.09349624,-0.01800387,0.02348953,-0.01714927,-0.06609822,-0.03273195,0.03804776,-0.08445781,0.01934888,0.11494202,0.03787949,-0.06704236,-0.03014544,-0.01538724,-0.04883184,0.02558408,-0.04370662,-0.03211393,0.01396747,-0.01674461,-0.07832967,-0.01689725,-0.01659755,0.01592284,0.06113369,-0.00335844,0.00791666,6.2e-7,-0.03045576,-0.03814702,-0.04315973,-0.07582768,-0.00742894,0.04388443,-0.06602231,0.0123683,0.01698961,0.03347844,0.07918828,0.02545431,0.01287385,-0.02781998,-0.08465857,0.08487434,0.02835659,-0.0878248,-0.02784946,0.03486744,0.06629525,-0.07635403,0.06140501,0.00186953,0.00445852,-0.05233828,0.02302447,-0.01998622,-0.00138019,-0.09253524,-0.18599057,-0.04230819,-0.01248899,0.05645224,0.0434952,-0.03249548,-0.00434344,-0.02683316,0.03938577,0.07876237,0.05789814,-0.08506329,0.00030547,0.02667768,-0.02975836,0.03658788,-0.06786659,-0.03714771,-0.02779111,0.04722564,0.04974977,-0.00332664,-0.03533386,-0.11936022,0.0426406,-0.01501476,0.12522779,-0.02623386,-0.01312446,-0.00432916,0.06365445,-0.04660679,-0.01727012,0.0123915,0.02706004,0.00529238,-0.06314582,-0.03673063,0.00706778,-0.00198288,-0.07085074,0.0110054,-0.00066127,-0.09312564,0.04049978,0.01393456,0.00970319,-0.03901005,-0.02088539,0.05619439,-0.02948005,0.05710007,0.02741819,0.03612868,0.04564669,-0.03746373,0.0112476,0.01958432,-0.01085032,0.03589451,-0.03468928,-0.01958425,0.04195155,-0.04249321,0.0601089,-0.0074358,-0.02579332,-0.06817317,-0.00460026,-0.02212469,0.00337684,0.06014233,-0.01230853,0.04598092,-0.05589749,0.05156441,0.03710201,0.02391282,0.00920204,-0.01490679,-0.03929406,-0.02583457,0.04826697,0.10592934,0.02083605,0.01938169,0.05883556,0.01486049,-0.01798624,0.06994587,0.02655154,0.03994324,-0.01169839,0.00020447,0.03907725,0.03319098,-0.23878029,0.05107699,-0.00910539,0.04626894,-0.02302671,-0.03811571,0.01633268,0.05984375,0.01843589,-0.04179075,0.06962104,0.04005094,-0.01629436,-0.03475897,0.00514274,-0.03291512,0.10012144,-0.02612271,0.06304287,-0.01540158,0.02872184,0.01636913,0.25954869,0.01578666,0.05042592,-0.00093065,-0.02489724,0.01331412,-0.02094328,0.03278181,-0.00219388,0.00683559,0.08079573,-0.0027048,-0.05980042,-0.00300443,-0.0414216,0.00376476,-0.02076546,0.0398642,-0.06059896,0.00356595,-0.06789261,0.0167417,0.08749586,0.02321764,-0.00292716,-0.100302,0.01860145,0.04768458,-0.0605203,-0.03901964,-0.11624738,-0.04098167,0.00197922,0.02370587,0.00189798,-0.01720197,0.01467385,-0.06472499,-0.01114512,-0.0768249,0.06856246,0.01991618,-0.01282398],"last_embed":{"hash":"rx2fpm","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"rx2fpm","at":1753423480436},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{9}","lines":[120,120],"size":290,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05832326,-0.07706802,0.02368903,-0.02339301,-0.0542316,0.04100035,0.02184129,-0.02007675,0.02908598,-0.02907237,0.01524971,0.02557172,0.02321328,0.00873745,-0.01135323,0.02490597,0.00848619,0.02393234,-0.09371628,-0.04209535,0.08750319,-0.00389376,-0.07665487,-0.05186785,0.0017585,0.02155861,-0.05988487,-0.05238288,-0.04761701,-0.18105227,0.06100683,0.03167524,-0.03389799,-0.0307057,-0.06845452,-0.05670164,-0.06566534,0.09003776,-0.07016756,0.02953387,0.02496556,0.02260166,-0.03614887,-0.00281637,0.02187958,-0.04598804,0.00697033,0.0020552,0.05003669,0.01972911,-0.07213825,-0.01421576,0.03314199,0.00892915,0.00593185,0.015516,0.00988721,0.09599433,0.06052745,0.0403481,0.06287678,0.0859341,-0.13133179,0.04151741,-0.01259529,0.00519779,-0.00607286,-0.04291925,0.01371101,0.11099601,0.0797978,0.04682354,0.00278685,0.08583663,0.07244304,-0.0383427,-0.04161652,-0.09073599,-0.04991816,0.05016574,-0.07376809,-0.03144517,0.01354809,0.00348459,-0.03380828,-0.0160076,0.01963503,-0.03437335,0.05552359,-0.04974987,0.0192721,0.04992893,0.00637331,0.07282055,0.00105597,-0.03839043,0.04680732,-0.05349191,0.05575939,0.14631601,-0.02843098,0.02278109,-0.01277646,0.02069391,-0.0411175,0.00500662,-0.03149908,-0.02770629,-0.02848917,0.04586284,0.07279405,-0.00826528,0.07914787,-0.03265989,-0.04816711,-0.01635989,-0.01161912,0.01289011,0.01197529,0.00224859,-0.03805394,0.01495619,0.01881896,0.01771509,-0.00402307,0.01333353,0.0513994,0.08072713,-0.00063137,0.01112546,0.04775257,0.00012113,-0.12818842,-0.0303448,0.01921709,-0.01389007,0.01463473,-0.01998628,0.0135951,0.01781042,0.01526634,-0.01432142,0.07193977,-0.08876963,-0.05634886,0.07761959,-0.06219833,0.00417835,0.01873648,-0.00020445,0.00670793,-0.01856332,-0.01042266,-0.03750018,0.02292909,-0.00966879,0.17598289,0.02805799,-0.01622666,0.02820333,-0.07363038,-0.03096879,-0.01224353,0.10981232,-0.04221727,-0.07521478,0.00349128,0.04020934,-0.01251312,-0.09371179,-0.03592245,0.03004748,-0.03841012,0.08351213,0.05681947,0.06979973,-0.08258768,-0.03264139,0.00844817,-0.01554814,0.04220954,-0.0374579,-0.04271458,0.03630436,-0.038714,-0.07493602,-0.00373235,-0.03179164,0.03156109,0.04837215,-0.02582411,0.02425353,-0.0103656,-0.02612904,-0.02517973,-0.06930339,-0.03789101,-0.0448525,0.02361743,-0.04257366,0.01141306,0.01586328,0.04947701,0.07844064,0.0428246,0.00771527,0.00810608,-0.09793065,0.05206351,0.02487331,-0.07437927,-0.02589845,0.02957525,0.09000622,-0.07356911,0.06433547,-0.00905822,0.0493807,0.00364654,-0.04182489,0.01398343,-0.02289403,-0.06818531,-0.17601416,-0.05471864,-0.00565264,0.08495894,0.03916028,-0.01688646,-0.01819087,-0.02731917,0.00022582,0.05943372,0.02093502,-0.10566182,0.02287691,0.06207925,-0.02628482,0.02732891,-0.08923584,-0.00949664,-0.03307639,0.02424879,0.02767075,0.02942429,-0.01652756,-0.11237875,0.0516221,-0.01456829,0.13708641,-0.01422772,0.00861988,-0.01989508,0.09083097,-0.04369251,-0.02325453,-0.01378159,0.01737793,0.03240139,-0.05689874,-0.05516299,-0.04401567,-0.01240677,-0.04731623,0.02466535,0.02180878,-0.10107733,-0.00211808,-0.01813968,-0.0274775,0.00378694,0.01735211,0.06681763,-0.02475926,0.0170643,0.01173007,0.01902195,0.05585698,-0.03160637,0.00839668,-0.01764276,-0.01040563,-0.00943108,0.00732866,-0.05269386,0.05412735,-0.06615987,0.03038372,0.0081198,0.00724843,-0.03522399,-0.0102504,-0.00676036,0.01747089,0.09844065,0.02525318,0.04713368,-0.00644529,0.05164747,0.02978273,-0.01202719,0.01340125,-0.01079292,0.00042726,-0.03028929,0.02573217,0.10269448,0.01666684,-0.0092002,0.04992568,0.03804832,-0.00818385,0.03381044,0.02317276,0.05200786,-0.05287742,0.06319173,0.01375649,0.00229242,-0.23922312,0.04910423,-0.01750125,0.0616075,0.02667721,-0.01399679,0.04146021,0.03617987,0.03510845,0.01715488,-0.01454573,0.02523616,0.03420341,-0.04850584,-0.00335605,-0.01430622,0.11384908,-0.02532511,0.05729976,-0.02977156,0.04752721,-0.00920701,0.23337515,0.02192101,0.03919679,0.01197535,-0.01816899,0.030512,-0.05463481,0.02464307,0.00826098,0.00313182,0.03979777,-0.02516823,-0.03553589,-0.02214871,-0.02384586,0.00903126,-0.00190114,0.0178494,-0.06454378,-0.01075599,-0.05523292,-0.00487357,0.10033652,0.03070175,-0.00718255,-0.09293859,-0.0071543,0.03487094,-0.06924161,-0.05506047,-0.10487387,-0.05639667,-0.00594838,0.02624643,0.03229637,0.01583563,0.01379087,-0.02548337,0.00640483,-0.08893678,0.04244079,-0.02242499,-0.01482227],"last_embed":{"hash":"1wzy36m","tokens":88}}},"text":null,"length":0,"last_read":{"hash":"1wzy36m","at":1753423480470},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{10}","lines":[121,121],"size":220,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05162454,-0.06449106,0.0029578,-0.00085632,-0.06042604,0.00855681,0.01954171,-0.00304447,0.02790349,-0.01439284,0.00834107,-0.01180176,0.05263562,0.013679,-0.01490431,0.00296137,0.0485491,0.05066266,-0.05011666,-0.03510192,0.10866727,-0.02839246,-0.09412125,-0.0793926,0.02700031,0.05745428,-0.04858363,-0.06849559,-0.00348105,-0.14166936,0.01125243,0.02883036,-0.0106342,-0.03768175,-0.05385758,-0.0458958,-0.0630103,0.0685777,-0.09855144,0.01956137,0.00950967,0.02984383,-0.02981891,0.01906823,0.0225185,-0.06708468,0.03170793,0.02186561,0.05506868,-0.01037162,-0.04409941,0.01234255,0.00678815,0.03568218,0.01017793,0.03612708,0.03134147,0.08917779,0.02280953,0.01436678,0.06420054,0.07634971,-0.16881207,0.05410639,0.00351264,0.04745669,0.00384291,-0.0168823,0.01709066,0.0710455,0.05535754,0.03540299,0.0117118,0.0729432,0.0545649,-0.02705611,-0.02904941,-0.0758256,-0.05635016,0.00370031,-0.06190821,-0.01037065,0.01769646,0.02603902,-0.04511001,0.0198357,0.03622061,-0.00446476,0.09763175,-0.06783362,0.00006841,0.03499454,0.05432484,0.05973918,0.00458828,-0.02973602,0.0576967,-0.03611817,0.04127537,0.1557593,-0.01706566,0.00160934,0.01259927,-0.04504074,0.00807599,-0.03172215,-0.00455111,-0.03457813,-0.03605854,0.06747491,0.07141387,0.02080428,0.04984261,-0.02390403,-0.0446442,-0.0568769,-0.02563513,-0.01058059,0.01219496,0.00708057,-0.053371,0.02149507,-0.00747747,0.00569564,-0.00571081,0.00629775,0.01192467,0.08301735,-0.00979881,0.00251831,0.04108679,0.03538274,-0.12198053,-0.02037833,0.01556101,-0.03005576,0.03985859,-0.01676501,-0.01741332,0.06344242,-0.02574507,-0.03862224,0.07760114,-0.10490933,-0.04894038,0.0728093,-0.01959637,0.00528712,-0.01310987,0.02653716,-0.00378617,0.01533382,-0.0308223,-0.03502306,0.00761081,0.01079658,0.14301743,0.01761122,-0.03938382,0.02708128,-0.03504201,-0.01418745,-0.0430967,0.13747518,-0.04882073,-0.11015897,-0.01603594,0.05509675,-0.02733686,-0.07074132,-0.03166914,0.03368073,-0.08891367,0.03153262,0.10625714,0.0080755,-0.09605046,-0.02874361,-0.01269513,-0.03609311,0.02804903,-0.05145479,-0.02347647,0.00883161,-0.00488924,-0.05119324,-0.02829235,-0.0466752,0.04495232,0.06611045,-0.0025785,0.02793154,-0.0023179,-0.02588083,-0.06576587,-0.03611757,-0.07628878,-0.03051935,0.05846153,-0.04498398,0.00836473,0.02544782,0.03627458,0.04844866,0.00271546,0.01098773,-0.02326045,-0.06216345,0.04323962,0.04265145,-0.09136655,-0.01672925,0.04434051,0.05019989,-0.08632196,0.03769682,0.01827697,0.04493443,-0.04957996,0.01933695,-0.029201,0.00755568,-0.0847729,-0.18058026,-0.04287089,-0.01969495,0.03712419,0.01813694,-0.01147469,-0.00080351,-0.03712163,-0.00933961,0.07767601,0.08148573,-0.09596188,0.01349694,0.04367965,-0.03965884,0.000151,-0.07104119,-0.02689355,-0.01168747,0.02465413,0.0281376,-0.03959793,0.01105315,-0.12158136,-0.00719872,-0.00074418,0.1316023,-0.02020939,0.01851721,0.00552227,0.05757315,-0.05670293,-0.03442138,-0.01890503,0.02120929,0.03691057,-0.06826948,-0.030873,-0.01104395,-0.00414401,-0.05226694,-0.0108958,0.02459047,-0.07062548,0.00161605,-0.01291417,-0.00393465,-0.00978693,-0.00693032,0.07991128,-0.02426958,0.05340993,0.03427619,0.03174315,0.03544101,0.00080941,-0.02179944,0.00533255,-0.03785617,0.02038159,-0.00222947,-0.05873515,0.0205419,-0.021119,0.07393575,0.02044893,-0.03274451,-0.05651078,-0.0004321,-0.02188748,0.01109997,0.10003936,0.00025365,0.02617875,0.00801324,0.04483385,0.07533133,-0.05163459,0.00384039,0.00537526,-0.04736667,-0.05438628,0.04496813,0.10339584,0.04912152,0.04190307,0.06471859,0.03424579,-0.03444969,0.01530795,0.01583807,0.03828489,-0.03613692,-0.00113971,0.01940168,0.03683516,-0.24813633,0.04601669,-0.00808407,0.0545029,-0.01975922,-0.04324984,0.05019797,0.02586881,0.02022835,-0.02880224,0.05088263,0.01212689,0.00218987,-0.04828678,0.00266272,-0.02939959,0.09698653,-0.04524145,0.07189621,0.03739243,0.01625874,0.04017249,0.24224928,0.01944273,0.04770943,-0.00227229,-0.01705034,0.01781503,-0.01034644,0.05254665,0.01071324,0.02284961,0.04301115,0.00881135,-0.05537003,0.04173215,-0.02345877,0.00467937,0.00408913,0.04154555,-0.06019357,0.00774412,-0.06001638,0.0424824,0.09992953,0.01887988,-0.01278201,-0.11005478,0.02337399,0.02656368,-0.06734528,-0.03050041,-0.1012437,-0.03945581,0.01433048,0.01263154,0.0367598,-0.02041777,0.00816977,-0.0560223,-0.00670562,-0.06302968,0.05037555,0.01013134,-0.00580526],"last_embed":{"hash":"1m22jsz","tokens":82}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423480498},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{12}","lines":[123,123],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{25}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08005293,-0.05655979,-0.02152987,-0.00469398,-0.047742,0.02779984,-0.0324185,0.01384611,0.00834213,-0.01306251,-0.03773219,-0.04928705,0.03733686,0.00329404,0.01053842,-0.01586,0.03594632,0.01605119,-0.06564623,-0.01661661,0.12278065,-0.01864252,-0.04797795,-0.06150206,0.02536033,0.00992398,-0.03688896,-0.05812474,-0.01325753,-0.19817623,0.01729944,0.0153729,-0.00887004,-0.01973826,-0.08807146,0.01097055,-0.05684036,0.08447729,-0.04717806,0.04450376,0.00007888,0.02391809,-0.0334515,0.01205133,0.02118478,-0.05742962,-0.01292843,0.00216367,0.01895528,-0.00233867,-0.09604262,-0.00897236,0.00578554,0.02914086,0.01871524,0.04509763,0.03212716,0.10418794,0.02335823,0.04803078,0.08357465,0.08100435,-0.21759519,0.03172758,0.01062425,-0.00244716,-0.04546078,-0.04392008,-0.00209781,0.06161343,0.05462509,0.043879,0.00564421,0.07188671,0.06091214,-0.04187595,-0.04568046,-0.08879587,-0.0508504,0.00588788,-0.06509954,-0.0022137,-0.0090742,-0.00088241,0.01138712,0.02028387,0.03146429,0.02948421,0.05565195,-0.04261697,0.0091773,-0.00949681,0.05997297,0.06020089,-0.00743999,-0.00137818,0.00354555,-0.03568349,0.02086745,0.1397792,0.00413216,0.0274936,0.02250594,0.00781832,0.01574223,0.01074047,0.00961441,-0.00762521,-0.01830048,0.03902421,0.07052195,0.0059307,0.02870661,-0.03110351,-0.07344257,-0.05828555,0.00754238,-0.02751901,-0.00275229,0.0143474,-0.05677868,0.03252423,0.01979212,-0.00628001,0.00623767,0.00474756,0.03862247,0.05866968,0.03310378,0.02549042,0.01687055,0.07272073,-0.1304844,-0.03786203,-0.01514052,-0.01955109,0.00398843,-0.01636915,0.02517625,0.05198888,0.01030153,-0.07307801,0.04730204,-0.07407476,-0.06753729,0.08902133,-0.02733567,0.00633011,0.02458126,0.03050051,-0.02133785,0.01301458,0.00678507,-0.03769069,0.00341817,0.039332,0.1460842,0.07754724,-0.02948778,0.03765,0.00713637,-0.04740806,-0.03399867,0.1408578,-0.05265092,-0.1061964,-0.016118,0.08822903,-0.01533543,-0.0876401,0.0100052,0.00499933,-0.0502175,0.06692059,0.05505004,0.01584711,-0.07529371,-0.02891781,-0.01564781,-0.01791226,0.00788131,-0.05772348,-0.0450191,0.0422989,-0.0209569,-0.0808794,-0.0192774,-0.06678415,0.0495485,0.04576246,-0.02154568,-0.02815005,0.00041937,0.00097569,-0.04863646,-0.03229878,-0.02732575,-0.04328961,0.05250487,-0.01680357,-0.00994983,0.03527042,0.01135816,0.02933692,-0.00580407,0.02217377,-0.04747196,-0.0681681,0.03231832,0.01746859,-0.03527803,0.02929162,0.04799432,0.07092085,-0.07796701,0.06168406,0.02392723,0.03685901,-0.02752039,0.01574179,0.01019834,-0.01409047,-0.0904744,-0.1895238,-0.03484716,-0.05487788,0.0154796,0.02019562,-0.02120238,0.03231125,-0.035045,0.00708529,0.05990344,0.08867066,-0.06676831,0.01033493,0.01266913,-0.01810794,0.03102015,-0.07499043,-0.02640204,0.00766826,0.03626277,0.01649202,-0.01348911,0.0047981,-0.10790477,0.00979373,-0.01824299,0.13427617,-0.00740935,0.00195,-0.00221321,0.0908613,-0.04279196,-0.03466368,-0.02198356,0.05289238,0.01943665,-0.10497897,-0.02530537,-0.05864423,-0.00426554,-0.06478508,0.02823406,0.02412817,-0.08464412,-0.02375541,-0.0214425,0.00611306,-0.03690781,-0.00352953,0.04963166,-0.00259932,0.02943639,0.02864002,0.03978413,0.05144278,0.00110765,-0.03442975,-0.01453751,-0.03636805,-0.02528705,0.00034438,-0.04016563,0.0795866,-0.03413449,0.04110784,0.01069809,-0.01840873,-0.04918458,-0.01015112,0.00514133,-0.01327822,0.11381284,0.00857688,0.04492503,0.00930377,0.02732705,0.01473277,-0.02642849,0.02394933,-0.00156717,-0.01831834,-0.06848057,0.05536112,0.07177258,0.05576171,0.03909202,0.04993672,-0.00403129,-0.00060948,0.04542934,0.02701235,0.05941593,-0.03943402,0.02159137,0.03884876,0.0483001,-0.25834641,0.0461326,-0.02722065,0.06498937,0.01414301,0.00229599,0.08228707,-0.01627571,0.01447392,-0.01923727,0.06682558,0.05271307,-0.00084732,-0.06851368,-0.04396491,-0.0088852,0.06466228,-0.05521026,0.0687753,-0.00602931,0.01085273,0.00079798,0.23000249,0.03796905,0.00818523,-0.01647866,0.00029743,0.00590767,-0.00820132,0.0381252,-0.01381787,0.01432306,0.05813034,0.02904681,-0.03137016,0.06532999,-0.02575285,-0.02322574,0.01975427,0.01468971,-0.06025028,0.03059192,-0.02765838,0.03151546,0.1068127,0.01815039,-0.01936609,-0.08120002,0.03169333,0.00433472,-0.05324849,-0.05649814,-0.10379915,-0.00501409,0.00820516,0.02289719,0.02848195,0.01039282,0.01087444,-0.03869436,0.01742989,-0.02145418,0.03130297,-0.01640484,0.01283825],"last_embed":{"hash":"1bq3jyo","tokens":298}}},"text":null,"length":0,"last_read":{"hash":"1bq3jyo","at":1753423480531},"key":"notes/saliu/Lexicographical Lotto Wheels, Lottery Wheels Software.md#Lexicographical Lotto Wheels, Lottery Wheels Software#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{25}","lines":[137,142],"size":659,"outlinks":[{"title":"Lotto wheels, lottery wheels are lexicographically balanced and randomized.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Software","target":"https://saliu.com/infodown.html","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"The lotto wheels consist of balanced lexicographic indexes or ranks; software to generate best lotto wheels.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":5}],"class_name":"SmartBlock"},
