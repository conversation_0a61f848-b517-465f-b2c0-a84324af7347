"smart_sources:讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md": {"path":"讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md","last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664"}}},"last_read":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664","at":1750403581161},"class_name":"SmartSource","last_import":{"mtime":1750381043016,"size":20858,"at":1750384454497,"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664"},"blocks":{"#":[1,506]},"outlinks":[],"key":"讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md"},
"smart_blocks:讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664","at":1750403581165},"key":"讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md#","lines":[1,506],"size":10821,"outlinks":[],"class_name":"SmartBlock"},"smart_blocks:讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664","at":1750403581165},"key":"讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md#","lines":[1,506],"size":10821,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"d38b2a94cd0da74a08bf4f7451336738c542e53ad5ce0f0bdf831b4a8e4a7664","at":1750403581165},"key":"讓 AI 更懂你的程式碼：Python DocString 撰寫全攻略 - Calpa 的煉金工房.md#","lines":[1,506],"size":10821,"outlinks":[],"class_name":"SmartBlock"},
