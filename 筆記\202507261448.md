# src/WonderGridEngine.jl

  

module WonderGridEngine

  

using ..SaliuLottery: Drawing, GameConfiguration

using ..DataManager: GAME_CONFIGURATIONS

using ..SkipAnalyzer: calculate_skip_values, calculate_ffg

using ..FrequencyAnalyzer: calculate_number_frequencies, calculate_pairing_frequencies

using Printf

using Dates

using Statistics

  

export WonderGridConfig, WonderGridResult, WonderGridStrategy,

       execute_wonder_grid_strategy, calculate_pair_frequencies,

       select_key_number, generate_wonder_combinations,

       WonderGridError, PairStatistics, analyze_pair_trends

  

# 錯誤處理

struct WonderGridError <: Exception

    message::String

    code::Symbol

end

  

Base.showerror(io::IO, e::WonderGridError) = print(io, "WonderGridError [$(e.code)]: $(e.message)")

  

# 配對統計數據結構

struct PairStatistics

    pair::Tuple{Int,Int}

    frequency::Int

    last_occurrence::Int

    skip_values::Vector{Int}

    avg_skip::Float64

    trend_score::Float64

    efficiency_rating::Float64

    function PairStatistics(pair, frequency, last_occurrence, skip_values)

        avg_skip = isempty(skip_values) ? 0.0 : mean(skip_values)

        trend_score = calculate_trend_score(skip_values)

        efficiency_rating = calculate_efficiency_rating(frequency, avg_skip, trend_score)

        new(pair, frequency, last_occurrence, skip_values,

            avg_skip, trend_score, efficiency_rating)

    end

end

  

# 配置系統

struct WonderGridConfig

    analysis_range::Int           # 分析範圍（建議為最大號碼的3倍）

    top_pair_percentage::Float64  # 頂級配對百分比（預設 25%）

    key_number_strategy::Symbol   # 關鍵號碼選擇策略 (:ffg, :skip, :frequency)

    combination_limit::Int        # 組合數量限制

    enable_purge::Bool           # 啟用 Purge 過濾

    enable_lie::Bool             # 啟用 LIE 消除

    game_type::Symbol            # 遊戲類型

    pair_analysis_depth::Int     # 配對分析深度

    trend_weight::Float64        # 趨勢權重

    function WonderGridConfig(;

        analysis_range::Int = 150,

        top_pair_percentage::Float64 = 0.25,

        key_number_strategy::Symbol = :ffg,

        combination_limit::Int = 1000,

        enable_purge::Bool = true,

        enable_lie::Bool = false,

        game_type::Symbol = :Lotto6_49,

        pair_analysis_depth::Int = 100,

        trend_weight::Float64 = 0.3

    )

        # 驗證參數

        if analysis_range <= 0

            throw(WonderGridError("分析範圍必須大於 0", :invalid_range))

        end

        if !(0.0 < top_pair_percentage <= 1.0)

            throw(WonderGridError("配對百分比必須在 0 到 1 之間", :invalid_percentage))

        end

        if !(key_number_strategy in [:ffg, :skip, :frequency])

            throw(WonderGridError("無效的關鍵號碼策略", :invalid_strategy))

        end

        if combination_limit <= 0

            throw(WonderGridError("組合限制必須大於 0", :invalid_limit))

        end

        if !(game_type in keys(GAME_CONFIGURATIONS))

            throw(WonderGridError("不支援的遊戲類型: $game_type", :invalid_game_type))

        end

        if pair_analysis_depth <= 0

            throw(WonderGridError("配對分析深度必須大於 0", :invalid_depth))

        end

        if !(0.0 <= trend_weight <= 1.0)

            throw(WonderGridError("趨勢權重必須在 0 到 1 之間", :invalid_weight))

        end

        new(analysis_range, top_pair_percentage, key_number_strategy,

            combination_limit, enable_purge, enable_lie, game_type,

            pair_analysis_depth, trend_weight)

    end

end

  

# 結果結構

struct WonderGridResult

    key_number::Int

    top_pairs::Vector{Int}

    combinations::Vector{Vector{Int}}

    efficiency_metrics::Dict{String, Float64}

    generation_time::Float64

    config::WonderGridConfig

    pair_statistics::Dict{Tuple{Int,Int}, PairStatistics}

    function WonderGridResult(key_number, top_pairs, combinations,

                            efficiency_metrics, generation_time, config, pair_statistics)

        if key_number <= 0

            throw(WonderGridError("關鍵號碼無效", :invalid_key_number))

        end

        if isempty(top_pairs)

            throw(WonderGridError("頂級配對不能為空", :empty_pairs))

        end

        if isempty(combinations)

            throw(WonderGridError("組合不能為空", :empty_combinations))

        end

        new(key_number, top_pairs, combinations, efficiency_metrics,

            generation_time, config, pair_statistics)

    end

end

  

# 策略結構

struct WonderGridStrategy

    key_number::Int

    top_pairs::Vector{Int}

    pair_frequencies::Dict{Tuple{Int,Int}, Int}

    analysis_range::Int

    created_at::DateTime

    function WonderGridStrategy(key_number, top_pairs, pair_frequencies, analysis_range)

        new(key_number, top_pairs, pair_frequencies, analysis_range, now())

    end

end

  

# 核心介面定義

  

"""

執行完整的 Wonder Grid 策略

  

# Arguments

- `drawings::Vector{Drawing}`: 歷史開獎數據

- `config::WonderGridConfig`: 策略配置

  

# Returns

- `WonderGridResult`: 策略執行結果

"""

function execute_wonder_grid_strategy(

    drawings::Vector{Drawing},

    config::WonderGridConfig

)::WonderGridResult

    start_time = time()

    try

        # 驗證輸入數據

        if isempty(drawings)

            throw(WonderGridError("開獎數據不能為空", :empty_data))

        end

        # 檢查遊戲類型一致性

        game_types = unique([d.game_type for d in drawings])

        if length(game_types) > 1

            throw(WonderGridError("數據包含多種遊戲類型", :mixed_game_types))

        end

        if game_types[1] != config.game_type

            throw(WonderGridError("數據遊戲類型與配置不符", :game_type_mismatch))

        end

        println("開始執行 Wonder Grid 策略...")

        println("配置: 分析範圍=$(config.analysis_range), 配對百分比=$(config.top_pair_percentage)")

        # 步驟 1: 計算配對頻率和統計

        println("步驟 1: 計算配對頻率和統計...")

        pair_statistics = calculate_advanced_pair_statistics(drawings, config)

        # 步驟 2: 選擇關鍵號碼

        println("步驟 2: 選擇關鍵號碼...")

        key_number = select_key_number(drawings, config.key_number_strategy)

        # 步驟 3: 篩選頂級配對

        println("步驟 3: 篩選頂級配對...")

        top_pairs = select_top_pairs_advanced(pair_statistics, key_number, config)

        # 步驟 4: 生成組合

        println("步驟 4: 生成 Wonder Grid 組合...")

        combinations = generate_wonder_combinations(key_number, top_pairs, config)

        # 步驟 5: 計算效率指標

        println("步驟 5: 計算效率指標...")

        efficiency_metrics = calculate_efficiency_metrics(combinations, drawings, config, pair_statistics)

        generation_time = time() - start_time

        result = WonderGridResult(

            key_number, top_pairs, combinations,

            efficiency_metrics, generation_time, config, pair_statistics

        )

        println("Wonder Grid 策略執行完成!")

        println("關鍵號碼: $(key_number)")

        println("頂級配對數量: $(length(top_pairs))")

        println("生成組合數量: $(length(combinations))")

        println("執行時間: $(round(generation_time, digits=2)) 秒")

        return result

    catch e

        if isa(e, WonderGridError)

            rethrow(e)

        else

            throw(WonderGridError("策略執行失敗: $(string(e))", :execution_failed))

        end

    end

end

  

"""

計算進階配對統計

"""

function calculate_advanced_pair_statistics(

    drawings::Vector{Drawing},

    config::WonderGridConfig

)::Dict{Tuple{Int,Int}, PairStatistics}

    if isempty(drawings)

        throw(WonderGridError("開獎數據不能為空", :empty_data))

    end

    # 限制分析範圍

    analysis_drawings = length(drawings) > config.analysis_range ?

                       drawings[1:config.analysis_range] : drawings

    pair_stats = Dict{Tuple{Int,Int}, PairStatistics}()

    game_config = GAME_CONFIGURATIONS[config.game_type]

    println("分析 $(length(analysis_drawings)) 期開獎數據...")

    # 初始化配對追蹤

    pair_occurrences = Dict{Tuple{Int,Int}, Vector{Int}}()

    # 遍歷開獎數據

    for (draw_index, drawing) in enumerate(analysis_drawings)

        numbers = drawing.numbers

        # 生成所有配對

        for i in 1:length(numbers)

            for j in (i+1):length(numbers)

                pair = (min(numbers[i], numbers[j]), max(numbers[i], numbers[j]))

                if !haskey(pair_occurrences, pair)

                    pair_occurrences[pair] = Int[]

                end

                push!(pair_occurrences[pair], draw_index)

            end

        end

    end

    println("找到 $(length(pair_occurrences)) 個不同配對")

    # 計算每個配對的統計數據

    for (pair, occurrences) in pair_occurrences

        frequency = length(occurrences)

        last_occurrence = minimum(occurrences)  # 最近出現（索引越小越近）

        # 計算跳躍值

        skip_values = Int[]

        if length(occurrences) > 1

            for i in 2:length(occurrences)

                skip = occurrences[i-1] - occurrences[i] - 1

                if skip >= 0

                    push!(skip_values, skip)

                end

            end

        end

        # 添加當前跳躍值（距離最後一次出現）

        current_skip = last_occurrence - 1

        if current_skip >= 0

            push!(skip_values, current_skip)

        end

        pair_stats[pair] = PairStatistics(pair, frequency, last_occurrence, skip_values)

    end

    println("完成配對統計計算")

    return pair_stats

end

  

"""

計算號碼配對頻率（簡化版本，向後相容）

"""

function calculate_pair_frequencies(

    drawings::Vector{Drawing},

    range::Int

)::Dict{Tuple{Int,Int}, Int}

    if isempty(drawings)

        throw(WonderGridError("開獎數據不能為空", :empty_data))

    end

    # 使用現有的配對頻率分析功能

    return calculate_pairing_frequencies(drawings)

end

  

"""

進階頂級配對選擇

"""

function select_top_pairs_advanced(

    pair_statistics::Dict{Tuple{Int,Int}, PairStatistics},

    key_number::Int,

    config::WonderGridConfig

)::Vector{Int}

    # 找出包含關鍵號碼的配對

    key_pairs = filter(((pair, stats),) -> key_number in stats.pair, pair_statistics)

    if isempty(key_pairs)

        throw(WonderGridError("找不到包含關鍵號碼的配對", :no_key_pairs))

    end

    println("找到 $(length(key_pairs)) 個包含關鍵號碼 $key_number 的配對")

    # 按效率評級排序

    sorted_pairs = sort(collect(key_pairs), by = x -> x[2].efficiency_rating, rev = true)

    # 選擇頂級配對

    top_count = max(1, Int(round(length(sorted_pairs) * config.top_pair_percentage)))

    top_pairs = Int[]

    println("選擇前 $top_count 個頂級配對:")

    for i in 1:min(top_count, length(sorted_pairs))

        (pair, stats) = sorted_pairs[i]

        println("  配對 $pair: 頻率=$(stats.frequency), 效率評級=$(round(stats.efficiency_rating, digits=3))")

        for num in pair

            if num != key_number && !(num in top_pairs)

                push!(top_pairs, num)

            end

        end

    end

    return sort(top_pairs)

end

  

"""

分析配對趨勢

"""

function analyze_pair_trends(pair_statistics::Dict{Tuple{Int,Int}, PairStatistics})::Dict{String, Float64}

    trends = Dict{String, Float64}()

    if isempty(pair_statistics)

        return trends

    end

    all_frequencies = [stats.frequency for stats in values(pair_statistics)]

    all_avg_skips = [stats.avg_skip for stats in values(pair_statistics)]

    all_trend_scores = [stats.trend_score for stats in values(pair_statistics)]

    trends["avg_frequency"] = mean(all_frequencies)

    trends["max_frequency"] = maximum(all_frequencies)

    trends["min_frequency"] = minimum(all_frequencies)

    trends["avg_skip"] = mean(all_avg_skips)

    trends["avg_trend_score"] = mean(all_trend_scores)

    trends["total_pairs"] = Float64(length(pair_statistics))

    return trends

end

  

# 輔助函數

  

function calculate_trend_score(skip_values::Vector{Int})::Float64

    if length(skip_values) < 2

        return 0.0

    end

    # 計算跳躍值的趨勢（遞減趨勢得分更高）

    trend = 0.0

    for i in 2:length(skip_values)

        if skip_values[i-1] > skip_values[i]

            trend += 1.0

        elseif skip_values[i-1] < skip_values[i]

            trend -= 0.5

        end

    end

    return trend / (length(skip_values) - 1)

end

  

function calculate_efficiency_rating(frequency::Int, avg_skip::Float64, trend_score::Float64)::Float64

    # 綜合評級：頻率權重 0.5，跳躍權重 0.3，趨勢權重 0.2

    freq_score = frequency / 10.0  # 標準化頻率分數

    skip_score = avg_skip > 0 ? 1.0 / avg_skip : 0.0  # 跳躍值越小越好

    trend_score_normalized = (trend_score + 1.0) / 2.0  # 標準化到 0-1

    return 0.5 * freq_score + 0.3 * skip_score + 0.2 * trend_score_normalized

end

  

function select_top_pairs(pair_frequencies::Dict{Tuple{Int,Int}, Int},

                         key_number::Int, percentage::Float64)::Vector{Int}

    # 向後相容的簡化版本

    key_pairs = filter(((pair, freq),) -> key_number in pair, pair_frequencies)

    if isempty(key_pairs)

        throw(WonderGridError("找不到包含關鍵號碼的配對", :no_key_pairs))

    end

    sorted_pairs = sort(collect(key_pairs), by = x -> x[2], rev = true)

    top_count = max(1, Int(round(length(sorted_pairs) * percentage)))

    top_pairs = Int[]

    for (pair, freq) in sorted_pairs[1:top_count]

        for num in pair

            if num != key_number && !(num in top_pairs)

                push!(top_pairs, num)

            end

        end

    end

    return sort(top_pairs)

end

  

function select_key_number(

    drawings::Vector{Drawing},

    strategy::Symbol

)::Int

    if isempty(drawings)

        throw(WonderGridError("開獎數據不能為空", :empty_data))

    end

    if strategy == :ffg

        return select_key_number_ffg(drawings)

    elseif strategy == :skip

        return select_key_number_skip(drawings)

    elseif strategy == :frequency

        return select_key_number_frequency(drawings)

    else

        throw(WonderGridError("未知的關鍵號碼選擇策略: $strategy", :unknown_strategy))

    end

end

  

function generate_wonder_combinations(

    key_number::Int,

    top_pairs::Vector{Int},

    config::WonderGridConfig

)::Vector{Vector{Int}}

    if key_number <= 0

        throw(WonderGridError("關鍵號碼無效", :invalid_key_number))

    end

    if isempty(top_pairs)

        throw(WonderGridError("頂級配對不能為空", :empty_pairs))

    end

    # 基礎實現 - 後續任務會完善

    combinations = Vector{Vector{Int}}()

    game_config = GAME_CONFIGURATIONS[config.game_type]

    # 簡單的組合生成邏輯

    for i in 1:min(config.combination_limit, 100)  # 暫時限制

        combo = [key_number]

        available_numbers = copy(top_pairs)

        while length(combo) < game_config.numbers_per_combination

            if !isempty(available_numbers)

                next_num = rand(available_numbers)

                if !(next_num in combo)

                    push!(combo, next_num)

                    filter!(x -> x != next_num, available_numbers)

                end

            else

                break

            end

        end

        if length(combo) == game_config.numbers_per_combination

            sort!(combo)

            if !(combo in combinations)

                push!(combinations, combo)

            end

        end

    end

    return combinations

end

  

function select_key_number_ffg(drawings::Vector{Drawing})::Int

    skip_values = calculate_skip_values(drawings)

    ffg_scores = Dict{Int, Float64}()

    for (number, skips) in skip_values

        if !isempty(skips)

            ffg_scores[number] = calculate_ffg(length(skips))

        end

    end

    if isempty(ffg_scores)

        throw(WonderGridError("無法計算 FFG 分數", :ffg_calculation_failed))

    end

    return argmax(ffg_scores)

end

  

function select_key_number_skip(drawings::Vector{Drawing})::Int

    skip_values = calculate_skip_values(drawings)

    avg_skips = Dict{Int, Float64}()

    for (number, skips) in skip_values

        if !isempty(skips)

            avg_skips[number] = sum(skips) / length(skips)

        end

    end

    if isempty(avg_skips)

        throw(WonderGridError("無法計算跳躍值", :skip_calculation_failed))

    end

    return argmin(avg_skips)

end

  

function select_key_number_frequency(drawings::Vector{Drawing})::Int

    frequencies = calculate_number_frequencies(drawings)

    if isempty(frequencies)

        throw(WonderGridError("無法計算頻率", :frequency_calculation_failed))

    end

    return argmax(frequencies)

end

  

function calculate_efficiency_metrics(combinations::Vector{Vector{Int}},

                                    drawings::Vector{Drawing},

                                    config::WonderGridConfig,

                                    pair_statistics::Dict{Tuple{Int,Int}, PairStatistics})::Dict{String, Float64}

    metrics = Dict{String, Float64}()

    metrics["combination_count"] = Float64(length(combinations))

    metrics["unique_numbers"] = Float64(length(unique(vcat(combinations...))))

    metrics["coverage_ratio"] = metrics["unique_numbers"] /

                               GAME_CONFIGURATIONS[config.game_type].max_number

    # 配對統計指標

    if !isempty(pair_statistics)

        all_frequencies = [stats.frequency for stats in values(pair_statistics)]

        metrics["avg_pair_frequency"] = mean(all_frequencies)

        metrics["max_pair_frequency"] = maximum(all_frequencies)

        metrics["total_pairs_analyzed"] = Float64(length(pair_statistics))

    end

    # 基本效率指標

    metrics["generation_efficiency"] = 1.0

    metrics["filter_efficiency"] = 1.0

    return metrics

end

  
  

end # module WonderGridEngine