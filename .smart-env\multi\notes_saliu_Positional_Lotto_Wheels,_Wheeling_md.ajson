
"smart_sources:notes/saliu/Positional Lotto Wheels, Wheeling.md": {"path":"notes/saliu/Positional Lotto Wheels, Wheeling.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"15spwpw","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363612595,"size":14873,"at":1753423416500,"hash":"15spwpw"},"blocks":{"#---frontmatter---":[1,6],"#Positional Lotto Wheels, Wheeling":[8,131],"#Positional Lotto Wheels, Wheeling#{1}":[10,17],"#Positional Lotto Wheels, Wheeling#{2}":[18,19],"#Positional Lotto Wheels, Wheeling#{3}":[20,27],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>":[28,56],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{1}":[30,47],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{2}":[48,48],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{3}":[49,49],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{4}":[50,50],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{5}":[51,51],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{6}":[52,52],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{7}":[53,53],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{8}":[54,54],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{9}":[55,56],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>":[57,80],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{1}":[59,59],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{2}":[60,60],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{3}":[61,62],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{4}":[63,80],"#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>":[81,101],"#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>#{1}":[83,101],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)":[102,131],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}":[104,105],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{2}":[106,106],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{3}":[107,107],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{4}":[108,108],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{5}":[109,109],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{6}":[110,110],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{7}":[111,111],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{8}":[112,112],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{9}":[113,113],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{10}":[114,114],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{11}":[115,115],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{12}":[116,116],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{13}":[117,117],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{14}":[118,118],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{15}":[119,119],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{16}":[120,120],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{17}":[121,121],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{18}":[122,122],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{19}":[123,123],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{20}":[124,125],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{21}":[126,131]},"outlinks":[{"title":"_**Positional Lotto Wheels from Groups of Numbers**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/jsjtCP6Pszc","line":18},{"title":"The lottery skip software generates combinations from positional systems.","target":"https://saliu.com/ScreenImgs/skip-systems-lotto.gif","line":46},{"title":"Lottery utility software has a function to wheel in combinations that satisfy minimum guarantees.","target":"https://saliu.com/ScreenImgs/positional-wheel-utility.gif","line":67},{"title":"Special lottery wheeling software creates efficient lotto wheels from input files.","target":"https://saliu.com/ScreenImgs/positional-wheel-program.gif","line":73},{"title":"The best lotto wheels are created on-the-fly by purging files generated from positional systems.","target":"https://saliu.com/ScreenImgs/on-the-fly-wheeling.gif","line":100},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":102},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":106},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":108},{"title":"_**Create, Make Lotto Wheels in Lottery Wheeling Software or Manually**_","target":"https://saliu.com/lottowheel.html","line":110},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":111},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":113},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":115},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":116},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":117},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":118},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":119},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":120},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>**_","target":"https://saliu.com/reverse-strategy.html","line":121},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":122},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":123},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":124},{"title":"Lottery wheeling master teaches how to create best positional lotto wheel from in-position systems.","target":"https://saliu.com/HLINE.gif","line":126},{"title":"Forums","target":"https://forums.saliu.com/","line":128},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":128},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":128},{"title":"Contents","target":"https://saliu.com/content/index.html","line":128},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":128},{"title":"Home","target":"https://saliu.com/index.htm","line":128},{"title":"Search","target":"https://saliu.com/Search.htm","line":128},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":128},{"title":"You need a little patience if you want to generate positional lottery wheels with Ion Saliu software.","target":"https://saliu.com/HLINE.gif","line":130}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["positional lotto wheels","reduced","lotto","systems","lottery wheel","software","position-by-position","in-position","real winnings","analyses"],"source":"https://saliu.com/positional-lotto-wheels.html","author":"Ion Saliu"}},
"smart_sources:notes/saliu/Positional Lotto Wheels, Wheeling.md": {"path":"notes/saliu/Positional Lotto Wheels, Wheeling.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06456257,-0.04856928,-0.00407485,-0.00392695,-0.04709943,0.07857457,0.01753738,0.02980296,0.07395195,-0.01141158,0.00448193,0.021104,0.02621958,0.00315219,-0.03291168,-0.06365193,0.00431101,0.03153033,-0.05456256,-0.01578934,0.04118317,-0.07760142,-0.0686642,-0.06212484,0.0228239,0.00117864,-0.04426624,-0.07968868,-0.05164681,-0.24092835,-0.0038495,0.04180123,-0.00588703,-0.02065031,-0.07309397,-0.04321319,-0.06248116,0.04730095,-0.06211387,0.00399929,0.02104335,0.06817169,0.00246973,0.0037123,0.03084395,-0.02202517,0.01104685,-0.0102883,-0.00090556,-0.01331374,-0.04851917,-0.01381929,0.01303198,0.04729476,0.04826649,0.0221986,0.00840265,0.0768193,0.03887678,0.00145716,0.08529025,0.03453478,-0.19344875,0.04604542,0.0017581,-0.00583326,0.0179322,-0.04280439,-0.01637989,0.04107509,0.08111632,0.02395442,-0.02222608,0.05010619,0.04806957,-0.03967538,-0.00448107,-0.07220341,-0.04620392,-0.02790875,0.02415319,-0.04206164,-0.01572447,-0.04905915,0.01053769,0.02120517,0.05480329,0.00136999,0.05010318,-0.06644575,0.0484429,0.04992764,0.05180892,0.03847094,-0.00140634,-0.00518112,0.03523635,0.03786807,0.001051,0.09669802,-0.00140993,0.07548203,-0.00037074,0.00333068,0.02182372,-0.02127561,-0.0314647,-0.01325082,-0.05859023,0.04590957,0.09107766,0.05623956,0.03536978,-0.01850673,-0.0482973,0.00936692,0.03958923,0.04148874,0.03163027,0.00740344,-0.04619215,0.04771112,0.05229741,0.01933158,0.00741713,0.01658442,0.04323166,0.06418941,0.01946864,0.02330356,0.05336441,-0.01232992,-0.11482886,-0.03403721,-0.01636609,-0.02836612,0.00431683,0.02141365,0.0286455,0.03217394,-0.01150482,-0.11387412,0.07143163,-0.10743058,-0.06239639,0.07468446,-0.03371057,-0.00463908,-0.00620867,0.01621348,-0.03182292,0.00660104,-0.04874281,-0.05238899,-0.00860916,0.00299535,0.05177645,0.06346086,-0.06213232,0.00148534,-0.05645701,-0.04389532,-0.07889304,0.11168852,-0.03197057,-0.12130548,-0.02799041,0.04230628,-0.00919237,-0.05323437,-0.01849565,-0.00375062,-0.07104725,0.0234871,0.07723305,-0.00040024,-0.0739067,-0.00115815,-0.05077901,0.01375364,0.01871302,-0.02761906,-0.03326033,-0.00223953,-0.02318757,-0.00522531,-0.00164324,0.00453637,0.02740803,0.00637232,-0.03457141,-0.00337056,-0.03002633,0.02736799,0.00347681,-0.01497122,-0.02054294,-0.03833473,0.04949477,0.02292218,-0.00639826,0.00061045,-0.01259283,0.02808029,-0.01183996,0.03017649,0.02452008,-0.06722878,0.09721753,0.06523551,-0.03319447,-0.04064189,0.03123622,0.03440959,-0.03976782,0.02490869,0.02715795,0.02674658,0.02156005,-0.02808626,-0.00204639,0.03358294,-0.02811101,-0.19886157,-0.05495896,-0.01591993,0.0340087,0.05169295,-0.00089516,-0.01715307,0.01347485,0.07391755,0.05154162,0.06564365,-0.04018477,-0.0117748,0.02659768,0.00100928,0.04583879,-0.07377832,-0.02439059,-0.04046498,0.04134274,0.06883339,0.05313032,-0.0662884,-0.1005232,0.0649327,-0.02169109,0.12772512,-0.00078142,0.01391637,0.02357863,0.06449436,-0.00582737,0.01922874,0.01844519,0.0154944,0.02963155,-0.06369299,-0.07441016,-0.03178399,-0.00759761,-0.08204269,-0.01339232,0.00492551,-0.09522395,-0.03529863,-0.02077057,-0.03846408,0.00985038,0.00106858,0.10894051,0.07292449,-0.01945812,0.030491,-0.00299054,0.06253198,-0.01284417,-0.04322871,-0.02309081,-0.01030021,0.05314775,-0.02237634,-0.0354625,0.04456624,-0.03952617,0.04218403,0.03999666,-0.0706366,-0.0156472,-0.04436874,0.0252921,-0.01084266,0.06692714,0.02206445,0.03933642,-0.01168301,0.0215182,0.0082662,0.0130924,0.02947152,-0.00782291,-0.05277057,-0.02576125,0.04127005,0.08773711,0.03318153,0.01816496,0.059749,0.02798765,-0.02934925,-0.01305393,0.01413649,0.05465217,-0.0725194,-0.0282793,0.01974562,0.01508723,-0.24614459,0.05024328,-0.02861791,0.01909015,-0.04646262,-0.00699773,0.02653786,0.03382019,0.04357831,0.0320829,0.03930939,0.0936929,-0.00410768,-0.0108265,0.00647665,-0.00960409,-0.01082209,-0.05674519,0.02640795,-0.02666812,0.04518346,0.05509077,0.23782519,0.03714543,0.01318883,0.00819223,-0.0470404,-0.00135369,-0.07240992,0.01935665,0.00397455,-0.01026556,0.06232563,0.00137281,-0.03277746,0.03206797,-0.03302414,0.02332621,-0.02098153,0.02228484,-0.0625734,-0.00581225,0.01757973,0.01581481,0.19776195,0.02263381,-0.00501349,-0.13116516,0.11131753,0.01881373,-0.05746949,-0.03390416,-0.08158564,-0.03276807,-0.02557812,0.00932414,0.00788325,-0.02513977,0.01286419,-0.03951238,0.01387081,-0.01654904,0.03854791,-0.00169148,0.01879871],"last_embed":{"hash":"15spwpw","tokens":496}}},"last_read":{"hash":"15spwpw","at":1753423598108},"class_name":"SmartSource","last_import":{"mtime":1753363612595,"size":14873,"at":1753423416500,"hash":"15spwpw"},"blocks":{"#---frontmatter---":[1,6],"#Positional Lotto Wheels, Wheeling":[8,131],"#Positional Lotto Wheels, Wheeling#{1}":[10,17],"#Positional Lotto Wheels, Wheeling#{2}":[18,19],"#Positional Lotto Wheels, Wheeling#{3}":[20,27],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>":[28,56],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{1}":[30,47],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{2}":[48,48],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{3}":[49,49],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{4}":[50,50],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{5}":[51,51],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{6}":[52,52],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{7}":[53,53],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{8}":[54,54],"#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{9}":[55,56],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>":[57,80],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{1}":[59,59],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{2}":[60,60],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{3}":[61,62],"#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{4}":[63,80],"#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>":[81,101],"#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>#{1}":[83,101],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)":[102,131],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}":[104,105],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{2}":[106,106],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{3}":[107,107],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{4}":[108,108],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{5}":[109,109],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{6}":[110,110],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{7}":[111,111],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{8}":[112,112],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{9}":[113,113],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{10}":[114,114],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{11}":[115,115],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{12}":[116,116],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{13}":[117,117],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{14}":[118,118],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{15}":[119,119],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{16}":[120,120],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{17}":[121,121],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{18}":[122,122],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{19}":[123,123],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{20}":[124,125],"#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{21}":[126,131]},"outlinks":[{"title":"_**Positional Lotto Wheels from Groups of Numbers**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/jsjtCP6Pszc","line":18},{"title":"The lottery skip software generates combinations from positional systems.","target":"https://saliu.com/ScreenImgs/skip-systems-lotto.gif","line":46},{"title":"Lottery utility software has a function to wheel in combinations that satisfy minimum guarantees.","target":"https://saliu.com/ScreenImgs/positional-wheel-utility.gif","line":67},{"title":"Special lottery wheeling software creates efficient lotto wheels from input files.","target":"https://saliu.com/ScreenImgs/positional-wheel-program.gif","line":73},{"title":"The best lotto wheels are created on-the-fly by purging files generated from positional systems.","target":"https://saliu.com/ScreenImgs/on-the-fly-wheeling.gif","line":100},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":102},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":106},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":108},{"title":"_**Create, Make Lotto Wheels in Lottery Wheeling Software or Manually**_","target":"https://saliu.com/lottowheel.html","line":110},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":111},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":113},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":115},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":116},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":117},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":118},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":119},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":120},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>**_","target":"https://saliu.com/reverse-strategy.html","line":121},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":122},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":123},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":124},{"title":"Lottery wheeling master teaches how to create best positional lotto wheel from in-position systems.","target":"https://saliu.com/HLINE.gif","line":126},{"title":"Forums","target":"https://forums.saliu.com/","line":128},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":128},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":128},{"title":"Contents","target":"https://saliu.com/content/index.html","line":128},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":128},{"title":"Home","target":"https://saliu.com/index.htm","line":128},{"title":"Search","target":"https://saliu.com/Search.htm","line":128},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":128},{"title":"You need a little patience if you want to generate positional lottery wheels with Ion Saliu software.","target":"https://saliu.com/HLINE.gif","line":130}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["positional lotto wheels","reduced","lotto","systems","lottery wheel","software","position-by-position","in-position","real winnings","analyses"],"source":"https://saliu.com/positional-lotto-wheels.html","author":"Ion Saliu"}},"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08962771,-0.04635275,0.01961698,-0.00760807,-0.02402366,0.06878714,0.00141691,0.05408433,0.03236536,-0.02245724,0.00108524,-0.02213599,0.01882119,0.01698268,-0.00745136,-0.02785875,0.01779941,0.03877664,-0.05377555,-0.00510859,0.06522708,-0.04205936,-0.05718252,-0.08111901,0.0285857,0.00589849,-0.03260559,-0.05080822,-0.00390743,-0.19490324,-0.01138972,0.03194293,-0.03120679,0.02459162,-0.04626037,-0.03314866,-0.06112476,0.05133441,-0.03103254,0.00969013,0.03889291,0.0504511,-0.02775828,0.00809158,0.03135689,-0.0190952,0.03331805,0.00515803,0.01235763,0.00829345,-0.03615766,-0.02392472,0.01031736,0.0407496,0.05354192,0.04780602,0.02116239,0.0530881,0.04824866,0.02472535,0.07711627,0.02606091,-0.21970645,0.06517345,-0.00297209,0.00560504,0.00830887,-0.05341365,-0.03575814,0.0398187,0.08034885,-0.00484058,-0.02832473,0.06099903,0.05723681,-0.02723945,-0.00315995,-0.07763715,-0.05160672,-0.03707698,0.00727593,-0.00467328,-0.03322734,-0.02704538,-0.02192896,0.03244874,0.08025643,0.03487702,0.05149112,-0.06678388,0.03461045,-0.015419,0.09813232,0.06987508,-0.05587675,0.01890748,0.02579324,0.04666808,-0.00908633,0.1172682,-0.03486385,0.08761456,0.00258276,-0.01224319,0.03584281,0.02147047,-0.01020958,-0.02293556,-0.0489535,0.0402587,0.05604789,0.07046895,0.02160839,-0.04525062,-0.00902927,-0.01703951,0.03366545,0.03787162,0.0037593,0.03227974,-0.04155141,0.0270517,0.03725875,0.01308972,0.00692966,0.01837179,0.05357841,0.07125835,-0.00148843,0.01498373,0.0543972,-0.00783728,-0.07675606,-0.02893266,-0.00018819,-0.03425845,0.00960092,-0.02478764,0.03037324,-0.00261924,-0.03631672,-0.09317579,0.03347725,-0.10966868,-0.05906892,0.06822996,-0.02156006,-0.02771492,0.00939993,0.02133433,-0.04839661,0.00256992,-0.01336606,-0.03055288,0.01887293,0.00492643,0.06644845,0.07974621,-0.04585486,0.01644203,-0.02429387,-0.05930845,-0.07955765,0.14336175,-0.03414442,-0.12853043,-0.04935632,0.06356265,0.01927748,-0.0416816,-0.01331807,0.00591945,-0.06197565,0.01648147,0.10363457,0.02501448,-0.03035114,0.00421136,-0.06756467,-0.01462989,0.00023742,-0.04558531,-0.0525051,0.00774253,-0.05442811,-0.02728267,-0.02225776,-0.00392523,0.03430754,0.03948367,-0.02743356,-0.0156938,-0.0152307,0.01375175,-0.02390434,-0.03197801,-0.01627402,-0.04488612,0.05464015,-0.01059513,-0.00227531,0.02987786,-0.04856578,0.03842426,-0.02906964,-0.01638531,-0.01194064,-0.05625837,0.05304276,0.05789903,-0.02124264,0.01337576,0.05081082,0.01221625,-0.04657989,0.01328633,-0.01370693,0.00974756,0.01972474,-0.02434245,-0.04350106,0.00567835,-0.03706217,-0.22638372,-0.01061275,-0.01938905,0.01830258,0.00574976,-0.01243722,-0.01062513,0.01874095,0.06334978,0.02858905,0.10519989,-0.02876549,-0.00005599,-0.02621444,0.00604246,0.06458643,-0.04150296,-0.031137,-0.01122078,0.05161668,0.05794108,0.04097841,-0.06047773,-0.11834399,0.06383701,-0.01013981,0.12293481,0.04284166,0.02945133,-0.02167005,0.10778148,-0.0111154,0.01793206,-0.02862714,0.01140393,0.01818827,-0.09634659,-0.0316835,-0.03369254,-0.03814009,-0.07965131,0.01062922,0.01520407,-0.09624358,0.02182752,-0.03209966,0.00495182,-0.02717061,-0.01055263,0.06844953,0.05686932,-0.01730277,0.01362087,0.01544216,0.0482064,-0.01544626,-0.03775601,-0.04675974,-0.02971093,0.02362452,-0.02805421,-0.01117489,0.05239995,-0.04600096,0.02068133,0.02510849,-0.05440984,-0.04067757,-0.07442346,0.01297578,0.00957481,0.05685573,0.04239313,0.03926234,-0.04259214,0.00325582,-0.01924424,0.03992165,0.04468575,0.01478349,-0.06266339,-0.03497977,0.05926672,0.08581337,0.0383276,0.05985436,0.03013164,-0.02468122,-0.00193333,0.01263065,0.01556543,0.06716231,-0.0692904,-0.02489707,0.06745145,0.00429437,-0.25841507,0.05663835,0.02359591,0.01262709,-0.01916828,0.02729674,0.01215008,0.02367737,0.00753674,-0.00161411,0.0470201,0.09373014,-0.02194943,-0.00142989,-0.01276222,0.00930438,0.00687781,-0.02259325,0.02882823,-0.00881699,0.00762651,0.02553607,0.23581402,0.0542368,0.02503934,0.00486693,-0.06119551,-0.02306261,-0.04929856,0.03199139,-0.02353258,-0.00307328,0.07418828,0.03594379,-0.03559374,0.04537235,-0.05523177,0.0455459,-0.02111338,0.04451586,-0.09741402,0.03039227,0.01234724,0.00639566,0.14895694,0.04152416,-0.02171038,-0.11038209,0.06155055,-0.0035215,-0.04935193,-0.0660702,-0.10304821,-0.02440517,-0.02223131,0.02372005,0.04848083,-0.03923957,-0.00116898,-0.0266693,0.02857438,-0.03030491,0.03859339,-0.01827946,0.03719958],"last_embed":{"hash":"1hirv86","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"1hirv86","at":1753423596012},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#---frontmatter---","lines":[1,6],"size":256,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04983623,-0.04948948,0.00315408,-0.00937597,-0.04765414,0.07891644,0.01584004,0.02340872,0.06310219,-0.00423165,-0.00260897,0.02954092,0.02668353,0.01059158,-0.04131392,-0.05594188,0.00301889,0.03433968,-0.05957412,-0.02640899,0.04239213,-0.07808517,-0.07003559,-0.06285682,0.02654409,0.00284271,-0.0543985,-0.07626401,-0.05764788,-0.24136384,-0.00150264,0.04184857,0.00033172,-0.0160185,-0.07294672,-0.05204206,-0.06611659,0.06327654,-0.0648726,-0.00099122,0.01934133,0.0652291,-0.00893103,0.00509538,0.02921879,-0.02605334,0.01436515,-0.0052849,-0.00288379,-0.01708288,-0.03851405,-0.0141474,0.01983907,0.0447151,0.03717302,0.02280806,0.00463417,0.07920601,0.04718389,-0.00984084,0.08109293,0.03588996,-0.18947461,0.04372431,-0.00219453,-0.00762198,0.02207626,-0.03798153,-0.0118597,0.04548807,0.08154167,0.03061452,-0.01978307,0.04846441,0.04184664,-0.04106903,0.00021278,-0.06694755,-0.04376499,-0.01140025,0.02008968,-0.03561234,-0.01559582,-0.04449121,0.0080519,0.01108156,0.04525158,-0.00180791,0.05462478,-0.05289502,0.05969101,0.0481258,0.0419315,0.02738624,0.00923823,-0.00688154,0.02786375,0.03028328,0.00177071,0.10102164,0.0048662,0.07448521,0.01423583,0.0016356,0.01366258,-0.02962984,-0.03677803,-0.01275254,-0.06997164,0.04547298,0.09427652,0.05539319,0.03147376,-0.00969246,-0.04913699,-0.00238688,0.03861303,0.04039879,0.02787379,0.0068478,-0.04725004,0.04634642,0.05310252,0.0314905,0.00315281,0.00733024,0.04109477,0.07266212,0.02261874,0.02194968,0.05236541,0.00106362,-0.10873432,-0.02925169,-0.01392444,-0.03536522,0.01757727,0.02888296,0.03572319,0.03280124,-0.00384963,-0.11297674,0.06807521,-0.10274255,-0.05584583,0.07878506,-0.04006818,0.00251632,-0.01205712,0.00327191,-0.03205221,-0.00358811,-0.04811771,-0.0526155,-0.0128699,-0.00118803,0.04828132,0.06027472,-0.05566935,-0.00155704,-0.06704018,-0.02411055,-0.0770428,0.112842,-0.03505421,-0.12007408,-0.02395879,0.03223283,-0.00539852,-0.0506715,-0.01103313,0.00896092,-0.07995368,0.02975917,0.06648341,-0.0061054,-0.076037,-0.00059107,-0.04330988,0.01900239,0.03303989,-0.03115591,-0.03232682,-0.00442578,-0.01567261,-0.00853996,0.00531038,-0.00248387,0.02133438,0.00592025,-0.03169016,0.00334128,-0.02397644,0.01450467,0.01532629,-0.01304554,-0.02599554,-0.04029934,0.05126463,0.012133,0.00989874,-0.00575315,-0.00708392,0.03407823,0.00018961,0.02973476,0.03246562,-0.06639999,0.11620881,0.06428465,-0.03990814,-0.05680938,0.02584821,0.03637023,-0.04755894,0.01997958,0.02937254,0.03312088,0.01399454,-0.02656762,0.00567024,0.03973112,-0.03244435,-0.18888836,-0.06369709,-0.01419194,0.03901589,0.05871621,0.00022275,-0.01361958,0.01570379,0.06737829,0.05453555,0.06642895,-0.04632701,-0.01024185,0.02661002,-0.00082916,0.04385303,-0.08055521,-0.0268539,-0.03116675,0.03213023,0.07441156,0.04449868,-0.07209574,-0.10184505,0.07111545,-0.01903521,0.1251118,-0.00866136,0.01885319,0.02265409,0.05529708,-0.01555917,0.02132199,0.03118015,0.01314608,0.0258843,-0.05264572,-0.0820606,-0.03277674,-0.00059991,-0.07534543,-0.02252778,-0.00234573,-0.09391853,-0.04068331,-0.01747073,-0.0426044,0.01671066,0.00280341,0.11122099,0.068247,-0.02878919,0.04647599,-0.00283103,0.06607221,-0.01546747,-0.03758723,-0.00911687,-0.0056104,0.06650802,-0.01262815,-0.03391139,0.04079688,-0.0330028,0.04114855,0.0333839,-0.07087044,-0.01866315,-0.04226872,0.02614661,-0.01656995,0.05656172,0.0066778,0.03838719,-0.00499254,0.02242201,0.00941089,0.00377772,0.02428108,-0.02130155,-0.05595399,-0.02203314,0.03851802,0.08729434,0.02368129,0.0103184,0.05590975,0.03570313,-0.03007212,-0.00656041,0.00558657,0.04733887,-0.08220369,-0.03984369,0.00801753,0.00817714,-0.24455205,0.04977929,-0.03927753,0.01622434,-0.05805545,-0.01647151,0.0311898,0.03881987,0.04392182,0.03837915,0.03849271,0.09554307,-0.00456824,-0.01342022,0.00668634,-0.01538882,-0.00244408,-0.06261065,0.01949636,-0.0287043,0.05506653,0.05545281,0.23908499,0.03319457,0.01990501,0.00878434,-0.0486957,0.01037588,-0.0757111,0.00763234,0.01118735,-0.00893149,0.05821307,-0.0016005,-0.02729625,0.02759104,-0.01388556,0.02100086,-0.01934364,0.02052642,-0.05063678,0.00148394,0.02033434,0.02726333,0.19135632,0.01039612,0.00205794,-0.13212578,0.11226155,0.0233903,-0.06770711,-0.02215443,-0.08915158,-0.03181046,-0.02898889,0.01291782,0.00351795,-0.03136822,0.01830428,-0.04164422,0.01577398,-0.02669203,0.0409614,-0.00486434,0.00677675],"last_embed":{"hash":"1lukvno","tokens":419}}},"text":null,"length":0,"last_read":{"hash":"1lukvno","at":1753423596063},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling","lines":[8,131],"size":14593,"outlinks":[{"title":"_**Positional Lotto Wheels from Groups of Numbers**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/jsjtCP6Pszc","line":11},{"title":"The lottery skip software generates combinations from positional systems.","target":"https://saliu.com/ScreenImgs/skip-systems-lotto.gif","line":39},{"title":"Lottery utility software has a function to wheel in combinations that satisfy minimum guarantees.","target":"https://saliu.com/ScreenImgs/positional-wheel-utility.gif","line":60},{"title":"Special lottery wheeling software creates efficient lotto wheels from input files.","target":"https://saliu.com/ScreenImgs/positional-wheel-program.gif","line":66},{"title":"The best lotto wheels are created on-the-fly by purging files generated from positional systems.","target":"https://saliu.com/ScreenImgs/on-the-fly-wheeling.gif","line":93},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":95},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":99},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":101},{"title":"_**Create, Make Lotto Wheels in Lottery Wheeling Software or Manually**_","target":"https://saliu.com/lottowheel.html","line":103},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":104},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":106},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":108},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":109},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":110},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":111},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":112},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":113},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>**_","target":"https://saliu.com/reverse-strategy.html","line":114},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":115},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":116},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":117},{"title":"Lottery wheeling master teaches how to create best positional lotto wheel from in-position systems.","target":"https://saliu.com/HLINE.gif","line":119},{"title":"Forums","target":"https://forums.saliu.com/","line":121},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":121},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":121},{"title":"Contents","target":"https://saliu.com/content/index.html","line":121},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":121},{"title":"Home","target":"https://saliu.com/index.htm","line":121},{"title":"Search","target":"https://saliu.com/Search.htm","line":121},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":121},{"title":"You need a little patience if you want to generate positional lottery wheels with Ion Saliu software.","target":"https://saliu.com/HLINE.gif","line":123}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03515579,-0.04121993,0.0172735,-0.01036401,-0.03162054,0.06699979,0.02873153,0.03321141,0.04039803,-0.01285138,-0.00548714,0.0129008,0.00503562,0.02526562,-0.03580922,-0.04458307,0.00342422,0.03509435,-0.07429507,-0.02238693,0.05296071,-0.07067858,-0.06352743,-0.06454169,0.03495098,0.00465169,-0.04788216,-0.06596044,-0.04668193,-0.22495149,-0.0048741,0.02639144,-0.02136305,-0.0133162,-0.06815396,-0.04741116,-0.05659991,0.06716069,-0.05960058,0.00144676,0.02419574,0.05153592,-0.0339236,0.01699417,0.03796781,-0.02831922,0.03065812,-0.00848863,-0.00718744,-0.01358441,-0.04252791,-0.00575912,0.01691379,0.033312,0.03030392,0.03608558,0.00044763,0.06531225,0.04805739,-0.01528831,0.0788913,0.03307954,-0.19638433,0.0456878,-0.00652606,-0.02470447,0.02094563,-0.04551917,-0.02295309,0.03786929,0.09492619,0.03138701,-0.01884744,0.0518434,0.04394397,-0.04022132,0.01910514,-0.05918544,-0.03040792,-0.01718262,0.02719564,-0.01945382,-0.02425206,-0.0452962,0.00076628,0.00158817,0.04029122,-0.00696926,0.05260588,-0.05811938,0.06859133,0.03187764,0.04285856,0.03243141,-0.00042735,0.00582638,0.01334874,0.02965413,-0.00040081,0.1171689,-0.00670664,0.07954579,0.00511391,-0.0112129,0.01965798,-0.02212643,-0.01960681,-0.00448777,-0.04422198,0.05899649,0.067971,0.04563832,0.036582,0.00195536,-0.04325537,-0.0032798,0.04571398,0.05229202,0.02395486,0.02175756,-0.04651861,0.0603787,0.06205506,0.03642184,-0.00633391,-0.0211554,0.0332669,0.07577019,0.01405874,0.02531012,0.05792201,-0.0049282,-0.0982096,-0.02878445,0.00637877,-0.03853061,0.03370503,0.02366078,0.04366245,0.04171173,-0.01805964,-0.11076818,0.04463861,-0.1141614,-0.069271,0.07445198,-0.04461016,-0.01606905,0.00164129,0.021597,-0.04118717,-0.00547077,-0.03794987,-0.05378976,-0.00440439,-0.00519043,0.03533471,0.0691647,-0.05193391,-0.00322223,-0.06742651,-0.02667235,-0.07685453,0.12870757,-0.06372016,-0.14085497,-0.03675943,0.02023395,0.01618447,-0.04138767,-0.00481395,0.00508177,-0.08892322,0.02392464,0.07366602,0.00673702,-0.05096434,-0.01987486,-0.05799922,0.01959525,0.02703592,-0.05399408,-0.04499032,0.00433,-0.01160554,0.00414204,0.00076236,-0.00235857,0.01799324,0.00547078,-0.03754865,0.011179,-0.03536298,0.0226975,0.02092036,-0.01082363,-0.01948793,-0.05037257,0.0435946,0.00758561,-0.00534897,0.01036996,-0.00928289,0.0415457,0.01160655,0.0233257,0.03121551,-0.05762268,0.11159074,0.0668581,-0.03508171,-0.04456003,0.02657724,0.03685564,-0.05092593,0.00909998,0.03659154,0.03060665,0.01182081,-0.02765716,0.01586313,0.04280223,-0.03863804,-0.19506288,-0.04992468,-0.03992095,0.01587276,0.08028069,-0.00020894,-0.01180581,0.01433513,0.07011517,0.05413579,0.05837724,-0.0451158,0.00051654,0.02991258,0.00527481,0.05345159,-0.06919877,-0.03852584,-0.03191063,0.0241752,0.06479374,0.05518903,-0.05238116,-0.10030156,0.07174643,-0.00888689,0.12415908,0.00658635,0.0323848,0.02391604,0.0492308,-0.03145226,0.02984397,0.00202995,0.00174979,0.02041656,-0.06033842,-0.09618982,-0.04814503,-0.00674007,-0.04760278,-0.01443624,-0.01002592,-0.08372992,-0.03085237,-0.02700512,-0.03180002,0.03162137,0.00095421,0.11863858,0.06058177,-0.0474731,0.0560276,0.00394735,0.06249327,-0.0189086,-0.02100789,-0.00623107,-0.00316013,0.08430013,-0.00714969,-0.0259429,0.03223979,-0.03850193,0.04065081,0.02153336,-0.07829844,-0.03329557,-0.06323543,0.02303667,-0.03328782,0.03265608,-0.00190082,0.02712623,-0.01847642,0.02839199,0.01229631,0.03109334,0.02860316,-0.03016831,-0.05314715,-0.01241485,0.04846559,0.07671976,0.02008221,0.02007443,0.04250634,0.00833306,-0.03674203,-0.00361627,0.0104847,0.02254995,-0.08835299,-0.04821775,0.01560468,0.00003228,-0.24215357,0.05526212,-0.01735233,0.00385383,-0.04406104,0.00084147,0.02322301,0.03051227,0.04010564,0.02923406,0.06245983,0.11399382,-0.01232659,0.0008824,0.0053962,-0.03229376,0.01746322,-0.05943436,0.01634398,-0.028899,0.04713608,0.046128,0.22850658,0.04144845,0.01398763,0.00968301,-0.05000763,0.00179855,-0.09408617,-0.00501433,0.01340155,-0.00794428,0.03969749,0.02082588,-0.0076385,0.03804276,-0.01622056,0.04569234,-0.00227807,0.02058495,-0.06225063,0.01160096,0.02632456,0.0438518,0.18725772,0.01494397,-0.01608611,-0.11754078,0.11133453,0.01854531,-0.06326587,-0.02832722,-0.09188495,-0.03565118,-0.04056541,0.01389294,0.02120537,-0.04791699,0.02517047,-0.04614636,0.02123897,-0.02696438,0.02570331,-0.00578836,0.02556809],"last_embed":{"hash":"nttjxa","tokens":162}}},"text":null,"length":0,"last_read":{"hash":"nttjxa","at":1753423596227},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#{1}","lines":[10,17],"size":624,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05942517,-0.04964325,0.00115991,-0.044276,-0.07455943,0.05901759,0.00919644,-0.00212681,0.07693297,-0.01647206,0.01021179,0.01290788,0.04669374,-0.01724835,-0.03894588,-0.00582605,0.01258681,0.03226366,-0.0626258,-0.01441058,0.04289796,-0.05233371,-0.06110731,-0.09400267,0.00247541,0.01413724,-0.04908911,-0.06897744,-0.03997055,-0.26922438,0.02238753,0.04162921,0.02085125,-0.02262275,-0.03628362,-0.04843495,-0.086242,0.06064252,-0.05746424,-0.01501235,0.01240378,0.05918874,0.01561233,-0.00826095,0.01378514,-0.03536483,0.01688879,0.0133962,0.02442892,-0.03068711,-0.04572206,-0.03089854,0.02402934,0.0476951,0.02841314,0.01298892,0.01118866,0.07836511,0.03397033,0.03512293,0.09418209,0.05267492,-0.18407591,0.03725044,-0.00631263,0.00071835,0.02028228,-0.03896206,-0.00533905,0.06534782,0.06107697,-0.0037836,-0.02527844,0.06441614,0.0516937,-0.02646177,-0.0260092,-0.07861666,-0.05855683,0.00567319,-0.01979034,-0.03097,-0.00574303,-0.03418057,0.00559657,0.03960104,0.07033565,0.01910747,0.04073092,-0.04145041,0.03572617,0.04810192,0.02296384,0.03452003,-0.02006884,-0.01243749,0.05299052,-0.00194021,0.02526152,0.10097725,0.00988108,0.05250587,0.00829873,0.00361856,-0.01162579,-0.03159453,-0.04245969,-0.01834062,-0.09181414,0.0109498,0.10769767,0.05851316,0.03343667,-0.02426793,-0.04981836,0.01714224,0.01000503,0.02457613,0.01233478,0.00092053,-0.0298341,0.03368849,0.01237528,-0.00089109,-0.01078947,0.02686439,0.06457065,0.07445002,0.01908273,0.03052989,0.04293109,-0.01426665,-0.09778602,-0.02188976,-0.05055384,-0.03610751,0.00652736,0.02858815,0.02272012,0.01403813,-0.00464607,-0.09630809,0.07238618,-0.07661565,-0.01783219,0.08486658,-0.03998611,0.00920225,-0.00363326,0.00633362,-0.04357795,-0.00363243,-0.04379092,-0.03357417,0.00244475,0.02059022,0.05242374,0.05095835,-0.05941029,0.00972024,-0.05864394,-0.0216601,-0.06243563,0.12007067,-0.0023927,-0.09678061,-0.02213226,0.06487655,-0.02358019,-0.06378206,-0.01424994,0.00821381,-0.06362271,0.01019674,0.07156967,-0.00173137,-0.11172217,0.03025273,-0.00557892,0.00837034,0.03610013,-0.01144137,-0.0140423,0.00187808,-0.02105249,-0.04862135,0.01315564,0.00812145,0.03400689,0.02306068,-0.0128113,0.01301135,-0.00512356,-0.00100135,-0.00488605,-0.02477672,-0.04045793,-0.02457421,0.06868468,-0.04797794,0.03598445,-0.0028464,-0.00787614,0.04391334,-0.00768101,0.00816772,0.00361271,-0.06327778,0.08945817,0.06476764,-0.04889638,-0.0264218,0.03397286,0.03774496,-0.08052432,0.03619264,0.01429686,0.03411743,0.02144879,-0.01722165,0.0100581,0.00794199,0.01090997,-0.20233302,-0.05180499,0.01658894,0.0614187,0.02388408,0.01803226,-0.02032258,0.01578662,0.05966385,0.03106941,0.08736087,-0.0387057,-0.00967691,0.00784317,-0.00854308,0.05094688,-0.10053179,0.0016413,-0.01370105,0.05928561,0.06781706,0.03108114,-0.09661149,-0.10446999,0.05634457,-0.04358268,0.13208799,-0.00746888,0.01043097,-0.01525865,0.06797368,-0.0175209,0.00467809,0.04620448,0.04066538,0.00996255,-0.05489902,-0.05381251,-0.00599453,-0.03038505,-0.08855622,-0.01181644,-0.00429682,-0.09289138,-0.00370612,-0.02942322,-0.03916371,-0.00715892,0.01390824,0.08634378,0.06069131,-0.00320242,0.01243889,0.00660837,0.06218126,-0.04161979,-0.04919928,-0.00113093,-0.00538768,0.0187924,0.0021408,-0.04881467,0.04881487,-0.01711875,0.04425798,0.05892446,-0.06086161,-0.019619,0.0106416,0.0035876,0.01036522,0.05502982,0.02022202,0.06942322,0.0022147,0.02877704,0.01630406,-0.02092251,0.03974629,0.00924434,-0.0524822,-0.01489886,0.03806293,0.08760223,0.0261289,0.00285087,0.07026792,0.05907743,-0.00331424,0.00955504,0.01419645,0.08717225,-0.03820237,-0.01243151,0.00745308,0.02431435,-0.24916968,0.02904683,-0.06535976,0.05272854,-0.05970163,-0.02961202,0.03992301,0.03586496,0.02361345,0.03214357,0.02793739,0.05486259,0.01329103,-0.05113706,0.02308773,-0.01237507,0.00748443,-0.02988707,0.02520701,-0.05913329,0.06835447,0.0340561,0.25539079,0.02562103,0.0232515,-0.0136283,-0.03831715,-0.00740953,-0.02226872,0.01721658,0.00123252,-0.01547706,0.08265296,-0.02555996,-0.0341044,0.018039,-0.00866246,0.00972051,-0.03853392,0.0104685,-0.06805085,-0.00661185,-0.00945454,-0.00860865,0.16449976,0.03686915,0.00551553,-0.11709421,0.05163578,0.00551831,-0.06264668,-0.03100821,-0.08575649,-0.03775692,-0.00803216,0.02628817,0.00359947,-0.0202903,-0.02629852,-0.02908164,0.02538577,-0.0407492,0.05751345,0.00155467,-0.01401638],"last_embed":{"hash":"1wxvdor","tokens":433}}},"text":null,"length":0,"last_read":{"hash":"1wxvdor","at":1753423596279},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#{3}","lines":[20,27],"size":1823,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04547369,-0.04795141,-0.00530899,-0.05460834,-0.0752934,0.07029035,-0.00532039,-0.01356948,0.05286073,-0.01045101,-0.01903651,-0.01583358,0.0375877,-0.01466268,0.00227229,-0.01554901,0.01535954,0.02719302,-0.11085755,-0.03183275,0.07632259,-0.01692214,-0.05027199,-0.11594348,-0.01013752,0.02758379,-0.0310024,-0.05321585,-0.01346352,-0.25341541,0.02874027,0.03597486,-0.01132679,-0.00283936,-0.06877135,-0.04537823,-0.09721486,0.06066553,-0.08632467,0.00666718,0.03576706,0.04724281,0.00894325,0.01394404,-0.00026782,-0.01562329,-0.01995684,0.01218181,0.00901475,0.0124997,-0.03935213,-0.035047,0.01028065,0.04321646,0.00125673,0.03810395,0.03572441,0.08951528,0.02930586,0.03663995,0.07158833,0.03760544,-0.16966887,0.0610782,0.01281676,-0.00055119,0.04510823,-0.03886835,0.01958908,0.09658135,0.04114862,0.02617584,-0.024868,0.07630662,0.05175134,-0.00051866,-0.03118959,-0.08155543,-0.00519712,0.03148302,-0.00219754,-0.01818226,-0.02138101,0.01669148,0.02039886,0.03031025,0.01959408,0.00441322,0.00009367,-0.01874786,0.04718388,0.03287331,0.03403715,0.02101088,-0.0089124,-0.01406694,0.01901707,-0.00469142,0.01509929,0.09117605,0.02421845,0.04602166,0.02728407,0.01678585,0.00543807,-0.03482332,-0.06082092,-0.02625354,-0.0591304,-0.00663313,0.09635694,0.06180115,0.01416076,-0.05759219,-0.05555157,-0.00112113,0.03888867,0.00553627,0.01881783,0.01643001,-0.03762925,0.01657048,-0.00078357,0.02799118,0.02742722,0.03702269,0.02277699,0.11548687,0.03501097,0.06385603,0.01341747,-0.02439942,-0.07022513,-0.04563911,-0.03605878,-0.04883876,0.02382722,0.04143525,0.01859283,0.00960102,-0.00830685,-0.10327215,0.05501905,-0.07518895,-0.01423616,0.08485718,-0.0325187,0.0103785,-0.04478705,0.01130274,-0.02251593,-0.03425043,-0.02368092,-0.03879689,-0.00792501,0.01915621,0.04549022,0.0528318,-0.03576052,0.02556287,-0.07541726,-0.01146023,-0.03465364,0.12806912,-0.010533,-0.09257858,-0.02437476,0.05612277,-0.00939139,-0.06706014,-0.0186156,0.03312372,-0.05656484,0.03071473,0.09917719,0.0244879,-0.09954614,0.01922891,0.01062042,0.01963426,0.01585246,-0.01546576,-0.03659045,-0.01953267,-0.05654645,-0.08038179,0.01408214,0.0155849,0.00710932,0.01632847,-0.02829296,-0.0050083,0.01223438,-0.01223003,0.00373012,-0.03854849,-0.00715674,-0.05376033,0.06536332,-0.0190607,0.05931471,0.00904782,-0.00038233,0.01783691,0.01165174,0.01631378,0.02400678,-0.07366996,0.09547237,0.05087046,-0.0077084,-0.01742501,0.04441788,0.05961062,-0.04673981,0.02925821,0.00229969,0.01101703,0.01724861,-0.01178277,-0.01097692,-0.02664763,-0.02064153,-0.22743364,-0.0170629,0.03283216,0.03285544,0.03701312,0.0138864,-0.01835717,0.00110182,0.03558789,0.0406892,0.07138175,-0.03404721,-0.03989771,0.00577376,0.00473718,0.03663,-0.106529,-0.01087345,-0.02908128,0.07104739,0.03244814,0.0154333,-0.06178812,-0.10404145,0.06203679,-0.04111896,0.1473083,-0.01165063,0.01581005,-0.02506468,0.07125076,-0.01070499,0.02810348,0.05432727,0.05357432,0.01583109,-0.07009167,-0.0110664,0.02353377,-0.02444167,-0.06653076,0.00504958,-0.0221482,-0.0779157,0.01016051,-0.03806089,-0.04969592,-0.0432333,0.03982216,0.05477813,0.03722862,-0.0231947,0.02112267,-0.00084198,0.06176127,-0.08400233,-0.05404352,0.02444724,-0.01988232,0.02587555,-0.02846489,-0.05117689,0.05849093,-0.01440182,0.01202698,0.03541249,-0.04754543,0.0141639,-0.00336466,0.01454297,-0.01012677,0.07456826,0.01848295,0.03589039,-0.03765979,-0.00568749,-0.0166656,-0.01261959,0.03178049,-0.01102516,-0.05011336,-0.01919041,0.00925179,0.08532762,0.0248074,-0.02980655,0.07009166,0.03448853,0.04228663,0.0006719,-0.01571861,0.07523248,0.00367656,0.02779096,0.02492296,-0.00070877,-0.26149252,0.02820985,-0.04585399,0.03343374,-0.06146078,-0.03501657,0.03027361,0.02546861,0.01579187,0.0057115,0.0193706,0.03397609,0.02018102,-0.0674585,0.02549265,0.01636065,0.01530981,-0.05792858,0.02622081,-0.05336677,0.07721763,0.04686268,0.25382376,0.01270772,0.03739261,0.02768881,-0.03382598,0.0087942,-0.03936506,0.03851455,-0.03276695,-0.01122455,0.09491272,-0.02559327,-0.04166221,0.01480363,0.00132251,0.01523631,-0.04722453,0.00863763,-0.06469662,0.0107137,-0.00504125,0.00971951,0.13443421,0.01924185,0.02486355,-0.12208185,0.06870703,0.03066978,-0.05957175,0.02311741,-0.06348867,-0.04730483,-0.01127251,0.02667584,0.01995407,-0.03465201,0.00188414,-0.0000028,0.02359877,-0.07715607,0.06052889,-0.01253851,-0.03233172],"last_embed":{"hash":"1y9pstr","tokens":487}}},"text":null,"length":0,"last_read":{"hash":"1y9pstr","at":1753423596444},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>","lines":[28,56],"size":3528,"outlinks":[{"title":"The lottery skip software generates combinations from positional systems.","target":"https://saliu.com/ScreenImgs/skip-systems-lotto.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04644184,-0.05261347,0.00219994,-0.05749091,-0.07148119,0.08593647,-0.00471235,-0.00819132,0.05092052,-0.00890075,-0.01688202,-0.01194695,0.02037738,-0.02048009,-0.0023738,-0.00312833,0.02487118,0.01895758,-0.11545248,-0.03134807,0.0811792,-0.00465273,-0.0498085,-0.12576126,-0.01573063,0.02145209,-0.02902946,-0.0573223,-0.00728088,-0.23731454,0.02381506,0.03041352,-0.00849391,0.00059721,-0.06476124,-0.05085598,-0.09478631,0.06002432,-0.0933787,0.00682967,0.03375043,0.0514555,0.01375017,0.01923332,-0.0040387,-0.01787433,-0.02495119,0.01618215,0.01508094,0.00460679,-0.04625036,-0.0358773,0.00709947,0.03001163,0.01142081,0.03949001,0.04898599,0.09856124,0.03089271,0.03469775,0.06779842,0.02992757,-0.18009163,0.07051183,0.00846386,-0.00324393,0.04922802,-0.03620944,0.01426861,0.09467298,0.04540273,0.02099358,-0.01786119,0.07763109,0.05352716,0.00109712,-0.03258438,-0.09290526,-0.0108549,0.03521625,-0.00565955,-0.0198476,-0.01434131,0.01853472,0.01935131,0.03097785,0.02304091,0.00335024,-0.00101418,-0.01951404,0.04905402,0.02814583,0.02382545,0.02675593,-0.02525584,-0.01289836,0.01377102,-0.00397933,0.02011084,0.09345564,0.02551965,0.03605159,0.03488502,0.02102999,0.00766376,-0.0286174,-0.0607294,-0.02009249,-0.05467181,-0.00140945,0.09191289,0.06540871,0.0076954,-0.05992386,-0.05500431,-0.00947963,0.0268113,-0.00196716,0.01804777,0.01454233,-0.03115438,0.01968482,-0.0027061,0.02883131,0.031824,0.04332753,0.02125711,0.10997105,0.04069688,0.07263386,0.02326191,-0.02320409,-0.06015749,-0.03860366,-0.04221436,-0.04466708,0.02168392,0.03900035,0.01781331,0.00712821,-0.01611496,-0.10053262,0.04752889,-0.07672796,-0.01967287,0.08383205,-0.04712748,0.00474376,-0.02733635,0.00994941,-0.01185829,-0.04504725,-0.0180744,-0.03072189,-0.00960247,0.03195605,0.04002826,0.05765789,-0.03132354,0.01419749,-0.07888994,-0.01173311,-0.03705545,0.12341082,-0.00659882,-0.08002879,-0.0329836,0.06020323,-0.00665062,-0.07167321,-0.0156564,0.03425486,-0.06392537,0.03837625,0.09521355,0.02567972,-0.09441049,0.01877617,0.0025476,0.00590181,0.0217077,-0.01511799,-0.02704352,-0.01686312,-0.04857652,-0.07938796,0.00890964,0.01771982,0.01351042,0.02475452,-0.0265385,-0.0039197,0.01578793,-0.01618924,-0.00297564,-0.03243989,-0.00494946,-0.05073769,0.06218088,-0.02118729,0.05234463,0.0108739,-0.0008586,0.0163529,0.00393536,0.00446088,0.01979797,-0.066705,0.07223064,0.03728011,-0.01121716,-0.01002553,0.0515296,0.05202179,-0.04949728,0.01764832,-0.00203019,0.01091501,0.02494297,-0.00977427,-0.01227509,-0.03232932,-0.02067098,-0.22955365,-0.01476283,0.03243736,0.01957294,0.03527769,0.01967434,-0.01042029,-0.00172529,0.04893811,0.03096191,0.07194708,-0.03053648,-0.03474255,0.00106486,0.00523724,0.03846479,-0.11559679,0.00095267,-0.01570764,0.08280461,0.03448376,0.00795075,-0.07120188,-0.09886616,0.06419133,-0.03352472,0.150134,-0.00733737,0.00332549,-0.03980113,0.07824039,-0.01360562,0.02963657,0.05482878,0.05271536,0.00309438,-0.07983282,-0.00183402,0.01746983,-0.03504281,-0.06462392,0.01150195,-0.0164462,-0.06936883,0.01550424,-0.02763929,-0.03986109,-0.05973116,0.03037544,0.03960055,0.03763885,-0.01527543,0.01917302,0.00364008,0.06030081,-0.07961886,-0.04977337,0.02552199,-0.01962307,0.02048995,-0.02261356,-0.05594363,0.06354776,-0.00655123,0.01658427,0.0294092,-0.03076774,0.01271412,-0.01025134,0.0198897,-0.00641607,0.06431177,0.01713152,0.0387913,-0.04629661,-0.01733596,-0.02207618,-0.01496761,0.03237468,-0.00859605,-0.05012255,-0.01590859,0.00966839,0.07848398,0.04581564,-0.02229922,0.07262819,0.03313146,0.04606385,0.00473154,-0.00627057,0.07305633,0.00504206,0.01508165,0.02213473,-0.00634763,-0.26685318,0.03414664,-0.04569243,0.0377314,-0.06471087,-0.02877881,0.02774129,0.02636724,0.0190512,0.00306058,0.03938456,0.03984141,0.01833308,-0.06518327,0.02356206,0.01205143,0.01364688,-0.05370338,0.0368606,-0.05286779,0.07737549,0.05016191,0.25537211,0.00136506,0.04661998,0.02850346,-0.02638227,-0.00330714,-0.03359543,0.03744602,-0.03110285,-0.00476322,0.11009214,-0.02200379,-0.04253178,0.03131356,-0.0043682,0.01610479,-0.05025623,0.01171528,-0.05726742,0.01140864,-0.01217825,0.01566111,0.12427836,0.02401669,0.02619183,-0.123925,0.05596738,0.03162401,-0.06183315,0.01836654,-0.06717765,-0.05457817,-0.01847447,0.01640754,0.02117821,-0.04177828,0.00840567,0.00124815,0.01956411,-0.09302399,0.04940187,-0.02094864,-0.0283125],"last_embed":{"hash":"1dv2lo","tokens":348}}},"text":null,"length":0,"last_read":{"hash":"1dv2lo","at":1753423596654},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{1}","lines":[30,47],"size":1139,"outlinks":[{"title":"The lottery skip software generates combinations from positional systems.","target":"https://saliu.com/ScreenImgs/skip-systems-lotto.gif","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02363983,-0.03739835,0.02324991,-0.03423798,-0.07884243,0.05837863,-0.02905032,-0.01633069,0.00916628,-0.03233435,-0.03338441,-0.02701037,0.07775977,0.00773991,0.03212137,-0.01371224,-0.0002978,0.09412438,-0.11257882,-0.06493408,0.059134,-0.00518379,-0.06170152,-0.06765497,-0.00609686,0.03895743,-0.03856548,-0.05487313,0.01523326,-0.25590238,0.01554816,0.05434108,-0.02250724,0.00305652,-0.04113793,0.00274325,-0.07993972,0.06978504,-0.09367864,0.01555656,0.04416501,0.03491319,0.00296367,-0.01257182,0.01151321,-0.00818242,0.01852068,0.03749137,0.01358783,0.03389787,-0.01033557,-0.03162231,-0.0055387,0.03380377,-0.00782949,0.02269408,0.01851971,0.07224326,-0.00166094,0.05279684,0.07091818,0.0750028,-0.15257111,0.03418268,0.01202768,0.00290533,0.02926844,-0.05253934,0.01982759,0.116185,0.02787565,-0.00005073,-0.04728289,0.06516716,0.03594494,-0.01002476,-0.03179252,-0.07069494,-0.01852352,0.03412566,-0.02097385,-0.01407092,-0.02944235,0.01698016,0.00835422,0.00194136,0.02191783,0.02058429,0.00080942,-0.04148257,0.03714681,0.02466014,0.04933805,0.03097416,0.00814775,-0.00583327,-0.00610924,0.02892869,0.00213767,0.11973438,-0.00269709,0.05685622,0.02518229,-0.01961445,-0.01233921,-0.01913005,-0.02407162,-0.03208694,-0.06161102,0.01051795,0.06199422,0.04739495,-0.00735847,-0.03815848,-0.01136314,0.02341546,0.06076632,0.02261582,-0.04540663,0.02126262,-0.02784138,0.00340522,0.01841365,0.03662883,0.01693825,-0.04730503,0.03961631,0.10334947,0.02175002,0.03695361,0.01474794,-0.00155671,-0.06572124,-0.04808555,-0.01190913,-0.07189845,0.01260166,0.0487622,0.03992326,0.00541454,-0.02615189,-0.08504011,0.03932432,-0.07099618,0.00125623,0.1019491,-0.03396099,0.01028826,-0.04392032,-0.0041404,-0.04565066,-0.00328482,-0.00569383,-0.06867173,0.03308654,-0.00009566,0.05375696,0.01846105,-0.00919327,0.05341981,-0.06695478,-0.00302414,-0.0680149,0.16487435,-0.04710343,-0.08308674,-0.00980778,0.06165714,0.0323377,-0.02120179,-0.01929938,0.03568019,-0.04010603,0.00808772,0.09797228,0.0082643,-0.08409061,0.01911444,-0.00666397,0.0441168,0.02046633,-0.03798828,-0.05474321,0.00360547,-0.04270244,-0.04352493,0.00855542,0.02742735,0.02035166,0.0163985,-0.06690139,0.01126429,-0.00815518,-0.00668655,0.00575797,-0.031559,-0.02538924,-0.05848466,0.03544043,-0.04675565,0.04793696,0.01030394,-0.01561069,0.03771874,0.0091746,0.00649037,-0.00623214,-0.06405608,0.11081574,0.07686768,-0.03920828,-0.00919178,0.0572072,0.05855709,-0.04565649,0.04271464,-0.00479912,0.01842369,0.00093973,-0.02800941,-0.01255602,-0.02010323,-0.00894373,-0.2422251,0.00023145,0.0314675,0.04283751,0.04294476,-0.01817345,-0.01814613,0.02105603,-0.04643834,0.03736685,0.05182371,-0.01921199,-0.01484514,-0.00921802,0.01398322,0.04856908,-0.06161651,-0.02311984,-0.03181984,0.04798343,0.05299368,0.02995538,-0.03680138,-0.12995738,0.0462354,-0.02180329,0.12504092,-0.00549925,0.05747487,0.00428636,0.06290705,-0.02851887,0.01674078,0.03144191,0.04504255,0.01986302,-0.05919605,-0.04171493,0.06971005,-0.01531018,-0.03359347,-0.00289266,-0.04055598,-0.09125318,0.06505008,-0.06708866,-0.05842167,-0.00678084,0.05031459,0.06366141,0.01304909,-0.0365056,0.0366487,0.00122269,0.02742536,-0.08527196,-0.05154827,0.01783473,-0.02408339,0.00848988,-0.01403802,-0.01801016,0.05405824,-0.02674412,0.01725226,0.0525112,-0.08612192,0.00859882,-0.01047962,0.01847634,-0.01765914,0.05308823,0.029629,0.04186455,-0.05115775,0.00939591,-0.05927818,-0.02604672,0.01896157,-0.03884353,-0.05699885,-0.01950282,0.0301799,0.06837406,-0.0212567,-0.00827134,0.03928303,0.01593396,0.03311947,0.00458238,-0.00893209,0.05185804,-0.00329482,0.05238683,0.05525278,-0.00727158,-0.25721145,0.01736741,-0.04141884,0.01180865,-0.05395107,-0.00717892,0.03312268,0.00720941,0.00156573,-0.03137378,0.00195399,0.056514,0.02019117,-0.03073251,0.03852566,0.02585385,0.05567301,-0.05946774,0.01901133,-0.05722193,0.07471773,0.0134182,0.26045701,0.05425932,0.0372883,0.03937456,-0.05974605,0.01210748,-0.02470753,0.01149218,-0.03245861,-0.03073655,0.06092014,-0.00113885,0.00143061,0.02601727,-0.01277906,0.03197988,-0.0181803,0.0335192,-0.09483579,0.0028827,0.00878978,0.00312906,0.13021855,0.00387274,0.02122551,-0.11775346,0.03498363,0.01005848,-0.05099909,0.02413484,-0.07173107,-0.03917866,0.01720339,0.0445551,0.0209233,-0.0412334,-0.00626875,-0.00826697,0.03640075,-0.05919927,0.05716991,-0.00957896,-0.03607883],"last_embed":{"hash":"ibobbv","tokens":120}}},"text":null,"length":0,"last_read":{"hash":"ibobbv","at":1753423596773},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{2}","lines":[48,48],"size":348,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05633182,-0.04058648,-0.00433297,-0.03645868,-0.0825019,0.07004265,0.00030931,-0.00551333,0.05353668,-0.01797166,-0.00845572,-0.02352719,0.05374408,-0.00125459,-0.02045541,-0.01066757,-0.0041698,0.05024558,-0.10721747,-0.02509064,0.07817995,-0.02913209,-0.07490171,-0.0812877,-0.00145154,0.0235833,-0.01717399,-0.02824545,-0.00056248,-0.26112822,0.02255414,0.02913143,-0.01302883,0.00514974,-0.04102154,-0.02085114,-0.0743682,0.06418446,-0.0728804,0.01305518,0.02634309,0.03480835,-0.00069655,-0.00563443,0.01280017,-0.01713054,0.01837515,0.02617629,-0.00717846,0.01620014,-0.02538145,-0.02276973,-0.01182939,0.04057135,0.01074897,0.01804752,0.0280801,0.07267299,0.01249812,0.05581499,0.06175858,0.05323735,-0.18216342,0.03434494,0.02153192,-0.00111329,0.03998351,-0.04361009,0.00516126,0.08855872,0.0779624,0.01092192,-0.04108196,0.07125248,0.0501456,0.00580188,-0.03615768,-0.06897701,-0.01254576,0.02133557,-0.00455123,0.00082764,-0.02313911,0.00395432,0.02070221,0.01187083,0.0312822,0.0117326,-0.00284699,-0.04317633,0.02626977,0.02176198,0.02672247,0.01535986,0.01455873,-0.012688,-0.00036451,0.02255685,0.02274702,0.1015802,0.00037605,0.0611294,0.03413457,-0.00727314,-0.00392179,-0.0178292,-0.0566377,-0.05635817,-0.0607976,0.00867452,0.08751753,0.05913323,-0.00069556,-0.05464658,-0.02787988,0.01173192,0.04495053,0.02030787,0.00893569,0.0207922,-0.04791604,0.0321691,0.03341623,0.01920165,-0.00009652,0.00724642,0.02789377,0.11143588,0.00606672,0.03219789,0.01096784,-0.02795901,-0.0675489,-0.03959014,-0.02587574,-0.06602076,0.0213355,0.04780208,0.01127799,0.02424399,-0.00290357,-0.11244642,0.04810903,-0.08685171,-0.02307011,0.08726159,-0.02169142,-0.00751726,-0.040735,0.03383703,-0.02663148,-0.02994249,-0.02104478,-0.08256634,0.00732187,-0.00394631,0.03614629,0.02878568,-0.02364956,0.04151618,-0.04431497,-0.02661677,-0.06867178,0.16797332,-0.03900811,-0.09394978,-0.02328432,0.04052642,0.01529499,-0.05148065,-0.00587183,0.02043537,-0.06432872,0.04014512,0.10060428,0.01603485,-0.08670062,0.02418918,-0.00965221,0.03151351,0.02447495,-0.05157812,-0.05888325,0.00484776,-0.03921282,-0.02851501,-0.01540874,0.01287246,0.01772289,0.01662848,-0.02950378,0.01253624,-0.02675475,-0.00054565,-0.01269103,-0.03508942,-0.01529625,-0.02800047,0.0492264,-0.01967344,0.03183834,0.02200055,-0.02810635,0.05098306,0.00404503,0.00854723,0.02350824,-0.05591789,0.10374698,0.05846153,-0.02022002,-0.01521009,0.04565511,0.04643652,-0.03453646,0.03423618,0.0047213,0.02050888,0.03009498,-0.0207434,-0.00884904,-0.03611078,-0.01823282,-0.22224897,-0.0342202,0.03298627,0.02769923,0.04129005,0.01454351,-0.01978715,0.02141433,0.01776068,0.04190957,0.05318831,-0.04321885,-0.01977724,0.01183762,-0.01590111,0.04715345,-0.08765651,-0.0293224,-0.01241452,0.06672137,0.03970014,0.03823661,-0.06442917,-0.13282308,0.06533825,-0.03336076,0.12549524,-0.01042998,0.03923222,-0.00561453,0.05766732,-0.03198627,0.0127673,0.05229005,0.05154232,-0.00499048,-0.07549883,-0.05823817,0.02176161,-0.02487002,-0.05428301,-0.034203,-0.01155015,-0.07471901,0.01628871,-0.07175794,-0.06089588,-0.00684018,0.02679562,0.07176488,0.03596695,-0.0305858,0.038576,-0.0093368,0.0326945,-0.05581426,-0.05166798,0.02279165,-0.02831059,0.0460236,-0.02809038,-0.01870492,0.07475408,-0.02384736,-0.00540033,0.02652837,-0.06952531,0.00378373,-0.02591651,0.01080635,-0.01502685,0.05080724,0.04250148,0.05029074,-0.01086056,0.00566176,-0.03041757,-0.01357042,0.04289361,-0.01480891,-0.04941063,-0.01470448,0.04893682,0.04811795,0.00700696,-0.00356449,0.06158951,0.02036475,0.02261261,0.01652569,-0.00833748,0.08292677,-0.01546543,0.033168,0.04055209,0.00304992,-0.25476578,0.03945477,-0.06218091,0.02580161,-0.03757336,-0.03597884,0.01646974,0.03172828,0.00640569,0.00869904,0.03024041,0.05285098,0.01731869,-0.02737957,0.03289209,-0.01105394,0.04081535,-0.05200413,0.02169461,-0.04113479,0.08905514,0.03856967,0.26444623,0.04886378,0.03735657,0.01968624,-0.04652068,0.02435599,-0.0529642,0.00205013,-0.00524358,-0.02162736,0.07582982,-0.00037256,-0.03887267,0.02657152,-0.01767574,0.02808488,-0.04773136,0.01917812,-0.07731916,0.007405,-0.00541248,0.00326683,0.15484889,0.04342286,0.00943283,-0.12475085,0.08588075,0.01346716,-0.06311176,0.00525106,-0.07943459,-0.03864838,-0.01816327,0.04465122,0.02440342,-0.04498744,0.01220298,-0.01011571,0.03994692,-0.04267059,0.05596082,-0.01370068,-0.0306385],"last_embed":{"hash":"1bxk7lz","tokens":123}}},"text":null,"length":0,"last_read":{"hash":"1bxk7lz","at":1753423596817},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{3}","lines":[49,49],"size":357,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05461442,-0.0358617,-0.0290343,-0.01969057,-0.06231045,0.04971159,-0.00099364,0.01425688,0.03371719,-0.01729064,-0.01423018,-0.00794074,0.07093163,-0.01999863,-0.00174275,0.00242024,-0.01059721,0.06956095,-0.08812705,-0.02032971,0.07410827,-0.01963358,-0.07050085,-0.05346923,-0.01127577,-0.00069518,-0.00684136,-0.05320868,-0.01554806,-0.26904342,0.01674362,0.03647138,-0.00290286,-0.04035833,-0.03096372,-0.02993403,-0.06864293,0.05464016,-0.06038936,0.02535537,0.02291293,0.03948638,0.03612977,-0.0215108,-0.00366601,-0.00921504,0.00997143,0.01683908,-0.01393862,0.01047036,-0.03282105,-0.03518392,0.00429752,0.0410823,-0.00521599,0.01991929,0.01501499,0.08432046,0.01201744,0.05123866,0.07153545,0.04356792,-0.1908263,0.05052148,-0.00688597,-0.00951419,0.04353157,-0.03923323,0.01275571,0.09860736,0.06373239,0.02964592,-0.03878004,0.06688163,0.03503226,0.01483128,-0.02295501,-0.06694589,-0.02257474,0.03244447,-0.0047351,-0.0299459,-0.00767495,-0.02023479,0.02706303,0.01693007,0.02745192,-0.01114568,-0.00799656,-0.02168109,0.02799592,0.03981343,0.01920862,0.01229743,0.01441567,-0.02026237,-0.02097876,0.04739032,0.01618873,0.09473498,0.0085469,0.07351114,0.00991394,-0.03032347,-0.0011401,-0.04390821,-0.04959919,-0.03106097,-0.07570688,0.01909305,0.08188099,0.04668541,0.02291509,-0.04036536,-0.02322046,0.04076774,0.06914892,-0.00295722,0.00199899,-0.00068674,-0.06130173,0.00737747,0.03507752,0.02893201,0.0231917,0.02914398,0.04849065,0.11139767,0.00566113,0.0472371,0.02354517,-0.06456295,-0.08290436,-0.03157574,-0.01644658,-0.0396506,-0.0016868,0.04674933,0.00153139,0.01452342,-0.01235623,-0.11006106,0.03511162,-0.06318246,-0.04903349,0.09788863,-0.00647008,0.00400422,-0.06755909,-0.00754148,-0.02805714,0.00281609,-0.02987086,-0.06509976,0.00356241,-0.00565,0.00048953,0.01126328,-0.05038087,0.04460791,-0.0283019,-0.03037489,-0.07097846,0.16899076,-0.02633525,-0.04768195,-0.02749303,0.02850458,0.0036922,-0.06266163,-0.02962967,0.03334685,-0.06097323,0.03311985,0.08593948,0.01565962,-0.09882292,0.02232581,-0.01958508,0.01716839,0.02865052,-0.03646119,-0.05155198,-0.009155,-0.02197389,-0.02135985,0.00663648,0.01198415,0.03707286,0.02079335,-0.0399326,0.01061401,-0.0146073,0.03730763,-0.01961454,-0.04498429,-0.00014825,-0.04513382,0.04007829,-0.02801953,0.04329255,-0.01005653,-0.01424276,0.04153471,0.00877786,0.02172025,0.01651217,-0.06110569,0.10307609,0.05829277,-0.03868042,-0.02265437,0.04925986,0.02181361,-0.04805168,0.03269907,0.00500481,0.02942524,0.01614338,-0.03416771,-0.01818031,0.00400458,-0.02278861,-0.23008287,-0.02142758,0.03567943,0.04227668,0.04587539,0.01008009,-0.04198228,0.03707269,0.04320441,0.07930057,0.05266172,-0.02344513,-0.02808053,0.03459415,-0.00594908,0.04607491,-0.07178742,-0.01564257,-0.02368329,0.06536765,0.04945549,0.03789759,-0.04688869,-0.11596523,0.06680146,-0.04576656,0.12582657,0.00001784,0.04129927,0.0019251,0.06235042,-0.00615152,0.00957718,0.064492,0.052136,0.01560172,-0.07298584,-0.04700216,0.01619014,-0.03211921,-0.02551907,-0.03562865,-0.0162933,-0.08598577,0.03696888,-0.06997467,-0.07256253,0.00663435,0.02047248,0.05971832,0.06239408,-0.03460517,0.03935439,-0.02207787,0.05499065,-0.05592105,-0.05221139,0.01390429,-0.00754366,0.03352484,-0.04186789,-0.00460734,0.06641461,-0.03140556,0.01734844,0.06379742,-0.06655457,0.01076584,0.00560529,0.02219227,-0.01182815,0.05744975,0.03192707,0.05391303,-0.02481442,0.00432809,-0.04578838,-0.04415144,0.03525808,-0.01153592,-0.0205376,-0.00908957,0.03675413,0.04658474,0.00981149,-0.00227599,0.03922126,0.04460442,0.01934979,0.00684323,-0.00111168,0.09787771,-0.01853333,0.00159714,0.02630027,-0.00607364,-0.25547972,0.007317,-0.05631492,0.00336348,-0.0564509,-0.04344586,0.032942,0.01734601,-0.00030113,0.01159471,-0.02444462,0.05545956,0.02743959,-0.01997282,0.01936058,0.00557959,0.01011643,-0.06224079,0.03305789,-0.0367808,0.0802161,0.05784778,0.26417249,0.0497773,0.03119912,0.01557049,-0.02752481,0.01253253,-0.05803327,0.00535692,-0.00831807,-0.0226912,0.09137364,-0.00410699,-0.01653719,0.01356609,-0.0196061,0.02904526,-0.06226436,0.01199751,-0.08863666,0.01642941,0.02373399,0.0069994,0.15357336,-0.00243651,0.01857778,-0.13140289,0.04293239,0.0168097,-0.06228834,0.02902613,-0.04073747,-0.0583005,-0.02269902,0.04953706,-0.00027052,-0.03559042,0.00294046,-0.01403841,0.02152565,-0.03495409,0.04102106,-0.0050695,-0.01540473],"last_embed":{"hash":"1s7heak","tokens":224}}},"text":null,"length":0,"last_read":{"hash":"1s7heak","at":1753423596867},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{5}","lines":[51,51],"size":760,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04799819,-0.02987902,0.01140905,-0.01682365,-0.05866811,0.08152398,-0.00446765,-0.00669159,0.02365476,-0.04121153,-0.02293206,-0.00323628,0.07403906,-0.00699583,0.02078507,-0.00292622,0.0091966,0.0851469,-0.07741895,-0.04148133,0.08507562,-0.01713038,-0.06046148,-0.06353196,-0.01476808,0.01217936,-0.00814787,-0.03493478,0.00124829,-0.24396536,0.01971291,0.04161187,-0.03269403,-0.00225787,-0.03985209,-0.01996543,-0.05640692,0.06811374,-0.082848,0.02124503,0.03010486,0.03422498,0.02926083,-0.02210317,-0.00878205,0.00815217,0.00899638,0.02687976,-0.01258084,0.00166571,-0.02881566,-0.04114371,-0.00916914,0.02778988,0.00594178,0.01618679,0.01882619,0.06294496,0.00675042,0.06238738,0.06620034,0.04301148,-0.1724477,0.05747657,0.01225791,-0.00539845,0.04536973,-0.05210311,0.02303866,0.10231805,0.05306762,0.00666857,-0.06246824,0.07345477,0.04442362,-0.00355043,-0.01396143,-0.07137923,-0.0449294,0.02122477,0.00909092,-0.03310162,-0.01267595,-0.00055186,-0.00422908,0.00423504,0.02120856,-0.003009,-0.00917853,-0.06105717,0.03094287,0.02500253,0.02963384,0.01350891,0.01082556,-0.01542998,-0.0195526,0.0457834,0.01836836,0.11026128,0.01118221,0.0584984,0.02984202,-0.00640323,-0.00539377,-0.03340534,-0.0278389,-0.03358029,-0.06193166,0.04002338,0.06251338,0.04135932,-0.00560663,-0.03905731,-0.01110839,0.03078822,0.05519088,0.0273755,-0.02278041,0.01382936,-0.04404302,-0.0014561,0.05479028,0.03657584,0.02571602,-0.01161078,0.03802751,0.10826285,0.00808037,0.04421671,0.02491862,-0.04628896,-0.03641458,-0.05442112,-0.00416832,-0.03294775,0.00577444,0.05173431,0.01786449,0.0323193,-0.03857387,-0.08126243,0.04741536,-0.07134911,-0.02603209,0.10591485,-0.00577554,0.01828255,-0.05447709,-0.00296222,-0.0283603,0.00078036,-0.00775182,-0.07218917,0.01530707,-0.00346228,0.03807658,0.00808919,-0.04464336,0.05655009,-0.06049777,-0.00825225,-0.08825213,0.18146408,-0.04145885,-0.05910727,-0.02177447,0.01943627,0.01852213,-0.03630735,-0.03716183,0.02260872,-0.04989783,0.01615263,0.10405087,0.02909349,-0.10132832,0.02672117,-0.00988042,0.01453116,0.01100269,-0.04688386,-0.03553073,-0.01050608,-0.03002456,-0.03297824,0.01485536,0.02631092,0.01890746,0.02101831,-0.05675444,-0.00532966,-0.01681051,-0.01135077,-0.03471859,-0.03766471,-0.00760709,-0.02102606,0.02869347,-0.03170293,0.02515552,-0.00963279,-0.01561316,0.04590632,0.01566796,0.01321826,0.00178768,-0.04074771,0.10999805,0.06323948,-0.04556493,-0.00728683,0.06580815,0.02486593,-0.03115223,0.04026753,0.00079147,0.02257338,0.02514863,-0.040419,-0.00959892,-0.02971292,-0.0043026,-0.24076083,-0.02123784,0.03352881,0.0635638,0.04949378,-0.01492559,-0.04283705,0.03620844,-0.00561947,0.07648321,0.02297342,-0.03348765,-0.05017726,0.01803958,0.00216272,0.0627522,-0.06677307,-0.03067695,-0.02227449,0.05981942,0.043476,0.04163983,-0.05943836,-0.13693915,0.06627302,-0.0247939,0.11142831,-0.03567196,0.04806253,0.0002148,0.04762844,-0.02779741,0.03525966,0.06364197,0.04039027,-0.01644477,-0.07905363,-0.06766758,0.0437157,-0.04856673,-0.01825737,-0.01302689,-0.01884933,-0.07088716,0.04026685,-0.04374357,-0.0456299,-0.00552118,0.02489013,0.04031722,0.05301271,-0.06208516,0.0199883,-0.01393404,0.01855758,-0.06662802,-0.0360094,0.00968914,-0.04114939,0.02200288,-0.03783303,-0.00822377,0.05926394,-0.00836302,0.00717428,0.0499351,-0.06505223,0.02841575,-0.023021,0.03178516,-0.00227918,0.0778365,0.03221348,0.04482782,-0.05273996,0.00213406,-0.06266607,-0.00011198,0.0254319,-0.03427568,-0.02893765,0.00497265,0.04033462,0.05812891,0.00294331,0.00547006,0.05110672,-0.00932537,0.01470617,0.01915821,-0.0062383,0.06737301,-0.00861658,0.01872696,0.04858972,-0.02432781,-0.26296785,0.00999609,-0.0624153,0.01240445,-0.05004226,-0.03543108,0.01315491,0.03601581,0.01255947,-0.00049964,0.00277245,0.0751498,0.03171045,0.00149462,0.01604456,-0.00297898,0.04004059,-0.04945284,0.04065049,-0.0711365,0.08672971,0.02859483,0.27271935,0.04749471,0.04278519,0.02107359,-0.03383375,0.00639716,-0.05843031,0.01687874,-0.00655747,-0.02404475,0.08727483,0.00595701,-0.00468013,0.04484541,-0.02693776,0.0659925,-0.03592211,0.02537476,-0.07200289,-0.00004328,0.01040078,0.01853012,0.14061552,-0.003557,0.00692353,-0.13147154,0.02988627,0.01402939,-0.0541251,0.03108263,-0.06505171,-0.03856498,-0.00249668,0.03951618,0.00185619,-0.04545626,-0.00994744,-0.02009414,0.03980712,-0.06473312,0.02731612,-0.00666787,-0.01699122],"last_embed":{"hash":"6phtru","tokens":101}}},"text":null,"length":0,"last_read":{"hash":"6phtru","at":1753423596937},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>#{6}","lines":[52,52],"size":249,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07076479,-0.05917656,0.0349282,-0.02800459,-0.02093833,0.06746737,-0.03760007,-0.01280014,0.04996218,-0.03813655,-0.01019172,0.00330211,0.03938523,-0.01659918,-0.03151019,0.02837333,0.04742304,0.02858416,-0.11331225,-0.0502648,0.0340858,-0.05642344,-0.03628349,-0.09613851,0.00665258,0.01749103,-0.07269998,-0.01428095,-0.02344058,-0.25734365,0.0327117,0.01825664,-0.05029294,-0.05291991,-0.05090262,-0.04062946,-0.03505448,0.03212567,-0.07106443,-0.00542049,0.03795443,0.02681749,-0.02412487,-0.01402933,0.01744538,-0.03558834,-0.0132431,0.0022338,0.01425245,0.0267534,-0.04377044,-0.04659658,0.03091065,0.00361235,0.0117782,0.01834706,0.05174618,0.10280566,0.02365055,0.06096945,0.03551412,0.07918599,-0.15142985,0.04285304,0.01028935,-0.02764292,-0.01707239,-0.08711546,-0.01073756,0.08623734,0.03125546,0.01846679,-0.01037405,0.10806163,0.02118175,-0.03558685,0.01111749,-0.08841719,-0.07645845,0.04314857,-0.04795646,-0.01797899,0.00229487,0.0448753,0.00149124,-0.00811708,0.03608211,0.04471933,0.01130489,-0.07739007,0.0471037,0.03379731,0.03875424,0.03287343,0.0006754,0.0106721,0.04019781,-0.01027177,0.01849393,0.09964567,-0.00465865,0.0201193,0.04456552,0.01656589,0.02607347,-0.00964417,-0.03378348,0.00421403,-0.07632741,-0.00054557,0.08901922,0.00602063,0.08003967,-0.04308648,-0.03778977,0.01410013,-0.02482937,0.0222587,-0.01637855,-0.01044673,-0.02001865,0.04706417,0.02550031,0.02442471,-0.01569063,-0.01951937,0.0613908,0.06567499,0.02797782,0.03881096,0.03975795,-0.00700344,-0.0431586,-0.02717453,0.01444678,-0.02108007,-0.0189192,-0.00196845,0.01595901,0.01099709,0.02591028,-0.05658003,0.06415486,-0.08969468,-0.01827011,0.09565765,-0.05370149,-0.01421374,0.02897313,-0.01277057,-0.00138889,-0.00624887,-0.02587061,-0.02487963,0.04167477,0.00719912,0.13297656,0.07637464,-0.07196253,0.02709079,-0.07893124,-0.03420494,0.00727911,0.11844397,-0.05520668,-0.06355496,0.00615832,0.07720587,-0.00095121,-0.06445746,-0.0249529,0.01686579,-0.00864858,-0.01274773,0.07785329,0.00646031,-0.05497985,-0.0043267,-0.0033788,0.02249108,0.03568865,-0.05008971,-0.06609456,0.00101976,-0.02349037,-0.06621251,-0.01469752,0.03963212,0.0196068,0.0526213,-0.09011541,0.0240373,-0.01653709,-0.03830637,-0.03209337,-0.06535182,-0.03807532,-0.03854641,0.01429964,-0.03705085,0.02877542,0.00635495,0.01367393,0.06115507,0.00193988,0.01424992,-0.00967141,-0.05980336,0.07134134,0.03119254,-0.05948234,-0.02539631,0.05088507,0.08574828,-0.09770224,0.0495315,-0.01143493,0.06443172,0.01244459,-0.04796363,0.02363441,-0.01372241,-0.01025924,-0.21796493,-0.0116102,0.00831952,0.04935494,0.0545596,-0.02651431,-0.02343514,-0.0044738,0.01425509,0.03194262,0.09133099,-0.05578132,0.01148623,0.01582421,-0.00270403,0.01485169,-0.08290552,-0.02150146,-0.00830571,0.06231355,0.05577919,0.01495409,-0.01499277,-0.11551906,0.06885067,-0.03146365,0.13374873,0.00218304,0.0319002,-0.04528823,0.0594453,-0.00582409,-0.002603,-0.00448105,0.02368974,0.0266814,-0.06991158,-0.01443723,-0.01117954,-0.03800748,-0.02568527,0.02919409,0.00090262,-0.09980153,0.03489917,-0.04314658,-0.00576087,0.00782148,-0.0126114,0.05616156,0.04609886,-0.02719447,0.00552819,0.00700581,0.07646477,-0.06825095,-0.02861259,0.01172271,-0.02647423,0.02697652,0.01108586,-0.04741238,0.0659146,-0.03673093,0.05707168,0.02051821,-0.01683389,-0.03536259,0.00789539,0.02626707,-0.00219445,0.03931572,0.01708894,0.092059,-0.02707316,0.03833405,0.03410702,0.02579861,0.03158364,-0.02051308,-0.00368741,-0.00450277,0.03518838,0.08705691,-0.00763881,-0.02637504,0.06313865,0.02076595,-0.01092095,0.03203663,0.02355361,0.03947954,-0.0200881,0.013861,0.03549631,0.03216579,-0.2580449,-0.00333913,-0.06323194,0.00757619,-0.04035997,0.00266412,0.04361821,0.00404692,0.00182825,-0.01746489,-0.00027893,0.08397835,0.04518005,-0.05430037,0.03387069,-0.03092699,0.05316887,-0.0041106,0.01844021,-0.04404986,0.07598293,0.03220539,0.23834452,0.00940907,-0.01894184,0.04769085,-0.01735475,-0.00213091,-0.01442406,0.00953531,-0.02260847,0.00354503,0.04822636,-0.00223942,-0.00103244,0.02701177,-0.05196996,0.03950789,-0.01950871,0.01861861,-0.05165616,-0.0226173,-0.00546861,-0.00280249,0.12356231,0.00003654,0.01092861,-0.08564354,0.00803157,0.0400858,-0.08925093,-0.05099842,-0.07244138,-0.10033919,-0.00717498,0.03155822,0.00799084,-0.01303615,0.01383492,0.00718219,0.05290939,-0.05638609,0.10878099,0.02202566,-0.04342191],"last_embed":{"hash":"1n7wyh","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"1n7wyh","at":1753423596975},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>","lines":[57,80],"size":2856,"outlinks":[{"title":"Lottery utility software has a function to wheel in combinations that satisfy minimum guarantees.","target":"https://saliu.com/ScreenImgs/positional-wheel-utility.gif","line":11},{"title":"Special lottery wheeling software creates efficient lotto wheels from input files.","target":"https://saliu.com/ScreenImgs/positional-wheel-program.gif","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08043968,-0.04483135,0.06804011,-0.03092308,-0.00134154,0.06495801,-0.04416463,-0.01458575,0.05801038,-0.02662574,0.00276605,-0.0109731,0.02453691,-0.02396785,-0.03088626,0.01082693,0.02056023,0.04796593,-0.10382819,-0.0544972,0.03331937,-0.0354204,-0.07408969,-0.10031884,-0.03016634,0.03808274,-0.07011392,0.00608888,-0.01216176,-0.22014281,0.01904636,0.04789863,-0.03936862,-0.02840364,-0.03305093,-0.03131571,-0.03437746,0.02707717,-0.07334246,0.00096646,0.03754929,0.02424064,-0.01056444,-0.00743708,0.01825684,-0.03598487,-0.00160021,0.03519532,0.03709765,0.00504739,-0.03703234,-0.0560464,0.03112291,-0.02835294,0.02638278,0.01298026,0.0556744,0.09397558,0.02761144,0.06064806,0.04623539,0.07566678,-0.16949008,0.04738292,0.00372519,-0.04027227,-0.00632765,-0.07354251,0.0096798,0.06997544,0.01295797,-0.00500655,0.01363673,0.08084062,0.04489116,-0.04965449,0.00275565,-0.10153618,-0.05949052,0.02291066,-0.0324188,-0.00778167,-0.0242231,0.05311733,0.00952782,0.01367936,0.03881872,0.04423376,0.03008492,-0.05868868,0.02057557,0.00466582,0.04633631,0.03397207,0.00525771,-0.00118056,0.03799972,0.01000455,0.03902433,0.11257864,-0.01907017,0.03267189,0.06533922,0.00718698,0.00186631,0.00091693,-0.01637543,0.02685358,-0.0775902,0.00988761,0.07275396,0.01765301,0.05416681,-0.06007474,-0.01871255,-0.00026676,-0.05517677,0.04123075,-0.03612601,-0.00983107,0.00458499,0.03122626,0.02755254,0.02573696,0.00682279,-0.02473291,0.0701209,0.06999247,0.03426806,0.04411022,0.0450413,0.01044738,-0.03727519,-0.01619809,-0.00151302,-0.02944207,-0.00451032,-0.01007225,-0.00783737,0.01995817,0.02785118,-0.03923063,0.05081966,-0.0929149,-0.00376417,0.10947017,-0.06210614,-0.01909984,0.02944689,-0.00139351,0.00316308,-0.01628509,0.00549636,-0.01406685,0.03471769,0.02152723,0.12847494,0.07101723,-0.07160829,0.03368986,-0.0828916,-0.04124995,0.00492484,0.10389808,-0.05706345,-0.03663374,-0.00933054,0.0925943,-0.00059726,-0.08062568,-0.02433522,0.02679445,-0.02547282,0.00214284,0.09329899,0.01534302,-0.02425677,-0.00112769,0.00011053,-0.00905696,0.06634315,-0.08229615,-0.04852098,0.00091022,-0.01058766,-0.05391962,0.00553922,0.02536816,0.01584215,0.08846372,-0.05351382,0.03452215,-0.01610074,-0.07121326,-0.06441225,-0.06691409,-0.02910169,0.00831254,0.0096636,-0.0586683,0.07592192,0.01597183,0.00219602,0.07352066,0.01385405,-0.01873975,-0.02403527,-0.0298661,0.05764944,0.00133901,-0.06442314,-0.01559325,0.07083949,0.0645603,-0.11002643,0.02265864,-0.03858143,0.06909305,0.01439455,-0.0511768,0.02441403,-0.04681976,-0.03193173,-0.21813811,0.01808091,0.04871595,0.03912066,0.02142302,-0.0130164,-0.0436231,0.01224465,0.04450646,0.02464939,0.08338878,-0.03028619,0.01701411,-0.02222129,-0.02393742,0.01664691,-0.04630624,-0.02283246,0.009946,0.04299859,0.07321864,-0.01752944,-0.02497384,-0.1188318,0.09369353,-0.05888002,0.12026812,0.02615527,0.09075085,-0.05169272,0.05400767,-0.01228887,0.01196259,-0.01207739,0.01206636,0.01448916,-0.07176805,-0.02341327,-0.00383117,-0.04568556,-0.03102262,0.00006989,0.00349961,-0.09126896,0.07672178,-0.05074016,0.004651,-0.04971337,-0.00466983,0.0312611,0.05106234,-0.02964892,0.00699983,0.02509362,0.0456631,-0.08357548,-0.01201519,0.00538152,-0.03529849,-0.00450061,0.00811809,-0.04466657,0.06081751,-0.03501028,0.03049209,0.03474433,-0.03055523,-0.04631598,0.0161167,0.0361262,-0.00504179,0.03313403,0.03872762,0.09120091,-0.0309814,0.02168179,0.01860579,0.02264645,0.02713065,-0.02052012,-0.03048127,0.0266753,0.03769362,0.0748267,-0.0080489,-0.05090587,0.04914935,-0.02480149,0.00558959,0.01679565,0.00386943,0.05026328,-0.02602237,0.01525897,0.00769799,0.04393193,-0.26731017,-0.00508996,-0.04291896,0.0129624,-0.022587,0.01955335,0.04499532,0.00537809,-0.00503549,-0.00309101,-0.00284948,0.05908798,0.03785625,-0.06663238,0.0187708,-0.03173953,0.06572767,0.00568241,0.0478103,-0.05420329,0.05484858,0.00917045,0.22935568,0.02736536,-0.00499802,0.03617546,-0.05574294,-0.03370516,-0.00676988,0.01256014,-0.00138763,0.00869663,0.06501972,0.01811981,0.00419907,0.05054541,-0.03333729,0.03342994,-0.02898916,0.00103858,-0.03057278,-0.0086302,-0.02167848,-0.01308117,0.09904566,0.00411194,0.00437848,-0.09136072,-0.00139785,0.02916036,-0.08738948,-0.02706936,-0.1011164,-0.05478525,0.01311104,0.02306412,-0.01296805,-0.02283198,0.00637676,-0.00935114,0.04987671,-0.08866484,0.11185569,0.00169896,-0.01845646],"last_embed":{"hash":"p2qd7o","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"p2qd7o","at":1753423597224},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{2}","lines":[60,60],"size":206,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06253599,-0.04879025,0.02586307,-0.04576391,-0.03067344,0.08276802,-0.0206006,0.00073106,0.03547367,-0.02323297,-0.0264706,-0.00966049,0.02680576,-0.01990991,-0.03402921,0.03258969,0.04384168,0.02918331,-0.10756733,-0.03235145,0.05926767,-0.07480762,-0.02323282,-0.11585108,0.02366309,0.02348829,-0.05578406,-0.00813408,-0.00629558,-0.26170006,0.03207876,0.02071023,-0.04773876,-0.04053964,-0.06025252,-0.03742116,-0.0367499,0.03672433,-0.06403674,-0.03021284,0.02940046,0.03975993,-0.01600791,-0.00981067,0.00869639,-0.05301891,0.01183108,-0.00614819,0.01460307,0.01075711,-0.01906737,-0.04460645,0.02343373,0.0021337,0.01786129,0.01787017,0.05245905,0.07100302,0.00597216,0.03340595,0.027173,0.07604047,-0.17620091,0.04997698,0.01921096,-0.01481929,-0.00808767,-0.07687726,-0.01284284,0.07364575,0.04525102,0.00867591,-0.02023757,0.10678353,0.0159842,-0.03501792,0.01050985,-0.0911833,-0.08266649,0.02749703,-0.02952017,0.00566115,-0.00481471,0.04586758,0.00433974,0.01280017,0.0333531,0.04668467,0.01844366,-0.08244432,0.06038595,-0.00002495,0.03737289,0.04576663,-0.02566352,0.02313613,0.03159233,-0.00146521,-0.00580962,0.09052021,-0.00168647,0.02447227,0.03544344,0.00397278,0.03829946,-0.01312735,-0.0399964,-0.01057707,-0.08067803,-0.00380421,0.08820455,0.03183096,0.05338791,-0.04011576,-0.05142104,0.01489353,-0.01448558,0.00851843,-0.02309548,0.00442268,-0.01847521,0.04757379,0.03266842,0.02449311,-0.01587401,-0.02979637,0.06148101,0.06080274,0.02075655,0.03981461,0.03779314,-0.01624678,-0.03838502,-0.01769808,0.01429212,-0.03124761,-0.00926982,0.00421901,0.00940667,0.0176978,0.00305672,-0.0603088,0.05833929,-0.08985572,-0.00202676,0.09728572,-0.04425117,-0.02927346,0.02070108,-0.00969155,-0.00328084,-0.00183967,-0.03672872,-0.01010457,0.0434096,0.01622954,0.08567224,0.0657298,-0.08218853,0.02438266,-0.07858214,-0.04101923,-0.01664818,0.12616608,-0.04290074,-0.09234327,-0.01025924,0.07136212,-0.00351303,-0.04629184,-0.00715371,0.02437253,-0.0292285,-0.03865254,0.08108827,-0.00010314,-0.06049794,0.01444014,-0.00940241,0.0269889,0.02273789,-0.05017239,-0.05527733,-0.02055319,-0.01953153,-0.05155556,-0.0145437,0.01415955,0.01011953,0.04400365,-0.08326633,0.01945312,-0.00727567,-0.03543776,-0.02756532,-0.05221958,-0.03272461,-0.0385022,0.03447162,-0.02925214,0.02564013,0.02540245,0.0013436,0.04918615,-0.0050141,0.01373483,-0.02752035,-0.04579868,0.0795397,0.02553424,-0.0617721,0.00381909,0.07297329,0.08088569,-0.11207991,0.03881328,0.00385438,0.04212457,0.01472358,-0.02696957,0.03443112,-0.00246532,-0.00538667,-0.21728751,-0.00486104,-0.01213089,0.0498677,0.05226096,-0.02888675,-0.00864008,-0.0041097,0.01092839,0.02421836,0.11471947,-0.05579136,0.01090873,0.02994269,-0.01537996,0.03005906,-0.08239311,-0.03091387,-0.0129634,0.06463787,0.06117893,0.00602761,-0.02708166,-0.11940876,0.07592465,-0.0184452,0.14479385,0.00603792,0.04315298,-0.03394944,0.07391354,-0.01660694,0.01440575,-0.00164434,0.01835336,0.02538215,-0.0651919,-0.02789676,0.00030321,-0.05433588,-0.01921832,0.03571047,-0.00704858,-0.09516348,0.03754164,-0.03454691,0.00238564,0.01497714,-0.02414287,0.06398354,0.05730437,-0.02173503,0.00495643,0.00123066,0.06645582,-0.06636368,-0.03008269,0.01331366,-0.03028865,0.04540835,0.02665721,-0.02695964,0.05892042,-0.01370482,0.05842126,0.02542406,-0.04067611,-0.03640028,-0.00833474,0.03331468,-0.00640487,0.01946881,0.01184128,0.09882469,-0.03852628,0.02569325,0.03143479,0.03049597,0.04336353,-0.03492345,-0.03291078,-0.0084473,0.02940643,0.07588422,0.00765924,0.01292128,0.05793995,0.00788904,-0.00259291,0.02833357,0.00088887,0.03684106,-0.00104857,0.00389403,0.04947573,0.0271404,-0.25604635,0.01612023,-0.06393244,0.01488527,-0.06269813,-0.01562425,0.04305455,-0.00774183,0.00591765,-0.01639248,0.04062009,0.08260742,0.0323972,-0.03676705,0.05116455,-0.03623839,0.04341602,-0.00801993,0.00150003,-0.04270174,0.07410426,0.05929318,0.24853368,0.01909761,-0.00564755,0.02672139,-0.03327164,-0.01429657,-0.01681885,0.01598518,-0.02457474,-0.00449049,0.06558703,0.0009248,-0.01743494,0.0408857,-0.05498913,0.02593523,-0.01037681,0.03752931,-0.05980815,0.00036485,0.00154288,-0.00097956,0.13310196,0.0005958,0.01098084,-0.08779814,0.03065697,0.02695489,-0.07403853,-0.04937936,-0.06541016,-0.10199937,-0.00398917,0.02391366,0.0230161,-0.03417036,-0.00515395,0.01734404,0.0469937,-0.02787087,0.09821522,0.01374547,-0.03819595],"last_embed":{"hash":"bnvvcp","tokens":496}}},"text":null,"length":0,"last_read":{"hash":"bnvvcp","at":1753423597271},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>STEP 2: Wheel the output file generated at Step 1</u>#{4}","lines":[63,80],"size":2317,"outlinks":[{"title":"Lottery utility software has a function to wheel in combinations that satisfy minimum guarantees.","target":"https://saliu.com/ScreenImgs/positional-wheel-utility.gif","line":5},{"title":"Special lottery wheeling software creates efficient lotto wheels from input files.","target":"https://saliu.com/ScreenImgs/positional-wheel-program.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02858636,-0.04512542,0.02372245,0.0041295,-0.08747334,0.06475524,-0.01891921,0.03430061,0.06965728,-0.01439318,-0.00279803,0.01271614,0.04791419,0.01701648,-0.0402291,-0.029275,-0.00256406,0.00493735,-0.09542003,0.03179436,0.06664002,-0.08786083,-0.05214658,-0.0475901,0.01065571,0.05206069,-0.04655209,-0.03869372,-0.00981706,-0.2721549,0.02933769,0.02716188,0.02071444,-0.01582864,-0.06546666,-0.05194835,-0.08326809,0.09263435,-0.04546316,0.01284403,0.00767818,0.04926774,0.0279021,0.01685676,0.02309691,0.01563865,-0.0508503,0.01927711,0.01725795,-0.00136555,-0.03246378,-0.02225176,0.00710883,0.03080625,0.01931147,0.02307087,0.02426185,0.06670353,0.03886231,0.062308,0.05526996,0.02694609,-0.20066449,0.02850621,0.01091336,0.01350444,0.00281316,-0.0514243,-0.02126163,0.08076138,0.03860699,-0.02422079,-0.00779484,0.05418125,0.00412166,-0.01102774,-0.03699845,-0.06382789,-0.05168845,0.01048161,-0.02297698,0.00472186,-0.01542416,-0.03264182,-0.03467102,0.01460533,0.07446657,0.00774447,0.0218044,-0.06768902,0.02268376,0.03101166,0.0306002,0.02891008,-0.03362724,0.00112862,0.00877754,0.00179045,0.00354013,0.09840677,0.0267,0.05024597,0.04428742,0.00927226,0.02330748,-0.0092538,-0.05530538,-0.02783563,-0.06535705,0.01466998,0.09735246,0.05481337,-0.00365382,0.00858311,-0.02205493,-0.00186793,-0.00210649,0.02565288,-0.0133742,0.01082707,-0.05845502,0.00192177,0.0321333,-0.01184079,-0.01696021,0.02902268,0.04468011,0.06108951,0.01496396,-0.00465234,0.03951063,-0.03068918,-0.05654896,-0.04549123,-0.01375377,-0.03647528,0.02257471,0.0560548,0.05960489,-0.02299223,-0.00056453,-0.05046907,0.02122544,-0.08053048,-0.04509193,0.09116027,-0.04705458,-0.00760698,0.03073797,0.01583854,-0.01626385,-0.02256956,-0.06926682,-0.04222813,0.02397139,0.01550626,0.06069326,0.08135841,-0.02870868,0.03559919,-0.02861843,-0.04874605,-0.0579357,0.14596979,-0.04771235,-0.09509958,-0.00791004,0.04873258,-0.00378764,-0.06536665,-0.03846717,-0.00281024,-0.06150832,0.05632243,0.0805135,-0.00637165,-0.09471639,0.03098906,-0.02640605,-0.01682577,0.01012516,-0.03854024,-0.04207126,0.00328991,-0.02394293,-0.05822087,-0.00376416,0.02096,0.00151147,0.04672488,0.00162282,0.05518245,-0.0001332,0.05351947,-0.00778818,-0.05470926,0.02340704,-0.03433892,0.02502839,-0.03395107,0.02795153,0.00664742,-0.0138928,0.02296407,-0.01130793,0.0517876,0.01358785,-0.06371546,0.0812989,0.05620086,-0.06016105,-0.02539662,0.01671473,0.06238021,-0.00451002,0.04264018,0.03271685,0.0272439,0.03679763,-0.03459894,0.00964448,-0.0341627,-0.02724406,-0.19965704,-0.06958827,-0.01576194,0.03577089,0.06367155,0.01204488,-0.03075693,0.02016001,0.03371214,0.05278514,0.10545786,-0.01860807,-0.02442588,0.03426108,-0.02813668,0.04968985,-0.09396237,-0.02091935,-0.03654182,0.0471405,0.05861169,0.06346598,-0.05304429,-0.09851786,0.03884725,-0.04461475,0.1558238,-0.01884037,-0.02194922,-0.01513744,0.06380498,-0.03614971,0.0373306,0.04747372,0.02743915,-0.00755299,-0.11199422,-0.06829613,-0.05695329,-0.05551278,-0.07524116,0.000566,-0.01807324,-0.10858618,0.00394176,-0.01195994,-0.02282732,0.0046888,-0.01032142,0.07043421,0.05384086,-0.02351206,0.03335853,0.01222812,0.05340282,-0.01840144,-0.08283816,-0.01287986,-0.03311365,0.06250527,-0.02639592,-0.0467994,0.08858946,-0.0203526,0.01530874,0.07558176,-0.04952627,-0.02155049,-0.01911453,-0.00464965,-0.03304913,0.04711128,0.06149234,0.03950853,-0.02989856,0.01259554,0.006145,0.00164961,0.03114441,-0.0017828,-0.02624159,-0.03456728,-0.00302018,0.06429667,0.03047306,0.03567867,0.05185454,0.00511005,-0.00022868,-0.00829225,-0.00471781,0.02309532,-0.01334186,0.01193193,0.00939324,0.00374523,-0.23610844,0.05518992,-0.07298038,0.044617,-0.03429548,-0.00108306,0.020937,0.08122412,0.03873711,-0.04789011,0.04891877,0.07117615,0.01846956,-0.02009459,0.03833352,-0.02317963,-0.01898409,-0.02756045,0.05746868,-0.00408581,0.05587837,0.04326203,0.21408686,0.04318217,0.0196662,0.0130907,-0.03883878,0.02220688,-0.04406137,0.02657192,-0.01905293,-0.03892866,0.08082309,0.01878259,-0.03254433,0.00416398,-0.00682609,0.01506113,-0.04177987,0.05055886,-0.05736036,-0.00893349,0.01536264,0.02390342,0.18050936,0.03047971,0.00409801,-0.1145118,0.0673942,0.00921634,-0.02932111,-0.03242524,-0.0784173,-0.05040685,-0.02964124,0.0226242,0.00463113,-0.02086708,-0.02159877,0.00769614,0.03685393,-0.02684802,0.0212521,0.00705369,-0.01395383],"last_embed":{"hash":"1og5eyp","tokens":414}}},"text":null,"length":0,"last_read":{"hash":"1og5eyp","at":1753423597456},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>","lines":[81,101],"size":2047,"outlinks":[{"title":"The best lotto wheels are created on-the-fly by purging files generated from positional systems.","target":"https://saliu.com/ScreenImgs/on-the-fly-wheeling.gif","line":20}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0277412,-0.04239593,0.02394562,0.0061801,-0.08661415,0.0646612,-0.01879542,0.03567987,0.07321744,-0.01228345,-0.00044104,0.01418741,0.04526915,0.01633221,-0.04226355,-0.02837509,-0.00421469,0.00578993,-0.09462076,0.03071248,0.06797812,-0.08567202,-0.05256666,-0.04716579,0.00985069,0.05113386,-0.04747418,-0.04073355,-0.01032424,-0.27043584,0.03071582,0.02146145,0.02134925,-0.0151293,-0.06340494,-0.05461785,-0.08508044,0.08878113,-0.04525565,0.0105699,0.00861222,0.04615966,0.03101243,0.01663875,0.02199493,0.01523229,-0.05553365,0.02196661,0.0179101,-0.00054092,-0.0305287,-0.02305163,0.00997448,0.03172734,0.01833841,0.02006677,0.02476364,0.0675717,0.03633665,0.06287574,0.05131145,0.02704571,-0.20180069,0.02538639,0.01114016,0.01487832,0.00455583,-0.05133016,-0.01860005,0.08017794,0.0369642,-0.02498195,-0.00614619,0.05250593,0.00268383,-0.01137142,-0.03788099,-0.06786849,-0.05418512,0.01186095,-0.02056467,0.00140705,-0.01378974,-0.03577771,-0.03436824,0.01233438,0.07674988,0.00480214,0.02093896,-0.07051357,0.02139406,0.0331736,0.03056578,0.03054987,-0.03437686,-0.00268953,0.00835178,0.00105546,0.00361827,0.09745632,0.02605099,0.04935736,0.0453426,0.00872531,0.02289717,-0.01305278,-0.05998918,-0.0292053,-0.06398521,0.01524424,0.09839854,0.05500258,-0.00362364,0.00740108,-0.02074197,-0.00260787,-0.0024147,0.02804717,-0.01086107,0.01270546,-0.05517857,0.00102858,0.0357669,-0.01135242,-0.01654289,0.03093751,0.04676806,0.06028311,0.01441253,-0.00312803,0.0431027,-0.03445961,-0.05953216,-0.04581949,-0.01156321,-0.03584844,0.02188094,0.0570169,0.05933502,-0.02153812,0.00159024,-0.04947607,0.01891622,-0.07990145,-0.0467959,0.09143861,-0.04727551,-0.00834435,0.02942564,0.01594276,-0.01490539,-0.022149,-0.0699273,-0.04132838,0.02183737,0.01730163,0.06020025,0.08175354,-0.02815848,0.03459454,-0.03026381,-0.05018013,-0.05671636,0.14474043,-0.04547861,-0.09455834,-0.0057597,0.04820915,-0.0048014,-0.06536497,-0.03868644,-0.00215542,-0.06111743,0.05583287,0.07974613,-0.0099043,-0.09439585,0.03103049,-0.02723667,-0.01577002,0.00914907,-0.03820205,-0.04002472,0.00404294,-0.02069464,-0.05785752,-0.00453372,0.02233864,0.00077462,0.04468766,-0.00281685,0.05708151,0.00027194,0.0565308,-0.00643372,-0.05163908,0.02236626,-0.03148318,0.02417893,-0.03379085,0.03074985,0.00758217,-0.01197878,0.02211812,-0.01035913,0.05219346,0.01241946,-0.06198384,0.07981385,0.05257947,-0.06204978,-0.02648995,0.01242863,0.06360212,-0.00012328,0.04345035,0.03043517,0.02790204,0.03372379,-0.03797912,0.01044512,-0.03323284,-0.02621002,-0.1987723,-0.07276336,-0.01459808,0.0333217,0.06293051,0.01508522,-0.03489544,0.01804444,0.03377197,0.05442967,0.10463396,-0.01512185,-0.02491267,0.03795579,-0.02423948,0.05307533,-0.09452799,-0.01843623,-0.03549931,0.04805296,0.06172144,0.05895709,-0.05082969,-0.09671172,0.03706104,-0.04230671,0.15468669,-0.02037906,-0.02556855,-0.01503695,0.06371641,-0.03792487,0.03616738,0.05260821,0.03036785,-0.00896148,-0.11248224,-0.07112946,-0.05616179,-0.05668041,-0.07718614,0.0022006,-0.0188338,-0.10761905,0.00465267,-0.00772053,-0.02066686,0.00543418,-0.00627093,0.07078871,0.05589034,-0.02173689,0.03316007,0.01295226,0.05522697,-0.02152643,-0.08416744,-0.01227751,-0.0330285,0.06157204,-0.0270144,-0.04890023,0.08785228,-0.01853396,0.01902013,0.07263817,-0.05079902,-0.02068334,-0.01786997,-0.00360172,-0.0372428,0.04619614,0.06246322,0.04060222,-0.02801466,0.00995587,0.0054109,0.00267253,0.02767615,-0.00150024,-0.02514637,-0.03062458,-0.003121,0.0666861,0.02930825,0.03601392,0.05442662,0.00400432,-0.00409073,-0.00697261,-0.00312241,0.02091044,-0.01291756,0.01573217,0.00865955,0.00281017,-0.2380272,0.05316364,-0.07465683,0.04272399,-0.03355955,-0.00122684,0.02147373,0.08270086,0.03633396,-0.04686181,0.04917588,0.07000897,0.01991859,-0.0209368,0.03830571,-0.02459528,-0.02276515,-0.02407446,0.06050462,-0.00137415,0.05570745,0.04672977,0.2130491,0.04413835,0.01821075,0.01419357,-0.03966332,0.02502998,-0.04493072,0.02581061,-0.01648866,-0.03843203,0.07982515,0.01664122,-0.03353804,0.00610631,-0.00485513,0.01521934,-0.0393623,0.04839556,-0.05595917,-0.01006646,0.01276746,0.01936378,0.18179874,0.03002143,0.00238226,-0.11675557,0.0696588,0.00903356,-0.02881472,-0.03073498,-0.07822595,-0.04881229,-0.02808216,0.01996331,0.00371275,-0.02025132,-0.01958733,0.00498719,0.03528919,-0.02340064,0.01860835,0.0053049,-0.01541767],"last_embed":{"hash":"pxsxgc","tokens":415}}},"text":null,"length":0,"last_read":{"hash":"pxsxgc","at":1753423597660},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling##<u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>#{1}","lines":[83,101],"size":1971,"outlinks":[{"title":"The best lotto wheels are created on-the-fly by purging files generated from positional systems.","target":"https://saliu.com/ScreenImgs/on-the-fly-wheeling.gif","line":18}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07847983,-0.03981663,0.01220592,-0.02580798,-0.05951682,0.06291398,-0.01076451,0.01460401,0.04042599,-0.0117068,0.01051154,-0.02662149,0.02332103,0.00394667,-0.00336626,-0.0434744,0.0282907,0.06736549,-0.04892458,-0.01127698,0.04653026,-0.04705657,-0.07144542,-0.08611253,0.02404028,0.02573211,-0.05387753,-0.06795516,-0.01301475,-0.21940145,0.01633605,0.02149789,-0.01250093,0.01336866,-0.07946752,-0.02034838,-0.04729375,0.05279876,-0.0783195,0.01271019,0.03251689,0.04835255,0.00035581,-0.00044765,0.03791354,-0.03919627,0.01821848,0.03197211,0.04549008,-0.00649864,-0.05786301,-0.01816255,0.00323234,0.0263816,0.05276436,0.03715592,0.05130478,0.0948423,0.01856932,0.03728132,0.05639564,0.07328946,-0.19594885,0.05915922,0.01954396,0.0244509,-0.00775805,-0.04447659,0.02741569,0.03704325,0.02377688,0.01851932,-0.01407685,0.06989927,0.04214677,-0.03557748,-0.03130334,-0.07227669,-0.03907514,-0.00716622,-0.03069418,0.00373736,0.01598991,0.02021401,-0.00619327,0.05683032,0.04871023,0.0083125,0.07438495,-0.09189533,0.0197402,0.0169933,0.07573415,0.03414742,-0.0010601,0.01320989,0.04089329,0.00157004,0.01217803,0.11188758,-0.00382829,0.04308535,0.00144822,0.01616814,0.02616893,-0.03100467,-0.02600796,-0.03012118,-0.04901931,0.05631189,0.08054294,0.03092932,0.00589602,-0.00736074,-0.03631402,-0.02763238,0.00242544,0.00635268,-0.0061334,0.00372815,-0.06231226,0.03013659,0.01796371,0.02338296,0.0094628,-0.01866365,0.01113382,0.07534006,0.01793857,0.01596073,0.0559062,0.01145826,-0.09134937,-0.02458316,-0.00413873,-0.04537733,0.00585275,0.02435748,0.00881181,0.04997705,-0.00597454,-0.05714216,0.04327891,-0.11097188,-0.06938987,0.06687399,-0.00235539,0.00336945,0.01543346,-0.00124466,-0.02034525,-0.00975063,-0.0476652,-0.02931447,0.00345525,0.00355929,0.07447331,0.05743833,-0.07699391,0.0350442,-0.06409931,-0.06586648,-0.03922464,0.13607717,-0.03195811,-0.07823393,-0.01935196,0.08784571,-0.00056923,-0.06270923,-0.04179035,0.0091546,-0.06564263,0.00455975,0.0976003,-0.01153521,-0.08003917,-0.03094119,-0.0152575,-0.03076483,0.0143787,-0.02672876,-0.03241207,0.01444702,-0.02700407,-0.05410181,-0.02267063,-0.0077751,0.01012596,0.04821057,-0.04269105,-0.00813225,-0.01727306,-0.01104978,-0.05155106,-0.03200541,-0.03238812,-0.03988868,0.05746747,0.0090257,0.00117228,0.02209614,-0.01991173,0.02433788,-0.04076373,0.0302878,-0.00783149,-0.06137695,0.09387374,0.04001187,-0.04184329,-0.00181804,0.05271485,0.05474229,-0.03804061,0.04809874,0.0296616,0.02733872,-0.00810808,-0.00019264,-0.02436848,0.00529586,-0.06173275,-0.21692334,-0.03584881,-0.02029748,0.02810661,0.02804895,-0.00748173,-0.01770639,-0.01998856,0.04856579,0.04561792,0.13231827,-0.05560554,0.00559545,0.01677145,-0.00662079,0.03407779,-0.09462788,-0.02193868,-0.03966599,0.04499555,0.02911157,0.0240634,-0.02054815,-0.12602074,0.03184925,-0.01545797,0.12241099,-0.02770348,0.03889439,0.01575331,0.06995972,-0.04566647,-0.00545974,-0.01934565,0.01838401,0.03811518,-0.08255753,-0.02746601,-0.01667345,0.00048402,-0.07731064,-0.0126503,0.03039702,-0.08512916,-0.01854305,-0.00593879,-0.01131752,-0.01701542,0.0029695,0.07278209,0.04712312,0.01863799,0.04094118,0.00831712,0.02539184,-0.03057793,-0.05006575,0.01292146,-0.0501269,0.0574854,-0.01341154,-0.04214611,0.05975184,-0.0263664,0.05454934,0.01533875,-0.05949352,-0.04367309,-0.0182608,0.02827885,0.00374599,0.06621129,0.03886006,0.06277061,-0.03898885,0.03325109,0.03876583,-0.00069451,0.01848918,0.0049034,-0.07876766,-0.052618,0.04793036,0.11717365,0.03191733,0.03981533,0.06104048,-0.00639427,-0.02349153,0.01162874,0.01618838,0.0341046,-0.06918412,0.00354991,0.02345935,0.04989973,-0.26424268,0.04418383,-0.01367044,0.02287994,-0.02895968,-0.01386137,0.01899077,0.00522101,0.03060337,-0.05322563,0.07109755,0.07099824,-0.00264339,-0.04612013,-0.00634915,-0.00095496,0.02172117,-0.03585563,0.05561315,-0.00283288,0.03077012,0.05840704,0.24602103,0.01352567,0.02683436,0.0328595,-0.03636798,-0.00951613,-0.03672531,0.03866635,-0.01432772,0.00266633,0.08861841,0.0537589,-0.04656027,0.03769455,-0.05672623,0.00409182,-0.01327008,0.0307153,-0.055387,0.00555624,0.0188816,0.04135589,0.13820215,0.04168634,-0.00942375,-0.10754815,0.07044793,0.03582122,-0.06787378,-0.03909979,-0.09527975,-0.01847242,0.00691626,0.01683218,0.01113775,-0.02821646,-0.01071499,-0.03397291,0.01964865,-0.06394249,0.03709453,-0.01565752,0.02387427],"last_embed":{"hash":"r1zcsi","tokens":420}}},"text":null,"length":0,"last_read":{"hash":"r1zcsi","at":1753423597885},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)","lines":[102,131],"size":3532,"outlinks":[{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":7},{"title":"_**Create, Make Lotto Wheels in Lottery Wheeling Software or Manually**_","target":"https://saliu.com/lottowheel.html","line":9},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":10},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":12},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":14},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":15},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":16},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":17},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":18},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":19},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>**_","target":"https://saliu.com/reverse-strategy.html","line":20},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":21},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":22},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":23},{"title":"Lottery wheeling master teaches how to create best positional lotto wheel from in-position systems.","target":"https://saliu.com/HLINE.gif","line":25},{"title":"Forums","target":"https://forums.saliu.com/","line":27},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":27},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":27},{"title":"Contents","target":"https://saliu.com/content/index.html","line":27},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":27},{"title":"Home","target":"https://saliu.com/index.htm","line":27},{"title":"Search","target":"https://saliu.com/Search.htm","line":27},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":27},{"title":"You need a little patience if you want to generate positional lottery wheels with Ion Saliu software.","target":"https://saliu.com/HLINE.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06612614,-0.05931424,0.01095344,-0.02344836,-0.05998624,0.0511728,0.0063542,-0.00070163,0.02248525,-0.01540259,0.0057911,-0.01494445,0.02501584,-0.00138776,-0.00265715,-0.01723768,0.03460851,0.07508706,-0.04437572,-0.01754489,0.05133219,-0.02732889,-0.09451218,-0.10263187,0.00807213,0.0319148,-0.04606712,-0.06263009,0.01568785,-0.16615942,0.01661642,0.02246725,-0.04624974,-0.01803531,-0.06979135,-0.03568636,-0.04184819,0.03386296,-0.09967244,0.01119268,0.02421481,0.04223687,-0.0045648,-0.01055313,0.0241651,-0.05239806,0.05624235,0.03414899,0.06288882,-0.00246825,-0.03093218,-0.00284169,0.00499565,0.03453305,0.0273398,0.04453172,0.04057888,0.08756955,0.00215242,0.02401384,0.0429877,0.07407919,-0.1732778,0.06225641,-0.00334112,0.03823416,0.02706617,-0.02770656,0.01402161,0.05517481,0.05397644,0.01549544,-0.01460437,0.07866961,0.04973197,-0.02205491,-0.0176283,-0.06835758,-0.04301291,0.00610556,-0.04089997,-0.01052606,0.01091436,0.03466549,-0.02825057,0.06397749,0.05599685,0.01754376,0.09369829,-0.09317245,0.02763649,0.02638489,0.06577111,0.04718189,0.00679924,-0.01499252,0.05206382,-0.00004603,0.02863169,0.13866994,-0.01949658,0.0201782,0.01720066,-0.01523158,0.02617063,-0.0268285,-0.03839697,-0.0262225,-0.06416345,0.06927644,0.07573403,0.0351918,0.0200612,-0.03191182,-0.03464556,-0.04055802,-0.02530231,0.01235106,0.00204848,-0.008787,-0.05153804,0.02518005,0.00238018,0.02484742,-0.00556033,-0.02508075,0.01903043,0.08120272,-0.00731817,0.0197417,0.06530971,0.00690592,-0.10860099,-0.03876894,-0.00713373,-0.0543539,0.00626723,-0.00110785,-0.01799668,0.0451186,-0.02806311,-0.05055889,0.05798989,-0.1217178,-0.04432994,0.07499071,-0.01387036,-0.00011834,-0.00364759,-0.00995656,-0.00937623,-0.01649381,-0.02774999,-0.05686595,0.02042227,-0.00111167,0.09149696,0.03392141,-0.05431098,0.03433118,-0.06119457,-0.03932903,-0.04602206,0.14332049,-0.0301735,-0.09270923,-0.03362973,0.07498873,-0.00899805,-0.06156527,-0.02718497,0.02663033,-0.08160006,0.01121522,0.12967417,-0.00098139,-0.10324292,-0.01922736,-0.03479332,-0.02998237,0.02120219,-0.04790325,-0.01274022,-0.00717305,-0.03211363,-0.0477789,-0.00694672,-0.03241806,0.00971042,0.05767792,-0.02682064,0.02879182,-0.02106726,-0.03545367,-0.04440093,-0.00932104,-0.06846448,-0.03554165,0.0811461,-0.01378378,-0.00589005,0.02758115,-0.00844975,0.03749704,-0.01987841,0.01095855,-0.01269805,-0.06464665,0.07362174,0.05776121,-0.05913452,0.00719937,0.06046127,0.04407047,-0.06322204,0.04260901,0.02053854,0.04191682,-0.01841232,-0.00109845,-0.01694039,0.02058336,-0.05986017,-0.19085853,-0.02886732,-0.02994138,0.03025931,0.01639853,-0.00573685,-0.01067381,-0.00867232,0.03160402,0.04698281,0.12866485,-0.06642678,0.02547789,0.0411602,-0.00940722,0.03044675,-0.07172546,-0.02788067,-0.02742726,0.03272,0.05119668,-0.00906874,-0.01642011,-0.13487378,0.03731954,-0.00867112,0.11725923,-0.02727394,0.06360818,0.00250756,0.0760677,-0.03857375,-0.00493969,-0.03258187,0.00265231,0.04028456,-0.08093295,-0.03831096,-0.0019065,-0.01024696,-0.05743554,-0.03313034,0.03664831,-0.08030232,0.01099831,-0.00637839,-0.00849382,-0.00018167,0.00050889,0.07001481,0.02853253,0.01512963,0.04309376,0.02211455,0.0260029,-0.02509712,-0.03054521,0.00201135,-0.0426514,0.04793745,-0.00846446,-0.04651684,0.03754944,-0.00979467,0.0634743,0.00629112,-0.03973231,-0.05085935,-0.0120544,0.00375165,0.0012082,0.05054215,0.00917625,0.0789394,-0.03369725,0.02326279,0.05476381,-0.01703149,0.01601834,0.0161829,-0.08326524,-0.04778391,0.0436253,0.10774213,0.04926845,0.04567713,0.05928836,0.02339959,-0.03436682,0.01375217,0.02161076,0.04201183,-0.05493826,-0.01495597,0.02165368,0.03347791,-0.25688219,0.04643987,-0.03251486,0.03479692,-0.04079354,-0.04435117,0.01067938,0.00618421,0.02732755,-0.04840184,0.07692445,0.03518079,-0.00375487,-0.03814661,0.02615806,-0.01014301,0.03674552,-0.02126176,0.05712339,0.010698,0.02560563,0.04178437,0.26032731,0.03048138,0.05289102,0.02273173,-0.04028191,-0.00627003,-0.03244661,0.02739524,0.00521256,-0.00190318,0.06715259,0.02753459,-0.0544904,0.04291182,-0.0280655,0.03036827,-0.02487279,0.04077717,-0.06621264,0.01770632,-0.00901775,0.04682336,0.10896482,0.02724014,-0.00217403,-0.11007722,0.05902661,0.04032534,-0.07714976,-0.03056007,-0.09829059,-0.04494698,0.01528974,0.01713598,0.03278066,-0.0450869,-0.01109642,-0.04246992,0.02337008,-0.05029089,0.03704708,-0.00559356,-0.00514562],"last_embed":{"hash":"1m22jsz","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423598062},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{3}","lines":[107,107],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{21}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07883852,-0.05407513,0.02362217,-0.03494879,-0.06042719,0.09387105,-0.02766702,0.02014898,0.01217805,-0.02261639,-0.0050087,-0.05026206,0.01344682,0.02039585,-0.00716856,-0.01529923,0.01130995,0.06589533,-0.07365716,-0.00585923,0.0672184,-0.05027967,-0.05457018,-0.10151638,0.01569385,-0.00919719,-0.03111106,-0.01557882,0.01447475,-0.22258961,0.0028295,0.0282734,-0.03141234,-0.00157709,-0.09479056,-0.02057603,-0.04575036,0.04750714,-0.06414827,0.00671047,0.0318966,0.03652452,-0.0127512,-0.02229861,0.03795192,-0.03605035,0.04995003,0.0109682,0.06175755,0.01160054,-0.05191866,-0.01944675,0.00102338,0.03164101,0.05362178,0.04328245,0.01880853,0.08150193,0.01163113,0.03113486,0.04176116,0.06148868,-0.21470585,0.05614986,-0.00750452,0.01146588,0.00446557,-0.0225275,0.00737098,0.02202331,0.05745801,-0.00962678,-0.00950617,0.11078683,0.07035483,-0.03693219,-0.00966902,-0.07320973,-0.0178559,0.00834219,-0.04509964,0.01439514,-0.03182882,0.01444171,0.00641692,0.0472578,0.05007104,0.02157316,0.04732861,-0.06589936,0.05830685,-0.00472083,0.05513145,0.0368642,-0.03017979,0.02336382,0.01258674,0.01043609,-0.01190375,0.11264804,0.0091072,0.04525794,-0.00580483,-0.00189687,0.021743,0.01308655,-0.03191913,0.00869049,-0.04928704,0.04700041,0.06069786,0.05530357,-0.02247313,-0.03661137,-0.06700687,-0.03007442,0.02273908,0.00070778,-0.03509862,0.02706215,-0.0630003,0.02909402,0.01642608,0.02489604,0.01469697,-0.03038268,0.02126326,0.05034605,0.02118565,0.04690469,0.05699703,0.02804264,-0.09410478,-0.0520368,-0.00100609,-0.07565451,-0.03142397,-0.00901728,0.01202372,0.02529291,0.00833555,-0.06360269,0.02890733,-0.08941048,-0.03541279,0.04759333,-0.03166199,-0.00516964,-0.02166391,-0.00816692,-0.03160977,-0.03474057,-0.00439357,-0.02844966,0.04545457,0.00023668,0.07399876,0.07600375,-0.04653616,0.05097835,-0.06082716,-0.05784945,-0.04512013,0.15198478,-0.00432727,-0.09021152,-0.06865238,0.07812932,0.02484408,-0.04331519,-0.0094257,0.02580381,-0.06647006,0.04453898,0.11379533,-0.00853893,-0.0818481,-0.00544698,-0.04394631,-0.00309069,0.02300597,-0.08099248,-0.02186008,-0.00240339,-0.06170407,-0.06977534,0.03006653,-0.03506829,-0.00674428,0.04917305,-0.02162066,0.0290534,-0.00781406,-0.01523487,-0.03275711,-0.0335062,-0.04284506,-0.04943077,0.0659682,-0.01933219,-0.04065868,0.01971653,-0.03130158,0.03028809,-0.04649408,0.00881685,-0.00708352,-0.05954352,0.10050175,0.05195985,-0.02795975,0.01184524,0.07608105,0.03524472,-0.07107779,0.02527151,0.00046464,0.02744762,0.0115015,-0.01561104,-0.01058333,0.00934188,-0.05343043,-0.20818949,-0.00854911,-0.04654212,0.03535586,0.0225015,-0.02647655,0.02895253,-0.01681955,0.0314138,-0.00234377,0.12861404,-0.06021183,0.04905963,-0.0010201,0.00612061,0.04139499,-0.07129345,-0.00445111,0.00022439,0.04312976,0.04253808,0.00983236,-0.03724035,-0.10877454,0.05519316,-0.021494,0.12192945,-0.00125849,0.06320921,-0.01185192,0.10079355,-0.03828231,0.00622768,-0.02230845,0.00443992,0.0089123,-0.07840638,-0.01310179,-0.01585672,-0.02928285,-0.04062522,0.01782095,0.02406536,-0.06368768,-0.01582948,-0.03897679,-0.01185361,-0.0092441,0.00402202,0.05824117,0.05895385,-0.01244805,0.04040838,-0.01944924,0.00442726,-0.03879904,-0.04322647,0.00223087,-0.03269672,0.05108808,-0.01652606,-0.00516818,0.0802632,-0.03701471,0.02216665,0.00152939,-0.02959784,-0.0374144,-0.00838199,0.0187932,-0.00320346,0.0546478,0.02663227,0.08960175,0.00071583,0.00880539,-0.00341049,-0.00443921,0.01814289,0.00777397,-0.0895735,-0.04876294,0.0717313,0.0759506,0.04251814,0.05240213,0.03233504,0.00095099,-0.01030826,0.03223962,0.01205452,0.07020376,-0.0664457,0.02176561,0.03755445,0.02825329,-0.24923524,0.04109765,-0.03745685,0.03372941,-0.04740077,-0.02823699,0.01199694,0.0011597,0.01870175,-0.0286164,0.08757641,0.05193798,0.0126537,-0.02279222,0.02143236,0.00389209,0.01229056,-0.0188676,0.03288491,-0.03830281,0.01828257,0.03431938,0.25533581,0.05288059,0.02502819,0.02392218,-0.03174057,-0.01057986,-0.0153679,0.01449257,-0.00096316,-0.01044186,0.06790441,0.03950877,-0.02005831,0.05302094,-0.05224628,0.01233155,-0.02061728,0.01399163,-0.05755611,0.04280393,0.02441068,0.03819875,0.11714815,0.01539371,0.01306061,-0.08746899,0.06728106,0.02905471,-0.08665366,-0.03004954,-0.09198527,-0.01743623,-0.01087406,0.0186352,0.04448614,-0.04254358,-0.02247855,-0.0264832,0.04489547,-0.02165068,0.06679204,-0.03126208,0.01165155],"last_embed":{"hash":"19c5qa2","tokens":312}}},"text":null,"length":0,"last_read":{"hash":"19c5qa2","at":1753423598108},"key":"notes/saliu/Positional Lotto Wheels, Wheeling.md#Positional Lotto Wheels, Wheeling#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{21}","lines":[126,131],"size":659,"outlinks":[{"title":"Lottery wheeling master teaches how to create best positional lotto wheel from in-position systems.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"You need a little patience if you want to generate positional lottery wheels with Ion Saliu software.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
