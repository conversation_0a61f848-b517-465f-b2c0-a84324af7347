
"smart_sources:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md": {"path":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1gr66df","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363612713,"size":19954,"at":1753423416500,"hash":"1gr66df"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Software Program Odd Even Low High Number Groups":[8,223],"#Lotto Software Program Odd Even Low High Number Groups#{1}":[10,15],"#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)":[16,23],"#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)#{1}":[17,23],"#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>":[24,52],"#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>#{1}":[26,52],"#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>":[53,82],"#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>#{1}":[55,82],"#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>":[83,158],"#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>#{1}":[85,158],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>":[159,192],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{1}":[161,188],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{2}":[189,190],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{3}":[191,192],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>":[193,223],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{1}":[195,212],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{2}":[213,213],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{3}":[214,215],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{4}":[216,223]},"outlinks":[{"title":"The lotto numbers can be divided in low or high and odd or even.","target":"https://forums.saliu.com/HLINE.gif","line":14},{"title":"Software for Lotto Groups in High Demand","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto","line":16},{"title":"Generate Lotto Combinations from Groups of Numbers","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#numbers","line":17},{"title":"New Lotto Programs: _UserGroups_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#programs","line":18},{"title":"Upgrades to Lotto Group Software","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#updates","line":19},{"title":"The Famous Lotto Strategy of _Perfect Balance_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#strategy","line":20},{"title":"Lotto groups software is the first to accurately generate combinations from groups of numbers.","target":"https://forums.saliu.com/HLINE.gif","line":22},{"title":"The lottery players can create their groups of numbers, not only high, low, even, odd.","target":"https://forums.saliu.com/HLINE.gif","line":51},{"title":"The lotto software to generate combinations as even odd low high is not free.","target":"https://forums.saliu.com/HLINE.gif","line":81},{"title":"The menu of the only lottery software program to generate combinations from user groups of numbers.","target":"https://forums.saliu.com/UserGroups6.gif","line":95},{"title":"The number group software is component of the powerful Bright applications.","target":"https://forums.saliu.com/HLINE.gif","line":157},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/UserGroups5-1.gif","line":163},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/sums-odd-even.gif","line":167},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-stats.gif","line":169},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":189},{"title":"This is the best known strategy played by many lotto players – based on balance.","target":"https://forums.saliu.com/HLINE.gif","line":191},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-strategy.gif","line":216},{"title":"Download your lotto software to generate combinations of numbers – odd or even, low or high.","target":"https://forums.saliu.com/HLINE.gif","line":218},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":220},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":220},{"title":"Search","target":"https://saliu.com/Search.htm","line":220},{"title":"Exit the best site of software, programs, strategies, mathematics of lotto for the world.","target":"https://forums.saliu.com/HLINE.gif","line":222}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["lottery","lotto","software","programs","odd","even","low","high","numbers","groups"],"source":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","author":null}},
"smart_sources:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md": {"path":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10434973,-0.04163311,-0.06311738,-0.05252162,-0.04882516,-0.01132469,-0.0690207,-0.00136495,0.00551965,-0.05349139,0.03635058,-0.03796362,0.04580969,0.01503041,-0.00675832,-0.05561926,0.00611735,-0.03609628,-0.08336357,-0.00224367,0.07566071,-0.01215489,-0.02660051,-0.06340685,0.06429932,-0.00458898,-0.05025461,-0.06960349,-0.03148775,-0.22267962,-0.00201288,0.0731519,0.07071073,-0.04189571,-0.05476101,-0.01875309,-0.03074684,0.03048315,-0.06804372,0.04531379,-0.00087275,0.05142552,-0.02635628,-0.03313752,0.01587478,-0.04717272,-0.01075627,0.00792986,0.02866057,0.02812994,-0.04952023,-0.00655032,-0.01070192,0.07877425,0.04529603,0.01049323,0.02077816,0.05365473,-0.00186811,0.06722038,0.05699416,0.06348535,-0.1493607,0.02323557,0.01709422,0.02183303,-0.0269043,-0.05502021,0.03965266,0.01263099,0.02930854,0.03277414,-0.03688377,0.02735348,0.05700779,-0.05669864,-0.03530569,-0.03280189,-0.04130144,-0.02659847,-0.04187106,-0.04269963,-0.0461965,-0.0176174,0.03936516,0.01127207,0.08221151,0.02190266,0.04165832,-0.05431638,-0.02693251,0.04148436,0.04872238,0.03157099,0.06951412,0.00231994,0.06242888,-0.00750982,-0.0401413,0.13044818,-0.00792389,-0.01404031,0.01880202,-0.00472616,0.01873154,-0.02195962,-0.02047995,-0.02333531,-0.04056507,0.00763256,0.04316763,0.01878112,0.07521048,-0.06165319,-0.05253534,-0.03543887,-0.00672578,0.0331983,0.00983604,-0.00332502,-0.02069221,0.0046535,0.02095741,-0.01940781,0.03692744,0.00423858,-0.0185385,0.06672098,0.03694997,0.01823879,0.04285309,0.01161755,-0.10667817,-0.05130614,0.00108952,-0.05591726,-0.02394691,-0.0117886,-0.00957025,0.00761766,-0.02074459,-0.04480109,0.06038455,-0.11850777,-0.00413231,0.05359803,-0.02426753,-0.01459823,-0.00538,0.01492197,0.00019285,-0.02312347,-0.05293372,-0.08562779,0.00627112,-0.03196641,0.13621598,0.10868683,-0.04207513,0.009394,0.02119709,-0.02241836,-0.02133086,0.13187657,-0.00282295,-0.07558423,-0.03646674,0.06468226,-0.03662477,-0.04956782,-0.01069176,0.02263991,-0.00438358,0.05423182,0.11941571,-0.04008732,-0.07236191,-0.05616323,-0.01642383,-0.00039214,-0.00478411,0.01572254,-0.03924464,-0.0221097,-0.02301184,-0.09124455,0.0012885,-0.00518524,0.00553943,0.01946243,-0.0385748,0.01891151,-0.03069664,-0.00096145,-0.01490442,-0.0088042,-0.00787342,-0.0191411,0.08141202,-0.02269414,-0.01314111,0.00574858,0.03696835,0.04615551,-0.05031268,0.06563276,0.01708191,-0.07238641,0.09699136,0.01427393,-0.00750477,0.01173161,0.04825433,0.11396012,-0.03352078,0.04695595,-0.01159301,0.0378999,0.02569432,0.03762777,0.01032915,0.00430455,-0.07639919,-0.19911718,-0.00784254,-0.00169804,-0.02030796,-0.00397456,-0.01103982,0.07253186,-0.0342333,0.02785436,0.07318188,0.11559413,-0.06044884,-0.00636908,0.04759142,-0.01514928,0.01726642,-0.08304997,-0.02294418,-0.01635534,0.06283043,-0.00504604,0.0440411,-0.0301014,-0.08567379,0.01991242,0.00286646,0.11845122,0.04023666,0.01095032,-0.03892908,0.05136516,-0.01204763,-0.01513812,-0.03501243,0.02034682,0.05770291,-0.04024799,0.00077001,0.01773747,-0.00712249,-0.07988761,-0.00718155,0.00109661,-0.10718177,0.00632499,0.021798,-0.01224188,0.01513145,0.01042711,0.01968329,0.05288168,-0.02027491,0.03730687,0.05000702,0.04285055,-0.030192,-0.09577703,-0.01532431,0.01855005,0.03552043,0.00134058,-0.03923913,0.06670682,-0.02463591,0.038579,0.04766944,-0.01577521,-0.02521947,0.03968099,-0.00303093,-0.02558223,0.10035417,-0.00802795,0.07029446,-0.02923084,0.01220048,0.01998119,-0.02625811,0.00158223,-0.03057369,-0.0093989,-0.05833186,0.04519033,0.08378603,0.01629992,0.00728458,0.00787651,0.04969006,0.03369739,0.02079034,0.02920086,0.00331097,-0.01979716,0.05297753,0.02142178,0.0048643,-0.24656719,0.01909138,-0.00521943,0.04160611,0.00021297,0.03115017,0.02803757,-0.00356159,0.01276393,-0.03373966,0.05495443,0.03242866,0.0241318,-0.09841955,0.00127452,0.0012871,-0.02122956,-0.04777673,0.02786548,-0.01663299,0.04089913,0.02775542,0.26937601,0.01187617,-0.03011383,0.04979477,0.02481725,0.02757779,-0.00542198,0.03298477,-0.0105534,0.00234857,0.08921462,-0.00888899,-0.04034963,0.00096108,-0.02495802,-0.06409673,-0.03187275,0.05078441,-0.09479149,-0.00848521,-0.06958353,0.00241759,0.12675437,0.03582667,-0.03529209,-0.04865767,0.05215114,0.0493308,-0.08452567,-0.06556496,-0.04557507,-0.02980685,0.05161286,0.04057672,-0.00773534,0.02128609,-0.02484006,0.03440185,-0.00335538,0.03847146,0.08171777,0.0270263,-0.00129224],"last_embed":{"hash":"1gr66df","tokens":488}}},"last_read":{"hash":"1gr66df","at":1753423535363},"class_name":"SmartSource","last_import":{"mtime":1753363612713,"size":19954,"at":1753423416500,"hash":"1gr66df"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Software Program Odd Even Low High Number Groups":[8,223],"#Lotto Software Program Odd Even Low High Number Groups#{1}":[10,15],"#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)":[16,23],"#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)#{1}":[17,23],"#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>":[24,52],"#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>#{1}":[26,52],"#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>":[53,82],"#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>#{1}":[55,82],"#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>":[83,158],"#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>#{1}":[85,158],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>":[159,192],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{1}":[161,188],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{2}":[189,190],"#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{3}":[191,192],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>":[193,223],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{1}":[195,212],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{2}":[213,213],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{3}":[214,215],"#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{4}":[216,223]},"outlinks":[{"title":"The lotto numbers can be divided in low or high and odd or even.","target":"https://forums.saliu.com/HLINE.gif","line":14},{"title":"Software for Lotto Groups in High Demand","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto","line":16},{"title":"Generate Lotto Combinations from Groups of Numbers","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#numbers","line":17},{"title":"New Lotto Programs: _UserGroups_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#programs","line":18},{"title":"Upgrades to Lotto Group Software","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#updates","line":19},{"title":"The Famous Lotto Strategy of _Perfect Balance_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#strategy","line":20},{"title":"Lotto groups software is the first to accurately generate combinations from groups of numbers.","target":"https://forums.saliu.com/HLINE.gif","line":22},{"title":"The lottery players can create their groups of numbers, not only high, low, even, odd.","target":"https://forums.saliu.com/HLINE.gif","line":51},{"title":"The lotto software to generate combinations as even odd low high is not free.","target":"https://forums.saliu.com/HLINE.gif","line":81},{"title":"The menu of the only lottery software program to generate combinations from user groups of numbers.","target":"https://forums.saliu.com/UserGroups6.gif","line":95},{"title":"The number group software is component of the powerful Bright applications.","target":"https://forums.saliu.com/HLINE.gif","line":157},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/UserGroups5-1.gif","line":163},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/sums-odd-even.gif","line":167},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-stats.gif","line":169},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":189},{"title":"This is the best known strategy played by many lotto players – based on balance.","target":"https://forums.saliu.com/HLINE.gif","line":191},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-strategy.gif","line":216},{"title":"Download your lotto software to generate combinations of numbers – odd or even, low or high.","target":"https://forums.saliu.com/HLINE.gif","line":218},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":220},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":220},{"title":"Search","target":"https://saliu.com/Search.htm","line":220},{"title":"Exit the best site of software, programs, strategies, mathematics of lotto for the world.","target":"https://forums.saliu.com/HLINE.gif","line":222}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["lottery","lotto","software","programs","odd","even","low","high","numbers","groups"],"source":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","author":null}},"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11859439,-0.04968419,-0.06447314,-0.04541418,-0.0371641,-0.03414831,-0.04458777,0.0211676,0.02100445,-0.05508628,0.04801275,-0.0227522,0.03295499,0.01236321,-0.03434644,-0.04843159,0.01532323,-0.02985553,-0.05246588,0.01606642,0.08943925,-0.01345938,-0.05314664,-0.06400356,0.07791238,0.00344035,-0.05992813,-0.065947,-0.0256404,-0.18712506,-0.01437462,0.04637933,0.08124953,-0.0239766,-0.02097438,-0.03949198,-0.03474177,0.02266288,-0.02445697,0.0431574,-0.01187564,0.04321018,-0.04867662,-0.02421548,0.01153895,-0.0506932,-0.00540805,0.00485862,0.01981476,0.027801,-0.04314524,0.01512679,-0.01243074,0.06892014,0.04212464,0.02064343,0.03009228,0.02241459,0.02321784,0.05927958,0.04253404,0.02469136,-0.16031034,0.03565063,0.02732955,0.01549581,-0.01276823,-0.07183181,0.02421549,-0.01603313,0.0481281,0.03341234,-0.03342541,0.04150148,0.06465647,-0.04719284,-0.02323331,-0.05239375,-0.04414647,-0.02373301,-0.03616583,-0.06446097,-0.04349168,-0.04590266,0.03040702,0.00265021,0.07287087,0.0495299,0.05517391,-0.05375084,-0.0108171,0.03534541,0.07895084,0.03630469,0.04496925,0.01365922,0.08311791,0.00406956,-0.05455541,0.15099911,-0.04312748,-0.00243189,0.0025391,0.01229825,0.05038853,-0.00962339,-0.02160385,-0.02225339,-0.04938318,-0.00441248,0.03467788,0.02740421,0.10851789,-0.06830143,-0.01443658,-0.03395921,0.01107755,0.04355797,0.02931288,0.0096908,-0.02929592,-0.0140142,0.00899208,-0.0423877,0.03032484,0.03038657,-0.01110973,0.06067129,0.01437617,0.01023688,0.05958548,-0.02994822,-0.0980566,-0.03596946,0.01779595,-0.02643263,-0.01235677,-0.02018248,-0.00363195,-0.03577993,-0.02341118,-0.04982686,0.02996758,-0.11401758,-0.01248254,0.04207939,-0.01354328,-0.02422123,0.01288266,0.01985716,-0.03419798,-0.02795009,-0.05034518,-0.07924385,-0.01277282,-0.03943363,0.12644447,0.10384791,-0.02266069,-0.00142611,0.01993921,-0.03171256,-0.03117055,0.1265002,-0.01645713,-0.08653951,-0.04322477,0.05399651,-0.03314425,-0.05928337,-0.01990664,0.00312131,0.00186667,0.06136255,0.12530316,-0.04209239,-0.03330561,-0.04684697,-0.03813501,-0.00161506,-0.0211987,0.00532739,-0.04842712,-0.01447521,-0.03847098,-0.07669454,-0.00663022,-0.01210777,0.03221058,0.03508108,-0.03678997,-0.0236118,-0.04011487,-0.01539343,-0.01825026,-0.01335766,0.01191156,-0.02375386,0.08008112,-0.03844774,0.01201,-0.00369329,0.03319634,0.05752357,-0.03572442,0.04227912,0.01047539,-0.06673042,0.07540204,0.01616038,-0.00418311,0.01753508,0.0393437,0.09443364,-0.02908121,0.0313717,-0.04935375,0.04274857,0.02190933,0.01099242,-0.00201729,-0.00376958,-0.0963726,-0.2310442,0.01434331,0.00477598,-0.02528971,-0.02685706,-0.02158705,0.05697631,-0.02931531,0.04966062,0.10020632,0.08825687,-0.06345686,-0.01741994,0.01715114,-0.01964248,0.04594561,-0.05475708,-0.0392715,0.00505795,0.05001048,0.00029087,0.04124913,-0.03826178,-0.09330148,0.03467583,0.00279636,0.1268062,0.05975821,0.02282846,-0.06162562,0.06073663,-0.02385113,-0.01839421,-0.04279936,-0.00760631,0.05650826,-0.05483876,0.02671647,0.00087578,-0.02443166,-0.10582548,-0.01415133,0.01870042,-0.08952554,0.00825969,0.03569789,0.01614058,-0.01245587,0.00480459,0.01893005,0.04853005,-0.02473203,0.03001874,0.07651058,0.05026849,-0.0027128,-0.08536743,-0.04776031,0.01637098,0.03974535,-0.00253437,-0.04654851,0.05502427,-0.01613913,0.00734396,0.04163628,-0.02236878,-0.04809595,0.01850059,0.00613046,-0.00954474,0.10960994,0.00242225,0.02575476,-0.03873414,0.00137828,0.02490398,-0.00680775,0.02839681,-0.00511086,0.00664842,-0.04159261,0.06676592,0.07828473,0.02435501,0.04151908,-0.00066317,0.04180766,0.03254629,0.01488025,0.05195511,0.00900142,-0.02777083,0.03865335,0.04561448,-0.02339981,-0.2504929,0.01370956,0.00963677,0.02914613,0.01910276,0.04160262,0.02421748,0.01030594,0.00204148,-0.01954011,0.06119144,0.03948022,0.0275567,-0.08232371,-0.0258329,0.00355837,-0.02040026,-0.03116059,0.02471078,0.03068228,0.01525026,0.03656776,0.26408297,0.03216663,-0.03892515,0.04240131,0.0298802,0.02476503,0.0080389,0.03839522,-0.01436197,0.01244762,0.07465856,-0.00561633,-0.02349086,0.00072962,-0.02721648,-0.0353505,-0.01919587,0.06885815,-0.09470936,0.01434668,-0.06060276,-0.01554348,0.11706761,0.04520868,-0.05321037,-0.03689265,0.04640511,0.0397461,-0.06646781,-0.06942423,-0.05005199,-0.00271776,0.03290768,0.05217354,-0.00141865,0.0261085,-0.01345239,0.0235091,-0.01683522,0.03478019,0.06607712,0.04065862,0.01638351],"last_embed":{"hash":"19tunx4","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"19tunx4","at":1753423533140},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#---frontmatter---","lines":[1,6],"size":203,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08029179,-0.03658028,-0.0561005,-0.07000384,-0.05949379,-0.00126429,-0.05506104,-0.02147684,0.00657775,-0.05335827,0.03630624,-0.03101697,0.04068717,0.01521881,0.00121493,-0.04708477,0.00563365,-0.01838756,-0.09984346,-0.00678167,0.06864186,-0.00807122,-0.03001525,-0.05687765,0.04793844,0.01061679,-0.05134406,-0.07786454,-0.02961167,-0.22229758,0.00864267,0.07156982,0.06936646,-0.04137804,-0.05565943,-0.0195366,-0.03670495,0.03376883,-0.0875981,0.04386273,-0.00345015,0.05123425,-0.0066424,-0.03206164,0.01314139,-0.03860305,-0.0185839,0.01114766,0.04618024,0.02388223,-0.03918868,-0.00963333,-0.01396602,0.07654649,0.03578926,0.0138256,0.02675742,0.06670049,-0.00785807,0.07667015,0.0628271,0.08224024,-0.13704482,0.02455484,0.02325158,0.02135085,-0.02842353,-0.04531987,0.02366536,0.03885254,0.01530701,0.03699449,-0.0292372,0.02781508,0.05472242,-0.06219653,-0.04620767,-0.0362243,-0.0270067,-0.01465356,-0.06498235,-0.0366882,-0.03416196,-0.01415164,0.04850464,0.00365204,0.07300187,0.03053828,0.03387815,-0.04472718,-0.02049604,0.06509569,0.03792848,0.01679325,0.07671238,0.00542615,0.05206019,-0.01931094,-0.03771694,0.13375001,0.00833423,-0.02215134,0.02140504,0.00288049,0.00335047,-0.02813151,-0.03174968,-0.01495964,-0.04433897,0.01133665,0.04186415,0.01626209,0.0676355,-0.05587737,-0.07213772,-0.02020715,-0.0157373,0.0324324,0.00781113,0.00377099,-0.0155742,-0.00052753,0.02490309,-0.02534332,0.04699515,-0.00036446,-0.01945179,0.0728251,0.04681611,0.02051516,0.03320807,0.00924131,-0.10105222,-0.04859156,-0.01039037,-0.05229481,-0.01295873,-0.0049605,-0.00200601,-0.00235424,-0.02228825,-0.05762305,0.08017046,-0.11775687,0.01852556,0.06342126,-0.02679719,-0.00744172,-0.00577882,0.00607254,-0.00253526,-0.03269029,-0.06557675,-0.08041279,-0.00064078,-0.0309731,0.1304757,0.08397866,-0.05806694,0.01120465,0.00656888,-0.01762397,-0.00785703,0.12038873,0.00556526,-0.0555974,-0.03622022,0.05146345,-0.05270417,-0.0599273,-0.01166108,0.02696952,-0.00561731,0.047449,0.1184166,-0.03675532,-0.0998705,-0.04177329,0.00460904,0.00232216,0.00159187,0.02768958,-0.03300945,-0.02361637,-0.02322905,-0.09342108,0.01172495,0.00798698,0.00036965,0.01525373,-0.03937995,0.01716905,-0.02761011,0.00146376,-0.00897461,-0.01270013,-0.01987167,-0.02864418,0.08145405,-0.02097684,0.0099915,0.01642201,0.04728911,0.04210808,-0.03635957,0.06761473,0.01254465,-0.08039289,0.09333063,0.01439601,-0.02579262,0.01183853,0.04586568,0.10600314,-0.03049118,0.05803918,-0.01140908,0.02882601,0.02859509,0.0403731,0.0192736,-0.00304869,-0.05073783,-0.1946265,-0.01544445,0.00273894,-0.00585759,0.0032748,-0.00487161,0.06333828,-0.03922645,0.01621976,0.07185722,0.11867475,-0.06434184,-0.0206239,0.0520036,-0.03357933,0.01190109,-0.09143257,-0.02798669,-0.02742666,0.06270953,-0.00820698,0.03632497,-0.01910502,-0.08061972,0.02686626,0.00961351,0.12140983,0.0156588,0.00740559,-0.03199624,0.04485891,-0.01166016,-0.02357633,-0.01791119,0.02642435,0.05017382,-0.04942368,-0.01560281,0.02359933,-0.00092631,-0.07071327,-0.00795007,-0.00724957,-0.11133614,0.00437193,0.0201708,-0.02207683,0.02161411,0.0221499,0.01760366,0.04040631,-0.01019049,0.03012013,0.04211511,0.03935008,-0.03482277,-0.09832395,0.00340401,0.02036847,0.02908868,-0.00691389,-0.04807867,0.05874574,-0.01949582,0.04518353,0.05369721,-0.00936879,-0.02312219,0.0485539,-0.0005028,-0.01699005,0.07764746,-0.0030946,0.07100318,-0.03355855,0.02182453,0.02184038,-0.02348643,-0.00030747,-0.02634351,0.00181403,-0.07337382,0.02856244,0.09202695,0.01554248,-0.00868178,0.02295712,0.06098453,0.03213239,0.01561213,0.02470446,0.00530347,-0.01003448,0.06814992,0.01030322,0.00537347,-0.24609885,0.01388186,-0.01241402,0.05313724,-0.00732783,0.0242587,0.02918198,-0.00149794,0.0048689,-0.04119363,0.06684799,0.03062044,0.03027384,-0.10046508,0.01609333,-0.00659412,-0.01964778,-0.05019018,0.03794347,-0.02205175,0.05979501,0.02918883,0.26849312,-0.00501453,-0.01757572,0.05236224,0.02452261,0.03207155,-0.01150159,0.04215487,-0.01888543,-0.01177596,0.09161723,-0.02093998,-0.03169882,-0.00740761,-0.01823169,-0.05557146,-0.03641707,0.05328943,-0.09254159,-0.01446364,-0.08659,-0.00332813,0.13430732,0.04238912,-0.03824444,-0.05174669,0.0300505,0.04535462,-0.08453592,-0.06003729,-0.04476297,-0.04545294,0.0524077,0.03972745,-0.01014151,0.01955792,-0.02695096,0.04246565,0.00344968,0.02181297,0.07059927,0.02463374,-0.01894358],"last_embed":{"hash":"1dujq2r","tokens":503}}},"text":null,"length":0,"last_read":{"hash":"1dujq2r","at":1753423533179},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups","lines":[8,223],"size":19706,"outlinks":[{"title":"The lotto numbers can be divided in low or high and odd or even.","target":"https://forums.saliu.com/HLINE.gif","line":7},{"title":"Software for Lotto Groups in High Demand","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto","line":9},{"title":"Generate Lotto Combinations from Groups of Numbers","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#numbers","line":10},{"title":"New Lotto Programs: _UserGroups_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#programs","line":11},{"title":"Upgrades to Lotto Group Software","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#updates","line":12},{"title":"The Famous Lotto Strategy of _Perfect Balance_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#strategy","line":13},{"title":"Lotto groups software is the first to accurately generate combinations from groups of numbers.","target":"https://forums.saliu.com/HLINE.gif","line":15},{"title":"The lottery players can create their groups of numbers, not only high, low, even, odd.","target":"https://forums.saliu.com/HLINE.gif","line":44},{"title":"The lotto software to generate combinations as even odd low high is not free.","target":"https://forums.saliu.com/HLINE.gif","line":74},{"title":"The menu of the only lottery software program to generate combinations from user groups of numbers.","target":"https://forums.saliu.com/UserGroups6.gif","line":88},{"title":"The number group software is component of the powerful Bright applications.","target":"https://forums.saliu.com/HLINE.gif","line":150},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/UserGroups5-1.gif","line":156},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/sums-odd-even.gif","line":160},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-stats.gif","line":162},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":182},{"title":"This is the best known strategy played by many lotto players – based on balance.","target":"https://forums.saliu.com/HLINE.gif","line":184},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-strategy.gif","line":209},{"title":"Download your lotto software to generate combinations of numbers – odd or even, low or high.","target":"https://forums.saliu.com/HLINE.gif","line":211},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":213},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":213},{"title":"Search","target":"https://saliu.com/Search.htm","line":213},{"title":"Exit the best site of software, programs, strategies, mathematics of lotto for the world.","target":"https://forums.saliu.com/HLINE.gif","line":215}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0759139,-0.02402855,-0.04708618,-0.05804124,-0.05710268,-0.00645054,-0.01972199,0.00457086,0.0091171,-0.04680647,0.04478762,-0.01318605,0.03883803,0.01857236,-0.02750233,-0.05135631,0.00357512,-0.01691928,-0.0845697,0.02402127,0.08799805,-0.01288992,-0.05016913,-0.05808392,0.06076496,-0.00422897,-0.06296379,-0.07525259,-0.03016664,-0.20718157,0.00288308,0.07049982,0.07249147,-0.0393842,-0.02718461,-0.04987325,-0.03475305,0.0571135,-0.07686336,0.04765199,0.00014525,0.04264811,-0.00990869,-0.02826219,0.01905755,-0.05821508,-0.00669086,0.01371113,0.04308446,0.01903534,-0.04189405,0.01775742,-0.00279764,0.06060107,0.02576855,0.01348531,0.02517335,0.0618843,0.00440372,0.07454212,0.06077252,0.07074682,-0.12554553,-0.00700555,0.04627697,0.02081889,-0.01693542,-0.06275009,0.02275595,0.01573225,0.05085001,0.02520964,-0.03708341,0.02848278,0.04672882,-0.07121766,-0.04519626,-0.04905083,-0.03928873,-0.006144,-0.06235945,-0.05677113,-0.03624024,-0.02454733,0.02266089,0.01941599,0.06853594,0.0291559,0.03161921,-0.06480055,-0.02090817,0.05833656,0.03575516,0.02820938,0.08263706,-0.00193184,0.06242557,-0.03437198,-0.01739531,0.15860355,-0.01488388,-0.01788958,0.01967064,-0.00100605,0.00502523,-0.02560845,-0.03415575,-0.02836879,-0.05916414,0.00477081,0.03996591,0.02162755,0.09442156,-0.03216258,-0.06067947,-0.0368373,-0.01639494,0.05449387,0.002306,0.00742059,-0.03159402,0.00263024,-0.00158379,-0.0361723,0.02466777,0.00856367,-0.01779976,0.06873553,0.03532778,0.01138462,0.04607381,-0.02007503,-0.09580002,-0.0498609,0.00490578,-0.0556546,-0.02037122,0.00268674,0.00410557,-0.01405649,-0.01879876,-0.03415063,0.07211772,-0.10779139,0.02057897,0.0456295,-0.04113027,-0.00580445,0.00745371,0.00783497,-0.01036321,-0.03411966,-0.06948312,-0.07845639,-0.00165837,-0.0330317,0.11751744,0.08916817,-0.03592599,0.0090552,0.0141459,-0.01345503,-0.0209854,0.13204364,-0.01538264,-0.03509128,-0.0503744,0.03676946,-0.04701341,-0.05583013,-0.01223787,0.02818775,-0.01372137,0.05149997,0.09979906,-0.03238459,-0.09340893,-0.04934608,-0.0230695,-0.00593366,0.00821533,0.0064899,-0.0347779,-0.01792524,-0.01645284,-0.09318939,-0.00120067,0.00686023,0.01051983,0.0380035,-0.03688727,0.01333263,-0.0420005,-0.02417988,-0.01302057,-0.02052608,-0.02146495,-0.02729979,0.07801621,-0.02578304,-0.00975027,0.00977562,0.04559286,0.06941038,-0.03015195,0.06315954,0.03250143,-0.07577511,0.10422645,0.02146591,-0.02126008,-0.00354697,0.04113671,0.10986498,-0.01921653,0.0517794,-0.01851534,0.03758994,0.022135,0.02018761,0.00780815,-0.02174144,-0.05852158,-0.2026799,-0.02198484,-0.00250103,-0.01466613,0.0100003,0.00551938,0.03308414,-0.01805886,0.03132097,0.08402919,0.10000119,-0.0871465,-0.02713495,0.04662745,-0.02375652,0.03726462,-0.10258577,-0.03596715,-0.01118488,0.06357767,-0.00716484,0.0553928,-0.04304992,-0.08901633,0.0278092,0.00977674,0.11023819,0.0285001,0.03036727,-0.05877277,0.04108782,-0.02906748,-0.01740343,0.00514402,0.01071404,0.04023121,-0.05831996,-0.01365477,0.00887197,-0.00678705,-0.09579837,-0.00953049,0.00162775,-0.09090438,0.02541679,0.02148579,-0.01101486,0.01593625,0.02473175,0.02831191,0.04617641,-0.0240005,0.03075742,0.05804037,0.04731924,-0.02720546,-0.09599136,-0.02402088,0.01724535,0.02865803,0.00443869,-0.05446701,0.06513289,-0.02852457,0.03304196,0.05318563,-0.00643313,-0.04687294,0.03830162,0.01451124,-0.012386,0.10358645,-0.00535055,0.05316057,-0.03777963,0.03687376,0.0337344,-0.0238191,0.00310667,-0.02368286,0.02396222,-0.06337042,0.0322206,0.08077769,0.00214035,0.00126266,0.00904306,0.04986067,0.02323886,0.016819,0.0526639,0.00974941,-0.01847178,0.06080467,0.00176542,-0.01087599,-0.24530613,0.02085603,-0.00418856,0.05510407,-0.00975534,0.01875223,0.02061113,0.02901795,0.00680446,-0.0239522,0.0782266,0.00776508,0.04056815,-0.10203295,0.00049792,-0.00341654,-0.00481708,-0.0603226,0.03435243,0.00415304,0.05845664,0.04177534,0.27757972,0.01009007,0.00350235,0.03785708,0.03373877,0.01990064,-0.01596047,0.03860445,0.0100934,0.00656964,0.0827089,-0.01646741,-0.01716703,-0.00712067,-0.00843738,-0.03482225,-0.02271894,0.06309445,-0.09116115,-0.01768993,-0.09060942,-0.00352925,0.11701865,0.04322685,-0.04255975,-0.04384501,0.04688811,0.0438002,-0.06990269,-0.04674339,-0.04616459,-0.04873943,0.0499745,0.02914242,-0.01829746,0.01939648,-0.0272354,0.02838335,-0.00030777,0.02519281,0.05791378,0.02025058,-0.02096506],"last_embed":{"hash":"vq2fxf","tokens":101}}},"text":null,"length":0,"last_read":{"hash":"vq2fxf","at":1753423533384},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#{1}","lines":[10,15],"size":260,"outlinks":[{"title":"The lotto numbers can be divided in low or high and odd or even.","target":"https://forums.saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10357569,-0.04013603,-0.06604164,-0.05341115,-0.04561047,-0.03154784,-0.0884499,-0.00899127,-0.01199739,-0.06950884,0.03915774,-0.03087105,0.05197192,0.0014685,0.00265709,-0.04753685,0.01952309,-0.0120662,-0.07061615,-0.01313093,0.08420766,0.01290409,-0.01840507,-0.05001608,0.06470798,0.00549948,-0.05187812,-0.06276493,-0.00776513,-0.22334132,-0.0020553,0.06685723,0.09941922,-0.03498949,-0.03830053,-0.00111749,-0.02422653,0.01314225,-0.07009919,0.05346929,-0.01622444,0.05140749,-0.02275866,-0.04303014,0.01366203,-0.0434717,0.00951238,0.01296668,0.0161044,0.0226316,-0.0485523,-0.01892004,-0.01335477,0.07725275,0.02789451,0.00757392,0.03016728,0.04928479,-0.00207851,0.06278841,0.05365444,0.06873833,-0.15479574,0.03752915,0.02272595,0.02323835,-0.02637229,-0.06841608,0.03092966,0.02350779,0.01247375,0.02098016,-0.04652461,0.01509582,0.07641923,-0.06025153,-0.02564542,-0.03011765,-0.03585797,-0.03453982,-0.02779971,-0.0476,-0.0367717,0.00165512,0.04769588,-0.00477206,0.07968219,0.01118982,0.06255151,-0.04841125,-0.03933173,0.04301013,0.03689543,0.02393204,0.08713289,0.00705112,0.07330391,0.00132482,-0.04486503,0.14187674,-0.00796128,-0.01726068,0.04166665,-0.00824893,0.0133613,-0.02381309,-0.00522858,-0.00122423,-0.03964283,0.00383823,0.03588371,0.02040354,0.05998733,-0.07013842,-0.0396406,-0.05546734,-0.00520175,0.02756281,-0.0114435,-0.00760304,0.00138714,-0.00213654,0.05993953,-0.03260009,0.05485277,-0.00845135,-0.02775862,0.06240814,0.03212316,0.01426923,0.02899211,0.01009956,-0.08505347,-0.0478166,-0.00647022,-0.05196821,-0.01652884,0.0013183,-0.0205693,0.00671764,-0.03074632,-0.06298194,0.04987461,-0.12459067,0.00852054,0.07066555,-0.03428258,-0.01247224,-0.02650717,-0.00062768,-0.0037004,-0.01222123,-0.04729537,-0.08179011,0.00312146,-0.0554346,0.12851404,0.10202134,-0.0360496,-0.00082047,0.0104159,0.00396841,-0.01861635,0.12534612,0.00619171,-0.05452618,-0.03857309,0.05594977,-0.02793148,-0.02653357,-0.00266464,0.02999401,0.00860135,0.0482086,0.11653364,-0.04806987,-0.08336323,-0.03911485,0.00127318,-0.00157835,-0.00232738,0.00927553,-0.03789554,-0.01327063,-0.02711537,-0.08540169,0.00104706,-0.00326982,0.0228912,0.02983941,-0.03912253,0.01151765,-0.03148622,-0.01840178,-0.01490529,-0.00567239,-0.01473451,-0.0097502,0.0814378,-0.03145106,0.00286904,0.006718,0.04772627,0.02123423,-0.03060466,0.06128477,0.00124162,-0.0721042,0.08842569,0.01810413,-0.02324468,0.03363451,0.07039259,0.10387637,-0.02616624,0.04802759,-0.02112999,0.05785185,0.01655901,0.04715874,0.03458285,0.0066969,-0.07565912,-0.20271596,-0.00357493,0.01720645,-0.0158227,0.00515239,-0.03575798,0.08889576,-0.03378857,0.01115066,0.06975351,0.09977541,-0.04744422,-0.0054683,0.04627781,-0.02116499,0.02442295,-0.05539781,-0.02647011,-0.03470713,0.03920488,-0.01279678,0.05707289,-0.02686759,-0.09539869,0.02554632,0.00506491,0.11858698,0.01216314,0.03494461,-0.02593372,0.03137741,-0.01258355,-0.05185748,-0.03736266,0.02851283,0.0666737,-0.02813875,-0.01539475,0.0401726,-0.01521433,-0.05874138,-0.02665281,0.0009828,-0.10689395,0.00109193,0.04171617,-0.00432259,-0.0090654,0.00273637,0.00186284,0.01942526,0.00472465,0.0324439,0.0718708,0.01972365,-0.01970094,-0.08989112,-0.02393852,0.01494839,0.00834704,-0.00674338,-0.0568989,0.05658878,-0.02089759,0.0420937,0.04278088,-0.00165663,-0.02796561,0.04167802,0.01724247,-0.02421278,0.11452904,-0.00280187,0.0680136,-0.03284972,0.00596601,0.01567805,-0.02715539,0.01251772,-0.02965221,-0.01254257,-0.06022117,0.06102183,0.0774534,0.01465835,0.00366846,0.01619687,0.01962747,0.03181351,0.01050527,0.0212834,-0.0001601,-0.01450995,0.05422276,0.03438291,0.00614086,-0.25664347,-0.01357805,-0.00792245,0.03389138,0.00235413,0.05214337,0.02111705,-0.02108634,0.01520628,-0.035408,0.04396027,0.01306175,0.03640705,-0.09892826,0.00713129,0.00101723,-0.01145691,-0.03222237,0.02751817,-0.0176118,0.01802992,0.01769911,0.25656494,0.01770883,-0.03353869,0.04906123,0.00708365,0.02144542,0.00994419,0.02457365,-0.01546305,-0.00923244,0.08909106,-0.01473035,-0.02283976,0.00158163,-0.03107706,-0.07699346,-0.02483136,0.06441494,-0.07754955,-0.00769978,-0.08903573,0.00271819,0.1319714,0.0231406,-0.03094408,-0.05135618,0.04435204,0.05565035,-0.08262678,-0.0611246,-0.04630482,-0.01891889,0.06575866,0.04572917,-0.02641003,0.02638227,-0.04128143,0.02816427,0.00365601,0.03410349,0.07505838,0.04328961,0.00605125],"last_embed":{"hash":"nfpeo8","tokens":316}}},"text":null,"length":0,"last_read":{"hash":"nfpeo8","at":1753423533423},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)","lines":[16,23],"size":741,"outlinks":[{"title":"Software for Lotto Groups in High Demand","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto","line":1},{"title":"Generate Lotto Combinations from Groups of Numbers","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#numbers","line":2},{"title":"New Lotto Programs: _UserGroups_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#programs","line":3},{"title":"Upgrades to Lotto Group Software","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#updates","line":4},{"title":"The Famous Lotto Strategy of _Perfect Balance_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#strategy","line":5},{"title":"Lotto groups software is the first to accurately generate combinations from groups of numbers.","target":"https://forums.saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10092891,-0.03665406,-0.06102296,-0.05580457,-0.04611218,-0.02762828,-0.08722155,-0.00702595,-0.01301964,-0.06128104,0.03859428,-0.03186411,0.05642749,-0.00225341,0.0033288,-0.04681898,0.01739407,-0.01248612,-0.07740733,-0.01602575,0.08063648,0.01548288,-0.01774671,-0.05011753,0.06517261,0.01077429,-0.04747983,-0.06460191,-0.00762299,-0.22722518,-0.00161319,0.06779567,0.08929994,-0.03532963,-0.03979312,-0.00219482,-0.02620298,0.02133435,-0.07084385,0.05611614,-0.01514524,0.04995259,-0.02099741,-0.04205948,0.00991846,-0.04080854,0.00891759,0.01552014,0.01946757,0.02088169,-0.04485919,-0.01673684,-0.01099009,0.08330988,0.02509137,0.00596012,0.02466113,0.05093285,-0.00418378,0.0638582,0.04994803,0.0751174,-0.16073048,0.03974805,0.01932482,0.02339301,-0.02106936,-0.07175778,0.02747328,0.02699149,0.00843516,0.02213145,-0.04367993,0.01436187,0.07953567,-0.05464257,-0.02903892,-0.02950035,-0.03510692,-0.02247913,-0.03246872,-0.04325416,-0.03754963,0.00137682,0.04948233,-0.00304348,0.0752477,0.01737752,0.06018499,-0.0453362,-0.03362254,0.04167536,0.03831975,0.02463198,0.0865476,0.00237848,0.06687491,-0.00226493,-0.03956014,0.14336023,-0.00694032,-0.01146463,0.04344022,-0.00311738,0.01137924,-0.02520563,-0.01063102,-0.00419593,-0.04167641,-0.00190381,0.03814315,0.02320577,0.06219801,-0.07145791,-0.04118859,-0.05371137,-0.00558333,0.03092189,-0.01014164,-0.00352191,0.00478886,-0.00468762,0.05842153,-0.03474564,0.05406734,-0.005377,-0.02757062,0.0620296,0.0344794,0.01394281,0.0262434,0.01476418,-0.08416536,-0.04825057,-0.0076311,-0.05606823,-0.01734633,0.00559597,-0.01750111,0.00131842,-0.03185844,-0.06078538,0.05366804,-0.12272779,0.01282316,0.07085261,-0.03622632,-0.01155027,-0.02253512,-0.0008487,-0.00379551,-0.01305609,-0.04859529,-0.08104996,0.00452344,-0.05008332,0.12970179,0.09121665,-0.03478688,0.00418113,0.01148759,0.00640513,-0.01793328,0.12661804,0.00623908,-0.05742904,-0.04090331,0.05703358,-0.02873304,-0.03028959,-0.00647772,0.02661532,0.00886552,0.04575113,0.11725097,-0.05088023,-0.08553579,-0.04083429,0.00454819,-0.00029237,0.00066608,0.00790562,-0.03782162,-0.01427828,-0.0282708,-0.08973638,-0.00006981,-0.00077386,0.02252472,0.0295486,-0.03883629,0.00855558,-0.03435678,-0.0192917,-0.00813696,-0.00737143,-0.01747168,-0.01044516,0.08051895,-0.03174331,0.00443007,0.00570925,0.0487566,0.01673528,-0.03213555,0.05396669,-0.00105241,-0.07088225,0.09090278,0.02656831,-0.02345699,0.02739233,0.07040951,0.10306173,-0.02596858,0.05662421,-0.02504391,0.05708341,0.01743217,0.04101711,0.03469386,0.00614129,-0.07635061,-0.20119275,-0.00613844,0.02024401,-0.01702197,0.00520575,-0.04337997,0.08251578,-0.03145464,0.00491409,0.06629575,0.08828823,-0.0489636,-0.00221404,0.04682638,-0.02399027,0.02154438,-0.05673409,-0.02530251,-0.03136634,0.04081194,-0.01779269,0.05971982,-0.0270988,-0.09850894,0.02803462,0.00060369,0.11531482,0.01043939,0.03338544,-0.02304951,0.03310708,-0.01077911,-0.05085353,-0.03433646,0.03264843,0.06453692,-0.02996043,-0.02297772,0.03766197,-0.01175656,-0.0525949,-0.02786976,-0.00033614,-0.11361099,0.0063635,0.03923312,-0.00957561,-0.01284947,0.00732028,0.00415357,0.01664376,0.00277302,0.03457173,0.06477454,0.02861312,-0.02653834,-0.09099709,-0.01550648,0.01293557,0.00551961,-0.00383926,-0.05798656,0.05645614,-0.02158013,0.04250465,0.042675,-0.00615671,-0.03025714,0.0446273,0.01470632,-0.01705511,0.11372636,-0.00580038,0.06461561,-0.0357032,0.00450345,0.01545173,-0.03483998,0.00755084,-0.03222549,-0.01628986,-0.06525411,0.05867292,0.07705973,0.00908318,0.00119029,0.02295466,0.02479039,0.03055262,0.01093039,0.02005291,-0.00197986,-0.01018315,0.05786317,0.0376857,0.00434112,-0.25647473,-0.01249506,-0.01412255,0.03130434,-0.00024214,0.0537868,0.02333523,-0.01588563,0.0106301,-0.03538035,0.048178,0.01090433,0.03500776,-0.09748418,0.01203592,0.00581235,-0.00991612,-0.03685625,0.02955898,-0.01753102,0.02780918,0.02182193,0.26022357,0.02509385,-0.03062206,0.04697428,0.00969341,0.0258155,0.01159003,0.02963456,-0.01501773,-0.00486455,0.08200859,-0.01297901,-0.02181266,0.00505552,-0.02686968,-0.07555634,-0.0315671,0.0632135,-0.07896996,-0.00936215,-0.09003171,0.00457184,0.13478792,0.02531599,-0.02675417,-0.05165019,0.03825104,0.05621694,-0.08049078,-0.05613742,-0.04695974,-0.02491038,0.07273866,0.04472549,-0.02804371,0.02080837,-0.04172739,0.03472515,0.00615288,0.02908754,0.07682329,0.0426214,0.00190306],"last_embed":{"hash":"g7ofb8","tokens":275}}},"text":null,"length":0,"last_read":{"hash":"g7ofb8","at":1753423533521},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups##I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)#{1}","lines":[17,23],"size":619,"outlinks":[{"title":"Generate Lotto Combinations from Groups of Numbers","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#numbers","line":1},{"title":"New Lotto Programs: _UserGroups_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#programs","line":2},{"title":"Upgrades to Lotto Group Software","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#updates","line":3},{"title":"The Famous Lotto Strategy of _Perfect Balance_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html#strategy","line":4},{"title":"Lotto groups software is the first to accurately generate combinations from groups of numbers.","target":"https://forums.saliu.com/HLINE.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07184303,-0.05550239,-0.04033691,-0.06324094,-0.05481984,-0.02403546,-0.04463315,-0.041564,0.00297219,-0.04775617,0.04300015,-0.0161464,0.06229606,-0.00046982,-0.01161456,-0.06019458,-0.00202164,-0.01075326,-0.10318273,-0.00985186,0.06101472,-0.00070216,-0.04896705,-0.05713935,0.04239244,0.05540189,-0.03803157,-0.06096807,-0.01427479,-0.23719695,0.02532579,0.0712455,0.07066944,-0.04125804,-0.03173732,-0.00841034,-0.03207968,0.01486914,-0.09933736,0.04038423,-0.0105759,0.03965667,0.0068016,-0.03128375,0.02112341,-0.03952458,-0.02950717,0.0225611,0.06024572,0.0350867,-0.0255096,-0.0050494,-0.02333016,0.06143551,0.05212925,-0.0071875,0.02167667,0.03558495,0.00472659,0.06500254,0.04912551,0.04363542,-0.12391564,0.05210091,0.00099443,0.00592532,-0.02884955,-0.010957,0.03377923,0.04493047,-0.04598135,0.04183005,-0.0317896,0.03202529,0.04730013,-0.04675959,-0.03021596,-0.0271543,-0.03564926,0.00781041,-0.07930643,-0.04113067,-0.0295822,-0.00966856,0.05247059,0.00599426,0.07059565,0.02194292,0.03158095,-0.04236031,-0.03462817,0.06036816,0.04197141,0.00979553,0.09017219,-0.01003437,0.0699117,0.00242627,-0.03255535,0.11618643,0.01174944,-0.04337715,0.02318377,0.00802506,-0.00954946,-0.01882347,-0.05361663,-0.01470897,-0.05519446,-0.00979492,0.04513083,0.01051601,0.04512769,-0.07519964,-0.04776982,-0.0217192,-0.00945948,0.0394714,0.01438967,0.00654482,-0.02375027,-0.0209148,0.02466945,-0.01692683,0.04774656,0.00898457,-0.03446508,0.0770997,0.0295997,0.00452032,0.0534311,-0.02120937,-0.08078559,-0.03568168,0.01069295,-0.03119149,-0.00208425,0.01130473,0.01206874,-0.03435864,-0.02089175,-0.05138721,0.07145121,-0.10876126,0.01736836,0.05520884,-0.03743564,-0.00833027,-0.01934031,0.01676519,-0.01880802,-0.04013311,-0.07984586,-0.09209748,0.02883961,-0.03310842,0.1710095,0.06321634,-0.05586535,-0.00638587,-0.03301866,-0.01625184,-0.0354578,0.11258018,0.00098445,-0.05356794,-0.03390226,0.03187913,-0.06549899,-0.05355515,-0.00237294,-0.00328031,0.00397166,0.06679232,0.10400007,-0.04115226,-0.12512636,-0.0411756,0.0415791,0.0106168,0.00185219,0.03766787,-0.01583641,-0.02665714,0.00045867,-0.09121189,0.0125542,0.01598096,-0.00130089,0.02516309,-0.01791586,0.02854145,-0.02954124,0.0083447,-0.01935533,-0.01376804,-0.0115984,-0.01315796,0.05475003,-0.01525478,0.06080285,0.0043226,0.03190899,0.03708541,-0.00301531,0.06942271,0.01444024,-0.04616392,0.1017767,0.01652084,-0.04024759,-0.00095454,0.05193143,0.06838495,-0.04191405,0.04068973,-0.01629628,0.04845541,0.04289581,0.04207592,0.00082207,-0.04440303,-0.04928321,-0.20817402,-0.00860962,0.04864025,0.00450904,0.02303741,-0.01433043,0.06736591,-0.04678564,0.02810945,0.08693676,0.10997844,-0.02948624,-0.02449894,0.0315804,-0.07778659,0.0254408,-0.07622775,-0.0254206,-0.0249832,0.0767567,-0.02845067,0.02025376,-0.04480555,-0.04721736,0.0578577,-0.01496502,0.13620917,-0.0039257,0.01897887,-0.0311588,0.04942821,-0.02257412,-0.0079329,-0.01046428,0.00482083,0.05187218,-0.02746558,0.00668455,0.02718635,-0.01746218,-0.05820543,-0.00975336,-0.01610091,-0.09985704,-0.00452705,0.01833112,-0.02964092,-0.03644935,0.01216479,0.01070942,0.05656607,-0.01664117,0.03106963,0.03457426,-0.00582736,-0.02349998,-0.10424559,0.01573749,0.01706625,0.02471271,0.00356161,-0.04673788,0.03527282,-0.01777719,0.00860036,0.06338647,0.0042254,-0.00970754,0.04716582,0.00180354,0.01115871,0.07763942,-0.0055197,0.05444926,-0.03671832,0.00263454,0.0215937,0.00939569,0.00498475,-0.01495974,0.0071636,-0.0561443,0.0320731,0.08014161,0.02076409,-0.02692888,0.02779547,0.06877698,0.05109454,0.0113421,0.02400939,0.00244049,0.00426904,0.08518305,0.02621073,0.01470544,-0.25190073,0.00248566,-0.02591298,0.05353353,-0.00058293,0.00367062,0.0189378,0.00527788,0.00097869,-0.02396214,0.06152847,0.03213779,0.02795913,-0.08923381,0.04524619,-0.005814,0.00361949,-0.04065629,0.02544633,-0.02318506,0.053273,0.01523755,0.26879925,-0.01606037,-0.00345178,0.04679455,0.0106146,0.04541036,0.01523051,0.05861313,-0.02136165,-0.01776359,0.10963491,-0.04036944,-0.02570799,0.00424662,0.00354351,-0.03755948,-0.04679209,0.04332663,-0.06973869,-0.02974666,-0.08969336,-0.03439586,0.11083443,0.03600936,-0.04071444,-0.06428119,-0.00162777,0.06436846,-0.07526629,-0.03719347,-0.05822286,-0.0332646,0.04082662,0.04851466,-0.02925232,0.03912644,-0.01526136,0.02819288,-0.00172935,-0.03275368,0.07769537,0.04744012,-0.0300602],"last_embed":{"hash":"7okfyi","tokens":484}}},"text":null,"length":0,"last_read":{"hash":"7okfyi","at":1753423533613},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>","lines":[24,52],"size":2542,"outlinks":[{"title":"The lottery players can create their groups of numbers, not only high, low, even, odd.","target":"https://forums.saliu.com/HLINE.gif","line":28}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07192218,-0.05792607,-0.03767163,-0.0635663,-0.0559711,-0.02135378,-0.04463089,-0.04100428,0.00466215,-0.05116523,0.04245378,-0.01564323,0.0621843,0.00030391,-0.01011066,-0.06078402,-0.00145844,-0.0119124,-0.10235845,-0.01008993,0.0603372,-0.00146989,-0.0475756,-0.05641511,0.04273727,0.05493045,-0.03764625,-0.06089582,-0.01498938,-0.23771597,0.0291384,0.07181885,0.06952797,-0.04041627,-0.03333016,-0.0078205,-0.03139151,0.01612939,-0.10061015,0.04045873,-0.01040074,0.03884777,0.00771449,-0.03344315,0.02101138,-0.03847829,-0.02852865,0.02184452,0.06201684,0.03607663,-0.02646806,-0.00245146,-0.02366906,0.06073809,0.05214188,-0.0073826,0.02229585,0.03822848,0.00796752,0.06318462,0.04971904,0.0425926,-0.12621275,0.05168899,0.0010104,0.00555988,-0.02968806,-0.01104623,0.03376881,0.04410191,-0.04593723,0.03976746,-0.03091639,0.0323947,0.04535415,-0.04634376,-0.03175227,-0.02658632,-0.03575662,0.00888948,-0.08046334,-0.04050898,-0.02989186,-0.01064464,0.05118709,0.00597899,0.07135224,0.02219768,0.03267013,-0.04145276,-0.03389004,0.05937308,0.04195326,0.01175075,0.08874839,-0.01184372,0.06676994,0.00440011,-0.03086141,0.1150534,0.01544168,-0.0429446,0.02139227,0.00894495,-0.00802062,-0.01666863,-0.05370359,-0.01514423,-0.05675611,-0.00604241,0.04621578,0.01344886,0.04279181,-0.07527757,-0.04735455,-0.01939634,-0.00826392,0.03927424,0.0130752,0.00759559,-0.02363021,-0.02021531,0.02565677,-0.01796489,0.04702779,0.00890485,-0.03207485,0.07626721,0.03028285,0.00468919,0.05237375,-0.01959187,-0.0838867,-0.03594395,0.01251688,-0.03272201,0.00083514,0.01199607,0.01100608,-0.03217742,-0.02187527,-0.0505675,0.07206313,-0.10960124,0.01461886,0.05381563,-0.03529927,-0.0077585,-0.0187741,0.01570352,-0.016954,-0.0396327,-0.07934237,-0.09160589,0.03176741,-0.03135649,0.17085864,0.06453709,-0.05681591,-0.00718122,-0.03575335,-0.01805563,-0.03466378,0.1115469,-0.00001016,-0.05489719,-0.03677082,0.03307372,-0.06514085,-0.0535502,-0.00191974,-0.00214232,0.00258999,0.06533713,0.10318901,-0.04140037,-0.12514128,-0.04196336,0.03989278,0.00966621,0.00286454,0.03767984,-0.01521801,-0.02721749,0.00188873,-0.09176316,0.01192741,0.01516754,-0.00173866,0.02573973,-0.01588067,0.03087542,-0.02903454,0.00953841,-0.01964796,-0.01512474,-0.01464073,-0.01338073,0.05418053,-0.01835386,0.0590324,0.00483945,0.0344118,0.03720388,-0.00304606,0.07040621,0.01226896,-0.04393234,0.10099964,0.01714284,-0.04032874,-0.00231161,0.04946913,0.06843659,-0.04458606,0.04227814,-0.01459366,0.0472761,0.04435755,0.04356327,0.0003739,-0.04088851,-0.0497758,-0.20833102,-0.00756064,0.0477347,0.00563339,0.02305313,-0.01327152,0.06651958,-0.04806763,0.029712,0.08506729,0.11174907,-0.02921634,-0.02328654,0.0327974,-0.08014217,0.02522524,-0.07554574,-0.02538184,-0.02604634,0.07559752,-0.02744569,0.01915818,-0.04605112,-0.04606355,0.05735194,-0.01436844,0.13444087,-0.00329679,0.01952815,-0.03216334,0.05113465,-0.02070194,-0.00578542,-0.00913183,0.00354836,0.04945139,-0.02739504,0.00582423,0.02420778,-0.01616746,-0.05953209,-0.00883292,-0.01556476,-0.1001974,-0.00627483,0.01881296,-0.02769706,-0.03451575,0.01051857,0.0097527,0.05705108,-0.01473881,0.03127577,0.03439722,-0.00547821,-0.02548115,-0.10305489,0.0169669,0.0186714,0.02180769,0.00371309,-0.04498446,0.0362893,-0.02014464,0.00860394,0.06463524,0.004221,-0.00957893,0.04875249,0.00053168,0.00981501,0.0775482,-0.00919295,0.05367971,-0.03788821,0.0027678,0.02178579,0.01077912,0.00456644,-0.01429532,0.00439721,-0.05551783,0.03220192,0.08016835,0.02144413,-0.02456868,0.0288553,0.07041511,0.04901605,0.01319963,0.02325999,0.0045023,0.00245493,0.08420637,0.02663434,0.01583529,-0.25203174,0.00389678,-0.02721822,0.05578393,-0.00272295,0.00305218,0.01922774,0.00603606,0.0015808,-0.02441998,0.06144877,0.03244619,0.02933889,-0.08731511,0.04604218,-0.0056353,0.00538139,-0.04212067,0.02746706,-0.02730927,0.05437751,0.01416819,0.26959908,-0.01560287,-0.00528668,0.04629139,0.00711225,0.04317058,0.01283457,0.06017703,-0.02208117,-0.01828444,0.10856081,-0.03985195,-0.02689164,0.00306591,0.00227062,-0.03936068,-0.04612034,0.0426985,-0.07035815,-0.02978929,-0.08902761,-0.03271917,0.1119064,0.03602319,-0.04107723,-0.06634341,-0.00152112,0.06607413,-0.07508028,-0.03856713,-0.06077926,-0.03554285,0.04016121,0.04862096,-0.02871636,0.03727043,-0.01423971,0.02684792,-0.00214029,-0.02837399,0.07921208,0.04417717,-0.02932793],"last_embed":{"hash":"sc5yh3","tokens":482}}},"text":null,"length":0,"last_read":{"hash":"sc5yh3","at":1753423533786},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>1. Software for Lotto Groups in High Demand</u>#{1}","lines":[26,52],"size":2487,"outlinks":[{"title":"The lottery players can create their groups of numbers, not only high, low, even, odd.","target":"https://forums.saliu.com/HLINE.gif","line":26}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08274342,-0.04203647,-0.04016723,-0.04541838,-0.05471457,-0.01252667,-0.04795539,-0.00274362,0.01037791,-0.03811008,0.01809677,0.01194364,0.05308742,-0.0094753,-0.02602783,-0.05066641,0.04706397,-0.03121341,-0.08511105,0.0196344,0.06834982,-0.01495582,-0.05281257,-0.05982554,0.02551498,-0.01076381,-0.04864794,-0.0539014,-0.05096312,-0.2378944,0.0405104,0.04699817,0.03683922,-0.03853709,-0.03411782,-0.03198617,-0.04099772,0.02734145,-0.07801478,0.0372302,0.00909076,0.05033066,-0.01190653,-0.03132423,0.00526239,-0.03734135,-0.01778075,0.02957296,0.01836737,0.05087368,-0.04892251,-0.00902721,0.03034556,0.08230127,0.01358877,0.00597949,0.03140324,0.05578591,0.02224153,0.05479212,0.04449648,0.0718149,-0.13031381,0.03809202,0.01727638,0.04981327,0.01340051,-0.03549755,0.02949308,0.03400429,0.0339581,0.03096591,-0.05463589,0.05250648,0.0563754,-0.0304815,-0.02518096,-0.02538356,-0.06832797,0.00420731,-0.07443624,-0.05324356,-0.02048823,-0.02056096,-0.00065723,0.01835283,0.06269035,0.02637429,0.05836286,-0.04459739,-0.0014231,0.03201632,0.06093502,0.04106682,0.05685593,0.01680542,0.05461055,-0.01270436,-0.04845084,0.12408327,0.01957455,-0.00222472,0.02400924,-0.00246699,0.0212067,-0.02322868,-0.04390793,-0.00866751,-0.04551079,0.0042168,0.05657746,0.05221333,0.0846061,-0.07017753,-0.0599164,-0.015166,-0.01049295,0.03528379,0.00774185,-0.02141973,-0.06240638,0.02305583,-0.00569387,-0.0151836,0.0110342,-0.01218208,-0.0265479,0.08684905,0.04739258,0.02846878,0.03728627,0.01859113,-0.09397579,-0.03422162,0.00482553,-0.05469263,-0.01749959,-0.00366418,0.01884558,0.02357251,-0.02226479,-0.02801354,0.06010977,-0.08928279,0.02070875,0.02996515,0.00611138,-0.0147944,0.02353361,0.01873969,-0.01257697,-0.03324911,-0.0771347,-0.07268707,0.03688642,-0.02130031,0.12242638,0.07704423,-0.05186164,-0.00023122,0.02666437,0.00036111,-0.00777094,0.13170734,-0.00627708,-0.08876134,-0.03625624,0.04730939,-0.04360605,-0.03948804,-0.0368642,0.02388146,-0.00227982,0.05685941,0.09851629,-0.037403,-0.13656999,-0.03034706,-0.01360771,-0.00317764,0.00015209,0.00237884,-0.03100981,-0.03109877,0.00880187,-0.12028114,-0.01890974,-0.00448337,-0.00784331,0.03910723,-0.03939987,0.00513926,-0.03839447,0.00350376,-0.00061966,-0.04385081,-0.00289204,-0.03021707,0.0916825,-0.01891761,-0.01591091,-0.00498853,0.02489301,0.02026665,-0.03452517,0.04884968,0.01420978,-0.05453607,0.10332162,0.0340703,-0.03758785,-0.00778961,0.04791576,0.11442542,-0.062093,0.05272822,-0.00637874,0.04523128,0.01819661,0.02210985,0.00241099,0.00170657,-0.03957886,-0.19831267,-0.00847365,-0.02332026,0.00628639,0.02317634,-0.03133779,0.02651979,-0.01835835,-0.00835803,0.08059754,0.10204912,-0.06804178,-0.02448644,0.03250296,-0.00894478,0.01790216,-0.10198577,-0.01295444,-0.02483169,0.03910271,-0.03254593,0.04824998,-0.02649297,-0.09595751,0.04291131,-0.00193133,0.12855791,0.02530663,0.00186306,-0.03583553,0.08592152,-0.0025647,0.00928917,-0.0580226,0.00196819,0.04159947,-0.04427004,-0.00736867,0.02987999,0.00038075,-0.08515336,-0.0119745,-0.01146478,-0.10232315,0.01468335,0.03620478,-0.04154244,0.02639674,0.00234173,0.05459376,0.02843966,-0.02869996,0.02984612,0.04556014,0.07344157,-0.03634126,-0.08964837,0.0058831,0.00885902,0.03737812,0.01170207,-0.05514235,0.0851844,-0.04144725,0.04787606,0.02367677,-0.02673461,-0.01479871,0.02047603,0.00612499,-0.01960055,0.10611898,0.00872489,0.05882828,-0.03622961,0.02639519,0.02378549,-0.01249748,0.00739923,-0.02483927,-0.00477513,-0.05723654,0.02830188,0.09254228,-0.00013656,0.03497086,0.014671,0.05794132,0.016461,0.0053735,0.00895599,0.00881479,-0.03904089,0.06123068,0.04600775,0.02115107,-0.2168965,0.00780569,-0.0462545,0.05756301,-0.00348038,0.03526717,0.03443072,0.00324252,0.01368143,-0.02802566,0.06564921,0.031915,0.02601349,-0.09553295,0.0414656,-0.00170449,-0.01502327,-0.03971361,-0.00676949,-0.00951266,0.04770064,0.02718171,0.26540044,0.01068958,-0.01984641,0.03556825,-0.00311747,0.05256869,-0.00280588,0.00767259,-0.02119906,-0.01744495,0.05280675,-0.04175495,-0.00760622,-0.01836879,-0.02034689,-0.01585462,-0.03928592,0.03583039,-0.09564964,-0.03064743,-0.05518109,-0.01419646,0.12735915,0.04296093,-0.02040765,-0.06405659,0.05393265,0.08395876,-0.07927876,-0.06655962,-0.07547014,-0.04465984,0.05380461,0.04270088,-0.00782702,0.00818002,0.00700602,0.0472106,0.00500518,0.00601691,0.05505316,0.02123921,-0.0608394],"last_embed":{"hash":"bqepm9","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"bqepm9","at":1753423533963},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>","lines":[53,82],"size":3589,"outlinks":[{"title":"The lotto software to generate combinations as even odd low high is not free.","target":"https://forums.saliu.com/HLINE.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08225106,-0.0435219,-0.0378077,-0.04533624,-0.0514617,-0.01109572,-0.04990734,-0.00219999,0.00921865,-0.0360658,0.01709929,0.01385802,0.05213346,-0.01459147,-0.02525548,-0.051591,0.04199547,-0.03032161,-0.08699464,0.01926898,0.06928849,-0.0143508,-0.05111286,-0.06173819,0.02330068,-0.01080586,-0.04963943,-0.05444445,-0.04965619,-0.23843485,0.04419569,0.05085923,0.0323301,-0.03989457,-0.03368757,-0.03111982,-0.04113308,0.02315406,-0.07825509,0.03599208,0.01299575,0.0508479,-0.01107727,-0.03345492,0.00571841,-0.03759169,-0.0209483,0.02761172,0.01804806,0.05283679,-0.04653152,-0.0082171,0.03024963,0.08211456,0.01681068,0.00583087,0.03277855,0.05730356,0.0201651,0.05517806,0.04183382,0.0704667,-0.13233104,0.03904306,0.01423027,0.04933805,0.01466679,-0.03380436,0.03071211,0.03354533,0.03738948,0.03327139,-0.05282507,0.05302706,0.05403861,-0.02830941,-0.02392472,-0.02860075,-0.06525037,0.00708972,-0.07754814,-0.05030884,-0.0194152,-0.02091591,-0.00549837,0.01628776,0.06495062,0.02539743,0.05794471,-0.04076776,-0.00028386,0.02872828,0.05949413,0.04297587,0.05904076,0.019151,0.05165839,-0.0159159,-0.04684629,0.12258615,0.01858693,-0.00199311,0.02829967,-0.00129796,0.02217348,-0.02389969,-0.0459433,-0.0080072,-0.04851266,0.00726888,0.05804231,0.0532183,0.08212572,-0.07323243,-0.05814887,-0.01401915,-0.01144597,0.03483076,0.00841796,-0.02336025,-0.06260506,0.02350633,-0.00687297,-0.01705244,0.00992678,-0.0123315,-0.02421863,0.08671378,0.04606697,0.03080306,0.03492315,0.01519805,-0.09526955,-0.03326811,0.00444641,-0.05542861,-0.01626482,-0.00289369,0.01538866,0.02778768,-0.0188585,-0.02687468,0.05533662,-0.09115618,0.02318147,0.03159202,0.00770334,-0.01387716,0.02512186,0.01596384,-0.01579441,-0.03301118,-0.07781413,-0.07378267,0.03918931,-0.01959477,0.12080866,0.0697654,-0.05212501,-0.00029643,0.02208467,-0.000539,-0.00720335,0.13105892,-0.01007551,-0.08671556,-0.03762032,0.04707328,-0.04074726,-0.04055931,-0.03657497,0.02592049,-0.00285231,0.05777998,0.09970734,-0.03599501,-0.13880882,-0.03029008,-0.01261323,-0.00391078,0.0008684,0.00238519,-0.0323208,-0.02959108,0.0079652,-0.11950622,-0.01928747,-0.00479092,-0.00928891,0.03948166,-0.03651523,0.00588414,-0.04253464,0.00391657,0.00127298,-0.04489775,-0.00351822,-0.02931448,0.09364314,-0.02098929,-0.01697091,-0.00651561,0.02455586,0.01866282,-0.03514378,0.04889654,0.01454882,-0.05416784,0.10265207,0.03343217,-0.03910781,-0.00906892,0.04852119,0.11256628,-0.06186902,0.053446,-0.00805934,0.04607577,0.0200849,0.02406141,0.0000526,-0.00084902,-0.03823165,-0.19569342,-0.01058473,-0.02567953,0.00796379,0.02118415,-0.0283405,0.02311211,-0.01486918,-0.00825588,0.07662382,0.10387999,-0.06678558,-0.02114646,0.03360087,-0.00744242,0.01831538,-0.10473688,-0.01227786,-0.02402463,0.03746499,-0.03164325,0.04675717,-0.02410894,-0.09784508,0.04126559,-0.00289781,0.13130671,0.02195466,0.00201815,-0.03300067,0.08690251,-0.00140662,0.01102077,-0.05836378,0.00292197,0.04162868,-0.04555152,-0.0092982,0.02801972,0.00110333,-0.0849786,-0.00914392,-0.01154708,-0.10155506,0.01348147,0.03381591,-0.03877812,0.02707641,0.00055179,0.05723032,0.03008445,-0.02914485,0.03077012,0.04403085,0.07165,-0.03926777,-0.09010705,0.00671405,0.00891489,0.03753272,0.0121261,-0.05865633,0.08296619,-0.04295031,0.04944859,0.02183284,-0.02698469,-0.0147629,0.01948762,0.00645938,-0.01927519,0.10657765,0.00536453,0.05962815,-0.03525333,0.02702,0.02494914,-0.01286787,0.0074353,-0.02335769,-0.00908857,-0.05550892,0.02907234,0.094869,0.00151927,0.03306253,0.01595087,0.05944912,0.01606044,0.00636545,0.00642961,0.01054789,-0.03953067,0.05827184,0.04880272,0.02299556,-0.21518067,0.00843834,-0.04757957,0.05889274,-0.00441687,0.03363771,0.03362611,0.00124528,0.01332496,-0.02923501,0.0663012,0.02814456,0.02470536,-0.09247707,0.04313842,0.00052922,-0.01116961,-0.03954553,-0.00718502,-0.00771928,0.05086029,0.0283831,0.26620501,0.00609627,-0.02001373,0.03517135,-0.00618764,0.05524415,-0.0039953,0.0107671,-0.02203585,-0.0168434,0.05633945,-0.04066177,-0.00756172,-0.01870121,-0.02058676,-0.01365903,-0.03606593,0.03321442,-0.09447885,-0.03222196,-0.05512498,-0.01303229,0.12888213,0.04136855,-0.01893976,-0.06704312,0.05173315,0.08724844,-0.07633194,-0.06529021,-0.07588749,-0.04787733,0.05847798,0.0413853,-0.00805074,0.00798893,0.01014231,0.04988195,0.00306056,0.00470659,0.05637303,0.02312232,-0.0610397],"last_embed":{"hash":"x46pu0","tokens":432}}},"text":null,"length":0,"last_read":{"hash":"x46pu0","at":1753423534124},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>2. Generate Lotto Combinations from Groups of Numbers</u>#{1}","lines":[55,82],"size":3524,"outlinks":[{"title":"The lotto software to generate combinations as even odd low high is not free.","target":"https://forums.saliu.com/HLINE.gif","line":27}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07671794,-0.02287925,-0.04438417,-0.0618936,-0.04203459,-0.00381722,-0.02295036,-0.01745939,0.02021441,-0.04061665,0.05228778,0.00876166,0.04543262,-0.01122597,-0.00856608,-0.07569935,0.01868223,-0.05553674,-0.08702342,0.04881923,0.06768073,-0.04685732,-0.05015324,-0.05367097,0.04197687,0.00402435,-0.07638917,-0.07581379,-0.0317016,-0.23439839,0.01513398,0.0560481,0.0922519,-0.04347043,-0.0427702,-0.05195351,-0.04552454,0.02700408,-0.06241188,0.03170176,-0.00047009,0.03812471,-0.01063321,-0.00302591,0.03635679,-0.03552512,-0.02432981,0.0154674,0.04853568,0.03024808,-0.0332424,0.00634347,-0.00916829,0.08599868,0.03105818,-0.01543944,0.02676531,0.04381792,0.01519028,0.04560997,0.04966717,0.04353122,-0.1410282,0.02890908,0.00133366,0.02944787,-0.00401341,-0.0623701,0.02067977,0.01359181,0.02173502,0.02341788,-0.01628553,0.01742843,0.07049946,-0.05727876,-0.03963893,-0.02768061,-0.05145413,0.02049706,-0.05347591,-0.05094002,-0.01162997,-0.0049678,0.01941299,0.001601,0.09325952,0.02655749,0.02637778,-0.04499719,0.01907971,0.0569576,0.04084557,0.03568361,0.06671731,0.0063794,0.05878561,-0.00169619,-0.06777982,0.12293626,0.00778736,-0.03527596,0.01031929,-0.00751358,0.02706197,-0.01429369,-0.02194116,0.00536314,-0.03658393,0.01061484,0.01906921,0.05841793,0.08676518,-0.06726526,-0.06075601,-0.01268225,-0.0177579,0.03834415,-0.00158672,-0.01131645,-0.00845877,0.00478617,-0.00751805,-0.0330456,0.01870556,0.00054506,-0.0326478,0.08173653,0.04703399,0.04052729,0.03679218,0.00719776,-0.10467217,-0.01411645,0.02186364,-0.04151097,-0.02544349,0.00215259,0.01013173,0.00665263,-0.01247329,-0.00709766,0.0481348,-0.10294406,0.00366178,0.05197861,-0.0133104,-0.04173325,0.01494656,0.02622543,-0.02149164,-0.04103486,-0.08712051,-0.06953246,-0.00937918,-0.04048894,0.1239706,0.06728094,-0.07624897,-0.01647773,0.00680563,-0.02201424,0.00251331,0.08952074,0.02565175,-0.07284579,-0.03371035,0.03622808,-0.04005769,-0.07068653,-0.03212023,0.00758411,-0.02602449,0.06760024,0.08782939,-0.04382176,-0.0977885,-0.04288015,-0.00325898,0.02342912,0.014819,0.0203505,-0.03788464,-0.01883097,-0.02464221,-0.09928596,-0.0022232,0.00281768,0.0205078,0.01103797,-0.02350892,0.03922891,-0.05674475,-0.01319974,-0.01995664,-0.02288678,0.01340018,-0.0358546,0.06817209,-0.02750279,-0.00634647,-0.014116,0.03611878,0.05328017,-0.01541115,0.07720438,0.01208956,-0.06618136,0.12015056,0.03124523,-0.03614093,-0.01084159,0.03213852,0.10530116,-0.06433164,0.05291998,-0.01547016,0.02670266,0.02839441,0.03230145,0.02685356,0.01741422,-0.04612843,-0.19931634,-0.00788258,0.0324408,-0.01265709,-0.0104921,-0.01292845,0.05557794,-0.03841267,0.02558865,0.06906512,0.12343831,-0.07558953,-0.02399893,0.04449769,-0.01953597,0.02690271,-0.10213913,-0.00761094,-0.01495573,0.05123149,-0.04874973,0.04181995,-0.0178244,-0.08717373,0.01992234,-0.0009409,0.13663347,0.03229272,0.00997854,-0.03166591,0.06937264,0.00882932,-0.0251074,-0.03615602,0.01464649,0.04081927,-0.03875868,-0.00664142,0.0180477,-0.01422969,-0.08917814,-0.0247463,-0.03267027,-0.10046387,-0.00053815,0.03198804,-0.04111248,0.03903363,0.0116935,0.04641271,0.04367874,-0.0350411,0.03726224,0.04974983,0.0395826,-0.05138469,-0.08879217,0.01217776,-0.00097381,0.07755983,0.00135724,-0.05727318,0.04569774,-0.01539757,0.04347502,0.01156412,-0.01141775,-0.00694599,0.06700126,-0.03215715,-0.00892481,0.08212706,-0.0009258,0.04680912,-0.01832944,0.00750733,0.03387743,-0.03708927,-0.00624393,-0.01521155,-0.00898318,-0.05448719,0.02364045,0.10045132,0.00199964,0.0212257,0.01437232,0.08288407,0.02408441,0.0184853,0.02081698,0.01855483,-0.03403219,0.05935692,0.00533706,-0.00550075,-0.2430516,0.01020302,-0.03093834,0.06058117,0.01863762,0.02574669,0.03152485,0.02634377,0.00928109,-0.01333559,0.08486287,0.02873883,0.04035448,-0.08793636,0.054524,-0.01952274,-0.03703539,-0.01991749,0.02796599,-0.01267305,0.04356935,0.02312338,0.24702077,0.00881537,-0.02578832,0.04188908,0.01788154,0.0238824,0.01159702,0.02235004,-0.01471182,-0.01661375,0.06749697,-0.01689843,-0.00241153,-0.04350817,-0.01915661,-0.0354172,-0.03006523,0.05732302,-0.09148771,-0.03043679,-0.04903398,-0.02027879,0.1489142,0.04760342,-0.01621051,-0.06127123,0.04479649,0.06439973,-0.06065502,-0.04031789,-0.04821106,-0.05736116,0.0399872,0.03889578,-0.02440354,0.01629282,0.0150811,0.04528674,-0.02891697,0.02160844,0.06531499,0.01005178,-0.03530296],"last_embed":{"hash":"1w83lze","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1w83lze","at":1753423534270},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>","lines":[83,158],"size":7761,"outlinks":[{"title":"The menu of the only lottery software program to generate combinations from user groups of numbers.","target":"https://forums.saliu.com/UserGroups6.gif","line":13},{"title":"The number group software is component of the powerful Bright applications.","target":"https://forums.saliu.com/HLINE.gif","line":75}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07571352,-0.02437693,-0.04225022,-0.06105941,-0.04005169,-0.00122611,-0.02347383,-0.01806075,0.0207488,-0.03899103,0.05185037,0.00998106,0.04379948,-0.01289003,-0.00484612,-0.07582792,0.01804179,-0.05780523,-0.08698363,0.049535,0.06872578,-0.04877191,-0.04986769,-0.05349822,0.04151271,0.00371889,-0.07658867,-0.07588445,-0.03225058,-0.23510084,0.01685939,0.05491823,0.09088363,-0.04504475,-0.04180389,-0.05198854,-0.04491964,0.02688496,-0.06028198,0.03221161,-0.0000481,0.03789826,-0.00953679,-0.00211242,0.03633728,-0.03346691,-0.02625358,0.01387113,0.04975675,0.03157663,-0.03132153,0.00537248,-0.00890306,0.08573702,0.03103865,-0.01812906,0.02800575,0.04613988,0.01340573,0.04337266,0.04979138,0.0447328,-0.142746,0.0290565,-0.00109521,0.03102514,-0.00288666,-0.06109667,0.01925845,0.01537756,0.02024894,0.02354162,-0.01375482,0.0185969,0.06869421,-0.05700267,-0.03832402,-0.02592601,-0.05118775,0.02147223,-0.05390133,-0.0483383,-0.00997804,-0.00372852,0.01590754,0.00240458,0.09460419,0.02576924,0.02484061,-0.04530184,0.01890317,0.05438143,0.04096581,0.03838715,0.06349985,0.00581288,0.05658852,0.00156021,-0.06850749,0.12030577,0.00849503,-0.03635978,0.01364661,-0.00730791,0.02850687,-0.01292095,-0.0220911,0.00538977,-0.03750787,0.01169087,0.01952618,0.0601776,0.08419269,-0.06854038,-0.05885718,-0.01192869,-0.01626375,0.03682316,-0.0011629,-0.01051368,-0.01035133,0.00450603,-0.00834493,-0.03429354,0.01922615,-0.00102077,-0.03243307,0.08250686,0.04912685,0.04071443,0.03593727,0.00888942,-0.10725037,-0.01389812,0.02181715,-0.0411383,-0.0253016,-0.00154129,0.0100133,0.0077635,-0.01126365,-0.00488931,0.04846679,-0.10180309,0.00203164,0.05500196,-0.00960361,-0.04066364,0.01706672,0.02653239,-0.02096699,-0.03912009,-0.08715612,-0.06902071,-0.00940881,-0.03656804,0.12374032,0.06430434,-0.07869358,-0.01413207,0.00771084,-0.02373458,0.00225278,0.08975542,0.02672224,-0.07395702,-0.03291203,0.03721058,-0.03883545,-0.07180477,-0.03324005,0.00923858,-0.02627856,0.06501805,0.08831467,-0.0431486,-0.09904961,-0.03950413,-0.00378299,0.02329399,0.01587177,0.02042971,-0.03810181,-0.01884489,-0.02620322,-0.09871412,-0.00262351,0.00252988,0.02027334,0.01264805,-0.02381164,0.04089875,-0.05863536,-0.01460953,-0.02092777,-0.0225286,0.01280413,-0.03731598,0.06669767,-0.02815048,-0.00821251,-0.0123309,0.03506221,0.05077057,-0.01527116,0.0773345,0.01268684,-0.0647159,0.11953338,0.03210492,-0.03878747,-0.01129282,0.0319491,0.10483024,-0.06833812,0.0533604,-0.01457314,0.02272524,0.02727555,0.03245121,0.02655429,0.01960293,-0.04456538,-0.19885381,-0.00936921,0.03175268,-0.01191403,-0.00934509,-0.01538771,0.05368693,-0.03651547,0.02259611,0.06864393,0.12426595,-0.07588707,-0.02357059,0.04652741,-0.0195677,0.0252269,-0.10211264,-0.0078083,-0.01538652,0.05151829,-0.05026964,0.03978145,-0.01539036,-0.08601939,0.02083508,-0.00372573,0.13815765,0.03389176,0.00751492,-0.02922343,0.07172561,0.00955302,-0.02535433,-0.03966748,0.01570091,0.03985333,-0.0398645,-0.00878937,0.01694543,-0.01243692,-0.0893435,-0.02303364,-0.03199225,-0.09874281,-0.00222739,0.02996208,-0.04076729,0.03987306,0.01189842,0.04927547,0.04345851,-0.03418381,0.03464225,0.04927704,0.03838384,-0.05308102,-0.0899976,0.01521895,-0.00190478,0.0777011,0.0020458,-0.05834498,0.04482197,-0.01804096,0.04552389,0.00842572,-0.01216008,-0.00703347,0.06709777,-0.03249531,-0.00850375,0.08186334,-0.0031649,0.04578328,-0.01632862,0.00812479,0.0352369,-0.03832499,-0.00605822,-0.0164614,-0.01072061,-0.05452775,0.02241985,0.09951718,0.00288356,0.02286227,0.01464466,0.08184967,0.02193646,0.01792737,0.0179156,0.02001686,-0.03449092,0.05821719,0.00652596,-0.00647866,-0.24370588,0.01123112,-0.02919633,0.06265476,0.02061202,0.02566767,0.03108611,0.02600876,0.00803765,-0.01193118,0.08650286,0.02715513,0.03875797,-0.08825827,0.05777216,-0.0195391,-0.03528016,-0.0188171,0.02852129,-0.01353116,0.04426846,0.02114367,0.24630783,0.00644467,-0.02523755,0.04099312,0.01708872,0.02384861,0.01115092,0.02332526,-0.01446506,-0.0187717,0.06857309,-0.01567752,-0.00123032,-0.04261016,-0.01954937,-0.03503565,-0.02996547,0.05533319,-0.09292493,-0.03100791,-0.05006506,-0.02165785,0.14947732,0.04935472,-0.0144732,-0.06393471,0.04528621,0.06557817,-0.05897342,-0.03958581,-0.05071054,-0.05706738,0.04092747,0.03874459,-0.02248863,0.01363749,0.01798148,0.04713526,-0.02903587,0.02232113,0.06478784,0.00886827,-0.03562397],"last_embed":{"hash":"1l9kem1","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"1l9kem1","at":1753423534443},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>3. New Lotto Programs: UserGroups</u>#{1}","lines":[85,158],"size":7716,"outlinks":[{"title":"The menu of the only lottery software program to generate combinations from user groups of numbers.","target":"https://forums.saliu.com/UserGroups6.gif","line":11},{"title":"The number group software is component of the powerful Bright applications.","target":"https://forums.saliu.com/HLINE.gif","line":73}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09144032,-0.05811597,-0.02585137,-0.03174716,-0.03360005,0.00576947,-0.0491362,-0.01964649,0.01299466,-0.04453924,0.04274403,0.00634916,0.06354323,-0.00357224,-0.03841031,-0.06095507,0.03161569,-0.01753199,-0.05339642,-0.02753234,0.07122356,-0.02991259,-0.05399887,-0.05695661,0.07032827,0.02782492,-0.07675786,-0.07428945,-0.05152622,-0.25669339,0.00233006,0.04174018,0.05939975,-0.04921705,-0.03977491,-0.04472178,-0.03344753,0.02077182,-0.07807221,0.05343142,0.01268265,0.03437464,-0.00853945,-0.01969949,0.01570179,-0.02122428,-0.01278237,0.00805021,0.00240128,0.0155391,-0.05222317,0.00849059,-0.0057691,0.08182593,0.03234177,0.01764173,0.02247136,0.04620936,0.03273066,0.0549806,0.05468881,0.04612418,-0.14739999,0.04425706,0.01875196,0.01859243,-0.00904149,-0.08483449,0.04316314,0.0185628,0.02307434,0.03830454,-0.01077885,0.01158124,0.06435956,-0.05841417,-0.02975824,-0.04179782,-0.04054403,0.02926238,-0.04073636,-0.04349887,-0.02203368,-0.02483366,0.03437667,0.0020652,0.06705786,0.02278058,0.04873916,-0.07469734,0.00827626,0.0769743,0.02029904,0.03640671,0.06740995,0.01292347,0.0875983,0.00444391,-0.02833785,0.12575485,-0.00012736,-0.01655492,0.00162201,-0.00531525,0.03471719,-0.03786784,-0.0288484,-0.01697906,-0.03639812,0.01697533,0.0438025,0.03286594,0.07725398,-0.04136511,-0.05770183,-0.04046143,-0.02808358,0.00485527,-0.00647184,0.00697891,-0.01766312,0.0103174,0.01461181,-0.01843708,0.04119453,0.03439963,-0.02386949,0.05840787,0.04747597,0.02362223,0.05617686,0.01180279,-0.09731058,-0.0447851,0.00469873,-0.01510299,-0.02208944,0.01683657,-0.0006912,0.01404199,-0.03286429,-0.0330746,0.04448223,-0.10579811,-0.00612031,0.08240541,-0.04532839,-0.0231556,0.01629655,0.04663928,-0.02520467,-0.00177585,-0.0858134,-0.08190506,-0.00442526,-0.06587163,0.12321592,0.08889241,-0.04689744,-0.00045095,-0.00711949,0.00245195,-0.01821185,0.08927442,-0.01656058,-0.0881544,-0.02357611,0.04500163,-0.04791354,-0.05978987,-0.03454815,0.01325977,0.00159277,0.04220092,0.08585949,-0.05262129,-0.05865562,-0.04942602,-0.00749065,0.00008833,-0.01500347,0.00594094,-0.02080673,-0.01835512,-0.02645196,-0.0940145,0.00818097,-0.0084521,0.04895096,0.01959015,-0.04227276,0.01337926,-0.05106585,0.00183843,-0.01204654,-0.03479608,-0.02896411,-0.03130328,0.05904572,-0.03103198,0.00075979,0.00180621,0.07086076,0.0156573,0.00587652,0.0729957,-0.01495935,-0.06790543,0.09005319,0.02804803,-0.0245282,-0.0049668,0.06068531,0.09354456,-0.03763711,0.04929484,-0.03440287,0.01101278,-0.01764239,0.0372646,0.00309364,0.06586754,-0.08564728,-0.20424718,-0.02013478,0.02220684,-0.02446365,0.01097301,-0.05677554,0.05184482,-0.02672858,0.0472593,0.09110192,0.09684207,-0.05733344,-0.02022462,0.0514162,0.00155694,0.01041915,-0.07599365,-0.02131405,-0.04513947,0.03957556,-0.01704217,0.05732334,-0.03185245,-0.07470986,0.00801412,0.00882521,0.10031892,0.00894598,0.02225026,-0.04505632,0.0372352,-0.00593129,-0.0232104,-0.01890071,-0.00340485,0.07469768,-0.02540536,-0.02168948,0.01099851,-0.01541176,-0.08390272,-0.03346531,0.00179554,-0.12449422,0.00086963,0.04391865,-0.02254309,0.03652722,0.00056322,0.04620736,0.05495305,-0.02545503,0.06886342,0.05186782,0.04013158,-0.04121141,-0.06625357,-0.01961957,0.00966699,0.06601764,0.00228645,-0.05573195,0.04906623,-0.01166728,0.04949626,0.02371861,-0.02247181,-0.0267207,0.06309227,-0.0182648,-0.01863617,0.12580414,-0.00833584,0.0387109,-0.00900539,0.0364115,0.04197871,-0.04511499,-0.00114501,-0.02029698,0.00010975,-0.03308365,0.05521151,0.04565452,0.00305487,0.02337216,0.04846769,0.06924741,0.00709854,0.02066092,0.04956606,-0.0053207,-0.0136414,0.04663872,0.02749806,0.00882811,-0.25182799,0.00156886,-0.03480053,0.00024369,-0.01081299,0.00469352,0.04451931,0.00982543,0.0139093,-0.04488729,0.04675198,0.03396146,0.04834061,-0.08643506,0.0318588,-0.00974063,-0.02202096,-0.03555787,0.03376751,0.00532923,0.03705486,0.0424399,0.26337847,-0.00488757,-0.01079437,0.05130989,0.05022078,0.01165082,0.01949801,0.01166513,-0.0230501,-0.01135723,0.07618577,-0.01900539,-0.03305582,-0.0290705,-0.02184156,-0.03821003,-0.00629379,0.05692698,-0.03979189,-0.02088065,-0.04420657,0.00236977,0.14192015,0.0033168,-0.03562969,-0.0618793,0.0614385,0.06460029,-0.05444055,-0.07354161,-0.05785306,-0.03019837,0.04541143,0.03930684,-0.01511756,0.02603991,0.00307353,0.03055643,-0.0121529,0.0215377,0.05103097,0.04450903,-0.00921192],"last_embed":{"hash":"1v4injx","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1v4injx","at":1753423534623},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>","lines":[159,192],"size":2128,"outlinks":[{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/UserGroups5-1.gif","line":5},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/sums-odd-even.gif","line":9},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-stats.gif","line":11},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":31},{"title":"This is the best known strategy played by many lotto players – based on balance.","target":"https://forums.saliu.com/HLINE.gif","line":33}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09094308,-0.05862473,-0.02249876,-0.03416259,-0.03203804,0.0065691,-0.05217249,-0.02025904,0.01545866,-0.0408958,0.04395748,0.00717807,0.06479754,-0.0058771,-0.03609984,-0.06204581,0.02730827,-0.01930493,-0.05371198,-0.02792081,0.06939494,-0.02935119,-0.05313917,-0.0576327,0.06944569,0.02688983,-0.07485431,-0.07461236,-0.04935574,-0.25905031,0.00444862,0.04255406,0.05677186,-0.05098951,-0.03780992,-0.04707592,-0.03302722,0.01832454,-0.0738191,0.05299703,0.01056614,0.03560502,-0.00759686,-0.01959353,0.0162503,-0.02109916,-0.01245997,0.00837751,0.00115714,0.01547931,-0.05058846,0.00596438,-0.0061504,0.08288986,0.03558136,0.01528199,0.02269008,0.047028,0.02858234,0.0538227,0.05341474,0.04606015,-0.14827868,0.04261441,0.01618807,0.01751223,-0.00933887,-0.08493585,0.0422012,0.0179947,0.02072565,0.04105508,-0.00969851,0.01219877,0.06422365,-0.05639518,-0.03093051,-0.04490842,-0.03922251,0.03264753,-0.04274387,-0.04034474,-0.02326775,-0.02615848,0.03426155,0.00331291,0.06706519,0.02008022,0.04651823,-0.07388533,0.00684556,0.07537822,0.02206932,0.03774305,0.06622244,0.01278102,0.08555508,0.00693629,-0.02916837,0.12355158,0.00178941,-0.01606,0.00120878,-0.00583997,0.03330725,-0.03248616,-0.02879694,-0.01679911,-0.0364296,0.01803,0.04446358,0.03546213,0.07379116,-0.04274129,-0.05508903,-0.04100449,-0.02770271,0.00387857,-0.00825235,0.00699521,-0.01569396,0.00912763,0.0139038,-0.0178413,0.04084989,0.03686602,-0.02318026,0.05866481,0.04720069,0.02371718,0.05450275,0.00984694,-0.09606367,-0.04557355,0.00597003,-0.01295903,-0.02308867,0.01566702,-0.00013573,0.01681869,-0.03557982,-0.0320481,0.04314362,-0.10596614,-0.00606953,0.08406074,-0.04295504,-0.02260596,0.01734686,0.04748351,-0.0226357,-0.00120398,-0.08515753,-0.08445013,-0.00525151,-0.06638084,0.12257583,0.08787082,-0.0493638,0.00074894,-0.00799964,-0.00164214,-0.02007082,0.08921142,-0.01740403,-0.08886152,-0.02417617,0.045249,-0.04492155,-0.05857845,-0.03477293,0.01307462,0.00174423,0.0429972,0.08798623,-0.05151401,-0.0600334,-0.04915809,-0.00761439,0.00189749,-0.0146292,0.00671962,-0.02338039,-0.02033765,-0.02673124,-0.09302486,0.00821471,-0.0079512,0.05079366,0.01971483,-0.04326315,0.0147628,-0.05135719,0.00401864,-0.01096034,-0.03485997,-0.0302391,-0.0309335,0.06046843,-0.02921879,0.00347257,0.00415935,0.06977496,0.0101429,0.00753255,0.07430962,-0.01581883,-0.0656222,0.08795298,0.02955599,-0.02547514,-0.00460882,0.06241184,0.09426004,-0.03864668,0.05001688,-0.03248238,0.00779364,-0.01485463,0.03759974,0.00150229,0.06938146,-0.0857605,-0.20236434,-0.02183194,0.02293917,-0.02576451,0.00866585,-0.05936012,0.05258939,-0.02733611,0.04981945,0.0879265,0.09454253,-0.05488911,-0.02054318,0.05450611,0.00211333,0.01291693,-0.07555389,-0.02024688,-0.04599642,0.04041595,-0.01943847,0.0576368,-0.0277341,-0.07398083,0.00787993,0.00755013,0.10231662,0.00692582,0.0191449,-0.04543104,0.03637585,-0.0045685,-0.02352565,-0.02206323,-0.00304832,0.07764994,-0.02478859,-0.02577807,0.01173378,-0.01639715,-0.08276634,-0.03403931,0.00428717,-0.12659611,0.00321708,0.04250127,-0.02124993,0.03687959,0.00121521,0.04631527,0.05489113,-0.02629214,0.07020448,0.05032095,0.03797319,-0.04403102,-0.06576174,-0.01953835,0.01111917,0.0673128,0.00213354,-0.05476449,0.04900029,-0.01403997,0.04821561,0.01893112,-0.0211053,-0.02361634,0.06256922,-0.02092247,-0.01905691,0.12593047,-0.01021361,0.04157847,-0.01014988,0.03375978,0.04247361,-0.04332787,-0.00441846,-0.02248783,-0.00252112,-0.03358225,0.05440797,0.04069485,0.00439165,0.02158248,0.05002634,0.06710764,0.0081601,0.02235899,0.04749651,-0.00454507,-0.01528188,0.04532623,0.0287123,0.00844813,-0.25466636,0.0010495,-0.03102528,0.00040025,-0.00893225,0.00538389,0.04604158,0.00818008,0.0116725,-0.04688841,0.04753855,0.03294523,0.04798673,-0.08672827,0.03645163,-0.00690027,-0.02255516,-0.0324315,0.03457646,0.00098414,0.03673423,0.04329448,0.26440215,-0.00516552,-0.01096158,0.05298787,0.05004683,0.01202901,0.02191337,0.01026444,-0.02389074,-0.01021462,0.07749496,-0.01846296,-0.03560572,-0.02647891,-0.02162959,-0.04014153,-0.00366392,0.0569171,-0.04305436,-0.02101928,-0.04466569,0.00457618,0.14302707,0.00358108,-0.03406469,-0.05969726,0.06153267,0.06532829,-0.04927482,-0.07498207,-0.05834191,-0.02797927,0.04617492,0.04054549,-0.01728746,0.02455681,0.00249646,0.03085057,-0.01220943,0.02340232,0.05036079,0.0430831,-0.00839875],"last_embed":{"hash":"1d01afe","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"1d01afe","at":1753423534811},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>4. Upgrades to Yet Unreleased Software</u>#{1}","lines":[161,188],"size":1813,"outlinks":[{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/UserGroups5-1.gif","line":3},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/sums-odd-even.gif","line":7},{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-stats.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08921275,-0.0265112,-0.03659763,-0.05290708,-0.04632194,0.00932822,-0.01813067,0.00277466,0.02897619,-0.03923738,0.03286772,-0.00378992,0.05593235,0.00275545,0.0090196,-0.04741066,0.01812876,-0.01052119,-0.04476518,0.01878044,0.09169834,-0.00994248,-0.04107499,-0.04641968,0.05695482,-0.00856646,-0.05161895,-0.08109992,-0.02297247,-0.25601563,0.02678701,0.02587531,0.04290838,-0.0455645,-0.06350999,-0.02027275,-0.04139822,0.06356272,-0.0578242,0.0378089,0.00862056,0.04985592,0.03213418,-0.00634197,0.00919486,-0.01557946,-0.02177479,-0.00572409,0.02037756,0.0245144,-0.04352417,-0.01611034,0.0059198,0.04678364,0.01174193,0.0280224,0.03407021,0.09258487,0.02858355,0.08764569,0.0186182,0.05933525,-0.15260226,0.03111536,0.02174299,0.02497063,-0.02660752,-0.05219658,0.00573703,0.05544051,0.0363001,0.05180556,-0.02501287,0.01273411,0.0340557,-0.02743944,-0.03113488,-0.06549799,-0.03975139,0.03291705,-0.04511513,-0.0566516,-0.01757452,-0.02155377,0.05670014,0.00655764,0.04979384,0.03299275,0.07345348,-0.06233807,0.00987816,0.06730305,0.02972685,0.02072709,0.09575573,0.00389256,0.03640339,-0.01150423,-0.03578782,0.10950911,0.02451866,0.02831281,0.01792609,-0.01693591,0.04316803,-0.04547266,-0.02982806,-0.00493814,-0.03606128,0.00394793,0.06219665,0.00236611,0.10596276,-0.05596074,-0.04486423,-0.01146795,0.01953668,0.00590141,0.00459515,-0.00545643,-0.00677579,0.00982531,0.02147835,-0.03160068,0.03056588,-0.00191321,-0.0103736,0.07265813,0.02775263,0.01568554,0.01555629,-0.03137279,-0.09198564,-0.05155518,-0.0235916,-0.02506818,0.01203556,0.01679445,0.00526041,0.02754991,-0.06045723,-0.02794482,0.04682117,-0.12215552,-0.02594676,0.08401322,0.00353127,-0.00611825,0.01861126,-0.01060411,0.0018381,-0.02689838,-0.05190763,-0.07997689,-0.01172145,-0.0046487,0.09304933,0.11084328,-0.06131,-0.01135156,-0.03629892,-0.00770396,-0.01251532,0.10783051,-0.01948308,-0.05836567,-0.01087769,0.03347917,-0.07259857,-0.08780338,-0.02391423,-0.00360237,-0.01066722,0.04125978,0.09675097,-0.03573638,-0.0809422,-0.04409183,-0.00287686,-0.01149919,0.00866785,0.00979318,-0.02665539,0.00804685,-0.0169441,-0.08182869,0.02716104,0.00817496,0.03213194,0.0052234,-0.07719917,-0.02907528,-0.05227645,0.01854334,-0.01791426,-0.01808329,0.01578033,-0.02228327,0.08217638,-0.02307615,0.00305352,-0.02158594,0.02127349,0.01941101,-0.01115478,0.07313287,-0.0259279,-0.06676817,0.1034041,0.03639747,-0.0227906,0.00868977,0.02601927,0.11563206,-0.02043492,0.03493872,-0.03394529,0.03286529,-0.0078826,0.00660627,0.01146689,0.02268541,-0.0715296,-0.20731403,-0.01085888,0.00013047,-0.00042949,0.01725568,-0.02162049,0.01643202,-0.03501198,0.0404254,0.09650383,0.07083398,-0.04392676,-0.04100868,0.0342657,-0.02709917,0.01834122,-0.10658605,-0.01509746,-0.05193277,0.04557457,-0.00721068,0.03375585,-0.03959172,-0.08955243,0.04792122,-0.00667087,0.12755078,-0.0014029,0.00911752,-0.01768087,0.04650898,-0.01836567,-0.02964567,0.00011835,0.01200408,0.04963427,-0.00185282,-0.05630205,-0.01639519,-0.00149,-0.06515555,-0.04661773,-0.02315811,-0.12829079,-0.00511436,0.06560533,-0.03222843,0.04157037,0.01177093,0.04007319,0.03911388,-0.02322528,0.04133245,0.04876069,0.07204797,-0.05584353,-0.09049615,-0.00334526,-0.00220414,0.0231803,-0.03432344,-0.06088472,0.0579011,-0.00183284,0.03152952,0.03530214,0.0145675,-0.01271909,0.0313405,0.02977735,-0.01034203,0.08283495,-0.01224244,0.04143726,-0.06477556,0.01795639,0.06645074,-0.02741041,0.00481919,-0.04658756,0.02719439,-0.02905394,0.04690192,0.07625663,0.01130667,-0.0120993,0.05237568,0.0368785,0.02475404,-0.00621626,-0.00647972,0.02603711,-0.01730085,0.0439375,0.03103788,-0.00036826,-0.26905656,0.00560938,-0.05716786,0.03514318,0.00277903,0.01267805,0.03606446,0.01617312,0.00187119,-0.02742856,0.04654037,0.04082733,0.03677526,-0.08498004,0.00101066,-0.01585509,-0.02666353,-0.03508988,0.0769947,0.01933203,0.0793082,0.04623372,0.25550708,-0.00960641,-0.01798421,0.03685975,0.02985108,0.03259476,-0.01699408,0.02683553,-0.02171756,-0.01983795,0.07348272,-0.01155715,-0.0317165,-0.0231257,0.01173968,-0.03174269,-0.03856279,0.04829838,-0.06750248,-0.02106108,-0.02874795,-0.0096883,0.13892746,0.03613542,-0.05400829,-0.0462921,0.03196735,0.04813457,-0.0667619,-0.03944465,-0.04604136,-0.06055157,0.05346929,0.06617055,-0.02826759,0.02590734,-0.0343716,0.02623487,0.00114214,0.02053267,0.04017299,0.04703468,-0.00566574],"last_embed":{"hash":"x8ogve","tokens":422}}},"text":null,"length":0,"last_read":{"hash":"x8ogve","at":1753423534994},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>","lines":[193,223],"size":2621,"outlinks":[{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-strategy.gif","line":24},{"title":"Download your lotto software to generate combinations of numbers – odd or even, low or high.","target":"https://forums.saliu.com/HLINE.gif","line":26},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":28},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":28},{"title":"Search","target":"https://saliu.com/Search.htm","line":28},{"title":"Exit the best site of software, programs, strategies, mathematics of lotto for the world.","target":"https://forums.saliu.com/HLINE.gif","line":30}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08412831,-0.02679161,-0.03682148,-0.05559,-0.0470667,0.01333746,-0.02094306,0.00131182,0.03183583,-0.0379332,0.03155807,-0.00907155,0.06073251,0.00103344,0.01057504,-0.04594024,0.01207322,-0.01439384,-0.0534398,0.01560323,0.09877921,-0.01426684,-0.04295824,-0.04873342,0.05354426,-0.0098407,-0.04942333,-0.08028209,-0.02002828,-0.25915638,0.02905084,0.0215156,0.04115593,-0.04789094,-0.06430209,-0.02348343,-0.03946992,0.07037768,-0.05540031,0.03563969,0.00566912,0.05004359,0.0362728,-0.00724595,0.00686417,-0.01661811,-0.01977299,-0.00578633,0.02263803,0.02269855,-0.03745079,-0.01825456,0.00935904,0.05179506,0.01020603,0.02378047,0.03188167,0.0942608,0.03134704,0.08525039,0.01010548,0.06348727,-0.15394405,0.02718148,0.01889708,0.02391207,-0.03249253,-0.04498306,0.00612897,0.05273577,0.03373968,0.05745452,-0.02309893,0.01437417,0.03366282,-0.02535191,-0.03733963,-0.067225,-0.04028067,0.03239748,-0.04584864,-0.05851296,-0.01970777,-0.01700227,0.05458134,0.00349408,0.05267165,0.0319521,0.07267199,-0.05488208,0.00902974,0.06499651,0.02148849,0.0267944,0.09440726,0.00451474,0.03586835,-0.0125822,-0.02821469,0.10523119,0.02516074,0.02913244,0.02012837,-0.02173601,0.03998151,-0.04780343,-0.02983734,-0.00329053,-0.03771112,0.00996688,0.06205422,0.00011675,0.10001557,-0.06323248,-0.04071461,-0.009285,0.02258117,0.00051783,0.00501498,-0.00836799,-0.00810958,0.01102649,0.01930465,-0.03455952,0.03413403,0.00382222,-0.00663717,0.07721169,0.0315525,0.02026433,0.01277537,-0.02511644,-0.09793797,-0.05151353,-0.02037701,-0.02620535,0.0145251,0.01900744,0.0031591,0.02788756,-0.06191329,-0.02185573,0.04606296,-0.11792307,-0.02458504,0.09238929,0.00439781,-0.00373875,0.02111829,-0.01496067,0.00597101,-0.02182771,-0.04437347,-0.08147533,-0.01367136,0.00544966,0.09015889,0.10697392,-0.05965486,-0.01296082,-0.03781163,-0.01009191,-0.01262504,0.10304455,-0.01628698,-0.06235144,-0.01065313,0.03759874,-0.07238387,-0.08429925,-0.02154172,0.00032759,-0.01270423,0.03853439,0.09414338,-0.03110484,-0.08642568,-0.04165211,-0.00017173,-0.01313686,0.00981251,0.01473622,-0.03348814,0.00571708,-0.02492235,-0.07824479,0.02764541,0.00881473,0.02737755,0.01308274,-0.07348126,-0.02796835,-0.04799142,0.02156669,-0.01315854,-0.0165087,0.01534694,-0.01830797,0.08473057,-0.02520631,0.01118791,-0.02153561,0.01981647,0.01577629,-0.01381443,0.06956762,-0.03039139,-0.06220779,0.10500421,0.03520192,-0.02491069,0.01212287,0.0252121,0.12093648,-0.02273166,0.03590564,-0.03161952,0.02938692,-0.0068101,0.00487204,0.0103422,0.01784088,-0.06633474,-0.20273559,-0.00935076,0.00109425,0.00339329,0.01479537,-0.01814582,0.01272634,-0.0387309,0.03913138,0.09532482,0.07186095,-0.03965639,-0.04162327,0.03578153,-0.02778878,0.02369888,-0.10962117,-0.01008357,-0.05477431,0.04654962,-0.00642252,0.03183077,-0.03486422,-0.08917146,0.04906537,-0.01145782,0.12737617,-0.00606542,0.00246524,-0.01836373,0.04511643,-0.01590888,-0.03367618,0.00682133,0.01207295,0.05091201,-0.00717462,-0.05684732,-0.01600647,-0.0014707,-0.06278989,-0.04601825,-0.02929996,-0.12889782,-0.00254304,0.0640135,-0.0352214,0.04144965,0.01476789,0.03750118,0.03470557,-0.02571441,0.0482032,0.0373541,0.07180628,-0.06320069,-0.08995942,-0.00263159,-0.00159619,0.02126245,-0.03258791,-0.06109848,0.05546401,-0.0001883,0.03012243,0.03033143,0.01656529,-0.0084575,0.02595634,0.02766612,-0.00666679,0.07765128,-0.01595078,0.04118937,-0.06441735,0.0152631,0.06089433,-0.02334001,0.00552413,-0.05445228,0.03275181,-0.02961025,0.04430833,0.0757041,0.01233052,-0.01736136,0.05466116,0.03981458,0.03106208,-0.00789717,-0.01320816,0.029124,-0.01590566,0.03906466,0.02641142,-0.0010014,-0.27130705,0.00498031,-0.0615943,0.03717697,0.00526608,0.00597212,0.03741344,0.02289705,0.0056407,-0.02554005,0.04235933,0.0451091,0.03576844,-0.08476347,0.00244193,-0.01654701,-0.02633056,-0.03650723,0.07548597,0.01616663,0.08178857,0.04201604,0.25623405,-0.00776018,-0.01514592,0.03213496,0.02816098,0.03209665,-0.01744552,0.02977667,-0.02476463,-0.0177768,0.08310337,-0.01520606,-0.03350207,-0.02114816,0.01271759,-0.03059548,-0.0391198,0.05030002,-0.06666407,-0.01867716,-0.0314179,-0.00932645,0.13715705,0.03622756,-0.05234865,-0.04878334,0.03121213,0.04839101,-0.06717692,-0.03534309,-0.04226527,-0.05905722,0.05475747,0.06403775,-0.02542876,0.03181276,-0.03935524,0.02805046,0.00034512,0.02007283,0.03644393,0.05318659,-0.00559747],"last_embed":{"hash":"1ry2bdf","tokens":484}}},"text":null,"length":0,"last_read":{"hash":"1ry2bdf","at":1753423535181},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{1}","lines":[195,212],"size":1670,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10722208,-0.0307253,-0.04431117,-0.0334205,-0.0574642,0.00100653,-0.03294954,0.0124765,-0.00546529,-0.05701396,0.02999437,-0.0098486,0.01355464,0.0135701,-0.02054131,-0.05244066,0.01026068,0.00985165,-0.04924543,0.01856489,0.07366245,-0.00027868,-0.04539831,-0.05205832,0.05264961,-0.00062403,-0.05564828,-0.07364351,-0.02812009,-0.21534505,-0.00349159,0.05818521,0.04241094,-0.03079872,-0.06227358,-0.02851208,-0.04782935,0.03790237,-0.09240111,0.0593829,0.01139202,0.04076276,0.01761518,-0.00552432,0.01519009,-0.03166093,0.00398944,0.0032334,0.04032475,0.02502489,-0.05612891,0.00206686,-0.02042629,0.05617094,0.02937726,0.01348953,0.03438545,0.06974362,0.00161678,0.07230185,0.03641947,0.05997501,-0.17917207,0.03899068,0.00377488,0.03231807,-0.01177598,-0.04848146,0.01904314,0.02725019,0.0145336,0.02619538,-0.02694165,0.02019087,0.07705316,-0.04143756,-0.04055981,-0.06183653,-0.02833412,0.02143408,-0.02301462,-0.03688506,-0.01964253,-0.01371003,0.04906365,0.02751194,0.05347061,0.03159169,0.06697255,-0.05122943,-0.01112055,0.05979346,0.02129637,0.01356804,0.0858063,-0.00365075,0.06639715,-0.02866446,-0.03035495,0.13179304,0.00497307,-0.00629326,0.01596097,0.01092435,0.04033997,-0.03123024,-0.03314901,-0.00715155,-0.03753611,-0.01693795,0.04317844,0.02145096,0.10380794,-0.05436137,-0.07526799,-0.03067544,-0.00871091,0.00917553,-0.00049203,0.01286798,-0.02963275,-0.00068449,0.03259654,-0.02316988,0.04739855,-0.00982056,-0.04970679,0.0587666,0.02956723,0.03505436,0.03224456,-0.00067903,-0.08726503,-0.0463499,-0.02633588,-0.05481412,-0.01117702,0.00596265,-0.01443744,-0.02063503,-0.02707208,-0.03943076,0.06261992,-0.11298248,0.00437024,0.04412344,-0.01187954,-0.02693109,0.00811713,0.01668729,0.00330382,-0.0308697,-0.04632377,-0.05293053,-0.01803839,-0.02560846,0.13432838,0.10449532,-0.05738123,0.00942805,-0.03041409,-0.01485444,-0.00492391,0.13201106,0.00306121,-0.07961388,-0.04739057,0.06062408,-0.06840609,-0.08049987,-0.0081375,0.01245589,-0.01349171,0.05834062,0.11593258,-0.05280722,-0.07307043,-0.05519024,-0.00058752,0.00454783,-0.00197644,-0.00859105,-0.00853497,0.00494596,-0.03024653,-0.07934062,0.00519983,-0.02097419,0.02846605,-0.00161997,-0.038255,-0.02369477,-0.06194599,-0.00896096,-0.02032587,-0.02554233,-0.02437555,-0.03891521,0.09004727,-0.01237697,-0.00199218,-0.00217582,0.04296589,0.03252129,-0.02528116,0.06135643,-0.01677441,-0.08841269,0.07962315,0.01758264,-0.00529107,0.021374,0.03510316,0.10653935,-0.01544711,0.0567435,-0.00681031,0.04665069,-0.02927926,0.0099054,0.03346892,0.01592227,-0.07136456,-0.19681244,-0.01762,-0.02891218,-0.02345258,0.03525865,-0.02389163,0.0553376,-0.0419228,0.02405433,0.08599724,0.08563371,-0.07398906,-0.01558236,0.07052323,-0.02228044,0.02810498,-0.1025126,-0.03624233,-0.02963138,0.03953686,-0.0105628,0.03642424,-0.05294521,-0.10039727,0.04464002,0.01370701,0.12581682,0.01595462,0.03521522,-0.0156839,0.04468283,-0.0050013,-0.01438668,-0.02236585,0.04747164,0.04300237,-0.01215629,-0.02951468,0.00835533,-0.00402906,-0.06525508,-0.01273137,0.01495653,-0.11194575,-0.02091247,0.03547617,-0.0247005,0.01470515,0.01474734,0.0274183,0.04268987,-0.00001623,0.02871247,0.05386143,0.03584721,-0.03274512,-0.07665206,-0.01645005,-0.01452107,0.0388715,-0.01831327,-0.04748341,0.06842485,-0.01205506,0.05443479,0.03517203,-0.01166034,-0.02215353,0.05985868,0.01502309,-0.00086758,0.09215896,-0.00631704,0.05462516,-0.00293956,0.02582575,0.0534716,-0.03799174,-0.00305335,-0.00893121,-0.01011655,-0.05686003,0.0646309,0.08711597,0.03194727,0.00586579,0.03303009,0.04153238,0.03005647,0.01143349,0.04589636,0.00644168,-0.0134055,0.05635151,0.01190433,0.00227775,-0.2601909,0.00844822,-0.03603152,0.04177961,-0.00297157,0.0252065,0.03159217,0.0094362,-0.00896311,-0.03442043,0.09213968,-0.00039276,0.03819773,-0.09558409,-0.0055449,-0.004805,-0.04587145,-0.05547077,0.05686917,0.02783193,0.0487692,0.05034614,0.26313338,0.00832847,-0.02770473,0.02788818,0.03860437,0.02268875,0.0036054,0.02567292,-0.01324599,-0.0045025,0.05495128,-0.01548589,-0.02858314,0.00527365,-0.00372885,-0.04721281,-0.03594593,0.03669935,-0.09566838,-0.00732915,-0.03608344,0.01052482,0.11409128,0.03332574,-0.03172735,-0.03614577,0.04232828,0.06581595,-0.0691004,-0.06215606,-0.03280628,-0.0527918,0.05478819,0.03513853,-0.03168016,0.01926255,-0.01938792,0.02732594,-0.02566594,0.02164877,0.07132373,0.02206445,-0.01829201],"last_embed":{"hash":"18vrgzs","tokens":269}}},"text":null,"length":0,"last_read":{"hash":"18vrgzs","at":1753423535363},"key":"notes/saliu/Lotto Software Program Odd Even Low High Number Groups.md#Lotto Software Program Odd Even Low High Number Groups#<u>5. The Famous Lotto Strategy of Perfect Balance</u>#{4}","lines":[216,223],"size":687,"outlinks":[{"title":"The software for groups of lottery numbers works with lotto 5 and lotto 6.","target":"https://forums.saliu.com/groups-strategy.gif","line":1},{"title":"Download your lotto software to generate combinations of numbers – odd or even, low or high.","target":"https://forums.saliu.com/HLINE.gif","line":3},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":5},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":5},{"title":"Search","target":"https://saliu.com/Search.htm","line":5},{"title":"Exit the best site of software, programs, strategies, mathematics of lotto for the world.","target":"https://forums.saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
