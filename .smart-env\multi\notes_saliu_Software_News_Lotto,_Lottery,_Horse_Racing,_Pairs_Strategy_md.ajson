
"smart_sources:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md": {"path":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"3wyckx","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753362431410,"size":15821,"at":1753423416500,"hash":"3wyckx"},"blocks":{"#---frontmatter---":[1,6],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy":[8,184],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#{1}":[10,15],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>":[16,25],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>#{1}":[18,25],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>":[26,99],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}":[28,99],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>":[100,154],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}":[102,154],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)":[155,184],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{1}":[157,160],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{2}":[161,161],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{3}":[162,162],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{4}":[163,163],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{5}":[164,164],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{6}":[165,165],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{7}":[166,166],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{8}":[167,167],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{9}":[168,168],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{10}":[169,169],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{11}":[170,170],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{12}":[171,171],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{13}":[172,172],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{14}":[173,173],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{15}":[174,174],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{16}":[175,175],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{17}":[176,176],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{18}":[177,178],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{19}":[179,184]},"outlinks":[{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/LIE-lottery-strategy.gif","line":32},{"title":"Powerful lottery strategies eliminates combinations with the worst lotto pairs.","target":"https://saliu.com/ScreenImgs/eliminate-pairs.gif","line":38},{"title":"Software generates reports for the best and the worst lotto pairs, pairings.","target":"https://saliu.com/ScreenImgs/best-lotto-pairs.gif","line":60},{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/pair-grid-pick.gif","line":147},{"title":"Missing lotto pairs can reduce drastically total combinations to play.","target":"https://saliu.com/HLINE.gif","line":153},{"title":"<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>","target":"https://saliu.com/content/lottery.html","line":155},{"title":"**Pick-3 Lottery Software**","target":"https://saliu.com/lottery3-software.html","line":161},{"title":"**Pick-4 Lottery Software**","target":"https://saliu.com/lottery4-software.html","line":162},{"title":"**Horse Racing Trifecta Software**","target":"https://saliu.com/horseracing-software.html","line":163},{"title":"**5-Number Lotto Software**","target":"https://saliu.com/lotto5-software.html","line":164},{"title":"**6-Number Lotto Software**","target":"https://saliu.com/lotto6-software.html","line":165},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":166},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":167},{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":169},{"title":"_**Lottery, Lotto Strategy in Reverse: Win, Lose, Hit, Miss**_","target":"https://saliu.com/reverse-strategy.html","line":170},{"title":"_**Software Analysis, Lotto Number Frequency, Lottery Pairs**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":171},{"title":"**<u>Software, Systems, Lottery Skips</u>**","target":"https://saliu.com/skip-strategy.html","line":172},{"title":"_**Lotto, Decades: Systems, Software, Analysis, Strategy**_","target":"https://saliu.com/decades.html","line":173},{"title":"_**Lottery, Lotto Strategy Based On Sums (Sum-Totals), Odd – Even, Low - High Numbers**_","target":"https://saliu.com/strategy.html","line":174},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":175},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":176},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":177},{"title":"The lottery software groups pick lotteries, horse racing, jackpot lotto games.","target":"https://saliu.com/HLINE.gif","line":179},{"title":"Forums","target":"https://forums.saliu.com/","line":181},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":181},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":181},{"title":"Contents","target":"https://saliu.com/content/index.html","line":181},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":181},{"title":"Home","target":"https://saliu.com/index.htm","line":181},{"title":"Search","target":"https://saliu.com/Search.htm","line":181},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":181},{"title":"Get source code to generate lotto combinations from 3 groups of numbers.","target":"https://saliu.com/HLINE.gif","line":183}],"metadata":{"created":"2025-07-24T21:07:10 (UTC +08:00)","tags":["software","lottery","lotto","horse racing","update","program","lottery pairs","reversed lottery strategy","LIE elimination"],"source":"https://saliu.com/software-news.html","author":null}},
"smart_sources:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md": {"path":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08363104,-0.01701744,0.0018581,0.06474274,-0.01838141,0.03249102,-0.04872956,-0.02059036,0.06476897,0.00257536,-0.04014217,-0.00681496,0.05905912,-0.02115833,-0.03763957,-0.03782679,-0.00201682,0.02546268,-0.03938191,-0.01496644,0.02179131,-0.04335042,-0.03361152,-0.04377264,0.03645069,0.02388557,-0.06591561,-0.02283365,-0.05760294,-0.23725317,0.00446021,0.0310699,0.00338755,-0.03231543,-0.0631937,-0.00089397,-0.02937455,0.01925712,-0.0311528,0.02788216,0.02945988,-0.01748924,-0.00560684,-0.0291525,0.00732573,-0.00295272,-0.02784092,0.00949742,0.02810058,-0.03280968,-0.03637978,-0.04222054,-0.01202305,0.01725948,0.05956713,0.06032394,0.03627161,0.11625894,0.0139385,0.0554457,0.0472218,0.12529372,-0.18609896,0.02256368,-0.01848452,-0.00341332,-0.04352735,-0.05250993,0.01735346,0.05613268,0.00057673,0.02493315,-0.05179911,0.00422638,-0.00076521,-0.03928332,0.01503091,-0.01185412,-0.00878761,-0.02184697,-0.0439049,-0.01205054,-0.03263898,-0.00916143,0.03535829,0.02400104,0.05610372,0.04324672,0.04197925,-0.09423903,0.01016151,0.01073664,0.09802473,0.03522399,0.00908157,0.04069073,0.03537714,-0.01593746,-0.02634074,0.11410424,-0.01595643,-0.01135956,-0.01723322,0.03909082,0.072027,-0.06172193,-0.02276337,-0.03188734,-0.0124,0.03396701,0.06889741,-0.01764099,0.07204589,-0.03460241,-0.00688799,0.04829527,-0.01202476,0.02749608,-0.01645975,0.0298936,-0.04314226,0.03550878,0.03675839,0.01831114,-0.0206008,-0.00385931,0.07336202,0.06039393,-0.00610429,0.0103843,-0.00834395,0.01268221,-0.11569189,-0.04756245,-0.02606835,-0.01289507,-0.00745792,-0.01011206,0.02930103,0.06209582,-0.03517684,-0.07849324,0.08332674,-0.07525717,-0.00473594,0.09734985,-0.00888853,-0.03401804,-0.01084029,0.03520697,-0.05507421,0.01905481,-0.02302914,-0.05941564,0.02127888,-0.03433558,0.06313876,0.10142954,-0.05859005,0.01149759,0.0206459,0.00376996,-0.0690646,0.0806063,-0.02231213,-0.14349039,0.02562611,0.07980296,-0.04432973,-0.03549782,-0.03067238,0.03595176,-0.01286491,-0.0223561,0.03923132,-0.03854385,-0.08681019,-0.03150396,0.0277032,-0.01903369,0.03444174,-0.01216577,-0.06666408,0.03440397,-0.01635448,-0.06996269,0.00835073,0.01520032,0.04818492,0.06318615,-0.06733774,-0.03407767,-0.03008126,-0.00939028,-0.03080472,-0.01712485,-0.03611231,-0.02624132,0.06056314,-0.01058832,-0.07479384,-0.00461084,0.02409707,-0.00893659,0.00182748,0.02768555,-0.04053021,-0.07259224,0.05052041,0.01175785,-0.04106921,0.01172891,-0.01823964,0.06070409,-0.07431522,0.03240621,-0.03749644,0.06505884,-0.03730122,-0.00250149,0.03727819,0.02988794,0.01369205,-0.21892898,-0.01515834,-0.03473527,0.00218572,0.01311629,-0.03221089,0.04021088,0.03003783,0.03869636,0.11218335,0.05960378,-0.04214378,-0.02688405,-0.03287469,0.01343327,-0.00654937,-0.04956823,-0.04913367,-0.03485172,0.02299464,0.01027526,0.02245454,-0.00834473,-0.08061757,0.048689,-0.04709892,0.13522698,0.06107659,-0.00914986,-0.01839171,-0.0011081,-0.01179942,-0.01867148,-0.08029191,0.04579344,0.06658136,0.02789711,-0.01257424,-0.04106405,0.00143409,-0.08938023,0.01565348,-0.01600461,-0.12718502,0.02240647,0.04448356,0.04080589,-0.00824562,0.02265922,0.06991149,0.05289939,-0.00592998,0.03760532,0.0790366,0.07145224,-0.05389039,-0.09136362,-0.00856586,0.00959889,0.01093188,0.01447407,-0.03572992,0.04925572,-0.03853919,0.03606127,0.01032869,-0.00275918,-0.02015035,0.03002948,0.02171243,-0.0218504,0.10003626,0.00408021,0.0293772,-0.03115099,0.03939565,0.05699561,-0.09718147,0.00155673,0.01606505,0.04151901,-0.0659249,0.08613142,0.0832717,0.05759381,0.00669436,0.06259768,0.0583529,0.02715542,0.00351384,-0.04233711,-0.00799067,-0.02594709,-0.00737838,0.06678924,0.04142489,-0.2533294,-0.00307355,-0.0323425,0.01030465,-0.03229042,0.01053389,0.04408747,-0.02170958,0.01601961,-0.10813392,0.00757546,0.05602622,0.01040798,-0.06708473,-0.00171937,0.00518297,0.04002625,-0.02194265,0.0324721,0.02828689,0.027203,0.04243806,0.19746463,0.00088002,0.01175852,0.04233521,-0.03353018,0.03883583,-0.00979569,0.04036799,-0.01810074,0.02408512,0.05405337,0.01174678,0.01723362,0.05358963,-0.02034057,-0.01317823,0.03602704,0.07025444,-0.05482101,0.02334236,-0.0281446,-0.01387652,0.12789816,0.04025198,-0.00292761,-0.06272848,0.0122305,0.03222258,-0.10056517,-0.09604685,-0.07018882,-0.00305863,0.0257569,0.04806231,0.01892204,0.00292212,0.01706335,0.00741396,0.0411693,-0.0361836,-0.00077252,0.043472,-0.01690349],"last_embed":{"hash":"3wyckx","tokens":475}}},"last_read":{"hash":"3wyckx","at":1753423619551},"class_name":"SmartSource","last_import":{"mtime":1753362431410,"size":15821,"at":1753423416500,"hash":"3wyckx"},"blocks":{"#---frontmatter---":[1,6],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy":[8,184],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#{1}":[10,15],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>":[16,25],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>#{1}":[18,25],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>":[26,99],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}":[28,99],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>":[100,154],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}":[102,154],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)":[155,184],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{1}":[157,160],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{2}":[161,161],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{3}":[162,162],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{4}":[163,163],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{5}":[164,164],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{6}":[165,165],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{7}":[166,166],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{8}":[167,167],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{9}":[168,168],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{10}":[169,169],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{11}":[170,170],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{12}":[171,171],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{13}":[172,172],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{14}":[173,173],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{15}":[174,174],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{16}":[175,175],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{17}":[176,176],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{18}":[177,178],"#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{19}":[179,184]},"outlinks":[{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/LIE-lottery-strategy.gif","line":32},{"title":"Powerful lottery strategies eliminates combinations with the worst lotto pairs.","target":"https://saliu.com/ScreenImgs/eliminate-pairs.gif","line":38},{"title":"Software generates reports for the best and the worst lotto pairs, pairings.","target":"https://saliu.com/ScreenImgs/best-lotto-pairs.gif","line":60},{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/pair-grid-pick.gif","line":147},{"title":"Missing lotto pairs can reduce drastically total combinations to play.","target":"https://saliu.com/HLINE.gif","line":153},{"title":"<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>","target":"https://saliu.com/content/lottery.html","line":155},{"title":"**Pick-3 Lottery Software**","target":"https://saliu.com/lottery3-software.html","line":161},{"title":"**Pick-4 Lottery Software**","target":"https://saliu.com/lottery4-software.html","line":162},{"title":"**Horse Racing Trifecta Software**","target":"https://saliu.com/horseracing-software.html","line":163},{"title":"**5-Number Lotto Software**","target":"https://saliu.com/lotto5-software.html","line":164},{"title":"**6-Number Lotto Software**","target":"https://saliu.com/lotto6-software.html","line":165},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":166},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":167},{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":169},{"title":"_**Lottery, Lotto Strategy in Reverse: Win, Lose, Hit, Miss**_","target":"https://saliu.com/reverse-strategy.html","line":170},{"title":"_**Software Analysis, Lotto Number Frequency, Lottery Pairs**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":171},{"title":"**<u>Software, Systems, Lottery Skips</u>**","target":"https://saliu.com/skip-strategy.html","line":172},{"title":"_**Lotto, Decades: Systems, Software, Analysis, Strategy**_","target":"https://saliu.com/decades.html","line":173},{"title":"_**Lottery, Lotto Strategy Based On Sums (Sum-Totals), Odd – Even, Low - High Numbers**_","target":"https://saliu.com/strategy.html","line":174},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":175},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":176},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":177},{"title":"The lottery software groups pick lotteries, horse racing, jackpot lotto games.","target":"https://saliu.com/HLINE.gif","line":179},{"title":"Forums","target":"https://forums.saliu.com/","line":181},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":181},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":181},{"title":"Contents","target":"https://saliu.com/content/index.html","line":181},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":181},{"title":"Home","target":"https://saliu.com/index.htm","line":181},{"title":"Search","target":"https://saliu.com/Search.htm","line":181},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":181},{"title":"Get source code to generate lotto combinations from 3 groups of numbers.","target":"https://saliu.com/HLINE.gif","line":183}],"metadata":{"created":"2025-07-24T21:07:10 (UTC +08:00)","tags":["software","lottery","lotto","horse racing","update","program","lottery pairs","reversed lottery strategy","LIE elimination"],"source":"https://saliu.com/software-news.html","author":null}},"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11814892,-0.05558334,-0.00288271,0.03650007,-0.01858149,0.03249037,-0.0400239,-0.00250146,0.04674426,0.01401745,-0.03666995,-0.00767581,0.0597398,-0.00169558,0.00705487,-0.04487471,0.00421628,0.00166672,-0.02203353,0.00131939,0.05776685,-0.01371,-0.04892604,-0.0658664,0.04286284,0.01915027,-0.05938305,-0.05917653,-0.03124666,-0.16463415,-0.00655955,0.03537077,-0.0018441,-0.03859548,-0.03492104,-0.02021763,-0.0197993,0.02850711,-0.04803774,0.06021727,-0.01482071,0.00126502,-0.04246965,-0.03807252,0.0366162,-0.00283487,-0.0188314,0.01729126,0.02851524,-0.00657966,-0.01884169,-0.00110412,-0.03129879,0.0355014,0.0728922,0.03055936,0.03176356,0.07027304,0.02466996,0.05353723,0.03256703,0.0694847,-0.20913716,0.05306135,-0.02519502,0.02050509,-0.03132311,-0.05094296,0.00779661,0.04608859,0.02821745,0.01979247,-0.0263423,0.02158082,0.02425132,-0.01777587,-0.00584981,-0.02464658,-0.02844356,-0.02597043,-0.02783611,-0.0196016,-0.04603151,-0.03124888,-0.00507195,0.03385059,0.05847219,0.07861165,0.07551196,-0.08017324,-0.00436063,-0.00701179,0.12869169,0.0592475,-0.01895785,0.03597054,0.02030009,0.02156679,-0.01404532,0.11905389,-0.04731549,0.00939083,0.00912866,0.00734094,0.07098278,-0.02309348,-0.01499676,-0.02260432,-0.00868575,0.02831249,0.04855496,0.02346443,0.08913318,-0.06366815,0.02449222,0.03474169,0.02042161,0.02837101,0.00096446,0.02773549,-0.06172089,0.0055165,0.00123067,-0.00915456,-0.02792737,0.02750698,0.04621582,0.06569075,-0.02715225,0.03945207,0.01432496,0.01625512,-0.12990655,-0.04751386,-0.01434481,-0.01608209,0.00416772,-0.04983657,0.00035746,0.00987926,-0.05258431,-0.07905821,0.04897238,-0.0992222,-0.03356573,0.07966737,0.03535366,-0.02685915,0.01180573,0.02369951,-0.06391034,0.01850835,-0.01440543,-0.05692377,0.02462797,-0.01631837,0.08902378,0.08093712,-0.0419542,0.00563095,0.03578508,-0.00820164,-0.07766721,0.12404417,-0.0214103,-0.17497407,-0.02093003,0.0934365,-0.01176867,-0.05022098,-0.03527437,0.00635343,-0.02510298,-0.00370765,0.0896953,-0.00865456,-0.0364344,-0.0657066,-0.00275775,-0.0106077,0.00914637,-0.01359244,-0.05040755,0.03944077,-0.05404917,-0.06627033,0.0012286,-0.04417497,0.05128387,0.04434957,-0.03355002,-0.05176376,-0.04180742,-0.00227601,-0.00559518,-0.03214527,-0.02540472,-0.04046194,0.0527534,-0.00326367,-0.06091507,-0.0122023,-0.00636334,0.01243513,-0.01876332,0.01875656,-0.03130858,-0.05151921,0.0288905,0.03586553,-0.00233247,0.02535486,0.00083707,0.04258251,-0.05240233,0.01237402,-0.04353665,0.02585253,-0.01656564,0.02791579,-0.00468408,0.04305429,-0.01564034,-0.2287814,0.01182404,-0.04665929,0.00638607,-0.0029585,-0.02219315,0.04058357,0.02562184,0.03551145,0.11091079,0.07849128,-0.04147506,-0.02589461,-0.0164321,-0.00958421,0.00015031,-0.01777088,-0.04735282,-0.01420548,0.01507975,-0.01371402,0.01121103,-0.01335866,-0.09151709,0.04734377,-0.02028821,0.1127556,0.09295098,0.00142367,0.00893201,0.04860932,0.01145879,-0.01936667,-0.10013051,0.01663573,0.04777547,0.01356638,0.00989983,-0.03549283,-0.00052239,-0.09007851,0.0329707,0.00923836,-0.12029758,0.01740585,0.02100744,0.02083137,-0.01778695,0.01286926,0.05886377,0.0129517,-0.00243967,0.03710726,0.07420377,0.05936076,-0.02786993,-0.06746956,-0.03979857,0.01433875,0.00924473,0.02073859,-0.03409334,0.02771929,-0.03800471,0.01485925,0.02119367,-0.01739529,-0.04758389,-0.00867585,0.01059084,-0.00069784,0.10116208,0.03620797,-0.00374754,-0.05073575,0.01248032,0.06603135,-0.06918962,0.00943368,-0.0151301,0.01071829,-0.07015939,0.10030738,0.08607321,0.06399163,0.02736301,0.05720498,0.02271648,0.03384936,0.00697075,-0.01393969,-0.01535373,-0.03053294,-0.01955211,0.09788512,0.02233834,-0.27333373,0.03372927,0.02867834,0.02593182,-0.00080746,0.03007208,0.03250545,-0.01548196,-0.01423469,-0.07593951,0.01853579,0.05791464,-0.00218206,-0.02182622,-0.00938152,0.03360844,0.03175964,-0.05420357,0.04105489,0.04695973,-0.00648188,0.04798803,0.21866226,0.02270843,0.00756674,0.01999947,-0.0413946,0.04206471,0.01147201,0.05401775,-0.01380687,-0.01732694,0.03327876,0.00619668,-0.03646465,0.06761228,-0.0257809,0.02862245,0.02660754,0.0632136,-0.11611149,0.01361059,-0.02216475,-0.0042607,0.11489874,0.04568444,-0.03089348,-0.06342361,-0.00577451,0.0177129,-0.09940895,-0.10435496,-0.08261382,0.01472127,0.01891688,0.07141657,0.03921169,-0.01047741,0.02740107,0.00556184,0.05565212,-0.03307131,0.02224343,0.03679852,-0.00708371],"last_embed":{"hash":"7b4vl9","tokens":92}}},"text":null,"length":0,"last_read":{"hash":"7b4vl9","at":1753423618302},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#---frontmatter---","lines":[1,6],"size":219,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09108839,-0.01280552,0.00074586,0.06105284,-0.01832968,0.02429036,-0.05670323,-0.01747246,0.07039892,-0.01222457,-0.03708873,-0.00836787,0.06089871,-0.01366029,-0.0370416,-0.0186015,-0.00804018,0.02185171,-0.04038657,-0.00870542,0.00776277,-0.04274275,-0.0473166,-0.04161517,0.03197505,0.03273941,-0.05649108,-0.02352861,-0.05291202,-0.24593611,0.00906679,0.04897533,0.00300555,-0.02634391,-0.06156444,0.00242359,-0.0281568,0.01838808,-0.03108212,0.01791826,0.03589459,-0.01340221,-0.00997515,-0.02106033,0.00508837,-0.01949608,-0.02023822,0.00988337,0.00644891,-0.03874448,-0.03944087,-0.04293543,-0.00416928,0.01286529,0.05263382,0.06324328,0.02496594,0.12715341,0.00823134,0.05381843,0.04662056,0.12536673,-0.17753397,0.02419279,-0.00219296,-0.01492149,-0.04469931,-0.05333703,0.02592099,0.06243545,0.01236107,0.03054782,-0.04638639,-0.00053542,-0.01058594,-0.03918246,0.01217604,-0.01027751,-0.00184035,-0.02142691,-0.04938333,-0.01506768,-0.02827596,-0.00194088,0.02821286,0.02605106,0.05201719,0.02489977,0.03256558,-0.08854652,-0.00233883,0.01515272,0.08254521,0.03144706,-0.00083196,0.0434661,0.04134224,-0.03318156,-0.026623,0.12738881,-0.02242129,-0.01136447,-0.0156385,0.04158935,0.06035057,-0.0606579,-0.02995274,-0.05078654,-0.02804215,0.03408621,0.07127729,-0.01848114,0.06707108,-0.0314659,-0.01785143,0.03382111,-0.02290851,0.01552313,-0.00088928,0.02100898,-0.04872267,0.04860745,0.0548872,0.02175735,-0.01491874,0.00846303,0.07303688,0.05385391,-0.01006955,0.01233134,0.00103681,0.02917638,-0.09686964,-0.04355516,-0.01726962,0.00085658,-0.00819728,-0.00344746,0.0251921,0.07391084,-0.02123644,-0.07595767,0.10029468,-0.07611784,0.00745904,0.09691759,-0.01301029,-0.0263877,-0.01445329,0.04444477,-0.04927631,0.010301,-0.0136266,-0.04597611,0.02414447,-0.03587249,0.07070968,0.10756993,-0.05869633,0.01277126,0.01853605,0.00399633,-0.06557688,0.08280447,-0.02337166,-0.13835774,0.03690815,0.06382151,-0.05107471,-0.03492909,-0.03528919,0.05720508,-0.01883245,-0.02236338,0.03331726,-0.04508169,-0.10193402,-0.02378863,0.02181464,-0.02621657,0.04775199,-0.00111846,-0.06483661,0.03652875,0.00083402,-0.07005918,0.01167409,0.01366777,0.03380658,0.06471588,-0.0624864,-0.02653151,-0.027345,-0.01344888,-0.03105688,-0.01361837,-0.04302943,-0.03521829,0.06240718,-0.01143088,-0.0685633,0.00211994,0.03115413,-0.0111437,0.00847962,0.02566285,-0.04251661,-0.0786083,0.03919885,0.01755195,-0.03875273,0.01127439,-0.01820607,0.07066184,-0.07884064,0.03383122,-0.02702001,0.08177413,-0.04184326,-0.00080369,0.04998447,0.02081811,0.02221416,-0.21328348,-0.00937768,-0.03877147,-0.01695023,0.00402522,-0.02636803,0.03960325,0.02844541,0.02313804,0.10972264,0.06256004,-0.04244967,-0.01849187,-0.03982645,0.01010365,-0.00316989,-0.05128283,-0.04748924,-0.03626054,0.01303833,0.004121,0.02137187,-0.02086365,-0.08624549,0.05348599,-0.04746297,0.12858781,0.07093434,-0.01607161,-0.04368348,-0.0033682,-0.01074823,-0.01715842,-0.07648598,0.05473279,0.07004603,0.04290286,-0.01442504,-0.02806681,0.00121226,-0.09427715,0.01997751,-0.02452628,-0.11538973,0.03007159,0.04514443,0.04443711,-0.00778225,0.02200796,0.06683883,0.06087601,-0.00738092,0.02163115,0.08024865,0.06328081,-0.04586405,-0.09568136,0.00540013,0.01105476,0.01125963,0.01367003,-0.0391595,0.05305345,-0.03840152,0.04368468,0.01832766,0.00497859,-0.02467148,0.02720022,0.02477725,-0.02429164,0.09203663,-0.00822859,0.03868819,-0.03247363,0.02853361,0.0590805,-0.09952082,0.00007589,0.0222468,0.04876617,-0.06281739,0.08363774,0.07779105,0.05384983,-0.01185594,0.05735666,0.05755719,0.01861241,0.00125957,-0.04531484,-0.00805368,-0.01461959,-0.01464716,0.06593265,0.04237427,-0.24888617,-0.00174217,-0.02668725,0.00891424,-0.04712768,0.01553887,0.04494293,-0.02674563,0.00860847,-0.10064247,0.00439268,0.04644598,0.00792521,-0.07790273,-0.00514645,0.00774528,0.05155751,-0.02663115,0.03061195,0.01651759,0.02655435,0.05248942,0.19718683,-0.00014453,0.01540744,0.04527651,-0.03422819,0.04150655,-0.01574695,0.03955295,-0.01377612,0.04152861,0.05104367,0.00262128,0.02036082,0.05678966,-0.00396964,-0.03012122,0.04526622,0.0697251,-0.03726029,0.03292959,-0.04305404,-0.01504128,0.12001903,0.03008161,0.00185195,-0.05440303,0.0091166,0.0324898,-0.09793095,-0.08956861,-0.06693017,0.00008819,0.02595153,0.03252026,0.00923682,0.00146611,0.0035958,0.00772453,0.04066953,-0.04242384,-0.00447979,0.03521273,-0.02935239],"last_embed":{"hash":"1vybdfk","tokens":366}}},"text":null,"length":0,"last_read":{"hash":"1vybdfk","at":1753423618331},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy","lines":[8,184],"size":15598,"outlinks":[{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/LIE-lottery-strategy.gif","line":25},{"title":"Powerful lottery strategies eliminates combinations with the worst lotto pairs.","target":"https://saliu.com/ScreenImgs/eliminate-pairs.gif","line":31},{"title":"Software generates reports for the best and the worst lotto pairs, pairings.","target":"https://saliu.com/ScreenImgs/best-lotto-pairs.gif","line":53},{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/pair-grid-pick.gif","line":140},{"title":"Missing lotto pairs can reduce drastically total combinations to play.","target":"https://saliu.com/HLINE.gif","line":146},{"title":"<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>","target":"https://saliu.com/content/lottery.html","line":148},{"title":"**Pick-3 Lottery Software**","target":"https://saliu.com/lottery3-software.html","line":154},{"title":"**Pick-4 Lottery Software**","target":"https://saliu.com/lottery4-software.html","line":155},{"title":"**Horse Racing Trifecta Software**","target":"https://saliu.com/horseracing-software.html","line":156},{"title":"**5-Number Lotto Software**","target":"https://saliu.com/lotto5-software.html","line":157},{"title":"**6-Number Lotto Software**","target":"https://saliu.com/lotto6-software.html","line":158},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":159},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":160},{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":162},{"title":"_**Lottery, Lotto Strategy in Reverse: Win, Lose, Hit, Miss**_","target":"https://saliu.com/reverse-strategy.html","line":163},{"title":"_**Software Analysis, Lotto Number Frequency, Lottery Pairs**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":164},{"title":"**<u>Software, Systems, Lottery Skips</u>**","target":"https://saliu.com/skip-strategy.html","line":165},{"title":"_**Lotto, Decades: Systems, Software, Analysis, Strategy**_","target":"https://saliu.com/decades.html","line":166},{"title":"_**Lottery, Lotto Strategy Based On Sums (Sum-Totals), Odd – Even, Low - High Numbers**_","target":"https://saliu.com/strategy.html","line":167},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":168},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":169},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":170},{"title":"The lottery software groups pick lotteries, horse racing, jackpot lotto games.","target":"https://saliu.com/HLINE.gif","line":172},{"title":"Forums","target":"https://forums.saliu.com/","line":174},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":174},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":174},{"title":"Contents","target":"https://saliu.com/content/index.html","line":174},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":174},{"title":"Home","target":"https://saliu.com/index.htm","line":174},{"title":"Search","target":"https://saliu.com/Search.htm","line":174},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":174},{"title":"Get source code to generate lotto combinations from 3 groups of numbers.","target":"https://saliu.com/HLINE.gif","line":176}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09372473,-0.02376637,0.01949721,0.05708798,-0.02353128,0.01719085,-0.04281626,-0.01326297,0.06380638,-0.01656227,-0.04151031,0.02141185,0.08075275,-0.00225046,-0.01127212,-0.03203802,0.00725807,0.02723961,-0.03195211,-0.01238576,0.02666665,-0.02444088,-0.04934874,-0.0503609,0.04209902,0.02639966,-0.06326694,-0.05363507,-0.03863087,-0.17837927,-0.01077101,0.05620701,0.01048517,-0.04431636,-0.04409922,0.00747099,-0.02211479,0.01713879,-0.05925899,0.04854687,0.02582891,-0.01646304,-0.01760729,-0.02631638,0.01473344,-0.01405874,-0.01079466,0.00105289,0.01743061,-0.04785072,-0.02304291,-0.01632912,-0.02422931,0.02874235,0.06539106,0.0463511,0.02650384,0.11948793,0.01642669,0.06023909,0.0399707,0.10290203,-0.19410315,0.05338506,-0.00624917,-0.00650511,-0.03887897,-0.05684898,0.0278747,0.05849997,0.02184298,0.02569696,-0.04342595,0.01128354,0.0116428,-0.03846117,0.00857444,-0.01124062,-0.00494182,-0.03076632,-0.03268114,-0.01070451,-0.02012485,-0.0161249,-0.00733042,0.03037578,0.06587008,0.01354215,0.05968709,-0.1137412,-0.02008461,-0.00738359,0.09751479,0.04431804,-0.00573579,0.02575602,0.02715118,-0.01081999,0.0218833,0.14443125,-0.04627092,-0.011055,0.00114787,0.01630295,0.06019209,-0.05241662,-0.01025747,-0.03234579,-0.00127351,0.05043193,0.05548135,0.00888206,0.0571826,-0.03434686,0.00234442,0.03316229,-0.02206084,0.03962139,0.00102746,0.02918525,-0.0542822,0.00744026,0.03588034,0.00759981,-0.03147576,0.00616708,0.06503658,0.05172097,-0.01942562,0.0397197,0.0058608,0.04092405,-0.11345021,-0.05888533,-0.00736455,0.00185197,-0.00074138,-0.03642035,-0.01762009,0.06989325,-0.04597529,-0.07596503,0.09111059,-0.10712399,0.0064912,0.11190383,0.01949468,-0.02506818,-0.00703249,0.03639894,-0.04961366,0.02176875,-0.00227717,-0.04182486,0.02415926,-0.03269368,0.05957703,0.07877844,-0.05401178,0.01383775,0.01591755,0.02384885,-0.07352497,0.11419911,-0.04337212,-0.12764394,0.00214884,0.075181,-0.0130913,-0.04389693,-0.03727721,0.03883479,-0.03273513,-0.00768309,0.06456757,-0.0178076,-0.05474119,-0.04811384,-0.00571002,-0.02760918,0.01559477,-0.02858826,-0.05819518,0.05971482,-0.01998367,-0.06057014,0.02340635,-0.01184858,0.05587006,0.07247965,-0.031226,-0.02523913,-0.03095016,-0.0058639,-0.022802,-0.01449682,-0.04430864,-0.04457242,0.03738181,-0.00298415,-0.08851691,-0.01920253,0.00048479,-0.00102269,0.0148736,0.01954004,-0.04301897,-0.05190904,0.03926906,0.02684822,-0.04298123,0.0181015,0.00107741,0.05666196,-0.06663054,0.01394871,-0.0359102,0.06290283,-0.04332571,0.0199376,0.03292694,0.02899422,0.01788132,-0.22037917,0.00460741,-0.02588283,-0.0061661,0.00412957,-0.03020968,0.03100826,0.04499774,0.02825179,0.11480161,0.05863829,-0.06679142,-0.01914322,-0.00117777,0.00187296,-0.03121753,-0.03237331,-0.06339263,-0.03741857,0.0028689,-0.00283747,0.00988165,-0.01868109,-0.09140971,0.04289781,-0.02791323,0.10452882,0.05820158,-0.01254954,0.01024367,0.00333692,0.00073153,-0.02388502,-0.07922159,0.0101248,0.07191979,0.01980462,-0.02963402,-0.03132134,0.00983338,-0.08746117,0.03851047,-0.01906675,-0.11288574,0.0246197,0.03944431,0.02715321,0.00293022,0.02906467,0.07163729,0.02663581,0.01713862,0.0287627,0.06733875,0.04782825,-0.05161022,-0.07204677,-0.01055447,0.00014991,0.00665918,0.01068746,-0.04462624,0.02895303,-0.03399565,0.06614326,0.02558689,-0.01986193,-0.05741609,-0.00098621,0.03526826,-0.04077651,0.08627042,0.00174705,0.00524845,-0.06335698,0.03435655,0.05943009,-0.08538601,-0.02245984,-0.00418721,0.03217318,-0.07363545,0.08866554,0.09841027,0.05823372,0.00906858,0.06867282,0.01634874,0.00626219,0.00513467,-0.04121459,-0.02510372,-0.01120333,-0.00284472,0.05947634,0.03806349,-0.26488519,0.02377947,-0.00889889,0.01545992,-0.0500031,0.03857748,0.05767856,-0.00316416,-0.00159989,-0.07643954,0.01047595,0.03030634,-0.00693448,-0.04987741,-0.02400534,0.02586851,0.07172265,-0.04786876,0.03811376,0.02723785,0.01363203,0.04426447,0.21913478,0.00710089,0.00617523,0.03855262,-0.04911111,0.02799477,-0.00829788,0.05185921,0.01820703,-0.00714106,0.05878553,0.00541192,-0.01525633,0.0619751,-0.02345496,0.01558803,0.05306779,0.0820822,-0.0757101,0.01380152,-0.04346103,-0.00877821,0.14776833,0.02636299,-0.03449498,-0.06689695,-0.00420916,0.01270874,-0.09902251,-0.08720195,-0.08357631,0.01311658,0.03631532,0.03445036,0.00653496,-0.00151784,0.02559395,0.01812863,0.05726063,-0.03273903,-0.01193551,0.03794822,-0.01806118],"last_embed":{"hash":"yqyoyi","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"yqyoyi","at":1753423618448},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#{1}","lines":[10,15],"size":213,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0755987,-0.00950185,-0.00426302,0.05237486,-0.00812562,0.02199423,-0.04317418,-0.01887959,0.07422984,-0.00969307,-0.03721335,-0.00904122,0.05520644,-0.00496902,-0.04440612,-0.00953247,-0.0084579,0.01857126,-0.03676209,-0.00591911,0.0095121,-0.04285465,-0.04884735,-0.04341037,0.02549353,0.03797438,-0.05520089,-0.01458204,-0.06061278,-0.24648675,0.00650113,0.03945884,0.01015273,-0.02711437,-0.05582485,-0.00990185,-0.02085586,0.02593947,-0.02355732,0.01251057,0.02867727,-0.01881477,-0.00330401,-0.02702997,0.00090094,-0.02861075,-0.00870481,0.01156943,0.01248771,-0.03193578,-0.04538217,-0.04989959,-0.00875085,0.00618241,0.05210979,0.0703522,0.02389756,0.11371374,0.00149838,0.05218384,0.04072742,0.1192578,-0.18079451,0.01274255,-0.00970677,-0.01102069,-0.05245885,-0.04301584,0.01046007,0.05095994,0.01364227,0.02529038,-0.04453897,0.01092243,-0.01810039,-0.04085543,0.00863167,-0.0170168,-0.01148125,-0.01446059,-0.06499056,-0.01597832,-0.0314674,0.00332623,0.02855569,0.02979561,0.04950755,0.03699417,0.04228498,-0.08396944,0.01969802,0.01631386,0.06617539,0.0285337,0.00882282,0.04225221,0.04790111,-0.03720817,-0.04260798,0.12922776,-0.01280303,-0.01913609,-0.02212618,0.04047355,0.0628691,-0.05685696,-0.03564928,-0.03948658,-0.03080137,0.0314769,0.07636639,-0.02249039,0.06305279,-0.0372602,-0.0209872,0.02855308,-0.02305671,0.0095205,0.00023474,0.02092128,-0.04783689,0.03574315,0.05971706,0.03087519,-0.00730877,0.00365555,0.07884505,0.0573336,-0.0005838,0.00124181,-0.00826616,0.02320605,-0.106971,-0.03200801,-0.01710423,-0.00567801,-0.00813161,-0.00347008,0.03358911,0.07075746,-0.02357664,-0.07010081,0.08657055,-0.07291065,-0.00275015,0.09634333,-0.02965917,-0.02150648,-0.01882704,0.03330583,-0.05084866,-0.00140212,-0.0166146,-0.05373441,0.02157724,-0.03385556,0.07714992,0.11184134,-0.05881571,0.00227632,0.00600801,-0.0073634,-0.05761585,0.07909511,-0.02150505,-0.13268735,0.04037407,0.06001869,-0.04989774,-0.03785726,-0.02165162,0.05064819,-0.00517187,-0.01789287,0.02796489,-0.05421507,-0.10053147,-0.00893971,0.0221204,-0.02780152,0.0445013,-0.00005512,-0.06664269,0.03456356,0.0127165,-0.07582613,0.01485566,0.0221134,0.03291878,0.06516221,-0.0640647,-0.02694053,-0.02659927,-0.01007966,-0.03472676,-0.01639332,-0.04148975,-0.01761873,0.06497193,-0.0203934,-0.04884062,0.00687425,0.03131699,-0.00321053,0.01257397,0.02435037,-0.05521714,-0.07763954,0.04356755,0.0034697,-0.03818791,0.00646796,-0.01328368,0.07389296,-0.0880575,0.0333427,-0.03638882,0.0922142,-0.03271899,-0.00582565,0.04260504,0.02435541,0.01430028,-0.22298524,-0.01685776,-0.04127963,0.00026798,-0.00373545,-0.01583273,0.04514823,0.02924397,0.0226201,0.12027941,0.06936179,-0.04484169,-0.01820275,-0.04788198,0.00918231,-0.00001648,-0.05934105,-0.04593733,-0.03646276,0.02499492,0.015146,0.01572972,-0.01737125,-0.08403404,0.0587322,-0.05669915,0.14540204,0.06465222,-0.00952428,-0.05102598,-0.00538363,-0.01278752,-0.01278921,-0.07208604,0.05626807,0.06566649,0.0314045,0.00158082,-0.03259602,-0.00818433,-0.09436227,0.00806157,-0.01690479,-0.11612511,0.03170461,0.0383992,0.05233376,-0.00855272,0.00720451,0.05542804,0.06376079,-0.0143617,0.02268514,0.07849365,0.06759558,-0.04694633,-0.09607664,0.00649357,0.01363463,0.00780567,0.019798,-0.04297955,0.06340272,-0.03826771,0.03383532,0.00918439,0.0087231,-0.013747,0.03449829,0.02268068,-0.01839126,0.09290059,-0.02724559,0.04911071,-0.01826443,0.03233179,0.06104944,-0.09748081,0.01591238,0.0247267,0.05456707,-0.05541368,0.08557968,0.07732121,0.05424517,-0.01348596,0.05972137,0.06326959,0.0284311,0.00226925,-0.04257514,-0.00852103,-0.0181411,-0.0088111,0.05882339,0.04253913,-0.24874862,-0.0058887,-0.0361701,0.01192912,-0.04237464,-0.00328606,0.03333892,-0.03175737,0.00997928,-0.08987886,0.01849573,0.04096505,0.00887011,-0.09244431,-0.00789095,0.01274613,0.04298347,-0.01447025,0.02989272,0.02513667,0.03348488,0.04324452,0.19560647,0.00172534,0.02482838,0.05017808,-0.02811413,0.03867725,-0.01910018,0.04282007,-0.01226813,0.04768702,0.05621753,0.00578932,0.01937842,0.06529568,0.00546466,-0.02790188,0.04179661,0.06402519,-0.02414272,0.04526453,-0.04567029,-0.01680913,0.10715639,0.03094145,0.00532704,-0.05365396,0.01157871,0.04198561,-0.10479443,-0.0887928,-0.06412745,0.00159283,0.02099719,0.03611187,0.01857387,0.00323653,-0.00499112,-0.0007173,0.02850986,-0.04413033,0.00419629,0.03504654,-0.028294],"last_embed":{"hash":"xulfx8","tokens":389}}},"text":null,"length":0,"last_read":{"hash":"xulfx8","at":1753423618479},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>","lines":[16,25],"size":1493,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07656635,-0.00850871,-0.00341928,0.05094541,-0.00682845,0.02111724,-0.04584489,-0.0177857,0.07518145,-0.01105708,-0.03665237,-0.00885373,0.05541844,-0.00688868,-0.04379089,-0.00792666,-0.00838363,0.01773912,-0.03952769,-0.00695692,0.00804466,-0.04132371,-0.04855455,-0.04291033,0.0252484,0.03920363,-0.05347582,-0.01388288,-0.06001872,-0.24601248,0.00800942,0.0418589,0.01021059,-0.02549263,-0.05430445,-0.0085747,-0.02033091,0.02499602,-0.02259756,0.01205207,0.03201815,-0.01966998,-0.00024245,-0.02654013,0.00186657,-0.02971811,-0.00994107,0.01003184,0.01221458,-0.03062839,-0.04299427,-0.04931868,-0.00690638,0.00773311,0.05114957,0.06910325,0.02565316,0.11495057,-0.00020904,0.05409165,0.04030396,0.12177907,-0.18030643,0.01181045,-0.00921597,-0.01136157,-0.05219872,-0.04408391,0.01390984,0.05024807,0.01207655,0.02621341,-0.04414947,0.01150365,-0.01961823,-0.04153198,0.00973577,-0.0183777,-0.00874555,-0.01373486,-0.06568468,-0.01658517,-0.03358758,0.00189465,0.0286395,0.02669385,0.05007615,0.03460435,0.04090516,-0.08297484,0.01849723,0.01470168,0.06352567,0.02696686,0.00671618,0.04238215,0.04814941,-0.0404412,-0.04280438,0.12938045,-0.01454061,-0.01979995,-0.02236247,0.03968178,0.06153155,-0.05584754,-0.03718477,-0.04123894,-0.03119975,0.03192931,0.07700989,-0.02409247,0.06063895,-0.03856161,-0.02007274,0.0285456,-0.02588852,0.00818663,0.00142249,0.01754192,-0.04829293,0.03651343,0.06301177,0.03161889,-0.00766447,0.00386696,0.07865956,0.05719071,-0.00036523,-0.00069479,-0.00705641,0.02290476,-0.10400128,-0.03265132,-0.01776374,-0.00302688,-0.00833084,-0.00143894,0.03238629,0.07173879,-0.02232232,-0.06837552,0.08857496,-0.07238308,-0.00068557,0.09719158,-0.03020179,-0.01983661,-0.0203896,0.03281213,-0.05117667,-0.00207727,-0.01630041,-0.05221234,0.0217463,-0.03166034,0.07879881,0.11219256,-0.05812065,0.00450713,0.00557386,-0.00962306,-0.05778688,0.08000356,-0.02263328,-0.12933081,0.04305168,0.05932355,-0.04961481,-0.03738973,-0.02229489,0.05498635,-0.00334632,-0.01673618,0.02948406,-0.05434661,-0.10093737,-0.00788563,0.02256931,-0.02896535,0.04636716,0.00153285,-0.06812873,0.03216715,0.01404818,-0.07539021,0.01452218,0.02507192,0.03344758,0.06513467,-0.06947,-0.0256751,-0.02832958,-0.01098617,-0.03501616,-0.01297974,-0.04206397,-0.01727872,0.0665419,-0.02330499,-0.04712761,0.00791753,0.03296838,-0.0049912,0.01371747,0.02595968,-0.05439684,-0.07839322,0.04379313,-0.00087819,-0.03879008,0.00636669,-0.0119476,0.07509944,-0.08637342,0.03272793,-0.0348317,0.09357486,-0.03270408,-0.00857209,0.04300563,0.02302076,0.01632353,-0.22018529,-0.01651722,-0.03934291,-0.00297924,-0.00558516,-0.01723287,0.04365457,0.02875012,0.02144569,0.1206065,0.06969061,-0.0446204,-0.01754421,-0.04794846,0.01035088,0.00232392,-0.05824349,-0.04465973,-0.03595794,0.0241957,0.01426816,0.01285543,-0.01722414,-0.08269447,0.05793872,-0.05715931,0.14498833,0.06566478,-0.01074702,-0.05330685,-0.00732095,-0.01251879,-0.01157051,-0.07148535,0.05808371,0.06636843,0.03126248,-0.00163044,-0.02943433,-0.01000357,-0.09532902,0.00592082,-0.01922839,-0.11618409,0.03451705,0.03709696,0.05223296,-0.00870098,0.00922724,0.05562135,0.06426989,-0.01525784,0.02101492,0.07982529,0.06741686,-0.04754437,-0.09788576,0.00811003,0.01414862,0.00886673,0.01854064,-0.04336866,0.06314274,-0.04016345,0.03209188,0.01005998,0.010002,-0.01318375,0.03580038,0.02357065,-0.01867704,0.09348807,-0.0280417,0.04982192,-0.01999803,0.02980662,0.0604144,-0.09736928,0.0176445,0.0259156,0.05306869,-0.05406745,0.08440792,0.07777159,0.05427817,-0.01567823,0.05908084,0.06341876,0.02834257,0.00278598,-0.04167879,-0.00681199,-0.01771072,-0.00928027,0.05788682,0.04010876,-0.24845515,-0.00518256,-0.03752426,0.01130811,-0.04391591,-0.0039831,0.03331852,-0.03440774,0.00671314,-0.09281411,0.01720624,0.03902641,0.01043484,-0.09301589,-0.00678125,0.01177765,0.04456434,-0.01366411,0.02868327,0.02484353,0.03277728,0.04262405,0.19584733,0.00071431,0.02359325,0.05100076,-0.02810826,0.03837302,-0.0191726,0.04111101,-0.01230756,0.05065725,0.05744617,0.00529422,0.02044397,0.06759179,0.00607305,-0.03204184,0.04078539,0.06472609,-0.02203153,0.04509979,-0.04758918,-0.0181043,0.10574215,0.03012537,0.00569537,-0.05285209,0.01071968,0.04272028,-0.10268304,-0.08581241,-0.06338475,0.00036935,0.02127671,0.03235536,0.01809951,0.00387225,-0.00763726,-0.00063632,0.02845215,-0.04201786,0.00357009,0.03524673,-0.02834917],"last_embed":{"hash":"hl75az","tokens":388}}},"text":null,"length":0,"last_read":{"hash":"hl75az","at":1753423618608},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>#{1}","lines":[18,25],"size":1416,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0910999,-0.0336713,-0.0172518,0.00219532,-0.03335124,0.05474216,0.00655842,0.00747421,0.04730121,-0.02861061,-0.06067444,0.00441924,0.06939603,0.02333101,-0.02055305,-0.04333968,-0.03900368,0.06325791,-0.03948582,-0.00407227,0.06043038,-0.07126052,-0.0576221,-0.09097435,0.04668348,0.03535945,-0.06352893,-0.07072084,-0.02803231,-0.24381958,0.01245155,0.03861379,0.01655185,-0.04361384,-0.07178542,-0.01144204,-0.03807448,0.00515217,-0.06061393,-0.00055423,0.00383804,-0.00816183,0.02751413,-0.04253468,0.02865301,-0.00929644,-0.01801819,0.01797269,0.00986625,0.0025007,-0.04384011,-0.01847284,-0.03368078,0.04491579,0.07913302,0.09568425,0.04773516,0.06975412,-0.01862663,0.07355846,0.01523803,0.05466067,-0.17805944,0.00831762,-0.02005364,0.02402551,-0.04075237,-0.05270609,-0.00324238,0.07762209,0.01139474,0.01854009,-0.04109139,0.00726809,0.02059576,-0.03227044,-0.04413684,0.00765787,-0.00224505,0.01469924,-0.04279494,0.00202092,-0.01164373,0.03447267,0.02063311,0.0450557,0.08265634,0.03650992,0.07299691,-0.09164169,0.02695122,0.00672895,0.0692991,0.04234696,0.01650476,0.042565,0.00904971,-0.00557355,-0.05795417,0.11690274,-0.0250204,0.00087775,-0.01345167,0.01314109,0.04613814,-0.06526213,-0.01406431,-0.04109059,0.00284597,0.01389622,0.02937387,-0.02854647,0.03560311,-0.05040129,-0.01283089,0.05673468,-0.00023599,0.02555442,-0.00555457,0.02481561,-0.04541167,0.04034289,0.01937498,0.01276023,0.00223326,-0.00224166,0.03075227,0.05789367,-0.009049,0.05267237,0.03269837,-0.02449904,-0.11310643,-0.03407406,-0.03862314,-0.00545972,-0.00472892,-0.00236439,0.04917284,0.05438753,0.00112392,-0.04455246,0.06400712,-0.05937156,0.00572538,0.10870935,-0.02754379,-0.01806125,-0.00196467,0.03408529,-0.00661862,-0.00601611,-0.02802114,-0.08739056,0.03426344,-0.03238842,0.06152759,0.05512704,-0.04382909,0.03343839,-0.0257162,0.00214677,-0.04612647,0.08647037,0.03235849,-0.13144092,-0.00691026,0.09960312,-0.05722089,-0.03131225,-0.01780972,0.00613989,-0.02353826,-0.03573579,0.03466547,-0.02655029,-0.08553768,-0.07576203,0.0094138,-0.00771155,0.01196483,-0.01292478,-0.05759855,0.01642936,-0.03440323,-0.04479831,-0.01975106,-0.03517997,0.01692482,0.04110614,-0.05316932,-0.0426129,-0.04034165,-0.01074568,-0.0253974,-0.05201733,-0.01989456,-0.0533109,0.00441946,0.02039678,-0.00880612,-0.00586191,0.00729219,0.00880989,-0.0085821,0.0331013,-0.04216712,-0.05858033,0.06735772,0.04940332,-0.01688446,0.00150144,0.00859793,0.03772736,-0.06032468,0.00452522,-0.01960731,0.03731026,-0.03371213,0.03143319,0.02449526,0.06657636,-0.00171477,-0.22966865,-0.01616782,-0.0670765,0.02817267,0.00946954,-0.03548297,0.0505742,0.01593802,0.00770273,0.09591698,0.09126106,-0.03828156,-0.01440573,0.01830916,-0.03359701,0.02692165,-0.03426158,-0.0364263,-0.03149391,0.00850062,-0.02216176,0.05014206,0.00720168,-0.07928287,0.09407384,-0.03156829,0.15226287,0.06869088,0.02991938,0.03310612,0.04855083,0.02074992,-0.02071029,-0.06683907,0.03561027,0.06805968,0.05114881,-0.07067033,-0.0362303,0.02842317,-0.07758147,0.02008049,0.05263203,-0.10015269,-0.01767512,0.00102702,0.0169141,-0.01746795,-0.0048344,0.07698877,0.01539913,-0.05234526,0.02752684,0.04304535,0.03377433,-0.01711562,-0.07157875,-0.01812841,0.01540663,-0.01188935,0.015057,-0.01911556,0.03517908,-0.03617669,0.01584362,0.01100655,-0.01459713,0.01904998,0.04702352,0.02701916,0.00995382,0.05855327,0.00331723,0.0538437,-0.02104691,0.01810875,0.05542387,-0.09835722,0.01219716,-0.0564325,0.01997758,-0.06889336,0.07621509,0.02614533,0.06599153,-0.02611811,0.09849028,0.07397888,0.03377502,-0.01087307,-0.03543209,-0.01186207,-0.01271549,-0.01957004,0.08187675,0.0509468,-0.27932295,-0.02331288,-0.03693912,0.03865118,-0.02440816,-0.01850403,0.0513298,-0.03835515,-0.0024644,-0.08291481,0.03618862,0.04342408,0.0201509,-0.03345599,0.01615215,0.04545788,0.02000421,-0.06646289,0.05044111,0.01247679,0.04458508,0.03586101,0.21259914,0.00153926,0.0358921,0.05015797,-0.0355092,0.01694835,-0.01427104,0.01926669,0.00659785,-0.02877023,0.04531644,-0.00119926,0.01250125,0.06336682,0.0259422,-0.00818017,0.04485133,0.05698574,-0.07636102,0.00940597,-0.01128869,-0.03027577,0.1355869,0.00239593,0.00157264,-0.0507907,0.03161731,0.04182215,-0.11012415,-0.02504673,-0.04817626,-0.00749074,0.00375262,0.03910998,0.02976804,-0.0110472,0.01677613,0.02121935,0.04599686,-0.02229307,0.02982348,0.03459297,-0.0048089],"last_embed":{"hash":"g5hfji","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"g5hfji","at":1753423618718},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>","lines":[26,99],"size":6023,"outlinks":[{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/LIE-lottery-strategy.gif","line":7},{"title":"Powerful lottery strategies eliminates combinations with the worst lotto pairs.","target":"https://saliu.com/ScreenImgs/eliminate-pairs.gif","line":13},{"title":"Software generates reports for the best and the worst lotto pairs, pairings.","target":"https://saliu.com/ScreenImgs/best-lotto-pairs.gif","line":35}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09354484,-0.03420645,-0.01806793,0.0021129,-0.0332197,0.0560456,0.00745497,0.00726253,0.04946474,-0.02960681,-0.06120714,0.00503855,0.06989355,0.02189788,-0.02102435,-0.04222113,-0.04152287,0.0643912,-0.03970729,-0.00410237,0.05734422,-0.07092811,-0.05755226,-0.08989849,0.04714213,0.03400134,-0.06204537,-0.0708669,-0.02664628,-0.24435309,0.01419505,0.0378085,0.01638979,-0.04365548,-0.07130602,-0.01277429,-0.03874245,0.00548089,-0.05633442,-0.00196077,0.00545618,-0.00724324,0.02707955,-0.04350121,0.02842089,-0.01122416,-0.01798808,0.01831385,0.01161085,0.00205481,-0.04179927,-0.01862636,-0.03542229,0.04451787,0.07959383,0.09772065,0.05084209,0.07110304,-0.01913443,0.07224984,0.01409459,0.0567069,-0.17760751,0.01056471,-0.02254481,0.02343648,-0.03882914,-0.05277079,-0.00289535,0.07991463,0.01148059,0.01752,-0.04183251,0.00780914,0.02169776,-0.03263651,-0.04494094,0.00748697,-0.00115775,0.01872791,-0.04247423,0.00345202,-0.01139528,0.03539614,0.02066382,0.0454551,0.08320011,0.03469026,0.07042377,-0.09183266,0.02790338,0.00599841,0.06479709,0.0418811,0.01411445,0.03945198,0.00788758,-0.00637035,-0.05811756,0.11651924,-0.02522607,-0.00026138,-0.01702092,0.01351485,0.04769302,-0.0641953,-0.01450353,-0.04283894,0.0003734,0.01492205,0.03102851,-0.02809318,0.03353349,-0.04986286,-0.01317452,0.06058417,-0.00329413,0.02377855,-0.00470983,0.02330019,-0.04489012,0.03923651,0.02056682,0.01292224,-0.00110022,-0.00334779,0.03181757,0.05850296,-0.01187536,0.04978426,0.0334178,-0.02852946,-0.11353915,-0.03546022,-0.03912586,-0.00370316,-0.00362792,-0.00391195,0.04934015,0.05454475,0.00331706,-0.04200849,0.06377518,-0.06167106,0.00585441,0.10781134,-0.02741828,-0.01944036,-0.0027768,0.03374615,-0.00479421,-0.00875846,-0.02941484,-0.08636421,0.03174489,-0.03149767,0.06374421,0.05627398,-0.04280456,0.03153554,-0.03023436,-0.0001572,-0.0469637,0.08586945,0.03085393,-0.13060619,-0.00628747,0.0999632,-0.05752765,-0.03304682,-0.01778811,0.00654487,-0.02431375,-0.03585722,0.03649808,-0.02616953,-0.08664735,-0.07451211,0.00661806,-0.00808703,0.01136488,-0.01273535,-0.05539274,0.01824814,-0.03546495,-0.045592,-0.01826962,-0.03450421,0.01709673,0.0389954,-0.05435371,-0.03929008,-0.04109695,-0.01038302,-0.02699094,-0.0496511,-0.02248108,-0.05493685,0.00598745,0.01928051,-0.00603904,-0.0074545,0.00675439,0.00732779,-0.0086775,0.03593786,-0.04206418,-0.05857591,0.06990548,0.04842112,-0.01771729,0.00309059,0.00730457,0.03623322,-0.05808333,0.00421955,-0.01916311,0.03704995,-0.03147382,0.03106396,0.02512164,0.0686866,-0.00045917,-0.22754569,-0.01542948,-0.06911044,0.02816059,0.00725727,-0.03592498,0.051746,0.01547135,0.00637062,0.09381565,0.09178054,-0.03761885,-0.01111363,0.02083699,-0.03241897,0.02529165,-0.03467197,-0.03584325,-0.03035327,0.00849203,-0.02265959,0.05219202,0.00514118,-0.07818754,0.09518917,-0.03246597,0.15255888,0.06705634,0.02697002,0.0334705,0.0505214,0.02171377,-0.01820164,-0.0656107,0.03533109,0.06976217,0.05250197,-0.07245348,-0.0350191,0.02771754,-0.07887719,0.01952248,0.05178603,-0.09982351,-0.01690941,-0.00053914,0.01723916,-0.01582697,-0.0059384,0.07773324,0.01798544,-0.0496532,0.02617321,0.04335772,0.0338745,-0.01840437,-0.0726187,-0.01390988,0.01790052,-0.00912821,0.01519396,-0.01853617,0.03388923,-0.03541529,0.01539642,0.01043971,-0.01581327,0.01920505,0.04877214,0.0266513,0.01219831,0.05765129,0.00260144,0.05243468,-0.02142062,0.01747817,0.05492571,-0.0962984,0.01154644,-0.05738582,0.0192471,-0.07043143,0.07731681,0.02650502,0.06586593,-0.02606085,0.0973783,0.07389771,0.03437377,-0.00968629,-0.03513696,-0.01106204,-0.01464437,-0.01818409,0.08026285,0.05101397,-0.27914214,-0.02499658,-0.04022521,0.03846831,-0.0230526,-0.01904546,0.05312228,-0.03824679,-0.00490595,-0.08325233,0.03452196,0.04423356,0.02347876,-0.03225745,0.01394636,0.04435087,0.02109573,-0.0657396,0.05060038,0.01124951,0.04729772,0.03543025,0.21399759,0.00123687,0.03630171,0.05099738,-0.03571761,0.01754823,-0.01503979,0.01883784,0.00533452,-0.02752901,0.04719615,-0.00218445,0.01182329,0.06238527,0.02773764,-0.00831897,0.04542831,0.05483,-0.07612827,0.00934842,-0.0110506,-0.0322749,0.13516775,0.00135203,0.00326512,-0.05072023,0.03084491,0.04299423,-0.10789558,-0.02400725,-0.0510411,-0.00838973,0.00560234,0.03814413,0.0291603,-0.01230705,0.01815015,0.0212709,0.04469667,-0.0170131,0.03113423,0.03352031,-0.00566988],"last_embed":{"hash":"1dosz0c","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"1dosz0c","at":1753423618880},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}","lines":[28,99],"size":5940,"outlinks":[{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/LIE-lottery-strategy.gif","line":5},{"title":"Powerful lottery strategies eliminates combinations with the worst lotto pairs.","target":"https://saliu.com/ScreenImgs/eliminate-pairs.gif","line":11},{"title":"Software generates reports for the best and the worst lotto pairs, pairings.","target":"https://saliu.com/ScreenImgs/best-lotto-pairs.gif","line":33}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09406017,-0.06437092,-0.03148272,0.02103995,-0.05013825,0.05470965,-0.0282873,-0.00854954,0.07603785,-0.0248522,-0.00966648,-0.01487177,0.09291188,0.01462793,0.00399815,-0.01918085,-0.04073847,0.01518151,-0.05524751,0.00860467,0.07217783,-0.07588789,-0.0602908,-0.0803258,0.03726687,0.01700444,-0.04190873,-0.06753688,-0.04639702,-0.25074044,0.03008477,0.03175014,0.01689779,-0.05481182,-0.08100251,-0.03455273,-0.03393433,0.0165782,-0.0744114,0.02398772,-0.00783909,0.0111135,-0.01237107,-0.03641295,-0.01847807,-0.03249802,-0.04627223,-0.00052596,0.02443689,0.00265883,-0.07359786,-0.04609104,-0.01947661,0.01571062,0.05824902,0.07649138,0.0290749,0.05897319,-0.01083199,0.05166249,0.02042253,0.07810515,-0.17488153,0.02790035,0.02477584,-0.00060593,-0.03281901,-0.01654173,0.01447225,0.09580291,0.02508802,0.05132583,-0.04027604,0.01900325,-0.00026329,-0.02533247,-0.04639822,-0.02057136,-0.0253718,0.0102427,-0.09463271,-0.00441862,-0.02128017,0.00718473,-0.00146151,0.04583836,0.04311616,0.00331883,-0.00367194,-0.04693058,-0.0196356,0.04593696,-0.00428682,0.02036013,0.01890052,0.03810775,0.03419892,-0.01799432,-0.02300187,0.11746332,-0.02055437,0.00838318,-0.00923451,0.0171255,0.00393426,-0.05591139,-0.0379907,-0.05769693,-0.03345803,0.03315409,0.06430855,-0.01647312,0.00692106,-0.04397345,-0.00736074,0.0499244,0.02097178,-0.00289749,0.03216996,0.00850844,-0.04582918,0.03929596,0.0338286,0.02624505,0.00918645,0.02096805,0.04619724,0.08270726,-0.00076071,0.04580028,0.02411804,0.00329484,-0.13036148,-0.03201738,-0.05819295,-0.02834552,0.01623352,0.01548364,-0.02116899,0.04107302,-0.03396823,-0.02876194,0.10282964,-0.07772123,0.00958066,0.06590852,-0.01182707,-0.00564304,0.01988299,0.03697773,0.01196581,0.01203816,-0.01402971,-0.06856232,0.01082869,-0.02916343,0.06958015,0.04350557,-0.02709554,0.03616977,-0.03070644,-0.01176316,-0.05619745,0.11534848,-0.01349088,-0.12673692,0.01334854,0.08359968,-0.0536951,-0.04467179,-0.00209526,0.05263777,-0.04363886,-0.00278435,0.03650675,-0.01004183,-0.13393143,-0.03836731,0.04007368,0.0207035,0.01322601,0.04225993,-0.03226,0.01382036,-0.0259849,-0.05361432,0.00152505,-0.02184032,-0.01452021,0.03590943,-0.01466676,0.01983554,-0.01996287,0.00780857,-0.01645342,-0.03941932,-0.01819082,-0.0497419,0.04827746,0.05038345,-0.0009875,-0.01813838,0.04333726,0.01450953,-0.00338667,0.03570223,-0.03164527,-0.0284471,0.05958353,0.03854281,-0.0207105,-0.01141067,-0.0109797,0.06470781,-0.05179804,0.03574051,0.00350837,0.02981337,0.01026001,0.02739648,0.00622606,0.01066586,0.00525939,-0.22125848,-0.00801327,-0.03246479,-0.00685232,0.03164952,-0.01056868,0.06130658,0.0145585,0.00989135,0.1001631,0.07971419,-0.0534777,-0.03274234,0.0267136,-0.0197333,0.02738575,-0.07923872,-0.02886142,-0.04128431,0.0355822,-0.01887666,0.06841246,-0.0367698,-0.0661495,0.08372007,-0.03450068,0.14676778,0.03824934,-0.01545458,-0.0134341,0.03923915,0.01636841,-0.0115193,-0.022881,0.01114059,0.06861994,0.05824832,-0.04812578,-0.02700244,-0.0193432,-0.09486127,0.03211148,-0.00320424,-0.10245518,0.0093177,0.0067365,0.00917772,0.01899101,0.00548428,0.06431797,0.06507147,-0.02738379,0.01728543,0.01698479,0.0178614,-0.03500845,-0.08976589,0.00478479,0.02089749,0.03878336,0.02756906,-0.04182121,0.02643558,-0.03697,0.01315316,0.0361175,0.01677388,-0.00937584,0.0160825,0.00061485,0.02757752,0.05870272,0.03325671,0.06559496,-0.00678102,0.01778816,0.05356862,-0.05614431,0.01910713,-0.0317684,0.03844835,-0.04696041,0.042735,0.0571408,0.04879651,-0.01399554,0.07141346,0.06939498,0.03724902,-0.02430661,-0.01202587,0.01770673,0.01307125,0.02061575,0.08459532,0.03550657,-0.28941277,0.01395973,-0.03105978,0.04028847,-0.03161776,-0.00745397,0.05667275,0.00167054,0.01183706,-0.08300852,0.04018781,0.04416503,-0.00950982,-0.07226001,-0.00207303,0.03207914,0.03479256,-0.06280167,0.03781662,-0.0353104,0.05329029,0.02800249,0.22996536,-0.01907649,-0.00211594,0.03516873,-0.05032615,0.04875192,-0.03237474,0.03819642,0.01754759,-0.00105577,0.0739501,-0.04347024,-0.01815855,0.07988538,0.01973603,-0.01562817,0.01135049,0.02844731,-0.09441965,0.0143983,-0.01655628,-0.01964779,0.10102177,0.01421078,-0.02881583,-0.0454092,0.01975323,0.06553196,-0.10920276,-0.06393392,-0.03906285,-0.04538128,0.02950512,0.067466,0.05159865,0.01999871,-0.01197169,0.03715053,0.00933465,-0.03988321,0.02180034,0.02601905,-0.01555898],"last_embed":{"hash":"1uutnjj","tokens":423}}},"text":null,"length":0,"last_read":{"hash":"1uutnjj","at":1753423619049},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>","lines":[100,154],"size":4661,"outlinks":[{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/pair-grid-pick.gif","line":48},{"title":"Missing lotto pairs can reduce drastically total combinations to play.","target":"https://saliu.com/HLINE.gif","line":54}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09268162,-0.06534522,-0.03101009,0.02068815,-0.04513106,0.05477526,-0.02958455,-0.00885446,0.07525561,-0.02537286,-0.00735456,-0.01566114,0.09177417,0.01127308,0.00617605,-0.02055498,-0.04309944,0.01642407,-0.05416426,0.00789959,0.07340039,-0.07707961,-0.05900363,-0.07706401,0.03754229,0.01736378,-0.04346835,-0.0641335,-0.04847769,-0.25125211,0.03023264,0.0289917,0.01609709,-0.05544897,-0.08143754,-0.03778098,-0.03391893,0.01630084,-0.07263748,0.02395107,-0.00816646,0.01188147,-0.01222577,-0.03496855,-0.02072741,-0.03369417,-0.04587699,0.00045709,0.02427444,0.00201905,-0.07249121,-0.04333129,-0.01764642,0.01662573,0.05657281,0.07472972,0.03112466,0.05707858,-0.01096928,0.05120068,0.01906354,0.07763128,-0.17662348,0.02883025,0.02622362,-0.00098555,-0.03224226,-0.01610556,0.01556677,0.09782049,0.02297021,0.05100315,-0.03871771,0.0201074,-0.00063508,-0.0246742,-0.04622507,-0.02065947,-0.02487328,0.01211792,-0.09495246,-0.00437885,-0.02230777,0.00540153,-0.00294343,0.04157967,0.0417152,0.00136668,-0.00746201,-0.04588905,-0.01984374,0.04530228,-0.00711984,0.02050471,0.01719543,0.03629955,0.03380283,-0.01723472,-0.02409369,0.1159228,-0.01891476,0.00745237,-0.00859936,0.01697811,0.00241165,-0.05674902,-0.03842366,-0.05979725,-0.03445249,0.03355236,0.06494755,-0.01427075,0.00353126,-0.04213681,-0.00551602,0.0516085,0.02186336,-0.00540673,0.03336256,0.00739779,-0.04448902,0.04095576,0.03426301,0.02614442,0.00943278,0.0192573,0.04642261,0.08380975,-0.00063125,0.04440121,0.02596073,0.00017118,-0.13361117,-0.03038604,-0.05902298,-0.03181152,0.01720312,0.01419548,-0.02453596,0.03896681,-0.03425195,-0.02728709,0.10348488,-0.07719418,0.00921277,0.06385155,-0.01224454,-0.00701184,0.01992806,0.03703823,0.01410722,0.01303979,-0.01301115,-0.06824603,0.00978371,-0.03068666,0.07181075,0.04138071,-0.02557955,0.037522,-0.03325704,-0.0156651,-0.05399359,0.11592728,-0.0133145,-0.12638815,0.01396171,0.08451221,-0.05493198,-0.04525643,-0.00267482,0.05414044,-0.0451321,-0.0035605,0.03576341,-0.00884712,-0.13369638,-0.03673578,0.04312963,0.02292738,0.01308946,0.04223143,-0.03003144,0.01214657,-0.02668707,-0.05164525,0.00125229,-0.01762504,-0.01588815,0.0328541,-0.01662086,0.02543777,-0.02079932,0.00729746,-0.01682631,-0.03766757,-0.01714458,-0.04898872,0.04934886,0.04823572,0.00092741,-0.01736306,0.04570564,0.01341702,-0.00613031,0.03747686,-0.03234333,-0.02678744,0.06138347,0.03719212,-0.02292923,-0.01016355,-0.00925078,0.06430226,-0.05230365,0.0339632,0.00350803,0.02729045,0.00849539,0.02706388,0.00600927,0.01403481,0.00499917,-0.22163355,-0.0077993,-0.03352048,-0.00871066,0.02972994,-0.01112034,0.06066491,0.01371657,0.01104663,0.09767541,0.08056076,-0.05262306,-0.0310918,0.02935831,-0.01898383,0.02804135,-0.07995731,-0.02873542,-0.04114527,0.0362176,-0.01950397,0.07079341,-0.03794527,-0.06422266,0.08484665,-0.03488865,0.14712738,0.03859442,-0.01339742,-0.01658342,0.03854952,0.01521823,-0.01078204,-0.02156937,0.01276506,0.06831725,0.06100806,-0.04904232,-0.02911012,-0.0225729,-0.09614705,0.0330256,-0.00373814,-0.09967849,0.00936329,0.00435578,0.01058647,0.0199361,0.00716248,0.06708999,0.06628656,-0.02746766,0.01694424,0.01796767,0.01652598,-0.03454376,-0.09102862,0.00789303,0.02077659,0.03968466,0.02943228,-0.04249296,0.02335862,-0.03402283,0.0107788,0.0349621,0.01519171,-0.00929386,0.0194119,-0.00011109,0.02871285,0.05675509,0.03421252,0.06619152,-0.00319539,0.01718571,0.05291931,-0.05456697,0.02099043,-0.03330325,0.03986474,-0.04525134,0.04344461,0.0562315,0.04795188,-0.01414766,0.06850533,0.0716697,0.03855163,-0.02514905,-0.01140734,0.01979904,0.01246347,0.01905696,0.08797438,0.03563423,-0.28936669,0.01376744,-0.03135027,0.03911991,-0.02680385,-0.0097869,0.05599162,0.00191727,0.01100817,-0.08279076,0.04005336,0.04475102,-0.01131773,-0.07044906,-0.00172622,0.03177232,0.0346409,-0.05982789,0.03828672,-0.03795764,0.05419397,0.02895039,0.23106778,-0.0195118,-0.00183274,0.03616139,-0.0484628,0.05006137,-0.03545268,0.03705599,0.01758616,-0.00076058,0.07437348,-0.04300771,-0.01786927,0.07837843,0.02104898,-0.01726978,0.00928197,0.02632338,-0.09421299,0.01345838,-0.01679116,-0.01899377,0.09996085,0.01537578,-0.03065667,-0.04435486,0.01991701,0.06417885,-0.10627093,-0.06163907,-0.04091694,-0.04711928,0.03206731,0.06846745,0.05279487,0.01841891,-0.01239782,0.03558205,0.00648132,-0.03876803,0.02289552,0.02626658,-0.01452051],"last_embed":{"hash":"1tfhwl8","tokens":422}}},"text":null,"length":0,"last_read":{"hash":"1tfhwl8","at":1753423619203},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#<u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>#{1}","lines":[102,154],"size":4579,"outlinks":[{"title":"Pair grid software for lottery creates winning lotto strategy systems.","target":"https://saliu.com/ScreenImgs/pair-grid-pick.gif","line":46},{"title":"Missing lotto pairs can reduce drastically total combinations to play.","target":"https://saliu.com/HLINE.gif","line":52}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11071976,-0.03421649,-0.00953817,0.04039941,-0.00615171,0.03148666,-0.05396828,0.00707774,0.03402549,0.0055009,-0.02454617,-0.00464661,0.04401067,0.01127177,0.00479359,-0.02629619,0.01966152,0.0171376,-0.04239451,0.0010152,0.0531471,-0.02764502,-0.03696937,-0.07242583,0.04556651,0.02526128,-0.04675539,-0.03130954,-0.0366069,-0.21351297,0.0184866,0.04809959,0.0026919,-0.04679541,-0.09347794,-0.01382765,-0.02689007,0.02231136,-0.03874023,0.01789013,0.0326774,0.01868633,-0.06302793,-0.0044695,0.02960361,-0.07786829,0.01542324,-0.00715589,0.04284401,-0.04975409,-0.07703999,-0.04709693,-0.02572803,0.00958607,0.05941799,0.04863689,0.03891808,0.099886,-0.00457905,0.03358978,0.03420505,0.10197806,-0.18672165,0.06822569,-0.0253583,-0.02760381,-0.04859713,-0.04289078,0.02930205,0.02328593,0.00784604,0.01412424,-0.01769315,0.03465511,-0.00820639,-0.03237263,-0.00160507,-0.0410475,-0.01590215,-0.04073027,-0.02225554,-0.01469406,-0.00618641,-0.0089507,0.01261597,0.07139508,0.03724414,0.03138782,0.04672421,-0.08117943,-0.0010747,-0.00191901,0.04990716,0.06335128,-0.01175332,0.01585968,0.03158523,-0.02842226,-0.01602108,0.12256769,0.00054248,-0.0093254,0.02265472,0.02477723,0.04267656,-0.02458115,0.0043461,-0.0298449,-0.04490424,0.04659801,0.0932653,-0.00402507,0.01911253,-0.03568398,0.00021509,-0.0113896,-0.02963736,-0.00534569,0.01858003,0.02293958,-0.0809523,0.00132471,0.01694046,0.01300767,-0.00782124,0.01486836,0.05792611,0.04474013,0.01394493,0.04081559,0.02138019,0.04419773,-0.1243594,-0.04941661,-0.01830659,0.00796844,0.00953021,-0.04970963,-0.01264595,0.05015925,-0.01491602,-0.06197559,0.08447371,-0.12102521,-0.02167785,0.06880541,-0.00225753,-0.02600995,-0.0123573,0.02931153,-0.02834921,0.01271354,-0.00477524,-0.04018701,0.01999277,-0.02895051,0.10196715,0.09749319,-0.05949231,0.01654018,0.00265515,0.0015704,-0.05063811,0.10842416,-0.02628669,-0.16911881,0.00101567,0.07604729,-0.02908956,-0.07528383,-0.03040911,0.03592063,-0.04581164,0.01076881,0.10309045,-0.04639944,-0.0583135,-0.03549626,-0.01862099,-0.00019502,0.01511308,-0.01656882,-0.03448328,0.02645373,-0.03442386,-0.08155987,0.01673616,-0.03335793,-0.00271633,0.055,-0.03490224,0.00026125,-0.01263243,0.0014945,-0.02632653,-0.01152813,-0.05681678,-0.04265367,0.07193984,-0.00514487,-0.03573069,0.00770324,0.02013733,0.01566795,-0.00943144,0.01052345,-0.03096555,-0.06614613,0.05339606,0.0050525,-0.0328223,0.04583207,0.03870599,0.06312143,-0.07308366,0.01974791,-0.00419988,0.06444025,-0.03618393,0.02099554,0.03954967,0.040685,-0.03008325,-0.20891763,0.00243149,-0.05449226,-0.00623637,0.01074153,-0.04794831,0.07999676,0.02220902,0.03606436,0.09079885,0.10930528,-0.05902339,-0.00024418,-0.0131012,0.00383848,-0.01024791,-0.04580527,-0.0526437,-0.03236847,0.01569774,0.02040424,0.03682974,0.01381193,-0.08772693,0.02236782,-0.04266562,0.10691264,0.05429685,0.01654021,-0.03191432,0.02143526,-0.01957841,-0.02005288,-0.08713593,0.03149884,0.05401255,0.01230262,0.01666628,-0.0536642,-0.03117611,-0.08436068,0.03097672,-0.02518408,-0.0972209,0.01547418,0.03751686,0.02648194,0.01053582,-0.00136708,0.03490505,0.03364975,0.00584117,0.01464751,0.05894767,0.04136548,-0.03677782,-0.06571405,-0.01357823,-0.00432831,0.03160889,-0.00259289,-0.03653581,0.0615358,-0.04314927,0.06445429,0.00480825,0.00109964,-0.02844238,0.01705777,0.01234546,-0.01645912,0.080811,0.02029383,0.08055665,-0.01904994,0.03568966,0.07265573,-0.05192505,0.02725019,-0.00058106,-0.02201312,-0.03619347,0.07214084,0.10924713,0.08043506,0.00724399,0.0581237,0.00096921,0.02246027,0.00041273,-0.01033252,-0.00433215,0.00020552,0.00309563,0.06291949,0.04810261,-0.2753509,0.02335268,-0.00470335,0.01848787,-0.03432471,0.01076073,0.06036429,-0.04172163,0.03305195,-0.05272849,0.0392894,0.01605449,0.00474373,-0.07377679,-0.05218532,0.02959975,0.04504607,-0.01546952,0.02973009,0.0228946,-0.00897705,0.05372978,0.21818067,0.03732246,-0.00886549,0.03016093,-0.03434534,0.01371887,-0.00731957,0.05608987,0.00574955,0.01118368,0.04584351,-0.00396901,-0.01771498,0.06434838,-0.03287726,-0.02278884,0.05524676,0.04062483,-0.06087512,0.04853664,-0.05160984,0.00051028,0.09064073,0.03340102,-0.0143598,-0.0930599,0.03155085,0.06232321,-0.09675505,-0.09629852,-0.07650226,0.0142178,0.03071396,0.02669192,0.05623766,-0.006938,0.00384672,0.00777314,0.0601294,-0.05791723,0.02569284,0.0296321,-0.00339979],"last_embed":{"hash":"t3h3i5","tokens":441}}},"text":null,"length":0,"last_read":{"hash":"t3h3i5","at":1753423619355},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)","lines":[155,184],"size":3141,"outlinks":[{"title":"<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"**Pick-3 Lottery Software**","target":"https://saliu.com/lottery3-software.html","line":7},{"title":"**Pick-4 Lottery Software**","target":"https://saliu.com/lottery4-software.html","line":8},{"title":"**Horse Racing Trifecta Software**","target":"https://saliu.com/horseracing-software.html","line":9},{"title":"**5-Number Lotto Software**","target":"https://saliu.com/lotto5-software.html","line":10},{"title":"**6-Number Lotto Software**","target":"https://saliu.com/lotto6-software.html","line":11},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":12},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":13},{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":15},{"title":"_**Lottery, Lotto Strategy in Reverse: Win, Lose, Hit, Miss**_","target":"https://saliu.com/reverse-strategy.html","line":16},{"title":"_**Software Analysis, Lotto Number Frequency, Lottery Pairs**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":17},{"title":"**<u>Software, Systems, Lottery Skips</u>**","target":"https://saliu.com/skip-strategy.html","line":18},{"title":"_**Lotto, Decades: Systems, Software, Analysis, Strategy**_","target":"https://saliu.com/decades.html","line":19},{"title":"_**Lottery, Lotto Strategy Based On Sums (Sum-Totals), Odd – Even, Low - High Numbers**_","target":"https://saliu.com/strategy.html","line":20},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":21},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":22},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":23},{"title":"The lottery software groups pick lotteries, horse racing, jackpot lotto games.","target":"https://saliu.com/HLINE.gif","line":25},{"title":"Forums","target":"https://forums.saliu.com/","line":27},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":27},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":27},{"title":"Contents","target":"https://saliu.com/content/index.html","line":27},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":27},{"title":"Home","target":"https://saliu.com/index.htm","line":27},{"title":"Search","target":"https://saliu.com/Search.htm","line":27},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":27},{"title":"Get source code to generate lotto combinations from 3 groups of numbers.","target":"https://saliu.com/HLINE.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09200357,-0.03124433,0.00056604,0.03327174,0.00744125,0.02831961,-0.03070722,0.00123785,0.03164612,-0.01224334,-0.03463901,0.00332287,0.05417975,0.0238063,0.00711576,-0.02038125,0.01575425,0.02950024,-0.02997537,-0.0045615,0.05872748,-0.01126173,-0.03558533,-0.0782043,0.0461924,0.01592294,-0.04070788,-0.03305142,-0.02734397,-0.17434512,0.00767513,0.05238977,-0.01661962,-0.05678108,-0.08669671,-0.01776151,-0.00360277,0.01901048,-0.04191498,0.02285536,0.04771795,-0.00670523,-0.05283116,0.01002038,0.03057459,-0.06279845,0.01687007,0.00173144,0.02983273,-0.04395544,-0.06196426,-0.01250894,-0.02453715,0.00270256,0.05916715,0.04985467,0.03345143,0.09460335,0.00922703,0.03878646,0.02804987,0.08068943,-0.18475792,0.05867469,-0.02185509,-0.02703198,-0.03383609,-0.01803617,0.02320881,0.0207953,0.01938759,0.02011409,-0.01406167,0.03820252,0.00554011,-0.04223961,0.00591318,-0.03847354,-0.01254847,-0.01611984,-0.02925195,-0.01688905,-0.00310992,-0.00058263,0.00428741,0.06792486,0.06664833,0.04776628,0.05736426,-0.09844853,-0.00279592,0.00680104,0.0680269,0.05682376,-0.01486273,0.00441005,0.03725696,-0.02556814,0.00972268,0.14181271,-0.01680798,-0.00506825,0.04514772,0.01563654,0.04551898,-0.02098496,0.00566857,-0.01532928,-0.03625063,0.05713346,0.0829864,0.00545242,0.00387878,-0.03398418,0.01871418,-0.03471057,-0.03075823,0.01568458,0.02375224,0.00022205,-0.09722243,-0.01308931,0.03511555,0.00975794,-0.01757084,-0.00272877,0.05853372,0.04588173,0.01459928,0.0271781,0.00440378,0.02421916,-0.11589111,-0.04955783,-0.02256698,0.01204214,0.0279412,-0.05259541,-0.02669575,0.07873795,-0.02770712,-0.04521159,0.08518986,-0.13375954,-0.02713137,0.08371118,0.00473586,-0.01718467,-0.02714711,0.03742099,-0.03968534,0.01138905,0.01606203,-0.03668189,0.01302389,-0.0273474,0.11487534,0.08600505,-0.04592881,0.01766414,-0.00155806,-0.00268392,-0.04900866,0.12800515,-0.04781422,-0.15628491,-0.00182539,0.06700103,-0.04834927,-0.07903825,-0.03273898,0.02985389,-0.03551655,0.00842648,0.10366645,-0.05843862,-0.04850709,-0.03183199,-0.02784182,-0.01001034,0.01807719,-0.03338425,-0.03249716,0.03034672,-0.01884206,-0.07476591,0.01128061,-0.01758271,0.01172812,0.05824796,-0.05803422,-0.01456614,-0.01673867,-0.00991147,-0.03135817,-0.01296079,-0.0609465,-0.04677262,0.06859604,-0.0212779,-0.03607918,-0.01306769,0.00189798,0.01949652,0.00155939,0.01716026,-0.04737512,-0.04912953,0.04098717,0.00824502,-0.0555897,0.03523177,0.03616606,0.0691117,-0.08663093,0.00011614,-0.00097683,0.06977672,-0.02670744,0.01564847,0.0365345,0.01951216,-0.04750402,-0.20116144,-0.0032147,-0.05296717,-0.00479556,0.01091181,-0.04680211,0.05168873,0.02180054,0.02133477,0.10117,0.10467689,-0.07754657,0.00545518,-0.02737467,-0.00803548,-0.03191065,-0.0318299,-0.0828568,-0.03275645,0.01728014,0.01644002,0.01878171,0.00319543,-0.08704974,0.01376453,-0.04153012,0.11152857,0.06149879,0.02006295,-0.02208013,0.01327676,-0.0202035,-0.01258995,-0.09100765,0.0094602,0.04928941,0.00171689,-0.0028478,-0.0320123,-0.00632104,-0.08782528,0.01654263,-0.03776138,-0.08559862,0.02582559,0.02911071,0.03594324,-0.01285579,0.00087024,0.03144265,0.02772945,-0.01166972,0.01966007,0.08186264,0.0447112,-0.03546296,-0.06195616,-0.0092861,-0.00955074,0.02261943,-0.00391484,-0.03727152,0.056525,-0.050908,0.04830351,0.00703672,-0.02321418,-0.04590879,0.00964266,0.00054,-0.03242843,0.09195237,0.00792953,0.06279229,-0.02628504,0.04107406,0.08259367,-0.04463301,0.01947227,0.01569686,-0.01528013,-0.03877303,0.07737817,0.10377057,0.07549977,0.00283885,0.05398661,-0.00747657,0.00937989,-0.00015967,-0.02201096,0.00074185,-0.00391985,0.00250874,0.06353931,0.04928619,-0.25609985,0.03724307,-0.01311881,0.03619178,-0.04449582,-0.00325668,0.0596298,-0.0401221,0.03870592,-0.04830118,0.08243259,0.01427174,-0.00571222,-0.07770255,-0.05402228,0.03281201,0.07236093,-0.03206129,0.03723944,0.04116668,0.00364339,0.04805173,0.23411471,0.04710549,-0.00962191,0.03556304,-0.05151436,0.00412056,-0.02433744,0.06896479,0.0313218,0.01334208,0.02550455,-0.00215938,-0.00551001,0.07097113,-0.02168546,0.00786271,0.05914272,0.05607035,-0.06354215,0.03806011,-0.06264974,0.0145295,0.0895083,0.0408627,-0.00566114,-0.09683509,0.01340474,0.03808989,-0.09152132,-0.09378834,-0.08496717,0.00352201,0.02045672,0.02736677,0.06116011,-0.02102279,0.01295051,-0.0017753,0.05872159,-0.04413024,0.02450953,0.01968716,0.00181047],"last_embed":{"hash":"i9rt9j","tokens":127}}},"text":null,"length":0,"last_read":{"hash":"i9rt9j","at":1753423619512},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{1}","lines":[157,160],"size":225,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{19}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11138914,-0.03834186,-0.03127075,0.01763628,-0.01726745,0.05554393,-0.05955191,-0.00558678,0.02405909,0.00469317,-0.0477989,-0.02115915,0.07697803,0.01466284,0.01229761,-0.05147231,-0.01582561,-0.00370868,-0.06244203,0.00463931,0.06407247,-0.01474479,-0.04040046,-0.0907113,0.0250562,0.0026982,-0.05540441,-0.06103226,-0.0353457,-0.20080054,0.01573072,0.05186041,0.01302606,-0.06637171,-0.08079594,0.01148433,-0.00959917,0.02816962,-0.07318807,0.06086067,-0.01203273,0.00733838,-0.02686477,-0.01503571,0.0192041,-0.0524784,-0.01084415,0.00862624,0.03720437,-0.01493765,-0.06265451,-0.00641852,-0.02974993,0.04077693,0.07797737,0.03063309,0.00814143,0.07224473,-0.029529,0.03658684,0.02812987,0.09502854,-0.21169145,0.04941682,-0.00661982,0.00752884,-0.0382142,-0.00994895,0.02398096,0.03088113,0.01569766,0.04960079,-0.01135088,0.03210364,0.03150268,-0.02559825,-0.04076396,-0.0407293,-0.04183711,-0.01326707,-0.05276343,-0.00519315,-0.05421551,-0.00620297,0.01452583,0.06406248,0.04739222,0.04422253,0.07063113,-0.06339686,-0.00444409,0.01782481,0.03994136,0.06378473,0.01003267,0.01476554,0.03519442,-0.02728642,-0.00557989,0.11589341,0.00808293,-0.02069261,0.02877873,0.02673169,0.03692953,-0.02285084,-0.02822998,-0.01850597,-0.01977487,0.04215004,0.0647888,0.02873064,0.02292898,-0.05683492,-0.04572355,-0.00784101,0.00016411,-0.01633767,-0.00042119,0.00576151,-0.077168,0.00146806,0.02719456,0.00466322,0.02103235,0.01732982,0.03512271,0.06327485,0.02060247,0.04320901,0.01034955,0.06379698,-0.1498777,-0.05959294,-0.04188559,-0.03373111,-0.00683866,-0.04423617,-0.02626833,0.02793635,-0.02016159,-0.06450667,0.08431358,-0.09521744,-0.01025429,0.07074932,-0.00550472,-0.01133654,-0.02534849,0.01803796,-0.03664087,0.00176452,0.00329304,-0.04314445,0.014575,-0.01733504,0.10509539,0.07490619,-0.04899088,0.02381259,0.01281512,-0.01907679,-0.02864906,0.14167087,0.00533815,-0.12542883,-0.0265993,0.08642957,-0.01027734,-0.06224613,-0.01895578,0.03695769,-0.03412668,0.01165755,0.07983435,-0.03459105,-0.08518108,-0.0303872,0.00303857,0.0103383,0.0083286,0.0075194,-0.01682503,0.01995765,-0.04528211,-0.0899254,0.02610456,-0.06501825,-0.00584434,0.05926551,-0.02519907,-0.01067296,-0.03843277,-0.00494654,-0.0159249,-0.0107861,-0.0442562,-0.05653736,0.08639845,0.02261947,-0.05481916,-0.01086701,0.02083472,-0.0054531,-0.01308192,0.03787413,-0.03915946,-0.06290126,0.04983818,0.03020997,-0.00616257,0.02414159,0.04429993,0.06584166,-0.06125033,0.05707669,0.0060435,0.03392295,0.00223752,0.02558933,0.03318974,0.02387575,-0.04462847,-0.19498508,-0.00567936,-0.06902289,-0.01335484,0.00722163,-0.02731174,0.076686,0.00327688,0.01759588,0.08915648,0.09657698,-0.07257029,0.00059099,0.0166404,-0.00332869,-0.01976247,-0.07180532,-0.04510244,-0.03020186,0.05770791,0.00281977,0.03010233,-0.02127568,-0.08389951,0.03513931,-0.03206564,0.12275754,0.06927843,0.01218877,-0.01114816,0.04932911,0.01994412,-0.01287181,-0.06371979,0.04980088,0.05848945,-0.01097424,0.00510216,-0.02974315,0.00704143,-0.07154687,0.04635727,-0.00673367,-0.0952133,-0.00115213,0.01187554,0.0047753,0.00108459,-0.00634168,0.03413354,0.03948688,-0.00091136,0.05609156,0.03894217,0.04412246,-0.01386229,-0.06558801,-0.0028947,0.00890076,0.0227515,0.03457443,-0.04801891,0.07004886,-0.03031675,0.03663442,0.00558662,-0.01873901,-0.01979559,0.00524921,0.00426482,-0.01410637,0.09046277,0.02270016,0.05431563,-0.01289995,0.01420397,0.04922663,-0.08050075,0.01504589,-0.03477433,-0.01526594,-0.08224563,0.05412114,0.09225965,0.08644482,-0.00015291,0.05916675,0.0367839,0.04940474,-0.00162161,-0.0213731,-0.0050099,-0.0228509,0.02859309,0.06712393,0.04225338,-0.26167837,0.02258099,-0.00442607,0.04267594,-0.0050315,0.00164752,0.04672886,-0.03231158,0.01833518,-0.0483229,0.07407375,0.01820821,-0.00232807,-0.10610638,-0.01632398,0.05015275,0.01862921,-0.05229009,0.03722242,0.01155005,0.00148684,0.05780731,0.23118894,0.0238201,0.01052709,0.02035126,-0.01757641,0.02622041,0.01077798,0.03924751,-0.00146281,-0.00025588,0.07032435,-0.0286714,-0.04481954,0.06460425,-0.01125496,-0.02647167,0.02970557,0.02598503,-0.0637412,0.02975636,-0.05171551,0.00306166,0.0942831,0.02917093,-0.00551192,-0.085846,0.01685279,0.04581638,-0.10471983,-0.08391624,-0.07113153,-0.00004136,0.0414917,0.03535091,0.05321798,-0.01378103,0.00963966,-0.00035962,0.03841336,-0.03280723,0.04840128,0.01173704,-0.01875342],"last_embed":{"hash":"vyg4of","tokens":317}}},"text":null,"length":0,"last_read":{"hash":"vyg4of","at":1753423619551},"key":"notes/saliu/Software News Lotto, Lottery, Horse Racing, Pairs Strategy.md#Software News: Lotto, Lottery, Horse Racing, Pairs Strategy#[<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)#{19}","lines":[179,184],"size":609,"outlinks":[{"title":"The lottery software groups pick lotteries, horse racing, jackpot lotto games.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Get source code to generate lotto combinations from 3 groups of numbers.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
