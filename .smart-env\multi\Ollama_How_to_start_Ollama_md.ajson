"smart_sources:Ollama/How to start Ollama.md": {"path":"Ollama/How to start Ollama.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0840097,-0.0154583,0.01175718,-0.05908568,-0.02130892,0.01259194,-0.06808967,0.01838151,0.01955842,0.04840634,0.00152041,-0.0512108,-0.01221187,0.01215652,-0.01209651,0.03796196,-0.01471152,0.05266899,-0.02827032,0.02846283,0.10680436,-0.05664766,0.03040094,-0.01465497,0.03779001,0.03430468,-0.02533447,-0.00391738,-0.01460441,-0.22979636,0.02117641,0.00592709,-0.00671208,-0.04924385,-0.04132584,-0.03005413,0.00215222,-0.033427,-0.06693023,0.03744541,0.01918138,0.00523827,-0.00443043,-0.01650336,-0.02551741,-0.04213979,-0.07232607,0.08623651,0.05577214,-0.01453731,0.01392204,-0.04289606,-0.0622258,-0.07782418,0.03047274,0.03812618,0.06536196,0.05252496,0.00941796,0.06380875,-0.01477442,0.01360709,-0.19166863,0.14614755,0.05970478,0.00336769,0.0273566,0.03016317,0.00982251,0.03642235,-0.03716363,0.0243216,0.03457056,-0.00592452,-0.02183572,0.01500766,-0.02014002,-0.00189964,0.00927193,-0.01563077,-0.02701315,0.00100805,0.03988992,-0.01638813,-0.03090922,-0.00644032,0.00107,0.00570774,0.0031945,0.00584258,-0.07829396,-0.03415028,0.04983794,-0.00967994,-0.07433103,0.02160853,-0.00453221,0.0054407,-0.05941974,0.10851096,-0.03595931,-0.05098162,0.0521126,-0.08480427,0.05049785,0.01030628,0.00859183,-0.00539315,-0.04494816,0.02951861,0.01637659,0.02753934,-0.02713954,-0.12845889,0.02791879,-0.04507099,-0.03025365,-0.00856348,-0.02683718,-0.06063517,0.04641563,-0.02466256,0.00894608,-0.02370772,0.01229106,0.0009493,0.09254621,0.04144004,0.03408095,0.0159323,0.06752887,0.03308496,-0.03928665,-0.0595946,-0.03104572,0.05204483,0.03852108,0.05254176,-0.01177055,-0.00979435,0.00110529,-0.02464602,0.03950398,-0.10700237,0.04974844,-0.0082857,-0.00476415,0.00941093,-0.07815816,-0.05495971,-0.02460222,0.04539946,-0.03038378,0.00062221,0.04437776,-0.01524537,0.03892555,0.11345969,-0.0750493,0.01595441,-0.09461323,-0.03756648,0.0342963,0.00858478,-0.03248002,-0.05706247,-0.04733286,0.0417234,-0.01684924,-0.04935763,0.02173846,0.05819095,-0.07119546,-0.02401371,0.08051624,-0.03405938,-0.07463649,0.01010954,-0.00890807,0.05010189,0.00720826,-0.06279322,0.01134933,-0.03647645,0.0046378,-0.06584524,-0.00811596,-0.07080135,0.02393421,-0.00712311,-0.06537514,0.02865661,-0.03811041,0.02905548,-0.00892419,-0.01765702,-0.01938618,-0.0132066,0.02373707,-0.0479715,0.07897713,0.01795864,-0.03109428,0.08261676,-0.08706684,-0.0267611,0.02165878,-0.03700676,0.12405227,-0.02112808,-0.0941155,-0.0013777,0.08492399,0.00520032,-0.05536042,0.03042495,0.01928789,0.01715159,0.02182508,0.03939475,0.05149516,-0.02833464,-0.09469889,-0.21434887,0.0031342,0.02487209,0.0424538,0.0452907,-0.10983311,0.05510603,-0.01137095,0.01681514,0.01189419,0.09445234,-0.05721151,0.00296368,-0.00470982,-0.04701865,0.0095935,-0.05049865,0.02169523,0.00441513,0.05166315,-0.00370862,-0.01199772,-0.01446775,-0.07214488,-0.013422,0.00288848,0.16212328,0.08074218,0.05208177,-0.01829761,0.06617952,0.0321051,-0.0650207,-0.0642845,0.00772744,0.00950271,0.04331223,0.06692092,0.02719711,0.04797393,0.02283418,0.03105612,0.0265252,-0.04623069,-0.0340542,-0.01848627,0.00299577,0.02456779,-0.04242783,0.00380371,0.02951565,-0.0226476,0.0026354,0.02390167,-0.04883151,-0.05442718,-0.09514324,-0.01365755,-0.00155817,-0.00089023,0.01943159,0.03216378,0.00518949,-0.05533185,0.02456362,0.00973326,0.01152626,-0.03651876,0.11304424,-0.05021962,0.02045638,0.10846462,0.01283822,0.05495125,0.12291226,-0.02206722,0.00088229,-0.0444904,-0.01732734,0.03333475,0.01068598,-0.02715725,0.03879527,0.03564039,0.01018085,0.0359728,0.0164656,0.0050165,0.06302891,-0.06015664,-0.07868144,0.01202156,0.01258664,0.00335348,0.0676261,0.02595871,-0.27662796,0.01746446,0.04072578,0.06338706,-0.00553278,0.0578871,-0.00172558,-0.01022266,0.01424355,-0.01046276,-0.04957662,0.02681462,0.05163292,0.00936866,-0.00037413,0.04113381,0.01147852,-0.01353443,0.03105723,-0.01759121,0.029311,0.01065333,0.21655816,-0.03848974,0.01302315,0.02654745,-0.01452392,0.02050621,0.08627347,0.02950228,0.04489659,0.00838399,0.07365834,-0.03412892,0.07856907,-0.04507084,-0.04357795,-0.00394881,0.00455649,0.01217675,-0.07620835,0.03146912,0.01353204,-0.01651887,0.03404156,-0.02200923,-0.02445466,-0.00595625,-0.0301011,0.05346623,0.03547424,0.02407816,-0.01020531,0.02385439,-0.00703744,0.05930345,-0.01620906,0.00222314,-0.03845847,-0.02276993,0.01821014,0.02126914,0.048913,0.0020559,-0.01996682],"last_embed":{"hash":"7f656910fc1992e723e3225b9f40bde3cc7fb5ac948aa7b6358d1747cf28d774","tokens":462}}},"last_read":{"hash":"7f656910fc1992e723e3225b9f40bde3cc7fb5ac948aa7b6358d1747cf28d774","at":1745995214300},"class_name":"SmartSource2","outlinks":[{"title":"Download","target":"https://ollama.com/download/Ollama-darwin.zip","line":80},{"title":"Download","target":"https://ollama.com/download/OllamaSetup.exe","line":84},{"title":"Manual install instructions","target":"https://github.com/ollama/ollama/blob/main/docs/linux.md","line":93},{"title":"Ollama Docker image","target":"https://hub.docker.com/r/ollama/ollama","line":98},{"title":"ollama-python","target":"https://github.com/ollama/ollama-python","line":103},{"title":"ollama-js","target":"https://github.com/ollama/ollama-js","line":104},{"title":"Discord","target":"https://discord.gg/ollama","line":110},{"title":"Reddit","target":"https://reddit.com/r/ollama","line":111},{"title":"Llama 3.2","target":"https://ollama.com/library/llama3.2","line":119},{"title":"ollama.com/library","target":"https://ollama.com/library 'ollama model library'","line":134},{"title":"ollama.com/library","target":"https://ollama.com/library `ollama model library","line":136},{"title":"guide","target":"docs/import.md","line":219},{"title":"Modelfile","target":"docs/modelfile.md","line":267},{"title":"developer guide","target":"https://github.com/ollama/ollama/blob/main/docs/development.md","line":384},{"title":"API documentation","target":"./docs/api.md","line":424},{"title":"Open WebUI","target":"https://github.com/open-webui/open-webui","line":430},{"title":"Enchanted (macOS native)","target":"https://github.com/AugustDev/enchanted","line":431},{"title":"Hollama","target":"https://github.com/fmaclen/hollama","line":432},{"title":"Lollms-Webui","target":"https://github.com/ParisNeo/lollms-webui","line":433},{"title":"LibreChat","target":"https://github.com/danny-avila/LibreChat","line":434},{"title":"Bionic GPT","target":"https://github.com/bionic-gpt/bionic-gpt","line":435},{"title":"HTML UI","target":"https://github.com/rtcfirefly/ollama-ui","line":436},{"title":"Saddle","target":"https://github.com/jikkuatwork/saddle","line":437},{"title":"Chatbot UI","target":"https://github.com/ivanfioravanti/chatbot-ollama","line":438},{"title":"Chatbot UI v2","target":"https://github.com/mckaywrigley/chatbot-ui","line":439},{"title":"Typescript UI","target":"https://github.com/ollama-interface/Ollama-Gui?tab=readme-ov-file","line":440},{"title":"Minimalistic React UI for Ollama Models","target":"https://github.com/richawo/minimal-llm-ui","line":441},{"title":"Ollamac","target":"https://github.com/kevinhermawan/Ollamac","line":442},{"title":"big-AGI","target":"https://github.com/enricoros/big-AGI/blob/main/docs/config-local-ollama.md","line":443},{"title":"Cheshire Cat assistant framework","target":"https://github.com/cheshire-cat-ai/core","line":444},{"title":"Amica","target":"https://github.com/semperai/amica","line":445},{"title":"chatd","target":"https://github.com/BruceMacD/chatd","line":446},{"title":"Ollama-SwiftUI","target":"https://github.com/kghandour/Ollama-SwiftUI","line":447},{"title":"Dify.AI","target":"https://github.com/langgenius/dify","line":448},{"title":"MindMac","target":"https://mindmac.app","line":449},{"title":"NextJS Web Interface for Ollama","target":"https://github.com/jakobhoeg/nextjs-ollama-llm-ui","line":450},{"title":"Msty","target":"https://msty.app","line":451},{"title":"Chatbox","target":"https://github.com/Bin-Huang/Chatbox","line":452},{"title":"WinForm Ollama Copilot","target":"https://github.com/tgraupmann/WinForm_Ollama_Copilot","line":453},{"title":"Get Started Doc","target":"https://docs.nextchat.dev/models/ollama","line":454},{"title":"NextChat","target":"https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web","line":454},{"title":"Alpaca WebUI","target":"https://github.com/mmo80/alpaca-webui","line":455},{"title":"OllamaGUI","target":"https://github.com/enoch1118/ollamaGUI","line":456},{"title":"OpenAOE","target":"https://github.com/InternLM/OpenAOE","line":457},{"title":"Odin Runes","target":"https://github.com/leonid20000/OdinRunes","line":458},{"title":"LLM-X","target":"https://github.com/mrdjohnson/llm-x","line":459},{"title":"AnythingLLM (Docker + MacOs/Windows/Linux native app)","target":"https://github.com/Mintplex-Labs/anything-llm","line":460},{"title":"Ollama Basic Chat: Uses HyperDiv Reactive UI","target":"https://github.com/rapidarchitect/ollama_basic_chat","line":461},{"title":"Ollama-chats RPG","target":"https://github.com/drazdra/ollama-chats","line":462},{"title":"IntelliBar","target":"https://intellibar.app/","line":463},{"title":"QA-Pilot","target":"https://github.com/reid41/QA-Pilot","line":464},{"title":"ChatOllama","target":"https://github.com/sugarforever/chat-ollama","line":465},{"title":"CRAG Ollama Chat","target":"https://github.com/Nagi-ovo/CRAG-Ollama-Chat","line":466},{"title":"RAGFlow","target":"https://github.com/infiniflow/ragflow","line":467},{"title":"StreamDeploy","target":"https://github.com/StreamDeploy-DevRel/streamdeploy-llm-app-scaffold","line":468},{"title":"chat","target":"https://github.com/swuecho/chat","line":469},{"title":"Lobe Chat","target":"https://github.com/lobehub/lobe-chat","line":470},{"title":"Integrating Doc","target":"https://lobehub.com/docs/self-hosting/examples/ollama","line":470},{"title":"Ollama RAG Chatbot","target":"https://github.com/datvodinh/rag-chatbot.git","line":471},{"title":"BrainSoup","target":"https://www.nurgo-software.com/products/brainsoup","line":472},{"title":"macai","target":"https://github.com/Renset/macai","line":473},{"title":"RWKV-Runner","target":"https://github.com/josStorer/RWKV-Runner","line":474},{"title":"Ollama Grid Search","target":"https://github.com/dezoito/ollama-grid-search","line":475},{"title":"Olpaka","target":"https://github.com/Otacon/olpaka","line":476},{"title":"OllamaSpring","target":"https://github.com/CrazyNeil/OllamaSpring","line":477},{"title":"LLocal.in","target":"https://github.com/kartikm7/llocal","line":478},{"title":"Shinkai Desktop","target":"https://github.com/dcSpark/shinkai-apps","line":479},{"title":"AiLama","target":"https://github.com/zeyoyt/ailama","line":480},{"title":"Ollama with Google Mesop","target":"https://github.com/rapidarchitect/ollama_mesop/","line":481},{"title":"R2R","target":"https://github.com/SciPhi-AI/R2R","line":482},{"title":"Ollama-Kis","target":"https://github.com/elearningshow/ollama-kis","line":483},{"title":"OpenGPA","target":"https://opengpa.org","line":484},{"title":"Painting Droid","target":"https://github.com/mateuszmigas/painting-droid","line":485},{"title":"Kerlig AI","target":"https://www.kerlig.com/","line":486},{"title":"AI Studio","target":"https://github.com/MindWorkAI/AI-Studio","line":487},{"title":"Sidellama","target":"https://github.com/gyopak/sidellama","line":488},{"title":"LLMStack","target":"https://github.com/trypromptly/LLMStack","line":489},{"title":"BoltAI for Mac","target":"https://boltai.com","line":490},{"title":"Harbor","target":"https://github.com/av/harbor","line":491},{"title":"PyGPT","target":"https://github.com/szczyglis-dev/py-gpt","line":492},{"title":"Alpaca","target":"https://github.com/Jeffser/Alpaca","line":493},{"title":"AutoGPT","target":"https://github.com/Significant-Gravitas/AutoGPT/blob/master/docs/content/platform/ollama.md","line":494},{"title":"Go-CREW","target":"https://www.jonathanhecl.com/go-crew/","line":495},{"title":"PartCAD","target":"https://github.com/openvmp/partcad/","line":496},{"title":"Ollama4j Web UI","target":"https://github.com/ollama4j/ollama4j-web-ui","line":497},{"title":"PyOllaMx","target":"https://github.com/kspviswa/pyOllaMx","line":498},{"title":"Claude Dev","target":"https://github.com/saoudrizwan/claude-dev","line":499},{"title":"Cherry Studio","target":"https://github.com/kangfenmao/cherry-studio","line":500},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":501},{"title":"Archyve","target":"https://github.com/nickthecook/archyve","line":502},{"title":"crewAI with Mesop","target":"https://github.com/rapidarchitect/ollama-crew-mesop","line":503},{"title":"Tkinter-based client","target":"https://github.com/chyok/ollama-gui","line":504},{"title":"LLMChat","target":"https://github.com/trendy-design/llmchat","line":505},{"title":"Local Multimodal AI Chat","target":"https://github.com/Leon-Sander/Local-Multimodal-AI-Chat","line":506},{"title":"ARGO","target":"https://github.com/xark-argo/argo","line":507},{"title":"OrionChat","target":"https://github.com/EliasPereirah/OrionChat","line":508},{"title":"G1","target":"https://github.com/bklieger-groq/g1","line":509},{"title":"Web management","target":"https://github.com/lemonit-eric-mao/ollama-web-management","line":510},{"title":"Promptery","target":"https://github.com/promptery/promptery","line":511},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":512},{"title":"chat-ollama","target":"https://github.com/annilq/chat-ollama","line":513},{"title":"SpaceLlama","target":"https://github.com/tcsenpai/spacellama","line":514},{"title":"YouLama","target":"https://github.com/tcsenpai/youlama","line":515},{"title":"DualMind","target":"https://github.com/tcsenpai/dualmind","line":516},{"title":"ollamarama-matrix","target":"https://github.com/h1ddenpr0cess20/ollamarama-matrix","line":517},{"title":"ollama-chat-app","target":"https://github.com/anan1213095357/ollama-chat-app","line":518},{"title":"Perfect Memory AI","target":"https://www.perfectmemory.ai/","line":519},{"title":"Hexabot","target":"https://github.com/hexastack/hexabot","line":520},{"title":"Reddit Rate","target":"https://github.com/rapidarchitect/reddit_analyzer","line":521},{"title":"OpenTalkGpt","target":"https://github.com/adarshM84/OpenTalkGpt","line":522},{"title":"VT","target":"https://github.com/vinhnx/vt.ai","line":523},{"title":"Nosia","target":"https://github.com/nosia-ai/nosia","line":524},{"title":"Witsy","target":"https://github.com/nbonamy/witsy","line":525},{"title":"Abbey","target":"https://github.com/US-Artificial-Intelligence/abbey","line":526},{"title":"Minima","target":"https://github.com/dmayboroda/minima","line":527},{"title":"aidful-ollama-model-delete","target":"https://github.com/AidfulAI/aidful-ollama-model-delete","line":528},{"title":"Perplexica","target":"https://github.com/ItzCrazyKns/Perplexica","line":529},{"title":"Ollama Chat WebUI for Docker ","target":"https://github.com/oslook/ollama-webui","line":530},{"title":"AI Toolkit for Visual Studio Code","target":"https://aka.ms/ai-tooklit/ollama-docs","line":531},{"title":"MinimalNextOllamaChat","target":"https://github.com/anilkay/MinimalNextOllamaChat","line":532},{"title":"Chipper","target":"https://github.com/TilmanGriesel/chipper","line":533},{"title":"ChibiChat","target":"https://github.com/CosmicEventHorizon/ChibiChat","line":534},{"title":"LocalLLM","target":"https://github.com/qusaismael/localllm","line":535},{"title":"Ollamazing","target":"https://github.com/buiducnhat/ollamazing","line":536},{"title":"Google Cloud","target":"https://cloud.google.com/run/docs/tutorials/gpu-gemma2-with-ollama","line":540},{"title":"Fly.io","target":"https://fly.io/docs/python/do-more/add-ollama/","line":541},{"title":"Koyeb","target":"https://www.koyeb.com/deploy/ollama","line":542},{"title":"oterm","target":"https://github.com/ggozad/oterm","line":546},{"title":"Ellama Emacs client","target":"https://github.com/s-kostyaev/ellama","line":547},{"title":"Emacs client","target":"https://github.com/zweifisch/ollama","line":548},{"title":"neollama","target":"https://github.com/paradoxical-dev/neollama","line":549},{"title":"gen.nvim","target":"https://github.com/David-Kunz/gen.nvim","line":550},{"title":"ollama.nvim","target":"https://github.com/nomnivore/ollama.nvim","line":551},{"title":"ollero.nvim","target":"https://github.com/marco-souza/ollero.nvim","line":552},{"title":"ollama-chat.nvim","target":"https://github.com/gerazov/ollama-chat.nvim","line":553},{"title":"ogpt.nvim","target":"https://github.com/huynle/ogpt.nvim","line":554},{"title":"gptel Emacs client","target":"https://github.com/karthink/gptel","line":555},{"title":"Oatmeal","target":"https://github.com/dustinblackman/oatmeal","line":556},{"title":"cmdh","target":"https://github.com/pgibler/cmdh","line":557},{"title":"ooo","target":"https://github.com/npahlfer/ooo","line":558},{"title":"shell-pilot","target":"https://github.com/reid41/shell-pilot","line":559},{"title":"tenere","target":"https://github.com/pythops/tenere","line":560},{"title":"llm-ollama","target":"https://github.com/taketwo/llm-ollama","line":561},{"title":"Datasette's LLM CLI","target":"https://llm.datasette.io/en/stable/","line":561},{"title":"typechat-cli","target":"https://github.com/anaisbetts/typechat-cli","line":562},{"title":"ShellOracle","target":"https://github.com/djcopley/ShellOracle","line":563},{"title":"tlm","target":"https://github.com/yusufcanb/tlm","line":564},{"title":"podman-ollama","target":"https://github.com/ericcurtin/podman-ollama","line":565},{"title":"gollama","target":"https://github.com/sammcj/gollama","line":566},{"title":"ParLlama","target":"https://github.com/paulrobello/parllama","line":567},{"title":"Ollama eBook Summary","target":"https://github.com/cognitivetech/ollama-ebook-summary/","line":568},{"title":"Ollama Mixture of Experts (MOE) in 50 lines of code","target":"https://github.com/rapidarchitect/ollama_moe","line":569},{"title":"vim-intelligence-bridge","target":"https://github.com/pepo-ec/vim-intelligence-bridge","line":570},{"title":"x-cmd ollama","target":"https://x-cmd.com/mod/ollama","line":571},{"title":"bb7","target":"https://github.com/drunkwcodes/bb7","line":572},{"title":"SwollamaCLI","target":"https://github.com/marcusziade/Swollama","line":573},{"title":"Demo","target":"https://github.com/marcusziade/Swollama?tab=readme-ov-file#cli-usage","line":573},{"title":"aichat","target":"https://github.com/sigoden/aichat","line":574},{"title":"PowershAI","target":"https://github.com/rrg92/powershai","line":575},{"title":"orbiton","target":"https://github.com/xyproto/orbiton","line":576},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":580},{"title":"pgai","target":"https://github.com/timescale/pgai","line":584},{"title":"Get started guide","target":"https://github.com/timescale/pgai/blob/main/docs/vectorizer-quick-start.md","line":585},{"title":"MindsDB","target":"https://github.com/mindsdb/mindsdb/blob/staging/mindsdb/integrations/handlers/ollama_handler/README.md","line":586},{"title":"chromem-go","target":"https://github.com/philippgille/chromem-go/blob/v0.5.0/embed_ollama.go","line":587},{"title":"example","target":"https://github.com/philippgille/chromem-go/tree/v0.5.0/examples/rag-wikipedia-ollama","line":587},{"title":"Kangaroo","target":"https://github.com/dbkangaroo/kangaroo","line":588},{"title":"Pacman","target":"https://archlinux.org/packages/extra/x86_64/ollama/","line":592},{"title":"Gentoo","target":"https://github.com/gentoo/guru/tree/master/app-misc/ollama","line":593},{"title":"Homebrew","target":"https://formulae.brew.sh/formula/ollama","line":594},{"title":"Helm Chart","target":"https://artifacthub.io/packages/helm/ollama-helm/ollama","line":595},{"title":"Guix channel","target":"https://codeberg.org/tusharhero/ollama-guix","line":596},{"title":"Nix package","target":"https://search.nixos.org/packages?show=ollama&from=0&size=50&sort=relevance&type=packages&query=ollama","line":597},{"title":"Flox","target":"https://flox.dev/blog/ollama-part-one","line":598},{"title":"LangChain.js","target":"https://js.langchain.com/docs/integrations/chat/ollama/","line":602},{"title":"example","target":"https://js.langchain.com/docs/tutorials/local_rag/","line":602},{"title":"LangChain","target":"https://python.langchain.com/docs/integrations/llms/ollama","line":602},{"title":"Firebase Genkit","target":"https://firebase.google.com/docs/genkit/plugins/ollama","line":603},{"title":"crewAI","target":"https://github.com/crewAIInc/crewAI","line":604},{"title":"Yacana","target":"https://remembersoftwares.github.io/yacana/","line":605},{"title":"reference","target":"https://docs.spring.io/spring-ai/reference/api/chat/ollama-chat.html","line":606},{"title":"Spring AI","target":"https://github.com/spring-projects/spring-ai","line":606},{"title":"example","target":"https://github.com/tzolov/ollama-tools","line":606},{"title":"LangChainGo","target":"https://github.com/tmc/langchaingo/","line":607},{"title":"example","target":"https://github.com/tmc/langchaingo/tree/main/examples/ollama-completion-example","line":607},{"title":"LangChain4j","target":"https://github.com/langchain4j/langchain4j","line":608},{"title":"example","target":"https://github.com/langchain4j/langchain4j-examples/tree/main/ollama-examples/src/main/java","line":608},{"title":"LangChainRust","target":"https://github.com/Abraxas-365/langchain-rust","line":609},{"title":"example","target":"https://github.com/Abraxas-365/langchain-rust/blob/main/examples/llm_ollama.rs","line":609},{"title":"LangChain for .NET","target":"https://github.com/tryAGI/LangChain","line":610},{"title":"example","target":"https://github.com/tryAGI/LangChain/blob/main/examples/LangChain.Samples.OpenAI/Program.cs","line":610},{"title":"LLPhant","target":"https://github.com/theodo-group/LLPhant?tab=readme-ov-file#ollama","line":611},{"title":"LlamaIndex","target":"https://docs.llamaindex.ai/en/stable/examples/llm/ollama/","line":612},{"title":"LlamaIndexTS","target":"https://ts.llamaindex.ai/modules/llms/available_llms/ollama","line":612},{"title":"LiteLLM","target":"https://github.com/BerriAI/litellm","line":613},{"title":"OllamaFarm for Go","target":"https://github.com/presbrey/ollamafarm","line":614},{"title":"OllamaSharp for .NET","target":"https://github.com/awaescher/OllamaSharp","line":615},{"title":"Ollama for Ruby","target":"https://github.com/gbaptista/ollama-ai","line":616},{"title":"Ollama-rs for Rust","target":"https://github.com/pepperoni21/ollama-rs","line":617},{"title":"Ollama-hpp for C++","target":"https://github.com/jmont-dev/ollama-hpp","line":618},{"title":"Ollama4j for Java","target":"https://github.com/ollama4j/ollama4j","line":619},{"title":"ModelFusion Typescript Library","target":"https://modelfusion.dev/integration/model-provider/ollama","line":620},{"title":"OllamaKit for Swift","target":"https://github.com/kevinhermawan/OllamaKit","line":621},{"title":"Ollama for Dart","target":"https://github.com/breitburg/dart-ollama","line":622},{"title":"Ollama for Laravel","target":"https://github.com/cloudstudio/ollama-laravel","line":623},{"title":"LangChainDart","target":"https://github.com/davidmigloz/langchain_dart","line":624},{"title":"Semantic Kernel - Python","target":"https://github.com/microsoft/semantic-kernel/tree/main/python/semantic_kernel/connectors/ai/ollama","line":625},{"title":"Haystack","target":"https://github.com/deepset-ai/haystack-integrations/blob/main/integrations/ollama.md","line":626},{"title":"Elixir LangChain","target":"https://github.com/brainlid/langchain","line":627},{"title":"Ollama for R - rollama","target":"https://github.com/JBGruber/rollama","line":628},{"title":"Ollama for R - ollama-r","target":"https://github.com/hauselin/ollama-r","line":629},{"title":"Ollama-ex for Elixir","target":"https://github.com/lebrunel/ollama-ex","line":630},{"title":"Ollama Connector for SAP ABAP","target":"https://github.com/b-tocs/abap_btocs_ollama","line":631},{"title":"Testcontainers","target":"https://testcontainers.com/modules/ollama/","line":632},{"title":"Portkey","target":"https://portkey.ai/docs/welcome/integration-guides/ollama","line":633},{"title":"PromptingTools.jl","target":"https://github.com/svilupp/PromptingTools.jl","line":634},{"title":"example","target":"https://svilupp.github.io/PromptingTools.jl/dev/examples/working_with_ollama","line":634},{"title":"LlamaScript","target":"https://github.com/Project-Llama/llamascript","line":635},{"title":"llm-axe","target":"https://github.com/emirsahin1/llm-axe","line":636},{"title":"Gollm","target":"https://docs.gollm.co/examples/ollama-example","line":637},{"title":"Gollama for Golang","target":"https://github.com/jonathanhecl/gollama","line":638},{"title":"Ollamaclient for Golang","target":"https://github.com/xyproto/ollamaclient","line":639},{"title":"High-level function abstraction in Go","target":"https://gitlab.com/tozd/go/fun","line":640},{"title":"Ollama PHP","target":"https://github.com/ArdaGnsrn/ollama-php","line":641},{"title":"Agents-Flex for Java","target":"https://github.com/agents-flex/agents-flex","line":642},{"title":"example","target":"https://github.com/agents-flex/agents-flex/tree/main/agents-flex-llm/agents-flex-llm-ollama/src/test/java/com/agentsflex/llm/ollama","line":642},{"title":"Parakeet","target":"https://github.com/parakeet-nest/parakeet","line":643},{"title":"Haverscript","target":"https://github.com/andygill/haverscript","line":644},{"title":"examples","target":"https://github.com/andygill/haverscript/tree/main/examples","line":644},{"title":"Ollama for Swift","target":"https://github.com/mattt/ollama-swift","line":645},{"title":"Swollama for Swift","target":"https://github.com/marcusziade/Swollama","line":646},{"title":"DocC","target":"https://marcusziade.github.io/Swollama/documentation/swollama/","line":646},{"title":"GoLamify","target":"https://github.com/prasad89/golamify","line":647},{"title":"Ollama for Haskell","target":"https://github.com/tusharad/ollama-haskell","line":648},{"title":"multi-llm-ts","target":"https://github.com/nbonamy/multi-llm-ts","line":649},{"title":"LlmTornado","target":"https://github.com/lofcz/llmtornado","line":650},{"title":"Ollama for Zig","target":"https://github.com/dravenk/ollama-zig","line":651},{"title":"Abso","target":"https://github.com/lunary-ai/abso","line":652},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":656},{"title":"Maid","target":"https://github.com/Mobile-Artificial-Intelligence/maid","line":657},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":658},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":659},{"title":"Raycast extension","target":"https://github.com/MassimilianoPasquini97/raycast_ollama","line":663},{"title":"Discollama","target":"https://github.com/mxyng/discollama","line":664},{"title":"Continue","target":"https://github.com/continuedev/continue","line":665},{"title":"Vibe","target":"https://github.com/thewh1teagle/vibe","line":666},{"title":"Obsidian Ollama plugin","target":"https://github.com/hinterdupfinger/obsidian-ollama","line":667},{"title":"Logseq Ollama plugin","target":"https://github.com/omagdy7/ollama-logseq","line":668},{"title":"NotesOllama","target":"https://github.com/andersrex/notesollama","line":669},{"title":"Dagger Chatbot","target":"https://github.com/samalba/dagger-chatbot","line":670},{"title":"Discord AI Bot","target":"https://github.com/mekb-turtle/discord-ai-bot","line":671},{"title":"Ollama Telegram Bot","target":"https://github.com/ruecat/ollama-telegram","line":672},{"title":"Hass Ollama Conversation","target":"https://github.com/ej52/hass-ollama-conversation","line":673},{"title":"Rivet plugin","target":"https://github.com/abrenneke/rivet-plugin-ollama","line":674},{"title":"Obsidian BMO Chatbot plugin","target":"https://github.com/longy2k/obsidian-bmo-chatbot","line":675},{"title":"Cliobot","target":"https://github.com/herval/cliobot","line":676},{"title":"Copilot for Obsidian plugin","target":"https://github.com/logancyang/obsidian-copilot","line":677},{"title":"Obsidian Local GPT plugin","target":"https://github.com/pfrankov/obsidian-local-gpt","line":678},{"title":"Open Interpreter","target":"https://docs.openinterpreter.com/language-model-setup/local-models/ollama","line":679},{"title":"Llama Coder","target":"https://github.com/ex3ndr/llama-coder","line":680},{"title":"Ollama Copilot","target":"https://github.com/bernardo-bruning/ollama-copilot","line":681},{"title":"twinny","target":"https://github.com/rjmacarthy/twinny","line":682},{"title":"Wingman-AI","target":"https://github.com/RussellCanfield/wingman-ai","line":683},{"title":"Page Assist","target":"https://github.com/n4ze3m/page-assist","line":684},{"title":"Plasmoid Ollama Control","target":"https://github.com/imoize/plasmoid-ollamacontrol","line":685},{"title":"AI Telegram Bot","target":"https://github.com/tusharhero/aitelegrambot","line":686},{"title":"AI ST Completion","target":"https://github.com/yaroslavyaroslav/OpenAI-sublime-text","line":687},{"title":"Discord-Ollama Chat Bot","target":"https://github.com/kevinthedang/discord-ollama","line":688},{"title":"ChatGPTBox: All in one browser extension","target":"https://github.com/josStorer/chatGPTBox","line":689},{"title":"Integrating Tutorial","target":"https://github.com/josStorer/chatGPTBox/issues/616#issuecomment-1975186467","line":689},{"title":"Discord AI chat/moderation bot","target":"https://github.com/rapmd73/Companion","line":690},{"title":"Headless Ollama","target":"https://github.com/nischalj10/headless-ollama","line":691},{"title":"Terraform AWS Ollama & Open WebUI","target":"https://github.com/xuyangbocn/terraform-aws-self-host-llm","line":692},{"title":"node-red-contrib-ollama","target":"https://github.com/jakubburkiewicz/node-red-contrib-ollama","line":693},{"title":"Local AI Helper","target":"https://github.com/ivostoykov/localAI","line":694},{"title":"vnc-lm","target":"https://github.com/jake83741/vnc-lm","line":695},{"title":"LSP-AI","target":"https://github.com/SilasMarvin/lsp-ai","line":696},{"title":"QodeAssist","target":"https://github.com/Palm1r/QodeAssist","line":697},{"title":"Obsidian Quiz Generator plugin","target":"https://github.com/ECuiDev/obsidian-quiz-generator","line":698},{"title":"AI Summmary Helper plugin","target":"https://github.com/philffm/ai-summary-helper","line":699},{"title":"TextCraft","target":"https://github.com/suncloudsmoon/TextCraft","line":700},{"title":"Alfred Ollama","target":"https://github.com/zeitlings/alfred-ollama","line":701},{"title":"TextLLaMA","target":"https://github.com/adarshM84/TextLLaMA","line":702},{"title":"Simple-Discord-AI","target":"https://github.com/zyphixor/simple-discord-ai","line":703},{"title":"llama.cpp","target":"https://github.com/ggerganov/llama.cpp","line":707},{"title":"Lunary","target":"https://lunary.ai/docs/integrations/ollama","line":710},{"title":"OpenLIT","target":"https://github.com/openlit/openlit","line":711},{"title":"HoneyHive","target":"https://docs.honeyhive.ai/integrations/ollama","line":712},{"title":"Langfuse","target":"https://langfuse.com/docs/integrations/ollama","line":713},{"title":"MLflow Tracing","target":"https://mlflow.org/docs/latest/llms/tracing/index.html#automatic-tracing","line":714}],"blocks":{"##Modelfile":[1,714],"##Modelfile#{1}":[3,44],"##Modelfile#Multiline input":[45,55],"##Modelfile#Multiline input#{1}":[47,55],"##Modelfile#Multimodal models":[56,63],"##Modelfile#Multimodal models#{1}":[58,63],"##Modelfile#Pass the prompt as an argument":[64,714],"##Modelfile#Pass the prompt as an argument#{1}":[66,714],"#---frontmatter---":[71,null]},"last_import":{"mtime":1739861712306,"size":35628,"at":1740449882094,"hash":"0e0dbc0214ce1e6841f61eaabeecb633ff240bee5f3e10ff72deeb5062a42d1c"},"key":"Ollama/How to start Ollama.md"},
"smart_sources:Ollama/How to start Ollama.md": {"path":"Ollama/How to start Ollama.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0840097,-0.0154583,0.01175718,-0.05908568,-0.02130892,0.01259194,-0.06808967,0.01838151,0.01955842,0.04840634,0.00152041,-0.0512108,-0.01221187,0.01215652,-0.01209651,0.03796196,-0.01471152,0.05266899,-0.02827032,0.02846283,0.10680436,-0.05664766,0.03040094,-0.01465497,0.03779001,0.03430468,-0.02533447,-0.00391738,-0.01460441,-0.22979636,0.02117641,0.00592709,-0.00671208,-0.04924385,-0.04132584,-0.03005413,0.00215222,-0.033427,-0.06693023,0.03744541,0.01918138,0.00523827,-0.00443043,-0.01650336,-0.02551741,-0.04213979,-0.07232607,0.08623651,0.05577214,-0.01453731,0.01392204,-0.04289606,-0.0622258,-0.07782418,0.03047274,0.03812618,0.06536196,0.05252496,0.00941796,0.06380875,-0.01477442,0.01360709,-0.19166863,0.14614755,0.05970478,0.00336769,0.0273566,0.03016317,0.00982251,0.03642235,-0.03716363,0.0243216,0.03457056,-0.00592452,-0.02183572,0.01500766,-0.02014002,-0.00189964,0.00927193,-0.01563077,-0.02701315,0.00100805,0.03988992,-0.01638813,-0.03090922,-0.00644032,0.00107,0.00570774,0.0031945,0.00584258,-0.07829396,-0.03415028,0.04983794,-0.00967994,-0.07433103,0.02160853,-0.00453221,0.0054407,-0.05941974,0.10851096,-0.03595931,-0.05098162,0.0521126,-0.08480427,0.05049785,0.01030628,0.00859183,-0.00539315,-0.04494816,0.02951861,0.01637659,0.02753934,-0.02713954,-0.12845889,0.02791879,-0.04507099,-0.03025365,-0.00856348,-0.02683718,-0.06063517,0.04641563,-0.02466256,0.00894608,-0.02370772,0.01229106,0.0009493,0.09254621,0.04144004,0.03408095,0.0159323,0.06752887,0.03308496,-0.03928665,-0.0595946,-0.03104572,0.05204483,0.03852108,0.05254176,-0.01177055,-0.00979435,0.00110529,-0.02464602,0.03950398,-0.10700237,0.04974844,-0.0082857,-0.00476415,0.00941093,-0.07815816,-0.05495971,-0.02460222,0.04539946,-0.03038378,0.00062221,0.04437776,-0.01524537,0.03892555,0.11345969,-0.0750493,0.01595441,-0.09461323,-0.03756648,0.0342963,0.00858478,-0.03248002,-0.05706247,-0.04733286,0.0417234,-0.01684924,-0.04935763,0.02173846,0.05819095,-0.07119546,-0.02401371,0.08051624,-0.03405938,-0.07463649,0.01010954,-0.00890807,0.05010189,0.00720826,-0.06279322,0.01134933,-0.03647645,0.0046378,-0.06584524,-0.00811596,-0.07080135,0.02393421,-0.00712311,-0.06537514,0.02865661,-0.03811041,0.02905548,-0.00892419,-0.01765702,-0.01938618,-0.0132066,0.02373707,-0.0479715,0.07897713,0.01795864,-0.03109428,0.08261676,-0.08706684,-0.0267611,0.02165878,-0.03700676,0.12405227,-0.02112808,-0.0941155,-0.0013777,0.08492399,0.00520032,-0.05536042,0.03042495,0.01928789,0.01715159,0.02182508,0.03939475,0.05149516,-0.02833464,-0.09469889,-0.21434887,0.0031342,0.02487209,0.0424538,0.0452907,-0.10983311,0.05510603,-0.01137095,0.01681514,0.01189419,0.09445234,-0.05721151,0.00296368,-0.00470982,-0.04701865,0.0095935,-0.05049865,0.02169523,0.00441513,0.05166315,-0.00370862,-0.01199772,-0.01446775,-0.07214488,-0.013422,0.00288848,0.16212328,0.08074218,0.05208177,-0.01829761,0.06617952,0.0321051,-0.0650207,-0.0642845,0.00772744,0.00950271,0.04331223,0.06692092,0.02719711,0.04797393,0.02283418,0.03105612,0.0265252,-0.04623069,-0.0340542,-0.01848627,0.00299577,0.02456779,-0.04242783,0.00380371,0.02951565,-0.0226476,0.0026354,0.02390167,-0.04883151,-0.05442718,-0.09514324,-0.01365755,-0.00155817,-0.00089023,0.01943159,0.03216378,0.00518949,-0.05533185,0.02456362,0.00973326,0.01152626,-0.03651876,0.11304424,-0.05021962,0.02045638,0.10846462,0.01283822,0.05495125,0.12291226,-0.02206722,0.00088229,-0.0444904,-0.01732734,0.03333475,0.01068598,-0.02715725,0.03879527,0.03564039,0.01018085,0.0359728,0.0164656,0.0050165,0.06302891,-0.06015664,-0.07868144,0.01202156,0.01258664,0.00335348,0.0676261,0.02595871,-0.27662796,0.01746446,0.04072578,0.06338706,-0.00553278,0.0578871,-0.00172558,-0.01022266,0.01424355,-0.01046276,-0.04957662,0.02681462,0.05163292,0.00936866,-0.00037413,0.04113381,0.01147852,-0.01353443,0.03105723,-0.01759121,0.029311,0.01065333,0.21655816,-0.03848974,0.01302315,0.02654745,-0.01452392,0.02050621,0.08627347,0.02950228,0.04489659,0.00838399,0.07365834,-0.03412892,0.07856907,-0.04507084,-0.04357795,-0.00394881,0.00455649,0.01217675,-0.07620835,0.03146912,0.01353204,-0.01651887,0.03404156,-0.02200923,-0.02445466,-0.00595625,-0.0301011,0.05346623,0.03547424,0.02407816,-0.01020531,0.02385439,-0.00703744,0.05930345,-0.01620906,0.00222314,-0.03845847,-0.02276993,0.01821014,0.02126914,0.048913,0.0020559,-0.01996682],"last_embed":{"hash":"7f656910fc1992e723e3225b9f40bde3cc7fb5ac948aa7b6358d1747cf28d774","tokens":462}}},"last_read":{"hash":"7f656910fc1992e723e3225b9f40bde3cc7fb5ac948aa7b6358d1747cf28d774","at":1745995307495},"class_name":"SmartSource2","outlinks":[{"title":"Download","target":"https://ollama.com/download/Ollama-darwin.zip","line":80},{"title":"Download","target":"https://ollama.com/download/OllamaSetup.exe","line":84},{"title":"Manual install instructions","target":"https://github.com/ollama/ollama/blob/main/docs/linux.md","line":93},{"title":"Ollama Docker image","target":"https://hub.docker.com/r/ollama/ollama","line":98},{"title":"ollama-python","target":"https://github.com/ollama/ollama-python","line":103},{"title":"ollama-js","target":"https://github.com/ollama/ollama-js","line":104},{"title":"Discord","target":"https://discord.gg/ollama","line":110},{"title":"Reddit","target":"https://reddit.com/r/ollama","line":111},{"title":"Llama 3.2","target":"https://ollama.com/library/llama3.2","line":119},{"title":"ollama.com/library","target":"https://ollama.com/library 'ollama model library'","line":134},{"title":"ollama.com/library","target":"https://ollama.com/library `ollama model library","line":136},{"title":"guide","target":"docs/import.md","line":219},{"title":"Modelfile","target":"docs/modelfile.md","line":267},{"title":"developer guide","target":"https://github.com/ollama/ollama/blob/main/docs/development.md","line":384},{"title":"API documentation","target":"./docs/api.md","line":424},{"title":"Open WebUI","target":"https://github.com/open-webui/open-webui","line":430},{"title":"Enchanted (macOS native)","target":"https://github.com/AugustDev/enchanted","line":431},{"title":"Hollama","target":"https://github.com/fmaclen/hollama","line":432},{"title":"Lollms-Webui","target":"https://github.com/ParisNeo/lollms-webui","line":433},{"title":"LibreChat","target":"https://github.com/danny-avila/LibreChat","line":434},{"title":"Bionic GPT","target":"https://github.com/bionic-gpt/bionic-gpt","line":435},{"title":"HTML UI","target":"https://github.com/rtcfirefly/ollama-ui","line":436},{"title":"Saddle","target":"https://github.com/jikkuatwork/saddle","line":437},{"title":"Chatbot UI","target":"https://github.com/ivanfioravanti/chatbot-ollama","line":438},{"title":"Chatbot UI v2","target":"https://github.com/mckaywrigley/chatbot-ui","line":439},{"title":"Typescript UI","target":"https://github.com/ollama-interface/Ollama-Gui?tab=readme-ov-file","line":440},{"title":"Minimalistic React UI for Ollama Models","target":"https://github.com/richawo/minimal-llm-ui","line":441},{"title":"Ollamac","target":"https://github.com/kevinhermawan/Ollamac","line":442},{"title":"big-AGI","target":"https://github.com/enricoros/big-AGI/blob/main/docs/config-local-ollama.md","line":443},{"title":"Cheshire Cat assistant framework","target":"https://github.com/cheshire-cat-ai/core","line":444},{"title":"Amica","target":"https://github.com/semperai/amica","line":445},{"title":"chatd","target":"https://github.com/BruceMacD/chatd","line":446},{"title":"Ollama-SwiftUI","target":"https://github.com/kghandour/Ollama-SwiftUI","line":447},{"title":"Dify.AI","target":"https://github.com/langgenius/dify","line":448},{"title":"MindMac","target":"https://mindmac.app","line":449},{"title":"NextJS Web Interface for Ollama","target":"https://github.com/jakobhoeg/nextjs-ollama-llm-ui","line":450},{"title":"Msty","target":"https://msty.app","line":451},{"title":"Chatbox","target":"https://github.com/Bin-Huang/Chatbox","line":452},{"title":"WinForm Ollama Copilot","target":"https://github.com/tgraupmann/WinForm_Ollama_Copilot","line":453},{"title":"Get Started Doc","target":"https://docs.nextchat.dev/models/ollama","line":454},{"title":"NextChat","target":"https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web","line":454},{"title":"Alpaca WebUI","target":"https://github.com/mmo80/alpaca-webui","line":455},{"title":"OllamaGUI","target":"https://github.com/enoch1118/ollamaGUI","line":456},{"title":"OpenAOE","target":"https://github.com/InternLM/OpenAOE","line":457},{"title":"Odin Runes","target":"https://github.com/leonid20000/OdinRunes","line":458},{"title":"LLM-X","target":"https://github.com/mrdjohnson/llm-x","line":459},{"title":"AnythingLLM (Docker + MacOs/Windows/Linux native app)","target":"https://github.com/Mintplex-Labs/anything-llm","line":460},{"title":"Ollama Basic Chat: Uses HyperDiv Reactive UI","target":"https://github.com/rapidarchitect/ollama_basic_chat","line":461},{"title":"Ollama-chats RPG","target":"https://github.com/drazdra/ollama-chats","line":462},{"title":"IntelliBar","target":"https://intellibar.app/","line":463},{"title":"QA-Pilot","target":"https://github.com/reid41/QA-Pilot","line":464},{"title":"ChatOllama","target":"https://github.com/sugarforever/chat-ollama","line":465},{"title":"CRAG Ollama Chat","target":"https://github.com/Nagi-ovo/CRAG-Ollama-Chat","line":466},{"title":"RAGFlow","target":"https://github.com/infiniflow/ragflow","line":467},{"title":"StreamDeploy","target":"https://github.com/StreamDeploy-DevRel/streamdeploy-llm-app-scaffold","line":468},{"title":"chat","target":"https://github.com/swuecho/chat","line":469},{"title":"Lobe Chat","target":"https://github.com/lobehub/lobe-chat","line":470},{"title":"Integrating Doc","target":"https://lobehub.com/docs/self-hosting/examples/ollama","line":470},{"title":"Ollama RAG Chatbot","target":"https://github.com/datvodinh/rag-chatbot.git","line":471},{"title":"BrainSoup","target":"https://www.nurgo-software.com/products/brainsoup","line":472},{"title":"macai","target":"https://github.com/Renset/macai","line":473},{"title":"RWKV-Runner","target":"https://github.com/josStorer/RWKV-Runner","line":474},{"title":"Ollama Grid Search","target":"https://github.com/dezoito/ollama-grid-search","line":475},{"title":"Olpaka","target":"https://github.com/Otacon/olpaka","line":476},{"title":"OllamaSpring","target":"https://github.com/CrazyNeil/OllamaSpring","line":477},{"title":"LLocal.in","target":"https://github.com/kartikm7/llocal","line":478},{"title":"Shinkai Desktop","target":"https://github.com/dcSpark/shinkai-apps","line":479},{"title":"AiLama","target":"https://github.com/zeyoyt/ailama","line":480},{"title":"Ollama with Google Mesop","target":"https://github.com/rapidarchitect/ollama_mesop/","line":481},{"title":"R2R","target":"https://github.com/SciPhi-AI/R2R","line":482},{"title":"Ollama-Kis","target":"https://github.com/elearningshow/ollama-kis","line":483},{"title":"OpenGPA","target":"https://opengpa.org","line":484},{"title":"Painting Droid","target":"https://github.com/mateuszmigas/painting-droid","line":485},{"title":"Kerlig AI","target":"https://www.kerlig.com/","line":486},{"title":"AI Studio","target":"https://github.com/MindWorkAI/AI-Studio","line":487},{"title":"Sidellama","target":"https://github.com/gyopak/sidellama","line":488},{"title":"LLMStack","target":"https://github.com/trypromptly/LLMStack","line":489},{"title":"BoltAI for Mac","target":"https://boltai.com","line":490},{"title":"Harbor","target":"https://github.com/av/harbor","line":491},{"title":"PyGPT","target":"https://github.com/szczyglis-dev/py-gpt","line":492},{"title":"Alpaca","target":"https://github.com/Jeffser/Alpaca","line":493},{"title":"AutoGPT","target":"https://github.com/Significant-Gravitas/AutoGPT/blob/master/docs/content/platform/ollama.md","line":494},{"title":"Go-CREW","target":"https://www.jonathanhecl.com/go-crew/","line":495},{"title":"PartCAD","target":"https://github.com/openvmp/partcad/","line":496},{"title":"Ollama4j Web UI","target":"https://github.com/ollama4j/ollama4j-web-ui","line":497},{"title":"PyOllaMx","target":"https://github.com/kspviswa/pyOllaMx","line":498},{"title":"Claude Dev","target":"https://github.com/saoudrizwan/claude-dev","line":499},{"title":"Cherry Studio","target":"https://github.com/kangfenmao/cherry-studio","line":500},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":501},{"title":"Archyve","target":"https://github.com/nickthecook/archyve","line":502},{"title":"crewAI with Mesop","target":"https://github.com/rapidarchitect/ollama-crew-mesop","line":503},{"title":"Tkinter-based client","target":"https://github.com/chyok/ollama-gui","line":504},{"title":"LLMChat","target":"https://github.com/trendy-design/llmchat","line":505},{"title":"Local Multimodal AI Chat","target":"https://github.com/Leon-Sander/Local-Multimodal-AI-Chat","line":506},{"title":"ARGO","target":"https://github.com/xark-argo/argo","line":507},{"title":"OrionChat","target":"https://github.com/EliasPereirah/OrionChat","line":508},{"title":"G1","target":"https://github.com/bklieger-groq/g1","line":509},{"title":"Web management","target":"https://github.com/lemonit-eric-mao/ollama-web-management","line":510},{"title":"Promptery","target":"https://github.com/promptery/promptery","line":511},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":512},{"title":"chat-ollama","target":"https://github.com/annilq/chat-ollama","line":513},{"title":"SpaceLlama","target":"https://github.com/tcsenpai/spacellama","line":514},{"title":"YouLama","target":"https://github.com/tcsenpai/youlama","line":515},{"title":"DualMind","target":"https://github.com/tcsenpai/dualmind","line":516},{"title":"ollamarama-matrix","target":"https://github.com/h1ddenpr0cess20/ollamarama-matrix","line":517},{"title":"ollama-chat-app","target":"https://github.com/anan1213095357/ollama-chat-app","line":518},{"title":"Perfect Memory AI","target":"https://www.perfectmemory.ai/","line":519},{"title":"Hexabot","target":"https://github.com/hexastack/hexabot","line":520},{"title":"Reddit Rate","target":"https://github.com/rapidarchitect/reddit_analyzer","line":521},{"title":"OpenTalkGpt","target":"https://github.com/adarshM84/OpenTalkGpt","line":522},{"title":"VT","target":"https://github.com/vinhnx/vt.ai","line":523},{"title":"Nosia","target":"https://github.com/nosia-ai/nosia","line":524},{"title":"Witsy","target":"https://github.com/nbonamy/witsy","line":525},{"title":"Abbey","target":"https://github.com/US-Artificial-Intelligence/abbey","line":526},{"title":"Minima","target":"https://github.com/dmayboroda/minima","line":527},{"title":"aidful-ollama-model-delete","target":"https://github.com/AidfulAI/aidful-ollama-model-delete","line":528},{"title":"Perplexica","target":"https://github.com/ItzCrazyKns/Perplexica","line":529},{"title":"Ollama Chat WebUI for Docker ","target":"https://github.com/oslook/ollama-webui","line":530},{"title":"AI Toolkit for Visual Studio Code","target":"https://aka.ms/ai-tooklit/ollama-docs","line":531},{"title":"MinimalNextOllamaChat","target":"https://github.com/anilkay/MinimalNextOllamaChat","line":532},{"title":"Chipper","target":"https://github.com/TilmanGriesel/chipper","line":533},{"title":"ChibiChat","target":"https://github.com/CosmicEventHorizon/ChibiChat","line":534},{"title":"LocalLLM","target":"https://github.com/qusaismael/localllm","line":535},{"title":"Ollamazing","target":"https://github.com/buiducnhat/ollamazing","line":536},{"title":"Google Cloud","target":"https://cloud.google.com/run/docs/tutorials/gpu-gemma2-with-ollama","line":540},{"title":"Fly.io","target":"https://fly.io/docs/python/do-more/add-ollama/","line":541},{"title":"Koyeb","target":"https://www.koyeb.com/deploy/ollama","line":542},{"title":"oterm","target":"https://github.com/ggozad/oterm","line":546},{"title":"Ellama Emacs client","target":"https://github.com/s-kostyaev/ellama","line":547},{"title":"Emacs client","target":"https://github.com/zweifisch/ollama","line":548},{"title":"neollama","target":"https://github.com/paradoxical-dev/neollama","line":549},{"title":"gen.nvim","target":"https://github.com/David-Kunz/gen.nvim","line":550},{"title":"ollama.nvim","target":"https://github.com/nomnivore/ollama.nvim","line":551},{"title":"ollero.nvim","target":"https://github.com/marco-souza/ollero.nvim","line":552},{"title":"ollama-chat.nvim","target":"https://github.com/gerazov/ollama-chat.nvim","line":553},{"title":"ogpt.nvim","target":"https://github.com/huynle/ogpt.nvim","line":554},{"title":"gptel Emacs client","target":"https://github.com/karthink/gptel","line":555},{"title":"Oatmeal","target":"https://github.com/dustinblackman/oatmeal","line":556},{"title":"cmdh","target":"https://github.com/pgibler/cmdh","line":557},{"title":"ooo","target":"https://github.com/npahlfer/ooo","line":558},{"title":"shell-pilot","target":"https://github.com/reid41/shell-pilot","line":559},{"title":"tenere","target":"https://github.com/pythops/tenere","line":560},{"title":"llm-ollama","target":"https://github.com/taketwo/llm-ollama","line":561},{"title":"Datasette's LLM CLI","target":"https://llm.datasette.io/en/stable/","line":561},{"title":"typechat-cli","target":"https://github.com/anaisbetts/typechat-cli","line":562},{"title":"ShellOracle","target":"https://github.com/djcopley/ShellOracle","line":563},{"title":"tlm","target":"https://github.com/yusufcanb/tlm","line":564},{"title":"podman-ollama","target":"https://github.com/ericcurtin/podman-ollama","line":565},{"title":"gollama","target":"https://github.com/sammcj/gollama","line":566},{"title":"ParLlama","target":"https://github.com/paulrobello/parllama","line":567},{"title":"Ollama eBook Summary","target":"https://github.com/cognitivetech/ollama-ebook-summary/","line":568},{"title":"Ollama Mixture of Experts (MOE) in 50 lines of code","target":"https://github.com/rapidarchitect/ollama_moe","line":569},{"title":"vim-intelligence-bridge","target":"https://github.com/pepo-ec/vim-intelligence-bridge","line":570},{"title":"x-cmd ollama","target":"https://x-cmd.com/mod/ollama","line":571},{"title":"bb7","target":"https://github.com/drunkwcodes/bb7","line":572},{"title":"SwollamaCLI","target":"https://github.com/marcusziade/Swollama","line":573},{"title":"Demo","target":"https://github.com/marcusziade/Swollama?tab=readme-ov-file#cli-usage","line":573},{"title":"aichat","target":"https://github.com/sigoden/aichat","line":574},{"title":"PowershAI","target":"https://github.com/rrg92/powershai","line":575},{"title":"orbiton","target":"https://github.com/xyproto/orbiton","line":576},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":580},{"title":"pgai","target":"https://github.com/timescale/pgai","line":584},{"title":"Get started guide","target":"https://github.com/timescale/pgai/blob/main/docs/vectorizer-quick-start.md","line":585},{"title":"MindsDB","target":"https://github.com/mindsdb/mindsdb/blob/staging/mindsdb/integrations/handlers/ollama_handler/README.md","line":586},{"title":"chromem-go","target":"https://github.com/philippgille/chromem-go/blob/v0.5.0/embed_ollama.go","line":587},{"title":"example","target":"https://github.com/philippgille/chromem-go/tree/v0.5.0/examples/rag-wikipedia-ollama","line":587},{"title":"Kangaroo","target":"https://github.com/dbkangaroo/kangaroo","line":588},{"title":"Pacman","target":"https://archlinux.org/packages/extra/x86_64/ollama/","line":592},{"title":"Gentoo","target":"https://github.com/gentoo/guru/tree/master/app-misc/ollama","line":593},{"title":"Homebrew","target":"https://formulae.brew.sh/formula/ollama","line":594},{"title":"Helm Chart","target":"https://artifacthub.io/packages/helm/ollama-helm/ollama","line":595},{"title":"Guix channel","target":"https://codeberg.org/tusharhero/ollama-guix","line":596},{"title":"Nix package","target":"https://search.nixos.org/packages?show=ollama&from=0&size=50&sort=relevance&type=packages&query=ollama","line":597},{"title":"Flox","target":"https://flox.dev/blog/ollama-part-one","line":598},{"title":"LangChain.js","target":"https://js.langchain.com/docs/integrations/chat/ollama/","line":602},{"title":"example","target":"https://js.langchain.com/docs/tutorials/local_rag/","line":602},{"title":"LangChain","target":"https://python.langchain.com/docs/integrations/llms/ollama","line":602},{"title":"Firebase Genkit","target":"https://firebase.google.com/docs/genkit/plugins/ollama","line":603},{"title":"crewAI","target":"https://github.com/crewAIInc/crewAI","line":604},{"title":"Yacana","target":"https://remembersoftwares.github.io/yacana/","line":605},{"title":"reference","target":"https://docs.spring.io/spring-ai/reference/api/chat/ollama-chat.html","line":606},{"title":"Spring AI","target":"https://github.com/spring-projects/spring-ai","line":606},{"title":"example","target":"https://github.com/tzolov/ollama-tools","line":606},{"title":"LangChainGo","target":"https://github.com/tmc/langchaingo/","line":607},{"title":"example","target":"https://github.com/tmc/langchaingo/tree/main/examples/ollama-completion-example","line":607},{"title":"LangChain4j","target":"https://github.com/langchain4j/langchain4j","line":608},{"title":"example","target":"https://github.com/langchain4j/langchain4j-examples/tree/main/ollama-examples/src/main/java","line":608},{"title":"LangChainRust","target":"https://github.com/Abraxas-365/langchain-rust","line":609},{"title":"example","target":"https://github.com/Abraxas-365/langchain-rust/blob/main/examples/llm_ollama.rs","line":609},{"title":"LangChain for .NET","target":"https://github.com/tryAGI/LangChain","line":610},{"title":"example","target":"https://github.com/tryAGI/LangChain/blob/main/examples/LangChain.Samples.OpenAI/Program.cs","line":610},{"title":"LLPhant","target":"https://github.com/theodo-group/LLPhant?tab=readme-ov-file#ollama","line":611},{"title":"LlamaIndex","target":"https://docs.llamaindex.ai/en/stable/examples/llm/ollama/","line":612},{"title":"LlamaIndexTS","target":"https://ts.llamaindex.ai/modules/llms/available_llms/ollama","line":612},{"title":"LiteLLM","target":"https://github.com/BerriAI/litellm","line":613},{"title":"OllamaFarm for Go","target":"https://github.com/presbrey/ollamafarm","line":614},{"title":"OllamaSharp for .NET","target":"https://github.com/awaescher/OllamaSharp","line":615},{"title":"Ollama for Ruby","target":"https://github.com/gbaptista/ollama-ai","line":616},{"title":"Ollama-rs for Rust","target":"https://github.com/pepperoni21/ollama-rs","line":617},{"title":"Ollama-hpp for C++","target":"https://github.com/jmont-dev/ollama-hpp","line":618},{"title":"Ollama4j for Java","target":"https://github.com/ollama4j/ollama4j","line":619},{"title":"ModelFusion Typescript Library","target":"https://modelfusion.dev/integration/model-provider/ollama","line":620},{"title":"OllamaKit for Swift","target":"https://github.com/kevinhermawan/OllamaKit","line":621},{"title":"Ollama for Dart","target":"https://github.com/breitburg/dart-ollama","line":622},{"title":"Ollama for Laravel","target":"https://github.com/cloudstudio/ollama-laravel","line":623},{"title":"LangChainDart","target":"https://github.com/davidmigloz/langchain_dart","line":624},{"title":"Semantic Kernel - Python","target":"https://github.com/microsoft/semantic-kernel/tree/main/python/semantic_kernel/connectors/ai/ollama","line":625},{"title":"Haystack","target":"https://github.com/deepset-ai/haystack-integrations/blob/main/integrations/ollama.md","line":626},{"title":"Elixir LangChain","target":"https://github.com/brainlid/langchain","line":627},{"title":"Ollama for R - rollama","target":"https://github.com/JBGruber/rollama","line":628},{"title":"Ollama for R - ollama-r","target":"https://github.com/hauselin/ollama-r","line":629},{"title":"Ollama-ex for Elixir","target":"https://github.com/lebrunel/ollama-ex","line":630},{"title":"Ollama Connector for SAP ABAP","target":"https://github.com/b-tocs/abap_btocs_ollama","line":631},{"title":"Testcontainers","target":"https://testcontainers.com/modules/ollama/","line":632},{"title":"Portkey","target":"https://portkey.ai/docs/welcome/integration-guides/ollama","line":633},{"title":"PromptingTools.jl","target":"https://github.com/svilupp/PromptingTools.jl","line":634},{"title":"example","target":"https://svilupp.github.io/PromptingTools.jl/dev/examples/working_with_ollama","line":634},{"title":"LlamaScript","target":"https://github.com/Project-Llama/llamascript","line":635},{"title":"llm-axe","target":"https://github.com/emirsahin1/llm-axe","line":636},{"title":"Gollm","target":"https://docs.gollm.co/examples/ollama-example","line":637},{"title":"Gollama for Golang","target":"https://github.com/jonathanhecl/gollama","line":638},{"title":"Ollamaclient for Golang","target":"https://github.com/xyproto/ollamaclient","line":639},{"title":"High-level function abstraction in Go","target":"https://gitlab.com/tozd/go/fun","line":640},{"title":"Ollama PHP","target":"https://github.com/ArdaGnsrn/ollama-php","line":641},{"title":"Agents-Flex for Java","target":"https://github.com/agents-flex/agents-flex","line":642},{"title":"example","target":"https://github.com/agents-flex/agents-flex/tree/main/agents-flex-llm/agents-flex-llm-ollama/src/test/java/com/agentsflex/llm/ollama","line":642},{"title":"Parakeet","target":"https://github.com/parakeet-nest/parakeet","line":643},{"title":"Haverscript","target":"https://github.com/andygill/haverscript","line":644},{"title":"examples","target":"https://github.com/andygill/haverscript/tree/main/examples","line":644},{"title":"Ollama for Swift","target":"https://github.com/mattt/ollama-swift","line":645},{"title":"Swollama for Swift","target":"https://github.com/marcusziade/Swollama","line":646},{"title":"DocC","target":"https://marcusziade.github.io/Swollama/documentation/swollama/","line":646},{"title":"GoLamify","target":"https://github.com/prasad89/golamify","line":647},{"title":"Ollama for Haskell","target":"https://github.com/tusharad/ollama-haskell","line":648},{"title":"multi-llm-ts","target":"https://github.com/nbonamy/multi-llm-ts","line":649},{"title":"LlmTornado","target":"https://github.com/lofcz/llmtornado","line":650},{"title":"Ollama for Zig","target":"https://github.com/dravenk/ollama-zig","line":651},{"title":"Abso","target":"https://github.com/lunary-ai/abso","line":652},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":656},{"title":"Maid","target":"https://github.com/Mobile-Artificial-Intelligence/maid","line":657},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":658},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":659},{"title":"Raycast extension","target":"https://github.com/MassimilianoPasquini97/raycast_ollama","line":663},{"title":"Discollama","target":"https://github.com/mxyng/discollama","line":664},{"title":"Continue","target":"https://github.com/continuedev/continue","line":665},{"title":"Vibe","target":"https://github.com/thewh1teagle/vibe","line":666},{"title":"Obsidian Ollama plugin","target":"https://github.com/hinterdupfinger/obsidian-ollama","line":667},{"title":"Logseq Ollama plugin","target":"https://github.com/omagdy7/ollama-logseq","line":668},{"title":"NotesOllama","target":"https://github.com/andersrex/notesollama","line":669},{"title":"Dagger Chatbot","target":"https://github.com/samalba/dagger-chatbot","line":670},{"title":"Discord AI Bot","target":"https://github.com/mekb-turtle/discord-ai-bot","line":671},{"title":"Ollama Telegram Bot","target":"https://github.com/ruecat/ollama-telegram","line":672},{"title":"Hass Ollama Conversation","target":"https://github.com/ej52/hass-ollama-conversation","line":673},{"title":"Rivet plugin","target":"https://github.com/abrenneke/rivet-plugin-ollama","line":674},{"title":"Obsidian BMO Chatbot plugin","target":"https://github.com/longy2k/obsidian-bmo-chatbot","line":675},{"title":"Cliobot","target":"https://github.com/herval/cliobot","line":676},{"title":"Copilot for Obsidian plugin","target":"https://github.com/logancyang/obsidian-copilot","line":677},{"title":"Obsidian Local GPT plugin","target":"https://github.com/pfrankov/obsidian-local-gpt","line":678},{"title":"Open Interpreter","target":"https://docs.openinterpreter.com/language-model-setup/local-models/ollama","line":679},{"title":"Llama Coder","target":"https://github.com/ex3ndr/llama-coder","line":680},{"title":"Ollama Copilot","target":"https://github.com/bernardo-bruning/ollama-copilot","line":681},{"title":"twinny","target":"https://github.com/rjmacarthy/twinny","line":682},{"title":"Wingman-AI","target":"https://github.com/RussellCanfield/wingman-ai","line":683},{"title":"Page Assist","target":"https://github.com/n4ze3m/page-assist","line":684},{"title":"Plasmoid Ollama Control","target":"https://github.com/imoize/plasmoid-ollamacontrol","line":685},{"title":"AI Telegram Bot","target":"https://github.com/tusharhero/aitelegrambot","line":686},{"title":"AI ST Completion","target":"https://github.com/yaroslavyaroslav/OpenAI-sublime-text","line":687},{"title":"Discord-Ollama Chat Bot","target":"https://github.com/kevinthedang/discord-ollama","line":688},{"title":"ChatGPTBox: All in one browser extension","target":"https://github.com/josStorer/chatGPTBox","line":689},{"title":"Integrating Tutorial","target":"https://github.com/josStorer/chatGPTBox/issues/616#issuecomment-1975186467","line":689},{"title":"Discord AI chat/moderation bot","target":"https://github.com/rapmd73/Companion","line":690},{"title":"Headless Ollama","target":"https://github.com/nischalj10/headless-ollama","line":691},{"title":"Terraform AWS Ollama & Open WebUI","target":"https://github.com/xuyangbocn/terraform-aws-self-host-llm","line":692},{"title":"node-red-contrib-ollama","target":"https://github.com/jakubburkiewicz/node-red-contrib-ollama","line":693},{"title":"Local AI Helper","target":"https://github.com/ivostoykov/localAI","line":694},{"title":"vnc-lm","target":"https://github.com/jake83741/vnc-lm","line":695},{"title":"LSP-AI","target":"https://github.com/SilasMarvin/lsp-ai","line":696},{"title":"QodeAssist","target":"https://github.com/Palm1r/QodeAssist","line":697},{"title":"Obsidian Quiz Generator plugin","target":"https://github.com/ECuiDev/obsidian-quiz-generator","line":698},{"title":"AI Summmary Helper plugin","target":"https://github.com/philffm/ai-summary-helper","line":699},{"title":"TextCraft","target":"https://github.com/suncloudsmoon/TextCraft","line":700},{"title":"Alfred Ollama","target":"https://github.com/zeitlings/alfred-ollama","line":701},{"title":"TextLLaMA","target":"https://github.com/adarshM84/TextLLaMA","line":702},{"title":"Simple-Discord-AI","target":"https://github.com/zyphixor/simple-discord-ai","line":703},{"title":"llama.cpp","target":"https://github.com/ggerganov/llama.cpp","line":707},{"title":"Lunary","target":"https://lunary.ai/docs/integrations/ollama","line":710},{"title":"OpenLIT","target":"https://github.com/openlit/openlit","line":711},{"title":"HoneyHive","target":"https://docs.honeyhive.ai/integrations/ollama","line":712},{"title":"Langfuse","target":"https://langfuse.com/docs/integrations/ollama","line":713},{"title":"MLflow Tracing","target":"https://mlflow.org/docs/latest/llms/tracing/index.html#automatic-tracing","line":714}],"blocks":{"##Modelfile":[1,717],"##Modelfile#{1}":[3,44],"##Modelfile#Multiline input":[45,55],"##Modelfile#Multiline input#{1}":[47,55],"##Modelfile#Multimodal models":[56,63],"##Modelfile#Multimodal models#{1}":[58,63],"##Modelfile#Pass the prompt as an argument":[64,717],"##Modelfile#Pass the prompt as an argument#{1}":[66,717],"#---frontmatter---":[71,null]},"last_import":{"mtime":1740979704197,"size":35657,"at":1745995300437,"hash":"7f656910fc1992e723e3225b9f40bde3cc7fb5ac948aa7b6358d1747cf28d774"},"key":"Ollama/How to start Ollama.md"},"smart_blocks:Ollama/How to start Ollama.md##Modelfile": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08318888,-0.01265711,0.01248767,-0.06431944,-0.02292555,0.01567599,-0.06726018,0.02466885,0.01874154,0.05208879,-0.00138288,-0.04849686,-0.01737209,0.01446025,-0.01125072,0.03537334,-0.01160355,0.04482955,-0.02745864,0.03038462,0.10890861,-0.05483588,0.02617803,-0.01236462,0.03589323,0.03724245,-0.02296464,-0.00026087,-0.01697801,-0.23282427,0.01986902,0.00719475,-0.00913404,-0.04919629,-0.04229577,-0.03374863,-0.00520237,-0.03607392,-0.06712516,0.03322988,0.02414755,0.00950634,-0.00573557,-0.01707667,-0.0195451,-0.03442677,-0.07274636,0.08776411,0.06066116,-0.01903738,0.01384306,-0.04770349,-0.06438582,-0.07757008,0.03203217,0.04094789,0.06413673,0.04910647,0.00970675,0.06279006,-0.01240752,0.00987132,-0.19369784,0.15046088,0.05901602,0.00712306,0.02194078,0.02913059,0.01536631,0.03336157,-0.03725616,0.02370117,0.03043847,-0.00737456,-0.01768502,0.00796933,-0.01029543,-0.00391177,0.01268455,-0.00961453,-0.02562112,0.0014226,0.04485476,-0.0166877,-0.02692898,-0.00778038,0.0044717,0.00422933,0.00533446,0.00197918,-0.0796387,-0.03432035,0.0481436,-0.0104696,-0.07482221,0.0232547,-0.00693776,0.00657588,-0.05314955,0.11066802,-0.03484553,-0.04923676,0.05371511,-0.08644811,0.04239563,0.00829403,0.00816263,0.00085545,-0.04699687,0.03036728,0.00924031,0.02139737,-0.02754013,-0.12666391,0.02558728,-0.05139163,-0.03110446,-0.00387863,-0.02132666,-0.06583343,0.05122332,-0.02490152,0.01646608,-0.02802057,0.00888269,0.00385372,0.09010919,0.04035882,0.03049606,0.01776382,0.06942752,0.03880364,-0.03387491,-0.05863544,-0.02996678,0.04641215,0.03684812,0.05414402,-0.01212767,-0.0129611,0.00010223,-0.02446828,0.03278743,-0.11200207,0.04634881,-0.00570947,-0.00985464,0.01030748,-0.08019084,-0.05464929,-0.02770983,0.05119791,-0.03169226,0.00544288,0.04306901,-0.01133258,0.04239282,0.10874778,-0.07674111,0.01637677,-0.09026492,-0.0399321,0.03510283,0.01043256,-0.03462384,-0.05206486,-0.04561247,0.04580379,-0.01386083,-0.04811624,0.02051197,0.05448203,-0.06607471,-0.01917103,0.08521806,-0.0384653,-0.07011552,0.0059532,-0.00769244,0.04650854,0.00490465,-0.06559493,0.01545399,-0.03370178,0.00607974,-0.06708784,-0.00526506,-0.07531784,0.02455932,-0.01185185,-0.06196674,0.02824644,-0.03967535,0.03067604,-0.01015846,-0.01984445,-0.01049455,-0.0100819,0.02569174,-0.04957249,0.08230659,0.01879275,-0.03363381,0.08361733,-0.09144501,-0.01860019,0.02084157,-0.03732066,0.12584478,-0.02584313,-0.09315221,0.00149548,0.08944988,-0.00087305,-0.05404839,0.03747367,0.01656462,0.01508708,0.01813437,0.03513296,0.04901304,-0.03097735,-0.09575415,-0.21506576,0.0066803,0.02347235,0.03843264,0.04663737,-0.10662311,0.05466064,-0.01167063,0.02184352,0.01758486,0.09341175,-0.05452547,-0.00312632,-0.0061247,-0.05268092,0.01571781,-0.04296215,0.02187105,0.00471136,0.05005789,-0.00800286,-0.01042499,-0.01544149,-0.07207846,-0.01267755,0.00505596,0.15662232,0.07626742,0.04705647,-0.01480637,0.06934901,0.03275463,-0.06819905,-0.0622448,0.00779822,0.01088741,0.04246992,0.07221171,0.02866411,0.0447154,0.02929913,0.03317845,0.0237636,-0.04318342,-0.03346891,-0.01779656,-0.00108517,0.02196077,-0.04548071,0.00431182,0.02580598,-0.02612216,0.00766072,0.02575406,-0.05192325,-0.06110515,-0.09343062,-0.01242933,-0.00197291,-0.00042297,0.01891992,0.03039379,0.00400858,-0.05334938,0.02256313,0.00537493,0.00750769,-0.03715263,0.11244782,-0.04697548,0.02055389,0.11106277,0.01703552,0.05549883,0.12284929,-0.02080544,-0.00066646,-0.04574754,-0.01529342,0.03220977,0.0077692,-0.02629896,0.04489576,0.03620986,0.00869218,0.03914946,0.01877529,0.00271484,0.06145162,-0.05925643,-0.0745775,0.01041551,0.0122171,0.00352373,0.07117588,0.02333108,-0.27840778,0.01753319,0.04260087,0.05696303,-0.00986354,0.05391042,-0.0023064,-0.01002094,0.01682615,-0.01508324,-0.05075897,0.02610951,0.04883404,0.01117168,-0.00444991,0.04769827,0.0117616,-0.01370374,0.03501474,-0.01671905,0.02647089,0.0101649,0.21722664,-0.03769232,0.01097005,0.02870917,-0.0165703,0.02288898,0.08791044,0.03245053,0.04653722,0.01033833,0.0662772,-0.0322707,0.07660954,-0.04698681,-0.04043354,-0.00180651,0.00932246,0.01221663,-0.07649697,0.02828806,0.01157672,-0.01162889,0.0288373,-0.02460098,-0.0246905,-0.00444277,-0.03113612,0.04667085,0.03272382,0.02538843,-0.00768425,0.02870239,-0.01077496,0.05654743,-0.01882797,-0.00001705,-0.03081107,-0.02295711,0.01656505,0.02322189,0.03985932,0.00605495,-0.01890579],"last_embed":{"hash":"7f656910fc1992e723e3225b9f40bde3cc7fb5ac948aa7b6358d1747cf28d774","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"7f656910fc1992e723e3225b9f40bde3cc7fb5ac948aa7b6358d1747cf28d774","at":1745995306374},"key":"Ollama/How to start Ollama.md##Modelfile","lines":[1,717],"size":35657,"outlinks":[{"title":"Download","target":"https://ollama.com/download/Ollama-darwin.zip","line":80},{"title":"Download","target":"https://ollama.com/download/OllamaSetup.exe","line":84},{"title":"Manual install instructions","target":"https://github.com/ollama/ollama/blob/main/docs/linux.md","line":93},{"title":"Ollama Docker image","target":"https://hub.docker.com/r/ollama/ollama","line":98},{"title":"ollama-python","target":"https://github.com/ollama/ollama-python","line":103},{"title":"ollama-js","target":"https://github.com/ollama/ollama-js","line":104},{"title":"Discord","target":"https://discord.gg/ollama","line":110},{"title":"Reddit","target":"https://reddit.com/r/ollama","line":111},{"title":"Llama 3.2","target":"https://ollama.com/library/llama3.2","line":119},{"title":"ollama.com/library","target":"https://ollama.com/library 'ollama model library'","line":134},{"title":"ollama.com/library","target":"https://ollama.com/library `ollama model library","line":136},{"title":"guide","target":"docs/import.md","line":219},{"title":"Modelfile","target":"docs/modelfile.md","line":267},{"title":"developer guide","target":"https://github.com/ollama/ollama/blob/main/docs/development.md","line":384},{"title":"API documentation","target":"./docs/api.md","line":424},{"title":"Open WebUI","target":"https://github.com/open-webui/open-webui","line":430},{"title":"Enchanted (macOS native)","target":"https://github.com/AugustDev/enchanted","line":431},{"title":"Hollama","target":"https://github.com/fmaclen/hollama","line":432},{"title":"Lollms-Webui","target":"https://github.com/ParisNeo/lollms-webui","line":433},{"title":"LibreChat","target":"https://github.com/danny-avila/LibreChat","line":434},{"title":"Bionic GPT","target":"https://github.com/bionic-gpt/bionic-gpt","line":435},{"title":"HTML UI","target":"https://github.com/rtcfirefly/ollama-ui","line":436},{"title":"Saddle","target":"https://github.com/jikkuatwork/saddle","line":437},{"title":"Chatbot UI","target":"https://github.com/ivanfioravanti/chatbot-ollama","line":438},{"title":"Chatbot UI v2","target":"https://github.com/mckaywrigley/chatbot-ui","line":439},{"title":"Typescript UI","target":"https://github.com/ollama-interface/Ollama-Gui?tab=readme-ov-file","line":440},{"title":"Minimalistic React UI for Ollama Models","target":"https://github.com/richawo/minimal-llm-ui","line":441},{"title":"Ollamac","target":"https://github.com/kevinhermawan/Ollamac","line":442},{"title":"big-AGI","target":"https://github.com/enricoros/big-AGI/blob/main/docs/config-local-ollama.md","line":443},{"title":"Cheshire Cat assistant framework","target":"https://github.com/cheshire-cat-ai/core","line":444},{"title":"Amica","target":"https://github.com/semperai/amica","line":445},{"title":"chatd","target":"https://github.com/BruceMacD/chatd","line":446},{"title":"Ollama-SwiftUI","target":"https://github.com/kghandour/Ollama-SwiftUI","line":447},{"title":"Dify.AI","target":"https://github.com/langgenius/dify","line":448},{"title":"MindMac","target":"https://mindmac.app","line":449},{"title":"NextJS Web Interface for Ollama","target":"https://github.com/jakobhoeg/nextjs-ollama-llm-ui","line":450},{"title":"Msty","target":"https://msty.app","line":451},{"title":"Chatbox","target":"https://github.com/Bin-Huang/Chatbox","line":452},{"title":"WinForm Ollama Copilot","target":"https://github.com/tgraupmann/WinForm_Ollama_Copilot","line":453},{"title":"Get Started Doc","target":"https://docs.nextchat.dev/models/ollama","line":454},{"title":"NextChat","target":"https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web","line":454},{"title":"Alpaca WebUI","target":"https://github.com/mmo80/alpaca-webui","line":455},{"title":"OllamaGUI","target":"https://github.com/enoch1118/ollamaGUI","line":456},{"title":"OpenAOE","target":"https://github.com/InternLM/OpenAOE","line":457},{"title":"Odin Runes","target":"https://github.com/leonid20000/OdinRunes","line":458},{"title":"LLM-X","target":"https://github.com/mrdjohnson/llm-x","line":459},{"title":"AnythingLLM (Docker + MacOs/Windows/Linux native app)","target":"https://github.com/Mintplex-Labs/anything-llm","line":460},{"title":"Ollama Basic Chat: Uses HyperDiv Reactive UI","target":"https://github.com/rapidarchitect/ollama_basic_chat","line":461},{"title":"Ollama-chats RPG","target":"https://github.com/drazdra/ollama-chats","line":462},{"title":"IntelliBar","target":"https://intellibar.app/","line":463},{"title":"QA-Pilot","target":"https://github.com/reid41/QA-Pilot","line":464},{"title":"ChatOllama","target":"https://github.com/sugarforever/chat-ollama","line":465},{"title":"CRAG Ollama Chat","target":"https://github.com/Nagi-ovo/CRAG-Ollama-Chat","line":466},{"title":"RAGFlow","target":"https://github.com/infiniflow/ragflow","line":467},{"title":"StreamDeploy","target":"https://github.com/StreamDeploy-DevRel/streamdeploy-llm-app-scaffold","line":468},{"title":"chat","target":"https://github.com/swuecho/chat","line":469},{"title":"Lobe Chat","target":"https://github.com/lobehub/lobe-chat","line":470},{"title":"Integrating Doc","target":"https://lobehub.com/docs/self-hosting/examples/ollama","line":470},{"title":"Ollama RAG Chatbot","target":"https://github.com/datvodinh/rag-chatbot.git","line":471},{"title":"BrainSoup","target":"https://www.nurgo-software.com/products/brainsoup","line":472},{"title":"macai","target":"https://github.com/Renset/macai","line":473},{"title":"RWKV-Runner","target":"https://github.com/josStorer/RWKV-Runner","line":474},{"title":"Ollama Grid Search","target":"https://github.com/dezoito/ollama-grid-search","line":475},{"title":"Olpaka","target":"https://github.com/Otacon/olpaka","line":476},{"title":"OllamaSpring","target":"https://github.com/CrazyNeil/OllamaSpring","line":477},{"title":"LLocal.in","target":"https://github.com/kartikm7/llocal","line":478},{"title":"Shinkai Desktop","target":"https://github.com/dcSpark/shinkai-apps","line":479},{"title":"AiLama","target":"https://github.com/zeyoyt/ailama","line":480},{"title":"Ollama with Google Mesop","target":"https://github.com/rapidarchitect/ollama_mesop/","line":481},{"title":"R2R","target":"https://github.com/SciPhi-AI/R2R","line":482},{"title":"Ollama-Kis","target":"https://github.com/elearningshow/ollama-kis","line":483},{"title":"OpenGPA","target":"https://opengpa.org","line":484},{"title":"Painting Droid","target":"https://github.com/mateuszmigas/painting-droid","line":485},{"title":"Kerlig AI","target":"https://www.kerlig.com/","line":486},{"title":"AI Studio","target":"https://github.com/MindWorkAI/AI-Studio","line":487},{"title":"Sidellama","target":"https://github.com/gyopak/sidellama","line":488},{"title":"LLMStack","target":"https://github.com/trypromptly/LLMStack","line":489},{"title":"BoltAI for Mac","target":"https://boltai.com","line":490},{"title":"Harbor","target":"https://github.com/av/harbor","line":491},{"title":"PyGPT","target":"https://github.com/szczyglis-dev/py-gpt","line":492},{"title":"Alpaca","target":"https://github.com/Jeffser/Alpaca","line":493},{"title":"AutoGPT","target":"https://github.com/Significant-Gravitas/AutoGPT/blob/master/docs/content/platform/ollama.md","line":494},{"title":"Go-CREW","target":"https://www.jonathanhecl.com/go-crew/","line":495},{"title":"PartCAD","target":"https://github.com/openvmp/partcad/","line":496},{"title":"Ollama4j Web UI","target":"https://github.com/ollama4j/ollama4j-web-ui","line":497},{"title":"PyOllaMx","target":"https://github.com/kspviswa/pyOllaMx","line":498},{"title":"Claude Dev","target":"https://github.com/saoudrizwan/claude-dev","line":499},{"title":"Cherry Studio","target":"https://github.com/kangfenmao/cherry-studio","line":500},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":501},{"title":"Archyve","target":"https://github.com/nickthecook/archyve","line":502},{"title":"crewAI with Mesop","target":"https://github.com/rapidarchitect/ollama-crew-mesop","line":503},{"title":"Tkinter-based client","target":"https://github.com/chyok/ollama-gui","line":504},{"title":"LLMChat","target":"https://github.com/trendy-design/llmchat","line":505},{"title":"Local Multimodal AI Chat","target":"https://github.com/Leon-Sander/Local-Multimodal-AI-Chat","line":506},{"title":"ARGO","target":"https://github.com/xark-argo/argo","line":507},{"title":"OrionChat","target":"https://github.com/EliasPereirah/OrionChat","line":508},{"title":"G1","target":"https://github.com/bklieger-groq/g1","line":509},{"title":"Web management","target":"https://github.com/lemonit-eric-mao/ollama-web-management","line":510},{"title":"Promptery","target":"https://github.com/promptery/promptery","line":511},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":512},{"title":"chat-ollama","target":"https://github.com/annilq/chat-ollama","line":513},{"title":"SpaceLlama","target":"https://github.com/tcsenpai/spacellama","line":514},{"title":"YouLama","target":"https://github.com/tcsenpai/youlama","line":515},{"title":"DualMind","target":"https://github.com/tcsenpai/dualmind","line":516},{"title":"ollamarama-matrix","target":"https://github.com/h1ddenpr0cess20/ollamarama-matrix","line":517},{"title":"ollama-chat-app","target":"https://github.com/anan1213095357/ollama-chat-app","line":518},{"title":"Perfect Memory AI","target":"https://www.perfectmemory.ai/","line":519},{"title":"Hexabot","target":"https://github.com/hexastack/hexabot","line":520},{"title":"Reddit Rate","target":"https://github.com/rapidarchitect/reddit_analyzer","line":521},{"title":"OpenTalkGpt","target":"https://github.com/adarshM84/OpenTalkGpt","line":522},{"title":"VT","target":"https://github.com/vinhnx/vt.ai","line":523},{"title":"Nosia","target":"https://github.com/nosia-ai/nosia","line":524},{"title":"Witsy","target":"https://github.com/nbonamy/witsy","line":525},{"title":"Abbey","target":"https://github.com/US-Artificial-Intelligence/abbey","line":526},{"title":"Minima","target":"https://github.com/dmayboroda/minima","line":527},{"title":"aidful-ollama-model-delete","target":"https://github.com/AidfulAI/aidful-ollama-model-delete","line":528},{"title":"Perplexica","target":"https://github.com/ItzCrazyKns/Perplexica","line":529},{"title":"Ollama Chat WebUI for Docker ","target":"https://github.com/oslook/ollama-webui","line":530},{"title":"AI Toolkit for Visual Studio Code","target":"https://aka.ms/ai-tooklit/ollama-docs","line":531},{"title":"MinimalNextOllamaChat","target":"https://github.com/anilkay/MinimalNextOllamaChat","line":532},{"title":"Chipper","target":"https://github.com/TilmanGriesel/chipper","line":533},{"title":"ChibiChat","target":"https://github.com/CosmicEventHorizon/ChibiChat","line":534},{"title":"LocalLLM","target":"https://github.com/qusaismael/localllm","line":535},{"title":"Ollamazing","target":"https://github.com/buiducnhat/ollamazing","line":536},{"title":"Google Cloud","target":"https://cloud.google.com/run/docs/tutorials/gpu-gemma2-with-ollama","line":540},{"title":"Fly.io","target":"https://fly.io/docs/python/do-more/add-ollama/","line":541},{"title":"Koyeb","target":"https://www.koyeb.com/deploy/ollama","line":542},{"title":"oterm","target":"https://github.com/ggozad/oterm","line":546},{"title":"Ellama Emacs client","target":"https://github.com/s-kostyaev/ellama","line":547},{"title":"Emacs client","target":"https://github.com/zweifisch/ollama","line":548},{"title":"neollama","target":"https://github.com/paradoxical-dev/neollama","line":549},{"title":"gen.nvim","target":"https://github.com/David-Kunz/gen.nvim","line":550},{"title":"ollama.nvim","target":"https://github.com/nomnivore/ollama.nvim","line":551},{"title":"ollero.nvim","target":"https://github.com/marco-souza/ollero.nvim","line":552},{"title":"ollama-chat.nvim","target":"https://github.com/gerazov/ollama-chat.nvim","line":553},{"title":"ogpt.nvim","target":"https://github.com/huynle/ogpt.nvim","line":554},{"title":"gptel Emacs client","target":"https://github.com/karthink/gptel","line":555},{"title":"Oatmeal","target":"https://github.com/dustinblackman/oatmeal","line":556},{"title":"cmdh","target":"https://github.com/pgibler/cmdh","line":557},{"title":"ooo","target":"https://github.com/npahlfer/ooo","line":558},{"title":"shell-pilot","target":"https://github.com/reid41/shell-pilot","line":559},{"title":"tenere","target":"https://github.com/pythops/tenere","line":560},{"title":"llm-ollama","target":"https://github.com/taketwo/llm-ollama","line":561},{"title":"Datasette's LLM CLI","target":"https://llm.datasette.io/en/stable/","line":561},{"title":"typechat-cli","target":"https://github.com/anaisbetts/typechat-cli","line":562},{"title":"ShellOracle","target":"https://github.com/djcopley/ShellOracle","line":563},{"title":"tlm","target":"https://github.com/yusufcanb/tlm","line":564},{"title":"podman-ollama","target":"https://github.com/ericcurtin/podman-ollama","line":565},{"title":"gollama","target":"https://github.com/sammcj/gollama","line":566},{"title":"ParLlama","target":"https://github.com/paulrobello/parllama","line":567},{"title":"Ollama eBook Summary","target":"https://github.com/cognitivetech/ollama-ebook-summary/","line":568},{"title":"Ollama Mixture of Experts (MOE) in 50 lines of code","target":"https://github.com/rapidarchitect/ollama_moe","line":569},{"title":"vim-intelligence-bridge","target":"https://github.com/pepo-ec/vim-intelligence-bridge","line":570},{"title":"x-cmd ollama","target":"https://x-cmd.com/mod/ollama","line":571},{"title":"bb7","target":"https://github.com/drunkwcodes/bb7","line":572},{"title":"SwollamaCLI","target":"https://github.com/marcusziade/Swollama","line":573},{"title":"Demo","target":"https://github.com/marcusziade/Swollama?tab=readme-ov-file#cli-usage","line":573},{"title":"aichat","target":"https://github.com/sigoden/aichat","line":574},{"title":"PowershAI","target":"https://github.com/rrg92/powershai","line":575},{"title":"orbiton","target":"https://github.com/xyproto/orbiton","line":576},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":580},{"title":"pgai","target":"https://github.com/timescale/pgai","line":584},{"title":"Get started guide","target":"https://github.com/timescale/pgai/blob/main/docs/vectorizer-quick-start.md","line":585},{"title":"MindsDB","target":"https://github.com/mindsdb/mindsdb/blob/staging/mindsdb/integrations/handlers/ollama_handler/README.md","line":586},{"title":"chromem-go","target":"https://github.com/philippgille/chromem-go/blob/v0.5.0/embed_ollama.go","line":587},{"title":"example","target":"https://github.com/philippgille/chromem-go/tree/v0.5.0/examples/rag-wikipedia-ollama","line":587},{"title":"Kangaroo","target":"https://github.com/dbkangaroo/kangaroo","line":588},{"title":"Pacman","target":"https://archlinux.org/packages/extra/x86_64/ollama/","line":592},{"title":"Gentoo","target":"https://github.com/gentoo/guru/tree/master/app-misc/ollama","line":593},{"title":"Homebrew","target":"https://formulae.brew.sh/formula/ollama","line":594},{"title":"Helm Chart","target":"https://artifacthub.io/packages/helm/ollama-helm/ollama","line":595},{"title":"Guix channel","target":"https://codeberg.org/tusharhero/ollama-guix","line":596},{"title":"Nix package","target":"https://search.nixos.org/packages?show=ollama&from=0&size=50&sort=relevance&type=packages&query=ollama","line":597},{"title":"Flox","target":"https://flox.dev/blog/ollama-part-one","line":598},{"title":"LangChain.js","target":"https://js.langchain.com/docs/integrations/chat/ollama/","line":602},{"title":"example","target":"https://js.langchain.com/docs/tutorials/local_rag/","line":602},{"title":"LangChain","target":"https://python.langchain.com/docs/integrations/llms/ollama","line":602},{"title":"Firebase Genkit","target":"https://firebase.google.com/docs/genkit/plugins/ollama","line":603},{"title":"crewAI","target":"https://github.com/crewAIInc/crewAI","line":604},{"title":"Yacana","target":"https://remembersoftwares.github.io/yacana/","line":605},{"title":"reference","target":"https://docs.spring.io/spring-ai/reference/api/chat/ollama-chat.html","line":606},{"title":"Spring AI","target":"https://github.com/spring-projects/spring-ai","line":606},{"title":"example","target":"https://github.com/tzolov/ollama-tools","line":606},{"title":"LangChainGo","target":"https://github.com/tmc/langchaingo/","line":607},{"title":"example","target":"https://github.com/tmc/langchaingo/tree/main/examples/ollama-completion-example","line":607},{"title":"LangChain4j","target":"https://github.com/langchain4j/langchain4j","line":608},{"title":"example","target":"https://github.com/langchain4j/langchain4j-examples/tree/main/ollama-examples/src/main/java","line":608},{"title":"LangChainRust","target":"https://github.com/Abraxas-365/langchain-rust","line":609},{"title":"example","target":"https://github.com/Abraxas-365/langchain-rust/blob/main/examples/llm_ollama.rs","line":609},{"title":"LangChain for .NET","target":"https://github.com/tryAGI/LangChain","line":610},{"title":"example","target":"https://github.com/tryAGI/LangChain/blob/main/examples/LangChain.Samples.OpenAI/Program.cs","line":610},{"title":"LLPhant","target":"https://github.com/theodo-group/LLPhant?tab=readme-ov-file#ollama","line":611},{"title":"LlamaIndex","target":"https://docs.llamaindex.ai/en/stable/examples/llm/ollama/","line":612},{"title":"LlamaIndexTS","target":"https://ts.llamaindex.ai/modules/llms/available_llms/ollama","line":612},{"title":"LiteLLM","target":"https://github.com/BerriAI/litellm","line":613},{"title":"OllamaFarm for Go","target":"https://github.com/presbrey/ollamafarm","line":614},{"title":"OllamaSharp for .NET","target":"https://github.com/awaescher/OllamaSharp","line":615},{"title":"Ollama for Ruby","target":"https://github.com/gbaptista/ollama-ai","line":616},{"title":"Ollama-rs for Rust","target":"https://github.com/pepperoni21/ollama-rs","line":617},{"title":"Ollama-hpp for C++","target":"https://github.com/jmont-dev/ollama-hpp","line":618},{"title":"Ollama4j for Java","target":"https://github.com/ollama4j/ollama4j","line":619},{"title":"ModelFusion Typescript Library","target":"https://modelfusion.dev/integration/model-provider/ollama","line":620},{"title":"OllamaKit for Swift","target":"https://github.com/kevinhermawan/OllamaKit","line":621},{"title":"Ollama for Dart","target":"https://github.com/breitburg/dart-ollama","line":622},{"title":"Ollama for Laravel","target":"https://github.com/cloudstudio/ollama-laravel","line":623},{"title":"LangChainDart","target":"https://github.com/davidmigloz/langchain_dart","line":624},{"title":"Semantic Kernel - Python","target":"https://github.com/microsoft/semantic-kernel/tree/main/python/semantic_kernel/connectors/ai/ollama","line":625},{"title":"Haystack","target":"https://github.com/deepset-ai/haystack-integrations/blob/main/integrations/ollama.md","line":626},{"title":"Elixir LangChain","target":"https://github.com/brainlid/langchain","line":627},{"title":"Ollama for R - rollama","target":"https://github.com/JBGruber/rollama","line":628},{"title":"Ollama for R - ollama-r","target":"https://github.com/hauselin/ollama-r","line":629},{"title":"Ollama-ex for Elixir","target":"https://github.com/lebrunel/ollama-ex","line":630},{"title":"Ollama Connector for SAP ABAP","target":"https://github.com/b-tocs/abap_btocs_ollama","line":631},{"title":"Testcontainers","target":"https://testcontainers.com/modules/ollama/","line":632},{"title":"Portkey","target":"https://portkey.ai/docs/welcome/integration-guides/ollama","line":633},{"title":"PromptingTools.jl","target":"https://github.com/svilupp/PromptingTools.jl","line":634},{"title":"example","target":"https://svilupp.github.io/PromptingTools.jl/dev/examples/working_with_ollama","line":634},{"title":"LlamaScript","target":"https://github.com/Project-Llama/llamascript","line":635},{"title":"llm-axe","target":"https://github.com/emirsahin1/llm-axe","line":636},{"title":"Gollm","target":"https://docs.gollm.co/examples/ollama-example","line":637},{"title":"Gollama for Golang","target":"https://github.com/jonathanhecl/gollama","line":638},{"title":"Ollamaclient for Golang","target":"https://github.com/xyproto/ollamaclient","line":639},{"title":"High-level function abstraction in Go","target":"https://gitlab.com/tozd/go/fun","line":640},{"title":"Ollama PHP","target":"https://github.com/ArdaGnsrn/ollama-php","line":641},{"title":"Agents-Flex for Java","target":"https://github.com/agents-flex/agents-flex","line":642},{"title":"example","target":"https://github.com/agents-flex/agents-flex/tree/main/agents-flex-llm/agents-flex-llm-ollama/src/test/java/com/agentsflex/llm/ollama","line":642},{"title":"Parakeet","target":"https://github.com/parakeet-nest/parakeet","line":643},{"title":"Haverscript","target":"https://github.com/andygill/haverscript","line":644},{"title":"examples","target":"https://github.com/andygill/haverscript/tree/main/examples","line":644},{"title":"Ollama for Swift","target":"https://github.com/mattt/ollama-swift","line":645},{"title":"Swollama for Swift","target":"https://github.com/marcusziade/Swollama","line":646},{"title":"DocC","target":"https://marcusziade.github.io/Swollama/documentation/swollama/","line":646},{"title":"GoLamify","target":"https://github.com/prasad89/golamify","line":647},{"title":"Ollama for Haskell","target":"https://github.com/tusharad/ollama-haskell","line":648},{"title":"multi-llm-ts","target":"https://github.com/nbonamy/multi-llm-ts","line":649},{"title":"LlmTornado","target":"https://github.com/lofcz/llmtornado","line":650},{"title":"Ollama for Zig","target":"https://github.com/dravenk/ollama-zig","line":651},{"title":"Abso","target":"https://github.com/lunary-ai/abso","line":652},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":656},{"title":"Maid","target":"https://github.com/Mobile-Artificial-Intelligence/maid","line":657},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":658},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":659},{"title":"Raycast extension","target":"https://github.com/MassimilianoPasquini97/raycast_ollama","line":663},{"title":"Discollama","target":"https://github.com/mxyng/discollama","line":664},{"title":"Continue","target":"https://github.com/continuedev/continue","line":665},{"title":"Vibe","target":"https://github.com/thewh1teagle/vibe","line":666},{"title":"Obsidian Ollama plugin","target":"https://github.com/hinterdupfinger/obsidian-ollama","line":667},{"title":"Logseq Ollama plugin","target":"https://github.com/omagdy7/ollama-logseq","line":668},{"title":"NotesOllama","target":"https://github.com/andersrex/notesollama","line":669},{"title":"Dagger Chatbot","target":"https://github.com/samalba/dagger-chatbot","line":670},{"title":"Discord AI Bot","target":"https://github.com/mekb-turtle/discord-ai-bot","line":671},{"title":"Ollama Telegram Bot","target":"https://github.com/ruecat/ollama-telegram","line":672},{"title":"Hass Ollama Conversation","target":"https://github.com/ej52/hass-ollama-conversation","line":673},{"title":"Rivet plugin","target":"https://github.com/abrenneke/rivet-plugin-ollama","line":674},{"title":"Obsidian BMO Chatbot plugin","target":"https://github.com/longy2k/obsidian-bmo-chatbot","line":675},{"title":"Cliobot","target":"https://github.com/herval/cliobot","line":676},{"title":"Copilot for Obsidian plugin","target":"https://github.com/logancyang/obsidian-copilot","line":677},{"title":"Obsidian Local GPT plugin","target":"https://github.com/pfrankov/obsidian-local-gpt","line":678},{"title":"Open Interpreter","target":"https://docs.openinterpreter.com/language-model-setup/local-models/ollama","line":679},{"title":"Llama Coder","target":"https://github.com/ex3ndr/llama-coder","line":680},{"title":"Ollama Copilot","target":"https://github.com/bernardo-bruning/ollama-copilot","line":681},{"title":"twinny","target":"https://github.com/rjmacarthy/twinny","line":682},{"title":"Wingman-AI","target":"https://github.com/RussellCanfield/wingman-ai","line":683},{"title":"Page Assist","target":"https://github.com/n4ze3m/page-assist","line":684},{"title":"Plasmoid Ollama Control","target":"https://github.com/imoize/plasmoid-ollamacontrol","line":685},{"title":"AI Telegram Bot","target":"https://github.com/tusharhero/aitelegrambot","line":686},{"title":"AI ST Completion","target":"https://github.com/yaroslavyaroslav/OpenAI-sublime-text","line":687},{"title":"Discord-Ollama Chat Bot","target":"https://github.com/kevinthedang/discord-ollama","line":688},{"title":"ChatGPTBox: All in one browser extension","target":"https://github.com/josStorer/chatGPTBox","line":689},{"title":"Integrating Tutorial","target":"https://github.com/josStorer/chatGPTBox/issues/616#issuecomment-1975186467","line":689},{"title":"Discord AI chat/moderation bot","target":"https://github.com/rapmd73/Companion","line":690},{"title":"Headless Ollama","target":"https://github.com/nischalj10/headless-ollama","line":691},{"title":"Terraform AWS Ollama & Open WebUI","target":"https://github.com/xuyangbocn/terraform-aws-self-host-llm","line":692},{"title":"node-red-contrib-ollama","target":"https://github.com/jakubburkiewicz/node-red-contrib-ollama","line":693},{"title":"Local AI Helper","target":"https://github.com/ivostoykov/localAI","line":694},{"title":"vnc-lm","target":"https://github.com/jake83741/vnc-lm","line":695},{"title":"LSP-AI","target":"https://github.com/SilasMarvin/lsp-ai","line":696},{"title":"QodeAssist","target":"https://github.com/Palm1r/QodeAssist","line":697},{"title":"Obsidian Quiz Generator plugin","target":"https://github.com/ECuiDev/obsidian-quiz-generator","line":698},{"title":"AI Summmary Helper plugin","target":"https://github.com/philffm/ai-summary-helper","line":699},{"title":"TextCraft","target":"https://github.com/suncloudsmoon/TextCraft","line":700},{"title":"Alfred Ollama","target":"https://github.com/zeitlings/alfred-ollama","line":701},{"title":"TextLLaMA","target":"https://github.com/adarshM84/TextLLaMA","line":702},{"title":"Simple-Discord-AI","target":"https://github.com/zyphixor/simple-discord-ai","line":703},{"title":"llama.cpp","target":"https://github.com/ggerganov/llama.cpp","line":707},{"title":"Lunary","target":"https://lunary.ai/docs/integrations/ollama","line":710},{"title":"OpenLIT","target":"https://github.com/openlit/openlit","line":711},{"title":"HoneyHive","target":"https://docs.honeyhive.ai/integrations/ollama","line":712},{"title":"Langfuse","target":"https://langfuse.com/docs/integrations/ollama","line":713},{"title":"MLflow Tracing","target":"https://mlflow.org/docs/latest/llms/tracing/index.html#automatic-tracing","line":714}],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md##Modelfile#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08158027,-0.00285335,0.03128005,-0.07779751,-0.01142341,0.01901243,-0.08405648,0.01141052,0.01342998,0.03900879,0.00117834,-0.04467852,-0.01372849,0.01034443,-0.00543612,0.02490654,-0.02366113,0.05548102,-0.01083241,0.04175672,0.10451176,-0.08230831,0.00050489,-0.00249664,0.01744526,0.05596367,-0.02024751,0.01664224,-0.0055271,-0.22884639,0.02125784,-0.01770245,-0.03093078,-0.0452967,-0.03482467,-0.02963836,0.00838227,-0.05210502,-0.06799123,0.03066645,0.03216383,0.01562957,0.00332637,-0.0056743,-0.01739541,-0.01362812,-0.05424861,0.10143,0.06216986,-0.01730046,-0.0012007,-0.03964066,-0.07812774,-0.06653539,0.03794155,0.05356989,0.07196744,0.04532576,0.00716062,0.05869133,-0.02391437,0.00917411,-0.19992016,0.14254436,0.05791059,0.02316185,0.01516914,0.0142707,0.01948194,0.03722804,-0.04846262,0.03670993,0.03010292,0.01984709,-0.03353154,0.02600216,-0.02425068,-0.0153748,0.03389722,-0.00427201,-0.03171506,-0.02354642,0.05088364,-0.00699249,-0.04421189,-0.02500121,0.02287014,0.00410803,-0.00232651,0.0073438,-0.08056632,-0.02467169,0.06238914,-0.00037308,-0.08271665,0.03201105,0.00427181,0.01806511,-0.05465448,0.11670249,-0.02635295,-0.04170667,0.02767951,-0.06840989,0.03670883,0.00928964,0.00672635,0.00024231,-0.01752685,0.04042408,0.01228452,0.0364265,-0.04472861,-0.13081713,0.03942744,-0.04787582,-0.02248675,-0.01431061,-0.04808159,-0.05159526,0.05493536,-0.01906747,0.03362129,-0.02994876,0.00827336,-0.01498346,0.08326811,0.04213053,0.01535186,0.01943124,0.06704649,0.02041797,-0.03192286,-0.06154539,-0.02718939,0.04867001,0.03839064,0.05773995,0.00306997,-0.01466402,0.00532896,-0.01982947,0.01488996,-0.10878097,0.05755937,0.00540983,-0.00775677,-0.00468977,-0.0839151,-0.03701663,-0.02170196,0.03488576,-0.03042677,-0.0184906,0.04515419,0.01065031,0.0327728,0.09733299,-0.07799184,0.00819025,-0.10028543,-0.04557361,0.04624529,0.02069929,-0.02203193,-0.03598334,-0.04313967,0.06197214,-0.01820972,-0.05060604,0.02806936,0.06009936,-0.07852623,-0.01095975,0.08301425,-0.04312418,-0.05538382,-0.0006374,-0.0163152,0.04436027,0.00050935,-0.05925334,0.02395772,-0.04609949,0.0019757,-0.07932999,-0.00195264,-0.08197517,0.01336185,0.00855995,-0.06696028,0.04316093,-0.02215576,0.02421103,0.00563508,-0.01188324,0.00074267,-0.00051137,0.01255457,-0.0424907,0.06898999,0.0287128,-0.04153053,0.08314358,-0.10134909,-0.00059752,0.01864626,-0.04308509,0.10084505,-0.02246829,-0.08220289,-0.00603395,0.07418365,0.00426867,-0.06904653,0.0479986,0.001817,-0.00135876,0.01630337,0.02874339,0.05221512,-0.01413721,-0.06137734,-0.19940212,0.00574747,0.0054937,0.02517222,0.04391577,-0.10390554,0.04912653,-0.01356732,-0.01304876,0.01003006,0.07357456,-0.06951341,0.01113463,0.00699765,-0.05908622,0.00919393,-0.03433574,0.00787503,0.00623587,0.07020386,-0.01309031,-0.03992519,-0.03049206,-0.05333955,-0.03401345,0.00833388,0.14716841,0.06264773,0.06481266,-0.02861344,0.06595842,0.00501045,-0.07717171,-0.07160092,-0.01573449,-0.0020315,0.05400811,0.07928924,0.04752396,0.04766858,0.03574462,0.0484754,0.01423841,-0.02353626,-0.04800687,-0.02240366,-0.00118421,0.02570542,-0.05000187,0.01275047,-0.00045487,-0.01865138,0.02532678,-0.00444556,-0.06264752,-0.06530958,-0.09261253,0.00642337,0.00237426,0.01116459,0.01468744,0.03358416,0.00032874,-0.05282612,0.01933357,-0.01658227,0.0212282,-0.0505683,0.10956679,-0.05531871,0.01918795,0.10003329,0.00440975,0.05547694,0.12723781,-0.02922055,-0.01739752,-0.06307579,-0.02464424,0.03496911,0.01771029,-0.02867474,0.05989452,0.01592034,0.01071515,0.05075102,0.02075044,0.01243325,0.05294297,-0.05007204,-0.07916984,0.02170549,-0.01409907,0.01909706,0.07932728,0.03498684,-0.26727587,0.03410415,0.05357894,0.04909132,-0.01666876,0.01856003,0.0054854,-0.01523306,0.01863337,-0.0292468,-0.05226297,0.01786975,0.05386513,0.03944397,-0.00377903,0.02928393,0.01730002,-0.01905988,0.04600161,-0.01455434,0.03603103,0.01637722,0.2181927,-0.01696088,0.0078095,0.03605625,-0.01293304,0.0253689,0.0753044,0.03545566,0.0417802,0.01432628,0.04843714,-0.00386232,0.08807807,-0.04950426,-0.00914765,0.00115813,0.02223548,0.01192411,-0.0775553,0.02780885,0.00986194,0.01134589,0.03032885,-0.02129498,-0.03344952,-0.01730031,-0.04250776,0.05876537,0.01805839,0.02321631,-0.00226347,0.04526403,-0.02157793,0.05429474,-0.04019184,-0.00594981,-0.02108806,-0.02520484,0.01550731,0.0165205,0.03816036,0.01382439,-0.0040834],"last_embed":{"hash":"28b4295aafce59fd7bc05055004851980a068025d919dbcb8a85df87930ba546","tokens":304}}},"text":null,"length":0,"last_read":{"hash":"28b4295aafce59fd7bc05055004851980a068025d919dbcb8a85df87930ba546","at":1745995306639},"key":"Ollama/How to start Ollama.md##Modelfile#{1}","lines":[3,44],"size":754,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md##Modelfile#Multiline input": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06925251,-0.03996227,-0.00626054,-0.05688188,-0.03488276,-0.00242596,-0.0783987,0.05339887,0.00416755,0.03340965,-0.00692467,-0.01998872,-0.00977892,0.04862978,-0.01016567,0.00602015,-0.04035768,0.03843927,-0.08365025,0.01762813,0.11308167,0.02435847,-0.00573625,0.0173517,0.06063135,0.03959575,-0.05042843,-0.05265153,-0.04429185,-0.21112993,-0.00347153,0.00742632,0.01771543,-0.04350352,-0.04382528,-0.02411512,-0.02626751,-0.0178604,-0.02972319,0.02271055,0.02406313,-0.01218831,-0.01787533,-0.05624427,0.04430474,-0.06945212,-0.03531083,0.05423689,0.0694712,-0.02180528,0.00152151,0.00925701,-0.04029109,-0.09359559,0.05873746,-0.00481177,0.01614845,0.03320541,0.0062716,0.04265115,0.02874073,0.02982355,-0.19440913,0.13153677,0.00461281,0.01615279,-0.00243996,0.04829405,0.03609903,0.07801838,-0.0419077,-0.00837622,0.00242219,0.0315285,0.00292154,-0.01471104,0.01630743,-0.02603055,0.00345617,0.01614611,-0.0196746,-0.01438983,0.01754037,-0.02143154,-0.02295994,0.05673757,0.00780056,0.01207105,-0.00712491,-0.02390004,-0.02947323,-0.08062046,0.02997086,-0.00339232,-0.09781148,0.00112567,0.02349988,-0.03597813,-0.05621626,0.12378982,-0.0433674,-0.02436508,0.04212814,-0.07842014,-0.00086574,0.03758518,-0.0466445,-0.00496687,-0.05791358,-0.0176736,0.00308274,-0.00289589,0.0178432,-0.04014853,0.01514293,0.01183922,-0.02098227,-0.00007647,-0.01110376,-0.01979683,0.01277006,-0.04238785,-0.03011607,0.01818528,0.03959936,-0.01532974,0.06812109,0.03844266,0.04224198,0.04815743,0.05294282,0.04842539,-0.07427873,-0.02095622,-0.04334588,0.01523259,0.03136185,0.04210071,-0.01984276,-0.03242924,-0.02073593,-0.04762248,-0.02358045,-0.0676377,-0.02221189,0.04203334,-0.01541187,-0.04248436,-0.01307966,-0.0524042,-0.00014925,0.06485089,-0.03367237,0.00467302,0.07340972,0.00605376,0.10151349,0.03794129,-0.05300278,0.04536178,-0.10265853,-0.00321462,-0.01981816,0.09399842,-0.03193963,-0.09252905,-0.05611182,-0.00537161,-0.0235878,-0.04432565,0.03696437,0.04344878,-0.03687083,0.00071944,0.05475046,-0.01344925,-0.05751893,-0.00598869,0.0015278,0.01081527,0.0322414,-0.04503356,0.00175212,-0.00437086,0.02352569,-0.05506977,-0.02142826,-0.07671437,0.01412405,0.00499365,-0.058117,0.02237388,-0.02026305,0.01731171,-0.05118692,-0.0236081,-0.00532709,-0.03806712,0.04364936,-0.09285346,0.10526955,0.04228593,-0.05188023,0.06232303,-0.03862487,0.00046317,0.03576792,-0.02609941,0.11540022,-0.0560494,-0.07772975,0.0031442,0.11097514,-0.00612417,-0.0465856,0.0212498,0.03178965,0.0232884,0.02530877,0.06077486,0.02550629,-0.07200274,-0.07150891,-0.21305963,0.01228072,0.03696756,-0.02238563,0.01149259,-0.08685821,0.03567098,-0.03918231,-0.01726114,0.08678381,0.13826321,0.00219962,0.01793336,-0.03282951,-0.0483233,0.03666416,-0.0496176,-0.03000622,-0.00136657,0.04909649,0.00292112,0.02099388,0.00358185,-0.06872882,0.04539803,-0.01918736,0.14636467,0.06385162,0.09524216,0.01141111,0.05387977,0.03506028,-0.05367615,-0.06867538,0.02200058,0.00505834,-0.01750885,0.0757583,0.01623198,0.00800672,0.04706392,0.00843332,0.03639598,-0.05149829,0.01139847,-0.01747317,-0.00224513,-0.03779754,-0.03755813,-0.00133924,0.01928231,-0.00867519,0.02573696,0.06211452,-0.04993016,-0.03735574,-0.08211037,0.01634942,-0.03303295,-0.06470448,0.04568724,0.0295248,-0.00305642,-0.06136931,0.00573311,0.04326022,0.01132415,-0.03937682,0.09483502,-0.02566356,0.01967352,0.0630528,-0.00586157,0.10785616,0.07287993,0.04705023,-0.00890404,-0.02904762,-0.05868797,0.01673573,-0.01130133,0.00463236,0.05422956,0.05416225,0.01955344,0.03546564,0.01529533,-0.0384043,0.09494564,-0.04021437,-0.02213387,-0.02621758,-0.0145545,0.04161451,0.05896713,-0.03957164,-0.24579088,0.03511623,0.05197844,0.0980209,-0.05024759,0.11407202,0.04023673,-0.04358732,-0.03166528,-0.0080821,-0.03325338,0.02916482,0.00014395,-0.0025136,0.02223356,0.03872177,0.00882887,-0.01364828,0.05825126,-0.00088414,0.0407977,0.01143718,0.18593672,-0.04901265,0.03141849,0.02528312,0.00727021,0.00857051,0.12320786,0.02297673,0.05810118,0.00011772,0.08570164,-0.02553609,0.06939132,-0.03667624,0.00325658,0.0310217,0.0078693,-0.05935748,-0.08969858,0.03538324,-0.03376237,-0.06990931,-0.01470889,0.01631405,-0.01994687,0.00531597,-0.08217888,0.02204574,0.01912231,0.02542289,-0.01933223,0.00845575,0.00534658,0.06065639,0.00730206,-0.00541052,-0.04180665,-0.00279258,0.04495464,-0.02706593,0.07177435,0.00366674,0.00277911],"last_embed":{"hash":"bc489b6748709aa4423d8e77ab1bfeef4d34eb4485e00f68c3aa6075ba8e5c1f","tokens":85}}},"text":null,"length":0,"last_read":{"hash":"bc489b6748709aa4423d8e77ab1bfeef4d34eb4485e00f68c3aa6075ba8e5c1f","at":1745995306786},"key":"Ollama/How to start Ollama.md##Modelfile#Multiline input","lines":[45,55],"size":197,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md##Modelfile#Multiline input#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07231726,-0.038679,-0.00711496,-0.05157393,-0.0400582,-0.00461678,-0.07331989,0.04981683,0.00195246,0.02631656,-0.00720604,-0.01146379,-0.00768649,0.05322342,-0.0100335,0.0090852,-0.04460513,0.03269269,-0.08947297,0.0176012,0.1117604,0.03591281,-0.00432182,0.00873506,0.06059033,0.03976403,-0.05482124,-0.05532667,-0.04461019,-0.20627339,-0.00694385,0.01216179,0.02643599,-0.04246822,-0.04719501,-0.0180376,-0.02438945,-0.00825288,-0.03695183,0.02399081,0.02566395,-0.01182673,-0.01856741,-0.05831149,0.04281531,-0.07523903,-0.03075573,0.04892173,0.06671728,-0.01592493,0.00988303,0.01705204,-0.03584255,-0.09100102,0.05519845,-0.0109302,0.01541727,0.03651088,0.01214453,0.03452244,0.02421598,0.02404066,-0.19222264,0.12893,0.00280223,0.01370462,-0.00318364,0.04440197,0.03981068,0.07874254,-0.03854704,-0.0127142,0.00108606,0.03335381,0.00668203,-0.02138308,0.01166537,-0.02047385,0.00358505,0.01713325,-0.01107217,-0.0217664,0.01502345,-0.02198882,-0.02341277,0.05420413,0.01215469,0.00995146,-0.00151411,-0.01876685,-0.03308798,-0.08264669,0.03024128,0.00219681,-0.09358244,0.00093726,0.02397561,-0.03392002,-0.05782409,0.12212238,-0.04749864,-0.02134294,0.03534627,-0.08175022,0.00137698,0.03242799,-0.04163718,0.00141666,-0.06260057,-0.02536724,-0.00163516,-0.00565365,0.01817158,-0.03738903,0.0128165,0.00726208,-0.01304417,0.00262411,-0.00408909,-0.01748502,0.01132062,-0.04772376,-0.03340945,0.0147572,0.04447136,-0.01620572,0.06419256,0.03920144,0.03625253,0.04671765,0.06147837,0.04465135,-0.07990335,-0.01569935,-0.04330943,0.01438805,0.03018587,0.04228434,-0.02098145,-0.03738832,-0.01682028,-0.05052166,-0.01314391,-0.06523219,-0.01868865,0.04241829,-0.02431019,-0.03628477,-0.01471464,-0.0584694,0.00021713,0.06006537,-0.03099881,0.00404813,0.06629834,0.00633339,0.10819634,0.03410886,-0.04502289,0.04242364,-0.1030092,-0.0026924,-0.02486427,0.08644813,-0.030505,-0.08407474,-0.07354106,-0.00765694,-0.0211868,-0.04001482,0.03298997,0.03636751,-0.04213705,-0.00333573,0.04453935,-0.01771636,-0.06342085,0.00366995,0.00862572,0.01737287,0.03486937,-0.04610695,0.00637676,-0.00494778,0.03051185,-0.05328782,-0.02060794,-0.07347352,0.01438217,0.01073762,-0.05994627,0.0229694,-0.02454954,0.01968974,-0.05025771,-0.02394204,-0.00166488,-0.03145992,0.04244271,-0.0872055,0.10341865,0.04233081,-0.05123116,0.06395783,-0.03705559,-0.00625127,0.02976711,-0.02386066,0.1209983,-0.06727254,-0.07814493,0.01270723,0.11495189,-0.01268221,-0.04548154,0.01519141,0.02968111,0.02059301,0.01514268,0.05988287,0.03144559,-0.07359911,-0.07954857,-0.21958552,0.01094059,0.04469098,-0.01697788,0.02279599,-0.08980519,0.03243137,-0.03548054,-0.01213231,0.09424091,0.13246362,0.00176422,0.02232976,-0.03223488,-0.0461754,0.03808507,-0.04358345,-0.03038327,0.00454217,0.05227999,-0.00921587,0.01822068,0.00637204,-0.06873039,0.04318606,-0.01031144,0.1487921,0.06465323,0.09481551,0.00403589,0.04666084,0.03365816,-0.05470318,-0.06919375,0.02215615,0.0121983,-0.01701529,0.07323684,0.01861358,0.01108174,0.04033982,0.00837294,0.02841026,-0.05141534,0.01147761,-0.01407693,-0.00610689,-0.0468008,-0.03656856,0.00176849,0.02224525,-0.00827346,0.02062611,0.05946822,-0.04126234,-0.03737294,-0.08253965,0.01792475,-0.03811003,-0.06749255,0.04225691,0.0217351,-0.01285438,-0.06159765,0.0105069,0.04549715,0.01733705,-0.03039265,0.09841082,-0.02527733,0.01635424,0.06749117,-0.00801054,0.1105525,0.07629236,0.05160787,-0.0102326,-0.03300819,-0.06406961,0.00653556,-0.00978076,0.00277136,0.05316406,0.05118838,0.01790882,0.03759456,0.02624185,-0.03978382,0.1023277,-0.03513952,-0.01186319,-0.02045301,-0.01703148,0.04318267,0.05560619,-0.04242714,-0.24365108,0.03011113,0.05566601,0.08826321,-0.04844831,0.11908448,0.04326227,-0.04451799,-0.0266207,-0.00810416,-0.02639452,0.03504472,-0.00631785,0.00507191,0.01560007,0.04273887,0.01888091,-0.01265928,0.05056355,-0.00729877,0.03836589,0.00778966,0.18500291,-0.05654235,0.02744154,0.01696624,0.00615455,0.00161456,0.12417316,0.01829159,0.05761297,-0.00390228,0.08946054,-0.02883269,0.07310892,-0.031367,0.00116225,0.030898,-0.00044591,-0.05811085,-0.09529268,0.03967091,-0.0336563,-0.07473142,-0.01633063,0.02435797,-0.01091827,0.0068955,-0.07349011,0.02180491,0.0190408,0.02839449,-0.0114862,0.00474101,0.00584832,0.06261601,0.00746609,-0.00832479,-0.04119649,0.00118268,0.04370743,-0.02871954,0.07671247,0.00622128,0.00000313],"last_embed":{"hash":"d61bd48fb74b84d1713b494ae89f1f94727a51615805aa7a252ba08e60da9a8a","tokens":83}}},"text":null,"length":0,"last_read":{"hash":"d61bd48fb74b84d1713b494ae89f1f94727a51615805aa7a252ba08e60da9a8a","at":1745995306823},"key":"Ollama/How to start Ollama.md##Modelfile#Multiline input#{1}","lines":[47,55],"size":176,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md##Modelfile#Multimodal models": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07467727,-0.01351528,0.0275436,-0.0151257,-0.00885753,0.04403437,-0.04954659,0.01339901,0.01014447,0.04178225,0.02661452,-0.07818769,-0.02448641,0.01804367,0.01293317,0.03636932,-0.00579277,0.0255373,-0.03321636,0.03020384,0.08380232,-0.02246129,0.06845085,-0.03392098,0.06686363,0.01958985,-0.01938845,-0.01262061,-0.0057014,-0.23191528,0.01433335,0.04832829,0.03393777,-0.04753859,-0.02763441,-0.02792039,-0.03050999,0.02716625,-0.03590308,0.00363221,0.0083986,-0.03653172,-0.06915047,-0.0648414,-0.01098052,-0.07093245,-0.05550222,0.06472902,0.0170907,-0.05185154,0.04521088,-0.08312608,-0.00677107,-0.07557427,0.00216785,0.01859786,0.07285236,0.03698805,-0.00320847,0.06286072,-0.01266869,0.02193748,-0.14142384,0.1340977,-0.00051732,-0.01497982,0.02616725,0.04060416,0.01838093,0.04796853,-0.01419527,-0.00971118,-0.002277,-0.04094716,-0.00426458,-0.04290729,0.00023507,0.02532429,-0.02903685,-0.03834019,-0.03655464,0.03234626,-0.01673002,-0.00821545,-0.01136635,0.01173949,-0.03797095,-0.02648081,0.00506333,-0.01654656,-0.01853716,-0.03295882,-0.00457076,-0.01711688,-0.08270238,0.01796168,-0.01293659,-0.00454365,0.00576816,0.15335646,-0.04844322,-0.04319436,0.10587022,-0.06366862,0.04797258,0.01143907,-0.01431088,-0.00131148,-0.06630702,0.02038184,0.03311375,0.01224938,0.01151359,-0.10407756,0.01571835,-0.06818892,-0.03787518,-0.00151561,0.00639954,-0.04390797,0.01542368,-0.02556278,0.00292318,-0.0259165,-0.01670111,0.01453846,0.08068952,0.00490604,0.05266199,0.003763,0.01678739,0.07845831,-0.01590772,-0.02298981,-0.04065143,0.01166709,0.05064257,0.00345141,-0.01921504,0.01343306,-0.02617141,-0.02695627,0.04143714,-0.08758584,-0.01918462,0.01486659,-0.03671364,0.01638745,-0.05216152,-0.05241994,-0.01638284,0.06392559,-0.02665704,0.04923134,0.05882968,-0.04159894,0.02808582,0.09781867,-0.09283171,0.03259419,-0.02679221,-0.04643779,-0.01872574,0.05652333,-0.07201094,-0.09331312,-0.00858758,-0.00358956,-0.01650609,0.01424662,0.03098386,0.03515156,-0.02805198,-0.00652019,0.09940805,0.00812487,-0.06680904,0.01285799,-0.02320389,0.03048896,0.02669479,-0.05995035,-0.02723127,-0.01552993,-0.01011892,-0.03484278,0.03254623,-0.04664931,0.03258885,-0.04149648,-0.06343,0.00115905,-0.0111947,0.00333835,-0.05201614,-0.04623876,-0.05436507,-0.0297499,0.04566783,-0.08674888,0.05578514,0.01376066,-0.01352801,0.07415944,0.00501815,-0.03113228,0.00810041,-0.01815206,0.1097895,-0.00150152,-0.13170919,-0.00273396,0.05931291,-0.00612488,-0.01494984,0.02138886,0.04375605,0.05918192,0.00054139,0.03614559,0.01821694,-0.04753936,-0.1317856,-0.21830161,0.01310213,0.01934027,0.04011349,0.02985227,-0.11764091,0.05775274,0.01501222,0.0790032,0.05235314,0.09373224,-0.02446037,-0.03733361,-0.04078411,-0.00871633,0.00020901,-0.02036058,0.00977433,0.00522647,0.02563718,0.03295112,0.00429977,0.01020538,-0.07681308,0.01908106,-0.02799656,0.1730933,0.0821162,0.01552387,-0.0040183,0.01537445,0.07568807,-0.10547702,-0.0569615,0.05161664,0.02724577,0.02837217,0.0293963,-0.01102464,0.0007933,0.04113538,0.02370098,0.01887983,-0.06184512,-0.01126828,-0.00748466,0.03788782,-0.02503611,-0.03765985,-0.01846456,0.06136555,0.00413777,0.00225018,0.08540048,-0.05011912,-0.05128402,-0.10526677,-0.04566959,-0.02020555,-0.01187278,0.01356645,0.03222279,0.00162,-0.06629139,0.0307707,0.0429905,-0.00559805,-0.05432081,0.08863623,-0.01294059,0.00937492,0.13071947,-0.0124271,0.04515642,0.08934042,0.01199178,0.04081757,-0.02035304,-0.02966172,0.01194489,-0.01644372,-0.01512221,0.00637813,0.0391654,0.0036029,0.01407546,0.00859148,-0.0508879,0.02174539,-0.04337754,-0.02064222,0.00523018,0.06166698,0.00047584,0.08056324,0.03775358,-0.26696154,-0.00045314,0.02444709,0.09592849,-0.04646989,0.12826282,0.01892978,-0.00898104,-0.01802355,0.03924407,-0.02360253,0.01847031,0.06772093,-0.03380515,0.00050284,0.02670446,0.01868216,-0.01084908,0.07995149,-0.01822186,0.00290254,-0.03787287,0.16774948,-0.07456895,0.01908056,0.02237241,0.02294939,0.00237534,0.0690117,0.02432167,0.08001754,0.03832122,0.09501564,-0.01009801,0.05469323,0.00100766,-0.09034382,-0.00001809,0.00849786,0.01829903,-0.02214306,0.05562372,-0.01141107,-0.03323181,0.04571438,-0.06662647,-0.00195059,0.03689818,-0.01516884,0.01771425,0.04289931,-0.01132714,-0.04395974,0.01118219,0.00558721,0.06084747,-0.01113548,0.01906801,-0.0147214,0.01603757,0.03635265,0.00332344,0.03353662,0.0174119,-0.02401917],"last_embed":{"hash":"da18c8fb4c0610723185b3504a86f6440c569b80e96206a178b28f585df03120","tokens":83}}},"text":null,"length":0,"last_read":{"hash":"da18c8fb4c0610723185b3504a86f6440c569b80e96206a178b28f585df03120","at":1745995306859},"key":"Ollama/How to start Ollama.md##Modelfile#Multimodal models","lines":[56,63],"size":211,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md##Modelfile#Multimodal models#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07529993,-0.0220339,0.02866726,-0.01556448,-0.00660811,0.04371601,-0.0550974,0.00940461,0.01281018,0.03544385,0.02536963,-0.07295271,-0.02261471,0.01509841,0.01382359,0.03413097,-0.0039503,0.03262814,-0.02979998,0.03853438,0.08038672,-0.01416737,0.0699921,-0.03895763,0.06053804,0.0155617,-0.01924878,-0.01119038,-0.00011203,-0.22909187,0.01466814,0.04939193,0.03078789,-0.04659076,-0.03329822,-0.02479993,-0.02507378,0.02897304,-0.03653437,0.00025734,0.00663746,-0.03008716,-0.06891894,-0.06110484,-0.01711574,-0.06905576,-0.06284473,0.06280915,0.00482624,-0.05382332,0.05205764,-0.09294854,-0.00771478,-0.06592719,-0.00769373,0.01348505,0.06972623,0.04328505,-0.00306971,0.06409827,-0.01324323,0.02495671,-0.14191534,0.13089573,-0.00066448,-0.01356252,0.02686938,0.03491037,0.01293094,0.05301212,-0.0249509,-0.01381755,0.00036907,-0.04096505,-0.00113704,-0.05156585,-0.00487056,0.03282086,-0.0361793,-0.03893564,-0.0305196,0.02255533,-0.01952997,-0.00557488,-0.01186031,0.01341635,-0.04851247,-0.02763349,0.00582073,-0.00910951,-0.02874652,-0.03083241,0.00176261,-0.01002649,-0.08323937,0.01725027,-0.014574,-0.00309478,0.00876158,0.14999203,-0.05551919,-0.05040891,0.10757612,-0.05975574,0.04895332,0.00761558,-0.01091022,-0.00698905,-0.07233644,0.02852366,0.04160638,0.01498223,0.00911103,-0.09886735,0.01587171,-0.0766741,-0.04323794,0.0003694,0.0036496,-0.04947435,0.01526381,-0.02304439,0.00975267,-0.03365337,-0.01515029,0.01917836,0.07753224,0.01229813,0.05149774,0.0055282,0.02372652,0.07732846,-0.01537358,-0.02515764,-0.03314177,0.01806702,0.04733017,-0.0029712,-0.02287947,0.01988739,-0.0247712,-0.02046176,0.05378705,-0.08676003,-0.01225796,0.01385476,-0.03092847,0.02105282,-0.05093958,-0.05282994,-0.01498037,0.06813136,-0.02140478,0.04613053,0.05739823,-0.04499185,0.02357483,0.09831388,-0.09678154,0.03018858,-0.02128881,-0.03906952,-0.01744981,0.06613906,-0.07981171,-0.08847564,-0.00842606,-0.0053463,-0.01751464,0.01937025,0.03044624,0.02453238,-0.03283117,-0.00813471,0.09666143,0.01386306,-0.07436056,0.01098562,-0.03212621,0.02885058,0.02506208,-0.05935706,-0.02989659,-0.00766966,-0.0023043,-0.03138229,0.03893334,-0.0446266,0.03654666,-0.03661589,-0.05977724,0.00978248,-0.00465993,0.00459194,-0.04220771,-0.03918881,-0.0527881,-0.02883788,0.04632535,-0.08054256,0.04929856,0.01340941,-0.0164207,0.06696521,0.00341092,-0.03690818,-0.00702594,-0.01241302,0.10593593,-0.0019524,-0.13131738,-0.00223553,0.05648861,-0.00138971,-0.01230038,0.02261122,0.04351711,0.05643228,0.0042996,0.03400019,0.01147077,-0.04421735,-0.13456285,-0.22263907,0.01190973,0.01855379,0.04765902,0.02906282,-0.12055056,0.05647648,0.01513403,0.07282274,0.05214877,0.09587327,-0.02884662,-0.03402511,-0.0377179,-0.00706555,-0.00003922,-0.01756823,0.01420845,-0.00282156,0.03010394,0.03004752,-0.00220731,0.01265616,-0.0766822,0.0152175,-0.02205839,0.17694487,0.07854739,0.01416212,-0.00803056,0.00991818,0.07913511,-0.11344494,-0.05629204,0.04549802,0.02633545,0.03695299,0.03747473,-0.00668084,-0.00029084,0.02383226,0.02663748,0.02064839,-0.06423116,-0.00806045,-0.00509598,0.04231604,-0.02451246,-0.03706035,-0.02014643,0.06348095,0.01639335,0.00295057,0.08096671,-0.04679836,-0.04822865,-0.10303628,-0.04407707,-0.02257707,-0.01044209,0.0158546,0.03333768,0.00465786,-0.07506054,0.03366524,0.0341747,-0.0057727,-0.0514457,0.07895264,-0.01894138,0.00664709,0.12048404,-0.01707981,0.04571097,0.09462851,0.00870574,0.04537803,-0.0196081,-0.04289758,0.01020814,-0.01636853,-0.01201007,-0.00112617,0.03695763,0.00152933,0.00792473,0.00814166,-0.05710978,0.02487817,-0.04271457,-0.02095021,-0.00222273,0.06717604,0.0056574,0.07864159,0.04792807,-0.26333967,-0.00209275,0.0281282,0.09497155,-0.04955228,0.12546059,0.02081906,-0.01894203,-0.01018985,0.03920784,-0.02947946,0.03347633,0.05921533,-0.03183254,-0.00372311,0.01737438,0.02429575,-0.00798885,0.07949468,-0.02267573,-0.00217647,-0.03229419,0.16994993,-0.07816964,0.01706708,0.0295939,0.02643279,0.00103631,0.05776509,0.02245488,0.07418025,0.03564332,0.09521859,-0.00991875,0.04787998,0.00581266,-0.09395191,0.00771743,0.01208275,0.02096521,-0.009776,0.062139,-0.01215959,-0.03023479,0.04945424,-0.06245816,0.00368579,0.03604139,-0.01865624,0.01877009,0.03923565,-0.01804091,-0.04844178,0.01455644,0.0194628,0.06391843,-0.0076348,0.01560688,-0.01237243,0.01035602,0.03892095,0.00125822,0.03312869,0.01518752,-0.02409637],"last_embed":{"hash":"21006ecdd8a55c170f0e195272a2e80eb6b13cf6e0e71bafd3efe8803103103a","tokens":81}}},"text":null,"length":0,"last_read":{"hash":"21006ecdd8a55c170f0e195272a2e80eb6b13cf6e0e71bafd3efe8803103103a","at":1745995306895},"key":"Ollama/How to start Ollama.md##Modelfile#Multimodal models#{1}","lines":[58,63],"size":188,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md##Modelfile#Pass the prompt as an argument": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07497184,-0.04253998,-0.0168893,-0.0184421,0.00729149,0.01229422,-0.05985019,-0.01101689,0.02622803,0.00360238,0.0129703,-0.06505965,0.02767008,-0.00973993,0.01477747,0.03363355,-0.02901521,0.04335162,-0.00812425,0.05025334,0.09547582,-0.03768172,0.02311033,0.01418897,0.02592166,0.04050731,-0.05230323,-0.03997336,-0.02476744,-0.21508077,0.02777971,-0.00506638,0.02519615,-0.05894262,-0.00704782,-0.01092244,-0.00759335,-0.01513712,-0.00729138,0.05366007,0.00632647,0.00693236,-0.00905525,0.01546278,-0.01112456,-0.05043433,-0.0252825,0.10784314,0.0748419,-0.05907441,0.00305871,-0.04160105,-0.05541951,-0.10953899,0.02042704,0.01250187,0.04883701,0.05379294,0.00357374,0.02515954,-0.01628381,0.05652843,-0.20238081,0.16540019,0.04457341,0.01727203,0.04475196,0.07015946,0.03525623,0.0221653,-0.04498096,0.03368001,0.03203197,0.01454677,-0.03160556,0.01225854,-0.02275708,0.0090717,0.02688864,-0.05119346,-0.03170536,0.02475164,0.00709685,-0.01171122,-0.05432595,-0.0061973,-0.01983932,0.01167632,-0.01781525,-0.02460535,-0.04792239,-0.02436874,0.04700026,0.00215046,-0.0506466,0.01218935,0.03106897,0.00126351,-0.08698415,0.14319724,-0.02849339,-0.00057835,0.0046136,-0.05928449,0.06297501,-0.02195662,-0.03859264,0.0228995,0.00122933,0.02431724,0.02585239,0.06501732,0.00254845,-0.1164945,0.0185323,-0.00351166,-0.01769649,-0.03730345,-0.05393507,0.00144005,-0.02914036,-0.02870769,0.01071927,0.0177867,0.00347806,-0.0141673,0.07721642,0.03145817,0.04718832,0.01472378,0.02491275,0.0513371,-0.0663012,-0.02382478,-0.01868727,0.04645648,0.06304049,0.01549598,-0.03939588,-0.01606219,-0.01832578,-0.08967246,0.01409728,-0.08025362,0.01567332,0.00754177,0.00197472,0.00621823,-0.03608018,-0.05676721,0.01869156,0.04416879,-0.00744625,-0.01299815,0.08230156,-0.03257078,0.06688286,0.12685566,-0.09764499,0.00564295,-0.12107943,-0.00137111,-0.00894866,0.05456426,-0.01283253,-0.0914061,-0.05739937,0.00759307,-0.0210226,-0.00400552,0.04477094,0.07179695,-0.06656065,-0.01108816,0.06493469,-0.03405067,-0.08091982,0.00597992,0.01271502,0.03761319,0.02372947,-0.04780268,-0.00504167,-0.02329006,-0.00643696,-0.09496749,-0.00701947,-0.06027304,0.01679788,-0.01164924,-0.07065033,0.0271543,0.02416964,0.01046067,-0.00676076,-0.03551206,-0.04817529,-0.01327978,0.04720623,-0.05267652,0.06906183,0.09568638,-0.03186978,0.08296771,-0.05152997,-0.06733475,0.0150438,-0.01520955,0.06771582,0.00944215,-0.08261508,0.00402158,0.08219539,0.01871734,-0.01382471,0.03131098,0.0350345,0.04826126,0.0240984,0.02378592,0.05825281,-0.05135663,-0.0336467,-0.20698744,-0.0101567,0.03285316,0.04193911,0.0156944,-0.1099095,0.04540092,-0.03131557,-0.00907617,0.0488174,0.08017746,-0.03732615,0.03309139,0.00879655,-0.03183845,0.03592642,-0.08013954,0.04974563,-0.01440075,0.06109872,0.04746522,-0.01887882,-0.048246,-0.04443912,0.01545085,-0.00116408,0.18059631,0.07475375,0.05627904,0.00315839,0.05340679,0.04544995,-0.06915747,-0.1180433,0.00999914,0.0343143,0.02554692,0.03663014,0.03864407,0.04445441,0.06379739,0.03706134,0.00679594,-0.05810048,-0.06041323,-0.01764316,0.04842073,0.0057696,-0.03038911,-0.02844715,0.01323814,-0.01931511,0.0516319,0.02323514,-0.07273176,-0.02357019,-0.11513133,-0.02320014,-0.04650911,-0.04146057,0.03340254,0.06263337,-0.02802666,-0.06954683,-0.02191224,-0.01713281,0.01662236,-0.02429017,0.05298615,-0.05412206,0.05131351,0.08123831,-0.02599922,0.0596293,0.10213413,0.01529112,-0.02804302,-0.05273835,-0.01916641,0.01009866,-0.01853832,-0.02040381,0.03092972,0.01647114,0.03437307,0.0047672,-0.00106441,-0.0190444,0.0178301,-0.04042735,-0.09359106,0.01559472,-0.00989322,-0.00433304,0.0509422,0.05382766,-0.24955265,0.0429896,0.02903209,0.09460498,0.00026016,0.05808324,0.02452584,-0.02876256,-0.01527078,0.00012156,-0.03588865,0.04696793,0.03516566,-0.02205678,0.00575415,0.02192886,0.04609987,-0.01893839,0.03987076,0.01776738,0.0137883,-0.0041936,0.14730449,-0.06565271,0.03110877,0.00422979,0.01399796,-0.00420254,0.07063201,-0.00504453,0.02600154,0.02885986,0.07325128,-0.05046817,0.06488299,-0.01815959,-0.01318737,-0.02314164,0.02530839,-0.03106217,-0.02877128,0.01415575,-0.00030014,-0.02401317,0.02263802,-0.03935081,-0.01148931,-0.05248415,-0.03388838,0.08631711,0.02738606,0.01682316,-0.01922427,0.02945476,0.0100053,0.06432188,-0.013765,-0.00975168,-0.04419829,-0.02747994,0.01508248,-0.05049053,0.05663086,-0.00381527,0.0414005],"last_embed":{"hash":"f5eb1a1851a07cd806272b9136d18033c5a44756960044fb58f04f0b6921614b","tokens":477}}},"text":null,"length":0,"last_read":{"hash":"f5eb1a1851a07cd806272b9136d18033c5a44756960044fb58f04f0b6921614b","at":1745995307212},"key":"Ollama/How to start Ollama.md##Modelfile#Pass the prompt as an argument","lines":[64,717],"size":34478,"outlinks":[{"title":"Download","target":"https://ollama.com/download/Ollama-darwin.zip","line":17},{"title":"Download","target":"https://ollama.com/download/OllamaSetup.exe","line":21},{"title":"Manual install instructions","target":"https://github.com/ollama/ollama/blob/main/docs/linux.md","line":30},{"title":"Ollama Docker image","target":"https://hub.docker.com/r/ollama/ollama","line":35},{"title":"ollama-python","target":"https://github.com/ollama/ollama-python","line":40},{"title":"ollama-js","target":"https://github.com/ollama/ollama-js","line":41},{"title":"Discord","target":"https://discord.gg/ollama","line":47},{"title":"Reddit","target":"https://reddit.com/r/ollama","line":48},{"title":"Llama 3.2","target":"https://ollama.com/library/llama3.2","line":56},{"title":"ollama.com/library","target":"https://ollama.com/library 'ollama model library'","line":71},{"title":"ollama.com/library","target":"https://ollama.com/library `ollama model library","line":73},{"title":"guide","target":"docs/import.md","line":156},{"title":"Modelfile","target":"docs/modelfile.md","line":204},{"title":"developer guide","target":"https://github.com/ollama/ollama/blob/main/docs/development.md","line":321},{"title":"API documentation","target":"./docs/api.md","line":361},{"title":"Open WebUI","target":"https://github.com/open-webui/open-webui","line":367},{"title":"Enchanted (macOS native)","target":"https://github.com/AugustDev/enchanted","line":368},{"title":"Hollama","target":"https://github.com/fmaclen/hollama","line":369},{"title":"Lollms-Webui","target":"https://github.com/ParisNeo/lollms-webui","line":370},{"title":"LibreChat","target":"https://github.com/danny-avila/LibreChat","line":371},{"title":"Bionic GPT","target":"https://github.com/bionic-gpt/bionic-gpt","line":372},{"title":"HTML UI","target":"https://github.com/rtcfirefly/ollama-ui","line":373},{"title":"Saddle","target":"https://github.com/jikkuatwork/saddle","line":374},{"title":"Chatbot UI","target":"https://github.com/ivanfioravanti/chatbot-ollama","line":375},{"title":"Chatbot UI v2","target":"https://github.com/mckaywrigley/chatbot-ui","line":376},{"title":"Typescript UI","target":"https://github.com/ollama-interface/Ollama-Gui?tab=readme-ov-file","line":377},{"title":"Minimalistic React UI for Ollama Models","target":"https://github.com/richawo/minimal-llm-ui","line":378},{"title":"Ollamac","target":"https://github.com/kevinhermawan/Ollamac","line":379},{"title":"big-AGI","target":"https://github.com/enricoros/big-AGI/blob/main/docs/config-local-ollama.md","line":380},{"title":"Cheshire Cat assistant framework","target":"https://github.com/cheshire-cat-ai/core","line":381},{"title":"Amica","target":"https://github.com/semperai/amica","line":382},{"title":"chatd","target":"https://github.com/BruceMacD/chatd","line":383},{"title":"Ollama-SwiftUI","target":"https://github.com/kghandour/Ollama-SwiftUI","line":384},{"title":"Dify.AI","target":"https://github.com/langgenius/dify","line":385},{"title":"MindMac","target":"https://mindmac.app","line":386},{"title":"NextJS Web Interface for Ollama","target":"https://github.com/jakobhoeg/nextjs-ollama-llm-ui","line":387},{"title":"Msty","target":"https://msty.app","line":388},{"title":"Chatbox","target":"https://github.com/Bin-Huang/Chatbox","line":389},{"title":"WinForm Ollama Copilot","target":"https://github.com/tgraupmann/WinForm_Ollama_Copilot","line":390},{"title":"Get Started Doc","target":"https://docs.nextchat.dev/models/ollama","line":391},{"title":"NextChat","target":"https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web","line":391},{"title":"Alpaca WebUI","target":"https://github.com/mmo80/alpaca-webui","line":392},{"title":"OllamaGUI","target":"https://github.com/enoch1118/ollamaGUI","line":393},{"title":"OpenAOE","target":"https://github.com/InternLM/OpenAOE","line":394},{"title":"Odin Runes","target":"https://github.com/leonid20000/OdinRunes","line":395},{"title":"LLM-X","target":"https://github.com/mrdjohnson/llm-x","line":396},{"title":"AnythingLLM (Docker + MacOs/Windows/Linux native app)","target":"https://github.com/Mintplex-Labs/anything-llm","line":397},{"title":"Ollama Basic Chat: Uses HyperDiv Reactive UI","target":"https://github.com/rapidarchitect/ollama_basic_chat","line":398},{"title":"Ollama-chats RPG","target":"https://github.com/drazdra/ollama-chats","line":399},{"title":"IntelliBar","target":"https://intellibar.app/","line":400},{"title":"QA-Pilot","target":"https://github.com/reid41/QA-Pilot","line":401},{"title":"ChatOllama","target":"https://github.com/sugarforever/chat-ollama","line":402},{"title":"CRAG Ollama Chat","target":"https://github.com/Nagi-ovo/CRAG-Ollama-Chat","line":403},{"title":"RAGFlow","target":"https://github.com/infiniflow/ragflow","line":404},{"title":"StreamDeploy","target":"https://github.com/StreamDeploy-DevRel/streamdeploy-llm-app-scaffold","line":405},{"title":"chat","target":"https://github.com/swuecho/chat","line":406},{"title":"Lobe Chat","target":"https://github.com/lobehub/lobe-chat","line":407},{"title":"Integrating Doc","target":"https://lobehub.com/docs/self-hosting/examples/ollama","line":407},{"title":"Ollama RAG Chatbot","target":"https://github.com/datvodinh/rag-chatbot.git","line":408},{"title":"BrainSoup","target":"https://www.nurgo-software.com/products/brainsoup","line":409},{"title":"macai","target":"https://github.com/Renset/macai","line":410},{"title":"RWKV-Runner","target":"https://github.com/josStorer/RWKV-Runner","line":411},{"title":"Ollama Grid Search","target":"https://github.com/dezoito/ollama-grid-search","line":412},{"title":"Olpaka","target":"https://github.com/Otacon/olpaka","line":413},{"title":"OllamaSpring","target":"https://github.com/CrazyNeil/OllamaSpring","line":414},{"title":"LLocal.in","target":"https://github.com/kartikm7/llocal","line":415},{"title":"Shinkai Desktop","target":"https://github.com/dcSpark/shinkai-apps","line":416},{"title":"AiLama","target":"https://github.com/zeyoyt/ailama","line":417},{"title":"Ollama with Google Mesop","target":"https://github.com/rapidarchitect/ollama_mesop/","line":418},{"title":"R2R","target":"https://github.com/SciPhi-AI/R2R","line":419},{"title":"Ollama-Kis","target":"https://github.com/elearningshow/ollama-kis","line":420},{"title":"OpenGPA","target":"https://opengpa.org","line":421},{"title":"Painting Droid","target":"https://github.com/mateuszmigas/painting-droid","line":422},{"title":"Kerlig AI","target":"https://www.kerlig.com/","line":423},{"title":"AI Studio","target":"https://github.com/MindWorkAI/AI-Studio","line":424},{"title":"Sidellama","target":"https://github.com/gyopak/sidellama","line":425},{"title":"LLMStack","target":"https://github.com/trypromptly/LLMStack","line":426},{"title":"BoltAI for Mac","target":"https://boltai.com","line":427},{"title":"Harbor","target":"https://github.com/av/harbor","line":428},{"title":"PyGPT","target":"https://github.com/szczyglis-dev/py-gpt","line":429},{"title":"Alpaca","target":"https://github.com/Jeffser/Alpaca","line":430},{"title":"AutoGPT","target":"https://github.com/Significant-Gravitas/AutoGPT/blob/master/docs/content/platform/ollama.md","line":431},{"title":"Go-CREW","target":"https://www.jonathanhecl.com/go-crew/","line":432},{"title":"PartCAD","target":"https://github.com/openvmp/partcad/","line":433},{"title":"Ollama4j Web UI","target":"https://github.com/ollama4j/ollama4j-web-ui","line":434},{"title":"PyOllaMx","target":"https://github.com/kspviswa/pyOllaMx","line":435},{"title":"Claude Dev","target":"https://github.com/saoudrizwan/claude-dev","line":436},{"title":"Cherry Studio","target":"https://github.com/kangfenmao/cherry-studio","line":437},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":438},{"title":"Archyve","target":"https://github.com/nickthecook/archyve","line":439},{"title":"crewAI with Mesop","target":"https://github.com/rapidarchitect/ollama-crew-mesop","line":440},{"title":"Tkinter-based client","target":"https://github.com/chyok/ollama-gui","line":441},{"title":"LLMChat","target":"https://github.com/trendy-design/llmchat","line":442},{"title":"Local Multimodal AI Chat","target":"https://github.com/Leon-Sander/Local-Multimodal-AI-Chat","line":443},{"title":"ARGO","target":"https://github.com/xark-argo/argo","line":444},{"title":"OrionChat","target":"https://github.com/EliasPereirah/OrionChat","line":445},{"title":"G1","target":"https://github.com/bklieger-groq/g1","line":446},{"title":"Web management","target":"https://github.com/lemonit-eric-mao/ollama-web-management","line":447},{"title":"Promptery","target":"https://github.com/promptery/promptery","line":448},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":449},{"title":"chat-ollama","target":"https://github.com/annilq/chat-ollama","line":450},{"title":"SpaceLlama","target":"https://github.com/tcsenpai/spacellama","line":451},{"title":"YouLama","target":"https://github.com/tcsenpai/youlama","line":452},{"title":"DualMind","target":"https://github.com/tcsenpai/dualmind","line":453},{"title":"ollamarama-matrix","target":"https://github.com/h1ddenpr0cess20/ollamarama-matrix","line":454},{"title":"ollama-chat-app","target":"https://github.com/anan1213095357/ollama-chat-app","line":455},{"title":"Perfect Memory AI","target":"https://www.perfectmemory.ai/","line":456},{"title":"Hexabot","target":"https://github.com/hexastack/hexabot","line":457},{"title":"Reddit Rate","target":"https://github.com/rapidarchitect/reddit_analyzer","line":458},{"title":"OpenTalkGpt","target":"https://github.com/adarshM84/OpenTalkGpt","line":459},{"title":"VT","target":"https://github.com/vinhnx/vt.ai","line":460},{"title":"Nosia","target":"https://github.com/nosia-ai/nosia","line":461},{"title":"Witsy","target":"https://github.com/nbonamy/witsy","line":462},{"title":"Abbey","target":"https://github.com/US-Artificial-Intelligence/abbey","line":463},{"title":"Minima","target":"https://github.com/dmayboroda/minima","line":464},{"title":"aidful-ollama-model-delete","target":"https://github.com/AidfulAI/aidful-ollama-model-delete","line":465},{"title":"Perplexica","target":"https://github.com/ItzCrazyKns/Perplexica","line":466},{"title":"Ollama Chat WebUI for Docker ","target":"https://github.com/oslook/ollama-webui","line":467},{"title":"AI Toolkit for Visual Studio Code","target":"https://aka.ms/ai-tooklit/ollama-docs","line":468},{"title":"MinimalNextOllamaChat","target":"https://github.com/anilkay/MinimalNextOllamaChat","line":469},{"title":"Chipper","target":"https://github.com/TilmanGriesel/chipper","line":470},{"title":"ChibiChat","target":"https://github.com/CosmicEventHorizon/ChibiChat","line":471},{"title":"LocalLLM","target":"https://github.com/qusaismael/localllm","line":472},{"title":"Ollamazing","target":"https://github.com/buiducnhat/ollamazing","line":473},{"title":"Google Cloud","target":"https://cloud.google.com/run/docs/tutorials/gpu-gemma2-with-ollama","line":477},{"title":"Fly.io","target":"https://fly.io/docs/python/do-more/add-ollama/","line":478},{"title":"Koyeb","target":"https://www.koyeb.com/deploy/ollama","line":479},{"title":"oterm","target":"https://github.com/ggozad/oterm","line":483},{"title":"Ellama Emacs client","target":"https://github.com/s-kostyaev/ellama","line":484},{"title":"Emacs client","target":"https://github.com/zweifisch/ollama","line":485},{"title":"neollama","target":"https://github.com/paradoxical-dev/neollama","line":486},{"title":"gen.nvim","target":"https://github.com/David-Kunz/gen.nvim","line":487},{"title":"ollama.nvim","target":"https://github.com/nomnivore/ollama.nvim","line":488},{"title":"ollero.nvim","target":"https://github.com/marco-souza/ollero.nvim","line":489},{"title":"ollama-chat.nvim","target":"https://github.com/gerazov/ollama-chat.nvim","line":490},{"title":"ogpt.nvim","target":"https://github.com/huynle/ogpt.nvim","line":491},{"title":"gptel Emacs client","target":"https://github.com/karthink/gptel","line":492},{"title":"Oatmeal","target":"https://github.com/dustinblackman/oatmeal","line":493},{"title":"cmdh","target":"https://github.com/pgibler/cmdh","line":494},{"title":"ooo","target":"https://github.com/npahlfer/ooo","line":495},{"title":"shell-pilot","target":"https://github.com/reid41/shell-pilot","line":496},{"title":"tenere","target":"https://github.com/pythops/tenere","line":497},{"title":"llm-ollama","target":"https://github.com/taketwo/llm-ollama","line":498},{"title":"Datasette's LLM CLI","target":"https://llm.datasette.io/en/stable/","line":498},{"title":"typechat-cli","target":"https://github.com/anaisbetts/typechat-cli","line":499},{"title":"ShellOracle","target":"https://github.com/djcopley/ShellOracle","line":500},{"title":"tlm","target":"https://github.com/yusufcanb/tlm","line":501},{"title":"podman-ollama","target":"https://github.com/ericcurtin/podman-ollama","line":502},{"title":"gollama","target":"https://github.com/sammcj/gollama","line":503},{"title":"ParLlama","target":"https://github.com/paulrobello/parllama","line":504},{"title":"Ollama eBook Summary","target":"https://github.com/cognitivetech/ollama-ebook-summary/","line":505},{"title":"Ollama Mixture of Experts (MOE) in 50 lines of code","target":"https://github.com/rapidarchitect/ollama_moe","line":506},{"title":"vim-intelligence-bridge","target":"https://github.com/pepo-ec/vim-intelligence-bridge","line":507},{"title":"x-cmd ollama","target":"https://x-cmd.com/mod/ollama","line":508},{"title":"bb7","target":"https://github.com/drunkwcodes/bb7","line":509},{"title":"SwollamaCLI","target":"https://github.com/marcusziade/Swollama","line":510},{"title":"Demo","target":"https://github.com/marcusziade/Swollama?tab=readme-ov-file#cli-usage","line":510},{"title":"aichat","target":"https://github.com/sigoden/aichat","line":511},{"title":"PowershAI","target":"https://github.com/rrg92/powershai","line":512},{"title":"orbiton","target":"https://github.com/xyproto/orbiton","line":513},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":517},{"title":"pgai","target":"https://github.com/timescale/pgai","line":521},{"title":"Get started guide","target":"https://github.com/timescale/pgai/blob/main/docs/vectorizer-quick-start.md","line":522},{"title":"MindsDB","target":"https://github.com/mindsdb/mindsdb/blob/staging/mindsdb/integrations/handlers/ollama_handler/README.md","line":523},{"title":"chromem-go","target":"https://github.com/philippgille/chromem-go/blob/v0.5.0/embed_ollama.go","line":524},{"title":"example","target":"https://github.com/philippgille/chromem-go/tree/v0.5.0/examples/rag-wikipedia-ollama","line":524},{"title":"Kangaroo","target":"https://github.com/dbkangaroo/kangaroo","line":525},{"title":"Pacman","target":"https://archlinux.org/packages/extra/x86_64/ollama/","line":529},{"title":"Gentoo","target":"https://github.com/gentoo/guru/tree/master/app-misc/ollama","line":530},{"title":"Homebrew","target":"https://formulae.brew.sh/formula/ollama","line":531},{"title":"Helm Chart","target":"https://artifacthub.io/packages/helm/ollama-helm/ollama","line":532},{"title":"Guix channel","target":"https://codeberg.org/tusharhero/ollama-guix","line":533},{"title":"Nix package","target":"https://search.nixos.org/packages?show=ollama&from=0&size=50&sort=relevance&type=packages&query=ollama","line":534},{"title":"Flox","target":"https://flox.dev/blog/ollama-part-one","line":535},{"title":"LangChain.js","target":"https://js.langchain.com/docs/integrations/chat/ollama/","line":539},{"title":"example","target":"https://js.langchain.com/docs/tutorials/local_rag/","line":539},{"title":"LangChain","target":"https://python.langchain.com/docs/integrations/llms/ollama","line":539},{"title":"Firebase Genkit","target":"https://firebase.google.com/docs/genkit/plugins/ollama","line":540},{"title":"crewAI","target":"https://github.com/crewAIInc/crewAI","line":541},{"title":"Yacana","target":"https://remembersoftwares.github.io/yacana/","line":542},{"title":"reference","target":"https://docs.spring.io/spring-ai/reference/api/chat/ollama-chat.html","line":543},{"title":"Spring AI","target":"https://github.com/spring-projects/spring-ai","line":543},{"title":"example","target":"https://github.com/tzolov/ollama-tools","line":543},{"title":"LangChainGo","target":"https://github.com/tmc/langchaingo/","line":544},{"title":"example","target":"https://github.com/tmc/langchaingo/tree/main/examples/ollama-completion-example","line":544},{"title":"LangChain4j","target":"https://github.com/langchain4j/langchain4j","line":545},{"title":"example","target":"https://github.com/langchain4j/langchain4j-examples/tree/main/ollama-examples/src/main/java","line":545},{"title":"LangChainRust","target":"https://github.com/Abraxas-365/langchain-rust","line":546},{"title":"example","target":"https://github.com/Abraxas-365/langchain-rust/blob/main/examples/llm_ollama.rs","line":546},{"title":"LangChain for .NET","target":"https://github.com/tryAGI/LangChain","line":547},{"title":"example","target":"https://github.com/tryAGI/LangChain/blob/main/examples/LangChain.Samples.OpenAI/Program.cs","line":547},{"title":"LLPhant","target":"https://github.com/theodo-group/LLPhant?tab=readme-ov-file#ollama","line":548},{"title":"LlamaIndex","target":"https://docs.llamaindex.ai/en/stable/examples/llm/ollama/","line":549},{"title":"LlamaIndexTS","target":"https://ts.llamaindex.ai/modules/llms/available_llms/ollama","line":549},{"title":"LiteLLM","target":"https://github.com/BerriAI/litellm","line":550},{"title":"OllamaFarm for Go","target":"https://github.com/presbrey/ollamafarm","line":551},{"title":"OllamaSharp for .NET","target":"https://github.com/awaescher/OllamaSharp","line":552},{"title":"Ollama for Ruby","target":"https://github.com/gbaptista/ollama-ai","line":553},{"title":"Ollama-rs for Rust","target":"https://github.com/pepperoni21/ollama-rs","line":554},{"title":"Ollama-hpp for C++","target":"https://github.com/jmont-dev/ollama-hpp","line":555},{"title":"Ollama4j for Java","target":"https://github.com/ollama4j/ollama4j","line":556},{"title":"ModelFusion Typescript Library","target":"https://modelfusion.dev/integration/model-provider/ollama","line":557},{"title":"OllamaKit for Swift","target":"https://github.com/kevinhermawan/OllamaKit","line":558},{"title":"Ollama for Dart","target":"https://github.com/breitburg/dart-ollama","line":559},{"title":"Ollama for Laravel","target":"https://github.com/cloudstudio/ollama-laravel","line":560},{"title":"LangChainDart","target":"https://github.com/davidmigloz/langchain_dart","line":561},{"title":"Semantic Kernel - Python","target":"https://github.com/microsoft/semantic-kernel/tree/main/python/semantic_kernel/connectors/ai/ollama","line":562},{"title":"Haystack","target":"https://github.com/deepset-ai/haystack-integrations/blob/main/integrations/ollama.md","line":563},{"title":"Elixir LangChain","target":"https://github.com/brainlid/langchain","line":564},{"title":"Ollama for R - rollama","target":"https://github.com/JBGruber/rollama","line":565},{"title":"Ollama for R - ollama-r","target":"https://github.com/hauselin/ollama-r","line":566},{"title":"Ollama-ex for Elixir","target":"https://github.com/lebrunel/ollama-ex","line":567},{"title":"Ollama Connector for SAP ABAP","target":"https://github.com/b-tocs/abap_btocs_ollama","line":568},{"title":"Testcontainers","target":"https://testcontainers.com/modules/ollama/","line":569},{"title":"Portkey","target":"https://portkey.ai/docs/welcome/integration-guides/ollama","line":570},{"title":"PromptingTools.jl","target":"https://github.com/svilupp/PromptingTools.jl","line":571},{"title":"example","target":"https://svilupp.github.io/PromptingTools.jl/dev/examples/working_with_ollama","line":571},{"title":"LlamaScript","target":"https://github.com/Project-Llama/llamascript","line":572},{"title":"llm-axe","target":"https://github.com/emirsahin1/llm-axe","line":573},{"title":"Gollm","target":"https://docs.gollm.co/examples/ollama-example","line":574},{"title":"Gollama for Golang","target":"https://github.com/jonathanhecl/gollama","line":575},{"title":"Ollamaclient for Golang","target":"https://github.com/xyproto/ollamaclient","line":576},{"title":"High-level function abstraction in Go","target":"https://gitlab.com/tozd/go/fun","line":577},{"title":"Ollama PHP","target":"https://github.com/ArdaGnsrn/ollama-php","line":578},{"title":"Agents-Flex for Java","target":"https://github.com/agents-flex/agents-flex","line":579},{"title":"example","target":"https://github.com/agents-flex/agents-flex/tree/main/agents-flex-llm/agents-flex-llm-ollama/src/test/java/com/agentsflex/llm/ollama","line":579},{"title":"Parakeet","target":"https://github.com/parakeet-nest/parakeet","line":580},{"title":"Haverscript","target":"https://github.com/andygill/haverscript","line":581},{"title":"examples","target":"https://github.com/andygill/haverscript/tree/main/examples","line":581},{"title":"Ollama for Swift","target":"https://github.com/mattt/ollama-swift","line":582},{"title":"Swollama for Swift","target":"https://github.com/marcusziade/Swollama","line":583},{"title":"DocC","target":"https://marcusziade.github.io/Swollama/documentation/swollama/","line":583},{"title":"GoLamify","target":"https://github.com/prasad89/golamify","line":584},{"title":"Ollama for Haskell","target":"https://github.com/tusharad/ollama-haskell","line":585},{"title":"multi-llm-ts","target":"https://github.com/nbonamy/multi-llm-ts","line":586},{"title":"LlmTornado","target":"https://github.com/lofcz/llmtornado","line":587},{"title":"Ollama for Zig","target":"https://github.com/dravenk/ollama-zig","line":588},{"title":"Abso","target":"https://github.com/lunary-ai/abso","line":589},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":593},{"title":"Maid","target":"https://github.com/Mobile-Artificial-Intelligence/maid","line":594},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":595},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":596},{"title":"Raycast extension","target":"https://github.com/MassimilianoPasquini97/raycast_ollama","line":600},{"title":"Discollama","target":"https://github.com/mxyng/discollama","line":601},{"title":"Continue","target":"https://github.com/continuedev/continue","line":602},{"title":"Vibe","target":"https://github.com/thewh1teagle/vibe","line":603},{"title":"Obsidian Ollama plugin","target":"https://github.com/hinterdupfinger/obsidian-ollama","line":604},{"title":"Logseq Ollama plugin","target":"https://github.com/omagdy7/ollama-logseq","line":605},{"title":"NotesOllama","target":"https://github.com/andersrex/notesollama","line":606},{"title":"Dagger Chatbot","target":"https://github.com/samalba/dagger-chatbot","line":607},{"title":"Discord AI Bot","target":"https://github.com/mekb-turtle/discord-ai-bot","line":608},{"title":"Ollama Telegram Bot","target":"https://github.com/ruecat/ollama-telegram","line":609},{"title":"Hass Ollama Conversation","target":"https://github.com/ej52/hass-ollama-conversation","line":610},{"title":"Rivet plugin","target":"https://github.com/abrenneke/rivet-plugin-ollama","line":611},{"title":"Obsidian BMO Chatbot plugin","target":"https://github.com/longy2k/obsidian-bmo-chatbot","line":612},{"title":"Cliobot","target":"https://github.com/herval/cliobot","line":613},{"title":"Copilot for Obsidian plugin","target":"https://github.com/logancyang/obsidian-copilot","line":614},{"title":"Obsidian Local GPT plugin","target":"https://github.com/pfrankov/obsidian-local-gpt","line":615},{"title":"Open Interpreter","target":"https://docs.openinterpreter.com/language-model-setup/local-models/ollama","line":616},{"title":"Llama Coder","target":"https://github.com/ex3ndr/llama-coder","line":617},{"title":"Ollama Copilot","target":"https://github.com/bernardo-bruning/ollama-copilot","line":618},{"title":"twinny","target":"https://github.com/rjmacarthy/twinny","line":619},{"title":"Wingman-AI","target":"https://github.com/RussellCanfield/wingman-ai","line":620},{"title":"Page Assist","target":"https://github.com/n4ze3m/page-assist","line":621},{"title":"Plasmoid Ollama Control","target":"https://github.com/imoize/plasmoid-ollamacontrol","line":622},{"title":"AI Telegram Bot","target":"https://github.com/tusharhero/aitelegrambot","line":623},{"title":"AI ST Completion","target":"https://github.com/yaroslavyaroslav/OpenAI-sublime-text","line":624},{"title":"Discord-Ollama Chat Bot","target":"https://github.com/kevinthedang/discord-ollama","line":625},{"title":"ChatGPTBox: All in one browser extension","target":"https://github.com/josStorer/chatGPTBox","line":626},{"title":"Integrating Tutorial","target":"https://github.com/josStorer/chatGPTBox/issues/616#issuecomment-1975186467","line":626},{"title":"Discord AI chat/moderation bot","target":"https://github.com/rapmd73/Companion","line":627},{"title":"Headless Ollama","target":"https://github.com/nischalj10/headless-ollama","line":628},{"title":"Terraform AWS Ollama & Open WebUI","target":"https://github.com/xuyangbocn/terraform-aws-self-host-llm","line":629},{"title":"node-red-contrib-ollama","target":"https://github.com/jakubburkiewicz/node-red-contrib-ollama","line":630},{"title":"Local AI Helper","target":"https://github.com/ivostoykov/localAI","line":631},{"title":"vnc-lm","target":"https://github.com/jake83741/vnc-lm","line":632},{"title":"LSP-AI","target":"https://github.com/SilasMarvin/lsp-ai","line":633},{"title":"QodeAssist","target":"https://github.com/Palm1r/QodeAssist","line":634},{"title":"Obsidian Quiz Generator plugin","target":"https://github.com/ECuiDev/obsidian-quiz-generator","line":635},{"title":"AI Summmary Helper plugin","target":"https://github.com/philffm/ai-summary-helper","line":636},{"title":"TextCraft","target":"https://github.com/suncloudsmoon/TextCraft","line":637},{"title":"Alfred Ollama","target":"https://github.com/zeitlings/alfred-ollama","line":638},{"title":"TextLLaMA","target":"https://github.com/adarshM84/TextLLaMA","line":639},{"title":"Simple-Discord-AI","target":"https://github.com/zyphixor/simple-discord-ai","line":640},{"title":"llama.cpp","target":"https://github.com/ggerganov/llama.cpp","line":644},{"title":"Lunary","target":"https://lunary.ai/docs/integrations/ollama","line":647},{"title":"OpenLIT","target":"https://github.com/openlit/openlit","line":648},{"title":"HoneyHive","target":"https://docs.honeyhive.ai/integrations/ollama","line":649},{"title":"Langfuse","target":"https://langfuse.com/docs/integrations/ollama","line":650},{"title":"MLflow Tracing","target":"https://mlflow.org/docs/latest/llms/tracing/index.html#automatic-tracing","line":651}],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md##Modelfile#Pass the prompt as an argument#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08087134,-0.04533342,-0.012742,-0.02016226,0.00412919,0.02062303,-0.05489092,-0.00965184,0.02720158,0.00260776,0.01005424,-0.05673195,0.03308238,-0.00285585,0.00899494,0.04059295,-0.03598569,0.04033401,-0.0064352,0.04729408,0.10021015,-0.02951651,0.01810113,0.01219051,0.03176722,0.04285727,-0.04720522,-0.03655262,-0.0233564,-0.21724421,0.02561382,-0.00864759,0.02732758,-0.0596137,-0.0177782,-0.00597956,-0.00650205,-0.01718343,-0.00442832,0.04836408,0.00884138,0.00834666,-0.01217995,0.0129452,-0.01734867,-0.06014832,-0.02134443,0.11023205,0.07538522,-0.05162713,0.0075248,-0.04584987,-0.05356138,-0.11240378,0.0247243,0.01426949,0.05036274,0.05193448,0.00689987,0.02482718,-0.01380009,0.04992621,-0.20093076,0.1643818,0.03694056,0.02285751,0.04622965,0.06404708,0.03695878,0.0266361,-0.04029747,0.02917135,0.03158728,0.01042621,-0.03237827,0.01233319,-0.02542329,0.00870008,0.02606739,-0.04524416,-0.03225951,0.02097343,0.0117621,-0.0098602,-0.05891449,-0.00222083,-0.01808491,0.00557048,-0.02239909,-0.03352074,-0.05206731,-0.02075844,0.03589338,0.0025776,-0.04902088,0.01336467,0.02248228,-0.00064099,-0.08956867,0.14512345,-0.02719932,-0.00189498,-0.00091141,-0.05458665,0.06026366,-0.01670292,-0.03293248,0.02522811,0.00469235,0.02823392,0.02442038,0.06575887,-0.0075533,-0.1192599,0.01437876,-0.00098771,-0.01726638,-0.03952299,-0.0523865,-0.00351913,-0.02257643,-0.02846265,0.00391707,0.01797078,-0.01134755,-0.01393075,0.07411467,0.03590056,0.05317633,0.01120343,0.02466129,0.044063,-0.06623224,-0.02363213,-0.0120051,0.0518968,0.06831775,0.01592443,-0.03880793,-0.01844903,-0.02204158,-0.08527776,0.01694903,-0.08152174,0.01402519,0.00740727,-0.00309184,0.00546972,-0.03557928,-0.05468641,0.01846315,0.04016294,-0.00520651,-0.01257861,0.08713341,-0.03130268,0.06192422,0.12422598,-0.09384862,0.00344957,-0.12833519,-0.00145151,-0.00985471,0.0520283,-0.01669633,-0.08713946,-0.05492516,0.00781842,-0.02442094,-0.00176458,0.04239481,0.08116189,-0.07040034,-0.01169514,0.07106857,-0.03491103,-0.07600317,0.00391086,0.00580046,0.04379126,0.01690932,-0.04844894,-0.00391123,-0.02747436,-0.00539636,-0.08564077,-0.00941444,-0.06481452,0.01366748,-0.01653561,-0.07064277,0.03467301,0.02584356,0.01775397,-0.00465605,-0.04052921,-0.05104131,-0.01465032,0.04891273,-0.05817669,0.07172348,0.09851948,-0.02523011,0.08557187,-0.04999558,-0.06867041,0.01692059,-0.0161884,0.06761471,0.0108417,-0.08730873,-0.00356086,0.08350331,0.01544764,-0.01550065,0.03670807,0.03465408,0.05202182,0.02551888,0.02902688,0.057288,-0.05143617,-0.03435501,-0.20737098,-0.00789887,0.03136262,0.03866618,0.01208457,-0.11081302,0.04348165,-0.02723671,-0.01045626,0.04370198,0.08142769,-0.04150239,0.0313359,0.00960039,-0.03601343,0.0326553,-0.07877575,0.05906954,-0.01122021,0.05632913,0.03968821,-0.01296514,-0.0485507,-0.04290495,0.01379607,0.00103613,0.18126298,0.07430532,0.04740191,0.00149313,0.05228256,0.0448381,-0.06881715,-0.11871468,0.01086209,0.03387203,0.02295949,0.03277954,0.04207893,0.03833446,0.06655771,0.04188976,0.00793118,-0.05969778,-0.06225847,-0.0204783,0.04827603,0.01642255,-0.02189066,-0.03420071,0.01492795,-0.01870346,0.04429683,0.02474104,-0.06904119,-0.01456067,-0.11401885,-0.01972417,-0.03751795,-0.0435682,0.03112129,0.05704399,-0.03250235,-0.07374859,-0.02179466,-0.01819986,0.02283313,-0.02976662,0.05709381,-0.05217423,0.05254079,0.08076929,-0.02224709,0.06194642,0.10434514,0.02188659,-0.02409701,-0.05625476,-0.01416758,0.00493445,-0.02291448,-0.02541734,0.02633303,0.01815564,0.0349655,0.01151427,0.00804652,-0.01871287,0.01617888,-0.03796349,-0.0863768,0.00921185,-0.00759811,-0.00748303,0.04052076,0.05550984,-0.24751996,0.04451207,0.02378573,0.09089722,-0.00073059,0.05327892,0.02173235,-0.02482394,-0.01512781,-0.0023461,-0.03855094,0.04091945,0.0369648,-0.01465737,0.00515474,0.01959323,0.04914379,-0.01411447,0.04543274,0.015386,0.0132471,-0.00309187,0.15124208,-0.07202525,0.02887925,0.0063204,0.02005647,-0.00835469,0.07048069,0.0025061,0.03096459,0.02856811,0.08273162,-0.05241252,0.06739065,-0.01198887,-0.01115643,-0.02301897,0.03132333,-0.02775729,-0.03791098,0.01478483,0.00523111,-0.02339121,0.0245658,-0.03561251,-0.01505627,-0.05773462,-0.03524185,0.08247717,0.0247107,0.01182834,-0.0211015,0.02791749,0.00791217,0.05619122,-0.01587571,-0.00977521,-0.04308311,-0.02013573,0.02076462,-0.04626776,0.05446051,-0.00575005,0.04108321],"last_embed":{"hash":"54713947eff9a9146d0062ea406c4585028ad9d28a9cb1a68f9e11692c07a426","tokens":476}}},"text":null,"length":0,"last_read":{"hash":"54713947eff9a9146d0062ea406c4585028ad9d28a9cb1a68f9e11692c07a426","at":1745995307213},"key":"Ollama/How to start Ollama.md##Modelfile#Pass the prompt as an argument#{1}","lines":[66,717],"size":34442,"outlinks":[{"title":"Download","target":"https://ollama.com/download/Ollama-darwin.zip","line":15},{"title":"Download","target":"https://ollama.com/download/OllamaSetup.exe","line":19},{"title":"Manual install instructions","target":"https://github.com/ollama/ollama/blob/main/docs/linux.md","line":28},{"title":"Ollama Docker image","target":"https://hub.docker.com/r/ollama/ollama","line":33},{"title":"ollama-python","target":"https://github.com/ollama/ollama-python","line":38},{"title":"ollama-js","target":"https://github.com/ollama/ollama-js","line":39},{"title":"Discord","target":"https://discord.gg/ollama","line":45},{"title":"Reddit","target":"https://reddit.com/r/ollama","line":46},{"title":"Llama 3.2","target":"https://ollama.com/library/llama3.2","line":54},{"title":"ollama.com/library","target":"https://ollama.com/library 'ollama model library'","line":69},{"title":"ollama.com/library","target":"https://ollama.com/library `ollama model library","line":71},{"title":"guide","target":"docs/import.md","line":154},{"title":"Modelfile","target":"docs/modelfile.md","line":202},{"title":"developer guide","target":"https://github.com/ollama/ollama/blob/main/docs/development.md","line":319},{"title":"API documentation","target":"./docs/api.md","line":359},{"title":"Open WebUI","target":"https://github.com/open-webui/open-webui","line":365},{"title":"Enchanted (macOS native)","target":"https://github.com/AugustDev/enchanted","line":366},{"title":"Hollama","target":"https://github.com/fmaclen/hollama","line":367},{"title":"Lollms-Webui","target":"https://github.com/ParisNeo/lollms-webui","line":368},{"title":"LibreChat","target":"https://github.com/danny-avila/LibreChat","line":369},{"title":"Bionic GPT","target":"https://github.com/bionic-gpt/bionic-gpt","line":370},{"title":"HTML UI","target":"https://github.com/rtcfirefly/ollama-ui","line":371},{"title":"Saddle","target":"https://github.com/jikkuatwork/saddle","line":372},{"title":"Chatbot UI","target":"https://github.com/ivanfioravanti/chatbot-ollama","line":373},{"title":"Chatbot UI v2","target":"https://github.com/mckaywrigley/chatbot-ui","line":374},{"title":"Typescript UI","target":"https://github.com/ollama-interface/Ollama-Gui?tab=readme-ov-file","line":375},{"title":"Minimalistic React UI for Ollama Models","target":"https://github.com/richawo/minimal-llm-ui","line":376},{"title":"Ollamac","target":"https://github.com/kevinhermawan/Ollamac","line":377},{"title":"big-AGI","target":"https://github.com/enricoros/big-AGI/blob/main/docs/config-local-ollama.md","line":378},{"title":"Cheshire Cat assistant framework","target":"https://github.com/cheshire-cat-ai/core","line":379},{"title":"Amica","target":"https://github.com/semperai/amica","line":380},{"title":"chatd","target":"https://github.com/BruceMacD/chatd","line":381},{"title":"Ollama-SwiftUI","target":"https://github.com/kghandour/Ollama-SwiftUI","line":382},{"title":"Dify.AI","target":"https://github.com/langgenius/dify","line":383},{"title":"MindMac","target":"https://mindmac.app","line":384},{"title":"NextJS Web Interface for Ollama","target":"https://github.com/jakobhoeg/nextjs-ollama-llm-ui","line":385},{"title":"Msty","target":"https://msty.app","line":386},{"title":"Chatbox","target":"https://github.com/Bin-Huang/Chatbox","line":387},{"title":"WinForm Ollama Copilot","target":"https://github.com/tgraupmann/WinForm_Ollama_Copilot","line":388},{"title":"Get Started Doc","target":"https://docs.nextchat.dev/models/ollama","line":389},{"title":"NextChat","target":"https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web","line":389},{"title":"Alpaca WebUI","target":"https://github.com/mmo80/alpaca-webui","line":390},{"title":"OllamaGUI","target":"https://github.com/enoch1118/ollamaGUI","line":391},{"title":"OpenAOE","target":"https://github.com/InternLM/OpenAOE","line":392},{"title":"Odin Runes","target":"https://github.com/leonid20000/OdinRunes","line":393},{"title":"LLM-X","target":"https://github.com/mrdjohnson/llm-x","line":394},{"title":"AnythingLLM (Docker + MacOs/Windows/Linux native app)","target":"https://github.com/Mintplex-Labs/anything-llm","line":395},{"title":"Ollama Basic Chat: Uses HyperDiv Reactive UI","target":"https://github.com/rapidarchitect/ollama_basic_chat","line":396},{"title":"Ollama-chats RPG","target":"https://github.com/drazdra/ollama-chats","line":397},{"title":"IntelliBar","target":"https://intellibar.app/","line":398},{"title":"QA-Pilot","target":"https://github.com/reid41/QA-Pilot","line":399},{"title":"ChatOllama","target":"https://github.com/sugarforever/chat-ollama","line":400},{"title":"CRAG Ollama Chat","target":"https://github.com/Nagi-ovo/CRAG-Ollama-Chat","line":401},{"title":"RAGFlow","target":"https://github.com/infiniflow/ragflow","line":402},{"title":"StreamDeploy","target":"https://github.com/StreamDeploy-DevRel/streamdeploy-llm-app-scaffold","line":403},{"title":"chat","target":"https://github.com/swuecho/chat","line":404},{"title":"Lobe Chat","target":"https://github.com/lobehub/lobe-chat","line":405},{"title":"Integrating Doc","target":"https://lobehub.com/docs/self-hosting/examples/ollama","line":405},{"title":"Ollama RAG Chatbot","target":"https://github.com/datvodinh/rag-chatbot.git","line":406},{"title":"BrainSoup","target":"https://www.nurgo-software.com/products/brainsoup","line":407},{"title":"macai","target":"https://github.com/Renset/macai","line":408},{"title":"RWKV-Runner","target":"https://github.com/josStorer/RWKV-Runner","line":409},{"title":"Ollama Grid Search","target":"https://github.com/dezoito/ollama-grid-search","line":410},{"title":"Olpaka","target":"https://github.com/Otacon/olpaka","line":411},{"title":"OllamaSpring","target":"https://github.com/CrazyNeil/OllamaSpring","line":412},{"title":"LLocal.in","target":"https://github.com/kartikm7/llocal","line":413},{"title":"Shinkai Desktop","target":"https://github.com/dcSpark/shinkai-apps","line":414},{"title":"AiLama","target":"https://github.com/zeyoyt/ailama","line":415},{"title":"Ollama with Google Mesop","target":"https://github.com/rapidarchitect/ollama_mesop/","line":416},{"title":"R2R","target":"https://github.com/SciPhi-AI/R2R","line":417},{"title":"Ollama-Kis","target":"https://github.com/elearningshow/ollama-kis","line":418},{"title":"OpenGPA","target":"https://opengpa.org","line":419},{"title":"Painting Droid","target":"https://github.com/mateuszmigas/painting-droid","line":420},{"title":"Kerlig AI","target":"https://www.kerlig.com/","line":421},{"title":"AI Studio","target":"https://github.com/MindWorkAI/AI-Studio","line":422},{"title":"Sidellama","target":"https://github.com/gyopak/sidellama","line":423},{"title":"LLMStack","target":"https://github.com/trypromptly/LLMStack","line":424},{"title":"BoltAI for Mac","target":"https://boltai.com","line":425},{"title":"Harbor","target":"https://github.com/av/harbor","line":426},{"title":"PyGPT","target":"https://github.com/szczyglis-dev/py-gpt","line":427},{"title":"Alpaca","target":"https://github.com/Jeffser/Alpaca","line":428},{"title":"AutoGPT","target":"https://github.com/Significant-Gravitas/AutoGPT/blob/master/docs/content/platform/ollama.md","line":429},{"title":"Go-CREW","target":"https://www.jonathanhecl.com/go-crew/","line":430},{"title":"PartCAD","target":"https://github.com/openvmp/partcad/","line":431},{"title":"Ollama4j Web UI","target":"https://github.com/ollama4j/ollama4j-web-ui","line":432},{"title":"PyOllaMx","target":"https://github.com/kspviswa/pyOllaMx","line":433},{"title":"Claude Dev","target":"https://github.com/saoudrizwan/claude-dev","line":434},{"title":"Cherry Studio","target":"https://github.com/kangfenmao/cherry-studio","line":435},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":436},{"title":"Archyve","target":"https://github.com/nickthecook/archyve","line":437},{"title":"crewAI with Mesop","target":"https://github.com/rapidarchitect/ollama-crew-mesop","line":438},{"title":"Tkinter-based client","target":"https://github.com/chyok/ollama-gui","line":439},{"title":"LLMChat","target":"https://github.com/trendy-design/llmchat","line":440},{"title":"Local Multimodal AI Chat","target":"https://github.com/Leon-Sander/Local-Multimodal-AI-Chat","line":441},{"title":"ARGO","target":"https://github.com/xark-argo/argo","line":442},{"title":"OrionChat","target":"https://github.com/EliasPereirah/OrionChat","line":443},{"title":"G1","target":"https://github.com/bklieger-groq/g1","line":444},{"title":"Web management","target":"https://github.com/lemonit-eric-mao/ollama-web-management","line":445},{"title":"Promptery","target":"https://github.com/promptery/promptery","line":446},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":447},{"title":"chat-ollama","target":"https://github.com/annilq/chat-ollama","line":448},{"title":"SpaceLlama","target":"https://github.com/tcsenpai/spacellama","line":449},{"title":"YouLama","target":"https://github.com/tcsenpai/youlama","line":450},{"title":"DualMind","target":"https://github.com/tcsenpai/dualmind","line":451},{"title":"ollamarama-matrix","target":"https://github.com/h1ddenpr0cess20/ollamarama-matrix","line":452},{"title":"ollama-chat-app","target":"https://github.com/anan1213095357/ollama-chat-app","line":453},{"title":"Perfect Memory AI","target":"https://www.perfectmemory.ai/","line":454},{"title":"Hexabot","target":"https://github.com/hexastack/hexabot","line":455},{"title":"Reddit Rate","target":"https://github.com/rapidarchitect/reddit_analyzer","line":456},{"title":"OpenTalkGpt","target":"https://github.com/adarshM84/OpenTalkGpt","line":457},{"title":"VT","target":"https://github.com/vinhnx/vt.ai","line":458},{"title":"Nosia","target":"https://github.com/nosia-ai/nosia","line":459},{"title":"Witsy","target":"https://github.com/nbonamy/witsy","line":460},{"title":"Abbey","target":"https://github.com/US-Artificial-Intelligence/abbey","line":461},{"title":"Minima","target":"https://github.com/dmayboroda/minima","line":462},{"title":"aidful-ollama-model-delete","target":"https://github.com/AidfulAI/aidful-ollama-model-delete","line":463},{"title":"Perplexica","target":"https://github.com/ItzCrazyKns/Perplexica","line":464},{"title":"Ollama Chat WebUI for Docker ","target":"https://github.com/oslook/ollama-webui","line":465},{"title":"AI Toolkit for Visual Studio Code","target":"https://aka.ms/ai-tooklit/ollama-docs","line":466},{"title":"MinimalNextOllamaChat","target":"https://github.com/anilkay/MinimalNextOllamaChat","line":467},{"title":"Chipper","target":"https://github.com/TilmanGriesel/chipper","line":468},{"title":"ChibiChat","target":"https://github.com/CosmicEventHorizon/ChibiChat","line":469},{"title":"LocalLLM","target":"https://github.com/qusaismael/localllm","line":470},{"title":"Ollamazing","target":"https://github.com/buiducnhat/ollamazing","line":471},{"title":"Google Cloud","target":"https://cloud.google.com/run/docs/tutorials/gpu-gemma2-with-ollama","line":475},{"title":"Fly.io","target":"https://fly.io/docs/python/do-more/add-ollama/","line":476},{"title":"Koyeb","target":"https://www.koyeb.com/deploy/ollama","line":477},{"title":"oterm","target":"https://github.com/ggozad/oterm","line":481},{"title":"Ellama Emacs client","target":"https://github.com/s-kostyaev/ellama","line":482},{"title":"Emacs client","target":"https://github.com/zweifisch/ollama","line":483},{"title":"neollama","target":"https://github.com/paradoxical-dev/neollama","line":484},{"title":"gen.nvim","target":"https://github.com/David-Kunz/gen.nvim","line":485},{"title":"ollama.nvim","target":"https://github.com/nomnivore/ollama.nvim","line":486},{"title":"ollero.nvim","target":"https://github.com/marco-souza/ollero.nvim","line":487},{"title":"ollama-chat.nvim","target":"https://github.com/gerazov/ollama-chat.nvim","line":488},{"title":"ogpt.nvim","target":"https://github.com/huynle/ogpt.nvim","line":489},{"title":"gptel Emacs client","target":"https://github.com/karthink/gptel","line":490},{"title":"Oatmeal","target":"https://github.com/dustinblackman/oatmeal","line":491},{"title":"cmdh","target":"https://github.com/pgibler/cmdh","line":492},{"title":"ooo","target":"https://github.com/npahlfer/ooo","line":493},{"title":"shell-pilot","target":"https://github.com/reid41/shell-pilot","line":494},{"title":"tenere","target":"https://github.com/pythops/tenere","line":495},{"title":"llm-ollama","target":"https://github.com/taketwo/llm-ollama","line":496},{"title":"Datasette's LLM CLI","target":"https://llm.datasette.io/en/stable/","line":496},{"title":"typechat-cli","target":"https://github.com/anaisbetts/typechat-cli","line":497},{"title":"ShellOracle","target":"https://github.com/djcopley/ShellOracle","line":498},{"title":"tlm","target":"https://github.com/yusufcanb/tlm","line":499},{"title":"podman-ollama","target":"https://github.com/ericcurtin/podman-ollama","line":500},{"title":"gollama","target":"https://github.com/sammcj/gollama","line":501},{"title":"ParLlama","target":"https://github.com/paulrobello/parllama","line":502},{"title":"Ollama eBook Summary","target":"https://github.com/cognitivetech/ollama-ebook-summary/","line":503},{"title":"Ollama Mixture of Experts (MOE) in 50 lines of code","target":"https://github.com/rapidarchitect/ollama_moe","line":504},{"title":"vim-intelligence-bridge","target":"https://github.com/pepo-ec/vim-intelligence-bridge","line":505},{"title":"x-cmd ollama","target":"https://x-cmd.com/mod/ollama","line":506},{"title":"bb7","target":"https://github.com/drunkwcodes/bb7","line":507},{"title":"SwollamaCLI","target":"https://github.com/marcusziade/Swollama","line":508},{"title":"Demo","target":"https://github.com/marcusziade/Swollama?tab=readme-ov-file#cli-usage","line":508},{"title":"aichat","target":"https://github.com/sigoden/aichat","line":509},{"title":"PowershAI","target":"https://github.com/rrg92/powershai","line":510},{"title":"orbiton","target":"https://github.com/xyproto/orbiton","line":511},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":515},{"title":"pgai","target":"https://github.com/timescale/pgai","line":519},{"title":"Get started guide","target":"https://github.com/timescale/pgai/blob/main/docs/vectorizer-quick-start.md","line":520},{"title":"MindsDB","target":"https://github.com/mindsdb/mindsdb/blob/staging/mindsdb/integrations/handlers/ollama_handler/README.md","line":521},{"title":"chromem-go","target":"https://github.com/philippgille/chromem-go/blob/v0.5.0/embed_ollama.go","line":522},{"title":"example","target":"https://github.com/philippgille/chromem-go/tree/v0.5.0/examples/rag-wikipedia-ollama","line":522},{"title":"Kangaroo","target":"https://github.com/dbkangaroo/kangaroo","line":523},{"title":"Pacman","target":"https://archlinux.org/packages/extra/x86_64/ollama/","line":527},{"title":"Gentoo","target":"https://github.com/gentoo/guru/tree/master/app-misc/ollama","line":528},{"title":"Homebrew","target":"https://formulae.brew.sh/formula/ollama","line":529},{"title":"Helm Chart","target":"https://artifacthub.io/packages/helm/ollama-helm/ollama","line":530},{"title":"Guix channel","target":"https://codeberg.org/tusharhero/ollama-guix","line":531},{"title":"Nix package","target":"https://search.nixos.org/packages?show=ollama&from=0&size=50&sort=relevance&type=packages&query=ollama","line":532},{"title":"Flox","target":"https://flox.dev/blog/ollama-part-one","line":533},{"title":"LangChain.js","target":"https://js.langchain.com/docs/integrations/chat/ollama/","line":537},{"title":"example","target":"https://js.langchain.com/docs/tutorials/local_rag/","line":537},{"title":"LangChain","target":"https://python.langchain.com/docs/integrations/llms/ollama","line":537},{"title":"Firebase Genkit","target":"https://firebase.google.com/docs/genkit/plugins/ollama","line":538},{"title":"crewAI","target":"https://github.com/crewAIInc/crewAI","line":539},{"title":"Yacana","target":"https://remembersoftwares.github.io/yacana/","line":540},{"title":"reference","target":"https://docs.spring.io/spring-ai/reference/api/chat/ollama-chat.html","line":541},{"title":"Spring AI","target":"https://github.com/spring-projects/spring-ai","line":541},{"title":"example","target":"https://github.com/tzolov/ollama-tools","line":541},{"title":"LangChainGo","target":"https://github.com/tmc/langchaingo/","line":542},{"title":"example","target":"https://github.com/tmc/langchaingo/tree/main/examples/ollama-completion-example","line":542},{"title":"LangChain4j","target":"https://github.com/langchain4j/langchain4j","line":543},{"title":"example","target":"https://github.com/langchain4j/langchain4j-examples/tree/main/ollama-examples/src/main/java","line":543},{"title":"LangChainRust","target":"https://github.com/Abraxas-365/langchain-rust","line":544},{"title":"example","target":"https://github.com/Abraxas-365/langchain-rust/blob/main/examples/llm_ollama.rs","line":544},{"title":"LangChain for .NET","target":"https://github.com/tryAGI/LangChain","line":545},{"title":"example","target":"https://github.com/tryAGI/LangChain/blob/main/examples/LangChain.Samples.OpenAI/Program.cs","line":545},{"title":"LLPhant","target":"https://github.com/theodo-group/LLPhant?tab=readme-ov-file#ollama","line":546},{"title":"LlamaIndex","target":"https://docs.llamaindex.ai/en/stable/examples/llm/ollama/","line":547},{"title":"LlamaIndexTS","target":"https://ts.llamaindex.ai/modules/llms/available_llms/ollama","line":547},{"title":"LiteLLM","target":"https://github.com/BerriAI/litellm","line":548},{"title":"OllamaFarm for Go","target":"https://github.com/presbrey/ollamafarm","line":549},{"title":"OllamaSharp for .NET","target":"https://github.com/awaescher/OllamaSharp","line":550},{"title":"Ollama for Ruby","target":"https://github.com/gbaptista/ollama-ai","line":551},{"title":"Ollama-rs for Rust","target":"https://github.com/pepperoni21/ollama-rs","line":552},{"title":"Ollama-hpp for C++","target":"https://github.com/jmont-dev/ollama-hpp","line":553},{"title":"Ollama4j for Java","target":"https://github.com/ollama4j/ollama4j","line":554},{"title":"ModelFusion Typescript Library","target":"https://modelfusion.dev/integration/model-provider/ollama","line":555},{"title":"OllamaKit for Swift","target":"https://github.com/kevinhermawan/OllamaKit","line":556},{"title":"Ollama for Dart","target":"https://github.com/breitburg/dart-ollama","line":557},{"title":"Ollama for Laravel","target":"https://github.com/cloudstudio/ollama-laravel","line":558},{"title":"LangChainDart","target":"https://github.com/davidmigloz/langchain_dart","line":559},{"title":"Semantic Kernel - Python","target":"https://github.com/microsoft/semantic-kernel/tree/main/python/semantic_kernel/connectors/ai/ollama","line":560},{"title":"Haystack","target":"https://github.com/deepset-ai/haystack-integrations/blob/main/integrations/ollama.md","line":561},{"title":"Elixir LangChain","target":"https://github.com/brainlid/langchain","line":562},{"title":"Ollama for R - rollama","target":"https://github.com/JBGruber/rollama","line":563},{"title":"Ollama for R - ollama-r","target":"https://github.com/hauselin/ollama-r","line":564},{"title":"Ollama-ex for Elixir","target":"https://github.com/lebrunel/ollama-ex","line":565},{"title":"Ollama Connector for SAP ABAP","target":"https://github.com/b-tocs/abap_btocs_ollama","line":566},{"title":"Testcontainers","target":"https://testcontainers.com/modules/ollama/","line":567},{"title":"Portkey","target":"https://portkey.ai/docs/welcome/integration-guides/ollama","line":568},{"title":"PromptingTools.jl","target":"https://github.com/svilupp/PromptingTools.jl","line":569},{"title":"example","target":"https://svilupp.github.io/PromptingTools.jl/dev/examples/working_with_ollama","line":569},{"title":"LlamaScript","target":"https://github.com/Project-Llama/llamascript","line":570},{"title":"llm-axe","target":"https://github.com/emirsahin1/llm-axe","line":571},{"title":"Gollm","target":"https://docs.gollm.co/examples/ollama-example","line":572},{"title":"Gollama for Golang","target":"https://github.com/jonathanhecl/gollama","line":573},{"title":"Ollamaclient for Golang","target":"https://github.com/xyproto/ollamaclient","line":574},{"title":"High-level function abstraction in Go","target":"https://gitlab.com/tozd/go/fun","line":575},{"title":"Ollama PHP","target":"https://github.com/ArdaGnsrn/ollama-php","line":576},{"title":"Agents-Flex for Java","target":"https://github.com/agents-flex/agents-flex","line":577},{"title":"example","target":"https://github.com/agents-flex/agents-flex/tree/main/agents-flex-llm/agents-flex-llm-ollama/src/test/java/com/agentsflex/llm/ollama","line":577},{"title":"Parakeet","target":"https://github.com/parakeet-nest/parakeet","line":578},{"title":"Haverscript","target":"https://github.com/andygill/haverscript","line":579},{"title":"examples","target":"https://github.com/andygill/haverscript/tree/main/examples","line":579},{"title":"Ollama for Swift","target":"https://github.com/mattt/ollama-swift","line":580},{"title":"Swollama for Swift","target":"https://github.com/marcusziade/Swollama","line":581},{"title":"DocC","target":"https://marcusziade.github.io/Swollama/documentation/swollama/","line":581},{"title":"GoLamify","target":"https://github.com/prasad89/golamify","line":582},{"title":"Ollama for Haskell","target":"https://github.com/tusharad/ollama-haskell","line":583},{"title":"multi-llm-ts","target":"https://github.com/nbonamy/multi-llm-ts","line":584},{"title":"LlmTornado","target":"https://github.com/lofcz/llmtornado","line":585},{"title":"Ollama for Zig","target":"https://github.com/dravenk/ollama-zig","line":586},{"title":"Abso","target":"https://github.com/lunary-ai/abso","line":587},{"title":"Enchanted","target":"https://github.com/AugustDev/enchanted","line":591},{"title":"Maid","target":"https://github.com/Mobile-Artificial-Intelligence/maid","line":592},{"title":"Ollama App","target":"https://github.com/JHubi1/ollama-app","line":593},{"title":"ConfiChat","target":"https://github.com/1runeberg/confichat","line":594},{"title":"Raycast extension","target":"https://github.com/MassimilianoPasquini97/raycast_ollama","line":598},{"title":"Discollama","target":"https://github.com/mxyng/discollama","line":599},{"title":"Continue","target":"https://github.com/continuedev/continue","line":600},{"title":"Vibe","target":"https://github.com/thewh1teagle/vibe","line":601},{"title":"Obsidian Ollama plugin","target":"https://github.com/hinterdupfinger/obsidian-ollama","line":602},{"title":"Logseq Ollama plugin","target":"https://github.com/omagdy7/ollama-logseq","line":603},{"title":"NotesOllama","target":"https://github.com/andersrex/notesollama","line":604},{"title":"Dagger Chatbot","target":"https://github.com/samalba/dagger-chatbot","line":605},{"title":"Discord AI Bot","target":"https://github.com/mekb-turtle/discord-ai-bot","line":606},{"title":"Ollama Telegram Bot","target":"https://github.com/ruecat/ollama-telegram","line":607},{"title":"Hass Ollama Conversation","target":"https://github.com/ej52/hass-ollama-conversation","line":608},{"title":"Rivet plugin","target":"https://github.com/abrenneke/rivet-plugin-ollama","line":609},{"title":"Obsidian BMO Chatbot plugin","target":"https://github.com/longy2k/obsidian-bmo-chatbot","line":610},{"title":"Cliobot","target":"https://github.com/herval/cliobot","line":611},{"title":"Copilot for Obsidian plugin","target":"https://github.com/logancyang/obsidian-copilot","line":612},{"title":"Obsidian Local GPT plugin","target":"https://github.com/pfrankov/obsidian-local-gpt","line":613},{"title":"Open Interpreter","target":"https://docs.openinterpreter.com/language-model-setup/local-models/ollama","line":614},{"title":"Llama Coder","target":"https://github.com/ex3ndr/llama-coder","line":615},{"title":"Ollama Copilot","target":"https://github.com/bernardo-bruning/ollama-copilot","line":616},{"title":"twinny","target":"https://github.com/rjmacarthy/twinny","line":617},{"title":"Wingman-AI","target":"https://github.com/RussellCanfield/wingman-ai","line":618},{"title":"Page Assist","target":"https://github.com/n4ze3m/page-assist","line":619},{"title":"Plasmoid Ollama Control","target":"https://github.com/imoize/plasmoid-ollamacontrol","line":620},{"title":"AI Telegram Bot","target":"https://github.com/tusharhero/aitelegrambot","line":621},{"title":"AI ST Completion","target":"https://github.com/yaroslavyaroslav/OpenAI-sublime-text","line":622},{"title":"Discord-Ollama Chat Bot","target":"https://github.com/kevinthedang/discord-ollama","line":623},{"title":"ChatGPTBox: All in one browser extension","target":"https://github.com/josStorer/chatGPTBox","line":624},{"title":"Integrating Tutorial","target":"https://github.com/josStorer/chatGPTBox/issues/616#issuecomment-1975186467","line":624},{"title":"Discord AI chat/moderation bot","target":"https://github.com/rapmd73/Companion","line":625},{"title":"Headless Ollama","target":"https://github.com/nischalj10/headless-ollama","line":626},{"title":"Terraform AWS Ollama & Open WebUI","target":"https://github.com/xuyangbocn/terraform-aws-self-host-llm","line":627},{"title":"node-red-contrib-ollama","target":"https://github.com/jakubburkiewicz/node-red-contrib-ollama","line":628},{"title":"Local AI Helper","target":"https://github.com/ivostoykov/localAI","line":629},{"title":"vnc-lm","target":"https://github.com/jake83741/vnc-lm","line":630},{"title":"LSP-AI","target":"https://github.com/SilasMarvin/lsp-ai","line":631},{"title":"QodeAssist","target":"https://github.com/Palm1r/QodeAssist","line":632},{"title":"Obsidian Quiz Generator plugin","target":"https://github.com/ECuiDev/obsidian-quiz-generator","line":633},{"title":"AI Summmary Helper plugin","target":"https://github.com/philffm/ai-summary-helper","line":634},{"title":"TextCraft","target":"https://github.com/suncloudsmoon/TextCraft","line":635},{"title":"Alfred Ollama","target":"https://github.com/zeitlings/alfred-ollama","line":636},{"title":"TextLLaMA","target":"https://github.com/adarshM84/TextLLaMA","line":637},{"title":"Simple-Discord-AI","target":"https://github.com/zyphixor/simple-discord-ai","line":638},{"title":"llama.cpp","target":"https://github.com/ggerganov/llama.cpp","line":642},{"title":"Lunary","target":"https://lunary.ai/docs/integrations/ollama","line":645},{"title":"OpenLIT","target":"https://github.com/openlit/openlit","line":646},{"title":"HoneyHive","target":"https://docs.honeyhive.ai/integrations/ollama","line":647},{"title":"Langfuse","target":"https://langfuse.com/docs/integrations/ollama","line":648},{"title":"MLflow Tracing","target":"https://mlflow.org/docs/latest/llms/tracing/index.html#automatic-tracing","line":649}],"class_name":"SmartBlock"},
"smart_blocks:Ollama/How to start Ollama.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07452817,0.02599237,0.08173883,0.01135041,0.04577705,0.0039413,-0.03678299,0.01949338,0.03653656,0.01692049,0.00428435,-0.03600248,-0.03071324,0.02606766,0.00138543,0.04110929,-0.06344958,0.12386509,0.00774166,0.02003523,0.11874476,-0.07198707,-0.00932296,-0.02764057,0.05834346,0.06492796,-0.0605357,-0.0272908,0.01061415,-0.20327151,-0.0135156,-0.02026504,-0.02418009,-0.03731281,0.01523147,0.01658132,-0.04700865,0.06196424,-0.06229041,0.01173233,0.01315553,0.01243756,-0.01947317,-0.06460302,-0.01778737,0.04792041,0.01970384,0.06162324,0.10769728,-0.05269215,0.00093424,-0.00851239,0.02138598,-0.08268743,0.02340325,0.00718829,0.0443081,0.01121024,-0.03472338,0.05287072,-0.00982315,-0.00349834,-0.14550804,0.07742488,0.06492267,0.0109402,0.05530427,0.03741688,0.04447873,0.10329653,0.00076994,0.04296843,0.00774753,0.01737005,-0.03313722,0.01393789,-0.01073215,-0.00033476,-0.00078075,0.02187693,-0.12679543,-0.06848112,-0.0072699,0.0071766,-0.04714869,0.01314957,0.02090308,-0.00011224,0.03439071,0.01637215,0.00187827,0.02357998,0.06956147,-0.03226087,-0.06417771,-0.03263398,-0.05936419,0.02382742,-0.06545546,0.13846955,-0.05040722,0.03190833,-0.00733894,-0.02036032,0.02851803,-0.01489821,-0.03008162,0.0175988,0.00714877,0.01284716,0.10976426,-0.00026651,0.03248076,-0.1054634,0.04405069,-0.06891514,0.0091811,-0.01112164,-0.06570853,0.0096594,-0.05658678,-0.06477518,0.01384412,-0.0021202,0.00420008,0.01936668,0.05203608,0.04254822,0.05202163,0.03337817,0.01527451,-0.0386464,-0.05169268,0.03242308,-0.01623319,0.03191099,0.0380609,0.02548622,0.03510133,-0.07254277,-0.01299526,-0.03972431,0.01598869,-0.08405408,0.00623276,0.00960522,-0.03244366,0.0018112,-0.01619409,-0.02179057,-0.00428623,0.0266273,-0.04132026,-0.03452577,0.03075324,-0.06000682,0.03970532,0.0918338,-0.01822458,-0.00313965,-0.06142675,-0.03055014,-0.04693306,0.12034453,-0.01304101,-0.01573266,-0.00751581,-0.0020254,-0.06043797,-0.00805191,0.01105546,0.03858782,-0.02060521,0.00214984,0.07895468,-0.03925662,-0.04129362,0.00941967,-0.02251097,0.02036922,0.03146562,-0.03273537,-0.00130797,-0.0310437,0.0018989,-0.0365133,0.05911412,-0.06589768,0.01903149,0.00524817,-0.13507265,-0.00836516,-0.01902865,-0.00641874,0.0001279,-0.01954064,-0.01276061,-0.03538714,0.01161804,-0.03252132,0.05896063,0.05812893,-0.03248483,0.06977244,-0.0363184,0.01110033,0.01819754,-0.08619598,0.07528194,0.02778276,-0.05267426,-0.0172774,0.06073583,-0.0039547,-0.01561314,-0.00608542,0.03343268,0.04782605,-0.01137362,0.02271986,0.05099151,0.02348525,-0.06456932,-0.2232423,0.03676962,-0.00637504,0.03034298,-0.04870919,-0.06982395,0.0319731,-0.06348024,-0.02187396,0.05215236,0.03571064,-0.02582612,-0.00979475,0.00940689,-0.00060811,0.02949273,0.00508692,-0.0109248,-0.02202425,0.04409992,0.06862214,-0.05776146,0.02670674,-0.06934258,0.00253095,-0.02973818,0.21194573,0.03684297,0.02877988,-0.04092776,-0.00040106,0.04093305,-0.11596957,-0.1570822,0.00571511,0.03974195,0.03698734,0.04639497,0.08591209,-0.00266933,0.04245577,0.04992351,-0.03183667,-0.04587992,-0.02780522,-0.03474406,0.03164241,-0.02343507,0.0086899,-0.03197856,0.02305733,0.00233419,0.08489578,0.07450128,-0.03472149,-0.01763886,-0.0705955,-0.00365932,-0.03613902,-0.00147273,-0.03356386,0.04850772,-0.00244669,-0.0768641,0.02535972,0.07248416,0.01064571,-0.01438613,0.07192082,-0.00044612,-0.04183779,0.07942324,-0.01624463,-0.00411305,0.00543616,0.02216171,0.02082325,-0.05295175,-0.0680977,-0.0026333,-0.02330902,-0.04244987,0.02063831,-0.0182701,-0.05013628,0.02531699,-0.01408841,-0.05192922,0.04647029,-0.03329997,-0.00730289,0.05424478,-0.02523719,0.00417594,0.11748452,-0.00335242,-0.2341274,0.05391462,0.06049391,0.00449187,-0.03676019,0.08428518,0.02291393,-0.03540617,-0.00870407,-0.02307384,-0.09535487,0.04590846,0.08035599,-0.0562266,-0.00697962,0.00637587,0.03127826,0.01789562,0.05990219,0.03917556,0.03421522,-0.02455628,0.18927084,-0.03761974,0.02898385,-0.01764729,0.02619848,-0.01151036,0.03525934,-0.01954396,0.00929952,0.02566656,0.02874017,-0.05397085,0.07071425,-0.04139479,-0.04369008,0.04247181,0.03273744,-0.04463953,-0.12279268,0.03761984,-0.00918292,-0.01000651,0.0190519,-0.07328428,-0.04846637,0.02734205,0.01830507,0.02031813,-0.02844401,0.01249851,0.00762538,0.0417662,-0.00935769,0.05544021,-0.03136466,0.01234691,-0.04599327,-0.00039659,0.01540691,0.04453288,0.02205908,-0.0142603,0.05220673],"last_embed":{"hash":null,"tokens":24}}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Ollama/How to start Ollama.md#---frontmatter---","lines":[71,null],"size":0,"outlinks":[],"class_name":"SmartBlock"},
