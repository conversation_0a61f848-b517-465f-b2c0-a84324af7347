
"smart_sources:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md": {"path":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1qmms14","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363607249,"size":16419,"at":1753423416500,"hash":"1qmms14"},"blocks":{"#---frontmatter---":[1,6],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program":[8,135],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}":[10,15],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)":[16,20],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)#{1}":[17,20],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>":[21,70],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{1}":[23,24],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{2}":[25,26],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{3}":[27,53],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{4}":[54,55],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{5}":[56,56],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{6}":[57,62],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{7}":[63,63],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{8}":[64,64],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{9}":[65,65],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{10}":[66,66],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{11}":[67,68],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{12}":[69,70],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>":[71,111],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{1}":[73,76],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{2}":[77,77],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{3}":[78,78],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{4}":[79,79],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{5}":[80,80],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{6}":[81,81],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{7}":[82,82],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{8}":[83,83],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{9}":[84,84],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{10}":[85,86],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{11}":[87,98],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{12}":[99,99],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{13}":[100,100],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{14}":[101,102],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{15}":[103,111],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>":[112,135],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{1}":[114,114],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{2}":[115,115],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{3}":[116,116],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{4}":[117,117],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{5}":[118,118],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{6}":[119,119],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{7}":[120,120],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{8}":[121,121],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{9}":[122,122],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{10}":[123,123],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{11}":[124,124],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{12}":[125,125],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{13}":[126,126],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{14}":[127,127],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{15}":[128,129],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{16}":[130,135]},"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"First Lotto Program Based on _Markov Chains_: _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":16},{"title":"Ion Saliu's Algorithm for Enhanced _Markov Chains_ Lottery Software","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":17},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":19},{"title":"_**thornc Cristiano Lopes programming**_","target":"https://saliu.com/programming.html","line":25},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":69},{"title":"**wonder grid** lotto strategy","target":"https://saliu.com/bbs/messages/9.html","line":78},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":85},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":87},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":91},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":95},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":97},{"title":"**_markov.exe_ lotto program**","target":"https://saliu.com/freeware/markov.exe","line":99},{"title":"_**Markov Lotto Program: Help, Instructions**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":101},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":103},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":110},{"title":"**Theory of Probability**","target":"https://saliu.com/theory-of-probability.html","line":114},{"title":"software","target":"https://saliu.com/infodown.html","line":115},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/Saliu2.htm","line":116},{"title":"Mathematics of _**Fundamental Formula of Gambling**_","target":"https://saliu.com/formula.htm","line":117},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html","line":118},{"title":"_**Software, Formulae Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/oddslotto.html","line":119},{"title":"_**Online Probability, Odds Calculator**_","target":"https://saliu.com/online_odds.html","line":120},{"title":"_**Software, Formulae to Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/bbs/messages/266.html","line":121},{"title":"_**Standard Deviation, Gauss, Normal, Binomial, Distribution**_","target":"https://saliu.com/formula.html","line":122},{"title":"_**Lotto**_ **<u>Wonder Grid</u>**, _**Super Loto Strategy, System**_.","target":"https://saliu.com/bbs/messages/grid.html","line":124},{"title":"_**Randomness, Degree of Randomness, Degrees of Certainty**_.","target":"https://saliu.com/bbs/messages/683.html","line":125},{"title":"_**CoolRevGui**_: _Definitive File Reverser, Shuffler, Text Viewer Software_","target":"https://saliu.com/programming.html","line":127},{"title":"_**UPDOWN**_: _Reverse Order in Lottery Results, Text Files_","target":"https://saliu.com/bbs/messages/539.html","line":128},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":130},{"title":"Forums","target":"https://forums.saliu.com/","line":132},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":132},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":132},{"title":"Contents","target":"https://saliu.com/content/index.html","line":132},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":132},{"title":"Home","target":"https://saliu.com/index.htm","line":132},{"title":"Search","target":"https://saliu.com/Search.htm","line":132},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":132},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":134}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["Markov Chains","games","gambling","software","formula","probability","lottery","lotto","mathematics","random","lotto-6","source code","programming","algorithm","number","follower","lotto pair"],"source":"https://saliu.com/Markov_Chains.html","author":null}},
"smart_sources:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md": {"path":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.16428803,-0.04059322,-0.08089766,-0.0400208,-0.03715488,0.06753862,-0.01351763,0.02609823,0.05925658,0.01138221,0.01089548,-0.02591701,0.04909807,0.03726497,-0.00629662,-0.01899529,-0.0308271,0.02333347,-0.03893746,-0.0517658,0.04863295,-0.0754471,-0.05381423,-0.08381428,0.05606386,0.0441011,-0.01368434,-0.07337163,-0.01598595,-0.211597,0.03180134,-0.01255654,0.03598471,-0.03544545,-0.08515538,0.00417513,0.0045675,0.03706206,-0.06391595,0.02386301,0.01076543,0.02221318,0.00277487,-0.04961149,0.0234667,-0.05721907,-0.01058976,-0.02225713,-0.02560444,-0.02797567,-0.10175724,-0.00175205,0.02605922,0.03010793,0.06802679,-0.00067894,0.05211434,0.05943435,-0.00385685,0.03427336,0.02134625,0.05433704,-0.18341605,0.06578742,-0.00313391,0.04063053,-0.02073639,-0.00747335,0.04219315,0.06817987,0.01781345,0.02222395,-0.02132644,0.04887331,0.06372154,-0.040029,0.01120092,-0.04243346,-0.02763161,-0.0020687,-0.03280798,-0.035485,-0.01283533,-0.02794894,-0.02534113,0.05661991,0.03557388,-0.00888886,0.00806563,-0.03801449,0.01494957,0.04985873,0.05931336,0.04673629,0.01525383,0.03489526,0.0586035,0.00050347,0.03133026,0.11253352,0.02720813,0.00928678,-0.00021908,0.01874951,0.06695096,-0.0240607,-0.00322963,-0.05530083,-0.009396,-0.00876182,0.02226647,-0.01349813,0.06029246,-0.04961316,-0.02281844,0.00141793,0.00940422,0.06011848,0.01744566,0.02321813,-0.01584895,-0.00848162,0.03407319,0.0095827,-0.01619699,0.02328725,-0.03499288,0.09980877,0.04843693,0.03088152,0.06923347,0.00465723,-0.10868382,-0.00638952,0.00828027,-0.01073852,-0.01872105,-0.04929894,0.00240165,-0.01077623,-0.03266233,-0.0154337,0.02425885,-0.10499432,-0.04534815,0.10257277,0.02189708,-0.01808362,0.00612606,-0.01803102,0.0184532,-0.00570307,-0.04907485,-0.06239625,-0.00110562,0.01952134,0.08752491,0.05367356,-0.00548148,0.01121421,-0.02685864,-0.05229421,-0.02488698,0.16468443,-0.00095124,-0.11405253,0.00412492,0.03284305,-0.00939264,-0.06427705,0.03779809,0.05798246,-0.04776765,0.0591015,0.06064754,-0.04717841,-0.09572628,-0.02781476,0.02057164,0.00290445,-0.01134716,-0.04572231,-0.03999693,-0.00545571,-0.03493243,-0.08239567,0.01516904,0.01020796,-0.00888195,0.02632766,0.0219883,-0.03359666,-0.01816389,-0.019689,-0.05087564,-0.04865833,-0.02582868,-0.03375228,0.0386217,-0.05163715,-0.03615424,0.00486302,0.00995234,0.02057202,-0.05354275,-0.00170701,-0.01898874,-0.02291966,0.04903991,0.0337194,-0.00878725,-0.00255503,0.02191473,0.08036789,-0.05399003,0.02923015,0.03580107,0.02565219,0.03994175,0.02240058,-0.01786967,0.02341912,-0.06581234,-0.17343329,-0.01708035,-0.02941963,0.0197957,0.02898144,-0.0625284,0.0064534,-0.07069193,0.0364777,0.06554513,0.0803228,-0.06338608,-0.01673115,-0.01564595,0.00006631,0.02997021,-0.07444083,-0.01914391,-0.02076148,0.06554323,-0.01766326,0.03814566,-0.05965691,-0.08466512,0.03190771,-0.03371551,0.14143118,0.05171579,-0.02760787,0.043625,0.04817656,0.00354288,-0.04972909,-0.07679318,-0.01155759,0.04223347,-0.00233981,0.00420486,-0.01361233,-0.03693688,-0.08010468,0.02463985,0.02627669,-0.06240364,-0.04199374,0.00167831,-0.01792318,0.00079847,0.0076942,0.03013432,-0.00295129,0.01372022,0.04068261,-0.01900265,0.03189021,-0.04143247,-0.05224988,-0.02610083,-0.00782126,0.06795243,-0.00591381,-0.04630566,0.03461107,-0.01060788,0.05563878,-0.02831155,-0.00079463,-0.04208881,0.01240177,0.0018163,-0.02125121,0.11411039,0.04011379,-0.00071069,0.00759744,0.00285392,-0.00548493,-0.05074735,0.00111819,-0.03463116,0.0186936,-0.07599411,0.0325154,0.08502428,0.05649252,0.02003358,0.07832842,0.06280712,-0.00590224,0.04089373,0.01118212,0.0025774,-0.00454969,0.03482675,0.05845115,0.03431296,-0.28934714,0.04051623,0.00080636,0.05729273,0.0119056,-0.00735965,0.06039048,-0.0148457,0.01558991,-0.0959309,0.05126995,0.05521903,0.04792781,-0.0260677,-0.0156999,-0.01942266,-0.01683676,-0.03930993,0.05633527,0.02236532,0.04045074,0.01656042,0.24652018,-0.03294711,0.02332488,0.01233467,0.01188038,0.02417087,0.0115282,0.0315749,-0.02496972,0.01705449,0.06388402,-0.03966891,0.00293644,0.04378376,-0.01009086,0.01843511,-0.02460945,0.00967257,-0.04912787,-0.00970904,-0.02938236,0.02148067,0.1470623,0.05839461,0.01289886,-0.10345885,0.03380182,0.10906247,-0.0627818,-0.09595338,-0.03145415,0.00290516,-0.01056472,0.01412992,-0.00432859,-0.02189104,-0.01062405,0.01219232,0.02780796,-0.02660356,0.07371929,0.04547232,0.00895833],"last_embed":{"hash":"1qmms14","tokens":482}}},"last_read":{"hash":"1qmms14","at":1753423556694},"class_name":"SmartSource","last_import":{"mtime":1753363607249,"size":16419,"at":1753423416500,"hash":"1qmms14"},"blocks":{"#---frontmatter---":[1,6],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program":[8,135],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}":[10,15],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)":[16,20],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)#{1}":[17,20],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>":[21,70],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{1}":[23,24],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{2}":[25,26],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{3}":[27,53],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{4}":[54,55],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{5}":[56,56],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{6}":[57,62],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{7}":[63,63],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{8}":[64,64],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{9}":[65,65],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{10}":[66,66],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{11}":[67,68],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{12}":[69,70],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>":[71,111],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{1}":[73,76],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{2}":[77,77],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{3}":[78,78],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{4}":[79,79],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{5}":[80,80],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{6}":[81,81],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{7}":[82,82],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{8}":[83,83],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{9}":[84,84],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{10}":[85,86],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{11}":[87,98],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{12}":[99,99],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{13}":[100,100],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{14}":[101,102],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{15}":[103,111],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>":[112,135],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{1}":[114,114],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{2}":[115,115],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{3}":[116,116],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{4}":[117,117],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{5}":[118,118],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{6}":[119,119],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{7}":[120,120],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{8}":[121,121],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{9}":[122,122],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{10}":[123,123],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{11}":[124,124],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{12}":[125,125],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{13}":[126,126],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{14}":[127,127],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{15}":[128,129],"#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{16}":[130,135]},"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"First Lotto Program Based on _Markov Chains_: _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":16},{"title":"Ion Saliu's Algorithm for Enhanced _Markov Chains_ Lottery Software","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":17},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":19},{"title":"_**thornc Cristiano Lopes programming**_","target":"https://saliu.com/programming.html","line":25},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":69},{"title":"**wonder grid** lotto strategy","target":"https://saliu.com/bbs/messages/9.html","line":78},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":85},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":87},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":91},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":95},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":97},{"title":"**_markov.exe_ lotto program**","target":"https://saliu.com/freeware/markov.exe","line":99},{"title":"_**Markov Lotto Program: Help, Instructions**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":101},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":103},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":110},{"title":"**Theory of Probability**","target":"https://saliu.com/theory-of-probability.html","line":114},{"title":"software","target":"https://saliu.com/infodown.html","line":115},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/Saliu2.htm","line":116},{"title":"Mathematics of _**Fundamental Formula of Gambling**_","target":"https://saliu.com/formula.htm","line":117},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html","line":118},{"title":"_**Software, Formulae Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/oddslotto.html","line":119},{"title":"_**Online Probability, Odds Calculator**_","target":"https://saliu.com/online_odds.html","line":120},{"title":"_**Software, Formulae to Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/bbs/messages/266.html","line":121},{"title":"_**Standard Deviation, Gauss, Normal, Binomial, Distribution**_","target":"https://saliu.com/formula.html","line":122},{"title":"_**Lotto**_ **<u>Wonder Grid</u>**, _**Super Loto Strategy, System**_.","target":"https://saliu.com/bbs/messages/grid.html","line":124},{"title":"_**Randomness, Degree of Randomness, Degrees of Certainty**_.","target":"https://saliu.com/bbs/messages/683.html","line":125},{"title":"_**CoolRevGui**_: _Definitive File Reverser, Shuffler, Text Viewer Software_","target":"https://saliu.com/programming.html","line":127},{"title":"_**UPDOWN**_: _Reverse Order in Lottery Results, Text Files_","target":"https://saliu.com/bbs/messages/539.html","line":128},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":130},{"title":"Forums","target":"https://forums.saliu.com/","line":132},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":132},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":132},{"title":"Contents","target":"https://saliu.com/content/index.html","line":132},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":132},{"title":"Home","target":"https://saliu.com/index.htm","line":132},{"title":"Search","target":"https://saliu.com/Search.htm","line":132},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":132},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":134}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["Markov Chains","games","gambling","software","formula","probability","lottery","lotto","mathematics","random","lotto-6","source code","programming","algorithm","number","follower","lotto pair"],"source":"https://saliu.com/Markov_Chains.html","author":null}},"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.15344173,-0.05360666,-0.0666222,-0.02092788,-0.02841202,0.05550294,-0.01507058,0.02175967,0.06652941,0.01057595,0.02146355,-0.02924673,0.03867503,0.04932799,-0.00575349,-0.00137746,-0.03480799,0.01014611,-0.01512194,-0.02946176,0.07494032,-0.06029071,-0.06416944,-0.08901259,0.04198682,0.05709964,-0.00372115,-0.06096666,-0.00660069,-0.16545439,-0.00010155,-0.00490928,0.02046252,-0.03497323,-0.05420152,-0.02473631,-0.02037985,0.04284674,-0.04519365,0.0422608,-0.00026143,0.04064107,-0.0171238,-0.03973442,0.02129485,-0.0495487,-0.01832099,-0.01996513,-0.01418617,-0.00903841,-0.08811319,0.00687351,0.00655198,0.04934884,0.06611431,-0.00713861,0.03726554,0.05152218,0.0127607,0.03385552,0.0276396,0.04299619,-0.18920399,0.06556883,0.0104447,0.03463805,-0.0306963,0.00482881,0.01302433,0.07104175,0.0189485,0.03142881,-0.02063928,0.05420704,0.05946025,-0.02988439,-0.00441307,-0.05629113,-0.02781728,-0.00224555,-0.04496806,-0.04817083,-0.03861998,-0.04225652,-0.03291328,0.05151552,0.05479125,0.02804643,0.02665069,-0.02288655,-0.01030648,0.05381168,0.08692199,0.04497584,-0.01625237,0.05274153,0.05898723,0.00772343,0.03015821,0.119027,-0.00573845,0.01738378,-0.01082839,0.00925635,0.0659938,-0.01506817,-0.01188406,-0.05303192,-0.01829039,-0.0020215,0.02798851,0.02135658,0.08483814,-0.09595986,-0.00104622,-0.01301155,0.01640027,0.05570308,0.01179126,0.02293739,-0.03676188,-0.00892124,0.04673257,0.03000257,-0.00323699,0.0387642,-0.02577693,0.09545164,0.03437679,0.01510929,0.06363087,-0.00264431,-0.09665281,0.01004414,0.01260157,-0.01456632,0.0079076,-0.07237356,0.00998987,-0.0282102,-0.03537253,-0.00832149,0.02678819,-0.106107,-0.04594101,0.10089973,0.02471203,-0.01218867,0.00379665,-0.01353051,0.00505131,-0.0012588,0.00244414,-0.06452476,0.00858787,0.0180503,0.09735837,0.05993385,0.00038589,0.00106856,-0.01228841,-0.06186382,-0.03529653,0.1906067,-0.02137746,-0.11708972,-0.010242,0.03669854,0.00034246,-0.08033849,0.02431651,0.03417328,-0.04128638,0.06248499,0.07439677,-0.03715269,-0.05183254,0.00291551,0.01016559,0.00900895,-0.02190022,-0.03902363,-0.05371149,0.00753466,-0.03031815,-0.05899015,-0.00845826,0.00494191,0.01770076,0.02899294,0.02005925,-0.05852315,-0.01310403,-0.01399885,-0.0436888,-0.04853579,-0.02938266,-0.0323952,0.04240106,-0.05088462,-0.01828317,0.00491577,-0.00887479,0.01606278,-0.0398894,-0.01229363,-0.03371127,-0.02647618,0.02255897,0.02969982,0.00215219,0.00783697,0.02061294,0.068599,-0.07620277,0.00228629,0.01030511,0.0182279,0.01359496,0.00430237,-0.04691231,0.00608421,-0.08049845,-0.19157013,-0.00121726,-0.02988881,0.01633156,-0.0027663,-0.03250107,-0.01297477,-0.06093412,0.03718441,0.08584104,0.07362194,-0.06270419,-0.01701523,-0.04530216,-0.00777541,0.02427895,-0.06636979,-0.03546953,-0.02230258,0.07211497,-0.01531081,0.01123157,-0.0628871,-0.11531524,0.03134172,-0.02579018,0.14005557,0.07350028,-0.01040308,0.02246262,0.07370526,0.01361369,-0.03510769,-0.07403353,-0.0041405,0.0376203,-0.02835319,0.02515544,-0.02849961,-0.04831509,-0.08197159,0.03280745,0.02444335,-0.06448574,-0.01379187,-0.021413,-0.01137234,-0.00755073,0.0040667,0.02984129,-0.01679598,0.01151728,0.03782333,-0.01647996,0.03406089,-0.02029359,-0.04247869,-0.04857963,-0.00229918,0.0340581,0.00656846,-0.05552851,0.02574698,0.00705777,0.02414708,-0.01620281,0.00781354,-0.0724934,-0.00183411,-0.0160755,0.01057653,0.11562828,0.04037542,-0.02180585,-0.00114973,-0.01678082,-0.00155278,-0.0416435,0.00655843,-0.03596932,0.01208143,-0.05466202,0.05027018,0.09714888,0.05675182,0.00283743,0.05508912,0.05632045,0.02094351,0.03582485,0.01945263,0.01657346,-0.00503991,0.01967468,0.07243548,0.02020689,-0.2917653,0.05646818,0.03016115,0.05773949,0.0152534,0.01461165,0.04927134,-0.01541011,-0.02290087,-0.06845296,0.05580067,0.06473672,0.046856,-0.01304635,-0.01446028,-0.00432883,0.0113995,-0.03593542,0.06858825,0.0350661,0.02748321,0.02170959,0.25266513,-0.01732947,0.02440974,0.00914879,0.00219087,0.04028549,0.02105043,0.03584758,-0.0124416,0.01934046,0.06846044,-0.03023701,0.01738354,0.03908675,-0.01267165,0.01938123,-0.01612433,0.02340961,-0.07146563,-0.01539338,-0.03945037,0.02918251,0.13052528,0.07878111,-0.02152954,-0.10907384,0.01503135,0.07918889,-0.06186174,-0.10148577,-0.04098716,-0.00740529,-0.02457303,0.03094477,0.03022691,-0.03882796,-0.00578284,0.00120244,0.02182045,-0.04593052,0.06192949,0.02702912,-0.00550048],"last_embed":{"hash":"109zw21","tokens":114}}},"text":null,"length":0,"last_read":{"hash":"109zw21","at":1753423554437},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#---frontmatter---","lines":[1,6],"size":272,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.15161388,-0.04647625,-0.06725027,-0.04841863,-0.03974821,0.06618264,0.009869,0.02907634,0.06184648,-0.00333822,0.01461522,-0.01446269,0.03273569,0.04856635,0.00329202,-0.03203899,-0.02539019,0.03798335,-0.02855728,-0.0480185,0.05523883,-0.07587346,-0.05477029,-0.09826464,0.05596976,0.02372725,-0.01416809,-0.06598569,-0.02771414,-0.23213588,0.0385271,0.00034456,0.04477171,-0.04074015,-0.07923631,-0.01059046,-0.00374614,0.04674986,-0.07837653,0.00898916,0.02240226,0.00305715,0.00381358,-0.04643772,0.02835939,-0.08236388,-0.01330572,-0.01172211,-0.02676251,-0.04136524,-0.10301794,-0.00109385,0.03163021,0.03574584,0.06461948,-0.00400072,0.07754069,0.06609544,0.00265449,0.03457785,0.01218011,0.05762639,-0.17842394,0.04914983,-0.0034001,0.03006496,-0.03218738,-0.016841,0.04033173,0.06175172,0.0227251,0.01133698,-0.00694167,0.05704586,0.0645766,-0.05857362,-0.00376504,-0.0307217,-0.02584728,0.00851415,-0.01564107,-0.03134856,-0.00271569,-0.02599807,-0.02611381,0.06383749,0.04121369,-0.01734181,0.00529313,-0.04483426,0.03050649,0.05222945,0.05270708,0.0339965,0.02265974,0.02971982,0.05159751,0.00144279,0.02688635,0.11638837,0.03490213,-0.02597448,0.00544783,0.02105077,0.06091005,-0.0070218,0.00584631,-0.04522891,-0.00257287,-0.01208643,0.021462,-0.02709991,0.05260323,-0.02451642,-0.04301178,0.0037103,-0.00331386,0.05424912,0.01004407,0.01183911,-0.00771107,0.01447601,0.02593096,0.00619359,-0.01907279,0.00710756,-0.01767203,0.10779958,0.04188028,0.03540103,0.04333154,0.00962048,-0.09559779,-0.00444846,0.00167342,-0.01218151,-0.02623351,-0.02697,-0.01059806,0.00668675,-0.02354617,-0.00115135,0.01709932,-0.08813918,-0.02783685,0.08833686,0.02078141,-0.00296598,0.00226193,-0.01564384,0.03406867,-0.01071393,-0.05252709,-0.06965354,-0.00636158,0.01964752,0.08282734,0.05462198,-0.01424111,0.02495598,-0.04360343,-0.03899173,-0.01124846,0.15553296,-0.0075219,-0.10049576,0.01584738,0.05131152,-0.01450628,-0.06310058,0.03806151,0.06140664,-0.05480829,0.04909139,0.05072145,-0.0502449,-0.10704513,-0.04473188,0.01464558,0.00862084,-0.00372687,-0.03915707,-0.03748373,-0.00948431,-0.03357725,-0.08772995,0.02399005,0.01367522,-0.01172956,0.02144515,0.01133605,-0.00246527,-0.02878646,-0.02988914,-0.04968266,-0.05197839,-0.0195175,-0.02875183,0.03466666,-0.0431186,-0.0406656,-0.0078566,0.01611495,0.03017857,-0.05792192,0.01232993,-0.0306967,-0.03746895,0.05451613,0.04585632,-0.00245074,0.0002007,0.00847301,0.08673226,-0.04590365,0.04379824,0.04548063,0.02481277,0.03527803,0.03799481,-0.01358961,0.02093421,-0.06496856,-0.17447692,-0.02477129,-0.03114556,0.02221518,0.04970003,-0.06438182,0.0057607,-0.06900407,0.04248964,0.06051224,0.07546869,-0.07338913,-0.0266061,0.0071718,0.00170815,0.01833025,-0.07394482,-0.02464326,-0.02917418,0.05160679,-0.01966834,0.04952089,-0.05649427,-0.08822291,0.03133171,-0.02292688,0.1416411,0.03619527,-0.02337741,0.03864695,0.03473241,-0.00917272,-0.04830813,-0.07371012,-0.01885151,0.05124323,0.01215804,0.00105383,-0.00469524,-0.03448604,-0.07676892,0.03713226,0.0211394,-0.05480794,-0.05563382,0.00956605,-0.01991344,0.00362127,0.01057144,0.04608936,0.00040414,0.0230811,0.04543078,-0.00518993,0.03658874,-0.04393747,-0.05939595,-0.01721002,0.01311333,0.07649327,-0.00955699,-0.05652906,0.03083718,-0.01308724,0.08406614,-0.03918314,0.00104268,-0.02691512,0.02347305,0.0007263,-0.03795043,0.11170046,0.02770931,-0.00271018,0.02477929,0.01552899,0.00529218,-0.06508429,0.00108668,-0.024011,0.01647866,-0.07396382,0.03394971,0.06658173,0.03357016,0.00900933,0.07688747,0.06057989,-0.02661277,0.04003577,0.01645258,0.0036909,-0.0174125,0.05153669,0.03771001,0.03457476,-0.28521904,0.03607103,-0.02427389,0.05692698,0.01487865,-0.01840111,0.05962601,-0.01124705,0.01797345,-0.08826201,0.05640167,0.0501012,0.05892265,-0.04547903,-0.01661256,-0.03063882,-0.01250165,-0.04969654,0.05280736,0.01698321,0.05245654,0.03693565,0.24898994,-0.02986296,0.0215659,0.01592484,0.00818398,0.01934532,0.00840463,0.03268202,-0.01368431,-0.00128253,0.04850749,-0.03814021,0.00274837,0.04516962,-0.01214843,-0.00295803,-0.02164941,0.00005755,-0.03946168,-0.00415176,-0.02346273,0.02453924,0.1505428,0.04296719,0.00435904,-0.09265742,0.04039298,0.1000533,-0.06063174,-0.10097964,-0.03269662,-0.00697061,-0.00284918,0.0147306,-0.02168035,-0.0086873,-0.02708198,0.00502758,0.02510161,-0.00865554,0.07336149,0.05095201,0.00107833],"last_embed":{"hash":"1s16pv5","tokens":483}}},"text":null,"length":0,"last_read":{"hash":"1s16pv5","at":1753423554487},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program","lines":[8,135],"size":16087,"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"First Lotto Program Based on _Markov Chains_: _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":9},{"title":"Ion Saliu's Algorithm for Enhanced _Markov Chains_ Lottery Software","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":10},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":12},{"title":"_**thornc Cristiano Lopes programming**_","target":"https://saliu.com/programming.html","line":18},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":62},{"title":"**wonder grid** lotto strategy","target":"https://saliu.com/bbs/messages/9.html","line":71},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":78},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":80},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":84},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":88},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":90},{"title":"**_markov.exe_ lotto program**","target":"https://saliu.com/freeware/markov.exe","line":92},{"title":"_**Markov Lotto Program: Help, Instructions**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":94},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":96},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":103},{"title":"**Theory of Probability**","target":"https://saliu.com/theory-of-probability.html","line":107},{"title":"software","target":"https://saliu.com/infodown.html","line":108},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/Saliu2.htm","line":109},{"title":"Mathematics of _**Fundamental Formula of Gambling**_","target":"https://saliu.com/formula.htm","line":110},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html","line":111},{"title":"_**Software, Formulae Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/oddslotto.html","line":112},{"title":"_**Online Probability, Odds Calculator**_","target":"https://saliu.com/online_odds.html","line":113},{"title":"_**Software, Formulae to Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/bbs/messages/266.html","line":114},{"title":"_**Standard Deviation, Gauss, Normal, Binomial, Distribution**_","target":"https://saliu.com/formula.html","line":115},{"title":"_**Lotto**_ **<u>Wonder Grid</u>**, _**Super Loto Strategy, System**_.","target":"https://saliu.com/bbs/messages/grid.html","line":117},{"title":"_**Randomness, Degree of Randomness, Degrees of Certainty**_.","target":"https://saliu.com/bbs/messages/683.html","line":118},{"title":"_**CoolRevGui**_: _Definitive File Reverser, Shuffler, Text Viewer Software_","target":"https://saliu.com/programming.html","line":120},{"title":"_**UPDOWN**_: _Reverse Order in Lottery Results, Text Files_","target":"https://saliu.com/bbs/messages/539.html","line":121},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":123},{"title":"Forums","target":"https://forums.saliu.com/","line":125},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":125},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":125},{"title":"Contents","target":"https://saliu.com/content/index.html","line":125},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":125},{"title":"Home","target":"https://saliu.com/index.htm","line":125},{"title":"Search","target":"https://saliu.com/Search.htm","line":125},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":125},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":127}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1351368,-0.03421232,-0.05690749,-0.05087453,-0.05245711,0.06794456,0.01886917,0.00803055,0.04072604,-0.01848353,0.0189977,-0.03739988,0.05811806,0.02721657,-0.00765517,-0.01009073,-0.03604229,0.05675453,-0.03338648,-0.06489989,0.07578399,-0.06795736,-0.06442056,-0.08373693,0.05382968,0.05417441,-0.02072196,-0.07616396,0.00144715,-0.17275657,0.02502512,0.00395305,0.0557223,-0.04136154,-0.07404575,0.00926713,-0.00863794,0.05249463,-0.08559182,0.0316925,0.01610124,0.01358765,-0.00791065,-0.02910448,0.02614485,-0.06942537,-0.00609355,-0.01829445,-0.01708334,-0.0398959,-0.09007432,-0.00048376,0.02329761,0.02789048,0.06050853,-0.01667333,0.06017524,0.06637304,-0.00038633,0.03135731,0.04163776,0.0386827,-0.17375101,0.05194338,0.0109537,0.0447559,-0.00361001,-0.02257224,0.04742999,0.09432457,0.00640334,0.01878298,-0.02677966,0.056591,0.0434981,-0.03859452,-0.00455688,-0.04114252,-0.01764846,0.00404062,-0.04106551,-0.0351497,0.00683841,-0.01113795,-0.03761927,0.06614174,0.03820726,-0.00036131,0.00503905,-0.04450204,0.01779371,0.05727259,0.04132549,0.03530964,-0.00129734,0.02119945,0.05204118,-0.03248959,0.05659438,0.14199972,0.01709754,-0.01047505,0.00569738,0.02231294,0.04679224,-0.0124343,-0.01318681,-0.06224127,0.00197806,0.0180277,0.00426371,-0.01249656,0.06371521,-0.0550437,-0.02836792,-0.01346381,-0.00861656,0.06287482,0.026399,0.01434541,-0.01142421,0.0072348,0.03347543,0.0279776,-0.01320871,0.00637929,-0.02226103,0.09806065,0.01618055,0.00761281,0.05046007,0.00502889,-0.11930672,-0.0023379,0.01582633,-0.001356,-0.01166932,-0.04314993,-0.02241815,0.01305482,-0.02294391,-0.00117703,0.04616531,-0.0958949,-0.02829169,0.12090921,0.00475645,-0.00304736,-0.01723761,-0.02525114,0.02718486,-0.02087094,-0.04791572,-0.0467379,0.00155903,0.01934275,0.09656408,0.04998445,-0.00221354,0.03040688,-0.04967752,-0.04609972,-0.02386048,0.18738891,-0.01606897,-0.0787171,0.02264745,0.03707008,-0.02957349,-0.05977025,0.03277325,0.06380406,-0.0730863,0.0589049,0.06820832,-0.05865279,-0.07543374,-0.03298654,0.00277539,0.007507,-0.01009562,-0.0493806,-0.03517217,-0.00801922,-0.03140514,-0.07198437,0.00025392,0.01660903,-0.00207393,0.01181416,0.00617519,-0.010076,-0.03556954,-0.0370872,-0.03688448,-0.05914266,-0.03985601,-0.03702454,0.02821209,-0.07107463,-0.02557581,0.00683018,0.0129791,0.04180146,-0.03873748,0.0053944,-0.04808578,-0.02228945,0.04317305,0.02513488,-0.02549266,0.00527635,0.01603972,0.0575521,-0.0335671,0.02292505,0.0393793,0.02124281,0.02007177,0.06124477,-0.01823789,0.01245486,-0.05259585,-0.18538737,-0.03498375,-0.019461,0.02504661,0.02612626,-0.04463321,-0.00051013,-0.07437911,0.02438742,0.0923112,0.08837342,-0.07205804,-0.02044982,0.01269714,0.00173126,0.01317863,-0.10010794,-0.02984498,-0.02484859,0.05474225,-0.00488016,0.02484111,-0.04803783,-0.09876738,0.01626806,-0.02011985,0.13210055,0.04487132,-0.01710873,0.03630355,0.03878346,-0.01836541,-0.04892206,-0.05360979,-0.02375086,0.03826389,-0.01920052,0.01153018,-0.0026963,-0.02949386,-0.07980052,0.03949783,0.02352459,-0.03615907,-0.00970908,0.00422318,-0.00906343,-0.01079686,0.024108,0.02361646,-0.01821472,0.02653134,0.03154271,0.00375813,0.03347299,-0.049254,-0.0511356,-0.02489874,-0.0234433,0.0499639,-0.01811881,-0.06771946,0.04099765,-0.00841172,0.06493937,-0.01407778,-0.0105837,-0.04500235,0.02016661,-0.00603811,-0.02864635,0.09104518,0.0282045,-0.00132782,0.01531495,0.01845668,0.02225207,-0.05435612,0.00111688,-0.0414616,0.01372197,-0.09996367,0.02665705,0.0639885,0.05103647,0.0123778,0.07409617,0.04645876,0.00753253,0.05047039,0.02916287,0.00737813,-0.0051505,0.05432217,0.0219222,0.03937666,-0.27915284,0.04370872,-0.00387889,0.07782961,0.01299166,-0.03158384,0.05660474,-0.02037217,-0.00864893,-0.10148559,0.04951587,0.03604623,0.06102565,-0.04620561,-0.01075887,-0.00498291,0.05120112,-0.05549159,0.06661967,0.02143457,0.04593159,0.03438484,0.2637139,-0.04872527,0.04189733,0.00779597,-0.00124092,0.01433176,-0.00106614,0.03898512,0.01466499,-0.01183467,0.0683173,-0.0228394,0.01321067,0.04800422,-0.01333961,0.02221583,0.00068532,0.00897329,-0.04319037,-0.02167665,-0.05101685,0.01691096,0.13705841,0.06449859,0.01001534,-0.10616258,0.01920772,0.09167021,-0.06604566,-0.08521454,-0.03389333,-0.01139292,0.00381242,0.00831724,-0.01488449,-0.0176117,-0.02530505,-0.01142549,0.04021112,-0.02548307,0.06254865,0.02505065,-0.00667214],"last_embed":{"hash":"1r0z96g","tokens":109}}},"text":null,"length":0,"last_read":{"hash":"1r0z96g","at":1753423554711},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}","lines":[10,15],"size":300,"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1689197,-0.02594335,-0.04089318,-0.03599086,-0.03138217,0.07087127,-0.02553031,0.03374143,0.06485951,0.00561672,0.02704174,-0.03541335,0.05348644,0.03639376,0.00060837,-0.03790065,-0.02328366,0.03601709,-0.04045941,-0.05237803,0.07282239,-0.06738427,-0.0375727,-0.07386589,0.05732602,0.03999988,-0.00469398,-0.0642219,-0.02087503,-0.22166471,0.03403896,-0.03248497,0.04917537,-0.0267747,-0.0800693,-0.00416636,0.01817925,0.0378033,-0.07717947,0.02799873,0.0137896,0.04100773,-0.01380327,-0.0597687,0.00952387,-0.0672924,-0.00433942,-0.00716877,0.001849,-0.03988337,-0.0899666,-0.00843887,0.0146534,0.00810047,0.06390443,0.00677858,0.04793385,0.07243764,-0.01333921,0.02012324,0.02721228,0.06719341,-0.18245772,0.05964635,-0.02487712,0.03617446,-0.00877305,-0.00749641,0.04050779,0.07249057,0.02156985,0.0305567,-0.02829575,0.04169803,0.05116844,-0.04062753,0.0006072,-0.02010223,-0.02168987,-0.00216116,-0.05118234,-0.04891622,0.00455812,-0.03288651,-0.01756768,0.0658724,0.0560102,-0.01402984,-0.00572721,-0.04031609,0.00974613,0.0332587,0.03609291,0.02876613,0.02051708,0.03820862,0.05370932,-0.00780486,0.03873412,0.13041075,0.02205043,0.03161014,0.00246982,0.01998904,0.04514079,-0.02334022,0.00054336,-0.04831842,-0.01072311,0.0020611,0.03167948,-0.01158919,0.0434074,-0.05079485,-0.03304129,0.00655972,0.01186667,0.06986316,-0.00146457,0.03227626,-0.00914387,-0.03220439,0.05045186,0.01951884,-0.03420582,-0.00243517,-0.00715427,0.07715946,0.05088005,0.02057841,0.06262889,0.0058599,-0.0967829,-0.0134733,0.00933875,-0.00415106,-0.02428091,-0.0640019,-0.01176932,-0.00591897,-0.02326869,-0.02223446,0.01168703,-0.1150129,-0.0325464,0.12562093,0.00796927,-0.00288352,-0.01012353,-0.04435443,0.0218425,-0.02015222,-0.03763939,-0.08698641,-0.01015167,0.01472058,0.08751159,0.0340306,0.00185972,-0.00012986,-0.02372148,-0.04152067,-0.02309419,0.18819556,-0.00957786,-0.0948089,-0.00409383,0.0201733,-0.00523095,-0.07444079,0.04077571,0.06105765,-0.04121996,0.05361544,0.05528252,-0.05785648,-0.08974326,-0.03689728,0.00620075,0.00755684,-0.01913914,-0.04213154,-0.03278142,-0.00168345,-0.00313159,-0.07151128,0.0299765,0.01440895,0.00056394,0.0288754,0.01790684,-0.01735736,0.00721555,-0.02273702,-0.04919676,-0.04723635,-0.03099623,-0.02119008,0.02509475,-0.02178346,0.00642731,0.01925672,0.0204727,0.03616121,-0.06971421,-0.00707559,-0.0280411,-0.01087466,0.04595877,0.03945578,-0.01363721,0.01417165,0.0289905,0.06664646,-0.05886692,0.02441468,0.02574096,0.0295592,0.04743511,0.03006928,-0.00586194,0.01111043,-0.07064307,-0.15937097,-0.03598398,-0.02435844,0.03021532,0.02715801,-0.07582579,0.02034043,-0.05173214,0.03935,0.05376436,0.07115897,-0.08220041,0.00128043,0.02033534,0.00405071,0.04672877,-0.0623042,-0.01150429,0.00077018,0.06089508,-0.01339215,0.05906076,-0.04715582,-0.07354353,0.04645887,-0.03232524,0.14661558,0.01491869,-0.00721067,0.0348182,0.03027742,0.01708389,-0.06702991,-0.07496782,-0.01658605,0.04207397,-0.00673819,0.01439581,-0.0021703,-0.02745192,-0.05306174,0.01021117,0.02311171,-0.09071164,-0.03789498,-0.01079132,-0.01186958,0.01116526,-0.00124253,0.0183035,0.0098818,0.02234943,0.04858207,-0.02801202,0.03012013,-0.04506217,-0.07242443,-0.02227988,-0.00325552,0.04661595,-0.00086882,-0.04180968,0.02736657,-0.00606402,0.05440427,-0.04063689,-0.01554908,-0.04967619,0.00101441,0.00823689,-0.01281673,0.10836521,0.02992292,0.02208943,-0.00518724,0.00869769,-0.00727203,-0.05563813,-0.03095029,-0.03686861,0.02543484,-0.08197831,0.02335507,0.06589222,0.0464607,0.03357973,0.08372001,0.05893591,-0.01548242,0.04608034,0.03272895,-0.01474288,-0.00555235,0.04516908,0.05060418,0.02311662,-0.28640574,0.02056513,-0.01590627,0.05334346,-0.00697352,-0.00353356,0.05377989,-0.03002353,0.04577798,-0.09193277,0.02266972,0.07674883,0.04376347,-0.02982664,-0.03477381,-0.02951815,0.01146751,-0.05326723,0.0484476,-0.00137192,0.05682346,0.00467544,0.26329148,-0.01439563,0.02273696,0.01509363,-0.00164817,-0.00536796,-0.00041562,0.02466667,-0.0013288,0.00567526,0.04112692,-0.01904293,0.02608734,0.04665938,0.00952935,0.00871533,-0.03134335,0.00897603,-0.03951868,-0.00048856,-0.04923408,0.02501329,0.13901335,0.05239498,0.00207677,-0.09822506,0.03506196,0.10657348,-0.06465226,-0.09427995,-0.02455467,0.0075256,-0.00285846,0.01033428,-0.020755,-0.02274585,-0.00944701,-0.00260262,0.06063663,-0.03164001,0.04638902,0.03736998,0.00962137],"last_embed":{"hash":"1197g0f","tokens":199}}},"text":null,"length":0,"last_read":{"hash":"1197g0f","at":1753423554760},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)","lines":[16,20],"size":374,"outlinks":[{"title":"First Lotto Program Based on _Markov Chains_: _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":1},{"title":"Ion Saliu's Algorithm for Enhanced _Markov Chains_ Lottery Software","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":2},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.17134362,-0.02757826,-0.04949764,-0.03152236,-0.03033248,0.067539,-0.02081284,0.03380471,0.06638497,0.00917609,0.02964415,-0.03770592,0.05397607,0.03594252,0.00128979,-0.03615327,-0.0222033,0.03758126,-0.04524579,-0.05324658,0.06832362,-0.06150071,-0.03517824,-0.07557824,0.05248463,0.03963231,-0.0096182,-0.06654751,-0.02037757,-0.21839949,0.0311648,-0.03248169,0.0416807,-0.02951954,-0.08543889,-0.00776189,0.01262968,0.03972724,-0.0727826,0.02678756,0.01033986,0.03795407,-0.01252097,-0.05892177,0.01009896,-0.06980839,-0.00535644,-0.00664919,0.00497064,-0.04068184,-0.08727898,-0.00781538,0.0131443,0.01175647,0.064895,0.00994108,0.04679719,0.07231914,-0.01433216,0.0195525,0.02883325,0.06719878,-0.18106601,0.05740169,-0.02961017,0.03507467,-0.00862888,-0.01097056,0.03294969,0.07516135,0.02832648,0.03195349,-0.02741794,0.04692505,0.05907476,-0.03566308,-0.00345812,-0.02710491,-0.01770732,-0.00121052,-0.05632658,-0.05280257,0.00631317,-0.0354492,-0.01162914,0.05947725,0.05788256,-0.01337877,-0.00433895,-0.04108873,0.01024184,0.03733235,0.03605103,0.02856534,0.01004782,0.03216076,0.04929421,-0.01447629,0.04336087,0.13065498,0.01739323,0.02898707,0.00599167,0.01690338,0.04659952,-0.02645448,-0.00167635,-0.04398273,-0.01492489,-0.00109717,0.02971884,-0.00882571,0.04995973,-0.05360364,-0.03258688,0.00849389,0.01003474,0.06994647,-0.00482296,0.03417874,-0.01284966,-0.02930379,0.05147671,0.02124664,-0.02494214,-0.00000877,-0.00589583,0.07188464,0.04600612,0.027197,0.06470724,0.00702819,-0.09697969,-0.01365727,-0.0023878,-0.00456806,-0.02378907,-0.06762017,-0.01404224,-0.00694179,-0.02477412,-0.01739208,0.01179533,-0.11798357,-0.02479493,0.12647261,0.01300118,-0.00128702,-0.0087463,-0.04589295,0.01868347,-0.02276865,-0.03652466,-0.08107183,-0.00785161,0.0211668,0.09302296,0.03878636,0.00465333,0.00668551,-0.01992278,-0.0425136,-0.02537608,0.18567267,-0.0098747,-0.09843691,-0.00185248,0.01861587,-0.01062916,-0.07192577,0.04101935,0.0670628,-0.04409262,0.06419168,0.05843997,-0.05720192,-0.09087056,-0.03596871,0.00674643,0.00306633,-0.01987526,-0.04604084,-0.03354177,-0.0012726,-0.00660135,-0.0673737,0.02672182,0.01559712,0.00222463,0.0332321,0.01317341,-0.0137343,0.00099813,-0.02071719,-0.04915121,-0.038964,-0.02841456,-0.02300005,0.02534891,-0.02718777,0.00296126,0.01426159,0.01578473,0.02854094,-0.06091233,-0.00562211,-0.02279038,-0.00434309,0.04502846,0.03937704,-0.01632888,0.01137923,0.02867465,0.06539155,-0.05763295,0.02300066,0.02470508,0.02591812,0.04232391,0.02835804,-0.00594498,0.01227096,-0.07303671,-0.16275561,-0.03718977,-0.02311749,0.03053891,0.03088135,-0.07617425,0.01641819,-0.05593819,0.04413272,0.05515584,0.06862008,-0.08170094,-0.00523694,0.01503394,0.00720984,0.05013633,-0.06272831,-0.01209844,-0.00024883,0.06297617,-0.01257701,0.06181612,-0.04413427,-0.06826631,0.04231869,-0.03124546,0.14438504,0.02212176,-0.01235859,0.03455732,0.03035433,0.01957398,-0.07002413,-0.0753341,-0.01716752,0.03662104,-0.00664718,0.01393798,0.00266277,-0.03556522,-0.05730239,0.01247972,0.02434637,-0.08718041,-0.02675883,-0.00587712,-0.00777637,0.00960784,0.00206622,0.01554918,0.01037431,0.0318289,0.04700926,-0.03148724,0.02902841,-0.04719182,-0.06797271,-0.01946201,-0.00581663,0.04410317,-0.00381902,-0.04438465,0.02313859,-0.00180962,0.05335751,-0.03826598,-0.01757015,-0.04810829,-0.00043572,0.0056488,-0.0127315,0.1073124,0.03021154,0.02618545,-0.0079698,0.00553336,-0.01171045,-0.05978208,-0.03266982,-0.03723703,0.03023187,-0.07902043,0.0263008,0.06894711,0.04772666,0.03284864,0.08217837,0.05954198,-0.01518155,0.04599447,0.02869476,-0.01240645,-0.00579802,0.04102246,0.04792485,0.02057178,-0.2880142,0.02171272,-0.01705582,0.05460218,-0.00821892,-0.00527409,0.05372567,-0.03320684,0.04341364,-0.08975441,0.02649898,0.08191485,0.0500955,-0.02878644,-0.03185579,-0.01904579,0.01199865,-0.04909478,0.04572036,-0.002543,0.05555495,0.00484482,0.27208084,-0.01797506,0.02445221,0.01375809,0.00059794,-0.00478495,-0.00046529,0.02139655,-0.0031736,0.00339235,0.03843075,-0.02225026,0.02837655,0.05221568,0.01095288,0.01477682,-0.0325905,0.01051028,-0.04508619,-0.00601945,-0.05138216,0.03017361,0.14038152,0.05148531,-0.00495216,-0.08514515,0.03484062,0.10508235,-0.06081763,-0.09092911,-0.02678062,0.00626268,-0.00132549,0.00733638,-0.01631359,-0.02392519,-0.01250515,-0.00178424,0.05588987,-0.02678259,0.04785274,0.03327889,0.0087367],"last_embed":{"hash":"cd7q62","tokens":157}}},"text":null,"length":0,"last_read":{"hash":"cd7q62","at":1753423554845},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program##I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)#{1}","lines":[17,20],"size":252,"outlinks":[{"title":"Ion Saliu's Algorithm for Enhanced _Markov Chains_ Lottery Software","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":1},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14895071,-0.05680402,-0.0440219,-0.03017344,-0.02721692,0.05750524,0.02202842,0.00850684,0.05708716,-0.01496679,0.01048377,0.02454773,0.01407683,0.0545605,-0.00443341,-0.02034782,0.00346758,-0.00398196,-0.00413919,-0.01738217,0.04228056,-0.0899705,-0.06467713,-0.09672848,0.06059799,0.01948115,-0.00642895,-0.05073965,-0.04307553,-0.23611116,0.04510037,0.00683086,0.01921537,-0.04796018,-0.04112542,-0.01479107,-0.00348642,0.02541109,-0.07279237,0.01923734,0.05077168,-0.01137468,0.00479983,-0.03348201,0.0256424,-0.08803914,-0.01132834,-0.00076398,0.01199538,-0.01944397,-0.08648089,0.00612094,0.03382915,0.02071682,0.05830137,0.00668882,0.08643214,0.05591115,0.01640119,0.03087653,-0.01160028,0.04636911,-0.1664827,0.0581511,-0.00048685,0.02498398,-0.03882201,0.00567109,0.01267077,0.05881236,-0.00172393,0.01295407,-0.01219723,0.06964438,0.04643575,-0.10154886,0.0134119,-0.02349997,-0.05414571,0.01524017,-0.00693733,-0.04228831,-0.04655746,-0.03838453,-0.02093806,0.07551072,0.0213365,-0.00812459,0.01789518,-0.04604251,0.00725604,0.01433992,0.08209,0.04326412,0.02687786,0.00967006,0.06615704,0.05904671,0.03036571,0.11174007,0.04991112,-0.02977452,0.02986384,-0.00775013,0.07013676,0.01062127,0.01089357,-0.04877372,-0.00543072,-0.00088394,0.06265522,0.00054334,0.06494704,-0.02992667,-0.02988488,-0.00742578,-0.0345838,0.05891343,0.00005121,-0.00718004,-0.02956609,0.0054943,0.02681154,0.00247206,-0.05587212,-0.01872586,0.00512128,0.10376588,0.04973838,-0.00413116,0.02026177,-0.02109799,-0.08072624,-0.01028364,0.02511237,0.00650053,-0.02231362,-0.03385208,0.00714743,-0.0176022,-0.04868654,0.00132929,0.02744763,-0.08590517,-0.04031625,0.05523347,0.01141803,0.00103514,0.0120473,-0.00028549,0.03492518,0.0018385,-0.03826555,-0.06762014,-0.01894675,0.01277042,0.07707295,0.07016127,-0.01211658,0.00996215,-0.05408931,-0.04795471,-0.011269,0.11411615,0.00215241,-0.11242323,-0.00948603,0.07130056,-0.01170796,-0.05782994,0.03079903,0.01629135,-0.02382324,0.02926604,0.028497,-0.02676853,-0.12822208,-0.03608242,-0.01002239,-0.0021166,0.00508443,-0.04799027,-0.04193325,-0.02113501,-0.01493916,-0.08967599,0.01574623,-0.00946336,0.00006385,0.03142831,0.03310623,-0.0109237,-0.01296083,-0.03313329,-0.03456482,-0.04930139,-0.01376813,-0.0035544,0.0277982,-0.03873097,-0.03376106,-0.02587806,0.0142754,0.04233946,-0.04753429,-0.00337049,-0.0264231,-0.03966059,0.07097127,0.04577073,0.00577036,0.01046711,0.02615695,0.08180229,-0.07022641,0.01997826,0.04455739,0.02520518,0.03793894,0.02977146,-0.03058542,0.0213443,-0.06893844,-0.19027683,0.01771529,-0.04615894,0.03688244,0.0611229,-0.04361126,0.01445864,-0.04547779,0.04806753,0.0533186,0.06567632,-0.06708375,-0.00062044,0.01664516,-0.0123592,0.02056375,-0.03476319,-0.02046723,-0.02662504,0.03274113,-0.00255715,0.03617377,-0.0819453,-0.0911961,0.04206479,-0.02060709,0.15718111,0.04688074,-0.0155544,0.01454589,0.03028526,0.00132526,-0.03274658,-0.07917606,-0.03484908,0.07284032,0.031016,-0.02923241,-0.02533718,-0.03340501,-0.07168277,0.0337349,0.0057299,-0.07912984,-0.06152179,-0.02760831,-0.02113128,0.00333434,-0.00366646,0.04719523,0.00225249,-0.01249522,0.03875981,0.01386698,0.03553838,-0.03712742,-0.06831778,-0.01799241,0.05492854,0.05154136,0.04325437,-0.04949777,0.03632734,-0.02125658,0.06228998,-0.02899767,0.00866978,-0.03122149,0.04755644,-0.03959928,-0.01744322,0.10539519,0.02288627,-0.00281999,0.02859813,0.01335302,0.00175141,-0.06015203,0.00208533,-0.01824636,0.01032405,-0.02577073,0.05687442,0.08549753,0.02895503,0.0135605,0.08351951,0.04663761,-0.01867608,0.01918371,0.03611781,0.00687941,-0.0341188,0.04525789,0.0719878,0.02768283,-0.27811709,0.04565242,-0.02099705,0.06664968,0.00309941,-0.01000072,0.05558398,0.00773282,0.04217025,-0.05256753,0.09078892,0.04055483,0.04727661,-0.05673565,-0.02589169,-0.05463492,0.00877955,-0.0344286,0.05141035,0.02191019,0.05740796,0.04241486,0.2382217,-0.0153629,0.00138138,0.00351988,-0.00063183,0.04242357,0.02056339,0.0242854,0.00648929,-0.01497464,0.03749909,-0.05166084,0.00183011,0.03495465,-0.01679893,-0.00845439,-0.02899062,-0.00289568,-0.07013832,0.03524019,-0.0274422,-0.02209584,0.1418568,0.0463214,-0.00429494,-0.09709441,0.03419755,0.09378614,-0.05770077,-0.12082684,-0.05040281,-0.01678335,-0.00712366,0.01428753,-0.00740606,-0.00436737,-0.01833719,-0.0049967,0.02728776,-0.00166712,0.08343449,0.07277952,-0.01023512],"last_embed":{"hash":"373d1w","tokens":491}}},"text":null,"length":0,"last_read":{"hash":"373d1w","at":1753423554909},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>","lines":[21,70],"size":7094,"outlinks":[{"title":"_**thornc Cristiano Lopes programming**_","target":"https://saliu.com/programming.html","line":5},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":49}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13174032,-0.04182026,-0.0514953,-0.03884917,-0.0355771,0.05098728,0.00576694,0.01838323,0.07815167,0.00115662,0.01704508,-0.00578855,0.01511184,0.06948192,-0.02011989,-0.04155524,0.00719569,0.01760533,-0.00594079,-0.03241263,0.07276256,-0.0821854,-0.0773083,-0.09133581,0.06598997,0.04243965,0.0050837,-0.06448214,-0.02587859,-0.21931848,0.0342666,-0.01138869,0.03701954,-0.02782887,-0.04822578,-0.00884129,0.01053576,0.02521928,-0.08717964,0.04446551,0.04342115,0.0035673,-0.01159142,-0.05572034,0.01940819,-0.07480194,-0.00963386,0.01094752,-0.00618852,-0.03480778,-0.08486323,0.02708513,0.04810065,0.01969103,0.05629061,0.0026296,0.07728226,0.06457526,-0.01549185,0.02945428,0.00372144,0.04148778,-0.19169793,0.07245457,-0.00222042,0.04369039,-0.01347011,0.00596788,0.03644576,0.07120994,0.01977185,0.02680305,-0.00147256,0.05151031,0.063046,-0.09424103,-0.00245901,-0.03012419,-0.02098161,0.00971562,-0.03571642,-0.04966191,-0.03594318,-0.05414074,-0.01266835,0.07156344,0.03979698,0.00978949,0.00692305,-0.03778732,0.02894217,0.01830167,0.06055994,0.03173735,0.01206891,0.02995077,0.05251472,0.03469115,0.02880959,0.13452892,0.0454389,-0.01492267,0.03406295,0.01108626,0.05722602,0.0024198,0.00575489,-0.06194743,-0.02337406,0.00535634,0.02697024,-0.01351845,0.05152257,-0.04345347,-0.0305829,-0.01323372,-0.01152075,0.0504929,0.01582887,0.00898843,-0.01691512,-0.01498388,0.01174661,-0.01341871,-0.03652702,-0.01014487,-0.00223787,0.1060942,0.05885021,0.0052776,0.03934358,-0.00245678,-0.10399377,-0.01712011,0.02339168,-0.00148543,-0.01425276,-0.02818634,0.00300487,-0.01056891,-0.05112719,-0.02704966,0.01473202,-0.09294355,-0.04686252,0.05740648,0.01478525,-0.02111267,0.01858194,0.00233912,0.01234527,-0.01568397,-0.04572861,-0.08853745,-0.01454044,0.00351192,0.06368098,0.04732051,0.00145194,-0.00169236,-0.04984017,-0.05994605,-0.02253144,0.18440863,-0.02479213,-0.10989829,-0.00710539,0.04690539,0.00943577,-0.06368235,0.03670883,0.02189983,-0.03715514,0.02680624,0.04022733,-0.02112853,-0.06180166,-0.01520228,-0.00831036,0.01864937,0.00212685,-0.03435779,-0.03039919,-0.01201148,-0.01822533,-0.09259617,0.00887632,-0.01325025,-0.00335431,0.01781098,0.0186878,-0.02664033,-0.00381345,-0.03600572,-0.05520281,-0.05418647,-0.02387979,-0.01117355,0.02091128,-0.04789235,-0.00070006,0.00720562,0.01790468,0.06424133,-0.06582134,0.0125455,-0.03403528,-0.02881177,0.07368173,0.03502724,-0.00803117,0.00509314,0.01799313,0.08193274,-0.05013169,0.0277036,0.03059111,0.01300695,0.02983703,0.03049215,-0.01249177,0.0086587,-0.06440211,-0.19829409,0.01034494,-0.0191572,-0.00588536,0.01916245,-0.04273227,0.01257915,-0.03416119,0.04819845,0.03996689,0.0771741,-0.06020559,-0.01108985,-0.01133794,-0.00952866,0.01979076,-0.0511749,-0.03033905,0.00131956,0.04331968,-0.01964763,0.01601129,-0.0657647,-0.11459368,0.05009449,-0.02216914,0.15826578,0.04454274,-0.0076983,-0.00043196,0.03623457,-0.02164397,-0.0516858,-0.07002483,-0.02049563,0.05954145,-0.00361006,-0.003856,-0.01115238,-0.03108178,-0.06818744,0.02026193,-0.00306484,-0.07092407,-0.06013307,-0.00446327,-0.01533286,0.00062394,0.00845575,0.06068198,0.01366263,-0.03410254,0.06914569,0.01256567,0.04174607,-0.04114123,-0.06999227,-0.04059388,0.03464516,0.05703216,0.01531843,-0.0460448,0.06081847,-0.03225069,0.06548783,-0.02282495,-0.01398603,-0.04794375,0.04740394,-0.02151969,-0.02937551,0.10664528,0.02765483,-0.0273266,0.00157327,0.0265444,0.00544056,-0.04479708,0.0040023,-0.00960427,0.01042447,-0.04257595,0.04002781,0.05686202,0.04339693,0.02645916,0.08268402,0.05157949,-0.01643834,0.03433578,0.03311644,0.00941599,-0.01165625,0.0605822,0.05982135,0.01814362,-0.28628743,0.03720183,-0.0001217,0.0567591,0.01134601,0.01344122,0.04948006,-0.0019881,0.01919286,-0.07403213,0.08777883,0.03598195,0.04862441,-0.02938317,-0.04615021,-0.03856134,0.02149187,-0.03810971,0.04615651,0.01401489,0.04385028,0.04542448,0.24622278,-0.01486253,0.00403678,0.0162391,0.01096056,0.01183283,0.01960734,0.04751628,0.00212481,0.00662393,0.03816016,-0.02271738,0.0138899,0.03413291,-0.00368149,0.00978805,-0.02342927,0.01008747,-0.05567751,0.03484526,-0.0153012,-0.00460569,0.12553114,0.05877906,-0.01129279,-0.10034957,0.02561604,0.07874589,-0.06780326,-0.11003224,-0.03830994,-0.01706291,-0.01805006,0.01586981,-0.01367952,-0.02332369,-0.00299687,0.00641755,0.0427232,-0.00476886,0.07441788,0.04869707,0.00589198],"last_embed":{"hash":"1fcnzo0","tokens":170}}},"text":null,"length":0,"last_read":{"hash":"1fcnzo0","at":1753423555109},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{2}","lines":[25,26],"size":257,"outlinks":[{"title":"_**thornc Cristiano Lopes programming**_","target":"https://saliu.com/programming.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14248303,-0.04695303,-0.03340713,-0.00974523,-0.02177696,0.04871026,0.05083983,0.03148521,0.06665467,-0.0136252,-0.00145165,0.02153377,0.01955537,0.05606441,0.01453779,-0.01775692,0.0021632,-0.00386528,0.00261958,-0.0031468,0.03257702,-0.08925182,-0.0646838,-0.09575129,0.05875225,-0.00052509,-0.00921849,-0.04356581,-0.04911915,-0.22777301,0.04163783,0.0158196,0.02346439,-0.04281946,-0.03839323,-0.02630801,-0.01733457,0.02375672,-0.06832711,-0.00182567,0.04567572,-0.01158024,0.00378582,-0.01970119,0.02916954,-0.09704987,-0.03197166,0.00342412,0.0040638,-0.00536747,-0.08901226,0.01581321,0.01133611,0.01858808,0.04745584,0.01398699,0.08010598,0.04840362,0.02052061,0.02007808,-0.005149,0.05719303,-0.15333089,0.04682526,0.01297879,0.02087954,-0.05125281,0.0257047,-0.01515504,0.04980391,0.00424292,0.01223362,0.01520489,0.07118126,0.04792548,-0.08953738,-0.00242513,-0.02420557,-0.05157171,0.02888034,0.01648092,-0.05229614,-0.05465282,-0.04819905,-0.02628721,0.07075809,0.03830662,-0.01873823,0.02490959,-0.04648924,-0.00007259,0.03382945,0.08400251,0.0429227,0.02616955,0.0001694,0.06213542,0.06225004,0.00475339,0.08992524,0.02990882,-0.04263419,0.02263065,-0.01515299,0.08314208,0.01117798,0.03061939,-0.01867555,0.00837699,-0.00043836,0.06363191,-0.00195798,0.06635388,-0.01881542,-0.01410433,0.00938282,-0.03109969,0.04536579,-0.0088555,-0.0098052,-0.04063921,0.02156251,0.02298904,0.0045396,-0.04709554,-0.04692428,0.00549922,0.09948246,0.03545209,-0.02353692,0.01772162,-0.02300465,-0.06378724,0.0164244,0.01533493,0.00165441,-0.03241301,-0.02443368,0.01229305,0.00742756,-0.03268325,0.01012014,0.03743756,-0.10151108,-0.03576174,0.04763534,0.03074774,0.00786368,0.02185329,-0.0034997,0.02760943,-0.00953846,-0.02008708,-0.06020464,-0.01882395,-0.00040905,0.07454071,0.06249561,-0.02728197,-0.00913789,-0.04804372,-0.03201338,-0.00841843,0.12045543,-0.00649334,-0.10037865,0.01267661,0.07690821,-0.0256803,-0.06294631,0.02033098,0.00513113,-0.03811646,0.03735309,0.02804729,-0.04241407,-0.14965609,-0.03367053,-0.00264487,0.01444997,0.03431271,-0.05401012,-0.05341839,-0.02203043,-0.00885458,-0.07891221,0.01019862,-0.01600866,0.00033524,0.03030563,-0.00981132,0.0071921,-0.03193438,-0.02672279,-0.02865004,-0.04462272,0.01765659,-0.01572461,0.02987542,-0.0307894,-0.07754821,-0.03441618,0.00333492,0.03087526,-0.04830734,0.0156088,-0.01343589,-0.06003923,0.07465456,0.05312014,0.00348839,-0.0059783,0.0337058,0.07677519,-0.05840982,0.00218485,0.06153813,0.0366962,0.02843218,0.02657258,-0.03296226,0.03449706,-0.05590552,-0.18754874,0.01584421,-0.06962732,0.02961563,0.09341341,-0.04348519,0.01004045,-0.04881256,0.0369921,0.05585685,0.06310678,-0.07906445,-0.01406375,0.0189096,-0.01534079,-0.00372959,-0.04407177,-0.01528766,-0.02639049,0.02898731,0.00205623,0.04942103,-0.06525671,-0.09775672,0.02506867,-0.01249852,0.16608734,0.0696201,-0.0081987,0.0256159,0.02989557,0.01518768,-0.03555399,-0.09211065,-0.02654892,0.07496017,0.05423282,-0.04761876,-0.0291625,-0.0446475,-0.08028723,0.04702652,0.00041312,-0.07630932,-0.07596532,-0.02649934,-0.02424303,0.00828057,-0.02334282,0.06968816,0.01115155,-0.00425957,0.06161965,-0.00621793,0.04463623,-0.03879793,-0.0886541,-0.01152479,0.04873357,0.04579929,0.03600604,-0.05191751,0.02719651,-0.01650179,0.07045384,-0.01983736,0.02140183,-0.02447308,0.04387516,-0.02245148,-0.01246348,0.08691771,0.01933754,-0.02367035,0.03329121,0.00914236,0.00481834,-0.07011482,-0.00243059,0.00485026,0.01953452,-0.00624826,0.06099821,0.08649422,0.01993685,-0.01050907,0.09008993,0.02753494,-0.0108778,-0.01172864,0.01343787,0.01647332,-0.0574021,0.03263967,0.05835601,0.02908232,-0.28092313,0.03499838,-0.0000323,0.04601927,-0.00452724,-0.01841399,0.05804151,0.00330105,0.01885353,-0.01627019,0.08640786,0.03347825,0.02950606,-0.0454475,-0.00713391,-0.0390641,0.030346,-0.03513367,0.06368995,0.0486658,0.0603653,0.06911708,0.24370022,-0.03720563,0.00862669,0.00484302,-0.00786873,0.04956578,0.01408964,0.0042467,0.01347131,-0.0317006,0.01226597,-0.05061674,0.00537154,0.04000105,-0.02403254,-0.00765995,-0.01823099,-0.01234283,-0.07172442,0.02538585,-0.01797901,0.00422097,0.14588146,0.02839622,0.00091657,-0.08136309,0.01333698,0.06743974,-0.05536156,-0.10469653,-0.04687195,-0.02210246,-0.00708624,-0.00283782,-0.0049282,-0.00766862,-0.00846081,-0.01519158,0.02808312,0.01665005,0.05309897,0.09384621,-0.01786636],"last_embed":{"hash":"4xpqut","tokens":454}}},"text":null,"length":0,"last_read":{"hash":"4xpqut","at":1753423555170},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{3}","lines":[27,53],"size":5035,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11456311,-0.03761933,-0.03719806,-0.0578498,-0.02566177,0.04047403,-0.04357262,-0.02419074,0.07938688,0.01378072,0.05915954,-0.00466528,0.06185658,0.05862924,-0.04550203,-0.02888463,0.01071308,0.00635996,-0.03582126,-0.04015819,0.07313731,-0.05546091,-0.05812486,-0.07463013,0.05606941,0.05529674,0.00755478,-0.09035465,-0.02435024,-0.26059422,0.02614375,-0.00654847,0.0633575,-0.04322055,-0.01282134,0.001997,-0.02775639,0.03278834,-0.07846533,0.04070626,0.02919233,0.0499736,0.00709781,-0.06119005,0.00392848,-0.0536333,-0.0314999,-0.00406503,0.02051057,-0.02577197,-0.07445707,-0.00099491,0.03814407,0.04408109,0.04576484,-0.00419279,0.06643037,0.08884437,-0.03983757,0.02833618,0.03270394,0.07003326,-0.18269573,0.0668526,0.01579084,0.0112331,-0.01320077,-0.00281676,0.01546876,0.08173355,-0.03229705,0.01494732,-0.00668606,0.06262399,0.05617012,-0.07435215,0.00525935,-0.05324106,-0.02977096,0.02585203,-0.06224646,-0.08629972,-0.02356736,-0.03019748,-0.02250578,0.0463448,0.04608471,0.0321339,0.01267969,-0.01858825,0.01946331,0.03529947,0.07688674,0.0263906,-0.00294816,0.01935571,0.06900149,0.04821727,0.04068807,0.11369278,0.01533858,-0.02538004,0.0282188,-0.02125927,0.03923869,0.01536258,-0.01145336,-0.03753956,-0.03170132,-0.00468265,0.03676322,-0.01253797,0.05772574,-0.05984023,-0.02861136,-0.01394795,0.00144082,0.05105997,-0.02037823,0.01139527,-0.02424823,-0.00681579,0.03423151,0.00518547,0.00135958,0.0012614,0.00426948,0.09771982,0.0666623,-0.00688559,0.02782767,0.00434908,-0.08387697,-0.00852954,0.01518184,-0.00712138,0.00601913,-0.03368733,-0.02002006,-0.0400493,-0.04396594,-0.02527264,0.02610756,-0.08021783,-0.00676859,0.11213493,-0.00894604,0.00895813,0.00805649,-0.04590681,0.00551892,0.0160797,-0.03636001,-0.08699992,0.02615977,0.03777035,0.07982522,0.03216235,-0.03066859,0.02689148,-0.0591494,-0.04251504,-0.02509156,0.16924489,-0.03354928,-0.05705639,-0.02132862,0.05378856,0.0058816,-0.0681015,0.02750834,0.0057701,0.00118198,0.02432795,0.05542022,-0.04950571,-0.07468817,0.00477818,0.01993198,0.01494297,-0.02837848,-0.01629129,-0.02462439,-0.00501911,0.00341419,-0.08642276,0.01511466,0.01525389,0.03576695,0.01074032,-0.01113796,-0.03079424,0.02856175,-0.04184474,-0.06143228,-0.04612396,-0.03800672,-0.01537317,0.02758764,-0.08639488,0.06063646,-0.01300867,-0.00344148,0.0548907,-0.03257992,0.01407699,-0.04527484,-0.03167103,0.07696414,0.0414871,-0.02101718,0.01698207,0.02991105,0.06133379,-0.06329528,0.0209756,0.02343748,0.02572437,0.04852245,0.02319482,0.00077887,-0.00018357,-0.06453501,-0.20365305,0.00287058,-0.00455294,0.00535066,0.02214016,-0.06009848,-0.01535333,-0.0528701,0.06141915,0.04404086,0.04696167,-0.04103032,-0.03340435,-0.01432342,-0.01772382,-0.00448664,-0.06421538,0.00132983,-0.02143804,0.05422086,-0.02689829,0.01464563,-0.06345668,-0.08910001,0.06488745,-0.028076,0.1417077,0.00726944,0.00918689,-0.00517269,0.04359136,0.02874956,-0.05432309,-0.02330888,0.00605508,0.05305357,-0.0221133,-0.00945225,0.02805633,-0.03570303,-0.03377975,-0.01441582,0.00253221,-0.09460635,-0.02171701,-0.01475315,-0.02578191,0.00435315,0.00516889,0.07052135,-0.00004789,0.01764229,0.05760886,0.00275298,0.05897526,-0.03405556,-0.07401443,-0.004704,0.03176042,0.02713111,0.00487385,-0.07783302,0.05943667,-0.00376976,0.06560879,0.00443855,-0.01470764,-0.04931263,0.03618409,-0.02998838,-0.01018881,0.10959256,0.02196872,-0.00705242,-0.02675738,0.01674252,-0.00668806,-0.05363551,-0.01238723,-0.05025483,0.02149196,-0.02261963,0.03517826,0.07704289,0.00975459,0.00054316,0.07739874,0.04045909,0.00258207,0.02089197,0.05312817,-0.00995687,0.01535488,0.07559739,0.07199042,0.01511873,-0.27388954,0.01390437,-0.00351622,0.06784143,-0.00546828,0.02390249,0.0267232,0.00710628,0.0171912,-0.04812087,0.01867756,0.02978051,0.05488318,-0.0833656,-0.01776038,-0.04392906,0.02101223,-0.0408634,0.07104439,0.01279269,0.05641124,0.00717529,0.23789868,-0.01762279,0.00427602,0.02951872,0.00208399,0.0260351,0.05495721,0.02594583,-0.01970492,0.00713879,0.07528136,-0.0424392,0.00103984,0.0212816,-0.00498788,-0.00214358,-0.03476505,-0.01963102,-0.02922042,0.01918135,-0.0200433,-0.01761847,0.12812831,0.03756094,-0.00607668,-0.09642842,0.03414043,0.08179231,-0.05377371,-0.09236708,-0.04051675,-0.01704873,-0.00108108,0.02079783,-0.00662309,-0.03109481,-0.00716692,-0.02500877,0.06158089,-0.01499125,0.03547388,0.05009575,-0.04375318],"last_embed":{"hash":"1kanphf","tokens":165}}},"text":null,"length":0,"last_read":{"hash":"1kanphf","at":1753423555343},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{4}","lines":[54,55],"size":300,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14484423,-0.04181786,-0.05592022,-0.03092679,-0.04663731,0.09328432,-0.01460729,0.01970574,0.05574,0.01777098,0.03600964,0.00141787,0.00897119,0.07589456,-0.02199464,-0.0282603,0.00174822,0.01295303,0.01052834,-0.02866934,0.05466836,-0.05993721,-0.06465352,-0.08127444,0.05538191,0.04534361,0.02820219,-0.07086742,-0.03224634,-0.24334651,0.04117472,0.00045071,0.02503367,-0.03792964,-0.04017295,-0.01398201,-0.02629942,0.0349876,-0.07694641,0.05364574,0.0387555,0.02898925,-0.01171466,-0.05829174,0.02372468,-0.0571379,-0.02063397,0.01192895,0.01907217,-0.00379804,-0.07694975,-0.01592428,0.0546358,0.01416306,0.04352846,-0.01958942,0.06137472,0.06012294,0.00595325,0.01547393,0.01654391,0.06078405,-0.17448039,0.06144703,-0.0144217,0.03358259,-0.03789938,0.01016981,0.04118048,0.08636942,-0.01195111,-0.00390986,-0.00130694,0.06029348,0.04795252,-0.09071571,-0.01678296,-0.04884472,-0.03568306,-0.00231022,-0.05132199,-0.0594127,-0.03382413,-0.04342678,-0.01877698,0.08290969,0.03833012,0.02007787,0.03133927,-0.01097485,0.01049691,0.00715188,0.06523722,0.0495386,0.01027399,0.04606789,0.06677285,0.0165908,0.00153703,0.12856573,0.0398417,-0.01642264,0.02935534,0.00367474,0.0563146,-0.00213838,0.0069089,-0.05195884,-0.02387963,-0.00424062,0.04741071,-0.01926636,0.06667837,-0.07542787,-0.03009294,0.01114915,-0.00719651,0.04440574,0.00031494,0.01942931,-0.0130108,-0.00753644,0.02862661,-0.01385732,-0.02224514,-0.02110944,0.01124314,0.09773711,0.04427398,-0.00132713,0.04251855,0.00533335,-0.09455907,-0.00289826,0.01499833,0.01202834,-0.00091841,-0.02261228,-0.00238158,-0.01882702,-0.03404963,0.01633657,0.02431235,-0.09700722,-0.03981733,0.05227071,0.00880038,0.02655175,0.01647495,-0.02574902,0.0202107,0.00145254,-0.03092923,-0.07242663,0.00063818,0.0137408,0.04014784,0.04104728,-0.02280859,-0.00545215,-0.0605303,-0.06437112,-0.0362941,0.17037907,-0.02695292,-0.08015648,-0.01472121,0.03971292,0.0176302,-0.08624989,0.0532128,0.01938686,-0.03102623,0.01362486,0.03917217,-0.04431528,-0.08562039,0.00487616,0.01293495,0.01646828,0.02240053,-0.01492431,-0.04866677,-0.00238197,-0.02139907,-0.07259563,0.04189363,-0.00989823,0.01162486,0.00820264,0.016977,-0.00850295,-0.01515611,0.0008878,-0.04760853,-0.05567374,-0.02266332,-0.00916639,0.03321623,-0.05926571,0.01307725,-0.01946933,0.02060667,0.01271605,-0.02965993,0.01866853,-0.02767607,-0.04311338,0.08951537,0.03575928,-0.03385974,-0.0197088,0.048176,0.08624069,-0.0704919,0.00460963,0.03013961,0.02203046,0.03835181,0.02532266,-0.03707797,0.02817576,-0.04283508,-0.20413037,0.02299575,-0.01634796,0.01197871,0.05213777,-0.04258365,-0.00168372,-0.03496614,0.07135453,0.04141055,0.0728805,-0.0602755,-0.00249466,0.00059381,-0.00665769,0.01058643,-0.06011561,-0.01570372,-0.00406041,0.05053119,-0.01500619,0.01379656,-0.06400231,-0.1062728,0.06433216,-0.03618516,0.14933746,0.02553148,-0.00313204,0.0026228,0.04636073,-0.01742267,-0.05704611,-0.07910683,-0.00728617,0.05685122,-0.00123485,0.01215031,-0.01166726,-0.05807273,-0.05916845,0.00814109,0.00542755,-0.08609755,-0.06852917,-0.03618033,-0.02391609,0.01265337,-0.01248367,0.05446493,-0.00654978,-0.02320046,0.04755127,-0.01114782,0.0387671,-0.03531487,-0.07056436,-0.03026008,0.03462734,0.04268727,0.03665938,-0.04496867,0.04735645,-0.01537647,0.06072139,-0.0411902,0.0017484,-0.05890258,0.05179949,0.00165669,-0.00448204,0.11212447,0.03468681,0.01705994,0.01879172,0.00647458,0.00667623,-0.05340227,0.01015933,-0.00925477,0.01889776,-0.03447771,0.02860743,0.03561452,0.02882087,0.03801855,0.06776886,0.03488974,-0.02177577,0.02199209,0.02312654,0.01828994,-0.00806191,0.04622552,0.06670307,0.0155558,-0.28180668,0.05018571,-0.0076594,0.05188228,-0.02250434,-0.02541427,0.05594025,-0.02884635,0.00017232,-0.07106417,0.04535081,0.05534542,0.05902346,-0.05292958,-0.02363579,-0.03294845,0.04403745,-0.01751325,0.04723556,-0.00066034,0.03313607,0.04360161,0.24997656,-0.0288101,0.0036637,0.01620972,0.03351157,0.02212822,0.05290392,0.0180158,0.01434205,-0.00647525,0.0305833,-0.03691265,0.00449858,0.03563078,-0.01023062,-0.00271651,-0.01446618,0.00076167,-0.0392401,0.00931908,-0.01399103,-0.00609061,0.12798314,0.05471624,-0.00902264,-0.09444813,-0.00566421,0.09726294,-0.04628488,-0.12058587,-0.01863067,-0.04149672,-0.0161809,0.04892334,-0.00819479,-0.0188319,-0.01034427,-0.010303,0.05281342,-0.03020811,0.06957737,0.05082859,-0.01456222],"last_embed":{"hash":"14pdm0q","tokens":251}}},"text":null,"length":0,"last_read":{"hash":"14pdm0q","at":1753423555406},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color=\"#ff0000\"><i>markov.exe</i></span> by Cristiano Lopes</u>#{6}","lines":[57,62],"size":575,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12315123,-0.0366852,-0.07016962,-0.02272327,-0.03919167,0.09896145,0.00981359,0.01497642,0.09706024,-0.00119545,0.03439759,-0.02428255,0.03262554,0.05267927,-0.03286388,-0.01558438,-0.03146018,-0.00216233,-0.02748318,-0.03549999,0.0629625,-0.08436579,-0.02788427,-0.10581574,0.04480873,0.03327623,0.00669075,-0.08308253,-0.05281486,-0.24700542,0.02752678,-0.0127957,0.02444779,-0.05804511,-0.07893907,-0.02615786,-0.02606785,0.0394395,-0.07054807,0.0181232,0.02235798,0.03297396,0.00811175,-0.04786466,0.02439815,-0.05675011,-0.03079779,-0.00763726,0.00649461,-0.02988691,-0.08711239,0.00702817,0.04379418,0.03211318,0.06439951,0.00985579,0.04557506,0.05955514,0.03335343,0.03083526,0.03004485,0.06621084,-0.18803804,0.03318095,-0.0204152,0.0292172,-0.03766141,0.00897013,0.02074968,0.0739786,0.00780575,0.04067123,-0.01848687,0.02983939,0.06876782,-0.03000967,-0.01278747,-0.02904958,-0.02991974,0.04472801,-0.04641972,-0.01976683,-0.02679778,-0.03759421,-0.0014639,0.06240815,0.02589789,-0.03691097,0.01549978,-0.00722217,0.02346263,0.06397157,0.01786938,0.03949364,0.00769639,0.00863204,0.07635354,-0.01960913,0.02955888,0.09462503,0.04484453,0.01741878,-0.02245497,0.02017427,0.04934739,-0.03711724,-0.00880768,-0.028282,-0.00486995,-0.0179551,0.04254667,0.00789906,0.07374624,-0.06247963,-0.05630076,0.00249564,-0.00458882,0.05329893,-0.00491477,0.02608101,-0.01386945,-0.02451337,0.01949791,0.016802,-0.00096859,0.02021869,-0.0262137,0.08253876,0.07330818,0.01052957,0.07176495,-0.02216308,-0.09163932,-0.025612,-0.01724781,0.01185848,-0.00925598,-0.02349243,-0.01609126,-0.00683513,-0.01620628,0.00355325,0.05853295,-0.1020365,-0.01893409,0.10661833,0.00811781,-0.02560419,-0.00398403,-0.02934322,0.01176267,-0.01185717,-0.04655689,-0.06317788,-0.00193208,-0.00756503,0.06638062,0.05548996,0.00286496,0.01041417,-0.06687476,-0.06751242,0.0030279,0.16442774,0.00518384,-0.07520483,-0.01871163,0.01371527,-0.0109272,-0.09754758,0.0229356,0.04146505,-0.04442567,0.07544827,0.03715901,-0.03738423,-0.09296775,-0.00557243,0.03668915,0.00693791,0.01253101,-0.01100342,-0.04559583,0.00099688,-0.00688931,-0.05115774,0.01146366,-0.02863868,-0.02635957,0.00209376,-0.01752499,0.00440892,-0.02162061,-0.01910249,-0.04788673,-0.03688331,-0.01366287,-0.01627767,0.02429802,-0.03630283,-0.03106327,-0.00604498,0.02739143,-0.02237948,-0.07626167,0.00801061,-0.02159482,-0.03879068,0.06024709,0.03613121,-0.00974038,-0.01365415,0.01902716,0.06706549,-0.05169051,0.01576708,0.02834205,0.01269054,0.01771934,0.00529193,-0.01094178,0.02359016,-0.06049678,-0.17756326,-0.05951764,-0.03836209,0.0476716,0.04163196,-0.04093422,0.02343895,-0.0666993,0.04979621,0.06078846,0.07152474,-0.06713046,-0.02036561,0.02762781,-0.01139009,0.03937785,-0.08968274,-0.03150176,-0.03795785,0.08328573,-0.00984537,0.0657684,-0.07944067,-0.07324565,0.04318978,-0.01211483,0.13996486,0.04336279,-0.00913025,0.02073426,0.05765299,0.05620873,-0.06856772,0.00330216,0.00596761,0.02810729,0.01496584,-0.00627763,-0.00773451,-0.05234111,-0.05203975,0.03244667,-0.01255126,-0.06476075,-0.05110418,0.0033294,-0.03440926,0.01888342,0.00819242,0.05643751,0.0103977,0.01674606,0.0749579,-0.04174013,0.04740049,-0.04079177,-0.04437825,-0.00544876,0.0089709,0.04901065,-0.00879368,-0.03571435,0.03620844,0.0362596,0.05600145,-0.02249921,-0.00712741,-0.03272597,0.01558333,-0.01048701,0.01299634,0.08668416,0.02019142,-0.00273816,0.01312898,0.00450859,-0.02049987,-0.05154881,-0.02996361,-0.03571762,0.07831516,-0.04798796,0.02083858,0.06746884,0.0379412,-0.0118477,0.09322635,0.08250066,-0.00200078,0.03656897,0.01761848,0.035064,-0.03747854,0.03536893,0.04041655,0.02397723,-0.28867587,0.03727955,-0.0014092,0.07080146,0.00078683,-0.0181392,0.04508066,0.00628165,0.01725445,-0.054058,0.04343861,0.07674915,0.07631703,-0.06097532,-0.03740228,-0.00692989,0.00339465,-0.04725115,0.05169548,-0.00889048,0.04868166,0.04009793,0.23567417,-0.04487544,0.01665228,0.01068435,0.03060537,0.01408681,0.00987323,-0.01328656,-0.02561044,0.00476533,0.09447852,-0.0496638,0.03023509,-0.00142231,0.00232192,0.03117469,-0.01100159,-0.02215748,-0.04074911,0.00660723,-0.04476058,0.0139123,0.12398691,0.01857267,-0.02104886,-0.07784399,0.03668281,0.12035128,-0.0498188,-0.06653626,-0.02335153,-0.03085229,-0.01104209,0.00625236,-0.01611881,-0.02723222,-0.01812546,0.02075696,-0.00674145,-0.01715719,0.05899607,0.04376522,-0.00413513],"last_embed":{"hash":"2wfqkw","tokens":454}}},"text":null,"length":0,"last_read":{"hash":"2wfqkw","at":1753423555690},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>","lines":[71,111],"size":5775,"outlinks":[{"title":"**wonder grid** lotto strategy","target":"https://saliu.com/bbs/messages/9.html","line":8},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":15},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":17},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":21},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":25},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":27},{"title":"**_markov.exe_ lotto program**","target":"https://saliu.com/freeware/markov.exe","line":29},{"title":"_**Markov Lotto Program: Help, Instructions**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":31},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":33},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":40}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12932114,-0.01808317,-0.03613322,-0.01441805,-0.01006539,0.08585179,0.03097192,0.03667418,0.09731335,-0.00868581,0.01776938,-0.04430643,0.01337871,0.0520366,-0.01631173,-0.00898504,-0.02281316,0.00586221,-0.01761643,-0.02170113,0.07599279,-0.05160061,-0.02743684,-0.12439208,0.06684322,0.026559,0.00793919,-0.06519272,-0.04336961,-0.23793475,0.02624344,-0.02641418,0.00182374,-0.04076036,-0.07812677,-0.01410115,-0.0167156,0.02271235,-0.06053808,0.02874267,0.03850603,0.03385147,-0.01043938,-0.04599972,0.0096551,-0.0555049,-0.02657666,-0.01133025,-0.01991252,-0.02318057,-0.08778474,0.02874018,0.02893014,0.01774974,0.0587315,0.01957205,0.04531032,0.07164147,0.01791668,0.05156862,0.0113273,0.05745266,-0.20182273,0.03468551,-0.00350022,0.01328903,-0.02882138,0.01849735,0.02302695,0.07527521,0.00280216,0.0579591,-0.00637876,0.02810012,0.06773656,-0.03741297,0.00749599,-0.03519555,-0.03917564,0.05826112,-0.04287793,-0.01339886,-0.01049753,-0.02777542,-0.01846977,0.06694368,0.00119132,-0.04749591,0.03251561,-0.01906178,0.0342199,0.06084474,0.02735477,0.0318606,-0.00633584,0.03855743,0.06606358,-0.0133426,0.02134859,0.09667861,0.01985089,0.03746547,-0.01696356,0.02400475,0.06236667,-0.01762142,-0.00197511,-0.0380423,-0.00019478,-0.0054995,0.02397704,0.01053916,0.05621566,-0.0357902,-0.059384,0.00345463,-0.00803636,0.06154634,0.00185557,0.02205092,-0.03810208,-0.00657868,0.00581628,0.02823316,-0.02298458,-0.01281936,-0.02810453,0.05895415,0.04774056,-0.01098933,0.06152604,-0.04092812,-0.0899185,-0.01402566,-0.00100409,0.01511525,-0.01249908,-0.01168864,-0.00319438,0.03116908,-0.0169482,0.00072533,0.04745172,-0.10043015,-0.05486057,0.11091211,0.02172279,-0.02988011,-0.01022932,-0.04018609,0.01589284,-0.00080733,-0.00768911,-0.07259275,-0.00876557,-0.01282448,0.06091773,0.06828003,-0.00675794,0.00452612,-0.07820489,-0.06971325,0.00282289,0.19692826,-0.03179611,-0.0658635,-0.0206387,-0.00211274,-0.00520966,-0.08247674,0.015471,0.04341815,-0.05936409,0.05463892,0.0344287,-0.05376857,-0.07971203,0.00142568,0.00232585,0.00641889,0.00926163,-0.03389544,-0.02989803,0.02046221,0.00487421,-0.06608369,-0.00740339,-0.01451017,-0.02956564,-0.00302974,-0.03367149,-0.01593989,-0.02220139,-0.02280359,-0.05561696,-0.04432239,-0.02128798,-0.02483186,0.02459345,-0.0146132,-0.04050681,-0.00056493,0.03024729,-0.00455069,-0.0815913,0.00070712,-0.02810612,-0.04403283,0.04756618,0.04691244,-0.01770516,-0.01893892,0.01015423,0.05342776,-0.03845745,0.00443069,0.03780778,0.02694804,-0.00076219,0.0202423,-0.0174244,0.03890611,-0.08612031,-0.19287145,-0.05871333,-0.06614688,0.03660063,0.04687989,-0.04128067,0.02656568,-0.0620499,0.04834852,0.07269167,0.04389679,-0.06844304,-0.00754484,0.01690337,-0.00646188,0.01351999,-0.08970309,-0.05149307,-0.05320975,0.07926346,-0.00584641,0.06556645,-0.07198893,-0.07072908,0.0375734,-0.02096263,0.15761027,0.04673358,0.03937285,0.00826295,0.05378306,0.03920071,-0.05565532,-0.03334869,-0.00032382,0.03803389,0.02198284,0.00494366,-0.00816856,-0.04604143,-0.05235781,0.02910319,-0.01269018,-0.05394237,-0.05988258,0.0185867,-0.02343336,0.01849584,0.01122046,0.06215497,0.03790682,0.01711413,0.07403079,-0.05239703,0.04483068,-0.03168338,-0.03301441,0.0023527,0.01198796,0.05694604,-0.01030209,-0.0238891,0.03584217,0.01641667,0.0640733,-0.01681332,0.000769,-0.05382612,0.01556644,-0.00337074,0.0115664,0.07094172,0.01408851,-0.01110635,0.0450594,0.01267276,-0.00327418,-0.053032,-0.01820467,-0.03753315,0.06440397,-0.03301547,0.03361342,0.06454807,0.04387267,0.00067134,0.09705367,0.05310333,-0.01136164,0.03058786,0.01556009,0.02424179,-0.05639469,0.02095496,0.0377435,0.03425997,-0.29569021,0.02407141,0.01939915,0.07324945,0.00536441,-0.02474684,0.05972551,-0.01720808,-0.00398152,-0.0416186,0.04539049,0.06158317,0.05898524,-0.05225878,-0.05595655,-0.01535061,0.0320332,-0.0455997,0.08010893,0.00277106,0.04386818,0.04454094,0.24334081,-0.03883318,0.01703774,0.01507648,0.03258051,0.00102717,0.02287014,-0.01913515,-0.00016372,0.01439623,0.06550635,-0.0258875,0.06258292,0.01230349,-0.012729,0.04074409,0.01422382,-0.03069855,-0.01377903,-0.00310941,-0.03754603,0.00510611,0.10219168,0.01123863,-0.02663671,-0.08493654,0.03869773,0.11229184,-0.05220314,-0.0769309,-0.01684095,-0.02815273,-0.00191771,0.00948282,0.00411205,-0.03010933,-0.02462727,-0.01381051,-0.00536227,-0.00384259,0.03744865,0.02823093,0.01294468],"last_embed":{"hash":"1i8gknr","tokens":217}}},"text":null,"length":0,"last_read":{"hash":"1i8gknr","at":1753423555867},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{1}","lines":[73,76],"size":555,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13150615,-0.03956865,-0.07465236,-0.03270231,-0.04250837,0.08392348,-0.01761095,-0.01017065,0.06773322,0.00626222,0.06654186,-0.01385849,0.05384406,0.03725782,-0.031428,-0.02879632,-0.03373068,-0.00220636,-0.0370269,-0.04427733,0.08203936,-0.09226482,-0.0175184,-0.08330709,0.03317901,0.04793771,0.00032388,-0.09172533,-0.05297968,-0.24629885,0.01406604,0.01644653,0.03634885,-0.05386763,-0.04431843,-0.03270792,-0.03791653,0.03549538,-0.08310428,0.01026278,0.01719604,0.03100876,-0.00264219,-0.06114954,0.00270884,-0.06098816,-0.04427866,-0.00345753,0.02807596,-0.04055319,-0.08144993,-0.00486769,0.04043383,0.05560389,0.05873094,-0.01547549,0.05445554,0.05422476,0.02001244,-0.01460439,0.06622546,0.0687388,-0.19068453,0.04204837,-0.01716411,0.03058237,-0.04718734,-0.00983929,-0.00684097,0.06328624,-0.00118897,-0.00928966,-0.02743956,0.04950534,0.05530437,-0.03462948,-0.02657714,-0.04413728,-0.01913588,0.0044715,-0.05130839,-0.0378751,-0.02914013,-0.04216639,0.02312176,0.05368423,0.04251515,0.00681946,0.00769556,-0.02479455,0.01317548,0.04088209,0.04677987,0.06113012,0.01004328,-0.0143803,0.0483647,0.01190507,0.04613632,0.11584532,0.04549245,-0.04488258,-0.00278074,0.01701692,0.03999709,-0.03781932,-0.01369455,0.00143666,-0.01621616,-0.00153907,0.03115395,-0.01747888,0.07905861,-0.08173223,-0.02201953,-0.00975772,0.02207867,0.04450705,-0.02478867,0.03431994,0.00282604,-0.02731204,0.03132069,0.02627035,0.01413205,0.02804894,0.01252829,0.08108565,0.08077708,0.02174668,0.0664857,0.00630766,-0.08506267,-0.02952592,-0.03741707,-0.00170877,-0.00171537,-0.04413886,-0.03427731,-0.03322038,-0.01035751,-0.00656684,0.04039802,-0.09056472,0.00892347,0.12128533,-0.00131252,-0.00825915,0.02028214,-0.0023041,-0.00119624,-0.00375865,-0.05797442,-0.0557557,0.00630109,0.02073613,0.06266535,0.03646137,-0.0359525,0.02360178,-0.03808219,-0.05613409,-0.02164666,0.17598468,0.02216084,-0.06798068,-0.01705571,0.04661579,-0.00611006,-0.08820328,0.0472452,0.04285487,0.00185503,0.05480748,0.05476744,-0.0281962,-0.08008188,-0.0373026,0.02222299,0.00859669,-0.00662478,-0.00090049,-0.06626119,-0.00232305,-0.00541688,-0.03011514,0.02011058,-0.03335147,0.01999154,0.02956207,0.00309695,0.02774471,-0.01900284,-0.01182831,-0.05057175,-0.02354707,0.00155905,0.01208945,-0.00622307,-0.05545278,0.00766729,0.00908292,0.00947247,-0.02796424,-0.0260994,0.00968605,-0.02597167,-0.00258853,0.06291854,0.0324666,-0.01963971,0.00175068,0.04060119,0.05591535,-0.06196544,0.01854897,0.01898668,0.0036496,0.03279515,0.02007269,0.00458611,-0.01444574,-0.03736586,-0.19299728,-0.02341017,-0.01079942,0.02761514,0.04938341,-0.05875317,0.00870143,-0.04256292,0.0538575,0.03121463,0.08986147,-0.03796192,-0.04278056,0.0329879,-0.0157163,0.04544801,-0.03810817,-0.01703596,-0.0060712,0.08607605,-0.00684572,0.03368806,-0.06289417,-0.06028508,0.03764745,-0.00067159,0.12585327,0.04142374,-0.0423317,0.02284063,0.03742003,0.05390748,-0.08071017,-0.01501051,0.01246934,0.02992284,-0.01940437,-0.00608741,0.01673295,-0.06202826,-0.0670638,0.03143967,-0.01088061,-0.05536961,0.00332712,-0.01573114,-0.02267767,-0.00354047,0.00775616,0.02281418,-0.00879967,0.01905148,0.04563164,-0.02362413,0.05109515,-0.03393874,-0.07941372,-0.02576285,-0.01050741,0.02233773,0.01269576,-0.03941828,0.03473294,0.0273107,0.03027186,-0.02894058,-0.01253413,-0.01203494,0.00292971,-0.02612652,0.01495417,0.1043048,0.00331067,-0.01921273,-0.03620141,0.00386121,-0.0203483,-0.06698911,-0.04925216,-0.02924418,0.058994,-0.07546443,0.02611123,0.03481085,0.0423792,0.01966118,0.0642897,0.07663526,-0.01284036,0.02746196,0.03318133,0.04206187,-0.01421409,0.0615288,0.0507424,0.01284292,-0.28359401,0.0552762,-0.00892063,0.04442516,-0.00519565,0.01917085,0.0173723,-0.01363988,0.02534868,-0.04985076,0.04896008,0.10373726,0.08749187,-0.07116821,-0.01206848,0.00440968,0.02636216,-0.05049577,0.03943909,-0.01199647,0.04700466,0.04221889,0.24560794,-0.02726186,0.00141751,-0.01050048,0.00528807,0.03884894,0.00777762,0.01119063,-0.03874695,-0.01557427,0.08552627,-0.04090372,0.00674274,0.01731301,0.00540184,0.01302266,-0.00796222,0.00593891,-0.06715427,-0.00495516,-0.05467418,0.02185176,0.14654694,0.04951179,-0.03595529,-0.06103701,0.03071983,0.10362042,-0.0551835,-0.080979,-0.0566728,-0.01225377,-0.00522077,0.02032729,-0.05705544,-0.04149487,-0.01194846,0.03400302,0.01512254,0.00102856,0.08070876,0.04955098,0.00176182],"last_embed":{"hash":"9hihu2","tokens":228}}},"text":null,"length":0,"last_read":{"hash":"9hihu2","at":1753423555940},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{2}","lines":[77,77],"size":550,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1606061,-0.02565165,-0.06720248,-0.01371506,-0.05191889,0.08005146,-0.02554543,-0.00988726,0.09270012,0.00065331,0.02871239,-0.02012679,0.07901058,0.03197483,-0.00337392,0.00086144,-0.01886667,0.00850944,-0.02611877,-0.05384121,0.07507195,-0.0882392,-0.02837175,-0.0805869,0.03140448,0.04077578,0.00307618,-0.07308351,-0.01628337,-0.23960014,0.04557397,-0.04269091,0.04584244,-0.04937403,-0.06534269,-0.02346356,-0.0123068,0.03433054,-0.07530174,0.02459373,-0.00486203,0.02548883,0.00669287,-0.05935419,0.01985927,-0.05400732,-0.01710179,-0.007257,0.02333418,-0.03627037,-0.09538878,-0.01117389,0.01919111,0.0179013,0.06393455,0.02139668,0.04821875,0.08022054,-0.02753877,0.03308586,0.03337956,0.05507341,-0.16680318,0.03794793,-0.00658685,0.03489175,-0.01162243,-0.00286064,0.04330977,0.08614656,0.0264432,0.01903409,-0.02312746,0.05623261,0.05557311,-0.01840677,-0.03426865,-0.00869638,0.01126172,0.03049085,-0.07778431,-0.0463897,-0.01131737,-0.03751898,-0.01336241,0.04950637,0.04197058,-0.00829068,-0.01691239,-0.02569656,-0.01675991,0.05650746,0.02232747,0.038651,0.00943252,0.00922362,0.05108326,-0.00278402,0.02516186,0.11197915,0.01332768,0.00724501,-0.02416175,0.00171776,0.04476376,-0.04349686,-0.00368558,-0.0508018,-0.01397066,-0.02083528,0.0629281,-0.01181176,0.04668628,-0.09167632,-0.03946987,0.00754832,0.01184132,0.06412125,-0.00679955,0.02752353,-0.01336586,-0.03528053,0.0551114,0.01133675,0.00336028,0.01296452,-0.02137844,0.08908451,0.03685369,0.05164449,0.07952113,-0.02789247,-0.08461396,-0.04161044,-0.01481464,-0.01237181,-0.00662237,-0.0665599,-0.00434056,-0.02581784,-0.01710138,0.00263193,0.04109779,-0.10756043,0.0027467,0.12509657,-0.00050852,-0.00204626,-0.01171088,-0.04472782,-0.00016162,-0.02014202,-0.03746044,-0.09491654,0.02145468,-0.00660336,0.07726258,0.04498158,0.0164374,0.0362814,-0.02286166,-0.04171854,-0.01626943,0.17666471,0.02208149,-0.07922171,-0.02088925,0.00936067,-0.01533626,-0.06845345,0.04667285,0.07404383,-0.01395808,0.09180616,0.0602097,-0.03738647,-0.10768446,-0.02209265,0.01133921,0.01202655,-0.02804231,-0.01021803,-0.02202509,0.00445953,-0.00390719,-0.06409649,0.02554709,-0.00513731,-0.01187143,-0.01554203,-0.00714858,0.02013516,0.00822556,-0.01409474,-0.04569415,-0.03827754,-0.02110552,-0.03447434,0.01815243,-0.0487428,-0.02120047,-0.00698202,0.01329948,0.0167437,-0.05707319,-0.00657926,-0.04435566,-0.00377915,0.04235391,0.05451298,-0.02119933,-0.01135557,0.02521534,0.05484052,-0.0585227,0.03359929,0.0138534,0.0089468,0.0491316,0.01183521,-0.01536376,0.01836511,-0.03961731,-0.18297023,-0.04415123,-0.02647642,0.04408855,0.01953483,-0.06957316,0.01219897,-0.04718998,0.02001131,0.03544324,0.07391816,-0.0701539,-0.01437464,0.02761303,-0.01171936,0.05433476,-0.05636447,0.00745549,0.01218901,0.0723801,-0.02170233,0.06213928,-0.04275764,-0.07504275,0.030238,-0.01523481,0.12988812,0.02416973,-0.01958441,0.02865452,0.0426867,0.04624692,-0.04921751,-0.03561658,0.00131679,0.02970354,0.0157018,0.00344814,-0.00043491,-0.04824031,-0.07336088,0.01888313,0.01211299,-0.08237921,-0.01958267,-0.0172071,-0.01424646,-0.00282116,0.02468811,0.03695851,0.00171446,0.0246808,0.04925464,-0.04715369,0.03312096,-0.05714653,-0.05950907,-0.01560152,0.01671679,0.01646289,-0.01933417,-0.03687457,0.02521798,0.02902454,0.02655358,-0.00335835,0.00553482,-0.02769128,-0.00553302,-0.01382288,0.01411612,0.11006346,0.01510628,0.0242072,0.00438257,0.0003817,-0.02473265,-0.03591464,-0.0498723,-0.03973714,0.05407851,-0.0719754,0.02761919,0.06259438,0.04895572,-0.00501757,0.09070531,0.11580842,0.00079742,0.05258348,0.0185324,0.00312642,0.00590953,0.04562036,0.0614855,0.02156777,-0.28860429,0.0251063,-0.02330843,0.07764421,-0.01171217,-0.00499512,0.0569594,-0.02229002,0.00432851,-0.04567171,0.03349194,0.06125161,0.07182532,-0.02218841,0.01046808,0.00676153,0.0087374,-0.04316132,0.03085617,-0.04383358,0.04535322,0.0137697,0.24808823,-0.02484292,0.02012569,0.01487513,0.00551076,0.02468952,-0.00940429,-0.01091347,-0.01632846,0.01123711,0.05869971,-0.05597453,0.03270188,0.01247822,0.0177233,0.02148759,-0.02078761,0.00062074,-0.07227677,0.00973958,-0.02593886,0.02407822,0.13894856,0.05595722,-0.00853023,-0.07216351,0.00887391,0.11141263,-0.05755648,-0.06095045,-0.02061599,-0.0084143,-0.00524426,0.01026349,-0.02206257,-0.0333751,-0.03199458,0.01035148,0.04694229,-0.02702278,0.04871677,0.03314954,-0.03068826],"last_embed":{"hash":"a0apqf","tokens":204}}},"text":null,"length":0,"last_read":{"hash":"a0apqf","at":1753423556016},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{3}","lines":[78,78],"size":531,"outlinks":[{"title":"**wonder grid** lotto strategy","target":"https://saliu.com/bbs/messages/9.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1615916,-0.04012809,-0.05372754,-0.01774719,-0.05668342,0.07189373,0.00709669,0.01334308,0.07707171,0.01475091,0.03236775,-0.0139076,0.06858478,0.03875332,-0.00581719,0.00572929,-0.03866975,0.02883873,-0.05405207,-0.0327916,0.05596518,-0.09188555,-0.0344942,-0.09906723,0.03088466,0.04197607,-0.0125707,-0.07265218,-0.00987422,-0.24148095,0.04630582,-0.01290428,0.04157228,-0.04847414,-0.06649275,-0.01527388,-0.02805109,0.02397248,-0.07390956,0.01822872,0.00232768,0.03112928,0.0008888,-0.05129142,0.0027229,-0.05728628,-0.00832509,-0.00446803,-0.01407561,-0.02213765,-0.07897257,-0.03046674,0.00342708,0.03746444,0.08222629,0.05225503,0.03507553,0.06055152,-0.03167582,0.03151488,0.03328285,0.06351386,-0.16399944,0.03610217,-0.01761341,0.01741777,-0.00621442,0.01049695,0.02431399,0.08138714,0.03654163,0.01799969,-0.02598944,0.0393285,0.04499683,-0.01534265,-0.02437703,-0.02179609,0.00176168,0.02703145,-0.06558041,-0.03811546,-0.01030699,-0.05195459,-0.01219261,0.05677425,0.05254947,-0.02465058,-0.02434168,-0.0410871,-0.02537015,0.05389765,0.03707101,0.04193312,0.01128338,0.03201241,0.0502754,-0.01848587,0.02742633,0.11702353,0.00095613,0.01568131,-0.00400278,-0.01103244,0.02353545,-0.05319503,0.00307931,-0.04946499,-0.02287532,-0.01771262,0.04835987,-0.00747939,0.04361242,-0.07783281,-0.03278869,-0.00103098,0.02426077,0.06626587,-0.01090131,0.04645246,-0.03473072,-0.013093,0.05941854,0.01376893,-0.00788756,0.00849313,-0.0146763,0.07604928,0.03972606,0.02938286,0.0685476,-0.00420654,-0.05607812,-0.03864707,-0.00693529,-0.00502904,0.01248931,-0.07695353,-0.00036918,0.00456725,-0.01806932,-0.00317482,0.04791844,-0.11820213,0.01795083,0.12148589,-0.00888484,-0.00052951,-0.00950228,-0.01268116,-0.01084171,-0.02611585,-0.02535302,-0.07856789,0.01951027,0.02576718,0.07403065,0.05841397,-0.00234848,0.02997096,-0.02131495,-0.03984221,-0.03090486,0.1907528,0.02228427,-0.11095847,-0.01445833,0.02336898,-0.01873505,-0.07733917,0.03968663,0.06986289,-0.03210465,0.07455913,0.06832672,-0.04515431,-0.11768117,-0.02158511,0.01729091,-0.01134342,-0.00131277,-0.01252609,-0.02300725,0.02021243,-0.02709223,-0.0554318,0.0024264,0.01440663,-0.0050536,0.00947335,-0.0002224,-0.00687121,0.00906,0.00559939,-0.03103677,-0.04123947,-0.02664086,-0.05518665,0.02404637,-0.02446006,-0.01996024,-0.00697349,-0.00472439,-0.00035353,-0.03506625,-0.000947,-0.04956964,0.00039217,0.04140674,0.05222578,-0.02484995,-0.00232778,0.03272938,0.08281621,-0.06654405,0.02640708,0.01815055,0.018411,0.0430242,0.01042298,-0.00148231,0.00293533,-0.03957852,-0.18707453,-0.04152987,-0.03970833,0.03303609,0.03143779,-0.06389224,0.0160217,-0.05505194,0.03272206,0.05752425,0.06736396,-0.05316148,0.00088618,0.00610005,-0.00709443,0.03766512,-0.06630749,-0.00465319,-0.00247857,0.07102538,-0.01863555,0.07871922,-0.05391243,-0.0733242,0.03688622,-0.02405995,0.14797041,0.0579215,-0.02870633,0.02238057,0.03300324,0.06168932,-0.05435043,-0.04437421,-0.00402286,0.04848118,0.00471277,-0.01697195,-0.00882486,-0.05137417,-0.08121603,0.01561213,-0.01414745,-0.09920918,-0.01087208,-0.0137358,-0.00965969,0.013252,0.01227951,0.0325123,0.00734722,-0.00529256,0.03766971,-0.01385605,0.03590386,-0.04237536,-0.08685493,0.0073511,0.00234845,0.02399007,-0.01673523,-0.03161181,0.00692516,0.0243216,0.0302227,-0.00880112,-0.00962942,-0.01598458,-0.006031,-0.01224168,0.00798829,0.07926598,0.02948476,0.02714364,-0.00886759,-0.00049159,-0.00774258,-0.0531609,-0.03770342,-0.03419295,0.03454928,-0.06475009,0.01208446,0.06551545,0.05162599,-0.00977954,0.07582989,0.10050692,-0.00384672,0.03089048,0.01336159,0.00768484,0.00645849,0.02900791,0.06996813,0.00775586,-0.29003137,0.03068857,-0.0196857,0.07667331,-0.03317763,0.01457225,0.03737175,-0.02638956,-0.00172082,-0.05098438,0.04859352,0.07383744,0.05782262,-0.03456295,0.00784636,0.02564415,0.03084166,-0.03564914,0.03616515,-0.0165018,0.05684794,0.01844449,0.2484113,-0.00745365,0.0269082,0.00615492,-0.01442009,0.0231267,-0.02660302,0.0094667,-0.032887,0.00111305,0.05703975,-0.05898891,0.02917266,0.03986483,0.01657673,0.02157957,-0.01000396,0.01074398,-0.08613233,0.01991617,-0.01417593,0.02443177,0.134826,0.0402737,0.00746704,-0.07036847,0.00350459,0.10710014,-0.05733571,-0.05432962,-0.01198077,0.00472011,0.00005029,0.01429791,-0.00778614,-0.03294903,-0.03150631,0.02525337,0.05279003,-0.02312996,0.06130416,0.02781889,-0.03938821],"last_embed":{"hash":"o7hqkm","tokens":183}}},"text":null,"length":0,"last_read":{"hash":"o7hqkm","at":1753423556087},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{4}","lines":[79,79],"size":396,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14232767,-0.04229363,-0.08134227,-0.03578489,-0.06072649,0.07098101,0.0025502,0.00302839,0.09617189,0.01544267,0.02127527,-0.01584508,0.05670937,0.05069164,-0.01611395,-0.02126734,-0.02607556,0.0324588,-0.06081456,-0.04293582,0.06614248,-0.07809641,-0.05023669,-0.07420983,0.07666689,0.04408935,0.00518441,-0.08291815,-0.01931079,-0.24340695,0.03766578,-0.01899415,0.03423984,-0.04497793,-0.05497864,-0.01434134,-0.02367493,0.03439016,-0.07558966,0.00477704,-0.01217808,0.03271859,0.00250035,-0.05924591,0.0025598,-0.03784116,-0.01824771,-0.00644968,-0.00641798,-0.02619328,-0.08196206,-0.00958017,0.02054663,0.02878297,0.06225898,0.02631846,0.05326729,0.07853585,-0.00171983,0.02904679,0.05070062,0.06483543,-0.17505342,0.03285562,-0.02442402,0.02404778,-0.02384593,-0.01938355,0.0162629,0.07601564,0.04701619,0.01197755,-0.05885691,0.05494026,0.04765833,-0.0434345,-0.02037666,-0.03124108,-0.01972879,0.01809596,-0.0598154,-0.03450852,0.00188217,-0.03239166,-0.01630897,0.04406467,0.03033453,-0.02254962,0.00055895,-0.03796011,-0.00898734,0.0438622,0.01953157,0.04198581,0.02712534,0.03972464,0.0599304,-0.00926721,0.03577083,0.11526418,0.01323252,0.01562594,-0.02690792,0.0193763,0.04752734,-0.05902148,0.01315779,-0.05238565,-0.0410938,-0.02020363,0.0326926,-0.00929042,0.05579653,-0.0714574,-0.0317517,0.01225121,0.01697959,0.05874087,0.0048852,0.04044237,-0.01907001,-0.01920123,0.03962367,-0.00936908,-0.00478076,0.00600106,-0.00897142,0.07143294,0.05222668,0.01961458,0.0871537,-0.0020305,-0.09141444,-0.04116182,-0.01747352,0.00465443,-0.00922457,-0.0670623,0.00590668,-0.01343374,-0.00379234,-0.00849489,0.04731988,-0.09304038,0.01022082,0.1338409,0.0155615,0.01826939,-0.00598333,-0.02301336,-0.01440896,-0.04364675,-0.05036459,-0.08050548,0.01765998,0.01534614,0.04399528,0.05328818,-0.02629037,0.01511841,-0.00542818,-0.04533292,-0.01700342,0.17752464,0.01036796,-0.07407402,0.00300817,-0.00218489,-0.01558337,-0.06459048,0.04305067,0.08150756,-0.03284815,0.0686629,0.06518058,-0.05742648,-0.11577466,-0.05179631,0.02862241,-0.00630898,0.01171652,-0.01333412,-0.02976752,0.02480952,-0.00485646,-0.01476632,0.02674819,0.00215389,0.01184656,0.04072072,0.00671229,0.00596062,-0.01935877,0.02380191,-0.03327,-0.02892946,-0.04113844,-0.0229972,0.01648506,-0.01766479,-0.04514152,0.01598793,0.01234517,-0.00645884,-0.020903,-0.00103152,-0.03078904,0.00493118,0.03244347,0.046055,-0.04635493,-0.0154185,-0.00151419,0.0734958,-0.0757318,0.01007798,0.01201583,0.02425863,0.02308122,-0.00137343,-0.01186644,0.00981572,-0.02686227,-0.19164197,-0.0446166,-0.01653469,0.05596083,0.06166881,-0.04800457,0.00532178,-0.05971564,0.00714504,0.04556527,0.0875822,-0.05195212,-0.03031424,0.01688381,-0.00576052,0.07486383,-0.07761709,-0.00184337,-0.02425151,0.08311861,-0.02076216,0.05000284,-0.04246256,-0.06371757,0.05839375,-0.01778992,0.13001797,0.04251672,-0.02182822,0.03621098,0.03141436,0.0290225,-0.06876223,-0.03428376,0.00471762,0.04015167,0.0011213,-0.01850495,0.00777027,-0.02105188,-0.05812114,-0.00507066,-0.00629388,-0.07110926,-0.02775217,-0.01613009,-0.00872319,0.01190007,0.03407393,0.02899157,0.00689534,0.01791641,0.02044877,-0.0330175,0.02749795,-0.0558942,-0.06802354,-0.00415005,-0.00376688,0.03298948,-0.00203595,-0.05414877,0.02945045,0.00473876,0.03309465,-0.00620455,-0.01115682,-0.00523047,-0.01268584,0.01583226,-0.01316224,0.10053781,0.01700764,0.03088054,-0.00897344,0.00677267,0.01208519,-0.03289235,-0.0162137,-0.03313498,0.04414769,-0.07046933,0.02802002,0.06806867,0.05413856,-0.01850819,0.08647162,0.08315979,-0.02013527,0.04112435,0.01425448,0.0278765,-0.01719024,0.03350395,0.02347403,0.01189665,-0.2804251,0.05121302,-0.0274603,0.06218638,-0.03295201,-0.00151498,0.03286261,-0.02834816,0.00993777,-0.05462098,0.04555926,0.08382635,0.05970057,-0.05667554,-0.01267421,0.02702881,0.02352264,-0.04920186,0.04435949,-0.0083988,0.06083211,0.02284188,0.23587804,-0.03765175,0.04299634,0.00085064,-0.00041097,0.00661177,-0.01112397,-0.00690847,-0.04825304,-0.01091333,0.05978639,-0.03967794,0.01742499,0.04570663,0.02798626,0.02850814,0.00110778,0.02987621,-0.08685786,-0.00080696,-0.03808882,0.00496863,0.16728634,0.04354288,-0.00324712,-0.07422577,0.02220299,0.12453517,-0.06551533,-0.07321785,-0.02977829,0.00764416,-0.01728224,0.00853291,-0.02629472,-0.02319469,-0.02958577,0.01810424,0.05646812,-0.01514691,0.0505538,0.02906212,-0.01340964],"last_embed":{"hash":"xxhxpe","tokens":211}}},"text":null,"length":0,"last_read":{"hash":"xxhxpe","at":1753423556152},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{7}","lines":[82,82],"size":576,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.16263853,-0.01783356,-0.07310223,-0.02427725,-0.0331332,0.05321962,-0.00531366,0.00853819,0.08033665,0.00050237,0.0299817,-0.03330417,0.06192608,0.02168891,0.01741104,0.00049973,-0.01180515,0.02418432,-0.04805732,-0.05089835,0.06551355,-0.05152978,-0.04161865,-0.0817654,0.04639297,0.03035379,-0.0063816,-0.05290247,-0.01245192,-0.22018251,0.04448363,-0.00876923,0.04809005,-0.05637467,-0.06560621,-0.01624217,-0.02652435,0.034599,-0.07633223,0.03149644,0.00550593,0.01775103,0.00686935,-0.07443164,0.00050719,-0.04632682,-0.02412188,0.01406249,-0.00615965,-0.02129885,-0.08181068,0.00707397,0.00488886,0.036077,0.04731707,0.03247634,0.03589455,0.08647493,-0.00809285,0.02665451,0.05114298,0.08575849,-0.16698289,0.04670043,-0.01358793,0.0285703,-0.00900844,-0.00799343,0.05291026,0.04749954,0.0406095,0.03523082,-0.02234516,0.0545691,0.05254618,-0.03941942,-0.02049599,-0.01398548,-0.01912794,0.04304988,-0.07235636,-0.06530944,-0.0170976,-0.02985985,-0.00769898,0.0661962,0.03340372,0.00427885,0.02329437,-0.0362608,-0.01865287,0.04635416,0.03064928,0.01788711,0.0134124,0.02840627,0.04640018,-0.00829076,0.03527496,0.12236676,0.01999635,-0.01060016,-0.00521637,0.0000552,0.04426454,-0.02553665,-0.01346532,-0.06705538,-0.01554191,-0.0125447,0.04901148,0.01385422,0.0405449,-0.06983923,-0.04657562,-0.00677278,-0.00230927,0.05919165,-0.0010758,0.02391392,-0.03642978,-0.0232835,0.03468781,0.00842992,-0.01264591,0.00497661,-0.00001305,0.07329345,0.03750101,0.01111224,0.05236549,0.01754715,-0.10782312,-0.03336359,-0.01197692,-0.01469889,-0.02754062,-0.08189858,-0.01291379,0.03608628,-0.01755149,0.01715845,0.02883381,-0.11241051,-0.01276172,0.08982164,0.02169166,0.00803609,0.00522581,-0.01898568,-0.00005697,-0.02133636,-0.01307772,-0.08027941,0.01955176,0.03288627,0.07354184,0.04476524,-0.00370165,0.04081621,-0.02539862,-0.03179035,-0.02709154,0.20068362,-0.00758744,-0.10843083,-0.01399609,0.01446979,0.00305366,-0.06233797,0.03115105,0.07655653,-0.01286797,0.07157662,0.05096205,-0.07228771,-0.10933322,-0.03405914,0.00699832,-0.00306789,-0.0113355,-0.0472299,-0.02795813,0.04117526,-0.01025547,-0.07383957,0.0228482,0.00238146,0.01384328,0.02290935,0.01502366,0.01595917,0.01749282,-0.01167373,-0.04469265,-0.04959212,-0.01783864,-0.02525436,0.03696837,-0.03782957,-0.03778586,0.0136158,0.02086652,0.01449379,-0.0318428,-0.019558,-0.03491418,0.00394284,0.02514705,0.05312072,-0.01571019,0.00070476,0.01694468,0.08336104,-0.07168351,0.00794895,0.01093819,0.02125875,0.0224399,0.02904168,-0.02122173,0.01200089,-0.07600117,-0.19807944,-0.02550933,-0.04199332,0.04679346,0.02343358,-0.05931852,0.00281482,-0.07334177,0.02406294,0.02730731,0.07670684,-0.05608886,-0.0279833,0.02588023,0.0080693,0.03447616,-0.06589627,-0.02970533,0.00393338,0.07238049,-0.01023879,0.03387529,-0.01453605,-0.07694058,0.0253159,-0.03055246,0.13768001,0.03420219,-0.01242082,0.03977156,0.03654329,0.02118707,-0.05520986,-0.05385761,0.00081611,0.05235867,0.00944778,0.01773617,0.01798653,-0.03135368,-0.07575051,0.01942519,0.0132806,-0.09266254,-0.01124854,0.00303592,-0.01424638,-0.00022904,0.01273174,0.02705477,0.01622763,0.04148896,0.03728551,-0.03678731,0.02370202,-0.05647215,-0.08434425,0.003066,0.00179024,0.01141315,0.00611695,-0.05715424,0.03340396,-0.0026854,0.03669663,-0.01956735,-0.00284607,-0.04440379,-0.00774399,-0.0303183,-0.00095074,0.13103098,0.02617989,-0.00707788,0.00383709,0.0150654,-0.02033917,-0.0593536,-0.02434351,-0.02296176,0.01470404,-0.06065734,0.01743368,0.06742968,0.04440096,0.02018958,0.06015568,0.08137584,-0.00128654,0.03433224,0.0289609,0.01326432,-0.00454656,0.04970941,0.0314649,0.00629405,-0.29280806,0.02232238,-0.0454989,0.06885524,-0.02548585,-0.00372259,0.0337852,-0.02444705,-0.01002124,-0.06342622,0.04371784,0.05612757,0.06441134,-0.03024158,-0.0023398,0.00452115,0.0510767,-0.04772149,0.04229363,-0.00771555,0.03813954,0.0430991,0.27243775,-0.02804399,0.0363413,0.01871465,0.0118283,0.01042792,-0.00346937,0.00285118,-0.00874343,-0.00234685,0.06479366,-0.02227746,0.01220254,0.03511768,0.00322079,0.01349766,-0.00689189,0.00310392,-0.06783157,0.0088706,-0.02791865,0.0229166,0.14065295,0.0466609,-0.00531029,-0.07527281,0.02779065,0.10823842,-0.0644145,-0.07675269,-0.02686512,-0.01103021,0.01908274,0.00798112,-0.02375034,-0.02499843,-0.02678551,0.00345974,0.05979472,0.00227481,0.05466113,0.01236236,-0.02088409],"last_embed":{"hash":"a25aeg","tokens":138}}},"text":null,"length":0,"last_read":{"hash":"a25aeg","at":1753423556227},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{8}","lines":[83,83],"size":290,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12726139,-0.03474953,-0.06158711,-0.01614123,0.00090468,0.04849242,0.00112274,0.04735618,0.05913553,-0.01108457,0.02227368,-0.01257122,0.0624348,0.02888373,0.0007008,-0.02867119,-0.02624317,0.04365294,-0.04159751,-0.04628661,0.06567957,-0.06212531,-0.01829372,-0.07771236,0.03935857,0.01802243,-0.02348322,-0.06629302,-0.02111653,-0.23029612,0.03464221,-0.04976882,0.04912349,-0.05693623,-0.07015499,0.00270728,-0.00037024,0.0129591,-0.06121729,0.01079654,0.02434001,0.03182552,0.01634356,-0.07327126,-0.00987201,-0.0387168,-0.01371494,-0.00309988,-0.01843031,-0.00394382,-0.08534519,-0.01766337,0.01307358,0.02994404,0.08353792,0.01493542,0.0527128,0.08332147,-0.02469044,0.02416,0.03655816,0.09805579,-0.16569073,0.05345781,-0.01214588,0.00892613,-0.00749477,-0.02244855,0.04260229,0.0526288,0.01787331,0.04185254,-0.03510182,0.05763819,0.06364851,-0.03975366,-0.00141504,-0.01690822,-0.02632387,-0.01983435,-0.03747199,-0.07314935,0.02693498,-0.05373628,-0.01471262,0.06045647,0.04633202,-0.03830401,0.00176233,-0.05369173,-0.01024871,0.07158916,0.03484501,0.01380061,0.04487615,0.02278445,0.05469766,0.01059864,0.02938544,0.12839639,-0.03272319,0.02054027,-0.00872534,0.01154748,0.08115483,-0.03621558,0.00136232,-0.00284086,-0.0198644,0.0461813,0.03620772,-0.00972895,0.03179259,-0.04994228,-0.023161,0.02687068,0.05086913,0.04914298,-0.00591028,0.04645596,-0.01284466,-0.00661578,0.05833751,-0.00099898,-0.02448323,-0.00757898,-0.0096032,0.0666853,0.04639173,0.01727712,0.05730341,0.00052269,-0.1040331,0.00400647,0.00022807,-0.03582376,-0.03910266,-0.0647322,-0.03156537,0.01846104,-0.01748174,0.01120397,0.03272253,-0.11082351,-0.02419353,0.09683333,0.00133138,0.0119903,-0.0060462,-0.04013518,0.03434659,-0.00326299,-0.01261787,-0.07906009,0.00614503,0.0322571,0.07532902,0.05770698,-0.01354905,0.01549071,-0.05422651,-0.02436842,-0.01460853,0.15885876,-0.03777833,-0.07984027,0.00131196,0.01645299,-0.01224151,-0.05477583,0.0422914,0.05200643,-0.03468803,0.04785537,0.03780821,-0.07196645,-0.11789847,-0.05040608,0.00150657,0.01491757,-0.03361907,-0.03050466,-0.01663243,0.01019235,-0.01772262,-0.05667841,0.02897546,0.01452922,0.0179453,0.02511497,0.02631714,0.00343351,0.00328929,-0.0097545,-0.04985023,-0.04434037,-0.03338782,-0.04730251,0.00888396,-0.01882935,-0.02159213,0.00080349,0.04863355,0.02590973,-0.01899154,-0.00515458,-0.03573766,-0.00975906,0.00661542,0.06448947,-0.04062546,-0.00229692,0.00704076,0.08131584,-0.09050764,0.01904554,0.02931147,0.04871925,0.02429526,0.01887912,-0.00384016,0.00151736,-0.07752702,-0.18079136,-0.03466598,-0.05598818,0.04378985,0.06699184,-0.099879,0.02800469,-0.07230241,0.04510875,0.03966635,0.05532636,-0.05784239,0.00294305,0.05589315,0.04863103,0.05873267,-0.06058969,-0.00378639,-0.01232717,0.05496136,-0.02949926,0.05376114,-0.0319434,-0.05356196,0.04069369,-0.0146177,0.13010041,0.01671738,-0.00484701,0.05542134,0.02551343,0.04862882,-0.05785766,-0.05544028,-0.00448067,0.04091335,0.01303405,-0.0300747,0.02053919,-0.00977695,-0.05729846,0.04676072,0.01099298,-0.12485587,-0.03586783,0.02330888,-0.02237749,0.00762738,-0.02217808,0.01713753,0.03504701,0.04785983,0.03094686,-0.0155543,0.02692094,-0.03812972,-0.07187049,-0.02759642,0.00714065,0.03729971,-0.02163168,-0.05216233,0.01789997,0.01332661,0.04902761,-0.01655015,-0.02524371,-0.04250465,-0.00444121,0.01094979,-0.00331191,0.11628066,-0.00412681,0.01392271,0.02031128,0.01560572,-0.00188347,-0.05128055,-0.0440731,-0.04204312,0.04928486,-0.06237336,0.03543694,0.05333378,0.03853379,-0.01057971,0.08368052,0.05871826,-0.03505209,-0.00061552,0.04576669,-0.00683389,-0.01979439,0.01775161,0.03254366,0.01136594,-0.29235497,0.00298815,-0.04310735,0.05594793,-0.04493504,-0.00596551,0.03935463,-0.04548772,0.01968821,-0.0371169,0.03126514,0.08153956,0.04293706,-0.05276207,-0.00744921,-0.01857757,0.02213091,-0.05090022,0.056499,-0.01129001,0.06171462,-0.0017619,0.25698254,-0.03841396,0.03439979,0.02028057,-0.00887258,-0.00400109,0.00383592,-0.01784113,-0.03667658,0.00918824,0.04161365,-0.02178622,-0.00341278,0.05088785,0.00338358,0.02906123,-0.04262545,-0.01740632,-0.04574561,0.0061136,-0.01214525,0.02706176,0.15353672,0.01981395,0.02639327,-0.08132268,0.04513644,0.10018764,-0.05791713,-0.07926712,-0.03021698,0.01609438,0.00724911,0.009202,-0.01557482,-0.02593746,-0.03454779,-0.00355905,0.0497788,0.01633577,0.04410993,0.04617006,0.01465852],"last_embed":{"hash":"1o4ixz6","tokens":326}}},"text":null,"length":0,"last_read":{"hash":"1o4ixz6","at":1753423556278},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{11}","lines":[87,98],"size":904,"outlinks":[{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":1},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":5},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":9},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.17953113,-0.04442248,-0.04864302,-0.01942002,-0.01945944,0.06250796,-0.04691477,-0.02231557,0.04654852,0.0068887,0.02172482,-0.00172523,0.05398929,0.03385794,-0.00727605,-0.00552566,-0.01531768,0.00483774,-0.03952078,-0.01454877,0.03991227,-0.06998776,-0.02038912,-0.081489,0.0328602,0.0354972,-0.0202347,-0.05407355,-0.05561129,-0.25746199,0.04298728,-0.00965863,0.02702784,-0.03794026,-0.06492007,-0.0198838,-0.01821691,0.00915774,-0.08544035,0.02463415,0.0245088,0.03625969,-0.00715614,-0.04855529,-0.00308314,-0.08152409,-0.02575007,-0.02045675,0.02539904,-0.00420235,-0.09251324,0.01980707,0.02832124,0.04278994,0.04532304,0.01647758,0.01846171,0.09063204,0.00180385,0.01130686,0.03632843,0.05177125,-0.17340796,0.06418942,-0.00221308,-0.00315661,-0.01004652,-0.0292409,0.02166118,0.06051571,0.00842233,0.02176262,-0.03331271,0.05686885,0.04895131,-0.05006858,-0.01777806,-0.01241321,0.00793791,0.0081335,-0.06749862,-0.03777667,-0.01188147,-0.0367384,-0.04026822,0.05850604,0.04460211,0.02608574,0.01145517,-0.05903472,-0.0207151,0.04388729,0.05228146,0.01824804,0.02834957,0.02241868,0.06064773,0.01453714,0.01386912,0.10759115,0.02031986,-0.01637128,-0.02199663,-0.01149913,0.05626374,-0.00078083,-0.00305776,-0.00361937,-0.04120793,0.03924762,0.03159992,-0.00608879,0.05997067,-0.04543067,-0.03999606,0.0010919,-0.01770769,0.05822239,-0.01958389,0.03538834,-0.05087597,0.00213749,0.03750094,0.0144979,-0.01100872,-0.00608978,-0.01787972,0.04617969,0.04354089,0.01263762,0.0631652,-0.01292283,-0.10655937,-0.0221003,-0.01505682,-0.00601884,-0.03182755,-0.04199735,0.00838617,-0.01434392,-0.02400951,0.00636164,0.03157512,-0.08390473,0.00540828,0.12348868,0.01274664,0.00103247,-0.00462146,-0.0266367,0.02816815,-0.00363183,-0.0350327,-0.10007571,0.00506947,0.01502867,0.10206363,0.07325425,-0.01018503,0.01564881,-0.00441811,-0.04132411,-0.03205048,0.14765789,-0.02527134,-0.07598516,0.00046396,0.0386832,-0.02147543,-0.07765356,0.03593826,0.03611296,-0.02051036,0.05161127,0.04960791,-0.06461939,-0.10430403,-0.01957627,-0.00523705,0.01025929,-0.03276212,-0.01885442,-0.02137245,0.00543619,0.02328895,-0.07839863,0.01753676,-0.00389192,0.02275949,0.0186616,0.0186785,-0.0043101,0.00536388,-0.01433809,-0.03166395,-0.02907733,-0.03825092,-0.02934071,0.02024365,-0.02029695,-0.0053143,0.00017835,0.03226737,0.00431957,-0.04063435,0.00220719,-0.06047348,-0.02435447,0.06384221,0.0586288,-0.01354437,-0.00800523,0.04707701,0.09073605,-0.08403642,-0.00501172,0.00152597,0.02571691,0.0371813,0.03606053,-0.03347721,0.03756702,-0.07800086,-0.19039513,-0.01980744,-0.0729363,0.01970674,0.04819175,-0.05763344,0.02351837,-0.04536369,0.02660844,0.0617615,0.06902806,-0.08215547,0.0035102,0.01222371,-0.01084994,0.04009381,-0.06699878,-0.01596243,-0.01855244,0.05154971,-0.00649791,0.04643947,-0.07272416,-0.05997772,0.01637711,-0.01263205,0.13487065,0.01642008,0.04320419,0.05048917,0.05707369,0.03712605,-0.00789955,-0.09940759,-0.02856345,0.08956254,-0.01766164,0.03179239,-0.00040194,-0.03023086,-0.08371244,0.01927569,0.01861083,-0.08947498,-0.0186634,-0.00649398,-0.00431828,0.01398626,-0.00659917,0.0210783,0.0108032,0.03115522,0.00446472,-0.01039385,0.00323499,-0.02897526,-0.04637302,-0.01698461,0.01814866,0.01254713,0.03037702,-0.05701668,0.03024365,0.01288022,0.03460586,-0.01704638,0.00233473,-0.04541008,0.02983568,-0.00910378,0.01215405,0.10812772,-0.00452177,0.0210788,0.02981866,0.03275978,0.00175711,-0.02269796,-0.03549034,-0.06245558,0.02636847,-0.03048135,0.05585843,0.05616762,0.06279948,0.01940572,0.07225416,0.07498112,-0.01019061,0.04571101,0.04970924,0.01658224,-0.03522071,0.0463238,0.04447634,0.02945017,-0.27026612,0.04070794,-0.00643812,0.03763917,-0.02778416,-0.02094937,0.03396978,-0.04088202,0.00287208,-0.06122877,0.02957487,0.0659406,0.04197673,-0.06080073,0.00263324,-0.03657983,0.00734967,-0.01617677,0.08604747,0.00131125,0.06489692,0.01570923,0.27359435,-0.00724737,0.00880062,0.01697992,0.02955274,0.01538856,0.0519351,0.00118,0.00842737,-0.01416083,0.05711981,-0.02987705,0.02540428,0.02013508,-0.03585865,0.02141256,-0.00236853,0.02125475,-0.05732923,0.02199107,-0.03746023,-0.00342727,0.13271122,0.03997449,0.0059785,-0.06648006,0.03508896,0.12070854,-0.05136325,-0.08769562,-0.01845475,-0.01153146,0.00499599,-0.00711925,-0.00796475,-0.06136828,-0.0252535,-0.01517483,0.05985669,-0.02241848,0.05565157,0.03774154,-0.01980875],"last_embed":{"hash":"q1dces","tokens":329}}},"text":null,"length":0,"last_read":{"hash":"q1dces","at":1753423556404},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>#{15}","lines":[103,111],"size":942,"outlinks":[{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13071166,-0.02622565,-0.05503066,-0.00939549,-0.04712151,0.06064789,0.01591827,0.0237175,0.08809374,0.00519032,0.00492133,-0.04133198,0.01339886,0.05212334,-0.00741049,0.00005367,-0.02858904,0.02502624,-0.03224911,-0.01726673,0.06981401,-0.07525683,-0.07599641,-0.1052404,0.06443284,0.04439994,-0.01720987,-0.04854389,-0.01725129,-0.22088388,0.00870879,-0.01341926,0.01717567,-0.07073862,-0.05986886,-0.03022485,-0.00546326,0.03421714,-0.03463583,0.03432659,0.0141176,0.03721037,-0.0173392,-0.02553917,0.03137388,-0.05504649,-0.01433915,-0.00708151,-0.00617579,-0.02211145,-0.08675223,0.00490383,0.01521112,0.01803135,0.04668539,-0.0216569,0.05601036,0.07742438,-0.01341941,0.04387387,-0.00402745,0.07259674,-0.20743488,0.05353635,0.02160736,0.01082275,-0.05831328,0.00043181,0.04691333,0.0538865,-0.02220802,0.03566862,-0.01941077,0.07728301,0.0340749,-0.04124795,0.01862792,-0.06739646,-0.01471539,0.02308081,-0.04169776,-0.01507092,-0.01546376,-0.04702187,-0.0132513,0.04767654,0.04087688,-0.02260077,0.02532563,-0.0385302,0.0243545,0.08621933,0.03151562,0.02825522,0.00781026,0.03228094,0.07455675,-0.02615895,0.0696499,0.10647871,0.04381299,0.00392616,-0.01843453,0.01691105,0.05872589,-0.02985832,-0.03862102,-0.05083145,-0.00675721,0.01302773,0.04387594,-0.00759951,0.0592903,-0.04538898,-0.05631838,-0.00674206,-0.00445986,0.02613563,0.01929556,-0.006399,-0.05622719,0.00055658,0.06113035,0.0454176,-0.00240803,-0.01854827,-0.02205377,0.08931921,0.02260582,0.01399316,0.07272775,-0.00828496,-0.0861007,-0.00759302,0.00785973,-0.0097632,0.00555018,-0.02079235,0.00043951,0.02702177,-0.01158853,-0.03141286,0.03157568,-0.08134059,-0.043484,0.11665051,-0.01881874,0.0149155,-0.00874238,-0.04817655,0.0264596,-0.01151729,-0.00300746,-0.06143194,-0.00313124,0.00451181,0.06577498,0.06033725,-0.02145359,0.02517877,-0.05831996,-0.08048682,-0.01495514,0.17999344,-0.02152243,-0.06146747,0.005375,0.04704482,0.00460757,-0.08015535,0.02379433,0.03751322,-0.03900208,0.04070902,0.06744317,-0.05226213,-0.07570334,-0.02726746,0.03981194,-0.00022826,-0.00891648,-0.04797015,-0.06642772,0.01494274,-0.01613313,-0.06067842,0.01270058,-0.00885222,0.01978937,0.02592522,0.00578097,-0.06122751,-0.03005539,-0.02959165,-0.04890485,-0.03488677,-0.05561231,-0.03495833,0.03474073,-0.02815821,0.00094056,-0.01242559,-0.01485378,0.00773827,-0.04957914,0.0236628,-0.0309534,-0.02112221,0.04580313,-0.00051309,0.02613878,-0.01043436,0.00953783,0.06164965,-0.0240649,0.04471326,0.0522271,0.05380649,0.00670064,0.02085575,0.00494594,0.01097562,-0.0830644,-0.17293829,-0.038581,-0.0277796,-0.00589923,0.03420967,0.00004086,0.01051094,-0.04422257,0.0652132,0.07648998,0.07334699,-0.06039771,-0.01360456,-0.00509836,-0.01457476,0.00494682,-0.09847092,-0.02299804,-0.0553611,0.07560813,-0.0141719,0.0118019,-0.06506687,-0.09839483,0.02772553,-0.0112833,0.12500195,0.01668787,-0.00521135,0.02016872,0.03671087,0.01804174,-0.05661137,-0.01850961,0.02188563,0.04647664,-0.03356387,0.01026942,-0.03851986,-0.02557458,-0.05558221,0.05374613,0.0276299,-0.04694932,-0.06444475,-0.01263824,0.00879309,0.04176901,0.0064694,0.03549931,0.01928585,0.02433321,0.07840647,-0.03406711,0.04008085,-0.03272218,-0.01421957,-0.00715403,-0.00630184,0.04275438,0.00612029,-0.041354,0.03513879,-0.0108382,0.04429345,-0.04583819,-0.00220872,-0.08987123,0.00222138,-0.01666493,-0.0094352,0.10964565,0.03655671,0.01122337,0.04248133,0.00919976,0.03852753,-0.07114048,-0.00383229,-0.00932575,-0.00191059,-0.07503176,0.04630138,0.07246987,0.06625958,-0.01274268,0.09053731,0.04118301,0.00888095,0.04178711,0.01843891,0.00687539,-0.03554042,0.04089644,0.02556013,0.09148746,-0.28667185,0.04876483,-0.0195359,0.05162793,0.00650883,-0.03048712,0.05525222,-0.05010956,-0.01263809,-0.05677637,0.07498603,0.04764937,0.05687832,-0.03122902,-0.03405894,-0.01965755,0.00869233,-0.04048747,0.06768169,0.02949863,0.02260533,0.04251125,0.22948949,-0.04486568,0.00916492,0.01014428,0.01473068,0.03574384,0.03158497,0.00468433,-0.00783562,0.00832264,0.07691935,-0.03906938,0.01586965,0.05365307,-0.00494011,-0.00744833,0.00156,-0.02959497,-0.01598722,-0.02630115,-0.01282274,0.04398064,0.13319188,0.03082097,-0.01407667,-0.11901086,0.05909755,0.08384161,-0.07071615,-0.06870358,-0.02227668,-0.0129795,-0.02675939,0.00124041,0.02709913,-0.02965984,-0.00961397,-0.01559329,0.03032401,-0.03121854,0.0493429,0.02330181,0.01011536],"last_embed":{"hash":"1lsvtjx","tokens":435}}},"text":null,"length":0,"last_read":{"hash":"1lsvtjx","at":1753423556526},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>","lines":[112,135],"size":2476,"outlinks":[{"title":"**Theory of Probability**","target":"https://saliu.com/theory-of-probability.html","line":3},{"title":"software","target":"https://saliu.com/infodown.html","line":4},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/Saliu2.htm","line":5},{"title":"Mathematics of _**Fundamental Formula of Gambling**_","target":"https://saliu.com/formula.htm","line":6},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html","line":7},{"title":"_**Software, Formulae Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/oddslotto.html","line":8},{"title":"_**Online Probability, Odds Calculator**_","target":"https://saliu.com/online_odds.html","line":9},{"title":"_**Software, Formulae to Calculate Lotto Odds, Hypergeometric Distribution Probability**_","target":"https://saliu.com/bbs/messages/266.html","line":10},{"title":"_**Standard Deviation, Gauss, Normal, Binomial, Distribution**_","target":"https://saliu.com/formula.html","line":11},{"title":"_**Lotto**_ **<u>Wonder Grid</u>**, _**Super Loto Strategy, System**_.","target":"https://saliu.com/bbs/messages/grid.html","line":13},{"title":"_**Randomness, Degree of Randomness, Degrees of Certainty**_.","target":"https://saliu.com/bbs/messages/683.html","line":14},{"title":"_**CoolRevGui**_: _Definitive File Reverser, Shuffler, Text Viewer Software_","target":"https://saliu.com/programming.html","line":16},{"title":"_**UPDOWN**_: _Reverse Order in Lottery Results, Text Files_","target":"https://saliu.com/bbs/messages/539.html","line":17},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":19},{"title":"Forums","target":"https://forums.saliu.com/","line":21},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":21},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":21},{"title":"Contents","target":"https://saliu.com/content/index.html","line":21},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":21},{"title":"Home","target":"https://saliu.com/index.htm","line":21},{"title":"Search","target":"https://saliu.com/Search.htm","line":21},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":21},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13088961,-0.03368754,-0.05520865,-0.04300619,-0.05017832,0.07888532,0.00231717,0.0266184,0.07203112,-0.00041784,0.01683743,-0.03283944,0.02906028,0.04465129,0.00942939,0.009926,-0.02776689,0.02805465,-0.03601646,-0.02297616,0.08026069,-0.05565304,-0.05659582,-0.09548066,0.06165449,0.03919247,-0.00726062,-0.05907791,-0.00905488,-0.21211961,0.02112149,-0.0069847,0.03131666,-0.05587655,-0.06244786,-0.0106308,-0.01163866,0.06419129,-0.04581405,0.03367031,0.02595603,0.02273805,-0.00022236,-0.02668981,0.03543098,-0.05719222,-0.01372647,-0.00844693,0.00848844,-0.0386414,-0.09025382,0.02220991,0.02064141,0.02118244,0.06940786,-0.02537438,0.0552392,0.07929097,-0.00902828,0.03124827,0.01216573,0.07378687,-0.20570828,0.06506607,0.00936649,0.01344246,-0.03181964,0.01558453,0.02308073,0.06382076,-0.00882198,0.02427137,-0.01837984,0.05820676,0.04917129,-0.05287968,-0.02469211,-0.05329486,-0.00966262,0.01796684,-0.05804972,-0.03806927,-0.02145348,-0.02677198,-0.03072503,0.06098461,0.04225349,0.00046456,0.0168827,-0.02844982,0.03290792,0.09133904,0.00853289,0.04506662,-0.00143491,0.03195978,0.05108908,-0.00309242,0.06116473,0.12610109,0.03278461,-0.00174179,-0.00933631,0.01257946,0.06143965,-0.02970467,-0.02822359,-0.05268691,-0.0138467,0.01413784,0.04499167,-0.0058277,0.0604915,-0.05869786,-0.04569827,-0.02334568,-0.00628054,0.03700697,0.02976472,-0.00454069,-0.05503483,0.0059065,0.04646518,0.02815009,-0.01773126,-0.00375103,-0.02723533,0.09963387,0.04345583,0.01276352,0.04517968,0.00728624,-0.12033448,-0.01039354,0.01478934,-0.0021378,-0.01179004,-0.03310268,-0.00578178,0.02653324,-0.00841359,-0.02967589,0.02477602,-0.09791128,-0.045991,0.08729463,-0.00927504,0.00993773,-0.02525032,-0.00970475,0.01836877,-0.00840481,-0.00938368,-0.06749921,0.00480256,0.01103098,0.089963,0.04750605,-0.01351542,0.01314813,-0.05306196,-0.0622033,-0.02307145,0.17092055,-0.00973392,-0.0701929,0.00794804,0.03590747,-0.00563952,-0.08579168,0.02992093,0.04023165,-0.03531197,0.03985576,0.05529891,-0.05877845,-0.09191211,-0.03634538,0.01298101,0.00461035,-0.04033927,-0.03476388,-0.02349954,0.01264735,-0.03936607,-0.08039317,0.02844615,0.00090434,0.00415504,0.01340179,-0.00502122,-0.03989662,-0.03296525,-0.02936252,-0.04282757,-0.0485997,-0.0513572,-0.04166406,0.06187105,-0.04148062,-0.02437963,-0.00516745,0.00605556,0.0287734,-0.03643896,0.01288546,-0.04784435,-0.02649283,0.06410478,0.01445576,0.00277215,0.00226456,0.01538592,0.05092189,-0.04579968,0.02911845,0.05570813,0.03164425,0.02124468,0.02172926,-0.00284746,0.01409073,-0.07632478,-0.17352901,-0.02367439,-0.04754502,0.0067962,0.02663174,-0.01386716,0.00381292,-0.05155694,0.0299631,0.0649289,0.07637396,-0.0563245,-0.01520579,0.01561441,0.00698079,-0.01804771,-0.0948506,-0.02614725,-0.03794116,0.07738203,-0.00370102,0.02382958,-0.05224109,-0.08711853,0.03424018,-0.01190056,0.1249755,0.03372772,-0.01787345,0.02597518,0.06409539,0.00728016,-0.06111528,-0.02748747,0.02040279,0.04878855,-0.03896995,0.00542272,-0.022666,-0.01963086,-0.05981558,0.04326697,0.00420595,-0.05100827,-0.0553455,-0.00282606,-0.02119004,0.02511065,0.01331565,0.033567,-0.01969501,0.03150617,0.06612755,-0.01508312,0.02875672,-0.05326873,-0.05141436,-0.02471515,-0.01942263,0.05671619,0.00348958,-0.04954903,0.06058726,0.01114222,0.06975569,-0.03384858,-0.01989587,-0.06716229,0.01348268,-0.01446196,-0.01717117,0.09853494,0.03646721,0.01942448,0.03778347,-0.00114097,0.01534192,-0.0547103,0.00718337,-0.02213911,-0.00852949,-0.10364347,0.03146075,0.06878185,0.06440782,0.01424044,0.0721534,0.05118294,0.01090389,0.06241703,0.04355809,0.00537799,-0.01134589,0.07685777,0.02906452,0.04737427,-0.26871586,0.0439045,-0.01395283,0.06194648,-0.01406422,-0.02501341,0.07547352,-0.01691071,-0.00405711,-0.0665925,0.07857526,0.03999784,0.06622779,-0.04996498,-0.01910691,-0.03998449,-0.00875842,-0.04811058,0.08687054,0.01674043,0.04453273,0.03490213,0.2505061,-0.03000947,0.00955974,0.00134835,0.02309085,0.01308612,0.03157584,0.00674357,0.00267347,0.00686561,0.05943482,-0.02875589,0.00189488,0.04397666,0.00513052,-0.00864562,-0.01009241,-0.03283463,-0.0427748,-0.00088793,-0.0342945,0.01645288,0.1349207,0.05796674,0.017315,-0.1187788,0.01803622,0.09472565,-0.04030879,-0.08562627,-0.01733064,-0.02921007,-0.02087598,-0.00036308,0.01243785,-0.03321776,-0.01103447,-0.01941735,0.03625314,-0.02488477,0.05555415,0.02075016,-0.00310622],"last_embed":{"hash":"kvqqhn","tokens":300}}},"text":null,"length":0,"last_read":{"hash":"kvqqhn","at":1753423556694},"key":"notes/saliu/Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>#{16}","lines":[130,135],"size":641,"outlinks":[{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
