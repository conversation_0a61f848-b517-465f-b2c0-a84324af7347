"smart_sources:Obsidian/Obsidian Local GPT Plugin/Obsidian AI Providers.md": {"path":"Obsidian/Obsidian Local GPT Plugin/Obsidian AI Providers.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00266217,-0.04496213,0.01953232,-0.05247493,-0.0098937,-0.00706239,-0.04177509,0.0665632,-0.01086511,-0.00568039,-0.00143917,-0.0336649,-0.01772571,0.05571241,0.0321555,0.02979379,-0.02106411,0.05314859,0.02304553,-0.02342708,0.05962322,-0.06888808,0.04397546,-0.01318124,-0.07478323,0.02124267,-0.00445033,-0.01021648,0.02678808,-0.18602915,0.0675417,-0.00307765,-0.08095433,0.00875014,-0.0439679,-0.00453274,-0.01934148,-0.01956384,-0.08154983,0.04262111,0.01970862,-0.01642635,-0.02014888,-0.07616516,0.01817501,0.01895524,-0.0222283,0.02598321,0.0207752,-0.02324968,0.07325857,0.01528348,0.01052086,-0.0315236,0.00343569,-0.01157349,0.07176331,0.09007802,0.01054122,0.0308107,0.02265428,0.04679741,-0.14948703,0.10499611,-0.00245076,0.06197138,-0.01628351,-0.00699822,0.00403757,-0.00892822,-0.0160209,0.05382608,-0.00075743,-0.00628185,0.01137479,-0.01723694,0.02652995,0.00240077,0.02058895,-0.01266104,0.03390168,0.03316538,-0.02873038,-0.03662312,0.00720594,-0.04912044,-0.00195722,0.01518539,0.04002251,-0.01040846,-0.06470408,-0.04801721,0.0225587,-0.02095534,0.04599796,0.04056752,0.04127595,-0.03144737,-0.1141068,0.12357334,-0.02940623,-0.03927547,-0.00887102,0.0025098,0.01477613,-0.05518381,-0.06458181,-0.00830491,-0.01398932,0.09406787,-0.00363927,0.03031086,0.00180984,-0.06570218,0.0087591,-0.00094508,-0.03271596,0.01942845,-0.01204556,0.01294264,-0.02100034,0.03316255,0.04841514,0.00146807,0.0470583,-0.0117497,0.03022482,0.01288426,-0.0007275,0.09917987,0.07409797,0.00500372,-0.04118326,-0.04789803,0.00846512,-0.02062444,0.00286001,-0.0109845,-0.03065808,0.0365843,-0.03582718,0.00818684,0.0225144,-0.05623189,0.07207381,0.08554193,-0.02159991,0.04119703,0.0220111,-0.09874359,-0.04388693,0.05889049,-0.10097655,-0.0126743,-0.0171105,-0.00682662,0.09463822,0.0574483,-0.09344555,0.07052165,-0.01368969,-0.06683004,0.03157267,0.07544855,0.03279925,-0.09697419,-0.09152324,-0.01804803,0.03131304,-0.02174988,-0.02071592,0.03220507,-0.03686834,0.03790842,0.06610502,-0.03254823,-0.11441194,-0.04651852,-0.04353889,0.06562052,-0.01068042,-0.0154541,0.03486943,-0.01311183,0.00442239,-0.07871051,0.02749639,-0.07969896,-0.0151724,-0.07206563,-0.06666937,0.01404218,-0.07174462,0.01388062,0.06213931,0.02150582,-0.02599377,-0.02952118,-0.00493376,0.02262065,0.06497202,-0.03046841,0.0181605,0.016903,-0.10198684,0.02389698,-0.01811542,-0.04345832,0.0069824,0.00764461,-0.02791613,-0.01730202,0.07821261,0.00975587,-0.04913266,0.04498335,0.00221148,0.02033782,0.03000199,0.07652493,0.03109365,0.02622776,-0.03730986,-0.19926202,-0.03831417,0.00739278,0.01555401,0.01529659,-0.12826075,0.08246823,0.02056033,-0.01866072,0.13485959,0.10522395,0.01634643,0.03782964,0.09697334,-0.03432928,0.03870842,0.02528196,0.05269731,-0.02060395,0.0077554,0.01074345,0.03420176,-0.01416586,-0.11816329,0.00526811,0.00120581,0.19549982,0.01989043,0.05143591,-0.02842953,-0.02001549,0.02727368,-0.03234372,-0.18290351,0.05184191,0.05035388,0.03210979,0.0720413,0.0121372,-0.01092481,-0.05227703,0.11649895,0.005693,-0.08236763,0.02090266,-0.02811215,-0.0033895,0.02347721,-0.03250973,-0.00958303,-0.01444416,0.00749665,0.01365754,0.03528507,-0.00489727,-0.08087417,-0.05459858,-0.0052695,0.00071564,0.06847144,-0.06558671,-0.03301661,-0.00476672,-0.05751419,0.06523094,0.02658475,0.04728344,-0.01411389,0.0919711,-0.00269977,-0.00448429,0.10274726,-0.05040788,0.07061548,-0.00627155,-0.07259308,0.02268058,-0.00581403,-0.00782055,0.05429427,0.02288888,-0.01405977,0.03605279,0.02599044,-0.01602682,0.0217912,-0.00811587,0.05430134,0.04237235,0.01810644,0.00269897,0.04375679,-0.06063408,-0.02700074,0.0534773,0.01249792,-0.24922524,0.06944561,0.08536586,-0.01118875,-0.05029954,0.02527755,0.00612082,-0.04249263,0.00885262,-0.03089109,-0.02530762,0.0206688,0.00805828,-0.01405644,-0.00778624,0.03754551,0.01586424,-0.018351,-0.02995807,-0.03044269,-0.00392363,0.05751994,0.19667336,-0.00204374,-0.02151377,-0.02458603,-0.02771833,0.02422429,0.04808399,-0.05864961,-0.02546292,0.02319873,0.05440694,-0.04369162,0.06187962,0.02227058,-0.0248738,-0.02532968,0.0108097,0.0200005,0.00102496,0.04818894,-0.03204164,0.03702767,0.00902098,-0.04288993,-0.06638458,0.03058606,-0.03598168,0.0230816,-0.0292875,0.00480899,-0.01976929,-0.01062874,0.00614509,0.01476419,-0.02822269,-0.03240237,-0.05119311,-0.01217128,0.01024614,-0.07117233,0.0181483,0.05108786,-0.04756736],"last_embed":{"hash":"452efce0af59c39164259bcac6da03563e7e9e18197fa07c40ba7a1fb866d44b","tokens":445}}},"last_read":{"hash":"452efce0af59c39164259bcac6da03563e7e9e18197fa07c40ba7a1fb866d44b","at":1745995237274},"class_name":"SmartSource2","outlinks":[{"title":"image","target":"https://github.com/user-attachments/assets/09b6313d-726c-440b-9201-1b2f2e839fa7","line":14},{"title":"Local GPT","target":"https://github.com/pfrankov/obsidian-local-gpt","line":18},{"title":"https://obsidian.md/plugins?id=ai-providers","target":"https://obsidian.md/plugins?id=ai-providers","line":41},{"title":"BRAT","target":"https://obsidian.md/plugins?id=obsidian42-brat","line":45},{"title":"Ollama","target":"https://ollama.com/","line":51},{"title":"from the library","target":"https://ollama.com/library","line":52},{"title":"check the docs","target":"https://github.com/ollama/ollama/blob/main/docs/faq.md#how-do-i-configure-ollama-server","line":59},{"title":"API keys page","target":"https://platform.openai.com/api-keys","line":65},{"title":"Open WebUI","target":"https://docs.openwebui.com/tutorials/integrations/continue-dev/","line":72},{"title":"llama.cpp","target":"https://github.com/ggerganov/llama.cpp","line":73},{"title":"llama-cpp-python","target":"https://github.com/abetlen/llama-cpp-python#openai-compatible-web-server","line":74},{"title":"LocalAI","target":"https://localai.io/model-compatibility/llama-cpp/#setup","line":75},{"title":"Text generation web UI","target":"https://github.com/pfrankov/obsidian-local-gpt/discussions/8","line":76},{"title":"LM Studio","target":"https://lmstudio.ai/","line":77},{"title":"API keys page","target":"https://openrouter.ai/settings/keys","line":84},{"title":"API keys page","target":"https://aistudio.google.com/apikey","line":91},{"title":"API keys page","target":"https://groq.com/docs/api-reference/introduction","line":104},{"title":"Docs: How to integrate AI Providers in your plugin.","target":"app://obsidian.md/packages/sdk/README.md","line":109},{"title":"Local GPT","target":"https://github.com/pfrankov/obsidian-local-gpt","line":127},{"title":"Colored Tags","target":"https://github.com/pfrankov/obsidian-colored-tags","line":128}],"blocks":{"#Obsidian AI Providers":[1,128],"#Obsidian AI Providers#{1}":[3,7],"#Obsidian AI Providers#{2}":[8,8],"#Obsidian AI Providers#{3}":[9,9],"#Obsidian AI Providers#{4}":[10,11],"#Obsidian AI Providers#{5}":[12,15],"#Obsidian AI Providers#Required by plugins":[16,19],"#Obsidian AI Providers#Required by plugins#{1}":[18,19],"#Obsidian AI Providers#Supported providers":[20,29],"#Obsidian AI Providers#Supported providers#{1}":[22,22],"#Obsidian AI Providers#Supported providers#{2}":[23,23],"#Obsidian AI Providers#Supported providers#{3}":[24,24],"#Obsidian AI Providers#Supported providers#{4}":[25,25],"#Obsidian AI Providers#Supported providers#{5}":[26,26],"#Obsidian AI Providers#Supported providers#{6}":[27,27],"#Obsidian AI Providers#Supported providers#{7}":[28,29],"#Obsidian AI Providers#Features":[30,36],"#Obsidian AI Providers#Features#{1}":[32,32],"#Obsidian AI Providers#Features#{2}":[33,33],"#Obsidian AI Providers#Features#{3}":[34,34],"#Obsidian AI Providers#Features#{4}":[35,36],"#Obsidian AI Providers#Installation":[37,46],"#Obsidian AI Providers#Installation#Obsidian plugin store (recommended)":[39,42],"#Obsidian AI Providers#Installation#Obsidian plugin store (recommended)#{1}":[41,42],"#Obsidian AI Providers#Installation#BRAT":[43,46],"#Obsidian AI Providers#Installation#BRAT#{1}":[45,46],"#Obsidian AI Providers#Create AI provider":[47,106],"#Obsidian AI Providers#Create AI provider#Ollama":[49,60],"#Obsidian AI Providers#Create AI provider#Ollama#{1}":[51,51],"#Obsidian AI Providers#Create AI provider#Ollama#{2}":[52,52],"#Obsidian AI Providers#Create AI provider#Ollama#{3}":[53,53],"#Obsidian AI Providers#Create AI provider#Ollama#{4}":[54,55],"#Obsidian AI Providers#Create AI provider#Ollama#{5}":[56,57],"#Obsidian AI Providers#Create AI provider#Ollama#{6}":[58,58],"#Obsidian AI Providers#Create AI provider#Ollama#{7}":[59,60],"#Obsidian AI Providers#Create AI provider#OpenAI":[61,67],"#Obsidian AI Providers#Create AI provider#OpenAI#{1}":[63,63],"#Obsidian AI Providers#Create AI provider#OpenAI#{2}":[64,64],"#Obsidian AI Providers#Create AI provider#OpenAI#{3}":[65,65],"#Obsidian AI Providers#Create AI provider#OpenAI#{4}":[66,67],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server":[68,79],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{1}":[70,71],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{2}":[72,72],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{3}":[73,73],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{4}":[74,74],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{5}":[75,75],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{6}":[76,76],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{7}":[77,77],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{8}":[78,79],"#Obsidian AI Providers#Create AI provider#OpenRouter":[80,86],"#Obsidian AI Providers#Create AI provider#OpenRouter#{1}":[82,82],"#Obsidian AI Providers#Create AI provider#OpenRouter#{2}":[83,83],"#Obsidian AI Providers#Create AI provider#OpenRouter#{3}":[84,84],"#Obsidian AI Providers#Create AI provider#OpenRouter#{4}":[85,86],"#Obsidian AI Providers#Create AI provider#Google Gemini":[87,93],"#Obsidian AI Providers#Create AI provider#Google Gemini#{1}":[89,89],"#Obsidian AI Providers#Create AI provider#Google Gemini#{2}":[90,90],"#Obsidian AI Providers#Create AI provider#Google Gemini#{3}":[91,91],"#Obsidian AI Providers#Create AI provider#Google Gemini#{4}":[92,93],"#Obsidian AI Providers#Create AI provider#LM Studio":[94,99],"#Obsidian AI Providers#Create AI provider#LM Studio#{1}":[96,96],"#Obsidian AI Providers#Create AI provider#LM Studio#{2}":[97,97],"#Obsidian AI Providers#Create AI provider#LM Studio#{3}":[98,99],"#Obsidian AI Providers#Create AI provider#Groq":[100,106],"#Obsidian AI Providers#Create AI provider#Groq#{1}":[102,102],"#Obsidian AI Providers#Create AI provider#Groq#{2}":[103,103],"#Obsidian AI Providers#Create AI provider#Groq#{3}":[104,104],"#Obsidian AI Providers#Create AI provider#Groq#{4}":[105,106],"#Obsidian AI Providers#For plugin developers":[107,110],"#Obsidian AI Providers#For plugin developers#{1}":[109,110],"#Obsidian AI Providers#Roadmap":[111,124],"#Obsidian AI Providers#Roadmap#{1}":[113,113],"#Obsidian AI Providers#Roadmap#{2}":[114,114],"#Obsidian AI Providers#Roadmap#{3}":[115,115],"#Obsidian AI Providers#Roadmap#{4}":[116,116],"#Obsidian AI Providers#Roadmap#{5}":[117,117],"#Obsidian AI Providers#Roadmap#{6}":[118,118],"#Obsidian AI Providers#Roadmap#{7}":[119,119],"#Obsidian AI Providers#Roadmap#{8}":[120,120],"#Obsidian AI Providers#Roadmap#{9}":[121,121],"#Obsidian AI Providers#Roadmap#{10}":[122,122],"#Obsidian AI Providers#Roadmap#{11}":[123,124],"#Obsidian AI Providers#My other Obsidian plugins":[125,128],"#Obsidian AI Providers#My other Obsidian plugins#{1}":[127,127],"#Obsidian AI Providers#My other Obsidian plugins#{2}":[128,128]},"last_import":{"mtime":1741745830259,"size":5024,"at":1741911372551,"hash":"452efce0af59c39164259bcac6da03563e7e9e18197fa07c40ba7a1fb866d44b"},"key":"Obsidian/Obsidian Local GPT Plugin/Obsidian AI Providers.md"},