"smart_sources:利用大型語言模型提升軟體開發效率的策略 - Calpa 的煉金工房.md": {"path":"利用大型語言模型提升軟體開發效率的策略 - Calpa 的煉金工房.md","last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"3f1502431069f28351120ba7e2247ec6900d80e9586d144ae594295288899e6c"}}},"last_read":{"hash":"3f1502431069f28351120ba7e2247ec6900d80e9586d144ae594295288899e6c","at":1750403577084},"class_name":"SmartSource","last_import":{"mtime":1750383457741,"size":15295,"at":1750384454489,"hash":"3f1502431069f28351120ba7e2247ec6900d80e9586d144ae594295288899e6c"},"blocks":{"#":[1,286]},"outlinks":[],"key":"利用大型語言模型提升軟體開發效率的策略 - Calpa 的煉金工房.md"},
"smart_blocks:利用大型語言模型提升軟體開發效率的策略 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"3f1502431069f28351120ba7e2247ec6900d80e9586d144ae594295288899e6c"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"3f1502431069f28351120ba7e2247ec6900d80e9586d144ae594295288899e6c","at":1750403577128},"key":"利用大型語言模型提升軟體開發效率的策略 - Calpa 的煉金工房.md#","lines":[1,286],"size":6620,"outlinks":[],"class_name":"SmartBlock"},"smart_blocks:利用大型語言模型提升軟體開發效率的策略 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"3f1502431069f28351120ba7e2247ec6900d80e9586d144ae594295288899e6c"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"3f1502431069f28351120ba7e2247ec6900d80e9586d144ae594295288899e6c","at":1750403577128},"key":"利用大型語言模型提升軟體開發效率的策略 - Calpa 的煉金工房.md#","lines":[1,286],"size":6620,"outlinks":[],"class_name":"SmartBlock"},
