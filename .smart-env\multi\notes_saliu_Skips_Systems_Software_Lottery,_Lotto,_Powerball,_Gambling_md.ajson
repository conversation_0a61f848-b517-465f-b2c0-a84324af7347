
"smart_sources:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md": {"path":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"15ntk06","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363612462,"size":25789,"at":1753423416500,"hash":"15ntk06"},"blocks":{"#---frontmatter---":[1,6],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling":[8,230],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#{1}":[10,33],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>":[34,84],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>#{1}":[36,84],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>":[85,120],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>#{1}":[87,120],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>":[121,193],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{1}":[123,132],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{2}":[133,133],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{3}":[134,135],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{4}":[136,157],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{5}":[158,158],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{6}":[159,160],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{7}":[161,166],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{8}":[167,171],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{9}":[172,187],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{10}":[188,189],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{11}":[190,193],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>":[194,230],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{1}":[196,197],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{2}":[198,198],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{3}":[199,199],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{4}":[200,200],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{5}":[201,201],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{6}":[202,202],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{7}":[203,203],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{8}":[204,204],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{9}":[205,205],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{10}":[206,206],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{11}":[207,207],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{12}":[208,208],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{13}":[209,209],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{14}":[210,210],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{15}":[211,211],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{16}":[212,212],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{17}":[213,213],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{18}":[214,214],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{19}":[215,215],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{20}":[216,216],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{21}":[217,217],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{22}":[218,218],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{23}":[219,219],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{24}":[220,220],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{25}":[221,221],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{26}":[222,222],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{27}":[223,224],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{28}":[225,230]},"outlinks":[{"title":"_**lotto system for Powerball**_","target":"https://saliu.com/powerball-systems.html","line":22},{"title":"_**Home of Notepad++**_","target":"https://notepad-plus-plus.org/","line":26},{"title":"The main software function creates positional skip systems for lotto, lottery, Mega Millions.","target":"https://saliu.com/HLINE.gif","line":32},{"title":"The SKIP systems are just a part of the best lottery software in the world - ever.","target":"https://saliu.com/ScreenImgs/skip-systems-powerball.gif","line":40},{"title":"**_lottery strategy_**","target":"https://saliu.com/LottoWin.htm","line":49},{"title":"Nay-Sayers try to detract the viability of lottery systems software for skips, misses of N drawings.","target":"https://saliu.com/HLINE.gif","line":83},{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/skip-systems-euromillions.gif","line":91},{"title":"There are important facts to consider in using lottery software for skip strategies and systems.","target":"https://saliu.com/HLINE.gif","line":119},{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/lotto-skips-win.gif","line":127},{"title":"The combinations generated by a lottery skip system must be purged to reduce tickets to play.","target":"https://saliu.com/HLINE.gif","line":136},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":148},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":159},{"title":"Lotto skips strategies are efficient tools in conjunction with lottery filters in Ion Saliu apps.","target":"https://saliu.com/HLINE.gif","line":161},{"title":"_**lottery, lotto strategy software**_","target":"https://saliu.com/LottoWin.htm#Methods","line":165},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":188},{"title":"Run software to create lottery systems based on the mathematics of skips: Gaps between lotto wins.","target":"https://saliu.com/HLINE.gif","line":190},{"title":"\n\n## <u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>\n\n","target":"https://saliu.com/content/lottery.html","line":192},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":198},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":200},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":202},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":204},{"title":"_**Lottery Mathematics, Lotto Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":205},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":206},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":207},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":208},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":209},{"title":"_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":210},{"title":"_**Gail Howard's <u>Skip</u> Lottery Systems, Lotto <u>Wheels</u>**_","target":"https://saliu.com/bbs/messages/278.html","line":211},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":212},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":213},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":214},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":215},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":216},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":217},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":218},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":219},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":220},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":221},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":222},{"title":"**Lottery Software, Lotto Applications**","target":"https://saliu.com/infodown.html","line":223},{"title":"Lottery developer Ion Saliu created the best software to create lotto systems based on number skips.","target":"https://saliu.com/HLINE.gif","line":225},{"title":"Forums","target":"https://forums.saliu.com/","line":227},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":227},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":227},{"title":"Contents","target":"https://saliu.com/content/index.html","line":227},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":227},{"title":"Home","target":"https://saliu.com/index.htm","line":227},{"title":"Search","target":"https://saliu.com/Search.htm","line":227},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":227},{"title":"Thanks for reading software for systems, lottery strategies based on skips after lotto jackpot hits.","target":"https://saliu.com/HLINE.gif","line":229}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["skip","gap","miss","hit","software","strategy","system","lotto","pick","lottery","Powerball","Mega Millions","Euromillions","roulette","sports","football","horse racing","numbers"],"source":"https://saliu.com/skip-strategy.html","author":null}},
"smart_sources:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md": {"path":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10388287,-0.01954364,-0.01868225,-0.01834186,-0.03689129,0.05383861,-0.00064595,-0.01143584,0.10293917,0.02301167,0.02924167,-0.01831146,0.0689667,-0.0141426,-0.03073676,-0.05221719,-0.02066393,-0.01176212,-0.03755244,0.00881354,0.06698205,-0.05738921,-0.03611731,-0.12050921,0.03812578,0.04449197,-0.02827247,-0.03696078,-0.04742495,-0.22883494,0.01744419,-0.0133678,-0.00831961,-0.08817663,-0.09365056,-0.06473626,-0.02424654,0.03381798,-0.04410297,0.04050896,-0.02483391,0.00121743,-0.02059344,-0.05321846,-0.00208001,-0.01054796,-0.02863871,0.01901293,0.05722858,0.00720286,-0.05385923,-0.00936767,0.03323697,0.00172187,0.07930654,-0.00115863,0.04123174,0.07875565,-0.00145252,0.01818792,0.04566933,0.03720054,-0.18376707,0.04256816,0.00240232,0.0346016,-0.0094692,-0.03212821,0.04619813,0.09127973,-0.00850611,0.01516604,-0.03692945,0.10002625,0.03093053,0.00657542,-0.01740102,0.00657521,-0.03764435,0.04088321,-0.01544766,-0.03933103,-0.02529404,-0.04114966,0.00470058,0.04366048,0.00115296,0.03708355,0.05048357,-0.06134212,0.03999025,-0.03097993,0.03646807,0.01911165,0.00776925,0.00989681,0.08839364,-0.05367443,0.00923418,0.09069658,0.03872573,0.00046411,-0.00708669,0.0505825,0.01743429,-0.0623015,-0.05041976,-0.06090773,-0.06267517,-0.00471417,0.03237442,0.01314487,0.07851893,-0.01830493,-0.02613189,0.01493003,0.00750706,0.01816686,0.06354664,-0.00027427,-0.0460113,0.02092399,-0.00808741,0.02824182,0.00161034,0.03793907,0.02596777,0.06392085,0.01366434,0.03766454,0.03812412,-0.04786821,-0.116841,-0.05779459,0.02547922,0.01282114,-0.0140675,0.02956158,-0.0147516,-0.01223948,0.00375148,-0.02699212,0.05095021,-0.10580084,-0.02668193,0.06694399,0.02604314,0.00264061,0.0091212,-0.06140514,-0.00903839,-0.01989322,-0.06119768,-0.02858399,-0.03518783,0.03966001,0.07352885,0.0779813,-0.05601051,-0.01405706,-0.04531796,-0.02427988,-0.05185996,0.09133758,0.04785346,-0.07455076,0.00423513,0.01993953,-0.04272538,-0.08408631,-0.04386346,0.02959288,-0.09883342,-0.00096442,0.09504554,-0.01324239,-0.06767953,-0.07959665,0.01403828,-0.01860259,0.06561215,-0.01635148,-0.04084471,-0.01791635,-0.03544216,-0.12067427,0.00686074,-0.07263509,-0.01223775,0.0808828,-0.01250493,-0.00695276,-0.02184812,-0.01000845,-0.02348055,-0.0347126,-0.01574838,-0.02392357,0.06541324,-0.00717684,0.02167021,-0.01730097,0.04292503,-0.00521498,0.01042244,0.04707086,0.01683439,-0.03175944,0.07571252,0.01928481,-0.02562295,-0.06501712,0.04465586,0.03649008,-0.02849332,0.05656232,-0.02058174,0.01763603,-0.01286879,-0.01481035,0.00560272,0.04865832,-0.02047568,-0.19917838,-0.03739551,-0.03565253,0.04218962,0.02873188,-0.02718143,0.04786185,-0.02994968,0.02867678,0.10178728,0.06704235,-0.05997905,-0.05301802,0.04389718,-0.02164547,0.05799272,-0.10796881,-0.05657778,-0.06172079,0.06006166,-0.00462344,0.02136913,-0.01940299,-0.05484629,0.01567622,-0.05402688,0.15190588,0.04239816,-0.01380858,-0.03206855,0.05627574,0.02602401,-0.00976931,0.00034734,-0.0122787,0.04112471,0.00634697,0.01481033,-0.00409417,-0.01971947,-0.09402455,0.05182194,-0.0153454,-0.08738732,-0.02266544,0.04350054,0.00413057,-0.04592029,0.01003324,0.05859424,0.06283059,-0.04929503,0.01304153,0.01639354,0.05220178,-0.01401357,-0.06501331,0.03272483,-0.01522641,0.05326954,0.00129773,-0.06618086,-0.00971914,-0.03471624,0.02376999,0.00630135,0.01372123,0.00196311,0.03423991,-0.01745043,0.01555694,0.11008644,0.04237993,-0.00416033,-0.01511435,0.04554905,0.05303866,-0.02962261,-0.00958983,0.02674975,0.00834253,-0.02376959,0.03527548,0.07551842,0.06868193,-0.00761935,0.09085636,0.02015949,0.03194686,-0.02324994,-0.0373686,0.02717588,-0.04726768,0.01356632,0.01000929,0.0160694,-0.25484499,0.05315599,-0.01346202,0.04568506,0.02566675,-0.03663458,-0.00033385,-0.00917368,0.01040033,-0.02508599,0.05188132,0.0503543,0.05059849,-0.05716792,-0.03493028,0.02036206,-0.00204768,0.01061604,0.06095921,-0.01810463,0.05256369,0.05511101,0.21397778,-0.02594907,0.01392952,0.0125949,0.00396795,0.00489286,0.04454656,0.04429715,-0.03024817,0.04695288,0.11243369,-0.00630894,-0.04861113,0.00992931,-0.0385672,0.0762905,0.00326956,-0.03567533,-0.04932877,0.01257748,0.00920497,0.01752703,0.09372796,0.01584429,-0.01278164,-0.0971178,0.05461942,0.08469654,-0.08914565,-0.03561442,-0.04265683,-0.02158695,0.05807341,0.04469926,-0.00985594,-0.00397792,-0.02190722,0.01468733,-0.00914136,-0.02008156,0.07719631,0.02095288,0.02599766],"last_embed":{"hash":"15ntk06","tokens":472}}},"last_read":{"hash":"15ntk06","at":1753423608720},"class_name":"SmartSource","last_import":{"mtime":1753363612462,"size":25789,"at":1753423416500,"hash":"15ntk06"},"blocks":{"#---frontmatter---":[1,6],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling":[8,230],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#{1}":[10,33],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>":[34,84],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>#{1}":[36,84],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>":[85,120],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>#{1}":[87,120],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>":[121,193],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{1}":[123,132],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{2}":[133,133],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{3}":[134,135],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{4}":[136,157],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{5}":[158,158],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{6}":[159,160],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{7}":[161,166],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{8}":[167,171],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{9}":[172,187],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{10}":[188,189],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{11}":[190,193],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>":[194,230],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{1}":[196,197],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{2}":[198,198],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{3}":[199,199],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{4}":[200,200],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{5}":[201,201],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{6}":[202,202],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{7}":[203,203],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{8}":[204,204],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{9}":[205,205],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{10}":[206,206],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{11}":[207,207],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{12}":[208,208],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{13}":[209,209],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{14}":[210,210],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{15}":[211,211],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{16}":[212,212],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{17}":[213,213],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{18}":[214,214],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{19}":[215,215],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{20}":[216,216],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{21}":[217,217],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{22}":[218,218],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{23}":[219,219],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{24}":[220,220],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{25}":[221,221],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{26}":[222,222],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{27}":[223,224],"#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{28}":[225,230]},"outlinks":[{"title":"_**lotto system for Powerball**_","target":"https://saliu.com/powerball-systems.html","line":22},{"title":"_**Home of Notepad++**_","target":"https://notepad-plus-plus.org/","line":26},{"title":"The main software function creates positional skip systems for lotto, lottery, Mega Millions.","target":"https://saliu.com/HLINE.gif","line":32},{"title":"The SKIP systems are just a part of the best lottery software in the world - ever.","target":"https://saliu.com/ScreenImgs/skip-systems-powerball.gif","line":40},{"title":"**_lottery strategy_**","target":"https://saliu.com/LottoWin.htm","line":49},{"title":"Nay-Sayers try to detract the viability of lottery systems software for skips, misses of N drawings.","target":"https://saliu.com/HLINE.gif","line":83},{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/skip-systems-euromillions.gif","line":91},{"title":"There are important facts to consider in using lottery software for skip strategies and systems.","target":"https://saliu.com/HLINE.gif","line":119},{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/lotto-skips-win.gif","line":127},{"title":"The combinations generated by a lottery skip system must be purged to reduce tickets to play.","target":"https://saliu.com/HLINE.gif","line":136},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":148},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":159},{"title":"Lotto skips strategies are efficient tools in conjunction with lottery filters in Ion Saliu apps.","target":"https://saliu.com/HLINE.gif","line":161},{"title":"_**lottery, lotto strategy software**_","target":"https://saliu.com/LottoWin.htm#Methods","line":165},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":188},{"title":"Run software to create lottery systems based on the mathematics of skips: Gaps between lotto wins.","target":"https://saliu.com/HLINE.gif","line":190},{"title":"\n\n## <u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>\n\n","target":"https://saliu.com/content/lottery.html","line":192},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":198},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":200},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":202},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":204},{"title":"_**Lottery Mathematics, Lotto Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":205},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":206},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":207},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":208},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":209},{"title":"_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":210},{"title":"_**Gail Howard's <u>Skip</u> Lottery Systems, Lotto <u>Wheels</u>**_","target":"https://saliu.com/bbs/messages/278.html","line":211},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":212},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":213},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":214},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":215},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":216},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":217},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":218},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":219},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":220},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":221},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":222},{"title":"**Lottery Software, Lotto Applications**","target":"https://saliu.com/infodown.html","line":223},{"title":"Lottery developer Ion Saliu created the best software to create lotto systems based on number skips.","target":"https://saliu.com/HLINE.gif","line":225},{"title":"Forums","target":"https://forums.saliu.com/","line":227},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":227},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":227},{"title":"Contents","target":"https://saliu.com/content/index.html","line":227},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":227},{"title":"Home","target":"https://saliu.com/index.htm","line":227},{"title":"Search","target":"https://saliu.com/Search.htm","line":227},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":227},{"title":"Thanks for reading software for systems, lottery strategies based on skips after lotto jackpot hits.","target":"https://saliu.com/HLINE.gif","line":229}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["skip","gap","miss","hit","software","strategy","system","lotto","pick","lottery","Powerball","Mega Millions","Euromillions","roulette","sports","football","horse racing","numbers"],"source":"https://saliu.com/skip-strategy.html","author":null}},"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10640414,-0.02820452,-0.00483993,-0.01966735,-0.00003976,0.05229516,-0.00573112,0.01821331,0.0765288,0.01787329,0.02092002,-0.03082231,0.05383689,-0.00211041,-0.00970587,-0.03733281,0.01202528,-0.03327362,-0.02409121,0.01737365,0.05026337,-0.04217552,-0.02541715,-0.10265918,0.03522488,0.01596935,-0.02286744,-0.0324787,-0.05217573,-0.1728314,-0.00102097,0.0037015,-0.02263196,-0.08248838,-0.07178283,-0.06554773,-0.01634467,0.0261048,-0.02132561,0.05455855,-0.01915725,0.01793749,-0.03998363,-0.03780166,0.00372976,-0.01052847,-0.00811193,-0.00263486,0.0593799,0.00624079,-0.05982672,0.00378159,0.00002534,0.02712189,0.09062122,-0.00543421,0.04186388,0.05483829,0.01365861,0.03270887,0.03564572,0.03228455,-0.19347204,0.04937397,0.0063164,0.04052863,0.01178823,-0.01725529,0.01022541,0.049618,0.01766162,0.038839,-0.02141935,0.1011403,0.02339809,0.01773125,-0.01663511,-0.01395207,-0.04753755,0.02331522,-0.0052687,-0.04053966,-0.05783992,-0.06113528,-0.0115088,0.03652548,0.01571448,0.06052554,0.04386525,-0.06493092,0.03618571,-0.03905297,0.08739904,0.04200978,-0.03561212,0.01051573,0.06808582,-0.04770842,-0.02433282,0.10203291,0.00176353,0.02389836,-0.0073349,0.04518791,0.03315936,-0.03118161,-0.02221251,-0.03341238,-0.04601821,0.01109741,0.02901702,0.02714838,0.0915495,-0.03132712,-0.01402865,0.00304534,0.01335315,0.00478226,0.04857539,0.01925048,-0.08046135,0.02081105,0.0084184,0.03395817,0.00015612,0.04195143,0.03533455,0.05083124,0.00939565,0.03269946,-0.00569814,-0.03279071,-0.10731667,-0.06167602,0.03302511,-0.01182653,-0.01990629,-0.01155874,0.00003051,-0.02193743,-0.01999036,-0.06146184,0.04923946,-0.13686469,-0.03548872,0.03853988,0.03206896,-0.01357989,0.00615744,-0.03562009,-0.02930614,-0.01563411,-0.01250222,-0.0226357,-0.03552438,0.03106727,0.1014955,0.09680689,-0.03961274,-0.00987442,-0.01790714,-0.03427498,-0.05336113,0.12695317,0.0334424,-0.14181139,-0.03380153,0.01398451,-0.04082721,-0.06974547,-0.05382178,0.0223148,-0.09037773,0.0099336,0.1066511,-0.01726597,-0.0082572,-0.06818233,-0.03324355,-0.01316556,0.03008256,-0.02306417,-0.04041596,-0.03077338,-0.04841951,-0.11620365,-0.01062647,-0.070504,0.02462503,0.05647913,-0.04746329,-0.02670466,-0.03499893,-0.01160468,-0.01472845,-0.02312571,-0.01001463,-0.0414139,0.09006757,-0.02951884,-0.01633763,-0.00363946,0.02357285,0.0144111,0.01239181,0.04489528,-0.00736074,-0.03179158,0.05950234,0.00328941,-0.00258911,-0.04249619,0.08427989,0.04457219,-0.05068349,0.04905231,-0.03104113,-0.00472128,0.0036435,-0.01640663,0.00287342,0.07261143,-0.05396095,-0.21065699,-0.007761,-0.06736327,0.02841395,-0.00997963,-0.03384914,0.04278765,-0.02185059,0.02679329,0.12634854,0.06722959,-0.0660617,-0.05079433,0.03070876,-0.01069966,0.04306896,-0.08367056,-0.07689988,-0.0399729,0.05578898,0.00831447,0.02005179,-0.02963837,-0.06748421,0.02815663,-0.05781614,0.15009852,0.10035121,-0.03362738,-0.04278795,0.09251765,0.03468368,0.00463226,-0.05673846,-0.02422893,0.02297663,0.01702848,0.02328404,-0.0207598,-0.03409766,-0.09280472,0.07283279,-0.02853194,-0.09931497,0.00244863,0.01944256,0.02139608,-0.0382053,0.00607891,0.07221697,0.06603131,-0.04447024,0.02261329,0.04040402,0.06951527,-0.00860408,-0.08917794,0.01238124,-0.00993971,0.04330826,0.00887201,-0.04143332,-0.01085347,-0.04686153,0.00570064,0.03664984,0.00397492,0.0083002,0.01969337,0.00070981,0.0222346,0.08487283,0.04743562,-0.02371896,-0.02914924,0.02214719,0.02812759,-0.01806999,0.01061775,0.02276777,0.01708821,-0.00337186,0.04782785,0.07706767,0.07652105,0.02920234,0.0865696,-0.00834069,0.05145684,-0.01670948,-0.02588328,0.0300967,-0.05326515,0.00782111,0.06124831,-0.00319112,-0.2517682,0.05914849,0.04121246,0.03510723,0.03961978,0.00040652,-0.01108025,-0.04078631,-0.01303725,0.01102113,0.07548805,0.04142991,0.02116117,-0.03232546,-0.03835858,0.02815693,-0.00173561,0.01149633,0.05305828,0.00838008,0.02147068,0.04126127,0.2267419,-0.01151435,-0.00437164,0.02051634,-0.00949369,0.00064974,0.02115793,0.05723134,-0.01753406,0.04456564,0.09203277,0.00974364,-0.04916085,0.03044114,-0.01112244,0.08827687,-0.00386212,-0.03965474,-0.09107177,0.02719704,-0.00309545,0.01352759,0.0775229,0.01602306,-0.02275433,-0.08757164,0.02443581,0.04071648,-0.07412544,-0.04576835,-0.04346234,0.00182125,0.03104907,0.05859862,0.02082641,-0.04660898,0.00085776,0.00575002,0.00947919,0.00581291,0.04916741,0.00410201,0.01057443],"last_embed":{"hash":"1sqmn2m","tokens":113}}},"text":null,"length":0,"last_read":{"hash":"1sqmn2m","at":1753423606318},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#---frontmatter---","lines":[1,6],"size":257,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08670629,-0.02110642,-0.00717279,-0.02456491,-0.05069147,0.0528002,0.02397671,-0.02018609,0.10183951,0.02163887,0.03373066,-0.00643305,0.07496665,-0.01575589,-0.03408846,-0.04235121,-0.01898153,-0.01348635,-0.04763628,0.01278898,0.07789524,-0.06426145,-0.05322983,-0.11545225,0.03295536,0.04887879,-0.02579216,-0.0390141,-0.04176576,-0.22545993,0.0249686,-0.02473323,-0.01521736,-0.07580792,-0.09623387,-0.07591184,-0.02050486,0.04389788,-0.05342295,0.03923241,-0.01432045,-0.00308244,-0.01703565,-0.05614484,-0.00719081,-0.01574028,-0.02793262,0.01234848,0.05344296,0.00949689,-0.05206605,-0.0079667,0.05029913,0.00987083,0.06075075,-0.00870833,0.04948372,0.08509105,0.00630575,0.01022327,0.03175391,0.03122912,-0.17644827,0.03684594,-0.00137157,0.03530667,-0.00839319,-0.02483761,0.05653491,0.1028121,-0.0185646,0.01943497,-0.02941785,0.11232244,0.02054968,-0.00061401,-0.01590247,0.00319822,-0.03687954,0.0442695,-0.01906282,-0.04299811,-0.01751807,-0.03890301,-0.00774736,0.04142925,-0.00500361,0.02221162,0.05148743,-0.05198577,0.05109036,-0.0203117,0.0115955,0.01664471,0.02790068,0.01197152,0.08152319,-0.06058719,0.02550655,0.10051504,0.04552217,0.00109466,-0.00235731,0.05120515,0.01305856,-0.06740472,-0.05628103,-0.05083321,-0.06570783,-0.00050151,0.02750359,0.0147699,0.0838327,-0.00942897,-0.03370532,0.00045104,-0.00016329,0.01669388,0.06923241,-0.00726014,-0.03530121,0.02144334,-0.02048541,0.02532314,0.0015352,0.03460046,0.01548467,0.07104452,0.00972713,0.03310021,0.05245971,-0.05304317,-0.10960221,-0.04633364,0.03077741,0.01897559,0.00083792,0.04734619,-0.01759495,-0.00572493,0.00271607,-0.0132087,0.04283202,-0.09797121,-0.02637986,0.07455233,0.01303161,0.00911023,-0.00055261,-0.06654363,-0.01137982,-0.0193622,-0.06481136,-0.02702953,-0.03110407,0.03848277,0.06163262,0.06585822,-0.05558947,-0.0131329,-0.06148525,-0.01387114,-0.04618799,0.08817907,0.05201656,-0.04568367,0.00512359,0.01364728,-0.04706715,-0.07831164,-0.04506458,0.0245624,-0.11414173,-0.00251304,0.09221506,-0.00299405,-0.08334824,-0.07618739,0.00797968,-0.0237071,0.07633503,-0.01778845,-0.04488599,-0.0182152,-0.0277282,-0.11573742,0.0010057,-0.06178981,-0.01787756,0.07760854,-0.0028568,-0.00275861,-0.01886793,-0.01494268,-0.01844064,-0.04448113,-0.01532825,-0.01973682,0.05350122,-0.02163728,0.04247364,-0.0217572,0.04794372,0.00718636,0.00009794,0.03740643,0.01057294,-0.03040966,0.07816799,0.01925094,-0.03867621,-0.06931372,0.02750042,0.03298254,-0.0172081,0.05237738,-0.01967323,0.02423421,-0.0072604,-0.01711112,0.0020354,0.04814808,-0.00911458,-0.20160694,-0.04189641,-0.02420133,0.05303032,0.04399785,-0.02058537,0.03520305,-0.0204774,0.02729189,0.09239051,0.07104748,-0.07233537,-0.05038063,0.0611623,-0.02455949,0.05880409,-0.10681677,-0.04439389,-0.04865624,0.05744887,-0.01241841,0.01284034,-0.01939123,-0.05660256,0.01048392,-0.0354508,0.14831372,0.0118218,-0.00130975,-0.03250691,0.04931892,0.02770501,-0.00680321,0.02122216,-0.0083987,0.04915965,-0.00843704,0.00997216,-0.00739471,-0.01447617,-0.09366291,0.05365014,-0.02395894,-0.08425499,-0.03039195,0.04040987,0.00155278,-0.03810298,0.00869313,0.06555277,0.04838969,-0.06213615,0.02024468,0.00575106,0.05475863,-0.01416834,-0.04513819,0.04149825,-0.01112107,0.06467931,0.00846053,-0.07623895,-0.0180071,-0.02585451,0.02464472,-0.00248575,0.01908039,0.00217556,0.02939295,-0.01785998,0.00660424,0.11268833,0.02763075,-0.01496821,-0.00155171,0.05588143,0.04348615,-0.0356571,-0.01271601,0.02657241,0.0061974,-0.02156204,0.0373074,0.08157737,0.05810976,-0.02113274,0.09657095,0.03360849,0.02342487,-0.01888769,-0.04977235,0.02499452,-0.05545276,0.0118424,-0.00771658,0.02023332,-0.25618699,0.04746,-0.03000321,0.04674684,0.01102231,-0.0495183,0.00005663,0.00547884,0.00886403,-0.02508294,0.04880292,0.04972652,0.05756155,-0.05873967,-0.040291,0.01823685,0.0058947,0.01616918,0.05504605,-0.0209312,0.06004496,0.06081243,0.20664091,-0.0238754,0.01337368,0.01178894,-0.00339389,0.01704542,0.03176843,0.0319355,-0.02445356,0.04062661,0.11794481,-0.0135259,-0.0298661,0.00137386,-0.04498192,0.06915151,0.01297055,-0.02843646,-0.03926567,0.00281794,0.01031162,0.01909818,0.10191024,0.00812121,-0.01830498,-0.10189896,0.05732124,0.09532299,-0.09162661,-0.02585943,-0.04440107,-0.02660746,0.06564017,0.03763269,-0.02895089,0.01055447,-0.01201537,0.01020405,-0.00995196,-0.02050307,0.08367278,0.02357134,0.0239812],"last_embed":{"hash":"p8q4qz","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"p8q4qz","at":1753423606355},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling","lines":[8,230],"size":25522,"outlinks":[{"title":"_**lotto system for Powerball**_","target":"https://saliu.com/powerball-systems.html","line":15},{"title":"_**Home of Notepad++**_","target":"https://notepad-plus-plus.org/","line":19},{"title":"The main software function creates positional skip systems for lotto, lottery, Mega Millions.","target":"https://saliu.com/HLINE.gif","line":25},{"title":"The SKIP systems are just a part of the best lottery software in the world - ever.","target":"https://saliu.com/ScreenImgs/skip-systems-powerball.gif","line":33},{"title":"**_lottery strategy_**","target":"https://saliu.com/LottoWin.htm","line":42},{"title":"Nay-Sayers try to detract the viability of lottery systems software for skips, misses of N drawings.","target":"https://saliu.com/HLINE.gif","line":76},{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/skip-systems-euromillions.gif","line":84},{"title":"There are important facts to consider in using lottery software for skip strategies and systems.","target":"https://saliu.com/HLINE.gif","line":112},{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/lotto-skips-win.gif","line":120},{"title":"The combinations generated by a lottery skip system must be purged to reduce tickets to play.","target":"https://saliu.com/HLINE.gif","line":129},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":141},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":152},{"title":"Lotto skips strategies are efficient tools in conjunction with lottery filters in Ion Saliu apps.","target":"https://saliu.com/HLINE.gif","line":154},{"title":"_**lottery, lotto strategy software**_","target":"https://saliu.com/LottoWin.htm#Methods","line":158},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":181},{"title":"Run software to create lottery systems based on the mathematics of skips: Gaps between lotto wins.","target":"https://saliu.com/HLINE.gif","line":183},{"title":"\n\n## <u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>\n\n","target":"https://saliu.com/content/lottery.html","line":185},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":191},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":193},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":195},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":197},{"title":"_**Lottery Mathematics, Lotto Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":198},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":199},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":200},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":201},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":202},{"title":"_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":203},{"title":"_**Gail Howard's <u>Skip</u> Lottery Systems, Lotto <u>Wheels</u>**_","target":"https://saliu.com/bbs/messages/278.html","line":204},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":205},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":206},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":207},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":208},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":209},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":210},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":211},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":212},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":213},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":214},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":215},{"title":"**Lottery Software, Lotto Applications**","target":"https://saliu.com/infodown.html","line":216},{"title":"Lottery developer Ion Saliu created the best software to create lotto systems based on number skips.","target":"https://saliu.com/HLINE.gif","line":218},{"title":"Forums","target":"https://forums.saliu.com/","line":220},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":220},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":220},{"title":"Contents","target":"https://saliu.com/content/index.html","line":220},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":220},{"title":"Home","target":"https://saliu.com/index.htm","line":220},{"title":"Search","target":"https://saliu.com/Search.htm","line":220},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":220},{"title":"Thanks for reading software for systems, lottery strategies based on skips after lotto jackpot hits.","target":"https://saliu.com/HLINE.gif","line":222}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08900841,-0.02312782,-0.00981767,-0.02613537,-0.04848641,0.04488552,0.01290506,-0.02227355,0.09726087,0.01876665,0.03438244,-0.00355203,0.07505371,-0.01568184,-0.03205677,-0.04234784,-0.01750352,-0.01501833,-0.04801811,0.01155281,0.07417995,-0.06179376,-0.04891034,-0.11097109,0.03529703,0.05101303,-0.0272393,-0.04316176,-0.04228818,-0.22862025,0.02804606,-0.02317445,-0.00836194,-0.07287453,-0.08861127,-0.0726909,-0.02245083,0.04494239,-0.05975049,0.03878906,-0.01338574,-0.00170639,-0.01782298,-0.05958716,-0.00835979,-0.01259935,-0.02879167,0.01603048,0.05773019,0.00964789,-0.05082849,-0.01061073,0.05136479,0.00866356,0.06156607,-0.00909125,0.04922486,0.08587594,0.00288509,0.00556299,0.03804835,0.02756357,-0.17511855,0.03940189,-0.0048253,0.03589027,-0.00630064,-0.02915985,0.05446252,0.10810791,-0.01117643,0.01293291,-0.03297627,0.11070467,0.01982419,-0.00352642,-0.01659516,0.01041854,-0.03761056,0.04400628,-0.02394403,-0.04571525,-0.0173466,-0.0395785,-0.00737414,0.03961167,-0.0058924,0.02540161,0.05225753,-0.05187014,0.04828427,-0.02610794,0.0159484,0.01941296,0.02847631,0.01065673,0.08299581,-0.0581422,0.02699812,0.10031693,0.04304009,0.00298035,0.00178855,0.05181419,0.01094858,-0.06235603,-0.05558056,-0.05445283,-0.06343309,-0.00023184,0.02868291,0.01871579,0.08547342,-0.00996053,-0.02914638,-0.0022122,0.00156116,0.01947303,0.06580546,-0.00378934,-0.0350656,0.02229146,-0.02380723,0.02115383,-0.00027672,0.03498776,0.0172537,0.07080951,0.00796035,0.03181576,0.04998773,-0.05205766,-0.11354981,-0.04519802,0.02780554,0.01881708,0.00154458,0.04371845,-0.01746403,-0.00652762,0.00260584,-0.01481258,0.04538805,-0.09992179,-0.0234647,0.07486511,0.01735399,0.01068573,0.00035028,-0.06783254,-0.01255715,-0.01798318,-0.06739737,-0.0301423,-0.03199385,0.04354687,0.06228333,0.06503568,-0.05683433,-0.01019805,-0.05350958,-0.0091078,-0.04764973,0.08506308,0.05233891,-0.04728635,0.00525003,0.01532932,-0.04875199,-0.08087999,-0.04704906,0.02836504,-0.11313064,-0.00026132,0.09751143,-0.00605589,-0.08670595,-0.0791963,0.01007542,-0.02069061,0.07463932,-0.01496514,-0.04212571,-0.01491218,-0.02716723,-0.1175795,0.00676962,-0.06028637,-0.01559645,0.08390561,-0.00002319,0.00165106,-0.0136287,-0.0133512,-0.01825622,-0.0449494,-0.01249228,-0.01930723,0.0538916,-0.02275757,0.04166023,-0.02212295,0.04582349,0.0084819,0.001054,0.03366376,0.01250295,-0.0349643,0.08028136,0.02703768,-0.04487431,-0.06924584,0.02768508,0.03533493,-0.02067891,0.05129953,-0.02018484,0.02569988,-0.01071532,-0.01494888,-0.00099978,0.04476187,-0.00985175,-0.20468822,-0.04112331,-0.01866917,0.05261816,0.04335925,-0.02450268,0.03616858,-0.0165062,0.02586862,0.09358817,0.0727141,-0.07416258,-0.05042707,0.05467759,-0.02602321,0.06190855,-0.10479736,-0.03829003,-0.04230859,0.05083873,-0.01421527,0.0077958,-0.01834202,-0.05417616,0.01157864,-0.03927295,0.14856291,0.01744112,0.00189895,-0.03581018,0.04641929,0.02347163,-0.00461723,0.01583928,-0.01256947,0.04875616,-0.00988289,0.0107022,-0.00335393,-0.01714372,-0.09514045,0.04633102,-0.02222658,-0.08293456,-0.02718541,0.04232568,-0.00159004,-0.04426878,0.01192463,0.06481656,0.04577674,-0.06268625,0.01410832,0.00737809,0.05501868,-0.0172035,-0.05128969,0.04186183,-0.0106873,0.06338406,0.00775467,-0.07715349,-0.01642065,-0.02855605,0.02518396,0.00468062,0.02165095,0.00219831,0.02691785,-0.01722052,0.00873424,0.10917231,0.02508364,-0.0143237,-0.00600755,0.05685439,0.0468839,-0.03664988,-0.01342389,0.02370789,0.00355668,-0.02676852,0.03855468,0.08009909,0.05698781,-0.01428143,0.09360704,0.02964009,0.0238396,-0.02296529,-0.04269772,0.02808995,-0.05152038,0.01179013,-0.00392536,0.01719293,-0.25298727,0.0504234,-0.03158043,0.04781207,0.00532201,-0.0461931,-0.00053695,0.0075527,0.01510968,-0.03084246,0.04884738,0.05331517,0.05388189,-0.06016278,-0.03840023,0.0215877,0.00635196,0.01817777,0.05738136,-0.02209985,0.06180281,0.05902283,0.2089612,-0.01910251,0.01100656,0.0094874,-0.00396488,0.01521553,0.02579092,0.03403496,-0.02482064,0.03848127,0.11558219,-0.00803917,-0.03563663,0.00182006,-0.04501888,0.06914198,0.01256613,-0.02320604,-0.04305792,0.00330976,0.00631782,0.01656297,0.10439967,0.01042399,-0.01911155,-0.10326117,0.05368405,0.09398723,-0.09025878,-0.03552981,-0.04493452,-0.02135237,0.06806076,0.04479805,-0.03197835,0.0110282,-0.01402788,0.01223134,-0.00818985,-0.02375964,0.08302251,0.02328991,0.02489535],"last_embed":{"hash":"1bcxc0l","tokens":432}}},"text":null,"length":0,"last_read":{"hash":"1bcxc0l","at":1753423606525},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#{1}","lines":[10,33],"size":2537,"outlinks":[{"title":"_**lotto system for Powerball**_","target":"https://saliu.com/powerball-systems.html","line":13},{"title":"_**Home of Notepad++**_","target":"https://notepad-plus-plus.org/","line":17},{"title":"The main software function creates positional skip systems for lotto, lottery, Mega Millions.","target":"https://saliu.com/HLINE.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08646505,-0.00224995,-0.01668198,-0.04665972,-0.01472081,0.07046862,0.01163455,-0.03128399,0.07147975,0.02327133,0.03984964,0.01774282,0.07378511,-0.00333982,-0.0101831,-0.04383242,0.02974029,-0.03765606,-0.03506609,0.03329485,0.0576505,-0.05818539,-0.07066369,-0.11905316,0.01472911,0.02917744,-0.01744748,-0.04009828,-0.05917868,-0.22233194,0.04292572,-0.01168175,-0.02464398,-0.06886377,-0.1013988,-0.09008788,-0.02096277,0.05193957,-0.06804311,0.01336966,-0.00881147,-0.01201116,-0.0046523,-0.04319141,-0.00477406,-0.02057905,-0.03526721,0.0176689,0.08204255,0.00691163,-0.08020702,0.01053758,0.03627275,0.01589649,0.05822217,0.00008999,0.04110619,0.07230552,-0.01199747,0.00833469,0.0570849,0.01420395,-0.16085544,0.03557738,-0.00587951,0.05388779,0.013395,-0.02219905,0.05054713,0.03168861,-0.04075137,0.03042768,-0.04871489,0.1246512,0.01904256,-0.00665639,-0.02678534,-0.00755541,-0.03789916,0.04892307,-0.03336339,-0.05283816,-0.03337907,0.00289022,-0.02194839,0.04558609,0.01499349,0.05267589,0.03806013,-0.06521538,0.04122073,-0.04202949,0.02946736,0.03075205,0.02025617,0.00470821,0.07273461,-0.04951103,0.01200006,0.10534951,0.02783879,-0.00627468,0.03139605,0.05642361,-0.01324211,-0.066145,-0.03814458,-0.02477348,-0.04097116,0.01566366,0.04129312,-0.00545604,0.0588894,-0.03406978,-0.04119745,-0.00724741,-0.02115079,0.02699584,0.04211128,0.01213359,-0.03519847,0.01889431,-0.02614876,0.03591498,0.01068774,-0.00120098,0.01698267,0.04572563,0.05063841,0.03873651,0.03931659,-0.00130054,-0.13284287,-0.06446935,0.02392304,0.00502288,-0.00910066,0.04800343,-0.00529044,-0.03355787,0.011052,-0.01698892,0.05578842,-0.0833845,-0.0159063,0.06698287,-0.03769023,0.02146669,-0.0156893,-0.04424773,-0.01072268,-0.01344415,-0.06877698,-0.04343801,-0.00706573,0.02281268,0.08430433,0.04240153,-0.06784314,0.00021052,-0.07227261,-0.00787543,-0.01330833,0.10789494,0.04024058,-0.07975169,-0.00810712,0.02431033,-0.0242331,-0.0719522,-0.02811863,0.01076961,-0.08522826,-0.0136807,0.07719872,-0.00838121,-0.06219748,-0.08371685,-0.00671008,-0.00446734,0.05148728,-0.00153117,-0.03315528,-0.01388978,-0.03579079,-0.12992507,-0.02665343,-0.05569652,-0.03329189,0.04845998,-0.02689421,0.01835891,-0.03179322,-0.00840156,-0.03141899,-0.03659847,-0.00622064,-0.01022203,0.04213072,-0.0291573,0.06911673,-0.01522783,0.07547003,0.0254194,0.02620104,0.0332018,0.00309092,-0.03795629,0.09651613,-0.01534764,-0.0366491,-0.05712256,0.0710031,0.02105718,-0.03692293,0.05148081,-0.00465631,-0.00487645,0.00658371,-0.00049496,-0.00093716,0.05355918,-0.03684376,-0.20918271,-0.06504099,-0.03882099,0.05859574,0.02473536,-0.04487143,0.03454965,-0.01080575,-0.00776431,0.11265512,0.10459721,-0.08355746,-0.03348479,0.07571558,-0.00919338,0.0382036,-0.07247476,-0.05238019,-0.04509115,0.07423082,-0.00760401,0.00719966,0.00493337,-0.04739657,0.03532437,-0.04368874,0.15887199,-0.00404238,-0.00498455,-0.0089893,0.08140438,0.02113696,-0.00350252,-0.01124851,-0.00805636,0.05568457,0.00198556,0.02122935,-0.00936404,-0.00246378,-0.0657081,0.02136311,-0.01101824,-0.08430272,-0.02872784,-0.00990377,-0.00365371,-0.03167766,-0.01105622,0.05620227,0.0337487,-0.06283996,0.03698032,0.01281904,0.05085059,-0.00272873,-0.06676017,0.04434052,0.00085589,0.03686543,-0.00043213,-0.07370531,0.00306117,-0.0156793,0.01979907,0.00215709,-0.02416654,0.01621979,0.0411916,-0.01418103,0.01071899,0.10750574,0.01451859,0.0177877,-0.00821551,0.06925916,0.02008942,-0.01969719,-0.00238144,0.00367966,0.00200555,0.0034857,0.01021085,0.07470173,0.06456169,-0.00769725,0.11787586,0.04497754,0.03635659,-0.00374807,-0.0292359,0.02974889,-0.03084251,0.0449962,0.01311644,0.01901747,-0.25709751,0.0185853,-0.0183097,0.0451951,-0.00020662,-0.06125058,-0.008069,0.0094971,0.0485271,0.00881709,0.04998035,0.00476736,0.06050723,-0.07667135,-0.02752875,0.03029812,0.00484003,0.0239323,0.07406587,-0.04604341,0.05242675,0.0199494,0.23413745,-0.03283334,0.00391996,0.02205893,-0.01174484,0.02003314,-0.00987537,0.0484109,0.01921547,0.03336594,0.08527675,0.01365058,-0.01363545,0.01455967,-0.03661031,0.05610314,-0.00678907,-0.01169743,-0.0708731,0.01189606,-0.02839871,0.01491952,0.08804885,0.01343368,-0.00067609,-0.0977979,0.03457958,0.10420329,-0.0851557,0.00247972,-0.02328342,-0.03291252,0.04103927,0.01510474,-0.02337639,0.00273878,-0.015694,-0.00342398,0.02880254,-0.02961966,0.07924574,0.01824677,0.01480663],"last_embed":{"hash":"yqm0mw","tokens":470}}},"text":null,"length":0,"last_read":{"hash":"yqm0mw","at":1753423606667},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>","lines":[34,84],"size":4715,"outlinks":[{"title":"The SKIP systems are just a part of the best lottery software in the world - ever.","target":"https://saliu.com/ScreenImgs/skip-systems-powerball.gif","line":7},{"title":"**_lottery strategy_**","target":"https://saliu.com/LottoWin.htm","line":16},{"title":"Nay-Sayers try to detract the viability of lottery systems software for skips, misses of N drawings.","target":"https://saliu.com/HLINE.gif","line":50}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0866197,-0.00315198,-0.01558914,-0.04708477,-0.01515664,0.07034318,0.0102886,-0.02879539,0.07292489,0.0241976,0.03904681,0.01706874,0.07531493,-0.00410688,-0.01091248,-0.0449017,0.02946994,-0.03839904,-0.03576794,0.03342558,0.05809606,-0.05753302,-0.07291402,-0.11856541,0.01372281,0.03021043,-0.01602586,-0.03990695,-0.05936623,-0.22309905,0.04430437,-0.01307556,-0.02390662,-0.06692579,-0.10135847,-0.08971331,-0.02123945,0.05065256,-0.06655943,0.0127466,-0.01010725,-0.01102186,-0.005748,-0.04299888,-0.00469649,-0.02181358,-0.03588859,0.01830556,0.080135,0.00838179,-0.07878374,0.01123178,0.03661291,0.01570525,0.05859892,0.00016712,0.04170963,0.07281278,-0.01113006,0.00884867,0.0555454,0.01432117,-0.16214746,0.03595269,-0.00635839,0.05514971,0.01437136,-0.02337814,0.05289746,0.03205931,-0.04074964,0.03078794,-0.04839383,0.12595102,0.01753727,-0.00727721,-0.0261353,-0.00883155,-0.03731588,0.05062622,-0.03289217,-0.05256513,-0.03303231,0.00435458,-0.02369042,0.04532406,0.01312814,0.05208771,0.03823409,-0.06257502,0.03991443,-0.04276123,0.02882697,0.03034254,0.01934261,0.00386518,0.07074792,-0.04829038,0.01359682,0.10369258,0.0276762,-0.00686681,0.03246284,0.05432711,-0.0124405,-0.06588875,-0.03952546,-0.02406885,-0.0404218,0.01635502,0.04045869,-0.00502029,0.0577184,-0.0350797,-0.04060042,-0.00700569,-0.02063679,0.02740466,0.04086442,0.01042015,-0.03477887,0.01898032,-0.0262606,0.03508783,0.01009731,-0.00108108,0.01688187,0.04537062,0.04967112,0.03812449,0.04060892,-0.00188134,-0.13190238,-0.06474637,0.02357749,0.00615921,-0.00904618,0.04853997,-0.00516422,-0.03242324,0.01292849,-0.01605375,0.0566607,-0.08431593,-0.01534308,0.06807568,-0.03812252,0.0230841,-0.01627864,-0.0436797,-0.00947073,-0.01369755,-0.06630871,-0.04469521,-0.00767784,0.02325936,0.0833521,0.04196846,-0.06857104,-0.00025481,-0.07428429,-0.00813105,-0.01325483,0.10611085,0.03907559,-0.07867844,-0.00684011,0.02490531,-0.0244291,-0.07281711,-0.02682563,0.0104082,-0.08519682,-0.01429685,0.07672375,-0.0094826,-0.06210684,-0.08303642,-0.00507826,-0.00339131,0.05323099,-0.00213145,-0.03173764,-0.01278981,-0.03522975,-0.13106562,-0.02660435,-0.05425995,-0.03378783,0.04740461,-0.02716076,0.02016887,-0.03060204,-0.00685591,-0.03325272,-0.03511796,-0.00683996,-0.00891312,0.04063912,-0.03012712,0.07042967,-0.01501911,0.07367437,0.02418848,0.02747179,0.03330619,0.00458125,-0.03766683,0.09672506,-0.01523651,-0.03910988,-0.05799614,0.07077088,0.02165277,-0.03767516,0.05046941,-0.00530569,-0.00581372,0.00367306,-0.00123642,-0.00204167,0.05373051,-0.03810679,-0.20975913,-0.06693619,-0.03810496,0.05828837,0.02447363,-0.04255867,0.03442687,-0.0109491,-0.00777553,0.11140588,0.10458418,-0.08179022,-0.03237416,0.07665981,-0.00981555,0.03643169,-0.07410331,-0.05226407,-0.04518165,0.0748247,-0.00825882,0.00751185,0.00646767,-0.04733609,0.03521872,-0.0424374,0.15886322,-0.00395528,-0.00419175,-0.01015743,0.08073507,0.01835351,-0.0033998,-0.00862363,-0.00839437,0.05760712,0.0029296,0.01870287,-0.00781905,-0.00315069,-0.0670111,0.02154595,-0.01124484,-0.08373577,-0.03071233,-0.0101603,-0.00425565,-0.03117057,-0.01081724,0.05928859,0.03318462,-0.06162623,0.03861284,0.01319311,0.0503708,-0.00299323,-0.06609015,0.04355314,0.00228572,0.03818446,-0.0000371,-0.07317586,0.0018831,-0.0164305,0.022404,0.00118685,-0.02449011,0.01632903,0.04272229,-0.01519132,0.01000458,0.10669037,0.01447819,0.01842557,-0.00695665,0.06834158,0.02051272,-0.01985949,-0.00302877,0.00281122,0.00417523,0.00370143,0.00950721,0.07314965,0.06423504,-0.00855122,0.11783042,0.04623832,0.03620367,-0.00421915,-0.03005517,0.031315,-0.03092055,0.04512172,0.01256978,0.01720831,-0.25732708,0.01795291,-0.01698683,0.04466892,-0.00246785,-0.06346869,-0.00638484,0.01086144,0.04674492,0.00663343,0.04960116,0.00380247,0.06000447,-0.07549209,-0.02682254,0.03120779,0.00603049,0.02690145,0.07606833,-0.04835915,0.05046066,0.0204883,0.23550843,-0.03349753,0.00383183,0.02282681,-0.01096685,0.0212555,-0.01026246,0.0461247,0.01973686,0.03411007,0.0842341,0.01425412,-0.01451273,0.0143927,-0.03742292,0.05589699,-0.005848,-0.0111457,-0.07082811,0.01317776,-0.02868217,0.01485948,0.08940694,0.01264872,-0.00009284,-0.09633169,0.03377851,0.10564576,-0.08509754,0.00395675,-0.02261943,-0.03408355,0.04137954,0.01442818,-0.02514076,0.00318878,-0.01473723,-0.00427188,0.02879704,-0.03002899,0.07827424,0.01931822,0.01287868],"last_embed":{"hash":"1ib5rs5","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"1ib5rs5","at":1753423606870},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function S = Create the <i>Skip Systems</i></u>#{1}","lines":[36,84],"size":4660,"outlinks":[{"title":"The SKIP systems are just a part of the best lottery software in the world - ever.","target":"https://saliu.com/ScreenImgs/skip-systems-powerball.gif","line":5},{"title":"**_lottery strategy_**","target":"https://saliu.com/LottoWin.htm","line":14},{"title":"Nay-Sayers try to detract the viability of lottery systems software for skips, misses of N drawings.","target":"https://saliu.com/HLINE.gif","line":48}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08158516,0.00073947,-0.00127611,-0.05469115,-0.04576127,0.06017677,0.02157239,-0.03332887,0.07818545,0.02565381,0.00522484,0.0042391,0.08620571,-0.01130268,0.00033669,-0.0423032,-0.00846001,0.00712492,-0.0704378,-0.02575798,0.06843432,-0.04974747,-0.0637528,-0.12211832,0.00861772,0.03082789,-0.0166492,-0.04106792,-0.02823739,-0.24350454,0.02780432,0.02439526,-0.03478632,-0.06241416,-0.09611678,-0.06512103,-0.04658154,0.04771582,-0.07851439,0.0288424,0.00377288,0.02740791,-0.02145026,-0.03495933,0.00085297,-0.0002705,-0.01352447,-0.00674616,0.01806488,0.00444475,-0.07009958,0.00520075,0.00296037,0.06017802,0.05602668,-0.00018929,0.0224645,0.07768903,-0.02975303,0.02144424,0.07492773,0.0267129,-0.16717176,0.02234212,0.02511481,0.03980961,0.00645948,-0.03307006,0.04514206,0.06750949,-0.01099766,0.03306991,-0.05316805,0.10387946,0.03957367,-0.00470392,-0.043209,0.00609539,-0.02523049,0.04459285,-0.01807982,-0.065541,-0.05330969,-0.00378792,0.01396975,0.02405707,0.02144091,0.02769906,0.03118364,-0.03320522,0.03302215,-0.01540892,0.02501871,0.02377152,0.03553379,0.00851588,0.05010314,-0.05822045,-0.0240968,0.10918245,0.01904839,0.02290354,0.02759487,0.05919624,-0.04461972,-0.05119266,-0.05856035,-0.03055166,-0.07492238,-0.0208826,0.04606674,0.02072518,0.08112746,-0.04987393,-0.05040563,-0.01228982,0.01248549,0.03403731,0.0436013,-0.00533525,-0.02694324,0.0388137,-0.01197555,0.03745705,0.01707878,0.01148692,0.00730321,0.08962727,0.0398773,0.03964218,0.04502529,-0.01253458,-0.10180138,-0.06901392,0.01249368,-0.0357006,-0.01222815,0.02291984,0.00615936,0.00345276,0.02645474,-0.04427421,0.08460885,-0.07763783,0.01575185,0.08420098,-0.02442163,0.03573427,-0.01992012,-0.0066841,-0.00204328,-0.02792295,-0.0355863,-0.04200143,-0.00972749,0.03912643,0.06603953,0.04932873,-0.04352636,0.03720413,-0.08288407,0.00923094,-0.00891871,0.12578219,0.04745156,-0.08255566,-0.01856406,0.03891267,-0.03190423,-0.02352681,-0.02064657,0.06095815,-0.07638569,0.00755334,0.08134163,-0.01338931,-0.07421779,-0.03952266,0.00976555,0.0007704,0.05679988,-0.03801424,-0.0490816,-0.01167736,-0.05582605,-0.13390256,-0.00812782,-0.02908061,-0.01781587,0.01227281,-0.0226432,-0.0060432,-0.01432407,-0.00763014,0.00596309,-0.03100104,-0.02094805,-0.03969045,0.07762488,-0.01263648,0.05251502,-0.01390725,0.04216641,0.01733666,0.01307284,0.02785832,0.02709466,-0.0491327,0.09238529,0.01924515,-0.02173876,-0.06230951,0.07327818,0.05068862,-0.03009553,0.0622783,-0.01605748,-0.01030227,0.01428896,-0.02662962,0.00510508,0.06018794,-0.00809855,-0.19746877,-0.01263295,-0.0358748,0.03752954,-0.0026237,-0.03455108,-0.01112319,-0.01536164,-0.01041307,0.07518677,0.0806624,-0.05196561,-0.02771935,0.07456414,-0.02958737,0.0376089,-0.08152075,-0.06095788,-0.05825691,0.04829599,-0.00089714,0.00945275,-0.02088683,-0.07997186,0.03631107,-0.03921253,0.13666444,0.01467669,0.00668708,-0.0002674,0.07230528,0.0202653,0.01818282,0.0168329,0.02640081,0.07400092,0.00033234,-0.01826493,0.0225303,0.00591906,-0.05645675,0.02388319,-0.0378641,-0.07597861,0.00175925,-0.01507076,-0.04110197,-0.01619645,0.03856062,0.09244958,0.0497344,-0.07475729,0.04300126,-0.01576106,0.05217159,-0.03328707,-0.05030179,0.03056421,-0.02100436,0.04389562,-0.01932609,-0.05816508,0.02458094,-0.01383029,0.01712423,0.03597201,-0.00334042,0.01347316,0.03814782,-0.00265358,-0.01091734,0.09260495,0.02061223,0.01366235,-0.00686714,0.03029718,0.02437171,-0.01519877,-0.00369388,-0.01647956,-0.00913286,-0.02236067,-0.00952257,0.05947039,0.03301635,-0.07276031,0.0858361,0.03828941,0.04481396,-0.02067781,-0.07482696,0.07579082,-0.02657892,0.02472903,0.04419943,0.00274525,-0.25766778,0.03259386,-0.03414508,0.03041384,0.00085632,-0.04518032,0.00232202,-0.01071258,0.00139349,0.02457379,0.05177739,-0.00037155,0.0468306,-0.07410575,-0.01584298,0.04888247,0.00632164,-0.03568719,0.04027751,-0.0630521,0.07268595,0.05890974,0.24153838,-0.01264873,0.00388074,0.02254041,-0.01532641,0.01633262,-0.00716676,0.03452583,-0.00682244,0.03530653,0.0739214,-0.01936697,-0.03658295,0.01416454,0.00492029,0.06409431,-0.02567496,-0.02752365,-0.11911908,-0.00692493,-0.02687257,0.01019033,0.11235166,0.00602443,0.00167989,-0.10093455,0.04162165,0.07481686,-0.07909016,0.01896298,0.00961994,-0.03448078,0.0280637,0.01385644,-0.01802488,-0.00877274,-0.04898524,0.02475009,0.04828554,-0.01695875,0.07104718,0.0269511,-0.00264062],"last_embed":{"hash":"kx1384","tokens":453}}},"text":null,"length":0,"last_read":{"hash":"kx1384","at":1753423607042},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>","lines":[85,120],"size":2884,"outlinks":[{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/skip-systems-euromillions.gif","line":7},{"title":"There are important facts to consider in using lottery software for skip strategies and systems.","target":"https://saliu.com/HLINE.gif","line":35}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08174931,0.00051641,0.00059122,-0.05532486,-0.04330007,0.06121101,0.02191616,-0.03086513,0.07940643,0.02673355,0.00504071,0.0038668,0.08983628,-0.01256925,0.00038356,-0.04286997,-0.00970527,0.00899225,-0.07088318,-0.026653,0.07016577,-0.04845507,-0.0633369,-0.12368279,0.00961769,0.0304132,-0.0168165,-0.03979784,-0.02738489,-0.24486069,0.02537802,0.02509164,-0.03384151,-0.06165779,-0.09587885,-0.06598889,-0.04567373,0.04698013,-0.07609595,0.02918075,0.00479376,0.02699477,-0.02187076,-0.0358205,0.00154792,-0.00031334,-0.01644369,-0.00553258,0.01545763,0.00516824,-0.06850214,0.00781072,0.00053135,0.0601314,0.05685499,-0.00187691,0.02272598,0.07671672,-0.03029326,0.02187834,0.07595094,0.02638831,-0.16820955,0.02422074,0.02270708,0.03885166,0.00737158,-0.03515946,0.04459305,0.06772163,-0.01199771,0.0322358,-0.05438556,0.10285876,0.03877814,-0.00653521,-0.04300324,0.00388033,-0.0251305,0.04672022,-0.01658801,-0.06705278,-0.05480461,-0.00510948,0.01416953,0.02186075,0.0209132,0.02697798,0.0301868,-0.032467,0.03387873,-0.01752423,0.02294766,0.02416168,0.03593889,0.01012144,0.04851823,-0.0577402,-0.02526042,0.108779,0.01769265,0.02423334,0.03009185,0.05849557,-0.04595843,-0.0499635,-0.06001712,-0.0293627,-0.07622166,-0.02179535,0.04562557,0.02072674,0.07960326,-0.05147046,-0.04887874,-0.01091605,0.01452735,0.03356561,0.0416442,-0.00733724,-0.02506877,0.03900447,-0.01108964,0.03668926,0.01774717,0.01130802,0.00894572,0.08960567,0.04053764,0.04002364,0.04599346,-0.01400471,-0.10155232,-0.069559,0.01086011,-0.03590543,-0.01250659,0.02138781,0.00654938,0.00296641,0.02813953,-0.04267933,0.08233248,-0.0758456,0.01533108,0.08562498,-0.02526088,0.03823791,-0.01824759,-0.00555768,-0.00231156,-0.02842435,-0.03418551,-0.04347372,-0.01095331,0.0410915,0.06305034,0.04816725,-0.04238412,0.03740572,-0.08747449,0.01079599,-0.00821192,0.12503164,0.04577339,-0.08016841,-0.01829515,0.03745071,-0.03003207,-0.02373013,-0.01910888,0.06092051,-0.07571986,0.006595,0.08277424,-0.01457526,-0.07381671,-0.0392739,0.00902982,-0.00020553,0.05766392,-0.03803707,-0.04836508,-0.01055497,-0.05752619,-0.133362,-0.00841262,-0.02629648,-0.01649954,0.01249733,-0.02313326,-0.00478642,-0.01374849,-0.00757292,0.0065318,-0.0301442,-0.02226089,-0.03940561,0.07739414,-0.01120906,0.0534411,-0.01419364,0.04068526,0.0145666,0.01402917,0.02649439,0.02885342,-0.04783416,0.0923937,0.02030056,-0.02205037,-0.06286599,0.07295293,0.0494716,-0.02957914,0.06200936,-0.0136902,-0.0106725,0.01216238,-0.02827473,0.00626152,0.06232876,-0.0075257,-0.19827165,-0.01113266,-0.0365964,0.03600676,-0.00342564,-0.03489204,-0.01192327,-0.01458754,-0.01100882,0.07169364,0.08012811,-0.04763472,-0.02757995,0.07451107,-0.0280636,0.04068331,-0.08021467,-0.06195872,-0.05658448,0.04697985,-0.00046595,0.01029013,-0.01981359,-0.07856333,0.03661729,-0.03860787,0.13706854,0.01500579,0.00940531,-0.00101641,0.07285339,0.01913191,0.01864019,0.0179548,0.02731393,0.07460993,0.00141778,-0.01961314,0.02392483,0.00494078,-0.05444693,0.02240684,-0.03827022,-0.07408501,0.00238154,-0.01511788,-0.04091545,-0.01606986,0.0387496,0.09155177,0.04944079,-0.0751676,0.04423244,-0.01554502,0.0501393,-0.03359091,-0.04964227,0.02900393,-0.02139944,0.04421557,-0.02029131,-0.05815133,0.02414577,-0.01477558,0.01629445,0.03461703,-0.00438827,0.01448586,0.04041801,-0.00197522,-0.01220705,0.0932527,0.01843567,0.01496999,-0.00607287,0.02874849,0.02338623,-0.01682233,-0.00275135,-0.01746331,-0.00889055,-0.02269134,-0.01071659,0.05560139,0.03203459,-0.07530756,0.08469708,0.03752089,0.04460469,-0.02166953,-0.07642806,0.07598671,-0.02444985,0.02168316,0.046434,0.00076045,-0.25842142,0.03096035,-0.03542113,0.02984757,-0.00014633,-0.04632464,0.0028765,-0.00878299,-0.00084774,0.0257372,0.0516731,-0.0012636,0.04683017,-0.0738197,-0.01569491,0.05085568,0.00457344,-0.03520824,0.04047885,-0.06244421,0.07422426,0.05949523,0.24262583,-0.01323973,0.00460921,0.02287057,-0.01690845,0.01666809,-0.00703166,0.0321102,-0.00643671,0.03806385,0.07287485,-0.02027532,-0.03730747,0.0153423,0.00731683,0.06599379,-0.02493753,-0.02667977,-0.11775556,-0.00844245,-0.02559754,0.01077747,0.11279074,0.00497827,0.00292917,-0.10164117,0.04022546,0.07462928,-0.0764759,0.02181358,0.00962928,-0.03373478,0.02773473,0.01388664,-0.01762707,-0.01032739,-0.04966361,0.0240946,0.04692258,-0.01605709,0.07163843,0.03054461,-0.00346351],"last_embed":{"hash":"bpa21h","tokens":453}}},"text":null,"length":0,"last_read":{"hash":"bpa21h","at":1753423607206},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Function G = <i>Generate Combinations</i> from Skip Systems</u>#{1}","lines":[87,120],"size":2813,"outlinks":[{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/skip-systems-euromillions.gif","line":5},{"title":"There are important facts to consider in using lottery software for skip strategies and systems.","target":"https://saliu.com/HLINE.gif","line":33}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10017994,-0.00338333,-0.01627919,-0.0221694,-0.05356842,0.07715455,0.04546472,-0.02347159,0.08586641,0.02746596,0.01743833,0.01623977,0.06348382,-0.01485584,-0.01803042,-0.02437767,0.0010635,-0.02921536,-0.01471455,0.00851705,0.036989,-0.05787311,-0.05129747,-0.11267177,0.04401125,0.00960246,-0.01445454,-0.06131331,-0.06827357,-0.23074076,0.02322227,-0.0008491,-0.03337551,-0.08494869,-0.1112854,-0.05184143,-0.03080645,0.06590574,-0.0376403,0.03133864,-0.00726849,0.01126469,-0.01707841,-0.05900952,-0.00335372,-0.02861937,-0.02978095,-0.00421465,0.06362305,0.00170538,-0.06560384,-0.00480824,0.03239973,0.03390173,0.05858997,-0.00792656,0.04166992,0.09372298,-0.00698307,0.01276271,0.04098719,0.03107385,-0.15249562,0.03623571,0.024239,0.04098437,-0.00962411,-0.00324917,0.03833742,0.094727,-0.00955032,0.02109041,-0.05683799,0.11066855,0.0365088,-0.02655316,-0.02745692,-0.01771477,-0.03449693,0.03851547,-0.04021528,-0.05624802,-0.00903829,-0.00826511,-0.01708741,0.04799492,0.0027902,0.03029115,0.05164778,-0.06463088,0.06864332,-0.00323598,0.01501916,0.01768137,0.02344735,0.00297075,0.09970888,-0.06524045,0.00031775,0.10404667,0.03843117,-0.01065055,0.00777382,0.06631676,0.01574522,-0.07466276,-0.05504491,-0.0366833,-0.08234493,0.00609961,0.06420743,-0.02575344,0.08005329,0.00237777,-0.03097221,0.01898015,0.00348811,0.03181942,0.05375206,-0.00668523,-0.0297178,0.03757415,-0.00681483,0.01381151,-0.01143111,-0.00546703,0.01934902,0.07200815,0.01167343,0.02635425,0.04871461,-0.0222401,-0.10844137,-0.06761908,0.02618187,-0.01013478,-0.00573564,0.03063233,-0.01302309,-0.00886832,0.00986213,-0.01126002,0.07065499,-0.10200574,-0.01279016,0.06217143,-0.00688771,0.02827807,-0.01025786,-0.06505683,-0.01493796,-0.02647279,-0.06124809,-0.03640067,-0.0321047,0.01699381,0.0640444,0.07190099,-0.06782423,0.01441308,-0.06236199,-0.02699037,-0.02952362,0.09709709,0.04077544,-0.04982153,-0.00942562,-0.00091437,-0.03697803,-0.07047272,-0.05681778,0.03013423,-0.07431547,-0.02074113,0.08536401,-0.00614588,-0.10932292,-0.0630597,-0.00732611,-0.02664031,0.06694394,0.0221639,-0.03523497,-0.00100298,-0.01022269,-0.13668942,0.0153931,-0.07127012,0.00118412,0.02514078,-0.0347033,-0.01750496,-0.05636843,0.02674449,-0.02848979,-0.03449057,-0.03447507,-0.03510265,0.05363892,-0.01728598,0.02331888,-0.03788566,0.05230649,0.00115393,0.01074673,0.05832323,0.01050242,-0.04976036,0.1154407,0.00298062,-0.03974199,-0.05393167,0.05870425,0.0358431,-0.014031,0.05197251,-0.01108976,0.03835154,0.02402487,-0.02241619,0.00884541,0.06514185,-0.00094369,-0.19006896,-0.05499265,-0.03690441,0.06903391,0.02955612,-0.03194151,0.0124861,-0.01448528,-0.00370814,0.07834495,0.08332609,-0.06372688,-0.01558595,0.07217991,-0.02866448,0.01697331,-0.10500327,-0.0367087,-0.0508244,0.07763504,-0.00410699,0.01611732,-0.00858506,-0.07056013,0.01762142,-0.02078021,0.13349947,0.00474207,-0.02349373,-0.00615596,0.05572524,0.01207993,-0.03719306,-0.00827981,0.0181171,0.06322398,-0.00089109,-0.01691277,-0.02161152,0.00074611,-0.074664,0.03183673,-0.04236672,-0.07564671,-0.05585781,0.00484994,-0.0231544,0.02054995,0.01049035,0.07891606,0.06494979,-0.02210062,0.02000602,0.02149441,0.05708063,-0.00413377,-0.06840678,0.0491138,-0.02826888,0.0758684,0.01333804,-0.07078389,0.01248177,-0.00944805,0.04447313,0.01733397,0.0046569,0.01950173,0.03775486,-0.00693783,0.01514823,0.11011177,0.01100714,-0.00874804,-0.00542158,0.06876104,0.06532684,-0.02079112,-0.00428246,0.02689122,0.00087701,-0.04616843,0.00584915,0.0850893,0.05081659,-0.01662763,0.07690138,0.04718646,-0.00613929,-0.00906637,-0.04549626,0.03074334,-0.06151648,0.03472875,-0.01041643,0.01799484,-0.2420045,0.03331121,-0.01796969,0.05043291,0.00783855,-0.04782783,0.02733991,-0.00870579,0.03841839,0.01836427,0.07051263,0.02909036,0.04588829,-0.07304014,-0.01019018,0.01477369,-0.00753797,0.01328199,0.04809423,-0.02765403,0.0571368,0.04206759,0.21454263,-0.02717511,0.0177012,0.01268891,-0.00387233,0.01982925,-0.0063448,0.04379583,0.00150739,0.04409526,0.07770742,-0.00985851,-0.03678465,0.00770438,-0.05211311,0.03572223,-0.00082111,-0.02165264,-0.06152504,-0.01296689,-0.02266344,0.01050818,0.11382411,0.01408623,-0.00690093,-0.08869624,0.04449368,0.09624656,-0.06549046,-0.03214926,-0.02936591,-0.05396844,0.04727274,0.0271998,-0.02037186,-0.00771564,-0.01161549,0.00120922,0.0483225,-0.00423102,0.0750857,-0.00433432,0.01918154],"last_embed":{"hash":"16xv4ru","tokens":418}}},"text":null,"length":0,"last_read":{"hash":"16xv4ru","at":1753423607391},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>","lines":[121,193],"size":10851,"outlinks":[{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/lotto-skips-win.gif","line":7},{"title":"The combinations generated by a lottery skip system must be purged to reduce tickets to play.","target":"https://saliu.com/HLINE.gif","line":16},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":28},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":39},{"title":"Lotto skips strategies are efficient tools in conjunction with lottery filters in Ion Saliu apps.","target":"https://saliu.com/HLINE.gif","line":41},{"title":"_**lottery, lotto strategy software**_","target":"https://saliu.com/LottoWin.htm#Methods","line":45},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":68},{"title":"Run software to create lottery systems based on the mathematics of skips: Gaps between lotto wins.","target":"https://saliu.com/HLINE.gif","line":70}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09754014,-0.00794284,-0.01058588,-0.01419444,-0.05322045,0.08437537,0.03652387,-0.02060506,0.08963001,0.03427064,0.01433166,0.0201568,0.06294861,-0.01368106,-0.02304991,-0.02526194,0.00440123,-0.02927721,-0.0175438,0.01199041,0.03554304,-0.05894319,-0.05013128,-0.11204629,0.04044207,0.01040291,-0.01755482,-0.05954319,-0.07043446,-0.23253354,0.02697335,-0.00053696,-0.03362009,-0.08110739,-0.11665887,-0.05140802,-0.02884201,0.06193724,-0.02979722,0.03287711,-0.00830877,0.00539016,-0.02070098,-0.05398672,-0.00893267,-0.02575215,-0.03517563,-0.00190957,0.05888589,0.0027684,-0.0607159,-0.01100074,0.03103415,0.03230899,0.06418904,-0.00778219,0.04032616,0.0932177,-0.00448086,0.0114333,0.03852536,0.04216103,-0.15905699,0.038322,0.02032314,0.03913145,-0.01482453,-0.00981544,0.03622998,0.09714828,-0.01553638,0.02394084,-0.05521523,0.10398801,0.03140925,-0.02191009,-0.02367902,-0.01944949,-0.03732217,0.03850274,-0.03739831,-0.05410672,-0.01692281,-0.0179993,-0.01512153,0.05103474,0.00137432,0.02762211,0.04980179,-0.06034948,0.0690342,-0.00914043,0.01592386,0.01864737,0.02191175,0.00320396,0.09626412,-0.06452865,0.00361213,0.09967598,0.04203622,-0.00820852,0.00540313,0.0647031,0.01740132,-0.07368077,-0.05798137,-0.03294949,-0.07692464,0.01018935,0.0656151,-0.02214921,0.0813645,0.00107637,-0.02781145,0.02614771,-0.00333319,0.02866436,0.05319835,-0.00244401,-0.02967563,0.03935191,-0.00521687,0.01069883,-0.0102608,0.00298522,0.02645004,0.07225924,0.00427231,0.02183427,0.04125931,-0.02701459,-0.11023732,-0.0644419,0.02107521,-0.01040004,-0.00352079,0.03725804,-0.00764229,-0.00086923,0.00765443,-0.01743976,0.06849431,-0.10142571,-0.01885808,0.06747664,-0.00546899,0.02377855,-0.00597686,-0.06638198,-0.01614934,-0.02254922,-0.06037382,-0.03298571,-0.03070994,0.01307386,0.05258295,0.07476743,-0.06601375,0.00447318,-0.06628025,-0.02769971,-0.03281529,0.09444129,0.04024719,-0.05531784,-0.01692278,-0.00411509,-0.04042443,-0.06682113,-0.05827636,0.02387143,-0.07493866,-0.01724388,0.07965458,-0.00009749,-0.11409266,-0.05921788,-0.00653784,-0.02338407,0.07442133,0.02859608,-0.03693068,-0.00099645,-0.01310459,-0.13450183,0.00909478,-0.06873104,-0.00213586,0.02876047,-0.03360496,-0.01806718,-0.0559082,0.02022983,-0.02698499,-0.03597699,-0.03154688,-0.03130124,0.05303629,-0.01598493,0.01816255,-0.03368904,0.05464689,0.00045986,0.01580244,0.06140307,0.00917103,-0.05193823,0.11344577,0.0059085,-0.04058855,-0.05665909,0.04689582,0.03164258,-0.01246821,0.05612945,-0.0132891,0.03982612,0.01878096,-0.02828046,0.01574388,0.06568566,0.00445606,-0.19178081,-0.05701641,-0.0375042,0.07196265,0.02851504,-0.02973017,0.01972989,-0.00704164,0.00382999,0.08655086,0.07953039,-0.06405352,-0.02023709,0.07283216,-0.02657102,0.0247394,-0.11177874,-0.04144761,-0.05504962,0.07824779,-0.00436455,0.02914372,-0.00929848,-0.066889,0.01965105,-0.02010072,0.1367871,-0.00094832,-0.02394928,-0.01035545,0.05866648,0.01291586,-0.03426673,0.00253987,0.02365448,0.0639929,0.00114392,-0.01189073,-0.02395788,0.00163604,-0.0741402,0.03159886,-0.04387778,-0.08333977,-0.05625175,0.00987675,-0.02206399,0.01962043,0.00892343,0.08382403,0.06367603,-0.02198917,0.0276274,0.02287552,0.05529398,-0.00351143,-0.07016356,0.04454576,-0.0199898,0.07945074,0.01839871,-0.07622431,0.01727388,-0.00917887,0.0482779,0.01352791,0.00408291,0.02133255,0.03361233,-0.00727482,0.0135306,0.10513117,0.01390239,-0.00582426,-0.00821329,0.06940921,0.06026432,-0.02241185,-0.00112993,0.02619688,0.01190856,-0.04276093,0.00424244,0.08011591,0.05108343,-0.02287417,0.07875183,0.04969634,-0.00217282,-0.01177695,-0.05126783,0.02965835,-0.06547578,0.03198826,-0.0053986,0.01829454,-0.24053514,0.03028742,-0.01173428,0.04574763,0.0041442,-0.04553625,0.02849605,-0.00590361,0.04399435,0.01038456,0.0626685,0.03366924,0.04636317,-0.07794221,-0.01718241,0.01793137,-0.01446198,0.01794108,0.04593001,-0.03051682,0.05504476,0.04469756,0.21183702,-0.02858293,0.01576588,0.0094987,-0.0062963,0.01895061,-0.00642714,0.04003571,0.00068516,0.04271258,0.07224741,-0.01307965,-0.04195417,0.01115976,-0.05250928,0.03098974,0.00177692,-0.0170251,-0.05896591,-0.00835194,-0.02555759,0.00748916,0.11114022,0.00815485,-0.00669558,-0.08920696,0.05286745,0.09893547,-0.06503955,-0.03116971,-0.03212251,-0.05163547,0.04798683,0.0303492,-0.01624404,-0.00358829,-0.00815049,0.00389965,0.04448066,-0.00414381,0.07203667,0.00345386,0.0168453],"last_embed":{"hash":"vrau3d","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"vrau3d","at":1753423607561},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{1}","lines":[123,132],"size":1732,"outlinks":[{"title":"The lottery software generates combinations from the skip systems for many lotto, lottery games.","target":"https://saliu.com/ScreenImgs/lotto-skips-win.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09852819,0.02326271,0.00642367,-0.01179371,-0.01793382,0.02224909,0.04169096,-0.0275088,0.05873066,0.01783378,0.00737377,0.00395087,0.08047085,-0.02545234,-0.01972441,-0.01439617,0.03181973,0.02563175,0.0012648,0.00947573,0.03638243,-0.02317199,-0.08469147,-0.07778513,0.02265453,0.02159493,-0.01755594,-0.02648819,-0.07052755,-0.19668336,0.04785247,-0.00774828,-0.03101491,-0.06815574,-0.09032063,-0.06450917,-0.00741224,0.01654402,-0.08193498,0.00640543,-0.0044144,0.01479926,-0.00146838,-0.0550919,-0.0072508,-0.03495051,0.01763618,0.00805716,0.03003844,-0.01278847,-0.05333149,-0.00564693,0.02778797,0.03185535,0.04899575,0.00211912,0.04099455,0.09725632,-0.01490579,-0.00199882,0.04832941,0.04433987,-0.16638629,0.03838323,0.02641098,0.02926335,0.00740029,-0.00167208,0.05376971,0.05822322,0.00323241,0.03601493,-0.04097433,0.08985467,0.01657367,0.00822786,-0.00896476,-0.02924224,-0.02640429,0.02782281,-0.01877947,-0.06667575,0.00148768,-0.01019103,0.00327467,0.03288376,0.00366165,0.04630614,0.06320712,-0.08760819,0.03200962,-0.02657772,0.03852026,0.02754177,0.04567801,-0.03096061,0.06760091,-0.09020577,-0.01949531,0.12957822,0.03125457,-0.00947376,-0.013795,0.03074231,0.00939448,-0.0731781,-0.03593971,-0.03133164,-0.07911916,-0.00959647,0.04576899,-0.00271122,0.10716479,-0.01579417,-0.03485423,0.0006953,-0.04196199,0.02037557,0.04595687,0.00891299,-0.03330115,0.0221719,0.03396655,0.04590602,-0.00523704,-0.01682642,0.04111223,0.04765992,-0.01583412,0.05548827,0.03099588,-0.05159716,-0.09580354,-0.05610806,0.03946649,0.00556415,0.0005951,-0.0029647,-0.01204427,0.03291284,0.00498448,-0.00882877,0.08202811,-0.1205571,0.02495673,0.07599247,-0.02268939,0.00672372,-0.03163932,-0.02519119,0.00093414,-0.02459058,-0.02570469,-0.04180542,-0.01188408,0.01772239,0.08934042,0.0699541,-0.08671054,0.0064711,-0.05892385,0.03370303,-0.03207529,0.13125415,0.02684417,-0.07740244,0.01592663,0.0068485,-0.04990313,-0.05225998,-0.05705247,0.06547617,-0.05357128,0.00603807,0.08361352,-0.01436295,-0.07970206,-0.04568109,-0.03012767,-0.05336832,0.05005034,-0.00316569,-0.0194476,-0.02295105,-0.0105391,-0.12951478,0.01885443,-0.06126809,-0.01394382,0.03687005,-0.09043578,-0.03304638,-0.06062119,-0.02578539,-0.02023598,-0.03367476,-0.06407815,-0.02913328,0.04854333,-0.01411477,0.02989571,-0.0038556,0.06223647,0.01433622,0.04128086,0.05049318,-0.01249743,-0.05398991,0.06677371,-0.00257638,-0.06458862,-0.04776182,0.0756001,0.05572074,-0.03585471,0.03974939,-0.01697057,0.03700154,0.00067938,-0.01285255,0.01811273,0.07826272,-0.02658829,-0.20283255,-0.04195024,-0.05910034,0.036186,0.00994012,-0.05284004,0.03070224,-0.01468506,-0.00881121,0.07991134,0.06904081,-0.07778613,-0.01487596,0.06042476,-0.00884387,0.01682214,-0.0695647,-0.08815861,-0.03788452,0.04004388,-0.0124699,0.03244872,0.03126295,-0.06334053,0.03421079,-0.05583148,0.13369475,0.01583448,-0.01619449,-0.00711664,0.04718709,-0.01077878,-0.00806368,-0.03089468,0.02157768,0.08716311,0.02075016,-0.01343374,0.02351437,-0.00514183,-0.06167772,0.05373066,-0.04960057,-0.08246112,-0.01236339,0.01310738,-0.00693304,0.00461304,0.00395067,0.05240973,0.06041393,-0.03081694,-0.0036903,0.04307756,0.06921115,-0.02299305,-0.06849743,0.04317208,-0.01495421,0.01986477,0.00182331,-0.08563649,-0.00264968,-0.00989699,0.0558709,0.00351578,-0.0113521,0.01405775,0.02319045,-0.00115837,-0.01006369,0.09101732,0.0044971,0.03255994,-0.05865056,0.08304363,0.03654911,-0.06146723,0.00848924,0.04125899,0.00063889,-0.02877303,0.03058247,0.10248568,0.0866858,-0.01533563,0.06686457,0.04056126,0.01852706,0.0000464,-0.05059651,0.02341222,-0.05830142,0.03835482,0.00922182,0.02178732,-0.25219324,0.03224128,-0.0300632,-0.00085476,-0.02382064,-0.03526663,0.02493796,-0.03157212,0.00206643,0.01927863,0.07869786,0.00964216,0.03687022,-0.05535446,-0.00681129,0.01992094,0.05735374,0.0170662,0.04263832,-0.02423411,0.0582288,0.07326926,0.23675564,-0.01544415,0.01157513,0.03621429,-0.00841772,0.01693269,0.01305806,0.07229386,0.0272899,0.0616116,0.1071253,-0.01995239,-0.02081359,0.00379749,-0.01556909,0.02353093,0.02451363,-0.00965416,-0.07266587,0.00424629,-0.04410581,0.01080766,0.08053975,0.02268461,-0.01529796,-0.08774457,0.0373367,0.06748165,-0.06748177,-0.02519853,-0.04418284,-0.04283367,0.04413437,0.01726816,-0.05478198,-0.03775743,-0.01917879,-0.00188246,0.02146519,-0.01593824,0.07941679,0.00295826,0.0149375],"last_embed":{"hash":"12pfzb","tokens":148}}},"text":null,"length":0,"last_read":{"hash":"12pfzb","at":1753423607725},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{2}","lines":[133,133],"size":390,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09255046,0.02949652,-0.00560872,0.00461883,-0.03344401,0.04645531,0.0552066,-0.01651386,0.06161804,0.02256906,0.00515798,0.0132015,0.07885973,-0.02793389,-0.0285109,-0.02503726,0.01824404,0.01896182,-0.00959476,0.01162257,0.03758084,-0.04474365,-0.09512867,-0.08664975,0.05719299,0.02610192,-0.00205988,-0.04832988,-0.05604887,-0.2196748,0.01151902,-0.01148609,-0.04598551,-0.08964065,-0.07608942,-0.05449626,-0.01855317,0.01564382,-0.05969385,-0.00139153,-0.00401976,0.02683705,-0.00430383,-0.04147548,-0.01190526,-0.0522691,-0.01007251,0.01114548,0.01059198,0.01085228,-0.04113632,-0.00116478,0.01499777,0.03726141,0.05340808,0.01988286,0.04948552,0.0931084,-0.03814764,0.00315953,0.03000398,0.0621789,-0.16332988,0.04009973,0.00544201,0.04434649,-0.00740981,-0.01048264,0.05014785,0.09209071,0.02009378,0.03469024,-0.04337059,0.08154854,0.00494185,0.00793036,-0.01758704,-0.03333573,-0.03861708,0.06735441,-0.02886635,-0.04954021,-0.00311105,-0.01912813,-0.00289299,0.04305592,-0.00486041,0.04716314,0.06778389,-0.06910949,0.04430878,-0.01859213,0.0419687,0.03131103,0.05392294,-0.0296187,0.09250379,-0.07952083,-0.0002628,0.12673806,0.01487451,0.00093451,-0.01004294,0.02746201,0.01614933,-0.08614092,-0.03516103,-0.04110265,-0.10080934,-0.02021284,0.04897761,-0.0061578,0.10820401,-0.01028286,-0.02826284,0.03541791,-0.01510143,0.03470948,0.08248501,-0.00985288,-0.03106389,0.0326045,0.01390876,0.02013575,-0.01196053,-0.02834507,0.04608619,0.04490314,-0.01096162,0.0335021,0.07276532,-0.02469154,-0.09285455,-0.06282709,0.01244186,-0.00745578,0.01872434,0.02655706,0.0039228,0.02094302,-0.01474577,-0.00712947,0.09083199,-0.11282723,0.01199025,0.06433611,-0.01070776,0.01188997,-0.0251525,-0.01757119,-0.00921072,-0.01277322,-0.03336493,-0.05248878,-0.01447033,0.00497677,0.07349361,0.05234239,-0.08533669,-0.01287218,-0.04085609,0.01913381,-0.02672771,0.1332472,0.02148923,-0.08184123,0.00740804,0.01751078,-0.06931143,-0.07048306,-0.04847165,0.04170196,-0.07119039,-0.00956105,0.07282246,-0.02172855,-0.07169158,-0.03849488,-0.04408202,-0.04077964,0.04462269,-0.0116866,-0.02560313,0.00221551,-0.01196427,-0.12281129,0.01235093,-0.06647627,0.01346197,0.03928801,-0.08754015,-0.01324299,-0.05511122,0.01093363,-0.01800051,-0.0106251,-0.01950279,-0.0378075,0.04981827,-0.00649701,0.02515353,-0.03087393,0.03796789,0.00178112,0.04054953,0.03431308,-0.00698276,-0.05341059,0.09047952,0.01306287,-0.0582578,-0.044532,0.06335485,0.05184365,-0.03300448,0.02313036,-0.01000824,0.0402967,-0.0127077,-0.02579858,-0.00098788,0.06075034,-0.01593987,-0.20743777,-0.03339574,-0.04651242,0.03829551,0.01141774,-0.04477846,0.02167939,-0.00980624,-0.01191353,0.1039208,0.07434953,-0.04838895,-0.03701751,0.07051257,-0.01378162,0.02161905,-0.10833438,-0.07966096,-0.04784786,0.05737896,-0.01110834,0.04824165,0.00754227,-0.05414462,0.02690702,-0.04803489,0.13446809,0.02894829,0.00422909,-0.02699645,0.04355345,0.01989703,-0.01569509,-0.02026606,0.01944008,0.07093074,-0.01324564,-0.00930998,-0.01436273,-0.00962801,-0.05817257,0.0364417,-0.03989784,-0.08386213,-0.00560278,0.01095004,-0.02280695,-0.00879684,0.01731558,0.07227267,0.07876642,-0.04660647,0.00536158,0.04144752,0.06662862,-0.02018634,-0.07876906,0.01556007,-0.01760097,0.03629218,0.00505541,-0.08485305,-0.00934978,-0.0041609,0.06857795,-0.00096462,-0.00893919,0.01392047,0.02649253,-0.00074096,0.0116433,0.06824991,0.00635509,0.01550309,-0.01434285,0.0536507,0.07664444,-0.03498077,0.01594375,0.02263317,-0.00388211,-0.03322252,0.02266805,0.09273937,0.07661925,-0.00935227,0.09226075,0.0505483,-0.00247843,0.00048186,-0.01442936,0.05253902,-0.02907627,0.02591101,-0.01190957,0.02492018,-0.25218359,0.02160415,-0.02842586,-0.00612315,-0.02268596,-0.03874447,0.02640122,-0.03730559,-0.01409125,-0.00061485,0.07645911,0.01910076,0.02984623,-0.07379403,-0.02365076,0.02364711,0.05455901,0.01189716,0.05255196,-0.00151549,0.05357892,0.07139729,0.23363738,-0.01561546,-0.00118937,0.02779238,0.00566962,-0.00243926,0.00868447,0.0522349,0.03282585,0.05345345,0.09118444,-0.02211853,-0.03233249,0.04839082,-0.00847945,0.05823743,-0.00899553,-0.01458815,-0.09001631,0.00421713,-0.0324227,0.01220912,0.08077085,-0.00150918,-0.02315524,-0.06676648,0.01117221,0.06344476,-0.06125385,-0.02648098,-0.01877107,-0.06638739,0.02585439,0.00972171,-0.04182456,-0.03979771,0.00478064,0.00801466,0.02182841,0.02006799,0.05886225,0.02849622,0.01886513],"last_embed":{"hash":"10ggowh","tokens":145}}},"text":null,"length":0,"last_read":{"hash":"10ggowh","at":1753423607782},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{3}","lines":[134,135],"size":399,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08540847,-0.0222853,-0.00725664,-0.03834177,-0.04016744,0.0609495,0.0197649,-0.02876707,0.08209103,0.04264139,0.01184587,0.00029576,0.10980683,-0.03081597,0.01050169,-0.0355752,0.00992038,-0.0080637,-0.06052843,0.00343472,0.06403892,-0.04739859,-0.06816626,-0.10719589,0.01608823,0.01569914,-0.02314071,-0.06452971,-0.05128185,-0.23519145,0.03792319,-0.00532805,-0.02810307,-0.06397339,-0.09603275,-0.05958744,-0.00710032,0.05310052,-0.06618982,0.04620684,-0.00859814,0.0344753,0.00799062,-0.03950915,-0.01549259,-0.00466855,-0.03412697,0.01775011,0.040499,0.01491128,-0.06448416,-0.00286226,0.00975495,0.03955008,0.02393773,0.01269142,0.03600322,0.09254774,-0.0307535,0.01914858,0.0477093,0.02552465,-0.14062111,0.02362766,0.03139582,0.02998894,0.01861609,-0.01737103,0.04959249,0.09669004,-0.02043899,0.04069997,-0.02761842,0.12038653,0.01689606,-0.00477791,-0.04180568,-0.0118099,-0.0219216,0.05336174,-0.02890666,-0.04473473,-0.04711821,-0.0190004,-0.01253667,0.00485485,-0.00229925,0.0484379,0.0268695,-0.08897768,0.04669546,-0.02241495,0.03080119,0.01715552,0.04324671,0.00075395,0.05222193,-0.06233863,0.00101485,0.10181837,0.03906533,-0.01354539,0.0196439,0.06288434,-0.00940357,-0.05777211,-0.04966377,-0.01750263,-0.04509647,0.00100519,0.0521421,-0.02008311,0.07669865,-0.01518609,-0.04562698,0.01350057,0.01018695,0.0427188,0.04036735,0.00130159,-0.04895056,0.01940536,0.00716828,0.02331537,0.00796418,-0.01228452,-0.0063854,0.08101042,0.02089709,0.05588866,0.01447924,-0.04361806,-0.10023355,-0.06002343,0.02609553,-0.02226753,0.005046,0.03514586,0.00605661,0.00699964,0.00723842,-0.02556167,0.06092983,-0.09670652,0.00004013,0.0880581,-0.00368946,0.0238769,-0.00648829,-0.04946089,-0.01679159,-0.04157984,-0.06710707,-0.06231594,-0.03037039,0.02262742,0.0907473,0.04460243,-0.06821608,0.0255824,-0.04249759,-0.00335815,-0.01726107,0.11352017,0.03604319,-0.0484676,-0.01662275,0.01185235,-0.04079851,-0.03930939,-0.05795219,0.01116156,-0.06342133,0.00141592,0.11045823,-0.00508574,-0.09400539,-0.03301554,-0.01332515,-0.0063323,0.04411406,-0.01893033,-0.02048578,-0.02494812,-0.02402035,-0.14611015,-0.00446907,-0.04124554,-0.02320779,0.01828331,-0.05685546,-0.02015439,-0.04376469,0.00020519,-0.00428113,-0.01887108,-0.01142909,-0.03203312,0.06539413,-0.0223761,0.02557568,-0.03072968,0.04972553,0.02151443,0.02195608,0.07013488,-0.00066285,-0.05231643,0.10092687,-0.00027409,-0.03719112,-0.05492052,0.07520026,0.05296862,-0.01657531,0.06474344,-0.024492,0.01602956,0.01837828,-0.01674957,0.01663694,0.05621729,-0.02840029,-0.19937271,-0.04318725,-0.05181855,0.02736101,0.02112083,-0.05699591,-0.01124937,-0.02152449,-0.00191699,0.10518672,0.0653597,-0.07449222,-0.02914329,0.08700421,-0.01947769,0.00903915,-0.11986244,-0.03936272,-0.05329765,0.07809781,-0.00363316,0.01702295,0.00748031,-0.06446016,0.02030439,-0.03908217,0.1648372,0.00369054,-0.00971405,-0.01348079,0.06421129,0.04104297,0.00239512,-0.01199616,-0.0025466,0.07168321,-0.01210906,-0.02304659,0.00731208,-0.01743993,-0.0707641,0.04052699,-0.05959162,-0.10501166,-0.0189434,0.01480145,-0.04055145,0.02050078,0.01988144,0.09134603,0.06494375,-0.05376647,0.03278559,0.00724247,0.08095497,-0.02336167,-0.0734906,0.0526245,-0.0085636,0.06419006,-0.00937239,-0.05703007,0.00523777,-0.0393692,0.00411305,0.02817859,-0.0079857,0.00523248,0.01220795,0.00173248,-0.01574449,0.09332975,0.00693514,0.02361359,-0.01611433,0.03319968,0.01258166,-0.00416934,-0.00174481,-0.00071173,0.01161289,-0.02138644,0.00307824,0.0910389,0.0454349,-0.02648366,0.08157169,0.05147872,0.02210855,-0.00835693,-0.04812328,0.02866575,-0.02282774,0.04072799,0.02617787,-0.01671185,-0.24505061,0.03870718,-0.00852739,0.04289594,0.00752204,-0.05341614,0.01643392,-0.01229877,0.01114118,-0.00112028,0.05457618,0.02231725,0.04017558,-0.06971885,0.00512916,0.03329798,-0.00273211,-0.00476153,0.04395362,-0.03807135,0.09190785,0.03062111,0.22852764,-0.00633804,-0.00545808,0.03203479,-0.00501682,0.05286913,-0.00919274,0.054923,-0.01854978,0.02643107,0.09515916,-0.0066667,-0.02602617,0.00533719,-0.02376532,0.0367023,0.00274524,-0.02124052,-0.07647367,-0.01795803,-0.01775173,0.02304196,0.10622414,0.0091414,-0.00455974,-0.08298717,0.03288372,0.07689966,-0.0695674,0.01342976,-0.00619607,-0.0425769,0.0466853,0.03448901,-0.02513062,-0.01251034,-0.00054774,0.00162427,0.05296028,0.00036461,0.05727386,0.00659991,-0.001241],"last_embed":{"hash":"1t56rrx","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"1t56rrx","at":1753423607839},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{4}","lines":[136,157],"size":4300,"outlinks":[{"title":"The combinations generated by a lottery skip system must be purged to reduce tickets to play.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10877352,0.00548307,-0.01493987,-0.00539966,-0.0432101,0.05893803,0.04775237,0.00930517,0.06525341,-0.00109031,0.02680186,0.00141949,0.07713088,-0.01627038,0.03376022,-0.0438971,0.02203412,-0.00665752,-0.05604785,-0.01051139,0.07670928,-0.07641588,-0.05645876,-0.10226865,0.04515321,-0.00916267,-0.00823222,-0.03365429,-0.03017269,-0.22325569,0.04089163,-0.01359069,-0.03904012,-0.08138857,-0.08964345,-0.02659738,-0.01321178,0.02736108,-0.06338055,0.03526669,0.0038009,0.01901139,0.00671981,-0.04534621,-0.01211282,-0.01927375,0.00751403,0.01101597,0.03907893,0.01661932,-0.08061974,0.00616401,-0.01018399,0.03273097,0.03606105,0.00547123,0.04423115,0.11347536,-0.00948026,0.02243602,0.03687417,0.0429286,-0.1637021,0.05611587,0.02180008,0.02831794,-0.01080228,-0.02544979,0.01950109,0.0567207,0.00927162,0.02845572,-0.02538136,0.07520597,0.034419,-0.02325354,-0.03753933,-0.00625408,-0.0216045,0.03049485,-0.01109377,-0.04680147,-0.01327974,-0.01560993,0.00697602,0.02606086,0.01014048,0.04221324,0.04961795,-0.09539716,0.02407705,-0.01846566,0.03270265,0.01546668,0.0553591,-0.00023034,0.06063219,-0.04520307,-0.01719795,0.1103014,0.02825653,-0.0122708,0.01096487,0.05667831,0.01148345,-0.05063143,-0.04210499,-0.02217831,-0.03988484,0.02650711,0.0317736,-0.00008132,0.06342949,-0.01326022,-0.04428381,0.02179359,-0.00001975,0.00427414,0.03554149,-0.01637959,-0.05867174,0.01548455,0.02718211,0.03021877,-0.01066294,-0.04595196,-0.00619693,0.0549609,0.00707539,0.03611445,0.01861452,-0.04824794,-0.08926514,-0.06197453,0.02475007,-0.00568823,-0.00339681,0.02486892,0.00376409,0.02111175,-0.03162713,0.00342054,0.06265052,-0.10007998,-0.03863727,0.05527237,0.01207783,0.00400749,-0.00798696,-0.02043827,-0.00386332,-0.02854483,-0.05796984,-0.05742208,0.00205058,-0.02326912,0.10127311,0.05394583,-0.07799332,0.00633403,-0.024192,0.03066242,-0.03684288,0.14146724,0.03623362,-0.06554247,-0.01151368,-0.00180948,-0.03584698,-0.07513982,-0.05699228,0.035635,-0.06849136,0.00206813,0.099376,-0.03627363,-0.0949507,-0.05309159,-0.06398983,-0.00931507,0.04275371,-0.00619185,-0.01975879,0.00706979,-0.01273156,-0.13151403,0.00745948,-0.05041908,0.00318876,0.02158102,-0.05111123,0.02312465,-0.07498152,0.00914351,-0.0140921,-0.01404779,-0.01005382,-0.03250062,0.03398927,0.00177094,-0.04613674,-0.03285021,0.05480368,0.02578784,0.00280438,0.04879926,-0.01238965,-0.03921216,0.09111959,0.02072774,-0.01756078,-0.01415979,0.07117782,0.05627496,-0.024829,0.06627754,0.01273649,0.02521045,-0.00056503,-0.00561752,0.01100091,0.05366866,-0.03700249,-0.20474789,-0.03989235,-0.07942434,0.03233714,0.03783826,-0.09021259,0.02526305,-0.06046826,-0.00146256,0.12176853,0.08288637,-0.08815242,-0.01129352,0.07823513,0.00958158,0.00617716,-0.07607208,-0.04886325,-0.06204239,0.06892467,-0.00912531,0.03184012,0.02608252,-0.07170552,0.00428862,-0.02475509,0.15136611,0.02005672,0.01422325,0.03127903,0.0704407,0.02693837,-0.01072788,-0.03420104,-0.01173603,0.05113193,0.0182458,-0.04279473,0.00439138,-0.00513841,-0.07077312,0.0369818,-0.04216418,-0.11197267,-0.04315381,0.02842695,-0.01952479,0.03478149,-0.00862447,0.05163108,0.07737651,-0.03832415,0.01969424,0.00210357,0.06782398,-0.03442962,-0.08438296,0.03304325,-0.01163314,0.06906047,-0.01858869,-0.03686752,-0.00218476,-0.02896421,0.04229704,0.02673364,-0.00635792,-0.02110679,0.03691749,0.0065107,-0.01439769,0.11611279,0.02930622,0.01857171,-0.00399811,0.04127932,0.04693896,-0.02204189,-0.01029576,0.03131881,0.00397171,-0.02298706,0.01616985,0.09383213,0.04030263,0.00306998,0.08281502,0.03786774,-0.00497437,-0.00456655,-0.03670041,0.03684559,-0.04068379,0.02645722,0.02080264,0.01266143,-0.24237463,0.02617179,-0.05086909,0.04994052,0.0155916,-0.04642696,0.02401507,-0.04357447,0.02554033,-0.00905273,0.03156368,0.02583736,0.04905483,-0.06404912,0.02178294,0.00652143,0.0209643,-0.03287806,0.07018477,0.00201192,0.04753052,0.05514109,0.24236983,-0.01828593,-0.01316339,0.03124142,0.0045532,0.02199901,-0.02589641,0.05316389,0.00212727,0.02831941,0.04565895,0.00752269,-0.0262133,-0.00156654,-0.04031132,0.04526851,-0.00989463,-0.01779686,-0.05218622,-0.00570492,-0.02964186,0.03369148,0.12206391,0.0071489,-0.01977591,-0.07142692,0.05630199,0.09184373,-0.08531755,-0.03057292,-0.03115057,-0.05426071,0.06652706,0.0401202,-0.02727222,-0.04014929,-0.00953202,-0.02128515,0.02652576,0.05092008,0.0617081,-0.00891454,0.00469912],"last_embed":{"hash":"14lo7c8","tokens":272}}},"text":null,"length":0,"last_read":{"hash":"14lo7c8","at":1753423608006},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{7}","lines":[161,166],"size":911,"outlinks":[{"title":"Lotto skips strategies are efficient tools in conjunction with lottery filters in Ion Saliu apps.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"_**lottery, lotto strategy software**_","target":"https://saliu.com/LottoWin.htm#Methods","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07832953,-0.02647722,-0.01824042,-0.04786429,-0.05423035,0.05057027,0.0103069,-0.03944912,0.06932228,0.01894002,0.02068245,0.03995918,0.06964044,-0.00460862,0.014451,-0.03052015,0.02125009,-0.00694732,-0.03214813,0.00781562,0.08092403,-0.05309795,-0.08118522,-0.09785941,0.01924464,0.01978455,-0.0245084,-0.07329708,-0.05262238,-0.21376975,0.03348802,0.04416691,-0.01657597,-0.05896933,-0.09297289,-0.07609618,-0.04304127,0.04818304,-0.08558608,0.03521264,-0.0165363,0.00710396,0.00665426,-0.03010813,-0.00050607,-0.0244314,-0.02934236,0.01026788,0.03650175,-0.01480035,-0.05558301,0.00020057,0.01595166,0.06696246,0.03431674,0.01809192,0.0282641,0.06872094,-0.00708458,-0.00408759,0.05539881,0.01172827,-0.15427403,0.03393945,0.00548248,0.0440298,0.01990474,-0.00126286,0.05093824,0.0679794,-0.00526029,0.04371823,-0.04962615,0.08998484,0.03873911,-0.01429393,-0.03563763,0.00272449,-0.00764516,0.0260983,-0.0248926,-0.05447216,-0.03456558,-0.02191342,-0.0087845,0.01751415,0.02570283,0.02976805,0.02501526,-0.03961262,0.03042858,0.00327053,0.05059814,0.02835684,0.03889424,-0.01280233,0.05899935,-0.06475775,-0.03608732,0.10563024,0.04520968,-0.00827734,0.01678433,0.04699424,0.00127962,-0.07157811,-0.06050634,-0.03618228,-0.0670206,-0.01442904,0.04113409,0.01905697,0.08924131,-0.03342342,-0.05485685,-0.02173905,0.00260241,0.03183779,0.04779726,-0.00383276,-0.03935228,0.02299034,-0.00990977,0.00177928,0.03579212,0.00481851,-0.00457115,0.06099971,0.02915486,0.05332781,0.04015849,-0.00869397,-0.08098564,-0.06498758,0.02542532,-0.00201459,-0.00001652,0.03142814,0.00464199,0.00747439,0.00756403,-0.04842427,0.07622318,-0.09680379,0.00769231,0.06456573,-0.03069497,0.02295224,-0.0264505,-0.0121399,-0.03910529,-0.04758939,-0.07411395,-0.04449972,-0.00281694,0.0011824,0.09618872,0.05639649,-0.06008516,-0.00038178,-0.0463357,-0.00819426,-0.04594297,0.12463751,0.0629307,-0.0888309,-0.01935391,0.01564033,-0.03591746,-0.06016072,-0.03848309,0.02440676,-0.0769273,0.00958563,0.08432411,-0.00694878,-0.07813024,-0.0407348,-0.02093223,0.01077515,0.04404078,-0.00322297,-0.01392325,-0.02879017,-0.02217864,-0.13815983,-0.01296808,-0.05052237,-0.0322068,0.01559835,-0.03245775,-0.00889547,-0.01687655,-0.00484481,0.00403894,0.00220248,-0.01074187,-0.02336196,0.07538073,-0.05288021,0.03849037,-0.00128295,0.0745532,0.01442899,0.0261132,0.0593191,0.00275451,-0.05538342,0.10108591,0.00730694,-0.0281498,-0.0510776,0.07595298,0.04660942,-0.05873277,0.0719966,-0.01157216,0.00271824,0.00728018,0.00015394,0.01223499,0.08535453,-0.02281162,-0.1873336,-0.04667939,-0.04110257,0.04896746,0.00840284,-0.03255647,0.02205757,-0.02214823,-0.00806025,0.10580356,0.08372627,-0.06470201,-0.03436489,0.07895593,-0.0290635,0.02963972,-0.09840154,-0.07185932,-0.04654949,0.05808126,-0.01140618,0.04627727,-0.02634551,-0.0804415,0.01559981,-0.01377091,0.15312596,0.00523817,-0.00536788,-0.02865375,0.07782929,0.02550684,0.00176439,0.0162343,0.00836486,0.05319029,-0.01395612,-0.02039714,0.00596124,0.02387028,-0.08055256,0.0282359,-0.05631673,-0.09815592,-0.02401575,-0.00780503,-0.03743199,0.00170558,0.01832904,0.08247991,0.01783566,-0.05391155,0.04871837,0.01836026,0.06642172,-0.02292205,-0.08356271,0.04369335,-0.00222202,0.05585881,-0.01691757,-0.06923591,0.00267308,-0.00883597,0.0196351,0.02494416,-0.01674544,0.0119062,0.0412702,-0.02695963,-0.00284202,0.10105018,-0.00702428,-0.00154457,-0.0442508,0.03470225,0.01118344,-0.02625375,-0.00709046,0.00745802,-0.0018537,-0.02684976,0.01103782,0.09842149,0.03681125,-0.02475124,0.0866067,0.07430911,0.02958436,-0.00965222,-0.05822782,0.04682464,-0.04146934,0.03796822,0.03373988,-0.01057622,-0.24850844,0.01938203,-0.00408859,0.03932966,-0.00143092,-0.03653529,0.0184446,-0.01078268,-0.00239543,0.01768702,0.07590234,0.00115944,0.06061311,-0.04755485,0.00383394,0.03733812,0.00118038,0.00367784,0.03972699,-0.06100269,0.0751639,0.04031136,0.23230885,-0.01559729,0.01775601,-0.00085558,0.00265237,0.05715853,-0.02422415,0.05434217,0.0021586,0.03176056,0.10188203,-0.03027577,-0.03083174,-0.0146746,-0.01392458,0.02840534,0.00848701,-0.00089339,-0.08879811,-0.00870842,-0.03564203,0.03156253,0.10533086,0.01093838,-0.01729893,-0.08666407,0.05721058,0.09507151,-0.07528736,0.00385733,-0.02586766,-0.03961276,0.0597291,0.00781929,-0.00691877,-0.00489896,-0.01366938,0.01598591,0.03866021,-0.01714587,0.06366855,0.0216576,-0.01472666],"last_embed":{"hash":"1jy1bxx","tokens":353}}},"text":null,"length":0,"last_read":{"hash":"1jy1bxx","at":1753423608079},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{8}","lines":[167,171],"size":1175,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07478212,-0.04133776,0.01535429,-0.01568607,-0.00641536,0.07805452,0.03687857,0.0069932,0.09100559,0.03160598,0.02192422,0.01300517,0.05526637,0.01160279,-0.00710429,-0.03046891,0.00941135,0.01321295,0.00417411,0.01872819,0.02577997,-0.06806015,-0.05792065,-0.10152478,0.00389864,0.038869,-0.03982968,-0.06827081,-0.08724572,-0.20659287,0.02176407,-0.03490916,-0.04330828,-0.04549754,-0.10982955,-0.08433291,-0.00034705,0.05163127,-0.04898879,0.03609135,-0.00257535,-0.00821087,0.00456713,-0.03452278,-0.02444314,-0.00848996,-0.02795948,0.00256061,0.02885981,0.00541238,-0.05269546,0.01498895,0.02287935,-0.00783677,0.06651162,-0.02312162,0.04729495,0.05390984,-0.01803555,0.00410931,0.0059684,0.00809565,-0.16649976,0.03081766,0.0192557,0.06442191,0.00097822,-0.00750942,0.03823613,0.09420488,-0.0414032,0.04245315,-0.04565705,0.10791799,0.02269831,-0.00915687,-0.02107036,-0.00251849,-0.02462163,0.04546791,0.01075159,-0.0679748,-0.0353719,-0.0716961,-0.01025524,-0.00276596,-0.0022524,0.00238718,0.03716763,-0.05569257,0.06147356,-0.01041092,0.03067102,-0.00742338,0.0422102,0.00195775,0.07452956,-0.0478626,0.00234695,0.09386631,0.05620075,-0.03127097,0.02457274,0.07928416,0.01568559,-0.06093599,-0.04413223,-0.03624123,-0.0455526,0.0106385,0.00041244,-0.00953549,0.08233864,-0.00507242,-0.00440341,0.01377386,-0.00002694,0.02388142,0.05722737,-0.01306457,-0.04023965,0.0235981,0.02606813,0.03563345,0.02934971,0.00897709,0.00190589,0.07864881,0.01523175,0.05070905,0.01295341,-0.08962048,-0.07129259,-0.03175356,-0.00556571,0.01533866,0.02884629,0.07038265,0.01981671,0.00945475,-0.00739705,-0.03572193,0.06334578,-0.09745717,-0.02138833,0.06533923,-0.00253675,0.02854647,-0.01263987,-0.08143976,0.01491025,-0.01113945,-0.04153237,-0.0293223,-0.07971951,0.03723832,0.05135775,0.0579686,-0.0756361,0.01007857,-0.07822487,-0.01502919,-0.04038805,0.09627749,0.04810754,-0.03321515,-0.03284504,-0.01115789,-0.05439775,-0.07361619,-0.03678813,0.00880098,-0.10403797,0.01262859,0.08954251,0.0068139,-0.05018267,-0.06250916,-0.02172676,-0.01574694,0.05167316,-0.00614696,-0.02448284,-0.0177084,-0.01623983,-0.12601991,-0.02682282,-0.06205263,-0.00359417,0.01745553,-0.07988994,-0.04554315,-0.06644984,0.00151084,0.01025453,-0.03603862,-0.00184784,0.00089958,0.02945385,-0.00901477,0.06940426,-0.01994239,0.07026761,0.04719565,0.02844405,0.04341258,0.02175213,-0.05558848,0.08062918,-0.01567662,-0.02722913,-0.05122451,0.06087123,0.03177131,-0.03120796,0.0431021,-0.013958,0.00743054,-0.02335849,0.00462634,-0.00120964,0.07650586,-0.0031916,-0.18943086,-0.05810078,-0.03832649,0.04407528,0.02367016,-0.03643997,0.03087224,0.00378606,0.01770728,0.1489353,0.05068703,-0.06302349,-0.04722212,0.06664131,-0.00525645,0.05166921,-0.08955257,-0.06632256,-0.049025,0.05676534,-0.01029612,-0.00194619,-0.03459324,-0.04781204,0.02693849,-0.03316288,0.18971561,0.01216661,-0.01856464,-0.03589914,0.03925606,0.04716728,-0.00227007,0.01353593,0.00143099,0.05424733,0.02225733,-0.02958024,0.01022138,-0.00818519,-0.06896823,0.07114957,-0.01052609,-0.07915986,-0.06650428,0.01745892,-0.00594663,-0.02549929,0.00162684,0.08174513,0.06119644,-0.05759933,0.00867665,0.01267852,0.04321191,-0.01847277,-0.05969683,0.02981435,-0.01534254,0.07330548,0.00305506,-0.05080772,-0.02229213,-0.04973262,0.03176771,-0.01272928,0.00545692,0.00514765,0.04886365,0.00174747,-0.00554299,0.09223352,0.03479394,-0.01753938,-0.00753139,0.05845286,-0.01130448,-0.02070407,-0.01576572,0.04845943,0.03759563,-0.00469166,0.03507034,0.08027699,0.03557041,-0.05609715,0.09392565,0.00886932,0.03625835,-0.01262547,-0.04958946,0.04979389,-0.0553392,0.00219941,0.00329786,0.00014083,-0.27010372,0.025079,0.01133567,0.02288456,0.01635071,-0.06410718,-0.0089263,-0.0007594,-0.00815403,0.02878808,0.07197471,0.03637354,0.0045802,-0.04279124,-0.02045632,0.02256161,-0.01047723,0.01444258,0.05565827,-0.04994223,0.05380718,0.08272142,0.21643399,-0.01758833,0.01391684,-0.01019484,-0.0174011,0.01939129,-0.00365632,0.05002733,-0.01331735,0.04439394,0.10633232,0.00964466,0.00111404,0.00201019,-0.03865719,0.0641392,-0.00608734,-0.03412601,-0.06950324,0.00465569,-0.00844606,0.01652305,0.08996595,-0.01095593,-0.00557343,-0.09331434,0.06073275,0.08729282,-0.03472587,-0.0184467,0.00824505,-0.03626117,0.06097082,0.02695747,-0.03584346,-0.00425796,-0.02208793,0.02482575,0.00833469,0.01586466,0.05773282,0.04310136,-0.00243512],"last_embed":{"hash":"1yapbyj","tokens":487}}},"text":null,"length":0,"last_read":{"hash":"1yapbyj","at":1753423608198},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Other Considerations in <i>Skip Strategies</i></u>#{9}","lines":[172,187],"size":1303,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10525725,0.0024432,-0.0106484,-0.03522529,-0.03525683,0.04752391,-0.00370049,-0.01203468,0.05148025,0.0123281,0.0268852,-0.00384103,0.08855478,-0.03673344,-0.00458783,-0.04878517,0.03330837,0.01859429,-0.02227259,-0.02544774,0.04533587,-0.029015,-0.06997141,-0.10978789,0.03230021,0.03291734,-0.04040534,-0.03503209,-0.04166111,-0.191586,0.02532419,-0.00224685,-0.01092895,-0.07971383,-0.08075036,-0.03224279,-0.01827387,0.02577898,-0.06243254,0.02360477,0.00203417,0.01579526,0.00199314,-0.01431127,0.0304534,-0.03807165,0.0222147,0.02047673,0.0685286,-0.01617029,-0.11147476,-0.00139843,0.00887695,0.01740003,0.05130505,0.01043906,0.03924839,0.09778377,-0.00833366,0.02432809,0.04118964,0.05504851,-0.18937151,0.0671282,0.03093733,0.05082645,0.00977917,-0.02089785,0.04362213,0.04562154,-0.01310624,0.02231245,-0.0117002,0.10315175,0.0401632,-0.00336602,-0.04459209,-0.02928613,-0.0359317,0.01701877,-0.02608046,-0.01870734,0.00187194,0.00884783,-0.01605125,0.04604706,0.0319958,0.03678769,0.08296629,-0.09243324,0.04051098,-0.00047301,0.06316625,0.03564843,0.0152888,-0.01869662,0.07515977,-0.05673685,-0.00756199,0.1224087,0.01521979,-0.0111755,0.01357039,0.02787144,0.01010284,-0.08028284,-0.00747434,-0.03169279,-0.05024621,0.03764204,0.04575239,0.0000727,0.02545561,-0.02463888,-0.03319823,-0.0267317,-0.01405594,0.01802386,0.02676016,0.00770584,-0.0563797,-0.00874417,0.01897984,0.04303703,-0.00434711,-0.00967424,-0.00158144,0.05756103,0.00553433,0.03738545,0.04667074,0.0233182,-0.12202995,-0.06529555,0.02546793,-0.00942861,-0.00019658,-0.0090021,-0.00288809,0.02523364,-0.01328168,-0.04199706,0.05025998,-0.10581757,-0.04437702,0.06567074,0.00773646,0.00007033,-0.01355387,-0.02541405,0.00815301,-0.03279758,-0.02879216,-0.02487886,0.00494666,0.02266083,0.1291564,0.06182354,-0.06781601,0.00254314,-0.06518944,-0.01470878,-0.04078803,0.15485194,0.02809431,-0.11283777,0.00515565,0.04644556,-0.03113627,-0.07703913,-0.03031087,0.00778515,-0.09752086,-0.00337643,0.10570382,-0.01517608,-0.04632115,-0.05851716,-0.01152862,-0.02916879,0.03446274,-0.04364766,-0.02361517,-0.00374641,-0.03125896,-0.11651289,-0.01941977,-0.0516868,-0.02084401,0.03810497,-0.02778424,0.00405954,-0.06024706,-0.01916375,-0.04851759,-0.02229388,-0.03809449,-0.05114763,0.0686044,-0.03351453,0.02896248,-0.00185341,0.05286321,0.01536104,0.0193564,0.0407259,-0.02905667,-0.04144096,0.06800855,0.0139857,-0.04428425,-0.02019014,0.07471997,0.03682146,-0.03068172,0.06730896,-0.00149741,0.01751154,-0.01932382,0.01329861,-0.00325881,0.05658904,-0.06050497,-0.19418442,-0.03825974,-0.06173012,0.02030296,-0.00602182,-0.05404533,0.03743745,-0.04226249,-0.00967886,0.09512111,0.10620147,-0.07186911,-0.00979854,0.06807353,-0.01263761,0.00725904,-0.06710482,-0.05448546,-0.06670955,0.04954873,0.00864183,-0.01240615,0.00524199,-0.07955249,0.00206337,-0.03892532,0.12610479,-0.00550518,0.00410213,0.01810361,0.07971854,0.01104659,-0.01634854,-0.05143772,-0.0125831,0.06455265,-0.00792135,-0.00024828,-0.0016126,0.00321264,-0.07125618,0.04427805,-0.00474342,-0.07904443,-0.02516596,0.00605282,0.02025494,-0.02497902,0.00305923,0.05553138,0.02502656,-0.02073599,0.03198127,0.02992615,0.04019986,-0.00586351,-0.04990312,0.02260427,-0.03316511,0.04644658,-0.04147625,-0.07644096,0.00261391,-0.03043438,0.04466535,-0.00229755,-0.01891964,-0.00548753,0.04050618,0.0023676,0.00811384,0.09829545,0.02057868,0.03653472,-0.01502912,0.05721649,0.06693389,-0.02472645,-0.00337296,0.01897006,-0.05985307,-0.03423018,0.04697163,0.08607408,0.08565546,0.00879159,0.10366873,0.00809233,0.000991,0.00553458,-0.01152464,0.00811807,-0.04105989,0.0378563,0.04072059,0.05256052,-0.25590792,0.02841926,-0.00286387,0.04087174,0.0042058,-0.0539436,0.01059754,-0.04945664,0.0280621,-0.00958147,0.07073705,0.00826496,0.03401503,-0.06449092,-0.00681996,-0.00115431,0.0407708,0.00062214,0.0669834,0.01492237,0.02387605,0.04182783,0.23473231,0.00737274,0.00615583,0.0325662,-0.01604403,0.01476363,0.00781455,0.06952688,0.01775922,0.05596828,0.08013271,0.0074399,-0.05592389,0.02100908,-0.032868,0.05416781,0.01555157,0.00112863,-0.06884599,0.01206433,-0.03549194,0.026727,0.08353832,0.0206899,-0.0146086,-0.10786995,0.04289608,0.0711399,-0.06180723,-0.02892455,-0.0701915,-0.00632673,0.04245576,0.02159569,-0.01211262,-0.03181201,-0.0022412,-0.01361887,0.04571228,-0.03107435,0.06353854,0.01274156,0.01903615],"last_embed":{"hash":"1ro4xkb","tokens":391}}},"text":null,"length":0,"last_read":{"hash":"1ro4xkb","at":1753423608430},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>","lines":[194,230],"size":4468,"outlinks":[{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":9},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":11},{"title":"_**Lottery Mathematics, Lotto Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":12},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":13},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":14},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":15},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":16},{"title":"_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":17},{"title":"_**Gail Howard's <u>Skip</u> Lottery Systems, Lotto <u>Wheels</u>**_","target":"https://saliu.com/bbs/messages/278.html","line":18},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":19},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":20},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":21},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":22},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":23},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":24},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":25},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":26},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":27},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":28},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":29},{"title":"**Lottery Software, Lotto Applications**","target":"https://saliu.com/infodown.html","line":30},{"title":"Lottery developer Ion Saliu created the best software to create lotto systems based on number skips.","target":"https://saliu.com/HLINE.gif","line":32},{"title":"Forums","target":"https://forums.saliu.com/","line":34},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":34},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":34},{"title":"Contents","target":"https://saliu.com/content/index.html","line":34},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":34},{"title":"Home","target":"https://saliu.com/index.htm","line":34},{"title":"Search","target":"https://saliu.com/Search.htm","line":34},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":34},{"title":"Thanks for reading software for systems, lottery strategies based on skips after lotto jackpot hits.","target":"https://saliu.com/HLINE.gif","line":36}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08282605,-0.03906406,0.00037555,-0.02842754,-0.03867677,0.03054145,0.01360219,-0.02942497,0.04880548,0.01209602,0.02769919,-0.00502717,0.07167827,-0.01702532,-0.00241093,-0.01687177,0.03703029,0.03858694,-0.01431291,-0.0137042,0.06593731,-0.02921741,-0.09414031,-0.10280095,0.02034421,0.05876119,-0.04979007,-0.05397739,-0.01850488,-0.14582564,0.02966403,0.00209994,-0.03291914,-0.06277752,-0.06462044,-0.0622581,-0.03949183,0.02892018,-0.11242907,0.01558205,0.01143903,0.02518875,-0.01342657,-0.02150241,0.00637057,-0.05588336,0.04300414,0.0204988,0.08380102,-0.01440104,-0.0432688,0.02449828,-0.00499751,0.03251261,0.02892087,0.00684656,0.04405057,0.10411261,-0.02371601,0.00632788,0.02775972,0.06159838,-0.15867677,0.04072317,0.02242962,0.07691671,0.01976508,-0.00690673,0.05350439,0.05850068,0.02271787,0.02526869,-0.00009663,0.09845898,0.02010999,0.00422275,-0.02576192,-0.03479211,-0.03058915,0.01320846,-0.04174949,-0.03865216,0.00419959,0.02992928,-0.04055262,0.03483961,0.02940944,0.01938757,0.09724146,-0.07853966,0.00576408,0.00859762,0.05820983,0.04378436,0.02722875,-0.03188716,0.07128403,-0.051936,0.01174281,0.1493986,-0.01327967,-0.01334848,0.01035849,-0.01005138,0.00257581,-0.06210016,-0.03256048,-0.03229731,-0.06379935,0.05521056,0.05704289,0.01067542,0.06407207,-0.00554166,-0.03108006,-0.04363965,-0.03381964,0.01928187,0.0273852,-0.01415037,-0.06080404,0.0165325,0.00586474,0.03135773,0.00030872,-0.01024675,-0.00004552,0.06917595,-0.01480221,0.03248417,0.03466043,-0.00068716,-0.11677742,-0.04582121,0.03090034,-0.02447646,0.01972984,-0.00503165,-0.03969258,0.0492598,-0.01911351,-0.0253924,0.07193825,-0.12552267,-0.03263256,0.05535782,0.00020311,0.01724426,-0.03050649,-0.01153485,-0.00869161,-0.02368284,-0.03267409,-0.04031798,0.00529196,0.0227004,0.11876259,0.02164305,-0.06549535,0.02211374,-0.0682271,-0.0051704,-0.0176303,0.13958062,0.00545662,-0.09848793,-0.00334839,0.04512402,-0.03227882,-0.07063587,-0.04779079,0.02996618,-0.10184885,-0.01067182,0.11145084,-0.02598383,-0.08451756,-0.06129972,-0.0142142,-0.03682754,0.05247229,-0.03577003,-0.01233596,-0.01338889,-0.00920918,-0.09583402,-0.02476675,-0.05289957,0.00480284,0.04241031,-0.02551142,0.01948114,-0.04313793,-0.03787939,-0.05070047,-0.01713162,-0.06681511,-0.03332959,0.07765233,-0.027497,0.00390904,0.01107485,0.04480532,0.02788605,-0.00116441,0.0371405,-0.02118612,-0.05322302,0.04655491,0.02540458,-0.05877666,-0.02578974,0.07997136,0.05465982,-0.05832994,0.0566182,0.01357506,0.03929581,-0.03323223,-0.00620584,-0.01485466,0.06933489,-0.06911363,-0.18457738,-0.04014933,-0.03481397,0.03604,0.01757552,-0.02974813,0.00817183,-0.03863317,-0.01327978,0.10231891,0.10120179,-0.09356147,-0.00570443,0.0837585,-0.01965597,0.0132014,-0.06571683,-0.06168502,-0.04058683,0.02436842,0.01442312,-0.02412909,0.02262111,-0.10906725,0.00648398,-0.01270481,0.13339582,-0.00846779,0.01433068,0.02310064,0.06313008,-0.02593867,-0.02360306,-0.03431374,0.00401444,0.05594333,-0.01679755,-0.02398635,0.00018768,0.01067993,-0.05158819,0.00691718,-0.00657262,-0.07952527,-0.00710578,-0.00574619,-0.01376305,-0.01542896,0.0096909,0.07763389,0.00946268,0.00292723,0.03574134,0.02403342,0.03812497,0.00125456,-0.04878676,0.02531295,-0.02628899,0.05191754,-0.00123412,-0.08007119,-0.0235084,-0.02209514,0.06309059,0.00509716,-0.02304897,-0.02582679,0.03191267,-0.02199548,0.00450445,0.09063194,0.01616787,0.03542856,0.00816911,0.04631084,0.08369618,-0.03626998,-0.00565256,0.01328616,-0.04808153,-0.02128791,0.04737735,0.1211135,0.07300323,0.01016236,0.08036676,0.03789419,-0.01262414,-0.00479907,-0.00978572,0.03149289,-0.05319354,0.0094668,0.02080001,0.03312945,-0.25240886,0.04749701,-0.0037215,0.04623947,-0.02331149,-0.07218497,0.02246164,-0.00119421,0.00833925,-0.02524275,0.06230983,0.00132202,0.02587081,-0.05405686,0.00566853,0.00454564,0.06004397,-0.02817477,0.08901651,0.02691218,0.03095646,0.06137034,0.23937342,-0.00270266,0.03954997,0.02537013,-0.02042067,0.02834698,-0.00345766,0.06873825,0.02045182,0.04854339,0.06521536,0.01257313,-0.05148515,0.03780757,-0.01726099,0.03409772,-0.00384893,0.0075185,-0.08395206,0.01205886,-0.04412447,0.03645146,0.08078367,0.00482217,-0.02163417,-0.10041185,0.03868652,0.06205278,-0.07620684,-0.00230456,-0.06836804,-0.03911329,0.04975084,0.02405264,0.01054474,-0.04330123,0.00958583,-0.03770307,0.01875519,-0.0340791,0.04872682,0.00038534,0.00010187],"last_embed":{"hash":"1m22jsz","tokens":93}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423608568},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{3}","lines":[199,199],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08282875,0.00454707,0.0060055,-0.06283011,-0.00685211,0.02540256,0.02973749,-0.01366533,0.06344112,-0.0030595,0.03919083,-0.0030114,0.0943991,-0.04479979,-0.02613902,-0.03580621,0.02338379,0.02174501,-0.00266412,-0.03296166,0.05084547,-0.01939299,-0.08605582,-0.10567757,0.02803413,0.04230906,-0.06355883,-0.05518929,-0.05889133,-0.18198088,0.04096756,-0.00631977,-0.01525201,-0.06092221,-0.05002474,-0.0276252,-0.02180569,0.01112113,-0.03922167,0.01102861,0.00379821,0.00762103,-0.01005606,0.00209856,0.01746149,-0.04656668,0.01625726,0.02304766,0.0328716,-0.01548755,-0.09737383,0.00296361,0.02919961,0.01484554,0.02252381,0.00872313,0.03154652,0.09207869,0.00880036,0.03716797,0.05056166,0.04651579,-0.18380748,0.03792984,0.06083537,0.0590192,0.00188522,-0.02863082,0.03666508,0.03907893,-0.01197208,0.01931053,-0.00728655,0.10171507,0.0383443,-0.00294032,-0.05168379,-0.04337887,-0.046546,0.02402356,-0.02275738,-0.01110414,0.00777144,0.02251174,-0.04588801,0.03168463,0.03719464,0.01298794,0.10333026,-0.06423175,0.03286919,0.01601249,0.04887169,0.03668087,0.01799972,-0.03833952,0.05955063,-0.06921927,0.02282483,0.1452744,-0.01368469,-0.0228715,0.00669307,-0.00349269,-0.00546824,-0.0640678,-0.01643229,-0.02205135,-0.04310851,0.02590751,0.03017931,0.00640776,0.01802066,-0.01613505,-0.04075442,-0.03720309,-0.03141855,0.00939945,0.01632553,0.00432761,-0.04344479,-0.01407588,0.01544742,0.04447285,-0.00709099,-0.01402907,0.03130816,0.06696469,-0.02358467,0.02742233,0.04389865,0.0022506,-0.12390822,-0.04599782,0.03019901,0.01035997,0.01962665,-0.01110487,-0.004564,0.04041588,-0.01969525,-0.01973978,0.06452738,-0.10802546,-0.03925118,0.08161192,0.01363365,-0.00609721,-0.03357228,-0.02208373,0.02056661,-0.0329647,-0.02895902,-0.01165147,0.01220211,0.0297249,0.12096873,0.04782992,-0.04728116,0.00157124,-0.06803298,-0.03147805,-0.02847867,0.15719429,0.02109967,-0.10496677,0.01965159,0.02822622,-0.03263243,-0.07974682,0.00246282,0.01077303,-0.10415025,-0.00062839,0.05204731,-0.02014013,-0.03975395,-0.05799313,-0.00391012,-0.03666255,0.07227124,-0.03380712,-0.0334484,0.01502019,-0.02472969,-0.10430908,-0.0381836,-0.02285245,-0.02563671,0.04393061,-0.01339253,0.00783265,-0.06601956,-0.0172083,-0.02983768,-0.03408194,-0.03778495,-0.03119318,0.06312846,-0.0701332,0.02761393,0.00624871,0.05225689,0.02571298,0.04775279,0.045069,-0.06675541,-0.03179059,0.0385676,0.00970706,-0.04844251,-0.02496913,0.05836644,0.03348863,-0.03067871,0.06064073,0.00314204,0.00429817,-0.01032817,0.01953696,-0.01416197,0.07124408,-0.03428737,-0.21255331,-0.04685495,-0.02812967,0.0324086,-0.00002911,-0.03406854,0.02082639,-0.04008045,-0.02184827,0.11051761,0.08856702,-0.05462296,-0.00240933,0.06448904,-0.01372544,0.02640064,-0.07107223,-0.07466798,-0.08674247,0.05905026,0.0018454,-0.01740287,-0.01637255,-0.07848176,0.0105356,-0.02053491,0.12124202,-0.02678534,0.00075419,0.02241739,0.07997283,-0.01772346,-0.00735776,-0.04841748,0.00340149,0.06935495,0.00066458,-0.00421232,-0.00076982,0.01873619,-0.09389973,0.05626972,-0.01404475,-0.08225629,-0.00925778,0.02020716,0.04098031,-0.06191758,0.00880489,0.05445223,0.00762344,-0.02730685,0.02147242,0.04595016,0.04949027,-0.00582853,-0.0355422,0.06215726,-0.0218006,0.03698536,-0.04344619,-0.09893704,0.0098082,-0.01540292,0.05606554,-0.00402035,-0.01181306,-0.00508596,0.03723287,-0.00473302,-0.00241015,0.10785577,-0.00041165,0.02040247,-0.01165195,0.07140147,0.06734171,-0.03385838,-0.0156211,0.02691101,-0.03327005,-0.02962586,0.05207463,0.07782351,0.06862324,0.00891655,0.08622621,0.01137252,0.0126874,0.0048791,-0.00882329,-0.00624756,-0.03625484,0.02493857,0.02513503,0.04289743,-0.24303034,0.04056635,-0.01052141,0.039842,-0.00959693,-0.05804709,0.0193106,-0.0178135,0.02831485,0.00144627,0.05904826,0.00221029,0.02679191,-0.07127641,-0.01290429,0.00567633,0.08897424,-0.02879345,0.07160642,0.02353276,0.01964886,0.05294732,0.24577411,0.00295945,0.02616647,0.02945516,-0.01965952,0.00721266,-0.00765144,0.07092191,0.0306563,0.04086811,0.07838792,0.00313641,-0.07078464,0.00944753,-0.02813273,0.06793751,0.03737715,0.00696681,-0.07530656,0.01516325,-0.05016788,-0.02033826,0.07795464,0.03325976,-0.02652594,-0.10480479,0.03663689,0.05178225,-0.05257256,-0.02757149,-0.08560846,-0.03155092,0.04739502,0.018836,-0.02357032,-0.02258306,0.03084506,-0.02018807,0.05763308,-0.02486045,0.04360952,0.01751103,0.02205838],"last_embed":{"hash":"1sdoqa9","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"1sdoqa9","at":1753423608601},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{5}","lines":[201,201],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10077588,-0.03226434,-0.00519373,-0.03931821,-0.03493203,0.06436906,0.02406313,-0.01536982,0.05774043,0.00190651,0.02470885,-0.03351868,0.08382256,-0.02090676,-0.00839982,-0.02219303,0.01599834,0.02944548,-0.01691993,-0.02134929,0.08119628,-0.04817748,-0.06461713,-0.09459527,0.00580262,0.0510739,-0.04133663,-0.0440907,-0.03421281,-0.18655384,0.01836329,0.01244414,-0.02519509,-0.06198735,-0.07983841,-0.05058233,-0.02832785,0.04924285,-0.06697567,0.03860777,0.00968103,0.01165982,-0.02794157,-0.02704719,0.01882513,-0.04124832,0.01063944,0.00486735,0.05121229,-0.02721248,-0.07812167,0.00846354,-0.00414248,0.02871685,0.05687093,0.01249211,0.06257994,0.08928852,-0.0072647,0.02372248,0.04437662,0.0385307,-0.18209673,0.04710105,0.03615569,0.04837566,0.01645582,-0.03064789,0.03686215,0.06055032,0.01193682,0.03363284,-0.01657858,0.10854763,0.02206844,0.0111776,-0.03674958,-0.03792931,-0.01875327,0.02668762,-0.02571245,-0.03238203,-0.02310576,-0.00298501,-0.00871666,0.03498324,0.00769134,0.04030653,0.06621929,-0.08570226,0.0197601,-0.00430893,0.07059943,0.05020233,0.01444763,-0.01321879,0.07080042,-0.07229735,-0.00234231,0.12373222,0.01362482,-0.00689218,0.00348668,0.04801345,0.01231665,-0.06018674,-0.02190775,-0.03610567,-0.04755948,0.04288925,0.0465852,0.00617082,0.05402301,-0.00145788,-0.01661728,-0.02437222,-0.0107233,0.02837882,0.02116209,-0.01133966,-0.05377983,0.01965994,0.04145576,0.04024367,-0.01033237,-0.0094897,-0.00400002,0.0720588,-0.00518005,0.06187728,0.03439771,-0.00277701,-0.09841119,-0.0710811,0.00973347,0.01743611,0.02216607,-0.0143741,-0.02403922,0.05173881,-0.00071372,-0.04771794,0.05767528,-0.103105,-0.03889483,0.0862731,-0.01691728,0.01935467,-0.00889977,-0.00192828,-0.01484041,-0.03195131,-0.02415106,-0.02476703,-0.01105354,0.02079111,0.10995393,0.06083746,-0.06182706,0.02758173,-0.06570365,-0.03665749,-0.0207976,0.16564381,0.0297708,-0.07355998,0.0123137,0.02164865,-0.03109483,-0.08036111,-0.02866569,0.02135011,-0.09801874,0.00782576,0.11629321,-0.02105854,-0.04509171,-0.06885118,-0.03726327,-0.01664097,0.04530298,-0.04361237,-0.00626334,-0.02450508,-0.02622419,-0.09877602,-0.00589999,-0.05940105,0.00151407,0.03742488,-0.04795579,-0.00796576,-0.06186514,-0.03511831,-0.03022202,-0.0124215,-0.03899413,-0.04606728,0.07166736,-0.04475833,0.02865786,0.0061726,0.05188883,0.0095949,0.00503673,0.05849233,-0.03517108,-0.01763878,0.04001909,0.00464264,-0.0368864,-0.03128583,0.06278918,0.03754831,-0.02370834,0.05282115,0.0134736,0.03312426,0.01128888,-0.00957414,0.01431317,0.06073033,-0.05652022,-0.20947355,-0.02460755,-0.05764931,0.03490186,-0.00584837,-0.05377037,0.01329563,-0.02636362,-0.01730165,0.10455798,0.08676566,-0.07381347,-0.02049484,0.05587075,-0.01919029,0.02865561,-0.06955667,-0.09118106,-0.05456406,0.03653442,-0.012892,0.01043005,-0.0136929,-0.08821993,0.00950905,-0.02158588,0.1366898,0.01699216,-0.01680851,0.00800541,0.06239472,0.01641593,-0.02380164,-0.02703689,0.00668069,0.04701015,-0.02328391,-0.01180804,-0.01775773,-0.01186205,-0.07589728,0.05754467,-0.02087886,-0.06629474,-0.00050679,0.000041,0.01462606,-0.0162822,0.00547195,0.06073641,0.01402059,-0.01340105,0.02134765,0.02784543,0.04321618,-0.00371603,-0.04169985,0.02549746,-0.02692121,0.05122364,-0.01867599,-0.06201967,-0.00702603,-0.03112552,0.03308693,-0.00136379,-0.01570005,-0.01680899,0.03045201,0.00809826,0.00728822,0.10403375,0.04172222,0.00625977,-0.02139434,0.04027852,0.04413461,-0.01885986,0.00181244,0.01087087,-0.02217383,-0.03006577,0.03359306,0.10608474,0.10326941,-0.01328869,0.07607098,0.00689724,0.01110967,0.00604202,-0.02216,0.03837464,-0.04743844,0.02240936,0.03787791,0.04096583,-0.27580968,0.03559729,-0.01465129,0.0319767,-0.00460809,-0.04202334,0.01818973,-0.02471182,0.00143006,-0.02809558,0.09309296,0.02165934,0.03581553,-0.06312612,-0.03370719,0.01116175,0.03497486,-0.02262074,0.08878478,0.00931702,0.02274065,0.06367095,0.24805818,0.00547494,0.01309933,0.01298802,-0.01765648,0.01979456,-0.00653218,0.07319357,0.00555969,0.0530947,0.07285856,0.00900668,-0.04879125,0.02910238,-0.02157789,0.03744006,0.02631804,-0.0150594,-0.0851098,-0.00107711,-0.03953749,0.04485332,0.08483434,0.02356432,-0.01291904,-0.08954311,0.04763153,0.05879869,-0.05310922,-0.05072249,-0.07537562,-0.01791803,0.05604355,0.0299192,-0.01339496,-0.03931452,-0.01313019,-0.02214661,0.0334193,-0.02241326,0.04679951,-0.00771231,0.01221816],"last_embed":{"hash":"1x2w9gi","tokens":118}}},"text":null,"length":0,"last_read":{"hash":"1x2w9gi","at":1753423608678},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{9}","lines":[205,205],"size":200,"outlinks":[{"title":"_**Lottery Mathematics, Lotto Mathematics**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{28}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14007512,-0.0055487,-0.01227095,-0.02158864,-0.02223865,0.05867786,-0.00122676,0.00460916,0.05508146,0.01591149,0.0191933,-0.02137416,0.08489621,-0.00967077,0.00977845,-0.0457512,0.03959315,0.00873036,-0.01224059,-0.00836763,0.06404552,-0.03330536,-0.04732956,-0.11057395,0.01843976,0.01935788,-0.02209806,-0.0355076,-0.03636746,-0.20019849,0.01109564,-0.01003604,-0.01218925,-0.06657339,-0.1162312,-0.03934795,-0.00740562,0.03436156,-0.06594019,0.03118164,-0.01228868,0.01292346,-0.00219199,-0.04258954,0.00790677,-0.02764298,0.01668013,0.01489359,0.07954431,0.00854488,-0.08977991,0.01160202,-0.01456876,0.00860761,0.06890479,0.01634998,0.02047727,0.09800451,-0.0179142,0.01554552,0.02796576,0.02371553,-0.19680154,0.05115432,-0.00044404,0.04461939,0.00424016,-0.01301691,0.04702566,0.0411811,0.002733,0.0244587,-0.01958771,0.1148864,0.04569205,0.00062376,-0.0233744,-0.02444617,-0.0117366,0.01419212,-0.02559391,-0.04979776,-0.04125145,-0.01177953,0.00224564,0.03238345,0.02391751,0.03984939,0.05544798,-0.09959666,0.04047524,-0.02672556,0.05894032,0.01627165,0.00500318,-0.00141019,0.05773052,-0.06029607,-0.02356316,0.12034093,0.04034071,-0.00997259,-0.00625717,0.03382207,0.03014001,-0.04447672,-0.02234645,-0.02516091,-0.05388174,0.0336442,0.05511719,0.01250233,0.03608704,-0.02168159,-0.05821161,-0.0271008,-0.00308949,0.01789782,0.02766383,0.01848977,-0.05737684,0.01364737,0.01188535,0.01854334,-0.00396286,0.00496378,0.0034914,0.03728217,-0.01528153,0.05922527,0.03364471,-0.00382362,-0.10392544,-0.08019561,0.02341361,-0.01024465,-0.03333031,-0.02130941,-0.02325048,0.021852,0.00369517,-0.03382942,0.04850562,-0.09747173,-0.02740445,0.02779852,-0.00934634,0.01367374,-0.02216774,-0.01953,-0.01891599,-0.03136306,-0.02420455,-0.02837034,-0.00805645,0.02020711,0.1122762,0.0757165,-0.06107474,0.02970641,-0.0518082,-0.02128336,-0.0370809,0.14939678,0.03252466,-0.08193945,-0.03043572,0.04229695,-0.01084179,-0.065056,-0.06102991,0.01406794,-0.07745924,-0.0071548,0.10487436,-0.02149913,-0.05626,-0.06059667,-0.03041232,-0.0185702,0.03348399,-0.02678901,-0.01495442,-0.01710852,-0.04397211,-0.14000452,0.01235657,-0.07885735,-0.00810646,0.03936166,-0.02783639,0.00321148,-0.0328243,-0.01539156,-0.04165326,-0.03316083,-0.04288619,-0.03367337,0.06120397,-0.00890041,-0.00196332,0.01098786,0.05952743,0.00947571,-0.01093296,0.04800781,-0.0283891,-0.030215,0.06978338,0.00928275,-0.0293987,-0.01480704,0.08402361,0.02830295,-0.02990887,0.06610133,-0.02039616,0.02587299,0.00582769,-0.00879099,-0.00330017,0.06647559,-0.07228644,-0.20942855,-0.01580132,-0.07064267,0.0214918,0.02478034,-0.06900778,0.04611265,-0.0390536,0.00209683,0.07639572,0.09914997,-0.07952086,-0.00034348,0.06903844,-0.00158228,0.03330932,-0.04704555,-0.05678179,-0.04670992,0.04175647,-0.00196853,-0.01665622,0.01232667,-0.06979734,0.01792697,-0.03792371,0.1400542,0.02335995,0.00749258,-0.00214089,0.09286265,0.01113439,-0.01243543,-0.04346992,0.00155498,0.05234559,-0.01012427,0.0061284,-0.01741734,0.00020728,-0.05568497,0.05924807,-0.00628938,-0.09571917,-0.02755542,0.00495592,0.00068298,-0.03887332,-0.00079661,0.04344033,0.06191139,-0.02588788,0.03402608,0.01184789,0.04783932,-0.00304233,-0.08207072,0.03203532,-0.02804055,0.04438783,-0.0259565,-0.04240424,0.00594638,-0.01027425,0.0229769,0.00328554,-0.00766781,0.00444953,0.03873885,-0.01638854,-0.00229302,0.11586013,0.01799151,0.03994304,-0.00112338,0.04310876,0.01661662,-0.03257644,-0.02564917,0.03268768,-0.03277475,-0.02432983,0.06112539,0.06727038,0.08777879,0.00185069,0.10559214,0.01275976,0.00938324,0.00955246,-0.00917313,0.02819306,-0.04998011,0.02838981,0.05429379,0.0406282,-0.24888904,0.00872521,-0.0089306,0.01552001,0.0004273,-0.05078616,0.01190023,-0.03966364,0.03563902,-0.00126038,0.09031242,0.03517838,0.04438388,-0.0431501,-0.01195975,0.0271243,-0.00260479,-0.00004848,0.08630751,-0.02302923,0.0241475,0.0429742,0.24439824,-0.00070797,0.00663297,0.02766592,-0.00528769,0.01668023,-0.00265914,0.05822336,0.00975555,0.06957603,0.07440475,0.01988301,-0.0636869,0.02315168,-0.03427853,0.04580291,-0.01708266,-0.01977621,-0.08087453,0.03862491,-0.0242477,0.05895521,0.0858907,0.00758569,-0.01128344,-0.10178421,0.0628776,0.06995253,-0.07175823,-0.0254897,-0.04534829,0.01003245,0.05162461,0.03230067,-0.02052116,-0.02241399,-0.01116699,-0.00962236,0.03732236,0.00601802,0.06849273,-0.00240034,0.03091402],"last_embed":{"hash":"bk5w3i","tokens":301}}},"text":null,"length":0,"last_read":{"hash":"bk5w3i","at":1753423608720},"key":"notes/saliu/Skips Systems Software Lottery, Lotto, Powerball, Gambling.md#Skips Systems Software: Lottery, Lotto, Powerball, Gambling#<u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>#{28}","lines":[225,230],"size":659,"outlinks":[{"title":"Lottery developer Ion Saliu created the best software to create lotto systems based on number skips.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Thanks for reading software for systems, lottery strategies based on skips after lotto jackpot hits.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
