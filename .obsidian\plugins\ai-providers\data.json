{"_version": 1, "debugLogging": false, "useNativeFetch": false, "providers": [{"id": "id-1741738921406", "name": "Ollama", "apiKey": "ollama", "url": "http://localhost:11434", "type": "ollama", "model": "qwen2.5-coder:latest", "availableModels": ["qwen2.5-coder:latest", "gemma:latest", "deepseek-r1:latest", "openthinker:latest", "dolphin-mistral:latest", "nomic-embed-text:latest"]}, {"id": "id-1741739692464", "name": "Groq", "apiKey": "********************************************************", "url": "https://api.groq.com/openai/v1", "type": "groq", "model": "llama-3.2-3b-preview", "availableModels": ["llama-3.2-3b-preview", "llama-guard-3-8b", "llama3-8b-8192", "qwen-qwq-32b", "whisper-large-v3-turbo", "mixtral-8x7b-32768", "qwen-2.5-coder-32b", "llama-3.2-90b-vision-preview", "qwen-2.5-32b", "llama-3.2-1b-preview", "llama-3.2-11b-vision-preview", "llama-3.3-70b-versatile", "llama-3.1-8b-instant", "allam-2-7b", "deepseek-r1-distill-llama-70b", "deepseek-r1-distill-qwen-32b", "llama-3.3-70b-specdec", "distil-whisper-large-v3-en", "gemma2-9b-it", "mistral-saba-24b", "whisper-large-v3", "llama3-70b-8192"]}]}