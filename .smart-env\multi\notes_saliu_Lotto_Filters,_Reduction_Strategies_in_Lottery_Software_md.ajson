"smart_sources:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md": {"path":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11379133,-0.01691829,-0.02071168,-0.01759998,-0.03918438,0.02820331,-0.01342924,0.04845273,0.04505201,-0.0286932,-0.00765332,-0.01463735,0.08850914,-0.00181468,0.0050787,-0.06998542,-0.00592249,0.01075723,-0.06545078,-0.03450829,0.13018601,-0.0273282,-0.06454311,-0.08790955,0.05562085,-0.0073063,0.01590121,-0.05137579,-0.00007084,-0.21122108,0.01533908,-0.00111789,0.01797291,-0.05687177,-0.06431904,0.00533363,-0.00158378,0.05256651,-0.10720561,0.06230738,0.02660207,0.01183641,0.03246346,-0.03270312,0.01412377,-0.00774588,0.01991967,-0.00416042,0.00061728,-0.01612115,-0.07552411,0.01366255,-0.00417619,0.04211578,0.04334942,0.00337793,0.02807964,0.11531069,0.02336306,0.05353617,0.00000233,0.04673895,-0.19506389,0.08868827,-0.04149631,0.04477081,-0.02042686,-0.02085301,0.02810103,0.05816341,0.01276227,0.06610271,-0.02930858,0.04726531,0.01534548,-0.04814283,-0.04177976,-0.05585297,-0.0462324,0.00670633,-0.02055768,-0.03051267,-0.00996363,-0.01794822,0.0199955,0.04304963,0.03487386,-0.01006545,0.05284769,-0.07854663,0.00447627,-0.00013801,0.03868156,0.06198811,0.01187979,0.00778624,0.02689799,0.01150095,0.01868069,0.13220859,0.00111608,0.02152572,-0.00723166,-0.00791389,0.0574556,-0.04664866,0.00528033,-0.05465711,0.01166575,0.0443262,0.03969745,0.00175145,0.06078193,-0.04233718,-0.04733806,0.02370832,0.01820372,0.01172931,0.05120661,0.0084751,-0.06202459,-0.00674222,0.02363501,-0.01650161,0.04060169,0.02584237,0.00114924,0.06916887,-0.00231776,0.03868981,0.03050803,-0.00398862,-0.14564337,-0.051508,-0.01153373,0.00878361,0.00522173,-0.03463691,-0.04302154,0.04655156,-0.06315511,-0.03097368,0.03667775,-0.09390642,-0.01751683,0.07592438,-0.00093088,0.00532434,-0.02375074,-0.02360464,0.01480497,0.01740864,-0.02125038,-0.04745267,-0.01031032,0.02425015,0.08105966,0.06768116,-0.07868298,0.01712627,-0.000985,-0.02969097,-0.03194829,0.11826417,-0.02222445,-0.07139661,-0.020916,0.05955806,-0.02908599,-0.05038892,-0.07222824,0.01354704,-0.04800798,-0.0075571,0.08325595,-0.01035854,-0.07781979,-0.03953272,-0.05138836,-0.01727654,-0.00963134,-0.02730701,-0.02291802,-0.00016183,-0.04429753,-0.07804214,0.04537558,-0.00690948,0.03594688,0.05549752,-0.0608575,-0.01441372,-0.04539722,-0.00178732,-0.01890894,-0.01038093,-0.00358662,-0.04325612,0.03024976,0.00860619,-0.02409595,-0.00748505,0.01677044,0.05181659,-0.057576,0.05611441,-0.00178162,-0.0336704,0.08081467,0.02758985,-0.03466755,0.00547103,0.03853336,0.07014352,0.01453044,0.01880875,0.01024357,0.00137647,0.00433865,-0.00067216,0.02503025,0.02908976,-0.10010912,-0.18738554,-0.00934886,-0.05565986,0.00391915,0.01514499,-0.03949689,-0.02390211,-0.0596507,0.02004486,0.13033837,0.07764626,-0.04690594,0.00620583,0.06347499,0.00126973,-0.02919666,-0.07517799,-0.01711726,-0.08320133,0.01475476,0.01908066,0.01327302,0.02581762,-0.06868798,0.00476407,-0.02697009,0.10969951,0.02295297,0.02434268,0.03844245,0.06001375,0.01427675,-0.0313981,-0.01079748,-0.01075259,0.06128483,-0.06389223,-0.0135717,0.00421742,0.00213332,-0.02712527,0.04208491,-0.01985845,-0.12068582,-0.03204665,0.05236483,-0.01643273,0.07337168,0.00573767,0.0423114,0.07426104,-0.0127444,0.02447198,0.00207762,0.07822025,-0.06455264,-0.10118557,-0.0185875,-0.02145765,0.02565837,-0.03609497,-0.06384654,0.04614155,-0.03072473,0.05922703,0.04425621,-0.01973727,-0.00611562,-0.03714271,0.00607752,-0.01409614,0.09162427,0.00122431,0.03180772,-0.03392357,0.04013685,0.04604017,-0.01513215,-0.03613339,-0.03748973,0.04304885,-0.04096708,0.0405826,0.06345142,0.05304231,0.03977671,0.04886162,0.0047964,-0.01262348,-0.02695801,-0.01361145,0.00540488,-0.03443109,0.06204934,0.03624238,0.00638501,-0.25924358,0.02492429,0.01799561,0.03535127,0.00746575,0.01726083,0.0318563,-0.02604397,0.03676452,-0.09033202,0.0163369,0.05250411,0.02990165,-0.01823,-0.0309746,-0.00451379,-0.02821604,-0.04996097,0.10438867,0.03515402,0.05906976,0.05166328,0.2389335,-0.01107225,-0.01446861,0.00577199,0.01512237,-0.02191022,-0.06077342,0.06078373,0.01172084,-0.00465057,0.10924101,0.00703738,-0.04003285,0.02337936,-0.03833054,0.0391233,0.00999057,-0.01195513,-0.06810234,0.00945715,-0.02050869,-0.01697093,0.13708754,0.02356165,-0.05872656,-0.05979259,0.04772321,0.06547,-0.07080609,-0.04904634,-0.03480937,-0.01464787,0.03052819,0.03331989,0.01882178,0.0166792,0.00603825,-0.01909247,0.0451254,0.00569905,0.01057995,0.02395365,0.02170392],"last_embed":{"hash":"1ja8dfo","tokens":464}}},"last_read":{"hash":"1ja8dfo","at":1753423525541},"class_name":"SmartSource","last_import":{"mtime":1753363612796,"size":45998,"at":1753423416500,"hash":"1ja8dfo"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Filters, Reduction Strategies in Lottery Software":[8,292],"#Lotto Filters, Reduction Strategies in Lottery Software#{1}":[10,15],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>Theory of <i>Filters, Filtering, Reduction</i> in Lotto, Lottery Software</u>":[16,18],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>Theory of <i>Filters, Filtering, Reduction</i> in Lotto, Lottery Software</u>#{1}":[17,18],"#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_":[19,32],"#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#{1}":[21,24],"#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#I. [Introduction to Lottery Filters and Filtering in Software](https://saliu.com/filters.html#Filters)":[25,32],"#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#I. [Introduction to Lottery Filters and Filtering in Software](https://saliu.com/filters.html#Filters)#{1}":[26,32],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>1. Introduction to Lottery Filters and Filtering in Dedicated Software</u>":[33,34],"#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).":[35,66],"#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{1}":[37,45],"#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{2}":[46,46],"#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{3}":[47,48],"#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{4}":[49,66],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>":[67,114],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{1}":[69,74],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{2}":[75,75],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{3}":[76,76],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{4}":[77,77],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{5}":[78,78],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{6}":[79,79],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{7}":[80,80],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{8}":[81,81],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{9}":[82,82],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{10}":[83,84],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{11}":[85,86],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{12}":[87,87],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{13}":[88,88],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{14}":[89,89],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{15}":[90,91],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{16}":[92,108],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{17}":[109,109],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{18}":[110,111],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{19}":[112,112],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{20}":[113,114],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>":[115,164],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#{1}":[117,118],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#{2}":[119,119],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#{3}":[120,121],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#{4}":[122,131],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.":[132,164],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{1}":[134,145],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{2}":[146,146],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{3}":[147,147],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{4}":[148,149],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{5}":[150,151],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{6}":[152,152],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{7}":[153,153],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{8}":[154,154],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{9}":[155,155],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{10}":[156,156],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{11}":[157,158],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{12}":[159,160],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{13}":[161,162],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{14}":[163,164],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>":[165,249],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#{1}":[167,183],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>":[184,249],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{1}":[186,197],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{2}":[198,198],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{3}":[199,199],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{4}":[200,201],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{5}":[202,211],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{6}":[212,213],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{7}":[214,217],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{8}":[218,218],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{9}":[219,220],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{10}":[221,221],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{11}":[222,223],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{12}":[224,241],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{13}":[242,242],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{14}":[243,243],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{15}":[244,244],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{16}":[245,245],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{17}":[246,246],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{18}":[247,247],"#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{19}":[248,249],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)":[250,292],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{1}":[252,253],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{2}":[254,254],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{3}":[255,255],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{4}":[256,256],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{5}":[257,257],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{6}":[258,258],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{7}":[259,260],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{8}":[261,262],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{9}":[263,263],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{10}":[264,264],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{11}":[265,265],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{12}":[266,266],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{13}":[267,267],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{14}":[268,268],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{15}":[269,269],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{16}":[270,270],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{17}":[271,271],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{18}":[272,272],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{19}":[273,273],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{20}":[274,274],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{21}":[275,275],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{22}":[276,276],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{23}":[277,277],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{24}":[278,278],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{25}":[279,279],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{26}":[280,280],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{27}":[281,281],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{28}":[282,282],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{29}":[283,283],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{30}":[284,284],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{31}":[285,286],"#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{32}":[287,292]},"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lottery-software-systems.gif","line":14},{"title":"Set filters in lottery software, filtering for reduction of combinations in lottery software.","target":"https://saliu.com/HLINE.gif","line":21},{"title":"Run the best lottery software, lotto software to win the lottery or lotto, based on filtering.","target":"https://saliu.com/images/lotto.gif","line":23},{"title":"Introduction to Lottery Filters and Filtering in Software","target":"https://saliu.com/filters.html#Filters","line":25},{"title":"Setting the Lottery Filters: Theory and Software Tools","target":"https://saliu.com/filters.html#Software","line":26},{"title":"More Advanced Theories in Setting Filters and Lottery Strategies","target":"https://saliu.com/filters.html#Theories","line":27},{"title":"Older Writings in Setting Filters; Lottery Software Users Creating Strategies","target":"https://saliu.com/filters.html#History","line":28},{"title":"Resources in Lotto, Lottery, Software, Filters, Strategies, Systems","target":"https://saliu.com/filters.html#Links","line":29},{"title":"Implement filtering to reduce lotto playing tickets with lottery software.","target":"https://saliu.com/images/lotto.gif","line":31},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":35},{"title":"Column sorting software: program sorts by column lottery reports to discover lotto strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":40},{"title":"_**creating, updating lottery data files of past winning numbers**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":76},{"title":"_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":80},{"title":"_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":81},{"title":"The lotto filters can eliminate huge amounts of lottery combinations or tickets, cost of play.","target":"https://saliu.com/images/lotto-filters.gif","line":85},{"title":"Create winning reports, sort them and use them for effective lottery strategies.","target":"https://saliu.com/images/strategy-sorted-reports.gif","line":94},{"title":"Study advanced methods in setting lotto filters to create winning lottery strategies.","target":"https://saliu.com/images/lottery-filter-strategies.gif","line":100},{"title":"The lottery filters are founded on theory of probability, mathematical formulas.","target":"https://saliu.com/images/filter-report.gif","line":106},{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":130},{"title":"_**rules of probability**_: _multiplication_ and _addition_ of _non-mutually exclusive_ _events:_ _**<u>Bayes Theorem</u>**_","target":"https://saliu.com/bayes-theorem.html","line":148},{"title":"Combining more lottery strategies and playing the aggregate result increase the profits.","target":"https://saliu.com/images/lotto.gif","line":150},{"title":"_**pick-4 lottery super strategy based on digit <u>frequency</u> groups**_","target":"https://saliu.com/frequency-lottery.html","line":152},{"title":"There is a balance of confidence in the number of lottery filters we set in software.","target":"https://saliu.com/images/lotto.gif","line":159},{"title":"Run lotto lottery data files, systems, strategies with filtering, filters in software.","target":"https://saliu.com/HLINE.gif","line":163},{"title":"_**UK 6-59 Lotto: Statistics, Lottery Strategy, Hottest-24 Numbers**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/_4vQk00mxzI","line":244},{"title":"_**Results Draw #1, World Lottery Championship**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/E-aM_zUsQFY","line":245},{"title":"_**Filters, Restrictions, Reduction in Lotto, Lottery Software**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/f_I6udi8raQ","line":246},{"title":"_**Lottery Software, Strategies for Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/0P7xKzncan8","line":247},{"title":"<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":250},{"title":"_**Lotto, Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":254},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":256},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, eBook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":257},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":259},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":263},{"title":"_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge","target":"https://saliu.com/bbs/messages/623.html","line":264},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":265},{"title":"_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_","target":"https://saliu.com/bbs/messages/569.html","line":266},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":267},{"title":"**Vertical or Positional** _**Filters in Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":268},{"title":"_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_","target":"https://saliu.com/bbs/messages/896.html","line":269},{"title":"**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_","target":"https://saliu.com/bbs/messages/919.html","line":270},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":271},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":272},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":273},{"title":"**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_","target":"https://saliu.com/decades.html","line":274},{"title":"_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":275},{"title":"_**Software to Generate Lotto Combinations with _Favorite Lottery Numbers in Fixed Positions_**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":276},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":277},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":278},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":279},{"title":"_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_","target":"https://saliu.com/STR30.htm","line":280},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":281},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":282},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":283},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":284},{"title":"**Lottery Software, Lotto Programs**","target":"https://saliu.com/infodown.html","line":285},{"title":"Ion Saliu lottery software, lotto software is best to win with mathematics of filtering, filters.","target":"https://saliu.com/HLINE.gif","line":287},{"title":"Forums","target":"https://forums.saliu.com/","line":289},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":289},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":289},{"title":"Contents","target":"https://saliu.com/content/index.html","line":289},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":289},{"title":"Home","target":"https://saliu.com/index.htm","line":289},{"title":"Search","target":"https://saliu.com/Search.htm","line":289},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":289},{"title":"Effective lottery software must apply filters or restrictions to the huge amounts of combinations.","target":"https://saliu.com/HLINE.gif","line":291}],"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md","metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["filters","restrictions","lottery","lotto","formula","probability","software","mathematics","theory probability","system","statistics","drawings","filters","filtering","numbers","winning","combinations"],"source":"https://saliu.com/filters.html","author":null}},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10679379,-0.04223772,-0.01401375,-0.00757724,-0.0269523,0.03297573,0.00689304,0.06351091,0.04216494,-0.01506597,-0.00863328,-0.01536184,0.08642704,0.00221487,-0.00047979,-0.02411836,-0.01970114,0.00925204,-0.06327736,-0.01854114,0.1200657,-0.01258883,-0.09623955,-0.08551262,0.06091718,-0.02017573,0.01646601,-0.05508548,-0.00055665,-0.1834517,0.00172249,0.00641621,0.00567215,-0.07024613,-0.04842416,-0.01520091,-0.00620842,0.0714893,-0.06490377,0.06930856,0.02872011,0.02206577,-0.00322169,-0.01748189,-0.00169635,-0.00019232,-0.00200746,-0.01593928,-0.01142456,-0.00790879,-0.06836975,0.00800142,0.00609621,0.04771005,0.03237696,-0.01907751,0.02799824,0.08980982,0.04163893,0.05648186,-0.00249373,0.02372175,-0.20627056,0.07896578,-0.0479699,0.02964975,-0.03129671,-0.02599479,0.01493191,0.07794022,0.01965619,0.05644673,-0.02201737,0.06724068,0.01622293,-0.02149262,-0.04622628,-0.08244939,-0.03769779,-0.00412179,-0.01054456,-0.04676158,-0.02404045,-0.02017189,0.01013401,0.02663791,0.04028509,0.03136409,0.05637624,-0.05150084,0.02115537,0.0075935,0.04478716,0.0694455,-0.02835488,0.00067475,0.01977819,0.00578218,0.0190315,0.13978751,-0.00724156,0.03230425,-0.009857,-0.00679679,0.04983728,-0.04754334,-0.00267401,-0.03778318,-0.00754671,0.04688818,0.03310683,0.01742684,0.07356313,-0.0770725,-0.02425584,0.02820823,0.03369572,0.00987031,0.04805487,-0.0026999,-0.07761235,-0.01310134,0.03078153,-0.01419638,0.04955678,0.05656199,0.01203862,0.0860836,-0.0022324,0.05067879,0.02100572,-0.00754555,-0.14054927,-0.02607456,-0.00248758,0.00240328,0.02256778,-0.04299066,-0.02890333,0.03402746,-0.06161204,-0.00906484,0.03545127,-0.09852967,-0.02780886,0.09212378,0.00790415,0.012474,-0.00810728,-0.04469795,-0.00882782,0.02386195,0.00334178,-0.05651703,-0.00200731,0.02234102,0.07582346,0.06503264,-0.05530495,0.02124104,0.01021622,-0.03406791,-0.03729117,0.14650339,-0.02745234,-0.08612899,-0.0314388,0.053097,-0.02269279,-0.0471727,-0.05876389,0.01189903,-0.0418646,0.00124434,0.10311439,0.00601035,-0.04721314,-0.03094641,-0.05059077,-0.03546386,-0.01989236,-0.01752594,-0.04147046,0.0001859,-0.05248027,-0.06064305,0.02802677,-0.01034311,0.04053297,0.06452806,-0.05389818,-0.05957891,-0.04873901,0.01814839,-0.0033614,-0.01215001,-0.00269899,-0.04180062,0.02249812,-0.00617937,-0.01837523,-0.03112801,-0.01387992,0.03591483,-0.04533716,0.05707282,0.00348546,-0.03451706,0.06802746,0.02972956,-0.02725364,0.00631901,0.05106632,0.05732583,0.00902893,0.00060568,0.00204099,0.00447469,0.00300078,-0.02154326,-0.00017999,0.01442444,-0.08741749,-0.20971549,0.0066515,-0.0397068,0.00818255,0.00077769,-0.02894613,-0.03395678,-0.04161594,0.01505988,0.15731746,0.06386101,-0.03994339,-0.01788598,0.03400323,0.00086446,-0.0390076,-0.06483012,-0.04396036,-0.06352776,0.02340484,0.02126998,0.00671976,0.02136562,-0.07542279,0.00836277,-0.01294507,0.11653555,0.03351212,0.00669331,0.04100259,0.07245458,0.0214095,-0.03461675,-0.00713545,-0.00994468,0.05285659,-0.09189325,0.01604244,0.01088569,-0.01988101,-0.03591359,0.03984657,-0.02628638,-0.10410927,-0.01010201,0.04146575,-0.005269,0.06489488,-0.00800547,0.03865854,0.04729629,-0.02266395,0.02358812,0.00688881,0.08886908,-0.03953812,-0.08994851,-0.02320383,-0.02757912,0.00262117,-0.02680288,-0.06752197,0.04086315,-0.03754597,0.02402072,0.040369,-0.01918281,-0.03403673,-0.05829756,0.00778153,0.00705405,0.08318061,-0.00160057,0.0000039,-0.05086975,0.04027358,0.02731246,-0.01579885,-0.00284077,-0.04015212,0.0470577,-0.04261094,0.04439214,0.06806621,0.05940278,0.0516906,0.02956664,-0.00602174,-0.00381941,-0.0147376,-0.01282245,0.00264061,-0.03753544,0.03653941,0.04570384,-0.02365385,-0.2621468,0.04710788,0.02672631,0.03802793,0.01048987,0.01633338,0.01480462,-0.00428147,0.00668116,-0.06719279,0.03278709,0.07348095,0.01659671,-0.01173264,-0.03712053,0.01144595,-0.02927756,-0.04699352,0.08841892,0.0511607,0.05518775,0.05086593,0.24442425,0.01601147,-0.00532975,0.00310396,0.00741968,-0.01615654,-0.06538662,0.05547551,0.0147249,-0.00942607,0.12380832,-0.00649288,-0.028241,0.04556546,-0.03719996,0.04443733,0.01250661,-0.00043334,-0.09256528,0.01078168,-0.02195539,-0.00293826,0.13233666,0.04061214,-0.07357206,-0.06437861,0.02679004,0.0461821,-0.07321877,-0.05327998,-0.03402703,-0.01625065,0.01183465,0.04577843,0.03736113,0.01337572,0.02467867,-0.01410781,0.04631095,-0.00592737,0.00646942,0.01380462,0.02850372],"last_embed":{"hash":"1e24vu5","tokens":99}}},"text":null,"length":0,"last_read":{"hash":"1e24vu5","at":1753423519207},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#---frontmatter---","lines":[1,6],"size":275,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10887986,0.00006388,-0.00861133,-0.02752064,-0.0376549,0.02919781,-0.0112401,0.04077888,0.03714317,-0.03954713,-0.00512338,-0.01696722,0.08821035,-0.00494847,0.00769337,-0.08336724,0.00948393,0.01873054,-0.06431493,-0.04465372,0.12408401,-0.03035771,-0.06220686,-0.07641426,0.04900267,-0.00737513,0.00856504,-0.06082982,-0.00594547,-0.21089436,0.03004378,-0.01340229,0.02437149,-0.04316768,-0.06354681,0.00633782,-0.00160629,0.04441895,-0.12511961,0.05997324,0.03026059,0.01043108,0.03625032,-0.04515342,0.01458424,-0.02456407,0.0337994,0.00575327,0.01399212,-0.02086805,-0.07199418,0.01729795,-0.00255121,0.04459802,0.03452954,0.01493069,0.03262216,0.11980005,0.02459929,0.04919714,0.00141258,0.0498881,-0.18978922,0.09191705,-0.03753415,0.04949543,-0.00741832,-0.01457853,0.03458643,0.05673151,0.0075686,0.06900205,-0.02587535,0.03584196,0.00374764,-0.06362719,-0.04211584,-0.0395412,-0.03938428,0.00777761,-0.02378671,-0.02779995,0.00019222,-0.02608392,0.02462447,0.04185058,0.02715448,-0.02226019,0.04979071,-0.07630381,0.00255569,-0.00113537,0.02824846,0.05222727,0.02324997,0.00860662,0.02750173,0.00467561,0.02556375,0.14546372,0.002685,0.01439314,-0.00630942,-0.00852593,0.0563555,-0.05361732,0.00465495,-0.05813269,0.01615993,0.04477663,0.03364863,0.0005485,0.05416106,-0.0160126,-0.05254687,0.01971593,0.00340115,0.01645544,0.04096292,0.01613025,-0.05315842,-0.0133078,0.01987813,-0.0255549,0.03617379,0.00781041,0.00556437,0.06569383,-0.00541199,0.04600924,0.02534021,-0.00199547,-0.1472367,-0.05516779,-0.00671886,0.02055137,-0.00239229,-0.0240641,-0.05395963,0.05052116,-0.05637961,-0.03578825,0.03876261,-0.09177311,-0.0148535,0.07352328,-0.00846545,0.00013714,-0.03843437,-0.02271993,0.02820587,0.0132819,-0.03326701,-0.05165922,-0.01412935,0.03102197,0.07744779,0.0636951,-0.0837319,0.01309332,-0.01252477,-0.01738886,-0.02821532,0.11426245,-0.01522253,-0.06498655,-0.01766961,0.05302388,-0.04247271,-0.04784519,-0.07860836,0.01872931,-0.0534501,-0.01244504,0.07014671,-0.01284628,-0.08163259,-0.04385958,-0.04787923,-0.01832275,0.00161776,-0.02921583,-0.00851374,-0.00117744,-0.02444182,-0.08068213,0.04806945,-0.00644037,0.03324146,0.04963969,-0.0621424,-0.01389952,-0.03373832,-0.02018346,-0.01982529,-0.00697421,-0.01169364,-0.0449031,0.03161479,0.00908037,-0.00698738,0.00367978,0.02690487,0.06655104,-0.05294411,0.06021865,-0.010746,-0.03634594,0.08422813,0.02605782,-0.03982384,0.01077379,0.04069461,0.07347428,0.01373271,0.01669873,0.00797881,-0.0009117,-0.00124846,0.00996381,0.03203197,0.03101518,-0.1003437,-0.19177017,-0.00844579,-0.04715521,0.00901545,0.02283329,-0.04581149,-0.02569911,-0.05995237,0.02069372,0.12168159,0.08006981,-0.05870585,0.01536841,0.08568123,-0.0025786,-0.01382653,-0.07276846,-0.01016548,-0.09050333,-0.00108525,0.01585976,0.00705579,0.03029883,-0.07158732,0.01595996,-0.02565458,0.10786524,0.01733563,0.03043156,0.03301442,0.05905397,-0.00012843,-0.02975811,-0.00870657,-0.02003623,0.0596166,-0.05270809,-0.02796814,0.00426019,0.00631744,-0.03092125,0.04205329,-0.0201849,-0.11693525,-0.02771289,0.05703903,-0.01963845,0.06802568,0.01463694,0.0535612,0.07587729,-0.01696013,0.02786751,0.00343324,0.07759221,-0.06823994,-0.09595298,-0.01856367,-0.0133191,0.02834777,-0.03262449,-0.07236961,0.03979973,-0.03071642,0.07326243,0.04447576,-0.01671005,0.00150327,-0.02399871,0.01671378,-0.02329454,0.08324697,-0.00608406,0.02979145,-0.02670554,0.04996908,0.04483714,-0.01675635,-0.04649784,-0.04201409,0.0449424,-0.03342954,0.04213895,0.0663145,0.0453378,0.03248121,0.05754173,0.00563054,-0.02509073,-0.02888016,0.00297879,0.01024741,-0.02741874,0.06944805,0.01862945,0.01122512,-0.25714892,0.02460394,0.01111443,0.03085525,-0.00385706,0.01824356,0.03315481,-0.01265666,0.04756152,-0.09440456,0.01661541,0.04231456,0.03084708,-0.02603107,-0.03615871,-0.0005111,-0.00589444,-0.05580344,0.10567256,0.03106795,0.05378575,0.05365498,0.23417589,-0.02441188,-0.00856413,0.00843171,0.01662714,-0.02899981,-0.06058675,0.05719498,0.01849153,-0.00611686,0.1062431,0.01509374,-0.03466352,0.01187693,-0.0382418,0.03463389,0.01354472,-0.01367125,-0.06001188,0.01267689,-0.02064561,-0.02805911,0.13203521,0.01686229,-0.05452437,-0.05870135,0.04866523,0.07180504,-0.06861217,-0.04449531,-0.0350497,-0.01123697,0.04190003,0.02194107,-0.00382409,0.02113975,0.00286074,-0.01562889,0.04585193,0.00539221,0.00289224,0.02453591,0.02175505],"last_embed":{"hash":"rne8eb","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"rne8eb","at":1753423519250},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software","lines":[8,292],"size":45675,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lottery-software-systems.gif","line":7},{"title":"Set filters in lottery software, filtering for reduction of combinations in lottery software.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Run the best lottery software, lotto software to win the lottery or lotto, based on filtering.","target":"https://saliu.com/images/lotto.gif","line":16},{"title":"Introduction to Lottery Filters and Filtering in Software","target":"https://saliu.com/filters.html#Filters","line":18},{"title":"Setting the Lottery Filters: Theory and Software Tools","target":"https://saliu.com/filters.html#Software","line":19},{"title":"More Advanced Theories in Setting Filters and Lottery Strategies","target":"https://saliu.com/filters.html#Theories","line":20},{"title":"Older Writings in Setting Filters; Lottery Software Users Creating Strategies","target":"https://saliu.com/filters.html#History","line":21},{"title":"Resources in Lotto, Lottery, Software, Filters, Strategies, Systems","target":"https://saliu.com/filters.html#Links","line":22},{"title":"Implement filtering to reduce lotto playing tickets with lottery software.","target":"https://saliu.com/images/lotto.gif","line":24},{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":28},{"title":"Column sorting software: program sorts by column lottery reports to discover lotto strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":33},{"title":"_**creating, updating lottery data files of past winning numbers**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":69},{"title":"_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":73},{"title":"_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":74},{"title":"The lotto filters can eliminate huge amounts of lottery combinations or tickets, cost of play.","target":"https://saliu.com/images/lotto-filters.gif","line":78},{"title":"Create winning reports, sort them and use them for effective lottery strategies.","target":"https://saliu.com/images/strategy-sorted-reports.gif","line":87},{"title":"Study advanced methods in setting lotto filters to create winning lottery strategies.","target":"https://saliu.com/images/lottery-filter-strategies.gif","line":93},{"title":"The lottery filters are founded on theory of probability, mathematical formulas.","target":"https://saliu.com/images/filter-report.gif","line":99},{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":123},{"title":"_**rules of probability**_: _multiplication_ and _addition_ of _non-mutually exclusive_ _events:_ _**<u>Bayes Theorem</u>**_","target":"https://saliu.com/bayes-theorem.html","line":141},{"title":"Combining more lottery strategies and playing the aggregate result increase the profits.","target":"https://saliu.com/images/lotto.gif","line":143},{"title":"_**pick-4 lottery super strategy based on digit <u>frequency</u> groups**_","target":"https://saliu.com/frequency-lottery.html","line":145},{"title":"There is a balance of confidence in the number of lottery filters we set in software.","target":"https://saliu.com/images/lotto.gif","line":152},{"title":"Run lotto lottery data files, systems, strategies with filtering, filters in software.","target":"https://saliu.com/HLINE.gif","line":156},{"title":"_**UK 6-59 Lotto: Statistics, Lottery Strategy, Hottest-24 Numbers**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/_4vQk00mxzI","line":237},{"title":"_**Results Draw #1, World Lottery Championship**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/E-aM_zUsQFY","line":238},{"title":"_**Filters, Restrictions, Reduction in Lotto, Lottery Software**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/f_I6udi8raQ","line":239},{"title":"_**Lottery Software, Strategies for Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/0P7xKzncan8","line":240},{"title":"<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":243},{"title":"_**Lotto, Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":247},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":249},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, eBook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":250},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":252},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":256},{"title":"_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge","target":"https://saliu.com/bbs/messages/623.html","line":257},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":258},{"title":"_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_","target":"https://saliu.com/bbs/messages/569.html","line":259},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":260},{"title":"**Vertical or Positional** _**Filters in Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":261},{"title":"_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_","target":"https://saliu.com/bbs/messages/896.html","line":262},{"title":"**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_","target":"https://saliu.com/bbs/messages/919.html","line":263},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":264},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":265},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":266},{"title":"**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_","target":"https://saliu.com/decades.html","line":267},{"title":"_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":268},{"title":"_**Software to Generate Lotto Combinations with _Favorite Lottery Numbers in Fixed Positions_**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":269},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":270},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":271},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":272},{"title":"_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_","target":"https://saliu.com/STR30.htm","line":273},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":274},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":275},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":276},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":277},{"title":"**Lottery Software, Lotto Programs**","target":"https://saliu.com/infodown.html","line":278},{"title":"Ion Saliu lottery software, lotto software is best to win with mathematics of filtering, filters.","target":"https://saliu.com/HLINE.gif","line":280},{"title":"Forums","target":"https://forums.saliu.com/","line":282},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":282},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":282},{"title":"Contents","target":"https://saliu.com/content/index.html","line":282},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":282},{"title":"Home","target":"https://saliu.com/index.htm","line":282},{"title":"Search","target":"https://saliu.com/Search.htm","line":282},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":282},{"title":"Effective lottery software must apply filters or restrictions to the huge amounts of combinations.","target":"https://saliu.com/HLINE.gif","line":284}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09716284,-0.00233528,-0.00739936,-0.02808892,-0.04932138,0.03468764,-0.00514906,0.02178765,0.03882493,-0.03327528,-0.00123787,-0.01128445,0.09857487,-0.003274,0.00179597,-0.07945651,0.01574693,0.00901325,-0.07213413,-0.04890027,0.12116554,-0.03285391,-0.06606113,-0.0699078,0.04321651,-0.01617275,0.00294908,-0.05418183,0.00016493,-0.20200779,0.03426067,-0.00913026,0.03013668,-0.06122091,-0.06158055,0.00420097,-0.0096828,0.04911548,-0.13003851,0.06843773,0.03858423,0.01650469,0.02514791,-0.03757105,0.02097651,-0.03714454,0.02794108,0.00642919,0.03679807,-0.0260818,-0.04936082,0.0241766,0.01177691,0.05103053,0.03960591,0.00230437,0.04354899,0.1126004,0.03435988,0.05270425,-0.00158847,0.0561901,-0.17409851,0.08145561,-0.02600092,0.05187216,0.01276043,-0.02410275,0.03285319,0.05800785,0.02410838,0.06754921,-0.02231481,0.03781152,0.0078822,-0.06123435,-0.05531603,-0.04281045,-0.04362373,0.01270027,-0.02931651,-0.05248547,0.00265233,-0.02597842,0.02252811,0.04489657,0.02955558,0.00238276,0.05306981,-0.06667895,0.00110737,-0.00337526,0.03668228,0.05293877,0.00749207,0.00755653,0.03425914,0.00273077,0.0313123,0.16150331,-0.00060643,0.00852593,-0.00197069,-0.02232103,0.04597238,-0.05383918,0.00413547,-0.05228543,0.00779397,0.05126753,0.02651283,-0.0001372,0.07063515,-0.0119502,-0.04694699,0.01221964,-0.0017218,0.01292641,0.03771141,0.01970741,-0.04627952,-0.00691085,0.03227933,-0.02482216,0.03105172,0.01214181,0.01383554,0.06944819,0.00129721,0.04697933,0.00706159,-0.0140572,-0.14380865,-0.05692762,0.00606251,0.01259666,0.00770057,-0.02437609,-0.04669913,0.04266729,-0.06255125,-0.02523072,0.03890386,-0.10518822,-0.00894827,0.06963368,0.00476793,-0.00239893,-0.03211591,-0.01975778,0.02545847,0.01252963,-0.04138226,-0.05649584,-0.01304513,0.03623576,0.07022215,0.0505443,-0.07121594,-0.00542579,-0.0123003,-0.01418386,-0.02593267,0.12607683,-0.02987899,-0.0768123,-0.0165261,0.04190627,-0.03609557,-0.04813803,-0.07492001,0.02580735,-0.06893,-0.01075274,0.07371306,-0.01022392,-0.07271127,-0.04662544,-0.06351817,-0.02716094,-0.00250801,-0.03016658,-0.00483593,-0.0032825,-0.03696635,-0.08658078,0.03971176,-0.01120426,0.045471,0.05385093,-0.05310893,-0.0094727,-0.03514718,-0.02268638,-0.02410079,-0.0015192,-0.02172951,-0.04924557,0.0365719,-0.01171903,0.00841218,0.00803767,0.02740074,0.07669162,-0.03538996,0.06493352,-0.02168581,-0.0338988,0.08498622,0.02733709,-0.05247103,0.00692035,0.05010791,0.08058137,-0.00582451,0.02012107,0.00934623,-0.00844954,-0.00858175,0.00231473,0.03270905,0.02032342,-0.08672303,-0.20310874,0.00224725,-0.04128421,0.01113956,0.00936822,-0.04512737,-0.02189112,-0.05341605,0.01798833,0.12030639,0.06802017,-0.06913684,0.00679749,0.08654046,-0.00553596,-0.00909361,-0.06836226,-0.02130879,-0.07474975,0.00488782,0.0305368,0.0064583,0.00914914,-0.08040322,0.03205841,-0.03240539,0.11078098,0.01129103,0.03450545,0.0242158,0.06690943,-0.01017708,-0.02668897,0.00252542,-0.02674875,0.05104889,-0.04970679,-0.04074731,0.00575967,0.00465635,-0.04067073,0.01672451,-0.02702849,-0.1169199,-0.0014775,0.04936343,-0.02294037,0.06791015,0.01209541,0.05295502,0.06062845,-0.01291275,0.02890198,0.01450642,0.09471812,-0.05040948,-0.08516471,-0.01456026,-0.00899192,0.02606932,-0.01906651,-0.0791194,0.03870422,-0.03602031,0.06196824,0.04514118,-0.01653768,0.00299027,-0.00751933,0.00514981,-0.02037472,0.07406954,0.00711335,0.0276429,-0.0418326,0.05172017,0.05079004,-0.00703192,-0.04230805,-0.05317229,0.04622093,-0.02881955,0.04225602,0.06916392,0.04422133,0.03772406,0.05566641,0.00392436,-0.03227144,-0.0275173,-0.00132905,0.00935161,-0.03363615,0.07220959,0.01416195,0.00635354,-0.25404996,0.02882998,0.01163092,0.04011782,-0.00248863,0.02142616,0.02234897,-0.00215499,0.04574508,-0.08610319,0.01906277,0.0336189,0.02854365,-0.02230467,-0.0272027,-0.00055517,0.00993963,-0.05442014,0.11309812,0.03313014,0.06277626,0.04377345,0.23273326,-0.01931216,-0.00854159,0.00171101,0.00553537,-0.01666738,-0.05882871,0.05419869,0.03152578,0.00161648,0.09940262,0.00615411,-0.0255897,0.01196125,-0.03531748,0.04488973,0.00078923,-0.00358254,-0.07269322,0.00373299,-0.0324967,-0.03691301,0.12888294,0.02192013,-0.05631321,-0.07205516,0.02760203,0.06870441,-0.07734568,-0.04818738,-0.03618935,-0.0169145,0.04936691,0.02222757,-0.00602764,0.0121459,0.00371698,-0.02025357,0.04104706,0.01946766,0.00368319,0.01289735,0.00932169],"last_embed":{"hash":"n9xu4r","tokens":138}}},"text":null,"length":0,"last_read":{"hash":"n9xu4r","at":1753423519487},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#{1}","lines":[10,15],"size":406,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lottery-software-systems.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10624039,-0.00003247,-0.0089042,-0.01012812,-0.01595629,0.03372508,0.00501564,0.0560598,0.01725194,-0.04330992,-0.00790012,-0.02480714,0.08308961,-0.01166853,0.00652649,-0.06731143,0.00478931,0.03471711,-0.05604877,-0.03426,0.10963123,-0.02284139,-0.06343217,-0.07626197,0.05800219,-0.01892848,-0.00083084,-0.06286318,-0.01269326,-0.19296828,0.02691487,-0.00956385,0.0125435,-0.04170623,-0.0656954,-0.00017184,0.00471578,0.05571377,-0.12228304,0.05842232,0.02632954,-0.00415662,0.03818569,-0.04291456,0.01235067,-0.00617838,0.03993042,-0.00717486,0.01088272,-0.0122356,-0.0827277,0.01660915,-0.01416871,0.01815929,0.03060288,0.01665832,0.02469129,0.12764837,0.01251538,0.02964215,0.01148656,0.04080881,-0.20013303,0.08963168,-0.05010393,0.0403523,-0.01680634,-0.01643099,0.01373699,0.0714764,0.00379216,0.04130154,-0.02787336,0.04242251,0.01173564,-0.06843128,-0.03864687,-0.03690272,-0.03159675,-0.00965845,-0.0277323,-0.0193937,-0.00347602,-0.01318597,0.03255459,0.04362698,0.03103687,-0.01174379,0.0492451,-0.09576485,0.01722122,0.0036999,0.02451427,0.05254939,0.0191893,0.00232916,0.00800339,-0.00132799,0.01271696,0.15127213,-0.00454192,0.00407426,-0.01583904,0.00356602,0.06442135,-0.05044083,0.00293487,-0.0448392,0.01638355,0.04951669,0.04001244,0.01184937,0.0523869,-0.02692286,-0.05250911,0.02727018,0.00257363,0.02486486,0.02454153,0.02623475,-0.05571565,-0.01496438,0.01977979,-0.01075597,0.04062885,0.00739716,-0.00312318,0.06406352,-0.01076278,0.04593172,0.0332992,-0.00212469,-0.15255301,-0.04717933,-0.02550283,0.0197909,-0.0128843,-0.03621084,-0.05589282,0.05396628,-0.06074617,-0.03353438,0.04508345,-0.07895133,-0.01552435,0.07749409,-0.02445698,0.00351067,-0.02620405,-0.02472437,0.01585908,0.01359434,-0.02369509,-0.05045665,-0.01489847,0.01913443,0.07787883,0.07366108,-0.08896542,0.03293876,-0.00958611,-0.01950214,-0.02285786,0.12063797,-0.01514303,-0.05233928,-0.03543697,0.06140584,-0.03791841,-0.03958512,-0.07081036,0.01765082,-0.04613168,-0.00835565,0.08502936,-0.01156283,-0.08015044,-0.04110524,-0.04855605,-0.02233928,0.01468839,-0.03575997,-0.01772451,-0.00672081,-0.01295269,-0.06972925,0.06110284,-0.01045415,0.033254,0.05365945,-0.06674062,-0.01432078,-0.03160001,-0.01622101,-0.00172774,-0.00075549,-0.01736869,-0.04217296,0.03115588,0.02970265,-0.021513,-0.00300485,0.02387358,0.05165695,-0.06480149,0.0774134,-0.00417645,-0.04096026,0.07893398,0.03445297,-0.04744821,0.02055022,0.03817727,0.07303362,0.01774272,-0.00044442,0.01389646,0.01156501,-0.00775144,0.00188497,0.03160691,0.04240701,-0.11176105,-0.18811873,-0.01611789,-0.0578435,0.00169238,0.03212933,-0.0425931,-0.0286457,-0.04830712,0.03500802,0.13194723,0.08743945,-0.0510082,0.01335904,0.07774328,-0.00507222,-0.00801704,-0.06521177,-0.01840202,-0.07615163,-0.00354106,-0.00405892,0.00296088,0.03755146,-0.05284218,0.0139929,-0.01295638,0.10516957,0.03402877,0.03460544,0.03250147,0.0588541,0.02055334,-0.03030371,-0.02236177,-0.01986375,0.06056114,-0.06425413,-0.01090982,0.004153,-0.00021602,-0.02203613,0.055044,-0.01508299,-0.11872776,-0.02687786,0.06295105,-0.02047984,0.0720873,0.01612846,0.04495192,0.08839617,0.00074978,0.00601603,0.01108051,0.07748727,-0.06939977,-0.09599173,-0.01906083,-0.01941236,0.01893158,-0.04679058,-0.06046587,0.03028594,-0.03203155,0.06476467,0.04470539,-0.01493423,-0.01975982,-0.03020361,0.02043759,-0.01677789,0.07156692,-0.02341585,0.03334362,-0.03199199,0.05829359,0.03839758,-0.0309059,-0.05991619,-0.03478675,0.05159441,-0.04643959,0.04974717,0.05802814,0.04458804,0.04709092,0.05339463,-0.00120566,-0.02918478,-0.02601162,0.00223387,0.00825068,-0.02087378,0.05601761,0.01753675,-0.01339238,-0.25097498,0.03161196,0.00106314,0.02926859,-0.01571857,0.0145576,0.04023863,-0.01435589,0.03987634,-0.09256732,0.02170522,0.04709632,0.02089622,-0.02534754,-0.03286539,0.00800785,-0.0182292,-0.06098524,0.09688374,0.03286455,0.05532615,0.05595956,0.23231059,-0.00840847,-0.00061089,0.00477558,0.00927244,-0.02597444,-0.07642737,0.06278262,0.01222751,-0.01667072,0.10304762,0.01833486,-0.03095759,0.01762514,-0.03077102,0.03076174,0.02635336,-0.00120598,-0.06901496,0.003794,-0.02941016,-0.01715202,0.13657039,0.00894588,-0.06166562,-0.04184419,0.05733736,0.0683652,-0.07205234,-0.05853254,-0.03412577,-0.0113663,0.02235504,0.03966159,0.00865322,0.01421419,0.00482401,-0.00317369,0.05059771,-0.00266368,-0.0039379,0.01154013,0.0218292],"last_embed":{"hash":"1oge1fh","tokens":303}}},"text":null,"length":0,"last_read":{"hash":"1oge1fh","at":1753423519545},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_","lines":[19,32],"size":1000,"outlinks":[{"title":"Set filters in lottery software, filtering for reduction of combinations in lottery software.","target":"https://saliu.com/HLINE.gif","line":3},{"title":"Run the best lottery software, lotto software to win the lottery or lotto, based on filtering.","target":"https://saliu.com/images/lotto.gif","line":5},{"title":"Introduction to Lottery Filters and Filtering in Software","target":"https://saliu.com/filters.html#Filters","line":7},{"title":"Setting the Lottery Filters: Theory and Software Tools","target":"https://saliu.com/filters.html#Software","line":8},{"title":"More Advanced Theories in Setting Filters and Lottery Strategies","target":"https://saliu.com/filters.html#Theories","line":9},{"title":"Older Writings in Setting Filters; Lottery Software Users Creating Strategies","target":"https://saliu.com/filters.html#History","line":10},{"title":"Resources in Lotto, Lottery, Software, Filters, Strategies, Systems","target":"https://saliu.com/filters.html#Links","line":11},{"title":"Implement filtering to reduce lotto playing tickets with lottery software.","target":"https://saliu.com/images/lotto.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09638986,0.00065319,-0.01274541,-0.00788866,-0.01750392,0.04122907,0.0239154,0.04311341,0.00865787,-0.03505628,-0.0022213,-0.03254993,0.09689717,-0.01171667,-0.0028496,-0.06924191,0.01056473,0.03798438,-0.07000011,-0.04019926,0.10621691,-0.02524209,-0.06535374,-0.0792105,0.05130864,-0.03282242,-0.0067149,-0.06036687,-0.0209962,-0.18607534,0.01843288,-0.00573915,0.00958116,-0.05496138,-0.06246084,-0.00631495,-0.00104598,0.05925112,-0.12699156,0.05989201,0.03713016,-0.00960437,0.03436702,-0.0423501,0.0079876,-0.01500637,0.03765718,-0.00914551,0.03655104,-0.00449342,-0.06410179,0.02662305,0.0022368,0.02208148,0.02992116,0.00967402,0.01787843,0.12511601,0.01149054,0.03371418,0.00879173,0.03705872,-0.18061763,0.06830015,-0.05321962,0.02658859,0.00311508,-0.04321057,-0.00319841,0.06347454,0.02421177,0.03457462,-0.0343117,0.04429767,0.0184336,-0.06079634,-0.03693182,-0.04478981,-0.03374905,-0.00619478,-0.03347581,-0.03936966,-0.00073855,-0.00933443,0.03417684,0.05504341,0.03413472,0.00164464,0.05096437,-0.09667702,0.01313417,0.00241743,0.02976357,0.05841248,0.01640891,-0.00661905,0.00603861,-0.0066042,0.01666606,0.16035978,-0.01450267,-0.00329269,-0.01461392,-0.00976806,0.05993542,-0.05417456,-0.00340273,-0.02812365,0.0054392,0.04994141,0.03955832,0.00345585,0.07064205,-0.03673946,-0.06124784,0.02244101,-0.00095934,0.00861162,0.02887331,0.03044463,-0.05543727,0.00116532,0.02604973,-0.01260177,0.03761309,0.03081449,0.003013,0.06176815,-0.01038497,0.05001723,0.03044572,-0.01116064,-0.14720222,-0.04843694,-0.0283147,0.01438822,-0.0104589,-0.0447064,-0.05714867,0.03632684,-0.06569538,-0.01941837,0.04960404,-0.07424256,-0.01162712,0.06583186,-0.03278342,0.01160112,-0.0214837,-0.03426692,0.00786908,0.0180951,-0.02353979,-0.05029387,-0.00477452,0.02112936,0.07893237,0.07347771,-0.07573356,0.02695126,-0.00691357,-0.00994616,-0.02904496,0.12338847,-0.02709754,-0.04726299,-0.04259936,0.05428875,-0.04419098,-0.02767625,-0.06106777,0.02726006,-0.04794579,0.00170117,0.0935917,-0.02243347,-0.07842442,-0.04647626,-0.07180109,-0.03368111,0.00093491,-0.04062019,-0.02136564,-0.0172057,-0.01383279,-0.07532753,0.06845066,-0.01817444,0.04282706,0.05274602,-0.0501908,0.01690763,-0.03927155,-0.01049951,0.00832135,0.00379277,-0.03267539,-0.03835992,0.02944844,0.00766907,-0.02114334,0.00685924,0.03130019,0.05633042,-0.05081359,0.07481182,-0.02177905,-0.03061051,0.07497425,0.04809494,-0.05476905,0.01786588,0.0413729,0.07795586,0.0030081,0.0010613,0.02277707,0.00588929,-0.01379934,-0.00148114,0.02937203,0.04081829,-0.09902228,-0.19758344,-0.0163711,-0.05956258,0.00803556,0.03485459,-0.05223792,-0.02883415,-0.04081928,0.03985191,0.1319035,0.09543811,-0.04900096,0.00848087,0.08513667,-0.00622483,0.01122407,-0.04874479,-0.01653722,-0.05846102,-0.00500724,0.00433086,-0.00509078,0.03122228,-0.04546848,0.02481631,-0.0048173,0.10379401,0.03622505,0.03661768,0.02998629,0.06515957,0.01947679,-0.01800831,-0.02759745,-0.01424993,0.05461243,-0.06794002,-0.01041555,-0.00799034,-0.00329856,-0.02529334,0.04354947,-0.00409206,-0.1282454,0.0053455,0.05044363,-0.0220748,0.06843565,0.00119598,0.03619454,0.09032492,0.00757358,-0.00154077,0.01261898,0.08664861,-0.05537925,-0.08170094,-0.03299688,-0.01561009,0.0083473,-0.0339641,-0.06434148,0.03360738,-0.03629295,0.05344579,0.0473065,-0.01081606,-0.0190382,-0.00570173,0.02092255,-0.01463527,0.06910426,-0.02589555,0.03937105,-0.04176735,0.06634379,0.05070614,-0.03582606,-0.05209948,-0.04419037,0.06461947,-0.04701238,0.05150744,0.05733545,0.0447689,0.05459838,0.05229169,0.01389153,-0.0352249,-0.02245034,0.00184241,0.01543284,-0.02205403,0.0505134,0.00497324,-0.0214565,-0.25225365,0.03722919,-0.01793954,0.03734531,-0.02479078,0.00008929,0.02423368,-0.00749945,0.03120941,-0.07798421,0.02052936,0.0393367,0.0325497,-0.04061154,-0.02044524,0.00909781,-0.02155615,-0.06435026,0.10587548,0.03705904,0.06137602,0.05301078,0.24083038,-0.00672169,0.00747222,-0.00384611,0.00426826,-0.02765994,-0.08070643,0.06225226,0.02682252,-0.01364251,0.09235025,0.01526819,-0.02007621,0.03463837,-0.03354623,0.03596077,0.01412527,0.00377242,-0.06494366,-0.00531063,-0.04745446,-0.0221872,0.12981664,0.00709523,-0.05820875,-0.0309045,0.0499099,0.06780791,-0.07079201,-0.06562156,-0.04213822,-0.02267524,0.01275311,0.03739312,0.01425393,0.00313253,0.00178353,-0.00932988,0.0479989,0.01685626,0.00600343,-0.00229591,0.01383206],"last_embed":{"hash":"1ecidhz","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"1ecidhz","at":1753423519651},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#{1}","lines":[21,24],"size":261,"outlinks":[{"title":"Set filters in lottery software, filtering for reduction of combinations in lottery software.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Run the best lottery software, lotto software to win the lottery or lotto, based on filtering.","target":"https://saliu.com/images/lotto.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#I. [Introduction to Lottery Filters and Filtering in Software](https://saliu.com/filters.html#Filters)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11411731,-0.00386905,-0.01102254,-0.01823399,-0.0196548,0.03221247,0.0131747,0.05753323,0.02945346,-0.0480071,0.00381982,-0.02086526,0.07339394,-0.0061937,0.01845036,-0.06320482,-0.00122353,0.02491641,-0.05330911,-0.03587041,0.1074889,-0.01979535,-0.06280365,-0.06908239,0.06055784,-0.0106197,0.0110412,-0.05426172,0.00143564,-0.19039218,0.02760992,-0.00377775,0.02457608,-0.04438081,-0.07246128,-0.01467581,0.0095666,0.05230183,-0.11149792,0.0580521,0.02874362,-0.01778375,0.03806253,-0.04187399,0.01141951,-0.00107574,0.0504025,-0.00700262,0.00810919,-0.01862214,-0.07926488,0.01507371,-0.01700721,0.02256226,0.02967213,0.01368751,0.03126625,0.12670721,0.02376619,0.02747186,0.00318958,0.01683402,-0.21565181,0.10040333,-0.04768587,0.04341569,-0.02653747,-0.0102649,0.01373035,0.05197838,-0.00217363,0.03980134,-0.01773688,0.04564722,0.01640279,-0.07157688,-0.0415613,-0.03351203,-0.02883278,-0.00667094,-0.01960498,-0.02560786,-0.0027092,-0.01150511,0.02482409,0.04305123,0.03979181,-0.01873703,0.0443827,-0.09161373,0.01226363,0.00072927,0.02254848,0.06054216,0.01305809,0.01078307,0.00137637,0.00599497,0.0022618,0.14651495,0.01947805,0.01385912,-0.01638758,0.01048828,0.06039679,-0.03840161,0.00318786,-0.04875007,0.02980763,0.04519292,0.02888556,0.0211631,0.03367461,-0.02653193,-0.03890336,0.01978118,0.00211104,0.02872521,0.02864745,0.02208677,-0.05392475,-0.02994411,0.01730501,-0.01258264,0.04101059,0.01329078,-0.00620768,0.06827772,-0.00418807,0.04867808,0.02953658,-0.00915359,-0.14619896,-0.05126264,-0.01990164,0.02802134,-0.00997384,-0.05065465,-0.04956837,0.07504675,-0.05177027,-0.03222953,0.03169647,-0.07602391,-0.02272496,0.0780479,-0.00572957,-0.00267338,-0.01923903,-0.01768024,0.01872915,0.01591769,-0.02283095,-0.04337896,-0.02150427,0.01554926,0.06808792,0.07411353,-0.08118789,0.03708827,-0.01797924,-0.02825729,-0.02643524,0.13129264,-0.01165074,-0.04774242,-0.03250213,0.05050173,-0.02442474,-0.04000377,-0.0681299,0.00623957,-0.04274213,-0.00539593,0.08500071,-0.00664193,-0.07788353,-0.04842323,-0.06293502,-0.01037533,0.00867877,-0.04481885,-0.02025401,0.00533585,-0.01986814,-0.06991073,0.06097122,-0.00479224,0.02981693,0.06080274,-0.06414187,-0.00751328,-0.03138651,-0.01707852,-0.00699306,-0.00560122,-0.00242894,-0.04325451,0.0218022,0.02094143,-0.02615115,-0.00033758,0.01462148,0.05635257,-0.07255116,0.07238956,0.00897908,-0.02211357,0.08244075,0.03415789,-0.05108053,0.01934689,0.02802922,0.06018636,0.0254528,0.00060443,0.01260537,0.00499396,0.00689283,0.0041414,0.03426354,0.04554885,-0.11407919,-0.19145122,-0.00725998,-0.05487646,-0.00026162,0.0248543,-0.04772989,-0.0287606,-0.05294201,0.03533065,0.11960585,0.08165585,-0.04500481,0.02741846,0.07594794,-0.00576235,-0.01113503,-0.05667468,-0.02854995,-0.07445684,-0.00567012,-0.00919985,0.00663927,0.04470854,-0.05390086,0.00304369,-0.02396531,0.11474967,0.02771894,0.0321181,0.03485231,0.05704598,0.02088243,-0.03388174,-0.02356883,-0.03426884,0.0629353,-0.07052533,0.00062623,0.00794531,-0.00710414,-0.02574495,0.05827196,-0.02647444,-0.09768867,-0.0401333,0.06778277,-0.01094577,0.0766087,0.01805001,0.03681317,0.07770035,-0.00600262,0.01685398,0.01503933,0.07426459,-0.07880071,-0.09385884,-0.01218335,-0.02294156,0.01980797,-0.05753726,-0.04476194,0.02405163,-0.03381594,0.05721106,0.04592529,-0.01982973,-0.02593455,-0.03667093,0.01867289,-0.02030029,0.0787058,-0.02561907,0.02051524,-0.04352858,0.04002829,0.02117148,-0.03420482,-0.05293205,-0.02856365,0.05073136,-0.04501651,0.04978071,0.04607164,0.04877151,0.04841146,0.04445512,-0.01236173,-0.03364993,-0.02906047,-0.00689347,0.01392405,-0.02259754,0.06030969,0.02378242,-0.01465561,-0.25470239,0.02828632,-0.00342969,0.03963871,-0.0003779,0.02887145,0.04151782,-0.01898879,0.05183243,-0.08970778,0.02823866,0.04789864,0.01929718,-0.00717581,-0.04822036,0.01311216,-0.01949718,-0.06434664,0.08721723,0.0229056,0.05526656,0.05627465,0.23673648,-0.00909201,-0.01150657,0.0120901,0.00640423,-0.03824799,-0.0894724,0.07100507,0.01318098,-0.02891665,0.11199253,0.02191812,-0.02537927,0.01752914,-0.03962927,0.03052826,0.03396446,-0.00511475,-0.06689018,-0.00757702,-0.0324409,-0.01512074,0.13146949,0.02017003,-0.06772394,-0.04165082,0.06483178,0.07193061,-0.06633515,-0.04205849,-0.03483164,0.00303854,0.02118,0.03810617,-0.00233046,0.02277078,0.00301312,-0.00827035,0.0543487,-0.00372982,-0.01208128,0.01221038,0.03250822],"last_embed":{"hash":"ueats4","tokens":252}}},"text":null,"length":0,"last_read":{"hash":"ueats4","at":1753423519690},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#I. [Introduction to Lottery Filters and Filtering in Software](https://saliu.com/filters.html#Filters)","lines":[25,32],"size":680,"outlinks":[{"title":"Introduction to Lottery Filters and Filtering in Software","target":"https://saliu.com/filters.html#Filters","line":1},{"title":"Setting the Lottery Filters: Theory and Software Tools","target":"https://saliu.com/filters.html#Software","line":2},{"title":"More Advanced Theories in Setting Filters and Lottery Strategies","target":"https://saliu.com/filters.html#Theories","line":3},{"title":"Older Writings in Setting Filters; Lottery Software Users Creating Strategies","target":"https://saliu.com/filters.html#History","line":4},{"title":"Resources in Lotto, Lottery, Software, Filters, Strategies, Systems","target":"https://saliu.com/filters.html#Links","line":5},{"title":"Implement filtering to reduce lotto playing tickets with lottery software.","target":"https://saliu.com/images/lotto.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#I. [Introduction to Lottery Filters and Filtering in Software](https://saliu.com/filters.html#Filters)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11205535,-0.00578287,-0.01016806,-0.01803738,-0.02251824,0.03226704,0.01155334,0.05469544,0.03049031,-0.0454812,0.00239326,-0.02371458,0.07030009,0.00028257,0.02343883,-0.06345326,-0.00194669,0.02669467,-0.05259153,-0.03449953,0.11228119,-0.01525715,-0.06376012,-0.07007819,0.05868842,-0.00737597,0.01497678,-0.05378375,0.00160507,-0.18918821,0.02218529,-0.00212989,0.01862331,-0.04107768,-0.07268193,-0.01417457,0.00827677,0.04995594,-0.11217086,0.05981494,0.02843049,-0.01447093,0.03810571,-0.04392704,0.01094669,-0.00248203,0.05078493,-0.00568906,0.01109098,-0.0194561,-0.07590932,0.0150401,-0.02070547,0.02553739,0.02987863,0.01465822,0.03283033,0.12368393,0.02488793,0.02570046,0.0007237,0.0188436,-0.21608727,0.10107981,-0.05194529,0.0450908,-0.02786552,-0.01513369,0.01023995,0.05281212,-0.00014924,0.03995334,-0.02036792,0.04420203,0.02319696,-0.06850839,-0.04178924,-0.03684168,-0.03019855,-0.00383848,-0.01897927,-0.02542876,-0.00185473,-0.01318644,0.02489091,0.0402427,0.03966548,-0.01871821,0.04576219,-0.08960869,0.01076534,-0.00298134,0.02694096,0.05873682,0.01307946,0.01068009,0.00152982,0.00883425,0.00457588,0.14907986,0.02002201,0.01708659,-0.01561095,0.00983464,0.05851593,-0.0403147,0.00054944,-0.0461687,0.02671022,0.04392375,0.02876532,0.02188832,0.03239546,-0.02964118,-0.03678938,0.02099924,0.00025402,0.03077792,0.02945464,0.01857476,-0.05428927,-0.03230371,0.01828491,-0.01433094,0.04242852,0.01172598,-0.00656319,0.0677534,-0.0065588,0.04553782,0.02754148,-0.00998825,-0.14615223,-0.05218885,-0.02235577,0.0284181,-0.00376114,-0.05079386,-0.05289857,0.07549012,-0.05388727,-0.02726256,0.03390142,-0.07456036,-0.02207374,0.07985394,-0.0081436,0.00203793,-0.02117313,-0.01636711,0.01633103,0.01351344,-0.02179699,-0.04112408,-0.02074623,0.01582842,0.06844712,0.06996961,-0.07823177,0.03573719,-0.01705756,-0.0307685,-0.0284072,0.13439201,-0.01038243,-0.04610267,-0.03441402,0.04328538,-0.02347269,-0.04275545,-0.06500423,0.01138723,-0.0403096,-0.00862109,0.08738028,-0.00732014,-0.07899709,-0.04425802,-0.06598589,-0.00973523,0.00648895,-0.04255575,-0.01838094,0.00718523,-0.02254391,-0.07340617,0.06052235,-0.00509556,0.03057708,0.0631042,-0.06301612,-0.00361956,-0.03331097,-0.01828803,-0.00486489,-0.00523877,-0.00160319,-0.04100926,0.02027129,0.01738498,-0.02889436,0.00073699,0.01637401,0.05362576,-0.07896276,0.07129166,0.00964032,-0.01990052,0.08104211,0.03278119,-0.05259547,0.01662588,0.03220719,0.06121827,0.02481852,0.0011144,0.01261544,0.0029632,0.00089014,0.00654428,0.03637876,0.04507283,-0.11416552,-0.19523694,-0.00847726,-0.05540234,0.00021354,0.02295565,-0.04645159,-0.03054803,-0.05133254,0.03308831,0.11952138,0.07939059,-0.04390153,0.02941159,0.07187384,-0.00577245,-0.01106175,-0.05444336,-0.02885706,-0.07483259,-0.00165988,-0.00984087,0.00441954,0.04225074,-0.05623453,0.0034367,-0.02116222,0.11243797,0.02893229,0.03334466,0.03029139,0.05497292,0.02049829,-0.03736684,-0.0261832,-0.02981212,0.05929303,-0.07314543,0.00152592,0.01219448,-0.00668918,-0.024781,0.05347461,-0.02828748,-0.09876389,-0.03309515,0.07031039,-0.00966947,0.07646504,0.0201096,0.03374908,0.07802368,-0.00902464,0.01535155,0.01489449,0.07003968,-0.07777934,-0.09564996,-0.01243795,-0.02567386,0.02304773,-0.05832104,-0.04425748,0.02299957,-0.03082745,0.06117556,0.0420259,-0.02185163,-0.02921189,-0.03375199,0.01745586,-0.01760498,0.07795272,-0.02554284,0.02498987,-0.04356895,0.03517055,0.0227169,-0.03671429,-0.0489544,-0.02777834,0.05293982,-0.04700769,0.04937853,0.04815185,0.0486264,0.05146183,0.04355857,-0.01345637,-0.03143231,-0.02776526,-0.01075305,0.01289443,-0.02178112,0.0620642,0.02610153,-0.01256601,-0.25417218,0.02968937,-0.0044164,0.03975296,-0.0013091,0.02421766,0.04071738,-0.0216323,0.05092663,-0.09130254,0.0315152,0.05054758,0.02344465,-0.00782026,-0.04780765,0.00841483,-0.02118684,-0.06263594,0.08940683,0.02190824,0.05552573,0.05970305,0.23974764,-0.00291355,-0.01555891,0.0101361,0.00917264,-0.04142125,-0.09140993,0.07159236,0.01437307,-0.02591275,0.1101486,0.02294688,-0.02902612,0.02022976,-0.04215874,0.03329542,0.03185516,0.00256255,-0.06582295,-0.00659588,-0.03714126,-0.01270705,0.13093454,0.01707765,-0.06431986,-0.0385046,0.06444449,0.07246532,-0.06485508,-0.03898712,-0.0325924,0.00459941,0.02255735,0.03686878,-0.00150234,0.0188293,0.00362157,-0.00696668,0.05520858,-0.00246607,-0.00860085,0.0095802,0.03683935],"last_embed":{"hash":"dz1ydi","tokens":224}}},"text":null,"length":0,"last_read":{"hash":"dz1ydi","at":1753423519764},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#By Ion Saliu, _Founder of Lottery Filter Mathematics_#I. [Introduction to Lottery Filters and Filtering in Software](https://saliu.com/filters.html#Filters)#{1}","lines":[26,32],"size":571,"outlinks":[{"title":"Setting the Lottery Filters: Theory and Software Tools","target":"https://saliu.com/filters.html#Software","line":1},{"title":"More Advanced Theories in Setting Filters and Lottery Strategies","target":"https://saliu.com/filters.html#Theories","line":2},{"title":"Older Writings in Setting Filters; Lottery Software Users Creating Strategies","target":"https://saliu.com/filters.html#History","line":3},{"title":"Resources in Lotto, Lottery, Software, Filters, Strategies, Systems","target":"https://saliu.com/filters.html#Links","line":4},{"title":"Implement filtering to reduce lotto playing tickets with lottery software.","target":"https://saliu.com/images/lotto.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08408383,-0.02742438,-0.00138682,-0.05048219,-0.03342858,0.04843831,0.00462078,0.03672814,0.02793952,0.00112077,0.00502327,-0.00628287,0.09812713,0.00896858,0.00479887,-0.0499429,0.02379956,0.00699194,-0.0950969,-0.05042635,0.09801258,-0.03172501,-0.07691909,-0.06273919,0.06897753,-0.01340215,-0.00195304,-0.08786481,-0.04288998,-0.23977228,0.02879441,-0.01576314,0.0423555,-0.05218077,-0.07643902,-0.01349804,-0.01982242,0.05621726,-0.0898174,0.03209038,0.00405579,-0.00005095,0.01654839,-0.02776745,-0.02059495,-0.01441524,0.00046442,-0.0267955,-0.01129687,-0.00139611,-0.06102925,0.01650143,0.00334484,0.05110429,0.00576576,-0.00034693,0.02848973,0.10475881,0.02845125,0.05569267,0.02324343,0.0551648,-0.1825867,0.04118067,-0.0360382,0.02173594,-0.02370945,-0.04515006,-0.00691793,0.08220476,0.02576563,0.04799238,-0.0321715,0.04231802,0.03003041,-0.05716669,-0.0431368,-0.07526695,-0.02035806,0.01593373,-0.051835,-0.02992669,0.017063,-0.00800004,0.01062124,0.02247542,0.01172306,-0.00909003,0.06704332,-0.05192562,0.03037371,-0.0023787,0.01805132,0.03613096,0.03076974,-0.00086878,0.00429126,-0.0169297,-0.00278061,0.14206629,0.01918886,0.02829006,-0.02175189,-0.01089267,0.05366265,-0.06247558,-0.00276113,-0.02367084,0.00727324,0.03716783,4.2e-7,0.00145063,0.08755939,-0.01190044,-0.03590392,0.04619749,0.0154573,0.01419894,0.0169772,0.02015512,-0.05641747,-0.04022781,0.00297365,-0.03234076,0.03629827,0.01443074,0.00564359,0.06528086,0.00348424,0.05257303,0.03493094,-0.01956675,-0.13793433,-0.03305988,-0.00888172,-0.02913009,0.0011273,-0.01190184,-0.0284078,0.03180013,-0.05394813,-0.02252836,0.03716857,-0.09365512,-0.0385778,0.11792587,-0.01593197,0.01361155,-0.02292082,-0.04581961,0.01138453,0.00431409,-0.03930803,-0.06398806,0.00749702,0.01333422,0.07730795,0.03598677,-0.07528055,-0.00194188,-0.02166849,0.00115259,-0.01458616,0.1059321,-0.01921844,-0.07388473,-0.03772477,0.06895707,-0.05037769,-0.02100342,-0.03874369,0.03003356,-0.05295934,0.0130078,0.06526647,-0.02674568,-0.1117257,-0.05018273,-0.01720956,-0.02366042,0.02696761,-0.01124379,-0.03316895,-0.01417844,-0.01119665,-0.06165257,0.04906973,-0.00725305,0.04504208,0.0446395,-0.0740101,-0.01521802,-0.00315519,0.01782631,0.01067752,-0.00439476,0.00173504,-0.03501999,0.0322401,-0.00975628,0.00552988,-0.02104921,0.03653468,0.06205749,-0.04314488,0.07490929,0.00562695,-0.04984814,0.12780263,0.04493906,-0.062879,-0.02254678,0.03870847,0.07513712,0.01412878,0.00501671,0.0166229,-0.00307742,-0.01428137,0.01182794,0.03569996,0.02774098,-0.08730777,-0.18905139,-0.01035704,-0.02614771,0.03092172,0.022394,-0.0292395,-0.03856067,-0.03268977,0.00726153,0.12082085,0.09371986,-0.05504913,-0.0090034,0.08014663,-0.01652741,0.00117941,-0.0695256,-0.02115505,-0.08172611,-0.00316386,0.02889292,0.00969509,0.02820954,-0.05847755,0.01187931,-0.01805023,0.12600414,-0.01130551,0.02423302,0.06004807,0.05654874,-0.01158956,-0.02699873,0.00645268,-0.01850536,0.05228696,-0.04420197,-0.03202587,0.0059816,0.02032784,-0.04485258,0.02466012,-0.01503388,-0.12578526,-0.03277655,0.03297205,-0.00775831,0.0846881,-0.00356276,0.04229166,0.04465657,-0.02531653,0.0478921,-0.00218516,0.09686276,-0.05806348,-0.09433398,-0.02268912,-0.01852001,0.01782513,-0.00419952,-0.07319818,0.0376701,-0.05057124,0.040291,0.02074605,-0.00073349,-0.0070423,-0.00238493,0.02807508,-0.03583158,0.08588325,-0.01747848,0.0261073,-0.05718296,0.06947227,0.0356589,0.00654867,-0.02269577,-0.05469053,0.06926169,-0.03631779,0.06023872,0.06377092,0.01666821,0.03641505,0.06441692,0.0211859,-0.00306185,0.00061723,-0.01456374,0.00881344,-0.03212681,0.04044602,0.03468907,-0.01589985,-0.2296138,0.0387396,-0.0188774,0.02371667,0.00720878,0.00332148,0.03584761,0.03620676,0.05460859,-0.04393966,0.01561594,0.04038158,0.02791047,-0.04026255,-0.01227427,0.02061109,-0.01597976,-0.0521552,0.09359682,0.05230362,0.07773013,0.03998708,0.22219868,-0.02806769,-0.01204341,-0.00033354,-0.01192922,-0.00339712,-0.08017763,0.03181513,0.00474012,-0.0108073,0.10892302,-0.00845325,-0.01241708,0.00736031,-0.00940883,0.03617212,0.00255389,-0.00772161,-0.07133968,0.00062844,-0.02552244,-0.04801497,0.14222564,0.00955069,-0.08049783,-0.0820182,0.05259091,0.06556063,-0.08497939,-0.03373235,-0.02439148,-0.00535334,0.02769737,0.03050281,0.03926331,0.02901849,0.04152966,-0.02209918,0.04605481,0.01644292,0.03647993,0.03916432,0.01688155],"last_embed":{"hash":"1jnewd1","tokens":441}}},"text":null,"length":0,"last_read":{"hash":"1jnewd1","at":1753423519846},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).","lines":[35,66],"size":5170,"outlinks":[{"title":"_**The Best Ever Lottery Strategy, Lotto Strategies**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":1},{"title":"Column sorting software: program sorts by column lottery reports to discover lotto strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08867613,-0.02072657,-0.0057026,-0.04354154,-0.02410947,0.05281713,-0.00432224,0.03570419,0.02419116,-0.0124785,0.00310127,0.01064577,0.09262877,0.00512348,0.00137314,-0.04000491,0.02791975,0.00404891,-0.07695164,-0.0477406,0.08748902,-0.0254801,-0.07107011,-0.06467894,0.05926586,-0.02735116,0.00591016,-0.08155392,-0.04300003,-0.23311168,0.01848516,-0.01574985,0.04598206,-0.06039874,-0.06325789,-0.00678826,-0.02445316,0.05791676,-0.09875907,0.04335404,-0.00491173,-0.01061843,0.02853808,-0.02425858,-0.0187737,-0.02712535,0.02014987,-0.02608422,-0.00900827,-0.00350267,-0.07290819,0.01829696,0.00785034,0.04939028,0.00714195,-0.00455471,0.02572621,0.09594548,0.02600498,0.05781592,0.02134955,0.04047703,-0.19578756,0.05049587,-0.04394379,0.02627489,-0.02869835,-0.03859544,-0.00554835,0.07540651,0.03241749,0.04820368,-0.05052934,0.04633587,0.03166853,-0.06952468,-0.04788446,-0.06985219,-0.01780332,0.02165862,-0.04438513,-0.01902154,0.01810127,-0.00432555,0.00418651,0.02277859,0.01951126,-0.01253519,0.06245613,-0.05724843,0.02616985,-0.00886518,0.0203228,0.04476096,0.01808743,-0.00160829,0.01019318,-0.00847967,-0.00363904,0.14306888,0.02349567,0.03069163,-0.00856174,-0.00946331,0.05223384,-0.05717403,0.00020248,-0.02762978,0.00712756,0.04173058,-0.00319694,0.01490613,0.09261134,-0.01806828,-0.0343671,0.03803656,0.01049504,0.01450429,0.01958125,0.0278537,-0.06969059,-0.03317654,0.00909998,-0.03274108,0.03692881,0.01545091,0.00768499,0.05932556,0.0020789,0.0477028,0.0253252,-0.01831968,-0.14154834,-0.03304993,-0.01666669,-0.02743043,-0.0030133,-0.01481781,-0.02672815,0.03388967,-0.05862772,-0.01722625,0.03294959,-0.09009973,-0.04465017,0.11515542,-0.01901336,0.0134089,-0.03045122,-0.037247,0.00697277,0.00019045,-0.04789494,-0.06192076,0.01451219,0.01693145,0.07353664,0.04679727,-0.07523939,0.00284053,-0.00576307,-0.00548482,-0.01753765,0.11122558,-0.00486069,-0.07264308,-0.05268824,0.07142282,-0.04024788,-0.01269709,-0.0489327,0.02896491,-0.04317082,0.01143017,0.06085318,-0.02434749,-0.10063857,-0.04934667,-0.0256714,-0.02124441,0.01901899,-0.0193334,-0.03728206,-0.00821752,-0.01310798,-0.06596121,0.05040861,-0.01222674,0.0519849,0.04645934,-0.06909531,-0.01724171,-0.00654837,0.01785608,0.00757346,-0.00534534,0.00253193,-0.03709784,0.03461346,-0.01807307,-0.00610688,-0.01628799,0.03198943,0.06216234,-0.05330344,0.07120846,-0.00237525,-0.04381071,0.13484739,0.03494171,-0.06575624,-0.01074446,0.05412138,0.06626747,0.00990441,0.00740382,0.00913151,0.00083854,-0.01520798,-0.00004772,0.03803323,0.04153609,-0.08698017,-0.19487701,-0.01173039,-0.03497489,0.033945,0.01831599,-0.02533475,-0.03568818,-0.03057812,0.01655506,0.1200883,0.08816873,-0.06352516,-0.01049379,0.07573187,-0.02001713,-0.00013494,-0.06522971,-0.03356715,-0.07488594,-0.01009309,0.02630048,0.00634153,0.02035751,-0.06807438,0.01652315,-0.02823254,0.12345496,0.00212907,0.02853877,0.04996402,0.06305835,-0.01287879,-0.02171143,-0.01130242,-0.01948405,0.06649297,-0.04705327,-0.03705985,0.00595979,0.02046273,-0.04198354,0.0191244,-0.01426328,-0.11733299,-0.03059027,0.02145592,-0.00032771,0.0772143,-0.00266099,0.04913684,0.03923586,-0.02626806,0.04703202,0.0088518,0.08757722,-0.0576915,-0.0970602,-0.02729366,-0.01168692,0.00888657,-0.00751368,-0.07462659,0.04436689,-0.06146722,0.04861053,0.0197916,-0.0067776,0.00808917,-0.00292822,0.03430713,-0.03313259,0.08157802,-0.02229253,0.02310929,-0.05545909,0.07070134,0.0323016,-0.00600448,-0.0130775,-0.05972159,0.06219479,-0.04132298,0.06123583,0.05386369,0.0203917,0.05100849,0.06185911,0.00720038,0.00393513,0.00792044,-0.00610657,0.00558409,-0.03065079,0.04347537,0.05522577,-0.01342184,-0.22948383,0.0464777,-0.00613675,0.03549307,0.01545017,0.01744099,0.04005371,0.02941632,0.05626741,-0.04175415,0.02434753,0.03198079,0.03005758,-0.04261848,-0.00882919,0.0261514,-0.01736696,-0.04116254,0.09383403,0.04264606,0.0699909,0.0376012,0.22342587,-0.01754229,-0.01474861,-0.00932144,-0.00755903,-0.00382562,-0.08936684,0.02970751,0.01734932,-0.0155057,0.10325312,-0.00348369,-0.01847421,0.01113729,-0.01073156,0.05243386,0.00131242,-0.00058706,-0.07783677,0.00607575,-0.03033424,-0.04868675,0.14076908,0.01349289,-0.08097099,-0.08010808,0.04790638,0.06064609,-0.08261648,-0.04142744,-0.02623253,-0.00328453,0.03513692,0.03410275,0.04496288,0.02204355,0.03581132,-0.01965724,0.05117288,0.01563642,0.03406294,0.03395033,0.02267798],"last_embed":{"hash":"oo9egt","tokens":322}}},"text":null,"length":0,"last_read":{"hash":"oo9egt","at":1753423520054},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{1}","lines":[37,45],"size":1052,"outlinks":[{"title":"Column sorting software: program sorts by column lottery reports to discover lotto strategies.","target":"https://saliu.com/ScreenImgs/sort-lottery-reports.gif","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05562101,-0.02882107,0.00415397,-0.04027819,-0.08217915,0.07580955,0.01902647,0.02816787,0.05183652,0.00476462,0.00233733,-0.01629986,0.0923688,0.01488845,0.0047658,-0.04899082,-0.00172485,-0.00950345,-0.11846751,-0.03489141,0.12347054,-0.04370829,-0.07664251,-0.05610174,0.08488647,0.0010423,-0.00265797,-0.0883311,-0.02871669,-0.24145174,0.03491464,-0.0122142,0.03995438,-0.04035779,-0.07980057,-0.02186446,-0.0237163,0.07849389,-0.05783014,0.02751764,0.02536129,0.02076191,0.01269451,-0.02092873,-0.02748308,0.00936973,-0.047391,-0.02423163,0.01157995,0.00013558,-0.02209011,0.02129212,-0.0067639,0.04016725,0.0176494,-0.0007459,0.03782057,0.1121809,0.0289494,0.04563904,0.00823836,0.06665701,-0.17904188,0.05005386,-0.03086351,0.02462663,-0.01387019,-0.0283159,-0.00815608,0.122226,0.00855896,0.04597127,0.0045574,0.01608542,0.03272703,-0.0273708,-0.05263622,-0.06917127,-0.03086607,-0.00362133,-0.07625473,-0.04276776,0.00128842,-0.0179204,0.03380205,0.00959465,0.02409685,0.01324704,0.05361645,-0.03430064,0.03718505,0.03732451,0.0053683,0.01197223,0.01856178,0.01196069,-0.00444879,-0.02977549,-0.00039973,0.13194022,0.00412334,0.02451155,-0.03273723,-0.0105542,0.03015103,-0.06243153,-0.02041719,-0.02635197,0.00172524,0.03060274,0.01109017,-0.01898529,0.06737287,-0.03316455,-0.0319899,0.07119527,0.03863083,0.01921677,0.007595,0.01450175,-0.01080849,-0.03742668,0.0193824,-0.02995083,0.03400119,0.01298157,0.02221771,0.07957363,0.01986614,0.06112595,0.05013207,-0.02655966,-0.12095549,-0.04610493,-0.00588991,-0.01759263,0.02155857,0.00573347,-0.02448187,0.02603674,-0.0365253,-0.04847484,0.05886004,-0.09835756,-0.00910796,0.1209482,0.00009514,0.01344811,-0.00467393,-0.03543633,-0.00796714,0.00193416,-0.0310701,-0.05975395,-0.01819947,0.01233723,0.05507794,0.0226503,-0.0651367,0.00113799,-0.04392458,-0.01537337,-0.0200529,0.09999928,-0.02278449,-0.05382901,-0.00567397,0.03368778,-0.06066483,-0.07106447,-0.02509894,0.01537826,-0.05621492,0.01396343,0.09408837,-0.01550257,-0.11498505,-0.03463908,-0.01406502,-0.01636652,0.03429009,0.00992508,-0.02795441,-0.0148344,-0.03059695,-0.03720065,0.04088496,-0.00149468,0.04040667,0.05428144,-0.07824173,0.00336064,-0.02025996,0.03445011,0.00923784,0.00923473,0.00310392,-0.04115945,0.03208992,0.00303015,0.02579597,-0.02954476,0.02145072,0.04604962,-0.02296405,0.08338105,0.01959158,-0.04686389,0.10342588,0.05035722,-0.04758705,-0.04430966,0.01586816,0.07307057,0.04846145,0.0078583,0.03631989,-0.01653159,-0.00827897,0.00679851,0.03235387,-0.00990568,-0.04870152,-0.16784461,-0.02529463,-0.00670073,-0.01125317,0.02560333,-0.01964434,-0.01168333,-0.0474919,0.01223908,0.11122833,0.07461547,-0.03494257,-0.01219468,0.08794788,-0.01630282,0.00409679,-0.08943151,0.01168288,-0.08110663,0.03770454,0.02022294,0.00864345,0.03028764,-0.03720683,0.02454361,-0.0090585,0.1263984,-0.02010013,-0.01390865,0.05439085,0.04017986,-0.00076561,-0.03541679,0.06049315,-0.01509841,0.0138333,-0.05381243,-0.0374697,-0.00551508,-0.00324967,-0.03639436,0.02930161,-0.03803936,-0.12342606,-0.0609522,0.06211126,-0.04127559,0.09940153,-0.00544181,0.02807993,0.05655744,-0.03011902,0.05853853,-0.01895497,0.09788652,-0.06419382,-0.09444985,-0.00009795,-0.02175877,0.05086098,-0.01331193,-0.05380554,0.02086122,-0.02767712,0.03885552,0.03563286,-0.00198273,-0.03614215,-0.00784151,0.004329,-0.03769584,0.05018036,0.00132212,0.01632068,-0.05986894,0.02931627,0.03078428,0.00261499,-0.03672878,-0.04113705,0.07634057,-0.05654578,0.03784195,0.07359818,0.01502899,0.01104903,0.05812183,0.04597143,-0.01449797,-0.0278465,-0.03130256,0.0200196,-0.03244514,0.055742,-0.0240697,-0.03040109,-0.24448813,0.03359429,-0.04204307,0.03668463,-0.00885316,-0.00819793,0.0415095,0.05166124,0.03217322,-0.05668721,0.01541355,0.06483077,0.02212622,-0.03291505,-0.00120672,-0.02125377,-0.02997907,-0.06869133,0.07977676,0.04415178,0.1222025,0.0518236,0.21181969,-0.03634736,-0.00339791,0.02217532,-0.00948124,-0.00482104,-0.06145387,0.02529634,-0.0283079,-0.01234731,0.11084525,-0.02999238,-0.00428111,0.01063025,-0.01067938,0.00948692,0.00640097,-0.00452747,-0.06390338,-0.02692399,-0.0078633,-0.03566819,0.15869293,0.01702544,-0.06537047,-0.07402277,0.03764087,0.05740867,-0.09020127,-0.00379611,-0.01158577,-0.02914993,-0.00266682,0.03804331,0.02304589,0.02965026,0.03842697,-0.00298767,0.01424191,0.01648126,0.02861245,0.02585316,0.0040469],"last_embed":{"hash":"t8hfph","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"t8hfph","at":1753423520170},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).#{4}","lines":[49,66],"size":3639,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.118444,-0.05523627,0.00834217,-0.03665849,-0.02042559,0.05704281,-0.02226972,0.00390564,0.01433599,-0.00608059,0.02224885,0.00658473,0.07845677,-0.00203025,0.00879088,-0.05040655,0.02613979,0.01074111,-0.06520119,-0.00965189,0.06774722,-0.04669954,-0.04642826,-0.0781058,0.05690739,-0.01145152,0.00366537,-0.06697389,-0.07582561,-0.23722802,0.05725816,0.00477568,0.03224167,-0.04081294,-0.05240706,-0.0130947,-0.00895415,0.04521053,-0.0616113,0.0110981,0.04577517,0.01684253,0.04267648,-0.03929402,-0.01853227,-0.05336284,-0.00298863,-0.03693355,0.02206218,0.01666692,-0.04514582,0.00261393,0.01863104,0.03878841,0.02877064,0.00523002,0.01523188,0.11458138,0.04256013,0.03660724,0.02738546,0.02041911,-0.16534761,0.08143192,-0.04099426,0.02462642,0.00185003,-0.03275427,0.0178929,0.02803583,0.00904861,0.03602824,-0.00234502,0.07450335,0.01899206,-0.12068988,-0.03610843,-0.05145238,-0.01243909,0.01726408,-0.06206406,-0.0183256,-0.01058083,0.01175632,-0.03093812,0.04446024,0.06147931,-0.00455354,0.03054357,-0.02845741,0.00828053,-0.04816259,-0.01766771,0.03125452,-0.00437256,-0.03714276,0.01182617,0.00341059,-0.03130792,0.12249694,0.03791021,-0.00149867,-0.05386458,-0.00306871,0.05516408,-0.05615624,-0.02006536,-0.061982,-0.03668728,0.08339337,0.05171373,-0.0140273,0.05787128,-0.01266045,-0.04522909,0.01092652,-0.03079728,0.02712149,0.03363215,0.00853681,-0.07494596,-0.022801,0.02063265,-0.0077026,0.04958812,0.05968793,0.03532231,0.04919869,-0.03259015,0.05674527,0.04996904,-0.02398945,-0.12341636,-0.00995271,0.00752406,0.02173345,0.01817278,-0.01274843,0.00361022,0.02414818,-0.03769844,-0.03240458,0.01582685,-0.07969686,-0.04418797,0.08386519,-0.06545681,-0.00064329,-0.00060387,-0.07704587,0.0387327,0.02038815,-0.07089466,-0.04618756,0.00968787,0.03275198,0.07949498,0.05241666,-0.05769634,-0.04233432,-0.04763424,-0.02398259,-0.04751231,0.08056405,-0.02188972,-0.05169795,-0.02159637,0.04167852,-0.02467182,-0.02556327,-0.0217689,0.0086946,-0.0420368,0.03603597,0.06486778,-0.04187173,-0.0914273,-0.02079266,-0.01256083,-0.04394965,0.01049087,-0.00856628,-0.03449235,-0.0120578,0.01377705,-0.09078765,0.08007423,0.00093531,0.0263365,0.0299009,-0.00552396,0.01127479,-0.04957699,-0.00463549,0.00372977,0.00006495,-0.0358365,-0.01650399,0.02943255,-0.03701435,0.07850994,0.00183786,0.01385314,0.05416502,-0.05331061,0.07045975,-0.02407109,-0.0511001,0.11968976,0.01955665,-0.05215976,-0.02217357,0.04781634,0.06326174,-0.00654773,0.02758554,0.01753309,0.01011007,0.01156043,0.04244594,0.00747706,0.05409637,-0.07500012,-0.20312445,-0.01864058,-0.00508525,0.03268954,0.04573281,-0.03574118,-0.03445835,-0.00214585,0.04664905,0.12328789,0.11533951,-0.05308751,0.01142835,0.05979183,0.00259406,0.05608789,-0.0524698,0.01200239,-0.07553887,0.00417656,0.02884208,0.03106114,-0.03198886,-0.06250018,0.04823879,-0.0158268,0.12049708,-0.01398134,0.04995517,0.025892,0.07506307,-0.02808993,0.01255839,-0.02563471,-0.02387528,0.07583883,-0.05702767,0.01716815,-0.01828388,0.01577072,-0.04566059,0.01238915,-0.01532954,-0.14201838,-0.01711329,0.05691306,-0.0300757,0.04278223,-0.00964878,0.03042879,0.03307037,-0.01975477,0.01501815,0.03804528,0.07243399,-0.07536216,-0.07279915,0.00536807,0.01563906,0.03897426,-0.00463123,-0.06850261,0.02115654,-0.04166427,0.01625277,0.02283617,0.00989579,-0.02247093,0.03202752,-0.01030249,-0.04704385,0.04745912,-0.03072637,0.04740163,-0.0616333,0.07337302,0.04647391,0.0067819,0.0167762,-0.06466362,0.05474289,-0.04376614,0.04183724,0.04774449,0.00829042,0.01389123,0.06417035,0.04284688,0.01719447,0.00680385,-0.0180091,0.00907427,-0.04864385,0.04800707,0.01596159,-0.03032303,-0.24770761,0.01818572,-0.03541398,0.03550357,-0.02148258,-0.03225784,0.0275094,0.02627672,0.0486314,-0.03043414,-0.03452706,0.06562496,0.04731414,-0.05208545,-0.02470912,-0.02069683,-0.03483488,-0.01789184,0.09055888,0.00060451,0.05717666,0.0204317,0.2299405,-0.00050651,0.00969397,-0.00082096,0.01064777,-0.03732419,-0.04483111,0.05653913,0.0066726,-0.02212493,0.12040743,-0.01330375,0.01056947,-0.00001366,-0.05682123,0.05370948,-0.0102003,0.02482508,-0.03156316,0.03687194,-0.03947382,-0.04779664,0.11616914,0.00570079,-0.03063982,-0.0705276,0.05500671,0.11577178,-0.05814457,0.00070493,-0.0722978,-0.02467404,0.0102341,0.00861692,-0.00396095,0.03070899,0.00844958,0.0266321,0.0467504,-0.04144756,0.03972714,0.01898066,0.00986064],"last_embed":{"hash":"3kzgnc","tokens":433}}},"text":null,"length":0,"last_read":{"hash":"3kzgnc","at":1753423520345},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>","lines":[67,114],"size":9021,"outlinks":[{"title":"_**creating, updating lottery data files of past winning numbers**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":10},{"title":"_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":14},{"title":"_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":15},{"title":"The lotto filters can eliminate huge amounts of lottery combinations or tickets, cost of play.","target":"https://saliu.com/images/lotto-filters.gif","line":19},{"title":"Create winning reports, sort them and use them for effective lottery strategies.","target":"https://saliu.com/images/strategy-sorted-reports.gif","line":28},{"title":"Study advanced methods in setting lotto filters to create winning lottery strategies.","target":"https://saliu.com/images/lottery-filter-strategies.gif","line":34},{"title":"The lottery filters are founded on theory of probability, mathematical formulas.","target":"https://saliu.com/images/filter-report.gif","line":40}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11681796,-0.04393263,0.01802955,-0.03780341,-0.02179903,0.0598158,-0.02789136,0.00258336,0.0152676,-0.00321566,0.01749221,0.00542097,0.07840209,-0.00503318,0.00594678,-0.05238958,0.03265115,0.01316631,-0.06984926,-0.00938287,0.06308252,-0.0396748,-0.06172742,-0.07830977,0.06202164,-0.00671708,0.00264761,-0.0641027,-0.07496318,-0.23791353,0.05643078,-0.01421916,0.03409996,-0.03781261,-0.0475782,-0.01277047,-0.0098881,0.04909358,-0.05978891,0.00704995,0.04402578,0.00988253,0.04726792,-0.04257993,-0.0254897,-0.05610597,0.00012629,-0.02256916,0.01892587,0.01239605,-0.03929834,0.00204594,0.01251411,0.03712319,0.02193419,0.0080691,0.02552205,0.11729647,0.04524786,0.04195192,0.01608877,0.01771896,-0.16294765,0.07555383,-0.03599451,0.02709832,0.00348139,-0.03205138,0.02512815,0.03751936,0.00761503,0.03197261,0.00406261,0.07113823,0.01760872,-0.12005442,-0.04304281,-0.06091004,-0.00891228,0.03007474,-0.06503417,-0.01129335,-0.01645617,0.01688482,-0.02708833,0.04763949,0.0586252,-0.00889302,0.02739586,-0.02316874,0.01262679,-0.05087397,-0.03128171,0.03315824,-0.01383524,-0.03773366,0.00962398,-0.00502057,-0.02310808,0.12468665,0.04139537,0.00075884,-0.04765336,0.00393532,0.05735791,-0.054423,-0.0215618,-0.06086097,-0.03831207,0.07454899,0.05554774,-0.00673535,0.05046104,-0.01591033,-0.04275041,0.01521434,-0.03593342,0.03383131,0.02874461,0.00703909,-0.0673378,-0.02758356,0.01683518,-0.02186626,0.05541176,0.06441674,0.04069971,0.05286098,-0.03647446,0.0595429,0.04909464,-0.0266163,-0.11980724,-0.01214577,0.0075519,0.02651507,0.01415709,-0.01321194,-0.00421559,0.03424593,-0.03623326,-0.03190465,0.01179821,-0.08061451,-0.03814434,0.0914521,-0.07088863,-0.00126973,-0.00257911,-0.07633578,0.03812379,0.01307513,-0.06084269,-0.04429885,0.0107452,0.02697631,0.0671589,0.05138542,-0.05922651,-0.04039206,-0.04881582,-0.0200044,-0.04720522,0.08475681,-0.01573617,-0.03541823,-0.01652017,0.03808691,-0.02701238,-0.01528844,-0.02802324,0.02466778,-0.03993972,0.03040881,0.07114905,-0.03634224,-0.09720914,-0.01513331,-0.02036713,-0.04594165,0.01988138,-0.01022375,-0.02801889,-0.02001322,0.01372646,-0.09372786,0.07771052,0.00082126,0.0218364,0.03208087,-0.01240789,0.01655913,-0.04909924,-0.01282414,0.00337543,-0.00082616,-0.0233665,-0.01096909,0.0296203,-0.03580214,0.07142473,0.00303372,0.01425855,0.05729488,-0.05397665,0.06649721,-0.02059305,-0.05090504,0.12444716,0.01406285,-0.05034328,-0.03206209,0.04412196,0.0595769,-0.00142794,0.02379024,0.01496672,0.00678093,0.00143387,0.04544644,0.01170539,0.05588305,-0.07557759,-0.20073834,-0.01608412,0.00116779,0.03555062,0.03690971,-0.03477341,-0.04327887,0.00265174,0.0505082,0.12530191,0.12662707,-0.0469726,0.01381539,0.05872246,0.00426947,0.05875169,-0.05074773,0.02048887,-0.07252233,0.00501816,0.02469227,0.02656594,-0.03296829,-0.07059108,0.04643228,-0.01349679,0.11822077,-0.03091588,0.04329279,0.01745564,0.07847133,-0.03158858,0.00598664,-0.01268619,-0.02197628,0.0736348,-0.06038287,0.01150587,-0.02066056,0.01973982,-0.04134696,0.01001808,-0.01372641,-0.12848911,-0.02226063,0.06093172,-0.02468616,0.05990549,-0.00329429,0.01899511,0.03115435,-0.02492443,0.01472154,0.03111388,0.07857548,-0.07825258,-0.07738151,0.00564573,0.02208233,0.03565643,-0.00684314,-0.0667334,0.02779703,-0.03787657,0.02165921,0.01360695,0.00833237,-0.02684108,0.02379222,-0.00285399,-0.04928665,0.03589875,-0.03396848,0.05106277,-0.06614064,0.07550684,0.03938328,0.00116036,0.0092839,-0.05953148,0.06359467,-0.04735486,0.04603973,0.04512658,0.01681507,0.01120205,0.06316112,0.03812178,0.01369579,0.01352395,-0.02279223,0.01156362,-0.04423879,0.05739504,0.01549177,-0.03387392,-0.24558455,0.01597487,-0.03737097,0.03028549,-0.02754101,-0.03673044,0.0285078,0.03126268,0.04223534,-0.03959776,-0.03753275,0.06027085,0.04974445,-0.04353381,-0.03028336,-0.02102288,-0.03169906,-0.01891613,0.09087239,0.00534096,0.05766366,0.01645331,0.22912043,-0.00481095,0.0175701,-0.0025324,0.01024861,-0.04842107,-0.05374171,0.05798939,0.02425538,-0.01528082,0.11320118,-0.00982341,0.01466828,-0.0036495,-0.05053975,0.05679943,-0.00444961,0.02072799,-0.0326546,0.03438463,-0.03632861,-0.05267692,0.11735637,-0.0000728,-0.02906897,-0.0680661,0.05148612,0.11434156,-0.06830969,-0.00039539,-0.07353155,-0.02671191,0.0160138,0.01004536,-0.00519655,0.02468742,0.0094737,0.02927398,0.04926333,-0.04044448,0.04367004,0.00457954,0.01349598],"last_embed":{"hash":"l4w1vu","tokens":350}}},"text":null,"length":0,"last_read":{"hash":"l4w1vu","at":1753423520504},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{1}","lines":[69,74],"size":1273,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11247617,-0.03761402,-0.00608894,-0.02659776,-0.02537195,0.02394508,-0.03067722,0.03821423,0.02706988,-0.0060467,0.01631802,-0.00267589,0.08833557,-0.02397663,0.0227543,-0.04223391,0.00072265,0.00367463,-0.03282104,-0.00304236,0.08574436,-0.01875091,-0.02460319,-0.07401628,0.05978124,-0.01832778,-0.00161296,-0.07090058,-0.02274819,-0.22857808,0.05937142,0.03458345,0.0187845,-0.04454973,-0.06957061,-0.00769679,-0.01473846,0.02265373,-0.08819214,0.03533981,0.02641506,0.03595518,0.01940836,-0.04531905,0.02881638,-0.04988597,0.0163166,-0.04251712,0.02957235,0.01191504,-0.04433804,0.03621583,0.02302066,0.03155681,0.04115999,-0.01215526,0.00981231,0.09513184,-0.00037967,0.02257185,0.01962425,0.03243516,-0.17903578,0.10131387,-0.08542661,0.02724871,0.00076708,-0.03749684,-0.0183372,0.06281037,0.01075396,0.03861864,-0.03196316,0.06876426,0.01240372,-0.09059768,-0.02893243,-0.04586363,-0.01691952,-0.00551478,-0.04223126,-0.05167742,-0.00778183,-0.00569598,0.00410298,0.02431015,0.03560558,0.00792494,0.05009921,-0.08607271,0.01772071,-0.02117615,0.02312846,0.06652367,0.01615652,0.00613899,0.01852874,0.01763485,-0.05084272,0.14068994,0.00904453,-0.00391569,-0.04994847,-0.02722951,0.05179626,-0.05992813,-0.00711433,-0.07249387,-0.018573,0.06865583,0.04199042,0.00016809,0.0454048,-0.01713411,-0.0459564,0.02913733,-0.01196978,0.01737062,0.03752888,0.01975184,-0.06683937,-0.01720226,0.02127448,0.01859097,0.03133068,0.02576184,0.03030276,0.03555954,-0.0165561,0.06048415,0.04466942,-0.02068614,-0.14792961,-0.02037563,-0.00809013,0.02591939,0.01721844,-0.04122246,0.00876224,0.0248076,-0.04663437,0.00723906,0.0321923,-0.08558799,-0.01327184,0.08884432,-0.00466684,0.0052453,-0.01016559,-0.05996097,0.01352751,0.04185899,-0.06833922,-0.05058231,0.00057177,0.03382712,0.09741142,0.03898979,-0.05808621,-0.00483219,-0.03506974,-0.04181021,-0.04125603,0.09918415,-0.04104748,-0.0735176,-0.04148776,0.03489894,-0.02184325,-0.07148076,-0.03948398,-0.02141677,-0.04185499,0.02619601,0.08991592,-0.02830598,-0.06924297,-0.03270851,-0.02572903,-0.02777504,0.00705733,-0.01631463,-0.04178526,0.00889798,-0.00870365,-0.05990767,0.06182238,-0.00005392,0.05298549,0.0710298,-0.04886127,-0.00853567,-0.03903693,0.02490853,0.00348079,0.00014391,-0.04436998,-0.02749953,-0.00147023,-0.01957428,0.04503563,0.00126736,-0.00481715,0.02818886,-0.05841867,0.06839793,-0.00262477,-0.04494566,0.09393664,0.02609149,-0.06175939,0.00554227,0.08609923,0.05902253,-0.01657465,0.00203554,0.02084812,0.00850866,-0.00347075,0.01733742,0.01485115,0.06441315,-0.09195685,-0.21839303,-0.01596182,-0.04493567,0.01787004,0.05116691,-0.04192262,-0.02456438,-0.03164526,0.03407173,0.10433792,0.05630827,-0.07500257,0.0190673,0.07194388,-0.01161798,0.02650291,-0.04700692,-0.03606385,-0.05916184,-0.00115287,0.01379017,0.04170989,0.025534,-0.04120569,0.04507362,-0.02864178,0.1255291,0.00556071,0.03895095,0.04253216,0.04721785,0.00275259,-0.00520824,-0.06242292,-0.01278155,0.08931231,-0.04748602,0.032642,0.01556739,-0.01056687,-0.02556751,0.03843474,-0.02689939,-0.13745993,-0.00450636,0.0456486,-0.02375177,0.02269535,-0.00626183,0.05097439,0.05654088,0.00589983,0.03289768,0.03847221,0.05597242,-0.08191606,-0.08459084,-0.00791216,-0.01188389,0.01823446,-0.02597556,-0.08370036,0.00434884,-0.05023887,0.02798428,0.03547575,-0.01805168,-0.0048226,0.00112381,0.00796277,-0.02248265,0.0676818,-0.03000415,0.03482437,-0.05665246,0.04246582,0.05176501,-0.02007614,-0.00667843,-0.06006504,0.03897569,-0.0123313,0.04020587,0.0455506,0.03089255,0.0412453,0.06604023,0.02778485,-0.00541487,-0.03385826,0.00305216,0.00665174,-0.03579549,0.05065139,0.01554345,-0.02062332,-0.2557382,0.02191645,-0.0129713,0.02609365,0.00340553,0.01007129,0.03657602,-0.00577802,0.02905533,-0.04229322,-0.02131959,0.05588307,0.02606967,-0.05185204,-0.0372565,-0.01365183,-0.01854707,-0.03090504,0.09853922,0.01373244,0.06862224,0.03929042,0.24934667,0.0073801,-0.00705378,0.01482615,0.01894373,-0.03299554,0.00292021,0.04071874,-0.02798524,-0.03377166,0.14912519,-0.00076117,-0.02528896,0.01967538,-0.05929277,0.04697949,0.0226912,0.04541978,-0.02369926,0.01506904,-0.04476985,-0.00879989,0.11225217,0.02311738,-0.04083994,-0.06586049,0.04091445,0.08529931,-0.05424273,-0.00577212,-0.05480546,0.00067489,0.01513179,0.00965455,-0.00893051,0.0332081,0.02339521,0.01193976,0.04975874,-0.02102976,0.0161594,0.0555912,0.00450948],"last_embed":{"hash":"9ez28h","tokens":138}}},"text":null,"length":0,"last_read":{"hash":"9ez28h","at":1753423520626},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{3}","lines":[76,76],"size":324,"outlinks":[{"title":"_**creating, updating lottery data files of past winning numbers**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09363037,-0.03467916,0.01480353,-0.04462125,-0.03072618,0.04423415,0.00578092,0.03167722,0.02788296,-0.0160085,-0.01250813,-0.00046127,0.06908298,0.02511573,-0.01949826,-0.04197103,0.01590226,0.05829543,-0.07581422,-0.03978682,0.08389857,-0.01693266,-0.07275644,-0.07345854,0.03808697,-0.00223978,-0.01930695,-0.05676325,-0.05125691,-0.21049783,0.02998594,-0.00773887,0.05166402,-0.04715644,-0.04735947,-0.03703589,-0.03057248,0.0537752,-0.14941834,0.02579671,0.00841158,-0.03290911,0.03710891,-0.03764208,0.0077469,-0.01994467,0.03262313,-0.01783865,0.00280835,-0.00987938,-0.0583282,-0.00105484,0.00300092,0.04374705,0.00837701,-0.00349833,0.03057271,0.09690222,0.03342091,0.04031853,0.03186687,0.03542198,-0.1952464,0.08596379,-0.03132996,0.02480013,-0.04366671,-0.06007893,0.00345416,0.070053,0.00583585,0.0405966,-0.03065923,0.06597865,0.02628755,-0.07833659,-0.04596179,-0.08712094,-0.03431809,0.01925118,-0.06857305,-0.02558496,-0.03009884,-0.00782316,0.01608217,0.04551201,0.03390256,0.00082855,0.05176061,-0.03964674,0.03921673,-0.02509002,0.02803917,0.09099662,0.0165853,-0.00535463,-0.01215501,0.00755101,0.0029039,0.14551644,0.02302775,0.01335528,-0.02248387,-0.01858724,0.04803532,-0.04070523,-0.02409464,-0.03192953,-0.02721546,0.0355539,0.03060624,0.04121203,0.07059965,-0.03201038,-0.03951577,0.06085858,0.00184226,0.03032618,0.0202063,0.02635525,-0.0790257,-0.03284704,0.00147014,-0.01491764,0.03101466,0.03177631,0.03612145,0.05747269,0.00001509,0.05471531,0.03007804,-0.01230352,-0.15499847,-0.03721876,0.00184199,0.02027676,0.00013359,-0.04136525,-0.03031552,0.01214387,-0.04419883,-0.02379799,0.01850766,-0.08485928,-0.03656149,0.11165938,-0.06067124,-0.00614178,-0.02630527,-0.03699618,-0.00514948,-0.0037341,-0.03210942,-0.03925173,0.0063886,0.01421698,0.06661792,0.02790717,-0.07742909,-0.00591381,-0.00317606,-0.0251273,0.00397674,0.12225816,-0.00888275,-0.04447481,-0.06538227,0.05045328,-0.03726705,-0.03776955,-0.02913695,0.03722542,-0.05212319,0.02950855,0.04092127,-0.01733594,-0.09253898,-0.06562001,0.01121205,-0.05790916,0.06667943,-0.01482468,-0.01251066,0.00402787,-0.02127826,-0.0810934,0.03499196,-0.02216965,0.02816029,0.04895235,-0.03549036,-0.02928439,0.02058605,-0.03902579,0.02077464,-0.02057521,-0.01400185,-0.02882833,0.02594859,-0.01221676,0.01709225,0.01239669,0.05346273,0.06770959,-0.04065159,0.05522939,-0.03483337,-0.04165594,0.12018825,0.0171893,-0.05981467,-0.00208757,0.02716301,0.05754884,0.00098723,0.00577734,0.00796574,0.00925918,0.00480212,0.00486744,0.0421475,0.04734101,-0.04976834,-0.19233507,-0.00997605,-0.02186498,0.04840311,0.01241809,-0.00904447,-0.02977309,-0.01822187,0.04432814,0.14084369,0.10171504,-0.06177579,-0.03904951,0.07579383,0.00203234,0.03813527,-0.05755575,-0.02981461,-0.07699083,-0.01784667,0.00626321,0.01447107,0.01519662,-0.07661735,0.03085659,-0.0128569,0.11782929,0.00887608,0.05409793,0.02527513,0.04980152,0.00454005,0.01028509,0.00783345,-0.01088059,0.08119633,-0.04454348,-0.00877096,0.01779495,0.01724034,-0.03715585,0.03168888,-0.01164053,-0.11745987,-0.00137308,0.03865797,-0.01021788,0.0350002,0.02383308,0.04103146,0.04230243,-0.02162415,0.03681238,0.02863246,0.08121326,-0.04576171,-0.06484639,-0.03053229,0.00417162,-0.01841179,-0.0096779,-0.09328993,0.03005776,-0.05321475,0.02034274,0.0029282,-0.01323599,-0.01209702,0.00184007,0.00960485,-0.03339846,0.05005166,-0.02570914,0.03458406,-0.04815479,0.06678484,0.01818407,0.00591877,-0.0162417,-0.0521971,0.06234876,-0.00974915,0.06737743,0.05174404,0.05882695,0.03112916,0.05995083,0.02213821,0.01384759,0.0137248,0.00749706,0.02636461,-0.03547625,0.05216192,0.03527552,-0.00483799,-0.24190776,0.06155074,-0.00861283,0.04995795,0.00079624,-0.01956993,0.03700252,0.02508062,0.05659041,-0.0447044,0.00976748,0.05756353,0.02983167,-0.06782588,-0.03939979,0.02303588,-0.01228161,-0.06504953,0.0919774,0.03348586,0.04949448,0.04350758,0.21680182,-0.01320124,0.00698669,0.00439507,0.00163766,-0.01957774,-0.06584492,0.05519057,0.03486264,-0.0437269,0.12279227,-0.00786404,-0.02194392,0.00490398,-0.00719636,0.02685227,0.02124948,0.01221285,-0.06510942,0.01457747,-0.06858959,-0.06367781,0.10363536,0.01793936,-0.07955552,-0.0670839,0.01653355,0.09040708,-0.07079738,-0.04770096,-0.04874299,-0.03018062,0.01429895,0.01704002,0.03586333,0.0410385,0.00420554,0.00117136,0.02281624,-0.03751666,0.02621693,0.01906375,0.01759808],"last_embed":{"hash":"15bp01y","tokens":186}}},"text":null,"length":0,"last_read":{"hash":"15bp01y","at":1753423520693},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{4}","lines":[77,77],"size":547,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1044022,-0.02019472,-0.01484017,-0.055382,-0.03131712,0.03232728,0.00267629,0.01873625,0.01408965,-0.00044627,-0.00759481,-0.00329238,0.07554368,0.02598633,-0.02700151,-0.04981417,0.02045299,0.02717028,-0.09223665,-0.0227184,0.09035537,-0.02483159,-0.07854959,-0.07225932,0.04132504,-0.00846256,-0.02540983,-0.08507232,-0.05061043,-0.20204006,0.02183423,0.00498823,0.06021235,-0.06710947,-0.07221238,-0.04075501,-0.00974838,0.02288121,-0.13135272,-0.00215809,0.01501627,-0.02203539,0.03101315,-0.03830928,0.00698414,-0.04318567,0.01024777,-0.01081554,0.02124368,-0.01304378,-0.03508729,0.02800497,0.00843148,0.04800827,-0.00050711,-0.00983804,0.03305623,0.09237032,0.02426746,0.06275512,0.0293726,0.00504607,-0.17313981,0.07643476,-0.03482097,0.00768284,-0.02201129,-0.06238587,0.01871161,0.03903645,0.00633334,0.05173757,-0.0166982,0.0965021,0.00800061,-0.07291703,-0.03548933,-0.08493682,-0.01701865,0.03870661,-0.06356117,-0.03610351,-0.01238067,0.01152337,0.00530665,0.01595727,0.02425797,-0.00083357,0.04344235,-0.07098673,0.02380649,-0.00646764,0.01616409,0.0713848,0.01574319,-0.01160508,-0.00691509,0.00641799,0.01656843,0.15571836,0.03535919,-0.00953995,-0.02924387,-0.00211234,0.04286471,-0.05049362,0.01087951,-0.05142059,-0.02878419,0.03146655,0.01191508,0.04815255,0.04401115,-0.01208857,-0.00695732,0.05201743,-0.02145091,0.01497393,0.03944398,0.02904003,-0.08317468,-0.02573144,-0.03355643,-0.040677,0.05221666,0.0203142,0.00122747,0.05449105,0.00237874,0.07384652,0.04623744,-0.0305175,-0.1304739,-0.03880944,0.00498474,0.0358499,0.00744312,-0.04602949,-0.03141082,0.03348402,-0.04100352,-0.02433584,0.02695847,-0.07195894,0.00316906,0.11715905,-0.02921144,0.00776084,-0.0203091,-0.03274136,0.00968981,0.01927432,-0.01417989,-0.05093572,0.01076485,0.03269435,0.07007989,0.01907055,-0.07825886,-0.0049302,-0.01543297,-0.02204041,-0.01523387,0.13278595,-0.0219013,-0.04659104,-0.0608577,0.03789194,-0.05475396,-0.04449312,-0.04011351,0.03689342,-0.03105471,0.02284696,0.08799592,-0.01473273,-0.06652322,-0.06265862,-0.01422833,-0.0420837,0.04394124,-0.03164823,-0.00650843,-0.00603084,-0.02599605,-0.07263337,0.04874157,-0.00729691,0.02422582,0.03923776,-0.05734353,0.00815926,-0.01904236,-0.02161109,0.00189071,0.01149882,-0.01938017,-0.03959715,0.01253219,0.01744507,0.0326579,-0.00336934,0.02776032,0.04066358,-0.02635696,0.06128438,-0.01424136,-0.02544189,0.09124403,0.00997513,-0.08425988,-0.01946641,0.07047755,0.07660328,-0.02199804,-0.00282885,0.02683369,-0.0137721,0.00771333,0.01528758,0.04528482,0.02106654,-0.0597157,-0.21094224,0.01009635,0.00165517,0.00450245,0.02928641,-0.0173424,-0.04113991,-0.012117,0.04014829,0.13121456,0.10826301,-0.04735006,-0.01889817,0.0877372,-0.02266411,-0.00044855,-0.07514879,-0.038569,-0.08337443,-0.02264867,0.00688414,0.04040828,0.01999209,-0.04465917,0.04340011,-0.0326108,0.10631413,0.00955504,0.04672551,0.0082382,0.05213082,-0.00554912,-0.00965917,-0.01443874,-0.01145487,0.05507499,-0.08880994,0.00360977,0.00306147,0.03079108,-0.01928216,0.01489344,-0.02215303,-0.09710925,0.03106209,0.03212566,0.00272244,0.04521439,0.03549566,0.03052749,0.04562911,-0.04463853,0.03708432,0.04972512,0.06878151,-0.06256218,-0.06076862,-0.0278746,-0.0050681,-0.01059105,-0.00207761,-0.08348328,0.03888072,-0.03982607,0.02831862,0.02154476,-0.03523873,-0.01618926,-0.00060477,0.02192023,-0.02721787,0.04673078,-0.05156383,0.03775748,-0.05084864,0.0590385,0.03677433,0.01419569,-0.02041995,-0.04058731,0.07147989,-0.01234373,0.05194072,0.03472868,0.05805651,0.04587872,0.05037625,0.00443386,-0.01920906,0.0139213,0.02225922,0.02995508,-0.01514399,0.06660934,0.0367578,-0.01696459,-0.26148996,0.05148119,-0.03596439,0.03692578,-0.01462087,-0.00716935,0.04258019,0.0219809,0.03720611,-0.02409425,0.01345977,0.07452329,0.048981,-0.05934017,-0.05108788,0.00716743,0.02064875,-0.03539824,0.08816939,0.01359442,0.05674588,0.03229268,0.23496456,-0.01945189,-0.01142115,0.00980057,-0.00681763,-0.03083715,-0.05610062,0.08538345,0.03423937,-0.02128248,0.11567872,-0.00976994,0.01240155,0.0207489,-0.01493334,0.10064498,0.00874729,0.01328739,-0.06423855,0.01739892,-0.06617379,-0.06491097,0.09674692,0.01176867,-0.07981826,-0.06251349,0.02551067,0.07780284,-0.06222636,-0.02369284,-0.02904465,-0.02789815,0.01385886,0.01466616,-0.00038779,0.04374596,0.01395641,0.00214003,0.0481653,-0.02218645,0.01566188,0.03994162,0.03682134],"last_embed":{"hash":"1ybobar","tokens":107}}},"text":null,"length":0,"last_read":{"hash":"1ybobar","at":1753423520771},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{5}","lines":[78,78],"size":243,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06865945,-0.03306466,0.01980903,-0.00940043,-0.02142722,0.04745104,-0.01812302,0.03600873,0.03744739,-0.00027317,-0.01309359,0.0084718,0.07660484,0.02051834,0.00407345,-0.04168979,0.00124292,0.02767805,-0.05521682,-0.02548949,0.06988478,-0.01163379,-0.05191376,-0.06979314,0.06763721,-0.01189437,-0.01569171,-0.06567142,-0.05054238,-0.22295587,0.03459662,-0.00819888,0.03618077,-0.03959863,-0.06008235,-0.0297482,-0.02529184,0.07515718,-0.09450419,0.03295727,0.01460204,-0.00964482,0.02386506,-0.01557969,-0.00041187,-0.0277762,0.00437213,-0.0040736,0.01445199,-0.04154264,-0.0638439,-0.00723775,-0.00402423,0.03400578,0.02620802,-0.00395873,0.02040277,0.10636495,0.03926495,0.01581987,0.02319415,0.0463569,-0.18551809,0.06561865,-0.05497926,0.03427109,-0.03605986,-0.0321287,-0.00049897,0.10057594,0.02607581,0.03258952,-0.0401058,0.04978461,0.02582399,-0.0661171,-0.03025758,-0.0800516,-0.01791669,0.02682596,-0.06021219,-0.03425399,-0.00884068,-0.00276003,0.02023824,0.0334748,0.03605605,0.02253923,0.07100145,-0.05644359,0.06793729,-0.00167797,0.01881309,0.06318194,-0.00658346,-0.00535671,0.00114599,0.00210466,-0.01629798,0.14696467,0.02091659,0.01445441,-0.01653875,-0.02003184,0.02999047,-0.04590178,-0.01886536,-0.04742535,-0.01144323,0.05629937,0.03432322,0.00178987,0.0562416,-0.04907043,-0.02790934,0.05677459,-0.00146109,0.04952752,-0.01118369,0.02556512,-0.07029457,-0.0101344,0.0136527,-0.01357617,0.02900977,0.01154342,0.04226624,0.07025919,0.0155566,0.05576535,-0.00055747,0.01008312,-0.18258744,-0.03466526,-0.01491505,0.00712662,0.01176984,-0.0313323,0.00056428,0.03881215,-0.03709649,-0.00181548,0.02574247,-0.10390541,-0.00720175,0.09777603,-0.00967122,-0.01462689,-0.00207059,-0.0380385,-0.0074853,0.02383064,-0.03217926,-0.06144476,-0.0018126,0.00966468,0.05437567,0.04817061,-0.07190457,-0.00090208,-0.02995459,-0.03523061,-0.00893087,0.13735285,-0.02758438,-0.08402445,-0.05416067,0.02898504,-0.03085074,-0.04795416,-0.05819095,0.04070009,-0.03563874,-0.0115705,0.06191107,-0.00022725,-0.11364082,-0.05726436,-0.02600111,-0.01934768,0.03622977,-0.01068685,-0.03792274,0.01038227,-0.02077873,-0.08947543,0.04127835,0.00072611,0.0276008,0.06088654,-0.06403696,-0.04217389,-0.03928765,-0.01084866,-0.00042901,-0.01061248,0.01268543,-0.02161006,0.03190091,0.01038037,0.0228503,-0.0111796,0.01772494,0.05002612,-0.06247884,0.06364883,-0.00779756,-0.05624366,0.12027897,0.04066966,-0.05458086,-0.00112284,0.06270307,0.06388144,-0.01114495,-0.01428411,0.01980129,0.00236967,-0.01668146,-0.01336356,0.01714228,0.02607908,-0.05459626,-0.19679476,-0.01817217,-0.03791204,0.0441819,0.0132474,-0.02587562,-0.02326828,-0.01897859,0.0375857,0.11828757,0.08939023,-0.05789997,-0.0051701,0.06933948,0.007886,0.00981811,-0.07799819,-0.06866635,-0.05981034,-0.00049983,0.00841956,0.00805974,0.04003697,-0.06293871,0.04343769,-0.01935448,0.12631421,0.01684829,0.02998607,0.03556704,0.04613686,-0.01079167,-0.01133207,-0.02802143,-0.01212277,0.08079169,-0.03695951,-0.01657797,0.00669668,0.01202193,-0.03686833,0.04007744,-0.02816476,-0.11046368,-0.03813173,0.04649058,-0.00594514,0.07392465,0.02831664,0.06334979,0.04716409,-0.03772474,0.04843568,0.02168825,0.06816129,-0.05148955,-0.07604489,-0.03075274,0.00495357,0.02300089,-0.01702335,-0.07816547,0.03222132,-0.04733707,0.05409445,0.02503027,-0.02459235,-0.01352428,-0.01329526,-0.00509517,-0.01172417,0.02808217,-0.03663992,0.02270858,-0.03262413,0.05008744,0.02222014,-0.02586568,-0.0196073,-0.03878835,0.04529828,-0.02606077,0.045756,0.06055128,0.04374029,0.05457644,0.04124288,-0.00061681,0.0224322,-0.01330407,-0.01176507,0.00295082,-0.04443542,0.06281679,0.02601595,-0.00073281,-0.24701557,0.02645455,0.01042638,0.03336147,0.00250885,0.00800881,0.04099325,0.02203846,0.0673642,-0.06368482,0.03858783,0.06498959,0.01221992,-0.04485073,-0.04667215,-0.00770871,-0.01041408,-0.03167227,0.10837344,0.03500403,0.08068492,0.03330719,0.25194213,0.0027659,-0.00238318,-0.00063113,0.00778809,-0.02316475,-0.07445764,0.03243096,0.01862019,-0.04346393,0.09547532,-0.00149046,-0.03617604,0.01041886,0.00071315,0.03864786,0.01353012,0.00842051,-0.06958589,-0.00124712,-0.02320511,-0.03095398,0.12141003,0.01002015,-0.05457228,-0.06686486,0.03486026,0.08208621,-0.08453059,-0.05533785,-0.03841329,-0.03436599,0.01658591,0.05328151,0.02232995,0.01981526,0.01553633,0.00751447,0.02081505,-0.01983489,0.02844232,0.01593911,0.02308941],"last_embed":{"hash":"1hdh79w","tokens":203}}},"text":null,"length":0,"last_read":{"hash":"1hdh79w","at":1753423520837},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{9}","lines":[82,82],"size":510,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04927463,-0.00145142,-0.00330855,-0.02391935,-0.03803319,0.05849448,0.01608423,0.07038839,0.05267014,-0.00652367,0.01336171,0.03982211,0.06466002,0.02067696,-0.01605773,-0.02515975,-0.00277992,-0.01716688,-0.08353008,0.04729013,0.10132954,-0.06502376,-0.04921621,-0.06468567,0.10041513,0.00490277,0.0234206,-0.06787351,-0.0396818,-0.24535391,0.03471931,0.00826763,0.01205975,-0.04083353,-0.02803301,-0.01981404,-0.03912942,0.08263291,-0.06840249,0.03536381,0.02409347,0.0023113,0.0438051,-0.03552536,-0.00796377,-0.01235409,-0.02437278,-0.02610238,0.02813771,-0.02182883,-0.03744671,0.00568166,0.03178276,0.05924027,0.03248252,-0.0185107,0.02549553,0.09154256,0.05183797,0.01283396,0.01864058,0.02380257,-0.1864071,0.03785145,-0.05598253,0.0480609,-0.03944393,-0.01827177,0.0063517,0.06460058,0.03309141,0.0342453,-0.04189451,0.02328073,0.00659319,-0.07513809,-0.03820856,-0.03910289,-0.03128074,0.00175809,-0.05191167,-0.04023835,-0.01031741,-0.03144543,-0.00177827,0.01529744,0.0272836,0.01163379,0.05620644,-0.05130244,0.02861986,-0.01379147,-0.02381241,0.05599394,-0.01214595,0.03565005,0.02626873,-0.00593203,0.00296631,0.13870768,0.01278077,0.01932032,-0.04789116,-0.01575791,0.01924415,-0.05441318,-0.0058206,-0.02605524,-0.03099634,0.05539268,0.05269875,0.01401991,0.04918864,-0.04121976,-0.0257114,0.03866167,0.01004998,0.04915563,0.01528393,0.01716174,-0.05116937,0.00473282,0.01342883,-0.02473281,0.02616599,0.0457411,0.05162828,0.07799775,0.02378297,0.04867975,0.01551993,-0.01583374,-0.15595864,-0.02458096,-0.00811638,0.00881189,0.02844449,0.01366502,-0.00105373,0.0537545,-0.02648068,-0.0011624,-0.00318232,-0.09830039,-0.0059257,0.08899342,-0.02330499,0.01723322,-0.01273729,-0.06442036,-0.01746275,0.05859833,-0.04289474,-0.08251821,-0.01883672,0.03226605,0.02817679,0.03831913,-0.04457342,0.01643451,-0.00765543,-0.04664381,-0.00730418,0.07511226,-0.01854708,-0.03999543,-0.04878218,-0.00806953,-0.03979412,-0.04466397,-0.04870312,-0.00053255,-0.03923513,-0.02618525,0.06562111,-0.01506034,-0.13490617,-0.06430713,-0.02704947,-0.02894654,0.03599514,-0.00338073,-0.03930928,-0.01677773,-0.03175354,-0.05104468,0.01938048,0.0148573,0.04748039,0.05827035,-0.06233471,-0.01244797,-0.05431116,0.02014348,0.01173371,-0.01722606,0.00627577,-0.01177437,0.01632102,-0.02176921,0.00036817,-0.01340897,0.01029249,0.04918552,-0.0569969,0.06871327,-0.00080756,-0.04859011,0.14640602,0.00608932,-0.06343815,-0.02753324,0.06062128,0.06849965,-0.00925207,-0.02573853,0.01011854,0.01502198,-0.02360979,-0.01357585,0.00739495,0.03211295,-0.06563119,-0.20524505,-0.02983105,0.00286428,0.04866851,0.04069617,-0.01995828,-0.05921553,0.01127236,0.02267547,0.16958809,0.07204825,-0.03104517,-0.02718444,0.06200123,-0.00046955,0.03615081,-0.06996311,-0.00477854,-0.05133747,-0.00774764,-0.00415567,0.03658097,-0.0052193,-0.05471742,0.03447086,-0.01874115,0.11432415,-0.02284265,-0.0007596,0.01162975,0.04965037,0.00119169,-0.02238824,0.00466198,-0.01206919,0.04647177,-0.06282262,0.02209075,-0.03549679,-0.00205312,-0.02935333,0.00501319,-0.02782823,-0.11264422,-0.02246795,0.06152476,-0.02377181,0.09208038,-0.00585514,0.06396937,0.03306132,-0.03153538,0.03794733,0.00285874,0.0700795,-0.08456875,-0.1052001,-0.0178967,0.00496401,0.03946508,-0.01141687,-0.09424046,0.03935584,-0.01805905,0.05223866,0.03569955,-0.00941731,-0.0178399,0.01289769,0.00838097,-0.01243239,0.03915215,-0.01882456,0.02960976,-0.04499709,0.0694764,0.01398828,-0.0016616,0.00012279,-0.05802275,0.08206472,-0.02887468,0.04867545,0.0560961,0.0256339,0.08182057,0.03528586,-0.00874996,-0.00067717,-0.02321154,-0.00288666,0.04214833,-0.03815276,0.03215051,-0.02151575,-0.02337337,-0.23870216,0.01547739,-0.00621572,0.04794717,0.005755,-0.00914059,0.02575105,0.05155297,0.04499561,-0.06742495,0.04403405,0.04867409,0.04123618,-0.07569493,-0.03296021,-0.00024727,-0.0396626,-0.03382672,0.07104701,0.04571829,0.08405472,0.04081767,0.23823342,0.00325269,0.00172986,-0.02865306,-0.00018854,-0.03623239,-0.07342675,0.0270361,-0.00027567,-0.01666901,0.098116,0.00144905,-0.02314645,0.04182847,-0.05068107,0.04809379,0.01732583,-0.01186757,-0.04083795,-0.01592247,-0.03112252,-0.03836489,0.13339414,0.01003981,-0.05632645,-0.07730501,0.03800198,0.08748934,-0.0674745,-0.01308815,0.00935664,-0.04439294,-0.00510658,0.0494971,0.00180791,0.02955644,0.02932858,-0.00222656,-0.00162585,0.0317015,0.00944852,0.00075487,0.04309808],"last_embed":{"hash":"3hr10i","tokens":158}}},"text":null,"length":0,"last_read":{"hash":"3hr10i","at":1753423520927},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{10}","lines":[83,84],"size":478,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05693283,-0.01928463,0.014848,-0.00915705,-0.04424197,0.07501819,0.00351255,0.03358128,0.03258251,-0.0176186,-0.00602714,0.00034794,0.06345915,0.00795925,-0.00093942,-0.04254228,-0.01119668,-0.01692728,-0.07282995,-0.01599684,0.13342473,-0.04644415,-0.04556341,-0.06593773,0.0798905,-0.01402472,0.01175118,-0.0791048,-0.01777848,-0.22581445,0.03629448,-0.03472965,0.01932968,-0.05320586,-0.0218529,-0.02364119,-0.01617374,0.05465814,-0.1027271,0.0426747,0.01942563,0.00149518,0.03790726,-0.05115019,-0.00783362,-0.0107938,0.01158123,-0.02985423,0.00822479,-0.04357398,-0.03535052,-0.00430042,0.03495003,0.03129358,0.00936718,-0.01561266,0.01168304,0.10353778,0.03978132,0.01854408,0.01495111,0.01975073,-0.19287096,0.05082243,-0.0323047,0.05932872,-0.02532845,-0.03730793,0.00730228,0.08770499,0.03915698,0.01502953,-0.02330252,0.02967004,0.03583948,-0.07905357,-0.04829455,-0.02717294,-0.02580811,-0.00834692,-0.05968597,-0.01594868,0.00568456,-0.02199535,-0.01181921,0.00733709,0.04009727,0.01324908,0.03091174,-0.05416951,0.01151253,-0.00702778,-0.00252684,0.08013052,-0.0166116,-0.00568185,0.01812353,0.00152396,-0.00758739,0.14164473,0.0131448,-0.00329455,-0.00308693,-0.01241681,0.0386723,-0.04292535,-0.01480693,-0.04685185,-0.01574681,0.03225519,0.05738504,0.01355593,0.05125562,-0.03553499,-0.04048532,0.03825437,0.01331362,0.03815881,0.0174457,0.02699571,-0.06289207,-0.0320674,0.02643631,-0.02067344,0.03361856,0.04398429,0.0509665,0.0921526,0.02617435,0.06310073,0.00068717,-0.02830232,-0.14240004,-0.03527288,-0.00906729,0.02010677,0.02288876,-0.01638809,-0.02940636,0.03202708,-0.0308239,0.00305573,0.02529825,-0.08508854,0.00527072,0.09181338,-0.0142538,0.00255673,-0.01143576,-0.04901012,-0.03591174,0.03601746,-0.04713631,-0.05099965,-0.00418528,0.03892744,0.01431853,0.05689133,-0.07272851,0.03682736,-0.00835345,-0.02753084,-0.03406011,0.12355024,-0.01831717,-0.03494294,-0.01428551,0.01577069,-0.0196456,-0.0604483,-0.0580778,0.0260391,-0.036489,-0.02344118,0.07448406,-0.01300067,-0.12247405,-0.03289733,-0.04700407,-0.0188466,0.01920461,0.00470921,-0.02834903,0.00135392,-0.03112088,-0.07542382,0.0528041,0.00337563,0.02819142,0.06996743,-0.06445503,-0.00586593,-0.04540109,0.01061879,0.03348714,0.00697326,-0.01797639,-0.02665061,0.00988148,-0.02700794,0.04715896,0.01149347,0.01071997,0.05335804,-0.05520975,0.06539362,-0.009239,-0.03050235,0.12176554,0.0093852,-0.06570666,-0.01841015,0.06340364,0.07440567,0.01669245,-0.00439353,0.01328906,0.01295049,0.00121677,-0.01817001,0.01245647,0.06078872,-0.04323347,-0.21399535,-0.02875781,0.00274807,0.02894661,0.0161989,-0.0103705,-0.03753335,0.00317158,0.03129966,0.13278443,0.06595135,-0.02610824,-0.01900355,0.05290704,-0.00821005,0.02134056,-0.07804656,-0.01171468,-0.05744587,0.0029045,-0.00107507,0.02235542,0.00262091,-0.07780824,0.01648492,-0.02800109,0.11664683,-0.01557692,0.00561409,0.02741909,0.04075599,-0.00534593,-0.0120295,0.00114201,-0.0133617,0.09751061,-0.08521096,-0.00149151,0.00271687,0.00404343,-0.05196421,0.04640036,-0.03189934,-0.12563105,-0.04954686,0.0497714,-0.02168428,0.07681251,0.00970439,0.06293679,0.03333381,-0.03732786,0.01144203,0.03796156,0.10482271,-0.05494354,-0.10168225,0.00166318,-0.00067909,0.02498689,-0.018684,-0.08690892,0.03794054,-0.02650875,0.04691934,0.03020902,-0.02861364,-0.02994134,0.01465043,0.00438146,-0.02148364,0.0568496,-0.03116058,0.01838132,-0.06334716,0.06765426,0.02986196,-0.01407596,-0.03104442,-0.04431844,0.04737758,-0.02290972,0.04355463,0.03634936,0.04586861,0.09571087,0.01939816,0.00112415,-0.00773844,-0.0163686,-0.00856876,0.00645337,-0.02363805,0.04504531,0.01238602,-0.01534146,-0.25283265,0.02678079,0.00833387,0.06783012,-0.01149927,0.00109743,0.03578812,0.04881929,0.02932739,-0.09050035,0.02471717,0.05926681,0.02685757,-0.04643908,-0.02046332,-0.00656388,0.00184818,-0.05907031,0.07565337,0.04134623,0.06513545,0.04977367,0.2380857,0.00939249,-0.00139086,-0.00465774,0.00081155,-0.03744622,-0.08196767,0.05986901,0.01189232,-0.04734649,0.10590149,0.00592016,-0.00800288,0.01490653,-0.02371146,0.02103155,0.03221487,0.02565397,-0.05110068,0.01091105,-0.04557307,-0.06566612,0.13341142,0.0591392,-0.06425196,-0.04402275,0.01918901,0.06426691,-0.05395491,-0.02365123,-0.00611118,-0.05449009,0.01209239,0.0327142,0.01458781,0.02850865,0.00152379,0.01577227,0.03047396,-0.02415899,-0.00221666,0.00641274,0.01642747],"last_embed":{"hash":"16e4vqz","tokens":123}}},"text":null,"length":0,"last_read":{"hash":"16e4vqz","at":1753423521009},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{12}","lines":[87,87],"size":309,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07066455,-0.03803881,0.03071722,-0.05067839,-0.03682207,0.07303113,-0.00886758,0.03883858,0.04640857,-0.02398219,0.00663674,-0.01909507,0.07363965,-0.00513316,0.03033393,-0.01323884,0.00721589,-0.02306569,-0.10288878,0.00680392,0.13010211,-0.04335637,-0.05510142,-0.09846575,0.06413643,-0.0332425,0.00635038,-0.07286873,-0.01982512,-0.23288943,0.05504389,0.00661108,-0.01285581,-0.05724305,-0.04927228,-0.02632283,-0.02808725,0.02639515,-0.11133533,0.03591257,-0.00312776,0.02333549,0.05068744,-0.07089186,-0.02275954,-0.00242646,0.00976677,-0.03201737,0.00644047,-0.02865976,-0.03882045,0.03825604,0.02401083,0.03329282,0.02414647,-0.00747687,0.00897834,0.11285036,0.01934792,0.00860652,0.00611523,0.03348915,-0.17773497,0.04924487,-0.04462795,0.05469452,-0.02874374,-0.04218912,0.00849595,0.10629608,0.04498653,0.0567934,0.00070877,0.05052068,0.01729039,-0.05655544,-0.06684896,-0.05619202,-0.00898418,0.01268686,-0.04952336,-0.02580005,0.00039453,-0.02124366,0.02114536,-0.01647019,0.0185601,0.00370238,0.02478979,-0.03446921,0.02795848,-0.0007391,0.00236871,0.06861072,-0.00123471,0.02342375,0.00064505,0.00885144,-0.02355091,0.13768362,0.01883495,-0.00206557,-0.05681755,0.0081918,0.02274538,-0.02914514,-0.0070217,-0.03110238,-0.02930145,0.02506012,0.01755155,0.02328008,0.04678305,-0.02976076,-0.05390335,0.05125622,0.02430353,0.03984771,0.01421662,0.02182076,-0.05609623,-0.01526839,0.03971653,-0.04499309,0.04018419,0.0446269,0.04123889,0.07157404,-0.01932732,0.05736561,0.01784401,-0.03289207,-0.13737556,-0.01811298,0.00588142,0.01609493,0.02037667,-0.00761358,-0.00246866,0.03739983,-0.03881782,-0.02411573,0.04479599,-0.09417162,0.01562443,0.10537931,-0.01015672,0.00411973,-0.01953858,-0.05433916,0.00152215,0.02387494,-0.03399946,-0.06544126,-0.01217246,0.03962089,0.07221539,0.01403245,-0.05243906,0.00546291,-0.01781369,-0.01071808,-0.03610052,0.09409864,-0.0275538,-0.03347168,-0.0342485,0.02528602,-0.03830414,-0.01252059,-0.05032945,0.02261375,-0.04067042,0.00002298,0.08261265,-0.01625807,-0.09568524,-0.01765658,-0.06109029,-0.00327279,0.0184685,-0.01711299,-0.02550659,-0.00295996,-0.04162373,-0.07116003,0.05730113,-0.00940375,0.0124407,0.04478301,-0.05601573,-0.00412422,-0.03517556,-0.01757275,0.02572731,-0.00203675,-0.0037694,-0.03644874,0.01009845,-0.01046749,0.02213506,0.00293985,0.01436171,0.05728177,-0.03412789,0.06252691,-0.01556872,-0.04801638,0.12580986,0.02007775,-0.05099998,-0.02746873,0.07056096,0.07379138,-0.01159731,0.02193025,-0.01781559,0.00179589,0.00481948,-0.00463535,0.02380078,0.06589772,-0.05102803,-0.20120905,-0.00877954,-0.01939653,0.03652357,0.01990671,-0.00519304,-0.04115197,-0.00575629,0.01873113,0.1463367,0.07994195,-0.03677766,-0.00824007,0.04998963,-0.0304977,0.01162784,-0.08944782,0.01287842,-0.06099452,0.02580577,0.01556321,0.00072465,-0.02074663,-0.0694117,0.04253556,-0.03686188,0.13331278,-0.01501689,0.01326956,-0.00641138,0.05619905,0.01106311,-0.00084304,0.01169063,-0.02362162,0.07499455,-0.07801092,-0.00043735,0.03034671,0.01767124,-0.01212166,0.03335645,-0.06141733,-0.11686864,-0.01348479,0.02328748,-0.02732546,0.11120006,0.02990423,0.02343139,0.03377489,-0.05467311,0.01042252,-0.0094098,0.08846005,-0.07925802,-0.09889244,-0.00386012,0.00628576,0.01544528,-0.01619725,-0.07725982,0.04402687,-0.04183022,0.01039118,0.03539177,-0.00283169,-0.03187771,-0.00506353,-0.00159594,-0.01192083,0.01623375,-0.03840514,0.04376464,-0.06520816,0.02982634,0.02464174,-0.00331682,-0.04014542,-0.07923987,0.0637084,-0.03074441,0.04560532,0.03620848,0.05151137,0.07339321,0.03677133,0.02727695,0.01743184,-0.00480322,-0.00104038,0.0546848,-0.00639012,0.04110274,0.01365938,-0.04332555,-0.23497403,0.03308519,0.00192421,0.06211536,-0.01565157,0.00981784,0.03526541,0.04288838,0.00240937,-0.08007386,0.00790573,0.05970021,0.02289305,-0.01287042,-0.03357288,0.00486712,0.00431051,-0.06311247,0.08564822,-0.00169939,0.08590614,0.05362445,0.23750165,0.00928999,-0.00812743,0.00383188,-0.00310279,-0.01597292,-0.06958102,0.05796369,0.01013136,-0.00450358,0.12916586,-0.00218735,-0.00632618,0.03851362,-0.04138508,0.02267218,0.02773263,0.03394662,-0.05183811,-0.01365849,-0.04323351,-0.03639637,0.12455823,0.00265589,-0.05931902,-0.06495874,0.00757997,0.06453972,-0.09590265,0.00547152,-0.01330345,-0.03640644,0.02387935,0.02829999,0.02274486,0.01430287,0.00173796,0.02100252,0.03661392,-0.00752622,0.02403408,0.00560926,0.0237585],"last_embed":{"hash":"1646knn","tokens":110}}},"text":null,"length":0,"last_read":{"hash":"1646knn","at":1753423521071},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{13}","lines":[88,88],"size":234,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08875351,-0.03524414,0.0000068,-0.03275372,-0.04466501,0.05680812,-0.0118358,0.02071859,0.05979082,-0.01387049,0.0153357,0.01529179,0.06667398,0.00033201,-0.00945309,-0.04695753,-0.00990619,-0.0559924,-0.07262862,0.00841592,0.09573472,-0.02699964,-0.06010348,-0.07840786,0.08117423,-0.01634333,-0.0018086,-0.08596536,-0.02067867,-0.21616361,0.05238334,0.00074893,0.0288026,-0.04346827,-0.05039732,-0.04871714,-0.01720166,0.05683511,-0.06504489,0.05570291,0.01382492,0.02530388,-0.01597901,-0.07941457,0.006959,-0.00744015,-0.00230013,-0.03589809,0.05158811,0.00387164,-0.03686295,0.00550862,0.04066469,0.04181159,0.01378922,-0.05228195,0.0285992,0.08193469,0.06047899,0.01969823,-0.0121883,0.00046681,-0.16877872,0.05288874,-0.0702795,0.02032125,-0.0041145,-0.03113993,0.03553069,0.09371111,0.03341391,0.02190008,-0.02825304,0.04446203,0.03114849,-0.08599612,-0.0401628,-0.07005831,-0.01795952,0.00065925,-0.03899896,-0.04275931,0.02676459,0.00192509,0.00136686,-0.00595177,0.06033587,0.02806909,0.05099474,-0.04923332,0.04643362,0.0016784,0.00626535,0.05106602,-0.03052422,0.02592223,0.03879653,-0.01325903,-0.0036884,0.14799805,0.02013648,0.01501511,-0.00118339,0.00692613,0.0425026,-0.05054885,-0.02747592,-0.00536885,-0.02870268,0.03784623,0.02964452,0.01945517,0.08844842,-0.02207843,-0.02960535,0.03452739,0.01089522,0.02342587,0.03497734,0.05314472,-0.06453585,-0.00267638,-0.00818163,-0.0214634,-0.00087183,0.0309079,0.03967029,0.08673912,0.00410849,0.06029413,0.04809393,-0.02765056,-0.13488635,-0.03819542,0.00143569,0.00537578,0.01168614,-0.00981162,-0.03691619,0.03570361,-0.0181094,0.01505668,0.02514926,-0.08786026,-0.01653255,0.06186413,0.03603642,0.01458726,-0.00705013,-0.06196081,-0.01885063,0.03746207,-0.05035287,-0.05166469,-0.0245052,0.02451753,0.0650502,0.07609742,-0.06575778,0.024214,-0.00317266,-0.04653629,-0.01957478,0.06305741,-0.0206482,-0.05527563,-0.02300279,-0.00862293,-0.03686444,-0.06367648,-0.06041205,0.02109274,-0.04481519,-0.01140594,0.11693493,-0.00200583,-0.11805668,-0.05350342,-0.03530343,-0.03442365,0.04420549,0.01746787,-0.03654606,0.01790585,-0.03596941,-0.09351107,0.03766163,0.00146358,0.04305573,0.05452704,-0.02069156,-0.02591185,-0.05615971,0.05489299,0.02435715,-0.02170008,-0.02091154,-0.01954859,0.03299189,-0.01343386,0.01629913,-0.03720577,0.00871036,0.01797008,-0.03312492,0.0666219,0.01497781,-0.02731019,0.13189915,0.03017636,-0.07822289,-0.02522153,0.09328235,0.08264957,0.01213961,-0.0018479,0.00638355,0.00571982,0.00923858,-0.00854325,0.03860485,0.03370744,-0.06896003,-0.1936437,-0.01883554,-0.01284445,0.02720536,0.05997798,-0.01636744,-0.04643633,-0.01569711,0.05016652,0.1225137,0.10027227,-0.06677769,0.00426455,0.0657161,-0.02846849,0.00809755,-0.08826724,-0.01118048,-0.06365976,0.01828186,0.00223523,0.02705606,-0.00044829,-0.06516202,0.0586662,-0.01478179,0.11960094,0.00664397,-0.02778111,0.00345384,0.05227752,-0.00603651,-0.00680317,-0.00904923,-0.02224151,0.05983859,-0.06622911,0.00752723,-0.01139352,-0.00662787,-0.04013641,0.03122571,-0.04125871,-0.09855291,-0.01933076,0.07600557,-0.0413895,0.10357661,-0.01475203,0.03879982,0.07116254,-0.03645379,0.02105989,-0.0014028,0.08958387,-0.06425258,-0.09787057,0.00456425,-0.02598993,0.05406193,0.0094295,-0.06674185,0.02850208,-0.0486229,0.05797448,0.02076066,0.01378582,-0.03781925,-0.01839239,-0.0046704,0.00192529,0.05129619,-0.01738114,-0.00492581,-0.03911021,0.0447929,0.04555813,-0.0107795,-0.04259356,-0.02918084,0.07132155,-0.04272676,0.01973776,0.07129963,0.01742804,0.08550572,0.03516488,0.01438119,-0.04919289,-0.01627301,-0.01787885,0.00236792,-0.05444143,0.04197709,-0.01155427,-0.0178656,-0.23824057,0.06027661,0.00725103,0.05453265,-0.01423319,-0.00390458,0.02544715,0.02699122,0.04092522,-0.07914256,0.04639616,0.07455124,0.02071346,-0.03815815,-0.01049995,-0.02008984,-0.0285389,-0.0368454,0.07440752,0.01618494,0.07181131,0.0321616,0.2169383,-0.01786312,0.01008036,-0.00930722,-0.0245671,0.00290256,-0.04086512,0.03725323,-0.00868711,-0.03328142,0.08865811,0.0034387,-0.01479701,-0.00051257,-0.05012428,0.05443528,0.02991159,0.00414633,-0.05765147,-0.01133286,-0.02734889,-0.02756733,0.12281989,0.04745909,-0.05898213,-0.07855033,0.03087373,0.08347812,-0.0734164,-0.05097081,-0.01241896,-0.03724054,0.00905826,0.03683661,-0.00388425,0.0305274,0.01664641,0.0074197,0.00998309,-0.00713676,-0.00038427,-0.01914782,0.0082813],"last_embed":{"hash":"16i7y0w","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"16i7y0w","at":1753423521110},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{14}","lines":[89,89],"size":288,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09655575,-0.01760176,0.00822703,0.00480592,-0.02353826,0.04492692,-0.00631106,0.00850864,0.02729737,-0.02088281,0.01437013,-0.00954299,0.07374981,0.00420445,0.0277428,-0.02308327,-0.01149314,-0.0041616,-0.04620626,-0.0104593,0.10366767,-0.02788289,-0.06958849,-0.0649177,0.07711258,-0.01547344,0.01299668,-0.07000796,-0.01254949,-0.20335992,0.05377662,-0.00532232,0.00741076,-0.05320603,-0.05672978,-0.0127181,-0.004396,0.0682206,-0.09188399,0.05851468,0.01060422,0.00409633,-0.00046187,-0.05966594,-0.00636619,-0.0204005,0.00893366,-0.00351242,0.00991329,-0.02646327,-0.0677059,-0.00019312,0.0079297,0.03288579,0.00503818,-0.01989186,0.04325772,0.0973153,0.03453142,0.00748236,-0.0053545,0.01870972,-0.18394302,0.06303313,-0.05132966,0.05008148,0.01347347,-0.03121834,0.01657776,0.09447949,0.03727546,0.04148405,-0.03839338,0.05980237,0.02931909,-0.06679662,-0.02617438,-0.07764427,-0.00726326,0.02772062,-0.03977263,-0.03761281,0.02075529,-0.00093966,-0.01162952,-0.00262421,0.03441596,0.00845715,0.03880606,-0.05062393,0.0432284,0.01257123,-0.00438611,0.0687521,0.00580739,-0.0071338,0.03377245,0.01992517,-0.00047577,0.17078716,0.02968416,0.03427984,-0.01410748,0.0019377,0.04475541,-0.05887869,-0.0223192,-0.02840662,-0.0189881,0.05529753,0.01387705,0.03761093,0.0691558,-0.04215123,-0.03943096,0.02653589,-0.02093983,0.03848764,0.04435832,0.0322729,-0.07413033,-0.01406558,0.01825341,-0.02441353,0.01521427,0.01397089,0.01218553,0.08669171,-0.01156875,0.03982681,0.01840439,-0.02273084,-0.16106243,-0.04209775,-0.02280023,0.01866464,-0.01881917,-0.03136178,-0.04251652,0.05672182,-0.05198719,0.00071586,0.05898206,-0.09843373,-0.00865024,0.07951956,0.03233499,0.02435765,-0.0362062,-0.0589451,-0.00854748,0.03505208,-0.04391159,-0.05544072,-0.00587362,0.02121774,0.06321388,0.0433842,-0.08305383,0.0132482,-0.03342623,-0.01824495,-0.02908696,0.08765276,-0.0067907,-0.04640018,-0.01604682,0.00771964,-0.03523486,-0.03877253,-0.04866602,0.03397628,-0.05189902,-0.02362281,0.08241738,-0.01254674,-0.11660425,-0.04829542,-0.0547318,-0.02015809,0.04408646,-0.00440389,-0.03299366,0.01978886,-0.01748803,-0.07407707,0.03957143,-0.00574701,0.04158087,0.04559085,-0.06201681,-0.01936491,-0.04537503,0.01833889,0.01353523,-0.03041918,-0.01624466,-0.0243606,0.02101606,-0.0086121,-0.01203743,-0.0201602,0.02882622,0.03712285,-0.04306835,0.09322424,0.0107106,-0.04059704,0.11248986,0.01722869,-0.06288359,0.01651413,0.04274556,0.05297932,0.01863774,0.00426098,0.01768918,-0.00294979,-0.01977754,0.01625133,0.01535488,0.05334119,-0.06764958,-0.18978088,-0.02108558,-0.02390497,0.05245924,0.03395213,-0.01097746,-0.05072324,-0.03841748,0.03932603,0.12202539,0.07029331,-0.05317959,0.00439646,0.07494048,-0.0238145,0.01079293,-0.07284707,-0.01656515,-0.08136226,0.0012378,0.01000926,0.01402683,-0.00910439,-0.06955595,0.01165051,0.00754948,0.12983295,-0.02205693,-0.02073902,0.0175049,0.03229278,0.00267129,-0.03602582,-0.02100025,-0.03034597,0.05736447,-0.05183644,-0.00572302,0.01492085,0.01069818,-0.04134326,0.03837728,-0.03486253,-0.06689978,-0.03477891,0.0858798,-0.00748894,0.1004334,0.01465266,0.01614138,0.07586965,-0.01952347,0.02951088,0.02211505,0.07101638,-0.07076769,-0.09441517,-0.00317838,-0.00605097,0.02308299,-0.01038355,-0.05929508,0.03017659,-0.02378892,0.03665396,0.00552613,-0.00426846,-0.02714815,-0.03576519,0.00449111,-0.00132182,0.08147043,-0.04193146,-0.00987832,-0.04794824,0.04331005,0.03636502,-0.02276713,-0.02415294,-0.04856117,0.07809644,-0.04933915,0.04440265,0.07906398,0.04429663,0.05594601,0.03350505,0.00739619,-0.02744616,-0.00895691,-0.02511427,0.02529048,-0.0604182,0.0533678,-0.00302174,-0.01139442,-0.25417149,0.04026022,0.00921276,0.05801533,-0.024183,-0.00237022,0.01325843,0.0284154,0.01473234,-0.08543139,0.04016229,0.06743261,0.03648319,-0.03800539,-0.02680429,0.00071139,0.01612574,-0.04849575,0.08011967,0.01307504,0.08243655,0.0634224,0.24764463,-0.00600869,0.00279229,-0.00978935,-0.00279127,-0.01230407,-0.05841051,0.03087614,0.01750975,-0.01024644,0.10935795,-0.00637964,-0.01092941,0.00735869,-0.04419563,0.06595115,0.01761998,-0.00205962,-0.05123245,-0.00003291,0.00004289,-0.04082003,0.14266987,0.01795767,-0.06522802,-0.06651196,0.02843878,0.08162608,-0.09773677,-0.05056242,-0.03897326,-0.00323404,0.02754916,0.03560008,0.00527963,0.01470574,0.01225667,0.02541806,0.02740642,-0.00231205,0.00182149,0.0030677,-0.00900145],"last_embed":{"hash":"rg6yzr","tokens":96}}},"text":null,"length":0,"last_read":{"hash":"rg6yzr","at":1753423521167},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{15}","lines":[90,91],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.068459,-0.02576066,-0.00411899,-0.01533147,-0.01681653,0.07068101,0.00331095,0.05216925,0.04138697,-0.00667987,-0.00137212,0.00715861,0.09521071,0.01007409,-0.00468382,-0.04049802,0.00596254,0.00324696,-0.07661494,-0.02597341,0.06921244,-0.04785899,-0.05157033,-0.06576645,0.06767695,-0.02072949,-0.00133889,-0.06593236,-0.05040434,-0.25737089,0.02253729,-0.00663781,0.02558235,-0.05523854,-0.05766356,-0.02829297,-0.02187783,0.08426515,-0.0865384,0.04401504,0.0160492,-0.00157778,0.02789615,-0.03566962,-0.0107629,-0.01110385,0.01264186,-0.01953799,-0.00173662,-0.02510011,-0.07053158,0.00451689,-0.02550067,0.04359832,0.01367908,-0.00235163,0.03544511,0.09582247,0.02731512,0.03569434,0.02456003,0.03667407,-0.17796494,0.05855671,-0.02758878,0.02235498,-0.03229427,-0.01825817,-0.02293972,0.09353629,0.04350809,0.02968341,-0.02362123,0.04848205,0.0453428,-0.07063643,-0.05912128,-0.06202359,-0.01625396,0.02469895,-0.04101493,-0.02600297,0.00362278,-0.00260128,0.03370105,0.01991324,0.02812242,0.01645546,0.07365517,-0.04001643,0.05807331,-0.01027401,-0.00096893,0.06136086,0.02993679,0.00199332,0.01281961,0.00139891,-0.00832799,0.12664251,0.02994025,0.02509363,-0.00030094,-0.02283066,0.04595473,-0.05109008,-0.00096898,-0.02415065,-0.01002405,0.05477168,0.03089772,-0.00320084,0.06664063,-0.05737473,-0.02156908,0.04175129,0.01268375,0.03057805,-0.00075852,0.01533971,-0.07372704,-0.02092752,0.01361574,-0.02596975,0.04350413,0.02008111,0.01911844,0.07699268,0.01381014,0.05325535,0.00706975,-0.01389713,-0.16585304,-0.03378659,-0.03008353,-0.02059962,0.01610939,-0.01452481,0.01206832,0.04468804,-0.05003576,-0.00383253,0.01982863,-0.11512168,-0.02722267,0.12756625,-0.02559633,-0.00744895,-0.00540705,-0.03652333,-0.00077446,0.02511802,-0.03595664,-0.08383463,-0.01466377,0.00568288,0.07097327,0.05520372,-0.06459247,-0.00778974,-0.02014429,-0.01355696,-0.01889618,0.09711392,-0.01454497,-0.06690466,-0.05573256,0.04325262,-0.03498656,-0.04595548,-0.03928492,0.01747555,-0.03317779,-0.02612456,0.06520382,-0.00971738,-0.09169988,-0.05246871,-0.04644968,-0.01405281,0.02351991,-0.00001794,-0.04558053,0.00079691,-0.03328387,-0.06619584,0.03632309,-0.01429453,0.04034821,0.0424923,-0.09019965,-0.04074101,-0.03496075,0.01954538,-0.00189488,-0.01158014,0.02579155,-0.02042805,0.01477904,0.01188801,-0.01213065,-0.03298008,0.02445244,0.05270324,-0.05468102,0.05988419,-0.00313523,-0.04435513,0.12773007,0.04011748,-0.04343414,-0.01703613,0.054313,0.07888415,-0.01073161,-0.00913,0.00740475,0.0183536,-0.01943614,-0.00602206,0.04448013,0.04690592,-0.07946621,-0.19717082,-0.01759034,-0.05653639,0.03755366,0.00787973,-0.01281641,-0.027885,-0.01796195,0.02903237,0.12531595,0.07803458,-0.05222678,-0.00331745,0.06381666,-0.00342406,-0.01485385,-0.07680269,-0.00680884,-0.06086683,0.00918911,0.02657398,0.00956389,0.02718256,-0.06249965,0.02952473,-0.02354219,0.13135108,0.02506499,0.04602211,0.02438938,0.04188913,0.02451216,-0.02825179,-0.00677663,-0.02428087,0.06245167,-0.04282013,-0.02036389,-0.00645435,0.02216741,-0.02284973,0.02077538,-0.02596283,-0.13751398,-0.04382056,0.05334264,-0.00561459,0.07751932,-0.00176428,0.04595026,0.04759824,-0.0576319,0.04877372,0.00005789,0.07848425,-0.06484702,-0.08536945,-0.02896231,-0.00279031,0.00855183,-0.02345327,-0.05528972,0.05328581,-0.04490454,0.03905284,0.01113825,-0.01095139,-0.00842058,-0.00909815,0.00996736,-0.00838988,0.05387758,-0.04749551,0.0459966,-0.02803059,0.05478257,0.01075098,-0.01752033,-0.01591876,-0.04464207,0.0671217,-0.04378828,0.0554926,0.04485089,0.04043338,0.04112307,0.05230775,0.01133667,0.01025973,-0.00540347,-0.00630502,0.01289976,-0.02659194,0.05587959,0.02865351,-0.01109412,-0.235365,0.03594878,-0.01888265,0.02719033,0.01189656,-0.00582726,0.02746413,0.01456859,0.05892634,-0.03047761,0.01048912,0.06340308,0.03316239,-0.05060601,-0.02539661,-0.01240328,-0.02388159,-0.04542208,0.09760458,0.03839501,0.08570581,0.03618801,0.2393613,0.00722022,-0.02093968,0.00619884,-0.00630277,-0.01941113,-0.09311965,0.05556162,0.02675611,-0.01500686,0.10165133,0.01351743,-0.02934145,0.00028423,-0.01975537,0.04470306,0.00047155,0.01290154,-0.05864672,0.01106221,-0.01381485,-0.03403756,0.14611378,-0.00110719,-0.04674421,-0.06084425,0.02551698,0.08328985,-0.08960549,-0.04594534,-0.0163935,-0.02720887,0.019402,0.03702715,0.02796829,0.02219453,0.01233453,0.00207765,0.02337115,-0.0076395,0.00689933,0.01383073,0.01588922],"last_embed":{"hash":"1w0eqq8","tokens":450}}},"text":null,"length":0,"last_read":{"hash":"1w0eqq8","at":1753423521212},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{16}","lines":[92,108],"size":2702,"outlinks":[{"title":"Create winning reports, sort them and use them for effective lottery strategies.","target":"https://saliu.com/images/strategy-sorted-reports.gif","line":3},{"title":"Study advanced methods in setting lotto filters to create winning lottery strategies.","target":"https://saliu.com/images/lottery-filter-strategies.gif","line":9},{"title":"The lottery filters are founded on theory of probability, mathematical formulas.","target":"https://saliu.com/images/filter-report.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08612351,-0.0333836,0.00173606,-0.0623108,-0.04103299,0.06726748,0.00574947,0.05022698,0.03454818,-0.00961762,0.01265684,-0.03205752,0.08741626,0.01004394,0.02873176,-0.01227594,0.02727751,0.04152058,-0.08138278,-0.02643169,0.12451749,-0.03103108,-0.070559,-0.06831534,0.10119257,-0.02256794,-0.02107115,-0.07840189,-0.03299488,-0.21169902,0.05715299,-0.03133322,0.03241757,-0.03350817,-0.07139543,-0.01138535,-0.01517833,0.04061227,-0.0810445,0.04278987,0.00824435,-0.00553658,0.01460255,-0.0442913,-0.04461206,-0.01925587,0.0091368,-0.05138054,-0.00381988,0.00796712,-0.06562947,0.02546447,0.00378101,0.01712616,-0.00008165,-0.00130269,0.04924254,0.07275968,0.02406298,0.0662048,0.0256191,0.01978813,-0.16591427,0.05409342,-0.00567734,0.03607693,0.00074198,-0.06763119,-0.02638373,0.06965937,0.03814824,0.04832645,0.02228637,0.05588324,0.03575398,-0.06463796,-0.05894483,-0.09210722,0.006366,-0.01350576,-0.07139086,-0.04373855,0.03034155,-0.01051197,0.04167065,0.00858145,0.02410305,-0.00256084,0.03318003,-0.0370205,0.04154734,-0.01955305,-0.0250138,0.03320155,0.00474335,0.01514083,-0.01058249,-0.01121778,-0.01131102,0.13614379,0.02808044,0.00199826,-0.05640204,-0.00945398,0.06609371,-0.04593746,-0.01349255,-0.02248905,0.00291592,0.0417145,-0.00341164,0.01552191,0.0315315,-0.00943867,-0.02344314,0.0381485,0.00769208,0.00514249,0.02210227,-0.0128451,-0.05118466,-0.02696848,0.01730861,-0.04448698,0.05779604,0.02566367,0.02963175,0.0820897,-0.00902588,0.06246432,0.03281286,-0.04923189,-0.12002049,-0.01605749,-0.00445532,0.01932993,0.00489868,-0.02076852,-0.01478862,0.05879862,-0.04374871,-0.0245468,0.00647744,-0.09278688,-0.04210907,0.11443856,-0.04442717,0.00094446,-0.01065075,-0.07670056,-0.0028331,-0.01619704,-0.04734388,-0.04970537,0.00933608,0.03931371,0.03947196,0.00836761,-0.05418479,-0.01667324,-0.03371985,-0.0056216,-0.03324613,0.13761683,-0.03474178,-0.02554197,-0.01528716,0.02217296,-0.05518829,0.02107131,-0.01646381,0.02893955,-0.04198793,-0.00626251,0.09907789,-0.00892862,-0.0784581,-0.02240399,-0.06113088,-0.01123306,0.01646092,0.00851402,-0.04415048,-0.00878743,-0.01082778,-0.04430191,0.03736118,0.00020945,0.05093696,0.03746837,-0.09129645,-0.00731474,-0.02081034,-0.01688967,0.02112984,-0.036055,0.01206938,-0.01066421,-0.00795562,0.00143108,0.04520435,0.02160448,0.03584371,0.04282202,-0.02593219,0.06951614,-0.0048096,-0.02607825,0.10099508,0.03612441,-0.03417405,-0.03195852,0.0585192,0.0709874,0.03895765,0.01713099,0.02475921,-0.0165416,0.00394349,0.03625639,0.01716319,0.05937989,-0.08344941,-0.21322922,0.03823086,-0.01979911,0.00632588,0.03050835,0.00158904,-0.02828348,-0.01451031,0.03218866,0.1175382,0.10742422,-0.05972114,-0.01093186,0.06640361,-0.01710539,0.01523272,-0.08194784,-0.00247756,-0.06361488,0.01210591,0.03681058,0.02552508,0.01429957,-0.0512059,0.06860524,0.00473797,0.12869623,-0.02915789,0.01993017,0.03775827,0.05953114,-0.00801449,-0.0140752,0.02951623,-0.03633032,0.04024694,-0.08471622,0.01341463,0.0076319,0.01971789,-0.01032113,0.04481273,-0.01625057,-0.1206465,-0.04438916,0.02974744,0.00443967,0.09827579,0.00368662,-0.00884164,0.02033012,-0.02772249,0.01765272,-0.00785158,0.08758518,-0.10330757,-0.09331327,-0.02215146,-0.00898589,0.02817993,-0.01118161,-0.09260611,0.03477138,-0.05485412,0.02720022,0.0174121,0.02830748,-0.03037968,-0.04047761,0.01543738,-0.03025369,0.03478652,-0.048644,0.01789916,-0.06050548,0.06968959,-0.00373989,0.02921093,-0.00089757,-0.03735194,0.05763257,-0.02513575,0.0441211,0.04097512,0.02325741,0.03376177,0.03243339,0.00172283,-0.03590209,0.0229324,0.01681157,-0.01592325,-0.02157165,0.06477235,-0.00471541,-0.06039904,-0.2469988,0.02596979,-0.01586507,0.02070649,-0.00928473,0.00597516,0.01919704,0.02673787,-0.00289617,-0.04410207,0.0190336,0.07115614,0.05978413,-0.02191085,-0.03181398,0.00395337,-0.00456314,-0.05157948,0.09821238,0.00274382,0.06444827,0.0494616,0.23152219,-0.0507932,-0.00359132,0.01313092,-0.03039388,-0.00686406,-0.06510108,0.02609709,0.02687051,-0.00713859,0.1471791,-0.00586528,-0.00860033,0.02473149,-0.06492942,0.01506435,0.04580549,0.01536644,-0.05714829,0.00319121,-0.02538465,-0.03792338,0.13023998,0.0048343,-0.06517407,-0.07136992,0.05439217,0.07175217,-0.07143234,-0.01660405,-0.02536001,-0.02157527,0.02718717,0.02178282,0.01803859,0.03233989,0.01529605,-0.00561842,0.0219975,0.01698288,0.00669879,0.00831243,0.00890726],"last_embed":{"hash":"o0hlfx","tokens":138}}},"text":null,"length":0,"last_read":{"hash":"o0hlfx","at":1753423521422},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{18}","lines":[110,111],"size":287,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{19}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04880885,-0.01976482,-0.0068201,-0.03456201,-0.01875311,0.05738015,0.02514928,0.02070969,0.04424227,-0.00387165,-0.00770637,0.01339448,0.0813457,0.00926814,0.02102144,-0.02371123,0.0050392,-0.01864345,-0.0583004,-0.04055814,0.12038817,-0.03591558,-0.06986895,-0.05796579,0.04823127,-0.02303338,-0.00295661,-0.08650155,-0.03926963,-0.21365632,0.01718648,-0.0134542,0.0406201,-0.0382945,-0.06345958,-0.05468465,-0.03474658,0.07920455,-0.09258621,0.0284562,-0.00762631,0.01138507,0.04130717,-0.0528763,-0.00877451,-0.00328297,0.00590337,-0.0112021,0.04052299,-0.0559666,-0.07246104,0.00965183,-0.01296825,0.041409,0.01092942,-0.00765995,0.01806815,0.09969889,0.02438493,0.03845996,0.02748576,0.02833672,-0.18057612,0.057586,-0.03795882,0.04686598,-0.01550809,-0.02257905,-0.01064675,0.08408825,0.02861553,0.03307902,-0.0235848,0.046022,0.0313306,-0.07315806,-0.04167628,-0.06931725,-0.02039567,0.01234042,-0.05927977,-0.04022063,0.00746247,-0.00100536,0.02187211,0.01342604,0.0407772,0.02322775,0.06232614,-0.062617,0.04803924,0.04029487,0.00183205,0.04094928,0.0206256,0.00878083,0.00305811,-0.01768567,-0.00619593,0.15732805,0.01181226,0.01108853,-0.01189915,-0.00892173,0.04055589,-0.03568536,-0.01027577,-0.04721954,0.00048191,0.04885295,0.0225393,0.00439028,0.08704893,-0.04545273,-0.03131627,0.0513755,0.01906496,0.03404967,0.02007779,0.03513326,-0.06274664,-0.034732,0.01828624,-0.03563447,0.05003083,0.0329509,0.03162211,0.07237684,0.02343198,0.05381222,0.00700797,-0.01760135,-0.17502338,-0.02485699,-0.01857398,-0.00224479,0.02668374,-0.02848296,-0.01116602,0.06706759,-0.06589504,-0.00459369,0.05141766,-0.11348501,-0.01252168,0.11057071,-0.00626969,-0.00374025,-0.01332732,-0.03668413,-0.00978537,0.04495525,-0.0433784,-0.08506673,-0.01485417,0.0145402,0.05824354,0.02444907,-0.07016614,0.01430691,-0.0227689,-0.01815651,0.00080785,0.10061746,-0.00671954,-0.05485405,-0.03385646,0.02677863,-0.04271139,-0.06734197,-0.06190748,0.0295064,-0.05927631,-0.01089461,0.05623422,-0.01092135,-0.08994168,-0.05891158,-0.01238918,-0.02185315,0.02973117,0.00952274,-0.0285257,0.02667442,-0.02027182,-0.06264624,0.03991372,0.01104726,0.04972829,0.04023039,-0.06107626,0.00794244,-0.01866093,-0.00217262,0.00823749,-0.0104528,-0.00581268,-0.040736,0.03172599,-0.00084344,0.0030312,-0.02260436,0.0197763,0.04750292,-0.0505649,0.06593501,-0.00688185,-0.03750139,0.10870167,0.01370261,-0.06146475,-0.00045508,0.06187051,0.06286109,-0.01718789,-0.00966802,0.00864922,0.01185557,-0.01328496,-0.00759758,0.01864706,0.03644803,-0.06638291,-0.175126,-0.03012045,-0.01435763,0.04016041,0.01851579,-0.00566047,-0.03951329,-0.02029546,0.0433183,0.14937644,0.07489356,-0.03517311,-0.01002948,0.07537639,-0.0154171,0.01582733,-0.07265304,-0.01779694,-0.0684842,0.01653115,0.02784352,-0.01031078,0.00331459,-0.06161189,0.04384342,-0.03032141,0.13882026,-0.01201191,-0.00399646,0.04376438,0.0408119,0.00790261,-0.00651247,0.01820883,-0.01519369,0.07402897,-0.06229115,-0.03406411,0.00386489,0.00714043,-0.03499321,0.0464349,-0.04333731,-0.10754424,-0.04467703,0.06325271,-0.00209825,0.09166127,0.00548791,0.03500295,0.04574118,-0.04205611,0.03870492,-0.00335245,0.07347321,-0.07180747,-0.09050477,-0.00519651,0.00926631,0.01512714,-0.03305492,-0.06255145,0.02966997,-0.03627835,0.0504881,0.02521456,-0.03004634,-0.01821147,-0.00993799,-0.01206528,-0.01312119,0.05387981,-0.02687245,0.00852574,-0.05802859,0.04677727,0.02261669,-0.03609629,-0.03592891,-0.04965726,0.0777802,-0.03479367,0.04142429,0.07176212,0.02901088,0.04922643,0.04911558,-0.00229589,0.00566397,-0.01713497,0.00532461,0.01489418,-0.03420031,0.03451725,-0.0033714,-0.01079154,-0.23459366,0.0192684,-0.02349716,0.05383821,-0.00118024,0.00982001,0.0450136,0.04828174,0.06620893,-0.04991603,0.03982304,0.06504725,0.01448725,-0.0384074,-0.01816493,-0.01731424,-0.01398785,-0.05320184,0.09408344,0.01144504,0.0959735,0.03100789,0.23123628,-0.00888181,0.00425257,-0.00256217,-0.00084804,-0.01728142,-0.11236715,0.04899934,0.01264201,-0.03620062,0.09405331,0.00609093,-0.03112472,0.00372176,0.00687711,0.05107299,0.00890147,0.00665527,-0.06156497,-0.00351332,-0.02213669,-0.0462901,0.13755015,0.0119138,-0.06895304,-0.08585675,0.00037558,0.0752166,-0.08767872,-0.03940235,-0.0221971,-0.03599592,0.02103183,0.04644851,0.03466159,0.03732895,-0.00144166,-0.00449964,0.02051933,0.00346567,-0.01669288,0.02662932,0.03431709],"last_embed":{"hash":"146r94d","tokens":138}}},"text":null,"length":0,"last_read":{"hash":"146r94d","at":1753423521477},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{19}","lines":[112,112],"size":403,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{20}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05856408,-0.0108183,0.00470302,-0.04278514,-0.04796707,0.05013149,-0.02169373,0.01649873,0.03576604,-0.03611597,0.00313124,-0.02136565,0.09472086,-0.00363708,0.04384068,-0.04653943,-0.01439345,0.00525666,-0.06071486,-0.03971982,0.1191996,-0.02470809,-0.06581085,-0.05249446,0.0422718,-0.02586349,0.01211872,-0.08176717,-0.00844973,-0.22560847,0.04151418,0.03057792,0.01916225,-0.04896095,-0.02779081,-0.02911296,-0.00396104,0.07849956,-0.11621282,0.05664785,0.01496703,0.02335467,0.03181867,-0.05335031,-0.03240487,-0.01692636,0.01166015,-0.01671945,0.00960846,-0.05783426,-0.01033,0.01558609,-0.01376281,0.0393337,-0.03830912,-0.00077775,-0.00643251,0.12372456,-0.00148034,0.0305267,-0.00034338,0.03344458,-0.17934327,0.05866537,-0.00874154,0.04903102,0.02157771,-0.00646654,0.02538871,0.12346189,0.01881106,0.04365155,-0.00622498,0.06396567,-0.0020642,-0.05451538,-0.05875838,-0.04028641,-0.00868157,0.03593189,-0.04438404,-0.04560167,0.00868547,-0.00099876,0.01272072,0.00730259,0.02583118,-0.00600501,0.03163755,-0.01706678,0.02655844,-0.02080079,0.01152398,0.0554966,0.01065475,-0.00461444,0.03073364,0.0089793,-0.00777053,0.15032619,0.00563996,0.00653683,0.00814258,-0.00541429,0.02812288,-0.02858059,-0.01617749,-0.0348521,0.00151287,0.03718312,0.05250118,0.01419301,0.03596474,-0.06119115,-0.03965256,0.0346367,-0.00594726,0.01821526,0.03685579,0.02528911,-0.05221303,-0.02493384,0.02637651,-0.04198007,0.04681928,0.01478041,0.03448289,0.07830882,-0.01177817,0.02208217,0.01441975,-0.03774838,-0.11300024,-0.04302075,-0.00747076,0.0214442,-0.00836105,-0.04020813,-0.02679632,0.06672851,-0.05842775,-0.00472215,0.06119445,-0.09154984,0.02150423,0.11830203,0.02295646,0.01575555,-0.04323334,-0.05564025,-0.04180292,0.04244228,-0.05306846,-0.04559168,-0.0089192,0.0441705,0.05647238,0.02474764,-0.05582629,0.01113225,-0.01141173,-0.01260647,-0.02467143,0.10142613,-0.0247633,-0.05044422,-0.03771877,0.03259177,-0.05187188,-0.06243945,-0.07156617,0.0231479,-0.02426381,-0.02330568,0.07783822,0.00398025,-0.12611078,-0.02453685,-0.05243543,-0.03321654,0.03579686,-0.0040393,-0.02859682,0.00328939,-0.02706589,-0.08817794,0.02870841,-0.00101605,0.02739682,0.04317699,-0.08131464,0.00105088,0.00745735,-0.00827835,0.0000473,-0.00449555,0.0049306,-0.0321952,0.033149,-0.03034899,-0.02133536,-0.01135027,0.02616095,0.06081154,-0.04036395,0.07408933,9.9e-7,-0.02215154,0.09132581,0.0287795,-0.04774497,-0.00390555,0.03716972,0.08298691,0.00676025,-0.01316264,-0.00886471,-0.0021961,0.01467872,-0.00620926,0.03126519,0.05343395,-0.06450433,-0.19064252,-0.02008375,-0.04413014,0.01737846,0.02225535,-0.00377705,-0.04685197,-0.01180185,-0.02874159,0.12638381,0.06380082,-0.03791547,0.00838384,0.08556469,-0.0249364,0.03283119,-0.08127167,0.00627753,-0.09244967,0.00724348,0.03429206,0.00759311,0.01346575,-0.06806874,0.03789587,-0.02293766,0.12471918,-0.00926799,0.02744965,0.02901057,0.04443744,0.01416315,-0.03170227,0.00340041,-0.02795782,0.07582463,-0.04997598,-0.01131917,0.04029559,0.01793128,-0.02095796,0.03608604,-0.04282648,-0.11406577,-0.0133782,0.054868,-0.01057879,0.09916434,0.02150188,0.03634614,0.03899518,-0.07472609,0.03516752,0.01948282,0.08333168,-0.07672849,-0.09927764,-0.00367006,0.0037461,0.03734855,-0.0232584,-0.02922382,0.03462136,-0.03363732,0.05341769,0.01985176,-0.02769569,-0.03293371,-0.04011732,0.00516264,-0.00116275,0.05543349,-0.04722399,0.03182913,-0.06557653,0.02036808,0.02735853,-0.00953137,-0.06077649,-0.04366107,0.06424589,-0.0367142,0.0502157,0.07993764,0.0510316,0.02302554,0.03998333,0.02449611,-0.01383849,-0.01933785,-0.03436254,0.02866006,-0.00860103,0.07260142,0.02112468,-0.03856171,-0.24480592,0.04976545,-0.02321525,0.05507057,-0.02354825,0.00783334,0.02345845,0.02592818,0.00960886,-0.08110095,0.00898098,0.08602121,0.05307132,-0.01349095,-0.01894405,0.02038431,0.0215802,-0.07324439,0.06849455,-0.00830878,0.10170842,0.06145588,0.22911562,-0.01226292,0.00836533,0.00862723,-0.01206272,-0.01414702,-0.08258197,0.0484945,0.0243518,-0.00773499,0.10867932,-0.02319113,-0.0056905,0.00186737,-0.03112693,0.04670348,0.04574904,0.01372068,-0.05650233,-0.00796706,-0.03356095,-0.02725494,0.12140541,0.00547901,-0.05896617,-0.06299186,-0.02688308,0.06116404,-0.08431386,-0.01134971,-0.00654815,-0.02942964,0.0226657,0.02686218,0.01357892,0.02199752,-0.00280218,0.00853186,0.04905172,0.01354294,-0.0431878,0.00560256,0.00059278],"last_embed":{"hash":"1sm48bn","tokens":121}}},"text":null,"length":0,"last_read":{"hash":"1sm48bn","at":1753423521542},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>2. Setting the Filters: Theory and Software Tools</u>#{20}","lines":[113,114],"size":314,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07827629,-0.06299828,-0.00847373,-0.04744026,-0.01750616,0.07590839,-0.00327306,0.02257006,0.02828681,-0.00138264,0.00019896,0.0000811,0.07173176,-0.00260563,-0.00006786,-0.0461719,0.02186448,0.01229289,-0.12944633,-0.00094588,0.10455232,-0.04381761,-0.07246611,-0.05191905,0.06282309,-0.02450826,-0.01673943,-0.08838871,-0.07082211,-0.21489143,0.04571441,-0.02137294,0.03626825,-0.0428545,-0.06872301,-0.03048361,-0.02437121,0.08747122,-0.08620783,0.04541343,0.00983008,-0.00310363,0.06096466,-0.03636976,-0.04312155,-0.0086208,-0.00455886,-0.01611755,0.03379754,-0.0053855,-0.05221552,-0.00344846,-0.01242735,0.03474734,0.01447325,-0.00296189,0.03380507,0.11610315,0.02608905,0.05428486,0.0268484,0.02835458,-0.16524422,0.05075797,-0.00398332,0.03695203,-0.0150817,-0.04009988,0.01241354,0.08413438,0.00870056,0.04440693,0.00791682,0.0607251,0.00688629,-0.08880613,-0.07324388,-0.06912804,-0.02362155,-0.00055141,-0.07184176,0.00097317,0.01124931,-0.00002751,0.02312387,0.01848734,0.05518319,-0.01404057,0.02524147,-0.03172041,0.01089692,0.00243306,-0.03438438,0.01590858,0.00603418,-0.02810305,0.00801184,-0.00935518,-0.00234339,0.12434135,0.02834485,0.03069415,-0.03057226,-0.01459191,0.04953444,-0.05574971,-0.02427019,-0.02532498,-0.01347642,0.05114071,0.03088589,0.00551918,0.05094382,-0.02309632,-0.03415486,0.0306425,0.0162064,0.01397531,0.02935742,0.00169624,-0.05572837,-0.02694603,-0.00278175,-0.02719789,0.05499282,0.05486977,0.0197123,0.0743898,0.00969111,0.06560855,0.01496474,-0.04471122,-0.13959999,-0.00706787,-0.0095579,0.0245283,0.01908813,0.02009835,-0.00393492,0.03492833,-0.02380851,-0.05128836,0.02143289,-0.06869229,-0.03025455,0.11744652,-0.05189024,0.01592287,-0.01726344,-0.05988417,0.0255586,0.00460849,-0.04767175,-0.05210536,-0.00445749,0.02383849,0.07009283,0.0191319,-0.06139366,0.00937527,-0.03598606,-0.01595091,-0.01418625,0.08994536,-0.00932554,-0.03189778,-0.03242818,0.03408513,-0.03727579,-0.01666984,-0.01586775,0.03832524,-0.04548986,0.02957091,0.08102989,-0.01189303,-0.09692167,-0.0347055,-0.02513244,-0.01585023,0.0047998,-0.00443849,-0.03377914,-0.00562848,-0.02594367,-0.06705938,0.05752881,0.00027481,0.0309869,0.03868209,-0.06789684,-0.00387774,-0.0565809,-0.00403679,0.0078114,-0.00525217,-0.01080029,-0.02442389,0.02478894,0.02440546,0.04423852,-0.00091006,0.01681597,0.04717677,-0.05988626,0.08353997,0.00754148,-0.04642883,0.12851197,0.03419835,-0.04554256,-0.02533253,0.03636471,0.06128721,0.02802756,0.0057994,0.04150928,-0.000393,-0.01861419,0.01974868,0.01776257,0.01374281,-0.06164353,-0.16691124,-0.00937723,-0.00396072,0.00040396,0.02698829,-0.00256573,-0.03454268,-0.01732644,0.03449058,0.14283486,0.10423282,-0.05798916,0.00011604,0.0677286,-0.01051358,0.04318272,-0.08984115,-0.00374058,-0.07377381,0.03136093,0.0345328,0.01075191,-0.0121457,-0.05896229,0.04111481,-0.00800023,0.13164087,-0.0267166,0.01797058,0.00955677,0.0639096,-0.00739826,0.00136098,0.06261571,-0.02928593,0.05153346,-0.07923049,-0.01687531,-0.01091382,0.02342655,-0.05014168,0.01868271,-0.02274036,-0.10908029,-0.05059947,0.04872885,-0.03399812,0.10009754,0.02948033,0.03005212,0.03334677,-0.04097712,0.02857864,-0.00896357,0.10682217,-0.07182518,-0.10345978,-0.01792566,-0.00992178,0.02342678,-0.02894158,-0.08329433,0.03004451,-0.04644046,0.01582196,0.02432122,0.00164865,-0.02884872,-0.02435435,-0.0081979,-0.02917405,0.03085814,-0.00965203,0.03052937,-0.06476546,0.05485605,0.0072731,0.01604163,-0.0230764,-0.06572015,0.09068776,-0.05241575,0.0278647,0.04885099,0.0128352,0.01379261,0.05074207,0.03052704,0.01903356,0.01649775,-0.02100695,0.02070552,-0.03820517,0.04901924,0.00873704,-0.04936989,-0.25137669,0.05694429,-0.02514827,0.04013715,-0.02722177,-0.02715957,0.03178217,0.04132946,0.03126137,-0.05648788,0.00543251,0.06388924,0.02629959,-0.02171771,-0.02307936,-0.01046235,-0.05957798,-0.05001121,0.09243046,0.01192966,0.09067306,0.02317804,0.2203079,-0.01174961,0.02590216,0.02398064,-0.00322913,0.00779334,-0.0796077,0.06846951,0.02381031,-0.01021592,0.12222169,-0.00550141,0.00152565,0.00476398,-0.0376064,0.03110312,0.02141783,0.02184706,-0.05911636,-0.00622232,-0.03271039,-0.03374571,0.1434367,0.01575042,-0.05914959,-0.07714076,0.0577712,0.07887205,-0.0677404,-0.00680518,-0.01230193,-0.03828459,0.02193234,0.04508099,0.01415603,0.02943632,-0.00226285,0.02066565,0.02751045,-0.03424641,0.00205027,-0.00512498,-0.00523803],"last_embed":{"hash":"2uzp1h","tokens":457}}},"text":null,"length":0,"last_read":{"hash":"2uzp1h","at":1753423521598},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>","lines":[115,164],"size":10635,"outlinks":[{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":16},{"title":"_**rules of probability**_: _multiplication_ and _addition_ of _non-mutually exclusive_ _events:_ _**<u>Bayes Theorem</u>**_","target":"https://saliu.com/bayes-theorem.html","line":34},{"title":"Combining more lottery strategies and playing the aggregate result increase the profits.","target":"https://saliu.com/images/lotto.gif","line":36},{"title":"_**pick-4 lottery super strategy based on digit <u>frequency</u> groups**_","target":"https://saliu.com/frequency-lottery.html","line":38},{"title":"There is a balance of confidence in the number of lottery filters we set in software.","target":"https://saliu.com/images/lotto.gif","line":45},{"title":"Run lotto lottery data files, systems, strategies with filtering, filters in software.","target":"https://saliu.com/HLINE.gif","line":49}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07330607,-0.05257794,-0.00367275,-0.03967669,-0.02809128,0.0914119,-0.00233063,0.03542199,0.02925454,-0.00997011,0.0000842,-0.00384143,0.0748194,-0.00436375,0.01064887,-0.03649897,0.02234211,0.00281913,-0.14536609,0.00588948,0.11648793,-0.03406832,-0.07514591,-0.05761117,0.07200685,-0.04093744,0.00793551,-0.08755253,-0.04587867,-0.21574706,0.02958613,-0.01212174,0.01983421,-0.04165805,-0.05516927,-0.03181174,-0.03395039,0.07614823,-0.07384373,0.04913232,0.01721567,0.00576082,0.05823787,-0.03572567,-0.04672877,-0.00970778,-0.00936323,-0.03371507,0.03596323,-0.00885402,-0.04067592,0.00616729,-0.00586117,0.02803474,0.01204728,0.00320697,0.04028066,0.11267906,0.02068667,0.04580254,0.02985613,0.02689425,-0.16600467,0.0507699,-0.01397535,0.03085364,-0.0193296,-0.04441587,-0.00067289,0.09393814,0.02742591,0.0558921,0.00559386,0.0442433,-0.0010887,-0.08210493,-0.08753386,-0.07065016,-0.0208577,0.00523584,-0.06676074,-0.00209922,0.01549027,0.00676727,0.01472547,0.00336086,0.03839964,-0.02059602,0.01957665,-0.03377309,0.00892508,0.00589977,-0.0367545,0.02901104,0.01171161,-0.01731595,0.00421601,-0.0171167,-0.01952401,0.11477382,0.03839849,0.0402935,-0.02789076,-0.01259621,0.05172087,-0.04659717,-0.0122861,-0.01636828,-0.0128699,0.04716437,0.02323822,0.00677835,0.04265183,-0.02588305,-0.03909672,0.04373251,0.03157676,0.0072428,0.02704815,0.01479147,-0.05126016,-0.02162944,-0.00473528,-0.02485707,0.04423067,0.05055341,0.02478436,0.0754298,0.02542169,0.06220538,0.00547657,-0.05262386,-0.11942124,-0.015323,-0.01648722,0.01463217,0.03077675,0.02973942,0.00083885,0.02245405,-0.03191185,-0.05451557,0.03206691,-0.06006528,-0.0357367,0.120376,-0.0461846,0.0177169,-0.02252671,-0.05499016,0.01412916,-0.0038149,-0.05372781,-0.05119779,-0.01739455,0.02756002,0.07426793,0.02960521,-0.04410769,0.01982884,-0.03373392,-0.02248301,-0.01760971,0.07716724,0.00026193,-0.04213361,-0.03696745,0.01505301,-0.04035246,-0.02076639,-0.0187172,0.04255723,-0.04535784,0.02497806,0.07822504,-0.01278887,-0.10628246,-0.02224956,-0.03643861,-0.00909457,-0.00418286,0.0004358,-0.04324256,-0.00808564,-0.03960649,-0.0601809,0.04934734,-0.00051057,0.03243254,0.04113474,-0.07536502,-0.00199997,-0.04503939,0.01277066,0.0147859,-0.0058176,-0.00066364,-0.02445442,0.00522041,0.01982627,0.02852328,-0.01423091,0.01361874,0.05379571,-0.063908,0.07770876,0.00690835,-0.04572971,0.12723359,0.03453082,-0.04055415,-0.01447476,0.0289736,0.05509607,0.02466391,-0.00557326,0.0409769,-0.00210507,-0.01918192,0.00780693,0.04010468,0.01425171,-0.04598791,-0.16882172,-0.01574801,-0.01479636,-0.00752231,0.0345079,-0.00382294,-0.01943371,-0.0140576,0.02581226,0.13050075,0.0963713,-0.06669568,0.00627826,0.06202001,-0.01134983,0.03222026,-0.1054697,0.00142221,-0.05373477,0.03080376,0.03281315,0.02177531,-0.0007124,-0.05786687,0.03851736,-0.00963299,0.13774613,-0.02725643,-0.00979932,0.01270238,0.06027042,-0.00710558,-0.01382682,0.05625103,-0.02638076,0.03942826,-0.08068187,-0.01833628,-0.01211907,0.01875398,-0.03383598,0.02390847,-0.03393473,-0.10770798,-0.04832267,0.05109973,-0.03212083,0.11458176,0.00481955,0.03089724,0.04686379,-0.03578322,0.0300533,-0.01641241,0.10063048,-0.06247602,-0.10125141,-0.01797425,-0.00386703,0.02691072,-0.03321981,-0.07769255,0.02308484,-0.04660824,0.02394707,0.02295227,-0.01208306,-0.0230238,-0.03211631,-0.00673045,-0.01957022,0.02797584,-0.01193678,0.03617125,-0.06887538,0.04529531,-0.00773377,0.01233287,-0.01838994,-0.06350689,0.10288981,-0.05638429,0.01603392,0.04676727,0.02167796,0.02737175,0.0663375,0.02775941,0.0277408,0.01508253,-0.03658953,0.03360415,-0.02421075,0.05863599,0.00946075,-0.07235844,-0.26143837,0.06227918,-0.02271227,0.0489201,-0.03088732,-0.02119494,0.03893148,0.04914963,0.03054388,-0.06689013,0.00991653,0.05934679,0.02708433,-0.01164508,-0.01074346,-0.01320456,-0.05595329,-0.0670003,0.08523787,0.01681395,0.10481711,0.02677803,0.22388902,0.00085154,0.01677843,0.02385948,-0.01115046,0.01525554,-0.08222292,0.06103187,0.00547216,-0.01195677,0.12196831,-0.02122776,-0.00507907,0.025136,-0.04546944,0.02306361,0.04004303,0.03613481,-0.04124001,-0.00935957,-0.03371006,-0.03798137,0.14582942,0.01716422,-0.06105806,-0.07004215,0.04860524,0.06124204,-0.07164668,0.00592801,0.00453295,-0.04367775,0.02046336,0.04586339,0.02639835,0.02345906,0.01175961,0.01540211,0.03168777,-0.01973313,-0.00313528,-0.00876477,-0.00748127],"last_embed":{"hash":"1fmtzac","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"1fmtzac","at":1753423521791},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#{4}","lines":[122,131],"size":2847,"outlinks":[{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0993034,-0.04915581,-0.00585501,-0.00235455,-0.0519353,0.08118025,0.01103966,0.03968262,0.0567879,-0.00570197,0.01843426,0.01025428,0.05722629,-0.02738748,-0.0076039,-0.05201664,-0.01706943,-0.01552435,-0.11964536,-0.01078476,0.0971433,-0.03798716,-0.05476501,-0.071144,0.09577087,-0.0300339,0.00730153,-0.06230373,-0.04374744,-0.2301039,0.03167819,-0.01532017,0.01617463,-0.06458231,-0.09130602,-0.00613141,-0.04172311,0.09223545,-0.0431505,0.03706663,-0.0023818,0.01810681,0.00864091,-0.05685429,-0.03448913,-0.03839744,0.01205524,-0.04811177,-0.01319808,-0.02290624,-0.0384111,-0.00896059,0.02564619,0.04312601,-0.00613645,0.01331284,0.04265955,0.1018459,0.0265638,0.04565675,0.03825461,0.02437061,-0.17876022,0.07905564,-0.02391677,0.02151774,-0.03163989,-0.02737362,0.02869472,0.10896835,0.00165408,0.02620704,-0.02919414,0.06243423,0.01576171,-0.08314859,-0.04299048,-0.06173604,-0.01380704,-0.01024033,-0.0509852,-0.02557634,0.0022919,0.0064277,-0.00388168,0.01142347,0.02241531,-0.0514187,0.02283271,-0.0279503,0.01497043,-0.02825373,-0.04119806,0.0445031,-0.00681542,-0.00264143,0.05039036,-0.00356026,-0.01949296,0.13219485,0.01244516,0.03325064,-0.01711193,0.03604141,0.06998093,-0.05458599,-0.02572412,-0.02596158,-0.01189558,0.0279058,0.01797758,0.02693716,0.02314502,0.00603425,-0.06200496,0.05317272,0.02402129,0.02784191,0.00913004,0.02294832,-0.03873407,-0.00570608,0.01051754,-0.05707557,0.00757192,0.01600954,0.03248711,0.07804558,0.0012346,0.03509382,0.05986879,-0.04240783,-0.1139985,-0.02080196,-0.02384731,-0.0075101,-0.02597937,0.03479955,-0.03144198,0.02860177,0.0029489,-0.00547369,0.02737455,-0.08157206,-0.00413381,0.09830809,-0.04602879,0.02297435,-0.01048555,-0.07098776,-0.01150119,0.01284243,-0.04822187,-0.03602813,-0.02058308,0.0118925,0.06868199,0.03548916,-0.04175772,0.02469533,-0.05600379,-0.02169506,-0.04141225,0.0693888,0.00546177,-0.03419776,-0.02834822,0.00184137,-0.01713306,-0.03634039,-0.04518394,0.05734746,-0.03749995,-0.01005331,0.06566618,-0.01955908,-0.14858769,-0.04825527,0.00563668,-0.03002832,0.03980199,0.01659437,-0.04769748,0.00071973,-0.0294317,-0.05721565,0.04467154,0.01084271,0.02811681,0.08952557,-0.05447041,0.00837569,-0.03359572,-0.00731746,-0.00691491,-0.01746093,-0.01893133,-0.00778076,0.01025714,0.00820261,0.01103121,-0.02164266,0.02255079,0.03737202,-0.05788972,0.0889084,0.03056363,-0.04813682,0.10961477,0.02020749,-0.04871857,-0.02976691,0.05097332,0.06057282,0.01961163,0.00797935,-0.00550059,0.01144931,-0.01283768,-0.01231747,0.0172782,0.0530473,-0.02797289,-0.19498616,-0.02594453,-0.00348708,0.0320996,0.04074335,-0.00541564,-0.03649471,-0.00554118,0.02764631,0.10213809,0.10358889,-0.06335433,0.03343441,0.04973375,-0.00870475,0.02257943,-0.09364678,-0.00047821,-0.02659515,0.06052154,-0.01841293,0.04463685,-0.00262566,-0.07627027,0.04823809,-0.02793807,0.09925706,0.00058319,0.00088509,0.01750013,0.03552405,-0.04195973,-0.05153828,0.0199692,0.01137395,0.09497062,-0.06354773,-0.01774992,-0.02179655,-0.00262764,-0.0503974,0.0303125,-0.02291355,-0.0790637,-0.07157129,0.04203838,-0.00368693,0.10797346,0.04118274,0.0547795,0.06578352,-0.00753914,0.01074737,0.00973498,0.08666651,-0.05371491,-0.09145436,0.01417019,-0.02196226,0.05169864,-0.00427952,-0.05113847,0.02430323,-0.01613463,0.04516653,0.04553199,-0.01153076,-0.03419966,-0.00627621,0.03863514,-0.00214773,0.0789951,-0.01508123,0.00840319,-0.02716858,0.04473959,-0.0013294,0.0050583,-0.05093052,-0.04841224,0.07023048,-0.10137005,0.01715724,0.05089577,0.01683423,0.04302216,0.03513759,0.02540309,-0.02364606,0.00727522,-0.02568004,0.02572981,-0.05341767,0.05296877,-0.02502214,-0.01982957,-0.25739968,0.05080109,-0.01243358,0.05572985,-0.0332268,-0.00174756,0.05146984,0.04635865,0.0250523,-0.07457078,-0.01726632,0.03358214,0.01751298,-0.01054142,-0.03057543,0.00087777,-0.02107194,-0.05421433,0.06469851,-0.00814706,0.07161168,0.05001763,0.22893061,0.01909093,0.03177856,-0.01180232,0.02087323,0.00768194,-0.07301833,0.02094739,0.02328435,-0.00013987,0.13227926,-0.00675169,0.00711896,0.02465129,-0.04814237,0.02475127,0.00688532,-0.00475184,-0.03186543,0.02192538,-0.02674178,-0.03459724,0.15541732,0.00500427,-0.04254055,-0.02710309,0.06371362,0.10335179,-0.06148642,-0.01222807,-0.03156456,-0.02203555,0.01415132,0.01828451,0.01341831,0.01134163,0.00648629,0.00834845,0.03566323,-0.02183187,0.03702972,-0.0244043,0.03313111],"last_embed":{"hash":"xco858","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"xco858","at":1753423521990},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.","lines":[132,164],"size":7511,"outlinks":[{"title":"_**rules of probability**_: _multiplication_ and _addition_ of _non-mutually exclusive_ _events:_ _**<u>Bayes Theorem</u>**_","target":"https://saliu.com/bayes-theorem.html","line":17},{"title":"Combining more lottery strategies and playing the aggregate result increase the profits.","target":"https://saliu.com/images/lotto.gif","line":19},{"title":"_**pick-4 lottery super strategy based on digit <u>frequency</u> groups**_","target":"https://saliu.com/frequency-lottery.html","line":21},{"title":"There is a balance of confidence in the number of lottery filters we set in software.","target":"https://saliu.com/images/lotto.gif","line":28},{"title":"Run lotto lottery data files, systems, strategies with filtering, filters in software.","target":"https://saliu.com/HLINE.gif","line":32}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10130057,-0.05142066,-0.00511091,-0.00054012,-0.04880917,0.07818215,0.01309412,0.03920881,0.0572013,-0.00714623,0.01845126,0.01301822,0.05805319,-0.02604944,-0.00702663,-0.05043305,-0.01761444,-0.01599052,-0.1194407,-0.01060538,0.09734768,-0.03789831,-0.05600313,-0.07039186,0.0975469,-0.02904151,0.00535149,-0.06017691,-0.04483553,-0.22947846,0.03201749,-0.01846351,0.01296624,-0.06638519,-0.08895411,-0.00741144,-0.0398643,0.0913058,-0.03905747,0.03627622,-0.0031672,0.01517538,0.00404025,-0.05679614,-0.03500716,-0.03984036,0.01340781,-0.04789861,-0.01286856,-0.02384678,-0.03725403,-0.00734491,0.02436515,0.04219242,-0.00775353,0.01362723,0.04217861,0.10147112,0.02802436,0.04678078,0.0386449,0.02164413,-0.17861193,0.07988939,-0.02442851,0.02241632,-0.03181277,-0.02829499,0.02871585,0.10955226,0.00069826,0.02635345,-0.03076287,0.06322158,0.01557446,-0.08306672,-0.04184858,-0.06426045,-0.01287805,-0.00923327,-0.04882786,-0.024435,0.00224734,0.00571414,-0.00479208,0.01211722,0.02392245,-0.05268566,0.02073445,-0.02601395,0.01429717,-0.02834728,-0.04169606,0.04628306,-0.00749584,-0.00193106,0.04963486,-0.00250497,-0.01648214,0.13229473,0.01107486,0.03130278,-0.0176641,0.03512401,0.07143044,-0.0546907,-0.02379488,-0.02473409,-0.01144001,0.02940415,0.02104515,0.02444375,0.02367802,0.00754088,-0.06170375,0.0566408,0.02413785,0.02835302,0.01082445,0.02065126,-0.03920799,-0.00512995,0.01009925,-0.05772281,0.00492167,0.01800028,0.03411249,0.07783967,-0.00012115,0.03270674,0.06187235,-0.04558843,-0.11462259,-0.01946828,-0.02453562,-0.0039666,-0.02622577,0.03393631,-0.03369834,0.02882258,0.00043896,-0.00767122,0.0267805,-0.08043943,-0.00816067,0.10014692,-0.04805486,0.02204031,-0.01062606,-0.0716284,-0.0126339,0.01376351,-0.04966694,-0.0359729,-0.02429605,0.01138652,0.06866149,0.03574361,-0.04127695,0.02497424,-0.05739275,-0.02245548,-0.04121746,0.06996021,0.00564372,-0.03186006,-0.02915693,0.00043455,-0.01633516,-0.03692051,-0.04463135,0.0566974,-0.03720787,-0.00986235,0.06579384,-0.01800921,-0.14716242,-0.04848782,0.00202526,-0.03181959,0.03824814,0.01747796,-0.04560722,0.00167283,-0.02846467,-0.05638798,0.04241285,0.0132165,0.02576061,0.08998794,-0.05523504,0.00813405,-0.03410171,-0.00593826,-0.00520937,-0.01567651,-0.01798488,-0.00428975,0.00941749,0.00711897,0.01095373,-0.0225229,0.02109934,0.03758056,-0.05762271,0.08830573,0.02822687,-0.04758862,0.10984515,0.01845184,-0.04795198,-0.02884177,0.05076437,0.05945263,0.02139439,0.00642418,-0.00584913,0.01264439,-0.0131086,-0.01090308,0.01557137,0.05536372,-0.02760106,-0.19671382,-0.02626585,-0.00436408,0.03269324,0.04129596,-0.00355047,-0.03938017,-0.00273353,0.02946702,0.10413895,0.1029207,-0.06207687,0.03498777,0.04854091,-0.00635503,0.02189705,-0.08950746,0.00150574,-0.028067,0.05923557,-0.01838123,0.04535703,-0.00258607,-0.07414489,0.04669289,-0.02409559,0.09853056,-0.00056697,-0.00025281,0.01663778,0.03339659,-0.04179893,-0.05243853,0.01955705,0.0139666,0.09331843,-0.06317459,-0.01930051,-0.0221249,-0.00391129,-0.05333884,0.03104626,-0.02213636,-0.07999355,-0.07522094,0.04199723,-0.00154011,0.11019564,0.04069579,0.05180863,0.06379401,-0.00840415,0.00903643,0.01139619,0.08759348,-0.05347614,-0.09126841,0.01312563,-0.02059465,0.0520455,-0.0051996,-0.05066923,0.02251685,-0.01412761,0.04817619,0.04171448,-0.01050563,-0.03402837,-0.0025272,0.03975612,-0.00281311,0.08093885,-0.01624199,0.00954003,-0.02714971,0.04717649,-0.00065977,0.00365878,-0.05349451,-0.04681327,0.07374452,-0.10058694,0.01723181,0.05053378,0.01580849,0.04213919,0.03400175,0.02314088,-0.0239287,0.00935445,-0.02580312,0.02485028,-0.05449465,0.05180722,-0.02426637,-0.01815757,-0.25693563,0.05091183,-0.01193509,0.05337681,-0.03566212,-0.00401629,0.05060036,0.04457149,0.02305967,-0.07371733,-0.01837459,0.03714404,0.01842371,-0.00895183,-0.03299568,-0.00070289,-0.02022733,-0.0518935,0.06487805,-0.00548446,0.07057615,0.05204334,0.22983937,0.02030644,0.03300383,-0.01346093,0.01990941,0.00890396,-0.0730717,0.01971673,0.02401438,-0.00077438,0.13158587,-0.00489373,0.00755157,0.02405894,-0.04643196,0.02587683,0.0080639,-0.00537465,-0.03088301,0.02290808,-0.02960737,-0.03401833,0.15660489,0.00663211,-0.04499167,-0.02483661,0.06604613,0.10254343,-0.06020041,-0.01302996,-0.03255999,-0.02185958,0.01129683,0.01577059,0.01495335,0.01211683,0.00534262,0.00661313,0.03532756,-0.02205979,0.03603074,-0.0234114,0.0327943],"last_embed":{"hash":"kccz9h","tokens":430}}},"text":null,"length":0,"last_read":{"hash":"kccz9h","at":1753423522193},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{1}","lines":[134,145],"size":2750,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08932643,-0.02351239,0.01835786,0.01791854,-0.0223519,0.06229896,0.03293622,0.06215294,0.03640609,-0.01602332,0.0123446,0.00122497,0.06224004,-0.0115169,-0.00948291,-0.00684658,-0.02510102,0.01524879,-0.06611117,-0.01007098,0.09630368,-0.02551735,-0.05953712,-0.06349856,0.06274387,-0.02873628,0.01324317,-0.04298266,-0.03322623,-0.21331619,0.03643988,-0.0122475,0.00760707,-0.08274306,-0.0814265,-0.0222528,-0.06022444,0.08597188,-0.05542331,0.07479155,0.01742197,0.00466226,0.00716813,-0.03463212,-0.01810739,-0.01161291,0.00214452,-0.05349744,-0.03178144,-0.02378557,-0.03608652,0.01425809,0.01160906,0.00219179,0.00457181,0.00454826,0.02853748,0.11441492,0.04324436,0.06352971,-0.01198385,0.04271436,-0.19394657,0.04752053,-0.02793314,0.01423119,-0.01517591,0.00711455,0.00834163,0.1055999,-0.00134997,0.07994889,-0.01743321,0.06608448,0.03122138,-0.04462092,-0.03506906,-0.06278847,-0.02680654,0.01172035,0.00610787,-0.03227174,-0.01791652,-0.03395338,0.0105193,-0.00783042,0.03348272,-0.0447216,0.04339103,-0.00945339,0.04788036,0.04270432,-0.02035714,0.03483133,0.0112481,-0.00374538,0.01127836,0.0316011,0.02639849,0.12576444,0.01333909,0.04786925,-0.02824022,0.00199584,0.05980576,-0.08653669,-0.02552336,-0.00793307,0.00955326,0.05101118,0.01576832,0.02750832,0.05098371,-0.02163111,-0.04309101,0.05855075,0.01946295,0.04320129,0.00980108,0.01012526,-0.0523569,0.02154868,0.0350934,-0.05213106,0.02120333,0.02017776,-0.00409972,0.10421046,0.01312016,0.02447816,0.04650137,-0.05082468,-0.1354886,-0.00251927,-0.00854885,-0.02330125,0.00342699,0.01001945,-0.02542765,0.05790122,-0.06194801,-0.02098854,0.04947912,-0.09624106,-0.04821029,0.09560636,0.00428257,0.00624775,0.00242751,-0.05544722,0.01035555,0.01551116,-0.03126081,-0.06533851,-0.00527045,0.02399445,0.03498356,0.06015463,-0.03671484,0.00941977,-0.0610139,-0.0348006,-0.03005008,0.14049703,-0.02334378,-0.03641165,-0.05282476,0.01476509,-0.02914676,-0.04206005,-0.03100565,0.02032221,-0.03980764,-0.02103242,0.07373422,-0.00424113,-0.11613622,-0.06804866,-0.01203531,-0.03168412,0.04033285,-0.00768812,-0.04835503,0.02269995,-0.02732336,-0.04713702,0.01004627,0.01034896,0.0252529,0.04434025,-0.07378919,-0.04558108,-0.07939118,-0.00701297,0.01661471,-0.03104629,-0.01190967,-0.02619254,0.00572958,0.00894897,-0.01170393,-0.04922202,-0.00615269,0.04147232,-0.06898005,0.08530398,0.00607738,-0.04053707,0.09033231,0.03420278,-0.01994199,-0.03131389,0.03629464,0.07188393,0.01686835,-0.00977211,0.01078356,0.01674526,-0.00501343,-0.02212246,0.01101675,0.04422143,-0.03177107,-0.19521202,-0.01652744,-0.03609357,-0.0020112,0.03373317,0.01667557,-0.02999931,-0.02826552,0.04637724,0.09996859,0.05098899,-0.04451403,-0.01710023,0.07278822,-0.00267839,-0.00790532,-0.09146187,-0.04047235,-0.06294116,0.03262089,-0.01788458,0.05363607,-0.00510191,-0.08686386,0.02768518,0.01533921,0.1026618,-0.00352079,-0.00090924,0.03805933,0.02844857,0.0007466,-0.02848963,0.05184992,0.01498697,0.04721182,-0.0342038,-0.04607988,-0.05144491,0.00590709,-0.01506686,0.04942521,-0.0385531,-0.07916055,-0.07776783,0.04776644,-0.01260163,0.10941621,0.02496948,0.06842534,0.06880393,-0.01928983,0.06679068,0.01933676,0.09273105,-0.05678999,-0.06626059,-0.00925944,-0.00811723,0.02044253,0.00394921,-0.02105924,0.01980858,-0.0109129,0.05242265,0.03089123,-0.01166393,-0.05256356,-0.0200917,0.02137649,-0.00648161,0.10012621,-0.01592301,-0.01423674,-0.03323042,0.02594595,0.00685848,-0.02125686,-0.0585998,-0.05314412,0.09525761,-0.04986477,0.02844219,0.05547618,0.02452089,-0.00821338,0.0252321,0.00856364,0.00033958,0.00476439,-0.01809328,-0.00324921,-0.04574499,0.0392818,-0.0174137,-0.0208079,-0.27510959,0.04195866,-0.01385054,0.02959174,-0.01621607,0.00275867,0.03909452,0.03669026,0.00146274,-0.0522,0.00466582,0.06573224,0.01023757,0.00500933,-0.02040457,-0.04070294,-0.01388937,-0.06713287,0.08968764,0.01427751,0.09201995,0.05977812,0.24040236,-0.00060392,-0.00548347,0.00582902,-0.01437602,0.01266982,-0.0605055,0.00074182,0.00014893,-0.02280375,0.10748481,-0.02441474,0.00282917,0.03201127,-0.0196976,0.06442074,0.0116517,-0.04101261,-0.05369593,-0.00804307,0.0070236,-0.02357818,0.159128,0.02684147,-0.04844845,-0.04155624,0.0591191,0.08834079,-0.07587424,0.01019512,-0.03533946,-0.02246165,-0.0008353,0.00620894,-0.01917432,0.0295651,0.00371962,-0.01638393,0.02794015,0.02285997,0.02591828,0.01293547,0.01869329],"last_embed":{"hash":"mqnfq","tokens":148}}},"text":null,"length":0,"last_read":{"hash":"mqnfq","at":1753423522370},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{2}","lines":[146,146],"size":329,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08013284,-0.05528623,-0.00730454,-0.02389753,-0.04432685,0.05094946,0.02123839,0.0445283,0.03353915,-0.01381753,0.00856866,0.00584916,0.05295796,-0.0043805,0.02865021,0.00181506,-0.00813514,-0.00952325,-0.09219615,0.01697381,0.10302231,-0.02617569,-0.07245268,-0.06789716,0.06645294,-0.01537189,0.00846739,-0.0770929,-0.02361831,-0.20599763,0.01777678,0.00244706,0.03580425,-0.06969654,-0.07517747,-0.01927216,-0.04440377,0.09311815,-0.0597213,0.05009069,0.01768126,-0.0020454,0.0181019,-0.04758591,-0.01762895,-0.00664217,0.02118235,-0.05064048,0.01284597,-0.02818857,-0.03006143,0.01715602,0.00180462,0.04925423,-0.02639831,0.01815132,0.02240566,0.13633364,0.01628588,0.04101811,0.00651725,0.04159825,-0.16256043,0.0485129,-0.04019431,0.0449861,-0.02306361,-0.02386657,-0.02030741,0.11196528,0.01900304,0.06374425,-0.03549719,0.06941967,0.02293092,-0.0566944,-0.03902292,-0.07067299,-0.02703825,0.02334609,-0.02692991,-0.03957973,0.0155063,-0.01182458,-0.01222302,-0.00609182,0.03982439,0.00372597,0.02004046,-0.02649428,0.04323198,0.03893545,-0.03204931,0.05574767,0.02769845,-0.00680315,-0.00079273,0.00544657,0.01004512,0.13275599,-0.00389867,0.0177365,-0.0109266,0.00004977,0.05079403,-0.03342886,0.00259548,-0.00121316,-0.01880819,0.03515954,0.04311255,0.02237494,0.05500279,-0.03739804,-0.05663282,0.06733643,0.02916601,0.00724823,0.01591426,0.00173294,-0.05520243,-0.00953515,0.0368567,-0.05046695,0.03329686,0.0240385,0.03960796,0.07499839,-0.00335696,0.0327802,0.03986828,-0.04324618,-0.15364228,-0.01917554,-0.03070266,-0.00194299,0.02436056,-0.01506427,-0.03935691,0.05653188,-0.03477456,-0.01224731,0.04975186,-0.09609971,-0.02912766,0.11184585,-0.00889598,0.02432683,-0.02857299,-0.07275539,-0.02123365,0.01701588,-0.040213,-0.07348995,-0.00904759,0.0151422,0.04094321,0.02500143,-0.06518311,0.01169439,-0.02830234,-0.02884386,-0.02162511,0.11373603,-0.0133389,-0.04074853,-0.05973273,0.00610764,-0.04459601,-0.05024041,-0.02658649,0.03082979,-0.05109738,0.01966494,0.06745544,-0.01910608,-0.15307888,-0.03374762,-0.0490267,-0.01497821,0.01318463,0.00760582,-0.03337869,0.00363204,-0.02183499,-0.05482801,0.04261237,0.01560858,0.02390292,0.05821756,-0.08286843,-0.00782685,-0.04293176,0.00849536,-0.00241666,-0.0164124,-0.01165002,-0.02511388,0.02900924,0.00529382,-0.03278346,-0.0577133,-0.00550822,0.05684856,-0.06964265,0.10295971,-0.00782859,-0.05009652,0.09997856,0.0434178,-0.04865081,-0.00601805,0.07163007,0.06840838,0.01108538,-0.02058753,0.01808482,0.00411623,-0.00512504,-0.02451364,0.03939719,0.01323962,-0.06112704,-0.19033654,-0.01704481,-0.02631896,-0.00237827,0.0436556,-0.01071745,-0.02840697,-0.01756974,0.01915873,0.12821896,0.08419969,-0.06061355,0.01073336,0.07178041,-0.00102045,0.00831177,-0.09883428,-0.02514079,-0.05683788,0.02051006,-0.00800125,0.03767757,0.01087154,-0.06475684,0.02298016,0.0094233,0.09680706,-0.00511659,-0.02464254,0.04756733,0.06665514,-0.02901564,-0.03950854,0.04931282,0.00911364,0.03836894,-0.06961098,-0.033121,-0.01477425,0.00560414,-0.00578812,0.0209527,-0.04408824,-0.06767547,-0.05837894,0.02990588,-0.01877953,0.13557896,0.01291333,0.03105167,0.03703788,-0.01797576,0.02971755,0.01947011,0.06627961,-0.04481483,-0.10198658,-0.0063606,-0.02450971,0.04982136,-0.00635877,-0.0436942,0.01325908,-0.00075001,0.04329114,0.02333102,-0.00514905,-0.04284137,-0.02147778,0.00161771,-0.00861611,0.0620499,-0.01497948,0.01616889,-0.04184356,0.02783697,0.00773567,-0.00154212,-0.02464386,-0.04818179,0.10264076,-0.07936388,0.01626332,0.0666561,0.03586964,0.03560334,0.02322947,0.03809159,-0.00513397,-0.00611244,-0.00662625,0.02206654,-0.03782058,0.04047944,-0.01224125,-0.0284998,-0.23207939,0.04205707,-0.02392361,0.0579922,-0.0087975,-0.00824755,0.04213929,0.03214769,0.01568107,-0.04826701,0.04622304,0.05975321,0.01979917,-0.0161007,-0.02343034,-0.01762687,-0.03702398,-0.05003093,0.09260007,0.04251922,0.11072057,0.05644118,0.23742534,0.00418389,0.00393933,0.0016805,0.0054063,0.00907471,-0.10176247,0.0193369,0.00238324,-0.02961237,0.11379933,-0.02884687,-0.0131268,0.03433656,-0.01603728,0.03343093,0.01746618,-0.0013354,-0.03013179,0.00782062,-0.02966416,-0.02560682,0.15506171,0.00761219,-0.06666508,-0.05540402,0.05803185,0.08072374,-0.08167197,0.00034628,-0.04186383,-0.03628044,0.00501633,0.04062511,0.01590731,0.00860532,-0.00136411,-0.00430491,0.01188806,0.03882721,0.0137826,0.01918034,0.00253356],"last_embed":{"hash":"z7jz81","tokens":124}}},"text":null,"length":0,"last_read":{"hash":"z7jz81","at":1753423522419},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{3}","lines":[147,147],"size":281,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08702662,-0.0501495,-0.0185866,-0.010293,-0.0510742,0.05351665,0.02067874,0.04250691,0.06174657,-0.00039197,0.00475919,-0.00834125,0.04985488,-0.00601529,0.02017297,0.00902443,-0.02819437,0.02325265,-0.09352368,-0.0120687,0.09696383,-0.05199563,-0.07123468,-0.07383109,0.07413049,-0.01249391,0.01304857,-0.05680199,-0.02660153,-0.23661776,0.01266031,-0.01222096,-0.0037625,-0.08781786,-0.06446839,-0.02555204,-0.02815419,0.07112342,-0.03840594,0.05825669,0.02018408,0.02387689,0.02623311,-0.03113421,-0.01778479,-0.02256634,0.00710086,-0.05605582,-0.04271437,-0.02703869,-0.03806053,0.01369481,0.01768635,0.01519466,-0.00077533,0.00964224,0.02134731,0.13516374,0.00446136,0.07622143,0.00225862,0.05357387,-0.18022555,0.04721524,-0.00962032,0.01861662,-0.01939654,-0.0255081,-0.0146318,0.10091875,0.01428121,0.06646268,-0.01724408,0.08267637,0.03195414,-0.04404179,-0.0427424,-0.07907012,-0.04088024,0.03223224,-0.03249493,-0.04326485,0.02701722,-0.01982266,-0.01155488,-0.00525689,0.02153736,-0.02475766,0.01628198,-0.01437282,0.0323693,0.03152712,-0.02601301,0.0380864,0.02082717,0.04215712,0.02133689,0.01655582,0.01763307,0.11266372,0.00641374,0.04569668,-0.0234373,0.00824292,0.0704411,-0.06104964,-0.00698522,-0.01423503,-0.02531016,-0.00296922,0.0570261,0.02006605,0.04189926,-0.04457641,-0.06552832,0.08498324,0.03165281,0.01676564,0.01314847,-0.00227021,-0.0587961,0.02813517,0.04144235,-0.05306491,0.02426125,0.01876003,0.0115015,0.0881388,0.0208973,0.02678628,0.07495307,-0.04537544,-0.12167703,-0.00981698,-0.00259279,-0.01186032,0.00984618,-0.00291002,-0.02063901,0.03631173,-0.03623264,-0.04001598,0.03278005,-0.07985705,-0.01954769,0.09898891,0.00183312,0.02109782,-0.01784078,-0.05735648,-0.00650994,0.00984456,-0.02675258,-0.07316838,-0.00625268,-0.00223098,0.03340077,0.03191394,-0.06807389,0.01385323,-0.03342335,-0.03934239,-0.00929851,0.15217559,-0.00896391,-0.02866301,-0.03012284,0.02641365,-0.01986746,-0.03983478,-0.03313248,0.02401022,-0.01773794,-0.00617665,0.07206059,0.00301478,-0.13338076,-0.03856333,-0.029294,-0.01163302,-0.00470931,-0.02854691,-0.03880998,0.02039308,-0.03754331,-0.06932209,0.04455357,0.00649645,0.01720016,0.05490857,-0.05910175,-0.02074683,-0.06138021,0.00142014,-0.01522826,-0.01333925,0.00388197,-0.04166032,0.01581403,-0.0024286,-0.01653125,-0.04669435,-0.0255771,0.0257113,-0.04880628,0.07837842,-0.01119471,-0.02028127,0.07916034,0.04286848,-0.00748164,-0.02563344,0.03095531,0.05894731,0.02470411,-0.00671647,0.03661889,0.03903086,-0.01601477,-0.03100199,0.02436747,0.03135291,-0.03452044,-0.20403928,-0.03401744,-0.03934827,-0.02031716,0.0476437,-0.01261958,-0.03676233,-0.01805576,0.01002864,0.11309122,0.08223806,-0.03631086,-0.0035756,0.07256572,0.00756669,-0.0110498,-0.08896903,-0.00336897,-0.06505177,0.04582098,-0.01821447,0.05013202,-0.01310033,-0.06619451,0.04741358,0.00707478,0.10488949,0.01342007,0.01332906,0.03389372,0.04232798,0.01087412,-0.02304222,0.06701013,0.00196359,0.06879663,-0.08283011,-0.03706855,-0.01871536,-0.00055412,-0.0081262,0.01187171,-0.04008107,-0.07635124,-0.075785,0.0211006,-0.02520577,0.12537868,0.0014497,0.05115136,0.04298889,-0.01778802,0.05115822,-0.01544483,0.09613328,-0.05686982,-0.07382267,-0.01548495,-0.01069694,0.04017273,-0.0169028,-0.02877979,0.05451889,0.002211,0.0503891,0.01468765,-0.00769258,-0.03528114,-0.02706505,0.01300194,-0.01666079,0.06953774,-0.00954162,-0.00794459,-0.0163056,0.0267008,0.02428486,-0.01881245,-0.01839536,-0.05826951,0.09639007,-0.07467547,0.02590248,0.05245028,0.02346682,0.02822125,0.02856258,0.0337539,-0.01740585,0.00954492,-0.01105514,0.02727495,-0.03831903,0.06064196,-0.02456579,-0.03833844,-0.26398835,0.03409558,-0.01649803,0.02949174,-0.01802608,-0.00939256,0.04852195,0.00256151,-0.01204023,-0.05001924,0.0098846,0.07437139,0.0231634,-0.01100756,-0.02725725,-0.03101575,-0.05000347,-0.05584845,0.09129285,0.02172161,0.08934382,0.07029686,0.23207086,-0.0015499,0.0014561,0.0153074,0.01193981,-0.01787835,-0.07561886,0.00065896,0.00793984,-0.02284987,0.11121286,-0.0429388,-0.00710492,0.05749107,-0.00800535,0.05702612,0.00897619,-0.02486219,-0.02626997,-0.01507028,-0.0054839,-0.01830859,0.16487777,0.01685331,-0.0373187,-0.05065621,0.05591157,0.07710657,-0.05020141,-0.00368936,-0.01905869,-0.03139729,0.00719424,0.02185459,0.00542954,0.02881512,-0.00188959,-0.02170455,0.03325674,0.04019805,0.01078479,0.01718807,0.01972171],"last_embed":{"hash":"11vobvy","tokens":275}}},"text":null,"length":0,"last_read":{"hash":"11vobvy","at":1753423522457},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{4}","lines":[148,149],"size":622,"outlinks":[{"title":"_**rules of probability**_: _multiplication_ and _addition_ of _non-mutually exclusive_ _events:_ _**<u>Bayes Theorem</u>**_","target":"https://saliu.com/bayes-theorem.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09596598,-0.02392152,-0.01668593,-0.01397454,-0.03331665,0.07234652,0.01313876,0.04082296,0.0362996,-0.03634955,0.00212821,0.00701529,0.07867816,0.00168695,0.03616369,-0.0142198,-0.0102742,0.00217781,-0.04696727,-0.0134157,0.12126392,-0.03167841,-0.06360039,-0.05638904,0.066631,-0.03889297,0.01059441,-0.05958791,-0.02001124,-0.22468126,0.04806501,-0.00568814,0.01458854,-0.05684088,-0.0923508,-0.0101443,-0.03138566,0.09453575,-0.06059654,0.03642118,0.02437303,0.00851854,0.02535742,-0.01778926,-0.04561294,0.01393272,0.01436726,-0.02083107,-0.01162532,-0.02714499,-0.06536362,0.01828887,-0.01848247,0.00577936,0.01764101,0.00605828,0.04496054,0.09820463,0.03534887,0.07155946,-0.01264374,0.05479687,-0.18376835,0.07755995,-0.00990159,0.03541661,0.00720277,-0.01300538,-0.00168971,0.1086702,0.01409296,0.05060302,-0.01489263,0.07048606,0.02660482,-0.05844162,-0.04893232,-0.08250266,-0.02688427,0.02570322,-0.03211631,-0.02452635,0.01862084,-0.01536363,0.01987395,0.02184265,0.01860597,-0.01751087,0.05313451,-0.03378134,0.03729828,0.03678231,-0.02049419,0.04809297,0.01415792,0.00415351,-0.01872645,-0.01775286,0.02657149,0.14208458,-0.01706618,0.02420577,-0.02076128,0.023346,0.04375151,-0.04018947,-0.00988577,-0.03106262,-0.0029151,0.05701675,0.01386115,0.01080817,0.05056519,-0.03863365,-0.04441658,0.04371916,0.00851687,0.02579857,0.01412534,-0.00917931,-0.05346999,-0.02251019,0.03262013,-0.03191667,0.02730486,0.00753566,0.03176604,0.08107049,0.01141264,0.04361014,0.02472855,-0.0680364,-0.14368407,-0.02767896,-0.04658943,-0.00591669,-0.00344751,-0.03021362,-0.05525237,0.04683404,-0.03754511,-0.0093487,0.05564519,-0.09002096,-0.01943081,0.08244947,0.00246414,0.00604154,-0.03387563,-0.0050052,-0.0141638,0.01754633,-0.03343594,-0.04312478,-0.02613694,0.02747777,0.03849162,0.03094491,-0.05839993,-0.00562547,-0.07229406,-0.03278061,-0.01944626,0.14068368,-0.02147852,-0.03883767,-0.04772329,0.00852544,-0.02177875,-0.07651415,-0.061873,-0.00545401,-0.02027613,0.00622337,0.08290768,0.01121276,-0.10378362,-0.05853157,-0.05163331,-0.02161246,0.02780015,-0.02233648,-0.03088388,-0.01375813,-0.02365949,-0.05168748,0.05410446,0.01536228,0.06142722,0.04136036,-0.10336295,-0.0132743,-0.02867754,0.00043934,-0.01979474,-0.01463293,-0.01769188,-0.05574361,0.03349605,0.01998244,-0.01674706,-0.02086028,-0.01243461,0.03182691,-0.05372112,0.08194003,0.02277895,-0.04785574,0.098744,0.0247992,-0.0433052,-0.01205159,0.06681131,0.05696746,0.01019112,-0.0154752,0.00981022,-0.00829937,-0.02239647,0.0063901,0.02010183,0.03086681,-0.07105753,-0.17466538,-0.01457569,-0.03167446,-0.00368287,0.03235755,0.00633719,-0.02886775,-0.03828812,0.05246425,0.10586219,0.07531701,-0.05903378,0.02219103,0.08080163,-0.0059887,-0.00032245,-0.07065962,-0.03893234,-0.0491792,0.00461508,-0.00511415,0.0186653,0.00583486,-0.05837576,0.05070603,-0.00180024,0.12786502,-0.01485285,0.02694056,0.05993271,0.05390005,-0.01349168,-0.02829225,0.01238808,-0.01571896,0.02584757,-0.02007557,-0.02901002,0.00056833,-0.01779114,-0.03315251,0.05213185,-0.02965863,-0.11449299,-0.05529802,0.05843224,-0.00890395,0.1055332,0.00248529,0.00418001,0.0642428,-0.02530255,0.0332466,0.01576157,0.06910519,-0.06287361,-0.08067784,-0.01310494,-0.01633141,0.04231802,-0.03661074,-0.02590087,0.03116183,-0.01146173,0.04905469,0.01620392,-0.00593286,-0.01729673,-0.03134591,0.00503254,-0.0109858,0.06177834,-0.01329423,0.01005019,-0.03271212,0.03992022,0.02388939,-0.02374394,-0.03213348,-0.04504837,0.09522768,-0.05770194,0.04071468,0.05655317,0.03722619,0.02029059,0.03718238,0.00467917,-0.00092423,-0.00167275,-0.02926828,-0.00069476,-0.0431301,0.06005314,-0.00196452,-0.04041758,-0.26246795,0.05651449,-0.00220063,0.06236848,-0.02330176,-0.02382678,0.02938806,0.02176277,0.01774208,-0.05852167,0.01051692,0.04413379,0.0494113,0.0001858,-0.03261005,-0.01165358,0.00503579,-0.04667136,0.08414151,0.03966084,0.08776379,0.05670087,0.25618935,-0.01016557,0.01730274,0.00384598,-0.0105845,-0.00957788,-0.09406196,0.03066137,0.01603135,-0.00527877,0.10344674,-0.03697051,-0.03339267,0.00878579,0.00308958,0.04513114,0.0037135,0.00507101,-0.05181315,-0.00316535,-0.00885505,-0.01499727,0.16187631,0.01357741,-0.05155532,-0.05531144,0.03246924,0.08123552,-0.08041136,-0.01862792,-0.02788367,-0.0264521,0.01384192,0.05169291,0.02044585,-0.01408959,0.02318514,0.00692567,0.03781816,0.01190567,0.01877085,0.01229177,0.00460052],"last_embed":{"hash":"p6b613","tokens":176}}},"text":null,"length":0,"last_read":{"hash":"p6b613","at":1753423522543},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{6}","lines":[152,152],"size":354,"outlinks":[{"title":"_**pick-4 lottery super strategy based on digit <u>frequency</u> groups**_","target":"https://saliu.com/frequency-lottery.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09037898,-0.03982414,0.00156696,-0.01039248,-0.01885947,0.03843666,-0.00362999,0.03000712,0.06547352,-0.02857324,0.01544647,-0.00348622,0.08369565,-0.01589534,0.03463125,-0.01924364,-0.02097483,0.0010777,-0.08778521,-0.01947496,0.08397204,-0.05185978,-0.05595356,-0.06941628,0.06908741,-0.02148789,0.01310722,-0.06348074,-0.01564944,-0.22974335,0.04287577,-0.00383856,-0.01483329,-0.07218207,-0.0542539,-0.02027822,-0.02397777,0.11646984,-0.06838009,0.04746773,0.01451131,0.00220238,-0.01177576,-0.0268592,-0.033339,-0.00811074,0.00447981,-0.02416752,-0.01372927,-0.02832937,-0.03749047,-0.01336952,-0.01744863,0.00745369,0.00760615,-0.00334812,0.03137212,0.12901828,0.03084644,0.04357467,0.00664544,0.06614887,-0.17921503,0.06098534,-0.0137836,0.02042856,-0.00169927,-0.00591679,-0.01057888,0.13319047,0.00625585,0.04200663,-0.01328066,0.07904004,0.03748834,-0.06046877,-0.06408542,-0.06805126,-0.0334203,0.04861155,-0.03707971,-0.05169906,-0.00211244,-0.00506667,-0.00716594,0.011967,0.02990948,-0.00737251,0.03678782,-0.04230797,0.04793692,0.02035615,-0.00666663,0.05287189,0.00873651,0.02013998,0.01123295,0.02342962,0.04128044,0.1385328,-0.00483019,0.02859943,0.00712778,-0.01459874,0.03479549,-0.03576668,-0.01253259,-0.01085466,-0.00223258,0.03548571,0.03971109,0.00873172,0.0529561,-0.04455835,-0.04241789,0.0602981,-0.00528653,0.02953395,-0.00213452,0.00454977,-0.05468018,-0.02749777,0.01598323,-0.02879387,0.01995124,0.01346141,0.02196432,0.09076742,0.01929069,0.03855497,0.02954062,-0.03982341,-0.13416977,-0.02839367,-0.02770322,0.00479624,0.00942107,-0.02293723,-0.03752735,0.05797246,-0.03585552,-0.01573395,0.02641837,-0.09535843,-0.00128888,0.11192174,-0.00733954,0.00701628,-0.0113162,-0.04420942,-0.0096268,0.016156,-0.03096988,-0.06054647,-0.00793853,0.00843925,0.05065426,0.0231086,-0.05933418,0.00032501,-0.04131882,0.00870702,-0.02931434,0.13725953,-0.00341111,-0.05332907,-0.05185453,0.01809597,-0.02496845,-0.0633077,-0.0580475,0.00199955,-0.00403284,-0.00963433,0.06612555,0.00056157,-0.13566539,-0.04288766,-0.04571973,-0.02118881,0.02647868,-0.00399243,-0.05010865,0.00512783,-0.02888,-0.08498999,0.02614546,0.01416278,0.03089577,0.09779714,-0.09136803,0.01279283,-0.02810119,-0.01468093,-0.01956035,-0.02607905,-0.01255727,-0.0320184,0.02936495,-0.00305121,0.00523396,-0.03717773,-0.00279249,0.03665884,-0.04086481,0.07540105,0.00199772,-0.03283201,0.10505389,0.02314604,-0.03354884,-0.01167016,0.04872322,0.08121565,0.00141645,-0.00014255,0.02476283,0.020904,-0.00777767,-0.01810053,0.02336731,0.00735473,-0.04274953,-0.19331522,-0.01684625,-0.02085348,0.01644352,0.030789,-0.00785718,-0.03813181,-0.00239196,0.02863007,0.11160609,0.05930004,-0.03887666,0.00622718,0.08393865,-0.016055,-0.01720838,-0.06451244,-0.01955785,-0.05071239,0.04520412,-0.00806343,0.02089084,0.02365307,-0.06158381,0.05200122,0.00141853,0.10714637,-0.00515396,0.01862818,0.05536225,0.04625117,0.03013609,-0.03105947,0.0184427,-0.00315515,0.04994236,-0.03677048,-0.02743308,-0.01005377,0.00072177,-0.04584819,0.02677556,-0.04826664,-0.1128314,-0.03162492,0.05569576,-0.0207159,0.10626712,-0.00306372,0.05353997,0.05213694,-0.02305298,0.05113517,0.0022288,0.08556724,-0.06927098,-0.08562033,0.00227653,0.01066807,0.02326849,-0.01008771,-0.03847386,0.0307766,-0.0192611,0.07862553,0.02514989,0.00053089,-0.01414528,-0.03053922,0.00764305,-0.02904321,0.06025081,-0.04385237,0.00289767,-0.04956374,0.05010376,0.02311828,-0.01545217,-0.05645986,-0.03692303,0.10478053,-0.04375979,0.02943514,0.06363861,0.02339563,0.01386693,0.01955401,0.01609753,0.00973776,-0.01278941,-0.01746718,0.00293719,-0.03160969,0.06180821,-0.0228972,-0.02162888,-0.27346781,0.03410526,-0.01194496,0.03397152,-0.04257175,0.01807368,0.04184363,0.03318109,0.05867949,-0.04537859,0.01229217,0.05745522,0.01889635,-0.03824497,-0.02047426,-0.00467181,-0.01690818,-0.03937412,0.07748242,0.03080021,0.07280675,0.05452847,0.2526792,-0.00452221,-0.00553456,0.00210664,-0.00808159,0.00696499,-0.06793325,0.03139722,-0.00391162,-0.01634432,0.09454355,-0.02627733,-0.0266496,0.02180264,0.00700524,0.06060966,0.02293089,-0.00818808,-0.05176979,-0.00387419,-0.01670655,-0.04540738,0.14609891,0.0150897,-0.06268255,-0.06303103,0.02010849,0.07008393,-0.07850283,-0.02853231,-0.02479523,-0.0459208,0.02251862,0.03778171,-0.00045701,0.0149398,0.01397424,0.02096145,0.031023,0.0113224,0.00070481,0.01004379,0.00058466],"last_embed":{"hash":"1odm5ro","tokens":126}}},"text":null,"length":0,"last_read":{"hash":"1odm5ro","at":1753423522602},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{7}","lines":[153,153],"size":240,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08544176,-0.02704933,0.02071393,-0.01914785,-0.02174687,0.04557211,-0.00158659,0.03726511,0.07483456,-0.00134531,0.00984479,0.01127604,0.07741394,0.00274795,0.00795002,-0.03759354,0.00064569,-0.01440784,-0.03818848,0.00228871,0.09092795,-0.03824538,-0.07004628,-0.06843751,0.05778059,-0.0175423,0.00145298,-0.07839717,-0.06341332,-0.22397761,0.03463321,-0.01570701,-0.0085715,-0.05799988,-0.08245322,-0.03129839,-0.02095794,0.09675959,-0.06221597,0.05615775,0.02299277,-0.00180408,0.0019442,-0.03648506,-0.01328772,-0.01076045,-0.0074861,-0.01555104,0.00082806,-0.02298827,-0.06112243,0.01598338,0.00696795,0.00760158,0.00449592,-0.01890286,0.03718098,0.10567415,0.05483895,0.03537225,0.01270369,0.02299335,-0.16585533,0.0490031,-0.0239935,0.03697859,-0.02119249,-0.02069376,0.01642847,0.10332385,-0.02143396,0.04405207,-0.03056059,0.07962912,0.02458301,-0.07701951,-0.03926328,-0.05201056,-0.0338549,0.02240727,-0.0250759,-0.04976921,-0.01371989,-0.00372028,0.00823076,0.01481262,0.0091578,-0.00754839,0.02967825,-0.03992099,0.05215809,0.00851067,0.0110307,0.03625928,0.00991648,0.03076105,0.01915259,-0.00211646,0.01334291,0.13595171,0.02329713,0.03620633,0.00281132,0.02318649,0.04364362,-0.07127968,0.00039806,-0.03495272,-0.01986911,0.02487476,0.02131918,0.00879552,0.07906625,0.00244685,-0.02315677,0.03888223,0.01787884,0.05513319,-0.0160814,0.00760595,-0.03980704,-0.0036189,0.00824434,-0.02541697,0.0323168,0.02705357,0.01670901,0.08237971,0.00560661,0.04426716,0.01648131,-0.06626013,-0.14248127,-0.04737877,-0.0100939,0.01660664,-0.02204551,0.02614257,-0.04051417,0.05047658,-0.01534403,-0.00921691,0.02953573,-0.07972104,-0.03051371,0.08637813,-0.02233118,0.00649739,-0.01834686,-0.04595728,0.00921762,0.01091631,-0.04047791,-0.06421363,-0.03398016,0.01346839,0.03877769,0.07099181,-0.05727101,0.02218854,-0.03928258,-0.02491435,-0.01798363,0.14684312,-0.01669194,-0.01988625,-0.03667084,0.01469959,-0.01619449,-0.05308805,-0.06360108,-0.00704229,-0.05751433,-0.02818331,0.07717203,-0.00903771,-0.09469859,-0.04254348,-0.03793595,-0.02227927,0.02885903,-0.0068253,-0.02083485,-0.0098381,-0.01449726,-0.08095602,0.02162731,-0.00208829,0.02328989,0.05024699,-0.0817329,-0.03185771,-0.03933263,-0.01309707,-0.00759298,-0.03591246,-0.00544949,-0.01902359,0.01050389,0.00135156,0.01888873,-0.01755342,0.0182894,0.03783944,-0.04451186,0.0961852,0.02812244,-0.05201991,0.12579761,0.02420574,-0.05093341,-0.05185331,0.06830042,0.060154,0.01316183,0.00318204,-0.00167617,-0.00208209,-0.02467658,-0.01823445,-0.00189183,0.06203072,-0.06701954,-0.20780481,-0.03185872,-0.01873398,0.01102167,0.03387205,-0.03318581,-0.03296378,-0.01090457,0.05200442,0.15192112,0.06667297,-0.07051567,0.00112295,0.06977178,-0.00371163,-0.00813765,-0.09210963,-0.0290438,-0.0488021,0.04514264,0.00096555,0.03520406,0.00649857,-0.08329586,0.01935086,0.00740248,0.12078906,-0.00059935,0.03252178,0.00781251,0.05510977,0.00724557,-0.04074791,0.04034866,-0.00955919,0.04487157,-0.04341931,-0.01667238,0.00384864,-0.00374264,-0.05268265,0.04032588,-0.04775154,-0.10522904,-0.04177832,0.02944434,-0.0164054,0.07037074,0.01820629,0.06662799,0.04820162,-0.02774449,0.03810686,0.00330112,0.11714342,-0.05143359,-0.08262998,-0.0025757,-0.02146984,0.05858242,-0.00835053,-0.05656478,0.02133846,-0.03541797,0.04342021,0.03222509,-0.0246778,-0.01383424,-0.00448008,0.02933478,-0.01013762,0.09208405,-0.03579252,-0.02455981,-0.03696883,0.05748447,0.01427067,-0.00395478,-0.04862034,-0.03294264,0.11032206,-0.01678152,0.02250097,0.04759194,-0.00175103,0.01679661,0.03400669,0.01980974,-0.01530974,-0.02119613,-0.02194668,0.01375255,-0.04894952,0.06140884,0.0151939,-0.02971642,-0.2533648,0.03154177,-0.0179838,0.02374206,-0.03383676,-0.00654334,0.03844005,0.04769381,0.03822516,-0.04546084,0.0054635,0.05499481,0.03047118,-0.01961639,-0.04023716,0.00956555,-0.00518798,-0.034443,0.09129874,0.01409921,0.06736468,0.04322325,0.25049263,0.00019306,-0.01382359,0.00400911,-0.00009761,0.00474172,-0.06615042,0.03191283,-0.00255334,-0.01082287,0.10328036,0.00123526,-0.01526929,0.00579229,-0.03327653,0.06275051,0.04116947,-0.00480778,-0.04773538,-0.01554742,-0.02903297,-0.01978107,0.13671869,0.01409992,-0.04634602,-0.05423279,0.083156,0.10593005,-0.06423709,-0.0055076,-0.0350715,-0.05956199,0.01866732,0.0415581,-0.01545464,0.01547735,0.01734106,0.00706397,0.03104051,-0.00329146,0.02517196,0.00541671,0.00827296],"last_embed":{"hash":"1kq82cl","tokens":186}}},"text":null,"length":0,"last_read":{"hash":"1kq82cl","at":1753423522641},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{8}","lines":[154,154],"size":494,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07428072,-0.02961532,-0.01120603,-0.02227062,-0.02699221,0.05198942,-0.00684781,0.03431103,0.03310764,-0.01386701,0.01349471,0.00784462,0.07475403,0.00308824,-0.01446875,-0.04586677,-0.0111897,-0.01349354,-0.1002741,-0.0252086,0.09718715,-0.04348955,-0.0544358,-0.06129183,0.05089219,-0.00942998,0.02069177,-0.07415638,-0.04428166,-0.21753056,0.00385828,-0.00697665,0.00431644,-0.05939747,-0.06466456,-0.01539917,-0.00313088,0.1042077,-0.05897646,0.05993764,0.01161086,-0.00645201,0.00976701,-0.04135393,-0.02007493,-0.0056957,0.00863856,-0.02859916,-0.01685064,-0.03096819,-0.03833601,-0.03768066,-0.00755485,0.04011528,-0.00836287,-0.00006215,0.03626664,0.09514791,0.03791634,0.04953377,0.01925056,0.03113857,-0.1842683,0.0471832,-0.02166248,0.06242188,-0.01198037,-0.01235135,0.02232287,0.10589667,-0.00880591,0.05901914,-0.02850931,0.07334189,0.02761616,-0.06259185,-0.05221978,-0.08602764,-0.04325961,0.02657643,-0.03826714,-0.0261556,-0.00756885,0.00629215,-0.01363095,0.0165583,0.01458007,-0.01737709,0.02426017,-0.01680929,0.04054968,0.00506165,-0.01085166,0.04634071,-0.00384663,-0.00638781,0.0275936,-0.0192288,0.01604073,0.13793185,-0.00103478,0.0317458,0.03762324,0.00006397,0.02658507,-0.04883776,-0.02867684,-0.03480394,-0.0052538,0.04220087,0.05408486,-0.00528737,0.04807927,-0.04674801,-0.04536246,0.03254694,0.01963217,0.03659775,-0.01322459,0.01451486,-0.05508387,-0.0401402,0.03536679,-0.02903225,0.03542585,0.0353063,0.0007336,0.07287358,0.03208022,0.02720099,-0.00701342,-0.04358312,-0.13289374,-0.02401415,-0.01961647,-0.00016242,0.01251969,-0.01118785,-0.04258747,0.04243975,-0.01885852,-0.01406173,0.0366066,-0.07687087,-0.00002548,0.10013157,-0.0312169,0.00474901,-0.034674,-0.03543626,-0.0282705,0.03727768,-0.06551979,-0.02878713,-0.03173498,0.00987101,0.05268666,0.01832325,-0.04526626,0.05051045,-0.03055575,-0.00698765,-0.03308245,0.14409658,-0.02299179,-0.02633885,-0.01393782,0.02632241,-0.01439657,-0.04716181,-0.06478202,0.03957691,-0.00374241,-0.00172796,0.06826705,-0.00399824,-0.11797931,-0.03255638,-0.04786153,-0.02558509,0.03613417,0.03188214,-0.04425399,0.00966595,-0.02092936,-0.07221741,0.03369774,0.01718705,0.04983305,0.0681039,-0.08643467,-0.00601389,-0.02059569,-0.00696613,0.02200958,-0.02007606,0.00247988,-0.01588591,0.01682056,-0.00890702,0.00448355,-0.03138585,0.00537662,0.03881098,-0.04573245,0.09333277,0.0035042,-0.04319466,0.12405997,0.00504566,-0.04923366,-0.0323249,0.05211043,0.06354789,0.0320624,-0.0097584,-0.00212064,0.00671331,-0.04193146,-0.01482613,0.00858203,0.03270986,-0.05847441,-0.18868156,-0.05247209,0.01028271,0.03367838,0.01808731,-0.01958736,-0.03763171,0.00558073,0.01743691,0.16261944,0.0808366,-0.01435988,0.00826309,0.06887194,-0.00517045,0.00428361,-0.07992984,-0.0317524,-0.06838309,0.07092105,0.00748252,0.01866236,0.00903452,-0.06543624,0.03009646,-0.00199684,0.11012083,0.01354287,-0.00138187,0.04007759,0.04363906,0.00448996,-0.04007652,0.03199447,0.00584981,0.07788716,-0.07383441,-0.02622304,0.01373142,-0.00032097,-0.034697,0.02167457,-0.04034913,-0.11358052,-0.03744132,0.03976836,-0.02832283,0.0894329,0.01869899,0.02758522,0.0447209,-0.03297333,0.03792002,0.00839138,0.09580909,-0.05245491,-0.08717316,0.0060334,-0.01720479,0.02420453,-0.01659436,-0.03653486,0.03283753,-0.03017544,0.04725965,0.00000994,-0.03950557,-0.018669,-0.01475531,0.00960494,-0.01828697,0.07039499,-0.03295377,-0.0010555,-0.05153811,0.0438136,-0.01087451,-0.03691534,-0.04482938,-0.05607099,0.09902749,-0.05837741,0.02858772,0.06224346,0.01701681,0.05031818,0.0055065,0.00646704,0.00039323,-0.00250041,-0.02294252,-0.02135087,-0.04086855,0.05891849,0.00467138,-0.03329543,-0.24604313,0.05495279,-0.00962203,0.03191944,-0.0253142,-0.00560279,0.04181076,0.034096,0.05506727,-0.05520031,0.02322705,0.05510864,0.01832622,-0.04114157,-0.03640206,0.00950385,-0.01078687,-0.04814371,0.07806038,0.04227732,0.0686492,0.06274167,0.25734848,0.01761407,0.01496624,0.00053676,-0.00541172,-0.00650589,-0.11255332,0.04493972,0.02332617,-0.01962548,0.10706051,-0.03460656,-0.01309196,0.02674038,0.00581607,0.06049276,0.01846984,0.00375224,-0.05816927,-0.00001743,-0.03621849,-0.04034128,0.14089656,0.03152033,-0.05549564,-0.05693512,0.05118262,0.10176399,-0.05284327,-0.02040801,-0.03706923,-0.0509056,0.02004195,0.03716464,0.02203781,0.01692492,0.01783562,0.01010786,0.01859632,0.0231295,0.01981947,-0.00684127,0.02127258],"last_embed":{"hash":"1dsv4wh","tokens":134}}},"text":null,"length":0,"last_read":{"hash":"1dsv4wh","at":1753423522704},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{9}","lines":[155,155],"size":324,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09427208,-0.06185178,-0.00720879,0.00466164,-0.02863455,0.03352036,0.02346279,0.04701154,0.04228922,-0.00775642,-0.00878384,-0.00605097,0.06659541,0.01093361,0.0145542,-0.01931157,-0.01872465,0.01321311,-0.0638762,-0.00132431,0.10914027,-0.03946591,-0.04685269,-0.06287673,0.07824817,-0.02460298,0.00242004,-0.07256842,-0.0310414,-0.24517222,0.01776598,-0.01023266,0.01032068,-0.08235,-0.04846474,-0.01825031,-0.0111496,0.05561706,-0.04838002,0.04684627,0.03220293,0.01459063,0.01506424,-0.03759547,-0.01967277,-0.00710134,0.01886894,-0.00829198,-0.04872192,-0.02684509,-0.04233415,0.01557206,0.01077628,0.04650985,-0.01180085,0.01141444,0.04232768,0.10978295,0.05410528,0.06058199,0.00265604,0.0487448,-0.17524722,0.04485843,-0.0207331,0.01606325,-0.003891,-0.04943249,0.00982333,0.08251681,0.00464398,0.05164456,-0.01778362,0.08914439,0.02202968,-0.06311943,0.00650857,-0.09063246,-0.04299964,0.05319295,-0.0201033,-0.05010717,-0.0224764,-0.01858045,-0.01170975,0.01604038,0.032683,-0.0145475,0.0180187,-0.06359793,0.06018141,0.03460105,0.00044335,0.05019946,0.02605992,0.01316077,0.00942061,0.01388348,0.0011919,0.12090194,-0.00751996,0.02483634,-0.00457929,0.00878273,0.04283937,-0.06290073,0.0083562,-0.03106333,0.00993711,0.01772239,0.02247953,0.00985404,0.0739901,-0.00675225,-0.01079727,0.07331227,0.04060558,0.02406112,-0.01128241,-0.00532801,-0.04667609,0.00341913,0.02697067,-0.03441285,0.00804269,-0.01892999,-0.001087,0.08566562,-0.00319054,0.01728262,0.02487153,-0.0646893,-0.10960923,-0.03773538,-0.01310641,0.01031527,0.00334981,0.02144967,-0.00687971,0.04768891,-0.03092731,-0.04713072,-0.00263135,-0.08075742,-0.04375743,0.08015221,0.0079111,0.00319618,0.00411298,-0.03687058,-0.01294792,0.04254159,-0.05038336,-0.04520674,0.0055106,0.00159508,0.04418966,0.0347616,-0.06538074,0.00328689,-0.03107217,-0.00508517,-0.03083582,0.17573105,-0.0391015,-0.02026866,-0.0221784,0.0126439,-0.01818687,-0.07117034,-0.04931954,-0.0056436,-0.01479684,-0.01067153,0.08074015,-0.01316985,-0.09787581,-0.02219749,-0.03157541,-0.0198993,0.00399103,-0.02662896,-0.0169019,0.02518533,-0.01710876,-0.06675272,0.02617742,0.01373833,0.02884045,0.04174348,-0.08113229,-0.00949801,-0.05259774,0.00260267,-0.0278703,-0.02468946,0.0018212,-0.04390211,0.00353344,0.00429413,-0.02482983,-0.03704886,0.00707029,0.03336453,-0.04249136,0.10149261,0.02198866,-0.03370949,0.08237725,0.04372512,-0.03558256,-0.03102647,0.01751852,0.05038034,0.01625919,-0.00233431,0.02579692,0.02584307,-0.0401064,-0.03090024,0.01862468,0.03497391,-0.03800659,-0.21822338,-0.03430639,-0.060628,0.01173839,0.04323921,0.00183669,-0.02162225,-0.03632155,0.01791536,0.11175646,0.05701315,-0.02869368,-0.0144216,0.06676342,-0.01654095,-0.03131782,-0.09751318,-0.02764873,-0.10373267,0.03577721,-0.02342419,0.04891362,-0.03137124,-0.07984736,0.0232276,0.01844854,0.13389389,0.02101383,0.03991347,0.03071401,0.0393807,-0.00541565,-0.01829673,0.01700688,0.00677737,0.05006696,-0.06245698,-0.04307657,0.01603342,-0.00361465,-0.01937272,0.01990704,-0.07054082,-0.08484019,-0.05654317,0.02876175,-0.01654417,0.14646022,-0.00103404,0.07427984,0.05922019,-0.03170701,0.04222102,0.01422278,0.07270593,-0.03784758,-0.08160669,-0.01825934,-0.00258411,0.05659473,-0.00378492,-0.01726977,0.05143666,-0.01192055,0.0520313,0.03586108,-0.02268242,-0.02171581,-0.00867192,0.02304975,-0.02221874,0.09940514,-0.03671758,-0.02603095,-0.0031459,0.04711079,0.00972442,-0.01136339,-0.03632714,-0.03469075,0.10154405,-0.04382142,0.05711487,0.06150218,0.01684804,0.02019006,0.01085689,0.01589224,-0.01979394,-0.01774123,-0.02343465,0.00651345,-0.06826933,0.02136773,0.02370244,-0.03561493,-0.24551654,0.0373802,-0.02695511,0.03603506,-0.03065702,-0.01484428,0.06951303,0.0438863,0.01808132,-0.04987498,-0.01840921,0.05861348,0.04040244,-0.01227919,-0.03091864,-0.01568509,-0.01072902,-0.05729371,0.07096966,0.04517488,0.11735725,0.07383092,0.23946735,0.02655365,-0.01106166,0.02038047,0.01461277,0.00727894,-0.08265863,0.01587057,-0.00603676,0.00698835,0.08622029,-0.03678191,0.02349472,-0.01278338,-0.00689826,0.06072387,0.02459174,-0.02234946,-0.04217802,-0.00634321,0.02798565,-0.02358863,0.15210497,0.01576378,-0.03672287,-0.06018087,0.05834417,0.06970348,-0.06419358,-0.02688301,-0.00792422,-0.04794626,-0.00123244,0.03907358,-0.00461451,0.01962,0.00475066,-0.02931111,0.02452561,0.04603021,0.01306967,0.01676514,-0.00730665],"last_embed":{"hash":"zief5","tokens":214}}},"text":null,"length":0,"last_read":{"hash":"zief5","at":1753423522756},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{10}","lines":[156,156],"size":517,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08890534,-0.0220578,0.01413306,0.01459274,-0.03323767,0.05203629,0.02560235,0.03205916,0.05780168,-0.02718865,-0.00476192,-0.00011704,0.09906276,-0.00723804,0.03090848,-0.01700204,-0.01100503,0.01734753,-0.06598273,-0.03309598,0.08293404,-0.0376054,-0.06384218,-0.04541086,0.07497673,-0.02777338,-0.00584354,-0.08451668,-0.01706846,-0.21639563,0.05291403,-0.00755518,-0.0081427,-0.06297068,-0.07023535,-0.02814752,-0.02927854,0.10538366,-0.07019003,0.06103704,0.02510569,-0.01224565,-0.01270184,-0.03938955,-0.02286541,-0.00324374,0.01716603,-0.01363787,-0.030447,-0.02837336,-0.02841315,-0.00661628,-0.00381519,0.01015236,0.00251632,0.01483372,0.01228026,0.12498634,0.02763148,0.06140822,-0.001366,0.04485973,-0.1668153,0.04810645,-0.01878637,0.02974922,0.00204433,0.00603515,-0.00370746,0.11854579,0.01224022,0.05913719,-0.02881134,0.07837231,0.01908732,-0.07108101,-0.04817043,-0.06401841,-0.04961847,0.03281197,-0.0379782,-0.05216807,0.00990561,-0.03616627,0.0068332,0.00580321,0.01858549,-0.04053149,0.04479549,-0.0396798,0.046554,0.03405039,-0.00819444,0.03500685,0.02564744,0.01679212,0.00482199,-0.00717915,0.03554319,0.16074766,-0.00456048,0.02093708,0.00108872,-0.00549308,0.04600181,-0.04210801,-0.00175078,-0.04666837,-0.00671317,0.05000581,0.03389419,0.01231549,0.04157963,-0.02522179,-0.02734947,0.04183332,-0.00288251,0.03685791,0.01607794,0.01836352,-0.06349657,-0.02434454,0.01789941,-0.03499436,0.01322922,0.01333054,-0.00099057,0.09689704,0.01047458,0.01911043,0.02413413,-0.04578373,-0.12934156,-0.03831694,-0.02882376,0.00033488,0.00131072,-0.01899989,-0.02492138,0.0552248,-0.0625304,-0.00654591,0.06682977,-0.10330337,-0.00207389,0.1008558,0.00945414,0.01400694,-0.02567226,-0.0241892,-0.01803282,0.02360504,-0.04108512,-0.05950297,-0.01851307,-0.01367279,0.04027066,0.05067208,-0.03569428,0.02036711,-0.05127721,-0.01853136,-0.04298607,0.14572448,-0.01612508,-0.05536899,-0.02917481,0.0122597,-0.03268775,-0.04909731,-0.04532232,0.00696961,-0.01145738,-0.00343527,0.05623576,-0.0024912,-0.11748751,-0.04385691,-0.04512428,-0.02209176,0.03126139,-0.01256668,-0.04334195,0.02098212,-0.01112378,-0.06498084,0.04054395,0.00756332,0.04211731,0.04899435,-0.09429715,-0.01382639,-0.02272538,0.00259652,-0.00781993,-0.02720261,-0.00509401,-0.04174146,0.00286244,0.00676701,-0.02481369,-0.04380807,0.00148382,0.04598178,-0.04729951,0.08049067,0.00932224,-0.05076078,0.09840923,0.02143756,-0.0483882,-0.030219,0.03193181,0.06985362,0.03741295,0.00262468,0.01396896,0.0031775,-0.03470831,-0.00639647,-0.01005168,0.03975262,-0.04499566,-0.18729633,-0.02782545,-0.02136949,0.02867821,0.03749632,-0.00175018,-0.02929606,-0.01782737,0.01117642,0.10751893,0.07612433,-0.05277098,0.01308437,0.08562182,-0.01028821,0.00025541,-0.06411636,-0.02370947,-0.05824741,0.02335217,0.01695035,0.02427609,0.01384917,-0.056898,0.02986997,0.00620043,0.11766852,-0.00790574,-0.00827987,0.05548789,0.03207137,0.01423403,-0.02753254,0.03115771,-0.00809662,0.04485634,-0.01745704,-0.04823913,-0.00425956,-0.00748189,-0.04609777,0.01896063,-0.05560636,-0.10559592,-0.03870266,0.05545283,-0.0325259,0.12371121,0.02945028,0.04074575,0.0794076,-0.03080736,0.04709275,-0.0027563,0.08155847,-0.06134695,-0.08437885,-0.00019063,-0.01759871,0.02513449,-0.03093931,-0.03812877,0.04264543,-0.02494278,0.05203167,0.04160762,-0.0113896,-0.02044602,-0.02416539,0.02510211,-0.01975705,0.08107278,-0.04014903,-0.00689837,-0.02993067,0.04620123,0.01713478,-0.01796937,-0.04694699,-0.04366739,0.13543637,-0.05802349,0.02733481,0.06194962,0.0249356,-0.01079219,0.03781943,0.00433395,-0.00315235,0.00140429,-0.0359286,0.00020424,-0.02394197,0.0565888,-0.01486281,-0.03346514,-0.26348481,0.04019547,-0.02626696,0.04307067,-0.03883342,0.00682695,0.02764522,0.03924079,0.03553395,-0.04922701,-0.00572605,0.04881337,0.02521657,-0.00790617,-0.03085472,-0.01291348,0.01423429,-0.0712442,0.08375377,0.03056839,0.08694677,0.05300393,0.24551994,-0.00547497,0.01013875,0.0213229,0.01519876,-0.00968954,-0.07738421,0.03400446,0.0018868,-0.01942983,0.09594766,-0.00204484,-0.01818552,-0.00026375,0.00254646,0.07500843,0.01592436,0.00865268,-0.07212797,0.00070913,0.0005117,-0.0174043,0.13737389,0.00960125,-0.05743167,-0.06294531,0.02265843,0.08438701,-0.08353268,-0.01160368,-0.02526301,-0.0353669,0.00853953,0.04451057,0.0097238,0.02283986,0.01981569,0.01122803,0.0468886,0.0164682,-0.0038508,0.00206249,-0.01154819],"last_embed":{"hash":"7rnkqw","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"7rnkqw","at":1753423522827},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{11}","lines":[157,158],"size":256,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08377972,-0.02756683,0.01067461,-0.04466326,-0.02988232,0.07629386,-0.04176575,-0.0284368,0.01247915,-0.02723012,0.04165471,0.0064668,0.05131073,-0.02272499,-0.01089143,-0.03256352,-0.0068179,-0.02827857,-0.07940648,0.01369867,0.08269019,-0.04448422,-0.04411554,-0.04344974,0.04472901,0.00615892,-0.00031688,-0.04815377,-0.03493967,-0.26683539,0.04841016,0.02037038,0.02259079,-0.02479992,-0.05381108,-0.03312262,-0.04266109,0.0930943,-0.07507927,0.05935841,0.02984121,-0.01773161,0.02763908,-0.01043682,-0.01847816,-0.01865585,-0.00602555,-0.01662059,0.05410124,-0.02061757,-0.0424624,-0.00130834,-0.01324545,0.0257043,0.05157057,-0.00288548,0.07541939,0.11657748,0.06263717,0.07312033,0.01198683,0.07824819,-0.19541855,0.07319974,0.00282802,0.02615445,-0.02571023,-0.02643698,-0.00175882,0.05700453,-0.06472006,0.04504218,-0.03332652,0.06550579,0.01173318,-0.0517524,-0.05782493,-0.04318804,-0.05660549,0.02051587,-0.04441124,-0.04687471,-0.00768936,-0.00698644,-0.00629668,-0.0084787,0.04083244,0.03036691,0.02602315,-0.05190687,0.05985211,0.01954515,0.041955,0.0537637,0.00699231,0.01388361,0.00522941,0.03353781,0.03191346,0.12957907,-0.00111756,-0.01608893,-0.02879597,0.03074453,0.01895726,-0.05417982,-0.00140099,-0.02381567,-0.00705783,0.04701566,0.02919435,0.01128027,0.04084401,-0.01710569,-0.01906031,0.0264534,-0.01951817,0.01317627,0.0026795,0.03652899,-0.0558993,0.01202621,-0.00573344,-0.01508641,0.02694655,0.01933441,0.02278443,0.05610978,0.02081166,0.04682433,0.02461084,-0.02111979,-0.17073037,-0.06239405,0.00927293,0.01794093,0.03199352,0.00265465,-0.00972879,0.01801035,-0.03585435,-0.02539227,0.01185335,-0.09361528,-0.01831755,0.1078633,-0.01418878,0.01760392,0.00698783,-0.01457877,0.01238778,0.00301385,-0.05982858,-0.04289948,0.000765,0.0187625,0.06496508,0.0475236,-0.067937,0.00446595,-0.00604721,-0.02354733,0.00332759,0.12643225,0.00231236,-0.0609696,-0.04927244,0.00019851,-0.00510175,-0.06821264,-0.04691051,0.00710293,-0.0234075,-0.00147775,0.0655955,0.00972581,-0.07499997,-0.06037205,-0.03349278,-0.03355703,0.03153172,0.00254959,-0.04317183,-0.00139608,-0.04648824,-0.10100532,0.02511699,0.01561782,0.04314659,0.07393061,-0.0423093,0.00168601,-0.02936624,0.00658881,-0.01544831,-0.00428417,0.00248693,-0.03299414,0.05089385,-0.04609625,-0.00479223,-0.00397035,0.01873788,0.04225264,-0.05763107,0.07550358,0.01340064,-0.08002799,0.10121607,0.00620638,-0.06571954,-0.04231054,0.06769275,0.07987382,-0.01461909,0.0426181,0.00737076,-0.04448372,0.01030712,-0.00814384,0.01280826,0.01677386,-0.03906818,-0.18160449,-0.02479057,-0.03402471,-0.0016838,0.03336642,-0.02508188,-0.00091951,-0.01612765,0.01537632,0.12496364,0.08284707,-0.02294271,-0.01444819,0.06307198,-0.0306615,-0.03294301,-0.04394355,-0.04321911,-0.05683346,0.03184727,-0.01590969,0.01174046,-0.01908428,-0.0613681,0.07792491,-0.03630422,0.12314034,0.01306088,-0.00525802,0.03438291,0.06588546,-0.0061809,-0.02834474,0.00823859,-0.01231218,-0.00395489,-0.06535518,0.01579375,-0.04795,0.0121962,-0.05322526,0.00499618,-0.04395224,-0.13901405,-0.00350337,0.0476421,-0.02331163,0.06426238,0.01461695,0.06123003,0.05106557,-0.02794208,0.04170949,0.01361742,0.05534572,-0.06727839,-0.05881943,0.0189163,0.02038384,0.03949207,-0.0236057,-0.03093641,0.02608305,-0.01113251,0.04526124,0.05892543,0.0064642,-0.01262879,-0.00639938,0.01238283,-0.01041092,0.04343949,-0.00658104,0.00690712,-0.02231129,0.06553804,0.00012065,-0.00632097,-0.00017142,-0.03841497,0.07153057,-0.02079244,0.04166541,0.08586379,0.03130279,0.01922685,0.02936286,0.03878951,0.0069109,-0.01022977,-0.05302142,-0.01255373,-0.03366191,0.04384179,0.02687335,-0.02600336,-0.26420557,0.04200835,0.00344371,0.05952172,-0.03558365,0.00460803,0.02288986,0.04459222,0.03080685,-0.01115951,0.01902933,0.02577075,0.05503996,-0.04300795,-0.00875342,-0.02126839,-0.03492153,-0.02439204,0.09279205,0.00641762,0.0857842,0.03211332,0.24955237,-0.01234406,-0.02268918,-0.00748751,0.00807606,0.00618221,-0.04803256,0.04182113,-0.02613848,0.00058365,0.09906408,0.01395995,-0.01973577,0.00336612,-0.02289479,0.04030131,-0.02117695,-0.00361871,-0.08418064,-0.02681955,-0.03455335,-0.03914047,0.14015882,0.05213882,-0.0561343,-0.06229572,0.0267844,0.07322047,-0.06316007,-0.03622072,-0.05771526,-0.03660573,-0.01061803,0.05071415,-0.00680762,0.022813,0.02193126,0.01355436,0.05767307,0.01229399,0.02126888,-0.00391695,-0.0089816],"last_embed":{"hash":"r00pvu","tokens":265}}},"text":null,"length":0,"last_read":{"hash":"r00pvu","at":1753423522869},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>#The other path considers a higher probability of a filter level to occur.#{13}","lines":[161,162],"size":880,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07795437,-0.03085926,0.00100153,-0.06467938,-0.01679325,0.04910452,0.03670484,0.0243957,0.00332941,-0.04152028,0.04756136,-0.02273614,0.09083539,0.01155548,0.00721058,-0.03547744,0.0268364,-0.04826119,-0.12231639,-0.02199479,0.09432912,-0.04942033,-0.0248653,-0.09434562,0.06401382,-0.00532642,-0.00767795,-0.05681847,-0.05809837,-0.25529405,0.02459147,0.00352716,0.04468916,-0.06152811,-0.08602944,-0.03677984,-0.01835674,0.09356154,-0.04804964,0.02022449,0.0332165,-0.02696471,0.02851269,-0.0545199,-0.0433571,-0.00207318,0.00559304,-0.0398178,0.04581606,0.00764365,-0.07683206,-0.0155331,0.00652418,0.03042253,0.01978559,0.00337996,0.00959553,0.09282684,0.05834932,0.03582358,0.00580049,0.03312397,-0.18083549,0.03594264,-0.02814537,0.0134597,-0.03884427,-0.02654224,-0.03035609,0.03080093,-0.00488815,0.0538442,-0.04750103,0.05469893,0.02274237,-0.07302055,-0.04393057,-0.05059428,-0.02384309,0.00025878,-0.0383313,-0.03813533,-0.00369981,0.00770499,0.00261867,0.03651706,0.05733614,0.02853505,0.02500674,-0.05180368,0.04552012,0.00825416,-0.01309369,0.04197035,-0.01613436,0.00557178,0.03188701,0.01070916,0.00440884,0.13029267,0.02303754,0.01203455,-0.01856221,-0.01935153,0.035939,-0.02959586,-0.00287583,-0.02535814,0.01358375,0.03952354,0.03634095,0.01716935,0.04166071,-0.04485643,-0.03102148,0.0262872,0.00847871,-0.01027483,-0.02279706,0.02544702,-0.03483754,-0.03505911,0.00013123,-0.0028039,0.06836247,0.05117371,0.03328191,0.08116666,0.0467708,0.05521975,0.01224454,-0.00888872,-0.12681432,-0.03814262,-0.01064965,-0.00469572,0.04406117,-0.03412279,-0.01728644,0.00944015,-0.05196454,0.01166678,0.04401329,-0.05428633,-0.02043016,0.08080884,-0.0043318,0.01062081,0.01464478,-0.06467707,-0.00686201,0.02394074,-0.03933197,-0.0395964,0.0140253,0.01457685,0.04646412,0.05164374,-0.07393931,-0.02293524,0.00412623,-0.04265049,-0.00353129,0.11527511,-0.02013053,-0.0230662,-0.05348302,0.01207599,-0.0364773,-0.05328037,-0.02716926,-0.00773621,-0.03272558,0.00009648,0.05816001,-0.03795527,-0.13338365,-0.06022819,-0.04950621,-0.01048783,0.00757744,0.00694024,-0.04152454,0.01488162,-0.02917701,-0.0872484,0.05844551,0.01935137,0.03725538,0.09930091,-0.03314647,0.00337404,0.024051,0.01105811,0.00771843,-0.03311235,-0.01009431,-0.00858598,0.02234605,-0.01758347,0.02129122,-0.00004806,0.01922981,0.0405,-0.05039173,0.08173762,-0.0212432,-0.02161697,0.11109798,0.05688894,-0.06977009,-0.00833461,0.03050599,0.05595052,-0.01286662,0.00365881,0.03861172,-0.00486057,0.01822776,-0.00552799,0.00649914,0.01191669,-0.06090495,-0.22617759,-0.02045872,-0.03310613,0.01715907,0.04175917,-0.04086006,-0.00215803,-0.03471179,0.03419251,0.10282964,0.09334841,-0.06468831,-0.00849527,0.06434004,-0.00119836,0.02089982,-0.05720648,0.00010336,-0.07040468,0.03053531,0.03126813,0.03400908,-0.00282145,-0.01974289,0.03351674,-0.01536981,0.13951151,-0.00905965,0.0305301,0.00279462,0.08121264,0.02412607,0.01936731,0.01151683,-0.00936008,0.0262401,-0.07350229,0.01872723,-0.02331459,0.00317864,-0.06351981,0.02254746,-0.01724061,-0.11650141,-0.03104992,0.05814865,-0.00772398,0.05863236,-0.01145026,-0.00756255,0.04942598,-0.01282684,0.02613826,-0.0082294,0.10181382,-0.06047717,-0.08753553,-0.00917983,0.00776271,0.00706875,-0.01576546,-0.03456286,0.02822007,-0.02795304,-0.00436369,0.05721755,0.00821302,0.00723484,0.00548132,-0.02030276,-0.01563233,0.07819345,-0.0257206,0.03846717,-0.05842366,0.05524007,0.0008406,0.00773866,-0.02930954,-0.05191477,0.09223954,-0.01695335,0.02787163,0.04509122,0.02347523,0.05813769,0.04869683,0.03160791,0.00508753,-0.01942665,-0.01928363,-0.00025325,-0.01999567,0.01693047,-0.00664432,-0.01798775,-0.24602914,0.02883772,-0.02671066,0.05832753,-0.03181129,0.00578968,0.04545289,0.04445595,0.04279571,-0.0441229,0.00327171,0.06236636,0.01097808,-0.07204709,-0.00827329,-0.01169838,-0.03262157,-0.01518511,0.11952096,0.00543995,0.10371874,0.01262228,0.2510944,-0.0028628,-0.02368218,0.01719567,-0.01115141,-0.0061966,-0.07574862,0.02595911,0.02160997,-0.00593403,0.10485858,-0.00013832,-0.01347435,0.01835337,-0.030385,0.0299791,0.01045288,-0.00673445,-0.03219372,-0.0226022,-0.04510254,-0.01910031,0.13553818,0.02070462,-0.04022366,-0.06202752,0.04319237,0.08864564,-0.07762682,-0.02932849,-0.02263808,-0.02253349,0.02727366,0.0596143,-0.00254029,0.02679921,0.0289049,-0.01204927,0.04360343,0.00344919,0.02691366,-0.02307713,0.00494421],"last_embed":{"hash":"egkqh1","tokens":453}}},"text":null,"length":0,"last_read":{"hash":"egkqh1","at":1753423522960},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>","lines":[165,249],"size":14374,"outlinks":[{"title":"_**UK 6-59 Lotto: Statistics, Lottery Strategy, Hottest-24 Numbers**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/_4vQk00mxzI","line":80},{"title":"_**Results Draw #1, World Lottery Championship**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/E-aM_zUsQFY","line":81},{"title":"_**Filters, Restrictions, Reduction in Lotto, Lottery Software**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/f_I6udi8raQ","line":82},{"title":"_**Lottery Software, Strategies for Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/0P7xKzncan8","line":83}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07655369,-0.03075664,0.00135913,-0.06562699,-0.0198602,0.04972707,0.03641348,0.02437309,0.00059208,-0.04193418,0.04731118,-0.02376317,0.08970705,0.01520145,0.00639767,-0.03230425,0.02695299,-0.04960844,-0.12416972,-0.02263246,0.09486604,-0.05091592,-0.02377271,-0.09237708,0.06676207,-0.0050283,-0.00380082,-0.05680936,-0.05713761,-0.25647965,0.02360963,0.00475544,0.04357296,-0.06128975,-0.08705589,-0.03590848,-0.01851236,0.09405027,-0.04698968,0.01856659,0.03354227,-0.02605433,0.02862053,-0.05656457,-0.04546648,-0.00331134,0.00566411,-0.04182614,0.04593628,0.00841166,-0.07370183,-0.0155866,0.00818191,0.02969364,0.01917437,0.0051356,0.0100131,0.09179517,0.05816674,0.03689273,0.00561657,0.03206113,-0.1821887,0.03436185,-0.02888245,0.01440918,-0.03938223,-0.02530921,-0.03135477,0.02980868,-0.00403651,0.05269573,-0.04682941,0.05368536,0.02125317,-0.07267492,-0.04305881,-0.05304035,-0.02113851,0.00003918,-0.03886697,-0.03643231,-0.00341973,0.00862165,0.00127029,0.03458496,0.05641098,0.02833183,0.02312818,-0.05099256,0.04594954,0.00877677,-0.01536256,0.04179395,-0.01645418,0.00651546,0.03289682,0.01230514,0.00330439,0.12838808,0.02559431,0.01234897,-0.01559262,-0.02123245,0.03642301,-0.02649838,-0.00323334,-0.02342961,0.01263106,0.03657506,0.0380009,0.01635629,0.0399434,-0.04549042,-0.03139257,0.02628855,0.00903563,-0.00938485,-0.02282266,0.02594296,-0.03374713,-0.03712583,0.00096634,0.00022778,0.06727817,0.05267039,0.03333006,0.08216162,0.04810715,0.05435094,0.01010322,-0.00902279,-0.1237778,-0.03802716,-0.01367097,-0.00546539,0.04499036,-0.03798064,-0.01676331,0.0074163,-0.05028377,0.01392264,0.04367393,-0.05303833,-0.02031389,0.08167979,-0.00197921,0.01177017,0.01318471,-0.06706199,-0.00885844,0.0241535,-0.03796709,-0.03888395,0.01459803,0.01557536,0.04408115,0.04967132,-0.07278807,-0.02282694,0.00293949,-0.04519248,-0.00500749,0.11678555,-0.02226759,-0.01901892,-0.05237179,0.01113817,-0.03604324,-0.05382208,-0.02515451,-0.00559507,-0.03042841,0.00171086,0.05797728,-0.04079103,-0.13456547,-0.06025948,-0.05057076,-0.00937332,0.00777814,0.00865103,-0.04304258,0.01582403,-0.02938656,-0.08627436,0.05830713,0.01965916,0.03707647,0.10008767,-0.03247294,0.00524434,0.02527785,0.01222117,0.00676583,-0.03302605,-0.00976418,-0.00746384,0.02204228,-0.02349491,0.02208127,-0.00192465,0.01850581,0.03949339,-0.04907978,0.08032761,-0.021412,-0.01835418,0.11179023,0.057557,-0.06926095,-0.00966549,0.03062479,0.05486912,-0.01243646,0.00195079,0.04060746,-0.00318899,0.01858842,-0.00525324,0.0084814,0.01221531,-0.06090141,-0.22764979,-0.02243653,-0.03167735,0.02067246,0.04466872,-0.04217275,-0.00125908,-0.03463275,0.03132287,0.10063721,0.09457224,-0.06422985,-0.00911689,0.06426742,-0.00092433,0.02371585,-0.05662819,0.00069168,-0.06985348,0.03149891,0.03148285,0.03274478,-0.00398845,-0.01883529,0.03307771,-0.0144431,0.14041938,-0.01283869,0.02937673,0.00001192,0.08023662,0.02355601,0.01792794,0.0120814,-0.0088,0.02729525,-0.07317486,0.01804248,-0.02240755,0.00238913,-0.06372394,0.02151898,-0.01705898,-0.11594962,-0.03295637,0.0577947,-0.00955504,0.05919297,-0.01170462,-0.01096663,0.0480531,-0.01362099,0.02587283,-0.00920138,0.10308273,-0.05972592,-0.08850129,-0.00739477,0.00630881,0.00725794,-0.01538603,-0.03446165,0.03114449,-0.02670049,-0.00507827,0.05427768,0.0096981,0.00664478,0.00639945,-0.0209107,-0.0151612,0.07724044,-0.02856707,0.04080541,-0.05807716,0.05282553,-0.00098691,0.00646976,-0.02744686,-0.05311596,0.09266165,-0.01655654,0.02691183,0.04313844,0.02575686,0.06000719,0.0488142,0.03382282,0.006961,-0.01902017,-0.02010161,-0.00121437,-0.02082633,0.01559908,-0.00739879,-0.01923677,-0.24528652,0.02913838,-0.02771683,0.05842265,-0.03374015,0.00359575,0.04656225,0.04424534,0.04142571,-0.04277979,0.00126906,0.06145468,0.01297298,-0.07394966,-0.00560936,-0.01192933,-0.03253639,-0.01711712,0.11818258,0.00358428,0.10533037,0.01453327,0.25109988,-0.00294176,-0.02358204,0.01850419,-0.00996339,-0.00649645,-0.07767438,0.02418235,0.02254791,-0.0062144,0.10426752,0.00134789,-0.01204189,0.02155523,-0.03088225,0.02855374,0.00990004,-0.00678664,-0.031539,-0.02087502,-0.04480654,-0.01837841,0.13375948,0.02190696,-0.04024881,-0.05978441,0.04274119,0.09048439,-0.0750172,-0.0280501,-0.0214969,-0.02380899,0.02578642,0.05870963,-0.00153346,0.02663257,0.02986194,-0.01371935,0.04296055,0.00586393,0.02948355,-0.02444088,0.00257364],"last_embed":{"hash":"7l1713","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"7l1713","at":1753423523146},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#{1}","lines":[167,183],"size":2945,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06630299,-0.04932509,0.00347913,-0.08229222,-0.0180216,0.06247038,-0.00334859,0.01642875,0.02133343,-0.01177118,0.01868367,-0.00793369,0.07404891,-0.0013655,-0.01207175,-0.02612434,0.02281428,-0.01709972,-0.11298989,-0.00336737,0.11725275,-0.05272907,-0.04778795,-0.07799871,0.06375828,-0.01208375,-0.03036321,-0.04705448,-0.0303263,-0.23465908,0.04295072,-0.01732796,0.02581302,-0.04919081,-0.07861125,-0.04563871,-0.02192247,0.02818449,-0.08551459,0.04228948,0.03359313,0.01716093,0.04753115,-0.03239507,-0.02578438,-0.00820133,0.01793472,-0.07053269,-0.00458304,0.00598042,-0.03743989,0.01950978,0.02591051,0.03339707,0.01567691,0.00282569,0.0213202,0.10765769,0.03371834,0.03253191,0.02989862,0.04485856,-0.18938008,0.04870486,-0.0248987,0.01406689,-0.01131484,-0.04909812,-0.00847348,0.05534383,0.03855054,0.05797153,0.00697875,0.03054456,0.00733719,-0.06230854,-0.06555708,-0.07187989,-0.01639583,-0.02326636,-0.0516523,-0.02448221,0.02739806,-0.02996392,0.01117561,0.01055363,0.02628755,0.01880203,0.03335115,-0.05398332,0.05227572,-0.00413219,0.0251425,0.06125852,0.01196808,0.04396479,-0.00102338,0.00227575,-0.0206244,0.12196997,0.02418626,-0.00384787,-0.06401395,-0.01010574,0.06506988,-0.03262435,-0.04084399,-0.00769785,0.00671369,0.01922833,0.00661298,-0.00833178,0.0424453,0.00051203,-0.0433654,0.04816249,0.03513402,0.00439727,0.02039794,0.00741987,-0.04728233,-0.02069226,0.02822296,-0.03448511,0.03142177,0.03007706,0.04720547,0.06871982,-0.00797007,0.03900308,0.00650844,-0.05614585,-0.11626995,-0.01206225,-0.00246391,0.02047951,0.02447421,0.00658527,0.00577299,0.04560206,-0.00689036,-0.04999847,0.02321121,-0.1023563,-0.04628945,0.10040991,-0.01336798,-0.01794315,-0.00300356,-0.04433256,0.00845519,0.01839324,-0.05395214,-0.05742101,0.00646912,0.03686756,0.05443772,0.04822331,-0.08149758,-0.04475425,-0.01130425,-0.00610778,-0.0145188,0.09419134,-0.0197781,-0.03829665,0.00090396,0.01663977,-0.05002969,-0.03016875,-0.03759614,0.00172076,-0.04672605,-0.00327401,0.08610388,0.00130054,-0.06992517,-0.02832785,-0.05815863,-0.00558691,-0.01663687,0.01526799,-0.03008403,0.00061694,-0.02683967,-0.06193348,0.05781979,0.0079284,0.04121843,0.05546315,-0.05256704,0.00144768,-0.02566505,0.00556295,0.01120046,-0.00944752,-0.00018818,-0.0116613,0.00587176,-0.05290863,0.02738115,0.02690778,0.02560385,0.02799727,-0.03484083,0.0660977,-0.00347012,-0.05306797,0.11127657,0.03408071,-0.0620466,-0.02913807,0.04189925,0.08341752,0.0002033,0.01767482,0.01325884,-0.01575158,0.0124744,0.01047979,0.02160587,0.05285514,-0.06777778,-0.2181226,0.00326794,-0.03237743,0.0268982,0.03755008,-0.03417864,0.00932065,-0.03476351,0.02734261,0.09935331,0.08812758,-0.06244057,-0.01019063,0.07636836,-0.01422842,0.01105521,-0.07496212,-0.02800447,-0.05356217,0.02245028,0.00890758,0.02414652,0.0055429,-0.05179162,0.05582567,-0.04611014,0.14837186,-0.00281293,-0.00529929,0.00953872,0.06961552,0.00429543,-0.01275016,0.02370607,-0.02646963,0.04729801,-0.07294204,-0.00962922,-0.01007979,0.02285853,-0.04671833,0.03526847,-0.02599205,-0.12099422,-0.03969064,0.04599601,-0.0085421,0.10327131,-0.01831431,0.0481464,0.02814561,-0.00965706,0.00920568,0.01690929,0.12246673,-0.08684689,-0.08756226,-0.01084725,0.01156753,0.02602804,-0.03385244,-0.06072743,0.04483341,-0.02933432,0.03246214,0.02985466,0.00857308,0.01194975,0.0088914,0.02280658,-0.01976564,0.05280896,-0.05075129,0.02175811,-0.07211869,0.06878468,0.03402776,0.01385727,-0.0132688,-0.04175506,0.06174766,-0.02427341,0.06214281,0.06555973,0.04040918,0.05449574,0.03060532,0.0366884,0.00333689,-0.00626335,-0.02710027,0.00459762,-0.03883044,0.03398468,-0.0093778,-0.04704592,-0.2296901,0.01849612,-0.0246501,0.0604777,-0.03352403,-0.0131327,0.04153331,0.03651389,-0.0078272,-0.05497663,0.00655316,0.08388669,0.05347208,-0.02684107,-0.03268308,-0.02761887,-0.02950321,-0.02455699,0.08952981,-0.00011071,0.08988696,0.06061073,0.22705053,-0.02628791,-0.00662577,0.00344498,-0.01631528,0.00155531,-0.08530419,0.01623347,-0.00806149,-0.01114863,0.15365584,0.01632706,-0.01388832,0.00411096,-0.08806509,0.01304714,0.04159114,0.01183176,-0.05297398,-0.00603634,-0.01089933,-0.03381692,0.16977391,0.02999645,-0.05089569,-0.06399412,0.02690668,0.052378,-0.07792295,-0.01479582,-0.01247791,-0.03158234,0.01633371,0.02357027,0.02822084,0.00905826,0.01137341,-0.0022296,0.02336116,0.03285399,0.03867749,-0.00048995,-0.00085693],"last_embed":{"hash":"1c3iwrm","tokens":497}}},"text":null,"length":0,"last_read":{"hash":"1c3iwrm","at":1753423523361},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>","lines":[184,249],"size":11336,"outlinks":[{"title":"_**UK 6-59 Lotto: Statistics, Lottery Strategy, Hottest-24 Numbers**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/_4vQk00mxzI","line":61},{"title":"_**Results Draw #1, World Lottery Championship**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/E-aM_zUsQFY","line":62},{"title":"_**Filters, Restrictions, Reduction in Lotto, Lottery Software**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/f_I6udi8raQ","line":63},{"title":"_**Lottery Software, Strategies for Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/0P7xKzncan8","line":64}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0672325,-0.04976886,0.00425938,-0.08072744,-0.01415681,0.06092913,-0.0016904,0.01664839,0.01751932,-0.01365484,0.01773101,-0.00572529,0.07781282,-0.00110612,-0.01219554,-0.02718044,0.02243346,-0.018888,-0.11305737,-0.0036276,0.1127348,-0.05413093,-0.0495769,-0.07825424,0.0631658,-0.01411756,-0.03083478,-0.04678835,-0.03018725,-0.23219547,0.04559169,-0.01760269,0.02506062,-0.04815089,-0.07532346,-0.04745054,-0.01828865,0.0303298,-0.08643905,0.04372423,0.03368814,0.014551,0.04793961,-0.03065931,-0.02599672,-0.00723964,0.01815442,-0.07071523,-0.00255949,0.00371828,-0.0370724,0.01905162,0.02636616,0.03739254,0.01664545,0.0002516,0.02056123,0.11119153,0.03268262,0.030852,0.02723932,0.04405125,-0.18601799,0.05053761,-0.02692439,0.01727673,-0.00998325,-0.05092146,-0.00475857,0.0574551,0.03453911,0.06090858,0.0051191,0.03224572,0.00755608,-0.0645601,-0.06540275,-0.06945287,-0.01510851,-0.02234864,-0.05351878,-0.02293278,0.02803634,-0.03188834,0.00959506,0.01363853,0.02513672,0.01845044,0.03302009,-0.05284835,0.05436076,-0.00625635,0.0235702,0.06016359,0.01041259,0.0423029,-0.00039437,0.00194332,-0.01921191,0.12194447,0.02171075,-0.00542764,-0.06392259,-0.01176583,0.06516906,-0.03162463,-0.03876676,-0.00551291,0.00785781,0.02057669,0.00912102,-0.0051721,0.04189626,0.00145487,-0.04540706,0.04729832,0.03408797,0.00471771,0.01925027,0.00905257,-0.04957778,-0.02084969,0.02626196,-0.03475021,0.02687142,0.03033825,0.048399,0.068447,-0.00703953,0.03662857,0.00714987,-0.05757938,-0.11845531,-0.01036674,-0.00021776,0.01897581,0.02319657,0.00544164,0.00233275,0.04533239,-0.00393099,-0.04892296,0.02213476,-0.10036836,-0.04816244,0.09990157,-0.01314002,-0.01699655,-0.00134187,-0.04112601,0.0089615,0.01827918,-0.05329357,-0.05753987,0.00616203,0.037993,0.05570546,0.04975051,-0.08125049,-0.0448767,-0.01130134,-0.00240747,-0.01544162,0.09171436,-0.0201204,-0.03874349,-0.00046127,0.01938506,-0.0486846,-0.03095011,-0.03900941,0.00164351,-0.04874271,-0.00128001,0.08540427,-0.00027198,-0.07216532,-0.02884383,-0.05723441,-0.00954638,-0.01595975,0.01443123,-0.02722181,0.0014794,-0.02595108,-0.06509021,0.05688143,0.00953129,0.04024373,0.05716877,-0.05243082,-0.00003553,-0.02564292,0.00568411,0.01071377,-0.00926888,-0.00157818,-0.01260807,0.00509037,-0.05791439,0.02688215,0.02446419,0.02451927,0.02941881,-0.03359232,0.0667816,-0.00524593,-0.05320084,0.1133092,0.03306709,-0.06161547,-0.02903928,0.04128093,0.08478798,0.00186148,0.01564148,0.0147763,-0.01172776,0.00976367,0.01012788,0.01997369,0.05162846,-0.06838597,-0.21881995,-0.00096243,-0.03088734,0.02861007,0.03862971,-0.03511171,0.00496041,-0.03270156,0.02680235,0.09856995,0.08756129,-0.05931627,-0.00982413,0.07596879,-0.00975931,0.01238422,-0.07466493,-0.0247437,-0.05307285,0.02162228,0.00920924,0.02215968,0.00324545,-0.05244923,0.05549186,-0.04819499,0.14731188,-0.00451835,-0.00402707,0.01203967,0.06904937,0.00486624,-0.01147712,0.02175437,-0.02705316,0.05210252,-0.07346508,-0.00650909,-0.01002998,0.02283748,-0.04979996,0.0360786,-0.02518314,-0.12159359,-0.0372921,0.04705292,-0.00772034,0.10191747,-0.01880392,0.0465676,0.03119572,-0.00992604,0.00938477,0.01945915,0.1236655,-0.08909293,-0.087773,-0.01042841,0.01224181,0.02635408,-0.03408955,-0.0625926,0.04589469,-0.03005425,0.03334908,0.03289643,0.01104949,0.01033501,0.01155478,0.02351962,-0.02294239,0.05113778,-0.05572378,0.02201067,-0.07312739,0.06841321,0.03415669,0.01161052,-0.01705432,-0.04408962,0.06313699,-0.02084959,0.06023317,0.06522709,0.04080302,0.0519934,0.03003223,0.03628349,0.00375621,-0.00501649,-0.02708337,0.00475558,-0.04076424,0.03447837,-0.01069204,-0.0437434,-0.23122257,0.01777164,-0.02433197,0.06082419,-0.03475007,-0.01646024,0.03766187,0.04058363,-0.00669103,-0.06080049,0.00402504,0.08318683,0.05100251,-0.0286624,-0.03421959,-0.02364557,-0.02921953,-0.02296017,0.08722454,0.00138836,0.08935102,0.06202296,0.22801758,-0.02470749,-0.00503159,0.00023184,-0.01676188,0.00283758,-0.08397343,0.01665988,-0.00745806,-0.01295069,0.15245891,0.01624378,-0.0104013,0.00328733,-0.0869762,0.01309614,0.04067852,0.01217854,-0.05175245,-0.0070862,-0.01125407,-0.03421235,0.16905472,0.03428754,-0.05136287,-0.06410245,0.02864903,0.05718214,-0.07641509,-0.01226813,-0.01193905,-0.03182118,0.0148548,0.02254187,0.02679942,0.0082677,0.01575694,-0.00050702,0.02268531,0.03042638,0.03928361,0.00008495,-0.0023693],"last_embed":{"hash":"y6twpg","tokens":480}}},"text":null,"length":0,"last_read":{"hash":"y6twpg","at":1753423523625},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{1}","lines":[186,197],"size":1404,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04501644,-0.03271876,-0.02142157,-0.03590256,-0.05895647,0.05476267,-0.00140598,0.00221818,0.03551455,-0.01964124,-0.00103715,-0.0144208,0.10700639,-0.01641725,-0.00040998,-0.03057781,-0.02205217,-0.00744077,-0.11977615,-0.01053596,0.08765496,-0.05844712,-0.06655885,-0.05140575,0.06426461,-0.00353421,0.00193942,-0.09819762,-0.03566894,-0.23693924,0.04824474,0.03669553,0.02980569,-0.07404273,-0.05274472,-0.03507702,-0.03520434,0.07786156,-0.07921214,0.03782291,0.01744694,0.01554592,0.03923419,-0.03703035,-0.00746439,0.00171184,-0.0253021,-0.03482265,0.03633993,-0.00724542,-0.04000664,-0.01776009,0.01055545,0.04214299,0.02977263,0.02516671,0.0341676,0.11560124,0.0106571,0.05065198,0.04286164,0.05651115,-0.17241459,0.04666676,-0.02166245,0.01019008,-0.02208849,-0.02803954,-0.0089072,0.11198442,0.03238772,0.05068486,-0.0178288,0.03195072,-0.00556266,-0.04506797,-0.07956813,-0.06629761,-0.02737349,-0.00679305,-0.07062771,-0.00780411,0.01891012,-0.01293036,0.02158755,0.01349723,0.04638821,0.01259256,0.02037878,-0.04993863,0.03214817,0.01891656,0.01540321,0.04948786,0.00703412,-0.00988726,0.03155788,-0.01029865,0.01172193,0.11911963,0.010744,0.03149938,0.00443361,-0.02786815,0.02828036,-0.0673539,-0.03651555,-0.04979216,0.00526016,0.02207001,0.03195709,0.00429573,0.07621288,-0.03294376,-0.03126808,0.07533007,0.04108137,0.03367228,0.03122826,0.02444492,-0.03245302,-0.01417377,0.01893328,-0.02686733,0.02245651,0.01489083,0.02664551,0.09136815,0.02604721,0.03684363,0.03258491,0.00024064,-0.10691585,-0.03453157,-0.02318087,0.01380947,0.01800991,0.00344797,-0.00229367,0.00823506,-0.01684544,-0.06421353,0.04951982,-0.09001996,0.01825761,0.10938261,-0.00550507,-0.0011666,-0.00630995,-0.03815333,0.0069359,0.02046179,-0.06687374,-0.06111712,0.00899778,0.01955107,0.07136395,0.02888024,-0.06255759,0.03042093,-0.01377354,-0.0337686,-0.0366898,0.07249683,-0.01897922,-0.05500288,-0.00936471,0.00620376,-0.06959109,-0.04661854,-0.02636859,0.02561068,-0.03868261,0.00579526,0.08525203,0.01056652,-0.09925811,-0.02414442,-0.02758009,-0.00106655,-0.01201028,0.0163615,-0.0521875,0.00769632,-0.03238287,-0.04361523,0.04754328,0.01306229,0.02190752,0.07037783,-0.05359844,-0.01263896,-0.02148529,0.03302692,-0.00691328,-0.00882449,0.00747149,-0.04319312,0.0023276,-0.01310137,0.04342147,-0.03165625,0.0075934,0.02611074,-0.03291057,0.07626797,0.02439829,-0.05846424,0.09529841,0.05099197,-0.04018474,-0.04667183,0.02563214,0.05403471,0.03153159,0.00289784,0.0208419,0.00075873,0.01012073,0.0056689,0.0332849,-0.00014909,-0.0309586,-0.17943002,-0.02223177,0.01260957,0.02582136,0.03844031,-0.02412458,-0.02335505,-0.01983386,0.01285499,0.11725474,0.06220592,-0.05763929,-0.01647728,0.07905712,-0.03002785,0.00858151,-0.07467106,-0.0091495,-0.05848568,0.04260662,0.00611652,0.01620989,0.01766778,-0.04525,0.04827364,-0.0166378,0.13694669,0.00610457,-0.05810265,0.01438934,0.05262245,0.03944411,-0.04004671,0.06242894,0.00471672,0.0571397,-0.05656524,-0.04339186,-0.00226101,0.01926867,-0.06885262,0.01534488,-0.03066071,-0.11843818,-0.0391255,0.05422252,-0.0533632,0.09310187,0.00162347,0.04448213,0.05098175,0.00056066,0.02034217,0.00323462,0.09380321,-0.04953175,-0.10774826,-0.00626734,-0.00668404,0.03177426,-0.03163019,-0.06822497,0.03448719,-0.02837185,0.03137339,0.04497815,-0.01657981,-0.02010623,-0.02334832,0.00431706,-0.00889966,0.03460129,-0.0327741,0.00337312,-0.07232265,0.03267013,0.01426474,0.00331681,-0.00559434,-0.04273124,0.07447129,-0.0743404,0.01066393,0.05517157,0.01747831,0.03353385,0.05170141,0.05226024,0.00473642,-0.05320651,-0.02930415,0.0249761,-0.00290047,0.04951544,-0.00029584,-0.0469646,-0.25947657,0.02236489,-0.03034877,0.06336214,-0.02922813,-0.00471036,0.05526233,0.05540712,0.01781519,-0.07179613,0.01728628,0.07839826,0.03710366,-0.05526559,0.00509836,-0.01115056,-0.04306707,-0.07322741,0.04658388,0.01221765,0.11146189,0.03507005,0.22632033,-0.01429844,0.00785285,0.00398894,-0.00444278,0.02916035,-0.06825708,0.05224617,-0.02281735,-0.02251332,0.12292735,-0.02975335,-0.01659907,-0.00456534,-0.04541513,0.01306832,0.02725766,0.03451874,-0.08370681,-0.0232425,-0.03216565,-0.05301126,0.16635074,0.02904743,-0.06151349,-0.06241649,0.03901632,0.05314974,-0.05973112,-0.01575191,-0.00178396,-0.04548061,0.01568774,0.0437382,0.01978793,0.02995832,0.01040473,0.02950069,0.01575802,-0.00677401,0.0206352,0.02726184,-0.00061636],"last_embed":{"hash":"1a8cndy","tokens":470}}},"text":null,"length":0,"last_read":{"hash":"1a8cndy","at":1753423524203},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{5}","lines":[202,211],"size":2960,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07893422,-0.04220917,-0.01071043,-0.01766218,-0.04465942,0.05178312,-0.02350858,-0.00009621,0.00025946,-0.0289868,-0.00140063,0.01545355,0.11285764,0.01135616,-0.01292208,-0.02683672,-0.01995258,-0.00861649,-0.09735499,-0.04079249,0.08986134,-0.0333445,-0.06844667,-0.03833643,0.07112953,0.02736621,-0.01966467,-0.07950633,-0.03156599,-0.2577967,0.03328357,0.03083181,0.01867671,-0.07925654,-0.04316396,0.00105782,-0.03817838,0.06949325,-0.10303728,0.04624701,-0.00122311,0.00278913,0.01688063,-0.04655434,-0.0129537,0.00685968,-0.0139374,-0.00097824,-0.00458422,-0.01470411,-0.00192881,0.00947669,-0.00366722,0.06111224,0.00885732,0.02936119,0.00906155,0.10621477,0.02097136,0.0279823,0.04846251,0.04491198,-0.18578038,0.06806596,-0.01997025,0.01228859,0.00149189,-0.03452557,-0.00139848,0.09886883,0.04735034,0.04276067,-0.01042188,0.06566039,0.01787051,-0.03854279,-0.05227569,-0.09015543,-0.04269262,0.02684575,-0.0307119,-0.00712093,0.00631884,-0.01044665,-0.001361,-0.0052209,0.04412252,-0.00232849,0.00934393,-0.05379132,0.04252674,0.01846415,-0.00947851,0.0539031,0.01113801,-0.03128317,0.02071475,-0.00724985,0.00588395,0.12139177,0.0094027,0.04461991,-0.01242033,0.01749322,0.02048687,-0.03881895,-0.00182483,-0.03808445,-0.00194215,0.03831386,0.00445053,0.01879717,0.04861699,-0.01832507,-0.05142111,0.02527354,0.03575953,0.02563755,0.00495815,-0.00751359,-0.03951494,-0.01906669,0.0004035,-0.03358167,0.02558491,0.01883717,0.00141135,0.0950657,0.02973969,0.00937312,0.03354059,-0.03662382,-0.11075161,-0.05067158,-0.0185842,0.03052252,-0.00632399,-0.00980196,0.02385113,0.02381742,-0.00955328,-0.04301991,0.03499934,-0.07816878,0.01885756,0.12164018,-0.03405566,-0.01281816,-0.00383163,0.004514,-0.02334367,-0.01104799,-0.06645964,-0.06074275,-0.00034553,0.01105752,0.06222173,0.03332065,-0.05372937,-0.0158869,0.00406387,-0.00155553,-0.04120442,0.1337513,-0.05320861,-0.04574386,-0.0209258,0.03691723,-0.0124209,-0.05122283,-0.02654,0.01456407,-0.02308971,0.04226381,0.06753279,0.00034483,-0.08165152,-0.02621129,-0.04995616,-0.03635412,0.00867033,-0.01870144,-0.03893448,0.02282496,-0.00910312,-0.05232001,0.01772131,-0.00028297,0.05072611,0.05357283,-0.0604983,-0.00950813,-0.03056395,0.02400642,-0.02296104,-0.01112686,-0.02458639,-0.03781708,0.01189458,-0.00813947,-0.00317319,-0.01528265,0.02862494,0.02726858,-0.05252928,0.09416185,0.01069503,-0.05676029,0.1049247,0.02081198,-0.0708949,-0.02354812,0.03513577,0.07091033,0.02193753,0.00112884,-0.0022266,-0.00270621,0.007679,-0.00075595,0.0458042,0.03498869,-0.05421427,-0.20507812,-0.03267163,0.00035862,0.00984159,0.01209299,-0.01938252,-0.00398383,0.00108724,0.01923637,0.11517239,0.04061248,-0.01636411,-0.00952747,0.07485981,-0.02591545,0.01603014,-0.06765716,-0.02731917,-0.05960881,0.02666721,-0.02289426,0.01383494,-0.01838596,-0.0564325,0.05163211,-0.01515825,0.1405692,-0.002827,-0.01722602,0.01885942,0.04760313,0.03933337,-0.02640257,0.03488232,0.02265498,0.06097598,-0.08381488,-0.0445849,0.01933864,-0.00382722,-0.02527403,0.04543395,-0.0381993,-0.12743296,-0.04117849,0.06327417,-0.04967025,0.085521,0.02483538,0.05217754,0.04024512,-0.04260656,0.04244034,0.04617777,0.08135234,-0.04762286,-0.10901815,-0.01212406,0.0003062,0.034958,-0.00379448,-0.02873244,0.035807,-0.01717015,0.04021605,0.03190455,-0.03519185,-0.02055579,0.00341334,0.04422104,-0.01696417,0.07273255,-0.06326475,0.00683865,-0.06252892,0.04541273,-0.00541023,-0.00213839,-0.00068567,-0.04357515,0.08992516,-0.05498943,0.04276808,0.04913684,0.05849029,0.01579254,0.00630268,0.06449732,-0.00207275,-0.02409692,-0.02954449,0.04965827,-0.05100374,0.04458103,0.01418725,-0.05561211,-0.2438812,0.06577596,-0.03609694,0.03388221,-0.01594504,-0.01959579,0.0311986,0.04092111,-0.00165817,-0.04852014,0.03639466,0.08697991,0.02073277,-0.035688,-0.01741539,0.01735084,-0.00470877,-0.07862847,0.07640754,-0.01238756,0.07935211,0.0515739,0.24297555,0.0126445,-0.00432667,0.02146947,0.00455417,0.02007493,-0.067598,0.02612037,-0.03048045,-0.0268543,0.13271324,-0.00990017,-0.02608269,0.00108168,0.00122451,0.03435371,0.02833076,0.01391193,-0.09148895,-0.01976913,-0.0422267,-0.03528723,0.14865062,0.03627196,-0.06156192,-0.0452177,0.03229801,0.04906893,-0.0575307,-0.01523601,-0.0338365,-0.03826219,0.01844412,0.03077912,0.00166457,0.0127492,-0.00324521,0.02936626,-0.00009152,0.03175849,0.04738178,0.02696524,-0.01868851],"last_embed":{"hash":"ip5s2w","tokens":171}}},"text":null,"length":0,"last_read":{"hash":"ip5s2w","at":1753423524438},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{6}","lines":[212,213],"size":413,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06211728,-0.05199538,-0.01975154,-0.03909201,-0.05123229,0.06146719,-0.00043523,0.01266806,0.02975306,-0.01088302,-0.00732741,-0.01141966,0.08250356,-0.02759603,0.00838085,-0.03022079,-0.03473761,-0.02590015,-0.11062652,-0.01317631,0.1228919,-0.04229732,-0.07389887,-0.07396504,0.06850685,0.00010232,-0.0143107,-0.08375311,-0.02510707,-0.23140608,0.03665116,0.03293887,0.01332579,-0.07749458,-0.04763838,-0.03660192,-0.0451797,0.09367841,-0.06869096,0.04323748,0.00954089,0.00611359,0.01759357,-0.04366026,-0.01317105,-0.00479343,-0.04003253,-0.02180981,0.04741454,-0.00140546,-0.03753347,-0.00646131,0.03044313,0.03325322,0.04171916,-0.0032376,0.02481124,0.11846875,0.01836219,0.03769261,0.03903059,0.0642109,-0.18547744,0.04460952,-0.03879149,0.03180667,-0.03621831,-0.0328843,0.02133046,0.1094419,0.0453142,0.04699145,-0.0190411,0.03386201,-0.0081234,-0.02871865,-0.07396267,-0.05776084,-0.03936826,-0.00068924,-0.05628469,-0.01528134,0.01462799,-0.01931721,0.02457,0.02828094,0.0321339,0.01969748,0.0295766,-0.04732076,0.04093505,0.0249613,0.01343082,0.03664138,0.00365076,0.00462822,0.02463702,-0.00194033,0.00618125,0.11963597,0.03280056,-0.00483255,-0.0002605,-0.00303287,0.02004634,-0.04970791,-0.02968313,-0.0385059,-0.01853968,0.02528796,-0.01184878,0.017044,0.05806742,-0.01799844,-0.04353273,0.07698099,0.03905915,0.03886348,0.01147619,0.01958482,-0.04019298,-0.01318495,-0.00358195,-0.04743629,0.01625718,0.03502924,0.02455005,0.09281266,0.00852809,0.03886307,0.03506875,0.01150076,-0.11914238,-0.02707839,-0.02126846,0.00162062,0.03534392,0.0131295,0.00533765,0.0060266,-0.01256145,-0.03181114,0.05262334,-0.07219101,0.01748281,0.10139345,0.00118823,0.00284751,0.00591412,-0.02958376,0.02032374,0.02602699,-0.03752393,-0.06766906,0.01031261,0.02697776,0.07269502,0.04919677,-0.07168829,0.01179046,0.01583115,-0.04631013,-0.02435975,0.077852,-0.02335412,-0.05002554,-0.00542032,0.01489948,-0.04847646,-0.05150651,-0.02979145,0.00996862,-0.03882107,0.01216544,0.09113654,-0.00119796,-0.10230421,-0.01375655,-0.01902114,-0.00000736,-0.00124342,-0.01250672,-0.0455651,0.01693932,-0.04052297,-0.06473313,0.05493747,-0.00553583,0.03426689,0.0771952,-0.05238499,0.00261081,-0.02263235,0.04992992,-0.01858757,-0.00786626,-0.00000326,-0.01338198,0.01320829,-0.01202732,0.05077637,-0.02861442,-0.00204291,0.01001078,-0.03391571,0.0714867,0.01917344,-0.0543387,0.11355761,0.04390563,-0.05176966,-0.02950613,0.04360023,0.06444957,0.03985782,0.00053943,-0.00112075,-0.00341619,0.01478013,0.00346645,0.02908305,-0.02644857,-0.04401176,-0.19580726,-0.02736724,0.01992236,0.00223713,0.04078105,-0.01165802,-0.01216809,-0.01082331,0.02244592,0.11935389,0.05657646,-0.04610417,-0.01378737,0.0622192,-0.0215808,0.00746682,-0.10178646,-0.00150034,-0.07358915,0.05746508,-0.00021331,-0.00923474,0.00998588,-0.03161876,0.03381176,-0.0292268,0.16039498,-0.00533073,-0.05396475,0.00213442,0.04861911,0.02061254,-0.03438368,0.04671409,0.00824508,0.04415264,-0.08697295,-0.02165816,-0.00996343,0.01077695,-0.05535164,0.0285309,-0.03504936,-0.11005931,-0.02628593,0.06241953,-0.05081283,0.07980371,0.00863959,0.04694356,0.05053759,-0.00707919,0.00634293,-0.00805607,0.09019526,-0.0461317,-0.11108954,0.00338789,-0.02542098,0.04212702,-0.03371801,-0.07473249,0.03544766,-0.02408868,0.02807949,0.04602224,0.00585397,-0.02881271,-0.01271644,0.0050224,-0.00338537,0.04114293,-0.01539972,-0.01040745,-0.0482118,0.03155994,0.03633768,0.00882606,0.00923364,-0.04883825,0.04870373,-0.08286165,0.01879271,0.05358896,0.0328402,0.02497679,0.03605262,0.02700749,-0.00580727,-0.05255049,-0.03021992,0.02537788,-0.01872645,0.02309174,-0.02165177,-0.04029398,-0.25132468,0.02949601,-0.030642,0.06736708,-0.03066249,-0.01022648,0.0434283,0.06249735,0.02418393,-0.08104951,0.01663926,0.09615748,0.01593881,-0.02426365,-0.01704327,-0.02037999,-0.02765665,-0.0702098,0.07277394,0.00632407,0.11238967,0.0398904,0.21585958,-0.00925766,0.01238814,0.00371146,-0.00331832,0.01089646,-0.06612346,0.02628844,-0.02225645,-0.0262966,0.13472174,-0.01707528,-0.01128834,0.00525217,-0.04776714,0.02688339,0.02212756,0.02765552,-0.07194975,-0.03500098,-0.05663254,-0.02970587,0.16579691,0.03897942,-0.05780715,-0.04820727,0.03536421,0.06360586,-0.06398222,-0.01141551,-0.02949915,-0.03706653,0.01091905,0.04041716,0.01438729,0.03017353,-0.00198949,0.02651824,0.00898912,0.0024491,0.02651074,0.02433481,0.00309484],"last_embed":{"hash":"1ynfnpw","tokens":400}}},"text":null,"length":0,"last_read":{"hash":"1ynfnpw","at":1753423524525},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{7}","lines":[214,217],"size":1310,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05533257,-0.02650312,-0.01540798,-0.0692526,-0.05552856,0.057383,0.00049134,0.04161653,0.03372742,-0.00263984,-0.00423787,-0.00114018,0.09659978,-0.00937726,-0.00157958,-0.02069743,0.00315335,-0.02944439,-0.10979333,-0.01418928,0.12294546,-0.04921366,-0.0748959,-0.07137357,0.0989587,-0.03113837,-0.03080961,-0.06783308,-0.02985848,-0.2335833,0.06110958,-0.00345402,0.0266493,-0.05054055,-0.05744392,-0.02502342,-0.02346295,0.06878441,-0.05602095,0.02801699,0.00858967,0.02302198,0.01309591,-0.04334227,-0.02917779,-0.01977411,-0.01118476,-0.06489168,0.01601568,0.01719537,-0.03866174,0.01757862,0.01477964,0.03901827,0.00910221,0.00281965,0.03814391,0.11749525,0.00930576,0.04144014,0.03335286,0.04812774,-0.15175764,0.02318331,-0.02033477,0.02197503,-0.02122807,-0.04821234,-0.01059264,0.08599155,0.04898702,0.0437679,-0.01285661,0.0247131,0.03728593,-0.08511128,-0.06965008,-0.07767782,-0.01828182,-0.01946103,-0.04146373,-0.02647819,0.01698412,-0.01809638,0.01351191,-0.00045072,0.03812179,-0.0035227,0.0300726,-0.05798838,0.03903677,-0.01683041,-0.00567934,0.05408144,0.0160402,0.0177935,0.03774339,-0.00497547,-0.00730581,0.12204816,0.02756946,-0.00563368,-0.02748391,-0.02705196,0.03455131,-0.05239312,-0.02570703,-0.01975501,-0.01872045,0.02675939,0.02625572,-0.00649718,0.04840322,-0.00922386,-0.0464663,0.05605577,0.03421308,0.0071219,0.0176268,0.02458434,-0.04917832,-0.0116836,0.02768555,-0.03079157,0.01972228,0.01282276,0.04863521,0.09089477,0.01168885,0.0449975,0.02778625,-0.01163576,-0.106934,-0.01169914,-0.01130236,0.00696578,0.03244764,0.00691428,-0.0043883,0.03238646,-0.02592848,-0.0106463,0.0256175,-0.08597666,-0.01503219,0.11512369,-0.01453851,-0.01031955,0.00621229,-0.05818285,-0.01013158,0.01048737,-0.06780967,-0.06144127,-0.00179188,0.0321922,0.0894569,0.04469547,-0.06614935,-0.02363637,-0.02937328,-0.00702286,-0.03750479,0.08197317,-0.0220082,-0.04698081,-0.00111426,0.0063538,-0.06423069,-0.0136066,-0.0393757,0.03934655,-0.05013821,-0.00753811,0.10269382,-0.00602436,-0.12497653,-0.02483926,-0.0393043,-0.01373403,0.01249839,0.03091687,-0.06403477,0.01106568,-0.01401476,-0.0650039,0.04345461,0.00715593,0.02360047,0.07546535,-0.08606594,-0.00993711,-0.03394917,0.03047899,0.01081796,-0.0088528,0.01053945,0.00424032,0.01570849,-0.01958271,0.03148888,-0.02900304,0.03773553,0.04154514,-0.01780764,0.07303103,0.00222563,-0.06321795,0.11670548,0.02928101,-0.0439934,-0.02766157,0.02920716,0.07480805,0.01668727,-0.01302704,0.01624303,0.02166488,-0.00801106,0.00509688,0.03186549,0.01903098,-0.06426123,-0.19815333,-0.01579485,-0.00887721,0.03098141,0.04346143,-0.02565848,-0.00426631,-0.00242839,0.00465172,0.12618074,0.06170159,-0.07213987,-0.02873106,0.04633204,-0.02139274,0.02184944,-0.09608145,0.00532452,-0.04270897,0.03930693,0.02258032,0.02312051,0.03347439,-0.0372442,0.03821327,-0.02330388,0.13067043,-0.03604448,-0.02146139,0.00454297,0.04693675,-0.01969502,-0.03660464,0.04770435,-0.0094363,0.05649543,-0.06491562,0.00547195,-0.00712039,0.00289331,-0.03951612,0.01341963,-0.04299852,-0.12303387,-0.05165367,0.05024305,-0.0200397,0.09963399,0.01552407,0.03302575,0.03738628,-0.01379147,-0.00623882,0.0116317,0.09699072,-0.07537945,-0.11611954,-0.00466069,-0.02081396,0.04256156,-0.01914,-0.0797221,0.03188344,-0.02493749,0.05532709,0.01620596,0.02133045,-0.00792921,-0.01917079,0.01247389,-0.01669899,0.0568191,-0.05342839,0.00846179,-0.04416652,0.0510737,0.02254789,0.03427907,-0.01405319,-0.04953827,0.06920813,-0.04456961,0.0358819,0.05507012,0.04325224,0.03637096,0.03320976,0.01940795,-0.0151709,-0.00650423,-0.0175645,0.02521052,-0.03009833,0.04053438,-0.02184359,-0.04811261,-0.23594935,0.01201648,-0.0212629,0.04414159,-0.02154206,-0.0226643,0.0220345,0.0473449,0.03228623,-0.04463383,0.01098161,0.0614365,0.04497687,-0.03814419,-0.01118618,-0.01996678,-0.04938976,-0.02546957,0.07550073,0.01693937,0.09202287,0.04384302,0.23267031,-0.02080804,-0.00914839,-0.00976351,-0.02299542,0.01492565,-0.07059516,0.02735701,-0.00082571,-0.0168912,0.15467267,-0.00989372,-0.01003336,0.02284065,-0.0562163,-0.00137065,0.04883954,0.04021832,-0.04433813,0.00893048,-0.04602315,-0.0489772,0.16048054,0.03268328,-0.07056091,-0.04945761,0.06568182,0.05375975,-0.07823988,-0.02529384,0.00492596,-0.03272915,0.00723313,0.03261924,0.0310804,0.04380034,0.01743961,-0.00197482,0.01308897,0.03192028,0.02158413,0.02070837,-0.00477637],"last_embed":{"hash":"1wnf1f4","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"1wnf1f4","at":1753423524872},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{12}","lines":[224,241],"size":3200,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0992186,-0.02712861,-0.0270553,-0.00899058,-0.0001176,0.05292906,0.01163555,0.03685878,0.00532831,-0.03449416,-0.00666037,0.00226161,0.07497998,-0.00675026,-0.00042709,-0.04462808,0.00383882,-0.01374542,-0.04589287,-0.05270861,0.08007077,-0.03634971,-0.03688692,-0.04813821,0.06497736,-0.01727123,-0.00502414,-0.07598664,-0.04551814,-0.2178982,0.0106735,-0.00038083,-0.00045794,-0.06906157,-0.08015116,0.01278716,-0.01140573,0.00952599,-0.11064993,0.07183851,0.01022691,0.00301996,0.00588671,-0.02638307,0.00245133,-0.00798206,0.00360822,-0.03198572,0.0226417,-0.00512103,-0.05136151,0.03333236,0.00353948,0.0309469,0.03202766,0.00899226,0.00604002,0.10493641,0.02761628,0.01081753,0.02197373,0.02723765,-0.22073741,0.08252553,-0.05450326,0.00573401,-0.01561914,-0.01185483,0.00512729,0.04687261,0.05737031,0.04601204,-0.02973202,0.06165684,0.02796659,-0.03478735,-0.0379416,-0.05975111,-0.01792185,0.00194197,-0.00423555,-0.04009788,-0.0006533,-0.04784738,0.02791978,0.04001447,0.04109684,0.01280706,0.03644863,-0.08861454,0.03865725,0.01160506,0.05076736,0.06884815,0.00188028,-0.01122507,0.02687025,0.01597443,-0.00581924,0.13254395,0.01245229,0.02697957,-0.00300263,0.00993104,0.04900781,-0.03010843,0.01264715,-0.00580943,0.0031378,0.07607515,-0.00188919,0.01921579,0.05685764,-0.01265565,-0.04921638,0.02262104,0.02108663,0.03029137,0.00497054,0.00541184,-0.05143946,-0.01141318,0.01985214,-0.00226468,0.03492028,-0.00379727,0.00194055,0.0655019,0.02398639,0.06533941,0.03486344,-0.01243484,-0.14977959,-0.06047029,-0.00628474,0.01648371,-0.00328767,-0.05352684,-0.00062275,0.070996,-0.06258979,-0.03192685,0.02317894,-0.0786932,-0.04741899,0.07948685,0.00884407,-0.00640968,0.00070721,0.01616092,-0.02337084,0.02024275,-0.03797673,-0.04581744,0.02248163,-0.00336329,0.08202707,0.0657141,-0.07670451,-0.00906907,0.00464168,-0.02453551,-0.0447451,0.13894095,-0.0454961,-0.08051795,-0.04583527,0.04575066,-0.01539399,-0.04271484,-0.03864463,0.01462671,-0.02638907,0.01507409,0.07620654,-0.02294821,-0.06492577,-0.04274536,-0.07895069,-0.01243667,-0.0266319,-0.02885806,-0.02136487,0.00817421,-0.02229111,-0.08984935,0.05785369,-0.0258461,0.06793186,0.04229544,-0.05985669,0.00599047,-0.03133222,0.02387838,-0.01753073,0.0027179,-0.02220262,-0.03717117,0.01311615,-0.00641453,-0.02732548,-0.00061246,0.01696851,0.01495332,-0.05439672,0.09136387,-0.02385963,-0.03396317,0.08295296,0.031869,-0.05177769,0.01300817,0.04862658,0.07069664,-0.0001461,-0.00476737,-0.00326435,0.0038488,-0.00336692,0.01843135,0.03316935,0.05403119,-0.0968922,-0.21871491,-0.00157555,-0.05570985,0.00800719,0.02827214,-0.044126,-0.0137367,-0.02016493,0.0517515,0.09063648,0.06999901,-0.04367254,0.02503522,0.09623633,0.0042725,-0.00501019,-0.04729484,-0.03895323,-0.05723843,0.0459508,-0.00339339,-0.01066102,0.00745668,-0.03740484,0.04489339,0.00916117,0.12723146,0.0500628,-0.00064782,0.01111799,0.05477067,0.02181659,-0.02306443,-0.0664665,-0.00758388,0.07301518,-0.05025936,0.00689504,-0.00266539,-0.01436899,-0.03906595,0.03886066,-0.03340986,-0.11524527,-0.03689774,0.04353744,-0.02422172,0.06893123,-0.00319683,0.04611648,0.06731655,-0.01706371,0.02426228,0.02353744,0.06887902,-0.07548073,-0.09664861,-0.0000955,-0.03780585,0.03489492,-0.01075294,-0.01807762,0.04780205,-0.03558585,0.0609392,0.03815782,-0.02886987,-0.01717493,0.00405376,0.00841798,-0.02486654,0.09177446,-0.05362459,0.02318851,-0.04075691,0.03929663,0.00572331,-0.01774742,-0.0365661,-0.04080873,0.0361504,-0.04766499,0.05940367,0.06240538,0.04136081,0.04541862,0.01478912,0.03626487,-0.01963914,-0.02317725,-0.00279586,0.02340661,-0.04688023,0.02690836,0.03773684,-0.02529535,-0.24002066,0.04528878,0.00168109,0.01723207,0.00891469,0.00579218,0.01878467,-0.03180442,0.03934719,-0.04532801,0.04772449,0.077102,0.02111171,-0.02164132,-0.01309439,0.01645829,-0.01614462,-0.05777063,0.06839154,0.00248695,0.06217185,0.05346993,0.25503397,0.00543247,-0.03072915,0.02466728,0.00689438,-0.00986246,-0.08136164,0.05761998,0.00001332,-0.03568017,0.0921269,0.01833169,-0.01677345,0.01910598,-0.05364615,0.03736426,0.01590694,-0.00092999,-0.08787499,0.00796811,-0.03101728,-0.01405694,0.14108156,0.04938212,-0.06953941,-0.03612799,0.06102484,0.0683189,-0.07075259,-0.05140891,-0.03622795,-0.01890315,0.00250847,0.03146943,0.00318948,-0.00940611,0.01096419,0.01264309,0.02414432,0.04854248,0.01449477,0.01526863,0.03466507],"last_embed":{"hash":"t34iqe","tokens":177}}},"text":null,"length":0,"last_read":{"hash":"t34iqe","at":1753423525118},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{13}","lines":[242,242],"size":448,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{19}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10257436,-0.03209994,0.00547107,-0.03494786,-0.01564971,0.04498542,-0.00852929,0.0410254,-0.01387122,-0.01863014,-0.03027915,0.01305909,0.0955355,-0.03257326,0.00330092,-0.02312576,-0.0025443,0.01197033,-0.04690955,-0.0025035,0.10760422,-0.02906361,-0.06643936,-0.04175554,0.07724405,-0.01670007,-0.01536317,-0.06380637,-0.03274615,-0.19713666,0.03816468,-0.01789005,-0.00646687,-0.06203075,-0.04164417,-0.00379108,-0.01823499,0.06149282,-0.08045893,0.03311335,0.00388186,0.00028521,0.03724478,-0.0282251,0.00293959,-0.01239328,0.01155601,-0.01776126,0.01401332,-0.01609905,-0.04561356,0.0228605,-0.02129963,0.0254698,0.01697584,0.00148768,0.02924385,0.13357858,0.03075975,0.02943544,0.0089087,0.03931461,-0.21107838,0.08999167,-0.05060847,0.04041309,-0.0165874,-0.00406843,-0.00048577,0.08405795,0.01520455,0.03037197,-0.00642905,0.09469935,0.01127468,-0.0426699,-0.04909685,-0.09195588,-0.04116804,0.0195538,-0.03154852,-0.00359923,0.02026695,-0.00402996,-0.01850594,0.03880729,0.03167209,0.00383916,0.03906967,-0.06860898,0.04058148,-0.00342013,0.02107695,0.06607153,0.01915953,-0.0218069,0.00041975,0.01550358,0.00983141,0.14167067,-0.02038953,0.00150646,-0.01349268,0.00584781,0.0491207,-0.03929311,0.00276126,-0.02950794,-0.00955035,0.06337182,0.00683772,0.02447152,0.04160309,-0.01191177,-0.03398382,0.0392131,0.01586059,0.03452429,0.0088242,0.00952473,-0.05890166,0.00485342,-0.00083558,-0.02475793,0.0389945,-0.01566312,0.01616471,0.07422294,0.01140319,0.04229206,0.02270707,-0.00482495,-0.1497294,-0.05269585,-0.00696809,0.03186937,-0.0157414,-0.047104,0.00227202,0.06619254,-0.03018272,0.00599735,0.00836897,-0.06747804,-0.00528085,0.08817142,0.0026447,0.00809471,0.00779718,-0.02129145,0.00662702,0.02664054,-0.04068562,-0.05049592,-0.00229753,0.04012884,0.05491365,0.03031798,-0.07458272,-0.01946999,-0.00379362,-0.01136377,-0.03715383,0.1158286,-0.05204146,-0.05253534,-0.04569325,0.0461288,-0.01627179,-0.04131038,-0.03291635,0.00376953,-0.04438001,0.00426127,0.06974909,0.00538271,-0.05374286,-0.02236657,-0.06906516,-0.02903215,0.01645473,-0.02921372,-0.03347885,0.01062183,-0.03469935,-0.07697488,0.02426751,-0.01785007,0.06313562,0.09778447,-0.0532763,0.02938649,-0.0375446,-0.01569428,-0.04172556,-0.01791268,-0.01563079,-0.04320034,0.00659468,-0.03280672,-0.0308164,-0.00414647,0.00629091,0.03939368,-0.05456918,0.06267183,0.0352229,-0.07131107,0.12840183,0.02121939,-0.06237166,-0.00515836,0.06600837,0.08078972,-0.02154646,-0.00113862,-0.00473864,0.00952813,-0.01503495,-0.0018318,0.00709391,0.05256671,-0.06754333,-0.21269396,-0.0171666,-0.03610807,0.00904068,0.03967241,-0.02513208,-0.02735712,0.0000715,0.0353547,0.10504603,0.08305107,-0.05884954,0.01878616,0.07324068,-0.01232581,-0.01121734,-0.07731993,-0.03418376,-0.04918171,0.0205421,-0.01368653,-0.00538137,0.00685041,-0.04133253,0.06703153,-0.01014728,0.12778929,-0.00834561,0.01679718,0.02076796,0.06117005,0.00970054,-0.02780662,-0.04631666,-0.01444789,0.07604107,-0.08579677,-0.00152322,-0.00620114,-0.00537409,0.00039803,0.0385994,-0.04847525,-0.11649895,-0.05688285,0.05175836,-0.01508584,0.08808048,-0.01481644,0.04557773,0.05050652,-0.01016672,0.025889,0.02619199,0.05327071,-0.07498725,-0.0966379,0.00132145,-0.01167018,0.02911114,-0.0145255,-0.04451791,0.06263267,-0.03184582,0.04721784,-0.00343155,-0.00500913,-0.03167149,-0.00662986,0.02299185,-0.00979406,0.07014085,-0.06413008,0.02377584,-0.04711872,0.05113403,-0.00101132,-0.00453144,-0.04103643,-0.04931993,0.06297686,-0.03728548,0.06264737,0.07989357,0.05479437,0.05027637,0.01653831,0.02785515,-0.00030814,-0.0262484,-0.02682713,0.02528165,-0.03324356,0.03779469,-0.00174315,-0.0381018,-0.26957983,0.01545667,-0.01366497,0.06735408,-0.0121068,-0.01833989,0.0463088,0.03963172,0.00306256,-0.03531444,0.0206122,0.06360746,0.00859732,-0.03509694,-0.02433189,-0.0089768,0.01668008,-0.04236151,0.100392,-0.00287165,0.06272933,0.04069152,0.25552616,0.01044945,-0.01108979,0.02472449,0.02399785,0.00057843,-0.06592803,0.04460987,0.00625318,-0.00347479,0.10864462,0.02643286,-0.01600457,0.00333369,-0.04500647,0.0490548,0.03305943,-0.00712107,-0.07848442,0.00568477,-0.02351854,-0.02584855,0.13127331,0.01997058,-0.0686549,-0.03357782,0.02035725,0.06785242,-0.08144409,-0.01248953,-0.03762638,-0.03157021,0.01083197,0.03776541,0.01836069,0.00795505,-0.01253961,0.01771565,0.02206619,0.01822849,0.02363792,0.03399334,0.0015007],"last_embed":{"hash":"18ysvmu","tokens":118}}},"text":null,"length":0,"last_read":{"hash":"18ysvmu","at":1753423525220},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#<u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>#<u>Older writings - but still valid</u>#{19}","lines":[248,249],"size":214,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09561385,-0.01732142,-0.00978214,-0.01789099,-0.03251316,0.03404864,-0.03030541,0.03020993,0.02223836,-0.0308782,-0.00339676,-0.01562089,0.09116819,-0.0238392,0.00289861,-0.06927315,0.02506998,0.04021976,-0.05187619,-0.03913105,0.08699002,-0.02507155,-0.07534312,-0.05970059,0.04750033,-0.01421719,-0.01534375,-0.05010718,-0.02228245,-0.18840694,0.0407733,-0.00894281,0.00249493,-0.05241248,-0.07264862,0.00537728,-0.01559319,0.03772489,-0.10250182,0.04587018,0.02545281,0.00655662,0.03330977,-0.01208289,0.02791638,-0.03350348,0.02667743,0.0079138,0.04555134,-0.02959003,-0.08173782,0.00379963,0.01065251,0.01509336,0.04738531,0.00896071,0.03526172,0.12320357,0.01206155,0.03039274,0.01648772,0.06685212,-0.19279391,0.09167494,-0.01826245,0.04076132,0.00295161,-0.01528746,0.03406049,0.06379256,0.01078252,0.02903235,-0.02243217,0.07101565,0.00826971,-0.04879894,-0.03421254,-0.0659565,-0.03190395,-0.01717066,-0.03363802,-0.03180921,0.01965351,-0.00893626,0.01982267,0.05477735,0.05143562,0.01804113,0.07420048,-0.09049291,0.01489506,-0.00006916,0.0474615,0.06442775,-0.00647728,-0.003772,0.03460328,0.00336748,0.00830806,0.14060055,0.01123588,0.01599515,0.00566106,-0.01406316,0.05752528,-0.07212945,0.01234302,-0.03622596,-0.01710051,0.07017338,0.04487781,-0.00141097,0.04107618,-0.02344635,-0.03244852,0.00067602,-0.00202915,0.02642136,0.00640779,0.01870265,-0.06496683,-0.01871219,0.02321803,0.00456671,0.031022,0.00058138,0.00359258,0.06800178,0.00362977,0.05238492,0.04057775,0.02265233,-0.1604809,-0.05158031,0.01019653,-0.00451612,-0.00861862,-0.02134779,-0.04245945,0.05765649,-0.04924992,-0.03515229,0.02535106,-0.0989157,-0.04057323,0.0768474,-0.01647196,0.0003581,-0.02102766,-0.02723406,0.01140578,0.01653852,-0.02502487,-0.05079719,0.01156214,0.02792838,0.09141328,0.0474664,-0.08050124,0.0171552,-0.01619519,-0.01444947,-0.03571412,0.14788225,-0.02296408,-0.09333169,-0.03990063,0.06954452,-0.0194744,-0.06013655,-0.05885317,0.0047434,-0.07694378,0.0056272,0.10206863,-0.01103665,-0.07073696,-0.03638533,-0.04133997,-0.04491819,0.01298493,-0.02676759,-0.02406601,-0.00555208,-0.00907234,-0.06448921,0.02828235,-0.01506961,0.03489099,0.05007474,-0.05612941,-0.01959428,-0.01632625,-0.01779379,-0.03183046,0.00533463,-0.04319714,-0.05099805,0.04749214,0.00829474,0.02757449,0.01604811,0.01034752,0.04706029,-0.02500596,0.07700805,-0.03150004,-0.04771562,0.08210118,0.0327815,-0.06000572,0.01540366,0.07359685,0.06958865,-0.0048081,0.01768776,0.01649778,0.0119201,-0.03063271,-0.00286077,0.01019497,0.02699027,-0.0946785,-0.18557097,-0.01527199,-0.04376298,0.01493012,0.00665447,-0.03311932,-0.01499078,-0.04449626,0.02794629,0.10819181,0.1044487,-0.058019,0.00782246,0.07294532,-0.00220252,-0.00219936,-0.0565909,-0.0262776,-0.07725731,0.00584069,0.03471684,-0.00185604,0.01777432,-0.08316381,0.0151252,-0.02091605,0.10938897,-0.00499286,0.02726126,0.03186371,0.0828867,0.00968284,-0.02828266,-0.02220118,-0.02624284,0.05380096,-0.06907396,-0.0168309,0.01038959,-0.00026027,-0.04040338,0.03269436,-0.0096888,-0.1164899,-0.01930968,0.02784188,0.00247497,0.04375321,0.00442771,0.05831113,0.03124141,0.01678011,0.01400357,0.01489941,0.06326079,-0.0392124,-0.08293342,-0.02584362,-0.03463846,0.01803837,-0.037103,-0.08526434,0.03302374,-0.03989969,0.05833787,0.02665157,-0.02414799,-0.01165769,-0.01280295,0.01883064,-0.01704421,0.07405444,-0.0068527,0.05163448,-0.0587712,0.0567514,0.04307221,-0.00333477,-0.0344962,-0.03799743,-0.02090864,-0.02801274,0.04715579,0.09328031,0.05478505,0.05915918,0.05303503,-0.01119103,-0.01907797,-0.0123831,-0.00687678,-0.00044533,-0.03453679,0.03293701,0.02893187,0.01772536,-0.26358089,0.04021112,-0.00437919,0.0310545,-0.02515249,-0.00132719,0.02365099,0.00376715,0.05217093,-0.06450818,0.03154654,0.03659496,0.00484708,-0.03008092,-0.03145412,0.01073403,0.02181411,-0.03814482,0.08877087,0.04325133,0.03992915,0.03956105,0.24206726,0.00659127,0.00951655,0.01982335,-0.02012853,-0.00707345,-0.04594826,0.05181587,0.01542023,0.0031505,0.10023792,0.01210455,-0.03515685,0.01953099,-0.05349297,0.0339805,0.02876786,0.01602726,-0.07226403,0.00646118,-0.02359505,-0.00398663,0.13265353,0.02700957,-0.04581615,-0.08882451,0.04949475,0.07905221,-0.07293648,-0.03748032,-0.06483213,-0.01715644,0.02724069,0.03307413,0.01558543,-0.01081983,0.00725618,-0.0155898,0.04573088,-0.02594151,0.01588751,0.01085381,0.00475836],"last_embed":{"hash":"zh43kr","tokens":380}}},"text":null,"length":0,"last_read":{"hash":"zh43kr","at":1753423525339},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)","lines":[250,292],"size":4825,"outlinks":[{"title":"<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, eBook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":8},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":10},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":14},{"title":"_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge","target":"https://saliu.com/bbs/messages/623.html","line":15},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":16},{"title":"_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_","target":"https://saliu.com/bbs/messages/569.html","line":17},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":18},{"title":"**Vertical or Positional** _**Filters in Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":19},{"title":"_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_","target":"https://saliu.com/bbs/messages/896.html","line":20},{"title":"**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_","target":"https://saliu.com/bbs/messages/919.html","line":21},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":22},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":23},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":24},{"title":"**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_","target":"https://saliu.com/decades.html","line":25},{"title":"_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":26},{"title":"_**Software to Generate Lotto Combinations with _Favorite Lottery Numbers in Fixed Positions_**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":27},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":28},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":29},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":30},{"title":"_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_","target":"https://saliu.com/STR30.htm","line":31},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":32},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":33},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":34},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":35},{"title":"**Lottery Software, Lotto Programs**","target":"https://saliu.com/infodown.html","line":36},{"title":"Ion Saliu lottery software, lotto software is best to win with mathematics of filtering, filters.","target":"https://saliu.com/HLINE.gif","line":38},{"title":"Forums","target":"https://forums.saliu.com/","line":40},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":40},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":40},{"title":"Contents","target":"https://saliu.com/content/index.html","line":40},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":40},{"title":"Home","target":"https://saliu.com/index.htm","line":40},{"title":"Search","target":"https://saliu.com/Search.htm","line":40},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":40},{"title":"Effective lottery software must apply filters or restrictions to the huge amounts of combinations.","target":"https://saliu.com/HLINE.gif","line":42}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08441589,-0.03868501,-0.00563399,-0.00689536,-0.03207901,0.02472884,0.01022671,0.0243778,0.03045807,-0.02061202,0.00010292,-0.01215492,0.08869751,-0.01725726,0.0064402,-0.05357895,0.02286287,0.05047406,-0.04424567,-0.02597445,0.08415876,-0.01343754,-0.10237559,-0.07807986,0.04120502,-0.00542333,-0.01025,-0.06284867,0.00025886,-0.16226767,0.03207276,0.00080785,-0.01393268,-0.05947901,-0.07103218,-0.02498854,-0.01967038,0.04576173,-0.1215739,0.05502012,0.02600737,0.00849203,0.02525671,-0.01045573,0.00763984,-0.02316637,0.02961048,0.01201725,0.04732666,-0.02257142,-0.04792202,0.02252869,0.00582257,0.02741482,0.02329355,0.01147278,0.035584,0.12414581,0.00336542,0.02621517,0.00842178,0.05320011,-0.16857891,0.07147396,-0.02132681,0.05883799,0.01646758,-0.01136778,0.02165758,0.07567924,0.03667703,0.03283997,-0.01897569,0.07561611,0.02611242,-0.04045264,-0.04018884,-0.06946231,-0.02745236,-0.00442297,-0.039232,-0.04164165,0.01147229,0.01562575,0.00201891,0.04531209,0.0467118,0.00984896,0.08894744,-0.1015816,0.01438385,0.0106711,0.04130183,0.06332558,0.01515949,-0.01589917,0.02685399,-0.00388352,0.03390119,0.16481055,-0.01374606,0.00977977,0.01212582,-0.02698433,0.05230857,-0.05854883,-0.02012726,-0.03472359,-0.02478058,0.08468122,0.05864091,0.00607215,0.05757866,-0.02800924,-0.03401458,-0.00863296,-0.01559022,0.02677492,0.01736069,0.00707619,-0.06821879,-0.00727243,0.01292301,-0.00694711,0.02345657,0.0102881,0.00165834,0.08475743,-0.01019101,0.04542262,0.0311377,-0.0186277,-0.14660305,-0.0458769,-0.00651597,-0.01659331,0.00286396,-0.02395316,-0.05824496,0.07744952,-0.05484814,-0.01153586,0.04706081,-0.10780112,-0.02684442,0.07071196,-0.00377033,0.01167678,-0.02306041,-0.01569398,-0.00288988,0.01833601,-0.0277779,-0.05902812,0.00407771,0.0190026,0.0850604,0.0268749,-0.06710259,0.03470307,-0.03617951,-0.01800401,-0.02564973,0.13685842,-0.02335887,-0.06909061,-0.03742533,0.06374726,-0.02305987,-0.06301916,-0.0541239,0.01390915,-0.07779772,-0.00063778,0.11776931,-0.00673584,-0.10176675,-0.04440828,-0.0501552,-0.04369743,0.02017195,-0.02996118,-0.01096444,-0.0007152,-0.01759291,-0.06856415,0.02523179,-0.02922632,0.02818617,0.05615829,-0.05786554,0.00240542,-0.03347024,-0.03382222,-0.01900549,0.00744498,-0.04733472,-0.04200843,0.05336121,0.00251022,-0.00287955,0.01070032,0.01344993,0.04556964,-0.03539393,0.07006834,-0.02249894,-0.05448039,0.06577592,0.05094112,-0.0697633,0.00674471,0.06662214,0.07170363,-0.01817681,0.0096469,0.02169404,0.02412744,-0.02904612,-0.01357154,-0.00368845,0.03999915,-0.09325384,-0.18192926,-0.02695676,-0.03211463,0.01784623,0.02085771,-0.02221613,-0.03390772,-0.04239028,0.03393774,0.10285298,0.09672256,-0.05544903,0.0180116,0.08147196,-0.00336301,0.00402884,-0.06049908,-0.03209602,-0.05133798,0.000935,0.03042258,-0.01216155,0.03167644,-0.10313214,0.00997134,-0.00626593,0.11660895,-0.0224386,0.04018008,0.03757721,0.0617309,-0.01270888,-0.03808029,-0.0213023,-0.01139283,0.05743226,-0.08069415,-0.03148578,0.0152772,0.00640175,-0.04335028,-0.00003154,-0.00594672,-0.10648078,-0.00174029,0.03644967,-0.01505992,0.04559376,0.01128531,0.05580157,0.02746747,0.01722772,0.0287784,0.02447443,0.06347083,-0.03279388,-0.07221449,-0.00998026,-0.02795436,0.01436548,-0.02643966,-0.07921167,0.01755867,-0.02204501,0.05931284,0.027528,-0.02555848,-0.02985758,-0.01552655,-0.00424577,-0.00046153,0.07240423,-0.01850464,0.05140327,-0.05004375,0.05412833,0.0599244,-0.03207589,-0.03728324,-0.01282451,0.00114451,-0.04061651,0.05190764,0.09490591,0.05123192,0.05025493,0.0482895,0.01082784,-0.03565856,-0.01696561,-0.00852895,0.01542038,-0.04046826,0.02015334,0.00693571,0.00035961,-0.25211525,0.04309602,-0.01286042,0.03117594,-0.02604719,-0.02530554,0.02112051,0.02303889,0.04117514,-0.06806333,0.0471805,0.02600363,-0.00059933,-0.02864842,-0.00931962,0.01042159,0.02792272,-0.04553166,0.09050428,0.05051435,0.04218858,0.05236396,0.25691244,0.01620709,0.03559414,0.01087123,-0.02794117,-0.0032499,-0.06734429,0.06071457,0.01992941,-0.00040096,0.08939655,0.00863324,-0.05080153,0.02548735,-0.03336605,0.03963072,0.01131568,0.02626098,-0.08396163,0.00107751,-0.02387474,0.00709383,0.1171841,0.02035161,-0.04193592,-0.08795352,0.04465507,0.06477737,-0.09154395,-0.02899545,-0.05784027,-0.03580867,0.03922287,0.03348272,0.01807959,-0.01293233,0.01381718,-0.02416197,0.03162028,-0.02370059,0.01080335,0.00887489,-0.00466817],"last_embed":{"hash":"1m22jsz","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423525492},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{3}","lines":[255,255],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{32}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09792849,-0.02661731,-0.01241619,-0.02187071,-0.01967776,0.03580207,-0.01104299,0.05403815,0.00692427,-0.03361103,-0.01734039,-0.03826702,0.09542232,0.00006925,0.0146829,-0.06364844,0.02000751,0.04541319,-0.06278784,-0.01989102,0.09600006,-0.00671675,-0.0665831,-0.07637617,0.05554022,-0.02502115,-0.00586039,-0.05804741,-0.01943948,-0.20867939,0.02571112,-0.01447995,-0.00017923,-0.05067626,-0.0850149,0.01536468,0.00518794,0.04668183,-0.09513383,0.04891549,0.02594336,0.00060144,0.03029802,-0.0326535,-0.00318413,-0.0235817,0.01967714,0.00505618,0.02848676,0.0046713,-0.06983652,0.01647331,0.01041802,0.03141029,0.02996095,0.00139287,0.02873879,0.11408883,0.0080566,0.042559,0.00441771,0.04867351,-0.19673222,0.07583158,-0.03989072,0.01820293,-0.02040101,-0.02100874,0.01167352,0.06792988,0.01362539,0.04395595,-0.0323085,0.06308462,0.01874173,-0.0595568,-0.04232802,-0.06372404,-0.01944277,-0.01708692,-0.04016228,-0.04293805,-0.00339365,-0.02914778,0.04075553,0.0399014,0.03003576,0.01701298,0.06360183,-0.09857494,0.02864844,0.00661967,0.03733356,0.05655681,0.01247338,-0.0112246,0.00484564,-0.00328698,0.01018612,0.14740051,0.01005337,0.00525855,-0.02020971,0.00130652,0.07690338,-0.05518521,-0.00458926,-0.01054596,-0.00521482,0.05485602,0.04148583,0.00324807,0.05186982,-0.02498477,-0.06132876,0.00747224,0.01901597,-0.00853931,0.02129108,0.02172948,-0.07315754,-0.01255486,0.02858632,-0.00351025,0.04530235,0.00837,0.00700461,0.07289658,-0.0034745,0.07770012,0.03440006,0.00346725,-0.1530439,-0.05959385,-0.01862697,-0.00093945,-0.01747116,-0.02472162,-0.04837674,0.05404416,-0.05464345,-0.02650228,0.03580834,-0.08569221,-0.02007814,0.07753585,-0.02536622,0.00197274,-0.03008612,-0.04206971,-0.00454756,0.02280279,-0.0031288,-0.05340056,0.01732647,0.01664502,0.0813918,0.06276573,-0.09276765,0.04246683,-0.00558448,-0.01894872,-0.01760805,0.14023699,-0.01354798,-0.05202558,-0.04268645,0.06280094,-0.0187163,-0.04772822,-0.05779824,0.0185172,-0.05368484,0.0036593,0.08905305,-0.0184386,-0.08861639,-0.05050123,-0.0580928,-0.03828824,-0.01115739,-0.02028278,-0.00946647,-0.00457735,-0.02766174,-0.07810503,0.05940366,-0.0298363,0.02488093,0.04952201,-0.06768588,-0.02045418,-0.03051363,-0.0060558,-0.01345323,0.00900359,-0.03864626,-0.04697081,0.04734744,0.0093732,-0.00549878,0.00309674,0.02100578,0.04674543,-0.02799372,0.08097393,-0.03555714,-0.03506963,0.07429981,0.04289583,-0.04718659,0.02243513,0.05092582,0.06241322,-0.00655114,0.00166112,0.02303618,0.01236591,0.00193901,-0.00250843,0.0283825,0.03759484,-0.08760431,-0.20313627,-0.00223319,-0.06919881,0.01396413,0.02893904,-0.05096443,-0.01528522,-0.05076126,0.02125314,0.11798068,0.0946862,-0.03752145,0.01448305,0.06767955,0.0135468,-0.00345804,-0.0635245,-0.01816878,-0.08260954,0.01267762,0.02326489,0.0042919,0.02741306,-0.04908233,0.02692928,-0.011006,0.1047384,0.01154141,0.03067067,0.02560913,0.07507962,0.01519289,-0.02653722,-0.01239738,-0.0084658,0.05984769,-0.07685611,-0.00640147,0.01508083,0.00049009,-0.04065004,0.04941021,-0.0134362,-0.11390874,-0.01132438,0.0557717,-0.00871577,0.04835875,-0.00798944,0.04080585,0.07048574,0.01997626,0.01090693,0.01559979,0.08377185,-0.05348627,-0.09414586,-0.01547008,-0.02183223,-0.00039438,-0.03991082,-0.05788881,0.04764375,-0.02966474,0.04086197,0.04669422,-0.00728444,-0.0153094,-0.02817073,0.02107934,-0.02046939,0.07984425,-0.01978651,0.04877239,-0.04347562,0.06422228,0.02234266,-0.00337511,-0.03321527,-0.02346903,0.02356026,-0.04608893,0.05110711,0.06851873,0.05218864,0.04468914,0.05598305,0.00107495,-0.02000692,-0.00395875,0.01074802,0.00788874,-0.04369141,0.03701313,0.01904806,-0.00713122,-0.25255805,0.03045093,-0.00803535,0.02693983,-0.02107273,-0.010866,0.02051756,-0.01398526,0.03275573,-0.0686601,0.04076825,0.04542544,0.012803,-0.04771757,-0.03696551,0.00938004,-0.00814347,-0.03941439,0.09805641,0.02965768,0.04951441,0.03803158,0.25639141,-0.00426717,0.00435003,-0.00256211,-0.00995243,-0.0138165,-0.06259074,0.03530228,0.00737716,0.00404467,0.11477157,-0.00137648,-0.03899037,0.02906401,-0.03124119,0.0266242,0.01746729,-0.00902537,-0.06807996,0.01253289,-0.02736545,0.00599805,0.1239207,0.01825513,-0.03592772,-0.05216237,0.05703466,0.06345341,-0.07809833,-0.05028652,-0.03896257,-0.00457479,0.02999206,0.03824191,0.0119148,0.00244381,0.00644854,-0.02281508,0.03914804,0.01691481,0.00538561,0.0003914,0.01799867],"last_embed":{"hash":"6pnsd5","tokens":305}}},"text":null,"length":0,"last_read":{"hash":"6pnsd5","at":1753423525541},"key":"notes/saliu/Lotto Filters, Reduction Strategies in Lottery Software.md#Lotto Filters, Reduction Strategies in Lottery Software#[<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)#{32}","lines":[287,292],"size":654,"outlinks":[{"title":"Ion Saliu lottery software, lotto software is best to win with mathematics of filtering, filters.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Effective lottery software must apply filters or restrictions to the huge amounts of combinations.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},