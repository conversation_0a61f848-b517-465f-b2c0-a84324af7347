"smart_sources:smart connections/Smart Connections 202405161305.md": {"path":"smart connections/Smart Connections 202405161305.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0679841,-0.03220907,-0.00797215,-0.02194431,-0.00026996,-0.03179817,0.00556765,0.04386298,0.01689013,-0.00008627,0.017126,-0.02470882,0.08620004,0.06263026,0.03069566,0.03443782,-0.01063804,-0.01020402,-0.04998273,0.03116883,0.0978968,-0.03138993,0.04989109,-0.00937703,0.01807392,0.03430508,-0.02755493,-0.08537497,-0.00213156,-0.18452081,0.0145436,-0.01564566,0.01174466,0.02401555,-0.02291913,0.00096453,-0.02425381,-0.03135793,-0.02933723,0.01949264,0.00157184,0.00258107,0.02346664,-0.00601239,-0.00371765,-0.05113446,-0.02477982,-0.00547561,-0.03669463,-0.04214931,-0.01105409,-0.02807106,-0.02538555,0.01925925,0.01993206,0.0224229,0.00980402,0.04826389,0.0236854,0.0669366,0.0657931,0.03750448,-0.18506411,0.11451678,-0.00968554,0.01302032,-0.01613891,0.00356299,0.04307682,0.01873126,0.0013284,0.04173956,0.01555205,-0.00333132,0.03491053,0.03392978,0.00256434,-0.03026434,-0.00418672,-0.04171993,0.03924396,0.03371374,-0.01035138,0.00271906,-0.01928331,-0.01585551,0.01732381,-0.01104108,-0.03064859,0.0060376,-0.06521542,-0.04361243,0.01734099,0.05514075,-0.02123965,-0.01661894,0.0031183,0.0006094,-0.13437407,0.10373877,-0.08871157,0.02864139,0.0090661,-0.00465484,0.03873402,-0.03032337,-0.00207323,-0.03749103,-0.02898279,0.02218979,-0.01304978,0.01148201,0.04085884,-0.07169655,0.05186712,0.00398803,0.04027553,0.03699905,0.02200107,0.00874173,-0.03666576,0.04964339,0.04981013,-0.02810706,0.0514868,-0.04086145,0.04874927,0.05243393,0.02178424,0.06719444,0.06342195,0.01540617,-0.03952665,-0.01819495,0.00300871,-0.00327502,0.02868422,-0.01739618,0.02646477,-0.00422108,-0.01371539,0.02073292,0.02648185,-0.13217643,0.0169235,0.05388797,0.00848981,0.00830886,-0.02366265,-0.07929508,-0.00477281,0.04601678,0.01451276,0.01225289,0.04124321,-0.03719423,0.07362816,0.11266793,-0.13361184,0.05870476,0.02525964,-0.05263081,-0.04009128,0.0618722,0.00597279,-0.16896223,-0.04641394,-0.02136089,0.01162351,-0.03466722,0.03065985,0.04097121,-0.03046486,0.01197463,0.09482035,-0.00061498,-0.070857,-0.02692827,-0.02773582,0.05190656,-0.01799479,-0.04060014,0.00905172,-0.03115292,-0.04279217,-0.07168444,-0.01843954,-0.05302978,0.02496473,-0.0639694,-0.09257603,-0.04453436,-0.02224772,0.08815791,-0.00747075,-0.03414803,-0.01312141,-0.04473056,0.02660866,-0.01287604,0.04064754,-0.0140609,-0.03843464,0.00782931,-0.06565791,-0.01356635,0.01413365,-0.0194682,0.06053521,0.07795922,-0.05572111,0.0132756,0.08347764,0.02133377,-0.01655921,-0.03862631,0.04133342,0.03594802,0.01279745,0.06004142,0.00622914,0.02456519,-0.07182967,-0.23263159,-0.04030973,0.01766268,-0.01048188,0.03277015,-0.10279727,0.02589447,0.00028093,0.06243036,0.07365973,0.13028997,0.03994579,-0.01592062,-0.02707843,-0.0054216,0.02509331,-0.00050491,0.04547337,-0.02341521,0.03643577,0.01023904,0.07907735,-0.00696441,-0.0630575,0.01314314,-0.04484994,0.15152751,0.02884231,0.02503401,-0.00433666,0.01529912,0.01275508,-0.01928553,-0.13150874,0.05327002,0.01079215,0.01869287,0.03303242,-0.02235405,-0.04133634,-0.09362266,0.05559682,-0.02575895,-0.09693926,-0.06549775,-0.03779504,-0.00864786,-0.10760698,-0.05737626,-0.01792832,0.02842417,0.00544494,-0.00035237,0.07692711,-0.03230907,0.02823604,-0.08543939,0.00456342,-0.02803509,0.07542811,-0.04236204,0.03018548,-0.02222507,-0.03663078,0.06283095,0.00106377,0.03069551,0.03217072,0.00745696,0.00043741,0.02508228,0.12427547,0.00991288,0.01095007,0.05707395,-0.04001389,0.02037342,-0.05455172,0.0006878,0.01485066,0.05763502,-0.04964745,0.08058034,0.03262953,0.01369908,0.07708643,0.04175328,0.01637407,0.06049442,-0.01157528,0.00312594,0.00376291,0.00607436,-0.05229243,0.07001524,-0.00619302,-0.27129996,0.04614474,0.00831331,-0.00852996,-0.04461462,0.04123618,0.02991727,-0.05675223,-0.02750348,0.04063055,0.00423782,0.0098986,0.01463886,-0.0407255,0.0427914,-0.00899091,0.01202699,-0.00806843,0.0039116,-0.02829478,-0.02398333,0.03093139,0.21998705,0.02723281,-0.00384961,0.00402969,-0.05071096,0.0519464,0.07711409,-0.03052278,-0.04173076,0.0070468,0.04591423,-0.03446749,0.04933535,-0.02915491,-0.03163661,-0.09438107,0.03020666,0.00848814,-0.02549018,0.00275725,-0.02413901,0.0585032,0.10526615,0.01295959,-0.065409,-0.03559255,0.00719989,0.0493056,-0.00637369,-0.05129292,0.00703808,0.02623958,-0.02323032,0.07628052,0.00629741,-0.02391851,-0.02254243,-0.0082755,0.02717328,-0.03845729,0.02371397,0.01308181,-0.02616046],"last_embed":{"hash":"316ff9b95e976f5127d1a1af798996f4835a73e95112aee2b9424b1488d2c68d","tokens":503}}},"last_read":{"hash":"316ff9b95e976f5127d1a1af798996f4835a73e95112aee2b9424b1488d2c68d","at":1745995227857},"class_name":"SmartSource2","outlinks":[{"title":"people are saying","target":"app://obsidian.md/index.html#user-testimonials","line":9},{"title":"人們怎麼說","target":"app://obsidian.md/index.html#user-testimonials","line":18},{"title":"Easy to install and use","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/smart-connections-install.gif","line":25},{"title":"易於安裝和使用","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/smart-connections-install.gif","line":34},{"title":"Smart View demo showing that the most relevant notes are shown at the top based on the current note.","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/SCv2-Smart-View-light.gif","line":101},{"title":"Smart View 範例顯示最相關的筆記會根據目前的筆記顯示在最上方。","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/SCv2-Smart-View-light.gif","line":109},{"title":"Demo showing how Smart Chat can answer the question \"Who am I?\" based on the notes in Obsidian","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/smart-connections-chat-who-am-i.gif","line":115},{"title":"示範 Smart Chat 如何根據 Obsidian 中的筆記回答「我是誰？」的問題","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/smart-connections-chat-who-am-i.gif","line":124},{"title":"\"I've switched over from Mem to Obsidian when I found this plugin\"","target":"https://discord.com/channels/686053708261228577/694233507500916796/1091164112425320538","line":136},{"title":"\"I actually decided to start using Obsidian BECAUSE of Smart Connections.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/441#:~:text=I%20actually%20decided%20to%20start%20using%20Obsidian%20BECAUSE%20of%20Smart%20Connections.","line":137},{"title":"\"This is such a game-changingly helpful plugin\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/329#issuecomment-2002162224:~:text=This%20is%20such%20a%20game%2Dchangingly%20helpful%20plugin","line":138},{"title":"\"This plugin has become a vital part of my life\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/120#issuecomment-1492842117:~:text=This%20plugin%20has%20become%20a%20vital%20part%20of%20my%20life","line":139},{"title":"\"This is by far my favourite Obsidian plug-in and it is immensely helpful. I'll be doing a full video about using it for PhD research\"","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions/371#discussioncomment-7977910:~:text=This%20is%20by%20far%20my%20favourite%20Obsidian%20plug%2Din%20and%20it%20is%20immensely%20helpful.%20I%27ll%20be%20doing%20a%20full%20video%20about%20using%20it%20for%20PhD%20research","line":140},{"title":"\"It's astonishing the power it provides to deal with scientific research and scientific articles included in the vault.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/250#issuecomment-1595108987:~:text=It%27s%20astonishing%20the%20power%20it%20provids%20to%20deal%20with%20scientific%20research%20and%20scientific%20articles%20included%20in%20the%20vault.","line":141},{"title":"\"This is an AWESOME little plugin. Thanks for sharing.\"","target":"https://forum.obsidian.md/t/introducing-smart-chat-a-game-changer-for-your-obsidian-notes-smart-connections-plugin/56391/8?u=wfh#:~:text=This%20is%20an%20AWESOME%20little%20plugin.%20Thanks%20for%20sharing.","line":144},{"title":"\"Hopping on to also say thanks. I have been wanting this feature in something ever since reading about tad starners remembrance agent in the 90s! And this is even better.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/47#issuecomment-1471441869:~:text=Hopping%20on%20to%20also%20say%20thanks.%20I%20have%20been%20wanting%20this%20feature%20in%20something%20ever%20since%20reading%20about%20tad%20starners%20remembrance%20agent%20in%20the%2090s!%20And%20this%20is%20even%20better.","line":145},{"title":"\"I'm having so much fun using your chat plugin to search my notes better and get insights.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/57#:~:text=Hi%2C-,I%27m%20having%20so%20much%20fun%20using%20your%20chat%20plugin%20to%20search%20my%20notes%20better%20and%20get%20insights,-.%20I%20was%20just","line":146},{"title":"\"This is undoubtedly an excellent plugin that makes a significant improvement in how we interact with our notes.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/pull/219#issuecomment-1562572364:~:text=This%20is%20undoubtedly%20an%20excellent%20plugin%20that%20makes%20a%20significant%20improvement%20in%20how%20we%20interact%20with%20our%20notes.","line":147},{"title":"wrote a post about how I use it to massively save time on summarizing legal cases","target":"https://careylening.substack.com/p/the-power-of-links-and-second-brains-d1d","line":149},{"title":"寫了一篇關於如何使用它來大幅節省總結法律案件時間的文章","target":"https://careylening.substack.com/p/the-power-of-links-and-second-brains-d1d","line":176},{"title":"Even more love for Smart Connections 🥰","target":"https://smartconnections.app/smart-connections-love/","line":211},{"title":"Demo showing Smart View results changing based on the current note","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/SCv2-Smart-View-dark.gif","line":298},{"title":"Demo showing the fold/unfold feature which displays or hides the content of the notes in the Smart View","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/SCv2-fold-unfold.gif","line":306},{"title":"展示智慧檢視結果會根據目前的筆記而變化的示範","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/SCv2-Smart-View-dark.gif","line":321},{"title":"展示摺疊/展開功能的示範，此功能會顯示或隱藏智慧檢視中筆記的內容","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/SCv2-fold-unfold.gif","line":329},{"title":"Access model settings in the Smart Chat","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/smart-chat-settings.png","line":368},{"title":"Public Discussions on GitHub","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions","line":469},{"title":"`smart-chat-model`","target":"https://github.com/brianpetro/jsbrains/tree/main/smart-chat-model","line":508},{"title":"`smart-embed-model`","target":"https://github.com/brianpetro/jsbrains/tree/main/smart-embed-model","line":509},{"title":"`smart-chats`","target":"https://github.com/brianpetro/jsbrains/tree/main/smart-chats","line":510},{"title":"`smart-chunks`","target":"https://github.com/brianpetro/jsbrains/tree/main/smart-chunks","line":511},{"title":"`smart-chunks`","target":"https://github.com/brianpetro/jsbrains/tree/main/smart-chunks","line":512},{"title":"`smart-entities`","target":"https://github.com/brianpetro/jsbrains/tree/main/smart-entities","line":513},{"title":"Report an issue on GitHub","target":"https://github.com/brianpetro/obsidian-smart-connections/issues","line":542},{"title":"Brian on Founderoo","target":"https://www.founderoo.co/posts/smart-connections-brian-petro","line":571},{"title":"Smart Chat","target":"app://obsidian.md/index.html#smart-chat-transform-your-notes-into-interactive-conversations","line":599},{"title":"Smart View","target":"app://obsidian.md/index.html#smart-view","line":599},{"title":"Obsidian","target":"https://obsidian.md/","line":599},{"title":"Introducing Smart Connections","target":"https://wfhbrian.com/introducing-obsidian-smart-connections/","line":599},{"title":"Smart Chat","target":"app://obsidian.md/index.html#smart-chat-transform-your-notes-into-interactive-conversations","line":606},{"title":"Smart View","target":"app://obsidian.md/index.html#smart-view","line":606},{"title":"Obsidian","target":"https://obsidian.md/","line":606},{"title":"介紹 Smart Connections","target":"https://wfhbrian.com/introducing-obsidian-smart-connections/","line":606},{"title":"discussion on GitHub","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions","line":613},{"title":"GitHub 上的熱烈討論","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions","line":618},{"title":"Smart Chat","target":"https://wfhbrian.com/introducing-smart-chat-transform-your-obsidian-notes-into-interactive-ai-powered-conversations/","line":625},{"title":"智慧聊天","target":"https://wfhbrian.com/introducing-smart-chat-transform-your-obsidian-notes-into-interactive-ai-powered-conversations/","line":632},{"title":"Smart View demo showing that the most relevant notes are shown at the top based on the current note.","target":"https://raw.githubusercontent.com/brianpetro/obsidian-smart-connections/HEAD/assets/SCv2-Smart-View-light.gif","line":723},{"title":"obsidian-smart-connections- Chat with your notes & see links to related content with AI embeddings.","target":"obsidian-smart-connections- Chat with your notes & see links to related content with AI embeddings.","line":730},{"title":"obsidian-smart-connections- Chat with your notes & see links to related content with AI embeddings.","target":"obsidian-smart-connections- Chat with your notes & see links to related content with AI embeddings.","line":737},{"title":"2021-08-01","target":"https://openai.com/api/pricing/","line":793},{"title":"GitHub repository","target":"https://github.com/brianpetro/obsidian-smart-connections/issues","line":810}],"blocks":{"#Smart Connections: AI-Powered Note Connections `v2.1`":[1,817],"#Smart Connections: AI-Powered Note Connections `v2.1`#{1}":[3,4],"#Smart Connections: AI-Powered Note Connections `v2.1`#{2}":[5,5],"#Smart Connections: AI-Powered Note Connections `v2.1`#{3}":[6,6],"#Smart Connections: AI-Powered Note Connections `v2.1`#{4}":[7,7],"#Smart Connections: AI-Powered Note Connections `v2.1`#{5}":[8,8],"#Smart Connections: AI-Powered Note Connections `v2.1`#{6}":[9,10],"#Smart Connections: AI-Powered Note Connections `v2.1`#{7}":[11,38],"#Smart Connections: AI-Powered Note Connections `v2.1`#A brief history":[39,60],"#Smart Connections: AI-Powered Note Connections `v2.1`#A brief history#{1}":[41,60],"#Smart Connections: AI-Powered Note Connections `v2.1`#Mission":[61,86],"#Smart Connections: AI-Powered Note Connections `v2.1`#Mission#{1}":[63,86],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections":[87,128],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#{1}":[89,96],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart View: AI-Powered Note Discovery":[97,110],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart View: AI-Powered Note Discovery#{1}":[99,110],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart Chat: AI Conversations Based on Your Notes":[111,128],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart Chat: AI Conversations Based on Your Notes#{1}":[113,128],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials":[129,212],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{1}":[131,134],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{2}":[135,135],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{3}":[136,136],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{4}":[137,137],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{5}":[138,138],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{6}":[139,139],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{7}":[140,140],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{8}":[141,141],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{9}":[142,142],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{10}":[143,143],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{11}":[144,144],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{12}":[145,145],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{13}":[146,146],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{14}":[147,147],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{15}":[148,148],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{16}":[149,149],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{17}":[150,150],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{18}":[151,151],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{19}":[152,152],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{20}":[153,153],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{21}":[154,154],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{22}":[155,156],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{23}":[157,186],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{24}":[187,187],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{25}":[188,188],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{26}":[189,189],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{27}":[190,190],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{28}":[191,191],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{29}":[192,192],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{30}":[193,193],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{31}":[194,194],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{32}":[195,195],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{33}":[196,196],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{34}":[197,197],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{35}":[198,198],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{36}":[199,199],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{37}":[200,200],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{38}":[201,201],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{39}":[202,202],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{40}":[203,203],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{41}":[204,204],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{42}":[205,205],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{43}":[206,206],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{44}":[207,207],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{45}":[208,208],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{46}":[209,210],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{47}":[211,212],"#Smart Connections: AI-Powered Note Connections `v2.1`#How it Works":[213,230],"#Smart Connections: AI-Powered Note Connections `v2.1`#How it Works#{1}":[215,220],"#Smart Connections: AI-Powered Note Connections `v2.1`#How it Works#{2}":[221,230],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation":[231,238],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation#{1}":[233,234],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation#Installing from Obsidian community plugins":[235,238],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation#Installing from Obsidian community plugins#{1}":[237,238],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings":[239,291],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Default settings":[241,264],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Default settings##Local embedding models":[243,264],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Default settings##Local embedding models#{1}":[245,264],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup":[265,291],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup#{1}":[267,270],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup##OpenAI API Key":[271,291],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup##OpenAI API Key#{1}":[273,274],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup##OpenAI API Key#{2}":[275,291],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features":[292,456],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View":[294,337],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View#{1}":[296,299],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View":[300,307],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{1}":[302,302],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{2}":[303,303],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{3}":[304,305],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{4}":[306,307],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search":[308,337],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search#{1}":[310,311],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search#{2}":[312,313],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search#{3}":[314,337],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat":[338,394],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat#{1}":[340,341],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)":[342,365],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#{1}":[344,345],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock":[346,365],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock#{1}":[348,348],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock#{2}":[349,350],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock#{3}":[351,365],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models":[366,394],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#{1}":[368,369],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)":[370,394],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)#{1}":[372,376],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)#{2}":[377,379],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)#{3}":[380,383],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)#{4}":[384,386],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)#{5}":[387,389],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)#{6}":[390,392],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (current)#{7}":[393,394],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Random Note":[395,402],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Random Note#{1}":[398,399],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Random Note#{2}":[400,400],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Random Note#{3}":[401,402],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)":[403,440],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{1}":[405,406],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{2}":[407,408],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{3}":[409,411],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{4}":[412,412],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{5}":[413,413],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{6}":[414,414],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{7}":[415,416],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{8}":[417,440],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Dynamic Code Blocks":[441,447],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Dynamic Code Blocks#{1}":[443,443],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Dynamic Code Blocks#{2}":[444,447],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Note vs Block Level Smart Connections":[448,452],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Note vs Block Level Smart Connections#{1}":[450,452],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Notifications and Progress Indicators":[453,456],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Notifications and Progress Indicators#{1}":[455,456],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility":[457,462],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility#{1}":[459,459],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility#{2}":[460,460],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility#{3}":[461,462],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community":[463,480],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#{1}":[465,468],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#{2}":[469,470],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)":[471,480],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)#{1}":[473,474],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)##Screencasts":[475,480],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)##Screencasts#{1}":[477,480],"#Smart Connections: AI-Powered Note Connections `v2.1`#Content & Resources":[481,485],"#Smart Connections: AI-Powered Note Connections `v2.1`#Content & Resources#{1}":[483,483],"#Smart Connections: AI-Powered Note Connections `v2.1`#Content & Resources#{2}":[484,485],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture":[486,526],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{1}":[488,489],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{2}":[490,490],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{3}":[491,491],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{4}":[492,493],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models":[494,499],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{1}":[496,496],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{2}":[497,497],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{3}":[498,499],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies":[500,522],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies#{1}":[502,503],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies":[504,514],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{1}":[506,507],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{2}":[508,508],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{3}":[509,509],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{4}":[510,510],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{5}":[511,511],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{6}":[512,512],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{7}":[513,514],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##Third-party Dependencies":[515,522],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##Third-party Dependencies#{1}":[517,518],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##Third-party Dependencies#{2}":[519,522],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Themes & Styles":[523,526],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Themes & Styles#{1}":[525,526],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues":[527,543],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#Embedding process keeps restarting or is slow":[529,533],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#Embedding process keeps restarting or is slow#{1}":[531,531],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#Embedding process keeps restarting or is slow#{2}":[532,533],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors":[534,543],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{1}":[536,536],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{2}":[537,537],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{3}":[538,539],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{4}":[540,541],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{5}":[542,543],"#Smart Connections: AI-Powered Note Connections `v2.1`#Developers":[544,555],"#Smart Connections: AI-Powered Note Connections `v2.1`#Developers#{1}":[546,550],"#Smart Connections: AI-Powered Note Connections `v2.1`#Developers#{2}":[551,555],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification":[556,561],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{1}":[558,559],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{2}":[560,561],"#Smart Connections: AI-Powered Note Connections `v2.1`#Commercial use":[562,565],"#Smart Connections: AI-Powered Note Connections `v2.1`#Commercial use#{1}":[564,565],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator":[566,817],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator#{1}":[568,569],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator#{2}":[570,570],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator#{3}":[571,817],"#---frontmatter---":[573,null]},"last_import":{"mtime":1718845781110,"size":59002,"at":1740449882728,"hash":"316ff9b95e976f5127d1a1af798996f4835a73e95112aee2b9424b1488d2c68d"},"key":"smart connections/Smart Connections 202405161305.md"},