"smart_sources:PromptingTools.jl/Reference for Experimental Module - PromptingTools.jl.md": {"path":"PromptingTools.jl/Reference for Experimental Module - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13019359,0.02122116,-0.01533842,-0.04033316,-0.02471199,0.01822625,-0.02297327,0.05629485,0.04728392,-0.02373198,-0.00279754,-0.01483091,-0.00641681,0.04191297,-0.00208549,0.01164137,-0.05372632,0.03300127,-0.06890495,-0.02483831,0.07296129,0.04102506,0.02220419,-0.05303769,0.03574927,0.05482867,-0.00220072,-0.04437204,-0.00943938,-0.20051436,0.05491772,-0.02397033,0.00416026,-0.00614902,-0.04914489,-0.00466303,-0.04147421,0.03150131,-0.01386538,0.00709938,0.04225761,0.00181018,-0.0294214,0.00144898,-0.04828544,-0.09406307,-0.02764616,-0.02555772,-0.00614983,-0.05614986,-0.00893471,-0.04505108,0.05177935,-0.02286474,0.01015152,0.01006322,0.01476367,0.0972689,0.0416391,0.02255548,0.02749525,0.01100706,-0.18942693,0.14875446,0.02114107,0.00384193,-0.01103711,-0.00075155,0.0352424,0.0387409,-0.04256469,0.03626047,-0.00175542,0.05634337,0.00889751,-0.02491834,-0.02111806,-0.02927677,0.00896821,-0.10430805,-0.0271935,-0.00459467,-0.01900336,-0.01762361,-0.05151383,0.00913799,0.01244948,-0.00613878,0.06202458,0.00067922,-0.00267262,-0.08581823,-0.016869,0.03789108,-0.05467024,-0.00186507,0.03656501,-0.04257663,-0.09258355,0.12999178,-0.03011257,-0.01105727,0.02572369,-0.01056779,0.01841873,-0.06720984,-0.0143462,-0.02735455,-0.02561464,0.01845978,-0.00007814,-0.03319591,-0.00557709,-0.06994534,0.00185955,-0.02445398,0.01566142,-0.01726035,0.02070788,-0.05145919,0.01640393,0.02458651,0.02974255,-0.00494083,0.00068647,0.03906675,0.02711387,0.04721528,-0.00901203,-0.01100069,0.0512659,0.00973741,-0.05274666,-0.03501002,-0.00897547,0.05215861,0.03253827,-0.06919006,0.02747214,-0.00497803,0.04115532,-0.00035656,0.01648986,-0.10800353,-0.00304773,0.03407994,-0.0146575,-0.0302239,-0.00534981,-0.02791561,0.0456684,0.05128005,-0.0569215,0.01621765,0.056784,0.04338819,0.0529886,0.08467714,-0.00818417,0.01918542,-0.06834126,-0.0439875,0.0125265,0.14386205,0.00625314,-0.05449469,-0.09437334,-0.03216061,-0.0493746,-0.04431936,0.01623855,0.04653086,-0.04821513,-0.0136269,0.04148394,0.03600771,-0.06652132,0.04753035,0.01683488,0.0278895,0.03759739,-0.06839909,-0.0331305,0.02619235,-0.005463,-0.01061054,-0.02844725,-0.08917565,-0.01825837,0.06052218,-0.07110524,0.02698187,0.06528389,-0.03137641,0.01083619,0.00038212,-0.00025445,-0.06582695,0.04462987,-0.04472283,0.06145646,0.05178898,0.03150498,0.04533479,-0.00990422,0.06520157,0.06258895,-0.01950216,0.10354532,0.02261947,-0.10794062,-0.06452514,0.06540932,0.00761975,-0.02634653,-0.01869663,-0.06313317,0.00297224,0.01059132,0.07180102,-0.03492641,-0.01232195,-0.03986512,-0.23279236,0.02308649,0.04534744,-0.04038772,0.06601405,-0.07967176,0.09431773,-0.0524024,-0.03474417,0.04956349,0.09926406,-0.0350425,0.04538539,-0.01459019,-0.02823873,-0.00573806,-0.02727695,-0.00518693,-0.01206257,0.0316709,0.00012209,-0.01177706,0.03678431,-0.07755871,0.00181282,-0.0270833,0.11300217,0.0308117,0.04094378,-0.08500365,-0.00240365,-0.00047519,0.07053708,-0.11977087,-0.03565041,0.03586776,-0.03585219,0.07044686,0.08882588,0.01677016,-0.0277039,0.04392492,0.00603755,-0.03874993,-0.01165359,-0.00138379,-0.05511395,-0.04263216,-0.04185612,0.00648772,0.02256728,-0.05243934,0.00946784,0.00007872,0.00656148,-0.01004767,-0.02089654,-0.05638401,0.00564348,-0.00044986,0.0285162,-0.01228644,-0.0095206,-0.04492315,0.05564319,0.03172483,0.02909954,-0.00273805,0.06437144,0.00863529,-0.01503505,0.10301375,0.04652601,0.07890677,-0.000986,0.05007881,-0.00811108,-0.05887261,-0.00544582,-0.03025791,-0.03306958,0.03061464,0.00496979,0.01899705,0.0420843,0.04809134,0.02696049,-0.03226132,0.10477562,-0.03264375,-0.02526715,0.01956096,0.02284952,-0.03023924,0.04972887,0.04112904,-0.25347981,0.02693366,0.02361891,0.02790351,-0.02901608,0.07802784,0.042298,0.00565898,0.00216365,0.05122724,-0.02215287,-0.00383165,0.01387724,0.0033292,0.04161355,0.04621801,0.02583666,-0.00675164,0.01960825,-0.07419651,0.0355501,0.01143165,0.24034785,-0.03204229,0.0041873,-0.00815069,0.00221821,-0.05806175,0.07861889,0.02913088,0.0137112,0.04684341,0.14045928,-0.02158871,0.03758135,0.01180268,-0.05124665,-0.01174647,0.02182663,0.01771472,-0.02960404,0.04003,-0.05341049,0.00045757,0.0593334,-0.02202163,-0.02479231,-0.08042817,-0.06124101,0.0372779,0.0024304,-0.00739491,-0.02382182,-0.01026338,-0.01154605,-0.02243872,0.06630791,0.01444883,-0.06471116,-0.01824361,0.05741138,-0.00832679,0.09886387,0.00157466,-0.01972332],"last_embed":{"hash":"5e4d2b0a5b45379c01ea4e83b6035c6a2485c2e5d9c6ba81303b6a8500bdd1e3","tokens":460}}},"last_read":{"hash":"5e4d2b0a5b45379c01ea4e83b6035c6a2485c2e5d9c6ba81303b6a8500bdd1e3","at":1745995225217},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/reference_experimental/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"​","target":"#Reference-for-Experimental-Module","line":243},{"title":"`PromptingTools.Experimental.AgentTools`","target":"#PromptingTools.Experimental.AgentTools","line":247},{"title":"`PromptingTools.Experimental.RAGTools`","target":"#PromptingTools.Experimental.RAGTools","line":248},{"title":"#","target":"#PromptingTools.Experimental","line":250},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/Experimental.jl#L1-L11","line":269},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/reference_experimental.md","line":273},{"title":"Previous pagePromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":275},{"title":"Next pageRAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":277},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":279},{"title":"Icons8","target":"https://icons8.com","line":279},{"title":"**VitePress**","target":"https://vitepress.dev","line":279}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,242],"##Reference#{1}":[209,242],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)":[243,281],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{1}":[245,246],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{2}":[247,247],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{3}":[248,249],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{4}":[250,261],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{5}":[262,263],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{6}":[264,265],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{7}":[266,268],"#Reference for Experimental Module [​](#Reference-for-Experimental-Module)#{8}":[269,281]},"last_import":{"mtime":1712728000205,"size":5389,"at":1740449882783,"hash":"5e4d2b0a5b45379c01ea4e83b6035c6a2485c2e5d9c6ba81303b6a8500bdd1e3"},"key":"PromptingTools.jl/Reference for Experimental Module - PromptingTools.jl.md"},