#!/usr/bin/env julia

# 簡單測試 WonderGridEngine 結構定義
println("開始測試 WonderGridEngine 結構定義...")

try
    using Dates
    using Statistics
    using Printf

    # 直接測試結構定義
    struct WonderGridError <: Exception
        message::String
        code::Symbol
        context::Dict{String, Any}
        timestamp::DateTime

        function WonderGridError(message::String, code::Symbol, context::Dict{String, Any} = Dict{String, Any}())
            new(message, code, context, now())
        end
    end

    struct WonderGridConfig
        analysis_range::Int
        top_pair_percentage::Float64
        key_number_strategy::Symbol
        combination_limit::Int
        enable_purge::Bool
        enable_lie::Bool
        game_type::Symbol
        pair_analysis_depth::Int
        trend_weight::Float64

        function WonderGridConfig(;
            analysis_range::Int = 150,
            top_pair_percentage::Float64 = 0.25,
            key_number_strategy::Symbol = :ffg,
            combination_limit::Int = 1000,
            enable_purge::Bool = true,
            enable_lie::Bool = false,
            game_type::Symbol = :Lotto6_49,
            pair_analysis_depth::Int = 100,
            trend_weight::Float64 = 0.3
        )
            new(analysis_range, top_pair_percentage, key_number_strategy,
                combination_limit, enable_purge, enable_lie, game_type,
                pair_analysis_depth, trend_weight)
        end
    end

    println("✅ 結構定義測試成功")

    # 測試基本結構創建
    config = WonderGridConfig()
    println("✅ WonderGridConfig 創建成功: $(config.game_type)")

    # 測試錯誤處理
    error = WonderGridError("測試錯誤", :test_error)
    println("✅ WonderGridError 創建成功: $(error.code)")

    println("🎉 WonderGridEngine 基本結構測試完成！")

catch e
    println("❌ 測試失敗: $e")
    exit(1)
end
