
"smart_sources:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md": {"path":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"16zbjaq","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363607266,"size":12051,"at":1753423416500,"hash":"16zbjaq"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Wheels Software, Cover, Missing Combinations, Systems":[8,99],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#{1}":[10,18],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>":[19,34],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>#{1}":[21,34],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>":[35,59],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>#{1}":[37,59],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling":[60,99],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{1}":[62,63],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{2}":[64,64],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{3}":[65,65],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{4}":[66,66],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{5}":[67,68],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{6}":[69,70],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{7}":[71,72],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{8}":[73,74],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{9}":[75,75],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{10}":[76,77],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{11}":[78,78],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{12}":[79,79],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{13}":[80,80],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{14}":[81,81],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{15}":[82,82],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{16}":[83,83],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{17}":[84,84],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{18}":[85,85],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{19}":[86,86],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{20}":[87,87],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{21}":[88,88],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{22}":[89,89],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{23}":[90,90],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{24}":[91,91],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{25}":[92,93],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{26}":[94,99]},"outlinks":[{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":40},{"title":"Verify, check lotto wheels and generate lotto-6 systems.","target":"https://saliu.com/HLINE.gif","line":56},{"title":"\n\n## Resources in Lottery Software, Systems, Lotto Wheeling\n\n","target":"https://saliu.com/content/lottery.html","line":58},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":64},{"title":"_**Lotto, Lottery, Software**_","target":"https://saliu.com/LottoWin.htm","line":78},{"title":"_**Lotto Wheels for Lotto Games Drawing 5, 6, 7 Numbers**_","target":"https://saliu.com/lotto_wheels.html","line":80},{"title":"_**Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":82},{"title":"_**Design Lotto Wheels Manually, Lotto Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":84},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":85},{"title":"_**Best On-The-Fly Wheeling Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":87},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":88},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":89},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":90},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":91},{"title":"**Lotto Wheels Software**","target":"https://saliu.com/infodown.html","line":92},{"title":"The best software to verify lotto wheels and systems for cover and guarantee: WheelCheck.","target":"https://saliu.com/HLINE.gif","line":94},{"title":"Forums","target":"https://forums.saliu.com/","line":96},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":96},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":96},{"title":"Contents","target":"https://saliu.com/content/index.html","line":96},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":96},{"title":"Home","target":"https://saliu.com/index.htm","line":96},{"title":"Search","target":"https://saliu.com/Search.htm","line":96},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":96},{"title":"Run free lottery software to verify the lotto wheels for missing combinations.","target":"https://saliu.com/HLINE.gif","line":98}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto wheels","lottery systems","software","lottery wheeling","verify","cover","generate wheels"],"source":"https://saliu.com/check-wheels.html","author":null}},
"smart_sources:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md": {"path":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06320326,-0.04191704,-0.00457744,-0.018243,-0.05377058,0.03292771,-0.00863651,0.00891491,0.03976492,-0.01838809,0.0001901,-0.01725286,0.04967512,-0.00328544,-0.00092065,-0.02198351,0.0549906,-0.00111482,-0.04016573,-0.00974078,0.04675993,-0.06446142,-0.07398812,-0.06703393,-0.00518975,0.04887511,-0.04624275,-0.04740074,-0.04139434,-0.25533575,-0.00346263,0.02768983,0.00805786,-0.0191888,-0.05302108,-0.01601684,-0.04720831,0.02950836,-0.06813301,0.01382715,0.00101691,0.04940546,0.01772961,0.025179,0.02305924,-0.01858622,-0.00501121,0.01797572,0.04327254,-0.01275078,-0.05102475,-0.03012642,-0.001199,0.04393015,0.02331273,0.04341769,0.03638551,0.08447231,0.02116946,0.03005795,0.06296981,0.07315405,-0.16364741,0.03660628,0.01460814,0.02888509,0.00952604,-0.08468686,-0.00641216,0.06945403,0.03338653,0.02325181,-0.03462341,0.07433919,0.01845447,0.0005221,-0.02265902,-0.06798311,-0.04590146,0.01028519,-0.04568767,-0.00545872,-0.00680156,0.01031876,-0.04189363,0.00563359,0.03076884,0.03822324,0.04712945,-0.05257608,0.02854898,0.0337333,0.09039856,0.0304599,-0.03476669,-0.0002148,0.03268507,-0.0127355,0.00079124,0.09988905,0.03149909,-0.01139631,0.0070734,0.00840093,0.0134115,-0.01890268,-0.03335026,-0.03340663,-0.03852348,0.03213765,0.09594173,0.03129444,0.09935866,-0.01341826,-0.04754294,-0.00739283,0.02142007,-0.01111749,-0.0134262,0.01814664,-0.06272762,0.01411502,0.06363274,-0.01218734,0.0046113,0.0089873,0.01300517,0.06942999,0.00527394,0.04193803,-0.01719929,-0.00915628,-0.07974746,-0.0493096,0.01098678,-0.0384144,-0.01949118,0.03511467,-0.00103104,0.01888902,-0.05157363,-0.05274257,0.03214244,-0.09856357,-0.03980192,0.01715031,0.01385283,0.0279833,0.03118373,-0.00579839,0.00146975,-0.00747509,-0.04064361,-0.02239204,0.04007599,-0.0256303,0.11949917,0.08980653,-0.08824583,0.02403288,-0.01495168,-0.04553594,-0.04458886,0.12022707,-0.0328786,-0.110179,0.00073893,0.0477723,-0.04478681,-0.09925909,-0.04512477,0.03282161,-0.06305374,0.0098817,0.10027526,-0.04009602,-0.09876142,-0.00113791,-0.01869989,-0.02104283,-0.00905167,-0.03088614,-0.05579438,0.01726355,-0.01505397,-0.12526904,0.00164881,-0.0028548,0.00194034,0.00943504,-0.01873847,0.00242531,0.00689585,0.04039577,0.0002761,-0.06104656,-0.02578585,-0.0473009,0.07044721,-0.06033867,-0.0314735,0.009679,0.04114447,0.01657753,-0.01844198,0.05720231,-0.02507218,-0.06054685,0.10010825,0.04441162,-0.0324214,0.00232874,0.02862605,0.08569112,-0.05595997,0.08819244,0.02783976,0.00980123,-0.01851253,-0.0108955,-0.00461595,0.04102279,-0.03925706,-0.21033838,-0.0536699,-0.03096836,0.06689244,0.05111425,-0.04799779,-0.01541746,-0.00203303,0.00734939,0.07072251,0.06534609,-0.04173094,0.00539655,0.03722034,-0.00278065,0.0373175,-0.13311294,-0.03919761,-0.06676968,0.07251057,-0.01774845,0.03084256,-0.02563311,-0.09345803,0.02211112,-0.04816816,0.15064842,0.03044332,0.01162422,0.00858779,0.05674588,0.00210254,-0.00358797,-0.03846645,0.03823951,0.04828063,-0.0372428,-0.01150274,-0.01219061,-0.01761491,-0.06858068,0.03062364,-0.0298607,-0.12192442,0.01088367,0.00446823,0.00727044,-0.01906107,0.00697492,0.09003034,0.03588467,0.0308142,0.02277317,0.01679143,0.0374666,-0.03084188,-0.08474506,0.00048815,-0.0254482,0.0502836,-0.02894481,-0.02694161,0.06595451,-0.0401031,0.07876511,0.01353864,-0.05042847,-0.02689513,0.0312901,-0.0109879,-0.01526078,0.08865468,-0.01039198,0.03163729,-0.02721874,0.05500996,0.05781876,-0.02317699,0.03157787,0.01877645,-0.02873654,-0.04490777,0.04266355,0.13434127,0.03954076,0.0609219,0.03651032,0.00252067,0.03240566,0.00646838,0.00018288,0.03633951,-0.01277127,-0.00627484,0.0545619,0.04807574,-0.23262045,0.00857983,-0.00330918,0.05427991,-0.06005174,-0.01842684,0.03888666,0.02507675,0.03126546,-0.04413202,0.07578343,0.02799897,0.03510825,-0.06951252,0.05105504,0.0117051,0.02327531,-0.05290934,0.03701748,-0.01113375,0.02727872,0.04353873,0.23784207,-0.02023537,-0.026237,0.03322628,-0.01869477,0.01734142,-0.01585603,0.04540327,-0.05352934,0.00051459,0.05500327,0.01880402,-0.06388925,0.02147814,-0.02682699,0.00835477,-0.00885623,0.01892484,-0.07264144,0.00170986,-0.00248512,0.02363254,0.09287311,0.02493331,-0.00416045,-0.07714684,0.05478403,0.0433115,-0.04483316,-0.05309089,-0.05930984,-0.02898775,0.01124481,0.05897628,0.02416306,-0.03752606,-0.00021311,-0.01879838,0.00244252,-0.03556693,0.06112579,0.02270024,-0.01467714],"last_embed":{"hash":"16zbjaq","tokens":477}}},"last_read":{"hash":"16zbjaq","at":1753423548510},"class_name":"SmartSource","last_import":{"mtime":1753363607266,"size":12051,"at":1753423416500,"hash":"16zbjaq"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Wheels Software, Cover, Missing Combinations, Systems":[8,99],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#{1}":[10,18],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>":[19,34],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>#{1}":[21,34],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>":[35,59],"#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>#{1}":[37,59],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling":[60,99],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{1}":[62,63],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{2}":[64,64],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{3}":[65,65],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{4}":[66,66],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{5}":[67,68],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{6}":[69,70],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{7}":[71,72],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{8}":[73,74],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{9}":[75,75],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{10}":[76,77],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{11}":[78,78],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{12}":[79,79],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{13}":[80,80],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{14}":[81,81],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{15}":[82,82],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{16}":[83,83],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{17}":[84,84],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{18}":[85,85],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{19}":[86,86],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{20}":[87,87],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{21}":[88,88],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{22}":[89,89],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{23}":[90,90],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{24}":[91,91],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{25}":[92,93],"#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{26}":[94,99]},"outlinks":[{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":40},{"title":"Verify, check lotto wheels and generate lotto-6 systems.","target":"https://saliu.com/HLINE.gif","line":56},{"title":"\n\n## Resources in Lottery Software, Systems, Lotto Wheeling\n\n","target":"https://saliu.com/content/lottery.html","line":58},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":64},{"title":"_**Lotto, Lottery, Software**_","target":"https://saliu.com/LottoWin.htm","line":78},{"title":"_**Lotto Wheels for Lotto Games Drawing 5, 6, 7 Numbers**_","target":"https://saliu.com/lotto_wheels.html","line":80},{"title":"_**Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":82},{"title":"_**Design Lotto Wheels Manually, Lotto Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":84},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":85},{"title":"_**Best On-The-Fly Wheeling Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":87},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":88},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":89},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":90},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":91},{"title":"**Lotto Wheels Software**","target":"https://saliu.com/infodown.html","line":92},{"title":"The best software to verify lotto wheels and systems for cover and guarantee: WheelCheck.","target":"https://saliu.com/HLINE.gif","line":94},{"title":"Forums","target":"https://forums.saliu.com/","line":96},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":96},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":96},{"title":"Contents","target":"https://saliu.com/content/index.html","line":96},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":96},{"title":"Home","target":"https://saliu.com/index.htm","line":96},{"title":"Search","target":"https://saliu.com/Search.htm","line":96},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":96},{"title":"Run free lottery software to verify the lotto wheels for missing combinations.","target":"https://saliu.com/HLINE.gif","line":98}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto wheels","lottery systems","software","lottery wheeling","verify","cover","generate wheels"],"source":"https://saliu.com/check-wheels.html","author":null}},"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05025764,-0.03939613,0.00373984,-0.02578526,-0.06161135,0.03601616,0.00581222,0.00834081,0.03975477,-0.02456927,0.00169094,-0.01327464,0.03977768,-0.00021359,-0.01080826,-0.01462023,0.05665469,-0.0027998,-0.03505094,-0.0141596,0.03945014,-0.06727663,-0.06992495,-0.05963501,-0.0055567,0.04605806,-0.05755235,-0.04337705,-0.04553866,-0.24920505,0.00185626,0.02209548,0.02393191,-0.01745961,-0.0463005,-0.02028907,-0.0515216,0.03339729,-0.0834351,0.00895577,0.00812264,0.04683511,0.02216568,0.02629329,0.02305157,-0.03402805,0.00324872,0.03385761,0.04002658,-0.01176664,-0.05615452,-0.03306561,-0.00413197,0.04161609,0.0212622,0.03611695,0.03931923,0.09265936,0.0348154,0.02993636,0.06357144,0.07756298,-0.15997496,0.04203094,0.01462852,0.02913648,0.0108064,-0.08042098,-0.01254238,0.07498457,0.03154076,0.01810423,-0.03423483,0.07549597,0.0191428,0.00334556,-0.02418938,-0.06559448,-0.0453897,0.01956517,-0.04758317,0.00280702,-0.00421606,0.006355,-0.04915652,0.00552197,0.02492606,0.03266017,0.05083411,-0.03890176,0.03140841,0.0479928,0.07855671,0.0222741,-0.03082683,-0.00520639,0.03206425,-0.01649474,0.00214069,0.10521971,0.03143954,-0.01036728,-0.00331412,0.00708303,0.01312918,-0.01800572,-0.04123405,-0.03140225,-0.03408615,0.03128085,0.09436821,0.02923677,0.10329947,-0.00390271,-0.04079793,-0.00616257,0.01094238,-0.01724729,-0.0174484,0.0307572,-0.06589647,0.01965247,0.05748802,-0.00979553,0.00592736,0.00260863,0.00635123,0.07264307,0.00568408,0.0372656,-0.01881032,-0.00423263,-0.08215745,-0.04120719,0.00274329,-0.04435658,-0.00679529,0.03631555,-0.00091835,0.02572045,-0.05727922,-0.05627072,0.03480233,-0.09643402,-0.03338451,0.01737717,0.01450475,0.0299348,0.03940535,-0.00487516,0.01183782,-0.003804,-0.04794303,-0.02283679,0.04279589,-0.02225574,0.11493465,0.09080305,-0.10291125,0.01783836,-0.02337689,-0.03970563,-0.03325891,0.11589295,-0.0308325,-0.09987384,0.0020308,0.0428846,-0.04949653,-0.10871848,-0.04335093,0.03624608,-0.07715411,0.01157347,0.09344115,-0.03816374,-0.10572225,-0.00577835,-0.00848255,-0.02297935,-0.00387882,-0.0258377,-0.04526764,0.01978889,-0.00196564,-0.11104251,0.00509651,-0.00659176,-0.00078655,0.00972713,-0.02199247,0.00750456,0.00736758,0.0366368,-0.00411267,-0.06786715,-0.02471917,-0.04439611,0.07889955,-0.06218149,-0.02554239,0.00413081,0.04097033,0.03124111,-0.01127649,0.05250657,-0.03604134,-0.06026019,0.10134854,0.04490013,-0.02880731,0.00113128,0.02827515,0.08900297,-0.05720954,0.09363019,0.03312688,0.01479949,-0.02990227,-0.00876262,0.00350223,0.03520273,-0.02551055,-0.21806002,-0.05547433,-0.02355552,0.06854381,0.05192297,-0.04006804,-0.02083737,-0.00740402,0.01637563,0.07219375,0.06015592,-0.04803552,0.00954521,0.03717239,-0.00194454,0.04193284,-0.13706416,-0.0421537,-0.06036654,0.06814124,-0.02280689,0.01687637,-0.03022755,-0.09814907,0.02439742,-0.03506782,0.14896464,0.02929925,0.01479781,0.00129716,0.05328619,0.00888401,0.00017897,-0.03407596,0.0341578,0.05300424,-0.04018812,-0.00701857,-0.01075618,-0.00916133,-0.06695502,0.02773516,-0.0226534,-0.12185756,0.0021645,0.00811982,0.00844108,-0.01645308,0.01168664,0.08167633,0.0369152,0.04157123,0.0242225,0.01898095,0.0406034,-0.03924958,-0.07972629,0.00988842,-0.02130999,0.05214597,-0.0238851,-0.03089516,0.05179241,-0.03360634,0.08397821,0.00732349,-0.04697385,-0.02300323,0.03692125,-0.01173119,-0.01581435,0.07302937,-0.01480496,0.02939736,-0.02642193,0.06030687,0.0649354,-0.03144887,0.03242117,0.01839434,-0.03573788,-0.05192286,0.04131554,0.13216619,0.03501178,0.05132647,0.04016345,0.00911545,0.0251285,0.00913088,0.00768074,0.03729213,-0.01915233,-0.00965701,0.04049966,0.05690279,-0.23369631,0.00855294,-0.00992952,0.05604567,-0.07441477,-0.02411419,0.03501577,0.02824394,0.03022735,-0.04000154,0.07919931,0.02506467,0.04423008,-0.07150988,0.05523559,0.00957919,0.03072153,-0.05789485,0.03877793,-0.00539064,0.03205563,0.04569039,0.22378971,-0.02500956,-0.02793846,0.02792436,-0.01169853,0.01702999,-0.02681081,0.03744732,-0.05694472,-0.01152448,0.0682102,0.01325188,-0.05878711,0.0184599,-0.02519692,0.00950287,-0.00495396,0.02534094,-0.06403012,0.00367351,-0.00373587,0.01798289,0.09544649,0.03422031,-0.01265066,-0.07636783,0.04878253,0.0439849,-0.05112584,-0.05856534,-0.05445927,-0.034874,0.00394282,0.05891436,0.02000729,-0.03876334,-0.00686681,-0.0255012,-0.00645454,-0.04318588,0.055716,0.01147753,-0.0293542],"last_embed":{"hash":"nf3zn9","tokens":412}}},"text":null,"length":0,"last_read":{"hash":"nf3zn9","at":1753423547357},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems","lines":[8,99],"size":11842,"outlinks":[{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":33},{"title":"Verify, check lotto wheels and generate lotto-6 systems.","target":"https://saliu.com/HLINE.gif","line":49},{"title":"\n\n## Resources in Lottery Software, Systems, Lotto Wheeling\n\n","target":"https://saliu.com/content/lottery.html","line":51},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":57},{"title":"_**Lotto, Lottery, Software**_","target":"https://saliu.com/LottoWin.htm","line":71},{"title":"_**Lotto Wheels for Lotto Games Drawing 5, 6, 7 Numbers**_","target":"https://saliu.com/lotto_wheels.html","line":73},{"title":"_**Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":75},{"title":"_**Design Lotto Wheels Manually, Lotto Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":77},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":78},{"title":"_**Best On-The-Fly Wheeling Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":80},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":81},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":82},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":83},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":84},{"title":"**Lotto Wheels Software**","target":"https://saliu.com/infodown.html","line":85},{"title":"The best software to verify lotto wheels and systems for cover and guarantee: WheelCheck.","target":"https://saliu.com/HLINE.gif","line":87},{"title":"Forums","target":"https://forums.saliu.com/","line":89},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":89},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":89},{"title":"Contents","target":"https://saliu.com/content/index.html","line":89},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":89},{"title":"Home","target":"https://saliu.com/index.htm","line":89},{"title":"Search","target":"https://saliu.com/Search.htm","line":89},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":89},{"title":"Run free lottery software to verify the lotto wheels for missing combinations.","target":"https://saliu.com/HLINE.gif","line":91}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07142826,-0.04083547,0.01349364,-0.04017365,-0.07331259,0.05041789,-0.01040675,-0.00216756,0.02728491,-0.03448615,0.02153312,-0.01601796,0.0499612,-0.02388147,-0.02779184,-0.02148554,0.06237014,0.02309631,-0.04998682,-0.03138752,0.0381016,-0.03284097,-0.07076099,-0.0727557,-0.00439718,0.05325795,-0.03762191,-0.05784566,-0.04214465,-0.22475587,0.00977012,0.03192142,0.02391108,-0.01063392,-0.02186366,-0.02500631,-0.0608529,0.03572056,-0.09570363,0.01586917,0.00302891,0.04469841,0.03585596,0.01037202,0.01722536,-0.0416214,-0.00294515,0.01738748,0.06152794,-0.02413458,-0.03722466,-0.04588229,0.01196705,0.03947209,0.01368608,0.04506944,0.05148295,0.09358831,0.01699751,0.03790845,0.06211865,0.05960485,-0.17872384,0.01808121,0.02549547,0.03108965,0.02218155,-0.08357893,0.00527212,0.10747541,0.04396408,0.01291939,-0.02609062,0.0829557,0.02158877,-0.01052433,-0.04624225,-0.07916959,-0.03917437,-0.00320162,-0.0346843,-0.00056556,0.01121747,0.03112353,-0.05177812,0.00983776,0.05263352,0.02493576,0.05031988,-0.04103274,0.03389887,0.01618309,0.0699784,0.04612591,-0.05539824,-0.01395162,0.05469967,-0.00222934,0.01047761,0.1281701,-0.00763277,-0.01577251,-0.00179775,0.01029465,0.00803604,-0.04072564,-0.02387469,-0.01993726,-0.05295472,0.03044075,0.0980432,0.02878154,0.09347223,-0.02319778,-0.05669169,-0.02657076,0.012956,0.00252963,-0.01988071,0.0312829,-0.01111372,0.0069376,0.05895142,-0.02297041,0.01420886,0.01331853,0.00657591,0.06792738,-0.0113098,0.05782027,-0.00288924,0.01225059,-0.07090164,-0.04162605,0.04416116,-0.04469834,-0.019082,0.03090745,-0.032362,0.00957627,-0.04510816,-0.04882821,0.06201924,-0.08475438,-0.04876516,0.04545864,-0.02449539,0.02196913,0.00123992,-0.0177743,-0.00602138,-0.00092779,-0.05756751,-0.01524856,0.04642904,0.00027984,0.0970137,0.03960979,-0.09066773,0.03475576,-0.02138432,-0.01358474,-0.04964834,0.11660033,-0.03661524,-0.08887444,-0.01094488,0.06451234,-0.02342323,-0.08887279,-0.03736826,0.03846919,-0.07986116,0.01696986,0.09287693,-0.03158728,-0.09301895,0.02738796,-0.02892464,-0.04219708,0.0166668,-0.01962166,-0.04187036,0.02618463,-0.03399576,-0.08940517,0.01977315,-0.01109414,0.01959697,0.02188577,-0.03250377,-0.01346121,0.01922801,0.01267116,-0.00746181,-0.04953197,-0.07499588,-0.04118026,0.06989282,-0.07440387,0.01561228,0.01658958,0.06417715,0.03522676,0.00414692,0.04290631,-0.04197965,-0.05397337,0.09147819,0.06712869,-0.04412745,0.00150646,0.02980319,0.05415893,-0.04334393,0.07647913,0.00976575,0.02933909,-0.02528975,-0.0087942,0.00984515,0.02848613,-0.03937965,-0.20069645,-0.05424454,-0.00385541,0.08633298,0.04730516,-0.04927735,-0.01628838,0.00022869,0.00275887,0.06821547,0.06621635,-0.02569627,-0.01632426,0.05257663,-0.00659334,0.05204817,-0.11619397,-0.05020866,-0.06823905,0.07888202,0.00684796,0.01207896,-0.05462176,-0.09465513,0.02485943,-0.0340721,0.134101,-0.02155279,0.01597713,-0.00287849,0.06550597,0.00654724,-0.02264223,0.00898778,0.06365979,0.06913279,-0.08055212,-0.03119157,-0.00175596,-0.0305296,-0.06246772,0.0229415,-0.02430118,-0.11358795,0.03038008,-0.00708803,0.00772547,-0.02200814,0.01094444,0.06162006,0.0174984,0.06448361,0.00763045,0.02279341,0.04245298,-0.02862578,-0.08853055,0.01844434,-0.03933581,0.05632107,-0.02692644,-0.03255245,0.04772645,-0.03312906,0.08507472,0.00862506,-0.05492864,-0.02212785,0.00942503,-0.00750476,-0.0014671,0.05434299,-0.03212618,0.06293648,-0.04530346,0.04798967,0.04167339,0.01248598,0.00645936,0.00132163,-0.00519767,-0.04898011,0.04156434,0.11683467,0.05997026,0.04706027,0.03157201,0.02216829,0.02125514,0.02230447,0.02134752,0.03144315,-0.01751094,-0.00395536,0.04381762,0.03133173,-0.23199695,0.02553465,-0.01661252,0.05663499,-0.06747649,-0.0012808,0.02196834,0.04726012,0.03887518,-0.04477969,0.07447728,0.02669011,0.0246961,-0.06955098,0.04711248,0.03829083,0.02906465,-0.06371114,0.05082322,-0.01708208,0.03891479,0.01622787,0.22198837,-0.01595209,0.01512471,0.04255426,-0.02062121,0.01002064,-0.00558216,0.0149028,-0.02866538,-0.02630862,0.07643677,-0.01583619,-0.05974109,0.02598722,-0.04254449,0.00764484,0.00094613,0.02978314,-0.0733887,-0.00988685,-0.03030632,0.0053291,0.09964115,0.02860135,-0.01796217,-0.09659453,0.0419645,0.05868594,-0.02897627,-0.03203806,-0.07072451,-0.05121841,0.02355457,0.04603612,0.01336991,-0.02640015,-0.01422339,-0.0276347,0.00652967,-0.0373721,0.05258438,-0.00004389,-0.02846698],"last_embed":{"hash":"rlwebm","tokens":128}}},"text":null,"length":0,"last_read":{"hash":"rlwebm","at":1753423547501},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#{1}","lines":[10,18],"size":369,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04725356,-0.03625097,0.00362707,-0.01309265,-0.04870487,0.02066647,0.01244547,0.01326061,0.04839391,-0.02359385,-0.00955406,-0.00240509,0.03435331,0.01462879,0.00872856,-0.00214268,0.04362937,-0.00072234,-0.05002722,0.00654959,0.04507077,-0.08090291,-0.07721936,-0.06898557,0.00327607,0.06229148,-0.05607422,-0.0314681,-0.03455364,-0.26623297,-0.00138545,0.01562335,0.01309668,-0.02223629,-0.05566266,-0.0150164,-0.04955004,0.04331618,-0.06557541,0.00826962,0.00873648,0.04134714,0.01865488,0.02823431,0.02466893,-0.01977156,0.00437748,0.0227464,0.0268059,-0.00631057,-0.04125668,-0.02876809,-0.00519717,0.02121712,0.02653099,0.02804683,0.03699448,0.06803796,0.027266,0.03052116,0.05310399,0.07314081,-0.152169,0.04708837,-0.00041662,0.02468803,-0.00014438,-0.07607409,-0.0156407,0.06363831,0.03166417,0.01948916,-0.03673699,0.07165764,0.01422804,-0.00478333,-0.01108996,-0.05880933,-0.04840533,0.03357493,-0.05678972,-0.01496412,-0.00499676,-0.00812704,-0.03663214,-0.01098437,0.01214991,0.02930895,0.03959896,-0.04088637,0.03183743,0.0602683,0.06954994,0.01293277,-0.0256793,0.00329386,0.02433309,-0.02638514,-0.00628477,0.10045702,0.0560098,-0.01513309,0.01472064,0.01316344,0.00757638,-0.00149486,-0.0409725,-0.02841504,-0.03659429,0.0260217,0.08553541,0.02342888,0.08446769,0.00451074,-0.03054346,0.00530742,0.01008277,-0.02800749,-0.00997914,0.02559548,-0.08534725,0.00627177,0.05713357,-0.00100112,-0.00021775,0.00088371,0.0094375,0.07962541,0.00372718,0.01656887,-0.02459343,-0.01503939,-0.09094458,-0.04419139,-0.0048567,-0.01534402,0.00972242,0.05036857,0.0140947,0.01179661,-0.04719475,-0.06360276,0.02912507,-0.10793501,-0.02909206,0.01888885,0.02288353,0.02476895,0.03781736,-0.00916369,0.00740759,-0.01929021,-0.0412584,-0.02142549,0.02249318,-0.03336971,0.11624277,0.10231438,-0.09472097,0.01028834,-0.04691469,-0.0433689,-0.02054474,0.13613413,-0.02769279,-0.09199534,0.00735889,0.0172228,-0.06184549,-0.10845302,-0.0444107,0.0282002,-0.06758758,0.01200869,0.10698588,-0.02816832,-0.10772175,-0.02170563,-0.00627158,-0.01447946,-0.02353019,-0.02325429,-0.04798264,0.01676748,0.01220773,-0.13167495,0.00018922,0.00907972,-0.00103419,0.00204486,-0.01704495,0.00661024,0.00462669,0.04849898,0.00775999,-0.06362693,0.00566587,-0.05312878,0.06553011,-0.05258409,-0.0357949,0.0071262,0.03049299,0.02467633,-0.02675298,0.05611242,-0.01980705,-0.05857511,0.09647673,0.03541076,-0.04000171,-0.00640553,0.01910625,0.10279588,-0.06391586,0.09244994,0.0420946,0.00598099,-0.0171627,-0.01513291,0.0110999,0.0264874,-0.0214165,-0.21540816,-0.05111361,-0.0328939,0.05185305,0.05261599,-0.0405898,-0.01477076,0.00106656,0.0204035,0.07758026,0.06723277,-0.0667514,0.00972361,0.04002957,-0.01016652,0.02885496,-0.13045241,-0.04228741,-0.06852115,0.06957161,-0.02380851,0.02984176,-0.02826438,-0.08087689,0.03380581,-0.04023474,0.15182143,0.04690805,0.00688167,-0.00449319,0.04951554,0.00733422,-0.00868751,-0.03764874,0.02638498,0.02879311,-0.02408302,-0.01879303,-0.02613985,-0.01202691,-0.05937532,0.02821776,-0.03231501,-0.11262891,-0.00101111,0.01249823,0.00705837,-0.00903906,0.00862853,0.09979679,0.04466954,0.01048059,0.04329858,0.01610538,0.04289039,-0.03279841,-0.06829915,0.00066849,-0.00703625,0.04787151,-0.02758587,-0.0194707,0.05985983,-0.04655794,0.0882537,0.0161719,-0.03500267,-0.04753313,0.04162128,-0.02104174,-0.02299371,0.07775717,-0.00547402,0.01585366,-0.02518298,0.05136092,0.07991756,-0.02905838,0.04996718,0.02942564,-0.03199333,-0.04434359,0.02936038,0.13668583,0.02591391,0.04581895,0.03763044,-0.00993446,0.03128066,0.00852289,-0.00340054,0.04473295,-0.00926354,0.0088177,0.03813164,0.05069293,-0.23727256,0.00883443,-0.01207953,0.05662708,-0.05572264,-0.03277121,0.03467032,0.02106954,0.01813091,-0.05098609,0.07694077,0.04035348,0.0401683,-0.06988348,0.04492826,-0.02912946,0.03666416,-0.0390526,0.04664725,-0.01093891,0.03589335,0.05543549,0.24292621,-0.02247912,-0.03711898,0.01762858,-0.01189653,0.00600464,-0.0306555,0.04951135,-0.06014768,0.01037053,0.04944199,0.02862897,-0.04938524,0.02496239,-0.00047311,0.00709854,-0.01097514,0.0200005,-0.05337817,0.00848127,-0.00527665,0.02414376,0.09254643,0.02977265,-0.0143661,-0.06433263,0.05072613,0.03192113,-0.04939381,-0.06617969,-0.04629297,-0.02771625,-0.00152425,0.05546852,0.0264869,-0.03130442,-0.00237409,0.00085842,-0.00236347,-0.05096357,0.05286403,0.03657207,-0.01264295],"last_embed":{"hash":"o1n5d7","tokens":453}}},"text":null,"length":0,"last_read":{"hash":"o1n5d7","at":1753423547541},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>","lines":[19,34],"size":2761,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04441116,-0.03448583,0.00465068,-0.01406604,-0.04771632,0.02302659,0.01381649,0.00984162,0.04822993,-0.02430653,-0.01205205,-0.00266413,0.03104805,0.01293219,0.00944083,-0.00085108,0.04164015,-0.00226738,-0.04844357,0.00601096,0.04468703,-0.07879584,-0.07679393,-0.06485406,0.00285603,0.06223617,-0.05531089,-0.02978695,-0.03392669,-0.26449865,-0.00237412,0.01536019,0.01013692,-0.02247039,-0.05567106,-0.01486732,-0.05072662,0.04429679,-0.06690591,0.00758252,0.00729384,0.04208956,0.0185355,0.02849801,0.02278297,-0.02034797,0.00405939,0.02098389,0.02544627,-0.00554968,-0.03961599,-0.0291912,-0.00443274,0.02308165,0.02298025,0.02794231,0.03556827,0.06736973,0.0292122,0.03087248,0.05565745,0.0727236,-0.15333955,0.04814279,-0.00298441,0.02592943,0.00185105,-0.07519835,-0.01558008,0.0657122,0.0305665,0.01556865,-0.03684959,0.07059795,0.01432765,-0.00449261,-0.01104303,-0.06153894,-0.04859816,0.03640473,-0.05875927,-0.01359384,-0.00554783,-0.00457048,-0.03774625,-0.01251616,0.01126213,0.02932048,0.04053887,-0.03988089,0.03157124,0.06279556,0.06703173,0.0130687,-0.02507475,0.00308744,0.02165922,-0.02643509,-0.00327625,0.10099869,0.05684654,-0.01219452,0.01279224,0.01183674,0.00684735,-0.00192853,-0.04290511,-0.03005557,-0.03626066,0.02526977,0.08726886,0.02478279,0.08480557,0.0060749,-0.02987563,0.00434768,0.01177061,-0.029177,-0.01128595,0.02694321,-0.08742171,0.00597325,0.05692407,-0.00331769,-0.0014851,0.0008701,0.01101645,0.08082782,0.00620636,0.0137702,-0.02709331,-0.01797176,-0.08983541,-0.044087,-0.00279139,-0.01523039,0.00844937,0.05120943,0.01483634,0.01414174,-0.04805536,-0.06597164,0.02841791,-0.10704615,-0.03067914,0.01703365,0.02282719,0.02422724,0.03790135,-0.00876513,0.00814389,-0.02032058,-0.04266446,-0.01866577,0.02190317,-0.03245435,0.11306875,0.10372252,-0.09519136,0.00917203,-0.04610828,-0.04263737,-0.01852402,0.13417511,-0.02856741,-0.08998642,0.01049636,0.01812215,-0.05918882,-0.10985369,-0.04517784,0.03058798,-0.06898563,0.01136448,0.10613821,-0.02723451,-0.11075637,-0.01962749,-0.00661544,-0.01634004,-0.01956199,-0.02659764,-0.04785355,0.01961154,0.01135326,-0.13145059,0.0002492,0.01326834,-0.00227277,0.00150496,-0.01341563,0.00834305,0.00579376,0.04782218,0.00979455,-0.06634809,0.00809839,-0.05140855,0.06552117,-0.05511477,-0.03763824,0.00608831,0.02982096,0.02782642,-0.02623173,0.05512844,-0.0198053,-0.05756701,0.10075651,0.03119326,-0.0419164,-0.0069005,0.01889716,0.10207085,-0.06327163,0.09143928,0.0421087,0.00837344,-0.01766026,-0.0183751,0.01129971,0.02755665,-0.01710791,-0.2162441,-0.05115578,-0.02949069,0.05548723,0.05403503,-0.03829274,-0.01767784,0.00035365,0.01898556,0.07693044,0.0656139,-0.06410857,0.00947346,0.03923634,-0.01054532,0.03001736,-0.13350545,-0.04220327,-0.06726371,0.06777357,-0.02671039,0.03096513,-0.03117999,-0.08085491,0.03431784,-0.03819551,0.15154296,0.04470853,0.00702564,-0.00440721,0.04921007,0.00580579,-0.00770486,-0.03461409,0.02929438,0.02677448,-0.02258545,-0.01939395,-0.02408233,-0.01325439,-0.05934796,0.02842498,-0.03536221,-0.11408606,0.00089339,0.01521775,0.00663624,-0.00619979,0.00821826,0.09954638,0.04424737,0.0080332,0.04372699,0.01482024,0.04284279,-0.03495521,-0.06858587,0.00203494,-0.00548972,0.04963794,-0.02642011,-0.02005218,0.05909646,-0.04843413,0.09090172,0.01506705,-0.03639829,-0.04842243,0.04324271,-0.02066362,-0.02383132,0.07746214,-0.00815742,0.01502515,-0.02576657,0.0511634,0.08003981,-0.03182908,0.04982892,0.02874205,-0.02785723,-0.04341504,0.02651944,0.13585579,0.02575552,0.04410839,0.03649314,-0.00914697,0.03511085,0.00743959,-0.006775,0.04390581,-0.00713058,0.00836433,0.03706968,0.05049076,-0.23756513,0.00824257,-0.01308919,0.05607615,-0.05658497,-0.03213527,0.03638241,0.02478724,0.01862594,-0.05176211,0.07807609,0.03983967,0.04269483,-0.06925913,0.04657249,-0.02982089,0.03794084,-0.0393617,0.04670673,-0.01028209,0.03895519,0.05393425,0.24138758,-0.0224174,-0.03675764,0.01807339,-0.01245203,0.00800265,-0.03130382,0.04818575,-0.06286526,0.00989819,0.04977384,0.02704187,-0.04934657,0.0260407,0.00269447,0.00704456,-0.01039359,0.01832962,-0.05323878,0.00833823,-0.00536551,0.0214038,0.09111489,0.0279118,-0.01083487,-0.06486908,0.04880461,0.03016894,-0.04743065,-0.06219171,-0.04722644,-0.02955489,-0.00078276,0.05558865,0.02502835,-0.03263059,-0.00116144,0.00058839,-0.00443352,-0.04935577,0.05491757,0.03882777,-0.01379092],"last_embed":{"hash":"1f9tjm8","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"1f9tjm8","at":1753423547724},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>#{1}","lines":[21,34],"size":2653,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06853774,-0.0416308,-0.02366756,-0.04743639,-0.07358956,0.0621742,-0.0358454,-0.00082806,0.01644825,-0.01191093,0.01087141,-0.01401864,0.06328933,-0.03143845,-0.021907,-0.02110378,0.04209288,0.01614108,-0.0833451,-0.01462067,0.05688465,-0.07001793,-0.08605838,-0.08913169,0.0094699,0.04157616,-0.02320847,-0.03333434,-0.04460583,-0.2278832,0.0308715,0.02185951,0.0450773,-0.03629202,-0.04723446,-0.01424361,-0.06213088,0.04121906,-0.08302565,0.01369001,-0.01560914,0.03601521,0.03607741,0.02244136,0.0338073,-0.03681309,-0.0014057,0.00355564,0.09332264,0.00100494,-0.04001609,-0.05164854,0.02778097,0.016822,0.01846067,0.02295201,0.02992827,0.08244096,0.00253893,0.02390469,0.03454278,0.0749291,-0.16449396,0.02493731,-0.02054554,0.03098284,-0.00512908,-0.06389402,0.04624998,0.06058498,0.01047909,-0.00436908,-0.01409699,0.09853458,0.01841674,-0.0345738,-0.05601626,-0.05458216,-0.02244115,0.00640051,-0.06242106,-0.03236832,0.00503598,0.04182807,-0.02446318,0.02241232,0.04314113,0.04527934,0.03643053,-0.05726169,0.02936635,0.02034448,0.03994188,0.04209911,-0.04266525,-0.01874295,0.02488238,-0.03369143,-0.01464834,0.11048839,0.03148029,-0.00827076,0.02737776,0.0113555,-0.02406899,-0.04492364,-0.03133337,-0.00466611,-0.05365855,0.01353699,0.10258886,0.02386332,0.04035217,-0.03164348,-0.06024006,-0.01437908,-0.0002243,0.01379937,-0.02855576,0.02717914,-0.01360696,-0.00839562,0.02292015,-0.01611531,0.02367784,0.01431554,0.01608392,0.05263324,0.00630748,0.05320686,0.00934176,0.03218934,-0.10606258,-0.03031226,0.03984375,-0.0067237,-0.04023102,0.02773875,-0.01035486,0.01266142,-0.0306869,-0.03912613,0.03143228,-0.0922309,-0.00613921,0.04474299,-0.02517513,0.0420269,0.00921977,-0.03599915,-0.01871143,-0.03715667,-0.05906879,-0.03342648,0.02741264,0.01207417,0.08581713,0.04784221,-0.06229251,0.01540741,-0.05094544,-0.03642846,-0.02687027,0.13213529,-0.03873601,-0.10775711,0.00033391,0.06146133,-0.0235879,-0.09519433,-0.01650749,0.003381,-0.02348627,0.02596938,0.10051595,-0.03208995,-0.11059249,0.02923826,-0.01918045,-0.01790296,0.01041973,-0.03273747,-0.04912128,0.02086996,-0.03372196,-0.11253576,0.03691222,-0.00200779,0.00760535,0.01036525,-0.01248571,0.00678974,0.00930611,0.01420621,-0.01330609,-0.03317551,-0.03824589,-0.04088913,0.05618852,-0.03491226,0.05470935,0.03305133,0.04097808,0.02528715,-0.03011577,0.04996848,-0.03214924,-0.03244684,0.1068194,0.07049364,-0.05641107,0.01697048,0.06161197,0.06173797,-0.05465773,0.07361518,0.0105904,0.01844412,0.0307591,-0.01692286,0.02571587,0.00771254,-0.04349731,-0.19444917,-0.03480773,-0.01188611,0.06711425,0.04341443,-0.05029893,-0.01940711,-0.0249752,0.00263043,0.06020136,0.12535803,-0.05368595,-0.01002754,0.06837492,-0.02433325,0.02903568,-0.10142463,-0.04364314,-0.05365577,0.06421963,0.01182868,0.01947006,-0.01742641,-0.08437476,0.04039584,-0.04942668,0.15400231,-0.02129733,0.02086265,0.01472266,0.10182898,-0.02675894,-0.01505138,-0.0002613,0.02202787,0.04367854,-0.06408168,0.00108663,-0.03384474,-0.00743442,-0.02316991,0.02865415,-0.04048871,-0.1207517,0.0202709,-0.01704277,-0.00908858,-0.00635432,0.00064627,0.05608019,0.00763989,0.02078371,0.02785421,0.01028076,0.07317803,-0.03477561,-0.07394904,-0.0015095,-0.0227764,0.00833484,-0.03544028,-0.04775058,0.06936073,-0.0538624,0.05747817,0.02841054,-0.0416279,-0.04224903,0.01049545,-0.00362779,-0.00735564,0.06365816,-0.01407497,0.07816657,-0.04967135,0.02159383,0.02639049,0.01120527,-0.00232424,-0.02369503,-0.00871716,-0.04331253,0.01360199,0.11660972,0.05216627,0.05151189,0.06172676,0.04369428,0.03691699,-0.00734732,0.00687884,0.03491556,-0.01184392,0.05412836,0.04531358,-0.00955632,-0.25030321,0.03704529,-0.06538617,0.07666764,-0.03560818,-0.02574247,0.05749409,0.06420811,0.03537041,-0.05004259,0.05381572,0.04620275,0.02378142,-0.06599038,0.04921336,0.01426924,0.00233343,-0.04614997,0.03382329,-0.04121755,0.07642406,0.01570386,0.227768,-0.01813678,0.00880808,0.04913931,-0.00666162,0.02254586,-0.00026178,0.04562771,-0.03212988,-0.01363164,0.04826824,-0.00321883,-0.03982076,0.01809759,-0.04419242,0.00598851,-0.02537172,0.02953536,-0.07168636,-0.01590523,-0.00549672,0.01630025,0.10127107,0.01695446,-0.02301741,-0.0946697,0.05342519,0.06250938,-0.03011379,-0.00683276,-0.05838599,-0.043523,0.02632325,0.03926441,0.02720617,0.01320587,0.01794819,0.02168767,0.03046784,-0.05203533,0.06776461,0.01376482,-0.02470918],"last_embed":{"hash":"1xcruea","tokens":444}}},"text":null,"length":0,"last_read":{"hash":"1xcruea","at":1753423547879},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>","lines":[35,59],"size":4550,"outlinks":[{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":6},{"title":"Verify, check lotto wheels and generate lotto-6 systems.","target":"https://saliu.com/HLINE.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06701429,-0.04091636,-0.02136592,-0.04565391,-0.07455648,0.06218963,-0.03533071,-0.00069631,0.01617576,-0.01401614,0.01056229,-0.01482093,0.06343958,-0.02947817,-0.02210535,-0.02051811,0.04061472,0.01832908,-0.08144167,-0.01346423,0.05693778,-0.07139279,-0.08685939,-0.09137549,0.00869524,0.04382265,-0.02151209,-0.03072366,-0.04282345,-0.22784077,0.03009416,0.01978795,0.0431563,-0.03598323,-0.04712663,-0.01475031,-0.05985516,0.04163188,-0.08273456,0.01419425,-0.01551258,0.03505698,0.03526392,0.02266483,0.03378327,-0.03720323,-0.00100269,0.00244528,0.0944701,0.00284979,-0.03954763,-0.05228354,0.02498417,0.01496295,0.0186796,0.02279099,0.02813101,0.08285249,0.00417178,0.02494578,0.03259701,0.07644907,-0.16385071,0.02619628,-0.02081728,0.03161353,-0.00412501,-0.06413811,0.04711013,0.0619606,0.01061088,-0.00723264,-0.01400203,0.10037279,0.02037062,-0.03361569,-0.05635542,-0.05540169,-0.02218993,0.00539472,-0.06451134,-0.03221067,0.00554428,0.0405781,-0.02396796,0.02195793,0.04283102,0.0468131,0.0368486,-0.05696681,0.02938578,0.01935062,0.04068879,0.0431812,-0.04238513,-0.02028417,0.02428514,-0.03159915,-0.01405967,0.10917624,0.03288208,-0.00713564,0.02631645,0.00989857,-0.02452858,-0.04163577,-0.03128172,-0.00249765,-0.05590038,0.0150317,0.10124226,0.02528252,0.03817696,-0.03071824,-0.05888088,-0.01546134,-0.00071338,0.01449409,-0.0311416,0.02736653,-0.01355987,-0.01003749,0.02384703,-0.01633716,0.02295423,0.01309247,0.01660586,0.05438144,0.0032699,0.05062346,0.00802464,0.0311321,-0.10716336,-0.03039104,0.04059981,-0.00831858,-0.03840375,0.02870239,-0.0120255,0.01289034,-0.03175838,-0.03732941,0.03060978,-0.0945496,-0.00639882,0.04249905,-0.02197599,0.03922967,0.01063652,-0.03597867,-0.01833527,-0.03439457,-0.05974956,-0.03385134,0.02729776,0.0113892,0.08561449,0.04791604,-0.0601722,0.01298878,-0.05245472,-0.03598217,-0.02754286,0.12922856,-0.03911059,-0.10642818,0.00003475,0.06358878,-0.02388039,-0.09435384,-0.01576535,0.00167207,-0.02388279,0.02501101,0.1022049,-0.03060848,-0.11300809,0.03100475,-0.02254487,-0.01696537,0.01088842,-0.03173001,-0.04948395,0.02065978,-0.03323933,-0.11401714,0.03850123,-0.00059956,0.00733941,0.00918479,-0.01361029,0.00948474,0.00872477,0.01694224,-0.01107927,-0.03198225,-0.03770547,-0.0388491,0.05593655,-0.0349224,0.05600884,0.03125707,0.0395728,0.02352301,-0.02867616,0.04792858,-0.03410663,-0.02905524,0.10692178,0.0707448,-0.05802687,0.01687662,0.06007973,0.05963996,-0.054075,0.07429768,0.01088601,0.02202084,0.03129338,-0.01971108,0.02632962,0.00495369,-0.04199987,-0.19344799,-0.03283466,-0.01284234,0.0689973,0.04353879,-0.0503821,-0.02000251,-0.02294543,0.0018363,0.05891117,0.126404,-0.05293993,-0.00986458,0.0677017,-0.02491536,0.0296628,-0.10075996,-0.04264987,-0.05300983,0.06256573,0.01229747,0.02029064,-0.01598921,-0.08531949,0.03873188,-0.05021887,0.15515126,-0.02385224,0.02072815,0.01355361,0.10259097,-0.02723478,-0.01325082,0.00034289,0.01998109,0.04154554,-0.06284001,-0.00379005,-0.03744507,-0.0072912,-0.02260646,0.02672985,-0.03951759,-0.12055807,0.02354036,-0.01728769,-0.0084464,-0.0067342,-0.00016904,0.05384927,0.00589369,0.02203299,0.02814204,0.00975788,0.06850187,-0.03656919,-0.07275578,-0.0050308,-0.02194294,0.00795016,-0.0354444,-0.04385115,0.06767768,-0.05602596,0.05975641,0.03021832,-0.04032429,-0.04419073,0.01402775,-0.00178879,-0.00636763,0.0639944,-0.01381554,0.07846063,-0.05029409,0.01976539,0.02816926,0.01468987,-0.00172849,-0.02380987,-0.00891532,-0.0427871,0.01433963,0.11786298,0.05233147,0.05125718,0.06349701,0.04274672,0.03521252,-0.00537549,0.00712092,0.03704253,-0.01150296,0.05554066,0.04639822,-0.00958197,-0.25157216,0.03571063,-0.06699729,0.07525194,-0.03590761,-0.0253978,0.05778416,0.06363443,0.03498784,-0.05188331,0.05600118,0.04951561,0.02583439,-0.06379984,0.05144395,0.01494156,0.00215416,-0.0460179,0.0335525,-0.04117431,0.07667614,0.01293722,0.22839531,-0.01991667,0.01129671,0.04789185,-0.012753,0.01987244,-0.00036607,0.04454107,-0.03472276,-0.01342363,0.04855908,-0.00411019,-0.03804402,0.02128395,-0.04463939,0.0062128,-0.02638917,0.03167752,-0.07083052,-0.01710689,-0.0040304,0.01630117,0.1029291,0.01451116,-0.02434931,-0.09548704,0.05037288,0.06339452,-0.02770473,-0.00901373,-0.0599712,-0.04280123,0.02620623,0.04078684,0.026998,0.01208835,0.01779465,0.02162865,0.02955327,-0.05177122,0.0664856,0.01351847,-0.02348016],"last_embed":{"hash":"1bypsck","tokens":443}}},"text":null,"length":0,"last_read":{"hash":"1bypsck","at":1753423548062},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems##<u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>#{1}","lines":[37,59],"size":4456,"outlinks":[{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":4},{"title":"Verify, check lotto wheels and generate lotto-6 systems.","target":"https://saliu.com/HLINE.gif","line":20}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09083142,-0.06138902,-0.00461669,-0.03950639,-0.05518121,0.02875554,-0.00599616,-0.00642514,0.01827117,-0.0323115,0.01017376,-0.0099146,0.06625379,-0.00307377,0.01158716,0.00828167,0.04964865,0.01581798,-0.08511303,-0.03092196,0.07011051,-0.0384926,-0.07053278,-0.07377546,-0.00204369,0.02832447,-0.03632528,-0.04087687,-0.02708114,-0.22832936,0.02897263,0.03245749,-0.01867368,-0.03489279,-0.06768721,-0.01167066,-0.04422663,0.02635434,-0.09271879,-0.00743693,0.00302169,0.04778478,0.01204044,-0.00435969,0.03194417,-0.04187706,0.02600246,0.00344179,0.09429521,0.00807673,-0.04685588,-0.02295592,0.02872776,0.03035545,0.03329736,0.03368986,0.04233565,0.08869589,0.02570866,0.04457579,0.04780606,0.09505049,-0.15983061,0.04869289,0.01382076,0.02728708,0.01494007,-0.0935003,0.01654487,0.06425457,0.0352794,0.04867409,-0.01040157,0.07958739,0.02145868,-0.02055945,-0.05422604,-0.08748569,-0.06969071,0.02734082,-0.05826199,0.00209543,0.00284344,0.01079665,-0.00637228,0.02592058,0.03854054,0.0221654,0.0608376,-0.06812135,0.00356725,0.03242781,0.08082736,0.05549452,-0.02027183,0.00857036,0.05480891,-0.02933523,0.03226271,0.12086225,-0.00414968,0.00201267,0.00801822,-0.01066956,0.0022039,-0.04580357,-0.01722338,-0.01877752,-0.04765354,0.03880602,0.0811134,0.00817543,0.06802837,-0.00461561,-0.05680339,-0.01442366,-0.00494361,-0.01295058,0.01236004,0.01196785,-0.05155158,-0.01282278,0.07118784,0.01471834,-0.00662497,0.00476473,0.03285297,0.05871004,0.0031323,0.03560116,0.02361205,0.03525754,-0.1018014,-0.05164074,0.02634696,-0.03860543,-0.00120461,0.01976136,-0.01828967,0.00057391,-0.02697799,-0.0570021,0.07517638,-0.08347332,-0.05619458,0.0463405,-0.02260123,0.00360672,-0.02326204,-0.01903579,-0.00489245,0.00023247,-0.01765956,-0.01582072,0.05028262,0.00632902,0.1362313,0.06273227,-0.06382214,0.0359069,-0.04521297,-0.03674312,-0.03523465,0.12707888,-0.02602844,-0.09447286,0.01275101,0.05628189,-0.04154486,-0.061097,-0.02394837,0.05080635,-0.0559091,0.01626567,0.10254617,-0.01699292,-0.06991964,-0.00802227,-0.02326687,-0.0381864,0.00469635,-0.03787299,-0.03797706,0.0112902,-0.02130632,-0.10879534,0.01833921,-0.01440824,0.00747025,0.03685811,-0.02841145,0.0142833,0.03513775,-0.00341055,-0.01541057,-0.02749334,-0.06424654,-0.0304875,0.06986588,-0.05038768,0.00527519,0.03041461,0.06537223,0.01421379,0.00293692,0.04085813,-0.04929319,-0.0859215,0.06581712,0.0356692,-0.05867338,-0.00732555,0.05337865,0.0926031,-0.07972935,0.05891482,0.01178458,0.01976263,-0.01978748,0.00620437,0.0047511,0.01536836,-0.07768777,-0.19642803,-0.02998605,-0.03327634,0.07934403,0.04626613,-0.03830909,-0.00094244,-0.02267165,0.00999228,0.08739702,0.06436417,-0.07338542,-0.00089669,0.05486108,-0.01340681,0.03749311,-0.10920242,-0.03857963,-0.08443665,0.07628892,0.0381772,0.03312575,-0.02254024,-0.09229004,0.04242253,-0.03667455,0.12972696,-0.00729047,0.00767545,-0.01840713,0.07682843,-0.0158806,-0.00058822,0.0023505,0.04704963,0.03319892,-0.03190748,-0.03875657,-0.00102577,-0.01522041,-0.06193687,0.0126648,-0.00655738,-0.12021008,0.03828432,-0.01641831,-0.00254184,-0.03015648,0.01301509,0.05045373,0.00332324,0.02780333,0.02612709,0.00636334,0.0596044,-0.01269171,-0.04961839,0.01276454,-0.01657371,0.02883091,-0.02417104,-0.03910396,0.05770257,-0.05840289,0.06188897,0.00523174,-0.02014381,-0.03873297,0.01452621,-0.01746504,-0.00770492,0.06347787,0.00186399,0.09818508,-0.04283567,0.03841273,0.06031461,0.01944811,0.02094806,-0.00667498,-0.02675738,-0.03426033,0.03890823,0.11969084,0.02545566,0.03099861,0.06873478,0.01256597,0.00971014,0.02463532,0.01337475,0.03016447,-0.01853563,0.02359024,0.04166451,0.05195183,-0.23141111,0.0136904,-0.02487022,0.04949701,-0.03948113,-0.02596758,0.00714132,-0.00666389,0.04899552,-0.07180332,0.03214216,0.04681923,0.02613068,-0.05838794,0.02229925,0.02389221,0.0564973,-0.03050432,0.05643261,-0.03237664,0.03432574,0.00353564,0.23155509,-0.00329852,0.00409502,0.03347576,-0.04496974,-0.00056556,-0.0281515,0.00347967,-0.01038583,0.02056513,0.05949027,-0.01868726,-0.05267733,0.01874774,-0.04908826,0.00610634,-0.02569392,0.0232957,-0.07336497,-0.00073181,-0.04525656,-0.00029719,0.09123556,0.01619011,-0.01424816,-0.1023196,0.0495958,0.05325142,-0.06804007,-0.04181596,-0.07651452,-0.03239281,0.03638576,0.03979171,0.01006658,-0.02436391,-0.0199714,-0.02663076,0.02325502,-0.04420969,0.05824575,0.00143102,0.00138863],"last_embed":{"hash":"hbsxdl","tokens":388}}},"text":null,"length":0,"last_read":{"hash":"hbsxdl","at":1753423548245},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling","lines":[60,99],"size":4096,"outlinks":[{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":5},{"title":"_**Lotto, Lottery, Software**_","target":"https://saliu.com/LottoWin.htm","line":19},{"title":"_**Lotto Wheels for Lotto Games Drawing 5, 6, 7 Numbers**_","target":"https://saliu.com/lotto_wheels.html","line":21},{"title":"_**Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":23},{"title":"_**Design Lotto Wheels Manually, Lotto Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":25},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":26},{"title":"_**Best On-The-Fly Wheeling Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":28},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":29},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":30},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":31},{"title":"_**Check WHEEL System, Lotto Wheels Winners**_","target":"https://saliu.com/bbs/messages/90.html","line":32},{"title":"**Lotto Wheels Software**","target":"https://saliu.com/infodown.html","line":33},{"title":"The best software to verify lotto wheels and systems for cover and guarantee: WheelCheck.","target":"https://saliu.com/HLINE.gif","line":35},{"title":"Forums","target":"https://forums.saliu.com/","line":37},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":37},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":37},{"title":"Contents","target":"https://saliu.com/content/index.html","line":37},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":37},{"title":"Home","target":"https://saliu.com/index.htm","line":37},{"title":"Search","target":"https://saliu.com/Search.htm","line":37},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":37},{"title":"Run free lottery software to verify the lotto wheels for missing combinations.","target":"https://saliu.com/HLINE.gif","line":39}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07917358,-0.07041358,0.01318732,-0.0524415,-0.06464484,0.0166389,0.00050942,-0.01774042,0.03179258,-0.02072993,0.01189776,0.01125135,0.05852077,-0.0062728,0.021957,0.02088398,0.04747066,0.03743702,-0.06501675,-0.00473033,0.01977328,-0.04343036,-0.07445284,-0.07573188,0.00376351,0.06889703,-0.04658234,-0.05702046,-0.02031953,-0.20265271,0.03348638,0.0671443,-0.03976745,-0.00980166,-0.07531677,-0.01748768,-0.06111003,0.02937179,-0.09022784,0.01227301,0.00477469,0.03514757,0.01385095,0.01455165,0.00455873,-0.03245933,0.04094828,-0.0007248,0.10334855,0.00869955,-0.01077064,-0.03979548,0.0358071,0.03121117,0.02501424,0.03817014,0.03049248,0.06438917,0.00847649,0.04148898,0.05146822,0.08398852,-0.14053167,0.04366684,0.02576266,0.04390524,0.01714586,-0.07498886,0.00897742,0.08219764,0.02305621,0.02430069,-0.00961848,0.04827456,0.01340457,-0.0207432,-0.03259804,-0.08582335,-0.05871162,0.02103112,-0.03839641,-0.01612059,0.01598939,0.01535249,-0.05682394,0.00970217,0.04697318,0.00471454,0.04766169,-0.0718988,-0.00902174,0.0506665,0.08863638,0.04826042,-0.03431569,0.01880535,0.0619474,-0.0004519,0.02448945,0.14432918,-0.03380973,-0.01804387,0.02475824,-0.0189949,0.01921979,-0.02515459,-0.02332719,-0.00499093,-0.07498246,0.06098629,0.07817844,0.00343583,0.06862123,0.00434916,-0.06013077,-0.03916698,-0.02544798,0.01332758,0.00973903,0.01629254,-0.04240794,-0.03365442,0.03376382,0.00741179,-0.01634757,-0.00452463,0.03881361,0.07012367,-0.02729333,0.04059894,0.02157878,0.01311515,-0.07728366,-0.01376089,0.03162356,-0.02960178,0.01689638,0.05088,-0.04189243,0.00004591,-0.02456349,-0.06731804,0.05427156,-0.09089641,-0.04176198,0.05188251,-0.02227941,0.0173931,0.00262345,-0.01099446,-0.00563144,-0.00375666,-0.0424324,-0.01930712,0.05212945,-0.0013563,0.11808811,0.04013366,-0.05431149,0.03809563,-0.05353726,-0.04287157,-0.03120954,0.10566319,-0.03690245,-0.08617622,-0.01108851,0.01967553,-0.03199396,-0.05007965,-0.03475032,0.04476247,-0.07722287,-0.00019624,0.13126171,0.00820505,-0.07158767,-0.01820179,-0.02772833,-0.05575163,0.00453881,-0.02814543,-0.03729397,0.00713269,-0.01236168,-0.09964632,-0.00918739,0.0084498,-0.00907496,0.04273035,-0.00020507,-0.00666463,0.00642913,-0.0256057,-0.02266998,-0.04075174,-0.08298744,-0.01679781,0.04990513,-0.06500982,-0.00715395,0.02031064,0.04443963,0.05035878,0.0127395,0.03614926,-0.03405552,-0.07792606,0.09628395,0.04524168,-0.07463622,-0.01740021,0.04047821,0.0706394,-0.060172,0.07259296,0.00667846,-0.0007813,-0.02983854,0.00873989,-0.01184496,0.01844933,-0.08348753,-0.18981521,-0.04257295,-0.00733071,0.06514362,0.06232028,-0.03597287,-0.00598233,-0.00431727,0.03766041,0.08392144,0.07956469,-0.07136427,-0.01049601,0.03847134,-0.02114048,0.03292019,-0.06904251,-0.04393134,-0.06804761,0.06221737,0.04616005,0.02731479,-0.04929618,-0.09639831,0.0560098,-0.02256816,0.12191458,-0.023568,0.00508102,0.00128083,0.06361725,-0.02654844,-0.0089569,0.00712862,0.04184453,0.02021088,-0.043197,-0.03655707,0.00814103,0.00369792,-0.06890614,-0.0043721,-0.0120601,-0.10207845,0.06468184,0.01420072,-0.00132044,-0.03662356,-0.01514217,0.05906644,-0.01702134,0.03915101,0.02984011,0.03657333,0.05615806,-0.02598116,-0.01821065,0.01900007,-0.00513655,0.05049352,-0.04485714,-0.01880245,0.04045999,-0.04322644,0.0645218,-0.00619717,-0.02800089,-0.07109723,-0.00321129,-0.03485673,0.00089938,0.03212614,-0.02255295,0.06845232,-0.07425269,0.03838987,0.05590894,0.04619728,0.02128518,-0.01050739,-0.03056376,-0.02319074,0.03390015,0.12475737,0.02767522,0.03004814,0.0577982,0.0046163,-0.00569717,0.04952753,0.02970421,0.03246069,0.00127668,-0.0030542,0.05218928,0.02787699,-0.23304845,0.0396296,-0.010705,0.04599204,-0.04585816,-0.0338447,-0.01282271,0.05676957,0.01867547,-0.06573483,0.08273136,0.05116022,-0.01900092,-0.04637818,0.0262656,-0.00609327,0.07340941,-0.02148309,0.06262331,-0.03737564,0.03854482,0.02009576,0.25536138,0.00839765,0.04252797,0.03003666,-0.03926222,0.01106939,-0.01925499,0.0242532,0.003041,0.00174417,0.07950797,-0.00749582,-0.06053699,-0.00020466,-0.03957951,0.01048055,-0.0351913,0.043194,-0.07121006,0.00161827,-0.06928811,0.00783769,0.07921003,0.01079487,-0.01174735,-0.08371656,0.04308364,0.06822474,-0.06211735,-0.01816596,-0.09782501,-0.04588404,0.01885452,0.02941685,-0.00594732,-0.02712778,0.00598745,-0.05051139,0.01425846,-0.0700497,0.06709385,0.02787447,-0.00951995],"last_embed":{"hash":"rx2fpm","tokens":109}}},"text":null,"length":0,"last_read":{"hash":"rx2fpm","at":1753423548390},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{9}","lines":[75,75],"size":290,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07516141,-0.07437257,0.02159708,-0.04063154,-0.05281021,0.04204292,0.01185596,-0.02102358,0.03472373,-0.02669031,0.02074358,0.03195262,0.03669264,-0.00188429,-0.00062494,0.01634593,0.01608509,0.03078115,-0.09298044,-0.02599853,0.02756951,-0.01130091,-0.09381028,-0.06502235,0.00335736,0.02058769,-0.05268412,-0.04316357,-0.05505439,-0.20487316,0.05511366,0.03563001,-0.03512881,-0.03624741,-0.07260498,-0.04023651,-0.0436998,0.06950978,-0.07800324,0.02130951,0.02517695,0.03173565,-0.01094856,-0.01496422,0.01336202,-0.02699715,0.00948764,0.00043658,0.07583734,0.01999264,-0.05507204,-0.02443215,0.03682178,0.00146413,0.01211898,0.0226156,0.02156309,0.09361804,0.0335275,0.04726433,0.0392793,0.07936964,-0.11359588,0.01805695,-0.00754091,0.01831151,0.0099266,-0.06340475,0.02522543,0.10804514,0.07600761,0.04154158,-0.01125774,0.08587175,0.03789504,-0.02596421,-0.03980029,-0.0904889,-0.04699177,0.04791189,-0.0707868,-0.0383192,0.00826137,0.0194944,-0.03650281,-0.00110432,0.02465837,-0.02161001,0.04885423,-0.06060844,0.01595523,0.05054218,0.01313568,0.06501467,-0.00776511,-0.04093407,0.05378256,-0.03791896,0.04987749,0.14673448,-0.01391424,0.00004351,0.01503322,0.03809431,-0.03561687,-0.01066325,-0.04948731,-0.02103122,-0.0626631,0.04677004,0.09475727,-0.01575173,0.07668059,-0.02748153,-0.04732992,-0.01133897,-0.02671565,0.01319235,0.00708535,-0.02250783,-0.04374431,0.00175773,0.0542384,0.00832945,-0.01187295,0.01917307,0.04422287,0.08000972,-0.01535883,0.02329791,0.0387888,-0.00785282,-0.11604637,-0.04411135,0.02966605,-0.0203224,-0.00318811,-0.00006311,-0.00915785,-0.0179802,-0.00129585,-0.00970057,0.07804991,-0.10126237,-0.05155329,0.06313467,-0.05472412,0.02437616,0.01022385,-0.03492083,0.0046434,-0.03641695,-0.01071263,-0.03598857,0.04281931,-0.02821841,0.14680804,0.02381162,-0.03041314,0.03817873,-0.07908962,-0.02306511,-0.00213618,0.11932209,-0.0414198,-0.06417955,0.00052093,0.04037227,-0.02424524,-0.08449715,-0.04022412,0.03368885,-0.03429997,0.05761804,0.08150158,0.03426771,-0.09216511,-0.03192265,-0.00277006,-0.03081498,0.02445962,-0.02223979,-0.04619033,0.03065441,-0.03606653,-0.10031283,0.00551388,-0.00579376,-0.00269009,0.03175826,-0.03068195,0.00809965,-0.00145281,-0.02850873,-0.00130599,-0.06056194,-0.04902472,-0.0513151,0.03989862,-0.04608978,-0.01626685,0.01835024,0.05638088,0.051951,0.01886887,0.03166379,0.00111167,-0.09963286,0.06558358,0.04885013,-0.05668711,-0.01919623,0.0338696,0.09776347,-0.05993045,0.07707863,-0.00750135,0.05463031,0.02661072,-0.06086335,0.0209405,-0.00016923,-0.05557703,-0.1848318,-0.04947229,-0.0025904,0.10272069,0.06087333,-0.02348413,-0.02315987,-0.00217542,-0.00143564,0.06764609,0.0452306,-0.09006962,0.01307212,0.06921191,-0.01174435,0.03010101,-0.10465255,-0.02152393,-0.07631547,0.03788015,0.02031944,0.05800856,-0.04338125,-0.09271545,0.06307156,-0.02259765,0.13019577,-0.01951099,0.02623628,-0.01195452,0.09438846,-0.02071021,-0.01722754,-0.00913778,0.03149309,0.04685478,-0.04526588,-0.04892225,-0.04196603,-0.00364205,-0.04141659,0.01134597,0.00559692,-0.11017153,0.02281086,-0.01738211,-0.02908917,0.00708644,0.02615789,0.06470667,-0.00946498,0.00103682,0.01588049,0.0167747,0.07073624,-0.02424114,-0.01862544,-0.01565764,-0.00450641,0.01108191,-0.00058313,-0.04852213,0.05797727,-0.06645804,0.03959745,0.00064458,0.00736508,-0.03918335,-0.00780241,-0.02006852,0.01927079,0.07758617,0.0116531,0.07786688,-0.03267204,0.04080845,0.04381295,0.01241578,0.02670744,-0.00380243,0.01398524,-0.02573856,0.01695331,0.12587976,0.02543068,0.00710447,0.04267014,0.0231129,0.00169605,0.01983275,0.02544471,0.04111646,-0.03602818,0.05436417,0.02435234,0.00154933,-0.22774553,0.0345155,-0.027084,0.06758911,-0.00207563,-0.01287989,0.0125986,0.04350373,0.04294561,-0.00736891,0.00185427,0.03460262,0.03542687,-0.06042363,0.01828307,0.01039175,0.08153832,-0.02275313,0.05603896,-0.04540152,0.05532623,-0.01002678,0.23974024,0.02023038,0.03374591,0.04504704,-0.0313595,0.02348483,-0.05110661,0.01239045,0.00802968,0.00653821,0.03920281,-0.03275085,-0.03824441,-0.02343583,-0.02502878,0.01378371,-0.01587832,0.01106825,-0.08240283,-0.01808289,-0.04911479,-0.01081466,0.09125406,0.01290889,-0.01534503,-0.07972393,0.02575376,0.05852403,-0.0763845,-0.04244658,-0.0912025,-0.06163632,0.01405559,0.03749582,0.02701886,0.00563131,0.00603147,-0.00404834,0.03086333,-0.07660764,0.04554157,-0.01635462,-0.01680735],"last_embed":{"hash":"t293yq","tokens":85}}},"text":null,"length":0,"last_read":{"hash":"t293yq","at":1753423548429},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{10}","lines":[76,77],"size":221,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.064662,-0.0602675,0.00761139,-0.01634449,-0.06219988,0.01453259,0.00448837,-0.00561419,0.02939989,-0.01124692,0.00869838,-0.00050848,0.06630493,-0.00218908,0.00026602,-0.00029587,0.05562485,0.05630692,-0.05714305,-0.01752229,0.03975453,-0.02531485,-0.11576954,-0.09150624,0.02574782,0.05811164,-0.0411271,-0.06087965,-0.00812038,-0.16035895,0.009454,0.02823008,-0.02350151,-0.04638543,-0.05937738,-0.035048,-0.04320556,0.04999894,-0.10641076,0.0111339,0.01557667,0.04517278,0.00903854,0.00828525,0.00916332,-0.04018259,0.04035608,0.01598654,0.09076892,-0.01532321,-0.02656444,-0.0038409,0.01059737,0.03128699,0.01352227,0.03852076,0.04024419,0.08364417,-0.01550238,0.02074552,0.03484422,0.07351723,-0.15404832,0.02535309,0.01199023,0.05862324,0.02136876,-0.03754244,0.02677691,0.07468618,0.05197385,0.0164726,0.00170755,0.07138801,0.01730108,-0.01264017,-0.02911471,-0.07441826,-0.05096712,0.00445415,-0.0546634,-0.01050424,0.01277937,0.04093868,-0.05396701,0.02332429,0.04196389,0.00980417,0.08650659,-0.08575723,0.00436541,0.03260093,0.06326975,0.05550534,-0.00184288,-0.02673077,0.06531919,-0.02215304,0.03324887,0.15302266,-0.00318462,-0.02270478,0.03886644,-0.03419559,0.00650658,-0.05541721,-0.02596096,-0.02718707,-0.07554799,0.06489889,0.08805332,0.01624394,0.05008001,-0.02518107,-0.0452176,-0.04545715,-0.03334094,-0.00383955,0.01436176,-0.01645995,-0.0504882,0.00593443,0.02616462,0.0057705,-0.01461226,0.01013089,0.01250851,0.09087507,-0.03028615,0.01314101,0.03438539,0.02502139,-0.10490229,-0.03503764,0.02266124,-0.04094545,0.01522271,0.01208145,-0.03678252,0.02461664,-0.03945584,-0.03159554,0.07524828,-0.12172999,-0.04519548,0.06094147,-0.0140817,0.025832,-0.02611709,-0.01090068,-0.01264206,-0.01561833,-0.03129247,-0.03630924,0.02906679,-0.00799383,0.11036687,0.01486185,-0.04726435,0.03436295,-0.04308976,-0.00319725,-0.03997626,0.15160987,-0.0393876,-0.09896498,-0.00748817,0.05375584,-0.04929223,-0.06471547,-0.03170168,0.03506834,-0.08333202,0.00132502,0.13322748,-0.02199776,-0.10467712,-0.02497684,-0.02529077,-0.04844397,0.01154717,-0.03941406,-0.03062592,0.00531329,-0.00666115,-0.08127394,-0.01590255,-0.01812869,0.01148523,0.04009534,-0.00533764,0.01739227,-0.00353254,-0.02721467,-0.03727278,-0.02581,-0.08114892,-0.04437785,0.07340574,-0.0461956,-0.02187474,0.02653141,0.04604807,0.02561274,-0.0162513,0.0357632,-0.01605817,-0.06136554,0.05975326,0.06740704,-0.07348377,-0.0123677,0.04534096,0.06195313,-0.06481548,0.0521458,0.02741175,0.03616447,-0.0231418,-0.0027792,-0.0207338,0.03282287,-0.07017248,-0.18296236,-0.04781479,-0.01651426,0.04784346,0.03712254,-0.01957554,-0.00920566,-0.00401866,-0.01385733,0.08483129,0.10499492,-0.08113988,0.00033072,0.0643017,-0.02499823,-0.00163386,-0.08328544,-0.03281267,-0.06305239,0.03636468,0.02280902,-0.00401417,-0.00754626,-0.10347937,0.01095469,-0.00879938,0.12778641,-0.01887815,0.03653787,0.00447859,0.06173697,-0.02964934,-0.03710669,-0.01814475,0.03741886,0.04426428,-0.0557708,-0.02877872,-0.01472519,-0.0000446,-0.04007436,-0.02594387,0.01019506,-0.08053492,0.02940072,-0.01159803,-0.00928315,-0.01072328,0.00701719,0.08162439,-0.00607382,0.03587303,0.04118132,0.03077192,0.04900566,0.01413757,-0.06035242,0.00763804,-0.03096776,0.04388316,-0.01377031,-0.05690948,0.0221016,-0.01827648,0.08375504,0.01381163,-0.0406622,-0.05985407,-0.00249481,-0.03854993,0.01330008,0.0710493,-0.01252492,0.0566656,-0.01769639,0.03742245,0.09622419,-0.02750089,0.01741646,0.01976884,-0.03565771,-0.04863746,0.03769286,0.12631172,0.05997386,0.05389346,0.05975354,0.02285941,-0.0150849,0.0002889,0.01502931,0.02632265,-0.01582191,-0.00687255,0.02030278,0.02900396,-0.2386837,0.04246945,-0.01123476,0.05682172,-0.04840035,-0.0375025,0.01357104,0.02676634,0.0175596,-0.05677341,0.0682445,0.02180651,0.0054514,-0.05957786,0.02903703,-0.00276557,0.06451292,-0.03163977,0.06897764,0.02155931,0.02926806,0.04520547,0.2467082,0.01044247,0.04343807,0.03483901,-0.02793791,0.01787073,-0.0108222,0.03357309,0.01338203,0.01892344,0.03945512,0.00749445,-0.06056352,0.03747296,-0.01879957,0.00858148,-0.01679151,0.03643044,-0.07269771,-0.00657646,-0.05186455,0.03078436,0.09059223,-0.00136187,-0.0204215,-0.09241628,0.05638335,0.05476449,-0.0762905,-0.01399851,-0.08216543,-0.04258505,0.03319213,0.02113281,0.03259365,-0.03534285,0.0030879,-0.02928156,0.01437381,-0.05474023,0.05279521,0.01788622,-0.00663354],"last_embed":{"hash":"1m22jsz","tokens":79}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423548471},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{12}","lines":[79,79],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{26}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09737784,-0.05326639,-0.0086933,-0.02262446,-0.03796053,0.04294104,-0.01146503,0.03162906,-0.00545215,-0.03900779,-0.03057663,-0.05521761,0.0670932,-0.00787446,0.00835884,-0.02151431,0.06054191,0.05141217,-0.03745597,-0.00505986,0.04618533,-0.02154749,-0.07975528,-0.06890229,0.03043141,0.04299219,-0.02337405,-0.04626311,-0.03461504,-0.2307025,-0.00670971,-0.0138939,-0.02024285,-0.04570675,-0.04520599,0.00596305,-0.01470311,0.02837322,-0.04660832,0.00770546,-0.0139181,0.03504327,-0.00007193,0.00898249,0.01509847,-0.03595755,0.03188618,0.01361584,0.0813949,-0.0085582,-0.04761269,-0.00841702,0.02145061,0.01627186,0.01713407,0.07015049,0.04293261,0.09882364,0.01233936,0.03221263,0.03279514,0.08264941,-0.18986519,0.02595968,0.02426297,0.04200172,0.00926698,-0.08847024,0.02895761,0.06684037,0.03961066,0.01928994,-0.03338839,0.09973519,0.00356082,-0.01093213,-0.02323713,-0.08460522,-0.06292104,-0.00181198,-0.06763496,0.00205194,0.00236823,0.02891628,-0.0299816,0.0178137,0.03566829,0.05675161,0.04511926,-0.07053964,0.05171738,0.02244371,0.08473232,0.04818691,-0.03499634,-0.01477795,0.02253012,-0.03573115,0.01378151,0.12835112,0.01181025,-0.01299114,0.00760147,0.03074684,0.02825173,-0.05158684,-0.02605289,-0.0055334,-0.06123539,0.03905402,0.09397821,0.01428217,0.05100122,-0.01125834,-0.06762913,-0.01534025,0.02814356,-0.01595539,-0.00719305,-0.01754673,-0.07009019,0.00315607,0.07060501,0.00239715,-0.0246847,-0.01442563,0.01027757,0.04582509,-0.00333148,0.067552,0.01235988,0.03607423,-0.08136568,-0.0629992,0.02115178,-0.011371,-0.03308956,0.03138816,-0.01969304,-0.00861871,-0.02979572,-0.08644079,0.04384435,-0.06908662,-0.04758961,0.02505513,-0.03972403,0.02618506,-0.01817049,-0.0081921,-0.02608174,-0.00021985,-0.02626283,-0.00731908,0.05526351,-0.02618406,0.13768412,0.05892057,-0.05883896,0.0386059,-0.02048478,-0.02897013,-0.04330139,0.15785973,-0.03098388,-0.09962656,0.00933989,0.05334689,-0.02097743,-0.06811701,-0.00945718,0.02787128,-0.05125995,0.00994163,0.08228762,-0.03542545,-0.07314302,0.00945299,-0.03840242,-0.01722309,-0.00956095,-0.03324546,-0.04782657,0.03268275,-0.0170679,-0.11584695,0.02520872,-0.03552377,-0.00059198,0.01467332,-0.03433252,-0.02926807,-0.00004243,0.00698556,-0.02613777,-0.02101661,-0.07493855,-0.04708877,0.06477453,-0.03930302,-0.01480863,0.02135869,0.066617,-0.00344726,-0.01527938,0.06868062,-0.04401805,-0.06041261,0.07678003,0.04941006,-0.04259417,0.03231214,0.03960752,0.04415029,-0.04532157,0.06282715,0.02583725,0.02884564,-0.01728421,0.00554559,-0.0019104,0.02735432,-0.06861579,-0.19874229,-0.04351695,-0.04594003,0.07320634,0.05089775,-0.05635727,0.02741651,0.00676651,-0.02708128,0.06477113,0.09357591,-0.04692036,0.00314716,0.03646706,0.00201999,0.02579959,-0.09699432,-0.04645364,-0.0686658,0.07370678,-0.00893961,0.02786842,-0.04885866,-0.06068878,0.03011649,-0.04481328,0.13996869,0.01062066,0.01309412,0.00562395,0.06851747,-0.02913442,-0.01824916,-0.03825183,0.08484098,0.05356267,-0.07031485,-0.02297422,-0.01025928,-0.02617905,-0.03678202,0.04546288,-0.01803258,-0.09266887,0.00021928,-0.0031712,0.0058887,-0.01932786,-0.00760215,0.06690785,0.0272551,0.04635599,0.00950767,0.02297817,0.03345076,0.01251482,-0.06601401,-0.01705674,-0.04612499,0.03869872,-0.02559393,-0.01569947,0.06889988,-0.03266335,0.08005366,0.00623701,-0.03735106,-0.05631971,0.03260199,0.01155402,-0.00507967,0.08204731,-0.02456648,0.05593204,0.00620655,0.0336931,0.04263264,-0.01355356,0.03069028,0.00039119,-0.00690584,-0.05871796,0.05710319,0.09904311,0.08269026,0.05690878,0.05801249,-0.01402888,0.02175376,0.0211477,0.03119262,0.01962152,-0.01134676,0.00585597,0.05664005,0.04530116,-0.2320296,0.00100239,-0.01611892,0.06658736,-0.06641923,-0.0500368,0.01284565,-0.01058285,0.0143426,-0.0645019,0.07462419,0.03518748,0.02954133,-0.0797863,0.02133031,0.03162137,0.03761979,-0.04461884,0.05506649,-0.02990718,0.01653522,0.00818,0.23344068,-0.00209154,-0.00248047,0.03892524,-0.003927,0.01305603,0.0163454,0.00983517,-0.0120096,0.00796381,0.02709088,0.00834959,-0.06125332,0.05333814,-0.03968624,0.00305158,0.00376117,0.00721691,-0.07394828,0.00440358,-0.03672583,0.02679706,0.08255252,-0.02136318,0.01117758,-0.06104169,0.05388066,0.04472636,-0.04644051,-0.03249212,-0.06295022,-0.02633282,0.01783919,0.04216637,0.02574264,-0.02169061,-0.00902982,-0.02841978,0.01789329,-0.00537511,0.09036012,-0.00412908,-0.01850251],"last_embed":{"hash":"2devd3","tokens":280}}},"text":null,"length":0,"last_read":{"hash":"2devd3","at":1753423548510},"key":"notes/saliu/Lotto Wheels Software, Cover, Missing Combinations, Systems.md#Lotto Wheels Software, Cover, Missing Combinations, Systems#Resources in Lottery Software, Systems, Lotto Wheeling#{26}","lines":[94,99],"size":626,"outlinks":[{"title":"The best software to verify lotto wheels and systems for cover and guarantee: WheelCheck.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Run free lottery software to verify the lotto wheels for missing combinations.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
