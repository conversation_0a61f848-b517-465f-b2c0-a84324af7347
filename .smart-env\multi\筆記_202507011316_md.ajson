
"smart_sources:筆記/202507011316.md": {"path":"筆記/202507011316.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"tdsdzp","at":1751356761736},"class_name":"SmartSource","last_import":{"mtime":1751347306634,"size":8542,"at":1751356761738,"hash":"tdsdzp"},"blocks":{"#":[1,16],"##{1}":[5,5],"##{2}":[6,11],"##{3}":[12,12],"##{4}":[13,13],"##{5}":[14,14],"##{6}":[15,16],"###程式碼變更":[17,123],"###程式碼變更#{1}":[19,123],"###如何使用新的架構":[124,166],"###如何使用新的架構#{1}":[126,127],"###如何使用新的架構#{2}":[128,128],"###如何使用新的架構#{3}":[129,130],"###如何使用新的架構#{4}":[131,132],"###如何使用新的架構#{5}":[133,133],"###如何使用新的架構#{6}":[134,134],"###如何使用新的架構#{7}":[135,166],"###如何使用新的架構#{8}":[137,166]},"outlinks":[{"title":"analyze_triggered_pattern_performance_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl \"f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl\"","line":10},{"title":"analyze_triggered_pattern_performance_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl \"f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl\"","line":19}]},