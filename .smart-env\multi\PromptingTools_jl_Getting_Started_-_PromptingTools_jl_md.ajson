"smart_sources:PromptingTools.jl/Getting Started - PromptingTools.jl.md": {"path":"PromptingTools.jl/Getting Started - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10495877,-0.02011091,-0.02263022,-0.07404612,-0.0486803,0.02989788,-0.04513969,0.05560337,0.05076954,-0.01776073,0.00532031,0.00260149,-0.00715559,0.04411708,-0.00786522,0.01811527,-0.03855339,-0.01518886,-0.06525147,-0.02441604,0.06918602,0.04882446,0.0045369,-0.05698521,0.0128269,0.08405771,0.00973906,-0.02489321,0.01381207,-0.18847011,0.05778783,-0.03193565,0.02531954,-0.02441518,-0.03822853,-0.01322716,-0.01831512,0.04650289,-0.01380978,-0.01801471,0.0476855,0.01157674,-0.04065774,-0.00821326,-0.04197041,-0.09635542,0.01139428,0.00304116,0.03070821,-0.04963967,-0.00636621,-0.0260735,0.04569812,-0.02813452,0.02295297,0.00815241,0.01792131,0.07314078,0.01948539,0.00748006,0.01554617,0.00843925,-0.17119731,0.15235156,-0.0023938,0.01832314,0.02912486,0.01160719,0.04531494,0.03524945,-0.01784196,0.03837506,-0.00819986,0.05178369,-0.00200963,-0.02167363,-0.02004811,-0.01059183,0.01932235,-0.09100543,-0.03361784,-0.01816915,-0.00297368,0.01088811,-0.06635378,0.00444557,0.00341205,0.00137946,0.04224932,-0.00876362,0.01897599,-0.05880978,-0.02096757,0.01520049,-0.06830606,-0.03946206,0.01207544,-0.03182853,-0.09730822,0.14065705,-0.01179652,-0.00556309,0.03812087,-0.01604343,0.02766966,-0.0567292,-0.00581945,-0.04205779,-0.01262392,0.01566464,0.00578923,-0.01651875,0.00105198,-0.11366899,-0.00087455,-0.022138,-0.02134862,-0.02685198,0.02324291,-0.04092974,0.06075831,0.01989957,0.00179526,0.00691195,-0.020861,0.04255774,0.02879551,0.05220059,0.02534083,-0.0041144,0.05680781,0.03099324,-0.07691687,-0.03536951,-0.01015876,0.06257363,0.03877001,-0.04710896,0.02834545,-0.01029686,0.01031757,-0.01682592,0.00952017,-0.13689071,0.01612042,0.01319487,-0.05162219,-0.01120484,-0.02380437,-0.05158401,0.04137541,0.02231044,-0.0598282,-0.02986051,0.06467865,0.03926806,0.07420257,0.07077005,-0.0033173,0.0022355,-0.08787727,-0.03486072,-0.00301831,0.11773881,0.04189728,-0.05967005,-0.07232293,-0.02931756,-0.04829096,-0.03101616,0.01978982,0.05833265,-0.04917667,-0.01241823,0.07904797,0.03532817,-0.05138513,0.01267537,0.00697053,0.01811882,0.03748985,-0.05016995,-0.05525398,-0.01415971,-0.02669358,-0.04082108,-0.02903312,-0.0880412,-0.00917073,0.04887839,-0.07666868,0.0259607,0.04362734,-0.03728165,0.00673993,-0.00866299,-0.01754075,-0.08876101,0.08097351,-0.03544506,0.05623265,0.01768479,0.0586063,0.04968626,-0.00782971,0.03607135,0.06483077,-0.00514662,0.12104544,0.02934134,-0.09640533,-0.05822481,0.06714382,-0.02313349,-0.02467975,-0.01721472,-0.05209542,0.02776167,0.0078558,0.07633147,-0.0275218,0.00969532,-0.02571842,-0.21265966,0.03909855,0.03933733,-0.03999072,0.04377212,-0.08430767,0.11383103,-0.0482268,-0.03367101,0.05263827,0.11722296,-0.0225101,0.05444159,0.00879784,0.00972964,-0.01368091,-0.022955,0.0129013,0.02495373,0.04728692,0.021124,-0.0261709,0.02887978,-0.08331075,-0.00968287,-0.01312077,0.10559537,0.04674445,0.04822955,-0.11412416,0.01022071,-0.01321103,0.03371001,-0.12113932,-0.00881244,0.01479881,-0.0227539,0.03252556,0.08862659,0.03018981,-0.02588161,0.0407717,-0.02115732,-0.03433331,-0.03190667,-0.01653511,-0.08018818,-0.03827947,-0.04251384,0.00883419,0.01811469,-0.05675841,0.03197972,0.00921618,0.01258987,-0.02913307,-0.06023806,-0.05411477,0.03110323,-0.00830212,0.02490634,-0.01386854,-0.01028609,-0.02875328,0.05294704,0.01717011,0.00154695,-0.00090959,0.08013655,-0.00566375,-0.02277225,0.09283105,0.02463626,0.08387571,-0.00901243,0.04735889,-0.00090365,-0.06563909,-0.00675973,-0.03339365,-0.05480686,-0.00236355,0.02009653,0.01379787,0.02786128,0.06846313,0.03433475,-0.02617219,0.06915884,-0.04542623,0.00395274,0.03614782,0.01050078,-0.02831095,0.03527138,0.00208276,-0.2347101,0.04014593,0.0020807,0.0399827,-0.0166389,0.07691409,0.06762129,0.01497987,-0.00799948,0.03658687,-0.03367491,-0.00155014,0.00556025,-0.006279,0.05318478,0.03394913,0.03720053,-0.00569539,0.01566281,-0.08467515,0.05773823,0.00773212,0.21583702,-0.02831569,0.02226613,-0.0092078,0.01130363,-0.07846934,0.08549693,0.02903909,0.01203095,0.04582768,0.15253775,-0.00179317,0.07558627,0.00464917,-0.01043766,0.00894898,0.01450769,-0.01728909,-0.0461536,0.03668019,-0.04113678,-0.00563253,0.04900831,-0.0303956,0.00043869,-0.08112834,-0.05020483,0.03830829,-0.02692327,0.00688372,-0.01854714,-0.00546506,-0.02921766,-0.00968802,0.05009884,0.02820666,-0.07296487,-0.00029995,0.08784048,-0.0190576,0.11342714,0.00441634,-0.01654962],"last_embed":{"hash":"19eadcbb2cbd034e84c8fa7aa925b29253e76dfcc86061194b768e378b1ab14f","tokens":462}}},"last_read":{"hash":"19eadcbb2cbd034e84c8fa7aa925b29253e76dfcc86061194b768e378b1ab14f","at":1745995219074},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/getting_started#Getting-Started/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"Prerequisites","target":"#Prerequisites \"Prerequisites\"","line":243},{"title":"Installation","target":"#Installation \"Installation\"","line":244},{"title":"Quick Start with @ai_str","target":"#Quick-Start-with-@ai_str \"Quick Start with @ai_str\"","line":245},{"title":"Using aigenerate with placeholders","target":"#Using-aigenerate-with-placeholders \"Using aigenerate with placeholders\"","line":246},{"title":"​","target":"#Getting-Started","line":248},{"title":"​","target":"#Prerequisites","line":250},{"title":"OpenAI","target":"https://platform.openai.com/signup","line":256},{"title":"API Key page","target":"https://platform.openai.com/account/api-keys","line":258},{"title":"OpenAI Documentation","target":"https://platform.openai.com/docs/quickstart?context=python","line":267},{"title":"Visual tutorial","target":"https://www.maisieai.com/help/how-to-get-an-openai-api-key-for-chatgpt","line":269},{"title":"OpenAI Guide","target":"https://platform.openai.com/docs/quickstart?context=python","line":287},{"title":"​","target":"#Installation","line":289},{"title":"​","target":"#Quick-Start-with-@ai_str","line":302},{"title":" Info: Tokens: 31 @ Cost: $0.0 in 1.5 seconds --> Be in control of your spending! \nAIMessage(\"The capital of France is Paris.\")\n```\n\nReturned object is a light wrapper with generated message in field `:content` (eg, `ans.content`) for additional downstream processing.\n\nIf you want to reply to the previous message, or simply continue the conversation, use `@ai!_str` (notice the bang `!`):\n\njulia\n\n```\nai!\"And what is the population of it?\"\n```\n\nYou can easily inject any variables with string interpolation:\n\njulia\n\n```\ncountry = \"Spain\"\nai\"What is the capital of \\$(country)?\"\n```\n\nplaintext\n\n```\n[ Info: Tokens: 32 @ Cost: $0.0001 in 0.5 seconds\nAIMessage(\"The capital of Spain is Madrid.\")\n```\n\nPro tip: Use after-string-flags to select the model to be called, eg, `ai\"What is the capital of France?\"gpt4` (use `gpt4t` for the new GPT-4 Turbo model). Great for those extra hard questions!\n\n## Using `aigenerate` with placeholders [​","target":"#Using-aigenerate-with-placeholders","line":315},{"title":" Info: Tokens: 74 @ Cost: $0.0001 in 1.3 seconds\nAIMessage(\"The capital of Spain is Madrid. And yes, the population of Madrid is larger than 1 million. As of 2020, the estimated population of Madrid is around 3.3 million people.\")\n```\n\nPro tip: Use `asyncmap` to run multiple AI-powered tasks concurrently.\n\nPro tip: If you use slow models (like GPT-4), you can use the asynchronous version of `@ai_str` -> `@aai_str` to avoid blocking the REPL, eg, `aai\"Say hi but slowly!\"gpt4` (similarly `@ai!_str` -> `@aai!_str` for multi-turn conversations).\n\nFor more practical examples, see the [Various Examples","target":"/PromptingTools.jl/dev/examples/readme_examples#Various-Examples","line":360},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/getting_started.md","line":370},{"title":"Previous pageHome","target":"/PromptingTools.jl/dev/index","line":372},{"title":"Next pageHow It Works","target":"/PromptingTools.jl/dev/how_it_works","line":374},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":376},{"title":"Icons8","target":"https://icons8.com","line":376},{"title":"**VitePress**","target":"https://vitepress.dev","line":376}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,247],"##Reference#{1}":[209,242],"##Reference#{2}":[243,243],"##Reference#{3}":[244,244],"##Reference#{4}":[245,245],"##Reference#{5}":[246,247],"#Getting Started [​](#Getting-Started)":[248,378],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)":[250,288],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{1}":[252,255],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{2}":[256,257],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{3}":[258,259],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{4}":[260,262],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{5}":[263,266],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{6}":[267,268],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{7}":[269,271],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{8}":[272,275],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{9}":[276,277],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{10}":[278,280],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{11}":[281,286],"#Getting Started [​](#Getting-Started)#Prerequisites [​](#Prerequisites)#{12}":[287,288],"#Getting Started [​](#Getting-Started)#Installation [​](#Installation)":[289,301],"#Getting Started [​](#Getting-Started)#Installation [​](#Installation)#{1}":[291,301],"#Getting Started [​](#Getting-Started)#Quick Start with `@ai_str` [​](#Quick-Start-with-@ai_str)":[302,346],"#Getting Started [​](#Getting-Started)#Quick Start with `@ai_str` [​](#Quick-Start-with-@ai_str)#{1}":[304,346],"#Getting Started [​](#Getting-Started)#Using `aigenerate` with placeholders [​](#Using-aigenerate-with-placeholders)":[347,378],"#Getting Started [​](#Getting-Started)#Using `aigenerate` with placeholders [​](#Using-aigenerate-with-placeholders)#{1}":[349,378]},"last_import":{"mtime":1712726482998,"size":8553,"at":1740449882769,"hash":"19eadcbb2cbd034e84c8fa7aa925b29253e76dfcc86061194b768e378b1ab14f"},"key":"PromptingTools.jl/Getting Started - PromptingTools.jl.md"},