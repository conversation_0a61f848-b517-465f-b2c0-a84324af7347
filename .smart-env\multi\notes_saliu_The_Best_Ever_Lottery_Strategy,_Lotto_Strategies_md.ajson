
"smart_sources:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md": {"path":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1ldxaqr","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753362720755,"size":26491,"at":1753423416500,"hash":"1ldxaqr"},"blocks":{"#---frontmatter---":[1,6],"#The Best Ever Lottery Strategy, Lotto Strategies":[8,217],"#The Best Ever Lottery Strategy, Lotto Strategies#{1}":[10,13],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>":[14,50],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{1}":[16,17],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{2}":[18,19],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{3}":[20,27],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{4}":[28,29],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{5}":[30,33],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{6}":[34,34],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{7}":[35,36],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{8}":[37,40],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{9}":[41,41],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{10}":[42,42],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{11}":[43,43],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{12}":[44,44],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{13}":[45,45],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{14}":[46,46],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{15}":[47,48],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{16}":[49,50],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>":[51,178],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#{1}":[53,70],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters":[71,80],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{1}":[73,74],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{2}":[75,75],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{3}":[76,76],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{4}":[77,78],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{5}":[79,80],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips":[81,94],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{1}":[83,84],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{2}":[85,86],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{3}":[87,88],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{4}":[89,89],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{5}":[90,90],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{6}":[91,92],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{7}":[93,94],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings":[95,105],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{1}":[97,98],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{2}":[99,99],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{3}":[100,100],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{4}":[101,101],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{5}":[102,102],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{6}":[103,103],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{7}":[104,105],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits":[106,115],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{1}":[108,111],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{2}":[112,112],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{3}":[113,113],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{4}":[114,115],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas":[116,122],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{1}":[118,119],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{2}":[120,120],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{3}":[121,122],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains":[123,129],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{1}":[125,126],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{2}":[127,127],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{3}":[128,129],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game":[130,135],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game#{1}":[132,133],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game#{2}":[134,135],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games":[136,143],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{1}":[138,139],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{2}":[140,140],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{3}":[141,141],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{4}":[142,143],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning":[144,156],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{1}":[146,151],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{2}":[152,152],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{3}":[153,153],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{4}":[154,154],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{5}":[155,156],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems":[157,170],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{1}":[159,160],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{2}":[161,161],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{3}":[162,162],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{4}":[163,163],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{5}":[164,164],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{6}":[165,165],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{7}":[166,166],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{8}":[167,167],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{9}":[168,168],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{10}":[169,170],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies":[171,178],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies#{1}":[173,176],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies#{2}":[177,178],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>":[179,194],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>#{1}":[181,192],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>#{2}":[193,194],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>":[195,217],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{1}":[197,198],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{2}":[199,200],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{3}":[201,201],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{4}":[202,202],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{5}":[203,203],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{6}":[204,204],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{7}":[205,205],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{8}":[206,206],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{9}":[207,207],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{10}":[208,208],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{11}":[209,209],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{12}":[210,211],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{13}":[212,217]},"outlinks":[{"title":"_**filters: Reduction parameters, eliminating restrictions in lotto software**_","target":"https://saliu.com/bbs/messages/919.html","line":24},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":26},{"title":"_**odd, even, low, high numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":41},{"title":"_**Lottery Sums, Lotto Sums, Sum-Totals in Lotto**_","target":"https://saliu.com/forum/lottery-sums.html","line":42},{"title":"_**sums of combinations, root sums (Fadic addition)**_","target":"https://saliu.com/strategy.html","line":42},{"title":"_**lotto decades, last digits**_","target":"https://saliu.com/decades.html","line":43},{"title":"_**largest number less than a user-chosen level (e.g. the biggest number in a lotto drawing is under 31 = <u>birthday numbers</u>**_)","target":"https://saliu.com/bbs/messages/910.html","line":44},{"title":"_**lowest lotto number higher than a user-chosen level (e.g. the lowest number in a lottery draw is above 31 = <u>anti-birthday-numbers</u>)**_","target":"https://saliu.com/bbs/messages/898.html","line":45},{"title":"_**ranges of lotto numbers, Excel spreadsheets**_","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":46},{"title":"_**groups of favorite numbers chosen by lottery players**_","target":"https://saliu.com/lotto-groups.html","line":47},{"title":"The winning lottery strategies are created by best lotto software for any jackpot game in the world.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":61},{"title":"_**Create Pick Lottery Strategies from Restriction Reports**_","target":"https://saliu.com/pick-software.html","line":63},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":65},{"title":"The filter report generator is a function of the best looking lottery software piece of art.","target":"https://saliu.com/ScreenImgs/lotto-filters.gif","line":67},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":75},{"title":"_**<u>BRIGHT Software</u> for Lottery, Lotto, Pick 3 4 Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":76},{"title":"_**<u>ULTIMATE Software</u> for Lotto, Pick Daily Lottery, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":77},{"title":"_**Winning Lottery Strategies, Systems, Software**_","target":"https://saliu.com/LottoWin.htm","line":85},{"title":"_**Skips Systems Software, Strategies for Lottery, Lotto, Gambling, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":89},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":90},{"title":"_**Powerball, Mega Millions, Strategy, Skip Systems**_","target":"https://saliu.com/powerball-systems.html","line":91},{"title":"_**<u>Wonder Grid</u> Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":99},{"title":"_**<u>Magical Lotto Wheel</u>: Lottery Pairs System, Pairing Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":100},{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":101},{"title":"_**Lottery <u>Wonder-Grid</u>, Lotto Pairs Strategy Software, Analysis**_","target":"https://saliu.com/bbs/messages/grid.html","line":102},{"title":"_**Lottery Pairs, Lotto Frequency, <u>Lotto Wonder-Grid</u>**_","target":"https://saliu.com/forum/lottery-pairs.html","line":103},{"title":"_**Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":104},{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":112},{"title":"_**Lotto Frequency Strategy, Lottery Software**_","target":"https://forums.saliu.com/lotto-frequency-efficiency.html","line":113},{"title":"_**Lottery Strategy on Positional Frequencies**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":114},{"title":"_**<u>Delta</u> Lotto, Lottery Systems, Strategies**_","target":"https://saliu.com/delta-lotto-software.html","line":120},{"title":"_**Lottery Deltas Build Effective Lotto Strategies, Systems**_","target":"https://saliu.com/bbs/messages/648.html","line":121},{"title":"_**<u>Markov Chains</u>: Followers, Pairs, Lottery, Lotto, Systems, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":127},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Programs**_","target":"https://saliu.com/Markov_Chains.html","line":128},{"title":"_**All Lotto Numbers: Professors Win UK Lottery Jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":134},{"title":"_**Jackpot Lottery Strategy: 12 Numbers Combinations, Lotto-6 Wheels System**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":140},{"title":"_**Lotto Strategy, Software: <u>12-Numbers</u> Combinations Applied to 6-Number Lottery Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":141},{"title":"_**Lottery Strategy, Software: <u>10-Numbers</u> Combinations Wheeled to 5-Number Lotto Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":142},{"title":"_**Lottery, Lotto Strategy in Reverse: Turn Loss into Win**_","target":"https://saliu.com/reverse-strategy.html","line":152},{"title":"_**Lottery Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":153},{"title":"_**Lottery Strategy Reversed Decades, Last Digits, Odd Even**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":154},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":155},{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":161},{"title":"_**Create, Make Lotto Wheels, Lottery Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":162},{"title":"_**The Best <u>Lotto Wheels for 9, 12, 18, 21 Numbers</u>: 4-in-6 Minimum Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":164},{"title":"**_The Best On-The-Fly Wheeling Software Applies Real Lottery Filtering_**","target":"https://saliu.com/bbs/messages/wheel.html","line":166},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":167},{"title":"_**Lottery Wheeling Software: Convert Systems to Player's Tickets**_","target":"https://saliu.com/bbs/messages/857.html","line":168},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":169},{"title":"_**Software to Combine Lottery Strategies, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":177},{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":193},{"title":"_**Lottery, Lotto: Software, Strategies, Systems, Wheels**_","target":"https://saliu.com/content/lottery.html","line":197},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage**_","target":"https://saliu.com/lottery-numbers-loss.html","line":199},{"title":"_**Playing Lottery Strategies, Lotto Strategy Means Start**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":201},{"title":"_**The Best Strategy in Lottery, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":203},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":205},{"title":"_**Judge Best Lotto Software, Lottery Strategy, Theory**_","target":"https://saliu.com/bbs/messages/623.html","line":207},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":209},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":210},{"title":"Lottery software programs ready to create the best lotto strategies to win big money.","target":"https://software.saliu.com/HLINE.gif","line":212},{"title":"**Roulette**","target":"https://download.saliu.com/roulette-systems.html","line":214},{"title":"**Sic Bo**","target":"https://forums.saliu.com/strategies-casino-war-sic-bo-gambling-formula.html","line":214},{"title":"**Artificial Intelligence**","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":214},{"title":"**Craps**","target":"https://saliu.com/bbs/messages/504.html","line":214},{"title":"**Sports**","target":"https://saliu.com/betting.html","line":214},{"title":"**Blackjack**","target":"https://saliu.com/blackjack-strategy-system-win.html","line":214},{"title":"**Baccarat**","target":"https://saliu.com/winning_bell.html","line":214},{"title":"**_Software_ Home**","target":"https://software.saliu.com/index.html","line":214},{"title":"**Live Sports**","target":"https://software.saliu.com/live-sports-streams.html","line":214},{"title":"This is the site for lotto, lottery software strategies, winning systems for all games in the world.","target":"https://software.saliu.com/HLINE.gif","line":216}],"metadata":{"created":"2025-07-24T21:12:00 (UTC +08:00)","tags":["best","strategy","lottery","lotto","strategies","filters","systems","software","programs","applications","apps"],"source":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","author":null}},
"smart_sources:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md": {"path":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0973653,-0.03972553,-0.03013695,-0.01997477,-0.03982204,0.01635017,0.02611339,0.01938297,0.05140842,-0.00385258,0.00739834,0.00639393,0.05560241,-0.00149913,0.01280585,-0.02910008,0.02189538,-0.01197001,-0.04670342,-0.00597465,0.11523139,-0.05766995,-0.05187639,-0.06680668,0.08333979,-0.01492732,-0.01774011,-0.04928456,-0.02024158,-0.22951478,-0.00263215,0.00085274,-0.01493302,-0.0457253,-0.10815171,0.00228945,0.00612923,0.06692737,-0.04954942,0.05955925,-0.00618094,0.02283073,0.00900007,0.00384185,-0.015837,-0.02007298,0.01629327,0.00773132,0.0240373,0.01148644,-0.05660743,0.04054741,-0.01086095,0.00694648,0.05511057,0.0302937,0.05102357,0.10512321,0.02631989,0.03246948,-0.0126493,0.03620567,-0.20757931,0.0937849,-0.03768901,0.04296688,0.01527512,-0.00765352,-0.00737939,0.05887689,0.04910764,0.04150621,-0.01013892,0.07892747,0.04347004,-0.02866294,-0.00888169,-0.05287418,-0.03001953,0.03751603,-0.03640989,-0.0220404,-0.00186278,-0.00683479,0.01748657,0.04715355,0.00440194,0.0107798,0.06331401,-0.07847925,0.02960578,0.0134821,0.05175458,0.03249945,0.03493779,0.01993685,0.00884809,-0.01713631,-0.0006771,0.09665505,-0.0007934,0.02289911,-0.01090164,-0.01956518,0.06104474,-0.02963874,-0.0018933,-0.05714557,0.00192695,0.0499067,0.0221468,-0.00866152,0.09872746,-0.02961054,-0.0132568,-0.00241635,0.02017147,-0.01608373,0.05718958,-0.00276908,-0.05345997,0.03601626,0.02515549,-0.01463073,-0.03073114,-0.01540814,0.03285545,0.04109051,-0.00697601,-0.00042864,0.02785819,-0.03329662,-0.12776929,-0.05624825,-0.01932027,-0.01839181,-0.01243608,0.00107868,0.00878125,0.02708474,-0.03448472,-0.01844223,0.03809524,-0.13617228,-0.04488385,0.064012,0.02798947,-0.00224389,-0.00750901,0.02199057,-0.00245577,-0.03120683,-0.02983413,-0.04325585,0.0000246,0.00032774,0.07245842,0.06224589,-0.03694563,-0.02189421,-0.01858672,-0.04227443,-0.05627252,0.09306931,-0.03746665,-0.11386438,-0.04962814,0.04567401,-0.0604315,-0.08322588,-0.03555753,-0.03869506,-0.03737024,0.03539906,0.08355674,-0.01185416,-0.09239472,-0.04481302,-0.07576662,-0.01234454,0.01982646,-0.0453486,-0.04577254,-0.01018221,-0.01492393,-0.08743094,0.03581784,-0.02644599,0.05264726,0.04514582,-0.05734151,0.03502498,-0.01808655,0.02336041,-0.03869145,-0.00494583,-0.00000102,-0.03981439,0.05438418,-0.00397588,-0.04554144,-0.0056748,-0.00259161,0.01796267,-0.06006587,0.04674201,-0.01925497,-0.06431685,0.12983672,0.03475604,-0.03618621,0.02573805,0.02770711,0.06138705,-0.03679658,0.01163559,-0.01032873,0.01804694,-0.03801423,0.01669473,0.01430673,0.02392459,-0.09970585,-0.20114531,-0.02800326,-0.06163375,0.00794944,0.03521837,-0.0129315,0.0057342,-0.04188018,0.03541697,0.07999265,0.07029566,-0.0492546,0.03383314,0.07620457,-0.01329043,-0.01053475,-0.08429314,-0.02812089,-0.06026084,0.02307242,0.02420085,0.01844319,0.0116429,-0.07815377,0.01937888,-0.02497379,0.16490543,0.03099101,0.0113514,0.03294259,0.07400765,-0.00980755,-0.00323064,-0.0525703,-0.01651246,0.01143212,-0.00087541,-0.04222913,-0.03380265,-0.0061712,-0.05299868,-0.02814395,-0.02588584,-0.10711865,-0.04533638,0.01268457,-0.04172156,0.06329318,-0.01665805,0.02977228,0.06541124,-0.02400475,0.03908659,-0.00759298,0.05796246,-0.05103914,-0.09443747,-0.00663689,0.00575287,0.0494609,-0.02525325,-0.00181816,0.03988594,-0.0297476,0.03690605,0.01660569,0.01607347,-0.0050218,-0.00608945,-0.00880548,-0.04595398,0.08604217,-0.0101974,0.01439861,0.00593509,0.01241891,0.06982552,-0.03002784,-0.01555504,-0.02861551,0.00930172,-0.04691088,0.06866342,0.04564267,0.03280871,0.02730367,0.0497463,0.01558425,-0.01010054,-0.02946362,-0.00348913,0.02792651,-0.04445162,0.02047044,0.04775953,0.00698185,-0.24269348,0.02985672,-0.01566305,0.05174494,0.00736492,-0.02252533,0.00744652,0.01923291,0.03332682,-0.01957232,0.05189274,0.05341386,0.04216986,-0.00635394,0.00806834,-0.00444759,0.005468,-0.0150351,0.08545453,0.0246816,0.08962826,0.03850542,0.24305588,-0.01939119,-0.01023642,0.02809075,0.00009817,0.02968936,-0.09425965,0.03484309,0.018055,0.0396954,0.03578759,-0.00584262,-0.02649057,-0.00726151,0.0107642,0.041338,-0.02345799,0.01359599,-0.1002102,0.01720257,0.02634986,0.03630793,0.14293417,0.00950504,-0.0242138,-0.08414875,0.04679115,0.0532467,-0.10522027,-0.0577479,-0.02088138,-0.01607041,0.01179957,0.04424992,0.02469664,-0.00605533,0.00703513,0.00225103,0.04830847,0.05284471,0.07131945,0.06372788,0.03808803],"last_embed":{"hash":"1ldxaqr","tokens":489}}},"last_read":{"hash":"1ldxaqr","at":1753423630910},"class_name":"SmartSource","last_import":{"mtime":1753362720755,"size":26491,"at":1753423416500,"hash":"1ldxaqr"},"blocks":{"#---frontmatter---":[1,6],"#The Best Ever Lottery Strategy, Lotto Strategies":[8,217],"#The Best Ever Lottery Strategy, Lotto Strategies#{1}":[10,13],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>":[14,50],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{1}":[16,17],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{2}":[18,19],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{3}":[20,27],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{4}":[28,29],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{5}":[30,33],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{6}":[34,34],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{7}":[35,36],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{8}":[37,40],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{9}":[41,41],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{10}":[42,42],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{11}":[43,43],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{12}":[44,44],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{13}":[45,45],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{14}":[46,46],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{15}":[47,48],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{16}":[49,50],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>":[51,178],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#{1}":[53,70],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters":[71,80],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{1}":[73,74],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{2}":[75,75],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{3}":[76,76],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{4}":[77,78],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{5}":[79,80],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips":[81,94],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{1}":[83,84],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{2}":[85,86],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{3}":[87,88],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{4}":[89,89],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{5}":[90,90],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{6}":[91,92],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{7}":[93,94],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings":[95,105],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{1}":[97,98],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{2}":[99,99],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{3}":[100,100],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{4}":[101,101],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{5}":[102,102],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{6}":[103,103],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{7}":[104,105],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits":[106,115],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{1}":[108,111],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{2}":[112,112],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{3}":[113,113],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{4}":[114,115],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas":[116,122],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{1}":[118,119],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{2}":[120,120],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{3}":[121,122],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains":[123,129],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{1}":[125,126],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{2}":[127,127],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{3}":[128,129],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game":[130,135],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game#{1}":[132,133],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game#{2}":[134,135],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games":[136,143],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{1}":[138,139],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{2}":[140,140],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{3}":[141,141],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games#{4}":[142,143],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning":[144,156],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{1}":[146,151],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{2}":[152,152],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{3}":[153,153],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{4}":[154,154],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{5}":[155,156],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems":[157,170],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{1}":[159,160],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{2}":[161,161],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{3}":[162,162],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{4}":[163,163],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{5}":[164,164],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{6}":[165,165],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{7}":[166,166],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{8}":[167,167],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{9}":[168,168],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{10}":[169,170],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies":[171,178],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies#{1}":[173,176],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies#{2}":[177,178],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>":[179,194],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>#{1}":[181,192],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>#{2}":[193,194],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>":[195,217],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{1}":[197,198],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{2}":[199,200],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{3}":[201,201],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{4}":[202,202],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{5}":[203,203],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{6}":[204,204],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{7}":[205,205],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{8}":[206,206],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{9}":[207,207],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{10}":[208,208],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{11}":[209,209],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{12}":[210,211],"#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{13}":[212,217]},"outlinks":[{"title":"_**filters: Reduction parameters, eliminating restrictions in lotto software**_","target":"https://saliu.com/bbs/messages/919.html","line":24},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":26},{"title":"_**odd, even, low, high numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":41},{"title":"_**Lottery Sums, Lotto Sums, Sum-Totals in Lotto**_","target":"https://saliu.com/forum/lottery-sums.html","line":42},{"title":"_**sums of combinations, root sums (Fadic addition)**_","target":"https://saliu.com/strategy.html","line":42},{"title":"_**lotto decades, last digits**_","target":"https://saliu.com/decades.html","line":43},{"title":"_**largest number less than a user-chosen level (e.g. the biggest number in a lotto drawing is under 31 = <u>birthday numbers</u>**_)","target":"https://saliu.com/bbs/messages/910.html","line":44},{"title":"_**lowest lotto number higher than a user-chosen level (e.g. the lowest number in a lottery draw is above 31 = <u>anti-birthday-numbers</u>)**_","target":"https://saliu.com/bbs/messages/898.html","line":45},{"title":"_**ranges of lotto numbers, Excel spreadsheets**_","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":46},{"title":"_**groups of favorite numbers chosen by lottery players**_","target":"https://saliu.com/lotto-groups.html","line":47},{"title":"The winning lottery strategies are created by best lotto software for any jackpot game in the world.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":61},{"title":"_**Create Pick Lottery Strategies from Restriction Reports**_","target":"https://saliu.com/pick-software.html","line":63},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":65},{"title":"The filter report generator is a function of the best looking lottery software piece of art.","target":"https://saliu.com/ScreenImgs/lotto-filters.gif","line":67},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":75},{"title":"_**<u>BRIGHT Software</u> for Lottery, Lotto, Pick 3 4 Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":76},{"title":"_**<u>ULTIMATE Software</u> for Lotto, Pick Daily Lottery, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":77},{"title":"_**Winning Lottery Strategies, Systems, Software**_","target":"https://saliu.com/LottoWin.htm","line":85},{"title":"_**Skips Systems Software, Strategies for Lottery, Lotto, Gambling, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":89},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":90},{"title":"_**Powerball, Mega Millions, Strategy, Skip Systems**_","target":"https://saliu.com/powerball-systems.html","line":91},{"title":"_**<u>Wonder Grid</u> Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":99},{"title":"_**<u>Magical Lotto Wheel</u>: Lottery Pairs System, Pairing Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":100},{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":101},{"title":"_**Lottery <u>Wonder-Grid</u>, Lotto Pairs Strategy Software, Analysis**_","target":"https://saliu.com/bbs/messages/grid.html","line":102},{"title":"_**Lottery Pairs, Lotto Frequency, <u>Lotto Wonder-Grid</u>**_","target":"https://saliu.com/forum/lottery-pairs.html","line":103},{"title":"_**Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":104},{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":112},{"title":"_**Lotto Frequency Strategy, Lottery Software**_","target":"https://forums.saliu.com/lotto-frequency-efficiency.html","line":113},{"title":"_**Lottery Strategy on Positional Frequencies**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":114},{"title":"_**<u>Delta</u> Lotto, Lottery Systems, Strategies**_","target":"https://saliu.com/delta-lotto-software.html","line":120},{"title":"_**Lottery Deltas Build Effective Lotto Strategies, Systems**_","target":"https://saliu.com/bbs/messages/648.html","line":121},{"title":"_**<u>Markov Chains</u>: Followers, Pairs, Lottery, Lotto, Systems, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":127},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Programs**_","target":"https://saliu.com/Markov_Chains.html","line":128},{"title":"_**All Lotto Numbers: Professors Win UK Lottery Jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":134},{"title":"_**Jackpot Lottery Strategy: 12 Numbers Combinations, Lotto-6 Wheels System**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":140},{"title":"_**Lotto Strategy, Software: <u>12-Numbers</u> Combinations Applied to 6-Number Lottery Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":141},{"title":"_**Lottery Strategy, Software: <u>10-Numbers</u> Combinations Wheeled to 5-Number Lotto Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":142},{"title":"_**Lottery, Lotto Strategy in Reverse: Turn Loss into Win**_","target":"https://saliu.com/reverse-strategy.html","line":152},{"title":"_**Lottery Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":153},{"title":"_**Lottery Strategy Reversed Decades, Last Digits, Odd Even**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":154},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":155},{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":161},{"title":"_**Create, Make Lotto Wheels, Lottery Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":162},{"title":"_**The Best <u>Lotto Wheels for 9, 12, 18, 21 Numbers</u>: 4-in-6 Minimum Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":164},{"title":"**_The Best On-The-Fly Wheeling Software Applies Real Lottery Filtering_**","target":"https://saliu.com/bbs/messages/wheel.html","line":166},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":167},{"title":"_**Lottery Wheeling Software: Convert Systems to Player's Tickets**_","target":"https://saliu.com/bbs/messages/857.html","line":168},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":169},{"title":"_**Software to Combine Lottery Strategies, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":177},{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":193},{"title":"_**Lottery, Lotto: Software, Strategies, Systems, Wheels**_","target":"https://saliu.com/content/lottery.html","line":197},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage**_","target":"https://saliu.com/lottery-numbers-loss.html","line":199},{"title":"_**Playing Lottery Strategies, Lotto Strategy Means Start**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":201},{"title":"_**The Best Strategy in Lottery, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":203},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":205},{"title":"_**Judge Best Lotto Software, Lottery Strategy, Theory**_","target":"https://saliu.com/bbs/messages/623.html","line":207},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":209},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":210},{"title":"Lottery software programs ready to create the best lotto strategies to win big money.","target":"https://software.saliu.com/HLINE.gif","line":212},{"title":"**Roulette**","target":"https://download.saliu.com/roulette-systems.html","line":214},{"title":"**Sic Bo**","target":"https://forums.saliu.com/strategies-casino-war-sic-bo-gambling-formula.html","line":214},{"title":"**Artificial Intelligence**","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":214},{"title":"**Craps**","target":"https://saliu.com/bbs/messages/504.html","line":214},{"title":"**Sports**","target":"https://saliu.com/betting.html","line":214},{"title":"**Blackjack**","target":"https://saliu.com/blackjack-strategy-system-win.html","line":214},{"title":"**Baccarat**","target":"https://saliu.com/winning_bell.html","line":214},{"title":"**_Software_ Home**","target":"https://software.saliu.com/index.html","line":214},{"title":"**Live Sports**","target":"https://software.saliu.com/live-sports-streams.html","line":214},{"title":"This is the site for lotto, lottery software strategies, winning systems for all games in the world.","target":"https://software.saliu.com/HLINE.gif","line":216}],"metadata":{"created":"2025-07-24T21:12:00 (UTC +08:00)","tags":["best","strategy","lottery","lotto","strategies","filters","systems","software","programs","applications","apps"],"source":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","author":null}},"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12626694,-0.04464983,-0.03511303,-0.00163749,-0.0185438,0.03037761,-0.02044907,0.02936032,0.02230679,-0.01471055,-0.00010553,0.00258733,0.01541894,0.03236056,-0.00208904,-0.02243976,0.00954646,-0.03862133,-0.03244861,0.00118723,0.11881668,-0.03627788,-0.06207914,-0.07627276,0.07753932,-0.00859696,-0.02670542,-0.06177511,-0.02299295,-0.16406828,-0.02447786,-0.01220164,-0.01108972,-0.04062241,-0.0813073,-0.01179959,-0.01429707,0.07772375,-0.01404853,0.0645078,-0.01682074,0.0535763,-0.04210809,-0.00246993,-0.00302331,-0.02414491,0.03235992,0.00710825,0.02771723,0.01630245,-0.05919824,0.02603163,-0.0231642,0.00831798,0.08068161,0.02672421,0.04501401,0.06805617,0.02643667,0.05248958,0.00269071,0.03105096,-0.22366829,0.09373979,-0.0283113,0.0233616,0.01400793,-0.00032935,-0.0491944,0.058508,0.06127498,0.01471608,-0.01116593,0.05690518,0.06842033,-0.00706423,-0.01231042,-0.07183318,-0.03851116,0.0167901,-0.03445759,-0.01441901,-0.01348696,-0.04247234,0.02902604,0.04303547,0.0324634,0.0756324,0.08037569,-0.04142869,0.04720663,0.02051149,0.07230502,0.0283758,-0.0077276,0.02795904,0.01188718,-0.0319554,-0.01690555,0.12838352,-0.03628751,0.03413627,-0.00988406,0.00199324,0.07695986,-0.02136077,-0.00008896,-0.0468507,-0.02436846,0.03762859,0.00294767,0.01222812,0.10838753,-0.07126217,0.00485117,0.00304463,0.04318505,-0.01132149,0.02858491,0.01437519,-0.05599226,0.01446462,0.00840668,-0.02214934,0.00202239,0.00233299,0.01368379,0.05047252,-0.01564942,0.01843541,0.0345826,0.00673118,-0.14052077,-0.03363895,-0.00644987,-0.03719574,0.00998438,-0.05998602,0.0000465,-0.01929206,-0.0259251,-0.05537982,0.04995323,-0.13445875,-0.04039373,0.09203013,0.03237095,-0.00244196,0.00032376,-0.00402229,-0.03178452,-0.0201727,-0.01052186,-0.05268024,-0.00419493,0.02805565,0.08040488,0.08347774,-0.02133835,-0.01702689,-0.03122761,-0.02161536,-0.04643755,0.13268605,-0.04319345,-0.16479389,-0.05368964,0.04587741,-0.02042313,-0.0819091,-0.03439244,-0.0269969,-0.04768592,0.03834932,0.12632167,-0.01233864,0.00128753,-0.03418585,-0.05183737,-0.00172503,0.00235352,-0.02261266,-0.06751169,0.00684104,-0.0376744,-0.06802079,0.03230767,-0.05459732,0.07345717,0.05524468,-0.04169193,-0.01947226,-0.00522847,0.02537694,-0.03180036,-0.00707792,-0.0275335,-0.0371634,0.05700936,-0.02119667,-0.02549615,-0.01891818,-0.02395799,-0.0013651,-0.01726492,0.03203696,-0.02464251,-0.0670203,0.06303567,0.03728266,-0.02380677,0.02572553,0.07483618,0.05852285,-0.06934046,0.00054124,-0.01201386,0.02013941,-0.02420278,0.00607122,0.00144712,0.02561965,-0.08818935,-0.20287292,-0.00923476,-0.05094577,-0.00427261,0.00472172,-0.01106126,0.04677219,-0.03072895,0.0552481,0.08580289,0.07707813,-0.04729987,0.01606718,0.02552636,-0.01154111,-0.01895015,-0.05697463,-0.04626036,-0.02101721,0.0238256,0.01842736,-0.01146659,-0.00734721,-0.08510932,0.03028367,-0.02855916,0.14336413,0.0647099,-0.00420473,0.00983309,0.09684584,0.00626412,-0.02773598,-0.07368559,-0.00114071,0.00818741,-0.01286377,0.01412543,-0.03752361,-0.02719687,-0.04624847,0.01140703,0.00165877,-0.10006691,-0.04260555,-0.00835535,0.01329037,0.03102154,-0.01832357,0.02026382,0.04432093,-0.01642422,0.05306767,0.02366792,0.06768856,-0.00335918,-0.07540066,-0.03439293,-0.02777151,0.03701608,-0.02313205,-0.01146811,0.02471294,-0.01542469,0.01833831,0.01283836,0.01314887,-0.03831946,-0.02266777,-0.008877,-0.01246828,0.04716565,-0.00155313,-0.00327199,-0.00611772,-0.00312583,0.07369184,-0.03384385,0.01657335,-0.0147057,0.00695262,-0.06025589,0.07184613,0.04932034,0.03852265,0.06412964,0.03217474,0.01577339,-0.00693623,-0.00669674,0.00809839,0.00669812,-0.05090428,-0.00264431,0.0526456,0.00993223,-0.26049429,0.03411857,0.01330135,0.04824118,0.01424654,0.00453776,0.00547144,-0.00439138,0.00123649,-0.00708985,0.05826662,0.06573543,-0.00145119,-0.01007626,-0.02903943,-0.01485028,0.02004984,-0.02696748,0.06499479,0.04373052,0.04006575,0.03357711,0.23469269,0.02413717,-0.00806178,0.02588372,0.00298151,0.01921442,-0.04687817,0.01849159,0.0119589,0.02218249,0.03110981,0.00111466,-0.0493513,0.03819116,0.00634777,0.04654149,-0.03400189,0.036451,-0.09520019,0.03695843,0.000316,0.03554616,0.13190956,0.0150237,-0.05725966,-0.06914256,0.02029837,0.02683535,-0.10266738,-0.08993766,-0.04707116,0.00435855,-0.02490797,0.06224104,0.06598094,-0.0192715,0.01458565,0.02017586,0.05666288,0.04275711,0.07877455,0.04206997,0.04424945],"last_embed":{"hash":"1o24va4","tokens":95}}},"text":null,"length":0,"last_read":{"hash":"1o24va4","at":1753423627373},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#---frontmatter---","lines":[1,6],"size":232,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09007748,-0.04136463,-0.03163102,-0.02141647,-0.04057118,0.01835285,0.04910807,0.01611843,0.05187801,-0.00572874,0.00633885,0.00442699,0.0595345,-0.00718935,0.0150851,-0.01756922,0.01569199,0.00227307,-0.05305492,-0.00472136,0.1134703,-0.05948462,-0.04760117,-0.06099061,0.08525806,-0.01592025,-0.02792309,-0.05481307,-0.02442368,-0.23121074,0.00408994,-0.00685679,-0.01054549,-0.04300743,-0.10088368,0.00036554,0.00934098,0.0665733,-0.04873876,0.06157602,0.00184328,0.01330635,0.01426902,0.00210444,-0.01291691,-0.0191747,0.00776241,0.01885589,0.01994956,0.01246487,-0.05894378,0.04943487,-0.00803002,0.00974864,0.05219115,0.03943105,0.05911733,0.10760092,0.03055756,0.03604497,-0.00956433,0.0384558,-0.20370741,0.09086668,-0.03422461,0.04748429,0.01443402,-0.00667214,-0.01175973,0.06427171,0.04933086,0.04234457,-0.00867686,0.07854982,0.03859154,-0.03541898,-0.01198779,-0.05013276,-0.02314574,0.05108434,-0.03774384,-0.01819094,-0.00566419,-0.01159017,0.00780174,0.03894998,0.001729,0.00158943,0.06114915,-0.07957385,0.02493847,0.01207546,0.02624779,0.02999024,0.04464217,0.0148675,0.00420512,-0.02388006,0.00347972,0.10487133,-0.00101874,0.00695799,-0.0191251,-0.0207101,0.05203351,-0.02741992,-0.01379619,-0.0610668,0.00634685,0.04998346,0.02257189,-0.01260755,0.09302944,-0.02213544,-0.00930341,-0.00384938,0.00361188,-0.02167775,0.05806194,-0.00355698,-0.05753589,0.03941079,0.02759386,-0.00938751,-0.04229705,-0.03204341,0.03984325,0.04595851,-0.00363333,0.00002805,0.02766164,-0.03273604,-0.12297447,-0.05186234,-0.02290672,-0.0148602,-0.01442482,0.01234736,0.01071134,0.03600946,-0.02971577,-0.02015237,0.03751668,-0.1339784,-0.04058252,0.05910014,0.02133395,-0.00902579,0.00046471,0.02888528,0.00346953,-0.03604804,-0.04153317,-0.03902205,0.00448312,-0.00864246,0.07975648,0.0584918,-0.03904903,-0.02229659,-0.02597661,-0.0435435,-0.0497914,0.08567683,-0.03418,-0.10299543,-0.03958005,0.04085162,-0.06561303,-0.08949513,-0.0247973,-0.03189974,-0.03193255,0.02941368,0.0750058,-0.01292142,-0.10941909,-0.04814522,-0.07668389,-0.01092428,0.03136401,-0.04487813,-0.0309472,-0.00886115,-0.00507123,-0.08553285,0.03258953,-0.01836162,0.04182397,0.03499528,-0.05621174,0.04284618,-0.0261302,0.01349935,-0.04331167,-0.01014054,-0.01033009,-0.03780486,0.0535148,0.00213822,-0.05331665,0.00232127,0.0052216,0.03247071,-0.05516504,0.04476233,-0.01318749,-0.06449933,0.12903136,0.02903734,-0.04558364,0.01869869,0.01883659,0.06274372,-0.03583135,0.01732621,0.00180988,0.01448563,-0.03692054,0.02924769,0.0213237,0.02483367,-0.09526423,-0.20656991,-0.02755409,-0.05478543,0.00782191,0.04023613,-0.01279124,-0.00572905,-0.0395808,0.03091718,0.06563328,0.06570589,-0.06170059,0.03489693,0.08704513,-0.01990329,-0.01413425,-0.09605904,-0.03301413,-0.06550437,0.0264396,0.01934068,0.01978319,0.00487494,-0.08538,0.02721807,-0.0090442,0.16312268,0.03430383,0.01300773,0.02837444,0.06885467,-0.02526602,-0.00176935,-0.05186163,-0.01355909,0.00603959,0.00590846,-0.05925911,-0.0364792,-0.0094838,-0.05374594,-0.02886647,-0.03113594,-0.10076883,-0.04772701,0.01696666,-0.04277991,0.06467872,-0.00946797,0.03584726,0.05981399,-0.01881744,0.04257138,-0.00617736,0.03733661,-0.05248045,-0.09032,0.00502775,0.01108704,0.05929859,-0.01645623,-0.00668499,0.04464528,-0.03384236,0.04434349,0.01376489,0.01808411,-0.00746858,0.00441301,-0.00695883,-0.04460611,0.09219666,-0.00512212,0.01637135,0.01649988,0.01229009,0.0664536,-0.02839579,-0.0205486,-0.02067614,0.01260461,-0.04041887,0.06121511,0.05064626,0.03277742,0.02185667,0.04927739,0.02088957,-0.0129839,-0.02313188,-0.0042531,0.03015356,-0.04592634,0.03039202,0.03263115,0.00384577,-0.23641554,0.03534882,-0.03149344,0.05333263,-0.00147322,-0.02636374,0.01458652,0.03439321,0.02877338,-0.01369562,0.05877175,0.05186991,0.04320879,-0.00172917,0.01314193,-0.00720571,0.00844683,-0.01496962,0.08562744,0.01909062,0.10268469,0.03996687,0.24196817,-0.02584298,-0.00100078,0.02404208,-0.00285355,0.034217,-0.09479664,0.02867007,0.01712952,0.03426499,0.0271676,-0.00819728,-0.01849,-0.00767626,0.0090087,0.04560257,-0.01537893,0.01655496,-0.0970373,0.01365213,0.03487017,0.03463779,0.14523773,0.0095288,-0.01769648,-0.0898348,0.04760071,0.05906156,-0.10806357,-0.05619042,-0.0175895,-0.02371823,0.01713715,0.04537816,0.01396032,-0.00818001,0.01093573,0.00162043,0.04816625,0.05206596,0.0657028,0.05504028,0.02165728],"last_embed":{"hash":"bjwwhe","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"bjwwhe","at":1753423627429},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies","lines":[8,217],"size":26224,"outlinks":[{"title":"_**filters: Reduction parameters, eliminating restrictions in lotto software**_","target":"https://saliu.com/bbs/messages/919.html","line":17},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":19},{"title":"_**odd, even, low, high numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":34},{"title":"_**Lottery Sums, Lotto Sums, Sum-Totals in Lotto**_","target":"https://saliu.com/forum/lottery-sums.html","line":35},{"title":"_**sums of combinations, root sums (Fadic addition)**_","target":"https://saliu.com/strategy.html","line":35},{"title":"_**lotto decades, last digits**_","target":"https://saliu.com/decades.html","line":36},{"title":"_**largest number less than a user-chosen level (e.g. the biggest number in a lotto drawing is under 31 = <u>birthday numbers</u>**_)","target":"https://saliu.com/bbs/messages/910.html","line":37},{"title":"_**lowest lotto number higher than a user-chosen level (e.g. the lowest number in a lottery draw is above 31 = <u>anti-birthday-numbers</u>)**_","target":"https://saliu.com/bbs/messages/898.html","line":38},{"title":"_**ranges of lotto numbers, Excel spreadsheets**_","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":39},{"title":"_**groups of favorite numbers chosen by lottery players**_","target":"https://saliu.com/lotto-groups.html","line":40},{"title":"The winning lottery strategies are created by best lotto software for any jackpot game in the world.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":54},{"title":"_**Create Pick Lottery Strategies from Restriction Reports**_","target":"https://saliu.com/pick-software.html","line":56},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":58},{"title":"The filter report generator is a function of the best looking lottery software piece of art.","target":"https://saliu.com/ScreenImgs/lotto-filters.gif","line":60},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":68},{"title":"_**<u>BRIGHT Software</u> for Lottery, Lotto, Pick 3 4 Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":69},{"title":"_**<u>ULTIMATE Software</u> for Lotto, Pick Daily Lottery, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":70},{"title":"_**Winning Lottery Strategies, Systems, Software**_","target":"https://saliu.com/LottoWin.htm","line":78},{"title":"_**Skips Systems Software, Strategies for Lottery, Lotto, Gambling, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":82},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":83},{"title":"_**Powerball, Mega Millions, Strategy, Skip Systems**_","target":"https://saliu.com/powerball-systems.html","line":84},{"title":"_**<u>Wonder Grid</u> Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":92},{"title":"_**<u>Magical Lotto Wheel</u>: Lottery Pairs System, Pairing Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":93},{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":94},{"title":"_**Lottery <u>Wonder-Grid</u>, Lotto Pairs Strategy Software, Analysis**_","target":"https://saliu.com/bbs/messages/grid.html","line":95},{"title":"_**Lottery Pairs, Lotto Frequency, <u>Lotto Wonder-Grid</u>**_","target":"https://saliu.com/forum/lottery-pairs.html","line":96},{"title":"_**Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":97},{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":105},{"title":"_**Lotto Frequency Strategy, Lottery Software**_","target":"https://forums.saliu.com/lotto-frequency-efficiency.html","line":106},{"title":"_**Lottery Strategy on Positional Frequencies**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":107},{"title":"_**<u>Delta</u> Lotto, Lottery Systems, Strategies**_","target":"https://saliu.com/delta-lotto-software.html","line":113},{"title":"_**Lottery Deltas Build Effective Lotto Strategies, Systems**_","target":"https://saliu.com/bbs/messages/648.html","line":114},{"title":"_**<u>Markov Chains</u>: Followers, Pairs, Lottery, Lotto, Systems, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":120},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Programs**_","target":"https://saliu.com/Markov_Chains.html","line":121},{"title":"_**All Lotto Numbers: Professors Win UK Lottery Jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":127},{"title":"_**Jackpot Lottery Strategy: 12 Numbers Combinations, Lotto-6 Wheels System**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":133},{"title":"_**Lotto Strategy, Software: <u>12-Numbers</u> Combinations Applied to 6-Number Lottery Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":134},{"title":"_**Lottery Strategy, Software: <u>10-Numbers</u> Combinations Wheeled to 5-Number Lotto Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":135},{"title":"_**Lottery, Lotto Strategy in Reverse: Turn Loss into Win**_","target":"https://saliu.com/reverse-strategy.html","line":145},{"title":"_**Lottery Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":146},{"title":"_**Lottery Strategy Reversed Decades, Last Digits, Odd Even**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":147},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":148},{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":154},{"title":"_**Create, Make Lotto Wheels, Lottery Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":155},{"title":"_**The Best <u>Lotto Wheels for 9, 12, 18, 21 Numbers</u>: 4-in-6 Minimum Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":157},{"title":"**_The Best On-The-Fly Wheeling Software Applies Real Lottery Filtering_**","target":"https://saliu.com/bbs/messages/wheel.html","line":159},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":160},{"title":"_**Lottery Wheeling Software: Convert Systems to Player's Tickets**_","target":"https://saliu.com/bbs/messages/857.html","line":161},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":162},{"title":"_**Software to Combine Lottery Strategies, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":170},{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":186},{"title":"_**Lottery, Lotto: Software, Strategies, Systems, Wheels**_","target":"https://saliu.com/content/lottery.html","line":190},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage**_","target":"https://saliu.com/lottery-numbers-loss.html","line":192},{"title":"_**Playing Lottery Strategies, Lotto Strategy Means Start**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":194},{"title":"_**The Best Strategy in Lottery, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":196},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":198},{"title":"_**Judge Best Lotto Software, Lottery Strategy, Theory**_","target":"https://saliu.com/bbs/messages/623.html","line":200},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":202},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":203},{"title":"Lottery software programs ready to create the best lotto strategies to win big money.","target":"https://software.saliu.com/HLINE.gif","line":205},{"title":"**Roulette**","target":"https://download.saliu.com/roulette-systems.html","line":207},{"title":"**Sic Bo**","target":"https://forums.saliu.com/strategies-casino-war-sic-bo-gambling-formula.html","line":207},{"title":"**Artificial Intelligence**","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":207},{"title":"**Craps**","target":"https://saliu.com/bbs/messages/504.html","line":207},{"title":"**Sports**","target":"https://saliu.com/betting.html","line":207},{"title":"**Blackjack**","target":"https://saliu.com/blackjack-strategy-system-win.html","line":207},{"title":"**Baccarat**","target":"https://saliu.com/winning_bell.html","line":207},{"title":"**_Software_ Home**","target":"https://software.saliu.com/index.html","line":207},{"title":"**Live Sports**","target":"https://software.saliu.com/live-sports-streams.html","line":207},{"title":"This is the site for lotto, lottery software strategies, winning systems for all games in the world.","target":"https://software.saliu.com/HLINE.gif","line":209}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08025072,-0.04333263,-0.0220768,-0.0257745,-0.01874776,0.00604342,0.06039117,0.01744629,0.03800278,-0.01178284,0.0031437,0.00675542,0.07085081,-0.01004661,0.0203317,-0.00582456,0.01399986,0.01212683,-0.04752178,0.00300106,0.10814438,-0.04070842,-0.06058999,-0.04758321,0.06900495,-0.02464016,-0.01989612,-0.06290743,-0.03499684,-0.23623797,0.0059361,-0.00656955,-0.02064659,-0.04969018,-0.08611508,-0.02216966,0.00654811,0.07639833,-0.05136986,0.06204937,0.00721485,0.01208425,0.00678918,0.00015106,-0.01670572,-0.01888458,0.00839886,-0.00156409,0.00454579,0.00877661,-0.05322902,0.0476984,-0.00098805,0.01180374,0.02960305,0.0284679,0.05042104,0.10016542,0.03694175,0.0352235,-0.0073445,0.04702541,-0.19839159,0.09476953,-0.03962196,0.03752897,0.02307395,-0.01000655,-0.0159107,0.08420669,0.05149237,0.05613564,-0.01465125,0.08240536,0.0289927,-0.03498391,-0.00994852,-0.06331693,-0.03644577,0.05731945,-0.04486147,-0.02394658,0.00110217,-0.0172807,0.01209475,0.03756661,-0.00400531,-0.01415595,0.05991251,-0.05371505,0.02615659,0.01835197,0.02114956,0.03823047,0.05127143,0.01111317,0.00831856,-0.02831708,0.00880932,0.10020389,-0.00520357,0.01994335,-0.02271203,-0.02990526,0.04279472,-0.04063179,-0.03036632,-0.04135381,-0.00564267,0.04785476,0.02808509,-0.00342672,0.10497445,-0.01375311,-0.0062282,0.01657271,0.02055026,-0.01481571,0.06797951,-0.00992672,-0.07469047,0.03439665,0.02698327,0.00000128,-0.02633959,-0.01572273,0.04468019,0.05986667,-0.0073581,0.00612766,0.02570617,-0.04800622,-0.12983364,-0.03820498,-0.03041312,-0.01277594,-0.00925441,0.01090316,0.01121313,0.03630747,-0.02878481,-0.02749589,0.03985962,-0.12202635,-0.0401363,0.07592483,0.01928582,-0.0003903,-0.00875773,0.00632633,0.00413254,-0.01471039,-0.03904172,-0.04067537,-0.00803609,0.0028801,0.0695257,0.05416095,-0.04335541,-0.02277703,-0.0293876,-0.04454428,-0.05299835,0.08410138,-0.03149369,-0.08809498,-0.04493822,0.02120505,-0.07202064,-0.08955576,-0.02536154,-0.02951767,-0.0326435,0.02022955,0.07310958,-0.00648392,-0.12247917,-0.03955406,-0.06873696,-0.02533421,0.03304847,-0.03763272,-0.03676325,-0.00734845,-0.00427995,-0.07730312,0.03862016,-0.00312712,0.04878018,0.02936608,-0.06718832,0.02343911,-0.03049984,0.02335278,-0.03202122,-0.01091147,0.00235979,-0.04372269,0.05774805,-0.00665326,-0.04028795,-0.00879297,-0.00158899,0.02138258,-0.04467667,0.05499304,-0.01993473,-0.07597011,0.13341896,0.03345209,-0.05378176,0.00561462,0.02326172,0.06867798,-0.02676108,-0.00541986,0.00891957,0.00645073,-0.04886907,0.0059565,0.02696854,0.02898138,-0.09155427,-0.20217237,-0.02784797,-0.05619529,0.02298406,0.0403926,-0.00569616,-0.02740195,-0.03450195,0.03404507,0.10381852,0.04955574,-0.05976239,0.02837162,0.08541179,-0.01391308,-0.00711839,-0.10606954,-0.03519887,-0.07857194,0.01998535,0.02471696,0.02143122,0.00297064,-0.090827,0.04182454,-0.00102217,0.1631227,0.03394433,0.01411893,0.03288193,0.05838216,-0.01354206,0.0099282,-0.03840793,-0.00593018,0.01403033,-0.00196558,-0.06142779,-0.02841792,-0.01704087,-0.04732055,-0.02656723,-0.04029364,-0.10323203,-0.04209867,0.03692373,-0.03966521,0.09321986,-0.02095965,0.05060045,0.06670628,-0.01928169,0.04751649,-0.00451119,0.0486306,-0.05164988,-0.08178577,-0.00210203,0.01626045,0.06566276,-0.01227855,-0.01895297,0.02690667,-0.03139204,0.04321044,0.0147202,0.03958704,-0.00423672,-0.00625589,0.00168179,-0.04165965,0.08129755,-0.02189248,0.01881699,0.00469894,0.0278226,0.06042279,-0.0112796,-0.00821219,-0.04126566,0.04582535,-0.02108054,0.06059718,0.05764989,0.02159101,-0.0016793,0.03308122,0.02496551,0.00095956,-0.03018312,-0.01866787,0.03683146,-0.04135462,0.0197689,0.02241676,-0.00334645,-0.23157543,0.03508701,-0.03767816,0.04722965,-0.00379082,-0.04191656,0.00938157,0.04173733,0.02245069,-0.01852411,0.04477043,0.05716981,0.04086404,-0.01603603,0.00200825,-0.00711891,0.00829803,-0.00548476,0.09101161,0.02797756,0.11112784,0.03182493,0.24447758,-0.02618852,0.00077039,0.01655422,-0.00112541,0.02874382,-0.10390001,0.01024491,0.01226127,0.02536153,0.03370481,-0.02363064,-0.01632742,-0.02253469,0.01311124,0.0555558,-0.00725565,0.01243569,-0.08913821,0.00919368,0.02462088,0.03131108,0.1387897,0.00543347,-0.0195018,-0.07361595,0.04477089,0.06295064,-0.09093603,-0.04633198,-0.01642711,-0.03873199,0.01984158,0.05114814,0.00756255,-0.00913565,0.00416326,0.00399851,0.03830921,0.05223715,0.05093668,0.0573484,0.01779086],"last_embed":{"hash":"bv3kxp","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"bv3kxp","at":1753423627610},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>","lines":[14,50],"size":4994,"outlinks":[{"title":"_**filters: Reduction parameters, eliminating restrictions in lotto software**_","target":"https://saliu.com/bbs/messages/919.html","line":11},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":13},{"title":"_**odd, even, low, high numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":28},{"title":"_**Lottery Sums, Lotto Sums, Sum-Totals in Lotto**_","target":"https://saliu.com/forum/lottery-sums.html","line":29},{"title":"_**sums of combinations, root sums (Fadic addition)**_","target":"https://saliu.com/strategy.html","line":29},{"title":"_**lotto decades, last digits**_","target":"https://saliu.com/decades.html","line":30},{"title":"_**largest number less than a user-chosen level (e.g. the biggest number in a lotto drawing is under 31 = <u>birthday numbers</u>**_)","target":"https://saliu.com/bbs/messages/910.html","line":31},{"title":"_**lowest lotto number higher than a user-chosen level (e.g. the lowest number in a lottery draw is above 31 = <u>anti-birthday-numbers</u>)**_","target":"https://saliu.com/bbs/messages/898.html","line":32},{"title":"_**ranges of lotto numbers, Excel spreadsheets**_","target":"https://saliu.com/Newsgroups.htm#Spreadsheet","line":33},{"title":"_**groups of favorite numbers chosen by lottery players**_","target":"https://saliu.com/lotto-groups.html","line":34}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07982583,-0.02910077,-0.02193537,-0.01404709,-0.02743789,-0.00115655,0.04690087,0.02014701,0.04638694,-0.00240843,-0.01358414,-0.01158079,0.08083183,-0.0195345,0.01561035,-0.02061457,0.02928932,0.01458724,-0.04045435,-0.00150031,0.1127119,-0.03625797,-0.0625106,-0.04965786,0.07232109,-0.0323288,-0.00466541,-0.05480525,-0.02870638,-0.2351509,-0.00098991,-0.01973637,-0.02469965,-0.05112611,-0.07971007,-0.01188573,0.01333835,0.0697726,-0.05882599,0.06135218,0.00225747,0.00840411,0.00800573,0.00536065,-0.01545472,-0.02045267,0.0067802,0.0023958,0.01241708,-0.00234136,-0.05240464,0.04499653,-0.00523524,0.00797637,0.02307535,0.04054488,0.05213914,0.11722511,0.03208504,0.02793146,-0.01517437,0.04536276,-0.20287725,0.09743646,-0.02645205,0.0411075,0.01179505,-0.00898877,-0.00157245,0.07782811,0.04957965,0.05676877,-0.03233859,0.08167393,0.03456626,-0.03608138,-0.02182094,-0.05738937,-0.04312139,0.04839583,-0.04138959,-0.03185922,0.00331268,-0.01229107,0.0191964,0.03882689,-0.00077394,-0.00842417,0.05177948,-0.0778341,0.03122899,0.01495323,0.0308216,0.04394698,0.047411,0.01271682,0.01283935,-0.02467609,0.01698639,0.10006786,0.00085853,0.03553566,-0.01101495,-0.02474528,0.04907845,-0.05161704,-0.02209673,-0.05106868,-0.00876033,0.03554372,0.04178285,0.01087623,0.10110418,-0.02124573,-0.01732363,0.00407487,0.0243567,-0.00094192,0.06655257,-0.0107109,-0.07615403,0.03121883,0.02510446,0.00719775,-0.03240533,-0.00517681,0.04381981,0.05753025,0.00292906,-0.00067918,0.0194364,-0.04149587,-0.12920216,-0.05683908,-0.02870536,0.00297561,-0.01003338,0.01283828,0.00123152,0.03136086,-0.030728,-0.03659348,0.02119556,-0.11636064,-0.03351878,0.0826476,0.01924583,0.01102851,-0.01725235,0.00856644,-0.00465403,-0.01545323,-0.01802326,-0.03424531,-0.01358957,0.00009696,0.06327946,0.05197787,-0.03777866,-0.01331491,-0.01086494,-0.03584071,-0.05800755,0.08918072,-0.01952786,-0.09337179,-0.050376,0.02598103,-0.07520454,-0.09016704,-0.02931703,-0.02411016,-0.02546364,0.01336497,0.07504471,-0.00771258,-0.11832522,-0.02616896,-0.08078782,-0.03198383,0.03554539,-0.0472836,-0.04640813,-0.00003368,-0.00897517,-0.08185451,0.02823002,-0.01284489,0.05360998,0.04779224,-0.08437769,0.01515266,-0.02076556,0.01449612,-0.0310542,-0.0174867,0.01499941,-0.04582919,0.05732212,-0.00854798,-0.04181344,-0.00157935,0.00741366,0.02060461,-0.0486587,0.0639018,-0.02090839,-0.07081167,0.1249368,0.04454836,-0.05098895,0.02004399,0.02241346,0.05651513,-0.01566312,0.00457881,-0.0076332,0.02159682,-0.04755715,-0.014259,0.02784778,0.01140169,-0.09782618,-0.1992076,-0.02853467,-0.05597302,0.02172557,0.03755967,-0.01443197,-0.02765531,-0.04074093,0.02993113,0.10699497,0.05941556,-0.0448576,0.03217696,0.08356062,-0.01328628,0.00123351,-0.08155821,-0.03068742,-0.07059244,0.01630972,0.03334662,0.02746836,0.00557713,-0.08782393,0.02013602,-0.00452423,0.15955365,0.02719914,0.01953122,0.03562803,0.05542737,-0.00232578,0.00408816,-0.04065725,0.00064154,0.02069397,-0.0238075,-0.05726695,-0.03078387,-0.02421902,-0.04932448,-0.03032323,-0.03959474,-0.1023937,-0.03750283,0.04202584,-0.04119271,0.09667381,-0.01997116,0.05556662,0.06195616,-0.02780153,0.04394977,-0.00423821,0.05774234,-0.03979416,-0.07953136,-0.01105038,0.01755436,0.0361352,-0.02002913,-0.03112908,0.02430738,-0.01800302,0.04433713,0.0203266,0.02563589,-0.00166213,-0.00755246,0.0074741,-0.04797899,0.08736901,-0.01467371,0.00830017,-0.01611289,0.03584408,0.03262436,-0.01149599,-0.00994568,-0.02829191,0.04218861,-0.03708132,0.06246134,0.05575814,0.02828236,0.0093908,0.04279173,0.02542902,-0.00592368,-0.03602896,-0.023842,0.04471305,-0.04385614,0.03101242,0.03541941,0.0021634,-0.24164349,0.03184618,-0.04020339,0.04490504,0.0035895,-0.03494246,0.01974,0.02310568,0.02507758,-0.0332426,0.03564758,0.04973274,0.05253234,-0.01769682,-0.01195979,0.01154219,0.01035941,-0.00651357,0.07897892,0.03746664,0.09870014,0.04241433,0.24756265,-0.02011349,-0.00377121,0.01247431,-0.00042979,0.01486113,-0.1039948,0.01895767,0.02387526,0.02914051,0.04053077,-0.01507757,-0.02033152,-0.01956917,0.00664884,0.05317751,0.00436373,0.00714664,-0.08996944,0.00522861,0.0275642,0.02699406,0.1455076,0.00043336,-0.03261343,-0.07704827,0.0456069,0.06469256,-0.0795377,-0.0408456,-0.01664148,-0.02461163,0.03091179,0.04363933,-0.00103164,-0.00953369,0.01422775,0.00905096,0.03734389,0.03867522,0.03804587,0.06043524,0.02459633],"last_embed":{"hash":"1oxq7cw","tokens":437}}},"text":null,"length":0,"last_read":{"hash":"1oxq7cw","at":1753423627760},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{3}","lines":[20,27],"size":1647,"outlinks":[{"title":"_**filters: Reduction parameters, eliminating restrictions in lotto software**_","target":"https://saliu.com/bbs/messages/919.html","line":5},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05782687,-0.01048691,-0.00609907,-0.01413005,-0.07035388,0.03026314,0.01607811,0.00843947,0.01237328,-0.01466927,-0.00955211,0.01826048,0.05934384,0.0076443,-0.00557229,-0.00609637,-0.01630727,-0.00698936,-0.07207739,-0.02271866,0.11776622,-0.03010324,-0.08535221,-0.0289598,0.05778931,-0.010707,0.01360405,-0.08473606,0.0004464,-0.23541288,0.0088458,-0.00137738,-0.00762462,-0.04076602,-0.08098008,-0.01893756,-0.04302927,0.08643508,-0.07237468,0.07518107,0.02699311,0.03526373,-0.00351324,-0.02185269,-0.02619505,-0.04152924,0.00392041,0.00064186,0.02004679,-0.02528257,-0.02612116,0.05447505,0.02880863,0.00943339,0.05284346,0.00842966,0.05153146,0.09838717,0.03356407,0.03700501,-0.01380728,0.07944347,-0.1873899,0.0585514,-0.04457341,0.05494567,0.0200922,0.01262009,-0.03029973,0.09389943,0.05043475,0.04413412,-0.01542217,0.03556411,0.01548064,-0.01021944,-0.04022385,-0.06708536,-0.05598573,0.06307673,-0.03865407,-0.0151855,0.0161178,0.00198885,0.0138322,0.02780906,-0.00532574,-0.0133415,0.07256503,-0.01092397,0.04713128,0.04206628,0.01130671,0.03639953,0.02668589,0.04341049,0.03696499,-0.06371978,0.00382086,0.12367293,0.02059409,0.04154269,0.02557177,-0.01920635,0.03454656,-0.03831165,-0.02790117,-0.05589239,-0.00535112,0.02082317,0.02561011,0.00429775,0.1041204,-0.05270771,-0.01933379,0.01169789,0.03432046,-0.01546664,0.0468661,0.00661941,-0.05664695,0.01022943,0.01488549,-0.02286673,0.02051937,0.02093682,0.04248276,0.08962018,0.02597793,0.02988604,0.01812315,-0.05086914,-0.11700385,-0.04979448,-0.04020391,-0.01799053,-0.01259613,-0.00010934,-0.0032702,0.01123532,-0.01537526,-0.01417665,0.04423952,-0.11490782,0.01119858,0.09495831,0.03921173,0.0290947,-0.03310942,-0.02348693,-0.0222183,-0.03015952,-0.0718512,-0.04743421,-0.00849682,0.01931546,0.01627489,0.06345356,-0.03431734,-0.01090893,-0.04703362,-0.03916889,-0.03717328,0.1144182,-0.01508677,-0.11190545,-0.03117438,-0.00401582,-0.06005799,-0.09181187,-0.04907431,0.00241454,-0.03497492,0.02511257,0.07560112,0.00271287,-0.12935989,-0.04083556,-0.05908002,-0.00129827,0.03041386,-0.01755088,-0.06164137,-0.0141939,-0.01589866,-0.06868733,0.05116469,-0.01082397,0.0499278,0.06767786,-0.07230577,0.01699354,-0.01638261,0.02601297,-0.03495099,-0.00796667,0.00292056,-0.06058017,0.06786731,-0.05281219,-0.02403108,-0.01466658,0.00361451,0.03487703,-0.06140238,0.04939216,-0.01297224,-0.08044739,0.09666788,0.02506209,-0.0617612,0.00422808,0.02755168,0.061703,-0.01617427,0.00415072,0.02136729,0.00055974,-0.02044501,-0.00925543,0.06475903,0.00235368,-0.07033025,-0.17916052,-0.04383771,-0.03117301,0.02764032,0.03307724,-0.02704152,-0.0160668,-0.03186858,0.00273815,0.08257008,0.04124155,-0.040689,0.01101027,0.07524796,-0.01361515,-0.0100529,-0.09122002,-0.02997008,-0.04397389,0.02393595,0.0422448,0.00717719,-0.03428498,-0.10774018,0.05110305,-0.01743743,0.13715516,0.00127298,0.01751393,0.00950607,0.04449978,-0.00135729,0.00660558,0.01938352,0.00747183,0.00494721,-0.01547619,-0.04874468,-0.02788521,-0.01714586,-0.00288068,-0.01206826,-0.03815416,-0.09150364,-0.04728383,0.0247915,-0.04344036,0.13443881,-0.0012068,0.04660928,0.04952978,-0.03737263,0.07467704,-0.01041546,0.0669009,-0.02428016,-0.09218512,0.00284714,0.0001069,0.0598677,-0.01929632,-0.02789852,0.03993836,-0.00050921,0.06453533,0.03105772,0.00417711,0.01196904,-0.01183732,-0.00505397,-0.03479597,0.06209275,-0.00840664,0.03576518,-0.04940648,0.06640842,0.030977,-0.0319518,-0.00472252,-0.03320588,0.06399328,-0.03929457,0.03449708,0.05610001,0.01652857,0.03450617,0.0447776,0.04737921,0.0076076,-0.03725384,-0.04154405,0.0085004,-0.04038319,0.0212191,0.03089746,-0.01864603,-0.23731925,0.03016864,-0.03680326,0.04902102,0.01225584,-0.00090128,-0.00122534,0.04259892,0.01098556,-0.05213028,0.0553715,0.04652676,0.03103954,-0.01968352,-0.00922895,-0.01019713,-0.0065214,-0.05256006,0.07335908,0.02002441,0.10668235,0.04395901,0.22686887,-0.02091276,0.00329995,-0.00262889,0.00132627,0.00028164,-0.11678217,-0.01644169,0.0163992,0.00338303,0.04401848,-0.04835477,-0.03888875,0.00944762,0.03110702,0.05530915,-0.01430494,0.00145823,-0.07566362,0.01007599,-0.0091491,0.01418016,0.13059355,0.01309561,-0.01871294,-0.0684828,0.01478851,0.03897561,-0.09620038,-0.03801445,0.0054755,-0.02508206,0.0112341,0.0579166,0.0266046,-0.01217357,0.01200186,0.03267102,0.05198143,0.05553683,0.05037459,0.05857648,0.01069621],"last_embed":{"hash":"lasygz","tokens":180}}},"text":null,"length":0,"last_read":{"hash":"lasygz","at":1753423627925},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{5}","lines":[30,33],"size":669,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09648862,-0.01281644,-0.01252752,-0.03860697,-0.05616001,0.02408265,0.01624173,0.01418138,0.0115431,-0.04332308,-0.01028936,-0.00433612,0.06689914,-0.00204763,0.00253108,-0.02391789,0.01833916,-0.00677906,-0.0290409,-0.02593568,0.09401006,-0.00457327,-0.05118192,-0.03608477,0.02805076,-0.0121524,-0.02489675,-0.03615815,0.00660832,-0.2140003,0.01192605,-0.05105537,0.01588183,-0.050381,-0.07384551,-0.02335451,-0.00866449,0.09002855,-0.0542538,0.07538799,-0.00075234,0.03936154,0.01314466,-0.0641035,0.02000506,-0.05738196,0.04726595,-0.01005105,0.02955738,0.01230197,-0.04663777,0.03279943,0.04105144,0.03323174,-0.00815577,0.04310107,0.0367513,0.12674032,0.08810191,0.06324252,-0.0217313,0.06503268,-0.18386912,0.10262733,-0.03887465,0.04313919,-0.00076062,-0.0086443,-0.00067692,0.0629028,0.04459732,0.07001766,-0.01305502,0.05304641,0.03481896,-0.00138092,-0.06518168,-0.08116511,-0.04256827,0.03132748,-0.020322,-0.049389,0.02382749,-0.01140268,0.01613648,0.04153678,0.02313589,-0.01432522,0.09477767,-0.00594087,0.01963087,0.00457105,0.03780182,0.04169626,0.0140133,0.01995662,0.034491,-0.01324933,-0.00213779,0.14705338,-0.01423571,0.0274998,-0.01025388,-0.05247185,0.04978771,-0.0421492,-0.01671542,-0.01061504,-0.03046162,0.04451845,0.01529996,0.005592,0.09766603,-0.05540494,0.01005913,0.00522709,-0.00822613,-0.01559416,0.01582732,-0.00083374,-0.0555933,0.00312608,0.01448274,-0.03169255,0.01513311,0.03236513,0.04757896,0.06763714,0.00274067,0.04797469,0.01890694,-0.0076712,-0.13125376,-0.08490958,0.00526222,0.00059826,-0.00222817,-0.01366949,-0.03655877,0.05165687,-0.04816978,0.00359417,0.00836003,-0.08932871,0.000691,0.10058364,0.02190679,0.01492539,-0.0122055,0.00054913,0.00119574,-0.01035564,-0.03251047,-0.02765973,-0.03298833,0.03041024,0.04820113,0.08179958,-0.03393323,-0.02055629,-0.0321505,-0.02946696,-0.03134867,0.1142929,-0.01569223,-0.11670583,-0.04343555,-0.00492257,-0.05028733,-0.03610559,-0.04813082,-0.01059021,-0.03401902,0.02179749,0.07175136,-0.01152006,-0.11010933,-0.02709287,-0.06263978,-0.03336941,0.03809287,-0.01562875,-0.03518298,0.01334165,-0.05581075,-0.08923189,0.05243904,-0.00675804,0.02537192,0.04955638,-0.08543098,-0.00947784,-0.03034393,0.0389851,-0.02351712,-0.03401065,0.04226193,-0.04058942,0.08057132,-0.06099699,0.01186271,-0.01937143,0.02102237,0.03494653,-0.08699731,0.01482524,-0.06678567,-0.06087958,0.0910839,0.03337385,-0.03709037,0.01655866,0.04966539,0.05590354,-0.03417557,-0.01706564,-0.0081178,0.00945988,-0.01027653,-0.00655726,0.03594791,0.01391465,-0.10480998,-0.23014735,0.01359323,-0.0247078,0.04618487,0.03089267,-0.02593078,-0.02623946,-0.03961623,0.03341078,0.11208334,0.07769527,-0.02109847,0.02531466,0.05493278,0.00375295,0.00527827,-0.05680769,-0.00146272,-0.0547487,-0.03496116,0.01901543,-0.00077057,-0.03112603,-0.08416898,0.06152755,-0.03977966,0.14095463,0.01804887,0.01256436,-0.01393871,0.07238203,-0.01413265,-0.00687859,-0.04223491,-0.00604036,0.02791155,-0.00706581,-0.05235915,-0.02811586,-0.02810275,-0.01592927,-0.05971535,-0.02364734,-0.11911771,-0.03448785,0.03845234,-0.00961326,0.09466472,-0.02763353,0.02321318,0.0456696,-0.05759215,0.10135821,-0.0118271,0.03240668,-0.07924092,-0.06076119,-0.02773283,0.02261356,0.07076139,-0.02846189,-0.02473286,0.00191346,0.00521229,0.05283768,0.01749855,0.01476684,0.00903029,0.01162692,-0.02022563,-0.02032975,0.06373133,-0.00944382,0.03954424,-0.01820488,0.0397153,0.03090334,-0.01633765,-0.00980612,-0.04538596,0.0343397,-0.01773813,0.07703202,0.06588615,0.0416976,0.02488876,0.02950331,0.01908558,-0.02961322,-0.04032224,-0.014506,0.02050003,-0.04494183,0.00133414,0.01464864,0.01552623,-0.24456014,0.03574773,-0.02296344,0.03816994,0.01211014,-0.00536421,-0.00331792,0.01656091,0.03954955,-0.04623434,0.03810662,0.06049904,0.02661265,-0.0241381,0.00265658,-0.00693439,0.01785309,-0.02690559,0.07828613,-0.01007262,0.06004735,0.03922876,0.24030195,-0.00618665,-0.03005593,0.00661634,0.00361197,0.00698967,-0.07906371,-0.02166628,0.04765931,-0.0010385,0.04043004,-0.00162237,-0.0201572,-0.0191464,0.02893183,0.03524273,-0.03926269,-0.00708153,-0.03672988,0.01348905,0.01674061,0.02150768,0.10249054,0.01486782,-0.03638997,-0.07729954,0.00292728,0.071013,-0.06212459,-0.05400709,-0.01360757,-0.01134933,0.02656766,0.05961482,-0.00665932,0.01860016,-0.03153833,-0.00247685,0.02624607,0.05375591,0.06716159,0.04788214,0.0395503],"last_embed":{"hash":"8waez8","tokens":159}}},"text":null,"length":0,"last_read":{"hash":"8waez8","at":1753423627979},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{8}","lines":[37,40],"size":564,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05145562,-0.01083031,-0.01703854,-0.01022336,-0.03943056,0.04168985,0.02336235,0.00752045,0.02384608,-0.03939288,0.00250267,-0.01807889,0.07316753,0.00001172,0.00480667,0.00444856,-0.01049622,0.00238148,-0.07460287,-0.00567016,0.08883367,-0.02914204,-0.0374779,-0.04410419,0.02817883,-0.01722431,-0.0097124,-0.06744285,-0.00685393,-0.24735968,0.01871784,-0.01985426,-0.03681332,-0.0258424,-0.08085717,-0.03265427,-0.02694374,0.10324979,-0.02650082,0.08075877,0.00688079,0.03456609,-0.02849128,-0.0386088,-0.0105238,-0.01508111,-0.01699262,-0.01651935,0.03095338,-0.01088593,-0.03179311,0.01961249,0.04489042,-0.01005553,0.03565719,0.02852084,0.03045742,0.11314519,0.06495859,0.03842386,-0.0235053,0.07354743,-0.17454134,0.0488989,0.00518982,0.0185384,-0.00093725,0.00871565,-0.01825302,0.08706211,0.01790002,0.08288994,-0.02908062,0.05265718,0.04930522,-0.02376302,-0.0684448,-0.08336958,-0.06624559,0.04617427,-0.03794562,-0.06229871,0.00633013,-0.00905141,0.0300925,-0.00588854,0.00826831,-0.00669212,0.08624467,-0.01029949,0.04994746,0.04664242,0.03074591,0.05442665,0.03977114,0.02846415,0.03840135,-0.03752838,0.00066751,0.11431716,0.0309998,0.02904709,-0.00866723,-0.02511315,0.04606567,-0.04904214,-0.0364191,-0.01336929,-0.04549785,0.04030005,0.03444922,-0.00891087,0.09787742,-0.04204402,-0.00216193,0.01491358,0.02639093,0.00662203,0.00768437,-0.00098075,-0.0578305,0.0513226,0.02131875,-0.00571598,0.02423508,0.01553568,0.02213825,0.08087803,0.04519883,0.03066356,0.00787922,-0.06441846,-0.11239512,-0.06802041,-0.02267811,-0.01697766,0.03236944,0.04066014,0.02513928,0.04499748,-0.01972595,-0.02680058,0.04789533,-0.09358198,-0.00790357,0.10732496,0.02284124,0.02499564,-0.0012893,-0.01426955,-0.03656359,-0.02238994,-0.01230728,-0.05402963,-0.02283413,0.01392737,0.03695614,0.09833775,-0.05249409,-0.03350633,-0.03672101,-0.0459337,-0.0543614,0.11837317,-0.03318673,-0.07835916,-0.04549461,0.0045958,-0.07833274,-0.03192224,-0.04615875,0.01036627,-0.0086382,-0.00408908,0.07785018,-0.01110895,-0.1093385,-0.0525375,-0.02952821,-0.01101666,0.04943256,0.004391,-0.0646422,0.01924497,-0.04956722,-0.08395742,0.04654688,-0.00433797,0.04536064,0.01968016,-0.0818045,-0.0367812,-0.03278988,0.06219964,-0.00929175,-0.03121137,0.03735587,-0.02544921,0.05431092,-0.02826727,-0.00556564,-0.0285443,0.00568624,0.00209906,-0.07850148,0.05613203,-0.02282174,-0.07882958,0.10898677,0.02150946,-0.05824832,0.01307082,0.02498082,0.08881422,-0.02713912,-0.00412834,0.00393937,0.0193952,-0.01148681,-0.0151616,0.02968781,0.01085763,-0.06638899,-0.20698658,-0.02340425,-0.03048783,0.01399357,0.06253331,-0.00098522,-0.00921393,-0.01499526,0.03044702,0.07301132,0.03113065,-0.0205231,-0.00616141,0.04791087,-0.01921071,0.02701095,-0.10155446,-0.00652193,-0.05480503,-0.00194979,0.01934672,0.02712166,-0.04247858,-0.07699596,0.06987746,-0.04303515,0.13885519,0.01856265,-0.02109529,-0.02077725,0.04823438,-0.00997396,-0.00673292,-0.00883529,-0.00473871,0.01398354,-0.0094647,-0.0617671,-0.02463298,-0.05757719,0.00397091,-0.04059229,-0.06234906,-0.09132373,-0.04473049,0.06552781,-0.06365648,0.10001431,0.02448938,0.05330746,0.03821775,-0.0468962,0.07117867,-0.01204677,0.0748301,-0.03821352,-0.07533241,-0.0107213,-0.01161709,0.05887285,-0.00796375,-0.0131139,0.02688084,-0.01987693,0.04047642,0.02101596,0.02260331,0.00077139,0.00513205,0.00621475,0.00144458,0.0428401,-0.00088894,0.00517284,-0.05515538,0.02263864,0.03749419,-0.00287849,0.00744267,-0.06551235,0.07841797,-0.03215877,0.05324408,0.03284148,0.01865113,-0.00982761,0.01987294,0.00965533,-0.00693652,-0.02334236,-0.06206406,0.05652897,-0.03309592,-0.01373023,0.03586278,0.01474431,-0.2459334,0.03519316,-0.04280964,0.0427489,0.02035559,-0.0248457,0.01587969,0.02433243,0.01858162,-0.02244801,0.04325862,0.06285056,0.01030838,-0.02636943,0.01380495,-0.03763511,0.01190372,-0.04580608,0.10607923,-0.00114842,0.11661866,0.02638525,0.24296652,0.00999399,-0.02500376,0.00272734,0.01535514,0.01635779,-0.09058582,-0.01522901,0.00613332,-0.00020747,0.03495947,0.00836719,-0.00367821,-0.01331713,0.0234364,0.02788463,-0.01322468,0.00540926,-0.05160956,-0.00589278,-0.01412059,0.00105046,0.13580135,0.04166938,-0.03593505,-0.06579462,-0.00762,0.03988298,-0.08413311,-0.00848356,-0.01389856,-0.03547415,-0.00673377,0.07385284,-0.01922483,0.02011008,0.00468784,0.03069196,0.03284202,0.05574007,0.05478393,0.06261406,0.01334746],"last_embed":{"hash":"jcfez1","tokens":211}}},"text":null,"length":0,"last_read":{"hash":"jcfez1","at":1753423628022},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>I. Filter: The Keystone of Lottery Strategies</u>#{16}","lines":[49,50],"size":703,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08952704,-0.01743964,-0.03829628,-0.03174488,-0.05335357,0.04932399,0.01894358,-0.00226224,0.01718268,-0.0273088,-0.00169593,0.01221855,0.06136785,0.03424946,0.02586684,-0.02348669,0.00300825,-0.02593066,-0.04652382,-0.0024556,0.0803257,-0.01150377,-0.05535063,-0.05543924,0.03702469,-0.03184063,-0.03375868,-0.06276737,-0.01412096,-0.22982512,0.03134863,-0.01152183,-0.02124587,-0.05216623,-0.08941329,-0.03352692,-0.02531092,0.10689727,-0.04484153,0.05643509,-0.00613117,0.02210801,0.01675567,-0.04102187,-0.00673977,-0.04859175,0.02337344,0.00607402,0.02542859,0.0077935,-0.04977385,0.01094154,0.01728119,0.03006523,0.00737275,0.03781164,0.03172234,0.13908289,0.03823332,0.0561524,-0.00196264,0.05368016,-0.1701431,0.08325763,-0.042036,0.03582221,-0.01779015,0.01604919,0.01345705,0.08673274,0.03028103,0.07722608,-0.04093811,0.05817109,0.03946849,-0.01464385,-0.05353302,-0.07569489,-0.04447222,0.05427572,-0.05394781,-0.04192819,0.0295318,-0.01228254,0.02654358,0.0554779,0.03332725,0.001651,0.07428513,-0.01647283,0.04051284,0.03216759,0.0282775,0.01897973,0.0451206,0.00612728,0.04395404,-0.03278327,0.01376891,0.13125034,0.00410922,0.0161947,0.01608863,-0.0429926,0.03772859,-0.04670404,-0.01884168,-0.03719014,-0.03937027,0.0290052,0.04302889,-0.00830495,0.08274037,-0.03284054,-0.01767446,0.01211828,-0.00263875,-0.02060611,0.02482736,-0.00338028,-0.07435665,-0.00455047,-0.0159549,-0.01487914,0.01624451,0.01751572,0.03933089,0.06236052,-0.00674143,0.02257999,0.0416517,0.02883899,-0.15925205,-0.05186395,-0.02123489,-0.02601547,0.00210803,0.01248688,-0.00884521,0.05258387,-0.02873586,0.00576675,0.05398793,-0.11838417,-0.03397639,0.1132619,0.01478835,0.02112188,-0.01613237,-0.00893274,-0.00182669,-0.00213055,-0.04838637,-0.04874906,-0.01596555,0.03132697,0.0764365,0.06790223,-0.07056691,-0.03458818,-0.04072354,-0.00398441,-0.04934923,0.11049442,-0.0259474,-0.12150767,-0.04146023,0.01040819,-0.06903008,-0.06698997,-0.04061342,-0.01820165,-0.05059667,0.02383353,0.07849012,-0.01049835,-0.1173212,-0.03968719,-0.03036161,-0.01966623,0.04002552,0.00409533,-0.04739897,0.01305345,-0.0346719,-0.08842798,0.07566647,-0.01983597,0.04805437,0.04520984,-0.07354722,-0.0019137,-0.02492448,0.03629235,-0.0267577,-0.01353117,0.01743064,-0.02296769,0.06853594,-0.0255277,0.00966355,-0.01655663,0.00455026,0.01314291,-0.05349889,0.04594657,-0.02540535,-0.05928099,0.11405122,0.03466146,-0.03699433,0.03479442,0.03204798,0.08604681,-0.02110657,-0.01859832,0.00972603,0.02434685,0.00903558,-0.00542061,0.02190489,0.0114423,-0.06300587,-0.19471928,-0.02888096,-0.04012467,0.04707199,-0.00130032,-0.0051371,-0.00606291,-0.02904313,0.03838639,0.12173926,0.0779008,-0.03668445,0.01730998,0.05423564,-0.01146108,-0.00585144,-0.09534595,0.00057296,-0.06726023,-0.00105257,0.01626576,0.00636749,-0.00662518,-0.06342006,0.03873189,-0.04858999,0.14265129,-0.00420493,0.01251498,0.00781108,0.06831355,-0.01485331,-0.02139425,-0.02317543,-0.00069398,0.04877649,-0.00752095,-0.03744461,-0.02996632,-0.01019503,-0.02223699,-0.03970968,-0.02744001,-0.12796365,-0.04115952,0.01440334,-0.00667227,0.0583961,-0.01740776,0.0191815,0.06366379,-0.04449408,0.05559036,-0.00350302,0.04590961,-0.06302263,-0.08731081,-0.01676611,-0.01473481,0.05426839,-0.01601686,-0.02717817,0.00970543,-0.00544359,0.05161186,-0.00853969,0.0296024,-0.00546712,-0.00386479,-0.02573702,-0.01142512,0.04962439,-0.02262835,0.03478852,-0.0154375,0.0361435,0.05488728,-0.01719164,-0.03090592,-0.0562817,0.03430104,-0.03045045,0.05522757,0.07450666,0.02379603,0.00139509,0.05103374,0.00840228,-0.0280047,-0.01878871,-0.00981233,0.00833745,-0.04762954,0.03174791,-0.00742843,0.03472331,-0.24680068,0.04338166,-0.02618143,0.0405166,0.0049919,-0.01795312,0.01914338,0.00165875,0.04842833,-0.04210457,0.0259909,0.04260243,0.03665034,-0.04983972,0.0067824,0.00180263,0.02009474,-0.00932745,0.08190934,-0.01089008,0.0741398,0.03585512,0.24407981,-0.00983535,0.01935047,0.02212562,0.01769156,0.00400592,-0.07547251,0.00394815,0.03836091,0.00759105,0.0860708,-0.0285433,-0.03709736,-0.01549222,0.03002139,0.04206832,-0.01283267,-0.00683623,-0.05302124,0.01924195,0.00057443,-0.00478487,0.12074064,-0.00010905,-0.05262807,-0.06331815,0.00540383,0.07797622,-0.08303867,-0.04917321,-0.02948217,-0.01595174,0.02568984,0.05117562,0.00077703,0.03273297,-0.01668448,0.01294147,0.02548984,0.03094768,0.05500638,0.05345556,0.02215113],"last_embed":{"hash":"rnevmu","tokens":373}}},"text":null,"length":0,"last_read":{"hash":"rnevmu","at":1753423628085},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>","lines":[51,178],"size":15480,"outlinks":[{"title":"The winning lottery strategies are created by best lotto software for any jackpot game in the world.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":11},{"title":"_**Create Pick Lottery Strategies from Restriction Reports**_","target":"https://saliu.com/pick-software.html","line":13},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":15},{"title":"The filter report generator is a function of the best looking lottery software piece of art.","target":"https://saliu.com/ScreenImgs/lotto-filters.gif","line":17},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":25},{"title":"_**<u>BRIGHT Software</u> for Lottery, Lotto, Pick 3 4 Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":26},{"title":"_**<u>ULTIMATE Software</u> for Lotto, Pick Daily Lottery, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":27},{"title":"_**Winning Lottery Strategies, Systems, Software**_","target":"https://saliu.com/LottoWin.htm","line":35},{"title":"_**Skips Systems Software, Strategies for Lottery, Lotto, Gambling, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":39},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":40},{"title":"_**Powerball, Mega Millions, Strategy, Skip Systems**_","target":"https://saliu.com/powerball-systems.html","line":41},{"title":"_**<u>Wonder Grid</u> Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":49},{"title":"_**<u>Magical Lotto Wheel</u>: Lottery Pairs System, Pairing Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":50},{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":51},{"title":"_**Lottery <u>Wonder-Grid</u>, Lotto Pairs Strategy Software, Analysis**_","target":"https://saliu.com/bbs/messages/grid.html","line":52},{"title":"_**Lottery Pairs, Lotto Frequency, <u>Lotto Wonder-Grid</u>**_","target":"https://saliu.com/forum/lottery-pairs.html","line":53},{"title":"_**Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":54},{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":62},{"title":"_**Lotto Frequency Strategy, Lottery Software**_","target":"https://forums.saliu.com/lotto-frequency-efficiency.html","line":63},{"title":"_**Lottery Strategy on Positional Frequencies**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":64},{"title":"_**<u>Delta</u> Lotto, Lottery Systems, Strategies**_","target":"https://saliu.com/delta-lotto-software.html","line":70},{"title":"_**Lottery Deltas Build Effective Lotto Strategies, Systems**_","target":"https://saliu.com/bbs/messages/648.html","line":71},{"title":"_**<u>Markov Chains</u>: Followers, Pairs, Lottery, Lotto, Systems, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":77},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Programs**_","target":"https://saliu.com/Markov_Chains.html","line":78},{"title":"_**All Lotto Numbers: Professors Win UK Lottery Jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":84},{"title":"_**Jackpot Lottery Strategy: 12 Numbers Combinations, Lotto-6 Wheels System**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":90},{"title":"_**Lotto Strategy, Software: <u>12-Numbers</u> Combinations Applied to 6-Number Lottery Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":91},{"title":"_**Lottery Strategy, Software: <u>10-Numbers</u> Combinations Wheeled to 5-Number Lotto Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":92},{"title":"_**Lottery, Lotto Strategy in Reverse: Turn Loss into Win**_","target":"https://saliu.com/reverse-strategy.html","line":102},{"title":"_**Lottery Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":103},{"title":"_**Lottery Strategy Reversed Decades, Last Digits, Odd Even**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":104},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":105},{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":111},{"title":"_**Create, Make Lotto Wheels, Lottery Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":112},{"title":"_**The Best <u>Lotto Wheels for 9, 12, 18, 21 Numbers</u>: 4-in-6 Minimum Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":114},{"title":"**_The Best On-The-Fly Wheeling Software Applies Real Lottery Filtering_**","target":"https://saliu.com/bbs/messages/wheel.html","line":116},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":117},{"title":"_**Lottery Wheeling Software: Convert Systems to Player's Tickets**_","target":"https://saliu.com/bbs/messages/857.html","line":118},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":119},{"title":"_**Software to Combine Lottery Strategies, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":127}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09167018,-0.01547005,-0.03877968,-0.03352205,-0.05395614,0.04596714,0.02650541,-0.00113209,0.02030303,-0.02232682,-0.00845679,0.01180371,0.06571774,0.0321756,0.02483135,-0.02994207,0.00194369,-0.02512628,-0.05047205,-0.00048682,0.08591611,-0.00761201,-0.05545784,-0.05917139,0.03246196,-0.03023462,-0.03893918,-0.05579958,-0.01339731,-0.22098128,0.03022437,-0.00940078,-0.03014163,-0.04872076,-0.09485143,-0.03760634,-0.02819099,0.10307519,-0.04959787,0.05187284,-0.00314024,0.02129611,0.02445523,-0.03935272,-0.00494013,-0.04659021,0.01451213,0.00981263,0.02639739,0.00990147,-0.04715222,0.01576536,0.01286651,0.02576686,0.01440963,0.03730996,0.03211528,0.1427297,0.04121677,0.0620776,0.00064368,0.05148173,-0.17046925,0.0826405,-0.04601934,0.03513732,-0.01497515,0.00754747,0.01219619,0.07521131,0.03180043,0.07335117,-0.03638427,0.0578826,0.04054375,-0.01789081,-0.0557565,-0.08498561,-0.05435257,0.05588259,-0.05406424,-0.03861823,0.03170968,-0.01048294,0.03250045,0.05649719,0.03124683,0.00048025,0.07726403,-0.01815607,0.0429229,0.02848508,0.03150447,0.02178246,0.04202452,0.00996836,0.04687468,-0.03131424,0.01589129,0.13090076,0.00230149,0.01642432,0.01460533,-0.03831019,0.04698582,-0.04912998,-0.02422861,-0.03812285,-0.04141627,0.03197705,0.03905739,-0.00621213,0.08826517,-0.03313251,-0.02550305,0.01250214,-0.00753507,-0.0203328,0.0317121,-0.0039214,-0.06911751,-0.00015905,-0.01915889,-0.02142259,0.01571392,0.02056456,0.0368624,0.05520815,-0.00863519,0.01995634,0.05027342,0.02971284,-0.16198222,-0.04716905,-0.01981767,-0.02872065,-0.00415253,0.00948757,-0.00782842,0.04691745,-0.0301999,0.00719013,0.05827836,-0.1190771,-0.02756877,0.10331516,0.00745269,0.02202898,-0.01798357,-0.00933507,-0.00141494,-0.00193116,-0.0512727,-0.04580053,-0.01446944,0.03167727,0.08079106,0.06815927,-0.06876498,-0.0353289,-0.04625034,-0.00744137,-0.04757348,0.10095809,-0.02926695,-0.11949503,-0.03480545,0.01940127,-0.06921479,-0.06423078,-0.04365649,-0.01981342,-0.05675709,0.02180233,0.07719904,-0.01461888,-0.11816421,-0.03831779,-0.02643904,-0.01348394,0.03451998,-0.00449777,-0.05003881,0.003079,-0.03657907,-0.08889662,0.07242409,-0.02962352,0.05092068,0.04645526,-0.07196854,-0.00210241,-0.02813165,0.03119513,-0.02581127,-0.0120187,0.0149462,-0.02799304,0.07355171,-0.02220164,0.00888717,-0.01408213,0.00430524,0.0109413,-0.05820182,0.04369372,-0.02879022,-0.06356884,0.11328066,0.03752781,-0.03891549,0.03104546,0.02659771,0.08571066,-0.02120739,-0.01340167,0.01349385,0.0200522,0.00906844,-0.0062944,0.01881654,0.00969194,-0.06468868,-0.19254561,-0.03040737,-0.0456644,0.04221207,-0.00227306,-0.00243465,-0.00552537,-0.02984717,0.03895709,0.11615659,0.08560824,-0.03982366,0.01272314,0.05716288,-0.00359356,-0.0037078,-0.08913855,-0.00190513,-0.06381497,0.00138285,0.01749683,-0.00098557,0.00182591,-0.06115287,0.03601043,-0.03885027,0.14011596,-0.00414073,0.01551829,0.00478543,0.07546188,-0.01519206,-0.01647074,-0.0244232,-0.00629606,0.04181894,-0.00803691,-0.04161372,-0.036472,-0.00622937,-0.02821226,-0.03599299,-0.02277111,-0.12700716,-0.03383763,0.01682638,-0.00735948,0.05595353,-0.01604764,0.02530728,0.0629695,-0.04302308,0.05887409,-0.00554741,0.04754825,-0.05945465,-0.08242788,-0.02148953,-0.02047157,0.05598763,-0.01494372,-0.03618418,0.01056903,-0.00167288,0.05133241,-0.00905072,0.03174821,-0.00196488,-0.00066706,-0.02674958,-0.01391348,0.05548498,-0.01974281,0.04246715,-0.00749736,0.0359483,0.06381623,-0.02548736,-0.02466202,-0.05253097,0.02937135,-0.02362075,0.05751866,0.08094516,0.0275708,-0.00364423,0.05830921,0.01202739,-0.02321691,-0.02616709,-0.00691614,0.00469516,-0.04809041,0.02875381,-0.00758283,0.03778794,-0.24556759,0.03544457,-0.02259735,0.03774744,0.004427,-0.02622402,0.01767092,0.00040799,0.05092777,-0.04199949,0.02957157,0.03629193,0.03863532,-0.05541147,0.0058149,-0.00076193,0.02027626,-0.01265371,0.08089127,-0.00614996,0.07083084,0.03877773,0.24422061,-0.01926697,0.02528677,0.0258627,0.01819934,0.00510826,-0.0716178,0.00207133,0.03666828,0.01450822,0.08324036,-0.02715776,-0.03840654,-0.01023767,0.03358751,0.04211142,-0.0166211,-0.00387428,-0.05269883,0.02107058,-0.0044301,-0.0049541,0.12027313,-0.00225267,-0.05187853,-0.06324626,0.01301242,0.07226347,-0.08514337,-0.05324896,-0.02803386,-0.01989181,0.02774577,0.05070755,0.00670446,0.03030381,-0.01242595,0.02129228,0.02803513,0.03397502,0.06170637,0.05484008,0.02265594],"last_embed":{"hash":"mo70in","tokens":442}}},"text":null,"length":0,"last_read":{"hash":"mo70in","at":1753423628216},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#{1}","lines":[53,70],"size":3347,"outlinks":[{"title":"The winning lottery strategies are created by best lotto software for any jackpot game in the world.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":9},{"title":"_**Create Pick Lottery Strategies from Restriction Reports**_","target":"https://saliu.com/pick-software.html","line":11},{"title":"_**Filters, Filtering, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":13},{"title":"The filter report generator is a function of the best looking lottery software piece of art.","target":"https://saliu.com/ScreenImgs/lotto-filters.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10603594,-0.00018285,-0.04275494,0.01072189,-0.01224105,0.05194085,-0.02827477,-0.00761709,0.01714259,-0.02568555,-0.00861836,0.00815143,0.03301018,-0.00081218,0.00387002,-0.02956161,0.01879612,-0.0049141,-0.04892495,-0.01903011,0.10220739,-0.0194311,-0.03828078,-0.05289688,0.04714708,-0.02523001,-0.01557017,-0.02801779,-0.02518245,-0.21427037,-0.00332334,0.00298168,-0.00805143,-0.06512585,-0.10068101,-0.01284249,-0.00536262,0.03478051,-0.02837686,0.02800874,0.00833907,-0.00144827,0.00749318,-0.03538163,0.01457649,-0.06268872,0.03218424,0.00673443,0.04080036,-0.02944731,-0.06939235,0.02830821,0.01843222,0.00730088,0.03793143,0.04092684,0.04313316,0.14128578,0.03668202,0.03346602,0.00051433,0.08350715,-0.18962891,0.08789483,-0.07377427,-0.0047466,0.00977807,-0.01843404,0.01233409,0.05884455,0.04103277,0.03241124,-0.05168334,0.05195676,0.02080015,-0.02589535,0.00450933,-0.08429587,-0.03198482,0.03489894,-0.04792106,-0.03597007,0.00866632,-0.02233575,0.03536469,0.05046553,0.02035218,0.03626832,0.0504156,-0.06043316,0.02522748,0.01168606,0.03264982,0.04028459,0.00632118,0.01630263,0.04846717,-0.03991612,0.03379696,0.13110766,0.01530781,0.03677993,0.01264277,-0.01032462,0.08916651,-0.03816814,0.00429869,-0.06066135,-0.04589602,0.03079402,0.02721428,0.00383324,0.08321345,-0.032492,-0.00789208,0.00989715,-0.03126368,-0.02638036,0.0092516,0.01689198,-0.05497589,0.01757301,0.01800456,-0.01804229,0.00147761,0.00201278,0.06642782,0.02871881,0.01155483,0.04331176,0.04292374,0.03482407,-0.15025645,-0.05132071,0.0147568,0.00129502,0.01105534,-0.04139929,-0.0239215,0.03953887,-0.03527981,-0.0266346,0.06450643,-0.12460308,-0.00881698,0.07507078,-0.00479378,-0.00031674,-0.01925488,0.01419631,-0.00083844,-0.00461423,-0.04220194,-0.01988369,-0.01547816,0.04061742,0.06355529,0.11221797,-0.05920802,-0.05768532,-0.01287582,-0.01256189,-0.04766157,0.11655982,-0.05103566,-0.14020984,-0.02530834,0.02447143,-0.04785614,-0.09067597,-0.04083261,0.01272118,-0.06366113,0.03268728,0.08537319,-0.0359007,-0.06500632,-0.0349708,-0.04057714,-0.02667461,0.01697316,-0.01653576,-0.04352246,-0.00514745,-0.03266188,-0.08014777,0.07787932,-0.00155019,0.06830464,0.05786831,-0.04170791,0.00244477,-0.02143192,0.00877114,-0.04766375,0.0227477,-0.05062736,-0.03208444,0.08245181,-0.03452012,-0.01151005,-0.02378323,-0.0016703,-0.00284807,-0.00381032,0.04283094,-0.02761088,-0.05881905,0.08107489,-0.01138627,-0.04486587,0.04003576,0.05745062,0.08046916,-0.06192117,-0.00223881,0.00410423,0.04826749,-0.04209023,-0.02228633,0.04504378,0.02281624,-0.08133113,-0.18780941,-0.00186463,-0.05680837,-0.00265869,0.01946596,-0.00848815,0.01999192,-0.03639274,0.062318,0.10164083,0.09134863,-0.03972584,0.01552146,0.0573271,0.00171572,0.00338185,-0.05969098,-0.04142045,-0.05041491,-0.00042566,0.03504151,0.00539573,-0.01113974,-0.03863927,0.05024255,-0.0725143,0.13864316,0.01770545,0.01853541,-0.0215639,0.04747564,-0.01882694,-0.00447283,-0.02267705,-0.02388511,0.00843665,-0.01116574,-0.01456304,-0.02770118,-0.03432962,-0.02066631,0.00153655,-0.04784752,-0.11661541,-0.0058226,0.02638292,0.01621779,0.0711248,-0.03012941,-0.00108536,0.05744009,-0.01532986,0.03187841,0.03850267,0.03081882,-0.07911539,-0.09769204,-0.03557337,-0.01334901,0.05551817,-0.01936718,-0.05365232,0.01562066,-0.02488026,0.06187278,0.0096178,0.02487559,-0.0220441,0.02630026,-0.00839176,-0.03051321,0.08090595,-0.02400231,0.06235659,-0.02493827,0.03533007,0.06533061,-0.04873796,0.00134777,-0.02850429,0.01719429,0.00277998,0.05696148,0.08979878,0.0696326,0.01956341,0.0620578,0.00860361,-0.00955097,-0.01899593,-0.00067073,-0.0105916,-0.05389643,0.00806764,-0.00378553,0.03487056,-0.25281802,0.01378441,-0.04014818,0.00857445,-0.02442423,-0.01547157,0.02150733,-0.03122092,0.05481561,-0.06060485,0.04060403,0.05696424,0.02317682,-0.03956166,-0.04604382,-0.02882979,0.03490627,0.00760053,0.06972034,0.01666832,0.05522047,0.03525822,0.23111704,-0.00488567,0.01225608,0.04457735,0.0119646,-0.01203409,-0.06516763,0.01459776,0.04469008,0.04323492,0.06692447,-0.01445556,0.00758176,0.0342229,0.01439419,0.04820937,-0.01506494,-0.00322596,-0.03110211,0.0314787,-0.03209512,0.02346351,0.11123419,0.0080997,-0.02600898,-0.06451371,0.02287592,0.08973967,-0.08675457,-0.06161646,-0.05646721,-0.0116465,0.00428253,0.03172108,0.01840961,0.00412668,0.00733591,0.02636428,0.04538491,0.0336958,0.04310204,0.05265646,0.03171587],"last_embed":{"hash":"11rsnjq","tokens":295}}},"text":null,"length":0,"last_read":{"hash":"11rsnjq","at":1753423628365},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters","lines":[71,80],"size":844,"outlinks":[{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":5},{"title":"_**<u>BRIGHT Software</u> for Lottery, Lotto, Pick 3 4 Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":6},{"title":"_**<u>ULTIMATE Software</u> for Lotto, Pick Daily Lottery, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08910347,-0.01981426,-0.02970685,0.00677064,-0.02819289,0.05219059,0.00561396,-0.01791389,0.00943241,-0.03514177,-0.00862017,0.01887448,0.03617633,0.01912643,0.01642801,-0.01230322,0.01246071,-0.0167993,-0.041204,-0.01489513,0.11396296,-0.01012985,-0.05360141,-0.03490258,0.05688183,-0.03024346,-0.01231786,-0.04354378,-0.01124153,-0.19594248,-0.0013026,0.001865,-0.03278673,-0.05056092,-0.08767248,-0.0488456,-0.01840721,0.08146289,-0.03789904,0.04501852,0.00895228,-0.0005644,0.00131457,-0.04327566,0.0065433,-0.06042964,0.05026502,0.0142005,0.01406458,-0.02145039,-0.05305168,0.03432141,0.01456757,0.01377641,0.01190679,0.02940293,0.0385896,0.12243743,0.05334489,0.02790237,0.00876796,0.0574594,-0.18003835,0.08409102,-0.08398697,0.01887284,0.01424179,0.00485423,-0.00107547,0.07344525,0.0646101,0.0352178,-0.04699162,0.04672933,0.03017601,-0.01063892,-0.01522463,-0.09387469,-0.03476862,0.04344863,-0.03933994,-0.05097784,0.01985938,-0.01604516,0.01945779,0.02686791,0.01687314,0.01258211,0.05724516,-0.04302799,0.04189316,0.027213,0.03305205,0.02969732,0.01526225,0.00709832,0.02930177,-0.05216771,0.05417152,0.15156856,-0.00763465,0.04782312,0.03334472,-0.01783955,0.07419354,-0.04312642,-0.00798001,-0.05896332,-0.04058219,0.03319626,0.02576676,0.00102877,0.09986515,-0.03638064,-0.01718032,-0.01339599,-0.01324358,-0.01209039,0.02679855,-0.00509248,-0.06032469,0.0023317,0.01904934,-0.04145071,0.00903783,0.00518112,0.0498502,0.03789799,0.00960016,0.04048448,0.04218396,0.03204872,-0.13175471,-0.04302905,0.01245513,-0.00132359,0.02644209,-0.0340392,-0.01491555,0.04255348,-0.03716293,-0.0048707,0.057836,-0.13753214,-0.01822235,0.09153383,-0.00991582,0.02469801,-0.02492102,0.02269656,-0.00658851,0.00000959,-0.06031329,-0.0329161,-0.01454738,0.04586774,0.04964991,0.09263218,-0.04948624,-0.05515781,-0.03057296,-0.00570021,-0.05950417,0.11274589,-0.05417597,-0.15152614,-0.03176757,-0.00716327,-0.04677539,-0.09229378,-0.04188661,0.00296453,-0.06577779,0.02386138,0.07772786,-0.02432962,-0.06054703,-0.05128958,-0.05454446,-0.0260485,0.03783521,-0.01407116,-0.06234408,-0.00561665,-0.02765151,-0.07224324,0.06168371,-0.00623568,0.06942631,0.06651094,-0.05310504,-0.00709581,-0.01754455,0.0115048,-0.01530015,0.01296023,-0.03402628,-0.02568016,0.09839107,-0.05408128,0.00154301,-0.03008164,-0.00281395,0.00968694,-0.02607488,0.03644405,-0.02263627,-0.05113579,0.08139092,0.00318894,-0.05291424,0.03365202,0.05757872,0.07698885,-0.05245033,-0.02735826,0.00835456,0.04132705,-0.0302389,-0.01454049,0.02854486,0.01748449,-0.08621182,-0.18429317,-0.01808335,-0.03855255,0.00000377,0.0173951,-0.00649286,0.00344275,-0.02660188,0.04574544,0.11525526,0.06561677,-0.03137578,0.02791393,0.05917521,-0.00546654,-0.00557258,-0.05550484,-0.04197907,-0.03435523,-0.01033745,0.02737916,0.00088741,-0.02386738,-0.05142509,0.04969988,-0.04588088,0.12972525,0.00813572,0.02377729,-0.00925643,0.04412076,-0.01481377,-0.0050895,-0.00024945,-0.01715873,0.00427344,-0.01007339,-0.04024625,-0.02946301,-0.01945707,-0.01779028,-0.02133206,-0.05283037,-0.10430385,-0.00755243,0.02377947,0.01784362,0.08545405,-0.04231603,0.00462562,0.04800977,-0.03592686,0.04495485,0.03582035,0.04822954,-0.0717192,-0.08971757,-0.0404733,-0.01079125,0.05603241,-0.01775905,-0.04504698,-0.00051291,-0.00284643,0.04699565,-0.00144981,0.03098742,-0.02599552,0.00258455,-0.03985208,-0.03009369,0.09833434,-0.03806225,0.03330271,-0.02887649,0.03815202,0.05502569,-0.04379422,-0.00443806,-0.03877923,0.03908373,0.00496273,0.06579497,0.08898766,0.04419167,0.01586472,0.05448408,0.00008451,-0.01565848,-0.02347394,-0.00679947,-0.01526599,-0.05446139,-0.00436057,-0.0064043,0.0223958,-0.25109056,0.03143712,-0.02951819,0.0182267,-0.0289233,-0.01997704,0.00477051,-0.00310217,0.0608936,-0.04078871,0.05492734,0.05855791,0.02649379,-0.0175037,-0.03636303,-0.03260975,0.04096172,-0.01711887,0.08410329,0.01824268,0.07113349,0.03434093,0.24079964,-0.00145747,0.02892277,0.03332717,0.01197793,0.00784963,-0.09727899,-0.00181588,0.05350131,0.025687,0.06922803,-0.02227193,0.00171783,0.01555313,0.03615673,0.07269822,-0.01691072,0.00093773,-0.04754269,0.03116325,-0.02060715,0.03139789,0.12022625,0.00210992,-0.02355083,-0.06516841,0.01839162,0.08678015,-0.08965507,-0.0494033,-0.0509366,-0.01252687,-0.00226548,0.02573089,0.02010299,0.0078419,0.00159879,0.03485018,0.05141445,0.05340603,0.04147477,0.06308978,0.03679805],"last_embed":{"hash":"1sxpsag","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"1sxpsag","at":1753423628466},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#1\\. Lottery Strategies Founded on Traditional Dynamic Filters#{1}","lines":[73,74],"size":257,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10924605,-0.00718637,-0.01993243,0.01124079,-0.05243891,0.07399423,-0.00371403,-0.00137127,0.05792942,0.02053061,-0.00638346,0.01606843,0.05154878,0.01689442,-0.00682619,-0.04158494,0.01510203,-0.02700225,-0.02577543,0.01831533,0.07740857,-0.05034412,-0.0171515,-0.09338739,0.04907298,-0.0104044,-0.02911513,-0.05290711,-0.04117823,-0.21485405,0.00491095,-0.01531222,-0.02945411,-0.03864409,-0.13048543,-0.04295198,-0.01883697,0.0768908,-0.02511148,0.04260152,-0.00987555,0.01262167,-0.0071129,-0.01679893,0.00776123,-0.01946299,-0.00257987,0.02773272,0.06038641,-0.01759104,-0.0891582,0.03708037,-0.00153707,-0.01072384,0.09474234,-0.00260472,0.04687162,0.10478491,-0.01568762,0.02997505,0.02592262,0.04011754,-0.17916396,0.09071311,-0.00681365,0.05533501,0.01306085,0.01643728,-0.00033592,0.08357897,0.03425703,0.00438341,-0.04510326,0.08272621,0.05845171,-0.00192053,-0.020256,-0.02856732,-0.03271656,0.05003247,-0.01865995,-0.05031453,-0.01838992,-0.03537427,0.01253891,0.0448522,0.01100226,0.03844032,0.07313874,-0.07995798,0.0691995,0.01289234,0.02084629,-0.00019813,0.04709951,0.00896319,0.03671157,-0.08055161,0.01653154,0.09782206,0.03760855,0.01033414,0.02102261,0.03722418,0.03032239,-0.06559809,-0.03139812,-0.07757837,-0.03212007,0.01927161,0.02903294,-0.00730672,0.08987726,-0.01883529,-0.02323895,0.01523434,0.01514242,0.01228103,0.05578175,0.01248448,-0.06847214,0.03064063,0.00960135,0.01707848,-0.01066804,-0.02986044,0.01585324,0.04987003,0.00597322,0.02427709,0.02004187,-0.00230125,-0.11558675,-0.08033486,-0.00579791,-0.03020836,0.00199553,0.02010799,0.00935017,0.01761878,-0.00937264,-0.04638659,0.04619229,-0.13534102,-0.02636019,0.06608613,0.04424258,0.02642592,-0.00563343,-0.00157574,-0.00158436,-0.03571684,-0.05161313,-0.05268006,-0.02331213,0.02270435,0.06555054,0.07242494,-0.06809615,-0.02823691,-0.08021954,-0.00370557,-0.05117757,0.11655467,0.00043635,-0.13416868,-0.02540628,-0.00397515,-0.05246186,-0.08871811,-0.02236106,-0.01020744,-0.08077791,0.00695575,0.09845407,0.00157608,-0.07751522,-0.08117349,-0.02898656,0.01034617,0.02771607,-0.01360841,-0.02794416,-0.0124137,-0.03642944,-0.09857978,0.03330031,-0.05176855,0.03942936,0.06457227,-0.0377924,0.00372388,-0.04693689,-0.00612035,-0.0355863,0.00056562,-0.0244872,-0.03736766,0.07205567,-0.01204747,0.00283368,-0.03508655,0.01386264,0.0037876,0.00510923,0.04199908,0.01528599,-0.06363813,0.08970837,0.01445928,-0.02315491,-0.02367678,0.04284402,0.03697143,-0.014823,0.03160669,0.01829869,0.02921031,-0.0041706,-0.00415995,0.01829252,0.02004529,-0.06785588,-0.16639604,-0.05767949,-0.06331506,0.03604639,0.01825647,-0.04493139,0.04392039,-0.0260144,0.03361626,0.06801649,0.07501977,-0.07087848,-0.00006381,0.0858296,-0.01451422,0.01160964,-0.0859433,-0.06515241,-0.04469268,0.05718429,0.00094148,0.0082303,-0.0203841,-0.057784,0.02550009,-0.05584215,0.14798376,0.02189474,-0.0188944,-0.00388616,0.05293771,0.03596362,0.01239534,-0.02199713,0.00381194,0.01592713,0.01916385,-0.02723211,-0.03491975,-0.01030246,-0.04897207,0.02112377,-0.02728012,-0.08465328,-0.07282521,0.00507161,-0.00872049,0.02533471,-0.00039054,0.04439762,0.05927593,-0.02724638,0.06517491,0.01290206,0.04132227,-0.01333943,-0.09122965,0.02709045,-0.03121905,0.04802658,-0.02893412,-0.05947968,-0.00436304,-0.0034314,0.04927726,-0.00271427,0.01620278,0.00941364,0.0420919,-0.01577126,-0.00512692,0.08446416,-0.00127719,0.01569243,-0.01780345,0.03607974,0.06486934,-0.02938171,-0.01632204,0.03237004,-0.01621601,-0.01852846,0.02734214,0.06112302,0.02755221,-0.0024411,0.08829819,0.00209813,0.00052784,-0.0236541,-0.02814674,0.01633083,-0.05050913,0.03548234,0.02428481,0.01866071,-0.24535123,0.02706934,-0.03299951,0.04001939,-0.0047999,-0.02842073,0.01997091,-0.01793067,0.04229565,-0.0106282,0.0680867,0.04405059,0.03263514,-0.03006559,-0.04615293,0.00558507,-0.00021835,-0.00328435,0.06032068,0.00089195,0.07760062,0.04794217,0.24484588,-0.01894372,0.01574528,0.00950264,-0.00406249,0.03420475,-0.05632112,0.03024511,0.00077273,0.04034257,0.04968924,-0.01138145,-0.06442941,0.01065182,0.0029848,0.08832222,-0.02184247,-0.01763969,-0.07021149,0.00383465,-0.00500244,0.04322798,0.11992029,0.02444875,-0.01368709,-0.09299439,0.05665823,0.08407542,-0.10451844,-0.04997831,-0.028542,-0.00648136,0.0313322,0.04360389,0.01297962,-0.02740283,-0.01404552,0.04324055,0.04278177,0.02260641,0.05430381,0.06086215,0.03157665],"last_embed":{"hash":"5emhhf","tokens":364}}},"text":null,"length":0,"last_read":{"hash":"5emhhf","at":1753423628502},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips","lines":[81,94],"size":1264,"outlinks":[{"title":"_**Winning Lottery Strategies, Systems, Software**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Skips Systems Software, Strategies for Lottery, Lotto, Gambling, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":9},{"title":"_**First Lottery Systems, Gambling Systems on Skips, Software**_","target":"https://forums.saliu.com/lottery-gambling-skips-systems.html","line":10},{"title":"_**Powerball, Mega Millions, Strategy, Skip Systems**_","target":"https://saliu.com/powerball-systems.html","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09127514,-0.02282448,-0.01696876,0.01475562,-0.05158945,0.07580858,0.02527622,-0.01128383,0.04346139,0.00595326,0.00496629,0.0229778,0.03326738,0.03277108,-0.00414351,-0.00077019,0.00232638,-0.04048489,-0.04076722,0.02445424,0.10881738,-0.03872462,-0.00898513,-0.0824905,0.07098948,-0.00859891,-0.03054621,-0.0701903,-0.03299151,-0.18649688,0.00786111,-0.01052338,-0.03412179,-0.03065274,-0.10373583,-0.06691173,-0.01340669,0.10416201,-0.01566349,0.03578683,-0.00423202,0.00685597,-0.02024363,-0.01552731,0.0015819,-0.03039746,0.00152403,0.02389494,0.06609606,-0.02261158,-0.07386813,0.04834491,0.00444682,-0.01022983,0.09097114,-0.00388702,0.06113568,0.08351614,0.01461885,0.03014501,0.01505222,0.03264922,-0.1757008,0.08254416,-0.03427936,0.05765912,0.00333062,0.01873383,-0.02920051,0.10487109,0.04832878,-0.00910086,-0.04649259,0.07573725,0.06910217,-0.02453896,-0.02205108,-0.03776796,-0.03870983,0.04404134,-0.05493289,-0.04261712,-0.01173116,-0.0221702,0.00793203,0.03541552,0.01476986,0.03828776,0.07955635,-0.05902923,0.09839041,0.02988786,-0.00858937,-0.0072115,0.03411652,0.01753276,0.03307138,-0.08857151,0.03420462,0.12261678,0.01645691,0.02270722,0.03231093,0.03527714,0.03207979,-0.06545504,-0.04314318,-0.08747432,-0.03329996,0.0189035,0.02873441,-0.00679077,0.09807226,-0.02691988,-0.01797292,0.00806215,0.00660394,0.01447246,0.06436616,-0.00268212,-0.05363122,0.0169797,0.00780747,-0.00653068,-0.00192201,-0.01810865,0.01904961,0.05032909,0.01187292,0.01272386,0.03226534,-0.02495935,-0.11161,-0.0582649,-0.02930584,-0.03239774,0.02900271,0.02097866,0.00934416,-0.00084633,-0.0032495,-0.0487745,0.0424164,-0.13158411,-0.03499485,0.09636145,0.02600947,0.04367702,0.00749581,-0.0020317,-0.01040082,-0.02372128,-0.05635286,-0.05917374,-0.03341633,0.02411484,0.03947494,0.07726406,-0.07124105,-0.04716673,-0.05760384,-0.02006457,-0.04048283,0.08726457,-0.0263485,-0.12785661,-0.03017304,-0.02108539,-0.05926264,-0.11185534,-0.01844557,-0.01036786,-0.08127064,0.00829938,0.10470724,0.02152906,-0.05513836,-0.07189435,-0.00877165,0.01082074,0.02926442,-0.01811991,-0.04231479,0.00223655,-0.03100643,-0.06984515,0.04332951,-0.03271065,0.05861883,0.07106033,-0.04410855,-0.00580247,-0.0398405,-0.00448065,-0.02916806,-0.00249997,-0.0171358,-0.0389511,0.07951908,-0.02453236,0.0478664,-0.0405568,-0.00748208,0.00110921,-0.01803164,0.03816675,0.03227353,-0.05571128,0.08644351,0.01635096,-0.05996941,-0.02633121,0.01978319,0.04079467,0.00121628,0.01114486,0.03887487,0.02941322,0.01123393,0.00537226,0.01967755,-0.00059374,-0.05060432,-0.15530382,-0.07296601,-0.03378576,0.0288028,-0.00076659,-0.03641793,0.03686722,-0.01315833,0.04455994,0.07818009,0.0759387,-0.06674446,-0.00893045,0.07263281,-0.04139037,-0.00244849,-0.06979726,-0.0672242,-0.03078824,0.04764625,-0.01425047,-0.00979065,-0.05399967,-0.0501224,0.0459789,-0.03506941,0.14004941,0.02530975,-0.01696898,-0.02567516,0.05048667,0.03412874,0.00456351,-0.01112084,0.0050566,-0.01728477,-0.0091725,-0.04561315,-0.04243649,-0.01703533,-0.03867283,-0.00126405,-0.02203016,-0.07372434,-0.04957248,0.00458545,-0.00595141,0.04600712,-0.00622053,0.02569909,0.04529679,-0.02519768,0.05401766,0.04323957,0.04851017,-0.00103326,-0.08377788,0.01828701,-0.03451802,0.04591464,-0.03309326,-0.04637629,0.000228,-0.00930886,0.05028438,-0.00061413,0.02286646,-0.00491633,0.03381833,-0.02260825,0.0014459,0.06897938,-0.00335066,-0.0048897,-0.04677552,0.03613784,0.07055616,-0.01496058,-0.0112468,0.02444728,0.01883905,-0.02438536,0.03048493,0.04996128,0.00806465,-0.0082199,0.0735156,-0.01552995,-0.00799578,-0.0219012,-0.01794553,0.01404528,-0.0463674,0.01180235,0.00609191,-0.00024046,-0.25883669,0.03642564,-0.02979652,0.07114781,-0.00655629,-0.01907258,0.03100025,0.0182024,0.03434008,-0.02735581,0.09288174,0.05924581,0.02323828,-0.01984411,-0.04719184,-0.00697016,0.00796722,-0.02609912,0.05907027,0.01372828,0.0863355,0.02966686,0.24983263,-0.01467058,0.03127344,-0.00296788,-0.00437754,0.03823217,-0.06757563,0.02522593,0.00006418,0.01178955,0.03932764,-0.01897069,-0.05128604,0.00373587,0.01433059,0.10235049,-0.0146979,-0.01200462,-0.06653004,0.00595763,-0.04114002,0.03555718,0.13286136,0.02308219,-0.02367452,-0.08251838,0.032457,0.07587942,-0.10259771,-0.06296413,-0.03078071,-0.01321372,0.00854565,0.05536885,0.0306412,-0.02025028,-0.01409809,0.07543253,0.04291743,0.04355477,0.04943322,0.06287853,0.03758141],"last_embed":{"hash":"79ego7","tokens":121}}},"text":null,"length":0,"last_read":{"hash":"79ego7","at":1753423628614},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{1}","lines":[83,84],"size":300,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0968802,-0.01404501,-0.00098581,0.01333946,-0.04077492,0.0622863,0.00475158,0.02463087,0.0364776,0.01793775,-0.01702975,0.01302747,0.04040059,0.03096159,-0.00181727,-0.02571451,0.01338584,-0.00950584,-0.04651121,0.00748855,0.06381693,-0.02695396,-0.040223,-0.07512192,0.04496044,-0.00068498,-0.0507344,-0.04290864,-0.03991069,-0.19176786,-0.00577156,0.00240257,-0.05255542,-0.03231154,-0.10789005,-0.05308583,-0.02360409,0.08223321,-0.01792312,0.0494562,-0.01113048,0.0007706,-0.00547829,-0.02372529,0.00332534,-0.02853055,-0.00878584,0.02743762,0.05113483,-0.02968375,-0.08363847,0.04902188,-0.00728264,-0.030017,0.07681073,0.00065647,0.01811518,0.10495089,-0.00513777,0.04167912,0.030157,0.06253284,-0.18787381,0.08744336,-0.01011312,0.05077495,0.00032633,0.01400114,-0.00781501,0.09621418,0.04116514,0.00268835,-0.03929076,0.07297216,0.06362613,0.00195147,-0.01351832,-0.05542862,-0.0451616,0.05248908,-0.03950884,-0.01550656,-0.01122941,-0.03197319,0.00400819,0.03983086,0.01399608,0.01670102,0.07974751,-0.06034532,0.09965221,0.02443716,0.02692464,0.00072381,0.03973757,0.01372725,0.01026114,-0.08964016,0.03744778,0.11650056,-0.00029827,-0.00967454,0.01667069,0.03655494,0.0360039,-0.05253498,-0.03934515,-0.08984844,-0.02642797,0.01973991,0.01287441,0.01321869,0.09419969,-0.02955399,-0.02720824,0.02994215,0.02297049,0.00956528,0.02743725,0.02324223,-0.07525374,0.03397505,0.00175279,-0.00418882,-0.01049476,-0.05017114,0.02469986,0.04834972,-0.02705234,0.01499139,0.03400727,0.00483851,-0.1345765,-0.05852151,-0.0030031,-0.03164859,-0.01681368,-0.0019077,0.01065465,0.02102557,-0.00434196,-0.01864697,0.07509349,-0.13335568,-0.03101192,0.07757051,0.05412911,0.01015246,0.00793063,0.00655081,-0.00369413,-0.01812809,-0.06093413,-0.04339384,-0.00414796,0.00887487,0.04694589,0.06653911,-0.06465763,-0.03139091,-0.08051391,0.00315012,-0.05183991,0.12238563,-0.05185919,-0.1375685,-0.02414586,-0.00630496,-0.04867557,-0.08124661,-0.00955261,-0.01551787,-0.08394013,0.03450126,0.10600816,0.00753256,-0.07197043,-0.07470587,-0.01533573,0.01279296,0.0510578,-0.0344573,-0.0463198,-0.00436624,-0.02572863,-0.06571995,0.02074238,-0.04040835,0.0347199,0.08534661,-0.04697117,0.00658996,-0.05921243,-0.02392992,-0.0361836,-0.00377179,-0.02773068,-0.0375752,0.07667045,-0.01281854,-0.03911993,-0.02848469,-0.00480591,0.01453756,-0.00773166,0.03872788,0.02967821,-0.07208499,0.08761155,0.03850159,-0.02714503,-0.01806412,0.01986442,0.03954394,-0.03592988,0.02345335,0.03338733,0.03825738,-0.02552594,-0.00849657,0.02810816,-0.01259322,-0.05437683,-0.1615254,-0.0664246,-0.06252328,0.01774351,0.03613997,-0.02311676,0.03932247,-0.00673342,0.03540093,0.08642226,0.05442924,-0.07821684,-0.02348533,0.06776623,-0.00986441,0.00638086,-0.09152456,-0.08006763,-0.04753381,0.03695347,0.0166401,-0.00537795,-0.04247205,-0.05274594,0.03461824,-0.03733385,0.13879865,0.01924206,0.01857594,-0.00369759,0.05643327,0.03526401,0.03046409,-0.01670386,-0.00327815,0.01361374,0.00510262,-0.02172391,-0.04742499,-0.02988466,-0.02572673,0.04820465,-0.01901386,-0.06439788,-0.0752063,-0.0030381,0.02260643,0.04182415,0.01321728,0.06092305,0.07022057,-0.00424266,0.05534328,0.02152628,0.02648272,-0.02923116,-0.08426415,0.01174374,-0.02877796,0.05174591,-0.004273,-0.06058408,0.0086755,0.00780467,0.05309942,-0.01486374,0.01042313,-0.0077914,0.01439425,-0.01583175,-0.01141868,0.07980007,-0.01029302,0.00697134,-0.02654822,0.04505052,0.08259858,-0.04343117,-0.01948996,0.00221971,0.00893946,-0.02824312,0.06276751,0.07252742,0.01238413,-0.00677973,0.07923169,-0.01416063,0.00770451,-0.01605548,-0.03613117,-0.00282179,-0.05585235,0.01964364,0.02043897,0.02930482,-0.22959226,0.0260729,-0.0191406,0.05336819,-0.01465449,-0.02590343,0.01178795,-0.0075834,0.03155167,-0.01989463,0.06795962,0.05070057,0.0138728,-0.02712993,-0.04020766,-0.01574737,0.0388986,-0.02603972,0.06273938,0.00984491,0.08338978,0.06154951,0.23827767,-0.01782679,0.01890193,0.02103889,0.00888569,0.04354919,-0.07980069,0.02076035,0.01045352,0.01942777,0.04538087,-0.01727543,-0.06131839,0.01563972,0.03036182,0.10202758,0.00622983,0.00217749,-0.04248725,0.00203974,-0.01934957,0.04528819,0.12245935,0.01620668,-0.05038164,-0.08728921,0.03804733,0.07385236,-0.09713021,-0.07160031,-0.05228096,-0.00958311,0.01815324,0.05411105,0.04250257,-0.01966647,-0.00735199,0.04033261,0.05021389,0.00969568,0.05141421,0.07609937,0.00894951],"last_embed":{"hash":"eco74p","tokens":106}}},"text":null,"length":0,"last_read":{"hash":"eco74p","at":1753423628655},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#2\\. Lottery Strategies Based on Skips#{3}","lines":[87,88],"size":300,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11762104,-0.04115653,-0.05481843,0.03501765,-0.05500008,0.08113882,-0.0036084,0.00890624,0.04616356,-0.03030114,-0.02041509,-0.00175361,0.04585461,0.03602818,0.0032955,-0.00701255,-0.02957438,-0.00323596,-0.04081469,0.03647428,0.12416869,-0.09165175,-0.04071715,-0.06150238,0.06186182,0.00948459,-0.03309521,-0.08067525,-0.01334969,-0.22068089,0.00978116,0.01265514,-0.0050994,-0.03346844,-0.11212391,-0.03345555,-0.03572273,0.06621072,-0.02054394,0.02368385,-0.0087505,0.01740736,0.02407694,-0.00937904,-0.02172064,-0.0313229,0.00789267,0.02226463,0.04654705,-0.01386131,-0.07921018,0.01236772,-0.0275192,0.00125777,0.07509934,0.09610609,0.04898815,0.07808331,-0.02883313,0.04647218,0.0065481,0.04585747,-0.19825926,0.06177854,-0.00588083,0.00850239,0.03225927,0.00861168,-0.01098479,0.05989815,0.06855348,0.04123617,-0.02539513,0.02526121,0.02138542,0.01912241,-0.04255319,-0.05260295,-0.02686829,0.04813772,-0.0713177,-0.0183539,0.01931222,-0.01003849,0.01299326,0.03074164,0.03169957,0.01509088,0.02264003,-0.06990312,0.01339177,0.05516462,0.00634502,0.00805056,0.04155664,0.01926525,0.00017146,-0.04137806,0.01745358,0.12278112,-0.02774266,0.02732692,-0.01182236,0.02321897,0.04420888,-0.03234707,-0.04199039,-0.05427299,-0.00872473,0.00464638,0.0427,-0.01892319,0.04758941,-0.03156424,-0.02699101,0.01257627,0.03180914,0.00133505,0.05407881,0.01304989,-0.07482874,0.02970301,0.04184021,-0.01893717,0.00138603,-0.01173856,0.02696168,0.0275286,0.01798769,0.04439129,0.02064014,0.00577403,-0.11568788,-0.04701785,-0.06222888,-0.01469567,0.02837772,-0.00869418,0.01365009,0.02675135,0.01724906,-0.07475591,0.07735841,-0.11787792,0.01157402,0.08583207,-0.02770484,0.0052491,0.01529718,0.05382906,0.00427084,-0.02881644,-0.03858159,-0.08500784,-0.01758941,0.00635751,0.08122622,0.07518625,-0.01901486,0.00871075,-0.05284647,-0.01237565,-0.04648375,0.10835213,-0.00675976,-0.14874758,-0.02211912,-0.0031174,-0.04885969,-0.09581473,0.00385475,0.00782439,-0.0390943,0.04259852,0.07945523,-0.0303233,-0.07969899,-0.03826891,-0.01709419,0.02141314,-0.00691366,0.00302149,-0.03531826,-0.01873294,-0.01630362,-0.0615626,0.02047217,-0.02294891,0.0271217,0.05732962,-0.02792191,0.00214101,-0.01416493,0.02895019,-0.04161156,-0.01251327,-0.03764395,-0.0640764,0.05680858,0.01168756,-0.03366198,-0.01311644,-0.01916245,-0.01058866,-0.04007256,0.06246,-0.00839622,-0.05632247,0.07858931,0.02294573,-0.03298723,0.01275034,0.02834315,0.05164806,-0.04455271,0.01883101,0.02366094,0.02073576,-0.02912715,0.0304009,0.01744905,-0.03401034,-0.06872232,-0.17587329,-0.06148502,-0.03725024,-0.01144284,0.02957841,-0.0375899,0.05601499,-0.01257967,0.02541274,0.06596604,0.06374381,-0.0529316,0.02672539,0.05467924,-0.03486385,0.00836052,-0.06900629,-0.038204,-0.02612592,0.03074632,0.01315944,0.04653677,-0.02475154,-0.0693519,0.06388079,-0.03170432,0.16911881,0.03949561,-0.01509067,0.01897563,0.07136544,0.00117195,-0.03221363,-0.0297774,0.01703229,0.00298227,0.0202193,-0.06206014,-0.05204478,0.01384443,-0.08467253,-0.00892014,-0.00026101,-0.08355511,-0.04276559,0.00691725,0.00493309,0.04486169,-0.01604136,0.01705827,0.07106145,0.01229452,0.03454284,-0.01051938,0.02370091,0.01374516,-0.10172051,-0.00570062,-0.02356456,0.05109414,-0.0137916,-0.00478783,0.00795663,-0.00965193,0.02985928,0.00137565,0.01389547,0.02018257,0.00211753,0.00279061,-0.01070523,0.05552673,-0.00563604,0.04878184,0.01247864,0.02077796,0.03602363,-0.08544094,0.01703361,-0.00313821,0.00119472,-0.05057525,0.04425736,0.04305729,0.03144861,-0.00087702,0.07126644,0.0511378,-0.00176199,-0.03928753,-0.01891626,0.00132278,-0.01305883,0.02677448,0.05253435,0.01002225,-0.2689732,0.02438976,-0.05766141,0.05760488,-0.02558737,-0.02792795,0.01017983,0.01358249,0.01756614,-0.03713053,0.0940582,0.01648622,-0.00574846,-0.02344519,-0.04065728,0.0124233,0.00679155,-0.03563025,0.05695402,0.01425059,0.06398673,0.00891603,0.24006227,0.01178475,0.04827818,0.01719069,0.01070246,0.05511335,-0.0902633,-0.00888084,0.04188921,0.01562624,0.02666487,-0.05064166,-0.01501723,0.04841499,0.02556968,0.03470593,-0.01059182,0.02294571,-0.07483844,0.0156062,-0.00336309,0.04583205,0.16405693,0.02003784,-0.01095666,-0.06005155,0.06073734,0.05783189,-0.12425515,-0.04394364,-0.04916008,0.01203939,-0.00546324,0.03227045,0.04837933,-0.01127325,-0.00121049,0.05465486,0.03385033,0.02581505,0.03854038,0.04645522,0.01123211],"last_embed":{"hash":"1w42zzn","tokens":407}}},"text":null,"length":0,"last_read":{"hash":"1w42zzn","at":1753423628692},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings","lines":[95,105],"size":1130,"outlinks":[{"title":"_**<u>Wonder Grid</u> Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":5},{"title":"_**<u>Magical Lotto Wheel</u>: Lottery Pairs System, Pairing Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":6},{"title":"_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_","target":"https://saliu.com/STR30.htm","line":7},{"title":"_**Lottery <u>Wonder-Grid</u>, Lotto Pairs Strategy Software, Analysis**_","target":"https://saliu.com/bbs/messages/grid.html","line":8},{"title":"_**Lottery Pairs, Lotto Frequency, <u>Lotto Wonder-Grid</u>**_","target":"https://saliu.com/forum/lottery-pairs.html","line":9},{"title":"_**Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_","target":"https://saliu.com/lottery-lotto-pairs.html","line":10}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11005692,-0.02787717,-0.04308695,0.03933554,-0.07283939,0.07487317,0.00789805,-0.00629499,0.03751378,-0.02803975,-0.00588583,0.0101512,0.05415384,0.05814219,0.00958224,0.00271532,-0.04334392,-0.00972909,-0.03704293,0.04595606,0.13205339,-0.08968367,-0.05802747,-0.05969389,0.05785099,0.01764038,-0.02686129,-0.0845132,-0.00491837,-0.20246184,0.01897634,0.01019732,-0.00750666,-0.02657448,-0.09804465,-0.05828584,-0.03172937,0.07249577,-0.01634596,0.03631958,-0.00583785,0.00622002,0.01163543,-0.0155195,-0.00947656,-0.03567013,0.00558671,0.03392376,0.03663424,-0.01290717,-0.05675479,0.01624194,-0.02873018,0.00510336,0.06534013,0.08133963,0.03871354,0.07258939,-0.02325143,0.03204031,0.01983577,0.03615238,-0.19471103,0.0451028,-0.00564818,0.01592918,0.02752164,0.02744999,-0.0279664,0.06653375,0.07169621,0.03752574,-0.03012667,0.02427917,0.02716571,0.02391017,-0.03918755,-0.05217007,-0.02583928,0.06075735,-0.05868277,-0.02870773,0.0329858,-0.00980109,-0.00289076,0.02355884,0.02806028,0.00774383,0.02801137,-0.05868755,0.02961439,0.06201018,0.01590901,0.00202442,0.04751454,0.0158166,-0.00633271,-0.04532276,0.02359511,0.12812833,-0.02868005,0.02872333,-0.00087646,0.01028464,0.03550033,-0.03610233,-0.04472995,-0.05782549,-0.01377809,-0.00195539,0.03692136,-0.0123007,0.06098502,-0.02299988,-0.02547581,0.00606825,0.03356978,0.02270905,0.05977333,0.0101623,-0.08213129,0.02202635,0.02919366,-0.01712621,-0.01150395,-0.01810481,0.01901097,0.02940011,0.01432624,0.03591673,0.02977154,0.00947424,-0.11806262,-0.04980053,-0.04715216,-0.0201332,0.03359926,-0.00984787,0.02199727,0.02393654,0.01324328,-0.04128243,0.07035958,-0.12774454,0.0144243,0.09770188,-0.03147977,0.00803874,0.01355622,0.04283525,0.00259787,-0.03094756,-0.05512279,-0.09214233,-0.0145662,-0.00257675,0.07622194,0.06724936,-0.01974633,0.01365209,-0.05818312,0.01327908,-0.05361947,0.10802437,-0.01167712,-0.17356113,-0.02759463,-0.01451024,-0.037541,-0.10187887,-0.00605338,0.00931482,-0.03988648,0.05378744,0.0814136,-0.02675959,-0.07609832,-0.04126571,-0.01699666,0.02000106,0.01403183,0.00592317,-0.05336424,-0.00817779,-0.02894312,-0.05968773,0.01322877,-0.02036409,0.02762269,0.07553703,-0.02870014,0.01303933,-0.02923675,0.01660844,-0.02158536,-0.01301581,-0.03525985,-0.06533393,0.06284566,-0.00789647,-0.03861047,-0.02985904,-0.02157583,-0.00796677,-0.02770054,0.06645944,0.00027881,-0.06607897,0.08572435,0.03330157,-0.03663798,0.00483825,0.01665937,0.04953738,-0.0457561,0.00909783,0.02896304,0.03547798,-0.02758276,0.01694896,0.03334564,-0.03600032,-0.0620327,-0.16275491,-0.07788815,-0.02939562,-0.00682093,0.01918381,-0.02839313,0.05569616,-0.01119823,-0.00681833,0.08128714,0.05965146,-0.04825549,0.0309574,0.04969649,-0.04714129,-0.00342388,-0.06747875,-0.04288717,-0.01834532,0.01976239,0.01131458,0.04687631,-0.04392276,-0.07700965,0.05570031,-0.01932467,0.16094916,0.04205225,-0.02444383,0.01925231,0.05681602,0.01955202,-0.03375939,-0.03190172,0.015332,0.00151951,0.02841226,-0.06312328,-0.04039383,0.01437924,-0.08548752,-0.02149836,-0.01735445,-0.07217572,-0.04084796,0.01190073,0.00541408,0.05284983,-0.01546721,0.02527735,0.05861124,-0.00312777,0.03445257,0.00588244,0.02369216,0.02042353,-0.09596494,0.00828214,-0.02281675,0.04990192,-0.01283296,-0.00811853,-0.00711329,0.00081801,0.0388613,-0.00564764,0.01046427,0.01827966,0.00298386,-0.02936016,-0.00088879,0.06803609,-0.01013903,0.02882672,0.00456178,0.02966082,0.03885855,-0.08512972,0.01370579,-0.01317555,0.01320145,-0.0525641,0.04056174,0.04745375,0.00940943,0.00009548,0.06392015,0.0491682,-0.00095053,-0.0343738,-0.03537145,-0.00886861,-0.02798122,0.02322444,0.03711033,-0.00296644,-0.25209349,0.03739281,-0.04939141,0.0783989,-0.03195917,-0.01169812,0.00262572,0.03574323,0.02990049,-0.01787098,0.10027948,0.00863379,0.00733198,-0.03422002,-0.03461288,0.01749514,0.01548193,-0.04115444,0.05019137,0.0185908,0.07117652,0.01676963,0.2393862,0.00752446,0.04434681,0.01764531,0.00661314,0.06440561,-0.09639812,-0.01532882,0.04355228,0.00067822,0.02122215,-0.05940521,-0.01821221,0.02511898,0.02621097,0.04299294,0.00190946,0.02689198,-0.08843067,0.00743546,-0.00939713,0.03498516,0.16853011,0.03791983,-0.0073786,-0.06581048,0.04228047,0.06189477,-0.12508537,-0.03880031,-0.04581552,0.00228775,-0.00253546,0.0306997,0.04990567,-0.01058754,-0.00978945,0.06866488,0.03385362,0.04503381,0.03199397,0.05067973,-0.00185776],"last_embed":{"hash":"1tetx8","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"1tetx8","at":1753423628837},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#3\\. Lottery Strategies Founded on Pairs, or Number Pairings#{1}","lines":[97,98],"size":371,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09239639,-0.03057925,-0.04976326,-0.00778945,-0.03083059,0.07923236,0.03003215,-0.00534115,0.03959436,-0.02800632,0.00261157,0.02050081,0.05507289,0.00672821,-0.00090964,0.01231361,0.00662319,-0.01767137,-0.03777594,-0.00250734,0.0957247,-0.064872,-0.03995473,-0.05658103,0.07680091,-0.03510631,-0.04782481,-0.0555728,-0.05386889,-0.20709711,0.01583765,0.03076168,0.02270484,-0.06123265,-0.10512466,-0.03783278,-0.04156268,0.08189828,-0.05637575,0.02072069,-0.00476861,0.04011257,0.01147223,-0.01684262,-0.0111722,-0.01259519,0.00078425,0.02214243,0.02391073,-0.01543614,-0.06263547,0.05729796,-0.04040906,0.0184611,0.06654236,0.02518087,0.06092984,0.08447399,0.01972821,0.03506113,0.03289183,0.03306723,-0.19133039,0.07451242,-0.00827128,0.03112892,0.03862038,-0.01618412,-0.03101015,0.05647798,0.06022834,0.02457765,-0.02965742,0.06385119,0.06302083,-0.01080892,-0.05371281,-0.07240386,-0.04827787,0.03916236,-0.06306643,-0.03234701,0.00166008,-0.0407114,0.04441494,0.02569551,0.04478716,0.02414379,0.07484093,-0.07074239,0.04192902,0.09334356,0.00381456,-0.01761431,0.04583067,0.01572066,0.04084057,-0.04534326,0.0238453,0.11039866,0.02588367,0.02504458,-0.03059756,0.04815887,0.0579382,-0.02910602,-0.03269175,-0.06345224,-0.040519,0.0290527,0.01674097,0.02247108,0.0823686,-0.01312772,-0.03235283,0.00291753,0.02625706,0.01973375,0.02470775,0.01647038,-0.05786967,0.01914698,0.00241901,-0.01022054,0.02157791,0.00315245,0.02340744,0.06947882,0.01809631,0.01324417,0.04980969,-0.01126526,-0.1348772,-0.03420259,-0.03197838,-0.03565352,0.01425196,0.00467271,0.00865345,0.01388308,0.00150363,-0.06763902,0.07712289,-0.13128018,-0.01251487,0.07170995,0.00661603,0.01667419,-0.01478167,0.02727165,0.01806584,-0.01813423,-0.07348521,-0.07628453,-0.00923118,0.00587807,0.06777801,0.05178367,-0.04249878,-0.02902724,-0.0565864,-0.01046128,-0.01649603,0.11066197,-0.02992451,-0.11153226,-0.0211886,0.00448074,-0.06197394,-0.09684521,-0.02221153,-0.02865393,-0.04847497,0.02375875,0.09472567,-0.02273238,-0.05712114,-0.06104401,-0.01628865,0.02419387,0.01691305,-0.03216058,-0.01726535,-0.03069028,0.01504584,-0.03774641,0.04068787,-0.03249756,0.06467471,0.01675006,-0.0520446,0.02742592,-0.0231329,0.01315905,-0.05461546,-0.0044432,-0.01373369,-0.02632232,0.08209829,0.01116924,-0.02787681,-0.01827442,0.0094893,-0.0083792,-0.02182846,0.0543189,0.03713204,-0.08696706,0.09869836,0.02735884,-0.02833826,-0.00277132,0.05820551,0.04434583,-0.04441886,0.01074192,0.02360833,0.03401935,-0.02056898,0.01319906,0.01842859,-0.01373626,-0.08599745,-0.18342701,-0.05721926,-0.01391369,-0.00055374,0.04616434,-0.02496317,0.03642517,-0.03866199,0.05881734,0.03872186,0.07465026,-0.07343917,-0.02541098,0.05130698,-0.01444739,-0.0159262,-0.10069136,-0.05490143,-0.01964325,0.03899186,0.01091939,-0.01895003,-0.05349096,-0.08965894,0.06170832,-0.03420628,0.13879263,0.00147241,0.0054889,-0.00665028,0.0716191,-0.02384533,-0.0067388,0.0235459,0.00518478,-0.04230192,0.01176265,-0.03666618,-0.05153231,0.01663752,-0.06036995,0.01693827,-0.00391735,-0.07828685,-0.0695584,0.01299695,-0.01360362,0.03938704,-0.0148211,0.02381113,0.05534841,0.00401102,0.04824228,0.01049822,0.04513365,-0.03118176,-0.06606428,-0.0071691,-0.01987021,0.06001566,-0.01916033,-0.04718857,0.0489041,0.00327826,0.04108494,-0.00256896,0.01206304,0.00808439,0.02918419,-0.01399856,-0.0017307,0.09951197,-0.0060531,0.0220909,-0.00515284,0.03174915,0.05509294,-0.06138206,0.00024236,0.00751469,0.02200539,-0.03494103,0.04016377,0.04868507,0.01879302,0.01025506,0.06133798,0.05092835,-0.02297719,-0.00390247,0.00933519,0.02415729,-0.01295422,0.00465029,-0.00380291,0.01505932,-0.2361311,0.03060098,-0.0712734,0.03974789,-0.04111778,-0.04176842,-0.01122032,0.01693001,0.02126443,-0.00465872,0.05607362,0.05598297,0.04630044,-0.0258624,-0.0105767,-0.03723655,-0.03124928,-0.04312024,0.05004782,0.02147501,0.09450822,0.03294652,0.24920227,-0.02271513,0.04207334,0.03513853,0.02183642,0.04456491,-0.04739164,0.0180755,0.00835918,0.00166026,0.04184588,-0.00583947,-0.05440611,0.00971951,0.0202531,0.04274366,-0.03809496,0.01666232,-0.05614783,-0.01638573,0.00227921,0.02207224,0.16459222,-0.00398896,-0.04004098,-0.1013312,0.04518437,0.06154132,-0.1084647,-0.06725649,-0.05781149,-0.02143007,0.00304928,0.06659444,0.0247921,-0.00938124,-0.00323033,0.01482049,0.00946072,0.0302352,0.03779121,0.05654621,0.02980056],"last_embed":{"hash":"1tpqwmv","tokens":378}}},"text":null,"length":0,"last_read":{"hash":"1tpqwmv","at":1753423628888},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits","lines":[106,115],"size":1391,"outlinks":[{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":7},{"title":"_**Lotto Frequency Strategy, Lottery Software**_","target":"https://forums.saliu.com/lotto-frequency-efficiency.html","line":8},{"title":"_**Lottery Strategy on Positional Frequencies**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0819066,-0.03011061,-0.04421873,-0.00738146,-0.03715032,0.07466632,0.0321045,-0.00887404,0.03374802,-0.02460751,0.00561739,0.02738788,0.05717892,0.01505071,-0.00045076,0.02769093,-0.00188234,-0.02257766,-0.04619641,0.00105033,0.09633061,-0.0635141,-0.04563047,-0.048166,0.08088429,-0.02769866,-0.04781015,-0.05346579,-0.05654119,-0.20131896,0.02105685,0.03159053,0.02319909,-0.05828551,-0.09515087,-0.04545982,-0.04913351,0.09810305,-0.0565326,0.01755954,-0.00834397,0.03754945,0.01781017,-0.01134859,-0.01674527,-0.01709993,-0.00921545,0.03003808,0.01892196,-0.02130947,-0.05265649,0.06047448,-0.03680511,0.02014061,0.0600845,0.01351614,0.05570848,0.07919689,0.02542604,0.03262363,0.03633633,0.03278281,-0.18296218,0.0701772,-0.00323228,0.0346876,0.03535235,-0.0066949,-0.03418481,0.06931759,0.06047891,0.02202929,-0.02171992,0.06302326,0.05466728,-0.00257157,-0.05854151,-0.07244484,-0.05555442,0.04878365,-0.06995053,-0.0265539,0.0059881,-0.03839787,0.03747461,0.02094427,0.03849112,0.01645028,0.07165083,-0.05664999,0.04588884,0.09892293,-0.0088757,-0.02299407,0.04265747,0.0089421,0.0385003,-0.0524928,0.02419808,0.1146038,0.02214548,0.02998675,-0.0268308,0.03965739,0.05180253,-0.03125736,-0.03931179,-0.07211189,-0.0471615,0.02945716,0.01099957,0.01958936,0.10017733,-0.00682726,-0.03002383,0.00092605,0.02703379,0.0218934,0.02236502,0.01775627,-0.05668963,0.01878991,0.00512489,-0.01426149,0.01824712,0.0049554,0.01811011,0.07224984,0.01573096,0.00576435,0.05373884,-0.00666827,-0.13303711,-0.02673469,-0.02687602,-0.037261,0.02400173,0.01124174,0.01011559,0.0100254,0.00609917,-0.05374853,0.07665548,-0.1332172,-0.0119182,0.07064085,0.00858842,0.01999044,-0.01708824,0.02073406,0.0209825,-0.01898672,-0.08201417,-0.07747138,-0.00356752,0.00585273,0.06680677,0.0446266,-0.04525179,-0.0327717,-0.06252883,-0.00062433,-0.0173528,0.11427852,-0.02826603,-0.10825551,-0.01840079,-0.0089688,-0.06340405,-0.10315644,-0.02017146,-0.0260578,-0.05420198,0.02654522,0.08904709,-0.01601615,-0.06279837,-0.06651436,-0.00431441,0.02269887,0.03544799,-0.02813506,-0.02227617,-0.02423422,0.01858802,-0.03050013,0.03647575,-0.02730563,0.05687515,0.01666805,-0.05485604,0.03343632,-0.02506682,0.01025404,-0.06065892,-0.00599872,-0.00997554,-0.02692686,0.08411814,0.00021441,-0.02262142,-0.02350303,0.01019648,-0.00181435,-0.01917245,0.05447686,0.03994017,-0.09165151,0.10259423,0.02672526,-0.02887504,-0.010877,0.0493594,0.04234454,-0.04816953,0.00658051,0.0296661,0.04012654,-0.02781984,0.00803533,0.02166783,-0.01660319,-0.08169691,-0.17615464,-0.06876516,-0.00089273,-0.00246305,0.0501538,-0.01703145,0.03617634,-0.04273263,0.04734866,0.04562557,0.06488039,-0.07485719,-0.0321775,0.04609164,-0.01776316,-0.00684815,-0.11402698,-0.05425629,-0.01811696,0.03578571,0.00435975,-0.0254424,-0.06427098,-0.0919996,0.061997,-0.02625052,0.13034511,-0.0015669,-0.00299281,-0.01609908,0.06990018,-0.02291933,-0.0038727,0.0432202,0.00524588,-0.04722922,0.01806542,-0.04287016,-0.05208999,0.01442959,-0.05630202,0.01837518,-0.01418894,-0.07018024,-0.07445195,0.01238556,-0.01718017,0.04461161,-0.01174494,0.02597778,0.05187954,0.00393627,0.05356413,0.01366882,0.0467474,-0.02837858,-0.06501842,0.00398953,-0.01319433,0.06687161,-0.01188041,-0.05136989,0.03460593,0.00770108,0.04384623,-0.01034434,0.01371839,0.00656502,0.02160923,-0.02234297,0.00111601,0.09622515,-0.00696141,0.00798703,-0.00764439,0.03623491,0.04882902,-0.06221073,0.00235219,0.00493726,0.03143926,-0.0250368,0.03520955,0.05277774,0.01077817,0.00803061,0.05393251,0.05772726,-0.01638887,-0.00486481,0.00588769,0.02176871,-0.01760546,-0.00340221,-0.0095787,0.00518389,-0.22685917,0.03707711,-0.07196588,0.04685587,-0.04851184,-0.03755239,-0.00737604,0.03949099,0.0229758,-0.00220882,0.0608472,0.05625754,0.04742874,-0.02402331,-0.00608565,-0.04311581,-0.02802182,-0.04539103,0.04520441,0.01912993,0.10367124,0.03148953,0.24470654,-0.02873531,0.04848057,0.03762392,0.02721412,0.06126478,-0.05193652,0.01590975,0.0060302,0.00602922,0.04623123,-0.0151949,-0.05149839,0.00281542,0.02705731,0.04405762,-0.03681224,0.01795417,-0.05798864,-0.01872012,-0.00304431,0.01973664,0.15913595,-0.011598,-0.0390826,-0.10355753,0.03946909,0.06159521,-0.10808716,-0.06196834,-0.05429916,-0.03493511,0.00356928,0.07345984,0.02258419,-0.01076739,0.00188812,0.02336954,-0.00141145,0.03097032,0.03471809,0.05894823,0.02135756],"last_embed":{"hash":"8tgftx","tokens":263}}},"text":null,"length":0,"last_read":{"hash":"8tgftx","at":1753423629066},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#4\\. Lottery Strategies Based on Frequency of Numbers, Digits#{1}","lines":[108,111],"size":970,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07496317,-0.03783607,-0.03639745,0.01166101,-0.05489368,0.05710952,0.01820904,0.01753869,0.02946707,-0.02037874,-0.01779228,-0.00170989,0.0221373,0.01831867,-0.02627116,-0.04603832,0.01073132,-0.02771452,-0.06555187,0.03183345,0.10218038,-0.06957804,-0.03169491,-0.06717768,0.06332427,-0.00809906,-0.01706108,-0.05047216,-0.00129709,-0.23377356,-0.00434067,0.00132898,-0.04159307,-0.09448022,-0.08729737,-0.03927512,0.00655288,0.10545065,-0.0078102,0.01755877,0.00803012,0.00017612,0.0132321,-0.02763721,-0.04834368,-0.04408753,0.0112446,0.02113434,0.00679962,-0.0101048,-0.07012637,0.03443493,0.01306992,-0.00897118,0.04744649,0.04839774,0.04513512,0.12211414,0.00415902,0.01657147,0.01695287,0.03221224,-0.20719598,0.09965798,-0.03634341,0.01687042,0.00249453,0.00841692,-0.02598023,0.08146232,0.04257774,0.04400045,-0.03265188,0.06563292,0.06794755,-0.01676242,-0.06146688,-0.09567551,-0.02767241,0.02239424,-0.04862854,-0.05031043,-0.01604372,-0.01676205,0.03638233,0.06186533,0.03369346,0.02987206,0.07375368,-0.05775761,0.0256929,0.02262689,0.00995004,0.03463925,0.03592928,0.02138085,0.00723361,-0.07780903,0.03891271,0.1039985,0.02347217,0.0258888,-0.01604664,-0.00189642,0.06216403,-0.02721903,-0.02167024,-0.09428827,-0.02388283,0.02557772,0.03181381,-0.02416199,0.061718,-0.00306025,-0.07439192,0.00851907,0.04221618,-0.02534976,0.05199964,0.00352017,-0.04157415,0.02299384,-0.0113749,-0.0118396,-0.00113217,0.0125984,0.00843084,0.04380563,0.00865303,0.01330363,0.06431646,-0.02728713,-0.11092129,-0.04903772,-0.03367336,-0.0135559,0.01045502,0.00720746,-0.0084791,0.04510024,-0.02815355,-0.02697217,0.06767885,-0.09064653,-0.03799283,0.1171907,-0.01558095,0.03468393,0.00873604,0.03878271,-0.00289127,-0.01595349,-0.03190687,-0.0672788,-0.01732437,0.02819996,0.02731579,0.11519063,-0.07737628,-0.01953871,-0.01075542,-0.01518436,-0.05714852,0.12883474,-0.01668093,-0.07871345,-0.0277415,0.00191624,-0.00649692,-0.10509658,0.00459321,-0.00011206,-0.04888832,0.02716639,0.08301298,-0.01008158,-0.0157545,-0.05978266,-0.02096424,0.04648052,0.01704272,-0.02902978,-0.04403095,0.00396936,-0.03186473,-0.07832909,-0.00457157,-0.02127925,0.0668848,0.04036025,-0.03693575,0.01191386,-0.00452237,0.02094154,-0.01061847,-0.0465484,0.0060402,-0.02873934,0.06741549,0.02045221,0.02606122,0.02477209,0.01180887,-0.02128092,-0.03120633,0.067925,0.02233953,-0.06541595,0.07719148,0.03001614,-0.04384423,0.0004865,0.00252446,0.06397668,-0.04186219,0.00560891,0.03361059,0.00801033,-0.021519,0.00042225,-0.01381645,0.02520725,-0.05765326,-0.19346152,-0.04679511,-0.04188464,0.01891617,0.02688519,0.00861417,0.00346563,-0.01330197,0.04050661,0.08790506,0.04581017,-0.06347623,0.03093318,0.09967399,-0.03394202,-0.00196357,-0.08468463,-0.04042039,-0.02717584,0.01758146,-0.01283107,0.02361743,-0.0181595,-0.06315225,0.0341201,-0.02673648,0.14248498,-0.00287705,0.08103831,-0.01637481,0.03586467,0.04387781,0.00889282,0.00875761,0.02749441,-0.00003476,-0.06657693,-0.05442607,-0.02744261,-0.01243881,-0.07162049,0.01362224,-0.03608214,-0.06845316,-0.04247753,-0.01811027,0.01722838,0.04414032,-0.04177505,0.03676539,0.02211182,0.02127301,0.05463009,0.00974328,0.06694822,-0.02422819,-0.09226544,-0.00664781,-0.0159845,0.05585511,0.01556049,-0.02561087,-0.00700881,0.01405877,0.03589909,0.00246904,0.0088853,-0.02139063,-0.00379428,-0.00418258,0.012116,0.08665054,-0.02358566,-0.01274741,0.04219996,0.0126239,0.07490201,-0.02269932,-0.02392838,0.0083049,0.02615159,-0.06152333,0.03588942,0.03211236,0.00082603,0.01521588,0.07545372,0.01032443,-0.04861044,-0.03449258,-0.02371024,0.00958491,-0.04141019,0.02267171,-0.01104992,0.00933746,-0.26461789,0.03768997,-0.04227855,0.00979673,-0.01307608,-0.00784478,-0.00594814,-0.01494829,0.0358812,-0.0418781,0.01960363,0.06722076,0.02586808,-0.00207417,-0.066804,0.00120563,0.0447834,-0.05160768,0.04211688,0.01961928,0.09214205,0.02809668,0.22186607,-0.00456735,0.00595876,0.0072847,0.04007958,0.05670892,-0.06901754,0.00872836,0.0288431,0.03085709,0.06290945,-0.03266871,-0.06541042,0.06462488,-0.02635517,0.09348906,-0.01198893,0.01118781,-0.02138326,-0.01622834,0.01887329,0.05649484,0.11975082,0.03545891,-0.01449478,-0.04438029,0.05989438,0.06051628,-0.12615499,-0.04114048,-0.05160593,0.00394616,0.00711273,0.06000958,-0.01993441,-0.02735035,-0.01259659,0.01893233,0.02691302,0.05519435,0.02927059,0.03123522,0.02960384],"last_embed":{"hash":"11smkgg","tokens":224}}},"text":null,"length":0,"last_read":{"hash":"11smkgg","at":1753423629160},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas","lines":[116,122],"size":675,"outlinks":[{"title":"_**<u>Delta</u> Lotto, Lottery Systems, Strategies**_","target":"https://saliu.com/delta-lotto-software.html","line":5},{"title":"_**Lottery Deltas Build Effective Lotto Strategies, Systems**_","target":"https://saliu.com/bbs/messages/648.html","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06371982,-0.0441017,-0.02904226,0.01374165,-0.05609392,0.06312458,0.03158262,0.00997214,0.03822818,-0.0214704,-0.01510138,0.00583758,0.02680552,0.01923099,-0.03225447,-0.03759671,0.00456827,-0.04159066,-0.08083134,0.03383915,0.10666902,-0.07067864,-0.04178192,-0.06126284,0.06434668,-0.00444792,-0.01898621,-0.0496154,0.00085459,-0.2254671,0.00445306,0.00122751,-0.05071053,-0.09154173,-0.09019774,-0.05342773,0.00097262,0.11506893,-0.00502244,0.02204332,0.01251346,-0.00648415,0.00753692,-0.02433077,-0.04756737,-0.04251306,0.00546213,0.02248154,0.00434655,-0.01563865,-0.06853398,0.03041569,0.01436847,-0.00653377,0.0460722,0.04818225,0.03928615,0.11204013,0.01055931,0.01609086,0.02364579,0.02259597,-0.20352209,0.09562595,-0.03767996,0.01381355,0.00097812,0.01659169,-0.03213878,0.08378346,0.0451114,0.03995774,-0.02636945,0.06867439,0.06543614,-0.01602751,-0.0605955,-0.09630834,-0.03184072,0.0309783,-0.04800096,-0.04258141,-0.01371958,-0.01729616,0.02753332,0.06076185,0.03019344,0.0280048,0.07368343,-0.04828078,0.033976,0.02798319,0.00209682,0.02809815,0.03233806,0.02391781,0.01050127,-0.08374301,0.03861109,0.10664675,0.02735531,0.02226302,-0.01751817,-0.00380068,0.06007548,-0.02596265,-0.02756215,-0.10696347,-0.02874863,0.0273768,0.03698038,-0.02193182,0.07646592,0.00113177,-0.08272587,0.00809277,0.04783773,-0.01417429,0.06388152,0.00133269,-0.05113358,0.02643032,-0.02081317,-0.0111815,0.00008755,0.01636754,0.00759169,0.0442548,0.00420294,0.00895043,0.06925059,-0.0322797,-0.1092402,-0.04710366,-0.02664341,-0.01647022,0.01111984,0.0111431,-0.0037172,0.04091339,-0.02599069,-0.02109145,0.07096151,-0.09106094,-0.03687042,0.1238113,-0.02499468,0.03976154,0.00547616,0.04572584,-0.00095121,-0.01301213,-0.03797971,-0.06667173,-0.0190658,0.03143534,0.02547401,0.10652607,-0.07874496,-0.02379229,-0.01327234,-0.01172598,-0.05306622,0.12550442,-0.02237864,-0.08369181,-0.02539765,-0.01195504,-0.00917681,-0.10379498,0.00777292,0.00338848,-0.05035881,0.03324879,0.08287752,-0.00163604,-0.00404801,-0.05592782,-0.01217621,0.04604103,0.03429788,-0.02805884,-0.05621537,0.01487974,-0.02755173,-0.06999812,-0.01165135,-0.01601137,0.07239034,0.04709325,-0.03508547,0.01384474,-0.00472616,0.01034613,-0.00756891,-0.04987028,-0.0032518,-0.02930599,0.07472469,0.01622358,0.0314044,0.02598236,0.00975421,-0.01042966,-0.02645965,0.06874312,0.02240603,-0.06999864,0.07553081,0.02945353,-0.0508573,-0.00300566,-0.00693116,0.0642435,-0.04476077,0.00136507,0.03386841,0.00780875,-0.02431357,-0.00151522,-0.01292081,0.01737866,-0.05160538,-0.1906985,-0.05159899,-0.03251501,0.0197844,0.02768025,0.02394614,-0.0008715,-0.01035596,0.03714468,0.09259561,0.04030411,-0.06576977,0.02169019,0.09276295,-0.03697279,-0.0025134,-0.09001771,-0.0469844,-0.01777554,0.01841268,-0.00657296,0.01362869,-0.0284605,-0.05925959,0.03587709,-0.01942523,0.13778932,-0.00146627,0.07861364,-0.02422224,0.02481624,0.04619664,0.01642756,0.02452765,0.02543774,-0.00403767,-0.06590567,-0.05636537,-0.03134876,-0.01585207,-0.06811523,0.01386968,-0.03689188,-0.05542716,-0.04084623,-0.02610058,0.01624695,0.04488096,-0.03931718,0.03501524,0.01536598,0.01432572,0.04939429,0.00991383,0.07289267,-0.01975897,-0.08112482,0.00287414,-0.02051308,0.05348615,0.0213081,-0.03346823,-0.01105482,0.0157026,0.02914196,-0.00434909,0.00670051,-0.02188209,-0.00722172,-0.01431143,0.01016664,0.08299536,-0.01837289,-0.02710414,0.03818777,0.01390697,0.08088848,-0.0150269,-0.02278385,0.00603004,0.03158594,-0.05245842,0.02991874,0.02815371,-0.00703645,0.00480125,0.07365279,0.01114395,-0.04513039,-0.0244057,-0.02702312,0.01521269,-0.04372874,0.01247395,-0.01996845,0.00597424,-0.26184809,0.04660033,-0.04523782,0.0186906,-0.01275865,-0.00429157,-0.00909852,0.00157716,0.03214475,-0.03528699,0.02396835,0.06926994,0.02195105,-0.00400828,-0.06613509,0.0046429,0.05514116,-0.05774959,0.03630583,0.02052789,0.09576371,0.02675188,0.21876383,-0.00836087,0.00619184,0.00836909,0.04687444,0.06320427,-0.06756047,0.00564882,0.02872904,0.02376937,0.06764471,-0.03849496,-0.05955476,0.05865614,-0.02854601,0.09427409,-0.00329936,0.01615855,-0.01937478,-0.0107567,0.01299215,0.04874648,0.11718932,0.03605264,-0.02015866,-0.03851049,0.05224925,0.05863847,-0.1295702,-0.03419052,-0.05588865,-0.00222351,-0.00077932,0.05899041,-0.02375964,-0.03470841,-0.0043563,0.01876996,0.03020121,0.04808477,0.02529775,0.02754614,0.0241784],"last_embed":{"hash":"700y3z","tokens":138}}},"text":null,"length":0,"last_read":{"hash":"700y3z","at":1753423629222},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#5\\. Lottery Strategies Based on Deltas#{1}","lines":[118,119],"size":413,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.127966,-0.05108252,-0.04924089,0.0101413,-0.04850771,0.0757274,0.01839046,0.01819083,0.03142822,0.00487886,0.02368934,-0.03077635,0.05151789,0.04430236,0.02209865,-0.00078696,-0.01971399,-0.00229485,-0.03372584,-0.01054831,0.07780038,-0.08020732,-0.06149697,-0.06336174,0.06955779,0.01955336,0.01133515,-0.02885837,-0.01569849,-0.22729488,0.03196412,-0.00609877,0.01678058,-0.0198138,-0.06042278,-0.00824395,-0.02510221,0.04357629,-0.04260929,0.03207059,-0.01546344,0.03590533,0.02722275,-0.03516177,0.01788942,-0.04787527,0.01521054,0.04340494,-0.02029959,-0.01413814,-0.08205388,0.03827924,-0.01045676,-0.00609685,0.049863,0.04701928,0.05951561,0.10083635,0.00512846,0.01303114,0.02085152,0.07714807,-0.17375162,0.05836072,0.00849127,0.02410693,0.00886773,-0.01353723,0.002734,0.07100384,0.02094147,0.02355557,-0.03890072,0.06177885,0.06399829,-0.0239874,-0.01098421,-0.04715407,-0.01757534,0.05196054,-0.07369116,-0.04264927,0.00421803,-0.02354034,-0.01708856,0.05174332,0.02651381,-0.01003584,0.02493853,-0.03957755,0.03550565,0.07906532,0.05756886,-0.0018648,0.06511728,0.00656406,0.01707117,-0.03026599,0.04788157,0.10865986,0.01292052,0.02737347,0.00484959,-0.01253974,0.05526471,-0.02068232,-0.01871285,-0.08710842,0.00623124,-0.00404415,0.02571917,-0.01286877,0.08755752,-0.05405455,-0.01377992,0.00895314,-0.00588109,0.05171837,0.0207201,0.00583865,-0.03832902,0.02536189,0.0452389,0.01719967,-0.04466701,-0.02218843,-0.03721824,0.06354131,0.01562003,-0.02378222,0.04553931,-0.00982503,-0.08460429,-0.03735836,-0.00225417,-0.02984117,0.01308851,-0.02527078,0.02502642,0.00407089,-0.02438506,-0.0294143,0.04085223,-0.12571388,-0.05769157,0.0698554,-0.01046878,0.02659507,0.00997323,0.02694114,0.00482341,-0.02286484,-0.03742438,-0.09919091,0.00147575,0.01797329,0.03080223,0.0642499,-0.03337967,-0.01375789,-0.07647827,-0.01702493,-0.05048379,0.14981282,-0.03856018,-0.11935621,-0.00523317,0.02293077,-0.0105185,-0.08866124,-0.0056067,0.0229506,-0.04568246,0.06400803,0.08297813,-0.06043321,-0.06703138,-0.02941477,0.00514033,-0.0012339,0.00474271,-0.05309555,-0.03951063,0.00944289,-0.00994867,-0.08367629,0.03026692,-0.00191987,0.02231644,0.00085707,-0.01352831,0.00274103,-0.01822832,-0.03207622,-0.05025138,-0.0419967,-0.00174503,-0.03234476,0.07853257,-0.00668895,-0.03766207,-0.03584258,-0.01025038,0.03508987,-0.06325073,0.01378456,0.02066144,-0.04060462,0.08297992,0.05371249,-0.01127388,0.01900866,-0.0053971,0.08239172,-0.0479884,0.03407167,0.03746232,0.02855126,-0.04430086,0.00201894,-0.00027567,0.00999637,-0.11197244,-0.18005337,-0.06687748,-0.05897766,0.01081965,0.06117211,-0.01065439,-0.012308,-0.02178933,0.04912498,0.06217645,0.06957955,-0.03088835,0.00435277,0.04548936,-0.02320723,-0.00421003,-0.09471876,-0.02716008,-0.03016809,0.06095207,0.00448961,0.01251317,-0.04869058,-0.11130068,0.01922222,-0.03304201,0.14345904,0.04036172,0.04384909,0.05100849,0.04867536,0.01475841,-0.04777434,-0.05555722,0.02600106,0.03762095,0.01248017,-0.05730878,-0.05398357,-0.04385887,-0.03646133,-0.00989428,-0.01999051,-0.07145015,-0.05801025,-0.00173224,-0.03606137,0.05617747,-0.00433105,0.05345203,0.02663813,-0.00773673,0.04405078,0.00367645,0.04107092,-0.00506547,-0.05456544,-0.02635986,0.01732637,0.07234718,-0.00165923,-0.03112708,-0.00188417,0.02591839,0.04142356,-0.02977,-0.00167544,-0.04342596,0.02031653,-0.03298049,-0.02869372,0.10507957,0.02117965,0.0019707,0.01822506,0.01031532,0.0336975,-0.05907238,-0.00543378,-0.03059857,0.02572063,-0.03817394,0.06143075,0.05182483,-0.00330307,-0.01286871,0.05937481,0.03534958,-0.0202628,-0.0012743,-0.00451264,0.00876618,-0.01633677,0.0466416,0.05112809,0.02013572,-0.26368415,0.03370624,-0.04662507,0.06125518,-0.01392185,-0.00253057,0.02651297,0.00501923,0.03437139,-0.06538886,0.05567355,0.05488655,0.05819944,-0.02227012,-0.00005643,-0.02702609,0.0244925,-0.04713669,0.07377447,0.03188287,0.0679936,0.04035679,0.24231017,-0.01625528,0.02025413,0.02071976,0.01301515,0.04381781,-0.05119389,-0.01521755,0.01405538,0.01953876,0.00918437,-0.02018899,0.00117014,-0.0167331,-0.00669994,0.04404963,-0.01337788,0.00857569,-0.07249475,-0.04776617,-0.01580928,0.03687227,0.1627562,0.03602472,-0.00371697,-0.09925731,0.05435461,0.0998894,-0.07044637,-0.07929498,-0.03529618,-0.01344354,-0.00822721,0.04454919,0.00933157,-0.00810224,-0.00457115,0.00493247,0.03900736,-0.00055886,0.05686265,0.082227,0.0135952],"last_embed":{"hash":"1f188uu","tokens":256}}},"text":null,"length":0,"last_read":{"hash":"1f188uu","at":1753423629272},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains","lines":[123,129],"size":768,"outlinks":[{"title":"_**<u>Markov Chains</u>: Followers, Pairs, Lottery, Lotto, Systems, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":5},{"title":"_**Markov Chains, Lottery, Lotto, Software, Algorithms, Programs**_","target":"https://saliu.com/Markov_Chains.html","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12133728,-0.0484685,-0.04005756,0.01011776,-0.04971216,0.07139149,0.0254583,0.01557004,0.03418002,0.0053591,0.02596007,-0.02853228,0.04964507,0.04922059,0.02214032,0.00310481,-0.01749249,-0.01277275,-0.02854124,-0.00359478,0.0792263,-0.07506973,-0.07391025,-0.05578558,0.06568217,0.01731789,0.0108495,-0.02671777,-0.02089107,-0.22275756,0.03867245,-0.00760174,0.01119161,-0.01946262,-0.05681638,-0.02587987,-0.03377749,0.05533103,-0.03280614,0.03190124,-0.00964354,0.02226594,0.02875915,-0.03113548,0.01619584,-0.04649764,0.01484365,0.05017813,-0.02227669,-0.0144502,-0.07697629,0.03682451,-0.00751293,-0.0120112,0.04286925,0.03976749,0.05521368,0.0978373,0.01233678,0.01015131,0.02644557,0.07055876,-0.16604574,0.04959242,0.00906355,0.01805155,0.00583553,-0.00713605,-0.00628208,0.07643247,0.02573148,0.02284593,-0.03561044,0.06484668,0.06021834,-0.0178181,-0.01089713,-0.05251673,-0.02184899,0.06310583,-0.07357673,-0.03864577,0.00459486,-0.02822395,-0.01781028,0.046903,0.02322486,-0.00932849,0.02491572,-0.03056691,0.04218907,0.08394473,0.05403619,-0.01322214,0.06528773,0.00126638,0.01840361,-0.03584939,0.04620555,0.10348392,0.00757114,0.02655523,0.0086063,-0.02085509,0.04911987,-0.01386698,-0.02038771,-0.08972272,0.00393888,-0.00869762,0.02915066,-0.0123185,0.10204896,-0.05134985,-0.01221157,0.01303607,-0.00334571,0.05496498,0.01786773,-0.00389251,-0.04333459,0.02682727,0.0465385,0.0149369,-0.04112398,-0.02605641,-0.0383129,0.06677323,0.01014138,-0.02771142,0.04308862,-0.01381674,-0.08454599,-0.0294205,0.00109301,-0.03057442,0.01819544,-0.01245474,0.03764824,0.00346204,-0.02624372,-0.02235248,0.0353901,-0.13336948,-0.06229239,0.06679318,-0.01239635,0.02930397,0.01056532,0.02977733,0.00746681,-0.02274954,-0.0394167,-0.09691095,0.00203479,0.01620725,0.0325623,0.06444064,-0.03348856,-0.01887709,-0.08166799,-0.01255047,-0.04875071,0.14429371,-0.04288028,-0.1248791,-0.00842967,0.0098069,-0.00725088,-0.09913749,-0.00762925,0.0230627,-0.04774439,0.06518774,0.08614139,-0.05298825,-0.05802317,-0.02657412,0.00787929,0.00195076,0.01822289,-0.04784282,-0.04983893,0.01189162,-0.00585081,-0.08197311,0.02054488,0.00248125,0.02300669,0.00097718,-0.01669054,0.00545671,-0.02622828,-0.02636736,-0.04843346,-0.04307079,-0.00386606,-0.03177485,0.08404025,-0.01409274,-0.03395238,-0.04818865,-0.01781579,0.0374758,-0.05728621,0.01894289,0.02852128,-0.04518569,0.08901851,0.05474378,-0.01157796,0.01194336,-0.00709833,0.08274151,-0.05065042,0.03373274,0.03706232,0.03081456,-0.05115308,-0.01215158,0.0011062,0.0090987,-0.10988006,-0.18395534,-0.07811023,-0.0513352,0.00729397,0.06419717,0.00187195,-0.01506737,-0.01824814,0.04883811,0.06374793,0.06085862,-0.03093385,0.0020573,0.03612207,-0.02741602,-0.00943654,-0.1016298,-0.03340079,-0.02870966,0.05784018,0.00573371,0.01258521,-0.05139354,-0.11451525,0.02154345,-0.02655144,0.14003095,0.04071078,0.04410359,0.04460389,0.04227658,0.01537867,-0.04224424,-0.04421294,0.02910981,0.03308729,0.02285548,-0.05919162,-0.05141018,-0.04683643,-0.03477128,-0.00919807,-0.02990172,-0.07082257,-0.05663048,-0.0046675,-0.03927642,0.06473409,0.00003212,0.05950068,0.02563157,-0.0131547,0.03956923,0.0009308,0.04003711,-0.00599666,-0.04388265,-0.02093881,0.02005791,0.0784066,-0.0011343,-0.03668002,-0.00466412,0.03234563,0.03469498,-0.02512564,-0.00363022,-0.04018285,0.02197551,-0.03862119,-0.02869399,0.10335626,0.02076915,-0.00787871,0.02014821,0.01340073,0.03482039,-0.05577774,-0.00062683,-0.02722844,0.03014906,-0.02416391,0.05586017,0.05493566,-0.01039673,-0.01796536,0.05766378,0.03417952,-0.01647757,-0.00608353,-0.01346999,0.00429629,-0.01874688,0.0448684,0.05033448,0.01503705,-0.25811511,0.03947539,-0.04610538,0.0610668,-0.01836508,0.00143279,0.02582431,0.01834924,0.03639827,-0.05572128,0.05544467,0.05407711,0.05930892,-0.02358136,0.00546815,-0.02896281,0.02376687,-0.04547623,0.08016821,0.02887932,0.07447733,0.04226879,0.24519064,-0.01361971,0.01862468,0.01952986,0.01927536,0.05478772,-0.05033047,-0.01835054,0.01732991,0.02077074,0.00221144,-0.02480796,-0.00022148,-0.03062753,-0.00236397,0.05225525,-0.01107278,0.00858625,-0.07016774,-0.04557655,-0.01546312,0.03723008,0.16372553,0.03133671,-0.00353326,-0.0977342,0.05081699,0.0978717,-0.06751945,-0.07913818,-0.03587272,-0.02557906,-0.00574181,0.05092178,0.01373459,-0.0166744,0.004308,0.00103828,0.03468011,-0.00244017,0.05532846,0.08135203,0.00837348],"last_embed":{"hash":"17hfc9e","tokens":161}}},"text":null,"length":0,"last_read":{"hash":"17hfc9e","at":1753423629356},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#6\\. Lottery Strategy Founded on Markov Chains#{1}","lines":[125,126],"size":470,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0947421,-0.02990792,-0.01651431,-0.01777751,-0.02689513,0.06150333,0.02983445,-0.0073758,0.03794719,-0.00268705,0.00697786,0.01895462,0.02433235,0.01283678,-0.0247071,-0.02411604,-0.01099393,-0.03038776,-0.0329889,-0.01764583,0.06858075,-0.04325718,-0.06880271,-0.05110738,0.04552634,0.00282175,-0.02769042,-0.0655131,-0.05244898,-0.21425109,0.0127219,0.03901357,0.01032611,-0.03181145,-0.08807135,-0.03878454,-0.01869377,0.07981002,-0.07917505,0.04150012,0.00795756,-0.00452244,0.0131306,-0.0209315,0.02524909,-0.03293816,-0.00374865,0.02390661,0.04314432,-0.01415434,-0.06137609,0.04714882,-0.02486725,-0.00299406,0.06950321,0.00131286,0.03422141,0.11444119,0.01963515,0.02848606,0.04651036,0.06912483,-0.18120155,0.05606274,-0.01351634,0.04241959,0.01171524,0.00738231,-0.02217296,0.06269528,0.06746554,0.03420554,-0.0239138,0.04465107,0.05575909,-0.00600633,-0.04737312,-0.0625485,-0.03406372,0.07292012,-0.05783986,-0.03982916,0.00124236,-0.01275718,0.02661692,0.01850362,0.00275515,0.0303506,0.07113544,-0.05207565,0.05803327,0.07527204,-0.01381761,0.00793307,0.06474077,0.01100774,0.03660641,-0.05271278,0.02656721,0.12764274,0.01399402,-0.00639714,0.01425332,0.00956683,0.03558096,-0.0475527,-0.04449632,-0.069317,-0.01674408,0.02012482,0.03955991,0.00437267,0.09200823,-0.04216256,-0.04771173,0.00565802,0.00809103,0.01271672,0.0265903,0.01363221,-0.05275024,0.04162946,-0.02282094,-0.03274114,-0.00776146,-0.00657205,0.01960719,0.04679881,0.01346857,0.0131519,0.04028464,0.01210961,-0.14115292,-0.03980843,-0.02098888,-0.03224183,0.01885604,-0.0018421,0.00131857,0.01747753,-0.00520581,-0.06303266,0.07900412,-0.11184284,-0.01059445,0.07811474,0.00302776,0.03848018,0.01975155,0.03269103,0.00042173,-0.02583637,-0.08248804,-0.0906484,-0.01751822,0.00886582,0.09328289,0.09030333,-0.05308061,-0.0279674,-0.06742247,-0.01933003,-0.00411534,0.09462819,-0.02444172,-0.09828581,-0.014412,0.02219352,-0.0540251,-0.13557027,-0.02911283,-0.01159961,-0.05016229,0.0407398,0.09797469,-0.03453394,-0.08152771,-0.06019151,0.00376148,0.00132971,0.02286207,-0.00105613,-0.02190242,0.00038559,-0.00284193,-0.06525025,0.02176838,-0.02277937,0.03389115,0.05643742,-0.05305422,0.02628816,-0.04842602,0.00159205,-0.03180495,-0.02508817,-0.03600459,-0.04741437,0.07371311,0.00515493,-0.02753371,0.0037616,-0.00412979,0.03613145,-0.01467892,0.04721632,0.00430969,-0.08428979,0.10239775,0.03506858,-0.04055932,-0.01342409,0.03937789,0.07429232,-0.03644931,0.01913784,0.03129322,0.02004425,-0.00456277,-0.00697568,0.03165376,-0.00671306,-0.07611804,-0.16237243,-0.06228211,-0.02668831,0.0036288,0.01635311,-0.01059667,0.02395929,-0.02172113,0.06385964,0.07094834,0.05616663,-0.0586016,0.00010002,0.05680845,-0.02157368,-0.01238741,-0.09681399,-0.03207659,-0.05096989,0.06032328,0.03258868,-0.02155845,-0.04515854,-0.05477957,0.0361273,-0.04031892,0.14188112,0.01648071,0.0249567,0.00721347,0.05924837,0.00697347,-0.01579079,-0.02096422,-0.00171448,0.00652339,-0.01606518,-0.04034548,-0.05477533,0.0174652,-0.06294624,0.01225116,-0.01774658,-0.0995632,-0.0649792,-0.00567461,-0.01594146,0.04382907,-0.00047629,0.03882718,0.05727603,-0.01849361,0.08446389,0.00847761,0.04733526,-0.02336579,-0.07819862,-0.02178339,-0.01568008,0.08163978,-0.00560489,-0.05078975,0.00995496,-0.00661872,0.05788175,-0.0173144,0.02085697,-0.01025166,0.00817508,-0.04560312,0.00413036,0.07189403,0.0107124,-0.01448302,-0.00910163,0.02676649,0.09131005,-0.05192402,-0.00316735,0.01143634,0.03482554,-0.06591754,0.03425587,0.08458827,0.02013187,0.0009857,0.07496414,0.0635623,-0.00280192,-0.00666068,0.00277924,0.02552518,-0.06298914,0.03755895,-0.02946863,0.04391519,-0.23596936,0.01665721,-0.04901846,0.04922206,0.01556825,-0.03034782,0.0306569,0.0208053,0.03218045,-0.01791615,0.08590545,0.03611144,0.00249881,-0.04080378,0.00255229,-0.01695195,0.01772561,-0.0396608,0.08133648,0.01579513,0.09253593,0.04486164,0.21606609,-0.01630745,0.03589379,0.0157529,0.01243196,0.05334656,-0.08153918,0.00439362,0.00326086,-0.00547153,0.01747501,-0.01860029,-0.06242646,-0.01261557,0.02129659,0.04998565,-0.01729087,0.0066188,-0.04803322,0.0034133,-0.03355391,0.00047495,0.14904813,0.04547137,-0.03028695,-0.09631769,0.03003029,0.06649337,-0.10197257,-0.06061396,-0.04706123,-0.05485972,0.00062508,0.05582139,0.00979269,0.00267933,-0.01360001,0.02822768,0.03813246,0.02165692,0.06502105,0.05847965,0.00924812],"last_embed":{"hash":"tijik8","tokens":240}}},"text":null,"length":0,"last_read":{"hash":"tijik8","at":1753423629409},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game","lines":[130,135],"size":815,"outlinks":[{"title":"_**All Lotto Numbers: Professors Win UK Lottery Jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09022351,-0.02962125,-0.01913794,-0.02193839,-0.02570373,0.06295653,0.030037,-0.00758782,0.03606254,-0.00446062,0.01413127,0.02450861,0.02563859,0.01929472,-0.02599212,-0.0160329,-0.00760773,-0.03449734,-0.03203143,-0.01602324,0.07017371,-0.04565824,-0.0725174,-0.05022659,0.04312288,0.01192349,-0.04118106,-0.06785595,-0.05381394,-0.21561994,0.01421991,0.04599404,0.01283942,-0.02858953,-0.08784109,-0.04281463,-0.02392691,0.08388679,-0.0820135,0.03839128,0.00220794,0.00046566,0.01306026,-0.01843634,0.01947942,-0.03026154,-0.00693021,0.03156554,0.0366562,-0.01472189,-0.06152558,0.04336081,-0.02117026,0.00088926,0.06273316,-0.00068078,0.03097322,0.10945646,0.02131515,0.03050378,0.05623086,0.06346516,-0.17442304,0.05350577,-0.01460804,0.04266118,0.00614249,0.00703635,-0.02154077,0.06181576,0.0685662,0.03702372,-0.019757,0.04503867,0.05250882,-0.00389886,-0.04553487,-0.06340399,-0.03441216,0.07273341,-0.0591507,-0.04706487,0.00421867,-0.00792359,0.02013075,0.01494238,0.00766834,0.03611251,0.07047384,-0.05043769,0.06279381,0.0867607,-0.01453678,0.00073378,0.06239692,0.0099738,0.03140899,-0.05803065,0.02976802,0.12855335,0.01161772,-0.01105834,0.01338986,0.00188829,0.02627397,-0.04220799,-0.04737858,-0.07179572,-0.01838899,0.01580381,0.03762377,0.00405806,0.09675191,-0.04048438,-0.04529198,0.00472416,0.01448978,0.01137992,0.0293432,0.01310168,-0.05502487,0.0379899,-0.02116589,-0.03117197,0.00079278,-0.00940927,0.01755405,0.04767944,0.01791456,0.01446159,0.04490959,0.01129083,-0.13545185,-0.04070273,-0.0061333,-0.03711097,0.02365863,-0.00001418,0.00206164,0.01815752,-0.00761744,-0.05068744,0.08839571,-0.11503331,-0.01463508,0.08366964,0.00556082,0.03460249,0.02342028,0.02970199,-0.0000034,-0.02784916,-0.08496114,-0.0879044,-0.01746731,0.01126366,0.09015237,0.08593148,-0.06070678,-0.027349,-0.06774125,-0.01440738,-0.00037402,0.09743868,-0.02062139,-0.09536131,-0.01177352,0.01627588,-0.04792635,-0.13365871,-0.03115818,-0.00475786,-0.05093462,0.04564362,0.09415035,-0.03212748,-0.08189034,-0.05946324,0.00727416,0.00366393,0.0410517,0.00270004,-0.02639129,0.00449842,-0.00065234,-0.0638271,0.0206452,-0.01544902,0.02809111,0.04790721,-0.05711267,0.02717037,-0.04425927,0.00269398,-0.03120769,-0.03312567,-0.02916278,-0.04270513,0.07715279,-0.00328916,-0.02294114,-0.00200205,0.00022082,0.04130038,-0.01133024,0.05067912,-0.0013011,-0.09104425,0.0986945,0.02959046,-0.03761164,-0.01348912,0.0372347,0.07518941,-0.03417213,0.01566328,0.03035584,0.01750994,-0.00915693,-0.00254963,0.03139837,-0.00098841,-0.07694516,-0.16035596,-0.06586462,-0.01888091,0.00883736,0.0135873,-0.00408835,0.02145158,-0.02613938,0.06780224,0.06976108,0.05223333,-0.06107576,-0.00605026,0.05196569,-0.0252961,-0.0162536,-0.10517101,-0.02882752,-0.05245072,0.05548514,0.03013543,-0.01988746,-0.04188593,-0.05392512,0.03594834,-0.03613426,0.14063933,0.01258778,0.0172549,0.00565031,0.05978686,0.00662792,-0.01456824,-0.01080968,0.00186213,0.00729338,-0.01095983,-0.04407946,-0.05123811,0.01607744,-0.06619779,0.01089082,-0.02214609,-0.09735699,-0.0609288,-0.008442,-0.0131211,0.04101785,0.00085187,0.03703107,0.0489367,-0.0221947,0.07485966,0.00777206,0.04958906,-0.02901713,-0.0740095,-0.01375061,-0.01509507,0.08354433,-0.00200496,-0.05226774,0.0048522,-0.008339,0.05996477,-0.02011805,0.0147537,-0.01247286,0.00387157,-0.04697379,0.0026063,0.07079267,0.00421149,-0.02609322,-0.00736395,0.02889271,0.08726802,-0.05376157,-0.00377151,0.01090232,0.03220934,-0.05852822,0.03059251,0.08807019,0.01393759,-0.00215788,0.07385886,0.05873781,0.00181647,-0.00830236,0.00575409,0.02923605,-0.06132794,0.02717776,-0.03144351,0.0334494,-0.23691118,0.02278535,-0.04928779,0.04630461,0.01126478,-0.03284387,0.0300925,0.02975455,0.03100321,-0.01901753,0.08500532,0.04048221,0.0085034,-0.04298386,-0.00031242,-0.02218865,0.02639503,-0.04510239,0.08602029,0.00830485,0.09918021,0.0446439,0.22209087,-0.01681918,0.03071387,0.01907264,0.01102211,0.05930521,-0.08443814,0.0041176,0.00013615,-0.00571504,0.02784998,-0.01638569,-0.06043479,-0.01705104,0.03139688,0.05419282,-0.01587256,0.00696639,-0.05053733,0.00120054,-0.03088263,0.0011436,0.1458104,0.0477234,-0.03191828,-0.0904622,0.02758675,0.06501692,-0.10867489,-0.0549702,-0.04761786,-0.06080537,-0.00204612,0.05812068,0.00810855,0.00498187,-0.01147522,0.02338183,0.03955042,0.0181606,0.06679236,0.05779266,0.00631688],"last_embed":{"hash":"145nbz1","tokens":198}}},"text":null,"length":0,"last_read":{"hash":"145nbz1","at":1753423629484},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#7\\. Lottery Strategies Based on Playing All Lotto Numbers in the Game#{1}","lines":[132,133],"size":631,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0940511,-0.02621192,-0.00490436,0.00900834,-0.05234309,0.0694227,-0.01415325,0.00753916,0.01144361,-0.00058599,0.00338424,0.03139097,0.0271646,-0.00259264,-0.01320614,0.00938912,0.00116352,-0.0122633,-0.07085499,-0.00463134,0.09870585,-0.04055479,-0.04118356,-0.07388676,0.02431599,-0.00118307,-0.05879803,-0.07330348,-0.03628375,-0.21868582,-0.00029773,0.02581329,0.00282973,-0.05549508,-0.10604997,-0.02157032,-0.06041234,0.06555894,-0.04051983,0.04077801,0.00338938,0.03266598,0.01580671,-0.01332982,-0.01108929,-0.03797412,-0.00120185,0.02938992,0.08323935,-0.00748569,-0.0509603,0.04445399,-0.00920687,0.01938697,0.09005865,0.04690879,0.04164392,0.10226965,0.02033573,0.03020014,0.01421036,0.08499297,-0.21348999,0.06170435,-0.00478626,0.05561436,0.01808062,0.00303249,-0.0113319,0.0799316,0.0397541,0.02835948,-0.03021711,0.03934714,0.04698868,-0.01883235,-0.06512713,-0.07937998,-0.02035481,0.04781679,-0.04813129,-0.00050383,-0.01972785,-0.009803,0.02595608,0.02544433,0.01006017,0.03646746,0.07429457,-0.050077,0.0515388,0.06844781,0.02456036,0.00755718,0.04244599,0.01142257,0.02330978,-0.06632021,0.01673046,0.09941254,-0.001628,0.00799277,0.02417329,0.00994328,0.04007121,-0.04572231,-0.04579251,-0.058277,-0.02676291,0.03790397,0.0521221,0.02205011,0.08569381,-0.02722391,-0.06880769,-0.01251006,0.01198394,0.00929075,-0.0038001,0.01025957,-0.05740816,0.01676287,0.01921683,-0.03633664,0.00324742,-0.02414808,0.02527574,0.04784242,-0.00388855,0.01223555,0.01660684,-0.00681762,-0.11688886,-0.04302784,-0.04435895,-0.04251932,-0.00362772,-0.01547357,0.00784013,-0.00567844,-0.01562463,-0.11131419,0.08348258,-0.10149892,0.01381654,0.04916634,0.00406649,0.02019079,0.02229478,0.04213607,-0.01971162,-0.02904196,-0.04217337,-0.03693051,-0.00157451,0.02499726,0.07137622,0.0663235,-0.034417,-0.01293637,-0.0494742,-0.02558018,-0.03641804,0.11305751,-0.0082658,-0.11488756,-0.03141496,0.02888376,-0.02381197,-0.10697982,-0.03262004,-0.00659343,-0.04974656,0.04650151,0.11442887,-0.02945606,-0.06443128,-0.02922291,-0.04198273,0.02422781,-0.00615747,-0.03180231,-0.03980708,0.01063387,-0.02861657,-0.05319231,0.0300024,-0.02142376,0.06326623,0.05026698,-0.02610702,0.01441678,-0.03132191,0.01951502,-0.01636976,0.0006168,-0.03707384,-0.06088952,0.09440651,-0.01125377,-0.01590427,-0.00742367,-0.00128804,-0.01111217,-0.03498219,0.04420073,-0.02066658,-0.07193405,0.08118023,0.03208945,-0.05047349,0.00540636,0.04796058,0.07640989,-0.04807152,0.05415301,0.00781908,0.02566455,-0.01681064,-0.0288213,0.02456407,-0.0233665,-0.05812377,-0.16962098,-0.05830598,-0.0342961,-0.01727626,0.02802914,-0.02751411,0.00912667,-0.02629039,0.04908329,0.0537803,0.05186411,-0.06624799,0.00401603,0.05768684,0.00271071,-0.01287662,-0.09558403,-0.05694357,-0.03277139,0.07932215,0.02936199,-0.03707778,-0.02855277,-0.0628529,0.02504349,-0.05185118,0.16994281,0.0025663,0.00903756,0.01444402,0.06263793,0.0190538,-0.00065401,0.02191426,0.02183226,-0.01815014,-0.03186986,-0.05019109,-0.04271319,-0.01235572,-0.04734594,0.01994334,-0.00661082,-0.10585264,-0.02771763,-0.0127894,-0.03370259,0.03643453,0.01665331,0.047515,0.04431101,0.05414942,0.06323586,-0.0237993,0.04252305,-0.02323114,-0.09071776,0.00033705,-0.00421033,0.05079279,-0.04350812,-0.02765361,0.03173608,-0.02507182,0.04984889,-0.00043935,0.0032615,0.00556638,0.01026748,-0.0097159,-0.01602642,0.05694599,0.01246202,0.02877946,-0.00739604,0.00775532,0.04243023,-0.06090387,0.01307715,-0.00958388,-0.0086458,-0.05713993,0.03713635,0.078081,0.02833516,-0.00765745,0.0543706,0.04922772,-0.00275853,-0.0093808,-0.02569623,0.00450453,-0.0371811,0.0392277,0.02134375,0.01559414,-0.25139216,0.03623543,-0.01360513,0.04098749,-0.01322827,-0.02324898,0.05092904,0.01326326,0.01945316,-0.01708381,0.08459506,0.02744052,0.01976682,-0.02876983,0.01212265,-0.01626538,-0.01814669,-0.04032071,0.08402661,0.02163361,0.09405582,0.06630509,0.22458442,-0.00617136,0.04900913,0.04026294,0.01289711,0.04574695,-0.04947192,0.0177029,0.01457283,0.0229532,0.00171549,-0.04677503,-0.04960668,0.01056842,0.0128935,0.05822778,-0.04853239,0.0379499,-0.08690377,0.01049974,-0.02329104,0.0399898,0.15559815,0.01640232,-0.01622238,-0.10525756,0.06883734,0.03946007,-0.09069367,-0.0410349,-0.03401441,-0.01026885,0.01600765,0.03253261,0.04208592,-0.01589081,-0.00542095,0.02221839,0.04654093,0.02665488,0.07756421,0.03183517,0.00084505],"last_embed":{"hash":"wnwlsj","tokens":257}}},"text":null,"length":0,"last_read":{"hash":"wnwlsj","at":1753423629558},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#8\\. Lottery Strategy: Generate 10/12-Number Lotto Combinations, Wheel and Play Them in 5/6-Number Games","lines":[136,143],"size":673,"outlinks":[{"title":"_**Jackpot Lottery Strategy: 12 Numbers Combinations, Lotto-6 Wheels System**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":5},{"title":"_**Lotto Strategy, Software: <u>12-Numbers</u> Combinations Applied to 6-Number Lottery Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":6},{"title":"_**Lottery Strategy, Software: <u>10-Numbers</u> Combinations Wheeled to 5-Number Lotto Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08131991,-0.01134939,-0.03954271,0.01652715,-0.04052549,0.06807082,0.02912288,-0.01995394,0.00522095,-0.0018628,-0.03063666,0.00366627,0.08022424,-0.00985929,-0.00354203,-0.01472202,-0.0209389,0.02847972,-0.04822428,-0.01798176,0.07553665,-0.03068688,-0.04351857,-0.05199454,0.06617694,-0.02040144,-0.01097591,-0.09351946,-0.04083009,-0.25923645,0.00735585,0.00313199,-0.03387699,-0.065233,-0.07662645,-0.0218496,-0.06269322,0.06910896,-0.06165496,0.05531548,0.01556369,0.0024969,0.02113509,-0.0348958,0.01200885,0.00338175,-0.02193629,0.01308805,0.02845393,0.01234438,-0.0520861,0.06341819,-0.02010526,0.04109753,0.06119052,0.03964082,0.06203939,0.11927691,0.02101688,0.05047653,0.03267457,0.06076146,-0.16758393,0.03905248,-0.05301944,0.03246537,0.00037655,-0.00089677,-0.01418107,0.11472892,0.01352822,0.00206813,-0.04767118,0.02846507,0.04519036,-0.01809378,-0.03612024,-0.05404544,-0.0348768,0.03861355,-0.02557556,-0.0245981,-0.01932327,-0.01847393,0.03546101,0.01816101,0.03186719,0.03833442,0.06582681,-0.0693707,0.05709033,0.07023326,0.03435404,0.02337555,0.04999331,0.03254424,0.01868115,-0.00245636,0.036826,0.11132994,-0.00906693,0.01967811,-0.00595271,-0.01500046,0.06565302,-0.09125587,-0.02623528,-0.05097938,0.00579946,0.02888085,0.00193274,-0.01316858,0.09433636,-0.02823146,-0.02894109,0.05409454,0.03719797,0.03891375,-0.02447964,-0.00061998,-0.04545452,0.02943077,-0.01297291,0.00069394,-0.0072594,-0.02552585,0.02501998,0.06696369,0.01594127,0.03029863,0.01066462,-0.00856889,-0.15277103,-0.06165758,-0.03322633,-0.02786753,-0.02034633,0.01994625,0.01433604,-0.00681221,-0.02723689,-0.03969794,0.05583429,-0.08710441,-0.01912098,0.08123421,0.01726028,0.01355544,-0.000808,0.01090099,0.00080598,0.00204643,-0.03510694,-0.08876476,0.00305428,0.01020688,0.04019632,0.07226364,-0.04649262,-0.00425108,-0.03438959,0.00825161,-0.04990261,0.12466791,-0.04263831,-0.09616579,-0.0170821,0.02567989,-0.03605548,-0.06743719,-0.05729032,-0.03422106,-0.02255805,-0.04285789,0.0903592,-0.01904481,-0.09498803,-0.09428018,-0.00993736,0.00337094,0.00309999,-0.01750383,-0.04339701,0.00266038,-0.03350586,-0.03981976,0.04579953,-0.03468844,0.07695434,0.05369563,-0.07514419,-0.00570843,-0.05167999,0.01267542,-0.01047206,-0.01748053,-0.02103602,-0.05818577,0.04267211,-0.01004065,-0.03990667,-0.0326982,-0.0049404,-0.00640584,-0.02219512,0.05925541,0.03282788,-0.08535798,0.1188053,0.03701313,-0.0482043,0.00334726,0.03311241,0.07045874,-0.01087192,-0.00892089,0.01237224,0.02500151,-0.03672739,-0.01167384,0.00829813,0.0041905,-0.05782629,-0.18917507,-0.0377207,-0.04923191,0.00520121,0.02635946,-0.05492753,-0.00447306,-0.01296419,0.03630299,0.0787269,0.06324248,-0.01873016,-0.01738895,0.08199074,0.00153432,-0.01346946,-0.06060629,-0.0056759,-0.05202342,0.04411272,0.00972808,0.0019267,-0.00685313,-0.08950613,0.0382417,-0.02122819,0.144307,0.02998246,0.03388241,0.03879428,0.05494508,0.02954103,-0.02578088,0.01212753,0.02544418,0.0221045,0.01628301,-0.04588115,-0.0213323,-0.00441861,-0.04231513,0.00607499,-0.02445456,-0.11098345,-0.02797833,0.00554123,-0.04826793,0.07840054,0.00493395,0.07697109,0.05465449,-0.01921844,0.06753723,0.01773891,0.07127803,-0.0220859,-0.07183372,-0.04505855,-0.01358998,0.02157523,-0.02159876,0.00139815,0.02638536,-0.01509508,0.03336963,0.02734965,-0.00402931,-0.00887239,0.0203246,-0.00740412,-0.00328255,0.08473827,0.00496514,-0.00433363,-0.02354999,0.05182393,0.03528237,-0.07228313,-0.02574531,-0.03144182,0.04242758,-0.04802165,0.06796182,0.03431039,0.0308169,0.00468043,0.03783251,0.01742509,-0.00112463,-0.01951695,-0.02654019,0.02494988,-0.04533926,0.03618921,0.01152134,-0.00303043,-0.24445425,0.01148382,-0.06199396,0.03972816,-0.02899823,-0.00308049,0.02724059,0.00619034,0.03302885,-0.05560261,0.00730341,0.05647881,0.02420457,-0.01351732,0.00187354,-0.02378551,0.02823408,-0.04271102,0.09242682,0.03981147,0.08220221,0.03624559,0.23349388,0.00503734,0.01464225,0.01664459,0.00229668,0.01764437,-0.03827909,-0.00086968,0.00080767,-0.01722408,0.0520249,-0.00184837,-0.00108337,-0.00566528,-0.0055267,0.06454951,-0.0189129,-0.0018136,-0.09749223,-0.01027967,0.03326594,-0.00638439,0.16344798,-0.01158694,-0.02794537,-0.06324898,0.03015779,0.04260548,-0.09327625,-0.01644595,-0.03723904,-0.04415527,0.00179389,0.0652502,0.00801466,-0.00593467,0.0164122,0.03363767,0.0545261,0.02946998,0.06558754,0.07772249,0.00708517],"last_embed":{"hash":"1ao11r4","tokens":479}}},"text":null,"length":0,"last_read":{"hash":"1ao11r4","at":1753423629645},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning","lines":[144,156],"size":1826,"outlinks":[{"title":"_**Lottery, Lotto Strategy in Reverse: Turn Loss into Win**_","target":"https://saliu.com/reverse-strategy.html","line":9},{"title":"_**Lottery Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":10},{"title":"_**Lottery Strategy Reversed Decades, Last Digits, Odd Even**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":11},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":12}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06922293,-0.00423323,-0.0279418,0.00275487,-0.02585271,0.05430916,0.04192575,-0.02465754,0.00557659,-0.00374958,-0.02992257,0.01281728,0.08188347,0.00035008,-0.00169604,-0.00959064,-0.02190359,0.0244364,-0.04779551,-0.0227405,0.06189661,-0.02972583,-0.05697051,-0.03862571,0.07022961,-0.01555282,-0.01217321,-0.09760956,-0.0446366,-0.26149032,0.01212106,0.00504504,-0.03022529,-0.05947728,-0.06206615,-0.03620116,-0.05958035,0.08636042,-0.06633747,0.05106171,0.02305788,0.00089111,0.01417429,-0.03373884,0.00441311,-0.00038062,-0.02004935,0.01506147,0.01147767,0.00337837,-0.0511892,0.05002976,-0.02560086,0.02724002,0.04453516,0.02388836,0.05302284,0.12151914,0.02856968,0.04773997,0.04163237,0.06344752,-0.16406612,0.03785251,-0.0585984,0.03850468,-0.00855306,0.00070334,-0.01055327,0.13227397,0.0086028,0.00382876,-0.04346327,0.02801736,0.0350195,-0.02001908,-0.02558091,-0.04948927,-0.0378379,0.04692188,-0.0299291,-0.02923903,-0.01610712,-0.01265585,0.02056614,0.01573749,0.02239377,0.03006083,0.05824595,-0.05451453,0.06443636,0.07193809,0.02012648,0.01815428,0.04907235,0.03329914,0.00533872,-0.0140321,0.03020193,0.11235666,-0.00765457,0.01305682,0.00282977,-0.02378076,0.05832698,-0.08044852,-0.02540481,-0.05451851,-0.00897329,0.02646675,0.00242008,-0.02319524,0.09182759,-0.01673208,-0.02071547,0.05472334,0.04798871,0.03362076,-0.03903331,0.00303507,-0.04894156,0.02925511,-0.01056703,0.01049676,-0.00638287,-0.03382415,0.02412,0.07539479,0.01788482,0.0190845,0.00860688,-0.00096217,-0.14893974,-0.05856977,-0.02099174,-0.02417617,-0.01568985,0.02977766,0.01636134,-0.00129072,-0.03163861,-0.02318294,0.04689862,-0.0908572,-0.02791158,0.08786897,0.01777303,0.01774793,-0.01408453,0.00579371,-0.0016879,0.00321936,-0.04466829,-0.08589815,0.00287425,0.00188663,0.03370616,0.06826849,-0.05595531,-0.01115453,-0.05398157,0.02285042,-0.04923546,0.13769527,-0.05618604,-0.08533921,-0.01214368,0.0158795,-0.02072908,-0.07127972,-0.06408653,-0.03738412,-0.03013746,-0.04615281,0.08026867,-0.01653691,-0.10404857,-0.09505469,-0.00463488,-0.00007922,0.03111283,-0.00657379,-0.04815297,0.01013122,-0.02451098,-0.04131097,0.04343514,-0.02846958,0.07399669,0.05925284,-0.08951288,0.01184441,-0.04534199,0.01128305,-0.02432594,-0.02141239,-0.02192552,-0.05712666,0.04236594,-0.01959832,-0.03565532,-0.03244575,-0.00829107,0.00827399,-0.01189462,0.05919911,0.02572813,-0.08915472,0.12475289,0.03459249,-0.05230952,-0.00311058,0.02786217,0.07455625,-0.00395701,-0.01532541,0.01944049,0.03490982,-0.05030718,-0.02277894,0.00497593,0.01047771,-0.04835333,-0.18699352,-0.04049792,-0.03506484,0.01348014,0.02808653,-0.04765989,-0.00745532,-0.0046695,0.01791373,0.08819567,0.06194625,-0.01462958,-0.01189707,0.07766711,0.00070006,-0.02163049,-0.07052054,0.00304646,-0.05376754,0.04326868,0.01528192,-0.00018114,-0.01265573,-0.09172236,0.02962035,-0.01348861,0.14103982,0.03362923,0.04419837,0.03652709,0.05005289,0.04555517,-0.01871049,0.02090283,0.02826508,0.02592357,0.0192667,-0.04576598,-0.00774443,-0.01040106,-0.03053755,0.0082078,-0.04132034,-0.11610057,-0.02970287,0.00913608,-0.0484392,0.09278025,-0.01137641,0.07644926,0.06185222,-0.02014307,0.0718195,0.01758245,0.07813095,-0.02535314,-0.07426849,-0.02842129,-0.00395032,0.0355971,-0.00820046,0.00219484,0.01764149,-0.0065768,0.03626076,0.0301534,-0.0053557,-0.00840742,0.02437889,-0.01302339,-0.00862278,0.07963055,-0.0116799,-0.02681491,-0.02247467,0.05684884,0.0388101,-0.07271896,-0.02759087,-0.03664308,0.05372892,-0.03519843,0.06158148,0.03137743,0.01701068,0.00836776,0.02406362,0.01894889,0.00594241,-0.02307589,-0.03430769,0.03347294,-0.05176635,0.03290135,0.01523684,-0.00453337,-0.23517229,0.01726468,-0.05431421,0.03432155,-0.04188976,0.00250352,0.0260842,0.02102752,0.03426751,-0.04886141,-0.00076414,0.06294512,0.02445968,-0.01707547,-0.00446365,-0.02453632,0.04492004,-0.04468459,0.09420203,0.03163305,0.07617433,0.02919421,0.22921576,0.00133644,0.01155111,0.02148107,0.00138501,0.02142568,-0.05021441,-0.00370353,-0.00209807,-0.01835411,0.05422665,-0.0034265,-0.00671831,-0.02241581,0.00401662,0.06881387,-0.01085152,-0.00576145,-0.09042656,-0.00205658,0.04322943,-0.01151063,0.1535634,-0.01281184,-0.03138409,-0.07089747,0.0209562,0.04504664,-0.08773287,-0.00996777,-0.02855747,-0.05486138,0.00786221,0.07138297,0.00157477,-0.00148948,0.0153045,0.03949945,0.0485039,0.02947718,0.06777578,0.08299389,0.00130112],"last_embed":{"hash":"121f20x","tokens":316}}},"text":null,"length":0,"last_read":{"hash":"121f20x","at":1753423629876},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#9\\. Lottery Strategy in Reverse: Turn Losing into Winning#{1}","lines":[146,151],"size":1247,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08654941,-0.02548089,-0.02505987,-0.00599587,-0.05322075,0.07609154,-0.00962161,0.01134271,0.025079,-0.00790491,-0.01318169,-0.01215028,0.04587405,0.0120061,-0.00710714,-0.0162279,0.04490238,0.02716258,-0.06116541,-0.00688289,0.08760694,-0.03799311,-0.07388765,-0.04721957,0.02701685,-0.02460697,-0.04092127,-0.05563152,-0.01677139,-0.24005052,0.02636076,-0.01971302,-0.0319049,-0.01137242,-0.09495284,-0.02700647,-0.05433577,0.05030249,-0.06811339,0.01252492,-0.00731209,0.05231223,0.04374698,-0.01061154,0.0315446,-0.0247703,0.02425301,0.02622958,0.03808881,-0.00463585,-0.06899506,0.01561521,0.01479174,0.01812895,0.01340083,0.032945,0.02724352,0.13386048,0.02497098,0.04516944,0.01071054,0.05467419,-0.1707256,0.06493507,-0.02110236,0.02598247,0.0025072,-0.02506432,0.01572081,0.06751747,0.05682234,0.03168958,-0.02767017,0.07062517,0.0247882,-0.01666971,-0.03848115,-0.10257557,-0.0300213,0.0245379,-0.03368406,-0.04467051,0.01136034,-0.00350827,0.01406736,0.02509777,0.03555168,-0.00697134,0.06728052,-0.07865379,0.02355334,0.01982373,0.05798641,0.02529557,0.00406475,0.00171125,0.00559415,-0.02998021,0.02594016,0.11665516,0.00831623,0.04339265,0.01498898,-0.00381757,0.0290601,-0.04939124,-0.02884579,-0.03367597,-0.04481545,0.03562229,0.05627636,-0.00399724,0.07893492,0.00236465,-0.06222194,-0.0103998,0.02343115,0.00595522,0.00599072,0.00798065,-0.0609025,0.03745084,0.03990336,-0.01273164,0.0124295,-0.00789169,0.02779424,0.03144643,-0.0124321,0.06064828,0.02356661,0.00178372,-0.10045598,-0.04567368,0.01440224,-0.04030608,-0.01233394,0.03227492,-0.00479162,0.04033014,-0.02051426,-0.05580236,0.04659615,-0.11123672,-0.05298074,0.04810964,-0.01928254,0.0125862,-0.00523121,0.01641681,-0.0179515,-0.03293226,-0.05660577,-0.03000541,0.01365555,0.03865195,0.08191629,0.06501253,-0.08299081,0.00157363,-0.06082462,-0.03203579,-0.05999698,0.10197465,-0.05885742,-0.1022009,-0.02591832,0.0441148,-0.02493566,-0.08003385,-0.04804574,-0.02017481,-0.06589403,0.02153133,0.09695309,-0.01546273,-0.11010402,-0.02203039,-0.03675712,-0.01053177,0.02829228,-0.010945,-0.03710577,0.00391389,-0.00040246,-0.05148838,-0.00239853,-0.0032808,0.03338724,0.01727452,-0.07550804,-0.00710272,-0.02385265,0.01646104,-0.02461443,-0.02192389,0.00370587,-0.03707529,0.05479531,-0.01436574,0.00244053,0.01531854,0.00059011,0.03562455,-0.04321161,0.04066267,-0.01509104,-0.0844826,0.09219497,0.05247478,-0.03979329,-0.00044249,0.03974366,0.07383283,-0.02576266,0.05568375,0.01952758,0.02946705,-0.02641082,-0.02905598,0.00682006,0.01914644,-0.07430243,-0.20112427,-0.04168541,-0.02610575,0.04613319,0.0632595,-0.03201475,-0.02024722,-0.04441204,0.04975264,0.06603627,0.0961234,-0.04183811,0.0160229,0.05728178,0.00101003,0.05012959,-0.11078267,-0.02681685,-0.04044337,0.02692535,0.05486143,0.00591887,-0.00117159,-0.09606162,0.01622965,-0.02969551,0.14509545,-0.04132833,0.03832108,0.03705403,0.07596825,-0.03940305,0.00222596,0.02788639,0.01711474,0.03203994,-0.05681598,-0.07245816,-0.01939243,-0.00819823,-0.04079023,-0.00432594,-0.00744117,-0.11688357,-0.02808095,-0.0247687,-0.00060879,0.05270711,-0.01215833,0.0567552,0.03963932,0.02514719,0.05100015,-0.02760246,0.04820064,-0.03231394,-0.09692325,-0.00043113,-0.02224766,0.064316,-0.03548264,-0.0570011,0.04129826,-0.00922834,0.06983728,-0.00863014,-0.01820449,-0.00425261,-0.01884136,0.0101816,-0.01909193,0.07589126,-0.00163634,0.07355838,-0.02240292,0.04200732,0.02756963,0.01231542,0.00497386,-0.02756931,-0.03636225,-0.01646515,0.06121416,0.11932948,0.01675841,0.02868515,0.05172858,0.00846789,-0.04860849,0.00491895,0.01113339,0.00271319,-0.06531338,0.03807344,0.03536939,0.02467501,-0.244095,0.0451934,-0.02721276,0.01479585,-0.0306885,-0.02291352,0.0229482,0.02180318,0.0556246,-0.04058806,0.04698322,0.07045425,0.0217081,0.00189435,0.00927522,0.00892238,0.00305211,-0.03390976,0.07309161,-0.00767362,0.05363989,0.03532732,0.23343989,0.00960383,0.0371622,0.03736885,-0.00797526,0.03532096,-0.08014971,0.01080979,0.01922895,0.00548375,0.05682919,0.00056819,-0.03969523,0.01069441,-0.04160256,0.06189328,-0.02953747,0.01264141,-0.07723052,0.002963,0.03743387,0.03492531,0.13346644,0.0345259,-0.04131271,-0.09455959,0.06649567,0.05820207,-0.06738827,-0.0232469,-0.06583931,-0.04538753,0.01429153,0.0427767,0.02688965,-0.00355771,0.00256023,0.00642466,0.03852535,-0.0036468,0.04738259,0.03994036,0.01218125],"last_embed":{"hash":"1o0cuu1","tokens":447}}},"text":null,"length":0,"last_read":{"hash":"1o0cuu1","at":1753423629966},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems","lines":[157,170],"size":1739,"outlinks":[{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":5},{"title":"_**Create, Make Lotto Wheels, Lottery Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":6},{"title":"_**The Best <u>Lotto Wheels for 9, 12, 18, 21 Numbers</u>: 4-in-6 Minimum Guarantee**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":8},{"title":"**_The Best On-The-Fly Wheeling Software Applies Real Lottery Filtering_**","target":"https://saliu.com/bbs/messages/wheel.html","line":10},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":11},{"title":"_**Lottery Wheeling Software: Convert Systems to Player's Tickets**_","target":"https://saliu.com/bbs/messages/857.html","line":12},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0943489,-0.01803282,-0.01820119,0.00259661,-0.05004515,0.06810904,0.00823701,0.00904276,0.03397594,-0.01182633,-0.00738431,-0.00114131,0.05141739,0.00987047,-0.00533556,-0.00781582,0.04112351,0.01279778,-0.04843168,0.00366249,0.09618672,-0.03216869,-0.08454532,-0.04220866,0.03659522,-0.02679153,-0.03903159,-0.04598747,-0.0230738,-0.23877007,0.02657072,-0.03410772,-0.03796124,-0.02086017,-0.07566209,-0.04841264,-0.05289355,0.05944571,-0.06401201,0.02859954,-0.00772606,0.04920907,0.03363157,-0.00120597,0.03639075,-0.03053483,0.02487323,0.01961921,0.0097295,-0.01655051,-0.05990825,0.03566966,0.00710058,0.00455536,-0.00035232,0.03675883,0.02089469,0.13788491,0.03170368,0.03861789,0.01627981,0.05934782,-0.17445005,0.05053063,0.00980173,0.0331389,-0.00460298,-0.00016943,0.01041376,0.07582425,0.06628363,0.03388601,-0.02683292,0.05808487,0.0278441,-0.00755612,-0.03836648,-0.10512222,-0.03563922,0.03354584,-0.03490803,-0.05878548,0.00652801,-0.00810154,0.00526241,0.00908399,0.03052564,-0.01327779,0.06428764,-0.07825268,0.03168643,0.04001105,0.05322158,0.02554621,0.00481797,-0.01399255,-0.00037339,-0.03459172,0.03592122,0.1263245,0.00896256,0.03469057,0.02111738,-0.00191169,0.02971596,-0.04954042,-0.03432415,-0.04566179,-0.05137499,0.02595591,0.05119122,0.00248318,0.09345908,0.00383177,-0.05028009,-0.0237258,0.01805581,0.02812885,0.00693478,0.00146017,-0.0665967,0.03723498,0.04990714,-0.02180783,0.00615413,-0.01447674,0.01934059,0.03438677,-0.01696658,0.04254924,0.01361388,-0.00324482,-0.10163239,-0.04921524,0.01761732,-0.04048358,-0.0103044,0.03548175,0.00680081,0.04487706,-0.02287949,-0.04823801,0.04273798,-0.11621895,-0.06503987,0.06128304,0.00167048,0.02309747,0.00982676,0.03527107,-0.01995688,-0.03788135,-0.05754851,-0.03828692,0.01580404,0.03746928,0.0777996,0.06184309,-0.08091629,-0.01975182,-0.05838296,-0.00943447,-0.06694598,0.13016337,-0.07167515,-0.11600857,-0.02025209,0.02519204,-0.01799691,-0.08524226,-0.03929865,-0.01931583,-0.06649385,0.00678753,0.092682,-0.0147656,-0.09927414,-0.03183591,-0.04120849,-0.00640522,0.04728389,-0.00772948,-0.04987293,0.0172309,0.00645944,-0.04108205,-0.01359767,0.00261173,0.03398988,0.03032812,-0.07719827,-0.01252765,-0.03938851,0.01366171,-0.02174232,-0.01896141,0.01544926,-0.02869771,0.055181,-0.0207435,-0.01274423,0.01997709,-0.00250666,0.04245416,-0.04392669,0.04679768,-0.01558115,-0.07339851,0.07533702,0.05668053,-0.03471636,-0.0002068,0.01593511,0.08324679,-0.02755347,0.05882623,0.0234971,0.04069352,-0.0370024,-0.04223445,0.01203163,0.0307637,-0.07645276,-0.19256353,-0.05263861,-0.02301039,0.03561677,0.06310548,-0.02157988,-0.01045364,-0.04991452,0.03303158,0.05539053,0.07385778,-0.03532709,0.01217067,0.04853022,-0.00802364,0.04476481,-0.11345828,-0.0317826,-0.03023138,0.02426714,0.06020009,0.01237655,-0.02452136,-0.09922031,0.0138644,-0.02713663,0.13464507,-0.04114052,0.03899518,0.02353335,0.06173588,-0.02145131,0.01158122,0.0323387,0.01697626,0.02522121,-0.04714932,-0.08518291,-0.02386655,-0.00053968,-0.03798943,-0.01585095,-0.01883153,-0.08859472,-0.02650797,-0.02243028,0.00888968,0.07051192,-0.00986558,0.07906214,0.0398786,0.01316105,0.05770329,-0.01174096,0.05954375,-0.02526531,-0.09534064,0.00822001,-0.0142242,0.05876507,-0.02316131,-0.05423247,0.03600828,-0.00937258,0.06058022,-0.01691553,-0.01638384,-0.00476162,-0.02575453,-0.01616604,-0.01148741,0.08362303,-0.0066225,0.04500822,-0.0310508,0.04721949,0.02173975,0.00428847,0.00065324,-0.03071672,-0.0207962,0.00207481,0.06563911,0.11076029,0.00390727,0.01539027,0.04177403,-0.00221962,-0.05146385,0.00274547,-0.00268095,0.00814308,-0.06289659,0.02872196,0.03349177,0.02390859,-0.23530442,0.05192992,-0.02672653,0.0078917,-0.03574918,-0.00749266,0.01829512,0.02799632,0.04591198,-0.03069079,0.05124592,0.07080084,0.01265479,0.01278938,0.0119217,0.00492921,0.02862537,-0.0379115,0.07262953,-0.00548408,0.06742831,0.04617393,0.23154722,0.01627211,0.02488851,0.03287312,-0.00791556,0.05706153,-0.07678328,0.00381963,0.02013724,0.0059127,0.04812149,-0.00742017,-0.04213378,-0.0043234,-0.02507402,0.07828365,-0.01660017,0.01449442,-0.07240646,-0.00319166,0.02534909,0.02587075,0.1423146,0.04257992,-0.04676089,-0.10350821,0.05472111,0.04926328,-0.07500064,-0.0280633,-0.06819058,-0.05352517,0.01796517,0.04992275,0.02842773,-0.01223169,0.01137992,0.00515132,0.03877295,0.01718834,0.04596828,0.04512326,0.00212308],"last_embed":{"hash":"6ob8bp","tokens":166}}},"text":null,"length":0,"last_read":{"hash":"6ob8bp","at":1753423630113},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#10\\. Lotto Wheels, Abbreviated Lottery Systems#{1}","lines":[159,160],"size":565,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07659917,-0.03642086,-0.02036722,-0.0391034,-0.06148609,0.03974025,-0.02119946,0.00286583,0.03115931,-0.00513807,-0.00718086,0.02091793,0.07114936,0.02320975,0.02208346,-0.03620883,0.0123277,-0.00837113,-0.08103969,-0.01486307,0.06288172,-0.04886207,-0.05185202,-0.02408449,0.06932344,0.00984412,-0.03423179,-0.08695182,-0.03618927,-0.24945877,0.0400094,0.014704,-0.01902597,-0.04778948,-0.06294919,-0.02233559,-0.02190993,0.0954268,-0.0518831,0.03905197,-0.0132753,0.00474697,0.00583398,-0.00082457,-0.0383658,-0.05315136,-0.01089329,0.02190754,0.04056698,-0.01605681,-0.06400045,0.01887466,-0.0321786,0.01786373,0.01770366,0.02832193,0.0384057,0.12248029,-0.00842144,0.04138486,0.02906378,0.05656119,-0.18996485,0.06436512,-0.0412965,0.05001365,-0.01434335,0.02234687,-0.00618593,0.07387362,0.01509949,0.00967075,-0.05221942,0.06649646,0.05254248,-0.03875818,-0.05266797,-0.04796835,-0.04545331,0.06510678,-0.06809723,-0.02715184,-0.02964628,0.00571298,0.00936564,0.0252302,0.0195519,0.04247939,0.06228338,-0.03811211,0.07197756,0.05767658,0.00201468,0.01336945,0.04824062,0.02533972,0.04787141,-0.04692281,0.02327628,0.13106626,-0.01339644,-0.03479686,0.03126255,-0.00141012,0.02060719,-0.01297845,-0.01559745,-0.04248332,-0.03593749,0.00532498,0.00318728,-0.00196285,0.03358221,-0.06194234,-0.02090032,0.01596563,-0.03704659,-0.00040934,0.01310948,-0.01289677,-0.0555699,-0.00488643,-0.00380727,-0.00806878,0.0173533,-0.02651386,0.02741774,0.05411612,0.03166481,0.00520769,0.04459855,0.04476241,-0.13895723,-0.04739109,-0.0328902,-0.02773143,-0.0160902,-0.03586292,-0.00341494,0.01719418,-0.01115141,-0.02541226,0.09238093,-0.13457379,0.00469287,0.14007191,0.00129568,0.01486643,-0.00525749,0.00310521,-0.02571906,-0.01311804,-0.06745296,-0.06310111,0.01420792,0.00297397,0.06826693,0.06404623,-0.05115004,-0.03181887,-0.0537365,0.00304268,-0.02756467,0.12166956,-0.01277378,-0.12814967,-0.03364878,0.0352139,-0.0431484,-0.09289601,-0.02568841,-0.03434467,-0.02641228,0.00321748,0.07826733,-0.02145551,-0.09476791,-0.0502257,-0.01175657,0.01187966,0.04319472,-0.02891224,-0.03620525,0.00574287,-0.01897361,-0.060767,0.04160403,-0.02306653,0.03043503,0.05044602,-0.04568062,0.03944884,-0.04567272,0.00657525,-0.0257084,-0.00765921,-0.02982311,-0.04586468,0.06726664,-0.02354498,-0.02167927,-0.01363936,-0.00665062,0.0157284,-0.01987023,0.01651702,-0.01020185,-0.03314185,0.08831614,0.01042651,-0.04523383,0.03259799,0.04578582,0.08422083,-0.07749044,0.02337636,0.00394077,0.03471882,-0.01215656,0.00763322,0.01709095,0.02532256,-0.05952382,-0.17801982,-0.04185174,-0.03582063,0.01702511,0.01743122,-0.0271392,0.01827381,-0.01906369,-0.02282743,0.07123648,0.11330484,-0.03121227,0.01532979,0.07276572,-0.02086223,-0.02750457,-0.08469193,-0.0218023,-0.03550386,0.04710818,0.03177037,-0.00030528,0.00991028,-0.05344488,0.0388221,-0.02170104,0.1323767,-0.00635833,0.03490419,0.0350479,0.06438283,0.03100712,-0.04484254,-0.04178119,0.02880677,0.02646861,0.00538651,-0.01606336,-0.02311549,0.00906495,-0.03956776,0.00495445,-0.02317812,-0.10959997,-0.01986764,0.0047498,-0.02338174,0.06206512,0.01196557,0.0410246,0.0582614,-0.0276638,0.0492279,-0.01211533,0.06040486,-0.04871821,-0.0804438,0.01801995,0.01358217,0.03841623,-0.00315874,-0.02196087,0.04573502,0.00349774,0.06414893,0.01428995,0.0255276,0.01665059,-0.01367906,-0.00286769,-0.00194937,0.06941431,-0.02664836,0.02922416,0.01625202,0.03368314,0.06550124,-0.00637652,-0.02260148,-0.03529449,0.0253691,-0.05279359,0.05098329,0.08223283,0.00944283,0.00963529,0.07106882,0.01694377,0.00727793,0.01758088,-0.00944976,0.01041049,-0.03153751,0.06866678,-0.01014492,-0.00304113,-0.24362467,0.0215533,-0.04335281,0.03354659,0.00511215,-0.00872241,0.00199863,-0.00165955,0.03755851,-0.02166646,0.04669362,0.0357683,0.03022312,-0.05154375,-0.0173979,-0.01466429,0.04841152,-0.0371516,0.10053528,-0.00545095,0.09237641,0.03221182,0.23642133,-0.02086422,0.00549642,0.02335291,0.03906802,0.02902803,-0.05234662,0.02989806,0.01497354,-0.01977795,0.01596807,-0.01124363,-0.04050793,0.01186275,0.05927775,0.07063936,0.00140148,0.01219236,-0.07359049,-0.01207408,-0.02885804,-0.026743,0.1414167,-0.00003652,-0.03249551,-0.07551565,0.00979557,0.05790116,-0.11507863,-0.06374655,-0.05200408,-0.0174151,0.02331298,0.0273245,0.04956431,-0.00064298,0.01465367,0.03329722,0.07365453,0.01059374,0.05837573,0.07574002,-0.0013156],"last_embed":{"hash":"1wc4vw1","tokens":264}}},"text":null,"length":0,"last_read":{"hash":"1wc4vw1","at":1753423630171},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies","lines":[171,178],"size":954,"outlinks":[{"title":"_**Software to Combine Lottery Strategies, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07244657,-0.03737157,-0.01342989,-0.04378312,-0.06579308,0.03527023,-0.02304896,0.00348907,0.02972442,-0.00683679,-0.0042856,0.02854997,0.07006697,0.02430703,0.02186853,-0.03088919,0.010922,-0.01094902,-0.08262671,-0.01151403,0.06803831,-0.05150683,-0.05653479,-0.01327336,0.06889895,0.0123882,-0.03908379,-0.08831087,-0.03986116,-0.24702175,0.04827496,0.02063511,-0.01386974,-0.04013152,-0.05845227,-0.02953177,-0.03093792,0.10219693,-0.05501046,0.0329164,-0.01207675,0.00472815,0.0049463,-0.00314584,-0.04302169,-0.0544014,-0.00916144,0.02402878,0.03664944,-0.01721515,-0.05872932,0.01680588,-0.02696649,0.02357532,0.01272307,0.0219447,0.03307243,0.11931314,-0.00624897,0.04096333,0.03563332,0.05207403,-0.18640456,0.06076208,-0.03890247,0.04993007,-0.01670974,0.02662904,-0.00325052,0.07505801,0.02026175,0.01119952,-0.0463547,0.06736379,0.04779959,-0.03752835,-0.05185239,-0.04557902,-0.04557797,0.06776766,-0.07139646,-0.02499485,-0.02518852,0.00784006,-0.00077376,0.01961214,0.01572753,0.04012765,0.06207049,-0.02674452,0.07218818,0.06538495,-0.00421777,0.0101992,0.04322687,0.02300957,0.04731961,-0.05209489,0.02050409,0.13388339,-0.02070834,-0.03810307,0.03033511,-0.00313278,0.0170591,-0.00805758,-0.01720264,-0.04574063,-0.04300303,0.00722344,0.00336754,0.00099417,0.04106345,-0.05407358,-0.02101531,0.01702226,-0.03345028,0.00203081,0.01638177,-0.01299434,-0.05915002,-0.00360607,-0.00619491,-0.01054941,0.02101094,-0.02846179,0.02811421,0.05269429,0.03111205,0.00386027,0.04744429,0.04373287,-0.13908745,-0.04114725,-0.0298205,-0.02622027,-0.01331067,-0.0363826,-0.00538876,0.01724463,-0.00979578,-0.01802084,0.09502096,-0.13601197,0.00552401,0.13915788,0.00169408,0.01733798,-0.00969951,0.00234565,-0.02831169,-0.0109963,-0.06949885,-0.06220007,0.01332104,0.00215911,0.06529088,0.06074433,-0.05663148,-0.03218911,-0.05739532,0.00760943,-0.02588226,0.11911312,-0.01285479,-0.13066052,-0.03449539,0.03189289,-0.0415373,-0.09382467,-0.02670655,-0.02744318,-0.03706038,0.00524072,0.07281182,-0.01840737,-0.09658623,-0.051113,-0.00298765,0.01376607,0.06008202,-0.02573058,-0.0361887,0.00325772,-0.01487671,-0.05297172,0.03653714,-0.02036615,0.02793743,0.04304411,-0.04378905,0.03640668,-0.04742568,0.00851078,-0.02690336,-0.01313154,-0.03073214,-0.04507003,0.06955466,-0.03135187,-0.01702958,-0.01791327,-0.00907252,0.02308711,-0.0182805,0.0151165,-0.01063558,-0.04329757,0.09150905,0.00673066,-0.05001493,0.02823175,0.0432663,0.08605788,-0.0836186,0.01917926,0.00189606,0.03835459,-0.01868506,0.00563822,0.01719961,0.03225219,-0.05906795,-0.17579837,-0.0470138,-0.02746389,0.02184587,0.01872437,-0.01925717,0.01597247,-0.01975494,-0.02676402,0.07653803,0.10877319,-0.03490864,0.01469973,0.06763883,-0.02342361,-0.0262341,-0.09227703,-0.02820089,-0.03157581,0.03895366,0.03229544,-0.00211456,0.00411314,-0.05709242,0.04030933,-0.01797062,0.12639077,-0.00963743,0.03257543,0.03010049,0.06142578,0.03039501,-0.04101409,-0.03199643,0.03293937,0.0304409,0.00893036,-0.01952754,-0.02064728,0.00789112,-0.04190757,0.00892624,-0.03041261,-0.10856284,-0.02551979,0.00211833,-0.01983265,0.06416189,0.01298622,0.05136661,0.05519333,-0.0277248,0.04833556,-0.01316641,0.06170116,-0.04842367,-0.07714126,0.02732493,0.01840881,0.04630033,0.0042169,-0.02645283,0.04206374,0.00651237,0.06225654,0.01059205,0.0272918,0.01861116,-0.01530701,-0.00756928,0.00192372,0.06925999,-0.02720809,0.02180259,0.02323654,0.03632958,0.06872837,-0.00386475,-0.01603222,-0.04042764,0.02507302,-0.0461469,0.05097403,0.08544198,0.00029254,0.00693664,0.06753954,0.02079284,0.0114551,0.01626742,-0.01380287,0.01099738,-0.03188803,0.06385802,-0.0098376,-0.00608055,-0.23708498,0.02508978,-0.04262471,0.03436878,-0.00225703,-0.0090496,0.00500108,0.01015981,0.03408477,-0.01617816,0.04419485,0.03489399,0.02995259,-0.05517637,-0.01724464,-0.0204749,0.05611208,-0.03705783,0.10152681,-0.01037664,0.09654781,0.03315627,0.23311056,-0.02518097,0.00765408,0.02401717,0.04386413,0.04051259,-0.0553612,0.02697912,0.01833976,-0.02013868,0.01858388,-0.01564883,-0.04166673,0.00488017,0.06177234,0.06856746,0.00216006,0.01204861,-0.07380596,-0.00998285,-0.02953842,-0.02778655,0.14040233,-0.00233624,-0.03323191,-0.07709181,0.00828942,0.05642076,-0.11724623,-0.05976199,-0.05284801,-0.02266263,0.02242225,0.0317205,0.05073203,-0.0015424,0.01470713,0.03577691,0.06884435,0.0106043,0.057067,0.07919632,-0.00894651],"last_embed":{"hash":"aq5mmt","tokens":226}}},"text":null,"length":0,"last_read":{"hash":"aq5mmt","at":1753423630248},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>II. Types of Lottery Strategies</u>#11\\. Expandability and Interoperability: Combining Lottery Strategies#{1}","lines":[173,176],"size":768,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0846254,-0.03003957,-0.04038827,-0.02844702,-0.03564653,0.08639835,0.02641043,0.01448921,0.05896793,-0.01019774,0.01024446,0.01170512,0.06727359,-0.00846213,-0.02097359,0.00033772,-0.01289619,-0.01982435,-0.02652864,-0.00449843,0.06453686,-0.08876818,-0.0337359,-0.03392038,0.07697333,-0.01642835,-0.02761162,-0.07024972,-0.0770562,-0.25510991,0.0264134,0.02218455,0.00809443,-0.04626023,-0.0974182,-0.01796146,-0.04752484,0.10053466,-0.03149165,0.04307024,0.00097261,0.03417287,0.02754061,0.00675374,-0.01701287,-0.01958441,-0.05847853,0.00276594,0.03330154,-0.01175231,-0.07589308,0.02431494,0.01042264,0.0427248,0.07837237,0.02507093,0.07459965,0.0712152,0.03087091,0.03727752,0.03179758,0.02536419,-0.19275217,0.06014534,-0.01571977,0.01813033,0.00210869,-0.01265129,-0.00973136,0.05646985,0.01679999,0.0427724,-0.02247192,0.04093608,0.05512543,-0.01762472,-0.04248606,-0.05148171,-0.04124927,0.02877152,-0.05579421,-0.01580893,-0.04320209,-0.05957979,0.03071549,0.01903036,0.00704052,0.03393211,0.05132349,-0.03309137,0.0733593,0.07829098,-0.02004915,-0.00878408,0.04478994,0.0364961,0.04613059,-0.03693447,0.00320961,0.10872169,0.0156353,0.02114797,-0.0345308,0.02551587,0.03499693,-0.03698988,-0.0330647,-0.06953163,-0.05935046,0.04061899,0.01856102,0.0042115,0.10550212,-0.05039908,0.00612371,0.01712366,0.00929133,0.03063876,0.02074278,-0.00175843,-0.00473256,0.03826539,0.05188767,0.00016658,0.02197071,0.01238113,0.02201994,0.09556016,0.052246,-0.01528782,0.03797583,-0.00337719,-0.12997225,-0.04310928,-0.01761506,-0.03645304,-0.00278181,0.02029732,0.01180835,0.02482756,-0.00364907,-0.04791003,0.07497495,-0.13551028,-0.02047459,0.0976264,-0.00037765,0.01981764,-0.0144848,0.01635603,0.0214464,0.00023755,-0.06038626,-0.06342993,-0.02099088,0.0242901,0.03974051,0.0683507,-0.01782881,-0.00390536,-0.07400879,-0.01786827,-0.00482676,0.11495365,-0.0347507,-0.10585269,-0.04242289,-0.00838165,-0.05610625,-0.07861207,-0.02671483,-0.02146734,-0.05017544,0.0233173,0.05672629,-0.00991401,-0.07089376,-0.06093492,0.02028147,0.00419629,0.04798633,-0.00411329,-0.03714752,-0.02510913,-0.00003074,-0.03604471,0.02636274,-0.0286881,0.03958841,-0.01605723,-0.06194164,0.00893613,-0.04678667,0.02733419,-0.0271119,-0.02680535,-0.00879655,-0.01866765,0.06078078,-0.0305406,-0.00946495,-0.02654341,0.0085493,0.00713676,-0.00597615,0.06360896,0.02054515,-0.05452066,0.08523677,0.01549009,-0.02536624,-0.01127861,0.05056071,0.04697467,-0.07981755,-0.01688556,-0.01342153,0.03142588,-0.02253019,0.00943081,0.01246715,0.03990178,-0.06831617,-0.18492687,-0.03775781,-0.0131888,0.03909228,0.04937179,-0.01082608,0.03067836,-0.04887163,0.0690439,0.06533103,0.06180533,-0.01849406,-0.0369934,0.08326223,-0.02295232,0.00797185,-0.09071163,-0.02283062,-0.05750142,0.03195682,0.01055941,0.01616527,-0.06677934,-0.06153474,0.08400553,-0.02964845,0.13260335,0.01658925,-0.01944128,0.00484098,0.0652101,0.01133242,-0.00555637,0.03004414,0.01474242,-0.00020944,0.02780085,-0.01993004,-0.05249225,0.00450753,-0.03351359,0.01603659,-0.02400138,-0.10704025,-0.05532059,0.00078003,-0.04275037,0.07078192,0.01716395,0.04205297,0.06959955,-0.03264438,0.06927017,-0.00354181,0.03921516,-0.0403621,-0.06347717,-0.00089473,0.00163278,0.06055837,-0.01458647,-0.02916094,0.03865054,0.00434638,0.01719827,0.01710789,0.01383205,0.01097871,0.02963843,-0.02659513,0.00894044,0.06357088,0.0278003,0.02341131,-0.0034449,0.02061444,0.02700359,-0.04451066,0.03454071,-0.04895789,0.04942755,-0.01653252,0.01890267,0.03349848,0.0277894,-0.02201446,0.04764947,0.05229871,0.00708675,0.0020499,-0.00653881,0.0378963,-0.02432698,0.02075436,0.00129681,-0.01718126,-0.2589052,0.01859923,-0.08613294,0.05040836,-0.00041561,-0.01348801,-0.01037204,0.02157351,0.03069267,-0.0133325,0.0393307,0.06332382,0.03389013,-0.05131504,-0.00812701,-0.03901975,-0.01823696,-0.04788073,0.05624655,-0.02357033,0.08868205,0.01829271,0.22778343,-0.02911321,0.01426251,0.01801094,0.01732485,0.02171216,-0.06172273,-0.00030724,-0.02500992,0.00416113,0.05169966,-0.02258264,-0.02878674,0.00471707,-0.00122551,0.02698698,-0.03232062,0.00488515,-0.06783882,-0.04361672,0.00048878,0.00499078,0.15991291,-0.03166562,-0.02661959,-0.09169376,0.04838561,0.09508687,-0.09158832,-0.06184603,-0.0558026,-0.0344435,-0.00042256,0.08606942,0.0097675,-0.00646214,0.02585036,0.01393211,0.01073903,0.0441932,0.08081431,0.04825385,0.00467101],"last_embed":{"hash":"l5x48","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"l5x48","at":1753423630325},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>","lines":[179,194],"size":2656,"outlinks":[{"title":"_**Lottery Strategy, Systems Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08541336,-0.03161202,-0.03998899,-0.02594558,-0.02699188,0.08512294,0.02752377,0.01068966,0.05997115,-0.01339548,0.0104335,0.01085462,0.06752364,-0.01560718,-0.0154219,0.00177235,-0.01035802,-0.02258821,-0.02565253,-0.00581732,0.06606915,-0.09188622,-0.03204221,-0.03806894,0.07912947,-0.01805027,-0.03265256,-0.06783454,-0.08101802,-0.25608742,0.02528229,0.02420927,0.01124099,-0.04906471,-0.09968115,-0.02041896,-0.04797946,0.09753317,-0.03382534,0.03910523,0.00131708,0.03642514,0.02925375,0.00607882,-0.01724666,-0.01845118,-0.06173947,0.00432805,0.03250415,-0.01051671,-0.07421719,0.02120105,0.0049436,0.04455887,0.07893728,0.02885057,0.07726657,0.07255714,0.02491435,0.03744252,0.03272364,0.02571367,-0.19196288,0.05945212,-0.01340376,0.02047946,0.00385618,-0.01549392,-0.00703682,0.05756542,0.01552048,0.0402562,-0.02249899,0.04093288,0.05953801,-0.01642894,-0.0414585,-0.05621547,-0.03981984,0.03241599,-0.0528353,-0.01777655,-0.04724901,-0.05766453,0.03404362,0.01497783,0.00833915,0.03512259,0.04558221,-0.03838968,0.06437512,0.0730897,-0.02226206,-0.01590664,0.04320525,0.03834665,0.04426305,-0.03959618,0.00465105,0.10558677,0.01658479,0.02173703,-0.03447452,0.0265432,0.03747682,-0.0317512,-0.03165795,-0.06981579,-0.06167147,0.03883557,0.02025817,0.00708263,0.10448481,-0.04651101,0.00596106,0.01662148,0.01178957,0.02515562,0.02149128,-0.00195659,-0.00748804,0.04156175,0.05780273,0.00033485,0.01985293,0.01104617,0.01996456,0.09503569,0.0513742,-0.01554994,0.03799026,-0.0049673,-0.13522001,-0.04417299,-0.02090181,-0.03954842,-0.00513991,0.01904174,0.01184749,0.02493604,0.00055181,-0.04713802,0.07573073,-0.1393957,-0.01790205,0.09681898,-0.00280174,0.0191139,-0.01844469,0.02414834,0.02343791,0.00028668,-0.05917566,-0.06333136,-0.02142685,0.02141179,0.04635649,0.06925656,-0.01564619,0.00016039,-0.0742888,-0.01680554,-0.00305495,0.11323397,-0.03706715,-0.10412814,-0.03677345,-0.00843615,-0.05494354,-0.0760537,-0.02559579,-0.01821589,-0.05102957,0.02238092,0.05622089,-0.00859783,-0.06946751,-0.06000086,0.02215336,0.00549481,0.04388804,-0.00736391,-0.03607577,-0.02681587,0.00585414,-0.03802489,0.02448794,-0.03221634,0.03745059,-0.01501401,-0.07051989,0.01660766,-0.04814614,0.03014151,-0.02992887,-0.02294783,-0.0091983,-0.0168425,0.06024067,-0.03050539,-0.01421989,-0.0249097,0.00644079,0.00831826,0.00205345,0.06699772,0.02207466,-0.05383044,0.08378957,0.01596651,-0.02431981,-0.01271679,0.05086714,0.04647277,-0.0777139,-0.01889839,-0.01084804,0.02987279,-0.02555676,0.00604031,0.01373327,0.0361131,-0.06573261,-0.18703677,-0.0325969,-0.01456594,0.03719051,0.05119133,-0.01207588,0.03394901,-0.05128708,0.07135969,0.06026121,0.0596699,-0.01785919,-0.03654201,0.08073041,-0.02252962,0.00795552,-0.09248519,-0.02477639,-0.05746133,0.03983182,0.01079862,0.01448339,-0.06585045,-0.05991603,0.08008017,-0.03056939,0.13544552,0.01576671,-0.01500879,0.0104525,0.06532359,0.00973835,-0.00673947,0.03017388,0.01560868,-0.00165106,0.02977666,-0.02098585,-0.05387949,0.00335789,-0.03626154,0.01859389,-0.02627989,-0.10498156,-0.05402854,-0.00225709,-0.04169024,0.07186057,0.01668252,0.03996579,0.06989326,-0.03041743,0.0637215,-0.00315801,0.03622272,-0.03864446,-0.06573679,0.00422435,-0.00295109,0.05800127,-0.01499834,-0.03073766,0.03987738,0.00519799,0.01716805,0.01320051,0.01205057,0.0169722,0.02856746,-0.02914132,0.00658495,0.06253,0.02628853,0.02387084,0.00009722,0.02175811,0.02638604,-0.0514997,0.03494711,-0.04571186,0.04689104,-0.01412607,0.01945281,0.02969223,0.03278077,-0.02112315,0.04129503,0.05418466,0.00654565,0.0027497,-0.01202672,0.03716408,-0.02420758,0.02094694,0.00140558,-0.01626847,-0.2568585,0.01807149,-0.08534164,0.04910237,-0.0027198,-0.01477171,-0.0103419,0.01878744,0.02852567,-0.01099883,0.04095095,0.06480806,0.03356887,-0.05126693,-0.00558141,-0.03925286,-0.01976918,-0.04630665,0.04991129,-0.01938198,0.08722609,0.01806057,0.22957759,-0.02307376,0.01679908,0.02041464,0.01861571,0.02354645,-0.0619767,0.00412715,-0.0230928,0.00627964,0.04929355,-0.01951519,-0.02538112,0.01162371,0.00339831,0.02550447,-0.03586745,0.00702422,-0.06835986,-0.04384426,0.00494875,0.00786385,0.16023204,-0.0318133,-0.02622946,-0.08784459,0.04846154,0.09147663,-0.09251437,-0.06304875,-0.05543068,-0.0353205,0.00222788,0.08885659,0.01237931,-0.01073234,0.02363699,0.01067397,0.01217296,0.04949516,0.07924936,0.04668063,0.00946107],"last_embed":{"hash":"1fa3mrz","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"1fa3mrz","at":1753423630519},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>III. A Real-Life Application of Lottery Strategies</u>#{1}","lines":[181,192],"size":2474,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08670903,-0.02922524,-0.03891993,-0.00354819,-0.05921971,0.0517976,0.00376919,0.00038868,0.03716602,-0.01236645,0.01028494,-0.00354274,0.03800097,0.00777597,-0.03727945,-0.02350453,0.00471173,0.01568693,-0.03712338,-0.0125708,0.12073015,-0.06167141,-0.05585655,-0.08302435,0.069842,0.00904617,-0.02960938,-0.06835217,-0.02507179,-0.19878502,-0.01347948,-0.01588495,-0.00880891,-0.05947132,-0.0915842,-0.01105037,-0.03419483,0.06274171,-0.06456894,0.03378227,-0.00834753,0.02649632,-0.02153748,0.00125941,0.03072245,-0.01488157,0.05171359,0.02337708,0.0358631,-0.00245193,-0.06337947,0.05964375,-0.00897414,0.00787134,0.08117905,0.03559436,0.05430717,0.11450522,0.00388149,0.01693933,0.0323294,0.07317496,-0.2042753,0.07490752,0.00085427,0.04832309,0.01504939,0.00774181,-0.03179013,0.06038442,0.04819128,0.00386224,-0.00390202,0.06067324,0.04841933,-0.01471109,-0.0106971,-0.05863371,-0.02425463,0.05151127,-0.03298943,-0.00973701,-0.0039208,-0.00204161,0.00247432,0.04754795,0.02598741,0.03719899,0.09476085,-0.09567705,0.0213165,0.06976874,0.03701123,0.02196259,0.04396832,-0.00004636,0.04059199,-0.03932181,0.00681115,0.11321045,-0.01200392,0.00306649,-0.02359616,-0.01091192,0.06996576,-0.05607862,0.00201672,-0.03879125,-0.00847768,0.03380772,0.02293511,0.00274609,0.08947673,-0.01396364,-0.02941078,0.00172451,0.02408356,-0.01013042,0.01723043,0.00767295,-0.0531888,0.06617963,0.03439282,-0.02583065,-0.01765385,-0.02734824,0.0120822,0.05480074,-0.00035185,0.01825583,0.04264436,0.01463444,-0.13030516,-0.03485467,-0.0356554,-0.02075017,0.01447473,-0.01081737,-0.01494306,0.02029441,-0.0136347,-0.04560487,0.06579918,-0.12456082,-0.03599594,0.05625966,0.01319455,0.01256922,-0.00892497,0.03132477,0.00496544,-0.03916659,-0.01608838,-0.07703918,-0.00849695,-0.00274809,0.08949045,0.0724509,-0.05851088,-0.01845923,-0.06493988,-0.01510561,-0.01920277,0.12761594,-0.01582529,-0.08667603,-0.01966265,0.02844484,-0.02573734,-0.10586848,-0.01970163,-0.02486724,-0.0749563,0.02011953,0.08145575,-0.03428681,-0.06754248,-0.06755072,-0.03926732,0.01520661,0.03608519,-0.03443252,-0.01827445,-0.02381655,-0.00538715,-0.04341564,0.01457514,-0.03605909,0.07859933,0.04091261,-0.0184943,0.04378049,-0.02429505,-0.0040164,-0.04288149,-0.00926644,-0.04758775,-0.04921223,0.06940608,-0.01273084,-0.03454619,-0.00833066,0.00056598,0.00660143,-0.02370127,0.03340348,-0.02030279,-0.07168228,0.07684766,0.0422629,-0.04351868,-0.01585901,0.03205412,0.06910393,-0.04303643,0.02585697,0.04161621,0.05039241,-0.02611918,0.03455916,0.008056,0.01322239,-0.08658661,-0.17677327,-0.05771317,-0.05415508,0.00152237,0.05349547,0.00161357,0.02396164,-0.05071034,0.01304876,0.04797861,0.08180745,-0.06433375,0.01457495,0.07325344,-0.01179565,-0.00995622,-0.10603978,-0.04471053,-0.06281732,0.02000881,0.0052769,0.00502383,-0.03028777,-0.08081127,0.0124977,-0.02545582,0.14924721,0.0030988,0.01666725,0.02600445,0.04874092,0.01551312,-0.02862316,-0.02529124,0.01336633,-0.00016466,-0.01163222,-0.02653509,-0.04596413,0.00056989,-0.04483689,0.00553803,0.00811946,-0.07880388,-0.07379299,-0.02180455,-0.00092041,0.0563805,0.00010366,0.06532687,0.04230564,0.0373315,0.06271254,-0.00177836,0.06383844,-0.00723283,-0.05826634,-0.00224804,-0.01700551,0.04848843,-0.02651848,-0.03453366,0.03056226,-0.00106943,0.07899243,-0.01221645,0.01176737,-0.00263577,0.0090329,-0.00462306,-0.00462904,0.07888388,-0.00443681,0.01413152,0.02979447,0.02758054,0.10539088,-0.05784341,-0.0008438,-0.0014726,-0.00976728,-0.06246269,0.06288195,0.06313035,0.06519032,0.02331831,0.04738865,0.03776601,-0.04216551,-0.01301913,0.01167372,0.01962233,-0.07416957,-0.0001116,-0.00440433,0.03611093,-0.2571803,0.02545069,-0.0519102,0.02886067,-0.04437993,-0.03156151,0.01747629,0.00359315,0.00184056,-0.03006573,0.05143989,0.03613242,0.01529107,-0.03906243,-0.02664766,-0.02102216,0.01092092,-0.04364082,0.09656309,0.05578953,0.05678033,0.04463809,0.23664896,-0.02279096,0.01767134,0.01640174,0.00018905,0.02717266,-0.03558906,0.03167591,0.01770976,0.03135512,0.02084376,0.00184647,-0.05114794,0.01030669,-0.00328434,0.04695509,-0.01959432,0.00306527,-0.05809881,0.00346044,0.003766,0.04605951,0.13834211,0.01150311,-0.01446501,-0.09095678,0.05150931,0.04404183,-0.12507075,-0.06274696,-0.06310362,-0.03261945,-0.00364153,0.05110287,0.0221941,-0.03517485,0.00656063,-0.01116653,0.02686845,0.03458916,0.06376418,0.03176087,0.00675827],"last_embed":{"hash":"1g3gj6v","tokens":388}}},"text":null,"length":0,"last_read":{"hash":"1g3gj6v","at":1753423630667},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>","lines":[195,217],"size":2864,"outlinks":[{"title":"_**Lottery, Lotto: Software, Strategies, Systems, Wheels**_","target":"https://saliu.com/content/lottery.html","line":3},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage**_","target":"https://saliu.com/lottery-numbers-loss.html","line":5},{"title":"_**Playing Lottery Strategies, Lotto Strategy Means Start**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":7},{"title":"_**The Best Strategy in Lottery, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":9},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":11},{"title":"_**Judge Best Lotto Software, Lottery Strategy, Theory**_","target":"https://saliu.com/bbs/messages/623.html","line":13},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":15},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":16},{"title":"Lottery software programs ready to create the best lotto strategies to win big money.","target":"https://software.saliu.com/HLINE.gif","line":18},{"title":"**Roulette**","target":"https://download.saliu.com/roulette-systems.html","line":20},{"title":"**Sic Bo**","target":"https://forums.saliu.com/strategies-casino-war-sic-bo-gambling-formula.html","line":20},{"title":"**Artificial Intelligence**","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":20},{"title":"**Craps**","target":"https://saliu.com/bbs/messages/504.html","line":20},{"title":"**Sports**","target":"https://saliu.com/betting.html","line":20},{"title":"**Blackjack**","target":"https://saliu.com/blackjack-strategy-system-win.html","line":20},{"title":"**Baccarat**","target":"https://saliu.com/winning_bell.html","line":20},{"title":"**_Software_ Home**","target":"https://software.saliu.com/index.html","line":20},{"title":"**Live Sports**","target":"https://software.saliu.com/live-sports-streams.html","line":20},{"title":"This is the site for lotto, lottery software strategies, winning systems for all games in the world.","target":"https://software.saliu.com/HLINE.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0953434,-0.04912411,-0.00825474,-0.02092763,-0.03459762,0.03877123,-0.01239348,0.00738803,0.02266204,-0.0130545,0.00287527,-0.01550825,0.0426325,0.00958435,-0.01049957,-0.02636899,0.02471098,0.00758278,-0.07024392,-0.02091663,0.12391398,-0.05023558,-0.05092644,-0.06299888,0.05679523,0.00649547,-0.04715224,-0.05501092,-0.01145271,-0.17775206,-0.003966,-0.01175145,-0.01851269,-0.05256015,-0.09458239,-0.02221348,-0.0198269,0.05937424,-0.05082236,0.04004917,-0.01011802,0.04075073,-0.03677333,0.00863201,0.02390531,-0.05534859,0.05186513,0.02612838,0.05905171,-0.00530574,-0.03763665,0.0486237,-0.00903715,0.00179582,0.07792269,0.03551574,0.05696601,0.08063246,0.01375054,0.03234268,0.0354984,0.09468964,-0.20849773,0.08091962,0.01729436,0.04101192,0.02674877,-0.01006927,-0.04061964,0.06580082,0.04970556,-0.00145706,0.00325705,0.06665102,0.04415536,-0.01064216,-0.02811683,-0.07569601,-0.05014049,0.04514912,-0.04800399,-0.0143734,0.00779332,-0.0138717,0.02557619,0.05723299,0.01992918,0.03267718,0.10306612,-0.05511747,0.02864672,0.06200409,0.04842457,0.03591498,0.02123652,0.00256452,0.01411113,-0.05863026,0.02182416,0.13996442,-0.01338013,0.00448223,-0.00008684,-0.02866709,0.03886048,-0.02535228,-0.00143657,-0.03472807,-0.00794242,0.04787342,0.02037984,-0.00348527,0.05420998,-0.00688879,-0.02809125,-0.0127397,0.01375938,-0.02134033,0.00511285,0.01761485,-0.07209878,0.03352648,0.03795194,-0.00430477,-0.00305128,-0.05096999,0.02294697,0.03319755,-0.00621223,0.01880318,0.0382906,0.01442802,-0.14303719,-0.04653284,-0.03008164,-0.0198859,0.03428036,-0.02276504,-0.0204531,0.03313558,-0.00881009,-0.0729449,0.07217206,-0.14145184,-0.03212954,0.07765532,-0.00057158,0.00940447,-0.01898807,0.02594461,-0.01133328,-0.0166754,0.0052539,-0.04005762,0.00166874,0.02636836,0.07796005,0.06607261,-0.04957773,-0.00759302,-0.07077647,-0.00696201,-0.03349408,0.14668955,-0.04540956,-0.12650146,-0.02794859,0.04380589,-0.02927855,-0.10062432,-0.01881859,-0.01622238,-0.09096813,0.03826676,0.10844163,-0.04437844,-0.03042774,-0.0453219,-0.04991433,0.00394962,0.02628398,-0.03919482,-0.03541899,-0.00009318,-0.00678098,-0.05638039,0.02107118,-0.02028349,0.07640728,0.06407828,-0.02580872,0.03686145,-0.0008767,-0.01358613,-0.05482989,0.00876148,-0.05454503,-0.03958394,0.08916761,-0.013074,-0.00397418,0.022847,-0.00227827,0.00789633,-0.0017982,0.02916824,-0.03959268,-0.06194746,0.05682012,0.0226965,-0.05579808,0.01121122,0.06086752,0.07239822,-0.08762798,0.00047353,0.04005684,0.04926252,-0.04614605,0.01064666,0.02568247,0.01269317,-0.10659728,-0.20294587,-0.04265345,-0.04106815,0.00892197,0.03109766,-0.00290973,0.03402541,-0.03846259,0.01159944,0.07534438,0.06973545,-0.07363785,0.02753783,0.05945932,-0.01874556,-0.01659963,-0.08355238,-0.0708307,-0.0561407,0.01268623,0.02002097,0.00225799,-0.03588916,-0.09128568,0.01835269,-0.03449967,0.14856036,0.0133542,0.00112446,0.01854432,0.06540016,-0.0050276,-0.02438181,-0.02237787,0.0159663,0.01035916,-0.02355997,-0.02312061,-0.02396522,-0.01158795,-0.02652258,-0.0060812,-0.01106434,-0.07401655,-0.03132903,-0.01916963,0.00425283,0.02446697,0.01806973,0.03413355,0.02355322,0.01135642,0.05267351,0.01310096,0.05361805,0.00671601,-0.06369855,-0.00427104,-0.01623885,0.05667898,-0.01832316,-0.04194738,0.01804752,-0.01329223,0.06468318,-0.0223216,0.00901695,-0.0151905,0.00792817,-0.01833281,-0.00638547,0.07143441,0.02237769,0.03817274,0.02595042,0.02504447,0.09580512,-0.01864842,0.01292445,-0.00626614,-0.02235289,-0.04962578,0.05795365,0.05230051,0.02742005,0.04368732,0.03404687,0.01163648,-0.03997428,-0.01004499,0.00527258,0.00858475,-0.05092802,0.01154662,0.02271099,0.04408174,-0.24307537,0.03922104,-0.03941986,0.03512877,-0.03706887,-0.02511225,0.00363761,-0.0064714,0.03521188,-0.01857671,0.08249874,0.04111235,0.012649,-0.0355712,-0.02387382,-0.01765074,0.04679147,-0.03432148,0.09593622,0.05020782,0.03122211,0.03399329,0.23267932,0.01749611,0.0205214,0.04082755,-0.02039988,0.02547566,-0.06693026,0.00116431,0.03319349,0.03538784,-0.00569294,-0.00132268,-0.04636521,0.02114476,0.00159538,0.03780429,-0.00852593,0.02903006,-0.07909011,0.01706374,-0.01781369,0.03089568,0.12258669,0.00559849,-0.01277528,-0.09979076,0.03626181,0.03258456,-0.10782567,-0.06658772,-0.06859434,-0.01858773,0.01077127,0.05036236,0.03773415,-0.03984855,-0.00046778,-0.00588241,0.04206191,0.02950523,0.04511359,0.03541762,0.019648],"last_embed":{"hash":"1tyc9di","tokens":120}}},"text":null,"length":0,"last_read":{"hash":"1tyc9di","at":1753423630809},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{1}","lines":[197,198],"size":260,"outlinks":[{"title":"_**Lottery, Lotto: Software, Strategies, Systems, Wheels**_","target":"https://saliu.com/content/lottery.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08848253,-0.05676704,-0.0260592,-0.00393711,-0.05263821,0.05199435,0.0087876,0.00532749,0.03075777,0.00092213,0.00663071,0.002357,0.05066026,0.0085356,-0.01535944,-0.01656963,0.00078637,0.03096457,-0.05277937,-0.00248331,0.10880197,-0.05204403,-0.04826628,-0.08312683,0.04818704,-0.02014413,-0.03247308,-0.08364267,-0.04495112,-0.17825961,0.00949682,-0.02682161,-0.03146292,-0.06203292,-0.09306835,-0.03829328,-0.04316704,0.07657914,-0.04869417,0.04438401,-0.00217329,0.0349814,-0.0297346,-0.00405488,0.01032517,-0.02947385,0.04008999,0.02361832,0.06262622,0.00927399,-0.05314579,0.07688542,-0.00968809,0.03301564,0.06797014,0.0294527,0.05793293,0.11893584,-0.00455468,0.02083593,0.03491822,0.06851438,-0.19388548,0.0602858,-0.00629929,0.04187745,0.02290024,0.01460838,-0.02518907,0.07325175,0.04727437,-0.01711091,0.00716685,0.06617469,0.04554854,-0.01901481,-0.0227435,-0.0675802,-0.03301661,0.05083902,-0.0437818,-0.04041026,-0.00287883,-0.00189495,0.01695882,0.01652603,0.0509831,0.04521738,0.08658376,-0.08019395,0.03842598,0.08890601,0.01901358,0.02141548,0.04053027,0.01016365,0.03693236,-0.04700149,0.00612958,0.14006041,-0.01663266,0.0003603,-0.03189313,-0.00063876,0.06311342,-0.05043368,-0.02240656,-0.02493735,-0.01570994,0.03999619,0.0366693,0.01653656,0.08550037,-0.03848335,-0.0295949,-0.00141933,0.01991808,0.01073525,0.01434171,0.00920619,-0.04941013,0.04474555,0.04540818,-0.03040004,0.00878781,-0.03333461,0.02837405,0.04263505,-0.00154092,0.04330917,0.04801689,-0.01340006,-0.14283313,-0.03636683,-0.02498509,-0.01300327,0.01196006,-0.01631029,-0.02617831,0.01738901,-0.00640107,-0.03553167,0.05759561,-0.1353761,-0.00066153,0.05963877,0.02439192,0.03058835,0.02308868,0.00801752,-0.00279879,-0.04063931,-0.02068724,-0.07601158,-0.01857043,0.01028237,0.07812221,0.05476244,-0.03859764,-0.0117611,-0.03933236,-0.01828301,-0.00777943,0.11429249,-0.04130466,-0.08274217,-0.053035,0.01296164,-0.02493711,-0.11112674,-0.030612,-0.02834458,-0.05717083,0.00649573,0.0873016,-0.02527706,-0.08002279,-0.06761166,-0.03451249,0.0076376,0.02197346,-0.01104352,-0.01544542,-0.01957918,-0.01758241,-0.03411984,0.02079685,-0.01275536,0.07147568,0.05371204,-0.02770195,0.01618443,-0.04164481,0.00390978,-0.02412514,0.00640513,-0.04660324,-0.03556955,0.0805546,-0.01571308,-0.01812017,-0.02453532,0.00382453,0.03345617,-0.01275282,0.03102228,-0.01633628,-0.09132054,0.07605176,0.04195421,-0.05699302,0.00867133,0.05458629,0.06979024,-0.04200779,0.02646706,0.04361806,0.05478716,0.00061794,0.01230835,0.01284745,0.00865203,-0.07330139,-0.19462922,-0.06392167,-0.04171057,-0.02457844,0.04342622,-0.02625602,0.00622428,-0.05602995,0.01833788,0.07403939,0.08282693,-0.07258529,0.02128019,0.07715271,-0.01405949,0.01150241,-0.09440915,-0.05203808,-0.0638239,0.04215992,0.00975254,0.00570518,-0.0357939,-0.08960769,0.03108663,-0.03170478,0.14820251,-0.029185,0.01364299,0.00972132,0.05215912,0.019186,-0.03920312,-0.00116425,0.02205241,0.00792712,-0.05646869,-0.0184162,-0.04242398,0.00146778,-0.03568612,-0.00349901,0.0101237,-0.07966268,-0.05401051,-0.00377881,-0.02582181,0.05453838,-0.00808046,0.06499415,0.02541977,0.03004371,0.08194868,0.00273967,0.05969793,-0.01957511,-0.04994136,0.00098327,-0.0316917,0.03404212,-0.02496948,-0.05239099,0.03028692,-0.02123905,0.08579606,-0.02352618,0.00859922,-0.01764817,0.00323205,-0.01195971,-0.00454145,0.04019187,-0.00492563,0.01692173,0.00993463,0.02444758,0.08275872,-0.03719922,-0.02353896,-0.0090039,0.01952702,-0.06542937,0.06061042,0.0617067,0.07342999,0.03514262,0.04075734,0.04579704,-0.0221666,-0.00242086,-0.0011443,0.01376582,-0.06508,-0.00601662,-0.0089621,0.00009579,-0.2280868,0.02558277,-0.07061107,0.04902683,-0.05661543,-0.04124201,0.01084813,0.03169207,0.01614954,-0.0301752,0.08144514,0.04807779,-0.00628415,-0.03833136,-0.01593342,-0.02081717,0.0193765,-0.02266862,0.08753253,0.07260764,0.06987431,0.04182507,0.23738851,0.00692616,0.02066476,0.01721971,-0.00145036,0.03714331,-0.03706553,0.01754137,0.02673928,0.01341572,0.02730799,-0.00780148,-0.03812299,-0.003371,-0.00121462,0.0562187,-0.02421868,0.02095527,-0.06060894,-0.00322245,-0.02499478,0.04282266,0.13755293,0.02198258,-0.02635285,-0.08046276,0.03114514,0.0416653,-0.11259603,-0.05693786,-0.05580089,-0.06640517,-0.00612112,0.05692091,0.01527405,-0.02717069,0.02349901,-0.00294105,0.03492736,0.03974924,0.04854662,0.01793769,-0.00837407],"last_embed":{"hash":"1j028ha","tokens":117}}},"text":null,"length":0,"last_read":{"hash":"1j028ha","at":1753423630857},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{2}","lines":[199,200],"size":302,"outlinks":[{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage**_","target":"https://saliu.com/lottery-numbers-loss.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10276783,-0.02577583,-0.03480232,0.00414336,-0.01675203,0.05202818,-0.01267426,0.02158458,0.01602294,-0.01247992,-0.03306829,-0.02007387,0.00416359,0.01443888,-0.01237685,-0.04023168,0.02626421,0.00737626,-0.04243098,0.03717446,0.07440916,-0.05088111,-0.05415235,-0.06966472,0.07327368,-0.0200738,-0.02908786,-0.04869264,-0.04129275,-0.20130171,0.00136488,-0.03135601,0.00024589,-0.04813726,-0.11080972,-0.0067363,-0.00741598,0.07217909,-0.02385095,0.0248663,-0.00953722,0.03877198,0.00243286,0.00465047,0.00361286,-0.04208082,0.04111326,0.03943594,0.05633857,0.01865656,-0.07857428,0.03395392,-0.00656516,-0.00378733,0.08898832,0.02854004,0.04933871,0.10929502,-0.00884155,0.02174533,0.00736777,0.0754186,-0.20801021,0.07339085,-0.02286975,0.04496166,0.01341812,-0.0005586,-0.01510569,0.05836495,0.04621786,0.00001165,0.00354512,0.04116838,0.04874327,-0.01807725,-0.01247093,-0.05673423,-0.01251902,0.05136835,-0.0417277,-0.02505201,-0.00295595,-0.04423392,0.00976772,0.05104876,-0.00598524,0.0369586,0.07850813,-0.05667014,0.03887507,0.05554642,0.04317293,0.01890059,0.02275263,-0.01191028,0.02235791,-0.05292419,0.00667146,0.11367095,0.01622945,-0.00456639,-0.02141649,-0.00602024,0.06698073,-0.03691196,-0.03034583,-0.02394943,-0.04320163,0.04688389,0.01933041,-0.00417943,0.08421697,-0.01893604,-0.05080047,0.00605685,0.01166262,-0.0287466,-0.00102569,0.01326508,-0.05517018,0.02896716,0.02090059,0.00566547,-0.02839788,-0.05215675,0.02696416,0.0516069,-0.01698237,0.04127051,0.03644759,0.02751305,-0.14082494,-0.05143511,-0.03683897,-0.0477805,0.01506872,0.01809418,-0.00599923,0.00176596,-0.02216535,-0.06099529,0.0644327,-0.14563319,-0.01848938,0.03393406,0.01418508,0.01067095,0.00781934,0.00983689,0.00035764,-0.02770299,-0.00405223,-0.04905681,-0.02093454,-0.00173296,0.06387551,0.07455784,-0.05822355,-0.01193513,-0.07666159,-0.00975262,-0.02092683,0.11621652,-0.01730537,-0.12497528,-0.06590422,0.02032076,-0.03615899,-0.09771018,-0.02287838,-0.03952599,-0.07249285,0.03406779,0.08601721,-0.02374998,-0.07218177,-0.07168657,-0.02425272,0.0045653,0.01925174,-0.04367195,-0.03855467,-0.00403895,-0.00790118,-0.080023,0.0160688,-0.0549487,0.06876095,0.0327906,-0.0180556,0.01797899,-0.02949244,0.00903136,-0.03888952,0.00504689,-0.04086361,-0.06092625,0.073187,0.01736765,-0.02105333,-0.02450611,-0.02473415,-0.01639491,-0.02170185,0.02957819,-0.03041187,-0.0939443,0.08158869,0.01913828,-0.00434596,0.01541716,0.06695067,0.06276449,-0.06161087,0.0134757,0.01769788,0.05304505,-0.04909483,0.01793029,0.01518033,0.02151052,-0.08525284,-0.18863055,-0.04147661,-0.05639001,-0.00600536,0.0506169,0.00108495,0.03919488,-0.02853187,0.0629827,0.08323003,0.09745312,-0.07062387,0.04013713,0.06806701,-0.00604621,0.0137454,-0.08957339,-0.02796495,-0.04574317,0.0379618,-0.00076164,-0.00014069,-0.03659623,-0.07171234,0.03274499,-0.02576843,0.15959474,0.01243647,0.02032558,0.02603768,0.06957356,0.03258098,-0.0081269,-0.05904905,0.02935808,0.01585784,0.00914445,0.0072228,-0.04695635,0.00678569,-0.03441986,0.02849509,-0.00095244,-0.09146436,-0.05790049,-0.01056293,0.00107496,0.03205908,-0.0290683,0.03161883,0.0700589,0.02573529,0.0708528,0.00524246,0.04995872,-0.01647945,-0.05642956,-0.01968884,0.00524255,0.04945238,-0.01310642,-0.02816925,0.00716125,-0.00320075,0.07470318,-0.00488209,0.02115027,-0.01547005,0.02298189,-0.02614684,-0.00290602,0.04575764,0.00579837,0.02445022,0.05863934,0.02693648,0.10926493,-0.06799652,0.01368708,0.02323086,-0.01204646,-0.06469275,0.06516406,0.0775468,0.05117473,0.04729644,0.05963028,0.02705671,-0.00892465,0.01420817,0.01823399,0.02227147,-0.07520711,0.00475473,0.01469805,0.05729989,-0.2588928,0.01958853,-0.01946163,0.0266401,-0.03349032,-0.03327632,0.03013186,-0.01405018,0.00535027,-0.00277923,0.06175049,0.04473296,0.0082838,-0.0297115,-0.02593173,-0.02334816,0.00451688,-0.0062731,0.07810888,0.04862436,0.0091562,0.04842882,0.21893679,-0.00455044,0.01520037,0.02104618,-0.01022464,0.00867344,-0.0475186,0.01446403,0.02609966,0.04798079,0.01431506,-0.00366698,-0.05819776,0.02764533,0.00501705,0.01806025,-0.02667413,0.01178154,-0.08024651,0.03689671,0.02877791,0.04856655,0.11274921,0.00524619,-0.03728374,-0.07860298,0.05221991,0.03555175,-0.10274729,-0.08060458,-0.02763288,-0.03057158,-0.00733782,0.04842722,0.04489158,-0.01578956,0.02772722,0.03114173,0.01728184,0.02028309,0.06294186,0.03526855,0.01387596],"last_embed":{"hash":"6lecfc","tokens":384}}},"text":null,"length":0,"last_read":{"hash":"6lecfc","at":1753423630910},"key":"notes/saliu/The Best Ever Lottery Strategy, Lotto Strategies.md#The Best Ever Lottery Strategy, Lotto Strategies#<u>IV. Resources in Lottery, Strategies, Software, Systems, Lotto Wheels</u>#{13}","lines":[212,217],"size":888,"outlinks":[{"title":"Lottery software programs ready to create the best lotto strategies to win big money.","target":"https://software.saliu.com/HLINE.gif","line":1},{"title":"**Roulette**","target":"https://download.saliu.com/roulette-systems.html","line":3},{"title":"**Sic Bo**","target":"https://forums.saliu.com/strategies-casino-war-sic-bo-gambling-formula.html","line":3},{"title":"**Artificial Intelligence**","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":3},{"title":"**Craps**","target":"https://saliu.com/bbs/messages/504.html","line":3},{"title":"**Sports**","target":"https://saliu.com/betting.html","line":3},{"title":"**Blackjack**","target":"https://saliu.com/blackjack-strategy-system-win.html","line":3},{"title":"**Baccarat**","target":"https://saliu.com/winning_bell.html","line":3},{"title":"**_Software_ Home**","target":"https://software.saliu.com/index.html","line":3},{"title":"**Live Sports**","target":"https://software.saliu.com/live-sports-streams.html","line":3},{"title":"This is the site for lotto, lottery software strategies, winning systems for all games in the world.","target":"https://software.saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
