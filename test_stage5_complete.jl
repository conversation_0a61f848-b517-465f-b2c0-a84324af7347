#!/usr/bin/env julia

# 階段 5 完整功能測試
println("開始測試階段 5 的完整實現...")

try
    using Dates
    include("src/WonderGridEngine.jl")
    using .WonderGridEngine
    
    println("✓ WonderGridEngine 模組載入成功")
    
    # 創建更豐富的測試數據
    test_drawings = [
        WonderGridEngine.Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
        WonderGridEngine.Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
        WonderGridEngine.Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47]),
        WonderGridEngine.Drawing(4, :Lotto6_49, Date(2024, 1, 4), [4, 10, 17, 30, 37, 48]),
        WonderGridEngine.Drawing(5, :Lotto6_49, Date(2024, 1, 5), [1, 11, 18, 31, 38, 49]),
        WonderGridEngine.Drawing(6, :<PERSON>to6_49, Date(2024, 1, 6), [5, 12, 19, 32, 39, 40]),
        WonderGridEngine.Drawing(7, :Lotto6_49, Date(2024, 1, 7), [6, 13, 20, 33, 41, 42]),
        WonderGridEngine.Drawing(8, :<PERSON>to6_49, Date(2024, 1, 8), [7, 14, 21, 34, 43, 44]),
        <PERSON>GridEngine.Drawing(9, :Lotto6_49, Date(2024, 1, 9), [8, 15, 22, 35, 45, 46]),
        <PERSON>GridEngine.Drawing(10, :Lotto6_49, Date(2024, 1, 10), [1, 9, 16, 23, 36, 47])
    ]
    
    println("✓ 測試數據創建成功 ($(length(test_drawings)) 期)")
    
    # 測試配置創建
    config = WonderGridEngine.WonderGridConfig(
        analysis_range = 50,
        top_pair_percentage = 0.25,
        key_number_strategy = :ffg,
        combination_limit = 100,
        enable_purge = true,
        enable_lie = false,
        game_type = :Lotto6_49
    )
    println("✓ 配置創建成功")
    
    # 測試完整的 Wonder Grid 策略執行
    println("\n開始執行完整的 Wonder Grid 策略...")
    result = WonderGridEngine.execute_wonder_grid_strategy(test_drawings, config)
    
    println("✓ Wonder Grid 策略執行成功")
    println("  - 關鍵號碼: $(result.key_number)")
    println("  - 頂級配對數量: $(length(result.top_pairs))")
    println("  - 生成組合數量: $(length(result.combinations))")
    println("  - 執行時間: $(round(result.generation_time, digits=2)) 秒")
    
    # 測試效率指標
    println("\n效率指標:")
    for (key, value) in result.efficiency_metrics
        println("  - $key: $(round(value, digits=4))")
    end
    
    # 測試關鍵號碼選擇的不同策略
    println("\n測試不同的關鍵號碼選擇策略:")
    
    strategies = [:ffg, :skip, :frequency]
    for strategy in strategies
        try
            key_result = WonderGridEngine.select_key_number(test_drawings, strategy)
            println("  ✓ $strategy 策略: 號碼 $(key_result.number), 評分 $(round(key_result.score, digits=3))")
        catch e
            println("  ✗ $strategy 策略失敗: $e")
        end
    end
    
    # 測試配對統計計算
    println("\n測試配對統計計算...")
    pair_stats = WonderGridEngine.calculate_advanced_pair_statistics(test_drawings, config)
    println("✓ 配對統計計算成功，找到 $(length(pair_stats)) 個配對")
    
    # 顯示前 5 個配對的統計信息
    sorted_pairs = sort(collect(pair_stats), by = x -> x[2].frequency, rev = true)
    println("  前 5 個高頻配對:")
    for i in 1:min(5, length(sorted_pairs))
        pair, stats = sorted_pairs[i]
        println("    $(pair): 頻率=$(stats.frequency), 平均跳躍=$(round(stats.avg_skip, digits=2))")
    end
    
    # 測試組合生成
    println("\n測試組合生成...")
    key_number = result.key_number
    game_config = WonderGridEngine.GAME_CONFIGURATIONS[:Lotto6_49]
    
    # 創建測試用的頂級配對
    test_top_pairs = [
        WonderGridEngine.TopPair((key_number, 5), 3, 1, 2.0, 1, 0.8, 1, "測試配對"),
        WonderGridEngine.TopPair((key_number, 12), 2, 1, 3.0, 2, 0.7, 2, "測試配對"),
        WonderGridEngine.TopPair((key_number, 23), 2, 0, 4.0, 3, 0.6, 3, "測試配對")
    ]
    
    combinations = WonderGridEngine.generate_wonder_combinations(key_number, test_top_pairs, config, game_config)
    println("✓ 組合生成成功，生成 $(length(combinations)) 個組合")
    
    # 顯示前 3 個組合
    println("  前 3 個組合:")
    for i in 1:min(3, length(combinations))
        combo = combinations[i]
        println("    組合 $i: $(combo.numbers), 評分=$(round(combo.quality_score, digits=3))")
    end
    
    println("\n✓ 階段 5 所有功能測試通過！")
    
catch e
    println("✗ 測試失敗: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

println("\n階段 5 測試完成")
