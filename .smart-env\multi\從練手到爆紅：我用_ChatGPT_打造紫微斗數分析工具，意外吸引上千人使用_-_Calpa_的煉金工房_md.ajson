"smart_sources:從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md": {"path":"從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md","last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51"}}},"last_read":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51","at":1750403579233},"class_name":"SmartSource","last_import":{"mtime":1750381013558,"size":24971,"at":1750384454493,"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51"},"blocks":{"#":[1,342]},"outlinks":[],"key":"從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md"},
"smart_blocks:從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51","at":1750403579276},"key":"從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md#","lines":[1,342],"size":10359,"outlinks":[],"class_name":"SmartBlock"},"smart_blocks:從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51","at":1750403579276},"key":"從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md#","lines":[1,342],"size":10359,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"f7cbfc27366187c89bf28f0fc96dea1cf91f22c5ddbb3912f7fa57fb73dc0b51","at":1750403579276},"key":"從練手到爆紅：我用 ChatGPT 打造紫微斗數分析工具，意外吸引上千人使用 - Calpa 的煉金工房.md#","lines":[1,342],"size":10359,"outlinks":[],"class_name":"SmartBlock"},
