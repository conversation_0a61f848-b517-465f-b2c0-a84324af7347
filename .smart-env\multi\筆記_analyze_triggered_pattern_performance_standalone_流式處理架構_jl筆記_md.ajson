"smart_sources:筆記/analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記.md": {"path":"筆記/analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"132nu8b","at":1751950847602},"class_name":"SmartSource","last_import":{"mtime":1751946473884,"size":586,"at":1751950847604,"hash":"132nu8b"},"blocks":{"#analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記":[2,15],"#analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記#{1}":[4,6],"#analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記#模式的選擇彈性":[7,15],"#analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記#模式的選擇彈性#{1}":[8,14],"#analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記#模式的選擇彈性#{2}":[15,15]},"outlinks":[{"title":"gx_wins","target":"gx_wins","line":9}],"key":"筆記/analyze_triggered_pattern_performance_standalone_流式處理架構.jl筆記.md"},