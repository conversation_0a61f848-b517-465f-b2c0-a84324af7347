
"smart_sources:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md": {"path":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1gj7vty","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363611221,"size":32067,"at":1753423416500,"hash":"1gj7vty"},"blocks":{"#---frontmatter---":[1,6],"#Neural Networks, Lottery Prediction, Artificial Intelligence":[8,225],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{1}":[10,13],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{2}":[14,15],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{3}":[16,17],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{4}":[18,27],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{5}":[28,28],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{6}":[29,29],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{7}":[30,30],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{8}":[31,31],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{9}":[32,33],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{10}":[34,41],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{11}":[42,42],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{12}":[43,44],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{13}":[45,68],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{14}":[69,69],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{15}":[70,70],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{16}":[71,71],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{17}":[72,72],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{18}":[73,73],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{19}":[74,75],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{20}":[76,150],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{21}":[151,151],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{22}":[152,152],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{23}":[153,153],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{24}":[154,154],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{25}":[155,155],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{26}":[156,157],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{27}":[158,161],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_":[162,195],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{1}":[164,181],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{2}":[182,182],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{3}":[183,183],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{4}":[184,184],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{5}":[185,185],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{6}":[186,186],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{7}":[187,187],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{8}":[188,189],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{9}":[190,195],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking":[196,225],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{1}":[198,199],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{2}":[200,200],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{3}":[201,201],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{4}":[202,202],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{5}":[203,203],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{6}":[204,204],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{7}":[205,205],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{8}":[206,206],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{9}":[207,207],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{10}":[208,208],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{11}":[209,209],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{12}":[210,210],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{13}":[211,211],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{14}":[212,212],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{15}":[213,213],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{16}":[214,217],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{17}":[218,219],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{18}":[220,225]},"outlinks":[{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":18},{"title":"_**degrees of certainty**_","target":"https://saliu.com/Saliu2.htm","line":18},{"title":"_**everything is random, mathematically proven**_","target":"https://saliu.com/formula.htm","line":22},{"title":"Neural networking, artificial intelligence originated in super computer HAL of 2001 Space Odyssey.","target":"https://saliu.com/HLINE.gif","line":34},{"title":"_**Jackpot Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":40},{"title":"_**Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/#!topic/rec.gambling.lottery/VUB7H9NbNW4","line":51},{"title":"Lottery software helps neural networking, artificial intelligence AI learn patterns from the past.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":55},{"title":"The lotto-6 numbers ranked by frequency regardless of position are part of neural nets.","target":"https://saliu.com/images/neural-nets-lotto-1.gif","line":59},{"title":"The lotto-6 numbers ranked by frequency by position create effective neural networks.","target":"https://saliu.com/images/neural-nets-lottery-2.gif","line":65},{"title":"Checking lottery numbers for winners is the method to validate neural networking.","target":"https://saliu.com/images/neural-networks-lotto-3.gif","line":89},{"title":"Applying positional statistics improves the efficiency of lottery neural networks.","target":"https://saliu.com/images/neural-networks-lottery-4.gif","line":91},{"title":"_**Gail Howard**_","target":"https://saliu.com/bbs/messages/278.html","line":93},{"title":"There is no real artificial intelligence AI, in lottery or elsewhere; that's science fiction.","target":"https://saliu.com/images/artificial-intelligence-lotto-5.gif","line":99},{"title":"_**super lotto wheel**_","target":"https://saliu.com/lottowheel.html","line":101},{"title":"We must test how neural network prediction fares by checking future lottery drawings for winners.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":107},{"title":"Only humans have intelligence and it can be applied successfully to predicting lotto.","target":"https://saliu.com/images/artificial-intelligence-lottery-6.gif","line":113},{"title":"The best term or concept is axiomatic intelligence AxI working in lotto prediction.","target":"https://saliu.com/images/axiomatic-intelligence-lotto-7.gif","line":119},{"title":"The neural nets can group lottery numbers in pairs and also triplets for successful forecasting.","target":"https://saliu.com/images/axiomatic-intelligence-lottery-8.gif","line":125},{"title":"One great neural networking strategy for lottery is playing 12 lotto numbers in wheeling.","target":"https://saliu.com/images/best-neural-networks-9.gif","line":131},{"title":"This is the best real neural net strategy applied to lottery; jackpot within 20 draws.","target":"https://saliu.com/images/lottery-neural-nets-10.gif","line":137},{"title":"Neural networking, artificial intelligence in lottery also works with random combination generating.","target":"https://saliu.com/HLINE.gif","line":143},{"title":"created all those concepts derived from _axiomatic: axio, axios, axiomatics_","target":"https://www.facebook.com/Parpaluck/posts/10156708533469049","line":158},{"title":"There is no artificial intelligence, really: Only humans can make discoveries and program computers.","target":"https://saliu.com/HLINE.gif","line":160},{"title":"<u><i><b>Chess: The only case where AI is the closest to real INTELLIGENCE (human-like)</b></i></u>","target":"https://www.facebook.com/Parpaluck/posts/pfbid02MgjsXVjiLWzEpNpWpQoTxkncumNcbDD5CBx8BayzKz3cW9dwSUu5kbrafRkqhTZgl","line":188},{"title":"Neural networking, neural networks, artificial intelligence applied to predicting lottery, lotto.","target":"https://saliu.com/HLINE.gif","line":192},{"title":"\n\n## Resources in Lottery Software, Systems, Strategies, Neural Networking\n\n","target":"https://saliu.com/content/lottery.html","line":194},{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":201},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":202},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":203},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":204},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":205},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":206},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":207},{"title":"_**Artificial Intelligence, AI Essay: Ion Saliu's Philosophy of Politics**_","target":"https://saliu.com/AI-political-systems-Ion-Saliu.html","line":208},{"title":"_**Artificial Intelligence, AI Chatbots, Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":209},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":210},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":211},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":212},{"title":"_**Lottery Prediction Using Neural Networks**_","target":"https://groups.google.com/forum/#!topic/comp.ai.neural-nets/vnz8DKlBQqM","line":213},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":214},{"title":"_**Online Random Number Generator: Lotto, Powerball, Mega Millions, Lottery, Horse Racing, Roulette, Sports Betting, Euromillions**_","target":"https://saliu.com/generator.html","line":216},{"title":"_**Offline Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Horses, Sports, Roulette**_","target":"https://saliu.com/gambling-lottery-lotto/odds-generator.html","line":217},{"title":"**software**","target":"https://saliu.com/infodown.html","line":218},{"title":"Ion Saliu devised neural networking, AI strategies for lottery and lotto long before anyone else.","target":"https://saliu.com/HLINE.gif","line":220},{"title":"Forums","target":"https://forums.saliu.com/","line":222},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":222},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":222},{"title":"Contents","target":"https://saliu.com/content/index.html","line":222},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":222},{"title":"Home","target":"https://saliu.com/index.htm","line":222},{"title":"Search","target":"https://saliu.com/Search.htm","line":222},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":222},{"title":"Neural networking strategies in lottery are based on lotto number frequency, pairing in past draws.","target":"https://saliu.com/HLINE.gif","line":224}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["neural networking","neural networks","artificial intelligence","AI","prediction","lottery","lotto","systems","strategy","theory"],"source":"https://saliu.com/neural-networking-lottery.html","author":null}},
"smart_sources:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md": {"path":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08600264,-0.05297786,-0.05671954,0.02163206,-0.00617953,0.05824143,0.05684379,0.00961017,0.09694067,-0.03152185,-0.00671854,-0.02218561,0.01884859,0.04119061,-0.00716358,0.00577748,-0.0505215,-0.00415417,-0.06361518,0.02420077,0.08582679,-0.03738273,-0.04770288,-0.11664464,0.03689185,-0.01862973,0.03922643,-0.05288996,-0.01757273,-0.21797378,-0.00467681,-0.00107502,0.00011652,-0.05480755,-0.03350731,-0.02324369,0.01463653,0.03536955,-0.0244649,0.06369349,0.01783671,-0.0022598,-0.00456412,-0.03034419,0.04600953,0.00431718,-0.00930987,-0.02444225,-0.05883824,-0.02076068,-0.06889518,-0.0015251,-0.01383904,0.04493839,0.05494789,0.05373571,0.04645834,0.08467156,0.01808115,0.04534851,0.01268275,0.07565393,-0.17526633,0.04037713,0.04267249,-0.00331845,-0.03282575,-0.03236376,0.01275162,0.02646711,0.06588248,0.04849727,-0.00434489,0.06019235,0.0436415,0.03314551,0.01712809,-0.05091793,-0.01870028,0.04013852,0.00397581,0.0012299,-0.07124951,-0.02804322,-0.03736429,0.05957513,0.01901433,0.00292519,0.00513496,-0.09605449,-0.01377616,0.03670913,-0.02288081,0.05294221,0.03840796,0.03401573,0.05836992,-0.00066199,-0.01037971,0.09632187,-0.02801313,0.00580422,-0.00023623,0.01809797,0.04540249,-0.01561295,-0.05839869,-0.05890572,-0.0680026,0.00875866,0.00924789,-0.04971029,0.03301428,-0.05562813,-0.03720458,0.01042449,0.04598315,0.05856204,0.03792631,-0.04598059,-0.04938655,0.03322631,0.03585252,0.0237292,-0.02993217,-0.00098654,-0.02278332,0.09756988,-0.00479258,-0.00646589,0.04402899,-0.02177942,-0.10587488,-0.04005674,0.03680971,-0.00786755,0.03057936,0.00418594,-0.01233173,0.03125396,-0.00680277,-0.0587068,0.07731862,-0.09779596,-0.09914123,0.07833977,0.0268754,0.00387965,0.04825515,-0.01260168,-0.02168016,0.01357741,-0.03633224,-0.07582165,-0.0114217,0.01353224,0.0702454,0.02536051,-0.06374429,0.02918908,-0.00603917,-0.05533545,-0.06564581,0.14250275,-0.01081201,-0.04966018,-0.03257386,0.02564696,0.01271029,-0.09511099,0.02171941,0.00459712,-0.02129515,0.02215297,0.01802071,0.00027486,-0.10757808,-0.03399695,-0.06552519,0.02927916,-0.05096823,-0.04374774,-0.02725391,0.0448129,-0.04146716,-0.08086917,0.01402907,-0.05614656,-0.00431897,0.00518343,-0.02065658,0.03257371,-0.04667778,0.01967097,-0.03087216,-0.0296733,-0.01698314,-0.02394639,0.03684001,-0.01268719,-0.12020064,-0.05444086,-0.01017859,-0.00646778,-0.05014322,0.00151323,0.03373076,-0.01283117,0.02953343,0.05369134,0.0029766,-0.04705267,0.00545435,0.0307926,-0.03351667,0.04480287,0.01028315,0.04685862,0.01236931,-0.00383267,0.04349182,0.02056436,-0.07236984,-0.16343889,-0.03873269,-0.04604455,-0.03654075,0.06900102,-0.04328355,0.03655932,-0.03324484,0.03603318,0.09717929,0.03437802,0.00032797,0.00749538,0.01077985,-0.01176054,0.0004393,-0.03033354,-0.02093484,-0.04380092,0.08816728,-0.02885284,0.08301339,-0.01777336,-0.09587666,-0.01050031,-0.01019021,0.11834078,0.02188719,0.04034963,0.04218367,0.03251361,0.06114768,-0.03877976,-0.05624684,0.07137448,0.00480048,4.8e-7,0.00628372,-0.06476597,-0.02534009,-0.06369551,0.05206823,0.00590833,-0.03778137,-0.07895719,-0.02721212,-0.01213373,0.009064,-0.05735876,0.04722439,0.05156652,0.00699042,0.06847862,0.01229588,0.03954347,0.02534811,-0.06479348,-0.00838624,-0.00151287,0.0727072,0.00309394,-0.03225561,-0.00430433,-0.02795515,0.05747763,0.00991193,-0.00725984,-0.03167905,0.0143612,-0.01083942,-0.00457028,0.18108951,0.06110342,-0.00646072,0.07704418,-0.03550098,0.02524259,-0.03496914,0.02111619,0.07407267,0.04342458,-0.10043481,0.06646131,0.05833761,0.04795031,0.00349702,0.04593117,0.01835372,0.03999021,0.03975845,-0.00700783,0.01055454,-0.05348306,-0.00240303,0.06652358,0.00916904,-0.23033915,0.07026462,-0.01549309,0.08472802,0.01287605,-0.03187495,0.06351594,-0.03303751,-0.0010811,-0.00213875,0.03380967,0.03439727,0.05822404,-0.04889948,-0.03783396,-0.01613384,0.03017219,-0.0725181,0.04493338,0.00276898,0.03678758,0.04372527,0.24314025,0.00058418,0.02103483,0.00006709,-0.00896475,-0.02598576,-0.0389882,0.00070404,0.02952312,0.0234102,-0.00033159,-0.0617522,-0.01200243,0.09099298,-0.01252858,0.00704288,0.02520845,-0.02694303,-0.02764241,-0.01923933,-0.01542086,0.00134226,0.1490569,0.0494535,-0.01986386,-0.07050901,0.00704006,0.08184894,-0.05616274,-0.06751452,-0.01651749,-0.05471317,-0.03572223,0.02488584,-0.00098966,-0.01275116,-0.01963952,-0.01418427,0.04602889,-0.00942234,0.06567878,0.0199432,-0.01257574],"last_embed":{"hash":"1gj7vty","tokens":484}}},"last_read":{"hash":"1gj7vty","at":1753423576713},"class_name":"SmartSource","last_import":{"mtime":1753363611221,"size":32067,"at":1753423416500,"hash":"1gj7vty"},"blocks":{"#---frontmatter---":[1,6],"#Neural Networks, Lottery Prediction, Artificial Intelligence":[8,225],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{1}":[10,13],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{2}":[14,15],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{3}":[16,17],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{4}":[18,27],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{5}":[28,28],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{6}":[29,29],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{7}":[30,30],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{8}":[31,31],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{9}":[32,33],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{10}":[34,41],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{11}":[42,42],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{12}":[43,44],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{13}":[45,68],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{14}":[69,69],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{15}":[70,70],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{16}":[71,71],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{17}":[72,72],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{18}":[73,73],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{19}":[74,75],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{20}":[76,150],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{21}":[151,151],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{22}":[152,152],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{23}":[153,153],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{24}":[154,154],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{25}":[155,155],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{26}":[156,157],"#Neural Networks, Lottery Prediction, Artificial Intelligence#{27}":[158,161],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_":[162,195],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{1}":[164,181],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{2}":[182,182],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{3}":[183,183],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{4}":[184,184],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{5}":[185,185],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{6}":[186,186],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{7}":[187,187],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{8}":[188,189],"#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{9}":[190,195],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking":[196,225],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{1}":[198,199],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{2}":[200,200],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{3}":[201,201],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{4}":[202,202],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{5}":[203,203],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{6}":[204,204],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{7}":[205,205],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{8}":[206,206],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{9}":[207,207],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{10}":[208,208],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{11}":[209,209],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{12}":[210,210],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{13}":[211,211],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{14}":[212,212],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{15}":[213,213],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{16}":[214,217],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{17}":[218,219],"#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{18}":[220,225]},"outlinks":[{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":18},{"title":"_**degrees of certainty**_","target":"https://saliu.com/Saliu2.htm","line":18},{"title":"_**everything is random, mathematically proven**_","target":"https://saliu.com/formula.htm","line":22},{"title":"Neural networking, artificial intelligence originated in super computer HAL of 2001 Space Odyssey.","target":"https://saliu.com/HLINE.gif","line":34},{"title":"_**Jackpot Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":40},{"title":"_**Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/#!topic/rec.gambling.lottery/VUB7H9NbNW4","line":51},{"title":"Lottery software helps neural networking, artificial intelligence AI learn patterns from the past.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":55},{"title":"The lotto-6 numbers ranked by frequency regardless of position are part of neural nets.","target":"https://saliu.com/images/neural-nets-lotto-1.gif","line":59},{"title":"The lotto-6 numbers ranked by frequency by position create effective neural networks.","target":"https://saliu.com/images/neural-nets-lottery-2.gif","line":65},{"title":"Checking lottery numbers for winners is the method to validate neural networking.","target":"https://saliu.com/images/neural-networks-lotto-3.gif","line":89},{"title":"Applying positional statistics improves the efficiency of lottery neural networks.","target":"https://saliu.com/images/neural-networks-lottery-4.gif","line":91},{"title":"_**Gail Howard**_","target":"https://saliu.com/bbs/messages/278.html","line":93},{"title":"There is no real artificial intelligence AI, in lottery or elsewhere; that's science fiction.","target":"https://saliu.com/images/artificial-intelligence-lotto-5.gif","line":99},{"title":"_**super lotto wheel**_","target":"https://saliu.com/lottowheel.html","line":101},{"title":"We must test how neural network prediction fares by checking future lottery drawings for winners.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":107},{"title":"Only humans have intelligence and it can be applied successfully to predicting lotto.","target":"https://saliu.com/images/artificial-intelligence-lottery-6.gif","line":113},{"title":"The best term or concept is axiomatic intelligence AxI working in lotto prediction.","target":"https://saliu.com/images/axiomatic-intelligence-lotto-7.gif","line":119},{"title":"The neural nets can group lottery numbers in pairs and also triplets for successful forecasting.","target":"https://saliu.com/images/axiomatic-intelligence-lottery-8.gif","line":125},{"title":"One great neural networking strategy for lottery is playing 12 lotto numbers in wheeling.","target":"https://saliu.com/images/best-neural-networks-9.gif","line":131},{"title":"This is the best real neural net strategy applied to lottery; jackpot within 20 draws.","target":"https://saliu.com/images/lottery-neural-nets-10.gif","line":137},{"title":"Neural networking, artificial intelligence in lottery also works with random combination generating.","target":"https://saliu.com/HLINE.gif","line":143},{"title":"created all those concepts derived from _axiomatic: axio, axios, axiomatics_","target":"https://www.facebook.com/Parpaluck/posts/10156708533469049","line":158},{"title":"There is no artificial intelligence, really: Only humans can make discoveries and program computers.","target":"https://saliu.com/HLINE.gif","line":160},{"title":"<u><i><b>Chess: The only case where AI is the closest to real INTELLIGENCE (human-like)</b></i></u>","target":"https://www.facebook.com/Parpaluck/posts/pfbid02MgjsXVjiLWzEpNpWpQoTxkncumNcbDD5CBx8BayzKz3cW9dwSUu5kbrafRkqhTZgl","line":188},{"title":"Neural networking, neural networks, artificial intelligence applied to predicting lottery, lotto.","target":"https://saliu.com/HLINE.gif","line":192},{"title":"\n\n## Resources in Lottery Software, Systems, Strategies, Neural Networking\n\n","target":"https://saliu.com/content/lottery.html","line":194},{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":201},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":202},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":203},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":204},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":205},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":206},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":207},{"title":"_**Artificial Intelligence, AI Essay: Ion Saliu's Philosophy of Politics**_","target":"https://saliu.com/AI-political-systems-Ion-Saliu.html","line":208},{"title":"_**Artificial Intelligence, AI Chatbots, Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":209},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":210},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":211},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":212},{"title":"_**Lottery Prediction Using Neural Networks**_","target":"https://groups.google.com/forum/#!topic/comp.ai.neural-nets/vnz8DKlBQqM","line":213},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":214},{"title":"_**Online Random Number Generator: Lotto, Powerball, Mega Millions, Lottery, Horse Racing, Roulette, Sports Betting, Euromillions**_","target":"https://saliu.com/generator.html","line":216},{"title":"_**Offline Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Horses, Sports, Roulette**_","target":"https://saliu.com/gambling-lottery-lotto/odds-generator.html","line":217},{"title":"**software**","target":"https://saliu.com/infodown.html","line":218},{"title":"Ion Saliu devised neural networking, AI strategies for lottery and lotto long before anyone else.","target":"https://saliu.com/HLINE.gif","line":220},{"title":"Forums","target":"https://forums.saliu.com/","line":222},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":222},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":222},{"title":"Contents","target":"https://saliu.com/content/index.html","line":222},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":222},{"title":"Home","target":"https://saliu.com/index.htm","line":222},{"title":"Search","target":"https://saliu.com/Search.htm","line":222},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":222},{"title":"Neural networking strategies in lottery are based on lotto number frequency, pairing in past draws.","target":"https://saliu.com/HLINE.gif","line":224}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["neural networking","neural networks","artificial intelligence","AI","prediction","lottery","lotto","systems","strategy","theory"],"source":"https://saliu.com/neural-networking-lottery.html","author":null}},"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10630307,-0.0635096,-0.0298295,0.02217312,0.02515348,0.06257237,0.003152,0.02748379,0.08744103,-0.01095313,-0.01736023,-0.03982424,-0.00461722,0.05137628,0.00636957,-0.02035985,-0.03830292,-0.01769007,-0.02956692,0.01220505,0.10737329,-0.00117069,-0.0436426,-0.09443024,0.03113355,-0.03657185,0.02097096,-0.04790043,-0.03775829,-0.16030118,0.01030241,0.01325212,0.01338019,-0.00821499,-0.04214225,-0.04699044,0.02336788,0.05099044,-0.00747157,0.08383016,0.01579457,0.01249185,-0.03898135,-0.01584799,0.06726326,-0.00126992,-0.00245319,-0.01706822,-0.03670794,-0.00523296,-0.03740131,-0.01427726,-0.00954974,0.04590739,0.06293084,0.01603768,0.04592177,0.05868626,0.0474767,0.02085041,0.01757963,0.04724666,-0.19187176,0.0417172,-0.00442577,0.02018469,-0.03134048,-0.01940305,-0.00680511,0.02545425,0.0674967,0.02894712,-0.00055059,0.03914558,0.02417936,0.04914697,0.00444259,-0.04305718,-0.01435109,0.0078245,0.02199144,0.00175266,-0.0891037,-0.05215491,-0.00591033,0.04122567,0.01060371,0.05826238,0.02317276,-0.06335992,-0.00197986,0.0066411,0.04435791,0.06253904,-0.03043202,0.04127268,0.0277629,0.00429838,-0.05547819,0.12739709,-0.05556766,0.01591868,0.0140345,0.00641629,0.04582006,-0.01038191,-0.04019062,-0.06256094,-0.06762411,0.0076423,0.00564708,-0.0016207,0.0621164,-0.06884152,0.00201919,-0.00795517,0.05121977,0.04974149,0.0373865,0.00362601,-0.06938618,0.00624714,0.01946373,0.02119169,-0.00378392,0.02452343,-0.00555399,0.10339015,0.00534187,0.01920144,0.01555019,0.02025651,-0.1092305,-0.01868818,0.02393115,-0.01365728,0.0525987,-0.06818869,-0.02461566,-0.00794814,-0.02572189,-0.03268577,0.04400511,-0.09304235,-0.07074278,0.07159586,0.03761857,-0.03127071,0.03488658,-0.01075982,-0.04080028,0.02163264,-0.00450079,-0.073341,-0.00869397,0.02819842,0.08328315,0.05710553,-0.05526828,0.01646397,0.01096905,-0.0600452,-0.07876652,0.13683753,0.00229182,-0.10773671,-0.05126561,0.03299159,0.0218965,-0.07321025,0.00797333,-0.00816671,-0.01996336,0.01746934,0.04742132,0.00950807,-0.01533679,-0.04769734,-0.08564046,0.02263394,-0.04535221,-0.06210536,-0.05514785,0.04044184,-0.0471332,-0.07226951,0.00836441,-0.0765566,0.02244155,0.0505315,-0.04520348,0.00844472,-0.03562899,0.04735275,-0.02379196,-0.03189478,-0.00849305,-0.01008166,0.04945132,-0.03988981,-0.08579636,-0.02753655,-0.01513595,-0.02395035,-0.04738596,-0.00709765,0.00511706,-0.02233396,0.03210687,0.04417599,0.00887065,-0.01126741,0.06038204,0.01941234,-0.06197611,0.01806266,-0.02863658,0.01005273,-0.00260801,0.01419092,0.02023472,0.0049048,-0.11312725,-0.18243581,-0.00131312,-0.01726908,-0.06975161,0.03561406,-0.06585098,0.03281983,0.00357782,0.04232773,0.09239157,0.06522988,-0.0107461,0.01662752,-0.02586261,0.00336424,0.01363153,-0.02309694,-0.02838934,-0.02007872,0.05222989,0.00206096,0.04604623,0.00110358,-0.10424353,0.00010148,0.00251677,0.11105238,0.07693004,0.05702294,-0.0151295,0.06176829,0.03541097,-0.00905993,-0.10998449,0.04352973,-0.01590512,0.01199016,0.04020435,-0.08280817,-0.05360077,-0.08354229,0.05617838,0.00146348,-0.07636362,-0.04326532,0.00265087,0.00300253,-0.03165645,-0.05281634,0.02172064,0.0447122,-0.01836218,0.0401083,0.04178846,0.04923528,-0.00167467,-0.08575441,-0.01784683,-0.02157111,0.06321327,0.01646305,-0.03888686,0.00939232,-0.04676259,0.00855012,0.02214403,-0.01077617,-0.03295402,-0.01134332,0.02287455,-0.00737969,0.14878617,0.06899953,-0.00815983,0.04465373,-0.05274339,0.01070531,-0.04108704,0.02091225,0.07323143,0.02623206,-0.08314364,0.09016502,0.0660507,0.05577242,0.04060989,0.03877756,-0.0074577,0.06042528,0.02711013,-0.00529051,0.01846942,-0.0421631,-0.0092562,0.10703972,0.00013984,-0.25951627,0.09863744,0.06277879,0.08713111,0.04344053,0.00239342,0.03227201,-0.03532873,-0.02293847,-0.01894148,0.03854449,0.02980739,0.02781945,-0.0306531,-0.05072883,-0.00307922,0.03037932,-0.05600962,0.02334759,0.03138718,-0.00732929,0.04238702,0.25061026,-0.0064028,-0.00232077,-0.00331014,-0.01330446,-0.01685115,0.00925839,0.02095165,0.00588806,0.01827262,0.02790729,-0.02935426,-0.01502479,0.09472977,-0.03292145,0.01304892,0.01682588,0.01260348,-0.05355179,0.00761939,-0.03392034,0.02480316,0.11275247,0.06469968,-0.04472831,-0.0671005,-0.01952166,0.04576728,-0.05463075,-0.07136207,-0.03870605,-0.01795354,-0.01500949,0.04366564,0.04718409,-0.04115693,-0.01940299,-0.00980129,0.05245251,-0.03407331,0.07560843,-0.00113025,-0.02390416],"last_embed":{"hash":"vgxlyl","tokens":90}}},"text":null,"length":0,"last_read":{"hash":"vgxlyl","at":1753423574205},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#---frontmatter---","lines":[1,6],"size":234,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0789075,-0.05734627,-0.05030672,0.01812388,-0.01283318,0.05246306,0.07049874,0.00809386,0.09957483,-0.0405734,-0.00226615,-0.01857082,0.035935,0.05173115,0.00132828,0.02793626,-0.04581638,-0.00751705,-0.07065257,0.01914432,0.08036227,-0.04729774,-0.03225598,-0.10865165,0.03393403,-0.01427955,0.03425279,-0.05673888,-0.02134085,-0.22378027,0.00548663,-0.00572519,0.00147554,-0.05767317,-0.02545095,-0.02374011,0.00583436,0.02201046,-0.02611305,0.0583332,0.02018369,-0.00398487,-0.00415848,-0.03593802,0.03779097,-0.01100652,-0.0027948,-0.02686062,-0.05859672,-0.02162937,-0.06213403,-0.00713785,-0.00324361,0.04616113,0.04622202,0.05692687,0.04943255,0.09024636,0.02337527,0.05480124,0.01706174,0.08217013,-0.17109655,0.03505239,0.05941945,-0.01490179,-0.03472703,-0.03483995,0.01690191,0.03093469,0.06279449,0.04625725,0.00089159,0.07427962,0.03750403,0.0250299,0.01994229,-0.0528651,-0.00574344,0.04467777,0.00098049,0.00819827,-0.05257484,-0.01904944,-0.0451076,0.05079148,0.02240806,-0.02025316,-0.01145317,-0.09762532,-0.01286368,0.03358142,-0.04112851,0.05126853,0.05038135,0.02510954,0.05762561,-0.00779417,-0.00097173,0.09821756,-0.02528451,-0.00877285,-0.0119877,0.01919835,0.03833026,-0.02341004,-0.05960565,-0.05265338,-0.06568881,0.00749292,0.01718747,-0.05373779,0.03422912,-0.04237164,-0.03439079,0.01519592,0.04914208,0.04782979,0.03300975,-0.05043304,-0.05541198,0.03582939,0.03908248,0.01689925,-0.02981808,-0.0141328,-0.02510677,0.09140011,-0.00073426,-0.00853646,0.05494733,-0.03263287,-0.09720752,-0.0364573,0.04145452,-0.00832872,0.02288589,0.01829511,-0.01305209,0.03123393,0.0042678,-0.06914558,0.08461629,-0.09404081,-0.10685305,0.07492119,0.0183959,0.0137418,0.05261072,-0.02105013,-0.01702074,0.00170468,-0.04490952,-0.06906588,-0.00812153,0.01259896,0.07823601,0.02277333,-0.06884637,0.04349395,-0.00547803,-0.05717945,-0.04991324,0.14857611,-0.01672789,-0.02798124,-0.03608047,0.01458871,0.01254439,-0.09019658,0.02239971,0.00835394,-0.01672573,0.03073968,0.01568352,-0.00102076,-0.11299757,-0.03031906,-0.05576688,0.03345816,-0.04373995,-0.0527063,-0.01691526,0.04930046,-0.04089839,-0.08572681,0.00583008,-0.0441106,-0.00647813,-0.01480399,-0.023488,0.03125159,-0.05124678,0.00748245,-0.04000575,-0.03338292,-0.01221004,-0.01838583,0.02500482,-0.00739825,-0.11644019,-0.06152269,-0.00724818,0.00449048,-0.04800595,0.00661613,0.03237535,-0.0077849,0.03207313,0.04889289,0.00542224,-0.0532475,0.00061496,0.03355306,-0.02519956,0.04236082,0.01294269,0.05569209,0.00881715,0.00098827,0.0461345,0.0200525,-0.06693972,-0.16786148,-0.04484939,-0.04507509,-0.03570167,0.07466292,-0.04015309,0.03318523,-0.03562685,0.03581022,0.1083767,0.0314037,-0.00063989,-0.00189539,0.0186338,-0.00887864,0.00173545,-0.03673054,-0.018771,-0.0438715,0.09045234,-0.02992056,0.08608869,-0.03290508,-0.09433601,-0.01362553,-0.00918942,0.12605408,0.0106685,0.04430024,0.05050881,0.01812628,0.05966811,-0.04735861,-0.0452518,0.07521067,0.01923031,-0.0021175,0.00048281,-0.06275113,-0.02105972,-0.06322216,0.06693379,-0.0056451,-0.04108822,-0.08252142,-0.033362,-0.00261414,0.02029366,-0.04996575,0.04645592,0.05070649,0.01241,0.07077254,0.0142396,0.04938549,0.01639139,-0.07128422,-0.00022219,0.00108751,0.07551525,0.00857884,-0.02311376,-0.00899256,-0.02583923,0.07200534,0.00500286,-0.00531652,-0.02353556,0.02079847,-0.01078608,-0.00567644,0.18076706,0.05784249,-0.0116195,0.08131799,-0.03606659,0.03543012,-0.041432,0.01406699,0.06900023,0.05509263,-0.10339495,0.04894431,0.04704649,0.0389441,-0.00877646,0.03591799,0.01933159,0.03705742,0.04391353,-0.00723864,0.01622996,-0.0547321,0.00388706,0.05269995,0.00594787,-0.22188407,0.06971878,-0.02671546,0.07944319,0.00581586,-0.03422173,0.0564361,-0.02556422,-0.00885595,-0.00306483,0.02649743,0.03146915,0.05383112,-0.05321085,-0.04384664,0.00124571,0.03231215,-0.07456891,0.04312491,-0.00481919,0.04570828,0.04671771,0.23026775,-0.00322873,0.03330549,0.00589205,-0.00805211,-0.02013878,-0.05014413,-0.02403142,0.02888678,0.01272444,-0.00818569,-0.07101836,0.00074331,0.07654735,-0.00815966,0.01754201,0.03668582,-0.04050046,-0.02368637,-0.03080918,-0.0096095,0.00203524,0.14665286,0.04412042,-0.01521028,-0.06086232,0.00889048,0.08255642,-0.0506849,-0.05891985,-0.00287119,-0.05269426,-0.0322777,0.02706969,-0.01380026,-0.00484625,-0.01254752,-0.02163839,0.04153088,-0.01353429,0.06277847,0.01592003,-0.01653493],"last_embed":{"hash":"rn4e4t","tokens":438}}},"text":null,"length":0,"last_read":{"hash":"rn4e4t","at":1753423574242},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence","lines":[8,225],"size":31777,"outlinks":[{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":11},{"title":"_**degrees of certainty**_","target":"https://saliu.com/Saliu2.htm","line":11},{"title":"_**everything is random, mathematically proven**_","target":"https://saliu.com/formula.htm","line":15},{"title":"Neural networking, artificial intelligence originated in super computer HAL of 2001 Space Odyssey.","target":"https://saliu.com/HLINE.gif","line":27},{"title":"_**Jackpot Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":33},{"title":"_**Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/#!topic/rec.gambling.lottery/VUB7H9NbNW4","line":44},{"title":"Lottery software helps neural networking, artificial intelligence AI learn patterns from the past.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":48},{"title":"The lotto-6 numbers ranked by frequency regardless of position are part of neural nets.","target":"https://saliu.com/images/neural-nets-lotto-1.gif","line":52},{"title":"The lotto-6 numbers ranked by frequency by position create effective neural networks.","target":"https://saliu.com/images/neural-nets-lottery-2.gif","line":58},{"title":"Checking lottery numbers for winners is the method to validate neural networking.","target":"https://saliu.com/images/neural-networks-lotto-3.gif","line":82},{"title":"Applying positional statistics improves the efficiency of lottery neural networks.","target":"https://saliu.com/images/neural-networks-lottery-4.gif","line":84},{"title":"_**Gail Howard**_","target":"https://saliu.com/bbs/messages/278.html","line":86},{"title":"There is no real artificial intelligence AI, in lottery or elsewhere; that's science fiction.","target":"https://saliu.com/images/artificial-intelligence-lotto-5.gif","line":92},{"title":"_**super lotto wheel**_","target":"https://saliu.com/lottowheel.html","line":94},{"title":"We must test how neural network prediction fares by checking future lottery drawings for winners.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":100},{"title":"Only humans have intelligence and it can be applied successfully to predicting lotto.","target":"https://saliu.com/images/artificial-intelligence-lottery-6.gif","line":106},{"title":"The best term or concept is axiomatic intelligence AxI working in lotto prediction.","target":"https://saliu.com/images/axiomatic-intelligence-lotto-7.gif","line":112},{"title":"The neural nets can group lottery numbers in pairs and also triplets for successful forecasting.","target":"https://saliu.com/images/axiomatic-intelligence-lottery-8.gif","line":118},{"title":"One great neural networking strategy for lottery is playing 12 lotto numbers in wheeling.","target":"https://saliu.com/images/best-neural-networks-9.gif","line":124},{"title":"This is the best real neural net strategy applied to lottery; jackpot within 20 draws.","target":"https://saliu.com/images/lottery-neural-nets-10.gif","line":130},{"title":"Neural networking, artificial intelligence in lottery also works with random combination generating.","target":"https://saliu.com/HLINE.gif","line":136},{"title":"created all those concepts derived from _axiomatic: axio, axios, axiomatics_","target":"https://www.facebook.com/Parpaluck/posts/10156708533469049","line":151},{"title":"There is no artificial intelligence, really: Only humans can make discoveries and program computers.","target":"https://saliu.com/HLINE.gif","line":153},{"title":"<u><i><b>Chess: The only case where AI is the closest to real INTELLIGENCE (human-like)</b></i></u>","target":"https://www.facebook.com/Parpaluck/posts/pfbid02MgjsXVjiLWzEpNpWpQoTxkncumNcbDD5CBx8BayzKz3cW9dwSUu5kbrafRkqhTZgl","line":181},{"title":"Neural networking, neural networks, artificial intelligence applied to predicting lottery, lotto.","target":"https://saliu.com/HLINE.gif","line":185},{"title":"\n\n## Resources in Lottery Software, Systems, Strategies, Neural Networking\n\n","target":"https://saliu.com/content/lottery.html","line":187},{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":194},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":195},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":196},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":197},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":198},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":199},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":200},{"title":"_**Artificial Intelligence, AI Essay: Ion Saliu's Philosophy of Politics**_","target":"https://saliu.com/AI-political-systems-Ion-Saliu.html","line":201},{"title":"_**Artificial Intelligence, AI Chatbots, Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":202},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":203},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":204},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":205},{"title":"_**Lottery Prediction Using Neural Networks**_","target":"https://groups.google.com/forum/#!topic/comp.ai.neural-nets/vnz8DKlBQqM","line":206},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":207},{"title":"_**Online Random Number Generator: Lotto, Powerball, Mega Millions, Lottery, Horse Racing, Roulette, Sports Betting, Euromillions**_","target":"https://saliu.com/generator.html","line":209},{"title":"_**Offline Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Horses, Sports, Roulette**_","target":"https://saliu.com/gambling-lottery-lotto/odds-generator.html","line":210},{"title":"**software**","target":"https://saliu.com/infodown.html","line":211},{"title":"Ion Saliu devised neural networking, AI strategies for lottery and lotto long before anyone else.","target":"https://saliu.com/HLINE.gif","line":213},{"title":"Forums","target":"https://forums.saliu.com/","line":215},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":215},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":215},{"title":"Contents","target":"https://saliu.com/content/index.html","line":215},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":215},{"title":"Home","target":"https://saliu.com/index.htm","line":215},{"title":"Search","target":"https://saliu.com/Search.htm","line":215},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":215},{"title":"Neural networking strategies in lottery are based on lotto number frequency, pairing in past draws.","target":"https://saliu.com/HLINE.gif","line":217}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06780422,-0.06483068,-0.04971206,0.01515607,-0.0058016,0.05362967,0.07603808,-0.00229496,0.11043846,-0.02760409,0.00534707,-0.02283795,0.03109012,0.05333357,0.00065679,0.02703003,-0.04635119,-0.02878135,-0.0595514,0.03634138,0.07515138,-0.04477593,-0.02796182,-0.10866152,0.02316288,-0.02492463,0.01758237,-0.05081671,-0.0227294,-0.22301663,0.01709551,-0.00634943,0.0027707,-0.0476303,-0.03899903,-0.03484275,0.01777223,0.02505804,-0.01867116,0.05614946,0.01176373,-0.00766403,-0.00931421,-0.0354167,0.0328708,-0.00018242,-0.00068364,-0.02511457,-0.07321704,-0.01992581,-0.06312368,-0.00142394,-0.00217666,0.04875949,0.04179441,0.05798019,0.04467656,0.08801205,0.01712654,0.05840303,0.01027786,0.072438,-0.15797338,0.03108543,0.05191584,-0.01197439,-0.03383252,-0.04342174,0.0161546,0.02416083,0.06467703,0.05953763,0.00030276,0.07162191,0.03752355,0.0140376,0.01339986,-0.04360282,-0.00308144,0.05802836,0.00399752,0.00575733,-0.06028255,-0.02452027,-0.04266151,0.04465293,0.02851023,-0.01259286,-0.00411187,-0.09846564,-0.0016092,0.04788748,-0.0407107,0.03816266,0.05115711,0.04000716,0.06203072,-0.01016404,-0.02018576,0.09010454,-0.01920825,-0.02126258,-0.01566231,0.03550433,0.03645626,-0.02374391,-0.06489334,-0.05183147,-0.05704696,0.00867234,0.01807659,-0.05604314,0.03851265,-0.03636502,-0.03849228,0.01776132,0.04412357,0.05441532,0.03954121,-0.04484822,-0.06058137,0.03771379,0.04284009,0.02218503,-0.0239811,-0.01792082,-0.03109122,0.09689163,-0.00472451,-0.00414378,0.04962687,-0.04302275,-0.09481367,-0.03013725,0.03207125,-0.01252806,0.02567508,0.02184109,-0.01029639,0.02896377,0.0084304,-0.05590127,0.08699434,-0.090319,-0.09523696,0.07461704,0.02916584,0.01664468,0.06616452,-0.0276855,-0.01979244,-0.00457162,-0.04550743,-0.06556288,-0.01462007,0.01365709,0.08116366,0.02658391,-0.07660899,0.03525558,-0.00484724,-0.06776654,-0.04347776,0.14623088,-0.01660432,-0.02039842,-0.03236559,0.01088476,0.00368732,-0.09213912,0.01293041,0.01030568,-0.01347439,0.03696834,0.01554293,0.00031874,-0.10541791,-0.02449979,-0.04492973,0.02971394,-0.03634332,-0.04276049,-0.01407487,0.04312432,-0.04000738,-0.1000836,0.00826841,-0.0444993,-0.00994797,-0.01042654,-0.02589239,0.02271359,-0.04498192,0.0095143,-0.03827217,-0.03493643,-0.00512156,-0.01010819,0.02715933,-0.00266876,-0.12263815,-0.05825634,-0.00525088,0.01187769,-0.05698088,0.0205468,0.03617353,-0.02366384,0.04835657,0.03994198,0.00538475,-0.05425865,-0.00663737,0.0420182,-0.02503088,0.05149817,0.00883226,0.05108972,0.01979474,0.00086169,0.048211,0.02730526,-0.06089515,-0.16402143,-0.04712609,-0.04917875,-0.02515758,0.07572716,-0.03902752,0.0215752,-0.03085504,0.03805501,0.11679062,0.03335943,-0.01681181,-0.00265959,0.01017461,-0.01838469,-0.00250171,-0.05097425,-0.02808406,-0.04749417,0.08624548,-0.03030682,0.07818206,-0.04382079,-0.10305882,-0.01033811,-0.00827202,0.12542871,0.00614096,0.04734991,0.0349786,0.01826696,0.05180319,-0.0320143,-0.0506841,0.07302388,0.01707905,0.00895196,0.01372299,-0.07359234,-0.02863769,-0.06378254,0.06784996,-0.00387641,-0.03274224,-0.08707824,-0.02870374,-0.00491336,0.03030946,-0.0534084,0.04601314,0.0458372,0.00425255,0.06710463,0.00916917,0.05515929,0.00881697,-0.06821233,0.005804,0.00022954,0.08264188,0.01063345,-0.02325302,0.00261928,-0.02669936,0.06196167,0.00706098,-0.00307737,-0.02457648,0.02269242,0.01208194,-0.00335209,0.18204257,0.04660504,-0.01307709,0.07947638,-0.03622368,0.03673895,-0.03404993,0.01459008,0.06498323,0.05905792,-0.09591586,0.04461029,0.05701203,0.03234049,-0.02928063,0.04182424,0.02147388,0.03949308,0.04891345,-0.01015891,0.01942099,-0.04706738,0.00363591,0.04215291,-0.00248946,-0.22178058,0.07384057,-0.03559867,0.07842141,0.01506376,-0.04161431,0.04297226,-0.02901156,-0.00527634,-0.00864883,0.0296873,0.02266463,0.07271006,-0.05625713,-0.04122537,-0.00426188,0.03964506,-0.08321559,0.03870693,-0.00315526,0.06418463,0.04280556,0.22910722,-0.01407961,0.02769731,0.00461283,-0.00558569,-0.01157736,-0.04178809,-0.01578095,0.01706447,0.00928755,-0.01360188,-0.06733733,0.00558548,0.06169662,-0.01272031,0.02363767,0.03415193,-0.04055848,-0.0237042,-0.02935338,-0.02181942,0.00293769,0.14940922,0.0515957,-0.01913092,-0.04294892,0.01505173,0.07704505,-0.06291476,-0.06435895,-0.00524869,-0.05565882,-0.03618153,0.02487115,-0.01637079,-0.01096393,-0.01413458,-0.03164429,0.04740729,-0.01275233,0.06803629,0.01030824,-0.02622868],"last_embed":{"hash":"1l1mxl0","tokens":483}}},"text":null,"length":0,"last_read":{"hash":"1l1mxl0","at":1753423574433},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{4}","lines":[18,27],"size":1853,"outlinks":[{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":1},{"title":"_**degrees of certainty**_","target":"https://saliu.com/Saliu2.htm","line":1},{"title":"_**everything is random, mathematically proven**_","target":"https://saliu.com/formula.htm","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06137083,-0.05334123,-0.01721721,0.00197799,0.01179219,0.03957299,0.10632548,-0.00474632,0.09045856,-0.00367619,0.00248081,-0.03377384,0.03783895,0.01729972,-0.01127435,0.02247556,-0.03410664,-0.01503403,-0.06570857,0.01385442,0.10365032,-0.02191888,-0.02972076,-0.04885891,-0.00965237,-0.01553767,-0.00609248,-0.06717578,-0.04364447,-0.13570186,0.01426033,0.0106498,0.04344299,0.01073284,-0.01890464,-0.01739611,0.04296736,0.00573465,-0.04204013,0.07536606,-0.02155466,-0.05496076,0.0000927,-0.00539486,0.05973359,0.02627194,-0.01616856,-0.01794198,-0.1126467,-0.0215988,-0.01476201,0.02915045,-0.01601762,0.06330566,0.01890717,0.02687926,0.03313904,0.06445374,0.04341833,-0.00675381,0.03893127,0.06137201,-0.15985277,0.03646157,0.01644891,0.02517219,-0.01276386,-0.08714166,-0.06105386,0.01695871,0.05919516,0.0363167,0.01614327,0.0359869,0.02417046,0.00551687,0.02937063,-0.05023587,0.01973208,0.06043825,0.03638747,0.01273003,-0.0525303,-0.01590601,-0.01610444,0.05962794,-0.01329893,-0.03410568,0.02340168,-0.05918491,-0.01196071,0.02176781,0.00741131,0.07139266,0.08139635,0.01007074,0.013733,-0.04621131,-0.08727796,0.11090858,-0.03842327,0.01874575,0.01613452,-0.0208976,0.05149507,-0.02164383,-0.03634772,-0.09665871,-0.05330982,-0.00007294,-0.00372669,-0.02013406,0.03763859,0.00108325,0.00086282,-0.00983126,0.03498326,0.05280344,0.0454692,-0.01191227,-0.06013669,0.01509204,0.03198257,0.02774452,-0.00713096,-0.05881316,-0.00088835,0.07726598,-0.03338592,0.00950038,0.04308873,-0.0584448,-0.09873917,-0.0194542,0.03004936,0.01535997,0.02546404,-0.03446231,-0.04071951,0.02289707,-0.01490473,-0.05216134,0.01307611,-0.06063942,-0.07175609,0.07396945,0.02775149,0.02095894,0.08310673,0.03772368,-0.03257152,-0.0090166,-0.04377282,-0.07963067,-0.01703032,0.05359669,0.0561444,-0.00699559,-0.08176153,0.00322193,0.06835166,-0.00964838,-0.08789577,0.16362455,-0.01044221,-0.04037159,-0.04705675,0.03347075,0.05440482,-0.01772541,0.03840657,-0.01484738,-0.04681113,0.03560185,0.03197892,-0.01169389,-0.05700506,-0.01801419,-0.09286519,0.03917652,-0.00003741,-0.02676051,-0.02246022,0.01606367,-0.00357746,-0.03891083,0.01557414,-0.04755897,0.02594527,0.0397382,-0.04731919,0.07265,-0.01185767,0.0208381,-0.02664782,-0.02306147,0.00936917,-0.01763936,0.03602532,-0.02919116,-0.11439814,0.00997119,0.02183077,-0.01958065,-0.01271451,0.02957167,0.02429798,-0.02563298,0.07059608,0.03769662,-0.03405759,-0.06749613,-0.02235447,0.01730752,-0.04550921,0.03313453,0.02471859,0.01559664,0.05399414,0.01450975,0.01316269,0.02864503,-0.12257974,-0.14760098,-0.04941731,-0.04024832,-0.07322495,0.05616544,-0.01265639,0.00274694,0.03505551,0.01538432,0.1038706,0.0409222,-0.00495396,0.02342899,-0.00396471,-0.01241871,0.0516072,-0.03470157,-0.00504925,-0.06583029,0.04648408,-0.04027783,0.07550406,-0.00982535,-0.09264687,0.00284702,0.01855068,0.15982288,0.05634641,0.06033232,0.0443237,-0.01177666,0.04770938,-0.02999087,-0.13196917,0.04914142,0.00871597,-0.02216475,-0.05424139,-0.07756598,-0.04754739,-0.05436807,0.03742227,0.00479436,-0.06651907,-0.05407754,-0.00291167,-0.0108031,-0.01106063,-0.06776319,0.04405957,0.01377403,-0.02175386,0.01952749,0.05873168,0.05305843,-0.00535137,-0.06988288,0.01189133,-0.00609299,0.11700553,0.05129998,-0.07485938,-0.02306904,-0.04085646,0.05292126,-0.02725014,-0.02292477,-0.00989613,0.01192061,0.02267529,-0.01778796,0.20975502,0.0401977,-0.05354419,0.01047805,-0.04993325,0.03038619,-0.05515783,0.00927083,0.08700197,0.02955339,-0.01865556,0.06823806,0.04669708,0.0604536,-0.0151141,0.01018523,-0.01987101,0.04360527,-0.00503406,-0.00573474,0.01976702,-0.06824284,-0.03295489,0.01856079,-0.03739272,-0.23302846,0.07223977,-0.0244238,0.07510313,0.01499415,-0.01677562,0.03277249,-0.01818042,-0.02121275,-0.02273542,0.05881181,-0.01198703,0.02517298,-0.029016,-0.04898446,-0.0122035,0.0765541,-0.04785956,0.0416587,0.02883391,0.04994402,0.0527461,0.23519997,-0.04713814,-0.00307093,-0.01483862,-0.02725285,-0.0034094,-0.05547419,0.01113942,-0.00160535,0.0122008,0.02225265,-0.03834506,0.00430692,0.05592972,0.00983976,-0.00580177,0.03299069,-0.02271593,-0.05461553,-0.00933621,-0.01284896,0.01819365,0.13820693,0.08042162,-0.02721937,-0.05906688,-0.01517517,0.06493723,-0.04311701,-0.05356954,-0.07082991,-0.02199533,-0.0021389,0.03239356,0.00187991,-0.02902363,-0.00962504,-0.04672891,0.01417318,0.03748298,0.03712004,0.06484099,-0.05977507],"last_embed":{"hash":"11vv1gw","tokens":93}}},"text":null,"length":0,"last_read":{"hash":"11vv1gw","at":1753423574638},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{5}","lines":[28,28],"size":267,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08935511,-0.03558628,0.01631252,0.00323563,0.0288961,0.05083154,0.07180997,0.00433114,0.09263442,-0.02678522,-0.026443,-0.01950229,0.04921779,0.01087114,-0.00148302,0.00236683,-0.03634184,0.00573854,-0.09268968,-0.00467453,-0.00155609,0.0026797,-0.0348691,-0.10293701,0.00831211,-0.03929263,-0.02481989,-0.05984149,-0.07249276,-0.19062923,0.02016347,-0.03008955,0.05590009,-0.02290683,-0.03620864,-0.02429984,0.03041029,-0.0006868,-0.0599932,0.06191216,-0.00685615,-0.01323348,-0.0223411,0.00355482,0.05250779,-0.0274235,0.02371271,-0.01765409,-0.04116328,-0.0187472,-0.0182707,-0.0184057,-0.00342436,0.07127362,0.03627476,0.01754433,0.02827824,0.07951357,0.02987294,0.00333778,0.00760352,0.07287535,-0.15256172,0.03769185,0.00736238,0.00694835,-0.03535111,-0.07507204,-0.00833786,0.06455716,0.02287061,0.04112055,0.04367443,0.04314727,0.04046728,0.02366091,0.02408398,-0.0479189,0.00761104,0.01751811,0.04310928,0.01923376,-0.04609433,-0.03686131,-0.01323298,0.04315174,0.01648204,-0.01136205,0.03951219,-0.09513432,-0.01276361,0.0145981,0.00143875,0.05164718,0.0187059,0.01660487,0.02750115,-0.00381948,-0.0517634,0.13343543,-0.03524822,-0.01715252,-0.01480338,-0.00838016,0.03217316,-0.03541782,-0.01159083,-0.04527998,-0.10127513,0.01682512,0.04954902,-0.02226388,0.05488554,0.0062498,-0.01812896,0.00796027,-0.00317174,0.02753995,0.04196532,0.00003706,-0.07378032,0.03967234,0.02253233,-0.00833288,-0.01570838,-0.03382621,-0.0054429,0.1247651,-0.02085481,0.01523746,0.02208151,-0.04849749,-0.08786108,0.00586895,0.04601816,-0.00310534,0.006484,-0.05340286,-0.03666689,0.04371738,-0.04565452,-0.00012539,0.0281119,-0.08879202,-0.08175391,0.07113066,0.0544699,-0.02350897,0.04615771,-0.04093483,-0.00708522,0.01838494,-0.03121911,-0.07012595,-0.03352897,0.01942418,0.0860355,0.03124439,-0.04623175,0.01754824,-0.00047567,-0.02401038,-0.07479391,0.14241259,-0.01172929,-0.06009507,-0.02370885,0.03607862,0.00391234,-0.03899506,0.03859923,-0.02123128,-0.02545825,0.02995634,0.02440497,-0.02048043,-0.07920045,-0.01999223,-0.10540248,0.01616648,0.00571014,-0.06704832,-0.04521754,0.0375375,-0.00296515,-0.05681598,0.0200267,-0.05996564,0.02816867,0.03448753,-0.02613388,0.05683742,-0.03307007,0.03953972,-0.0008758,-0.02576581,-0.01234548,0.00405277,-0.01104853,-0.03934341,-0.11796229,-0.05021657,0.01860926,-0.01154414,-0.01470848,0.01373066,0.00910975,-0.04752605,0.07375832,0.02821005,-0.01330172,-0.04974294,0.08705592,0.00600139,-0.06326411,0.03568483,0.02999664,0.02790739,0.0037138,0.0409798,0.04357432,0.02718428,-0.12291194,-0.17736068,-0.04308857,-0.05025997,-0.02927363,0.07093785,-0.03158409,0.0226152,0.02135212,0.03535219,0.09485674,0.0726734,-0.02909951,0.03271259,0.01585881,-0.01165593,0.01746039,-0.0315071,0.02141273,-0.05831735,0.03243805,-0.00997854,0.07574105,0.00449893,-0.09049886,-0.0235154,0.02244073,0.15870923,-0.00559506,-0.00210579,0.03329938,0.01017613,0.03851448,-0.01361408,-0.13107698,0.05062921,0.01649039,0.01711821,-0.025106,-0.06249383,-0.03361657,-0.06803533,0.0341776,-0.00705653,-0.0699463,-0.06243841,-0.00022631,0.03719755,-0.01498491,-0.02176313,0.04045039,0.01905849,-0.03079001,0.02864147,0.01610896,0.06992353,-0.04232503,-0.07559715,0.03793765,-0.04015646,0.12077382,0.06414263,-0.09149855,-0.0207665,-0.01652018,0.05219635,0.01132192,-0.01258154,-0.01253901,0.00294156,0.0159886,-0.01919505,0.1285315,0.05159285,0.02264536,0.04784581,-0.05480391,0.05430083,-0.02571929,0.02987324,0.05785608,0.06850656,-0.06561415,0.06548386,0.05086439,0.05426644,-0.0146139,0.05269893,0.01113391,0.02168666,0.01047693,-0.0244225,-0.00459738,-0.03595055,-0.01652813,0.05284042,0.01196081,-0.21705677,0.09477841,-0.029359,0.02247293,-0.00701519,-0.00360514,0.04529059,-0.00045888,0.04025947,-0.00513495,0.0540227,0.02021841,0.0167036,-0.0376661,-0.02587323,-0.00378512,0.08812339,-0.04693636,0.01314907,0.04201157,0.0379522,0.07185207,0.26165977,-0.03421029,0.02757527,0.01149115,0.01102372,-0.04635034,-0.00571701,-0.02769706,0.00099268,-0.00813487,-0.02596346,-0.06796242,-0.00944539,0.08435398,-0.03840584,0.00513781,0.02263848,0.0175951,-0.03144265,0.00906902,-0.04017746,0.00970402,0.13655983,0.09133977,-0.04089971,-0.08989204,0.02558472,0.04656973,-0.05243552,-0.01218763,-0.04252388,-0.05015063,-0.03265652,0.01031982,-0.00814768,-0.0065002,-0.00090446,-0.03861481,0.04568147,-0.00288262,0.06750061,0.02669426,-0.05893514],"last_embed":{"hash":"yvwm5m","tokens":72}}},"text":null,"length":0,"last_read":{"hash":"yvwm5m","at":1753423574673},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{7}","lines":[30,30],"size":264,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05335365,-0.05928367,0.0178443,0.00584098,0.02921677,0.02145745,0.07730559,-0.00117925,0.04338799,-0.03305295,0.00441798,-0.04102873,-0.00351939,0.03515778,-0.00800102,-0.04071639,-0.00419172,0.00871399,-0.05932321,0.00117742,0.08828079,0.00300792,-0.04802467,-0.10482563,-0.0071502,-0.00377142,0.03945733,-0.01535443,-0.05371828,-0.14432706,0.01033814,0.02385661,0.03725969,0.0069772,-0.02431586,-0.01385806,0.06157643,0.0235018,-0.07573113,0.07192145,0.01232092,-0.03634552,0.00779779,-0.03052372,0.03205783,-0.0212329,-0.00924727,-0.0152761,-0.06365993,-0.04406701,-0.0515289,-0.00130614,0.00049568,0.02313502,0.01169485,0.0226875,0.00968607,0.04108841,0.06143444,-0.01967931,0.02512351,0.01906924,-0.17232493,0.01426872,0.0656955,0.01653254,-0.03315672,-0.08189391,0.00976404,0.03214344,-0.0059687,0.0381387,0.00455681,0.03653035,0.04562157,0.00484728,-0.00794864,-0.06492737,-0.0108667,-0.00375881,-0.00445861,0.02189632,-0.09247868,0.00521715,-0.01606794,0.08024722,-0.00265704,0.01082421,0.07131118,-0.0190011,-0.03136859,0.02122506,-0.00922929,0.04579316,0.00371804,0.04187465,0.04533751,-0.02427204,-0.03239844,0.10299662,-0.01747501,-0.02424246,0.06155469,-0.03587968,0.01841614,-0.02490731,-0.03373928,-0.06680291,-0.03303704,0.01187832,0.01914663,-0.01829221,0.06459944,-0.00496937,-0.01071899,0.01835841,0.02598074,0.03333091,0.03528811,-0.02945933,-0.03825067,-0.00288377,0.02170817,-0.00073633,-0.06267885,-0.00252702,0.04039695,0.10088466,-0.01087185,-0.02562855,0.02694289,-0.00478359,-0.09757333,-0.01605813,0.01063941,0.04591995,0.03191397,-0.01289461,-0.03580672,-0.02327611,-0.0241876,-0.01899974,0.0232252,-0.06458432,-0.04115593,0.08204634,0.03928191,-0.00668464,0.05055214,0.02143852,-0.01139165,0.03325278,-0.0211412,-0.08570264,-0.03499267,-0.03910742,0.0690038,0.06759442,-0.07599711,0.05756332,-0.05121819,-0.01830779,-0.07236687,0.0912208,0.02336554,-0.04780887,-0.0410008,0.03266662,0.02050525,-0.01592106,0.02003023,-0.0015684,-0.02156675,0.02293721,0.02502723,-0.01710263,-0.05702554,-0.01312971,-0.07234751,0.01196407,-0.05625512,-0.10685405,-0.06238971,0.05804251,-0.01637407,-0.08323912,0.04020891,-0.06138108,0.03158875,0.0456432,0.03188088,0.06015997,-0.02311176,0.02162938,-0.03664942,-0.018761,-0.01320031,-0.03909701,0.001779,0.00658212,-0.12403212,-0.02855192,-0.02214961,0.00344026,-0.01712839,0.01715856,-0.00365153,-0.00190649,0.04185063,0.04172289,-0.02239596,-0.0669473,0.05519182,0.07053182,-0.03457166,-0.01623708,-0.02069027,-0.04441714,-0.04403695,-0.00620984,0.0559963,0.00909735,-0.14406241,-0.22468808,0.01970824,-0.04222234,-0.0603563,0.04377405,-0.03010484,0.00425086,0.05207878,0.07338634,0.06447627,0.05199954,-0.02814167,0.04234753,0.02122963,0.01216217,-0.01139621,-0.03198523,0.00100581,-0.04302143,0.03720949,0.01724343,0.05136604,-0.01724345,-0.08560733,-0.05304842,-0.0222501,0.14624324,0.10471941,0.06369223,0.01085118,0.06843626,0.07554792,-0.00869797,-0.11823872,0.0264141,-0.01517371,0.09237644,0.0493694,-0.10824405,-0.04938567,-0.08352979,0.05319946,-0.01269871,-0.03291166,-0.05282164,0.00802223,-0.02933885,-0.02569867,-0.039224,0.02914521,0.02316626,-0.02312903,0.01035503,0.00779048,0.03400166,0.00590418,-0.0330791,0.01083901,-0.00598658,0.1166091,0.02183303,-0.04159977,-0.01057458,0.00246586,0.02031526,0.01292716,-0.00272991,-0.01567386,-0.01719803,0.00786211,-0.00920824,0.14898232,0.04689643,-0.05536604,0.11871561,-0.0472972,0.09948517,-0.00420777,0.00648592,0.0637915,0.04818985,-0.01057387,0.04502581,0.01067557,0.05645751,0.01674235,0.01848591,-0.04790119,0.03809483,-0.00318229,-0.01512943,0.04593864,-0.02439252,0.02018873,0.06826612,0.04996591,-0.2640411,0.12387493,-0.03166352,0.08065076,0.01162959,-0.02141692,0.02169494,0.00445244,0.00816299,-0.05687457,0.01923846,-0.01041208,0.02709712,-0.04710547,-0.0639054,0.01403935,0.11752819,-0.035114,0.08189963,0.01338439,0.03443302,0.049318,0.23262988,-0.01768946,-0.03445408,0.00134485,0.01659311,-0.01029606,-0.03910054,-0.01457253,0.04064429,0.01243719,-0.03030785,-0.04836415,0.0092133,0.06939952,-0.01217251,0.00212464,0.06541841,0.01841881,-0.02872119,-0.02207189,-0.017861,-0.01474704,0.12891307,0.04113109,-0.01645794,-0.02493116,0.00470524,0.02435437,-0.02800485,-0.06152092,-0.03188678,-0.01185185,-0.00408084,-0.01866767,-0.0135012,-0.01865731,-0.00733615,-0.04843608,0.02504895,-0.04187823,0.08044036,0.02392054,-0.01789295],"last_embed":{"hash":"1zeqf6","tokens":86}}},"text":null,"length":0,"last_read":{"hash":"1zeqf6","at":1753423574711},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{9}","lines":[32,33],"size":220,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09902111,-0.03971256,-0.03071035,0.01023947,-0.01611707,0.06049172,0.03966366,0.00462409,0.08637394,-0.01857106,-0.00235435,-0.00575135,0.00673505,0.03287081,-0.00999835,-0.01588876,-0.04827536,-0.01934635,-0.05036808,0.0236843,0.0594315,-0.03140591,-0.03987211,-0.0991731,0.04918496,-0.03682134,0.00033213,-0.05310431,-0.03973972,-0.21118695,0.07825875,0.0107152,-0.00084364,-0.04610693,-0.05593792,-0.0248503,0.02552719,0.03782259,-0.0335649,0.08364139,0.03322346,-0.00139784,0.00475393,-0.0458511,0.04692137,-0.02644201,-0.00711384,0.01175961,-0.04657707,0.00199919,-0.03303462,0.03852739,-0.01062027,0.0275253,0.06920967,0.02636055,0.03302324,0.0869757,0.0039568,0.03086214,0.01985136,0.04958373,-0.20253584,0.03639654,0.00619679,0.01836218,-0.00040293,-0.02744901,0.01006248,0.04556211,0.05857672,0.03427187,0.00358825,0.03170782,0.05165011,0.01120831,-0.00602422,-0.00943399,0.01447378,0.04559878,0.01398598,0.01243526,-0.06944725,-0.00932625,-0.0042822,0.05773687,-0.00072448,0.0265092,0.01057003,-0.07495005,-0.0192122,0.03058107,-0.00029002,0.04203682,0.01539057,0.03321136,0.04532613,0.0114936,-0.02694172,0.10251029,0.01191236,-0.02904494,0.00244404,0.02250129,0.03591302,-0.01919941,-0.07185786,-0.06699948,-0.01662188,0.03999861,0.0029725,-0.01096348,0.06860295,-0.04751078,-0.03362192,0.04066147,-0.00544677,0.03965566,0.05053189,0.00073314,-0.07472233,0.01493045,0.0051688,0.00587708,-0.04934741,-0.02556638,0.00689494,0.10047007,-0.00811052,-0.01432963,0.02341695,-0.00531491,-0.13839225,-0.03850272,-0.00744038,-0.02560409,0.00854822,-0.01959239,-0.03039247,0.02957279,-0.02629034,-0.03619767,0.06356128,-0.07394929,-0.04945129,0.03138267,0.04299407,-0.02613706,0.05095971,0.01834707,-0.01810543,-0.00359643,-0.04716071,-0.07939614,-0.03340173,0.00945184,0.07928679,0.07239768,-0.0700428,-0.00265595,-0.0311576,-0.07516414,-0.06478029,0.1290182,0.0162428,-0.09823008,-0.03102777,0.03060554,0.01380886,-0.06276763,0.01050367,0.01305308,-0.01408396,0.0272081,0.03574277,-0.00842223,-0.07595184,-0.04206802,-0.09328325,0.03342555,-0.03531535,-0.0742367,-0.03308592,0.03444913,-0.05544472,-0.10244326,0.01565171,-0.06837136,-0.00546276,0.03332394,-0.07282642,0.0498417,-0.08590122,0.03423985,-0.03044985,-0.00263642,0.00631587,-0.03654765,0.0677034,-0.01469082,-0.11527517,-0.02133029,0.01155266,0.00033696,-0.04515246,0.01537286,0.01202251,-0.04721704,0.08487447,0.06090735,0.00522114,-0.00908529,0.04282266,0.05158751,-0.04749667,0.03034444,0.00527703,0.00431353,0.01592027,0.00964234,0.0370823,-0.00974865,-0.08393659,-0.16006275,-0.02170576,-0.06270175,-0.04767734,0.03543425,-0.03806096,0.05487602,-0.0028943,0.05021425,0.07754576,0.05565872,-0.01650762,0.03399409,0.05267444,-0.00732039,-0.00176928,-0.06427843,-0.00250111,-0.02877804,0.04149226,-0.00574749,0.03967543,-0.00292441,-0.0903859,-0.00037466,-0.0168124,0.13228719,0.06880231,0.05832989,-0.00904244,0.01874587,0.01886847,0.02028959,-0.0694882,0.02006401,0.00274771,0.04569475,-0.00895818,-0.10785829,-0.01918021,-0.08581733,0.0403287,0.00473615,-0.05802783,-0.08485734,0.01943853,-0.03292475,0.01008193,-0.03447663,0.05332465,0.05200473,-0.0240848,0.03349643,0.02745621,0.05503293,-0.01923966,-0.07631826,0.00259521,-0.00648551,0.09268224,0.01550156,-0.04274768,0.022364,-0.01232611,0.04229674,0.04415708,0.00328428,-0.01703478,0.03076551,-0.0110991,-0.03163448,0.14494321,0.04879365,-0.00042318,0.05715821,-0.02663829,0.05478515,-0.03681853,0.01929273,0.07258937,0.02622399,-0.09956631,0.06372175,0.04166688,0.03781175,-0.00422105,0.05352575,0.02158224,0.03402277,0.0434292,-0.00911203,0.03211476,-0.03089946,0.06000306,0.05008757,-0.00275028,-0.23527846,0.090863,0.01368855,0.06037366,0.01893334,-0.03663865,0.02168244,-0.01093387,0.02315384,-0.05337334,0.07896042,0.01158843,0.05696249,-0.06097003,0.02430455,-0.00920954,-0.00313443,-0.03272933,0.02942372,0.00101504,0.06066898,0.05759799,0.23135111,-0.00744971,0.00153462,-0.01794014,0.01613647,-0.02115004,-0.02645624,0.01103631,0.02785015,-0.003498,-0.00360125,-0.05142815,-0.00226892,0.03481061,-0.01251476,0.00802507,0.01600721,0.002127,-0.08349153,-0.00781901,-0.0111495,0.00423192,0.14705269,0.06772655,-0.01241086,-0.09512959,0.03803901,0.06522318,-0.05515633,-0.08307377,-0.00940768,-0.04683229,-0.01232934,0.02391405,-0.01224203,-0.02954345,-0.01222744,-0.01711342,0.06955296,-0.01795878,0.07586006,-0.01618018,-0.04038567],"last_embed":{"hash":"fpzgtq","tokens":270}}},"text":null,"length":0,"last_read":{"hash":"fpzgtq","at":1753423574768},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{10}","lines":[34,41],"size":977,"outlinks":[{"title":"Neural networking, artificial intelligence originated in super computer HAL of 2001 Space Odyssey.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"_**Jackpot Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software**_","target":"https://saliu.com/lotto-jackpot-lost.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07850986,-0.05365311,-0.04861319,-0.01128282,-0.04838172,0.07042065,-0.00381472,-0.00506655,0.09799907,-0.02309972,-0.00491212,-0.00222026,0.04862412,0.04338654,-0.01570883,-0.01781219,-0.04818616,-0.01180475,-0.04763221,0.0103227,0.08685473,-0.00502896,-0.05916192,-0.08262516,0.07259335,-0.01103838,0.0212208,-0.08176387,-0.03191159,-0.20212504,0.04511562,0.02818683,0.05613127,-0.03179483,-0.0634453,-0.02568358,0.03548954,0.0665947,-0.03820039,0.07990981,0.01268673,-0.00812396,-0.0178988,-0.03907266,0.03804556,-0.00471053,-0.01453676,-0.00352773,-0.01285647,-0.02981677,-0.02140481,0.01283954,0.01070407,0.04420792,0.04805086,0.04747667,0.04588001,0.05262832,0.01530932,0.05147997,-0.0009273,0.06347759,-0.17070779,0.03691358,-0.01587151,0.0215614,0.00225932,-0.01777669,0.03594321,0.08870398,0.06150923,0.03797155,-0.00602601,0.01104152,0.01029196,0.03707441,-0.02048595,-0.02751238,-0.0025612,0.0090605,-0.03535269,-0.00672822,-0.04709996,-0.01534563,0.01132365,0.05072217,0.01166704,0.03007178,0.0057586,-0.06099617,-0.0046182,0.03624864,-0.07029008,0.03210438,0.0136406,0.02300279,0.05696722,-0.02611435,-0.02280373,0.13007496,-0.01131591,-0.01540627,0.00088646,0.0254692,-0.01043673,-0.05404558,-0.05597834,-0.11153266,-0.05630392,-0.0120033,0.04831095,-0.04000308,0.04746597,-0.04179656,-0.0564175,0.03534756,0.02238819,0.04256735,0.07441623,-0.0159966,-0.03120888,0.00347732,0.03309188,0.00357748,-0.0031566,0.0293055,0.02716314,0.10359347,0.00682049,0.04437909,0.03085475,-0.01141651,-0.10666176,-0.047529,-0.00634435,0.00566431,0.01933894,-0.00279085,-0.01663775,0.01872061,-0.00717669,-0.03304226,0.04657472,-0.095895,-0.00879794,0.06613536,0.00922451,-0.02279742,0.03406699,-0.00663476,-0.0086025,-0.01809001,-0.05249186,-0.09977945,-0.01972299,0.01087657,0.08368562,0.02006128,-0.06314449,0.03588689,-0.02550848,-0.07007713,-0.0695814,0.12910664,0.0038391,-0.07893878,-0.03388683,-0.03048833,-0.00007901,-0.08281595,0.00866887,0.0261872,-0.01067405,0.03811076,0.02498732,0.03787147,-0.06770459,-0.03655935,-0.05468383,0.02893402,-0.0335749,-0.0175795,-0.05123412,0.03650405,-0.03727708,-0.06453551,0.05672261,-0.04632664,-0.00578623,0.06604654,-0.01077097,0.04781343,-0.03920401,0.05525762,-0.04134848,-0.04125363,-0.01923545,-0.00548985,0.01492564,-0.01574729,-0.06097675,-0.03015784,0.00269669,-0.00725878,-0.0124445,0.02108899,0.04314669,-0.03460028,0.0826024,0.05361703,0.00718966,-0.03046839,0.04017493,0.0233388,-0.03482505,0.02430784,0.0046333,0.01384733,-0.00193756,0.04578823,0.0472029,-0.00624284,-0.07096142,-0.18787903,-0.02303439,0.00290716,-0.00812609,0.04466452,-0.05238102,0.05278281,-0.00780865,0.0076437,0.09963286,0.05511858,0.00267371,0.01050665,0.03142169,-0.0377228,0.02456702,-0.0544702,-0.0261322,-0.02160831,0.04646811,-0.0276411,0.0771246,-0.04464856,-0.09334344,0.05293057,-0.02097243,0.13294746,0.00497222,-0.00180293,-0.01781627,0.02962161,-0.00771611,-0.0410851,-0.03328992,0.05579386,0.00109912,0.04047634,0.00034343,-0.06527446,-0.03216865,-0.09349205,0.01944608,0.02453431,-0.0760709,-0.08010816,-0.00318543,-0.03586341,-0.00586949,-0.03954887,0.01640222,0.04425617,-0.02291543,0.02596549,0.02330049,0.02539938,-0.00803426,-0.0821298,0.01569817,-0.03176571,0.07531754,0.04004207,-0.0295693,-0.0015507,-0.02778128,-0.00123121,0.04228774,-0.01386079,-0.0105428,0.02374218,0.03278359,0.0073649,0.11544076,0.03840639,0.03400878,0.05020154,-0.03345818,0.02579498,-0.06027743,0.02342167,0.05800512,0.05215058,-0.09934956,0.04612751,0.02120477,0.04545362,0.02123131,0.08528007,0.01830378,0.01719106,0.02048999,0.00561875,0.0245626,-0.01093193,0.04135385,0.08425669,-0.01310772,-0.25697622,0.06145316,-0.06257151,0.10103519,-0.0187489,-0.04178402,0.02159356,0.01124347,0.00310721,-0.04663059,0.05938781,0.01235123,0.05548261,-0.06761025,-0.01239278,-0.02494145,0.00702057,-0.0668148,0.00091208,-0.00558278,0.07731217,0.03342138,0.27372772,-0.02908884,0.02893268,-0.00970595,0.005212,-0.02608043,-0.0215511,0.00182442,0.02257218,-0.01603442,0.03467834,-0.05988711,-0.00976766,0.06594637,-0.00980476,-0.04976211,0.01544195,0.00956943,-0.03454701,0.00575154,-0.04271127,-0.02086119,0.14823157,0.08370122,-0.0319795,-0.04996868,-0.00343096,0.05947656,-0.10757494,-0.05742233,-0.02646058,-0.0579221,-0.00338545,0.03876671,0.00623418,0.0069264,-0.01482781,0.00662381,0.03812233,-0.02491016,0.08749244,0.00118441,-0.05692121],"last_embed":{"hash":"1j361zs","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"1j361zs","at":1753423574874},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{11}","lines":[42,42],"size":352,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06500972,-0.00577955,0.00843058,-0.00494845,-0.00246876,0.0473284,0.06782583,0.01879981,0.08044577,0.00006278,-0.02037056,-0.02061422,-0.00290419,0.03645218,-0.04158016,0.00034244,-0.06137426,-0.02436403,-0.10019674,0.02674912,0.0085408,-0.00270929,-0.071573,-0.10438651,0.04153147,-0.01450695,0.01664518,-0.04403258,-0.0598627,-0.20336378,0.04831779,0.00487709,-0.00209972,-0.058555,-0.06412931,-0.02729504,0.02511392,0.06191185,-0.0239745,0.05615541,0.01714168,0.00416653,-0.01083905,-0.04281799,0.04463428,-0.03627035,-0.03226631,-0.01720355,0.00442517,0.00154548,-0.03574944,0.03211015,0.01626095,0.04675788,0.06075692,0.02271695,0.04878178,0.07968403,0.03051388,0.01120922,0.0046857,0.07755415,-0.20456208,0.05261514,-0.02964017,-0.00277222,-0.04139338,-0.03078255,-0.00856197,0.06657363,0.05321725,0.02585346,0.01549467,0.03604367,0.06890815,0.04465971,-0.03383232,-0.03827742,-0.0114734,0.06069667,0.008896,-0.00480404,-0.0746987,-0.01806705,0.00964125,0.05497368,-0.02435952,0.05880707,0.00442473,-0.05152426,-0.01291797,0.02128117,-0.01299266,0.06944031,-0.00604726,0.03097732,0.0506977,0.01430519,-0.04418389,0.10902671,-0.0100692,-0.04548943,0.02079737,0.0146217,0.00835657,-0.03476382,-0.04561799,-0.05950154,-0.03900622,0.01715199,-0.00622767,-0.01961057,0.07421811,-0.00642104,-0.04350212,0.02579772,0.00235658,0.03835136,0.04815475,0.0083103,-0.04805904,0.02653036,-0.02219708,-0.03320714,-0.03498437,0.00938454,0.06917966,0.10736617,-0.00741119,-0.01267974,0.0453079,0.0098846,-0.11244839,-0.05181488,-0.01403236,0.00027356,-0.01269766,-0.02636822,-0.02549103,0.00789666,0.00682959,-0.02614906,0.03712019,-0.11361158,-0.05452362,0.02949665,0.04163932,-0.00561111,0.05909127,0.02022278,-0.01931048,0.00176097,-0.01421371,-0.07837395,-0.0462457,-0.01517512,0.07697977,0.07435011,-0.07728549,0.01460573,0.00395471,-0.07419998,-0.04646071,0.10443164,0.01277415,-0.07277714,-0.03633369,0.00331511,0.03466178,-0.07953396,0.02770704,-0.00325309,-0.03223659,0.02153322,0.02249904,0.00886583,-0.01996534,-0.04086583,-0.08686175,0.00209294,-0.01943688,-0.05326912,-0.03019774,0.02830755,-0.04086987,-0.06718653,0.0181121,-0.06831373,0.01314773,0.04477959,-0.02191997,0.0744504,-0.07099972,0.05506256,-0.01763,-0.01729873,0.00648098,-0.051929,0.05996744,-0.02853323,-0.08546598,-0.05046558,0.00449535,-0.00438837,0.0079371,0.00779706,0.04996293,-0.03594778,0.0748805,0.07016823,-0.00842831,-0.02945379,0.07114377,0.05353292,-0.02976378,0.02628062,-0.00264698,0.00499832,0.02783313,0.01585759,0.02570753,-0.00001314,-0.09435114,-0.18169543,-0.02415829,-0.04285594,-0.02030316,-0.01799766,-0.01797215,0.04082701,-0.00873028,0.09070801,0.07739332,0.09482618,-0.02991243,0.01236609,0.06632746,-0.00705552,-0.04662518,-0.05571393,0.01763073,-0.01029904,0.0616428,0.00929925,0.02370317,0.01140757,-0.07876274,0.00881178,-0.01712495,0.13782248,0.10750751,0.00082764,-0.06209011,0.0289714,0.04640569,0.02064657,-0.1124607,0.0377524,0.04363865,0.02280573,-0.03526175,-0.05784088,-0.02287671,-0.07397374,0.02824278,-0.02774823,-0.08207014,-0.08136353,-0.03097296,-0.02834971,0.02308837,-0.02292026,0.07012217,0.06683704,-0.01273569,0.05432003,-0.00156261,0.03403478,-0.02373214,-0.05018707,0.00209028,0.00131754,0.125267,0.0265796,-0.07486729,0.01323806,0.00124278,0.02141745,0.02604785,0.01163196,-0.00830623,0.00922145,-0.00047557,0.00732981,0.07724959,0.04981297,-0.0314684,0.07863924,-0.0141333,0.02923544,-0.05009007,0.02979822,0.06849042,0.04395844,-0.10268544,0.04389419,0.02327012,0.06780861,0.03438553,0.05592331,0.02334898,0.01722804,0.02720178,-0.00681959,0.03070266,-0.05030944,0.02353656,0.01990619,-0.00932076,-0.2381053,0.09028569,-0.02013438,0.0237412,0.00024523,-0.01319144,0.04918944,-0.00616643,0.00243282,-0.03242698,0.10716446,0.03758331,0.05363294,-0.05906976,0.01024406,-0.02863386,-0.01993385,-0.00414235,0.01862963,-0.0136849,0.04597384,0.070086,0.23126638,-0.00544526,-0.0085715,0.00284027,0.03011247,-0.02106483,-0.02954382,-0.01492112,0.01807442,0.03025528,-0.01468851,-0.04966649,-0.01457308,0.04576893,-0.02443513,-0.02241936,0.01089757,0.00466989,-0.04960889,-0.02087573,0.00224886,0.02085882,0.11538691,0.07332887,-0.01857842,-0.08128447,0.02028771,0.06446246,-0.06071398,-0.07641822,-0.03902261,-0.08778214,0.00532672,0.03756396,-0.036154,-0.02504809,-0.01743677,-0.00710918,0.01970183,-0.01657486,0.08816895,-0.02237107,-0.04120682],"last_embed":{"hash":"d1g07i","tokens":169}}},"text":null,"length":0,"last_read":{"hash":"d1g07i","at":1753423574923},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{12}","lines":[43,44],"size":593,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07274905,-0.04535969,-0.01519483,-0.01443344,-0.009384,0.07921709,0.01572323,0.00923826,0.09167665,0.00760512,-0.00535443,0.01560807,0.04076642,0.02877466,-0.02584174,-0.0012277,-0.02597165,-0.01157227,-0.07237145,0.00161826,0.06610653,-0.03787521,-0.05373821,-0.08644716,0.04367832,-0.03012568,-0.01981591,-0.03803803,-0.06352624,-0.24664956,0.0509736,0.0202967,0.01828974,-0.04233779,-0.0641615,-0.05766509,-0.00807284,0.05828558,-0.03623116,0.04616764,0.01750853,0.00891315,-0.00772918,0.00331466,0.0623067,-0.03135061,-0.00446461,0.00717783,-0.01725829,0.0014895,-0.02505788,-0.00861846,0.00945215,0.01726085,0.08024095,0.03396085,0.02117578,0.08309027,0.06965961,0.04781963,0.04825928,0.08592942,-0.18349473,0.02434329,0.01030558,0.0199798,-0.02291907,-0.01425887,-0.0090063,0.06525354,0.07718761,0.02130765,0.00557078,0.04656931,0.05932346,0.00180896,-0.02818857,-0.05904888,-0.00759629,0.03860052,-0.00933652,0.00116031,-0.04217128,-0.03254298,-0.01815996,0.04221524,0.01591136,0.03726805,0.0211682,-0.05691033,0.03276378,0.06342674,-0.0012637,0.02178512,-0.01187497,0.02947031,0.02238335,-0.00988059,-0.02655754,0.08583833,-0.00226687,-0.02068021,-0.00980573,-0.00639144,0.01511068,-0.04237011,-0.04081461,-0.05110955,-0.06314985,0.03293628,0.03236572,0.0023949,0.04543525,0.00443726,-0.06178514,0.05009035,0.00731781,0.07147983,-0.00484105,0.00476247,-0.06726864,0.02190887,0.01762662,0.01399012,-0.02669063,0.02443115,0.01256288,0.11430393,0.01346481,0.01788964,0.00238773,-0.00894585,-0.12829633,-0.01343941,-0.00763115,-0.0408329,0.01466312,-0.02233558,-0.02472123,0.03863709,-0.02507806,-0.04953405,0.05794514,-0.10039044,-0.04736132,0.04280278,0.03271374,-0.00609325,0.05150605,-0.00984253,-0.01022614,0.00791464,-0.06261098,-0.07209352,-0.01750974,0.02647696,0.06407388,0.0656143,-0.07611292,0.00878791,-0.07274758,-0.03803621,-0.05238098,0.12375119,-0.02309576,-0.07898283,-0.0461125,-0.00626888,-0.00661594,-0.09625593,-0.01057072,-0.03145786,-0.03402119,0.02214152,0.05849833,-0.01357618,-0.09579282,-0.01964486,-0.04837604,0.00691052,0.00246535,-0.06810195,-0.05160673,0.01074532,-0.02957156,-0.08534726,0.03974836,-0.03978806,0.01734834,0.02695058,-0.04247038,0.06635446,-0.05090436,0.03590443,-0.01955845,-0.02928726,-0.017178,-0.02862648,0.07406515,-0.0349788,-0.08859766,-0.02662593,-0.01415483,0.00474609,-0.02771327,0.02091427,0.01789856,-0.05524796,0.09473237,0.08439674,-0.02292804,-0.04093477,0.0615341,0.04344418,-0.06866928,0.03194588,0.00476571,0.01955434,-0.04317443,0.00861862,0.03315762,0.02189388,-0.06542102,-0.18280344,-0.04457328,-0.02958412,-0.00419863,0.07124297,-0.0566159,0.0271956,-0.02329206,0.05388942,0.08640671,0.06646203,-0.02858186,-0.01211314,0.03231339,-0.00295501,-0.00566337,-0.10970378,-0.02984078,-0.05263208,0.05258849,0.02524667,0.03769495,-0.04664594,-0.0896287,0.01409901,-0.0276168,0.125118,0.04358643,0.03413537,-0.00282796,0.04587954,0.01715742,0.00968954,-0.00900411,0.05223991,-0.01655668,0.03650586,-0.03153802,-0.09126868,-0.0279769,-0.08400268,0.03268183,0.00820211,-0.10991067,-0.05980063,-0.00061989,-0.02552683,0.02586342,-0.03750229,0.05231491,0.06450526,0.01848463,0.0551909,-0.00945208,0.07017385,-0.01956264,-0.05605164,0.00341931,0.01002995,0.09642425,-0.01031951,-0.05484273,0.01259635,-0.01431654,0.05084913,0.02026855,-0.00446929,0.00789334,0.01694326,0.00087488,-0.0221045,0.11333941,0.04763306,0.04273185,0.03739719,-0.00066696,0.05147373,-0.02080966,0.04885106,0.04924144,0.02642339,-0.05020816,0.06622743,0.06213295,0.03266679,0.00105812,0.03721447,0.02364159,0.0275221,0.02761342,-0.01442408,0.03011117,-0.04016557,0.00747393,0.04386689,0.0113919,-0.22823673,0.05420908,-0.01642974,0.08641428,-0.01545496,-0.02726335,0.04807204,0.03326099,0.02921768,-0.01878528,0.03083589,0.05776966,0.04735299,-0.05052369,-0.00403202,-0.04054223,0.00065852,-0.03345869,0.03645731,0.02149518,0.06926288,0.03831711,0.25339264,-0.01674675,0.03053846,-0.00038638,-0.00333889,0.00128934,-0.01523857,-0.01992188,-0.02505407,0.01320455,0.00181907,-0.04458692,-0.01566799,0.00291814,-0.01636661,0.00776996,-0.00498892,-0.01052111,-0.03413371,0.01876085,-0.02235376,-0.00645168,0.15524749,0.02715709,-0.02385904,-0.1066901,0.05984524,0.04098426,-0.06297552,-0.05863565,-0.0347042,-0.07296319,-0.02436368,0.05072398,0.04991106,-0.01673788,0.00057619,-0.01515325,0.02865741,-0.02326657,0.06695315,0.02067696,-0.0608632],"last_embed":{"hash":"3iei16","tokens":426}}},"text":null,"length":0,"last_read":{"hash":"3iei16","at":1753423574987},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{13}","lines":[45,68],"size":3269,"outlinks":[{"title":"_**Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://groups.google.com/forum/#!topic/rec.gambling.lottery/VUB7H9NbNW4","line":7},{"title":"Lottery software helps neural networking, artificial intelligence AI learn patterns from the past.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":11},{"title":"The lotto-6 numbers ranked by frequency regardless of position are part of neural nets.","target":"https://saliu.com/images/neural-nets-lotto-1.gif","line":15},{"title":"The lotto-6 numbers ranked by frequency by position create effective neural networks.","target":"https://saliu.com/images/neural-nets-lottery-2.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{20}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07656483,-0.06629089,-0.03768486,-0.02019951,-0.01878829,0.06837062,0.03056437,-0.00525194,0.06816582,-0.01390902,-0.00497878,-0.0027527,0.03877004,0.02231537,-0.03591679,-0.0357003,-0.01175214,-0.0071013,-0.07189524,0.0097806,0.08110753,-0.01185019,-0.06236288,-0.1031866,0.03660975,-0.01683655,0.01347697,-0.07081063,-0.07611905,-0.2319326,0.02901854,0.00315378,0.04061001,-0.04438264,-0.07540742,-0.05260307,-0.00763898,0.05143224,-0.04299324,0.03850118,0.01789588,0.02164187,-0.01258428,-0.03273962,0.02862185,-0.02363506,-0.01199975,0.01050993,0.0052594,0.02797194,-0.03830415,-0.01739972,0.0180028,0.06579733,0.05712467,0.00465966,0.02042913,0.08310404,0.05329911,0.03749131,0.05469289,0.07510743,-0.17431442,0.03411968,-0.01116883,0.01040003,-0.02154775,-0.0354922,0.00106192,0.03586589,0.05809401,0.04294494,0.01039751,0.04374331,0.03740865,0.00049347,-0.00973778,-0.06615811,-0.00803068,0.02769282,-0.02575523,-0.02549139,-0.03121738,-0.04406573,0.02267257,0.06921656,0.04071319,0.05451044,0.04918649,-0.0364135,0.02606499,0.04581224,-0.00577422,0.02226762,0.03060974,0.01594673,0.03865389,0.00462375,-0.01906219,0.11154117,0.01989158,-0.02171516,-0.03624154,0.01773146,0.0201672,-0.06762004,-0.05474416,-0.04476671,-0.07403925,0.02613608,0.03167515,-0.00478213,0.06908097,-0.04287977,-0.04980043,0.03745461,0.03793692,0.01525068,0.04161357,-0.01076758,-0.03621064,0.02335174,0.0022326,0.00818061,0.01030017,0.03058673,0.0233654,0.10524175,0.02399632,0.03306964,0.05676295,-0.00272714,-0.14932197,-0.01829123,-0.00430025,-0.03782907,0.01466664,0.02696057,-0.02739765,0.00908214,-0.04307279,-0.03413016,0.10266998,-0.09309418,-0.03359837,0.0577432,-0.02151205,0.00343276,0.03790707,-0.01197405,-0.00163342,0.01337232,-0.05860042,-0.07815504,-0.01425103,0.02147118,0.07091521,0.05037524,-0.05013872,-0.02578343,-0.03568049,-0.01876697,-0.03173789,0.09036007,-0.02051135,-0.07733355,-0.03841098,0.04439295,-0.05090997,-0.06754476,0.00351753,0.00098085,-0.04035614,0.03715032,0.05232033,-0.02043178,-0.08271869,-0.02394961,-0.04511282,0.01974062,-0.02649253,-0.01941465,-0.04827767,0.01912212,-0.03436128,-0.07172489,0.0377677,-0.04255653,0.02192473,0.01296037,-0.0025274,0.01381564,-0.05390432,0.06125221,-0.01485305,-0.01683697,-0.02125456,0.00729439,0.05633553,0.01256346,-0.01926721,-0.04622879,0.01104904,-0.01679186,0.00327154,0.03260564,0.00436157,-0.03961902,0.11941275,0.04653374,-0.02331958,-0.05782754,0.03196134,0.05797651,-0.04842487,0.00891542,0.01592842,0.02181906,0.00461782,0.05266859,0.02406553,0.00428001,-0.08351263,-0.20061757,-0.04522513,-0.01003264,0.01139076,0.02740504,-0.0266303,0.03995694,-0.0078465,0.07750928,0.10052864,0.04919758,-0.02400338,-0.04836346,0.02289306,-0.02044528,-0.00933715,-0.10784072,-0.02922287,-0.0641133,0.04119658,0.00114778,0.05719533,-0.04529524,-0.06068599,0.03011187,-0.03338959,0.14003748,0.02772637,-0.00037094,-0.00457091,0.04694082,-0.0029762,0.00936883,-0.01756138,0.05955126,0.00354023,0.02081459,0.00880346,-0.05866691,-0.03722647,-0.09062627,0.02178293,0.00480602,-0.09496004,-0.06450424,-0.00551296,-0.03396096,-0.00651236,-0.0356694,0.03922468,0.09783547,-0.00980387,0.04511547,0.01674559,0.0501034,-0.04021461,-0.06582914,-0.01408384,-0.02281741,0.07227344,0.01672904,-0.07205725,0.02453613,-0.02433329,0.01600226,0.0287541,-0.01976963,0.00813992,0.01224504,-0.01452836,0.0072217,0.0983106,0.02200921,0.03015709,0.07514483,-0.01359148,0.05668934,-0.04502195,0.02137769,0.04223015,0.01936334,-0.05378431,0.04896822,0.04589056,0.04050501,0.01042643,0.0784445,0.05541426,0.03449526,0.03641086,0.01075635,0.03209106,-0.0268569,0.00917127,0.01900242,0.04249014,-0.23471376,0.06706447,-0.02720998,0.05206272,-0.0307812,-0.06476731,0.02590461,-0.01186509,0.02825287,-0.01021895,0.04609024,0.04035488,0.05044112,-0.09523133,-0.0220108,-0.01995356,0.00652054,-0.06691236,0.04911737,-0.01997798,0.0605732,0.04014343,0.28098387,-0.03554728,0.01165526,0.033055,-0.01906686,0.00142046,-0.02740694,0.00275075,0.02478073,-0.01323478,0.04820223,-0.03578831,-0.04447258,0.05315985,-0.00097034,-0.00926027,-0.02087473,-0.02702073,-0.02268508,-0.00533681,-0.05077632,-0.00273832,0.14720057,0.03070458,-0.04199899,-0.05745718,0.03984075,0.07637843,-0.08341327,-0.03379765,-0.04926191,-0.06684063,-0.03763323,0.03317636,0.01185372,-0.00327976,0.00348583,-0.02513826,0.02648115,-0.0068961,0.08870051,0.00137043,-0.04390233],"last_embed":{"hash":"jrkrnr","tokens":445}}},"text":null,"length":0,"last_read":{"hash":"jrkrnr","at":1753423575160},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{20}","lines":[76,150],"size":10435,"outlinks":[{"title":"Checking lottery numbers for winners is the method to validate neural networking.","target":"https://saliu.com/images/neural-networks-lotto-3.gif","line":14},{"title":"Applying positional statistics improves the efficiency of lottery neural networks.","target":"https://saliu.com/images/neural-networks-lottery-4.gif","line":16},{"title":"_**Gail Howard**_","target":"https://saliu.com/bbs/messages/278.html","line":18},{"title":"There is no real artificial intelligence AI, in lottery or elsewhere; that's science fiction.","target":"https://saliu.com/images/artificial-intelligence-lotto-5.gif","line":24},{"title":"_**super lotto wheel**_","target":"https://saliu.com/lottowheel.html","line":26},{"title":"We must test how neural network prediction fares by checking future lottery drawings for winners.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":32},{"title":"Only humans have intelligence and it can be applied successfully to predicting lotto.","target":"https://saliu.com/images/artificial-intelligence-lottery-6.gif","line":38},{"title":"The best term or concept is axiomatic intelligence AxI working in lotto prediction.","target":"https://saliu.com/images/axiomatic-intelligence-lotto-7.gif","line":44},{"title":"The neural nets can group lottery numbers in pairs and also triplets for successful forecasting.","target":"https://saliu.com/images/axiomatic-intelligence-lottery-8.gif","line":50},{"title":"One great neural networking strategy for lottery is playing 12 lotto numbers in wheeling.","target":"https://saliu.com/images/best-neural-networks-9.gif","line":56},{"title":"This is the best real neural net strategy applied to lottery; jackpot within 20 draws.","target":"https://saliu.com/images/lottery-neural-nets-10.gif","line":62},{"title":"Neural networking, artificial intelligence in lottery also works with random combination generating.","target":"https://saliu.com/HLINE.gif","line":68}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{27}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09427057,-0.06223036,-0.03890682,-0.00287132,0.00763019,0.01139968,0.00810215,0.00655527,0.05720714,-0.03700683,0.00767338,0.00688412,0.02712085,0.01468516,-0.011652,-0.00801761,-0.04497612,-0.00987671,-0.04498494,-0.00948549,0.12280374,-0.01876174,-0.05313897,-0.09228576,0.00846861,0.01733785,0.00127444,-0.0160839,-0.05595303,-0.20837703,0.00826505,-0.007919,0.00110036,-0.04842003,-0.02509809,-0.02835554,0.01663176,-0.00921921,-0.0652329,0.07776424,0.00319461,-0.00257972,-0.00446655,-0.01583532,0.03131379,-0.00990888,-0.01184018,-0.04955693,-0.06180611,-0.01215766,-0.0580005,0.01398483,-0.00845165,0.04196502,0.03628691,0.02321044,0.02192969,0.07709257,0.02588002,0.01578886,0.05159986,0.06306219,-0.20417842,0.04368408,-0.00091147,0.01692863,-0.06260845,-0.03333337,0.02597712,0.01732588,0.10061936,0.0518403,-0.01607429,0.0675308,0.03151031,0.01610906,-0.01164682,0.01554791,-0.00720256,0.00154151,-0.00667109,-0.03859655,-0.05265526,-0.00312833,-0.02643815,0.0661103,0.02704132,-0.0067348,0.02434251,-0.02545514,-0.01698257,-0.00419856,0.05704735,0.04020791,0.00760134,0.00773198,0.02806306,0.01635446,-0.05641894,0.10343485,-0.00995933,-0.04277526,-0.03473618,0.00354883,0.03271271,-0.04515159,-0.02333456,-0.04243647,-0.04843488,0.03744138,0.00227128,-0.00441519,0.04157323,-0.01436038,0.00818908,0.0238461,0.05875979,0.07463112,0.02778643,-0.02146179,-0.05195192,0.01582557,0.01977765,0.02609066,-0.00848476,-0.01421826,-0.03175589,0.07638753,0.02693504,0.01388916,0.05510511,0.02256735,-0.08873399,-0.02436547,0.04404993,-0.01441648,-0.02224064,-0.00471681,-0.01902508,0.06307264,-0.01103369,0.00280239,0.01675907,-0.01652289,-0.05230455,0.06665358,0.0037112,-0.02734586,0.03402829,0.04657174,-0.0053384,0.01082268,-0.06934199,-0.06416133,-0.01442766,0.01142065,0.09892757,0.02934304,-0.06967113,-0.00180901,0.01794223,-0.03956875,-0.04816355,0.1406852,-0.02489323,-0.07500336,0.0020012,0.09093273,0.00652094,-0.05001124,0.01384939,-0.01706828,-0.01721899,-0.0029373,0.01949232,-0.04802316,-0.08458222,-0.04776833,-0.05596754,0.03488745,-0.02044974,-0.05933141,-0.02094024,0.02247118,-0.01053362,-0.08964156,-0.00987733,-0.06208429,0.01207542,0.00815729,-0.01548965,0.0795945,-0.07163174,-0.0032821,-0.04672185,-0.007023,0.01833001,0.0129587,0.02989322,-0.00429486,-0.10129678,-0.00267596,0.05499694,-0.00173812,-0.05066459,0.02837293,-0.0097717,-0.0035484,0.08292182,0.05709539,-0.00111843,-0.04817959,0.02909053,0.0370344,-0.06395144,0.05015976,-0.0155009,0.01220644,0.01700504,0.02713893,0.01278442,0.03347849,-0.11031847,-0.18415768,0.0006537,-0.06120594,-0.06638765,0.04540183,-0.03426666,0.02583227,0.0013787,0.04243475,0.08121977,0.06706647,-0.06871088,0.02573568,0.03879359,-0.01908039,-0.01441845,-0.0565926,-0.04319934,-0.04338182,0.0401534,-0.04438939,0.0179074,-0.01977774,-0.09774373,-0.016116,-0.00020064,0.12939052,0.10783165,0.1000379,0.02222962,0.02086288,0.03492822,0.01386641,-0.1819202,0.00023506,0.03626547,-0.00016337,0.02782209,-0.06881431,-0.05645394,-0.05476998,0.04442904,-0.00544817,-0.07223728,-0.04803219,-0.00606303,-0.00973318,-0.03769189,-0.03099894,0.05027339,0.06697747,-0.00725954,0.02175337,0.03540859,0.00549967,-0.0287092,-0.06605846,0.03486561,-0.02085903,0.1249835,0.03176872,-0.02824054,0.04701156,-0.05520134,0.07809732,-0.00563877,-0.02293275,-0.04526741,0.05044805,-0.00802264,-0.00952312,0.16902158,0.04040709,-0.00380502,0.08023012,-0.04489789,0.06343041,-0.01347272,-0.01659462,0.05474031,-0.00741552,-0.0719891,0.08315261,0.04419695,0.03432326,0.03513434,0.05171334,0.01704848,0.02027213,0.04042738,-0.00414764,0.01653635,-0.0411487,-0.00726724,0.06923538,0.00967084,-0.2192104,0.09344404,0.003132,0.06205594,0.02414877,-0.0375707,0.03569009,-0.04315915,-0.01635749,-0.03013099,0.05201574,0.05725798,0.03013734,-0.02600943,-0.00123564,0.01863365,0.07184445,-0.05912955,0.01354245,-0.02746907,0.0207731,0.02937468,0.25213045,-0.0201071,-0.03114886,0.02846074,0.01053802,0.02978642,-0.013065,0.02658027,-0.00418535,-0.02764808,-0.01359919,-0.01612912,0.00474796,0.05453994,-0.01395947,-0.0126695,0.03099399,-0.04726665,-0.04982084,-0.03981811,-0.03134243,0.00511705,0.10726403,0.0527923,-0.03885119,-0.01656651,-0.00956073,0.09597295,-0.03314247,-0.05784418,-0.02396575,-0.03303751,0.0133772,0.00174312,0.0419015,-0.02959932,-0.05450401,-0.0226326,0.03744522,-0.0070554,0.05430464,0.02286131,-0.01593986],"last_embed":{"hash":"1wuua5v","tokens":228}}},"text":null,"length":0,"last_read":{"hash":"1wuua5v","at":1753423575373},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#{27}","lines":[158,161],"size":725,"outlinks":[{"title":"created all those concepts derived from _axiomatic: axio, axios, axiomatics_","target":"https://www.facebook.com/Parpaluck/posts/10156708533469049","line":1},{"title":"There is no artificial intelligence, really: Only humans can make discoveries and program computers.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11364115,-0.05363326,-0.03836467,-0.02067862,-0.0177575,0.06129556,0.01784327,0.01200727,0.0663689,-0.03573846,-0.01506473,-0.00866127,0.05630418,0.02169991,0.02964586,0.00163193,-0.02137502,-0.01679662,-0.04861408,0.00920438,0.02949309,-0.01711161,-0.0087554,-0.09257887,0.02015793,-0.03280206,-0.00642459,-0.06902854,-0.06218478,-0.21345165,0.04764684,0.00775925,0.04879142,-0.05108709,-0.04366564,-0.01289771,0.02964921,0.03817736,-0.03655958,0.06846902,0.0119263,-0.01710213,0.01034441,-0.03130156,0.05732682,0.00066818,0.01986326,-0.02272898,-0.05459259,-0.02398369,-0.04397091,0.00456132,0.00614,0.044837,0.0204688,0.03323709,0.01120964,0.09907183,0.04503699,0.03164316,0.0229807,0.0869249,-0.18443586,0.02902626,-0.0208153,0.06063014,-0.02627468,-0.03922673,0.00200022,0.05260825,0.06984038,0.02239344,0.0139626,0.034058,0.02450465,0.01656314,0.00480958,-0.01063677,0.02344115,0.03687596,-0.01342646,-0.00489635,-0.05596474,-0.00518207,-0.00999903,0.07506686,0.02586272,-0.00352616,0.02046925,-0.05737218,-0.00235189,0.05168111,-0.01151709,0.05601851,0.05548657,0.00671202,0.0547081,-0.01359989,-0.08423335,0.08807694,0.0158812,-0.02165712,0.00636273,-0.00399973,0.02381896,-0.01399771,-0.03485334,-0.05831532,-0.03444554,-0.01322805,0.01381733,-0.01865757,0.07106163,-0.01709823,-0.02891231,0.03013667,0.03062629,0.01437723,0.0123462,-0.00369325,-0.08727897,0.01970685,0.00714965,-0.00203543,-0.03787909,-0.03112748,-0.01608799,0.11025421,-0.01426816,0.03823889,0.02549294,-0.02016038,-0.10589354,-0.02550387,0.01494737,-0.02232601,0.00830043,-0.03620968,-0.03925807,0.02538961,-0.0351875,-0.01931448,0.05336868,-0.08378232,-0.05264018,0.05773136,0.03995141,-0.00405255,0.04115821,-0.00160429,-0.00409252,0.03087109,-0.02542479,-0.08308346,-0.01384773,0.0240026,0.09004787,0.07350522,-0.0654134,-0.00055487,0.00237815,-0.02456819,-0.05519272,0.14115961,0.02994815,-0.05011393,-0.03015966,0.01605256,-0.00013768,-0.0579027,0.01956474,0.01825819,-0.020593,0.01932024,0.01008222,-0.02821848,-0.12499803,-0.04508625,-0.04379294,0.03177556,-0.01261314,-0.08671179,-0.01109827,0.03988654,-0.05214472,-0.10537851,0.02760435,-0.06788431,0.01116053,0.01536763,-0.06426813,0.06359338,-0.06753202,0.01390143,-0.02927086,-0.04476494,-0.01856265,-0.02660404,0.03586921,-0.01606433,-0.12404787,-0.04487364,0.00040909,0.01060187,-0.05353817,0.01969711,0.02206379,-0.03379395,0.12880129,0.0651354,-0.01722141,-0.01508147,0.01932015,0.0296176,-0.05755819,0.04232574,0.01850231,0.02089368,0.00142421,0.02819997,0.02546094,0.04817194,-0.10125902,-0.17628485,-0.03813266,-0.07575741,-0.02438994,0.04257328,-0.07428558,0.05529828,-0.00034025,0.01685015,0.07717657,0.0504088,-0.01435776,0.02721678,0.03011504,-0.01032974,-0.01962326,-0.05307608,-0.00105006,-0.0429666,0.05871602,-0.01556571,0.04103616,-0.00166116,-0.12405945,-0.01030008,-0.01195633,0.14135291,0.04946877,0.0853483,0.04306679,0.03598123,0.04357781,-0.03283151,-0.09090415,0.03476473,0.02767864,0.04150773,-0.0164293,-0.05964784,-0.00489895,-0.08384455,0.05975645,-0.02678112,-0.10345726,-0.0980153,0.00808625,-0.01762694,0.04842427,-0.02474491,0.04531463,0.0388302,-0.02417065,0.03707865,0.02504522,0.04014862,-0.01776091,-0.08690371,0.03924673,-0.0208266,0.10240522,0.0036365,-0.0395801,0.00939099,-0.03370935,0.04533163,0.02719545,-0.00867284,0.01390785,0.00653182,0.02605962,0.00335336,0.15227306,0.01666205,-0.0178061,0.04224695,-0.00607775,0.02871816,-0.05885185,0.00465444,0.0643542,0.03380793,-0.07402637,0.04619826,0.03557247,0.04471258,0.01935752,0.03523834,0.01368399,0.02845072,0.0059921,0.00005052,0.05268294,-0.05842774,0.01587113,0.05416998,0.00902935,-0.20729218,0.06622446,-0.02411587,0.08836398,0.00989524,-0.01498609,0.03929012,-0.04160327,0.01965383,-0.05060535,0.03047522,0.03449722,0.0441691,-0.06779182,-0.02025371,-0.01409977,0.02908508,-0.03968255,0.0049402,0.0219886,0.04154001,0.05938672,0.22246362,-0.01892266,-0.00213081,-0.01534501,0.00115602,0.01721104,-0.0129968,-0.01268438,0.02199921,0.01486755,0.01969947,-0.04019327,0.00577565,0.01205694,-0.02016689,-0.02750849,0.02249028,-0.04196997,-0.035457,-0.02235371,-0.02503457,0.02167759,0.16363874,0.03061282,-0.00275287,-0.07093292,0.03963903,0.08487014,-0.06306157,-0.07296766,-0.02256978,-0.02757269,0.02286921,0.056146,0.01504837,-0.03790981,-0.03449276,-0.03368118,0.04527514,-0.00680662,0.09252191,0.02542397,-0.03952925],"last_embed":{"hash":"ac9ewm","tokens":406}}},"text":null,"length":0,"last_read":{"hash":"ac9ewm","at":1753423575470},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_","lines":[162,195],"size":7276,"outlinks":[{"title":"<u><i><b>Chess: The only case where AI is the closest to real INTELLIGENCE (human-like)</b></i></u>","target":"https://www.facebook.com/Parpaluck/posts/pfbid02MgjsXVjiLWzEpNpWpQoTxkncumNcbDD5CBx8BayzKz3cW9dwSUu5kbrafRkqhTZgl","line":27},{"title":"Neural networking, neural networks, artificial intelligence applied to predicting lottery, lotto.","target":"https://saliu.com/HLINE.gif","line":31}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11033604,-0.05546183,-0.042168,-0.02087173,-0.02196401,0.06252052,0.02491331,0.01098534,0.06626458,-0.03930506,-0.01704848,-0.00775711,0.05733061,0.0234326,0.02860788,0.01042544,-0.0197284,-0.02093979,-0.05484841,0.0127042,0.03572644,-0.01869201,0.00060616,-0.09498826,0.02332906,-0.02986215,-0.01317193,-0.07122672,-0.0582501,-0.2152496,0.04456779,0.00799375,0.04990542,-0.05383811,-0.04232857,-0.00844777,0.02766304,0.03645229,-0.03284066,0.063324,0.01136319,-0.01417094,0.00925238,-0.03115073,0.05360958,-0.00175752,0.02535889,-0.02441004,-0.05706774,-0.02213991,-0.04882913,0.00466715,0.0029231,0.05356534,0.01235094,0.03550312,0.01174855,0.10519654,0.0434,0.03483462,0.02209828,0.09497058,-0.18801054,0.02923236,-0.01557023,0.05524031,-0.02621924,-0.03982965,0.00276696,0.04763756,0.0625015,0.02060977,0.0128443,0.03674779,0.03012045,0.01067316,0.00394837,-0.01700319,0.02372537,0.04278842,-0.0133714,-0.00694297,-0.05319901,-0.00406072,-0.01228801,0.07414176,0.03039935,-0.00375702,0.02134174,-0.05509053,0.00028155,0.04936355,-0.01593861,0.05359703,0.05668918,0.00407341,0.05769444,-0.01794941,-0.08669087,0.08850015,0.01524852,-0.02386435,0.00154766,-0.00199504,0.02281321,-0.01455522,-0.02986264,-0.05162313,-0.03711748,-0.01172586,0.02308801,-0.01729812,0.06787493,-0.01154866,-0.03276684,0.03367964,0.03561725,0.01189064,0.01420506,-0.00216875,-0.09084149,0.02156539,0.01056897,-0.00345599,-0.04128193,-0.03202483,-0.00943965,0.1075455,-0.01345858,0.03091761,0.0235335,-0.02782394,-0.10352845,-0.02885828,0.02104466,-0.02753062,0.00214401,-0.03362953,-0.03304411,0.02871711,-0.02795203,-0.02177153,0.05726647,-0.08104261,-0.05228741,0.05852165,0.03642489,-0.00475916,0.03516173,-0.00411199,-0.00153641,0.02247281,-0.02907887,-0.07458588,-0.01768968,0.01842244,0.08776683,0.07324423,-0.07005553,-0.00075156,0.0009459,-0.01984621,-0.05562522,0.13884002,0.02608828,-0.04807131,-0.02520217,0.01267804,0.00323487,-0.05628845,0.01812585,0.02036428,-0.01899388,0.02130071,0.00999917,-0.02533164,-0.13034695,-0.03927163,-0.04019252,0.02790652,-0.01049854,-0.08310725,-0.0077679,0.0421402,-0.05276924,-0.1068166,0.02822244,-0.07213295,0.00721582,0.01436071,-0.06091923,0.06181134,-0.07180714,0.01398784,-0.02715377,-0.04351965,-0.0237318,-0.02679122,0.034928,-0.01792424,-0.1228531,-0.04687278,0.00385767,0.00982876,-0.04892976,0.02406854,0.02037271,-0.03010334,0.12775545,0.06734737,-0.01328121,-0.01796317,0.01963452,0.0346485,-0.05741644,0.0420614,0.01760686,0.02387818,0.00143499,0.02241272,0.02600943,0.0488999,-0.10000242,-0.17528301,-0.03864636,-0.07460745,-0.02054388,0.04413844,-0.07562926,0.05349036,-0.00250377,0.0134018,0.07248357,0.05089711,-0.01852404,0.01993138,0.03393336,-0.01488984,-0.02013798,-0.05594131,0.00040556,-0.04423701,0.06112551,-0.02240202,0.04282515,-0.01046884,-0.12335353,-0.01557553,-0.01332625,0.14267029,0.04017809,0.07614752,0.04703627,0.03637283,0.03734916,-0.03392528,-0.09116381,0.04007998,0.03374987,0.03712736,-0.01136112,-0.05680991,-0.0011637,-0.08310635,0.05866447,-0.03160661,-0.10010061,-0.10006757,0.00544423,-0.01446976,0.04975509,-0.02139352,0.04387043,0.0377908,-0.01718535,0.03545656,0.02217383,0.04603693,-0.02157228,-0.08337663,0.04358608,-0.01623142,0.10527163,0.0034401,-0.04264917,0.00609299,-0.03121043,0.04695352,0.02908457,-0.00785986,0.01242306,0.00401482,0.02221722,0.00169239,0.15421301,0.00850597,-0.01405043,0.04356926,-0.00546568,0.03069882,-0.05653861,0.0048536,0.06255182,0.03456592,-0.07525934,0.04510631,0.0403142,0.04476042,0.01431207,0.03145098,0.02501917,0.03061082,0.00782355,-0.00038503,0.05086596,-0.05853216,0.01317474,0.04976366,0.00620432,-0.20664367,0.06912117,-0.0307971,0.08905746,-0.00014851,-0.01278006,0.04274325,-0.03735104,0.01830659,-0.03824713,0.03063306,0.03395869,0.04474337,-0.06481782,-0.01913668,-0.01461298,0.03131196,-0.04100108,0.0077419,0.02021126,0.04440697,0.06149279,0.22255044,-0.01900047,-0.00005486,-0.01667837,0.00452316,0.01197692,-0.01563553,-0.01170256,0.01978249,0.01254558,0.01669839,-0.04140037,0.00475702,0.01180206,-0.01658182,-0.02704062,0.0201206,-0.04245901,-0.02945372,-0.02466739,-0.02694611,0.01857394,0.16607992,0.03133158,0.00058629,-0.07212233,0.04314688,0.08260279,-0.06762144,-0.07115658,-0.02259656,-0.03321404,0.02177276,0.05810013,0.01393022,-0.03484653,-0.04095053,-0.03250556,0.04593495,-0.00038872,0.09711111,0.02385019,-0.03567594],"last_embed":{"hash":"1gusogq","tokens":424}}},"text":null,"length":0,"last_read":{"hash":"1gusogq","at":1753423575646},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{1}","lines":[164,181],"size":4312,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06822737,-0.02593954,-0.01629889,-0.01469889,0.02097086,0.03388198,0.03492995,0.00375963,0.04230643,0.00198489,-0.01649562,-0.01359368,0.04331841,0.05717698,-0.00477915,0.00895449,-0.05874037,0.01519736,-0.01918224,0.02414955,0.129594,0.00127865,-0.02369878,-0.09474164,-0.0046546,0.00704346,-0.02093029,-0.0796638,-0.05080841,-0.17909789,0.00896635,0.00250772,0.01888111,-0.01987031,-0.0139273,0.00193744,0.03734845,0.00044834,-0.03827631,0.04012197,-0.00451401,0.00132027,0.01960631,-0.03236584,0.08664109,0.0163163,0.00807344,-0.02840196,-0.07604505,-0.06369092,-0.06446546,0.00016989,0.01010175,0.02104772,0.03923507,0.06041576,0.00385214,0.08718412,0.02726932,0.01426177,0.02435128,0.0623741,-0.19865178,0.03488037,0.02837686,0.05065977,-0.00751137,-0.05536168,0.02723838,0.05783476,0.0334314,0.00014605,-0.01782672,0.00114566,0.03375516,0.0365661,0.00750852,-0.01942543,0.0272051,0.01110342,0.01323765,-0.03571247,-0.04171057,-0.02462239,-0.02918138,0.04717349,-0.00095418,-0.03986971,0.01065755,-0.06445172,-0.00686661,0.00960959,-0.00609534,0.03739219,0.02466511,0.0147074,0.03069597,-0.01825861,-0.09473244,0.10489276,-0.01757149,-0.03682912,-0.01268116,-0.00461743,0.03135272,-0.00709331,-0.00433176,-0.03022002,-0.04754712,-0.03525312,-0.03643547,-0.04354722,0.04383723,-0.00901437,-0.02559382,0.01982864,0.03001226,0.03249964,0.04176671,0.00495637,-0.06437665,0.00347391,0.04834984,0.05570286,-0.00347846,-0.03944602,-0.06278567,0.08741453,-0.03024875,0.01535463,0.08230586,-0.05700352,-0.06429343,-0.02017863,0.04092879,-0.01841176,0.01769269,-0.01092404,-0.04150155,0.00102695,-0.05437534,-0.00222149,0.06117896,-0.06021801,-0.06680481,0.05155763,0.0393502,-0.00596952,0.04388131,0.01558036,-0.03573803,-0.01350987,-0.03318992,-0.07028773,-0.02073543,0.04240349,0.08174263,0.04235695,-0.09121488,-0.01930461,0.00558796,-0.0401419,-0.07337235,0.15107553,0.01179107,0.01700987,-0.05063194,0.01189268,0.01932293,-0.04924075,0.01539969,0.00219923,-0.05614008,0.01246602,-0.00143486,-0.00189604,-0.06798928,-0.03623162,-0.03674643,0.02883429,-0.01542692,-0.09500624,-0.02211715,0.0549244,-0.00451183,-0.04496899,0.02200955,-0.09098673,0.02352666,0.02149468,-0.03792496,0.08241323,-0.04162857,0.00270432,-0.07114441,-0.0278568,0.00848462,-0.00473638,-0.0157536,-0.04043187,-0.09090172,-0.00984598,-0.01656996,0.01471507,-0.06745967,0.01689527,0.03872991,-0.00704204,0.07704689,0.03870095,-0.005988,-0.01918657,-0.00634123,-0.01635042,-0.0317904,-0.00709532,0.02131801,0.02672179,-0.02173209,0.01359579,0.02252644,0.02220045,-0.08227672,-0.19553131,-0.03544049,-0.0288371,-0.07337048,0.05741602,-0.05855745,0.06371812,0.0262316,-0.03390589,0.06174657,0.05617177,-0.0114223,0.00701607,-0.00868885,-0.00332436,-0.00585681,-0.00447404,0.01595605,-0.07122253,0.03161499,-0.01765747,0.04387886,-0.01225992,-0.13654299,-0.02337292,-0.00858607,0.13573734,0.03478403,0.13857713,0.00701056,0.0325658,0.05871361,-0.03735261,-0.10143954,0.05961168,-0.01376189,0.01941915,0.0038995,-0.05440111,-0.05205257,-0.04853886,0.06983311,-0.01208166,-0.06518216,-0.05434839,0.01637544,0.00205463,0.00174839,-0.04218778,0.03079888,0.02939003,-0.01512431,0.02607701,0.04907196,0.02068322,0.01761778,-0.05412738,0.05601101,-0.0288757,0.09440891,0.01934168,-0.0474676,0.01030599,-0.06167282,0.06090515,0.02518618,-0.01199229,0.00085145,0.04727407,0.03987292,-0.01298093,0.18699047,0.06553467,-0.0130929,0.02138249,-0.02171794,0.04692236,-0.05498596,-0.02301176,0.04270155,0.02644881,-0.04061757,0.09055105,0.02696353,0.08550325,0.00422152,0.0527103,-0.01976525,0.05526357,0.0368223,0.00325874,0.05910302,-0.0589717,-0.02215442,0.1064949,-0.01193171,-0.2435531,0.08284538,0.02074415,0.11560339,0.00009003,-0.01534221,0.03972214,-0.04738011,-0.00506414,-0.03392873,0.02474752,-0.00707612,0.0363936,-0.05923541,-0.0164335,0.03023299,0.09449846,-0.04644326,0.03547194,0.01912233,0.00306611,0.1104401,0.19766465,-0.05363324,0.01572509,-0.01294578,-0.02578208,-0.02973175,0.04280689,0.01150898,0.04411273,0.00375072,0.03905987,-0.05062975,0.01689375,0.06766495,-0.02469417,0.02950575,0.03539967,-0.0399842,-0.03699235,-0.03033462,-0.00949837,0.03024159,0.1274316,0.04563955,-0.00615069,-0.07056258,-0.00390899,0.04252732,-0.0241434,-0.04059599,-0.02523858,-0.00880445,-0.01084852,0.03688293,-0.00004519,-0.06520059,-0.03411769,-0.0518455,0.05517142,-0.02727745,0.08367994,0.03371102,-0.05003268],"last_embed":{"hash":"1idco37","tokens":120}}},"text":null,"length":0,"last_read":{"hash":"1idco37","at":1753423575825},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{2}","lines":[182,182],"size":366,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05012102,-0.03461069,0.00323335,-0.01440052,0.00113429,0.03612424,0.01824092,0.00878752,0.0782005,-0.03235091,-0.00337483,-0.03184167,0.02396272,0.03995834,-0.00691658,-0.02172286,-0.03482725,0.01340414,-0.02280867,-0.00233956,0.10144006,0.03101452,-0.0330442,-0.09303109,0.01680026,0.01128333,-0.01227079,-0.08455834,-0.04877631,-0.17085378,0.01098415,0.00276118,0.05936585,-0.03932349,-0.03948512,-0.0183976,0.03910578,0.02797561,-0.06109738,0.06425671,0.00268786,0.01580997,0.00824768,-0.05314492,0.07096379,-0.00946995,-0.01079091,-0.02216824,-0.07983368,-0.05056408,-0.04475849,0.01469234,-0.01035497,0.06919111,0.01641561,0.06895116,-0.00112402,0.09176371,0.00825071,0.00536462,0.04885383,0.05592447,-0.20457409,0.02046097,-0.01265929,0.02933766,-0.03469899,-0.07242301,0.05187064,0.08033335,0.03917998,0.00052185,0.01770968,0.03844298,0.01643985,0.017826,0.02985088,-0.0402759,-0.0001657,0.03935377,0.00899691,-0.02989756,-0.05584203,-0.01009752,-0.03350084,0.06110512,0.04034366,-0.01894522,0.03277608,-0.06045629,0.00286232,0.01714144,-0.03070677,0.04189466,0.02121903,0.01637148,0.04216536,-0.0239383,-0.03937854,0.11813406,-0.03012585,-0.02230815,-0.0132381,-0.02219113,0.04266937,-0.00868187,-0.00455996,-0.03152961,-0.10217208,-0.02628853,-0.00071853,-0.05171371,0.04280429,-0.01188767,-0.01409553,-0.0204787,0.03458501,0.03546252,0.04488309,-0.0141891,-0.03458633,0.00470885,0.04319332,0.03469073,-0.02685424,0.00215994,-0.00235799,0.08146963,-0.03552648,-0.00130592,0.06227836,0.00948738,-0.09702519,-0.0213195,0.04650376,-0.01147187,0.01358069,-0.02598365,-0.06129422,0.02578973,-0.05689416,0.01552556,0.06507365,-0.08909795,-0.05990217,0.08846417,-0.00010188,-0.00352181,0.01405343,-0.01511895,-0.03724764,0.0388251,-0.02458435,-0.08393943,0.00219165,0.01395992,0.06668098,0.00703355,-0.10398485,0.0075968,-0.01723205,-0.00662262,-0.08618291,0.15028399,-0.01848057,-0.0230488,-0.01722174,0.047549,-0.01254237,-0.03271846,0.01576717,0.02557774,-0.02564843,0.00299576,-0.01196911,0.0096549,-0.07554051,-0.04066074,-0.03624743,-0.01465151,-0.01920091,-0.07781206,-0.05207108,0.04632308,-0.00413623,-0.04832173,0.03378097,-0.07804512,0.00506417,0.00564732,-0.04257606,0.08095717,-0.0110677,0.0067539,-0.05539203,-0.02081568,-0.01018566,-0.01325252,0.03148042,-0.03944071,-0.08987052,-0.00982025,0.03045701,-0.00491699,-0.01537302,0.01228067,-0.00342958,0.01239552,0.0495253,0.06226224,-0.0284216,-0.0308296,0.00226023,-0.01843528,-0.03931969,-0.01983359,0.0075179,0.03732719,-0.02422624,0.04282157,0.03197171,0.04608824,-0.09568536,-0.17519917,-0.02086541,-0.02249948,-0.02710982,0.05642478,-0.08307938,0.02248342,0.03367601,-0.01112816,0.05380984,0.08369471,-0.00216902,0.00072369,0.01198373,-0.00460995,-0.01334281,-0.01781463,-0.00040556,-0.09586037,0.04896788,0.01515862,0.02657617,0.00913246,-0.10793816,-0.05437504,0.00049978,0.10643589,0.02028597,0.11425571,0.03764519,0.02620376,0.02495235,-0.07068674,-0.09084538,0.06278171,-0.00321909,0.04462425,0.02990015,-0.04301576,-0.00949532,-0.0449151,0.10489103,0.00140933,-0.06638304,-0.03884458,0.02560965,-0.00361107,-0.0033386,-0.02480428,0.01975702,0.03467537,-0.00341186,0.01650637,0.04573406,0.04284387,0.0074997,-0.0449054,0.00543838,-0.03128286,0.06148408,0.02279826,-0.04874835,0.01796558,-0.02581319,0.03814283,0.0126758,-0.01545297,-0.02919862,0.00395921,0.00146,-0.0376154,0.18735585,0.0163396,-0.01648077,0.0512502,-0.01851349,0.06408483,-0.06101723,0.00207407,0.05860141,0.00471439,-0.08240986,0.10285577,0.03346545,0.08887511,0.01491978,0.0663833,-0.02249226,0.0489769,0.05336044,0.02162097,0.01247149,-0.03656488,-0.01178746,0.06031428,0.01565687,-0.25997648,0.07029098,-0.01038147,0.10928513,0.00803163,-0.01014745,0.03840418,-0.04100303,-0.01017079,0.00449989,0.03692016,0.00778832,0.03885085,-0.07364866,-0.04347891,-0.00679466,0.11484525,-0.06203968,0.02823848,0.03919991,-0.01777182,0.07609048,0.21011364,-0.07362521,0.04203878,0.01205605,-0.0235748,-0.04995471,0.03455203,0.01033602,0.04345663,-0.01485725,0.04352656,-0.04582676,-0.00966733,0.08481163,-0.00642489,0.01607476,0.07558157,-0.033481,-0.01729552,-0.01725145,-0.01930639,0.03200509,0.11571339,0.04118228,-0.03221863,-0.06576595,-0.00031558,0.05897729,-0.06748778,-0.04707571,-0.07157858,-0.03509763,0.00268373,0.02107504,0.02356733,-0.03986674,-0.06914996,-0.02484785,0.05526761,-0.02097547,0.0847501,0.0133018,-0.03061072],"last_embed":{"hash":"kfwadn","tokens":106}}},"text":null,"length":0,"last_read":{"hash":"kfwadn","at":1753423575872},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{3}","lines":[183,183],"size":307,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0592555,-0.03662499,0.00101612,-0.018651,0.01957274,-0.01043903,0.07107953,-0.01199133,0.04423707,-0.00887692,0.00991294,-0.03267903,0.04734534,0.0140672,0.00096392,-0.02707914,0.00881392,0.00279886,-0.01178474,0.01959634,0.11225393,0.01511225,0.00239523,-0.08089001,-0.00042304,0.00411504,-0.02214375,-0.08576893,-0.05050328,-0.17864363,0.03368346,-0.01370879,0.08264355,-0.01990923,-0.00590741,0.0029209,0.03852075,-0.03796228,-0.06933705,0.05151193,0.00258295,0.0035231,0.01566066,0.01755902,0.05098322,0.01492453,-0.00952259,-0.00079901,-0.07937984,-0.01639103,-0.06404775,0.03807257,0.00503005,0.01383937,0.0350609,0.0231802,0.05928263,0.09023692,0.01670612,0.00571951,0.09800714,0.07747701,-0.16535817,0.01050319,-0.00891663,0.02318343,-0.02771949,-0.10921996,-0.02592988,0.04492136,0.01412639,-0.02804968,-0.03223223,0.03210265,0.0357803,0.01205318,-0.00985063,-0.02486504,-0.00247355,0.02268516,0.05126689,-0.02029437,-0.05153233,-0.02414401,-0.04325588,0.04630431,-0.00928341,0.02345838,0.05545231,-0.0864069,-0.02037691,0.01004338,0.01109757,0.05953242,0.06443138,-0.0040322,0.04664312,-0.00444499,-0.06683718,0.07919215,-0.00109455,-0.04281078,0.00028792,-0.01310907,0.04519361,-0.04925229,-0.04024477,-0.03203889,-0.0471446,-0.02461635,-0.04513901,-0.00787108,0.08136248,-0.01958473,0.01929154,0.00215199,0.00799804,0.01823809,0.03701431,0.02384753,-0.04437516,0.01279165,0.00272422,0.04131409,-0.02778104,-0.03399623,-0.012522,0.07760536,-0.02960712,0.00689717,0.04288108,0.00759046,-0.0889098,0.02194925,0.05809268,-0.02055719,0.01319019,-0.01017177,-0.01595366,-0.01951591,-0.03934047,-0.01654322,0.03175243,-0.03813303,-0.08344845,0.07039373,0.00448833,-0.01756173,0.02671366,0.02423013,-0.01072834,0.02097517,-0.04364233,-0.05926635,-0.02699654,0.01307945,0.10363758,0.04865249,-0.09164775,-0.04603321,0.00863007,0.0018066,-0.07906684,0.10162438,0.02018816,-0.04899457,-0.06075432,0.00664708,0.00907087,-0.0385546,0.02774535,-0.01427707,-0.04925843,-0.00958806,0.01358938,-0.0186708,-0.03193857,-0.0312698,-0.05123885,-0.00596582,0.00965718,-0.07506988,-0.03633922,0.03859396,-0.03706717,-0.09044142,0.01469251,-0.07557799,0.02614988,0.04964305,-0.03885529,0.0648171,-0.03889485,-0.00356732,-0.02315585,-0.035393,-0.02509679,0.00523911,0.04463596,-0.05382365,-0.05975995,-0.00010466,-0.02632317,0.00655811,-0.02744716,0.03724938,0.00919678,-0.02683038,0.04101902,0.0363343,-0.01879942,-0.02219364,0.02321773,0.03645229,-0.03691382,-0.03296955,0.00255625,0.01723795,0.00324986,0.00442413,0.03958769,0.02206826,-0.08806413,-0.21377057,-0.0205988,-0.05154305,-0.02742278,0.03008762,-0.02205279,0.03598278,0.02287243,0.01178178,0.05425124,0.04255293,0.0001842,0.00269694,-0.00505502,-0.00184935,0.06080533,-0.03456465,0.04232319,-0.07013843,0.01184271,-0.00536343,0.0716256,-0.01748084,-0.10213967,-0.04876887,-0.00511741,0.1410924,0.06960212,0.12892459,0.03602561,0.04076714,0.00043583,-0.00714131,-0.09667779,0.05189605,-0.03110519,0.0543534,0.00445114,-0.07687346,-0.02704367,-0.11892843,0.02379089,-0.00721184,-0.08435089,0.00376832,0.0203214,-0.02591641,-0.02991662,-0.03039606,0.09123836,0.05055274,-0.0049643,0.02872414,0.08937212,0.05978163,0.0157957,-0.05882525,0.02050032,-0.03212921,0.08754492,0.0109995,-0.06315827,-0.00761548,-0.02686367,0.044666,0.00078583,0.00565185,0.00792952,0.02546081,-0.00476181,0.00447279,0.1952334,0.02962589,-0.02383419,0.065953,-0.016621,0.06153838,-0.0916822,-0.02788108,0.04597192,0.01247772,-0.03620384,0.09078346,-0.00048057,0.08375986,0.03898377,0.05080676,-0.00738198,0.05112357,0.02459475,0.00100617,0.053991,-0.04320327,-0.03657136,0.05424074,0.002142,-0.27817759,0.07637305,-0.02764041,0.08995291,0.000753,-0.02056931,0.01007132,-0.01463475,0.03415075,-0.03827261,0.00413617,0.00844183,0.01270581,-0.05687937,-0.0408687,0.00155671,0.0855247,-0.0141319,0.01139296,0.05214056,-0.00586348,0.00689076,0.22493972,-0.03172961,-0.02174498,0.02307569,0.0109742,-0.03618101,0.03523187,0.01434758,-0.00428593,-0.01600382,0.05305038,-0.03414144,0.02547959,0.03666129,-0.00646236,0.03133787,0.06020881,-0.04057062,-0.04160229,-0.01763244,0.00057312,0.04481473,0.14962119,0.04254204,-0.03770746,-0.07160991,0.00374905,0.06509767,-0.06114737,-0.05526884,-0.06803476,0.03188152,-0.00487874,0.03497966,0.00182175,-0.02637853,-0.02603085,-0.0557795,0.0326067,-0.01205155,0.06985888,0.06015355,-0.0433366],"last_embed":{"hash":"ajp4o5","tokens":199}}},"text":null,"length":0,"last_read":{"hash":"ajp4o5","at":1753423575925},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{4}","lines":[184,184],"size":572,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06927586,-0.01988054,-0.02533332,-0.02291245,0.00686731,0.02736147,0.01317986,-0.00028628,0.05055305,-0.02725849,-0.00424542,0.01758895,0.03776025,0.03918996,0.01945398,-0.00714768,-0.03730208,0.02301543,-0.04147447,-0.00569366,0.1113077,0.02276745,-0.0053006,-0.07778087,-0.01141472,0.0277819,-0.05002888,-0.1032803,-0.05996786,-0.17554803,0.02188508,0.01894311,0.05606637,-0.0275509,-0.00982916,-0.00410056,0.05078676,0.00842618,-0.04759653,0.05645795,0.01283968,0.00370679,0.00923137,-0.04397356,0.05840398,-0.00549765,0.01256164,-0.02177076,-0.02664029,-0.05941416,-0.05181383,-0.01602576,-0.0197567,0.01180191,0.02062982,0.02846706,0.02248344,0.07700476,0.0412132,0.02168862,0.04702085,0.03444616,-0.1756296,0.05742354,0.037909,0.05288945,-0.02427219,-0.07068725,0.05837383,0.03404157,0.02171155,0.00006491,-0.00184929,0.03139063,0.03724582,0.01090439,0.02728705,-0.02178919,0.00116902,-0.00259929,-0.00358174,-0.02140499,-0.03729719,0.00746174,-0.03346184,0.07972187,0.03450253,0.00896654,0.01464169,-0.06517731,-0.02077496,0.00896471,-0.00639011,0.03285593,0.0287328,0.00190954,0.04103417,-0.00870979,-0.06245555,0.10823824,-0.01841932,-0.04129324,0.00552799,-0.00666093,0.04736051,0.01233646,0.0087893,-0.04511218,-0.02947147,-0.0048785,-0.00463208,-0.01992686,0.04116247,-0.00622226,-0.00885175,0.02592147,0.05229698,0.0179686,0.0074584,-0.00887127,-0.03266968,0.02609791,0.02260473,0.00833932,-0.01529418,-0.05067804,-0.01886474,0.07841062,-0.03171479,0.00242733,0.07047856,-0.02818692,-0.080155,-0.00942074,0.05023899,-0.00892906,-0.00344404,-0.02329982,-0.05304305,0.01553922,-0.04184062,0.02430014,0.04485682,-0.06658275,-0.0893034,0.02852483,0.03190944,-0.01976449,0.04300801,0.01292012,-0.01638375,0.00763831,-0.02336705,-0.07433974,-0.01133125,0.01147287,0.11209223,0.0379924,-0.10045145,0.00749536,-0.00426128,-0.01504287,-0.07400718,0.13873178,0.01312963,-0.00626452,-0.02737614,0.04736678,0.02579278,-0.05638258,-0.00707506,-0.00798809,-0.04581435,-0.00261524,0.01768649,-0.00154613,-0.0878407,-0.0525551,-0.00544776,0.02283422,-0.01341541,-0.09657346,-0.02187131,0.05873957,-0.03112091,-0.06826608,0.01397519,-0.08785159,0.02709298,0.01475553,-0.01763022,0.04643175,-0.04708306,0.00958295,-0.03853324,-0.04153707,-0.00610369,-0.03148514,0.00509907,-0.03431606,-0.1198362,-0.02359078,-0.00773814,-0.0010129,-0.03789507,0.02596459,0.01856565,-0.0134401,0.09105381,0.03552246,-0.0280193,-0.02533219,0.01771889,0.00776033,-0.04184686,-0.02180288,0.01612827,0.04055252,-0.03071718,0.0428425,0.0284539,0.03037869,-0.09225281,-0.18085474,-0.00877648,-0.00109016,-0.07674417,0.04850467,-0.05909475,0.03096962,0.0207279,0.01617297,0.04645164,0.07877004,-0.02317196,0.02781049,0.00646291,-0.01226981,0.00746939,-0.00089348,0.0179749,-0.07728009,0.04074207,-0.01399146,0.02057627,-0.030934,-0.13214868,-0.03074582,-0.02104651,0.13337986,0.05402593,0.16657276,0.02257794,0.01463556,0.0532909,-0.04815201,-0.1164472,0.05276159,0.00026895,0.0629648,0.01958145,-0.05977342,-0.03271893,-0.08122796,0.08103577,-0.03736459,-0.07258007,-0.07067958,-0.00017552,0.01668329,0.00200221,-0.01768434,0.0186762,0.0276179,-0.01942615,0.01893971,0.05303593,-0.00866441,0.00066094,-0.05672713,0.02865605,-0.03751272,0.10404909,0.024713,-0.02073244,-0.01961545,-0.01600642,0.05681582,0.00943971,-0.03006744,0.01451564,0.05708206,0.00448356,-0.03941585,0.17833187,0.04539765,-0.00716858,0.03109321,-0.00943447,0.09350307,-0.0556533,-0.01650065,0.04522385,0.01377331,-0.05389246,0.09658558,0.01583195,0.06936702,0.00197299,0.04138063,-0.03241182,0.03852428,0.01700409,-0.0055249,0.05530227,-0.05497427,-0.04167286,0.05932032,-0.00322006,-0.24624373,0.07126562,-0.00069371,0.11721942,-0.02889092,-0.0252567,0.03359323,-0.03707744,-0.01132829,-0.01078429,0.03114176,-0.02883385,0.02125974,-0.09151966,-0.0242591,0.02603705,0.11058741,-0.03734507,0.0354166,0.04996492,-0.00627695,0.08586404,0.21961999,-0.04470759,0.02224495,0.00447166,-0.03288713,-0.02787091,0.0543967,0.0228583,0.05706244,-0.00380736,0.00205572,-0.03350705,-0.01293717,0.05013482,0.00866783,0.01167863,0.05848627,-0.02218981,-0.01357984,-0.05649171,-0.02325105,0.02244474,0.12324665,0.04056562,-0.00058001,-0.07511429,0.02119933,0.04146624,-0.0571552,-0.05511314,-0.05887948,-0.03026044,-0.02926436,0.05791891,0.00913778,-0.05226666,-0.05440245,-0.05413169,0.03076886,-0.03655888,0.11383297,0.01425821,-0.02044216],"last_embed":{"hash":"h2sg79","tokens":121}}},"text":null,"length":0,"last_read":{"hash":"h2sg79","at":1753423576006},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{5}","lines":[185,185],"size":375,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0479399,-0.00554465,-0.01219043,-0.0091833,0.00304705,0.01672719,-0.01490197,0.03050078,0.04355344,-0.04072332,0.01261036,-0.02923269,0.02012992,-0.00012175,0.01590372,0.00053896,-0.02627266,0.00778562,-0.09269591,0.01978149,0.08603185,0.0113898,-0.02108202,-0.08594349,-0.00383119,-0.00277382,-0.01310116,-0.05100837,-0.07270247,-0.15175831,-0.00521016,0.00405664,0.06116769,0.00289535,-0.01338358,-0.01167714,0.05370148,0.00306574,-0.03627066,0.08425303,0.02306589,-0.01484301,0.02694038,-0.02953938,0.00792309,0.00058362,0.00041386,-0.01190373,-0.0706287,-0.0521609,-0.05519906,0.00332386,-0.02977849,0.04070706,-0.02347996,0.02177487,-0.00140731,0.03166045,0.05807822,-0.03228748,0.02372951,0.05185775,-0.18578024,0.00160486,0.09419227,-0.00045399,-0.0607972,-0.08436217,0.00019461,0.02789001,-0.04892808,0.04402526,0.03797779,0.04002978,0.02617982,0.02934922,0.01630449,-0.06140419,0.0016236,0.00240129,0.01716467,0.02239575,-0.06748702,0.01813648,-0.01837584,0.11530988,0.0215082,0.00722217,0.03026936,-0.00493661,-0.04564987,-0.01378942,-0.01406284,0.04571958,0.00658664,-0.0042555,0.02178243,-0.04164539,-0.01048034,0.06208006,-0.01502836,-0.02803655,0.0523565,-0.02600661,0.03230425,-0.03272822,0.0071944,-0.03074292,-0.0263239,0.00890701,0.02436872,-0.02042032,0.07440647,0.00340456,0.02482919,0.04757588,0.05126381,0.00614022,0.05260299,-0.01360006,-0.01683387,0.01339742,0.05665908,-0.06217935,-0.08187518,-0.04207766,0.05018415,0.07323157,-0.0109792,-0.04257328,0.04436267,-0.01827598,-0.06903657,-0.0073525,0.00316477,0.01229172,0.0067995,-0.020565,-0.02751385,-0.03930021,-0.01124696,-0.01842375,-0.01502832,-0.09327373,-0.04386837,0.0242266,0.0554431,0.00405132,0.03854376,0.0470961,-0.00919017,0.0368699,-0.02516475,-0.05530016,-0.05825753,-0.00761429,0.09513444,0.08585352,-0.06424712,0.03683034,-0.01033519,-0.00575112,-0.06701272,0.12300727,0.03044805,-0.05331669,-0.01160266,0.05666122,0.01587342,0.01211197,0.01829948,-0.05815911,-0.01180138,0.01990979,0.02325223,-0.00735567,-0.03605229,0.00469503,-0.04844299,0.02450057,-0.04722736,-0.08427573,-0.0652889,0.03405416,-0.01579234,-0.09090784,0.01072881,-0.05697735,0.0465848,0.04607024,-0.00019428,0.06207472,-0.0311633,0.00467924,-0.01627761,-0.00310141,-0.02839605,-0.03003243,0.00099086,-0.01367875,-0.12756893,-0.03162035,-0.03659014,-0.00898121,-0.06191979,0.03092693,0.02437161,0.02873113,0.07139566,0.03851373,-0.01815569,-0.07530961,0.03576317,0.08790296,-0.02872089,-0.01469248,-0.00505959,-0.03367568,-0.01486188,0.00733611,0.05094762,-0.01602164,-0.12376765,-0.21246812,0.02194067,-0.05682731,-0.05135065,0.03154146,-0.04363672,0.02984706,0.05512802,0.07697893,0.03646148,0.0580852,-0.00035356,0.02017077,0.02046345,0.00447631,-0.00124541,-0.02248088,-0.01431885,-0.06620541,0.04741171,-0.00580117,0.03836013,-0.0206881,-0.09631454,-0.08071267,-0.05773494,0.17667167,0.12419302,0.10947586,0.02071217,0.048411,0.12720601,-0.00199651,-0.13363132,0.0479336,-0.007948,0.10149795,0.05102737,-0.10065631,-0.04461079,-0.05587646,0.05187635,-0.0223389,-0.04974468,-0.03362392,-0.02838448,-0.02637008,-0.02548615,-0.01819051,0.0090744,0.04324276,-0.04639052,-0.00783226,-0.00007381,0.00743093,0.00767,-0.05109403,0.01262892,-0.01303991,0.11818691,0.01592194,0.00635333,0.00429317,0.02727077,0.0154866,0.02821006,-0.03737941,-0.00704974,0.00849727,-0.01262805,-0.02055381,0.12926078,0.00785972,-0.03691113,0.09619309,-0.00591488,0.08363384,-0.0012346,-0.00911511,0.03713453,0.05034937,-0.04354295,0.03484806,-0.00781547,0.0784859,-0.01819913,0.00831954,-0.06420939,-0.01001331,-0.03136244,-0.00885548,0.02368921,-0.06318411,0.00709093,0.06307137,0.00863749,-0.26334843,0.07516798,-0.02870166,0.08149061,-0.01178279,-0.00141965,0.00994078,0.01676594,0.03066403,-0.01774969,0.04782657,-0.00568974,0.04343409,-0.02334763,-0.02783237,0.02671568,0.07866446,-0.03571878,0.04785111,0.04616675,0.01771002,0.04637066,0.18331267,-0.02260706,-0.05565625,0.01635248,0.02978047,0.00577612,-0.04730607,0.00330486,0.03124247,0.0132722,-0.02210508,-0.09222047,0.00485159,0.05693934,0.01968382,0.02574569,0.06649825,0.02978459,-0.03795844,-0.0344463,0.01758664,0.0102784,0.16111632,0.04488875,-0.00976048,-0.04911225,0.0237144,0.0182401,-0.0996676,-0.03099081,-0.01255606,0.02170853,-0.02499853,0.00247672,0.00502404,-0.03176049,-0.00604484,-0.03202985,0.00657282,-0.01575697,0.0923757,0.01689588,-0.01546644],"last_embed":{"hash":"6vutzu","tokens":116}}},"text":null,"length":0,"last_read":{"hash":"6vutzu","at":1753423576062},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{6}","lines":[186,186],"size":306,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10434581,-0.03241671,-0.01229475,-0.06000366,-0.00365715,0.0375823,0.0140517,-0.00415167,0.07643697,-0.01709217,-0.02844352,0.02936313,0.005389,0.0332708,-0.02338937,-0.01711649,-0.02484894,0.01706649,-0.05297139,0.0243199,0.02980034,0.00627842,-0.03069678,-0.12478068,-0.01973205,-0.00298452,-0.04692398,-0.07871763,-0.04747068,-0.18041646,-0.00927938,-0.02650673,0.04196472,-0.02167879,-0.05280095,-0.03002744,0.02836382,0.01043338,-0.06734799,0.00795282,-0.01266913,0.05489004,0.02724435,-0.00067462,0.04903722,-0.007659,0.0099883,-0.01924843,-0.02560502,0.00031535,-0.07037606,0.02219468,-0.01935498,0.01991957,0.0708627,0.00539877,0.0242211,0.09837095,0.01828564,0.04350881,0.05246006,0.05862217,-0.16477618,0.02072794,0.01019877,0.02489131,-0.02176748,-0.06183738,0.01267879,0.08564808,0.01117331,0.02375286,0.01860069,0.0233173,0.02728635,0.00300239,0.00565709,-0.04913444,-0.01030255,0.0491469,-0.00990829,-0.0394076,-0.03165893,-0.0174613,-0.03722159,0.07978059,0.01063776,0.00587005,0.05129396,-0.07829674,0.03043156,0.06088813,0.00770931,0.01849171,0.02283753,0.03114841,0.04127884,-0.02117315,-0.04277393,0.13007164,-0.03791809,-0.02233953,-0.01876464,-0.04077876,0.04284876,-0.02106807,0.01551867,-0.00561811,-0.0743789,-0.02083201,0.01507139,-0.06731707,0.08939746,0.03406639,-0.02127191,0.02722819,0.05536088,0.03004382,0.01942923,0.0179276,-0.0738774,0.03763102,0.04621856,-0.03890633,-0.01394554,-0.02153208,-0.02339197,0.06748184,-0.04040499,-0.0035447,0.07499423,-0.00017431,-0.07611264,-0.01260566,0.02909312,-0.04468077,0.00606393,0.0083845,-0.0336908,0.00534961,-0.02713437,0.00469243,0.09587336,-0.09556265,-0.06963871,0.02526147,0.01486765,-0.03714444,0.02698421,-0.02012524,-0.002558,0.00995834,-0.0170175,-0.05121906,-0.02890912,0.00672221,0.09807783,0.06540231,-0.09095363,-0.04423188,-0.03411214,-0.02241526,-0.05348009,0.13731566,0.00967787,-0.04273775,-0.01100307,0.02523839,-0.02003795,-0.04219845,0.00418914,-0.0240162,-0.03916826,0.03519715,0.018749,-0.02749387,-0.07090884,-0.04922131,0.00071871,0.02707492,0.05898245,-0.02167221,-0.04803094,0.00424125,-0.01812065,-0.09271852,0.00901455,-0.05301001,0.0381456,-0.00238848,-0.03930758,0.07374702,-0.04563306,0.01839804,-0.05408338,-0.01060823,-0.00826694,-0.04675832,0.0224363,-0.04292443,-0.08195511,-0.02039258,-0.01002431,0.00618599,-0.01190785,0.00332025,-0.01253174,-0.06799503,0.09254286,0.08441801,-0.05419774,-0.02904773,0.04193795,0.04407835,-0.06927422,-0.02374739,-0.00445474,0.01728936,-0.04659124,0.04193358,0.0324871,0.0730199,-0.08633695,-0.19393535,-0.01970441,-0.05152093,-0.01566669,0.0601489,-0.07402618,0.03295317,-0.01093953,0.03400503,0.09351022,0.06464443,-0.03354344,-0.00184072,0.05102444,-0.00286879,-0.00355916,-0.0391708,0.03106138,-0.07408015,-0.00758511,0.00196645,0.03605649,-0.00175678,-0.10411475,-0.0340732,-0.02926776,0.13724582,0.03180836,0.12899074,0.04114776,0.03606847,0.00906541,-0.04017167,-0.0778287,0.0571476,0.0238935,0.05408999,-0.01507504,-0.05400559,-0.03198477,-0.06970785,0.08486074,0.00964505,-0.08880407,-0.01968489,0.00074721,0.01489353,-0.02861376,-0.02444875,0.03924621,0.04731167,0.01038714,0.02020439,0.04356579,0.01106734,-0.02560804,-0.05590779,0.01182605,0.00592886,0.11569606,0.0153924,-0.05949775,-0.00257954,0.00950173,0.064188,0.00038583,-0.00914892,0.0134239,0.04478658,0.00003358,-0.01747585,0.11848511,0.02957204,-0.0046253,0.07772024,-0.00092208,0.07096922,-0.03191527,0.00455436,0.05927881,-0.01385918,-0.05501686,0.07774512,0.05254047,0.0612198,-0.00722626,0.06814022,0.01405117,0.02678518,0.03182346,0.0263633,0.03015719,-0.05471377,-0.01412141,0.01582235,0.0144839,-0.23179507,0.06207488,0.01895172,0.06451161,-0.01746793,-0.06593655,0.0390428,-0.01827388,0.00054854,-0.00873523,0.04347938,0.00998309,0.00308447,-0.03325731,-0.03294883,-0.01441396,0.10957213,-0.03803746,0.05731143,0.04205514,0.04589541,0.06031176,0.24606797,-0.03462055,0.05387796,0.01057229,-0.00754434,-0.01671931,-0.03194267,-0.00161165,0.00497322,-0.00881184,0.02503153,-0.04667331,0.0062026,0.12283306,-0.02720467,-0.00248116,-0.01939625,-0.0294185,-0.05150685,-0.02717504,-0.04818157,0.03302947,0.12549564,0.01315259,-0.04333114,-0.04251086,0.03634119,0.0498152,-0.08287058,-0.04451828,-0.04433719,-0.05015325,-0.02740208,0.02672557,0.00356801,-0.045854,-0.02062602,-0.01727744,0.03916278,0.0098502,0.08138306,0.04325426,-0.02313334],"last_embed":{"hash":"1miomuo","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"1miomuo","at":1753423576112},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{7}","lines":[187,187],"size":486,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07073497,-0.01159555,-0.02646951,-0.02252272,0.00127408,0.03886272,0.02162716,0.02610476,0.08348318,-0.04142014,-0.0210182,-0.03104846,-0.01278982,0.06747711,0.02674562,-0.02838951,-0.00493228,0.04854982,-0.06007278,0.01019914,0.06684092,0.00759356,-0.02363868,-0.08514377,-0.01544857,0.01174109,0.00216945,-0.0747717,-0.05133978,-0.19584723,0.0393863,-0.02530456,0.03054113,-0.05299507,-0.01050219,-0.0327701,0.03311209,0.00339588,-0.00195566,0.03250456,0.00626883,0.01455084,0.01066222,0.00994106,0.05170527,0.02852583,0.01556964,-0.01644947,-0.05114241,-0.04808653,-0.02980883,0.03192931,0.01458188,0.03664674,0.03579853,0.00028382,0.04867955,0.06936842,0.04050674,0.02830645,0.03878614,0.06400557,-0.17519091,0.00423094,0.00986344,0.02358534,-0.01092831,-0.08151853,-0.00397897,0.04924575,0.04713619,0.0113496,-0.00293173,0.04113971,0.01721233,-0.00778986,0.01522028,-0.05134029,0.0014323,0.01753064,-0.01922672,0.00564718,-0.09501223,0.00045058,-0.03515567,0.08397118,0.01804189,-0.03629792,0.02212185,-0.06349855,-0.0235938,0.00751246,-0.02024714,0.04902835,0.01461678,0.01170366,0.04327218,-0.02388129,-0.04525276,0.11819494,-0.01111319,0.02749694,0.00476278,-0.02261318,0.04174266,-0.02241577,-0.05470587,-0.03172255,-0.06651516,-0.0179916,0.00204768,-0.06850681,0.06000486,0.00282059,0.01456386,0.0518644,0.06194878,0.04027516,0.02264327,-0.01619533,-0.05470315,0.02514173,0.01682859,-0.00431498,-0.01957118,-0.00337291,-0.01875596,0.09412942,-0.01098073,-0.01128749,0.02214796,0.00883835,-0.04260853,-0.009778,0.00642878,-0.02354486,-0.00892718,0.01473783,-0.03676863,-0.01581706,-0.02784864,-0.04503687,0.04949627,-0.08580803,-0.09390009,0.05588205,-0.02134114,-0.05200318,0.05244362,-0.03365,-0.00289044,0.02113646,-0.01753294,-0.03063836,-0.03288615,0.01560714,0.08782211,0.06170461,-0.07807747,-0.01995731,0.01864346,-0.065968,-0.0703707,0.14847125,-0.01137981,-0.05918644,-0.04099269,0.02460961,0.01708558,-0.05014541,0.04046329,-0.02499733,-0.01491317,0.03026819,0.01786386,0.00049131,-0.07683636,-0.0521904,-0.0298628,0.07085314,-0.03107258,-0.07840894,-0.04792857,0.0202958,-0.04416555,-0.06130977,0.04196,-0.06938928,0.0268117,0.02062701,-0.02929746,0.04830486,-0.02150324,0.03736383,-0.05735095,-0.02611769,0.01710265,-0.05569763,0.02095788,-0.01821083,-0.05439945,0.00220082,-0.02091307,-0.01474108,-0.01576685,0.01445154,0.00970888,-0.03591453,0.08019714,0.07786702,-0.04058692,-0.00900984,0.0180408,-0.00331261,-0.01514373,-0.01974513,0.02268021,0.02927059,-0.01871083,0.06844515,0.02208819,0.05592596,-0.08904369,-0.19625299,-0.03246363,-0.05884669,-0.04020678,0.03931426,-0.10002287,0.01532516,0.03096199,0.03035307,0.05473755,0.06561608,0.02654461,-0.01002638,0.06522689,0.01487295,0.02428494,0.03630029,0.03378074,-0.07139318,0.01498428,-0.01914053,0.03500602,-0.03802117,-0.12492634,0.01702827,0.00215983,0.1595238,0.03256319,0.12000638,0.01392947,0.00644993,0.04908758,-0.05602665,-0.08364526,0.06191601,0.00404654,0.06690089,-0.0004174,-0.06100683,-0.03333503,-0.07823035,0.08332446,-0.00694737,-0.08377001,-0.05141842,-0.02194208,0.00945615,-0.01907584,0.00025217,0.02177722,0.04770409,-0.02600718,0.05678388,0.01126009,0.0457008,0.00477631,-0.07471252,0.00009618,-0.02228423,0.13155061,-0.00293147,-0.03459303,0.02150867,-0.00129502,0.01588598,-0.00103562,-0.03721603,0.00439297,0.03882577,0.04039945,-0.03468292,0.129125,0.04728615,0.00534422,0.10806131,-0.03591384,0.05457054,-0.02092925,0.02545159,0.09025306,0.01740479,-0.04628939,0.0920855,0.02406843,0.08287689,0.00852812,0.06614505,-0.02691016,0.05883701,0.0293964,0.01130268,0.01378808,-0.05818279,-0.00765324,0.02886615,-0.00620901,-0.24897549,0.07262862,-0.0104685,0.08799282,-0.02348179,-0.03182887,0.04224898,-0.02578382,0.00016009,-0.02665782,0.04970561,0.01313893,0.04753273,-0.03281704,-0.06488267,-0.04074144,0.04527382,-0.07048382,0.03342155,0.03509659,0.01208739,0.05184703,0.23465328,-0.04573557,0.01378904,0.01046548,0.0228151,-0.01774686,-0.02287241,-0.01586838,-0.00128569,0.01479026,-0.01059196,-0.03531233,0.0190278,0.05911227,-0.00972789,0.04152329,0.03694509,-0.0705791,-0.03856276,-0.06598666,-0.034449,0.02518084,0.12803893,0.02464293,-0.0432853,-0.04457191,0.00738747,0.04723713,-0.0492212,-0.04612343,-0.03947977,-0.05794341,-0.01265893,0.05241698,0.03841295,-0.0578545,-0.04307749,-0.04783582,0.03322893,0.02714876,0.07298707,-0.00512784,-0.01327681],"last_embed":{"hash":"7sio3s","tokens":151}}},"text":null,"length":0,"last_read":{"hash":"7sio3s","at":1753423576169},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{8}","lines":[188,189],"size":222,"outlinks":[{"title":"<u><i><b>Chess: The only case where AI is the closest to real INTELLIGENCE (human-like)</b></i></u>","target":"https://www.facebook.com/Parpaluck/posts/pfbid02MgjsXVjiLWzEpNpWpQoTxkncumNcbDD5CBx8BayzKz3cW9dwSUu5kbrafRkqhTZgl","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05768623,-0.02442147,-0.0139426,0.04121948,-0.00005918,0.05613116,0.05513277,0.04653395,0.0827621,-0.04105166,0.0008812,-0.05677938,-0.01950887,0.04867025,0.0138236,0.0026181,-0.02069672,0.00303924,-0.06708524,0.01043846,0.13137202,0.00130102,-0.01949828,-0.07774504,0.01709039,-0.01599353,0.03096826,-0.06327194,-0.03986442,-0.17876951,0.02446435,-0.00232274,0.00118374,-0.05568343,-0.03423459,-0.01125519,0.02296822,0.03113808,-0.01327215,0.06492517,0.03671779,-0.00998283,-0.00752862,-0.02791451,0.05731843,0.00890231,-0.0018582,-0.00916454,-0.0447173,-0.01606175,-0.06336765,0.02724696,0.0041015,0.03977529,0.01331522,0.01785289,0.02699421,0.08801822,0.02900659,0.03402812,0.04159207,0.06028207,-0.17783406,0.02755619,0.00478946,0.01066306,-0.05890805,-0.05889103,0.00495303,0.0378346,0.03444273,-0.0241237,-0.00868075,0.03396233,0.04193713,-0.0057924,0.01279718,-0.01288839,0.01972078,0.01653275,0.02296941,-0.00867881,-0.07008686,-0.04485926,0.00006612,0.06940041,0.0031411,-0.00374851,0.02450496,-0.09993748,-0.01373655,0.02575607,-0.02828374,0.03600297,0.00514535,0.02600798,0.02845514,-0.02543148,-0.02953271,0.09870389,-0.04207215,-0.011652,0.01581406,0.00245864,0.03213033,-0.02732778,-0.04885344,-0.03956752,-0.0509713,0.00110567,-0.01271711,-0.02970933,0.07079797,-0.0316527,-0.0144863,0.02292807,0.06461659,0.01033129,0.01454292,-0.0192658,-0.05169161,0.03041205,0.03158439,0.03045815,-0.0560778,-0.0311723,-0.01679907,0.0731279,0.01026772,-0.01759643,-0.00796816,-0.01088618,-0.05355569,-0.02149185,0.01889028,-0.01060393,0.02799789,-0.02944566,-0.02779248,0.01486706,-0.02552839,-0.02148802,0.02412829,-0.07330388,-0.07017016,0.04937932,-0.01546224,-0.04218056,0.05758105,0.01745437,-0.03194054,0.00994133,-0.02837129,-0.06297796,-0.02584268,0.00480234,0.070622,0.09993386,-0.07515126,0.02043042,0.02069864,-0.03310227,-0.0872811,0.11885219,-0.01135237,0.00692956,-0.05394663,-0.00087843,0.00761941,-0.06328084,0.0181215,0.00285876,-0.0431652,0.0278239,0.0010805,0.01789761,-0.06156307,-0.02317,-0.06430273,0.01652578,-0.03222717,-0.10024376,-0.04636243,0.08992648,-0.01603734,-0.06539673,-0.00047143,-0.08723082,0.04548296,0.02964017,-0.06337744,0.06482314,-0.06273714,0.01741735,-0.02320451,-0.01822079,-0.00411376,-0.01513875,0.0178292,-0.0171285,-0.13768636,-0.04184715,-0.02523564,-0.03142808,-0.02716475,0.00830557,0.00222341,0.01015524,0.06510814,0.04530527,-0.01960934,-0.01213194,0.0258753,0.01832477,0.00099175,0.00121496,0.02392373,0.00303913,-0.0211989,0.01713243,0.04550786,0.00318343,-0.07770294,-0.17891614,-0.02465619,-0.01479776,-0.06490365,0.06619451,-0.09591191,0.02814951,0.01687369,0.05537202,0.07741082,0.03785639,-0.01822679,0.01579505,0.03102201,0.03060132,0.02784309,-0.03277055,-0.00018438,-0.04224116,0.08075799,-0.02132955,0.07459735,-0.00172896,-0.09993846,-0.02931372,-0.00097976,0.13176957,0.07889342,0.0738055,0.03506126,-0.00342614,0.05103425,-0.0342238,-0.1081646,0.04484164,-0.00524303,0.04193285,0.00875413,-0.07415351,-0.06963408,-0.10334799,0.09519736,-0.01862099,-0.06465906,-0.02929646,0.01828301,-0.00032754,0.0117783,-0.0210255,0.0373306,0.06226871,0.00497999,0.03639694,0.02466943,0.0404991,-0.00771806,-0.08775488,0.00757306,-0.03113006,0.07586381,0.00339601,-0.01079369,0.01798896,-0.01561424,0.05958946,0.04025915,-0.03032679,-0.02381862,0.03034818,0.05494685,-0.03357899,0.19719407,0.03858189,-0.02706046,0.08017652,-0.02997573,0.01998017,-0.08819423,-0.02116092,0.09240314,0.05362831,-0.07517723,0.06904031,0.0192927,0.05049386,0.00879611,0.02808427,-0.03084647,0.04618766,0.03099966,-0.00424868,0.0344879,-0.06463641,0.00164504,0.07478197,0.00404949,-0.25243515,0.05900519,-0.01811057,0.12633063,0.00873471,-0.00507,0.04048349,-0.0326716,0.00713706,-0.04247094,0.00046919,0.00790627,0.05945178,-0.03299537,-0.02634771,-0.00972697,0.05318032,-0.08461466,0.02556864,0.02619174,-0.00319814,0.04144417,0.25453463,-0.04564491,-0.00386936,0.00743907,0.0141073,-0.04521492,-0.00896929,0.01344435,0.02246956,0.00844014,-0.01167123,-0.00347633,0.02852466,0.03724708,-0.04522293,0.03471182,0.03617506,-0.04149725,-0.02820649,-0.01757556,-0.03948937,0.03629572,0.10821273,0.02501336,-0.00391322,-0.04144205,0.03044884,0.06459448,-0.07526208,-0.03101337,-0.0524268,-0.04362548,-0.00521393,0.04943156,0.01284067,-0.03366666,-0.03216924,-0.01466582,0.03171507,0.01695188,0.10905472,-0.00695597,-0.0189188],"last_embed":{"hash":"1bk322d","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"1bk322d","at":1753423576221},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence##Conclusion: _\"Close the page doors, HAL!\"_#{9}","lines":[190,195],"size":274,"outlinks":[{"title":"Neural networking, neural networks, artificial intelligence applied to predicting lottery, lotto.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1054013,-0.04584836,-0.03561619,-0.01752715,-0.01898707,0.07898412,0.00846884,0.01207574,0.07356611,0.00878036,-0.02999423,-0.01411636,0.0474484,0.02221217,-0.00950748,-0.05416847,-0.02100752,0.01072223,-0.05599742,0.00386831,0.08053153,-0.01622391,-0.0546027,-0.11458582,0.03248857,-0.04071734,0.00205948,-0.04189039,-0.05275918,-0.2262346,0.04698397,0.01947582,-0.00825087,-0.05908756,-0.05488596,-0.02588181,0.01081786,0.0369775,-0.0509768,0.07212222,0.01119847,-0.00720197,0.00105935,-0.03942246,0.05488103,-0.00817915,0.0049714,0.00432083,-0.01000232,-0.0090036,-0.05974782,-0.00121296,0.01420712,0.04869612,0.06032674,0.03773905,0.02933216,0.10975501,0.02508726,0.03772041,0.03442135,0.06745499,-0.17418368,0.04145658,-0.00439997,0.02572183,-0.02103586,-0.02221325,0.01720702,0.04133284,0.04123256,0.02986082,-0.01658938,0.05351152,0.03766749,0.00225313,-0.03068652,-0.02577175,-0.02735,0.02381043,-0.00836306,-0.00177395,-0.04504994,-0.01831551,0.02288669,0.06823527,0.04554123,0.03218376,0.05440181,-0.07168765,-0.00181839,0.02829449,0.02459971,0.06120196,0.01956282,0.03725127,0.04403361,-0.00661173,-0.01844386,0.10310045,0.0049032,-0.02106256,0.00657155,0.0097898,0.03530673,-0.07424773,-0.04882201,-0.05987466,-0.04310093,0.02784863,0.01577156,-0.01001018,0.04622116,-0.03765372,-0.04479565,0.01131915,0.01874539,0.02975244,0.02539568,-0.01227376,-0.08776,0.00790396,0.00701453,0.01024133,-0.01111376,0.02194315,-0.00647821,0.10172898,0.02092481,0.02562934,0.02429051,0.01526017,-0.13859792,-0.05869045,-0.02088241,-0.01277171,0.02479269,-0.03172958,-0.0319791,0.03702057,-0.01855324,-0.04093381,0.06769902,-0.09341995,-0.04439061,0.07682531,0.03263116,-0.0347305,0.03068394,-0.02293616,-0.01515791,0.00146124,-0.01897942,-0.06506097,-0.02622674,0.02138026,0.07045019,0.07144093,-0.07937729,0.02243569,-0.01902354,-0.03118139,-0.05288189,0.14678589,0.03186582,-0.06960323,-0.03372872,0.03786489,-0.01605787,-0.08957043,-0.00453845,0.00350203,-0.0387166,0.03003743,0.06244191,-0.00058253,-0.0894399,-0.061289,-0.05093948,0.01548254,-0.00110966,-0.06758001,-0.02383857,0.01877167,-0.06795362,-0.08150707,0.02808238,-0.0606909,0.01063187,0.04141987,-0.04170127,0.0180733,-0.04901632,-0.00296117,-0.01956552,-0.0418749,-0.00018184,-0.02405514,0.05396874,-0.01137659,-0.05452492,-0.0166502,0.01438783,0.00819005,-0.01955701,0.03457053,-0.00491028,-0.03975705,0.08769919,0.0307264,-0.01785523,-0.01813115,0.05262671,0.03724853,-0.03937109,0.03328534,-0.00351978,0.02948317,0.0092777,0.03209638,0.03625365,0.03625507,-0.09209932,-0.1745244,-0.02537295,-0.05667331,-0.03321093,0.04035171,-0.03785822,0.01926424,-0.02770251,0.07112885,0.0964468,0.08067201,-0.0393889,-0.00129737,0.03670578,-0.01407551,-0.02271479,-0.08217987,-0.04013633,-0.05119023,0.06048865,-0.00954747,0.0285597,0.01381646,-0.07660018,0.00070143,-0.02793538,0.10872402,0.01276583,0.04480521,-0.01031371,0.03541129,0.05772056,-0.02511939,-0.04525614,0.04022123,0.03485166,0.00730849,0.00154624,-0.04831426,-0.04249847,-0.10453057,0.05417136,-0.00399587,-0.07254931,-0.07068528,0.02911784,-0.01136332,0.00424584,-0.02854317,0.06125447,0.03124059,-0.02788528,0.06700589,0.03389818,0.07254931,-0.01722168,-0.06592578,0.00137815,-0.00542396,0.0631554,0.01032384,-0.0502073,0.02774031,-0.0513484,0.03680656,0.0002713,-0.02090829,-0.01213975,0.01643874,0.01931691,-0.01972614,0.133855,0.05223075,0.01823096,0.04129161,-0.00154726,0.04982681,-0.04199408,-0.01960838,0.03774995,0.01130921,-0.09053937,0.08154456,0.04589367,0.06338364,0.00716759,0.05787514,-0.01365589,0.03592873,0.03287838,-0.02212515,0.02957723,-0.04350448,0.02978349,0.04530704,0.02567316,-0.26412019,0.04224974,-0.02415892,0.04877596,0.03321537,-0.03266157,0.01555134,-0.02672016,0.01699963,-0.04514316,0.05116542,-0.01006118,0.04099224,-0.07685564,-0.04366567,-0.01271931,0.0300291,-0.0447515,0.03999117,0.02558804,0.04754865,0.06586815,0.27910814,-0.00108207,0.00421337,0.01127116,-0.00379102,0.00706592,-0.0156464,0.02979837,0.02855598,0.01524888,0.0484528,-0.03085564,-0.03529948,0.05430736,-0.0191407,0.00856256,0.03796174,-0.00802476,-0.02640867,-0.00467736,-0.02637448,0.00461967,0.1257851,0.04263517,-0.02807315,-0.06700499,0.04307408,0.07275463,-0.08652113,-0.04022484,-0.05557105,-0.03109545,0.00365336,0.03477847,0.01693559,-0.02946376,-0.01279998,-0.01428096,0.04852167,-0.02092697,0.0585255,0.01677666,-0.0283327],"last_embed":{"hash":"1etz8jv","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"1etz8jv","at":1753423576260},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking","lines":[196,225],"size":3592,"outlinks":[{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":6},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":7},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":8},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":9},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":10},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":11},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":12},{"title":"_**Artificial Intelligence, AI Essay: Ion Saliu's Philosophy of Politics**_","target":"https://saliu.com/AI-political-systems-Ion-Saliu.html","line":13},{"title":"_**Artificial Intelligence, AI Chatbots, Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":14},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":15},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":16},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":17},{"title":"_**Lottery Prediction Using Neural Networks**_","target":"https://groups.google.com/forum/#!topic/comp.ai.neural-nets/vnz8DKlBQqM","line":18},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":19},{"title":"_**Online Random Number Generator: Lotto, Powerball, Mega Millions, Lottery, Horse Racing, Roulette, Sports Betting, Euromillions**_","target":"https://saliu.com/generator.html","line":21},{"title":"_**Offline Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Horses, Sports, Roulette**_","target":"https://saliu.com/gambling-lottery-lotto/odds-generator.html","line":22},{"title":"**software**","target":"https://saliu.com/infodown.html","line":23},{"title":"Ion Saliu devised neural networking, AI strategies for lottery and lotto long before anyone else.","target":"https://saliu.com/HLINE.gif","line":25},{"title":"Forums","target":"https://forums.saliu.com/","line":27},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":27},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":27},{"title":"Contents","target":"https://saliu.com/content/index.html","line":27},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":27},{"title":"Home","target":"https://saliu.com/index.htm","line":27},{"title":"Search","target":"https://saliu.com/Search.htm","line":27},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":27},{"title":"Neural networking strategies in lottery are based on lotto number frequency, pairing in past draws.","target":"https://saliu.com/HLINE.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09461474,-0.05672469,-0.01028191,-0.00807529,0.00321246,0.07565616,0.05660725,0.02006908,0.0800253,-0.0093886,-0.02515542,-0.02230345,0.04232863,0.04893121,0.0028639,-0.0314106,-0.03886108,-0.00627778,-0.0451124,0.00690219,0.07238293,-0.00749043,-0.05527037,-0.08545397,0.01949123,-0.03685702,0.00349448,-0.03645098,-0.05118968,-0.17537715,0.04213497,0.02313754,-0.00355918,-0.04605209,-0.06240033,-0.05329658,0.01486725,0.04575865,-0.03925512,0.09211542,0.0397367,-0.00247207,-0.01423628,-0.03087505,0.04843136,-0.01789913,0.00376541,-0.00670935,-0.04049666,-0.02990456,-0.06414807,0.00950676,0.00533556,0.05273522,0.05081808,0.02499497,0.04682624,0.0812111,0.03865922,0.03003685,0.03818231,0.06182117,-0.16267653,0.03390899,0.01019279,0.0222964,-0.02413876,-0.00761324,0.01483129,0.02495042,0.05336149,0.02085236,-0.00595172,0.07583456,0.04934343,0.00623889,-0.01625952,-0.01594149,-0.01393565,0.0444011,0.00984872,0.00579528,-0.04066857,-0.01113763,0.00159848,0.07477286,0.02353338,0.03432523,0.02445605,-0.07168179,0.01584781,0.03902632,0.00114899,0.06092148,0.01638656,0.02699509,0.03923452,-0.0178985,-0.04329875,0.12805668,-0.01258066,-0.04110644,0.02985982,0.01446901,0.03194064,-0.04299783,-0.04394506,-0.07380332,-0.04811598,0.02130128,0.01608146,-0.01262636,0.04130162,-0.01959739,-0.02073769,0.00267934,0.0194196,0.0651086,0.02745833,-0.01662309,-0.09004656,0.02643985,-0.00259976,0.01452219,-0.01609734,-0.01022907,-0.01883584,0.10764156,0.0043745,0.01032518,0.01939629,0.01934972,-0.13732889,-0.05293779,-0.01616799,-0.01182143,0.04204288,-0.04720966,-0.02927985,0.06994379,-0.00888762,-0.02223055,0.04901078,-0.0968798,-0.05514407,0.0823008,0.04307021,-0.01906776,0.03825027,0.00256186,-0.02740184,0.01496284,-0.01106296,-0.08102395,-0.02997962,0.02500447,0.0701389,0.03074141,-0.06458261,0.02429875,-0.0311893,-0.04546671,-0.04274856,0.16708712,0.00834928,-0.09867734,-0.03413013,0.01567664,0.00356693,-0.08950017,0.01934965,0.00719223,-0.03997695,0.03215023,0.05518451,-0.01143314,-0.07205747,-0.04753563,-0.07655663,0.02685041,0.00071177,-0.06237062,-0.03060375,0.02906475,-0.05015062,-0.06919109,0.02947065,-0.05311962,0.00647551,0.04511536,-0.0506162,0.04246262,-0.05616018,-0.01352744,-0.04079114,-0.03837346,-0.00431902,-0.02120184,0.05105105,-0.01715693,-0.08370285,-0.01865374,-0.00362245,0.00377929,-0.02980104,0.02848277,-0.00441485,-0.01564411,0.04447163,0.03520865,-0.0086765,-0.01372622,0.04149106,0.01524863,-0.06374817,0.01366648,0.00999243,0.01804213,0.0057434,0.01104074,0.04747665,0.00325441,-0.11390843,-0.16905887,-0.0395385,-0.04342415,-0.0664189,0.05121781,-0.03149257,0.01837253,-0.02803856,0.04292471,0.08094218,0.07374495,-0.03344677,0.01753075,0.01802611,-0.02094921,-0.03629734,-0.05859615,-0.0585076,-0.03364382,0.04701062,-0.00690255,0.02552556,0.00902978,-0.08467796,-0.03400815,-0.01218321,0.1035535,0.04136563,0.05590926,-0.01103933,0.01851464,0.04567812,-0.02726408,-0.08549612,0.03367029,0.01733872,0.00958714,0.01702636,-0.07574425,-0.03582466,-0.0979327,0.0397884,-0.0127628,-0.0375139,-0.07910036,0.01181457,-0.00985104,0.00092659,-0.04222014,0.05273391,0.02661061,-0.01663734,0.04973457,0.03433158,0.05843941,-0.00069735,-0.06171396,0.01526196,-0.02452574,0.093229,0.0228113,-0.05486326,0.02280021,-0.03210897,0.03549737,0.01658648,-0.03935277,-0.00684123,0.01010671,0.00303491,-0.02149032,0.14351842,0.07355681,0.00004429,0.05936613,-0.02537556,0.0510748,-0.0364232,0.00868265,0.06470564,0.02133053,-0.09908989,0.06233941,0.06138389,0.04422366,0.0161478,0.05023776,-0.01706493,0.02126878,0.03814188,-0.02885207,0.04417251,-0.03917149,0.00524866,0.05379616,0.02023459,-0.24586616,0.08731162,-0.0218533,0.08625012,0.0234773,-0.02649357,0.01715126,-0.00635915,0.01790719,-0.03858044,0.07266821,-0.00321382,0.02452647,-0.06607157,-0.03599219,0.00083188,0.04255049,-0.0681942,0.01331506,0.02505357,0.04377619,0.07049154,0.28450766,0.00082027,-0.00356651,0.00663063,-0.00377794,0.02228835,-0.03559124,0.00000514,0.04793995,-0.00315028,0.00375271,-0.02751064,-0.02003803,0.06223522,-0.00036772,0.01892831,0.04733846,-0.00334607,-0.03074982,-0.01492019,-0.03765834,0.02635311,0.13777472,0.07558795,-0.03801423,-0.07775306,0.01622671,0.07256471,-0.0743714,-0.0649772,-0.03517595,-0.02149172,-0.01305552,0.03260235,0.03686143,-0.05073329,-0.0087172,-0.02434017,0.04992413,-0.02593441,0.05195233,-0.01366531,-0.04128216],"last_embed":{"hash":"149e1xj","tokens":92}}},"text":null,"length":0,"last_read":{"hash":"149e1xj","at":1753423576534},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{2}","lines":[200,200],"size":258,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08945132,-0.06521613,-0.02213866,0.0027764,-0.01418819,0.0750366,0.04966783,-0.01902098,0.06870431,-0.02476108,-0.00508705,-0.05007589,0.03856312,0.04222166,-0.00580004,-0.01855223,-0.0245017,0.03267237,-0.04695661,0.0013589,0.10265525,-0.02862664,-0.07009625,-0.0813251,0.02595966,-0.01659206,-0.02065559,-0.03935317,-0.02764105,-0.16186765,0.03057029,0.02060791,0.00034174,-0.05021224,-0.07231528,-0.05037459,0.0004361,0.07846953,-0.04209171,0.0711228,0.02584758,-0.00042686,-0.03998252,-0.02491857,0.04668592,-0.04262789,0.00561329,-0.00610666,-0.02208728,-0.03069426,-0.04642269,-0.00793487,0.01069675,0.04797819,0.05386433,0.02907524,0.05182844,0.10136591,0.03646629,0.03503685,0.02411529,0.06985674,-0.16326855,0.02606188,0.0178181,0.02760296,-0.02662035,-0.01848992,0.01745289,0.04192689,0.04523015,0.01989263,0.01623412,0.06568141,0.01781494,0.02916526,-0.01739128,-0.05536932,-0.01953631,0.03096687,-0.00656895,0.00869175,-0.05611488,-0.0102025,0.00151697,0.06575131,0.01298801,0.01485403,0.03927441,-0.0633155,-0.00215007,0.04500069,-0.00015366,0.05980844,0.00386491,0.01500222,0.03477652,-0.02952074,-0.0063741,0.13865602,-0.00969857,0.00023463,0.0193573,0.01173077,0.03541506,-0.04598746,-0.04697153,-0.0441494,-0.05295999,0.0273801,0.02804625,-0.03371329,0.03181415,-0.02279555,-0.03032512,-0.00499447,0.0165852,0.04115314,0.01858608,-0.00778995,-0.06577365,0.00776399,0.0285799,0.02846273,-0.00866269,0.01788762,-0.02780556,0.12345438,0.00322726,0.04412927,0.02766195,0.02432184,-0.12335268,-0.03305218,-0.00467891,0.00610306,0.05474244,-0.05602048,-0.05657835,0.06916597,-0.01169392,-0.03551425,0.0789848,-0.08325602,-0.05294642,0.08472456,0.00521993,-0.00944997,0.05839608,-0.00450702,-0.03152785,0.01379637,-0.02587095,-0.05150916,-0.02751995,0.01497392,0.07574569,0.04232681,-0.05323198,0.03357514,-0.03681856,-0.06198683,-0.0601683,0.15884657,-0.00022381,-0.07577466,-0.04516096,0.00833166,-0.0036772,-0.08640461,0.02590476,0.01203824,-0.04856063,0.04848935,0.06199679,0.00280655,-0.08354779,-0.05441372,-0.06558168,0.01744298,-0.02687087,-0.08472551,-0.02529151,0.02928973,-0.05443946,-0.06579511,0.01916406,-0.05971922,0.00774281,0.05156751,-0.03215702,0.0261013,-0.06874785,-0.01475367,-0.03443164,-0.03569204,-0.02950368,-0.00835284,0.07414364,-0.04366621,-0.05552185,-0.01413747,0.00431487,-0.01378543,-0.02808983,0.02352952,-0.01819837,-0.00983524,0.0298195,0.03152156,-0.00643034,-0.01607293,0.03426798,0.01750438,-0.03473925,0.01256572,0.03412812,0.01959479,0.00118026,0.00943803,0.0395491,0.01331953,-0.10319032,-0.16850929,-0.0224095,-0.02909714,-0.02669421,0.04860118,-0.04901154,0.01566219,-0.01759456,0.02824348,0.11363664,0.06002721,-0.03850885,0.02047309,0.00940038,-0.00534257,-0.00669288,-0.04870636,-0.04450944,-0.03333908,0.03096308,-0.00197674,0.04520084,-0.01253626,-0.10009159,-0.01176197,0.00849042,0.12047482,0.0236692,0.00782888,0.00292533,0.03597115,0.04243319,-0.05283911,-0.03484683,0.05503747,0.01614819,-0.00149667,0.0100421,-0.07835571,-0.03152475,-0.09070066,0.06090011,0.01927417,-0.04841531,-0.03942968,0.01305958,0.00037038,0.00409366,-0.0297609,0.03107186,0.00836786,0.00045265,0.03980849,0.03264538,0.0487294,-0.00331764,-0.03381066,0.00486824,-0.05045139,0.07548393,0.01674956,-0.06547847,-0.00727225,-0.03031126,0.04684362,0.02303166,-0.01363268,-0.03881524,0.00546297,0.01387345,-0.0262635,0.11608807,0.06082305,0.01770678,0.03472217,-0.02246209,0.03957785,-0.04388892,0.01443222,0.05340297,0.03303828,-0.10119148,0.0632226,0.07711163,0.06818342,0.01318113,0.04616174,-0.00623344,0.05103713,0.02872504,-0.01930838,0.02408779,-0.0430687,0.00987506,0.04763905,0.03534499,-0.269315,0.08764986,-0.02445488,0.0954392,-0.00106658,-0.0408971,0.0357005,-0.00738563,0.0119204,-0.05846296,0.08087701,0.00064513,0.04850269,-0.06885087,-0.0543033,-0.01073876,0.05451249,-0.07005084,0.05085639,0.02276696,0.02423417,0.06719699,0.27307114,-0.01401707,0.01489,-0.00728639,-0.02641152,-0.00478463,-0.02189232,0.01827906,0.03508342,0.0154696,0.02861071,-0.03136778,-0.03622779,0.09884323,-0.02672972,-0.00713133,0.05432629,-0.00273439,-0.03397032,-0.03118835,-0.06791978,0.03191099,0.11843827,0.06561659,-0.0184476,-0.07916483,0.0249,0.05870001,-0.072069,-0.0645425,-0.06610262,-0.05466491,-0.00126346,0.02112648,0.02320561,-0.01692246,-0.01531042,-0.02143823,0.03819428,-0.02194912,0.07102759,-0.00806171,-0.06241522],"last_embed":{"hash":"7kdphw","tokens":99}}},"text":null,"length":0,"last_read":{"hash":"7kdphw","at":1753423576575},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{12}","lines":[210,210],"size":227,"outlinks":[{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07283682,-0.05434727,-0.03397981,0.00766812,-0.03013195,0.05925141,0.00229608,-0.00286008,0.06977367,-0.00575529,-0.00967011,-0.03198883,0.05576077,0.02761984,-0.01727075,-0.02794628,-0.02916743,0.01626486,-0.05753581,0.00758158,0.10371672,-0.01430734,-0.03453681,-0.09744529,0.02335617,-0.03990831,-0.0023995,-0.05172091,-0.04886139,-0.19263086,0.03184052,-0.02612128,-0.00775118,-0.07399078,-0.06071344,-0.04805019,-0.00947158,0.03076921,-0.06946858,0.06118292,0.00798545,0.00616421,-0.03341507,-0.02839687,0.0614731,-0.00785202,0.02063211,-0.00477787,0.01864971,-0.00979007,-0.03228238,0.04147544,0.00824354,0.05232867,0.07720622,0.01046819,0.04573862,0.11835709,-0.00673572,0.03848425,0.02547656,0.07147364,-0.18216312,0.02207932,-0.01181668,0.03354877,-0.02008182,0.028244,0.02064789,0.04438861,0.03093374,0.02034052,0.00348034,0.05068697,0.00475375,0.01108556,-0.02614662,-0.02580681,-0.02166541,0.03359115,-0.02072791,-0.01672678,-0.05657807,-0.02790783,0.0339333,0.02369484,0.05306868,0.03012395,0.0530723,-0.08232119,-0.00097444,0.05131423,0.0136398,0.04211663,0.0182835,0.03235355,0.05009902,-0.01345324,-0.02423939,0.12384285,0.01611499,-0.02916176,-0.02153432,0.00956681,0.04940199,-0.06587749,-0.0481211,-0.03060221,-0.03820132,0.00145333,0.00077025,-0.01072198,0.04309367,-0.02949119,-0.04381159,0.01770755,0.02493919,0.03252412,0.01960224,-0.0042484,-0.07482553,0.02616726,0.0293576,0.02815119,0.02276446,0.01788185,0.01852587,0.07713451,0.01929604,0.06142477,0.00484207,0.00437036,-0.13663304,-0.03540986,0.00098492,-0.02307737,0.01907036,-0.03205113,-0.04457298,0.02551888,-0.02323414,-0.01729488,0.0656184,-0.10105307,-0.00921909,0.05031599,0.03690338,0.00039943,0.0502459,-0.03285467,-0.02730936,-0.00573327,-0.02833031,-0.07452983,-0.02449247,0.02040084,0.0831411,0.05267533,-0.06303305,0.00508407,-0.01257797,-0.04401481,-0.03663167,0.15973811,-0.00892151,-0.06904878,-0.03915002,0.02483243,-0.01172959,-0.08399673,0.02181745,-0.0029972,-0.05391783,-0.00042751,0.04142171,-0.0208158,-0.09665115,-0.08788377,-0.05693895,0.0298227,-0.00469114,-0.03079972,0.00022695,-0.00461912,-0.07180679,-0.06709993,0.01949941,-0.05300375,0.02915619,0.03560397,-0.0341871,0.02025696,-0.05905128,0.00031547,-0.01993013,-0.00353105,-0.03538237,-0.02437218,0.08090115,-0.00803292,-0.03560873,-0.01929694,0.00608106,0.00563194,-0.02303636,0.02183625,-0.01211116,-0.04839111,0.08366227,0.03661168,0.00274136,-0.02550114,0.05772332,0.0259556,-0.04438191,0.05388663,0.03011123,0.02853575,0.01707211,0.03349441,0.03969038,0.0245683,-0.07391242,-0.18478338,-0.03256577,-0.05987149,-0.04652965,0.05363277,-0.03699822,0.0146305,-0.04717351,0.03133481,0.08499087,0.08213256,-0.05694202,-0.00084879,0.03917727,-0.01707584,0.01520376,-0.07935973,-0.06118462,-0.0534557,0.07276531,0.0021987,0.04632631,-0.0014358,-0.10935096,-0.00147404,-0.03113523,0.1397586,0.00262583,0.03099612,-0.02751398,0.04575979,0.0553057,-0.03424146,-0.0330106,0.05490027,0.02728864,0.00031025,0.02940023,-0.06567442,-0.01623608,-0.06571712,0.06600302,0.02274539,-0.08629511,-0.05636002,0.00463178,-0.03370303,0.01689813,-0.0104877,0.06308944,0.04406919,0.02035929,0.0989368,0.01901792,0.06331101,-0.02684555,-0.06064958,0.01980023,-0.04095707,0.04014319,0.01571779,-0.0844705,0.02075294,-0.04577988,0.08097878,0.00199777,-0.01780035,-0.03242699,0.01385528,0.00486059,-0.01751964,0.09389959,0.03693442,0.02132748,0.03463041,-0.01386889,0.06098957,-0.06351346,-0.01805935,0.04916177,0.02614303,-0.08127012,0.07662641,0.07282002,0.07158933,-0.0081299,0.04847125,0.03268308,0.04641415,0.03406173,-0.00918784,0.00798972,-0.06031952,0.01304668,0.03493234,0.01976738,-0.24788387,0.06520673,-0.03654356,0.07064678,-0.01773859,-0.04629263,0.01578584,-0.01536902,-0.01102008,-0.04344381,0.06142276,0.02093395,0.04000041,-0.06167428,-0.02195227,0.00107024,0.03134368,-0.05369867,0.06746616,0.06897587,0.06758649,0.06822419,0.25225881,-0.01774071,0.01619879,-0.01257335,0.00565482,-0.00043747,0.04576175,0.03566366,0.03328742,0.0075116,0.04281962,-0.02864558,-0.03082299,0.0575709,-0.02851091,0.00803983,-0.00097736,-0.00923238,-0.03783166,-0.02334034,-0.04293755,0.02275121,0.10111365,0.04549545,-0.03379099,-0.06290361,0.01360711,0.03077745,-0.10555604,-0.05383392,-0.0155058,-0.08559927,-0.00032619,0.04940731,0.01996862,-0.03509735,0.00106004,-0.01847435,0.03700236,-0.00605484,0.04896273,-0.01315695,-0.05237695],"last_embed":{"hash":"p6e1hc","tokens":230}}},"text":null,"length":0,"last_read":{"hash":"p6e1hc","at":1753423576622},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{16}","lines":[214,217],"size":714,"outlinks":[{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":1},{"title":"_**Online Random Number Generator: Lotto, Powerball, Mega Millions, Lottery, Horse Racing, Roulette, Sports Betting, Euromillions**_","target":"https://saliu.com/generator.html","line":3},{"title":"_**Offline Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Horses, Sports, Roulette**_","target":"https://saliu.com/gambling-lottery-lotto/odds-generator.html","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11446047,-0.04739099,-0.03898118,0.01737988,-0.0190216,0.09189079,0.02070861,0.00690218,0.0874956,-0.03183903,-0.02620628,-0.04030335,0.03008438,0.05136069,0.01401461,-0.03453265,-0.02573791,0.00748683,-0.03598509,0.02242743,0.08973566,-0.01409418,-0.04006812,-0.0987559,0.04539936,-0.04935588,0.01315131,-0.05077049,-0.02521577,-0.19862673,0.04691678,0.01542155,0.00647204,-0.03245546,-0.0835498,-0.01851266,0.03371272,0.0617891,-0.02706987,0.08120384,0.02690005,-0.01894068,-0.01128521,-0.03049722,0.03575547,-0.01190556,0.00696243,-0.01253146,0.00796299,-0.01671656,-0.0618595,-0.00588992,0.00412761,0.020725,0.0712693,0.04558227,0.04553598,0.09257078,0.02685382,0.03905214,0.01971807,0.06549907,-0.20245107,0.05007946,-0.01337597,0.01706244,-0.0362996,0.01146951,0.04499412,0.02362461,0.02495639,0.02507881,-0.00684852,0.04267514,0.05012979,0.02325139,-0.025207,-0.02666739,0.00097076,0.01164783,-0.01668404,-0.00438082,-0.05994457,-0.02358904,0.03133106,0.07041749,0.00420375,0.03917878,0.02394173,-0.09266584,0.00145438,0.03740811,-0.01018818,0.03071046,-0.0030303,0.01947291,0.03997397,-0.02196991,-0.043646,0.12936014,-0.00531548,-0.00747035,0.00074344,0.04303257,0.04996656,-0.02103163,-0.0594692,-0.03655763,-0.03747583,0.03918526,0.02477717,-0.01895718,0.01733428,-0.05324226,-0.04669549,0.00616424,0.01839455,0.02479919,0.03046849,0.00053011,-0.09201138,-0.01010061,0.01317391,0.03328459,0.00289248,-0.0013127,-0.01281886,0.09510904,0.02083869,0.03845055,0.02000848,0.0326641,-0.13056424,-0.06264061,-0.02992365,-0.01173217,0.01431373,-0.06346657,-0.03358675,0.03712085,-0.01992794,-0.02432388,0.07266028,-0.08504705,-0.03077054,0.05645767,0.02510449,-0.00992632,0.03157702,-0.00110129,-0.02562422,0.00231878,0.00306833,-0.07699032,-0.02205901,0.01480575,0.07941084,0.06703246,-0.06491982,0.04086616,-0.02521378,-0.05772497,-0.04807859,0.16300707,0.0183781,-0.07228288,-0.04689998,0.01858295,0.00987001,-0.08103066,0.01323866,-0.00606462,-0.01691019,0.0073441,0.0402179,0.00265053,-0.07418781,-0.05992533,-0.07641073,0.03149237,-0.04332138,-0.05036466,-0.01905658,0.03926912,-0.04311052,-0.09851835,0.05986833,-0.07533368,0.01542313,0.05783197,-0.04520312,0.02895014,-0.04588713,0.02424221,-0.04907719,-0.03384023,-0.02536511,-0.00753023,0.06100241,0.0232518,-0.07115997,-0.03205477,0.00290713,-0.02595213,-0.03079697,0.01461464,-0.00762094,-0.02700152,0.04500043,0.0379205,0.00720652,0.00706471,0.0551288,0.01895729,-0.03977363,0.01650241,0.00542726,0.03985516,0.00967466,0.02974996,0.04630534,-0.00265388,-0.09965891,-0.17563856,-0.01298298,-0.05953093,-0.0539166,0.06173718,-0.06169643,0.06003054,-0.03534742,0.02389683,0.06770857,0.07739994,-0.01892798,0.02481074,0.02797127,-0.00920418,0.00179994,-0.04065627,-0.02414439,-0.02385154,0.05649355,-0.00100509,0.03148438,-0.0117718,-0.0676351,0.011087,-0.00489433,0.10837506,0.02926547,0.03767215,-0.02600978,0.04415198,0.04564766,-0.04297821,-0.05269641,0.0491974,0.01131989,0.00558855,0.03026282,-0.06379183,-0.03477275,-0.10633043,0.05533567,-0.00514784,-0.06517327,-0.07554626,0.01241069,-0.00399341,-0.01781793,-0.04931455,-0.00395066,0.07905068,-0.00134734,0.03636375,0.01962495,0.06621487,-0.02158411,-0.07089121,-0.00121081,-0.03726717,0.04741361,0.00724541,-0.03189378,0.011979,-0.03009671,0.02461841,0.02176335,0.00123244,-0.01584978,0.01911416,0.01566581,-0.01740227,0.12236566,0.05443338,0.0403325,0.05593354,-0.0386782,0.0167165,-0.06941668,-0.01286458,0.07687517,0.021811,-0.11431295,0.07212604,0.03380935,0.05304397,0.01563169,0.05483817,0.00442784,0.03740656,0.0379677,-0.01059488,0.02851476,-0.0244074,0.02713253,0.06434885,0.01658717,-0.26169395,0.08224361,-0.01150233,0.07633605,-0.00883045,-0.0357892,0.03323583,-0.04263109,0.00505878,-0.04972149,0.06522178,0.02167903,0.034383,-0.07708828,-0.03361811,-0.00302501,0.03116061,-0.04194227,0.05338917,-0.00075402,0.02861673,0.04350452,0.26037326,-0.00741072,0.01694934,-0.01328405,-0.00515567,-0.01997808,0.00207503,-0.00604271,0.04110191,0.02147278,0.03781001,-0.03086634,-0.02580404,0.07586174,-0.01966879,-0.00986899,0.01837042,-0.00827046,-0.04523684,0.01064308,-0.04083489,0.03203122,0.12565722,0.02889562,-0.02253576,-0.05340292,0.02595655,0.06475464,-0.08730286,-0.0669915,-0.01999285,-0.02252016,0.0008863,0.02581294,0.01687421,-0.02345179,-0.02547111,-0.00837021,0.05720477,-0.0014614,0.0544018,-0.0280667,-0.02559446],"last_embed":{"hash":"1k80mai","tokens":279}}},"text":null,"length":0,"last_read":{"hash":"1k80mai","at":1753423576714},"key":"notes/saliu/Neural Networks, Lottery Prediction, Artificial Intelligence.md#Neural Networks, Lottery Prediction, Artificial Intelligence#Resources in Lottery Software, Systems, Strategies, Neural Networking#{18}","lines":[220,225],"size":655,"outlinks":[{"title":"Ion Saliu devised neural networking, AI strategies for lottery and lotto long before anyone else.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Neural networking strategies in lottery are based on lotto number frequency, pairing in past draws.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
