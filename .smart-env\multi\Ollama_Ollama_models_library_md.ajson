"smart_sources:Ollama/Ollama models library.md": {"path":"Ollama/Ollama models library.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07140336,-0.03415622,-0.0384829,0.00417569,0.01302913,0.02388786,-0.04689527,-0.0002308,0.05145026,-0.00113046,-0.00467438,-0.07044625,0.01822746,0.08121423,0.01545754,0.05627433,-0.02764413,-0.01681234,-0.01430808,0.00796488,0.06193145,-0.00835115,0.00363761,0.0266109,0.03603486,0.04839092,-0.07585373,-0.04877418,-0.01572956,-0.25925702,-0.01228015,0.02490065,0.0384466,-0.01390946,-0.08680545,-0.01141359,-0.03160117,-0.00754229,0.0129822,0.03761483,-0.03206354,0.02295207,-0.01325154,-0.02603449,0.0082786,-0.0390735,-0.05609312,0.05064382,-0.01677953,-0.03192298,0.01690665,-0.11541082,-0.01482731,-0.04850912,0.02335048,-0.0204539,0.03985026,0.03347256,-0.02966945,0.06929039,0.04105483,0.03783809,-0.18249802,0.08786486,-0.01830588,0.03964419,-0.00693046,0.01722388,0.04221945,0.01506346,-0.0099733,0.01029149,0.04075418,-0.02024583,0.02736909,-0.00442659,-0.01232866,0.01556289,-0.00079657,-0.01378316,-0.01112648,0.00957006,0.02335796,-0.06698659,-0.03099109,0.000272,-0.02109903,0.00854955,0.01248331,-0.03530746,-0.04223356,0.02314742,0.04752804,0.03793278,-0.02832659,0.01530739,-0.00143991,0.00296417,-0.01699165,0.14828536,0.00189769,0.00352445,0.03714914,-0.01954106,0.02643046,-0.00187453,-0.01245723,-0.01890216,-0.03100387,0.03009804,0.04013645,0.07056182,0.03376483,-0.08670116,-0.00242165,-0.02916284,0.05471449,-0.01883714,-0.02099594,0.03271127,-0.03218322,-0.0039959,0.04433572,-0.0459141,0.00233263,0.03069217,0.05469085,0.06054994,0.05915815,0.0597246,0.04982474,0.06532645,-0.06841875,-0.0346307,0.00549492,0.02319399,0.09977744,0.02354524,0.03059776,-0.01126177,-0.03047281,-0.05154492,0.03489011,-0.09137105,-0.04224962,0.06190171,-0.00052525,0.03488453,-0.06775896,-0.0075321,0.02389757,0.06567622,0.02141486,-0.01603297,0.11892956,-0.02967157,0.0855507,0.11227622,-0.08579203,0.01974549,0.00216387,0.00752249,-0.01020114,0.02229296,-0.05784203,-0.1375685,-0.07550616,-0.00396562,-0.03041667,0.0065594,0.05997651,0.08337515,-0.06607008,0.03108271,0.02523294,0.00441395,-0.11211693,-0.01157726,0.03229796,0.03755097,-0.00139178,-0.03866452,-0.01463265,0.01465369,-0.01444503,-0.03462259,0.03118549,-0.06707226,0.02870473,-0.01638344,-0.05784239,0.02297797,0.02118473,0.07833797,-0.00009309,-0.02349406,-0.04199022,0.02021032,0.02675221,-0.03655758,0.02986359,0.07657585,-0.0135194,0.0040782,-0.0141578,-0.0991607,0.02001246,-0.01807047,0.0414421,0.06118165,-0.10389327,0.03763999,0.0453423,-0.00257877,0.00157726,0.02085093,0.04254574,0.03982779,0.00295012,0.0141024,0.04502668,-0.05057047,-0.06024708,-0.2279146,0.00243821,0.02268737,0.03092395,-0.00184352,-0.14641745,0.0490921,-0.01292208,0.0234083,0.09380277,0.04280829,0.02656243,-0.04204749,-0.01787161,-0.06752308,0.01189158,-0.05116073,0.01372403,-0.05579321,0.03175631,0.02556084,0.02413422,-0.01695487,-0.09305476,0.01908535,-0.0186501,0.17229331,0.01011506,-0.0094058,0.01621077,0.00720423,0.05691262,-0.01788548,-0.0441439,0.04313734,0.00004794,0.00807044,0.03477451,0.03137826,0.01274026,0.02867059,0.02858002,-0.00965014,-0.12165795,-0.06281524,0.02165582,-0.00359737,-0.06889888,-0.04281401,-0.01904978,-0.02664368,0.01123956,0.05253747,0.04619729,-0.08471867,0.01177628,-0.11083929,-0.04012885,-0.02704932,-0.03215891,-0.02612817,0.05879914,-0.05085991,-0.07690255,-0.03466218,-0.02976943,0.03220082,-0.0413008,0.00476134,-0.09939831,0.05598389,0.13134497,-0.02053452,0.03639356,0.05492016,0.00608078,-0.00263602,-0.02569149,-0.06161053,0.03749337,0.01672864,-0.02081895,0.02734876,0.04952419,0.04197342,0.00649595,0.01878989,-0.02198672,0.02044681,-0.02627554,-0.05234263,-0.03008195,0.00799019,-0.02466067,0.05061845,0.06652634,-0.2287387,0.01567334,0.04731182,0.05961661,-0.00666291,0.06626185,0.04122974,-0.0335211,0.00295677,-0.00693072,0.01170824,0.08953485,0.04511661,-0.00395353,-0.00948693,0.02947102,0.04585702,-0.06584022,0.05879252,-0.00893839,-0.01415763,-0.0066829,0.17404599,-0.01621274,0.00872019,-0.02261035,-0.01604716,0.02586803,0.0509155,0.01745868,-0.01565208,0.03820148,0.11886467,-0.05251866,0.01371079,0.01802327,-0.05241997,-0.01189232,0.04403597,0.01998785,0.01076308,0.00932581,-0.02757048,-0.04050874,0.07296874,-0.00539611,-0.04411519,-0.02562584,-0.03842865,0.04393875,0.031444,-0.03650981,-0.05111025,0.03919302,0.01024016,0.07793678,-0.02547369,-0.00844125,-0.03960742,-0.04619304,0.01030333,-0.04410741,0.00568818,-0.00157657,0.02187244],"last_embed":{"hash":"d4fe5deacb47b19ef51ae9360c0ee41b99befde2621a3e49c30a99b410cc6f13","tokens":469}}},"last_read":{"hash":"d4fe5deacb47b19ef51ae9360c0ee41b99befde2621a3e49c30a99b410cc6f13","at":1745995214779},"class_name":"SmartSource2","outlinks":[{"title":"\n    \n    ## gemma\n    \n    Gemma is a family of lightweight, state-of-the-art open models built by Google DeepMind.\n    \n    287.7K  Pulls 69 Tags Updated  4 days ago\n    \n    ","target":"/library/gemma","line":6},{"title":"\n    \n    ## llama2\n    \n    Llama 2 is a collection of foundation language models ranging from 7B to 70B parameters.\n    \n    911.4K  Pulls 102 Tags Updated  7 weeks ago\n    \n    ","target":"/library/llama2","line":15},{"title":"\n    \n    ## mistral\n    \n    The 7B model released by Mistral AI, updated to version 0.2.\n    \n    434.5K  Pulls 68 Tags Updated  4 days ago\n    \n    ","target":"/library/mistral","line":24},{"title":"\n    \n    ## mixtral\n    \n    A high-quality Mixture of Experts (MoE) model with open weights by Mistral AI.\n    \n    119.7K  Pulls 34 Tags Updated  8 weeks ago\n    \n    ","target":"/library/mixtral","line":33},{"title":"\n    \n    ## command-r\n    \n    Command R is a Large Language Model optimized for conversational interaction and long context tasks.\n    \n    237  Pulls 17 Tags Updated  4 hours ago\n    \n    ","target":"/library/command-r","line":42},{"title":"\n    \n    ## llava\n    \n    🌋 LLaVA is a novel end-to-end trained large multimodal model that combines a vision encoder and Vicuna for general-purpose visual and language understanding. Updated to version 1.6.\n    \n    102.4K  Pulls 98 Tags Updated  8 weeks ago\n    \n    ","target":"/library/llava","line":51},{"title":"\n    \n    ## codellama\n    \n    A large language model that can use text prompts to generate and discuss code.\n    \n    270.9K  Pulls 199 Tags Updated  8 weeks ago\n    \n    ","target":"/library/codellama","line":60},{"title":"\n    \n    ## dolphin-mixtral\n    \n    An uncensored, fine-tuned model based on the Mixtral mixture of experts model that excels at coding tasks. Created by Eric Hartford.\n    \n    175.3K  Pulls 70 Tags Updated  2 months ago\n    \n    ","target":"/library/dolphin-mixtral","line":69},{"title":"\n    \n    ## qwen\n    \n    Qwen 1.5 is a series of large language models by Alibaba Cloud spanning from 0.5B to 72B parameters\n    \n    122.8K  Pulls 319 Tags Updated  7 weeks ago\n    \n    ","target":"/library/qwen","line":78},{"title":"\n    \n    ## llama2-uncensored\n    \n    Uncensored Llama 2 model by George Sung and Jarrad Hope.\n    \n    121.8K  Pulls 34 Tags Updated  4 months ago\n    \n    ","target":"/library/llama2-uncensored","line":87},{"title":"\n    \n    ## mistral-openorca\n    \n    Mistral OpenOrca is a 7 billion parameter model, fine-tuned on top of the Mistral 7B model using the OpenOrca dataset.\n    \n    111.6K  Pulls 17 Tags Updated  5 months ago\n    \n    ","target":"/library/mistral-openorca","line":96},{"title":"\n    \n    ## deepseek-coder\n    \n    DeepSeek Coder is a capable coding model trained on two trillion code and natural language tokens.\n    \n    78.7K  Pulls 102 Tags Updated  3 months ago\n    \n    ","target":"/library/deepseek-coder","line":105},{"title":"\n    \n    ## nous-hermes2\n    \n    The powerful family of models by Nous Research that excels at scientific discussion and coding tasks.\n    \n    67.9K  Pulls 33 Tags Updated  2 months ago\n    \n    ","target":"/library/nous-hermes2","line":114},{"title":"\n    \n    ## phi\n    \n    Phi-2: a 2.7B language model by Microsoft Research that demonstrates outstanding reasoning and language understanding capabilities.\n    \n    67.5K  Pulls 18 Tags Updated  8 weeks ago\n    \n    ","target":"/library/phi","line":123},{"title":"\n    \n    ## orca-mini\n    \n    A general-purpose model ranging from 3 billion parameters to 70 billion, suitable for entry-level hardware.\n    \n    60.2K  Pulls 119 Tags Updated  4 months ago\n    \n    ","target":"/library/orca-mini","line":132},{"title":"\n    \n    ## dolphin-mistral\n    \n    The uncensored Dolphin model based on Mistral that excels at coding tasks. Updated to version 2.6.\n    \n    54.6K  Pulls 103 Tags Updated  2 months ago\n    \n    ","target":"/library/dolphin-mistral","line":141},{"title":"\n    \n    ## wizard-vicuna-uncensored\n    \n    Wizard Vicuna Uncensored is a 7B, 13B, and 30B parameter model based on Llama 2 uncensored by Eric Hartford.\n    \n    38.0K  Pulls 49 Tags Updated  4 months ago\n    \n    ","target":"/library/wizard-vicuna-uncensored","line":150},{"title":"\n    \n    ## vicuna\n    \n    General use chat model based on Llama and Llama 2 with 2K to 16K context sizes.\n    \n    33.3K  Pulls 111 Tags Updated  4 months ago\n    \n    ","target":"/library/vicuna","line":159},{"title":"\n    \n    ## tinydolphin\n    \n    An experimental 1.1B parameter model trained on the new Dolphin 2.8 dataset by Eric Hartford and based on TinyLlama.\n    \n    31.1K  Pulls 18 Tags Updated  8 weeks ago\n    \n    ","target":"/library/tinydolphin","line":168},{"title":"\n    \n    ## nomic-embed-text\n    \n    A high-performing open embedding model with a large token context window.\n    \n    30.9K  Pulls 3 Tags Updated  3 weeks ago\n    \n    ","target":"/library/nomic-embed-text","line":177},{"title":"\n    \n    ## llama2-chinese\n    \n    Llama 2 based model fine tuned to improve Chinese dialogue ability.\n    \n    30.1K  Pulls 35 Tags Updated  5 months ago\n    \n    ","target":"/library/llama2-chinese","line":186},{"title":"\n    \n    ## zephyr\n    \n    Zephyr beta is a fine-tuned 7B version of mistral that was trained on on a mix of publicly available, synthetic datasets.\n    \n    27.2K  Pulls 34 Tags Updated  3 months ago\n    \n    ","target":"/library/zephyr","line":195},{"title":"\n    \n    ## openhermes\n    \n    OpenHermes 2.5 is a 7B model fine-tuned by Teknium on Mistral with fully open datasets.\n    \n    27.0K  Pulls 35 Tags Updated  3 months ago\n    \n    ","target":"/library/openhermes","line":204},{"title":"\n    \n    ## tinyllama\n    \n    The TinyLlama project is an open endeavor to train a compact 1.1B Llama model on 3 trillion tokens.\n    \n    25.5K  Pulls 36 Tags Updated  2 months ago\n    \n    ","target":"/library/tinyllama","line":213},{"title":"\n    \n    ## openchat\n    \n    A family of open-source models trained on a wide variety of data, surpassing ChatGPT on various benchmarks. Updated to version 3.5-0106.\n    \n    24.1K  Pulls 50 Tags Updated  2 months ago\n    \n    ","target":"/library/openchat","line":222},{"title":"\n    \n    ## wizardcoder\n    \n    State-of-the-art code generation model\n    \n    22.6K  Pulls 67 Tags Updated  2 months ago\n    \n    ","target":"/library/wizardcoder","line":231},{"title":"\n    \n    ## starcoder\n    \n    StarCoder is a code generation model trained on 80+ programming languages.\n    \n    19.8K  Pulls 100 Tags Updated  5 months ago\n    \n    ","target":"/library/starcoder","line":240},{"title":"\n    \n    ## phind-codellama\n    \n    Code generation model based on Code Llama.\n    \n    19.0K  Pulls 49 Tags Updated  3 months ago\n    \n    ","target":"/library/phind-codellama","line":249},{"title":"\n    \n    ## neural-chat\n    \n    A fine-tuned model based on Mistral with good coverage of domain and language.\n    \n    18.5K  Pulls 50 Tags Updated  9 hours ago\n    \n    ","target":"/library/neural-chat","line":258},{"title":"\n    \n    ## starcoder2\n    \n    StarCoder2 is the next generation of transparently trained open code LLMs that comes in three sizes: 3B, 7B and 15B parameters.\n    \n    17.7K  Pulls 49 Tags Updated  3 weeks ago\n    \n    ","target":"/library/starcoder2","line":267},{"title":"\n    \n    ## yi\n    \n    A high-performing, bilingual language model.\n    \n    16.7K  Pulls 78 Tags Updated  3 months ago\n    \n    ","target":"/library/yi","line":276},{"title":"\n    \n    ## orca2\n    \n    Orca 2 is built by Microsoft research, and are a fine-tuned version of Meta's Llama 2 models. The model is designed to excel particularly in reasoning.\n    \n    15.7K  Pulls 33 Tags Updated  4 months ago\n    \n    ","target":"/library/orca2","line":285},{"title":"\n    \n    ## falcon\n    \n    A large language model built by the Technology Innovation Institute (TII) for use in summarization, text generation, and chat bots.\n    \n    15.4K  Pulls 38 Tags Updated  5 months ago\n    \n    ","target":"/library/falcon","line":294},{"title":"\n    \n    ## wizard-math\n    \n    Model focused on math and logic problems\n    \n    15.2K  Pulls 64 Tags Updated  3 months ago\n    \n    ","target":"/library/wizard-math","line":303},{"title":"\n    \n    ## dolphin-phi\n    \n    2.7B uncensored Dolphin model by Eric Hartford, based on the Phi language model by Microsoft Research.\n    \n    14.6K  Pulls 15 Tags Updated  3 months ago\n    \n    ","target":"/library/dolphin-phi","line":312},{"title":"\n    \n    ## starling-lm\n    \n    Starling is a large language model trained by reinforcement learning from AI feedback focused on improving chatbot helpfulness.\n    \n    13.0K  Pulls 18 Tags Updated  4 months ago\n    \n    ","target":"/library/starling-lm","line":321},{"title":"\n    \n    ## nous-hermes\n    \n    General use models based on Llama and Llama 2 from Nous Research.\n    \n    12.6K  Pulls 63 Tags Updated  4 months ago\n    \n    ","target":"/library/nous-hermes","line":330},{"title":"\n    \n    ## stable-code\n    \n    Stable Code 3B is a coding model with instruct and code completion variants on par with models such as Code Llama 7B that are 2.5x larger.\n    \n    12.4K  Pulls 36 Tags Updated  2 days ago\n    \n    ","target":"/library/stable-code","line":339},{"title":"\n    \n    ## medllama2\n    \n    Fine-tuned Llama 2 model to answer medical questions based on an open source medical dataset.\n    \n    10.7K  Pulls 17 Tags Updated  5 months ago\n    \n    ","target":"/library/medllama2","line":348},{"title":"\n    \n    ## bakllava\n    \n    BakLLaVA is a multimodal model consisting of the Mistral 7B base model augmented with the LLaVA architecture.\n    \n    10.4K  Pulls 17 Tags Updated  3 months ago\n    \n    ","target":"/library/bakllava","line":357},{"title":"\n    \n    ## codeup\n    \n    Great code generation model based on Llama2.\n    \n    10.1K  Pulls 19 Tags Updated  4 months ago\n    \n    ","target":"/library/codeup","line":366},{"title":"\n    \n    ## wizardlm-uncensored\n    \n    Uncensored version of Wizard LM model\n    \n    9,935  Pulls 18 Tags Updated  5 months ago\n    \n    ","target":"/library/wizardlm-uncensored","line":375},{"title":"\n    \n    ## solar\n    \n    A compact, yet powerful 10.7B large language model designed for single-turn conversation.\n    \n    9,888  Pulls 32 Tags Updated  3 months ago\n    \n    ","target":"/library/solar","line":384},{"title":"\n    \n    ## everythinglm\n    \n    Uncensored Llama2 based model with support for a 16K context window.\n    \n    9,374  Pulls 18 Tags Updated  3 months ago\n    \n    ","target":"/library/everythinglm","line":393},{"title":"\n    \n    ## sqlcoder\n    \n    SQLCoder is a code completion model fined-tuned on StarCoder for SQL generation tasks\n    \n    9,024  Pulls 48 Tags Updated  8 weeks ago\n    \n    ","target":"/library/sqlcoder","line":402},{"title":"\n    \n    ## dolphincoder\n    \n    An uncensored variant of the Dolphin model family that excels at coding, based on StarCoder2.\n    \n    8,992  Pulls 18 Tags Updated  3 weeks ago\n    \n    ","target":"/library/dolphincoder","line":411},{"title":"\n    \n    ## nous-hermes2-mixtral\n    \n    The Nous Hermes 2 model from Nous Research, now trained over Mixtral.\n    \n    8,572  Pulls 18 Tags Updated  2 months ago\n    \n    ","target":"/library/nous-hermes2-mixtral","line":420},{"title":"\n    \n    ## stable-beluga\n    \n    Llama 2 based model fine tuned on an Orca-style dataset. Originally called Free Willy.\n    \n    7,510  Pulls 49 Tags Updated  4 months ago\n    \n    ","target":"/library/stable-beluga","line":429},{"title":"\n    \n    ## yarn-mistral\n    \n    An extension of Mistral to support context windows of 64K or 128K.\n    \n    7,176  Pulls 33 Tags Updated  3 months ago\n    \n    ","target":"/library/yarn-mistral","line":438},{"title":"\n    \n    ## stablelm2\n    \n    Stable LM 2 1.6B is a state-of-the-art 1.6 billion parameter small language model trained on multilingual data in English, Spanish, German, Italian, French, Portuguese, and Dutch.\n    \n    7,137  Pulls 34 Tags Updated  8 weeks ago\n    \n    ","target":"/library/stablelm2","line":447},{"title":"\n    \n    ## samantha-mistral\n    \n    A companion assistant trained in philosophy, psychology, and personal relationships. Based on Mistral.\n    \n    6,945  Pulls 49 Tags Updated  5 months ago\n    \n    ","target":"/library/samantha-mistral","line":456},{"title":"\n    \n    ## meditron\n    \n    Open-source medical large language model adapted from Llama 2 to the medical domain.\n    \n    6,732  Pulls 22 Tags Updated  3 months ago\n    \n    ","target":"/library/meditron","line":465},{"title":"\n    \n    ## stablelm-zephyr\n    \n    A lightweight chat model allowing accurate, and responsive output without requiring high-end hardware.\n    \n    6,171  Pulls 17 Tags Updated  3 months ago\n    \n    ","target":"/library/stablelm-zephyr","line":474},{"title":"\n    \n    ## magicoder\n    \n    🎩 Magicoder is a family of 7B parameter models trained on 75K synthetic instruction data using OSS-Instruct, a novel approach to enlightening LLMs with open-source code snippets.\n    \n    6,145  Pulls 18 Tags Updated  3 months ago\n    \n    ","target":"/library/magicoder","line":483},{"title":"\n    \n    ## yarn-llama2\n    \n    An extension of Llama 2 that supports a context of up to 128k tokens.\n    \n    5,992  Pulls 67 Tags Updated  4 months ago\n    \n    ","target":"/library/yarn-llama2","line":492},{"title":"\n    \n    ## llama-pro\n    \n    An expansion of Llama 2 that specializes in integrating both general language understanding and domain-specific knowledge, particularly in programming and mathematics.\n    \n    5,798  Pulls 33 Tags Updated  2 months ago\n    \n    ","target":"/library/llama-pro","line":501},{"title":"\n    \n    ## deepseek-llm\n    \n    An advanced language model crafted with 2 trillion bilingual tokens.\n    \n    5,624  Pulls 64 Tags Updated  3 months ago\n    \n    ","target":"/library/deepseek-llm","line":510},{"title":"\n    \n    ## wizard-vicuna\n    \n    Wizard Vicuna is a 13B parameter model based on Llama 2 trained by MelodysDreamj.\n    \n    5,569  Pulls 17 Tags Updated  5 months ago\n    \n    ","target":"/library/wizard-vicuna","line":519},{"title":"\n    \n    ## codebooga\n    \n    A high-performing code instruct model created by merging two existing code models.\n    \n    5,452  Pulls 16 Tags Updated  4 months ago\n    \n    ","target":"/library/codebooga","line":528},{"title":"\n    \n    ## mistrallite\n    \n    MistralLite is a fine-tuned model based on Mistral with enhanced capabilities of processing long contexts.\n    \n    5,221  Pulls 17 Tags Updated  4 months ago\n    \n    ","target":"/library/mistrallite","line":537},{"title":"\n    \n    ## all-minilm\n    \n    Embedding models on very large sentence level datasets.\n    \n    4,611  Pulls 8 Tags Updated  5 weeks ago\n    \n    ","target":"/library/all-minilm","line":546},{"title":"\n    \n    ## nexusraven\n    \n    Nexus Raven is a 13B instruction tuned model for function calling tasks.\n    \n    4,365  Pulls 32 Tags Updated  2 months ago\n    \n    ","target":"/library/nexusraven","line":555},{"title":"\n    \n    ## open-orca-platypus2\n    \n    Merge of the Open Orca OpenChat model and the Garage-bAInd Platypus 2 model. Designed for chat and code generation.\n    \n    4,021  Pulls 17 Tags Updated  5 months ago\n    \n    ","target":"/library/open-orca-platypus2","line":564},{"title":"\n    \n    ## goliath\n    \n    A language model created by combining two fine-tuned Llama 2 70B models into one.\n    \n    3,548  Pulls 16 Tags Updated  4 months ago\n    \n    ","target":"/library/goliath","line":573},{"title":"\n    \n    ## notux\n    \n    A top-performing mixture of experts model, fine-tuned with high-quality data.\n    \n    3,236  Pulls 18 Tags Updated  2 months ago\n    \n    ","target":"/library/notux","line":582},{"title":"\n    \n    ## megadolphin\n    \n    MegaDolphin-2.2-120b is a transformation of Dolphin-2.2-70b created by interleaving the model with itself.\n    \n    2,909  Pulls 19 Tags Updated  2 months ago\n    \n    ","target":"/library/megadolphin","line":591},{"title":"\n    \n    ## alfred\n    \n    A robust conversational model designed to be used for both chat and instruct use cases.\n    \n    2,883  Pulls 7 Tags Updated  4 months ago\n    \n    ","target":"/library/alfred","line":600},{"title":"\n    \n    ## xwinlm\n    \n    Conversational model based on Llama 2 that performs competitively on various benchmarks.\n    \n    2,609  Pulls 80 Tags Updated  4 months ago\n    \n    ","target":"/library/xwinlm","line":609},{"title":"\n    \n    ## wizardlm\n    \n    General use 70 billion parameter model based on Llama 2.\n    \n    2,518  Pulls 73 Tags Updated  5 months ago\n    \n    ","target":"/library/wizardlm","line":618},{"title":"\n    \n    ## duckdb-nsql\n    \n    7B parameter text-to-SQL model made by MotherDuck and Numbers Station.\n    \n    2,404  Pulls 17 Tags Updated  8 weeks ago\n    \n    ","target":"/library/duckdb-nsql","line":627},{"title":"\n    \n    ## notus\n    \n    A 7B chat model fine-tuned with high-quality data and based on Zephyr.\n    \n    2,211  Pulls 18 Tags Updated  2 months ago\n    \n    ","target":"/library/notus","line":636},{"title":"\n    \n    ## mxbai-embed-large\n    \n    State-of-the-art large embedding model from mixedbread.ai\n    \n    97  Pulls 3 Tags Updated  6 hours ago\n    \n    ","target":"/library/mxbai-embed-large","line":645}],"blocks":{"#":[2,7],"##{1}":[6,7],"##gemma":[8,16],"##gemma#{1}":[10,14],"##gemma#{2}":[15,16],"##llama2":[17,25],"##llama2#{1}":[19,23],"##llama2#{2}":[24,25],"##mistral":[26,34],"##mistral#{1}":[28,32],"##mistral#{2}":[33,34],"##mixtral":[35,43],"##mixtral#{1}":[37,41],"##mixtral#{2}":[42,43],"##command-r":[44,52],"##command-r#{1}":[46,50],"##command-r#{2}":[51,52],"##llava":[53,61],"##llava#{1}":[55,59],"##llava#{2}":[60,61],"##codellama":[62,70],"##codellama#{1}":[64,68],"##codellama#{2}":[69,70],"##dolphin-mixtral":[71,79],"##dolphin-mixtral#{1}":[73,77],"##dolphin-mixtral#{2}":[78,79],"##qwen":[80,88],"##qwen#{1}":[82,86],"##qwen#{2}":[87,88],"##llama2-uncensored":[89,97],"##llama2-uncensored#{1}":[91,95],"##llama2-uncensored#{2}":[96,97],"##mistral-openorca":[98,106],"##mistral-openorca#{1}":[100,104],"##mistral-openorca#{2}":[105,106],"##deepseek-coder":[107,115],"##deepseek-coder#{1}":[109,113],"##deepseek-coder#{2}":[114,115],"##nous-hermes2":[116,124],"##nous-hermes2#{1}":[118,122],"##nous-hermes2#{2}":[123,124],"##phi":[125,133],"##phi#{1}":[127,131],"##phi#{2}":[132,133],"##orca-mini":[134,142],"##orca-mini#{1}":[136,140],"##orca-mini#{2}":[141,142],"##dolphin-mistral":[143,151],"##dolphin-mistral#{1}":[145,149],"##dolphin-mistral#{2}":[150,151],"##wizard-vicuna-uncensored":[152,160],"##wizard-vicuna-uncensored#{1}":[154,158],"##wizard-vicuna-uncensored#{2}":[159,160],"##vicuna":[161,169],"##vicuna#{1}":[163,167],"##vicuna#{2}":[168,169],"##tinydolphin":[170,178],"##tinydolphin#{1}":[172,176],"##tinydolphin#{2}":[177,178],"##nomic-embed-text":[179,187],"##nomic-embed-text#{1}":[181,185],"##nomic-embed-text#{2}":[186,187],"##llama2-chinese":[188,196],"##llama2-chinese#{1}":[190,194],"##llama2-chinese#{2}":[195,196],"##zephyr":[197,205],"##zephyr#{1}":[199,203],"##zephyr#{2}":[204,205],"##openhermes":[206,214],"##openhermes#{1}":[208,212],"##openhermes#{2}":[213,214],"##tinyllama":[215,223],"##tinyllama#{1}":[217,221],"##tinyllama#{2}":[222,223],"##openchat":[224,232],"##openchat#{1}":[226,230],"##openchat#{2}":[231,232],"##wizardcoder":[233,241],"##wizardcoder#{1}":[235,239],"##wizardcoder#{2}":[240,241],"##starcoder":[242,250],"##starcoder#{1}":[244,248],"##starcoder#{2}":[249,250],"##phind-codellama":[251,259],"##phind-codellama#{1}":[253,257],"##phind-codellama#{2}":[258,259],"##neural-chat":[260,268],"##neural-chat#{1}":[262,266],"##neural-chat#{2}":[267,268],"##starcoder2":[269,277],"##starcoder2#{1}":[271,275],"##starcoder2#{2}":[276,277],"##yi":[278,286],"##yi#{1}":[280,284],"##yi#{2}":[285,286],"##orca2":[287,295],"##orca2#{1}":[289,293],"##orca2#{2}":[294,295],"##falcon":[296,304],"##falcon#{1}":[298,302],"##falcon#{2}":[303,304],"##wizard-math":[305,313],"##wizard-math#{1}":[307,311],"##wizard-math#{2}":[312,313],"##dolphin-phi":[314,322],"##dolphin-phi#{1}":[316,320],"##dolphin-phi#{2}":[321,322],"##starling-lm":[323,331],"##starling-lm#{1}":[325,329],"##starling-lm#{2}":[330,331],"##nous-hermes":[332,340],"##nous-hermes#{1}":[334,338],"##nous-hermes#{2}":[339,340],"##stable-code":[341,349],"##stable-code#{1}":[343,347],"##stable-code#{2}":[348,349],"##medllama2":[350,358],"##medllama2#{1}":[352,356],"##medllama2#{2}":[357,358],"##bakllava":[359,367],"##bakllava#{1}":[361,365],"##bakllava#{2}":[366,367],"##codeup":[368,376],"##codeup#{1}":[370,374],"##codeup#{2}":[375,376],"##wizardlm-uncensored":[377,385],"##wizardlm-uncensored#{1}":[379,383],"##wizardlm-uncensored#{2}":[384,385],"##solar":[386,394],"##solar#{1}":[388,392],"##solar#{2}":[393,394],"##everythinglm":[395,403],"##everythinglm#{1}":[397,401],"##everythinglm#{2}":[402,403],"##sqlcoder":[404,412],"##sqlcoder#{1}":[406,410],"##sqlcoder#{2}":[411,412],"##dolphincoder":[413,421],"##dolphincoder#{1}":[415,419],"##dolphincoder#{2}":[420,421],"##nous-hermes2-mixtral":[422,430],"##nous-hermes2-mixtral#{1}":[424,428],"##nous-hermes2-mixtral#{2}":[429,430],"##stable-beluga":[431,439],"##stable-beluga#{1}":[433,437],"##stable-beluga#{2}":[438,439],"##yarn-mistral":[440,448],"##yarn-mistral#{1}":[442,446],"##yarn-mistral#{2}":[447,448],"##stablelm2":[449,457],"##stablelm2#{1}":[451,455],"##stablelm2#{2}":[456,457],"##samantha-mistral":[458,466],"##samantha-mistral#{1}":[460,464],"##samantha-mistral#{2}":[465,466],"##meditron":[467,475],"##meditron#{1}":[469,473],"##meditron#{2}":[474,475],"##stablelm-zephyr":[476,484],"##stablelm-zephyr#{1}":[478,482],"##stablelm-zephyr#{2}":[483,484],"##magicoder":[485,493],"##magicoder#{1}":[487,491],"##magicoder#{2}":[492,493],"##yarn-llama2":[494,502],"##yarn-llama2#{1}":[496,500],"##yarn-llama2#{2}":[501,502],"##llama-pro":[503,511],"##llama-pro#{1}":[505,509],"##llama-pro#{2}":[510,511],"##deepseek-llm":[512,520],"##deepseek-llm#{1}":[514,518],"##deepseek-llm#{2}":[519,520],"##wizard-vicuna":[521,529],"##wizard-vicuna#{1}":[523,527],"##wizard-vicuna#{2}":[528,529],"##codebooga":[530,538],"##codebooga#{1}":[532,536],"##codebooga#{2}":[537,538],"##mistrallite":[539,547],"##mistrallite#{1}":[541,545],"##mistrallite#{2}":[546,547],"##all-minilm":[548,556],"##all-minilm#{1}":[550,554],"##all-minilm#{2}":[555,556],"##nexusraven":[557,565],"##nexusraven#{1}":[559,563],"##nexusraven#{2}":[564,565],"##open-orca-platypus2":[566,574],"##open-orca-platypus2#{1}":[568,572],"##open-orca-platypus2#{2}":[573,574],"##goliath":[575,583],"##goliath#{1}":[577,581],"##goliath#{2}":[582,583],"##notux":[584,592],"##notux#{1}":[586,590],"##notux#{2}":[591,592],"##megadolphin":[593,601],"##megadolphin#{1}":[595,599],"##megadolphin#{2}":[600,601],"##alfred":[602,610],"##alfred#{1}":[604,608],"##alfred#{2}":[609,610],"##xwinlm":[611,619],"##xwinlm#{1}":[613,617],"##xwinlm#{2}":[618,619],"##wizardlm":[620,628],"##wizardlm#{1}":[622,626],"##wizardlm#{2}":[627,628],"##duckdb-nsql":[629,637],"##duckdb-nsql#{1}":[631,635],"##duckdb-nsql#{2}":[636,637],"##notus":[638,646],"##notus#{1}":[640,644],"##notus#{2}":[645,646],"##mxbai-embed-large":[647,653],"##mxbai-embed-large#{1}":[649,653]},"last_import":{"mtime":1711675569921,"size":15657,"at":1740449882069,"hash":"d4fe5deacb47b19ef51ae9360c0ee41b99befde2621a3e49c30a99b410cc6f13"},"key":"Ollama/Ollama models library.md"},