"smart_sources:PromptingTools.jl/Using AITemplates - PromptingTools.jl.md": {"path":"PromptingTools.jl/Using AITemplates - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10459272,0.02070514,-0.04116036,-0.03967213,-0.02668962,0.01867498,-0.03756882,0.02822358,0.04195675,-0.03790134,-0.02328772,0.01563275,0.00922583,0.06563701,-0.00737656,0.04101137,-0.04900407,0.04776525,-0.06429844,-0.01630184,0.06876638,0.03695635,-0.00649716,-0.0525693,0.00336973,0.06875222,-0.0040252,-0.04648608,0.01634444,-0.19358329,0.08887811,-0.02874624,0.01651816,-0.01958113,-0.06016348,-0.00301053,-0.0874434,0.05761773,-0.01483524,0.01008207,0.03046858,0.01912651,-0.03850183,-0.01892947,-0.0628251,-0.07047924,-0.01391228,-0.03092328,0.01271742,-0.03336613,-0.00829467,-0.03026281,0.03370175,-0.05936213,0.01745281,0.01107688,0.00134558,0.067398,0.02509669,0.02306101,0.04471666,0.01159349,-0.20035852,0.14374113,0.0098566,0.04475755,-0.0014117,0.01445686,0.02378561,0.05588802,-0.02414758,0.03533258,0.00291002,0.07189326,0.03121,-0.03301153,-0.03888304,-0.02954017,0.0118891,-0.10868523,-0.04133528,-0.01343599,-0.02897836,-0.01237335,-0.0082038,0.01919436,-0.00440264,-0.01274335,0.05779091,-0.01082126,0.02142665,-0.06074616,-0.01863529,0.01670115,-0.06017122,-0.00757798,0.00628288,-0.02131108,-0.11214892,0.12307893,-0.01691151,-0.00278033,0.01993009,-0.01437023,0.0360362,-0.06674173,0.0036077,-0.02718177,-0.02621359,0.0275552,-0.02071753,-0.05078164,-0.01529818,-0.0632537,-0.01869071,-0.05067011,-0.01452624,-0.00484668,0.01343404,-0.03150898,0.02705497,-0.00618844,0.01083747,0.011573,-0.00954656,0.04940521,0.0020022,0.03956162,0.01582712,0.02279395,0.06332016,0.01517645,-0.04769928,-0.02737849,-0.02816513,0.04594094,0.05854398,-0.06734603,0.03125756,-0.00631487,0.02520882,0.02496406,-0.01408218,-0.12260472,0.02661278,0.05060608,-0.02988872,0.00337303,-0.01253641,-0.06234659,0.03189439,0.0695225,-0.08238709,0.02258913,0.0456907,0.03767843,0.05241372,0.07969208,-0.0057871,0.04037838,-0.07613405,-0.03906148,-0.02260402,0.14186718,0.03379607,-0.05707212,-0.07742745,-0.00340191,-0.03718135,-0.02473203,0.03585925,0.05581282,-0.03163603,-0.01274554,0.06437021,0.05327384,-0.04910263,0.03836232,-0.00316845,0.03870617,0.04170293,-0.07231188,-0.05441343,0.00647924,-0.03007602,-0.03430772,0.00356705,-0.08092071,-0.02172244,0.00732977,-0.06697627,0.02816125,0.06566282,-0.01264673,-0.00820439,-0.02474771,-0.00337129,-0.06688982,0.07333242,-0.03074732,0.07844072,0.03151026,0.0417969,0.02764103,0.01019261,0.00820328,0.05969216,-0.03045522,0.09421141,0.03048853,-0.0952086,-0.03633508,0.08132228,0.02242618,-0.02544323,-0.02340951,-0.04848393,0.02499087,0.01410401,0.05289348,-0.04389648,-0.00179552,-0.02937648,-0.23563968,0.00329151,0.01796276,-0.03606962,0.02893265,-0.1046187,0.08797508,-0.03973694,-0.04681797,0.05015118,0.10056046,-0.01843493,0.04829456,-0.03040559,-0.01204204,-0.02178342,-0.02710569,-0.0050127,-0.00099744,0.03607849,-0.00242525,-0.00289366,0.02331109,-0.10184543,0.01315771,-0.02409624,0.14361183,0.04850182,0.0169413,-0.07007493,-0.01187588,0.00065025,0.0585399,-0.10847851,0.02211974,0.03215319,-0.01625052,0.04839923,0.09660033,0.01308569,-0.0472144,0.04244794,-0.0256722,-0.0372634,-0.00616198,-0.00896667,-0.05421485,-0.04072664,-0.02935767,0.01668835,0.02334364,-0.05054639,0.00569703,-0.00158463,-0.00572075,-0.02437655,-0.05092788,-0.06102129,0.018483,0.00253567,0.01575449,-0.00565748,-0.01285531,-0.04225004,0.0504032,0.04315869,0.01292089,0.032958,0.06634231,0.00771744,-0.01756083,0.10470251,0.03644257,0.07407546,0.00415929,0.04846112,-0.01090519,-0.0569706,-0.0054499,-0.03165666,-0.04007416,0.05322285,0.02118932,0.02255459,0.00967389,0.06677999,-0.00051266,-0.01881457,0.08330597,-0.03207456,-0.02081965,0.02910344,-0.00259876,-0.0047419,0.0478511,0.00267927,-0.23681676,0.02694737,-0.00132783,0.03150336,-0.02929384,0.04051706,0.04043547,0.00100299,-0.02256669,0.04532479,-0.05240272,0.01686458,0.00377662,-0.02726697,0.03518892,0.06618292,0.04119043,0.00733506,0.02893448,-0.07866721,0.03347118,0.02817607,0.22412172,-0.02006758,0.03713928,-0.01608628,-0.01297071,-0.06589521,0.06635217,0.04466147,0.01699089,0.02947076,0.15429984,0.02424817,0.06088372,0.00548848,-0.05385244,-0.01827059,0.02324427,0.00822607,-0.05420866,0.05621469,-0.04687546,0.00066388,0.03981782,-0.03969259,-0.02979723,-0.05877076,-0.03617865,0.03693769,-0.00817656,0.03543377,-0.0334442,0.00791008,0.00094635,-0.01648465,0.04781035,0.01589637,-0.07377987,-0.01869461,0.07906084,-0.01156912,0.08792856,-0.00941821,-0.02549697],"last_embed":{"hash":"21fb6c796f9901a6ea5d8db9e1d13c5a42f2cab4cf38a7aee98c0570e925ff58","tokens":467}}},"last_read":{"hash":"21fb6c796f9901a6ea5d8db9e1d13c5a42f2cab4cf38a7aee98c0570e925ff58","at":1745995226740},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/examples/working_with_aitemplates/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"​","target":"#Using-AITemplates","line":243},{"title":"Literate.jl","target":"https://github.com/fredrikekre/Literate.jl","line":399},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/examples/working_with_aitemplates.md","line":401},{"title":"Previous pageVarious examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":403},{"title":"Next pageLocal models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":405},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":407},{"title":"Icons8","target":"https://icons8.com","line":407},{"title":"**VitePress**","target":"https://vitepress.dev","line":407}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,242],"##Reference#{1}":[209,242],"#Using AITemplates [​](#Using-AITemplates)":[243,409],"#Using AITemplates [​](#Using-AITemplates)#{1}":[245,409],"#---frontmatter---":[397,null]},"last_import":{"mtime":1712726649534,"size":10136,"at":1740449883725,"hash":"21fb6c796f9901a6ea5d8db9e1d13c5a42f2cab4cf38a7aee98c0570e925ff58"},"key":"PromptingTools.jl/Using AITemplates - PromptingTools.jl.md"},