
"smart_sources:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md": {"path":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06420965,-0.07474255,-0.03688693,0.0000399,-0.07474591,0.05599096,0.05399273,0.01742072,0.07183518,0.03235029,0.00758715,0.01262899,0.04946443,0.01101746,-0.03403012,-0.02052976,-0.03300264,0.00827782,-0.05777106,0.03002059,0.09871044,-0.07033955,-0.04195448,-0.06480996,0.03338591,-0.0191651,0.00504769,-0.08572886,-0.06675844,-0.20655535,0.00253741,0.00758106,-0.00256418,-0.06370286,-0.06539495,-0.03500312,-0.05698967,0.07443034,-0.03675945,0.05133743,0.02987995,0.03939956,-0.02085157,-0.00916473,0.01904245,0.01127618,-0.03137441,-0.00758604,0.04532964,0.00604803,-0.05231484,0.07315651,0.01342148,0.06776804,0.07750859,0.00384522,0.06926715,0.09429969,0.00128858,0.00581478,0.05582976,0.02626959,-0.18982071,0.04198943,-0.01690855,0.01901783,0.00685843,0.01251433,0.02388627,0.03092269,0.00989706,0.00509164,-0.00667708,0.03892112,0.0224603,-0.05547513,-0.0087266,-0.03870798,-0.04232472,0.04893991,-0.01491685,-0.02617772,-0.03909936,-0.02647619,0.00250854,-0.02350189,0.0671814,0.05526858,0.03165826,-0.09329018,0.03095085,0.05583302,-0.00654015,0.01996542,0.03904488,0.03781974,0.08024737,-0.00872321,-0.01145554,0.10346314,-0.00010538,0.00275132,-0.04286128,0.02186622,0.0586741,-0.05524634,-0.0271375,-0.0178334,-0.0298858,0.00492217,0.03335198,0.02086941,0.08549835,-0.06529886,-0.01010917,0.00570307,0.04357392,0.05062377,0.0476286,-0.00978198,-0.01202134,0.05946247,0.05137349,-0.0391819,0.0346055,-0.00512796,0.02025251,0.0534946,0.02944627,0.06656037,0.04578539,-0.01808334,-0.12689735,-0.02407388,-0.01112117,0.00004778,0.00823112,0.01224358,0.0094317,0.0060729,0.01261191,-0.00682736,0.03112772,-0.09187047,0.00409543,0.05384123,0.04711591,0.04513209,0.05446629,-0.00404689,0.00166547,-0.0299239,-0.05908654,-0.08155156,-0.03011142,0.00277729,0.07437931,0.06445232,-0.0323236,0.00005232,0.02916849,-0.05790154,-0.00326906,0.07671206,-0.02631335,-0.03887425,-0.06637657,-0.02003717,-0.02142086,-0.11140206,-0.03953179,-0.01265428,-0.02775304,-0.00604715,0.063792,-0.01436235,-0.08415366,-0.07580381,-0.01146648,0.03467985,0.02141601,-0.001662,-0.01648937,-0.01962927,-0.04968021,-0.04576946,0.01155797,-0.0144919,0.0321526,0.030004,-0.00634451,-0.00540087,-0.07684263,0.04347759,0.00117262,0.00606493,-0.03795834,-0.02265546,0.05464879,-0.00720421,-0.04868573,-0.05221069,-0.00130169,0.02712858,-0.03851096,0.0378255,0.00897616,-0.07203319,0.08341811,0.02086024,-0.03376523,-0.02283921,0.01685127,0.04230326,-0.02055108,0.03992682,0.0371081,0.0350315,0.03685106,0.03654576,0.02716621,-0.05186635,-0.05805695,-0.18610032,-0.0683915,-0.02996378,-0.07663623,0.05937698,-0.0431634,-0.00879045,-0.06889432,0.02436842,0.08138413,0.05026126,-0.0507691,-0.02355259,0.05016917,-0.00029772,0.03345702,-0.11309129,-0.02726843,-0.03524608,0.05612312,-0.00626204,0.04492713,-0.03301818,-0.07726919,0.03318862,-0.02487189,0.15260044,0.01192105,-0.00836326,-0.01330744,0.04063116,0.04847278,-0.038584,-0.00558645,0.00869891,0.01429562,-0.06715663,0.01165845,-0.06276461,0.00159257,-0.07951748,0.01281304,0.02964751,-0.06112458,-0.07826494,0.01063725,-0.04609097,0.05912226,-0.00622634,0.11217991,0.05123654,0.00104753,0.08384099,-0.00753698,0.05271687,-0.03954813,-0.06277064,-0.00399308,-0.03588285,0.06452332,-0.00874763,-0.0493474,0.06830211,-0.03582668,0.08434189,-0.02570219,-0.00984039,-0.00683774,0.00044777,-0.01460078,-0.0084953,0.06799757,0.01220907,-0.02543181,0.02757615,0.0130298,0.06971557,-0.02433121,0.00234753,-0.00629568,0.0314301,-0.11505678,0.04275326,0.04727554,0.07573491,0.02106952,0.03385094,0.06104456,0.00648807,-0.0139997,-0.03328018,0.02252447,-0.05205249,-0.02155347,-0.04050254,-0.01076711,-0.23641473,0.03850156,-0.04331207,0.05621601,-0.03452688,-0.05432137,-0.00162191,0.03049938,-0.00998974,-0.04504779,0.08750877,0.03629415,0.01155782,-0.04786476,-0.00845346,-0.01703117,-0.03801521,-0.01671069,0.06913385,0.06574599,0.1164161,0.03604981,0.22351728,-0.02791524,-0.00404129,-0.03822051,0.00784524,0.01590479,0.00856479,0.01541114,0.00290485,0.00454468,0.07835111,-0.01823814,0.01470575,0.02685026,-0.0434742,0.05577876,-0.04534468,-0.01584173,-0.02767498,-0.04154756,-0.04305045,0.05391454,0.14755961,0.04621807,-0.03587084,-0.06291176,0.0408774,0.03775378,-0.06787717,-0.040764,-0.01099895,-0.0759263,-0.01968665,0.04360994,-0.01551654,-0.01801492,0.04489461,0.00106273,0.00841469,0.05104918,0.05545525,-0.01182787,0.00141918],"last_embed":{"hash":"b1bg8w","tokens":451}}},"last_read":{"hash":"b1bg8w","at":1753423485585},"class_name":"SmartSource","last_import":{"mtime":1753363612274,"size":11801,"at":1753423416052,"hash":"b1bg8w"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage":[8,116],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{1}":[10,13],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{2}":[14,14],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{3}":[15,15],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{4}":[16,16],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{5}":[17,17],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{6}":[18,19],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{7}":[20,70],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{8}":[71,71],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{9}":[72,72],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{10}":[73,74],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{11}":[75,83],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies":[84,87],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{1}":[86,87],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.":[88,116],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{1}":[90,91],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{2}":[92,92],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{3}":[93,93],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{4}":[94,94],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{5}":[95,95],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{6}":[96,96],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{7}":[97,97],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{8}":[98,98],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{9}":[99,99],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{10}":[100,100],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{11}":[101,101],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{12}":[102,102],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{13}":[103,103],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{14}":[104,104],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{15}":[105,106],"#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{16}":[107,116]},"outlinks":[{"title":"_**Lottery Numbers from PI, PHI (Divine Proportion)**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/07gM1VnVvFM","line":15},{"title":"_**Pick 3 <u>Straight</u> Sets Statistics**_","target":"https://saliu.com/freeware/Pick3-Straight-Stats.html","line":17},{"title":"_**roulette systems, magic numbers**_","target":"https://download.saliu.com/roulette-systems.html","line":18},{"title":"The best lottery software covers 5, 6, 7-number lotto jackpot games.","target":"https://saliu.com/ScreenImgs/lottery-software.gif","line":75},{"title":"Ion Saliu's Theory of Probability Book founded on mathematics applied to strategy, systems for lottery, pick-3-4-5 lotteries.","target":"https://saliu.com/probability-book-Saliu.jpg","line":77},{"title":"**Read Ion Saliu's book:** _**Probability Theory, Live!**_","target":"https://saliu.com/probability-book.html","line":77},{"title":"Losing big money in lottery is guaranteed if playing randomly without systems.","target":"https://saliu.com/HLINE.gif","line":80},{"title":"\n\n## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies\n\n","target":"https://saliu.com/content/lottery.html","line":82},{"title":"\n\n## The Best Ever Lottery Strategy, Lotto Strategies.\n\n","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":86},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":92},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":94},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":96},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":97},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":99},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":100},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":101},{"title":"_**The Best Strategy in Lottery, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":102},{"title":"_**Software, Formulas to Calculate Lotto Odds**_","target":"https://saliu.com/oddslotto.html","line":103},{"title":"_**Lotto, Lottery, Balls, Memory, Probability Laws, Rules of Randomness**_","target":"https://saliu.com/bbs/messages/575.html","line":104},{"title":"**_Lottery Software: Lotto, Pick 3 4, Powerball, Mega Millions, Euromillions, Keno_**","target":"https://saliu.com/free-lotto-lottery.html","line":105},{"title":"Losses amount to thousands of dollars in a lifetime for just one lottery game like pick-3.","target":"https://saliu.com/HLINE.gif","line":111},{"title":"Forums","target":"https://forums.saliu.com/","line":113},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":113},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":113},{"title":"Contents","target":"https://saliu.com/content/index.html","line":113},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":113},{"title":"Home","target":"https://saliu.com/index.htm","line":113},{"title":"Search","target":"https://saliu.com/Search.htm","line":113},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":113},{"title":"You can win the lottery only if playing systems, strategies with good lottery software.","target":"https://saliu.com/HLINE.gif","line":115}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["lottery","numbers","loss","drawings","draws","strategies","systems","winning the lottery"],"source":"https://saliu.com/lottery-numbers-loss.html","author":null}},"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05879728,-0.07478026,-0.03525832,-0.00337961,-0.07373075,0.05901027,0.05848372,0.0138789,0.0686584,0.02020726,0.00695651,0.01003762,0.05747567,0.01739969,-0.04247075,0.00312466,-0.0323041,0.0118289,-0.06450197,0.02567665,0.09222318,-0.06864247,-0.03377676,-0.06775624,0.03491642,-0.01650676,-0.0043854,-0.08809311,-0.07196049,-0.20879272,-0.00340052,0.0038101,-0.00056041,-0.07225884,-0.06613539,-0.03387514,-0.0574688,0.0694196,-0.04117824,0.04698449,0.03119189,0.02664847,-0.00452786,-0.01200581,0.02758768,0.01026794,-0.01813655,-0.01099421,0.0515983,0.00450402,-0.05343528,0.06764458,0.01806378,0.07188294,0.06092114,0.01194921,0.0679497,0.09378462,-0.00464946,0.00944698,0.04994353,0.03488605,-0.17514794,0.04860827,-0.01998868,0.01597056,0.0010603,0.00190803,0.01727904,0.03177457,0.0115854,0.02324014,-0.00959292,0.04219533,0.01862256,-0.06226918,-0.0049021,-0.03065971,-0.04048925,0.05500355,-0.03003499,-0.01781967,-0.03704535,-0.03583224,0.00456749,-0.03207769,0.06977105,0.04004966,0.02694048,-0.08075133,0.02937748,0.07441511,-0.01886361,0.01568693,0.05241339,0.03536671,0.07877981,-0.01269193,-0.00866834,0.11192885,-0.00160004,0.00287337,-0.06336851,0.02950403,0.05122682,-0.05576342,-0.0253335,-0.0122717,-0.03500893,0.00978634,0.03565527,0.01536991,0.08633196,-0.0662716,-0.01241404,0.01119647,0.03156298,0.05523847,0.05408541,-0.01342565,-0.01245159,0.06422128,0.06096356,-0.03756592,0.03408757,-0.0066358,0.02085087,0.05007372,0.02365647,0.06550055,0.04249927,-0.0109031,-0.1261287,-0.02118631,-0.0209019,0.0024591,0.0048902,0.01556696,0.00737926,0.00167643,0.0136902,-0.01436238,0.04932173,-0.08780996,0.00829786,0.0527507,0.03446285,0.0423597,0.05946429,-0.00612218,0.00343401,-0.03604343,-0.06206018,-0.08357798,-0.01957828,0.00006827,0.07784423,0.05914601,-0.02936484,0.0063702,0.02114746,-0.05931976,0.01262873,0.06639119,-0.03198159,-0.02327299,-0.06284302,-0.01898951,-0.02193521,-0.1004805,-0.03538868,-0.0087222,-0.0285,-0.00656134,0.04664559,-0.01133017,-0.08737659,-0.08107724,0.00249119,0.03308313,0.02005972,-0.00454344,-0.0110849,-0.01018478,-0.04854402,-0.04254694,0.02042352,-0.01422015,0.02503701,0.01892174,-0.00379405,-0.01214053,-0.08391607,0.03534321,0.00953438,0.00010815,-0.04055869,-0.02725031,0.0556906,-0.00463149,-0.06075422,-0.05479023,0.00481488,0.03690045,-0.03006658,0.05059643,0.00431938,-0.07443196,0.09286582,0.01906102,-0.03510688,-0.02791834,0.0079486,0.04090738,-0.02239444,0.04365017,0.03785797,0.03451052,0.04143936,0.03863991,0.03713511,-0.05031579,-0.05214995,-0.18725041,-0.06559048,-0.03242666,-0.07210965,0.06388612,-0.03520602,-0.01002471,-0.06289504,0.03671525,0.08369828,0.0523714,-0.05079156,-0.02874966,0.05689656,-0.00058338,0.03850718,-0.11883174,-0.02957589,-0.04430631,0.0509481,-0.0072604,0.03450699,-0.04286887,-0.07995757,0.0470056,-0.01981389,0.14911999,0.01351674,-0.01113034,-0.02192759,0.03451287,0.0457481,-0.03474541,-0.00331335,0.0166467,0.01184713,-0.06188889,-0.00369368,-0.06538277,0.00902369,-0.06684079,0.0262285,0.03039462,-0.06331684,-0.08148124,0.0004303,-0.0484965,0.06819642,0.00751346,0.11237464,0.05070939,0.00326525,0.08379309,-0.0079686,0.04566601,-0.03539212,-0.05473911,-0.00166656,-0.03211724,0.06213874,-0.00797763,-0.05055762,0.06563908,-0.04675473,0.08339073,-0.01976031,-0.01671523,-0.01170067,0.01088196,-0.01394014,-0.01456813,0.06381597,0.00600111,-0.02380317,0.03467803,0.01133879,0.0626673,-0.02723728,0.00038195,-0.01350506,0.03413735,-0.10723244,0.0377181,0.04983161,0.07186286,0.00386571,0.04714406,0.0738802,0.0031457,-0.00530882,-0.01442755,0.02145947,-0.03594705,-0.02629595,-0.0454974,-0.01021026,-0.23205963,0.0319148,-0.05795572,0.0605873,-0.0300249,-0.06075715,0.00003457,0.03214869,-0.02242489,-0.04344597,0.08604156,0.03394866,0.02044896,-0.05946872,-0.01123269,-0.0253802,-0.03028722,-0.03021898,0.06517788,0.05330431,0.1348962,0.0281434,0.22223142,-0.02182678,0.00151055,-0.02718538,0.01230325,0.02655063,0.00740289,0.00821055,0.00243541,-0.00848166,0.0741754,-0.02545043,0.01286922,0.0279122,-0.04303301,0.05743493,-0.04263941,-0.02837954,-0.02770507,-0.04263242,-0.04977551,0.04932155,0.14304355,0.0352694,-0.03439908,-0.05292499,0.03983916,0.03692802,-0.0652876,-0.04430883,-0.01854793,-0.07862358,-0.01717734,0.03751993,-0.01232898,-0.01819834,0.04304209,0.00332988,0.01058952,0.06288023,0.06505459,-0.01121676,-0.00798066],"last_embed":{"hash":"cccmfk","tokens":435}}},"text":null,"length":0,"last_read":{"hash":"cccmfk","at":1753423484700},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage","lines":[8,116],"size":11593,"outlinks":[{"title":"_**Lottery Numbers from PI, PHI (Divine Proportion)**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/07gM1VnVvFM","line":8},{"title":"_**Pick 3 <u>Straight</u> Sets Statistics**_","target":"https://saliu.com/freeware/Pick3-Straight-Stats.html","line":10},{"title":"_**roulette systems, magic numbers**_","target":"https://download.saliu.com/roulette-systems.html","line":11},{"title":"The best lottery software covers 5, 6, 7-number lotto jackpot games.","target":"https://saliu.com/ScreenImgs/lottery-software.gif","line":68},{"title":"Ion Saliu's Theory of Probability Book founded on mathematics applied to strategy, systems for lottery, pick-3-4-5 lotteries.","target":"https://saliu.com/probability-book-Saliu.jpg","line":70},{"title":"**Read Ion Saliu's book:** _**Probability Theory, Live!**_","target":"https://saliu.com/probability-book.html","line":70},{"title":"Losing big money in lottery is guaranteed if playing randomly without systems.","target":"https://saliu.com/HLINE.gif","line":73},{"title":"\n\n## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies\n\n","target":"https://saliu.com/content/lottery.html","line":75},{"title":"\n\n## The Best Ever Lottery Strategy, Lotto Strategies.\n\n","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":79},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":85},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":87},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":89},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":90},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":92},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":93},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":94},{"title":"_**The Best Strategy in Lottery, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":95},{"title":"_**Software, Formulas to Calculate Lotto Odds**_","target":"https://saliu.com/oddslotto.html","line":96},{"title":"_**Lotto, Lottery, Balls, Memory, Probability Laws, Rules of Randomness**_","target":"https://saliu.com/bbs/messages/575.html","line":97},{"title":"**_Lottery Software: Lotto, Pick 3 4, Powerball, Mega Millions, Euromillions, Keno_**","target":"https://saliu.com/free-lotto-lottery.html","line":98},{"title":"Losses amount to thousands of dollars in a lifetime for just one lottery game like pick-3.","target":"https://saliu.com/HLINE.gif","line":104},{"title":"Forums","target":"https://forums.saliu.com/","line":106},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":106},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":106},{"title":"Contents","target":"https://saliu.com/content/index.html","line":106},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":106},{"title":"Home","target":"https://saliu.com/index.htm","line":106},{"title":"Search","target":"https://saliu.com/Search.htm","line":106},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":106},{"title":"You can win the lottery only if playing systems, strategies with good lottery software.","target":"https://saliu.com/HLINE.gif","line":108}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07153002,-0.05984873,-0.0324333,0.00787574,-0.07358316,0.05527222,0.04745486,0.00673407,0.05264186,0.00845869,0.01430346,0.01512777,0.06395408,0.01531332,-0.02015337,-0.02487252,-0.01934463,0.03555936,-0.05494685,0.01581338,0.06391457,-0.04983371,-0.03918982,-0.07826205,0.03967064,-0.03260398,-0.00960006,-0.08587565,-0.05274077,-0.16358057,0.01586233,0.00622729,-0.00880908,-0.07186748,-0.06401002,-0.04973738,-0.05826372,0.05241521,-0.06025662,0.04897342,0.02462562,0.02709436,-0.03244057,-0.02234603,0.00682273,-0.02308441,0.0075033,0.00221894,0.05197815,0.00370641,-0.04540649,0.07653219,-0.01359937,0.07116014,0.06160894,0.01863107,0.06380528,0.1208587,-0.02227543,0.00013738,0.05764594,0.02412431,-0.17179057,0.02917137,-0.00471771,0.02638175,0.00686063,0.03007895,0.0084899,0.06333564,0.02051752,-0.00541299,-0.00089898,0.06200165,0.02411802,-0.0336601,-0.03434011,-0.04453284,-0.02767269,0.04742946,-0.02242746,-0.05432378,-0.00792813,0.00187261,-0.00625153,-0.01572254,0.06225986,0.04154995,0.0561181,-0.09389226,0.01568,0.0573867,-0.00100962,0.0424475,0.04472648,0.01764903,0.0807832,-0.03213077,-0.00737679,0.14811164,-0.01897414,-0.02659668,-0.03298562,0.02106814,0.06833152,-0.06108674,-0.02999202,-0.01671469,-0.01451648,0.00640154,0.0431778,0.02997286,0.07315287,-0.04670562,-0.04017406,0.00039131,0.01475925,0.02363256,0.05293861,0.00078053,-0.02827528,0.05166459,0.0397847,-0.03889337,0.01830732,-0.01594467,0.02807866,0.05768258,0.01087765,0.05954128,0.05152818,-0.0239405,-0.12056535,-0.02349857,-0.00314108,-0.00419481,0.00918181,-0.01461511,-0.01820797,0.01066681,0.01230584,0.01773808,0.04054811,-0.11887781,0.00784278,0.06901497,0.05692486,0.04122073,0.04128486,-0.0178187,-0.00382426,-0.03091788,-0.02113481,-0.09364615,-0.04119807,0.00429099,0.07569029,0.04821947,-0.03415552,-0.00802789,0.01391341,-0.02735187,-0.01176555,0.1102575,-0.03408454,-0.04252106,-0.04455703,-0.00113218,-0.03650822,-0.08992747,-0.05054875,-0.00261764,-0.0346366,-0.0006394,0.07038533,0.00099884,-0.08649657,-0.06842191,-0.02886136,0.00206785,0.01021374,0.00644176,-0.00134356,-0.0097405,-0.02491273,-0.04399597,-0.00444988,-0.01162405,0.05720086,0.06823011,-0.01718854,0.01535559,-0.06985132,0.02064135,-0.0073074,-0.0114964,-0.03250017,-0.03587477,0.04973378,-0.03935589,-0.04940009,-0.04297044,0.00813567,0.0463447,-0.00828054,0.01229194,-0.00525056,-0.08190973,0.08564038,0.03214233,-0.05623179,0.00071259,0.05129272,0.05853138,-0.01255561,0.0269781,0.03478523,0.045463,0.04223811,0.04318345,0.010235,-0.00480672,-0.0503341,-0.20716521,-0.05796303,-0.04705331,-0.0566586,0.04632834,-0.05621736,-0.02737559,-0.07005114,-0.00823772,0.0808906,0.08703738,-0.10493582,0.00115899,0.05245862,-0.00280283,0.00719178,-0.09673822,-0.04901836,-0.04795196,0.05678728,-0.00335977,0.03384621,-0.01651288,-0.09067247,0.01795589,-0.02831573,0.13549601,-0.03623306,0.02444418,-0.01560233,0.05238375,0.03988985,-0.04230208,0.00763026,0.01728392,0.05039202,-0.09233412,0.0064854,-0.05541956,-0.01019387,-0.0617457,0.01225756,0.0172984,-0.04913539,-0.05956478,-0.00192176,-0.03804038,0.04617509,-0.01367013,0.10064214,0.02987745,0.02671838,0.07583246,0.01126856,0.07788709,-0.03727454,-0.05864971,0.03160924,-0.05406409,0.03029049,-0.00292384,-0.07827231,0.06072763,-0.04410186,0.08484437,-0.01787484,-0.02349714,-0.03033585,-0.00423762,0.00145436,-0.00801306,0.04576277,-0.01694945,-0.02190899,-0.00870992,0.02022093,0.06130188,-0.03169771,-0.04526419,-0.00346237,0.04070837,-0.09800851,0.04059976,0.0636,0.09624678,0.04252525,0.04438166,0.0673596,-0.00849634,0.00103508,-0.02425954,0.03004049,-0.07095468,-0.00126419,-0.02272378,-0.01306076,-0.22433299,0.03449968,-0.04802755,0.05124959,-0.04826097,-0.05133702,0.00276449,0.02976782,-0.01045262,-0.04556494,0.0799701,0.03532378,-0.01601168,-0.04508389,-0.0121376,-0.00081386,0.01682013,-0.02829269,0.07636531,0.08371475,0.08265667,0.05614771,0.2391161,-0.00190732,0.01005445,-0.02056691,0.02625224,0.03388951,0.02359907,0.04421139,0.03601506,-0.0136368,0.08521485,0.01037831,-0.00961318,0.01819026,-0.03369335,0.04862406,-0.01617941,0.01572707,-0.04013453,-0.01905308,-0.05448363,0.04525283,0.12080073,0.04612213,-0.04459029,-0.06242425,0.02100745,0.04165291,-0.08069109,-0.04091207,-0.0229829,-0.0776445,0.0116518,0.02117993,-0.02441132,-0.03923607,0.03585728,-0.00979433,0.02150935,0.04348935,0.0395453,-0.01738739,-0.01022502],"last_embed":{"hash":"w21au3","tokens":66}}},"text":null,"length":0,"last_read":{"hash":"w21au3","at":1753423484846},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{1}","lines":[10,13],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03515214,-0.08828973,-0.01737429,-0.01162087,-0.07267206,0.09174926,0.0792656,-0.00540784,0.06345036,0.00048198,0.0209421,0.0044624,0.03622205,0.03532481,-0.01784636,-0.02455035,-0.06220304,0.02454937,-0.06588353,0.01156967,0.09590659,-0.05947039,-0.04189397,-0.06083642,0.01980549,-0.03433168,0.00675411,-0.09587124,-0.06001529,-0.17537645,0.01736357,0.00732819,-0.01487246,-0.06499933,-0.07678796,-0.05104587,-0.06224986,0.05394432,-0.04021671,0.0608939,0.04303855,0.03740317,-0.04014797,-0.03997618,-0.0213714,-0.01812957,0.01060034,-0.02466382,0.03031109,0.01584348,-0.03932763,0.06914409,0.00358033,0.0608495,0.06641233,0.00024667,0.06478318,0.10023271,-0.0104155,0.0066965,0.06125718,0.01391811,-0.16634378,0.00988028,0.01622308,-0.00387566,0.00871477,0.01833879,0.00682043,0.08038956,0.03658595,-0.01802057,0.00966364,0.05197565,0.01862903,-0.0484355,-0.0204438,-0.04292662,-0.01311144,0.04414695,-0.03824007,-0.03802184,-0.04123521,-0.0300352,-0.01044603,-0.03164447,0.05033428,0.021037,0.01794861,-0.0584784,0.00030488,0.06947242,-0.0058485,0.04429967,0.02155836,0.02132277,0.06601961,-0.00953228,-0.02198345,0.1373968,-0.00776146,-0.01310073,-0.01595873,0.01313545,0.04230326,-0.07333282,-0.03683023,-0.02115463,-0.02251743,0.01337571,0.0226511,0.00716726,0.06351274,-0.02539387,-0.01977411,0.01136266,0.04862715,0.03783084,0.06715899,-0.00014164,-0.0059571,0.06036453,0.07338687,-0.03236447,0.00786049,-0.01955945,0.02617851,0.06942382,0.0146768,0.02737412,0.07749719,-0.05301891,-0.08630977,-0.01785338,0.00997381,-0.01901846,0.03147019,0.0284182,-0.01678219,0.00538439,0.0353013,-0.00869979,0.03585845,-0.08370241,-0.0184036,0.05297353,0.0505793,0.02869158,0.05084445,0.01284955,-0.01066524,-0.02131601,-0.05510347,-0.08230806,-0.04324321,0.01437089,0.04250411,0.04945261,-0.01762268,-0.01288521,0.01289832,-0.05599786,-0.0428048,0.09341349,-0.06215999,-0.02976162,-0.06012972,-0.01935268,-0.03943566,-0.09416916,-0.02648502,-0.01492788,-0.03707948,-0.00848655,0.06307624,0.0059157,-0.06734814,-0.0913967,-0.04083839,0.02217359,0.01394727,0.01144237,0.00221537,-0.00152545,-0.03087555,-0.02496128,-0.01370426,-0.00671431,0.04976926,0.0138662,-0.02565172,0.01477342,-0.10327154,0.03386704,0.00642524,0.00689425,-0.02819971,-0.03237457,0.04630467,-0.02266283,-0.03012046,-0.04140893,0.01073225,0.06064327,-0.02750402,0.02810649,0.00204238,-0.04980952,0.08612259,0.02067081,-0.04012592,-0.00861716,0.04122469,0.05668486,-0.00675453,0.01325972,0.04697065,0.00626499,0.04420263,0.00788432,0.01281222,-0.01931935,-0.01179518,-0.20544413,-0.04570255,-0.05084642,-0.09156389,0.05522511,-0.03139437,-0.0152376,-0.05144479,0.00986635,0.08201998,0.05928771,-0.09830309,-0.01920187,0.05360962,-0.01176392,0.00755254,-0.09043399,-0.05208956,-0.04324302,0.0641137,-0.02162893,0.05973583,-0.04366587,-0.08266773,0.01451417,-0.01461037,0.15447953,0.02136972,0.02623655,-0.01001645,0.02323537,0.07101279,-0.01950076,0.02021277,-0.00003411,0.03397948,-0.0819007,-0.02776613,-0.08579117,-0.03157953,-0.03781845,0.01808592,0.02753038,-0.05198084,-0.06764352,0.01623331,-0.04794912,0.06903183,-0.01489577,0.1312101,0.06301019,-0.00888449,0.06580341,0.02804265,0.08194379,-0.01743553,-0.06101571,0.00384256,-0.03377421,0.04378723,0.01500704,-0.03612359,0.08756337,-0.06113917,0.07923318,-0.01443519,-0.04469106,-0.05049482,0.00122687,-0.01384085,-0.00258075,0.06318379,-0.00341576,-0.04630708,-0.00206028,0.00649676,0.06064186,-0.00213529,-0.0347787,-0.01388771,0.03102979,-0.10829081,0.01484317,0.0291593,0.06391428,0.05518179,0.0379315,0.04686714,-0.01732337,0.00532627,-0.00942077,0.02969764,-0.03791647,-0.01903406,-0.03932478,-0.02733053,-0.23123729,0.06147947,-0.01652211,0.04929068,-0.05092632,-0.04174281,-0.01408484,0.0545599,-0.01285479,-0.04809086,0.099268,0.05478101,-0.00537159,-0.0213338,0.0130709,-0.03256565,0.01332351,-0.04333142,0.08507156,0.04169871,0.11112063,0.04750691,0.23681128,-0.00682675,-0.01143008,-0.02036923,0.02879214,0.03266738,0.01012071,0.01943785,0.02314143,-0.00406653,0.07438978,-0.011795,0.03177503,0.02423412,-0.03455589,0.06679871,-0.02314425,-0.02745372,-0.03306324,-0.01753571,-0.05289418,0.07350963,0.12391838,0.07514416,-0.0386499,-0.03057627,0.03264366,0.02745537,-0.05522848,-0.01535363,-0.01262518,-0.08263411,-0.02204323,0.01106427,-0.02521789,-0.02065322,0.04640265,-0.02802345,0.00761355,0.06030816,0.05179357,-0.03149203,0.01171753],"last_embed":{"hash":"16vgeph","tokens":76}}},"text":null,"length":0,"last_read":{"hash":"16vgeph","at":1753423484874},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{2}","lines":[14,14],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06316777,-0.0687924,-0.00719316,-0.01328803,-0.07629618,0.04293292,0.07622561,0.01798108,0.05849316,0.02768476,0.0177426,0.00877006,0.04878679,0.03894208,-0.02473827,-0.00046984,-0.02158148,-0.00619603,-0.08518145,0.01959821,0.09381455,-0.06501873,-0.03411467,-0.0436824,0.04299293,-0.01693234,-0.0255663,-0.08162487,-0.06955099,-0.17257094,-0.00757698,0.0217825,0.00369898,-0.07851293,-0.05604297,-0.0490189,-0.04760102,0.06723063,-0.0583529,0.04207888,0.04260145,-0.00135943,-0.01106133,-0.00047431,0.00635284,0.01997852,0.00155137,0.00529278,0.05076127,0.00687941,-0.02193881,0.07813155,0.01901269,0.07871582,0.02821009,-0.00244185,0.05143696,0.07768876,-0.01326171,0.01322723,0.05007952,0.05125194,-0.18369538,0.04524928,-0.02873672,0.01453061,0.01533265,-0.0086819,0.02396497,-0.00015778,-0.00145887,0.01236082,-0.01068665,0.03850167,0.03351392,-0.06299908,-0.03028333,-0.04583806,-0.05112577,0.0442359,-0.03974655,-0.00955044,-0.045731,-0.04983494,0.02664595,-0.03960319,0.08474004,0.04614769,0.03310576,-0.07436946,0.02430109,0.05730181,-0.01314365,0.04114994,0.04782781,0.02146243,0.07052432,-0.03640689,-0.00835187,0.13714321,-0.02512574,0.02304909,-0.05466738,0.02241677,0.062799,-0.049115,-0.02096298,-0.03710041,-0.04177078,-0.00876228,0.0347336,0.03956629,0.08368556,-0.08201931,-0.01404977,-0.03007719,0.01674368,0.02477199,0.05929761,-0.00757525,0.00023426,0.01853029,0.06346947,-0.07087923,0.03724094,0.01284724,0.01946523,0.01833117,0.00724736,0.08131537,0.04279012,-0.01053124,-0.11717762,-0.02650099,-0.02463372,0.02325419,0.00688032,-0.01849345,0.00427782,-0.02663653,0.03298676,-0.02103201,0.01147833,-0.0702024,0.03321647,0.04268314,-0.00380608,0.05187815,0.05800507,0.02247727,-0.00960858,-0.04286038,-0.04997265,-0.05739818,-0.01419301,-0.00371623,0.06850993,0.06777186,-0.01559684,0.03140461,0.06773277,-0.05894847,0.01803363,0.09521877,-0.02763244,-0.04638785,-0.08239064,-0.01862468,-0.0220764,-0.10151127,-0.04185479,0.00223304,-0.02991543,0.03456683,0.08186656,-0.01383105,-0.04890633,-0.08360847,-0.02892192,0.03306781,0.00578605,-0.02991863,-0.02422648,-0.00805062,-0.06147379,-0.03719633,0.03349553,-0.00251147,0.00912722,0.02918803,-0.00010796,-0.00201821,-0.04979689,0.02039069,0.02378584,0.00981425,-0.03532193,0.00948805,0.05571716,-0.02497928,-0.0601513,-0.04785516,0.01228368,0.04263423,-0.02795462,0.03160306,0.00166464,-0.05212214,0.07647861,0.02462035,-0.03175372,-0.02302335,0.03171688,0.02702566,-0.03967829,0.04595366,0.00874043,0.03418552,0.04938083,0.01418099,0.05191593,-0.07481509,-0.08682596,-0.17278396,-0.05288013,-0.02088903,-0.09614567,0.06161829,-0.05007536,-0.00432868,-0.07267426,0.04320209,0.10726984,0.04938602,-0.06138111,-0.02214023,0.05379741,0.00648177,0.06792201,-0.09906266,-0.04262913,0.00206203,0.03154167,-0.00119016,0.0474362,-0.00576867,-0.07828549,0.04776853,-0.01605916,0.14335419,0.03854867,-0.0231429,-0.03116917,0.03378671,0.03088912,-0.02256509,0.01042092,0.0188919,-0.01300269,-0.07768515,0.00258725,-0.04986554,0.00303991,-0.06245802,0.02661894,0.04980385,-0.06754819,-0.04809154,-0.03470756,-0.02498914,0.05742673,0.02351881,0.11144975,0.02514369,-0.00267267,0.06548653,0.00456362,0.06206284,-0.03074975,-0.05598389,-0.03130367,-0.01963533,0.05010555,-0.00927508,-0.05817744,0.06348733,-0.04952734,0.04433906,-0.03246684,-0.05263046,0.0040784,0.00408207,0.00164213,-0.01757708,0.08514365,0.01826853,-0.02744485,0.00751048,-0.00030197,0.06391022,-0.03313649,-0.00046169,-0.01323298,0.02371981,-0.11491297,0.0265496,0.03342262,0.07251266,0.00014646,0.0457425,0.08302709,0.01784419,-0.01527138,-0.04124217,0.02409973,-0.02611322,-0.00917768,-0.02488233,-0.01876086,-0.22136672,0.04542167,-0.08023164,0.04911191,-0.00205133,-0.05572179,-0.03794856,0.0393827,-0.03034376,-0.05307441,0.09135155,0.01323813,0.03493903,-0.04533622,-0.02499299,-0.00000842,-0.03750196,-0.01472775,0.0612574,0.08213782,0.13026728,0.03291315,0.22007601,0.00626371,-0.00441817,-0.0424957,-0.00251234,0.03042105,0.00643868,0.0062799,-0.00061098,-0.0158486,0.09513523,-0.03885635,0.03228246,0.04647098,-0.02589029,0.04019728,-0.04674961,-0.01861619,-0.01899477,-0.03127859,-0.11051066,0.06088661,0.1388047,0.02247797,-0.05235433,-0.03867904,0.03823638,0.01396532,-0.03867482,-0.05527097,-0.03620465,-0.04107911,-0.01083084,0.0459551,-0.01823846,-0.01208834,0.04424472,0.00462558,0.01713603,0.06250323,0.072179,-0.00036888,-0.01028103],"last_embed":{"hash":"1fgeiku","tokens":205}}},"text":null,"length":0,"last_read":{"hash":"1fgeiku","at":1753423484904},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{3}","lines":[15,15],"size":585,"outlinks":[{"title":"_**Lottery Numbers from PI, PHI (Divine Proportion)**_","target":"https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/07gM1VnVvFM","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02189752,-0.07490046,-0.01214149,-0.00788907,-0.06295205,0.08277082,0.07144354,-0.00357294,0.07142854,0.04654427,0.01687944,0.01009755,0.02962614,0.01321154,-0.02452565,-0.06011158,-0.04236518,0.00383326,-0.09381236,0.02970947,0.08488262,-0.02966346,-0.05222554,-0.07343741,0.02686206,-0.01263256,-0.03027603,-0.10275014,-0.06142588,-0.16020916,0.03187351,0.00575755,0.00650938,-0.03287898,-0.06671184,-0.02927774,-0.0531905,0.06706031,-0.0240509,0.05389769,0.04152381,0.03954279,-0.03121269,-0.04930679,-0.01518749,-0.00461742,-0.03039657,0.00667424,0.05292459,0.04329963,0.00775314,0.05269635,0.00098779,0.06134391,0.06936369,-0.03923441,0.08372597,0.07383546,0.00949447,-0.0103884,0.03610757,0.01827993,-0.18844411,-0.00264658,-0.02688551,-0.0074459,0.00595336,0.01938203,0.03526774,0.06648833,0.00285036,-0.0399722,-0.01152873,0.04773171,0.02761785,-0.07358101,-0.03672818,-0.04364974,-0.03021783,0.01586798,-0.01944303,-0.03407854,-0.02136132,0.01050436,-0.00525383,-0.01021681,0.0634165,0.04863255,0.03111428,-0.05639118,0.01416737,0.0490056,-0.02641133,0.02665464,0.02163226,0.02709726,0.07841922,-0.02815574,-0.0277122,0.15661635,0.00811542,-0.01222836,-0.00842988,0.03609208,0.03436845,-0.07858239,-0.01505386,0.00349784,-0.01555877,0.00163324,0.02611576,0.04414191,0.04755628,-0.01965807,0.00210284,-0.01073436,0.04313696,0.0283756,0.02469166,0.02209742,0.00145415,0.03025734,0.02939917,-0.01627913,0.0489166,-0.02352943,0.0319088,0.06815658,0.03118129,0.0609992,0.04693126,-0.03476791,-0.11340104,-0.00970859,0.01492666,-0.00326755,0.04191412,0.01796004,-0.01389702,0.01908964,0.04544023,0.02560652,0.02352794,-0.07143839,0.01824213,0.05115177,0.04525892,0.02608153,0.03726262,-0.03379571,-0.00626246,-0.0206156,-0.05577615,-0.06574394,-0.05424038,0.01357081,0.03409594,0.03059247,-0.03744061,-0.0270752,0.01851695,-0.05452125,-0.03318276,0.05490659,-0.03918832,-0.02324338,-0.06764166,-0.03291183,-0.01001145,-0.10845879,-0.02277948,-0.00177434,-0.01611473,-0.00702193,0.1044689,-0.00008458,-0.08615514,-0.08460781,-0.02096443,0.00518511,0.02088118,0.03126181,-0.00631405,-0.00829456,-0.04007995,-0.03327656,0.01464774,-0.01770483,0.04676485,0.0807549,-0.01220296,-0.00084784,-0.08951622,0.03125026,0.00223966,0.03383959,-0.01534931,-0.04067098,0.0570033,-0.00675731,0.01940333,-0.05121516,-0.00681281,0.03184345,-0.04691814,0.02213191,0.01670939,-0.07487163,0.07427865,0.04190335,-0.06679231,-0.01503296,0.03279139,0.03243364,0.01683406,0.01526235,0.04696456,0.00167874,0.03564801,0.02610687,0.02904429,-0.08058999,-0.03003598,-0.19923848,-0.07361744,0.0029049,-0.04763274,0.0693645,-0.05108886,-0.0153881,-0.0479215,-0.00090418,0.09800697,0.06891917,-0.07392623,-0.0117985,0.053185,-0.01019855,0.04095768,-0.07659038,-0.0353426,-0.05822106,0.06791248,-0.02423833,0.05592717,-0.03339472,-0.05101453,0.02333835,-0.01783494,0.14856839,-0.0505241,0.01626314,-0.03790005,0.02122518,0.06774945,-0.03140541,0.02554415,0.00195959,0.02728042,-0.10003725,-0.00561403,-0.05310738,-0.02434984,-0.08004162,-0.0183508,0.03298829,-0.01424921,-0.0617272,0.0349116,-0.04275316,0.03221456,-0.03037715,0.06527688,0.05931908,0.02104115,0.07235287,0.0345363,0.04272116,-0.02693615,-0.05996802,0.01452634,-0.05779725,0.04458116,0.02568461,-0.07202684,0.07187748,-0.06383079,0.07804053,-0.03778486,-0.0143577,-0.0347151,-0.0091035,0.00902298,-0.00879611,0.06666113,0.00731923,-0.05738682,-0.00581764,-0.00153425,0.07923228,0.00510205,-0.02696329,-0.02438094,0.02917041,-0.13480236,0.02494068,0.03344325,0.08188923,0.09049401,0.01853292,0.01025335,-0.0222331,-0.01340456,-0.03102945,-0.00507768,-0.0761036,0.00126497,-0.08353169,-0.01989324,-0.22152191,0.0483673,-0.00155248,0.07363855,-0.06150177,-0.06293023,-0.00144007,0.05727366,0.01524933,-0.06186602,0.11846405,0.02637008,-0.00989823,-0.06483996,0.01812737,-0.02278218,-0.0175158,-0.00882752,0.08910283,0.09030154,0.09407324,0.03271929,0.20147039,-0.04109732,0.0202707,-0.02939904,-0.00397434,-0.01389063,0.02604727,0.02874961,0.029487,0.01007385,0.09373841,0.00826296,0.06773557,0.02353821,-0.03318248,0.03362069,-0.01588508,-0.02223477,0.0041702,-0.03790774,-0.06083911,0.06866243,0.11873524,0.07075384,-0.02660453,-0.05274456,0.02354454,0.04361127,-0.06268611,-0.01108986,-0.0065865,-0.06574283,-0.01342402,0.02451379,-0.04419638,-0.01716109,0.05638813,0.00613717,0.00630813,0.01800155,0.01051337,-0.06355616,0.02647104],"last_embed":{"hash":"bgyz6j","tokens":84}}},"text":null,"length":0,"last_read":{"hash":"bgyz6j","at":1753423484962},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{4}","lines":[16,16],"size":224,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06318335,-0.04227221,-0.02256457,0.00729768,-0.01960557,0.06636732,0.070507,0.04252052,0.05006303,0.02543498,0.03046482,-0.00143865,0.01607369,0.00103861,-0.01048813,-0.03915697,-0.01867466,0.0310416,-0.07264227,0.02972043,0.05044806,-0.0705113,-0.04061414,-0.05210997,0.0613139,-0.00831158,0.00567345,-0.06669004,-0.06834754,-0.20919834,0.0107236,-0.00127134,-0.00088034,-0.07552063,-0.03614141,-0.01695931,-0.06273776,0.05648071,-0.01489106,0.04740214,0.04230017,0.02917385,-0.01752989,0.00544733,-0.01176467,-0.01575645,-0.02786993,-0.01616664,0.03939366,-0.00380594,-0.03806425,0.06390658,0.02088389,0.06855369,0.08270425,-0.01237863,0.1061578,0.08782761,0.00182495,0.01316358,0.02177884,0.03935992,-0.2178981,0.01362494,0.02916248,0.0220011,-0.02187082,-0.02460866,0.01298134,0.03331072,-0.01241889,0.01520868,-0.00670617,0.03781436,0.02175221,-0.07309021,-0.00486554,-0.05193838,-0.04474528,0.01775174,-0.00439213,-0.02556138,-0.03137314,-0.00225726,0.00436629,0.00467167,0.0301722,0.05273445,0.02994589,-0.07256017,0.04243414,0.00902342,-0.02463266,0.04867613,0.03190987,0.07102992,0.0709623,-0.00960294,-0.00367023,0.12627326,-0.00306843,0.00886814,-0.02541469,0.00967043,0.03406263,-0.05161285,-0.02627151,-0.04231135,-0.0422822,0.02096956,0.04042226,0.04568807,0.05659191,-0.04212692,0.00486575,0.00266997,0.02247445,0.03985003,0.05549051,0.00888989,0.01690433,0.06113137,0.07364832,-0.01065715,0.0255931,-0.03483048,0.0440003,0.07773288,0.01486362,0.02265159,0.03518857,-0.01985414,-0.14337537,-0.01611354,-0.02451113,-0.00409307,0.06130483,0.02950967,0.01439766,0.017844,0.00310912,-0.02023628,0.01049721,-0.09312662,-0.00730152,0.07066505,0.03021904,0.05479468,0.04917689,-0.00517048,0.00203934,0.02650671,-0.03602426,-0.08334024,-0.06349312,-0.00701882,0.04390204,0.03928095,-0.00231432,0.02168393,0.01388511,-0.06285319,-0.00444631,0.07993646,-0.04816144,-0.06172042,-0.06243769,-0.00723207,-0.02548909,-0.0812867,0.00062642,-0.00818994,-0.04128663,-0.02730123,0.0412209,-0.00038869,-0.07383376,-0.05588831,0.01451775,0.00481309,-0.0255713,0.00620171,-0.02798107,0.00410342,-0.05395476,-0.04211292,-0.00297806,0.00432181,0.02000953,0.06621972,-0.04763659,-0.02019346,-0.10347936,0.02688146,0.00586629,0.01922687,-0.0069085,-0.03537161,0.05622059,-0.02427458,-0.02214448,-0.04000532,0.00645017,0.04486573,-0.06335408,0.03469671,-0.01930526,-0.06457099,0.09331085,0.0169223,-0.04384938,-0.01639866,0.0605088,0.01887602,-0.00937628,-0.00239758,0.05181245,-0.0209514,-0.00191003,0.04443597,0.01450252,-0.05181727,-0.03939596,-0.21197934,-0.06712795,-0.0449172,-0.03683932,0.03139726,-0.06046311,-0.01650549,-0.03836136,0.02064893,0.10020535,0.07951519,-0.03816004,-0.01766577,0.05960504,0.00043724,0.02489002,-0.06707598,-0.0289083,-0.07610396,0.05502341,-0.01833973,0.0438819,-0.04886571,-0.05834292,0.05480802,-0.03900746,0.14752136,-0.01688746,0.00670519,-0.02976623,0.03511571,0.05451924,-0.0263819,-0.00054158,0.03432157,0.04365213,-0.07251941,-0.00199965,-0.07225173,-0.01630333,-0.0731383,-0.00233575,0.05277869,-0.05676493,-0.04001081,-0.01698992,-0.0089004,0.0728369,-0.02961796,0.1126055,0.06374431,0.01054613,0.07532524,0.02195928,0.0262044,-0.01917034,-0.06558698,0.01710162,-0.03738916,0.05482477,0.01024783,-0.05576373,0.08521964,-0.01243988,0.08033468,-0.01816831,-0.04109259,-0.01790218,-0.01809966,-0.00864688,0.00744961,0.03390702,0.02947337,0.00284189,0.00998786,0.02155183,0.05107662,-0.03388256,0.03026738,-0.03083401,0.0353924,-0.0947009,0.00013884,0.03686516,0.08652749,0.08502091,0.05154007,0.01315604,-0.00305638,-0.012178,-0.02910166,0.03594574,-0.05245959,-0.02405317,-0.0308503,-0.01560996,-0.24779499,0.01952791,-0.03778392,0.04395114,0.00442665,-0.04404928,-0.00451291,0.01642542,0.03141956,-0.05967285,0.09886183,0.0371473,-0.00281754,-0.06978679,0.00018368,-0.02351945,-0.04166024,-0.02363542,0.07672521,0.08458246,0.08053003,0.04695655,0.21721129,-0.00079343,-0.03044531,-0.04038944,0.02943891,-0.03495598,-0.0166449,0.01214638,-0.00299794,-0.00785567,0.04914986,0.00997356,0.02403599,0.04085737,-0.04595898,0.03952239,-0.04420621,0.00747648,-0.01997207,-0.04438192,-0.08799648,0.05368856,0.13674879,0.04400469,-0.03990261,-0.06731152,0.0350633,0.06282242,-0.07196185,-0.03838754,-0.00723595,-0.0566466,-0.01473025,0.05790533,-0.01707885,-0.01565425,0.05303523,-0.02464058,0.02087092,0.04325102,0.05197422,-0.02805964,0.02349613],"last_embed":{"hash":"1e1cd8n","tokens":114}}},"text":null,"length":0,"last_read":{"hash":"1e1cd8n","at":1753423484992},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{5}","lines":[17,17],"size":297,"outlinks":[{"title":"_**Pick 3 <u>Straight</u> Sets Statistics**_","target":"https://saliu.com/freeware/Pick3-Straight-Stats.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05598873,-0.07121165,-0.03385288,-0.03011855,-0.03982205,0.08561543,0.05089105,0.03764905,0.05180397,0.01774755,0.00373735,0.0102106,0.00716108,0.0163448,-0.02358066,-0.04719654,-0.02814331,0.01532254,-0.07862379,0.03951722,0.01732663,-0.06138212,-0.05274602,-0.06459048,0.02331345,-0.01279088,-0.02204737,-0.07468949,-0.06609606,-0.19118747,0.01230283,-0.0142026,-0.00342074,-0.0732265,-0.07412598,-0.04207782,-0.06674466,0.04941382,-0.06658883,0.04913029,0.03168652,0.00085715,-0.00215328,-0.00125728,-0.026592,-0.00476928,0.00257641,0.00398288,0.03119253,0.01267067,-0.02905926,0.05463222,0.01199627,0.02307599,0.07064552,-0.02538084,0.03915853,0.10132588,-0.07310802,-0.00484117,0.0333312,0.02660268,-0.18154995,-0.00428363,0.00584643,0.01683236,0.00272056,0.02444545,0.02750263,0.06635628,0.03017299,-0.00579387,-0.00276839,0.03096657,0.02712784,-0.03979969,-0.04504099,-0.04924868,-0.03515977,0.03339279,-0.00081675,-0.05920113,-0.01393194,-0.04162508,0.00563718,-0.02697329,0.08235874,0.01711799,0.04539244,-0.07097942,0.03845916,0.06941168,-0.01253354,0.02441305,0.019026,0.04184214,0.05502782,0.01704624,0.02870324,0.11814601,0.02703655,-0.03471825,0.01840124,0.00036956,0.07367293,-0.06115551,-0.03846774,0.00590301,-0.01558331,-0.02890945,0.01378729,0.03055621,0.07480735,-0.03425928,-0.02890194,0.00944242,0.04117303,0.01688012,0.04224558,-0.00248831,-0.01404462,0.05169894,0.05872481,0.00833038,0.00167432,-0.01043589,0.03495617,0.06862772,0.03408337,0.08993831,0.04152619,-0.03331255,-0.0708102,-0.01336875,-0.00460909,-0.01863837,0.03131824,0.06323323,0.00361425,-0.00731037,0.01634775,-0.01564481,-0.00144078,-0.1064033,0.01092877,0.0309699,0.0392457,0.03158967,0.04541087,-0.03284686,0.00449273,0.00370107,-0.04387237,-0.09292093,-0.0702383,0.0217475,0.04337689,0.07503788,-0.02081365,0.0099511,-0.00060231,-0.05605064,-0.01166136,0.0682946,-0.03000939,-0.05291599,-0.06627473,-0.00121682,-0.0470874,-0.1092551,-0.01081596,-0.00217696,-0.04911938,0.00718479,0.08582149,-0.01645182,-0.06374232,-0.06582293,-0.0228493,-0.00127412,0.032945,-0.0120899,-0.02328701,-0.01530232,-0.03091945,-0.03311336,-0.02414081,-0.01612368,0.01260872,0.03946196,-0.06273863,-0.04042664,-0.10007739,0.04596203,0.03623219,0.01380975,-0.00723269,-0.01018653,0.04640241,-0.01740961,0.0046129,-0.01433098,0.00500723,0.07106379,-0.03540452,0.02408601,0.02347725,-0.06503952,0.09408915,0.03491413,-0.01370015,0.008231,0.05491559,0.06553058,-0.01900951,0.02864224,0.05196954,-0.00039062,0.00562988,0.02221519,0.0357797,-0.04187626,-0.04457135,-0.18857476,-0.06528409,-0.05958797,-0.03350023,0.07252567,-0.03851491,-0.01732883,-0.03020774,0.02494949,0.0535184,0.06528167,-0.10348154,-0.02175769,0.03960218,0.02417469,0.04220634,-0.09138973,-0.06534545,-0.03584548,0.04366545,-0.03065861,0.05400495,-0.00912296,-0.04703951,0.03496848,-0.04514406,0.16160171,0.00273768,-0.01270523,-0.03309954,0.04386548,0.08466167,-0.02530427,0.02476995,0.04012135,0.02474751,-0.03159509,-0.01860419,-0.04959928,0.00501422,-0.05637173,0.03375844,0.05240608,-0.05122321,-0.07912435,0.00397887,-0.04909951,0.01973243,-0.01865021,0.14440249,0.06405994,0.02897645,0.04834427,0.01231373,0.04407235,-0.03967624,-0.06340361,-0.02053676,-0.0419529,0.0483625,0.02718104,-0.05723124,0.04920109,-0.05049086,0.07265051,-0.04010139,-0.05044685,-0.05453625,0.01763975,0.00054643,-0.02265277,0.06071515,0.03284257,-0.002345,0.03600968,0.01193699,0.04593052,-0.03857837,-0.04728598,0.04089997,0.02571926,-0.08345741,0.03931285,0.07122444,0.04718734,0.04577039,0.04943044,0.0084867,-0.01856144,-0.01393149,-0.02825818,0.0402675,-0.08242252,0.00309006,-0.02165548,0.01004651,-0.25073713,0.02038723,-0.01304337,0.04608029,-0.03393861,-0.02893837,-0.00714569,0.04239418,-0.02501812,-0.02724906,0.12041494,0.0319168,-0.00114358,-0.05173904,0.02134094,-0.02425044,-0.05163955,-0.05774763,0.07720672,0.04957155,0.09425747,0.09976497,0.23108646,0.02728338,0.0025298,-0.06245216,0.01403576,0.02443709,0.01646746,0.04216634,0.01556086,-0.00713625,0.08297916,-0.00840555,0.02450894,0.03136413,-0.07417535,0.02438028,-0.05324836,-0.02374703,-0.01327708,-0.01848941,-0.0391205,0.03570659,0.13234115,0.03057493,-0.04641498,-0.06584068,0.05406477,0.01844168,-0.0575284,-0.02315193,0.01389004,-0.06374054,-0.00448731,0.05079668,-0.02884768,-0.01317044,0.05705745,-0.00813336,-0.01388075,0.03071593,0.05918771,0.01768271,-0.0190306],"last_embed":{"hash":"di7b0t","tokens":153}}},"text":null,"length":0,"last_read":{"hash":"di7b0t","at":1753423485026},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{6}","lines":[18,19],"size":459,"outlinks":[{"title":"_**roulette systems, magic numbers**_","target":"https://download.saliu.com/roulette-systems.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0613481,-0.07313782,0.01377249,-0.02869007,-0.05844172,0.06911081,0.05555022,0.0256325,0.09545863,0.04447864,0.03070467,-0.00445279,0.04024203,-0.00346417,-0.03690745,-0.0553033,-0.03526096,-0.00796008,-0.06762525,0.04256067,0.0731323,-0.06135267,-0.04300313,-0.05944166,0.05353959,-0.02078704,-0.02257924,-0.06855255,-0.07106659,-0.21486235,0.0475963,0.00036991,0.01867616,-0.06064202,-0.06444535,-0.04104866,-0.05719424,0.04369001,-0.02083539,0.03873913,0.01896013,0.04597516,-0.02042629,-0.04150797,-0.02073628,-0.01657023,-0.03905217,0.01190022,0.02351931,0.04673382,-0.00570052,0.06018421,-0.00074,0.03923727,0.07673109,-0.02171351,0.07332718,0.08278032,-0.01647726,0.02052178,0.00743832,0.02890526,-0.21616887,0.01586077,0.02123114,-0.01625622,-0.0089538,0.01491775,0.04298607,0.06078382,-0.02852256,-0.01156408,0.00304953,0.0132662,0.02786552,-0.07293173,-0.02000105,-0.05927244,-0.01748795,0.01032232,-0.01602203,-0.03127226,-0.03411548,-0.00000894,0.03082813,-0.0146714,0.06506627,0.03753379,0.03807792,-0.05021548,0.04521718,0.0479787,-0.02908004,0.01222454,0.03729786,0.04268882,0.04889723,-0.0020451,-0.01233388,0.10317033,0.00687663,-0.02664314,-0.02251077,-0.00136551,0.05301613,-0.06740255,-0.01534676,-0.02279112,-0.04837724,-0.0315486,0.00847522,0.03174143,0.04785525,-0.02892674,-0.01404662,0.00900437,0.02506844,0.02667157,0.03811546,-0.00175982,0.01019692,0.05759601,0.02906293,-0.00939743,0.01156509,-0.02003775,0.04950493,0.0689806,0.02936462,0.06225986,0.06827927,-0.06249554,-0.12030605,-0.00298869,0.00959711,0.00061763,0.04236967,0.04281387,0.00483092,0.02846552,-0.01224805,-0.00492417,0.01478888,-0.07276351,-0.03155181,0.07589832,0.03939649,0.01801986,0.05072366,-0.03380993,0.01607255,-0.00314472,-0.05323483,-0.08618861,-0.0683599,0.01531909,0.03215173,0.02075387,-0.02489081,-0.02281663,0.02089469,-0.06427845,-0.01051988,0.07497413,-0.03361914,-0.03662734,-0.07095383,-0.02015401,-0.01987487,-0.09642254,-0.00025122,-0.01651347,-0.03763724,0.01113818,0.07831871,-0.01068334,-0.08880129,-0.06059122,-0.00450511,0.00374271,0.02562132,0.01693733,-0.03376157,-0.04009667,-0.03610479,-0.03362835,-0.01464462,-0.01913211,0.01477374,0.03155572,-0.04818102,-0.01317396,-0.08623977,0.04323977,0.00428938,0.03061025,-0.00252607,-0.04550158,0.07911161,-0.00326385,0.03825886,-0.05469605,0.00361324,0.04167594,-0.02993117,0.01636225,0.00023924,-0.05483916,0.07817583,0.02543757,-0.0169401,-0.04354719,0.04255368,0.02839914,0.01280064,0.03540291,0.04436427,0.01133836,0.03716999,0.03905794,0.00205035,-0.04519305,-0.02732851,-0.17684467,-0.04437851,-0.03229033,-0.05226308,0.0441359,-0.03990009,-0.01183669,-0.07144254,0.00608754,0.0790866,0.0756676,-0.07478769,-0.01475643,0.05437741,0.00317802,0.01226733,-0.07367075,-0.0019948,-0.06010328,0.07096539,-0.01638232,0.0505568,-0.04368079,-0.0577724,0.0314014,-0.02115015,0.15807739,-0.03065503,0.00529668,-0.01878529,0.03384965,0.07995259,-0.02101732,0.03186189,0.01788417,0.04001259,-0.0492207,-0.00266447,-0.04693637,-0.02951694,-0.07711425,0.02860885,0.04869471,-0.07823591,-0.05981592,0.02817771,-0.04332661,0.05297751,-0.02434859,0.09227718,0.08627381,-0.02487382,0.07422803,0.0128058,0.05394037,-0.04276879,-0.05866212,0.01287664,-0.02642018,0.04881315,0.01480335,-0.05067044,0.09952489,-0.02850389,0.07910758,-0.03076539,-0.00168442,-0.01946114,-0.00735289,-0.01402353,-0.01267369,0.04079871,0.01873109,0.00509222,0.01678123,0.00420093,0.06062283,-0.01391158,-0.03697049,0.00044147,0.03060874,-0.11183902,0.00706897,0.05190979,0.07705903,0.07438178,0.06551237,0.03642869,0.00921202,0.00136642,-0.0261658,0.00128642,-0.05802909,0.00792689,-0.08970284,-0.01118948,-0.2544142,0.026727,-0.04358001,0.05397383,-0.01894355,-0.05670362,0.01252012,0.0371254,0.00081763,-0.08747501,0.07485981,0.0428135,-0.00716621,-0.05786916,0.05076293,-0.0363613,-0.03113351,-0.03951773,0.07001764,0.07853874,0.10346027,0.05886611,0.20441647,-0.03710352,0.00803326,-0.02376783,-0.00001051,-0.01967067,0.04518814,0.04396242,-0.01767706,0.008305,0.09863611,0.00637512,0.03456827,0.02002551,-0.03675232,0.00546221,-0.04168413,-0.00656636,-0.00897364,-0.04074278,-0.03556084,0.06657737,0.15453739,0.05148862,-0.03022587,-0.07150244,0.02347585,0.04402395,-0.06404364,-0.02019276,0.04642199,-0.05498218,0.00264247,0.04494551,-0.03002318,-0.00600834,0.04797149,-0.01544783,0.00519073,0.03379428,0.03517595,-0.01689399,-0.00030416],"last_embed":{"hash":"80ap3t","tokens":472}}},"text":null,"length":0,"last_read":{"hash":"80ap3t","at":1753423485069},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{7}","lines":[20,70],"size":4814,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06546442,-0.07298103,-0.02263469,-0.00464226,-0.04265297,0.06946561,0.07253615,0.01454914,0.05823831,0.01625034,0.01578203,0.01719837,0.04618,0.03444075,-0.01816122,-0.03275926,-0.0219986,0.00675586,-0.06539406,0.01792675,0.05953366,-0.05861458,-0.04912792,-0.08649962,0.03778482,-0.04176461,-0.01404028,-0.08253562,-0.07034456,-0.17404878,0.00899259,-0.01562138,-0.00926834,-0.07661114,-0.07390926,-0.04547338,-0.03976361,0.04880991,-0.04263866,0.04239897,0.02382182,0.0123475,-0.03857753,-0.01908085,-0.00854026,-0.00555666,0.01475259,-0.01215771,0.02415508,0.01830203,-0.03272228,0.07330931,0.00258866,0.07691384,0.05318263,0.0000458,0.0695257,0.09945268,-0.01104221,0.02458309,0.06106143,0.01958989,-0.18235236,0.0155725,0.00733184,0.03166948,0.00973041,0.00975727,0.01454301,0.05271238,0.0146769,0.00912514,0.00948017,0.04664624,0.04193254,-0.04860821,-0.03416879,-0.06406565,-0.02686182,0.04299331,-0.016186,-0.03069701,-0.02261903,0.00202145,-0.00346091,-0.03721968,0.05099306,0.04522369,0.04277983,-0.08006013,0.00713976,0.06083757,-0.01923181,0.04066777,0.04746374,0.03837581,0.07823034,-0.04189373,-0.01948575,0.15541086,-0.0173111,-0.01661723,-0.00282911,0.02626249,0.04996433,-0.0595654,-0.03180918,-0.01837409,-0.01055809,-0.00814693,0.02772603,0.01954245,0.05714153,-0.03612409,-0.03424643,0.00357399,0.03812863,0.03736123,0.06636229,0.00986215,-0.02028605,0.03022586,0.05699624,-0.04956655,0.02433683,-0.02389282,0.01807423,0.05991214,0.01467832,0.0567588,0.04799182,-0.04045293,-0.11329776,-0.01675788,0.00392306,-0.01028785,0.03398089,0.00826025,-0.01975143,-0.01253127,0.03180049,0.00537674,0.02964631,-0.10699988,0.00073119,0.05204064,0.05307179,0.03047667,0.05890897,-0.00319592,-0.01662889,-0.03314685,-0.04723685,-0.07376386,-0.05073328,-0.00759211,0.0464194,0.03935295,-0.01468644,0.00775897,0.02374973,-0.05569813,-0.01363692,0.10863937,-0.04292502,-0.02011598,-0.05892729,-0.01232145,-0.01883653,-0.09667193,-0.0426117,-0.00822954,-0.03027437,0.0025611,0.07380006,0.01052716,-0.07013234,-0.07736798,-0.04030882,0.02456685,-0.00688528,0.01470245,0.00464825,0.00191143,-0.0443628,-0.02968613,-0.00774542,0.0087074,0.03238094,0.05212329,-0.02151838,-0.01207448,-0.082806,0.03480142,0.01085813,0.00604297,-0.00794111,-0.03553653,0.04693886,-0.03625314,-0.03141047,-0.04451484,0.00971665,0.0607572,-0.02453485,0.01059111,0.00428459,-0.06847449,0.09596422,0.02973157,-0.03703975,-0.01486195,0.05992753,0.03932663,-0.00093028,0.02470573,0.04006287,0.00844134,0.04460361,0.02290175,0.00855983,-0.03215455,-0.04241771,-0.21243627,-0.06020214,-0.03307666,-0.07929447,0.05234294,-0.06113852,-0.02849073,-0.08028123,0.01487461,0.10574244,0.06277987,-0.08774894,-0.00973537,0.05822398,-0.00358516,0.0134583,-0.09560773,-0.06091742,-0.06055437,0.07510837,-0.00421905,0.04792443,-0.00627272,-0.08150668,0.01930542,-0.0164088,0.15004946,-0.02212504,0.02529765,-0.01107504,0.03429937,0.05375301,-0.02122931,0.01915365,0.02650719,0.03767653,-0.09024208,-0.0188298,-0.06041252,-0.01960466,-0.06337363,0.01345373,0.0452569,-0.0476165,-0.06366738,-0.00721654,-0.04397625,0.05176121,-0.02328152,0.10471175,0.04352005,0.00472392,0.08121715,0.00770956,0.07250408,-0.02344184,-0.04980591,0.01895303,-0.04422696,0.02622117,-0.00105563,-0.07453968,0.07296988,-0.06274951,0.0623896,-0.03745035,-0.0430121,-0.03278431,-0.00966323,0.00645586,-0.01487914,0.04923056,0.00023441,-0.03768406,-0.01104372,0.01912712,0.05728885,-0.02646703,-0.03308824,-0.00797058,0.0510174,-0.08766997,0.02055511,0.04595046,0.080598,0.06570657,0.02970616,0.04090817,-0.006839,0.0030052,-0.03429754,0.02974292,-0.06041029,-0.00549749,-0.03270023,-0.02735222,-0.21633773,0.03989837,-0.02450713,0.0470739,-0.03116461,-0.05231933,-0.00496954,0.04687493,-0.00983993,-0.04890748,0.08712719,0.03803257,-0.00798092,-0.03650355,-0.00695737,-0.01565303,0.01040912,-0.02370399,0.07903659,0.08572073,0.10662552,0.03403987,0.24128817,0.01060613,0.00354536,-0.03810525,0.0244114,0.02106885,0.00721036,0.03818269,0.03653022,0.00150685,0.09407119,-0.01334104,0.01131868,0.00725252,-0.04316305,0.06219376,-0.03733559,-0.01200443,-0.02217239,-0.02560644,-0.07628341,0.05817372,0.13233529,0.06108379,-0.03684073,-0.05628578,0.03421696,0.03726523,-0.060906,-0.01990123,-0.01763008,-0.06638128,0.00633107,0.03079926,-0.02271399,-0.01423103,0.06547535,-0.01974191,0.03229287,0.04335656,0.04752524,-0.03214503,0.00508204],"last_embed":{"hash":"1kei7rm","tokens":76}}},"text":null,"length":0,"last_read":{"hash":"1kei7rm","at":1753423485206},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{8}","lines":[71,71],"size":207,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05542219,-0.01269685,-0.02245423,-0.01745628,-0.03765906,0.0673696,0.01874378,0.01722208,0.04609044,0.01959418,0.03255222,0.02037295,0.05072115,0.00596065,-0.01153559,-0.05465008,-0.02667365,0.0206113,-0.05389915,-0.00940103,0.06711275,-0.00122833,-0.05139495,-0.06634995,0.04361779,-0.04542217,-0.02031505,-0.06418817,-0.06121508,-0.19638699,0.01948195,0.00287693,-0.00720215,-0.08557358,-0.08948489,-0.01268723,-0.04502667,0.06473751,-0.0398226,0.0270593,0.02155012,0.01542165,-0.02816783,-0.05141341,-0.03476807,-0.01589903,0.00823832,0.03854028,0.01812694,-0.00189687,-0.03691956,0.01899556,0.00180752,0.06898209,0.04758829,0.00235954,0.06481086,0.11602198,0.0024713,0.05608406,0.06273777,0.05738851,-0.19692145,-0.00463416,-0.04311004,0.01233367,0.01376812,0.00544779,0.04415431,0.06711586,-0.00407014,-0.029988,-0.02523258,0.06332596,0.03091547,-0.0495222,-0.03723087,-0.07473661,-0.02329461,0.00900104,-0.01308761,-0.03262947,-0.00579023,0.02478239,-0.00257968,-0.01445196,0.02930189,0.02857021,0.04601735,-0.09102089,0.03815435,0.04734322,-0.00043913,0.00955462,0.00592153,0.01272259,0.08628763,-0.03799203,-0.00959129,0.1464121,-0.00458159,-0.00785132,0.00181527,0.02208103,0.05138683,-0.11228023,0.0078231,-0.05697952,-0.04000808,-0.00151144,0.0453988,0.0234054,0.06794561,-0.01876678,-0.01998112,-0.01938942,-0.03383211,0.03370942,0.05802586,0.0343628,-0.00528839,0.04360342,0.04618026,-0.05941671,0.02465401,-0.01020694,0.0189412,0.07149498,0.01621744,0.00812241,0.03480211,0.01380992,-0.12906718,-0.025736,0.01534639,0.00008388,0.01285611,-0.01723731,-0.02702131,0.0314594,-0.00510501,0.05993798,0.08067558,-0.11662164,0.03338609,0.07793655,0.04050297,0.02259823,0.00840543,-0.00282209,-0.0153351,-0.03213863,-0.03847889,-0.04829666,-0.03358574,0.01156017,0.05756327,0.07001102,-0.04778434,0.00215126,0.00114142,-0.0243507,-0.02756609,0.12311433,-0.05102853,-0.0870026,-0.02611452,0.00977111,-0.0351074,-0.0544219,-0.04117243,0.03704916,-0.01372853,-0.01791221,0.09812167,-0.00545693,-0.08864229,-0.06631018,-0.03380891,-0.02654958,0.02905318,0.01166546,0.00560849,0.0208633,-0.01151231,-0.05843271,0.04516856,0.00040341,0.03867653,0.06012788,-0.0605425,-0.00593826,-0.0502716,0.04161411,0.01627858,0.01764751,-0.03222791,-0.01101367,0.07850662,-0.03414165,-0.03429425,-0.05365216,0.02836793,0.02214236,0.00474471,-0.00126427,0.00519559,-0.06688168,0.11261722,0.0009301,-0.05592365,0.0038755,0.07082791,0.06162814,-0.03542132,0.00354982,-0.00871818,0.03404675,0.02367945,0.0025883,0.02590246,0.01216081,-0.05156816,-0.20237389,-0.04312749,-0.04871931,-0.0639561,0.05383862,-0.04496216,-0.03743966,-0.05857343,-0.00510005,0.11254373,0.06949881,-0.05247263,-0.01679218,0.03292188,-0.00205963,-0.023618,-0.084037,-0.05153299,-0.06676199,0.05126486,-0.01529893,0.03626093,0.00214525,-0.0733672,0.00571807,-0.02239689,0.10425784,-0.04602683,-0.00380948,-0.02525309,0.02154003,0.00899289,-0.0340116,0.02210705,0.00886695,0.05746807,-0.05548113,-0.02290964,-0.03539374,-0.00346356,-0.05287276,0.02338238,0.02960679,-0.08536023,-0.04653645,0.00810838,-0.02076144,0.05188486,-0.00481229,0.07833786,0.05154347,0.02299092,0.06086941,0.04960395,0.05772695,-0.05176087,-0.03921051,0.05665581,-0.01839968,0.02347867,0.01219344,-0.07941374,0.07424347,-0.03470553,0.05288774,0.00221667,-0.03498794,-0.04378004,-0.00924447,0.00853943,-0.00567501,0.10841288,-0.0087109,0.00382489,-0.02535353,0.01931462,0.08292765,-0.01777055,-0.0563241,-0.0367059,0.02928237,-0.07449114,0.01712895,0.05375129,0.08200555,0.04878863,0.0576159,-0.00500715,-0.00043278,-0.01970201,-0.05884582,-0.0182221,-0.04207582,-0.01256703,-0.06021948,-0.01513245,-0.22828239,0.03084589,-0.03538951,0.02901104,-0.05807455,-0.03192394,-0.00816635,0.03497445,0.02801552,-0.06352489,0.06698991,0.04860291,-0.00002055,-0.05150655,0.01084243,-0.00479544,0.04445624,-0.02977099,0.07486218,0.04205678,0.09023964,0.05664475,0.24775203,-0.02152864,0.01722107,-0.01578551,0.01524692,0.00465615,-0.00245211,0.04177378,0.00558199,0.00773854,0.09099518,-0.00575241,0.02253885,0.06296457,-0.00498538,0.02874447,-0.02644859,0.00005725,-0.04422585,-0.02067877,-0.0751659,0.05528058,0.13164294,0.04305778,-0.03516314,-0.03566239,0.01785018,0.0686844,-0.07849474,-0.03488962,-0.01327648,-0.03640165,0.00335766,0.03724543,-0.04345318,0.0063486,0.04250358,-0.01945795,0.05542846,0.0123163,0.0186878,-0.00885215,0.01807201],"last_embed":{"hash":"1u7888f","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"1u7888f","at":1753423485239},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{9}","lines":[72,72],"size":427,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04464549,-0.03505466,-0.00814235,-0.0216817,-0.04030106,0.08176982,0.04344635,-0.01715187,0.05582611,0.04525675,0.03195061,0.03161059,0.04524183,0.02502919,-0.0331246,-0.04829412,-0.03549815,0.000762,-0.07852413,0.02705387,0.04903675,-0.04653272,-0.0327135,-0.03209258,0.0364405,-0.04625996,-0.00192224,-0.08768032,-0.07445252,-0.18529332,0.01283265,-0.01679282,0.01847332,-0.05009223,-0.06972312,-0.03158471,-0.03338625,0.0666803,-0.02708117,0.04403014,0.05366994,0.035707,-0.02551647,-0.04295662,-0.01172645,-0.01367835,-0.00419188,-0.00677518,0.06463725,0.03778933,-0.02117579,0.04849851,0.00372898,0.06166635,0.05418745,-0.02619618,0.05417038,0.08659682,0.01083103,-0.00468493,0.04542885,0.0432794,-0.21566515,-0.026064,0.00432684,0.00649673,0.01527808,0.02932321,0.0027025,0.03832081,0.02203531,-0.00009603,-0.00769343,0.02730719,0.03815767,-0.08501362,-0.03114774,-0.05974453,-0.06087973,0.01735499,-0.00710559,-0.01915102,0.00028083,-0.01229976,-0.03531611,-0.01317533,0.05058648,0.02134688,0.04755737,-0.08733586,0.05202705,0.04262255,-0.01354707,0.04235033,0.01539959,0.04106864,0.05050945,-0.01559363,-0.0176368,0.14325666,0.00444073,0.02135559,-0.00785593,-0.00408249,0.07002471,-0.05458588,-0.01992107,0.0252708,-0.00960003,0.00794172,0.00625352,0.05002073,0.05069342,-0.018349,-0.04258248,-0.00081827,0.02215865,0.05180598,0.00008492,0.0368005,-0.00402228,0.04994564,0.03684888,-0.03032875,-0.01158707,-0.0256194,0.02832844,0.06700058,0.04522863,0.04158522,0.02069781,-0.02434957,-0.13019595,-0.0215972,-0.00301877,-0.02235179,0.03689773,0.00494137,-0.01211181,0.0072999,0.0369327,0.00319511,0.01154081,-0.09287162,0.01770082,0.0414674,0.05191668,0.03643571,0.04859278,-0.01196621,-0.0194311,-0.00059033,-0.08467679,-0.1066848,-0.04256875,0.01899551,0.03526669,0.05627083,-0.03367992,-0.01078019,-0.00250465,-0.03928486,-0.02823885,0.09065696,-0.03546747,-0.01213486,-0.06890038,-0.01699913,-0.02192031,-0.08937222,-0.04903657,-0.01944931,-0.02840552,-0.02702955,0.07923613,-0.00835824,-0.11420446,-0.08402627,-0.0388263,0.00563686,0.02931023,0.02108708,-0.02157825,0.00281561,-0.0379214,-0.03051927,-0.00142989,-0.01309673,0.03630111,0.08025946,-0.01842458,0.02247922,-0.10762744,0.02369363,0.01654529,-0.00503278,-0.01165752,-0.03779437,0.05713524,-0.0416906,-0.02093501,-0.05398512,-0.00826664,0.0498509,-0.01962981,0.02162119,-0.003471,-0.06268935,0.1287488,0.02602897,-0.0468086,-0.02734989,0.03209025,0.02009102,0.01658665,0.0417427,0.05604549,0.01480253,0.0418286,0.02026702,0.04161127,-0.01560962,-0.04087328,-0.18551871,-0.07337609,-0.02768457,-0.06145355,0.04155023,-0.04716695,-0.03468396,-0.02660021,-0.01352553,0.05467463,0.0732045,-0.08114877,-0.02997618,0.07571124,-0.02500154,0.01161076,-0.09432369,-0.02947079,-0.02352863,0.05909478,-0.00915175,0.03568305,-0.03736684,-0.06440088,0.03862615,-0.04377891,0.15343997,-0.01033088,-0.01982346,-0.02599699,0.02468319,0.07282559,-0.05347392,0.00055514,0.00362451,0.04725862,-0.08481476,-0.03290879,-0.05127819,0.02038373,-0.07611477,-0.00644614,0.02298411,-0.04121414,-0.06838852,0.03013399,-0.02413543,0.08107033,-0.04813392,0.09138324,0.07977735,0.01976749,0.09222703,0.05775769,0.06855154,-0.04022105,-0.04578672,0.01598385,-0.00138297,0.06244661,0.02415788,-0.07558079,0.07228117,-0.0244821,0.09083164,-0.02002377,-0.03100226,-0.01847036,0.00448113,0.01019091,-0.01373645,0.0749281,-0.02288989,-0.02615582,0.00050509,0.02210051,0.07285748,-0.04730481,-0.0102479,0.01082694,0.02406394,-0.09758491,0.03405362,0.04944578,0.0744947,0.07268923,0.01822219,0.0098853,-0.03434404,-0.01576415,-0.014667,0.02129689,-0.0796233,0.02097272,-0.06847104,0.01494897,-0.20469484,0.01662154,-0.01135078,0.05968108,-0.04505974,-0.03255573,0.00893675,0.070466,0.00903344,-0.02897752,0.10485509,0.03136155,-0.01145061,-0.05758246,0.03474805,-0.03793266,-0.03601409,-0.01375273,0.07832319,0.08999068,0.08483284,0.03823319,0.22048023,-0.00994575,-0.01646376,-0.04291844,-0.0085031,0.02000042,-0.01289657,0.01745912,0.01354024,-0.00530942,0.08440569,0.00273404,0.00803131,0.01854834,-0.04228814,0.03762229,-0.04695336,-0.00701924,0.00953033,-0.03364502,-0.05732451,0.04046399,0.14751694,0.05089519,-0.01711254,-0.08685429,0.0323316,0.03129134,-0.06745525,-0.02238541,0.00004649,-0.04864988,-0.02028299,0.01394295,-0.03847334,0.00865727,0.05912454,-0.02331926,-0.00979672,0.04016832,0.0172052,-0.00346579,0.01190525],"last_embed":{"hash":"eei6pg","tokens":122}}},"text":null,"length":0,"last_read":{"hash":"eei6pg","at":1753423485274},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{10}","lines":[73,74],"size":438,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12507659,-0.04769443,-0.02412903,0.00701032,-0.06582225,0.05868025,0.01987161,0.02496682,0.06345506,0.00986121,0.0280368,0.00110752,0.04078388,0.02279814,-0.00823703,-0.05006953,-0.02692875,0.01577822,-0.04601283,0.00142268,0.10336385,-0.0660146,-0.05460722,-0.1141595,0.04847459,0.00133313,0.00365291,-0.07086729,-0.0442447,-0.20270905,-0.02044339,0.00828768,0.01863234,-0.06313936,-0.08881506,-0.02662645,-0.01620782,0.04024939,-0.06047472,0.0438514,0.00965571,0.02428553,0.00346528,-0.03043146,0.01996708,-0.03313613,0.00775051,0.00085682,0.03098659,0.00149245,-0.04363511,0.0476417,-0.01337155,0.04696928,0.06259738,0.01297775,0.05671361,0.10127825,-0.01796624,0.02650403,0.04188001,0.0273871,-0.21932274,0.0390724,-0.01473304,0.02549921,-0.00360135,0.00758779,0.02807817,0.04143535,0.00275012,0.01355325,-0.01225102,0.07342533,0.06963222,-0.03988955,-0.00772041,-0.06140766,-0.02087597,0.02574483,-0.00855976,-0.03687963,-0.00523705,0.02005188,-0.02326445,0.01554427,0.04948816,0.01840441,0.03960072,-0.08869439,0.02770772,0.04615015,0.02088158,0.02438458,0.01014399,0.03644088,0.07036062,-0.00971001,0.00390936,0.11750115,0.00597949,-0.01556101,-0.01790511,0.03895245,0.06679603,-0.05204264,-0.03204079,-0.03454656,-0.01835161,0.00040628,0.04486562,0.03254331,0.07152344,-0.04999352,-0.03439359,-0.00573494,0.04109762,-0.00095433,0.0503473,0.01651416,-0.02237324,0.04927108,0.03913039,-0.02209257,0.01984113,-0.02260865,-0.01255125,0.05449564,-0.00134814,0.0394782,0.05363,-0.00134215,-0.1165212,-0.04888204,0.01685174,-0.00854612,-0.01956217,-0.01343691,-0.04252718,0.00908725,0.00428353,0.00857139,0.03807067,-0.08977125,-0.03314143,0.02406329,0.02652975,0.0182212,0.00979247,-0.01066611,0.00595453,-0.03556746,-0.02331537,-0.04949789,-0.01814105,-0.01968963,0.08055133,0.07165888,-0.05263412,-0.00002676,-0.01832187,-0.02655551,-0.0389099,0.1174927,-0.0374381,-0.05587709,-0.03370823,0.02438706,-0.00992728,-0.10075319,-0.05472459,0.00512884,-0.03346115,0.01003801,0.08523003,-0.02789934,-0.0677444,-0.07317503,-0.02683697,0.00261764,-0.02152252,-0.016799,0.00162301,-0.00387174,-0.05034257,-0.08310369,0.00849314,-0.03889196,0.03215694,0.05739762,-0.00294633,0.02223295,-0.07494369,0.00805257,-0.02695937,-0.00533541,-0.04690028,-0.06325842,0.05101889,-0.00882187,-0.04931981,-0.04590171,-0.00687539,0.0485073,-0.04277614,0.02628874,-0.00698147,-0.04547647,0.08732037,0.0468736,-0.00539684,0.00482555,0.04143523,0.03024906,0.00214848,0.0509427,0.03378814,0.03010674,0.01352711,0.01098286,0.01537077,-0.01983872,-0.0834494,-0.19073021,-0.05377874,-0.06986002,-0.03874601,0.05340586,-0.05070166,0.00829908,-0.08212356,0.00993137,0.07209485,0.09400114,-0.08136088,0.01052769,0.06961682,-0.00734066,-0.00828181,-0.08968022,-0.04404994,-0.06301219,0.06243156,-0.00454766,0.00953001,-0.02922266,-0.070347,0.00005133,-0.00160109,0.12180763,0.00769096,0.04205832,0.01204009,0.07003698,0.07372904,-0.04235886,0.00371236,0.0353098,0.05738949,-0.06573756,0.00289848,-0.06585532,-0.0108385,-0.05342339,0.0407989,0.02943229,-0.0622744,-0.07879591,0.01940228,-0.02345774,0.03992756,-0.0052288,0.06763578,0.06259671,0.04128077,0.06120657,-0.00092555,0.05993935,-0.03143571,-0.05924584,0.01633443,-0.04007482,0.05189513,-0.00929263,-0.03765886,0.04728366,-0.01162609,0.1045039,-0.02665001,-0.05239755,-0.00537805,0.02259382,-0.01813505,-0.01052819,0.08250301,0.00155804,0.01039275,0.01705666,0.02122087,0.09193126,-0.05683495,-0.03673219,0.00062172,0.0172525,-0.09602235,0.04297327,0.04625696,0.07487395,0.04134271,0.06340011,0.05150808,-0.00603261,0.01421919,-0.0120323,0.0074373,-0.06340459,0.01848654,0.01137497,0.01824582,-0.2449303,0.03163227,-0.01294476,0.06826483,-0.0403404,-0.03458359,0.01909999,-0.016107,0.02831528,-0.03529133,0.07603108,0.0265842,0.01754784,-0.06566797,0.0251007,-0.00254973,-0.03236843,-0.04114152,0.07253984,0.06909413,0.06375072,0.05949089,0.22516757,-0.01458147,-0.01544553,-0.00177808,0.03501378,0.02461525,0.00784266,0.05141605,0.00891321,0.01580382,0.07395506,-0.00142433,-0.04525085,0.03275833,-0.03493093,0.03250416,-0.01923047,-0.01581832,-0.0249809,-0.02665465,-0.05847179,0.07475825,0.12060125,0.03610221,-0.02294611,-0.07511547,0.05617619,0.067428,-0.07247931,-0.04035298,-0.03437099,-0.06209385,0.00564944,0.03509978,0.00068704,-0.01788063,0.01213618,-0.03193602,0.03025098,0.02026792,0.07186954,-0.01838403,0.01154326],"last_embed":{"hash":"1swajzz","tokens":222}}},"text":null,"length":0,"last_read":{"hash":"1swajzz","at":1753423485311},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#{11}","lines":[75,83],"size":642,"outlinks":[{"title":"The best lottery software covers 5, 6, 7-number lotto jackpot games.","target":"https://saliu.com/ScreenImgs/lottery-software.gif","line":1},{"title":"Ion Saliu's Theory of Probability Book founded on mathematics applied to strategy, systems for lottery, pick-3-4-5 lotteries.","target":"https://saliu.com/probability-book-Saliu.jpg","line":3},{"title":"**Read Ion Saliu's book:** _**Probability Theory, Live!**_","target":"https://saliu.com/probability-book.html","line":3},{"title":"Losing big money in lottery is guaranteed if playing randomly without systems.","target":"https://saliu.com/HLINE.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09223291,-0.03908022,-0.04092817,-0.01664362,-0.06500672,0.0635263,-0.02113761,-0.00556872,0.05033309,0.00673727,0.0221688,0.00258156,0.06570182,0.0010434,-0.02461804,-0.06783307,-0.01188036,0.02701522,-0.03598422,-0.01608356,0.08233579,-0.04332451,-0.06945627,-0.0782839,0.0419548,-0.0222632,-0.01664228,-0.061142,-0.04978611,-0.19889796,0.02221578,-0.00726778,0.01067655,-0.09002564,-0.07428882,-0.00302886,-0.02720152,0.02811367,-0.06519805,0.04153584,0.00904035,0.01568991,0.00692007,-0.00739976,0.04396746,-0.02598867,0.03748589,0.02778181,0.0587317,0.0082843,-0.07355386,0.0360411,0.00078409,0.03203321,0.07787169,0.01203335,0.04958693,0.11429634,0.00919829,0.0159757,0.03531488,0.07454204,-0.22248939,0.06518654,-0.00391175,0.02184872,0.00215072,0.00370074,0.03316942,0.04598843,-0.0014376,-0.01855064,0.00794321,0.05061347,0.05165864,-0.0217712,-0.03580588,-0.06007801,-0.02724778,0.01382603,-0.0308698,0.00037479,0.00123263,0.00989061,0.01038823,0.0526614,0.05470444,0.03475117,0.0673416,-0.0838875,0.02043856,0.06069439,0.0258901,0.02886493,0.01793729,-0.00104628,0.0536878,-0.02080861,-0.01157454,0.12421876,-0.00463404,-0.02146365,0.00659769,-0.01355811,0.05060644,-0.09523773,-0.01413475,-0.03555872,-0.03853432,0.02165159,0.01994936,0.00074465,0.03729281,-0.03985436,-0.02480478,-0.00745976,0.01534443,0.00417963,0.02050449,0.00673733,-0.02666391,0.01650683,0.05304089,0.00105202,0.01365415,-0.0166315,0.01095234,0.05848076,0.00709869,0.02964997,0.06780611,0.03276637,-0.12939544,-0.0318931,0.01221673,-0.02823787,0.02098912,-0.01184071,-0.03259153,0.00662608,-0.01417724,-0.03035538,0.04006959,-0.10790396,-0.00540328,0.05673237,0.0310343,-0.00204069,0.01127861,-0.01367689,0.00750298,-0.0307751,-0.04723096,-0.07913554,-0.00874494,0.02178675,0.09751382,0.06862479,-0.04284207,-0.02351698,-0.02139297,-0.0320304,-0.04861943,0.14684266,-0.01896018,-0.11288919,-0.0413399,0.0364383,-0.01988557,-0.08928151,-0.01600585,-0.01821743,-0.05236446,-0.00625595,0.0932046,-0.02118662,-0.06205635,-0.06573491,-0.03499654,0.00541864,-0.00003367,-0.037664,-0.0131633,-0.00706688,-0.01962965,-0.05149021,0.0083649,-0.028035,0.0262956,0.0456898,-0.02119631,0.03315764,-0.06881126,0.0152704,-0.03645914,0.00936273,-0.03668226,-0.03948145,0.06592223,-0.00663289,0.02231129,-0.02448075,-0.00561517,0.01231019,-0.02102771,0.01967254,-0.02319259,-0.06934781,0.09582981,0.06240672,-0.03203069,-0.00372152,0.07937463,0.04649378,-0.02642378,0.03993395,0.02226224,0.00984186,-0.01818871,0.04748744,0.0039962,-0.00631202,-0.07859172,-0.18124701,-0.05379261,-0.04302198,-0.05987685,0.02974247,-0.04845662,0.0330078,-0.0690621,0.02139122,0.08191288,0.10952403,-0.06265792,0.01742559,0.06594864,-0.01661154,-0.00207926,-0.0678091,-0.03377848,-0.07412252,0.05069586,0.02015551,0.03075765,-0.00534782,-0.07581962,0.0080174,-0.03941311,0.13392071,-0.02679675,0.04966608,0.01565729,0.05711472,0.04886197,-0.01098327,-0.01478402,0.01167131,0.04205045,-0.03012376,0.0022024,-0.04049235,-0.00802914,-0.06699764,0.00406857,0.0464186,-0.08617081,-0.06237476,0.00331826,-0.01264076,0.02662147,-0.02992417,0.06750533,0.04079627,0.0290482,0.06025108,0.02840691,0.05183941,-0.02383907,-0.04364549,-0.01081585,-0.03704991,0.04169911,-0.0226948,-0.06635779,0.03937731,-0.03169329,0.07946908,-0.00878542,-0.02541562,-0.00535121,0.01329844,0.00165884,-0.00057378,0.07437514,-0.00037234,0.03099097,0.00269026,0.00950688,0.09595839,-0.04491591,-0.03517021,-0.00586008,-0.03834286,-0.08722179,0.06135323,0.04359999,0.07882936,0.07578808,0.06989491,0.02918468,0.00111983,-0.00442312,-0.0106526,-0.01670086,-0.05026798,0.01666811,0.01083106,0.02408055,-0.24858031,0.04017058,-0.02045757,0.05006953,-0.04360514,-0.04405421,0.00592417,-0.01768034,0.0295505,-0.05886542,0.05748797,0.02329214,0.00617228,-0.05251117,0.00616348,-0.0162191,0.00300338,-0.01477914,0.07757496,0.07587807,0.05263578,0.04782336,0.24134935,0.00469595,-0.00227652,0.006744,0.01144727,0.01900305,0.01662697,0.03781334,0.03156388,0.02985088,0.06376358,-0.00190976,-0.01826458,0.0251933,-0.02993112,0.02724075,-0.02645368,0.00214796,-0.03544521,-0.0105016,-0.04993311,0.0608523,0.1295228,0.04325856,-0.04472152,-0.0726922,0.05340217,0.05831498,-0.09908987,-0.03657627,-0.05149514,-0.05122957,-0.00019536,0.04286114,-0.00099285,-0.02232592,0.00731779,-0.02328662,0.03762967,-0.00671221,0.07206879,0.01540333,-0.00852148],"last_embed":{"hash":"1ugku35","tokens":408}}},"text":null,"length":0,"last_read":{"hash":"1ugku35","at":1753423485374},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.","lines":[88,116],"size":2913,"outlinks":[{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":9},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":10},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":12},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":13},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":14},{"title":"_**The Best Strategy in Lottery, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":15},{"title":"_**Software, Formulas to Calculate Lotto Odds**_","target":"https://saliu.com/oddslotto.html","line":16},{"title":"_**Lotto, Lottery, Balls, Memory, Probability Laws, Rules of Randomness**_","target":"https://saliu.com/bbs/messages/575.html","line":17},{"title":"**_Lottery Software: Lotto, Pick 3 4, Powerball, Mega Millions, Euromillions, Keno_**","target":"https://saliu.com/free-lotto-lottery.html","line":18},{"title":"Losses amount to thousands of dollars in a lifetime for just one lottery game like pick-3.","target":"https://saliu.com/HLINE.gif","line":24},{"title":"Forums","target":"https://forums.saliu.com/","line":26},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":26},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":26},{"title":"Contents","target":"https://saliu.com/content/index.html","line":26},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":26},{"title":"Home","target":"https://saliu.com/index.htm","line":26},{"title":"Search","target":"https://saliu.com/Search.htm","line":26},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":26},{"title":"You can win the lottery only if playing systems, strategies with good lottery software.","target":"https://saliu.com/HLINE.gif","line":28}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06669943,-0.05173101,-0.02129863,-0.00845177,-0.07505129,0.04530392,0.03795592,-0.00821622,0.04159065,0.02626668,0.02163352,0.01354533,0.05576151,0.01217919,-0.01220587,-0.03682212,0.00558622,0.05042461,-0.05596317,-0.01138877,0.08799005,-0.03967649,-0.09132964,-0.07185598,0.04843675,0.00178041,-0.02938071,-0.07800948,-0.03048185,-0.14553279,0.01102229,-0.00309874,-0.00346476,-0.07975196,-0.07857693,-0.04567435,-0.04895125,0.06748095,-0.09203289,0.03775807,0.01582634,0.0344376,-0.02664487,-0.01328156,-0.00117121,-0.02766078,0.04064704,0.02489513,0.07948013,0.00458127,-0.02730374,0.06101179,-0.01716736,0.04981729,0.04821938,-0.00060668,0.05191741,0.1119081,-0.01182997,0.00554054,0.02708614,0.06370413,-0.1775182,0.04244993,-0.01572216,0.04826749,0.01597905,0.01857639,0.02684523,0.070457,0.02052491,-0.02777475,0.0051099,0.05696389,0.04896243,-0.03005328,-0.03604683,-0.06547378,-0.03513094,0.02225288,-0.05007076,-0.02789843,0.00707842,0.02759474,-0.00343381,0.02189642,0.06353848,0.03418685,0.08286671,-0.08505785,0.01842251,0.05570103,0.02381232,0.02906023,0.03240237,0.00740554,0.07212327,-0.04423928,0.0057073,0.15988898,-0.0190943,-0.01397117,0.00255039,-0.01837613,0.03786274,-0.08386219,-0.03493782,-0.04084813,-0.03695851,0.03105121,0.04342757,0.02372054,0.07760668,-0.0298809,-0.02622938,-0.02806247,-0.0112312,0.00563335,0.03210088,-0.00094599,-0.02374889,0.03315655,0.03173618,-0.03228975,0.01836292,-0.01616279,0.01039661,0.07272853,-0.01192288,0.03101779,0.05797194,-0.00453995,-0.11834362,-0.01386776,0.01002527,-0.03909518,0.02943598,-0.00756595,-0.04387436,0.01726109,-0.00024317,0.00540364,0.05425948,-0.12617312,-0.00372889,0.05563581,0.02363733,0.02987262,0.0029857,-0.00345523,-0.00063468,-0.03179859,-0.02968082,-0.0767924,-0.01820607,0.01408955,0.07903181,0.02485232,-0.03610916,-0.00896583,-0.02808197,-0.00547511,-0.04305429,0.11956198,-0.04271613,-0.09188361,-0.04475331,0.02366732,-0.03844259,-0.08102784,-0.04655115,0.00147472,-0.05944014,-0.0081291,0.11445211,-0.01416514,-0.08768209,-0.0631848,-0.02869964,-0.01973521,0.02480768,-0.01772789,-0.0130973,-0.00977046,-0.00232469,-0.04012107,-0.00346538,-0.02368958,0.05039333,0.06865212,-0.01600931,0.0322234,-0.05849136,-0.00072952,-0.03315484,0.01033009,-0.04841763,-0.02777833,0.07540865,-0.02324048,-0.00245579,-0.02376513,0.00104835,0.04659276,-0.04293659,0.01784163,-0.0055959,-0.07885946,0.08182482,0.05726848,-0.06372482,-0.00586311,0.07563214,0.0548092,-0.03852477,0.01918229,0.02453952,0.02850437,-0.02187495,0.02075385,-0.00539181,-0.01009796,-0.07686902,-0.17866661,-0.06274538,-0.02890319,-0.02528716,0.03813245,-0.03410098,0.00294546,-0.06349563,0.00188855,0.09218659,0.10065173,-0.10133407,0.01919496,0.07968895,-0.02570581,-0.00895599,-0.08014262,-0.04222699,-0.0431442,0.03412934,0.01122966,0.00746978,0.01501392,-0.10037503,0.00667732,-0.01468272,0.13904268,-0.04375775,0.04595833,0.02626156,0.04377004,0.01013876,-0.02630637,0.02527999,0.01259379,0.03278513,-0.06327986,-0.01305149,-0.02631506,-0.00492053,-0.04365556,-0.03359782,0.03377831,-0.0743607,-0.03435341,-0.00568845,-0.03821679,0.05074911,-0.00506754,0.08969089,0.0301617,0.03762174,0.0682881,0.01431236,0.06146354,-0.01773235,-0.04601347,0.00302446,-0.04034661,0.03045405,-0.00377206,-0.07859762,0.02663122,-0.02396194,0.08392181,-0.01231616,-0.03000621,-0.02534658,-0.00101633,-0.02717541,0.00988993,0.05879667,-0.00150332,0.01197794,-0.01321906,0.02840152,0.10453697,-0.04841904,-0.04209356,-0.01327052,-0.00471502,-0.08401081,0.04761486,0.06384672,0.07791466,0.07032149,0.06187984,0.05071133,-0.0291963,-0.01374279,-0.02165528,-0.00106092,-0.06070291,-0.00365997,-0.03112481,-0.00431575,-0.2276451,0.03964031,-0.04416182,0.05901379,-0.05660615,-0.05718399,0.00498229,0.04197399,0.01656132,-0.05156656,0.06585075,0.02221466,-0.00386192,-0.03604293,0.02603834,-0.01589716,0.01879359,-0.03278734,0.08512171,0.08473779,0.07078411,0.05741213,0.23502742,-0.00015629,0.03565872,0.00286934,0.00659999,0.02694371,-0.00232852,0.0447787,0.03786747,0.01439176,0.05962425,0.00674466,-0.01899656,0.02739843,-0.00408011,0.03583563,-0.03811945,0.01757221,-0.04807999,-0.01305701,-0.06712066,0.07535599,0.12119463,0.0321275,-0.04242299,-0.07506433,0.03911676,0.04718279,-0.10867085,-0.02098319,-0.04345372,-0.05593371,0.00651356,0.03800743,0.00347528,-0.02812788,0.02225565,-0.02837544,0.02217312,-0.00781695,0.05256025,0.0028755,-0.0131611],"last_embed":{"hash":"1m22jsz","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423485492},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{3}","lines":[93,93],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0629265,-0.01907001,-0.01825089,-0.03712027,-0.05648488,0.05043175,0.02888707,0.00447324,0.04836639,0.01287944,0.03753867,0.0120735,0.0780822,-0.01052601,-0.0326701,-0.05019469,-0.01875029,0.03387323,-0.04778304,-0.03692377,0.07722961,-0.02447392,-0.07739719,-0.08034644,0.06810992,-0.01948685,-0.04756663,-0.08823152,-0.06076209,-0.17713659,0.01637282,-0.01113221,0.00091494,-0.07807632,-0.06770143,-0.00242787,-0.0324205,0.04458573,-0.02726346,0.03322469,0.02080502,0.01780041,-0.02453888,0.01206573,0.00372048,-0.02337506,0.02837232,0.02962852,0.040609,0.01405904,-0.0680622,0.05103391,0.01092805,0.03573205,0.04586908,0.00463402,0.04306719,0.1033457,0.02101716,0.03532891,0.04326309,0.05219838,-0.20895821,0.05094606,0.01947884,0.02931593,0.00444371,-0.00222524,0.00102849,0.05218169,-0.00436374,-0.04026916,0.00265448,0.06027034,0.07204896,-0.03943247,-0.05927121,-0.07255097,-0.05602843,0.0235799,-0.03781937,0.00718729,0.0104402,0.01817274,-0.00428882,0.01686292,0.07167259,0.02231879,0.09647211,-0.06543709,0.04493253,0.07288215,0.01003999,0.02049119,0.02846818,0.00472259,0.05688077,-0.06051466,0.01714778,0.16134585,-0.0374781,-0.01976566,-0.00280991,-0.0249499,0.02540788,-0.09520944,-0.01920534,-0.03314915,-0.01792126,-0.00656869,0.0067121,0.02476514,0.0429942,-0.04661783,-0.03118124,-0.01823383,-0.00882926,-0.02172174,0.02147104,0.0227039,-0.00348521,-0.00439157,0.04257214,-0.02566178,0.0182019,-0.02545486,0.04119379,0.06980946,-0.02188106,0.01734412,0.06234973,0.0105184,-0.13627326,-0.01836637,-0.00568372,-0.01207142,0.03374014,-0.01665325,-0.01344599,0.00402249,-0.00300013,0.01014094,0.03671912,-0.10832149,0.00273084,0.09232155,0.03184206,0.00528136,0.00092089,-0.01484439,0.02347486,-0.03919413,-0.03260976,-0.05585021,-0.01051587,0.01654454,0.07006425,0.05265379,-0.0111921,-0.03531748,-0.01940929,-0.02394235,-0.04454819,0.13374461,-0.04749712,-0.10939217,-0.03441435,0.00743809,-0.04393683,-0.09881727,0.00351654,-0.02617281,-0.05303664,0.00224961,0.06404535,-0.00358707,-0.04372797,-0.05274501,-0.01209285,-0.01507397,0.03039462,-0.02120348,-0.02890963,0.00988442,-0.02243579,-0.03633534,-0.00126398,0.0059814,0.02569467,0.07547771,-0.00562958,0.01497465,-0.08148815,0.01323104,-0.01051607,0.00519368,-0.02558671,-0.0380227,0.08115711,-0.05735941,0.01632849,-0.03379985,-0.00771077,0.03950753,0.00529466,0.02426926,-0.03088635,-0.06570671,0.07195558,0.04856477,-0.0619896,-0.00508216,0.06796284,0.03793649,-0.01297268,0.02738849,0.02800957,-0.00832513,-0.0058058,0.04853539,0.00891981,-0.0106842,-0.04139033,-0.19469139,-0.06073634,-0.02860141,-0.04429649,0.02262965,-0.03992357,0.01096145,-0.07150667,-0.0116506,0.10729026,0.08951207,-0.06829846,0.01666239,0.06071372,-0.0161102,-0.00675626,-0.0856415,-0.05580772,-0.08656045,0.05880011,-0.00012782,0.00789727,-0.02276469,-0.05756902,0.01611396,-0.02535951,0.1182431,-0.0553024,0.03695006,0.01218,0.05968418,0.01698111,-0.00799307,0.01412189,0.00583416,0.04149069,-0.04504974,0.00417617,-0.03025031,0.00279981,-0.07499761,0.00884404,0.04650621,-0.07636165,-0.04125322,0.01825427,0.01085296,0.02809914,-0.01430012,0.07075324,0.03501481,0.00729287,0.06110335,0.03899467,0.06897771,-0.02715974,-0.02373397,0.03820727,-0.0493622,0.0165473,-0.04586858,-0.09259017,0.05530053,-0.00995765,0.07843583,-0.0199264,-0.0202297,-0.01106142,-0.0019294,-0.00924335,0.0042679,0.0609227,-0.02020007,0.00002241,-0.02650973,0.04696937,0.10223716,-0.05043032,-0.0534995,-0.01382224,0.01260021,-0.1079028,0.05669175,0.01626363,0.06885274,0.08261289,0.07257108,0.03238229,-0.00616327,-0.00575735,-0.00813158,-0.04059377,-0.04466759,0.01989655,-0.0275155,-0.00302699,-0.21545468,0.03702372,-0.04559606,0.04988555,-0.04689872,-0.04848699,0.0021169,0.01338941,0.04195648,-0.04005033,0.06049169,0.02854773,-0.0041441,-0.05346556,0.01134543,-0.02418131,0.03971086,-0.02821745,0.07602614,0.09502132,0.07112685,0.05057715,0.24935158,0.00588058,0.019188,0.00648089,0.02120544,0.01331355,-0.00806431,0.03701868,0.04948015,0.0056176,0.07550721,-0.00575503,-0.02843571,0.00339291,-0.01367987,0.06913614,-0.00875883,0.02142439,-0.02736008,-0.0191518,-0.08082844,0.02068295,0.13096124,0.05886102,-0.05135756,-0.05742727,0.03362764,0.03492855,-0.091325,-0.03516342,-0.05716303,-0.05767052,-0.0104608,0.03443431,-0.02321682,-0.00859677,0.05093704,-0.00426003,0.06263894,0.01344595,0.04123512,0.02658778,0.00521999],"last_embed":{"hash":"pazr3d","tokens":84}}},"text":null,"length":0,"last_read":{"hash":"pazr3d","at":1753423485522},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{5}","lines":[95,95],"size":231,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07148873,-0.05711959,-0.03400949,0.00461307,-0.08254672,0.07748647,0.04351948,0.00876936,0.04260928,0.01966954,0.01974206,-0.0114272,0.04878512,0.02896701,-0.01852626,-0.03618396,-0.02685595,0.04201835,-0.06407823,-0.00245312,0.11872685,-0.07000577,-0.07081372,-0.05430183,0.05495152,-0.01020465,-0.02363174,-0.06502128,-0.03975379,-0.1635374,-0.01485266,-0.0019932,-0.01450265,-0.0854886,-0.0928146,-0.03091434,-0.04711252,0.09794152,-0.04768204,0.05567703,0.02315732,0.02741639,-0.04204242,-0.00735887,0.00284096,-0.02520446,0.02010299,0.00346911,0.04265222,0.00308208,-0.02823145,0.05592495,-0.01523877,0.03752777,0.06590404,0.00459005,0.07183714,0.10971628,0.00352301,0.01714431,0.0239971,0.05860883,-0.20244579,0.05696692,-0.00400375,0.01681068,0.011938,-0.00499257,0.00523126,0.08330052,0.01674079,-0.03418347,0.00678839,0.05462331,0.05342143,-0.03197266,-0.03061974,-0.08110617,-0.03620857,0.03480924,-0.03418427,-0.01374655,-0.01837828,0.00306035,0.03035646,0.01685023,0.04980968,0.03690919,0.05709815,-0.07132279,0.03619511,0.06166837,0.01377991,0.024404,0.02134938,0.0143364,0.04461417,-0.05177895,0.01236116,0.14347868,-0.00518451,0.00868087,-0.0020836,0.00905594,0.04370705,-0.08945744,-0.03722946,-0.04051713,-0.01994171,0.01204735,0.03031161,0.01421719,0.08347794,-0.02331568,-0.01848039,-0.01197843,0.02544888,0.00912024,0.02967276,0.01110969,-0.01677221,0.04737336,0.06745505,-0.02437583,0.01060705,-0.0130499,0.00501723,0.07164362,-0.0018042,0.05191782,0.05851423,-0.01072793,-0.10041201,-0.0215801,-0.01610197,-0.0095572,0.05340781,-0.00830492,-0.03404861,0.01500844,0.02348042,-0.02100703,0.0352715,-0.09528139,0.00240201,0.06641486,0.01076408,0.03040736,0.03767589,0.01042917,-0.01069823,-0.0397021,-0.02705025,-0.06385142,-0.04431532,0.0087179,0.04850603,0.05153962,-0.02944226,-0.00907961,-0.02301921,-0.03413048,-0.0482354,0.12458852,-0.05102395,-0.08025523,-0.05204232,-0.01199212,-0.02544258,-0.10490895,-0.03218855,-0.00684595,-0.04626208,0.01134264,0.10857663,-0.00828586,-0.06349052,-0.06669757,-0.03847209,0.01028181,0.01142661,-0.02284558,-0.02536958,-0.00618345,-0.02596492,-0.01773482,0.00672807,-0.01491585,0.06098654,0.06769545,-0.03272976,0.0101609,-0.08107238,0.01133811,-0.01749604,0.01869832,-0.02737982,-0.03993338,0.06317114,-0.02569936,-0.0059777,-0.03149702,-0.01528724,0.03133566,-0.03963403,0.02720055,-0.00428207,-0.06453792,0.07374556,0.04226526,-0.04298183,-0.00294912,0.05016521,0.04711365,-0.01152819,0.01059466,0.05576866,0.025525,-0.0013034,0.00302839,0.01606144,-0.04212902,-0.06037697,-0.18617661,-0.06876677,-0.03522484,-0.03646285,0.04428432,-0.0483335,0.00004128,-0.0592971,-0.00066058,0.10533395,0.07139619,-0.07632431,0.00809556,0.05142177,-0.01709407,0.00263956,-0.08670054,-0.05914242,-0.04408055,0.03978616,0.0009237,0.04340436,-0.01951122,-0.0865388,0.00843431,-0.00968611,0.15555282,-0.00915638,0.01235787,0.01609358,0.04349826,0.03706513,-0.02750132,0.05745649,0.02041887,0.01044609,-0.06679746,-0.02401875,-0.05347725,-0.04083054,-0.05000891,-0.00541986,0.02971476,-0.057326,-0.04044912,-0.00532439,-0.035201,0.0648742,-0.01210786,0.08750573,0.03616231,0.0218535,0.05902095,0.0039379,0.05758246,-0.01025501,-0.03929632,-0.0145736,-0.05346658,0.03691621,-0.01002274,-0.0462666,0.04491054,-0.03628433,0.08268768,-0.00893153,-0.00798039,-0.01782827,-0.01402848,-0.00177984,-0.00247115,0.04674684,0.01482611,-0.00241288,-0.01316595,0.01332295,0.08225743,-0.02885204,-0.0253973,-0.02807796,0.02454503,-0.09423462,0.03339837,0.04249702,0.07526881,0.0666144,0.04523119,0.02963099,-0.02055124,-0.00796369,-0.03000362,-0.00247308,-0.05352088,0.00384392,-0.03455956,-0.01270597,-0.24711999,0.04060752,-0.05087717,0.05951659,-0.05276896,-0.04558512,0.00355827,0.03403485,0.01915993,-0.05903865,0.08885071,0.03341266,0.01064218,-0.03180718,-0.00173993,-0.02989286,-0.01025034,-0.03166812,0.08586152,0.08799168,0.08034973,0.04772507,0.23742135,0.00019821,0.00700825,-0.02728222,0.00785194,0.03325546,-0.02460463,0.02299944,0.03690073,0.02038322,0.05913757,-0.01498458,-0.01263721,0.04177874,-0.01424354,0.05200453,-0.01955459,-0.00271538,-0.03113656,-0.03254345,-0.06618546,0.08503219,0.14655249,0.05310064,-0.02700266,-0.0595497,0.04578602,0.03774969,-0.09574927,-0.0444344,-0.04849326,-0.05594181,-0.00972358,0.0449869,-0.01356536,-0.02215691,0.03166498,-0.01896434,0.02068977,0.04130212,0.05508855,0.01019921,-0.00394889],"last_embed":{"hash":"7kdphw","tokens":103}}},"text":null,"length":0,"last_read":{"hash":"7kdphw","at":1753423485551},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{10}","lines":[100,100],"size":227,"outlinks":[{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10810459,-0.05419268,-0.04487583,0.00543463,-0.04832935,0.09288654,0.01269563,0.03381776,0.05061683,0.01253776,-0.00303066,-0.01670814,0.02986141,0.02018297,-0.01750877,-0.05168165,-0.0161719,0.03160761,-0.07218555,0.02323851,0.11426207,-0.03587033,-0.03988417,-0.06009087,0.05102793,-0.04199003,0.00469014,-0.08585852,-0.05124318,-0.19565256,0.0187076,-0.0240698,-0.01269486,-0.07677932,-0.09874066,-0.00985023,-0.04324491,0.05578908,-0.03704758,0.04706533,0.02736043,0.04003371,-0.00437963,-0.03123516,-0.00390089,-0.01911437,-0.01253475,0.0091502,0.08569196,0.01648754,-0.05185005,0.07624444,0.00988389,0.03599578,0.05711251,0.01530468,0.06105354,0.13642801,-0.01279779,0.01718479,0.01943774,0.01997122,-0.22429278,0.04429417,-0.02204276,0.02698943,0.00556659,0.01759691,0.00049085,0.0526844,0.01221217,-0.01110777,0.00270375,0.04436723,0.0526051,-0.04886098,-0.0100153,-0.04064113,-0.04837241,0.01076902,-0.03340056,-0.03716621,-0.03028236,-0.03014171,0.04190847,0.01393751,0.04801598,0.06459329,0.03098514,-0.09679901,0.02004196,0.05877437,0.02268737,0.02466746,0.00918594,-0.00252572,0.0435435,-0.03587542,-0.01702286,0.1167217,0.02787293,-0.00244101,-0.01780538,-0.00075842,0.08424552,-0.05728051,-0.04986331,-0.00973825,-0.0159024,0.01082276,0.0287337,0.0285763,0.08076692,-0.05021045,-0.04544681,0.01084989,0.02997431,0.00948345,0.03505294,0.00957221,-0.01516803,0.02498296,0.03608316,0.00000385,0.03066294,-0.03831729,0.02380454,0.03686581,0.01048641,0.06245764,0.05567483,-0.00504635,-0.13229631,-0.04918602,-0.02533251,-0.01592386,-0.00005225,-0.01516886,-0.01067846,-0.00406333,0.01469972,-0.05541235,0.00159548,-0.10267492,0.0077637,0.0297778,0.03830457,0.02302141,0.00728392,0.01750586,0.00412046,-0.01737357,-0.00181373,-0.07740007,-0.01957315,0.00144839,0.06038799,0.0706266,-0.0570255,0.00521126,-0.00547934,-0.05006994,-0.01064075,0.09819737,-0.01090625,-0.04799389,-0.08879879,0.00666087,-0.01593351,-0.0964864,-0.04140264,-0.02748791,-0.04928742,-0.0157197,0.09894476,-0.00957431,-0.05660053,-0.07094539,-0.01652599,0.02347271,-0.02707484,-0.01703478,0.00283485,-0.00835675,-0.03101848,-0.0546611,0.03758624,-0.04632414,0.05205134,0.03540294,-0.03357308,0.01071028,-0.06858984,0.04017114,-0.0323585,0.0416341,-0.03341686,-0.06644832,0.05776703,0.00276158,-0.00934768,-0.01544442,-0.00525393,0.02272912,-0.04197595,0.02969707,-0.01871363,-0.06917608,0.09964789,0.01060887,-0.02802205,0.01504978,0.07047649,0.04889708,-0.01273074,0.02581623,0.03739262,0.0327086,0.01695996,0.02741938,0.02147087,-0.01035056,-0.07671818,-0.18964303,-0.04945089,-0.07151522,-0.06511071,0.04606799,-0.02211243,0.02705888,-0.08160721,0.03095296,0.06974787,0.08493346,-0.05469915,0.02623688,0.07174169,-0.00187732,-0.0130281,-0.0790732,-0.02492975,-0.03591717,0.06508852,-0.00892901,0.01423027,-0.0116549,-0.0538033,0.04919606,-0.03636395,0.15522309,0.02433227,0.03712334,-0.02396865,0.05928094,0.05531424,-0.02312396,0.00618643,0.02121932,0.03422129,-0.04914766,-0.00275782,-0.03952695,0.00387662,-0.05576539,-0.00362516,0.02697024,-0.07944448,-0.0635124,-0.002654,-0.02730385,0.04554123,-0.02707937,0.07412673,0.09415237,0.00914868,0.06264868,-0.00039636,0.08919867,-0.02784938,-0.06796354,-0.00177086,-0.04013257,0.03365301,-0.01960134,-0.01350956,0.07265301,-0.04362577,0.07702324,-0.01296564,-0.02260257,-0.02055976,-0.00353622,-0.02439738,-0.0063562,0.04560274,0.00727396,0.0027633,0.03531397,0.00815274,0.06871872,-0.04857943,-0.04629913,-0.02393057,0.01277788,-0.07671744,0.03453499,0.01927196,0.07447813,0.1020086,0.06165244,0.01463481,-0.01918859,-0.01387175,-0.00028157,0.01019099,-0.07232033,0.03316203,-0.00775256,0.00881608,-0.23382996,0.0029482,-0.02707746,0.03759466,-0.03665412,-0.03323711,0.0233895,0.00558179,0.00643538,-0.04611542,0.0729757,0.05450175,-0.00630599,-0.05894952,0.00847153,-0.02890512,-0.04132336,0.00483455,0.07955057,0.07070132,0.07899866,0.03969441,0.22733004,-0.00788314,-0.01880443,-0.02477219,0.01356745,0.00282916,0.01766513,0.01799609,0.01131398,0.00356319,0.05276119,-0.01703798,0.00379689,0.00018058,-0.01942327,0.05008076,-0.04792109,-0.02208598,-0.01929191,-0.02281616,-0.02164448,0.06970163,0.13839319,0.02534414,-0.01545595,-0.04067287,0.03703199,0.04865902,-0.09947756,-0.04275599,-0.0143874,-0.05895021,0.01876517,0.05214623,0.01212289,-0.01619172,0.04572089,-0.01913741,0.00902087,0.06288283,0.03700519,-0.00729451,0.01623371],"last_embed":{"hash":"1586194","tokens":347}}},"text":null,"length":0,"last_read":{"hash":"1586194","at":1753423485585},"key":"notes/saliu/Lottery Numbers Loss, Cost, Drawings, House Advantage.md#Lottery Numbers: Loss, Cost, Drawings, House Advantage#The Best Ever Lottery Strategy, Lotto Strategies.#{16}","lines":[107,116],"size":783,"outlinks":[{"title":"Losses amount to thousands of dollars in a lifetime for just one lottery game like pick-3.","target":"https://saliu.com/HLINE.gif","line":5},{"title":"Forums","target":"https://forums.saliu.com/","line":7},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":7},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":7},{"title":"Contents","target":"https://saliu.com/content/index.html","line":7},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":7},{"title":"Home","target":"https://saliu.com/index.htm","line":7},{"title":"Search","target":"https://saliu.com/Search.htm","line":7},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":7},{"title":"You can win the lottery only if playing systems, strategies with good lottery software.","target":"https://saliu.com/HLINE.gif","line":9}],"class_name":"SmartBlock"},
