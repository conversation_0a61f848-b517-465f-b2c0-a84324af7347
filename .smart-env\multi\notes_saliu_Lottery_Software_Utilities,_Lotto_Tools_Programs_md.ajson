
"smart_sources:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md": {"path":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13012524,-0.04991159,-0.02947153,-0.02141909,-0.0295682,0.00759957,-0.04519982,-0.00355251,0.02694026,-0.01909521,0.00095323,-0.03068468,0.04310873,-0.02542311,-0.01901073,-0.04071816,0.01716439,0.0002232,-0.08628261,0.00341581,0.08200273,-0.04047217,-0.05772468,-0.08754233,0.0457527,0.02457915,-0.02691175,-0.03602914,-0.0278398,-0.21289083,0.01330372,0.02323027,0.02848308,-0.03229352,-0.08098178,-0.0133362,-0.01399383,0.02754804,-0.08146521,0.00739603,-0.00687529,0.03828961,-0.03045998,-0.0505757,0.03117092,-0.07332427,0.01735549,0.00026654,0.05107763,0.01550378,-0.02574414,-0.00239193,-0.00768163,0.03264893,0.07326498,0.02080025,0.05606259,0.06976794,0.0079714,0.05025957,0.02738741,0.08920155,-0.1864232,0.06855167,0.01975023,0.02189187,-0.02304886,-0.05564396,0.01521769,-0.01499566,-0.00032509,0.03306871,-0.00123872,0.06783966,0.00443386,-0.02546489,-0.04753456,-0.04193934,-0.0760266,0.01366633,-0.05369809,-0.01619831,-0.01332468,-0.00685987,0.02273277,0.05381947,0.04575318,0.07000464,0.08631665,-0.07033393,0.00556785,-0.00457704,0.08501462,0.04225077,0.03690732,0.03070745,0.05452958,-0.03036215,-0.01140403,0.11129962,-0.01986128,-0.02510116,0.00078209,0.01109196,0.07191809,-0.02979518,-0.01102663,-0.03563707,-0.02651384,0.00301042,0.04100489,-0.00219568,0.03562192,-0.05755727,-0.03672724,-0.01367076,-0.02671999,-0.00261907,0.01040009,0.0385512,-0.07569762,0.00283708,0.0197354,0.00585064,0.02327537,0.01086763,0.02347334,0.04210279,0.02149455,0.0425134,0.04196351,0.03200997,-0.14960046,-0.0270908,-0.00463225,-0.00010616,-0.0088748,-0.05010343,-0.00308399,0.0164947,-0.00201547,-0.03485372,0.05499175,-0.1103505,0.01748466,0.06413013,-0.01532801,0.0048292,0.02488508,-0.00845816,-0.00862438,0.00291788,0.00199968,-0.07229896,0.04274335,0.01506184,0.13353522,0.0537609,-0.04744898,0.02076562,0.00798887,-0.02673679,-0.04655607,0.11803327,-0.00929845,-0.13581836,-0.00640321,0.06927688,-0.03359019,-0.04877786,0.00461729,0.03349182,-0.03762026,0.03083553,0.08071413,-0.06024697,-0.02284964,-0.06793787,-0.00450324,-0.00502761,-0.00364975,-0.01240299,-0.04375929,-0.00762401,-0.02803365,-0.08154631,0.02746941,-0.04772685,0.03302971,0.08495796,-0.02116035,0.00489116,0.0229491,-0.01339056,-0.02241106,-0.04159071,-0.05324145,-0.04718022,0.05432823,-0.0390513,-0.03479962,0.01806072,0.02793839,0.02198604,-0.01819342,0.04385316,-0.06666055,-0.04728334,0.05565659,-0.0067552,-0.01099161,0.00987205,0.08612339,0.10264263,-0.1006103,0.03204481,0.00048488,0.02775411,0.00690601,0.0737707,-0.02152277,0.02133997,-0.07486927,-0.20107593,-0.02051303,-0.03771161,0.01482214,0.00486561,-0.03166035,0.05307928,-0.03479977,0.00336327,0.08126753,0.10409354,-0.06876527,0.00314597,0.01201058,-0.00744804,0.02037198,-0.07893004,-0.05230213,-0.04485174,0.06655276,0.00101238,0.00701842,0.00300203,-0.06488853,0.03781667,-0.06671083,0.1350614,0.0325412,-0.01208239,-0.03481014,0.07266661,0.01338021,-0.00332683,-0.04500503,0.01049677,0.04559856,-0.02249279,0.02707893,-0.00879815,0.01106081,-0.07565458,0.02958348,0.01799577,-0.09430673,-0.0022118,-0.01752777,-0.00090819,-0.01738336,-0.00806839,0.04806288,0.05408495,0.01453532,0.04595003,0.00507142,0.06343856,-0.00874974,-0.08455766,0.0102899,0.00081283,0.02089372,0.02694334,-0.06674296,0.07017925,-0.03638437,0.06104356,0.03591011,-0.0127366,-0.02050418,-0.01211195,0.00463293,-0.01420211,0.07012205,0.0038086,0.07106405,-0.04454209,0.02691961,0.05321207,-0.04807352,0.02154458,-0.01453144,-0.02917231,-0.05696345,0.04039203,0.07396542,0.04634101,0.02437399,0.04190288,0.05978739,0.02446411,-0.00428633,0.01782359,0.00715644,-0.01902223,0.03875148,0.03186313,0.06701171,-0.27103996,0.02453689,-0.00301138,0.01200883,-0.00470241,0.00629853,0.00940099,-0.06695884,0.04683831,-0.0505284,0.05106845,0.00746174,0.02402565,-0.07879121,0.02372095,-0.00233777,0.03975511,-0.00401155,0.0510291,0.01380702,0.02838703,0.02376807,0.23621182,-0.00142149,-0.01295362,0.04405947,0.02608486,-0.0207942,0.03261416,0.0331931,0.00487883,0.02680921,0.07694388,0.00040137,-0.04577237,0.05260469,-0.028344,0.00196565,-0.01031025,0.01684929,-0.06244143,0.03290129,-0.04551663,-0.01902426,0.08995798,0.0240399,-0.01424702,-0.06446353,0.03610732,0.0444576,-0.102011,-0.06874164,-0.06538668,-0.01685359,0.02416509,0.02315823,0.01035431,0.02495387,-0.00459612,-0.01726011,0.04807075,0.0165726,0.06646933,0.00724953,-0.00683357],"last_embed":{"hash":"3cosyp","tokens":478}}},"last_read":{"hash":"3cosyp","at":1753423493573},"class_name":"SmartSource","last_import":{"mtime":1753366216617,"size":29725,"at":1753423416052,"hash":"3cosyp"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Software Utilities, Lotto Tools Programs":[8,299],"#Lottery Software Utilities, Lotto Tools Programs#{1}":[10,15],"#Lottery Software Utilities, Lotto Tools Programs##I. [Overview of Lottery, Lotto Software Utilities](https://saliu.com/lottery-utility.html#Utility)":[16,31],"#Lottery Software Utilities, Lotto Tools Programs##I. [Overview of Lottery, Lotto Software Utilities](https://saliu.com/lottery-utility.html#Utility)#{1}":[17,31],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>":[32,81],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{1}":[34,35],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{2}":[36,36],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{3}":[37,37],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{4}":[38,38],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{5}":[39,39],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{6}":[40,40],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{7}":[41,41],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{8}":[42,42],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{9}":[43,44],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{10}":[45,50],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{11}":[51,51],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{12}":[52,52],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{13}":[53,53],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{14}":[54,55],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{15}":[56,59],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#1\\. Simulate lottery data files":[60,61],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#2\\. Strip off duplicates":[62,63],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#3\\. Count lines in files":[64,65],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#4\\. Check for lottery winners":[66,67],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#5\\. Statistical reporting (frequency, pairing, skipping, etc.)":[68,69],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#6\\. Calculate odds, generate random numbers":[70,71],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#7\\. Sort or add-up lottery data files":[72,73],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#8\\. Make/break/position.":[74,81],"#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#8\\. Make/break/position.#{1}":[76,81],"#Lottery Software Utilities, Lotto Tools Programs#<u>2. Simulate Lotto, Lottery Data Files</u>":[82,102],"#Lottery Software Utilities, Lotto Tools Programs#<u>2. Simulate Lotto, Lottery Data Files</u>#{1}":[84,102],"#Lottery Software Utilities, Lotto Tools Programs#<u>3. Strip Duplicates and Wheel Lotto, Lottery Numbers</u>":[103,124],"#Lottery Software Utilities, Lotto Tools Programs#<u>3. Strip Duplicates and Wheel Lotto, Lottery Numbers</u>#{1}":[105,124],"#Lottery Software Utilities, Lotto Tools Programs#<u>4. Count Lines in Lotto, Lottery Files, or Any Text Files</u>":[125,133],"#Lottery Software Utilities, Lotto Tools Programs#<u>4. Count Lines in Lotto, Lottery Files, or Any Text Files</u>#{1}":[127,133],"#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>":[134,162],"#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>#{1}":[136,145],"#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.1.":[146,151],"#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.1.#{1}":[148,151],"#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.2.":[152,162],"#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.2.#{1}":[154,162],"#Lottery Software Utilities, Lotto Tools Programs#<u>6. Statistical Reporting: Frequency, Skips, Pairs</u>":[163,187],"#Lottery Software Utilities, Lotto Tools Programs#<u>6. Statistical Reporting: Frequency, Skips, Pairs</u>#{1}":[165,187],"#Lottery Software Utilities, Lotto Tools Programs#<u>7. Calculating Lotto Odds, Generating Random Numbers</u>":[188,200],"#Lottery Software Utilities, Lotto Tools Programs#<u>7. Calculating Lotto Odds, Generating Random Numbers</u>#{1}":[190,200],"#Lottery Software Utilities, Lotto Tools Programs#<u>8. Sort, Add-Up Lottery, Lotto Data Files</u>":[201,213],"#Lottery Software Utilities, Lotto Tools Programs#<u>8. Sort, Add-Up Lottery, Lotto Data Files</u>#{1}":[203,213],"#Lottery Software Utilities, Lotto Tools Programs#<u>9. Make/Break/Position</u>":[214,272],"#Lottery Software Utilities, Lotto Tools Programs#<u>9. Make/Break/Position</u>#{1}":[216,272],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)":[273,299],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{1}":[275,276],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{2}":[277,277],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{3}":[278,278],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{4}":[279,279],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{5}":[280,280],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{6}":[281,281],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{7}":[282,282],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{8}":[283,283],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{9}":[284,284],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{10}":[285,285],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{11}":[286,286],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{12}":[287,287],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{13}":[288,288],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{14}":[289,289],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{15}":[290,290],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{16}":[291,291],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{17}":[292,293],"#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{18}":[294,299]},"outlinks":[{"title":"This is software for lottery, lotto tools, number generators, statistics, many functions.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Overview of Lottery, Lotto Software Utilities","target":"https://saliu.com/lottery-utility.html#Utility","line":16},{"title":"Simulate Lotto, Lottery Data Files","target":"https://saliu.com/lottery-utility.html#Simulate","line":17},{"title":"Strip Duplicates and Wheel Lotto, Lottery Numbers","target":"https://saliu.com/lottery-utility.html#Wheel","line":18},{"title":"Count Lines in Lotto, Lottery Files, or Any Text Files","target":"https://saliu.com/lottery-utility.html#Count","line":19},{"title":"Check for Winning Combinations in Lotto, Lottery Output Files","target":"https://saliu.com/lottery-utility.html#Winners","line":20},{"title":"Statistical Reporting: Frequency, Skips, Pairing","target":"https://saliu.com/lottery-utility.html#Statistics","line":21},{"title":"Calculating Lotto Odds, Generating Random Numbers","target":"https://saliu.com/lottery-utility.html#Odds","line":22},{"title":"Sort, Add-Up Lottery, Lotto Data Files","target":"https://saliu.com/lottery-utility.html#Sort","line":23},{"title":"Make/Break/Position","target":"https://saliu.com/lottery-utility.html#combinations","line":24},{"title":"Resources in Lotto, Lottery, Software, Programs, Utilities","target":"https://saliu.com/lottery-utility.html#Lottery","line":28},{"title":"See the overview of lottery, lotto software utilities, tools.","target":"https://saliu.com/images/lotto.gif","line":30},{"title":"_**Lottery Software for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/powerball-mega-millions.html","line":47},{"title":"**ToolsLotto5**: _**Special upgrade to the lottery utility applications for 5-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/toolslotto5.htm","line":51},{"title":"**ToolsLotto6**: _**Special upgrade to the lottery utility software for 6-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/toolslotto6.htm","line":52},{"title":"**SoftwareLotto**: _**Special upgrades to the lottery utility applications for 5, 6, and 7-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/lotto-software.htm","line":53},{"title":"_**Special upgrades to the utility software for two-in-one (5+1) lotto games: Powerball, Mega Millions, Euromillions, SuperLotto**_","target":"https://saliu.com/gambling-lottery-lotto/power-mega-software.htm","line":54},{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":78},{"title":"For starters, you need simulate lotto, lottery data files to use the software.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":80},{"title":"The software strips duplicates and wheels the lotto, lottery numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":101},{"title":"The software count total lines in lotto, lottery files, or any text files.","target":"https://saliu.com/images/lotto.gif","line":123},{"title":"The software programs check for winners in lotto, lottery combinations.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":132},{"title":"The lottery software generates statistical reports for frequency, pairing, skips or misses.","target":"https://saliu.com/images/lotto.gif","line":161},{"title":"Odds and random numbers are generated by these lotto programs.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":186},{"title":"The lottery software programs sort or add-up lottery, lotto data files.","target":"https://saliu.com/gambling-lottery-lotto/lotto-book.gif","line":199},{"title":"Lottery software breaks strings of numbers in combinations; create lotto combinations by position.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":212},{"title":"_**Excel Spreadsheets in Lotto, Lottery**_","target":"https://saliu.com/Newsgroups.htm","line":236},{"title":"These are special resources in lotto software, lottery software, mathematics, statistics.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":271},{"title":"<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":273},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":277},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":279},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":281},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":283},{"title":"_**<u>Lottery Skip System Software</u>**_","target":"https://saliu.com/skip-strategy.html","line":284},{"title":"_**Lottery Filters, Filtering in Software**_","target":"https://saliu.com/filters.html","line":285},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":286},{"title":"_**Vertical (Positional) Filters in Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":287},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":288},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":289},{"title":"_**Lottery Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":291},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":292},{"title":"Run very useful lottery statistical tools, utilities, apps, lotto combination generators.","target":"https://saliu.com/HLINE.gif","line":294},{"title":"Forums","target":"https://forums.saliu.com/","line":296},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":296},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":296},{"title":"Contents","target":"https://saliu.com/content/index.html","line":296},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":296},{"title":"Home","target":"https://saliu.com/index.htm","line":296},{"title":"Search","target":"https://saliu.com/Search.htm","line":296},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":296},{"title":"Thanks for visiting the site of lottery, lotto, best software programs, utilities.","target":"https://saliu.com/HLINE.gif","line":298}],"metadata":{"created":"2025-07-24T22:10:03 (UTC +08:00)","tags":["software","utilities","tools","programs","Powerball","Mega Millions","lotto","loto","lottery","pairs","pairing","Euromillions","Thunderball","generator","combination","number"],"source":"https://saliu.com/lottery-utility.html","author":null}},"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1339488,-0.0539524,-0.02947068,-0.02237755,-0.03876414,0.00311498,-0.04154256,0.00283344,0.029257,-0.01445076,0.00035299,-0.03437547,0.04196062,-0.00923043,0.00470976,-0.02481781,0.00033966,-0.01386837,-0.08167775,0.01113794,0.11162783,-0.04200224,-0.07205828,-0.07336976,0.04544828,0.02376245,-0.00967498,-0.04885738,-0.01153455,-0.17641667,0.01532297,0.04337489,0.03017153,-0.00940977,-0.05928523,-0.01688665,-0.01601338,0.01958543,-0.06181515,0.01571796,-0.00074191,0.05653752,-0.04292588,-0.07024884,0.02687104,-0.06472859,0.02417013,0.00060354,0.03069788,0.01084252,0.0139538,0.01003291,-0.01674886,0.04558028,0.07043295,0.0216029,0.05623832,0.03699785,0.02311629,0.05024717,0.02377773,0.06998584,-0.20986393,0.06522373,0.02069484,0.02087292,-0.01230785,-0.03253886,0.01086078,-0.03034349,0.02831355,0.02945006,0.00084284,0.05026203,0.00905979,-0.00134581,-0.05599861,-0.03141658,-0.0655157,-0.00141472,-0.04330767,-0.01264035,-0.04067495,-0.03199267,-0.00229241,0.04157992,0.05003834,0.07914469,0.06767073,-0.05153502,-0.02538596,-0.02823664,0.09925485,0.06086419,0.01212075,0.04025375,0.04687811,-0.02718749,-0.04563851,0.12884273,-0.04018574,0.01091098,0.00777267,0.00270239,0.06843816,-0.01616895,-0.00985722,-0.03573762,-0.0285577,-0.01431402,0.02659845,0.01376946,0.04936325,-0.07100152,0.00069318,-0.02150451,-0.02498743,0.01222385,0.02647632,0.05036294,-0.07540165,-0.00271281,0.00789011,0.01356496,0.0217777,0.02581513,0.01589608,0.05355398,0.01483326,0.04175429,0.02174457,0.009327,-0.12545231,-0.02657335,0.01159223,0.01229297,0.00828699,-0.07072444,0.00405248,0.0256669,0.00274178,-0.03255617,0.03333242,-0.11830755,0.03579885,0.0644844,0.01836023,-0.01056187,0.02745843,0.00176111,-0.03496541,-0.01515131,0.01284581,-0.07103667,0.03254701,0.02103888,0.12406085,0.05964742,-0.03190003,0.02507856,0.02675204,-0.02844407,-0.07611509,0.13516457,0.00145259,-0.16356675,-0.01418412,0.03381551,-0.01923088,-0.05873376,-0.00609345,0.05960633,-0.02267647,0.02190326,0.10082726,-0.05240626,0.00122622,-0.06841814,-0.04204699,-0.00155439,-0.01986828,-0.00022557,-0.04375822,-0.00163958,-0.04848163,-0.08659527,0.01228338,-0.0435736,0.04084054,0.08832224,-0.01685573,-0.01620215,0.01522235,-0.00194437,-0.00089774,-0.0405179,-0.04685886,-0.04084687,0.05873699,-0.05436715,-0.03613007,0.03293505,0.00296191,0.02739969,-0.01606637,0.04070266,-0.06892428,-0.03299082,0.03501368,0.00641573,0.00588879,0.02339674,0.08745936,0.09868046,-0.10102694,0.03090815,-0.02051616,0.00228436,0.00791956,0.05249834,-0.0364732,0.03520544,-0.06593369,-0.21322162,0.02297326,-0.03822572,-0.02286897,-0.02547424,-0.02115303,0.04733079,-0.03519804,-0.00598169,0.08228976,0.0946555,-0.0661724,0.01002995,-0.01817703,-0.02518687,0.03274028,-0.04373886,-0.0663416,-0.01466383,0.06528487,-0.0013227,0.01274987,-0.00588191,-0.07191189,0.03598943,-0.05025062,0.134876,0.06944834,-0.03147322,-0.05631888,0.08562189,0.00717105,0.00225731,-0.075836,-0.00177892,0.03545672,-0.02080037,0.02488573,-0.00397446,0.00280193,-0.10767127,0.01691661,0.00114646,-0.09297153,0.0342181,-0.0122937,0.01538828,-0.03265069,-0.00154969,0.04573459,0.05960707,-0.01075548,0.0487734,0.01474458,0.08083532,-0.01777948,-0.10212331,0.01441678,0.00266471,0.00961468,0.0266245,-0.04391755,0.03355618,-0.03964251,0.02625477,0.02770331,-0.02650684,-0.03078896,-0.0375578,-0.00718105,-0.00447771,0.07079431,-0.00041173,0.04174808,-0.04581948,-0.01314935,0.03440853,-0.03800251,0.03593046,-0.01479953,-0.01523231,-0.0559028,0.05040665,0.07178448,0.03453158,0.03208163,0.01798104,0.04356204,0.03364026,-0.01653332,-0.00290563,0.01068364,-0.0129516,0.0220804,0.05891548,0.01797707,-0.2619721,0.06362635,0.0299333,0.01498771,-0.00839353,0.03234747,0.00051474,-0.05520792,0.01998802,-0.03758939,0.07000565,0.01865412,0.00896105,-0.04213897,0.02067997,0.01086763,0.05142399,-0.0132278,0.02559789,0.02557815,0.02665846,0.02137474,0.24355629,0.02548066,-0.01191925,0.03327134,0.01933666,-0.00285484,0.02050802,0.04276098,0.01295061,0.01480447,0.06410301,-0.01854507,-0.02070202,0.04413757,-0.02331351,0.02235744,-0.00853058,0.04183841,-0.10433168,0.03781147,-0.04736019,-0.03407746,0.0870325,0.05552433,-0.02206585,-0.06228553,0.01923682,0.02116414,-0.08276703,-0.06634792,-0.04328914,0.0102281,0.01872659,0.03457568,0.0175661,0.01543158,-0.00544133,-0.00294239,0.0453813,0.02778398,0.04253009,-0.0012145,-0.0020201],"last_embed":{"hash":"1ckcxjn","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"1ckcxjn","at":1753423490137},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#---frontmatter---","lines":[1,6],"size":258,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11198694,-0.04375451,-0.01791461,-0.03045188,-0.02971045,0.01602913,-0.02698352,-0.00489698,0.02344569,-0.01825873,0.00898811,-0.02444647,0.03818264,-0.03051063,-0.03239015,-0.0398795,0.02605319,0.01595826,-0.08624747,-0.00586295,0.06981457,-0.03617121,-0.04079805,-0.09288856,0.04822693,0.01011076,-0.03541585,-0.03795645,-0.04123025,-0.20933582,0.02064789,0.01903616,0.01965587,-0.05086986,-0.07841351,-0.01976028,-0.01632365,0.02770886,-0.08920565,0.01403514,0.00418451,0.02611684,-0.01811006,-0.0424047,0.01930197,-0.07474052,0.01696094,0.00165531,0.063436,0.01693044,-0.04279822,0.01405107,-0.00087674,0.03473275,0.06718784,0.01092579,0.05900025,0.08298954,0.01645314,0.035145,0.0358353,0.08114322,-0.18031658,0.0613368,0.0142583,0.02589482,-0.01687175,-0.05308329,0.00778339,-0.00022673,-0.00204627,0.03248171,-0.0105677,0.07121711,0.00552823,-0.04449993,-0.03846649,-0.04602323,-0.057779,0.01732707,-0.05262762,-0.02284358,-0.00047845,-0.00358206,0.02656356,0.06241182,0.04220362,0.07270657,0.08438583,-0.07452975,0.02808447,0.00630357,0.06091306,0.04050014,0.03973953,0.02590795,0.05902183,-0.04339984,0.02226947,0.11898436,-0.00784971,-0.04538205,-0.00781154,0.01476353,0.06523044,-0.03179481,-0.01927608,-0.02792103,-0.02978769,0.02561823,0.04873485,0.00381498,0.0444072,-0.05426638,-0.0537056,-0.02689941,-0.03244571,-0.01627938,0.00567098,0.02573415,-0.07190141,0.02353957,0.00872043,0.00020349,0.01822281,-0.00153856,0.04265613,0.04278968,0.01152361,0.03499309,0.04744519,0.03730477,-0.16736831,-0.02713462,-0.0042279,-0.00550596,-0.01477006,-0.03528339,-0.01295774,0.01200265,-0.00560022,-0.03474506,0.06515464,-0.11102524,0.01248522,0.07249241,-0.02665153,0.00427999,0.01530279,-0.01774859,0.00567453,0.01681965,-0.00444155,-0.07242829,0.04032576,0.02183044,0.12593777,0.05649467,-0.05065718,0.01375037,-0.00518494,-0.02150594,-0.02165239,0.11417577,-0.01772458,-0.11587368,-0.01002363,0.07908382,-0.04619061,-0.05370611,-0.00518539,0.02739793,-0.06325019,0.02862694,0.08047038,-0.04995293,-0.03742694,-0.05807467,-0.00708669,-0.00802317,0.0002963,-0.02319446,-0.04123251,-0.02242091,-0.01672414,-0.08204716,0.02945811,-0.04850158,0.02898875,0.08229319,-0.02444019,0.00661443,0.01335174,-0.0249632,-0.0205897,-0.03734076,-0.06907421,-0.04767445,0.05349834,-0.02610269,-0.02733197,0.0174184,0.0283716,0.02760741,-0.02193904,0.03834697,-0.05916307,-0.05684399,0.05733414,-0.00488158,-0.03418914,0.01498102,0.08625889,0.09302744,-0.08801118,0.02433049,0.01402215,0.02951468,0.00756976,0.07943612,-0.00453818,0.02248161,-0.08447041,-0.20015128,-0.03775184,-0.03285111,0.01887702,0.01706951,-0.03034112,0.04074213,-0.03577157,0.01345419,0.07861951,0.1076673,-0.08822452,0.00284445,0.03086873,-0.00566238,0.00090487,-0.0853212,-0.04784151,-0.05250967,0.05687597,0.00957248,-0.00157234,0.01671241,-0.07106861,0.03308978,-0.05371681,0.12953858,0.01089621,0.01297448,-0.02612522,0.07001914,0.00787469,-0.0108612,-0.02152768,0.01141733,0.03925614,-0.03365476,0.02650702,-0.01614954,0.02314847,-0.05848247,0.04442314,0.01862593,-0.07933959,-0.00574053,-0.01911449,-0.0087768,-0.00378639,-0.00596067,0.04975236,0.04826447,0.01177772,0.04378199,0.01173437,0.0626594,-0.00329994,-0.07666611,0.01415086,-0.00699474,0.02226029,0.01918746,-0.06962588,0.0811587,-0.03412102,0.07002917,0.036445,-0.00432617,-0.01963341,-0.00689518,-0.00015368,-0.01715319,0.06677952,0.00504165,0.07358252,-0.03429659,0.05381437,0.05530193,-0.0562792,0.01294588,-0.01679215,-0.02428202,-0.05046431,0.02484319,0.07531612,0.05611043,0.02802523,0.06144936,0.06197867,0.0136399,0.00928283,0.03195702,0.01109828,-0.0244455,0.0397324,0.01386038,0.07291842,-0.26136273,0.01399123,-0.02073608,0.02839694,-0.00311965,-0.00721727,0.01424083,-0.05765821,0.05181014,-0.04273468,0.06137674,0.01698232,0.030114,-0.09393287,0.01398587,-0.01146948,0.03834997,-0.00778303,0.05764687,0.00512315,0.03585508,0.03248965,0.22960638,-0.01577509,-0.00856359,0.04479802,0.02405356,-0.02059665,0.01566407,0.0180404,0.0173461,0.02337101,0.08831541,0.01184861,-0.04545454,0.0464776,-0.02847191,-0.00299828,-0.00541154,0.01249293,-0.0494253,0.02105117,-0.04530632,0.00117639,0.10059495,0.01188206,-0.02362338,-0.07969554,0.04357323,0.05898791,-0.10597539,-0.06769947,-0.07719526,-0.032755,0.02366211,0.01846045,0.01014503,0.00231281,0.00165223,-0.02423855,0.04843618,0.00839587,0.0722374,-0.00090802,-0.01370526],"last_embed":{"hash":"1i54zkf","tokens":511}}},"text":null,"length":0,"last_read":{"hash":"1i54zkf","at":1753423490171},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs","lines":[8,299],"size":29452,"outlinks":[{"title":"This is software for lottery, lotto tools, number generators, statistics, many functions.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"Overview of Lottery, Lotto Software Utilities","target":"https://saliu.com/lottery-utility.html#Utility","line":9},{"title":"Simulate Lotto, Lottery Data Files","target":"https://saliu.com/lottery-utility.html#Simulate","line":10},{"title":"Strip Duplicates and Wheel Lotto, Lottery Numbers","target":"https://saliu.com/lottery-utility.html#Wheel","line":11},{"title":"Count Lines in Lotto, Lottery Files, or Any Text Files","target":"https://saliu.com/lottery-utility.html#Count","line":12},{"title":"Check for Winning Combinations in Lotto, Lottery Output Files","target":"https://saliu.com/lottery-utility.html#Winners","line":13},{"title":"Statistical Reporting: Frequency, Skips, Pairing","target":"https://saliu.com/lottery-utility.html#Statistics","line":14},{"title":"Calculating Lotto Odds, Generating Random Numbers","target":"https://saliu.com/lottery-utility.html#Odds","line":15},{"title":"Sort, Add-Up Lottery, Lotto Data Files","target":"https://saliu.com/lottery-utility.html#Sort","line":16},{"title":"Make/Break/Position","target":"https://saliu.com/lottery-utility.html#combinations","line":17},{"title":"Resources in Lotto, Lottery, Software, Programs, Utilities","target":"https://saliu.com/lottery-utility.html#Lottery","line":21},{"title":"See the overview of lottery, lotto software utilities, tools.","target":"https://saliu.com/images/lotto.gif","line":23},{"title":"_**Lottery Software for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/powerball-mega-millions.html","line":40},{"title":"**ToolsLotto5**: _**Special upgrade to the lottery utility applications for 5-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/toolslotto5.htm","line":44},{"title":"**ToolsLotto6**: _**Special upgrade to the lottery utility software for 6-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/toolslotto6.htm","line":45},{"title":"**SoftwareLotto**: _**Special upgrades to the lottery utility applications for 5, 6, and 7-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/lotto-software.htm","line":46},{"title":"_**Special upgrades to the utility software for two-in-one (5+1) lotto games: Powerball, Mega Millions, Euromillions, SuperLotto**_","target":"https://saliu.com/gambling-lottery-lotto/power-mega-software.htm","line":47},{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":71},{"title":"For starters, you need simulate lotto, lottery data files to use the software.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":73},{"title":"The software strips duplicates and wheels the lotto, lottery numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":94},{"title":"The software count total lines in lotto, lottery files, or any text files.","target":"https://saliu.com/images/lotto.gif","line":116},{"title":"The software programs check for winners in lotto, lottery combinations.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":125},{"title":"The lottery software generates statistical reports for frequency, pairing, skips or misses.","target":"https://saliu.com/images/lotto.gif","line":154},{"title":"Odds and random numbers are generated by these lotto programs.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":179},{"title":"The lottery software programs sort or add-up lottery, lotto data files.","target":"https://saliu.com/gambling-lottery-lotto/lotto-book.gif","line":192},{"title":"Lottery software breaks strings of numbers in combinations; create lotto combinations by position.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":205},{"title":"_**Excel Spreadsheets in Lotto, Lottery**_","target":"https://saliu.com/Newsgroups.htm","line":229},{"title":"These are special resources in lotto software, lottery software, mathematics, statistics.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":264},{"title":"<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":266},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":270},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":272},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":274},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":276},{"title":"_**<u>Lottery Skip System Software</u>**_","target":"https://saliu.com/skip-strategy.html","line":277},{"title":"_**Lottery Filters, Filtering in Software**_","target":"https://saliu.com/filters.html","line":278},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":279},{"title":"_**Vertical (Positional) Filters in Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":280},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":281},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":282},{"title":"_**Lottery Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":284},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":285},{"title":"Run very useful lottery statistical tools, utilities, apps, lotto combination generators.","target":"https://saliu.com/HLINE.gif","line":287},{"title":"Forums","target":"https://forums.saliu.com/","line":289},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":289},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":289},{"title":"Contents","target":"https://saliu.com/content/index.html","line":289},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":289},{"title":"Home","target":"https://saliu.com/index.htm","line":289},{"title":"Search","target":"https://saliu.com/Search.htm","line":289},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":289},{"title":"Thanks for visiting the site of lottery, lotto, best software programs, utilities.","target":"https://saliu.com/HLINE.gif","line":291}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10059017,-0.03831415,-0.00836167,-0.05049203,-0.02989113,0.01964051,0.00242446,-0.00227485,0.01867183,-0.02664159,0.02638413,-0.01758726,0.02273466,-0.00912654,-0.03955086,-0.02978002,0.0388062,0.03032579,-0.07647818,-0.0136605,0.08628651,-0.0366985,-0.04045685,-0.08819973,0.06239574,-0.00887744,-0.04308155,-0.03907579,-0.04410568,-0.17291793,0.02366468,0.0337434,0.02834035,-0.0474659,-0.05692589,-0.02020442,-0.01198795,0.00414771,-0.09211113,0.02681381,0.01575755,0.03909026,-0.01800759,-0.06122278,0.02032153,-0.07003821,0.03521545,-0.00091059,0.05259675,0.01795623,-0.02575181,0.04316378,-0.00218433,0.04479131,0.0587217,-0.00057252,0.05599394,0.06751797,0.03192844,0.03951496,0.03312051,0.06505848,-0.16952641,0.05181364,0.00459107,0.02913107,0.00366928,-0.05280396,-0.00375184,0.00826456,0.02602981,0.03125115,-0.00404154,0.06217908,0.00262452,-0.04535117,-0.04460246,-0.03121354,-0.04343623,0.02536895,-0.04280903,-0.04496826,-0.00705018,-0.0181707,0.01622443,0.05550928,0.03401379,0.06839357,0.08802494,-0.06395146,0.01065428,0.00669881,0.05844266,0.04967039,0.03131393,0.02335279,0.05983524,-0.05652651,0.01833651,0.14755353,-0.01503358,-0.05274291,-0.02062795,0.01114916,0.05374487,-0.01983194,-0.02006322,-0.02534706,-0.03271551,0.02407239,0.03792921,-0.00541343,0.06922833,-0.03529008,-0.03721909,-0.0692028,-0.03005826,-0.03178793,0.01312584,0.03367392,-0.10072066,0.02450155,0.02378888,0.00067686,0.02972761,0.00014006,0.03841385,0.04518624,0.00604958,0.02471898,0.02677985,0.02707263,-0.1680817,-0.02536334,0.01427549,0.00029488,-0.00575717,-0.04816573,-0.00346861,0.03514168,-0.01264093,-0.00824771,0.0670148,-0.13589233,0.00679751,0.05781277,-0.01663161,0.00548095,0.00153055,-0.02350328,-0.00767492,0.02007572,-0.00094986,-0.07000922,0.02351936,0.03423212,0.12236119,0.04838828,-0.0497013,0.01643315,-0.00748621,-0.00318656,-0.02414001,0.11949646,-0.03964691,-0.12260669,-0.01710617,0.02925049,-0.03524719,-0.04832669,-0.02542514,0.04646002,-0.07849786,0.02960647,0.06822275,-0.04896922,-0.03095801,-0.0593627,-0.04217683,-0.03560588,0.02550221,-0.00759334,-0.03560403,-0.01223866,-0.02392545,-0.08539443,0.02617975,-0.04672115,0.04279308,0.08436278,-0.01694229,0.0295544,0.01951282,-0.03772876,-0.01057894,-0.03048168,-0.08697406,-0.04570513,0.06042116,-0.04700473,-0.04353978,0.02453498,0.03000886,0.04445182,-0.01529623,0.04359851,-0.07838114,-0.06210073,0.05585118,-0.00557825,-0.03401388,0.01258837,0.09489835,0.10139067,-0.10770722,0.03460336,0.01658891,0.02069309,-0.00791781,0.06682258,-0.00151855,0.04869578,-0.09530495,-0.21281141,-0.03558591,-0.03981577,0.01570273,0.0335419,-0.0274123,0.03943444,-0.0383768,-0.00889553,0.10139807,0.09820458,-0.10151176,0.01180174,0.02358729,-0.02127506,0.02367266,-0.09096207,-0.07253574,-0.04802782,0.04626084,0.01566919,0.00199944,0.00788577,-0.07350427,0.024243,-0.04427566,0.11720866,0.00568,0.01810464,-0.04384561,0.07040954,-0.01051083,-0.01037693,-0.02312743,-0.00531739,0.03790915,-0.0249335,0.0132793,-0.0179729,0.0335196,-0.05302081,0.04899369,0.00175771,-0.0723338,-0.00545056,-0.01915554,0.0089894,0.00159243,-0.00038441,0.0440817,0.03634072,0.00529424,0.04582299,0.01801664,0.0725213,0.01004925,-0.07878678,0.03374039,-0.01288128,0.04396178,0.03092764,-0.06311914,0.05434443,-0.04228764,0.06296983,0.03225837,-0.00160248,-0.02099355,0.00272277,-0.02388475,-0.02332435,0.07048762,-0.01032282,0.06314196,-0.02443493,0.05477324,0.07060098,-0.04693336,0.00401467,-0.02523572,-0.01020146,-0.03084805,0.00993728,0.07436168,0.04199483,0.04036826,0.04452101,0.05283698,0.00610334,0.00682609,0.03118575,0.00471532,-0.04880755,0.02657906,0.01965118,0.05334895,-0.23615873,0.03403536,-0.00856767,0.03845258,-0.01546431,-0.00455617,-0.00602077,-0.0458567,0.05906215,-0.02846579,0.09389056,0.01533084,0.02984863,-0.08189705,0.01556669,-0.01842378,0.05015425,-0.01871763,0.06315473,0.01887191,0.05300731,0.02873101,0.22036839,-0.01357558,0.00356885,0.02864921,0.0397137,-0.02055571,-0.00576508,0.00275117,0.03789249,0.01778381,0.08150905,-0.00056016,-0.02465895,0.04699269,-0.01731345,0.01188548,-0.00207677,0.0177616,-0.06428564,0.02132759,-0.06473919,-0.00970929,0.08919842,0.01462943,-0.02487701,-0.0889449,0.03757335,0.06695662,-0.09126562,-0.06116981,-0.06739328,-0.03360508,0.02488614,0.02302923,0.01854465,-0.01617367,-0.00833381,-0.01924505,0.04600907,0.04525579,0.04495265,-0.0145385,-0.01767532],"last_embed":{"hash":"c8sl41","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"c8sl41","at":1753423490357},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#{1}","lines":[10,15],"size":291,"outlinks":[{"title":"This is software for lottery, lotto tools, number generators, statistics, many functions.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs##I. [Overview of Lottery, Lotto Software Utilities](https://saliu.com/lottery-utility.html#Utility)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12365685,-0.05778892,-0.01542466,-0.00662047,-0.02686825,0.03748322,-0.00832524,-0.02378868,0.03353179,-0.00699287,-0.0040982,-0.02841752,0.04566654,-0.04359202,-0.01801871,-0.03768105,0.00991922,0.00689769,-0.08901022,0.00671302,0.05539725,-0.02978878,-0.04805996,-0.07065711,0.03939644,0.01917346,-0.02683953,-0.04889065,-0.03427856,-0.20880106,0.02041398,0.01413699,0.00598791,-0.04862857,-0.09587631,-0.03189579,-0.01487241,0.05942851,-0.06783973,0.03082959,0.0134447,0.00404988,-0.03169554,-0.01594888,0.0105428,-0.07147067,0.00094128,-0.00877436,0.08010396,0.00332814,-0.05540583,-0.00432514,0.00364177,0.02875571,0.06220854,0.01288182,0.06351656,0.08743679,0.01883998,0.00879318,0.0404065,0.06893805,-0.19101502,0.05854211,0.02196361,0.0147966,-0.01745957,-0.05396891,0.01024152,0.0004659,-0.01457912,0.02495197,-0.01120545,0.08211343,0.01553016,-0.04665387,-0.03090735,-0.06346697,-0.06651273,0.01996403,-0.06740778,-0.01003741,-0.00615132,0.00508254,0.01708125,0.06680488,0.05746082,0.07334061,0.06487061,-0.08436413,0.03488452,-0.00263712,0.04757106,0.04645648,0.02616368,0.0083201,0.04270105,-0.04088515,0.01934928,0.1126909,-0.00613013,-0.02663317,0.0140499,0.03955697,0.054502,-0.02601204,-0.02481361,-0.01821466,-0.0162953,0.04072342,0.0514278,0.01494157,0.02416659,-0.07239211,-0.0562098,-0.0044628,-0.03499414,0.02589147,-0.00883082,0.01906447,-0.04020683,0.003774,-0.00159712,0.00205835,-0.00522066,-0.01100004,0.03954987,0.05685347,0.02110652,0.03204845,0.04721419,0.01775686,-0.15671314,-0.03492901,-0.02677359,0.0061756,-0.00665437,-0.03304492,-0.02118752,0.00093325,-0.00465967,-0.05676829,0.05267107,-0.09105869,0.01259063,0.08232912,-0.01816306,0.02214435,0.04813505,-0.01098128,0.00091062,0.01315893,-0.02554012,-0.06987123,0.03045534,-0.00802592,0.11572465,0.05841592,-0.03282917,0.02668199,0.00229485,-0.04745306,-0.0211163,0.13253905,-0.01093355,-0.08456566,-0.01551246,0.09450982,-0.03810979,-0.06504336,0.0123745,0.0086605,-0.04826437,0.02133882,0.09929013,-0.035613,-0.03881063,-0.06206479,-0.00763631,0.00500858,-0.02830513,-0.03722803,-0.04517615,-0.01755359,-0.03054461,-0.07983831,0.0207082,-0.04488971,0.01010597,0.08500563,-0.0300305,-0.00870861,-0.01222562,-0.01184769,-0.01763801,-0.04151868,-0.05674813,-0.05910055,0.05300163,-0.03084707,-0.01178403,0.01405154,0.02930341,0.02398664,-0.03926467,0.03813256,-0.03276784,-0.03464229,0.05765241,-0.0081278,-0.04188025,0.02919158,0.07845573,0.06800952,-0.06027576,0.02888869,0.01851098,0.01418461,0.0378562,0.0701239,-0.01623519,-0.00288121,-0.05934052,-0.18705839,-0.03026516,-0.02332362,-0.00510406,0.00042892,-0.0326729,0.03685429,-0.01106796,0.0287378,0.0605753,0.12293022,-0.07465915,-0.00311247,0.04251774,0.00337187,-0.01708053,-0.06075633,-0.03850005,-0.04283847,0.07487408,-0.02186718,0.027904,0.00615528,-0.06023249,0.04189501,-0.05718741,0.13482124,0.01196216,0.00470155,-0.00916447,0.0664486,0.02481295,-0.02003299,-0.02975076,0.01477099,0.03520262,-0.07053152,0.04845995,-0.02334238,0.01054057,-0.07133025,0.03251082,0.0217,-0.08209443,0.02110389,-0.00984456,-0.01749202,-0.00041625,-0.01857162,0.03748293,0.04609089,0.00518966,0.03877153,0.02238384,0.07314092,-0.02124109,-0.06609874,-0.00408722,-0.00683426,-0.00172515,0.0186886,-0.06551583,0.09187838,-0.03097385,0.05477098,0.04377616,-0.01433078,-0.03772064,-0.01531099,0.02186144,0.00038925,0.0693046,0.02621381,0.05879275,-0.04784888,0.04634265,0.04145027,-0.05649573,0.03198499,0.00744587,-0.02149938,-0.07909746,0.03600929,0.07989208,0.06337819,0.03222772,0.05920778,0.05590015,0.02398089,0.00914485,0.02683609,0.02620071,-0.00224219,0.05016191,-0.00166982,0.06387603,-0.27873623,0.00045225,-0.03458963,0.03413471,0.01013683,-0.001514,0.03653004,-0.03981173,0.04563222,-0.04554427,0.06063684,0.01802765,0.02063573,-0.09767491,0.00290855,-0.00888319,0.02802566,-0.00246832,0.05710002,-0.0022269,0.02665545,0.03111974,0.25073183,-0.0131071,-0.02068092,0.02702424,0.00651917,-0.01764953,0.00590675,0.05325205,0.00444751,0.00628815,0.07660341,0.01662404,-0.03337413,0.05063465,-0.03174306,0.01164326,-0.00491489,0.02027347,-0.05406962,0.00869474,-0.07185443,0.01792927,0.10180312,0.02809323,-0.02534082,-0.06890584,0.04599015,0.04803556,-0.0897411,-0.08384835,-0.0696134,-0.02874517,0.0185985,-0.00425725,0.00321351,0.0203353,0.02295082,-0.01126801,0.04669746,-0.01645515,0.07647403,-0.00972548,-0.00766405],"last_embed":{"hash":"52lbul","tokens":438}}},"text":null,"length":0,"last_read":{"hash":"52lbul","at":1753423490391},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs##I. [Overview of Lottery, Lotto Software Utilities](https://saliu.com/lottery-utility.html#Utility)","lines":[16,31],"size":1302,"outlinks":[{"title":"Overview of Lottery, Lotto Software Utilities","target":"https://saliu.com/lottery-utility.html#Utility","line":1},{"title":"Simulate Lotto, Lottery Data Files","target":"https://saliu.com/lottery-utility.html#Simulate","line":2},{"title":"Strip Duplicates and Wheel Lotto, Lottery Numbers","target":"https://saliu.com/lottery-utility.html#Wheel","line":3},{"title":"Count Lines in Lotto, Lottery Files, or Any Text Files","target":"https://saliu.com/lottery-utility.html#Count","line":4},{"title":"Check for Winning Combinations in Lotto, Lottery Output Files","target":"https://saliu.com/lottery-utility.html#Winners","line":5},{"title":"Statistical Reporting: Frequency, Skips, Pairing","target":"https://saliu.com/lottery-utility.html#Statistics","line":6},{"title":"Calculating Lotto Odds, Generating Random Numbers","target":"https://saliu.com/lottery-utility.html#Odds","line":7},{"title":"Sort, Add-Up Lottery, Lotto Data Files","target":"https://saliu.com/lottery-utility.html#Sort","line":8},{"title":"Make/Break/Position","target":"https://saliu.com/lottery-utility.html#combinations","line":9},{"title":"Resources in Lotto, Lottery, Software, Programs, Utilities","target":"https://saliu.com/lottery-utility.html#Lottery","line":13},{"title":"See the overview of lottery, lotto software utilities, tools.","target":"https://saliu.com/images/lotto.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs##I. [Overview of Lottery, Lotto Software Utilities](https://saliu.com/lottery-utility.html#Utility)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11760557,-0.05647958,-0.01453143,-0.00798689,-0.02197324,0.04311413,-0.00777375,-0.02659534,0.03243923,-0.00388004,-0.00469433,-0.03168646,0.04396825,-0.04435173,-0.01942921,-0.03302945,0.00597874,0.00402858,-0.09035078,0.00696674,0.05915748,-0.0303486,-0.05006257,-0.07120649,0.04015438,0.02342431,-0.0270624,-0.04250713,-0.0370712,-0.20985632,0.01744499,0.0137286,0.00754777,-0.04905127,-0.09275777,-0.03787395,-0.02217983,0.06668817,-0.06746054,0.03100891,0.01107554,-0.00340095,-0.03295465,-0.01243558,0.01128767,-0.07003332,-0.00128122,-0.00854153,0.08194479,0.01038432,-0.04897927,-0.00688612,0.00878101,0.0230263,0.06659333,0.00832641,0.06470853,0.08601882,0.01995331,0.0096889,0.03905423,0.07313325,-0.1886209,0.05990261,0.02128412,0.01442849,-0.01931222,-0.05711935,0.00349068,0.00848216,-0.0121349,0.02140016,-0.01264645,0.08576068,0.01824189,-0.04642614,-0.03171717,-0.06592578,-0.06820843,0.02164557,-0.07538897,-0.00849272,-0.00839409,0.01262024,0.01530337,0.06630477,0.05491727,0.07687625,0.06584626,-0.08068919,0.03445436,0.0054323,0.04708211,0.04165588,0.0216686,0.00705316,0.03750226,-0.03540768,0.02336872,0.11040146,-0.00268951,-0.02689266,0.01502175,0.04064912,0.05016444,-0.0277247,-0.03332661,-0.01868504,-0.01743032,0.0347048,0.04753958,0.01866067,0.02736101,-0.07381546,-0.0602391,0.00298536,-0.02794721,0.02873919,-0.01772675,0.01669434,-0.04252842,0.00397306,-0.00381299,-0.00042473,-0.00383928,-0.01123788,0.0412427,0.0589198,0.02158384,0.03224911,0.04624861,0.0189634,-0.15895303,-0.03403793,-0.0305516,0.00290548,-0.00831935,-0.03039436,-0.02148019,-0.00464026,-0.00265349,-0.05405926,0.0511079,-0.08703411,0.01812959,0.07955144,-0.02111058,0.02217308,0.05097292,-0.01286353,0.00100637,0.01429815,-0.02316795,-0.06579152,0.03735436,-0.00916922,0.1203297,0.05649453,-0.02973835,0.02539272,-0.00175884,-0.05037159,-0.01823793,0.13446611,-0.01390991,-0.07748171,-0.01282064,0.09679227,-0.03027274,-0.06653188,0.01977996,0.00448104,-0.05036765,0.02534692,0.09709808,-0.03905237,-0.04313545,-0.06350241,-0.00576126,0.00859418,-0.02849068,-0.03913031,-0.04621941,-0.0142936,-0.03580995,-0.07687072,0.0190635,-0.04187363,0.01319479,0.08547329,-0.03277176,-0.00553357,-0.01137122,-0.00844584,-0.01849369,-0.04393236,-0.05622496,-0.0594432,0.05183276,-0.03440272,-0.00587732,0.01074848,0.02847927,0.02432706,-0.03858443,0.03616804,-0.02697555,-0.03267074,0.06290597,-0.00857757,-0.04240257,0.02334172,0.07677481,0.06357036,-0.06208953,0.03575132,0.01758199,0.01584147,0.03893751,0.07256205,-0.01389691,-0.00651025,-0.05937268,-0.18684644,-0.03404013,-0.01926545,0.00347049,0.00254907,-0.03357262,0.03424506,-0.01131978,0.03046754,0.06576873,0.11833061,-0.06696874,-0.0050807,0.04279729,0.00571195,-0.01808302,-0.06879441,-0.04029252,-0.04675486,0.07430224,-0.0245872,0.02960591,0.00458063,-0.06022539,0.04694885,-0.05956779,0.13662922,0.01249578,0.00564406,-0.01060986,0.06720242,0.03109128,-0.02169066,-0.02112745,0.01891656,0.03315341,-0.07141509,0.05000699,-0.02539979,0.00298423,-0.06711166,0.03190131,0.02048614,-0.08397111,0.02334984,-0.0049783,-0.01986317,0.00129637,-0.0218696,0.03592312,0.04569908,0.00419144,0.0411939,0.02036541,0.07029229,-0.02334276,-0.06568008,-0.00718203,-0.00884826,-0.00057342,0.01658588,-0.06331129,0.09200191,-0.0295115,0.05654497,0.04200688,-0.01429059,-0.04064683,-0.01239618,0.02372132,0.00307763,0.06690693,0.02756956,0.05975641,-0.0438313,0.04430532,0.03829234,-0.05390873,0.03401868,0.00357255,-0.01669995,-0.07511751,0.03643313,0.07844471,0.06053351,0.03208314,0.05157506,0.05459277,0.02857559,0.01063028,0.022402,0.02530896,-0.00483524,0.04908522,-0.00408231,0.06350964,-0.27745193,-0.00425853,-0.04194482,0.03431722,0.0094583,-0.00304405,0.03624873,-0.03685863,0.04520671,-0.04658274,0.05507531,0.0180499,0.02493333,-0.0980612,0.00179242,-0.00946782,0.02102504,-0.00422024,0.05641889,-0.00372745,0.02709285,0.02152969,0.25372767,-0.01231878,-0.01473174,0.02664116,0.00255149,-0.01738369,0.01054349,0.0532214,-0.00347142,0.00408596,0.07344198,0.01498453,-0.03674185,0.05519018,-0.0371238,0.01508726,-0.00223938,0.01407878,-0.05388604,0.00669255,-0.06850429,0.01590086,0.0986452,0.02398802,-0.02149083,-0.06362431,0.04250756,0.05368539,-0.09241656,-0.0813896,-0.07152142,-0.0360458,0.02116248,-0.00096717,0.00213353,0.02280279,0.02572949,-0.01258692,0.04373364,-0.013742,0.08456304,-0.01008276,-0.01449391],"last_embed":{"hash":"1ipi8r8","tokens":408}}},"text":null,"length":0,"last_read":{"hash":"1ipi8r8","at":1753423490528},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs##I. [Overview of Lottery, Lotto Software Utilities](https://saliu.com/lottery-utility.html#Utility)#{1}","lines":[17,31],"size":1197,"outlinks":[{"title":"Simulate Lotto, Lottery Data Files","target":"https://saliu.com/lottery-utility.html#Simulate","line":1},{"title":"Strip Duplicates and Wheel Lotto, Lottery Numbers","target":"https://saliu.com/lottery-utility.html#Wheel","line":2},{"title":"Count Lines in Lotto, Lottery Files, or Any Text Files","target":"https://saliu.com/lottery-utility.html#Count","line":3},{"title":"Check for Winning Combinations in Lotto, Lottery Output Files","target":"https://saliu.com/lottery-utility.html#Winners","line":4},{"title":"Statistical Reporting: Frequency, Skips, Pairing","target":"https://saliu.com/lottery-utility.html#Statistics","line":5},{"title":"Calculating Lotto Odds, Generating Random Numbers","target":"https://saliu.com/lottery-utility.html#Odds","line":6},{"title":"Sort, Add-Up Lottery, Lotto Data Files","target":"https://saliu.com/lottery-utility.html#Sort","line":7},{"title":"Make/Break/Position","target":"https://saliu.com/lottery-utility.html#combinations","line":8},{"title":"Resources in Lotto, Lottery, Software, Programs, Utilities","target":"https://saliu.com/lottery-utility.html#Lottery","line":12},{"title":"See the overview of lottery, lotto software utilities, tools.","target":"https://saliu.com/images/lotto.gif","line":14}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12425746,-0.04680703,-0.00192832,-0.04942523,-0.05895192,0.02445106,-0.02732277,-0.02896766,0.02468508,-0.00092053,0.03372782,-0.00847721,0.03448059,-0.04351294,-0.03984043,-0.04293726,0.03300626,-0.00925418,-0.08161306,-0.01187647,0.08244533,-0.04045749,-0.03761112,-0.06551489,0.04929385,0.01841908,-0.03857855,-0.03786319,-0.07437623,-0.21572849,0.04669555,0.03238875,0.03194701,-0.03969773,-0.07414365,-0.0346753,-0.02560109,0.00392469,-0.10717596,0.00948604,0.014644,0.03639631,-0.00885563,-0.0586592,0.02552618,-0.09542024,-0.00370524,0.00192639,0.09128591,0.00129769,-0.04443009,0.0254043,0.01261498,0.05199403,0.0690902,-0.0170155,0.07846753,0.07545747,0.02189527,0.03192354,0.04968363,0.07372784,-0.19353709,0.07507821,0.00042716,0.02400531,-0.02788395,-0.06419085,0.01146161,-0.01080105,-0.00625734,0.03618602,-0.00614405,0.05673474,0.00923643,-0.04024625,-0.02494751,-0.05184421,-0.05489685,0.01812162,-0.04951866,-0.0309069,-0.01359824,-0.02800631,0.01851795,0.05620034,0.01886074,0.06694583,0.07988838,-0.05776427,0.02960314,-0.01222469,0.04405902,0.06847519,0.02602775,0.0056145,0.05888631,-0.0185106,-0.03908855,0.12028372,-0.00541329,-0.05161518,-0.03614252,0.02164792,0.08751547,-0.02170209,-0.01712926,-0.03819376,-0.04131479,0.00246358,0.02395833,0.01136093,0.03932676,-0.02766109,-0.0417912,0.00030801,-0.05309593,-0.01244061,-0.0068485,0.03663507,-0.05581801,0.02398891,-0.00956404,0.00279263,-0.01038752,0.00064557,0.04768616,0.02694069,-0.00385808,0.03394559,0.0364889,0.02624473,-0.13558614,-0.04075086,0.02106653,0.02818101,-0.01105527,-0.02494998,-0.02014347,0.01477159,-0.01803687,-0.01914898,0.05278955,-0.11762482,0.01889704,0.06563773,-0.01725177,0.00916362,0.02947826,-0.02505699,-0.00522169,0.02049658,-0.03992387,-0.05534697,0.02734056,0.00981122,0.09695717,0.04360456,-0.07867205,-0.01452259,-0.01063476,-0.02288558,-0.0268685,0.1158113,-0.0120394,-0.10279229,-0.0025368,0.02687,-0.03769969,-0.05375781,-0.01382358,0.05286792,-0.05243336,0.0153023,0.06677712,-0.05027759,-0.03515723,-0.08252972,-0.00496579,-0.03514931,0.00447466,0.01198626,-0.00568521,-0.01294307,-0.02691241,-0.09579928,0.02186112,-0.05242465,0.03106266,0.0714098,-0.04014851,0.01374154,-0.00787168,-0.03076874,0.00195923,-0.02176888,-0.0820436,-0.05015115,0.06868932,-0.04100964,0.02962563,0.0481967,0.05427417,0.03506358,-0.01085234,0.05473453,-0.06055339,-0.04342623,0.04050076,-0.00455229,-0.02394486,0.00592652,0.0825653,0.08815559,-0.07288509,0.03941206,0.0195182,-0.0095378,0.03455144,0.05078718,-0.00352772,0.06605409,-0.04532618,-0.18554732,-0.01134556,-0.03510321,-0.01943928,0.01212945,-0.03297566,0.0368206,-0.04967308,0.02932649,0.07076429,0.11964797,-0.06987616,-0.00684851,0.06235844,0.00524149,0.02488025,-0.06068595,-0.03448109,-0.05693886,0.06417742,-0.00235859,0.03453878,0.00178331,-0.03235415,0.0547774,-0.0418733,0.11553103,0.02701,-0.01791282,-0.05032835,0.08020885,0.00340406,0.01571096,-0.03892254,0.00335374,0.03968352,-0.02729299,0.02871371,-0.00419568,0.02088083,-0.0731179,0.02016956,0.00079916,-0.10548093,0.04165799,0.00956599,-0.01850926,0.01592697,0.03677086,0.04664955,0.05964452,0.01961888,0.03560988,0.02912594,0.10420401,-0.02270878,-0.07912879,0.03386569,0.0207124,0.02444681,0.01434236,-0.08246697,0.04899328,-0.03742892,0.06723344,0.03644659,-0.00826631,-0.01094105,0.00422919,-0.01869143,-0.00540784,0.05797056,0.00163992,0.05633445,-0.08645736,0.04383416,0.06427684,-0.04180639,0.0364159,-0.02260718,-0.00696498,-0.01531106,0.03719796,0.07295923,0.05404189,0.01493815,0.06657266,0.04466256,0.0076041,-0.01046111,0.00819758,0.02042841,-0.04103589,0.04901098,0.00999959,0.0335154,-0.26352832,0.03266768,-0.00192507,0.00015836,-0.00424877,0.00621711,0.00924725,-0.01865526,0.03912824,-0.05210875,0.05346928,0.01556047,0.0221168,-0.08329073,0.03445791,-0.01681266,0.02883294,0.00425354,0.06787559,0.01690167,0.05915694,0.03776145,0.21204671,-0.03161854,-0.00439581,0.03203753,0.01453835,-0.01078821,0.01870715,0.03440832,-0.00181658,0.03203671,0.08793682,-0.02185898,-0.04580538,0.02390253,-0.03367237,-0.01515231,0.00402978,0.02176049,-0.04934322,0.03429868,-0.06730376,-0.04598709,0.07758042,0.02753697,-0.02805793,-0.07049008,0.05890944,0.08204719,-0.06273577,-0.06001777,-0.07395165,-0.03130846,0.05962623,0.02463259,0.00144545,-0.00598235,-0.00716347,-0.00691166,0.01987718,0.00285549,0.05967233,-0.00369701,-0.00254638],"last_embed":{"hash":"qjxlpt","tokens":447}}},"text":null,"length":0,"last_read":{"hash":"qjxlpt","at":1753423490644},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>","lines":[32,81],"size":3773,"outlinks":[{"title":"_**Lottery Software for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/powerball-mega-millions.html","line":16},{"title":"**ToolsLotto5**: _**Special upgrade to the lottery utility applications for 5-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/toolslotto5.htm","line":20},{"title":"**ToolsLotto6**: _**Special upgrade to the lottery utility software for 6-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/toolslotto6.htm","line":21},{"title":"**SoftwareLotto**: _**Special upgrades to the lottery utility applications for 5, 6, and 7-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/lotto-software.htm","line":22},{"title":"_**Special upgrades to the utility software for two-in-one (5+1) lotto games: Powerball, Mega Millions, Euromillions, SuperLotto**_","target":"https://saliu.com/gambling-lottery-lotto/power-mega-software.htm","line":23},{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":47},{"title":"For starters, you need simulate lotto, lottery data files to use the software.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":49}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11236793,-0.04513919,0.01743929,-0.05438285,-0.03302136,0.02352571,-0.02625938,-0.02573194,0.03248806,-0.01619802,0.03886176,-0.00831589,0.03492659,-0.01054526,-0.05190286,-0.03605318,0.04665155,-0.01635694,-0.05862803,0.00280351,0.10232233,-0.05691263,-0.03358561,-0.06689342,0.05163115,0.01382412,-0.010782,-0.02894783,-0.06768575,-0.22122721,0.03659677,0.03397552,0.03079983,-0.05454909,-0.05434113,-0.01982655,0.0029307,0.00570864,-0.11285882,0.0224672,0.03275233,0.01653943,0.00029386,-0.05780306,0.02839684,-0.06460939,-0.00038773,-0.0298506,0.09389404,0.00161867,-0.03115227,0.04373216,0.01312551,0.05758187,0.06406267,-0.00661993,0.09848734,0.07438693,0.02985292,0.04586571,0.05412189,0.06846236,-0.20976591,0.07814987,0.01787716,0.02038025,0.01417014,-0.05384527,-0.00857435,-0.02431676,-0.02090695,0.01606877,0.00461145,0.06212398,0.00291137,-0.04278597,-0.02597497,-0.03319141,-0.03834132,0.04670189,-0.05226631,-0.04095441,-0.02705522,-0.01897067,0.00254787,0.0403001,0.03103038,0.06135757,0.06984431,-0.07803179,0.03985566,-0.03477452,0.07046266,0.08791868,0.01899504,0.03374343,0.05167627,0.00474684,-0.04145495,0.12402274,-0.02668055,-0.0470358,-0.04918068,0.02713517,0.06579698,-0.0318867,-0.01423497,-0.03622171,-0.02169173,0.030757,0.02349318,0.00132342,0.04001459,-0.01889393,-0.03228523,0.00777335,-0.03985133,0.00109924,-0.01229058,0.03668623,-0.04579992,0.01165771,-0.00593689,0.02768761,-0.01742485,-0.01972363,0.03550614,0.02367191,-0.00941629,0.02082953,0.00657214,0.01045011,-0.14527416,-0.0428809,0.03190671,0.03660474,0.01281295,-0.04122394,-0.02299376,-0.0059324,-0.01864561,-0.04663607,0.02916567,-0.10507912,0.03928094,0.0794289,-0.01884199,-0.01585956,0.04721615,-0.016354,-0.03053071,0.0205932,-0.04286824,-0.04198153,0.03084352,0.00017617,0.09301554,0.02359172,-0.06049802,-0.00417768,-0.0135181,-0.028401,-0.03426019,0.13468729,-0.03790136,-0.08182642,-0.05597309,0.01995122,-0.02562866,-0.03673742,-0.03601029,0.03505339,-0.04342655,0.02685064,0.07818022,-0.05604583,-0.00811118,-0.06329104,-0.05191137,-0.02614689,-0.00461242,0.00814813,0.02160929,-0.01591682,-0.04392065,-0.10296574,0.02155745,-0.06312535,0.04974747,0.08204934,-0.06343751,-0.0010529,0.00410964,-0.03596073,-0.00248993,-0.00760249,-0.08115112,-0.05150408,0.05800333,-0.05423119,-0.00591716,0.04073373,0.04219092,0.0448302,-0.0348925,0.06766036,-0.06389701,-0.05353126,0.05015744,-0.00857012,-0.03913232,0.02038337,0.07889189,0.08064106,-0.06397226,0.02360384,0.02425948,-0.01808922,0.02776672,0.05842218,-0.00756031,0.06233593,-0.05761322,-0.19070563,-0.01003696,-0.0474298,-0.00877351,-0.00226015,-0.05199446,0.03343121,-0.04770406,0.01033462,0.08300861,0.07928486,-0.05006835,0.01315391,0.05041377,-0.00104306,0.00818531,-0.04891519,-0.0456469,-0.06236153,0.05309469,-0.01298545,0.0211258,-0.01399314,-0.04452063,0.05821041,-0.04636395,0.12661818,0.02546245,0.01169246,-0.03629579,0.06904715,0.01904825,0.00592499,-0.0547546,-0.00991776,0.04258101,-0.03793212,0.01971823,-0.01723167,0.01372933,-0.05492813,-0.0059768,-0.01232036,-0.09165066,0.04372202,0.01443569,-0.03901621,0.02271074,0.02109923,0.06399176,0.0624208,-0.01772468,0.06149095,0.05844878,0.10072156,-0.02526118,-0.08494917,0.03703565,0.02434793,0.01340341,0.00727981,-0.06124502,0.04985638,-0.06736011,0.0676195,0.03874467,-0.03056775,-0.00400669,0.01374064,-0.00581785,-0.03166004,0.06844188,-0.00831297,0.03127674,-0.06835976,0.04334031,0.04556705,-0.03009816,0.03158775,-0.01994634,-0.01291155,0.00059334,0.05908654,0.0773878,0.05280749,0.04590204,0.04957824,0.03270066,0.0091894,-0.01075874,0.02822311,0.02928904,-0.05504035,0.05069762,0.03480068,0.01896055,-0.25079331,0.04064284,0.01525643,0.01243323,0.00230896,0.01989671,-0.00188533,-0.0186338,0.04980419,-0.02495703,0.04477093,0.00162824,0.03010559,-0.07207,0.01937096,-0.02627402,0.01791056,0.00821406,0.06068374,0.02762698,0.04790522,0.01856216,0.21239115,0.00026027,-0.00497564,0.02365652,0.03211917,-0.01655559,0.00006564,0.04684433,0.01727818,0.0169453,0.08746123,0.00288662,-0.00787551,0.00681911,-0.05511823,-0.00484661,0.0088558,0.00652254,-0.05992462,0.03634258,-0.04569926,-0.03633163,0.07711219,0.02713201,-0.03472233,-0.07028725,0.04799316,0.06933944,-0.06848288,-0.0637616,-0.07037842,-0.03867298,0.02568147,0.03067992,0.03214978,-0.0087377,0.00747273,-0.0247472,0.04495305,0.03880678,0.04840339,-0.02781486,-0.01409661],"last_embed":{"hash":"1e1h6cc","tokens":205}}},"text":null,"length":0,"last_read":{"hash":"1e1h6cc","at":1753423490788},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{10}","lines":[45,50],"size":625,"outlinks":[{"title":"_**Lottery Software for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/powerball-mega-millions.html","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12050649,-0.04706575,0.01673589,-0.05271821,-0.06266178,0.02452644,-0.01738281,-0.02227375,0.02861186,-0.01064796,0.01162594,-0.01656576,0.03785269,-0.03617205,-0.02293689,-0.04599004,0.02606202,-0.00444104,-0.08114073,-0.00783837,0.08944286,-0.02185874,-0.02931073,-0.05952927,0.04256018,0.00068906,-0.03392922,-0.04002956,-0.0249809,-0.19496679,0.06277015,0.0339633,0.00073455,-0.03716921,-0.08117815,-0.03820467,-0.00029118,0.00401358,-0.09681063,0.02190449,0.01845924,0.03378288,-0.02868054,-0.06596699,-0.00101522,-0.0921743,0.03193404,0.00029875,0.07396995,-0.01598146,0.00778578,0.04152184,0.00328419,0.04079118,0.05855938,-0.02953155,0.06368237,0.077592,0.02588863,0.04098546,0.0356162,0.06201006,-0.20342377,0.04706002,0.02310464,0.0312545,0.00065116,-0.03931623,0.02340465,0.00248405,0.02959879,0.00599035,0.00993189,0.04402616,0.00761138,-0.04363222,-0.03599652,-0.04554944,-0.05622486,0.04299415,-0.03283043,-0.04836476,-0.01006481,-0.05221403,0.00657326,0.04780106,0.02620306,0.0680408,0.07491662,-0.07342318,0.00868948,-0.02284614,0.06823851,0.07508782,0.02944358,0.01928705,0.0518702,-0.04567694,-0.02326697,0.14001895,0.00375482,-0.04418054,-0.01517238,0.01456578,0.08455215,0.0001535,0.00960897,-0.03104403,-0.02385724,0.00997306,0.02022659,0.0234758,0.04185497,-0.02691687,-0.03466141,-0.03317008,-0.06468602,0.00240499,0.0145885,0.04193864,-0.0549802,0.00280452,0.02534012,-0.00255873,0.00337193,-0.02367921,0.04413909,0.03736297,-0.00517165,0.02668627,0.02983374,0.00258501,-0.11897063,-0.035005,0.03195662,0.01412756,-0.00858549,-0.01796657,-0.01320397,0.05919549,-0.00863791,-0.01724624,0.05244773,-0.1478892,0.01469202,0.07386344,0.01630631,-0.00281149,0.00658435,-0.01771622,-0.00839246,0.02428635,-0.00641738,-0.05362148,0.02546308,0.02291374,0.11186054,0.04011086,-0.0460299,-0.01004561,0.00200819,-0.02035847,-0.04554452,0.11850838,0.00444666,-0.12649104,-0.00905425,0.01785138,-0.04563243,-0.06727996,-0.02336618,0.05576443,-0.06346369,0.01186456,0.08365808,-0.04558235,-0.02951089,-0.07091217,-0.0382033,-0.03737665,0.00519483,-0.00798053,-0.01845424,-0.03429234,-0.03030779,-0.10004304,0.0072791,-0.06828216,0.01426284,0.07261233,-0.04063521,0.00459497,-0.02041544,-0.02281291,0.01180595,-0.02244965,-0.09039106,-0.03217868,0.05837807,-0.05634757,0.00687274,0.05272958,0.0311323,0.03678915,-0.01180793,0.04815179,-0.0616546,-0.03649421,0.03059244,-0.01865313,-0.04849448,0.02010242,0.08675162,0.08467256,-0.05531426,0.04327685,0.01114981,0.0001491,0.01977465,0.06436595,-0.00479063,0.08078162,-0.0580228,-0.19270264,-0.01045078,-0.04614618,-0.02875734,-0.0096548,-0.01638519,0.04413161,-0.03450284,0.00223433,0.06317978,0.10086089,-0.09792894,0.01123946,0.05732745,-0.0204737,0.03177702,-0.04423812,-0.04889314,-0.02185699,0.04195315,-0.0023644,0.05633685,0.01690088,-0.06096498,0.03794039,-0.04736574,0.13528049,0.00324049,-0.0142785,-0.03930837,0.05741661,-0.01237988,0.00068768,-0.04846897,-0.008419,0.04173826,-0.03391608,0.00872348,-0.03215165,0.01623631,-0.07638966,0.02893767,-0.00517009,-0.08948254,0.02323523,0.00893613,-0.00018149,-0.01108256,0.02105167,0.05057417,0.04541902,-0.00458117,0.03231416,0.03142355,0.10899936,-0.02433901,-0.08724093,0.04551766,0.02058718,0.02102755,0.03185849,-0.07219043,0.05351293,-0.05862976,0.08908156,0.01665656,-0.02129989,-0.02523106,0.00219148,-0.01958893,-0.01359661,0.06011865,0.01616696,0.04821497,-0.05906106,0.05685572,0.07645532,-0.02796117,0.00973823,-0.00441885,-0.04145196,-0.03769461,0.03648144,0.07846375,0.04594013,0.02336868,0.04666771,0.03976952,0.00102227,-0.00031907,-0.01054742,0.0096452,-0.0162367,0.03500333,-0.00854513,0.04875782,-0.26138049,0.02734055,0.02052333,0.0059876,-0.0173024,0.03023024,0.00016547,-0.02017045,0.03235451,-0.04109659,0.08277639,0.02545962,0.04569791,-0.04294568,0.00291438,-0.0279636,0.06762066,-0.009576,0.05858632,0.00223482,0.06679614,0.05860579,0.21845183,-0.00198632,-0.01489506,0.00861512,-0.00016474,-0.01612008,-0.02917965,0.04271087,0.03129593,0.03425658,0.07239709,-0.01327498,-0.00392884,0.04057848,-0.02935421,0.01053843,0.00179961,0.04237915,-0.06747538,0.02914113,-0.08201008,-0.00596214,0.10310241,0.05382923,-0.02321326,-0.06670187,0.01827581,0.03604108,-0.08297362,-0.05759533,-0.0481937,-0.02098302,0.04025064,0.00796473,-0.00198523,-0.00643604,-0.00238143,-0.01135958,0.0413361,0.04391878,0.04530164,-0.02339579,-0.00298085],"last_embed":{"hash":"pwi1p6","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"pwi1p6","at":1753423490848},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{14}","lines":[54,55],"size":205,"outlinks":[{"title":"_**Special upgrades to the utility software for two-in-one (5+1) lotto games: Powerball, Mega Millions, Euromillions, SuperLotto**_","target":"https://saliu.com/gambling-lottery-lotto/power-mega-software.htm","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09908919,-0.04970092,-0.02357597,-0.03595228,-0.05467908,-0.00132639,0.03330729,0.00719926,0.02546083,-0.02172658,0.01228068,-0.00414059,0.03423858,-0.00534592,-0.01529348,-0.04093322,0.06456482,-0.00899824,-0.07067279,0.00359901,0.08025596,-0.04580736,-0.04544035,-0.09310539,0.03876915,0.02283111,-0.0333038,-0.06099126,-0.04024356,-0.16941394,0.02488591,0.02283182,0.01975761,-0.03850987,-0.06145445,-0.02836968,0.00021891,-0.02462116,-0.08900029,0.03596051,-0.00755735,0.03637093,-0.02688492,-0.06264029,-0.00380158,-0.07474471,0.00276426,-0.00275561,0.05996163,0.03303755,-0.00307145,0.02743187,0.00388398,0.05040714,0.03030271,-0.00195389,0.02079707,0.05980692,0.02395415,0.0167674,-0.00082305,0.01526843,-0.20628713,0.0398535,-0.01204529,0.03760843,0.0074522,-0.02254629,0.01831534,-0.03272165,-0.01416882,0.03835703,-0.02182979,0.01777138,0.03340862,-0.08509865,-0.03125005,-0.00496652,-0.0652944,0.04299523,-0.04001147,-0.02708687,-0.00987362,0.01120284,-0.00847094,0.03267838,0.08805651,0.06041353,0.04076257,-0.09419739,0.01161281,0.00023099,0.05312525,0.0404621,0.04482772,0.03272875,0.06681892,-0.0337393,-0.05764811,0.13370559,0.00217648,-0.01392419,-0.00997578,-0.009224,0.06470548,-0.00475372,-0.00872484,-0.01445014,-0.0337094,0.00550773,0.01895253,0.01614388,0.05247962,-0.07367402,-0.02874955,-0.02703113,-0.06298616,0.0008229,0.02764643,0.01704434,-0.04951453,-0.00195693,-0.00314233,-0.0184108,-0.01120435,-0.02004647,0.00798057,0.06230617,0.03732587,0.06074999,0.0813042,-0.02934623,-0.13156797,-0.01798137,0.03262731,-0.00528463,-0.00104388,-0.04855828,-0.04664839,0.08152493,-0.0262803,-0.00009923,0.04884042,-0.10884416,-0.03915678,0.06902198,0.01762689,0.00860286,0.05569221,0.01063239,-0.0066947,0.00166867,-0.03681782,-0.06963696,0.02461354,-0.02041867,0.12022853,0.03010472,-0.05999805,0.01508761,0.01632112,-0.04181735,-0.0109915,0.15011711,0.01787162,-0.08894168,-0.03081521,0.02553476,-0.04230433,-0.05535896,-0.02261254,0.04025261,-0.04900689,0.03573306,0.10358338,-0.04229441,-0.07518975,-0.04732626,-0.03784261,-0.03703088,0.01204207,-0.01103829,-0.02502,-0.02756177,-0.02551197,-0.10429715,0.0100058,-0.06086372,0.03374932,0.05107903,-0.01612115,0.01448725,-0.00752654,-0.00972087,-0.01110931,-0.02974541,-0.0637291,-0.04446676,0.03502795,-0.01196177,-0.00110014,0.02230046,0.02753389,0.04665752,-0.01374639,0.06047776,-0.04099451,0.00140305,0.03888475,0.01523616,-0.04165543,0.04566248,0.06999128,0.05793592,-0.07161178,0.03288588,0.02094155,0.01414161,0.03653234,0.06696017,-0.04572319,0.05999372,-0.05184432,-0.17859574,0.0286822,-0.03640078,-0.02863096,0.01586571,-0.04365255,0.02832683,-0.01856868,0.01362645,0.07158414,0.15591872,-0.09822804,0.01610854,0.04661029,-0.00151064,0.0331145,-0.02518417,-0.02290544,-0.00502267,0.0461842,-0.01954052,0.00637858,0.02378386,-0.03257662,0.03048138,-0.01230902,0.11230128,-0.0014182,-0.02136604,-0.03735512,0.08249914,0.00749351,0.00449199,-0.0816471,-0.0321497,0.06411311,-0.06505046,0.01281817,0.00418998,0.01848782,-0.07033499,0.05005949,-0.00962049,-0.05872028,0.0126451,0.00314917,-0.00492893,0.00803272,-0.01184149,0.03736796,0.07432354,-0.04870063,0.03395556,0.05297283,0.09652016,-0.03387052,-0.06047815,0.02446506,-0.01643878,0.02550201,0.04034988,-0.08975098,0.06631775,-0.05898225,0.06443699,-0.00868394,-0.02081856,-0.03226238,0.02080677,-0.02722721,-0.02696695,0.06662228,-0.00200571,0.03324032,-0.06910637,0.02655292,0.07366621,-0.01473546,-0.0058738,0.02135613,-0.02591604,-0.04311748,0.00654436,0.06164144,0.04061149,0.06373936,0.0679681,0.07130597,-0.00802677,0.03879229,0.01844154,0.02269873,-0.04287263,0.05937962,0.00554065,0.04695699,-0.24966523,0.03237711,-0.02751279,0.03406344,0.01241004,0.00395782,0.04305151,-0.05005158,0.03808806,-0.05952077,0.12229196,0.02054324,-0.01244952,-0.07863168,0.04595016,-0.00806348,0.02931876,-0.01350176,0.03060692,-0.01667694,0.04482302,0.05388495,0.22940272,-0.02420628,-0.04304784,0.01131409,0.00532536,0.00966404,0.03101613,0.04704774,0.0061579,0.01341792,0.07872647,-0.00673602,-0.05393071,0.03778765,-0.03595368,0.02378266,-0.010213,0.0281732,-0.05481312,-0.00775028,-0.07648966,-0.0237437,0.08630846,0.05606397,-0.04186125,-0.10746122,0.04406935,0.08869152,-0.04783366,-0.07227225,-0.0486286,-0.03765374,0.04440141,0.01092662,-0.00213483,-0.00652352,0.00128265,-0.01185309,0.01815048,0.033105,0.04368905,0.01296556,0.02092269],"last_embed":{"hash":"1al2arn","tokens":152}}},"text":null,"length":0,"last_read":{"hash":"1al2arn","at":1753423490886},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#{15}","lines":[56,59],"size":498,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#8\\. Make/break/position.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09759879,-0.04836324,-0.00784951,-0.04186031,-0.02367616,0.01934968,0.00965468,-0.03491238,0.00828361,-0.01154472,0.00734444,-0.00452444,0.01753619,-0.02351353,-0.03333947,-0.02862884,0.0503956,0.02381098,-0.05190963,0.00686105,0.08618971,-0.02862444,-0.0355833,-0.06931475,0.05903402,0.02812378,-0.02811372,-0.05244404,-0.04296943,-0.20507014,0.04166827,0.04607312,0.04850315,-0.03023372,-0.10605698,-0.02016105,-0.01185964,-0.00446177,-0.0858245,0.014805,0.02150709,0.02612541,0.0012245,-0.03631547,0.00529402,-0.09215553,0.02143232,-0.01854587,0.07292217,-0.00814169,-0.03225321,0.00919656,-0.00394837,0.04905652,0.04385794,-0.00413595,0.07209191,0.07775667,0.03190977,0.01562017,0.03651708,0.04203703,-0.19564256,0.0614019,0.0064746,-0.01292471,-0.00784781,-0.04592469,0.03518093,-0.0182539,0.01147301,0.03303482,-0.01752026,0.05642271,-0.00408785,-0.06473907,-0.00425513,-0.04482125,-0.03348888,0.01577896,-0.05256943,-0.02688155,0.00327327,-0.02216155,0.01574092,0.06332856,0.04285202,0.06419947,0.08703059,-0.0797135,0.02659369,-0.00320296,0.06906471,0.05831915,0.02409558,-0.00733293,0.03984779,-0.05787047,-0.02230223,0.13163741,-0.00902678,-0.04083972,-0.02937632,0.00470939,0.07404859,-0.01404688,-0.0116128,-0.00339062,-0.0566369,0.01638494,0.0212926,0.00999273,0.05189784,-0.05217628,-0.03872393,-0.04219111,-0.06341908,-0.0267046,0.01877854,0.0239221,-0.02987438,0.03395734,0.02077798,0.02074256,0.01259367,-0.02304239,0.05185131,0.03039595,0.01074462,0.01965061,0.03043714,0.01237422,-0.11363889,-0.02780691,0.01876119,0.02843531,0.00301351,-0.03052097,-0.02423804,0.04714033,-0.00395396,-0.01335947,0.06487518,-0.11165615,0.00660254,0.06874622,-0.03220599,0.00366412,0.00913734,-0.01377799,0.01006552,0.00932412,-0.02298147,-0.04186008,0.02424931,0.00077119,0.10965196,0.07303514,-0.08947326,0.006697,-0.02025672,-0.04995259,-0.01895325,0.09051774,-0.01386113,-0.10750651,0.0147032,0.04061585,-0.05776427,-0.08191349,-0.02086094,0.03536,-0.05494177,0.01841752,0.08371254,-0.03948437,-0.02361243,-0.08214173,-0.00272799,-0.03522687,-0.00769203,-0.01319768,-0.02768468,-0.01742818,-0.01487554,-0.09677263,0.02269811,-0.06178709,0.02820998,0.04478413,-0.0431538,0.01005025,-0.01969638,-0.03132623,-0.0120058,-0.00410151,-0.06628039,-0.0522469,0.05372414,-0.04110824,-0.00500157,0.02387075,0.0053413,0.01803924,-0.01270891,0.0560239,-0.04989088,-0.05238912,0.05151827,-0.0179724,-0.05459662,0.03195637,0.11400063,0.10647088,-0.08987202,0.02277764,0.00311611,-0.00594437,-0.00582347,0.05531465,0.00140212,0.04657898,-0.06736758,-0.19270433,0.01795969,-0.037744,-0.01252562,0.0271374,-0.00990191,0.0354122,-0.0436639,0.05368375,0.06923129,0.12895505,-0.08974452,0.01993933,0.02491173,-0.01931326,0.00464569,-0.04960458,-0.06776261,-0.05142568,0.04093401,-0.01770774,0.03024727,-0.00025426,-0.04946812,0.03630299,-0.03714749,0.14518353,0.03901124,0.01861184,-0.0381592,0.06374937,-0.02216394,0.00167932,-0.06255635,-0.02414542,0.02568882,-0.03713268,0.03681444,-0.01991891,0.01881135,-0.07883324,0.04091256,-0.00247651,-0.09814554,0.04188412,0.00286392,-0.0030772,-0.00539402,-0.01314868,0.0361704,0.05491329,0.00082879,0.01031664,0.04772108,0.08850724,-0.03297564,-0.05838405,0.01823116,0.02730693,0.00601989,0.02573642,-0.07116707,0.05278172,-0.02493733,0.07936591,0.02422454,-0.02911346,-0.0321829,0.01398171,-0.00440442,-0.02282546,0.06722726,0.00045157,0.05652384,-0.05449651,0.04315227,0.09743264,-0.08195841,0.03711333,0.00533962,-0.02874986,-0.06289351,0.03763693,0.09197357,0.05745989,0.02695959,0.05502394,0.03987681,0.02394472,0.0066609,0.01175175,0.02253276,-0.0296727,0.02798359,0.00390348,0.05655239,-0.26150221,0.04314346,-0.00688267,0.02434283,-0.01546331,0.00296323,0.05176941,-0.0548685,0.0318814,-0.02148757,0.07446959,0.01847387,0.04507773,-0.06625282,-0.00158179,-0.03136269,0.07201707,0.00770547,0.05453299,0.02249328,0.04200505,0.07036802,0.21711127,-0.03199278,0.00087664,0.04056374,0.00191674,-0.03111554,0.00112822,0.06079284,0.00817133,0.00251593,0.07857323,0.00519034,-0.0071883,0.01092754,-0.02664679,-0.00561049,0.00880324,0.01150935,-0.03372974,0.01300052,-0.06240675,-0.00755182,0.1018061,0.03961506,-0.0276615,-0.0802086,0.05622656,0.03855479,-0.09264074,-0.06596088,-0.04863796,-0.0405395,0.01821633,0.00729674,-0.00260424,0.00127396,-0.00498517,0.00166466,0.0307688,-0.00061044,0.06223978,-0.00268651,0.00070084],"last_embed":{"hash":"8ip5da","tokens":261}}},"text":null,"length":0,"last_read":{"hash":"8ip5da","at":1753423490930},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#8\\. Make/break/position.","lines":[74,81],"size":724,"outlinks":[{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":5},{"title":"For starters, you need simulate lotto, lottery data files to use the software.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#8\\. Make/break/position.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09355697,-0.04687745,-0.00839568,-0.03937932,-0.02151168,0.01889879,0.01181935,-0.03599064,0.00558866,-0.01164768,0.00475869,-0.00611082,0.0165615,-0.02095057,-0.03015138,-0.03188262,0.04851722,0.02585481,-0.0486323,0.00929576,0.09013095,-0.03171363,-0.03297412,-0.07073933,0.06003445,0.02823911,-0.02777659,-0.05520488,-0.04324457,-0.2053259,0.03976408,0.04594734,0.04942024,-0.02921964,-0.107747,-0.01780511,-0.01326532,-0.00389846,-0.08051213,0.01741267,0.02199171,0.02558,0.00303148,-0.03674078,0.00442592,-0.09144761,0.01906743,-0.01770524,0.07108903,-0.00629661,-0.0330864,0.0119257,-0.00345655,0.05244176,0.04210621,-0.00223479,0.07250737,0.07601276,0.02994287,0.01684084,0.03719229,0.04017315,-0.19733955,0.06168975,0.00662851,-0.01491346,-0.00621346,-0.04727908,0.03127813,-0.01737349,0.01273346,0.03096641,-0.02197818,0.05746013,-0.00412711,-0.06847474,-0.00385389,-0.04341065,-0.03224058,0.01790284,-0.05102149,-0.02770769,0.00201319,-0.02179537,0.01542408,0.06333682,0.04317008,0.06425526,0.08735776,-0.08119132,0.0284226,-0.00410356,0.06679448,0.05798092,0.02533849,-0.00943078,0.03836684,-0.05313753,-0.02331456,0.13330267,-0.00599834,-0.04133617,-0.02954906,0.00789607,0.07253923,-0.00951063,-0.01507542,0.001304,-0.05812873,0.01594114,0.01762294,0.01327047,0.04921656,-0.05277289,-0.03662612,-0.03834948,-0.06158365,-0.02700433,0.01665857,0.02207102,-0.02970553,0.03698891,0.01832904,0.02425008,0.01208303,-0.02516128,0.05343156,0.02988265,0.00947284,0.01734977,0.03043487,0.00700145,-0.11479335,-0.02872615,0.01705778,0.03041411,0.0035686,-0.02836665,-0.02703826,0.04707654,-0.00517245,-0.013437,0.06707163,-0.11083634,0.00756496,0.07179036,-0.0375546,0.00149192,0.01289748,-0.01687261,0.0081538,0.01176786,-0.02186568,-0.04328127,0.02157263,0.00081956,0.10699277,0.07422445,-0.09079106,0.0078296,-0.02082647,-0.04955126,-0.01753336,0.08671405,-0.01552849,-0.10750253,0.0112757,0.03976129,-0.05923619,-0.07922991,-0.02002681,0.03645286,-0.05424249,0.02104461,0.08377101,-0.04003363,-0.02597983,-0.0806447,-0.00655257,-0.03294772,-0.01394179,-0.01405042,-0.02636714,-0.02118328,-0.0146385,-0.09411941,0.0197608,-0.06218951,0.02772954,0.04399171,-0.04403441,0.01189222,-0.01985261,-0.02881195,-0.01302281,-0.00095493,-0.06490439,-0.05029939,0.05283467,-0.03645994,-0.00636341,0.0235761,0.00193,0.01453272,-0.01292557,0.0530844,-0.04909832,-0.05077256,0.04872759,-0.01764241,-0.05439187,0.03442957,0.11093356,0.10309259,-0.08941545,0.02049343,0.00174461,-0.00588029,-0.00478444,0.05408338,0.00041227,0.04766047,-0.06445201,-0.19521719,0.02066146,-0.03953959,-0.00866648,0.02721655,-0.00892088,0.03542756,-0.04099611,0.0588319,0.06689163,0.12869607,-0.08920856,0.0186832,0.02202928,-0.01855082,0.00539391,-0.04624379,-0.06790137,-0.05114466,0.03867803,-0.0149717,0.03379167,-0.00438895,-0.04783389,0.03844115,-0.03600278,0.14834869,0.03915883,0.01985757,-0.03770804,0.06525002,-0.02523451,0.00085083,-0.06322159,-0.021971,0.02435741,-0.03936994,0.03524555,-0.02117088,0.01764081,-0.0778966,0.03940823,-0.00073106,-0.09639665,0.04574905,0.00666928,-0.00312157,-0.00459199,-0.01402236,0.03744064,0.05906475,0.00049093,0.00869784,0.04884604,0.08662015,-0.03401391,-0.05780643,0.01171089,0.02614168,0.00178681,0.02429845,-0.06844094,0.05447439,-0.0220611,0.08301705,0.02192573,-0.02980255,-0.03042094,0.01754471,-0.00199003,-0.02232891,0.06578461,0.00285434,0.05858744,-0.05513464,0.04029414,0.09762329,-0.08665884,0.03950626,0.0062728,-0.02696655,-0.06725407,0.03738279,0.0913682,0.05945565,0.02507673,0.05830493,0.04016607,0.02365954,0.0077148,0.01476616,0.02397144,-0.03421434,0.02971432,0.00205591,0.05420147,-0.26317912,0.04456441,-0.01080073,0.02306948,-0.01659219,0.0021026,0.0493577,-0.05511136,0.03130886,-0.02086001,0.07440647,0.0187585,0.04715595,-0.06641013,-0.00551274,-0.027743,0.06860397,0.00937569,0.05378185,0.02499028,0.04195803,0.06752541,0.21923557,-0.03148792,0.00236909,0.03900484,-0.00450591,-0.03375015,0.00117091,0.06125265,0.01086704,0.00074227,0.07970984,0.00730959,-0.00531476,0.01007762,-0.02473293,-0.00278562,0.00936473,0.01012831,-0.03341564,0.01264895,-0.06147289,-0.00941004,0.10832965,0.04037641,-0.02641977,-0.07839152,0.0585761,0.03544395,-0.09347691,-0.06590325,-0.05023554,-0.03817834,0.01575591,0.00500415,-0.00546534,0.00076011,-0.00855552,0.00160702,0.02880139,0.00128182,0.06101489,-0.00456286,0.00411988],"last_embed":{"hash":"1ouaddf","tokens":259}}},"text":null,"length":0,"last_read":{"hash":"1ouaddf","at":1753423491012},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>1. Overview of Lottery, Lotto Software Utilities</u>#8\\. Make/break/position.#{1}","lines":[76,81],"size":694,"outlinks":[{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/ScreenImgs/super-utilities-lottery.gif","line":3},{"title":"For starters, you need simulate lotto, lottery data files to use the software.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>2. Simulate Lotto, Lottery Data Files</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07820017,0.00337672,-0.00586745,-0.07013346,-0.00785939,0.03059866,-0.00839959,-0.03797125,0.02442724,-0.00342087,0.02074098,-0.00742959,0.05146757,-0.02065708,-0.03159291,-0.03046159,-0.00648773,-0.01725715,-0.08162493,0.00279389,0.0887663,-0.02863786,-0.06800525,-0.10007259,0.03046686,0.02005886,-0.02115672,-0.04075669,-0.05513921,-0.21297187,0.06338164,-0.00276796,0.05286897,-0.03907787,-0.09335175,-0.03313424,-0.01123025,0.07499216,-0.09497704,0.01563076,0.03324946,0.01236468,0.00622249,-0.03880167,-0.00588279,-0.08459742,0.00976121,-0.05455448,0.01756409,0.02954802,-0.04647776,-0.00644825,0.05835464,0.03406746,0.03776386,-0.00795123,0.0456591,0.07829525,0.05675599,0.01495751,0.02650858,0.06191321,-0.16718553,0.05508719,-0.04883204,0.00200767,-0.01517578,-0.05987589,0.01953635,0.02059202,-0.02461573,0.03755147,-0.01064491,0.08303412,0.0178996,-0.06995536,-0.03796221,-0.04942821,-0.06449119,0.00013741,-0.0707846,-0.04386882,0.01528026,0.00376949,-0.02145814,0.06763774,0.02448955,0.02011373,0.03439833,-0.03447656,0.04499382,0.03136062,0.01865039,0.05028306,0.01161084,0.02980511,0.04196535,-0.01915289,0.00021673,0.12950355,0.02257338,-0.012229,-0.01313308,-0.01163938,0.05150042,-0.04475844,-0.02612023,-0.00871063,-0.03167435,0.03095376,0.02150677,-0.00727606,0.05123744,-0.03144865,-0.06946797,-0.01933551,-0.02657514,-0.00095934,-0.0053536,0.01746602,-0.01060643,0.01740719,-0.02396178,0.00590955,0.01665649,0.0198039,0.00775603,0.07502069,0.00529931,0.01700429,0.05739171,0.02803848,-0.13249098,-0.00519815,0.01857296,0.00764331,-0.00793474,-0.03835338,-0.05139037,0.00670908,-0.03561237,0.04716974,0.03342057,-0.102032,-0.01711786,0.06086924,0.0070551,0.02814127,0.01063688,-0.05644949,0.00744557,0.00925565,-0.05158474,-0.02947419,0.03869565,0.01476302,0.06457672,0.06937489,-0.06343361,-0.02186988,-0.0443532,-0.02330161,0.00579537,0.11800856,-0.02179713,-0.07585681,-0.01542764,0.06445472,-0.02942265,-0.06113567,-0.00736358,0.0084333,-0.05713916,0.03267064,0.05882036,-0.03013296,-0.09149121,-0.06774225,-0.00873321,-0.04028465,0.02532324,-0.01538981,-0.03356412,-0.00793126,-0.02425518,-0.07807751,0.0282181,-0.00470352,0.0105507,0.07559573,0.01477078,0.01505319,0.01353622,-0.01645169,-0.01224189,-0.06428506,-0.04724905,-0.05563384,0.04484892,-0.06057405,0.00770469,0.00819457,0.03113756,0.04794417,-0.02440375,0.03332857,-0.02677278,-0.04017631,0.08920912,0.01695334,-0.06366184,-0.01990989,0.04834836,0.05366424,-0.05552607,0.03538867,0.0105771,0.00870199,0.004523,0.03166603,-0.01495524,0.04118218,-0.03493781,-0.20178372,-0.03221453,-0.02355685,0.02297314,0.02302524,-0.02694059,0.00841484,-0.04805391,0.03829853,0.0663695,0.12964974,-0.04892075,-0.01892334,0.07462308,-0.03224501,-0.01239831,-0.08194315,-0.03099987,-0.07455928,0.05311284,0.00072925,0.02893169,-0.01851063,-0.04482198,0.02513489,-0.03607673,0.1158632,-0.01781087,0.02116744,0.00165567,0.09562793,0.03359981,0.01164018,0.00168706,-0.00151213,0.04331822,-0.03297578,0.055349,-0.01357116,0.01293572,-0.0868104,0.02668206,-0.00796721,-0.09909219,0.016415,0.03870402,-0.03552462,0.05951576,-0.03731112,0.02595688,0.01985872,0.01275142,0.02999157,-0.0076914,0.06941142,-0.05069027,-0.06857954,-0.00550907,0.00399039,0.03623789,0.01537789,-0.08611128,0.04980209,-0.02822458,0.05390314,0.01692522,0.02397721,-0.03715698,0.02442973,0.00074617,0.00425451,0.09149421,-0.02406296,0.06091379,-0.04894574,0.0461021,0.04968986,-0.0220677,0.01063775,-0.05288319,0.0347621,0.00324606,0.01150538,0.10314476,0.06035481,0.01642262,0.05807196,0.06265212,0.0462123,0.00279978,-0.01371071,0.01442749,-0.03231936,0.00047319,0.00002103,0.02630292,-0.27508396,0.02514635,-0.02554703,0.07868593,-0.03726995,-0.03143921,0.02597342,0.04964684,0.04815838,-0.04938938,0.02723048,0.01910883,0.04319722,-0.07612956,-0.0138856,-0.02853902,0.01996028,0.01241285,0.07400256,-0.01659875,0.06036704,0.03129467,0.25034398,-0.05534428,-0.01139111,0.02218343,0.01851389,-0.0158316,0.00565411,0.02323753,0.00310331,0.01747131,0.10524788,-0.01368948,-0.01427305,0.02940082,-0.02518016,0.04462086,0.02599432,0.00715418,-0.06287783,0.01929891,-0.04261165,-0.01106694,0.0884897,0.05683166,-0.01498983,-0.05361473,0.03019241,0.12028911,-0.08763996,-0.04707158,-0.07179372,-0.07450137,0.03825233,0.00831099,0.00286794,0.02090793,0.03577833,0.02593662,0.03158515,-0.00127045,0.09376441,0.00060161,-0.0386835],"last_embed":{"hash":"2bpji7","tokens":446}}},"text":null,"length":0,"last_read":{"hash":"2bpji7","at":1753423491089},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>2. Simulate Lotto, Lottery Data Files</u>","lines":[82,102],"size":3509,"outlinks":[{"title":"The software strips duplicates and wheels the lotto, lottery numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":20}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>2. Simulate Lotto, Lottery Data Files</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07512013,0.00523959,-0.00804029,-0.07188264,-0.00595368,0.03196773,-0.012449,-0.03857667,0.02452472,-0.00299518,0.01656549,-0.01116304,0.05316893,-0.01913297,-0.02846916,-0.02654689,-0.01024162,-0.02081845,-0.08123593,0.00455895,0.08456636,-0.02743044,-0.06800332,-0.10023874,0.02974207,0.01759486,-0.02074674,-0.04104632,-0.05488815,-0.21467957,0.06339803,-0.00445058,0.05133874,-0.03770512,-0.09042954,-0.03704068,-0.01256002,0.07274943,-0.08961867,0.01926167,0.03458397,0.0076078,0.00748581,-0.04004661,-0.00558256,-0.08375016,0.0108015,-0.05333511,0.01715558,0.02779928,-0.04587939,-0.0084931,0.06110669,0.03141117,0.03743638,-0.00837296,0.04496855,0.07794001,0.05650663,0.0151648,0.02912327,0.06322581,-0.16780204,0.05511026,-0.04830394,-0.00052724,-0.012091,-0.06114777,0.01785661,0.0195802,-0.02579954,0.03910472,-0.01109491,0.08510527,0.01634094,-0.07055837,-0.03855028,-0.05095086,-0.06192382,0.00200456,-0.07444458,-0.04147859,0.01508042,0.00278504,-0.02034873,0.06550718,0.0245477,0.02005881,0.03438283,-0.03448627,0.04667726,0.03305366,0.01710257,0.05091384,0.01399736,0.02756886,0.03963787,-0.01608117,0.00396505,0.12756895,0.02407053,-0.0106005,-0.01211984,-0.01355447,0.05034516,-0.04541449,-0.02774258,-0.00843841,-0.02748886,0.03348169,0.01845854,-0.00878827,0.05329963,-0.03208488,-0.07008126,-0.01677085,-0.02375549,-0.00112507,-0.00396761,0.0168227,-0.00830392,0.0152356,-0.02262194,0.00594441,0.01509046,0.01921563,0.01107641,0.07498873,0.00642989,0.0134578,0.05321276,0.02738571,-0.13119228,-0.00473454,0.01628053,0.00699968,-0.00787511,-0.03847778,-0.04985505,0.00535345,-0.03334158,0.04591725,0.03210742,-0.10413601,-0.0177261,0.06195043,0.00786795,0.03069239,0.0096812,-0.05735942,0.00660806,0.01226171,-0.05297391,-0.03221727,0.0408066,0.01538061,0.06443306,0.07396992,-0.06389835,-0.02399412,-0.04425177,-0.02229737,0.0062578,0.11870082,-0.02741571,-0.07541677,-0.01924137,0.06400607,-0.02527661,-0.05948801,-0.00566425,0.00727915,-0.05729944,0.03222148,0.06051096,-0.03171284,-0.09503748,-0.06629521,-0.00923033,-0.0377124,0.02393487,-0.01803704,-0.03314574,-0.00799898,-0.02645487,-0.07561287,0.02567402,-0.00016716,0.00848973,0.07450299,0.01406612,0.0190398,0.01113272,-0.01770645,-0.01409481,-0.06193116,-0.04480712,-0.05305092,0.04533271,-0.06538247,0.00841948,0.00768086,0.0307337,0.04369579,-0.02175775,0.03400423,-0.02606966,-0.03592972,0.0895042,0.01577259,-0.06477506,-0.02545211,0.04566198,0.05074822,-0.05325753,0.03441879,0.00790221,0.00638598,0.00661928,0.02834312,-0.01266483,0.03865991,-0.03760507,-0.20365037,-0.03353682,-0.02227909,0.02205308,0.02207733,-0.02687537,0.01153452,-0.04391388,0.04050262,0.06289924,0.12804358,-0.04686296,-0.02237132,0.07848562,-0.03272356,-0.01198453,-0.08057062,-0.03011726,-0.07382417,0.05245443,0.00060193,0.03088717,-0.01952879,-0.04528227,0.02699669,-0.03306848,0.11692206,-0.01745152,0.01539742,-0.00024817,0.09684217,0.03221318,0.01437558,0.00631234,0.00078948,0.03835624,-0.0340324,0.05240455,-0.01396482,0.01290412,-0.08891035,0.02580027,-0.00469299,-0.09648483,0.01545152,0.04070501,-0.03416029,0.06321903,-0.03980695,0.02513566,0.01716371,0.01761001,0.02971255,-0.01141079,0.06848522,-0.04926966,-0.06800203,-0.01109794,0.00518116,0.03497678,0.01611669,-0.08556278,0.05043488,-0.02928928,0.04979203,0.01125203,0.02500442,-0.03628638,0.02814984,0.00254831,0.00856864,0.09152203,-0.02371673,0.05859146,-0.04334306,0.04393216,0.05296136,-0.02433821,0.01076097,-0.05247105,0.03520776,0.00332972,0.01113509,0.1048453,0.05929697,0.01809368,0.05851961,0.06340115,0.04512097,0.00273364,-0.01360734,0.00940367,-0.02897251,-0.00072801,-0.00283023,0.02639206,-0.27681488,0.02655524,-0.0270627,0.08327229,-0.03406645,-0.03207632,0.02538652,0.05264947,0.04339802,-0.04947521,0.0283727,0.02263785,0.04799793,-0.075943,-0.01722937,-0.02919937,0.01891848,0.01229819,0.07358332,-0.02149529,0.05875522,0.02870951,0.25271276,-0.05731105,-0.00937559,0.02226833,0.01521305,-0.01318036,0.00564812,0.02428792,0.00469597,0.01552678,0.10420366,-0.01581022,-0.0140056,0.0298238,-0.02835084,0.04647125,0.03133512,0.00535194,-0.06336954,0.01728931,-0.04086064,-0.01232526,0.08835974,0.0582018,-0.01417742,-0.05209017,0.02802908,0.12395859,-0.08656345,-0.04611273,-0.0710021,-0.07348975,0.04218583,0.01021465,0.00413341,0.02055258,0.03858716,0.02611335,0.03156975,0.00205537,0.09524248,0.00045806,-0.03936636],"last_embed":{"hash":"jxnh09","tokens":446}}},"text":null,"length":0,"last_read":{"hash":"jxnh09","at":1753423491236},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>2. Simulate Lotto, Lottery Data Files</u>#{1}","lines":[84,102],"size":3460,"outlinks":[{"title":"The software strips duplicates and wheels the lotto, lottery numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":18}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>3. Strip Duplicates and Wheel Lotto, Lottery Numbers</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08860487,-0.06793274,-0.0032412,-0.01933203,-0.04396158,0.02506336,-0.00360815,-0.03843157,0.04789944,0.00634549,0.04258713,0.00304711,0.04875566,-0.06040693,-0.0223819,-0.0122351,0.00348569,0.0016428,-0.12168705,-0.02868871,0.05678325,-0.05668414,-0.0381953,-0.07229809,0.04560681,0.0393342,-0.06929468,-0.02412215,-0.03591121,-0.26613259,0.03910721,0.03995808,-0.00876748,-0.05628471,-0.04055354,-0.05650551,-0.03594332,0.12834142,-0.07451229,0.03032011,-0.00767755,0.01149201,-0.02672737,-0.01576944,0.00212862,-0.03213924,-0.0371704,-0.00161984,0.06729475,0.02975383,0.00008741,-0.03535378,0.01698755,0.04526058,0.04332252,0.00327997,0.04391192,0.09685569,0.02703259,0.0258592,0.04010368,0.08282045,-0.14915842,0.06185873,0.03006977,0.01286179,-0.00788981,-0.05930356,-0.01906575,0.0922144,-0.00572145,0.02870724,-0.01234701,0.09110474,0.01044936,-0.03174321,-0.0433336,-0.05261359,-0.0951453,0.0328452,-0.04249427,-0.02369177,0.00760428,-0.00450298,-0.02923711,0.00464189,0.0425106,0.04742808,0.03807165,-0.05316662,0.03367205,0.03526349,0.05140147,0.01335971,-0.00715209,0.01649276,0.05273311,0.02315879,0.00029131,0.09363362,-0.00436425,-0.00901406,0.01994159,0.01326249,0.0436142,-0.02073572,-0.04253636,-0.02046382,-0.04620525,0.00888446,0.06200158,0.01962063,0.05639681,-0.05419991,-0.05800297,0.01190097,-0.01070216,0.00696167,-0.03171867,0.03240542,-0.01947666,0.00279469,0.0080618,-0.02186886,-0.01031894,0.00724094,0.01246432,0.07278508,0.04111335,0.02413193,0.05616332,-0.00226738,-0.07614687,-0.02361178,0.00921757,0.01232974,0.00494014,-0.01086415,0.02107011,0.01206333,-0.00690425,-0.03302942,0.0406052,-0.05999691,0.01548442,0.06043265,-0.03829432,0.01125104,0.03756865,-0.00274592,-0.01424606,-0.00399298,-0.0743641,-0.04408837,0.03117926,0.00647197,0.10586303,0.04588114,-0.06171326,0.02029146,-0.00962723,-0.03158144,-0.03012975,0.12792121,-0.00230244,-0.07305248,-0.0032654,0.0493801,-0.03049843,-0.07962668,-0.01748471,-0.02773125,-0.04893346,0.03349314,0.06370597,-0.01513443,-0.10150003,-0.04336003,-0.01965038,-0.01091066,0.00063584,-0.02212597,-0.05767913,-0.00890824,-0.03098174,-0.10577724,-0.01668339,0.0083826,0.00462731,0.08000453,-0.03825535,0.05343891,0.03211775,-0.01145178,-0.02620066,-0.04800176,-0.03781411,-0.05178217,0.03350583,-0.05754377,0.01448314,0.00554109,0.05134473,0.03323884,-0.04221258,0.0523424,0.0079573,-0.04803213,0.08037938,0.03386375,-0.03231172,-0.01454076,0.02450919,0.10065871,-0.03462853,0.04541266,0.02774984,0.00910982,0.01934278,0.03024035,0.00382954,0.02619888,0.00849603,-0.20177248,-0.04107353,0.00659274,0.02071589,0.04276181,-0.02944557,-0.01314456,-0.00422978,-0.00123019,0.0574173,0.09491471,-0.04330518,-0.0167845,0.04801982,-0.00143057,0.01715432,-0.08849283,-0.01900679,-0.03280545,0.08311078,0.00043503,0.03453219,-0.00210062,-0.08582076,0.06719922,-0.03614108,0.12419741,-0.00689786,-0.00859997,-0.02604708,0.07576242,0.05454892,0.00619447,0.01748313,-0.00596209,0.03169069,-0.08057226,0.02067847,-0.02540137,-0.00629602,-0.05638945,0.00056499,-0.01607301,-0.1081587,0.03059759,-0.00100221,-0.03695676,0.00748521,-0.0028828,0.06707462,0.02471478,-0.00291108,0.00949637,-0.00683033,0.10483286,-0.02999794,-0.07436746,0.01918907,0.02525513,0.02613705,-0.00387918,-0.05403331,0.06248324,-0.04648686,0.04801741,0.0420412,-0.01054861,-0.04760826,0.00985541,0.0162227,-0.01512345,0.06287129,0.0111319,0.02843579,-0.09780312,0.0427666,0.02242257,0.00706918,0.03382518,-0.03151442,0.01646794,-0.02280887,0.01881403,0.09583002,0.00578741,-0.01140533,0.0263446,0.07109316,0.02329726,0.00446963,0.00512633,0.00141635,-0.00528355,0.01175846,0.00563462,0.00867475,-0.24512973,-0.00901111,-0.07018134,0.03500405,-0.02138993,0.04361172,0.02993584,0.02674825,0.05984743,-0.03057536,0.05145257,0.07624369,0.01724858,-0.0920326,0.04527512,-0.0168445,0.00665922,-0.02584504,0.02372683,-0.02487284,0.1061235,0.03754932,0.24945767,-0.00395357,0.00011557,0.01557883,0.01704907,0.02542893,-0.02883743,0.0491721,-0.05226951,0.01048991,0.06101311,-0.01013947,-0.027723,0.00915942,-0.04365292,0.00682556,0.00134839,0.01695283,-0.07316843,-0.00454949,-0.07706369,-0.03290488,0.1133124,0.03118567,-0.00251647,-0.09728537,0.03065074,0.06347138,-0.0651382,-0.06117034,-0.07027823,-0.07753634,0.02666601,0.01969077,-0.01318183,0.02159517,0.00800982,0.04630919,0.01796226,-0.04094965,0.08136058,0.02938602,-0.0610956],"last_embed":{"hash":"1wyd673","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"1wyd673","at":1753423491376},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>3. Strip Duplicates and Wheel Lotto, Lottery Numbers</u>","lines":[103,124],"size":2513,"outlinks":[{"title":"The software count total lines in lotto, lottery files, or any text files.","target":"https://saliu.com/images/lotto.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>3. Strip Duplicates and Wheel Lotto, Lottery Numbers</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08755682,-0.06892038,-0.00289222,-0.01888772,-0.04130391,0.02954846,-0.00538349,-0.03649968,0.05008443,0.00829136,0.04489663,0.00334785,0.04849292,-0.06057085,-0.02342508,-0.0112492,0.00045495,0.00177208,-0.12077954,-0.03183103,0.05749147,-0.0585912,-0.03655433,-0.06948239,0.04548753,0.04059714,-0.0682287,-0.02266587,-0.03376795,-0.26719844,0.03926791,0.03990276,-0.01011923,-0.05854955,-0.03788399,-0.06014741,-0.03913877,0.13156949,-0.0725989,0.02998855,-0.00774607,0.00704663,-0.02813292,-0.01220702,0.00063563,-0.02985567,-0.03694523,-0.00190497,0.06607038,0.0297749,0.00205218,-0.03824387,0.01842107,0.04578878,0.04078975,0.00178029,0.04295753,0.09755117,0.02482808,0.02554225,0.03903526,0.08276365,-0.14937724,0.0626751,0.02953886,0.01345756,-0.00652843,-0.05958683,-0.01973336,0.09676018,-0.00646809,0.02685545,-0.01347165,0.09246722,0.00914859,-0.03026441,-0.04507595,-0.0534096,-0.09351844,0.03571428,-0.04336683,-0.02249466,0.00694182,-0.00481683,-0.02941674,0.00283197,0.04204015,0.04768334,0.03538596,-0.05202521,0.03159777,0.03707106,0.04949285,0.01406484,-0.00875996,0.01405739,0.04796161,0.02326941,0.00413761,0.09129228,-0.0044589,-0.00646625,0.02465537,0.00947943,0.03964487,-0.01848696,-0.04120233,-0.02192502,-0.04815955,0.01129452,0.06349246,0.0214181,0.05660172,-0.0538733,-0.05438565,0.01211735,-0.007224,0.00659152,-0.03209795,0.03122191,-0.01888162,0.00357776,0.00920512,-0.02131144,-0.01298707,0.00917095,0.01374565,0.07248265,0.04198241,0.02317688,0.05710558,-0.00423031,-0.07544663,-0.02388375,0.00809499,0.01019218,0.00584958,-0.01245277,0.02076936,0.01367084,-0.00704794,-0.03295291,0.04003189,-0.05900065,0.01376125,0.06060185,-0.04069384,0.01149792,0.03596526,0.00021387,-0.01379731,-0.00424908,-0.07150841,-0.0440939,0.02925027,0.00478715,0.10627803,0.04805888,-0.06141639,0.02311607,-0.01016503,-0.03427469,-0.03136536,0.12889178,-0.00110337,-0.07119253,-0.00468011,0.04919717,-0.03012616,-0.08187953,-0.016242,-0.03061931,-0.04642941,0.03321475,0.06192454,-0.0096937,-0.10288882,-0.04170017,-0.02043916,-0.00918338,0.00093441,-0.02296231,-0.05927311,-0.00897854,-0.03144183,-0.10586033,-0.01895155,0.01282198,0.00534993,0.08106177,-0.03958853,0.05546933,0.03358416,-0.01090255,-0.0278888,-0.04823355,-0.03556968,-0.05091428,0.03216567,-0.05760306,0.01562774,0.00543941,0.05071663,0.03006266,-0.04305635,0.0519144,0.00945987,-0.04695448,0.08232999,0.03596225,-0.0323382,-0.01693597,0.02297494,0.09938716,-0.03282287,0.04354807,0.0276661,0.00865778,0.01688164,0.02499921,0.00554471,0.02597695,0.01034417,-0.20250365,-0.04335159,0.00752359,0.0221152,0.04338954,-0.02865023,-0.01430791,-0.00329617,0.00159761,0.05672853,0.09238153,-0.04202748,-0.01776072,0.04993571,-0.00203991,0.02228525,-0.08811598,-0.0177794,-0.03163666,0.08112369,-0.00204879,0.03831338,-0.00323996,-0.08724747,0.06713735,-0.03555309,0.1258152,-0.00844395,-0.00679146,-0.0240773,0.07552859,0.05300323,0.005971,0.02245291,-0.00568514,0.03162215,-0.08043469,0.01686843,-0.02680724,-0.00983243,-0.05527675,-0.00102338,-0.01581546,-0.10873183,0.03017135,-0.00263841,-0.03662624,0.01027068,-0.00469005,0.06836944,0.02274277,-0.00093533,0.00624213,-0.0081467,0.1025071,-0.02855472,-0.07319507,0.01736034,0.02501694,0.02674548,-0.00479285,-0.05104737,0.06113191,-0.04581634,0.04701725,0.04155582,-0.0094038,-0.04719207,0.01229593,0.01728914,-0.0154969,0.06349453,0.01228746,0.0273391,-0.09581902,0.04039641,0.02371039,0.00672151,0.03431694,-0.0330002,0.01822344,-0.01997963,0.01719952,0.09550954,0.00751674,-0.01058217,0.02506114,0.06959105,0.02287299,0.00325489,0.00245621,-0.00016802,-0.00274161,0.01008982,0.0062059,0.00672509,-0.24569075,-0.00962957,-0.07157585,0.03478254,-0.02148302,0.04070627,0.03144783,0.02687381,0.05731137,-0.03046345,0.05013849,0.07904305,0.01792167,-0.09136141,0.04425776,-0.01467936,0.00480156,-0.0275761,0.02320651,-0.02466485,0.10940067,0.03647619,0.25067791,-0.00493063,0.00121188,0.01443523,0.01505335,0.02695934,-0.03084658,0.04645951,-0.05339728,0.01069727,0.05961953,-0.01041484,-0.02666182,0.0120187,-0.04136831,0.00709899,0.0013569,0.0143,-0.07158374,-0.00571316,-0.07743625,-0.03211996,0.11452489,0.03085486,-0.0004298,-0.09612882,0.02957588,0.06187959,-0.06548487,-0.06038539,-0.07238058,-0.07936644,0.02818976,0.02056674,-0.01324191,0.01887632,0.00729765,0.04708797,0.01765196,-0.04024104,0.0802527,0.03152888,-0.06243904],"last_embed":{"hash":"o8yki0","tokens":433}}},"text":null,"length":0,"last_read":{"hash":"o8yki0","at":1753423491519},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>3. Strip Duplicates and Wheel Lotto, Lottery Numbers</u>#{1}","lines":[105,124],"size":2449,"outlinks":[{"title":"The software count total lines in lotto, lottery files, or any text files.","target":"https://saliu.com/images/lotto.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>4. Count Lines in Lotto, Lottery Files, or Any Text Files</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08220503,-0.03603537,-0.00867279,-0.03761739,-0.0335632,0.01589037,0.02210801,-0.01067266,0.01121892,-0.01982104,0.00789973,-0.00856961,0.04556258,-0.01139036,-0.04069323,-0.0528964,0.00339957,0.0055979,-0.08906304,0.00542523,0.08395807,-0.02588873,-0.05081253,-0.05583861,0.03601503,0.02285399,-0.04498712,-0.09772788,-0.04926152,-0.22135948,0.03713431,0.00435221,0.03807342,-0.07095436,-0.0390602,-0.02479013,-0.03467673,0.03936164,-0.07585195,0.01885024,0.02044006,0.0374872,0.00690896,-0.03614671,0.00638797,-0.1010966,0.01215179,-0.01101059,0.05916992,0.01061488,-0.0242141,-0.00015795,0.04128021,0.0335598,0.02869189,0.00033932,0.0241013,0.0679234,0.02184045,0.01527751,0.04145859,0.05663262,-0.1902767,0.0335785,-0.01656382,0.00223485,-0.03825723,-0.03602874,-0.01995732,0.05059572,0.0055463,0.02211012,-0.0531322,0.09140565,-0.02429221,-0.05165164,-0.04267611,-0.0559482,-0.06953388,0.03906846,-0.06157451,-0.02612987,-0.00854741,-0.01046557,-0.02052227,0.04875293,0.04517238,0.07882825,0.04333726,-0.0574167,0.04495665,0.02245978,0.04059488,0.04940393,0.00250159,0.005318,0.03866908,-0.0209237,-0.02941978,0.12903997,0.01467622,-0.02910956,-0.00803384,0.03393116,0.04607395,-0.00622537,-0.02046768,0.00183007,-0.02588017,0.03174672,0.08108415,-0.02229716,0.03647188,-0.05710116,-0.01645308,-0.00761145,-0.01806241,0.00733927,-0.0076047,0.02484364,-0.0047225,-0.0207123,-0.01542226,0.0128957,0.03256833,-0.00245281,0.02230738,0.07031492,0.0161529,0.01000455,0.03291156,0.01381808,-0.14380586,-0.02047273,-0.01627096,0.02651672,0.02466602,-0.00454836,-0.00602367,-0.03282417,-0.00361865,-0.02645786,0.06407349,-0.07454886,0.0237738,0.11439034,-0.0297569,0.00904172,0.03778759,-0.06585372,0.03003171,0.01319645,-0.06660584,-0.06777027,0.01071569,0.00758508,0.09849718,0.04122403,-0.06937049,0.00439166,-0.01160812,0.01156534,-0.01547512,0.12945591,-0.03372866,-0.06877199,-0.01932491,0.04800915,-0.04588852,-0.08672838,-0.00403269,-0.01335567,-0.06433103,0.03656337,0.09418668,-0.08265419,-0.05015789,-0.05086712,-0.03437738,0.00577246,-0.00268378,0.03249322,0.00483208,-0.00633243,-0.00174972,-0.06681237,0.02694335,-0.03784153,0.0439954,0.07778838,-0.01161633,0.01265894,0.03184306,-0.02957873,-0.0092807,-0.03903876,-0.05675253,-0.05313166,0.03095244,-0.01680987,0.04232176,0.00333416,0.04301357,0.02861006,0.01633624,0.05346483,-0.04308153,-0.00552293,0.08985301,-0.00801686,-0.05542331,-0.00692453,0.030238,0.04152492,-0.07383425,0.03545973,0.00874219,0.00924082,0.00712113,0.04533582,-0.00176565,0.01980056,-0.02752133,-0.19138134,-0.04221386,-0.02351555,-0.00356589,-0.00588908,-0.03053324,0.02839606,-0.0069119,0.02180191,0.10926575,0.10333367,-0.06725429,-0.01608536,0.02207704,-0.03539918,-0.00004999,-0.07901329,-0.04138865,-0.05372515,0.06817733,-0.0009025,0.03619318,-0.04135893,-0.04266693,0.02650168,-0.02513582,0.1074069,0.02616237,-0.01662093,-0.0186108,0.06802324,0.04874026,-0.04044841,-0.02174553,0.0276245,0.05396505,-0.09443079,0.0403723,-0.0368636,0.03217944,-0.09635469,-0.00758685,0.00795888,-0.07598368,0.00181668,0.00023372,0.00582032,0.01780509,-0.02181247,-0.00120477,0.01903214,0.00726107,0.00454869,0.03079409,0.09900843,-0.01170768,-0.07511788,-0.04266096,0.01346243,-0.00632312,0.02865053,-0.06569868,0.05515575,-0.04993518,0.03391673,0.06653981,-0.01506985,-0.00138277,-0.0016448,0.01394379,0.02378923,0.08177584,0.0014684,0.03662293,-0.04028143,0.01686767,0.05799074,-0.03119665,-0.00714589,-0.05891086,0.02425451,-0.03411821,0.03059381,0.08135982,0.06407662,0.0320031,0.05229913,0.07933554,0.0387987,-0.00135299,0.05821664,0.04030703,0.00269058,0.01991449,0.01127428,0.06962354,-0.26086038,0.02814216,-0.04109035,0.03909509,-0.02199649,0.00057211,0.02176191,0.02680274,0.0332617,-0.05629062,0.05113807,0.01229356,-0.01392083,-0.13133298,-0.0116838,0.0008732,0.01316508,-0.01074653,0.09261442,-0.01051661,0.07312885,0.01266596,0.25054452,-0.01571396,0.01339058,0.03385226,0.04440028,-0.00915876,0.02850452,0.04706811,0.02497175,0.01352341,0.08367935,-0.0130924,0.02172735,0.06760579,-0.03146078,-0.00296841,0.00243331,0.03104954,-0.07160021,-0.00818269,-0.09264609,-0.01094446,0.09547214,0.03251005,-0.01477079,-0.06113362,0.04011852,0.07779667,-0.07306947,-0.05245135,-0.06272549,-0.03112936,0.04352188,0.01830986,-0.00056831,0.00626397,0.02420395,-0.01716629,0.01941306,-0.02622395,0.08129918,-0.01287632,-0.03060069],"last_embed":{"hash":"m7b5vb","tokens":209}}},"text":null,"length":0,"last_read":{"hash":"m7b5vb","at":1753423491668},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>4. Count Lines in Lotto, Lottery Files, or Any Text Files</u>","lines":[125,133],"size":713,"outlinks":[{"title":"The software programs check for winners in lotto, lottery combinations.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>4. Count Lines in Lotto, Lottery Files, or Any Text Files</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07860382,-0.037158,-0.00942287,-0.03710226,-0.03237584,0.01877679,0.02138797,-0.0096518,0.01125395,-0.01933432,0.00756307,-0.00922487,0.0454274,-0.00978653,-0.04163785,-0.05331364,-0.00228717,0.00259147,-0.08910622,0.00734451,0.08547781,-0.02731858,-0.04766625,-0.05691708,0.03526273,0.02199801,-0.04235686,-0.09886357,-0.04937485,-0.22095664,0.03684945,0.00467229,0.03561124,-0.07338405,-0.03735178,-0.02678253,-0.0380762,0.03975288,-0.07369917,0.02075916,0.02000744,0.03717149,0.00799207,-0.03540412,0.005154,-0.09995596,0.01087418,-0.00995745,0.05946433,0.01173232,-0.0204899,0.00025085,0.04234251,0.03409006,0.02727401,-0.00119106,0.02354281,0.06721158,0.02146964,0.01774687,0.04143934,0.05838057,-0.19108827,0.03208967,-0.01990102,-0.00001777,-0.03816788,-0.03292114,-0.02372736,0.05434884,0.00509802,0.02182119,-0.05428398,0.09280514,-0.02736358,-0.05292319,-0.04520892,-0.0583395,-0.06926607,0.04428105,-0.06208068,-0.02493278,-0.01079211,-0.01231405,-0.02070369,0.05094485,0.04518204,0.07652957,0.03987416,-0.05377872,0.04521744,0.02599627,0.03699513,0.04996668,0.00088561,0.00276669,0.03518589,-0.02116827,-0.02369088,0.12673734,0.01567842,-0.02742634,-0.0064517,0.03692498,0.04106771,-0.00317815,-0.02048003,0.00122411,-0.02479814,0.03476914,0.08142836,-0.02290264,0.03736229,-0.05670345,-0.01617991,-0.00624697,-0.0159967,0.00615369,-0.00632257,0.02371386,-0.0006158,-0.02025197,-0.01709252,0.01429646,0.03244513,-0.00131987,0.02565337,0.07206759,0.01879346,0.00930602,0.03384871,0.01208366,-0.14443892,-0.02205088,-0.0206879,0.02771319,0.02720558,-0.00369989,-0.00738656,-0.034508,-0.00312493,-0.02857416,0.06290773,-0.07583439,0.02389054,0.11544485,-0.03039302,0.0092591,0.03993409,-0.06539029,0.03173666,0.01486386,-0.06549916,-0.06929062,0.00879728,0.00851079,0.09494161,0.04186014,-0.06777705,0.00448616,-0.01375233,0.01218557,-0.01489169,0.12843561,-0.03562839,-0.0657566,-0.0215135,0.04614476,-0.04737246,-0.08915691,-0.00212789,-0.01557492,-0.06534414,0.03825255,0.09613902,-0.08081248,-0.04838026,-0.05093234,-0.03702918,0.01035504,-0.00197466,0.03201474,0.00250334,-0.00790117,-0.00339506,-0.06377697,0.02735815,-0.03476755,0.04373588,0.07634652,-0.01236316,0.01669366,0.03066784,-0.02716151,-0.01117959,-0.03680988,-0.05628927,-0.05319401,0.03123916,-0.01398401,0.0448465,0.00177774,0.04243375,0.02367452,0.02032928,0.05071044,-0.03994368,-0.00102892,0.09088716,-0.00478658,-0.05644573,-0.00640884,0.02768387,0.03665126,-0.07364799,0.03362368,0.00669787,0.0095984,0.00639283,0.04237687,-0.00017501,0.0165819,-0.0265954,-0.19218758,-0.04476302,-0.02338107,-0.00603363,-0.0052163,-0.02887236,0.02733316,-0.00395105,0.02374513,0.10938264,0.10234307,-0.06514143,-0.01665154,0.02311296,-0.0362696,0.00244352,-0.07930027,-0.04051756,-0.05410053,0.06735995,-0.00126171,0.03619728,-0.04309287,-0.0424235,0.0256634,-0.02204533,0.10888733,0.0274914,-0.01781335,-0.0165194,0.06881527,0.0476228,-0.04228731,-0.01989912,0.02860638,0.05279019,-0.0967625,0.03969564,-0.04065913,0.0316107,-0.09554763,-0.00830595,0.00896266,-0.07574075,0.00051889,0.000377,0.00648743,0.0194721,-0.02148824,-0.00005017,0.01281886,0.00668745,0.00344996,0.02990158,0.09625787,-0.01154433,-0.07234817,-0.04673301,0.01519779,-0.00720215,0.02786669,-0.06334116,0.05417201,-0.0489572,0.03059575,0.06662109,-0.01573648,0.00168458,0.00173403,0.01330586,0.02524292,0.08077934,0.00365137,0.03625554,-0.03406508,0.01479028,0.0568231,-0.03056454,-0.01080472,-0.05840442,0.0271309,-0.03418037,0.03143796,0.07913128,0.06282529,0.03207428,0.05386284,0.08209838,0.03842015,0.00074134,0.05853222,0.04183825,0.00545787,0.01730995,0.00976002,0.06525831,-0.26228654,0.02868151,-0.04478167,0.04040878,-0.02159511,-0.00168175,0.01932847,0.02836926,0.0302534,-0.05442675,0.0495823,0.01336358,-0.01252048,-0.134702,-0.01468484,0.00134066,0.0098708,-0.01448662,0.09537289,-0.01004232,0.07422742,0.01238828,0.25193128,-0.01715407,0.01709396,0.03488166,0.04129589,-0.00726411,0.02791496,0.04754788,0.02617075,0.01252925,0.08252039,-0.01111503,0.0241385,0.07110309,-0.02952624,-0.00198978,0.00134551,0.02789459,-0.0736219,-0.0120489,-0.09270494,-0.01114818,0.09664487,0.03520076,-0.01527811,-0.05989988,0.03838553,0.07758213,-0.07133692,-0.05292992,-0.06472873,-0.03184925,0.04337001,0.02089948,-0.00143757,0.00717818,0.02479807,-0.01798504,0.01641836,-0.02455836,0.08097991,-0.01361213,-0.02941314],"last_embed":{"hash":"1tl2ro4","tokens":208}}},"text":null,"length":0,"last_read":{"hash":"1tl2ro4","at":1753423491730},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>4. Count Lines in Lotto, Lottery Files, or Any Text Files</u>#{1}","lines":[127,133],"size":644,"outlinks":[{"title":"The software programs check for winners in lotto, lottery combinations.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07112048,-0.02840165,-0.01163852,-0.00993151,-0.0537703,0.01022888,0.05791799,-0.00781788,0.03387633,-0.00266051,0.01244811,-0.00976798,0.04009385,-0.02654513,-0.05437948,-0.01707138,-0.01395994,0.00774177,-0.10046577,0.00291344,0.07343804,-0.0344937,-0.09213551,-0.09282207,0.0705428,0.02088164,-0.02958211,-0.06667617,-0.07480983,-0.20338091,0.01566254,0.01136213,-0.00535851,-0.05940529,-0.0658842,-0.01889078,-0.03205182,0.01951469,-0.10469261,0.02917298,0.04534549,0.04590696,-0.00742228,-0.0321627,0.01315065,-0.02150331,0.00725861,-0.00326533,0.00654422,0.01272394,-0.0741689,0.02671243,0.00093981,0.02273049,0.05542258,0.0134496,0.02550177,0.10612143,0.02302025,0.00178211,0.04695443,0.05802019,-0.15370356,0.05389437,0.01316243,0.00854857,-0.00848861,-0.02428613,-0.0009721,0.03747885,0.04259312,0.03417551,-0.01942526,0.06264608,0.04037938,-0.03192331,-0.01842189,-0.06632482,-0.02995394,0.04904903,-0.06010672,-0.02673702,0.00622945,0.00555386,-0.06053911,0.06620181,0.01845975,0.03757223,0.09459031,-0.06632486,0.03635787,0.03787688,-0.00699887,0.04415506,0.06226432,-0.00493901,0.06407454,-0.01796301,-0.01033081,0.11785554,-0.00908841,-0.02844854,-0.02459021,0.02603088,0.06895179,-0.03124584,-0.03042183,-0.01202941,-0.05210024,0.00128639,0.06416308,-0.02772916,0.07900139,-0.03856416,-0.04863358,-0.00827929,-0.04135102,0.02070672,0.00663754,-0.04552459,-0.05364373,0.04879165,-0.01286029,-0.00767384,0.00007006,-0.01994988,0.03144814,0.05540838,0.00892771,0.00342499,0.05881933,-0.02932303,-0.1341593,-0.03083862,0.02461358,-0.01310184,-0.00344458,0.00206571,0.009048,-0.00725888,-0.0097418,-0.00007397,0.06217742,-0.13198479,-0.03340962,0.03305928,0.03843263,0.01566531,0.03843784,-0.03929874,0.01604846,0.01789795,-0.05440595,-0.07795922,0.0075051,-0.01473002,0.12615418,0.02437785,-0.0720029,0.00357876,-0.02371532,-0.00233585,-0.00969017,0.08571132,-0.03870183,-0.05950612,0.00144511,0.05779509,-0.05641688,-0.08323219,-0.01401971,0.01863798,-0.0580389,0.00327206,0.07947627,-0.02158937,-0.0992401,-0.02563892,-0.03666502,-0.00335613,0.01055648,-0.02885405,-0.02415749,-0.00701804,-0.01617668,-0.10649446,0.01676732,-0.04198445,0.02586849,0.02264659,0.00277268,0.01148788,-0.01812393,0.00754131,-0.00315628,-0.08988984,-0.02976363,-0.04350964,0.04436421,-0.00568474,-0.06557738,-0.00597104,0.0477155,0.03093784,-0.01301847,0.02654852,-0.01305361,-0.06646575,0.0988654,0.02041524,-0.05354274,-0.01524418,-0.00281737,0.07932737,-0.07457545,0.04891766,0.03885432,0.04131014,0.01333403,0.02708815,0.00188057,0.03386995,-0.05602693,-0.19776513,-0.07255909,-0.07342409,0.02771845,0.06168742,-0.00903593,-0.01127271,-0.01109274,0.00457674,0.06041665,0.11735248,-0.08470797,0.01447239,0.06066799,-0.01832132,-0.01616414,-0.10515831,-0.04791338,-0.05708428,0.0589117,-0.00900548,0.03872588,0.02290849,-0.08408494,0.01671585,-0.02904599,0.13144597,0.02762674,0.05270268,0.01867812,0.07215526,0.02383035,-0.01254195,-0.08109449,0.03671055,0.05504761,-0.027178,0.00308661,-0.0644058,0.02258001,-0.06323324,0.0132898,-0.00429137,-0.07791801,-0.03842942,0.01873069,-0.02943058,0.03749179,-0.01189346,0.06784695,0.06974234,0.00295171,0.02965433,0.02977727,0.07197584,0.00253272,-0.05498735,-0.01145726,0.00269961,0.03427728,0.04722497,-0.06657043,0.04527414,-0.0333423,0.07427038,0.03563634,0.00858984,-0.04378594,0.02767267,-0.01746003,0.0127895,0.09426872,-0.03363968,0.02850434,-0.00032305,0.05507833,0.10490894,-0.03860156,0.03234209,0.00004556,0.04183811,-0.03090688,0.03268487,0.07227212,0.01293565,0.00421969,0.04879624,0.05169524,0.00579587,0.0032316,0.00297643,0.0390819,-0.02693666,0.01174684,-0.00853618,0.0349813,-0.22596061,0.01617225,-0.05244882,0.04747461,-0.02782775,-0.0090585,0.04667505,0.00687743,0.01040249,-0.00981866,0.06325659,0.01921587,0.00749604,-0.08356483,0.02931187,-0.0021684,0.03168017,-0.00962004,0.03919262,0.0091639,0.0923543,0.07204466,0.2364451,-0.02413604,-0.01210637,0.03182414,0.01663492,0.00382907,0.01502832,0.02732866,0.00756709,0.02461245,0.06691624,-0.02966032,-0.03363487,-0.00876276,-0.0167072,0.01610788,-0.00201838,-0.02201531,-0.04136923,0.00824685,0.00360992,-0.00184386,0.11222728,0.02647698,-0.04221116,-0.09097233,0.04982033,0.06465574,-0.07587073,-0.08210007,-0.05421185,-0.08320501,0.00237033,0.02551937,0.01912985,0.00837968,0.04796586,0.00180364,0.01117237,0.02749267,0.07420385,0.02530378,-0.0113068],"last_embed":{"hash":"1r0r1hj","tokens":450}}},"text":null,"length":0,"last_read":{"hash":"1r0r1hj","at":1753423491789},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>","lines":[134,162],"size":4127,"outlinks":[{"title":"The lottery software generates statistical reports for frequency, pairing, skips or misses.","target":"https://saliu.com/images/lotto.gif","line":28}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06913897,-0.0246937,-0.0110784,-0.01017583,-0.05118405,0.01581721,0.06127575,-0.00730177,0.03635197,-0.0041993,0.01407362,-0.01137289,0.03909474,-0.02576005,-0.05784323,-0.01832132,-0.01840533,0.00947438,-0.10070888,0.00162125,0.0698953,-0.0374713,-0.08866404,-0.09277238,0.07222597,0.01812719,-0.0263737,-0.06505945,-0.0745765,-0.20210934,0.01206377,0.00864156,-0.01176552,-0.06140719,-0.06309082,-0.02208303,-0.03193457,0.02103538,-0.10281309,0.03202871,0.05005533,0.04492691,-0.00556444,-0.031861,0.01145161,-0.01726994,0.0053695,-0.00390763,0.00392001,0.01447435,-0.07695014,0.0271896,-0.00210994,0.02179486,0.05577561,0.01569542,0.02641518,0.10699984,0.0201105,-0.00032503,0.04630854,0.06111893,-0.15169883,0.04953163,0.01089086,0.00641891,-0.00854807,-0.0203446,-0.00499774,0.04073837,0.04288623,0.03301132,-0.01981931,0.06569088,0.04060445,-0.02955395,-0.02200737,-0.06973265,-0.02841746,0.0546119,-0.0609828,-0.0252902,0.00548737,0.00355111,-0.06275239,0.06766303,0.01745285,0.03582741,0.09142044,-0.0630885,0.03864056,0.03825726,-0.01561065,0.0461168,0.06091665,-0.00791251,0.06089372,-0.01542465,-0.00629168,0.11642462,-0.00979191,-0.02627919,-0.02540848,0.02402733,0.06739493,-0.03196052,-0.03030015,-0.00858831,-0.05140202,0.00331963,0.06537549,-0.03001565,0.07961262,-0.03956502,-0.04767191,-0.00224166,-0.03830271,0.02233755,0.00674135,-0.04729138,-0.05087359,0.05063614,-0.01391628,-0.00742687,-0.00105069,-0.0178235,0.03467461,0.05299257,0.01066036,0.00125219,0.05719836,-0.03057637,-0.1344521,-0.03285119,0.02382126,-0.01799749,-0.00068539,0.00547383,0.00901837,-0.01000555,-0.00854725,-0.00387683,0.06331198,-0.13084954,-0.03681535,0.03220461,0.03844273,0.01853668,0.03690042,-0.03740324,0.01494244,0.02224114,-0.05647855,-0.07884046,0.00489462,-0.01327796,0.12248254,0.02402472,-0.07178592,0.00180492,-0.02486872,-0.00261252,-0.0079548,0.08564715,-0.03970212,-0.05575768,-0.00222224,0.05736742,-0.05655424,-0.08470778,-0.01395918,0.01634398,-0.05985112,0.00128797,0.07961494,-0.01887317,-0.10105526,-0.02388918,-0.04017626,-0.00105058,0.00916422,-0.02772514,-0.02720392,-0.00522334,-0.01885213,-0.10538749,0.01398907,-0.0413842,0.02542928,0.01961708,0.00171274,0.01368215,-0.01966532,0.01207432,-0.00129108,-0.09191635,-0.02898774,-0.04270625,0.04486105,-0.00520844,-0.06748573,-0.00493862,0.04700664,0.02375562,-0.01088063,0.02291545,-0.0113835,-0.06486095,0.09971869,0.02017318,-0.05313928,-0.01495523,-0.00962693,0.07563601,-0.07414554,0.0480007,0.04148786,0.04065793,0.01392363,0.01990102,0.00663788,0.03395483,-0.05329037,-0.19856142,-0.07419214,-0.0754559,0.02875268,0.06044198,-0.0059185,-0.01376727,-0.00890192,0.00642177,0.05697422,0.11517914,-0.08298716,0.01104758,0.06502414,-0.01763331,-0.01695003,-0.10666749,-0.04818411,-0.05614349,0.05973603,-0.01059148,0.04356734,0.02603807,-0.08188737,0.01794366,-0.0276855,0.1344005,0.02961525,0.05365945,0.02201893,0.07030822,0.02567802,-0.01511137,-0.0827892,0.03827468,0.05419749,-0.02759105,0.00106646,-0.06874822,0.02061628,-0.05889719,0.01194132,-0.00431234,-0.07675403,-0.04027597,0.01883652,-0.02699055,0.03916089,-0.01313534,0.07138199,0.07200605,0.00636868,0.03235614,0.0293764,0.07041895,0.00182118,-0.05346411,-0.01686256,0.00317396,0.03348003,0.04682758,-0.062337,0.04168774,-0.03227243,0.07073696,0.0343001,0.0094508,-0.04251577,0.03160612,-0.01958016,0.01543487,0.0917663,-0.03294937,0.02750757,0.0057613,0.0538708,0.10844171,-0.04079409,0.0323828,0.00247734,0.04489134,-0.02894773,0.03319756,0.07120118,0.01266309,0.00380665,0.05146784,0.05422648,0.00414193,0.00255646,0.00243017,0.04093047,-0.02279152,0.01222716,-0.01119179,0.03303651,-0.22684437,0.01568764,-0.05372163,0.04577124,-0.02912134,-0.01148506,0.04543712,0.00834406,0.0050786,-0.00430958,0.06362485,0.02154441,0.00637964,-0.08542918,0.02758664,0.00168189,0.02662501,-0.01257577,0.04125845,0.00850672,0.09521019,0.07238299,0.23844738,-0.0240074,-0.01098194,0.03161526,0.01412607,0.00262177,0.01294861,0.02601171,0.01142341,0.02107826,0.06293184,-0.03115076,-0.03259264,-0.00436087,-0.0155969,0.01856058,-0.00295094,-0.02510774,-0.04106412,0.00762223,0.00639886,-0.00151849,0.1133888,0.02554796,-0.0419726,-0.08970713,0.04848037,0.06447413,-0.07161803,-0.08252614,-0.05364625,-0.08606386,0.00244798,0.02359809,0.02041711,0.00928789,0.0494542,0.00446792,0.00714762,0.03112658,0.07345577,0.02525872,-0.01034553],"last_embed":{"hash":"19jp32b","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"19jp32b","at":1753423491952},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>#{1}","lines":[136,145],"size":1809,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.1.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06893309,0.00517368,0.00469811,-0.04030461,-0.02251331,0.03969154,0.04118735,-0.04152215,0.00639402,0.00644707,0.03308854,-0.01075303,0.04031989,-0.02593331,-0.05051641,-0.04948929,0.01676717,-0.00042095,-0.0936741,-0.00849444,0.03793386,-0.03570519,-0.0549929,-0.09773327,0.06044789,0.04036245,-0.01890751,-0.05228511,-0.06655242,-0.21929806,0.03056039,0.02483393,0.01253724,-0.07015941,-0.01795031,-0.04512915,-0.02078803,0.06954663,-0.07379179,0.00032725,0.02798075,0.02225084,0.00810767,-0.0411544,-0.00468488,-0.04505745,-0.01524036,-0.00696672,0.05478595,0.02273881,-0.02936798,-0.03453163,0.03570329,0.00191284,0.05072519,-0.0075953,0.0610003,0.07425163,0.00803616,0.02531603,0.052403,0.12252999,-0.17913653,0.02286435,-0.00472121,-0.00378502,-0.01948755,-0.00307102,-0.00269132,0.06498869,-0.0015884,-0.00513895,-0.05188442,0.04930421,0.00825442,-0.02190553,-0.04595872,-0.08830506,-0.06620575,0.04596452,-0.0834355,0.01988812,-0.01055301,0.03248984,-0.03327219,0.05967573,0.035198,0.07735636,0.0561896,-0.0305918,0.04848741,-0.00248873,0.00495747,0.05566491,0.01382955,0.00079961,0.08388621,-0.02415418,0.04228717,0.1293233,-0.01690297,-0.01797397,0.01792701,-0.0153182,0.02142822,-0.07987702,-0.02289684,0.0096554,-0.05326946,0.01367891,0.05713885,-0.00636344,0.0706815,-0.05201652,-0.07490812,0.0320219,-0.03800521,0.02131514,-0.00727049,0.00023492,0.01058591,-0.00045222,-0.02336623,0.01328376,0.0274791,-0.02238958,0.04470393,0.06502638,0.03526736,0.01516057,0.01700083,0.03917887,-0.12471151,-0.05458483,-0.00702933,-0.01265269,0.04136008,-0.00745475,-0.00364097,-0.01893521,-0.02212706,-0.03424801,0.08912915,-0.09582955,0.01675645,0.07613151,-0.02490486,0.00944192,0.02679228,-0.03609704,-0.00268129,0.03294721,-0.05660195,-0.02711642,0.02712446,-0.00568258,0.06314107,0.02522972,-0.08448701,-0.01302031,-0.01257326,-0.02124037,-0.00872442,0.09170976,-0.02756153,-0.08432387,-0.00796545,0.03848666,-0.04260783,-0.11150724,-0.01306809,0.00830155,-0.04376604,-0.02289019,0.06546944,-0.05017515,-0.0725839,-0.0341945,-0.01593988,-0.00652859,0.02268962,0.01973227,-0.03742416,0.02842608,-0.0102692,-0.08856902,0.02077895,-0.05032989,0.03833272,0.09545703,-0.0084221,-0.01961908,0.01575014,0.02485135,0.00908443,-0.0486476,-0.0415923,-0.04425005,0.05838911,-0.04053844,0.03742431,-0.00806817,0.02374046,0.00437012,0.02756035,0.03511668,-0.04285623,-0.05120704,0.12476882,0.01160072,-0.05905629,-0.00910246,0.01574341,0.07900921,-0.06408981,0.04105839,0.03476028,0.02815138,-0.00529671,0.03434674,0.00903111,-0.01467212,0.00499652,-0.19698195,-0.04587784,0.00322789,0.05471569,0.04722855,-0.03403031,-0.00974186,-0.01375184,0.00383138,0.08053878,0.11477943,-0.02935254,-0.02957683,0.06726474,-0.01911263,-0.01123322,-0.08794431,-0.04911525,-0.02750683,0.09087835,-0.02069054,0.00458666,-0.02178319,-0.04381612,0.05376561,-0.0422238,0.1128725,0.0171365,0.01123796,-0.01505333,0.05038082,0.07147625,-0.01766499,-0.01158861,0.07193857,0.03753417,-0.03040655,0.00807341,-0.03715121,-0.01152739,-0.07143334,-0.00703972,-0.01261223,-0.09950159,0.01947268,-0.00228399,-0.03343465,0.0077911,-0.01150985,0.04043373,0.02852059,0.0266841,0.02774053,0.0162005,0.09086017,-0.02087924,-0.0832094,0.00094412,0.02007946,0.01519944,0.0568576,-0.08009592,0.02755544,-0.01287947,0.05390657,0.02639814,-0.01390938,-0.00759728,0.02834079,0.01291354,0.00571088,0.05018998,-0.01426794,0.04371759,-0.00472675,0.05210583,0.09593552,-0.05727249,0.01745578,-0.02571417,0.02885232,-0.01270634,0.00926531,0.08654489,0.01202605,0.01807395,0.06900446,0.06912322,0.03587337,-0.00854418,-0.01251749,0.00350074,0.01964052,0.01025115,-0.03073518,0.04048068,-0.25256097,0.00808474,-0.02981496,0.06684893,-0.05703039,-0.00654411,0.0384397,0.0128654,0.01407121,-0.00790577,0.06902136,0.0233185,-0.00186463,-0.14330833,0.01165052,-0.01296911,-0.00101003,-0.02921628,0.04925745,-0.01428361,0.09794796,0.03288911,0.22529881,-0.02093248,0.0063465,0.02591431,0.00549086,-0.00292968,0.02530184,0.02046498,0.02284949,-0.0248628,0.07324708,-0.01245526,-0.02175005,0.06251924,0.00867348,0.00940999,-0.00444536,0.01333093,-0.06633195,-0.02604114,-0.08035515,-0.02056132,0.09959497,0.03297026,-0.0302805,-0.08543614,-0.00329639,0.06232984,-0.06867062,-0.05982004,-0.02924995,-0.09680682,0.01702915,0.03342924,0.02161189,-0.01461018,0.02758636,0.02688018,0.01193583,0.00064139,0.1036901,-0.0004415,-0.04727647],"last_embed":{"hash":"kgpnwa","tokens":270}}},"text":null,"length":0,"last_read":{"hash":"kgpnwa","at":1753423492108},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.1.","lines":[146,151],"size":932,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.1.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06637648,0.0040413,0.00583605,-0.03813416,-0.0256839,0.04135713,0.03988393,-0.04025258,0.00777709,0.00408531,0.03431436,-0.00730964,0.03956158,-0.02519239,-0.05124185,-0.0507226,0.01608921,0.00050482,-0.09411394,-0.0095473,0.04088781,-0.03721553,-0.05453369,-0.09957029,0.06026755,0.03947636,-0.02050829,-0.05037149,-0.06567248,-0.21925581,0.02784557,0.02780197,0.0120984,-0.07045289,-0.01945921,-0.04552408,-0.01949494,0.07041952,-0.07540625,0.00314656,0.02879148,0.02066284,0.00887147,-0.0414054,-0.00541305,-0.04378877,-0.01726643,-0.00652695,0.05510443,0.02311143,-0.02927929,-0.0344255,0.03142343,0.00225264,0.04890936,-0.00826072,0.06440041,0.07674998,0.00943618,0.02555447,0.05199585,0.12345213,-0.1786377,0.02454453,-0.00559259,-0.00467408,-0.01732626,-0.00108154,-0.00113956,0.06741544,-0.00257431,-0.00607378,-0.0523844,0.05017006,0.00951897,-0.02291951,-0.04846038,-0.08870479,-0.0639538,0.0466999,-0.08368083,0.01881527,-0.01155832,0.0330663,-0.03540947,0.05797173,0.03566341,0.07478639,0.05425126,-0.02877973,0.04648252,-0.00466001,0.00360346,0.05636048,0.01299569,-0.00138581,0.08599846,-0.02404081,0.04243626,0.12784339,-0.01760544,-0.01799379,0.02013578,-0.01361384,0.02004343,-0.08056851,-0.02030916,0.00748337,-0.05275596,0.01197951,0.05686979,-0.00671255,0.06940123,-0.05424916,-0.07482799,0.03229522,-0.03837758,0.02023591,-0.00798747,-0.00051489,0.01101141,-0.0011558,-0.02358818,0.01388365,0.02949996,-0.02169488,0.04562189,0.0659812,0.03691887,0.01776805,0.0158435,0.03965478,-0.12370917,-0.0558802,-0.00542167,-0.01207046,0.04405432,-0.00703986,-0.00488591,-0.02072436,-0.01982105,-0.03110787,0.09069881,-0.09610897,0.01885096,0.07659163,-0.02424379,0.00996524,0.02814665,-0.03600578,-0.00396549,0.03243894,-0.05781175,-0.02656485,0.0257139,-0.00415732,0.06210021,0.02375971,-0.08253561,-0.01289531,-0.01561974,-0.01896774,-0.00854882,0.09046949,-0.02966207,-0.0841841,-0.00927793,0.03775384,-0.04252772,-0.11263522,-0.0135048,0.01021844,-0.04202525,-0.02279297,0.06636085,-0.04764665,-0.07205845,-0.03611424,-0.01707439,-0.00576379,0.02242487,0.01972141,-0.03681801,0.02728151,-0.01133996,-0.08691663,0.01919374,-0.05100118,0.0377534,0.09796508,-0.008474,-0.01730449,0.01512598,0.02556038,0.00822465,-0.04923133,-0.04379244,-0.04363304,0.05969876,-0.03986821,0.03606533,-0.00729647,0.02376744,0.00287013,0.02878476,0.0347223,-0.04292399,-0.05192946,0.12344691,0.01153908,-0.05920001,-0.00922108,0.01648236,0.07993846,-0.06452546,0.04167453,0.03485943,0.0287551,-0.00645743,0.03161214,0.01021791,-0.01805883,0.00610749,-0.19726267,-0.04686031,0.0045754,0.05402182,0.04852292,-0.03522751,-0.00899867,-0.01397243,0.00092425,0.0820216,0.11407173,-0.03101278,-0.03031568,0.06823015,-0.01872981,-0.01110399,-0.08748949,-0.04816296,-0.02569336,0.09079199,-0.02070155,0.00381571,-0.02066138,-0.04410724,0.05437018,-0.03943774,0.11283513,0.01479817,0.011859,-0.01426181,0.05034849,0.07139731,-0.01841351,-0.01048662,0.07117198,0.03928475,-0.03049731,0.00501175,-0.03791745,-0.01238225,-0.06831072,-0.00852871,-0.01256847,-0.09829925,0.01610187,0.0000821,-0.03588546,0.00708014,-0.00992004,0.03904568,0.02791401,0.02564973,0.02851652,0.01581987,0.09049409,-0.02297072,-0.08192378,0.00180948,0.02005067,0.01517937,0.05703597,-0.08133698,0.02593056,-0.01526205,0.05595329,0.0268742,-0.01536484,-0.00728233,0.03041103,0.01306448,0.00478702,0.05021529,-0.01478603,0.04308926,-0.00300174,0.05234265,0.09598228,-0.05551391,0.0162895,-0.02602575,0.02964289,-0.01242305,0.00825194,0.08577727,0.00937876,0.01706056,0.07119537,0.06778026,0.03540158,-0.00796014,-0.01295154,0.00367201,0.02072551,0.00915962,-0.02951224,0.0376207,-0.25372535,0.00942038,-0.02990445,0.06659319,-0.05813285,-0.0065344,0.03777055,0.01377711,0.01565088,-0.00786033,0.06713735,0.02264017,-0.00012343,-0.14121187,0.01102463,-0.01258583,-0.00243317,-0.0298169,0.04848777,-0.0133738,0.09859322,0.03362136,0.22561894,-0.02113477,0.00608705,0.02392625,0.00632345,-0.00508824,0.02222891,0.01984891,0.02122425,-0.02606929,0.07325135,-0.01244062,-0.02170246,0.06379265,0.01071372,0.00893697,-0.00488823,0.01512786,-0.06661432,-0.02433794,-0.07791439,-0.01860024,0.09970346,0.03478393,-0.02956042,-0.08659313,-0.00433961,0.06233956,-0.06764714,-0.05941215,-0.03009689,-0.0975318,0.01640367,0.03391266,0.02379636,-0.01313009,0.02723453,0.02883576,0.01180586,0.00222326,0.10243172,-0.0011639,-0.04656128],"last_embed":{"hash":"1nvvfjz","tokens":267}}},"text":null,"length":0,"last_read":{"hash":"1nvvfjz","at":1753423492189},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.1.#{1}","lines":[148,151],"size":921,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.2.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07394652,-0.02340092,-0.00914718,-0.04483885,-0.02972783,0.04455289,0.02960118,-0.03110987,0.02437438,-0.01152537,0.03212132,-0.01292998,0.05953717,-0.02992257,-0.05024273,-0.04893331,0.01766645,-0.01490383,-0.08659045,-0.00408614,0.05070629,-0.04274491,-0.03634007,-0.09470903,0.04905507,0.01339713,-0.0239329,-0.04420371,-0.07549136,-0.21461517,0.01947627,0.02075117,-0.00202894,-0.08223011,-0.0620757,-0.05096965,-0.02113112,0.04006391,-0.08007492,0.02802145,0.01218176,0.02731034,-0.00615192,-0.05866181,0.01887848,-0.0332716,0.00924496,0.00280081,0.07961161,0.02933758,-0.05383864,-0.00511762,0.03817848,0.02231849,0.06503881,-0.00839498,0.07683995,0.06887283,0.02144214,0.0318909,0.03320758,0.08242702,-0.16147353,0.03574425,-0.00800855,0.0282593,0.00573697,-0.00478594,0.01265316,0.04829397,0.0003023,0.02627298,-0.05176873,0.05467615,0.02235075,-0.00352946,-0.04623676,-0.0567655,-0.06019004,0.041348,-0.06374694,-0.0179206,-0.01181549,-0.00003353,-0.00726702,0.04772136,0.04767852,0.07430963,0.04737382,-0.049203,0.05325996,0.00781578,0.0061899,0.06183567,0.02893437,0.02401952,0.08155596,-0.04941672,0.00185038,0.1242413,0.01931559,-0.02405713,0.00687368,0.0192467,0.01502516,-0.06454613,-0.02878513,-0.01525136,-0.04110893,0.00250691,0.04857212,-0.01182237,0.0754125,-0.04407777,-0.0673731,0.013208,-0.041017,0.00786609,-0.0017472,0.01488413,-0.0167269,-0.00387189,0.00817042,0.01141448,0.01449243,-0.00794108,0.02197541,0.06097928,0.05260612,0.05109753,0.03729162,-0.00239193,-0.16068777,-0.04950415,0.00321455,-0.0112321,0.01651672,-0.01215343,-0.0194114,-0.02856371,-0.00571336,-0.04807993,0.07178794,-0.10071076,0.0125042,0.07797422,-0.02104666,-0.01594158,0.03478273,-0.02770524,-0.01268589,-0.00667909,-0.07315902,-0.04909841,-0.00386932,0.00732633,0.11078891,0.06507525,-0.08442984,-0.00819902,-0.03577521,-0.00687216,-0.03059896,0.13017531,0.0080182,-0.05997658,-0.01430169,0.01558818,-0.06039416,-0.09331141,-0.03670929,0.04038385,-0.05281533,0.02842676,0.09176181,-0.03370439,-0.07012261,-0.06454625,-0.03991158,-0.02095161,0.01786932,0.01188852,0.00089191,-0.01474496,-0.01005927,-0.08983911,0.008107,-0.03697558,0.02730541,0.07505035,-0.04036159,-0.01019493,-0.03162449,-0.00663221,-0.00575785,-0.04960418,-0.05177566,-0.05290024,0.05963713,-0.03568881,0.00770823,-0.00001285,0.04750289,0.0395069,0.00907799,0.04968239,-0.02413601,-0.03472399,0.09950284,-0.00654704,-0.03930236,-0.05169591,0.05579181,0.07420067,-0.04654168,0.0511319,0.0127783,0.01167952,0.01294809,0.02692386,0.01052316,0.03014273,-0.02786163,-0.20189755,-0.063755,-0.03564457,0.03969402,0.0189525,-0.05693816,0.02666311,-0.02475852,0.0158709,0.08468487,0.110607,-0.07350288,-0.02714813,0.07397682,-0.03854529,-0.00092894,-0.08676817,-0.03844177,-0.02917764,0.09403687,-0.01485459,0.01305169,-0.01349323,-0.03571605,0.06114195,-0.05079812,0.13076894,0.02822022,-0.01721407,-0.02441646,0.07383794,0.02720623,-0.00006112,-0.00982491,0.02345608,0.03777506,-0.02678539,0.0120149,-0.03922527,0.02037339,-0.06288582,0.00539689,-0.00715644,-0.09034204,-0.00771836,0.01937917,-0.01077888,0.02058008,-0.01838746,0.00280601,0.04188814,-0.01136572,0.03350336,0.0116073,0.05928138,-0.01988589,-0.06450121,-0.00036399,-0.00744723,0.01448581,0.03266034,-0.06484687,0.04232471,-0.03500137,0.0500017,0.03161917,-0.02093977,0.0062034,0.04986152,0.0242898,0.02893833,0.05915705,0.01620883,0.05600917,-0.0335632,0.05960196,0.05035949,-0.05271498,0.01838844,-0.01170427,0.01530681,-0.02587463,0.02713637,0.08209389,0.05705269,0.01558193,0.0643782,0.06678619,0.00815897,0.00412424,-0.01864223,0.00927455,-0.02047739,0.0441989,-0.01057718,0.03976512,-0.24083194,0.03811004,-0.04756694,0.05335866,-0.01588784,-0.01667765,0.01165673,0.00158063,0.00781799,-0.00614752,0.08951887,0.01460527,0.0268303,-0.11153125,0.00001763,-0.0063665,0.01054251,-0.01269511,0.05058834,0.00452974,0.09863272,0.03202527,0.24799329,-0.01848808,0.00533751,0.03449662,0.00922071,-0.0198807,0.03169905,0.04833358,0.00798401,0.00495144,0.09970453,-0.01088791,-0.02524976,0.00856869,-0.01172551,0.01249303,-0.00929926,0.00495793,-0.0649382,-0.02431551,-0.04113135,-0.02570661,0.09321339,0.0338647,-0.02248845,-0.08792745,0.01923566,0.09034331,-0.07944529,-0.06045675,-0.05856836,-0.04840517,0.02907826,0.03724798,-0.02006833,-0.01047214,0.02104387,0.0212987,0.0005398,-0.01066387,0.11141265,-0.0166809,-0.02926977],"last_embed":{"hash":"ve3r7z","tokens":355}}},"text":null,"length":0,"last_read":{"hash":"ve3r7z","at":1753423492268},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.2.","lines":[152,162],"size":1308,"outlinks":[{"title":"The lottery software generates statistical reports for frequency, pairing, skips or misses.","target":"https://saliu.com/images/lotto.gif","line":10}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.2.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07244772,-0.02348667,-0.00531336,-0.04504323,-0.02961044,0.04219521,0.02862734,-0.02918698,0.02313254,-0.01242225,0.03141151,-0.00988166,0.06067166,-0.03064332,-0.05039826,-0.04968428,0.01801003,-0.01622523,-0.08760791,-0.00528826,0.05064684,-0.04343926,-0.03350381,-0.09457841,0.04671702,0.01593811,-0.02569197,-0.04273453,-0.07583258,-0.21364745,0.02075232,0.01910649,-0.00374746,-0.07897862,-0.06112266,-0.05457428,-0.02166385,0.04258299,-0.08116685,0.02994953,0.0122083,0.02852897,-0.00603709,-0.05957014,0.02045701,-0.03325363,0.01013193,0.00539799,0.07704385,0.03186252,-0.05217008,-0.0052567,0.03762951,0.02500904,0.06472413,-0.01069962,0.07560835,0.07058883,0.02488156,0.03276605,0.03359724,0.08292492,-0.15985782,0.03578904,-0.00924519,0.02950368,0.00614597,-0.0043828,0.01406473,0.04804695,-0.00003923,0.0263306,-0.05064369,0.05463188,0.02156587,-0.0034154,-0.04497068,-0.0583582,-0.06148812,0.03987845,-0.06344473,-0.01946583,-0.01074796,0.00100084,-0.00953622,0.04380846,0.04724995,0.07108846,0.04711133,-0.04578302,0.05660076,0.00872225,0.00550673,0.05991485,0.02800647,0.02400503,0.07806334,-0.04999986,0.00278164,0.12355705,0.01940198,-0.02259165,0.00892407,0.01916017,0.0115279,-0.06584749,-0.03004958,-0.01716156,-0.04385243,0.00101491,0.04774732,-0.01189834,0.0755363,-0.04164282,-0.06575004,0.01062981,-0.03992676,0.00650407,0.00005776,0.01742101,-0.01532148,-0.00299345,0.01027803,0.01148236,0.01499766,-0.00500011,0.01950745,0.06070058,0.0539528,0.0527522,0.03530764,-0.00219783,-0.16146572,-0.04795666,0.00570654,-0.01275721,0.02050769,-0.00950398,-0.01736179,-0.02988409,-0.00270345,-0.04662494,0.0711249,-0.10019685,0.01231466,0.07894969,-0.0232573,-0.01746712,0.03633013,-0.0259228,-0.01223859,-0.00675614,-0.07518447,-0.04935434,-0.00391975,0.00922517,0.10955029,0.06434654,-0.08553556,-0.00646924,-0.04034378,-0.00181309,-0.02856345,0.1293669,0.00811501,-0.05746954,-0.01410456,0.01476091,-0.06185195,-0.09314843,-0.03914307,0.04296298,-0.05774854,0.02924429,0.08956083,-0.0327271,-0.07351653,-0.06545816,-0.03776932,-0.02045504,0.02102527,0.01134164,0.0008243,-0.01715785,-0.00928757,-0.08933538,0.00467437,-0.03394654,0.02597723,0.07750316,-0.04152231,-0.00909643,-0.03425423,-0.0070354,-0.00300915,-0.05179187,-0.05285911,-0.05109881,0.0593536,-0.03769632,0.00499288,-0.00167242,0.04896159,0.04177155,0.00781191,0.04923913,-0.0228793,-0.03615363,0.10082931,-0.00759195,-0.04011089,-0.05812005,0.05469603,0.07342409,-0.04762225,0.05380621,0.01253289,0.01202428,0.01020061,0.0245135,0.00852726,0.02990689,-0.02771271,-0.20146634,-0.06556991,-0.03351747,0.04107644,0.01961237,-0.05903733,0.02488934,-0.02482761,0.01575457,0.08523935,0.10642426,-0.07471935,-0.02836178,0.07314748,-0.03951698,-0.00129722,-0.08653944,-0.03651061,-0.02886499,0.09371434,-0.01376342,0.01118869,-0.01340699,-0.03495373,0.06260827,-0.04910599,0.1290713,0.02663424,-0.01920689,-0.02277121,0.07487442,0.02497072,0.00112779,-0.00513665,0.02573393,0.03626503,-0.02685184,0.00816581,-0.03856343,0.02267123,-0.06206395,0.00440753,-0.00861804,-0.09091708,-0.01083035,0.02178952,-0.01049676,0.01950583,-0.01945469,0.00194102,0.03790363,-0.01150432,0.03555558,0.00925078,0.05898543,-0.02038259,-0.06202748,0.00111142,-0.00802176,0.01468836,0.03329696,-0.065346,0.03829993,-0.03603416,0.05226236,0.03089082,-0.02210847,0.01034809,0.05075273,0.0247322,0.02963694,0.05937775,0.0153395,0.05484209,-0.03127195,0.06108907,0.04973795,-0.05418476,0.0192531,-0.01216589,0.01748827,-0.02335634,0.02642379,0.08267041,0.05458033,0.01349974,0.06123821,0.06601656,0.00711104,0.00305561,-0.01978341,0.00915475,-0.02145958,0.04529431,-0.00899543,0.04148719,-0.23911384,0.03837369,-0.04868439,0.05222585,-0.01616933,-0.01622917,0.01259491,0.00824585,0.00731745,-0.00447623,0.08936183,0.01408671,0.02907786,-0.11267769,-0.00016787,-0.00630753,0.00993754,-0.01377229,0.05109824,0.00395558,0.10053334,0.0299705,0.24913163,-0.01853199,0.00559965,0.03467336,0.00808285,-0.0197193,0.03111992,0.04709461,0.00680222,0.00601567,0.10213402,-0.00925455,-0.02611655,0.00718089,-0.01196528,0.01288662,-0.01102641,0.00606298,-0.06421799,-0.02485496,-0.04077087,-0.02812236,0.09279346,0.03641402,-0.02204541,-0.08947261,0.01924511,0.08973083,-0.0809121,-0.05818802,-0.05892426,-0.05030162,0.02920941,0.0402423,-0.01985818,-0.00822706,0.02055476,0.02326999,-0.00196114,-0.01216839,0.11245918,-0.01714721,-0.0293114],"last_embed":{"hash":"adarap","tokens":352}}},"text":null,"length":0,"last_read":{"hash":"adarap","at":1753423492380},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>##5.2.#{1}","lines":[154,162],"size":1297,"outlinks":[{"title":"The lottery software generates statistical reports for frequency, pairing, skips or misses.","target":"https://saliu.com/images/lotto.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>6. Statistical Reporting: Frequency, Skips, Pairs</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06836074,-0.02579769,-0.02824167,-0.01902274,-0.05531773,0.07266283,0.05720026,0.00417176,0.09451041,0.01090111,0.01880377,0.0096764,0.06672063,0.0025985,-0.04672766,0.01096705,0.01016996,0.00782956,-0.06780202,-0.00477724,0.08913018,-0.0679043,-0.04419728,-0.10325961,0.07506837,0.00991784,-0.04097487,-0.06698576,-0.05629489,-0.24173091,0.02497561,0.01961419,0.02232746,-0.05863133,-0.10043384,-0.0602613,-0.02270781,0.0604702,-0.04263149,0.01736699,0.01568475,0.01629826,0.00224707,-0.04383456,-0.00312941,-0.03748477,-0.00556034,-0.01490948,0.03463705,0.01653904,-0.08799223,-0.01968803,0.01577603,0.03692347,0.06522421,0.04561112,0.06687246,0.03137438,0.02771456,0.07607789,0.04363862,0.02307611,-0.15818599,0.04457852,0.00253302,0.00299181,-0.01104933,-0.02702503,0.00565217,0.036251,0.02156668,0.02902081,-0.01138586,0.05764413,0.0190764,-0.04811197,-0.06535967,-0.0307463,-0.04912818,0.01959302,-0.07956871,-0.02580078,0.01195656,-0.01714446,0.02416471,0.0284199,0.03617273,0.04580296,0.06544176,-0.05412084,0.03560061,0.02330682,0.01965511,0.00812762,0.02014783,0.04110042,0.06532191,-0.07511909,-0.01781241,0.13261464,0.00000914,-0.00206365,-0.01487125,0.06518349,0.03468358,-0.08600175,-0.02975203,-0.04699092,-0.05345321,0.02098484,0.0469646,-0.02234343,0.05286158,-0.06141691,-0.05539285,-0.01600244,-0.00977876,0.01929524,0.05769153,0.0225988,-0.04981131,0.00729611,-0.00238168,-0.00427125,0.02763362,0.02958479,0.029724,0.08569314,0.03915529,0.0381589,0.05092864,-0.02621119,-0.12069423,-0.01700748,-0.03402082,0.00419563,0.01873769,-0.00561841,-0.00053454,0.02380351,0.02277969,-0.002314,0.04876776,-0.10399304,0.00848222,0.0861007,-0.02140575,0.00568703,0.03388415,-0.02847606,-0.01766261,-0.00660002,-0.06154896,-0.0577651,-0.0219293,0.02165844,0.06038697,0.07692256,-0.02755973,0.03784467,-0.06270228,-0.04176293,-0.0112191,0.09546825,0.00828753,-0.08428028,-0.00692476,-0.00646352,-0.07158063,-0.05825785,-0.00098352,0.03144621,-0.0447469,0.01603105,0.08072454,-0.00512387,-0.07192437,-0.06048373,-0.00106661,-0.02476748,0.01940436,0.00346207,-0.0474021,-0.02319719,-0.01715178,-0.08093719,0.00926492,-0.02845848,0.01638643,0.05227013,-0.01111525,-0.03734574,0.00756397,0.00914683,-0.01988079,-0.05675152,-0.02458398,-0.06018071,0.03610316,-0.03350726,0.000519,-0.03458611,0.04455344,0.03247896,-0.01121161,0.05364722,-0.02217315,-0.03398532,0.09283872,-0.00009492,-0.03259537,-0.04533918,0.02760914,0.07390767,-0.04688783,0.01962298,0.00359988,0.01798548,0.02405317,0.03559589,-0.01210037,0.02227278,-0.0521154,-0.20848879,-0.04240588,-0.00173755,0.04319736,0.04252932,-0.04627828,0.01239081,0.00145209,0.0505272,0.11049039,0.08114032,-0.06474833,-0.0407152,0.02063962,-0.03273373,-0.00680529,-0.07637928,-0.03956886,-0.04362938,0.05944838,-0.02373303,0.03497892,-0.02673589,-0.05048396,0.05865457,-0.03515617,0.13399915,0.00363416,-0.04630702,-0.05792135,0.04435967,0.0111735,-0.01877776,0.02635397,0.020884,0.04290451,-0.01318138,0.00744379,-0.06348222,0.02643929,-0.09762816,0.03312,0.01876148,-0.08298413,-0.03444569,0.02264787,0.01937918,-0.02678503,-0.02733033,0.02416381,0.05469945,-0.01635881,0.05665178,0.02131896,0.06865223,-0.02226933,-0.0507315,0.00487535,-0.01400049,0.0267548,0.00125681,-0.06484967,0.05959618,-0.05077477,0.02299684,0.03224625,0.00401331,-0.01842929,0.01910322,0.0052131,0.00980129,0.09562207,-0.00053672,0.02549337,-0.0503439,0.05072221,0.04613183,-0.05255993,0.01372348,0.01455439,0.04025141,-0.0327178,0.02880684,0.06046109,0.04462897,-0.0138425,0.09501824,0.06994495,0.03024825,0.01436878,-0.01002942,0.00242425,-0.00060773,-0.01394623,0.0115873,0.01231254,-0.26119518,0.01321464,-0.05749348,0.06406912,-0.01984534,-0.02640694,0.01758428,0.01456286,0.03465467,-0.02575222,0.0863082,0.02947081,0.02525604,-0.09523129,-0.00369122,0.00263066,0.0182539,0.00457311,0.01853829,-0.01432853,0.08490593,0.01189774,0.24595909,-0.02562981,0.01170165,0.0211883,0.02103898,0.01068757,-0.01422938,0.04093739,-0.01408458,0.02046826,0.11180995,-0.01670994,-0.03729085,0.04119021,-0.01379826,0.02423412,-0.01986606,-0.00509025,-0.03960095,-0.01162345,-0.04410263,0.00934368,0.11619221,0.01365977,-0.02902091,-0.10229295,0.04476612,0.08549963,-0.09224954,-0.0617605,-0.05456145,-0.00743697,0.02833912,0.03382406,0.00378633,0.02017793,0.01205405,0.01991003,-0.00381431,0.00680207,0.04910731,0.00052372,-0.00130255],"last_embed":{"hash":"t1dec4","tokens":442}}},"text":null,"length":0,"last_read":{"hash":"t1dec4","at":1753423492473},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>6. Statistical Reporting: Frequency, Skips, Pairs</u>","lines":[163,187],"size":1860,"outlinks":[{"title":"Odds and random numbers are generated by these lotto programs.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>6. Statistical Reporting: Frequency, Skips, Pairs</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06329288,-0.02660916,-0.02922455,-0.01975032,-0.0539303,0.07819483,0.05655797,0.00541801,0.0991695,0.01325795,0.01891802,0.00998537,0.06568789,0.00214194,-0.04615121,0.01198995,0.00994896,0.00562847,-0.06623084,-0.00404084,0.08989114,-0.06918018,-0.04133967,-0.10303426,0.07711663,0.00640603,-0.04109067,-0.06518632,-0.05551125,-0.24005221,0.02392289,0.01833398,0.02227778,-0.06043528,-0.09741933,-0.06297861,-0.02409888,0.05857128,-0.03844903,0.01872434,0.01624917,0.01533752,0.00371157,-0.04073948,-0.00270216,-0.03584674,-0.00602247,-0.01785458,0.03395775,0.01586479,-0.09031971,-0.01744757,0.01272249,0.03478605,0.06486905,0.04511643,0.06762084,0.03112542,0.02686963,0.07816614,0.04334523,0.02254071,-0.16083115,0.04226393,0.00188416,0.00221803,-0.01133365,-0.02599841,0.00306866,0.03824391,0.02084032,0.02820699,-0.01197219,0.0599248,0.02162003,-0.04822135,-0.06699568,-0.03151456,-0.04709245,0.02078392,-0.08140308,-0.02522567,0.01233551,-0.01909144,0.02474898,0.02634564,0.03569086,0.04311377,0.06585518,-0.05199445,0.03670793,0.02587696,0.01678662,0.00858179,0.01756889,0.04008348,0.06274576,-0.07611001,-0.01473457,0.13051628,0.0029871,0.000688,-0.01089228,0.06342202,0.03345634,-0.08729609,-0.03130091,-0.04844007,-0.05080768,0.02163001,0.04717349,-0.02142902,0.05417241,-0.06256747,-0.05276112,-0.01542839,-0.0076607,0.01948303,0.05682196,0.02154116,-0.04977814,0.00779182,-0.00211065,-0.00304976,0.02797343,0.03182079,0.03109843,0.08625559,0.04338821,0.03708063,0.04914669,-0.02939321,-0.12033421,-0.01770098,-0.03880672,0.00083514,0.01997881,-0.00755041,0.0012669,0.0232048,0.02356373,-0.00608653,0.04471885,-0.10477742,0.00535748,0.08737332,-0.0196235,0.00682229,0.0321112,-0.02584431,-0.01821921,-0.00629005,-0.06249258,-0.05719243,-0.02195145,0.02301408,0.05674706,0.07973474,-0.02654022,0.03748562,-0.06430852,-0.04361768,-0.00809168,0.09698393,0.00760812,-0.08279445,-0.00729263,-0.00737922,-0.07030465,-0.05996768,-0.00026396,0.03008769,-0.04259125,0.01363664,0.08165864,-0.00360713,-0.06859864,-0.05874578,-0.00218864,-0.02361827,0.01704049,0.00238013,-0.04810619,-0.02081194,-0.01840547,-0.0807164,0.01012362,-0.02602062,0.01556719,0.05159725,-0.01379426,-0.03734641,0.00787929,0.01155146,-0.02141261,-0.05588359,-0.02156595,-0.06151592,0.03803221,-0.03741934,0.00109009,-0.03394362,0.04530937,0.0289667,-0.01101927,0.05476083,-0.01981473,-0.03386975,0.09403861,-0.0006306,-0.02991731,-0.04599819,0.02899302,0.07175232,-0.0460337,0.01981567,0.00449608,0.01636738,0.02430362,0.02984616,-0.01032327,0.0220825,-0.05134204,-0.2099123,-0.04308509,-0.00132739,0.04358363,0.04282632,-0.04503116,0.00942708,0.00222475,0.05222474,0.10721187,0.07890397,-0.06333604,-0.04315786,0.02290763,-0.03339973,-0.0090317,-0.0774347,-0.04069488,-0.04347559,0.06029923,-0.02584896,0.03576162,-0.02653484,-0.04931137,0.05980413,-0.03471975,0.13444553,0.00423792,-0.04607965,-0.05733845,0.04298488,0.0095054,-0.01807559,0.02951023,0.02152643,0.04218397,-0.01244407,0.00449313,-0.06572791,0.02625208,-0.09632849,0.03336586,0.01904028,-0.08227293,-0.03916645,0.02404098,0.01957119,-0.02441187,-0.02709695,0.02451071,0.05282426,-0.01538229,0.05709102,0.02231797,0.06827486,-0.02221081,-0.05064474,0.00391521,-0.01272909,0.0280398,-0.00196075,-0.06309647,0.063099,-0.05143912,0.02087039,0.03079045,0.00342381,-0.01769781,0.020304,0.00666587,0.00767783,0.09913715,0.00019235,0.02483786,-0.04894629,0.0494425,0.04606288,-0.05327567,0.01119131,0.01522425,0.03930771,-0.0326921,0.02615181,0.05995943,0.04476728,-0.0140241,0.0962442,0.06901842,0.03065735,0.01634764,-0.01153306,-0.00060817,0.00306737,-0.0126187,0.01185622,0.01129021,-0.26327062,0.00979084,-0.05924045,0.06387576,-0.02060903,-0.02840227,0.01910875,0.01778826,0.03195617,-0.02290842,0.08688422,0.02924609,0.02725417,-0.09518314,-0.00344316,0.0030871,0.01413919,0.00221486,0.01902197,-0.01501597,0.08686449,0.01285386,0.24587879,-0.02738473,0.01134011,0.02165366,0.02143676,0.01297019,-0.01618981,0.03958608,-0.01434731,0.02067078,0.10928436,-0.01450001,-0.03852259,0.04252462,-0.01266525,0.02525544,-0.02169115,-0.00835408,-0.03997591,-0.01311774,-0.04260273,0.01101199,0.11799037,0.01434244,-0.02920214,-0.10295375,0.04376643,0.08485786,-0.09010383,-0.06104794,-0.05340388,-0.00850815,0.02869632,0.03267141,0.00837812,0.01847596,0.01378784,0.02020954,-0.00529572,0.00740239,0.04760646,0.00172928,-0.0004164],"last_embed":{"hash":"w04032","tokens":441}}},"text":null,"length":0,"last_read":{"hash":"w04032","at":1753423492609},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>6. Statistical Reporting: Frequency, Skips, Pairs</u>#{1}","lines":[165,187],"size":1799,"outlinks":[{"title":"Odds and random numbers are generated by these lotto programs.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>7. Calculating Lotto Odds, Generating Random Numbers</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08818012,-0.04587196,-0.02232432,-0.04474794,-0.05956309,0.03527386,0.02469015,-0.01319482,0.0556119,0.00360217,-0.00431243,-0.02238221,0.0329908,-0.00188172,-0.03020431,-0.00992932,-0.01002186,-0.03173333,-0.10113068,0.00565068,0.10705374,-0.03578081,-0.05565222,-0.11866301,0.04338403,0.01559447,-0.03724785,-0.075703,-0.0361633,-0.21875593,0.01854432,0.02624044,0.04781833,-0.07421318,-0.06774688,-0.03508044,-0.03355919,0.04324899,-0.07989696,0.03148182,0.00920452,0.02269717,0.0075772,-0.01619402,0.01285555,-0.04779257,-0.02503675,0.00016847,0.05079558,0.02551338,-0.03395234,0.01357628,0.01539797,0.03443399,0.04589397,-0.01351063,0.0432356,0.09439036,0.01770869,0.05466381,0.03290565,0.05511398,-0.18487431,0.03464167,0.01326677,0.01769912,-0.03480947,-0.04195382,0.01271829,0.03651175,0.01708606,0.05739184,-0.01389903,0.0595568,0.03658988,-0.046852,-0.03947777,-0.03641858,-0.02030285,0.02038169,-0.06003998,-0.04200768,-0.01450616,0.01030427,0.01204944,0.00855449,0.05759487,0.07030448,0.07960828,-0.06248796,0.04904274,0.04905863,0.0011166,0.02869221,0.04893069,0.03129496,0.04570933,-0.04191127,-0.00007568,0.12063226,0.00721364,-0.01138864,-0.02265202,0.01977912,0.04127704,-0.0210186,-0.05475606,-0.0308648,-0.05201236,-0.00320253,0.03951311,0.01006146,0.05637496,-0.0594244,-0.05869934,-0.00337796,-0.01189649,0.00091805,0.01280275,-0.00627661,-0.04330699,0.0145896,-0.00537242,-0.00281243,0.03431837,0.01239611,0.02904048,0.08251738,0.03958119,0.04070345,0.04009003,-0.00152644,-0.14863072,-0.03029835,0.01020039,-0.02381868,-0.00260559,-0.00668224,-0.02156905,0.01174725,0.00816385,-0.00990537,0.05814173,-0.08819269,-0.00435907,0.07075932,0.00781717,0.01766359,0.04319309,-0.0303949,0.01365956,-0.01165348,-0.0487805,-0.06443334,0.02940783,-0.01237894,0.09495653,0.04297791,-0.02131986,0.0084924,-0.01183028,-0.032512,0.0092185,0.08316931,-0.00953109,-0.0368853,-0.0001382,0.06609201,-0.02085823,-0.08864768,0.00072133,0.02240327,-0.04090405,0.03039569,0.09079527,-0.03221005,-0.07935667,-0.03821463,0.01881869,-0.00544374,-0.00069522,-0.00106026,-0.03389939,0.01854687,-0.05747829,-0.11520848,0.04333056,-0.03056541,0.00448386,0.07411868,-0.00164532,0.0017964,-0.03225045,-0.00287758,0.02492319,-0.03755377,-0.03943509,-0.05863553,0.07233664,-0.01547974,-0.03234501,-0.02239173,0.02759399,0.02584459,-0.03727825,0.03546257,-0.00643384,-0.06035998,0.08063322,0.03510804,-0.0283139,-0.02088581,0.03712212,0.09631287,-0.05190407,0.06486429,0.00954216,0.02725469,0.02176853,0.02367663,-0.00856948,0.00726417,-0.03351519,-0.17295536,-0.02811122,-0.02047124,-0.00307987,0.01493021,-0.01855743,0.02186433,-0.034117,0.03278193,0.07681247,0.08520982,-0.06863886,0.00088778,0.03065156,-0.02497355,-0.01394753,-0.11970796,-0.05775326,-0.05382934,0.07893501,-0.02846713,0.01357585,-0.02228422,-0.07819922,0.00806227,-0.02229095,0.1313155,0.01471687,-0.01118944,-0.02057775,0.07728134,0.01269748,-0.02107508,0.00302308,0.03763955,0.03922855,-0.06926455,0.00079606,-0.04032543,0.02087025,-0.08213886,0.03871666,-0.01657524,-0.08125667,-0.00347471,0.01292862,-0.04627017,0.01570016,0.01722852,0.05722029,0.02936018,-0.00407941,0.07746093,0.02579119,0.08460481,-0.05249695,-0.08532105,0.01200499,0.0113306,0.03642387,0.01807208,-0.06423719,0.06050817,-0.03157251,0.05855167,0.02385733,0.00577783,-0.06949798,0.0240169,-0.01702097,-0.01175983,0.0888676,-0.00161722,0.03082286,-0.03647047,0.01262715,0.05188303,-0.05459402,0.02020342,-0.00871867,0.0151642,-0.10976695,0.00786081,0.07658048,0.04095933,-0.00419832,0.05223029,0.07832492,0.02726364,0.01685034,-0.00317863,0.0290064,-0.01984115,0.02895288,-0.01918486,0.03261957,-0.24682796,0.03020566,-0.06754649,0.0740738,-0.00071648,-0.02654949,0.06546959,0.01618754,0.01925096,-0.03296733,0.06592435,0.01900307,0.02711051,-0.09755244,0.04176595,-0.03180475,-0.00482992,-0.03204058,0.05067473,0.0160163,0.10746098,0.05038092,0.23287977,0.00029268,0.0029489,0.03451355,0.00940077,-0.00417041,0.01667139,0.03136816,-0.00664981,-0.00086518,0.07226083,-0.03884633,-0.04016206,0.01299411,-0.00876228,0.01837911,-0.02908041,0.00074899,-0.06971898,-0.01956587,-0.05671704,-0.00318377,0.13025559,0.0357306,-0.02500086,-0.09171941,0.04709595,0.04630234,-0.09875695,-0.04966401,-0.03086525,-0.07922504,0.0120337,0.02795895,0.01228396,0.01905886,-0.00617121,0.02713951,0.02816815,0.02197905,0.08147487,0.00353246,-0.03399003],"last_embed":{"hash":"hvt9ha","tokens":248}}},"text":null,"length":0,"last_read":{"hash":"hvt9ha","at":1753423492754},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>7. Calculating Lotto Odds, Generating Random Numbers</u>","lines":[188,200],"size":877,"outlinks":[{"title":"The lottery software programs sort or add-up lottery, lotto data files.","target":"https://saliu.com/gambling-lottery-lotto/lotto-book.gif","line":12}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>7. Calculating Lotto Odds, Generating Random Numbers</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08512939,-0.04573191,-0.02344875,-0.04442272,-0.05792944,0.04172329,0.02530474,-0.01244716,0.05754376,0.00299963,-0.00527214,-0.02435036,0.0343641,0.00103655,-0.03045161,-0.00765439,-0.0132873,-0.03696368,-0.10110436,0.00657892,0.1074374,-0.03650049,-0.05426627,-0.12021721,0.04129536,0.01705311,-0.03695482,-0.07518789,-0.03520151,-0.21841833,0.01796199,0.02772286,0.04515056,-0.07586925,-0.06508868,-0.03690448,-0.03568364,0.04506186,-0.07511575,0.03447974,0.01217672,0.02151718,0.00895256,-0.01375398,0.01244158,-0.04445432,-0.02708278,0.00137127,0.05122878,0.02772447,-0.03257452,0.01372333,0.01478573,0.03410648,0.04094877,-0.01415252,0.04186248,0.09421026,0.01861582,0.0558106,0.03288236,0.05099118,-0.18521772,0.03250671,0.01006305,0.0154659,-0.03513577,-0.04029579,0.01325747,0.04020637,0.02043965,0.06286165,-0.01478261,0.06286774,0.03638347,-0.0486976,-0.04091856,-0.0341831,-0.01922926,0.02359073,-0.05989861,-0.04074249,-0.01404152,0.0094576,0.01032503,0.00629568,0.05705022,0.06671296,0.07627034,-0.06100827,0.04992648,0.05248871,-0.00541174,0.02876134,0.04885617,0.02841468,0.04161599,-0.04337222,0.00385889,0.11937252,0.00598954,-0.0095048,-0.02144987,0.02152802,0.0384321,-0.02072981,-0.05672412,-0.03244292,-0.05211683,-0.00005786,0.03882993,0.00680682,0.0567592,-0.06120326,-0.05779087,0.00127816,-0.00988794,-0.00186271,0.01452284,-0.00947629,-0.04069657,0.01706093,-0.00740725,-0.00118927,0.03542138,0.014926,0.03108856,0.0850307,0.04485934,0.04032318,0.03853574,-0.00234723,-0.15035257,-0.03169794,0.01166386,-0.02648561,-0.00177757,-0.00512497,-0.02138242,0.01042274,0.00930122,-0.01024714,0.06001832,-0.08724297,-0.00634853,0.07135613,0.0104384,0.01931274,0.04209814,-0.03028407,0.01367165,-0.01142624,-0.05110675,-0.06536725,0.02728687,-0.01169951,0.09253283,0.04503749,-0.01761398,0.01049636,-0.01424325,-0.03237426,0.00962776,0.08119028,-0.01038339,-0.03369853,-0.00252813,0.06344869,-0.02037483,-0.09051093,0.00107994,0.02383036,-0.04037393,0.03210539,0.09374212,-0.03075903,-0.08018737,-0.03438576,0.01877311,-0.00532834,-0.0018714,-0.00130169,-0.03676182,0.02257892,-0.06074407,-0.11502801,0.04415458,-0.02924245,0.00262332,0.07473706,-0.00355326,0.00346783,-0.03739218,-0.00102493,0.02466976,-0.03850129,-0.03691106,-0.05683541,0.07383158,-0.01617854,-0.03137383,-0.02527535,0.02473385,0.02525097,-0.03562604,0.0347616,-0.00397298,-0.06062483,0.08158561,0.0381459,-0.02678886,-0.02202133,0.03335709,0.09684854,-0.04970611,0.06442166,0.00850942,0.02600699,0.01997237,0.01641798,-0.00699587,0.0044619,-0.03385745,-0.17270938,-0.0317523,-0.01862699,-0.00249563,0.01575477,-0.01637403,0.01880546,-0.03218521,0.0358394,0.07878752,0.08214451,-0.06710786,-0.00447905,0.03173255,-0.0262859,-0.01460504,-0.12058799,-0.05744116,-0.05559298,0.07865646,-0.0307129,0.01437315,-0.02143779,-0.07746887,0.00875683,-0.02025834,0.13314424,0.01182889,-0.01237724,-0.0188265,0.0743233,0.01218908,-0.02249282,0.00566218,0.04058379,0.03886736,-0.06948625,-0.00394013,-0.04366482,0.01810847,-0.08019458,0.03912754,-0.01662866,-0.07966895,-0.00610207,0.01237422,-0.04697856,0.01918416,0.01526982,0.05697009,0.0294305,-0.00318441,0.07750975,0.02580985,0.08106633,-0.05347746,-0.08147147,0.00911239,0.01084981,0.03809261,0.0200176,-0.05880634,0.0570002,-0.03132651,0.05814097,0.0192787,0.00693961,-0.07068334,0.02596175,-0.0189917,-0.01216244,0.0887272,-0.00175564,0.03079079,-0.03309447,0.0112249,0.0528827,-0.05545377,0.01956958,-0.00748728,0.01888594,-0.11025261,0.00605695,0.07780544,0.04233838,-0.00746961,0.05288384,0.07947408,0.02822152,0.01789186,-0.00387438,0.02840886,-0.01792474,0.0273578,-0.02102572,0.02830681,-0.24758914,0.02869023,-0.07181182,0.07510341,-0.00139878,-0.03111359,0.0661508,0.01926252,0.01324599,-0.03274374,0.06663185,0.02067761,0.02948401,-0.09673464,0.04203011,-0.03328561,-0.00756181,-0.03439866,0.05121587,0.01741615,0.11120816,0.04914772,0.23316373,-0.00028644,0.00418554,0.03410407,0.00719872,-0.00005827,0.01470609,0.02655063,-0.00767491,-0.0014583,0.07136377,-0.03839191,-0.04125518,0.01484543,-0.00423025,0.01924055,-0.0297859,-0.0042744,-0.0673796,-0.02048242,-0.05341771,-0.0039939,0.1324545,0.03428608,-0.02295781,-0.09007674,0.04644115,0.0434702,-0.098093,-0.04710307,-0.03157273,-0.07994058,0.01081616,0.03149456,0.0116291,0.02104504,-0.00662621,0.02709683,0.02605296,0.02497452,0.07827146,0.00416612,-0.03414093],"last_embed":{"hash":"holgxk","tokens":247}}},"text":null,"length":0,"last_read":{"hash":"holgxk","at":1753423492819},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>7. Calculating Lotto Odds, Generating Random Numbers</u>#{1}","lines":[190,200],"size":813,"outlinks":[{"title":"The lottery software programs sort or add-up lottery, lotto data files.","target":"https://saliu.com/gambling-lottery-lotto/lotto-book.gif","line":10}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>8. Sort, Add-Up Lottery, Lotto Data Files</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11521599,-0.05354594,-0.01153006,-0.06296718,-0.0449774,0.04517577,0.01535003,-0.01891025,0.04990001,0.01551079,0.00065485,-0.00610554,0.0387588,0.00929992,-0.0477765,-0.02918361,0.00066741,-0.00193351,-0.09961264,-0.01534958,0.05332873,-0.02651816,-0.06665877,-0.11658528,0.06397628,-0.00217024,-0.04142236,-0.06489255,-0.06276335,-0.22890405,0.03444585,0.00746775,0.0266411,-0.08233928,-0.08306912,-0.01706376,-0.00429159,0.07471785,-0.05906207,-0.01880463,0.012451,0.00297055,-0.02166828,-0.07213441,0.01100153,-0.08182647,-0.00344386,-0.00698623,0.06664824,0.01384498,-0.04865165,-0.02056997,0.05559362,0.02347762,0.04688754,-0.0141219,0.04704829,0.0457399,0.03391926,0.04328659,0.05507838,0.0492752,-0.16993082,0.05143533,-0.00410651,-0.0199643,-0.02352425,-0.04899527,0.00140346,0.05433916,-0.02554754,0.01631523,-0.04224895,0.07250935,0.02134254,-0.07508682,-0.03394429,-0.07041916,-0.0834266,0.00436421,-0.07166518,-0.03935154,-0.00481547,0.01414358,0.0189863,0.0718549,0.06187849,0.03430567,0.04985112,-0.04590506,0.05650378,0.00085946,0.02577515,0.04299952,0.02225805,0.05514399,0.06295889,-0.03978617,-0.02791114,0.11379135,0.0284146,0.00437519,-0.03913527,0.04065933,0.04594378,-0.0382593,-0.01517442,-0.02523515,-0.05755092,0.00881825,0.01561812,-0.00989701,0.0543453,-0.02751261,-0.03050832,0.00528187,-0.01093745,-0.01340023,0.00290758,0.02027337,-0.0128682,0.00152683,-0.0240486,-0.00506887,0.00680978,-0.01741715,0.02593752,0.0867349,0.02119856,0.02613822,0.05986116,0.02819566,-0.12134808,-0.01546795,-0.01148717,0.02751675,-0.01220143,0.00747269,-0.01533103,-0.00702981,-0.00564046,0.02417907,0.03122911,-0.07418385,0.02302812,0.09710877,-0.04014653,0.0034228,0.03552644,-0.03992559,0.02455507,-0.0336518,-0.04510712,-0.0360117,0.02078451,-0.00163336,0.09180472,0.04617248,-0.07632973,-0.00672521,-0.04083126,-0.03142799,-0.00509619,0.10812137,-0.03346679,-0.04723749,-0.00350479,0.03857531,-0.01456105,-0.07513952,0.00223083,0.02607787,-0.03470063,0.03252097,0.08380938,-0.03868043,-0.04310106,-0.09073809,0.03190236,-0.02047145,0.0390345,0.01115572,-0.03492096,0.00096083,-0.02530744,-0.07274747,0.01588346,-0.03317856,0.03470288,0.09601801,-0.0155478,0.01065693,0.00065443,-0.02957307,-0.02448553,-0.06786064,-0.06361224,-0.03574435,0.02840129,0.02037386,0.03365893,0.0020576,0.05852506,-0.00482923,-0.00327744,0.0549322,-0.02416207,-0.03192333,0.0887,0.02123754,-0.07202217,-0.02297355,0.0461124,0.07379051,-0.03846985,0.03329869,0.02124557,0.0379511,0.03283682,0.03362733,-0.02090901,0.02341395,-0.03098456,-0.1913677,-0.03958948,-0.01347017,0.01642805,0.02088125,-0.01828495,0.04977531,0.00695821,-0.0159216,0.08686622,0.11619779,-0.06968685,-0.0225594,0.03223344,-0.04204141,-0.00214116,-0.0820513,-0.0638207,-0.03448213,0.0619473,-0.04073615,0.05126406,-0.02370549,-0.05462878,0.03518581,-0.02544429,0.12064148,0.00095203,0.00149771,-0.01462533,0.09095557,0.04893097,-0.00824003,-0.0171751,0.03552577,0.05804635,-0.05786509,0.02993918,-0.03786333,0.02218662,-0.09186888,0.00828795,-0.0067959,-0.06266505,-0.00789245,0.01342143,-0.02631014,0.0065298,0.01341073,0.00236813,-0.00472095,0.00499593,0.00720902,0.00207223,0.05810298,-0.02921558,-0.04459143,0.01565639,0.00378047,0.04278925,0.05389354,-0.08826207,0.05806797,-0.03184333,0.03779951,0.00173221,0.00802704,-0.02545169,0.05444797,0.02457962,-0.00954259,0.06403887,-0.00052452,0.02954986,-0.01429202,0.04826464,0.06085327,0.01321269,-0.00347739,-0.05143422,0.00186536,-0.07974168,0.0018543,0.04104979,0.04017249,0.04202412,0.06924669,0.03673574,-0.01591195,0.02885155,-0.02086421,0.02815126,-0.04432639,0.02193745,0.01306201,0.06459224,-0.25410265,0.04776243,-0.05341515,0.05858783,-0.01369481,-0.00365724,0.03699064,0.01167902,0.03494318,-0.03358115,0.03190315,0.05967893,0.01406198,-0.09152118,-0.00152295,-0.01991399,0.0051043,-0.00233979,0.09150528,-0.02756914,0.06433558,0.06006881,0.24794883,-0.02633081,-0.01363509,0.02366234,0.03892183,-0.01775026,0.02256517,0.0215735,0.02403315,0.01463834,0.09792484,-0.00087177,-0.01631906,0.05831019,-0.00819995,0.03896197,-0.01534479,0.03559154,-0.06550717,0.01018695,-0.06425174,-0.02263339,0.08928549,0.01057918,-0.05635267,-0.06496259,0.04513843,0.0956784,-0.08462322,-0.07470756,-0.05218343,-0.03980857,0.0019523,0.02208938,0.00721506,0.03370069,0.00431658,0.02586007,0.03492299,-0.03410216,0.10335703,0.00416675,-0.01949612],"last_embed":{"hash":"fa1huq","tokens":346}}},"text":null,"length":0,"last_read":{"hash":"fa1huq","at":1753423492888},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>8. Sort, Add-Up Lottery, Lotto Data Files</u>","lines":[201,213],"size":1252,"outlinks":[{"title":"Lottery software breaks strings of numbers in combinations; create lotto combinations by position.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":12}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>8. Sort, Add-Up Lottery, Lotto Data Files</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11463348,-0.05821111,-0.01309706,-0.06547824,-0.04227458,0.05110626,0.01661192,-0.02052424,0.05147164,0.01721543,-0.00301781,-0.0076044,0.03875847,0.01268098,-0.04622295,-0.0281333,-0.00002827,-0.00451597,-0.1006353,-0.01770067,0.05193968,-0.02801154,-0.06402716,-0.11521477,0.06417651,-0.00374595,-0.04154943,-0.06682116,-0.06202413,-0.22838828,0.03265948,0.00824183,0.02538151,-0.08478649,-0.08236508,-0.01983515,-0.00385396,0.07440408,-0.05695212,-0.02079693,0.01327763,0.00110065,-0.02251857,-0.07293936,0.01079397,-0.07637376,-0.00476812,-0.0079384,0.06650376,0.01373252,-0.04876211,-0.02009105,0.05449841,0.0251305,0.04405056,-0.01611765,0.04685878,0.04816842,0.03311261,0.04741194,0.05706051,0.04865952,-0.16967849,0.05231953,-0.00665658,-0.02492264,-0.02418214,-0.04734622,-0.00104617,0.05886479,-0.02539011,0.01666745,-0.04430468,0.07558504,0.02378028,-0.07381727,-0.03422565,-0.07240327,-0.0809248,0.00830409,-0.07119893,-0.03810627,-0.00629795,0.01622206,0.01977772,0.06658089,0.06244503,0.03182069,0.04496491,-0.04173812,0.05920438,0.00353036,0.01709805,0.04241619,0.02096852,0.05315304,0.05971514,-0.04228263,-0.02673686,0.11421297,0.02868036,0.00509101,-0.03690529,0.04222846,0.04458211,-0.03391974,-0.0159805,-0.0261196,-0.05717087,0.01099955,0.01400352,-0.01123234,0.05347446,-0.02323746,-0.02821498,0.00860769,-0.00736423,-0.01627919,0.00371225,0.02164073,-0.0114258,-0.00104022,-0.02296892,-0.00582412,0.0047777,-0.01587376,0.02595479,0.08554623,0.02347065,0.02556363,0.05899595,0.0272347,-0.1201169,-0.01845856,-0.01650357,0.02367441,-0.01282511,0.00685005,-0.01468579,-0.00894272,-0.00439055,0.02363462,0.02721046,-0.07548082,0.02031979,0.10123171,-0.04246075,0.00437228,0.03610066,-0.04031951,0.02402956,-0.03389018,-0.04710939,-0.03638721,0.01742067,-0.00118059,0.09042704,0.04711646,-0.07553719,-0.00391373,-0.04138898,-0.032677,-0.00507409,0.10750237,-0.03705618,-0.04616535,-0.00706276,0.03770838,-0.01269366,-0.07579577,0.00110098,0.02708882,-0.03202185,0.03502154,0.08585426,-0.03640091,-0.04308088,-0.09179407,0.03319754,-0.02062634,0.03952901,0.01042122,-0.03719203,0.0040626,-0.02933462,-0.07186561,0.01714666,-0.03048866,0.03437792,0.09386354,-0.01782364,0.01379461,-0.00021908,-0.02907206,-0.02484017,-0.06643125,-0.06039583,-0.03476713,0.0296541,0.0176674,0.03604774,0.00260991,0.05797571,-0.00638448,-0.00092537,0.05439027,-0.02293926,-0.03033968,0.09142199,0.01947293,-0.07075816,-0.02162335,0.04209172,0.07291602,-0.03456843,0.03231971,0.02017337,0.03792129,0.03579915,0.02825275,-0.01656506,0.02160806,-0.03026222,-0.19391961,-0.04107938,-0.01437145,0.01662998,0.01966409,-0.01894503,0.04714344,0.00656077,-0.01598765,0.0851474,0.11534119,-0.06768484,-0.02295469,0.03531816,-0.04093412,0.00091879,-0.08119039,-0.06514825,-0.03398145,0.06236271,-0.04328606,0.05586157,-0.0228795,-0.05501727,0.03495046,-0.02658863,0.1211748,0.00104705,0.00132403,-0.01337888,0.09268634,0.05206526,-0.00845864,-0.01867707,0.03787813,0.05832011,-0.05932231,0.02718738,-0.03630666,0.02384245,-0.08961537,0.0067856,-0.00513668,-0.06013812,-0.00982993,0.01222246,-0.02496517,0.00731005,0.01486276,0.0014627,-0.00781479,0.005423,0.00765118,0.00219082,0.05525178,-0.03213148,-0.04306447,0.01540436,0.00373392,0.04443345,0.05423019,-0.08757126,0.05770697,-0.03410365,0.03596237,-0.00159709,0.00722302,-0.0257834,0.05840279,0.02534356,-0.0102973,0.0647664,-0.00237894,0.0281665,-0.01095607,0.04754265,0.06107812,0.01683987,-0.00451734,-0.04969348,0.00319145,-0.08103328,-0.00090262,0.04080444,0.04141198,0.04158064,0.07075792,0.03876056,-0.01714609,0.03165067,-0.02404262,0.02658697,-0.04227927,0.02014078,0.01371795,0.06343848,-0.25566491,0.0498723,-0.05440553,0.05801717,-0.01573643,-0.00533578,0.03829364,0.01870785,0.03224207,-0.03336654,0.02988175,0.0594544,0.01505644,-0.09115652,-0.00463549,-0.01941918,0.00310597,-0.00442622,0.09515633,-0.02842956,0.06649695,0.06122435,0.24778134,-0.03013005,-0.01186938,0.02303458,0.03904238,-0.01435906,0.02088388,0.01653196,0.02613707,0.01497444,0.09603747,0.00036515,-0.01636917,0.06050036,-0.00591264,0.03880615,-0.0173176,0.03162197,-0.0637257,0.0085277,-0.06346763,-0.02344534,0.0901053,0.01228917,-0.05718202,-0.0654059,0.0422288,0.09800131,-0.08040165,-0.07521031,-0.05179682,-0.03657041,0.00255982,0.02402792,0.00892658,0.03455054,0.00403417,0.02592079,0.0364271,-0.03206474,0.10202678,0.00424607,-0.01814798],"last_embed":{"hash":"k3gpvp","tokens":345}}},"text":null,"length":0,"last_read":{"hash":"k3gpvp","at":1753423492993},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>8. Sort, Add-Up Lottery, Lotto Data Files</u>#{1}","lines":[203,213],"size":1199,"outlinks":[{"title":"Lottery software breaks strings of numbers in combinations; create lotto combinations by position.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":10}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>9. Make/Break/Position</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08319847,-0.06556696,0.00829389,-0.05431209,-0.03560347,0.04298029,-0.03831418,-0.03927146,0.01045242,-0.01208578,0.01419353,-0.00103281,0.0449268,-0.02928269,-0.02024858,-0.01722055,0.00766974,0.00237944,-0.12552343,0.03265626,0.05252295,-0.02925727,-0.05767175,-0.08767257,0.02550949,0.03771812,-0.03906154,-0.06250112,-0.0434474,-0.21663491,0.06309617,0.0312583,0.01886629,-0.05655815,-0.08535001,-0.06294042,-0.04452378,0.08586407,-0.1012645,0.02703477,0.01671254,0.00783875,-0.00468745,-0.03173653,-0.00168732,-0.05699256,-0.02291413,-0.00414085,0.08249649,0.01757251,-0.04099119,-0.00433405,0.01433345,0.01140644,0.05147276,0.0153714,0.02975978,0.11147449,0.03485993,0.03138677,0.06542764,0.08121351,-0.16049106,0.03186842,-0.0101475,0.02422594,-0.00038427,-0.02716962,-0.0085551,0.03903513,-0.03896971,0.0370825,-0.01242407,0.08967794,-0.00216658,-0.03075888,-0.0363833,-0.07761267,-0.04345939,0.03550595,-0.07441322,-0.01462201,-0.00587296,0.02415904,-0.01667189,0.04817919,0.0471072,0.04954883,0.00739922,-0.04282119,0.02483447,0.03197703,0.01229918,0.03516378,0.00659623,-0.00278424,0.04349998,-0.01834181,-0.00432869,0.09917053,0.01024265,-0.00113355,0.01849733,-0.01312446,0.01457477,-0.02019996,-0.03697884,-0.02215224,-0.04868985,0.01093052,0.05714675,0.03024146,0.04547663,-0.05167439,-0.06160454,0.01185602,-0.03834482,0.00803551,-0.01585731,0.03495718,-0.00360594,0.0331163,-0.05359901,0.00007973,0.00416039,-0.00747632,0.01467152,0.06003316,0.05022635,0.03114435,0.038249,0.0016713,-0.15314873,-0.01999399,-0.00376013,-0.0189991,0.00772841,-0.00815188,-0.0182094,-0.01798173,0.00453421,-0.02459659,0.0600744,-0.07865757,0.01629202,0.07431229,-0.03650417,-0.01557464,0.05946366,-0.04109486,0.00982546,0.00013049,-0.06720455,-0.07057363,0.04305298,0.0163297,0.09563833,0.04633594,-0.05623738,0.00361931,-0.04661005,-0.03491532,0.00306976,0.1237889,-0.03500611,-0.07220986,-0.02907203,0.06946932,-0.06544857,-0.07511535,-0.01109668,-0.00333242,-0.05666901,0.04539531,0.10911161,-0.00993231,-0.07583353,-0.05025536,0.01554597,0.02710783,0.00869239,-0.02109841,-0.0273,-0.03094369,-0.02481524,-0.07453362,-0.01797769,0.00903485,-0.00592755,0.05890722,-0.01642315,0.00154645,0.00025841,-0.02126739,-0.00392195,-0.06621641,-0.03257225,-0.0331603,0.03416043,-0.03174995,0.03005842,0.02905309,0.00097733,0.05167286,-0.04361324,0.02650811,0.00771108,-0.08497428,0.08979223,0.03411248,-0.05641675,0.00312394,0.08041501,0.09003978,-0.06546491,0.0121644,0.0188804,0.01770682,0.00447867,0.02769404,-0.02387784,-0.00442767,-0.02897732,-0.18850534,-0.04757624,-0.00310019,0.03365739,0.00839585,0.00540315,0.00576857,-0.01690692,0.02561137,0.0745667,0.1070746,-0.11319152,-0.01811098,0.03829975,-0.03451407,-0.01617119,-0.07495249,-0.02365968,-0.03253367,0.04972067,0.00078198,0.02279575,-0.02114768,-0.0638064,0.03744337,-0.04154615,0.14737937,0.00769515,0.04063489,-0.01216139,0.10977629,-0.00875268,-0.00470027,0.01061417,0.0325397,0.01852936,-0.0345468,0.01703991,-0.03055398,0.01115666,-0.0546486,0.02122363,0.00790248,-0.08270317,0.02662283,-0.03473159,-0.02766025,0.02966296,0.0243885,0.06779546,0.0428015,0.00010083,0.0431178,-0.00204255,0.04603466,-0.01037044,-0.05055751,0.01490787,-0.0054222,0.00108064,0.02709265,-0.08121307,0.05570743,-0.00081921,0.01347002,0.03090367,-0.02097782,-0.02418123,0.00758046,-0.00238976,0.01299651,0.04020842,0.01209458,0.06430829,-0.01344639,0.0395314,-0.0092391,-0.03035766,0.02526877,-0.0411582,0.02469896,-0.0178896,0.0329138,0.08842876,0.04342806,0.00139219,0.06393049,0.08133943,0.01690805,0.00830725,-0.00350859,0.02850352,-0.01676492,0.05926873,0.03390556,0.02172776,-0.26605347,0.03420627,-0.04550514,0.05669293,-0.0252326,0.00050722,0.04713504,0.02218585,0.01962504,-0.03183264,0.0492182,0.01643766,0.01028583,-0.07635916,0.03210267,-0.03264879,0.0155457,0.00232577,0.05712771,-0.03443618,0.07381285,0.04465053,0.25837362,-0.0060667,-0.02268467,0.05316148,0.01013118,0.02002355,-0.00236994,0.03337194,-0.00688498,-0.01846683,0.07978025,-0.00469597,-0.01847256,0.00518368,-0.02702187,0.024089,-0.01908974,0.03367772,-0.05118921,0.0013467,-0.04690633,-0.00800553,0.11950397,0.02906007,-0.0370165,-0.09061269,0.05215355,0.05804734,-0.09749399,-0.0279959,-0.07757285,-0.07475973,-0.0136217,0.03138524,0.02530923,0.04499449,0.01760746,0.01345906,0.03634785,-0.05735307,0.08754773,0.02648558,-0.04354057],"last_embed":{"hash":"ppq43p","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"ppq43p","at":1753423493099},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>9. Make/Break/Position</u>","lines":[214,272],"size":6154,"outlinks":[{"title":"_**Excel Spreadsheets in Lotto, Lottery**_","target":"https://saliu.com/Newsgroups.htm","line":23},{"title":"These are special resources in lotto software, lottery software, mathematics, statistics.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":58}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>9. Make/Break/Position</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08016936,-0.06396479,0.00972014,-0.05915434,-0.03393365,0.04840959,-0.03826985,-0.03968252,0.01267758,-0.01297304,0.0155196,-0.00227836,0.04559788,-0.02732836,-0.0196675,-0.01585847,0.00489883,0.00067637,-0.12612167,0.03447303,0.05318649,-0.0307869,-0.05470418,-0.08702256,0.02660206,0.03724677,-0.03707167,-0.06198594,-0.04234987,-0.21732898,0.0643537,0.03174289,0.01836202,-0.05742123,-0.08212774,-0.06538226,-0.04481055,0.08776707,-0.0987928,0.02876161,0.01972462,0.00623007,-0.00518335,-0.03164044,-0.0026528,-0.05394684,-0.02536988,-0.00582627,0.08222461,0.01842453,-0.03862236,-0.00451253,0.01429634,0.01126619,0.05325579,0.01723744,0.03049969,0.11242101,0.03389081,0.03101611,0.06665371,0.08111648,-0.16130964,0.03148216,-0.00897172,0.02261563,0.00107017,-0.02532995,-0.0091211,0.04351566,-0.04046841,0.03319259,-0.01310192,0.0942013,-0.00412749,-0.03129476,-0.03508804,-0.07803598,-0.03967319,0.03691441,-0.07651298,-0.01214267,-0.00561614,0.02498811,-0.01953945,0.04720771,0.04647795,0.04851383,0.00200332,-0.04051654,0.02716075,0.03241834,0.00401095,0.03462222,0.0034751,-0.00627243,0.04125483,-0.01653023,-0.00046521,0.09816793,0.01190543,0.00467596,0.02002012,-0.01327108,0.01101072,-0.0159953,-0.03883798,-0.02353086,-0.04740361,0.01280042,0.05692724,0.0322805,0.04523582,-0.05177112,-0.05852616,0.01487966,-0.03440358,0.00745729,-0.0181648,0.03367327,-0.00071131,0.03506097,-0.05479337,-0.00140721,0.00386251,-0.00588495,0.01595361,0.06073135,0.05381558,0.03084394,0.03647659,-0.00059198,-0.1525223,-0.02403127,-0.00726776,-0.02130084,0.00871628,-0.00681182,-0.01600773,-0.01997367,0.00689507,-0.02525849,0.05987671,-0.07721235,0.01613349,0.07825905,-0.0368581,-0.01561216,0.05976132,-0.03953849,0.00817809,0.00078988,-0.06792531,-0.07019281,0.03848213,0.01763408,0.09257971,0.04815822,-0.05596312,0.00393617,-0.04814069,-0.03645024,0.00533759,0.1267295,-0.03640207,-0.06992248,-0.03174203,0.06886765,-0.06432689,-0.07513696,-0.01149206,-0.00614379,-0.05650007,0.04524058,0.10879494,-0.00932642,-0.07672937,-0.04631745,0.01688447,0.03192922,0.01068308,-0.02133405,-0.02871965,-0.0301509,-0.02685321,-0.07193266,-0.01827624,0.01353634,-0.00797566,0.05522097,-0.01768817,0.00506366,0.00280715,-0.02166722,-0.00364221,-0.0671173,-0.02967411,-0.02921719,0.03420922,-0.03350546,0.03192326,0.02864222,-0.00390615,0.0483915,-0.04250096,0.02449791,0.0120265,-0.08513355,0.09122716,0.0350714,-0.05766249,0.00262443,0.0774127,0.08576707,-0.06169733,0.00744019,0.01862053,0.01952308,0.00354241,0.02318276,-0.02271394,-0.00476313,-0.02815684,-0.19045816,-0.05200008,-0.00399056,0.03548196,0.00553586,0.00906821,0.00418879,-0.01347356,0.02715504,0.07281873,0.10772546,-0.11240667,-0.02381562,0.03827828,-0.03362943,-0.01638934,-0.07400967,-0.02218335,-0.02991233,0.04780147,0.00187454,0.02460963,-0.02672646,-0.0633901,0.03905457,-0.04010152,0.14766483,0.00895621,0.03986492,-0.01275696,0.10979822,-0.00849818,-0.00754367,0.01047432,0.0329876,0.01708342,-0.0364104,0.01368591,-0.03185489,0.0058064,-0.05222204,0.0219459,0.00846926,-0.08164892,0.02502775,-0.03575208,-0.02746712,0.03077246,0.02689044,0.06880652,0.04184547,0.00007225,0.0418349,-0.00295374,0.0438381,-0.00981663,-0.04927444,0.014067,-0.00540669,0.00121858,0.02709287,-0.08014645,0.05251927,0.00282315,0.00870275,0.02789256,-0.02317389,-0.02198783,0.01233501,-0.00214928,0.01223147,0.03769436,0.01255549,0.06361635,-0.00889357,0.03566771,-0.01104663,-0.02773948,0.02625719,-0.04254843,0.02640291,-0.01529354,0.03243313,0.08782636,0.04473833,0.00108861,0.06491107,0.08299221,0.01590545,0.00751437,-0.00516646,0.02630305,-0.01649406,0.0607746,0.03539155,0.02009954,-0.26772371,0.03320247,-0.04907863,0.05622178,-0.02673741,-0.00241458,0.04747472,0.02507595,0.01420282,-0.03071387,0.04703912,0.01702434,0.00879843,-0.07772414,0.02574579,-0.0304655,0.01219649,0.00211415,0.06003536,-0.03725325,0.0743835,0.04462168,0.26006478,-0.00599999,-0.02145229,0.05490036,0.00563092,0.02561785,-0.0061438,0.02854412,-0.00355076,-0.01989431,0.07684702,-0.0020287,-0.01785639,0.00645875,-0.02543162,0.02930152,-0.0203008,0.03303069,-0.05112189,-0.00015753,-0.04432727,-0.00969331,0.12237749,0.02817417,-0.03780129,-0.09110308,0.04930192,0.05687234,-0.09654589,-0.02671576,-0.07767571,-0.0747345,-0.01510756,0.03030367,0.02777395,0.04553534,0.01601534,0.0119817,0.03405643,-0.05829797,0.08726342,0.0272849,-0.04328728],"last_embed":{"hash":"od8ffj","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"od8ffj","at":1753423493259},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#<u>9. Make/Break/Position</u>#{1}","lines":[216,272],"size":6120,"outlinks":[{"title":"_**Excel Spreadsheets in Lotto, Lottery**_","target":"https://saliu.com/Newsgroups.htm","line":21},{"title":"These are special resources in lotto software, lottery software, mathematics, statistics.","target":"https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif","line":56}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10331937,-0.03431258,-0.01241731,-0.02738362,-0.05729836,0.02406074,-0.02235837,-0.00280048,0.01602079,-0.00132041,0.00557505,-0.00609012,0.04150658,-0.03712195,-0.01754698,-0.04447234,0.03397508,0.03981098,-0.05153387,-0.01991802,0.06586525,-0.02263049,-0.08166977,-0.0873569,0.04726936,0.02995502,-0.043026,-0.06322714,-0.04069753,-0.18786357,0.03402191,0.02473154,0.02198921,-0.04231347,-0.0763012,-0.00244946,-0.02592016,0.02865444,-0.06618383,0.0087324,0.01552117,0.03148191,-0.0057919,-0.00294401,0.04223223,-0.07882452,0.02417706,0.01768702,0.06183629,-0.01614461,-0.0886137,-0.00433145,0.01337512,0.01778647,0.04893291,0.03184665,0.04643172,0.09985424,0.0186381,0.00936909,0.02985659,0.0716412,-0.20226906,0.08439537,0.01651484,0.03833099,-0.01616889,-0.03006882,0.03117252,0.00307265,-0.00316369,0.00583797,0.00421694,0.06745113,0.03073162,-0.02067668,-0.02907167,-0.05653305,-0.04254178,0.01122611,-0.04238061,-0.00377163,0.002842,0.0166131,-0.01497974,0.06910794,0.0517869,0.03411981,0.11285394,-0.08578803,0.01641281,0.010034,0.05868443,0.03202261,0.02050484,-0.01917241,0.06234997,-0.04247043,-0.02063064,0.12148926,0.00871607,-0.01530605,-0.00712423,-0.01268839,0.0401218,-0.05413024,-0.00943757,-0.03706688,-0.04312977,0.04380396,0.04530725,0.00174633,-0.00520219,-0.04854185,-0.04604558,-0.02764159,-0.04302394,0.01030885,0.00384897,0.00279745,-0.06256006,-0.01039568,0.01697922,0.02322765,-0.00300769,-0.01825201,0.00834703,0.05907333,-0.00326361,0.01662476,0.0512569,0.05529906,-0.1403199,-0.03176497,-0.00010388,0.00478128,0.00369906,-0.04040111,-0.00735748,0.04089696,-0.01613645,-0.0364463,0.03939506,-0.1084223,-0.02430841,0.08427674,0.00149454,-0.00792317,-0.00248811,-0.0150035,0.02056088,-0.02519715,-0.0105394,-0.0574789,0.03537682,0.0026212,0.13587427,0.04944929,-0.05978301,0.00813861,-0.03632396,-0.02263756,-0.04040929,0.14843184,-0.00554146,-0.12012924,-0.00276766,0.06132183,-0.02648047,-0.07867087,0.00549936,0.00402508,-0.0693906,0.02388641,0.11154595,-0.02070165,-0.05255411,-0.0509603,-0.01012514,-0.02779999,0.02054365,-0.03213936,-0.01053021,0.00878341,-0.00508858,-0.0867674,0.00690968,-0.03634897,0.00688496,0.07089624,-0.01518151,-0.00348667,-0.02746467,-0.01860174,-0.03506763,-0.02381952,-0.06357091,-0.05655097,0.06146316,-0.04629568,0.0099459,0.01477561,0.02340604,0.03132081,-0.00646392,0.02473037,-0.05480427,-0.07300627,0.05748155,0.01162433,-0.05001691,0.02974242,0.07236642,0.07331328,-0.05180395,0.04912395,0.02365128,0.02193598,-0.01004762,0.0663278,-0.02311861,0.04329815,-0.06877714,-0.18432657,-0.03007996,-0.0328708,-0.00023677,-0.01302254,-0.02055033,0.04387781,-0.04097866,-0.00951538,0.07052359,0.13004711,-0.07058437,0.02523112,0.04861993,-0.01020965,-0.02368949,-0.05998933,-0.05320163,-0.05835183,0.04614066,0.00121984,-0.01470742,-0.00402673,-0.06351428,0.00282154,-0.03948484,0.10611252,-0.01308538,0.00330866,0.00076678,0.07650381,0.01032797,-0.00792158,-0.06639541,-0.00068749,0.06602292,-0.03146088,0.02675215,-0.00109758,0.01457654,-0.07355084,0.02619864,0.0141184,-0.07796286,-0.00883699,0.00574326,0.00827074,-0.00682331,0.0067292,0.04740517,0.0224915,0.00177294,0.04086223,0.03069546,0.04035098,-0.00517897,-0.05526306,0.01917085,-0.01957059,0.02498485,-0.0241152,-0.09500149,0.04419256,-0.02979719,0.07829711,0.0005113,-0.02282244,-0.04236702,0.01446404,0.00927264,-0.00410287,0.0839754,-0.0092934,0.06460952,-0.02384825,0.03463198,0.08023802,-0.03979821,0.01200017,0.00891589,-0.07338903,-0.0616465,0.0489646,0.08564088,0.07011764,0.03714032,0.05037969,0.02066099,0.01213046,0.00631345,0.01885896,0.00578513,-0.02996353,0.04066027,0.02558361,0.06117414,-0.25712484,0.03344971,-0.00750113,0.0411751,-0.02570295,-0.01983276,0.03353563,-0.0408724,0.05748837,-0.02588977,0.07090452,0.00843174,-0.00037947,-0.06438494,0.01140458,-0.01844993,0.06915778,0.00014804,0.05944881,0.03455473,0.00014816,0.04399204,0.24716406,0.0152493,0.00526046,0.04237762,0.01298031,-0.01176891,0.01030639,0.05122801,0.0252567,0.01749733,0.06585342,0.01510189,-0.0630798,0.02341663,-0.04416673,0.01747046,0.00750993,0.03887317,-0.0536718,0.0260199,-0.0504987,-0.00083034,0.09286473,0.03165484,-0.02708009,-0.11323952,0.05626506,0.06026347,-0.07312571,-0.04665703,-0.08383564,-0.02173363,0.02999721,0.01780227,0.01262887,-0.00850676,0.01097728,-0.00473766,0.06940459,-0.0313528,0.06155984,0.01582283,0.00872173],"last_embed":{"hash":"17mus65","tokens":420}}},"text":null,"length":0,"last_read":{"hash":"17mus65","at":1753423493419},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)","lines":[273,299],"size":3018,"outlinks":[{"title":"<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":9},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":11},{"title":"_**<u>Lottery Skip System Software</u>**_","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"_**Lottery Filters, Filtering in Software**_","target":"https://saliu.com/filters.html","line":13},{"title":"_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_","target":"https://saliu.com/bbs/messages/42.html","line":14},{"title":"_**Vertical (Positional) Filters in Lottery Software**_","target":"https://saliu.com/bbs/messages/838.html","line":15},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":16},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":17},{"title":"_**Lottery Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":19},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":20},{"title":"Run very useful lottery statistical tools, utilities, apps, lotto combination generators.","target":"https://saliu.com/HLINE.gif","line":22},{"title":"Forums","target":"https://forums.saliu.com/","line":24},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":24},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":24},{"title":"Contents","target":"https://saliu.com/content/index.html","line":24},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":24},{"title":"Home","target":"https://saliu.com/index.htm","line":24},{"title":"Search","target":"https://saliu.com/Search.htm","line":24},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":24},{"title":"Thanks for visiting the site of lottery, lotto, best software programs, utilities.","target":"https://saliu.com/HLINE.gif","line":26}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08707673,-0.01756129,-0.01036316,-0.05416275,-0.02031305,0.00805527,0.0202208,-0.00039225,0.02769568,-0.01864395,0.02670928,-0.00031116,0.05281429,-0.04857077,-0.0320438,-0.04130618,0.02387805,0.04567416,-0.04118017,-0.03412624,0.06486864,0.00317775,-0.0846945,-0.09899686,0.0582426,0.01854459,-0.0564754,-0.07575704,-0.04977725,-0.17015964,0.03234778,0.01937229,0.01521909,-0.03845641,-0.05991822,0.00877711,-0.00727161,-0.0005297,-0.0352848,0.01635331,0.0225995,0.00990355,-0.01132622,0.01162276,0.02772479,-0.05673211,0.01724417,0.00714076,0.02986688,0.00228179,-0.08018307,0.01311023,0.03458554,0.00249395,0.02830978,0.02515701,0.04361958,0.07715994,0.02603871,0.04488324,0.03720868,0.07239547,-0.20242895,0.05978088,0.03743518,0.0387136,-0.01050205,-0.04875921,0.01205735,-0.00729649,-0.008972,0.00715077,0.00251357,0.06136904,0.04994704,-0.04245825,-0.06018742,-0.0612175,-0.05566409,0.02059933,-0.0412444,0.00204603,0.01872212,0.02562483,-0.03455195,0.05939454,0.05620228,0.02519333,0.12093363,-0.08449584,0.02477717,0.02637539,0.04988137,0.04222332,0.03026662,-0.02426397,0.05467521,-0.05669126,0.02105056,0.15443867,-0.03164311,-0.02427292,0.01257327,-0.01877249,0.03369201,-0.0431119,-0.01657482,-0.02026209,-0.02263156,0.02762708,0.02414395,0.00968997,-0.01257675,-0.05103583,-0.05412178,-0.03597344,-0.05993678,-0.00858414,-0.00412956,0.01393598,-0.04779461,-0.02872332,0.00879078,0.01928345,-0.00293886,-0.02607252,0.04224064,0.07558285,-0.02536737,0.00340133,0.05042952,0.01287895,-0.15412167,-0.02402559,-0.00332041,0.02272009,0.01647827,-0.04134822,0.00366806,0.06387709,-0.02524225,-0.01513267,0.04812844,-0.10754705,-0.01675057,0.08725949,0.01512353,-0.00245866,0.00400611,-0.01000413,0.01857567,-0.02093012,-0.02342523,-0.03016843,0.02893549,-0.00127524,0.12862846,0.03891809,-0.02800861,0.0084703,-0.02977348,-0.05146556,-0.02752664,0.14773269,-0.02065327,-0.10306428,0.0098416,0.03111954,-0.0323193,-0.08756258,0.03604964,-0.00006031,-0.07225278,0.02255281,0.0619894,-0.02388062,-0.04464343,-0.04527405,-0.01754455,-0.02986169,0.02902341,-0.03945834,-0.02599669,0.02796588,-0.02763489,-0.08614311,-0.00939895,-0.0161227,-0.00191571,0.06719132,-0.00688333,0.00394337,-0.0520645,-0.01861399,-0.01639405,-0.03074905,-0.05454801,-0.04938862,0.06358621,-0.05567292,-0.00148692,0.0273955,0.01872165,0.03049425,0.01913455,0.03607076,-0.06877887,-0.03523449,0.04603577,0.02384551,-0.06503256,0.03646435,0.04607356,0.05268503,-0.04057349,0.04941693,0.02679392,-0.00020754,0.0133581,0.06233479,-0.02256162,0.03808859,-0.04710875,-0.19255333,-0.03123021,-0.02300135,-0.01161709,-0.00593961,-0.01946301,0.02252721,-0.03464672,-0.00151665,0.09602553,0.11065541,-0.04927269,0.02907758,0.04280134,-0.0086753,-0.0013804,-0.06280041,-0.06312063,-0.07673819,0.06602456,-0.00005114,-0.00559648,-0.01873556,-0.05647858,0.00445679,-0.01943639,0.11099134,-0.03712704,0.01389851,0.00042269,0.07917074,-0.01670434,-0.00597233,-0.06865639,-0.00029819,0.0648398,-0.04009563,0.01000873,-0.00863653,0.02212666,-0.09823857,0.03967794,0.0086917,-0.07179019,0.01213588,0.02996688,0.0526314,-0.04569477,0.0086656,0.0356323,0.00975365,-0.01520227,0.04546937,0.05970971,0.07004922,-0.0148307,-0.04533216,0.03655587,-0.02538201,0.01606996,-0.04026005,-0.09354198,0.06247758,-0.01073858,0.07412209,0.00964588,-0.02351337,-0.04178079,0.00474674,-0.00013567,-0.00630306,0.09715708,-0.01103845,0.04986905,-0.03154318,0.05032342,0.08524032,-0.05218764,-0.00090971,0.0216792,-0.03268144,-0.07965135,0.05625661,0.0542657,0.05209232,0.03777948,0.05226859,0.00957223,0.00215009,0.0004429,0.03517165,-0.01738468,-0.02718979,0.03427555,0.02333689,0.04710022,-0.24015254,0.03994549,-0.01925443,0.0351385,-0.01198037,-0.02230041,0.03206357,-0.04112583,0.05454004,-0.03292324,0.08502771,0.00901568,-0.01008382,-0.07732856,0.02187946,-0.02895825,0.09256676,-0.02702261,0.04370091,0.04197601,0.02732501,0.0423003,0.26086763,0.020869,0.00672384,0.02982016,-0.00264425,-0.02583702,-0.00921203,0.05221502,0.03550451,0.01210767,0.05136395,0.00506508,-0.07432044,0.01391667,-0.03292613,0.05358995,0.03109005,0.0455474,-0.06373254,0.01667062,-0.07591651,-0.03399968,0.08722214,0.03914326,-0.03165407,-0.09672864,0.04520046,0.0478158,-0.04968726,-0.05602811,-0.087354,-0.03489713,0.01576718,0.00301461,-0.00542207,0.00239186,0.04755903,-0.00947173,0.07656638,0.00096859,0.04385934,0.01887933,0.0077257],"last_embed":{"hash":"1sdoqa9","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"1sdoqa9","at":1753423493541},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{5}","lines":[280,280],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10858507,-0.05160066,0.00047622,-0.02785253,-0.01567406,0.03408622,-0.01248663,0.03005688,0.01126575,-0.02698155,-0.0194927,-0.03207749,0.04619611,-0.02857668,-0.0043965,-0.03444535,0.01749318,0.04473036,-0.05429549,-0.00564008,0.07903579,-0.00671939,-0.05370466,-0.0788863,0.04662047,-0.01336593,-0.05173437,-0.04832369,-0.04357103,-0.19951239,0.0290888,0.01679538,0.00833984,-0.02996318,-0.10104159,0.0095277,-0.01193843,0.04072969,-0.04406941,0.03142326,0.0279029,0.04347557,-0.0200017,-0.02630342,0.02376974,-0.05379881,0.02436673,0.00113342,0.02675336,0.00531421,-0.05682357,0.01550304,-0.00724419,0.02056407,0.0613947,0.03863786,0.06128363,0.09983581,0.02225813,0.0497361,0.03812662,0.07785229,-0.21666527,0.05232686,0.00824933,0.00736946,-0.04306931,-0.03214693,0.022568,0.0045281,-0.00069407,0.03118455,0.00621916,0.06351627,0.02075894,-0.04627175,-0.0535519,-0.06286509,-0.03563122,0.00935605,-0.04374015,-0.01532249,-0.01626122,-0.00910471,0.03008054,0.04968692,0.030102,0.05625074,0.07818687,-0.06413854,0.03414174,0.01750644,0.05656926,0.05779208,0.01803779,0.00865702,0.04767506,-0.05307966,-0.00355443,0.12737808,0.0060758,-0.02575274,-0.00146857,0.02880615,0.06638875,-0.02497904,-0.02076859,-0.01711,-0.02918884,0.04575596,0.05369569,0.00122907,0.00323345,-0.04933165,-0.03674851,-0.05249991,-0.02390141,-0.0055941,0.00584149,-0.00195797,-0.10368574,-0.00394071,0.02406697,0.00909194,0.01883901,-0.01146265,0.02209683,0.06314164,0.02006052,0.05141931,0.03592642,0.04226561,-0.14580441,-0.04732677,-0.02820168,-0.00156587,0.00262823,-0.04998676,0.00933055,0.03908136,-0.00425883,-0.0344308,0.04098193,-0.10408096,-0.00591196,0.06962947,-0.0052467,0.0221082,0.00649544,-0.0015091,-0.02088649,0.00803977,0.01690041,-0.04876969,0.02420821,-0.00227571,0.12262293,0.0466306,-0.03472875,0.05531092,-0.04714712,-0.06049456,-0.02219145,0.13857831,-0.00368696,-0.09945984,-0.01421152,0.05147737,-0.02853009,-0.05915356,0.00330237,0.00851003,-0.04604522,0.00657731,0.0800735,-0.05420899,-0.03871716,-0.03710876,-0.02332737,-0.00417305,0.0032552,-0.02822164,-0.02105719,0.00443639,-0.0369865,-0.09548741,0.02974932,-0.06956085,0.02199018,0.07247791,-0.02850405,-0.03305504,-0.01525194,-0.02325963,-0.01538968,-0.02388274,-0.06871062,-0.06334342,0.05963247,-0.00977319,-0.03240617,0.01590529,0.01360583,0.02000559,-0.03413638,0.04707016,-0.06605981,-0.07135767,0.05642785,0.00287229,-0.00270642,0.04239787,0.06587262,0.08640834,-0.08708835,0.04371274,0.0338071,0.02657127,-0.00475112,0.04378073,-0.00206939,0.0429868,-0.09842274,-0.20828131,-0.01113145,-0.05075,-0.00939988,0.00970415,-0.02858893,0.02747849,-0.02534617,-0.00721225,0.07424854,0.11189654,-0.07305513,0.0286375,0.03088621,-0.00025555,-0.01950704,-0.07826378,-0.06145237,-0.04261926,0.07464296,0.00435531,0.00054277,0.00800479,-0.0788367,0.02199584,-0.03150667,0.12720731,0.00263157,0.00181881,-0.03418546,0.06335822,0.01876014,-0.01600829,-0.03445713,0.04183234,0.04372907,-0.05976219,0.02359259,-0.0223493,0.01966656,-0.07610169,0.0583658,0.00239642,-0.06975378,-0.02132759,-0.00346344,0.01662769,-0.03060512,-0.00866132,0.04893683,0.02283916,-0.0075177,0.06544116,0.00742481,0.06318866,-0.00722687,-0.05231174,0.02568656,-0.01960167,0.02762845,0.01532494,-0.0488367,0.09376577,-0.04253227,0.0489609,0.0015952,-0.0160862,-0.04477351,-0.00382498,-0.00774458,-0.02245679,0.1041425,-0.00146506,0.08053992,-0.00519949,0.02130025,0.03527531,-0.04492667,0.00692785,-0.01710398,-0.02982668,-0.06937085,0.04624627,0.07492284,0.04587037,0.00727374,0.05106904,0.01504835,0.03754063,0.02314569,0.01657426,0.00818755,-0.02051838,0.03720228,0.03817121,0.05605527,-0.24862862,0.0239418,-0.02043653,0.02507968,-0.02056269,-0.01666356,0.02449241,-0.05281734,0.04920724,-0.04677531,0.09470689,0.03428277,0.01822876,-0.10000948,0.00437621,-0.00939669,0.04747052,-0.01203024,0.06276289,0.00010499,0.03366917,0.03162965,0.24216591,0.01986859,-0.01171405,0.02539667,0.02130241,-0.02044904,0.03205638,0.02993362,0.0211344,0.02858162,0.07669669,0.01181461,-0.0419669,0.04413728,-0.02740889,-0.00801181,0.00050965,0.01794272,-0.06406883,0.0273899,-0.04329244,0.01813879,0.09120891,0.01294156,-0.01812628,-0.09611063,0.05984699,0.04749824,-0.07656433,-0.07013705,-0.05746681,-0.01196428,0.0370093,0.01351216,0.01685568,-0.00586277,0.0160061,-0.02936579,0.04815661,0.013572,0.05306092,-0.0146508,-0.00208703],"last_embed":{"hash":"fkn5pd","tokens":302}}},"text":null,"length":0,"last_read":{"hash":"fkn5pd","at":1753423493573},"key":"notes/saliu/Lottery Software Utilities, Lotto Tools Programs.md#Lottery Software Utilities, Lotto Tools Programs#[<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)#{18}","lines":[294,299],"size":630,"outlinks":[{"title":"Run very useful lottery statistical tools, utilities, apps, lotto combination generators.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Thanks for visiting the site of lottery, lotto, best software programs, utilities.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
