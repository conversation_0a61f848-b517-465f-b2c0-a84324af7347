
"smart_sources:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md": {"path":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"qtowty","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363606854,"size":15932,"at":1753423416500,"hash":"qtowty"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Software Positional Frequency, Lottery Strategy":[8,193],"#Lotto Software Positional Frequency, Lottery Strategy#{1}":[10,34],"#Lotto Software Positional Frequency, Lottery Strategy#{2}":[35,36],"#Lotto Software Positional Frequency, Lottery Strategy#{3}":[37,38],"#Lotto Software Positional Frequency, Lottery Strategy#{4}":[39,40],"#Lotto Software Positional Frequency, Lottery Strategy#{5}":[41,41],"#Lotto Software Positional Frequency, Lottery Strategy#{6}":[42,42],"#Lotto Software Positional Frequency, Lottery Strategy#{7}":[43,43],"#Lotto Software Positional Frequency, Lottery Strategy#{8}":[44,44],"#Lotto Software Positional Frequency, Lottery Strategy#{9}":[45,45],"#Lotto Software Positional Frequency, Lottery Strategy#{10}":[46,46],"#Lotto Software Positional Frequency, Lottery Strategy#{11}":[47,47],"#Lotto Software Positional Frequency, Lottery Strategy#{12}":[48,48],"#Lotto Software Positional Frequency, Lottery Strategy#{13}":[49,49],"#Lotto Software Positional Frequency, Lottery Strategy#{14}":[50,51],"#Lotto Software Positional Frequency, Lottery Strategy#{15}":[52,55],"#Lotto Software Positional Frequency, Lottery Strategy#{16}":[56,56],"#Lotto Software Positional Frequency, Lottery Strategy#{17}":[57,58],"#Lotto Software Positional Frequency, Lottery Strategy#{18}":[59,66],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software":[67,89],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{1}":[69,83],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{2}":[84,85],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{3}":[86,89],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01":[90,115],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{1}":[92,95],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{2}":[96,97],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{3}":[98,101],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{4}":[102,102],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{5}":[103,103],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{6}":[104,104],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{7}":[105,105],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{8}":[106,107],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{9}":[108,115],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1":[116,132],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{1}":[118,119],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{2}":[120,120],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{3}":[121,121],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{4}":[122,122],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{5}":[123,123],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{6}":[124,124],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{7}":[125,126],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{8}":[127,132],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version":[133,193],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{1}":[135,138],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{2}":[139,139],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{3}":[140,140],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{4}":[141,141],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{5}":[142,142],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{6}":[143,144],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{7}":[145,146],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{8}":[147,147],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{9}":[148,148],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{10}":[149,149],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{11}":[150,150],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{12}":[151,151],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{13}":[152,152],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{14}":[153,153],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{15}":[154,154],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{16}":[155,155],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{17}":[156,156],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{18}":[157,157],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{19}":[158,158],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{20}":[159,159],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{21}":[160,160],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{22}":[161,161],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{23}":[162,163],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{24}":[164,167],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{25}":[168,168],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{26}":[169,169],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{27}":[170,170],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{28}":[171,171],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{29}":[172,172],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{30}":[173,174],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{31}":[175,182],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{32}":[183,183],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{33}":[184,184],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{34}":[185,185],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{35}":[186,187],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{36}":[188,193]},"outlinks":[{"title":"_**Excel Spreadsheets Lotto Lottery Software Systems Strategies Programming**_","target":"https://saliu.com/Newsgroups.htm","line":96},{"title":"Original software generates lotto combinations position by position.","target":"https://saliu.com/images/lotto-ranges.gif","line":112},{"title":"Lotto software generates from ranges of numbers in 5 or 6 positions.","target":"https://saliu.com/images/lotto-positions.gif","line":114},{"title":"Lotto combinations, Powerball, Mega Millions, Euromillions generate from groups of numbers by lines.","target":"https://saliu.com/images/lotto-positions.gif","line":177},{"title":"The lotto software also creates gambling systems from skips of numbers.","target":"https://saliu.com/images/skip-lotto5.gif","line":179},{"title":"This Powerball, Mega Millions skip system hit the jackpot several times.","target":"https://saliu.com/images/combinations-lotto.5.gif","line":181},{"title":"_**Lottery Strategy and Software Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":183},{"title":"_**Skip Systems, Software: Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":184},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":185},{"title":"_**Powerball Strategy, Systems: Numbers, Drawings, Skips, Software**_","target":"https://saliu.com/powerball-systems.html","line":186},{"title":"Download your lottery software to create lotto systems based on positional frequency.","target":"https://forums.saliu.com/HLINE.gif","line":188},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":190},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":190},{"title":"Search","target":"https://saliu.com/Search.htm","line":190},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto skips.","target":"https://forums.saliu.com/HLINE.gif","line":192}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto","software","lottery","strategy","frequency","position","numbers"],"source":"https://forums.saliu.com/lotto-software-position-frequency.html","author":null}},
"smart_sources:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md": {"path":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08578695,-0.04866694,-0.04209367,-0.06010454,-0.06339954,0.06654935,-0.00531532,0.0234076,0.0679937,-0.03846379,0.01735413,0.01993252,0.07300787,-0.00025495,-0.01588649,-0.02615287,0.01330482,-0.00697492,-0.02057399,-0.00674299,0.09190375,-0.04166535,-0.03308759,-0.08392891,0.06987028,-0.00342454,-0.06878567,-0.043338,-0.03926013,-0.22802576,0.03371085,0.03977617,0.05938392,-0.03978956,-0.09118943,-0.04776236,-0.05057319,0.06947923,-0.07291079,0.04680919,0.00690071,0.02618383,-0.00644139,-0.01543074,0.00680857,-0.02294178,0.01383865,0.00183073,-0.0210749,0.01146675,-0.06921902,0.00440141,-0.03395541,0.05490438,0.06685819,0.03288675,0.05110263,0.06454941,0.04683331,0.05412992,0.08186696,0.01976931,-0.21054967,0.05064754,-0.01566758,0.02557107,-0.00753593,-0.02695758,-0.00415907,0.01658113,0.03029785,0.0300015,-0.00920432,0.05397165,0.04999373,-0.02170854,-0.02488976,-0.0456056,-0.03167565,0.00347537,-0.02711471,-0.04422784,-0.00151841,-0.03555654,0.05285817,0.05274169,0.07694607,0.05383138,0.08711834,-0.03635604,0.05355217,0.04085187,0.02183957,0.00248822,0.02096353,0.00965759,0.03098661,0.02751436,-0.03767242,0.10619735,-0.00041387,0.00671899,-0.06590828,0.02976461,0.05028931,-0.00955642,-0.03298394,-0.03858894,-0.01485453,0.03258331,0.04338691,0.07714874,0.04063853,-0.06215527,-0.02768464,0.00980465,0.01094764,0.01257188,0.02777002,0.00874362,-0.03449187,0.02203362,-0.01412046,-0.00756051,0.03986706,0.04144496,0.03073474,0.05953449,0.02284264,0.00557458,0.0381389,-0.03651961,-0.14944036,-0.02724145,-0.01865445,-0.01029161,-0.01167304,0.0058561,0.00731709,0.0263923,-0.02614283,-0.03434287,0.06900982,-0.10323475,-0.01833006,0.11552164,-0.01109002,0.01654708,0.00450824,-0.00002943,0.00252047,-0.01311793,-0.06197255,-0.08769114,-0.02118383,0.01284072,0.05964325,0.07796258,-0.03689087,-0.01768169,-0.04266589,-0.00724083,0.00856526,0.1101968,-0.01057807,-0.10918371,-0.05129154,0.07298828,-0.05554317,-0.06419829,0.00016499,0.00324471,-0.02699495,0.02095396,0.05748337,-0.02739982,-0.03810022,-0.02283858,0.0110739,0.00751195,0.04093655,-0.02291836,-0.03753171,-0.01684492,-0.04421222,-0.04725116,0.02310268,-0.03671476,0.03799611,-0.01880134,-0.04376093,-0.01185109,-0.02995515,-0.00637202,-0.024325,-0.03024363,-0.04862311,-0.00032725,0.07434195,0.01807523,0.00182356,-0.00823092,0.00136841,0.03620626,-0.02709926,0.05074664,0.04759597,-0.07785718,0.08032472,0.02452512,-0.01851158,-0.01125674,0.04801425,0.05278834,-0.06394803,0.00047888,-0.0025631,0.01780069,-0.00007544,0.02491815,-0.02333371,0.02884155,-0.08304139,-0.21227935,-0.0358514,-0.03409743,0.04001434,-0.01504764,0.00626806,0.03171837,-0.01779035,0.07560021,0.05402622,0.10574976,-0.06657684,-0.04237044,0.04704955,-0.03262067,-0.0492021,-0.07848579,-0.04686477,-0.02204756,0.02551571,0.0240001,-0.00047366,-0.05245585,-0.06966867,0.09019539,-0.03499042,0.10721476,0.03426783,0.02097689,-0.01120215,0.06322865,-0.02782644,0.00004871,-0.01171483,-0.00950648,0.01065752,-0.00501897,-0.00823423,-0.01861792,0.01107097,-0.10344996,-0.00305433,0.03821712,-0.10419014,-0.03437289,0.01623848,-0.00432711,0.00507607,-0.00575867,0.07186409,0.0745337,-0.00838022,0.04247204,0.01398594,0.04870134,-0.06458113,-0.06204489,-0.00618799,-0.03039739,0.03820391,-0.02953523,-0.06150445,0.07790684,-0.00026363,-0.00514391,0.03340048,0.01423896,0.01072358,0.0296228,0.01475442,0.02013842,0.08736715,-0.00901474,0.01765307,0.02652561,0.01091429,0.0500848,-0.06510723,0.01242077,-0.01309731,-0.00070177,-0.03631691,0.04027735,0.03005016,0.02599017,-0.01495089,0.05718298,0.06563823,0.00366682,0.02283768,-0.00485718,0.05364296,-0.02819705,-0.0090043,0.00984543,0.01593623,-0.25473309,0.01708704,-0.05700563,0.06412914,-0.0260041,-0.02740551,0.02908553,0.00672208,-0.00889879,0.01228322,0.00140327,0.03452831,0.0101932,-0.07392247,-0.00457589,-0.03189855,-0.00369982,-0.05797748,0.06814041,-0.00864064,0.0588058,0.03148397,0.24960467,0.00725943,0.00635712,0.03070128,0.00776586,0.01794429,-0.02662933,0.0240661,-0.00608768,-0.0371341,0.09760524,-0.01054501,-0.04775163,-0.01060846,0.02065685,0.01035988,-0.041916,0.00756507,-0.04919795,0.00179463,-0.00234227,0.01909959,0.15847428,-0.0032692,-0.03063906,-0.08474793,0.05594148,0.05550161,-0.10374226,-0.05930632,-0.05137851,-0.02436686,-0.01923536,0.0355512,0.01992185,0.02870845,0.001727,-0.02420797,0.01510841,-0.01925611,0.05989719,0.03572055,0.013536],"last_embed":{"hash":"qtowty","tokens":452}}},"last_read":{"hash":"qtowty","at":1753423528694},"class_name":"SmartSource","last_import":{"mtime":1753363606854,"size":15932,"at":1753423416500,"hash":"qtowty"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Software Positional Frequency, Lottery Strategy":[8,193],"#Lotto Software Positional Frequency, Lottery Strategy#{1}":[10,34],"#Lotto Software Positional Frequency, Lottery Strategy#{2}":[35,36],"#Lotto Software Positional Frequency, Lottery Strategy#{3}":[37,38],"#Lotto Software Positional Frequency, Lottery Strategy#{4}":[39,40],"#Lotto Software Positional Frequency, Lottery Strategy#{5}":[41,41],"#Lotto Software Positional Frequency, Lottery Strategy#{6}":[42,42],"#Lotto Software Positional Frequency, Lottery Strategy#{7}":[43,43],"#Lotto Software Positional Frequency, Lottery Strategy#{8}":[44,44],"#Lotto Software Positional Frequency, Lottery Strategy#{9}":[45,45],"#Lotto Software Positional Frequency, Lottery Strategy#{10}":[46,46],"#Lotto Software Positional Frequency, Lottery Strategy#{11}":[47,47],"#Lotto Software Positional Frequency, Lottery Strategy#{12}":[48,48],"#Lotto Software Positional Frequency, Lottery Strategy#{13}":[49,49],"#Lotto Software Positional Frequency, Lottery Strategy#{14}":[50,51],"#Lotto Software Positional Frequency, Lottery Strategy#{15}":[52,55],"#Lotto Software Positional Frequency, Lottery Strategy#{16}":[56,56],"#Lotto Software Positional Frequency, Lottery Strategy#{17}":[57,58],"#Lotto Software Positional Frequency, Lottery Strategy#{18}":[59,66],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software":[67,89],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{1}":[69,83],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{2}":[84,85],"#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{3}":[86,89],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01":[90,115],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{1}":[92,95],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{2}":[96,97],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{3}":[98,101],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{4}":[102,102],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{5}":[103,103],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{6}":[104,104],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{7}":[105,105],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{8}":[106,107],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{9}":[108,115],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1":[116,132],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{1}":[118,119],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{2}":[120,120],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{3}":[121,121],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{4}":[122,122],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{5}":[123,123],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{6}":[124,124],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{7}":[125,126],"#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{8}":[127,132],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version":[133,193],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{1}":[135,138],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{2}":[139,139],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{3}":[140,140],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{4}":[141,141],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{5}":[142,142],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{6}":[143,144],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{7}":[145,146],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{8}":[147,147],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{9}":[148,148],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{10}":[149,149],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{11}":[150,150],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{12}":[151,151],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{13}":[152,152],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{14}":[153,153],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{15}":[154,154],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{16}":[155,155],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{17}":[156,156],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{18}":[157,157],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{19}":[158,158],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{20}":[159,159],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{21}":[160,160],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{22}":[161,161],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{23}":[162,163],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{24}":[164,167],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{25}":[168,168],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{26}":[169,169],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{27}":[170,170],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{28}":[171,171],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{29}":[172,172],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{30}":[173,174],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{31}":[175,182],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{32}":[183,183],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{33}":[184,184],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{34}":[185,185],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{35}":[186,187],"#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{36}":[188,193]},"outlinks":[{"title":"_**Excel Spreadsheets Lotto Lottery Software Systems Strategies Programming**_","target":"https://saliu.com/Newsgroups.htm","line":96},{"title":"Original software generates lotto combinations position by position.","target":"https://saliu.com/images/lotto-ranges.gif","line":112},{"title":"Lotto software generates from ranges of numbers in 5 or 6 positions.","target":"https://saliu.com/images/lotto-positions.gif","line":114},{"title":"Lotto combinations, Powerball, Mega Millions, Euromillions generate from groups of numbers by lines.","target":"https://saliu.com/images/lotto-positions.gif","line":177},{"title":"The lotto software also creates gambling systems from skips of numbers.","target":"https://saliu.com/images/skip-lotto5.gif","line":179},{"title":"This Powerball, Mega Millions skip system hit the jackpot several times.","target":"https://saliu.com/images/combinations-lotto.5.gif","line":181},{"title":"_**Lottery Strategy and Software Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":183},{"title":"_**Skip Systems, Software: Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":184},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":185},{"title":"_**Powerball Strategy, Systems: Numbers, Drawings, Skips, Software**_","target":"https://saliu.com/powerball-systems.html","line":186},{"title":"Download your lottery software to create lotto systems based on positional frequency.","target":"https://forums.saliu.com/HLINE.gif","line":188},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":190},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":190},{"title":"Search","target":"https://saliu.com/Search.htm","line":190},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto skips.","target":"https://forums.saliu.com/HLINE.gif","line":192}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto","software","lottery","strategy","frequency","position","numbers"],"source":"https://forums.saliu.com/lotto-software-position-frequency.html","author":null}},"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07303542,-0.04177859,-0.03637153,-0.06451935,-0.05332442,0.06008587,0.00289182,0.02057205,0.06628406,-0.03301699,0.01583395,0.0214531,0.0778259,0.00087307,-0.01609064,-0.01541125,0.0101398,-0.01009864,-0.02375962,-0.00902828,0.09271504,-0.04138141,-0.0395712,-0.08116776,0.06593139,-0.00331255,-0.0741016,-0.04265109,-0.04696796,-0.23217674,0.03707737,0.04590296,0.06596462,-0.02946704,-0.086403,-0.05426531,-0.06293244,0.06661449,-0.0822895,0.03983558,0.0106862,0.02514844,-0.00804922,-0.01730758,0.00425579,-0.02944882,0.00822742,0.00337289,-0.03012965,0.00522526,-0.07115766,0.00190694,-0.02792498,0.05571689,0.05638479,0.02818741,0.05230684,0.06922206,0.05036892,0.05575852,0.08402962,0.02268468,-0.21050727,0.04009612,-0.01400366,0.02930797,-0.00692266,-0.02629919,-0.00424724,0.01839615,0.03089661,0.02939792,-0.00162525,0.05559228,0.04861132,-0.02127805,-0.0230981,-0.03552144,-0.03095754,0.01560572,-0.03591549,-0.04273853,0.00130722,-0.03195886,0.04733656,0.04424654,0.06982305,0.04453547,0.09083062,-0.02730002,0.04818219,0.03997399,0.01107194,-0.00584071,0.02652219,0.0064099,0.02693079,0.02177894,-0.03444845,0.11582872,0.00490357,0.0042764,-0.06005487,0.02545554,0.0472618,-0.01634046,-0.03994787,-0.03978674,-0.01819736,0.03572949,0.0428209,0.07924405,0.04260113,-0.04900364,-0.02618273,0.00526014,0.01795944,0.02127169,0.02907242,0.01482604,-0.03412173,0.0201911,-0.01309803,-0.00885224,0.03740929,0.04094473,0.02048872,0.0637409,0.02314047,0.00717747,0.04129931,-0.03691662,-0.1461117,-0.02197486,-0.01201757,-0.015092,-0.00842896,0.02219714,0.00360971,0.03055292,-0.02186694,-0.03050253,0.06657337,-0.09850267,-0.00949124,0.1125454,-0.01119868,0.01999367,-0.00024687,-0.00884266,0.01080683,-0.01186976,-0.07055891,-0.09237532,-0.01388529,0.00923323,0.06147778,0.07399568,-0.03500928,-0.01384935,-0.05338868,0.00288503,0.00833345,0.11505912,-0.00688303,-0.09748019,-0.05150806,0.08077861,-0.05576535,-0.06026726,0.0047927,0.01205045,-0.02718057,0.01796582,0.04007297,-0.03215244,-0.04367943,-0.02317867,0.01951724,0.01376602,0.05267563,-0.02305353,-0.04932944,-0.01567598,-0.02817038,-0.04881507,0.0134001,-0.03508954,0.03445155,-0.02571791,-0.04705695,-0.00587019,-0.03336098,-0.01223927,-0.02602238,-0.03578691,-0.04896806,0.00136442,0.07116225,0.01124448,0.01284984,-0.00481654,0.00527383,0.04317111,-0.01511823,0.04794576,0.04067466,-0.07917481,0.08175378,0.02180461,-0.0173032,-0.02209628,0.04306869,0.06011316,-0.06762277,-0.00187327,-0.00374511,0.02711026,-0.00090622,0.02867486,-0.02593426,0.03100597,-0.08093816,-0.21616407,-0.0415549,-0.02362965,0.04464035,-0.01596139,0.00880657,0.02879388,-0.02570191,0.07358386,0.05463805,0.10669748,-0.06214124,-0.0504991,0.05643584,-0.03430869,-0.04849971,-0.08837207,-0.03384962,-0.01819963,0.02378353,0.01859399,-0.00428622,-0.05408544,-0.07612889,0.09491814,-0.02970671,0.1106302,0.03113686,0.02366723,-0.0247491,0.0504553,-0.03391312,0.00805387,0.00039527,-0.00656639,0.01270597,0.00048656,-0.01507465,-0.01437841,0.00691443,-0.10251068,0.00351473,0.03623803,-0.0985798,-0.0378108,0.01105668,0.00225872,0.00166652,0.0030493,0.08324417,0.07407818,-0.01511559,0.05273303,0.01770665,0.04814881,-0.06986811,-0.06365093,0.00527475,-0.02814338,0.04458191,-0.02128113,-0.06761421,0.07830545,-0.00054417,0.00093797,0.02634439,0.01758586,0.01198288,0.03300722,0.01009046,0.02379845,0.09798516,-0.01889554,0.01039832,0.03817215,0.01354006,0.04482436,-0.07620563,0.00948749,-0.01790402,0.00290952,-0.03363552,0.03413823,0.02344376,0.01230579,-0.01979509,0.05275613,0.06892179,0.00476803,0.02666751,-0.00242915,0.05570702,-0.02397856,-0.00941141,-0.00412175,0.02065146,-0.25090387,0.01407845,-0.06074041,0.053576,-0.03195053,-0.04003993,0.03850197,0.01519727,-0.00354425,0.00930003,-0.00145877,0.03508628,0.01349146,-0.0750824,-0.0057807,-0.03463494,0.00850178,-0.05995134,0.06104757,-0.01428669,0.0596654,0.02261823,0.24552298,0.00030061,0.00881152,0.03768167,0.00397652,0.02178263,-0.02563435,0.02044108,-0.00819338,-0.04226596,0.10478722,-0.00930777,-0.03778649,-0.01200502,0.02232553,0.00941428,-0.03998387,0.0015329,-0.04688305,0.00140625,0.00203439,0.0141327,0.14925399,-0.01025484,-0.03615778,-0.07736425,0.05171125,0.06320054,-0.10219522,-0.05011086,-0.05010358,-0.03435886,-0.01608705,0.03252356,0.00794681,0.03378566,0.00287379,-0.02515665,0.00781421,-0.02012162,0.0519942,0.03675235,0.00519021],"last_embed":{"hash":"np7yl5","tokens":421}}},"text":null,"length":0,"last_read":{"hash":"np7yl5","at":1753423526655},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy","lines":[8,193],"size":15514,"outlinks":[{"title":"_**Excel Spreadsheets Lotto Lottery Software Systems Strategies Programming**_","target":"https://saliu.com/Newsgroups.htm","line":89},{"title":"Original software generates lotto combinations position by position.","target":"https://saliu.com/images/lotto-ranges.gif","line":105},{"title":"Lotto software generates from ranges of numbers in 5 or 6 positions.","target":"https://saliu.com/images/lotto-positions.gif","line":107},{"title":"Lotto combinations, Powerball, Mega Millions, Euromillions generate from groups of numbers by lines.","target":"https://saliu.com/images/lotto-positions.gif","line":170},{"title":"The lotto software also creates gambling systems from skips of numbers.","target":"https://saliu.com/images/skip-lotto5.gif","line":172},{"title":"This Powerball, Mega Millions skip system hit the jackpot several times.","target":"https://saliu.com/images/combinations-lotto.5.gif","line":174},{"title":"_**Lottery Strategy and Software Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":176},{"title":"_**Skip Systems, Software: Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":177},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":178},{"title":"_**Powerball Strategy, Systems: Numbers, Drawings, Skips, Software**_","target":"https://saliu.com/powerball-systems.html","line":179},{"title":"Download your lottery software to create lotto systems based on positional frequency.","target":"https://forums.saliu.com/HLINE.gif","line":181},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":183},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":183},{"title":"Search","target":"https://saliu.com/Search.htm","line":183},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto skips.","target":"https://forums.saliu.com/HLINE.gif","line":185}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07200647,-0.0416971,-0.03941625,-0.06456351,-0.05739221,0.06017599,-0.00131905,0.02105557,0.06555495,-0.0338189,0.01681244,0.02331059,0.07963141,-0.00049056,-0.01587584,-0.01550651,0.01004118,-0.00906453,-0.02131072,-0.01009643,0.0929614,-0.03949961,-0.03966547,-0.08186489,0.06516492,-0.00118863,-0.0729739,-0.03997574,-0.04328714,-0.23294902,0.03541985,0.04355308,0.0680507,-0.03125247,-0.08778981,-0.05523755,-0.06295812,0.06680325,-0.08417332,0.04231687,0.00941142,0.02333821,-0.00642562,-0.01736911,0.00602546,-0.03018915,0.0121723,0.00394411,-0.03120306,0.00492647,-0.07108538,0.0024095,-0.02821359,0.05669686,0.05725465,0.02821185,0.0519766,0.06881882,0.05035502,0.05376228,0.08710104,0.02232202,-0.20712084,0.03991299,-0.01529595,0.02880567,-0.00882297,-0.02449014,-0.00495904,0.01856774,0.02907885,0.027637,-0.00081927,0.0560716,0.04856384,-0.02109165,-0.02601256,-0.03484255,-0.03203033,0.01794553,-0.03487517,-0.04516617,0.0004202,-0.02854434,0.04766741,0.04556358,0.07381803,0.04441664,0.09108212,-0.02808098,0.04922264,0.03953433,0.01068707,-0.00532729,0.0303814,0.00732138,0.02685023,0.02308819,-0.0327885,0.11569793,0.00563877,0.00373572,-0.0576087,0.02239898,0.04688377,-0.01582451,-0.03775522,-0.03792193,-0.01724571,0.03536553,0.03999213,0.07993311,0.03782199,-0.04812066,-0.02525135,0.00599672,0.01723596,0.022657,0.02585062,0.01685561,-0.03393048,0.01831117,-0.01377933,-0.01023745,0.03973089,0.03850056,0.02104216,0.06163832,0.02453246,0.00654422,0.03963739,-0.03667571,-0.14557807,-0.02085524,-0.01228471,-0.01669361,-0.01028096,0.02288414,0.00352764,0.03017976,-0.02107399,-0.02825061,0.06648013,-0.09757701,-0.01127834,0.11346921,-0.00973268,0.02041992,-0.00089792,-0.00951089,0.00994844,-0.0137978,-0.07245863,-0.09381718,-0.01367514,0.0102695,0.06128419,0.07350193,-0.0341481,-0.01252966,-0.05368807,0.00080026,0.00790706,0.11938188,-0.00829539,-0.09453657,-0.05041897,0.07890311,-0.05646709,-0.06062872,0.00326267,0.01046817,-0.02896932,0.0172274,0.03805521,-0.03264991,-0.04402518,-0.02408954,0.02011068,0.01202089,0.05566594,-0.02339983,-0.04996596,-0.01466892,-0.02744808,-0.0477594,0.0131768,-0.03684193,0.0339129,-0.02616897,-0.04543828,-0.00340961,-0.0343651,-0.01489168,-0.02644745,-0.03577035,-0.04730797,0.00027585,0.07178889,0.01013612,0.01375718,-0.00488663,0.00616021,0.04689869,-0.01768032,0.04895983,0.04243875,-0.08086289,0.08349411,0.02221237,-0.0199472,-0.01942609,0.04487171,0.0578369,-0.06776222,-0.00126909,-0.00303186,0.0255535,-0.00289706,0.02456922,-0.02368129,0.03238598,-0.08242927,-0.21173598,-0.04403973,-0.02420957,0.04817851,-0.01552148,0.00936606,0.03025931,-0.02610071,0.0716539,0.0534813,0.10786156,-0.06586303,-0.04893209,0.06020355,-0.03518859,-0.04868893,-0.08691917,-0.03526902,-0.01698714,0.02404932,0.0176454,-0.00441277,-0.05262231,-0.07621159,0.09723899,-0.03261727,0.10752273,0.02898468,0.02755596,-0.02348554,0.04994613,-0.03659739,0.00835017,0.00089374,-0.00679582,0.01313551,0.00138266,-0.01581992,-0.01653096,0.00863636,-0.10144556,0.00135926,0.03574641,-0.09673274,-0.03531822,0.0098331,0.00145175,-0.00155176,0.0034036,0.0818129,0.07247608,-0.01362641,0.0528334,0.01672621,0.0475911,-0.06943744,-0.06258002,0.00817141,-0.02948701,0.04186266,-0.02321835,-0.06577295,0.07686262,-0.00050758,0.00171792,0.02905887,0.01807719,0.01077869,0.03414762,0.01252207,0.02371143,0.09645274,-0.02077236,0.01090527,0.03614569,0.01253777,0.04609856,-0.07940536,0.00988841,-0.01668327,0.00269831,-0.03324628,0.03574798,0.02435831,0.01266665,-0.02082431,0.05609843,0.06916825,0.00605432,0.02676999,-0.00493172,0.05561689,-0.02589342,-0.01057447,-0.00534238,0.01973108,-0.24965817,0.0131014,-0.06082395,0.05552286,-0.0316209,-0.038769,0.03826818,0.01312348,-0.00426324,0.00983234,-0.00438386,0.03092604,0.0119532,-0.07376701,-0.00448151,-0.03067571,0.01207304,-0.05924838,0.06118151,-0.0142225,0.06099715,0.01955456,0.2463723,0.00076782,0.0064196,0.03887607,0.00203838,0.02146266,-0.02810445,0.02319905,-0.00684963,-0.0419836,0.10579836,-0.00759251,-0.03940363,-0.01213716,0.02433941,0.00809075,-0.04206749,0.00190886,-0.0451524,0.0012254,-0.00152869,0.01222742,0.14802648,-0.01157147,-0.03734099,-0.07863408,0.05208493,0.06417498,-0.10362773,-0.05138091,-0.04991578,-0.03353373,-0.01480192,0.03512698,0.00810046,0.03487852,0.00313944,-0.0259596,0.0055338,-0.02218858,0.05236483,0.03861377,0.00584615],"last_embed":{"hash":"14w3az0","tokens":380}}},"text":null,"length":0,"last_read":{"hash":"14w3az0","at":1753423526824},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{1}","lines":[10,34],"size":1571,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06960545,-0.04743502,-0.00908634,-0.05253268,-0.04938944,0.05769736,-0.00847405,-0.00055721,0.01854934,-0.03119353,0.02954304,0.00601496,0.07440774,-0.02539878,-0.02071603,-0.04325691,0.02444985,0.01294686,-0.01396492,-0.02607665,0.09576939,-0.02957989,-0.02796916,-0.08118643,0.0660216,0.0037394,-0.06321891,-0.08286424,-0.0308598,-0.24418426,0.04768829,0.03615326,0.04079691,-0.02639506,-0.04895203,-0.03722639,-0.05581539,0.09278456,-0.05064202,0.0414799,0.00931907,0.02625077,0.00411911,-0.01945855,0.00506983,-0.03750135,0.01426874,-0.00033471,0.01531067,0.02145185,-0.05168654,0.01532846,0.00404566,0.05950101,0.0330457,0.01343083,0.04713062,0.06130049,0.02804662,0.05877499,0.05392247,0.04117033,-0.20419462,0.01915176,0.01558625,0.02962958,-0.00258648,-0.01992186,-0.01827095,0.0490508,-0.00497618,0.00644182,-0.00203135,0.05676008,0.0732263,-0.04059896,-0.01604989,-0.0331108,-0.05228252,0.00300269,-0.04936606,-0.01751429,0.00999586,0.00259165,0.04583126,0.03172785,0.08356243,0.0651421,0.0802239,-0.03762832,0.07309669,0.06725702,0.01415572,0.0107737,0.04645327,0.01888006,0.03198237,0.00141983,-0.0353015,0.1165057,-0.00148036,0.01735545,-0.05045274,0.0194113,0.02717726,-0.04025609,-0.06114185,-0.03018048,-0.0005455,0.00891385,0.0226663,0.05622014,0.03444207,-0.06869611,-0.03259796,0.01842994,0.02001229,0.02630763,-0.01805518,0.00541823,-0.03741684,0.00644953,-0.00682118,0.02000575,0.02317457,-0.00919455,0.03015213,0.08869392,0.0476827,0.02628117,0.04303712,-0.02252853,-0.11929522,-0.0337254,-0.01531621,-0.01663212,-0.01783536,0.03168286,0.01204501,0.02636069,-0.03265034,-0.05024892,0.06699877,-0.05673485,-0.01904293,0.12381327,-0.00204094,0.02353469,-0.00093547,0.00105064,-0.01262724,0.01034129,-0.04950719,-0.04750336,0.00986772,0.0098329,0.02598354,0.06451578,-0.05622995,-0.01346929,-0.0485599,0.0094523,-0.02526739,0.13973983,-0.02541985,-0.07428365,-0.03256987,0.04627677,-0.04234859,-0.06064961,-0.01720212,0.00355364,-0.00003688,0.01047099,0.08328188,-0.02476832,-0.06107767,-0.02823781,0.01603886,0.00074719,0.05387949,-0.02986391,-0.05309913,0.0215524,-0.0449652,-0.04614068,0.01326086,-0.00914512,0.04546969,-0.00441113,-0.05406836,0.00054473,-0.03433418,-0.00367593,-0.02364483,-0.0358657,-0.022139,-0.00861333,0.08231457,-0.00046046,0.05639746,-0.00176174,0.00333205,0.03879536,-0.00901205,0.06094602,0.0481403,-0.06088558,0.09123053,0.04519106,-0.02563887,-0.00722385,0.04191468,0.03066349,-0.03941011,0.01468232,0.01164055,0.01815345,0.01574174,0.00204885,-0.01160601,0.05134631,-0.00996463,-0.21536674,-0.03594454,-0.02378866,0.05498436,-0.00753117,-0.00662918,-0.02418788,-0.02126565,0.05089517,0.068602,0.07212619,-0.07287434,-0.03751981,0.03749723,-0.03460029,-0.01059122,-0.07451382,-0.05850663,-0.06424928,0.02938367,0.00430892,-0.00629118,-0.0846225,-0.06911776,0.10513067,-0.03208369,0.10995477,0.00130711,0.03030071,0.00421217,0.06216555,-0.02780088,0.00698775,0.01061488,0.01588682,0.04395941,-0.00896526,-0.03503344,0.0027828,0.00748605,-0.08918983,0.01018902,0.00660176,-0.09834287,-0.054956,0.01973695,-0.02722228,0.0203806,0.0211933,0.09772158,0.05072584,-0.02027358,0.02203518,0.0399567,0.07970863,-0.03448842,-0.0721029,-0.00731487,-0.01906308,0.01139131,-0.02398465,-0.03368798,0.06206831,-0.01327824,-0.01803314,0.05538275,0.01702643,-0.01871856,0.01744257,0.01974481,0.03352282,0.09307619,-0.01328647,-0.01481535,-0.00199898,0.02670614,0.05909798,-0.04755436,-0.02284311,-0.04605677,-0.00098361,-0.02815547,0.06669904,0.02646441,0.00782868,-0.03459225,0.05822751,0.04625978,0.00686939,-0.00529118,-0.01370841,0.0570606,-0.0497924,0.04823844,0.00550757,0.0111251,-0.26494566,0.00883872,-0.09422863,0.08566094,-0.03857268,-0.02001295,0.01103923,0.00940256,-0.02668969,-0.01907308,-0.01271154,0.03044954,0.02390322,-0.07929738,0.00638806,-0.03259039,0.03349031,-0.08537889,0.06876278,-0.02139673,0.07173078,0.02958297,0.24246028,-0.00721591,-0.02297957,0.03217343,-0.02958912,0.03984789,-0.0191319,0.03359112,-0.00423357,-0.04074753,0.05532525,0.01161899,-0.02528364,-0.01843424,0.00453424,0.00107565,-0.0341159,0.0142565,-0.05898681,-0.02658537,-0.02054391,-0.04400688,0.16430303,0.00299895,-0.01482885,-0.08907732,0.05061379,0.0459067,-0.11676574,-0.01748267,-0.07596057,-0.02955265,-0.00198293,0.04190416,0.00474013,0.01014023,-0.00842558,-0.02653594,0.04200433,-0.02552067,0.05614629,0.04535495,-0.03014094],"last_embed":{"hash":"g9fp2j","tokens":204}}},"text":null,"length":0,"last_read":{"hash":"g9fp2j","at":1753423526946},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{15}","lines":[52,55],"size":768,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05800463,-0.03902175,-0.00668583,-0.04307744,-0.05451043,0.09489236,0.01930039,0.00711589,0.07277946,0.00273989,-0.00329415,-0.00071018,0.01431684,-0.00807516,-0.0346325,-0.01235441,-0.01796594,-0.0479269,-0.00651627,-0.00096195,0.06602474,-0.02378109,-0.03078903,-0.07765184,0.04006989,-0.02262273,-0.03813528,-0.06522392,-0.04344279,-0.21366271,0.03269557,0.04354132,0.00674342,-0.04334662,-0.04850676,-0.06215061,-0.05733167,0.08741628,-0.02740225,0.03550588,0.00879323,0.01124707,0.01658139,-0.04894613,0.01871056,-0.01183127,0.01415072,-0.02852545,0.02495873,-0.01567042,-0.02338845,-0.02593339,0.00787209,0.05118177,0.04223191,-0.01472918,0.0446955,0.05824735,0.09708169,0.02716787,0.04417831,0.00995884,-0.20438255,0.02853515,-0.02980524,0.02726307,-0.01610232,-0.007242,0.01442521,0.03399542,0.0232057,0.0157185,-0.01663945,0.05765388,0.09985974,-0.03858643,-0.03360574,-0.05150057,-0.04295068,-0.01087406,-0.03673728,-0.04770923,0.02047744,-0.02575415,0.04933107,0.02972868,0.06654597,0.04902445,0.08751944,0.00205562,0.0649075,0.07281587,0.01153492,0.01570738,-0.0191711,0.00606253,0.0573699,-0.0091401,-0.04828978,0.12130887,0.01304982,0.02936945,-0.0375969,0.07062997,0.021721,-0.02521739,-0.02479107,-0.02655765,-0.03680978,-0.0001634,0.0531901,0.06512687,0.05283592,-0.08196377,-0.04697973,0.0200703,0.02237839,0.00784865,0.02048941,0.02673412,-0.03404623,0.03946656,-0.0219601,0.00939843,0.01140702,0.07146384,0.06478868,0.09307055,0.02047796,0.00484727,0.01544187,-0.03437393,-0.12538472,-0.04439512,-0.05698043,0.00885583,0.00512912,-0.01274176,0.01089232,0.01936472,-0.02866837,-0.05655463,0.05459027,-0.10667429,-0.01203494,0.09193732,-0.01723262,0.02609726,-0.00968419,0.03533016,-0.02612195,-0.00369639,-0.02858693,-0.03465862,-0.04174845,0.02418128,0.00861449,0.10510971,-0.0245764,-0.01732508,-0.03087145,-0.06039539,-0.01249309,0.116353,-0.02406669,-0.09692423,-0.03730041,0.01928432,-0.02739244,-0.06430709,-0.01336212,0.01819231,-0.02329917,-0.01252211,0.05782959,-0.00606487,-0.04253996,0.00033777,-0.00902719,0.00725878,0.04433941,-0.00349954,-0.02543455,0.03464618,-0.04902051,-0.05332836,0.0347371,-0.0188146,0.04720676,0.04511321,-0.00704844,-0.03895219,-0.05742691,0.02322221,0.01058606,-0.0489239,-0.04336447,-0.00420136,0.07582276,-0.00438291,0.07139863,-0.01700208,-0.00465377,0.013425,-0.01244789,0.05085187,0.04867582,-0.06913158,0.10072754,0.02694396,-0.03085204,-0.0181159,0.04312899,0.02536483,-0.04561072,-0.01568314,-0.00258991,0.00884984,0.01712297,0.00248421,0.00683292,0.047039,-0.02408832,-0.23614968,-0.03214921,-0.00197314,0.05397398,-0.00272466,0.03256427,-0.00782212,0.02030255,0.0947318,0.05497838,0.06618424,-0.03000943,-0.05021534,0.03903526,-0.03831895,-0.03291587,-0.09296031,-0.07811297,-0.02350446,0.05233341,0.01364344,-0.00457314,-0.1163113,-0.04919834,0.10826747,-0.0541939,0.12684932,-0.00113202,-0.0230256,0.00053107,0.04568511,0.00863088,0.00239449,-0.01726284,0.03208512,0.05033839,-0.04569843,0.02149495,-0.03209599,-0.01318868,-0.08978897,0.00156356,0.0059064,-0.08382678,-0.03480207,0.00514833,-0.04036519,0.01750342,-0.00122752,0.07809242,0.06897476,-0.02590905,0.00925795,0.02006101,0.0675412,-0.04815283,-0.07745815,-0.01541485,-0.00750761,0.04578348,-0.01823963,-0.04149795,0.08870552,-0.01100692,0.00361336,0.03848798,0.00308129,0.0053796,0.03793162,0.03611656,0.01982206,0.07430616,0.01924574,-0.05632139,0.00502198,0.01419084,0.07014658,-0.04689389,-0.00873626,-0.01009298,0.00024358,-0.03946607,0.03479746,0.03495711,0.04721075,0.00272308,0.04430108,0.04672959,0.00985063,-0.00540392,-0.02391562,0.05942175,-0.03226998,0.00927054,-0.00237543,-0.00682245,-0.25472894,0.04177684,-0.0690054,0.10647184,-0.03304826,-0.00702377,0.00012994,0.03782409,-0.01785375,0.0033986,0.01656521,0.0200264,0.03218178,-0.06726243,-0.02713134,-0.03429404,-0.00260599,-0.06710496,0.06067179,-0.01444394,0.04097728,0.07259136,0.24049746,-0.01717286,0.00857776,-0.00564133,-0.00411328,0.01386231,-0.04148139,0.02650781,-0.00037358,-0.05623152,0.05321799,0.02182251,-0.05754217,-0.00373883,0.01904811,0.00249035,-0.02089982,0.00029751,-0.05567686,-0.00739798,-0.03221883,-0.01134964,0.13980795,0.05658114,-0.02481606,-0.09226505,0.04232552,0.02889222,-0.08316498,-0.08898479,-0.04074022,-0.03791923,-0.0175974,0.03462842,0.042829,0.00530282,-0.0017857,-0.01792545,0.00356021,-0.01281267,0.06621447,-0.02491078,0.00993048],"last_embed":{"hash":"1mc5p1a","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"1mc5p1a","at":1753423527011},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{16}","lines":[56,56],"size":352,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{17}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06217661,-0.06471134,-0.01337696,-0.05716346,-0.09459298,0.08867741,-0.00287072,-0.01109003,0.05956119,-0.02043031,0.00419571,0.00473731,0.02798879,0.00039367,0.00026447,0.00550587,0.00819291,-0.01908819,-0.05734149,-0.00253224,0.08547326,-0.03656668,-0.04130621,-0.0534481,0.0521729,-0.00507647,-0.05504058,-0.04945922,-0.03622803,-0.19629434,0.01916728,0.02899629,0.00170826,-0.08724306,-0.02238991,-0.06220759,-0.04991862,0.07875889,-0.04688936,0.00610304,-0.00242085,0.03130994,-0.00738353,-0.01413975,-0.02376569,-0.02373506,0.02054386,0.01922425,-0.00847045,0.00809643,-0.04760137,-0.02017118,0.00115234,0.03703181,0.03748657,0.0196319,0.03726763,0.08146236,0.00850482,0.06262657,0.04157224,0.07554665,-0.17569704,0.02290761,0.02020779,0.04589136,0.02530894,-0.03079589,-0.03563966,0.05041151,0.04119503,0.06375164,-0.01091127,0.04066466,0.04089884,-0.03330241,-0.02160716,-0.03478991,-0.03349191,-0.00560049,-0.04200054,-0.01959567,0.00315755,-0.01738367,0.01211763,0.01877674,0.06286366,0.07613487,0.04608861,-0.01243112,0.03619146,0.06413306,-0.0010744,0.01226155,0.03620382,-0.00476079,0.03070956,-0.01899858,0.02616154,0.10907646,-0.0165272,0.00986836,-0.0206966,0.03895025,0.01563566,-0.0327191,-0.02918082,-0.05059576,-0.05845883,0.02512791,0.07769521,0.02274783,0.06118492,-0.11434007,-0.04043673,0.0158999,0.00768539,-0.03436026,0.01307306,-0.00278002,-0.04147141,0.02168334,0.02399218,0.00787179,0.00653732,0.00868023,0.02724075,0.09471598,0.01918472,-0.01284927,0.04739076,-0.02994201,-0.12622328,-0.03227996,-0.0508716,-0.00077183,0.02930845,-0.0263912,0.00346335,-0.01630145,-0.03207714,-0.07649206,0.11388739,-0.12662105,-0.00941844,0.07956933,-0.02179187,0.02174732,-0.02554432,0.01086755,-0.04319615,-0.02308575,-0.04007474,-0.03297196,-0.00738334,0.00746886,0.03371527,0.08508316,-0.03552589,-0.00636579,-0.03060595,-0.01754653,0.02524729,0.12457088,-0.0043933,-0.07092128,-0.02035293,0.02142344,-0.03274702,-0.03109867,-0.03951554,0.030666,-0.01580497,0.01280969,0.104174,0.00320171,-0.05698796,0.00269462,-0.01080381,0.0087958,-0.00397802,-0.02504568,-0.02429369,0.02101074,-0.05470154,-0.05026992,0.02069716,-0.00282564,0.04814891,0.01496511,-0.02883746,-0.00964101,-0.00226075,0.01323404,-0.00042897,-0.02293906,-0.05701524,-0.04406615,0.08453311,-0.00517756,0.03148761,-0.04439652,-0.02358455,0.01713197,0.03711695,0.03894358,0.04476146,-0.04927741,0.10533316,-0.00233044,0.00417344,-0.031012,0.0551073,0.03640182,-0.0529668,0.02265914,0.00131268,0.00581821,0.03954491,0.0097365,0.00339007,0.01767455,-0.04076628,-0.22131599,-0.06013675,-0.02931725,0.04103708,0.01900105,0.02958342,-0.0005279,0.0365949,0.06761568,0.0548792,0.08165038,-0.0694481,-0.04145095,0.05365359,-0.02214325,-0.03245651,-0.09263539,-0.03699505,-0.03057618,0.0412373,-0.00820887,0.02318766,-0.07257322,-0.0531172,0.11838688,-0.01978851,0.1251598,0.01189623,-0.01200333,-0.03643855,0.04069027,-0.01487364,-0.00145799,0.03668674,0.03796035,0.05251781,-0.01069498,-0.05561436,-0.00556141,0.00758876,-0.09214676,0.04103376,-0.00727568,-0.10673094,-0.04886322,-0.02266563,-0.0494715,0.04711305,0.02789878,0.06041081,0.05782766,-0.00316723,0.02322275,0.01717193,0.08898985,-0.0585249,-0.10613628,-0.02786505,0.01687026,0.04374418,-0.02488462,-0.05608932,0.07081448,-0.00422768,0.01667878,0.03371593,0.02887493,0.00336922,0.00510774,-0.00486817,0.00386683,0.07291209,-0.01186648,-0.02630461,0.00644685,0.02432209,0.09284688,-0.05857861,-0.00621686,-0.01554724,0.01683192,-0.09779359,0.01086969,0.04818533,0.02217307,0.0076624,0.03495155,0.0681422,0.01189901,0.00516205,-0.03039861,0.04781117,-0.03582848,0.01691733,0.02652597,0.01760333,-0.25251859,0.05099985,-0.05060232,0.08060177,-0.04345499,0.00685248,0.00712774,0.01875062,-0.02790335,0.0059446,0.00727188,0.05742478,0.02027707,-0.06992304,0.0229907,-0.03973884,-0.00305364,-0.04194817,0.03958666,0.00086762,0.05967192,0.05699329,0.24038318,-0.00801022,0.01592551,0.0300701,-0.01473726,0.05835625,-0.04300039,0.02277426,0.02558561,-0.01759899,0.02743511,-0.02824746,-0.0260935,0.03332592,0.04654616,-0.00235287,-0.00553873,0.02561273,-0.0665703,0.00939892,-0.05518457,0.01137826,0.18199782,0.02313429,-0.04732652,-0.09586264,0.05702578,0.04082102,-0.10848583,-0.06089951,-0.05476409,-0.04791363,-0.04300855,0.02789121,0.05519195,-0.00590572,-0.01099735,-0.00444552,0.04431694,-0.00758745,0.03964907,0.00633983,-0.02347653],"last_embed":{"hash":"19nff2j","tokens":93}}},"text":null,"length":0,"last_read":{"hash":"19nff2j","at":1753423527067},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{17}","lines":[57,58],"size":248,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06452145,-0.04108312,-0.0161258,-0.03799288,-0.05329239,0.0463682,0.00592894,-0.00345062,0.05476547,-0.02631052,0.00483411,0.00297251,0.09301715,-0.00146361,0.00849469,-0.02590167,-0.01315607,0.03292517,-0.06690154,-0.01861172,0.06657132,-0.03932104,-0.05377198,-0.0645951,0.03865308,0.00470624,-0.04562048,-0.08058425,-0.03265286,-0.25693446,0.0606225,0.04437914,0.0214454,-0.06577978,-0.04811105,-0.05320224,-0.06307827,0.10190365,-0.07275311,0.03121107,0.03264574,0.00242913,0.03145457,-0.02719722,-0.01063309,-0.00089557,-0.02450748,0.0105018,0.00916362,0.00550186,-0.06724163,-0.02383213,0.01048657,0.06352716,0.01660825,0.02894434,0.02926378,0.10903636,0.03430952,0.05561424,0.05613773,0.0577283,-0.16734618,0.04773026,-0.00312603,0.01261252,0.00072694,-0.02922606,0.03200229,0.0747731,-0.00013854,0.04185173,-0.03502127,0.03877269,0.01481197,-0.0389917,-0.00879408,-0.04579883,-0.00465222,0.00748584,-0.06362272,-0.03581341,-0.00736314,-0.01589916,0.04463053,0.03089993,0.07301757,0.05041085,0.03368348,-0.05212662,0.07485894,0.03938446,0.0055125,0.00473831,0.0214428,0.01242511,0.00230933,0.02644108,-0.00160031,0.10744952,0.00946898,0.00126875,-0.02142823,-0.01656694,0.02301296,-0.04834028,-0.04182405,-0.02218289,-0.03735793,0.01679049,0.03416817,0.02584949,0.05804496,-0.05333681,-0.02696942,0.06187738,0.02936931,0.02711453,-0.02129457,-0.01250739,-0.03237858,0.01160247,-0.00373694,0.03456111,0.03212357,0.01150043,0.0482902,0.0913837,0.02422223,0.03111657,0.02990632,-0.00330914,-0.14197658,-0.04816697,0.00383318,-0.0010156,-0.01374062,0.01837239,-0.00346271,0.0078715,-0.02861867,-0.04383297,0.08203711,-0.08833973,0.00935769,0.12996954,-0.01615398,0.00246765,-0.01359587,-0.03666367,-0.00770411,0.02581726,-0.06031543,-0.07552916,0.00990314,0.02020821,0.06106418,0.04642461,-0.0449976,-0.00233279,-0.02940417,-0.00177716,-0.02657411,0.10353629,-0.02800103,-0.09514791,-0.0273823,0.07158924,-0.05169613,-0.05218805,-0.04514585,0.00951971,-0.00873679,0.00200907,0.08063699,-0.02486382,-0.0864142,-0.00781903,0.00697535,0.01656123,0.01116117,-0.03364516,-0.02825882,-0.00711213,-0.05663694,-0.08246032,0.03272674,-0.00489389,0.02546513,0.00795568,-0.06117724,-0.02222124,-0.0469998,0.03205404,-0.01514841,-0.03053203,-0.02502507,-0.01110064,0.05558985,-0.01355397,0.04282086,-0.02587721,-0.01042925,0.02088298,0.02420698,0.06281203,0.03504022,-0.05151504,0.11265665,0.02326104,-0.04563309,-0.02736796,0.06626794,0.05502265,-0.00067572,0.00692905,-0.01780107,0.02340405,0.0041534,-0.00117717,-0.00295886,0.01681432,-0.04259955,-0.22970052,-0.0102279,-0.00302505,0.04340854,0.01872676,-0.02466448,-0.02055843,-0.00417235,0.04253894,0.10095088,0.06970215,-0.03593544,-0.04441699,0.05492719,-0.01501514,-0.02912385,-0.07037807,-0.02982173,-0.0458024,0.02928475,0.0429434,0.00298166,-0.03392298,-0.07190982,0.08024403,-0.03181213,0.12328379,0.01035789,0.01411454,0.02139595,0.04390958,0.00692875,-0.0012549,0.02407933,0.02634349,0.01619823,-0.01997374,-0.03068187,0.01892934,-0.00401046,-0.08265014,0.00539664,-0.02326734,-0.11914935,0.00053351,0.03706668,-0.04399958,0.04051548,0.00537009,0.06464646,0.08438794,-0.00333238,0.04266414,0.00446475,0.0840794,-0.07124854,-0.06683554,-0.01881268,-0.0206335,0.00829644,-0.02105577,-0.06428358,0.02732972,-0.00058588,0.0213093,0.07295775,-0.01975736,0.0078686,0.0391204,0.02014548,0.00183645,0.05319424,0.00309307,0.01387558,-0.00313665,0.03161882,0.05496376,-0.05535883,-0.00091451,-0.04306289,0.03186568,-0.03862469,0.02999597,0.05114941,0.00353633,-0.01546442,0.06070759,0.06368715,0.01503555,-0.00993665,-0.01379143,0.03625419,-0.00584163,0.04804074,-0.01905975,0.0041096,-0.25171855,0.01743845,-0.0894777,0.07102185,-0.04025499,-0.02209209,0.04012913,0.00821581,-0.00030416,-0.03829164,-0.03192988,0.05997194,0.05807938,-0.04840195,-0.01489301,-0.0050677,-0.00039352,-0.0625034,0.05374053,-0.01154267,0.0863283,0.03371949,0.25649041,0.00337217,0.00052723,0.04785515,-0.01182174,0.02008702,-0.0180151,0.00958508,-0.00868762,-0.0547786,0.07321187,0.00647242,-0.0202867,-0.03603597,-0.01491643,0.02055387,-0.01428948,0.00858634,-0.07288954,-0.04170335,-0.00893645,-0.01056813,0.1686134,-0.00892699,-0.03921796,-0.09545045,0.03370577,0.02960484,-0.09300002,-0.0241847,-0.04170616,-0.05810405,0.01051896,0.03940089,0.01640228,0.02462176,0.01394449,-0.01550054,0.02071888,-0.02332797,0.04924697,0.01734842,-0.02522381],"last_embed":{"hash":"1ghdg1e","tokens":426}}},"text":null,"length":0,"last_read":{"hash":"1ghdg1e","at":1753423527102},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy#{18}","lines":[59,66],"size":1677,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09272074,-0.04173651,-0.01018376,-0.06379193,-0.03597144,0.03169754,-0.03180956,0.00015639,0.02734481,-0.04006993,0.01035519,0.0170012,0.05584931,-0.00726349,0.01386705,-0.02144178,0.02390318,-0.00263081,-0.05143955,-0.02344772,0.07978853,-0.02066214,-0.04298745,-0.06578416,0.04606334,0.00889861,-0.0387081,-0.05195285,-0.02304797,-0.25852862,0.03249041,0.05982188,0.03937158,-0.04591497,-0.08698989,-0.03109106,-0.03425073,0.05543416,-0.03561815,0.0378364,0.03995472,0.02521029,-0.00292986,-0.0398463,-0.00781095,-0.02711304,0.02674626,0.00151088,-0.00221261,0.00046874,-0.04660716,-0.03666144,-0.01302011,0.06038327,0.02868005,0.01199005,0.03157008,0.0876229,0.04255006,0.04398153,0.06781134,0.0602572,-0.17557041,0.0479377,-0.01015075,-0.00315345,-0.00506458,-0.04502515,0.03367932,0.02912371,0.03146197,0.02003295,-0.01281878,0.05108421,0.03859567,-0.06153831,0.00217189,-0.01884137,-0.02522531,0.0173419,-0.03252842,-0.05506341,-0.00452668,-0.00866519,0.04189781,0.04047631,0.0741185,0.0599484,0.0599809,-0.04373488,0.05759513,0.02731392,0.00708868,0.01744036,0.01400772,-0.01168175,0.0055365,0.0097203,-0.03768312,0.11271827,0.02535018,0.00692839,-0.02824915,0.0262392,0.02122973,-0.03011972,-0.04712531,-0.04296591,-0.06158896,0.0089246,0.06504596,0.05476388,0.02840885,-0.06816034,-0.04820447,-0.01193876,-0.02733443,-0.01018425,0.00909298,-0.00443969,-0.04018719,0.03078301,0.003014,0.00840196,0.01844432,0.00380416,0.07498044,0.06734656,0.03268687,0.0308033,0.05414046,0.00456184,-0.14253244,-0.04465118,-0.00267608,0.00218583,-0.0065383,-0.02001605,0.02730213,0.03187051,-0.00412388,-0.0258848,0.07542776,-0.0896626,0.01155462,0.12293664,-0.02060741,0.02002594,0.00863722,0.01918504,-0.00885129,-0.00281512,-0.0236384,-0.06967454,0.02126823,0.01546965,0.05308984,0.09609992,-0.0477916,-0.02347563,-0.02269138,0.00621398,-0.0022355,0.09016979,-0.01603137,-0.11583351,-0.03415255,0.04798652,-0.03753341,-0.04602373,-0.03577536,0.05099177,-0.02189888,0.00869394,0.0854101,-0.02951436,-0.08541874,-0.01269446,-0.00300125,0.00566035,0.02855634,-0.03438254,-0.03836024,0.00054914,-0.0396972,-0.10928588,0.04414767,0.01471001,0.01462892,0.0407815,-0.02423386,-0.01408366,-0.04787066,0.00694103,-0.01640582,-0.02671377,-0.04120548,-0.00705066,0.0614439,-0.01329738,0.02230764,0.00887691,0.00099579,0.02383342,-0.00830791,0.04283313,0.03864153,-0.08122057,0.12091298,0.06085242,-0.04489949,0.01289509,0.08329483,0.07598744,-0.09282191,0.0229366,-0.01238653,0.03635522,0.00057707,0.00229532,-0.00223503,0.04212192,-0.04714557,-0.22670913,0.01362426,-0.013708,-0.00417734,-0.03774343,-0.00820962,0.00179994,0.01036949,0.0476809,0.05430346,0.0874335,-0.0660015,-0.0074147,0.00278683,-0.02927214,-0.02779046,-0.06414142,-0.07038515,-0.02690157,0.01950183,0.01927185,0.01961667,-0.06159387,-0.08916397,0.07254612,-0.05558561,0.11874613,0.01465974,0.02069641,-0.02317914,0.06568212,-0.07442454,0.00407041,-0.01256696,0.00892523,0.02778302,-0.02283281,0.00456611,0.00725805,0.01815249,-0.08417267,-0.00848154,-0.00749456,-0.0970975,0.01825034,0.0057622,-0.01199906,0.0323257,0.00143367,0.06269147,0.08515266,-0.01831891,0.00666643,0.02918402,0.0407677,-0.08731645,-0.06265658,-0.00125642,-0.01135063,0.02790035,-0.0164931,-0.05327596,0.0472311,-0.00325514,0.02415856,0.065807,-0.00768156,-0.00405296,0.02196917,-0.0157023,-0.00054833,0.07457364,-0.02311555,0.02819948,-0.00553128,0.01105351,0.03354581,-0.05251211,0.01253246,-0.01361998,-0.00006285,-0.05001573,0.04520987,0.07210486,0.02830957,-0.00385112,0.04753995,0.06348354,-0.00260558,0.00107692,-0.01560987,0.04493661,-0.01149157,0.01424427,0.00702911,0.02043369,-0.25546497,0.00007227,-0.07838147,0.05873456,-0.03107267,0.00417627,0.03644794,-0.00457348,-0.00777975,-0.01474724,-0.0119993,0.03270245,0.05327097,-0.05719579,-0.01760661,-0.00660608,0.0165883,-0.03585114,0.03879624,-0.02386253,0.06645668,0.05612404,0.27444047,0.02579981,-0.01668307,0.04301358,0.00626146,-0.00164816,-0.02328151,0.01700025,0.02006738,-0.00672794,0.08033847,-0.01848897,-0.02160485,-0.03494457,-0.00609707,0.00610741,-0.03046905,0.01637111,-0.05196335,-0.00939771,-0.032869,0.01064882,0.1701562,0.00723798,-0.02284615,-0.09663747,0.05868852,0.03814748,-0.10738566,-0.03792631,-0.04912499,0.00393905,-0.01106808,0.0258336,-0.00376814,0.02626936,-0.02322523,0.00129227,0.05522422,-0.03170751,0.05873623,0.02195062,-0.01449541],"last_embed":{"hash":"188t6kv","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"188t6kv","at":1753423527258},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software","lines":[67,89],"size":2616,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09392781,-0.04298018,-0.00955298,-0.0607432,-0.0338736,0.03409197,-0.03181768,-0.00283897,0.02482463,-0.04230011,0.00915825,0.01916037,0.05285191,-0.00491042,0.01168305,-0.02493875,0.02358109,-0.00372331,-0.04810371,-0.02517418,0.07432374,-0.01931308,-0.04517673,-0.06438529,0.04804633,0.00963053,-0.03755486,-0.05470586,-0.02404988,-0.26217341,0.03800401,0.06028205,0.03866033,-0.04621551,-0.08949469,-0.03100318,-0.03546632,0.05683188,-0.03530802,0.03506658,0.04275467,0.02569323,-0.00634753,-0.04153439,-0.00851759,-0.02694835,0.02622017,0.00255342,-0.00461096,-0.00034861,-0.04440028,-0.04432808,-0.01000773,0.05866895,0.02899801,0.00983237,0.03111339,0.08755989,0.04537684,0.04604624,0.07332604,0.06105289,-0.17052518,0.04442762,-0.01290492,-0.00242528,-0.01131867,-0.04503153,0.0368026,0.03199591,0.02831163,0.01613772,-0.01421091,0.0496453,0.03657342,-0.06180862,0.00179652,-0.01847203,-0.02886937,0.02018267,-0.03004345,-0.05212867,-0.00029709,-0.00253701,0.0386795,0.04166511,0.07290193,0.05822316,0.05818863,-0.043034,0.05885153,0.02227749,0.00422122,0.01720963,0.01424748,-0.01482177,0.00761771,0.00735577,-0.03316526,0.11350606,0.02500032,0.0078561,-0.02715371,0.02717225,0.02148032,-0.03265728,-0.0441272,-0.04280711,-0.06504176,0.00983914,0.06375171,0.05172222,0.02598108,-0.06493899,-0.04915205,-0.01664547,-0.02589346,-0.01040911,0.00972767,0.00107829,-0.0400528,0.03308354,-0.00329704,0.00525576,0.01688576,0.00553894,0.07238351,0.06895696,0.03143064,0.02832306,0.05639267,0.00745292,-0.14432397,-0.04694417,-0.0019242,0.00589526,-0.00715856,-0.01518266,0.02862153,0.03195637,-0.00271374,-0.01483509,0.07593317,-0.09299248,0.01234783,0.12046868,-0.0222729,0.02190053,0.00832535,0.01985546,-0.00821278,-0.00302663,-0.02767528,-0.06885782,0.02173527,0.0137052,0.04935078,0.09574047,-0.04899338,-0.02219834,-0.0216691,0.00950752,-0.00068745,0.08724467,-0.01927064,-0.1180246,-0.03098598,0.04849406,-0.03684844,-0.04583699,-0.03786841,0.05164426,-0.02122688,0.00641897,0.08154983,-0.02727343,-0.08948745,-0.01701411,-0.0009966,0.00235123,0.03211949,-0.03002803,-0.04204599,0.00412026,-0.03974856,-0.10935429,0.04470409,0.01468858,0.01494823,0.04507776,-0.02017554,-0.00789103,-0.04895199,0.01239393,-0.01750634,-0.03005582,-0.04322683,-0.00408425,0.05700796,-0.01498612,0.02329992,0.00882199,-0.00023702,0.02090705,-0.0047695,0.04055084,0.03542743,-0.08237939,0.12544124,0.06056387,-0.04634033,0.01070792,0.08280735,0.07699542,-0.08809056,0.02085793,-0.01149903,0.03592263,-0.00146234,0.00369831,-0.00161301,0.04282469,-0.04647503,-0.22959954,0.00767113,-0.01001476,-0.00359448,-0.03371937,-0.00726727,0.00227249,0.00931526,0.04879354,0.05852769,0.08886672,-0.06481065,-0.00836236,0.00248213,-0.02917844,-0.02266008,-0.06619374,-0.06935485,-0.02933103,0.02015345,0.01448844,0.02533701,-0.0639224,-0.08853951,0.06883777,-0.05487779,0.11821111,0.00941343,0.01708466,-0.02344377,0.0653503,-0.07531353,0.00324574,-0.01254152,0.01238562,0.03234776,-0.01790919,0.00657349,0.00761535,0.01671522,-0.08296852,-0.01056822,-0.00481257,-0.09810151,0.01824169,0.00588516,-0.01063748,0.0317219,0.00042845,0.06386811,0.08294883,-0.01777805,0.00456251,0.02798598,0.0381203,-0.08819114,-0.06117377,0.00451093,-0.01064345,0.02660273,-0.01267599,-0.0568546,0.04363186,-0.00262257,0.02625038,0.06439282,-0.00951039,-0.00517153,0.02126159,-0.01358139,-0.00066131,0.07343075,-0.02436727,0.02729502,-0.00479095,0.0120836,0.03989756,-0.05050388,0.01104649,-0.01266496,0.00158161,-0.050236,0.03990358,0.07434725,0.02575342,0.00075014,0.04817992,0.05885378,-0.00927041,0.00132583,-0.01896932,0.04013678,-0.01283259,0.01147403,0.00771168,0.02267342,-0.25453019,0.00246569,-0.07686707,0.05414977,-0.03044715,0.00380203,0.03940644,-0.00097391,-0.00238148,-0.01895188,-0.01418197,0.0337424,0.0551235,-0.05960197,-0.01871879,-0.00533203,0.02110995,-0.03477361,0.04070714,-0.02443421,0.06416649,0.0548643,0.27569216,0.02010219,-0.01864948,0.04248071,0.00672535,-0.00298825,-0.02471524,0.01521607,0.02025249,-0.0040726,0.08037703,-0.01761346,-0.02121895,-0.03004936,-0.00830348,0.00349662,-0.02861928,0.01701652,-0.04938013,-0.00807538,-0.0373119,0.00874581,0.17006901,0.01050697,-0.02710453,-0.09425453,0.0595456,0.04131907,-0.10729431,-0.03828877,-0.05240569,0.00294675,-0.01100829,0.02579169,-0.00365057,0.03108039,-0.01847336,0.00299759,0.05902154,-0.03228411,0.05903321,0.02227319,-0.01003541],"last_embed":{"hash":"1av7hrv","tokens":475}}},"text":null,"length":0,"last_read":{"hash":"1av7hrv","at":1753423527412},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{1}","lines":[69,83],"size":1835,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11735535,-0.05503064,0.00133446,-0.02822672,-0.0088131,0.04277771,0.01397445,0.02445576,0.03903747,-0.04148127,0.02574792,-0.00574737,0.05172067,-0.02466873,0.02407325,-0.01272038,0.03283365,-0.06528661,-0.01862727,-0.00483464,0.02098828,-0.02483325,-0.02096642,-0.01968664,0.06116473,-0.02208337,-0.0345807,-0.0512372,-0.05562704,-0.24650396,0.01957032,0.0786061,0.00841236,-0.0551346,-0.0619693,-0.05205858,-0.02929958,0.04265853,-0.03550968,0.05054332,0.03712389,0.02777913,-0.01701611,-0.05509003,-0.0012862,-0.02681012,0.03240432,-0.02497206,0.0068195,-0.03567579,-0.03588322,-0.0320996,0.01652126,0.05634661,0.03252006,0.03573633,0.03587485,0.07573699,0.07004079,0.030228,0.06751877,0.05246113,-0.19020084,0.04836389,-0.0433909,-0.0162499,-0.01248765,-0.05448275,0.02537417,0.04918081,0.05504477,0.04225183,-0.01776754,0.04112227,0.04591794,-0.05208438,0.00215428,-0.01091796,-0.03539229,0.03211233,-0.01094378,-0.03566147,0.00959039,-0.02118426,0.01472991,0.00722684,0.06050101,0.05721831,0.01991938,-0.04145159,0.04842927,0.04606558,0.03820269,0.04237307,-0.01388906,-0.01249485,0.01019596,0.0055658,-0.05953928,0.13311568,0.01684843,0.00740167,-0.02533682,0.04595902,0.05183312,-0.00711185,-0.00237461,-0.0180013,-0.06196499,0.0286545,0.05529898,0.07100454,0.04055493,-0.0476209,-0.02633166,0.02691249,-0.02943948,0.00766266,0.01841231,0.00333482,-0.05007106,0.0201888,0.03088679,0.01862174,-0.00676922,0.02444893,0.07304032,0.07150593,0.00824234,0.05695634,0.03964978,0.01108296,-0.11336569,-0.0607878,0.00060115,0.03320399,-0.0114672,-0.05658989,0.00230234,0.05768369,-0.04858964,-0.04541211,0.08053732,-0.12334789,0.00756061,0.11010837,-0.00764146,0.00784709,0.01162802,0.03346039,-0.03306029,0.01168664,-0.02569095,-0.06583741,-0.00281733,-0.00301687,0.04661038,0.07922792,-0.05665869,-0.02927625,-0.00199411,0.00062735,-0.01837715,0.09835358,-0.01504215,-0.1274485,-0.02514447,-0.01739455,-0.0408266,-0.04146196,-0.04013433,0.03769975,-0.0243997,0.02613274,0.12004682,-0.01557169,-0.04526272,-0.03476026,-0.02804988,-0.03946848,0.05275758,-0.03670389,-0.04530892,-0.00794614,-0.04200221,-0.09629086,0.04403116,0.0013311,0.02437192,0.02817293,-0.0382353,0.00598598,-0.06839363,0.01104112,-0.03338252,-0.0109733,-0.05648801,-0.02910307,0.06291581,-0.03432797,-0.01483633,0.00678766,0.01878959,-0.00034414,-0.02996833,0.05831861,0.02037332,-0.07785582,0.11608709,0.02221924,-0.03145462,0.01388167,0.09246127,0.04220701,-0.05287781,-0.00729022,-0.01785779,0.03483413,-0.00606522,-0.01037793,0.01205694,0.10805194,-0.04567633,-0.22690791,0.01122884,-0.01278174,-0.00660043,-0.01858021,-0.00550032,0.0230441,0.02033042,0.08658556,0.09004493,0.08612854,-0.0635779,-0.0113401,-0.00882669,-0.01275966,-0.0300174,-0.06149026,-0.0636081,-0.01790491,-0.00333436,0.00372932,0.06001261,-0.06162846,-0.07797854,0.06472016,-0.06441223,0.10864139,0.0121016,0.00455676,-0.04526563,0.05092328,-0.02539807,0.01247066,-0.05822019,0.00702353,0.06279053,0.01181495,0.02818884,-0.01870728,-0.02163011,-0.0787247,0.00255884,-0.03665096,-0.10649765,-0.0091779,0.01035807,-0.01314242,0.06286556,-0.02936308,0.04583861,0.0781608,-0.01385182,0.00297243,0.05568115,0.08081467,-0.06917045,-0.06442028,0.0045632,-0.0056804,0.05081431,-0.0334759,-0.01829552,0.03427076,-0.03300342,0.02516061,0.03517274,0.00242112,-0.04736636,0.02820911,-0.00162497,-0.0033669,0.03928997,-0.02877087,0.00319812,-0.03506483,0.02953532,0.02880033,-0.03767891,0.02147743,-0.00811288,0.01345614,-0.06197083,0.04981447,0.0493056,0.05536286,0.05450868,0.0536855,0.04545853,-0.019382,0.01528797,-0.02051167,0.03791144,-0.01515497,0.00431219,0.03465505,0.00978302,-0.25164378,-0.00291907,-0.01373784,0.0284502,-0.0169067,0.03344741,0.02990112,-0.0013038,-0.02554715,-0.02201834,0.00727156,0.02758501,0.00994957,-0.04762288,-0.01727818,-0.00722362,0.0364499,0.00134305,0.04568808,-0.00015277,0.03863705,0.04709893,0.25516829,0.00938559,-0.01496538,0.02869202,0.00784564,0.00130185,-0.0533161,0.01872611,0.01256247,0.00249805,0.08508907,-0.00732129,-0.03504141,-0.03227212,0.00333549,-0.01031424,-0.03578803,0.0030372,-0.02023278,-0.01433775,-0.04417581,0.01698739,0.16035859,-0.00521392,-0.04319477,-0.07712565,0.03455403,0.05579968,-0.08644193,-0.07320954,-0.07096795,-0.01360441,-0.00153545,0.02092697,0.00525463,0.01421515,0.01338468,-0.02086621,0.03911357,-0.0124692,0.0379031,0.00827251,0.00332123],"last_embed":{"hash":"1ajhlj8","tokens":196}}},"text":null,"length":0,"last_read":{"hash":"1ajhlj8","at":1753423527604},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##New Features in Lotto Software#{3}","lines":[86,89],"size":678,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08810243,-0.06612322,0.00127439,-0.0527668,-0.06203489,0.04666748,-0.01975027,0.01103363,0.08209529,-0.02247149,0.06598313,-0.0043837,0.05380144,0.01389655,-0.03557822,-0.04326405,-0.00314044,0.01047539,-0.05578529,-0.03909963,0.09291942,-0.03272484,-0.01734734,-0.08369668,0.04538728,0.01527815,-0.03683436,-0.04594887,-0.01942521,-0.27134374,0.02897348,0.06069007,0.00707995,-0.04982258,-0.08650135,-0.06177807,-0.04083646,0.0687466,-0.02401457,0.06763758,-0.00357961,0.02426274,0.00420285,-0.03000259,-0.00916295,-0.01887506,-0.01091801,0.01180339,-0.00730516,0.0121034,-0.08437509,-0.03933264,-0.01977393,0.07460325,0.04437254,0.03118413,0.0084,0.08035466,0.06358595,0.04229661,0.05731872,0.05196917,-0.20196852,0.05094494,0.01798029,-0.01269587,-0.00289882,-0.01388059,0.01499513,0.05662307,-0.00319459,0.02817973,-0.03086792,0.0593044,0.05305493,-0.04673106,-0.0490531,-0.03023256,-0.03071854,0.00308005,-0.06022661,-0.02289351,-0.05302642,-0.00968979,0.03774413,0.03097215,0.0675137,0.03101608,0.0259392,-0.02626801,0.02171061,0.00467402,-0.00433061,0.00103522,0.00374303,-0.02056986,0.041336,-0.00128047,-0.02383521,0.09681934,-0.00763019,0.01902809,-0.03774333,0.02329264,0.01107405,-0.04399057,-0.06710139,-0.03383329,-0.03885424,-0.02990048,0.04315224,0.01274002,0.05635504,-0.09655975,-0.07614809,-0.00195099,0.00160644,-0.03710553,0.02003142,0.01506458,-0.00394937,0.02926934,0.00227069,0.00221824,0.03774605,0.04065181,0.0466366,0.08078215,0.02038844,0.03297257,0.04389009,0.01433386,-0.11607625,-0.03931658,-0.00814924,-0.04898563,-0.00428583,0.00261958,0.05459672,0.01931334,-0.01948598,-0.04706264,0.10993157,-0.07742061,0.02732442,0.12338856,-0.03511187,0.01479377,0.00841847,0.01397032,0.01027008,-0.0079268,-0.01961611,-0.06019469,-0.00175263,0.02616846,0.04325361,0.071583,-0.03952689,-0.01239666,-0.03851431,-0.01018369,-0.01949202,0.09024839,-0.01198868,-0.122533,-0.02639866,0.09776502,-0.05216639,-0.07525653,0.01768284,0.01518966,-0.04251417,0.03233711,0.05257518,0.00214697,-0.10215183,-0.0135565,0.00485712,0.03298111,0.02734855,-0.0553502,-0.04758272,0.01897709,-0.03951471,-0.06779336,-0.00726986,-0.02445093,0.03926415,0.06479328,-0.0052592,-0.00632386,-0.0183891,-0.00111025,-0.0256646,-0.07486902,-0.0505847,0.0105162,0.07785318,0.01436796,0.04826492,0.00473898,0.01229888,0.03881131,0.01285275,0.0393252,0.00944948,-0.0439619,0.09417806,0.04480323,-0.01629914,-0.03014243,0.01300031,0.05373016,-0.05036602,0.04052392,0.00132232,0.00442545,-0.00599225,0.02987785,0.00859926,0.02225871,-0.03690178,-0.21498469,0.00361677,-0.02612479,0.01936007,-0.03162749,0.02211778,0.01114955,-0.01361817,0.03638276,0.05054998,0.05535988,-0.02079025,-0.01213121,0.02299463,-0.03542331,0.00627649,-0.06117234,-0.05828532,-0.02978571,0.04898946,0.01410414,0.01980933,-0.0952386,-0.08535668,0.06319468,-0.04221598,0.11990715,-0.01353761,0.03445457,-0.03812323,0.08866971,-0.01885195,-0.00298846,0.0359943,0.02401995,0.04181756,-0.03108867,0.00839698,-0.01268115,0.02472277,-0.09610094,0.004107,0.01908778,-0.09823169,0.00257456,0.01460548,0.00336842,0.00570778,0.00572529,0.05708573,0.05960438,-0.01630293,0.04616724,0.02518361,0.05061638,-0.06578705,-0.04385709,0.00368856,-0.03120505,0.01669602,0.00115053,-0.03640003,0.04525335,0.00338659,-0.01232235,0.06624555,0.01570002,0.00453427,0.0381764,0.00984222,0.02037578,0.09534094,0.00250963,0.01979363,-0.00278699,0.00008037,0.05179663,-0.05639635,0.00801409,-0.01186566,0.00552339,-0.03869101,0.03508653,0.03238093,0.01375511,-0.03783132,0.05687081,0.0804593,0.00208832,0.00078753,0.01048417,0.03107372,0.01059652,0.00169261,0.02645976,0.00511471,-0.26087877,0.01562356,-0.06316378,0.05227632,-0.00021238,-0.01440598,0.03441935,0.00357627,-0.04004767,0.00408848,-0.00496425,0.02269403,0.03047887,-0.06808954,-0.02729488,-0.00062324,-0.00051266,-0.05938954,0.04019126,-0.01859016,0.05525989,0.04487295,0.25255516,-0.00132655,-0.00742039,0.03477589,0.00007457,0.01027176,-0.02154702,-0.00751507,0.02002181,-0.00747853,0.08148783,-0.04634504,-0.06541597,-0.00584942,0.03360348,0.01590375,-0.00685703,0.0137352,-0.06359613,-0.01548455,-0.02272031,0.022594,0.15998946,0.03171026,-0.00183811,-0.07754096,0.05301359,0.03834878,-0.10762734,-0.02382992,-0.03208537,-0.00078478,0.03010325,0.04056248,-0.00135255,0.01903323,-0.03366409,0.00781023,0.04077856,-0.03766181,0.06007286,0.04193475,-0.02745001],"last_embed":{"hash":"1km3e90","tokens":467}}},"text":null,"length":0,"last_read":{"hash":"1km3e90","at":1753423527669},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01","lines":[90,115],"size":3044,"outlinks":[{"title":"_**Excel Spreadsheets Lotto Lottery Software Systems Strategies Programming**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"Original software generates lotto combinations position by position.","target":"https://saliu.com/images/lotto-ranges.gif","line":23},{"title":"Lotto software generates from ranges of numbers in 5 or 6 positions.","target":"https://saliu.com/images/lotto-positions.gif","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0900355,-0.0783308,0.01713645,-0.0444425,-0.06018191,0.03172951,0.00530167,0.01549558,0.07888772,-0.03441366,0.07626522,-0.011289,0.02811388,0.01972256,-0.02306169,-0.01865951,-0.00133647,-0.0003984,-0.04437768,-0.02726472,0.10022241,-0.03178438,-0.00252591,-0.05430039,0.06324825,0.01651938,-0.036751,-0.0560671,-0.01930064,-0.2660571,0.02998652,0.0556893,-0.00134704,-0.04651166,-0.08187838,-0.07996459,-0.05658552,0.09305572,-0.02165869,0.07072328,0.00108409,0.02124926,-0.00481313,-0.04033893,-0.01556927,-0.03397513,-0.0094226,0.0135074,-0.00883121,0.00494274,-0.09109911,-0.01858438,-0.02150738,0.07156051,0.03498912,0.02795864,0.02361193,0.06530558,0.09242667,0.026013,0.05416988,0.02989972,-0.20962617,0.05496461,0.00984194,-0.01213191,-0.00038355,-0.01425033,0.01300207,0.05463914,0.00073498,0.03753478,-0.02403804,0.07002515,0.06611802,-0.05498927,-0.04399263,-0.03208822,-0.0316583,-0.00676588,-0.05139214,-0.02415533,-0.05055259,-0.01834299,0.03517081,0.02042955,0.05825158,0.03280444,0.02439024,-0.01883587,0.03399365,-0.00320044,-0.02364935,0.00618721,-0.01773811,-0.01055224,0.03479586,0.0101866,-0.00133938,0.11088703,-0.00139459,0.01279365,-0.05114533,0.03608121,0.0219924,-0.02508909,-0.0657311,-0.0304661,-0.03135108,-0.00634176,0.04055113,0.01799704,0.09473994,-0.08882198,-0.07604308,-0.03330729,0.00267336,-0.03503189,0.02670894,0.01797667,0.0140844,0.03621117,0.0016062,-0.00170753,0.05088906,0.0547968,0.03646278,0.0802068,0.03453935,0.03585618,0.03634107,0.0155497,-0.09772775,-0.03251739,0.00613473,-0.03492435,-0.00539711,-0.00097995,0.0564727,0.02978063,-0.03391813,-0.04096916,0.09032511,-0.07377616,0.01723388,0.12597504,-0.04054763,0.02370563,0.00348502,0.02134793,0.00697289,-0.00195228,-0.02057098,-0.06802448,-0.02165486,0.03533565,0.0467039,0.07276575,-0.03980998,-0.00746275,-0.02184138,-0.01320499,-0.01026696,0.09979916,-0.01410963,-0.12656468,-0.01800187,0.0846753,-0.02737187,-0.07014175,0.01559709,0.02335666,-0.03875658,0.03202168,0.04860869,-0.00047746,-0.09004302,-0.0243094,-0.00205548,0.0175842,0.02899039,-0.06723752,-0.05008518,0.02535423,-0.03360615,-0.07438973,0.0000553,-0.0420268,0.05905332,0.06252583,-0.00788022,-0.01828861,-0.01410889,0.00486776,-0.03015939,-0.06692618,-0.05849782,0.01204411,0.07999028,0.01081098,0.06386469,0.0094192,0.02294727,0.03487924,0.0038796,0.04497516,-0.00061502,-0.03456645,0.09106221,0.0274693,-0.02574144,-0.03580446,0.02373066,0.04746288,-0.06734224,0.0252004,0.00446355,0.01175081,-0.01280406,0.03277132,0.00316481,0.0325943,-0.05959763,-0.21669669,-0.00599173,-0.01922094,0.01438885,-0.02882243,0.04050572,0.00156996,-0.02747715,0.05165657,0.04362193,0.02963775,-0.01774288,-0.01489131,0.03390922,-0.03201308,0.00894229,-0.0556802,-0.0394877,-0.0220046,0.03954755,0.01033341,0.00785481,-0.11306503,-0.09731025,0.06446239,-0.03917987,0.11795755,-0.01062303,0.0168205,-0.05949792,0.08569694,-0.02461459,-0.00030779,0.04551941,0.00917565,0.04650021,-0.05082146,0.01515458,-0.0134266,0.01295432,-0.0889522,0.00175032,0.01377366,-0.09160372,-0.02146649,0.01171041,0.01266573,0.01268217,0.01958067,0.0512077,0.04903095,0.00043919,0.03596953,0.01857723,0.03580059,-0.05751671,-0.04978045,0.0173011,-0.02882957,0.02791145,0.00409388,-0.00547652,0.03454626,0.00586678,-0.00742844,0.07434627,0.01503866,-0.00989857,0.03728906,0.00678255,0.01123427,0.09612599,-0.00161089,-0.01450846,-0.0074494,0.01362941,0.02896776,-0.06161289,0.00303302,-0.02001555,0.01197999,-0.0197699,0.03420435,0.03406833,0.00395669,-0.02360528,0.04098831,0.0666336,-0.01080947,0.00332154,0.01847443,0.04228583,-0.00074384,-0.02223119,0.01228189,0.00018405,-0.25889045,0.02102494,-0.06530412,0.04350169,0.01670624,0.00025688,0.04313776,0.0264877,-0.04318289,0.0174361,-0.01056006,0.01911469,0.02038412,-0.05131738,-0.04346583,-0.00434152,0.01477289,-0.06527898,0.05615274,-0.02114892,0.06156082,0.03933612,0.25433049,-0.01172303,-0.02190321,0.02052414,0.01493671,0.0114549,-0.03688552,-0.00890373,0.01587031,-0.00128602,0.07209038,-0.03247212,-0.0530966,-0.0033811,0.03711551,0.01392564,-0.01462252,-0.00691575,-0.05003079,-0.01243078,-0.02738849,0.04365622,0.16056938,0.03690453,0.00198293,-0.08717531,0.03853246,0.03507241,-0.11180037,-0.03981654,-0.03018018,0.01013785,0.02695884,0.03887207,-0.0110456,0.0151262,-0.03380929,-0.00667273,0.02573497,-0.02146279,0.05165236,0.02527585,-0.02024795],"last_embed":{"hash":"1d3f5ws","tokens":208}}},"text":null,"length":0,"last_read":{"hash":"1d3f5ws","at":1753423527843},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{1}","lines":[92,95],"size":656,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10106122,-0.0809793,0.01046268,-0.06913324,-0.05531059,0.05126186,-0.02346365,0.00360361,0.06879253,-0.0208264,0.03650514,-0.00207242,0.06977316,0.03727208,-0.04598982,-0.04563842,-0.0076866,0.03568591,-0.06908463,-0.02933191,0.09507523,-0.01249939,-0.04322748,-0.06353819,0.01777132,0.05051314,-0.04524136,-0.03274266,-0.00610602,-0.25581473,0.04216909,0.05042744,0.03328801,-0.04767552,-0.08768261,-0.05807999,-0.04168577,0.05814664,-0.02229643,0.07147047,0.01153272,0.02729777,-0.01731163,-0.02706985,-0.00170673,-0.02454997,-0.00252597,-0.00361351,-0.00796944,-0.00573277,-0.06186348,-0.03428043,-0.01914713,0.06295151,0.06351588,0.01127873,0.00560996,0.0774312,0.04068246,0.06998094,0.07467193,0.05699321,-0.1996472,0.05308361,0.02477296,-0.0052078,-0.01277398,-0.00713878,0.01598713,0.06905092,0.01155965,0.00669273,-0.03506164,0.04408307,-0.00217521,-0.0425109,-0.03086175,-0.01842492,-0.03374914,-0.00219007,-0.06266472,-0.03945296,-0.05188771,-0.01383293,0.04806164,0.05349937,0.07846514,0.03094727,0.0124392,-0.03410322,0.02170305,-0.00232775,0.00173343,-0.00187099,0.00506944,0.00324352,0.0193902,-0.01550016,-0.03135506,0.10815901,0.00399012,0.01956596,-0.03827153,0.0304998,0.00542217,-0.02506553,-0.07618047,-0.05105279,-0.04496323,-0.03543849,0.03638968,0.01424951,0.03585682,-0.08360235,-0.03302882,0.03180574,0.00130154,-0.02982931,0.0169548,0.02520076,-0.02895818,0.04043824,0.00150877,0.00793881,0.04091412,0.04896557,0.05055504,0.07881903,0.00212638,0.03190626,0.04551744,0.00910427,-0.14234895,-0.00884903,-0.02093861,-0.03260399,-0.0004153,0.01576893,0.03897152,0.02340404,0.0123239,-0.06170835,0.10812725,-0.06634657,0.02997438,0.11295383,-0.03464371,0.02924806,0.01022482,0.01041263,-0.01142245,0.00082103,-0.03369773,-0.07268291,0.00332016,0.0183655,0.00042944,0.04979218,-0.05858196,-0.00886917,-0.06754186,-0.00112768,-0.02616591,0.11100418,-0.01681223,-0.09456916,-0.0256872,0.08272732,-0.03886918,-0.06416124,0.00338093,0.00792009,-0.04653256,0.03793335,0.05439048,-0.00382565,-0.09440833,-0.0249337,0.01850546,0.04850862,0.02348381,-0.01809374,-0.04397219,0.01582846,-0.04801314,-0.04380159,-0.01604956,-0.00734085,0.04335963,0.05575262,-0.01222274,-0.00200237,-0.02219755,-0.03528152,-0.043658,-0.07857797,-0.04519168,0.01123937,0.05890435,0.02111566,0.04096414,0.01471657,-0.00985279,0.0546549,0.00026106,0.04828844,0.02654747,-0.05088594,0.08279739,0.05481958,0.00413706,-0.03893614,0.00553247,0.0399699,-0.02649134,0.0426457,0.01955589,-0.00343008,0.0154394,0.01198997,0.0224818,0.00414677,-0.01217933,-0.21338746,0.00953319,-0.0121043,0.032814,-0.00470876,0.02402236,0.02226044,0.01492676,0.04763948,0.06045237,0.08415169,-0.02642103,-0.03936056,0.01414635,-0.05177207,0.03015029,-0.05617055,-0.08199477,-0.03321908,0.0154012,0.00977228,0.04587886,-0.09980586,-0.08035024,0.08605596,-0.04778413,0.12160785,-0.01345833,0.04387173,-0.0162411,0.07858025,-0.03484546,-0.0092415,0.05391772,0.03815451,0.0227835,-0.01561779,0.00342316,-0.00147003,0.02804217,-0.08380234,0.00593635,0.02693261,-0.07439874,0.01979782,0.00045536,-0.01597878,0.00869811,0.01627854,0.0572616,0.07073953,-0.01984547,0.04582382,0.02764582,0.05125521,-0.05127562,-0.05523173,-0.02331256,-0.01706116,0.02757187,0.00955053,-0.06012255,0.04698803,-0.02165207,-0.01651294,0.06153582,0.0125662,0.01702656,0.02766049,0.0252452,0.02019131,0.0584615,0.00112638,0.03446351,-0.0024238,-0.00810234,0.05508072,-0.06654289,0.01523785,-0.00493061,0.01667911,-0.04532513,0.03247651,0.01235217,0.00815358,-0.0642148,0.02837801,0.07363486,0.01735206,-0.00823796,0.00216401,0.04952721,-0.00044771,0.00257701,0.04618306,0.00346435,-0.26099768,0.02546725,-0.06495582,0.05358804,-0.02095528,-0.0121249,0.02626856,0.00906959,-0.06648033,-0.0167971,-0.01236544,0.04351366,0.02213237,-0.05857621,-0.00123532,0.01498313,-0.01433906,-0.05701697,0.03994725,-0.04260546,0.05871771,0.04629544,0.23682138,-0.01369228,0.00588252,0.03552971,-0.01721809,0.01349236,-0.01380168,-0.00679807,0.01441869,-0.01365911,0.08994748,-0.05534775,-0.07003216,0.00874171,0.04044895,0.01096542,0.00571517,0.00420488,-0.07283819,-0.02616351,-0.0185853,0.00166832,0.16038901,0.00064885,-0.02514441,-0.06376901,0.04989022,0.04866263,-0.12035982,-0.01811596,-0.01602808,0.00410405,0.01408168,0.05089507,0.00749412,0.03851068,-0.03921878,0.00963382,0.02372576,-0.05425042,0.05165478,0.02897088,-0.05666619],"last_embed":{"hash":"1sc0wfl","tokens":274}}},"text":null,"length":0,"last_read":{"hash":"1sc0wfl","at":1753423527912},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{3}","lines":[98,101],"size":999,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07317119,-0.07697678,-0.02016237,-0.07628351,-0.05277128,0.05752677,-0.03971326,-0.01424824,0.05470241,-0.02497255,0.06312483,-0.00884229,0.05991822,0.00064323,-0.00647647,-0.04624537,0.00810905,-0.02930157,-0.0755624,-0.01763729,0.11082277,-0.02902347,-0.01091358,-0.10154021,0.04097739,0.00076889,-0.05311911,-0.05246715,-0.00435732,-0.25838953,0.02833004,0.04400111,0.02694323,-0.05264315,-0.10067091,-0.05814674,-0.02493951,0.098443,-0.03103889,0.05967401,-0.03359335,0.04018071,0.0074306,-0.05442256,-0.00900359,0.00722244,-0.01652663,0.00115164,0.03947587,0.00376507,-0.06397076,-0.03548236,-0.03940774,0.06377187,0.04761062,0.01502228,0.03943467,0.09640405,0.01986505,0.04449913,0.06451334,0.05917352,-0.17160082,0.03421723,0.03740031,0.00637991,0.0157818,-0.03885673,0.01202385,0.02850972,-0.02133002,0.02225892,-0.00861261,0.07562457,0.05796662,-0.04358925,-0.04353494,-0.02759516,-0.06100108,0.0401283,-0.04154614,-0.05108687,-0.00443326,-0.02818781,0.03576369,0.02428698,0.10568372,0.0709868,0.04176516,-0.04915723,0.03218862,0.0229484,-0.01016716,-0.02319784,0.01833002,-0.00231385,0.02598833,0.02626434,-0.0434157,0.10468974,-0.02534082,0.01491554,-0.02913712,0.06512415,0.02194337,-0.01181311,-0.0759066,-0.01636877,-0.01786538,-0.0204835,0.03679271,0.00100249,0.01240532,-0.09106788,-0.06055352,0.02578284,0.01798804,0.00848492,-0.00937083,0.0050098,-0.01751452,0.00583007,-0.00279245,-0.03564486,0.02271897,0.00920114,0.02928219,0.08121047,0.01679655,0.03545981,0.05868303,-0.00495873,-0.12140396,-0.04088211,-0.02549199,-0.02443685,-0.00633352,-0.01345592,0.01450324,0.01053829,0.00985596,-0.0644695,0.10258655,-0.0597757,0.02791726,0.13161206,-0.05146985,0.03013039,0.02439203,0.0014933,-0.01460832,-0.011844,-0.02090149,-0.0568627,0.001361,0.04644308,0.06039232,0.03868029,-0.03287098,-0.0269347,-0.00978809,-0.0089622,-0.0202761,0.10488033,0.01044994,-0.07223005,-0.0354654,0.08697369,-0.04686712,-0.03200159,-0.00750169,-0.00169138,-0.00131072,0.03618874,0.0695668,-0.01765429,-0.09252691,-0.00549216,0.00431457,0.04328968,0.00053317,-0.02441942,-0.03718945,0.02211274,-0.04419132,-0.08560323,0.01976893,-0.01935271,0.04392801,0.04913363,-0.01643317,0.02273498,-0.01546671,0.00848071,-0.02839943,-0.02617478,-0.04921963,-0.01263371,0.07827032,-0.03999649,0.03904612,0.0072063,0.00315947,0.02760394,-0.00629546,0.05770738,0.05503149,-0.04145099,0.08850176,0.02366549,-0.0096678,-0.0169951,0.03787977,0.04354063,-0.04596533,0.03578927,0.01375063,0.01339862,0.04405454,0.04288584,-0.00386938,0.03042929,-0.04052744,-0.20440844,0.00636792,-0.00695535,0.02138147,0.01791032,-0.00884437,0.00714604,0.0031338,0.03865813,0.04115411,0.09170725,-0.03401928,-0.02100257,0.0338736,-0.04212895,-0.0022041,-0.07878918,-0.00883519,-0.02704372,0.03846275,-0.00291186,0.02273834,-0.07967258,-0.06101879,0.09517953,-0.05684102,0.10866332,-0.02209187,0.02225893,-0.01523616,0.07151422,-0.02466507,-0.02259261,0.01149612,-0.0036714,0.01295313,-0.07524794,-0.00895503,-0.01496049,0.03856606,-0.07620452,-0.00612115,0.02512035,-0.08277147,-0.00722079,-0.00097302,-0.0096908,0.02470365,0.01224628,0.05924717,0.04505135,-0.02045973,0.02239197,0.00238136,0.08014249,-0.0644138,-0.07878236,-0.00723812,-0.01235368,0.00195936,-0.00554687,-0.04766229,0.05505322,-0.00829162,0.00569334,0.07902467,0.02322598,-0.00262967,0.04697075,0.04631741,0.02979798,0.08243529,0.0072468,0.01189383,-0.01057753,0.011312,0.06084448,-0.0216109,-0.01055764,-0.02584136,-0.00740786,-0.04162676,0.0552298,0.0591846,0.03188482,-0.04214349,0.05556985,0.07987947,0.00952322,-0.01084511,0.04005809,0.03475918,-0.02202047,0.01202184,-0.01339073,-0.024547,-0.26978022,0.003908,-0.08817238,0.09077098,-0.01339344,0.00487051,0.02451586,0.00459734,-0.02947334,-0.02261415,-0.00166694,0.05160248,0.00679933,-0.07174794,-0.00541428,0.02047905,-0.00161441,-0.05924363,0.04498501,-0.03440229,0.06175459,0.02363175,0.24266894,0.00419654,-0.00261154,0.02305598,0.00079964,0.01262091,0.0095844,0.01856568,-0.02133928,-0.02394045,0.06428034,-0.01495948,-0.04465595,0.03315618,0.01280368,0.02567601,-0.01763749,-0.02142255,-0.06439343,-0.03411356,-0.0586769,-0.00308286,0.1570355,0.00179643,-0.02321094,-0.07861928,0.03856828,0.06157419,-0.09934968,-0.04768546,-0.03974259,-0.00966196,0.01967531,0.00337059,-0.00594671,0.03895776,-0.03797179,-0.00422955,0.03903478,-0.01319739,0.05586692,-0.00327121,-0.01821163],"last_embed":{"hash":"1qbhuro","tokens":295}}},"text":null,"length":0,"last_read":{"hash":"1qbhuro","at":1753423528025},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5 New Version: 3.01#{9}","lines":[108,115],"size":1078,"outlinks":[{"title":"Original software generates lotto combinations position by position.","target":"https://saliu.com/images/lotto-ranges.gif","line":5},{"title":"Lotto software generates from ranges of numbers in 5 or 6 positions.","target":"https://saliu.com/images/lotto-positions.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10873451,-0.06720661,0.02103122,-0.07462265,-0.04679031,0.04614745,-0.04983437,-0.01448583,0.0893342,0.00287859,0.05992574,-0.00609338,0.06427074,0.01500113,-0.00345938,-0.03719671,-0.00482451,-0.01695118,-0.07370888,-0.05159706,0.07210954,-0.04448364,-0.04619616,-0.09948263,0.05701018,0.01411514,-0.05930663,-0.02575687,-0.02891027,-0.27466524,0.0287727,0.05195228,-0.0032686,-0.0454521,-0.06698023,-0.07134333,-0.05438759,0.10281762,-0.04402399,0.03595751,-0.00251101,0.02112034,-0.02754175,-0.04274637,-0.01503488,-0.01266328,0.00442913,0.00493863,0.00333784,-0.00854017,-0.05474671,-0.02238902,-0.01726153,0.04880122,0.06201963,-0.02794686,0.0431849,0.06272142,0.03591866,0.04332271,0.0781696,0.04063668,-0.18032983,0.05260001,0.04287754,-0.00372083,-0.00678504,-0.04234298,-0.00196096,0.07472195,-0.00695238,0.02768398,-0.00903876,0.07657104,0.05091729,-0.04857758,-0.04170015,-0.03132347,-0.07755312,0.03095962,-0.02633741,-0.06150085,-0.03281772,-0.00634094,0.02722124,0.024969,0.06042105,0.05799573,0.03845529,-0.0220643,0.04815606,0.00072925,0.00843586,0.01535858,0.00578719,0.02160019,0.00368674,0.01288711,-0.03415637,0.08561929,0.00978954,0.01272468,-0.00737239,0.08511074,-0.00352862,-0.00137818,-0.06088826,-0.02416916,-0.03650858,-0.02785542,0.03112089,0.01677245,0.06586234,-0.07479084,-0.03881381,0.01759585,0.00381471,-0.00161491,0.00083047,0.00015834,-0.04409776,0.02322561,-0.00431718,0.01697806,0.03077769,0.04848481,0.03474807,0.09368687,0.03688667,0.04760235,0.05688348,-0.00183131,-0.11080056,-0.04459053,-0.02465147,-0.00111667,-0.00170852,-0.01293181,0.05370509,0.01880525,0.0148408,-0.02788575,0.07679907,-0.08693967,0.02857333,0.116904,-0.03451927,0.03878974,0.01087959,0.00342311,-0.00905071,-0.0137875,-0.0249499,-0.04989386,-0.00066049,0.01923594,0.04830191,0.0434365,-0.06332811,-0.014478,-0.05359485,0.00954445,0.0013585,0.13269122,0.00369115,-0.08577607,-0.03451043,0.06493923,-0.02841044,-0.06087437,-0.0136203,0.02196973,-0.03538657,0.01831956,0.08241446,-0.02534704,-0.05773737,-0.04130195,0.00359808,0.01863979,0.03822286,-0.02219808,-0.03598396,-0.00585011,-0.04888507,-0.09670326,0.00824141,-0.01120002,0.03480998,0.05885532,-0.05055925,0.00826988,-0.00169679,-0.03473182,-0.02763378,-0.07655866,-0.0558647,0.00911443,0.06210253,0.00136154,0.04877608,-0.00387061,0.03162823,0.02743619,-0.02101468,0.05274336,0.0375533,-0.04278995,0.10185467,0.01633819,-0.01547418,-0.02753339,0.03509558,0.04962605,-0.04794832,0.03219731,0.00921863,-0.00224021,0.02580721,0.00250409,-0.00383353,0.04265089,-0.03385214,-0.21939874,-0.0262315,-0.00442932,0.02906905,-0.01854389,-0.00278751,0.00776855,-0.00947305,0.02784382,0.06903187,0.09603645,-0.05817064,-0.0256799,0.01837865,-0.04848896,-0.00750919,-0.07443086,-0.07548004,-0.03230689,0.02945042,-0.00191858,0.03430781,-0.06423334,-0.07510909,0.0764639,-0.05318946,0.14089181,-0.00095729,0.01242648,-0.01547151,0.07000238,0.02049313,0.02315922,0.02320743,0.00579012,0.04208969,-0.02371022,0.00539115,-0.00940583,0.01207384,-0.06789883,-0.00182559,0.00812179,-0.06029729,-0.01679944,0.00359612,-0.01334788,-0.00152018,0.01228184,0.06004407,0.04595705,-0.04487334,0.02308567,-0.00959853,0.07563049,-0.0457526,-0.05063013,-0.00513678,-0.008785,0.01653682,-0.00921534,-0.03982441,0.03587766,-0.01859138,-0.01182841,0.05837169,0.03333927,0.00679946,0.04824067,0.01709872,0.01430711,0.05232039,-0.00152262,0.01725867,-0.0044189,0.04028993,0.04039365,-0.01311292,0.01430171,-0.03851817,0.01541353,-0.02198336,0.02352167,0.03584234,0.0102337,-0.03877788,0.04684123,0.04444227,0.00569217,0.01613418,-0.02302686,0.04783862,-0.00635916,0.02288048,0.04937843,-0.00303932,-0.25347659,0.01508864,-0.06560477,0.0273571,-0.01040914,0.005914,0.01392844,-0.01551213,0.00018164,0.02084434,0.00233499,0.06798504,0.04206617,-0.07761987,-0.01408032,0.00794896,0.00299065,-0.03631613,0.06438051,-0.03916409,0.07941895,0.05332144,0.26715863,-0.00596494,-0.01792232,0.01536617,0.0005683,0.01041441,-0.03147581,-0.00123115,-0.00865531,-0.00145795,0.0628138,-0.01863345,-0.05848094,0.00883246,0.02463789,0.04556264,-0.0073745,-0.00523014,-0.08766779,-0.0162907,-0.04448118,0.00080244,0.15534277,-0.00713497,-0.02711229,-0.07456432,0.03502029,0.05634931,-0.11294822,-0.04021115,-0.01819384,-0.00971031,0.00017879,0.04134648,-0.00920731,0.03647862,-0.02752887,0.01332067,0.04090962,-0.02701509,0.06238646,0.00367787,-0.03301523],"last_embed":{"hash":"16rewgk","tokens":342}}},"text":null,"length":0,"last_read":{"hash":"16rewgk","at":1753423528154},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1","lines":[116,132],"size":1332,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11278991,-0.08514803,0.00745595,-0.07494576,-0.03763206,0.05652418,-0.02480568,-0.0055335,0.09501531,-0.01017522,0.04779091,-0.00681774,0.03728316,0.01232039,-0.01898311,-0.0366225,0.01505928,-0.00263409,-0.0544035,-0.04528132,0.07774024,-0.03198785,-0.06000623,-0.07248776,0.06992641,0.00053565,-0.07193025,-0.02795684,-0.03062778,-0.246613,0.01754698,0.04514235,0.00226685,-0.05268826,-0.06176547,-0.06731907,-0.05479965,0.1183714,-0.034786,0.02966986,-0.01766555,0.00270331,-0.04240204,-0.04758104,-0.0280378,-0.0299561,0.03053068,0.01537762,0.03767884,-0.02606546,-0.05513212,-0.01757476,-0.0200442,0.04142141,0.05829627,-0.02039484,0.04709285,0.05348287,0.04846269,0.03725786,0.08249489,0.00922188,-0.19040422,0.07333016,0.028008,-0.02265991,-0.00626981,-0.03597517,-0.03140651,0.06945648,-0.00355345,0.01081406,-0.00894618,0.07667294,0.07134748,-0.06024255,-0.02653052,-0.05654345,-0.08033665,0.00764254,-0.03629416,-0.05282192,-0.03186615,-0.03153289,0.03146326,0.0459683,0.07537295,0.06045423,0.04224397,-0.03449226,0.04695591,-0.00107655,0.00134637,0.0151583,-0.0111219,-0.00351777,-0.0093296,0.0219514,-0.00504187,0.11582896,0.00486435,0.01994116,-0.0237197,0.09391136,0.02228689,0.00810317,-0.04298161,-0.02924371,-0.0343734,0.00084563,0.0293015,0.04896093,0.07099877,-0.06988028,-0.03855653,0.01659137,-0.01716296,-0.0029337,0.00574727,-0.00535213,-0.03341527,0.00648642,-0.00860371,0.00629582,0.02683145,0.0389571,0.06161834,0.08000673,0.02503216,0.04328759,0.05890956,-0.02370492,-0.11656254,-0.04221622,-0.03944265,0.02591725,-0.00339064,-0.03805327,0.01511893,0.00876882,-0.01062096,-0.05034547,0.05201407,-0.08763059,0.02610584,0.11809047,-0.0726141,0.0389784,-0.00029166,0.01515073,-0.01904477,-0.02452153,-0.01295773,-0.0357487,-0.01151271,0.0258614,0.03855889,0.04971573,-0.04260264,-0.02163087,-0.02614903,0.00285747,-0.01904764,0.14445886,-0.02648344,-0.0830099,-0.05264919,0.06891972,-0.01720307,-0.06164647,0.00123612,0.00529146,-0.02564459,0.03479799,0.06629431,-0.01679829,-0.01335034,-0.04468849,-0.00502602,0.00476006,0.0304036,-0.04202484,-0.03354659,-0.00038681,-0.01590061,-0.0658635,-0.00000207,-0.02281745,0.06464048,0.07191151,-0.04373346,0.00534376,-0.00239472,-0.05242755,-0.0304202,-0.0642101,-0.08037983,0.0174592,0.05791407,0.00212667,0.06242364,0.01615647,0.03016026,0.03347757,-0.03091502,0.06848644,0.03766365,-0.03088778,0.06782167,-0.00273434,-0.02795791,0.01449947,0.04520367,0.0236224,-0.03071869,0.00966294,0.00568641,-0.00382903,0.02797814,0.02452836,-0.01088834,0.03599152,-0.03879343,-0.21845116,-0.01979081,0.00226114,0.01294038,-0.02547689,0.03005883,0.00912392,0.01672904,0.0731782,0.05236115,0.11775457,-0.04585598,-0.02031615,0.02309806,-0.04485489,-0.01687775,-0.04808151,-0.07753753,-0.01169888,0.0324356,0.00524787,0.02809582,-0.08284695,-0.0663735,0.10810437,-0.04943402,0.11827689,-0.00855803,0.01574135,-0.00438283,0.06056907,0.01934098,0.00941315,0.02037222,0.00430238,0.03707564,-0.03821444,0.02415325,-0.04037083,0.01204118,-0.06432198,-0.00389791,0.02684987,-0.06689621,-0.00003161,-0.00060455,0.00099914,0.00412833,0.0169263,0.05068715,0.05148083,-0.02643141,-0.00076778,0.00334966,0.08373745,-0.03983852,-0.03540475,-0.02452256,-0.01324231,-0.00234635,-0.00796268,-0.03145703,0.05150723,-0.01362746,-0.03402891,0.07941393,0.03507808,-0.01397177,0.05409237,0.03606633,0.00287079,0.04321661,0.00039226,0.00373175,0.0005227,0.0475422,0.05642547,-0.01508801,0.00862103,-0.02584894,0.01873478,-0.02252917,0.0527627,0.02626554,0.02045018,-0.02670784,0.03538134,0.04604197,-0.00818577,0.02604453,-0.00053651,0.03991126,-0.00648627,-0.00545922,0.04697751,-0.00995033,-0.25720346,0.02392555,-0.06406386,0.04242599,-0.00242366,0.02908688,0.01804962,0.0051034,-0.01228718,0.03221032,-0.02999306,0.065008,0.02147367,-0.06481642,-0.02976981,-0.00091872,0.01436499,-0.0598394,0.06575098,-0.01794874,0.05769654,0.05645809,0.25992423,0.00694826,-0.00191954,-0.01203472,0.00483259,0.00961429,-0.03660759,0.00750252,0.00129718,-0.02431286,0.03956605,-0.00703929,-0.0535812,0.02344436,0.03032871,0.0331796,-0.00302031,0.0149182,-0.08685128,0.00108157,-0.07288215,-0.00973322,0.13374698,-0.00998645,-0.05442884,-0.04754137,0.02633308,0.04951647,-0.12149141,-0.06522197,-0.05419381,0.00192564,-0.00094967,0.03401336,0.01473019,0.04286033,-0.01485672,0.0030348,0.04998995,-0.0259141,0.05342344,-0.0087912,-0.01203258],"last_embed":{"hash":"1p7d8i7","tokens":88}}},"text":null,"length":0,"last_read":{"hash":"1p7d8i7","at":1753423528274},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{1}","lines":[118,119],"size":235,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10785107,-0.06632712,0.02277175,-0.06747144,-0.06123565,0.0394735,-0.05491477,-0.01131015,0.07783164,-0.01382881,0.06460153,0.00050332,0.0759892,0.01251299,0.00725899,-0.03988199,-0.00943228,-0.01429382,-0.06152808,-0.0484883,0.08005111,-0.03993949,-0.02983179,-0.09538216,0.04403155,0.02295797,-0.04428413,-0.02394449,-0.01392799,-0.27354372,0.03647767,0.04632837,-0.00971094,-0.04849675,-0.07397365,-0.06818708,-0.0498814,0.08124344,-0.04994191,0.06585437,0.00317197,0.03182555,-0.01560541,-0.03914406,-0.0215475,-0.01332767,0.0049085,0.0090338,-0.01365856,-0.00327716,-0.0716915,-0.02373084,-0.02364253,0.04090564,0.0521757,-0.01586604,0.04467279,0.07003317,0.03542937,0.05614454,0.06699986,0.05102045,-0.17383285,0.03916369,0.02679133,0.01078582,0.00469685,-0.0400767,0.01075932,0.07709207,-0.00347774,0.03804101,-0.01372782,0.07551372,0.03150202,-0.0341746,-0.06021053,-0.02847732,-0.07531389,0.0370748,-0.02086009,-0.0659923,-0.04228395,-0.0075319,0.01904947,0.01795206,0.06234749,0.04918341,0.04307329,-0.01912364,0.04264032,0.00864518,0.01257599,0.02634938,0.00978246,0.01534713,0.027384,-0.00251489,-0.02973963,0.09373184,0.00493737,0.01150477,0.00210936,0.07730238,-0.00741704,-0.01930694,-0.06264361,-0.03034169,-0.0283994,-0.02166287,0.04596876,-0.00446134,0.06179684,-0.08640861,-0.04302166,0.00968751,-0.00162005,-0.00238375,0.02176017,0.00696615,-0.04577535,0.01805681,0.00848703,0.01460879,0.04913497,0.05920954,0.0207808,0.09635036,0.02561026,0.04537545,0.04390361,-0.0063364,-0.11138193,-0.04975263,-0.00417826,-0.01224957,0.00372826,-0.00408785,0.06002606,0.02424617,0.00463337,-0.0201193,0.09867233,-0.09404098,0.02552926,0.12134716,-0.01064813,0.03953142,0.01371688,0.00092118,0.00258398,-0.00836393,-0.02559404,-0.05183174,-0.00590017,0.02008475,0.04292043,0.03730891,-0.06551094,-0.02125415,-0.05835168,0.01055415,0.01513025,0.12427869,-0.00844356,-0.0903719,-0.0197208,0.04920122,-0.04274878,-0.07200621,-0.021925,0.02524657,-0.04292187,0.00191314,0.09245785,-0.01166414,-0.07763488,-0.03449244,-0.00044472,0.01555053,0.0350388,-0.01671751,-0.03060343,0.00056029,-0.0571858,-0.10147554,0.0168635,-0.01180134,0.02896889,0.0422946,-0.03775804,0.00730751,0.00270566,-0.02034385,-0.03245952,-0.06678709,-0.04965447,0.01205578,0.07153855,-0.00476122,0.0417427,-0.00306605,0.03752836,0.0315098,-0.00185541,0.05354109,0.02998842,-0.05793148,0.10985783,0.0291756,-0.01617323,-0.0328587,0.03521991,0.05654833,-0.0613398,0.04570266,0.01164788,-0.00266118,0.00756283,-0.01887468,0.00203065,0.04952069,-0.04526697,-0.21635066,-0.03241452,-0.0103536,0.03147434,-0.0046126,-0.0027692,-0.00287059,-0.01559278,0.01524434,0.07902236,0.07754972,-0.06769121,-0.03671684,0.02902721,-0.04724858,-0.01030501,-0.08289748,-0.07807454,-0.04451093,0.01844007,0.01220144,0.03317273,-0.06991587,-0.07547113,0.06947871,-0.04921029,0.12718862,-0.02143303,0.02632922,-0.01990248,0.0655441,0.00303486,0.02030638,0.03396076,0.01271675,0.04626304,-0.02269246,-0.01335574,-0.00809884,0.02220863,-0.06819659,0.00355662,0.01300636,-0.06137861,-0.02683304,0.00709597,-0.00432153,0.00056416,0.00984906,0.05017329,0.03587488,-0.02528882,0.02215001,-0.00472593,0.05871167,-0.04393411,-0.04643763,0.00334149,-0.01535282,0.01218042,-0.00833636,-0.03782072,0.01658526,-0.01624475,-0.00187669,0.04471361,0.03346441,0.00228787,0.04671809,0.01121034,0.02114884,0.06228615,-0.00098492,0.01815032,-0.00685305,0.0273197,0.04258426,-0.02722649,0.01247878,-0.04116335,0.02278257,-0.00086891,0.0131118,0.04383611,0.00493128,-0.03643646,0.05641731,0.05124,-0.00109208,0.00944552,-0.02425978,0.0484893,0.00848659,0.02218026,0.05072613,0.00544063,-0.24331674,0.00672071,-0.0543201,0.04161397,-0.01925546,-0.00451313,0.00319763,-0.02421278,-0.01840653,0.00330445,0.02006661,0.04990108,0.03544529,-0.06181255,-0.00968071,0.01733351,0.0143432,-0.03335508,0.07491275,-0.0393794,0.08301112,0.04632319,0.272479,-0.01556337,-0.02120703,0.03918475,0.01601922,0.01975029,-0.03408458,-0.00411632,0.00458308,-0.00356993,0.06402259,-0.02909469,-0.06031476,0.00185737,0.02759675,0.0406079,-0.01106943,-0.00820072,-0.07769658,-0.02161601,-0.03996053,0.01460814,0.16370916,-0.01095701,-0.02361357,-0.087075,0.0287773,0.06715427,-0.10985732,-0.03541376,-0.02160824,-0.01913054,0.02637511,0.04840582,-0.01118223,0.03123558,-0.02941782,0.00851317,0.03450337,-0.01620967,0.04893474,-0.00226371,-0.03795836],"last_embed":{"hash":"1sv0zdb","tokens":253}}},"text":null,"length":0,"last_read":{"hash":"1sv0zdb","at":1753423528308},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Range-5: New Version 3.1#{8}","lines":[127,132],"size":957,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10560494,-0.03138778,0.01274465,-0.04721874,-0.0445938,0.06984428,-0.03540915,-0.01080801,0.058355,0.02053883,0.03362239,-0.02144297,0.06777953,-0.01770614,0.00548597,-0.0361278,0.01338998,-0.02876767,-0.04050568,-0.00768502,0.07941284,-0.04652208,-0.0254939,-0.10830946,0.04240875,-0.00257105,-0.02716348,-0.04785173,-0.05123882,-0.25413221,0.03560852,0.0429234,0.0059201,-0.04922318,-0.06877368,-0.0591233,-0.04693587,0.053866,-0.04521231,0.03868617,-0.00050671,0.0437644,-0.02540331,-0.05201487,-0.023476,-0.00273639,0.00444023,0.00026465,0.00206985,0.00313561,-0.06368281,-0.03368829,0.01068831,0.07383199,0.04674149,0.0288497,0.04716392,0.04328939,0.01118973,0.05865046,0.06647978,0.03970513,-0.18840049,0.04762566,0.0190306,0.0327238,0.02778164,-0.03965508,0.02672142,0.05342332,0.01845293,0.05236553,-0.02697089,0.06593448,0.05421036,-0.01107196,-0.01771531,-0.01484555,-0.02882459,0.02158527,-0.02795238,-0.0512796,-0.03739047,-0.04698319,0.03145795,0.03077601,0.05125915,0.05086754,0.03592002,-0.02816615,0.04326671,0.00915871,0.00971318,0.01953961,0.0114375,-0.00391677,0.02019938,-0.02492434,-0.03985311,0.10204948,0.02805399,0.02809559,-0.01538644,0.07142583,-0.00063593,-0.03649371,-0.06797504,-0.00436604,-0.04396595,0.0013531,0.07802734,0.05264057,0.05679199,-0.07287261,-0.05996557,0.00476727,0.03070941,0.01214238,0.03307413,0.00444587,-0.03349664,0.04444111,0.02964607,0.0568022,0.00989151,0.0414699,0.02033935,0.09243032,0.04467583,0.05624387,0.05014609,0.00719244,-0.1045912,-0.07577576,-0.00461184,-0.00838159,0.00733423,0.00431407,0.03085036,-0.00509483,-0.01507184,-0.07883322,0.06893467,-0.09624549,-0.00498336,0.1066658,-0.02401248,0.02265129,-0.00630845,0.00371772,-0.01012828,0.0116652,-0.04639848,-0.0394273,-0.02477907,0.02245336,0.04495646,0.06206026,-0.06177806,-0.01090183,-0.05520985,0.00075008,-0.02254847,0.1038027,0.02378582,-0.10670453,-0.05128451,0.04276968,-0.05666206,-0.05621646,-0.0246918,0.04693666,-0.04091159,0.00565734,0.07650124,0.00639846,-0.05531716,-0.01513637,-0.01109055,-0.00063213,0.03523735,-0.02884887,-0.02453918,0.00738358,-0.07562377,-0.10402155,0.0123094,-0.01324653,0.01308556,-0.01053887,-0.0108799,-0.02530514,-0.00453488,0.00288791,0.00561941,-0.02843871,-0.0343498,-0.00229456,0.0787004,-0.01862456,0.071689,0.01446318,0.02532649,-0.00431854,0.0122809,0.03757701,0.02295907,-0.05452887,0.10450854,0.01896953,-0.00058141,-0.04936508,0.06900294,0.03690402,-0.02474427,0.03498514,-0.02116237,0.00182026,0.05340524,0.00962668,-0.00862447,0.070614,-0.03183782,-0.24537823,-0.03007774,-0.00632234,0.0307483,0.00141392,0.00831841,0.00689374,0.00680047,0.04990543,0.06960336,0.06337603,-0.0596339,-0.03008147,0.05774756,-0.03058117,-0.02728444,-0.08210811,-0.05606175,-0.03477003,0.05118299,0.01693129,0.03661156,-0.0714312,-0.07405744,0.07329473,-0.04484124,0.13490444,0.01820454,-0.02637733,-0.02965683,0.05495803,0.02928205,0.02179584,-0.01516898,0.01743623,0.05213892,-0.00940552,-0.02888072,0.00089678,-0.00700951,-0.09471085,0.00965168,-0.01065057,-0.1082209,-0.02779273,0.01020201,-0.01540743,-0.01419171,0.01439523,0.0711953,0.05862581,-0.05599009,0.06167338,0.00271207,0.05484541,-0.05170899,-0.06214128,-0.00145978,-0.00600093,0.02651732,-0.03091205,-0.04019351,0.03355652,-0.00741404,-0.0252691,0.01634089,0.00177897,0.01617282,0.03374333,-0.00694572,0.02522368,0.07283847,0.01835561,0.00070479,0.0094994,0.03234488,0.01839256,0.0006696,-0.0011483,-0.03683589,0.00395672,-0.01197035,0.0038137,0.05640633,0.04375757,-0.02531049,0.08662497,0.05447048,-0.00191168,-0.00099627,-0.02958703,0.04733011,-0.00292575,0.03474482,0.04437697,-0.00402269,-0.2743296,0.0254993,-0.04329681,0.05556511,-0.01668913,-0.03533576,0.01049712,-0.00771499,-0.00686591,0.02003081,0.01369229,0.04936441,0.04155176,-0.07604858,-0.01818366,-0.00761604,-0.00150601,-0.04609516,0.03875624,-0.05908326,0.05288586,0.04039123,0.25511533,0.01887333,-0.00453513,0.03914101,-0.01623546,0.04575205,-0.00751109,-0.00494884,-0.02045455,-0.00361467,0.0699921,-0.0175051,-0.04277602,-0.02008869,-0.00304576,0.04269777,-0.02282984,0.01245927,-0.08188907,-0.00647202,-0.01593795,0.03911974,0.13825797,-0.00945372,-0.01574179,-0.09204769,0.05788875,0.05921068,-0.07913086,-0.02158576,-0.03819302,-0.01396614,-0.00363108,0.01893313,-0.0210813,0.00687925,-0.01899756,-0.00118068,0.02599937,-0.01695133,0.04081452,-0.01045338,-0.02602689],"last_embed":{"hash":"1pg2mbg","tokens":418}}},"text":null,"length":0,"last_read":{"hash":"1pg2mbg","at":1753423528394},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version","lines":[133,193],"size":2857,"outlinks":[{"title":"Lotto combinations, Powerball, Mega Millions, Euromillions generate from groups of numbers by lines.","target":"https://saliu.com/images/lotto-positions.gif","line":45},{"title":"The lotto software also creates gambling systems from skips of numbers.","target":"https://saliu.com/images/skip-lotto5.gif","line":47},{"title":"This Powerball, Mega Millions skip system hit the jackpot several times.","target":"https://saliu.com/images/combinations-lotto.5.gif","line":49},{"title":"_**Lottery Strategy and Software Based on Number Frequency, Statistics**_","target":"https://saliu.com/frequency-lottery.html","line":51},{"title":"_**Skip Systems, Software: Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":52},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":53},{"title":"_**Powerball Strategy, Systems: Numbers, Drawings, Skips, Software**_","target":"https://saliu.com/powerball-systems.html","line":54},{"title":"Download your lottery software to create lotto systems based on positional frequency.","target":"https://forums.saliu.com/HLINE.gif","line":56},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":58},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":58},{"title":"Search","target":"https://saliu.com/Search.htm","line":58},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto skips.","target":"https://forums.saliu.com/HLINE.gif","line":60}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1261472,-0.0173907,0.01384396,-0.03797721,-0.04794994,0.06150698,-0.03010691,-0.01827607,0.05502971,0.01194847,0.04136257,0.00393403,0.06109696,-0.03472068,-0.00012333,-0.04349578,0.03564223,-0.00324811,0.01198869,-0.00647524,0.06555148,-0.03349032,-0.04025685,-0.09195981,0.03347106,0.00575442,-0.0366696,-0.04316988,-0.053507,-0.23511383,0.02134277,0.02032925,-0.02107227,-0.05412229,-0.06789178,-0.06164594,-0.02823388,0.05808241,-0.04042934,0.05272323,-0.00652029,0.03237221,-0.03592671,-0.05477254,-0.03194685,-0.00102169,0.027249,-0.00416763,-0.01097995,-0.01406291,-0.0697558,-0.02138121,0.01953154,0.06259554,0.04575993,0.03353759,0.03721939,0.04998032,0.01168048,0.04348672,0.0871872,0.01682491,-0.18059352,0.05830596,-0.00120173,0.0206357,0.03675758,-0.05523906,0.03741774,0.0655636,0.01037077,0.0444671,-0.03968104,0.08785535,0.06534104,-0.02550892,-0.01022576,-0.01321612,-0.01336303,0.01483771,-0.01968422,-0.07946482,-0.03380508,-0.03600607,0.03072463,0.03903719,0.05894385,0.03795265,0.04490497,-0.04200518,0.05311681,-0.00152296,0.01786755,0.02941191,0.02289554,-0.01059614,0.03014474,-0.03631281,-0.02892201,0.10671971,0.02001922,0.02459065,-0.00681465,0.08512408,0.00874428,-0.03274557,-0.06978678,0.01198969,-0.06259076,0.01127313,0.08038159,0.04922676,0.06594406,-0.05642411,-0.04325412,0.01728353,0.01317583,0.02810538,0.04820307,-0.00049053,-0.02613881,0.04022112,0.03341364,0.05902836,0.02262854,0.04036974,0.02633954,0.07430458,0.03405118,0.058884,0.05118727,-0.00772639,-0.09344232,-0.08822976,0.01004674,0.0169641,-0.00355049,-0.00713292,0.02425829,-0.00110324,-0.02637182,-0.06657839,0.07558059,-0.08956254,-0.02897359,0.11242919,-0.01869362,0.03426435,-0.01547754,0.00508624,-0.02219971,0.00956196,-0.04652423,-0.02416667,-0.03088473,0.027565,0.02610428,0.06617691,-0.06407288,-0.00449426,-0.07476,-0.01079476,-0.02493642,0.11801144,0.02116727,-0.10491335,-0.05015215,0.02685983,-0.0413596,-0.06312289,-0.01792791,0.05178661,-0.0559904,-0.00482935,0.07771683,0.01303316,-0.03903503,-0.01925499,-0.01472959,-0.0259907,0.05377486,-0.0380944,-0.02316017,0.0237407,-0.07018235,-0.11098593,0.01214422,-0.02471029,0.00998496,-0.01034162,-0.01175603,-0.02861951,0.00794988,-0.00839727,-0.00147495,-0.03458578,-0.03973427,-0.00020271,0.06482137,-0.01123469,0.07260991,0.01305256,0.02279694,-0.00318227,0.00430016,0.03212274,0.02485469,-0.04525804,0.08653894,0.0223011,-0.01314026,-0.0331301,0.07654034,0.01350323,-0.0104053,0.03460297,-0.0231021,-0.00109193,0.03130388,-0.00794487,-0.01252697,0.11526338,-0.01955299,-0.24090719,-0.03109861,-0.01280257,0.05474726,0.00361836,0.00324286,0.00086016,0.01751821,0.05515261,0.05559184,0.08692743,-0.07800713,-0.01753281,0.0778827,-0.01409479,-0.01377062,-0.04523419,-0.08306188,-0.02397214,0.02984096,0.01900095,0.04366155,-0.06973279,-0.08931608,0.08867305,-0.04270509,0.11655867,-0.00639565,0.00729962,-0.02038152,0.04642498,0.01977616,0.02508227,-0.03394542,0.01865175,0.07573237,-0.01471651,-0.02909764,0.00049693,-0.00097134,-0.09572743,0.00730473,-0.00519371,-0.09528229,-0.02498734,0.00986878,-0.02083573,-0.00606842,0.01046987,0.05267391,0.05147737,-0.04588409,0.03971559,0.00951189,0.04014315,-0.03722917,-0.03519068,0.00172307,-0.00796078,0.01443111,-0.04348569,-0.03740378,0.01050487,-0.00247858,-0.01479103,0.01634219,0.00498824,0.00729529,0.04570464,-0.00615837,0.0128632,0.0662929,-0.01146716,-0.02348727,-0.00396623,0.04931031,0.01347916,-0.00021749,-0.01618818,-0.01652548,0.00139725,-0.00507466,0.00672963,0.05290538,0.04693663,-0.02645013,0.0930177,0.04604982,-0.00464451,0.00106527,-0.02424684,0.04657447,-0.00919809,0.02411495,0.05939089,-0.0020428,-0.26517633,0.01266268,-0.04510851,0.0509546,-0.03375023,-0.03710152,-0.00614718,-0.01909736,-0.00374165,0.0299291,0.00644561,0.02892953,0.02422334,-0.06859509,-0.0261056,0.01812924,0.01999035,-0.026012,0.03210276,-0.08212953,0.04405587,0.04166346,0.26137629,0.0269714,-0.008325,0.03622238,-0.01687581,0.03787258,-0.00312195,0.01206176,-0.00447428,-0.00638239,0.05703995,-0.00878519,-0.05280982,-0.03593998,-0.00605219,0.04694831,-0.01440394,0.02463306,-0.08362582,-0.0057889,-0.03307496,0.04783447,0.13610518,-0.02431438,-0.02682717,-0.0963058,0.05561252,0.06689245,-0.07396087,-0.02982211,-0.04488606,-0.01385527,0.00685249,0.01535043,-0.03245271,0.00551103,-0.03438833,-0.00529098,0.03684495,-0.0248182,0.06096222,-0.02373615,-0.00500192],"last_embed":{"hash":"gk0edf","tokens":148}}},"text":null,"length":0,"last_read":{"hash":"gk0edf","at":1753423528551},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{1}","lines":[135,138],"size":460,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{24}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07693938,-0.03852488,-0.00432242,-0.06684891,-0.06758509,0.08346041,-0.06083598,-0.01870932,0.06889928,0.02809679,0.02203226,-0.02505983,0.05997456,-0.02681791,0.00609023,-0.00251212,0.03715069,-0.02326,-0.04609405,-0.00423468,0.09396977,-0.04326024,-0.03606938,-0.09619936,0.02123786,-0.00045476,-0.04791807,-0.06859735,-0.02563762,-0.22938709,0.03021268,0.03850958,0.02561749,-0.05873118,-0.06238716,-0.05762316,-0.05052869,0.04673581,-0.04095117,0.04675351,-0.01780409,0.05439831,-0.00327119,-0.03620683,-0.04122499,-0.01821495,0.01204197,0.00969697,0.03088921,-0.00754273,-0.07107459,-0.02344676,0.01877317,0.06117295,0.05333484,0.01112452,0.0129376,0.05694601,-0.02111879,0.0503351,0.06229953,0.03081772,-0.18189882,0.00366739,0.0053014,0.03269872,0.01198894,-0.03809032,0.01652675,0.07850451,0.0160796,0.05269235,-0.01365108,0.0721923,0.05425256,-0.00646212,-0.04131434,-0.02297123,-0.01033781,0.01184575,-0.06057964,-0.06547996,-0.02671762,-0.04722093,0.04192315,0.02539221,0.06644358,0.07298395,0.04022216,-0.04623959,0.05470578,0.03143099,0.01409446,-0.00882965,0.01624975,-0.00482504,0.0194523,-0.0509495,-0.00447122,0.11292842,0.01963137,-0.00043318,-0.01970791,0.06045343,-0.0228131,-0.01071582,-0.05778223,0.00425617,-0.05830839,-0.00391312,0.05180779,0.04715703,0.05892399,-0.06955721,-0.02463855,0.01218615,0.02755148,0.00615571,0.01961287,0.02291578,-0.01129861,0.03270892,0.03279452,0.01509392,0.03800571,0.03824252,0.0171328,0.09019318,0.04151821,0.04978701,0.02378891,0.01498481,-0.1055714,-0.04154524,0.00239096,-0.02966332,0.01091202,0.00131027,0.01179301,0.01906943,-0.00902053,-0.06349868,0.08490244,-0.10724672,0.018073,0.13298996,-0.03117222,0.04001346,-0.02964163,-0.01256207,-0.00959796,0.00399533,-0.01949436,-0.07319104,-0.02475785,0.0499857,0.03854132,0.06418911,-0.04855841,-0.00676567,-0.03125229,-0.01958613,-0.00356409,0.12559126,-0.01117535,-0.08210761,-0.03045703,0.0517403,-0.05331212,-0.05503662,-0.01683653,0.03714478,-0.03653754,0.01390124,0.10309898,-0.00626864,-0.04545827,0.01073543,-0.00142326,0.00098903,0.02335501,-0.02654293,-0.05462279,0.03164743,-0.05668569,-0.09184385,0.04395682,-0.00031613,0.01125395,-0.00199718,-0.01954605,-0.00993864,0.014624,0.00662466,0.00186719,-0.01134112,-0.03439485,0.00741739,0.08951364,-0.01897886,0.07875389,-0.00510282,-0.01086162,-0.01008492,0.03230327,0.05038631,0.01690776,-0.02999475,0.09652697,0.02772639,-0.00695604,-0.04581481,0.10148133,0.03789954,-0.02497761,0.03166783,-0.0188957,-0.00748795,0.04149722,0.01336986,0.01302762,0.03890849,-0.04617604,-0.23162095,-0.02076476,0.00726284,0.03004009,-0.00158188,0.02846611,0.01310359,0.01464035,0.05326303,0.05650453,0.06589694,-0.04388468,-0.06182276,0.0515545,-0.03059841,-0.04085015,-0.08832156,-0.06002116,-0.03737766,0.04745063,0.00279703,0.02066262,-0.07927711,-0.04225836,0.06497899,-0.05652054,0.12782417,-0.00485309,-0.02944406,-0.02807291,0.06087165,0.01000866,-0.01542516,0.0257409,0.02879861,0.03450954,-0.04630424,-0.04049505,0.01843794,0.01807112,-0.08089889,0.01911065,-0.02026787,-0.10600256,0.00230719,-0.01299996,-0.04782657,0.00370006,0.02879208,0.05641126,0.05698468,-0.02413717,0.05537788,0.00682455,0.07014848,-0.04903417,-0.0851585,-0.003116,-0.03338091,0.00817786,-0.03946092,-0.05098574,0.03830454,0.00527689,-0.01564399,0.04950029,0.01155086,0.00888375,0.00585593,-0.01937307,0.01861344,0.05403905,0.0125398,-0.01601561,-0.01738189,0.00816589,0.0290125,-0.02930943,-0.03008002,-0.02861094,0.02055508,-0.0443466,-0.0029366,0.04907281,0.02821285,-0.0109058,0.09001452,0.04699136,0.01314881,-0.00451707,-0.01439552,0.04541956,-0.00491602,0.02911313,0.01801841,-0.0060465,-0.27578181,0.02894359,-0.05410868,0.06821762,-0.02243341,-0.02203487,0.01202809,0.00183773,-0.02142188,0.01839783,0.01262716,0.04371323,0.0099356,-0.07016638,0.01199611,0.00579972,0.00781003,-0.04048825,0.04194088,-0.06068172,0.05578439,0.0378612,0.24740882,0.01532695,0.01699321,0.03812958,-0.02438859,0.07349309,0.0062575,-0.0163567,-0.0236165,0.00444803,0.06630988,-0.0195105,-0.03586944,0.01147659,0.01432534,0.02325051,-0.01074314,0.00070349,-0.07529554,-0.03463722,-0.04487656,0.05114144,0.15160459,-0.02932401,-0.03208181,-0.11573517,0.05644738,0.03536968,-0.09691547,-0.01178048,-0.03512023,-0.00107657,0.01328294,0.02726924,-0.00120746,0.007606,-0.02553452,-0.00715465,0.03973984,-0.01765023,0.02700323,-0.01010362,-0.0234332],"last_embed":{"hash":"1gvmbpj","tokens":91}}},"text":null,"length":0,"last_read":{"hash":"1gvmbpj","at":1753423528600},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{24}","lines":[164,167],"size":218,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{31}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09656079,-0.01449327,-0.0209294,-0.02228044,-0.04834174,0.08078837,-0.05805084,-0.02903126,0.04857717,0.01284255,0.03318671,-0.02786006,0.06961281,-0.0381289,-0.00084997,-0.03151037,0.00994963,-0.02592337,-0.02373721,-0.02292187,0.10088728,-0.03603179,-0.0196065,-0.08886445,0.01586907,-0.00805513,-0.02816063,-0.03058577,-0.02595553,-0.22636816,0.02167186,0.01635094,-0.02646209,-0.05921213,-0.08998889,-0.05707187,-0.04750061,0.05558218,-0.06706756,0.05875339,-0.00098088,0.02778263,-0.01323901,-0.07231628,0.00532929,-0.01004657,0.02922322,-0.00089927,0.04775466,0.00585869,-0.0604038,-0.00828309,0.00953133,0.05772161,0.07283946,0.00687074,0.04656632,0.05388012,-0.00096891,0.03536556,0.07631067,0.05426955,-0.19225968,0.03966513,-0.00278959,0.03068208,0.03070336,-0.0120909,0.03168006,0.04930404,0.01783745,0.03928418,-0.02429174,0.0872416,0.05681821,0.00421957,-0.04175894,-0.02162715,-0.05016378,0.04605022,-0.03446539,-0.04961417,-0.03340096,-0.06004192,0.0449671,0.0421707,0.05790986,0.03982966,0.05548473,-0.04125036,0.02676919,-0.00438767,0.03052232,0.02673836,0.016661,-0.00569307,0.05244006,-0.03769172,-0.01526701,0.1059361,0.03873363,0.01262823,-0.00662486,0.08258524,0.00712523,-0.02411556,-0.06217307,-0.00453788,-0.04905111,0.00630088,0.04139712,0.04310074,0.04985226,-0.03579295,-0.062309,0.00548251,-0.00895124,0.01650498,0.02118048,0.01658127,-0.03476492,0.03096639,0.00888239,0.05052514,0.02096147,0.02363412,0.01479531,0.06443252,0.0655495,0.03744798,0.01293004,0.00666055,-0.12434924,-0.06892622,-0.00487599,-0.00948622,-0.02302101,-0.00901294,0.0069302,-0.0218423,-0.01974183,-0.06402367,0.0547074,-0.11377224,0.01932253,0.08311188,-0.01347028,0.02159901,-0.02489727,-0.01111464,-0.01681298,-0.01602859,-0.04208755,-0.03514677,-0.02472755,0.04198865,0.06392594,0.09026998,-0.04925062,-0.01733216,-0.05413331,-0.01323485,-0.03863229,0.13550903,0.02125981,-0.08625884,-0.03353015,0.02040836,-0.04629267,-0.06203949,-0.05560828,0.0458346,-0.05933831,-0.0035158,0.08915518,-0.0116814,-0.03932145,-0.04084372,-0.03488225,-0.0082458,0.03054219,-0.03212553,-0.02190779,-0.00914557,-0.0654872,-0.12078398,0.01458483,-0.04323808,0.02606835,0.01356105,-0.02337304,-0.00293486,-0.01930762,-0.01680463,-0.02700041,-0.02222065,-0.03624273,-0.00452136,0.09153772,-0.01394066,0.03811412,0.02168699,0.02955012,0.00104904,-0.00629445,0.03942572,0.01536896,-0.04538784,0.07246802,0.00237036,-0.00932527,-0.02828248,0.09323417,0.04737159,-0.03748851,0.05728514,-0.01830253,-0.01091583,0.01721516,-0.00637604,0.02063216,0.07855617,-0.06311277,-0.21058106,-0.03757963,-0.02655966,0.02296684,-0.00156393,-0.01507941,0.03015402,0.00418402,0.03978926,0.06125913,0.07961573,-0.08164957,-0.03546178,0.07502245,-0.02456833,-0.00029916,-0.08546374,-0.07342488,-0.02198557,0.05454553,0.00532428,0.02704282,-0.05008636,-0.08503824,0.07395528,-0.05383063,0.13054758,0.04195342,-0.02612817,-0.03523582,0.07693031,0.01781266,-0.01311407,-0.04432344,-0.00251497,0.02871789,-0.00825941,-0.01140599,-0.00789824,-0.00916416,-0.07059906,0.02451135,-0.00160378,-0.10551771,-0.00656538,0.02503817,-0.02702449,0.00257345,0.00517103,0.06217672,0.07718863,-0.03662468,0.03588901,0.00982999,0.05968271,-0.03851282,-0.0510778,-0.00418581,-0.0167517,0.0225056,-0.01641365,-0.03104487,0.02711317,-0.01713967,0.02129568,0.05267839,-0.00209714,0.00699469,0.04806645,0.00755751,0.01502593,0.08690448,0.02449746,0.00180704,0.01001112,0.04238692,0.01567873,-0.03765094,-0.01914163,0.00055236,0.00412253,0.01505126,0.01958412,0.07042176,0.05655956,-0.02259811,0.08992996,0.0364856,0.01166671,-0.00123662,-0.04130492,0.03917249,-0.01053765,0.05191302,0.02525702,0.0104278,-0.2692585,0.01905309,-0.03983341,0.05126129,0.00365647,-0.02740166,-0.01328745,-0.02535682,-0.00786437,0.02869382,0.03770193,0.01821433,0.0553402,-0.05954136,0.00582575,0.0143105,0.00609027,-0.02024466,0.06947428,-0.05150304,0.0484313,0.03640412,0.26633099,0.01772384,-0.00513201,0.03252288,-0.00057186,0.02662846,0.01669112,0.04027535,-0.0131297,0.0130261,0.05254357,0.002015,-0.03910419,-0.00026524,-0.01053594,0.04689611,-0.03174877,-0.02160034,-0.08188505,0.01120194,-0.03209183,0.02768035,0.12428889,-0.0149246,-0.01822929,-0.09446214,0.05534371,0.05482464,-0.10148086,-0.03937803,-0.0543283,0.00359863,0.02416646,0.02763997,-0.02251301,0.01055847,-0.01833464,0.00558085,0.01796323,0.00004962,0.03145967,-0.0235167,-0.02360672],"last_embed":{"hash":"t1cyw8","tokens":182}}},"text":null,"length":0,"last_read":{"hash":"t1cyw8","at":1753423528635},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{31}","lines":[175,182],"size":489,"outlinks":[{"title":"Lotto combinations, Powerball, Mega Millions, Euromillions generate from groups of numbers by lines.","target":"https://saliu.com/images/lotto-positions.gif","line":3},{"title":"The lotto software also creates gambling systems from skips of numbers.","target":"https://saliu.com/images/skip-lotto5.gif","line":5},{"title":"This Powerball, Mega Millions skip system hit the jackpot several times.","target":"https://saliu.com/images/combinations-lotto.5.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{36}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12914759,-0.0106442,-0.01175404,-0.01426322,-0.0425331,0.07460453,-0.02359705,0.01466575,0.03803254,0.00132171,0.01736485,-0.01186691,0.04111849,-0.02514443,0.00611753,-0.01853627,0.02819364,0.02267236,0.03158805,0.02241068,0.05844277,-0.0114379,-0.03251112,-0.07043037,0.0212147,-0.01073724,-0.04557963,-0.04409033,-0.04795577,-0.17899437,0.00772833,0.03528241,-0.02347031,-0.04360687,-0.087199,-0.06686279,-0.03120908,0.05922533,-0.06762304,0.07019023,0.01821675,0.02719085,0.01251789,-0.04454295,-0.00986433,0.00686531,0.03423923,-0.00234428,0.03024395,-0.01286972,-0.08885752,-0.0051354,0.00420583,0.01933354,0.05857538,0.03503809,0.01618997,0.06029062,0.00057215,0.01274057,0.06822635,0.02027261,-0.23172617,0.08214349,-0.02530242,0.03157512,0.02167548,-0.02550598,0.03962421,0.05034585,0.00240349,0.02145938,-0.02147808,0.08028138,0.07195806,-0.01223739,-0.00334868,-0.03108027,0.00072245,-0.00002905,-0.00667386,-0.04613902,-0.03158583,-0.05100543,0.03038887,0.05098779,0.04598003,0.04781302,0.06483389,-0.0667533,0.03388411,-0.01724731,0.04915434,0.01568894,0.00495796,-0.01792806,0.03126947,-0.03193531,-0.00270242,0.1174624,0.02892074,0.0099373,-0.02172392,0.07189161,0.0225761,-0.01173296,-0.05332926,0.00738972,-0.05126799,0.02138598,0.052591,0.06414037,0.05774926,-0.04619382,-0.0423319,0.01370788,-0.00655737,0.01730845,0.03621813,0.01115933,-0.04882546,0.02485839,0.03001048,0.04795872,0.03337514,0.0067371,-0.00298558,0.0625662,0.03995204,0.03543033,0.02043427,0.00118002,-0.10166613,-0.04516567,-0.00802832,0.00388971,-0.02058201,-0.01049311,-0.0027674,0.00045586,-0.03139981,-0.07520735,0.05354563,-0.09121686,-0.02856024,0.07596105,-0.01477793,-0.00231878,-0.02774353,0.00382254,-0.00584068,-0.00264035,-0.03224805,-0.01739767,-0.05741687,0.02604884,0.07383478,0.09605297,-0.0690965,-0.01099119,-0.05641011,-0.02940791,-0.02040574,0.1515985,0.01203945,-0.09319527,-0.05637302,0.04892134,-0.04907991,-0.05777754,-0.01919231,0.02763613,-0.05551298,-0.01313949,0.10983627,0.01532132,-0.0340127,-0.01646292,-0.00993657,-0.03368718,0.04265191,-0.03328722,-0.01093269,0.00248274,-0.05219401,-0.12385944,0.01919018,-0.06505563,-0.00659573,-0.01866397,-0.03494254,-0.03874551,-0.02924959,-0.01409006,-0.0301108,-0.03222592,-0.04818597,-0.02332853,0.09385116,0.00720297,0.03112275,0.00728254,0.03689105,0.01151355,-0.04070848,0.05078355,0.03009456,-0.07350542,0.06144785,0.00811827,-0.00613896,0.00685916,0.08064334,0.01440281,-0.01726588,0.02613289,0.00330938,0.01231959,-0.00284811,0.01225975,0.00082378,0.08790407,-0.0709843,-0.21408738,-0.02080133,-0.06195185,0.00978284,0.01485138,-0.00160456,0.03592699,0.02032916,0.07260095,0.06910042,0.09172556,-0.08789147,-0.02740184,0.07643119,-0.00970513,-0.00613065,-0.06077625,-0.0867449,-0.00542925,0.00966478,0.00290199,0.02211277,-0.07089098,-0.08503007,0.06711631,-0.05833706,0.11994771,0.00858184,0.02811851,-0.00156245,0.06304005,0.01303923,0.0206877,-0.06130769,0.0173427,0.03693582,0.00907777,-0.01388491,-0.02701565,-0.00755445,-0.07934874,0.04052965,0.01235632,-0.097233,-0.05394099,0.01763095,0.00025814,0.0009073,-0.01084451,0.05617314,0.08250327,-0.02629404,0.02648098,0.02170768,0.02704183,-0.04566737,-0.05337229,-0.02337126,-0.0358256,0.03427928,-0.03448655,-0.00959371,0.02578485,-0.02728077,0.01313741,0.03850674,-0.00966528,-0.00892567,0.07523526,-0.00376258,0.02674021,0.07962255,-0.00420599,-0.00409356,0.02814989,0.0511929,0.0172422,-0.03602986,-0.02448433,0.03065787,-0.01744425,-0.03908011,0.04429379,0.05969558,0.06831076,0.00474439,0.06927107,0.02333143,0.00310537,0.00870741,0.01234382,0.05502179,-0.00903005,0.01563772,0.03751834,0.02052113,-0.27114868,0.00591738,-0.02114521,0.07112465,-0.01738617,-0.0457988,0.0065298,-0.03035139,-0.02347594,0.01124613,0.05940673,0.0183092,0.02400423,-0.05632854,-0.02635645,-0.00903423,-0.01456303,-0.02023701,0.07009842,-0.05026646,0.02832387,0.04056373,0.26420903,0.02786988,-0.01180941,0.0034007,0.00921468,0.02349057,-0.01318935,0.02688087,0.00412805,0.0323629,0.04629365,0.00335836,-0.0423633,-0.00896963,-0.0072161,0.03816629,-0.02988336,-0.02018817,-0.10056145,0.00452868,-0.03153104,0.08632409,0.11001665,-0.01332978,-0.02343807,-0.08416863,0.05350932,0.06957018,-0.08992539,-0.07879506,-0.0400127,0.00116731,0.01376028,0.01152704,-0.02058389,-0.01260954,-0.00412661,-0.00228296,0.00814995,0.00836453,0.06465379,-0.03519137,0.00977532],"last_embed":{"hash":"160hk0y","tokens":216}}},"text":null,"length":0,"last_read":{"hash":"160hk0y","at":1753423528694},"key":"notes/saliu/Lotto Software Positional Frequency, Lottery Strategy.md#Lotto Software Positional Frequency, Lottery Strategy##Update for SkipSystem - Finalized Version#{36}","lines":[188,193],"size":546,"outlinks":[{"title":"Download your lottery software to create lotto systems based on positional frequency.","target":"https://forums.saliu.com/HLINE.gif","line":1},{"title":"![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.","target":"https://forums.saliu.com/go-back.gif","line":3},{"title":"Socrates Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Exit the best site of software, systems, strategies, mathematics of lotto skips.","target":"https://forums.saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
