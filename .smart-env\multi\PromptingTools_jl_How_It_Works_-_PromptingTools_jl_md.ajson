"smart_sources:PromptingTools.jl/How It Works - PromptingTools.jl.md": {"path":"PromptingTools.jl/How It Works - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11709116,-0.00534584,-0.02629193,-0.04685716,-0.03226207,0.00626888,-0.02408016,0.03547489,0.03574141,-0.02506378,-0.016429,0.01710271,-0.00518995,0.05173835,-0.00254428,0.01360788,-0.05815456,0.03071067,-0.08088135,-0.01832861,0.07652769,0.05505109,0.00388895,-0.03322108,0.00585593,0.07792121,0.01190018,-0.03309671,-0.00563653,-0.1951886,0.05603778,-0.03993576,-0.00178239,-0.01232509,-0.03931313,-0.00587681,-0.04381111,0.0318689,-0.03654584,-0.01349603,0.04822983,0.02670604,-0.04199096,-0.02862997,-0.05244431,-0.08856789,-0.01530053,-0.02746856,0.02566718,-0.06813533,-0.00668607,-0.02788606,0.05941888,-0.04536677,0.01182046,0.00987025,-0.00158595,0.11079697,0.02639164,-0.00084658,0.04525974,0.0139437,-0.18812163,0.17143106,0.01615961,0.019844,0.00292133,-0.01264025,0.02925836,0.05358675,-0.02610282,0.02899731,-0.021864,0.05899108,0.01137064,-0.01790097,-0.02583002,-0.0155979,0.00371004,-0.09857177,-0.05887739,-0.04252283,-0.00805058,0.01422373,-0.02305086,0.01422635,-0.00525339,-0.01282121,0.07041195,-0.01185707,0.00570504,-0.0661325,-0.01189395,0.01712106,-0.0529204,-0.00965224,0.01173782,-0.0269624,-0.10455122,0.12552288,-0.00875608,0.00616169,0.02015116,-0.03450376,0.0117888,-0.08285148,-0.00484216,-0.03328617,-0.01487826,0.03751929,-0.00336607,-0.04196326,-0.02142905,-0.06041615,-0.03424403,-0.02548935,0.00383132,-0.01129776,-0.00603272,-0.05044536,0.01550577,0.01737001,-0.00803285,0.01845627,0.0009603,0.03507811,0.03266738,0.03309992,-0.00865288,0.02346839,0.06616481,0.00886996,-0.0746142,-0.02802504,-0.02257717,0.07518026,0.05211698,-0.05429086,0.03682588,-0.02536727,0.0433907,0.01541931,0.00558967,-0.09969974,0.02329811,0.04033813,-0.02985549,-0.00371516,0.00745322,-0.03082354,0.03770142,0.03863019,-0.06675451,-0.01482041,0.07060507,0.05356918,0.07856652,0.07710649,-0.00925678,0.03515977,-0.09478532,-0.03317913,-0.01278054,0.13234125,0.03720359,-0.03286565,-0.08575375,-0.01755505,-0.05195807,-0.05310738,0.01380712,0.04774873,-0.04792566,-0.00124174,0.05934992,0.03052041,-0.05061661,0.02575391,0.00325563,0.02691339,0.03378516,-0.05852996,-0.03508765,0.01572806,-0.03094857,-0.01029895,0.00071926,-0.07267199,0.00263866,0.03125716,-0.05212155,0.02650867,0.05050044,-0.02816497,-0.0026188,-0.01449087,-0.01106902,-0.09079936,0.05663614,-0.03146269,0.06866886,0.03982963,0.05772839,0.01627055,0.00474662,0.03904552,0.05559238,-0.01992351,0.10695966,0.019196,-0.09155563,-0.06534406,0.06765121,0.00040716,-0.02100139,-0.02077645,-0.060087,0.02961583,0.01359416,0.05813127,-0.04579813,-0.00416177,-0.02631709,-0.21877222,0.00783504,0.02315879,-0.04704752,0.0517338,-0.08987278,0.08617994,-0.04740095,-0.03262959,0.06946459,0.11308694,-0.03447624,0.04555982,-0.00570459,0.00610511,-0.00809348,-0.04276045,0.01472607,-0.00100956,0.03319022,0.02555819,-0.01767979,0.03852691,-0.07895207,0.01130264,-0.0192933,0.11166234,0.04132685,0.04940407,-0.07919131,0.02060625,-0.01437195,0.04586928,-0.11694952,0.0075259,0.03065276,-0.02912199,0.04254031,0.09601422,0.02311625,-0.05653553,0.03791571,-0.02453211,-0.04611664,-0.02635094,0.00079028,-0.04452477,-0.04229686,-0.05423063,0.01406947,0.03628244,-0.04302961,-0.00414641,0.00927307,0.00977913,-0.00893407,-0.01616795,-0.05821942,-0.00381068,-0.01082504,0.03552076,-0.01407681,-0.00906079,-0.03195427,0.06241259,0.04556623,0.00726783,0.01986021,0.06928015,0.00987061,-0.01402174,0.10981385,0.02054261,0.10005625,-0.01055934,0.08741903,-0.00718272,-0.05670123,0.0025267,-0.03939699,-0.04404045,0.05080581,0.01335693,0.01839926,0.0339384,0.05597955,0.02432899,-0.03926395,0.08619312,-0.07089757,-0.0252984,0.0174863,0.0303515,-0.02366568,0.02258728,0.03094677,-0.23075147,0.01417228,-0.01546385,0.02577717,-0.03993555,0.044609,0.04442551,0.00819993,-0.01523234,0.05861067,-0.03858379,-0.00645274,0.00299871,-0.03114994,0.06057148,0.04431579,0.02659689,0.010576,0.02420543,-0.08191302,0.0332776,0.02528204,0.22323942,-0.01514633,0.02436521,-0.01647831,0.01850611,-0.07718545,0.09994178,0.03619167,0.0116261,0.05396048,0.14674212,0.00319503,0.04192945,-0.01937342,-0.06565903,-0.00835497,0.03011709,0.00747131,-0.03090378,0.04697111,-0.02179807,-0.01806672,0.05551269,-0.0341766,-0.01580883,-0.07894175,-0.03552764,0.03308113,-0.00231926,-0.00125322,-0.01001345,-0.01432422,0.00268641,-0.01781511,0.04650282,0.02448973,-0.06598571,-0.0109274,0.05411157,-0.02238167,0.10037611,-0.00048945,-0.02335755],"last_embed":{"hash":"4ad3481791207962eaff5cfdb684dd7ca7e9a5c3c265fa3ee778ac169f051f7c","tokens":463}}},"last_read":{"hash":"4ad3481791207962eaff5cfdb684dd7ca7e9a5c3c265fa3ee778ac169f051f7c","at":1745995219611},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/how_it_works/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"Key Concepts","target":"#Key-Concepts \"Key Concepts\"","line":243},{"title":"API/Model Providers","target":"#API/Model-Providers \"API/Model Providers\"","line":244},{"title":"Schemas","target":"#Schemas \"Schemas\"","line":245},{"title":"Prompts","target":"#Prompts \"Prompts\"","line":246},{"title":"Messages","target":"#Messages \"Messages\"","line":247},{"title":"Prompt Templates","target":"#Prompt-Templates \"Prompt Templates\"","line":248},{"title":"ai* Functions Overview","target":"#ai*-Functions-Overview \"ai* Functions Overview\"","line":249},{"title":"Walkthrough Example for aigenerate","target":"#Walkthrough-Example-for-aigenerate \"Walkthrough Example for aigenerate\"","line":250},{"title":"Walkthrough Example for aiextract","target":"#Walkthrough-Example-for-aiextract \"Walkthrough Example for aiextract\"","line":251},{"title":"​","target":"#How-It-Works","line":253},{"title":"​","target":"#Key-Concepts","line":259},{"title":"​","target":"#API/Model-Providers","line":276},{"title":"​","target":"#Schemas","line":282},{"title":"​","target":"#Prompts","line":295},{"title":"​","target":"#Messages","line":303},{"title":"​","target":"#Prompt-Templates","line":320},{"title":"​","target":"#ai*-Functions-Overview","line":343},{"title":"​","target":"#Walkthrough-Example-for-aigenerate","line":412},{"title":"​","target":"#Walkthrough-Example-for-aiextract","line":502},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/how_it_works.md","line":701},{"title":"Previous pageGetting Started","target":"/PromptingTools.jl/dev/getting_started","line":703},{"title":"Next pageVarious examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":705},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":707},{"title":"Icons8","target":"https://icons8.com","line":707},{"title":"**VitePress**","target":"https://vitepress.dev","line":707}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,252],"##Reference#{1}":[209,242],"##Reference#{2}":[243,249],"##Reference#{3}":[250,250],"##Reference#{4}":[251,252],"#How It Works [​](#How-It-Works)":[253,709],"#How It Works [​](#How-It-Works)#{1}":[255,258],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)":[259,411],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#{1}":[261,262],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#{2}":[263,264],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#{3}":[265,266],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#{4}":[267,268],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#{5}":[269,270],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#{6}":[271,273],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#{7}":[274,275],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#API/Model Providers [​](#API/Model-Providers)":[276,281],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#API/Model Providers [​](#API/Model-Providers)#{1}":[278,281],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Schemas [​](#Schemas)":[282,294],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Schemas [​](#Schemas)#{1}":[284,289],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Schemas [​](#Schemas)#{2}":[290,291],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Schemas [​](#Schemas)#{3}":[292,294],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Prompts [​](#Prompts)":[295,302],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Prompts [​](#Prompts)#{1}":[297,302],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Messages [​](#Messages)":[303,319],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Messages [​](#Messages)#{1}":[305,308],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Messages [​](#Messages)#{2}":[309,310],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Messages [​](#Messages)#{3}":[311,312],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Messages [​](#Messages)#{4}":[313,314],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Messages [​](#Messages)#{5}":[315,316],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Messages [​](#Messages)#{6}":[317,319],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Prompt Templates [​](#Prompt-Templates)":[320,342],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#Prompt Templates [​](#Prompt-Templates)#{1}":[322,342],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)":[343,411],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{1}":[345,350],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{2}":[351,352],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{3}":[353,354],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{4}":[355,356],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{5}":[357,358],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{6}":[359,360],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{7}":[361,362],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{8}":[363,365],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{9}":[366,369],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{10}":[370,371],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{11}":[372,373],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{12}":[374,375],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{13}":[376,377],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{14}":[378,380],"#How It Works [​](#How-It-Works)#Key Concepts [​](#Key-Concepts)#ai* Functions Overview [​](#ai*-Functions-Overview)#{15}":[381,411],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aigenerate` [​](#Walkthrough-Example-for-aigenerate)":[412,501],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aigenerate` [​](#Walkthrough-Example-for-aigenerate)#{1}":[414,474],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aigenerate` [​](#Walkthrough-Example-for-aigenerate)#{2}":[475,476],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aigenerate` [​](#Walkthrough-Example-for-aigenerate)#{3}":[477,479],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aigenerate` [​](#Walkthrough-Example-for-aigenerate)#{4}":[480,501],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aiextract` [​](#Walkthrough-Example-for-aiextract)":[502,709],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aiextract` [​](#Walkthrough-Example-for-aiextract)#{1}":[504,670],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aiextract` [​](#Walkthrough-Example-for-aiextract)#{2}":[671,672],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aiextract` [​](#Walkthrough-Example-for-aiextract)#{3}":[673,675],"#How It Works [​](#How-It-Works)#Walkthrough Example for `aiextract` [​](#Walkthrough-Example-for-aiextract)#{4}":[676,709]},"last_import":{"mtime":1712726518894,"size":26764,"at":1740449882773,"hash":"4ad3481791207962eaff5cfdb684dd7ca7e9a5c3c265fa3ee778ac169f051f7c"},"key":"PromptingTools.jl/How It Works - PromptingTools.jl.md"},