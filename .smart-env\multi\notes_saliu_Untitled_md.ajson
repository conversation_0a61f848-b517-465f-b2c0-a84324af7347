
"smart_sources:notes/saliu/Untitled.md": {"path":"notes/saliu/Untitled.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"ltmzk0","at":1753230880448},"class_name":"SmartSource","last_import":{"mtime":1735815371000,"size":55422,"at":1753230880657,"hash":"ltmzk0"},"blocks":{"#":[1,9],"##Key Features of SkipSystem":[10,40],"##Key Features of SkipSystem#{1}":[12,13],"##Key Features of SkipSystem#{2}":[14,16],"##Key Features of SkipSystem#{3}":[17,18],"##Key Features of SkipSystem#{4}":[19,25],"##Key Features of SkipSystem#{5}":[26,27],"##Key Features of SkipSystem#{6}":[28,34],"##Key Features of SkipSystem#{7}":[35,36],"##Key Features of SkipSystem#{8}":[37,40],"##Practical Use":[41,210],"##Practical Use#{1}":[43,210],"#---frontmatter---":[56,106],"##1. 跳數分析":[211,214],"##1. 跳數分析#{1}":[213,214],"##2. 機率中位數":[215,218],"##2. 機率中位數#{1}":[217,218],"##3. 組合生成":[219,222],"##3. 組合生成#{1}":[221,222],"##4. 輪盤系統":[223,226],"##4. 輪盤系統#{1}":[225,226],"##5. 數據驅動決策":[227,230],"##5. 數據驅動決策#{1}":[229,230],"##6. 統計報告":[231,288],"##6. 統計報告#{1}":[233,234],"##6. 統計報告#{2}":[235,288],"##1. 跳數報告":[289,292],"##1. 跳數報告#{1}":[291,292],"##2. 中位數報告":[293,296],"##2. 中位數報告#{1}":[295,296],"##3. 頻率分析報告":[297,300],"##3. 頻率分析報告#{1}":[299,300],"##4. 組合生成報告":[301,304],"##4. 組合生成報告#{1}":[303,304],"##5. 整體趨勢報告":[305,308],"##5. 整體趨勢報告#{1}":[307,308],"##6. 總結報告":[309,346],"##6. 總結報告#{1}":[311,312],"##6. 總結報告#{2}":[313,346],"##1. 跳數計算":[347,350],"##1. 跳數計算#{1}":[349,350],"##2. 機率參數":[351,354],"##2. 機率參數#{1}":[353,354],"##3. 自動化組合生成":[355,358],"##3. 自動化組合生成#{1}":[357,358],"##4. 多種遊戲支持":[359,362],"##4. 多種遊戲支持#{1}":[361,362],"##5. 統計報告":[363,366],"##5. 統計報告#{1}":[365,366],"##6. 組合過濾器":[367,370],"##6. 組合過濾器#{1}":[369,370],"##7. 用戶自定義系統":[371,405],"##7. 用戶自定義系統#{1}":[373,374],"##7. 用戶自定義系統#{2}":[375,405],"##輸入文件要求":[406,431],"##輸入文件要求#{1}":[408,412],"##輸入文件要求#{2}":[413,417],"##輸入文件要求#{3}":[418,422],"##輸入文件要求#{4}":[423,426],"##輸入文件要求#{5}":[427,431],"##示例格式":[432,509],"##示例格式#{1}":[434,509],"##Input File Requirements":[510,519],"##Input File Requirements#{1}":[512,512],"##Input File Requirements#{2}":[513,514],"##Input File Requirements#{3}":[515,516],"##Input File Requirements#{4}":[517,517],"##Input File Requirements#{5}":[518,519],"##Example of an Input File":[520,646],"##Example of an Input File#{1}":[522,646],"##Key Components of SkipSystem Strategy":[647,654],"##Key Components of SkipSystem Strategy#{1}":[649,649],"##Key Components of SkipSystem Strategy#{2}":[650,650],"##Key Components of SkipSystem Strategy#{3}":[651,651],"##Key Components of SkipSystem Strategy#{4}":[652,652],"##Key Components of SkipSystem Strategy#{5}":[653,654],"##Practical Application":[655,758],"##Practical Application#{1}":[657,758],"##Skip Analysis":[759,762],"##Skip Analysis#{1}":[761,762],"##Frequency and Median Skips":[763,766],"##Frequency and Median Skips#{1}":[765,766],"##Combination Generation":[767,770],"##Combination Generation#{1}":[769,770],"##Wheeling Systems":[771,774],"##Wheeling Systems#{1}":[773,774],"##Practical Application[2]":[775,896],"##Practical Application[2]#{1}":[777,778],"##Practical Application[2]#{2}":[779,896],"##跳數分析":[897,900],"##跳數分析#{1}":[899,900],"##數據處理":[901,904],"##數據處理#{1}":[903,904],"##統計報告":[905,908],"##統計報告#{1}":[907,908],"##組合生成":[909,912],"##組合生成#{1}":[911,912],"##篩選條件":[913,916],"##篩選條件#{1}":[915,916],"##實際應用":[917,920],"##實際應用#{1}":[919,920],"##總結":[921,1040],"##總結#{1}":[923,1040],"##靜態過濾器與動態過濾器":[1041,1049],"##靜態過濾器與動態過濾器#{1}":[1043,1044],"##靜態過濾器與動態過濾器#{2}":[1045,1047],"##靜態過濾器與動態過濾器#{3}":[1048,1049],"##過濾策略":[1050,1057],"##過濾策略#{1}":[1052,1053],"##過濾策略#{2}":[1054,1057],"##組合生成[2]":[1058,1061],"##組合生成[2]#{1}":[1060,1061],"##實際應用[2]":[1062,1139],"##實際應用[2]#{1}":[1064,1065],"##實際應用[2]#{2}":[1066,1139],"##1. 跳數分析[2]":[1140,1143],"##1. 跳數分析[2]#{1}":[1142,1143],"##2. 機率中位數[2]":[1144,1147],"##2. 機率中位數[2]#{1}":[1146,1147],"##3. 過濾器使用":[1148,1151],"##3. 過濾器使用#{1}":[1150,1151],"##4. 組合生成":[1152,1155],"##4. 組合生成#{1}":[1154,1155],"##5. 數據驅動決策[2]":[1156,1159],"##5. 數據驅動決策[2]#{1}":[1158,1159],"##6. Ion Saliu悖論":[1160,1280],"##6. Ion Saliu悖論#{1}":[1162,1163],"##6. Ion Saliu悖論#{2}":[1164,1280],"##1. Mathematical Foundation":[1281,1284],"##1. Mathematical Foundation#{1}":[1283,1284],"##2. Application to Lottery Strategies":[1285,1288],"##2. Application to Lottery Strategies#{1}":[1287,1288],"##3. Enhancing Prediction Accuracy":[1289,1292],"##3. Enhancing Prediction Accuracy#{1}":[1291,1292],"##4. Combination Generation":[1293,1296],"##4. Combination Generation#{1}":[1295,1296],"##5. Statistical Reporting":[1297,1300],"##5. Statistical Reporting#{1}":[1299,1300],"##Conclusion":[1301,1332],"##Conclusion#{1}":[1303,1332],"##1. 數學基礎":[1333,1336],"##1. 數學基礎#{1}":[1335,1336],"##2. 應用於樂透策略":[1337,1340],"##2. 應用於樂透策略#{1}":[1339,1340],"##3. 提升預測準確性":[1341,1344],"##3. 提升預測準確性#{1}":[1343,1344],"##4. 組合生成[2]":[1345,1348],"##4. 組合生成[2]#{1}":[1347,1348],"##5. 統計報告[2]":[1349,1352],"##5. 統計報告[2]#{1}":[1351,1352],"##總結[2]":[1353,1382],"##總結[2]#{1}":[1355,1382],"##1. 理解基本概念":[1383,1386],"##1. 理解基本概念#{1}":[1385,1386],"##2. 收集歷史數據":[1387,1390],"##2. 收集歷史數據#{1}":[1389,1390],"##3. 計算機率":[1391,1398],"##3. 計算機率#{1}":[1393,1394],"##3. 計算機率#{2}":[1395,1398],"##4. 應用跳數分析":[1399,1402],"##4. 應用跳數分析#{1}":[1401,1402],"##5. 組合生成":[1403,1406],"##5. 組合生成#{1}":[1405,1406],"##6. 持續評估與調整":[1407,1410],"##6. 持續評估與調整#{1}":[1409,1410],"##7. 心理因素考量":[1411,1414],"##7. 心理因素考量#{1}":[1413,1414],"##總結[3]":[1415,1444],"##總結[3]#{1}":[1417,1444],"##1. 選擇彩票遊戲":[1445,1453],"##1. 選擇彩票遊戲#{1}":[1447,1453],"##2. 收集歷史數據[2]":[1454,1457],"##2. 收集歷史數據[2]#{1}":[1456,1457],"##3. 應用FFG進行計算":[1458,1465],"##3. 應用FFG進行計算#{1}":[1460,1461],"##3. 應用FFG進行計算#{2}":[1462,1465],"##4. 制定投注策略":[1466,1469],"##4. 制定投注策略#{1}":[1468,1469],"##5. 實際投注與記錄":[1470,1473],"##5. 實際投注與記錄#{1}":[1472,1473],"##6. 效果評估":[1474,1478],"##6. 效果評估#{1}":[1476,1476],"##6. 效果評估#{2}":[1477,1478],"##7. 調整與優化":[1479,1482],"##7. 調整與優化#{1}":[1481,1482],"##8. 心理因素考量":[1483,1486],"##8. 心理因素考量#{1}":[1485,1486],"##總結[4]":[1487,1584],"##總結[4]#{1}":[1489,1584],"##1. 定義與計算":[1585,1588],"##1. 定義與計算#{1}":[1587,1588],"##2. 跳數與重複出現":[1589,1592],"##2. 跳數與重複出現#{1}":[1591,1592],"##3. 策略制定":[1593,1596],"##3. 策略制定#{1}":[1595,1596],"##4. 數據分析":[1597,1600],"##4. 數據分析#{1}":[1599,1600],"##5. 實際應用":[1601,1631],"##5. 實際應用#{1}":[1603,1604],"##5. 實際應用#{2}":[1605,1631],"##1. 跳數（Skips）":[1632,1635],"##1. 跳數（Skips）#{1}":[1634,1635],"##2. 頻率（Frequency）":[1636,1639],"##2. 頻率（Frequency）#{1}":[1638,1639],"##3. 總事件數（Total Events）":[1640,1643],"##3. 總事件數（Total Events）#{1}":[1642,1643],"##4. 確定度（Degree of Certainty, DC）":[1644,1647],"##4. 確定度（Degree of Certainty, DC）#{1}":[1646,1647],"##5. 和數（Sum）":[1648,1651],"##5. 和數（Sum）#{1}":[1650,1651],"##6. 奇偶比例（Odd-Even Ratio）":[1652,1655],"##6. 奇偶比例（Odd-Even Ratio）#{1}":[1654,1655],"##7. 高低比例（High-Low Ratio）":[1656,1659],"##7. 高低比例（High-Low Ratio）#{1}":[1658,1659],"##8. 組合生成（Combination Generation）":[1660,1691],"##8. 組合生成（Combination Generation）#{1}":[1662,1663],"##8. 組合生成（Combination Generation）#{2}":[1664,1691],"##1. 收集數據":[1692,1695],"##1. 收集數據#{1}":[1694,1695],"##2. 定義勝利條件":[1696,1699],"##2. 定義勝利條件#{1}":[1698,1699],"##3. 計算成功事件數":[1700,1703],"##3. 計算成功事件數#{1}":[1702,1703],"##4. 計算總事件數":[1704,1707],"##4. 計算總事件數#{1}":[1706,1707],"##5. 計算勝率":[1708,1711],"##5. 計算勝率#{1}":[1710,1711],"##6. 使用概率中位數":[1712,1715],"##6. 使用概率中位數#{1}":[1714,1715],"##7. 持續評估與調整":[1716,1719],"##7. 持續評估與調整#{1}":[1718,1719],"##總結[5]":[1720,1749],"##總結[5]#{1}":[1722,1749],"##1. 跳數分析[3]":[1750,1753],"##1. 跳數分析[3]#{1}":[1752,1753],"##2. 機率中位數應用":[1754,1757],"##2. 機率中位數應用#{1}":[1756,1757],"##3. 靜態與動態過濾器":[1758,1761],"##3. 靜態與動態過濾器#{1}":[1760,1761],"##4. 組合生成[3]":[1762,1765],"##4. 組合生成[3]#{1}":[1764,1765],"##5. 數據驅動決策[3]":[1766,1769],"##5. 數據驅動決策[3]#{1}":[1768,1769],"##6. 心理因素管理":[1770,1773],"##6. 心理因素管理#{1}":[1772,1773],"##7. 實驗與調整":[1774,1777],"##7. 實驗與調整#{1}":[1776,1777],"##8. 設定預算":[1778,1799],"##8. 設定預算#{1}":[1780,1781],"##8. 設定預算#{2}":[1782,1799]},"outlinks":[{"title":"Favorite Lottery Numbers in Fixed Positions: Software - Ion Saliu","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":1},{"title":"Gail Howard: Lottery Software, Lotto Wheels, Skips Systems - Ion Saliu","target":"https://saliu.com/bbs/messages/278.html","line":2},{"title":"Software Skips Pick Lottery, Lotto, Roulette, Googoos - Ion Saliu","target":"https://saliu.com/bbs/messages/693.html","line":3},{"title":"Powerball, Mega Millions, Strategy, Skip Systems, Software - Ion Saliu","target":"https://saliu.com/powerball-systems.html","line":4},{"title":"\n    \n    1\n    \n    ","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":12},{"title":"\n    \n    2\n    \n    ","target":"https://saliu.com/bbs/messages/278.html","line":17},{"title":"\n    \n    3\n    \n    ","target":"https://saliu.com/bbs/messages/693.html","line":21},{"title":"\n    \n    1\n    \n    ","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":26},{"title":"\n    \n    3\n    \n    ","target":"https://saliu.com/bbs/messages/693.html","line":30},{"title":"\n    \n    2\n    \n    ","target":"https://saliu.com/bbs/messages/278.html","line":35},{"title":"\n\n1\n\n","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":43},{"title":"\n\n4\n\n","target":"https://saliu.com/powerball-systems.html","line":47},{"title":"\n\nLottery Wheel: How Does This Clever Strategy Work? - Lotterycodex.com\n\n![lotterycodex.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=lotterycodex.com","line":119},{"title":"\n\nAll Wheeling Systems - Lotto Expert\n\n![lottoexpert.net favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=lottoexpert.net","line":141},{"title":"\n\nHobby Lotto: Free Lottery Wheels, Systems, Strategies & Tips\n\n![hobbylotto.net favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=hobbylotto.net","line":163},{"title":"\n\nDesigning a Custom \"Abbreviated Wheel\" System for the Lottery\n\n![math.stackexchange.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=math.stackexchange.com","line":185},{"title":"jlis.glis.ntnu.edu.tw favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=jlis.glis.ntnu.edu.tw","line":267},{"title":"\n\nThe adop Utility - Oracle E-Business Suite Maintenance Guide\n\n![docs.oracle.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=docs.oracle.com","line":466},{"title":"\n\nSkip Logic - Tangerine Documentation\n\n","target":"https://docs.tangerinecentral.org/editor/getting-started-editor/skip-logic/","line":488},{"title":"\n\nhow to do I know when to skip a column in the \"input\" statement?\n\n","target":"https://communities.sas.com/t5/SAS-Programming/how-to-do-I-know-when-to-skip-a-column-in-the-quot-input-quot/td-p/233812","line":494},{"title":"\n\nInvoke-AzStorageSyncCompatibilityCheck (Az.StorageSync ...\n\n","target":"https://learn.microsoft.com/pl-pl/powershell/module/az.storagesync/invoke-azstoragesynccompatibilitycheck?view=azps-12.4.0","line":500},{"title":"\n\nLottery Strategies: 7 Systems for Predicting Winning Numbers\n\n![ftnnews.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=ftnnews.com","line":555},{"title":"\n\nSearch results for \"Lottery Skips, Lotto Systems, Strategy\" - siteLevel\n\n![sitelevel.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=sitelevel.com","line":577},{"title":"\n\nNumeral Skip System - Lottery Post\n\n![lotterypost.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=lotterypost.com","line":599},{"title":"\n\nMastering Smart Lotto Strategies: A Comprehensive Guide to Winning\n\n![gitlab.kci favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=gitlab.kci","line":621},{"title":"\n\nIon Saliu - CNET Download\n\n![download.cnet.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=download.cnet.com","line":667},{"title":"\n\nSearch results for \"Lottery Skips, Lotto Systems, Strategy\" - siteLevel\n\n![sitelevel.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=sitelevel.com","line":689},{"title":"\n\nFilters, Restrictions, Reduction in Lotto, Lottery Software\n\n![rec.gambling.lottery.narkive.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=rec.gambling.lottery.narkive.com","line":711},{"title":"geocities.ws favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=geocities.ws","line":737},{"title":"\n\nFree Winning Lotto and Lottery Strategies - pdfcoffee.com\n\n![pdfcoffee.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=pdfcoffee.com","line":805},{"title":"\n\nreplace function | Notepad++ Community\n\n![community.notepad favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=community.notepad","line":827},{"title":"\n\nSaliu's System - Blackjack and Card Counting Forums\n\n![blackjackinfo.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=blackjackinfo.com","line":849},{"title":"\n\nWheeling Mathematics by Ion Saliu - Google Groups\n\n![groups.google.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=groups.google.com","line":871},{"title":"\n\nGood Use of STATIC FILTERS in Lottery - Google Groups\n\n![groups.google.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=groups.google.com","line":949},{"title":"\n\nMedian Skips - Lottery Post\n\n![lotterypost.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=lotterypost.com","line":971},{"title":"\n\nFilters, Restrictions, Reduction in Lotto, Lottery Software\n\n![rec.gambling.lottery.narkive.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=rec.gambling.lottery.narkive.com","line":993},{"title":"\n\nIon Saliu new Bright 5 | Lottery Post\n\n![lotterypost.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=lotterypost.com","line":1015},{"title":"\n    \n    1\n    \n    ","target":"https://groups.google.com/g/rec.gambling.lottery/c/cYgHmh6kgfE","line":1043},{"title":"\n    \n    1\n    \n    ","target":"https://groups.google.com/g/rec.gambling.lottery/c/cYgHmh6kgfE","line":1052},{"title":"\n\nFree Winning Lotto and Lottery Strategies - pdfcoffee.com\n\n![pdfcoffee.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=pdfcoffee.com","line":1092},{"title":"\n\nProperties and Applications of Intelligent Packaging Indicators for ...\n\n![pmc.ncbi.nlm.nih.gov favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=pmc.ncbi.nlm.nih.gov","line":1114},{"title":"\n\nPositional Lotto Wheels from Groups of Numbers\n\n![groups.google.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=groups.google.com","line":1189},{"title":"\n\nION SALIU, FOUNDER OF LOTTERY MATHEMATICS, GAMBLING ...\n\n![groups.google.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=groups.google.com","line":1211},{"title":"\n\n\"ion\" | G NETWORK\n\n![gnetwork.com.au favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=gnetwork.com.au","line":1233},{"title":"reddit.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=reddit.com","line":1259},{"title":"saliu.com","target":"https://saliu.com/ScreenImgs/gambling-formula.gif","line":1309},{"title":"saliu.com","target":"https://saliu.com/ScreenImgs/mathematics-formula.gif","line":1311},{"title":"saliu.com","target":"https://saliu.com/ScreenImgs/standard-deviation-formula.gif","line":1313},{"title":"scribd.com","target":"https://d2u1z1lopyfwlx.cloudfront.net/thumbnails/8de3e25d-da4c-5e1a-9ac6-f0f7a8e492df/b5ab8d10-9bee-5b92-ad3e-cc24fdb954ea.jpg","line":1315},{"title":"saliu.com","target":"https://saliu.com/images/blackjack-winning-consistency.gif","line":1317},{"title":"forums.saliu.com","target":"https://saliu.com/ScreenImgs/reverse-gambler-fallacy.gif","line":1319},{"title":"saliu.com","target":"https://saliu.com/ScreenImgs/shuffle-1.gif","line":1321},{"title":"\n\nFree Winning Lotto and Lottery Strategies - pdfcoffee.com\n\n![pdfcoffee.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=pdfcoffee.com","line":1515},{"title":"\n\nION SALIU, FOUNDER OF LOTTERY MATHEMATICS, GAMBLING ...\n\n![groups.google.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=groups.google.com","line":1537},{"title":"\n\nIon Saliu | Lottery Post\n\n![lotterypost.com favicon","target":"https://www.google.com/s2/favicons?sz=128&domain=lotterypost.com","line":1559}]},