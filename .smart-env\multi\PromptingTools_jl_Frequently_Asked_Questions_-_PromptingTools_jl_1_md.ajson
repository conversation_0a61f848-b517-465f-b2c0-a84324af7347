"smart_sources:PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl 1.md": {"path":"PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl 1.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11018503,-0.00307162,-0.01831458,-0.05371992,-0.02209154,0.02797785,-0.01802394,0.03743451,0.02887708,-0.03639369,-0.02604969,0.00899736,0.00126919,0.04512223,0.01839796,0.02459423,-0.02952484,-0.0022017,-0.09671482,-0.01067695,0.08430327,0.03724925,0.01599948,-0.04343769,-0.00010573,0.03442036,0.0142868,-0.03909472,-0.00329607,-0.19171779,0.06096853,-0.02054179,0.00839915,-0.01855065,-0.03384982,0.00419154,-0.01528242,0.0638252,-0.02184867,-0.00739383,0.03005159,0.01073913,-0.03805697,-0.02764316,-0.05014906,-0.09102307,-0.00864057,-0.030718,0.01620819,-0.0501775,-0.0191761,-0.04491374,0.05488935,-0.03942065,0.01506063,-0.00116324,0.00704431,0.08257415,0.03447345,0.01852217,0.02929684,0.00010583,-0.1922981,0.1802668,-0.00045214,0.02993266,0.00386242,0.00626494,0.02043506,0.04747292,-0.03952088,0.02349221,0.00659875,0.06107197,0.01295745,-0.02312133,-0.03158451,-0.00460044,0.002083,-0.10644358,-0.05306401,-0.01540046,-0.02782954,-0.00960039,-0.04979283,-0.02375679,0.00418904,0.01280174,0.05494969,0.00634165,0.00345024,-0.063131,0.01403585,0.02546856,-0.05486956,-0.03902128,0.01628633,-0.0298492,-0.10958459,0.14451076,-0.0099433,0.00292685,0.03253679,-0.02197686,0.01789315,-0.04756213,0.01608671,-0.04590268,-0.02203826,0.0342105,0.01291652,-0.02969685,-0.0110032,-0.07478764,-0.02935981,-0.0125074,0.01789544,-0.0171315,-0.00652916,-0.03932801,0.01823128,0.00903103,0.02684221,0.00597475,0.00345592,0.01979505,0.03893355,0.02664194,-0.00200388,0.02187977,0.05930953,0.00397252,-0.06824736,-0.02908389,-0.01880659,0.05393918,0.06688024,-0.05949958,0.04758002,-0.00217871,0.01270544,-0.02181852,0.02933191,-0.10990912,0.0200279,0.0450953,-0.04316359,-0.0152222,-0.0017037,-0.05451598,0.03259352,0.03762023,-0.05288943,-0.02350784,0.0924561,0.05756713,0.0769018,0.06239577,0.00408621,0.00101709,-0.05038065,-0.03931816,0.00500804,0.16518544,0.05335386,-0.0632074,-0.10768148,-0.03865854,-0.04989666,-0.03375918,0.01531456,0.05747715,-0.05771652,0.00722613,0.07644878,0.03214879,-0.06744998,0.04258598,-0.00165438,0.02648316,0.02675761,-0.03888159,-0.02693179,0.00632216,-0.02899151,-0.03513826,-0.00579622,-0.0738169,-0.01468358,0.03177034,-0.06311169,-0.01555238,0.04292987,-0.02007805,-0.00852326,-0.00106975,0.00929919,-0.07722444,0.05413699,-0.04756789,0.04722128,0.02759197,0.04765476,0.04587714,-0.0136063,0.05158385,0.07343475,0.00174994,0.12822098,0.03543171,-0.08863738,-0.05895557,0.0456244,-0.0087753,-0.00750156,-0.04651365,-0.05826109,0.0084057,0.0146231,0.05888167,-0.02037648,-0.00005346,-0.02322035,-0.21449339,0.0151899,0.02893489,-0.03469267,0.06426132,-0.10083875,0.09195025,-0.05709554,-0.01240307,0.07577142,0.08955979,-0.03596843,0.03561314,-0.01838342,0.0065477,-0.00239137,-0.026844,-0.00056309,0.00337362,0.02800662,0.01983356,-0.00956548,0.03917108,-0.08615902,0.00633642,-0.03948646,0.10387407,0.04676713,0.0250754,-0.10678829,0.03001908,-0.01598781,0.05095353,-0.11757798,0.00887539,0.0044796,-0.05367533,0.04295483,0.07924069,0.02671853,-0.03615268,0.03804138,-0.03910374,-0.02614313,-0.03275821,-0.01322847,-0.04148035,-0.04779397,-0.04076366,0.02228657,0.00907904,-0.05814651,0.01028305,-0.00096492,0.02047856,-0.03332126,-0.05009618,-0.0699114,0.01842179,0.0037146,0.02320705,-0.00155036,-0.02226368,-0.03213104,0.05067042,0.03783321,-0.00145876,0.0071966,0.06067355,-0.00531986,-0.02786769,0.1157905,0.01588436,0.08779685,-0.02046991,0.06875701,0.0136102,-0.05124665,0.00407559,-0.04913981,-0.03256393,0.02491963,0.00637704,0.01399019,0.04343296,0.06630389,0.02713296,-0.02915377,0.08473988,-0.04200431,-0.01467408,0.01293818,0.02222013,-0.05967499,0.01304195,-0.00898328,-0.23233575,0.02639424,-0.00291981,0.0325141,-0.01006448,0.08285575,0.038791,0.02004372,-0.0126453,0.06879964,-0.02977361,0.03005238,0.0135701,-0.0419677,0.05632928,0.04797392,0.0383694,0.01268785,0.03850349,-0.09269819,0.03800371,0.01395517,0.21002701,-0.00303424,0.02816047,-0.00877397,0.00649966,-0.04183393,0.06768617,0.03735214,0.03310926,0.04467715,0.14535213,-0.00437962,0.04968055,0.00278904,-0.0402943,-0.01789441,0.01413173,0.0047666,-0.02658603,0.02711431,-0.05623448,-0.00800732,0.07179972,-0.01272383,-0.00507021,-0.08118483,-0.03567081,0.03371131,-0.0147221,-0.02094983,-0.02589342,-0.00359234,-0.00376696,0.00241384,0.06972481,0.01529026,-0.06922667,0.00946417,0.05190402,-0.02617986,0.10837365,-0.00751545,-0.00232702],"last_embed":{"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b","tokens":462}}},"last_read":{"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b","at":1745995218293},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/frequently_asked_questions/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"Why OpenAI","target":"#Why-OpenAI \"Why OpenAI\"","line":243},{"title":"What if I cannot access OpenAI?","target":"#What-if-I-cannot-access-OpenAI? \"What if I cannot access OpenAI?\"","line":244},{"title":"Data Privacy and OpenAI","target":"#Data-Privacy-and-OpenAI \"Data Privacy and OpenAI\"","line":245},{"title":"Creating OpenAI API Key","target":"#Creating-OpenAI-API-Key \"Creating OpenAI API Key\"","line":246},{"title":"Getting an error \"ArgumentError: api_key cannot be empty\" despite having set OPENAI_API_KEY? {#Getting-an-error-\"ArgumentError:-apikey-cannot-be-empty\"-despite-having-set-OPENAIAPI_KEY?}","target":"#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key \"Getting an error \"ArgumentError: api_key cannot be empty\" despite having set OPENAI_API_KEY? {#Getting-an-error-\"ArgumentError:-apikey-cannot-be-empty\"-despite-having-set-OPENAIAPI_KEY?}\"","line":247},{"title":"Getting an error \"Rate limit exceeded\" from OpenAI?","target":"#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI? \"Getting an error \"Rate limit exceeded\" from OpenAI?\"","line":248},{"title":"Setting OpenAI Spending Limits","target":"#Setting-OpenAI-Spending-Limits \"Setting OpenAI Spending Limits\"","line":249},{"title":"How much does it cost? Is it worth paying for?","target":"#How-much-does-it-cost?-Is-it-worth-paying-for? \"How much does it cost? Is it worth paying for?\"","line":250},{"title":"Configuring the Environment Variable for API Key","target":"#Configuring-the-Environment-Variable-for-API-Key \"Configuring the Environment Variable for API Key\"","line":251},{"title":"Setting the API Key via Preferences.jl","target":"#Setting-the-API-Key-via-Preferences.jl \"Setting the API Key via Preferences.jl\"","line":252},{"title":"Understanding the API Keyword Arguments in aigenerate (api_kwargs)","target":"#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs","line":253},{"title":"Instant Access from Anywhere","target":"#Instant-Access-from-Anywhere \"Instant Access from Anywhere\"","line":254},{"title":"Open Source Alternatives","target":"#Open-Source-Alternatives \"Open Source Alternatives\"","line":255},{"title":"Setup Guide for Ollama","target":"#Setup-Guide-for-Ollama \"Setup Guide for Ollama\"","line":256},{"title":"Changing the Default Model or Schema","target":"#Changing-the-Default-Model-or-Schema \"Changing the Default Model or Schema\"","line":257},{"title":"How to have Multi-turn Conversations?","target":"#How-to-have-Multi-turn-Conversations? \"How to have Multi-turn Conversations?\"","line":258},{"title":"How to have typed responses?","target":"#How-to-have-typed-responses? \"How to have typed responses?\"","line":259},{"title":"How to quickly create a prompt template?","target":"#How-to-quickly-create-a-prompt-template? \"How to quickly create a prompt template?\"","line":260},{"title":"Do we have a RecursiveCharacterTextSplitter like Langchain?","target":"#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain? \"Do we have a RecursiveCharacterTextSplitter like Langchain?\"","line":261},{"title":"How would I fine-tune a model?","target":"#How-would-I-fine-tune-a-model? \"How would I fine-tune a model?\"","line":262},{"title":"​","target":"#Frequently-Asked-Questions","line":264},{"title":"​","target":"#Why-OpenAI","line":266},{"title":"Setup Guide for Ollama","target":"/PromptingTools.jl/dev/frequently_asked_questions#setup-guide-for-ollama","line":272},{"title":"Ollama.ai","target":"https://ollama.ai/","line":272},{"title":"​","target":"#What-if-I-cannot-access-OpenAI?","line":274},{"title":"​","target":"#Data-Privacy-and-OpenAI","line":283},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance","line":289},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data","line":291},{"title":"OpenAI's How we use your data","target":"https://platform.openai.com/docs/models/how-we-use-your-data","line":295},{"title":"Data usage for consumer services FAQ","target":"https://help.openai.com/en/articles/7039943-data-usage-for-consumer-services-faq","line":297},{"title":"How your data is used to improve our models","target":"https://help.openai.com/en/articles/5722486-how-your-data-is-used-to-improve-model-performance","line":299},{"title":"​","target":"#Creating-OpenAI-API-Key","line":302},{"title":"OpenAI","target":"https://platform.openai.com/signup","line":306},{"title":"API Key page","target":"https://platform.openai.com/account/api-keys","line":308},{"title":"OpenAI Documentation","target":"https://platform.openai.com/docs/quickstart?context=python","line":317},{"title":"Visual tutorial","target":"https://www.maisieai.com/help/how-to-get-an-openai-api-key-for-chatgpt","line":319},{"title":"​","target":"#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key","line":324},{"title":"​","target":"#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?","line":343},{"title":"OpenAI Rate Limits","target":"https://platform.openai.com/docs/guides/rate-limits/usage-tiers?context=tier-free","line":347},{"title":"​","target":"#Setting-OpenAI-Spending-Limits","line":367},{"title":"OpenAI Billing","target":"https://platform.openai.com/account/billing","line":371},{"title":"OpenAI Forum","target":"https://community.openai.com/t/how-to-set-a-price-limit/13086","line":380},{"title":"​","target":"#How-much-does-it-cost?-Is-it-worth-paying-for?","line":382},{"title":"OpenAI Pricing per 1000 tokens","target":"https://openai.com/pricing","line":396},{"title":"​","target":"#Configuring-the-Environment-Variable-for-API-Key","line":398},{"title":"OpenAI Guide","target":"https://platform.openai.com/docs/quickstart?context=python","line":428},{"title":"​","target":"#Setting-the-API-Key-via-Preferences.jl","line":430},{"title":"​","target":"#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs","line":440},{"title":"OpenAI API reference","target":"https://platform.openai.com/docs/guides/text-generation/chat-completions-api","line":442},{"title":"​","target":"#Instant-Access-from-Anywhere","line":444},{"title":"​","target":"#Open-Source-Alternatives","line":457},{"title":"Ollama.ai","target":"https://ollama.ai/","line":459},{"title":"​","target":"#Setup-Guide-for-Ollama","line":461},{"title":"here","target":"https://ollama.ai/download","line":465},{"title":"Ollama Library","target":"https://ollama.ai/library","line":471},{"title":"Ollama.ai","target":"https://ollama.ai/","line":477},{"title":"​","target":"#Changing-the-Default-Model-or-Schema","line":479},{"title":"Working with Ollama","target":"/PromptingTools.jl/dev/frequently_asked_questions#working-with-ollama","line":487},{"title":"​","target":"#How-to-have-Multi-turn-Conversations?","line":492},{"title":" Info: Tokens: 50 @ Cost: $0.0 in 1.0 seconds\n## 5-element Vector{PromptingTools.AbstractMessage}:\n##  PromptingTools.SystemMessage(\"Act as a helpful AI assistant\")\n##  PromptingTools.UserMessage(\"Hi! I'm John\")\n##  AIMessage(\"Hello John! How can I assist you today?\")\n##  PromptingTools.UserMessage(\"What's my name?\")\n##  AIMessage(\"Your name is John.\")\n```\n\nNotice that the last message is the response to the second request, but with `return_all=true` we can see the whole conversation from the beginning.\n\n## How to have typed responses? [​","target":"#How-to-have-typed-responses?","line":524},{"title":" Info: Condition not met. Retrying...\n## [ Info: Condition not met. Retrying...\n## SmallInt(2)\n```\n\nWe ultimately received our custom type `SmallInt` with the number of car seats in the Porsche 911T (I hope it's correct!).\n\nIf you want to access the full conversation history (all the attempts and feedback), simply output the `response` object and explore `response.conversation`.\n\n## How to quickly create a prompt template? [​","target":"#How-to-quickly-create-a-prompt-template?","line":628},{"title":"​","target":"#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?","line":719},{"title":"`RecursiveCharacterTextSplitter`","target":"https://python.langchain.com/docs/modules/data_connection/document_transformers/recursive_text_splitter","line":723},{"title":"​","target":"#How-would-I-fine-tune-a-model?","line":739},{"title":"Axolotl","target":"https://github.com/OpenAccess-AI-Collective/axolotl","line":748},{"title":"JuliaLLMLeaderboard Finetuning experiment","target":"https://github.com/svilupp/Julia-LLM-Leaderboard/blob/main/experiments/cheater-7b-finetune/README.md","line":748},{"title":"JarvisLabs.ai","target":"https://jarvislabs.ai/templates/axolotl","line":748},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/frequently_asked_questions.md","line":750},{"title":"Previous pageAPITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":752},{"title":"Next pageGeneral","target":"/PromptingTools.jl/dev/prompts/general","line":754},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":756},{"title":"Icons8","target":"https://icons8.com","line":756},{"title":"**VitePress**","target":"https://vitepress.dev","line":756}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,263],"##Reference#{1}":[209,242],"##Reference#{2}":[243,244],"##Reference#{3}":[245,245],"##Reference#{4}":[246,246],"##Reference#{5}":[247,247],"##Reference#{6}":[248,248],"##Reference#{7}":[249,249],"##Reference#{8}":[250,250],"##Reference#{9}":[251,251],"##Reference#{10}":[252,252],"##Reference#{11}":[253,253],"##Reference#{12}":[254,254],"##Reference#{13}":[255,255],"##Reference#{14}":[256,256],"##Reference#{15}":[257,257],"##Reference#{16}":[258,258],"##Reference#{17}":[259,259],"##Reference#{18}":[260,260],"##Reference#{19}":[261,261],"##Reference#{20}":[262,263],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)":[264,758],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)":[266,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#{1}":[268,273],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)":[274,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{1}":[276,277],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{2}":[278,279],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Why OpenAI [​](#Why-OpenAI)#What if I cannot access OpenAI? [​](#What-if-I-cannot-access-OpenAI?)#{3}":[280,282],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)":[283,301],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{1}":[285,294],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{2}":[295,296],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{3}":[297,298],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Data Privacy and OpenAI [​](#Data-Privacy-and-OpenAI)#{4}":[299,301],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)":[302,323],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{1}":[304,305],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{2}":[306,307],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{3}":[308,309],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{4}":[310,312],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{5}":[313,316],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{6}":[317,318],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{7}":[319,321],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Creating OpenAI API Key [​](#Creating-OpenAI-API-Key)#{8}":[322,323],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)":[324,342],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{1}":[326,335],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{2}":[336,337],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{3}":[338,339],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"ArgumentError: api_key cannot be empty\" despite having set `OPENAI_API_KEY`? {#Getting-an-error-\"ArgumentError:-api_key-cannot-be-empty\"-despite-having-set-OPENAI_API_KEY?} [​](#getting-an-error-argumenterror-api-key-cannot-be-empty-despite-having-set-openai-api-key-getting-an-error-argumenterror-apikey-cannot-be-empty-despite-having-set-openaiapi-key)#{4}":[340,342],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)":[343,366],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{1}":[345,352],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{2}":[353,354],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{3}":[355,357],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Getting an error \"Rate limit exceeded\" from OpenAI? [​](#Getting-an-error-\"Rate-limit-exceeded\"-from-OpenAI?)#{4}":[358,366],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)":[367,381],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{1}":[369,370],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{2}":[371,372],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{3}":[373,375],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{4}":[376,379],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting OpenAI Spending Limits [​](#Setting-OpenAI-Spending-Limits)#{5}":[380,381],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)":[382,397],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)#{1}":[384,395],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How much does it cost? Is it worth paying for? [​](#How-much-does-it-cost?-Is-it-worth-paying-for?)#{2}":[396,397],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)":[398,429],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{1}":[400,411],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{2}":[412,413],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{3}":[414,416],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{4}":[417,420],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{5}":[421,422],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{6}":[423,425],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{7}":[426,427],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Configuring the Environment Variable for API Key [​](#Configuring-the-Environment-Variable-for-API-Key)#{8}":[428,429],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting the API Key via Preferences.jl [​](#Setting-the-API-Key-via-Preferences.jl)":[430,439],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setting the API Key via Preferences.jl [​](#Setting-the-API-Key-via-Preferences.jl)#{1}":[432,439],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`) [​](#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs))":[440,443],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Understanding the API Keyword Arguments in `aigenerate` (`api_kwargs`) [​](#Understanding-the-API-Keyword-Arguments-in-aigenerate-(api_kwargs))#{1}":[442,443],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Instant Access from Anywhere [​](#Instant-Access-from-Anywhere)":[444,456],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Instant Access from Anywhere [​](#Instant-Access-from-Anywhere)#{1}":[446,456],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Open Source Alternatives [​](#Open-Source-Alternatives)":[457,460],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Open Source Alternatives [​](#Open-Source-Alternatives)#{1}":[459,460],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setup Guide for Ollama [​](#Setup-Guide-for-Ollama)":[461,478],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Setup Guide for Ollama [​](#Setup-Guide-for-Ollama)#{1}":[463,478],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)":[479,491],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{1}":[481,484],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{2}":[485,486],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{3}":[487,488],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Changing the Default Model or Schema [​](#Changing-the-Default-Model-or-Schema)#{4}":[489,491],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)":[492,534],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{1}":[494,495],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{2}":[496,497],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{3}":[498,508],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{4}":[509,510],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have Multi-turn Conversations? [​](#How-to-have-Multi-turn-Conversations?)#{5}":[511,534],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)":[535,636],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{1}":[537,577],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{2}":[578,579],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{3}":[580,582],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to have typed responses? [​](#How-to-have-typed-responses?)#{4}":[583,636],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to quickly create a prompt template? [​](#How-to-quickly-create-a-prompt-template?)":[637,718],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How to quickly create a prompt template? [​](#How-to-quickly-create-a-prompt-template?)#{1}":[639,718],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Do we have a RecursiveCharacterTextSplitter like Langchain? [​](#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?)":[719,738],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#Do we have a RecursiveCharacterTextSplitter like Langchain? [​](#Do-we-have-a-RecursiveCharacterTextSplitter-like-Langchain?)#{1}":[721,738],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)":[739,758],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{1}":[741,742],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{2}":[743,744],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{3}":[745,747],"#Frequently Asked Questions [​](#Frequently-Asked-Questions)#How would I fine-tune a model? [​](#How-would-I-fine-tune-a-model?)#{4}":[748,758]},"last_import":{"mtime":1712727660438,"size":29627,"at":1740449882750,"hash":"27235a647f255c8d5ca2983e38944ab1eb3a30ebefe294a79fd31bb45eb90c5b"},"key":"PromptingTools.jl/Frequently Asked Questions - PromptingTools.jl 1.md"},