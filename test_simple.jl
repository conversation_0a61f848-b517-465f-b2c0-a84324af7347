#!/usr/bin/env julia

# 簡單測試
println("開始簡單測試...")

try
    using Dates
    include("src/WonderGridEngine.jl")
    using .WonderGridEngine
    
    println("✓ 模組載入成功")
    
    # 創建最小測試數據
    test_drawings = [
        WonderGridEngine.Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
        WonderGridEngine.Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
        WonderGridEngine.Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47])
    ]
    
    println("✓ 測試數據創建成功")
    
    # 測試配對統計計算
    config = WonderGridEngine.WonderGridConfig()
    println("✓ 配置創建成功")
    
    println("開始計算配對統計...")
    pair_stats = WonderGridEngine.calculate_advanced_pair_statistics(test_drawings, config)
    println("✓ 配對統計計算成功，找到 $(length(pair_stats)) 個配對")
    
    # 測試關鍵號碼選擇
    println("開始選擇關鍵號碼...")
    key_result = WonderGridEngine.select_key_number(test_drawings, :ffg)
    println("✓ 關鍵號碼選擇成功: $(key_result.number)")
    
    # 測試頂級配對選擇
    println("開始選擇頂級配對...")
    top_pairs = WonderGridEngine.select_top_pairs_advanced(pair_stats, key_result.number, config)
    println("✓ 頂級配對選擇成功，選出 $(length(top_pairs)) 個配對")
    
    println("✓ 所有測試通過")
    
catch e
    println("✗ 測試失敗: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

println("測試完成")
