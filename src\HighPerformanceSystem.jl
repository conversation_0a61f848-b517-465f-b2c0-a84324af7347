# src/HighPerformanceSystem.jl

module HighPerformanceSystem

using Dates
using Statistics
using Printf
using ..SaliuLottery: Drawing
using ..WonderGridEngine: WonderGridConfig, WonderGridError

# 重新導出子模組
include("HighPerformanceCombinationGenerator.jl")
include("ParallelProcessingEngine.jl")
include("MemoryOptimizationSystem.jl")

using .HighPerformanceCombinationGenerator
using .ParallelProcessingEngine
using .MemoryOptimizationSystem

export HighPerformanceController, PerformanceConfig, SystemMetrics,
       create_high_performance_system, initialize_hp_system,
       generate_combinations_hp, optimize_system_performance,
       get_system_metrics, shutdown_hp_system

# 高效能配置結構
struct PerformanceConfig
    max_combinations::Int
    parallel_enabled::Bool
    memory_optimization::Bool
    cache_enabled::Bool
    strategy_auto_switch::Bool
    performance_monitoring::Bool
    optimization_level::Int  # 1-5
    
    function PerformanceConfig(;
        max_combinations::Int = 10000,
        parallel_enabled::Bool = true,
        memory_optimization::Bool = true,
        cache_enabled::Bool = true,
        strategy_auto_switch::Bool = true,
        performance_monitoring::Bool = true,
        optimization_level::Int = 4
    )
        new(max_combinations, parallel_enabled, memory_optimization,
            cache_enabled, strategy_auto_switch, performance_monitoring,
            optimization_level)
    end
end

# 系統指標結構
struct SystemMetrics
    generation_time::Float64
    memory_usage::Float64
    cpu_utilization::Float64
    cache_hit_ratio::Float64
    parallel_efficiency::Float64
    strategy_performance::Dict{String, Float64}
    system_throughput::Float64
    error_rate::Float64
    
    function SystemMetrics(
        generation_time::Float64,
        memory_usage::Float64,
        cpu_utilization::Float64,
        cache_hit_ratio::Float64,
        parallel_efficiency::Float64,
        strategy_performance::Dict{String, Float64},
        system_throughput::Float64,
        error_rate::Float64
    )
        new(generation_time, memory_usage, cpu_utilization, cache_hit_ratio,
            parallel_efficiency, strategy_performance, system_throughput, error_rate)
    end
end

# 高效能系統控制器
mutable struct HighPerformanceController
    config::PerformanceConfig
    strategy_selector::StrategySelector
    parallel_manager::ThreadPoolManager
    memory_optimizer::MemoryOptimizer
    system_start_time::DateTime
    total_generations::Int
    performance_history::Vector{SystemMetrics}
    
    function HighPerformanceController(config::PerformanceConfig)
        strategy_selector = create_strategy_selector(config.strategy_auto_switch)
        
        parallel_manager = if config.parallel_enabled
            create_parallel_engine()
        else
            ThreadPoolManager(ParallelConfig(max_threads = 1))
        end
        
        memory_optimizer = if config.memory_optimization
            create_memory_optimizer(optimization_level = config.optimization_level)
        else
            create_memory_optimizer(optimization_level = 1)
        end
        
        new(config, strategy_selector, parallel_manager, memory_optimizer,
            now(), 0, Vector{SystemMetrics}())
    end
end

"""
創建高效能系統
"""
function create_high_performance_system(
    config::PerformanceConfig = PerformanceConfig()
)::HighPerformanceController
    
    println("🚀 創建高效能組合生成系統...")
    
    controller = HighPerformanceController(config)
    
    println("✅ 高效能系統創建完成")
    println("  - 最大組合數: $(config.max_combinations)")
    println("  - 並行處理: $(config.parallel_enabled ? "啟用" : "停用")")
    println("  - 記憶體優化: $(config.memory_optimization ? "啟用" : "停用")")
    println("  - 快取系統: $(config.cache_enabled ? "啟用" : "停用")")
    println("  - 自動策略切換: $(config.strategy_auto_switch ? "啟用" : "停用")")
    println("  - 優化等級: $(config.optimization_level)/5")
    
    return controller
end

"""
初始化高效能系統
"""
function initialize_hp_system(
    controller::HighPerformanceController,
    historical_data::Vector{Drawing}
)::Bool
    
    println("🔧 初始化高效能系統...")
    
    try
        # 預熱快取
        if controller.config.cache_enabled
            preheat_cache(controller, historical_data)
        end
        
        # 初始化並行引擎
        if controller.config.parallel_enabled
            println("  - 並行引擎初始化完成")
        end
        
        # 預計算常用數據
        if controller.config.memory_optimization
            precompute_common_data(controller, historical_data)
        end
        
        println("✅ 高效能系統初始化成功")
        return true
        
    catch e
        println("❌ 高效能系統初始化失敗: $e")
        return false
    end
end

"""
高效能組合生成
"""
function generate_combinations_hp(
    controller::HighPerformanceController,
    data_context::Dict{String, Any},
    target_combinations::Int = 0
)::Vector{Vector{Int}}
    
    start_time = time()
    println("⚡ 開始高效能組合生成...")
    
    # 確定生成數量
    combinations_count = target_combinations > 0 ? 
                        target_combinations : 
                        controller.config.max_combinations
    
    try
        # 選擇最佳策略
        current_strategy = if controller.config.strategy_auto_switch
            select_optimal_strategy(controller, data_context)
        else
            controller.strategy_selector.current_strategy
        end
        
        println("  - 使用策略: $(current_strategy.method)")
        
        # 檢查快取
        cache_key = generate_cache_key(data_context, current_strategy, combinations_count)
        cached_result = if controller.config.cache_enabled
            get_cached_combinations(controller, cache_key)
        else
            nothing
        end
        
        combinations = if cached_result !== nothing
            println("  - 使用快取結果")
            cached_result
        else
            # 生成新組合
            if controller.config.parallel_enabled && combinations_count > 500
                generate_parallel_combinations(controller, current_strategy, data_context, combinations_count)
            else
                generate_sequential_combinations(controller, current_strategy, data_context, combinations_count)
            end
        end
        
        # 快取結果
        if controller.config.cache_enabled && cached_result === nothing
            cache_combinations(controller, cache_key, combinations)
        end
        
        # 記錄效能指標
        generation_time = time() - start_time
        record_performance_metrics(controller, generation_time, combinations, current_strategy)
        
        controller.total_generations += 1
        
        println("✅ 高效能生成完成")
        println("  - 生成數量: $(length(combinations))")
        println("  - 執行時間: $(round(generation_time, digits=2)) 秒")
        println("  - 吞吐量: $(round(length(combinations) / generation_time, digits=1)) 組合/秒")
        
        return combinations
        
    catch e
        println("❌ 高效能生成失敗: $e")
        throw(WonderGridError("高效能生成失敗", :hp_generation_failed, Dict("error" => string(e))))
    end
end

"""
優化系統效能
"""
function optimize_system_performance(controller::HighPerformanceController)::Nothing
    
    println("🔧 優化系統效能...")
    
    # 記憶體優化
    if controller.config.memory_optimization
        cleanup_memory(controller.memory_optimizer)
        optimize_gc(controller.memory_optimizer)
    end
    
    # 並行處理優化
    if controller.config.parallel_enabled
        optimize_thread_allocation(controller.parallel_manager)
        balance_load(controller.parallel_manager)
    end
    
    # 策略效能優化
    if controller.config.strategy_auto_switch
        optimize_strategy_selection(controller)
    end
    
    # 快取優化
    if controller.config.cache_enabled
        optimize_cache_performance(controller)
    end
    
    println("✅ 系統效能優化完成")
    
    return nothing
end

"""
獲取系統指標
"""
function get_system_metrics(controller::HighPerformanceController)::SystemMetrics
    
    # 計算平均效能指標
    if isempty(controller.performance_history)
        return SystemMetrics(0.0, 0.0, 0.0, 0.0, 0.0, Dict{String, Float64}(), 0.0, 0.0)
    end
    
    recent_metrics = controller.performance_history[max(1, end-9):end]  # 最近10次
    
    avg_generation_time = mean([m.generation_time for m in recent_metrics])
    avg_memory_usage = mean([m.memory_usage for m in recent_metrics])
    avg_cpu_utilization = mean([m.cpu_utilization for m in recent_metrics])
    avg_cache_hit_ratio = mean([m.cache_hit_ratio for m in recent_metrics])
    avg_parallel_efficiency = mean([m.parallel_efficiency for m in recent_metrics])
    avg_throughput = mean([m.system_throughput for m in recent_metrics])
    avg_error_rate = mean([m.error_rate for m in recent_metrics])
    
    # 策略效能統計
    strategy_performance = Dict{String, Float64}()
    for (method, history) in controller.strategy_selector.performance_history
        if !isempty(history)
            strategy_performance[string(method)] = mean(history[max(1, end-4):end])
        end
    end
    
    return SystemMetrics(
        avg_generation_time,
        avg_memory_usage,
        avg_cpu_utilization,
        avg_cache_hit_ratio,
        avg_parallel_efficiency,
        strategy_performance,
        avg_throughput,
        avg_error_rate
    )
end

"""
關閉高效能系統
"""
function shutdown_hp_system(controller::HighPerformanceController)::Nothing
    
    println("🔌 關閉高效能系統...")
    
    # 清理記憶體
    cleanup_memory(controller.memory_optimizer, true)
    
    # 生成最終報告
    final_metrics = get_system_metrics(controller)
    runtime = now() - controller.system_start_time
    
    println("📊 最終系統報告:")
    println("  - 運行時間: $(round(runtime.value / 1000 / 3600, digits=2)) 小時")
    println("  - 總生成次數: $(controller.total_generations)")
    println("  - 平均生成時間: $(round(final_metrics.generation_time, digits=2)) 秒")
    println("  - 平均記憶體使用: $(round(final_metrics.memory_usage, digits=1)) MB")
    println("  - 平均吞吐量: $(round(final_metrics.system_throughput, digits=1)) 組合/秒")
    println("  - 快取命中率: $(round(final_metrics.cache_hit_ratio * 100, digits=1))%")
    println("  - 並行效率: $(round(final_metrics.parallel_efficiency * 100, digits=1))%")
    
    println("✅ 高效能系統已安全關閉")
    
    return nothing
end

# 輔助函數
function preheat_cache(controller::HighPerformanceController, historical_data::Vector{Drawing})::Nothing
    
    println("  - 預熱快取系統...")
    
    # 快取常用的頻率數據
    if !isempty(historical_data)
        frequencies = calculate_number_frequencies(historical_data)
        cache_data(controller.memory_optimizer, "number_frequencies", frequencies)
        
        pair_frequencies = calculate_pair_frequencies(historical_data)
        cache_data(controller.memory_optimizer, "pair_frequencies", pair_frequencies)
    end
    
    return nothing
end

function precompute_common_data(controller::HighPerformanceController, historical_data::Vector{Drawing})::Nothing
    
    println("  - 預計算常用數據...")
    
    if !isempty(historical_data)
        # 懶惰計算熱號
        compute_lazy(controller.memory_optimizer, "hot_numbers", 
                    () -> get_hot_numbers(calculate_number_frequencies(historical_data), 20))
        
        # 懶惰計算冷號
        compute_lazy(controller.memory_optimizer, "cold_numbers",
                    () -> get_cold_numbers(calculate_number_frequencies(historical_data), 20))
    end
    
    return nothing
end

function select_optimal_strategy(
    controller::HighPerformanceController,
    data_context::Dict{String, Any}
)::GenerationStrategy
    
    # 基於當前效能選擇最佳策略
    recommendations = get_strategy_recommendations(controller.strategy_selector, data_context)
    
    if !isempty(recommendations)
        best_strategy_name = recommendations[1]["strategy"]
        
        # 找到對應的策略
        for strategy in controller.strategy_selector.available_strategies
            if string(strategy.method) == best_strategy_name
                return strategy
            end
        end
    end
    
    return controller.strategy_selector.current_strategy
end

function generate_cache_key(
    data_context::Dict{String, Any},
    strategy::GenerationStrategy,
    combinations_count::Int
)::String
    
    # 生成快取鍵
    data_hash = hash(get(data_context, "historical_data", []))
    strategy_hash = hash(string(strategy.method))
    
    return "combinations_$(data_hash)_$(strategy_hash)_$(combinations_count)"
end

function get_cached_combinations(
    controller::HighPerformanceController,
    cache_key::String
)::Union{Vector{Vector{Int}}, Nothing}
    
    cache = controller.memory_optimizer.cache_manager
    
    if haskey(cache.cache_data, cache_key)
        cache.hit_count += 1
        cache.access_times[cache_key] = now()
        cache.access_counts[cache_key] = get(cache.access_counts, cache_key, 0) + 1
        return cache.cache_data[cache_key]
    else
        cache.miss_count += 1
        return nothing
    end
end

function cache_combinations(
    controller::HighPerformanceController,
    cache_key::String,
    combinations::Vector{Vector{Int}}
)::Nothing
    
    cache_data(controller.memory_optimizer, cache_key, combinations, 1800)  # 30分鐘TTL
    
    return nothing
end

function generate_parallel_combinations(
    controller::HighPerformanceController,
    strategy::GenerationStrategy,
    data_context::Dict{String, Any},
    combinations_count::Int
)::Vector{Vector{Int}}
    
    println("  - 使用並行生成")
    
    # 創建生成函數
    generation_function = (ctx, count) -> begin
        config = CombinationConfig(max_combinations = count)
        result = generate_combinations_parallel(strategy, ctx, config)
        return result.combinations
    end
    
    # 分配任務
    tasks = distribute_tasks(
        controller.parallel_manager,
        combinations_count,
        generation_function,
        data_context
    )
    
    # 執行並行生成
    combinations = execute_parallel_generation(controller.parallel_manager, tasks)
    
    return combinations
end

function generate_sequential_combinations(
    controller::HighPerformanceController,
    strategy::GenerationStrategy,
    data_context::Dict{String, Any},
    combinations_count::Int
)::Vector{Vector{Int}}
    
    println("  - 使用順序生成")
    
    config = CombinationConfig(max_combinations = combinations_count)
    result = generate_combinations_parallel(strategy, data_context, config)
    
    return result.combinations
end

function record_performance_metrics(
    controller::HighPerformanceController,
    generation_time::Float64,
    combinations::Vector{Vector{Int}},
    strategy::GenerationStrategy
)::Nothing
    
    # 計算各項指標
    memory_stats = get_memory_statistics(controller.memory_optimizer)
    parallel_stats = get_parallel_statistics(controller.parallel_manager)
    
    memory_usage = memory_stats["memory_pool"]["current_usage"] / (1024 * 1024)  # MB
    cpu_utilization = calculate_thread_utilization(controller.parallel_manager)
    cache_hit_ratio = memory_stats["cache"]["hit_ratio"]
    parallel_efficiency = parallel_stats["load_balance_efficiency"]
    system_throughput = length(combinations) / generation_time
    error_rate = 0.0  # 簡化實現
    
    # 策略效能
    strategy_performance = Dict{String, Float64}()
    quality_score = calculate_quality_score(combinations, Dict{String, Any}())
    strategy_performance[string(strategy.method)] = quality_score
    
    # 記錄策略效能
    evaluate_strategy_performance(controller.strategy_selector, strategy.method, quality_score)
    
    # 創建指標記錄
    metrics = SystemMetrics(
        generation_time,
        memory_usage,
        cpu_utilization,
        cache_hit_ratio,
        parallel_efficiency,
        strategy_performance,
        system_throughput,
        error_rate
    )
    
    push!(controller.performance_history, metrics)
    
    # 限制歷史長度
    if length(controller.performance_history) > 100
        controller.performance_history = controller.performance_history[end-99:end]
    end
    
    return nothing
end

function optimize_strategy_selection(controller::HighPerformanceController)::Nothing
    
    # 分析策略效能並調整
    for (method, history) in controller.strategy_selector.performance_history
        if length(history) >= 5
            recent_performance = mean(history[end-4:end])
            if recent_performance < 0.3  # 效能過低
                println("  - 策略 $method 效能過低，建議避免使用")
            end
        end
    end
    
    return nothing
end

function optimize_cache_performance(controller::HighPerformanceController)::Nothing
    
    cache = controller.memory_optimizer.cache_manager
    
    # 分析快取命中率
    total_requests = cache.hit_count + cache.miss_count
    if total_requests > 0
        hit_ratio = cache.hit_count / total_requests
        if hit_ratio < 0.5
            println("  - 快取命中率較低 ($(round(hit_ratio * 100, digits=1))%)，考慮調整快取策略")
        end
    end
    
    return nothing
end

end # module HighPerformanceSystem
