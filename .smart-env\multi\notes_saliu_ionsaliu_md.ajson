
"smart_sources:notes/saliu/ionsaliu.md": {"path":"notes/saliu/ionsaliu.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"nnpev9","at":1753230880448},"class_name":"SmartSource","last_import":{"mtime":1735450883000,"size":44421,"at":1753230880657,"hash":"nnpev9"},"blocks":{"#":[3,1110],"#---frontmatter---":[151,307],"#write new functions to implement the content in the `0002.Lottery, Lotto Strategy in Reverse Turn Loss into Win (2).md` file":[1111,1317],"#write new functions to implement the content in the `0002.Lottery, Lotto Strategy in Reverse Turn Loss into Win (2).md` file#{1}":[1112,1317]},"outlinks":[]},