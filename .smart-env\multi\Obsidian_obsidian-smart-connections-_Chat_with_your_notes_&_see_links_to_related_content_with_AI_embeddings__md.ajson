"smart_sources:Obsidian/obsidian-smart-connections- Chat with your notes & see links to related content with AI embeddings..md": {"path":"Obsidian/obsidian-smart-connections- Chat with your notes & see links to related content with AI embeddings..md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04359028,-0.02745365,0.00890227,-0.01548115,-0.01270998,-0.03780656,-0.0125548,0.0540421,-0.00376697,0.01663191,-0.00889858,-0.05306478,0.0563573,0.04834547,0.03502428,0.05555784,-0.01949133,0.02128992,-0.01163897,0.04209085,0.07467402,-0.05369189,0.04233892,-0.00033304,-0.00321346,0.02921111,-0.00767102,-0.08799821,0.02651615,-0.16705897,0.04700719,-0.00774129,-0.0182201,0.00678982,-0.05627262,0.00763638,-0.00654346,-0.04269039,-0.04467002,0.02797585,0.02786343,-0.01440615,0.01775468,-0.02697687,-0.0021971,-0.04069837,-0.04571204,0.00848484,0.01179024,-0.03610903,0.00389024,-0.02067508,0.00044617,-0.01691628,0.02521532,0.00720145,0.04978885,0.04540163,0.01897946,0.06563308,0.04908115,0.07497413,-0.16504651,0.11081263,-0.01050473,-0.00001416,-0.00168438,0.01696001,-0.00664977,0.0013772,0.03893121,0.0504486,0.00729738,-0.01113751,0.03006748,0.00861583,0.03424439,-0.01727419,0.0071343,-0.01742805,0.02211386,0.05192775,-0.03122509,0.01320283,-0.01623671,-0.05538964,0.01141662,0.01335953,-0.02364043,0.02632389,-0.08813952,-0.024274,0.03164177,0.02670868,-0.00690738,0.00682391,0.01800951,-0.0010109,-0.08024707,0.11670735,-0.06465284,-0.00548421,0.00445845,0.02702617,0.04622264,-0.02519521,-0.01654406,-0.04109962,-0.02538924,0.07491779,-0.00492707,0.00607864,0.0186944,-0.06503596,0.04752247,0.0175237,0.01765038,0.0447045,-0.0043718,0.00867102,-0.05225049,0.04404634,0.03724913,-0.02234155,0.06800459,-0.05191676,0.08429532,0.04758717,0.04054977,0.09303368,0.06981193,-0.00644949,-0.02192571,-0.0429682,-0.00814474,0.02750197,-0.01044424,-0.02282793,-0.00998193,0.00862409,-0.03722837,0.02092972,0.05109852,-0.11686502,0.0461038,0.02713777,0.03570194,0.00424739,0.00075333,-0.10526019,0.00454343,0.02723284,-0.01535493,-0.03103311,-0.00640829,-0.03319566,0.0763803,0.09444153,-0.11081546,0.05398792,-0.00966377,-0.06042679,-0.03238601,0.04497338,0.0126927,-0.13431208,-0.04384667,-0.02608221,0.00853738,-0.04222998,0.00540581,0.00945712,-0.04567324,0.02695636,0.08849397,0.00005579,-0.09968101,-0.04457984,-0.04929155,0.06732679,-0.01350276,-0.05068649,0.00957054,0.00121371,-0.03620563,-0.06081506,-0.01630004,-0.07107568,0.0147553,-0.08161979,-0.043405,-0.00468244,-0.04524964,0.09203964,0.01490307,-0.0156983,0.00390511,-0.02797383,-0.03206068,0.02267201,0.04451421,-0.04935268,-0.03228801,0.01772956,-0.10580768,-0.02358292,-0.00203939,-0.02938289,0.03123418,0.05462636,-0.03890307,-0.006799,0.0686297,0.01434212,-0.02393537,-0.02799997,0.03862428,0.02997597,0.00836355,0.10049649,0.00672291,0.00511491,-0.07316831,-0.22098395,-0.06264548,0.01759046,0.0034799,0.05871651,-0.12833926,0.03892581,0.00913878,0.04197741,0.08436088,0.1277172,0.03253911,0.0329041,-0.00958972,-0.01879982,0.05813252,0.0027206,0.0348013,-0.02635545,0.02102559,-0.01270792,0.08634254,-0.0111216,-0.10714182,-0.02253854,-0.01512248,0.18180569,0.01432448,0.02259941,-0.00626933,0.0077051,0.02784288,-0.00675509,-0.15138619,0.03938999,0.00339549,0.04501021,0.06788126,-0.01184762,-0.02762976,-0.11940103,0.08931176,-0.02227685,-0.08956472,-0.02162116,-0.02360587,-0.03742702,-0.08619571,-0.04019993,-0.02479731,0.02884954,0.05430166,0.01335815,0.0464651,-0.04547819,-0.0016182,-0.04988805,-0.01097479,-0.01715868,0.07286985,-0.05842088,0.02580141,-0.01794993,-0.05107244,0.08290217,0.03461906,0.02962524,0.01544779,0.05815064,0.02145223,-0.01587326,0.14531703,-0.02419897,0.02739718,0.06003699,-0.04048002,0.01541549,-0.05660302,0.00130405,0.0270582,0.07622304,-0.0484944,0.05595673,0.0269825,-0.02865968,0.04343458,0.02026705,0.00849706,0.06789296,0.01739151,0.00347179,0.04201741,-0.0135979,-0.05507369,0.06603958,0.01122974,-0.26800692,0.04599256,0.01308385,-0.01766599,-0.03073127,0.0251005,0.03945013,-0.05346208,-0.01374983,0.01209126,0.01590786,0.01355144,-0.00619636,-0.05571801,0.03679251,0.02368719,0.02474227,-0.02331842,-0.03642785,-0.01811528,-0.02683528,0.03048552,0.22431207,0.02345533,0.01161499,-0.01730577,-0.08254861,0.0446692,0.05530463,-0.03827814,-0.03379016,-0.00370373,0.04003695,-0.04392324,0.04791789,-0.01011103,-0.0285282,-0.07193399,0.02786943,0.00181012,0.00222163,0.02286089,-0.02373739,0.04214061,0.0791017,0.02627854,-0.07051969,-0.01551408,-0.0058391,0.02758921,-0.02200704,-0.03008073,-0.00620202,0.01574376,-0.00678247,0.03112232,0.00230403,-0.0395316,-0.02168498,-0.01033895,-0.00716894,-0.03394279,0.02927926,0.01674052,-0.03021403],"last_embed":{"hash":"af979d1f3385cfc5ad9a782efd4ad5acd5eb95358f050f45b2ff256083ab3619","tokens":474}}},"last_read":{"hash":"af979d1f3385cfc5ad9a782efd4ad5acd5eb95358f050f45b2ff256083ab3619","at":1745995209872},"class_name":"SmartSource2","outlinks":[{"title":"![Easy to install and use","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-install.gif","line":32},{"title":"![Easy to install and use","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-install.gif","line":32},{"title":"![Smart View demo showing that the most relevant notes are shown at the top based on the current note.","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-Smart-View-light.gif","line":106},{"title":"![Smart View demo showing that the most relevant notes are shown at the top based on the current note.","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-Smart-View-light.gif","line":106},{"title":"![Demo showing how Smart Chat can answer the question \"Who am I?\" based on the notes in Obsidian","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-chat-who-am-i.gif","line":116},{"title":"![Demo showing how Smart Chat can answer the question \"Who am I?\" based on the notes in Obsidian","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-chat-who-am-i.gif","line":116},{"title":"\"I've switched over from Mem to Obsidian when I found this plugin\"","target":"https://discord.com/channels/686053708261228577/694233507500916796/1091164112425320538","line":131},{"title":"\"I actually decided to start using Obsidian BECAUSE of Smart Connections.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/441#:~:text=I%20actually%20decided%20to%20start%20using%20Obsidian%20BECAUSE%20of%20Smart%20Connections.","line":132},{"title":"\"This is such a game-changingly helpful plugin\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/329#issuecomment-2002162224:~:text=This%20is%20such%20a%20game%2Dchangingly%20helpful%20plugin","line":133},{"title":"\"This plugin has become a vital part of my life\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/120#issuecomment-1492842117:~:text=This%20plugin%20has%20become%20a%20vital%20part%20of%20my%20life","line":134},{"title":"\"This is by far my favourite Obsidian plug-in and it is immensely helpful. I'll be doing a full video about using it for PhD research\"","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions/371#discussioncomment-7977910:~:text=This%20is%20by%20far%20my%20favourite%20Obsidian%20plug%2Din%20and%20it%20is%20immensely%20helpful.%20I%27ll%20be%20doing%20a%20full%20video%20about%20using%20it%20for%20PhD%20research","line":135},{"title":"\"It's astonishing the power it provides to deal with scientific research and scientific articles included in the vault.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/250#issuecomment-1595108987:~:text=It%27s%20astonishing%20the%20power%20it%20provids%20to%20deal%20with%20scientific%20research%20and%20scientific%20articles%20included%20in%20the%20vault.","line":136},{"title":"\"This is an AWESOME little plugin. Thanks for sharing.\"","target":"https://forum.obsidian.md/t/introducing-smart-chat-a-game-changer-for-your-obsidian-notes-smart-connections-plugin/56391/8?u=wfh#:~:text=This%20is%20an%20AWESOME%20little%20plugin.%20Thanks%20for%20sharing.","line":139},{"title":"\"Hopping on to also say thanks. I have been wanting this feature in something ever since reading about tad starners remembrance agent in the 90s! And this is even better.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/47#issuecomment-1471441869:~:text=Hopping%20on%20to%20also%20say%20thanks.%20I%20have%20been%20wanting%20this%20feature%20in%20something%20ever%20since%20reading%20about%20tad%20starners%20remembrance%20agent%20in%20the%2090s!%20And%20this%20is%20even%20better.","line":140},{"title":"\"I'm having so much fun using your chat plugin to search my notes better and get insights.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/57#:~:text=Hi%2C-,I%27m%20having%20so%20much%20fun%20using%20your%20chat%20plugin%20to%20search%20my%20notes%20better%20and%20get%20insights,-.%20I%20was%20just","line":141},{"title":"\"This is undoubtedly an excellent plugin that makes a significant improvement in how we interact with our notes.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/pull/219#issuecomment-1562572364:~:text=This%20is%20undoubtedly%20an%20excellent%20plugin%20that%20makes%20a%20significant%20improvement%20in%20how%20we%20interact%20with%20our%20notes.","line":142},{"title":"wrote a post about how I use it to massively save time on summarizing legal cases","target":"https://careylening.substack.com/p/the-power-of-links-and-second-brains-d1d","line":144},{"title":"\"I love this extension so much. So many potential features by the way.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/48#issuecomment-1459929611:~:text=I%20love%20this%20extension%0Aso%20much.%20So%20many%20potential%20features%20by%20the%20way.","line":185},{"title":"\"This plugin is fantastic\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/47#:~:text=This%20plugin%20is%20fantastic","line":186},{"title":"\"This is a terrific idea\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/54#:~:text=This%20is%20a%20terrific%20idea","line":187},{"title":"\"This plugins could be a Game changer!\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/54#:~:text=This%20plugins%20could%20be%20a%20Game%20changer!","line":188},{"title":"\"I personally love the app\"","target":"https://old.reddit.com/r/ObsidianMD/comments/11s0oxb/chat_with_your_notes_now_available_in_the_smart/jcd73y8/?context=3#:~:text=I%20personally%20love%20the%20app","line":189},{"title":"\"This app is such a game changer\"","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions/203#discussioncomment-5854265:~:text=This%20app%20is%20such%20a%20game%20changer.","line":190},{"title":"\"Absolutely LOVE this plugin\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/202#issue-1702708828:~:text=Absolutely%20LOVE%20this%20plugin.","line":191},{"title":"\"Smart-connections is a fantastic plugin\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/280#issuecomment-1630047763:~:text=Smart%2Dconnections%20is%20a%20fantastic%20plugin","line":192},{"title":"\"Hi, amazing plugin! 🔥\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/57#issuecomment-1488187361:~:text=Hi%2C%20amazing%20plugin,%F0%9F%94%A5","line":193},{"title":"\"Absolutely mind blowing\"","target":"https://twitter.com/micka_dore/status/1641527570867822615?s=20","line":194},{"title":"\"I love this plugin\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/496#issuecomment-1996755512:~:text=interest%20of%20course.-,I%20love%20this%20plugin,-.","line":195},{"title":"\"Now it serves me as a way to brainstorm potential connections, and I have seen major improvements over the past few months. I especially enjoy using it as part of my book digestion and relation process.\"","target":"https://old.reddit.com/r/ObsidianMD/comments/11s0oxb/chat_with_your_notes_now_available_in_the_smart/jcczwiq/?context=3#:~:text=Now%20it%20serves%20me%20as%20a%20way%20to%20brainstorm%20potential%20connections%2C%20and%20I%20have%20seen%20major%20improvements%20over%20the%20past%20few%20months.%20I%20especially%20enjoy%20using%20it%20as%20part%20of%20my%20book%20digestion%20and%20relation%20process.","line":196},{"title":"\"this is just such an incredible plugin!\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/244#issuecomment-1595765101:~:text=this%20is%20just%20such%20an%20incredible%20plugin","line":197},{"title":"\"Tried it, and it worked as well as I could hope! Thanks for making this.\"","target":"https://old.reddit.com/r/ObsidianMD/comments/11s0oxb/chat_with_your_notes_now_available_in_the_smart/jcdpwsg/?context=3#:~:text=Tried%20it%2C%20and%20it%20worked%20as%20well%20as%20I%20could%20hope!%20Thanks%20for%20making%20this.","line":198},{"title":"\"This is an amazing extension.\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/32#issuecomment-1435798970:~:text=This%20is%20an%20amazing%20extension.","line":199},{"title":"This is really cool...","target":"https://twitter.com/rcvd_io/status/1638271532932780035?s=20","line":200},{"title":"\"This is an amazing plugin!\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/20#:~:text=This%20is%20an%20amazing%20plugin!","line":201},{"title":"\"With smart connections, by just opening one such note, I can find all the others that reference the concept\"","target":"https://discord.com/channels/686053708261228577/694233507500916796/1091167414865109012","line":202},{"title":"\"Has amazing potential to unlock lots of new info that can be added to your vault\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/19#issue-1533699525:~:text=has%20amazing%20potential%20to%20unlock%20lots%20of%20new%20info%20that%20can%20be%20added%20to%20your%20vault","line":203},{"title":"\"Great plugin!\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/1#issue-1511238131:~:text=Dec%2026%2C%202022-,Great%20plugin!,-My%20request%20is","line":204},{"title":"\"Loving the plugin so far!\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/2#issue-1511288845:~:text=Loving%20the%20plugin%20so%20far!","line":205},{"title":"\"Smart Connections is so cool. I'm noticing similarities between notes that talk about the same thing but don't share any words.\"","target":"https://discord.com/channels/686053708261228577/694233507500916796/1065057689949982870#:~:text=Smart%20Connections%20plugin%3F%20It%27s%20so%20cool.%20I%27m%20noticing%20similarities%20between%20notes%20that%20talk%20about%20the%20same%20thing%20but%20don%27t%20share%20any%20words.","line":206},{"title":"\"Thanks for doing this. I love the idea to have OpenAI help look through my notes to find connections\"","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/47#issue-1609765217:~:text=Thanks%20for%20doing%20this.%20I%20love%20the%20idea%20to%20have%20OpenAI%20help%20look%20through%20my%20notes%20to%20find%20connections.","line":207},{"title":"Even more love for Smart Connections 🥰","target":"https://smartconnections.app/smart-connections-love/","line":209},{"title":"![","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/obsidian-community-smart-connections-install.png","line":231},{"title":"![Demo showing Smart View results changing based on the current note","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-Smart-View-dark.gif","line":273},{"title":"![Demo showing Smart View results changing based on the current note","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-Smart-View-dark.gif","line":273},{"title":"![Demo showing the fold/unfold feature which displays or hides the content of the notes in the Smart View","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-fold-unfold.gif","line":293},{"title":"![Demo showing the fold/unfold feature which displays or hides the content of the notes in the Smart View","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-fold-unfold.gif","line":293},{"title":"![Access model settings in the Smart Chat","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/smart-chat-settings.png","line":337},{"title":"Public Discussions on GitHub","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions","line":429},{"title":"GitHub 上的公開討論","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions","line":436},{"title":"`smart-chat-model`","target":"https://github.com/brianpetro/smart-chat-model","line":500},{"title":"Report an issue on GitHub","target":"https://github.com/brianpetro/obsidian-smart-connections/issues","line":547},{"title":"Brian on Founderoo","target":"https://www.founderoo.co/posts/smart-connections-brian-petro","line":639},{"title":"Smart Chat","target":"#smart-chat-transform-your-notes-into-interactive-conversations","line":657},{"title":"Smart View","target":"#smart-view","line":657},{"title":"Obsidian","target":"https://obsidian.md/","line":657},{"title":"Introducing Smart Connections","target":"https://wfhbrian.com/introducing-obsidian-smart-connections/","line":657},{"title":"discussion on GitHub","target":"https://github.com/brianpetro/obsidian-smart-connections/discussions","line":665},{"title":"Smart Chat","target":"https://wfhbrian.com/introducing-smart-chat-transform-your-obsidian-notes-into-interactive-ai-powered-conversations/","line":675},{"title":"![","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-chat-who-am-i.gif","line":677},{"title":"![smart-connections-chat-who-am-i.gif","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-chat-who-am-i.gif","line":677},{"title":"![","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-chat-backstory.gif","line":708},{"title":"![smart-connections-chat-backstory.gif","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/smart-connections-chat-backstory.gif","line":708},{"title":"![Smart View demo showing that the most relevant notes are shown at the top based on the current note.","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-Smart-View-light.gif","line":738},{"title":"![Smart View demo showing that the most relevant notes are shown at the top based on the current note.","target":"https://github.com/brianpetro/obsidian-smart-connections/raw/main/assets/SCv2-Smart-View-light.gif","line":738},{"title":"![","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/sc-dynamic-codeblock-code.png","line":763},{"title":"![","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/sc-dynamic-codeblock-rendered.png","line":763},{"title":"this issue","target":"https://github.com/brianpetro/obsidian-smart-connections/issues/27","line":771},{"title":"![","target":"https://github.com/brianpetro/obsidian-smart-connections/brianpetro/obsidian-smart-connections/raw/main/assets/search-feature.png","line":787},{"title":"2021-08-01","target":"https://openai.com/api/pricing/","line":804},{"title":"GitHub repository","target":"https://github.com/brianpetro/obsidian-smart-connections/issues","line":840},{"title":"OpenAI Embeddings","target":"https://beta.openai.com/docs/guides/embeddings","line":849},{"title":"Terms of Service","target":"https://openai.com/terms","line":851},{"title":"smartconnections.app","target":"https://smartconnections.app \"https://smartconnections.app\"","line":866},{"title":"chatgpt","target":"/topics/chatgpt \"Topic: chatgpt\"","line":870},{"title":"claude","target":"/topics/claude \"Topic: claude\"","line":870},{"title":"embeddings","target":"/topics/embeddings \"Topic: embeddings\"","line":870},{"title":"gemini","target":"/topics/gemini \"Topic: gemini\"","line":870},{"title":"llama3","target":"/topics/llama3 \"Topic: llama3\"","line":870},{"title":"obsidian","target":"/topics/obsidian \"Topic: obsidian\"","line":870},{"title":"obsidian-plugin","target":"/topics/obsidian-plugin \"Topic: obsidian-plugin\"","line":870},{"title":"Readme","target":"#readme-ov-file","line":874},{"title":"GPL-3.0 license","target":"#GPL-3.0-1-ov-file","line":878},{"title":"Activity","target":"/brianpetro/obsidian-smart-connections/activity","line":880},{"title":"**1.9k** stars","target":"/brianpetro/obsidian-smart-connections/stargazers","line":884},{"title":"**29** watching","target":"/brianpetro/obsidian-smart-connections/watchers","line":888},{"title":"**141** forks","target":"/brianpetro/obsidian-smart-connections/forks","line":892},{"title":"Report repository","target":"/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Fbrianpetro%2Fobsidian-smart-connections&report=brianpetro+%28user%29","line":894},{"title":"Releases 192","target":"/brianpetro/obsidian-smart-connections/releases","line":896},{"title":"\n\n2.1.54 Latest\n\nMay 2, 2024\n\n\n\n","target":"/brianpetro/obsidian-smart-connections/releases/tag/2.1.54","line":898},{"title":"+ 191 releases","target":"/brianpetro/obsidian-smart-connections/releases","line":908},{"title":"SmartConnections.app/support","target":"http://SmartConnections.app/support","line":912},{"title":"Contributors 6","target":"/brianpetro/obsidian-smart-connections/graphs/contributors","line":914},{"title":"![@brianpetro","target":"https://avatars.githubusercontent.com/u/1886014?s=64&v=4","line":916},{"title":"![@Lisandra-dev","target":"https://avatars.githubusercontent.com/u/30244939?s=64&v=4","line":917},{"title":"![@sofvanh","target":"https://avatars.githubusercontent.com/u/23138848?s=64&v=4","line":918},{"title":"![@yorrd","target":"https://avatars.githubusercontent.com/u/1191486?s=64&v=4","line":919},{"title":"![@Mearman","target":"https://avatars.githubusercontent.com/u/1331872?s=64&v=4","line":920},{"title":"![@pinuke","target":"https://avatars.githubusercontent.com/u/12621101?s=64&v=4","line":921},{"title":"JavaScript 84.0%","target":"/brianpetro/obsidian-smart-connections/search?l=javascript","line":925},{"title":"EJS 12.5%","target":"/brianpetro/obsidian-smart-connections/search?l=ejs","line":926},{"title":"CSS 3.5%","target":"/brianpetro/obsidian-smart-connections/search?l=css","line":927},{"title":"Terms","target":"https://docs.github.com/site-policy/github-terms/github-terms-of-service","line":935},{"title":"Privacy","target":"https://docs.github.com/site-policy/privacy-policies/github-privacy-statement","line":936},{"title":"Security","target":"https://github.com/security","line":937},{"title":"Status","target":"https://www.githubstatus.com/","line":938},{"title":"Docs","target":"https://docs.github.com/","line":939},{"title":"Contact","target":"https://support.github.com?tags=dotcom-footer","line":940}],"blocks":{"#":[2,2],"#brianpetro/obsidian-smart-connections":[3,5],"#Smart Connections: AI-Powered Note Connections `v2.1`":[6,944],"#Smart Connections: AI-Powered Note Connections `v2.1`#{1}":[8,37],"#Smart Connections: AI-Powered Note Connections `v2.1`#A brief history":[38,57],"#Smart Connections: AI-Powered Note Connections `v2.1`#A brief history#{1}":[40,57],"#Smart Connections: AI-Powered Note Connections `v2.1`#Mission":[58,85],"#Smart Connections: AI-Powered Note Connections `v2.1`#Mission#{1}":[60,85],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections":[86,121],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#{1}":[88,91],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart View: AI-Powered Note Discovery":[92,109],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart View: AI-Powered Note Discovery#{1}":[94,109],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart Chat: AI Conversations Based on Your Notes":[110,121],"#Smart Connections: AI-Powered Note Connections `v2.1`#Discover Smart Connections#Smart Chat: AI Conversations Based on Your Notes#{1}":[112,121],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials":[122,210],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{1}":[124,129],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{2}":[130,130],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{3}":[131,131],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{4}":[132,132],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{5}":[133,133],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{6}":[134,134],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{7}":[135,135],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{8}":[136,136],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{9}":[137,137],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{10}":[138,138],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{11}":[139,139],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{12}":[140,140],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{13}":[141,141],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{14}":[142,142],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{15}":[143,143],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{16}":[144,144],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{17}":[145,145],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{18}":[146,146],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{19}":[147,147],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{20}":[148,148],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{21}":[149,149],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{22}":[150,151],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{23}":[152,153],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{24}":[154,154],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{25}":[155,155],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{26}":[156,156],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{27}":[157,157],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{28}":[158,158],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{29}":[159,159],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{30}":[160,160],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{31}":[161,161],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{32}":[162,162],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{33}":[163,163],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{34}":[164,164],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{35}":[165,165],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{36}":[166,166],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{37}":[167,167],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{38}":[168,168],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{39}":[169,169],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{40}":[170,170],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{41}":[171,171],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{42}":[172,172],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{43}":[173,173],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{44}":[174,174],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{45}":[175,175],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{46}":[176,176],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{47}":[177,177],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{48}":[178,178],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{49}":[179,179],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{50}":[180,180],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{51}":[181,181],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{52}":[182,182],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{53}":[183,183],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{54}":[184,184],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{55}":[185,185],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{56}":[186,186],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{57}":[187,187],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{58}":[188,188],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{59}":[189,189],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{60}":[190,190],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{61}":[191,191],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{62}":[192,192],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{63}":[193,193],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{64}":[194,194],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{65}":[195,195],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{66}":[196,196],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{67}":[197,197],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{68}":[198,198],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{69}":[199,199],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{70}":[200,200],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{71}":[201,201],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{72}":[202,202],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{73}":[203,203],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{74}":[204,204],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{75}":[205,205],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{76}":[206,206],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{77}":[207,208],"#Smart Connections: AI-Powered Note Connections `v2.1`#User Testimonials#{78}":[209,210],"#Smart Connections: AI-Powered Note Connections `v2.1`#How it Works":[211,220],"#Smart Connections: AI-Powered Note Connections `v2.1`#How it Works#{1}":[213,214],"#Smart Connections: AI-Powered Note Connections `v2.1`#How it Works#{2}":[215,220],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation":[221,232],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation#{1}":[223,226],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation#Installing from Obsidian community plugins":[227,232],"#Smart Connections: AI-Powered Note Connections `v2.1`#Easy Installation#Installing from Obsidian community plugins#{1}":[229,232],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings":[233,262],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#{1}":[235,236],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Default settings":[237,248],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Default settings#{1}":[239,240],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Default settings##Local embedding models":[241,248],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Default settings##Local embedding models#{1}":[243,248],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup":[249,262],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup#{1}":[251,252],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup#{2}":[253,256],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup##OpenAI API Key":[257,262],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup##OpenAI API Key#{1}":[259,260],"#Smart Connections: AI-Powered Note Connections `v2.1`#Settings#Additional setup##OpenAI API Key#{2}":[261,262],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features":[263,412],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#{1}":[265,266],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View":[267,308],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View#{1}":[269,278],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View":[279,296],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{1}":[281,282],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{2}":[283,283],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{3}":[284,284],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{4}":[285,286],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Accessing notes in the Smart View#{5}":[287,296],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search":[297,308],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search#{1}":[299,302],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search#{2}":[303,304],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart View##Smart View Search#{3}":[305,308],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat":[309,367],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat#{1}":[311,316],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)":[317,332],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#{1}":[319,322],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock":[323,332],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock#{1}":[325,326],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock#{2}":[327,327],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock#{3}":[328,329],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##ChatMD (new in `v2.1`)#`sc-context` codeblock#{4}":[330,332],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models":[333,367],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#{1}":[335,338],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.0 (current)":[339,347],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.0 (current)#{1}":[341,342],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.0 (current)#{2}":[343,347],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)":[348,367],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)#{1}":[350,351],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)#{2}":[352,352],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)#{3}":[353,355],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)#{4}":[356,359],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)#{5}":[360,362],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)#{6}":[363,365],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Chat##chat models#v2.1 (early release)#{7}":[366,367],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)":[368,390],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{1}":[370,371],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{2}":[372,373],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{3}":[374,375],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{4}":[376,378],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{5}":[379,379],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{6}":[380,380],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{7}":[381,381],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{8}":[382,383],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Smart Command (Find Notes)#{9}":[384,390],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Dynamic Code Blocks":[391,399],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Dynamic Code Blocks#{1}":[393,394],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Dynamic Code Blocks#{2}":[395,395],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Dynamic Code Blocks#{3}":[396,399],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Note vs Block Level Smart Connections":[400,406],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Note vs Block Level Smart Connections#{1}":[402,403],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Note vs Block Level Smart Connections#{2}":[404,406],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Notifications and Progress Indicators":[407,412],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Notifications and Progress Indicators#{1}":[409,410],"#Smart Connections: AI-Powered Note Connections `v2.1`#Features#Notifications and Progress Indicators#{2}":[411,412],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility":[413,420],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility#{1}":[415,416],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility#{2}":[417,417],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility#{3}":[418,418],"#Smart Connections: AI-Powered Note Connections `v2.1`#File-type Compatibility#{4}":[419,420],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community":[421,452],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#{1}":[423,428],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#{2}":[429,430],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#{3}":[431,438],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)":[439,452],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)#{1}":[441,444],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)##Screencasts":[445,452],"#Smart Connections: AI-Powered Note Connections `v2.1`#Join Our Community#How to contribute (non-technical)##Screencasts#{1}":[447,452],"#Smart Connections: AI-Powered Note Connections `v2.1`#Content & Resources":[453,459],"#Smart Connections: AI-Powered Note Connections `v2.1`#Content & Resources#{1}":[455,456],"#Smart Connections: AI-Powered Note Connections `v2.1`#Content & Resources#{2}":[457,457],"#Smart Connections: AI-Powered Note Connections `v2.1`#Content & Resources#{3}":[458,459],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture":[460,525],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{1}":[462,465],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{2}":[466,466],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{3}":[467,467],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#{4}":[468,469],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models":[470,487],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{1}":[472,473],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{2}":[474,474],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{3}":[475,475],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{4}":[476,477],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Local Models#{5}":[478,487],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies":[488,519],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies#{1}":[490,493],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies":[494,509],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{1}":[496,499],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{2}":[500,501],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##First-party Dependencies#{3}":[502,509],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##Third-party Dependencies":[510,519],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##Third-party Dependencies#{1}":[512,513],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##Third-party Dependencies#{2}":[514,515],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Dependencies##Third-party Dependencies#{3}":[516,519],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Themes & Styles":[520,525],"#Smart Connections: AI-Powered Note Connections `v2.1`#Smart Connections Architecture#Themes & Styles#{1}":[522,525],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues":[526,548],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#{1}":[528,529],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#Embedding process keeps restarting or is slow":[530,536],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#Embedding process keeps restarting or is slow#{1}":[532,533],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#Embedding process keeps restarting or is slow#{2}":[534,534],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#Embedding process keeps restarting or is slow#{3}":[535,536],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors":[537,548],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{1}":[539,540],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{2}":[541,541],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{3}":[542,542],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{4}":[543,544],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{5}":[545,546],"#Smart Connections: AI-Powered Note Connections `v2.1`#Troubleshooting Common Issues#OpenAI API errors#{6}":[547,548],"#Smart Connections: AI-Powered Note Connections `v2.1`#Developers":[549,562],"#Smart Connections: AI-Powered Note Connections `v2.1`#Developers#{1}":[551,552],"#Smart Connections: AI-Powered Note Connections `v2.1`#Developers#{2}":[553,557],"#Smart Connections: AI-Powered Note Connections `v2.1`#Developers#{3}":[558,562],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification":[563,625],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{1}":[565,566],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{2}":[567,568],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{3}":[569,570],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{4}":[571,571],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{5}":[572,572],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{6}":[573,573],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{7}":[574,574],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{8}":[575,575],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{9}":[576,576],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{10}":[577,577],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{11}":[578,578],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{12}":[579,579],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{13}":[580,580],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{14}":[581,581],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{15}":[582,582],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{16}":[583,583],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{17}":[584,584],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{18}":[585,585],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{19}":[586,586],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{20}":[587,587],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{21}":[588,588],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{22}":[589,589],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{23}":[590,590],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{24}":[591,591],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{25}":[592,592],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{26}":[593,593],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{27}":[594,594],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{28}":[595,595],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{29}":[596,596],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{30}":[597,597],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{31}":[598,598],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{32}":[599,599],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{33}":[600,600],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{34}":[601,601],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{35}":[602,602],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{36}":[603,603],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{37}":[604,604],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{38}":[605,605],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{39}":[606,606],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{40}":[607,607],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{41}":[608,608],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{42}":[609,609],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{43}":[610,610],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{44}":[611,611],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{45}":[612,612],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{46}":[613,613],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{47}":[614,614],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{48}":[615,615],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{49}":[616,616],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{50}":[617,617],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{51}":[618,618],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{52}":[619,619],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{53}":[620,620],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{54}":[621,621],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{55}":[622,622],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{56}":[623,623],"#Smart Connections: AI-Powered Note Connections `v2.1`#Supporter License Clarification#{57}":[624,625],"#Smart Connections: AI-Powered Note Connections `v2.1`#Commercial use":[626,631],"#Smart Connections: AI-Powered Note Connections `v2.1`#Commercial use#{1}":[628,631],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator":[632,944],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator#{1}":[634,637],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator#{2}":[638,638],"#Smart Connections: AI-Powered Note Connections `v2.1`#Meet the Creator#{3}":[639,944],"#---frontmatter---":[641,null]},"last_import":{"mtime":1717052463967,"size":65380,"at":1740449882698,"hash":"af979d1f3385cfc5ad9a782efd4ad5acd5eb95358f050f45b2ff256083ab3619"},"key":"Obsidian/obsidian-smart-connections- Chat with your notes & see links to related content with AI embeddings..md"},