
"smart_sources:notes/saliu/Lottery Pairs, Lot<PERSON> Frequency, Lotto Wonder Grid.md": {"path":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12423541,-0.02660662,-0.06963696,-0.0037551,-0.05506782,0.06582584,0.03535209,0.01376877,0.10241793,-0.04066049,-0.02629812,-0.03380456,0.05878973,0.04547868,-0.04929329,0.00535101,-0.04262829,-0.00470809,-0.04812612,0.04481287,0.1228087,-0.09144323,-0.06321047,-0.10615442,0.07166444,0.0006474,-0.03086595,-0.05259834,-0.01172014,-0.23309837,0.00281335,0.04433525,0.02835767,-0.03406456,-0.08489221,-0.02706558,-0.01470765,0.03943987,-0.04010016,0.02696602,0.01825444,0.00991385,0.01429022,-0.00981793,-0.0299548,-0.01780943,-0.01216288,-0.00935343,-0.01024208,-0.01138478,-0.03084812,-0.02409812,-0.04432793,0.03862659,0.05367083,0.09815399,0.04978396,0.02520643,-0.00294861,0.06691436,0.01718198,0.06002371,-0.21419612,0.04972389,0.03793402,-0.02442751,-0.00817541,-0.00648218,-0.01003723,0.01757788,0.04326977,0.0615424,-0.01917464,0.01363956,0.02905927,-0.00946126,-0.04856023,-0.06474575,-0.04331277,0.02927094,-0.04335852,0.00052438,0.01914625,-0.02136209,0.01546971,0.05968055,0.04686523,-0.02186733,0.02198603,-0.05954597,-0.02310285,0.03524866,0.00858924,0.01212561,0.0337749,0.05319008,0.03273924,-0.02584822,0.0209365,0.10642604,-0.02751208,0.0247693,-0.01682915,0.0689889,0.02654627,-0.03640155,-0.01800709,-0.04782292,-0.00724698,0.00201729,0.02397086,-0.00277516,0.03665421,-0.02427344,-0.01870491,-0.00105044,0.04409913,0.01372532,0.08127317,-0.00665377,-0.0832166,0.03890553,0.02684467,-0.02884468,0.01650105,0.02454968,0.03418492,0.08065167,0.0471644,0.02910702,0.02752308,-0.0325086,-0.08481494,-0.04246235,-0.05618646,0.0102078,0.0397906,0.00048231,-0.0242095,0.04523838,0.01318238,-0.0356917,0.05197876,-0.09757127,0.00618723,0.06686293,-0.04037663,-0.01073697,-0.01769612,0.07786464,0.01170246,-0.01386486,-0.00961624,-0.07865076,-0.00574188,-0.00664715,0.08365814,0.06207676,0.00721491,0.02763364,-0.01298511,-0.05081246,-0.05554327,0.10315374,0.00373964,-0.10816117,-0.00306498,0.00609095,-0.05593263,-0.0817485,-0.00892233,0.02524044,-0.01469763,0.02880163,0.06143656,-0.02940638,-0.08024129,-0.04720687,0.0033596,-0.01066372,-0.003645,0.00468291,-0.05750844,0.00349719,-0.02136868,-0.0602197,-0.00811868,-0.03427878,-0.00417854,0.0500243,-0.02561906,-0.00386673,0.00315294,0.04442645,-0.01076063,-0.04194498,-0.01680061,-0.06589357,0.05485529,0.00594815,-0.06589953,-0.02977398,-0.01311616,-0.02184869,-0.02748799,0.03863435,0.00983316,-0.01891052,0.05641306,0.03115508,0.02934945,-0.0214488,0.00246833,0.07395332,-0.03667876,0.0378553,-0.00701117,0.03780709,0.00665495,0.02209675,0.01025956,-0.0406964,-0.09375553,-0.17897172,-0.03273347,-0.03293162,-0.01221935,0.04923712,-0.02327637,0.05985999,-0.01680652,0.01329579,0.07579876,0.06846929,-0.05308333,-0.02357322,-0.01110868,-0.04450922,-0.00256075,-0.07941481,-0.02875352,-0.02806273,0.02958862,-0.01305176,0.0800463,-0.05884477,-0.05322203,0.0752641,-0.0383323,0.15348962,0.06616332,-0.0313429,0.01391047,0.04484178,-0.00306156,-0.01318593,-0.02172144,0.01833302,0.01358207,-0.00488635,-0.03693043,-0.05502554,0.01098343,-0.12673748,0.01461133,0.01179277,-0.06487241,-0.04106846,0.03538412,-0.00769375,0.00705022,-0.02973763,0.02354226,0.07497887,-0.01856492,0.01151181,0.01117293,0.03363398,-0.01615533,-0.06459957,-0.00093628,0.00805122,0.03454173,0.00820356,0.0119666,0.03525782,-0.01767808,0.01354837,0.01709107,0.00118747,0.00589124,0.00068105,-0.01408673,0.00073105,0.09139425,0.02938573,0.02975805,0.01928926,0.01706772,0.04604322,-0.09363323,0.02322379,0.00022132,0.04256447,-0.0846458,0.03080037,0.05426048,0.06024464,-0.01568884,0.08275378,0.07726646,0.0189691,-0.01761577,-0.01932138,0.02721075,0.02207415,-0.00172908,0.06090381,0.01714008,-0.28766218,0.04855992,-0.04288557,0.09274339,-0.04733536,-0.02487044,0.01734783,-0.00485646,-0.00721034,-0.05405599,0.04641089,0.01887237,0.04618426,-0.03188566,-0.04036639,0.01261294,-0.0068613,-0.05784201,0.05010812,0.01476761,0.053811,0.01049255,0.25853488,0.01914919,0.00662654,0.02151736,-0.00746202,0.03990054,-0.01949595,0.04482603,0.00193862,0.00865439,0.06690099,-0.03659261,-0.03722207,0.0707552,0.00446678,-0.00309026,0.0123649,0.00740954,-0.05226772,0.02388353,-0.01436566,0.00296569,0.13626291,-0.0018878,-0.00867379,-0.06906169,0.01426858,0.05924902,-0.09757032,-0.04490545,-0.03507393,0.00777262,-0.00983708,0.02507084,0.0574779,0.01597262,-0.00833968,0.00505278,-0.00723195,0.02291773,0.03020516,0.01638252,0.00727328],"last_embed":{"hash":"1ukkcx5","tokens":461}}},"last_read":{"hash":"1ukkcx5","at":1753423487340},"class_name":"SmartSource","last_import":{"mtime":1753363612562,"size":17605,"at":1753423416052,"hash":"1ukkcx5"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid":[8,151],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#{1}":[10,15],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#<u>Lottery Pairs And Repeat Probability</u>":[16,18],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#<u>Lottery Pairs And Repeat Probability</u>#{1}":[17,18],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#By Ion Saliu, _★ Founder of: Lottery Mathematics, Lotto Programming Science_":[19,125],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#By Ion Saliu, _★ Founder of: Lottery Mathematics, Lotto Programming Science_#{1}":[21,125],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)":[126,151],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{1}":[128,128],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{2}":[129,129],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{3}":[130,130],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{4}":[131,131],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{5}":[132,132],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{6}":[133,133],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{7}":[134,134],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{8}":[135,135],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{9}":[136,136],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{10}":[137,137],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{11}":[138,138],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{12}":[139,139],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{13}":[140,140],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{14}":[141,141],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{15}":[142,142],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{16}":[143,143],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{17}":[144,145],"#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{18}":[146,151]},"outlinks":[{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/images/software-lottery-gambling.gif","line":14},{"title":"The Best Pick 4 software ever - Super Pick-4 lotto.","target":"https://saliu.com/forum/HLINE.gif","line":21},{"title":"lotto wonder grid","target":"https://saliu.com/bbs/messages/638.html","line":26},{"title":"_**Brief history of my lottery, lotto experience**_","target":"https://saliu.com/bbs/messages/532.html","line":76},{"title":"_**Lottery Strategy, Systems Based On Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":84},{"title":"<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","target":"https://saliu.com/content/lottery.html","line":126},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":128},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":130},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":132},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":134},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":135},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":136},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":137},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":138},{"title":"_**Lottery lotto filtering in software**_","target":"https://saliu.com/filters.html","line":139},{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":140},{"title":"_**Updates to the Bright lotto, lottery, horse racing software**_","target":"https://saliu.com/forum/software-updates.html","line":142},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":144},{"title":"The lottery wonder-grid shows improvement in odds compared to playing lotto numbers randomly.","target":"https://saliu.com/forum/HLINE.gif","line":146},{"title":"Forums","target":"https://forums.saliu.com/","line":148},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":148},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":148},{"title":"Contents","target":"https://saliu.com/content/index.html","line":148},{"title":"Help","target":"https://saliu.com/Help.htm","line":148},{"title":"Home","target":"https://saliu.com/index.htm","line":148},{"title":"Software","target":"https://saliu.com/infodown.html","line":148},{"title":"Search","target":"https://saliu.com/Search.htm","line":148},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":148},{"title":"Wonder Grid is a theory and strategy in lotto and lottery based on number frequency in recent drawings.","target":"https://saliu.com/forum/HLINE.gif","line":150}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["lottery","lotto","strategy","pairs","pairings","theory","number","frequency","combination","win","odds","random"],"source":"https://saliu.com/forum/lottery-pairs.html","author":null}},"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11807397,-0.03541248,-0.03901159,0.03319975,-0.04831454,0.07671078,-0.00434393,0.03823758,0.07109952,-0.02070192,-0.02287869,-0.0278021,0.05383547,0.05014935,-0.00543326,0.01312661,-0.05987811,0.00046101,-0.0504913,0.04601112,0.09315286,-0.06163612,-0.05452181,-0.08246826,0.04265719,-0.01405527,-0.02182271,-0.05646796,-0.01226976,-0.18562615,0.01933989,0.04879211,-0.0076324,-0.03897576,-0.04673602,-0.0419029,-0.05716738,0.0758267,-0.01727864,0.03943846,0.03685587,0.01220255,-0.04018766,-0.00801729,-0.01793507,-0.02648263,-0.00935767,-0.02053892,-0.00510037,0.02151205,-0.04948509,-0.02524692,-0.0247679,0.02814285,0.0771681,0.07099292,0.03492766,0.00815904,0.01135254,0.07230501,0.01383239,0.02515177,-0.20909439,0.0586247,0.03193856,-0.01998303,0.00197211,0.01774019,-0.02567223,0.03640952,0.0635864,0.05171329,-0.01390148,0.03024438,0.02985059,0.0243628,-0.07845685,-0.07465612,-0.05431323,0.01220515,-0.05259445,-0.01464934,-0.01193719,-0.04567416,-0.00276669,0.03262591,0.06092806,0.05224289,0.02728314,-0.02363921,-0.02461457,0.03760067,0.02525176,0.03383167,-0.01059539,0.04648136,0.0181946,0.00073985,-0.00158876,0.12760985,-0.04483961,0.03620885,0.020825,0.04422873,0.02000427,-0.01823101,-0.03087462,-0.04240941,-0.01027888,0.00351168,0.06531714,0.02173603,0.03682398,-0.07447914,-0.02224283,-0.00633365,0.0333537,0.03162892,0.05366565,-0.00112241,-0.09028217,0.01034711,0.01825308,-0.0120186,0.02762167,0.0558612,0.04101115,0.07365249,0.0336813,0.03019582,0.0046829,-0.0287298,-0.08856168,-0.01893078,-0.03966911,0.00118859,0.05634864,-0.04073176,0.00261954,-0.0022169,0.01399855,-0.04518092,0.04264086,-0.11493847,0.00315959,0.0760118,-0.00807171,-0.00708678,0.01012968,0.03248267,-0.0166253,-0.01113851,0.01589182,-0.09229638,-0.00253095,0.01083358,0.10468127,0.0864396,0.03910685,0.02886194,0.00498622,-0.02095905,-0.04879228,0.14144906,-0.0154754,-0.14636125,-0.02039045,0.01001494,-0.03103095,-0.09170767,-0.00272425,0.0362131,0.00088166,0.02347538,0.10544622,-0.00318815,-0.0432592,-0.03479319,-0.03406564,0.01184525,-0.04009299,0.00389209,-0.04672752,0.01168346,-0.04086833,-0.07448612,-0.0121927,-0.01304729,-0.00170104,0.06074338,-0.01665147,-0.08142835,0.00263153,0.05759742,0.00648047,-0.05652013,-0.0297858,-0.08601397,0.04847516,-0.01711516,-0.05304644,-0.02989948,-0.01596983,-0.00184348,-0.02111644,0.02469465,0.01047805,-0.02496251,0.05038623,0.04278764,0.01783402,0.00747899,0.03487722,0.05114982,-0.06469036,0.01915102,-0.01018387,0.00370046,0.02974827,0.00694677,-0.0120854,-0.03214334,-0.08263651,-0.20249748,-0.01688819,-0.02738734,-0.01528302,-0.00024251,-0.04496437,0.05563547,0.00915846,0.02919987,0.09153283,0.03868391,-0.06095034,-0.02477582,-0.0275807,-0.03336516,-0.01552669,-0.05262187,-0.04494835,0.00720137,0.03413947,-0.00315055,0.08578807,-0.04273458,-0.07877471,0.06985363,-0.02714954,0.13983893,0.08812871,-0.03005522,-0.02411542,0.07931372,0.01480589,0.00689174,-0.03553936,0.02100163,0.02496484,-0.01939921,-0.00255125,-0.07382302,-0.0291586,-0.12990847,0.00665012,0.00149539,-0.07113428,-0.00514968,0.0138505,0.00272231,0.00657853,-0.03103629,0.02070208,0.04339592,-0.02111404,0.01538645,0.00530008,0.0545617,0.01512089,-0.0818113,-0.02079692,0.0017079,0.00716954,-0.01857686,0.00734716,0.04320075,-0.03074813,-0.00905332,0.04048245,0.00833779,-0.02256824,-0.04930709,-0.01178421,0.01641037,0.06593417,0.02100957,0.02804325,-0.00541941,0.00854847,0.00536229,-0.06495394,0.04543708,-0.00059714,0.03296854,-0.03691696,0.03698346,0.06978734,0.06165827,0.01447547,0.0354804,0.05061349,0.03367938,-0.01757649,-0.02432742,0.03653256,0.03745124,-0.00694048,0.07901613,-0.01797474,-0.28158978,0.05570038,0.00479552,0.07904905,-0.01840333,0.01227552,0.0121905,0.01133759,-0.04097541,-0.01779534,0.06606994,0.03203627,-0.00290232,-0.04692672,-0.04166854,0.01667399,0.00858488,-0.0583852,0.03830059,-0.00842963,0.04358912,0.01635977,0.26157579,0.06060095,-0.00120811,0.00997037,0.0113865,0.03536021,-0.02454102,0.03039074,0.01141623,0.01709211,0.04841924,-0.04574337,-0.01314786,0.0694625,-0.02256876,0.00625214,-0.0054982,0.0357297,-0.08932574,0.03031483,-0.02383377,0.01898372,0.12345032,0.02944902,-0.02365449,-0.06350005,-0.01124884,0.02592736,-0.08376873,-0.07781267,-0.0517763,0.01165004,-0.01760614,0.03468451,0.06371291,-0.02073461,0.03281412,0.02330133,0.02147826,-0.0035516,0.03465701,-0.01472931,-0.01110913],"last_embed":{"hash":"14ndd09","tokens":95}}},"text":null,"length":0,"last_read":{"hash":"14ndd09","at":1753423486613},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#---frontmatter---","lines":[1,6],"size":208,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11700396,-0.02306274,-0.07294489,-0.01137119,-0.05760507,0.06113803,0.04490383,0.00563439,0.10791612,-0.04097264,-0.01806835,-0.0329364,0.05522931,0.04293645,-0.04746009,0.0014369,-0.0358659,-0.00060868,-0.05451411,0.0356617,0.11639492,-0.10401112,-0.07590731,-0.11178271,0.0689171,0.00013471,-0.03180783,-0.0538365,-0.01823897,-0.23747776,0.00377414,0.0402351,0.03428923,-0.03952572,-0.07771946,-0.03917448,-0.01924803,0.02927029,-0.05710813,0.01587377,0.00841051,0.0015583,0.01735166,-0.01413967,-0.0255276,-0.02977021,-0.00801998,-0.00615578,-0.0079249,-0.02097061,-0.03052896,-0.02288157,-0.03641493,0.05005157,0.04167763,0.09538151,0.0597266,0.03394846,-0.01895483,0.06619504,0.00702691,0.06840693,-0.20925543,0.04729953,0.05007669,-0.01941136,-0.00951469,-0.01635846,0.00427207,0.01635376,0.0399824,0.06717186,-0.01044691,0.00711548,0.01692953,-0.01481498,-0.04047598,-0.05511581,-0.02800878,0.02098339,-0.04836027,-0.00327925,0.02492172,-0.02028265,0.01992601,0.05486703,0.04658373,-0.02951447,0.02037136,-0.05093138,-0.02626265,0.04376551,0.00195642,-0.0037741,0.03287342,0.04926841,0.03624982,-0.02952001,0.02908538,0.11706097,-0.01434357,0.01514038,-0.03070448,0.07117426,0.02035518,-0.03933653,-0.02616898,-0.03827562,-0.01318278,0.00416189,0.03099404,-0.00752477,0.03644726,-0.00908041,-0.03121077,0.00608893,0.04447923,0.01074429,0.08029822,-0.00819366,-0.08428198,0.03238541,0.02696198,-0.03297618,0.01532095,0.02270102,0.03440694,0.08716317,0.03570473,0.04094826,0.02967872,-0.03026598,-0.07989198,-0.04278078,-0.05449889,0.01226473,0.03108898,0.01486106,-0.02543125,0.0579183,0.02032069,-0.03354392,0.05112632,-0.09403536,0.00824306,0.07017437,-0.06234456,-0.00404284,-0.01587305,0.06601697,0.01857903,-0.01898271,-0.01914222,-0.07992749,-0.01594014,-0.00360482,0.07618948,0.05740847,-0.00590313,0.03513347,-0.01490416,-0.05465474,-0.03465787,0.10927193,0.01997626,-0.08727333,0.00499706,0.00940137,-0.04632258,-0.07571795,-0.01075293,0.02510866,-0.01564742,0.02116964,0.04655031,-0.03138731,-0.09047562,-0.05188069,0.01358835,-0.01948139,0.00092188,0.00498816,-0.05174289,0.00142547,-0.00466244,-0.0531953,-0.00985525,-0.03137885,-0.00290425,0.05195781,-0.02756815,0.00037473,-0.00472199,0.03096748,-0.01106901,-0.03844543,-0.01734857,-0.06279776,0.05007745,0.00919062,-0.0513829,-0.02635688,-0.01403812,-0.01455448,-0.01199594,0.04618014,-0.00632699,-0.01848613,0.06019325,0.0297884,0.03441752,-0.02596983,0.00768098,0.06594522,-0.03324779,0.03764245,0.00329973,0.030301,0.00668966,0.04159698,0.01461055,-0.03651537,-0.08878504,-0.18766102,-0.03207144,-0.03338045,-0.005655,0.05650176,-0.01921513,0.05520164,-0.02842965,0.02038898,0.07776832,0.07650822,-0.05080711,-0.02418298,0.00458503,-0.04800256,-0.00010622,-0.07904761,-0.02709592,-0.02839872,0.03793596,-0.02576714,0.08278225,-0.046725,-0.05509251,0.07559786,-0.03994936,0.15946023,0.04366213,-0.03392024,0.01683645,0.02713652,-0.0013553,-0.02081407,-0.02063432,0.02339199,0.02169301,-0.01052235,-0.03753544,-0.04464416,0.03117616,-0.12383544,0.01549724,0.01153978,-0.06279401,-0.04629121,0.02991897,0.000557,0.00598403,-0.02544635,0.0275769,0.0720275,-0.01904895,0.01212969,0.00770457,0.04012547,-0.02049392,-0.06036329,0.00183839,0.0017615,0.04408468,0.01686868,0.00270809,0.02122618,-0.02021384,0.02523752,0.02151242,0.00785984,0.0033276,0.01601517,-0.00758515,0.00506374,0.08869656,0.03512047,0.02863237,0.02678772,0.01759405,0.04723367,-0.09670362,0.0144493,0.00808255,0.03513136,-0.08498668,0.02910651,0.05377225,0.05941623,-0.01131964,0.09201515,0.08175947,0.01942989,-0.0140566,-0.01664244,0.03421053,0.01052852,0.00482323,0.0474967,0.02553598,-0.29014122,0.05056899,-0.06507898,0.08941654,-0.04877276,-0.0363295,0.01918152,0.00106802,-0.00145059,-0.05311406,0.04582643,0.02131901,0.05246963,-0.03903006,-0.03631143,0.01088899,0.00678617,-0.06984295,0.04503668,0.00857258,0.05044749,0.01817651,0.25367036,0.00636203,0.01126172,0.01798049,-0.00465506,0.03544411,-0.02544082,0.04616183,0.00014223,0.01130436,0.06650206,-0.03446162,-0.03715995,0.07104873,0.00261134,-0.01275369,0.01926558,0.01169569,-0.04422922,0.00859129,-0.01787014,-0.00472269,0.14113727,0.00242036,-0.02087167,-0.06230072,0.02899505,0.06198306,-0.10498842,-0.03523856,-0.02690584,0.00189909,-0.00498638,0.02127857,0.03342364,0.02118075,-0.0116659,-0.01169258,-0.00412019,0.02196773,0.02102089,0.00936694,0.00433669],"last_embed":{"hash":"c7gb0a","tokens":470}}},"text":null,"length":0,"last_read":{"hash":"c7gb0a","at":1753423486643},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid","lines":[8,151],"size":17381,"outlinks":[{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/images/software-lottery-gambling.gif","line":7},{"title":"The Best Pick 4 software ever - Super Pick-4 lotto.","target":"https://saliu.com/forum/HLINE.gif","line":14},{"title":"lotto wonder grid","target":"https://saliu.com/bbs/messages/638.html","line":19},{"title":"_**Brief history of my lottery, lotto experience**_","target":"https://saliu.com/bbs/messages/532.html","line":69},{"title":"_**Lottery Strategy, Systems Based On Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":77},{"title":"<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","target":"https://saliu.com/content/lottery.html","line":119},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":121},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":123},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":125},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":127},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":128},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":129},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":130},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":131},{"title":"_**Lottery lotto filtering in software**_","target":"https://saliu.com/filters.html","line":132},{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":133},{"title":"_**Updates to the Bright lotto, lottery, horse racing software**_","target":"https://saliu.com/forum/software-updates.html","line":135},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":137},{"title":"The lottery wonder-grid shows improvement in odds compared to playing lotto numbers randomly.","target":"https://saliu.com/forum/HLINE.gif","line":139},{"title":"Forums","target":"https://forums.saliu.com/","line":141},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":141},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":141},{"title":"Contents","target":"https://saliu.com/content/index.html","line":141},{"title":"Help","target":"https://saliu.com/Help.htm","line":141},{"title":"Home","target":"https://saliu.com/index.htm","line":141},{"title":"Software","target":"https://saliu.com/infodown.html","line":141},{"title":"Search","target":"https://saliu.com/Search.htm","line":141},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":141},{"title":"Wonder Grid is a theory and strategy in lotto and lottery based on number frequency in recent drawings.","target":"https://saliu.com/forum/HLINE.gif","line":143}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1030243,-0.01331581,-0.05523896,0.0069385,-0.04486013,0.07060632,0.03824557,0.00125536,0.06443731,-0.04293444,-0.00866423,-0.02805826,0.04687618,0.03634805,-0.02580221,-0.01977835,-0.01870679,0.01510768,-0.01763684,0.03681785,0.12262752,-0.09673776,-0.09065286,-0.07269347,0.0580282,0.00287919,-0.03969312,-0.03942982,-0.01928159,-0.20926069,0.01642888,0.0548977,0.01476738,-0.04371478,-0.07232999,-0.05541102,-0.03373918,0.05351533,-0.07700451,0.02909172,0.02379093,0.01887878,-0.01911709,-0.03060947,-0.0206247,-0.02808007,0.00140031,0.00204312,0.02253917,-0.01622694,-0.03361507,0.0114689,-0.02887748,0.036123,0.06992014,0.07541437,0.0691042,0.03299975,0.00401194,0.05592379,0.00436828,0.06361093,-0.21739104,0.04334665,0.014946,0.00412273,0.00757156,-0.00929902,0.01226888,0.02753871,0.05279658,0.05484295,-0.04120328,0.03193053,0.01191782,-0.01281621,-0.05642936,-0.04075646,-0.0400458,0.02450394,-0.04244397,-0.03555626,0.03081091,-0.0288763,0.001915,0.04709438,0.03931349,-0.02455805,0.04537148,-0.05126857,-0.04810212,0.04223625,0.03349853,-0.0024781,0.00274916,0.04052674,0.05900769,-0.04132157,0.0509579,0.12537013,0.01840502,0.00929305,0.01436663,0.07007038,0.02078266,-0.01614882,-0.03219241,-0.04134904,-0.01943003,0.02349059,0.04407016,0.00634998,0.0217458,-0.00466269,-0.01352694,-0.01110781,0.02832715,0.02159843,0.05841776,0.00193787,-0.09583451,0.01867386,0.03193163,-0.00883599,0.02814989,0.01324457,0.03204844,0.0860826,0.0281608,0.03407288,0.00607587,-0.01674124,-0.07332768,-0.06177732,-0.03730146,0.00917841,0.05382159,-0.00414969,-0.01968927,0.04446823,0.01839502,-0.01485709,0.06085739,-0.13136207,0.0166322,0.07752479,-0.0223748,0.02389446,-0.04879921,0.04826724,0.00772803,-0.0086521,-0.00777591,-0.08186259,-0.02099708,0.01021257,0.07838748,0.07370329,-0.00022712,0.02405357,-0.0108014,-0.03291648,-0.04307117,0.11513399,0.00248756,-0.1062125,0.00505812,-0.02528444,-0.05048218,-0.10138216,-0.03157493,0.03196079,-0.0377447,0.00925325,0.07117577,-0.03185381,-0.06856084,-0.07164643,-0.01395252,-0.00364578,-0.01415768,-0.00590009,-0.02846735,0.00265427,0.01808481,-0.06953271,0.0014989,-0.04931027,0.01719624,0.05452813,-0.0279914,-0.01378462,-0.00811604,0.04201107,-0.02269392,-0.03737795,-0.02173649,-0.05949138,0.06857619,-0.02800984,-0.03817318,-0.02001255,-0.02502543,-0.02550806,-0.02507476,0.0507593,-0.00586284,-0.03629751,0.05496791,0.02650042,0.01111502,-0.00485511,0.04435188,0.07179773,-0.05072468,0.03242007,0.00791605,0.01568816,-0.00775114,0.02079374,0.02420033,-0.02041042,-0.08661762,-0.2051539,-0.02661982,-0.0293567,-0.01559196,0.05175915,-0.03556268,0.05594644,-0.02829189,0.02292375,0.08765622,0.06092101,-0.08426556,0.00912897,-0.00131489,-0.04091924,-0.00648763,-0.08206636,-0.0718401,-0.01155026,0.03899656,-0.02632246,0.07126954,-0.04010512,-0.08442948,0.06547441,-0.02886179,0.15209197,0.03663051,0.00100041,0.02004179,0.05599946,-0.0061927,-0.00873156,-0.02744659,0.01643607,0.02399256,-0.00215135,-0.00516982,-0.05432996,0.01205469,-0.11212007,0.03435524,0.00065585,-0.05627402,-0.0128604,0.03488715,0.01390015,-0.018204,-0.03788976,0.02591393,0.05847944,-0.01673258,-0.01266289,-0.00985887,0.04230988,-0.0030866,-0.07431519,0.01375484,0.00171981,0.03659975,-0.00043227,-0.01625684,0.01860006,-0.03011492,0.04643431,0.0066284,0.00434245,-0.00274266,0.01036086,0.02271158,0.00024564,0.09546538,0.02741499,0.0380285,0.02818757,0.04768637,0.05577676,-0.09141251,0.00418139,-0.00317604,0.0150214,-0.06845919,0.02830095,0.07378675,0.0633221,0.00357333,0.06007792,0.0365091,0.00395926,-0.02442644,-0.02985661,0.01245778,0.00162793,-0.01600126,0.04210748,0.02297573,-0.27872729,0.05050154,-0.03968498,0.09656246,-0.06199178,-0.04752271,-0.01296422,0.00105038,-0.00090577,-0.03063451,0.0464999,0.02514913,0.03935033,-0.0291834,-0.04513506,0.00447224,0.03360215,-0.08727452,0.07236857,0.00333862,0.02901768,0.01177409,0.26382291,0.00284787,0.02609236,0.01094751,0.01055996,0.02765258,-0.052439,0.06536382,0.0383024,-0.00726513,0.05134762,-0.00457687,-0.02612601,0.07278142,-0.01491532,0.01733678,0.01702086,0.03187093,-0.08412972,0.01362692,-0.02270929,0.0128346,0.1168597,0.01892192,-0.00836068,-0.05423662,0.00430088,0.05284623,-0.08727694,-0.03710691,-0.05216909,0.01447321,0.01837185,0.01624689,0.03968721,-0.02041994,0.01336828,-0.00894252,0.0107738,0.00409144,0.01411131,-0.03144597,0.00661544],"last_embed":{"hash":"tl911p","tokens":129}}},"text":null,"length":0,"last_read":{"hash":"tl911p","at":1753423486802},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#{1}","lines":[10,15],"size":359,"outlinks":[{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/images/software-lottery-gambling.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#By Ion Saliu, _★ Founder of: Lottery Mathematics, Lotto Programming Science_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10392147,-0.03690843,-0.05822223,-0.02541145,-0.06493807,0.07833726,0.03812644,0.01871748,0.10515151,-0.04364196,-0.02469721,-0.03407146,0.06140219,0.04542287,-0.02840569,0.01194849,-0.04953696,-0.01576604,-0.05577235,0.03413032,0.10650003,-0.09377803,-0.04687808,-0.11144422,0.06134361,-0.00441583,-0.03794361,-0.07266818,-0.02191263,-0.2378841,0.03108995,0.05691181,0.01835694,-0.02300243,-0.08628938,-0.03080567,-0.02197657,0.04174717,-0.03176663,0.02517862,0.02087439,0.00813062,0.02599846,-0.02562521,-0.04141808,-0.02550292,-0.01661558,-0.01895533,0.00997639,0.00721636,-0.04320423,-0.02898767,-0.03129074,0.038509,0.04234115,0.08448827,0.04436203,0.0145834,0.00112557,0.07464977,0.01155682,0.05134599,-0.19852975,0.05235396,0.04260862,-0.02688338,-0.00079926,0.00339442,-0.01267749,0.03322534,0.04264639,0.06191558,-0.01542297,0.00392263,0.02734879,-0.0123626,-0.06447095,-0.04237357,-0.04248124,0.04099644,-0.0611636,0.00829529,0.02020573,-0.03600739,0.02699556,0.0589371,0.05104858,-0.02057796,0.01645304,-0.04431829,-0.00171834,0.04497909,-0.00117729,0.02268873,0.02602277,0.06522173,0.02411654,-0.03210479,-0.00351132,0.11712567,-0.03846805,0.01952166,-0.03508476,0.06689987,0.00865376,-0.03904995,-0.02028305,-0.03671076,-0.00284782,0.010133,0.02700368,-0.01021639,0.04285277,-0.02802497,-0.04211036,0.00097202,0.02271079,0.00657092,0.06178915,-0.00057303,-0.0562899,0.0320976,0.01986923,-0.01369994,0.01960245,0.0360186,0.02635576,0.08211938,0.04419339,0.04408627,0.02680567,-0.01502647,-0.09191569,-0.03002173,-0.06155939,0.01262333,0.04644443,0.00894635,-0.02064758,0.05076374,0.02823211,-0.04456248,0.05897408,-0.08038858,0.01062156,0.06790859,-0.04420292,-0.02132534,0.00826112,0.06126328,0.02916658,-0.01097785,-0.01761162,-0.08275934,-0.00625034,-0.0058661,0.07627699,0.06652055,0.00140069,0.02850343,-0.0292232,-0.04163297,-0.03015317,0.11230767,0.00857012,-0.10014033,-0.01864792,0.00278245,-0.06623902,-0.07584026,0.00701912,0.02366608,-0.02303725,0.04230972,0.05965523,-0.01676825,-0.09268343,-0.04376636,-0.00209456,-0.00919924,-0.00716092,0.01651734,-0.04682868,-0.00416085,-0.03790258,-0.06751328,-0.001704,-0.01981063,-0.03001436,0.04666368,-0.0204041,-0.00564533,-0.02087933,0.0314535,-0.01495389,-0.04886611,-0.02953463,-0.06613661,0.05142235,0.01727415,-0.04878934,-0.0105824,-0.00095378,0.00033725,-0.0203087,0.05015632,-0.00374361,-0.00949749,0.06209528,0.04585586,0.02109174,-0.01153451,-0.00565033,0.0587978,-0.04549376,0.03971588,0.0148064,0.01923992,0.02019341,0.03540014,0.03194868,-0.04331722,-0.07839736,-0.18732515,-0.0380611,-0.04260299,0.0008303,0.04880712,-0.03208567,0.06743947,-0.00917179,0.02369038,0.08163188,0.06357084,-0.05585823,-0.02835061,0.00533004,-0.03558588,0.00801336,-0.07695319,-0.0212807,-0.0319018,0.02963869,-0.0302173,0.09244892,-0.0633495,-0.03206986,0.09099636,-0.04219537,0.17413381,0.0481786,-0.04189102,0.00092688,0.05464763,-0.01182829,-0.01590666,-0.00691624,0.00395334,0.01217909,-0.00946661,-0.04325362,-0.04459442,0.00320922,-0.14889097,-0.00469576,0.00793663,-0.07218966,-0.03559543,0.04209153,-0.01970693,0.01210373,-0.01714532,0.03376944,0.06232306,-0.02019021,0.02554844,0.00244138,0.04551804,-0.02539491,-0.05339404,-0.00988023,0.01059108,0.04542797,0.00264964,0.01619042,0.0318803,-0.01533584,0.00115368,0.03279505,-0.00179977,0.00923094,-0.00924127,-0.01383387,0.00720974,0.04805816,0.01486563,0.0192839,0.02098469,0.01111528,0.04162764,-0.08056503,0.03555293,-0.00163894,0.06341834,-0.07725022,0.0253462,0.04100156,0.05137367,0.00535138,0.09208028,0.09168293,-0.00870388,-0.00946677,-0.01141204,0.03907327,0.01402623,0.00485457,0.03495722,-0.00116522,-0.27963391,0.0393667,-0.05290852,0.09722676,-0.03220951,-0.02757101,0.02376846,0.02782349,-0.01139206,-0.05317762,0.05839704,0.01435374,0.03568184,-0.06269419,-0.03423411,0.01363429,-0.02001518,-0.06133955,0.0350771,-0.00288602,0.07870897,0.0036607,0.24765411,0.01237044,0.00469439,0.01717855,-0.01132508,0.03284938,-0.02010633,0.02358158,-0.00483515,-0.00275295,0.05955258,-0.05165195,-0.00650301,0.05573572,-0.00529304,-0.00276919,0.01957675,0.00283614,-0.02999285,0.00152298,-0.04752482,-0.00858395,0.14808062,0.0058278,-0.02121776,-0.06241269,0.03292492,0.07175881,-0.10082904,-0.05641158,-0.03220792,0.01993382,-0.01655505,0.01934752,0.03019481,0.03028872,-0.00040486,0.02953639,-0.00610751,0.03648048,0.02600939,0.00353824,-0.00534834],"last_embed":{"hash":"1gbmj4a","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"1gbmj4a","at":1753423486849},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#By Ion Saliu, _★ Founder of: Lottery Mathematics, Lotto Programming Science_","lines":[19,125],"size":13774,"outlinks":[{"title":"The Best Pick 4 software ever - Super Pick-4 lotto.","target":"https://saliu.com/forum/HLINE.gif","line":3},{"title":"lotto wonder grid","target":"https://saliu.com/bbs/messages/638.html","line":8},{"title":"_**Brief history of my lottery, lotto experience**_","target":"https://saliu.com/bbs/messages/532.html","line":58},{"title":"_**Lottery Strategy, Systems Based On Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":66}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#By Ion Saliu, _★ Founder of: Lottery Mathematics, Lotto Programming Science_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10595533,-0.03664526,-0.05990159,-0.02570413,-0.06756831,0.07503885,0.0408306,0.01871599,0.11119922,-0.04633407,-0.02249904,-0.02920657,0.06239906,0.04490645,-0.02846864,0.01430851,-0.04851958,-0.01777889,-0.05708015,0.03293429,0.10177924,-0.09561426,-0.04556687,-0.10773151,0.06037156,-0.00279155,-0.03342973,-0.0731709,-0.02085332,-0.2389614,0.02671029,0.05276076,0.0191839,-0.02277031,-0.08753251,-0.02715517,-0.02066989,0.04342223,-0.03011354,0.02430862,0.02047474,0.0076271,0.03059634,-0.02619471,-0.04168645,-0.0235393,-0.01682271,-0.01766644,0.00977494,0.00518923,-0.04336563,-0.02566446,-0.03071088,0.03843458,0.03986579,0.08446755,0.04470784,0.02171117,0.00179306,0.07495994,0.0138927,0.05172744,-0.20020746,0.05061812,0.04198232,-0.02276888,-0.00158918,0.00540164,-0.01121899,0.03351064,0.04462842,0.06300201,-0.01322799,0.00408572,0.0273148,-0.01204552,-0.06316335,-0.042022,-0.04219579,0.04182988,-0.06429484,0.00960141,0.01667546,-0.03589921,0.02655455,0.05606812,0.04651409,-0.02280731,0.01454465,-0.04451403,-0.00055698,0.04557292,-0.0023524,0.01872427,0.0270888,0.06512455,0.02649353,-0.03450526,-0.00297364,0.11410028,-0.03881362,0.02262441,-0.03751039,0.06800943,0.0116785,-0.03848417,-0.02434187,-0.03939617,-0.00174987,0.00836637,0.02857057,-0.00851289,0.04295718,-0.02953323,-0.04313338,0.0034726,0.0208099,0.00696259,0.05999599,0.00052038,-0.05379442,0.03157965,0.01864696,-0.00986071,0.01554812,0.03666401,0.0240259,0.08250213,0.04494024,0.04649998,0.02863288,-0.0136723,-0.09361845,-0.03082997,-0.06231361,0.01193723,0.0418677,0.0070349,-0.01880556,0.05209744,0.02886475,-0.04748955,0.05911617,-0.07871121,0.01195136,0.07110156,-0.0405599,-0.01991423,0.01071921,0.05990597,0.02813297,-0.01500077,-0.02142091,-0.08233322,-0.0071876,-0.00670028,0.07366453,0.06575058,-0.0008225,0.03260911,-0.03124676,-0.04163138,-0.03284048,0.11354131,0.00951053,-0.09498256,-0.01674873,-0.00155794,-0.06689553,-0.07372464,0.0040679,0.02394116,-0.02310605,0.04113516,0.05764013,-0.01603873,-0.09348808,-0.04147026,-0.00059225,-0.00841927,-0.00479229,0.01632981,-0.04916704,0.00140947,-0.03867076,-0.06501287,-0.00313908,-0.01975228,-0.02707602,0.04732104,-0.02102668,-0.00423849,-0.02714237,0.03044605,-0.01329034,-0.05066542,-0.02993402,-0.06541242,0.05148335,0.01845328,-0.05161236,-0.00967836,0.00040149,0.00128845,-0.02169638,0.05003288,-0.00233858,-0.00799293,0.05829355,0.04150485,0.02639232,-0.0141369,-0.01096922,0.05919315,-0.04079752,0.03877741,0.01587111,0.02214228,0.02109533,0.04010595,0.03023946,-0.04163953,-0.07598554,-0.18960236,-0.04330328,-0.04151057,0.0027494,0.04744285,-0.03227088,0.06179199,-0.00921611,0.02023602,0.08169482,0.06461497,-0.05520657,-0.0274346,0.0072297,-0.03584933,0.00808901,-0.07712557,-0.02032536,-0.03384757,0.03305763,-0.03312642,0.08623853,-0.06696532,-0.03033055,0.09169002,-0.04499342,0.17194724,0.04492712,-0.04388886,0.00071045,0.05396928,-0.01218124,-0.01742892,-0.01036875,0.00620411,0.01357686,-0.00745144,-0.04345367,-0.04197399,0.00323104,-0.14770399,-0.00833361,0.00891839,-0.07101794,-0.0359502,0.04260935,-0.02198472,0.01254686,-0.01520577,0.03627775,0.06531498,-0.01943951,0.0283885,0.00340741,0.0503789,-0.02898256,-0.05253426,-0.01122017,0.0093207,0.0477214,0.00152305,0.01646968,0.03003674,-0.01708355,0.00336914,0.03602676,-0.00337198,0.00221934,-0.00998168,-0.0114925,0.00684458,0.04995489,0.01353079,0.01566603,0.01836563,0.00793653,0.03918295,-0.08299396,0.03673377,0.00012195,0.06410167,-0.0826259,0.02584829,0.04214634,0.05223274,0.00647263,0.09309426,0.09127843,-0.00900856,-0.008565,-0.0122038,0.04068929,0.01187583,0.0061799,0.03410589,-0.00076444,-0.27800888,0.04269006,-0.0579219,0.0956051,-0.03214531,-0.02911918,0.02389791,0.02634525,-0.01069979,-0.05839171,0.05851249,0.01465408,0.0356026,-0.05834129,-0.03141905,0.01363951,-0.02031399,-0.06104843,0.0348693,-0.00220767,0.07919051,0.00584703,0.24650159,0.01082327,0.00534759,0.01916758,-0.00690839,0.0323029,-0.02263532,0.02292596,-0.00283029,-0.0033869,0.06137618,-0.05286438,-0.00901467,0.05836654,-0.0063183,-0.00241588,0.01982044,0.00210689,-0.02415656,-0.00392211,-0.04599317,-0.01315337,0.15290479,0.01073147,-0.02386619,-0.05934182,0.03693918,0.06906806,-0.09825464,-0.05720977,-0.02944132,0.02238313,-0.01693842,0.02284923,0.02334147,0.0320042,-0.00027846,0.02816079,-0.0060636,0.03590446,0.02599666,0.00362345,-0.00403753],"last_embed":{"hash":"1pnvoko","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"1pnvoko","at":1753423486998},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#By Ion Saliu, _★ Founder of: Lottery Mathematics, Lotto Programming Science_#{1}","lines":[21,125],"size":13693,"outlinks":[{"title":"The Best Pick 4 software ever - Super Pick-4 lotto.","target":"https://saliu.com/forum/HLINE.gif","line":1},{"title":"lotto wonder grid","target":"https://saliu.com/bbs/messages/638.html","line":6},{"title":"_**Brief history of my lottery, lotto experience**_","target":"https://saliu.com/bbs/messages/532.html","line":56},{"title":"_**Lottery Strategy, Systems Based On Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":64}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09795798,-0.01398328,-0.04096503,-0.0119269,-0.05754738,0.0585524,-0.02646379,-0.00731297,0.05834661,-0.02290831,-0.01815853,-0.03369733,0.07355245,-0.01823333,-0.01021926,-0.01903367,-0.02227952,0.03828317,-0.04531507,0.00141364,0.0777906,-0.05297839,-0.07213241,-0.08959307,0.04273378,0.00087166,-0.05376983,-0.04875505,-0.0144061,-0.2111425,0.0424679,0.03565868,-0.02510345,-0.04906303,-0.06934611,-0.00882327,-0.05443641,0.03679843,-0.07103372,0.01016338,0.03697664,0.01504852,0.03158446,-0.00538294,0.00518338,-0.046774,0.0127451,-0.01089082,0.03553024,-0.00751747,-0.0771198,-0.02388143,0.00984258,0.01503442,0.05665214,0.07031831,0.02948556,0.06158253,0.01533992,0.05532316,0.01233867,0.07428066,-0.21369787,0.06633397,0.02876289,0.00242426,0.01389401,0.00625371,0.0188003,0.03484848,0.01909952,0.043041,0.00149713,0.03985484,0.03027544,0.00278924,-0.08755478,-0.06459536,-0.06720803,0.00199934,-0.05392101,0.01387808,0.0355015,0.01131466,-0.01782002,0.07917954,0.05866122,0.01126732,0.07201117,-0.05608713,-0.00656813,0.04640854,0.04134168,0.03024961,0.00400476,0.015024,0.04890577,-0.02203902,0.00672894,0.12315137,-0.01035574,0.00148651,0.03211965,0.02196677,0.0155574,-0.06285437,-0.03421962,-0.02523841,-0.01934187,0.0313153,0.06384284,-0.00472297,-0.00715196,-0.04865054,-0.0456847,-0.0313268,-0.01092608,-0.00131721,0.02789401,-0.0078998,-0.07358294,-0.0007711,0.02372345,0.01007168,0.03232409,0.01769918,0.02999333,0.06641942,0.02467916,0.02194104,0.04048698,0.02664652,-0.11124349,-0.04377083,-0.03246007,-0.02097055,0.03841755,-0.02074919,0.00596378,0.04921963,-0.01165643,-0.04279241,0.0437864,-0.11567084,-0.01135761,0.0869097,-0.00424258,-0.0073086,-0.01001556,0.02004384,0.0201335,-0.01929054,-0.01115827,-0.08695655,0.02774101,0.02140742,0.11811261,0.07188852,0.00281343,0.01326326,-0.02101448,-0.02043545,-0.04647281,0.15004203,-0.00010403,-0.13157813,-0.00297177,0.03170633,-0.0488239,-0.0904543,0.00825998,0.03220245,-0.04073578,0.02083856,0.10480945,-0.02541878,-0.08162802,-0.02610386,-0.00977591,-0.01669773,-0.00245489,-0.02132896,-0.0252494,0.00529431,-0.01326331,-0.08556939,-0.01800503,-0.01354592,-0.01589876,0.06169034,-0.01117541,-0.02758664,-0.0037711,0.03347742,-0.04906014,-0.03402836,-0.05616815,-0.07143379,0.07026229,-0.01470293,-0.00270136,-0.00479569,-0.00719351,0.00395843,-0.00654863,0.04931955,-0.02081492,-0.03100289,0.07957278,0.05545565,-0.0227099,0.007891,0.03992298,0.04635682,-0.05356169,0.05561603,0.01169616,0.01796602,-0.01927236,0.04955273,-0.00267389,0.005749,-0.0834781,-0.18932454,-0.03793192,-0.04347194,-0.00755055,0.00346028,-0.03472001,0.06658213,-0.01720871,0.01523032,0.08353699,0.08698779,-0.0620277,-0.00138379,0.02233944,-0.03006037,-0.0140274,-0.0592788,-0.03471181,-0.03623745,0.03680124,0.02632696,0.05153156,-0.0237898,-0.07288253,0.04615739,-0.0275458,0.1256225,-0.00504275,0.01765973,-0.00373389,0.08037049,-0.00532173,0.01070908,-0.04540032,0.00330883,0.0534379,-0.03019613,-0.02370911,-0.03373699,0.01579171,-0.12348072,-0.00027306,0.02033365,-0.07760076,-0.02451756,0.0150061,0.02022978,0.00039185,-0.01895873,0.02965351,0.02075965,0.0136425,0.01842291,0.0063775,0.03661212,0.00070916,-0.0568248,0.00831078,-0.03272056,0.03099601,-0.0349217,-0.04714065,0.03629176,-0.01645176,0.03171222,0.01493408,0.00128396,-0.00459334,0.0042888,0.01941697,0.00539882,0.08089124,-0.00500923,0.07576405,-0.0149368,0.03532656,0.05364947,-0.0733638,0.01083879,-0.00955222,-0.04587716,-0.04721221,0.05018952,0.0780163,0.07371905,0.03576499,0.08331784,0.04947579,0.01002875,-0.02351472,-0.01550205,-0.0096898,0.02545055,0.009685,0.0479022,0.04390615,-0.28213078,0.02567661,-0.03958754,0.07143256,-0.04975814,-0.03474085,0.00960638,-0.01719824,0.02503018,-0.04557332,0.0600349,-0.00854001,-0.00658345,-0.05955225,-0.00115049,0.00058595,0.03629658,-0.05212887,0.0560082,0.00020341,0.03589457,0.01814294,0.24784827,0.03464831,0.02190444,0.02506734,0.00395056,0.03321017,-0.01199265,0.04004238,0.02094082,0.03104082,0.06265868,-0.03537443,-0.03048558,0.05484002,-0.03242059,0.01488481,0.0193416,0.0310998,-0.06720864,0.01989218,-0.03200375,0.01215624,0.1157415,0.03390765,-0.00809088,-0.09682241,0.03804495,0.06947573,-0.09819767,-0.02977134,-0.08523887,0.00943247,-0.00534912,0.01632543,0.0374774,-0.01421481,0.02489898,-0.01282481,0.03488687,-0.02935426,0.047351,-0.00214811,-0.02087541],"last_embed":{"hash":"1q41277","tokens":416}}},"text":null,"length":0,"last_read":{"hash":"1q41277","at":1753423487159},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)","lines":[126,151],"size":3090,"outlinks":[{"title":"<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":5},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":7},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":9},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":10},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":11},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":13},{"title":"_**Lottery lotto filtering in software**_","target":"https://saliu.com/filters.html","line":14},{"title":"**<u>Lotto wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":15},{"title":"_**Updates to the Bright lotto, lottery, horse racing software**_","target":"https://saliu.com/forum/software-updates.html","line":17},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":19},{"title":"The lottery wonder-grid shows improvement in odds compared to playing lotto numbers randomly.","target":"https://saliu.com/forum/HLINE.gif","line":21},{"title":"Forums","target":"https://forums.saliu.com/","line":23},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":23},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":23},{"title":"Contents","target":"https://saliu.com/content/index.html","line":23},{"title":"Help","target":"https://saliu.com/Help.htm","line":23},{"title":"Home","target":"https://saliu.com/index.htm","line":23},{"title":"Software","target":"https://saliu.com/infodown.html","line":23},{"title":"Search","target":"https://saliu.com/Search.htm","line":23},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":23},{"title":"Wonder Grid is a theory and strategy in lotto and lottery based on number frequency in recent drawings.","target":"https://saliu.com/forum/HLINE.gif","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08270604,-0.04383354,-0.03856886,-0.0019631,-0.06553806,0.06205889,-0.00179959,-0.01540322,0.06053881,-0.01275522,-0.01719148,-0.02632007,0.06256127,0.0075888,-0.00238563,-0.00359076,-0.00757159,0.06258333,-0.04153135,0.01121894,0.09101762,-0.04834783,-0.09709461,-0.09102728,0.02831619,0.01571536,-0.05239033,-0.06574697,0.0078716,-0.16948996,0.03400708,0.03907719,-0.04550272,-0.0465634,-0.06543466,-0.04681121,-0.05263747,0.0365466,-0.10468353,0.02573681,0.03659296,0.03192965,0.00392274,-0.01376663,-0.01278368,-0.04738837,0.02857017,0.00383471,0.05456663,0.00258799,-0.03905379,0.00112767,-0.00846331,0.03317775,0.03394595,0.06325815,0.03628031,0.05880217,-0.01990171,0.04132981,-0.00081716,0.0564976,-0.18165651,0.05667666,0.01665481,0.01896294,0.03168261,0.0101203,0.02675454,0.04946077,0.04233695,0.04868766,0.00003251,0.05208431,0.03306733,0.00115514,-0.06186017,-0.0664566,-0.0483183,0.01572789,-0.05627189,-0.015032,0.02478295,0.02520102,-0.02623888,0.07495273,0.05853193,0.01695999,0.08394819,-0.07633693,-0.0274334,0.03861174,0.04605842,0.04016844,0.01237942,0.00890464,0.03783514,-0.02181723,0.03899779,0.14990443,-0.02780944,-0.00147289,0.02922218,0.0148759,0.01326365,-0.04412141,-0.04346707,-0.02962513,-0.0464395,0.0606287,0.08032813,0.01036387,0.02392157,-0.03632753,-0.03489224,-0.04149472,-0.01941661,0.01750197,0.03019434,-0.00718006,-0.08497048,0.01933792,0.0124119,-0.00100995,0.0307497,0.01228678,0.01764178,0.07773609,0.00891511,0.03285678,0.03671702,-0.01324004,-0.10210866,-0.03506462,-0.02011269,-0.02916647,0.04375581,-0.00834334,-0.02476853,0.06925359,-0.01007165,-0.01931556,0.06799596,-0.13288309,-0.00055811,0.08060788,-0.01638744,0.00950349,-0.02061126,0.01848505,-0.0028917,-0.0105846,-0.00462693,-0.10221805,0.01423249,0.01526146,0.12254769,0.03300649,-0.00259036,0.04343975,-0.02719379,-0.02024303,-0.0341845,0.15062718,-0.01345537,-0.10611675,-0.01674438,0.04252346,-0.03970229,-0.08551838,-0.01835074,0.03919098,-0.04472343,-0.00455215,0.12680586,-0.02379568,-0.11126531,-0.03017165,-0.0270743,-0.02378464,0.00110343,-0.01728861,-0.01163575,-0.00882608,-0.00625697,-0.0681553,-0.01542591,-0.03538442,-0.00271139,0.06403314,-0.02699267,-0.00386808,0.00389223,0.00314384,-0.03987992,-0.02890948,-0.06476253,-0.06329245,0.07890061,-0.00908153,-0.02616779,0.00220038,-0.00220479,0.00938106,-0.01488159,0.04633972,-0.0162772,-0.04413035,0.07138261,0.07212711,-0.03293482,-0.00350598,0.04387717,0.04937962,-0.08029091,0.04129747,0.01708013,0.031266,-0.00982775,0.0219436,-0.00979758,0.00758189,-0.09741922,-0.17884484,-0.04276915,-0.03274896,0.00421259,0.02608689,-0.03163864,0.02905196,-0.01685815,0.02217851,0.08275299,0.08984677,-0.07224725,0.01238609,0.03124905,-0.02429198,-0.01135593,-0.05088063,-0.04175329,-0.01389826,0.0214093,0.01468199,0.04685144,0.00191156,-0.11263867,0.05005899,-0.0190782,0.13412182,-0.008659,0.02914389,-0.00256872,0.05471646,-0.027004,-0.00452018,-0.02331629,0.00741152,0.05316065,-0.04063577,-0.03587439,-0.02632121,0.0083564,-0.09910046,-0.02784676,0.02226051,-0.07931465,0.00173777,0.00577934,-0.00486394,-0.00540924,-0.01859263,0.03628614,0.02585557,0.01235915,0.02141699,0.00689811,0.04034121,0.00807623,-0.06613749,0.00653254,-0.01813995,0.03282644,-0.0125925,-0.0411807,0.01922436,-0.01185209,0.03486699,0.02235684,-0.01434391,-0.02248247,-0.0129794,-0.00905217,0.01242179,0.0776367,0.00277052,0.0743114,-0.01452267,0.0319846,0.05295049,-0.07446944,0.01082189,0.0113498,-0.02905796,-0.04155927,0.03970923,0.09346266,0.0662996,0.02829849,0.06965014,0.06010643,-0.02215162,-0.02475058,-0.00531786,0.01952243,-0.00048251,-0.01444201,0.04714646,0.02449675,-0.26600912,0.03139052,-0.03855563,0.0659731,-0.0533972,-0.05258449,0.00262217,0.0143234,0.01860633,-0.04847686,0.06939643,-0.00724944,-0.01128176,-0.05451929,0.01496551,0.02587585,0.04003629,-0.05802689,0.06840464,0.00159312,0.03953907,0.03191967,0.25979099,0.03379204,0.04784131,0.01651527,-0.01036304,0.02727697,-0.03923884,0.04390285,0.02824825,0.01729323,0.04530181,-0.03062084,-0.02748462,0.06876138,-0.01443591,0.01427753,-0.00569642,0.03090132,-0.07918054,0.00905016,-0.03410078,0.04645347,0.09978393,0.01165734,-0.01371269,-0.09771337,0.02417702,0.0581857,-0.10524407,-0.01376233,-0.07987598,-0.00687579,0.01415843,0.01399821,0.0440453,-0.02858182,0.02197944,-0.02398234,0.01343068,-0.02808924,0.03257951,-0.02318346,-0.02919606],"last_embed":{"hash":"1m22jsz","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423487271},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{2}","lines":[129,129],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07162969,-0.00445342,-0.03300628,-0.03692304,-0.03649199,0.06396528,0.01123238,-0.00164722,0.07338788,-0.03821256,-0.00622521,-0.0290111,0.08162274,-0.01617359,-0.02924315,-0.01895435,-0.03336228,0.04710266,-0.04047761,-0.0109585,0.08288856,-0.03119831,-0.08091862,-0.08803093,0.04570389,-0.0087682,-0.07108307,-0.06925561,-0.02723442,-0.19791006,0.05248817,0.04293914,-0.03000281,-0.03296531,-0.05248392,-0.01481426,-0.03271848,0.015582,-0.03412682,0.01388863,0.04604662,0.00686291,0.00134677,0.01372249,-0.00703338,-0.03768339,-0.00364386,-0.00587084,0.00876372,0.00502154,-0.08241878,-0.01940328,0.03202938,0.02183852,0.02312531,0.07202438,0.02403262,0.03850336,0.01961331,0.08229681,0.02313228,0.04911917,-0.20726185,0.04810632,0.06659656,-0.00586848,0.01154977,-0.01208913,0.00270428,0.02465122,0.01211618,0.03305304,-0.00513979,0.04801937,0.05487167,-0.01008661,-0.09895319,-0.07223906,-0.06794481,0.02087302,-0.03981744,0.02269833,0.03349075,0.01672998,-0.03491227,0.06819741,0.07099959,-0.00088871,0.09215476,-0.05206053,-0.00695422,0.05691695,0.02353223,0.03658819,0.00595778,0.00441579,0.03195254,-0.04070971,0.05243521,0.15088654,-0.0452759,-0.01097779,0.02408366,0.02144588,-0.00364866,-0.04785622,-0.03281337,-0.01045466,-0.01108341,0.03030553,0.04948214,0.00787116,-0.0226943,-0.04810521,-0.04808115,-0.03479302,-0.01870244,-0.01103124,0.02179501,0.00685982,-0.07327109,-0.02028864,0.02339363,0.00650197,0.03038806,0.00620431,0.06448252,0.07734721,0.0017506,0.01814212,0.04202104,-0.0095509,-0.1103104,-0.03388369,-0.0322824,0.00813869,0.04330181,-0.01365032,0.00828709,0.0642462,-0.01008116,-0.01640337,0.0532617,-0.11252114,-0.01008628,0.11054365,-0.01176597,-0.01439533,-0.02167248,0.01086532,0.02892914,-0.01842908,-0.00366323,-0.07368412,0.02716798,0.01962032,0.11620736,0.06122174,0.02246739,0.02678345,-0.0152769,-0.04185061,-0.03846345,0.16090907,-0.00891527,-0.114972,0.00142332,0.01465686,-0.04761494,-0.09504978,0.03572996,0.02085681,-0.04440412,0.01181054,0.05875616,-0.01165685,-0.06763141,-0.02652924,-0.01456976,-0.02516699,0.0058819,-0.0197184,-0.02918602,0.01879875,-0.02510861,-0.07208536,-0.02128237,0.00323033,-0.03496795,0.06408079,-0.01776884,-0.02512413,-0.0213784,0.0282284,-0.01441276,-0.04084111,-0.03893183,-0.06253371,0.07718071,-0.05383036,-0.0121928,-0.00747004,-0.00572283,0.0154907,0.03912373,0.05291266,-0.05687873,-0.00956488,0.060279,0.05644806,-0.02591185,0.00445493,0.02379485,0.03195179,-0.0480781,0.04201914,0.02013394,-0.00465873,0.01738717,0.05134648,0.00373725,0.00827765,-0.05704423,-0.19770035,-0.04041277,-0.02726546,-0.00548538,0.00641287,-0.03983094,0.0487363,-0.01006373,0.00635256,0.09811089,0.07054579,-0.03701485,0.00941489,0.00604977,-0.02072297,-0.00184605,-0.06063814,-0.04669897,-0.06101902,0.04917377,0.00202842,0.05896273,-0.04562609,-0.07109089,0.05764515,-0.02342282,0.11568142,-0.01736895,0.01303054,-0.00906853,0.07162642,-0.02310491,0.01961346,-0.04390381,-0.0043353,0.05920782,-0.02589931,-0.01819479,-0.0345254,0.01859685,-0.15063301,0.02051862,0.02453489,-0.07619204,0.00956243,0.03537493,0.05918986,-0.04679249,-0.02207904,0.0067719,0.01776916,-0.02503796,0.00856763,0.03430323,0.06015511,-0.00517885,-0.04910178,0.03518979,-0.01748464,0.01420956,-0.05952369,-0.05812305,0.05607003,-0.00837195,0.02481965,0.0222566,-0.00559322,-0.00249456,-0.01172604,0.00931025,0.00876433,0.08651514,-0.01111522,0.05330963,-0.0329451,0.05637741,0.0503865,-0.07261139,0.01104475,0.02105768,-0.0023744,-0.05916499,0.04240318,0.05000497,0.05623061,0.02882594,0.07500693,0.03288381,0.00509471,-0.0090822,0.00350033,-0.01224488,0.0221565,0.00135936,0.04842242,0.02735924,-0.25641066,0.03213728,-0.0506508,0.06972987,-0.04576369,-0.0352907,0.00035337,-0.0028002,0.02681792,-0.03014401,0.06976525,-0.01568356,-0.00665399,-0.07280221,-0.0118555,0.01818882,0.07807801,-0.06883949,0.04336564,0.00463924,0.03598817,0.0200803,0.26464689,0.04111388,0.02690084,0.01964173,-0.0111253,0.02053463,-0.03618954,0.03739947,0.04214475,0.00398197,0.05412333,-0.04507996,-0.04334298,0.03957039,-0.02573675,0.04226558,0.03965539,0.03050791,-0.05888199,0.01174164,-0.05384069,-0.02136742,0.10481792,0.04295009,-0.01296284,-0.09699608,0.01519886,0.04686356,-0.08081507,-0.03999312,-0.09903776,0.00422583,0.00288028,0.00376742,0.02106912,-0.00424778,0.04352377,-0.00905547,0.05098286,-0.01302885,0.020316,-0.00268365,-0.00899074],"last_embed":{"hash":"1sdoqa9","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"1sdoqa9","at":1753423487304},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{4}","lines":[131,131],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10680609,-0.04662324,-0.02329775,0.03090298,-0.0321423,0.09607568,-0.01631635,0.03156259,0.07116506,-0.02677066,-0.03634895,-0.03585459,0.06150069,0.04616405,-0.00293217,-0.01403258,-0.03978943,0.01143947,-0.05719011,0.03492156,0.07598439,-0.0486709,-0.02601883,-0.08844361,0.04813534,-0.0379997,-0.0587512,-0.04575104,-0.01235064,-0.22806001,0.02719891,0.01977358,-0.02148048,-0.03320427,-0.09065872,-0.01841615,-0.04098859,0.06332632,-0.04160786,0.03662969,0.04579545,0.01292346,-0.01627394,-0.02210463,-0.03946197,-0.0460772,0.01582547,-0.03197542,0.00680787,-0.00767224,-0.07624302,-0.01465631,-0.01232224,0.03434496,0.06504817,0.07064966,0.04130233,0.04541001,-0.00707376,0.07618231,0.01189561,0.0415346,-0.22596514,0.07332695,0.02924638,-0.03649997,-0.00258236,-0.00403085,0.00287761,0.03649102,0.03717434,0.05391132,-0.00095039,0.02678823,0.02865917,-0.0137113,-0.07442363,-0.05936827,-0.04193674,0.00878552,-0.05860477,-0.01896171,-0.00426786,-0.04029282,0.03270537,0.03407351,0.04558918,0.02086222,0.04945136,-0.04912461,0.00120057,0.05090426,-0.0033832,0.04447375,0.01387544,0.05153767,0.0159927,-0.02847593,0.00401523,0.13102643,-0.01178296,0.00719488,0.01454214,0.06424743,0.05574337,-0.03407065,-0.03212197,-0.00005747,-0.0068761,0.02360078,0.08244648,0.00702209,0.00122261,-0.01333792,-0.05681668,-0.02223894,0.03432913,0.02588103,0.03230716,0.00234014,-0.11221072,0.02671922,0.03601867,-0.02809626,0.04440577,0.02138227,0.03547708,0.06863689,0.03353152,0.04195509,0.01823823,-0.0188039,-0.08546419,-0.04040178,-0.04602462,-0.01169177,0.01078558,-0.00498287,-0.01367028,0.04921841,0.01639643,-0.04123612,0.02955709,-0.11611626,-0.0203222,0.07378993,-0.03583153,0.01508771,0.00621824,0.01221597,0.00527228,-0.00205242,0.00975498,-0.09280887,-0.01486334,0.00579951,0.0927873,0.07783805,0.01837399,0.03826269,-0.00356147,-0.02249462,-0.02069841,0.12528957,0.00199296,-0.07148239,-0.03410895,-0.0152911,-0.03790133,-0.09675527,-0.00160561,0.037367,-0.02059931,0.00599696,0.07435808,-0.03207133,-0.0900845,-0.04566081,-0.01035589,0.00484481,-0.00901278,0.01946002,-0.01240376,0.02048414,-0.02406448,-0.07186773,0.01022831,-0.00976076,-0.00987336,0.05897829,-0.04175743,-0.0656165,-0.0052514,0.03486182,0.01013755,-0.05201677,-0.04280066,-0.08238097,0.05872053,0.00303259,-0.05830164,-0.01410469,0.0036495,-0.01764601,-0.04302268,0.05710278,-0.0210472,-0.03890187,0.06735621,0.04926807,-0.00668039,-0.00947944,0.05157411,0.04900669,-0.05105237,0.04940631,0.01753936,-0.00238942,0.03266346,0.02796858,0.01799541,0.0032993,-0.07442582,-0.1882274,-0.03442013,-0.05445142,-0.02995405,0.03327868,-0.05764616,0.06649939,0.00620236,0.03748851,0.07075572,0.04823117,-0.06925721,0.02079165,0.00126502,-0.00331711,-0.0198926,-0.06597312,-0.03190138,-0.01241185,0.03318444,-0.00627159,0.11186367,-0.00853746,-0.08895915,0.06394045,-0.04600388,0.12398082,0.02429453,0.00909923,-0.02396125,0.0477945,0.01627512,0.00063931,-0.02384775,0.05117331,0.04706459,-0.04232377,-0.02314326,-0.07650553,-0.00556385,-0.12131166,0.0152602,0.00515153,-0.06959909,-0.03260149,0.03091339,0.001511,0.00268176,-0.03737664,0.01685448,0.04645547,0.00159853,0.0080002,-0.00888889,0.05495505,0.01282465,-0.08307118,0.0138821,-0.00414962,0.031064,-0.02299291,-0.0031223,0.0509402,-0.03159406,0.03138866,0.01544425,-0.0037755,-0.02703083,-0.01793187,0.0361393,0.00602443,0.06836022,0.00052519,0.07497354,-0.00383033,0.02854426,0.00268731,-0.05460646,0.00961515,0.00806071,0.0091461,-0.04528876,0.03797291,0.04861517,0.05901521,-0.00640853,0.05465096,0.03850507,-0.00282317,-0.00707932,-0.01903491,0.01388342,0.01100352,0.0013072,0.05626604,0.02795342,-0.26165408,0.02757291,-0.04202478,0.05587338,-0.05936994,-0.00858143,0.01375449,0.01482864,0.02387811,-0.05794037,0.07196382,0.01281786,-0.00759478,-0.05501904,-0.04485082,0.02655041,-0.00271799,-0.06993252,0.06067039,-0.01833693,0.05960703,0.03012452,0.26998127,0.04123958,0.00012757,0.00525464,0.03475407,0.0199726,-0.01965653,0.01963406,0.00015926,0.01692372,0.06136683,-0.03123951,-0.02543227,0.08599243,-0.03755103,-0.00371854,0.01265396,0.00119854,-0.05323419,0.03081285,-0.02691247,0.04454414,0.13685711,0.02392272,0.03473844,-0.06395283,0.03725767,0.06074921,-0.10241435,-0.05473008,-0.04631593,0.02531809,0.00159111,0.00570138,0.02664137,-0.03367502,0.03092526,-0.01052046,0.02698483,0.00231077,0.03230391,-0.03645012,-0.00648583],"last_embed":{"hash":"178fyac","tokens":331}}},"text":null,"length":0,"last_read":{"hash":"178fyac","at":1753423487340},"key":"notes/saliu/Lottery Pairs, Lotto Frequency, Lotto Wonder Grid.md#Lottery Pairs, Lotto Frequency, Lotto Wonder Grid#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{18}","lines":[146,151],"size":695,"outlinks":[{"title":"The lottery wonder-grid shows improvement in odds compared to playing lotto numbers randomly.","target":"https://saliu.com/forum/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Help","target":"https://saliu.com/Help.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Software","target":"https://saliu.com/infodown.html","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Wonder Grid is a theory and strategy in lotto and lottery based on number frequency in recent drawings.","target":"https://saliu.com/forum/HLINE.gif","line":5}],"class_name":"SmartBlock"},
