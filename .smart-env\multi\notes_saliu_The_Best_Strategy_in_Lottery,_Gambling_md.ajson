
"smart_sources:notes/saliu/The Best Strategy in Lottery, Gambling.md": {"path":"notes/saliu/The Best Strategy in Lottery, Gambling.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"sddx6v","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363612147,"size":32489,"at":1753423416500,"hash":"sddx6v"},"blocks":{"#---frontmatter---":[1,6],"#The Best Strategy in Lottery, Gambling":[8,196],"#The Best Strategy in Lottery, Gambling#{1}":[10,15],"#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)":[16,24],"#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)#{1}":[17,24],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>":[25,48],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{1}":[27,27],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{2}":[28,28],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{3}":[29,29],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{4}":[30,31],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{5}":[32,43],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._":[44,48],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._#{1}":[45,46],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._#{2}":[47,48],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>":[49,81],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{1}":[51,64],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{2}":[65,66],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{3}":[67,67],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{4}":[68,68],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{5}":[69,69],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{6}":[70,70],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{7}":[71,71],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{8}":[72,72],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{9}":[73,73],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{10}":[74,75],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{11}":[76,81],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>":[82,94],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{1}":[84,87],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{2}":[88,88],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{3}":[89,90],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{4}":[91,94],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>":[95,114],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{1}":[97,102],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{2}":[103,104],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{3}":[105,114],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>":[115,165],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{1}":[117,130],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{2}":[131,131],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{3}":[132,132],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{4}":[133,133],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{5}":[134,134],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{6}":[135,135],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{7}":[136,137],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{8}":[138,139],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{9}":[140,140],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{10}":[141,141],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{11}":[142,143],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{12}":[144,145],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{13}":[146,146],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{14}":[147,147],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{15}":[148,148],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{16}":[149,149],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{17}":[150,150],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{18}":[151,151],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{19}":[152,152],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{20}":[153,153],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{21}":[154,154],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{22}":[155,155],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{23}":[156,156],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{24}":[157,157],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{25}":[158,158],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{26}":[159,159],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{27}":[160,160],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{28}":[161,163],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{29}":[164,165],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>":[166,196],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{1}":[168,188],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{2}":[189,190],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{3}":[191,196]},"outlinks":[{"title":"Ion Saliu's theory, software in lottery, gambling is the only honest, no-nonsense strategy.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics","target":"https://saliu.com/strategy-gambling-lottery.html#mathematics","line":16},{"title":"The Best Strategy in Casino Gambling, Blackjack, Roulette","target":"https://saliu.com/strategy-gambling-lottery.html#gambling","line":17},{"title":"The Best Strategy in Sports Betting, Football, Soccer Pools","target":"https://saliu.com/strategy-gambling-lottery.html#sports","line":18},{"title":"The Best Strategy in Horse Racing, Longshot Trifectas","target":"https://saliu.com/strategy-gambling-lottery.html#horses","line":19},{"title":"The Best Lottery Strategy, Unusual Filter Settings","target":"https://saliu.com/strategy-gambling-lottery.html#lottery","line":20},{"title":"Conclusion","target":"https://saliu.com/strategy-gambling-lottery.html#conclusion","line":21},{"title":"The degree of certainty DC can be viewed as probability of a probability.","target":"https://saliu.com/HLINE.gif","line":23},{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":28},{"title":"_**lack of mathematical foundation of counting cards at blackjack**_","target":"https://saliu.com/bbs/messages/274.html","line":36},{"title":"**<u>Mathematics of <i>Fundamental Formula of Gambling</i>, Logarithms, God</u>**","target":"https://saliu.com/formula.htm","line":40},{"title":"**<u><i>Fundamental Formula of Gambling</i>: Theory of Probability, Mathematics, Degree of Certainty, Chance</u>**","target":"https://saliu.com/Saliu2.htm","line":40},{"title":"Run the best software to calculate probability, degree of certainty, trials, chance, win, loss.","target":"https://saliu.com/ScreenImgs/FFG1.jpg","line":42},{"title":"Casino gambling is the best application of fundamental formula, winning streaks, losing streaks.","target":"https://saliu.com/HLINE.gif","line":47},{"title":"<u><i><b>gambler's fallacy</b></i></u>","target":"https://saliu.com/gamblers-fallacy.html","line":51},{"title":"The gambler's fallacy is disproved by gambling software as events fall within one standard deviation from norm.","target":"https://saliu.com/ScreenImgs/gambler-fallacy.gif","line":59},{"title":"<u><i><b>chairman showed hostile reaction</b></i></u>","target":"https://saliu.com/bbs/messages/588.html","line":61},{"title":"<u><i><b>I was banned by a casino pit boss</b></i></u>","target":"https://saliu.com/winning.html","line":63},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":65},{"title":"<u><i><b>Blackjack Streaks, Software, Probability of Consecutive Losses / Wins, Stats</b></i></u>","target":"https://saliu.com/gambling-lottery-lotto/blackjack-report.htm","line":66},{"title":"<u><i><b>baccarat</b></i></u>","target":"https://saliu.com/winning_bell.html","line":67},{"title":"_**The Best-Ever Roulette Strategy, Systems**_","target":"https://saliu.com/best-roulette-systems.html","line":68},{"title":"<u><i><b>Craps Strategy, Systems Based on Fundamental Formula of Gambling</b></i></u>","target":"https://saliu.com/bbs/messages/504.html","line":69},{"title":"_**The Best Blackjack Strategy, System Tested with the Best Blackjack Software**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":70},{"title":"_**blackjack card-counting**_ \"bishops\" or \"gurus\"","target":"https://saliu.com/bbs/messages/511.html","line":73},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/gambling-fights.html","line":73},{"title":"The best probability software calculates winning and losing streaks based on mathematical formulae.","target":"https://saliu.com/ScreenImgs/streaks-gambling.gif","line":78},{"title":"The best strategy in sports betting applies records, scores, results and streaks.","target":"https://saliu.com/HLINE.gif","line":80},{"title":"<u><i><b>Software to Generate Full 1X2 Systems for Football Soccer Pools</b></i></u>","target":"https://forums.saliu.com/software-1x2-football-soccer-pools.html","line":88},{"title":"<u><i><b>Sports Prognosticating or Betting for NFL American Football, Excel Sport Bet Spreadsheet</b></i></u>","target":"https://saliu.com/bbs/messages/382.html","line":89},{"title":"The best strategy in horse racing betting plays longshot trifectas for several races.","target":"https://saliu.com/HLINE.gif","line":93},{"title":"_**races at three horse tracks**_","target":"https://saliu.com/horses.html","line":97},{"title":"_**The non-favorites won 169 horse races out of 272 or 62.13% of the time.**_","target":"https://saliu.com/horseracing.html","line":109},{"title":"The best strategies in lottery, lotto are based on wacky filters to reduce many combinations.","target":"https://saliu.com/HLINE.gif","line":113},{"title":"<u><i><b>lottery filters, lottery filtering, reduction: my discovery</b></i></u>","target":"https://saliu.com/bbs/messages/919.html","line":119},{"title":"<u><i><b>12-number lotto wheel</b></i></u>","target":"https://saliu.com/lottowheel.html","line":121},{"title":"<u><i><b>History of Lottery, Experience, First Lotto Software, Systems, Strategies</b></i></u>","target":"https://saliu.com/bbs/messages/532.html","line":123},{"title":"<u><i><b>Lottery Software, Lottery Strategies, Lotto Systems</b></i></u>","target":"https://saliu.com/LottoWin.htm","line":127},{"title":"_**increases chances to hit lotto jackpots sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":132},{"title":"_**Lottery Strategies, Lotto Systems**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":134},{"title":"_**Lottery Strategy, Systems, Software Based on Lotto Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":135},{"title":"_**Euromillions Strategies, Systems, Software, Winning Numbers**_","target":"https://saliu.com/euro_millions.html#software","line":136},{"title":"Playing just one lottery ticket every drawing is a losing, bad lotto strategy.","target":"https://saliu.com/HLINE.gif","line":138},{"title":"_**Formulas, Software to Calculate Lottery, Lotto <u>Odds, Hypergeometric Distribution Probability</u>**_","target":"https://saliu.com/oddslotto.html","line":141},{"title":"The best mathematical lottery strategy plays more tickets less frequently, in fewer drawings.","target":"https://saliu.com/HLINE.gif","line":144},{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":147},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":148},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":149},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":150},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":151},{"title":"<u><i><b>Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software</b></i></u>","target":"https://saliu.com/bbs/messages/42.html","line":152},{"title":"<u><i><b>Step-By-Step Guide to Lotto, Lottery Filters in Software</b></i></u>","target":"https://saliu.com/bbs/messages/569.html","line":153},{"title":"<u><i><b>Pick-3 Lottery Strategy Software, System, Method, Play Pairs</b></i></u>","target":"https://saliu.com/STR30.htm","line":154},{"title":"<u><i><b>Vertical or Positional</b></i><b> Filters in Lottery Software</b></u>","target":"https://saliu.com/bbs/messages/838.html","line":155},{"title":"<u><i><b>MDI Editor Lotto Is the Best Lotto Lottery Software; You Be Judge</b></i></u>","target":"https://saliu.com/bbs/messages/623.html","line":156},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":157},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":158},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":159},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":160},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":161},{"title":"Download the best software for strategies in lotto, gambling, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":164},{"title":"**software**","target":"https://saliu.com/infodown.html","line":189},{"title":"The best winning scientific strategies for gambling, lottery have always been free at Ion Saliu's.","target":"https://saliu.com/HLINE.gif","line":191},{"title":"Forums","target":"https://forums.saliu.com/","line":193},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":193},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":193},{"title":"Contents","target":"https://saliu.com/content/index.html","line":193},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":193},{"title":"Home","target":"https://saliu.com/index.htm","line":193},{"title":"Search","target":"https://saliu.com/Search.htm","line":193},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":193},{"title":"This is the site of truly winning lotteries, gambling, casino, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":195}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["best","strategy","gambling","lottery","mathematics","theory","software","sports betting","horse racing","blackjack","roulette","randomness","winning","real money"],"source":"https://saliu.com/strategy-gambling-lottery.html","author":null}},
"smart_sources:notes/saliu/The Best Strategy in Lottery, Gambling.md": {"path":"notes/saliu/The Best Strategy in Lottery, Gambling.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08742862,-0.02815078,-0.04893943,0.03182393,-0.0162983,0.04381096,0.02127161,0.04199116,0.0649593,-0.0136131,0.01373849,-0.01957001,-0.02682296,0.04459535,-0.03776306,-0.04715157,-0.01860957,-0.00682363,-0.00872295,0.03342013,0.0904818,-0.06037187,-0.04572038,-0.10707159,0.08592556,-0.00441416,-0.02548809,-0.0519714,-0.03489367,-0.20339587,-0.04105102,0.01799861,-0.00662527,-0.06485187,-0.10094209,-0.0330652,-0.00054655,0.04804474,-0.03022182,0.0439988,-0.00582895,0.00151762,-0.01597822,-0.01023236,0.00703252,-0.01122674,0.03088182,0.0260466,0.01675692,-0.00101594,-0.0629236,0.04648903,-0.03399428,-0.01993902,0.08384274,0.03011154,0.03435767,0.09135085,-0.02142859,0.02401031,-0.00227909,0.0684477,-0.20838735,0.06428352,-0.03761182,0.02863427,-0.01141302,-0.01062589,-0.01894522,0.04861323,0.04306574,0.0172471,-0.03107673,0.03988589,0.0660904,-0.0074101,0.0137275,-0.0377755,-0.01606631,0.04180478,-0.02473009,-0.01121614,-0.01093037,-0.02447814,0.00826869,0.05975368,0.00694546,0.00422179,0.06184525,-0.08595464,0.04623788,0.04372657,0.04478962,0.00852248,0.02456632,0.02793538,0.04562705,-0.03220784,0.0085585,0.08072693,-0.0034148,-0.01961355,-0.0247896,0.01556736,0.08466447,-0.02994202,-0.00132149,-0.0479271,0.00851472,0.01417338,0.0045876,-0.0043001,0.08594759,-0.01317125,-0.03491762,0.02280181,0.01718149,-0.03952974,0.03178583,0.00711901,-0.03382166,0.05906543,0.0097703,-0.00151175,-0.03276991,-0.05117014,-0.00696998,0.05592319,-0.02713366,0.01475057,0.02873864,-0.01645574,-0.10980993,-0.03318982,-0.06086687,-0.04811028,-0.00956464,0.00661106,-0.00695687,0.01277879,-0.03901954,-0.04463989,0.0571138,-0.11485367,-0.05163243,0.0454382,0.02864288,0.00062338,-0.00316964,0.00859195,0.01828926,0.00508369,-0.00287346,-0.0374011,-0.0289794,-0.02237132,0.06693514,0.07096677,-0.04204395,-0.01789863,-0.06625587,-0.05225947,-0.0359783,0.1214804,-0.05399277,-0.10779584,-0.03414221,0.0151268,-0.02726834,-0.08756298,-0.02429246,-0.03573979,-0.05207315,0.04869102,0.07174812,-0.01575959,-0.06231862,-0.08121143,-0.00175928,0.03014275,0.02836451,-0.05841044,-0.0586351,0.01837824,-0.00452263,-0.03314417,0.02336655,-0.06367684,0.06580782,0.04337322,-0.05188245,0.02474923,-0.04284159,0.00033446,-0.04932611,-0.00579824,-0.01295318,-0.0504994,0.06061197,0.01083768,-0.10473976,-0.03181861,-0.01311793,-0.00157067,-0.06263451,0.03313188,-0.00283861,-0.07162469,0.07021665,0.01719735,0.00242683,0.01858648,-0.00535323,0.03518028,-0.02998393,0.02743177,0.0270876,0.05042226,-0.05225609,0.024314,0.04055389,-0.00307972,-0.0734181,-0.17952515,-0.07090595,-0.09328891,0.00426421,0.05547897,0.00369838,0.03799642,-0.01398021,0.02527467,0.06651786,0.0652108,-0.05037091,-0.00432004,0.0334959,0.00726367,-0.00816924,-0.09414245,-0.04861746,-0.04815732,0.02276568,-0.01927294,-0.01404835,-0.03360914,-0.06901482,0.01695487,-0.0154745,0.15537271,0.04873715,0.04348223,0.03964408,0.05595207,0.02662837,-0.02279848,-0.06562712,0.02981287,0.00831304,0.0263378,0.00062974,-0.0484383,-0.0043346,-0.01483579,0.05772026,0.0197937,-0.07704495,-0.08746522,0.00819692,-0.01161143,0.04744443,-0.00647703,0.05203048,0.08542595,0.01247232,0.03035291,0.01590218,0.05775189,-0.01479082,-0.067377,-0.01896097,-0.01476584,0.06624714,-0.02655132,-0.01959367,0.04663599,-0.02017078,0.06672787,-0.00755497,0.02009179,-0.01220165,0.02747013,0.01741538,-0.02010505,0.10341346,0.03426151,0.00792061,0.0561333,0.02611733,0.10341553,-0.06794051,0.00363204,0.01130762,0.00383197,-0.09550002,0.09426673,0.05192548,0.03199213,0.02854729,0.07613629,0.02567359,0.00522717,0.00299709,0.01438501,0.00315771,-0.05985423,-0.0251308,0.0256678,0.05777559,-0.25894186,0.0192908,0.00912434,0.06349339,0.00759371,-0.02816672,0.03649668,-0.03799984,-0.01917489,-0.01981569,0.04159897,0.04692664,0.03558321,-0.00821368,-0.01291496,-0.03454081,-0.00246181,-0.077002,0.08785634,0.05568058,0.03174266,0.07516492,0.21608269,-0.03684342,0.01579115,0.00713892,-0.00168324,0.02309927,-0.04325692,0.03344848,0.02276354,0.0544668,0.03737364,0.01418743,-0.07062775,0.05894893,0.00785534,0.03596744,-0.00825195,-0.02060353,-0.0643651,0.01666976,0.02970691,0.05674357,0.10547771,0.01082518,-0.00110608,-0.04590369,0.06342719,0.01926203,-0.10175119,-0.08267327,-0.02661704,-0.0217394,0.0044419,0.04462885,0.05586161,-0.04662787,0.02358178,-0.00053248,0.01485283,0.04114146,0.0702313,0.07141229,0.03244209],"last_embed":{"hash":"sddx6v","tokens":469}}},"last_read":{"hash":"sddx6v","at":1753423637225},"class_name":"SmartSource","last_import":{"mtime":1753363612147,"size":32489,"at":1753423416500,"hash":"sddx6v"},"blocks":{"#---frontmatter---":[1,6],"#The Best Strategy in Lottery, Gambling":[8,196],"#The Best Strategy in Lottery, Gambling#{1}":[10,15],"#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)":[16,24],"#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)#{1}":[17,24],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>":[25,48],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{1}":[27,27],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{2}":[28,28],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{3}":[29,29],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{4}":[30,31],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{5}":[32,43],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._":[44,48],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._#{1}":[45,46],"#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._#{2}":[47,48],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>":[49,81],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{1}":[51,64],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{2}":[65,66],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{3}":[67,67],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{4}":[68,68],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{5}":[69,69],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{6}":[70,70],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{7}":[71,71],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{8}":[72,72],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{9}":[73,73],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{10}":[74,75],"#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{11}":[76,81],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>":[82,94],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{1}":[84,87],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{2}":[88,88],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{3}":[89,90],"#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{4}":[91,94],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>":[95,114],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{1}":[97,102],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{2}":[103,104],"#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{3}":[105,114],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>":[115,165],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{1}":[117,130],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{2}":[131,131],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{3}":[132,132],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{4}":[133,133],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{5}":[134,134],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{6}":[135,135],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{7}":[136,137],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{8}":[138,139],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{9}":[140,140],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{10}":[141,141],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{11}":[142,143],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{12}":[144,145],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{13}":[146,146],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{14}":[147,147],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{15}":[148,148],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{16}":[149,149],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{17}":[150,150],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{18}":[151,151],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{19}":[152,152],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{20}":[153,153],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{21}":[154,154],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{22}":[155,155],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{23}":[156,156],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{24}":[157,157],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{25}":[158,158],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{26}":[159,159],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{27}":[160,160],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{28}":[161,163],"#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{29}":[164,165],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>":[166,196],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{1}":[168,188],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{2}":[189,190],"#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{3}":[191,196]},"outlinks":[{"title":"Ion Saliu's theory, software in lottery, gambling is the only honest, no-nonsense strategy.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics","target":"https://saliu.com/strategy-gambling-lottery.html#mathematics","line":16},{"title":"The Best Strategy in Casino Gambling, Blackjack, Roulette","target":"https://saliu.com/strategy-gambling-lottery.html#gambling","line":17},{"title":"The Best Strategy in Sports Betting, Football, Soccer Pools","target":"https://saliu.com/strategy-gambling-lottery.html#sports","line":18},{"title":"The Best Strategy in Horse Racing, Longshot Trifectas","target":"https://saliu.com/strategy-gambling-lottery.html#horses","line":19},{"title":"The Best Lottery Strategy, Unusual Filter Settings","target":"https://saliu.com/strategy-gambling-lottery.html#lottery","line":20},{"title":"Conclusion","target":"https://saliu.com/strategy-gambling-lottery.html#conclusion","line":21},{"title":"The degree of certainty DC can be viewed as probability of a probability.","target":"https://saliu.com/HLINE.gif","line":23},{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":28},{"title":"_**lack of mathematical foundation of counting cards at blackjack**_","target":"https://saliu.com/bbs/messages/274.html","line":36},{"title":"**<u>Mathematics of <i>Fundamental Formula of Gambling</i>, Logarithms, God</u>**","target":"https://saliu.com/formula.htm","line":40},{"title":"**<u><i>Fundamental Formula of Gambling</i>: Theory of Probability, Mathematics, Degree of Certainty, Chance</u>**","target":"https://saliu.com/Saliu2.htm","line":40},{"title":"Run the best software to calculate probability, degree of certainty, trials, chance, win, loss.","target":"https://saliu.com/ScreenImgs/FFG1.jpg","line":42},{"title":"Casino gambling is the best application of fundamental formula, winning streaks, losing streaks.","target":"https://saliu.com/HLINE.gif","line":47},{"title":"<u><i><b>gambler's fallacy</b></i></u>","target":"https://saliu.com/gamblers-fallacy.html","line":51},{"title":"The gambler's fallacy is disproved by gambling software as events fall within one standard deviation from norm.","target":"https://saliu.com/ScreenImgs/gambler-fallacy.gif","line":59},{"title":"<u><i><b>chairman showed hostile reaction</b></i></u>","target":"https://saliu.com/bbs/messages/588.html","line":61},{"title":"<u><i><b>I was banned by a casino pit boss</b></i></u>","target":"https://saliu.com/winning.html","line":63},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":65},{"title":"<u><i><b>Blackjack Streaks, Software, Probability of Consecutive Losses / Wins, Stats</b></i></u>","target":"https://saliu.com/gambling-lottery-lotto/blackjack-report.htm","line":66},{"title":"<u><i><b>baccarat</b></i></u>","target":"https://saliu.com/winning_bell.html","line":67},{"title":"_**The Best-Ever Roulette Strategy, Systems**_","target":"https://saliu.com/best-roulette-systems.html","line":68},{"title":"<u><i><b>Craps Strategy, Systems Based on Fundamental Formula of Gambling</b></i></u>","target":"https://saliu.com/bbs/messages/504.html","line":69},{"title":"_**The Best Blackjack Strategy, System Tested with the Best Blackjack Software**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":70},{"title":"_**blackjack card-counting**_ \"bishops\" or \"gurus\"","target":"https://saliu.com/bbs/messages/511.html","line":73},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/gambling-fights.html","line":73},{"title":"The best probability software calculates winning and losing streaks based on mathematical formulae.","target":"https://saliu.com/ScreenImgs/streaks-gambling.gif","line":78},{"title":"The best strategy in sports betting applies records, scores, results and streaks.","target":"https://saliu.com/HLINE.gif","line":80},{"title":"<u><i><b>Software to Generate Full 1X2 Systems for Football Soccer Pools</b></i></u>","target":"https://forums.saliu.com/software-1x2-football-soccer-pools.html","line":88},{"title":"<u><i><b>Sports Prognosticating or Betting for NFL American Football, Excel Sport Bet Spreadsheet</b></i></u>","target":"https://saliu.com/bbs/messages/382.html","line":89},{"title":"The best strategy in horse racing betting plays longshot trifectas for several races.","target":"https://saliu.com/HLINE.gif","line":93},{"title":"_**races at three horse tracks**_","target":"https://saliu.com/horses.html","line":97},{"title":"_**The non-favorites won 169 horse races out of 272 or 62.13% of the time.**_","target":"https://saliu.com/horseracing.html","line":109},{"title":"The best strategies in lottery, lotto are based on wacky filters to reduce many combinations.","target":"https://saliu.com/HLINE.gif","line":113},{"title":"<u><i><b>lottery filters, lottery filtering, reduction: my discovery</b></i></u>","target":"https://saliu.com/bbs/messages/919.html","line":119},{"title":"<u><i><b>12-number lotto wheel</b></i></u>","target":"https://saliu.com/lottowheel.html","line":121},{"title":"<u><i><b>History of Lottery, Experience, First Lotto Software, Systems, Strategies</b></i></u>","target":"https://saliu.com/bbs/messages/532.html","line":123},{"title":"<u><i><b>Lottery Software, Lottery Strategies, Lotto Systems</b></i></u>","target":"https://saliu.com/LottoWin.htm","line":127},{"title":"_**increases chances to hit lotto jackpots sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":132},{"title":"_**Lottery Strategies, Lotto Systems**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":134},{"title":"_**Lottery Strategy, Systems, Software Based on Lotto Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":135},{"title":"_**Euromillions Strategies, Systems, Software, Winning Numbers**_","target":"https://saliu.com/euro_millions.html#software","line":136},{"title":"Playing just one lottery ticket every drawing is a losing, bad lotto strategy.","target":"https://saliu.com/HLINE.gif","line":138},{"title":"_**Formulas, Software to Calculate Lottery, Lotto <u>Odds, Hypergeometric Distribution Probability</u>**_","target":"https://saliu.com/oddslotto.html","line":141},{"title":"The best mathematical lottery strategy plays more tickets less frequently, in fewer drawings.","target":"https://saliu.com/HLINE.gif","line":144},{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":147},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":148},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":149},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":150},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":151},{"title":"<u><i><b>Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software</b></i></u>","target":"https://saliu.com/bbs/messages/42.html","line":152},{"title":"<u><i><b>Step-By-Step Guide to Lotto, Lottery Filters in Software</b></i></u>","target":"https://saliu.com/bbs/messages/569.html","line":153},{"title":"<u><i><b>Pick-3 Lottery Strategy Software, System, Method, Play Pairs</b></i></u>","target":"https://saliu.com/STR30.htm","line":154},{"title":"<u><i><b>Vertical or Positional</b></i><b> Filters in Lottery Software</b></u>","target":"https://saliu.com/bbs/messages/838.html","line":155},{"title":"<u><i><b>MDI Editor Lotto Is the Best Lotto Lottery Software; You Be Judge</b></i></u>","target":"https://saliu.com/bbs/messages/623.html","line":156},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":157},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":158},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":159},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":160},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":161},{"title":"Download the best software for strategies in lotto, gambling, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":164},{"title":"**software**","target":"https://saliu.com/infodown.html","line":189},{"title":"The best winning scientific strategies for gambling, lottery have always been free at Ion Saliu's.","target":"https://saliu.com/HLINE.gif","line":191},{"title":"Forums","target":"https://forums.saliu.com/","line":193},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":193},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":193},{"title":"Contents","target":"https://saliu.com/content/index.html","line":193},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":193},{"title":"Home","target":"https://saliu.com/index.htm","line":193},{"title":"Search","target":"https://saliu.com/Search.htm","line":193},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":193},{"title":"This is the site of truly winning lotteries, gambling, casino, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":195}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["best","strategy","gambling","lottery","mathematics","theory","software","sports betting","horse racing","blackjack","roulette","randomness","winning","real money"],"source":"https://saliu.com/strategy-gambling-lottery.html","author":null}},"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09265037,-0.04781646,-0.04354762,0.03994818,-0.01684833,0.0379139,0.00686666,0.0412812,0.05619438,-0.00112495,-0.00309846,-0.02223355,-0.02621485,0.04298983,-0.02441607,-0.04116014,0.01097742,-0.02138644,-0.0301203,0.03804799,0.06844842,-0.04518564,-0.0829964,-0.09488477,0.08512852,-0.01338254,-0.04334307,-0.04590942,-0.04798859,-0.16279286,-0.02998496,0.00820285,-0.01172865,-0.05870923,-0.08195713,-0.02923969,-0.01388278,0.08165994,-0.01872211,0.06556827,0.00948928,0.03087226,-0.05040236,0.00703061,0.01545213,-0.0073455,0.01880756,0.0172351,0.01529844,0.00262605,-0.05738851,0.03541999,-0.0324453,-0.00667988,0.10618148,-0.00596539,0.04662539,0.06784458,-0.00426491,0.02138277,-0.00563377,0.07007373,-0.20346332,0.04787728,-0.03351086,0.03662071,0.00605073,-0.01618305,-0.03909129,0.05696227,0.06504025,0.01945662,-0.02904575,0.04170569,0.04940487,-0.00358673,-0.00766494,-0.04783222,-0.01215231,0.04542813,-0.02531137,-0.0133449,-0.02177395,-0.05442073,0.01322683,0.03674624,0.00897559,0.04119559,0.07367815,-0.05204017,0.05581281,0.02601609,0.07977328,0.03013722,0.00179923,0.03736221,0.04034543,-0.04319627,-0.01268264,0.10175539,-0.01745468,-0.00571885,-0.01116322,0.00961316,0.0731876,-0.02412782,-0.01548061,-0.04204753,-0.01059892,0.0104224,0.007585,0.01074185,0.12241841,-0.03699147,-0.01391878,0.01683179,0.0179626,-0.03141844,0.0345419,0.02100459,-0.04848735,0.04113717,0.01676266,0.00570295,-0.00527782,-0.02038979,-0.0026684,0.06114659,-0.03235915,0.02494678,0.01547451,-0.00769373,-0.12396785,-0.01651043,-0.0496933,-0.05646181,0.00829381,-0.01220161,0.00983016,-0.01496552,-0.02427867,-0.04553219,0.05405606,-0.14124623,-0.04081669,0.06691509,0.02979615,-0.014485,0.01833596,-0.00143888,-0.01600599,-0.00002747,0.00484955,-0.05077761,-0.02743237,-0.0075007,0.06490996,0.069467,-0.02682567,-0.01434163,-0.04805502,-0.05925395,-0.03556201,0.10837366,-0.05262234,-0.14941132,-0.06142943,0.02078945,-0.0244445,-0.08477978,-0.02710111,-0.03046044,-0.06712414,0.0513075,0.09920824,0.00266674,-0.0179648,-0.07802396,-0.02656019,0.01535263,0.03070445,-0.03373408,-0.08095394,0.0221976,-0.01644136,-0.03373455,0.01428597,-0.05980673,0.07643066,0.05574392,-0.04512373,-0.01499326,-0.05071047,0.01429322,-0.03622458,-0.01146494,0.00193165,-0.04964722,0.05898588,-0.00315463,-0.078306,-0.02147378,-0.03698437,0.00078926,-0.04529886,0.0189724,-0.00119274,-0.09231258,0.06021109,0.02580321,0.0156241,0.01400966,0.03810738,0.0604087,-0.05916004,0.02325908,0.00817312,0.03233797,-0.05072332,0.00617594,0.03596591,-0.00214215,-0.06689043,-0.19977532,-0.04786434,-0.06779917,-0.00948998,0.0419168,0.01646075,0.02626804,0.00340737,0.04695911,0.09368365,0.05273808,-0.05122512,-0.01069124,0.00192494,0.00934999,0.00915383,-0.08019312,-0.06623752,-0.02354692,0.01844907,-0.01758617,-0.01092541,-0.01978956,-0.07775802,0.03938934,-0.01315214,0.15132059,0.08021105,0.01662688,0.00764716,0.0771183,0.03106904,-0.01557222,-0.08148105,0.00706999,-0.00190687,0.01832002,0.02402852,-0.06079061,-0.01939905,-0.0267331,0.05581659,0.02515639,-0.08719481,-0.06255294,-0.01496795,-0.00360051,0.02466162,-0.00366746,0.05792851,0.06600695,0.00732274,0.04512167,0.03357779,0.06125273,-0.00510567,-0.08082998,-0.02827472,-0.00349855,0.0470546,-0.01194428,-0.02263616,0.03288522,-0.03052901,0.02980815,-0.01885464,0.0098991,-0.03568413,-0.00589519,0.01015767,-0.00841766,0.06120177,0.04786746,-0.02497856,0.04026356,0.01938418,0.09751806,-0.05875906,0.01856809,0.00756297,0.00385532,-0.08739623,0.08660073,0.06242729,0.02198957,0.03994936,0.05177484,0.01217849,0.02204745,-0.00232275,0.00839411,0.01282428,-0.06786256,-0.03384928,0.04577782,0.03476425,-0.25892508,0.03935127,0.04242815,0.05884862,0.02201674,0.00155283,0.02027879,-0.01770951,-0.03671991,0.00611526,0.05282554,0.05731506,0.02266795,0.00003944,-0.02602834,-0.02411156,0.00099489,-0.06594695,0.06221806,0.07210233,0.00169107,0.07844212,0.22518502,-0.00798117,0.00228603,-0.00473449,0.00345398,0.01984062,-0.02059421,0.02961328,0.01490618,0.05284643,0.03574314,0.00516576,-0.04476288,0.06064028,-0.00026852,0.04233943,-0.0184338,-0.00116351,-0.08659485,0.02626833,0.01764617,0.04397545,0.10523872,0.02304847,-0.03751286,-0.0613507,0.02382576,0.00202858,-0.08742242,-0.09148683,-0.02374733,-0.00526333,-0.01498915,0.06466174,0.07217272,-0.04332059,0.04315932,0.03091996,0.01869328,0.02160846,0.07212342,0.05995466,0.02789779],"last_embed":{"hash":"e7dlzz","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"e7dlzz","at":1753423633046},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#---frontmatter---","lines":[1,6],"size":260,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08413304,-0.0275968,-0.04617576,0.02220593,-0.02530157,0.04708693,0.04652958,0.03159969,0.06823227,-0.02375064,0.01288069,-0.00943275,-0.0240145,0.04248773,-0.03890598,-0.02813621,-0.01515473,-0.01452047,-0.0214792,0.0296813,0.08212315,-0.05931456,-0.03625206,-0.10594038,0.08639651,-0.01432156,-0.021736,-0.05288742,-0.02803992,-0.20711751,-0.03399852,0.01095803,-0.02230929,-0.07206737,-0.11258996,-0.03159047,-0.0032634,0.04342199,-0.03275936,0.03542393,0.00578605,-0.00130074,-0.00319188,-0.03077575,-0.00300651,-0.00968858,0.03350146,0.02055548,0.01402743,0.01338165,-0.06310254,0.05517113,-0.03531444,-0.02354847,0.07580766,0.03072092,0.04106109,0.1000563,-0.0208301,0.02815955,-0.01583988,0.06633808,-0.19304109,0.06883674,-0.0361482,0.0318374,-0.01423037,0.01151858,-0.02339183,0.0549509,0.05143565,0.02466929,-0.029316,0.04400086,0.07022424,-0.02212538,0.01235918,-0.02543226,-0.00796309,0.05553757,-0.0271823,-0.02106816,-0.00767518,-0.01082968,0.02547354,0.05919708,-0.00492048,0.01382241,0.0632666,-0.09046274,0.04904761,0.05522433,0.01945592,0.00053591,0.04410243,0.02800132,0.04513103,-0.04080127,-0.00002674,0.08154632,0.00172298,-0.02110749,-0.02982996,0.024657,0.08401783,-0.02848833,-0.00756187,-0.05610023,0.00729988,0.01469632,0.01369472,-0.00970902,0.08024739,-0.01914352,-0.04642224,0.02121244,0.01507877,-0.03559713,0.03118592,0.00125156,-0.03384146,0.05287219,-0.00026349,0.00196451,-0.02729512,-0.0553827,-0.01164674,0.0501638,-0.02238996,0.00721009,0.04137151,-0.01702907,-0.10459741,-0.03984018,-0.05777741,-0.03861372,-0.01294947,0.01053472,0.0036627,0.0196631,-0.03176747,-0.04157395,0.0719541,-0.12172654,-0.04417009,0.05053046,0.0226717,0.00374088,-0.01515576,0.01433233,0.01360128,-0.00470258,-0.00903467,-0.03993302,-0.0234235,-0.02870505,0.0615728,0.06936454,-0.04084377,-0.02397539,-0.0624264,-0.0435752,-0.01716279,0.12639885,-0.04740053,-0.08132675,-0.03262656,0.01522975,-0.02864094,-0.08967149,-0.03160604,-0.02393105,-0.05164167,0.04649526,0.069908,-0.0266836,-0.07590119,-0.0733944,-0.00520578,0.02953925,0.02848914,-0.05848328,-0.04373542,0.01168082,0.00096531,-0.04349979,0.03298603,-0.05310062,0.06769423,0.04330788,-0.05621677,0.03221204,-0.03525819,0.00623032,-0.0550689,-0.01110239,-0.01422015,-0.04750935,0.05926997,0.02505191,-0.10753898,-0.03270977,-0.0107305,0.00040692,-0.07124638,0.03702344,0.00080576,-0.07862256,0.07818612,0.01955753,-0.00983695,0.01277851,-0.01185056,0.04594735,-0.0331098,0.01873142,0.02782203,0.05766189,-0.04084862,0.01497913,0.04267958,0.00609499,-0.06542012,-0.17223762,-0.08197315,-0.09485076,0.00888474,0.05662566,-0.00643941,0.03001589,-0.03040305,0.02457157,0.05513953,0.06821727,-0.06140342,0.00990047,0.04456099,-0.00656549,-0.00905584,-0.09595332,-0.04781417,-0.05237779,0.02617908,-0.01962288,-0.01532441,-0.03764007,-0.07498083,0.01447878,-0.00735408,0.15939493,0.03848983,0.05912608,0.04915047,0.04691037,0.02592435,-0.03036689,-0.05686913,0.03892774,0.00929851,0.01743236,-0.00595441,-0.05659818,0.000583,-0.01825801,0.05049261,0.01422186,-0.0734653,-0.09793437,-0.00132836,-0.0243839,0.07072499,-0.0040754,0.04232064,0.07714355,0.00263729,0.03204331,0.00672195,0.06172636,-0.00562586,-0.07629739,-0.00701715,-0.01417827,0.06475491,-0.02649837,-0.01169444,0.05128896,-0.00752795,0.05747849,0.00164404,0.0252762,-0.01382973,0.02627949,0.01515023,-0.02528519,0.10512687,0.01842525,0.00943771,0.05415666,0.03510353,0.09956332,-0.06366047,-0.0020929,0.01192171,0.00469622,-0.09099557,0.07873705,0.04419601,0.02801573,0.01799764,0.0768356,0.0401325,-0.01457718,0.00614308,0.01615717,0.00686802,-0.05783918,-0.02103223,0.00843573,0.03769379,-0.25215286,0.02477664,-0.00899667,0.0598659,0.00803768,-0.03766064,0.0277247,-0.03160388,-0.0176459,-0.01623827,0.04594553,0.03437028,0.03972029,-0.00815985,-0.02286053,-0.03256875,0.00555101,-0.07749792,0.08932626,0.05587814,0.04910988,0.06932101,0.21762763,-0.03749855,0.02138754,0.01111792,0.00344087,0.03305959,-0.05305779,0.0268241,0.03520067,0.04486172,0.02377834,0.00481186,-0.05322549,0.05658827,0.0090277,0.04043424,-0.01301477,-0.01949157,-0.06863234,0.0162195,0.02518153,0.05399929,0.12066041,0.00506751,-0.00475919,-0.03799567,0.0630333,0.03414985,-0.10899893,-0.06914163,-0.03521609,-0.02348489,0.00439007,0.03222306,0.04485735,-0.04795244,0.01645835,0.01139251,0.01996942,0.06402103,0.06977127,0.0690382,0.03425481],"last_embed":{"hash":"u1zhu3","tokens":497}}},"text":null,"length":0,"last_read":{"hash":"u1zhu3","at":1753423633087},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling","lines":[8,196],"size":32184,"outlinks":[{"title":"Ion Saliu's theory, software in lottery, gambling is the only honest, no-nonsense strategy.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics","target":"https://saliu.com/strategy-gambling-lottery.html#mathematics","line":9},{"title":"The Best Strategy in Casino Gambling, Blackjack, Roulette","target":"https://saliu.com/strategy-gambling-lottery.html#gambling","line":10},{"title":"The Best Strategy in Sports Betting, Football, Soccer Pools","target":"https://saliu.com/strategy-gambling-lottery.html#sports","line":11},{"title":"The Best Strategy in Horse Racing, Longshot Trifectas","target":"https://saliu.com/strategy-gambling-lottery.html#horses","line":12},{"title":"The Best Lottery Strategy, Unusual Filter Settings","target":"https://saliu.com/strategy-gambling-lottery.html#lottery","line":13},{"title":"Conclusion","target":"https://saliu.com/strategy-gambling-lottery.html#conclusion","line":14},{"title":"The degree of certainty DC can be viewed as probability of a probability.","target":"https://saliu.com/HLINE.gif","line":16},{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":21},{"title":"_**lack of mathematical foundation of counting cards at blackjack**_","target":"https://saliu.com/bbs/messages/274.html","line":29},{"title":"**<u>Mathematics of <i>Fundamental Formula of Gambling</i>, Logarithms, God</u>**","target":"https://saliu.com/formula.htm","line":33},{"title":"**<u><i>Fundamental Formula of Gambling</i>: Theory of Probability, Mathematics, Degree of Certainty, Chance</u>**","target":"https://saliu.com/Saliu2.htm","line":33},{"title":"Run the best software to calculate probability, degree of certainty, trials, chance, win, loss.","target":"https://saliu.com/ScreenImgs/FFG1.jpg","line":35},{"title":"Casino gambling is the best application of fundamental formula, winning streaks, losing streaks.","target":"https://saliu.com/HLINE.gif","line":40},{"title":"<u><i><b>gambler's fallacy</b></i></u>","target":"https://saliu.com/gamblers-fallacy.html","line":44},{"title":"The gambler's fallacy is disproved by gambling software as events fall within one standard deviation from norm.","target":"https://saliu.com/ScreenImgs/gambler-fallacy.gif","line":52},{"title":"<u><i><b>chairman showed hostile reaction</b></i></u>","target":"https://saliu.com/bbs/messages/588.html","line":54},{"title":"<u><i><b>I was banned by a casino pit boss</b></i></u>","target":"https://saliu.com/winning.html","line":56},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":58},{"title":"<u><i><b>Blackjack Streaks, Software, Probability of Consecutive Losses / Wins, Stats</b></i></u>","target":"https://saliu.com/gambling-lottery-lotto/blackjack-report.htm","line":59},{"title":"<u><i><b>baccarat</b></i></u>","target":"https://saliu.com/winning_bell.html","line":60},{"title":"_**The Best-Ever Roulette Strategy, Systems**_","target":"https://saliu.com/best-roulette-systems.html","line":61},{"title":"<u><i><b>Craps Strategy, Systems Based on Fundamental Formula of Gambling</b></i></u>","target":"https://saliu.com/bbs/messages/504.html","line":62},{"title":"_**The Best Blackjack Strategy, System Tested with the Best Blackjack Software**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":63},{"title":"_**blackjack card-counting**_ \"bishops\" or \"gurus\"","target":"https://saliu.com/bbs/messages/511.html","line":66},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/gambling-fights.html","line":66},{"title":"The best probability software calculates winning and losing streaks based on mathematical formulae.","target":"https://saliu.com/ScreenImgs/streaks-gambling.gif","line":71},{"title":"The best strategy in sports betting applies records, scores, results and streaks.","target":"https://saliu.com/HLINE.gif","line":73},{"title":"<u><i><b>Software to Generate Full 1X2 Systems for Football Soccer Pools</b></i></u>","target":"https://forums.saliu.com/software-1x2-football-soccer-pools.html","line":81},{"title":"<u><i><b>Sports Prognosticating or Betting for NFL American Football, Excel Sport Bet Spreadsheet</b></i></u>","target":"https://saliu.com/bbs/messages/382.html","line":82},{"title":"The best strategy in horse racing betting plays longshot trifectas for several races.","target":"https://saliu.com/HLINE.gif","line":86},{"title":"_**races at three horse tracks**_","target":"https://saliu.com/horses.html","line":90},{"title":"_**The non-favorites won 169 horse races out of 272 or 62.13% of the time.**_","target":"https://saliu.com/horseracing.html","line":102},{"title":"The best strategies in lottery, lotto are based on wacky filters to reduce many combinations.","target":"https://saliu.com/HLINE.gif","line":106},{"title":"<u><i><b>lottery filters, lottery filtering, reduction: my discovery</b></i></u>","target":"https://saliu.com/bbs/messages/919.html","line":112},{"title":"<u><i><b>12-number lotto wheel</b></i></u>","target":"https://saliu.com/lottowheel.html","line":114},{"title":"<u><i><b>History of Lottery, Experience, First Lotto Software, Systems, Strategies</b></i></u>","target":"https://saliu.com/bbs/messages/532.html","line":116},{"title":"<u><i><b>Lottery Software, Lottery Strategies, Lotto Systems</b></i></u>","target":"https://saliu.com/LottoWin.htm","line":120},{"title":"_**increases chances to hit lotto jackpots sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":125},{"title":"_**Lottery Strategies, Lotto Systems**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":127},{"title":"_**Lottery Strategy, Systems, Software Based on Lotto Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":128},{"title":"_**Euromillions Strategies, Systems, Software, Winning Numbers**_","target":"https://saliu.com/euro_millions.html#software","line":129},{"title":"Playing just one lottery ticket every drawing is a losing, bad lotto strategy.","target":"https://saliu.com/HLINE.gif","line":131},{"title":"_**Formulas, Software to Calculate Lottery, Lotto <u>Odds, Hypergeometric Distribution Probability</u>**_","target":"https://saliu.com/oddslotto.html","line":134},{"title":"The best mathematical lottery strategy plays more tickets less frequently, in fewer drawings.","target":"https://saliu.com/HLINE.gif","line":137},{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":140},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":141},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":142},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":143},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":144},{"title":"<u><i><b>Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software</b></i></u>","target":"https://saliu.com/bbs/messages/42.html","line":145},{"title":"<u><i><b>Step-By-Step Guide to Lotto, Lottery Filters in Software</b></i></u>","target":"https://saliu.com/bbs/messages/569.html","line":146},{"title":"<u><i><b>Pick-3 Lottery Strategy Software, System, Method, Play Pairs</b></i></u>","target":"https://saliu.com/STR30.htm","line":147},{"title":"<u><i><b>Vertical or Positional</b></i><b> Filters in Lottery Software</b></u>","target":"https://saliu.com/bbs/messages/838.html","line":148},{"title":"<u><i><b>MDI Editor Lotto Is the Best Lotto Lottery Software; You Be Judge</b></i></u>","target":"https://saliu.com/bbs/messages/623.html","line":149},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":150},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":151},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":152},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":153},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":154},{"title":"Download the best software for strategies in lotto, gambling, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":157},{"title":"**software**","target":"https://saliu.com/infodown.html","line":182},{"title":"The best winning scientific strategies for gambling, lottery have always been free at Ion Saliu's.","target":"https://saliu.com/HLINE.gif","line":184},{"title":"Forums","target":"https://forums.saliu.com/","line":186},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":186},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":186},{"title":"Contents","target":"https://saliu.com/content/index.html","line":186},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":186},{"title":"Home","target":"https://saliu.com/index.htm","line":186},{"title":"Search","target":"https://saliu.com/Search.htm","line":186},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":186},{"title":"This is the site of truly winning lotteries, gambling, casino, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":188}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08926497,-0.0042924,-0.03887472,-0.00151771,-0.02136814,0.02274524,0.06614349,0.02475659,0.06131924,-0.01914003,0.01808111,-0.01422423,-0.0095833,0.04311997,-0.01454056,-0.03661674,-0.00222139,0.003355,-0.01239098,0.01512123,0.08852642,-0.03932183,-0.05086693,-0.09124421,0.10392042,-0.01122032,-0.04077285,-0.07596526,-0.03060859,-0.17443787,-0.03429962,0.0215615,-0.02857389,-0.06056342,-0.09430574,-0.02726833,0.00191372,0.0370414,-0.06781517,0.0214398,0.00827936,-0.00370811,-0.01003315,-0.03914885,0.00017286,-0.02274251,0.0301415,0.01642372,0.0286206,0.00478081,-0.04348379,0.08168524,-0.02676524,-0.00737346,0.06091158,0.03171242,0.03383019,0.10128841,-0.00462758,0.0266111,-0.00834391,0.03652498,-0.19335367,0.04232527,-0.02517808,0.05312793,-0.00884271,0.02086403,-0.03556703,0.04601258,0.05136035,0.01842454,-0.03799427,0.06090886,0.07646611,-0.02919535,0.02020806,-0.02414058,-0.0041028,0.04811742,-0.03593278,-0.02975832,0.00349927,-0.00109759,0.01627151,0.05197342,0.00205742,0.00286279,0.07298201,-0.09380437,0.03900913,0.04486711,0.02770532,-0.01008696,0.0505452,0.04598894,0.04185745,-0.04537449,-0.01495412,0.12178199,-0.01482747,-0.009652,-0.04262449,0.00558957,0.08716898,-0.03184244,-0.00300929,-0.05132027,0.01212932,0.01544044,0.01823813,-0.00509091,0.09876171,-0.00747598,-0.04354542,0.00477074,0.0150318,-0.0288395,0.03419705,0.0071641,-0.04348483,0.05010173,-0.01128785,-0.00079627,-0.0227799,-0.06455853,-0.01832388,0.05619239,-0.02238331,0.0087671,0.04668382,-0.0191558,-0.11949865,-0.02637779,-0.05878733,-0.02771193,-0.00711319,-0.02056798,-0.01481845,0.03972983,-0.03690122,-0.02587457,0.05988624,-0.11561946,-0.02855808,0.05111707,0.03770636,-0.00023497,-0.03341062,0.02781824,0.00894599,-0.00167578,-0.01468111,-0.0402416,-0.0107489,-0.02619947,0.06628428,0.07393371,-0.04936155,-0.01349638,-0.05347806,-0.02169858,-0.03543555,0.13536257,-0.05063935,-0.06898331,-0.02188203,0.02510473,-0.0261983,-0.07387499,-0.03851291,-0.00984717,-0.0634989,0.02707484,0.07249221,-0.0399722,-0.06065078,-0.07703438,-0.02178439,0.01923573,0.03765507,-0.05027863,-0.03790791,0.03597233,0.0305629,-0.04455505,0.02547457,-0.05020113,0.07927515,0.0401548,-0.03947197,0.06041254,-0.03093561,0.01111644,-0.06122003,-0.02985678,-0.02839062,-0.0371296,0.03661833,0.01852942,-0.09763253,0.00197834,-0.00850735,0.00005015,-0.05276381,0.02522212,-0.01265587,-0.06982488,0.08225472,0.02138465,-0.0328832,0.01554117,0.00183339,0.04425853,-0.04482653,-0.00214696,0.03524371,0.04289626,-0.04314083,0.04157318,0.01992348,0.01189982,-0.08702417,-0.19471402,-0.07301646,-0.08346728,0.00393175,0.05364652,-0.01487369,0.01815859,-0.03169865,0.00640609,0.05709583,0.07653436,-0.07269111,0.02065159,0.04696178,-0.00036174,-0.01684585,-0.09309678,-0.05018745,-0.05366346,0.0118572,-0.01222103,-0.03404354,-0.03231916,-0.07763492,0.02905991,0.01899471,0.15570383,0.03630689,0.07071193,0.05449171,0.04829534,0.01949483,-0.03642573,-0.0586545,0.01836122,0.01616591,0.01308477,-0.00130901,-0.03927335,-0.00249253,-0.0356128,0.03868314,0.01089885,-0.07019036,-0.07323167,0.00996497,-0.03772485,0.06085921,0.0055601,0.03593836,0.06678031,0.02436889,0.02890262,0.01667139,0.07073902,-0.01836492,-0.07528415,0.00255815,-0.01425563,0.05137359,-0.03154258,-0.01077211,0.02635391,0.00524441,0.05044814,0.00588811,0.01149982,-0.0134803,0.02359968,0.0235527,-0.02293926,0.11697531,0.01160478,0.0000599,0.03711823,0.05900958,0.1069297,-0.07672759,-0.01380107,0.00662349,0.00718548,-0.06994856,0.08786985,0.03868091,0.02736519,0.02145996,0.05835351,0.03939538,-0.03559403,-0.00116832,0.0217484,0.01390897,-0.05822943,-0.02348232,-0.00995308,0.03767823,-0.24653389,0.02639942,-0.01215917,0.05602289,-0.01070569,-0.03128554,0.01026572,-0.02197412,-0.01943593,-0.01796377,0.06026235,0.02907987,0.03873976,-0.01239824,-0.03430524,-0.03830089,0.0276128,-0.0868075,0.09555318,0.05547059,0.04241975,0.07296751,0.23897834,-0.04834788,0.01990902,0.00738078,0.01230726,0.01587853,-0.04655233,0.02974251,0.03448845,0.02699472,0.02518934,0.02116214,-0.05728154,0.05528468,-0.00952506,0.04601654,-0.00560206,-0.01028065,-0.0798164,0.02522838,-0.00470166,0.04896853,0.10685375,-0.01180982,-0.00151782,-0.03668904,0.04669322,0.03797592,-0.10560302,-0.05211348,-0.0401139,-0.02441099,0.00600599,0.01780773,0.02165062,-0.05555455,0.01771048,0.00678696,0.0220056,0.09286575,0.06650018,0.05334794,0.03422974],"last_embed":{"hash":"1n7jerq","tokens":109}}},"text":null,"length":0,"last_read":{"hash":"1n7jerq","at":1753423633301},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#{1}","lines":[10,15],"size":330,"outlinks":[{"title":"Ion Saliu's theory, software in lottery, gambling is the only honest, no-nonsense strategy.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05706489,-0.04635064,-0.04381905,0.0306998,-0.02800949,0.07881041,0.03211601,0.03501161,0.0580139,-0.01885607,0.00219105,-0.02606124,-0.02913409,0.04093748,-0.05292258,-0.03835275,-0.03018744,-0.01464091,-0.000987,0.03418937,0.08913697,-0.07771242,-0.05117946,-0.09251658,0.08090025,-0.00432047,-0.00483555,-0.04134467,-0.02030429,-0.19921458,-0.03517994,0.00320161,-0.01253957,-0.05991499,-0.11916435,-0.04847162,0.00479831,0.06407936,0.00086248,0.06067994,0.01128482,0.00537154,0.00092857,-0.00417468,0.00513153,-0.00322742,0.05734114,0.02358805,0.03433275,0.00206258,-0.0455098,0.03030716,-0.04436867,-0.03571177,0.07720148,0.0366928,0.05714775,0.08856425,-0.02491384,0.00828461,-0.01408011,0.0785412,-0.2220332,0.07369214,-0.05175829,0.00397039,-0.0018347,-0.00954868,-0.03646614,0.06567219,0.05869796,0.00272079,-0.00830622,0.02214641,0.0514745,-0.00195824,0.00817619,-0.03831948,-0.01105728,0.0566908,-0.02430204,-0.01995721,-0.00517629,-0.02126539,0.02405342,0.05410074,-0.01894454,0.01261122,0.05256702,-0.07051996,0.07380851,0.04520292,0.02523848,0.01986333,0.00271123,0.00960778,0.01760408,-0.04186011,0.01406491,0.10280734,0.00705512,-0.02038159,-0.01377815,0.01776847,0.07353742,-0.02233359,-0.01122783,-0.03405178,0.01085251,0.01597546,-0.00576279,-0.00955337,0.07299912,-0.01343883,-0.01508917,0.01877554,0.00137689,-0.04162495,0.01200456,0.02036808,-0.02564107,0.06235705,0.02323661,0.00742482,-0.03510908,-0.04216513,-0.00827076,0.04279405,-0.01295304,0.01346875,0.0306166,-0.01136678,-0.09151079,-0.0506408,-0.06011648,-0.04338103,0.00919459,0.01854009,-0.00298028,-0.00351956,-0.02576311,-0.05884429,0.06656857,-0.11587843,-0.02413268,0.04267371,-0.01904972,0.01769467,0.00141088,0.01997492,0.00581808,-0.01690066,-0.01489194,-0.0496991,-0.04438366,-0.01772823,0.03877486,0.07207375,-0.01795586,-0.02521172,-0.08939839,-0.06699693,-0.01939785,0.11886184,-0.06088899,-0.09530967,-0.05291492,-0.00227249,-0.02303212,-0.08299873,-0.02461727,-0.04738547,-0.04971198,0.0704606,0.07774577,-0.01645185,-0.04556264,-0.04863227,-0.01870455,0.03500614,0.01495834,-0.0766461,-0.06658844,-0.01476507,-0.02148312,-0.02192341,0.0422345,-0.07953115,0.06992356,0.05129257,-0.06853957,0.04138469,-0.04519456,-0.0011024,-0.0420953,0.0233299,-0.01576475,-0.04485899,0.07713819,-0.00883391,-0.08488394,-0.04256532,-0.01450157,0.00991314,-0.06784537,0.05132378,0.01783784,-0.05993464,0.06116892,-0.01293454,0.00474316,0.03514663,0.00829585,0.01682161,-0.01936155,0.01659903,0.0285703,0.04802061,-0.06515929,0.00766957,0.08269013,-0.00680717,-0.06114932,-0.16438197,-0.06307516,-0.08337002,-0.00187845,0.03922893,-0.00899834,0.04038582,0.00372608,0.02534605,0.06138251,0.06869867,-0.0219491,0.00169803,0.05024804,-0.00618668,0.00772379,-0.07500896,-0.04993431,-0.01951385,0.01613791,-0.01592075,-0.00712037,-0.04415068,-0.05822029,0.01973727,-0.03898063,0.16424255,0.04069374,0.02641574,0.03317627,0.05205882,0.02181251,-0.04377513,-0.06230462,0.03505864,-0.00884265,0.01739477,-0.02746077,-0.075511,-0.0098096,0.00729388,0.02979669,0.00585047,-0.07215887,-0.07122444,0.00512296,-0.01069379,0.07847939,-0.00603661,0.04530638,0.07630324,-0.00804387,0.03318458,0.0280661,0.04063931,0.00986179,-0.0868649,-0.02225465,-0.03196189,0.08335187,-0.03837419,-0.01451649,0.06075234,-0.03315803,0.05608241,-0.02240108,0.01854,-0.00697375,0.0410506,0.01985271,-0.03024764,0.06670471,0.01470585,0.00193356,0.05012812,0.00109564,0.07826178,-0.0634123,0.02989956,-0.00032355,0.00586111,-0.10868164,0.07267384,0.03175533,0.04925786,0.0307854,0.08186406,0.01747041,-0.0134245,-0.00952961,0.01678749,0.00662394,-0.04654087,-0.01993287,0.01952006,0.03592734,-0.27360716,0.0262788,-0.00997756,0.06195385,0.02403456,-0.02861122,0.01751817,-0.00927742,-0.00830781,-0.02282665,0.0668828,0.02617225,0.01624202,0.00376365,-0.0287809,-0.02615154,-0.00762143,-0.07174578,0.08366391,0.06236997,0.02755002,0.05972073,0.19791268,-0.02826293,0.0258385,0.01207661,0.00452065,0.03454066,-0.08190216,0.02601504,0.03353801,0.05119028,0.04166512,0.01438143,-0.04278891,0.07528087,0.02682116,0.05547201,-0.01242948,-0.01075788,-0.0698941,-0.00533132,0.02796773,0.06117022,0.11611315,0.00370662,0.00007329,-0.03815914,0.07291781,0.02165402,-0.10288257,-0.06837333,-0.01904296,-0.00194951,-0.01706073,0.02637981,0.06559697,-0.03329314,0.03981714,0.02232461,0.01395408,0.03378916,0.07341563,0.0790926,0.03873736],"last_embed":{"hash":"15vr0pj","tokens":296}}},"text":null,"length":0,"last_read":{"hash":"15vr0pj","at":1753423633339},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)","lines":[16,24],"size":812,"outlinks":[{"title":"The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics","target":"https://saliu.com/strategy-gambling-lottery.html#mathematics","line":1},{"title":"The Best Strategy in Casino Gambling, Blackjack, Roulette","target":"https://saliu.com/strategy-gambling-lottery.html#gambling","line":2},{"title":"The Best Strategy in Sports Betting, Football, Soccer Pools","target":"https://saliu.com/strategy-gambling-lottery.html#sports","line":3},{"title":"The Best Strategy in Horse Racing, Longshot Trifectas","target":"https://saliu.com/strategy-gambling-lottery.html#horses","line":4},{"title":"The Best Lottery Strategy, Unusual Filter Settings","target":"https://saliu.com/strategy-gambling-lottery.html#lottery","line":5},{"title":"Conclusion","target":"https://saliu.com/strategy-gambling-lottery.html#conclusion","line":6},{"title":"The degree of certainty DC can be viewed as probability of a probability.","target":"https://saliu.com/HLINE.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05202626,-0.04512876,-0.04276783,0.03457097,-0.01928166,0.07789017,0.03318769,0.04239979,0.0576049,-0.01458013,-0.00032488,-0.02583076,-0.02838426,0.03837661,-0.04975253,-0.03506958,-0.02952334,-0.00204801,-0.00885611,0.03810947,0.08456394,-0.06733753,-0.05005197,-0.09843072,0.07284304,-0.01472778,-0.01227439,-0.04519097,-0.02659268,-0.19943567,-0.03378677,0.00303958,-0.02803196,-0.06525231,-0.11862138,-0.04868119,0.00240301,0.06701465,0.00783234,0.06096791,0.00999921,0.00528984,-0.00483359,-0.00826946,-0.00219057,-0.0117946,0.05465433,0.01818444,0.03545311,0.00048356,-0.04731905,0.03269567,-0.03496653,-0.0317363,0.07857922,0.0281407,0.05921629,0.09082711,-0.02558981,0.01293519,-0.01597896,0.08729364,-0.21759707,0.07381018,-0.04588911,0.00686713,-0.00644688,-0.01116253,-0.03902201,0.07871943,0.05040704,0.01062197,-0.02029658,0.02923386,0.05048657,-0.00889086,0.01147926,-0.04362782,-0.01685827,0.05956189,-0.02803489,-0.01397053,-0.00918006,-0.03200936,0.0252477,0.05425219,-0.02129625,0.00761986,0.05540944,-0.06946949,0.07418852,0.04856999,0.02347627,0.02038258,0.00054155,0.01287966,0.02488768,-0.0474861,0.02483972,0.101087,0.0113132,-0.02206008,-0.00868036,0.01776015,0.06722336,-0.03146857,-0.02121338,-0.03468537,0.00676474,0.01965147,-0.00269138,-0.00884481,0.07271641,-0.01296371,-0.02341987,0.02896969,0.00726163,-0.04062516,0.01022176,0.0189289,-0.03829807,0.05614267,0.02768514,0.00622138,-0.02914692,-0.04971159,0.00067987,0.04821776,-0.0207772,0.01768259,0.024266,-0.01704521,-0.09832338,-0.0471505,-0.06011937,-0.04548842,0.01421149,0.01879388,-0.00269617,-0.00380195,-0.02408032,-0.06142315,0.06657995,-0.11569481,-0.02294748,0.05045273,-0.01366442,0.01596078,0.00143831,0.01230605,0.00327553,-0.00723602,-0.00795494,-0.04212591,-0.03981752,-0.01894456,0.04003442,0.07198501,-0.02753969,-0.02645902,-0.08543315,-0.06392498,-0.02498831,0.12227535,-0.0637297,-0.09096442,-0.06161411,-0.00893185,-0.02292128,-0.0833295,-0.0285439,-0.04247891,-0.0492824,0.06644367,0.07361428,-0.01149494,-0.06320789,-0.05703432,-0.01018601,0.03173101,0.01247955,-0.06771187,-0.05968468,-0.01015629,-0.01677747,-0.01857511,0.03959725,-0.07136218,0.06994478,0.05739941,-0.07221042,0.02853,-0.05118785,-0.00537067,-0.03782612,0.02382361,-0.01656659,-0.04652336,0.07588209,-0.01055964,-0.08854263,-0.04344268,-0.02057139,0.00642982,-0.06209568,0.05352134,0.01287659,-0.0671749,0.06023379,-0.00874226,0.0074812,0.02837164,0.00930994,0.02391317,-0.01722852,0.02237161,0.03013489,0.05647036,-0.0622639,0.00217435,0.08681513,-0.00911074,-0.05098336,-0.16507919,-0.06087589,-0.08350506,0.00296975,0.04863155,-0.00968961,0.03770792,0.00527372,0.02997759,0.06974919,0.05880929,-0.02851458,0.00134598,0.04779718,-0.00373045,0.01281388,-0.07957231,-0.05703584,-0.0303343,0.0226406,-0.01827295,0.00016925,-0.04406616,-0.05131674,0.02361397,-0.04026321,0.15950371,0.0377317,0.03151856,0.03263415,0.04537966,0.02390086,-0.03958618,-0.04915753,0.04083722,-0.01460591,0.01251185,-0.02805404,-0.06595753,-0.01489644,0.01727307,0.03928177,0.00125551,-0.07332583,-0.07025747,0.0025064,-0.00176517,0.07525228,-0.00009862,0.04491017,0.08019217,0.00044307,0.0322155,0.02030955,0.04545246,0.00998945,-0.08883104,-0.02237926,-0.02652369,0.07540969,-0.03198912,-0.01997314,0.05828282,-0.03531324,0.05481753,-0.02329767,0.02651706,-0.00971778,0.0389313,0.02378577,-0.02340574,0.06644159,0.0237465,0.00294363,0.04982648,0.00190144,0.08509799,-0.06255799,0.02509364,0.00066209,0.01030251,-0.11154849,0.07319859,0.04283468,0.04474295,0.02848508,0.07680167,0.01507192,-0.00948059,-0.00925962,0.01120831,-0.00266728,-0.05199105,-0.01490839,0.02199049,0.03848979,-0.26556069,0.02856766,-0.00552267,0.0677773,0.01839872,-0.03303517,0.02248105,-0.0213061,-0.00722704,-0.01745139,0.06770314,0.03109856,0.0217836,0.00364704,-0.03276735,-0.03712928,-0.00426754,-0.07251102,0.08132092,0.06175425,0.02842627,0.06003974,0.20118465,-0.0199803,0.03010939,0.00568991,0.00551265,0.03017298,-0.07922008,0.02283091,0.03536196,0.04835388,0.03448353,0.00378457,-0.04731737,0.08012216,0.02759391,0.05969839,-0.00961812,-0.0083731,-0.06760118,-0.00850633,0.02499589,0.05474416,0.11649241,0.00359819,-0.0042006,-0.03797108,0.07177759,0.02031405,-0.10719761,-0.06762063,-0.01953934,-0.00928951,-0.01160125,0.03417937,0.06694603,-0.03852063,0.03548309,0.01992579,0.01570025,0.03334921,0.07111164,0.07027946,0.03879866],"last_embed":{"hash":"1tvkokv","tokens":259}}},"text":null,"length":0,"last_read":{"hash":"1tvkokv","at":1753423633428},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling##I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)#{1}","lines":[17,24],"size":671,"outlinks":[{"title":"The Best Strategy in Casino Gambling, Blackjack, Roulette","target":"https://saliu.com/strategy-gambling-lottery.html#gambling","line":1},{"title":"The Best Strategy in Sports Betting, Football, Soccer Pools","target":"https://saliu.com/strategy-gambling-lottery.html#sports","line":2},{"title":"The Best Strategy in Horse Racing, Longshot Trifectas","target":"https://saliu.com/strategy-gambling-lottery.html#horses","line":3},{"title":"The Best Lottery Strategy, Unusual Filter Settings","target":"https://saliu.com/strategy-gambling-lottery.html#lottery","line":4},{"title":"Conclusion","target":"https://saliu.com/strategy-gambling-lottery.html#conclusion","line":5},{"title":"The degree of certainty DC can be viewed as probability of a probability.","target":"https://saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07708509,-0.05480087,-0.02500863,0.01539451,-0.01916764,0.05915553,0.03910373,0.02793602,0.05110305,-0.03143735,0.00124584,0.02561864,-0.02467735,0.03244619,-0.01297391,0.00405483,-0.00784581,-0.05088867,-0.01025768,0.04054034,0.04821522,-0.05036911,-0.03934057,-0.09004286,0.07304533,-0.02485424,-0.03247208,-0.03994326,-0.053324,-0.21851262,-0.00525678,-0.01174016,-0.0158639,-0.09083344,-0.0996142,-0.03828919,-0.01526002,0.04894865,-0.02629212,0.06295595,0.01064084,0.01125012,0.00205846,-0.02177916,-0.00117613,-0.00993037,0.02141073,0.00745893,-0.0097012,-0.0039354,-0.07414076,0.03837917,-0.02332631,-0.02738823,0.05788337,-0.00474452,0.03979946,0.10218367,-0.00661005,0.0393247,-0.01564911,0.05507765,-0.18916646,0.08054773,-0.01408816,0.03623857,-0.00007659,0.01369955,-0.0031027,0.05176837,0.06369931,0.04280622,-0.02077908,0.0607128,0.0625672,-0.01861286,-0.00598259,-0.00895764,-0.01489802,0.06511379,-0.02023594,-0.01518245,0.00097966,-0.02660785,0.0118662,0.03873077,-0.00131495,0.03209344,0.05957442,-0.0639495,0.05404035,0.07656854,0.02821533,0.00242117,0.06272699,0.02115452,0.040936,-0.03387742,-0.02911764,0.06634022,0.00483774,-0.03407805,-0.02965371,0.04155093,0.07771622,-0.02642298,-0.03412512,-0.06783814,-0.0044307,0.00796678,0.02964582,-0.03382973,0.09762903,-0.01520973,-0.03688886,0.02391512,0.00595577,-0.00866924,0.02526211,-0.02329134,-0.04325425,0.05237512,0.00657763,-0.00648928,-0.02188461,-0.04276422,-0.01974887,0.06690871,-0.01618366,0.00292733,0.02333473,-0.01687952,-0.12415002,-0.0244248,-0.04503155,-0.03635922,-0.02993327,0.02924563,0.02662855,0.02572014,-0.00852364,-0.03943,0.07480969,-0.11458466,-0.05238275,0.06722473,0.02326048,-0.00502149,0.00497368,0.00623044,0.02446621,-0.00447066,-0.02580183,-0.0505491,-0.01702552,-0.01274563,0.05413917,0.0496878,-0.01742146,-0.02965924,-0.06047422,-0.0429845,0.00858099,0.12507987,-0.03260292,-0.11907876,-0.03556297,0.01098476,-0.02695045,-0.09485842,-0.01529307,-0.0236965,-0.04125735,0.0496425,0.07321461,-0.02340191,-0.07829598,-0.06912278,-0.01502136,0.04467653,0.03910898,-0.03968989,-0.04752925,0.02412132,-0.0179406,-0.06806919,0.03060104,-0.0345562,0.04066417,0.01538751,-0.07789527,0.00209546,-0.06517819,0.02564471,-0.03241827,-0.0276802,0.01513336,-0.03959778,0.05733759,0.03052632,-0.09693238,-0.01931269,-0.01654017,0.00262896,-0.06880176,0.04971505,-0.00709959,-0.10239848,0.10012549,0.02258911,-0.01421229,-0.00332669,-0.01290366,0.05580504,-0.03714535,0.02420093,0.03363175,0.05163167,-0.03164018,-0.00051824,0.03631689,0.02094559,-0.05552082,-0.19284573,-0.09648015,-0.08104831,0.00562027,0.05191346,0.00610218,0.01949363,-0.02404829,0.0310928,0.09005688,0.05838395,-0.05859698,0.01128246,0.0374768,-0.00670075,-0.0282829,-0.10347897,-0.05752799,-0.05278762,0.03119095,-0.02916201,0.00160073,-0.02209842,-0.0945523,0.02396896,-0.00010274,0.15991446,0.05580361,0.02919201,0.03039832,0.0393138,0.0192581,-0.00086516,-0.07482619,0.03806667,0.00679584,0.02047128,-0.01064785,-0.06680828,0.00948998,-0.02212911,0.04465706,0.02634885,-0.07979102,-0.12325198,-0.00881756,-0.03606161,0.05562649,-0.00800706,0.0821541,0.05361762,-0.02453918,0.05042041,0.01370042,0.05287249,-0.00027711,-0.06918868,0.00592552,-0.00053473,0.07658124,-0.00555314,-0.02138631,0.04490145,-0.0264341,0.06027205,0.01488462,0.02698547,-0.0268184,0.02435182,0.00000395,-0.02781268,0.09290227,0.00837795,-0.00835645,0.07768293,0.0261059,0.087552,-0.04770514,-0.00925656,0.01842453,0.02025143,-0.07540718,0.05990324,0.06169231,-0.00424252,0.00950042,0.05247582,0.04048766,-0.00886736,0.02009723,0.00843338,-0.00255544,-0.0570829,-0.0069603,-0.00252143,0.02176404,-0.22490096,0.02737684,-0.00502513,0.05797184,0.02220548,-0.02883434,0.04140503,0.0020492,-0.00621138,-0.00429714,0.05275372,0.0411853,0.04165547,-0.00813483,-0.00763375,-0.01089241,-0.00804309,-0.06506447,0.06346013,0.03184218,0.06272876,0.07643551,0.22060953,-0.03334462,0.01482098,0.00115087,0.00949519,0.05998717,-0.05501221,-0.00754447,0.03465218,0.02447415,0.00599645,-0.01937111,-0.03311474,0.00540151,0.01391128,0.04350093,-0.0134111,-0.01695001,-0.06782992,0.00343247,0.02895156,0.05068498,0.14387423,0.01883787,-0.01777516,-0.05750898,0.06450278,0.04479514,-0.09100071,-0.07603615,-0.01699005,-0.03103948,0.01125988,0.04973923,0.02758768,-0.04341957,0.01148356,0.02525584,0.01748445,0.05881353,0.04291748,0.06785412,0.00827143],"last_embed":{"hash":"15p0lud","tokens":440}}},"text":null,"length":0,"last_read":{"hash":"15p0lud","at":1753423633510},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>","lines":[25,48],"size":4295,"outlinks":[{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":4},{"title":"_**lack of mathematical foundation of counting cards at blackjack**_","target":"https://saliu.com/bbs/messages/274.html","line":12},{"title":"**<u>Mathematics of <i>Fundamental Formula of Gambling</i>, Logarithms, God</u>**","target":"https://saliu.com/formula.htm","line":16},{"title":"**<u><i>Fundamental Formula of Gambling</i>: Theory of Probability, Mathematics, Degree of Certainty, Chance</u>**","target":"https://saliu.com/Saliu2.htm","line":16},{"title":"Run the best software to calculate probability, degree of certainty, trials, chance, win, loss.","target":"https://saliu.com/ScreenImgs/FFG1.jpg","line":18},{"title":"Casino gambling is the best application of fundamental formula, winning streaks, losing streaks.","target":"https://saliu.com/HLINE.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07318209,-0.06602394,-0.01024199,0.023165,-0.03303216,0.0641942,0.05437518,0.02311846,0.04590428,-0.03516052,0.00622078,0.02002027,-0.02124093,0.03830693,0.000351,0.00959786,-0.03311255,-0.02857548,-0.02983097,0.02913029,0.08468334,-0.03612531,-0.03971818,-0.07392545,0.05976091,-0.02574738,-0.0203428,-0.06017844,-0.04033525,-0.18394686,-0.00427479,-0.01443955,-0.04795717,-0.08607429,-0.08073293,-0.05914675,-0.02253688,0.06464307,-0.01986674,0.06726655,0.03152969,0.01954896,-0.01552475,-0.0128988,-0.00262863,-0.01483741,0.01987339,-0.00656051,-0.01013311,0.00910963,-0.05105651,0.06345832,0.0007026,-0.04206872,0.06254489,-0.01950835,0.02755517,0.08539179,0.0081235,0.03438242,0.00447145,0.05080038,-0.17836817,0.0592,-0.01791511,0.02699496,0.01819777,0.03238949,-0.0267704,0.10242128,0.06738703,0.03768723,-0.01322174,0.05854146,0.06617809,-0.01594381,-0.01520783,-0.03720753,-0.00724251,0.08012012,-0.01231044,-0.03760284,-0.00762211,-0.02579151,0.01718322,0.04233493,0.0020324,0.03500582,0.07070062,-0.04493159,0.04814238,0.0769904,0.02073869,0.01253974,0.05477055,0.01601814,0.02910861,-0.03109978,-0.02108324,0.09028555,-0.01893031,-0.00326947,-0.02217373,0.0281422,0.05819328,-0.02562559,-0.04629581,-0.05314837,-0.0128409,0.02136683,0.03238571,-0.03928566,0.11385805,-0.03061087,-0.022431,0.01759639,0.02212241,-0.00458237,0.01750511,-0.02211347,-0.03355008,0.03653011,0.01484329,-0.01484314,-0.00513995,-0.05061694,-0.0349214,0.06885949,-0.00390371,0.00502145,0.03411264,-0.0415116,-0.1296777,-0.02721075,-0.04388986,-0.03694564,0.0032834,0.01824474,0.02149176,0.01461733,0.00591689,-0.050643,0.08169374,-0.11932091,-0.04213025,0.07680419,0.02608108,-0.0087209,0.00835107,-0.01365298,0.01004932,-0.01152894,-0.02952602,-0.0534338,-0.02325815,-0.00053072,0.05918684,0.0562943,-0.00359796,-0.04608265,-0.08849739,-0.02609454,-0.00048085,0.13676693,-0.04827387,-0.10539588,-0.051721,0.00098696,-0.03035911,-0.0989163,-0.01332732,-0.01217742,-0.0437819,0.05962078,0.09601612,-0.02525221,-0.05860637,-0.07442445,-0.04008044,0.04294878,0.04616289,-0.04727269,-0.04947571,0.02036245,-0.01591167,-0.04085999,0.03146988,-0.03325443,0.06021686,0.00775533,-0.08350093,0.00431708,-0.06339625,0.01928054,-0.02524374,-0.0284182,-0.01312978,-0.03051297,0.05764885,0.01253722,-0.08918097,-0.03932161,-0.01741527,0.00769139,-0.04327485,0.06236437,-0.00877067,-0.08379343,0.08394575,0.01210463,-0.01521782,-0.00411037,0.01009685,0.06578337,-0.05737728,0.00903859,0.04003575,0.05988032,-0.03507053,-0.02672514,0.03436122,0.02707214,-0.06358019,-0.1946418,-0.08270293,-0.06875694,-0.0208854,0.02606818,-0.00040019,0.020583,-0.02638462,0.02682775,0.07468832,0.02857489,-0.04983041,0.00379993,0.04002367,-0.02164339,-0.01860735,-0.11016892,-0.05826665,-0.04674314,0.01574872,-0.00048154,-0.00044123,-0.03890323,-0.08360298,0.03144505,0.00754408,0.17338644,0.09338326,0.04131262,0.0128099,0.04746985,0.01326677,-0.01410334,-0.0519796,0.02257345,-0.00238882,0.0172743,-0.02241775,-0.07719997,-0.01292775,-0.01252293,0.02995127,0.00472542,-0.08279541,-0.09255524,-0.00843144,-0.04179317,0.05098493,0.00538326,0.08085601,0.05350089,-0.03606174,0.05414444,0.02594963,0.05614945,0.02669559,-0.06109831,-0.00503993,-0.0233597,0.07154368,-0.0147642,-0.01098122,0.04426001,-0.02839713,0.05328159,0.01833266,0.03690226,-0.04350726,0.02616705,0.00128365,-0.03198685,0.09134653,-0.00859644,-0.01511831,0.06218367,0.02192107,0.0751723,-0.02941017,-0.01044199,0.00539196,0.02842825,-0.06130852,0.06038236,0.04275782,-0.00443599,0.01343346,0.02913818,0.0476668,-0.01866718,0.00208736,-0.00095447,0.01330729,-0.04638688,-0.00263397,0.00584981,-0.00971759,-0.23218465,0.06299324,0.00868069,0.04412062,0.01783904,-0.0195693,0.03177606,0.01632178,-0.01129763,0.01295622,0.07723099,0.03175268,0.02664611,-0.0131264,0.00098506,-0.03191277,0.00560583,-0.06336132,0.06983941,0.0269231,0.07796232,0.06603672,0.22755347,-0.01927662,0.00979256,0.00540283,0.01002118,0.04088716,-0.07094102,-0.02343692,0.0423703,0.02341765,-0.01140446,-0.04341365,-0.01592744,0.01866062,0.02699025,0.05797855,-0.00621232,-0.02394011,-0.08290001,0.00660085,0.00932674,0.04272157,0.11742621,0.03255231,0.00341818,-0.05732876,0.05017762,0.05832919,-0.09186503,-0.07374866,-0.02651224,-0.05224757,0.00139007,0.03913508,0.0296738,-0.03947858,0.02841864,0.02199252,0.01617748,0.07884529,0.05787374,0.07667484,0.00718558],"last_embed":{"hash":"fi6fb2","tokens":96}}},"text":null,"length":0,"last_read":{"hash":"fi6fb2","at":1753423633657},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{1}","lines":[27,27],"size":201,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06051955,-0.03693081,-0.00721327,0.00488906,-0.01945647,0.08393905,0.04969833,0.00437231,0.04812832,-0.03682244,0.00489902,-0.00116658,-0.01638994,0.02424943,-0.02627231,-0.00239752,-0.02895867,-0.03835424,-0.05363449,0.04059681,0.07670724,-0.05629241,-0.04659465,-0.09837692,0.06549581,-0.008279,-0.01392248,-0.03294677,-0.01508602,-0.18011478,0.02018747,0.0127084,-0.05877516,-0.09218901,-0.11364212,-0.04191025,-0.0208391,0.06753402,-0.02439658,0.05377042,0.01430711,0.00778566,-0.00757161,-0.01997911,-0.00147388,-0.00614217,0.02481854,0.01309175,0.0149313,-0.00008549,-0.07466438,0.04181753,-0.0332295,-0.03642621,0.06038474,0.00810518,0.06066654,0.09552852,-0.00372026,0.04340263,-0.01969616,0.05064898,-0.19792867,0.06319971,-0.0032797,0.02538022,0.01076373,0.00271993,-0.01618214,0.07638267,0.04432817,0.03613557,0.01122274,0.06023736,0.05377504,-0.01438067,-0.03230928,-0.04415718,-0.00236097,0.11016105,-0.02512169,-0.02716977,-0.00846495,-0.00626185,0.00416538,0.03769094,-0.01137852,0.01267724,0.05590883,-0.07669692,0.05861641,0.05610177,-0.03249799,0.01211459,0.05337911,0.02716636,0.03689968,-0.05567201,0.01418958,0.10533809,-0.00425439,0.00391073,-0.02681794,0.05267536,0.08060592,-0.02685987,-0.01664109,-0.06615454,-0.01235079,0.02735237,0.02116069,-0.0361735,0.07362321,-0.05152319,-0.05730709,-0.00626741,-0.01634005,-0.02084647,0.05310796,-0.02972711,-0.04407811,0.04386923,0.0157917,-0.02739667,-0.02580887,-0.03949269,-0.03357764,0.05842642,-0.01121905,0.00972829,0.05173432,-0.01596399,-0.11473518,-0.02481666,-0.01727617,-0.0178994,-0.00249603,0.01705064,0.0271512,0.01505541,0.00676813,-0.01986661,0.06249427,-0.14913221,-0.04839841,0.09250906,-0.01802009,0.0170836,0.00436368,0.01483594,-0.00063876,-0.01447007,-0.01418872,-0.04555398,-0.02318796,-0.01974583,0.03795009,0.02046906,0.00419562,-0.02489441,-0.05216121,-0.05255131,0.00602009,0.12077994,-0.04848278,-0.05028171,-0.04825298,-0.00549259,-0.02405318,-0.09833857,-0.02303451,-0.00940422,-0.05031336,0.05539954,0.07950462,-0.00822413,-0.04436852,-0.0586842,-0.02971066,0.02400122,0.0256662,-0.05687238,-0.04272744,0.0066099,-0.04239472,-0.06041661,0.02508668,-0.04232459,0.04398467,0.03512102,-0.0633423,0.02492701,-0.04517379,-0.0002401,-0.0454352,-0.00978154,-0.02311675,-0.014536,0.04798479,0.01460069,-0.08397695,-0.00198666,-0.02475182,0.02263325,-0.08628021,0.04753263,0.01576243,-0.08558453,0.09989135,0.00411823,-0.03098093,0.00297255,0.0005417,0.06573567,-0.03630167,0.01533257,0.04429193,0.0500489,-0.01812609,-0.01004445,0.03035174,-0.00696725,-0.05562293,-0.17405555,-0.07634734,-0.06276096,0.01745444,0.06034157,0.02098476,-0.00395656,-0.00794575,0.0193004,0.08406809,0.05027211,-0.05104395,0.00571786,0.05591161,-0.00233833,-0.02958493,-0.08581346,-0.06846242,-0.02228892,0.03185678,-0.04045777,0.00805931,-0.02818069,-0.08536427,0.0069515,-0.0004056,0.14001025,-0.00752843,0.02981099,0.02549289,0.04615325,0.02825308,-0.01923568,-0.0318192,0.05396503,-0.0223897,-0.0138903,-0.03642441,-0.07948887,-0.02611247,0.02956887,0.06503828,0.01672488,-0.03681764,-0.12248313,-0.01459582,-0.03718776,0.07276294,-0.02097192,0.06608601,0.04912292,-0.01017878,0.02199002,-0.00192342,0.0483638,0.01269056,-0.08595793,-0.00101347,-0.01574698,0.05890699,-0.00481707,-0.03076386,0.04638424,-0.01900361,0.0519515,-0.0083861,0.02218072,-0.02921062,0.04471875,0.01857873,-0.02007713,0.07057474,0.00359373,-0.02106938,0.05752524,0.02368599,0.09462067,-0.03083557,0.00569467,-0.00139787,0.04660583,-0.07285848,0.04191628,0.05396599,0.02974391,0.02127705,0.04917615,0.03226364,0.00234757,0.02230355,0.01504037,-0.00780833,-0.04391033,0.00643876,-0.01695522,0.00354754,-0.25873312,0.05361833,-0.0226305,0.03161985,0.02025947,-0.04124134,0.01379444,0.01301683,-0.01182287,0.00135656,0.0840106,0.01467524,0.05068839,0.00028138,-0.04036123,-0.02477573,0.0192967,-0.09658177,0.08922823,0.02992699,0.07170564,0.0621128,0.23350461,-0.02243525,0.02614655,0.00806694,0.02731135,0.05118733,-0.04873437,0.00758564,0.06210355,0.02265705,0.02363741,-0.02643142,-0.00688507,0.06773145,0.00963283,0.06160447,-0.02291421,-0.00110352,-0.05326604,-0.01393592,0.01534769,0.06366269,0.1446695,-0.00302845,-0.02826531,-0.04368421,0.0438158,0.02759862,-0.12080874,-0.04357188,-0.05216476,-0.00351293,-0.02266471,0.04509842,0.0360666,-0.03729185,0.02318301,0.05062117,0.04104953,0.06431539,0.05337629,0.05780501,0.00070424],"last_embed":{"hash":"1xel9rm","tokens":107}}},"text":null,"length":0,"last_read":{"hash":"1xel9rm","at":1753423633688},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{2}","lines":[28,28],"size":235,"outlinks":[{"title":"_**degrees of randomness**_","target":"https://saliu.com/bbs/messages/683.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05646382,-0.03622179,-0.01170219,0.00368508,-0.01979514,0.04675631,0.0546252,0.02707286,0.04045645,-0.00121804,-0.00013507,-0.00826537,-0.02566222,0.04485213,-0.02309385,-0.02445026,-0.00981788,-0.00119065,-0.00824341,0.02027927,0.10020892,-0.04722385,-0.08401647,-0.07307573,0.08595644,-0.0084456,-0.05232523,-0.04674398,-0.03048141,-0.18874955,-0.01630363,0.00960052,-0.02491478,-0.06660395,-0.10426828,-0.03396532,-0.01065545,0.05829901,-0.03811316,0.05953779,0.01645316,0.00486314,-0.02073033,0.00044077,0.00533198,-0.00512385,0.03062103,0.01617446,-0.00010696,-0.02019089,-0.03697933,0.07130428,-0.04174251,-0.01975538,0.06053604,0.01377395,0.04871576,0.09242478,-0.00810707,0.03882989,-0.00651139,0.0669712,-0.19420995,0.05855367,-0.02269021,0.04450146,0.00917289,-0.00098081,-0.01663874,0.07768253,0.04771245,0.0043406,-0.00561627,0.05689739,0.05262582,-0.0218434,0.0039594,-0.03605221,-0.00919649,0.07180017,-0.02187538,-0.00184411,0.01545118,-0.03481793,0.01364338,0.04409514,-0.01005926,-0.00454079,0.06695656,-0.06192784,0.06385017,0.05651196,0.01621722,-0.00168707,0.05556845,0.02928411,0.02299669,-0.04898613,-0.00608062,0.08812369,-0.00138135,-0.02333254,-0.01574782,0.01612406,0.06822853,-0.04293337,-0.0249175,-0.06894572,0.01751055,0.01462837,-0.00575489,-0.01578843,0.10434223,0.01921939,-0.02634635,0.00814773,-0.00610575,-0.03457165,0.02789551,-0.00387043,-0.04019834,0.06060156,0.0058059,-0.00316201,-0.03398221,-0.06320715,-0.02233548,0.07011412,-0.02837487,0.01091351,0.02813118,-0.03369519,-0.11731766,-0.02194809,-0.05525808,-0.03513147,-0.00228463,0.0261661,0.00031029,0.0417554,-0.01502215,-0.03650861,0.0567207,-0.12753661,-0.02389642,0.07152607,0.02159273,0.00026735,-0.00936277,0.01530185,0.01091039,-0.01255188,-0.03136809,-0.03815993,-0.02968845,-0.00745731,0.05039068,0.06073504,-0.02069006,-0.01581218,-0.09311856,-0.04732912,-0.02195101,0.11544512,-0.05695508,-0.09898622,-0.0380173,-0.004398,-0.02593258,-0.08084908,-0.02607642,-0.02066931,-0.07519264,0.05451914,0.09116407,-0.01263692,-0.06957418,-0.06716438,-0.0212493,0.03201199,0.06598729,-0.06581441,-0.06427407,0.01653953,0.02491776,-0.01853226,0.03278268,-0.04425254,0.04272389,0.02930043,-0.06628294,0.03866821,-0.0516329,0.00170229,-0.04617531,-0.01803313,-0.01082671,-0.03162104,0.05736673,0.00687399,-0.09223955,-0.00036902,-0.01628102,0.00771487,-0.04489198,0.05386271,-0.00492542,-0.09701007,0.08764185,0.00825185,-0.00564231,0.00355501,0.00392503,0.04431688,-0.03113907,0.00529535,0.03956891,0.04141426,-0.06002462,0.02578862,0.04927204,-0.00312453,-0.06294635,-0.18796307,-0.08395992,-0.06159032,-0.00012008,0.06372966,0.00788652,0.00957051,-0.00976559,0.00004823,0.08418965,0.04715192,-0.07257577,0.00342061,0.02178443,-0.01011777,-0.02965708,-0.11172222,-0.06610104,-0.04455417,0.000062,-0.01572377,-0.01321424,-0.02226634,-0.09209471,0.02563517,0.00446005,0.15843396,0.03606532,0.03413272,0.03592441,0.03301193,-0.00405408,-0.01625733,-0.0487884,0.01717749,-0.01241154,0.03335603,-0.04031827,-0.05824964,-0.00986172,-0.01389206,0.0428733,0.01177532,-0.05947368,-0.08870842,0.01135639,-0.03073667,0.06613507,-0.00485849,0.08377162,0.06067801,-0.0043496,0.03269401,0.0147655,0.05527404,-0.0115671,-0.05819955,-0.00566122,-0.01866252,0.08439711,-0.02460791,-0.02790375,0.03742323,-0.0177558,0.0737504,-0.01125773,0.02340218,-0.01768534,0.0238182,0.02981704,-0.03610859,0.09200577,0.02231736,-0.02393987,0.07237312,0.04382657,0.12134901,-0.04220902,0.00440765,-0.00037227,0.02246413,-0.06480772,0.06880624,0.04431363,0.02647571,0.01807775,0.05043544,0.03088109,-0.00571574,-0.00359715,-0.00354865,-0.00580949,-0.0442717,-0.01543567,-0.01348217,0.03505669,-0.25168228,0.04955979,-0.01259051,0.05910808,-0.01232675,-0.04121117,0.01521395,0.01202432,-0.01984737,-0.0244949,0.05209525,0.01935344,0.0383607,0.01161784,-0.02942676,-0.04568413,0.00446383,-0.09365202,0.08428744,0.0585135,0.04858425,0.0852857,0.22723649,-0.05323026,0.03597122,0.0057765,-0.0043389,0.04273588,-0.0560471,0.01416162,0.03029588,0.02039208,0.03157535,-0.00748122,-0.04370173,0.03879256,0.0266513,0.05060918,0.00232757,0.00539378,-0.05310494,0.00939847,0.00664214,0.05846753,0.11741062,0.00793465,0.00588362,-0.05964169,0.05663731,0.02153887,-0.1103832,-0.05081056,-0.03187697,-0.01662852,-0.00735008,0.0421558,0.04698241,-0.03715994,0.04011338,0.01580716,0.0226917,0.04109715,0.0610789,0.0782539,-0.00555517],"last_embed":{"hash":"135kpyc","tokens":127}}},"text":null,"length":0,"last_read":{"hash":"135kpyc","at":1753423633726},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{3}","lines":[29,29],"size":356,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07987376,-0.0416533,0.00361599,-0.00826964,-0.00150791,0.04910691,0.05729449,0.04934282,0.04202848,-0.00239553,-0.01879882,0.03745949,0.00715817,0.01856421,-0.00295808,-0.0118867,-0.00480278,-0.02615733,0.01691413,0.02619673,0.07559566,-0.03508471,-0.04147715,-0.09199274,0.07161361,-0.03588985,-0.04501497,-0.07263993,-0.0614401,-0.18657239,-0.01777218,-0.00261823,0.00412814,-0.06566741,-0.09296156,-0.02865647,0.00729813,0.05177512,-0.00545161,0.04932838,0.00634251,0.00953109,0.00845369,-0.01536347,0.01654369,-0.03985633,0.0094854,0.00459427,0.01674905,-0.03942112,-0.06627506,0.031314,-0.04467031,0.00450739,0.05464081,-0.01382973,0.03662493,0.08806861,-0.00174621,0.02972413,-0.00140436,0.02305786,-0.20978943,0.0676307,-0.02693477,0.04215516,0.00031933,0.01015425,-0.02361832,0.04268873,0.05057736,0.00219735,-0.0114056,0.06264421,0.07270937,-0.00051583,-0.00362151,-0.02805413,0.00055242,0.07135493,0.00324718,0.0066495,0.01257211,-0.0291441,-0.00934302,0.04980711,0.01599615,0.04247822,0.07264282,-0.07709039,0.06778867,0.05315536,0.0388734,-0.00280382,0.04444435,0.02277102,0.01236434,-0.03884125,-0.04248034,0.10935419,-0.00369782,-0.03789129,-0.04718313,0.01486329,0.07977819,-0.0203501,-0.02591592,-0.07745037,-0.01741068,0.01728662,0.00460895,-0.03447508,0.08102948,0.01426148,-0.01884081,0.03324772,-0.00455715,0.00290921,0.01321252,-0.01740683,-0.04765404,0.05387411,0.02127473,0.01184769,-0.0107531,-0.04554929,0.00189916,0.08960935,-0.01112991,0.03557703,-0.00311292,-0.00262723,-0.11575586,-0.03230015,-0.0330539,-0.02241004,-0.02644494,0.02013816,-0.00416175,0.04192236,-0.0122001,-0.00084611,0.05186331,-0.08400411,-0.01835404,0.07669,0.03018796,0.00524235,-0.02404997,0.01186613,0.03637959,0.01610627,-0.03267173,-0.04237475,-0.01574202,0.01678028,0.05704046,0.03701702,-0.01872466,-0.02478655,-0.04130865,-0.01343175,-0.00639888,0.16223457,-0.03799163,-0.11788938,-0.04823074,0.00620262,-0.01588134,-0.0714278,-0.01720332,-0.02581096,-0.06092328,0.03821091,0.07807135,-0.03614206,-0.04816664,-0.04813591,-0.03938387,0.04734425,0.04101614,-0.03767252,-0.05531915,0.02487746,-0.00115113,-0.05726305,0.02920973,-0.04514422,0.02030744,0.01198871,-0.0930288,0.03500934,-0.07140669,0.02361384,-0.02817606,-0.01291552,-0.01213677,-0.01962595,0.04487719,0.00058156,-0.05472261,0.00806777,-0.02078925,0.00380118,-0.04427582,0.04825314,-0.02672524,-0.0846663,0.07815602,0.00909478,-0.015001,0.01711628,0.01747441,0.01967217,-0.04715534,-0.00998847,0.04479322,0.06142082,-0.05417546,0.04013768,0.03417443,0.01768972,-0.09382801,-0.19325665,-0.09728479,-0.07661636,0.00382147,0.03247583,-0.01660925,0.0360776,-0.04093321,0.00733755,0.1128168,0.06725282,-0.04249546,-0.00729949,0.04113823,0.0022764,-0.02915251,-0.08478105,-0.06923296,-0.05217784,0.00776069,-0.002025,-0.01660159,-0.02565136,-0.08046263,-0.00640248,-0.00062333,0.16062242,0.03577925,-0.01537499,0.03090928,0.05093608,0.01243226,0.00536284,-0.09956042,0.02853815,0.00937955,0.01975868,-0.04043226,-0.05884914,0.01479444,-0.02458671,0.03126815,0.0281159,-0.07082453,-0.1061446,-0.01383676,-0.04563862,0.02045386,0.0041197,0.0775378,0.03583181,0.00684294,0.01907043,0.04203045,0.04804884,0.00149082,-0.07687856,0.02486942,-0.00737717,0.09521784,-0.02643256,-0.05257135,0.01898875,-0.02071907,0.07580677,-0.00865352,0.00639931,0.02010596,0.05503519,0.00361253,-0.04712655,0.09541331,-0.00542755,-0.01869662,0.06821382,0.02127393,0.07982586,-0.06152028,-0.02737495,-0.00340154,0.01255109,-0.05285374,0.07641249,0.05332302,0.01766475,0.03837963,0.04138299,0.0324313,-0.02345593,0.01573367,-0.0005685,-0.00784815,-0.05324132,-0.01761315,-0.02884752,0.02746687,-0.22443429,0.01492314,-0.00931956,0.07052442,0.00980288,-0.02832915,0.03421574,0.02454622,0.01425555,-0.02013588,0.09885042,0.05152542,0.0072776,-0.00981909,-0.02166216,-0.02387922,0.00606455,-0.07854337,0.0745252,0.01942107,0.0485222,0.07512841,0.24568518,-0.0165027,0.01726739,0.01678043,-0.01129072,0.05467702,-0.0836157,-0.0112826,0.03136347,0.00945649,0.01863258,0.00195286,-0.05433518,0.03310511,0.03226381,0.01454547,-0.01461244,0.00975101,-0.06851176,0.00933575,0.02208845,0.07710296,0.13819195,0.00963891,-0.04014191,-0.08769453,0.05341663,0.0162308,-0.11350324,-0.03783881,-0.00816252,-0.02488379,-0.00053668,0.05364951,0.0270255,-0.05002798,0.00897197,0.01149981,0.01349266,0.05959133,0.03797362,0.05856074,-0.00657841],"last_embed":{"hash":"1490xbd","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"1490xbd","at":1753423633771},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{4}","lines":[30,31],"size":473,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07529704,-0.03204092,-0.03479476,0.01008603,-0.03123744,0.04158526,0.02827225,0.03535708,0.03147284,-0.02297941,-0.00331413,0.00588022,-0.01587102,0.04920112,0.00300885,-0.02295198,-0.02411277,-0.03073416,-0.01620691,0.01096379,0.07858216,-0.09081472,-0.0573271,-0.07420115,0.07868194,-0.00154384,-0.04038564,-0.071227,-0.03785846,-0.23755878,-0.0005895,0.01695998,0.05229866,-0.0706251,-0.0903886,-0.03719234,-0.01011534,0.04872933,-0.04459181,0.05276207,0.01175713,0.01030629,0.00206438,-0.01028958,0.00304472,-0.02030072,0.01392363,0.00945432,0.00545989,-0.03332001,-0.04908237,0.02979319,-0.03566563,0.00503699,0.05282661,0.00267491,0.06557632,0.05833685,-0.00187777,0.02880717,0.01235308,0.04838349,-0.19448245,0.07608865,-0.02956747,0.04526826,-0.00060498,-0.02598413,-0.01664424,0.06169019,0.04436841,0.01150724,-0.00217945,0.05865499,0.07166114,-0.0054676,-0.00422281,-0.02197656,0.00538316,0.05831625,-0.0234426,-0.01810742,0.01249171,-0.02111699,0.02257473,0.01658967,-0.01286216,0.02973776,0.06467345,-0.05645191,0.07802277,0.06283198,0.03043671,-0.0146212,0.03275754,-0.00662523,0.0312641,-0.0260293,-0.06448594,0.09263494,0.01904601,-0.00751376,-0.00805795,0.03673018,0.04647733,-0.0248671,-0.02550242,-0.05965159,0.0014252,0.0108545,0.00902187,-0.03313759,0.08980039,0.00054421,-0.02615021,0.03026451,-0.00346932,0.00050711,0.03664753,0.00953538,-0.05375741,0.05841464,0.02056735,-0.01928053,-0.02778992,-0.040949,-0.05726535,0.07214765,-0.00124857,0.03299791,0.00739624,-0.02077759,-0.12180749,-0.04283863,-0.06848169,-0.02451396,0.00721907,0.01960885,0.01443045,0.04452766,-0.0044387,-0.02356008,0.04948892,-0.10045155,-0.00597251,0.09444573,0.02554636,-0.01389288,0.02979555,-0.00151806,0.04119611,-0.02823624,-0.03813719,-0.04348743,-0.01623304,-0.01679251,0.06858213,0.06298536,-0.04619395,-0.00761711,-0.06917626,-0.03299339,-0.02336262,0.12098562,-0.02061487,-0.09862562,-0.03366841,-0.02722674,-0.0357292,-0.10601106,-0.00748229,-0.01713808,-0.05356655,0.035053,0.11100096,-0.03977002,-0.06883176,-0.06458345,-0.00071773,0.03483178,0.02418501,-0.04230629,-0.04563058,0.01761503,-0.02010769,-0.06141377,0.04008307,-0.04995454,0.0205349,0.0163205,-0.09123064,0.01519364,-0.07774581,0.02696479,-0.05244623,-0.03289511,0.00200993,-0.03839944,0.04759448,0.00869694,-0.08745511,-0.03395056,0.00382506,0.00532568,-0.0346769,0.07422271,-0.00533493,-0.08054699,0.0931755,0.02498657,0.01371551,0.00837572,0.00261689,0.05061544,-0.02451851,0.03521128,0.05662058,0.02912893,-0.04162726,0.02481833,0.0414063,-0.0023906,-0.07470515,-0.18699348,-0.08579088,-0.07972543,-0.00094913,0.04202362,-0.02191044,0.01171973,-0.02396177,-0.005595,0.11218076,0.04353961,-0.05446961,-0.01155267,0.00613159,-0.01228645,-0.06175879,-0.1087288,-0.04631914,-0.0370289,0.02374832,-0.02473886,-0.02823562,-0.01373132,-0.08479829,0.00751733,-0.00770015,0.1547319,0.07189948,0.00518979,0.02904841,0.04786029,0.01543115,-0.02278167,-0.06508634,0.00852986,-0.01535684,-0.01304036,-0.03135773,-0.04620885,0.0098342,-0.0538644,0.02730354,0.0173701,-0.07225122,-0.1057948,0.01925316,-0.00146144,0.03916332,0.00560575,0.08986542,0.07680831,-0.03278023,0.03387999,0.02642565,0.05792589,-0.01935216,-0.0416801,-0.0024621,0.01195176,0.09484663,-0.04977974,-0.00774974,0.03576108,-0.03525521,0.08294808,0.03651277,-0.00183984,-0.00737807,0.03595697,0.01811815,-0.01327664,0.0898843,0.02505972,-0.01631149,0.07658957,0.03078261,0.10817139,-0.05195929,-0.00026712,-0.01177319,0.01665378,-0.08058883,0.04785073,0.04978533,0.01374973,0.03935895,0.05594136,0.04417968,-0.00484019,0.00840446,-0.00010911,0.00959197,-0.0321348,-0.00926703,-0.00991522,0.05502425,-0.22798258,0.02377161,-0.01227192,0.04535745,0.0003023,-0.03297473,0.05664754,-0.01215792,-0.01116203,-0.03548133,0.05414754,0.03498273,0.04999821,-0.01199188,0.00080539,-0.03281544,-0.0253716,-0.05911269,0.0664447,0.03469597,0.04037851,0.06541422,0.23869245,-0.02668694,0.00207288,-0.00327983,0.04092819,0.04253661,-0.04418474,0.01814944,-0.00976038,0.02663176,0.03722844,-0.00401514,-0.03301205,-0.0003897,0.01254843,0.03209696,-0.00059118,0.00858939,-0.02768183,-0.00850564,0.02231247,0.06996531,0.14185236,0.01488168,-0.00667013,-0.05200836,0.07549168,0.01669433,-0.0706556,-0.0445129,0.00341172,-0.00586669,0.00447183,0.07420679,0.01320262,-0.04361971,0.01509696,0.01224881,0.01688425,0.02946354,0.04893325,0.06120618,0.00032859],"last_embed":{"hash":"4otg8","tokens":417}}},"text":null,"length":0,"last_read":{"hash":"4otg8","at":1753423633818},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#{5}","lines":[32,43],"size":2637,"outlinks":[{"title":"_**lack of mathematical foundation of counting cards at blackjack**_","target":"https://saliu.com/bbs/messages/274.html","line":5},{"title":"**<u>Mathematics of <i>Fundamental Formula of Gambling</i>, Logarithms, God</u>**","target":"https://saliu.com/formula.htm","line":9},{"title":"**<u><i>Fundamental Formula of Gambling</i>: Theory of Probability, Mathematics, Degree of Certainty, Chance</u>**","target":"https://saliu.com/Saliu2.htm","line":9},{"title":"Run the best software to calculate probability, degree of certainty, trials, chance, win, loss.","target":"https://saliu.com/ScreenImgs/FFG1.jpg","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09003688,-0.01920928,0.00604989,0.01203203,-0.03825498,0.09771893,0.06916525,0.03156726,0.07221226,-0.00410892,0.00223635,-0.00883681,0.00355793,0.02954962,-0.0474026,0.01597552,-0.02867655,0.00988948,-0.02867614,0.03000144,0.10051605,-0.06131245,-0.03192622,-0.09783114,0.06857621,-0.05424459,-0.02145632,-0.07742676,-0.01425752,-0.21339858,-0.01009008,0.00818572,-0.04670895,-0.08335392,-0.0940268,-0.05942589,-0.02086684,0.06641954,0.01274143,0.04909921,0.00211128,0.02458732,-0.00801706,0.00320821,-0.01100407,-0.02386534,0.01177379,-0.00595278,0.03342843,-0.00256029,-0.01854463,0.0565918,0.00761151,-0.03680154,0.04649627,0.00031754,0.03092648,0.10319515,0.00406352,0.03009848,-0.00782576,0.05845676,-0.20488934,0.05868884,-0.00078368,0.00055827,-0.00546113,0.00022211,-0.04342703,0.11089434,0.02467236,0.04711492,-0.0060299,0.07750991,0.06915674,-0.01263433,-0.02215988,-0.07241172,-0.03204,0.05636799,-0.01135176,-0.00175528,-0.01101745,-0.04100276,0.02358327,0.03844441,-0.00649147,-0.01726719,0.05369644,-0.08164664,0.07818601,0.07940478,-0.04235237,-0.00069699,0.01162645,0.01621693,0.05325423,-0.03934671,0.01026754,0.11137419,-0.0064049,-0.00991454,-0.05504458,0.01875215,0.06396682,-0.0374116,-0.00419425,-0.02531439,-0.01354603,0.01102174,-0.0046021,-0.0089849,0.08873109,-0.03916971,-0.04783976,0.01039252,0.01345403,-0.02072889,-0.00553215,0.01072559,-0.02357817,0.06163268,0.01687432,-0.01813082,-0.00953306,-0.04458203,0.00127695,0.059202,-0.01405163,0.01642931,0.04827293,-0.02183262,-0.08897508,-0.01707041,-0.03340399,-0.03334068,-0.00794337,0.03680664,-0.00685939,0.02110501,-0.00870275,-0.00715153,0.06256568,-0.10788926,-0.00834302,0.07382062,-0.00110428,0.02585125,-0.02117027,-0.02389459,0.01663989,0.00879729,-0.01570596,-0.02064335,-0.01274075,0.00212504,0.05906413,0.04759989,0.00958779,-0.03952287,-0.07830756,-0.04850381,-0.01639356,0.15390036,-0.05368914,-0.06183939,-0.05478423,-0.00831657,-0.01913222,-0.07982094,-0.01203456,-0.01808959,-0.06046501,0.05393407,0.07451285,-0.03518995,-0.07246983,-0.04882566,0.01507246,0.01091144,0.02919464,-0.07951576,-0.06416781,0.01526442,-0.02061248,-0.02905283,0.01020347,-0.06021689,0.06238732,0.01554291,-0.07553131,0.01732196,-0.07624082,0.00848161,-0.04758182,-0.00700581,-0.03056289,-0.04879584,0.03607823,-0.00406192,-0.06407998,-0.03991807,-0.00113864,0.01257628,-0.02574826,0.06663357,-0.01905201,-0.06250873,0.04187618,0.0151506,-0.02518608,-0.00052556,0.0212196,0.04913491,-0.0333364,0.02288964,0.03196524,0.07164782,-0.03198796,0.00326347,0.05221076,0.03668647,-0.0278077,-0.17328607,-0.07305633,-0.05520198,0.00811691,0.07640898,0.01333121,0.04519636,-0.03139015,0.02713843,0.03115447,0.01390473,-0.04018478,-0.02523498,0.04863645,0.00692859,-0.01481286,-0.08787123,-0.06508508,-0.02565069,0.0338548,0.00964491,0.00752048,-0.05975236,-0.0956756,0.04205185,0.00485662,0.14431751,0.00095284,0.01090776,0.03826388,0.04401112,-0.00131393,-0.03724882,-0.00259884,0.02830202,-0.03184044,-0.00782935,-0.03712389,-0.08269147,-0.00151831,0.03327791,0.04319056,0.0110881,-0.04660131,-0.1057139,0.00773773,-0.04847579,0.09147521,0.00584459,0.07473081,0.0957545,0.01215745,0.03967972,-0.01654203,0.05031801,0.01135736,-0.05559807,-0.01142334,-0.03254541,0.06600718,-0.04188666,-0.03413962,0.06133912,-0.01068155,0.05892977,0.00155713,0.04045689,-0.01671657,0.05332054,0.04014947,-0.02655443,0.07375284,0.00411778,-0.01883262,0.07539654,-0.00335898,0.0464533,-0.05527151,-0.01098823,-0.02022553,0.03796275,-0.09349933,0.04607463,0.02255102,0.04447245,0.02214273,0.08348435,0.06143517,-0.00885939,0.00714417,-0.01188033,-0.01850789,-0.04153352,-0.03071293,-0.03941194,0.02518332,-0.26089197,0.05074409,-0.03456193,0.04665842,-0.01484968,-0.00888135,0.01393799,0.03371117,-0.04390969,-0.01022846,0.0481506,0.04942809,0.00114149,0.00711834,-0.01219019,-0.03676523,-0.01611844,-0.09583003,0.06042846,0.01633389,0.07793813,0.04951026,0.21757203,-0.02390499,0.03926169,-0.0109149,0.01798332,0.03794731,-0.01840751,-0.01555383,0.00977422,0.02728226,0.0165752,-0.01689032,-0.00055986,0.07540089,0.01962329,0.05025343,-0.01410585,-0.01246415,-0.03275316,0.00289906,0.04347157,0.04954555,0.129795,-0.03111947,0.00780082,-0.04936473,0.03314929,0.02818203,-0.11154238,-0.05723648,0.00954711,-0.02453351,-0.02290229,0.0375898,-0.01109839,-0.01452309,0.06426927,0.02824788,0.01344497,0.067515,0.06078946,0.04987017,0.00688315],"last_embed":{"hash":"kb7qb6","tokens":123}}},"text":null,"length":0,"last_read":{"hash":"kb7qb6","at":1753423633976},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>#-   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._","lines":[44,48],"size":314,"outlinks":[{"title":"Casino gambling is the best application of fundamental formula, winning streaks, losing streaks.","target":"https://saliu.com/HLINE.gif","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06376895,-0.03892814,-0.02579912,0.03294112,-0.03453211,0.08002456,0.02929834,0.03281569,0.06123316,-0.0027287,-0.03458735,0.00546136,-0.01484784,0.04321665,-0.04035432,-0.01544397,0.00241011,0.02817449,-0.01984515,0.03046145,0.06986292,-0.08219727,-0.03059755,-0.08030756,0.05945233,-0.03543081,-0.03024489,-0.07297006,-0.06260607,-0.2107794,-0.02598302,-0.02736797,-0.03159546,-0.06808735,-0.09804147,-0.03891005,-0.01860811,0.06798654,-0.01379101,0.04174636,0.02716864,0.00328025,-0.01526273,0.00943714,-0.0007068,-0.02193552,0.00322639,0.0222805,0.03063424,-0.02655315,-0.05906152,0.04214045,-0.00816872,-0.02446249,0.05890656,-0.02464716,0.04965658,0.09054255,-0.00396388,0.02412829,-0.01548169,0.05085629,-0.18057144,0.04575167,-0.02763927,0.04067292,-0.00548357,0.01316104,-0.02663084,0.06797475,0.02301944,0.03406655,-0.03666732,0.0651691,0.04429767,-0.03569527,0.01138994,-0.03713104,-0.0150911,0.0643943,0.01285705,-0.02654634,-0.00697731,-0.07687965,0.00099255,-0.00786139,-0.01375816,-0.02623915,0.06313724,-0.02802631,0.0606309,0.06808602,0.03157361,-0.01860666,0.0267981,0.00368225,0.0448004,-0.02490435,0.01114265,0.07733747,0.00313219,-0.01697421,-0.01600402,0.02673284,0.07414401,-0.04841639,-0.04475655,-0.04457538,0.00458369,0.01551039,-0.00971064,0.0272833,0.11146316,-0.01827019,-0.01597377,0.01472962,-0.00377499,-0.00337326,-0.0160419,0.03360297,-0.04392171,0.06961492,0.01797672,0.00492194,0.00745401,-0.07288387,-0.01980967,0.06748509,0.01768931,0.01147535,0.00933973,-0.03411942,-0.1057273,-0.01517132,-0.06321307,-0.02644807,0.01611275,0.04151969,0.01773002,-0.00715136,-0.01397272,-0.02640755,0.06233858,-0.1182839,0.00944482,0.06636024,0.02644151,0.02391226,0.02194052,-0.02144849,0.00704609,0.00227648,-0.01053161,-0.07279556,-0.01010924,-0.00628706,0.04727511,0.07920554,-0.05699481,-0.02506223,-0.05970725,-0.03292101,-0.01319888,0.0993361,-0.04652037,-0.07856866,-0.06186934,-0.00923692,-0.02354339,-0.103022,0.00427052,-0.03012016,-0.05935366,0.00342354,0.07520747,-0.001975,-0.06366365,-0.09713347,0.02144564,0.00870539,0.0111408,-0.06063607,-0.03006396,0.02213021,-0.00820034,-0.03162144,0.0170588,-0.05628035,0.0681992,0.02661963,-0.06073613,-0.02033208,-0.07438805,0.01979128,-0.01221956,-0.02252982,-0.0115003,-0.04713786,0.03857802,-0.02502134,-0.06360672,-0.04232351,0.00708461,0.00370254,-0.03423871,0.06312542,0.03420414,-0.09086031,0.10821299,-0.00137545,0.01380613,-0.02490815,0.00057279,0.06475249,-0.04628058,0.03005351,0.03552708,0.03926869,-0.04534196,0.01058309,0.05263532,0.030968,-0.03348527,-0.17579074,-0.08373795,-0.07881628,-0.00046405,0.06829564,0.01697927,0.00782575,0.00300562,0.03460233,0.07897986,0.03612629,-0.064532,-0.00540111,0.02698359,0.00256022,0.00181279,-0.10640413,-0.0449421,-0.05541316,0.02732256,-0.024387,0.0040746,-0.07698949,-0.06713251,0.05493826,0.00028738,0.16177876,0.06302141,-0.00119419,0.01097039,0.03416669,0.04090389,-0.01407769,-0.01981684,0.03444853,-0.00840255,0.03667247,-0.04123309,-0.05505743,0.02536643,-0.00387625,0.06628369,0.02671421,-0.08191651,-0.10786968,0.00612421,-0.02448204,0.08981267,0.01071031,0.08337473,0.10090821,0.01074034,0.06457078,-0.01520847,0.0637045,-0.00996217,-0.07109702,-0.01453081,0.02799293,0.05009766,-0.02176553,-0.02912827,0.05733283,-0.0166133,0.07788313,0.02618799,0.02245489,-0.01977966,0.03211211,0.00365488,-0.02369057,0.07837483,0.0069632,-0.04427158,0.05858829,0.0369104,0.05990572,-0.10072423,-0.01891595,0.01187734,0.03197889,-0.10389545,0.05407176,0.0748047,0.02472896,0.00245934,0.03415503,0.03507251,-0.00917225,-0.00070429,-0.02046171,0.01569216,-0.06512295,-0.02518466,-0.03573291,0.02677492,-0.25675723,0.02660844,-0.01555991,0.04624956,-0.02170237,-0.02732878,0.04720997,0.01054362,-0.02918814,0.01266098,0.037228,0.02754488,0.00826742,0.01344731,-0.02312689,-0.0394152,-0.00412931,-0.08154905,0.08360111,0.0296209,0.04885856,0.08747308,0.20445384,-0.04138087,0.01400651,-0.00085706,0.03775043,0.02827367,-0.0271124,-0.00050516,0.00856312,0.02062191,0.03808489,-0.00240929,0.00748157,0.03868963,0.00627099,0.04523396,-0.021276,-0.02699254,-0.02671507,-0.02633546,0.06468092,0.0460103,0.1603618,-0.03298008,-0.01225775,-0.02991609,0.05797859,0.0508852,-0.06839816,-0.04116806,0.00403987,-0.03807518,-0.01545403,0.03951144,0.037427,-0.0068584,0.05552527,0.02009984,-0.00828961,0.06535386,0.04969237,0.06325983,0.00059561],"last_embed":{"hash":"19yxvr7","tokens":443}}},"text":null,"length":0,"last_read":{"hash":"19yxvr7","at":1753423634013},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>","lines":[49,81],"size":6578,"outlinks":[{"title":"<u><i><b>gambler's fallacy</b></i></u>","target":"https://saliu.com/gamblers-fallacy.html","line":3},{"title":"The gambler's fallacy is disproved by gambling software as events fall within one standard deviation from norm.","target":"https://saliu.com/ScreenImgs/gambler-fallacy.gif","line":11},{"title":"<u><i><b>chairman showed hostile reaction</b></i></u>","target":"https://saliu.com/bbs/messages/588.html","line":13},{"title":"<u><i><b>I was banned by a casino pit boss</b></i></u>","target":"https://saliu.com/winning.html","line":15},{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":17},{"title":"<u><i><b>Blackjack Streaks, Software, Probability of Consecutive Losses / Wins, Stats</b></i></u>","target":"https://saliu.com/gambling-lottery-lotto/blackjack-report.htm","line":18},{"title":"<u><i><b>baccarat</b></i></u>","target":"https://saliu.com/winning_bell.html","line":19},{"title":"_**The Best-Ever Roulette Strategy, Systems**_","target":"https://saliu.com/best-roulette-systems.html","line":20},{"title":"<u><i><b>Craps Strategy, Systems Based on Fundamental Formula of Gambling</b></i></u>","target":"https://saliu.com/bbs/messages/504.html","line":21},{"title":"_**The Best Blackjack Strategy, System Tested with the Best Blackjack Software**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":22},{"title":"_**blackjack card-counting**_ \"bishops\" or \"gurus\"","target":"https://saliu.com/bbs/messages/511.html","line":25},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/gambling-fights.html","line":25},{"title":"The best probability software calculates winning and losing streaks based on mathematical formulae.","target":"https://saliu.com/ScreenImgs/streaks-gambling.gif","line":30},{"title":"The best strategy in sports betting applies records, scores, results and streaks.","target":"https://saliu.com/HLINE.gif","line":32}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06565325,-0.0400952,-0.02364193,0.03261888,-0.0366067,0.08033932,0.02951778,0.03376737,0.06656511,-0.00137461,-0.03409537,0.0088104,-0.01260467,0.04269481,-0.03925342,-0.01250473,0.000842,0.02540903,-0.02449468,0.02974821,0.07170111,-0.0860532,-0.02936761,-0.08408125,0.06142724,-0.03175659,-0.03132971,-0.07100992,-0.05835994,-0.21289247,-0.0244652,-0.02533139,-0.0292132,-0.06906603,-0.09816567,-0.0415279,-0.0161743,0.06753782,-0.01298642,0.04273371,0.0246802,0.00581414,-0.01505308,0.00818662,-0.00131926,-0.02160721,0.00154902,0.02077876,0.02599475,-0.02455567,-0.05659885,0.04445631,-0.00954023,-0.02402365,0.06104698,-0.0249588,0.04815215,0.08943557,-0.0057584,0.02466753,-0.01592475,0.04954983,-0.18111876,0.04763936,-0.02493199,0.03895868,-0.00803644,0.0135455,-0.02580527,0.07084596,0.02114441,0.03748484,-0.03284479,0.06736637,0.04517536,-0.0360792,0.01290647,-0.03986647,-0.01492079,0.06449835,0.01276824,-0.02612795,-0.00803353,-0.07469361,0.00356066,-0.00638816,-0.01227195,-0.03056743,0.06265641,-0.02721376,0.06126767,0.06666051,0.0265637,-0.0188975,0.02923368,0.00497697,0.04379759,-0.02163134,0.01482894,0.07416464,0.00362742,-0.01683694,-0.01597911,0.02716091,0.073244,-0.04589709,-0.04804009,-0.04555819,0.00399552,0.01504572,-0.01004796,0.02924197,0.10749531,-0.01683117,-0.01856821,0.01737432,-0.00156368,-0.00460829,-0.01362295,0.02953194,-0.04433354,0.07083673,0.01955524,0.00265188,0.00668059,-0.07082996,-0.02030113,0.06736441,0.01534438,0.00895422,0.01268016,-0.0391468,-0.10310646,-0.01387544,-0.06128025,-0.0262228,0.01651221,0.04160392,0.0178189,-0.00548354,-0.01200566,-0.02371151,0.06172821,-0.11826091,0.00695425,0.07044601,0.02763533,0.02278232,0.02182258,-0.02246944,0.00631489,0.0019798,-0.01201612,-0.07373282,-0.01130669,-0.00463971,0.0458499,0.07597718,-0.05440833,-0.02704167,-0.06071969,-0.03475809,-0.0130079,0.10002013,-0.04738311,-0.07309143,-0.05685451,-0.00869565,-0.02143182,-0.09970613,0.00417416,-0.02838292,-0.05481144,0.00512324,0.07453199,-0.00173099,-0.06508078,-0.09417363,0.02699421,0.00818309,0.01310219,-0.06209044,-0.03165546,0.02158267,-0.00990791,-0.02996688,0.01460524,-0.0530161,0.06649865,0.02827799,-0.05839526,-0.01795756,-0.07745942,0.01802505,-0.01446417,-0.02449227,-0.01049756,-0.0476683,0.03517634,-0.02292161,-0.06476033,-0.0432353,0.00386623,-0.00045499,-0.03365489,0.06271199,0.03543355,-0.0874114,0.10534665,-0.00146542,0.01499823,-0.02773601,-0.0021342,0.06398722,-0.04493991,0.03066452,0.03617536,0.04187589,-0.04625121,0.01115375,0.0487585,0.0308693,-0.03186234,-0.17529772,-0.08484623,-0.07660546,0.00101461,0.07111362,0.01597592,0.0093702,0.00027731,0.03515289,0.07346181,0.03461409,-0.06469952,-0.00832792,0.02804986,-0.00048609,0.00112409,-0.10798708,-0.04284,-0.05891095,0.0306405,-0.02287933,0.00589548,-0.07889065,-0.06836976,0.05358695,0.00055535,0.16228004,0.06086534,-0.00032101,0.0127397,0.03201156,0.03997644,-0.01404502,-0.01759942,0.03291079,-0.0083537,0.03866864,-0.04262908,-0.05693346,0.02381721,-0.00393097,0.06541272,0.0243291,-0.07847257,-0.10892318,0.00738615,-0.02403619,0.09152177,0.01199473,0.08307928,0.10084969,0.00775194,0.06479481,-0.01602256,0.06101014,-0.01065172,-0.07068295,-0.01276535,0.02476993,0.05520478,-0.02107837,-0.03015462,0.05845887,-0.01708919,0.07883735,0.02704711,0.02333503,-0.01847471,0.03032849,0.00477265,-0.02420174,0.08107051,0.00801662,-0.04430922,0.05945817,0.03548007,0.06084347,-0.10066213,-0.01932661,0.01232047,0.03082851,-0.10564903,0.05460763,0.07212957,0.02550034,-0.00255314,0.03467434,0.03736853,-0.01029802,0.00001354,-0.02303994,0.01361579,-0.06616115,-0.02482003,-0.03926338,0.02656905,-0.25923091,0.02669436,-0.02089011,0.04532716,-0.02202048,-0.02884301,0.04811925,0.01150609,-0.03095816,0.01004908,0.03471915,0.02815401,0.00993459,0.01368149,-0.02335692,-0.04013072,-0.0038227,-0.08136032,0.08351052,0.02899818,0.04845427,0.08658283,0.20502016,-0.04449157,0.01362243,0.00048823,0.03697733,0.02859019,-0.02518449,-0.00177447,0.00861284,0.02245159,0.03979871,-0.00663124,0.006778,0.04166362,0.00516953,0.04514653,-0.02036405,-0.02497534,-0.024412,-0.02915467,0.0659112,0.04615785,0.16411722,-0.03199727,-0.01126432,-0.0301939,0.05810153,0.05261053,-0.06785808,-0.03979369,0.00210023,-0.03789969,-0.01492331,0.03643605,0.03386082,-0.00670785,0.05522377,0.01779277,-0.00818864,0.06748413,0.05211538,0.0628191,0.00133938],"last_embed":{"hash":"16kkzlp","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"16kkzlp","at":1753423634188},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{1}","lines":[51,64],"size":3365,"outlinks":[{"title":"<u><i><b>gambler's fallacy</b></i></u>","target":"https://saliu.com/gamblers-fallacy.html","line":1},{"title":"The gambler's fallacy is disproved by gambling software as events fall within one standard deviation from norm.","target":"https://saliu.com/ScreenImgs/gambler-fallacy.gif","line":9},{"title":"<u><i><b>chairman showed hostile reaction</b></i></u>","target":"https://saliu.com/bbs/messages/588.html","line":11},{"title":"<u><i><b>I was banned by a casino pit boss</b></i></u>","target":"https://saliu.com/winning.html","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05580304,-0.01210474,-0.04785895,0.03392982,-0.00796208,0.06820089,0.02900611,0.03514314,0.05804294,-0.00474643,-0.02562791,-0.00881749,-0.02912543,0.04340721,-0.05344508,-0.04672294,0.02121777,0.03587624,-0.00945283,0.01024985,0.07206049,-0.07681011,-0.04436269,-0.09049501,0.051576,-0.01272527,-0.05372502,-0.06346495,-0.05876547,-0.20584501,-0.03487,-0.04278305,-0.00679236,-0.06277155,-0.11064699,-0.0440537,-0.02178795,0.06200904,-0.01995599,0.04079643,0.00991372,-0.00283426,-0.02460826,0.00510246,-0.01129704,-0.01864514,0.03017833,0.01011447,0.03335667,-0.0240131,-0.05357195,0.02224216,-0.01744915,-0.0083483,0.07304405,-0.01952175,0.05391458,0.08392075,-0.03776743,0.00622438,-0.01164446,0.04424718,-0.20923199,0.04184268,-0.04947565,0.05921995,-0.01152751,0.01367686,-0.01488067,0.07049394,0.02653584,0.03269405,-0.05448156,0.0600007,0.04108455,-0.03070044,0.01193594,-0.0510859,-0.01358152,0.04237907,-0.01257608,-0.03273258,0.01712882,-0.07375345,0.034906,0.02357407,-0.01223455,-0.00678261,0.06726178,-0.04415872,0.07501607,0.02924293,0.03850248,-0.020047,0.0076197,-0.00353994,0.02979502,-0.04320323,0.03931302,0.10837404,0.02580176,-0.02768944,0.0081005,0.03288175,0.06938536,-0.03646704,-0.02393402,-0.05783369,-0.02291864,0.03693604,0.02064103,0.00895635,0.08062939,-0.01775551,-0.01764586,0.01600071,0.00991711,-0.01611719,-0.00071231,0.04456935,-0.04449974,0.04689196,0.06772059,0.01034401,-0.00236817,-0.04996587,0.01687783,0.05236941,0.00175319,0.02395403,0.00201353,-0.01272094,-0.11025174,-0.01898075,-0.07869204,-0.03048783,0.03323864,0.03470269,0.00928736,-0.00866316,-0.04399879,-0.01198431,0.05604466,-0.14442386,0.00920147,0.09903037,-0.00167456,0.00772286,-0.00968533,-0.00118829,-0.01042149,-0.00081413,-0.00523022,-0.07307884,-0.04138493,0.00346602,0.0234616,0.06957413,-0.0296188,-0.00700558,-0.06756924,-0.07227962,-0.02127257,0.11568481,-0.00399127,-0.09679574,-0.06437223,0.01881573,-0.02899753,-0.0912064,-0.027261,-0.00723234,-0.06219576,0.02466152,0.05615379,-0.01698962,-0.05250116,-0.09087484,-0.00871006,0.0175353,0.02504238,-0.05129822,-0.0543243,0.01759554,0.02071059,-0.05786149,0.01018069,-0.06897026,0.07257959,0.03000972,-0.06779311,-0.02744969,-0.05198338,0.01656537,-0.01499372,-0.01959858,-0.00405636,-0.03988907,0.01279281,-0.00471671,0.00706324,-0.02377751,-0.01232675,0.0094258,-0.05105054,0.05699792,-0.01326378,-0.0730017,0.06741697,-0.00913761,-0.001013,0.01649131,0.05118262,0.03844086,-0.04156314,0.03152521,0.02004378,0.05898809,-0.06182767,0.04566136,0.0364874,0.02454276,-0.05550548,-0.18777311,-0.07177544,-0.05171281,0.00091774,0.04775065,-0.00043455,0.01367499,0.00603786,0.05820825,0.06955098,0.06474654,-0.03927551,0.00988953,0.04511274,0.00095786,0.02544162,-0.0672795,-0.07071103,-0.03587218,0.02995326,-0.00640605,-0.01959383,-0.01840691,-0.04819706,0.04593958,-0.00490304,0.15812017,0.04562927,0.01242957,0.02954172,0.02587068,0.01969939,-0.01533919,-0.0479921,0.02922194,0.00078458,0.02115648,-0.0406862,-0.04886354,0.00042791,-0.01837978,0.03724062,0.05634979,-0.07019255,-0.08782462,-0.0079202,0.00766174,0.03539263,0.00368603,0.04732787,0.08627158,0.02297066,0.04912942,-0.00930669,0.05845688,0.01344431,-0.08454732,-0.00741869,0.00005613,0.02712206,-0.02114897,-0.03126939,0.03571605,-0.03092823,0.06956599,-0.00253372,0.03282088,-0.00797504,0.03344746,0.00493168,-0.01456268,0.08613303,0.00251536,-0.01837568,0.06239448,0.0381296,0.07658375,-0.11139695,-0.02131054,0.00358301,0.02176439,-0.09961286,0.06240704,0.06652723,0.03424253,0.02363436,0.07106837,0.01011509,0.01972567,-0.01171798,-0.00175184,-0.00623841,-0.0495384,-0.02409846,-0.01629403,0.07899462,-0.26287121,0.00085896,-0.02074313,0.04404111,0.00094563,-0.03887229,0.0266605,-0.0451797,0.00611129,0.0316346,0.06394884,0.03344467,0.02540207,0.00700507,-0.02787924,-0.02765003,0.01166081,-0.07546734,0.08233768,0.06852739,0.00645051,0.07447042,0.22351529,-0.03623448,0.01762127,-0.00894585,0.01308189,0.01354642,-0.05286874,0.03191321,0.03263792,0.04498345,0.02987134,0.0330514,-0.03426281,0.04813685,0.01295611,0.03297718,-0.01004664,-0.0016848,-0.04990323,0.02140487,0.03285587,0.04743033,0.1399982,-0.01860826,-0.02532279,-0.06662202,0.08544023,0.03007513,-0.09982607,-0.0539847,0.00470873,-0.00615905,-0.00114303,0.06216787,0.02951763,-0.03788266,0.04136496,0.03372394,0.0086289,0.05241975,0.0415328,0.04440351,0.02562525],"last_embed":{"hash":"16hmo29","tokens":191}}},"text":null,"length":0,"last_read":{"hash":"16hmo29","at":1753423634357},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{2}","lines":[65,66],"size":465,"outlinks":[{"title":"_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":1},{"title":"<u><i><b>Blackjack Streaks, Software, Probability of Consecutive Losses / Wins, Stats</b></i></u>","target":"https://saliu.com/gambling-lottery-lotto/blackjack-report.htm","line":2}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05752826,-0.03403192,-0.04109073,0.01256917,-0.01931005,0.06294116,0.0274625,0.03677866,0.05178063,-0.00832166,-0.01093604,-0.00207061,-0.03085976,0.04696826,-0.02140841,-0.04790671,0.02562877,0.04574026,-0.01873039,0.02470669,0.08017809,-0.07466991,-0.06071721,-0.08743882,0.06264158,-0.0033509,-0.05265208,-0.0650303,-0.03897827,-0.18221417,-0.03473614,-0.01997337,-0.03539237,-0.05985502,-0.12033401,-0.04735454,-0.01132461,0.06695414,-0.01072638,0.04362312,0.00698145,0.00367627,-0.02027991,0.00666516,-0.02619511,-0.02163636,0.02640239,0.02405078,0.04040417,-0.03113217,-0.03146173,0.03825443,-0.02425082,-0.03272429,0.08695086,-0.00667154,0.05845876,0.07620583,-0.04212376,0.00384719,-0.01881151,0.05475721,-0.19524233,0.06840408,-0.03111571,0.05631,0.01591336,-0.00669863,-0.02495166,0.08230542,0.02944989,0.02934758,-0.03455672,0.06489646,0.04163505,-0.02451721,0.00001134,-0.04723727,-0.01023292,0.06609984,-0.01159457,-0.03740155,0.0173039,-0.07957371,0.03413882,0.01781259,-0.03240303,-0.02812267,0.06657978,-0.05248677,0.06581403,0.04007872,0.05521823,-0.02592614,0.01981948,0.0070845,0.0219419,-0.03765758,0.05781266,0.11230102,0.01637107,-0.0299785,0.02027305,0.02634959,0.06033136,-0.03006904,-0.04068852,-0.05825375,-0.00911414,0.03266412,0.00033124,-0.01028035,0.1053941,-0.00318124,-0.0143904,0.01921853,0.00826466,-0.02799184,0.03660176,0.02207535,-0.05668189,0.04992329,0.05164086,0.00311961,-0.00060604,-0.07073431,-0.00011697,0.04536072,-0.01246089,0.03344038,0.00466351,-0.02967341,-0.07913916,-0.03350041,-0.07843079,-0.02618402,0.0318648,0.03895054,0.01087096,0.01080146,-0.02776964,-0.03354,0.06047918,-0.15766767,0.01292613,0.09424599,0.01006695,0.03841681,-0.00864531,-0.00150283,0.00228788,-0.00076161,-0.00685535,-0.04512077,-0.05146142,0.00379788,0.03452893,0.04854459,-0.03503792,0.0018688,-0.08136092,-0.05372722,-0.03389691,0.0959999,-0.03629043,-0.10771172,-0.07172145,-0.00709516,-0.03375507,-0.08536522,-0.03291952,-0.02153922,-0.08435523,0.02534742,0.10227049,-0.0224956,-0.03017513,-0.09419775,-0.01741279,0.01712992,0.02406679,-0.04481098,-0.06213121,0.00977636,0.03271672,-0.04039909,0.01503914,-0.06546928,0.08271317,0.04044528,-0.07283004,-0.0031032,-0.05222677,0.02078151,-0.02094819,-0.00439859,-0.01484624,-0.03949977,0.03081829,0.01323741,-0.00244336,-0.02249066,-0.02705926,0.00256798,-0.03452097,0.05344484,0.00474391,-0.08717407,0.05707335,0.01001667,-0.01042977,0.02595284,0.04723949,0.04101569,-0.04962781,0.01455406,0.02637941,0.02885965,-0.08104035,0.02729618,0.05489298,0.00644386,-0.0561326,-0.17105155,-0.0697652,-0.05179844,-0.00653947,0.06245952,0.0167377,0.01540349,-0.00328033,0.02796115,0.09425767,0.05107575,-0.05239473,0.02958124,0.04134738,0.00257324,0.01668212,-0.08189931,-0.05944285,-0.03153328,0.0201015,0.00015676,-0.02121503,-0.02362769,-0.05403082,0.04435383,0.00014729,0.17224459,0.04117772,0.00054013,0.0316157,0.03088063,0.02131751,-0.01437502,-0.04426472,0.01527747,-0.01431822,0.01431003,-0.04601629,-0.03527626,0.00526272,-0.00204921,0.03929766,0.0333707,-0.06935123,-0.08692677,-0.01345687,-0.01677052,0.036395,0.00674052,0.04787343,0.08451983,0.02217061,0.02971797,-0.00712423,0.03912128,0.01745597,-0.07334431,-0.02111497,-0.0114364,0.03489478,-0.02688634,-0.02722316,-0.00335869,-0.02105395,0.05328018,-0.01351528,0.03684896,-0.02565763,0.04398733,-0.00553949,-0.03663218,0.07774315,0.02613134,-0.00536407,0.08326504,0.02575765,0.08010393,-0.07312908,-0.01846067,-0.00019408,0.00122808,-0.10047927,0.06080508,0.06020815,0.01515036,0.00736387,0.07036166,-0.00795565,-0.02258907,-0.02098725,-0.01050616,0.00079654,-0.07087431,-0.0199002,-0.00397186,0.05927916,-0.25996631,0.02399694,-0.01364752,0.05132785,-0.00691868,-0.03907799,0.00460281,-0.02485401,-0.00869569,0.02245042,0.0767267,0.04575383,0.01786731,0.01724698,-0.00951577,-0.02053116,0.02081499,-0.06984995,0.09090494,0.04673425,0.01370819,0.09033105,0.21987042,-0.02690632,0.03134105,-0.00634207,0.00429823,0.02296578,-0.05216052,0.02195048,0.02650831,0.04802607,0.0155824,0.00912898,-0.03084744,0.0406481,0.00755884,0.06008212,-0.01777511,0.00791775,-0.0564585,0.00314252,0.04476917,0.05194211,0.13956666,-0.02202689,-0.02203196,-0.06340466,0.07922138,0.03197428,-0.08506126,-0.04861942,0.0062141,-0.0061744,-0.00222482,0.05659113,0.03419222,-0.04285224,0.04160481,0.03314476,0.00641395,0.05919407,0.05834737,0.069869,0.0206024],"last_embed":{"hash":"2w39hd","tokens":117}}},"text":null,"length":0,"last_read":{"hash":"2w39hd","at":1753423634418},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{4}","lines":[68,68],"size":223,"outlinks":[{"title":"_**The Best-Ever Roulette Strategy, Systems**_","target":"https://saliu.com/best-roulette-systems.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06594807,-0.02474698,-0.04224488,0.01901797,-0.01698486,0.06161829,0.04116874,0.03634343,0.05804383,-0.01111574,-0.02475931,-0.02138796,-0.02980318,0.03575255,-0.02656845,-0.05158503,0.04525246,0.01876221,0.0017662,0.04398634,0.09069822,-0.06182262,-0.07250079,-0.07252623,0.06923252,-0.03059968,-0.05196479,-0.04248718,-0.06308848,-0.18734048,-0.01286525,-0.02085515,-0.031076,-0.04854408,-0.1144609,-0.05503304,-0.00026193,0.08403528,-0.00191429,0.03718654,0.02084689,0.01105647,-0.01102528,-0.01544029,-0.02003684,-0.02173635,0.00106706,0.02685413,0.05139021,-0.03002723,-0.02894911,0.03434416,-0.03680826,-0.03508035,0.08010717,-0.01712034,0.0680153,0.07983727,-0.03006305,0.01346516,-0.01852003,0.03766515,-0.20018677,0.05571241,-0.02027318,0.04998084,-0.0012552,0.01312656,-0.03605554,0.08151895,0.01635696,0.0239137,-0.02155125,0.08202964,0.03145367,-0.02525341,-0.01211357,-0.04693904,-0.01587131,0.05275668,-0.02254813,-0.0267536,0.01103224,-0.06572901,0.02086901,0.03303096,-0.04598961,-0.01329503,0.06664463,-0.04822905,0.08803301,0.03717248,0.04497833,-0.03408282,0.01678975,-0.01839286,0.01126515,-0.05601995,0.02535397,0.0972218,0.01589136,-0.02080629,-0.00356916,0.01613887,0.07112632,-0.02966131,-0.03046374,-0.04733252,-0.00625803,0.03155786,0.00627046,-0.01179787,0.0884381,-0.00988763,-0.02397557,-0.00583119,0.00450634,-0.02078237,0.03203289,0.01570985,-0.04368931,0.03693585,0.04842915,0.01463675,0.00518041,-0.0713895,-0.01045252,0.05293031,-0.02154548,0.02907588,0.01473281,-0.02142574,-0.09077376,-0.034834,-0.06874572,-0.03061149,0.02931296,0.04331585,0.01460809,0.00964705,-0.02628163,-0.03053558,0.06296623,-0.12780775,-0.00244314,0.07351937,0.00966396,0.01242234,-0.01846087,0.0034496,0.00242998,0.00340697,0.01301036,-0.02916006,-0.04749871,-0.01138134,0.01582189,0.06498291,-0.02467801,0.00401923,-0.08508504,-0.05340642,-0.02373453,0.10714379,-0.02704716,-0.08791602,-0.06644138,-0.00875225,-0.02588304,-0.08142871,-0.02239388,-0.02194244,-0.08191876,0.02972278,0.07894795,-0.00814252,-0.01846931,-0.08381606,-0.01132787,0.02312248,0.03834107,-0.04959714,-0.06749243,0.02060039,0.03108251,-0.04974192,-0.00565899,-0.07249766,0.06880441,0.02365111,-0.08683697,-0.01642016,-0.05039845,0.02687487,-0.03262516,-0.01350068,-0.02237158,-0.03911145,0.02389627,0.02653344,-0.00727624,-0.03411353,-0.03506237,-0.00937233,-0.03436655,0.05348461,-0.00495769,-0.09540708,0.06715191,-0.00496647,0.00335023,0.03460472,0.03884368,0.04591108,-0.04138417,0.00993216,0.03320314,0.0322484,-0.07054154,0.03867975,0.03570431,0.03483587,-0.05466342,-0.18083672,-0.07441564,-0.05168678,-0.01341232,0.06692079,0.01797556,-0.00387765,-0.00511,0.04479149,0.0996187,0.04682238,-0.05762342,0.0295154,0.03775742,-0.00881584,0.00856134,-0.10049112,-0.07033981,-0.0369576,0.03788564,-0.00743807,-0.01033333,-0.03052531,-0.05802051,0.05917159,0.01370922,0.17097555,0.02699184,0.01197303,0.04440723,0.04920168,0.03814258,-0.02371545,-0.04065131,0.01146777,-0.00661251,0.01840284,-0.0268357,-0.03136842,-0.00315675,-0.01571159,0.0358365,0.01844578,-0.07738472,-0.09089508,0.00993586,-0.0271016,0.01987808,-0.0047039,0.05546634,0.08303564,0.02455379,0.03909788,-0.00468531,0.06325267,0.02532869,-0.07135021,-0.00181151,0.00750146,0.03523411,-0.04706035,-0.02892941,0.0212088,-0.02438737,0.03571228,-0.02055834,0.0407685,-0.02434095,0.03010463,0.01660471,-0.02247347,0.09001024,0.01117767,-0.03567798,0.06397736,0.0378786,0.06853781,-0.07235502,-0.02376098,-0.00832264,0.00022755,-0.09641553,0.07343163,0.04721745,0.00892118,0.01186587,0.07537998,0.00074298,0.01568801,-0.00545504,-0.03366426,0.00769281,-0.06717616,-0.00385533,-0.0022169,0.05807638,-0.26161426,0.01609459,-0.01093246,0.05361834,-0.00626332,-0.04508169,0.0132302,-0.01635037,0.01053378,0.03133272,0.08884991,0.04203475,0.02506414,0.01771258,-0.03623961,-0.01535802,0.0108039,-0.07625514,0.10113449,0.05907104,0.01555691,0.06165845,0.22366285,-0.03872635,0.01841824,-0.00458107,-0.00120362,0.02426516,-0.05347291,0.04303597,0.04168218,0.0413397,0.0280432,0.00953009,-0.02817645,0.02559391,0.00608768,0.04562413,-0.0065102,0.01921283,-0.07375019,0.01337378,0.04485327,0.06569151,0.13274397,-0.00887207,-0.02581371,-0.06577232,0.08232476,0.03723507,-0.10102281,-0.0418166,0.0040883,-0.01179995,0.01445934,0.06031121,0.02921237,-0.03476448,0.03855291,0.05936393,0.01571142,0.04310094,0.04829422,0.05517985,0.03052176],"last_embed":{"hash":"1av6ncu","tokens":120}}},"text":null,"length":0,"last_read":{"hash":"1av6ncu","at":1753423634456},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{5}","lines":[69,69],"size":200,"outlinks":[{"title":"<u><i><b>Craps Strategy, Systems Based on Fundamental Formula of Gambling</b></i></u>","target":"https://saliu.com/bbs/messages/504.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07218122,-0.00931576,-0.04176363,0.01228589,0.00225054,0.04694786,0.03497245,0.0072859,0.0286808,-0.01387853,-0.00383839,0.01469541,-0.02959519,0.02656223,-0.01971731,-0.04336347,0.0338539,0.02817443,0.00311387,0.04715794,0.04228943,-0.06156948,-0.05368274,-0.07024416,0.07249258,-0.0251128,-0.04489795,-0.04600203,-0.0648662,-0.20308194,-0.01548965,-0.06286824,-0.0121845,-0.05117128,-0.0846324,-0.0671473,-0.01696759,0.06395645,-0.0188639,0.02894613,0.0172568,0.01873066,-0.00238355,0.00111391,-0.01807827,-0.02363034,0.04609112,0.05508725,0.07099257,-0.00793871,-0.04153624,0.04273183,-0.0317197,-0.01552255,0.06321535,-0.01213173,0.05947496,0.07543607,-0.03022119,0.00273253,-0.00646937,0.05001922,-0.17978962,0.05650115,-0.02167294,0.05046531,0.02031971,-0.01824462,-0.02075816,0.05561017,0.01412294,0.02597962,-0.03008101,0.07339124,0.04039517,-0.03487987,-0.00408445,-0.06123422,-0.01117628,0.04137092,-0.0160962,-0.04527986,0.02952765,-0.07165211,0.02896755,0.03378438,-0.02005999,-0.00427162,0.06868301,-0.0580579,0.037125,0.03534302,0.0477714,-0.03715291,0.01797911,-0.02259233,0.01671153,-0.05273401,0.01709544,0.11290526,0.00398377,-0.02543923,-0.0119609,0.012877,0.068792,-0.03667212,-0.02713654,-0.04231662,-0.05620308,0.04192799,0.01748699,-0.00259352,0.11142199,-0.0055182,-0.02930373,0.02367229,0.01050273,-0.03499747,0.03447781,0.00911469,-0.02438436,0.04711588,0.06815185,-0.02460053,-0.01746713,-0.0856306,0.02234354,0.03035664,-0.01622076,0.02709597,-0.0240427,-0.05593865,-0.07286874,-0.04882401,-0.06231258,-0.01668022,0.02515107,0.05585355,0.01239936,-0.0057014,-0.04391072,-0.01174753,0.08042879,-0.12696509,0.01327464,0.04521719,-0.01460789,0.02236565,-0.0208844,0.0194413,-0.01155847,-0.01036119,-0.01706106,-0.04834758,-0.05183883,-0.0045879,0.03825,0.07744476,-0.02564797,-0.01123284,-0.07830647,-0.04400244,-0.0296348,0.09106775,-0.02535183,-0.09694462,-0.06350259,0.00431483,-0.03645074,-0.08724295,-0.02695926,-0.03606258,-0.07503611,0.02209489,0.03977593,-0.02750112,-0.08558214,-0.06043751,-0.01776918,0.01984025,0.01743161,-0.04208093,-0.06704285,0.00105782,0.05168552,-0.06293183,0.02340449,-0.05430267,0.08318155,0.00349877,-0.0652101,-0.00392665,-0.03891971,0.03057479,-0.02692455,0.00379284,-0.00363782,-0.04520518,0.05031872,0.01442523,0.00029228,-0.0384827,-0.03250045,-0.00814904,-0.01985514,0.05755141,-0.01014311,-0.10319019,0.08148675,0.00121635,0.00076832,0.00252603,0.05898756,0.04048921,-0.03983591,-0.00590418,0.0035095,0.03852014,-0.06723601,0.05913343,0.01960132,0.03553922,-0.05473191,-0.18723992,-0.06869692,-0.05625154,-0.00273256,0.05548133,-0.03235879,-0.00542415,0.00007058,0.05755523,0.07826023,0.03656535,-0.06120006,0.03985568,0.06592869,-0.00264228,0.03517026,-0.07037954,-0.04030575,-0.05361961,0.02328649,-0.0057898,-0.01420675,-0.01232091,-0.06354408,0.06952284,-0.00771154,0.16732538,0.02506334,0.01921825,0.06794452,0.02285244,0.04961036,-0.01035959,-0.07101841,0.03612177,0.02289587,0.02592155,-0.05111584,-0.04352096,-0.00207808,-0.03698953,0.05909419,0.03652499,-0.11147878,-0.09012406,0.00502456,-0.00193046,0.06863464,-0.02576409,0.03406272,0.06933428,0.02960488,0.04183776,-0.00232135,0.05495748,0.01228548,-0.06141055,0.00408637,0.01929847,0.04237894,-0.02634098,-0.0233627,0.0165291,-0.01720108,0.04016232,-0.01066687,0.03595353,-0.00801904,0.06780215,-0.00634189,-0.01562553,0.06781999,0.00322009,0.00858713,0.10493463,0.04119891,0.09227939,-0.09022629,-0.00782848,-0.00958395,-0.01749995,-0.07411432,0.07238545,0.06926037,0.03191419,0.01205272,0.05958368,0.02417593,0.02333394,-0.02654232,-0.01416323,0.02887553,-0.06871587,-0.0327749,0.00083306,0.07677226,-0.24772906,-0.00039521,-0.02691791,0.05387413,-0.03189172,-0.06776004,0.02463486,-0.01599615,-0.00534199,0.02600826,0.06549428,0.02790644,0.03107288,0.01526819,-0.02463822,-0.04255506,0.01091313,-0.04539289,0.10715582,0.06177414,-0.01030313,0.08520392,0.22056977,-0.02272709,0.03941873,-0.01239537,0.00500017,0.03417525,-0.03443826,0.04291362,0.0314275,0.04432385,-0.00762538,0.01164989,-0.01382078,0.03452148,0.00342679,0.02944604,-0.01979514,0.00578512,-0.07628361,0.02029937,0.02541032,0.05279746,0.11399249,-0.03521718,-0.01232049,-0.05131053,0.07463876,0.03436574,-0.07784992,-0.03861285,0.01852565,-0.04546479,0.02850336,0.06202319,0.02302556,-0.03884076,0.05142598,0.01174835,0.01713017,0.08208553,0.06878506,0.03803219,0.02225218],"last_embed":{"hash":"1b3e34c","tokens":150}}},"text":null,"length":0,"last_read":{"hash":"1b3e34c","at":1753423634501},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{6}","lines":[70,70],"size":373,"outlinks":[{"title":"_**The Best Blackjack Strategy, System Tested with the Best Blackjack Software**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05668444,-0.04508194,-0.03788185,0.00813733,-0.05017682,0.07851827,0.03949481,0.03069491,0.08215541,-0.02583702,-0.0178482,-0.01402878,-0.02917507,0.05235845,-0.03733728,-0.03741755,0.01216313,0.04028283,-0.03186026,0.02177902,0.08597085,-0.10572824,-0.06084916,-0.08879162,0.05439049,-0.00755682,-0.04644036,-0.05140251,-0.04592301,-0.20616461,-0.0188655,-0.02003371,-0.03972783,-0.05308834,-0.09786444,-0.05996005,-0.04161739,0.06293305,-0.00290992,0.04540124,0.01161606,0.01559077,-0.02022717,0.01327771,-0.03070817,-0.01958577,0.0054026,0.02136343,0.02997371,-0.01788663,-0.03250872,0.03541643,-0.01361058,-0.0306976,0.06859977,-0.02066397,0.04772239,0.08061999,-0.0400839,0.01484927,-0.02191356,0.05429635,-0.19836529,0.04251503,-0.02485289,0.06558397,0.00782484,0.01688165,-0.00716101,0.09601519,0.0259389,0.04460003,-0.02964651,0.08421193,0.02875542,-0.02295286,-0.008009,-0.0485792,-0.00981224,0.04716859,-0.02178713,-0.0426659,0.02719872,-0.07186985,0.01661183,-0.0086766,-0.02984039,-0.04743618,0.05951944,-0.03372984,0.07777444,0.05300141,0.00945691,-0.05779289,0.01715133,-0.01066683,0.02921067,-0.04123701,0.01963146,0.10615728,0.01923717,-0.04290808,-0.02857157,0.03924578,0.04416228,-0.01970552,-0.04810231,-0.05838679,-0.03440679,0.02393762,0.00093207,0.00483794,0.07155668,-0.01415272,-0.02660604,0.02482477,0.01763927,-0.02006229,0.01558058,0.00454147,-0.04956718,0.04624229,0.05993188,-0.00266801,-0.01296138,-0.04724648,-0.00920471,0.06751709,-0.01287501,0.02045837,0.00533105,-0.0269041,-0.08926143,-0.0182942,-0.06088976,-0.02940174,0.03794788,0.03856551,0.01898515,0.01447309,-0.03213309,-0.01122557,0.07388117,-0.13030647,0.02373561,0.10183451,-0.00337078,0.03290362,-0.01358305,-0.02290538,0.01130097,0.00181438,-0.01444271,-0.05416967,-0.03384804,-0.01736584,0.02326836,0.05381773,-0.02663221,0.00434095,-0.0773739,-0.04203467,-0.01771517,0.11113799,-0.02821916,-0.08168884,-0.05057183,0.0017689,-0.02878884,-0.08923548,-0.00893313,-0.02338828,-0.06422538,0.03491547,0.08375138,0.00450606,-0.05061207,-0.06646515,0.01514345,0.02119828,0.04364596,-0.05638891,-0.06215168,0.00610101,0.0239656,-0.0321353,0.00632817,-0.06265732,0.07191838,0.00732479,-0.076734,-0.0022698,-0.0629262,0.01121159,-0.01707592,-0.03057629,-0.00804758,-0.02893785,0.01442696,-0.00240991,-0.00904391,-0.04622881,-0.02666212,0.01013346,-0.01117465,0.0634742,0.0119343,-0.07846015,0.07493953,-0.00993358,0.00446744,-0.01183675,0.04006477,0.04407752,-0.0346192,0.02180304,0.02108274,0.04816815,-0.04865093,0.03964985,0.05788354,0.0055991,-0.04111768,-0.17480376,-0.07090682,-0.0428099,0.02604883,0.07583465,0.0090661,0.01597936,-0.0090571,0.02708672,0.06124272,0.03686817,-0.06747861,-0.00328164,0.01567226,-0.00723461,0.01212936,-0.09604304,-0.06556418,-0.03970511,0.03592672,-0.01189789,-0.02328946,-0.06067804,-0.04130046,0.04299674,0.00126728,0.16540168,0.0229781,-0.04132823,0.03345297,0.03040362,0.00340889,-0.02192619,-0.00767582,0.01989022,-0.00486142,0.01726742,-0.02558326,-0.0529848,0.00958744,0.0012172,0.05115223,0.02100253,-0.0436029,-0.09599855,0.01077426,-0.00730188,0.07247067,0.01179966,0.046595,0.10240515,0.02279937,0.03247698,-0.02342535,0.0555572,-0.00064278,-0.06511771,0.00036439,-0.00407543,0.05077709,-0.01310253,-0.03332985,0.02266546,-0.0317347,0.05246142,-0.00930401,0.04641147,-0.01628109,0.04216995,0.00661735,-0.01903631,0.08841001,0.01805994,-0.01494403,0.10512474,0.00934969,0.07596979,-0.08778144,-0.01109817,-0.01640454,0.02436316,-0.11422691,0.05059617,0.06284793,0.01410165,0.01651974,0.0658057,0.03230232,0.01320265,0.02301545,-0.00805513,0.00478175,-0.06517719,-0.01440153,-0.02491157,0.0482972,-0.26861376,0.02784815,-0.04024794,0.07489996,-0.03284199,-0.02559061,0.02771623,-0.00186937,-0.02430462,0.03717665,0.05055807,0.04523248,0.03671258,0.03980396,-0.0268236,-0.03981057,0.01451393,-0.0798872,0.08180209,0.02957477,0.03730977,0.08343145,0.21553251,-0.03946067,0.04360537,0.00743072,0.01421763,0.02314756,-0.02517798,-0.00815722,0.04069691,0.02678231,0.0203431,0.00808644,-0.00975468,0.03665928,0.00092514,0.05649534,-0.00070436,-0.00168498,-0.04160553,-0.02612508,0.0606503,0.05322292,0.14605132,-0.03978562,-0.0305908,-0.0628706,0.06592164,0.03527026,-0.09973014,-0.01808669,0.00439595,-0.00924627,0.01044165,0.06006309,0.03991345,-0.01834696,0.06070394,0.04196842,0.01490379,0.0591384,0.04744372,0.03417743,-0.00050892],"last_embed":{"hash":"sp5pfw","tokens":128}}},"text":null,"length":0,"last_read":{"hash":"sp5pfw","at":1753423634553},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{8}","lines":[72,72],"size":301,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04526553,-0.00385189,-0.04640293,0.02160986,-0.00538339,0.0506727,0.05007943,0.06201255,0.02478611,-0.02513465,-0.04394655,-0.00759418,-0.05153698,0.04671577,-0.02152374,-0.03848062,0.00606014,0.00732624,-0.00594046,0.02522793,0.06598923,-0.05445006,-0.02801032,-0.07386515,0.06622157,-0.02837634,-0.04828454,-0.03003152,-0.04594341,-0.17641774,-0.00409451,-0.0226157,-0.04556742,-0.06482698,-0.09978382,-0.03874118,-0.00629463,0.07348735,-0.01735636,0.03535827,0.03034598,0.04553075,-0.00123672,-0.01822668,-0.01136025,-0.02210328,0.02576255,-0.0073271,0.03209958,-0.01248699,-0.0366244,0.03285654,-0.04380365,-0.03355522,0.07161962,-0.05389357,0.05174774,0.07320201,-0.0395017,0.034439,-0.02789142,0.0837549,-0.19728833,0.06762877,-0.03472737,0.0369272,-0.00267429,0.02267637,-0.01580539,0.08735205,0.04844372,0.02114038,-0.0137026,0.0722655,0.04822806,-0.00897498,-0.02591642,-0.03431935,-0.00401648,0.07584018,-0.01967767,-0.04458208,-0.01049506,-0.06276597,0.02760213,0.0093372,-0.01010746,-0.00521265,0.07818718,-0.04099059,0.08369192,0.03494751,0.04419982,-0.01093626,0.00745032,-0.01008273,0.02534733,-0.06794811,-0.04320802,0.08610556,0.02559479,-0.02426299,-0.03728476,0.0294572,0.07754062,-0.03373246,-0.02417508,-0.05741832,-0.03021392,0.02305153,0.00733038,-0.02812269,0.07776852,0.00365343,-0.01445063,0.01473917,0.02126422,-0.0378059,-0.0164829,0.01629486,-0.04071992,0.02967998,0.04720435,0.00797227,0.00813497,-0.09012166,-0.01701759,0.07393754,-0.00722863,0.04015071,0.00317336,-0.01831112,-0.08703857,-0.04734488,-0.06260546,-0.02087427,0.01708201,0.05280537,0.02107953,0.00624485,-0.00072714,-0.04423954,0.02824107,-0.09768107,0.03081238,0.07737746,0.00886883,-0.00007551,0.00464734,0.00899725,0.02225986,-0.0229515,-0.01082239,-0.0593436,-0.03739419,-0.00162591,0.03490292,0.06420679,-0.02577978,-0.0385071,-0.06602731,-0.03861622,-0.00868293,0.10576257,-0.04280611,-0.09120942,-0.05706604,-0.01478957,-0.04664037,-0.11323263,-0.01461725,-0.02001203,-0.07549637,0.03653993,0.05967523,-0.02295859,-0.0365089,-0.07588028,-0.01885509,0.04264278,0.04385073,-0.05995948,-0.05930836,0.01220763,0.00558327,-0.03019853,0.01270236,-0.06930053,0.07275089,0.05309334,-0.08341989,-0.00301165,-0.05815386,0.02766896,-0.03277672,0.02076999,-0.00483249,-0.01494696,0.04207731,0.00895291,-0.01549878,-0.036961,-0.05520237,-0.00973081,-0.03475619,0.05598086,0.00477538,-0.09678682,0.05042073,-0.02055721,-0.00453785,0.04245652,0.03932082,0.04453875,-0.02982045,0.03555895,0.04156123,0.07474387,-0.05420969,0.02076472,0.0787782,0.01563894,-0.00900703,-0.19757323,-0.06365127,-0.08775646,-0.0386688,0.02386439,-0.00639091,0.01667041,-0.01680709,0.00264168,0.09649859,-0.00616601,-0.06640222,0.01079532,0.03452959,-0.00459496,0.02419733,-0.11283125,-0.07942131,-0.0353384,0.05479016,-0.0467541,-0.02749752,-0.05276237,-0.05966318,0.04051099,0.02330999,0.16376737,0.07994109,-0.00378405,0.01327861,0.04316563,0.026288,-0.03147646,-0.07353325,0.00340555,-0.02424674,0.00791616,-0.01431677,-0.01485634,0.00431321,0.00564357,0.06476943,0.02853885,-0.08771192,-0.09386665,-0.01968964,-0.02124862,0.05544179,-0.02966247,0.05447973,0.09554709,0.03534976,0.04864991,0.00482191,0.05745722,-0.00672324,-0.09113952,-0.00575595,0.00015813,0.03963497,-0.03197435,-0.02827173,0.02017241,-0.03932637,0.06089828,0.00303785,0.04942372,-0.01168371,0.02163971,0.01289385,-0.06002161,0.06447842,0.02307851,-0.03898394,0.07079349,0.04411963,0.06490213,-0.0786245,-0.01698386,0.00955799,0.00481063,-0.0528695,0.08499562,0.04180285,0.00447581,0.01604805,0.06289008,0.01720081,0.01951653,-0.01936123,-0.0064796,0.01870276,-0.03672957,-0.01398086,-0.00765716,0.04071426,-0.25816664,0.02213661,-0.00706991,0.05499751,-0.00050841,-0.03080412,0.01052429,-0.02420384,-0.00240472,0.01289831,0.06779013,0.05968293,0.00475066,0.04197803,-0.04693795,-0.03267796,0.01702479,-0.06134138,0.05033427,0.05923575,0.0120087,0.0720521,0.22370952,0.01006084,0.02094083,-0.01155485,0.00819494,0.02779507,-0.05676126,0.03343744,0.02043865,0.0252865,0.02602466,-0.00207616,0.01161224,0.03630032,0.04576038,0.04910835,0.0020403,-0.00749827,-0.04603922,0.01958378,0.05819349,0.07794738,0.12076404,0.00366043,-0.06378404,-0.01816879,0.06664845,0.01406454,-0.07705757,-0.05787943,0.0178857,0.005194,-0.00024138,0.08202229,0.02156981,-0.04424307,0.02518859,0.0598999,0.01649777,0.06112177,0.03055043,0.0778477,0.02549715],"last_embed":{"hash":"8ayenn","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"8ayenn","at":1753423634606},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{9}","lines":[73,73],"size":357,"outlinks":[{"title":"_**blackjack card-counting**_ \"bishops\" or \"gurus\"","target":"https://saliu.com/bbs/messages/511.html","line":1},{"title":"_**Fundamental Formula of Gambling**_","target":"https://saliu.com/gambling-fights.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05915607,-0.02820769,-0.03496427,0.03616202,-0.03974394,0.04238451,0.06001942,0.0406991,0.05061049,-0.00584628,-0.01786325,-0.01015877,-0.01263474,0.05205445,-0.0222037,-0.03186974,0.00214202,0.01726132,0.00101853,0.032567,0.07137523,-0.08376791,-0.0568043,-0.05020046,0.05855537,-0.01737676,-0.0545843,-0.05587758,-0.05580092,-0.21262972,-0.00571356,-0.01717399,-0.04027246,-0.05015327,-0.0875873,-0.04248617,-0.01616581,0.06831376,-0.02275113,0.03124378,0.0141942,0.01285711,-0.01557229,0.01213818,-0.00590403,-0.02417316,0.01914627,0.01854499,0.0409159,-0.03376551,-0.0329817,0.04040696,-0.02052159,-0.03147062,0.06811488,-0.0357666,0.04461663,0.08155115,-0.01084013,0.00449305,0.00538004,0.05416037,-0.18555692,0.03597531,-0.0269799,0.07519731,-0.00375399,0.00946753,-0.03444678,0.09196091,0.04193163,0.01641282,-0.05242184,0.03927109,0.0378501,-0.02235758,0.00632207,-0.0299975,-0.01036523,0.07064503,0.00669941,-0.04929006,0.02101054,-0.05108151,0.02143107,-0.00993917,-0.02785943,-0.01267041,0.05798665,-0.05251917,0.07205012,0.03778849,0.07069717,-0.03561012,0.01152644,-0.00492464,0.03093689,-0.03031167,-0.02004637,0.08872772,0.01640549,-0.02179698,-0.02075332,0.02275714,0.05915225,-0.02048023,-0.03995461,-0.0625741,-0.00129887,0.0182232,0.00934022,0.00871952,0.10619313,-0.01223567,-0.01095869,0.0177823,0.01650049,0.00267541,-0.00854995,0.03385156,-0.05819711,0.05419302,0.05648294,0.02827081,-0.0085218,-0.08560468,-0.03759806,0.07228269,-0.0096598,0.02617401,0.00371392,-0.03808308,-0.10537998,-0.01423544,-0.07689729,-0.02963153,0.03545836,0.01027693,0.04388558,0.00344263,-0.04084969,-0.01779322,0.02941228,-0.10848283,0.02208888,0.0691985,0.0308495,-0.00849497,0.00052122,-0.00371681,0.02375954,-0.02892189,-0.03987178,-0.06426782,-0.04021521,-0.00339437,0.03870177,0.07623294,-0.04454614,-0.03025677,-0.06265683,-0.05434667,-0.0248593,0.09512911,-0.03348169,-0.08437526,-0.05637541,-0.01549403,-0.00382117,-0.09655036,-0.01645084,-0.02092287,-0.06717492,0.0184495,0.07360701,-0.00281684,-0.04524063,-0.08280015,-0.00836703,0.02459276,0.01747767,-0.04806589,-0.0442164,0.02353661,0.01892669,-0.04416214,0.02280094,-0.03877408,0.07589342,0.02031899,-0.0780442,-0.02089027,-0.08574512,0.02485132,-0.03603473,-0.02225169,0.00630443,-0.02952547,0.03025748,-0.00661207,-0.05311947,-0.05905761,-0.01663418,0.02165188,-0.05827644,0.06984831,0.00926975,-0.11195373,0.0886414,0.00635196,0.0235214,0.00359811,0.03520588,0.05266369,-0.0254715,0.01985041,0.03735068,0.03870897,-0.04834377,0.0311779,0.06534535,0.02276207,-0.04599421,-0.18382519,-0.09164936,-0.06125155,-0.01763231,0.05915175,0.0108351,0.01743278,-0.01149667,0.01446688,0.06940813,0.0188646,-0.05924027,0.00463272,0.00672945,-0.00898889,0.01469574,-0.0929463,-0.05563125,-0.04386686,-0.0010953,-0.03685766,-0.00017792,-0.05785137,-0.06542245,0.0449712,0.01016251,0.17808291,0.07960782,-0.00103872,0.04014934,0.02902863,0.02859577,-0.00614978,-0.05283037,0.01711329,-0.01510816,0.01963456,-0.03912621,-0.03304105,0.00971686,-0.0345935,0.02657288,0.01846259,-0.06244699,-0.10129015,0.01525541,-0.0280687,0.10093186,-0.00738708,0.09869146,0.10659844,0.00694849,0.03943622,0.00912505,0.06504538,0.00513482,-0.07320659,0.01399716,0.00230233,0.06067046,-0.02821306,-0.03415009,0.03472605,-0.03686695,0.07221495,0.01763609,0.04238465,-0.0034017,0.05136682,0.00713102,-0.03855891,0.0965361,0.01183403,-0.03487158,0.07227169,0.039138,0.06302895,-0.10875005,-0.01808233,0.00215405,0.01514826,-0.08072553,0.06531772,0.07724625,0.01939299,0.03141806,0.03580468,0.02038156,0.02191902,-0.00330927,-0.01398014,0.00424865,-0.05705987,-0.03025251,0.00075699,0.04150621,-0.23907842,0.02278811,-0.01857651,0.05322217,-0.02471082,-0.02508502,0.01719949,-0.01328465,0.01012453,0.02102408,0.05808408,0.02438583,0.02853807,0.01844412,-0.0075397,-0.02025137,-0.00649755,-0.0717738,0.07883488,0.059499,0.03979383,0.06880484,0.22044326,-0.03033137,-0.00062622,0.01023296,0.03082768,0.01951498,-0.03462264,0.00738493,0.01350758,0.01107387,0.02685798,0.01450608,-0.01586121,0.0087806,0.01098698,0.05953256,0.0012694,-0.01345101,-0.05328747,-0.0103642,0.06630766,0.05903491,0.14908741,0.01329877,-0.02197779,-0.04768735,0.05540378,0.0327245,-0.09326812,-0.06206499,0.01294907,-0.01633142,-0.00697173,0.06877261,0.00726977,-0.02467101,0.05998106,0.02453369,0.00001872,0.07150374,0.03928602,0.03269659,-0.02284576],"last_embed":{"hash":"q2ubjp","tokens":162}}},"text":null,"length":0,"last_read":{"hash":"q2ubjp","at":1753423634660},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{10}","lines":[74,75],"size":507,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06582793,-0.03319428,-0.01990122,0.00725609,-0.02132251,0.05012232,0.05533561,0.03281714,0.08123115,-0.01806104,-0.01492222,-0.01685986,0.00702469,0.02124469,-0.06217241,-0.04223635,-0.00321745,0.04360063,-0.0305094,0.01236803,0.09058397,-0.0766057,-0.04604123,-0.10103264,0.07459204,-0.0046505,-0.06891149,-0.06373039,-0.05465029,-0.20088881,0.00200194,-0.02964855,-0.00875583,-0.06894017,-0.0923211,-0.03982987,-0.0280033,0.04757803,-0.03810255,0.02285242,0.00576715,0.00664757,-0.0117192,-0.02017536,0.00581374,-0.03844455,0.02896601,0.00263363,0.02834895,-0.00006252,-0.05559961,0.02456771,-0.02482109,-0.00769257,0.04775833,-0.0403573,0.03219965,0.09031599,-0.02419228,0.04767619,-0.0083155,0.05371898,-0.19447386,0.05177381,-0.04105282,0.05162261,-0.02139165,0.00174091,-0.01819976,0.08706009,-0.00914345,0.019337,-0.02922966,0.08869746,0.05010553,-0.01992813,0.01162247,-0.03116176,-0.02916034,0.03232799,-0.02189584,-0.03944358,0.01668556,-0.05483118,0.02621679,0.01010613,0.00000809,-0.01804536,0.0894874,-0.02517253,0.07741293,0.06272697,0.01881047,-0.0463004,0.01016055,-0.02374864,0.05564808,-0.03581894,0.03301399,0.13270073,-0.01235439,-0.03840447,-0.05166685,0.02601552,0.07870572,-0.03554117,-0.03778403,-0.03209434,-0.03364772,0.02549457,0.02171774,0.01004045,0.07741041,-0.01511982,-0.03129359,0.00960342,-0.01848061,-0.01197045,0.01415963,0.02211796,-0.04736276,0.01596327,0.03228424,0.0114419,0.01911438,-0.03881444,-0.00248673,0.05694522,-0.02231674,0.03006715,0.03870791,-0.03807111,-0.11273888,0.00254338,-0.03557801,-0.03481909,-0.00835587,0.00956606,-0.01741739,0.00713777,-0.04146703,0.01454795,0.07974838,-0.10465999,0.01177037,0.10809556,-0.01201782,0.00715224,-0.01066686,-0.04393548,0.02508548,0.00640664,-0.0120594,-0.04748702,-0.00016547,-0.01808433,0.04152991,0.05839236,-0.06043014,-0.01137326,-0.05774391,-0.0437521,-0.0000652,0.08651523,-0.01075234,-0.06874888,-0.04464302,0.0437286,-0.0101083,-0.07931226,-0.01519514,-0.0219841,-0.06492486,0.00134076,0.05733976,-0.0010144,-0.03976927,-0.10353133,0.03454705,-0.0111855,0.04119731,-0.04148133,-0.04300559,0.00366064,0.02139615,-0.03608139,0.02037746,-0.07134477,0.06835547,0.05147944,-0.06556007,-0.02918883,-0.05638001,0.00999814,-0.03029312,-0.03927871,-0.04107896,-0.04675064,0.023933,-0.00617914,-0.02626465,-0.05768941,-0.0070124,0.0026985,-0.01451513,0.06091683,-0.01208056,-0.08668548,0.06440108,-0.0134596,-0.01125834,-0.00935225,0.01594402,0.04848878,-0.02406307,0.0285579,0.01952821,0.06677517,-0.06693212,0.04531821,0.03627564,0.03386395,-0.04540635,-0.18376993,-0.05654749,-0.06097311,0.02609186,0.06163133,0.00715007,0.00407687,-0.01253079,0.04255357,0.08393814,0.05361171,-0.06613743,0.00451484,0.02126557,-0.01263714,0.00982196,-0.09314457,-0.02810956,-0.06108143,0.03668145,0.00161895,-0.02490725,-0.03297128,-0.03939749,0.04538497,0.01246949,0.13799004,0.04009683,0.01199664,0.02865238,0.03673701,0.01790133,-0.02569799,-0.02615857,0.02121058,0.00072062,0.0241713,-0.03582314,-0.05146144,0.01263653,-0.01471763,0.06329653,0.06272946,-0.08588564,-0.07663012,0.03575232,0.00039089,0.04375863,0.01006266,0.05440414,0.09614264,0.04372297,0.05261056,0.0043363,0.06537501,-0.01814648,-0.06147581,-0.00486352,-0.00243078,0.02188356,-0.00294192,-0.06758163,0.05272133,-0.0529511,0.06483212,-0.00136014,0.04572481,-0.01924301,0.05216598,0.01357503,-0.00587865,0.11467807,0.00712313,-0.01918047,0.07393356,0.02281097,0.10203711,-0.10360086,-0.03702468,-0.01609689,0.01292178,-0.0991587,0.07432315,0.06198314,0.0428636,-0.00105963,0.09862849,0.0420153,0.02084708,0.02734371,0.00113751,-0.00619386,-0.04138326,-0.00583495,-0.04168966,0.05587487,-0.25929171,0.01150923,-0.02922943,0.07509612,-0.01629055,-0.04484983,0.02985645,-0.02511536,-0.0048227,0.00563159,0.04674653,0.02062685,0.03975021,-0.02868235,-0.0128536,-0.04631289,0.01959335,-0.09138614,0.10419873,0.06818213,0.01879028,0.06062702,0.20722544,-0.05743287,0.0494735,0.0115065,0.01778037,-0.00581472,0.0178748,0.01790066,0.01161994,0.01711291,0.0486122,0.02609451,-0.02345008,0.02505334,0.01046317,0.02674449,-0.00980962,-0.03138797,-0.02603891,-0.00346319,0.04093105,0.05421517,0.13695239,-0.00862795,-0.03435338,-0.0465977,0.05570184,0.03150784,-0.11077867,-0.05130383,-0.01798193,-0.01905274,-0.0025617,0.05218569,0.02394044,-0.02783365,0.02949022,0.01697057,0.01747272,0.05324843,0.04406615,0.03612627,-0.00176048],"last_embed":{"hash":"xfuhti","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"xfuhti","at":1753423634879},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>#{11}","lines":[76,81],"size":449,"outlinks":[{"title":"The best probability software calculates winning and losing streaks based on mathematical formulae.","target":"https://saliu.com/ScreenImgs/streaks-gambling.gif","line":3},{"title":"The best strategy in sports betting applies records, scores, results and streaks.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0251266,-0.00786857,-0.00150099,0.00205645,0.02022321,0.0844299,0.02780775,0.04896338,0.10328472,0.01917994,-0.01077344,-0.03151449,-0.02871823,0.00622008,-0.04351094,-0.05309627,-0.0350775,0.02750071,-0.02418185,-0.00808865,0.06732943,-0.02323071,-0.04264851,-0.08388647,0.06312171,-0.03161327,-0.0244998,-0.05603707,-0.06373065,-0.24259494,-0.01801099,-0.0218294,-0.03052951,-0.06289131,-0.09921481,-0.0456216,-0.0005045,0.03967478,0.02195478,0.07925203,0.00058061,0.00494568,-0.00744776,-0.04480714,0.00910398,0.0133284,0.02337889,0.01213268,0.04980362,0.03412439,-0.02953822,0.0575544,-0.02196298,-0.025011,0.07007365,0.01582589,0.04225359,0.09442311,-0.0191404,0.04422119,0.0110579,0.05605806,-0.22089341,0.06066887,-0.04224589,0.02474278,-0.02794532,0.04780926,-0.00324913,0.0784937,0.03146586,0.02221949,-0.04110771,0.0430375,0.06485134,0.00031075,0.02293115,-0.04499697,-0.02063994,0.02407897,-0.01505229,0.00250405,-0.00776879,-0.04672318,0.01265609,0.03044225,-0.01718148,0.00335867,0.05657607,-0.06025454,0.04078946,0.04821713,-0.00527173,0.00378308,0.00787006,0.00531891,0.02451703,-0.05435942,-0.01386222,0.09187853,-0.02115562,-0.01514878,-0.00881151,0.00047536,0.08503615,-0.06220948,-0.0056533,-0.03549337,-0.01444512,0.01026512,-0.01011845,-0.01320604,0.06267087,-0.00400412,-0.05174548,0.0285468,0.01511502,0.00718053,0.01261782,0.02494698,-0.02030904,0.03807638,0.01704051,-0.00057486,-0.03109515,-0.04374969,0.01404314,0.09477856,-0.02097805,0.02103158,0.00506675,-0.02547989,-0.13572866,-0.03418324,-0.04163079,-0.02606456,0.01084296,0.00394291,0.01338582,0.03950054,-0.02466458,-0.01675696,0.06454618,-0.13251814,-0.03630336,0.08706499,0.001755,-0.01634028,0.00391768,-0.0060986,-0.00513704,-0.00255696,-0.02687286,-0.05797946,-0.03639587,-0.01917186,0.03159317,0.03844991,-0.05196152,-0.02652179,-0.09865952,-0.05450121,-0.02341643,0.08431999,-0.04050988,-0.0781986,-0.04212732,0.00621144,0.02602182,-0.07073146,-0.00655956,-0.0395121,-0.00985569,0.0083566,0.03384437,0.01054333,-0.07085732,-0.03466259,0.01337912,0.02204363,0.03255662,-0.04909802,-0.0324739,0.02426454,-0.00399867,-0.03254036,0.02558221,-0.05040544,0.04829544,0.05495179,-0.06773529,-0.0058928,-0.07325254,0.01753171,-0.02195003,0.00959501,-0.02149884,-0.04439772,0.0761626,-0.00697852,-0.1159595,-0.04975509,0.00867641,-0.03682984,-0.02460046,0.05297512,0.00623638,-0.08194711,0.07870685,0.00116306,-0.02383155,-0.01976253,0.02256774,0.01624567,-0.02943393,0.02486404,0.01359199,0.05283517,-0.06105144,0.0336958,0.05850821,0.03131807,-0.05430695,-0.16098291,-0.06614775,-0.08490613,0.00468364,0.05740289,-0.01331244,0.04997533,-0.02024194,0.05999998,0.05059561,0.06164063,-0.03417063,0.00633115,0.03367849,-0.00746956,-0.04594784,-0.0784022,-0.0432411,-0.0797926,0.0505047,0.02553391,0.00024205,-0.03334304,-0.05041011,0.03865299,-0.04286991,0.13656136,0.03781741,0.03362334,0.02421802,0.04113493,0.01915572,-0.0073132,-0.05564998,0.05305514,0.01845732,0.03903315,-0.05425019,-0.04330399,-0.03944239,-0.01669089,0.02720183,0.01464538,-0.07581518,-0.11747114,0.00110999,-0.02659481,0.03985459,-0.0195282,0.04173636,0.09778391,0.01863395,0.08051515,0.00149907,0.05915702,-0.03380892,-0.0783335,0.01811197,-0.00884712,0.08039878,-0.01577537,-0.04726068,0.07673524,-0.03925754,0.07098345,-0.00576022,0.00965305,-0.00514084,0.00791635,0.04392056,-0.01766961,0.08365058,0.02600176,-0.01703061,0.07458442,0.0155627,0.08164053,-0.06052162,-0.03442201,0.0054968,0.04879727,-0.1060764,0.08576901,0.05019014,0.08248869,0.02624065,0.06820997,0.02337068,0.03009314,0.02195015,0.00084282,-0.01053667,-0.04837786,-0.00259615,0.00409705,0.03292347,-0.2321911,0.00743911,-0.02568062,0.06234306,0.03359672,-0.05597297,0.04414128,-0.0154793,-0.02508909,0.00561116,0.04595446,0.02903837,0.03814841,-0.0186071,-0.0354574,-0.06035599,0.01613467,-0.03705797,0.09723157,0.05304135,0.05332129,0.05812686,0.22993703,-0.03239007,0.04398598,0.03448594,0.02313085,-0.01112969,-0.04062052,0.00240388,0.02414189,0.0136598,0.02475652,0.01971785,-0.03979306,0.03694314,0.00417745,0.06394587,-0.01547598,-0.03499193,-0.05025929,0.01539472,0.01973136,0.01240481,0.11608825,-0.00340127,0.0021704,-0.04951636,0.03142957,0.02002121,-0.11709123,-0.04038389,-0.0022442,-0.05648495,0.00199227,0.04323705,0.02996921,-0.03945442,0.04146212,-0.00253339,0.04184882,0.01399364,0.0523219,0.05654246,0.01241854],"last_embed":{"hash":"h5g2f6","tokens":499}}},"text":null,"length":0,"last_read":{"hash":"h5g2f6","at":1753423634938},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>","lines":[82,94],"size":1949,"outlinks":[{"title":"<u><i><b>Software to Generate Full 1X2 Systems for Football Soccer Pools</b></i></u>","target":"https://forums.saliu.com/software-1x2-football-soccer-pools.html","line":7},{"title":"<u><i><b>Sports Prognosticating or Betting for NFL American Football, Excel Sport Bet Spreadsheet</b></i></u>","target":"https://saliu.com/bbs/messages/382.html","line":8},{"title":"The best strategy in horse racing betting plays longshot trifectas for several races.","target":"https://saliu.com/HLINE.gif","line":12}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.01292209,-0.01091141,0.00519349,-0.01623907,0.0302379,0.06204473,0.04888834,0.05436971,0.08205546,0.02013138,0.00059017,-0.01823995,-0.03249197,0.02642116,-0.03838972,-0.02527567,-0.06089617,0.01485249,-0.01025552,-0.00231049,0.06206445,-0.01785341,-0.06136845,-0.08241145,0.07915733,0.00006986,-0.02525063,-0.04979539,-0.05500181,-0.21854308,-0.0144728,-0.03656345,-0.03104759,-0.05954425,-0.08277537,-0.05949096,0.02064599,0.05348091,0.01693507,0.07693958,0.00107023,0.01470098,0.01090165,-0.04016863,0.01405729,0.01239002,0.03465178,0.01195437,0.01805482,0.03833339,-0.02704875,0.08737699,-0.04261444,-0.02231459,0.08104106,0.01618582,0.05141225,0.07173653,-0.00108937,0.06397009,0.01217163,0.07000208,-0.23927107,0.03555461,-0.03438849,0.04000754,-0.0147514,0.03474039,0.01286314,0.08781615,0.04681572,0.01208533,-0.01960669,0.05026482,0.05027854,0.00300122,-0.00149666,-0.05373544,-0.00786026,0.02679313,-0.00133859,0.01173062,-0.0084529,-0.03506244,0.00289274,0.03704826,-0.02877943,0.0187619,0.05214265,-0.0692045,0.05584968,0.05897479,-0.01127327,0.00921429,0.01762913,0.00872531,0.03767165,-0.05316629,-0.01842925,0.09253419,-0.02137582,-0.00748507,-0.00519968,0.00029147,0.07772646,-0.05121988,-0.01062644,-0.0401569,-0.00644393,0.01501888,-0.02011162,-0.02662482,0.06816994,0.00728171,-0.02114138,0.02563288,0.01866693,0.0107248,0.02434261,0.01202476,-0.01264952,0.03187571,0.01034835,-0.00592857,-0.044983,-0.05104047,0.00413935,0.09738275,-0.01170875,0.00477562,0.00140704,-0.02968265,-0.1515255,-0.03642658,-0.03128441,-0.03336501,0.02410398,0.00508509,0.03048343,0.03111068,-0.02929034,-0.03114027,0.06291202,-0.11260408,-0.04719217,0.08884098,-0.01028573,-0.0205973,0.00717677,-0.00097352,-0.00935501,-0.00314563,-0.02850518,-0.07612594,-0.03192851,-0.01767371,0.03095459,0.02687454,-0.03257807,-0.02476751,-0.11378611,-0.04809442,-0.01649786,0.10224372,-0.04337171,-0.07930082,-0.03059598,-0.00303359,0.02001717,-0.06241341,-0.0029809,-0.01943997,-0.03383427,0.0170292,0.04370035,0.00225057,-0.03619079,-0.04268394,-0.01255692,0.02515336,0.03231643,-0.04326535,-0.05387801,0.01049059,-0.00788568,-0.02497101,0.02095294,-0.04120373,0.05117165,0.04525374,-0.0772964,0.01449286,-0.06027165,0.01964458,-0.0313326,0.01878624,-0.01077818,-0.04424232,0.08574613,-0.02733854,-0.11124399,-0.03897151,0.00823542,-0.02152866,-0.03895666,0.03533262,0.01814627,-0.07507064,0.07925265,-0.00032427,-0.02112838,-0.02229913,0.03712195,0.00926263,-0.04801754,0.00379523,0.02282792,0.05661453,-0.063916,0.0322664,0.04371344,0.02268741,-0.06529109,-0.1629331,-0.05781848,-0.07431368,0.00491291,0.04163026,-0.0130449,0.03793927,-0.02189308,0.04712142,0.07409637,0.0457277,-0.0198564,-0.01046142,0.05073793,-0.01689835,-0.05083702,-0.08435179,-0.04636936,-0.08698179,0.03291127,0.01238633,-0.01283228,-0.05428592,-0.05981398,0.02180399,-0.0382456,0.14212592,0.03054066,0.03914669,0.0103259,0.03960245,0.00940624,-0.01039815,-0.0491639,0.05535167,0.0116058,0.03916869,-0.07282865,-0.05988059,-0.05811422,-0.00081732,0.03399844,0.01639201,-0.06808192,-0.12907033,0.00339753,-0.03815648,0.03827764,-0.0257603,0.0439668,0.06471648,0.01098119,0.08284868,0.00456421,0.05230448,-0.02628854,-0.08433785,0.03057535,-0.00233426,0.10955659,-0.01546268,-0.02740078,0.06727905,-0.03126848,0.05588106,-0.01836847,0.01182283,-0.0068462,0.01209047,0.04149748,-0.03448851,0.06459157,0.01942586,-0.02886834,0.08773259,0.01509931,0.08729425,-0.03918795,-0.01337844,0.01624742,0.03724412,-0.08708356,0.07209098,0.05095213,0.07610478,0.00592637,0.07435279,0.01070613,0.00761175,0.0165294,0.00133049,-0.00445003,-0.04928126,0.00634572,0.00044219,0.00896859,-0.22646753,0.01636952,-0.0177383,0.06212033,0.05001464,-0.06959724,0.03194281,0.02179384,-0.02404928,-0.0057734,0.05134298,0.01404094,0.03703111,-0.01389757,-0.02462235,-0.06405605,0.02682631,-0.04034743,0.09066364,0.05003764,0.06126148,0.06770869,0.21490687,-0.03207609,0.05534562,0.04165175,0.0179216,0.01246424,-0.06631361,-0.00446655,0.01729037,-0.00112537,0.01502964,0.02223903,-0.04057127,0.02085195,0.02077295,0.07543174,-0.01711238,-0.03358215,-0.06038495,-0.0018119,0.00832477,0.03573279,0.13148811,0.02025489,-0.01173021,-0.05879592,0.02322036,0.01091484,-0.11789031,-0.04643787,-0.00683321,-0.05423282,0.00272644,0.05810776,0.04071773,-0.04476739,0.02758644,-0.02286271,0.05192968,0.01244674,0.06125096,0.04175476,0.02079264],"last_embed":{"hash":"1xkrgvb","tokens":237}}},"text":null,"length":0,"last_read":{"hash":"1xkrgvb","at":1753423635137},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{1}","lines":[84,87],"size":921,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03083653,-0.02710754,0.00630808,-0.01787916,0.03161722,0.06451716,0.02737016,0.03402364,0.08066637,0.00517667,-0.01209678,-0.072161,-0.03047714,-0.00467273,-0.00317495,-0.05434611,-0.01841695,0.0164724,-0.02219772,0.00168324,0.07731965,-0.01454271,-0.0663374,-0.07039534,0.01579021,-0.04797694,-0.0313546,-0.05860921,-0.04843691,-0.20257352,0.00067166,0.01875088,-0.04904775,-0.05085299,-0.12283593,-0.04270015,-0.00550523,0.02531448,-0.0220373,0.05153842,-0.0001084,0.01215921,-0.00464796,-0.05888801,-0.00432802,-0.01939586,0.02694306,0.01587526,0.09866869,0.0187928,-0.02092618,0.05909513,-0.00633286,-0.04444262,0.05006715,0.02688546,0.04864035,0.0989981,-0.03199638,0.01812448,0.00872106,0.03875767,-0.19679573,0.05456588,-0.02875352,0.01024885,-0.03098189,0.05095243,0.0029788,0.08412653,0.02771795,0.01655649,-0.03307801,0.03404764,0.08870002,-0.00637192,0.01809569,-0.05687296,0.00261383,0.06139446,-0.051187,-0.02037796,-0.0052335,-0.02310029,0.02761895,0.0433965,-0.01625742,0.02102624,0.06385736,-0.05306498,0.05176093,0.0205422,-0.01537992,0.02385038,0.00116699,0.00549653,0.01171894,-0.06935247,0.01056733,0.1242307,-0.01311804,-0.02228035,0.01295752,0.00418663,0.08205947,-0.04183092,-0.00245798,-0.02850753,-0.00685748,0.00757471,-0.00376578,0.0003158,0.05981281,0.01006529,-0.0801685,0.00482751,-0.01250314,-0.0181043,0.00454994,0.03883509,-0.03881498,0.039759,0.02559235,0.01529337,-0.00818019,-0.05234969,0.00481468,0.06304292,-0.03019824,0.08892891,0.02247592,-0.0070009,-0.11585946,-0.04325803,-0.02516197,-0.03048236,-0.00550726,-0.00177767,-0.02606668,0.05024976,-0.00665218,-0.01652011,0.06231953,-0.16329443,-0.00429622,0.08639836,-0.0199769,-0.00321813,0.0089572,0.03027634,0.00271106,0.00244832,-0.01576494,-0.05968485,-0.02664109,-0.00480757,0.06153633,0.03971426,-0.04948488,-0.00123124,-0.08564337,-0.04919389,-0.00603447,0.11983744,-0.04995708,-0.09766003,-0.05622317,0.0380378,-0.01205377,-0.06252763,-0.02843302,-0.01456667,-0.01702052,0.02489498,0.07268924,-0.02002816,-0.08398234,-0.03038982,0.02107822,0.01307596,0.043282,-0.03243673,-0.02945942,0.00867715,-0.00529798,-0.03868673,0.03219269,-0.04785574,0.03777851,0.06228343,-0.07235381,0.00529985,-0.0776242,-0.00121752,-0.02127569,-0.00180394,-0.06281693,-0.03104756,0.06294211,0.00246295,-0.07135452,-0.00508438,-0.01279954,-0.04310533,-0.02719615,0.06053789,-0.01597032,-0.09190324,0.04929444,-0.00407078,-0.03179858,0.00285,0.03959091,0.03285721,-0.04945364,0.01442876,0.0167202,0.0496364,-0.03586359,0.02779611,0.05044701,0.01217442,-0.0780987,-0.18262254,-0.05669862,-0.08812094,-0.00694289,0.05334261,-0.01529866,0.03377002,-0.02392699,0.03964418,0.05637482,0.09415668,-0.05842912,0.03360261,0.04050977,-0.00296358,-0.02696494,-0.07253894,-0.04949657,-0.02030161,0.03838291,0.0408037,-0.01558744,-0.00414693,-0.04277556,0.03947256,-0.06257688,0.15430863,0.04891513,0.04231699,0.03892836,0.06148832,0.00426893,-0.01419296,-0.08081122,0.04148772,0.05629499,0.00659743,-0.0498015,0.0001492,-0.03457293,-0.012434,0.02114492,0.01650048,-0.07267972,-0.05573825,-0.05107635,-0.01210761,0.00013728,0.00149378,0.02735475,0.0512487,0.01497372,0.04647127,0.01279684,0.05410914,-0.01278314,-0.0847031,-0.00248629,-0.03942966,0.03789744,-0.00613223,-0.06375065,0.0593932,-0.04312873,0.05733464,-0.00640371,0.01656812,-0.0055726,0.02026891,0.041262,-0.00378825,0.09524764,0.03024218,0.00390999,0.04945172,0.03086114,0.08327889,-0.05002968,-0.01246006,-0.00635058,0.02078541,-0.08606295,0.08511683,0.04810707,0.07913005,0.0513181,0.02846168,0.02935166,0.00514828,-0.01122821,-0.00456208,0.01021956,-0.05341434,0.00401899,0.00513384,0.02695894,-0.24454291,0.01888598,-0.03034277,0.05261875,0.0209505,-0.04356597,0.03621159,-0.03296725,-0.02663693,0.00753517,0.08068734,0.00278875,0.00180098,0.00006877,-0.05193585,-0.02609544,0.04862277,-0.03960347,0.11371761,0.05764755,0.02050625,0.06873552,0.24279365,-0.0301123,0.01728908,0.03265166,-0.00379801,0.00106343,-0.03786188,0.02651436,0.02497605,0.03174571,0.04777459,0.00145457,-0.04509655,0.07428034,0.00232614,0.03862273,-0.00332381,-0.01609003,-0.08591163,0.04219749,-0.01155112,-0.00310664,0.06614407,0.01352892,0.01060299,-0.02960113,0.02798501,0.0188889,-0.13351448,-0.04307506,-0.01996374,-0.0417952,0.02380355,0.02964936,0.01774243,-0.07531348,0.0470865,0.03722811,0.02513838,0.02780058,0.06265751,0.06952792,0.02635818],"last_embed":{"hash":"k2s9sc","tokens":123}}},"text":null,"length":0,"last_read":{"hash":"k2s9sc","at":1753423635221},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{2}","lines":[88,88],"size":246,"outlinks":[{"title":"<u><i><b>Software to Generate Full 1X2 Systems for Football Soccer Pools</b></i></u>","target":"https://forums.saliu.com/software-1x2-football-soccer-pools.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02135429,-0.00752649,-0.00209581,-0.00536251,0.02087889,0.07955767,0.04016729,0.02850594,0.07855532,0.01833304,-0.0081087,-0.03529925,-0.03957497,-0.02476799,-0.02859901,-0.05385688,-0.02811605,0.01061875,-0.03357789,-0.00992932,0.04976397,0.01268983,-0.06556578,-0.07632796,0.06625193,-0.02505665,-0.03291047,-0.04210576,-0.06657255,-0.19317786,-0.00856081,0.0077235,-0.0647714,-0.05886434,-0.11146425,-0.04339111,-0.00398132,0.05651996,0.01612275,0.06377472,0.01785593,0.00846895,-0.01346546,-0.03698445,-0.00072553,0.01857335,0.0149797,-0.00960088,0.08266075,0.02951453,-0.0368729,0.06774231,0.0010875,-0.0549548,0.05879335,0.01930161,0.0441136,0.0898194,-0.0212877,0.02894377,-0.01538365,0.04033578,-0.20885344,0.07274457,-0.02281086,0.00735994,-0.01079757,0.02593825,-0.03885642,0.07564196,0.03174128,0.00671301,-0.02234642,0.04668847,0.06617751,0.00025792,0.01859966,-0.06722746,-0.01180291,0.04339904,-0.0443125,0.01479669,-0.00775101,-0.0180773,0.01023489,0.02207726,-0.01592881,0.01077695,0.0683902,-0.04870631,0.04534092,0.03648162,-0.03708445,0.01114994,0.00254675,-0.00030956,0.00283108,-0.07368872,-0.02496488,0.11298561,-0.02760804,-0.00662323,-0.00566072,-0.03220403,0.06885619,-0.07712238,-0.01010271,-0.02450829,-0.03221872,0.02401743,0.001796,-0.01629854,0.05869101,-0.00448879,-0.05920823,0.01656235,-0.01060742,-0.04523396,0.02069573,0.0156062,-0.0294077,0.02422298,0.01400851,0.01643037,-0.02417448,-0.07365278,0.01572133,0.07971686,-0.03051227,0.04510357,0.02048802,-0.01222895,-0.11994006,-0.04003496,-0.05640115,-0.03092533,0.02328503,-0.00736617,0.00959396,0.0435836,-0.00879107,-0.0252401,0.04365113,-0.15896916,-0.02557225,0.09496941,-0.01104163,-0.01543636,0.0098212,0.0173775,0.0101072,-0.00741212,-0.00634516,-0.03329287,-0.03986299,-0.00141316,0.0179205,0.04067409,-0.05745294,-0.01952747,-0.09375437,-0.05660672,-0.00080986,0.06825156,-0.07817109,-0.07637149,-0.05296841,0.00162964,0.00096302,-0.08699787,-0.00772989,-0.02477188,-0.03370119,0.03663932,0.05504764,0.03562298,-0.04232909,-0.0396851,0.00898411,0.00668933,0.05428427,-0.06599393,-0.04686228,0.03188905,0.02522329,-0.01160076,0.02359163,-0.02996507,0.03983396,0.08410481,-0.05226215,0.0134287,-0.07878387,0.01344658,-0.03973604,-0.00470906,-0.04081453,-0.03894428,0.07899926,0.01881702,-0.06359343,-0.03024414,-0.02653032,-0.03684036,0.00665664,0.0667767,-0.00060454,-0.09988879,0.07422182,-0.02324947,-0.03755753,0.01897801,0.03391273,0.02325921,-0.03472073,-0.010104,0.0347646,0.03391,-0.06150369,0.02248988,0.07819651,-0.00111689,-0.03832048,-0.17613229,-0.0683251,-0.06105312,-0.00839243,0.04049274,0.00282072,0.02854526,0.00934505,0.04307074,0.07405597,0.04810673,-0.03558152,0.05147526,0.02352097,0.00389388,-0.01756575,-0.06548768,-0.0615368,-0.05551316,0.04155914,0.00405359,-0.01282817,-0.04237656,-0.02666808,0.0648274,-0.02551796,0.13778915,0.03658283,0.03091224,0.03567003,0.06997036,0.03752137,-0.00420549,-0.06497156,0.04613824,0.02196586,0.01652358,-0.04161401,-0.04543813,-0.04596743,-0.02224988,0.03026551,0.01240072,-0.07660034,-0.08533494,-0.02455207,0.01086205,0.0310379,-0.03395429,0.03544655,0.07909553,0.01663406,0.05612207,0.03422201,0.06743807,-0.01732278,-0.05001763,0.01013397,-0.05495143,0.0453117,-0.00399426,-0.04572787,0.04238397,-0.02092928,0.0521588,-0.01698748,0.01188308,0.00239544,-0.0038727,0.0510149,-0.01192347,0.06826739,0.02155911,-0.02065651,0.02796379,0.04338652,0.10950432,-0.05395401,-0.02535536,-0.00111479,0.04421623,-0.08286564,0.11671676,0.00407595,0.07009143,0.04901555,0.04876436,0.01272658,0.02179619,-0.00039457,0.01991913,-0.04714185,-0.02913817,-0.02683637,0.00794285,0.02927025,-0.26036903,0.02140647,-0.02475744,0.04606272,0.01986721,-0.04874044,0.00780659,-0.03539957,-0.03463281,0.02885155,0.09376506,0.00122564,0.00889613,-0.02199291,-0.03788245,-0.04197107,0.05000228,-0.04857526,0.08704173,0.07533941,0.02629124,0.05430402,0.2458,-0.03492692,0.0653035,0.03604105,0.01779992,-0.00992557,-0.05164054,-0.00273492,0.05371635,0.02925491,0.03614161,0.00265012,-0.01051888,0.07300131,0.01133493,0.07522987,0.00359393,0.00140505,-0.05146267,0.03798778,0.00057775,-0.00555111,0.08674128,0.01139052,-0.01353047,-0.02389585,0.04144258,0.01276716,-0.10750373,-0.03641737,-0.02742365,-0.0591604,-0.02353944,0.02201916,0.04697724,-0.04758819,0.05261994,0.0529913,0.05001958,0.01269825,0.04542454,0.0957223,0.02149069],"last_embed":{"hash":"1gqmd0c","tokens":124}}},"text":null,"length":0,"last_read":{"hash":"1gqmd0c","at":1753423635273},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{3}","lines":[89,90],"size":258,"outlinks":[{"title":"<u><i><b>Sports Prognosticating or Betting for NFL American Football, Excel Sport Bet Spreadsheet</b></i></u>","target":"https://saliu.com/bbs/messages/382.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04323351,-0.0244709,-0.00833672,0.04624737,-0.00036853,0.09010376,0.02867236,0.03638216,0.09325846,0.00456772,-0.01266035,-0.0490235,-0.00758291,0.02190204,-0.04917264,-0.03523625,-0.01481778,0.03535692,-0.04261365,0.00352216,0.07410892,-0.04420464,-0.01612301,-0.06372704,0.06667075,-0.06674644,-0.0267998,-0.07306571,-0.06455018,-0.21336801,-0.01893858,-0.0244864,-0.05139444,-0.06078702,-0.09846228,-0.04246375,-0.02571663,0.04550258,0.01837024,0.06951268,0.00490907,0.0063426,-0.04185779,-0.03612398,0.00007775,-0.01021473,0.03318594,0.00322012,0.06033557,0.00354757,-0.02926685,0.03533622,-0.01042289,-0.02281843,0.05971402,0.00191644,0.02817266,0.10952805,-0.01587529,0.01966109,0.01705081,0.07277884,-0.20642404,0.05914778,-0.04358881,0.03541527,-0.01951275,0.05848068,-0.03872713,0.10953742,0.01780017,0.01496795,-0.04919909,0.04519708,0.04484765,-0.01985196,0.03067315,-0.03937139,-0.03939864,0.03873306,-0.01061468,-0.02398228,0.0067218,-0.0591347,0.01538931,0.00464184,-0.00780127,-0.03512866,0.04739159,-0.04932139,0.04770149,0.0493467,0.01761696,0.00162944,0.01150878,0.01087612,0.01290898,-0.05015089,-0.01740167,0.12767522,-0.02518355,-0.01770763,-0.01602934,0.03654512,0.08272427,-0.03674237,-0.01770526,-0.02993415,-0.02180308,0.02147508,0.01277154,0.01732895,0.08081759,-0.01659983,-0.04863825,0.03556002,0.00421712,-0.00406415,-0.01483258,0.04242697,-0.05407498,0.05745913,0.01766394,0.00357842,-0.0079571,-0.04636136,0.02253369,0.06561308,-0.01643208,-0.00521613,0.01258311,-0.03633002,-0.14397392,0.00372964,-0.05111924,-0.02431116,-0.00156966,-0.00562577,0.00700988,0.0171408,-0.01213554,-0.04046694,0.10240203,-0.13012221,0.01652254,0.05913889,0.01516802,-0.00890062,-0.02466214,-0.00954662,-0.02686826,0.00047219,-0.04484767,-0.0385413,-0.03262132,-0.02235018,0.02030702,0.06805122,-0.033542,-0.04374599,-0.08151866,-0.04310874,-0.05201299,0.09530956,-0.0471642,-0.07847311,-0.05464149,-0.01130499,-0.00271712,-0.05125043,-0.03470352,-0.03275701,-0.03243034,0.01888379,0.02989674,0.00268635,-0.09945673,-0.04078042,0.00756918,0.02416096,0.04221814,-0.03573337,-0.03276131,0.03282778,0.01271689,-0.02837649,0.02565021,-0.04374362,0.06676199,0.05690672,-0.07511917,0.00548102,-0.06271861,-0.01966718,-0.02901682,0.00301279,-0.01648784,-0.04110584,0.05626076,-0.00164549,-0.12953191,-0.03296141,0.00573293,-0.00602551,-0.03129125,0.05958223,-0.00380409,-0.08585804,0.06706814,-0.00351713,-0.01851029,-0.00392284,0.00909999,0.0275775,-0.00562203,0.03883464,0.01698185,0.05322557,-0.04638115,0.01429706,0.07026883,0.04163757,-0.02666838,-0.17081518,-0.07538408,-0.07315186,-0.00416846,0.09133475,-0.00542853,0.04875404,0.00715051,0.05656654,0.05082541,0.07548757,-0.04946131,-0.00343852,0.01251029,-0.00876439,0.01335281,-0.09410665,-0.04954634,-0.06378242,0.04094074,0.00694477,0.03704725,-0.0362364,-0.05356921,0.06923984,-0.03284388,0.14611821,0.03642698,0.02107823,0.01482415,0.02817319,-0.00405791,-0.02127582,-0.03550444,0.04988988,-0.01083856,0.02867838,-0.04873313,-0.05179558,-0.02999992,0.01358829,0.02547596,0.00038348,-0.05313269,-0.07636285,0.02414302,-0.01646398,0.08783352,0.02673676,0.04868494,0.1135631,0.02622392,0.06507173,0.00997632,0.0722122,-0.00847882,-0.10536886,-0.00380736,-0.00623696,0.06358315,-0.02206588,-0.04779052,0.06390063,-0.03652383,0.07722151,-0.00431914,0.0310805,-0.01198941,0.00889488,0.05440885,-0.00096008,0.07268872,0.01144744,-0.01887966,0.03701335,-0.0019748,0.07753065,-0.0752707,-0.00511228,-0.01049267,0.07714538,-0.11394731,0.06648941,0.05561828,0.07172832,0.02871901,0.04799207,0.04934994,0.02238093,0.01935468,-0.00982174,-0.00277317,-0.05367591,-0.01322405,0.00166548,0.01949321,-0.22835274,0.00467463,-0.03100517,0.07967775,0.00533963,-0.01018705,0.01617868,0.01252101,-0.0216284,-0.01094434,0.0386095,0.06877086,0.011369,-0.0082182,-0.05018404,-0.04422829,-0.00268885,-0.05808745,0.08545125,0.05320273,0.05344585,0.05931719,0.20872205,-0.02959092,0.03178163,0.0163782,0.03016463,0.00483839,-0.05321366,0.00062935,0.02918407,0.00792646,0.01156694,0.00885022,-0.04680153,0.05856168,0.01006489,0.07304686,-0.01027912,-0.01780174,-0.06228223,0.02167912,0.00281461,0.00918956,0.1087755,-0.03956399,0.00299873,-0.04534739,0.03275198,0.04057714,-0.09822144,-0.03732108,-0.03120305,-0.03406396,-0.00532151,0.04292742,0.0248472,-0.02376012,0.05232304,0.0341377,0.01699031,0.04770223,0.01945374,0.04031331,-0.00374482],"last_embed":{"hash":"ib4bek","tokens":143}}},"text":null,"length":0,"last_read":{"hash":"ib4bek","at":1753423635310},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>#{4}","lines":[91,94],"size":447,"outlinks":[{"title":"The best strategy in horse racing betting plays longshot trifectas for several races.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06913356,-0.03556795,-0.03298167,0.06777849,-0.02801629,0.05251551,-0.02564702,0.02181108,0.07369342,0.01120211,-0.01094931,0.01124749,0.02688895,0.01598006,-0.07634716,-0.02629622,0.01293974,0.00281977,-0.04123923,0.0027595,0.03385589,-0.05324251,-0.03102043,-0.08176208,0.07161498,-0.01645374,-0.04778345,-0.08034114,-0.04725966,-0.22884658,-0.0016216,-0.01263083,-0.01522603,-0.07181655,-0.11363091,-0.02012827,-0.02276224,0.03975296,-0.00875468,0.07118005,0.03244695,0.02437936,-0.04860129,-0.02915213,-0.02706766,-0.04005893,-0.00822403,0.00443789,0.05840898,0.01643862,-0.05982883,-0.01281158,-0.01534337,0.01531307,0.08287262,-0.01505792,0.02658271,0.10202241,-0.042093,-0.00556661,0.0084359,0.08810706,-0.18636218,0.07049764,-0.05358972,0.03930328,-0.03380929,0.02452823,-0.02729357,0.10651822,0.03568107,0.02112572,-0.03589272,0.01902648,0.00486124,-0.01201503,0.02576321,-0.00161844,-0.05121667,0.04932363,-0.03101952,-0.04410448,-0.01639692,-0.03884942,0.01413784,0.04306591,-0.00804695,0.00163668,0.07051416,-0.03887562,0.0452119,0.04330957,0.03505637,0.04224159,0.01280627,0.00562919,0.01898616,-0.02363762,0.00417834,0.1042681,0.01440413,-0.0071123,-0.01902135,0.01774848,0.07413056,-0.02370146,-0.04673719,-0.08532644,-0.02299068,0.04062954,0.04821699,0.01202873,0.07434954,-0.05186497,-0.02723157,0.05709938,0.00736995,-0.01875609,0.00509012,0.0287123,-0.05995182,0.04839536,-0.01096951,0.02851025,-0.0416295,-0.02421369,0.11204769,0.05383294,-0.02340093,-0.00884203,0.02444471,0.00418546,-0.15311614,-0.02237871,-0.04290168,-0.0326906,-0.00005196,0.01649697,0.00911424,0.01709817,-0.03383314,-0.0566507,0.10426827,-0.09487574,-0.02293538,0.0466055,0.03439591,-0.01214673,-0.01224044,-0.01652666,-0.02331643,0.01859612,-0.02953936,-0.02657879,0.01174382,-0.01421171,0.02163873,0.08280018,-0.04894121,-0.05349928,-0.07771064,-0.02778272,-0.02247843,0.0681207,-0.03295286,-0.13448305,-0.03382333,0.02237831,-0.02869972,-0.05536257,-0.03133613,-0.04011419,-0.06587856,0.01952425,0.03869925,-0.02557474,-0.15100822,-0.04647311,0.00081889,0.02949217,0.03917177,-0.01687907,-0.03611057,0.01372636,-0.00057088,-0.04947132,0.05836922,-0.06077682,0.02744506,0.06563361,-0.02079679,-0.01054614,-0.06627016,-0.0142583,-0.02970047,-0.02976839,-0.01248998,-0.00508986,0.08907373,0.01932314,-0.07134874,-0.00843892,0.02808125,0.02654166,0.00351633,0.04610704,-0.00674134,-0.10268926,0.07284599,0.03692279,-0.00694542,0.04138396,0.00857264,0.02841822,-0.03092793,0.05128594,0.02085349,0.07135948,-0.02487069,0.00711823,0.06330885,0.00588106,-0.02578663,-0.17667292,-0.03954281,-0.0792564,0.05711498,0.05225983,-0.04899431,0.03045738,0.00984483,0.07513779,0.09202155,0.09370787,-0.05730071,-0.00505246,0.02592878,-0.00697278,0.04486554,-0.07789305,-0.01853856,-0.04428255,0.00848778,0.01509386,-0.00618333,-0.0067516,-0.06860671,0.06448437,-0.04050522,0.15464747,0.01757725,0.00914086,-0.01183997,0.02895068,0.00501776,-0.02493072,-0.02260844,0.02739925,-0.00472334,0.06751661,-0.02484786,-0.03798607,-0.03006834,-0.02072766,0.01221783,-0.02009394,-0.09029335,-0.04140917,0.04179819,-0.01247462,0.06558483,0.02633657,0.04309693,0.08661366,-0.02067801,0.0359448,0.00821761,0.082021,-0.02254353,-0.09721538,0.00142701,0.00250181,0.05499358,-0.01243241,-0.05264294,0.07842078,-0.03709382,0.04834726,0.00168877,0.0351085,0.01005719,0.02705815,-0.00125633,0.00140248,0.0652274,-0.00113045,0.03836647,0.00900477,0.02035469,0.08726132,-0.08040711,0.00768053,0.03205781,0.04253848,-0.06654518,0.03845565,0.07425828,0.03276504,0.03585692,0.08069869,0.06829844,0.01155305,-0.00976469,0.01476753,0.01240422,-0.05593559,0.0009903,0.01823117,0.05446258,-0.22756355,0.00779876,-0.03333599,0.0490723,0.0046343,-0.01687587,0.02548226,-0.013632,0.03263386,-0.05364939,0.02338348,0.07725199,0.00552647,-0.04999095,-0.02990373,-0.03579987,-0.03863855,-0.03552478,0.05355236,0.01446642,0.05197008,0.05790899,0.18704003,-0.01537422,0.00494776,-0.00689092,-0.02689057,0.02201212,-0.02403061,-0.00102794,0.00444931,0.01898471,0.02694135,-0.01717488,-0.05023215,0.058038,0.02364306,0.00955004,-0.01595782,0.03031223,-0.06167872,0.03468011,0.00957181,-0.02225594,0.10540146,-0.04237057,-0.00140082,-0.05973643,0.04101949,0.06934687,-0.09665263,-0.06515037,-0.04910401,-0.03048766,0.01483172,0.07771292,0.03040729,0.00097859,0.01609159,0.01872524,0.00563086,0.00304407,0.03782661,0.06612873,-0.00329427],"last_embed":{"hash":"f8zaki","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"f8zaki","at":1753423635390},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>","lines":[95,114],"size":3204,"outlinks":[{"title":"_**races at three horse tracks**_","target":"https://saliu.com/horses.html","line":3},{"title":"_**The non-favorites won 169 horse races out of 272 or 62.13% of the time.**_","target":"https://saliu.com/horseracing.html","line":15},{"title":"The best strategies in lottery, lotto are based on wacky filters to reduce many combinations.","target":"https://saliu.com/HLINE.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06983229,-0.03446567,-0.02822112,0.06652323,-0.02619296,0.05972041,-0.03084878,0.03125063,0.07108688,0.01455791,-0.01159729,0.00328559,0.02553379,0.01726007,-0.07312706,-0.02186961,0.00904537,0.00466036,-0.03868433,-0.00202243,0.03906704,-0.04994337,-0.03294571,-0.0835276,0.06900308,-0.00883249,-0.04978891,-0.08015828,-0.04013768,-0.22432674,-0.00622376,-0.01129456,-0.00588347,-0.06397919,-0.11348911,-0.01418126,-0.01894891,0.04098481,-0.0098393,0.07383996,0.03049465,0.0228763,-0.04540077,-0.02327802,-0.03325925,-0.03914156,-0.00531292,0.01264374,0.06132171,0.00538519,-0.05392587,-0.01100018,-0.02200146,0.01477439,0.08272484,-0.02166516,0.02614406,0.10450371,-0.04863397,-0.0035544,0.00433957,0.09219465,-0.19805078,0.07317986,-0.05250342,0.03893416,-0.03135524,0.02426265,-0.02367906,0.10592949,0.02834744,0.02500943,-0.03191299,0.0198791,0.00405682,-0.01570115,0.02048718,-0.00144842,-0.05043275,0.04490722,-0.031514,-0.04849455,-0.01935175,-0.03854801,0.011779,0.047425,-0.00828303,0.00517124,0.06970707,-0.04046386,0.03771937,0.04085258,0.03439686,0.0387082,0.00886022,0.00713769,0.0142893,-0.02022688,0.00520057,0.1080285,0.01846651,-0.01970316,-0.01702475,0.02442321,0.06845953,-0.02363902,-0.0376902,-0.08457264,-0.02014856,0.04892874,0.04997779,0.01078617,0.06528041,-0.05126224,-0.02268221,0.05420405,-0.00767645,-0.02461074,0.00154456,0.03261223,-0.06178515,0.04657059,-0.00498105,0.01951882,-0.04045239,-0.02026571,0.10635477,0.05197401,-0.02952384,-0.01491985,0.02051232,0.00978466,-0.1539955,-0.01920116,-0.03955222,-0.03425626,-0.00070299,0.0117766,0.00397633,0.02275903,-0.03180877,-0.05924922,0.11380503,-0.09779881,-0.01763607,0.04089867,0.03684763,-0.01332647,-0.01589208,-0.00825185,-0.02286939,0.01430126,-0.02839927,-0.02240287,0.00893749,-0.01039284,0.0220885,0.08106385,-0.04960953,-0.05067806,-0.07805827,-0.02452195,-0.02442299,0.08166193,-0.03476312,-0.13624373,-0.03228119,0.03283557,-0.0297729,-0.05828153,-0.0330737,-0.03730258,-0.06629461,0.02097535,0.04147858,-0.02511353,-0.1545953,-0.03870122,-0.00345528,0.03011573,0.03631936,-0.01360659,-0.02960571,0.01978262,-0.00486643,-0.04665526,0.06212313,-0.06240164,0.03174767,0.06256077,-0.02424973,-0.00559106,-0.06724341,-0.0196008,-0.03300802,-0.02770945,-0.01572097,-0.01111459,0.08465926,0.02209639,-0.0766381,-0.00645482,0.02622349,0.02969407,0.00155426,0.04796624,-0.00423982,-0.09535055,0.07103656,0.04204498,-0.0062533,0.03865105,0.00523337,0.02287588,-0.02887968,0.05220062,0.02068601,0.06931444,-0.02822383,0.00981005,0.06509648,0.01538825,-0.03117046,-0.17918591,-0.03636998,-0.08040676,0.05893658,0.05876785,-0.04410568,0.03382227,0.00915655,0.06996446,0.08652223,0.09381492,-0.05899459,0.00040273,0.02940268,-0.0073695,0.04071831,-0.07793935,-0.02012671,-0.04138377,0.00392153,0.02006158,-0.01271078,-0.00333426,-0.07075795,0.06183339,-0.0424769,0.15411155,0.02135364,0.00875837,-0.00863618,0.02791465,-0.00196452,-0.02768934,-0.02118872,0.02777262,-0.00717719,0.07365832,-0.03017023,-0.03043102,-0.02541428,-0.01851157,0.00863274,-0.01607949,-0.08687261,-0.04312819,0.03891063,-0.01318383,0.06413336,0.03271586,0.04185792,0.08387632,-0.0224883,0.04371693,0.01135045,0.06685677,-0.02568568,-0.10202407,0.00593782,0.00340309,0.0568388,-0.01706815,-0.05206535,0.07977509,-0.03632733,0.04797981,0.00281872,0.03008462,0.0101326,0.03412219,0.00046775,0.00070106,0.06676755,0.00188437,0.03356944,0.00509671,0.0147752,0.08314393,-0.08128648,0.00098955,0.03150217,0.0405925,-0.07562442,0.04274894,0.06916753,0.03618279,0.03311146,0.08050104,0.06736423,0.00898042,-0.00848935,0.01920099,0.01079777,-0.05204452,0.00674562,0.0190157,0.05312429,-0.22551791,0.00603412,-0.03321509,0.0530827,-0.00089214,-0.01364228,0.02279692,-0.01889325,0.03386063,-0.05730623,0.02325038,0.07057981,0.00645993,-0.04544432,-0.0267852,-0.0340955,-0.03564129,-0.03182994,0.05233058,0.0086494,0.0510018,0.06385192,0.19149671,-0.01416561,0.00660621,-0.00263844,-0.02168092,0.01654382,-0.02807938,0.00784716,0.00372025,0.02169927,0.02405322,-0.01654059,-0.0522547,0.05582081,0.02856482,0.00679829,-0.0091226,0.03240313,-0.06627896,0.0335766,0.00489099,-0.02790144,0.10906269,-0.03940262,-0.00280891,-0.06372339,0.03432788,0.06964223,-0.09746642,-0.06123547,-0.05184251,-0.03387343,0.01412708,0.07842808,0.03024342,-0.00372955,0.01264374,0.01802305,0.00922228,-0.00033763,0.03611639,0.05992729,-0.0044154],"last_embed":{"hash":"7xyye2","tokens":357}}},"text":null,"length":0,"last_read":{"hash":"7xyye2","at":1753423635700},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{1}","lines":[97,102],"size":1315,"outlinks":[{"title":"_**races at three horse tracks**_","target":"https://saliu.com/horses.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04957772,-0.0251971,-0.03129801,0.084365,-0.01006116,0.04720748,0.00391518,0.04004322,0.04509221,0.01091912,-0.01098887,0.00441045,0.01652741,0.00493826,-0.05312348,-0.02141823,0.01384984,0.01683604,-0.05434979,0.02105091,0.06071747,-0.03849751,-0.04605444,-0.05346655,0.08820307,-0.04153299,-0.03319428,-0.07166313,-0.03749557,-0.19590673,0.002234,-0.01006463,-0.07513513,-0.06949722,-0.10844796,-0.02651536,-0.01126998,0.04312706,0.01158892,0.07323265,0.02608332,0.04091326,-0.05486249,-0.03322965,-0.04245698,-0.05222049,-0.00075544,-0.0054071,0.06471705,0.02128648,-0.04705106,0.00161314,-0.00812611,-0.00432761,0.07352823,-0.00653111,0.02259706,0.08574497,-0.04840118,0.00866397,0.02560588,0.08573557,-0.18147522,0.0537376,-0.04124176,0.03917836,-0.03927012,0.02733769,-0.02962614,0.12789035,0.05193742,-0.0027031,-0.03825662,0.03540587,-0.0171844,0.0202361,0.02322426,-0.04040857,-0.0320432,0.06220761,-0.02498789,-0.02529833,-0.03123708,-0.04419611,0.01438708,0.02640823,-0.02344354,-0.01971375,0.06998645,-0.04316781,0.04425912,0.03179294,0.03052061,0.05833853,0.00561496,-0.00086793,0.01677574,-0.06043853,-0.00126348,0.12511347,-0.01870972,0.01699673,-0.01338517,0.00221895,0.08294491,-0.02932474,-0.06362358,-0.08249121,-0.02936565,0.04601297,0.02520098,0.02295753,0.07695107,-0.03417747,-0.0387769,0.05254059,0.0289096,-0.02754038,-0.00156119,0.02631869,-0.06770571,0.0465432,-0.02184193,0.02723824,-0.0176343,-0.05065401,0.09167932,0.04389111,-0.03280193,0.01713215,0.0357694,0.00631078,-0.1373416,-0.02193564,-0.0463476,-0.01634008,0.012934,0.00790378,-0.00303231,0.00233706,-0.01834273,-0.07433697,0.0936568,-0.09959924,0.00899659,0.03956478,0.02297477,-0.00139404,-0.0268678,-0.00591928,-0.0405368,0.02520599,-0.03759358,-0.01822657,0.00151146,-0.00890347,-0.01005222,0.06823893,-0.03255108,-0.05844003,-0.07556158,-0.01807605,-0.04503735,0.07064775,-0.0574218,-0.11134537,-0.03005848,-0.00172819,-0.04465738,-0.03649164,-0.03488542,-0.02392786,-0.07852317,0.03229728,0.06752226,-0.01349288,-0.1458379,-0.0498028,-0.02285742,0.02507909,0.04650909,-0.00787947,-0.05718849,-0.00366011,0.01611785,-0.01293822,0.04722818,-0.0403606,0.04503473,0.08556747,-0.02227293,0.01532145,-0.04333537,-0.02647865,-0.01513973,-0.01106818,-0.01876213,0.00166889,0.12025841,-0.00092748,-0.07394885,0.00562071,0.01041535,0.01491771,0.00283607,0.03353962,-0.00568345,-0.10191307,0.06353155,0.01153524,-0.02578751,0.03868798,0.02642699,0.02871579,-0.02595612,0.03164611,0.02487002,0.06798101,-0.0370107,-0.00290591,0.08899521,-0.00172873,-0.00953361,-0.18123759,-0.02981456,-0.06281532,0.00412094,0.05738229,-0.04262713,0.02244573,0.0234155,0.06997446,0.10268838,0.07235357,-0.04483574,-0.02006543,0.02240163,-0.01061614,0.09194334,-0.08344465,-0.02603405,-0.03067979,0.00468022,0.00417628,0.01089302,-0.00313149,-0.05833241,0.07166469,-0.02005377,0.15904865,0.01533018,0.01183773,-0.01956238,0.02810422,-0.00151659,-0.04362471,-0.00516997,0.04646608,-0.01800638,0.04129198,-0.04735885,-0.05002512,-0.03706716,0.00614323,0.03890521,-0.03479207,-0.06853051,-0.02488832,0.03148594,0.01129275,0.08334479,0.02952289,0.045169,0.06801739,-0.00865503,0.02270472,0.01574939,0.08475652,-0.00377247,-0.09865452,-0.00843199,-0.00323829,0.07015022,-0.011052,-0.04925156,0.03858347,-0.02604184,0.0368982,-0.03062388,0.03267673,-0.00234531,0.00110243,0.01702558,-0.01506276,0.05974231,0.00257699,0.03133013,0.00633044,0.01558689,0.10810671,-0.06742922,0.02621727,0.03115058,0.04633627,-0.04910799,0.04648419,0.06058935,0.03519969,0.01994528,0.0363335,0.07223818,-0.00371186,-0.01415355,-0.001203,0.00768042,-0.05932866,-0.02400246,0.01025957,0.03913769,-0.25327995,0.02301526,-0.00521064,0.02707315,-0.00003962,-0.02377356,0.00244617,-0.00730827,0.00188761,-0.04946413,0.05583794,0.07298782,-0.0033191,-0.04047406,-0.06600899,-0.02811627,-0.00413563,-0.05746132,0.04732313,0.0315471,0.02930637,0.05558178,0.19145194,-0.01395698,0.02889218,-0.01365311,-0.02794523,0.05399137,-0.06855422,-0.01365571,0.02789108,0.02858473,0.01961791,-0.04027204,-0.05088096,0.11565021,0.02051324,0.03597631,-0.00175328,0.05236843,-0.06490493,0.0317345,-0.01464731,-0.01666334,0.08159155,-0.05437527,-0.01163326,-0.03044231,0.03532239,0.03450928,-0.09903391,-0.03985546,-0.06680781,-0.01636927,0.00787859,0.05031533,0.04361901,0.00138782,0.02631809,0.0317221,-0.00106887,0.02838103,0.02121792,0.09116514,0.02017922],"last_embed":{"hash":"1f1us3x","tokens":107}}},"text":null,"length":0,"last_read":{"hash":"1f1us3x","at":1753423635821},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{2}","lines":[103,104],"size":216,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07077264,-0.02217952,-0.03988715,0.05684166,-0.02387531,0.04488743,-0.01132135,0.01473736,0.05792127,0.01054466,-0.00905437,0.02417306,0.02397737,0.00555756,-0.08008911,-0.01525419,0.00704649,0.00477122,-0.04374037,0.0239241,0.03017296,-0.0620035,-0.04085781,-0.08312763,0.08022315,-0.02212191,-0.03787097,-0.07004763,-0.04962236,-0.24536093,-0.00412423,-0.01102213,-0.04324535,-0.08084812,-0.09910183,-0.02028882,-0.02698147,0.05377042,-0.00150541,0.0572276,0.03406755,0.02835706,-0.03997065,-0.02542968,-0.02115831,-0.02990882,-0.02068739,-0.00215215,0.06111277,0.03053054,-0.06240785,0.00060385,-0.01816683,-0.01420655,0.07604843,0.00440248,0.02758742,0.08728198,-0.03057915,0.00285906,0.02222497,0.08731727,-0.17693473,0.06886656,-0.0545724,0.04038342,-0.02957502,0.03104941,-0.04020829,0.10480884,0.05578396,0.01970071,-0.03177297,0.02125904,0.01221301,-0.00496452,0.01841435,-0.03234442,-0.05418534,0.05843446,-0.06025641,-0.03490889,-0.01738912,-0.02488545,0.02539289,0.0279114,-0.00532934,0.01092905,0.0534317,-0.04450745,0.06863832,0.06129343,0.02469658,0.03878947,0.02266097,0.01126772,0.02216092,-0.03576205,-0.00012548,0.10348093,0.02054551,0.02173749,-0.01181036,-0.00336474,0.07741356,-0.04447132,-0.06373304,-0.05738888,-0.01571098,0.03716945,0.04306325,0.00477006,0.09950484,-0.05801025,-0.03488495,0.07006437,0.01983643,-0.01179022,0.00360221,0.03516673,-0.0647939,0.04404268,-0.01395538,0.03748656,-0.0418722,-0.03861262,0.09581418,0.04858412,0.00441506,-0.00202872,0.01634124,-0.00883222,-0.14007756,-0.03059567,-0.05037758,-0.05777771,0.00641155,0.01572068,0.01418157,0.00936003,-0.02102485,-0.06847567,0.08522876,-0.09727767,-0.02775914,0.05549252,0.02508415,-0.01347153,-0.00004874,-0.01524169,-0.03603239,0.0071966,-0.01998999,-0.03199746,0.01377961,-0.0170977,0.02517137,0.10184427,-0.05691377,-0.03653695,-0.08173644,-0.03228981,-0.00989643,0.07143384,-0.0323471,-0.10574214,-0.03895614,0.01053382,-0.02777775,-0.06055848,-0.02882569,-0.04157282,-0.06079486,0.03023452,0.04094682,-0.02276926,-0.12977529,-0.03862641,0.00977628,0.02848535,0.05452534,-0.02416996,-0.05138513,0.00757259,0.00547894,-0.03682583,0.04846188,-0.0463062,0.03675108,0.0633426,-0.03563444,-0.03578937,-0.05313802,0.0021862,-0.02645657,-0.03457628,-0.00265263,0.00588383,0.0926419,0.0150133,-0.08775708,-0.00806331,0.01539291,0.02458241,0.0073028,0.03920802,-0.01146584,-0.10301612,0.08430226,0.03403446,-0.01166342,0.02650232,0.01594344,0.02717067,-0.04769369,0.04048766,0.03268363,0.07574957,-0.00613326,-0.00310613,0.04153227,-0.00367163,-0.00902923,-0.18773231,-0.04506771,-0.09068512,0.03884842,0.05170299,-0.05114716,0.00911969,0.0012692,0.06826417,0.10664621,0.07214885,-0.05877729,-0.00345904,0.02977059,0.00502605,0.04643803,-0.09738766,-0.00964392,-0.03690803,0.01285756,0.0249606,-0.00891362,0.00162539,-0.07337687,0.05653727,-0.03045109,0.16183376,0.00939622,0.01639845,-0.02959874,0.03783413,0.02045019,-0.03665544,-0.02559324,0.0276751,-0.00445088,0.04679141,-0.02074736,-0.04696082,-0.04555774,-0.01439547,0.01408505,-0.02146153,-0.09584376,-0.04130854,0.0305011,0.00401123,0.07062317,0.02718078,0.05441294,0.07787004,-0.01553474,0.04397446,0.00186209,0.10546536,-0.02526781,-0.08644255,0.00765376,-0.0082234,0.05694946,-0.00232361,-0.04137245,0.06983573,-0.03027621,0.04591304,-0.01509926,0.04253723,-0.00831279,0.02529141,0.00015675,-0.00361931,0.03276596,-0.00237512,0.04075628,-0.0061313,0.04420304,0.09063923,-0.07142743,0.01953379,0.03149851,0.04743768,-0.05905719,0.05492656,0.07042118,0.0157125,0.02773132,0.06058788,0.06014829,0.00172454,-0.02105343,0.0053102,0.02668997,-0.05163008,0.00273178,0.01403951,0.04978844,-0.23122928,0.01845605,-0.02879231,0.03034339,0.01297382,-0.03061986,0.01715578,-0.01186665,0.01823599,-0.04626072,0.04796017,0.08713442,-0.00068305,-0.04964665,-0.02867229,-0.03230766,-0.02707634,-0.03188097,0.05510009,0.01281398,0.07055603,0.05113786,0.17827259,-0.0029215,0.01876033,-0.0027276,-0.02494699,0.03687947,-0.03679919,-0.01652985,-0.00982983,0.01526787,0.03708119,-0.03474874,-0.03806851,0.05276033,0.0183219,0.0335568,-0.01319852,0.02040761,-0.06089459,0.03018356,0.02308017,-0.02064176,0.09650787,-0.03793136,-0.00545863,-0.05210671,0.04132701,0.06277609,-0.08608872,-0.07107448,-0.04609433,-0.03142839,0.02226737,0.07974721,0.03354385,-0.00178432,0.03233314,0.01844909,-0.0001992,-0.00713284,0.03462924,0.08259672,0.00474969],"last_embed":{"hash":"1oa6j49","tokens":424}}},"text":null,"length":0,"last_read":{"hash":"1oa6j49","at":1753423635856},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>#{3}","lines":[105,114],"size":1603,"outlinks":[{"title":"_**The non-favorites won 169 horse races out of 272 or 62.13% of the time.**_","target":"https://saliu.com/horseracing.html","line":5},{"title":"The best strategies in lottery, lotto are based on wacky filters to reduce many combinations.","target":"https://saliu.com/HLINE.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06154274,-0.0259031,-0.0389864,-0.0102365,-0.03592961,0.04653008,0.0554895,0.0217315,0.03963984,-0.02528538,0.0118422,0.03082328,0.04238561,0.00626659,-0.0419037,0.0045278,-0.02039555,-0.02274162,-0.05343905,0.01590002,0.04293926,-0.04687489,-0.07733199,-0.0941371,0.06666802,-0.0380738,-0.03879674,-0.05383786,-0.04161967,-0.22110872,-0.00359209,0.01510067,0.00607136,-0.07612475,-0.08321442,-0.02890709,-0.01959191,0.06037319,-0.03515511,0.03550511,0.01086643,-0.00597732,-0.00333182,-0.03260339,0.01038417,-0.01263972,0.01865526,-0.02031761,0.03102884,0.01187521,-0.06255584,0.05331618,-0.00051626,0.01469795,0.06322028,0.00010531,0.04936899,0.10356905,0.00001379,0.01962545,-0.03506526,0.04217357,-0.17863423,0.07367089,-0.03842683,0.04798593,0.00399503,-0.00018587,-0.02602243,0.09369028,0.05316213,0.03843255,-0.03326124,0.0637339,0.03322826,-0.03741151,-0.00372557,-0.04979853,-0.02700164,0.06320366,-0.02234565,-0.0643019,-0.0140813,-0.03596734,0.02613502,0.0403958,0.0153262,0.02525656,0.06377017,-0.04493081,0.01002627,0.03709579,0.02442708,0.01004203,0.05016331,0.00171003,0.05442506,-0.03835475,0.00118715,0.09928129,0.02895424,-0.00682603,-0.01425977,0.00097648,0.06787299,-0.05369797,-0.02852626,-0.04016154,-0.01554078,0.0262566,0.01689824,0.01798373,0.13265763,-0.00228328,-0.02717072,0.04099233,-0.00234242,-0.03412094,0.04079462,0.00901703,-0.05938109,0.03551089,-0.00782283,-0.01082164,-0.0182854,-0.01449841,0.02243559,0.0820858,0.00304376,-0.00362482,0.05198888,-0.03304319,-0.12329735,-0.03991527,-0.01990953,-0.04319701,-0.01280623,0.00231079,0.00055237,-0.0014347,-0.05295509,0.0046375,0.06227067,-0.09117238,-0.03582937,0.04929502,0.02979821,-0.00919932,0.00448619,0.01299209,0.00542718,-0.04653013,-0.03484027,-0.04749559,-0.01976202,-0.00676606,0.08799662,0.07517427,-0.04907205,-0.02109722,-0.03473521,-0.04874709,-0.00461469,0.07577961,-0.00348498,-0.09571822,-0.05801056,0.00498516,-0.03578083,-0.0682935,-0.01777736,-0.01068753,-0.04979702,0.0590914,0.06298403,-0.02251377,-0.10683419,-0.05384338,-0.06860265,0.00626652,0.01946512,-0.04458028,-0.04144524,0.00409624,-0.00869672,-0.08170564,0.04861853,-0.01187581,0.04954741,0.06630187,-0.04408554,0.02336985,-0.05316151,0.03579097,-0.00234724,-0.01304688,0.01754898,-0.02802024,0.05473996,-0.04509779,-0.05214278,-0.05148551,-0.01854022,0.05055615,-0.05924691,0.04903036,-0.00083444,-0.07857315,0.11543798,0.0103104,-0.03133812,-0.00851606,0.04366865,0.06811363,-0.03964867,-0.01201711,0.02394876,0.03280371,-0.01312652,-0.0002669,0.05390206,0.00429217,-0.06433199,-0.16978972,-0.0466867,-0.0864196,0.01763412,0.0250535,-0.01408061,-0.0088229,-0.02297896,0.03477677,0.09721915,0.08692262,-0.0510625,-0.00246417,0.064326,-0.02207682,0.00341547,-0.07822143,-0.02567789,-0.05686847,0.01156597,-0.02217366,-0.01052059,-0.02533931,-0.06263182,0.02176951,-0.01604528,0.17498466,0.02118005,-0.00734587,0.04034286,0.04830133,0.0278393,-0.01534012,-0.00732998,0.00701292,0.02815298,0.00128657,-0.02819304,-0.06419826,0.00999404,-0.05483301,-0.02151174,-0.00374409,-0.09056848,-0.08282629,0.00917121,-0.01508527,0.08849589,-0.01561712,0.03527717,0.07508662,-0.03560357,0.02653646,-0.01327542,0.06890183,-0.04185276,-0.08054702,0.00406167,0.04442544,0.08504445,-0.02703781,-0.0363876,0.03599463,-0.03374321,0.05281598,-0.00052318,0.03858771,0.00461475,0.0219271,-0.00021507,-0.03371903,0.0743247,0.00499596,-0.01885836,0.02387949,0.02266049,0.07423891,-0.02860755,0.03415359,-0.03446218,0.02437669,-0.06966205,0.06841655,0.07953169,0.02299022,0.02364937,0.05808844,0.03408361,-0.00913426,0.00489992,0.02419444,0.00494068,-0.03788846,0.00215397,0.01100642,-0.00695015,-0.25112468,0.03431386,-0.02964179,0.04803717,0.01547363,-0.02413102,0.01122522,0.03424691,0.01323294,-0.03036865,0.07353397,0.01601319,0.03016109,-0.03769657,-0.00730086,-0.03312909,-0.03441777,-0.03200865,0.06772039,0.05667916,0.06493947,0.06820656,0.25380957,-0.02388241,0.00448175,-0.00098661,0.01348782,0.01717323,-0.08030968,0.00559309,0.01256873,0.00727256,0.05104608,-0.03080961,-0.019069,-0.00246927,0.0129036,0.05658937,-0.04049317,0.01414346,-0.08258273,0.00747424,0.01980375,-0.00135183,0.14959317,0.04780385,-0.02709897,-0.06593496,0.06995736,0.05070462,-0.07854208,-0.05794799,-0.02978089,-0.02038188,-0.01193281,0.0487765,0.02439958,0.00969497,0.02904803,0.01147698,0.04244002,0.07965123,0.05441879,0.03440525,0.01332862],"last_embed":{"hash":"1j9t6qj","tokens":414}}},"text":null,"length":0,"last_read":{"hash":"1j9t6qj","at":1753423635992},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>","lines":[115,165],"size":10806,"outlinks":[{"title":"<u><i><b>lottery filters, lottery filtering, reduction: my discovery</b></i></u>","target":"https://saliu.com/bbs/messages/919.html","line":5},{"title":"<u><i><b>12-number lotto wheel</b></i></u>","target":"https://saliu.com/lottowheel.html","line":7},{"title":"<u><i><b>History of Lottery, Experience, First Lotto Software, Systems, Strategies</b></i></u>","target":"https://saliu.com/bbs/messages/532.html","line":9},{"title":"<u><i><b>Lottery Software, Lottery Strategies, Lotto Systems</b></i></u>","target":"https://saliu.com/LottoWin.htm","line":13},{"title":"_**increases chances to hit lotto jackpots sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":18},{"title":"_**Lottery Strategies, Lotto Systems**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":20},{"title":"_**Lottery Strategy, Systems, Software Based on Lotto Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":21},{"title":"_**Euromillions Strategies, Systems, Software, Winning Numbers**_","target":"https://saliu.com/euro_millions.html#software","line":22},{"title":"Playing just one lottery ticket every drawing is a losing, bad lotto strategy.","target":"https://saliu.com/HLINE.gif","line":24},{"title":"_**Formulas, Software to Calculate Lottery, Lotto <u>Odds, Hypergeometric Distribution Probability</u>**_","target":"https://saliu.com/oddslotto.html","line":27},{"title":"The best mathematical lottery strategy plays more tickets less frequently, in fewer drawings.","target":"https://saliu.com/HLINE.gif","line":30},{"title":"<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>","target":"https://saliu.com/filters.html","line":33},{"title":"<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>","target":"https://saliu.com/strategy.html","line":34},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":35},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":36},{"title":"<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>","target":"https://saliu.com/Newsgroups.htm","line":37},{"title":"<u><i><b>Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software</b></i></u>","target":"https://saliu.com/bbs/messages/42.html","line":38},{"title":"<u><i><b>Step-By-Step Guide to Lotto, Lottery Filters in Software</b></i></u>","target":"https://saliu.com/bbs/messages/569.html","line":39},{"title":"<u><i><b>Pick-3 Lottery Strategy Software, System, Method, Play Pairs</b></i></u>","target":"https://saliu.com/STR30.htm","line":40},{"title":"<u><i><b>Vertical or Positional</b></i><b> Filters in Lottery Software</b></u>","target":"https://saliu.com/bbs/messages/838.html","line":41},{"title":"<u><i><b>MDI Editor Lotto Is the Best Lotto Lottery Software; You Be Judge</b></i></u>","target":"https://saliu.com/bbs/messages/623.html","line":42},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":43},{"title":"_**Lottery, Software, Systems, Science, Mathematics**_","target":"https://saliu.com/lottery.html","line":44},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":45},{"title":"_**Bookie Lottery, Good Odds, Payouts, Special Software**_","target":"https://saliu.com/bookie-lottery-software.htm","line":46},{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":47},{"title":"Download the best software for strategies in lotto, gambling, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":50}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06713026,-0.02864003,-0.03351645,-0.01281766,-0.03608488,0.04717279,0.05449402,0.02547204,0.04268532,-0.02495147,0.01229319,0.03406481,0.04653681,0.00381241,-0.03760802,0.00423374,-0.02777824,-0.01758096,-0.0580822,0.01103402,0.04863334,-0.04693287,-0.0780526,-0.09770154,0.0668329,-0.03809225,-0.04366696,-0.05733294,-0.03729872,-0.22551702,0.00340948,0.02429561,0.0057974,-0.07453012,-0.08244412,-0.03428631,-0.02358488,0.05863304,-0.03927226,0.03295165,0.01087096,-0.00640478,0.00119613,-0.03467531,0.01411428,-0.01876485,0.01822577,-0.01815404,0.03258866,0.01548319,-0.06255339,0.04805207,-0.00130006,0.02094715,0.06426278,0.00485901,0.04950096,0.10383023,-0.00293782,0.02303235,-0.0353105,0.04417785,-0.17780054,0.07010903,-0.03414884,0.04292518,0.0021593,-0.0019527,-0.01766296,0.09257863,0.04996509,0.03933466,-0.03807611,0.06413429,0.02876647,-0.03059911,-0.00794954,-0.05043774,-0.02319747,0.06651302,-0.02291566,-0.05881636,-0.01863794,-0.03764386,0.02306451,0.04163155,0.01280293,0.0209785,0.06279769,-0.04349437,0.01004229,0.03261991,0.02161207,0.01094539,0.05722609,0.00179492,0.05169957,-0.04097262,0.00663959,0.09631539,0.02809293,-0.00990117,-0.01498848,-0.00271946,0.06340807,-0.05401626,-0.0338856,-0.03922868,-0.02043976,0.02719492,0.01967952,0.01702562,0.12616676,-0.00593499,-0.03123773,0.04154461,-0.00409225,-0.03162995,0.03923325,0.01079033,-0.062521,0.0332377,-0.01065612,-0.01193473,-0.01693503,-0.0127039,0.02293044,0.08156405,0.00035119,-0.00165937,0.05242416,-0.03122049,-0.12311173,-0.04186638,-0.01428166,-0.04785326,-0.01167965,0.00206149,0.00425167,-0.00321046,-0.0458675,0.00446327,0.06638034,-0.08925006,-0.03549642,0.05013342,0.02975954,-0.00868112,0.00641192,0.0166524,0.00463596,-0.047125,-0.03772923,-0.04951123,-0.01829725,-0.00165947,0.09273219,0.07763683,-0.04600792,-0.01950583,-0.03580152,-0.04834332,-0.00560484,0.07448036,-0.00304296,-0.09852929,-0.05701356,0.01465042,-0.03591981,-0.06705201,-0.0157343,-0.00934927,-0.04728436,0.05864696,0.06546269,-0.02185205,-0.10715546,-0.04870438,-0.0647459,0.00616783,0.01788066,-0.0465178,-0.04084649,0.0067506,-0.01584686,-0.08156984,0.04718259,-0.01181174,0.04766397,0.06762017,-0.04633375,0.0248825,-0.05469014,0.03837615,0.00040576,-0.01271355,0.01289883,-0.02667506,0.05315189,-0.04354684,-0.04790805,-0.04683986,-0.02104793,0.04841696,-0.05542935,0.04997969,-0.00698287,-0.07805779,0.11551622,0.0098594,-0.02787482,-0.01067416,0.04566586,0.07621082,-0.03822992,-0.00749264,0.0229254,0.0310118,-0.0126332,-0.00108237,0.05201434,0.0019706,-0.06468576,-0.17183229,-0.04967753,-0.08517397,0.01385724,0.02709522,-0.01072756,-0.01152258,-0.02441219,0.03797591,0.09337295,0.0860957,-0.04934316,-0.00144095,0.06323447,-0.02064341,0.00599585,-0.0783795,-0.02300666,-0.05383658,0.01393499,-0.02075361,-0.00509791,-0.02164267,-0.06639281,0.02101267,-0.01609202,0.17726251,0.01843287,-0.00961557,0.03878537,0.04926173,0.0299973,-0.01367907,-0.00644693,0.01175203,0.03146084,0.00225633,-0.02816174,-0.06234076,0.00714073,-0.05776612,-0.02375345,-0.00063065,-0.09695226,-0.07391642,0.00876758,-0.01896988,0.08010094,-0.01020913,0.03737405,0.07363899,-0.03796806,0.02710461,-0.01084151,0.06087218,-0.04511438,-0.0824476,0.00599646,0.04254959,0.08396231,-0.02629417,-0.04253668,0.03335988,-0.03448722,0.04656218,-0.00174901,0.03365666,0.00790692,0.02286492,0.00041069,-0.02858603,0.07599469,0.00853786,-0.01159247,0.02353234,0.01973589,0.07419083,-0.03026403,0.03299433,-0.03197902,0.01809392,-0.06778019,0.06402813,0.08090803,0.02109821,0.01231393,0.06295846,0.03877029,-0.00578914,0.00870018,0.0237987,0.00385352,-0.03603815,0.00551364,0.00897709,-0.00737514,-0.25622225,0.03409194,-0.03654214,0.04934391,0.01648272,-0.02944015,0.01369027,0.03323816,0.00905366,-0.02833834,0.07048116,0.01597051,0.03245495,-0.04189036,-0.00093225,-0.0280507,-0.03547669,-0.03354519,0.06709196,0.05212425,0.06533951,0.06754795,0.25437981,-0.02399555,0.0046204,0.0028539,0.01196203,0.02177098,-0.07843996,0.00742538,0.0089449,0.00523678,0.05497072,-0.03190232,-0.01934044,-0.00281659,0.0137739,0.05361587,-0.03831447,0.01549601,-0.08536227,0.00581424,0.01939788,-0.00438775,0.15280727,0.05077152,-0.02868459,-0.0619479,0.06780802,0.05043837,-0.07358746,-0.0546147,-0.02610318,-0.02323278,-0.00888919,0.04652609,0.02023475,0.0105652,0.02526623,0.0110124,0.04428926,0.07788626,0.05521121,0.03865485,0.00948882],"last_embed":{"hash":"yv2m89","tokens":459}}},"text":null,"length":0,"last_read":{"hash":"yv2m89","at":1753423636137},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{1}","lines":[117,130],"size":4206,"outlinks":[{"title":"<u><i><b>lottery filters, lottery filtering, reduction: my discovery</b></i></u>","target":"https://saliu.com/bbs/messages/919.html","line":3},{"title":"<u><i><b>12-number lotto wheel</b></i></u>","target":"https://saliu.com/lottowheel.html","line":5},{"title":"<u><i><b>History of Lottery, Experience, First Lotto Software, Systems, Strategies</b></i></u>","target":"https://saliu.com/bbs/messages/532.html","line":7},{"title":"<u><i><b>Lottery Software, Lottery Strategies, Lotto Systems</b></i></u>","target":"https://saliu.com/LottoWin.htm","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05387997,-0.03757982,-0.00232804,-0.00449142,-0.01185117,0.04365392,0.0502872,0.01134282,0.02387773,-0.02543714,0.03190116,0.01677352,0.02837693,0.03580176,-0.01221582,-0.01301828,0.00495206,-0.01664425,-0.07218315,0.01683133,0.1157739,-0.01395601,-0.07646667,-0.05426885,0.06292408,-0.04414038,-0.02461058,-0.05962174,-0.04702541,-0.18881372,0.00525647,-0.00450463,-0.04318613,-0.05827377,-0.08846548,-0.05761256,-0.04267428,0.10477519,-0.02224748,0.06000733,0.03582199,-0.00675958,-0.02337164,-0.02770817,-0.02746468,-0.0278871,0.02953864,0.00528509,0.04408366,-0.00415973,-0.03593479,0.04969318,0.00668273,0.01918832,0.06130089,0.02199722,0.04181612,0.09866098,0.02493717,0.03918786,-0.02436931,0.06679675,-0.16354914,0.03833215,-0.03766594,0.02536939,0.01463607,0.00302253,-0.04502034,0.0785801,0.07416049,0.04496837,-0.0121417,0.06061533,0.05490171,-0.0326711,-0.02807678,-0.07580449,-0.03824108,0.07373543,-0.04077972,-0.03489509,0.01420799,-0.03654082,0.02952065,0.01736034,0.00410109,-0.00406393,0.060381,-0.03038872,0.03078081,0.03170973,-0.02091817,0.01264957,0.05874899,0.02218899,0.02232893,-0.05373284,0.01076343,0.13588782,0.00241602,0.03116653,-0.00131233,0.00566116,0.06646094,-0.05706552,-0.05473605,-0.01393055,-0.0039439,0.04687776,0.01762285,0.00409405,0.10691474,-0.02096351,-0.05943309,0.04215875,0.02760236,-0.01803009,0.01325604,0.01257309,-0.05350519,0.01474486,-0.01053954,-0.04165292,0.0143073,-0.00194724,0.01962052,0.06525362,-0.00114489,0.016133,0.05112938,-0.03838395,-0.12354873,-0.0260874,-0.0347873,-0.03688097,-0.00038907,-0.00998613,-0.00275075,0.01986344,-0.03995597,-0.02264851,0.04856437,-0.12684412,-0.01462509,0.08700988,-0.01781257,0.0318203,-0.03062763,-0.00804944,-0.01709306,-0.00212603,-0.05452628,-0.06177302,-0.01271992,0.00379176,0.0351372,0.06078213,-0.03442633,-0.03593285,-0.05625528,-0.03125645,-0.03332241,0.09290573,-0.04917294,-0.0828189,-0.07828917,-0.04057874,-0.04507586,-0.06551974,-0.0418979,-0.00892163,-0.03865806,0.04111274,0.06449858,-0.01990564,-0.0947274,-0.05222573,-0.05529889,-0.00286337,0.02413325,-0.0242331,-0.0757103,-0.00788537,-0.00136353,-0.04106768,0.05191303,-0.02242399,0.06984519,0.06596773,-0.06704228,0.02050769,-0.02556756,0.01596252,-0.00951353,-0.01192804,-0.01898382,-0.01230967,0.0591927,-0.03135036,-0.02674194,-0.02059015,-0.00205852,0.04740797,-0.05375392,0.0571709,0.01061722,-0.08882598,0.11151227,0.01818799,-0.05090255,-0.00111689,0.02321075,0.07423984,-0.01921936,-0.02197961,0.01516515,0.02884943,-0.01572569,-0.03215343,0.0528703,0.00409057,-0.06798121,-0.17282683,-0.04869877,-0.05717772,0.02986145,0.04631994,-0.00257601,-0.02879342,-0.01056079,0.04135645,0.10609663,0.05044293,-0.06377742,0.01427479,0.0916978,-0.0040928,0.03487938,-0.08746833,-0.03450828,-0.03128771,0.00763786,0.02564826,-0.00585429,-0.00758026,-0.10202592,0.03037052,0.00724498,0.14508057,-0.00163862,0.01558085,0.02560696,0.03792025,-0.00096058,-0.02231202,0.01224007,0.00708841,0.00754292,-0.00957675,-0.03898815,-0.08300614,-0.01234565,-0.00639673,0.00022797,-0.01144769,-0.10014997,-0.06318797,0.03412873,-0.03489926,0.13267618,-0.021674,0.02566063,0.05789884,-0.01769397,0.0440025,-0.00418356,0.09688033,-0.00451385,-0.08755728,-0.02436014,-0.01055683,0.05736843,-0.02571298,-0.02772512,0.03789286,-0.02215072,0.03856927,-0.01457558,0.04249709,0.00136335,-0.00174287,0.01097575,-0.05897842,0.05472108,-0.00992994,0.01577402,-0.00403349,0.06798553,0.06234326,-0.02516973,0.00539874,-0.0394083,0.08732001,-0.01457844,0.06462889,0.04964523,0.02702325,0.00853865,0.0408774,0.02815865,-0.00546416,-0.00367918,0.0146444,-0.00661108,-0.04849805,-0.00466632,-0.00715731,-0.02705999,-0.25378475,0.01688611,-0.03359235,0.04113135,0.0013603,-0.03590401,-0.0096938,0.04517616,0.02438183,-0.01709403,0.05317338,0.03125365,0.01392509,-0.03274903,-0.04042994,-0.02940998,-0.01206657,-0.05163825,0.08793341,0.07541018,0.06119983,0.0558752,0.24336961,-0.02189305,0.0307561,-0.00955586,0.00805196,0.04106487,-0.12558833,-0.01515539,0.04558702,0.02986826,0.06628481,-0.00597319,-0.02690582,0.01470496,0.0268345,0.05857307,-0.02393177,0.00658051,-0.05505519,0.02631751,-0.02157735,0.00082886,0.13844694,-0.02419488,-0.01452945,-0.04196882,0.05636124,0.03900504,-0.10292008,-0.02698562,-0.03583979,-0.01911259,-0.00494175,0.02873903,0.03798337,0.00282325,0.04157824,0.02621663,0.03123178,0.06364453,0.04035147,0.02895888,0.00127329],"last_embed":{"hash":"a8ys87","tokens":92}}},"text":null,"length":0,"last_read":{"hash":"a8ys87","at":1753423636310},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{2}","lines":[131,131],"size":255,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09366348,-0.05027374,0.00878656,-0.01471565,-0.01435003,0.07919195,0.06746729,0.01173835,0.04592102,-0.01988271,0.0192391,0.01873707,0.01870234,0.024462,-0.02930819,-0.03270083,0.01182639,-0.00385049,-0.01843204,-0.02167934,0.07729589,-0.00653478,-0.06297266,-0.07297753,0.05930024,-0.00104919,-0.04019917,-0.0830334,-0.07819254,-0.21935843,0.00639465,-0.01105769,-0.04368096,-0.06815061,-0.11347397,-0.03721311,-0.01936401,0.0968949,-0.02238487,0.05545134,0.01289179,0.00400605,-0.01239441,-0.04421235,-0.04017204,-0.02266064,-0.00330649,-0.01020049,0.06360031,0.01540916,-0.07495805,0.01828953,0.01436337,-0.01615055,0.06886702,0.01055165,0.05933534,0.08791639,0.03925252,0.05238423,0.00696022,0.03423041,-0.17712803,0.03854953,-0.02564559,0.03786977,0.00586379,-0.00981697,-0.01874387,0.1185614,0.02035633,0.01667856,-0.0201492,0.08357488,0.07239047,-0.03830497,-0.02748077,-0.04920191,-0.04464801,0.05503942,-0.05845508,-0.02585391,0.00762616,-0.01874036,0.02864563,0.03660027,0.01423275,0.04477,0.0629233,-0.02644311,0.08142397,0.02863044,-0.01210009,-0.01218302,0.02832247,0.02964391,0.06043457,-0.06076362,-0.0114936,0.11313296,0.01133055,0.00167216,-0.01675978,0.04515288,0.06735445,-0.0639627,-0.03800476,-0.01312015,-0.03105741,0.02079185,0.00634191,-0.0040312,0.12939182,-0.02462928,-0.04913082,0.04677004,0.00837924,-0.01896931,0.00947894,0.00088852,-0.01626212,0.00756287,-0.00844707,-0.0391506,0.01803456,-0.0010882,0.00498478,0.07892502,0.00654984,0.02364183,0.0456307,-0.0622467,-0.1252199,-0.04328985,-0.02115884,-0.02581762,-0.02892626,0.01510965,-0.01846012,0.0176593,-0.03368072,-0.02019293,0.0735808,-0.10540906,-0.03526756,0.09694567,-0.02947748,0.02196839,0.00977701,-0.05394546,-0.00765115,-0.01578147,-0.03900556,-0.0379049,-0.00491663,-0.00969466,0.02311768,0.07632181,-0.04559452,-0.03074089,-0.09740178,-0.02311891,-0.03147668,0.09208476,-0.02174572,-0.03679658,-0.06546504,-0.01534584,-0.05289467,-0.09757428,-0.0153258,-0.01437449,-0.0648934,0.03647063,0.08098403,-0.02326242,-0.07071258,-0.05305862,-0.02293308,0.0065495,0.05862365,-0.03599031,-0.05073633,-0.0068103,-0.02679866,-0.07052789,0.04980496,-0.05523754,0.06099505,0.03772841,-0.07056905,-0.0062283,-0.03110691,0.01667248,-0.04852282,-0.03958246,-0.04082372,-0.00848971,0.05405718,0.00215573,0.01128074,-0.03568035,0.02706008,0.01956723,-0.03968193,0.05127672,-0.00198098,-0.07825647,0.12049041,0.00109101,-0.03725579,-0.01584204,0.03909678,0.06925217,0.00815301,0.00020406,-0.00747161,0.03435169,-0.03751623,-0.0045077,0.04087596,0.05579595,-0.05956767,-0.17925419,-0.05948939,-0.03307915,0.0479059,0.02438506,-0.00967635,0.02387864,0.00131437,0.04209045,0.11927699,0.03850207,-0.06175,-0.01392411,0.05341326,-0.00420062,0.03030324,-0.1035161,-0.03997301,-0.05462328,0.03416779,-0.00358852,-0.008173,-0.04781108,-0.07899551,0.03527043,-0.016923,0.14937465,0.00833003,0.01520852,0.00838416,0.07654796,-0.00226035,-0.02488442,0.02041378,0.02724299,0.0426155,-0.00877568,-0.00305646,-0.06122679,-0.01751632,-0.02147756,0.03152301,0.0080651,-0.09303109,-0.08869961,0.02526686,-0.02500096,0.04464823,0.02115564,0.02034435,0.0517802,-0.01162114,0.02544614,0.01547193,0.07109031,-0.01758143,-0.07006925,-0.01432539,-0.01558239,0.07320917,-0.00764288,-0.04932827,0.02412844,-0.02779652,0.06871546,-0.00858356,0.0400696,-0.00556477,0.03225183,0.03418026,-0.01421177,0.05928108,-0.01553717,-0.0211085,-0.00267185,0.06743357,0.07299668,-0.02859761,-0.00933432,-0.02185663,0.06260873,-0.02025571,0.04455251,0.02177717,0.02354972,-0.01570946,0.0605142,0.03957351,0.00221023,-0.00294151,0.00375214,0.00083066,-0.04643752,0.00004509,-0.01040818,0.00081482,-0.24820334,0.01913379,-0.02620102,0.04047932,-0.00340342,-0.04196817,0.01178619,0.02223219,0.01419128,-0.02504533,0.07830467,0.03614875,0.02335753,-0.05990919,-0.03922718,-0.00770033,-0.00835305,-0.02253162,0.08296271,0.00647958,0.06589679,0.04787084,0.23423773,-0.05261659,0.02942962,0.00488023,0.01513422,0.02588047,-0.03662844,-0.00191337,0.02497679,0.02853571,0.07468223,-0.00076662,-0.03472254,0.0355947,0.01992087,0.06754193,-0.01781815,-0.00854026,-0.04196864,0.01657339,-0.0297277,0.01077416,0.11496694,-0.0283017,-0.02381501,-0.054649,0.04637419,0.08688255,-0.08354041,-0.03708389,-0.0351062,-0.03282312,0.01770084,0.05249912,0.01422086,0.01604624,0.0126572,0.0301134,0.0001615,0.03469706,0.07323169,0.053944,0.00785418],"last_embed":{"hash":"odx9x1","tokens":192}}},"text":null,"length":0,"last_read":{"hash":"odx9x1","at":1753423636341},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{3}","lines":[132,132],"size":606,"outlinks":[{"title":"_**increases chances to hit lotto jackpots sevenfold**_","target":"https://saliu.com/bbs/messages/923.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06579707,-0.01654106,0.01269821,-0.00745955,-0.01567914,0.09163923,0.07288922,0.02103249,0.03865653,0.0135543,0.02132541,0.02410134,0.00928769,0.03808523,-0.03007852,-0.03870852,-0.01520181,-0.00066466,-0.0447087,0.01290934,0.06738078,-0.02978393,-0.05890535,-0.0832595,0.06433453,-0.03915688,-0.026945,-0.06243525,-0.04569488,-0.18748672,0.00612681,-0.00380121,-0.09381054,-0.04305048,-0.13392188,-0.06802186,-0.02156387,0.1044388,-0.00590371,0.03208332,0.02123825,0.01029086,-0.03040202,-0.03716486,-0.01800592,0.00788059,0.00739573,-0.00860931,0.06989438,0.00159408,-0.07904834,0.04770586,0.00209847,-0.03637516,0.06962182,-0.00397203,0.061071,0.07987572,0.0177241,0.02998782,0.01591092,0.04904452,-0.15615197,0.02662262,-0.03942839,0.03314528,0.00689233,0.02304192,-0.02178896,0.11561508,0.04131702,0.01916292,-0.02895246,0.06560551,0.06747269,-0.03740893,-0.02745507,-0.04976315,-0.02777008,0.08251001,-0.03196342,-0.04629075,0.0085979,-0.01934123,0.0206471,0.02708346,0.00321953,-0.01742007,0.03961419,-0.07125841,0.06590827,0.02926203,-0.0306058,0.01150578,0.04528803,0.03191375,0.05162492,-0.07167303,-0.02983961,0.12345398,0.01260439,0.00738529,-0.00077321,0.0332297,0.03953174,-0.09035319,-0.01917544,-0.00254551,-0.02137059,0.01148527,-0.01503989,-0.00850667,0.08171329,0.00454621,-0.04017569,0.02807903,0.01942349,0.01165359,0.00681318,0.02624338,-0.04065868,0.0381366,-0.00646328,-0.00351718,-0.000048,-0.03994532,-0.00637036,0.05646222,0.00845348,0.04587299,0.05229685,-0.06438963,-0.06987393,-0.02931875,-0.02995616,-0.02777142,-0.00448054,0.02818551,0.00539445,-0.01749736,-0.00194447,-0.0175751,0.05642353,-0.12145712,-0.02737091,0.05292605,0.00284281,0.03135092,0.00793789,-0.0256533,-0.02331987,-0.01927933,-0.05424984,-0.04249471,-0.0357457,0.00440424,0.03990666,0.07670131,-0.06630032,-0.02593232,-0.1049081,-0.04715418,-0.03481734,0.08466058,-0.04572026,-0.05638922,-0.06663306,-0.0559309,-0.06453467,-0.09459227,-0.02675241,0.00266184,-0.08798177,0.02142128,0.09763741,-0.00674755,-0.07840226,-0.07488005,-0.03209864,-0.00748775,0.04003832,-0.03439219,-0.04366202,-0.00942161,-0.0225703,-0.05115347,0.02722787,-0.03215376,0.0569239,0.0329463,-0.05390687,0.0216912,-0.05684428,0.01374678,-0.02957409,-0.01985893,-0.02561339,-0.00603025,0.02843703,-0.00861335,-0.00553882,-0.02823382,0.00853192,0.03380826,-0.03746385,0.04536257,0.0477235,-0.09912492,0.1314224,-0.00094493,-0.04300736,-0.02877308,0.01861829,0.06034927,-0.00449436,0.01858919,0.04820641,0.0253978,-0.03409835,-0.02402295,0.06454678,0.034668,-0.03390113,-0.17230137,-0.07712119,-0.06129359,0.0400137,0.05018971,-0.03573699,-0.00412138,0.01157563,0.02757315,0.08700647,0.03222186,-0.10382804,-0.02385819,0.09031785,-0.0122939,0.04001867,-0.09987783,-0.06288493,-0.04244883,0.04385553,-0.03301071,0.02938345,-0.0374009,-0.07095471,0.04228131,-0.00987336,0.15421017,0.00328955,-0.01218657,0.01503422,0.04621416,0.03624292,-0.03662771,-0.00200646,0.01148159,0.00998741,0.0123777,-0.04590569,-0.06768737,-0.01946541,0.00898525,0.03422372,-0.02936689,-0.05493648,-0.09552188,0.02538496,-0.02875893,0.09198665,-0.01068857,0.04459349,0.03673944,-0.00795413,0.04375999,0.00414548,0.05591189,-0.01415976,-0.06861815,0.01312527,-0.01780675,0.1084322,-0.01307875,-0.04309896,0.02813402,-0.0363931,0.04839636,-0.00757,0.05317929,0.00929347,0.05012177,0.03534028,-0.00972752,0.05490879,0.00309508,-0.01036165,-0.01359122,0.05780219,0.07633314,-0.00800713,0.00388292,-0.02550373,0.04975739,-0.01928301,0.03955377,0.05966742,0.0187551,-0.00156572,0.05740469,0.01493953,-0.02080289,0.01065216,-0.00972951,0.01469325,-0.04507128,-0.00346252,-0.01267418,-0.03858195,-0.2421634,0.02963931,-0.006235,0.0288907,0.01685123,-0.03081195,-0.02181023,0.0440089,-0.00623939,-0.00462474,0.10449958,0.02085022,0.01171952,-0.02818661,-0.0626763,-0.03130635,0.00441011,-0.02131699,0.08631198,0.01133817,0.08413795,0.06122734,0.23978412,-0.02906734,0.02173381,-0.02696342,0.00890131,0.04469698,-0.08070388,0.00287753,0.03249315,0.03571635,0.07622587,0.00853249,-0.01195742,0.05312823,0.01423312,0.0939399,-0.01960318,0.00200823,-0.05200569,-0.00427846,-0.0096795,0.03461592,0.10492154,-0.01074595,-0.02182849,-0.03479113,0.04323181,0.05893832,-0.07270806,-0.02136657,-0.02543155,-0.04388748,0.00758364,0.01820708,0.01241885,-0.00239731,0.05004284,0.05499692,0.01434936,0.04860521,0.06932476,0.02762735,0.03499782],"last_embed":{"hash":"26ko5b","tokens":141}}},"text":null,"length":0,"last_read":{"hash":"26ko5b","at":1753423636395},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{4}","lines":[133,133],"size":435,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05812945,-0.03786935,-0.00954374,0.00954911,-0.00350273,0.08237346,0.05466979,0.03144645,0.02929866,-0.0231496,0.02879417,0.02187079,-0.01974783,0.05360788,-0.00150537,-0.01617914,0.00169539,-0.01072993,-0.03652673,0.01412189,0.10821811,-0.01102293,-0.06547596,-0.08261576,0.09060949,-0.03850976,-0.00619754,-0.05602373,-0.0178708,-0.19499992,-0.0200589,0.00576434,-0.05596617,-0.06299739,-0.09100239,-0.04814243,-0.04128715,0.09709347,-0.0222351,0.04001127,0.03485723,0.01073845,-0.01560181,-0.01549495,-0.03951205,-0.00489159,0.00417133,-0.00115416,0.01683043,-0.01477007,-0.08815996,0.07637005,-0.00410159,-0.02315446,0.06642658,-0.001513,0.09348701,0.08338432,0.01298996,0.02004266,-0.00083193,0.02977401,-0.17516482,0.03902501,-0.01459386,0.05366315,0.01182826,0.02091266,-0.02848104,0.09836877,0.04707338,0.04832952,-0.0258956,0.05590864,0.05040514,-0.04448408,-0.00905796,-0.060517,-0.0076434,0.06408812,-0.00744338,-0.02689674,-0.00430142,-0.02804566,0.02203816,0.02533123,-0.00452559,-0.01094487,0.05553491,-0.06390917,0.05419932,0.01774614,-0.0228611,0.0250663,0.04419974,0.02628784,0.03749932,-0.0667377,-0.00561695,0.10115888,0.01541294,0.01966284,-0.00313791,0.03400329,0.09251621,-0.05744176,-0.00743308,-0.02881256,-0.00500509,0.01281822,-0.01800276,0.01274162,0.09442439,-0.01356661,-0.06459339,0.01095894,0.02204036,-0.01516398,0.0112886,0.00766512,-0.03853977,0.03737667,-0.00582975,-0.03075181,0.00396603,-0.04419307,-0.00501517,0.03801185,-0.00594591,0.00999659,0.04006917,-0.06046072,-0.10401511,-0.02171838,-0.0375483,-0.0331898,-0.00821401,0.04970596,0.0128058,0.01342196,-0.01735005,-0.04302558,0.05891873,-0.13728584,-0.06570841,0.06807712,-0.04090056,0.00824976,-0.0239747,-0.00498979,-0.02130665,-0.02935673,-0.06270619,-0.04051835,-0.02386067,-0.02261263,0.05305105,0.05829275,-0.02416586,-0.05208755,-0.08876732,-0.05651352,-0.03732804,0.09847197,-0.0271722,-0.08709522,-0.06810556,-0.02366628,-0.03467985,-0.09560543,-0.02964039,-0.00907058,-0.07280654,0.03366819,0.08481881,-0.02271651,-0.06395366,-0.0788138,-0.03888958,0.03475953,0.04128721,-0.03793701,-0.04672616,-0.00683638,-0.0115812,-0.03979973,0.03130947,-0.03976887,0.05670705,0.0371304,-0.06960148,0.013529,-0.04259802,0.01764141,-0.02878853,-0.01318034,-0.01505891,-0.01473328,0.04490858,-0.02436173,-0.02989084,-0.00185318,0.03220901,0.02210888,-0.05766045,0.05466227,0.03306249,-0.08192042,0.13360347,-0.00473586,-0.04016257,-0.0181405,0.0318789,0.05958284,-0.00957073,0.0003409,0.02621061,0.02226719,-0.04566086,0.01578789,0.05518968,0.04117514,-0.05205042,-0.18483876,-0.07669277,-0.06983002,-0.00389922,0.04296739,-0.01498766,0.02362237,-0.00738613,0.03717823,0.07667512,0.04638489,-0.05693588,0.01826927,0.06781708,0.01839672,0.01133356,-0.10822845,-0.07040366,-0.02876972,0.0439635,-0.00600472,0.01144474,-0.04814973,-0.08598132,0.02531751,0.00115007,0.14584944,0.01147854,0.02937871,0.02577633,0.04521025,0.01967048,-0.03428365,0.00699552,0.01134862,-0.01390063,0.01969198,-0.05303723,-0.04861698,-0.00439585,-0.00381122,0.02760074,-0.00947,-0.04612783,-0.08581381,0.00585643,-0.02276202,0.1113875,-0.02038356,0.03366819,0.02542976,0.00389389,0.05586897,0.00299374,0.0530034,-0.01933002,-0.11839279,-0.01739894,0.01310474,0.08551758,-0.01147289,-0.01509756,0.04263869,-0.04038934,0.0448687,0.0001843,0.0126581,-0.01792489,0.03539438,0.02424567,-0.03117435,0.0611462,0.00209877,-0.02235469,-0.00156232,0.0536197,0.04871121,-0.04652698,0.01092939,-0.01127795,0.03339824,-0.04388907,0.05137906,0.05809658,0.02200916,0.02888268,0.04421198,0.00602709,0.00430298,-0.00031702,-0.00125152,0.01191286,-0.06471606,0.00948089,0.01740233,-0.01789182,-0.2526609,0.03995086,0.00554258,0.02720962,0.00664686,-0.01881414,-0.01099254,0.03391213,-0.01550923,-0.01391452,0.07603493,-0.00844583,0.0305296,-0.00001604,-0.06107231,-0.01872146,-0.01270863,-0.04232579,0.08786462,0.03096756,0.0779594,0.06744602,0.24038899,-0.03995911,0.03709426,-0.01963247,0.00574121,0.03557365,-0.08023968,-0.00085515,0.04863577,0.03891113,0.06942509,-0.00257076,-0.00661082,0.05470941,0.03168047,0.06932326,-0.00208955,0.02456849,-0.05689711,0.02737446,-0.01480456,0.0457433,0.12356403,-0.02804794,-0.0188699,-0.07308244,0.03744562,0.05092175,-0.09760396,-0.01198946,-0.01237043,-0.0181828,0.00781727,0.01838859,0.03866863,-0.00938445,0.02680474,0.01739323,0.02932275,0.07456652,0.07167282,0.03612759,0.03701446],"last_embed":{"hash":"zd68ol","tokens":142}}},"text":null,"length":0,"last_read":{"hash":"zd68ol","at":1753423636450},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{5}","lines":[134,134],"size":387,"outlinks":[{"title":"_**Lottery Strategies, Lotto Systems**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08212773,-0.02985602,-0.03293641,0.00277133,-0.01807142,0.05634116,0.04145588,0.01767525,0.03177023,-0.01318723,0.03704002,0.02631963,0.01798681,0.01277288,-0.02228022,-0.02269673,-0.01216805,-0.04387325,-0.05046793,0.01852381,0.11396384,-0.03859831,-0.06622801,-0.04759856,0.09148344,-0.04623654,-0.04397183,-0.06296862,-0.04495795,-0.19025461,-0.00203099,0.01058244,-0.02405849,-0.06431554,-0.10301469,-0.03317982,-0.03180941,0.07120342,-0.03896463,0.06506619,0.02565439,0.04516031,-0.01762372,-0.01831437,-0.03612109,-0.00827448,-0.02770301,-0.01541659,0.01787419,0.02302696,-0.08468094,0.06664376,-0.00002482,0.02395878,0.05814368,-0.01034801,0.07136521,0.06647819,-0.00213637,0.01465688,-0.02080005,0.00813726,-0.19337793,0.02567754,-0.04257985,0.03157358,0.02198299,-0.00796788,-0.0312979,0.05243317,0.05619574,0.04631526,0.00789913,0.02949104,0.05298478,-0.01248134,-0.02364055,-0.08325855,-0.03167707,0.04082998,-0.03381763,-0.01237355,0.00517563,-0.03005977,0.06431933,0.05144818,0.03614164,0.01442974,0.08350349,-0.03701021,0.04582851,0.04051119,-0.02853806,0.02434856,0.03872056,0.01303078,0.04011326,-0.05041603,0.013657,0.11032359,0.0093457,0.01254272,-0.00933259,0.01254475,0.06332662,-0.03490976,-0.03564325,-0.00879848,-0.00805999,0.05217194,-0.00421205,0.03270229,0.11025071,-0.06878662,-0.05289042,0.02667338,0.00636561,-0.03631221,0.04006688,-0.00069562,-0.0151019,0.0264088,0.01406423,-0.00359169,0.0122815,-0.01076634,0.03416927,0.06873523,0.01648553,0.04092319,0.03962592,-0.0356637,-0.11841743,0.00038264,-0.0567417,-0.04030766,-0.00087605,-0.00893989,0.00771301,0.02487024,-0.02406296,-0.03567035,0.04580754,-0.12793925,-0.01972359,0.07386789,0.01245571,0.00274211,-0.025129,0.02523111,-0.01921794,0.00590812,-0.00988694,-0.06332146,-0.03292756,0.00164596,0.0674108,0.05380101,-0.05064552,-0.02560844,-0.07561143,-0.05888361,-0.02369613,0.13245828,-0.02863364,-0.09504294,-0.05147716,-0.00306265,-0.0349256,-0.08005276,-0.02180099,-0.00446254,-0.01720819,0.03853159,0.08763159,-0.01544793,-0.05333474,-0.0482965,-0.03656992,-0.00224519,0.03214332,-0.02143674,-0.06760632,-0.0084879,-0.00965337,-0.03189024,0.047257,-0.03784628,0.07411415,0.05752249,-0.07350713,-0.00892376,-0.04979215,0.03808296,-0.03542724,-0.03154372,-0.0347666,-0.02950663,0.05961563,0.00194637,-0.02240747,-0.02454529,0.01623551,-0.01362787,-0.03441243,0.0498064,0.0209892,-0.06025452,0.11000081,-0.00993912,-0.0333459,0.00916382,0.04299308,0.03590707,-0.03744353,-0.00785069,0.01664202,0.01857433,-0.04117592,0.03458611,0.03307034,0.0184929,-0.07739002,-0.18454638,-0.05113297,-0.06254131,0.0223084,0.05075927,-0.0026391,-0.00338935,-0.03873044,0.06806441,0.06708963,0.0795556,-0.0541927,0.0031612,0.08015506,0.00209157,0.0103153,-0.06973825,-0.05933928,-0.00927157,0.0202652,0.00412631,-0.00142815,-0.02260172,-0.07647195,0.07721893,-0.00921029,0.15209484,-0.02346565,-0.00352742,0.01181885,0.05744336,0.01247979,-0.03850114,0.00288819,0.00820058,-0.0015202,0.00887395,-0.03026931,-0.06217114,-0.01079618,-0.0198767,0.01182307,0.01394067,-0.06352866,-0.05561486,0.03221399,-0.03094661,0.11012867,-0.02162071,0.00933296,0.06512508,-0.01020363,0.03819752,0.00283532,0.06493095,-0.02260671,-0.07967936,-0.01221737,-0.03467768,0.08038942,-0.01370881,-0.01343543,0.05911303,-0.01287459,0.03947669,-0.02252326,0.00285116,-0.01617085,-0.0258141,0.00340146,-0.04265176,0.06877361,-0.00017987,-0.01423424,0.02367378,0.02604852,0.03798899,-0.05177096,0.00537907,-0.01104725,0.04543298,-0.03358804,0.04533276,0.05969248,0.04980301,0.04358265,0.04260533,0.03551721,-0.01084444,0.00871625,0.02202617,0.01969523,-0.07379017,0.01440932,-0.00048668,0.01162068,-0.2780678,0.04724294,-0.02895315,0.05056111,0.02709942,-0.03590431,-0.03913713,0.02017908,-0.00771443,-0.02940113,0.07664057,0.02644743,0.03593812,-0.04914093,-0.01101599,-0.01191028,-0.0323978,-0.08303975,0.07119277,0.07491797,0.04988293,0.05855155,0.23303472,-0.03056472,0.03807541,-0.00566597,0.00520813,0.03186041,-0.07191207,-0.00241659,0.04022468,0.01605873,0.06911258,-0.00726881,-0.03331158,0.03388179,-0.01179741,0.05058201,-0.03412992,0.02255845,-0.04597472,0.00609691,-0.0073286,0.0266734,0.14728543,-0.00677719,-0.02028728,-0.08561618,0.02999813,0.05271298,-0.10564474,-0.05679603,-0.03307479,-0.02062046,-0.0050638,0.06826274,0.02543772,-0.01116353,0.06069921,-0.01526431,0.00002249,0.05989416,0.04329659,0.04599378,0.02078648],"last_embed":{"hash":"1i91is4","tokens":120}}},"text":null,"length":0,"last_read":{"hash":"1i91is4","at":1753423636497},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{6}","lines":[135,135],"size":265,"outlinks":[{"title":"_**Lottery Strategy, Systems, Software Based on Lotto Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09806553,-0.03685129,-0.02284054,-0.01577815,-0.02771652,0.03329379,0.01188291,0.0407995,0.05011986,-0.0043331,-0.00398277,-0.01159932,0.01505248,0.02082586,-0.01279963,-0.02662199,0.00382333,-0.02762669,-0.07474627,0.04446473,0.08653386,-0.02283003,-0.05773846,-0.10702427,0.07453002,-0.02909675,-0.0268671,-0.05845819,-0.03891448,-0.21434493,0.008775,-0.01837658,-0.03862967,-0.07425071,-0.08100847,-0.04813706,-0.00539082,0.04557778,-0.052551,0.02567762,0.00898163,0.01590039,-0.01068538,-0.04357406,0.0034934,-0.01250014,0.00684823,0.024846,0.04361963,0.01139073,-0.034039,0.05462635,-0.01588984,0.01039485,0.05960221,0.01428646,0.04693923,0.09536045,0.02202332,0.01976621,-0.00272826,0.04110223,-0.18619482,0.05324413,-0.01833505,0.05223259,-0.00379416,0.02988566,-0.03336635,0.09425714,0.05223764,0.02285092,-0.02130698,0.06728768,0.03283656,-0.02082621,-0.02295144,-0.07241134,-0.038643,0.06437246,-0.01930109,-0.01436575,-0.00512389,-0.02611966,0.0400897,0.03205407,0.01040152,0.05790688,0.06606958,-0.07085987,0.01620825,0.02947694,0.0140255,0.02281118,0.03404552,-0.00840123,0.02808312,-0.05314493,0.0072046,0.1009391,0.01838406,-0.01618576,0.00948544,0.01314242,0.07811844,-0.03348467,-0.03910503,-0.01185991,-0.03709754,0.01309771,0.0226746,0.00266645,0.11082339,-0.02316667,-0.04325077,0.02058623,0.01984235,-0.01183062,0.0267273,0.02328178,-0.07271281,0.03646252,-0.00909702,-0.0215972,0.02425651,-0.00711996,0.02280227,0.04935195,0.00766723,0.01103636,0.03060161,-0.02844951,-0.14790602,-0.05638107,-0.06544125,-0.0301013,-0.00889322,0.00646744,0.01342048,0.01448698,-0.03618884,-0.04746967,0.07891884,-0.13112791,0.00099304,0.07674593,0.02296748,-0.01633008,-0.00527614,0.0114282,0.00124979,-0.00112913,0.00723566,-0.04840507,-0.02791228,0.00636014,0.07325909,0.07649826,-0.03970198,-0.04698376,-0.02342124,-0.01185909,-0.037987,0.13822579,-0.02518205,-0.11462618,-0.06824766,-0.00292281,-0.04682584,-0.08459596,-0.02935826,0.01299144,-0.07164659,0.03617921,0.09461955,-0.07493556,-0.08005964,-0.04906432,-0.04572029,0.00621766,0.03435219,-0.01973756,-0.03980234,0.00089447,-0.00335656,-0.06179655,0.04719637,-0.02585847,0.06164688,0.03989189,-0.05196573,0.03737557,-0.0210981,0.05543248,-0.02816534,0.00632431,-0.00723313,-0.00248657,0.05021532,0.00462512,-0.00469813,-0.00748267,-0.02277177,0.00914376,-0.01252214,0.06709723,0.00391572,-0.09630819,0.12746939,0.01727523,-0.04763625,-0.01024689,0.05220867,0.06156785,-0.05008336,-0.02466598,0.04219006,0.04226277,-0.03465067,-0.00012245,0.04603874,0.01115354,-0.08775298,-0.18201073,-0.06094737,-0.06060547,0.00270526,0.06538591,0.0061142,0.0000521,-0.01293771,0.04642661,0.08705764,0.0737187,-0.04349738,0.00262515,0.0270994,-0.01525369,-0.01937129,-0.10792892,-0.05200318,-0.03632858,0.02131637,0.00885372,-0.00396711,-0.01194503,-0.05799377,0.01686944,-0.01822889,0.1578359,0.04414289,-0.01937273,-0.01070962,0.03613087,0.03313067,-0.03521348,-0.02387173,0.01080696,0.02469847,0.0096346,-0.03701603,-0.03500964,-0.00980687,-0.02862042,-0.01028214,-0.00061196,-0.1024051,-0.07420014,0.00033251,-0.0499851,0.04368293,0.02839022,0.05632523,0.03961796,0.01675048,0.03941994,0.00758904,0.07473109,0.00351083,-0.10783776,0.00284765,0.0255362,0.06324103,-0.00590921,-0.01839672,0.05702142,-0.04776609,0.04551588,0.00238253,0.01720812,0.00037974,0.0437293,-0.02109447,-0.01876394,0.06791139,0.00054196,0.00606575,0.02684037,0.05940803,0.05365247,0.00026441,-0.00111294,-0.00819199,0.02162127,-0.06723279,0.04352476,0.04954914,0.01888183,0.03536448,0.02978328,0.06624357,-0.03286498,0.00011309,-0.00301795,0.00642752,-0.05417861,0.00724626,0.00719511,0.00561861,-0.23425154,0.06979739,-0.00401223,0.03790764,0.01751059,-0.02767257,0.00227817,0.00094657,0.00813735,-0.04298739,0.09887085,0.00542687,0.01419877,-0.03761456,-0.01186065,-0.02036561,-0.02983594,-0.05054284,0.09230793,0.06634418,0.06390118,0.06930766,0.2376978,-0.01134249,0.03182496,0.02379392,0.03148029,0.04495327,-0.03604781,-0.01664045,0.00937407,0.00303919,0.00392371,-0.01443097,-0.03012185,0.01764437,0.02004504,0.04587431,-0.04330949,0.00582553,-0.10053605,0.03421935,-0.00926374,0.00610617,0.11120259,0.01413963,-0.00339088,-0.05936107,0.06572046,0.04742345,-0.09993154,-0.04235786,-0.01986091,-0.05632933,0.01579032,0.04296358,0.04180031,-0.04895427,0.00567331,0.03111146,-0.0022917,0.05760932,0.03514753,0.03347123,-0.00491781],"last_embed":{"hash":"1kok8hy","tokens":151}}},"text":null,"length":0,"last_read":{"hash":"1kok8hy","at":1753423636536},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{7}","lines":[136,137],"size":435,"outlinks":[{"title":"_**Euromillions Strategies, Systems, Software, Winning Numbers**_","target":"https://saliu.com/euro_millions.html#software","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04376786,-0.05445734,0.00668247,-0.02184607,-0.05547136,0.07336646,0.07837114,0.01845348,0.06129017,-0.01720558,0.01521405,0.04862054,0.00420348,0.04417881,-0.02205879,-0.03589675,-0.00983797,-0.05432543,-0.06342864,0.02932755,0.05773234,-0.03002234,-0.06158831,-0.0745656,0.07566828,-0.0421437,-0.04174333,-0.07997826,-0.04522882,-0.18648402,0.00862523,-0.00336423,-0.05059203,-0.05469451,-0.1220218,-0.05428751,-0.00884125,0.1026303,0.00767291,0.05057258,0.02840188,0.01668368,-0.0360838,-0.05410943,-0.02278668,-0.00788285,0.0008094,-0.00635658,0.07453928,0.01755296,-0.03185392,0.0459644,-0.00039773,-0.00085394,0.04358521,-0.00981385,0.02877071,0.09333589,0.02768072,0.0331096,-0.01820976,0.03045845,-0.15111271,0.05112254,-0.03107647,0.01068857,0.01238928,0.01842764,-0.03860964,0.09352764,0.05214824,0.0056023,-0.01871765,0.04641232,0.04701176,-0.04438817,-0.01738986,-0.05502049,-0.04599953,0.05845618,-0.05369342,-0.04382622,-0.00763502,-0.03246406,0.04217153,0.01960808,-0.00362282,0.02858568,0.05501421,-0.04131319,0.06216799,0.04035482,-0.0157226,0.01620702,0.0277019,0.04156436,0.04971015,-0.04819082,-0.00070348,0.11693944,0.00004611,0.02192122,-0.01193132,0.04931056,0.05880244,-0.0490398,-0.02017748,-0.01162599,-0.01123716,0.03089552,0.02677096,-0.02497506,0.10650437,-0.01256285,-0.0743116,0.01548259,0.03280847,-0.01458879,0.02268757,0.02610128,-0.06605546,0.0225924,-0.02106341,-0.02522132,-0.00009208,-0.02132064,0.03111112,0.06270751,0.00608935,0.02523743,0.06025015,-0.05511397,-0.12287639,-0.04674992,-0.0461918,-0.03158112,-0.00276301,0.03455408,0.00172251,0.00889722,0.00755399,-0.01342331,0.08047824,-0.13037066,-0.0320606,0.0535243,0.00287097,0.03069989,0.00147525,-0.01123503,-0.03589014,-0.01416047,-0.05208825,-0.04457875,-0.02745297,0.00906074,0.04685894,0.06983717,-0.04609149,-0.01291932,-0.05204279,-0.05632589,-0.02094027,0.07707477,-0.04204848,-0.09066249,-0.05642731,-0.04594484,-0.04711412,-0.10322461,-0.04085605,0.00226977,-0.05421064,0.04367645,0.0806345,-0.03654779,-0.08898884,-0.09741411,-0.02878116,0.00239802,0.05142027,-0.00239172,-0.03682929,-0.00189916,0.008744,-0.05068891,0.0523081,-0.02683882,0.06120851,0.0536961,-0.04395854,0.00065742,-0.04073747,0.04306644,-0.02177108,-0.02701265,-0.03365925,-0.03158425,0.05813476,-0.00384078,-0.04899042,-0.02638195,0.00824689,0.0090624,-0.02355453,0.05747814,0.02110821,-0.1025863,0.11204379,-0.00117116,-0.05767839,-0.0173604,0.01172938,0.08468422,-0.0225731,-0.01605892,0.03582548,0.02965674,0.01341301,-0.03588492,0.04097755,0.02952343,-0.05299775,-0.17073268,-0.05704109,-0.05580776,0.02062688,0.06203258,-0.00301468,-0.01008587,-0.02072013,0.03601888,0.1224411,0.0683258,-0.05803899,0.01542692,0.06279152,-0.00705201,0.0270081,-0.11101286,-0.03316432,-0.00894691,0.02143854,-0.00217313,0.00570542,-0.02206818,-0.07745964,0.03289219,-0.01117239,0.14576818,0.00755803,0.00014386,0.01704838,0.03973484,-0.01169371,-0.02445895,-0.03037701,-0.00753492,0.01155545,-0.01751185,-0.03046944,-0.09048411,-0.03287436,-0.00036955,0.01433201,-0.01099748,-0.06469095,-0.08797009,0.01206249,-0.0284682,0.09568612,-0.0043108,0.05051211,0.04075573,-0.03007239,0.03411404,0.01393239,0.06552643,-0.02022719,-0.08048347,0.02587351,0.00110845,0.09239438,-0.02187433,-0.02710683,0.04214447,-0.03879733,0.03104735,0.02260819,0.02814655,-0.0234838,0.03258075,0.04099986,-0.04738294,0.05526954,-0.00666992,-0.02088864,0.01860362,0.0619299,0.08435576,0.021193,0.00616501,-0.01371257,0.04839075,-0.04890215,0.0395854,0.04598903,0.02308789,0.03409673,0.03535004,0.02373218,-0.0491493,-0.01886581,0.00937729,-0.01011912,-0.05070151,-0.01336426,-0.03950612,-0.02228646,-0.23850918,0.04929485,-0.0169915,0.0577646,-0.01878331,-0.02345018,-0.00900288,0.04125534,0.00892659,-0.037551,0.11505555,0.02959356,0.00882737,-0.04364138,-0.02366762,-0.01621843,0.00954841,-0.0449593,0.08213412,0.04023818,0.08762327,0.07116186,0.22957669,-0.02769375,0.02512979,-0.01925885,0.03598271,0.0388412,-0.08834517,-0.01769063,-0.00208405,0.01918859,0.05154917,-0.00335649,-0.02278153,0.04670844,0.01189037,0.05963786,-0.01122529,-0.00122114,-0.06287239,0.00526074,-0.00686349,0.03154979,0.12789625,0.02584562,-0.00959407,-0.02403464,0.03837744,0.03992761,-0.0944258,-0.05304086,-0.03742422,-0.0297325,-0.01351268,0.04088145,0.02838309,0.00363713,0.040219,0.04504409,0.03433647,0.07119753,0.04333993,0.0120502,0.04819308],"last_embed":{"hash":"1iyc64m","tokens":117}}},"text":null,"length":0,"last_read":{"hash":"1iyc64m","at":1753423636588},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{9}","lines":[140,140],"size":357,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05693312,-0.05293108,0.01430974,-0.01363272,-0.03898498,0.06786795,0.03455013,0.03564789,0.05310071,0.00576455,0.01359582,-0.02662581,0.03083664,0.02703137,-0.03528696,-0.01556045,-0.03026514,-0.0055729,-0.07132625,0.01615259,0.09980214,-0.02483343,-0.07914437,-0.09759015,0.08134616,-0.00393737,-0.04402458,-0.06281569,-0.05011563,-0.22623266,-0.02184567,-0.01459881,-0.054694,-0.08852992,-0.09888359,-0.04699993,-0.02545147,0.08578637,0.00620442,0.04483533,0.02512508,-0.00440545,-0.02287673,-0.04794938,-0.02386509,-0.05334504,0.02014841,-0.01036706,0.01113532,0.00219934,-0.06868164,0.08061323,-0.00115576,0.02417216,0.04942524,-0.00720183,0.04639033,0.09913064,0.0305482,0.06471191,-0.04263146,0.06428082,-0.17072669,0.04698649,-0.03318775,-0.00638847,-0.02811763,0.00076149,-0.0090172,0.09881539,0.03365558,0.03883029,-0.0029343,0.09085324,0.06023058,-0.04140593,0.00009769,-0.07589214,-0.04949879,0.08423889,-0.0195972,-0.03129955,-0.01394369,-0.00971947,0.04481215,-0.0025762,0.01235899,0.00458824,0.0622514,-0.04334847,0.05764391,0.05971687,-0.05345461,0.02189846,0.04029382,0.05237028,0.03924828,-0.04861453,0.03216181,0.11550517,0.02196603,-0.00555619,-0.01928207,0.04416793,0.06861646,-0.04967408,-0.04168793,-0.00396084,-0.01380243,-0.00648397,0.0252465,-0.00441311,0.07533916,-0.01016672,-0.05468965,0.04562424,0.030674,-0.02847512,0.00365366,-0.00725484,-0.04330684,0.00999916,0.01618315,-0.02871909,0.0167427,0.0103897,-0.00749566,0.06358315,0.0046176,0.02971314,0.06967174,-0.05775362,-0.09405568,-0.03828392,-0.02674401,-0.03187431,0.01506509,0.01896048,-0.02377695,0.0213977,-0.01347358,-0.02528433,0.05222235,-0.10521623,-0.04831969,0.08124291,-0.01457024,0.00895388,0.00349243,-0.03600386,-0.02447692,-0.00452701,-0.0259319,-0.04355399,-0.00294445,-0.01146989,0.02500545,0.06980821,-0.03176507,-0.01253638,-0.06335404,-0.05503407,-0.01161367,0.13809495,-0.04290454,-0.02187662,-0.0608947,-0.02084925,-0.0298274,-0.09246924,-0.0276205,-0.00695466,-0.04608067,0.04057206,0.08875315,-0.03555159,-0.07446872,-0.07026453,-0.0099288,-0.02248412,0.03872961,-0.01760566,-0.02540259,-0.02376523,-0.01861716,-0.03762595,0.05803412,-0.04311389,0.0462217,0.07127123,-0.03448928,-0.01005743,-0.0534195,0.01862512,-0.01057212,-0.02921629,-0.02914358,-0.02312888,0.02266751,-0.03172312,-0.04826413,-0.03713334,-0.02731491,0.00235736,-0.02063097,0.05770707,0.03892956,-0.06532399,0.09877447,-0.02147368,-0.02436432,-0.02150447,0.04264109,0.09148833,-0.02579134,0.01257062,0.01566745,0.02339668,-0.03740445,-0.03242016,0.02883164,0.02701276,-0.0441782,-0.18299279,-0.04625039,-0.06440886,0.01281537,0.0628158,0.00309103,0.00970747,-0.04333637,0.06586666,0.09213244,0.05951074,-0.04643727,-0.0056168,0.06070019,0.01737379,0.02595477,-0.11269002,-0.02584927,-0.04884581,0.02812611,0.00508619,0.01363139,-0.0395188,-0.05037516,0.0367964,-0.01033106,0.13271652,0.00785892,0.02373067,-0.00112629,0.03645357,0.00449362,-0.0049522,0.01080417,0.02145394,0.03179993,-0.00956447,-0.03323819,-0.05896001,-0.04406063,-0.00171235,0.03938504,-0.00974243,-0.06993109,-0.08045332,0.0396306,-0.04624056,0.10810729,-0.00623297,0.04947068,0.06260502,-0.02404003,0.04950436,-0.01538811,0.05488946,-0.02377474,-0.0740955,-0.00429879,-0.00373217,0.0549204,-0.00142981,-0.03722132,0.03907758,-0.04666827,0.01702901,-0.01289328,0.0323983,-0.02988288,0.02211335,0.02567744,-0.03750481,0.07156211,-0.00249032,0.02826086,-0.00015978,0.05703873,0.07461663,-0.01970739,-0.00235373,-0.00844392,0.05348126,-0.02481761,0.03072367,0.04977155,0.04346016,-0.00463966,0.05511162,0.01733156,-0.0252444,0.00955283,-0.00159487,-0.01105476,-0.05884724,0.02378541,-0.019011,-0.02843941,-0.26363865,0.04106811,-0.00142039,0.04318865,0.01322308,-0.06336566,0.00937745,0.0048495,-0.01912784,-0.03972923,0.08881747,0.03954699,0.03814805,-0.03890864,-0.02898992,-0.045974,0.01848494,-0.03960447,0.07321914,0.02407838,0.08466251,0.06865051,0.23842418,-0.01387084,0.04202826,-0.01078842,0.04286924,0.00952002,-0.05604023,-0.00543212,0.00593173,0.0286646,0.08374678,-0.0156998,-0.02971824,0.04937532,-0.00064086,0.05014498,-0.01582816,-0.03646157,-0.01777857,0.01279963,-0.00779906,0.04253706,0.11959784,-0.01121671,0.00546966,-0.03749494,0.05969176,0.04389249,-0.10048769,-0.03780683,-0.03926278,-0.03095379,0.00919753,0.01091123,0.03330599,0.00691516,0.01100579,0.01480815,0.01473435,0.05920031,0.07665389,0.01840078,0.03234612],"last_embed":{"hash":"1f95noq","tokens":172}}},"text":null,"length":0,"last_read":{"hash":"1f95noq","at":1753423636631},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{10}","lines":[141,141],"size":467,"outlinks":[{"title":"_**Formulas, Software to Calculate Lottery, Lotto <u>Odds, Hypergeometric Distribution Probability</u>**_","target":"https://saliu.com/oddslotto.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06234933,-0.03838534,0.00054232,-0.00963514,-0.03481613,0.07071816,0.07098089,0.02422887,0.06392137,-0.00527008,-0.00174122,0.01573704,0.02424232,0.03249476,-0.02044071,-0.0237823,-0.01855765,-0.05404463,-0.07655932,0.01752734,0.05637474,-0.0199955,-0.06826299,-0.0815201,0.06532322,-0.04190378,-0.02340859,-0.07122386,-0.04836842,-0.21841758,-0.00387454,-0.01114141,-0.0476685,-0.06761721,-0.10228817,-0.05207109,-0.03399695,0.10539756,-0.00144767,0.03344578,0.02891452,0.00030337,-0.02128536,-0.04045515,-0.02673431,-0.01264096,-0.00543702,0.0000405,0.05882574,0.01075705,-0.06368422,0.06504371,0.00023584,-0.00682298,0.06022972,0.00391445,0.04471814,0.11042269,-0.01234662,0.0414483,-0.01557865,0.05904847,-0.16809292,0.02261739,-0.0315625,0.03688251,-0.00769475,0.02222037,-0.0378068,0.12073852,0.05778681,0.01859638,-0.00791532,0.03123052,0.04410394,-0.0361086,-0.03813934,-0.05559582,-0.04629783,0.08249046,-0.05011503,-0.02504748,-0.00965707,-0.01737924,0.04190394,0.03260498,0.00283412,0.02443579,0.06516172,-0.05808421,0.05797587,0.05685902,-0.03545975,0.005562,0.05557201,0.02489945,0.04319523,-0.05699048,0.01346309,0.12786651,0.00705359,0.01487154,0.01395489,0.0283863,0.03636262,-0.06118658,-0.03628009,-0.02811254,-0.00632404,0.0062434,0.02244653,-0.01712041,0.11883498,-0.02992124,-0.06994417,0.05220373,0.02294486,-0.00008062,0.02708286,0.02037536,-0.04136309,0.02226334,0.00798657,-0.03389747,0.00710157,-0.01718253,0.01883515,0.0628482,0.01240413,0.01869754,0.05495293,-0.03120028,-0.12247036,-0.04211046,-0.04219214,-0.05852537,0.00083356,0.03215235,0.01171042,0.00175871,-0.01107063,-0.01492539,0.0760565,-0.14123866,-0.01530709,0.0833358,0.01436603,0.02613277,0.00176097,0.00606035,-0.00641569,-0.0144476,-0.03216415,-0.06343978,-0.02280954,-0.00530951,0.05665692,0.06464866,-0.03680714,-0.01263193,-0.05954077,-0.04371147,-0.01594473,0.11753177,-0.04207411,-0.06878861,-0.05770464,-0.01866594,-0.05250374,-0.0936007,-0.03720945,0.01318581,-0.0516581,0.02492152,0.06520714,-0.03344561,-0.09621265,-0.0699899,-0.03194907,0.0125208,0.04135001,-0.02901267,-0.04905194,0.01116695,-0.00978438,-0.0365083,0.04721197,-0.02939907,0.0569158,0.05942446,-0.0816939,0.00141516,-0.04827359,0.03388453,-0.02810756,-0.01236791,-0.01453106,-0.03939849,0.04771098,-0.00348396,-0.01770567,-0.030096,0.0002035,0.02494614,-0.02571647,0.05199851,0.02273804,-0.09413682,0.11036057,-0.0039898,-0.02892678,-0.01302163,0.0015704,0.06460324,0.00016406,-0.0049173,0.01866448,0.0406215,-0.0047365,-0.02133677,0.03835509,-0.00069583,-0.0554036,-0.17044497,-0.06501284,-0.06739839,0.01134385,0.02760808,0.00447279,-0.01882757,-0.01454253,0.04728538,0.09141899,0.06610791,-0.05385898,-0.00705729,0.0769036,-0.00523997,-0.00191763,-0.11632113,-0.02160928,-0.01531626,0.03719248,0.00899924,-0.00078647,0.00537501,-0.06187186,0.03000745,-0.01858259,0.15318468,0.02746601,-0.00106298,0.01019036,0.03844714,0.03419331,-0.02537753,0.00757034,0.01901318,0.023862,-0.01370215,-0.06113621,-0.0695877,-0.02448221,0.00490794,0.0142875,-0.01513237,-0.07578432,-0.08109792,0.00068042,-0.05569837,0.10546955,0.00853742,0.04927589,0.0590191,-0.02291389,0.03855428,-0.01235808,0.06486116,-0.00681568,-0.10101885,0.01488305,-0.02123963,0.07347055,-0.01269052,-0.04826481,0.06088252,-0.02868007,0.0448887,0.01089924,0.02468409,-0.00598635,0.0154168,0.02049649,-0.02952323,0.02165851,0.01404802,-0.0021448,0.0125692,0.03778226,0.07631296,-0.02468483,-0.01018392,-0.01185853,0.04560097,-0.03363267,0.02932053,0.04952233,0.01628034,0.0138982,0.05374768,0.02274135,-0.00420655,-0.02646712,-0.00175063,0.01661029,-0.02873915,0.01144698,-0.01883344,-0.03224733,-0.23840508,0.04056745,-0.01530446,0.0355338,0.0002,-0.02118234,-0.00855641,0.03497219,0.00556294,-0.03224322,0.08019491,0.02459882,0.01853123,-0.04023911,-0.02234293,-0.02150412,0.00868285,-0.06256505,0.0811964,0.04607689,0.08897388,0.07047126,0.23618801,-0.02802561,0.02464285,-0.0054922,0.02172135,0.03530823,-0.09211168,-0.00185654,0.02971029,0.02078322,0.06417971,-0.01579935,-0.01729089,0.05121394,0.02257655,0.05135142,-0.02897022,0.00404757,-0.07507079,-0.00036095,-0.02136856,0.01388807,0.12948519,0.0042768,-0.01225082,-0.04226464,0.03482789,0.04498681,-0.09194009,-0.03362872,-0.01176957,-0.05305642,-0.00216259,0.04429039,0.03783754,-0.00571003,0.04884905,0.01730655,0.02094176,0.04679327,0.06062324,0.0209476,0.02860994],"last_embed":{"hash":"aiqade","tokens":141}}},"text":null,"length":0,"last_read":{"hash":"aiqade","at":1753423636686},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{11}","lines":[142,143],"size":491,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07054917,-0.05554838,-0.00993744,-0.01342327,-0.01038318,0.06138224,0.07157982,0.05612879,0.02897714,-0.01762058,0.0211433,0.01873932,0.01162288,0.03914108,-0.01901419,-0.03519056,-0.01099889,-0.02914578,-0.05241652,0.02113776,0.09347844,-0.02023692,-0.06549036,-0.06689511,0.06034408,-0.04290405,-0.02499653,-0.04579153,-0.04520269,-0.18601516,-0.02126562,0.00542628,-0.04856369,-0.07392493,-0.10619643,-0.05099342,-0.01534705,0.07424709,-0.01913367,0.06589441,0.0335285,-0.01293001,-0.01599689,-0.02218109,-0.00965321,0.00679144,0.02695337,-0.01648376,0.0167677,-0.00018262,-0.06364778,0.04503512,-0.02575763,0.00365279,0.05315175,0.01575756,0.0647259,0.0950532,0.02630494,0.03650209,-0.00493738,0.04923099,-0.17109282,0.05769515,-0.02044256,0.02917513,0.01138069,0.00920959,-0.04460236,0.07971578,0.06878698,0.01762366,-0.02775338,0.0784183,0.06817059,-0.03547578,-0.02131207,-0.05940992,-0.02870332,0.07416054,-0.02588544,-0.01977443,0.01255006,-0.01341779,0.03797318,0.05174001,0.0169995,0.03021712,0.05598304,-0.03812518,0.04322694,0.02599804,0.00276204,0.03346381,0.04933484,0.01846807,0.01874964,-0.04298586,0.00122545,0.12014507,-0.01069854,-0.00560459,0.01515877,0.01413251,0.06325951,-0.04706997,-0.0276464,-0.03067977,0.00815819,0.01986905,0.00463392,0.01325645,0.10000864,-0.01395673,-0.04453556,0.02319865,0.02383896,-0.00640485,0.01674852,-0.00266768,-0.08055756,0.02743753,-0.0036487,-0.02201455,0.00835318,-0.03097074,0.00324158,0.04693401,0.01139016,0.01445112,0.03951655,-0.03895561,-0.12572472,-0.04602679,-0.04959908,-0.04088954,0.01735102,-0.00535571,0.01741413,0.03187491,-0.01426944,-0.02954947,0.03152047,-0.12737723,-0.0439963,0.10135144,0.00537443,-0.00103066,-0.0244983,0.0187117,-0.02093199,0.01028328,-0.02544888,-0.05043704,-0.01940485,0.00753568,0.0487612,0.05454968,-0.03536501,-0.02687938,-0.04349875,-0.03138467,-0.03348699,0.11806097,-0.04134946,-0.11366697,-0.075951,-0.02208242,-0.0361388,-0.08384542,-0.02323662,0.00261596,-0.04904411,0.06032683,0.06917515,-0.03605166,-0.06804878,-0.04665022,-0.06994481,0.00946976,0.05188705,-0.03740695,-0.07065976,-0.00364803,-0.01212008,-0.03953118,0.04568376,-0.02554636,0.08109234,0.06690224,-0.07365073,0.02354053,-0.03733735,0.01670067,-0.01939092,-0.02318985,-0.00820257,-0.02263051,0.03420984,-0.02308289,-0.05121366,-0.01349653,-0.01970507,0.04683384,-0.05160493,0.06410244,0.00857939,-0.06878638,0.10710499,0.00742558,-0.04131059,0.02185383,0.03536843,0.06069894,-0.0465802,-0.02251622,0.03376431,0.0323263,-0.01964167,-0.01073286,0.05529069,-0.00586668,-0.08093226,-0.18704854,-0.06328411,-0.0648429,-0.00628883,0.04262277,0.00124403,-0.01236281,-0.03319445,0.04451617,0.09556264,0.07549762,-0.04074229,0.00441823,0.06146331,-0.01883158,-0.00801474,-0.08533558,-0.07077528,-0.03368162,0.01553416,-0.00573478,-0.01355675,0.00748091,-0.0589428,0.00962669,-0.01133545,0.14454694,0.02909181,0.01683386,0.03257082,0.02935141,0.01993703,-0.01525596,-0.03519696,0.00181401,-0.00040256,-0.00687169,-0.03727296,-0.07063708,-0.03536606,-0.02074218,-0.00057552,-0.01105132,-0.06480809,-0.08982088,0.02084606,-0.0319144,0.08819475,-0.01214381,0.03860424,0.04520467,-0.0258665,0.04076814,0.00341121,0.06146398,0.00543377,-0.09492577,-0.01285541,-0.01066291,0.07642586,-0.01473732,-0.00774517,0.05970083,-0.03800973,0.02501256,-0.00846812,0.01101639,0.01782187,0.01310061,0.01572596,-0.04955322,0.08262152,0.00649107,-0.00882576,0.01533423,0.04850468,0.06964472,-0.00300945,0.00185422,-0.02249697,0.05099196,-0.04944305,0.06441958,0.0419245,0.01544585,0.02723673,0.03226078,-0.0101851,-0.01796456,0.00606373,0.011056,0.00733894,-0.04395198,-0.0139995,0.00364627,-0.02001207,-0.2500903,0.03159012,-0.02779665,0.03800055,0.04552969,-0.02334057,-0.0146136,0.02112545,0.0282057,-0.022464,0.09077957,-0.00257808,0.00349854,-0.03169637,-0.03038715,-0.01214532,-0.00209288,-0.05879774,0.06399576,0.06819323,0.07299785,0.0572605,0.26682079,-0.00725765,0.01024609,0.00557028,0.01522607,0.03934679,-0.12723646,-0.01437,0.05916052,0.00559808,0.03441886,-0.00762033,-0.03126236,0.03483875,0.03876868,0.07488745,-0.00331607,0.00109772,-0.05577971,0.02033201,0.00344888,0.02426135,0.14518288,0.0145616,-0.02975124,-0.05022943,0.04896648,0.05159583,-0.08566668,-0.04913402,-0.04432608,-0.00463406,-0.00965288,0.03794146,0.04952814,-0.04196784,0.03908592,0.00386962,0.03261159,0.08291283,0.0402956,0.03563327,0.02886317],"last_embed":{"hash":"13qqcwk","tokens":90}}},"text":null,"length":0,"last_read":{"hash":"13qqcwk","at":1753423636740},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{13}","lines":[146,146],"size":239,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{24}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07531455,-0.06046473,-0.01652099,-0.00116591,-0.03823266,0.06430922,0.05479943,0.03002231,0.03353192,-0.01731167,0.02463129,-0.00371034,0.01222359,0.03342536,-0.02350291,-0.02612084,-0.00049448,0.00880947,-0.06669031,0.00909196,0.11759906,-0.05020633,-0.08402443,-0.06401019,0.06275129,-0.01470042,-0.04237237,-0.05064294,-0.02929433,-0.18194024,-0.03486339,0.00716049,-0.03946061,-0.07941689,-0.1099781,-0.04657598,-0.03171535,0.10422994,-0.02026257,0.05363945,0.02339053,-0.00583425,-0.02766978,-0.01516569,-0.01188836,-0.01806539,0.0205716,-0.00188341,0.03414527,-0.00595256,-0.04682739,0.0370502,-0.01768028,0.01133769,0.05920224,0.02045226,0.07259008,0.10185149,0.01773754,0.04169877,-0.02489445,0.05821364,-0.17671019,0.05339569,-0.01755612,0.02912929,0.00841284,-0.01784321,-0.03940763,0.08603111,0.05335182,0.02314654,-0.00633057,0.06788513,0.04756638,-0.01527113,-0.02515937,-0.08604003,-0.02267185,0.07070697,-0.04625097,-0.02822555,-0.00343597,-0.01361012,0.04421476,0.04779178,0.0034004,0.02123187,0.07139567,-0.04370303,0.03982419,0.03878302,0.00083371,0.03187767,0.05030964,0.01213924,0.01566689,-0.05252181,0.03186974,0.12729464,0.00253915,0.01686881,0.00587487,0.0213982,0.05843116,-0.06065433,-0.04160464,-0.02238828,0.00422506,0.03562737,0.01856963,-0.01294344,0.09668784,-0.01766282,-0.05078088,0.01390257,0.02586402,-0.03031168,0.00969242,0.00367954,-0.06092322,0.02473411,0.02529929,-0.02768981,0.01730252,-0.01174608,-0.00907159,0.05850203,-0.00928485,0.04312456,0.05398334,-0.02924035,-0.10999321,-0.03985962,-0.04165036,-0.02535264,0.03555185,-0.00694245,-0.01224308,0.02937186,-0.01126529,-0.03427327,0.06691,-0.11423519,-0.03450447,0.09667172,-0.03060202,0.01680259,0.00597278,0.01406467,-0.02613628,-0.00626327,-0.0242656,-0.03652949,-0.01806239,-0.00819057,0.04611182,0.05671836,-0.03420623,-0.01281889,-0.06049955,-0.03980622,-0.04245836,0.12367719,-0.05125535,-0.08427157,-0.06957097,-0.01771666,-0.03093534,-0.09037738,-0.02567409,0.00161168,-0.05335066,0.07251438,0.08814422,-0.03271765,-0.07026178,-0.04774394,-0.06085814,0.008164,0.01565746,-0.05193983,-0.05617755,-0.00825742,-0.00954811,-0.02861315,0.04443379,-0.03216468,0.07966989,0.06376608,-0.05730718,0.02643612,-0.05410073,0.01449946,-0.03240966,-0.01828632,-0.03873178,-0.01933936,0.04944579,-0.02113208,-0.03760705,-0.00931211,-0.01487274,0.02139477,-0.04675499,0.0505025,0.00482636,-0.06288802,0.08349093,0.01315449,-0.04003115,0.00810575,0.02854839,0.0627916,-0.02083352,-0.01104166,0.04450922,0.03441599,-0.03487816,-0.01883098,0.03878164,0.00467614,-0.07675075,-0.17341094,-0.052925,-0.05046897,0.01634297,0.04080649,-0.01837441,-0.00392214,-0.0294077,0.0366796,0.11963397,0.0696645,-0.04490771,0.00713759,0.05390502,-0.00125667,0.00823403,-0.08854035,-0.06125637,-0.02867707,0.0090795,0.00890306,0.00178906,-0.00924896,-0.08449697,0.0098965,0.00231013,0.16091415,0.0184536,-0.00456554,0.05617222,0.04599301,0.00499944,-0.03616311,0.02465059,0.02312727,-0.0073362,-0.01676929,-0.04839689,-0.07006807,-0.03751625,-0.01473157,0.02023169,0.00647022,-0.06895891,-0.0499451,0.00100931,-0.03746625,0.09138899,0.00926894,0.02552863,0.03369423,-0.00929935,0.0323348,-0.01311401,0.05150041,0.00606527,-0.07239321,-0.02140065,-0.03361236,0.07064263,-0.03624382,-0.01788443,0.02905278,-0.03553044,0.03324691,0.00110626,0.02990397,-0.00651897,0.0095894,0.02420645,-0.03673951,0.05619172,0.0116224,0.01682219,0.00444078,0.04146517,0.0742486,-0.01617349,0.00777945,-0.02855607,0.04593348,-0.05263247,0.0586867,0.05425622,0.02960447,0.01492861,0.0394261,0.02626234,-0.01615755,-0.00662411,0.01732263,-0.00453481,-0.04744535,-0.00213894,-0.00545804,-0.01051115,-0.26891905,0.0367723,-0.04121392,0.04223444,0.01387831,-0.04097861,-0.00134873,0.01790298,0.00873022,-0.02978118,0.0880701,0.00742462,0.0267321,-0.03061156,-0.04051732,-0.03729123,-0.0006194,-0.05752726,0.09740999,0.07121867,0.05696284,0.05955663,0.25452974,-0.00856112,0.03186525,-0.01322105,0.01336393,0.03266379,-0.10689829,-0.00130274,0.05378461,0.03356131,0.04522444,-0.02403313,-0.03704922,0.06116533,0.01937149,0.06159761,-0.00972805,0.00762148,-0.06598698,-0.00272706,-0.02245313,0.04639415,0.1266686,0.0034902,-0.01194372,-0.04964836,0.06031009,0.04651984,-0.08959521,-0.03698907,-0.06762106,-0.02775232,0.00691197,0.03284989,0.03866022,-0.0220849,0.02857486,0.0007562,0.0260747,0.06629065,0.06573865,0.03703597,0.01350579],"last_embed":{"hash":"7kdphw","tokens":103}}},"text":null,"length":0,"last_read":{"hash":"7kdphw","at":1753423636774},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{24}","lines":[157,157],"size":227,"outlinks":[{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{28}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07052987,-0.057911,-0.01676842,0.00486409,-0.0400765,0.05452924,0.06151918,0.03047507,0.03634636,-0.01004823,0.02802989,0.00659949,0.02714767,0.02750343,-0.01566624,-0.02074149,-0.02188089,0.0294818,-0.06516317,0.00892945,0.11295731,-0.03534018,-0.05478958,-0.06668474,0.06032655,-0.04778906,-0.01966228,-0.07923479,-0.04923724,-0.17468689,-0.01254073,-0.02062614,-0.04100614,-0.08865541,-0.08718297,-0.06798121,-0.02617055,0.06372172,-0.03839841,0.03949474,0.01665194,0.01767435,-0.02990037,-0.02440163,-0.00756464,0.00596107,0.03076775,0.0036431,0.0542243,0.00038387,-0.03976306,0.07928824,-0.02550475,0.03817263,0.06518097,0.01089367,0.06189567,0.10142136,-0.0224613,0.03332512,-0.00362649,0.03627509,-0.18320453,0.05108334,-0.03199,0.0277742,0.01789198,0.02616059,-0.03708976,0.07818332,0.03982416,0.00117535,-0.01191987,0.06296092,0.0417738,-0.02842289,-0.01473088,-0.06065862,-0.01847369,0.06834486,-0.04109291,-0.05733098,-0.00838396,-0.01220295,0.04424762,0.01214864,0.04145476,0.02585364,0.07387006,-0.07186883,0.03405021,0.05632882,-0.00595427,0.01480121,0.06367803,0.01400982,0.03716584,-0.04146612,0.00845993,0.13245678,-0.01189764,-0.01018701,-0.03141679,0.00826108,0.07754013,-0.06690478,-0.02954751,-0.00727985,-0.00796497,0.02579507,0.01188533,-0.00063525,0.09350744,-0.0370485,-0.04163808,0.00195243,0.02792392,-0.00336326,0.03345855,-0.00960223,-0.04468561,0.03642311,0.03822905,-0.03108774,0.02725375,-0.01289889,0.01293865,0.03540462,0.00254714,0.05797267,0.04296342,-0.04665166,-0.11758268,-0.03820075,-0.02474244,-0.02479243,0.00992225,-0.01101872,-0.03072259,0.00313609,-0.00256706,0.00842501,0.06593241,-0.11743993,0.00127881,0.064415,-0.00305553,0.01852481,0.00903858,0.00562016,-0.02348875,-0.02205689,-0.04140199,-0.05735657,-0.0181796,-0.0039768,0.06013068,0.04952281,-0.03852876,-0.01943727,-0.02705588,-0.0243905,-0.02831834,0.12768131,-0.05927535,-0.06512947,-0.07692349,-0.00106255,-0.03240068,-0.08132885,-0.01742101,-0.012681,-0.05145686,0.02329066,0.05465493,-0.03220322,-0.09619456,-0.08674814,-0.06662604,0.0233193,0.02839992,-0.01957808,-0.02230166,-0.02338726,-0.01496139,-0.03223399,0.03974963,-0.02288128,0.07793572,0.03677556,-0.07845378,0.01828122,-0.04499159,0.02718779,-0.00848984,0.00441308,-0.03153811,-0.01450303,0.05226366,-0.00545946,-0.02874316,-0.03522659,-0.00156595,0.03787103,-0.0436228,0.02854253,-0.00310323,-0.09591025,0.11287045,0.01885219,-0.03739766,0.01383252,0.04482737,0.05528061,-0.00781056,0.00686719,0.03777758,0.04304128,-0.00905599,0.01755873,0.02679805,0.01925118,-0.07485363,-0.18241219,-0.062971,-0.07660221,-0.02442444,0.04149516,-0.02014342,-0.02218486,-0.04856033,0.02006144,0.09946684,0.07857302,-0.06486003,-0.00224613,0.0774063,-0.00899586,0.01615403,-0.08521078,-0.06392097,-0.05617836,0.04609248,0.01159989,0.00113034,-0.00870837,-0.08648369,0.00705757,-0.00947834,0.15910216,-0.02011505,0.03510427,0.03047761,0.03999485,0.04095403,-0.03740096,-0.00001505,0.01800946,0.01969593,-0.02418366,-0.02451213,-0.07149429,-0.01844354,-0.01388245,0.03496427,0.02597939,-0.08963751,-0.06721975,0.00040337,-0.0526924,0.08415669,0.00844403,0.04613701,0.04851891,0.00161397,0.07217848,-0.00189695,0.05707938,-0.01523371,-0.07186329,-0.00579641,-0.03974986,0.03583993,-0.02977972,-0.06113162,0.05331792,-0.04527282,0.06456292,-0.01167019,0.01159854,-0.0043888,0.00517669,0.01655935,-0.01988766,0.04302273,-0.00652913,0.00053702,0.00614084,0.04142356,0.08974408,-0.02482966,-0.04000003,-0.03213198,0.04490579,-0.05671622,0.07509258,0.05406893,0.06304324,0.01295849,0.03683513,0.03609099,-0.00818111,-0.00133166,0.01991545,-0.00684014,-0.05848607,-0.01786192,-0.01874854,-0.02850169,-0.23165116,0.0286116,-0.04172816,0.03278715,-0.00913727,-0.04408412,-0.01029511,0.01967335,-0.00314139,-0.03192429,0.08495525,0.01869527,0.00912284,-0.02457612,-0.01941962,-0.02051179,0.00442411,-0.03398352,0.10253994,0.11944403,0.0772675,0.05864752,0.24949338,-0.00873015,0.01649576,-0.02294674,0.02608266,0.01991904,-0.03406397,0.02881348,0.06145304,0.01667904,0.05682915,-0.01934615,-0.01493503,0.03582066,0.0051032,0.06434561,-0.03649955,0.01137687,-0.0673917,-0.00102424,-0.0308159,0.0521733,0.112731,0.0042449,-0.03158012,-0.02612434,0.03856349,0.03918612,-0.10615283,-0.03355287,-0.02036993,-0.06517605,0.00217183,0.05043923,0.01951645,-0.02919001,0.04473124,0.00344895,0.03560186,0.0708797,0.04811667,0.003511,0.00925787],"last_embed":{"hash":"1hmqvbb","tokens":113}}},"text":null,"length":0,"last_read":{"hash":"1hmqvbb","at":1753423636871},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>5. The Best Lottery Strategy, Unusual Filter Levels</u>#{28}","lines":[161,163],"size":317,"outlinks":[{"title":"_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_","target":"https://saliu.com/lottery-numbers-loss.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05811202,-0.03524165,-0.01771146,0.00834877,-0.05169053,0.07036199,0.03275456,0.02841109,0.07428429,-0.01627969,0.00427186,0.01933846,0.01267906,0.02843792,-0.02238329,0.00632341,-0.01297392,-0.0118611,-0.05113178,0.0240834,0.04776813,-0.06362271,-0.06053419,-0.10289062,0.07089382,-0.03130866,-0.01763249,-0.06197543,-0.04721253,-0.21058771,-0.00375901,0.00558249,0.00308859,-0.05347688,-0.10473964,-0.02884926,-0.02323925,0.05463926,-0.05218261,0.05185051,0.01802448,0.01895821,-0.0066082,-0.00499541,0.01922473,-0.01667383,0.0070473,-0.01084229,0.01503076,-0.00590241,-0.06331271,0.04824824,-0.03270729,0.00898464,0.05559591,0.01862057,0.04335958,0.1030657,-0.0173103,0.01620953,0.00109001,0.09297138,-0.1797642,0.06388475,-0.02083816,0.02609278,-0.02787969,0.0084841,-0.02274959,0.08004007,0.06225633,0.04112081,0.01366015,0.03848097,0.06671164,-0.01263202,-0.03190824,-0.04024724,0.00351631,0.07953206,-0.03346549,-0.0184559,-0.0193827,-0.0248147,0.0179963,0.03362484,0.00799852,0.02639195,0.06815492,-0.03525791,0.07385096,0.06060479,-0.00497581,0.00038074,0.06244928,0.00969066,0.0322391,-0.03111741,-0.02122169,0.09770211,0.01132138,-0.00674649,-0.01131528,0.00088828,0.0449204,-0.04603333,-0.06379987,-0.0434538,-0.01487195,0.01201901,0.01636478,-0.02960917,0.10115381,-0.00533421,-0.0455109,0.03766258,0.00978625,0.00827555,0.02153425,-0.0046397,-0.06137026,0.07022973,-0.01169688,-0.01477334,-0.02646046,-0.06534842,-0.01619873,0.07740355,0.00089538,0.00384941,0.03893457,-0.01184018,-0.1228256,-0.02176801,-0.03586822,-0.06274272,0.01535537,0.03191097,0.01013479,0.0325832,-0.00856322,-0.03361137,0.05987481,-0.12163538,-0.03645723,0.0723374,0.05390141,0.02425687,0.02293785,0.00865006,0.00763665,-0.00193118,-0.04006898,-0.07449222,-0.01200689,-0.00133426,0.06368875,0.0511817,-0.04353245,-0.01233618,-0.07248078,-0.01308793,-0.01143192,0.10378224,-0.03873605,-0.07983168,-0.04418617,0.00611737,-0.02334818,-0.09915487,0.00366989,-0.01931534,-0.04821842,0.01880155,0.0852413,-0.03099148,-0.08509832,-0.05173709,-0.00314555,0.04908613,0.05505612,-0.03316075,-0.03156758,0.02719027,-0.02881365,-0.05211321,0.03656409,-0.03083454,0.0346914,0.00710727,-0.03564975,0.02069868,-0.07856345,0.01048911,-0.03712943,-0.03899015,-0.0086485,-0.04944738,0.0420308,0.01195946,-0.08554414,-0.0018644,0.01925149,0.01165032,-0.02376348,0.06268191,0.02511532,-0.09364127,0.11247145,0.02860711,0.00138405,-0.01964049,-0.01204896,0.06452936,-0.05614853,0.02869279,0.0568647,0.04300643,-0.02371836,-0.01133566,0.02400875,0.01792153,-0.06650183,-0.16330166,-0.08395062,-0.09168874,0.01090652,0.04639053,-0.0193597,0.01525789,-0.02973503,0.01834072,0.06368999,0.04957645,-0.06596802,-0.01561047,0.05044864,-0.00032755,-0.05050868,-0.13024782,-0.04141374,-0.07681566,0.04549038,-0.00588623,0.00156735,-0.01975199,-0.08940104,0.01423926,-0.03605995,0.16269918,0.04867027,0.02610068,0.03567094,0.04719745,0.01312316,-0.00521843,-0.04301803,0.03540315,0.03052273,0.02012403,-0.024214,-0.0663923,0.00061971,-0.02202665,0.03606927,-0.00471017,-0.08493077,-0.12070806,0.00268377,-0.03312701,0.07001335,-0.00902368,0.09193808,0.06824117,-0.02055418,0.09194311,0.0184755,0.05412317,-0.01078865,-0.08789056,0.01879886,-0.00856203,0.09225218,-0.01324258,-0.01738226,0.03980283,-0.00714967,0.0629649,0.02559719,0.00164526,-0.02634802,0.02328279,-0.00319657,-0.003774,0.08024902,-0.00483521,-0.00751454,0.04688826,0.02062668,0.07304739,-0.0372223,-0.00251757,0.01382428,0.01784749,-0.07606159,0.03610535,0.06095161,-0.00503534,-0.01632454,0.0429331,0.04241274,-0.03644479,0.00340828,-0.00637298,0.03826722,-0.04243748,-0.00981718,-0.013077,0.02906717,-0.2264622,0.04293472,-0.02769469,0.03501752,-0.00431472,-0.04768717,0.04272545,-0.00661681,-0.01402865,-0.02382837,0.05573547,0.0432117,0.0176377,-0.03963306,0.01619857,-0.03540555,0.00056104,-0.06679619,0.06258883,0.0327509,0.07619371,0.06705406,0.23373176,-0.02641385,0.00386657,0.01062712,0.03015172,0.03595401,-0.04003862,-0.01298428,0.0007273,0.03596274,0.01981319,-0.03095671,-0.03382814,0.01731529,-0.0179146,0.04819321,-0.02417038,-0.03026137,-0.05009418,-0.01520107,0.03218392,0.03644491,0.14342479,0.00026254,0.00031229,-0.04415957,0.04453388,0.04841987,-0.07996549,-0.05051099,-0.01225509,-0.05486938,0.00072248,0.03629657,0.02969177,-0.02447721,0.03333958,-0.00459053,-0.01345153,0.05416184,0.0826259,0.06167088,0.00698133],"last_embed":{"hash":"17qqt8q","tokens":407}}},"text":null,"length":0,"last_read":{"hash":"17qqt8q","at":1753423636911},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>","lines":[166,196],"size":4161,"outlinks":[{"title":"**software**","target":"https://saliu.com/infodown.html","line":24},{"title":"The best winning scientific strategies for gambling, lottery have always been free at Ion Saliu's.","target":"https://saliu.com/HLINE.gif","line":26},{"title":"Forums","target":"https://forums.saliu.com/","line":28},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":28},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":28},{"title":"Contents","target":"https://saliu.com/content/index.html","line":28},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":28},{"title":"Home","target":"https://saliu.com/index.htm","line":28},{"title":"Search","target":"https://saliu.com/Search.htm","line":28},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":28},{"title":"This is the site of truly winning lotteries, gambling, casino, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":30}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05816859,-0.04086101,-0.01975407,0.00569686,-0.04702093,0.07043771,0.03132121,0.02362097,0.08264098,-0.01677263,0.00493578,0.01931366,0.01432759,0.02209199,-0.02762229,0.00892261,-0.01254403,-0.01007424,-0.05553395,0.02506338,0.04232999,-0.06196637,-0.0537692,-0.09379673,0.06780557,-0.03030068,-0.01753171,-0.06265801,-0.04332694,-0.21648362,-0.00074979,0.00214748,0.00142594,-0.05905396,-0.09762964,-0.03112389,-0.02081593,0.05496977,-0.04613291,0.0518567,0.01600136,0.02601939,-0.00966006,-0.0044088,0.01698123,-0.01635556,0.00286592,-0.01229369,0.01662976,-0.00362606,-0.06444927,0.04708419,-0.03330793,0.00706548,0.06164636,0.02434485,0.04485264,0.10795444,-0.0152823,0.01168792,0.00286357,0.08901978,-0.17751247,0.0631218,-0.01713143,0.02256264,-0.0270089,0.01142398,-0.02165524,0.08838765,0.06143111,0.0387517,0.01257303,0.04423697,0.06365203,-0.01564156,-0.03321799,-0.03710354,0.0020014,0.08028581,-0.03176131,-0.02102488,-0.02145836,-0.028498,0.00843443,0.0311782,0.01196412,0.02308696,0.06819686,-0.03765463,0.07855996,0.06314915,-0.00653866,0.00069418,0.06375287,0.00339925,0.03260186,-0.0211685,-0.01979544,0.09093857,0.00871099,-0.00556409,-0.01456812,-0.00134152,0.04370298,-0.04747136,-0.06351946,-0.0388434,-0.01543648,0.01310047,0.02231767,-0.02594693,0.09478621,-0.00452408,-0.04466904,0.03749553,0.01316245,0.00700131,0.01534363,-0.00290263,-0.05660565,0.07367226,-0.00328754,-0.01539471,-0.03136255,-0.06309152,-0.01440259,0.07857955,0.00257199,0.00083407,0.03784574,-0.01921783,-0.12348768,-0.02414019,-0.0355532,-0.06300326,0.02003325,0.03364062,0.01257815,0.02946506,-0.00952045,-0.03205359,0.06153911,-0.12261759,-0.04175583,0.07095008,0.05272083,0.02577868,0.02527553,0.00374254,0.00224253,-0.00531248,-0.04366638,-0.07976732,-0.01206539,-0.00071609,0.06094637,0.0534973,-0.04630633,-0.01952408,-0.07392166,-0.01134548,-0.00963477,0.09763623,-0.03894024,-0.07726933,-0.04269649,0.00584373,-0.02605036,-0.10108332,-0.0013212,-0.0239925,-0.04648522,0.01286865,0.08586934,-0.03337574,-0.09338263,-0.05304664,-0.00095159,0.04625069,0.05794559,-0.0293152,-0.02778662,0.02918585,-0.02808563,-0.05168356,0.02929026,-0.02703213,0.03575053,0.00109144,-0.03848016,0.0279522,-0.08241216,0.01268184,-0.03413048,-0.03993947,-0.00713109,-0.05315414,0.04690962,0.01265788,-0.08620905,-0.00646355,0.01604585,0.00891842,-0.02629859,0.06707707,0.02251467,-0.09065034,0.11330602,0.03014132,0.00167306,-0.0209021,-0.01590297,0.06465682,-0.05840451,0.02995743,0.06051926,0.0450591,-0.02045249,-0.01524556,0.02706931,0.02332425,-0.05805445,-0.16756089,-0.07703636,-0.08678861,0.00646551,0.04746826,-0.01756815,0.01624199,-0.03081641,0.01980874,0.05693059,0.05002635,-0.06936552,-0.02090225,0.0519285,-0.01090427,-0.04834464,-0.13058324,-0.04046897,-0.07832053,0.04717441,-0.01219599,0.00356028,-0.02507168,-0.08411197,0.01429137,-0.03286134,0.16377665,0.04259685,0.03013262,0.03200522,0.04669074,0.02183677,-0.00426502,-0.04385862,0.03196236,0.03613741,0.01923083,-0.0212894,-0.06357604,-0.00241489,-0.02166315,0.03349983,-0.01179391,-0.08749086,-0.11894104,0.00264809,-0.03518946,0.07395831,-0.01180451,0.09402639,0.07010477,-0.01610852,0.09288711,0.01467946,0.05345317,-0.01266498,-0.08676147,0.01931679,-0.00376241,0.09265881,-0.00951539,-0.01784789,0.03952509,-0.00264235,0.06569812,0.02618646,0.0073946,-0.02317092,0.01975052,-0.00786674,-0.00274188,0.08012788,-0.01051418,-0.00852676,0.04838921,0.02253484,0.07884634,-0.03597061,0.00116428,0.01648574,0.01949465,-0.07632232,0.04122714,0.06046551,-0.00095749,-0.02104119,0.04391092,0.04276175,-0.03555264,0.00415953,-0.00432091,0.03976265,-0.04671023,-0.01264788,-0.01299247,0.0290173,-0.22772321,0.04370369,-0.02305744,0.02854486,-0.00890946,-0.04496742,0.04139017,-0.00293843,-0.01182318,-0.01699413,0.04884547,0.046103,0.01489764,-0.0361012,0.02142015,-0.03614565,0.00074344,-0.06182316,0.06600862,0.03065284,0.07755101,0.06541315,0.23063859,-0.02754413,-0.0008786,0.01273062,0.02385096,0.03398891,-0.03870236,-0.00968247,0.00179506,0.03345031,0.01943627,-0.03318816,-0.03421171,0.01412273,-0.01744025,0.04646082,-0.01843125,-0.02418798,-0.04809725,-0.01267721,0.03820725,0.03409282,0.14313337,-0.00264493,0.00296555,-0.04748553,0.04526958,0.04746599,-0.07513613,-0.05362555,-0.0131511,-0.05869902,-0.00151746,0.03518033,0.0291005,-0.02157231,0.03471784,0.00066342,-0.01271056,0.05276687,0.08134144,0.06463638,0.00676134],"last_embed":{"hash":"ubyals","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"ubyals","at":1753423637067},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{1}","lines":[168,188],"size":3391,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07643553,-0.00320061,-0.03278222,0.03156725,-0.01433955,0.0677463,0.01884243,0.06263301,0.05288267,-0.00140257,-0.01679423,-0.0231963,-0.01770822,0.01942345,-0.03637165,-0.03883602,-0.00885862,0.01477032,-0.04605534,0.02057758,0.05714232,-0.04033078,-0.03885362,-0.09940009,0.08261932,-0.02725718,-0.03453322,-0.0632393,-0.03273143,-0.19235387,-0.02483489,-0.02904245,-0.05369993,-0.06708825,-0.09896942,-0.00120599,0.01062206,0.05528155,-0.02038954,0.05954893,0.05733575,0.00123756,-0.00913403,0.02040919,-0.0095509,0.00651939,0.04682083,-0.00558918,0.05523997,0.0081324,-0.05745181,0.03258989,-0.04482112,-0.01399646,0.08354639,0.01006292,0.0545962,0.08624342,-0.03876814,0.00217956,-0.01383202,0.09612538,-0.23501748,0.07285617,-0.02659751,0.03129021,-0.02096323,0.01631812,-0.00839965,0.05036344,0.04764337,0.01399911,0.00994644,0.03204541,0.07409537,-0.02801458,-0.03587289,-0.05991214,-0.01180496,0.04100195,-0.03861494,-0.00162203,-0.00814696,-0.03177296,0.05542384,0.07173653,-0.00629978,0.04530448,0.07267334,-0.06925797,0.05872307,0.04904489,0.01853326,0.01882667,0.02130285,0.03816623,0.03399477,-0.05905556,-0.00249567,0.09743512,0.00650256,0.01221945,-0.01708489,0.0075035,0.06717446,-0.04798388,-0.04671908,-0.03157905,-0.01313224,0.01220144,0.04149675,0.00648332,0.09639023,-0.01939034,-0.05166016,0.0103137,0.02005366,-0.02103381,-0.00187978,0.01463373,-0.05379876,0.03738463,0.00919302,0.00210178,-0.00281,-0.05729939,0.00273856,0.06552928,-0.00147082,0.02147662,0.03140538,0.00924605,-0.11510558,-0.07059219,-0.07699072,-0.05728024,0.02649453,0.00935024,0.00833708,0.01462756,-0.02723422,-0.0577299,0.02518485,-0.12952834,-0.0121125,0.06021035,0.04143032,0.00301732,0.00303566,0.02364466,-0.01349108,0.01257438,0.00546322,-0.04070938,-0.00777879,-0.00078657,0.02641166,0.06032284,-0.04318222,-0.01353329,-0.06722597,-0.0327671,-0.02958579,0.13673992,-0.0315768,-0.0863987,-0.05780715,0.00779368,-0.02411277,-0.09700917,-0.02717178,-0.01401918,-0.0493454,0.00516932,0.07624154,-0.01733145,-0.06132102,-0.05038422,-0.04015056,0.02435611,0.00497789,-0.05411313,-0.0486769,0.02793023,-0.0216107,-0.06162443,0.01585596,-0.06760054,0.06006999,0.07894916,-0.0425172,-0.00984027,-0.04306962,0.02696933,-0.04489189,-0.01200848,-0.04076549,-0.06402412,0.05663621,0.02067319,-0.0516349,-0.01611883,-0.01344952,-0.01978085,-0.04606763,0.05885217,0.01289304,-0.07583464,0.08111098,0.01313846,-0.00315316,0.0230725,0.03820018,0.03027777,-0.06500021,0.02419534,0.06355638,0.0219323,-0.04166452,0.02188637,0.06065514,0.00032982,-0.07319231,-0.17780569,-0.04693519,-0.0689141,-0.01898626,0.05151721,-0.02902391,0.0371456,-0.04633681,0.05053173,0.03248645,0.05799767,-0.04127245,0.01553774,0.05182273,-0.00851748,-0.0071999,-0.06813167,-0.05076093,-0.05389556,0.04701688,-0.0110371,-0.01942227,-0.01940952,-0.0488325,0.02967152,-0.01864116,0.14912084,0.04228656,0.04628561,0.02276238,0.04341756,0.04610867,-0.011104,-0.05218295,0.03027313,0.01340804,-0.00767889,-0.01624917,-0.04847509,-0.01587214,-0.00500707,0.035119,-0.00825641,-0.07699982,-0.10761377,0.00311996,-0.02539481,0.04820269,-0.00358104,0.0302714,0.09948517,0.02503019,0.05295915,0.00525807,0.07947592,0.01265196,-0.07772253,-0.01702024,-0.02936932,0.06074512,-0.01963418,-0.00393874,0.01746391,0.01152248,0.04304452,-0.01498447,0.03130903,-0.01698814,0.04245207,0.00208746,-0.03261507,0.074503,0.00195624,0.01307597,0.04621464,0.03100915,0.07721442,-0.0732834,-0.00897131,0.02502145,0.00584326,-0.06970332,0.05683779,0.04465614,0.03647134,0.04360627,0.08103675,0.01191957,-0.0302643,-0.03268586,0.00872205,0.02027244,-0.03734007,0.00442514,0.02052335,0.04419568,-0.25737977,0.02563678,-0.00388406,0.02461956,0.00377459,-0.01678765,0.03876592,-0.03027067,0.00219844,-0.00351252,0.08166943,0.02368627,0.00474019,-0.02673428,0.00367671,-0.04981846,-0.01421824,-0.05258474,0.07312261,0.03621059,0.0556651,0.06206805,0.22830753,-0.02347715,-0.01371306,0.00195353,0.01222775,-0.01332288,-0.01968232,-0.00408979,0.02094455,0.04996226,0.01142007,-0.00823991,-0.04385046,0.07778681,0.00989604,0.03036505,-0.03170836,-0.02619329,-0.06159453,0.02764649,0.02505995,0.04379662,0.13522398,-0.00860419,-0.03859667,-0.03355836,0.05043327,0.03345856,-0.09911186,-0.05405478,-0.02597279,-0.02068951,-0.02000791,0.02133639,0.06132399,-0.04191846,0.04905277,0.02225898,0.00156925,0.07478992,0.06665058,0.0475903,0.03202134],"last_embed":{"hash":"t43t70","tokens":285}}},"text":null,"length":0,"last_read":{"hash":"t43t70","at":1753423637225},"key":"notes/saliu/The Best Strategy in Lottery, Gambling.md#The Best Strategy in Lottery, Gambling#<u>6. Conclusions</u>#{3}","lines":[191,196],"size":656,"outlinks":[{"title":"The best winning scientific strategies for gambling, lottery have always been free at Ion Saliu's.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"This is the site of truly winning lotteries, gambling, casino, blackjack, roulette, horses, sports.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
