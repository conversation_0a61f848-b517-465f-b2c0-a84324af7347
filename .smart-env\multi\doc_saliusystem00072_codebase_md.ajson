
"smart_sources:doc/saliusystem00072_codebase.md": {"path":"doc/saliusystem00072_codebase.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09902696,0.01445481,-0.05020262,0.00369512,-0.00612074,0.05596053,0.00948299,0.01588629,0.04747713,0.00360724,0.01446074,-0.07335976,0.02619459,0.05385605,0.00890891,-0.05103103,0.0114369,-0.01401908,-0.05547706,0.04774888,0.09839473,-0.06539962,-0.00490898,-0.1198016,0.00944725,0.00990926,-0.00980509,-0.02674983,-0.04072078,-0.19884914,0.01063261,0.01465001,0.03127352,-0.02881983,-0.04417976,-0.01592331,-0.04187812,0.01862095,-0.0495321,0.02538997,0.03432037,-0.01901957,0.03603259,-0.00738619,0.0086565,-0.07018439,-0.01865705,-0.0254015,0.0807755,0.00324935,-0.07407279,-0.02580723,-0.00540706,0.03523927,0.02038162,0.06579421,0.00526766,0.04640484,0.00984432,-0.00977304,-0.00650201,-0.03090315,-0.24163611,0.03973056,-0.00655481,-0.01628793,-0.0272928,-0.04176998,0.02094168,-0.00574753,0.01164036,0.06238883,-0.01441251,0.04828963,0.03767211,-0.0314521,0.00045334,-0.06131023,-0.02109961,0.00756616,-0.04958794,-0.02500335,-0.0359643,-0.00915245,0.02775066,0.04050627,0.02693725,0.03144922,0.05210431,-0.06856504,-0.00634305,-0.00207418,0.0241426,0.05578464,-0.02111388,0.04375404,0.00138019,0.01362797,-0.05593261,0.10062399,0.0493989,-0.02145041,-0.00502387,0.00128007,0.06396376,-0.03951157,-0.02004613,-0.03067795,-0.02473114,-0.00995863,0.01906828,-0.05026031,0.05690146,-0.03199056,-0.04754824,0.03506958,0.05684615,0.0228788,0.00455114,0.01463515,-0.03550664,0.01904172,0.01523826,-0.0138463,0.04831313,-0.00170416,0.03654482,0.05330888,0.04848,0.01612795,0.01607832,0.03359678,-0.08943911,-0.02645791,0.00879907,-0.00498459,0.00178772,-0.00550705,-0.01686163,-0.02223341,0.00456411,-0.04116413,0.05706321,-0.09646953,-0.04000218,0.04904524,-0.03642473,0.01621222,-0.01590917,-0.00145285,0.00931704,0.0130058,0.01611919,-0.05684253,0.03571309,0.01528371,0.13355161,0.10721742,0.00088512,0.00979763,0.01418596,-0.04881,-0.03449314,0.18099661,0.03823638,-0.06478433,-0.03495293,0.03477819,0.03099462,-0.06864956,-0.00128793,-0.00380107,0.0034475,-0.00803979,0.10054029,-0.0400734,-0.02112553,-0.03435243,-0.04805784,0.02705836,-0.02985989,-0.01083573,0.00484908,0.02968286,-0.01484199,-0.10507051,0.01393021,-0.04294776,-0.02051747,-0.01030475,-0.0506109,0.06591186,0.01815677,0.0696613,-0.02092614,-0.05046703,-0.02546764,-0.01801869,0.00925541,-0.04326337,0.07516526,0.04964573,0.00618978,0.00741843,-0.0040305,-0.01184757,-0.01997987,-0.03403547,-0.00253063,0.0547202,-0.01500591,0.02250978,0.02978419,0.00828997,-0.01732119,0.03290295,-0.02115417,-0.02276054,0.04409153,0.01142391,0.01265176,0.02638971,-0.10180157,-0.24862361,-0.00116592,-0.05744667,-0.07776161,-0.01490957,-0.04495002,0.07142889,-0.07144877,0.09990442,0.03648196,0.11970669,0.01413127,-0.02413511,0.0137566,-0.03503723,-0.02448102,0.03676747,-0.00813848,-0.01740412,0.0413297,0.06196943,0.06621978,0.06050045,-0.01184191,0.01476235,-0.0084643,0.15016784,0.02773384,0.05146186,0.02917511,0.05300906,0.05395439,0.03243699,-0.10649675,0.06465018,0.04033377,-0.05788381,0.02981031,-0.04550719,-0.03784957,-0.06287837,0.04251063,-0.01502633,-0.08864442,-0.0071342,-0.04443136,-0.03831153,-0.04080166,-0.04464535,0.00299003,0.0284122,-0.0022588,0.00412396,0.04211914,0.05516542,0.01177567,-0.06674247,-0.00998469,-0.01427206,0.00531497,-0.0428216,0.00144031,0.02207035,-0.00400924,-0.00303925,-0.00918984,-0.02788554,-0.00392201,-0.00704048,-0.0533821,-0.00293008,0.11436751,-0.00306508,0.02199916,0.02833815,0.02564471,0.01233829,-0.06531002,0.02838282,-0.00889734,0.02552119,0.00831591,0.01814504,0.03456478,0.06344856,0.03559135,0.01230945,0.09348848,0.01174572,0.00824976,-0.04649784,0.01365092,-0.03049712,0.05172721,0.05001251,-0.00152475,-0.29073238,0.01670907,-0.0368037,0.01938961,-0.02084121,-0.02392374,0.02361105,-0.08925961,-0.01881183,-0.00209417,0.03660786,0.07202816,0.04942224,-0.06073477,-0.009948,-0.01125931,0.02058342,-0.01333659,0.05114647,-0.016,-0.02041262,0.0269056,0.23369621,0.03072157,-0.00075927,-0.02161027,0.03128069,0.03102192,0.0241574,0.03138907,-0.0282183,0.00387932,0.03565777,0.01238985,-0.05889072,0.05165587,-0.02093163,0.01379475,0.01999627,-0.01730885,-0.08114117,0.0579312,-0.06076207,0.01267375,0.10818745,0.01736157,-0.00123603,-0.06265235,0.05386174,0.10071241,-0.05426721,-0.03364097,-0.0163535,0.0001345,-0.02652756,0.0067787,-0.01890369,-0.04031844,0.02317136,-0.02845182,0.02732086,0.00181232,0.05237001,0.00537454,0.02766611],"last_embed":{"hash":"1nvkolf","tokens":388}}},"last_read":{"hash":"1nvkolf","at":1753511069936},"class_name":"SmartSource","last_import":{"mtime":1753510188075,"size":3380,"at":1753511068511,"hash":"1nvkolf"},"blocks":{"#Saliu Lottery System 代碼庫描述":[1,115],"#Saliu Lottery System 代碼庫描述#系統概述":[3,6],"#Saliu Lottery System 代碼庫描述#系統概述#{1}":[5,6],"#Saliu Lottery System 代碼庫描述#核心架構":[7,17],"#Saliu Lottery System 代碼庫描述#核心架構#主要目錄結構":[9,17],"#Saliu Lottery System 代碼庫描述#核心架構#主要目錄結構#{1}":[10,17],"#Saliu Lottery System 代碼庫描述#核心功能模組":[18,48],"#Saliu Lottery System 代碼庫描述#核心功能模組#1. 數據管理系統":[20,24],"#Saliu Lottery System 代碼庫描述#核心功能模組#1. 數據管理系統#{1}":[21,21],"#Saliu Lottery System 代碼庫描述#核心功能模組#1. 數據管理系統#{2}":[22,22],"#Saliu Lottery System 代碼庫描述#核心功能模組#1. 數據管理系統#{3}":[23,24],"#Saliu Lottery System 代碼庫描述#核心功能模組#2. 過濾器引擎":[25,31],"#Saliu Lottery System 代碼庫描述#核心功能模組#2. 過濾器引擎#{1}":[26,26],"#Saliu Lottery System 代碼庫描述#核心功能模組#2. 過濾器引擎#{2}":[27,27],"#Saliu Lottery System 代碼庫描述#核心功能模組#2. 過濾器引擎#{3}":[28,28],"#Saliu Lottery System 代碼庫描述#核心功能模組#2. 過濾器引擎#{4}":[29,29],"#Saliu Lottery System 代碼庫描述#核心功能模組#2. 過濾器引擎#{5}":[30,31],"#Saliu Lottery System 代碼庫描述#核心功能模組#3. 跳躍系統 (Skip System)":[32,37],"#Saliu Lottery System 代碼庫描述#核心功能模組#3. 跳躍系統 (Skip System)#{1}":[33,33],"#Saliu Lottery System 代碼庫描述#核心功能模組#3. 跳躍系統 (Skip System)#{2}":[34,34],"#Saliu Lottery System 代碼庫描述#核心功能模組#3. 跳躍系統 (Skip System)#{3}":[35,35],"#Saliu Lottery System 代碼庫描述#核心功能模組#3. 跳躍系統 (Skip System)#{4}":[36,37],"#Saliu Lottery System 代碼庫描述#核心功能模組#4. Wonder Grid 策略":[38,43],"#Saliu Lottery System 代碼庫描述#核心功能模組#4. Wonder Grid 策略#{1}":[39,39],"#Saliu Lottery System 代碼庫描述#核心功能模組#4. Wonder Grid 策略#{2}":[40,40],"#Saliu Lottery System 代碼庫描述#核心功能模組#4. Wonder Grid 策略#{3}":[41,41],"#Saliu Lottery System 代碼庫描述#核心功能模組#4. Wonder Grid 策略#{4}":[42,43],"#Saliu Lottery System 代碼庫描述#核心功能模組#5. LIE 消除技術":[44,48],"#Saliu Lottery System 代碼庫描述#核心功能模組#5. LIE 消除技術#{1}":[45,45],"#Saliu Lottery System 代碼庫描述#核心功能模組#5. LIE 消除技術#{2}":[46,46],"#Saliu Lottery System 代碼庫描述#核心功能模組#5. LIE 消除技術#{3}":[47,48],"#Saliu Lottery System 代碼庫描述#軟體工具整合":[49,64],"#Saliu Lottery System 代碼庫描述#軟體工具整合#核心軟體組件":[51,57],"#Saliu Lottery System 代碼庫描述#軟體工具整合#核心軟體組件#{1}":[52,52],"#Saliu Lottery System 代碼庫描述#軟體工具整合#核心軟體組件#{2}":[53,53],"#Saliu Lottery System 代碼庫描述#軟體工具整合#核心軟體組件#{3}":[54,54],"#Saliu Lottery System 代碼庫描述#軟體工具整合#核心軟體組件#{4}":[55,55],"#Saliu Lottery System 代碼庫描述#軟體工具整合#核心軟體組件#{5}":[56,57],"#Saliu Lottery System 代碼庫描述#軟體工具整合#報告生成系統":[58,64],"#Saliu Lottery System 代碼庫描述#軟體工具整合#報告生成系統#{1}":[59,59],"#Saliu Lottery System 代碼庫描述#軟體工具整合#報告生成系統#{2}":[60,60],"#Saliu Lottery System 代碼庫描述#軟體工具整合#報告生成系統#{3}":[61,61],"#Saliu Lottery System 代碼庫描述#軟體工具整合#報告生成系統#{4}":[62,62],"#Saliu Lottery System 代碼庫描述#軟體工具整合#報告生成系統#{5}":[63,64],"#Saliu Lottery System 代碼庫描述#技術特色":[65,81],"#Saliu Lottery System 代碼庫描述#技術特色#1. 大數據處理":[67,71],"#Saliu Lottery System 代碼庫描述#技術特色#1. 大數據處理#{1}":[68,68],"#Saliu Lottery System 代碼庫描述#技術特色#1. 大數據處理#{2}":[69,69],"#Saliu Lottery System 代碼庫描述#技術特色#1. 大數據處理#{3}":[70,71],"#Saliu Lottery System 代碼庫描述#技術特色#2. 動態過濾":[72,76],"#Saliu Lottery System 代碼庫描述#技術特色#2. 動態過濾#{1}":[73,73],"#Saliu Lottery System 代碼庫描述#技術特色#2. 動態過濾#{2}":[74,74],"#Saliu Lottery System 代碼庫描述#技術特色#2. 動態過濾#{3}":[75,76],"#Saliu Lottery System 代碼庫描述#技術特色#3. 跨平台策略":[77,81],"#Saliu Lottery System 代碼庫描述#技術特色#3. 跨平台策略#{1}":[78,78],"#Saliu Lottery System 代碼庫描述#技術特色#3. 跨平台策略#{2}":[79,79],"#Saliu Lottery System 代碼庫描述#技術特色#3. 跨平台策略#{3}":[80,81],"#Saliu Lottery System 代碼庫描述#數學理論基礎":[82,98],"#Saliu Lottery System 代碼庫描述#數學理論基礎#FFG (賭博基本公式)":[84,88],"#Saliu Lottery System 代碼庫描述#數學理論基礎#FFG (賭博基本公式)#{1}":[85,85],"#Saliu Lottery System 代碼庫描述#數學理論基礎#FFG (賭博基本公式)#{2}":[86,86],"#Saliu Lottery System 代碼庫描述#數學理論基礎#FFG (賭博基本公式)#{3}":[87,88],"#Saliu Lottery System 代碼庫描述#數學理論基礎#馬可夫鏈分析":[89,93],"#Saliu Lottery System 代碼庫描述#數學理論基礎#馬可夫鏈分析#{1}":[90,90],"#Saliu Lottery System 代碼庫描述#數學理論基礎#馬可夫鏈分析#{2}":[91,91],"#Saliu Lottery System 代碼庫描述#數學理論基礎#馬可夫鏈分析#{3}":[92,93],"#Saliu Lottery System 代碼庫描述#數學理論基礎#組合數學":[94,98],"#Saliu Lottery System 代碼庫描述#數學理論基礎#組合數學#{1}":[95,95],"#Saliu Lottery System 代碼庫描述#數學理論基礎#組合數學#{2}":[96,96],"#Saliu Lottery System 代碼庫描述#數學理論基礎#組合數學#{3}":[97,98],"#Saliu Lottery System 代碼庫描述#系統優勢":[99,106],"#Saliu Lottery System 代碼庫描述#系統優勢#{1}":[101,101],"#Saliu Lottery System 代碼庫描述#系統優勢#{2}":[102,102],"#Saliu Lottery System 代碼庫描述#系統優勢#{3}":[103,103],"#Saliu Lottery System 代碼庫描述#系統優勢#{4}":[104,104],"#Saliu Lottery System 代碼庫描述#系統優勢#{5}":[105,106],"#Saliu Lottery System 代碼庫描述#開發狀態":[107,115],"#Saliu Lottery System 代碼庫描述#開發狀態#{1}":[109,109],"#Saliu Lottery System 代碼庫描述#開發狀態#{2}":[110,110],"#Saliu Lottery System 代碼庫描述#開發狀態#{3}":[111,111],"#Saliu Lottery System 代碼庫描述#開發狀態#{4}":[112,112],"#Saliu Lottery System 代碼庫描述#開發狀態#{5}":[113,114],"#Saliu Lottery System 代碼庫描述#開發狀態#{6}":[115,115]},"outlinks":[]},"smart_blocks:doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09563708,0.01779126,-0.05065924,0.00716813,-0.00925714,0.05765688,0.00962506,0.01503022,0.04766506,0.00256796,0.01565503,-0.07385869,0.02534208,0.05484428,0.008674,-0.04983715,0.01077303,-0.01655689,-0.0558705,0.04495716,0.09815325,-0.06820241,-0.00497442,-0.11996378,0.01050691,0.00914745,-0.00944241,-0.0253826,-0.03984911,-0.19819559,0.01051696,0.01548638,0.0324037,-0.02836922,-0.04249802,-0.0141934,-0.04253475,0.02152363,-0.05272361,0.02441019,0.03700103,-0.02182966,0.03546116,-0.00609766,0.00482381,-0.07287794,-0.01924494,-0.02535022,0.07845695,0.00302688,-0.07485533,-0.02647381,-0.00391248,0.03707276,0.01974923,0.06460861,0.00578352,0.03978626,0.0118496,-0.0111885,-0.006858,-0.03181557,-0.2431608,0.03869642,-0.00865287,-0.01843663,-0.02647217,-0.04268628,0.02067657,-0.00752423,0.01152788,0.06261407,-0.01255867,0.04423977,0.03836866,-0.03240431,0.00068475,-0.06048054,-0.02235942,0.00650187,-0.04721272,-0.02665021,-0.03675285,-0.00869452,0.02925825,0.04035681,0.02867916,0.031465,0.05318677,-0.06623709,-0.00663457,0.0012784,0.02280735,0.05604944,-0.02024199,0.0426458,0.00085637,0.01273919,-0.05578319,0.10168811,0.04875639,-0.01932544,-0.0042406,-0.00020508,0.06299909,-0.03730489,-0.01676721,-0.02990915,-0.02515692,-0.01199517,0.01928345,-0.0514857,0.05637837,-0.0302737,-0.0514942,0.03246216,0.05655484,0.02302982,0.00432117,0.01457877,-0.03478941,0.01935299,0.01289148,-0.01311608,0.05080014,-0.0028453,0.03770707,0.05279612,0.04984049,0.01395617,0.01838644,0.03374936,-0.08884539,-0.02478062,0.00589072,-0.00367796,-0.00202752,-0.00886986,-0.02078285,-0.02350276,0.00468369,-0.03970517,0.05590781,-0.09949998,-0.03996956,0.04772768,-0.03635101,0.01771687,-0.01533683,-0.00071804,0.01101587,0.01283817,0.01747893,-0.05884093,0.0390165,0.01600439,0.12911648,0.10911951,0.00204071,0.00850914,0.01428696,-0.04584588,-0.03191357,0.18252011,0.03802691,-0.06320293,-0.03409458,0.03547478,0.03216164,-0.07005121,0.00089043,-0.00439097,0.00543551,-0.00466483,0.10058208,-0.0404228,-0.01972034,-0.0367592,-0.04917306,0.02532589,-0.02928484,-0.01208694,0.00542078,0.03274808,-0.01681463,-0.10233698,0.01570965,-0.04185627,-0.02006641,-0.00964377,-0.04922159,0.06577804,0.02073954,0.06914836,-0.02212355,-0.05030489,-0.02463631,-0.01996647,0.0087366,-0.04437848,0.07678658,0.04951669,0.00609694,0.01028199,-0.00501397,-0.01351598,-0.02060761,-0.03279159,-0.00032005,0.05731063,-0.01653693,0.02420581,0.02969521,0.00333752,-0.02004091,0.03239086,-0.02125923,-0.02338721,0.04598897,0.01392934,0.01314009,0.02723368,-0.10487029,-0.2486877,-0.0029136,-0.05819867,-0.07436948,-0.01165313,-0.04492579,0.07301541,-0.06890505,0.10325288,0.03577147,0.11655747,0.0141294,-0.02399904,0.01301524,-0.03441392,-0.02455817,0.03802904,-0.00779597,-0.01917311,0.03886672,0.06023322,0.06669385,0.05683625,-0.01120524,0.0179295,-0.00576891,0.15060145,0.0291658,0.04966151,0.02791523,0.05501354,0.05369443,0.03377226,-0.10323053,0.06467839,0.0400842,-0.05815174,0.03162726,-0.04521389,-0.03780328,-0.06259827,0.04296011,-0.01308979,-0.08848687,-0.00508198,-0.04294288,-0.04022519,-0.03664305,-0.04575719,0.00055366,0.02949193,-0.00301053,0.00344203,0.04287897,0.05690151,0.0089662,-0.06678169,-0.00996845,-0.01487768,0.00601867,-0.04305332,0.00102937,0.02120397,-0.00190731,-0.0025342,-0.00919141,-0.02661969,-0.00196832,-0.00715816,-0.0561395,-0.0045084,0.11472414,-0.00182772,0.02035212,0.02810426,0.02446035,0.01215198,-0.06613941,0.02795953,-0.01075112,0.02394677,0.00842484,0.01528344,0.03339749,0.06187234,0.03689461,0.01596547,0.093876,0.01291458,0.00777503,-0.04807102,0.01358443,-0.02806785,0.05201136,0.04973209,-0.00410975,-0.29183426,0.01827935,-0.03837789,0.02083666,-0.02202746,-0.02133899,0.02188374,-0.08729684,-0.01623166,0.0021229,0.03245815,0.07055914,0.05164299,-0.06198701,-0.0115155,-0.01162856,0.02098181,-0.01522986,0.05099696,-0.0187242,-0.02293783,0.02716335,0.23457414,0.02655597,-0.00253815,-0.02358644,0.02986316,0.03133639,0.02181924,0.0304479,-0.02798448,0.00521096,0.03435358,0.00957935,-0.0545711,0.05370896,-0.02261053,0.01400785,0.01903508,-0.0160485,-0.07779923,0.05892525,-0.06339189,0.01115834,0.10816031,0.01634333,0.00029237,-0.06145043,0.05212481,0.10128532,-0.0556325,-0.03256715,-0.01432518,-0.00018109,-0.02634511,0.00569457,-0.01844338,-0.04158708,0.02522552,-0.02811336,0.02697698,0.00311005,0.05182313,0.00482986,0.02903045],"last_embed":{"hash":"1nvkolf","tokens":388}}},"text":null,"length":0,"last_read":{"hash":"1nvkolf","at":1753511069314},"key":"doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述","lines":[1,115],"size":1754,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#核心架構": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10349019,-0.02063759,-0.04893783,-0.00854593,0.01373225,0.03018393,0.0265888,0.01202641,0.04206957,-0.00604655,-0.00633703,-0.08126204,0.01629652,0.02062766,0.01846972,-0.04059903,0.01233732,0.00615287,-0.02896787,0.05726116,0.08216555,-0.04196407,-0.02666207,-0.09312837,-0.00443411,0.00288852,-0.02784972,-0.0423826,-0.04526968,-0.16866484,-0.00054906,0.00902589,0.01346935,-0.02636534,-0.02778124,-0.02608483,-0.04697714,-0.00017075,-0.04724948,0.02291019,0.01496683,-0.01611147,0.02504699,-0.01155882,0.01393089,-0.0595041,-0.00250968,-0.00452702,0.09583822,-0.00283879,-0.06866997,0.00629487,-0.00512928,0.0205995,0.01437388,0.04744259,0.01897739,0.07807115,0.00796688,0.00825572,0.01469533,0.0132671,-0.23082997,0.0301843,0.00057604,-0.00678711,-0.01799618,-0.05379274,0.01071241,-0.03093861,0.04352997,0.06225754,-0.00953817,0.0858098,0.0344474,-0.01027252,-0.01431267,-0.06058534,-0.00948807,0.03431052,-0.04066974,-0.01066269,-0.05421677,-0.00167607,0.0183413,0.04214601,0.0226345,0.03092892,0.0560997,-0.0607004,-0.00111577,-0.01431992,0.03544281,0.06457865,-0.00381562,0.04487642,0.00496357,0.00274883,-0.04304875,0.12706508,0.03455354,-0.02708788,-0.01862478,-0.00349073,0.06062829,-0.02101018,-0.02758679,-0.02879039,-0.01611928,0.01722275,0.03494863,-0.04262063,0.06216487,-0.05215953,-0.03743422,0.03582069,0.00732312,0.01686993,0.00135442,0.00137688,-0.028974,0.01490679,0.00700918,-0.00255671,0.01140837,-0.01058812,0.05259357,0.04457343,0.05141628,0.0555193,0.0272413,0.04381859,-0.11709156,-0.04268107,0.01077509,-0.01452144,-0.01888997,-0.00610917,-0.01974645,-0.02405493,0.01687969,-0.04739122,0.05249846,-0.0939289,-0.04700034,0.037953,-0.02692569,0.01143053,-0.02073384,-0.02789895,0.01198781,0.00092491,-0.0029194,-0.03833707,-0.01583518,0.00035283,0.12077546,0.12268848,-0.01447844,0.0112267,0.01654775,-0.05371426,-0.03417752,0.16470557,0.01116134,-0.07473387,-0.02676221,0.03047833,0.02661382,-0.06562779,-0.00470099,-0.00953105,0.02035853,0.01532254,0.0972854,-0.0304332,0.01195477,-0.03327524,-0.04337709,0.03314666,-0.04146964,-0.0497676,-0.01008041,0.01394661,-0.02597983,-0.0738979,0.01772644,-0.06782421,0.02260309,0.00458928,-0.04818067,0.06738051,0.00238585,0.0167381,-0.02678412,-0.03878354,-0.04858588,-0.03230011,0.0019256,-0.02633237,0.0695537,0.07158599,0.01375676,0.03637848,-0.01638828,-0.00446323,-0.03462799,-0.04070131,-0.01124373,0.02701186,-0.00200915,0.02814806,0.05307741,0.06029292,-0.02952751,0.02682692,-0.02411117,-0.00445349,0.05588162,0.0263871,-0.00580364,0.03965504,-0.10325133,-0.2494117,-0.00100586,-0.07545388,-0.07432901,-0.02114277,-0.02028975,0.03058406,-0.06237499,0.08358563,0.01981078,0.15233639,0.00238586,-0.01201999,0.02156832,-0.01659908,0.00489254,0.03436376,-0.03748721,0.00182474,0.06042317,0.04338356,0.03271087,0.03474456,-0.03064108,0.03860477,-0.0114082,0.15147503,0.03153155,0.05953489,0.04262109,0.0516812,0.03412897,0.01703048,-0.15372314,0.06503364,0.02409337,-0.06289004,0.06192628,-0.03652248,-0.03159579,-0.07032926,0.02837079,0.00886121,-0.07745796,-0.00680996,-0.06372535,-0.01228818,-0.0356376,-0.01476621,0.03330058,0.01899007,0.02375283,0.02081413,0.05716102,0.04627255,-0.00279309,-0.05484385,-0.00933784,0.02532242,0.01854653,-0.02811637,-0.01767122,0.04070992,-0.02475711,0.00292508,0.00449461,-0.02525705,-0.0342942,0.02723214,-0.03426303,-0.01136845,0.11414944,-0.00569248,-0.00026712,0.01769624,0.03395306,0.02004288,-0.03760049,0.02493732,0.00205577,0.01604061,0.00374449,0.04383611,0.04517502,0.03787687,0.03423775,0.01780259,0.09394514,0.01746834,0.01946661,0.00428068,0.02573424,-0.02714297,0.04745954,0.02361826,0.02276204,-0.30042568,0.01551309,-0.05201668,0.01273422,-0.00601484,-0.01992626,0.01452059,-0.09584942,-0.04668092,-0.03736454,0.03518178,0.0590027,0.02915157,-0.05305934,-0.01646612,-0.03258712,0.04647122,-0.03797578,0.05710255,-0.0157683,-0.00052765,0.0476344,0.24020647,0.03382215,0.00154894,-0.0099573,0.02290993,0.0402948,0.04008216,0.04541781,-0.01204863,-0.0052569,0.01163393,0.01619326,-0.05030348,0.04937127,-0.02925664,-0.00887107,0.00816201,0.00173222,-0.07935537,0.03028722,-0.05075843,-0.00828614,0.11078265,0.01596211,-0.0299906,-0.06368911,0.05676479,0.09509292,-0.07091901,-0.07875672,-0.02611301,-0.00214294,-0.0184144,0.00773231,-0.02662187,-0.02823006,0.01488562,-0.03615388,0.0263974,-0.02176473,0.06161401,0.00565677,0.02386567],"last_embed":{"hash":"1v9hcus","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"1v9hcus","at":1753511069493},"key":"doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#核心架構","lines":[7,17],"size":209,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#核心架構#主要目錄結構": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10172283,-0.02141118,-0.04842052,-0.00588112,0.01296622,0.02800722,0.0259267,0.01159225,0.04365703,-0.00651526,-0.006809,-0.08227912,0.01600574,0.02182556,0.01858402,-0.04181212,0.01271738,0.00469615,-0.02955861,0.05808459,0.08038978,-0.03877815,-0.02422063,-0.09165345,-0.00377461,0.00062893,-0.02630571,-0.04183402,-0.04613405,-0.16783339,-0.00133113,0.00764806,0.01198221,-0.02412728,-0.02905017,-0.02633937,-0.04587853,0.00055765,-0.04800747,0.02194137,0.01664586,-0.01662925,0.02489997,-0.01254208,0.01240203,-0.05960444,-0.00185153,-0.00435753,0.09673653,-0.00497583,-0.06612726,0.00688038,-0.00425549,0.02272553,0.01407567,0.04667252,0.0190924,0.07859167,0.00865604,0.00860237,0.01427601,0.01085059,-0.23034889,0.03020509,-0.00312342,-0.00918486,-0.01961164,-0.05541749,0.00812451,-0.03236323,0.04446013,0.06148101,-0.01086421,0.08377992,0.03437308,-0.00981627,-0.01486208,-0.06085184,-0.01011846,0.03266142,-0.04074616,-0.01063129,-0.05417098,-0.00151367,0.01921756,0.04272699,0.02411025,0.02912375,0.0557157,-0.05898276,-0.00353051,-0.01457216,0.03447448,0.06523135,-0.00222463,0.044551,0.00564938,0.00248131,-0.04449279,0.12686625,0.03241646,-0.02678278,-0.01901402,-0.00457568,0.06155177,-0.02037058,-0.02678254,-0.02907984,-0.01587344,0.01747573,0.03535091,-0.04196356,0.06048555,-0.05361718,-0.03835414,0.0356269,0.00736152,0.01614102,0.00037582,0.00175354,-0.02742317,0.01317554,0.00590686,-0.00187817,0.01114296,-0.01036729,0.0523363,0.04306955,0.05115117,0.05614037,0.02740635,0.04189618,-0.11843611,-0.04345033,0.01022074,-0.01465684,-0.0179807,-0.00883297,-0.02144338,-0.0248607,0.01741127,-0.04586835,0.05398563,-0.09582245,-0.04903523,0.03936363,-0.02595087,0.01185934,-0.01946724,-0.03000003,0.01008657,0.00035168,-0.00228864,-0.03648273,-0.01414393,-0.00001006,0.11777121,0.12269004,-0.01298415,0.01240014,0.01745274,-0.0542412,-0.03164731,0.16622876,0.00995117,-0.07211711,-0.02748794,0.02981246,0.02687019,-0.0655116,-0.00394345,-0.01032668,0.01929235,0.01741182,0.0988602,-0.0295238,0.01370923,-0.03545173,-0.04339307,0.03442795,-0.04220105,-0.04910303,-0.00901779,0.0127794,-0.0261893,-0.07280649,0.01853674,-0.06914619,0.02305799,0.0051208,-0.0480858,0.06604732,0.0041877,0.0162543,-0.02902725,-0.03876309,-0.04995406,-0.0330844,0.00100845,-0.02587249,0.06867244,0.07123458,0.01417936,0.03560444,-0.01639545,-0.00557681,-0.03285563,-0.04054619,-0.01192405,0.0254219,-0.00066078,0.02690683,0.05150694,0.06010456,-0.03094587,0.02568625,-0.0249502,-0.00372539,0.05553523,0.0282997,-0.0055379,0.04043878,-0.10481121,-0.25015518,-0.00220636,-0.07767196,-0.07180863,-0.01954718,-0.02143694,0.03219506,-0.06160614,0.08361132,0.01891951,0.15339632,0.00213521,-0.01363249,0.0206609,-0.01532232,0.00784232,0.0353888,-0.03736994,0.00278839,0.06045333,0.0432524,0.03318438,0.0330831,-0.02747997,0.04001581,-0.00988042,0.15189683,0.03155365,0.06004029,0.04098507,0.05087737,0.03500224,0.01744587,-0.15695319,0.06360865,0.02382432,-0.0650427,0.06446668,-0.03645186,-0.03285132,-0.07104556,0.02999705,0.0090297,-0.07899735,-0.00555179,-0.06547351,-0.01077662,-0.03469578,-0.01432061,0.0312031,0.02039692,0.02740194,0.01857855,0.05616294,0.04585596,-0.00368189,-0.05430561,-0.00977383,0.02476002,0.01474514,-0.02927244,-0.01723267,0.04214947,-0.02535526,0.00155656,0.00264807,-0.02216525,-0.03509203,0.02887518,-0.03245337,-0.01227234,0.11460155,-0.0055159,-0.00099028,0.02019912,0.0345795,0.01858483,-0.03800387,0.02338415,0.00332362,0.01708937,0.00363814,0.04432639,0.04445443,0.03789712,0.03739145,0.01810212,0.09176037,0.01805999,0.01850045,0.00636595,0.02609956,-0.02534311,0.04634756,0.02482419,0.02295994,-0.29963693,0.01521776,-0.05255325,0.01214567,-0.00556273,-0.01843481,0.01370898,-0.09511338,-0.04752473,-0.03597311,0.03255998,0.0564662,0.0279928,-0.05052104,-0.01807191,-0.03347486,0.04781817,-0.03751764,0.05724944,-0.01532341,-0.00159203,0.04830322,0.24119557,0.03514975,-0.00200382,-0.00871387,0.02302933,0.03870533,0.04067992,0.04502448,-0.01107235,-0.00549972,0.01071616,0.01742289,-0.05041913,0.05188505,-0.02823571,-0.00957006,0.00905851,0.00036098,-0.07789899,0.03166434,-0.05225458,-0.00923574,0.11347462,0.01616535,-0.02847426,-0.06150673,0.05507696,0.09513751,-0.06937584,-0.07726087,-0.02447282,-0.00063212,-0.01744308,0.00793182,-0.02462629,-0.0306136,0.01607663,-0.03782519,0.02636768,-0.02111862,0.06311665,0.00481915,0.02706493],"last_embed":{"hash":"1bzcurl","tokens":101}}},"text":null,"length":0,"last_read":{"hash":"1bzcurl","at":1753511069572},"key":"doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#核心架構#主要目錄結構","lines":[9,17],"size":200,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#核心功能模組": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09461389,-0.00454486,-0.03449265,-0.01753357,0.00102172,0.05982595,0.01339759,0.01901906,0.06455621,-0.003342,0.01630079,-0.07757674,0.03076808,0.04701465,0.0110698,-0.05316992,-0.00151275,-0.02227434,-0.05479107,0.01015576,0.10847104,-0.07752296,-0.01729306,-0.13006422,0.01730692,0.01365035,-0.00273324,-0.03076842,-0.03363252,-0.19509791,0.00841937,0.00285615,0.04027495,-0.04415327,-0.05218644,-0.00739141,-0.04859972,0.01723502,-0.05627043,0.0268501,0.00865488,-0.01276109,0.02928557,-0.01316366,0.02026401,-0.06001174,-0.01681445,-0.01679702,0.08309377,0.00306583,-0.054402,-0.02083294,-0.00571323,0.02939985,0.04755033,0.0691769,0.01133661,0.05486742,0.02189908,-0.00050733,-0.00343993,-0.0244698,-0.2276122,0.01197157,-0.00841251,-0.00794615,-0.0121234,-0.03667996,0.02789633,-0.00753476,-0.00293069,0.07232609,-0.00854344,0.04358068,0.03807579,-0.02241961,-0.01931141,-0.05452185,-0.02186996,0.01668563,-0.05759398,-0.01282335,-0.02167022,-0.0316931,0.03018067,0.03955596,0.01987757,0.02766708,0.02859987,-0.04189348,0.00600388,0.01268109,0.01010505,0.03544384,-0.02033978,0.03619289,0.01306185,0.01952615,-0.0531235,0.08864839,0.05405673,-0.00882249,-0.00123487,-0.01073139,0.07482027,-0.04184387,-0.04290952,-0.02542027,-0.02080394,-0.00489675,0.02526098,-0.02744399,0.05149451,-0.04077225,-0.05288937,0.03497992,0.05120067,0.00849603,-0.01025411,0.00909372,-0.04571662,0.02094411,0.01424574,-0.01961908,0.04949918,0.00179674,0.02670527,0.06362007,0.06203347,0.01434337,0.02364233,0.02366768,-0.11000731,-0.04059496,0.01630807,0.00308576,0.00471474,-0.01076054,-0.00507256,-0.01741896,-0.01197447,-0.0661223,0.06015926,-0.10356324,-0.05043451,0.04977609,-0.04048005,0.01070987,-0.02204545,0.0037371,-0.00903689,0.0058596,-0.00572054,-0.06128838,0.02616602,-0.01164725,0.10515531,0.1304961,0.00205812,-0.00863982,-0.00200499,-0.04375643,-0.03565942,0.18212219,0.04950158,-0.06538677,-0.0379479,0.04055076,0.03333024,-0.06685332,-0.02776057,-0.01262515,-0.00959005,-0.00558675,0.09844345,-0.04221243,-0.00425566,-0.0277135,-0.06025001,0.02361884,-0.0189711,-0.02255761,0.00093931,0.02623579,-0.00879563,-0.10146952,0.01272103,-0.04316864,-0.01900116,-0.03022257,-0.06155111,0.0641885,0.01492027,0.06818283,-0.03189582,-0.04904261,-0.0359872,-0.03065127,0.01720737,-0.05166373,0.09193996,0.0533378,0.01264198,0.0046083,0.01035113,0.02613277,-0.02340132,-0.03541497,0.0002127,0.07096352,0.00150283,0.01243001,0.03920548,0.02843714,-0.01755163,0.03903345,-0.02184051,-0.00413843,0.0254452,0.00926474,0.01820328,0.03236487,-0.08565521,-0.2410219,-0.03454365,-0.05813415,-0.07318564,-0.01592042,-0.01968348,0.06231806,-0.06827021,0.11555874,0.04492881,0.11836646,0.02126711,-0.03414586,0.01139932,-0.03400832,-0.03117389,0.01447507,0.00196507,-0.02242235,0.04708581,0.07355881,0.06460317,0.05035796,-0.00157068,0.0021713,-0.0159797,0.1513145,0.02578086,0.03589036,0.01281848,0.05885962,0.04780797,0.01745527,-0.06694788,0.06251104,0.04380585,-0.06795254,0.01086902,-0.04255493,-0.03565258,-0.06172427,0.03797294,-0.01493715,-0.08651488,-0.00795212,-0.06629156,-0.04243819,-0.03566395,-0.03762894,0.0193547,0.01437149,-0.01219864,0.02859627,0.02033491,0.07451605,-0.00317437,-0.06607591,-0.01346554,-0.00769826,0.02075253,-0.04500956,-0.00286557,0.02693581,0.00913121,-0.00199537,-0.0046554,-0.02126914,-0.00165135,-0.00872488,-0.0542533,-0.01646219,0.11549705,-0.00677174,0.02596683,0.03226041,0.0345636,0.01578668,-0.03697282,0.02082705,-0.00321981,0.02686344,0.00671743,0.000881,0.03208346,0.06763677,0.02924303,0.01998866,0.11241405,0.00903135,0.0039873,-0.04047944,0.00463095,-0.03948678,0.04466907,0.04163365,0.0055575,-0.30225378,0.004246,-0.01188024,0.02347718,0.00242635,-0.03773307,0.0306744,-0.07174052,-0.02512569,-0.01695131,0.04034252,0.07987441,0.04569958,-0.05676607,0.0117142,0.00397137,0.01143822,-0.02583928,0.0413965,-0.01888754,-0.00195498,0.02719217,0.21838196,0.03021382,-0.0039709,-0.00746078,0.04085671,0.04733115,0.02045037,0.03322944,-0.02842819,-0.00970882,0.0435879,0.00249632,-0.05908268,0.0536553,-0.02128566,0.02078942,0.02709052,-0.00484022,-0.07708806,0.04825992,-0.05229156,0.02370605,0.11557829,0.01455339,-0.00464677,-0.05979828,0.06976607,0.10078608,-0.047217,-0.0326609,-0.01058136,-0.00236997,-0.03599833,-0.00018483,-0.02114724,-0.02261073,0.03438904,-0.03266714,0.03631054,0.03085447,0.03594849,0.00015572,0.0337534],"last_embed":{"hash":"qsibfq","tokens":438}}},"text":null,"length":0,"last_read":{"hash":"qsibfq","at":1753511069622},"key":"doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#核心功能模組","lines":[18,48],"size":555,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#軟體工具整合": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09930951,0.01987969,-0.04273117,-0.03332894,0.03918443,0.01573575,-0.00950495,0.00981785,0.04166191,0.00170219,0.01054643,-0.06201599,0.01625944,-0.01416259,0.00717657,-0.04534545,0.02726029,0.00276925,-0.04555271,0.01817671,0.06979676,-0.0425662,-0.02959304,-0.13439246,0.0080094,0.00232329,0.01360278,-0.00201808,-0.03240277,-0.19840941,-0.00355728,-0.01851575,0.04234822,-0.06462573,-0.04791832,-0.00165594,-0.02050377,0.00842738,-0.03505579,0.02064061,0.01104762,-0.01017781,0.05084574,0.00602828,0.02759505,-0.03227832,0.01578535,-0.03230958,0.06408803,-0.02115782,-0.08448157,-0.03649951,0.03132108,0.01206224,0.03827842,0.06272101,0.00998779,0.08360204,0.03833609,-0.00798041,0.00222583,0.02314698,-0.212107,0.01204921,-0.01223308,-0.02777737,-0.00317449,-0.0664421,0.03193347,-0.0015467,0.01269563,0.05300068,-0.01264476,0.0672518,0.02673892,-0.02659313,0.01270935,-0.05601593,-0.01054543,0.00713966,-0.0300061,-0.03469931,-0.03163182,-0.01005282,0.01935183,0.04798988,0.02092938,0.04644896,0.01204072,-0.06828053,0.03287589,-0.00188177,0.01150061,0.05223003,-0.02781403,0.03493802,0.01058955,0.00006953,-0.05157214,0.10091791,0.0471947,-0.0010651,0.02839447,-0.00090248,0.05694569,-0.0408455,-0.05331853,-0.02216835,-0.03833437,-0.01071445,0.04567867,-0.0246199,0.0621713,-0.05577476,-0.01065902,0.02463226,0.00459684,0.02112084,-0.02030714,-0.00977045,-0.03474283,0.03624541,0.02947627,0.00524994,0.02408965,-0.00738928,0.0427455,0.07587025,0.07433724,0.01645312,0.02121427,0.0135472,-0.10078578,-0.04576546,0.04258649,0.01300877,-0.0158088,-0.0181319,-0.02855635,-0.01862243,-0.01364548,-0.06078203,0.04434661,-0.10137393,0.01239247,0.06276844,-0.05069763,0.01867311,-0.00932124,0.00268122,-0.02353519,-0.02428046,-0.00566422,-0.04525013,-0.02036047,0.00028473,0.11041585,0.13068414,-0.015609,-0.01485036,0.00024634,-0.01982166,-0.04787361,0.16430777,0.02046485,-0.0715453,-0.02548086,0.02264045,0.01218262,-0.06166408,-0.05132025,0.02115566,0.00397135,-0.00992538,0.10551334,-0.04518756,-0.01925454,-0.01669239,-0.06692631,-0.01646753,0.00722317,-0.00924243,-0.03836814,0.02302419,-0.0109759,-0.11783485,0.04975697,-0.02350612,-0.02536515,0.00675342,-0.07761711,0.05181646,0.01100599,0.01562847,-0.02753218,-0.01570824,-0.04563799,-0.02952343,0.06135561,-0.03715441,0.10507693,0.06551726,0.0138939,-0.00623221,0.0105652,0.03540842,-0.00446099,-0.0235767,-0.00983254,0.03714399,-0.02075557,0.03017309,0.0667386,0.06356574,-0.04228031,0.04078289,-0.04609693,0.01901743,0.01167885,-0.01146966,0.03619358,0.02478816,-0.08638966,-0.24337107,0.00864797,-0.05839333,-0.10790551,-0.01181621,-0.01469373,0.04020287,-0.04725275,0.10280946,0.0327546,0.12840372,0.018808,-0.01543862,0.01171652,-0.01763194,-0.01496636,-0.00741827,-0.02519465,-0.00907007,0.0319046,0.05439681,0.05337703,0.05019425,-0.00146567,0.03368704,-0.05053373,0.1305934,0.04045552,0.04302479,-0.00234084,0.05655297,0.02273111,0.03476084,-0.09584717,0.04724704,0.02788199,-0.06372984,0.00567833,-0.05335589,-0.04207306,-0.04734209,0.03254237,-0.05397534,-0.09100232,0.01337488,-0.0345569,-0.02937172,-0.02109846,-0.03545076,0.0227249,0.00088406,-0.01495014,0.01226173,0.05630198,0.0666936,-0.03965354,-0.06916513,-0.0027257,-0.01828214,0.01914633,-0.02881877,-0.01127525,0.03195694,-0.01180053,0.01780839,0.02021926,-0.03795453,-0.01504048,0.00125534,-0.02997272,-0.04007635,0.09624869,-0.02244124,0.03601071,-0.01993405,0.02513443,0.0129814,-0.0466429,0.01112171,0.01427317,0.03453633,0.01807267,0.00802515,0.05314138,0.08776516,0.0185512,0.00783873,0.07804542,0.01664699,0.00485806,-0.02866905,-0.00667886,-0.02619174,0.03385438,0.05690806,0.00938203,-0.30718136,-0.00449853,-0.039919,0.00445477,-0.0072933,-0.02685746,0.02427724,-0.08064702,0.002948,-0.0251302,0.04696059,0.07550081,0.0433236,-0.04115896,0.02650171,-0.03165407,0.03502293,0.02119228,0.04593308,-0.03898601,0.02802341,0.02750797,0.24587233,0.04823145,0.00116297,0.00113291,0.03032513,0.01217936,0.03617492,0.048134,-0.00331446,0.03689776,0.04401074,-0.01291219,-0.02761858,0.05585843,-0.01814263,0.0454527,0.00545353,0.00210451,-0.06806546,0.0282198,-0.07120083,0.03349019,0.1165621,0.00857463,0.01786367,-0.07345128,0.06088074,0.09026607,-0.04192375,-0.03896322,-0.02254308,-0.00141136,-0.04986939,0.01294531,-0.03106311,-0.00330723,0.02843696,-0.01418117,0.02728131,-0.01743219,0.0486463,0.01224366,0.02321876],"last_embed":{"hash":"1y4rh71","tokens":216}}},"text":null,"length":0,"last_read":{"hash":"1y4rh71","at":1753511069855},"key":"doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#軟體工具整合","lines":[49,64],"size":271,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#技術特色": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09864801,0.00408114,-0.04156322,-0.03141549,-0.0071599,0.05270135,0.01190656,-0.01015326,0.06616054,-0.02584062,0.02499754,-0.08672281,0.03374556,-0.00254517,0.02924304,-0.04206711,0.01967162,-0.00620444,-0.04532174,0.01590584,0.0799844,-0.05808784,-0.02771886,-0.11278617,-0.01456916,0.00978997,-0.01257216,-0.04097998,-0.03326657,-0.17383862,0.02079709,0.00930611,0.03457483,-0.03525961,-0.02300801,-0.04023015,-0.02677981,0.02512815,-0.05347459,0.03137985,0.00138263,-0.00251555,0.02630208,-0.01793123,0.03455283,-0.07713544,0.00538023,-0.02143999,0.09389065,0.00754776,-0.06447933,0.00067371,0.04232552,0.01721911,0.03943767,0.07303045,-0.00673599,0.07239802,0.04221249,0.01707555,0.00779986,0.01087553,-0.21392266,0.01478162,-0.00359791,-0.00452411,0.0014355,-0.05803248,0.02470628,0.01201962,0.01451062,0.04883224,-0.01177476,0.06106758,0.02634331,-0.03247079,-0.03372809,-0.05000658,-0.02669178,0.01661198,-0.0636591,-0.00508292,-0.00604723,-0.01363789,-0.00986117,0.04257995,0.03225339,0.06236992,0.0362845,-0.0772589,0.01457323,0.00538469,-0.00949869,0.04655351,-0.01655033,0.04430981,0.02701516,0.0239709,-0.04569709,0.09967269,0.05315786,-0.01917035,-0.01159708,-0.02124197,0.05272356,-0.0114179,-0.04130786,-0.01946789,-0.03761043,0.02468199,0.03795684,-0.01492842,0.05107921,-0.01873511,-0.03131064,0.01564557,0.01700291,-0.00176475,-0.03139761,0.00987989,-0.02152685,0.01034488,0.01809797,-0.03815307,0.01532992,0.00925968,0.03140918,0.05361228,0.06610756,0.02666695,0.00313026,0.00460919,-0.12410071,-0.01978863,0.03565505,-0.0006058,0.00265834,0.00787228,0.0135948,-0.04120451,-0.02251961,-0.07234713,0.05623983,-0.09143557,-0.04978091,0.07823172,-0.04941885,0.01029389,-0.00075878,-0.04503831,0.01176766,0.02702511,-0.02677225,-0.04623606,-0.01430237,0.00024562,0.10328311,0.14880997,0.03246162,-0.01846704,0.00927786,-0.03174924,-0.02138863,0.12871285,0.03374648,-0.08860642,-0.03060071,0.03186581,0.02207401,-0.0416052,-0.05266939,-0.01881218,-0.02808817,0.01921666,0.1031751,-0.03032639,-0.02976095,0.00533865,-0.04926737,0.0233526,-0.02240081,-0.00816299,-0.03566794,0.017653,-0.00467852,-0.08456058,0.04390273,-0.04514787,0.01542579,0.00225295,-0.04685269,0.05940168,0.01259544,0.04180882,0.0027454,-0.04072933,-0.06997661,-0.01148591,0.03793157,-0.04992748,0.12676719,0.05542392,-0.01175866,-0.00489031,-0.00305493,0.01994929,-0.0232799,-0.04286727,-0.00333532,0.04529857,0.02163011,0.01880612,0.04285841,0.04717005,0.00052748,0.05819088,-0.02333006,-0.00112454,0.03042925,0.01588896,0.02390335,0.02123188,-0.12030019,-0.23998085,-0.00234719,-0.07765204,-0.09746595,-0.05220231,-0.00469378,0.02483287,-0.04802746,0.10254456,0.03385025,0.12617935,-0.00305108,-0.01875371,0.00558403,-0.04338249,0.00190096,0.00175745,-0.00713088,-0.00114817,0.02217389,0.06500097,0.07785239,0.0053212,0.00399579,0.04109612,-0.04079553,0.15154451,-0.01639661,0.0422147,0.02219539,0.08433218,0.06803264,0.01490196,-0.08812964,0.03574195,0.03423109,-0.08889476,0.0182371,-0.07181511,-0.02313146,-0.05407252,0.00946686,-0.00578786,-0.10904772,-0.0020849,-0.03935567,0.00346836,-0.02545003,-0.03976688,0.02079106,-0.0251487,-0.00277591,0.03493413,0.03190887,0.05045441,-0.00992428,-0.04655902,-0.01931602,-0.02126409,0.01123946,-0.02361601,-0.03790336,0.02231336,-0.03913327,-0.03187159,0.01657363,-0.01676261,-0.02933293,0.01052551,-0.03095156,-0.01036509,0.0900441,-0.01434106,0.01191196,0.00490843,0.02557719,0.01187498,-0.00858199,0.02336737,-0.01139938,0.0064683,0.0206308,0.03006751,0.05217642,0.07000492,0.02644426,0.00627649,0.09782836,0.01976073,0.01152618,-0.0199964,0.00276969,-0.04512989,0.01357861,0.022007,-0.00465261,-0.3012611,-0.00117949,-0.04360673,0.04553632,-0.00915274,-0.04605211,0.02239263,-0.04429791,-0.03173779,-0.03398291,0.00775251,0.06198722,0.03463887,-0.05259492,0.01832501,-0.02998506,0.01999449,-0.02946998,0.05169618,-0.00221968,0.03412417,-0.00570763,0.23058182,0.05078128,0.02132902,0.0010551,0.04365732,0.02376782,0.02591701,0.03552751,-0.00337565,-0.00663091,0.06973024,-0.01501413,-0.03440392,0.03779526,-0.04021119,0.0483142,0.03001257,0.04354733,-0.0513497,0.02393833,-0.08416537,-0.00251978,0.12335145,0.05243243,0.0048968,-0.05715371,0.06135512,0.10608535,-0.06785048,-0.05375274,-0.03125894,-0.01080386,-0.0203852,0.00744616,-0.040514,-0.04891553,-0.00679971,0.00326864,0.05364373,0.01897264,0.04590559,0.01813411,0.0333985],"last_embed":{"hash":"1hjjflg","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"1hjjflg","at":1753511069936},"key":"doc/saliusystem00072_codebase.md#Saliu Lottery System 代碼庫描述#技術特色","lines":[65,81],"size":200,"outlinks":[],"class_name":"SmartBlock"},
