#!/usr/bin/env julia

# 階段 5 最終測試
println("開始階段 5 最終測試...")

try
    using Dates
    include("src/WonderGridEngine.jl")
    using .WonderGridEngine
    
    println("✓ WonderGridEngine 模組載入成功")
    
    # 創建豐富的測試數據
    test_drawings = [
        WonderGridEngine.Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
        WonderGridEngine.Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
        WonderGridEngine.Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47]),
        WonderGridEngine.Drawing(4, :Lotto6_49, Date(2024, 1, 4), [4, 10, 17, 30, 37, 48]),
        WonderGridEngine.Drawing(5, :Lotto6_49, Date(2024, 1, 5), [1, 11, 18, 31, 38, 49]),
        WonderGridEngine.Drawing(6, :<PERSON><PERSON>6_49, Date(2024, 1, 6), [5, 12, 19, 32, 39, 40]),
        WonderGridEngine.Drawing(7, :Lotto6_49, Date(2024, 1, 7), [6, 13, 20, 33, 41, 42]),
        <PERSON>GridEngine.Drawing(8, :Lotto6_49, Date(2024, 1, 8), [7, 14, 21, 34, 43, 44]),
        WonderGridEngine.Drawing(9, :Lotto6_49, Date(2024, 1, 9), [8, 15, 22, 35, 45, 46]),
        WonderGridEngine.Drawing(10, :Lotto6_49, Date(2024, 1, 10), [1, 9, 16, 23, 36, 47]),
        WonderGridEngine.Drawing(11, :Lotto6_49, Date(2024, 1, 11), [2, 10, 17, 24, 37, 48]),
        WonderGridEngine.Drawing(12, :Lotto6_49, Date(2024, 1, 12), [3, 11, 18, 25, 38, 49]),
        WonderGridEngine.Drawing(13, :Lotto6_49, Date(2024, 1, 13), [4, 12, 19, 26, 39, 40]),
        WonderGridEngine.Drawing(14, :Lotto6_49, Date(2024, 1, 14), [5, 13, 20, 27, 41, 42]),
        WonderGridEngine.Drawing(15, :Lotto6_49, Date(2024, 1, 15), [6, 14, 21, 28, 43, 44])
    ]
    
    println("✓ 測試數據創建成功 ($(length(test_drawings)) 期)")
    
    # 測試不同的配置
    configs = [
        WonderGridEngine.WonderGridConfig(
            analysis_range = 50,
            top_pair_percentage = 0.25,
            key_number_strategy = :ffg,
            combination_limit = 50,
            enable_purge = true,
            enable_lie = false,
            game_type = :Lotto6_49
        ),
        WonderGridEngine.WonderGridConfig(
            analysis_range = 30,
            top_pair_percentage = 0.3,
            key_number_strategy = :skip,
            combination_limit = 30,
            enable_purge = false,
            enable_lie = true,
            game_type = :Lotto6_49
        ),
        WonderGridEngine.WonderGridConfig(
            analysis_range = 40,
            top_pair_percentage = 0.2,
            key_number_strategy = :frequency,
            combination_limit = 40,
            enable_purge = true,
            enable_lie = true,
            game_type = :Lotto6_49
        )
    ]
    
    println("✓ 測試配置創建成功 ($(length(configs)) 個配置)")
    
    # 測試每個配置
    for (i, config) in enumerate(configs)
        println("\n測試配置 $i (策略: $(config.key_number_strategy))...")
        
        try
            result = WonderGridEngine.execute_wonder_grid_strategy(test_drawings, config)
            
            println("  ✓ 策略執行成功")
            println("    - 關鍵號碼: $(result.key_number)")
            println("    - 頂級配對數量: $(length(result.top_pairs))")
            println("    - 生成組合數量: $(length(result.combinations))")
            println("    - 執行時間: $(round(result.generation_time, digits=3)) 秒")
            
            # 檢查效率指標
            if !isempty(result.efficiency_metrics)
                hit_rate = result.efficiency_metrics["hit_rate"] * 100
                coverage_rate = result.efficiency_metrics["coverage_rate"] * 100
                println("    - 中獎率: $(round(hit_rate, digits=2))%")
                println("    - 覆蓋率: $(round(coverage_rate, digits=2))%")
            end
            
        catch e
            println("  ✗ 配置 $i 測試失敗: $e")
        end
    end
    
    # 測試關鍵號碼選擇的不同策略
    println("\n測試關鍵號碼選擇策略:")
    strategies = [:ffg, :skip, :frequency]
    for strategy in strategies
        try
            key_result = WonderGridEngine.select_key_number(test_drawings, strategy)
            println("  ✓ $strategy 策略: 號碼 $(key_result.number), 評分 $(round(key_result.score, digits=3)), 信心度 $(round(key_result.confidence, digits=3))")
        catch e
            println("  ✗ $strategy 策略失敗: $e")
        end
    end
    
    # 測試配對統計計算
    println("\n測試配對統計計算...")
    config = configs[1]
    pair_stats = WonderGridEngine.calculate_advanced_pair_statistics(test_drawings, config)
    println("✓ 配對統計計算成功，找到 $(length(pair_stats)) 個配對")
    
    # 顯示前 5 個高頻配對
    sorted_pairs = sort(collect(pair_stats), by = x -> x[2].frequency, rev = true)
    println("  前 5 個高頻配對:")
    for i in 1:min(5, length(sorted_pairs))
        pair, stats = sorted_pairs[i]
        println("    $(pair): 頻率=$(stats.frequency), 平均跳躍=$(round(stats.avg_skip, digits=2)), 效率評級=$(round(stats.efficiency_rating, digits=3))")
    end
    
    # 測試組合生成的不同方法
    println("\n測試組合生成方法...")
    key_number = 1
    game_config = WonderGridEngine.GAME_CONFIGURATIONS[:Lotto6_49]
    
    # 測試隨機組合生成
    try
        random_combo = WonderGridEngine.generate_random_combination(game_config.max_number, game_config.numbers_per_draw)
        println("✓ 隨機組合生成成功: $random_combo")
    catch e
        println("✗ 隨機組合生成失敗: $e")
    end
    
    # 測試跳躍值計算
    try
        skip_values = WonderGridEngine.calculate_skip_values(test_drawings, key_number)
        println("✓ 跳躍值計算成功，號碼 $key_number 的跳躍值: $skip_values")
    catch e
        println("✗ 跳躍值計算失敗: $e")
    end
    
    println("\n✓ 階段 5 所有功能測試完成！")
    println("  - Wonder Grid 策略執行: ✓")
    println("  - 關鍵號碼選擇: ✓")
    println("  - 配對統計分析: ✓")
    println("  - 組合生成: ✓")
    println("  - 效率指標計算: ✓")
    println("  - 多種策略支持: ✓")
    
catch e
    println("✗ 測試失敗: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

println("\n階段 5 最終測試完成")
