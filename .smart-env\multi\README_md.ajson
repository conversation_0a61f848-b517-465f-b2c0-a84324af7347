"smart_sources:README.md": {"path":"README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04094571,-0.03634026,0.03692289,0.01530433,0.01977653,0.0265966,-0.06957128,0.03960929,-0.05035871,0.06469634,0.0301955,-0.11546803,0.02829677,0.04708464,-0.0535141,0.0366386,-0.02438013,0.05228173,-0.02037777,0.00440033,0.06781273,-0.07855374,0.02263333,-0.02853173,0.01681364,0.0736339,-0.02369258,-0.10871693,-0.0155515,-0.23171674,0.01345611,0.02665696,0.02614049,-0.06443381,-0.01797001,-0.0011254,-0.01413303,0.0221697,-0.03141654,0.0317874,0.04996919,0.05821059,-0.03459601,-0.04318331,0.01831686,-0.07017694,-0.0152594,-0.00752771,0.0127858,-0.03849679,0.0187968,-0.07904958,0.05860966,0.00589548,0.00685521,0.07567965,0.06090778,0.02482332,0.08422788,-0.01199114,0.05925201,0.05292943,-0.19616514,0.09927557,0.00815621,0.03668592,-0.03355712,-0.07817093,0.10966735,-0.01196468,-0.02330015,0.02079173,-0.00694819,-0.02946168,-0.00001569,-0.05296768,0.04661028,-0.03273099,-0.03572289,-0.01948818,-0.03095133,0.08851627,-0.01069829,0.026726,0.00137342,-0.02643056,0.00711609,-0.01932813,0.04800774,0.03881826,-0.05194907,-0.05568943,-0.02901819,0.03644952,-0.04521344,-0.03212266,0.04146259,0.00312994,0.04068984,0.12604111,-0.04014443,-0.02353628,0.08373216,0.00817711,0.04645762,-0.07685778,-0.03228141,-0.0435783,-0.01419926,0.05562857,-0.02618451,0.01618124,0.01088067,0.00094048,0.00400604,0.02312721,-0.01645513,0.05377035,-0.00105367,-0.01281499,-0.0349286,0.05916839,0.0615867,-0.02597489,0.0087176,-0.01320181,-0.00282943,0.04910054,0.01887525,0.02408069,0.04772921,0.02510127,-0.02895905,-0.00680873,-0.00825549,0.03622592,0.06329204,-0.05890108,0.01339507,-0.00229838,0.00681049,0.01278478,0.04897621,-0.02869161,-0.00671988,0.09694029,-0.05450122,0.05749565,-0.01749527,-0.13618617,0.0277566,0.05694627,-0.07998334,-0.01407255,0.04648365,-0.03527876,-0.01158107,0.06801933,-0.04612166,0.07286707,-0.01612834,-0.07549134,-0.03873581,0.07606485,-0.02297303,-0.05267216,-0.0231299,0.02186937,-0.01471929,-0.02376756,0.01529051,0.02866985,-0.03032153,0.0163545,0.04492879,0.0104773,-0.12932211,-0.02482409,0.0433258,0.03760701,0.02222939,-0.06911594,-0.01074007,-0.01781737,0.03983812,-0.04486214,-0.00565995,-0.01546045,-0.03334585,0.04423168,-0.03770626,0.01250536,-0.06989579,0.06324944,0.00752471,-0.01178679,-0.03285124,0.02066738,0.00629778,-0.03518692,0.02597699,-0.03140165,0.00389552,0.03533646,-0.02935161,-0.01011997,0.03439086,-0.00940812,0.00391956,0.07569194,-0.07753325,-0.04998195,0.04709292,-0.02997024,-0.06081116,-0.04242914,0.03557294,0.04913197,-0.13264576,0.07003368,-0.03791782,-0.05635275,-0.04985829,-0.2155617,0.00813782,-0.01215602,0.00876177,0.04220232,-0.05999691,-0.01570262,-0.01242666,0.00900278,0.07346661,0.1225982,0.01775548,0.00266934,-0.00781585,-0.05892572,-0.016009,-0.05817889,0.00262934,-0.09515093,-0.04608829,0.0218668,0.05921317,-0.03553871,-0.10677424,-0.01064116,-0.04149291,0.15530711,-0.01838766,0.0943524,0.04957394,0.00047547,-0.01174949,-0.02636002,-0.11227589,-0.01880353,0.03905169,0.01339984,0.04852701,-0.00964835,0.00948211,-0.03500231,0.04888376,0.048901,-0.09750721,-0.00139893,0.00194204,-0.03899616,0.0537927,-0.01936411,-0.0013088,0.03245511,-0.05340575,-0.03796755,-0.00763276,0.01297237,0.0177587,-0.07922032,-0.02931391,-0.02023738,0.00000583,0.01997228,0.02390946,0.03487824,-0.05645921,0.1053645,0.02737828,0.01752229,-0.05816353,0.06734186,-0.02332589,0.01971555,0.11039606,-0.0124616,0.03889431,0.07939997,0.02883176,0.03889721,0.00127649,0.00631692,-0.03590453,0.00085215,-0.03461154,-0.01060803,0.05813209,0.06196684,0.06766421,-0.00745895,-0.0001484,-0.00439873,-0.0306805,0.04491915,0.06515499,-0.01739364,-0.09866868,0.0093423,0.06303148,-0.2134067,-0.02733888,0.01260389,0.01105701,0.00332334,-0.00457265,0.06634083,-0.05226625,0.05716808,-0.06853718,-0.04297731,-0.02278523,-0.00909161,-0.01117939,0.01209744,0.07522549,0.04135188,-0.02139364,0.06583724,0.01757967,-0.00344425,0.02979769,0.17381456,-0.04263544,-0.01079246,-0.03101985,-0.02623928,0.00362234,0.02505312,0.02443322,-0.0510235,0.02053164,0.07896066,-0.01776598,0.03540046,0.07580444,0.03138215,0.01312353,0.02633549,-0.02301622,-0.02512244,-0.01241784,0.01321052,0.03815851,0.06738719,0.03063022,0.02371621,-0.00061333,-0.01860172,0.03558314,-0.0693355,0.01833453,0.03090724,0.01514793,0.01126238,0.03099236,0.00066018,0.00893376,-0.02774294,0.04778679,-0.02709389,-0.03949616,0.003448,-0.00930516,-0.05672598],"last_embed":{"hash":"f5042b1f32b949b416f8f4890d7fdbb6a61b72577cd5dbdeebff9c389761ee39","tokens":463}}},"last_read":{"hash":"1skd2oq","at":1753230880448},"class_name":"SmartSource2","outlinks":[{"title":"Julia 官方網站","target":"https://julialang.org/downloads/","line":22}],"blocks":{"#SaliuSystem0001 - 彩票分析與組合生成系統":[1,215],"#SaliuSystem0001 - 彩票分析與組合生成系統#專案簡介":[3,6],"#SaliuSystem0001 - 彩票分析與組合生成系統#專案簡介#{1}":[5,6],"#SaliuSystem0001 - 彩票分析與組合生成系統#功能特色":[7,18],"#SaliuSystem0001 - 彩票分析與組合生成系統#功能特色#{1}":[9,9],"#SaliuSystem0001 - 彩票分析與組合生成系統#功能特色#{2}":[10,10],"#SaliuSystem0001 - 彩票分析與組合生成系統#功能特色#{3}":[11,15],"#SaliuSystem0001 - 彩票分析與組合生成系統#功能特色#{4}":[16,16],"#SaliuSystem0001 - 彩票分析與組合生成系統#功能特色#{5}":[17,18],"#SaliuSystem0001 - 彩票分析與組合生成系統#安裝":[19,45],"#SaliuSystem0001 - 彩票分析與組合生成系統#安裝#{1}":[21,21],"#SaliuSystem0001 - 彩票分析與組合生成系統#安裝#{2}":[22,23],"#SaliuSystem0001 - 彩票分析與組合生成系統#安裝#{3}":[24,29],"#SaliuSystem0001 - 彩票分析與組合生成系統#安裝#{4}":[25,29],"#SaliuSystem0001 - 彩票分析與組合生成系統#安裝#{5}":[30,30],"#SaliuSystem0001 - 彩票分析與組合生成系統#安裝#{6}":[31,45],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法":[46,166],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用":[48,144],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用#{1}":[50,59],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用#範例 1：載入數據並進行基本分析":[60,81],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用#範例 1：載入數據並進行基本分析#{1}":[62,81],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用#範例 2：使用馬可夫鏈生成熱門組合":[82,111],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用#範例 2：使用馬可夫鏈生成熱門組合#{1}":[84,111],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用#範例 3：應用過濾器和 LIE 排除來精煉組合":[112,144],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#1. 在 Julia REPL 中互動式使用#範例 3：應用過濾器和 LIE 排除來精煉組合#{1}":[114,144],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#2. 作為命令列工具使用":[145,166],"#SaliuSystem0001 - 彩票分析與組合生成系統#使用方法#2. 作為命令列工具使用#{1}":[147,166],"#SaliuSystem0001 - 彩票分析與組合生成系統#專案結構":[167,207],"#SaliuSystem0001 - 彩票分析與組合生成系統#專案結構#{1}":[169,207],"#SaliuSystem0001 - 彩票分析與組合生成系統#貢獻":[208,211],"#SaliuSystem0001 - 彩票分析與組合生成系統#貢獻#{1}":[210,211],"#SaliuSystem0001 - 彩票分析與組合生成系統#許可":[212,215],"#SaliuSystem0001 - 彩票分析與組合生成系統#許可#{1}":[214,215]},"last_import":{"mtime":1753173877682,"size":8904,"at":1753230880656,"hash":"1skd2oq"},"key":"README.md"},
"smart_blocks:README.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04185089,-0.0402801,0.02895116,0.02802875,0.04175759,0.02426351,-0.11127362,0.03811745,-0.05296132,0.06185246,0.03552052,-0.10526916,0.01456456,0.04156448,-0.03942116,0.03953075,-0.0164963,0.0464187,0.00797707,0.00054552,0.04342426,-0.06010935,0.02741543,-0.03454723,0.00724163,0.06136239,-0.01953456,-0.1101842,-0.02507746,-0.23787971,0.00604008,0.01561922,0.03943774,-0.05581112,-0.00652341,0.02284474,-0.01278768,0.02750511,-0.0431199,0.02507466,0.04105397,0.06183302,-0.02888113,-0.01923313,-0.00744185,-0.05798573,-0.00020978,-0.02708821,0.00300901,-0.04426946,0.02120427,-0.08324574,0.06970119,0.00601792,-0.00349853,0.07957955,0.03612772,0.03297203,0.05672389,-0.01503858,0.04811176,0.04420633,-0.18718044,0.10240893,-0.02210958,0.03778186,-0.05806481,-0.06986114,0.11002391,-0.01786659,-0.01358742,0.03377465,-0.02371437,-0.03210779,0.00640443,-0.02358417,0.04664818,-0.01832686,-0.03851407,-0.00333996,-0.05248444,0.07330178,0.00039549,0.01461648,0.00703053,-0.03125181,0.00348531,-0.02616793,0.04366864,0.05348827,-0.04881399,-0.04972534,-0.00066674,0.04075542,-0.05975522,-0.03022998,0.05680288,-0.00765461,0.0163002,0.13737883,-0.03831789,-0.01651943,0.06784203,-0.00168759,0.04793433,-0.06632444,-0.00663092,-0.03062914,-0.01552209,0.05022941,-0.04074663,-0.00026115,0.01759293,-0.0207981,0.02237776,0.0339197,-0.02338355,0.04374367,-0.00550384,0.00808102,-0.03325191,0.06159489,0.06007433,-0.01175621,0.01352095,-0.00376699,-0.00514821,0.04965276,0.03160825,0.03964009,0.03698887,0.04728382,-0.0257042,-0.02943632,0.0071314,0.02496073,0.04657803,-0.0701299,0.0041466,-0.00459567,-0.01280612,0.03535353,0.03768659,-0.05812171,-0.00895349,0.07377404,-0.04788604,0.05366795,-0.02027882,-0.11154126,0.02822412,0.05916077,-0.08066396,0.00132196,0.04747055,-0.02971335,0.00170839,0.0676459,-0.04775931,0.08577409,-0.01751101,-0.09096858,-0.02995132,0.10328982,-0.02928551,-0.06441725,-0.04682645,0.00677577,-0.02389906,-0.0277221,0.02730182,0.037019,-0.02289326,0.0273736,0.04273329,0.00619658,-0.14174657,-0.02953164,0.04908436,0.01814652,0.01536349,-0.05372737,-0.03061642,0.01360535,0.03335516,-0.04098611,-0.00406517,-0.03601916,-0.02756981,0.03944999,-0.03605522,0.00730883,-0.07043881,0.06825854,-0.01021603,-0.01974414,-0.03400428,0.00825716,0.02063981,-0.04305553,0.03350586,-0.01886741,-0.01780629,0.01375667,-0.04475015,-0.01380947,0.00461328,-0.01133308,0.0056185,0.05548106,-0.07767721,-0.05539602,0.06006082,0.00185921,-0.05026611,-0.04088892,0.01846006,0.06250151,-0.11833209,0.05328128,-0.01662794,-0.04459079,-0.05849425,-0.22926126,0.01023893,-0.01603636,0.00120604,0.0543196,-0.05743974,-0.0054522,-0.03253007,0.00863009,0.09743626,0.1126757,0.0283964,-0.00602556,-0.00950519,-0.06731535,-0.00864485,-0.05270503,-0.0076999,-0.10600516,-0.03791342,0.03321987,0.05303255,-0.01758133,-0.1214896,0.01043106,-0.02629042,0.16232003,-0.0080886,0.106482,0.04520056,-0.0027509,-0.01613273,-0.00678152,-0.12174832,0.00017842,0.05162793,0.02711077,0.04615617,0.02202216,0.00793193,-0.0138212,0.04067032,0.03793597,-0.10828677,0.01000376,0.00222977,-0.0436404,0.0644384,-0.02869914,0.00196072,0.03282207,-0.05100621,-0.02109312,0.02044202,0.01360927,-0.00323889,-0.08862572,-0.0242261,-0.02969237,0.00452254,0.04382826,0.02625761,0.03335394,-0.07199908,0.1109659,0.02196311,0.01427932,-0.05995306,0.0733638,-0.02602246,-0.0030055,0.08440637,-0.01301068,0.05148884,0.06847347,0.04025408,0.02416466,-0.01826884,-0.00859186,-0.02618046,-0.00187414,-0.03923074,0.00615077,0.05220603,0.0386093,0.07982166,-0.01661695,0.00901004,-0.00066832,-0.0148629,0.04708689,0.06015571,-0.01857057,-0.07768226,0.0166406,0.05961913,-0.21439674,0.01448953,0.03422927,0.01796586,0.00618019,0.00547158,0.0496096,-0.06657278,0.05547423,-0.08153319,-0.03612388,-0.005235,-0.01378128,-0.00914403,0.0155548,0.04579225,0.03809972,-0.01392313,0.06073567,0.01021825,0.0058782,0.01729937,0.17583998,-0.03724508,-0.01290708,-0.02463291,-0.02585671,0.01305947,0.04742338,0.01840609,-0.05160552,0.03852148,0.08669968,-0.02011072,0.02604185,0.06214248,0.03483333,0.02452762,0.01851038,-0.04620585,-0.02764409,0.01086475,-0.01464283,0.04322034,0.05019096,-0.00033682,0.03247288,-0.00000429,-0.02418046,0.06137479,-0.06330516,0.00837713,0.02502519,-0.01048659,0.02213806,0.01004426,0.02554716,-0.00010867,-0.03243426,0.04113028,-0.02595484,-0.02797544,0.00738615,0.0070963,-0.05335544],"last_embed":{"hash":"950bdd6121e71d99cd18017e340fcadb2941ddd7e7ead531a5be38134b884e41","tokens":300}}},"text":null,"length":0,"last_read":{"hash":"950bdd6121e71d99cd18017e340fcadb2941ddd7e7ead531a5be38134b884e41","at":1745995300849},"key":"README.md#","lines":[3,16],"size":869,"outlinks":[{"title":"![CII Best Practices","target":"https://bestpractices.coreinfrastructure.org/projects/2997/badge","line":1},{"title":"AliceVision","target":"https://github.com/alicevision/AliceVision","line":3},{"title":"AliceVision website","target":"http://alicevision.github.io","line":5},{"title":"results of the pipeline on sketchfab","target":"http://sketchfab.com/AliceVision","line":7},{"title":"![Build status","target":"https://ci.appveyor.com/api/projects/status/25sd7lfr3v0rnvni/branch/develop?svg=true","line":10},{"title":"![Build Status","target":"https://travis-ci.org/alicevision/meshroom.svg?branch=develop","line":11}],"class_name":"SmartBlock"},
"smart_blocks:README.md##{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08242213,-0.02070063,0.0246245,0.00744103,-0.01077744,0.00233352,-0.10126107,0.02392186,-0.01820791,0.05642887,0.02957289,-0.10360704,-0.02298265,0.03351569,-0.01155483,0.04852969,-0.00880809,-0.01894892,-0.00946662,0.02416743,0.04818589,-0.01531752,0.07125614,-0.01259091,-0.01871753,0.0222602,0.00128589,-0.07607362,-0.04686003,-0.18307038,-0.07578401,0.02742411,0.03561265,0.01108713,0.03885217,-0.00266816,-0.00382439,0.04069982,-0.05318578,0.01305023,0.02843817,0.01768159,-0.00574731,0.0525509,-0.0181209,-0.0460224,-0.03338362,0.0041509,0.02442038,-0.04824605,0.00731362,-0.01642552,0.05129091,0.01189157,-0.02242021,0.11273476,0.05013881,0.04469009,0.07022963,-0.00054699,0.05154087,-0.00415258,-0.2164713,0.08825028,-0.02411722,0.02496332,-0.06548405,-0.10604089,0.08401497,-0.00541972,0.00681097,0.0072437,-0.05238272,0.01375606,0.0644675,-0.03760013,0.05471935,-0.00305385,0.00375843,-0.0314187,-0.03814596,0.06209514,-0.01858244,-0.0005965,0.02207297,-0.00328677,0.0379323,0.0382703,0.09295232,0.00424172,-0.05127485,-0.11354652,-0.00189097,0.07649509,-0.07139803,-0.02035134,0.00898406,-0.00731669,-0.01626129,0.13815668,-0.04402834,-0.00404505,0.00027024,0.00789945,0.0502203,-0.05007917,0.00428241,0.0076687,-0.02915476,0.03091843,-0.03672458,-0.0056198,0.02136934,-0.04124768,0.05289172,0.02414275,0.00037651,0.06046389,-0.03328995,0.04188463,-0.00471556,0.01512669,0.08244088,-0.03675945,-0.0137479,0.00047627,0.01692089,0.04533285,-0.01594688,0.0454458,0.03658032,0.02658275,-0.06774281,-0.02625708,0.00154726,0.00650583,0.01776414,-0.06766032,0.00486874,-0.02976114,-0.02237988,0.00620728,0.01187501,-0.09350861,-0.01992752,0.06600513,-0.05969579,0.05761304,-0.04616458,-0.09647608,0.02981776,0.03297026,-0.07371096,-0.0393103,0.04367447,-0.00102591,0.04403314,0.06143213,0.01296626,0.05038296,0.00827905,-0.09430511,-0.09048267,0.0734942,-0.03169259,-0.04100784,-0.02658631,0.04603038,-0.01315535,-0.00620029,0.00709854,0.03314177,-0.01034737,0.04228886,0.09047564,0.01865001,-0.07855919,0.00719385,0.02201173,-0.00109848,0.00954551,-0.05320409,-0.04281028,0.01189435,0.05580277,-0.00870469,0.05084678,-0.07411164,-0.01521678,0.01172895,-0.04574625,-0.02454482,-0.05740348,0.01437441,-0.02148985,-0.0076401,-0.03424266,-0.00363517,0.05928422,-0.0620488,0.05726961,-0.00420552,-0.02948226,0.03339987,-0.0610678,0.02829538,-0.03233551,-0.00362976,0.05416853,0.01647837,-0.03880847,-0.05008876,0.05144296,0.01969965,-0.07316532,-0.058821,0.00356464,0.07422022,-0.04981165,0.0219052,-0.02326202,-0.02966728,-0.08004574,-0.27390078,-0.0028536,-0.01788745,-0.03100777,-0.02195623,-0.02918512,-0.00669295,-0.00326403,-0.03203022,0.07699755,0.12292766,0.03575478,-0.01107676,-0.01458513,-0.02171353,0.02312297,-0.05054616,-0.02790088,-0.09554613,-0.01969064,0.02507192,-0.00683534,-0.05350115,-0.08313607,0.04465273,-0.05725178,0.12751347,0.07139766,0.08978877,0.03612444,0.04897343,0.0253958,0.00730393,-0.14212394,-0.00210121,0.10131624,-0.02023906,0.07905876,-0.00738393,0.02336475,-0.01957392,0.04837709,-0.0181439,-0.09538059,0.01941341,-0.02111734,0.03487633,0.04412917,-0.00671462,-0.00978639,0.01853943,-0.02278365,0.02864219,0.06325656,0.0131975,-0.01956375,-0.06261714,0.01884849,-0.00095551,0.00958054,0.02892022,0.03175375,0.05631771,-0.03562725,0.03548939,0.02800287,-0.03771359,-0.04892069,0.07709753,0.00141159,-0.01969284,0.01682084,0.01259935,0.03760217,0.03469984,0.02165929,-0.01285887,-0.03042951,0.02322138,-0.00866494,-0.02773184,-0.02055752,0.049743,0.03927289,0.04129587,0.05281965,-0.05703064,0.02227592,0.04394777,-0.03114683,-0.00511496,0.0567595,0.00570133,-0.08926319,0.06171485,0.03196384,-0.22673933,-0.00704314,0.06447581,-0.00691275,-0.04169814,-0.0087214,0.03597439,-0.04558491,-0.03575354,-0.02639593,-0.02400466,0.00244566,-0.02679909,-0.03582972,0.00001465,0.03937371,0.01364325,0.01642699,0.05813869,-0.00742553,0.02693683,0.00798688,0.23168771,0.02839039,0.0214751,0.0281071,-0.03749566,0.02799744,0.04588864,0.06599672,-0.02826541,0.00117052,0.09428218,-0.04965656,0.02119441,0.052988,0.04004274,0.06706162,-0.0023755,-0.0180417,-0.0489581,0.05866398,-0.06411642,0.01259938,0.05105892,-0.05635687,0.01457481,-0.03295996,-0.00275095,0.04841262,-0.06792071,-0.02521342,0.01498474,0.00126743,0.01856328,0.01187122,0.01232953,-0.04080879,0.00510163,0.04811965,-0.01892281,-0.02166258,0.0494919,-0.02290062,0.02260619],"last_embed":{"hash":"fd0d0962f54e672ab3c4c759cbaa920a4df7e323562ed92ba8cb29ec06690108","tokens":80}}},"text":null,"length":0,"last_read":{"hash":"fd0d0962f54e672ab3c4c759cbaa920a4df7e323562ed92ba8cb29ec06690108","at":1745995301004},"key":"README.md##{1}","lines":[12,12],"size":183,"outlinks":[{"title":"![Build status","target":"https://ci.appveyor.com/api/projects/status/25sd7lfr3v0rnvni/branch/develop?svg=true","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06772409,-0.01285775,0.07472458,-0.0292515,0.05110025,-0.01553054,-0.13067561,0.02909679,-0.00702374,0.02997151,0.0582506,-0.06821905,-0.00376237,0.00448047,0.0002006,-0.00164496,-0.00119161,0.00633889,0.06468148,0.04398318,-0.01070992,-0.0184673,0.07389618,-0.0287439,0.04829193,0.05552752,0.00008358,-0.07587782,-0.00381668,-0.17139195,-0.01931279,0.00467284,-0.00182971,-0.02212404,0.02457807,0.00288556,-0.02839574,-0.03440837,-0.04945804,0.00523278,0.06647199,0.02501386,-0.03270989,-0.01570881,-0.03891924,-0.03607858,0.00862885,-0.03737364,0.05787864,-0.07013539,-0.00301622,-0.01723799,0.08495174,-0.05153824,-0.01219569,0.1081264,0.04115132,0.05185392,0.03763975,-0.00431142,0.06466035,0.05931298,-0.20041402,0.07670496,-0.01016517,0.00755468,-0.08364117,-0.03199949,0.07013069,0.00585043,0.00213188,0.01969713,-0.03743659,0.00127789,0.01061331,-0.01879025,0.04764311,-0.04615359,0.02201412,-0.02378539,-0.05447533,0.01096667,0.00339948,-0.02131172,0.01963479,-0.01494028,-0.00436997,0.03030661,0.05934814,0.00775747,-0.05097193,-0.05778562,0.04809341,0.05460149,-0.08335966,-0.01788231,0.06374378,-0.02557477,-0.00397898,0.16175719,-0.05828385,-0.02619523,0.02898248,-0.00968196,0.09363456,-0.05373472,-0.01593114,0.02005236,-0.0010644,0.00912607,0.00186423,-0.00052535,0.00884585,-0.02923584,0.06037492,0.02060107,-0.0007546,0.07374219,-0.0294301,0.04255332,-0.02194084,0.00650443,0.03458695,-0.02250806,0.01932557,0.0054843,0.03078108,0.05058927,0.02035091,0.0550051,0.03517264,0.02328346,-0.06744832,-0.03950432,0.01469838,0.03773369,0.04020055,-0.08401036,-0.02359034,-0.03229756,-0.03536486,0.04466506,0.01538522,-0.06964804,-0.01825867,0.05398306,-0.06298523,0.03851423,0.00833713,-0.0812474,0.03035259,0.04400094,-0.03514859,-0.00488535,-0.0031791,-0.03761231,0.03021764,0.0646378,-0.0880466,0.06909739,0.01187786,-0.07594604,-0.02964343,0.12561963,-0.02632546,-0.04754756,-0.0515786,0.05656773,0.01358437,-0.02469625,0.00668548,0.02754464,-0.01446057,0.01740421,0.09071926,0.00263968,-0.09962036,-0.00984064,0.03882139,0.03223985,0.03099961,-0.05929757,-0.02971232,0.05496859,0.02284354,-0.0275276,-0.0460831,-0.04519347,-0.0181348,0.03899598,-0.03906057,-0.04138035,-0.03900749,-0.02189508,0.00572333,0.00015718,-0.04237734,0.00630088,0.07410911,-0.07495046,0.08952278,-0.00888654,-0.04990793,0.03668081,-0.12551492,-0.0027729,-0.01773886,-0.00761793,-0.0148822,0.01318377,-0.07750574,-0.01040105,0.04405,-0.00234003,-0.038366,-0.02238714,-0.03836522,0.057909,-0.03785573,0.05656268,-0.01366681,-0.04137425,-0.06639014,-0.26522085,-0.00467453,-0.03421108,-0.02620788,-0.01659917,-0.01562317,0.00464256,0.00756693,0.00436631,0.10465288,0.12791152,0.02067186,0.00428814,-0.06872105,-0.04787522,-0.00646124,-0.00931663,-0.01663435,-0.04260959,-0.0164705,0.00931646,-0.01561109,-0.05154729,-0.10472196,0.01883128,-0.02035915,0.1481671,0.09723252,0.09691201,-0.02863225,0.02234325,0.01089341,0.02073994,-0.13726738,-0.00309317,0.04309781,0.01688368,0.09183235,0.01782214,0.04674748,-0.01299556,0.03452319,-0.02199077,-0.11860581,0.06153883,-0.01933723,0.00751647,-0.00736792,0.0037108,-0.00936455,0.00426537,-0.02522079,-0.01646633,0.0250511,0.02335055,-0.03223653,-0.04015585,-0.03593668,0.0666343,0.00350694,0.0192224,0.05193511,0.00765654,-0.06480857,0.08247139,0.02580957,-0.03027923,-0.04651214,0.06099244,-0.00992843,-0.0374777,0.03927538,0.01309739,0.03520169,0.04109519,-0.0122441,-0.02694775,-0.02081624,0.00483971,-0.01405349,0.01854283,-0.01404149,0.04776964,0.02039335,-0.01494449,0.06071069,-0.05081473,-0.00609827,0.05171192,-0.0093393,-0.01220142,-0.00517389,0.0221688,-0.06919515,0.07359812,0.04088803,-0.22240168,0.02708268,0.04294804,-0.01976375,-0.02915186,0.02965455,0.0882372,-0.05876957,-0.01414098,0.00833585,-0.00126193,0.02065351,-0.05549533,-0.04166498,0.01229583,0.03680846,0.02919061,0.01522284,0.05474406,-0.01381556,0.02244022,-0.015946,0.18862863,0.0003042,0.00317583,0.02777225,-0.05539507,0.01762811,0.06254469,0.06250308,-0.02979021,0.06422616,0.05533089,-0.05655816,-0.0066278,0.05666109,0.02607142,0.02333991,0.05465785,-0.03931142,-0.01594363,0.03018417,-0.05260002,0.03358177,0.04628698,-0.06571604,-0.01906118,-0.02171781,-0.01423217,0.03380889,-0.03608133,-0.0380341,-0.01904508,0.01035508,0.0492681,0.06652087,0.02771857,-0.0053049,-0.0154891,-0.01936843,-0.03288445,-0.08858892,0.07533611,0.03552103,-0.00577582],"last_embed":{"hash":"cd7834e894b08c0251ef6884d1b02b0c5312b05eee20e16c7d4d2e44f97d2094","tokens":68}}},"text":null,"length":0,"last_read":{"hash":"cd7834e894b08c0251ef6884d1b02b0c5312b05eee20e16c7d4d2e44f97d2094","at":1745995301041},"key":"README.md##{2}","lines":[13,16],"size":162,"outlinks":[{"title":"![Build Status","target":"https://travis-ci.org/alicevision/meshroom.svg?branch=develop","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Photogrammetry": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.00582532,-0.01094467,0.03875921,-0.0173641,-0.02112668,0.02962298,0.03968601,0.03907919,-0.04879705,0.0734438,0.03953171,-0.123074,0.03390701,0.07475616,-0.05788323,0.00340746,-0.00561361,0.04688492,-0.02310589,0.01698498,0.11953814,-0.05087257,-0.02903636,-0.00179762,0.05032276,0.10134766,-0.03758892,-0.08237612,-0.0301509,-0.1821306,0.01204838,0.00838956,0.00438873,-0.03518117,-0.03533486,-0.0304389,-0.02439552,0.00340776,-0.0152269,0.04806303,0.03850459,0.01991057,-0.03140771,-0.0605269,0.02084099,-0.05928392,-0.03269576,0.02554423,-0.01691946,-0.06608653,-0.00445838,-0.04869946,0.04156828,-0.01607152,0.0294867,0.05493164,0.064638,0.00655044,0.05450478,-0.0250683,0.06124853,0.07645226,-0.20427778,0.07031424,0.03892471,0.02676453,0.02383706,-0.07901841,0.0619406,0.00815035,-0.01219812,0.00883682,-0.00989726,0.00140773,-0.0106378,-0.10959294,0.01397531,-0.0440878,-0.05725869,-0.04142718,-0.01724723,0.07970147,0.01224122,0.04543647,-0.00696464,-0.0271226,-0.00539261,-0.01158662,0.07115388,0.00542504,-0.03735944,-0.04327596,-0.07514232,0.01908862,-0.03480561,-0.00911595,0.02252369,0.00795125,0.10771019,0.16809987,-0.08592157,0.0052925,0.08591665,0.01346828,0.04383447,-0.07128842,-0.05921404,-0.05545913,-0.04642647,0.02012965,0.01329689,0.01729505,-0.00866243,0.01621601,0.01356411,-0.05542849,-0.00104188,0.08942054,0.01919559,0.00650234,-0.0316987,0.05955955,0.02236654,-0.05164153,0.02169679,-0.02602829,0.04209007,0.07366159,0.00809249,0.02943933,0.0926713,0.00626756,-0.06409536,0.01062817,-0.01607827,0.03186173,0.09533403,-0.05113609,0.03030852,-0.0130923,0.00363223,0.01458356,0.08714463,-0.01051845,-0.00297184,0.11782245,-0.05564547,0.06460109,0.00074279,-0.11919226,0.01705897,-0.00419961,-0.00720316,-0.06188206,0.01488732,-0.01386949,-0.01746117,-0.00427013,-0.06035261,0.04679738,-0.0256077,-0.04149663,-0.05370203,0.05861313,-0.01760142,-0.02503231,0.04242411,0.00910496,-0.01570704,-0.02546464,0.0275805,0.02734575,0.00670735,0.00252252,0.03606778,0.01614577,-0.05741976,-0.01726465,0.03469751,0.01744647,0.0433323,-0.10249128,0.01640598,-0.04500683,0.03139786,-0.03827998,-0.04127742,-0.00194588,-0.00624773,0.10289542,0.0036097,0.02282698,-0.04987311,-0.00200541,0.0090174,-0.01178695,-0.02220199,0.00004839,-0.02916461,-0.02839077,0.00743301,-0.03434417,0.02084153,0.0778554,0.01577435,0.00253017,0.09229242,-0.00593969,-0.01535164,0.09015367,-0.04336199,-0.03396812,-0.02465134,-0.0555879,-0.08296809,-0.01098666,0.05440925,0.01847767,-0.11806891,0.04940084,-0.05569082,-0.06067422,-0.04382201,-0.19338332,0.00433552,0.03549828,0.00655071,0.0353101,-0.07061332,-0.0422075,0.0393419,-0.0137757,0.0314054,0.16638649,-0.02721344,-0.00506582,-0.03540815,-0.01613996,-0.02583363,-0.06238252,0.00435575,-0.07218102,-0.04092943,0.00894856,0.04338277,-0.0689951,-0.08482733,0.00347321,-0.03850387,0.12640066,-0.01923169,0.06560326,0.03613641,-0.00056905,-0.00029034,-0.03831822,-0.06108798,-0.03834637,0.00374989,-0.03239266,0.05403782,-0.06245494,-0.01924481,-0.05683693,0.04411812,0.06306009,-0.03517335,-0.01468998,0.01304848,-0.01164086,0.02957204,0.01042344,-0.02986875,0.01403775,-0.04699871,-0.04128756,-0.00674948,0.04740577,0.04034809,-0.05510291,-0.03191583,-0.02715144,-0.01877405,0.00170911,0.04274389,0.02122543,-0.05966416,0.06999741,0.02949424,-0.02181498,-0.06526385,0.03503617,-0.00113115,0.03591134,0.14042649,-0.02344384,-0.0057344,0.06141239,0.01104525,0.08593298,-0.03012329,0.00154045,-0.04022647,0.03358921,-0.03609788,-0.02421223,0.07343727,0.08057364,0.01652971,-0.01470568,-0.04036798,-0.0187902,-0.01416054,0.02774294,0.04249138,0.00686496,-0.09774276,-0.0030157,0.05480522,-0.20853554,-0.02940985,-0.0313315,0.00782772,0.00885633,-0.00366467,0.0692986,0.00129308,0.03057877,-0.04368333,-0.03222922,-0.023345,-0.01147591,-0.01069663,0.00063069,0.07544866,0.02766193,-0.04654687,0.06065983,0.0005012,0.03184717,0.02388326,0.17073056,-0.05547058,-0.00450056,-0.00515093,-0.00316384,0.00695207,0.02583622,0.00443535,-0.00730761,-0.00565131,0.06700734,-0.01378542,0.03316528,0.11481616,0.01927273,0.02384231,0.02413876,0.01605541,0.00168153,-0.03555204,0.04281832,0.01309195,0.12837723,0.06274933,-0.03026413,-0.02771865,0.00521956,-0.00573633,-0.03718796,0.01103564,0.01056156,0.0818276,-0.01415737,0.05690505,-0.05407225,-0.00433716,0.00196408,0.03027484,-0.01794799,-0.04330399,-0.00420551,-0.0197479,-0.05840521],"last_embed":{"hash":"d035a5fd1d5d2c4a06db15841440f4d841ef2a8daf637ba5e4bdc2b7396e1df9","tokens":99}}},"text":null,"length":0,"last_read":{"hash":"d035a5fd1d5d2c4a06db15841440f4d841ef2a8daf637ba5e4bdc2b7396e1df9","at":1745995301072},"key":"README.md##Photogrammetry","lines":[17,26],"size":407,"outlinks":[{"title":"presentation of the pipeline steps","target":"http://alicevision.github.io/#photogrammetry","line":8}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Photogrammetry#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[0.01034561,-0.00851353,0.04788553,-0.01602183,-0.01930793,0.02457537,0.03878079,0.0420737,-0.04553774,0.06790461,0.04363798,-0.12379073,0.03621506,0.07522198,-0.05956735,0.00330834,-0.00458656,0.04497371,-0.0192642,0.01903306,0.12049855,-0.04839398,-0.02687842,0.00146876,0.05257861,0.09920157,-0.03787725,-0.07946496,-0.03359246,-0.18188171,0.0112458,0.00355858,0.0073377,-0.03415717,-0.03487293,-0.03592551,-0.02329941,-0.00281724,-0.00979376,0.04859307,0.03706779,0.02137229,-0.03013138,-0.06249497,0.01173364,-0.06339303,-0.03031548,0.0293971,-0.01790577,-0.06978557,-0.0026331,-0.0458024,0.0411478,-0.01538461,0.02889593,0.05657834,0.05988861,0.00427193,0.0578562,-0.02651423,0.05844112,0.07578021,-0.200105,0.07258367,0.03609404,0.02421471,0.01947358,-0.07890124,0.06252601,-0.00069516,-0.01094185,0.01069618,-0.01764403,0.00021403,-0.01216542,-0.10916007,0.01572025,-0.04300917,-0.06072582,-0.03742445,-0.02427071,0.07826014,0.01350816,0.04049924,-0.011824,-0.02898385,0.00037587,-0.01439255,0.06483973,0.00262734,-0.03919687,-0.04291799,-0.0801027,0.01771994,-0.03499707,-0.00945834,0.02423884,0.00708363,0.10950553,0.16634296,-0.08697579,0.00999567,0.07871554,0.00870655,0.04482706,-0.06545047,-0.06095293,-0.05954869,-0.0530689,0.01822912,0.01403527,0.01554125,-0.01073448,0.01775697,0.01752815,-0.05094319,-0.00246226,0.086576,0.02136322,0.01158769,-0.02902408,0.05407303,0.02341253,-0.05681414,0.02715532,-0.02142418,0.04227743,0.07419658,0.00654096,0.03283709,0.09434254,0.0077968,-0.06841801,0.01375981,-0.01462254,0.03251698,0.09070753,-0.04870144,0.03200557,-0.01601493,0.00230101,0.01868458,0.09248052,-0.01254182,-0.01028362,0.1113703,-0.05362336,0.06678566,-0.00005498,-0.11679406,0.01953021,-0.00182299,-0.00689855,-0.05932692,0.01429356,-0.01394325,-0.01640455,-0.00821238,-0.06092756,0.04593492,-0.02992834,-0.04020378,-0.04869544,0.05515657,-0.01466084,-0.02098453,0.04030175,0.01214865,-0.01285723,-0.02730652,0.03538064,0.02858216,0.00959351,0.00561945,0.03634047,0.01780697,-0.05768463,-0.0094406,0.0274448,0.01554222,0.04416723,-0.10149107,0.01928634,-0.04521615,0.03545471,-0.03483533,-0.03982922,-0.00079677,-0.00779954,0.11008181,0.01088292,0.0253414,-0.04827613,-0.0023299,0.00992282,-0.00988103,-0.02634214,0.00615437,-0.03115471,-0.02950053,0.00748493,-0.03774225,0.01838471,0.07482003,0.01303401,0.00040339,0.09313595,-0.00297857,-0.0160712,0.08209271,-0.04530849,-0.03173994,-0.02169878,-0.05866066,-0.08039708,-0.01026061,0.06172401,0.01348552,-0.11581227,0.05072955,-0.05444561,-0.06792693,-0.04610405,-0.19746546,0.00463157,0.03684577,0.00658978,0.03441783,-0.06783407,-0.03858447,0.03595364,-0.0123804,0.02918971,0.1642324,-0.02067678,0.00374661,-0.03530822,-0.01550205,-0.02316803,-0.06061455,0.00849418,-0.0745606,-0.04127117,0.01439524,0.03863504,-0.06766602,-0.07982919,0.00340622,-0.04619453,0.12634932,-0.02287863,0.06250511,0.03816427,-0.00372889,-0.00164262,-0.03640125,-0.06455997,-0.04385192,0.0046066,-0.03738627,0.05599862,-0.06107248,-0.01698812,-0.05888306,0.04391896,0.06247311,-0.03241844,-0.01772709,0.01431789,-0.00523808,0.03460054,0.00438918,-0.03193935,0.01903037,-0.05025893,-0.0418795,-0.01007864,0.04439611,0.03905068,-0.05625575,-0.02902907,-0.02521388,-0.0244958,0.00248469,0.04287921,0.02295347,-0.06181966,0.06590392,0.0319343,-0.01478259,-0.06318448,0.04017198,-0.00018286,0.0408283,0.13994336,-0.02528795,-0.00796108,0.05978876,0.01278155,0.0860417,-0.03429208,0.00299486,-0.03880526,0.03555122,-0.03399605,-0.02264085,0.06953056,0.07627881,0.01390978,-0.01537395,-0.04308164,-0.01914566,-0.0154166,0.03322817,0.04387752,0.00974985,-0.09715732,-0.00144972,0.05447998,-0.21254565,-0.03263482,-0.03680157,0.01219699,0.00813233,-0.00546617,0.06866035,0.00328786,0.03384486,-0.04200868,-0.03380254,-0.02755157,-0.02009896,-0.01105267,-0.00724238,0.08100441,0.02264947,-0.04248024,0.06494983,-0.00649128,0.03586914,0.02379837,0.17254642,-0.05674803,-0.01245332,-0.00017341,-0.00186165,0.00458293,0.0227302,0.00324234,-0.01087355,-0.00068362,0.07005966,-0.0138928,0.02828188,0.11776764,0.01921724,0.02648309,0.02329208,0.01277053,0.00667619,-0.02911523,0.04014414,0.00777703,0.12488066,0.06152049,-0.03347031,-0.02610093,0.00278402,0.00186279,-0.03841986,0.01197327,0.01777324,0.08346248,-0.00710397,0.05409369,-0.05580786,-0.0067488,0.00415888,0.02966438,-0.02038414,-0.04009286,-0.00242861,-0.02035142,-0.05620993],"last_embed":{"hash":"0f79098dde139c21c7aed0827fb7b877bd9694ae35aad48255e09779d2357351","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"0f79098dde139c21c7aed0827fb7b877bd9694ae35aad48255e09779d2357351","at":1745995301117},"key":"README.md##Photogrammetry#{1}","lines":[19,26],"size":388,"outlinks":[{"title":"presentation of the pipeline steps","target":"http://alicevision.github.io/#photogrammetry","line":6}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Manual": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08492802,-0.02227473,0.0600796,0.00418069,0.03909762,0.0014704,-0.04031407,0.04330824,-0.03005445,-0.02135913,-0.02235188,-0.05424477,0.0253469,0.05454157,0.03006135,-0.0031357,0.00896134,0.04254673,0.04153999,0.01629611,0.10174361,0.0116933,0.01095315,0.01256106,-0.00951812,0.02722957,-0.00512124,-0.01059843,-0.01796939,-0.18421133,0.00626797,0.01418565,0.03438311,-0.0033392,0.02271759,-0.03357544,-0.04612604,0.00286098,-0.05577232,0.0431133,0.01253693,0.03480157,-0.03691148,0.01648655,0.03309793,-0.04005319,0.03890831,-0.02574722,0.07182336,-0.04961938,-0.02003048,-0.03485999,0.06363473,-0.03191176,0.03961581,0.07366824,0.07469068,0.06209991,0.03689591,-0.0360544,0.0375964,0.02378474,-0.19129808,0.15299013,0.05499243,0.02433083,-0.05469652,-0.054359,0.07391879,0.00502138,0.00609133,-0.00418073,-0.0201468,0.01420357,0.03161265,-0.04807935,0.02139912,-0.08047315,0.01592177,-0.07248273,-0.04695983,0.08256974,0.002191,-0.00402535,-0.03107413,-0.02923205,-0.02473883,-0.00490308,0.03575756,-0.03362533,-0.03469332,-0.06363449,0.03163908,0.08335871,-0.04147403,-0.04953422,0.02245758,0.01498174,0.01224091,0.14279515,0.00177315,-0.03585102,0.00237285,0.01635874,0.03167461,-0.03889714,0.0052883,-0.01886531,-0.0302968,-0.00269953,0.00705751,-0.00580579,0.00240365,-0.0545517,0.05945274,0.01591015,0.01839086,0.03379407,-0.00072673,0.02546494,0.0046348,-0.01009626,0.01759684,0.01572933,0.02000192,-0.01050956,0.05167518,0.0189371,0.04378045,0.03490519,0.04615213,0.05798637,-0.03217565,-0.00532829,0.0409326,0.03850149,-0.02498474,-0.09060638,0.00172675,-0.0302536,-0.04096299,-0.00512116,0.01393858,-0.06663213,-0.01799173,0.11342049,-0.0642796,-0.00946454,-0.05787507,-0.05229789,0.04903799,0.03093343,-0.03700012,0.0371615,-0.02537768,-0.02251819,0.08451738,0.06772709,-0.010978,-0.00509737,-0.02900685,-0.06267788,-0.06007976,0.15666199,-0.01948934,-0.068249,-0.0475454,0.00620194,-0.0098221,-0.06586795,-0.02470869,0.02810327,-0.02343175,0.01244574,0.07874997,0.00874479,-0.01583672,-0.04829751,-0.01859036,-0.00281709,0.01992992,-0.07915055,-0.04411918,-0.01111702,0.02761877,0.00032092,-0.02835478,-0.05286852,0.0095708,0.07297961,0.00650675,-0.01620951,-0.0257003,0.00998572,-0.03777271,0.02082026,-0.0133459,0.01992023,0.02939467,-0.04704833,0.08916302,0.05005266,-0.03473686,0.01829891,-0.04035283,0.00881286,-0.00382648,-0.03856671,0.0564696,0.01454014,-0.08307184,-0.04062362,0.05539539,0.00643326,-0.09836362,-0.01104915,0.02222398,0.05073896,-0.02288782,0.03691705,-0.02976178,-0.03860211,-0.08455675,-0.27241805,0.02702058,-0.02024011,-0.05315791,-0.01695936,-0.0472415,0.05056831,-0.00307606,0.02707407,0.05722531,0.10866155,0.01651021,-0.00130027,-0.07039579,-0.01710402,0.0266879,-0.03737573,-0.04706771,-0.090386,0.02573473,0.03455486,-0.00545921,-0.07393959,-0.06798375,-0.00919385,-0.02752492,0.14445443,0.00762218,0.12840366,-0.00069795,0.08346839,-0.02207844,0.02188916,-0.09944784,-0.01005786,0.11106625,-0.00999825,0.05789771,0.0326312,-0.04946429,-0.04322293,0.07206568,-0.01376903,-0.1373522,0.00134527,-0.06886709,0.01879665,0.00109248,-0.04166268,0.04002657,0.0105096,0.00576559,-0.00587988,0.05210423,-0.03367759,0.02528901,-0.00093757,-0.04945767,-0.02686873,0.02195938,0.02622858,0.01375328,0.0192028,-0.08217883,0.05182498,0.00745458,0.00871318,-0.05739434,0.12454694,-0.06284317,-0.02056097,0.04422234,-0.00958483,0.06103707,-0.03029687,0.03485576,-0.02932749,0.00067887,-0.03183588,-0.03825738,-0.02931242,0.02103935,0.05423883,0.04529734,0.02502649,0.07116751,-0.05943953,0.00526279,0.04114686,-0.02728299,-0.03260456,0.07160525,-0.04466608,-0.0403851,0.0336448,0.0040359,-0.23821142,0.02417043,0.01367853,0.06049534,-0.03811352,0.02299295,0.07256951,-0.03426758,0.01551552,0.00723529,0.00141788,0.01488808,-0.05077138,-0.00378085,0.01431426,0.06136489,0.01865857,0.01782815,0.00565787,-0.05178258,0.00258207,0.0366118,0.18719201,0.03039183,-0.02482305,0.04397985,-0.01802657,0.05246062,0.0634376,0.03888879,0.00811472,0.02065949,0.07732292,-0.03200734,0.01450437,-0.00243212,0.01757639,0.05192973,0.0757988,-0.01526469,-0.08222579,0.02969043,-0.0284212,-0.00476724,0.04377698,-0.05596278,-0.03321914,-0.05735178,-0.021654,0.01315655,-0.03985383,-0.01894013,0.01742017,0.02419599,0.04255991,0.02571127,0.0097568,-0.03608289,-0.05647601,0.00479345,0.03706813,-0.0575671,0.01791925,-0.01954669,0.02127306],"last_embed":{"hash":"5c1c9c0b57dde5817625b81d33c9c582201b4933b7a1a0b13940afac19cfd254","tokens":23}}},"text":null,"length":0,"last_read":{"hash":"5c1c9c0b57dde5817625b81d33c9c582201b4933b7a1a0b13940afac19cfd254","at":1745995301162},"key":"README.md##Manual","lines":[27,31],"size":51,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Manual#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08294639,-0.02404619,0.06256623,0.00589749,0.04044814,-0.00003,-0.03621215,0.0373363,-0.03653916,-0.0233526,-0.02183605,-0.04643146,0.03154126,0.05052424,0.02449542,-0.00494385,0.01934383,0.04914198,0.04901543,0.02165062,0.10602345,0.02196846,0.00120203,0.01906352,-0.00365058,0.02495564,0.00098878,-0.00833997,-0.00848839,-0.1757502,0.00673967,0.01645224,0.02460265,-0.00174878,0.015188,-0.04349197,-0.04398856,-0.00201984,-0.06284203,0.04136458,0.001466,0.03433783,-0.02774301,0.00775768,0.03298724,-0.03783602,0.04170959,-0.02911779,0.06423228,-0.04765013,-0.01777782,-0.02819205,0.06876144,-0.03443808,0.03073889,0.07226948,0.07522123,0.06528524,0.0355664,-0.03486088,0.03582994,0.01832341,-0.19765414,0.15112019,0.03825222,0.02604889,-0.05517353,-0.06392035,0.07743366,0.00884295,0.00020766,-0.00973563,-0.02289409,0.00614733,0.03227757,-0.05628333,0.01656765,-0.07608227,0.00071724,-0.06480862,-0.0536362,0.08229444,-0.00117064,-0.00540684,-0.0284015,-0.0399084,-0.02940591,-0.01411042,0.03396263,-0.03687884,-0.02669097,-0.0631478,0.02363161,0.08384231,-0.03519265,-0.04678227,0.01429606,0.02163605,0.01757103,0.13897578,-0.00352631,-0.04034996,0.00959009,0.01711823,0.03386727,-0.04553945,0.00415895,-0.01325148,-0.02394397,-0.00616348,-0.00045438,-0.00484012,-0.00404631,-0.05267283,0.06522521,0.00736727,0.01123725,0.03056406,-0.00113488,0.02744375,-0.00358654,-0.0164107,0.02545497,0.01126312,0.00911278,-0.01523034,0.04056942,0.01883703,0.04464406,0.0251659,0.04824174,0.04013741,-0.02451676,-0.00243151,0.05105453,0.04286176,-0.0266111,-0.08179402,0.00995182,-0.02426073,-0.03871541,-0.00327481,0.02397063,-0.05460598,-0.02298775,0.11377944,-0.07593152,-0.00318832,-0.05548484,-0.04676659,0.05693807,0.02630303,-0.03060353,0.04286778,-0.03632201,-0.03022723,0.07517906,0.05883507,-0.01897567,-0.01073692,-0.03237309,-0.06613196,-0.058536,0.15656389,-0.02223861,-0.05230168,-0.04459967,0.00600919,-0.0091315,-0.0520404,-0.02489343,0.0249279,-0.02823161,0.01541335,0.07763319,0.00441294,-0.01970287,-0.0426986,-0.02568392,0.00235971,0.03196491,-0.07809458,-0.04239853,-0.01276849,0.03123193,0.01095091,-0.03177784,-0.05735168,0.01367875,0.07286367,0.01613867,-0.00702063,-0.05013688,0.0130566,-0.0308697,0.02858038,-0.01681275,0.02464934,0.03100558,-0.04498835,0.07955862,0.04703879,-0.03112792,0.01424329,-0.03878498,0.00595064,-0.00152441,-0.03964164,0.05564383,0.0062868,-0.08295079,-0.03782493,0.05489101,0.00277385,-0.10123563,0.00881343,0.02567347,0.04294926,-0.02116972,0.04454883,-0.03153496,-0.02835534,-0.06770609,-0.28110874,0.02808825,-0.02332848,-0.0530166,-0.00644294,-0.05674424,0.05151933,-0.00717921,0.02774994,0.04914641,0.10264364,0.01520112,0.00027945,-0.07230899,-0.02082773,0.04367701,-0.03099316,-0.03592183,-0.09848405,0.01896757,0.0252165,-0.00153804,-0.08143597,-0.06297179,-0.0076215,-0.02532135,0.15043551,-0.00457446,0.13174595,-0.00908425,0.08181805,-0.02247201,0.02368381,-0.10080689,-0.00328148,0.11225687,-0.01487523,0.03996582,0.0440526,-0.06328417,-0.0478571,0.07182627,-0.00936544,-0.13380688,0.00252644,-0.06739447,0.02855819,0.01185008,-0.05562042,0.05060782,0.00184711,0.00694583,-0.01488998,0.05914842,-0.02807177,0.02387898,-0.00203599,-0.05658316,-0.03259286,0.02396323,0.02518188,0.01521627,0.0048471,-0.08294742,0.0534641,0.00130751,0.01692955,-0.06733363,0.12782244,-0.04916596,-0.02209982,0.05551188,-0.01399888,0.05438803,-0.02881524,0.03043228,-0.03156436,0.00346763,-0.02382773,-0.04644056,-0.03489139,0.02300561,0.04360953,0.04588263,0.03011258,0.06437631,-0.06311672,-0.01490638,0.03388864,-0.02947742,-0.03877583,0.07684415,-0.04154442,-0.03666869,0.03554082,0.01860628,-0.24276683,0.01848796,0.00060408,0.06302559,-0.04017726,0.02383292,0.07684735,-0.02424649,0.02204175,0.01662423,0.01746293,0.01082587,-0.0537747,0.00563168,0.01488822,0.05895596,0.01244189,0.02785015,0.01566596,-0.05517527,0.0067549,0.0314206,0.18147096,0.0271077,-0.03211049,0.03484474,-0.00898945,0.04752977,0.05337857,0.03610838,0.01323952,0.02590334,0.0818705,-0.03235236,0.0223441,0.02222896,0.0213928,0.05028841,0.08273056,-0.00443322,-0.0889852,0.03888054,-0.0403541,-0.00096541,0.04750421,-0.04209035,-0.02953554,-0.05397458,-0.01788175,0.01170302,-0.02867177,-0.01565238,0.02699214,0.01694664,0.04089079,0.02335908,0.00644492,-0.02945591,-0.06115971,0.00409294,0.03765714,-0.03121087,0.01861561,-0.01977532,0.02725329],"last_embed":{"hash":"44dd9ec9fe3ab5221c438730ac1f598b53154210f99e0ad2c91d6586dca90d41","tokens":22}}},"text":null,"length":0,"last_read":{"hash":"44dd9ec9fe3ab5221c438730ac1f598b53154210f99e0ad2c91d6586dca90d41","at":1745995301174},"key":"README.md##Manual#{1}","lines":[29,31],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07089823,-0.02601486,0.01491335,0.00299529,-0.00623873,0.01325516,-0.08132792,0.04786772,-0.08749115,0.04996283,0.02196839,-0.07957453,0.02262935,0.05014093,-0.02565126,0.01631708,-0.01353888,0.04133686,-0.01793672,-0.0143774,0.02395029,-0.04651689,0.0190213,-0.00655175,0.01967067,0.04855973,-0.01710919,-0.08590656,-0.01700681,-0.24794811,0.03056428,0.05053103,0.00822986,-0.0448225,-0.03564111,-0.01207818,-0.0237812,0.02286595,-0.04706125,0.01161375,0.00992077,0.06434372,-0.06617504,-0.03649345,-0.0055597,-0.06740647,0.03095784,-0.01147611,0.05816526,-0.05646488,-0.0183463,-0.07935964,0.05812974,-0.05305452,0.0495977,0.03208036,0.06643029,0.03436269,0.03772879,-0.00137218,0.01682571,0.04373844,-0.1862727,0.1622619,0.0174312,0.02447861,-0.01701832,-0.05044427,0.11259528,0.01773674,-0.03108325,0.01447965,0.00227808,-0.00887816,0.01295158,-0.04007959,0.02953174,-0.05173305,-0.06401476,-0.056864,-0.03699746,0.08994061,0.02586733,0.00221768,-0.03699611,-0.00318047,-0.01669107,-0.04229849,0.03172577,0.02340291,-0.03709733,-0.02894542,-0.00684151,0.02068755,-0.02617213,-0.02494263,0.03584627,0.01931747,0.03947254,0.11568426,-0.0271458,0.0080495,0.09559197,0.03164809,0.01875698,-0.07724651,0.01895698,-0.03839936,-0.00292927,0.06590275,0.00085078,0.01302795,0.01473238,0.00624245,0.00363176,0.00544581,0.01285482,0.0232356,0.03624527,-0.01726834,0.00292803,0.00465788,0.05756674,-0.0041267,0.03135728,-0.01341264,-0.00596039,0.02362471,0.03116188,0.04314486,0.04475145,0.08281299,-0.05700635,0.0112641,0.02090856,0.01077393,0.04102506,-0.04837767,-0.00066224,-0.00856075,0.01202687,-0.00199342,0.06146896,-0.04865004,-0.03642131,0.09058077,-0.04172644,0.05472691,-0.02958748,-0.10786166,0.02758077,0.06711537,-0.06977148,0.01030624,0.05141827,-0.03774235,0.06890857,0.07548968,-0.0758547,0.02978682,-0.04160872,-0.07930072,-0.07329923,0.10354688,-0.02260601,-0.08595011,-0.03078738,0.01529139,-0.00792399,-0.03998826,-0.02111888,0.00192971,-0.00091419,-0.02341887,0.07075037,0.01635881,-0.11509174,-0.04411711,0.05503564,0.04824116,0.01827495,-0.10457784,-0.02237854,-0.00860218,0.00296017,-0.0753096,-0.00865255,-0.01099248,-0.02632516,0.06085313,0.00277831,-0.03241699,-0.04664266,0.06377547,-0.01942998,0.0185491,-0.03250895,0.00772773,0.01066067,-0.03349047,0.00965628,-0.02830809,0.04360073,0.05433548,-0.02081857,-0.00209217,0.04279113,-0.02084564,0.06284606,0.06151715,-0.08814899,-0.03126397,0.0496917,-0.00712844,-0.04048828,-0.01812592,0.02393854,0.0755119,-0.09708832,0.05560377,-0.02783323,-0.05943044,-0.0709001,-0.21675313,-0.00005648,0.01161035,0.03012889,0.0196101,-0.03829718,0.04731895,-0.00479535,0.04487703,0.0298159,0.11859996,-0.01593784,0.00167236,-0.03005072,-0.04254507,-0.02704694,-0.03713259,0.01015639,-0.09063186,-0.02293271,0.00242849,0.02785067,-0.02165809,-0.06659883,-0.00242285,-0.05786335,0.13893074,-0.01008892,0.09588574,0.04402234,0.05486041,-0.02587366,-0.02030584,-0.10419387,-0.01239135,0.08530253,0.05300415,0.02118572,0.02894801,0.00732406,-0.00899617,0.07114476,0.04731429,-0.14583144,-0.04484941,-0.00972717,-0.03128126,0.0540593,-0.02249111,0.02638568,0.02805501,-0.00436574,0.02709857,-0.00103553,-0.01771903,0.03122857,-0.05283684,-0.04424468,-0.0465787,0.02876982,-0.00107055,-0.00772852,0.05007709,-0.06334895,0.07434708,0.04485998,0.02821403,-0.08113033,0.07084575,-0.03727258,0.03499267,0.10121525,0.0108906,0.0353346,0.08345789,0.03922978,0.05594875,-0.00699046,-0.0243152,-0.0781919,-0.00037401,-0.00182652,0.00558306,0.07656559,0.05818866,0.06673168,0.02503469,-0.01016018,-0.03937519,-0.04406728,-0.00202257,0.0424712,-0.03104294,-0.04919066,-0.01324397,0.01692617,-0.23044163,-0.00438279,0.02904836,0.08695438,-0.01644446,-0.01401654,0.05284777,-0.07188778,0.03914197,-0.05408493,-0.03549297,-0.02638932,-0.01095913,0.01109592,0.02182206,0.05719661,0.0228607,-0.02819149,0.04911108,-0.03594551,0.02267439,0.03663928,0.16674042,-0.05120803,-0.0262166,-0.02105426,-0.0430329,0.02146848,0.06628532,0.0227853,-0.04481134,0.01102447,0.05977838,-0.02865564,0.03223439,0.02736389,0.01374586,0.01006231,-0.00712809,-0.0228403,-0.00413307,-0.01416739,0.00186301,0.02565076,0.03906956,0.00674144,0.02247184,-0.01940665,-0.05443837,0.03500852,-0.03496659,0.02260677,-0.00939492,0.00191248,0.00445237,0.04057725,-0.00610434,-0.00632923,-0.03377297,0.04862702,0.03248952,-0.04448554,0.00071976,0.04213475,-0.02409762],"last_embed":{"hash":"1286f7e0d89f3db971a14ee9b593348654d6eda0a7f3b2c5945a92ee3aeee0b0","tokens":391}}},"text":null,"length":0,"last_read":{"hash":"1286f7e0d89f3db971a14ee9b593348654d6eda0a7f3b2c5945a92ee3aeee0b0","at":1745995301187},"key":"README.md##Tutorials","lines":[32,54],"size":1164,"outlinks":[{"title":"Mikros Image","target":"http://www.mikrosimage.com","line":3},{"title":"Meshroom: Open Source 3D Reconstruction Software","target":"https://www.youtube.com/watch?v=v_O6tYKQEBA","line":3},{"title":"Mikros Image","target":"http://www.mikrosimage.com","line":7},{"title":"Meshroom Tutorial on Sketchfab","target":"https://sketchfab.com/blogs/community/tutorial-meshroom-for-beginners","line":7},{"title":"Prusa 3D Printer","target":"https://blog.prusaprinters.org","line":11},{"title":"Photogrammetry 2 – 3D scanning with just PHONE/CAMERA simpler, better than ever!","target":"https://www.youtube.com/watch?v=1D0EhSi-vvc","line":11},{"title":"CG Geek","target":"https://www.youtube.com/channel/UCG8AxMVa6eutIGxrdnDxWpQ","line":15},{"title":"How to 3D Photoscan Easy and Free! by ","target":"https://www.youtube.com/watch?v=k4NTf0hMjtY","line":15},{"title":"Moviola","target":"https://moviola.com","line":19},{"title":"Meshroom Survival Guide","target":"https://www.youtube.com/watch?v=eiEaHLNJJ94","line":19}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06568453,-0.02343913,0.02244055,-0.00716233,0.01743332,-0.04417472,-0.12439926,0.04221294,-0.06681321,0.0460468,0.04664281,-0.09712402,0.03196546,0.02573186,0.0007898,0.02242147,0.01893581,0.05400716,-0.02922717,0.00925284,0.05772775,-0.010413,0.03761218,0.0091409,-0.00993942,0.04923475,-0.00173784,-0.06948609,-0.03080441,-0.21651155,-0.01077991,0.03346906,0.01278529,-0.02793372,0.01048539,-0.02633459,-0.01414697,0.01081059,-0.03718429,0.02939459,0.03909947,0.06923394,-0.05113019,-0.01092939,0.03590009,-0.0227249,0.01931481,-0.0031717,0.06494606,-0.03834357,0.00054695,-0.00505586,0.05457297,-0.02039379,0.05959686,0.04656115,0.03605507,0.05279392,0.02741408,-0.04219699,0.05109819,0.06253918,-0.18787353,0.13224427,0.01846151,0.03773077,-0.02095423,-0.05667386,0.09539457,0.0094725,0.00437426,0.00192703,-0.00096294,-0.01285369,0.03185672,-0.02694452,0.01760409,-0.0400439,-0.05205528,-0.04596715,-0.03855303,0.07275295,0.0095455,0.01713204,-0.03758274,-0.04530621,0.00434502,-0.00661916,0.04586496,0.05616799,-0.03946895,-0.03911305,0.02038916,0.05895487,-0.04111518,-0.04664899,0.02083223,0.0579092,0.05305244,0.14237913,-0.01414597,0.01663563,0.06541285,0.01060511,0.00954356,-0.05695022,0.02132612,-0.01491492,0.00443724,0.05128077,0.021816,0.00736706,0.01289356,0.0043371,0.02664783,-0.00195699,0.00508028,0.01531056,-0.02919364,-0.01270577,-0.02891933,0.01320978,0.04005723,0.01338007,0.04593698,-0.03634223,-0.01355111,0.012282,0.0225768,0.05552088,0.00453823,0.06194144,-0.07736111,-0.01577501,0.00030766,0.03390494,0.05476635,-0.08674084,-0.0154089,-0.02841934,-0.01626882,-0.03987433,0.04801945,-0.04531994,-0.070274,0.08542102,-0.0150292,0.04772088,-0.05955648,-0.08359285,0.0288376,0.05544675,-0.07243398,0.0307849,0.04055765,-0.03492058,0.06861494,0.04812262,-0.08066489,0.02961959,-0.02067575,-0.09917933,-0.06957941,0.10130156,-0.00620593,-0.07268534,-0.03617081,0.03375302,0.00829997,-0.06249376,0.01327521,0.04688936,-0.01546814,-0.00449825,0.07913804,-0.00849183,-0.10764164,-0.06653199,0.04474473,0.03393247,0.00488672,-0.09147009,-0.02436919,0.01367668,-0.00894374,-0.088689,0.0057525,-0.0030955,-0.00997018,0.06732571,0.02283019,-0.04003916,-0.05625579,0.05574919,-0.00552658,0.01590121,-0.04622588,0.03147013,0.0418841,-0.05499749,0.05244326,-0.00577169,-0.00430837,0.04710899,-0.05049904,0.00694618,0.02241865,-0.03611522,0.04188893,0.06887421,-0.0883567,-0.02786607,0.02763887,0.01320979,-0.08126684,-0.00604123,0.03308443,0.03334951,-0.0885824,0.03282237,-0.02972381,-0.07694619,-0.08046315,-0.24133165,-0.01035302,-0.02980354,0.00096763,-0.00180278,-0.03913878,0.04018589,-0.03213518,0.0197957,0.07815202,0.11479155,0.00576271,-0.01223307,-0.03749328,-0.01554157,-0.00148588,-0.0317842,-0.00011495,-0.11233136,-0.01844147,-0.0042038,0.01418572,-0.01485744,-0.04526605,0.02164499,-0.06172215,0.14219706,0.03663773,0.10959989,0.03550451,0.08079223,-0.04497296,-0.00088353,-0.1159382,-0.00763439,0.08189231,0.05951832,0.05953425,0.01793595,-0.00178676,-0.00441708,0.03714473,0.02699666,-0.12573855,0.00496336,-0.00266997,0.00890029,-0.00307476,-0.03074166,0.05869685,-0.01669814,-0.00150873,0.01832881,0.02225954,-0.02065225,0.04561593,-0.05838522,0.00047327,-0.03836555,0.03716043,0.01294325,-0.00236348,0.03868448,-0.08170626,0.05804366,0.02291867,0.04537352,-0.07416125,0.08604623,-0.05548421,0.03563908,0.08469371,-0.01347341,0.05620284,0.08118786,0.05010958,0.01761497,-0.03676005,-0.01058076,-0.03506681,-0.00514804,-0.02072094,-0.01952175,0.07033318,0.04666363,0.08023071,-0.03428998,-0.02629674,-0.02300674,-0.04876233,-0.00617402,0.02853106,-0.03737963,-0.0498952,0.02578172,-0.01328663,-0.22881603,0.0205566,0.05548212,0.06006578,-0.03023855,-0.00561737,0.03559381,-0.0635208,0.04050775,-0.05921727,-0.02159644,-0.02329499,-0.05847379,-0.01344402,0.04406946,0.04850386,0.04783876,0.00361256,0.03581286,-0.02824561,0.02381906,0.03102709,0.16941671,-0.0298169,-0.0254498,-0.01393403,-0.03521148,0.03240781,0.03530527,0.03579063,-0.05378344,0.02548392,0.05117727,-0.04261623,0.00587408,0.04590306,0.0104373,0.04669132,0.01533828,-0.01258555,-0.03687,-0.01719204,-0.05448581,0.03750228,0.02766692,0.01549523,0.01083421,-0.01740253,-0.04873551,0.04077059,-0.03558166,0.00600964,0.03594854,-0.00810161,0.00669984,0.05372952,-0.00529084,-0.01585037,-0.01436985,0.02943416,0.01112586,-0.08096974,0.01989839,0.02768574,-0.04195874],"last_embed":{"hash":"310390bf52dfb9270318b262e5326e0010285ac7fdebb38e4ff3c7ea11d61ba9","tokens":65}}},"text":null,"length":0,"last_read":{"hash":"310390bf52dfb9270318b262e5326e0010285ac7fdebb38e4ff3c7ea11d61ba9","at":1745995301412},"key":"README.md##Tutorials#{1}","lines":[34,35],"size":144,"outlinks":[{"title":"Mikros Image","target":"http://www.mikrosimage.com","line":1},{"title":"Meshroom: Open Source 3D Reconstruction Software","target":"https://www.youtube.com/watch?v=v_O6tYKQEBA","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08676089,-0.02164239,0.06345209,-0.02386656,0.03144696,-0.0203813,-0.10247094,0.03267722,-0.01699929,0.01313113,0.00014096,-0.01640418,0.029129,0.04816224,0.0047739,0.01187942,0.01587675,0.02842042,0.0064211,0.00759242,0.00844247,0.02781551,0.01945479,0.01216353,-0.00220309,0.05826081,-0.03906968,-0.07328416,0.01097666,-0.18492056,0.00115376,0.00559097,0.06207718,-0.01014689,-0.00044241,-0.02814845,-0.00293015,-0.00353037,-0.06111064,-0.01666378,0.03602688,0.06182155,-0.02988002,0.00275644,0.02053571,-0.05969341,0.0291907,-0.02567268,0.04659479,-0.06284929,-0.01299845,-0.0628266,0.0385337,-0.02947028,0.0200879,0.05587915,0.05208623,0.03869631,-0.00981537,-0.01247161,0.024181,0.03706995,-0.15244845,0.13934022,0.02148376,0.05892809,-0.04158528,-0.05055611,0.10398983,0.00216479,-0.01183795,-0.00824539,-0.00116472,0.01688355,0.07225434,-0.04336255,0.0073319,-0.03132328,-0.01857859,-0.0526507,-0.0318507,0.03597637,0.02087537,0.00866012,-0.03134077,0.0010243,0.0154434,-0.01390753,0.05141468,0.00225776,-0.0561423,-0.06363898,-0.04457775,0.05336356,-0.01500484,-0.05533301,0.01887993,-0.03155002,0.05822165,0.14694729,-0.04704775,-0.03816068,0.07010318,-0.03198306,0.00909617,-0.03254773,0.0388976,-0.01873288,-0.02378649,0.04400121,-0.04729229,-0.00020974,-0.03783764,-0.00712126,0.0480569,-0.01083844,0.01694696,0.01898052,0.00757557,-0.01744382,0.03422464,0.03224358,0.02055188,0.02685805,0.03011977,0.00563953,0.04517569,0.02883571,0.00669051,0.08301932,0.05623465,0.06758482,-0.05560423,-0.00797725,0.05277015,-0.00009672,0.01115085,-0.08230586,-0.02167239,0.00552755,-0.06614723,0.04066669,0.01520558,-0.1001264,-0.06917834,0.14861785,-0.0079339,0.03075645,-0.0577473,-0.07354544,0.0405737,0.04245396,-0.02542618,0.00075265,0.01490538,-0.01254162,0.10922641,0.02672965,-0.0131096,0.04009819,-0.01947016,-0.06110123,-0.07066895,0.15832742,-0.03249081,-0.11348853,-0.03250698,0.03420817,-0.00812812,-0.06715273,-0.0204021,0.03854487,-0.02869455,0.01068924,0.04672984,0.00747651,-0.05515382,-0.05898321,0.04997082,-0.02210485,0.03364814,-0.08997127,-0.04535984,0.00961588,-0.03169024,-0.03091825,-0.00477138,-0.04589732,-0.01064529,0.07144938,0.00255582,-0.02545069,-0.01106377,0.01949672,-0.02035306,0.00402508,-0.023883,0.00414222,-0.00872332,-0.05901369,0.04494179,0.02433255,-0.02864742,0.04556395,-0.00505032,-0.01130076,-0.01652782,-0.00653828,0.06073091,0.00425441,-0.09984571,-0.04125628,0.01475091,0.03692776,-0.04234346,0.00436252,0.03755365,0.08877634,-0.05662511,0.06034001,0.01292346,-0.00587631,-0.09310132,-0.24074586,0.02949658,-0.01359278,-0.04650475,0.01657551,-0.032366,0.06557324,-0.00243897,-0.00174274,0.04900021,0.10129917,0.00929899,-0.01910732,-0.05296787,-0.01254746,-0.03430833,-0.05098246,0.02250885,-0.12041111,-0.00673835,0.04032121,-0.0270858,-0.01728664,-0.08207563,-0.058346,-0.02478851,0.12681766,-0.01593696,0.11999261,0.03869658,0.07061382,-0.03352256,-0.01301305,-0.13089706,0.00188678,0.09633662,0.05167676,0.04536983,0.05341283,-0.00035377,0.00226975,0.04942168,0.01704531,-0.160457,-0.03045198,-0.02107338,-0.00412111,-0.04392677,-0.05813491,0.01139001,0.03821189,-0.00485938,0.02696161,0.02041847,-0.03752185,0.05238381,-0.01141436,-0.00849953,-0.02647276,0.03282186,0.00607284,-0.02304454,0.04897629,-0.05872412,0.04829301,0.0594627,-0.00902038,-0.05492274,0.08403978,-0.06343254,-0.00773,0.03455507,-0.02629465,0.06365786,0.00265728,0.06156589,0.04900293,-0.02754085,-0.04059587,-0.02866907,-0.07122181,-0.01926839,0.02980062,0.04544705,0.04141517,0.06671908,-0.03243461,-0.01561971,0.00726365,-0.02789811,-0.02615895,0.04207515,-0.04418274,-0.02270713,-0.01545064,0.00717066,-0.21898562,0.03408154,0.03192559,0.03597883,-0.06051152,-0.0140418,0.04275865,-0.0599275,0.02845188,-0.02820482,-0.00601366,-0.02332265,-0.02034577,-0.04392941,0.02502764,0.04407849,0.06677966,0.0017345,0.04954716,-0.02924513,0.02887175,0.06041727,0.18313086,-0.02243339,-0.04352297,0.06269785,-0.04299104,0.02584274,0.1167099,0.06509198,0.01249529,0.02018279,0.12357265,-0.01692932,-0.0163518,0.04131471,0.03584465,0.03422901,0.05137074,-0.00976353,0.00402138,0.03155545,-0.04873077,-0.017951,-0.01377573,-0.01894887,-0.01000922,-0.04979087,0.00864048,0.01237631,-0.01715155,-0.04056932,-0.01196658,0.02736516,0.0347085,0.03156518,0.01768746,-0.03943346,-0.03925178,-0.01071161,0.01687546,-0.07315011,0.04568465,0.0132519,0.0289754],"last_embed":{"hash":"b2275fd883c00316ba4765cfa465a4c1edd2953c959b664693f2e6423cc359b3","tokens":16}}},"text":null,"length":0,"last_read":{"hash":"b2275fd883c00316ba4765cfa465a4c1edd2953c959b664693f2e6423cc359b3","at":1745995301442},"key":"README.md##Tutorials#{2}","lines":[36,37],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06821302,-0.03528363,0.01393085,0.00189141,-0.00469565,0.03260614,-0.12007297,0.03743532,-0.02974666,0.02137629,0.01516771,-0.06077215,-0.01859812,0.06246756,0.00137785,0.00032959,-0.00331267,0.02718626,-0.03030642,0.01687785,0.04751897,0.02765142,0.02857018,-0.02297257,0.003795,0.03199361,0.01394154,-0.06068899,-0.01155975,-0.17974317,0.02366218,0.07276022,0.02899388,-0.01889428,0.00497795,-0.02304202,-0.00800368,0.01584125,-0.0437169,-0.0007772,0.06920552,0.06557818,-0.05408048,0.01120652,0.02438894,-0.03225727,0.00904816,-0.01459313,0.07246137,-0.02494586,-0.01327368,-0.08353207,0.05918428,-0.05065082,0.05440518,0.04582296,0.04437463,0.02369359,0.04796956,-0.02475062,0.01929303,0.02385353,-0.16988257,0.17306794,-0.02223482,0.01976636,-0.04376433,-0.04637395,0.09458934,0.04309319,0.02547577,-0.00786358,-0.036924,0.00193938,0.0227512,-0.01343124,0.01991235,-0.03917172,-0.04252693,-0.04940849,-0.06264972,0.05406985,0.04923398,-0.0077262,-0.04392203,-0.00750765,-0.03591456,-0.04701134,0.02190604,0.04654573,-0.01690277,-0.07926404,-0.01352287,0.05630321,-0.0465944,-0.06687385,0.03098615,0.01180755,0.01703316,0.14055249,-0.04187616,0.01079216,0.0755803,0.00717633,-0.02143835,-0.05531593,0.02723419,-0.01774563,0.02851946,-0.00222303,-0.02371328,-0.02818089,0.00570668,-0.02270357,0.05051947,-0.02268852,0.03257151,0.03526452,0.03164857,0.03977923,-0.00899184,0.01353115,0.04391887,0.04421774,-0.00242234,-0.0430057,-0.0207396,0.02507111,0.00747372,0.05256288,0.00655398,0.06771823,-0.04374876,-0.01849102,0.02142284,0.03560015,0.04726909,-0.06341623,-0.00105612,-0.0285328,-0.010359,-0.01802045,0.06268546,-0.08158825,-0.01289403,0.04381155,-0.03593325,0.03921397,-0.0374369,-0.08028667,0.02139397,0.02628698,-0.0903049,-0.00134634,0.02481048,0.00571379,0.11631003,0.06536245,-0.04063724,0.00837832,-0.04743603,-0.09994661,-0.08848213,0.085623,-0.01237467,-0.06963267,-0.05173094,-0.00182356,-0.02259142,-0.07061824,0.01029159,0.01749654,-0.03503701,0.00723182,0.079321,0.02185318,-0.07759507,-0.07101142,0.01056783,0.0315875,0.01595072,-0.06410824,-0.04396474,0.00674271,-0.01324962,-0.03048984,-0.0099843,-0.07361005,-0.00571232,0.02407371,-0.01746473,-0.01928556,-0.0446487,0.05199087,-0.05228185,0.0078188,-0.02882221,-0.01712811,0.00008363,-0.05640669,0.04266499,0.01676086,-0.00745821,0.01701419,-0.0423723,0.02608675,0.02420681,-0.01782303,0.09564855,0.00975742,-0.11065167,-0.03193451,0.01732375,-0.00223109,-0.03581823,-0.02701263,0.01640045,0.08369534,-0.08608955,0.02276201,0.00887264,-0.08018355,-0.08701348,-0.23451577,-0.00515062,-0.00757903,-0.0279252,0.02281675,-0.0706299,0.03973117,0.00500543,-0.03232151,0.0787117,0.09552888,-0.02411495,-0.00063372,-0.08086148,-0.01907909,0.00402797,-0.04731025,-0.01131376,-0.05532882,0.0011393,0.01578492,0.01916981,0.00510219,-0.05669628,0.00070023,-0.01765081,0.12193587,0.05490524,0.11208554,-0.0072354,0.09545165,-0.0242998,-0.00810522,-0.09271366,-0.0054487,0.06267578,0.00763352,0.01625817,0.0275428,-0.04739239,0.00731558,0.07287848,0.02486188,-0.10394896,-0.05498077,-0.00663856,0.00277445,0.02394087,-0.04806079,0.01320952,-0.00194686,0.03064363,-0.00274903,0.05189076,-0.05277229,0.02577951,-0.0506372,-0.00415991,-0.0722676,0.0947385,0.02863674,-0.01161662,0.04684296,-0.06314607,0.08051103,0.06414255,-0.01447388,-0.05651795,0.10517033,0.01613631,0.02045539,0.07715762,0.04200433,0.06967831,0.08595952,0.03384949,0.05109123,0.00334382,-0.03093488,-0.06771266,-0.02598632,-0.05227635,0.01766218,0.03797229,0.01624553,0.10440329,0.00805171,-0.07402013,0.01650343,-0.01431993,-0.02850815,0.03831019,-0.04690848,0.02312394,-0.00744462,-0.01617592,-0.22663459,0.03448858,0.0652439,0.09519404,-0.0109533,0.00002688,0.04924466,-0.06623401,0.02328185,-0.045995,0.06017748,-0.00847069,-0.03836519,0.00720204,0.01560318,0.03655744,0.02918826,0.0269334,0.04311877,-0.02807191,0.05373042,0.01055112,0.19370632,-0.03177322,-0.02181514,-0.02083407,-0.02351232,0.01585467,0.07963765,0.08448969,-0.01402027,0.03450576,0.09801666,-0.030348,0.04050739,0.05335103,0.01328684,0.04789307,0.03487552,-0.0606914,-0.04554201,0.0314631,-0.07214172,0.02015741,-0.00918743,-0.01467832,-0.0066799,-0.02243135,-0.04043571,0.04534866,-0.0157308,-0.01099113,-0.02152127,0.02143393,0.01520032,0.00203188,-0.00128714,-0.04230784,-0.03450119,0.02503866,0.05146955,-0.02444812,0.05602089,0.00848109,-0.02774124],"last_embed":{"hash":"f73940f43b9a41d61aa276deb7fb38e3c9d8360d31dc7391ddb9576f936e7061","tokens":66}}},"text":null,"length":0,"last_read":{"hash":"f73940f43b9a41d61aa276deb7fb38e3c9d8360d31dc7391ddb9576f936e7061","at":1745995301452},"key":"README.md##Tutorials#{3}","lines":[38,39],"size":152,"outlinks":[{"title":"Mikros Image","target":"http://www.mikrosimage.com","line":1},{"title":"Meshroom Tutorial on Sketchfab","target":"https://sketchfab.com/blogs/community/tutorial-meshroom-for-beginners","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08361042,-0.01109727,0.06117484,-0.04653097,-0.02646691,0.03852593,-0.06174146,0.03134112,-0.00156619,-0.04122783,0.01132105,-0.02178214,0.00520953,0.0689469,0.04783021,0.01313688,0.02080481,-0.03836879,-0.04758095,0.00233425,0.05686819,0.01621636,0.00131024,0.00640066,-0.00835846,-0.00570781,0.01478164,-0.0353884,0.00374146,-0.18639401,0.00762504,0.01287769,0.04633876,0.05417541,-0.02346143,0.00030036,-0.01890857,0.01541281,-0.01732591,0.00313329,0.00279927,0.05719761,-0.06877501,-0.03689992,-0.04422145,-0.01927904,0.00089133,-0.02471036,0.00817485,-0.08030325,-0.05426709,-0.05701793,0.02799327,-0.04906085,0.06387398,0.02919447,0.024185,0.03657418,0.02781886,0.07115832,0.03170431,0.01043176,-0.18319102,0.12440439,-0.0369295,0.04043639,-0.01649124,-0.02554677,0.08364716,-0.0011203,-0.03338847,0.00553057,0.02071535,0.04569951,0.01913764,-0.02841119,0.00303718,-0.00711465,-0.0039553,-0.04147723,-0.02764208,0.00514909,0.0364931,-0.04150626,-0.05897176,-0.01492272,-0.01172112,-0.00812102,0.05006697,-0.04043234,-0.01208709,-0.05605618,0.01138351,0.02074064,0.00033097,0.00641111,0.00115454,-0.04024706,0.01128487,0.13070627,-0.05287985,0.02081875,0.03201701,-0.00598799,0.04197025,-0.00472187,0.05399107,-0.05459073,0.00200277,0.00228181,0.00495427,-0.04397046,0.00315604,-0.01339572,0.04975107,0.0033879,0.03131431,-0.00160902,0.07699014,0.03639492,0.04465346,-0.02385513,0.03314076,0.00079989,0.04748586,0.02002604,0.05739608,0.0120114,0.00819663,0.07350149,0.08392033,0.05686744,-0.0963528,-0.01337577,0.03410406,-0.00519791,-0.00085032,-0.07713158,-0.05345934,-0.00705272,-0.09711263,0.0231656,0.03068796,-0.12247224,-0.02919959,0.11792001,0.00165826,0.07098428,-0.03509742,-0.0477786,-0.00370753,0.02072891,-0.0194478,-0.01544138,0.00464728,0.00901632,0.07870949,0.00540306,-0.06152934,0.07049046,-0.05698043,-0.04121732,-0.11853454,0.05085633,0.00943626,-0.14398924,-0.01411019,0.0312699,-0.00285512,-0.00801295,0.03386979,0.03337969,0.01757597,-0.00667424,0.13151835,0.06550808,-0.0284232,-0.06339715,-0.02389883,-0.01923065,0.04685354,-0.09377467,-0.05580202,0.01484033,-0.03023038,-0.06352423,0.04011871,-0.05395083,0.02516558,0.03385797,-0.03763521,-0.02467773,0.00207963,-0.00437114,-0.04299419,0.0425696,-0.05218875,0.01485971,-0.02302965,-0.05246732,-0.03320407,0.00421321,-0.03233742,0.02276738,-0.00243371,0.0025379,-0.01733404,0.02077137,0.10562638,0.00700866,-0.05483585,0.04675855,0.01860089,-0.03330595,-0.04569099,-0.06836858,0.02468779,0.04493897,-0.04049365,0.04246326,0.02751531,-0.02404723,-0.06546438,-0.20508207,-0.01764926,0.03974823,-0.07503638,-0.00346241,0.00742242,0.07229963,0.02486623,0.03702787,0.06947631,0.11168876,-0.0291914,-0.03540923,-0.05947001,-0.00829416,-0.05854458,-0.03431383,0.00091659,-0.03473596,0.00006567,0.02717097,-0.01034388,0.00409086,-0.09204687,-0.01996462,-0.01206341,0.10364132,0.03852502,0.05692848,-0.00914343,0.03153059,-0.0040929,-0.01337695,-0.10146578,-0.01003298,0.04356848,0.0094909,0.04000003,-0.00447949,-0.02021294,0.06263601,0.05636076,-0.00546928,-0.12336382,-0.13368154,-0.01552467,-0.01342773,0.02863961,-0.04838287,0.00208575,0.06321868,0.00166677,0.0745459,0.03882281,-0.02385439,-0.00638507,-0.0673653,0.05598684,-0.00018334,-0.02162122,0.00006464,0.0096164,0.01688078,-0.06427353,0.01309035,0.01533612,-0.03072803,-0.03917947,0.01324183,-0.02158837,-0.0532771,0.02255341,0.01491391,0.06136928,-0.03151402,-0.02272431,0.07353601,0.00115026,-0.0269136,-0.02292555,-0.01793958,0.00935522,0.02330968,0.0479001,0.02076832,0.11915559,0.04821331,-0.06323077,0.03788802,-0.02331254,-0.05136673,-0.01045124,-0.02366113,-0.05774335,-0.01930316,0.02432866,-0.24257162,0.01692656,0.03500685,0.09593535,0.00659918,0.03189608,0.06359671,-0.05370001,-0.01069673,0.00610123,0.02355739,-0.01111976,-0.04803878,-0.01132384,-0.04815721,-0.00412544,0.11773281,-0.02091575,0.02150563,-0.07207987,0.04879866,0.04199407,0.21665595,0.02335865,-0.04441079,-0.01815059,0.02544364,0.01094488,0.13824159,0.05405828,0.01384579,0.01255984,0.07880978,-0.00737835,-0.03369857,0.08607546,0.06022155,0.04563054,0.03745414,0.00870912,0.04275871,0.01393258,0.01690998,-0.01159506,0.03678589,-0.06592562,-0.04681085,-0.07203618,-0.05716661,-0.00600821,0.01477768,-0.02519522,0.02532288,0.02044547,0.04247189,0.08688171,0.00797357,-0.04742475,0.0033354,-0.00989411,0.06313799,-0.01805064,0.04703834,0.03365625,0.01763507],"last_embed":{"hash":"0ab57a41034b3a34104c7ac2ad0c972132657162a0467ee9b6b2f2a1c29cf07b","tokens":24}}},"text":null,"length":0,"last_read":{"hash":"0ab57a41034b3a34104c7ac2ad0c972132657162a0467ee9b6b2f2a1c29cf07b","at":1745995301484},"key":"README.md##Tutorials#{4}","lines":[40,41],"size":70,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.078917,-0.0032247,-0.04056254,-0.00034412,0.00978751,-0.02686139,-0.07212397,0.09006139,-0.08669353,0.05063486,0.02687466,-0.07184169,0.05182071,0.04870193,0.01483708,-0.01157464,0.02912904,0.00689503,-0.01123833,0.04900113,0.06885497,-0.03710327,-0.03570741,-0.01044716,0.03500003,0.05004446,-0.04971718,-0.06748673,0.00753756,-0.20204477,0.03084818,0.05487482,-0.01725391,0.03118958,-0.03630833,-0.02814064,-0.02243946,0.00265292,-0.01173581,0.01580913,-0.03968079,0.04495625,-0.0720184,-0.05609461,-0.03315031,-0.05482385,0.01108588,-0.00131017,0.04535424,-0.08518229,-0.00692612,0.01651966,0.02761595,-0.08662817,0.01665088,0.00572456,0.09693425,0.02057531,0.00508877,-0.01342777,0.05226155,0.04685686,-0.21733034,0.14533812,0.00683941,0.01354159,-0.00742776,-0.04285245,0.03093042,0.07933427,-0.00697343,0.04860955,-0.02815684,0.04788464,0.0335705,-0.0792854,0.01009481,-0.08078616,-0.06754483,-0.04072727,-0.05627341,0.07895061,0.04677272,0.01050462,-0.0101458,0.02540833,-0.02320382,-0.00210483,0.07441199,-0.0070796,-0.01162206,-0.02901441,-0.02886239,0.03127306,-0.04743516,-0.01001364,0.04459316,0.02658555,0.05771803,0.11345938,-0.07915157,0.04263498,0.08529916,0.02559522,0.02496059,-0.05211012,-0.01283916,-0.05613678,-0.04611474,0.01220762,0.01991115,0.01675853,0.01550054,0.01543775,-0.02814943,-0.04281296,0.01662259,0.05434011,0.06247167,0.01583877,-0.02523914,-0.01797132,0.02598361,-0.04777493,0.03349817,-0.06053021,0.04061225,0.01771578,0.01085402,0.07298712,0.07898539,0.03907609,-0.08501057,0.04095151,-0.00316784,-0.00210985,0.05681617,-0.03964499,0.02910443,-0.04950008,-0.00324865,0.0203718,0.05169528,-0.04337514,0.0105357,0.05031896,-0.02724761,0.04291398,-0.01874658,-0.11103602,0.04639761,0.03999016,-0.02194922,0.00086921,0.05547112,-0.01581106,0.07991658,-0.01091989,-0.06449952,0.05580848,-0.01627273,-0.07580639,-0.06896238,0.04549181,-0.01768198,-0.0948202,0.0078824,-0.03223788,-0.03728617,-0.01813997,-0.00108127,0.02310992,-0.00962817,-0.00896203,0.08293493,0.0834668,-0.06369042,0.00945767,0.04064855,0.05867476,-0.01476993,-0.06876938,-0.02725599,-0.03795969,0.0344296,-0.05629427,0.00427399,-0.02328517,0.01100591,0.09045493,0.03458569,-0.03313353,-0.07554176,0.03869009,-0.02331707,0.00405939,-0.02950457,0.01519861,-0.01026621,-0.04725761,0.00337132,-0.01652631,0.00868292,0.02964675,0.00381573,-0.00931834,0.06943129,-0.02430795,0.02560042,0.04689396,-0.04130263,-0.02082009,0.06892101,-0.02465425,-0.04714553,0.00157262,0.01712667,0.09197517,-0.06972177,0.05707651,-0.00844219,-0.08726412,-0.07984257,-0.21894227,0.00493637,0.03285499,0.01857579,0.00409331,-0.06185369,0.00069391,0.01467683,0.00650557,0.03385314,0.11691051,-0.04337401,0.02274297,-0.03878536,-0.01027886,0.02157149,-0.01053324,0.00062637,-0.06270848,-0.03388168,-0.02887935,0.0226864,-0.03726,-0.05953357,0.07574902,-0.0052928,0.15336952,0.00520318,0.05583402,0.0540532,0.041147,-0.00763317,-0.05290288,-0.08639444,-0.00581873,0.03495541,-0.01465431,0.05856677,-0.03233905,-0.04434144,-0.03323504,0.07147043,0.02453882,-0.08523878,-0.03524176,-0.03673175,-0.03734731,0.05113614,0.01474665,0.00912403,-0.00911714,0.005527,-0.00769147,0.01191782,-0.00886536,0.0307216,-0.07074168,0.0060152,-0.0630946,-0.01064385,-0.02899851,-0.03018983,0.01462137,-0.05539032,0.03410159,0.02038884,-0.01470382,-0.06917537,0.08433605,0.04318176,0.02087166,0.11926775,0.00671036,0.01395488,0.05109281,0.04092577,0.07327496,-0.03515637,-0.00585943,-0.10207506,-0.0014113,0.04368015,0.01092086,0.06954754,0.04250554,0.0493825,0.03786846,-0.04555036,-0.0485377,-0.03417848,-0.02353081,0.04496345,0.02234301,-0.02694877,0.02346365,0.00608299,-0.21241413,-0.02206852,0.00121838,0.05980569,-0.05677143,0.00631033,0.04692052,-0.06784033,0.03670701,-0.04956641,-0.01610877,-0.0014345,0.00990236,0.06115286,-0.00258194,0.08551349,0.00087072,-0.017126,0.03189357,-0.10438909,0.04034708,-0.00710494,0.17392579,-0.0573197,-0.05882006,-0.00020398,-0.00734012,0.0195542,0.04921088,0.03336357,-0.03378038,0.00796138,0.06314854,-0.04916559,0.01155869,0.05880666,-0.05342271,0.03719188,-0.00376247,0.02223062,-0.00184238,-0.01517956,-0.02348475,-0.01136309,0.08272269,0.02718766,-0.07038952,0.01712045,-0.02525377,0.0577457,-0.02868477,-0.06358861,-0.00845128,0.04669403,-0.00022014,0.04335282,-0.0406854,-0.03137882,0.02452015,0.0426328,0.06533761,0.00055826,0.03327904,0.05222302,0.0281195],"last_embed":{"hash":"53875c6b20681dd1ebd60c75cda5f4a6f312ed4c25efcaefe4672d517e0313ed","tokens":74}}},"text":null,"length":0,"last_read":{"hash":"53875c6b20681dd1ebd60c75cda5f4a6f312ed4c25efcaefe4672d517e0313ed","at":1745995301496},"key":"README.md##Tutorials#{5}","lines":[42,43],"size":184,"outlinks":[{"title":"Prusa 3D Printer","target":"https://blog.prusaprinters.org","line":1},{"title":"Photogrammetry 2 – 3D scanning with just PHONE/CAMERA simpler, better than ever!","target":"https://www.youtube.com/watch?v=1D0EhSi-vvc","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04650113,-0.01271033,0.06518617,-0.01781507,-0.00192942,0.0218832,-0.04625593,0.02725355,-0.03522695,0.03213505,-0.00152512,-0.0447796,0.01832238,0.09324123,-0.00664378,0.010924,0.01628027,0.04910593,-0.00739959,0.00778948,0.00300373,-0.01708002,-0.00292492,0.00623538,0.01628798,0.06089929,-0.04923091,-0.07758658,0.00095513,-0.20426013,0.00262276,0.03117073,0.0328455,-0.02717692,-0.03763817,-0.0154192,-0.03459467,-0.00417321,-0.05262323,0.00363488,0.03225692,0.02407725,-0.05367333,-0.00755384,0.01535682,-0.0731363,0.02891368,-0.02497188,-0.00348192,-0.05800805,-0.01128639,-0.09281541,0.07121791,-0.03269134,0.01520809,0.06628546,0.08520992,0.01957961,0.00953194,-0.01742085,0.0306522,0.06318296,-0.19165178,0.12428962,0.02071493,0.03424161,-0.00545436,-0.04551741,0.10056783,0.0130128,-0.01542695,0.00592235,0.01107499,0.04420717,0.04509519,-0.05925547,-0.00479682,-0.04333571,-0.0470054,-0.05781026,-0.04176043,0.09027852,0.04655433,0.01941858,-0.01151373,-0.0015635,-0.00240676,-0.04317422,0.05154228,-0.01485371,-0.05527082,-0.06826354,-0.09597882,0.04035036,-0.01738683,-0.04148191,0.01076163,-0.00346918,0.09289484,0.15368886,-0.07577053,-0.03852844,0.11639105,0.00830254,-0.0024953,-0.05128825,0.00486544,-0.05067159,-0.04308636,0.03654393,-0.0544392,-0.00973948,-0.04255975,-0.00024951,0.02539013,-0.00838689,0.02814712,0.03986365,0.03367658,-0.008653,0.02354705,0.02249932,0.02624422,0.00116696,0.03161393,-0.00087526,0.04894779,0.06241509,0.0616021,0.05415064,0.08744755,0.04541382,-0.02809232,-0.00730563,0.04357241,0.01067757,0.05201268,-0.07913131,-0.00528402,-0.00699868,-0.01340393,0.05232964,0.07542411,-0.06601404,-0.03453205,0.16148825,-0.03307699,0.03617984,-0.03112655,-0.11796445,0.03516802,0.03404056,-0.02107579,-0.0129948,0.00737146,-0.01144443,0.07393426,0.03777455,-0.00755441,0.04075946,-0.01925199,-0.07271093,-0.07989795,0.14643249,-0.04473805,-0.069643,0.00839496,-0.01813433,-0.0222327,-0.0576945,0.00746603,0.01234651,0.01087808,0.03400933,0.0693111,0.03389964,-0.04697968,-0.05382197,0.03457306,0.01000862,0.02958527,-0.1111054,-0.02485244,-0.01433284,-0.00128099,-0.0116972,-0.00459216,-0.01668528,-0.00272733,0.1112488,0.02804152,-0.01294515,-0.03946282,0.02699618,-0.04292003,-0.01288183,-0.04180913,-0.04107144,-0.02126957,-0.05070151,0.02039238,0.00827452,-0.02783514,0.05120669,0.02423907,-0.01127654,0.04438191,0.01061602,0.04304096,0.04152527,-0.08514678,-0.04317516,0.03734694,0.00776332,-0.0451016,0.01164088,0.04055168,0.07698365,-0.10858901,0.053606,-0.02311493,-0.05383135,-0.09049086,-0.22176248,0.01919233,0.02592952,-0.0192548,0.00146511,-0.05905616,0.03881111,0.02434826,0.01362988,0.01162706,0.12559001,0.00936131,-0.01309484,-0.06494683,-0.02559117,-0.03398084,-0.06801886,0.01199768,-0.09708778,-0.01721639,0.02279623,0.0179132,-0.03672421,-0.09261999,-0.02259203,-0.03035862,0.10803998,-0.04136616,0.091474,0.04112013,0.0390096,-0.04522356,-0.03074196,-0.09179942,-0.00830443,0.07072159,0.02835761,0.05272046,0.02454411,-0.016408,-0.00073687,0.06825628,0.03302296,-0.11495715,-0.03685863,-0.0276974,-0.01084066,0.00821633,-0.03182062,-0.01352515,0.04275354,-0.04002958,0.01312282,-0.01545157,-0.02150042,0.0427477,-0.04666132,-0.01218356,-0.0545963,0.03367518,0.00053912,-0.0072429,0.0545901,-0.05947876,0.0539485,0.04681177,-0.01374402,-0.05845335,0.07113844,-0.03210232,-0.00288703,0.04909822,-0.00307219,0.04796521,0.04191927,0.04357917,0.07040031,-0.02488986,-0.02203003,-0.03328215,-0.02482937,-0.00317328,0.00013045,0.05235184,0.06743836,0.05199714,-0.01415854,-0.01441022,-0.01929261,-0.03392631,-0.02508406,0.05757977,-0.01954949,-0.02857177,-0.03466956,0.0231971,-0.2154973,0.00534766,-0.01005979,0.03863101,-0.05849347,-0.02235947,0.05787552,-0.07142174,0.04113327,-0.05121155,-0.02628888,-0.04256063,-0.02493757,-0.02167708,0.00569541,0.04328997,0.03744629,-0.00968611,0.06273541,-0.04227022,0.04791347,0.05019133,0.18117999,-0.05062713,-0.03614403,0.05943389,-0.01046679,0.00872721,0.11202047,0.04109599,0.0270501,0.01753288,0.09877052,-0.00312245,0.01825589,0.04860052,0.01681242,0.02001997,0.00487214,-0.01218984,0.04407182,0.01327481,-0.01129777,-0.01419651,0.03148506,0.02197357,-0.0035876,-0.04962211,0.01147344,0.0123157,-0.00928365,-0.01434386,-0.00734081,0.04241332,0.01837552,0.02302387,-0.00321922,-0.03615465,-0.01650627,0.00782812,0.01415704,-0.08145847,0.00943609,-0.01089579,-0.0101939],"last_embed":{"hash":"c61bd76f3a30bb596ac4646bd25270dc3b5810bf2f22088bf7a71edd40d55880","tokens":20}}},"text":null,"length":0,"last_read":{"hash":"c61bd76f3a30bb596ac4646bd25270dc3b5810bf2f22088bf7a71edd40d55880","at":1745995301530},"key":"README.md##Tutorials#{6}","lines":[44,45],"size":67,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07938163,-0.03033497,-0.00175455,0.01443718,0.01386178,-0.02652134,-0.08167381,0.03910761,-0.09469905,0.05964276,0.03217801,-0.05560827,-0.00855173,0.04861078,0.05612685,0.04757354,0.04282113,0.02069722,-0.05193773,0.02930159,0.06777439,-0.00500054,-0.03511579,-0.02633194,0.02279822,0.03353694,0.00173378,-0.06371242,-0.02078351,-0.19584234,0.00769021,0.02277353,-0.01693373,-0.00289291,-0.01718508,-0.04567194,0.00441502,0.0205292,-0.05850032,0.01460283,0.00486258,0.0627441,-0.06536818,0.00087266,-0.00624308,-0.04026757,0.02222682,0.00603371,0.10442866,-0.01875368,0.00456297,0.01685999,0.03453683,-0.0243034,0.02668971,0.023976,0.06170692,0.03782468,-0.00384132,-0.02908369,0.05856315,0.10765053,-0.15512258,0.13599521,-0.01095591,0.06505854,0.01923198,-0.01066519,0.0583166,0.07183567,-0.03043243,0.05156181,-0.02907635,0.0203904,0.01862812,-0.0681907,-0.0038646,-0.0343792,-0.06568789,-0.07431091,-0.06034209,0.07286353,0.03856289,-0.00905948,-0.01093448,-0.03310138,-0.00531961,-0.05555859,0.06009036,0.04483373,-0.00842765,-0.04547963,-0.01312268,0.01587588,-0.05960163,-0.04296514,0.06912263,0.04659652,0.01738619,0.12648655,-0.0632531,0.02357046,0.08341458,0.00402742,0.024111,-0.07659639,0.0104886,-0.0106387,-0.03476991,0.00903571,0.05044788,-0.00387738,0.00640257,0.0080171,0.00801917,-0.04053342,-0.00757692,-0.00302871,0.05296787,0.00224394,-0.02625477,0.02258096,0.04454104,0.03885472,0.07058991,-0.05054699,0.00381793,0.02142524,0.04910669,0.05740815,0.09112631,0.03867277,-0.08412232,-0.00744192,-0.02954929,-0.01664109,0.04024405,-0.01909259,0.02955622,-0.06366906,-0.03922797,-0.0141627,0.04598054,-0.0509916,0.00612496,0.02606385,-0.00200572,0.04337855,-0.03982284,-0.03643932,0.02981879,0.02827398,-0.02919423,-0.02116575,0.03590085,-0.03648529,0.14133455,0.01115589,-0.07596939,0.03733316,-0.0137229,-0.0605262,-0.0279527,0.07137487,0.00727225,-0.09379632,0.02173458,0.0246923,-0.02529876,-0.05305418,-0.00490356,0.05551387,-0.06343963,0.01903854,0.11444823,0.00493538,-0.07678986,-0.06559733,0.01849407,0.00909715,-0.04005094,-0.07600988,0.00809135,0.00013378,-0.01176269,-0.06415269,-0.00430409,0.0137062,0.02499175,0.08035436,0.0243017,-0.06677264,-0.07179683,0.02779471,-0.05386588,0.02189951,-0.04304434,-0.08281157,0.00599155,-0.01148216,0.04401698,-0.0746427,-0.00979901,0.06813673,-0.02934403,-0.00125644,0.05072701,-0.03522391,0.07460117,0.02655586,-0.05380318,0.00372577,0.01813217,-0.03674974,-0.06094284,-0.01608155,0.07476506,0.08372409,-0.1281784,0.03879367,-0.00056717,-0.05139738,-0.08215638,-0.19578782,0.00860671,-0.00309203,0.0069802,0.04586732,-0.07872899,0.07702632,0.00401434,0.01945449,0.10403576,0.1242889,-0.04967913,0.01252366,-0.07720318,0.0222092,-0.0391954,-0.0278626,0.01617371,-0.02789355,-0.06172181,0.00832119,0.00559892,0.00307062,-0.08473746,0.03239037,-0.00245855,0.13576786,0.05579175,0.07732031,0.01107151,0.03709221,-0.00449373,0.00980947,-0.09802924,-0.02275106,0.07074153,0.01987154,0.02317121,0.0080694,-0.02382878,-0.0075406,0.00012942,0.01056257,-0.12019879,-0.02813325,-0.02630709,-0.04265589,0.05496148,-0.03058341,0.01217117,0.04818475,0.02733805,-0.03702946,0.04118574,-0.01515209,-0.01299893,-0.06261877,-0.00073097,-0.04386364,0.058697,-0.0025087,0.00037934,0.0168195,-0.06735957,0.02415854,0.0295324,-0.04236698,-0.09621851,0.05004007,-0.02086379,-0.02537386,0.08873814,0.00383441,0.04293627,0.06254923,0.03807003,0.04486731,-0.00266855,-0.02752984,-0.05048161,-0.02406197,-0.0003255,0.03648055,0.06097009,0.0086076,0.06433914,0.01066426,-0.07741973,-0.02587304,-0.07231121,-0.04061021,0.0157473,-0.04259349,-0.02911158,0.03090165,-0.02457599,-0.22896574,0.0211519,0.02021435,0.03978331,-0.00000885,0.01173808,0.07870805,-0.06455404,-0.00492863,-0.03222963,-0.014987,-0.00839912,-0.06572715,-0.03873413,0.03668756,0.02441991,0.00791847,0.01148861,0.04143461,-0.01725756,0.0747125,0.00593152,0.18587551,-0.02996111,-0.03293656,-0.0018641,-0.04273698,0.03780445,0.10570405,0.07346439,-0.0671204,0.01538172,0.0122059,-0.02790299,0.01982183,0.04786767,0.00834758,0.04544677,0.00117042,0.00296414,0.01778194,0.009296,-0.0335759,0.00290659,0.04219592,-0.04777015,0.01143324,-0.0277205,-0.02676453,0.03281389,0.00739311,0.00176319,-0.02125142,0.04380446,-0.0076574,0.0805457,-0.02393139,-0.04501035,0.03041685,-0.02539525,0.02099933,-0.06497931,0.04441432,0.0514718,0.00243802],"last_embed":{"hash":"bb119ee8997000bf326980a4d2ef206cf697a494200c0058a694c9d6bfd88762","tokens":82}}},"text":null,"length":0,"last_read":{"hash":"bb119ee8997000bf326980a4d2ef206cf697a494200c0058a694c9d6bfd88762","at":1745995301541},"key":"README.md##Tutorials#{7}","lines":[46,47],"size":159,"outlinks":[{"title":"CG Geek","target":"https://www.youtube.com/channel/UCG8AxMVa6eutIGxrdnDxWpQ","line":1},{"title":"How to 3D Photoscan Easy and Free! by ","target":"https://www.youtube.com/watch?v=k4NTf0hMjtY","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04809137,0.02075387,0.08817852,-0.02217927,-0.06516784,0.02795716,-0.06220772,0.04727312,-0.03232305,0.01687018,-0.00987096,-0.06507512,0.01947649,0.08927511,0.01520741,0.02199733,-0.01199928,0.09016874,0.00880257,-0.01669011,0.05512671,-0.03457739,0.00370716,0.0279047,0.036642,0.07760434,-0.05066972,-0.06417058,0.0304207,-0.23740067,0.02686715,-0.00415279,0.00846977,-0.01735375,-0.02090595,-0.01852369,-0.01821776,0.04221296,-0.05973437,0.01382945,0.0130858,0.00475284,-0.06783558,0.00128601,0.02120763,-0.05908252,0.03449617,-0.01313861,0.05333377,-0.03088649,-0.02879732,-0.0869455,0.05893737,-0.04173532,0.01857127,0.04930348,0.1259945,0.02888543,0.05153848,0.00203365,0.0056474,0.02727423,-0.14902201,0.1366991,0.05220911,0.02833366,-0.04216176,-0.02830992,0.09988198,0.02115631,-0.04058116,0.00071667,0.02259883,0.04704248,0.04612417,-0.03332335,-0.01102569,-0.07559775,-0.02212176,-0.01930336,-0.04922578,0.0549168,0.01272837,-0.01170583,-0.03046399,0.01237251,-0.01181215,-0.02185574,0.06731,-0.03105235,-0.04057811,-0.07620703,-0.02966507,0.02067037,-0.06449622,-0.01736389,-9e-8,-0.03935453,0.08360439,0.13657276,-0.0299616,-0.03044068,0.09302908,0.01496714,-0.00959916,-0.0361329,-0.01164304,-0.04106808,-0.01762804,0.0560788,-0.02471187,0.00624711,-0.01875054,-0.01808164,0.01890226,0.00087422,0.01927729,0.02085148,0.00452455,0.02548582,0.0373482,0.01245505,-0.00442209,-0.01614974,0.05428885,0.00212844,0.0244983,0.04112473,0.01436583,0.05874222,0.06443152,0.09074318,-0.05186865,0.00939551,0.0618525,0.00316487,0.02398308,-0.09058967,0.03547261,-0.00381363,0.00173126,0.05659432,0.04792154,-0.07947721,-0.00305369,0.14706616,-0.05531833,0.04947065,-0.03510457,-0.09275624,-0.01263311,0.03659818,-0.03786532,-0.03135306,0.017717,-0.02083587,0.08573528,0.05993894,-0.01104617,0.04469232,-0.04986632,-0.08212715,-0.06867751,0.15661173,-0.01910823,-0.07475469,-0.03063164,0.00180924,0.01202725,-0.04258917,-0.0409134,0.03146091,0.00223144,0.01727656,0.07919392,0.0433945,-0.08323775,-0.06114871,0.04200841,0.03792693,0.0537292,-0.10844984,-0.02807677,0.01683887,-0.00594693,-0.04210826,-0.02287457,0.00335703,0.00337142,0.10049677,0.02262659,-0.04447045,-0.04387432,0.03997957,-0.01565616,-0.00025414,-0.00632213,-0.0381364,-0.01585409,-0.04573401,0.0134499,0.03303409,-0.00998782,0.05983669,0.03011444,-0.00321681,0.02871705,-0.0342654,0.09788091,0.02461451,-0.08908511,-0.05641646,0.0317965,0.03192648,-0.05799952,0.01713594,0.04789475,0.04260915,-0.0665115,0.03013752,-0.02166205,-0.03420628,-0.08629958,-0.21702713,-0.01063047,0.03386875,-0.02484338,0.00790387,-0.06259363,0.04112274,0.02831339,0.01796977,-0.03479404,0.08111504,0.00952796,-0.01816126,-0.01439003,-0.06399751,-0.03958803,-0.04771585,-0.00051625,-0.08383419,-0.00156765,0.01062626,0.01807764,-0.0354119,-0.06624964,-0.02107176,-0.04937675,0.12735176,-0.03737644,0.06252337,0.05181007,0.05292007,-0.0123643,-0.04022596,-0.09786186,-0.04568309,0.05066076,0.0281537,0.05638356,0.05422336,-0.02483544,0.00195833,0.06077113,0.00172507,-0.14327469,-0.03161513,-0.02176094,-0.03392595,0.0029081,0.00378471,0.0143974,-0.01083943,-0.0061435,0.0313519,-0.00133454,-0.03542576,0.01933442,-0.00950157,-0.02428245,-0.0515762,0.01812348,0.01361263,0.02665062,0.02395488,-0.0632764,0.06454644,0.07910271,0.02061788,-0.05701961,0.07162359,-0.06564374,0.01736492,0.0429581,-0.0161262,0.0248081,0.01411467,0.03607502,0.03741983,-0.02475722,-0.02977779,-0.03818269,-0.01174952,0.00241824,0.02527102,0.03636556,0.03796039,0.02577152,0.00486469,-0.02579305,-0.00108307,-0.03130948,-0.0391622,0.06219459,-0.00260393,0.03207596,-0.03130727,0.01000657,-0.24069755,0.01546926,0.02627158,0.09384552,-0.01391734,-0.03001516,0.03576526,-0.052756,0.00494483,-0.02104357,-0.02069202,-0.05197338,-0.03963872,-0.00281875,0.01490384,0.05333156,0.04186091,-0.02208151,0.05833572,-0.08051694,0.0314518,0.04212027,0.20322859,-0.00563716,-0.02776785,0.04036169,-0.01577993,0.00450814,0.06928594,0.0294793,0.00585781,0.01193033,0.08624455,-0.01253059,0.02556156,-0.00570739,0.01346275,0.01258422,0.04339451,0.00844259,0.01654958,-0.01605901,-0.02791809,-0.01839535,0.03644543,-0.03898389,-0.02534309,-0.0425036,-0.02430984,0.02743251,-0.0454635,-0.00665905,-0.00859018,-0.00110624,-0.00832859,0.03015601,-0.02893854,-0.02040182,-0.05393659,0.03316052,0.03486858,-0.07530104,0.02217001,0.00223907,-0.00862021],"last_embed":{"hash":"a536ab74e0e9aad28d4f888787644e3f968eca624ba617034a066802b6226112","tokens":35}}},"text":null,"length":0,"last_read":{"hash":"a536ab74e0e9aad28d4f888787644e3f968eca624ba617034a066802b6226112","at":1745995301579},"key":"README.md##Tutorials#{8}","lines":[48,49],"size":129,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12429358,0.02343376,0.06157842,0.01252068,0.05542547,0.02649016,-0.09655923,0.02910583,0.00175296,-0.00962817,0.00239734,-0.0628818,0.01841826,0.0446068,0.02928085,0.04227281,-0.01364885,0.07342776,-0.01606924,0.0707448,0.05291267,0.03061395,0.07325497,0.0153263,0.04323623,0.01661284,0.02432681,-0.01671696,0.00223843,-0.19480142,-0.00131493,0.01302522,-0.00562275,-0.01850875,-0.02326991,-0.0062115,0.01336258,0.03526829,-0.03232756,0.02825223,0.0094309,0.06725262,-0.02302589,0.01640043,0.02201092,-0.02810061,0.0152618,-0.03337175,0.08548,-0.09183661,-0.01983604,-0.04786563,0.03447328,-0.04467499,0.04006509,-0.02029218,0.04626579,0.0388645,0.01819867,0.00748304,0.04205731,0.00057718,-0.21032074,0.13599366,-0.01149495,0.02805269,-0.07112506,-0.0386983,0.09780448,0.04732187,0.00600477,0.01729211,-0.01656212,0.01236583,0.04060837,-0.02684618,0.0568995,-0.03566593,0.00417951,-0.02406434,-0.04516357,0.02086502,0.00741618,-0.03109867,0.01183393,-0.0512978,0.01779881,-0.01278153,0.04166649,0.04697832,-0.02790613,-0.06590148,-0.01583417,0.06108867,-0.06644781,-0.09225168,0.01490196,0.00059518,0.01308987,0.13291097,-0.01119077,-0.0141041,0.03225432,0.03157449,0.02727625,-0.05163889,0.01290722,-0.00580618,0.01749999,-0.00129513,-0.01580321,-0.00272582,-0.0004054,-0.04723997,0.0257053,-0.01315714,0.03310612,0.02177476,-0.00509855,-0.00208987,-0.01784892,-0.0384373,0.07912683,-0.03797594,0.07399713,-0.01211237,0.02520828,0.01253649,0.01784324,0.03838472,0.00896152,0.06345989,-0.06944814,0.01485228,-0.0282002,0.03369532,0.00613465,-0.07883269,0.01160662,-0.04145901,-0.01381033,-0.02834533,0.04226992,-0.1257102,-0.03923609,0.04297698,0.01247068,0.01805728,-0.05420128,-0.07754031,0.00503205,0.03163601,-0.03258056,-0.00923559,-0.00092056,0.00015632,0.09127021,0.03362352,-0.0769548,0.01524647,-0.03599736,-0.09232207,-0.05141557,0.1383553,-0.00096539,-0.0612141,-0.06987463,0.0065795,-0.0189471,-0.08969901,0.00894552,0.00212276,-0.03044398,0.0212353,0.08351849,0.01779577,-0.0641728,-0.06240008,0.00352767,0.01757131,0.01531113,-0.07342064,-0.04330765,0.04566416,-0.02170691,-0.02442192,-0.0257331,-0.07813261,0.01710856,0.01794688,-0.00023358,-0.07737913,-0.01857338,0.0189231,-0.04938908,0.02008906,-0.02639223,-0.01903479,0.02568338,-0.04372166,0.05338794,0.00297,-0.03373159,0.03378575,-0.05473357,0.03067328,0.05345324,-0.05332022,0.09264521,0.01394889,-0.10311355,-0.01442761,-0.00570995,0.01261459,-0.03892571,-0.00853484,0.03906365,0.05939439,-0.05686155,0.03882494,0.03989757,-0.05519141,-0.07921731,-0.24688262,-0.02580226,-0.03870769,0.02064923,-0.01216046,-0.02025591,0.05333693,-0.01181901,-0.03549749,0.04117262,0.0993994,-0.00968013,-0.01527881,-0.0611763,-0.00093902,0.00194004,-0.02971549,-0.03527951,-0.07719056,0.01376187,0.01683474,-0.03893821,-0.03157848,-0.07526854,0.00685408,-0.0364402,0.13630113,0.06580731,0.10007305,-0.02687006,0.07805263,-0.00476452,0.01357606,-0.10649974,0.0293913,0.02825457,0.03918776,0.01480707,0.04278799,-0.05667842,-0.01265618,0.1020929,0.01140726,-0.12962227,-0.04570896,-0.04426005,0.03136562,0.03411175,-0.0524936,0.02749837,0.03692849,0.01871846,0.02625431,0.01977576,-0.03863322,0.02060005,-0.02474377,-0.02429441,-0.05696839,0.03662621,-0.028982,0.00773748,0.0607616,-0.05816277,0.05522102,0.03200903,-0.00166881,-0.04606553,0.07399678,-0.08223426,-0.00627464,0.034541,-0.02524051,0.03182676,0.03582097,0.02444681,-0.02049285,-0.03356802,-0.00791166,-0.03382007,-0.060022,-0.0093854,0.01260455,0.02495857,0.03296858,0.07397313,-0.03829302,-0.03730959,0.04322631,-0.00597405,-0.00022183,0.04875563,-0.04224549,-0.04007113,0.01514201,0.0235001,-0.21778089,0.0377107,0.05260195,0.04386759,-0.06419891,-0.02663607,0.04564504,-0.08151418,0.03286229,0.01584728,0.04811439,0.008574,-0.02882715,0.00873948,0.05281016,0.05654639,0.0074195,0.04654135,0.02560258,0.00287051,0.04493117,0.02079801,0.18801497,0.01419919,0.00132054,0.028587,0.0063183,0.06965086,0.10670666,0.07267676,-0.02372168,0.01645878,0.08492981,-0.03272846,0.04817445,0.00173907,0.00489679,0.0718084,0.07078467,-0.03241575,-0.01329471,0.01079796,0.0020277,0.06864832,0.0272455,-0.04158393,-0.00992629,-0.06863863,-0.05482243,0.01309149,-0.05990248,-0.0289749,0.02847799,0.02099962,0.00947952,0.07175128,0.03632644,-0.04121638,-0.01351782,-0.01026754,0.00852605,-0.02599904,0.02708645,0.03407193,-0.00767516],"last_embed":{"hash":"1699fdfe6a3df87f7807cfcd33964b9c0d8ad8e84cd8acee6e7fe72017faad04","tokens":56}}},"text":null,"length":0,"last_read":{"hash":"1699fdfe6a3df87f7807cfcd33964b9c0d8ad8e84cd8acee6e7fe72017faad04","at":1745995301601},"key":"README.md##Tutorials#{9}","lines":[50,51],"size":107,"outlinks":[{"title":"Moviola","target":"https://moviola.com","line":1},{"title":"Meshroom Survival Guide","target":"https://www.youtube.com/watch?v=eiEaHLNJJ94","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Tutorials#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10147583,-0.00900391,0.05105969,-0.00562769,-0.00151566,0.01238229,-0.05551538,-0.01572982,0.0036662,-0.0061199,-0.00343624,-0.01528001,0.03235111,0.03239363,0.0109526,0.03856615,-0.02896749,0.07091184,-0.01619533,0.00037397,-0.00290701,0.01644105,-0.00020707,-0.00053306,0.00356178,0.0648815,-0.02417311,-0.08114805,-0.01542118,-0.1958876,-0.00980967,0.02343357,0.05292267,0.01334372,-0.02396148,-0.00514029,-0.00977587,-0.01345186,-0.06424467,-0.02263698,0.01251644,0.05018549,-0.05582873,-0.0070921,-0.00538209,-0.0429258,0.02290209,-0.02785113,0.02791315,-0.06861199,-0.05375283,-0.09588656,0.04580197,-0.04348035,0.02610607,0.11053386,0.03842232,0.01575578,-0.0075725,0.00392718,0.03179279,0.01504867,-0.12922156,0.14482804,-0.00568591,0.02050981,-0.01415115,-0.07394236,0.08371597,0.03821892,-0.02911156,-0.00871358,-0.01250975,0.02507469,0.04010887,-0.03018623,-0.03059337,-0.02861249,-0.01882304,-0.04192766,-0.06986822,-0.01492447,0.00833601,0.00224906,-0.05738402,0.03429034,-0.01480688,-0.04365252,0.0645095,-0.04441908,-0.0531804,-0.02023234,-0.05328532,0.02915119,-0.02101524,-0.04078188,0.01864515,0.01495254,0.0459863,0.15373129,-0.04847942,-0.04729666,0.04515418,0.00492713,0.00938423,-0.03822996,0.019628,-0.05101279,-0.01515251,0.02209652,-0.01880385,-0.02779451,-0.05474087,-0.00733835,0.03274834,0.033141,0.00843784,0.00829494,0.00452255,-0.04241869,0.03271987,0.01321758,-0.00511768,0.0012143,-0.02077726,0.00999554,0.06599727,0.00056197,-0.03100802,0.04750302,0.0671786,0.03105048,-0.05457982,-0.00404521,0.02019062,0.01178529,-0.0039821,-0.0741583,-0.0292033,0.01429369,-0.01106558,0.03258771,0.06043393,-0.09618101,-0.03316106,0.1551564,-0.02283943,0.02525369,-0.04404331,-0.08761188,0.01691937,0.01769907,-0.03740832,0.01293128,-0.01747117,-0.0110721,0.14503214,0.06592798,-0.05276903,0.02075835,-0.06108609,-0.0317581,-0.09904296,0.14132287,-0.03426182,-0.09870207,-0.02676424,-0.00417845,-0.02356279,-0.04690275,-0.00359554,0.04023647,0.0137003,0.04041572,0.04417355,0.01613218,-0.08631331,-0.05523716,0.03496003,-0.04176719,0.03903027,-0.10421915,-0.06233501,-0.00276496,-0.00253673,-0.02603496,-0.00522731,-0.01702682,0.00246626,0.06748044,-0.00910213,-0.03184146,-0.00058296,0.0264724,-0.02463303,0.03165555,-0.02939355,-0.01648223,0.0124937,-0.05725514,0.04945777,0.01348724,-0.00378928,0.03330237,0.0196326,-0.01428263,-0.02593563,-0.02351397,0.0706664,0.04366966,-0.09895121,-0.0282209,0.02917938,0.00213871,-0.05288095,-0.02859584,-0.01516847,0.07276408,-0.03949275,0.0426713,-0.00637587,-0.00260331,-0.04559219,-0.23786421,0.03901133,-0.03182647,0.02312835,0.02113814,0.00259461,0.02101148,0.03088435,-0.0093369,0.07937966,0.12352291,-0.0416984,-0.02361619,-0.02260678,-0.01860772,-0.06064364,-0.05219896,0.02678678,-0.09656405,-0.02351259,0.0571348,-0.02433834,-0.01549518,-0.08510671,-0.01264796,-0.02153177,0.14168757,-0.0105873,0.08966734,0.02960881,0.05188827,-0.06114114,-0.03535454,-0.10535961,-0.00556556,0.12130797,0.09123543,0.01811468,0.08177815,-0.00347274,-0.01879922,0.04380184,0.03353061,-0.12633924,0.00127257,-0.00197366,0.01201191,-0.0343722,-0.01166016,0.03761624,0.05725953,0.01662575,0.01182485,0.03624941,-0.06960192,-0.00094117,0.01268357,-0.02812532,-0.03053116,-0.00522881,-0.04605306,0.00428783,0.08619053,-0.03970794,0.07834071,0.08326985,0.03334584,-0.038236,0.04461712,-0.00730827,-0.00290477,0.02295334,-0.02773113,0.10958504,0.03813353,0.05368303,0.00738861,0.05200489,-0.03620007,-0.03371221,0.00300981,-0.01039051,0.0438715,0.0737719,0.04026913,0.05021189,0.01101022,-0.04403989,0.03783274,-0.04458231,-0.02607547,0.01959288,-0.046452,-0.00882312,-0.03612686,-0.02078632,-0.21362422,0.03572033,0.04741815,0.07769165,-0.07402764,-0.00968263,0.05041066,-0.03545215,-0.00819618,-0.01880254,-0.03225771,-0.04560151,-0.01865724,-0.02015234,0.02867666,0.07021878,0.05992622,0.00912613,0.04639753,0.00030493,0.05097945,0.04125141,0.21317734,-0.03081949,-0.00382425,0.00324617,-0.02480637,0.01639019,0.13012697,0.05185141,0.00286397,0.01512656,0.07965922,0.00916477,-0.01525169,0.01074301,0.04975803,0.04592013,0.05293479,-0.0056807,0.00717471,0.02926723,-0.00523741,-0.03453856,0.01065665,-0.01785905,0.00580923,-0.03598746,-0.04182874,0.00560491,-0.00491258,-0.01409018,-0.00005245,0.00909524,0.05963057,0.04410129,-0.01082139,-0.01557191,-0.0487924,0.01285301,0.04525099,-0.06388138,0.01869331,-0.03208915,0.04371573],"last_embed":{"hash":"fea44ff9463058beb5f70546c78ea19c659c713af246035d9abb65818d726202","tokens":24}}},"text":null,"length":0,"last_read":{"hash":"fea44ff9463058beb5f70546c78ea19c659c713af246035d9abb65818d726202","at":1745995301628},"key":"README.md##Tutorials#{10}","lines":[52,54],"size":82,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Customization": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03570564,-0.04161192,0.05048382,-0.00409828,-0.01713668,-0.02693003,-0.11041178,-0.00969099,-0.00330262,0.0074935,0.01519047,-0.07268396,0.08396756,0.03336411,0.00975586,-0.00511217,0.00076347,0.02422231,-0.04084848,0.02832011,0.02275373,0.01627279,0.01055901,-0.00571081,-0.01472664,0.00348606,0.00205968,-0.02842139,0.00716945,-0.21848175,0.08970571,-0.03324771,0.0104733,-0.02037101,-0.04351893,-0.07419042,-0.0274011,0.04189462,-0.03710178,0.04672474,0.02590065,0.02086574,-0.0583016,0.03080849,0.00295675,-0.06265329,0.00492554,-0.01090283,0.07302742,-0.06160609,-0.02225103,-0.0847142,0.00423941,-0.03794818,0.0059473,0.04058675,0.0492974,0.05696172,0.04074484,-0.01676595,0.02280648,0.04566822,-0.13827729,0.11007448,-0.00156716,0.05903895,-0.06932819,-0.03924629,0.1100808,0.03311589,-0.0438676,0.00548845,-0.02402324,0.03479142,0.05186282,-0.02731593,0.01615254,-0.00320992,0.02414355,0.01505609,-0.04617593,0.05824773,0.03142472,0.01892879,-0.02403136,0.00608738,0.04898199,-0.04546412,0.03236755,0.02508742,-0.03671497,-0.05390519,0.01378751,0.0853386,-0.03559477,-0.05051705,0.0603034,-0.0227425,0.01233937,0.16297223,-0.02734287,-0.00346553,0.0019342,0.0313225,0.03234506,0.02270982,0.02361032,-0.01866017,0.00916017,0.05232064,0.02224474,0.02116173,-0.03797988,-0.10749442,0.03027614,-0.01769599,-0.03742434,0.05320349,-0.05763197,-0.01444878,-0.0091059,0.00864633,0.0151964,-0.00132492,0.02917597,0.03685361,0.00703019,0.02070238,-0.05261759,0.09820028,-0.01684198,0.08455954,-0.0686003,0.01623977,0.02263379,0.03695288,-0.01657594,-0.08827166,-0.01322712,-0.02825188,0.00279678,0.05223052,-0.00485201,-0.06617601,0.02735518,0.11501168,-0.0419754,0.06120691,-0.01721716,-0.10925084,-0.03755812,0.06447803,-0.08200198,0.00694994,0.02842107,-0.00605104,0.03157103,0.01282612,0.00796767,0.02549984,-0.02753473,-0.06013961,-0.05083762,0.11770108,-0.03226122,-0.09079427,-0.04562329,0.00175651,0.02813748,-0.04622178,-0.00159448,0.00361704,0.05396249,0.03933272,0.03794731,0.03958317,-0.12469704,0.01021883,0.01072058,0.00037284,0.03992197,-0.03204775,-0.01024716,-0.05066664,-0.02535835,-0.09919042,-0.05128847,-0.02186238,-0.05428495,0.06928197,-0.03829015,0.07684743,-0.02226422,0.01460883,0.01801893,-0.0157219,-0.01754441,-0.02923294,0.05343933,-0.05131606,0.0331806,-0.01326982,-0.03196935,0.00731582,-0.0558915,-0.0478803,0.03762607,-0.04551413,0.0454333,0.02824696,-0.09369733,-0.07396486,0.04723617,0.05160331,-0.07340623,0.01110059,0.01652213,0.0539249,-0.0292319,-0.01769925,-0.03432937,-0.00887093,-0.07791207,-0.19037148,0.01430203,-0.0034542,-0.00814946,0.06382211,-0.06258227,0.01244283,0.06476454,-0.06334932,0.09824829,0.11461441,-0.02647634,0.00705006,0.03729704,-0.02084178,0.00332034,-0.05510087,-0.04101989,-0.05427318,0.04044537,-0.01112459,0.01639855,-0.05375429,-0.07835887,0.00833706,-0.04536897,0.15761636,-0.05632564,0.06200425,0.03275343,0.05523353,-0.0151079,0.01530606,-0.16115089,0.01299687,0.04085979,0.01747948,-0.00266103,0.00724191,-0.03329388,0.01595046,0.06841601,0.00203267,-0.13634981,0.00859375,-0.04569656,-0.0400675,0.09186953,-0.01106581,-0.01515089,0.01034392,-0.01680283,0.02805048,0.01292584,-0.00146165,0.00088686,-0.02095302,-0.02327512,-0.03084176,0.03428464,-0.01008129,-0.02973274,0.02706626,-0.07848247,0.08961608,0.01686584,0.02859484,-0.03957263,0.07163116,-0.02357435,0.01228086,0.05313274,0.01029152,0.07519161,0.08370008,0.02720562,-0.04773122,0.00647902,-0.01411408,-0.06102403,0.00103756,-0.06345508,0.02303713,0.07118934,-0.02043422,0.07092591,-0.0555448,0.0073787,0.01027099,-0.00171508,-0.04781467,0.02499617,-0.03896007,0.01603599,0.03881978,-0.05247152,-0.19930919,0.01398704,0.03006334,0.02820127,0.01917661,0.02858547,0.03053604,0.00069241,0.04390197,0.03335455,-0.05582965,-0.0160577,-0.01757629,-0.00708657,0.03359597,0.03655365,0.08153377,0.03327445,0.05306538,-0.01645362,0.04441451,-0.01447762,0.19469653,-0.00186437,-0.02279301,0.01270881,-0.04851883,0.04293814,0.02531472,0.05622615,0.0442681,0.03323771,0.08196984,-0.02929925,0.06838206,0.02834457,0.03845494,0.03703284,-0.00511629,-0.0253065,-0.04928825,-0.03330271,-0.0670996,-0.00084161,-0.00129794,-0.06285994,-0.01491619,-0.04131096,-0.01193376,0.01672891,-0.00097756,0.07042752,-0.01594758,-0.00005297,0.03080858,0.02033908,0.06296226,-0.02805728,-0.04822455,0.06760697,-0.01118659,-0.08793334,0.05445704,-0.00779416,-0.01667142],"last_embed":{"hash":"e277f3ccf2a1b581be681d14bb31d01e821a7cd873a26b54b27b7c071ab52395","tokens":233}}},"text":null,"length":0,"last_read":{"hash":"e277f3ccf2a1b581be681d14bb31d01e821a7cd873a26b54b27b7c071ab52395","at":1745995301640},"key":"README.md##Customization","lines":[55,70],"size":901,"outlinks":[{"title":"Here is an example","target":"meshroom/nodes/blender/ScenePreview.py","line":12}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Customization#Custom Pipelines": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04704292,-0.03871706,0.04922084,-0.02199844,-0.03351169,-0.01772241,-0.08724021,-0.01393443,-0.00034297,-0.00059906,-0.00648213,-0.04781672,0.07713764,0.03671898,0.00527491,-0.00126739,0.01139024,0.05206959,-0.07469309,0.03798334,0.03482712,0.02308358,-0.0011112,0.00895724,-0.03237384,-0.00211356,-0.00166123,-0.03445313,0.00179448,-0.19286261,0.0772787,-0.03234249,-0.00513374,-0.01948408,-0.04440279,-0.08463541,-0.03869251,0.05105142,-0.03773787,0.07217476,0.04118675,0.02865518,-0.05393258,0.01294526,0.01732389,-0.0615187,-0.00804157,0.00604841,0.07077599,-0.04690694,-0.02730579,-0.04270609,0.00255103,-0.03973966,-0.00888368,0.03683696,0.04655521,0.05258204,0.04164683,-0.03839811,0.02196648,0.0349931,-0.16252294,0.11966525,-0.01581776,0.0718767,-0.05781974,-0.06709702,0.08544539,0.03095139,-0.03838386,0.00629831,-0.03084584,0.05115976,0.07603627,-0.03357746,0.01362776,-0.00490191,0.0227947,0.00936077,-0.02448806,0.02909696,0.04073783,0.01787908,-0.03220514,-0.00771986,0.05049353,-0.0451426,0.05147839,0.02987489,-0.04193228,-0.0640116,0.02455294,0.09895928,-0.03971402,-0.04226452,0.02898822,-0.01218823,0.0145276,0.16946268,-0.03624963,-0.00747516,-0.01060445,0.02427047,0.04268524,0.04419665,0.01172724,-0.02688034,0.00286096,0.03117297,0.01282903,0.02156437,-0.0193873,-0.09457532,0.04907141,-0.01188508,-0.03443916,0.0487183,-0.04925577,0.0063312,-0.01955864,0.00017098,-0.01826681,-0.00406673,0.0307781,0.01344175,0.0283049,0.01521086,-0.0739466,0.09446298,-0.01276702,0.06902558,-0.06872741,-0.00773661,0.01424984,0.0569368,-0.02267239,-0.0969905,-0.03070328,-0.02299407,-0.0089755,0.08522662,-0.01121413,-0.07030492,0.03958683,0.13004234,-0.03092809,0.05707999,-0.0006688,-0.10376293,-0.03502468,0.06282526,-0.04640153,0.01189036,0.01406073,-0.01614611,0.012315,0.00026955,0.00861908,0.02191689,-0.02668524,-0.04684862,-0.06146588,0.11389528,-0.03690517,-0.07819127,-0.04883398,-0.0200929,0.04102407,-0.04521381,-0.000183,-0.00803673,0.05331892,0.05510229,0.03587738,0.02998509,-0.10308214,-0.00750693,0.00332971,0.00878968,0.0425858,-0.03266402,-0.02401324,-0.04948992,-0.01852919,-0.08726797,-0.04391295,-0.0219545,-0.03901382,0.07152577,-0.02307816,0.08287334,-0.01805565,0.0058338,0.007846,-0.0153653,-0.01085527,-0.02549576,0.050319,-0.05207997,0.03246741,0.01067666,-0.02875613,0.00255169,-0.0393711,-0.03829617,0.02196789,-0.03992254,0.03420274,0.0360416,-0.0772538,-0.05779642,0.01786667,0.06304138,-0.08589291,0.00651709,0.02992569,0.05696111,-0.03502734,-0.02518106,-0.01278101,-0.01118359,-0.06589194,-0.20522188,0.02291509,-0.00796401,-0.02523356,0.06215674,-0.06456931,0.01709985,0.06241336,-0.05718931,0.11128932,0.11713916,-0.02899194,-0.01999071,0.04530311,-0.00589764,0.0024612,-0.0446227,-0.05103131,-0.07431609,0.04369909,-0.01899583,0.02413509,-0.06377599,-0.08731296,0.02849939,-0.04910138,0.14841071,-0.04601376,0.08212703,0.04408466,0.06635398,-0.01614128,0.05781695,-0.1605805,0.02852654,0.01962917,-0.00387592,-0.00543035,0.02500587,-0.04369815,0.00704762,0.06615709,0.00694702,-0.12910795,0.03898425,-0.06173149,-0.03792578,0.07891168,-0.03539716,-0.02289093,0.01327139,-0.02451587,0.02836019,0.00916105,0.00782237,0.01190835,-0.01694694,-0.0373246,-0.03690599,-0.00931615,0.01160976,-0.02002044,0.03784665,-0.07213536,0.0641559,-0.00704346,-0.00363398,-0.01547641,0.06963508,-0.03553001,0.00315232,0.03154137,-0.00861419,0.07288884,0.06383137,0.02790144,-0.04061962,-0.01165577,-0.0281796,-0.0501111,0.00481047,-0.06575412,0.01602049,0.06260208,-0.03326332,0.07554955,-0.07052859,0.02158227,0.00434716,-0.00664669,-0.0449816,0.02449441,-0.0376396,-0.00302787,0.02863801,-0.0524908,-0.19155951,0.02127967,0.03626883,0.00574841,0.01421851,0.028205,0.02835247,0.00497113,0.04850181,0.04309975,-0.0476336,0.00188068,-0.02939483,-0.01063773,0.03437952,0.02369117,0.08411761,0.03970072,0.04616238,-0.01611163,0.04715928,-0.00166598,0.2093728,0.02454988,-0.03030913,0.014051,-0.04119998,0.06263391,0.0258924,0.06158827,0.07717574,0.02990358,0.08809293,-0.01963121,0.05815057,0.03424606,0.02759779,0.04268223,0.00699933,-0.0088888,-0.04807473,-0.02402114,-0.06798644,0.0036474,-0.0144891,-0.06893793,-0.02183778,-0.04812744,0.01537287,0.0225691,-0.00064408,0.06008136,-0.01243247,0.00878153,0.06329284,0.02496878,0.07792857,-0.05671786,-0.03739866,0.06039618,-0.00302526,-0.05310527,0.02436288,-0.01155069,-0.0043975],"last_embed":{"hash":"229d7af4160085071c0e32eb9ffd6c8d9a7fccd093eff99a7247743c5c0dfe0d","tokens":103}}},"text":null,"length":0,"last_read":{"hash":"229d7af4160085071c0e32eb9ffd6c8d9a7fccd093eff99a7247743c5c0dfe0d","at":1745995301749},"key":"README.md##Customization#Custom Pipelines","lines":[57,62],"size":400,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Customization#Custom Pipelines#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04812493,-0.0374032,0.05175538,-0.01863262,-0.02815609,-0.01330202,-0.09222959,-0.01356132,0.00088902,-0.00253001,0.00081567,-0.04259071,0.07657211,0.0396239,0.01115765,-0.00129191,0.010534,0.05038254,-0.06843129,0.04006719,0.03529831,0.02509792,-0.00181798,0.01002862,-0.03842142,-0.00641472,-0.00413763,-0.03694671,0.00544951,-0.19375774,0.07682442,-0.03145067,-0.01085637,-0.0187127,-0.04426789,-0.0784353,-0.04096538,0.04660434,-0.03549348,0.07866212,0.03654733,0.02424615,-0.04937856,0.01311581,0.01883107,-0.06991161,-0.00102479,0.00684187,0.06943208,-0.04668647,-0.01791802,-0.04486906,0.00597469,-0.0404381,-0.00841664,0.03539759,0.04560148,0.05805742,0.03642567,-0.04180128,0.02810181,0.03547047,-0.1614562,0.11667977,-0.0145438,0.07160113,-0.06433353,-0.06332059,0.08161417,0.03570104,-0.04044805,0.00793097,-0.02925625,0.053789,0.07823335,-0.03246578,0.01124003,-0.00458927,0.01994532,0.00649214,-0.0207969,0.02772008,0.03816631,0.01994064,-0.02977296,-0.01329291,0.04979227,-0.04650436,0.05265699,0.02788684,-0.04230572,-0.06352394,0.02686548,0.09426079,-0.04061862,-0.04213537,0.03089087,-0.01099923,0.01085332,0.16931598,-0.03890663,-0.00660744,-0.01401511,0.02056671,0.04182196,0.03995191,0.01383116,-0.02595415,0.00230514,0.03189019,0.01413834,0.02587147,-0.02212758,-0.09226788,0.0449877,-0.01297598,-0.04074241,0.04597365,-0.0534773,-0.00158085,-0.01984434,-0.00463984,-0.01218962,-0.0096355,0.03085305,0.01954079,0.02740094,0.01671545,-0.06907242,0.09256876,-0.00859688,0.06455939,-0.06396826,-0.00857625,0.01423936,0.05387388,-0.02089217,-0.0935641,-0.03237846,-0.02959282,-0.00884039,0.08934488,-0.01517761,-0.07204685,0.04131422,0.13268526,-0.03145564,0.05627749,0.00118532,-0.10192291,-0.03809764,0.05623509,-0.04908929,0.0179137,0.0083673,-0.01815315,0.0154571,-0.00006772,0.0112404,0.02498799,-0.03352423,-0.05111752,-0.05779593,0.11230863,-0.03801351,-0.07755896,-0.05359988,-0.01906269,0.03832639,-0.04484278,-0.00166101,-0.00687059,0.04890659,0.05190368,0.03596089,0.03093171,-0.10184558,-0.00660178,0.0048912,0.00878448,0.03825236,-0.0341051,-0.02495338,-0.05215571,-0.01737873,-0.08722515,-0.04085494,-0.02107527,-0.04052361,0.07339377,-0.02415878,0.0861323,-0.01437948,0.00026656,0.01211346,-0.01806449,-0.01092694,-0.02656954,0.05019581,-0.05159869,0.02831369,0.00746536,-0.02544165,0.00231648,-0.04432041,-0.03982912,0.01939597,-0.04147135,0.03131608,0.03483154,-0.07530367,-0.06138509,0.02245608,0.06892524,-0.08568631,0.01356704,0.02812937,0.05737352,-0.03358482,-0.0239448,-0.01727722,-0.00347565,-0.0647376,-0.20605879,0.02221388,-0.01441921,-0.02443334,0.06784279,-0.06354112,0.01752757,0.05977405,-0.06037063,0.10232395,0.12045487,-0.0350703,-0.02280064,0.0472842,-0.00108232,-0.00071961,-0.04688872,-0.04812105,-0.07432065,0.0418194,-0.02306508,0.02219811,-0.05989743,-0.08538169,0.03011191,-0.04898522,0.1541314,-0.04556011,0.080639,0.03864666,0.06413499,-0.01376759,0.05197209,-0.1651922,0.03198582,0.02063566,-0.00712991,-0.00594741,0.02928627,-0.04800855,0.00654103,0.06363063,0.01284661,-0.12785734,0.03764128,-0.05574501,-0.03579824,0.07728098,-0.03897391,-0.01976428,0.01506549,-0.03205411,0.03142761,0.00211363,0.00843132,0.01086707,-0.01280429,-0.03657625,-0.03384909,-0.00783308,0.00627018,-0.01218481,0.03585382,-0.07624165,0.06522212,-0.00779765,-0.00185601,-0.02388572,0.07104132,-0.02812569,0.00493454,0.03630805,-0.00936443,0.07323238,0.0646396,0.02232909,-0.0412664,-0.01808378,-0.02785106,-0.05703368,-0.00030797,-0.0649713,0.01577122,0.06441814,-0.02869001,0.07723629,-0.07007904,0.02008534,0.00692131,-0.00472224,-0.04177253,0.01672184,-0.0349195,-0.00367408,0.02952553,-0.04473775,-0.18954314,0.02603046,0.03763376,0.00410666,0.01509692,0.02926118,0.02939235,0.00820271,0.04581329,0.04410857,-0.04330302,-0.00110591,-0.02490596,-0.00908713,0.03652019,0.01553186,0.08095868,0.03738813,0.05002996,-0.01507606,0.04444021,0.0054897,0.21581301,0.02484905,-0.02696637,0.01535108,-0.04318697,0.06702989,0.02990519,0.06385919,0.07064547,0.02408505,0.0918997,-0.02331606,0.06364414,0.03427808,0.02839267,0.03315129,0.00638025,-0.01193568,-0.04764348,-0.02312845,-0.07143587,0.00795021,-0.0120954,-0.06703177,-0.01776215,-0.04646203,0.01274455,0.01880162,0.00119257,0.05869979,-0.01185198,0.01002782,0.06854966,0.02632011,0.07701141,-0.05575802,-0.03493673,0.06249972,-0.0023767,-0.05107911,0.02914849,-0.01301254,-0.00984405],"last_embed":{"hash":"abeca01e7fbc9cca74e350d8c40443d6fc1346a8646c46d9ffbab24164a331b4","tokens":101}}},"text":null,"length":0,"last_read":{"hash":"abeca01e7fbc9cca74e350d8c40443d6fc1346a8646c46d9ffbab24164a331b4","at":1745995301794},"key":"README.md##Customization#Custom Pipelines#{1}","lines":[59,62],"size":378,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Customization#Custom Nodes": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02985382,-0.04104112,0.0576734,0.00438424,0.00797761,-0.00905146,-0.11258733,0.00039869,-0.00726828,0.00593681,0.06007678,-0.06954772,0.08877796,0.03691727,0.03404017,-0.01584503,-0.01011297,0.01057875,-0.00523312,0.027468,-0.0158984,0.02074843,0.01710178,-0.00289951,0.01163442,0.01477099,-0.01632445,-0.02624255,-0.00577447,-0.22194159,0.08803984,0.0036787,0.01146542,-0.03407663,-0.01698132,-0.06690084,-0.01015592,-0.00032935,-0.0278683,0.01623149,0.04150615,-0.00129783,-0.05754451,0.03053848,-0.00919789,-0.02719108,0.02942417,-0.0158619,0.06942743,-0.0599507,-0.015816,-0.10265468,0.05196911,-0.02764443,0.027017,0.06536516,0.04721109,0.05195858,0.04971366,0.0132748,0.00406568,0.06634587,-0.10076418,0.09811202,-0.01583413,0.03131692,-0.05632252,-0.03073608,0.11875775,0.03885539,-0.02137887,-0.01461567,-0.0182742,0.01989977,0.04337401,-0.00680214,0.01780539,-0.01099528,0.01907683,0.02992852,-0.08715982,0.06070888,0.01523315,-0.00988275,-0.02342742,0.02627998,0.03432364,-0.05587012,0.01287695,0.00958217,-0.04133257,-0.05027915,-0.01547848,0.07076641,-0.01116406,-0.03937377,0.05708513,-0.02650126,0.02901194,0.1678504,-0.03637724,0.00063666,0.02333757,0.03978522,0.01835727,-0.00590388,0.02237917,-0.00401676,-0.00635897,0.06843703,0.00649271,0.01712812,-0.06023088,-0.09383895,0.01937347,-0.00458345,-0.02942763,0.05339058,-0.01714016,-0.03763998,0.01232822,0.03930895,0.02791366,0.00334421,0.02648708,0.04000837,-0.00803908,0.02301705,-0.03312732,0.11005208,-0.01128063,0.08070064,-0.0835891,0.03064242,0.04495624,-0.0015278,0.01220936,-0.094345,-0.00689827,-0.00961812,-0.00313932,0.02174228,0.02689018,-0.07378834,0.00614411,0.09345214,-0.0759247,0.05497076,-0.03439725,-0.09284319,-0.04358638,0.04601571,-0.11797104,-0.01371543,0.01630666,0.01177543,0.06800508,-0.00942639,-0.00802316,0.01146856,-0.02987196,-0.02111064,-0.0445445,0.13329114,-0.04337123,-0.0846576,-0.04213171,0.03365742,0.02748846,-0.05474483,-0.00903162,0.03338217,0.02802197,0.03556502,0.05595116,0.04940265,-0.15790167,-0.0087088,-0.00177991,-0.00711645,0.02251639,-0.05032361,-0.0005699,-0.04050595,-0.03833726,-0.08108647,-0.01941718,-0.01859793,-0.04533111,0.05283384,-0.02525513,0.03829004,-0.02416707,0.03098113,0.02406449,-0.00859007,-0.04122635,-0.01587829,0.06194296,-0.05660314,0.03992378,-0.02134323,-0.02241244,0.01917257,-0.03786983,-0.03572083,0.04058846,-0.04763245,0.05355918,0.00861996,-0.12670094,-0.08193552,0.07911221,0.03890026,-0.05791251,0.01735466,-0.01332476,0.04409911,-0.02331869,-0.01030345,-0.03133297,-0.02955778,-0.10501987,-0.17711243,0.00245134,-0.01275799,-0.00989593,0.01601866,-0.05899738,-0.01616173,0.07990315,-0.05496819,0.09770215,0.11001612,-0.00049115,0.01626965,0.02702641,-0.03364404,-0.00764761,-0.04886945,-0.00281734,-0.02936393,0.00438542,0.00873161,0.02659304,-0.04830496,-0.04325403,-0.0097249,-0.01900213,0.15632659,-0.05353216,0.05888999,0.0393385,0.06325259,-0.03313667,-0.00722426,-0.13918242,-0.03663364,0.06516286,0.01206141,-0.00420627,-0.00501836,-0.02536406,0.03871322,0.0532244,-0.0064474,-0.16135147,-0.00328481,-0.03562729,-0.03140496,0.05076484,0.01867426,0.0251118,-0.00369551,-0.01206976,0.00441798,0.03146182,-0.03213984,-0.00256514,-0.03404221,-0.01304112,-0.01884869,0.08834516,-0.03899636,-0.03883661,-0.00437158,-0.08193557,0.10911235,0.05099031,0.0569668,-0.07759238,0.07119685,-0.02024461,0.00906375,0.0639777,0.03411111,0.04319384,0.07011691,0.03224327,-0.04095835,0.03695235,-0.00573914,-0.05548384,0.01345491,-0.01695978,0.02778672,0.08379408,-0.01214499,0.055509,-0.02819572,-0.00635787,-0.00211603,-0.01474776,-0.02649172,0.04198415,-0.0417296,0.02805129,0.04251729,-0.05652481,-0.19836369,0.0186686,0.01848047,0.05276136,0.00174011,0.01533674,0.01073416,-0.02208059,0.0244133,0.00688092,-0.02898773,-0.0343167,-0.00940368,-0.0158997,0.01840721,0.04648036,0.05027577,0.04119118,0.0582917,-0.01706482,0.04804521,-0.02336677,0.20514411,-0.04307472,-0.00372043,0.0369963,-0.04276101,0.00307348,0.02881138,0.06160764,0.01769398,0.03253759,0.08122694,-0.02914242,0.04600876,-0.0084815,0.06156988,0.03732152,-0.01193853,-0.0224242,-0.04686302,-0.04324023,-0.05817684,-0.02027285,0.01291394,-0.05375085,-0.01963136,-0.02425972,-0.04948226,0.03842209,-0.01867687,0.05608814,-0.02737493,-0.01583942,0.00585441,0.00364187,0.03775782,-0.00552596,-0.05090702,0.06456035,0.01488066,-0.12100657,0.0570179,0.01239159,-0.02246498],"last_embed":{"hash":"e39d58bdeebc583cebc7700a09cc763d7f5dc1f9d62fc8b7e87ffb88774f6a3a","tokens":137}}},"text":null,"length":0,"last_read":{"hash":"e39d58bdeebc583cebc7700a09cc763d7f5dc1f9d62fc8b7e87ffb88774f6a3a","at":1745995301839},"key":"README.md##Customization#Custom Nodes","lines":[63,70],"size":482,"outlinks":[{"title":"Here is an example","target":"meshroom/nodes/blender/ScenePreview.py","line":4}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Customization#Custom Nodes#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02986216,-0.03888741,0.05998774,0.00684949,0.00523623,-0.00754334,-0.11125755,0.00167148,-0.01032904,0.00497141,0.05914571,-0.06731243,0.08695436,0.04005125,0.03829125,-0.01575111,-0.01015691,0.00920093,0.00322393,0.03458654,-0.01914225,0.01937773,0.022175,-0.00824378,0.00722453,0.01213302,-0.00480362,-0.02362229,-0.00432238,-0.21898988,0.09227968,-0.00348409,0.01290445,-0.03294173,-0.01526451,-0.06987641,-0.00784765,0.0012701,-0.03072123,0.01628779,0.03546608,0.00178207,-0.05332771,0.0290071,-0.01387353,-0.03145322,0.03563717,-0.01581194,0.07685322,-0.06769636,-0.01166503,-0.09991921,0.04935941,-0.02606383,0.0231374,0.06658754,0.04386915,0.05840553,0.05056883,0.01119,0.00434483,0.06902399,-0.0929866,0.09237755,-0.01386986,0.03283139,-0.05879818,-0.02570958,0.11846346,0.04213449,-0.02064412,-0.00981625,-0.01544902,0.01864907,0.04350584,-0.00408322,0.01284122,-0.00946203,0.01888832,0.02001267,-0.0898524,0.06021158,0.00992499,-0.00566219,-0.02245145,0.03343044,0.04289935,-0.05692576,0.01518416,0.00944862,-0.04407592,-0.0512908,-0.01464252,0.06696217,-0.0102615,-0.04004671,0.05856735,-0.0254886,0.02685991,0.17226888,-0.03423445,0.00471411,0.02007391,0.03684634,0.01692376,-0.0093126,0.02267026,-0.00847544,-0.00714201,0.07451661,0.01255314,0.01850441,-0.05881826,-0.09060272,0.01104206,-0.01428721,-0.02755886,0.05252171,-0.0195036,-0.04333434,0.01304018,0.03779608,0.02794251,0.00279164,0.02949333,0.03571491,-0.00910945,0.01449973,-0.03033822,0.10795288,-0.00996671,0.07454358,-0.08584584,0.03082538,0.04588198,-0.00449717,0.01195562,-0.09853708,-0.01161523,-0.01653736,0.00266818,0.01776496,0.02285758,-0.0713882,0.003836,0.09006982,-0.07522205,0.05820054,-0.03191362,-0.09268305,-0.03941977,0.0483136,-0.12207161,-0.00858482,0.01626087,0.01091671,0.05715074,-0.00507089,-0.01425506,0.01302388,-0.03571444,-0.02184509,-0.03683956,0.13225055,-0.04567385,-0.0847505,-0.04160027,0.03468842,0.02457009,-0.05161176,-0.00944311,0.02765235,0.034808,0.02268458,0.058298,0.04914204,-0.16043608,-0.00464495,-0.00567334,-0.01078246,0.01337543,-0.04681154,0.0018144,-0.0404075,-0.03854063,-0.08279692,-0.02370881,-0.02464714,-0.04724565,0.05144496,-0.02673998,0.04610723,-0.02498413,0.02486426,0.02592433,0.00017975,-0.04003622,-0.0151221,0.05892825,-0.05670373,0.04220744,-0.0225786,-0.02218701,0.02380502,-0.0438209,-0.03911204,0.03817638,-0.04940754,0.05605099,0.00624072,-0.12867239,-0.0806164,0.08541172,0.03870155,-0.06019451,0.01856919,-0.01543071,0.04372954,-0.02035357,-0.00930344,-0.03342039,-0.02950499,-0.10501677,-0.17639422,-0.00646615,-0.01762426,-0.01129951,0.0173212,-0.05299106,-0.01387314,0.07783891,-0.05448887,0.09503032,0.11083904,-0.00526692,0.01988984,0.02849435,-0.03779427,-0.00193014,-0.05045314,0.0026256,-0.02541116,0.00634305,0.00379126,0.01945599,-0.04531268,-0.04122644,-0.00255728,-0.01573,0.15590355,-0.0486554,0.05388822,0.03689875,0.06494217,-0.02834046,-0.00882167,-0.14125918,-0.03339176,0.06831818,0.01000849,-0.00311925,-0.00424588,-0.02343277,0.03788402,0.04757616,-0.00758529,-0.15691561,0.00170822,-0.0316535,-0.03230323,0.05820816,0.01860431,0.01842761,-0.00195725,-0.02307391,0.00432056,0.03181038,-0.03194924,-0.00527627,-0.03354244,-0.01507154,-0.0188827,0.09172604,-0.04180764,-0.03473774,-0.00754696,-0.0796494,0.11017827,0.05351563,0.05494289,-0.0729666,0.06950955,-0.01988248,0.011701,0.06958403,0.0282854,0.04548435,0.07946888,0.03155108,-0.0393735,0.03441958,-0.00376656,-0.05307545,0.01330321,-0.01556388,0.02804729,0.08938917,-0.00694633,0.05210594,-0.03016548,-0.00823392,-0.00047049,-0.0107972,-0.02925635,0.04212907,-0.04557072,0.03808608,0.04672372,-0.05867908,-0.19763692,0.01440695,0.02049039,0.05218828,0.00435945,0.01335803,0.00806219,-0.01988406,0.02573648,0.00670286,-0.02306295,-0.0322536,0.0002807,-0.01025367,0.0160834,0.04101193,0.05359431,0.03257889,0.06052126,-0.01764078,0.04723222,-0.0261752,0.19848113,-0.04784494,0.0033114,0.0384059,-0.04848463,0.00469831,0.02437418,0.06092717,0.01692123,0.02990374,0.08552007,-0.03568993,0.05217709,-0.00423373,0.06265466,0.03433758,-0.0156194,-0.01915199,-0.04898145,-0.04248098,-0.05812853,-0.02426438,0.01192629,-0.05476777,-0.01893433,-0.01018143,-0.05351768,0.03718207,-0.01823556,0.0558297,-0.02832527,-0.02038725,-0.00090219,0.00947069,0.03446899,-0.00685347,-0.06392107,0.06219525,0.01930439,-0.11764038,0.06377064,0.01687404,-0.02867579],"last_embed":{"hash":"665835291e575095bd3e892747cb8d21630f73f9d4530d190694a9856a878c56","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"665835291e575095bd3e892747cb8d21630f73f9d4530d190694a9856a878c56","at":1745995301901},"key":"README.md##Customization#Custom Nodes#{1}","lines":[65,70],"size":464,"outlinks":[{"title":"Here is an example","target":"meshroom/nodes/blender/ScenePreview.py","line":2}],"class_name":"SmartBlock"},
"smart_blocks:README.md##License": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10333337,-0.02033149,-0.04301878,-0.10340179,-0.00372765,-0.00438105,-0.06985749,0.05053261,-0.0106963,0.02605831,0.01171685,-0.05518486,0.05253235,0.00818492,0.01672758,0.02299309,0.04126241,-0.03349688,0.03681635,-0.00416632,-0.01375983,-0.02091603,-0.00462344,-0.01058459,-0.00406099,0.0433606,-0.04737774,0.0093833,-0.0335367,-0.20987198,0.02379199,-0.014123,-0.02669517,0.05514095,0.0224927,-0.08021503,-0.05826081,-0.00742684,-0.00188847,0.03665285,0.00341877,0.01897516,-0.05292169,-0.07505592,0.01995826,-0.06388657,0.01681634,0.00622047,0.05516805,-0.02611159,-0.0575087,-0.00051951,0.00717715,-0.01181956,-0.0116344,-0.00017663,0.03442726,0.06689422,0.01752317,0.0014387,0.03898,0.03088771,-0.2074769,0.09331723,0.01719573,0.06473102,-0.00538376,-0.05280329,-0.01561524,0.02176315,-0.04844734,0.07287221,-0.02622989,0.06961752,0.07104248,-0.033137,0.00476448,-0.04782853,-0.01699116,-0.05746865,-0.02406212,-0.01854108,-0.00011131,-0.05670902,-0.03594332,0.02201498,0.04085593,0.06142623,0.09482908,0.00579177,-0.04240423,-0.0910829,0.03556876,0.04505894,-0.08135515,0.06194576,0.04920834,0.02337596,0.0292718,0.11945157,-0.05617206,-0.00163595,-0.01576248,-0.01491716,0.06054777,-0.00374007,-0.02749238,0.02398919,-0.03947022,-0.05039486,-0.01944753,0.02150447,0.09080688,-0.0060503,0.0423093,0.04499182,0.03369983,0.06290769,-0.03132353,0.04421641,0.02501916,-0.00134209,0.02541607,-0.01078977,0.01236152,0.00287077,0.04157829,0.02754317,0.05115081,0.03743738,0.02384407,0.04228948,-0.05504937,-0.03653504,0.02025128,-0.02006666,-0.00031705,-0.01393044,0.00517217,-0.02782144,-0.01702574,0.04948965,0.00335776,-0.07087082,-0.02483111,0.05834203,0.03197393,0.01254641,-0.01903953,-0.01286092,0.03296637,0.04689733,-0.06697846,-0.0058468,0.04582364,-0.00049813,0.12970562,0.0445543,-0.07970919,0.0495648,0.0381707,-0.01583287,-0.03885336,0.13391477,-0.01982275,-0.08570838,-0.04267122,-0.05953304,-0.00705638,0.01259183,-0.01456234,0.04237361,0.02715129,-0.00965187,0.0388231,0.00492797,-0.05933017,0.01750232,0.00944978,0.00135097,0.00848131,-0.07761459,-0.08017766,-0.02362564,0.03038694,-0.06139652,-0.02975967,-0.04251529,0.00014276,0.07467377,-0.0152145,0.0455135,0.01997197,-0.0063217,-0.00327459,-0.04415291,-0.01710477,-0.00322017,0.07104076,-0.09383681,0.03654844,0.05651484,-0.03716525,-0.01792925,-0.01800293,-0.01243004,0.04633141,-0.029993,-0.00807279,0.06014728,0.00310678,-0.00259991,-0.00143336,0.0130379,-0.07689004,-0.0268097,0.0218063,0.07642565,0.00658354,0.06928363,-0.09923878,0.01568048,-0.02960484,-0.22474827,-0.00341843,-0.03771977,-0.02721682,-0.0187776,-0.02869575,-0.013646,0.00332756,-0.00418191,0.04228201,0.20803255,-0.00745112,-0.03299118,-0.00575021,-0.03824463,0.01067142,-0.05666635,-0.00879088,-0.02168976,-0.01340471,0.02141715,0.01131778,-0.0616045,-0.00505261,0.15812321,-0.0331521,0.13834117,0.04662576,0.07918964,0.04826482,0.07179964,0.03526123,0.01814429,-0.17436066,-0.07790092,0.04538986,-0.0025004,0.12539789,-0.02897293,0.01637985,0.00965476,0.02362616,0.00133917,-0.11864112,-0.01646489,-0.01343837,-0.01246274,0.03257245,-0.03306207,-0.00436831,-0.00115256,-0.0041549,0.00449918,-0.00648961,-0.01596421,-0.00294766,-0.02471716,-0.00413125,0.05559251,-0.0061912,0.00035981,-0.00565106,0.05581147,-0.07711824,0.00663416,-0.02552975,0.00957768,-0.0308954,0.07883924,0.00483341,-0.00562502,0.03257645,-0.0182833,0.02380814,-0.00097609,0.03158867,-0.02189812,-0.03568939,-0.02906828,-0.03672995,-0.01440553,0.06808692,0.00018266,0.05418541,0.04274327,0.00971136,-0.03932613,-0.01004246,-0.06544463,-0.01249818,-0.01605307,-0.02433363,-0.03678309,-0.0273954,0.06110404,-0.00449727,-0.22734033,-0.00601104,0.03985333,0.02847799,-0.03591138,-0.00172703,0.08159532,-0.03587398,-0.03405731,-0.02729656,-0.01342596,0.04860247,-0.00357779,0.00098549,0.02911761,0.02746824,0.06907694,0.03046153,0.0662194,-0.03346739,0.01171507,-0.03023262,0.1680088,-0.0519815,0.00475727,0.01156414,-0.04049019,0.04553381,0.09874979,0.05375633,-0.05365854,0.01206544,0.08307465,-0.02253309,-0.0315843,0.03614781,-0.05098238,0.03321519,0.05083668,0.01417999,0.00945974,0.02512398,-0.04881858,0.00192738,0.0282346,-0.0239962,-0.05659286,-0.07469196,-0.04108901,0.06249083,0.01629052,-0.03741486,0.00487537,0.02974519,0.00771028,0.01996654,0.03863061,-0.03197352,-0.01974859,-0.03261323,0.05469985,-0.06191704,0.03221137,0.0512454,0.03753185],"last_embed":{"hash":"43e6cf51fa2e47cc52c716fd3e415c60283ef06d4ec8aea59f2d1a37aabd0725","tokens":34}}},"text":null,"length":0,"last_read":{"hash":"43e6cf51fa2e47cc52c716fd3e415c60283ef06d4ec8aea59f2d1a37aabd0725","at":1745995301961},"key":"README.md##License","lines":[71,75],"size":84,"outlinks":[{"title":"**COPYING.md**","target":"COPYING.md","line":3}],"class_name":"SmartBlock"},
"smart_blocks:README.md##License#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10377616,-0.01774871,-0.04232256,-0.09942835,-0.00544587,-0.00164722,-0.07010864,0.0499564,-0.00549538,0.02079322,0.01188693,-0.0563759,0.06198657,0.0080156,0.01580876,0.01679313,0.04381438,-0.03201865,0.04563663,-0.00486137,-0.0255753,-0.0213688,-0.00591804,-0.01194787,-0.00585477,0.04702,-0.04921623,0.00062375,-0.03721749,-0.20583063,0.03011778,-0.01598687,-0.02974856,0.05477484,0.0139026,-0.08123527,-0.05798643,-0.00457616,0.0089716,0.02597695,-0.0057682,0.01153181,-0.05251458,-0.07667479,0.01065334,-0.06553688,0.01818668,0.0102045,0.05221431,-0.02580558,-0.06674483,0.00371961,0.0060294,-0.00383691,-0.01666696,-0.00528387,0.02245473,0.07051604,0.01313201,-0.00331276,0.02843558,0.02930765,-0.19758859,0.09132434,0.00381629,0.07316661,-0.00162565,-0.06373461,-0.01456628,0.01713282,-0.04708633,0.0699077,-0.03009807,0.06400485,0.07262727,-0.03687598,0.00282174,-0.04387686,-0.01547425,-0.05123867,-0.02688478,-0.01946309,0.00394486,-0.05985247,-0.0396243,0.01437524,0.04515234,0.05795655,0.10164078,0.00107889,-0.04021806,-0.08354036,0.03345005,0.04318849,-0.08148808,0.06084718,0.04290649,0.02438783,0.0342118,0.12681901,-0.06232219,0.00462769,-0.02069215,-0.01710465,0.07010936,-0.00839847,-0.04253007,0.02657161,-0.04636372,-0.05005968,-0.02231305,0.02149675,0.09476124,-0.00082182,0.03804624,0.05200111,0.02693616,0.06256761,-0.02667106,0.04278187,0.02881015,-0.00140226,0.03056508,-0.01579771,0.00726635,0.00382368,0.04404755,0.02893498,0.04548625,0.03233669,0.02503779,0.0293536,-0.05238887,-0.03849306,0.02368194,-0.01533136,-0.00339942,-0.01752877,0.00557596,-0.02222464,-0.01893582,0.05310324,0.00851938,-0.06081139,-0.02942313,0.05231655,0.04060195,0.01654536,-0.01984977,-0.01109674,0.0420673,0.03840194,-0.07060517,-0.00374518,0.04414662,0.00072112,0.14144938,0.04013417,-0.08487745,0.04489131,0.04484538,-0.01773643,-0.03582239,0.12406514,-0.02324044,-0.07384988,-0.04545679,-0.0597444,-0.01075202,0.02093712,-0.00992636,0.03596365,0.02729921,-0.00284134,0.03497179,0.00834334,-0.06035975,0.01350872,0.01025241,-0.00434806,0.01062512,-0.07940457,-0.0864142,-0.02045195,0.03008936,-0.06393357,-0.03026852,-0.04185579,-0.00524471,0.07105255,-0.00116037,0.05227095,0.01501092,-0.0073732,0.00776355,-0.03814051,-0.02566847,0.00553952,0.07412276,-0.09311307,0.03011391,0.05821415,-0.0304866,-0.02445294,-0.01850871,-0.02049513,0.04345704,-0.03172829,-0.02149307,0.0525939,0.00810127,-0.00444657,-0.01067807,0.01629457,-0.07857134,-0.02718609,0.02757929,0.07291332,0.00417831,0.07153719,-0.09033202,0.02229937,-0.02415556,-0.22111504,-0.00193399,-0.04224059,-0.01906574,-0.02192662,-0.02508851,-0.01412125,0.0191702,0.00283795,0.04496422,0.1988285,-0.00257209,-0.0266146,0.00420536,-0.03659362,0.01256761,-0.05985979,0.00306036,-0.02845732,-0.01290138,0.02844148,0.01629498,-0.05539679,0.00226876,0.17112906,-0.02918405,0.14409061,0.04207386,0.0747679,0.04055446,0.06421128,0.03324791,0.02789619,-0.17491917,-0.06880727,0.04048167,0.00301977,0.12933044,-0.02638689,0.01318977,0.00357214,0.02134912,-0.00655353,-0.11510898,-0.01567938,-0.00002507,-0.00900623,0.03536205,-0.04000528,0.00771557,0.00460008,-0.00539836,0.00483038,-0.00887597,-0.01560578,-0.00015594,-0.02292697,-0.0045944,0.06092284,-0.00345756,0.00208414,0.00025756,0.05610477,-0.08148663,0.01595549,-0.03191142,0.0150784,-0.04077128,0.07247862,0.00668796,-0.00964373,0.04048227,-0.01711803,0.02621466,-0.00289437,0.02315315,-0.02879897,-0.04069506,-0.02189929,-0.03820015,-0.02009013,0.06766681,-0.00501791,0.05606167,0.03969309,0.00599116,-0.03589819,-0.01660238,-0.07351787,-0.01459976,-0.0078238,-0.0278962,-0.03340083,-0.03060463,0.05620695,0.00489175,-0.22104184,-0.00648704,0.04080881,0.03717727,-0.03831037,-0.00221706,0.08073654,-0.03343745,-0.03585819,-0.03592355,-0.00480688,0.05205189,-0.01432795,0.00445293,0.01562626,0.02085092,0.07556985,0.02773439,0.07503785,-0.03994017,0.00573309,-0.02800372,0.1676776,-0.05517844,0.00220006,0.01100849,-0.04078303,0.03400346,0.08992692,0.05419586,-0.05230656,0.0129911,0.08803125,-0.0319451,-0.03239766,0.04934353,-0.04584643,0.03461228,0.04806498,0.01585564,0.01987747,0.03084439,-0.05629059,0.00774886,0.02717824,-0.01957686,-0.05008246,-0.08142289,-0.04004641,0.06711551,0.02166895,-0.03876356,0.00965082,0.02819514,0.00048301,0.01384284,0.03855458,-0.03045717,-0.00997712,-0.03612731,0.06094993,-0.06655145,0.02867814,0.04543875,0.03000033],"last_embed":{"hash":"157a0262178c10ac49c27269560616138eee3b0b86a6e807b3506444759bccff","tokens":33}}},"text":null,"length":0,"last_read":{"hash":"157a0262178c10ac49c27269560616138eee3b0b86a6e807b3506444759bccff","at":1745995301976},"key":"README.md##License#{1}","lines":[73,75],"size":72,"outlinks":[{"title":"**COPYING.md**","target":"COPYING.md","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Citation": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08910362,0.01354137,-0.01745356,-0.00935965,0.01903299,-0.00492868,-0.09060398,0.06402127,-0.03404344,0.04939133,-0.01360923,-0.09273907,0.06587619,0.03653497,-0.00262389,0.02378527,0.01682214,0.07436308,0.04089435,0.01829008,0.08933434,-0.06398871,0.03514974,-0.03997542,-0.00206429,0.03166636,-0.00289558,-0.04762376,-0.03059273,-0.28422239,-0.03727332,0.03558723,0.02970012,0.00979763,-0.01243726,-0.04369688,-0.02464045,0.00265457,0.0007813,0.07371061,0.00035989,0.03499042,-0.02719159,-0.03765683,-0.03139719,-0.04338035,-0.0108159,-0.01865519,-0.04177836,-0.06556752,-0.07572988,0.01509836,0.02602329,0.00656772,0.01386393,0.10762785,0.04046258,0.02512526,0.02180726,-0.01117318,0.09587559,0.06484167,-0.24074149,0.10637651,0.05522244,0.04882707,-0.0000715,0.0110112,0.09409475,-0.01846546,-0.05378406,0.03435176,-0.01399639,0.01307133,0.03935015,-0.00851201,0.01491772,-0.02048072,-0.03507511,-0.05475551,0.03461068,0.01837076,-0.00942723,-0.05763847,-0.01789228,-0.03693735,0.04565904,0.01267945,0.04457993,-0.01781197,-0.01070181,-0.04850967,0.04005628,0.03565365,-0.04197235,0.00946571,0.01923265,-0.00447216,-0.00777088,0.08165692,-0.00923306,0.02717428,0.04891049,-0.00228744,-0.00856304,-0.0105001,0.00198135,-0.0421,-0.0368791,0.0098533,0.01367452,-0.01690084,0.01651713,0.02934039,0.04750836,0.01673055,0.05003434,0.05013038,0.03476229,-0.01079491,-0.0316296,0.01649529,0.01860155,0.00077085,0.0082883,-0.02518454,0.02867401,0.04670006,0.04134433,0.02094495,-0.01063091,0.06266399,-0.06889188,-0.03557993,-0.00318388,0.00286233,0.0566615,-0.06126608,-0.00751834,-0.00874428,0.03283685,-0.03290795,-0.03431469,-0.08169767,-0.04839145,0.11551446,-0.01299544,0.03582644,-0.0786888,-0.04257392,0.03623869,0.04683866,-0.02480768,0.02490474,0.0398083,-0.01460098,0.05357832,0.09094387,-0.06929167,0.04578486,0.00229975,-0.06140951,-0.02710932,0.11644556,0.02050924,-0.06370789,-0.01277972,0.03195231,0.00229833,-0.06048289,0.00296938,0.0210313,0.00222184,0.01260931,0.05992439,0.02609751,-0.10504144,-0.02365017,0.00695218,0.02547254,0.00996546,-0.04718262,-0.02425378,0.00455677,-0.01035291,-0.07297032,-0.03596478,-0.0390574,-0.02295669,0.13259839,-0.04687142,-0.01372103,-0.00188976,0.05765242,-0.00456261,-0.00541402,-0.02500126,0.01445313,0.02153761,-0.06968986,-0.03908642,-0.00482106,-0.06243266,0.02369258,0.04767949,-0.00804329,-0.03382523,-0.04557462,0.01683057,0.08232033,-0.00979402,-0.04311619,0.01974234,0.00281926,-0.05951058,0.01804193,0.05154627,0.05624251,-0.03735317,0.03748556,-0.02447613,-0.03277294,-0.0796892,-0.22543354,-0.00383299,-0.00292648,-0.05336258,0.0721833,-0.04046861,0.00352045,-0.05420634,0.02702509,0.05331602,0.04613952,0.01443076,-0.03254512,0.02799014,-0.02653565,-0.02430143,-0.06407061,-0.03615131,-0.03850769,0.02439772,0.01076675,0.03087066,-0.02395765,-0.03008328,0.02277789,-0.06164239,0.16785972,0.03411965,0.02285017,0.05709965,0.03458161,0.03909888,-0.01064864,-0.11409799,-0.01422186,0.06023449,0.01207296,0.04858143,-0.00690996,0.01300834,-0.03510727,0.02652244,-0.001731,-0.09511473,-0.03259262,-0.00695456,-0.07140629,-0.01541113,-0.00588907,0.05806288,0.01212971,-0.02639065,0.02689423,0.00325311,-0.02063059,0.02487706,-0.07391948,0.00733563,-0.01670902,-0.02032745,0.02270975,0.01746082,0.02489777,-0.11617497,0.06829704,-0.00676706,-0.02170124,0.01391635,0.00506218,-0.03069935,0.02745901,0.07173406,-0.01589275,-0.01124549,0.04359652,0.02715051,0.02980958,-0.07892653,-0.05838744,-0.00917673,0.04269783,-0.03703403,0.01371495,0.08344875,0.02708452,0.05626411,-0.05489187,-0.0198122,-0.02392989,-0.00954216,-0.02551522,0.02505896,-0.05786705,-0.01298956,0.02856006,-0.00278347,-0.26380414,0.04797737,0.04507477,0.0200746,0.01539015,0.02548305,0.07850752,-0.05845809,0.0080216,0.01267437,-0.04899195,0.02926137,-0.0238074,0.00234267,0.0310181,0.04882856,0.0715831,-0.04128886,0.00377517,-0.05953606,0.01679162,0.04426522,0.18429761,-0.0180373,-0.01588856,-0.01051678,-0.01970622,0.07850869,0.03267771,0.03538364,-0.00057956,0.01022911,0.05306301,-0.02258877,-0.00124154,0.09510603,-0.03550384,0.02915099,0.03951624,0.04385092,-0.02043552,-0.01501459,0.03468832,0.05646977,0.10103756,-0.0415053,0.02449138,-0.09671906,-0.00030507,0.02874766,-0.03251689,0.02771169,0.01812698,-0.00252748,0.01303141,0.05029475,-0.03355739,-0.01712287,-0.05159187,0.00931206,0.02796158,-0.01627662,-0.02161067,0.03269242,0.03677708],"last_embed":{"hash":"c5cbc00a1cae3de5aeafb805371d67835139a1029f5acbdee8aba81c8ef672f8","tokens":199}}},"text":null,"length":0,"last_read":{"hash":"c5cbc00a1cae3de5aeafb805371d67835139a1029f5acbdee8aba81c8ef672f8","at":1745995301992},"key":"README.md##Citation","lines":[76,89],"size":599,"outlinks":[{"title":"paper","target":"https://hal.archives-ouvertes.fr/hal-03351139","line":3}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Citation#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09091907,0.0155701,-0.01814099,-0.00646227,0.01134292,0.00020878,-0.08898375,0.06790553,-0.03658335,0.04916858,-0.00896624,-0.07960667,0.06817057,0.03850216,-0.00188342,0.02215397,0.01148779,0.0743162,0.05334001,0.00585327,0.08922754,-0.06758269,0.03907875,-0.03472858,0.00201235,0.03360423,-0.01082014,-0.05038799,-0.02472322,-0.27360409,-0.03191479,0.04006446,0.02301956,0.00906142,-0.01472997,-0.03112095,-0.02505773,0.01213864,0.00445102,0.06942051,-0.00139946,0.03885625,-0.02515816,-0.03152794,-0.03728328,-0.03941052,-0.00565538,-0.00955931,-0.04220447,-0.05850629,-0.07047283,0.01021298,0.02602329,0.00660737,0.01375903,0.11127048,0.03904007,0.03085021,0.0247515,0.00328561,0.09003926,0.06869389,-0.24580336,0.10691522,0.05301267,0.05246197,-0.00813496,0.01318786,0.08307439,-0.01190119,-0.06017612,0.03462126,-0.01317131,0.01520209,0.04233546,-0.00709712,0.01925039,-0.02013668,-0.03948774,-0.05255477,0.02820304,0.0290811,-0.01190069,-0.05832417,-0.01557661,-0.03899234,0.05386573,0.00941672,0.05021215,-0.01250759,-0.00588553,-0.04414096,0.03189441,0.03260371,-0.03383616,0.01036346,0.03086846,-0.00788553,-0.00533199,0.08009987,-0.02243415,0.03328361,0.05621517,0.0033268,-0.00368881,-0.01878686,0.00189452,-0.03645398,-0.02891293,0.01627272,0.01081254,-0.00359172,0.02055816,0.03212462,0.04660407,0.01389969,0.03575029,0.03986691,0.03710619,-0.01576339,-0.03574689,0.02788942,0.03112513,-0.00395757,-0.00293809,-0.028329,0.02256583,0.05226659,0.04521872,0.01541414,-0.01147697,0.06254844,-0.06661148,-0.04110744,-0.00696164,-0.00053159,0.06289107,-0.07474487,-0.01049363,-0.00669553,0.05128407,-0.02847676,-0.04007292,-0.07301431,-0.05127167,0.11236189,-0.01356352,0.02762732,-0.07940094,-0.04444329,0.03603507,0.05088987,-0.03072389,0.02887454,0.04577594,-0.01205954,0.04767982,0.10329234,-0.07864099,0.04272969,0.00476393,-0.05755191,-0.02881117,0.10981202,0.01309246,-0.05662072,-0.0122352,0.03709482,0.00387689,-0.05896449,-0.00250322,0.01734856,0.00475568,0.014521,0.05634287,0.02419871,-0.11221424,-0.02491831,0.01074493,0.02753263,0.01148283,-0.04531521,-0.02420597,0.0087656,-0.02040449,-0.07179197,-0.02986991,-0.03647142,-0.02000435,0.13437204,-0.05318484,-0.01200561,-0.00520436,0.05909261,0.00335655,0.00160702,-0.02427378,0.01212418,0.02736705,-0.07153014,-0.04934033,-0.00698205,-0.06085659,0.01835197,0.04883494,-0.01776323,-0.02767742,-0.04089428,0.01859082,0.06739299,-0.01862706,-0.053968,0.01811751,-0.0002286,-0.05589593,0.01663834,0.05059484,0.04787112,-0.03952356,0.04231935,-0.0286939,-0.03608756,-0.08058129,-0.22445686,-0.0093077,-0.00118298,-0.03945246,0.07022256,-0.04411091,0.0074698,-0.05151886,0.0263226,0.04563124,0.03790361,0.00709044,-0.03946209,0.02214007,-0.03075049,-0.01755808,-0.06901911,-0.02887623,-0.03672173,0.02042801,0.00606771,0.02270781,-0.03918471,-0.03174415,0.03061834,-0.05915404,0.16840826,0.0370704,0.00583543,0.04742341,0.02697204,0.03592462,-0.01407949,-0.12833418,-0.02218436,0.05804692,0.01507315,0.04566252,-0.0074591,0.01812225,-0.03574201,0.03001539,-0.0027096,-0.09106863,-0.03800695,-0.00854883,-0.07079135,-0.01207876,-0.00177572,0.06073612,0.00820643,-0.02090655,0.0221727,0.00375238,-0.02295651,0.02108055,-0.07540797,0.01064493,-0.01113669,-0.02793346,0.02031645,0.01525137,0.02863602,-0.10977093,0.07451711,-0.01293848,-0.01942542,0.01580353,0.0097425,-0.01849294,0.0346849,0.07711427,-0.01819852,-0.01023058,0.03836638,0.0183521,0.03067048,-0.07490636,-0.05620879,-0.00660982,0.03710748,-0.03343195,0.01295277,0.0889833,0.02624964,0.04975,-0.04217008,-0.01924247,-0.02620585,-0.0169207,-0.02945901,0.02645412,-0.05789832,-0.01700316,0.01888737,0.00587618,-0.26503527,0.04135038,0.03887603,0.02032009,0.02243401,0.02743302,0.0903094,-0.060128,0.01395915,0.00442278,-0.05394417,0.02586306,-0.02578461,-0.00278783,0.02196815,0.04143369,0.07996808,-0.05162075,0.00929413,-0.0559667,0.01840422,0.04089171,0.18631364,-0.02647922,-0.02201037,-0.01302796,-0.01322257,0.07980291,0.02616109,0.03445473,-0.01038442,0.01485666,0.05541809,-0.02020229,0.00231301,0.10043224,-0.03643274,0.0223624,0.04338918,0.03648034,-0.01236752,-0.02192381,0.03288618,0.06238711,0.11153679,-0.0286411,0.02000081,-0.08673115,-0.01043261,0.01983513,-0.04662992,0.03302249,0.01170223,-0.00120865,0.01441331,0.05026667,-0.03062722,-0.01426393,-0.06047486,0.01155892,0.02343435,-0.00068652,-0.0238435,0.03319094,0.03706146],"last_embed":{"hash":"531b55cf353e402ed1e3c2c41818e4b19bd1ddabd28c972d1131260728c38e54","tokens":198}}},"text":null,"length":0,"last_read":{"hash":"531b55cf353e402ed1e3c2c41818e4b19bd1ddabd28c972d1131260728c38e54","at":1745995302084},"key":"README.md##Citation#{1}","lines":[78,89],"size":586,"outlinks":[{"title":"paper","target":"https://hal.archives-ouvertes.fr/hal-03351139","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Get the project": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05386184,-0.00984086,0.0286671,-0.04056783,0.05284555,0.03479375,-0.10434328,0.03457135,-0.04624461,0.01533771,0.03233505,-0.06159556,0.02811054,-0.00245312,0.03023013,0.05457111,0.01493142,0.02744339,0.03213906,0.01028096,-0.00286539,-0.00848482,0.02515119,0.00242669,0.03274182,0.03359507,-0.01899735,-0.09180597,-0.05138162,-0.19075499,0.01841585,0.0367859,-0.02704603,-0.01945348,0.06060948,0.0280297,-0.01820375,0.02589864,-0.05160854,0.03172541,0.02889303,0.02947137,-0.04505432,-0.02909726,-0.00517521,-0.06763555,0.00228764,-0.00754973,0.11400778,-0.06910421,0.01557291,-0.01974794,0.03684454,-0.08405861,-0.02331676,0.05235078,0.052509,0.06630322,0.04301899,-0.05551021,-0.01027635,0.05802969,-0.17989302,0.09921999,0.04616506,0.07002081,-0.03523675,-0.01966997,0.1136361,0.01045277,-0.04081039,0.00087957,0.01239504,-0.00424553,0.00879636,-0.00317861,0.02224778,-0.01909334,0.07254258,-0.01166761,-0.06940196,0.09958069,0.0072676,0.03775656,-0.03026249,-0.04760595,-0.00580886,0.03710442,0.04706532,0.03046475,-0.03568225,-0.04650771,0.00225492,0.01388193,-0.06223541,-0.01363761,0.05129332,-0.04670896,-0.00061088,0.13643724,-0.03459154,0.00322458,0.01775108,0.00786612,0.04905714,-0.01389538,-0.02628991,0.01972957,0.01501611,-0.00595539,-0.03534893,0.00628003,0.04438227,-0.06150276,0.04205566,-0.02139309,-0.07766967,0.04449873,-0.04061134,0.01038125,-0.01093816,0.00022248,0.08978254,-0.01305271,0.01196148,-0.02489825,-0.02203278,0.02259043,-0.0253893,0.08153984,0.00667785,0.03985846,-0.02639161,-0.00750905,0.0085525,0.02753241,0.01658789,-0.06915756,-0.01371651,-0.06549287,-0.00982594,0.01035979,0.0611374,-0.05014654,-0.01458135,0.05114138,-0.03875552,0.13364249,-0.04184213,-0.10192369,-0.02485332,0.00653826,-0.04841877,-0.01567282,0.03269225,0.00754101,0.07636227,0.05628663,-0.06033396,0.01436879,-0.03042798,-0.06025943,-0.0297699,0.06078106,0.02387394,-0.04237137,-0.02924572,-0.05382735,0.0470938,-0.06417598,0.01163981,0.02997855,0.0122808,0.0176421,0.08540589,-0.00886565,-0.065138,0.01456008,0.06842001,0.00220283,0.00304027,-0.05750369,-0.02039405,-0.01170751,0.01334942,-0.08976074,-0.02983461,-0.02435062,-0.0158109,0.021297,-0.04805962,0.0217247,-0.02306906,0.01796234,0.00648184,0.00840514,-0.03425495,0.002283,0.04185228,-0.06963573,0.02738962,0.00908238,0.01547856,0.0425178,-0.05990431,-0.04137171,0.03162646,-0.06258187,0.06241045,0.0046245,-0.04546559,-0.01860383,0.06249771,0.02832599,-0.07363955,-0.00091485,-0.00604479,0.07866469,-0.00503405,0.05921989,-0.074793,-0.02366884,-0.05578877,-0.22701083,0.03841451,0.00392294,-0.0030873,0.07250479,-0.02420568,0.01574236,-0.04216718,-0.05723974,0.11031599,0.17674117,-0.05978643,-0.01160195,0.01312129,-0.09055398,0.00732433,-0.04805039,0.00649233,-0.04531061,-0.00826895,0.04796805,0.00064707,-0.09604563,-0.07705024,0.0026318,0.0061228,0.12177332,0.0500388,0.08266701,0.01640271,0.08606931,-0.00011725,0.00926264,-0.18533871,-0.00839264,0.03590638,-0.00616123,0.04540319,0.03607925,0.00020631,0.00551697,0.03524546,-0.00377792,-0.11308259,-0.03875676,-0.01056819,-0.02475026,0.07156248,-0.01398857,-0.00342644,0.00648513,0.00254052,-0.01539628,-0.02546413,-0.02226641,-0.01385556,-0.04377255,-0.00967336,-0.01000285,0.04078776,0.04971505,0.01772311,-0.00489565,-0.10520659,0.00772479,-0.01332654,-0.02230027,-0.02174516,0.0646495,0.00351245,0.01308452,0.0110112,-0.01147679,0.08743469,0.02086575,0.01020987,-0.01061595,-0.01900084,0.01499353,-0.03592646,-0.04142273,-0.00718315,0.03519746,0.04746462,0.00001766,0.05995488,0.00714149,-0.00189498,0.00737091,0.03682471,-0.0354493,0.0015799,-0.01463455,-0.01256696,0.04319306,-0.00016583,-0.20956475,0.06970911,0.06102077,0.00026649,0.01773329,-0.02294525,0.12129826,-0.06178204,0.02079482,-0.00521873,-0.00138743,-0.00706576,-0.04013361,-0.09008913,0.02583175,0.02551724,0.08803669,0.01015704,-0.01794208,-0.05508558,0.03134737,-0.0179172,0.17013361,0.00831962,-0.0147133,0.02955997,0.00574308,0.01651343,0.02664899,0.04564681,-0.00222887,0.07032757,0.09194274,-0.04048595,-0.00989608,0.04553562,0.03058089,0.03103809,0.04832654,-0.03038788,-0.04310482,-0.01114317,-0.05358166,0.05659817,0.05023662,-0.09048367,0.02734106,-0.07430656,-0.07529911,0.03503545,-0.03866556,0.00652891,0.05848503,-0.02682331,-0.00810469,0.02646473,0.02827538,-0.01691391,-0.02694445,0.03489819,0.00779251,-0.07813984,0.08051328,0.02587886,-0.05720909],"last_embed":{"hash":"bc52cd51021f629c2e1c3b3d3473bf978ef5e68bf5cf46be621c5405e8b0c0ce","tokens":130}}},"text":null,"length":0,"last_read":{"hash":"bc52cd51021f629c2e1c3b3d3473bf978ef5e68bf5cf46be621c5405e8b0c0ce","at":1745995302175},"key":"README.md##Get the project","lines":[90,103],"size":418,"outlinks":[{"title":"download pre-compiled binaries for the latest release","target":"https://github.com/alicevision/meshroom/releases","line":3},{"title":"**INSTALL.md**","target":"INSTALL.md","line":5}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Get the project#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05591257,-0.00534852,0.03828534,-0.03696817,0.04950456,0.03890393,-0.10245579,0.03591873,-0.04141974,0.00704845,0.03056885,-0.05580007,0.0223697,-0.00525787,0.03067875,0.0533958,0.02204236,0.02447553,0.03291531,0.01271059,0.00211631,-0.01018384,0.02282486,0.00502406,0.03250907,0.02890005,-0.02132513,-0.09126824,-0.05495385,-0.19016938,0.0133597,0.03036464,-0.03426937,-0.02099329,0.05811813,0.02777456,-0.01524041,0.01954431,-0.04828442,0.03522509,0.03248865,0.03556022,-0.0457893,-0.02945187,0.00420196,-0.05853747,0.00427009,-0.00190034,0.11601192,-0.07414962,0.01633849,-0.01968746,0.0343318,-0.08005363,-0.02478131,0.05509699,0.04653508,0.06379276,0.03841681,-0.05036036,-0.01122369,0.05228558,-0.17760338,0.10096882,0.04945911,0.07502031,-0.04038022,-0.02452971,0.11411856,0.00726662,-0.03578648,0.00511947,0.01352505,-0.00969951,0.01676677,-0.00709601,0.0225219,-0.01256508,0.0730652,-0.00635123,-0.07230721,0.09992979,0.00760694,0.03785323,-0.02910003,-0.0516637,0.00276524,0.03855938,0.04853282,0.03037915,-0.03810645,-0.04502257,0.00790552,0.01744026,-0.05720378,-0.01873165,0.04646948,-0.05340448,0.00418124,0.13399307,-0.03912121,0.00549301,0.01579214,0.0128614,0.03980187,-0.01601175,-0.02878351,0.02244097,0.01398534,-0.01287864,-0.03801113,0.00801171,0.05004141,-0.06395327,0.04282971,-0.01943446,-0.07995671,0.04199968,-0.03853678,0.0145052,-0.00993797,0.00135439,0.08206253,-0.01178201,0.01019633,-0.02086519,-0.01840215,0.02681969,-0.03012492,0.08605701,0.0089041,0.03319759,-0.03620613,-0.00863545,0.01001124,0.02579718,0.00791496,-0.07897557,-0.02104015,-0.06516022,-0.00557111,0.0125969,0.06742802,-0.04706098,-0.01083744,0.05136225,-0.03961782,0.13422775,-0.04172621,-0.10081396,-0.02539834,0.0078033,-0.04601626,-0.01921505,0.03097052,0.00806892,0.06895341,0.0519642,-0.05816929,0.01200792,-0.03802548,-0.05326296,-0.02242826,0.06068954,0.02519056,-0.03983885,-0.03325411,-0.05585055,0.04826343,-0.06066308,0.0134228,0.03132197,0.00828022,0.01092028,0.08794761,-0.00476466,-0.0659677,0.00572811,0.06264387,0.00168675,0.00548182,-0.05559744,-0.01614231,-0.00976513,0.01728596,-0.08754622,-0.02353414,-0.0245971,-0.00697423,0.0237713,-0.03899655,0.02465195,-0.02544111,0.01890162,0.00187277,0.00992184,-0.04175679,0.00448797,0.04270677,-0.06352659,0.03455452,0.01840102,0.01221356,0.03962525,-0.05024428,-0.03630881,0.02260347,-0.06726193,0.06189998,-0.0012683,-0.04267013,-0.02074617,0.06360967,0.02822884,-0.07705437,0.00199198,-0.0044509,0.07180745,-0.01249479,0.05600969,-0.06979204,-0.02880733,-0.05049755,-0.23268726,0.03939627,0.00408506,0.00121561,0.07500938,-0.02526712,0.02103671,-0.04012171,-0.05760114,0.11641507,0.17397597,-0.0567166,-0.01329745,0.02514229,-0.08787627,0.01055254,-0.04438086,0.01113047,-0.04751293,-0.01210162,0.044659,-0.01407702,-0.09714011,-0.07607198,0.00322304,0.00437176,0.12716906,0.0476221,0.07826853,0.00997265,0.07975178,-0.0006387,0.00865679,-0.19347161,-0.00007251,0.03940266,-0.00690078,0.05132555,0.04110752,-0.00350128,0.00247786,0.03822551,0.0005796,-0.12131426,-0.03781175,-0.00387004,-0.02182147,0.0703645,-0.01022742,0.00007773,0.00742276,0.00257384,-0.01090266,-0.02363866,-0.02680759,-0.008105,-0.03999785,-0.01059345,-0.00610923,0.0329364,0.04546719,0.00818388,-0.01662791,-0.10670085,0.00780116,-0.01515314,-0.0206851,-0.02323616,0.07030957,0.00719979,0.01339004,0.00996591,-0.01621358,0.09159774,0.01423625,0.01226131,-0.01027695,-0.02041135,0.01244437,-0.03617674,-0.03986612,-0.01002722,0.03858408,0.04486251,0.00245535,0.05317321,0.0079699,-0.00129098,-0.00074926,0.0418036,-0.03677645,0.01040497,-0.01230845,-0.00697342,0.03354017,-0.00616663,-0.21292077,0.07067319,0.0591462,0.00058719,0.02343157,-0.01996366,0.1141248,-0.06011562,0.02429365,-0.00210925,-0.00166776,-0.00634242,-0.04445076,-0.09301528,0.02537716,0.02495289,0.08818469,0.01065876,-0.02379117,-0.0578229,0.03230309,-0.01913636,0.16882652,0.01627986,-0.01948271,0.03012413,0.00749424,0.01865156,0.03130646,0.04891194,-0.00276458,0.07357624,0.08934392,-0.04525156,-0.01060653,0.03626793,0.0258752,0.03583022,0.04829885,-0.03043569,-0.03886734,-0.00956422,-0.05515145,0.04881132,0.04400493,-0.09477235,0.02175513,-0.07289988,-0.07581863,0.03185531,-0.04208528,0.00624958,0.0603126,-0.02922152,-0.00349769,0.02464218,0.02496221,-0.01716402,-0.03335814,0.04087019,0.01427693,-0.07883561,0.08302372,0.02851879,-0.0482254],"last_embed":{"hash":"4f2659a875a49a628bbf48067775cc05a57c395419fd6f329358c8cac8754504","tokens":129}}},"text":null,"length":0,"last_read":{"hash":"4f2659a875a49a628bbf48067775cc05a57c395419fd6f329358c8cac8754504","at":1745995302232},"key":"README.md##Get the project#{1}","lines":[92,103],"size":398,"outlinks":[{"title":"download pre-compiled binaries for the latest release","target":"https://github.com/alicevision/meshroom/releases","line":1},{"title":"**INSTALL.md**","target":"INSTALL.md","line":3}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start Meshroom": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02594619,-0.03711079,0.08555377,0.01142417,0.00130779,0.01334408,-0.11100991,0.024844,-0.03127576,0.02417777,0.01770553,-0.0657251,0.01169885,0.01027716,-0.02279332,0.007094,-0.02210899,0.00516901,0.02249094,0.01621149,0.02387321,-0.03042507,0.04315203,-0.01756278,-0.04361011,0.11307035,0.03067705,-0.04266372,-0.03233565,-0.21675569,0.00623553,0.01679968,0.01005092,-0.01897708,-0.04038513,0.00816864,-0.01523105,0.01847317,-0.01439501,-0.00840402,0.0444527,0.0018048,-0.05351048,0.00300929,0.02233655,-0.09757417,0.01963432,-0.03231503,0.09165233,-0.05792043,-0.03074315,-0.0403001,0.05259742,-0.06687585,0.00688771,0.05019945,0.06133434,0.0660743,0.062852,-0.04116064,0.02107958,0.07088487,-0.18886681,0.12603603,0.02266978,0.02021066,-0.03644927,-0.04611323,0.05340676,0.00199865,-0.08585252,-0.00654765,-0.02612438,-0.03247358,0.0047687,-0.0568374,0.03920566,-0.05269042,0.04026352,-0.01200647,-0.03172503,0.06761695,-0.071375,0.021087,-0.02811333,-0.00969239,-0.01576513,0.02662759,-0.00374061,-0.05353444,-0.01951282,-0.06787641,-0.05242623,0.04821406,-0.05234538,-0.04029348,0.04792854,0.0101442,-0.01292125,0.1069747,-0.00138266,-0.01458273,-0.03393719,-0.00747662,0.02616001,-0.0140689,0.03711591,0.00816614,-0.0481354,0.06213924,0.01997599,-0.01264211,-0.04709604,-0.02232536,0.07205742,0.04306474,-0.08275976,0.05479172,-0.04688454,0.03715364,-0.03079332,0.00726151,0.04283153,-0.00428915,-0.01579323,0.00415407,-0.00049162,0.01692447,-0.01541227,0.06559709,0.02031199,-0.04117209,-0.05267455,0.01416238,-0.00697887,0.08260667,0.03937035,-0.04800655,-0.02449455,-0.00932534,-0.0089045,-0.00432571,0.04741015,-0.0340642,0.05218222,0.08158448,-0.06499349,0.03843358,-0.00520705,-0.06869844,0.03209409,0.02180529,-0.11996745,-0.0088734,-0.02385726,-0.01569587,0.08480626,0.07385571,-0.04483326,0.04509906,-0.03856363,-0.10507537,-0.02449956,0.10138401,0.00121607,-0.01306675,-0.01844954,-0.01393425,-0.01439782,-0.04185153,-0.01116046,-0.00012197,-0.0010864,0.02442418,0.08173026,-0.02446057,-0.10482462,0.03326278,-0.03700659,0.01249256,-0.04037119,-0.04454235,-0.03371405,-0.06649042,0.02019729,-0.04158529,-0.04145259,-0.03597955,-0.04201823,0.03899116,-0.00646228,0.0230957,-0.04709928,0.0215589,-0.00350591,-0.014319,-0.09157854,0.02278269,0.02234193,-0.05137226,0.05173773,0.01110721,0.00394248,0.02981687,-0.0855464,-0.04153568,-0.03876796,-0.02235793,0.04135452,0.02143817,-0.08318777,-0.0412823,0.07344482,0.00059745,-0.06528466,-0.07210911,0.01235085,0.04732525,-0.01111813,0.08509939,0.02365265,-0.04061391,-0.0484791,-0.22515684,0.02932805,-0.011338,0.03408657,0.01939385,-0.07026106,0.01662562,0.04937132,-0.03911876,0.12175867,0.11272053,-0.00338258,0.06749009,0.07582647,-0.05655923,0.02639597,-0.04452403,-0.03591961,-0.07614411,0.01750731,-0.02624639,0.02246734,-0.01976432,-0.07631136,-0.00928899,0.01853963,0.1533621,0.01243882,0.12547958,-0.00009054,0.04441992,0.00456299,-0.03265708,-0.11585344,-0.08401131,0.07605803,0.02407392,0.01378551,0.03895492,0.01946745,-0.00618246,0.02508517,0.02314513,-0.04590727,-0.04009015,-0.0084574,-0.00311236,0.08356059,0.00060984,0.004461,0.01686679,0.00098832,-0.01354927,-0.00234038,0.01025079,-0.05330119,-0.05071219,-0.02139917,0.01649636,0.02570792,0.00063676,0.02748664,0.06004235,-0.09662538,0.08160685,0.02158141,-0.0004235,-0.05158982,0.1305352,0.02606174,0.02529608,0.05901305,-0.01358316,0.04714701,0.04529712,0.03540783,0.00913005,0.01993679,-0.0401144,-0.02838679,0.01044417,-0.02623771,0.05884834,0.07732774,0.05483017,0.06111287,0.01424174,0.01367343,-0.00334497,-0.02746232,0.00732525,0.05074441,-0.03908828,-0.0196721,0.03471565,0.01819773,-0.21090479,0.03822779,0.03232404,0.04084795,0.02743883,0.00936914,0.07815316,-0.06025459,-0.04642074,-0.03959609,-0.05119431,0.0159871,-0.02240261,0.01073474,0.03315904,0.04247184,0.05862665,0.03347005,0.02053346,-0.02689808,0.04554732,0.03120103,0.18242441,-0.0477877,0.01114605,-0.01377052,-0.01863968,0.02657547,0.03440102,0.09350472,-0.02493111,0.03229456,0.03411876,-0.04472893,0.07478809,0.02569484,0.00872408,-0.01274906,0.06444257,-0.01831337,-0.00996771,0.02478912,-0.04543241,0.04013854,0.01426605,-0.03503104,0.00921158,0.01293638,-0.01999614,0.05026145,-0.03409195,-0.01626373,-0.00515845,0.04241602,0.04974088,-0.00457758,-0.00044765,-0.04667619,-0.05359239,0.0404684,0.03591854,-0.08015259,0.05624012,0.01609952,-0.03557122],"last_embed":{"hash":"2f630c3819e874e60730de4b776e862b7a4c644435a06e6b6221ffeeb3250c9c","tokens":263}}},"text":null,"length":0,"last_read":{"hash":"2f630c3819e874e60730de4b776e862b7a4c644435a06e6b6221ffeeb3250c9c","at":1745995302289},"key":"README.md##Start Meshroom","lines":[104,128],"size":845,"outlinks":[{"title":"AliceVision","target":"https://github.com/alicevision/AliceVision","line":3}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start Meshroom#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02320115,-0.04306746,0.08529602,0.01584719,0.00585774,0.02076078,-0.10913675,0.02799391,-0.03512719,0.02105876,0.01519448,-0.06245313,0.01187922,0.01324121,-0.02314587,0.01431782,-0.02175642,0.00863434,0.01880653,0.01430874,0.02621707,-0.02699728,0.04223676,-0.0109725,-0.04655149,0.10771037,0.02629906,-0.05040507,-0.03168024,-0.21677633,0.01096786,0.01918654,0.00546119,-0.0220676,-0.03711684,0.01422689,-0.01195355,0.01780269,-0.01436781,-0.00857205,0.04734024,0.00335679,-0.05109533,0.00239877,0.02525455,-0.09310655,0.01934429,-0.02404211,0.09308098,-0.06372087,-0.02233849,-0.04168639,0.04718456,-0.06091784,0.00596898,0.05507132,0.06893012,0.06563382,0.05779827,-0.03897892,0.02176715,0.06691036,-0.1877071,0.12259481,0.01804717,0.01886471,-0.03528576,-0.05123364,0.05609123,0.00197661,-0.08874996,-0.00693361,-0.02889502,-0.0371035,0.00251574,-0.05905405,0.04528606,-0.05239306,0.04145346,-0.01037817,-0.03865467,0.07242949,-0.06593078,0.02694585,-0.02731153,-0.01596973,-0.01383884,0.02007731,-0.00421699,-0.04742023,-0.01885275,-0.06621158,-0.05264634,0.05267024,-0.04702454,-0.04836351,0.04097642,0.01248551,-0.01132733,0.11063541,-0.00506398,-0.01933525,-0.02784963,-0.01310199,0.03126857,-0.02072415,0.03718309,0.01024797,-0.04492671,0.0637638,0.01130051,-0.00892568,-0.04967935,-0.01838242,0.07091203,0.04808515,-0.08631931,0.04724221,-0.04782391,0.03127476,-0.03132646,0.00241817,0.04728345,0.00122548,-0.018764,0.00016672,-0.0001355,0.01772642,-0.01615858,0.06934123,0.01789375,-0.03926601,-0.05120968,0.01852346,-0.00692843,0.07855132,0.04185323,-0.04516486,-0.02920159,-0.00965599,-0.00467325,-0.00691462,0.04849864,-0.0379615,0.05452139,0.07606211,-0.071442,0.0350691,-0.00325544,-0.07701764,0.03571935,0.02459792,-0.12257349,-0.01248977,-0.02239169,-0.01702294,0.07949479,0.07635815,-0.04595416,0.04658222,-0.04133633,-0.10746562,-0.0296117,0.09625243,-0.00336082,-0.01141344,-0.0230835,-0.01483753,-0.01122996,-0.0407088,-0.01425607,0.00113698,-0.00616108,0.03430695,0.08422016,-0.02442869,-0.10918593,0.03376542,-0.03270794,0.02057658,-0.04332333,-0.04359938,-0.03087303,-0.06611173,0.0188832,-0.03446192,-0.03676375,-0.03690313,-0.03916483,0.02872759,-0.00623589,0.02255699,-0.04847852,0.02342289,0.00238949,-0.01127734,-0.09558357,0.02991168,0.02169169,-0.05508042,0.05259465,0.00629658,0.00464358,0.02746868,-0.07738217,-0.03964528,-0.04015719,-0.0221739,0.04703014,0.0100958,-0.08042219,-0.04554891,0.07591328,-0.0001479,-0.06399795,-0.07095756,0.01696414,0.04667201,-0.01232094,0.08672971,0.01981346,-0.0422488,-0.04858515,-0.2210384,0.02737161,-0.01330948,0.03473468,0.02066309,-0.07244124,0.0143942,0.04297654,-0.04135479,0.11601532,0.11869902,-0.00126469,0.07098,0.07852999,-0.05604244,0.02502627,-0.04982221,-0.03232852,-0.08001336,0.01303826,-0.0171278,0.02251221,-0.03079163,-0.07405999,-0.01153826,0.02196429,0.1530232,0.01495068,0.12839293,0.00524381,0.04373956,-0.00308028,-0.03419641,-0.11723189,-0.08378551,0.07580913,0.02946661,0.00269596,0.03664633,0.01814434,-0.00928898,0.02764772,0.02863013,-0.04573269,-0.03914093,-0.00728349,-0.00073629,0.07785282,-0.00390027,0.00933821,0.01861915,-0.00318266,-0.01506577,0.00128987,0.00937485,-0.04528954,-0.05516327,-0.02523085,0.01590128,0.02806332,0.00261296,0.0287157,0.05893877,-0.09375878,0.0797405,0.02143762,-0.00096455,-0.05093908,0.12936383,0.02391056,0.02696102,0.0592281,-0.01649378,0.05019981,0.04400638,0.03023471,0.01126301,0.01782764,-0.03268692,-0.03337009,-0.00088931,-0.02289003,0.0505392,0.07364338,0.06136368,0.06546791,0.00783452,0.01189146,-0.00048922,-0.02868108,0.01177432,0.05257861,-0.03931406,-0.02487204,0.03075906,0.00901763,-0.21015859,0.03465002,0.0340343,0.04335919,0.02438663,0.00403596,0.07350563,-0.05127849,-0.0406973,-0.04046618,-0.04483141,0.00260762,-0.0209301,0.00172551,0.03688634,0.04086142,0.05981081,0.03791207,0.01917966,-0.02360318,0.05414949,0.03587661,0.18427598,-0.0454096,0.00887331,-0.012797,-0.01903101,0.02449583,0.02798249,0.09435262,-0.02236086,0.03478793,0.04421399,-0.04068027,0.07853732,0.0227459,0.01122186,-0.01225462,0.06460986,-0.01706987,-0.01977954,0.02582908,-0.05141661,0.0395067,0.01171816,-0.03219327,0.01141144,0.01774117,-0.02896853,0.05750182,-0.03221584,-0.01320454,-0.00193073,0.0385648,0.05302638,-0.00630701,0.00363874,-0.04486268,-0.05219014,0.04474747,0.03028003,-0.07409851,0.05506299,0.01717936,-0.04172155],"last_embed":{"hash":"f2601d7a933590126f7d2e2f10fce12d7893f2d43b7cca76e5849ae85bc64a5d","tokens":262}}},"text":null,"length":0,"last_read":{"hash":"f2601d7a933590126f7d2e2f10fce12d7893f2d43b7cca76e5849ae85bc64a5d","at":1745995302415},"key":"README.md##Start Meshroom#{1}","lines":[106,128],"size":826,"outlinks":[{"title":"AliceVision","target":"https://github.com/alicevision/AliceVision","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start Meshroom without building AliceVision": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05616144,-0.02178054,0.04956266,0.00709766,0.01708394,0.01919488,-0.11440254,0.01695835,-0.02515687,0.05294834,0.02862221,-0.04461971,0.01034915,0.0449613,-0.0410857,0.00768516,-0.02074615,0.02286081,0.0339697,0.01195818,0.05200947,-0.02871983,0.04115687,-0.00829051,0.0082178,0.05275879,-0.0106118,-0.06587442,-0.0116444,-0.2438754,0.01800898,-0.00438701,0.00781415,-0.04039348,-0.0230018,0.00069519,0.00688792,0.00369648,-0.01192497,0.0054469,0.01156547,0.07231431,-0.01591874,-0.03022535,0.03073777,-0.0456356,0.04985345,-0.03186768,0.06781468,-0.04296755,0.0286756,-0.06722685,0.04315566,-0.05449061,0.01597133,0.09328204,0.02315903,0.01337226,0.02419024,0.00517146,0.014646,0.06198772,-0.15931652,0.07822651,0.01365881,0.02890116,-0.05042271,-0.03429397,0.09235756,0.01588833,-0.03928382,0.04153973,-0.03889309,-0.04315443,0.02833455,-0.03345129,0.04405744,-0.0515319,0.04397902,-0.0194152,-0.07426319,0.05230129,-0.03063015,0.04518735,-0.03819053,-0.02229593,-0.02055405,0.00140659,0.01639161,-0.00392952,-0.0205524,-0.04857877,-0.01849215,0.0561263,-0.01214361,-0.04751716,0.03587236,-0.00501714,-0.02497597,0.14186268,-0.04046316,-0.01914717,-0.01029275,0.01157977,0.03584523,-0.03939071,0.00606362,-0.02575085,-0.00749869,0.00021898,-0.02040415,0.03289784,-0.00688191,-0.0282213,0.05257018,0.06969259,-0.03307992,0.02454821,-0.02851534,0.01579003,0.00055032,0.0012715,0.10405292,-0.02350836,-0.01661497,0.00895991,0.00971456,0.02361284,-0.00717105,0.05593181,0.02443268,-0.04491586,-0.04304551,0.00237983,0.00721932,0.03260238,0.02468661,-0.05093821,-0.00721829,-0.03711284,-0.03142095,0.0179845,0.03794706,-0.06970257,0.04323381,0.06572974,-0.03405845,0.03911099,-0.00812496,-0.07868102,0.02195763,0.03299396,-0.09443089,-0.02129248,0.02404127,-0.04758144,0.01911578,0.07418522,-0.03362913,0.01406812,-0.05996736,-0.07066713,-0.0656111,0.09008341,-0.06958945,-0.04110075,-0.03396951,-0.00263194,-0.04342667,-0.02639378,-0.02156142,0.00338176,-0.00302405,0.05889155,0.08253442,-0.0274308,-0.13139774,-0.02812071,0.00288281,0.05645551,-0.06190338,-0.0744134,-0.03791654,-0.01625624,-0.02567437,-0.0316842,0.00323321,-0.05013034,-0.01347183,0.05318315,-0.02521998,0.0155871,-0.01486154,0.02416734,0.0019694,0.01960383,-0.05332257,0.01281686,0.04821623,-0.06369349,0.08162406,0.00578262,-0.00385466,0.02357049,-0.02580974,-0.00979918,0.03103055,-0.02757009,0.06857943,0.01091525,-0.06180717,-0.0553201,0.0328291,-0.00340907,-0.0508275,-0.0448292,0.02860657,0.05235773,-0.04541631,0.03673474,-0.00100118,-0.0218114,-0.08106845,-0.22434291,0.04923373,-0.02840816,0.01086992,-0.01269423,-0.12647897,0.03301975,-0.02125045,-0.04806274,0.09541711,0.10553333,0.01503078,0.01183112,0.06285322,-0.06488381,0.01897521,-0.06662783,-0.03305184,-0.09624429,0.00819842,0.02530009,0.03164972,-0.04013783,-0.12426399,0.00681097,-0.02147953,0.15526421,0.00259268,0.15514787,0.0390108,0.04033561,0.00538288,-0.02528239,-0.11243118,-0.07711451,0.08091902,0.01526909,0.01065386,0.03408555,-0.00969177,-0.00345789,0.08174235,0.0031089,-0.09574232,-0.01693291,-0.01143383,0.00219704,0.0731916,-0.0177312,0.05410609,0.02230258,-0.02052419,0.02150712,0.04840791,0.01219432,-0.0164832,-0.07386795,-0.01608106,-0.02078044,0.05200009,-0.02061344,0.01556451,0.03748791,-0.0475743,0.05006975,0.01046505,-0.00316507,-0.05281999,0.13598643,0.00512063,0.01136254,0.07992353,-0.01374403,0.04057623,0.00594852,0.00950905,-0.0229775,-0.00126573,-0.0169933,-0.00703586,-0.01809833,0.02543378,0.03015887,0.08341738,0.0266733,0.08441351,-0.0128205,0.01216352,0.02702665,-0.03097284,0.01960921,0.06369838,-0.02169689,-0.01434829,0.07345346,-0.03178164,-0.21736361,0.0039903,0.06250455,0.0286203,-0.01626901,-0.00201641,0.06133031,-0.03698386,-0.00770557,-0.01472453,-0.04718473,-0.04206199,-0.03153463,-0.04927741,0.05219961,0.03327177,0.04393864,0.0338169,0.04121626,-0.0018175,0.07094415,0.00029861,0.19300631,-0.02793855,-0.0095583,-0.01114,-0.02129494,0.05217656,0.03130562,0.11579923,-0.05241012,0.05897565,0.09829026,-0.04232528,0.03969896,0.01247135,0.0406825,0.053834,0.04195257,-0.04746125,-0.0647327,0.02949847,-0.01412421,0.04321377,-0.00050428,-0.02013785,0.03147151,0.01956462,-0.06854825,0.03217758,-0.05222324,-0.01128408,0.00502769,0.07832551,0.02069758,0.01468788,0.03741318,-0.04655308,-0.03532264,-0.0007618,-0.01146232,-0.05021794,0.03571289,0.03108731,-0.04934467],"last_embed":{"hash":"18730e656495a20e737ef29593b9cdc8924abde4d400c317d3697478004868a4","tokens":134}}},"text":null,"length":0,"last_read":{"hash":"18730e656495a20e737ef29593b9cdc8924abde4d400c317d3697478004868a4","at":1745995302543},"key":"README.md##Start Meshroom without building AliceVision","lines":[129,135],"size":413,"outlinks":[{"title":"release","target":"https://github.com/alicevision/meshroom/releases","line":4}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start Meshroom without building AliceVision#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03041046,-0.00474458,0.06122706,0.01688582,0.01292276,0.02499836,-0.08915716,0.02741983,-0.01235415,0.04997952,0.03066449,-0.0466854,0.0008266,0.05436527,-0.02570464,0.01897712,-0.02174949,0.01895747,0.02360915,0.00496648,0.04519447,-0.04253964,0.01996866,-0.00424128,0.01174881,0.01696004,-0.0199457,-0.05993969,-0.00563046,-0.20287317,0.01622911,0.00616145,0.04967986,-0.02494652,-0.00748748,-0.0054737,0.01110462,0.01405068,-0.04769202,-0.00487975,0.02161658,0.05685243,-0.01098939,-0.01966245,0.02150169,-0.04544744,0.05928423,-0.02363854,0.08331925,-0.02793327,0.04158694,-0.06695201,0.03972851,-0.04848269,0.00200312,0.08844519,0.01005374,0.00550891,0.04123369,-0.04279114,0.01578123,0.04371635,-0.14518955,0.06254836,-0.00396313,0.02488817,-0.0509556,-0.04348549,0.10321842,0.02578851,-0.01526497,0.03030739,-0.03895275,-0.04388756,0.02639374,-0.0296013,0.01734976,-0.05281081,0.05728485,-0.01617748,-0.08293694,0.02414785,-0.0252823,0.04242831,-0.03750343,0.01073662,-0.0438736,-0.01348425,0.01885787,0.012417,-0.04823103,-0.05928458,-0.05570636,0.04829327,-0.00559164,-0.0792359,0.01797888,-0.01363107,-0.01751579,0.1524751,-0.03832511,-0.03075852,0.03422595,-0.02004472,0.02491668,-0.06520913,0.01163926,-0.02771451,-0.02727173,0.03019989,-0.02486934,0.02548024,-0.04636899,0.00096354,0.06284153,0.06470558,-0.02564592,0.00415322,-0.04272457,0.00519288,-0.00120905,0.02547899,0.05951278,-0.00397761,-0.00690109,-0.02178904,0.03233208,0.03917391,-0.00103475,0.0481883,0.00724692,-0.0389616,-0.01895943,0.00772511,0.01302266,0.03378947,0.04028076,-0.08313683,0.03150675,-0.02876148,-0.03501883,0.06458216,0.04422009,-0.09228478,0.07168823,0.05827591,-0.00974683,0.03311912,-0.02870042,-0.09319906,0.03015466,0.01079877,-0.05967848,-0.01081901,0.04746187,-0.03909143,-0.00263895,0.06593243,-0.01983295,0.01788681,-0.04971818,-0.06042438,-0.07013066,0.09441416,-0.05290607,-0.06995881,-0.05849689,0.01655195,-0.04480686,-0.03521847,-0.03330992,0.03013847,-0.01471467,0.05005464,0.08039575,-0.02177395,-0.13433662,-0.03126149,-0.02307179,0.04610785,-0.03962022,-0.08528777,-0.03842965,-0.0072763,-0.01776088,-0.00960009,0.03331903,-0.05769549,-0.02684922,0.06544742,-0.03361987,0.04195176,-0.03166112,0.02100811,-0.00284136,-0.003774,-0.05684186,0.00119476,0.03629933,-0.07442515,0.07444172,-0.0036519,-0.03521685,0.0355663,-0.00913999,0.00393838,0.02212595,0.00019646,0.12195838,-0.01351057,-0.05062727,-0.0585818,0.02837696,0.01102653,-0.0514289,-0.0201283,0.02287222,0.02746225,-0.04955735,0.03047898,0.0218546,-0.01482864,-0.07843002,-0.22775036,0.07224643,-0.06519584,-0.00716263,-0.04866837,-0.09951237,0.02370695,-0.01896687,-0.05884697,0.06826561,0.09942647,-0.01261988,0.01604912,0.0416688,-0.05418854,0.03204096,-0.08547084,-0.03434543,-0.09429763,-0.02187661,0.04593352,0.03679433,0.00008245,-0.14006312,-0.02899947,-0.00624704,0.16675659,-0.02492101,0.16116586,0.00862532,0.0347579,-0.01197328,-0.02750301,-0.10863727,-0.06816126,0.07588302,0.05467865,0.0424592,0.02646655,0.00778372,-0.00876022,0.08929693,-0.02751574,-0.09606393,0.00857469,-0.02136688,-0.02351185,0.06140215,-0.0277567,0.03241946,0.00561742,-0.04729261,0.00768423,0.04189958,0.0096834,-0.01949274,-0.10470285,0.00372119,-0.00704555,0.04571206,-0.0042841,0.03335607,0.03545107,-0.06292784,0.04968658,0.04102663,-0.01310594,-0.02614385,0.11754354,0.01742511,-0.0159136,0.05598874,0.01845724,0.06809595,0.01837823,0.00080015,-0.01604046,-0.00355415,-0.00931767,0.01322836,-0.04790469,0.0055452,0.02486053,0.05963282,0.05373336,0.11592712,-0.01347178,0.00808205,0.01238087,-0.03813573,0.01824216,0.05336377,-0.02853945,0.00526006,0.06011396,-0.04134034,-0.20905545,0.00952225,0.03932796,0.03079161,-0.0233671,0.00805149,0.03870245,-0.0032353,0.01490002,-0.00796632,-0.04801403,-0.05041105,-0.01711402,-0.03429908,0.07270788,0.05329666,0.04698918,0.05432329,0.05097128,0.00353794,0.06605052,0.01763312,0.1876737,-0.02940558,-0.02402374,-0.00323366,-0.02186708,0.02627389,0.03016325,0.11797169,-0.02819906,0.05537248,0.1128123,-0.00432724,0.04574749,0.01887942,0.03188791,0.05577552,0.04722959,-0.02653922,-0.09368309,0.04578426,-0.01937732,0.02928157,0.00301341,-0.01665205,0.02807241,0.02832218,-0.06093345,0.01956356,-0.04868494,-0.008799,0.02049048,0.0625224,0.00540742,-0.02132762,0.01858283,-0.03610186,-0.0079312,0.00598325,-0.02681511,-0.01817816,0.02420358,-0.01167124,-0.04087041],"last_embed":{"hash":"159dac9cae050e0f4f576f2a6a27e946ef4d989ac635239f5658126bdef33ea5","tokens":24}}},"text":null,"length":0,"last_read":{"hash":"159dac9cae050e0f4f576f2a6a27e946ef4d989ac635239f5658126bdef33ea5","at":1745995302602},"key":"README.md##Start Meshroom without building AliceVision#{1}","lines":[131,131],"size":49,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start Meshroom without building AliceVision#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05174573,0.00573762,0.04793313,0.01275495,0.0284187,0.03171719,-0.14624564,0.04799115,-0.01463123,0.0488025,0.03355254,-0.03315671,-0.00958518,0.03705398,-0.01059019,0.04306075,0.00089846,0.01599368,0.0278403,0.01026761,0.032215,-0.02410765,0.03129186,0.00194483,0.01524872,0.02205046,-0.01912642,-0.05489213,-0.01587603,-0.20633359,-0.00535907,0.02011311,0.04017476,-0.03056497,0.01007053,-0.00326805,0.00262602,0.00476938,-0.0430982,-0.00309736,0.0129007,0.06922225,-0.03607587,-0.01428022,0.01233482,-0.02140239,0.08323085,-0.02330563,0.08007405,-0.04193234,0.03138698,-0.02868271,0.03345404,-0.05599353,0.0163078,0.07934134,0.03576166,0.00992107,0.03242946,-0.03727428,0.0173096,0.04397258,-0.1501857,0.08397416,0.00932571,0.03602637,-0.05012145,-0.04101205,0.10343743,0.00188356,-0.02429732,0.04624471,-0.04793914,-0.03064553,0.0380716,-0.02934134,0.03041569,-0.02921553,0.05828502,-0.00822227,-0.07554569,0.02966424,-0.02481374,0.03413948,-0.04399106,-0.00074223,-0.02604949,0.00327316,0.01700264,0.00683702,-0.06894849,-0.04816242,-0.00191562,0.06070053,-0.02852605,-0.05482956,0.00343326,-0.02571935,0.00398192,0.15355489,-0.03228175,-0.02256826,0.01903861,-0.02613812,0.01533619,-0.06766146,0.00314563,-0.01894112,-0.01087089,-0.00304597,-0.01753355,0.02139304,-0.01046158,-0.02156205,0.05204813,0.05283822,-0.01703634,0.02237255,-0.0172273,0.02694468,0.00120829,0.02191567,0.09114084,-0.03069628,0.00793432,-0.00265472,0.00769473,0.04342885,0.01835287,0.04430149,0.00656228,-0.00001952,-0.03368422,-0.00428394,0.02002453,0.0211247,0.01766582,-0.08120426,0.00344572,-0.03439914,-0.04878226,0.03790427,0.03610912,-0.09631795,0.03405618,0.04729946,-0.00748985,0.05305937,-0.03002873,-0.06871152,0.01733764,0.02066249,-0.05671047,-0.01571017,0.03743784,-0.03817882,0.04055073,0.06126414,-0.05674142,0.01553033,-0.05700604,-0.07675191,-0.05454331,0.0786548,-0.05849544,-0.06066944,-0.07260214,0.00352908,-0.02109831,-0.03019518,-0.01745598,0.02832414,-0.02776934,0.04422176,0.08155526,-0.02962949,-0.12017879,-0.06423993,0.00096918,0.04416949,-0.0351092,-0.08056077,-0.02767054,0.00781902,-0.02186237,-0.02911241,0.00062517,-0.05365751,-0.00934658,0.03592323,-0.01796324,0.01803181,-0.00395878,0.01211484,-0.00594997,0.00237006,-0.04537726,0.0127064,0.0559839,-0.07069334,0.07936976,-0.00756297,-0.01971131,0.04033569,-0.03264695,0.02827018,0.02712253,-0.00439771,0.09172791,-0.00954095,-0.0443905,-0.077801,0.01272531,0.02271419,-0.06270128,-0.01973562,0.04036636,0.05173144,-0.0593848,0.02505724,-0.00052872,-0.03417609,-0.08855668,-0.23639163,0.0616864,-0.04907593,-0.02288557,-0.01601538,-0.09183343,0.04660707,-0.03072638,-0.02159132,0.0807151,0.11192705,0.00178651,-0.01193286,0.02705855,-0.07325028,0.02021371,-0.07014529,-0.02582056,-0.08287647,-0.01398481,0.04984262,0.00910638,-0.02498017,-0.1131222,-0.00515783,-0.02503035,0.15453838,0.02565773,0.16438608,0.00357119,0.04588201,0.00806519,-0.00174603,-0.13436173,-0.04347529,0.06759849,0.03951075,0.01787465,0.02590241,0.0133071,-0.00917052,0.08640897,-0.00605618,-0.10794117,-0.00744258,-0.00926335,-0.02658107,0.05976696,-0.02009259,0.03756207,0.01773549,-0.02640148,0.02930284,0.04537623,-0.00407333,-0.00987832,-0.08947363,-0.00459773,0.00336263,0.04896897,0.00200759,0.03005572,0.01626178,-0.06708754,0.06900349,0.03151169,-0.01406137,-0.05276822,0.10295689,-0.01026698,0.00025573,0.06387115,0.01262503,0.05801012,-0.00424524,0.00959703,-0.01545603,-0.02476946,-0.01230901,-0.0045705,-0.05962917,-0.01046501,0.03142472,0.06548992,0.01982781,0.11532883,-0.03557639,0.00314904,0.02273406,-0.0200938,0.00056831,0.06315888,-0.02804863,-0.02640645,0.06717655,-0.05131165,-0.21409135,0.02324166,0.06885929,0.02167319,-0.02596393,0.01194391,0.04356557,-0.02504526,-0.00064201,-0.01056613,-0.04032061,-0.05247042,-0.05588565,-0.03685452,0.06013315,0.03627956,0.05496921,0.04411397,0.04173803,0.000683,0.05693776,0.00675314,0.19535179,-0.02888543,-0.01424713,-0.00002117,-0.03967498,0.0352823,0.03767375,0.09846045,-0.0609805,0.07221529,0.09592651,-0.03167934,0.02275568,0.01926281,0.05223605,0.07714589,0.05256765,-0.05109512,-0.07032748,0.0344942,0.00387047,0.04954975,0.01643918,-0.04510787,0.03708107,0.00424399,-0.10230231,0.01587323,-0.05980761,-0.01540401,0.04511026,0.07638941,0.02052099,-0.01664197,0.02391082,-0.06078258,-0.01313897,0.00719198,-0.00229651,-0.04263499,0.03294393,0.02890077,-0.0207307],"last_embed":{"hash":"74fad4b6004123e68db18f4015c512d66ec6691fba9b3e37d992096b96be8877","tokens":38}}},"text":null,"length":0,"last_read":{"hash":"74fad4b6004123e68db18f4015c512d66ec6691fba9b3e37d992096b96be8877","at":1745995302614},"key":"README.md##Start Meshroom without building AliceVision#{2}","lines":[132,132],"size":74,"outlinks":[{"title":"release","target":"https://github.com/alicevision/meshroom/releases","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start Meshroom without building AliceVision#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0540189,-0.02487712,0.05031975,0.01887422,-0.00604935,0.01602916,-0.10761104,0.00091742,-0.00805331,0.03218343,0.0225703,-0.0275536,0.00918702,0.0597778,-0.03416486,0.01193421,-0.01758015,-0.00649466,0.04976168,0.01120071,0.01767335,-0.03487802,0.03242738,0.00949045,-0.00272295,0.04593144,-0.00547163,-0.04110992,-0.01390214,-0.22341916,-0.00603083,0.00391777,0.0090624,-0.02569706,-0.00118204,-0.01156698,0.00026066,-0.02512443,-0.01247629,-0.00217873,0.0169539,0.07429603,-0.04409124,-0.02849609,0.00928619,-0.03700625,0.06392016,0.02019926,0.05537614,-0.01441884,0.01593334,-0.07683123,0.0359288,-0.05351004,0.02248875,0.10179409,0.03274014,0.00397768,0.03285325,-0.00405703,0.01741835,0.04465334,-0.13732465,0.06147255,0.00238487,0.03519695,-0.05350433,-0.05721634,0.08038837,0.0276519,-0.00118766,0.03893695,-0.03896718,-0.0220663,0.03448872,0.01096023,0.02574612,-0.05598421,0.05313473,0.01624835,-0.07651483,0.03197815,-0.03072761,0.04005048,-0.05871561,-0.0170517,-0.02529765,0.03763443,0.03349878,0.00788827,-0.03408365,-0.07050843,0.00623205,0.04281299,-0.02081196,-0.05739465,0.02014659,-0.02023063,-0.0237313,0.13073592,-0.01946148,-0.01705731,0.0249922,0.01426381,0.03986217,-0.05371738,0.01721386,-0.03933742,0.00114027,-0.02993275,-0.04842345,0.01972924,0.029355,-0.00590472,0.04754796,0.06922463,-0.00975143,0.00312852,-0.01140133,0.00306786,0.02791932,0.02624271,0.06881744,-0.01527548,-0.00393493,-0.01802405,0.05007384,0.03855833,-0.0214147,0.04094952,0.00173319,-0.01605603,-0.05175748,-0.016335,0.03131438,0.03734547,0.00361282,-0.0793934,0.03383001,-0.03877204,-0.06464596,0.04169181,0.05007736,-0.09939017,0.04644053,0.08025392,-0.01608214,0.04658096,-0.03576966,-0.11623325,0.04621729,0.01445446,-0.08470219,-0.01882512,0.0218374,-0.02804573,0.0064797,0.06155518,-0.02416585,0.0070408,-0.03078973,-0.05433604,-0.07653905,0.08157568,-0.05794711,-0.03988964,-0.0479104,-0.01684576,-0.0441256,-0.02649269,-0.02666674,0.01880298,-0.02635144,0.06091896,0.07750401,-0.02273099,-0.11188301,-0.02943556,-0.01161286,0.02514692,-0.01795843,-0.07324061,-0.05973348,-0.0113223,-0.0195551,-0.0479642,0.03935291,-0.06733195,-0.00643551,0.05345962,-0.04601543,-0.00638978,-0.00534924,0.0510568,0.01154973,-0.00460619,-0.06639777,0.01342857,0.04956348,-0.07499634,0.08864338,0.02356733,-0.03256286,0.01547942,-0.02617599,-0.00891835,0.01172904,-0.00483929,0.1127851,-0.01388895,-0.05555955,-0.03221565,0.01728182,0.01869356,-0.05126968,-0.05402317,0.00722798,0.06446686,-0.04894983,0.03040995,0.02641475,0.01374307,-0.08624686,-0.23086885,0.06200324,-0.05850498,0.00145121,-0.04082393,-0.09755976,0.0348823,-0.02501811,-0.02913192,0.0804547,0.09398853,0.03587092,0.01347708,0.0223614,-0.04006732,0.01741593,-0.08596096,-0.01999308,-0.06698672,-0.00930559,0.03541583,0.03874717,-0.05799983,-0.12576814,0.0108234,-0.01884735,0.15950336,0.01560191,0.14614488,0.01828028,0.05917771,-0.00999169,0.00001717,-0.10594043,-0.04138165,0.09117305,0.02634031,0.01842192,0.00416178,0.01275009,0.0193796,0.08282407,-0.02584011,-0.12334544,-0.01688342,-0.0052637,-0.02931933,0.0705216,-0.03790901,0.03815696,0.01509027,-0.02822741,-0.01013434,0.0734669,0.01706144,-0.01716788,-0.07056301,-0.00674203,0.00694071,0.00900908,-0.00644654,0.01437761,0.02567546,-0.03290797,0.07761973,0.0098166,-0.0020942,-0.06062939,0.10798609,-0.0018551,0.02320587,0.06137111,-0.01047255,0.05440002,-0.00990083,0.01327892,-0.01305909,-0.01674515,-0.03213819,0.01132482,-0.01824334,0.02665449,0.00858585,0.05298932,0.03104644,0.10151409,-0.00840326,-0.00835788,0.03409097,-0.01903826,0.00605686,0.0840892,-0.03554654,-0.03351813,0.07242414,-0.02320369,-0.22103192,0.01383396,0.04386818,0.02299915,-0.03372091,-0.00556133,0.02025675,-0.04156696,-0.0050602,-0.04187535,-0.02677606,-0.04057388,-0.04548445,-0.04021953,0.05091026,0.0581134,0.04135894,0.03583036,0.06843268,-0.03500887,0.05881842,0.03189381,0.22517206,-0.03287285,-0.02745363,0.01242602,-0.02351561,0.03814069,0.06165898,0.11722548,-0.02509441,0.07248978,0.1115711,-0.01082121,0.01045,0.0404803,0.03876814,0.0532648,0.04144108,-0.03238048,-0.07870419,0.05463633,-0.02897533,0.0273928,-0.00506128,-0.03715824,-0.01160446,0.03023308,-0.06126475,0.02109664,-0.03869721,-0.03402665,0.01500615,0.07123309,0.03356436,0.00556745,0.00699584,-0.04664907,-0.02170237,0.00836327,0.00347836,-0.02387348,0.02553874,0.01142634,-0.05293251],"last_embed":{"hash":"fbd82b069b68863bc2db356187dd1716bc922ec893cffc40a77b9aa09804d961","tokens":32}}},"text":null,"length":0,"last_read":{"hash":"fbd82b069b68863bc2db356187dd1716bc922ec893cffc40a77b9aa09804d961","at":1745995302631},"key":"README.md##Start Meshroom without building AliceVision#{3}","lines":[133,133],"size":88,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start Meshroom without building AliceVision#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05244316,-0.02171405,0.06288315,0.0160774,0.03269349,0.01224507,-0.14561406,0.02983546,-0.00500997,0.04957623,0.03816659,-0.06771026,0.00003468,0.02996824,-0.02840365,0.01101535,-0.0227497,0.03231015,0.02789633,0.02948246,0.06864166,-0.01682601,0.03653546,-0.03207367,0.01708519,0.06237075,0.01325662,-0.05995243,-0.02356182,-0.21701914,0.01050534,0.00108562,0.01928372,-0.04657899,0.00546267,0.00508665,0.00738953,0.01321784,-0.03362829,0.00121839,0.0089461,0.04129157,-0.00932057,0.01022298,0.03309041,-0.07510626,0.03005274,-0.01825532,0.06925987,-0.06998514,-0.0020093,-0.06102125,0.04742218,-0.06830793,0.01122004,0.09712324,0.02984849,0.03580495,0.04177411,-0.01919967,0.00903256,0.06001168,-0.18070272,0.08669391,0.02117265,0.03219594,-0.0428318,-0.03265738,0.09329606,0.00686608,-0.05857047,0.03951241,-0.03433196,-0.02989219,0.03665222,-0.04048797,0.01773974,-0.03998428,0.04265507,-0.02714814,-0.07007583,0.02135178,-0.04774886,0.05505924,-0.03642751,0.00159246,-0.00369701,-0.00034091,0.00433102,-0.01172475,-0.0422328,-0.06257615,-0.03616115,0.07494484,-0.03696748,-0.05513527,0.03690285,-0.0087981,-0.00785599,0.14295462,-0.03464834,-0.01664774,-0.01937292,0.00367803,0.03573449,-0.04279875,0.03304403,-0.01597944,-0.01124274,-0.00142927,-0.00233206,0.02147833,-0.01014105,-0.05279908,0.06930058,0.07168824,-0.0328013,0.02239767,-0.05430735,0.02139077,-0.00002846,-0.00730368,0.06568975,-0.0232478,-0.04641494,0.0155506,0.01920175,0.03460518,0.00989764,0.06683153,0.04729182,-0.01411281,-0.0406744,0.00034539,0.00628771,0.05067951,0.01385511,-0.05710853,-0.00628111,-0.02718134,-0.0327968,0.007248,-0.0053111,-0.06485613,0.03501759,0.05919722,-0.0571477,0.0273827,-0.01276931,-0.05975289,0.02009456,0.02168998,-0.06790248,-0.02762048,0.01536163,-0.04369202,0.03742209,0.07540987,-0.01260753,0.00826289,-0.0701995,-0.06819735,-0.05998055,0.11588148,-0.05718442,-0.04379655,-0.02769418,-0.00794489,-0.04031359,-0.02738352,-0.01938811,0.01431662,-0.01002512,0.04233398,0.09253178,-0.02040789,-0.09945147,-0.01265705,-0.01816785,0.04297115,-0.0447504,-0.06281447,-0.03836657,-0.03109654,-0.01223698,-0.01538174,-0.02119394,-0.0507721,-0.00607984,0.06530005,-0.02268171,0.03397097,-0.02340042,-0.00810282,0.00130018,-0.00330048,-0.04488228,0.02397996,0.06948656,-0.0800569,0.10162882,0.0093106,-0.02147751,0.04894121,-0.03236267,-0.01719642,0.01241603,-0.00869201,0.05490195,0.01360512,-0.06571317,-0.07287505,0.04471725,-0.02433925,-0.04847849,-0.04134016,0.01801571,0.03787074,-0.03951829,0.04779084,-0.02864642,-0.03798083,-0.09536038,-0.22983488,0.06835795,-0.02745334,-0.00889028,-0.00462155,-0.07973026,0.01988829,-0.00653244,-0.06618965,0.09966388,0.11444973,-0.00713977,0.02589935,0.05603182,-0.05202099,0.02324145,-0.05646137,-0.03867327,-0.08860937,0.00983801,0.02636801,0.00088667,-0.02802589,-0.11168379,-0.01658602,-0.01033017,0.16241732,0.01674845,0.15214674,0.00982842,0.04826872,-0.00407926,-0.03491366,-0.11947744,-0.06773832,0.07706309,0.03403544,0.01741197,0.03457726,-0.00071876,-0.01947118,0.07056663,-0.00333201,-0.07204747,-0.0134956,-0.0193928,-0.00537638,0.06038079,-0.00746998,0.05776273,0.03202879,-0.01231166,0.030124,0.04934477,-0.01206125,-0.0207558,-0.06509171,-0.02783087,-0.02022478,0.04830815,-0.01711774,0.01417202,0.02381293,-0.04203746,0.04645118,0.0119792,-0.01462542,-0.06035784,0.12367874,-0.01248391,0.0204426,0.04131985,0.01517602,0.02019373,0.01986174,0.01686071,-0.02625281,0.0104073,-0.02101991,-0.01669329,-0.00093952,-0.00138152,0.0248919,0.06453735,0.04436665,0.06562767,-0.03822438,0.01771284,0.03811296,-0.01764599,0.00794321,0.0670421,-0.04218413,0.01210821,0.08326468,-0.0375994,-0.23562425,0.02603923,0.05394145,0.01213756,0.00141036,0.03350629,0.0720034,-0.02062677,-0.00821719,-0.01174523,-0.04116601,-0.0060437,-0.01540068,-0.01092068,0.04692716,0.00468818,0.05591534,0.03766993,0.03184415,0.0081224,0.05302104,0.00283363,0.18642898,-0.02584652,0.00448458,-0.00942939,-0.02669637,0.0563297,0.03568444,0.1095555,-0.05489038,0.05262342,0.09805038,-0.05338902,0.07124893,0.01859874,0.03576105,0.04853966,0.06124433,-0.0383761,-0.05183208,0.02750559,-0.02661485,0.03823964,-0.00881836,-0.03419333,0.03749345,0.00405305,-0.03928321,0.03718366,-0.07019102,-0.0005181,0.00601233,0.06479157,-0.00225061,0.01381579,0.05368957,-0.04603584,-0.0431344,-0.01391695,-0.00462434,-0.06505762,0.0460128,0.02279773,-0.0304235],"last_embed":{"hash":"31b7e7e859a3c8e7a3f7bddf3793166637a58fd07462333f854f6b7311383154","tokens":78}}},"text":null,"length":0,"last_read":{"hash":"31b7e7e859a3c8e7a3f7bddf3793166637a58fd07462333f854f6b7311383154","at":1745995302646},"key":"README.md##Start Meshroom without building AliceVision#{4}","lines":[134,135],"size":151,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start and Debug Meshroom in an IDE": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08462176,-0.00478643,0.04917841,-0.02354473,0.03671758,0.02488687,-0.05879577,0.0170818,-0.02390745,-0.0200434,0.06976845,-0.03159676,0.00686157,0.04726336,-0.05691721,0.06235852,-0.02390075,-0.00291005,0.00668447,0.00924765,0.0003841,-0.01861008,0.03311626,-0.01716185,-0.00050503,0.08861722,0.03057232,-0.05724989,-0.03476898,-0.21874048,0.06117498,0.06210284,0.03232943,-0.0334314,-0.01907931,0.03337074,-0.04465785,0.01667337,-0.03676689,0.06197866,0.07200775,0.01387151,-0.04031289,0.02275459,0.01735548,-0.08747269,0.01153542,-0.0229361,0.04813165,-0.02154178,0.02195024,-0.06326041,0.07347541,-0.03454861,-0.03246364,0.07415562,0.01524591,0.05043931,0.0099657,0.01432038,0.02972876,0.04519948,-0.18946065,0.09676721,0.01691986,0.03737402,-0.05265336,-0.0255491,0.0867708,-0.05728862,-0.02779965,0.02276392,-0.00728594,-0.00421131,0.0080724,-0.03322732,0.02708961,0.02146798,0.03622026,-0.05879842,-0.08664779,0.01260562,-0.01258449,0.02021585,0.02186362,-0.00100176,0.04955759,0.04375211,0.06209511,-0.02922453,0.00367436,-0.0380256,-0.0203122,0.06710049,-0.02065524,-0.00453661,0.03119383,-0.00326426,-0.05919086,0.12641202,-0.05902676,-0.0372315,0.01160641,-0.07039174,0.02094275,-0.02832447,0.02309966,-0.05298531,-0.0129264,0.00209511,-0.03251226,-0.00409683,0.07284494,-0.04254472,0.06553285,-0.00199396,-0.08180075,0.04999371,-0.01283554,-0.03322461,0.0429421,-0.00358601,0.02109263,0.02123249,-0.01092454,0.01840419,-0.03600803,0.05786864,-0.00572311,0.02917199,0.03318935,0.03777567,-0.06612415,0.03902708,0.01123521,0.03143128,0.05093188,-0.02127982,0.03922981,-0.03492975,-0.0353715,0.00918282,0.01870028,-0.09683938,0.02493789,0.08208892,-0.03870197,0.00686745,0.01736628,-0.11648245,0.01638559,0.05618121,-0.10925008,-0.0171395,0.02644207,0.00849124,0.09235603,0.04342469,-0.01291357,0.02591301,-0.03075688,-0.06817058,-0.01558687,0.06440357,-0.0569967,-0.00788253,-0.07332517,-0.02247667,-0.01816075,-0.02556014,-0.03297678,0.02740696,-0.00810588,0.05338299,0.01804088,-0.03161716,-0.04567552,0.00228094,0.02488426,0.02464483,0.01306467,-0.03346787,0.0013928,-0.01769159,-0.01466095,-0.07733401,-0.0315653,-0.00609904,0.00017199,0.02781001,-0.06210401,0.02544774,-0.0031872,0.03605912,-0.02398399,-0.02182427,-0.06197094,0.02252545,0.02801311,-0.06508593,0.10082534,0.04188062,-0.04619873,0.03430707,-0.04149161,-0.03092867,-0.01740574,-0.0297659,0.09145871,-0.04474683,-0.05101885,-0.03009124,0.05756697,-0.00122254,-0.06830693,-0.05840715,-0.00619572,0.06628351,-0.00849563,0.06130155,-0.00588188,-0.02547941,-0.06262106,-0.25585225,0.01885842,-0.03188947,0.00229998,-0.01797858,-0.08160312,0.02976894,-0.01646672,-0.03020932,0.09662789,0.12502646,-0.00730456,0.03361674,-0.0086125,-0.0360242,0.01757843,-0.03156044,-0.01611593,-0.06114786,-0.0151019,0.05252072,0.03191846,0.01768593,-0.07064688,0.00412471,-0.02460866,0.15456003,0.03501501,0.11870396,0.03243714,0.01987546,0.04300887,-0.00196543,-0.11053386,-0.05692549,0.06299505,0.03151233,0.02359981,0.04533296,-0.0030129,-0.02648614,0.03787603,0.00468703,-0.08655508,-0.07915208,-0.03419749,-0.04120876,0.01376907,-0.05408398,0.03095792,0.02909138,0.02408378,-0.00910575,0.04373017,-0.02273855,-0.03278388,-0.07144564,-0.00233739,-0.01684567,0.0540028,0.01211948,0.02708036,0.00821636,-0.0813469,0.06613009,0.0031696,0.01500314,-0.07021608,0.16903608,0.01683267,0.03191904,0.07978024,-0.01863009,0.02737902,0.06564227,-0.0161193,0.0067977,-0.01110507,-0.03820646,-0.01219116,0.00037799,-0.02379584,-0.01897329,0.03173315,0.02272053,0.06932865,-0.0058975,-0.03917821,0.02065814,-0.05399694,-0.03123964,0.04567982,-0.04353976,-0.0311762,0.04806878,-0.01325678,-0.24992931,0.03991423,0.01911268,0.02032041,0.02492496,0.02308694,0.03275698,-0.06950932,-0.04376907,-0.09086203,0.01394453,-0.00202431,-0.01392447,-0.05764725,0.02179878,0.01898324,0.01323042,-0.00638257,0.03624917,-0.02700515,0.04942466,0.00866535,0.18416686,0.03776756,-0.01576231,0.00463794,0.01508047,0.02938271,0.04259494,0.0987758,-0.04737407,0.06277516,0.06904236,-0.00539199,0.05509626,0.03645776,-0.00546526,0.00610679,0.09403042,-0.01355429,-0.03200177,0.0083014,-0.02927864,0.06934854,0.0176551,-0.0303366,-0.02853327,-0.01255293,-0.01764207,0.05771568,-0.01568468,0.05154721,-0.02300481,0.00640087,0.02139338,0.02461724,0.01411497,-0.03777,0.00329796,-0.0074397,-0.02999098,-0.04635015,0.05575202,0.03095876,-0.02443666],"last_embed":{"hash":"8dfafd323e1d0772797ce22f3013fde3b24fb81d4331cfb81769160ffa5cecbe","tokens":255}}},"text":null,"length":0,"last_read":{"hash":"8dfafd323e1d0772797ce22f3013fde3b24fb81d4331cfb81769160ffa5cecbe","at":1745995302681},"key":"README.md##Start and Debug Meshroom in an IDE","lines":[136,145],"size":672,"outlinks":[{"title":"image","target":"https://user-images.githubusercontent.com/937836/127321375-3bf78e73-569d-414a-8649-de0307adf794.png","line":8}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start and Debug Meshroom in an IDE#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08499926,0.0228459,0.03472096,-0.0389643,0.03687469,0.01712624,-0.02624217,0.00896491,-0.02515874,-0.04312116,0.06807974,0.01108045,0.02567964,0.0555841,-0.03571902,0.06145554,-0.00924226,0.00476069,0.00077193,0.02196872,-0.00962466,0.00840054,0.0356974,-0.00231223,-0.01050629,0.09782472,0.0261561,-0.04982777,-0.02681811,-0.17085333,0.05247915,0.06417815,0.04079362,-0.03727112,0.0096686,0.02199345,-0.01773878,-0.01649925,-0.04054697,0.0670965,0.07484131,0.02462974,-0.02595761,0.03242823,0.02282473,-0.06364021,0.03571415,-0.01766202,0.07373602,-0.01640538,0.02397698,-0.02738919,0.06267277,-0.02039242,-0.0280666,0.06441575,0.02252378,0.04356879,0.00265054,0.01125177,-0.00119268,0.04625285,-0.15586223,0.10716301,0.0494541,0.03788066,-0.02667957,-0.02124398,0.08353698,-0.04742034,-0.01427818,0.03257185,-0.00508462,0.02601779,0.02770885,-0.04749945,-0.00725527,0.04759115,0.01602898,-0.05945076,-0.06370118,-0.01026118,0.02589204,0.00121258,-0.00810981,0.00739348,0.05460027,0.0291819,0.08843384,-0.03331803,0.00269638,-0.03629132,-0.00489821,0.08629355,-0.00513035,0.00450411,0.03148255,0.00784338,-0.0386814,0.136235,-0.08963833,-0.04422247,-0.00826215,-0.07250835,-0.00782335,-0.04831517,0.048769,-0.05804924,-0.00333378,0.00892985,-0.04059603,0.00333998,0.07627599,-0.0604339,0.06992989,-0.02169842,-0.0720704,0.0477831,-0.00603301,-0.04420698,0.0488354,0.00623653,-0.01414919,0.02907648,0.00953658,0.02551322,-0.03492378,0.03831604,-0.01394128,0.01416819,0.02869809,0.054826,-0.07620879,0.03529642,0.02010814,0.0119575,0.06785805,-0.01932911,0.03887061,-0.04140512,-0.02890461,0.0072872,0.03335399,-0.1100108,0.02481656,0.07692567,-0.01662089,-0.01352497,0.00212494,-0.12026266,-0.01046872,0.06373288,-0.09071516,0.00925987,0.00329577,-0.00277517,0.09005254,-0.00239592,0.00570465,-0.01266535,-0.05369508,-0.0756212,-0.01378862,0.08908071,-0.0573664,-0.02445855,-0.07233092,-0.01058449,-0.03696048,-0.0248086,-0.03738707,0.04168132,-0.01369562,0.02587362,0.02367508,-0.05055743,-0.01770275,-0.00804787,0.03441822,0.01126339,0.05785053,-0.02267909,-0.00754863,-0.02555696,-0.02026289,-0.08060981,-0.02134042,-0.01744276,0.02054124,0.05125039,-0.0698452,-0.00656655,0.02634941,0.03199712,-0.02675391,-0.03519519,-0.05564835,-0.01425508,0.04129569,-0.05370107,0.09348404,0.05320968,-0.03662404,0.07347842,-0.0450437,-0.00233445,0.00148028,-0.04466696,0.11377578,-0.0703361,-0.04655955,-0.02060663,0.04460851,-0.00104147,-0.07752976,-0.02974056,0.01443022,0.05322121,-0.00465334,0.05221082,-0.00831043,-0.00825977,-0.05302369,-0.251147,0.01464256,-0.02350747,-0.00498015,-0.0463713,-0.07089562,0.04279111,-0.0138537,-0.05998528,0.08517678,0.14956316,0.01036645,0.0039451,-0.01105854,-0.00022969,0.0086248,-0.00432463,-0.00956906,-0.08494686,-0.01000172,0.08428396,-0.00404638,0.01767032,-0.03460387,-0.00370048,-0.03597617,0.14442971,0.0057752,0.11492525,0.01152658,0.01571515,0.03226199,0.02248096,-0.11512836,-0.04205501,0.04374627,0.00703883,0.01349369,0.02941308,-0.00291722,-0.01887288,0.02661299,0.01215564,-0.10883992,-0.08583653,-0.05041492,-0.02751005,-0.04399573,-0.06512073,-0.0096837,0.02118995,0.0298395,0.00907098,0.08089139,-0.06275945,-0.01386742,-0.04673789,0.00473063,-0.0291105,0.02557948,0.03132275,0.02874064,-0.01836148,-0.08709893,0.05576929,-0.01028457,0.02541215,-0.07863651,0.18199135,0.02012528,0.01144063,0.10758361,-0.01268119,0.02180253,0.05128254,-0.02699503,0.02204156,-0.0283003,-0.05060867,-0.01984319,-0.01609195,-0.01771414,-0.01421971,0.02806855,0.01146393,0.06349915,-0.02100258,-0.05351643,0.01001811,-0.07140214,-0.02284858,0.06026936,-0.06891434,-0.01080114,0.04150848,-0.0113036,-0.22760999,0.01760286,0.01770151,0.02283217,0.00647629,0.02297246,0.00639318,-0.07941435,-0.01755002,-0.06832151,0.05128634,-0.00441324,-0.04143259,-0.05017919,0.01012336,0.01519417,0.02397437,-0.00856662,0.01356271,-0.03424921,0.04219301,-0.00614928,0.19294739,0.04504449,0.01952629,0.01618016,0.01582978,0.0443576,0.01883127,0.08682381,-0.02521588,0.04020145,0.06065219,0.00448062,0.01961664,0.00912749,-0.00522431,0.02021047,0.08277466,0.00778513,-0.04339047,0.02698259,-0.0519897,0.04480792,-0.01467724,-0.06672505,-0.0477282,-0.06299786,-0.00744846,0.04856579,-0.0039591,0.06917813,-0.03092943,-0.00996447,0.00178895,0.03613931,0.0232589,-0.03348796,0.02759838,-0.02742144,-0.00161925,-0.04902535,0.07063796,0.04927732,-0.02618083],"last_embed":{"hash":"c9a6d6e126239ab763cd410794f2d3977bf2914627346674d1de539e6457dba5","tokens":81}}},"text":null,"length":0,"last_read":{"hash":"c9a6d6e126239ab763cd410794f2d3977bf2914627346674d1de539e6457dba5","at":1745995302802},"key":"README.md##Start and Debug Meshroom in an IDE#{1}","lines":[138,139],"size":201,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start and Debug Meshroom in an IDE#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06391491,-0.02684318,0.08083331,-0.01167439,0.0307027,0.0262548,-0.10585251,0.04718523,0.00335776,0.01879569,0.03420468,-0.04341974,0.00354856,0.04846931,-0.04081396,0.02884665,-0.0149203,0.02201074,0.020711,0.01558582,-0.00189007,-0.00340449,0.03496721,-0.01823701,-0.03109476,0.07480454,0.03422029,-0.05612202,-0.02115133,-0.21023268,0.036994,0.03852796,0.02348085,-0.02212622,-0.01049141,-0.00897171,0.00000391,0.02766597,-0.02923099,0.00905693,0.05108516,0.01736302,-0.03223419,0.00719995,0.01679529,-0.08209374,0.03718244,-0.01352017,0.07269713,-0.04826166,-0.01027644,-0.04498247,0.0619346,-0.06176041,-0.01714883,0.11017747,0.03892727,0.05976295,0.04760401,-0.01945646,0.03892977,0.02794335,-0.15501332,0.09443168,0.01818763,0.03002379,-0.05551127,-0.05313583,0.07037156,0.00206139,-0.01938823,0.01845029,-0.04360976,-0.00004755,0.025451,-0.0207234,0.0367489,-0.02844976,0.0485125,-0.01431947,-0.08362203,-0.00468374,-0.02611418,0.02832184,0.00557666,0.02092449,0.02619424,0.03107931,0.05639551,-0.01309822,-0.01814692,-0.07930481,-0.05361523,0.09316234,-0.02894465,-0.06317518,0.02781623,-0.00661089,-0.0068381,0.13091664,-0.02490627,-0.04473358,0.02016323,-0.03653883,0.03138874,-0.02859304,0.01324408,-0.02122035,-0.01454651,-0.01160062,-0.01381358,-0.00589402,0.01330391,-0.021634,0.08734284,0.02879335,-0.06364185,0.02137915,-0.04828831,-0.00986747,0.0289516,0.00087934,0.0133212,0.00444473,-0.03606363,-0.01004478,0.01641934,0.06363934,-0.00819198,0.0557994,0.01942616,0.02157543,-0.05048699,0.03491234,0.00815022,0.06602944,0.01689404,-0.05457145,-0.00106196,-0.04208002,-0.04175863,0.04963867,0.03441672,-0.07596199,0.02926108,0.07107383,-0.05514617,0.01951659,0.00475677,-0.12086803,0.05936064,0.02038019,-0.08335545,-0.03258823,-0.01608065,-0.0129488,0.05920329,0.05329788,-0.0168991,0.01678254,-0.0256826,-0.07690456,-0.03872483,0.07832596,-0.03234608,-0.0045518,-0.07320333,-0.01480264,-0.01570114,-0.02457357,-0.02315244,0.01752242,-0.02885558,0.0406802,0.04607426,-0.02084734,-0.06098138,-0.00095763,-0.03355727,0.0282912,0.00838728,-0.05133969,-0.0381966,-0.01312786,0.00002145,-0.04622206,-0.02253626,-0.02486766,-0.00298024,0.05425484,-0.04354436,0.03663279,-0.03857727,0.01327788,-0.01175289,-0.02513367,-0.06686516,0.03750955,0.03863707,-0.08149485,0.12543057,0.01838055,-0.04351279,0.04638964,-0.05960593,-0.04616805,-0.03764134,-0.03106279,0.08399124,-0.03544345,-0.06889487,-0.05128656,0.05354207,-0.00372052,-0.05029253,-0.05152673,0.00320443,0.03807644,-0.00086016,0.0739553,0.00449573,-0.05641982,-0.08314546,-0.26367339,0.03033537,-0.06004262,-0.03597794,-0.02962016,-0.07293309,0.02243581,0.00045727,-0.0538774,0.11245501,0.10126515,-0.02222073,0.05211208,-0.00312333,-0.03825636,0.02751523,-0.07563122,-0.03740078,-0.06987013,-0.01113403,0.03454291,0.01314611,0.0058112,-0.09752019,0.00027767,0.00416786,0.16964784,0.03937889,0.13870497,0.02298198,0.04789327,0.01049064,-0.01797383,-0.11542,-0.05111296,0.08179682,0.06956989,0.06096158,0.04961285,0.01404603,-0.03196093,0.03417385,-0.00254622,-0.06809907,0.00471599,-0.0383209,-0.01877249,0.01165737,-0.02303002,0.03859293,0.01590768,-0.01907697,-0.01354246,0.05762972,-0.02312644,-0.01186274,-0.04918718,-0.01533727,0.00783878,0.0408782,0.00766867,0.01232618,0.01915727,-0.06872615,0.0776036,0.00583116,-0.03072154,-0.05960727,0.13895302,0.00790865,0.03172264,0.01942983,0.01324518,0.03118896,0.06528154,0.00571157,-0.02056304,-0.018999,-0.03051488,-0.00872066,-0.02112684,-0.03992546,-0.01233941,0.03010329,0.02543303,0.08000743,-0.03139918,-0.04402984,0.04842864,-0.05567317,-0.02378966,0.03189969,-0.03325803,-0.01200086,0.04033975,-0.0206303,-0.23032841,0.06039974,0.04729834,0.03262151,-0.00874372,0.03317565,0.05230026,-0.04827984,-0.00210229,-0.05779664,-0.02044237,-0.00965736,-0.05010122,-0.06030093,0.02217001,0.03755341,0.01586726,0.02919466,0.05314971,-0.01179846,0.05290887,0.0356325,0.2018636,0.00718342,-0.03197832,0.01267003,-0.01853739,0.03942593,0.06013564,0.09894726,-0.01468986,0.05901905,0.10315731,-0.02799238,0.07533694,0.04955066,0.03657315,0.02471977,0.08336522,-0.02133679,-0.03152416,0.02424176,-0.03345767,0.04501582,-0.00505116,-0.03773826,0.00374961,0.00825763,-0.0392231,0.04284889,-0.03401111,-0.00796557,0.01333171,0.02458761,0.02366244,-0.01276223,0.00664592,-0.03857699,-0.03985677,0.00784194,-0.00504728,-0.02612519,0.05366413,-0.01159573,-0.00711055],"last_embed":{"hash":"8107f1bdde22cfcfbe7cf57f7de318b75efcb9a899d7e270e861752e7e9170c6","tokens":49}}},"text":null,"length":0,"last_read":{"hash":"8107f1bdde22cfcfbe7cf57f7de318b75efcb9a899d7e270e861752e7e9170c6","at":1745995302837},"key":"README.md##Start and Debug Meshroom in an IDE#{2}","lines":[140,140],"size":95,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start and Debug Meshroom in an IDE#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08672684,-0.03543039,0.07108289,0.00290302,0.03573782,0.02943233,-0.10894203,0.02922936,0.00307226,0.02606888,0.04459413,-0.04586867,-0.01206417,0.040821,-0.02468708,0.02957197,-0.01081679,0.0060838,0.00482069,0.01864593,0.02536962,-0.00700489,0.03042871,-0.01990379,-0.00886087,0.07587417,0.01748346,-0.09111131,-0.0260109,-0.22780815,0.02852898,0.03742284,-0.00381176,-0.03125534,-0.0390122,0.00923942,-0.00069415,0.04009198,-0.02922669,0.00486626,0.03938777,0.0093415,-0.03932705,-0.01027785,0.00718721,-0.08556046,0.02213711,-0.00326413,0.04476257,-0.07256764,0.00644536,-0.05858853,0.06764072,-0.04784042,-0.01345186,0.11125293,0.04046115,0.06151712,0.02840029,0.01258862,0.04785304,0.02878915,-0.18575518,0.08918809,-0.00140024,0.04012819,-0.05892332,-0.04271016,0.07907346,-0.02377647,-0.04762503,0.01309615,-0.02575167,-0.0078096,0.02749884,-0.03252216,0.04644198,-0.02898752,0.02805366,-0.00646752,-0.06160867,-0.00250642,-0.01449053,0.04335333,0.01572245,-0.00637207,0.05737984,0.02289391,0.05074717,-0.03026261,-0.00039925,-0.05831751,-0.04380841,0.0834518,-0.06691478,-0.03186252,0.04264158,-0.03886324,-0.00649853,0.14218549,-0.02534653,-0.0213209,0.01494567,-0.01428864,0.02547271,-0.02838011,0.01964574,-0.01837611,-0.00398722,-0.02049344,-0.00487417,0.01599845,0.02819505,-0.02366349,0.06421661,0.02534163,-0.06892315,0.01222155,-0.0490994,-0.00691354,0.03562365,-0.00116669,0.04400072,-0.00183926,-0.05217462,0.01369115,0.00273953,0.0604766,-0.00778108,0.04154242,0.05282777,-0.00202575,-0.06013535,0.00919979,-0.00183784,0.05551901,0.03553868,-0.04394139,-0.01226384,-0.02965273,-0.03690295,0.02022425,0.01672157,-0.05743514,0.01109097,0.087611,-0.08254866,0.0220335,0.02007723,-0.10802045,0.05525077,0.04040936,-0.10818242,-0.02673584,0.01334141,-0.0156395,0.049653,0.08195671,-0.02408756,0.05194501,-0.03250984,-0.06292104,-0.02307619,0.08237497,-0.04405828,-0.00585147,-0.06113317,-0.04569417,-0.00618902,-0.01306922,-0.01451948,0.01205717,-0.00835467,0.04591692,0.05183807,-0.00619886,-0.05878984,-0.00699982,-0.03126524,0.02998759,-0.01493207,-0.03132619,-0.04598962,-0.01962199,-0.00033345,-0.05144304,-0.04916893,-0.03192843,-0.00805127,0.05693953,-0.07488849,0.02221292,-0.02633524,0.02515369,-0.00930162,-0.02199764,-0.06542155,0.03605322,0.06169586,-0.08138282,0.11790626,0.02905876,-0.02722804,0.02095516,-0.02930961,-0.03295913,-0.02650013,-0.05148663,0.04270541,-0.02907573,-0.07574873,-0.05428452,0.0519876,-0.00308822,-0.06455436,-0.04612989,0.00483967,0.03117067,-0.03899878,0.06146958,-0.01055317,-0.02274015,-0.05146521,-0.2512463,0.03606725,-0.04134083,-0.02556456,-0.006652,-0.08574133,0.00890925,-0.00851794,-0.03801664,0.1129019,0.09554954,-0.01205384,0.05782354,0.0152991,-0.02035301,0.02016866,-0.06438675,-0.04312056,-0.04891391,-0.00609248,0.03299817,0.01041119,-0.01224293,-0.08508275,0.0120707,-0.03098937,0.1659863,0.01469575,0.14172192,0.03175467,0.01947983,0.02717122,-0.02231897,-0.10158845,-0.05934758,0.06009231,0.01863874,0.04886102,0.04172115,-0.01696034,-0.03624429,0.05045743,0.00525146,-0.10098625,-0.00198639,-0.00789606,-0.00484938,0.0341286,-0.00345747,0.05454728,0.01625738,0.00631389,-0.00250204,0.03041468,0.00041824,-0.02476257,-0.05753153,-0.01985187,-0.00507217,0.0466722,-0.012632,0.00014786,0.01827665,-0.0674815,0.07568069,0.00878997,-0.0096593,-0.07181236,0.12721959,0.00538618,0.04825671,0.02005174,0.01097575,0.01734241,0.05627913,-0.00343503,-0.00798708,-0.01669716,-0.03079536,-0.02470874,0.0002646,-0.04029085,-0.01287385,0.03484139,0.01070466,0.08396596,-0.02341771,-0.01842632,0.04302911,-0.0370205,-0.0350286,0.02628172,-0.00882048,-0.01927677,0.04735718,-0.00807698,-0.24077553,0.0532437,0.06936993,0.00503249,0.00831623,0.04154189,0.0695013,-0.04028269,-0.0282111,-0.06837941,-0.03725395,-0.0086054,-0.04068404,-0.03860498,0.02522029,0.03250034,0.0327573,0.00492628,0.03500831,-0.00964274,0.05108423,0.0201813,0.19541536,0.03783142,-0.04865464,0.01721901,-0.00776087,0.04340308,0.05531523,0.07022165,-0.03812045,0.08602857,0.09212636,-0.04102391,0.09751497,0.06498058,0.01008854,0.0314403,0.08805481,-0.00807326,-0.00505959,0.02896448,-0.0512985,0.06838005,0.01762802,-0.02082617,0.00537441,0.0344506,-0.03083238,0.05139769,-0.06387136,-0.01749902,0.01378252,0.02698736,0.01591801,0.01736357,0.0238372,-0.02113298,-0.05847434,0.02765788,-0.00245316,-0.04106473,0.02346258,-0.00405118,0.00572351],"last_embed":{"hash":"a53695db91c77718b8b0a0e44480b598db5b4e82bc3b9a835bd2e5175e9ba5ff","tokens":93}}},"text":null,"length":0,"last_read":{"hash":"a53695db91c77718b8b0a0e44480b598db5b4e82bc3b9a835bd2e5175e9ba5ff","at":1745995302860},"key":"README.md##Start and Debug Meshroom in an IDE#{3}","lines":[141,142],"size":223,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:README.md##Start and Debug Meshroom in an IDE#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07259513,0.00996983,0.0583941,-0.01469669,0.03594553,0.01579263,-0.03594678,0.06737003,0.01791412,-0.00679948,0.02696029,-0.05034979,0.01292128,0.05136702,-0.0275959,0.04240361,-0.00097591,-0.04386291,-0.01731559,0.06105154,0.06779919,0.02248596,0.03973276,-0.01758515,-0.03621183,0.06953851,0.02602288,-0.04249927,-0.04005279,-0.2038319,0.02285482,0.02426548,0.06391507,-0.0479046,0.05926697,-0.04485523,0.01967611,0.01443701,-0.02831305,0.00349971,0.05581686,0.03497256,-0.01170554,0.03589023,0.03007821,-0.03820794,-0.01392002,0.00623497,0.07959666,-0.02956436,0.00157269,-0.04581679,0.06011707,-0.02313951,0.01184121,0.10072366,0.03925377,-0.00090767,0.03419703,0.00294668,0.01524585,0.02223394,-0.17919949,0.10416538,0.02480948,0.04787844,-0.05257005,-0.05674152,0.08821103,-0.03362659,0.00254929,0.00699869,-0.03587043,0.00122125,0.04006993,-0.00913138,0.00338003,0.02432666,0.04448415,-0.02031689,-0.08327464,0.03664023,0.01808125,0.0240752,-0.04413171,0.01038206,0.02306184,0.01338265,0.04594915,-0.02839075,-0.00845924,-0.04475295,-0.04602456,0.11236826,-0.0782321,-0.05496555,0.01234122,0.02355392,-0.00655619,0.16610143,-0.05510045,-0.05110026,0.04144234,-0.04235191,0.00690093,-0.04791211,0.00609793,-0.04518909,0.00340015,-0.00016348,-0.03913005,-0.00351917,0.0525947,-0.05470558,0.11087579,0.00646715,-0.0053015,0.00085523,-0.04971755,-0.02660408,0.00364982,0.02195794,-0.01478444,0.01012867,-0.01649456,-0.01549425,-0.00310723,0.06447741,-0.01737392,0.02973526,0.00506545,0.07236009,-0.07830568,0.01811555,0.01993623,0.05688156,0.02195712,-0.01895387,-0.00774211,-0.04371344,-0.04843226,-0.05215525,0.00871446,-0.0524239,-0.02097952,0.06755117,-0.10203299,-0.00123842,-0.01786638,-0.12359898,0.05045684,0.05438281,-0.07862522,-0.02676071,-0.0144399,-0.00493775,0.0575764,0.07337581,-0.00471456,-0.03735356,-0.03701597,-0.0934322,-0.06654432,0.07603475,-0.03594431,-0.0170218,-0.09665646,-0.00877793,-0.01830968,-0.04299658,-0.03589715,0.00866589,-0.04578651,0.00320874,0.05474052,-0.01108447,-0.01561252,-0.01873374,0.01740401,0.0259939,0.0217064,-0.04039441,-0.04435636,-0.02156824,-0.01337043,-0.06612924,-0.0402583,-0.0251869,0.0256851,0.06977472,-0.02450716,-0.01843976,-0.04152605,0.02333121,-0.02941374,-0.04103699,-0.04758202,0.02750988,0.05523412,-0.08397596,0.19459346,0.02098437,-0.01848846,0.08273619,-0.01588534,-0.00977936,0.0020106,-0.02555903,0.07428232,-0.03003695,-0.04455888,-0.02922921,0.01218094,-0.01219662,-0.02251212,-0.01813728,0.0171939,0.03462928,0.00122602,0.06253567,0.00201179,-0.0408447,-0.06278834,-0.26086184,0.03505367,-0.04349931,-0.04488641,-0.03033112,-0.0404864,0.04679773,0.00553835,0.00498749,0.08508142,0.10461747,0.04096237,0.00709194,-0.03888332,-0.00407416,0.01181077,-0.03861872,0.00263662,-0.08480811,-0.01926927,0.06816454,-0.00965445,-0.03685117,-0.0468747,-0.00336462,-0.05857925,0.16355626,0.022603,0.08792791,0.02977039,0.04888649,-0.00443527,0.00918199,-0.05964674,0.01189281,0.05382264,0.00579066,0.01445159,0.0167641,-0.02327215,-0.06280855,0.04793895,0.02012183,-0.1052806,0.00237096,-0.02815193,-0.0166718,-0.02044529,-0.05946152,0.04113008,0.04826219,0.00252597,0.02204168,0.0931631,-0.04837575,0.00198871,-0.04118687,-0.01481844,-0.01242923,0.04199158,0.01869698,-0.03253536,0.01746727,-0.06825152,0.04522442,0.03445308,-0.01755933,-0.06944633,0.08956096,-0.0287612,0.02959639,0.03707301,0.02406259,0.02920823,0.02664614,0.0330697,-0.00266969,-0.04568026,-0.01467534,-0.04101147,-0.02293489,-0.06438848,0.04611695,0.01758818,0.02624493,0.07949544,-0.03894714,-0.03041811,0.04564025,-0.06393515,0.01954507,0.07231028,-0.03769445,-0.01996085,0.00748017,-0.02973139,-0.23543578,0.04135531,0.01465967,0.04083844,0.00007598,0.00865353,-0.00930898,-0.04919606,0.00798773,-0.05856958,0.02334502,-0.01962334,-0.06687856,-0.04604254,0.04198452,0.03228396,-0.00886997,0.00990324,0.0564524,0.00113253,0.03868911,0.02520912,0.17701547,0.02721763,0.01399587,0.01858684,0.01289429,0.06151589,0.0703762,0.07617576,-0.01265407,0.04478714,0.1350327,-0.03706941,0.05861228,0.00655229,0.02519111,0.03132972,0.06807355,-0.03292788,-0.06069869,0.04568886,-0.03081081,0.02047199,0.01679012,-0.06076665,0.01913179,-0.02390712,-0.00660366,0.01832255,-0.0447919,0.02147734,0.01969038,0.03692396,-0.00241026,0.00362092,0.01496841,-0.03160363,-0.01451233,0.02001172,0.01181915,-0.0546552,0.01957065,0.01294302,0.00092159],"last_embed":{"hash":"e6ba0a02f9183f822c3ab24292e823129a0a5a0c4fe8ef666054848d08fd5bf5","tokens":82}}},"text":null,"length":0,"last_read":{"hash":"e6ba0a02f9183f822c3ab24292e823129a0a5a0c4fe8ef666054848d08fd5bf5","at":1745995302903},"key":"README.md##Start and Debug Meshroom in an IDE#{4}","lines":[143,145],"size":111,"outlinks":[{"title":"image","target":"https://user-images.githubusercontent.com/937836/127321375-3bf78e73-569d-414a-8649-de0307adf794.png","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##FAQ": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.058314,-0.04729108,0.04992123,0.03257431,0.03802611,0.02856859,-0.05098495,0.05398641,-0.02988772,0.01612691,0.00752183,-0.05730975,0.00449172,0.04507998,0.01312004,0.0287014,0.00648376,-0.02432779,-0.02140717,0.01700453,0.07379879,-0.0211728,0.05573788,-0.00121863,-0.00079838,-0.0007327,-0.00551124,-0.07122076,-0.02709975,-0.17890862,-0.01383708,0.03692003,0.03853467,-0.00276462,0.00342996,-0.00983992,0.02711822,0.00716344,-0.02779314,0.04446025,0.05407634,0.02490181,-0.03396291,0.00759525,0.03772031,0.00099456,0.05496119,-0.01914759,0.04680604,-0.0577068,-0.00734845,-0.04505922,0.05437434,-0.02277466,0.02842427,0.07626191,0.06392991,0.01713165,0.04558801,-0.03001321,0.08224511,0.05324599,-0.19563991,0.12703298,-0.01132863,0.0297741,-0.05577253,-0.07603896,0.03991597,-0.01307768,0.00707529,-0.01781749,-0.07180213,-0.01398475,0.02955262,-0.02538625,0.03456329,-0.02529626,0.02142904,-0.01160939,-0.07416616,0.09509283,-0.00736546,-0.02963546,-0.01905569,-0.04347347,0.00283115,0.02483262,0.03398722,0.00511126,-0.02985406,-0.07062726,0.03923142,0.09008969,-0.04391937,-0.059018,0.02501104,-0.00374231,0.02827601,0.15246069,-0.04712598,-0.01723268,0.05998498,-0.00350809,0.01193127,-0.03088324,0.01523969,-0.01355427,-0.00860383,0.04280774,-0.02181402,0.00487805,-0.01996713,-0.0053864,0.0553027,0.02052182,0.04720589,0.04442868,0.00392733,-0.00161051,-0.01321657,0.00881125,0.06136909,-0.01312757,0.02728096,-0.04299426,0.02257372,0.04141277,-0.00252304,0.04869649,-0.00628113,0.0208236,-0.04411821,-0.00129376,0.00058558,0.03552197,0.044439,-0.1078372,0.01470703,-0.01395449,-0.03081882,-0.06867753,0.00988738,-0.06147875,-0.04519498,0.11756176,-0.0479939,0.00341388,-0.03192092,-0.07948694,0.0247175,0.07247873,-0.06971759,-0.03178631,0.01969274,0.00018634,0.08355252,0.09302997,-0.04219039,0.01914825,-0.00313794,-0.06421561,-0.04817832,0.08775784,-0.01427694,-0.07411799,-0.07816004,0.01276664,-0.00383976,-0.08303384,-0.01544259,0.02930529,-0.04558428,0.06291038,0.0684832,0.02304304,-0.09741749,-0.04744343,0.00579212,0.00087142,-0.00656576,-0.06298341,-0.00137264,-0.01366165,-0.01582385,-0.03613161,0.0153955,-0.04486536,-0.01937589,0.03985341,-0.02542673,-0.02564946,-0.02684428,0.06108359,-0.02909439,-0.01523982,-0.0257929,0.02059657,0.03213985,-0.07190193,0.06265792,0.03056296,-0.06187329,0.04101776,-0.07030175,0.04000667,-0.00299786,-0.039441,0.06948412,-0.01150296,-0.0577099,-0.04812725,-0.02520158,0.01044518,-0.04841754,-0.03585612,0.04251504,0.02716436,-0.04413939,-0.0092688,0.01940348,-0.05909382,-0.11324042,-0.25247127,0.02520323,-0.04509505,-0.03606915,-0.02696142,-0.07685047,0.01185885,0.00028333,-0.01140158,0.15377715,0.11418746,0.01812146,-0.01526438,-0.06639722,-0.04073405,0.04200031,-0.05857172,-0.04735154,-0.08336292,-0.02725862,0.03292052,0.03517525,-0.02462354,-0.08866443,0.0095306,-0.05243353,0.14362521,0.05250429,0.11140479,0.01904085,0.06928139,-0.0504674,-0.01014641,-0.09021014,0.00179994,0.0465112,-0.01378035,0.01957843,0.00088198,-0.04140744,-0.0329982,0.05960703,-0.03542957,-0.11598729,0.00745244,-0.00476085,0.00734156,0.0051875,-0.03861346,0.07022973,0.01267389,0.03555536,-0.00229968,0.06076446,-0.00085978,0.03553776,-0.0412152,-0.03019338,-0.01589597,0.08064617,-0.00049326,0.05625443,0.02353225,-0.08565918,0.05415118,0.03902493,-0.03601852,-0.06522111,0.09354956,-0.02683535,0.00861285,0.05657954,0.00483304,0.04757255,0.02716088,0.04591753,0.0350779,-0.00009789,0.01965916,-0.03901635,-0.01556035,-0.03726925,0.0444467,0.02669156,0.03179186,0.0679137,-0.05497393,-0.02823433,0.0261447,-0.02005675,-0.03731243,0.03035075,-0.0163837,-0.101725,-0.01625333,-0.0056149,-0.21442614,0.03283622,0.08246753,0.04727688,0.00839887,-0.00598454,0.05547335,-0.03400375,0.03188712,-0.01547944,0.05289465,0.00642353,-0.06514174,-0.03671069,0.02578297,0.04961047,0.02984348,0.02910996,0.03993602,0.0564973,0.03557381,0.03302581,0.19215836,0.03748526,-0.00540684,0.0156441,-0.03410493,0.04032449,0.03094545,0.06121774,-0.03809905,0.0352977,0.05965671,-0.03573738,0.0300604,0.0097309,0.00742235,0.04647681,0.04655851,-0.02807909,-0.02770413,0.01927897,-0.07510071,0.04544201,0.06284156,-0.01565742,-0.00469509,0.01039366,-0.04500263,0.03737403,-0.03693807,-0.0364491,-0.00292333,0.022807,0.02922981,-0.01450378,0.02849191,-0.06083271,-0.00364335,0.03458218,-0.05352169,-0.03010093,-0.00204862,0.00391594,0.01288902],"last_embed":{"hash":"0d459612ba7fc09e5bf697bc7b726c144fa5408e2a412ac6eba96e41fb83066b","tokens":41}}},"text":null,"length":0,"last_read":{"hash":"0d459612ba7fc09e5bf697bc7b726c144fa5408e2a412ac6eba96e41fb83066b","at":1745995302939},"key":"README.md##FAQ","lines":[146,150],"size":101,"outlinks":[{"title":"Meshroom wiki","target":"https://github.com/alicevision/meshroom/wiki","line":3}],"class_name":"SmartBlock"},
"smart_blocks:README.md##FAQ#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05528568,-0.04100659,0.05066338,0.02835245,0.04101954,0.03061245,-0.05278996,0.04757807,-0.03335165,0.01755381,0.00977213,-0.04466109,0.01209442,0.05240517,0.02182,0.02388914,0.00858993,-0.02012552,-0.02413591,0.01115094,0.0724287,-0.01386844,0.04552367,-0.00329732,0.00223437,0.00004832,-0.00632664,-0.07092937,-0.02843035,-0.17702043,-0.01286043,0.02663547,0.03493479,-0.00577795,0.0127095,-0.01638485,0.02947137,0.00794605,-0.03342656,0.04914014,0.06123371,0.02589703,-0.03218844,0.00948584,0.03525206,-0.00557494,0.05262325,-0.01584409,0.05173369,-0.05361267,-0.0101853,-0.05057264,0.05246314,-0.02763532,0.02860039,0.08297051,0.06220698,0.02508491,0.03981599,-0.03033024,0.08002154,0.05356673,-0.18989761,0.12370663,-0.02416073,0.0189623,-0.06025588,-0.07109073,0.0510714,-0.00573117,0.00361591,-0.02028565,-0.06864225,-0.01130931,0.03260551,-0.03756995,0.02081353,-0.02586106,0.01876526,-0.01137969,-0.08076397,0.09687763,-0.00062803,-0.03831329,-0.02176202,-0.05288985,-0.00271173,0.02431618,0.04154524,-0.00110082,-0.02396142,-0.07586159,0.0325673,0.09084377,-0.04219325,-0.05250763,0.01655346,-0.00859635,0.03950778,0.16183056,-0.04274864,-0.01987378,0.06472927,0.00416518,0.01392804,-0.03465128,0.01307729,-0.00990195,-0.01185961,0.04399288,-0.02756626,0.00420545,-0.01616012,-0.01066675,0.05406383,0.00673696,0.0477379,0.04503062,-0.00311734,-0.01000608,-0.01390073,0.00231021,0.0661146,-0.01101247,0.02070225,-0.03955787,0.02315983,0.03214576,0.00000831,0.04864407,0.00210339,0.01242314,-0.04470977,-0.00882913,0.00043038,0.03605989,0.06191046,-0.11456679,0.01554934,-0.01242025,-0.03319043,-0.07422353,0.01669026,-0.05882348,-0.04990355,0.12450647,-0.0509792,0.00477821,-0.04331213,-0.08899242,0.01448462,0.06957692,-0.05756863,-0.03580879,0.00964627,-0.00343978,0.08483212,0.07854297,-0.04434667,0.00998368,-0.00451563,-0.06646389,-0.04989863,0.08982086,-0.01776077,-0.06749813,-0.07721633,0.02406711,-0.00858472,-0.08946496,-0.01191165,0.02714648,-0.05384499,0.05340085,0.07370578,0.01242892,-0.0978042,-0.04216642,0.00980427,-0.00085145,0.00134714,-0.05999187,-0.00696861,-0.01264393,-0.0102371,-0.04736974,0.01183825,-0.04175768,-0.01164475,0.05366023,-0.03095867,-0.02999668,-0.03065488,0.05664127,-0.03549809,-0.01424605,-0.02231519,0.02521439,0.03367075,-0.06913203,0.0645693,0.03021976,-0.0615314,0.04335824,-0.06835558,0.03342813,-0.00283055,-0.03426044,0.06292721,-0.01251745,-0.07135309,-0.03893223,-0.02285521,0.01647529,-0.04210316,-0.02663387,0.04022025,0.01320068,-0.04809668,-0.00611937,0.01383145,-0.04807548,-0.10377063,-0.24924867,0.03826562,-0.04742306,-0.02690636,-0.02685351,-0.0706859,0.0223407,0.00040821,-0.01776632,0.15996717,0.10741369,0.01878856,-0.0082747,-0.05174454,-0.03749163,0.0440494,-0.04578603,-0.04102849,-0.09548412,-0.03037005,0.03515483,0.0303994,-0.02052008,-0.08944383,0.01498574,-0.05579435,0.14756727,0.04232634,0.1067628,0.0173676,0.0677762,-0.05647776,-0.01725498,-0.08698146,0.00968123,0.05364999,-0.01920843,0.01418851,-0.00237655,-0.04491917,-0.03244843,0.0579688,-0.03669493,-0.11706205,0.00747739,-0.00812378,0.02787923,0.00287751,-0.04158992,0.07073833,0.01162581,0.03580023,0.00090439,0.05789324,-0.01037516,0.04115728,-0.03645352,-0.02291642,-0.01948887,0.08065799,0.00077537,0.05983055,0.01882006,-0.08480562,0.05282675,0.03272314,-0.03337266,-0.06475293,0.09258141,-0.02035975,0.00850988,0.0516874,0.00013924,0.05140832,0.03590904,0.05391232,0.03731157,-0.00456722,0.02212898,-0.0431957,-0.01497875,-0.039612,0.04501521,0.02531086,0.03169225,0.06598691,-0.05234933,-0.03787664,0.03132674,-0.02289395,-0.04593981,0.02678646,-0.01375182,-0.0873277,-0.02195582,0.00618763,-0.2147581,0.02924618,0.08820827,0.04598361,0.00651928,-0.00528358,0.05238911,-0.03725418,0.03914071,-0.00838532,0.05818448,0.00595678,-0.07502421,-0.04096347,0.01825864,0.04423977,0.03772227,0.02861994,0.04702643,0.05573161,0.03851137,0.03058191,0.18666872,0.03218935,-0.00647793,0.01480691,-0.02641925,0.03467891,0.02517426,0.06108482,-0.03814121,0.0289529,0.06198585,-0.03083857,0.02600816,0.02695661,0.01393027,0.04319666,0.0454229,-0.03183914,-0.02963465,0.01901713,-0.08857536,0.04737246,0.06619435,-0.02085462,-0.00400351,0.01047186,-0.03052545,0.03397282,-0.02961658,-0.03265019,-0.00075884,0.02124047,0.02914529,-0.01266636,0.02942985,-0.05161743,0.0009321,0.04084719,-0.05225287,-0.0279236,0.00620161,0.00196514,0.01547796],"last_embed":{"hash":"86bcac4434c0d941dc2f0fddc94f607c1960cc3c555a316b91621d0ada1f1fc5","tokens":40}}},"text":null,"length":0,"last_read":{"hash":"86bcac4434c0d941dc2f0fddc94f607c1960cc3c555a316b91621d0ada1f1fc5","at":1745995302958},"key":"README.md##FAQ#{1}","lines":[148,150],"size":93,"outlinks":[{"title":"Meshroom wiki","target":"https://github.com/alicevision/meshroom/wiki","line":1}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Contact": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.042235,-0.00683278,0.00653572,-0.02638122,0.00411273,-0.00703086,0.01921159,0.0642987,-0.07219841,-0.03016111,0.00370308,0.00568953,0.01399014,0.0275668,-0.00059901,0.00090556,0.0376569,-0.06954812,-0.00484105,0.02978632,0.01880344,-0.01981029,0.00435173,0.059771,0.01682309,-0.00986959,-0.04897048,-0.0905147,-0.10843769,-0.15462725,0.02011833,0.01908101,0.03598856,0.01851017,-0.0017094,0.00153587,0.01854325,-0.00191335,-0.100038,0.02926976,0.05542433,-0.00542674,-0.04758856,0.01727146,0.00466266,-0.01996061,0.01341643,-0.05983947,0.02160363,-0.09215719,-0.00036792,0.02373515,0.01677463,0.03128491,-0.02016799,0.07448257,0.02504419,-0.01145079,-0.02250401,0.00841339,0.09063185,0.074,-0.22313619,0.11438207,-0.02843752,0.05031085,-0.02499792,0.00320806,0.0371692,0.00098596,-0.03446824,0.05373139,-0.0472187,0.01968138,0.00027071,-0.00561076,0.01747287,-0.00499685,-0.0636503,-0.03148432,-0.12166893,0.03277592,-0.01415422,-0.01827767,0.00770598,-0.01827985,0.09524895,0.01084628,0.09792716,0.00199296,-0.00965081,-0.03032369,0.02080547,0.0387591,-0.06474105,-0.04759328,0.03310301,-0.03829101,-0.06845091,0.13844928,-0.0907822,0.0680645,0.05343455,-0.04884867,-0.02816055,-0.01957945,0.03782849,-0.0409121,-0.06323701,0.04539278,0.03430392,-0.03143195,-0.0270527,-0.01812308,0.03475003,0.03353208,0.01295995,0.01935503,-0.00627858,-0.07242196,-0.0002803,0.02211162,0.02382201,0.0105851,0.05401015,-0.02861816,-0.00871366,0.08029611,0.01725151,0.08102857,0.06450299,-0.01827915,-0.07494119,-0.03995799,-0.00072124,0.00194637,0.01998597,-0.07553885,0.03500668,0.03303894,0.02412198,-0.04891384,-0.0078815,-0.07358909,0.01463489,0.11968083,-0.0425427,0.01796477,-0.06389048,-0.06973813,0.0401632,-0.00739534,-0.04844347,-0.07569494,0.03770614,-0.03225583,0.10504251,0.03730485,-0.03098263,0.02780097,-0.03178503,-0.01770611,-0.04137436,0.10652198,0.0294371,-0.10641287,-0.0494276,-0.03118804,-0.02225443,-0.02684315,-0.00194049,0.09887102,0.03772243,0.06319872,0.0842604,0.06547014,-0.04053454,-0.04422355,0.03798554,0.02129772,-0.02502171,-0.00875833,0.03410336,-0.01182931,0.01923568,-0.05886035,-0.01462016,-0.05945505,-0.02265799,0.00816814,-0.06132447,-0.02903986,-0.01154646,0.06451032,-0.09239092,0.00130428,-0.03316887,-0.00510555,0.05852388,-0.01791218,0.0031946,-0.02309983,-0.0287084,0.03233235,-0.01031635,0.07284606,0.02471833,-0.03664282,0.08682632,0.00962388,-0.02939893,-0.04478733,0.03684361,0.0237822,-0.08207416,-0.04271804,0.01840951,0.03288724,-0.01979031,0.02394624,0.01900159,-0.00016553,-0.06525346,-0.1899448,-0.01475893,0.00356652,-0.01389142,-0.02277686,-0.08985469,0.03114788,-0.01220544,0.0063877,0.18044183,0.14096518,-0.02606612,-0.00479602,0.040918,0.01336128,0.03414045,-0.05843098,-0.05936369,0.01343084,-0.02300932,0.02034706,0.016115,0.02845535,-0.0525961,0.08122794,0.03688124,0.09794421,0.08164495,-0.00434227,-0.00366147,0.04197595,0.01957258,-0.02299297,-0.10885129,0.01606517,0.03319263,-0.00069298,0.07127474,0.01299661,-0.03260509,-0.05668028,-0.01379821,-0.06538303,-0.06104587,-0.0164769,-0.01534808,-0.04784893,-0.01201405,-0.04071627,0.04713717,-0.00204879,-0.06463417,0.02951583,0.03023887,0.01642699,-0.02898145,-0.08497977,-0.03381707,-0.04309976,0.08426679,-0.0004051,0.02324752,0.02326772,-0.07389206,0.04060188,0.01322377,-0.05721103,0.01525629,0.02701364,0.02785353,-0.05425918,0.04887216,-0.04907863,0.11207069,0.01279988,0.0543891,0.07496274,-0.01888385,-0.02806186,-0.09179591,0.02541526,0.00645619,0.0747918,0.0340722,0.00848204,0.04326707,0.02038453,0.01089882,-0.02284664,-0.00525315,-0.03607583,0.00185662,-0.08725203,-0.04242185,0.06880619,-0.0217574,-0.17656289,0.04528173,-0.0035654,0.04996827,0.04800429,0.05082902,0.05137324,-0.04745672,0.01912057,-0.013267,0.00541281,0.03525315,-0.02893458,-0.05881885,0.01925274,0.05178783,-0.00891541,-0.01197476,-0.04711885,-0.02537283,0.07590403,-0.01888616,0.20460153,0.04138794,-0.01639247,0.00446152,0.04949046,0.03794942,-0.00108708,0.00270511,0.03174072,0.01760159,0.03787313,-0.06666894,0.0197084,-0.01625869,0.00556435,0.0082808,0.04327141,0.02036087,-0.04284229,0.00698884,-0.04584963,0.03630594,0.06485574,0.0511766,-0.07746838,-0.00817493,0.00171464,0.00601905,-0.05583158,-0.06374948,-0.00306933,0.00954455,0.04476569,0.01677275,0.00631577,-0.07081749,0.01442046,0.0628854,0.01054428,-0.0501721,0.06340741,0.0104422,0.03126073],"last_embed":{"hash":"293a2b39eb184deaa7b2006d11b68422b6ba8919fd3ed1119599877b3f16afa7","tokens":136}}},"text":null,"length":0,"last_read":{"hash":"293a2b39eb184deaa7b2006d11b68422b6ba8919fd3ed1119599877b3f16afa7","at":1745995302976},"key":"README.md##Contact","lines":[151,158],"size":489,"outlinks":[{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":4},{"title":"http://groups.google.com/group/alicevision","target":"http://groups.google.com/group/alicevision","line":5},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":7}],"class_name":"SmartBlock"},
"smart_blocks:README.md##Contact#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03215451,-0.00394727,0.00837784,-0.02492805,-0.00238124,-0.00858002,0.02042805,0.06424665,-0.07038241,-0.03060708,0.00451363,0.01669821,0.01328513,0.02299089,0.00460984,0.00291211,0.03790983,-0.05887192,-0.00425662,0.02890237,0.01059728,-0.01968985,0.00414401,0.06496186,0.01625558,-0.01482989,-0.05479869,-0.0908175,-0.10577017,-0.14966524,0.02393322,0.01239688,0.03340015,0.01829148,-0.00851351,0.0035308,0.02508598,-0.00278169,-0.10482777,0.03077062,0.05863962,-0.00165046,-0.04266243,0.01333952,0.0065055,-0.01835474,0.01938321,-0.05988979,0.03039331,-0.09133283,0.0074661,0.02082922,0.00937145,0.03437794,-0.01962205,0.07088783,0.02901624,-0.01290891,-0.02473097,0.01513742,0.09018978,0.07022308,-0.2184238,0.12001058,-0.02885853,0.0509503,-0.02161569,0.00129057,0.04104034,0.00311485,-0.03888977,0.05628066,-0.04870414,0.02675985,-0.00407026,-0.01119593,0.01479612,0.0015546,-0.06624226,-0.02978848,-0.12089105,0.03255476,-0.02265281,-0.01165951,0.01038542,-0.0207073,0.09675044,0.00186739,0.09770186,0.00112949,-0.0041527,-0.02835876,0.02422826,0.03780581,-0.07006846,-0.04869216,0.03494658,-0.03648344,-0.07493104,0.14013687,-0.08961212,0.06398404,0.05058046,-0.04788765,-0.03081573,-0.02520215,0.03533908,-0.04135305,-0.06997561,0.0451437,0.03730517,-0.03239803,-0.0345858,-0.01629293,0.0344696,0.03648207,0.00632699,0.01059514,-0.01131705,-0.07985079,-0.00865882,0.02346856,0.02017923,0.01091471,0.05456078,-0.03033936,-0.01497862,0.08025596,0.01061471,0.08261429,0.06478576,-0.0269975,-0.08123931,-0.03498091,0.00003288,-0.00423432,0.01835335,-0.06447975,0.03709444,0.03692061,0.02797597,-0.05281648,-0.00713812,-0.06677607,0.01707533,0.120954,-0.04592082,0.02487761,-0.06364317,-0.0739039,0.04590007,-0.00691146,-0.0461503,-0.07698692,0.0351686,-0.03436897,0.0996561,0.03823378,-0.02709337,0.02281559,-0.03546847,-0.0101317,-0.04061086,0.11244522,0.02767016,-0.10199279,-0.05072656,-0.03210446,-0.01752418,-0.01835823,-0.00235531,0.10020491,0.04511689,0.06475128,0.08349913,0.06057498,-0.0473179,-0.03743846,0.03514343,0.02771051,-0.02680147,-0.00480864,0.03777052,-0.01148153,0.02422438,-0.06224436,-0.0105743,-0.05846043,-0.02002464,0.00690994,-0.06306273,-0.02175635,-0.00816405,0.05639354,-0.09004234,0.00019727,-0.02970747,-0.00551032,0.05774231,-0.01319695,0.00269347,-0.02262054,-0.03001556,0.02537632,-0.0064997,0.06807993,0.02036475,-0.03846731,0.09234111,0.00894874,-0.02247211,-0.04757743,0.03855716,0.01478284,-0.08434999,-0.042246,0.01984837,0.02806497,-0.02517591,0.02620619,0.02448029,0.00748848,-0.06528996,-0.18799046,-0.02168639,-0.0018005,-0.00348419,-0.01982447,-0.10198405,0.03423542,-0.0111453,0.00112266,0.1842261,0.13434106,-0.02828788,-0.00343082,0.0492366,0.00950081,0.03411638,-0.0461314,-0.05669977,0.02010267,-0.02731546,0.01496861,0.01639055,0.02816258,-0.04251161,0.08688948,0.0433058,0.10095951,0.07634205,-0.01119747,-0.00563091,0.02912458,0.01292505,-0.02980675,-0.11219735,0.01459002,0.0345838,0.00026094,0.06463892,0.01620748,-0.03008865,-0.05890769,-0.01230324,-0.06335101,-0.05531178,-0.01707515,-0.01461282,-0.04594485,-0.01497405,-0.04601659,0.04404519,-0.00348602,-0.06501614,0.03043968,0.02143321,0.02111443,-0.02897277,-0.08601189,-0.03211048,-0.04323685,0.08686659,-0.00369276,0.01890686,0.02171498,-0.07261048,0.03875064,0.01451583,-0.05770766,0.01830574,0.02532674,0.03528505,-0.05843296,0.049603,-0.05446409,0.11519515,0.01773628,0.0496876,0.07357711,-0.01768803,-0.02487748,-0.09506267,0.02699733,0.00324525,0.07203821,0.03556379,0.00083634,0.0389187,0.0281203,0.01069301,-0.02698219,-0.00557619,-0.04119419,0.00679962,-0.08762606,-0.03917328,0.07102242,-0.01649164,-0.17530069,0.04213379,-0.00274392,0.04868015,0.04347141,0.05184453,0.04960008,-0.04549798,0.02025559,-0.01272443,0.00375922,0.03506591,-0.03088861,-0.05456077,0.01827403,0.05090111,0.00035998,-0.0168826,-0.05512913,-0.0291897,0.07571807,-0.02679373,0.20234917,0.03930577,-0.01490665,0.00635634,0.0528977,0.04184879,-0.00230853,-0.00386239,0.03886427,0.01934498,0.03000909,-0.0623589,0.02411629,-0.01201297,0.00164629,-0.00054179,0.04692044,0.01744553,-0.04589792,0.00668396,-0.04806719,0.03183831,0.06333763,0.05641095,-0.07565745,0.0003756,0.00292514,0.00825105,-0.053004,-0.05766803,-0.0051852,0.00793432,0.04716736,0.01004291,-0.00060116,-0.06713404,0.00941694,0.06260642,0.00753674,-0.04638189,0.06256846,0.00730117,0.03276848],"last_embed":{"hash":"9d1629b59e691dd1b6c756805b4072a00ed9d38a07bc2b0e9038622ceeecd965","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"9d1629b59e691dd1b6c756805b4072a00ed9d38a07bc2b0e9038622ceeecd965","at":1745995303036},"key":"README.md##Contact#{1}","lines":[153,158],"size":477,"outlinks":[{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":2},{"title":"http://groups.google.com/group/alicevision","target":"http://groups.google.com/group/alicevision","line":3},{"title":"<EMAIL>","target":"mailto:<EMAIL>","line":5}],"class_name":"SmartBlock"},