
"smart_sources:Obsidian AI Providers.md": {"path":"Obsidian AI Providers.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"3bae122518ad759c9a1221dc143f8fd35d19ec3916836111a355f5a4bfb37b0f","at":1741741841208},"class_name":"SmartSource2","outlinks":[{"title":"image","target":"https://github.com/user-attachments/assets/09b6313d-726c-440b-9201-1b2f2e839fa7","line":14},{"title":"Local GPT","target":"https://github.com/pfrankov/obsidian-local-gpt","line":18},{"title":"https://obsidian.md/plugins?id=ai-providers","target":"https://obsidian.md/plugins?id=ai-providers","line":41},{"title":"BRAT","target":"https://obsidian.md/plugins?id=obsidian42-brat","line":45},{"title":"Ollama","target":"https://ollama.com/","line":51},{"title":"from the library","target":"https://ollama.com/library","line":52},{"title":"check the docs","target":"https://github.com/ollama/ollama/blob/main/docs/faq.md#how-do-i-configure-ollama-server","line":59},{"title":"API keys page","target":"https://platform.openai.com/api-keys","line":65},{"title":"Open WebUI","target":"https://docs.openwebui.com/tutorials/integrations/continue-dev/","line":72},{"title":"llama.cpp","target":"https://github.com/ggerganov/llama.cpp","line":73},{"title":"llama-cpp-python","target":"https://github.com/abetlen/llama-cpp-python#openai-compatible-web-server","line":74},{"title":"LocalAI","target":"https://localai.io/model-compatibility/llama-cpp/#setup","line":75},{"title":"Text generation web UI","target":"https://github.com/pfrankov/obsidian-local-gpt/discussions/8","line":76},{"title":"LM Studio","target":"https://lmstudio.ai/","line":77},{"title":"API keys page","target":"https://openrouter.ai/settings/keys","line":84},{"title":"API keys page","target":"https://aistudio.google.com/apikey","line":91},{"title":"API keys page","target":"https://groq.com/docs/api-reference/introduction","line":104},{"title":"Docs: How to integrate AI Providers in your plugin.","target":"app://obsidian.md/packages/sdk/README.md","line":109},{"title":"Local GPT","target":"https://github.com/pfrankov/obsidian-local-gpt","line":127},{"title":"Colored Tags","target":"https://github.com/pfrankov/obsidian-colored-tags","line":128}],"blocks":{"#Obsidian AI Providers":[1,128],"#Obsidian AI Providers#{1}":[3,7],"#Obsidian AI Providers#{2}":[8,8],"#Obsidian AI Providers#{3}":[9,9],"#Obsidian AI Providers#{4}":[10,11],"#Obsidian AI Providers#{5}":[12,15],"#Obsidian AI Providers#Required by plugins":[16,19],"#Obsidian AI Providers#Required by plugins#{1}":[18,19],"#Obsidian AI Providers#Supported providers":[20,29],"#Obsidian AI Providers#Supported providers#{1}":[22,22],"#Obsidian AI Providers#Supported providers#{2}":[23,23],"#Obsidian AI Providers#Supported providers#{3}":[24,24],"#Obsidian AI Providers#Supported providers#{4}":[25,25],"#Obsidian AI Providers#Supported providers#{5}":[26,26],"#Obsidian AI Providers#Supported providers#{6}":[27,27],"#Obsidian AI Providers#Supported providers#{7}":[28,29],"#Obsidian AI Providers#Features":[30,36],"#Obsidian AI Providers#Features#{1}":[32,32],"#Obsidian AI Providers#Features#{2}":[33,33],"#Obsidian AI Providers#Features#{3}":[34,34],"#Obsidian AI Providers#Features#{4}":[35,36],"#Obsidian AI Providers#Installation":[37,46],"#Obsidian AI Providers#Installation#Obsidian plugin store (recommended)":[39,42],"#Obsidian AI Providers#Installation#Obsidian plugin store (recommended)#{1}":[41,42],"#Obsidian AI Providers#Installation#BRAT":[43,46],"#Obsidian AI Providers#Installation#BRAT#{1}":[45,46],"#Obsidian AI Providers#Create AI provider":[47,106],"#Obsidian AI Providers#Create AI provider#Ollama":[49,60],"#Obsidian AI Providers#Create AI provider#Ollama#{1}":[51,51],"#Obsidian AI Providers#Create AI provider#Ollama#{2}":[52,52],"#Obsidian AI Providers#Create AI provider#Ollama#{3}":[53,53],"#Obsidian AI Providers#Create AI provider#Ollama#{4}":[54,55],"#Obsidian AI Providers#Create AI provider#Ollama#{5}":[56,57],"#Obsidian AI Providers#Create AI provider#Ollama#{6}":[58,58],"#Obsidian AI Providers#Create AI provider#Ollama#{7}":[59,60],"#Obsidian AI Providers#Create AI provider#OpenAI":[61,67],"#Obsidian AI Providers#Create AI provider#OpenAI#{1}":[63,63],"#Obsidian AI Providers#Create AI provider#OpenAI#{2}":[64,64],"#Obsidian AI Providers#Create AI provider#OpenAI#{3}":[65,65],"#Obsidian AI Providers#Create AI provider#OpenAI#{4}":[66,67],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server":[68,79],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{1}":[70,71],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{2}":[72,72],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{3}":[73,73],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{4}":[74,74],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{5}":[75,75],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{6}":[76,76],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{7}":[77,77],"#Obsidian AI Providers#Create AI provider#OpenAI compatible server#{8}":[78,79],"#Obsidian AI Providers#Create AI provider#OpenRouter":[80,86],"#Obsidian AI Providers#Create AI provider#OpenRouter#{1}":[82,82],"#Obsidian AI Providers#Create AI provider#OpenRouter#{2}":[83,83],"#Obsidian AI Providers#Create AI provider#OpenRouter#{3}":[84,84],"#Obsidian AI Providers#Create AI provider#OpenRouter#{4}":[85,86],"#Obsidian AI Providers#Create AI provider#Google Gemini":[87,93],"#Obsidian AI Providers#Create AI provider#Google Gemini#{1}":[89,89],"#Obsidian AI Providers#Create AI provider#Google Gemini#{2}":[90,90],"#Obsidian AI Providers#Create AI provider#Google Gemini#{3}":[91,91],"#Obsidian AI Providers#Create AI provider#Google Gemini#{4}":[92,93],"#Obsidian AI Providers#Create AI provider#LM Studio":[94,99],"#Obsidian AI Providers#Create AI provider#LM Studio#{1}":[96,96],"#Obsidian AI Providers#Create AI provider#LM Studio#{2}":[97,97],"#Obsidian AI Providers#Create AI provider#LM Studio#{3}":[98,99],"#Obsidian AI Providers#Create AI provider#Groq":[100,106],"#Obsidian AI Providers#Create AI provider#Groq#{1}":[102,102],"#Obsidian AI Providers#Create AI provider#Groq#{2}":[103,103],"#Obsidian AI Providers#Create AI provider#Groq#{3}":[104,104],"#Obsidian AI Providers#Create AI provider#Groq#{4}":[105,106],"#Obsidian AI Providers#For plugin developers":[107,110],"#Obsidian AI Providers#For plugin developers#{1}":[109,110],"#Obsidian AI Providers#Roadmap":[111,124],"#Obsidian AI Providers#Roadmap#{1}":[113,113],"#Obsidian AI Providers#Roadmap#{2}":[114,114],"#Obsidian AI Providers#Roadmap#{3}":[115,115],"#Obsidian AI Providers#Roadmap#{4}":[116,116],"#Obsidian AI Providers#Roadmap#{5}":[117,117],"#Obsidian AI Providers#Roadmap#{6}":[118,118],"#Obsidian AI Providers#Roadmap#{7}":[119,119],"#Obsidian AI Providers#Roadmap#{8}":[120,120],"#Obsidian AI Providers#Roadmap#{9}":[121,121],"#Obsidian AI Providers#Roadmap#{10}":[122,122],"#Obsidian AI Providers#Roadmap#{11}":[123,124],"#Obsidian AI Providers#My other Obsidian plugins":[125,128],"#Obsidian AI Providers#My other Obsidian plugins#{1}":[127,127],"#Obsidian AI Providers#My other Obsidian plugins#{2}":[128,128]},"last_import":{"mtime":1741738763775,"size":4964,"at":1741741841211,"hash":"3bae122518ad759c9a1221dc143f8fd35d19ec3916836111a355f5a4bfb37b0f"}},