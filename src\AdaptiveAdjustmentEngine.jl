# src/AdaptiveAdjustmentEngine.jl

module AdaptiveAdjustmentEngine

using Dates
using Statistics
using Printf
using ..SaliuLottery: Drawing
using ..WonderGridEngine: WonderGridConfig, WonderGridError
using ..DynamicParameterManager: ParameterState, AdaptiveConfig, ParameterMonitor

export AdjustmentEngine, AdjustmentHistory, LearningSystem,
       create_adjustment_engine, adjust_parameters, evaluate_efficiency,
       learn_from_feedback, get_adjustment_recommendations,
       apply_parameter_adjustments, rollback_adjustments

# 調整歷史記錄結構
struct AdjustmentHistory
    timestamp::DateTime
    old_config::WonderGridConfig
    new_config::WonderGridConfig
    adjustment_reason::String
    efficiency_before::Float64
    efficiency_after::Float64
    success_score::Float64
    
    function AdjustmentHistory(old_config, new_config, reason, eff_before, eff_after=0.0)
        success_score = eff_after > eff_before ? (eff_after - eff_before) / eff_before : -1.0
        new(now(), old_config, new_config, reason, eff_before, eff_after, success_score)
    end
end

# 學習系統結構
mutable struct LearningSystem
    successful_adjustments::Vector{AdjustmentHistory}
    failed_adjustments::Vector{AdjustmentHistory}
    learning_rate::Float64
    confidence_threshold::Float64
    pattern_memory::Dict{String, Float64}
    
    function LearningSystem(learning_rate::Float64 = 0.1, confidence_threshold::Float64 = 0.7)
        new(Vector{AdjustmentHistory}(), Vector{AdjustmentHistory}(), 
            learning_rate, confidence_threshold, Dict{String, Float64}())
    end
end

# 自適應調整引擎結構
mutable struct AdjustmentEngine
    monitor::ParameterMonitor
    learning_system::LearningSystem
    adjustment_history::Vector{AdjustmentHistory}
    current_config::WonderGridConfig
    baseline_efficiency::Float64
    adjustment_count::Int
    last_adjustment_time::DateTime
    
    function AdjustmentEngine(
        monitor::ParameterMonitor,
        initial_config::WonderGridConfig,
        baseline_efficiency::Float64 = 0.5
    )
        new(monitor, LearningSystem(), Vector{AdjustmentHistory}(), 
            initial_config, baseline_efficiency, 0, DateTime(1900))
    end
end

"""
創建自適應調整引擎
"""
function create_adjustment_engine(
    monitor::ParameterMonitor,
    initial_config::WonderGridConfig,
    baseline_efficiency::Float64 = 0.5
)::AdjustmentEngine
    
    engine = AdjustmentEngine(monitor, initial_config, baseline_efficiency)
    
    println("🔧 自適應調整引擎已創建")
    println("  - 基準效率: $(round(baseline_efficiency, digits=3))")
    println("  - 學習率: $(engine.learning_system.learning_rate)")
    println("  - 信心閾值: $(engine.learning_system.confidence_threshold)")
    
    return engine
end

"""
調整參數
"""
function adjust_parameters(
    engine::AdjustmentEngine,
    current_data::Vector{Drawing},
    current_efficiency::Float64
)::WonderGridConfig
    
    println("🔄 開始參數調整分析...")
    
    # 檢查是否需要調整
    if !should_adjust_parameters(engine, current_efficiency)
        println("📊 當前參數表現良好，無需調整")
        return engine.current_config
    end
    
    # 分析當前狀態
    state = engine.monitor.current_state
    indicators = state.trend_indicators
    
    # 生成調整建議
    recommendations = generate_adjustment_recommendations(engine, indicators, current_efficiency)
    
    if isempty(recommendations)
        println("⚠️  無法生成有效的調整建議")
        return engine.current_config
    end
    
    # 選擇最佳調整方案
    best_adjustment = select_best_adjustment(engine, recommendations)
    
    # 應用調整
    new_config = apply_adjustment(engine.current_config, best_adjustment)
    
    # 記錄調整歷史
    adjustment_record = AdjustmentHistory(
        engine.current_config,
        new_config,
        best_adjustment["reason"],
        current_efficiency
    )
    
    push!(engine.adjustment_history, adjustment_record)
    engine.adjustment_count += 1
    engine.last_adjustment_time = now()
    engine.current_config = new_config
    
    println("✅ 參數調整完成 (第 $(engine.adjustment_count) 次)")
    println("  - 調整原因: $(best_adjustment["reason"])")
    println("  - 預期改善: $(round(best_adjustment["expected_improvement"], digits=3))")
    
    return new_config
end

"""
評估效率
"""
function evaluate_efficiency(
    engine::AdjustmentEngine,
    test_data::Vector{Drawing},
    config::WonderGridConfig
)::Float64
    
    if isempty(test_data)
        return 0.0
    end
    
    # 簡化的效率評估
    # 實際實現中應該運行完整的 Wonder Grid 策略並計算實際效率
    
    # 基於趨勢指標計算效率分數
    indicators = engine.monitor.current_state.trend_indicators
    
    efficiency_score = 0.0
    weight_sum = 0.0
    
    # 配對頻率效率
    if haskey(indicators, "avg_pair_frequency")
        pair_efficiency = min(1.0, indicators["avg_pair_frequency"] / 10.0)
        efficiency_score += pair_efficiency * 0.3
        weight_sum += 0.3
    end
    
    # 跳躍值穩定性效率
    if haskey(indicators, "skip_volatility")
        skip_efficiency = max(0.0, 1.0 - indicators["skip_volatility"] / 50.0)
        efficiency_score += skip_efficiency * 0.25
        weight_sum += 0.25
    end
    
    # 奇偶平衡效率
    if haskey(indicators, "balance_score")
        balance_efficiency = indicators["balance_score"]
        efficiency_score += balance_efficiency * 0.2
        weight_sum += 0.2
    end
    
    # 趨勢穩定性效率
    if haskey(indicators, "frequency_variance")
        trend_efficiency = max(0.0, 1.0 - indicators["frequency_variance"] / 100.0)
        efficiency_score += trend_efficiency * 0.25
        weight_sum += 0.25
    end
    
    final_efficiency = weight_sum > 0 ? efficiency_score / weight_sum : 0.5
    
    return clamp(final_efficiency, 0.0, 1.0)
end

"""
從回饋中學習
"""
function learn_from_feedback(
    engine::AdjustmentEngine,
    adjustment_id::Int,
    actual_efficiency::Float64
)::Nothing
    
    if adjustment_id > length(engine.adjustment_history)
        @warn "無效的調整ID: $adjustment_id"
        return
    end
    
    # 更新調整記錄
    adjustment = engine.adjustment_history[adjustment_id]
    updated_adjustment = AdjustmentHistory(
        adjustment.old_config,
        adjustment.new_config,
        adjustment.adjustment_reason,
        adjustment.efficiency_before,
        actual_efficiency
    )
    
    engine.adjustment_history[adjustment_id] = updated_adjustment
    
    # 學習系統更新
    learning_system = engine.learning_system
    
    if updated_adjustment.success_score > 0
        push!(learning_system.successful_adjustments, updated_adjustment)
        println("✅ 學習成功調整: $(adjustment.adjustment_reason)")
    else
        push!(learning_system.failed_adjustments, updated_adjustment)
        println("❌ 學習失敗調整: $(adjustment.adjustment_reason)")
    end
    
    # 更新模式記憶
    update_pattern_memory(learning_system, updated_adjustment)
    
    println("🧠 學習系統已更新")
    println("  - 成功調整: $(length(learning_system.successful_adjustments))")
    println("  - 失敗調整: $(length(learning_system.failed_adjustments))")
    
    return nothing
end

"""
獲取調整建議
"""
function get_adjustment_recommendations(
    engine::AdjustmentEngine,
    current_efficiency::Float64
)::Vector{Dict{String, Any}}
    
    recommendations = Vector{Dict{String, Any}}()
    
    state = engine.monitor.current_state
    indicators = state.trend_indicators
    
    # 基於當前指標生成建議
    if haskey(indicators, "avg_pair_frequency") && indicators["avg_pair_frequency"] < 5.0
        push!(recommendations, Dict(
            "parameter" => "top_pair_percentage",
            "adjustment" => "increase",
            "magnitude" => 0.05,
            "reason" => "配對頻率過低，增加頂級配對百分比",
            "confidence" => 0.8
        ))
    end
    
    if haskey(indicators, "skip_volatility") && indicators["skip_volatility"] > 30.0
        push!(recommendations, Dict(
            "parameter" => "analysis_range",
            "adjustment" => "decrease",
            "magnitude" => 20,
            "reason" => "跳躍值波動過大，減少分析範圍",
            "confidence" => 0.7
        ))
    end
    
    if haskey(indicators, "balance_score") && indicators["balance_score"] < 0.6
        push!(recommendations, Dict(
            "parameter" => "trend_weight",
            "adjustment" => "increase",
            "magnitude" => 0.1,
            "reason" => "奇偶平衡不佳，增加趨勢權重",
            "confidence" => 0.6
        ))
    end
    
    # 基於學習系統的建議
    learning_recommendations = get_learning_based_recommendations(engine.learning_system, current_efficiency)
    append!(recommendations, learning_recommendations)
    
    # 按信心度排序
    sort!(recommendations, by = x -> x["confidence"], rev = true)
    
    return recommendations
end

"""
應用參數調整
"""
function apply_parameter_adjustments(
    engine::AdjustmentEngine,
    adjustments::Vector{Dict{String, Any}}
)::WonderGridConfig
    
    new_config = engine.current_config
    
    for adjustment in adjustments
        new_config = apply_single_adjustment(new_config, adjustment)
    end
    
    return new_config
end

"""
回滾調整
"""
function rollback_adjustments(
    engine::AdjustmentEngine,
    steps::Int = 1
)::WonderGridConfig
    
    if steps > length(engine.adjustment_history)
        @warn "回滾步數超過歷史記錄長度"
        steps = length(engine.adjustment_history)
    end
    
    if steps == 0
        return engine.current_config
    end
    
    # 獲取回滾目標配置
    target_adjustment = engine.adjustment_history[end - steps + 1]
    rollback_config = target_adjustment.old_config
    
    # 移除回滾的調整記錄
    engine.adjustment_history = engine.adjustment_history[1:end-steps]
    engine.adjustment_count -= steps
    engine.current_config = rollback_config
    
    println("⏪ 已回滾 $steps 步調整")
    
    return rollback_config
end

# 輔助函數
function should_adjust_parameters(engine::AdjustmentEngine, current_efficiency::Float64)::Bool
    # 效率低於基準
    if current_efficiency < engine.baseline_efficiency * 0.9
        return true
    end
    
    # 效率持續下降
    if length(engine.monitor.current_state.efficiency_history) >= 3
        recent_efficiencies = engine.monitor.current_state.efficiency_history[end-2:end]
        if all(i -> recent_efficiencies[i] > recent_efficiencies[i+1], 1:length(recent_efficiencies)-1)
            return true
        end
    end
    
    # 距離上次調整時間過長且效率未達目標
    time_since_last = now() - engine.last_adjustment_time
    if time_since_last > Hour(48) && current_efficiency < engine.monitor.config.efficiency_target
        return true
    end
    
    return false
end

function generate_adjustment_recommendations(
    engine::AdjustmentEngine,
    indicators::Dict{String, Float64},
    current_efficiency::Float64
)::Vector{Dict{String, Any}}
    
    return get_adjustment_recommendations(engine, current_efficiency)
end

function select_best_adjustment(
    engine::AdjustmentEngine,
    recommendations::Vector{Dict{String, Any}}
)::Dict{String, Any}
    
    if isempty(recommendations)
        return Dict{String, Any}()
    end
    
    # 選擇信心度最高的建議
    best = recommendations[1]
    
    # 添加預期改善估計
    best["expected_improvement"] = best["confidence"] * 0.1
    
    return best
end

function apply_adjustment(config::WonderGridConfig, adjustment::Dict{String, Any})::WonderGridConfig
    return apply_single_adjustment(config, adjustment)
end

function apply_single_adjustment(config::WonderGridConfig, adjustment::Dict{String, Any})::WonderGridConfig
    param = adjustment["parameter"]
    adj_type = adjustment["adjustment"]
    magnitude = adjustment["magnitude"]
    
    if param == "top_pair_percentage"
        new_value = adj_type == "increase" ? 
                   config.top_pair_percentage + magnitude : 
                   config.top_pair_percentage - magnitude
        new_value = clamp(new_value, 0.1, 0.5)
        
        return WonderGridConfig(
            analysis_range = config.analysis_range,
            top_pair_percentage = new_value,
            key_number_strategy = config.key_number_strategy,
            combination_limit = config.combination_limit,
            enable_purge = config.enable_purge,
            enable_lie = config.enable_lie,
            game_type = config.game_type,
            pair_analysis_depth = config.pair_analysis_depth,
            trend_weight = config.trend_weight
        )
    elseif param == "analysis_range"
        new_value = adj_type == "increase" ? 
                   config.analysis_range + Int(magnitude) : 
                   config.analysis_range - Int(magnitude)
        new_value = clamp(new_value, 50, 300)
        
        return WonderGridConfig(
            analysis_range = new_value,
            top_pair_percentage = config.top_pair_percentage,
            key_number_strategy = config.key_number_strategy,
            combination_limit = config.combination_limit,
            enable_purge = config.enable_purge,
            enable_lie = config.enable_lie,
            game_type = config.game_type,
            pair_analysis_depth = config.pair_analysis_depth,
            trend_weight = config.trend_weight
        )
    elseif param == "trend_weight"
        new_value = adj_type == "increase" ? 
                   config.trend_weight + magnitude : 
                   config.trend_weight - magnitude
        new_value = clamp(new_value, 0.1, 0.8)
        
        return WonderGridConfig(
            analysis_range = config.analysis_range,
            top_pair_percentage = config.top_pair_percentage,
            key_number_strategy = config.key_number_strategy,
            combination_limit = config.combination_limit,
            enable_purge = config.enable_purge,
            enable_lie = config.enable_lie,
            game_type = config.game_type,
            pair_analysis_depth = config.pair_analysis_depth,
            trend_weight = new_value
        )
    end
    
    return config  # 如果參數不匹配，返回原配置
end

function update_pattern_memory(learning_system::LearningSystem, adjustment::AdjustmentHistory)::Nothing
    pattern_key = "$(adjustment.adjustment_reason)_$(adjustment.success_score > 0 ? "success" : "failure")"
    
    current_score = get(learning_system.pattern_memory, pattern_key, 0.0)
    learning_rate = learning_system.learning_rate
    
    if adjustment.success_score > 0
        new_score = current_score + learning_rate * (1.0 - current_score)
    else
        new_score = current_score - learning_rate * current_score
    end
    
    learning_system.pattern_memory[pattern_key] = clamp(new_score, 0.0, 1.0)
    
    return nothing
end

function get_learning_based_recommendations(
    learning_system::LearningSystem,
    current_efficiency::Float64
)::Vector{Dict{String, Any}}
    
    recommendations = Vector{Dict{String, Any}}()
    
    # 基於成功模式生成建議
    for adjustment in learning_system.successful_adjustments
        if adjustment.efficiency_before <= current_efficiency <= adjustment.efficiency_after
            pattern_key = "$(adjustment.adjustment_reason)_success"
            confidence = get(learning_system.pattern_memory, pattern_key, 0.5)
            
            if confidence >= learning_system.confidence_threshold
                push!(recommendations, Dict(
                    "parameter" => "learned_pattern",
                    "adjustment" => "apply_successful_pattern",
                    "magnitude" => 0.1,
                    "reason" => "基於學習的成功模式: $(adjustment.adjustment_reason)",
                    "confidence" => confidence
                ))
            end
        end
    end
    
    return recommendations
end

end # module AdaptiveAdjustmentEngine
