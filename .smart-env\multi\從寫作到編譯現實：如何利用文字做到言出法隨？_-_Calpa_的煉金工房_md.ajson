"smart_sources:從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md": {"path":"從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md","last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10"}}},"last_read":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10","at":1750403578872},"class_name":"SmartSource","last_import":{"mtime":1750381624892,"size":24160,"at":1750384454492,"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10"},"blocks":{"#":[1,326]},"outlinks":[],"key":"從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md"},
"smart_blocks:從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10","at":1750403578873},"key":"從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md#","lines":[1,326],"size":9873,"outlinks":[],"class_name":"SmartBlock"},"smart_blocks:從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10","at":1750403578873},"key":"從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md#","lines":[1,326],"size":9873,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"95894ae0c129f2cc7f42df9b611bda65efe1c01e57b8e6116628665def2d3e10","at":1750403578873},"key":"從寫作到編譯現實：如何利用文字做到言出法隨？ - Calpa 的煉金工房.md#","lines":[1,326],"size":9873,"outlinks":[],"class_name":"SmartBlock"},
