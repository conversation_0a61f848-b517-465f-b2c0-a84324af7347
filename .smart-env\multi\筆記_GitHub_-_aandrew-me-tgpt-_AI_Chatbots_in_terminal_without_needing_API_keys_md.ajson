"smart_sources:筆記/GitHub - aandrew-me-tgpt- AI Cha<PERSON>bots in terminal without needing API keys.md": {"path":"筆記/GitHub - aandrew-me-tgpt- AI Chatbots in terminal without needing API keys.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03933111,0.00806923,-0.01411149,-0.04493666,0.0093587,-0.00255406,-0.05301066,0.04628458,-0.01193988,-0.01935879,-0.02205419,-0.06765424,0.01630008,0.08492853,0.00548579,0.02228058,0.00626468,-0.02214306,-0.00655783,-0.04684227,0.05869835,-0.07173591,0.0296737,-0.06441185,-0.0482817,0.02588417,-0.05019307,-0.01694707,-0.01204206,-0.21988557,0.02394916,0.01308032,0.01630717,-0.01971495,-0.03882442,0.02529156,-0.05286317,0.01531551,-0.08286687,0.01902773,0.01385482,-0.00541161,-0.00636451,-0.04766281,0.0383778,-0.03879273,-0.03516972,-0.01154863,-0.03828977,-0.01247037,0.02822844,0.03379743,-0.00924288,-0.01956665,0.01021582,0.00164576,0.00109851,0.07200918,0.01025429,-0.03405785,0.0192187,0.06010149,-0.19101958,0.10551085,0.01120789,0.04196007,-0.02782411,0.03418477,0.09538902,0.00303958,-0.01089818,0.03207125,0.04218076,0.01674704,0.00195626,-0.00297491,-0.0284971,-0.03596397,0.02269867,-0.05265296,-0.01568221,0.02184873,-0.03627167,0.00343711,-0.01676498,-0.10468838,0.01279113,0.00192444,-0.004618,0.00769533,-0.03840242,-0.06123176,0.03539673,0.02046807,-0.03722778,0.0296991,0.05172575,0.01874363,-0.12749848,0.1146569,-0.0431659,-0.03086586,-0.05308133,-0.00492878,0.00864477,-0.01807981,0.00434413,-0.02227977,-0.06795871,0.07331301,-0.01709736,0.02889249,0.03568489,0.00108681,-0.02862145,0.00479369,0.02245382,0.03705709,-0.00321777,0.02749382,-0.02525328,-0.00250908,0.04674913,-0.01742432,0.02131901,0.01228797,0.01913713,0.06388942,0.0037123,0.06141313,0.066625,0.05128877,-0.07245777,0.00476515,0.01052569,-0.01350018,0.02199214,-0.00182951,0.00554058,-0.0706962,-0.0297659,-0.05073605,0.00813237,-0.05776409,0.02292556,0.06095806,-0.02589394,0.0498421,-0.02283046,-0.08639956,0.02098103,0.0357925,0.00723523,0.02991726,0.02004257,-0.0098032,0.0595936,0.09502491,-0.06659691,0.02828779,-0.02077057,-0.05678693,-0.02648808,0.04543633,0.03925126,-0.119027,-0.06355634,0.00847823,0.00996894,-0.0304251,0.02776715,0.0131922,-0.00100212,-0.01933893,0.05419312,-0.03490947,-0.06758565,-0.01352597,0.00397912,0.0718947,-0.02592988,-0.05439435,-0.01474803,0.02683229,0.0006922,-0.04935353,0.07763114,-0.06783522,0.0186934,-0.0349911,-0.04698186,-0.004444,-0.0331624,0.01546049,-0.02411893,-0.01056266,-0.01791446,-0.01819296,0.04477237,-0.01525188,0.04617613,0.00033372,-0.01469572,0.04983546,-0.03011869,0.02346061,0.02426954,-0.0191803,0.06157521,0.07778419,-0.03864466,-0.0076203,0.07599487,-0.01431948,-0.08001547,-0.02503082,0.00909668,0.01363095,-0.00729371,-0.00087606,0.05010432,0.04601542,-0.02715177,-0.23343471,-0.06257398,0.01389411,-0.00962416,0.00233708,-0.14831249,0.0297623,-0.03239144,0.03734672,0.06029871,0.13963677,0.02246856,-0.01666768,0.03719493,-0.02791743,0.00757313,-0.05326557,0.06877901,-0.05546353,-0.00052188,-0.01814514,0.03255533,-0.01902195,-0.09475548,0.0456171,-0.00853407,0.15797409,0.02725659,0.03240125,0.00437305,0.02386182,0.05784748,0.0032767,-0.13503627,0.04786093,0.05859254,-0.01382378,0.07292774,0.06813513,0.02525443,-0.00785937,0.06501218,-0.01326829,-0.11419411,-0.00586571,-0.05633341,-0.00400924,-0.04360086,-0.05956972,-0.0005992,0.03984593,0.00100291,0.00289114,0.00574858,0.01267071,-0.06502581,-0.048855,-0.02050429,-0.00577418,0.06082178,-0.03277364,0.01645166,-0.00553439,-0.02782485,0.04652866,0.00340171,0.01565783,0.00063078,0.04778814,-0.03941531,0.01925642,0.16673283,0.00440191,0.14231698,-0.00448138,-0.00808701,0.0472571,-0.02464639,-0.047606,0.00116156,-0.00930901,0.0702784,0.04965577,0.03703875,-0.03181873,0.06385066,0.02988963,0.02195721,0.08459987,0.01051532,0.02942252,0.05245454,-0.00251947,-0.05126454,0.07356075,0.03043077,-0.25862876,0.07588729,0.02771177,0.00206336,-0.06207051,0.05620308,0.0396848,-0.02449482,-0.09689605,0.02510589,-0.11097047,0.05349386,0.02442325,-0.04696071,0.00939249,0.0070355,0.04664772,-0.06049811,0.03431953,-0.06281693,0.02752949,0.02684454,0.18528096,-0.00694113,0.03317328,0.00607425,-0.0149654,-0.00492256,0.06224943,-0.04643762,-0.02592101,-0.00003708,0.08375793,-0.01093633,0.07377108,0.02956771,-0.01355877,-0.00706226,-0.01815771,0.0243451,-0.05349259,0.05050116,0.02035781,0.0151363,0.0288059,-0.01124005,-0.04227429,0.00080137,-0.01420518,0.04430512,-0.03118589,-0.01210394,0.00827309,0.03000713,0.01543797,0.07372612,0.03389859,-0.00279774,-0.03005257,0.01547512,0.02622984,-0.05672868,0.01785783,0.04594394,-0.04896233],"last_embed":{"hash":"7fec7f065a1c25de9f4b5b6cc4067979afb10fbab7378ae21e769e4b18ccf355","tokens":508}}},"last_read":{"hash":"7fec7f065a1c25de9f4b5b6cc4067979afb10fbab7378ae21e769e4b18ccf355","at":1745995234885},"class_name":"SmartSource2","outlinks":[{"title":"OpenGPTs","target":"https://opengpts-example-vz4y4ooboq-uc.a.run.app/","line":9},{"title":"KoboldAI","target":"https://koboldai-koboldcpp-tiefighter.hf.space/","line":10},{"title":"Phind","target":"https://www.phind.com/agent","line":11},{"title":"Blackbox AI","target":"https://www.blackbox.ai/","line":13},{"title":"OpenAI","target":"https://platform.openai.com/docs/guides/text-generation/chat-completions-api","line":14},{"title":"Groq","target":"https://groq.com/","line":15},{"title":"Ollama","target":"https://www.ollama.com/","line":16},{"title":"![demo","target":"https://user-images.githubusercontent.com/66430340/233759296-c4cf8cf2-0cab-48aa-9e84-40765b823282.gif","line":87},{"title":"![demo","target":"https://user-images.githubusercontent.com/66430340/233759296-c4cf8cf2-0cab-48aa-9e84-40765b823282.gif","line":87},{"title":"AUR package","target":"https://aur.archlinux.org/packages/tgpt-bin","line":107},{"title":"port","target":"https://www.freshports.org/www/tgpt","line":123},{"title":"add the Go install directory to your system's shell path","target":"https://go.dev/doc/tutorial/compile-install","line":140},{"title":"Scoop","target":"https://scoop.sh/","line":150},{"title":"ai","target":"/topics/ai \"Topic: ai\"","line":212},{"title":"chatbot","target":"/topics/chatbot \"Topic: chatbot\"","line":212},{"title":"chatgpt","target":"/topics/chatgpt \"Topic: chatgpt\"","line":212},{"title":"cli","target":"/topics/cli \"Topic: cli\"","line":212},{"title":"go","target":"/topics/go \"Topic: go\"","line":212},{"title":"golang","target":"/topics/golang \"Topic: golang\"","line":212},{"title":"gpt3","target":"/topics/gpt3 \"Topic: gpt3\"","line":212},{"title":"linux","target":"/topics/linux \"Topic: linux\"","line":212},{"title":"llama","target":"/topics/llama \"Topic: llama\"","line":212},{"title":"macos","target":"/topics/macos \"Topic: macos\"","line":212},{"title":"mixtral","target":"/topics/mixtral \"Topic: mixtral\"","line":212},{"title":"terminal","target":"/topics/terminal \"Topic: terminal\"","line":212},{"title":"windows","target":"/topics/windows \"Topic: windows\"","line":212},{"title":"Readme","target":"#readme-ov-file","line":216},{"title":"GPL-3.0 license","target":"#GPL-3.0-1-ov-file","line":220},{"title":"Activity","target":"/aandrew-me/tgpt/activity","line":222},{"title":"**1.7k** stars","target":"/aandrew-me/tgpt/stargazers","line":226},{"title":"**33** watching","target":"/aandrew-me/tgpt/watchers","line":230},{"title":"**141** forks","target":"/aandrew-me/tgpt/forks","line":234},{"title":"Report repository","target":"/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Faandrew-me%2Ftgpt&report=aandrew-me+%28user%29","line":236},{"title":"Releases 69","target":"/aandrew-me/tgpt/releases","line":238},{"title":"\n\ntgpt 2.8.0 Latest\n\nJun 9, 2024\n\n\n\n","target":"/aandrew-me/tgpt/releases/tag/v2.8.0","line":240},{"title":"+ 68 releases","target":"/aandrew-me/tgpt/releases","line":250},{"title":"Contributors 23","target":"/aandrew-me/tgpt/graphs/contributors","line":252},{"title":"![@aandrew-me","target":"https://avatars.githubusercontent.com/u/66430340?s=64&v=4","line":254},{"title":"![@1RaY-1","target":"https://avatars.githubusercontent.com/u/78962948?s=64&v=4","line":255},{"title":"![@orangbilangciperipampam","target":"https://avatars.githubusercontent.com/u/135487348?s=64&v=4","line":256},{"title":"![@grepsuzette","target":"https://avatars.githubusercontent.com/u/350354?s=64&v=4","line":257},{"title":"![@johnd0e","target":"https://avatars.githubusercontent.com/u/1838643?s=64&v=4","line":258},{"title":"![@guhitb","target":"https://avatars.githubusercontent.com/u/147658676?s=64&v=4","line":259},{"title":"![@shmilee","target":"https://avatars.githubusercontent.com/u/2681740?s=64&v=4","line":260},{"title":"![@schnz","target":"https://avatars.githubusercontent.com/u/3457747?s=64&v=4","line":261},{"title":"![@siuyutpang","target":"https://avatars.githubusercontent.com/u/53570269?s=64&v=4","line":262},{"title":"![@Axorax","target":"https://avatars.githubusercontent.com/u/78349410?s=64&v=4","line":263},{"title":"![@wildy368","target":"https://avatars.githubusercontent.com/u/153343629?s=64&v=4","line":264},{"title":"![@nxjosephofficial","target":"https://avatars.githubusercontent.com/u/153529729?s=64&v=4","line":265},{"title":"![@graves501","target":"https://avatars.githubusercontent.com/u/11211125?s=64&v=4","line":266},{"title":"![@eltociear","target":"https://avatars.githubusercontent.com/u/22633385?s=64&v=4","line":267},{"title":"+ 9 contributors","target":"/aandrew-me/tgpt/graphs/contributors","line":269},{"title":"Go 84.9%","target":"/aandrew-me/tgpt/search?l=go","line":273},{"title":"JavaScript 6.7%","target":"/aandrew-me/tgpt/search?l=javascript","line":274},{"title":"Shell 5.2%","target":"/aandrew-me/tgpt/search?l=shell","line":275},{"title":"PowerShell 3.2%","target":"/aandrew-me/tgpt/search?l=powershell","line":276},{"title":"Terms","target":"https://docs.github.com/site-policy/github-terms/github-terms-of-service","line":284},{"title":"Privacy","target":"https://docs.github.com/site-policy/privacy-policies/github-privacy-statement","line":285},{"title":"Security","target":"https://github.com/security","line":286},{"title":"Status","target":"https://www.githubstatus.com/","line":287},{"title":"Docs","target":"https://docs.github.com/","line":288},{"title":"Contact","target":"https://support.github.com?tags=dotcom-footer","line":289}],"blocks":{"#aandrew-me/tgpt":[1,2],"#Terminal GPT (tgpt) 🚀":[3,295],"#Terminal GPT (tgpt) 🚀##Currently available providers:":[6,19],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{1}":[9,9],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{2}":[10,10],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{3}":[11,12],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{4}":[13,13],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{5}":[14,14],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{6}":[15,15],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{7}":[16,17],"#Terminal GPT (tgpt) 🚀##Currently available providers:#{8}":[18,19],"#Terminal GPT (tgpt) 🚀#Usage":[20,90],"#Terminal GPT (tgpt) 🚀#Usage#{1}":[22,90],"#Terminal GPT (tgpt) 🚀#Installation ⏬":[91,156],"#Terminal GPT (tgpt) 🚀#Installation ⏬#{1}":[93,94],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Download for GNU/Linux 🐧 or MacOS 🍎":[95,118],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Download for GNU/Linux 🐧 or MacOS 🍎#{1}":[97,118],"#Terminal GPT (tgpt) 🚀#Installation ⏬#FreeBSD 😈":[119,135],"#Terminal GPT (tgpt) 🚀#Installation ⏬#FreeBSD 😈#{1}":[121,135],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Install with Go":[136,145],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Install with Go#{1}":[138,145],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Windows 🪟":[146,156],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Windows 🪟#{1}":[148,149],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Windows 🪟#{2}":[150,156],"#Terminal GPT (tgpt) 🚀#Installation ⏬#Windows 🪟#{3}":[152,156],"#Terminal GPT (tgpt) 🚀#Updating ⬆️":[157,193],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#{1}":[159,168],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#Proxy":[169,187],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#Proxy#{1}":[171,174],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#Proxy#{2}":[175,175],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#Proxy#{3}":[176,176],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#Proxy#{4}":[177,177],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#Proxy#{5}":[178,179],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#Proxy#{6}":[180,187],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#From Release":[188,193],"#Terminal GPT (tgpt) 🚀#Updating ⬆️#From Release#{1}":[190,193],"#Terminal GPT (tgpt) 🚀#Uninstalling":[194,205],"#Terminal GPT (tgpt) 🚀#Uninstalling#{1}":[196,205],"#Terminal GPT (tgpt) 🚀#About":[206,237],"#Terminal GPT (tgpt) 🚀#About#{1}":[208,209],"#Terminal GPT (tgpt) 🚀#About#Topics":[210,213],"#Terminal GPT (tgpt) 🚀#About#Topics#{1}":[212,213],"#Terminal GPT (tgpt) 🚀#About#Resources":[214,217],"#Terminal GPT (tgpt) 🚀#About#Resources#{1}":[216,217],"#Terminal GPT (tgpt) 🚀#About#License":[218,223],"#Terminal GPT (tgpt) 🚀#About#License#{1}":[220,223],"#Terminal GPT (tgpt) 🚀#About#Stars":[224,227],"#Terminal GPT (tgpt) 🚀#About#Stars#{1}":[226,227],"#Terminal GPT (tgpt) 🚀#About#Watchers":[228,231],"#Terminal GPT (tgpt) 🚀#About#Watchers#{1}":[230,231],"#Terminal GPT (tgpt) 🚀#About#Forks":[232,237],"#Terminal GPT (tgpt) 🚀#About#Forks#{1}":[234,237],"#Terminal GPT (tgpt) 🚀#[Releases 69](/aandrew-me/tgpt/releases)":[238,251],"#Terminal GPT (tgpt) 🚀#[Releases 69](/aandrew-me/tgpt/releases)#{1}":[240,251],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)":[252,270],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{1}":[254,254],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{2}":[255,255],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{3}":[256,256],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{4}":[257,257],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{5}":[258,258],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{6}":[259,259],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{7}":[260,260],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{8}":[261,261],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{9}":[262,262],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{10}":[263,263],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{11}":[264,264],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{12}":[265,265],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{13}":[266,266],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{14}":[267,268],"#Terminal GPT (tgpt) 🚀#[Contributors 23](/aandrew-me/tgpt/graphs/contributors)#{15}":[269,270],"#Terminal GPT (tgpt) 🚀#Languages":[271,277],"#Terminal GPT (tgpt) 🚀#Languages#{1}":[273,273],"#Terminal GPT (tgpt) 🚀#Languages#{2}":[274,274],"#Terminal GPT (tgpt) 🚀#Languages#{3}":[275,275],"#Terminal GPT (tgpt) 🚀#Languages#{4}":[276,277],"#Terminal GPT (tgpt) 🚀#Footer":[278,295],"#Terminal GPT (tgpt) 🚀#Footer#{1}":[280,281],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation":[282,295],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{1}":[284,284],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{2}":[285,285],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{3}":[286,286],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{4}":[287,287],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{5}":[288,288],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{6}":[289,289],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{7}":[290,290],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{8}":[291,292],"#Terminal GPT (tgpt) 🚀#Footer#Footer navigation#{9}":[293,295]},"last_import":{"mtime":1722298867415,"size":10364,"at":1740449882864,"hash":"7fec7f065a1c25de9f4b5b6cc4067979afb10fbab7378ae21e769e4b18ccf355"},"key":"筆記/GitHub - aandrew-me-tgpt- AI Chatbots in terminal without needing API keys.md"},