
"smart_sources:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md": {"path":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1li2f24","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753266086127,"size":20330,"at":1753423416500,"hash":"1li2f24"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD":[8,325],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#{1}":[10,15],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#Winning Reports for Lottery _Wonder-grid_: GridCheck Lotto Software ~":[16,18],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#Winning Reports for Lottery _Wonder-grid_: GridCheck Lotto Software ~#{1}":[17,18],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By <PERSON>, ★ _Founder of Lotto Mathematics_":[19,296],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By Ion Saliu, ★ _Founder of Lotto Mathematics_#{1}":[21,296],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)":[297,325],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{1}":[299,299],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}":[300,300],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{3}":[301,301],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{4}":[302,302],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{5}":[303,303],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{6}":[304,304],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{7}":[305,305],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{8}":[306,306],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{9}":[307,307],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{10}":[308,308],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{11}":[309,309],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{12}":[310,310],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{13}":[311,312],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{14}":[313,313],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{15}":[314,314],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{16}":[315,315],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{17}":[316,317],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{18}":[318,325]},"outlinks":[{"title":"![Download Best Software: Lottery, Lotto, Powerball, Mega Millions, Euromillions, Pick Digit, Keno..","target":"https://saliu.com/images/lottery-software-systems.gif","line":14},{"title":"Lotto Wonder Grid beats random expectation by over 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":21},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":32},{"title":"FFG Median, Filtering, Probability, Jackpot","target":"https://saliu.com/bbs/messages/923.html","line":227},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":297},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":299},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":301},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":303},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":305},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":306},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":307},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":308},{"title":"Lottery Utility Software","target":"https://saliu.com/lottery-utility.html","line":309},{"title":"Practical lottery and lotto filtering in software","target":"https://saliu.com/filters.html","line":310},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":311},{"title":"Updates to the Bright lotto, lottery, and horseracing software","target":"https://saliu.com/forum/software-updates.html","line":313},{"title":"_Markov Chains_ Lottery Software, Lotto, Program, Algorithms","target":"https://saliu.com/Markov_Chains.html","line":315},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":316},{"title":"Lottery Wonder Grid reports a strategy to beat random play.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":318},{"title":"Lotto systems of pairs beat random expectation consistently by 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":320},{"title":"Forums","target":"https://forums.saliu.com/","line":322},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":322},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":322},{"title":"Contents","target":"https://saliu.com/content/index.html","line":322},{"title":"Home","target":"https://saliu.com/index.htm","line":322},{"title":"Software","target":"https://saliu.com/infodown.html","line":322},{"title":"Search","target":"https://saliu.com/Search.htm","line":322},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":322},{"title":"The wonder grid lotto strategy beats lottery random picks by better than 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":324}],"metadata":{"created":"2025-07-23T18:21:25 (UTC +08:00)","tags":["winning","strategy","system","wonder grid","wonder-grid","lotto","software","lottery","systems","method","numbers","combinations","standard deviation","number","win"],"source":"https://saliu.com/bbs/messages/9.html","author":null}},
"smart_sources:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md": {"path":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09033354,-0.00863861,-0.03238669,-0.00082645,-0.00907174,0.03434262,-0.00545374,-0.04644651,0.03070282,-0.00877476,0.01405201,-0.01218522,0.06409194,0.00112099,-0.02307222,-0.00294031,-0.01692971,-0.03399608,-0.0165444,0.0139808,0.06078478,-0.03427558,-0.0157926,-0.07862104,0.03683077,0.00658296,-0.04825934,-0.05851481,-0.06555492,-0.22256947,0.01431569,-0.03031493,0.02179023,-0.08903567,-0.07083239,-0.04538037,-0.04814914,0.03936101,-0.0838836,0.02565204,0.03287564,-0.00814789,-0.03026623,-0.0368518,-0.01362593,-0.0423664,0.03568547,-0.03496082,0.03856959,0.02505942,-0.09499042,-0.00168631,-0.00627253,0.02936979,0.0545868,0.05503344,0.02610472,0.07496735,-0.00514801,0.06702022,0.05636863,0.06843927,-0.16676341,0.0269291,0.02201923,0.02062581,0.00888167,-0.02330489,-0.02062948,0.04050153,0.02999892,0.04019036,0.00098885,0.04206631,0.01181191,0.00451167,-0.0482222,-0.05505159,-0.07368078,0.02517598,-0.05886715,-0.00344706,-0.003373,0.03194048,0.00135805,0.04144775,0.06041265,0.04878949,0.06111552,-0.06144886,0.0001529,0.04576118,0.01315638,0.04219139,0.00529746,0.03399002,0.04027083,0.00070862,-0.03115724,0.12381171,0.01231812,-0.02529529,-0.01236341,0.03216339,0.04275977,-0.03331862,0.0305584,-0.01310623,-0.03305392,0.00369079,0.04676678,-0.01064949,0.05641951,-0.02553553,-0.05902729,0.00895399,0.02869255,-0.00743366,0.04023669,0.0081035,-0.05825365,0.05681291,0.02498823,-0.04676078,0.03523008,0.03070643,0.02428346,0.05226886,0.02451899,0.01193472,0.02010066,-0.02726356,-0.14889589,-0.05202278,-0.0252639,-0.01913138,-0.00340939,0.01386391,-0.01107592,-0.02458196,-0.01978023,0.00779024,0.0670763,-0.12081698,-0.05414892,0.02555444,-0.02937888,-0.02466404,0.00893147,-0.00142245,0.03660168,0.03258672,-0.03105914,-0.05645595,0.00397623,-0.0039375,0.1398849,0.08361336,-0.03195146,0.02797482,-0.01426494,-0.0058352,-0.00413259,0.09906209,0.01964141,-0.11626115,0.00974313,0.03507391,-0.03197803,-0.09419239,-0.02886086,0.01975446,-0.06259141,-0.00065047,0.03382396,-0.0447673,-0.09585097,-0.04243603,-0.00050243,-0.00946228,-0.01794599,0.01281938,-0.00127012,-0.00766088,-0.0322472,-0.10299487,0.01037144,-0.0070099,0.0011787,0.01999166,-0.00272633,-0.00858033,0.01788088,0.11513603,-0.02688508,-0.04541919,-0.04913292,-0.07681157,0.09174851,-0.00389679,-0.05854152,-0.01668345,0.01887603,-0.0122948,-0.01498183,0.03961879,0.00057208,-0.03631367,0.10345886,0.05313595,-0.05392588,0.01683563,0.04905944,0.05767081,-0.07814433,0.06624866,-0.00274667,-0.00056257,-0.0001166,0.00932134,0.00044184,0.03025489,-0.08440994,-0.20637679,-0.05095479,-0.06730607,-0.00309595,0.01778403,-0.04361603,0.06424636,-0.03990735,0.07120546,0.09345719,0.07459573,-0.03373847,0.00562007,0.02493678,-0.01757139,-0.00166087,-0.06922457,-0.0387631,-0.04597791,0.04205492,0.02726643,0.06651238,0.01645659,-0.07217948,0.0436511,-0.06165619,0.15504882,0.06030376,0.01945383,0.00541415,0.07208361,0.00739737,0.02965632,-0.07083081,0.03866581,0.07290536,0.00585538,0.00413599,-0.09049235,0.00159413,-0.1224046,0.02614893,-0.00779604,-0.09429004,-0.01776081,0.00252749,-0.00684395,-0.00569935,-0.02670962,0.01120131,0.06963311,-0.04308008,-0.01463794,0.00657762,0.08608011,0.00883392,-0.06928364,0.00596405,0.00318542,-0.000256,-0.00257993,-0.03286696,0.0171909,-0.03077222,-0.00241697,0.03118989,0.03822751,-0.00523609,0.01467352,-0.00478408,0.02173266,0.08135497,-0.01378154,0.05479384,0.03675546,0.04368409,0.06629816,-0.06424712,0.01845498,-0.00713502,0.01665444,0.02326352,0.0566101,0.055267,0.06824844,-0.00149308,0.02384621,0.06685718,0.01625112,-0.05525244,-0.0066469,0.03146432,-0.02312203,-0.01312725,0.07304142,0.03489726,-0.24309002,-0.00011596,-0.02547459,0.04314211,-0.01065122,0.001516,0.00062031,-0.03203709,-0.00975556,-0.01696386,0.06002681,0.01641055,-0.02405643,-0.07207314,-0.00421876,0.00702397,-0.0179461,-0.03998656,0.08382152,-0.03524076,0.05277355,0.036547,0.2371688,0.02298092,-0.00743901,0.01097669,0.00521466,0.03189583,0.01862027,0.03806285,-0.02259623,0.03872331,0.08381707,-0.01556296,0.02189399,0.03586565,-0.01421604,-0.01929888,0.00057527,-0.01532364,-0.05244758,0.02149948,-0.0179796,0.01158344,0.0819013,0.01824186,-0.01045475,-0.08003233,0.02438492,0.07532963,-0.05856182,-0.0617891,-0.07345796,-0.03467599,0.01825353,0.0372887,0.05823411,-0.03690964,0.01147615,-0.00190121,0.02212167,-0.02482483,0.08121473,-0.01621107,-0.00764748],"last_embed":{"hash":"1li2f24","tokens":499}}},"last_read":{"hash":"1li2f24","at":1753423549779},"class_name":"SmartSource","last_import":{"mtime":1753266086127,"size":20330,"at":1753423416500,"hash":"1li2f24"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD":[8,325],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#{1}":[10,15],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#Winning Reports for Lottery _Wonder-grid_: GridCheck Lotto Software ~":[16,18],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#Winning Reports for Lottery _Wonder-grid_: GridCheck Lotto Software ~#{1}":[17,18],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By Ion Saliu, ★ _Founder of Lotto Mathematics_":[19,296],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By Ion Saliu, ★ _Founder of Lotto Mathematics_#{1}":[21,296],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)":[297,325],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{1}":[299,299],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}":[300,300],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{3}":[301,301],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{4}":[302,302],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{5}":[303,303],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{6}":[304,304],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{7}":[305,305],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{8}":[306,306],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{9}":[307,307],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{10}":[308,308],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{11}":[309,309],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{12}":[310,310],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{13}":[311,312],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{14}":[313,313],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{15}":[314,314],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{16}":[315,315],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{17}":[316,317],"#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{18}":[318,325]},"outlinks":[{"title":"![Download Best Software: Lottery, Lotto, Powerball, Mega Millions, Euromillions, Pick Digit, Keno..","target":"https://saliu.com/images/lottery-software-systems.gif","line":14},{"title":"Lotto Wonder Grid beats random expectation by over 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":21},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":32},{"title":"FFG Median, Filtering, Probability, Jackpot","target":"https://saliu.com/bbs/messages/923.html","line":227},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":297},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":299},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":301},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":303},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":305},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":306},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":307},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":308},{"title":"Lottery Utility Software","target":"https://saliu.com/lottery-utility.html","line":309},{"title":"Practical lottery and lotto filtering in software","target":"https://saliu.com/filters.html","line":310},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":311},{"title":"Updates to the Bright lotto, lottery, and horseracing software","target":"https://saliu.com/forum/software-updates.html","line":313},{"title":"_Markov Chains_ Lottery Software, Lotto, Program, Algorithms","target":"https://saliu.com/Markov_Chains.html","line":315},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":316},{"title":"Lottery Wonder Grid reports a strategy to beat random play.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":318},{"title":"Lotto systems of pairs beat random expectation consistently by 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":320},{"title":"Forums","target":"https://forums.saliu.com/","line":322},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":322},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":322},{"title":"Contents","target":"https://saliu.com/content/index.html","line":322},{"title":"Home","target":"https://saliu.com/index.htm","line":322},{"title":"Software","target":"https://saliu.com/infodown.html","line":322},{"title":"Search","target":"https://saliu.com/Search.htm","line":322},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":322},{"title":"The wonder grid lotto strategy beats lottery random picks by better than 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":324}],"metadata":{"created":"2025-07-23T18:21:25 (UTC +08:00)","tags":["winning","strategy","system","wonder grid","wonder-grid","lotto","software","lottery","systems","method","numbers","combinations","standard deviation","number","win"],"source":"https://saliu.com/bbs/messages/9.html","author":null}},"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10057941,-0.0230575,-0.02865437,-0.01997333,-0.00287716,0.03628193,-0.00588124,-0.00480201,0.05004894,0.00533766,0.00126552,-0.02034277,0.05403966,0.01060579,-0.01417581,0.01374616,-0.01273218,-0.02420606,-0.01352332,0.01766734,0.05544319,-0.03034095,-0.03129502,-0.10004965,0.04816706,-0.01630988,-0.04714382,-0.06722525,-0.05636017,-0.1740389,0.01320379,-0.02063602,0.01977202,-0.09826219,-0.05443785,-0.05350207,-0.04791702,0.06287006,-0.06645311,0.03396491,0.01578063,0.00847277,-0.04653178,-0.04215286,-0.03155868,-0.03442685,0.01045346,-0.01129083,0.03400205,0.02949544,-0.10297207,0.0144411,-0.00207896,0.03839609,0.06634606,0.04536064,0.02762811,0.06883542,-0.00516789,0.04860919,0.05041745,0.03074759,-0.18360217,0.03917143,0.01982807,0.03008969,0.00968546,-0.01379871,-0.01581134,0.04071948,0.03085393,0.0599789,0.00839908,0.03923811,0.00650132,0.02840285,-0.05213262,-0.06099635,-0.06468488,0.01098543,-0.04206542,-0.02263884,-0.02724171,-0.00112753,-0.00238192,0.02139082,0.05150791,0.08683348,0.0596404,-0.03440677,-0.00060168,0.07428706,0.01950998,0.04734709,-0.01686518,0.02649143,0.04757372,-0.00552662,-0.02042914,0.1349681,-0.02701565,-0.0126631,-0.01589036,0.02011693,0.03581302,-0.00753909,0.00815665,-0.01438985,-0.04302602,0.02315376,0.04717641,0.00343935,0.07242846,-0.06363077,-0.04067887,-0.00358462,0.03510629,0.00632378,0.02264973,0.00061812,-0.07103834,0.0368715,0.01601574,-0.04957741,0.02681095,0.04063427,0.03506111,0.05966664,0.00789703,0.01380436,0.01653627,-0.02024605,-0.14257646,-0.02862462,-0.01437763,-0.02340528,0.01460419,-0.01281438,-0.03164892,-0.03602199,-0.01380657,0.02359671,0.07833584,-0.12950185,-0.04121957,0.02465788,-0.00163006,-0.02079933,0.01625512,0.00286473,0.02108323,0.00254931,-0.00385085,-0.05203475,-0.01850089,-0.01410833,0.15534681,0.07734276,-0.01604537,0.01384579,0.00868809,-0.01655837,0.00638493,0.12004281,0.0115267,-0.12827618,-0.00477099,0.03784903,-0.02471236,-0.10850369,-0.03523744,0.01902935,-0.04986884,-0.00017682,0.07246959,-0.01866229,-0.04901185,-0.04405821,-0.01753713,-0.01570124,-0.00356759,0.01269727,0.00322452,-0.01496412,-0.03720158,-0.09163544,0.00274633,-0.01149256,0.01266816,0.02261074,-0.01719575,-0.04653732,-0.01313219,0.09140851,-0.00135964,-0.04525691,-0.04338718,-0.09320245,0.10250727,-0.00720617,-0.04098642,-0.03299411,-0.00108102,-0.00266861,-0.01336646,0.02814656,-0.00081027,-0.06172666,0.0703351,0.045017,-0.03449348,0.02826044,0.07421253,0.05799306,-0.06862073,0.05441242,-0.00902467,-0.01404201,0.00574081,-0.01394685,-0.01292793,0.02993306,-0.07414938,-0.22281148,-0.02439239,-0.06862621,-0.02287341,0.00505316,-0.01660809,0.05296186,-0.02658459,0.07640596,0.10374484,0.07298773,-0.0449917,0.00634987,0.01490442,-0.00743616,-0.02786775,-0.06122035,-0.05052358,-0.03555733,0.02804094,0.03632603,0.04373503,0.02409816,-0.08718148,0.03625511,-0.03971617,0.15005939,0.08819934,0.02428821,-0.01421694,0.08545749,0.0307686,0.01118142,-0.06714567,0.02157365,0.05534703,-0.00287379,0.02654743,-0.07765196,-0.00435437,-0.11483885,0.0411956,-0.00958836,-0.09353288,-0.00517306,-0.00516392,-0.00345767,-0.00414617,-0.01773563,0.01700714,0.0473943,-0.03722082,-0.00275927,0.00761182,0.09642628,0.0217568,-0.07790623,0.02712201,0.02433027,-0.00340399,-0.01091235,-0.04732131,0.02120198,-0.03137591,-0.02854288,0.03521584,0.02476018,-0.02915917,-0.00770899,-0.03099898,0.02160649,0.05359265,0.00238055,0.02012257,0.02205754,0.03757301,0.05938468,-0.06469801,0.01288624,-0.02042175,0.01281491,0.01938183,0.0483622,0.04630873,0.07811343,0.00554312,0.0154451,0.06195741,0.03721728,-0.04432999,0.00165818,0.03269535,-0.0239913,-0.03255486,0.07029198,0.00827512,-0.25278938,0.02760451,0.00711455,0.04545272,0.00128604,0.03121704,0.01250196,-0.01821241,-0.03503652,0.01182313,0.05799161,0.02814022,-0.03343403,-0.04880708,-0.02372033,-0.00358324,-0.00806512,-0.04814363,0.08661367,-0.01812282,0.05868463,0.04285692,0.23434409,0.04191382,-0.00112754,0.00709652,0.02559716,0.0464231,0.01833778,0.04202168,0.00365845,0.03383851,0.08719303,-0.02581702,0.01637376,0.03422771,-0.00434715,-0.02124037,-0.00617295,0.01291486,-0.08745199,0.03473738,-0.01956095,0.02918903,0.08249984,0.02177076,-0.00900791,-0.08621853,0.00647773,0.04364192,-0.06403082,-0.0757919,-0.05832207,-0.04771357,0.00703327,0.04766996,0.06187832,-0.04943573,0.01523942,-0.00155817,0.02381104,-0.03354068,0.0791275,-0.01046332,-0.00121567],"last_embed":{"hash":"d1yrjp","tokens":107}}},"text":null,"length":0,"last_read":{"hash":"d1yrjp","at":1753423548809},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#---frontmatter---","lines":[1,6],"size":250,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08711806,-0.00717031,-0.02446596,0.00000856,-0.00933302,0.03902558,0.00134232,-0.05227526,0.02457763,-0.01287379,0.0185108,-0.00514163,0.06263229,-0.00215795,-0.03294688,0.0001769,-0.01653717,-0.02896168,-0.02032568,0.00254915,0.06299635,-0.03904116,-0.01762615,-0.07467002,0.03478082,0.00904122,-0.05644063,-0.04630506,-0.06890463,-0.23229189,0.01552621,-0.02486515,0.01099485,-0.08325949,-0.07878414,-0.03729278,-0.05733193,0.02643317,-0.08866724,0.01547855,0.04582065,-0.00857031,-0.03252236,-0.04351041,-0.00745642,-0.05511082,0.04428413,-0.03086673,0.04922458,0.02049019,-0.09016722,-0.00601633,-0.00192275,0.02892514,0.0502899,0.05516058,0.02372226,0.07841834,0.01088715,0.06684729,0.05236514,0.0764835,-0.16438143,0.01962571,0.01235815,0.00919249,0.00833582,-0.03664184,-0.01857713,0.03601524,0.0355035,0.03684113,0.00782514,0.03625964,0.01031592,-0.00682144,-0.04147444,-0.05131161,-0.07158452,0.02871469,-0.0676176,0.00874488,0.01014154,0.02620349,0.00476629,0.04160991,0.05581887,0.04367115,0.06480194,-0.04808407,0.0005847,0.04074229,0.00323251,0.03805215,0.00331907,0.03876936,0.05291031,-0.00482808,-0.01941908,0.1383922,0.00485779,-0.02689029,-0.02042529,0.03858526,0.03664231,-0.04685203,0.03042756,-0.01481238,-0.0328563,0.00733779,0.04946351,-0.0082247,0.05963123,-0.0031994,-0.06259941,0.00005822,0.01496274,-0.00562357,0.02301022,0.01548237,-0.05814719,0.05939378,0.02108491,-0.04716627,0.02850731,0.02289808,0.02837971,0.04316561,0.0277448,0.01921422,0.01693123,-0.02418696,-0.14515464,-0.0447537,-0.01548941,-0.01537544,-0.00515575,0.01382916,-0.01253764,-0.01935142,-0.01869284,0.00658101,0.06739074,-0.11374619,-0.04191547,0.02792472,-0.04535586,-0.02469685,0.00866822,-0.00949155,0.03570301,0.0341763,-0.03482082,-0.05775562,0.01005916,0.00067063,0.1328716,0.08975995,-0.03495324,0.02180923,-0.01570602,0.00298068,-0.00177619,0.08532841,0.00926415,-0.10673482,0.01760319,0.03612138,-0.03932599,-0.0919861,-0.02756057,0.03238368,-0.06706775,-0.00562532,0.02658985,-0.05096311,-0.09302612,-0.03265394,0.00171429,-0.02258667,-0.01158738,0.00833181,-0.00445798,-0.00847207,-0.01816006,-0.1084311,0.02566634,0.00206951,-0.00198313,0.02805577,-0.00677866,-0.00644157,0.01809018,0.10626934,-0.02949722,-0.04731686,-0.06705914,-0.07208338,0.09524456,-0.01653196,-0.03611711,-0.01360907,0.02861274,-0.00872422,-0.00969689,0.03127721,-0.00995837,-0.03859682,0.1141163,0.05133156,-0.06708045,0.01088981,0.05275827,0.07408274,-0.08773293,0.06835811,-0.00669333,0.01567996,-0.0107432,0.00763191,0.02335782,0.02572822,-0.08396274,-0.20502558,-0.04829638,-0.06079317,0.00576725,0.02655857,-0.03999787,0.06749631,-0.0333106,0.06480511,0.09596618,0.07239254,-0.03945915,0.00678572,0.0215596,-0.02113206,0.00542242,-0.06793786,-0.04329617,-0.04058391,0.03450098,0.02170901,0.05770624,0.00718427,-0.07325823,0.05376787,-0.07475071,0.1500605,0.0582608,0.01905693,-0.01395863,0.06542448,-0.0166207,0.03977284,-0.05922219,0.03453025,0.07875663,0.00450005,-0.00266711,-0.09012684,0.00049574,-0.11225634,0.02618442,-0.00623502,-0.10484362,-0.003328,0.00843855,-0.00130287,0.01021145,-0.01686632,0.02262606,0.06823827,-0.0347671,-0.02073016,0.01314801,0.07479146,-0.00712111,-0.05664666,0.00752337,0.00378978,-0.00184036,0.00797415,-0.04766554,0.02706515,-0.02953811,0.0174429,0.02392059,0.03721727,-0.01403481,0.02370398,0.00747007,0.02200376,0.07554399,-0.01799297,0.06962051,0.04078623,0.06053201,0.07498205,-0.06453256,0.01923343,-0.00709474,0.02237042,0.03471351,0.05142009,0.05814445,0.06729084,-0.0023189,0.02937461,0.06752633,0.0101074,-0.05126985,0.00473747,0.02617723,-0.01689761,-0.00693416,0.05539077,0.04912053,-0.2411238,-0.00186686,-0.0317487,0.03754937,-0.01872506,-0.00607803,0.0036314,-0.01901585,-0.00375747,-0.03130386,0.0586991,0.00790545,-0.00936539,-0.0807519,-0.00542568,0.0047193,-0.00405643,-0.04410888,0.08239037,-0.04034939,0.05274209,0.03418447,0.23472057,0.01462623,-0.00432893,0.01478211,0.0030553,0.02189874,0.02213213,0.03058097,-0.03244956,0.0452809,0.08179042,-0.00846906,0.02814409,0.03717458,-0.02945141,-0.03283035,0.0027492,-0.01409588,-0.03383721,0.02674886,-0.0330277,0.00803306,0.08303786,0.01450048,-0.01141203,-0.08471388,0.01780357,0.08028827,-0.0571854,-0.0674476,-0.07737945,-0.03001298,0.01855402,0.02917119,0.04359119,-0.02686586,0.00567102,-0.00866462,0.02154015,-0.0264467,0.07500475,-0.02026841,-0.01085273],"last_embed":{"hash":"s7ymia","tokens":483}}},"text":null,"length":0,"last_read":{"hash":"s7ymia","at":1753423548847},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD","lines":[8,325],"size":20018,"outlinks":[{"title":"![Download Best Software: Lottery, Lotto, Powerball, Mega Millions, Euromillions, Pick Digit, Keno..","target":"https://saliu.com/images/lottery-software-systems.gif","line":7},{"title":"Lotto Wonder Grid beats random expectation by over 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":14},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":25},{"title":"FFG Median, Filtering, Probability, Jackpot","target":"https://saliu.com/bbs/messages/923.html","line":220},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":290},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":292},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":294},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":296},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":298},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":299},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":300},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":301},{"title":"Lottery Utility Software","target":"https://saliu.com/lottery-utility.html","line":302},{"title":"Practical lottery and lotto filtering in software","target":"https://saliu.com/filters.html","line":303},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":304},{"title":"Updates to the Bright lotto, lottery, and horseracing software","target":"https://saliu.com/forum/software-updates.html","line":306},{"title":"_Markov Chains_ Lottery Software, Lotto, Program, Algorithms","target":"https://saliu.com/Markov_Chains.html","line":308},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":309},{"title":"Lottery Wonder Grid reports a strategy to beat random play.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":311},{"title":"Lotto systems of pairs beat random expectation consistently by 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":313},{"title":"Forums","target":"https://forums.saliu.com/","line":315},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":315},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":315},{"title":"Contents","target":"https://saliu.com/content/index.html","line":315},{"title":"Home","target":"https://saliu.com/index.htm","line":315},{"title":"Software","target":"https://saliu.com/infodown.html","line":315},{"title":"Search","target":"https://saliu.com/Search.htm","line":315},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":315},{"title":"The wonder grid lotto strategy beats lottery random picks by better than 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":317}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08551228,-0.01229216,-0.03094839,-0.0224446,-0.00095336,0.03012522,-0.00922862,-0.04335839,0.03196478,-0.02167964,0.00839956,-0.01795721,0.07257609,0.00117301,-0.02366822,0.00748545,-0.01632263,-0.00186027,-0.04645066,0.01414755,0.06271102,-0.03963288,-0.01897171,-0.08171299,0.03509656,0.00320986,-0.05209341,-0.07396649,-0.03805962,-0.19699201,0.01229567,-0.0233901,0.01214331,-0.08279835,-0.06353947,-0.04451322,-0.04842163,0.01422779,-0.1008196,0.00020701,0.03353003,-0.0009655,-0.04126786,-0.0592749,-0.01055878,-0.03590106,0.05302312,-0.02376582,0.06238786,0.02159623,-0.08528361,0.01761181,-0.00987309,0.04332907,0.05028783,0.06241108,0.02995751,0.07407895,-0.01064485,0.06567465,0.04124095,0.07983229,-0.1642614,0.01303069,0.03466916,0.02092439,0.01394435,-0.00273369,-0.03644653,0.04197139,0.02667861,0.05215111,0.00365694,0.02912398,0.01189331,-0.00029779,-0.06094223,-0.04413608,-0.07600947,0.03567261,-0.06994478,0.00060958,-0.01468853,0.028389,-0.00263919,0.03408371,0.06271903,0.04248938,0.0632825,-0.04048674,-0.01168891,0.05187002,0.0026082,0.05107298,0.00974837,0.04150555,0.05037256,-0.01715553,0.00731507,0.1583263,-0.04026204,-0.02803534,-0.0210561,0.01489346,0.02318656,-0.03722012,0.03949276,-0.00592338,-0.01373713,0.01639775,0.04748841,-0.00803445,0.03988688,-0.02695194,-0.0609949,-0.0114664,0.01630702,-0.00526578,0.01682291,0.02056096,-0.07921357,0.0595609,0.02500188,-0.05566692,0.03729414,0.01238076,0.02287598,0.0363814,0.03394736,0.03734919,0.01409625,-0.02165006,-0.15180072,-0.04237569,-0.03190864,-0.03304619,0.01534652,0.00776736,-0.02009832,-0.02477407,-0.00722801,0.00579741,0.07236516,-0.12063178,-0.03314119,0.03371106,-0.03977456,-0.03256267,-0.01134442,-0.00149258,0.02580697,0.02258089,-0.02712193,-0.06102434,0.00708832,0.0046372,0.14170985,0.06912955,-0.01720331,0.02758173,-0.00674472,-0.00458815,0.00690181,0.11062626,0.02115101,-0.11082664,0.01566666,0.05333638,-0.04008346,-0.08181161,-0.02843424,0.03468867,-0.07771342,-0.0046511,0.03831595,-0.03886049,-0.07963914,-0.05743558,0.00431115,-0.01862306,-0.03066353,0.0155637,-0.00165473,-0.02069229,-0.02233546,-0.10249177,0.00768338,-0.00124777,0.01692534,0.04023757,0.01427347,-0.00418966,0.01457543,0.08695573,-0.02838039,-0.03392345,-0.06473181,-0.08362543,0.08797298,-0.01931298,-0.03171178,-0.00519514,0.00701178,-0.01106852,0.00850227,0.03119063,-0.02606659,-0.04181919,0.09454166,0.0339889,-0.05476853,0.01324497,0.05968028,0.07821039,-0.08793805,0.05781491,-0.00747497,0.01100711,-0.01902211,0.01853633,0.02281461,0.02333592,-0.07121944,-0.22576708,-0.02741444,-0.06795637,-0.00351982,0.01378791,-0.03683645,0.05605722,-0.01999629,0.06296118,0.10719654,0.08000701,-0.0494145,-0.00627035,0.03434185,-0.02589186,0.01685256,-0.05942253,-0.0331162,-0.04193854,0.02638858,0.0124414,0.05112568,0.00725952,-0.06438132,0.07078747,-0.0551914,0.15851888,0.06403851,0.02648289,-0.02728844,0.06028743,0.01304917,0.02666749,-0.06201815,0.0424683,0.07576399,0.00712729,-0.0383497,-0.0781698,-0.00142454,-0.11532719,0.01621452,0.01746865,-0.07890356,0.00639381,-0.00651979,-0.00726405,-0.00589897,0.00897508,0.03553679,0.04567805,-0.0248166,0.00112093,0.00236619,0.06844247,0.00601302,-0.07011317,0.02519828,-0.00224007,-0.00324518,0.01919409,-0.05344997,0.02915001,-0.03745967,0.00403635,0.02944942,0.02694361,-0.01992837,0.01600453,0.00098208,0.02966588,0.05432836,-0.01668431,0.06429934,0.03367352,0.04448085,0.0691861,-0.08649883,0.02122025,-0.01913253,0.01878678,0.02681882,0.0541776,0.05312214,0.0465328,0.00845461,0.04394003,0.08166701,0.01608586,-0.04846859,0.01047854,0.02745085,-0.01640852,-0.00831816,0.06238385,0.0321983,-0.25856423,-0.00436835,-0.00862848,0.03493192,-0.01204715,-0.0034591,0.00037038,-0.03828603,-0.02084145,-0.02027215,0.06802794,-0.00934788,-0.02598894,-0.0692604,-0.00094789,0.02078187,0.00956271,-0.04618779,0.09853055,-0.02050839,0.05198183,0.01908674,0.22862242,0.01914662,0.00730355,0.01366851,-0.00149485,0.04234591,0.01744407,0.03172129,-0.00679144,0.0443665,0.07758583,-0.01195512,0.02267309,0.06169446,-0.01615349,-0.02362598,0.00351186,0.00114977,-0.04849989,0.02503748,-0.04936003,-0.00477829,0.08078226,0.01955568,-0.02324845,-0.06703065,0.00418336,0.06798858,-0.07148358,-0.06466562,-0.07071795,-0.01967933,0.03671343,0.01480758,0.04485236,-0.03481855,0.00059545,0.00690955,0.02109347,-0.01029647,0.04200328,-0.00881301,0.00324068],"last_embed":{"hash":"wj3qno","tokens":142}}},"text":null,"length":0,"last_read":{"hash":"wj3qno","at":1753423549061},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#{1}","lines":[10,15],"size":371,"outlinks":[{"title":"![Download Best Software: Lottery, Lotto, Powerball, Mega Millions, Euromillions, Pick Digit, Keno..","target":"https://saliu.com/images/lottery-software-systems.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By Ion Saliu, ★ _Founder of Lotto Mathematics_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07763755,-0.01467767,-0.0135727,-0.01780345,-0.01756869,0.06345065,0.01074817,-0.05086359,0.02923896,-0.00819448,0.02634612,-0.00010301,0.03408374,0.00193915,-0.04060571,-0.00122348,-0.0148722,-0.04755273,-0.03031671,-0.00022448,0.06546871,-0.05326244,-0.01201594,-0.07961546,0.04414565,0.01287469,-0.06641403,-0.03531541,-0.07416654,-0.23629963,0.02869712,-0.02338115,0.01870431,-0.07449766,-0.07689495,-0.04529246,-0.05174919,0.04941437,-0.07866704,0.02927495,0.04935817,0.00016726,-0.02199588,-0.04050558,-0.00747055,-0.06652147,0.0422348,-0.04110705,0.06023876,0.03566769,-0.08416478,-0.01752719,0.00749391,0.02837791,0.05982219,0.04012814,0.01838204,0.06745107,0.02349892,0.05270624,0.05342115,0.06994565,-0.155397,0.03554509,0.00544094,0.00124823,-0.00492527,-0.05082166,-0.01062887,0.03149706,0.0169143,0.03664902,-0.00209354,0.05425452,0.014209,-0.01949189,-0.05010797,-0.05386873,-0.06077266,0.01685062,-0.06892146,-0.00448538,0.02628159,0.02559197,0.00482377,0.03776724,0.05737837,0.05221401,0.05120188,-0.04125028,0.00128569,0.05392678,-0.01012859,0.0432146,-0.00547636,0.04255111,0.04834738,-0.00530444,-0.03026156,0.13704039,0.01461338,-0.03466685,-0.01553074,0.05352779,0.04014634,-0.03325041,0.01210638,0.00068899,-0.04168213,0.00386615,0.04341687,-0.01004081,0.0581156,-0.016081,-0.05289645,-0.00625427,0.0203945,-0.00673214,0.01639779,0.02157303,-0.05821986,0.05349771,0.00333637,-0.04293415,0.03593433,0.02285375,0.02178376,0.06042656,0.02157703,0.01373732,0.01057374,-0.01635055,-0.13742621,-0.04901532,0.00410831,-0.00273646,-0.00071241,0.01548524,-0.02408873,-0.02102665,-0.01384848,0.01704437,0.07382453,-0.11040387,-0.0341444,0.02089404,-0.04349654,-0.00382256,0.02247098,-0.00438492,0.03364551,0.03077531,-0.04214359,-0.05047739,0.00513332,-0.01243759,0.11934994,0.0909698,-0.04659616,0.01177267,-0.01734659,-0.01601301,0.01057157,0.08156199,0.00017189,-0.09377519,0.01398179,0.02513077,-0.03304551,-0.09766145,-0.03441308,0.03263624,-0.06254023,-0.01456435,0.03179764,-0.05108254,-0.0989746,-0.03097955,-0.00729113,-0.02250977,-0.01203473,0.01928719,0.00112354,0.00321523,-0.02581935,-0.1110474,0.04042124,-0.00396196,-0.00418324,0.03678042,-0.01889189,-0.01110687,0.01183417,0.10209426,-0.01824844,-0.05819563,-0.05299523,-0.07279011,0.10376345,-0.02560448,-0.01694494,-0.01549993,0.04173842,-0.0021531,-0.01849735,0.04602528,-0.0142553,-0.02665601,0.10707327,0.05827617,-0.07432217,-0.00069869,0.05204892,0.07521581,-0.08395708,0.07012026,0.02044735,-0.00354759,0.01874429,-0.00487656,0.03399039,0.00644685,-0.08658528,-0.20424429,-0.03782146,-0.05706499,0.000839,0.0423065,-0.0513215,0.07261166,-0.03395256,0.08269694,0.08269738,0.07300982,-0.04250333,0.01022116,0.02180255,-0.02419247,-0.00535263,-0.09041093,-0.04900369,-0.04296871,0.05662652,0.02531156,0.06582589,-0.00374458,-0.07054469,0.04150889,-0.06960116,0.14241439,0.05709197,0.01640149,-0.01685604,0.07694136,-0.01348279,0.0251509,-0.06295079,0.02281508,0.0658108,-0.0171887,0.01616202,-0.0898599,0.00825927,-0.10172257,0.0331795,-0.01482604,-0.09918743,-0.00198644,0.0103313,-0.00220859,0.02186914,-0.03080193,0.00829964,0.07130582,-0.04363803,-0.02004775,0.0177028,0.07737649,-0.00305614,-0.05895266,0.01473347,0.01173909,0.01349288,-0.00108645,-0.03925486,0.02443126,-0.0238167,0.01401071,0.03168112,0.0363122,-0.01613715,0.0318707,0.00110088,0.01712637,0.06016181,-0.01460932,0.05504143,0.03255802,0.05290739,0.09264892,-0.03676151,0.00890165,-0.01199573,0.02366385,0.0204021,0.03223416,0.04577572,0.07752483,0.0055518,0.01689486,0.06518375,-0.00036468,-0.04244291,0.00064186,0.0365985,-0.03426347,-0.01123609,0.04854981,0.03464131,-0.24066588,0.01486764,-0.02805965,0.05426661,-0.01935854,0.00277885,0.00527289,-0.00371491,0.00628292,-0.04380959,0.06319074,0.03035797,0.00499195,-0.09452445,-0.00937989,-0.00199865,-0.0096676,-0.02909888,0.08062874,-0.04792535,0.06543955,0.03792006,0.2308652,0.01457019,-0.02041935,0.01254814,0.01961448,0.01143529,0.01094611,0.02205525,-0.04043037,0.03109724,0.07751827,-0.01524145,0.02535292,0.02851469,-0.02736984,-0.02345174,0.01253902,-0.00870684,-0.03846947,0.02075225,-0.0410924,0.01538988,0.07406561,0.03047164,-0.00033461,-0.07768015,0.01732627,0.0822781,-0.04533877,-0.06669086,-0.06526356,-0.04126257,0.00341249,0.03478687,0.04580839,-0.02844053,0.00893611,-0.00769549,0.01764493,-0.03920431,0.09639668,-0.04762768,-0.01681422],"last_embed":{"hash":"jwhn85","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"jwhn85","at":1753423549114},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By Ion Saliu, ★ _Founder of Lotto Mathematics_","lines":[19,296],"size":16368,"outlinks":[{"title":"Lotto Wonder Grid beats random expectation by over 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":3},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":14},{"title":"FFG Median, Filtering, Probability, Jackpot","target":"https://saliu.com/bbs/messages/923.html","line":209}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By Ion Saliu, ★ _Founder of Lotto Mathematics_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08018528,-0.01214765,-0.01160684,-0.01824789,-0.02093411,0.06529427,0.01281259,-0.05065928,0.02951974,-0.01002901,0.0244889,0.00223502,0.03410306,0.00598721,-0.04235418,0.00008301,-0.0142239,-0.04992263,-0.03099221,0.00004024,0.06535798,-0.0536306,-0.01209395,-0.08068927,0.04280293,0.01487283,-0.06643304,-0.0324693,-0.07286143,-0.23804493,0.02989297,-0.02395316,0.01779361,-0.0717336,-0.07556013,-0.04547018,-0.05092264,0.04906517,-0.07925545,0.02928372,0.04864239,-0.00220087,-0.02188309,-0.03785592,-0.00452033,-0.06856812,0.04148205,-0.03879744,0.05795905,0.03854119,-0.08214775,-0.02038197,0.00502567,0.02836693,0.06228331,0.03773179,0.01793015,0.06519996,0.02832535,0.05440485,0.05118077,0.06824546,-0.15556516,0.03526293,0.00371527,0.00245604,-0.00687276,-0.05068974,-0.00998307,0.02686276,0.01904114,0.03478403,-0.00152953,0.05459855,0.01537425,-0.01918472,-0.05036922,-0.05378002,-0.06156629,0.02070736,-0.06830087,-0.00360458,0.02816006,0.02502672,0.00534461,0.04046907,0.05828998,0.05231757,0.05541489,-0.04042953,0.0030637,0.05128215,-0.00839282,0.0419392,-0.00572839,0.04301845,0.04688725,-0.00515264,-0.03116361,0.1371491,0.01518768,-0.03384432,-0.0155079,0.05484212,0.03901102,-0.03440012,0.01057262,0.00235687,-0.04064646,0.00150129,0.04249653,-0.01044712,0.05807277,-0.01212767,-0.05244756,-0.00612205,0.01908569,-0.00514048,0.01553963,0.02346786,-0.05709507,0.05207429,0.00177056,-0.04267122,0.03425248,0.02556,0.02126132,0.06082335,0.02039961,0.01358416,0.01174708,-0.01524221,-0.13299313,-0.04842885,0.0056376,-0.00337662,0.0001586,0.01781818,-0.02028737,-0.01922765,-0.01307601,0.01536119,0.07487676,-0.11057866,-0.03695614,0.02166405,-0.04309495,-0.00406556,0.0220033,-0.00550404,0.03204206,0.02848394,-0.04321624,-0.05194426,0.00679154,-0.01153923,0.11845613,0.09321105,-0.04344464,0.00936195,-0.01994647,-0.01557601,0.00602381,0.08416178,0.00031918,-0.09317496,0.0104953,0.02385487,-0.03202522,-0.09554125,-0.03313999,0.0308491,-0.06040026,-0.01294016,0.03076802,-0.0529603,-0.10212373,-0.03157381,-0.01115011,-0.02336176,-0.00816407,0.0173161,-0.00148878,0.00473079,-0.02376248,-0.11143237,0.03965793,-0.0076172,-0.0041512,0.03610854,-0.01946449,-0.01125161,0.01436639,0.10054137,-0.01608756,-0.05972557,-0.05422753,-0.07349943,0.10354581,-0.02628755,-0.01792675,-0.01483189,0.04041183,-0.00253858,-0.01872912,0.04325088,-0.01477346,-0.02579268,0.10930651,0.05913264,-0.07353123,-0.00625048,0.04973714,0.07746739,-0.08516943,0.07156014,0.02033082,-0.00114031,0.01851894,-0.00260729,0.03433869,0.00460535,-0.08871612,-0.20468894,-0.03731122,-0.05761258,0.00193842,0.04184489,-0.05426729,0.07105417,-0.03689184,0.08235369,0.08419808,0.07147201,-0.04183396,0.00983358,0.02257971,-0.02433934,-0.00699429,-0.08852433,-0.04967739,-0.03975675,0.05526597,0.02419993,0.06404196,-0.00573394,-0.06996281,0.0394753,-0.06668311,0.14042969,0.05604745,0.01615116,-0.01678938,0.07806816,-0.01390918,0.02668154,-0.06019728,0.02208475,0.06648453,-0.01656457,0.01637253,-0.08695934,0.00741651,-0.10085595,0.03251155,-0.0158781,-0.1002694,-0.00206782,0.01623384,-0.00150664,0.02098405,-0.03524806,0.00759494,0.0717904,-0.04312347,-0.01728647,0.01800309,0.07808842,-0.00671715,-0.05744671,0.01403974,0.01496853,0.01571579,-0.00139057,-0.03836698,0.02473052,-0.0232071,0.01843145,0.03115142,0.03730831,-0.01417934,0.03251971,0.00242056,0.0156653,0.06085694,-0.01597119,0.05677933,0.03147744,0.05373974,0.09256092,-0.03512808,0.00828931,-0.01146424,0.02474204,0.02070526,0.03401146,0.04669782,0.07636887,0.00604747,0.0174109,0.0643626,-0.00104535,-0.04273231,0.00023782,0.03714621,-0.03572202,-0.00795642,0.04543758,0.03473393,-0.24195974,0.01820615,-0.02844322,0.05231893,-0.02515998,0.00056571,0.004098,-0.00080775,0.00757751,-0.04415336,0.06299323,0.03016185,0.00893922,-0.0949154,-0.01317839,-0.00157636,-0.0092572,-0.031472,0.08203676,-0.04865477,0.06658981,0.03753145,0.23292263,0.01390543,-0.01924383,0.01258803,0.01858313,0.00702505,0.01076067,0.02172942,-0.04172334,0.03256549,0.07750976,-0.0138796,0.0237469,0.03023338,-0.02973841,-0.02255288,0.01192455,-0.01190708,-0.03979406,0.01980817,-0.04052439,0.01610376,0.07405942,0.02854984,-0.00003712,-0.07778376,0.01695676,0.0818252,-0.04705769,-0.06353156,-0.06501235,-0.03967661,0.00054562,0.0351766,0.0451663,-0.02843032,0.00902524,-0.00798128,0.01642445,-0.03529925,0.09693976,-0.05099253,-0.01839563],"last_embed":{"hash":"1cqtfp4","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"1cqtfp4","at":1753423549311},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#By Ion Saliu, ★ _Founder of Lotto Mathematics_#{1}","lines":[21,296],"size":16317,"outlinks":[{"title":"Lotto Wonder Grid beats random expectation by over 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":12},{"title":"FFG Median, Filtering, Probability, Jackpot","target":"https://saliu.com/bbs/messages/923.html","line":207}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08999398,-0.00993778,-0.02309778,-0.04178941,-0.01712362,0.04203923,-0.02156464,-0.03351495,0.03168041,-0.01282885,0.02054538,-0.01428042,0.06362972,-0.02672222,-0.02198554,-0.01979982,-0.00317448,0.01509723,-0.02989846,-0.01279553,0.04441392,-0.02979281,-0.03098558,-0.08777681,0.04640347,0.00620606,-0.05730132,-0.04909576,-0.04606184,-0.20008999,0.02466511,-0.02397027,0.00395459,-0.092659,-0.07934257,-0.01621294,-0.05470493,0.04078461,-0.10052188,0.02019949,0.03283627,0.00012824,-0.01053682,-0.0339898,0.00259319,-0.04449799,0.043228,-0.01490965,0.05307383,0.00342798,-0.11269147,0.00027408,0.00527647,0.02045714,0.05024585,0.04242337,0.0203234,0.11483673,-0.004473,0.05173907,0.04263727,0.06755098,-0.17767461,0.04894968,0.02124307,0.04062304,0.00939058,-0.00968956,0.00679337,0.04223112,-0.00030914,0.04124908,0.00848291,0.04927605,0.01168257,-0.00293965,-0.06615005,-0.05906224,-0.0732135,0.01013211,-0.05748247,-0.00074123,0.00508641,0.04381865,-0.01308621,0.03854355,0.04573591,0.03941618,0.07861399,-0.04801302,0.00900841,0.07692043,0.01422752,0.03841822,0.00675565,0.01787306,0.05594314,-0.00922377,-0.0254646,0.1408852,0.01155478,-0.02904535,0.00245879,0.01704926,0.02452123,-0.03870502,0.0202694,-0.01235466,-0.03532322,0.02901907,0.04564121,-0.01398112,0.01474432,-0.03646715,-0.06669579,-0.02256555,0.00748734,-0.00263899,-0.00200831,0.00718213,-0.06926162,0.02403553,0.01493268,-0.02290672,0.01516015,0.00721219,0.01338079,0.04925181,0.01793745,0.01977829,0.0315784,0.00398915,-0.1498,-0.05133248,-0.00879478,-0.03041768,0.01556328,-0.00544271,-0.02442166,-0.01035808,-0.02682582,0.00415779,0.06485336,-0.12419542,-0.04990445,0.03739958,-0.01392073,-0.03095189,-0.01012139,-0.00756525,0.04512836,0.00170166,-0.0336931,-0.05146705,0.00541602,-0.00408485,0.16353311,0.06174573,-0.02011587,0.01233193,-0.03238641,-0.01731899,0.00091285,0.12375644,0.01343354,-0.11475243,0.00120992,0.04954621,-0.02973474,-0.1105539,-0.03178719,0.01526474,-0.07363642,0.01329262,0.07295175,-0.02973394,-0.08120451,-0.03485235,0.01051921,-0.02002739,0.00164259,-0.00612404,0.0031082,-0.01022885,-0.02416308,-0.1022024,-0.00222529,0.00447692,-0.00393987,0.02341054,0.00315851,0.0010997,-0.01361105,0.0687852,-0.04555467,-0.03534997,-0.06688368,-0.09132919,0.10374333,-0.00812679,-0.00783103,0.00488184,0.02201172,0.00116262,0.00551506,0.03831706,-0.0034687,-0.05293824,0.1009962,0.03861603,-0.05925071,0.03747744,0.06962952,0.04634363,-0.05291063,0.07377036,0.0062226,-0.00318178,-0.01380985,0.00502825,-0.00123091,0.02696682,-0.07198014,-0.202537,-0.04531309,-0.06675471,-0.01085413,-0.00138671,-0.03005039,0.07007493,-0.04432411,0.06346915,0.09496818,0.09761553,-0.05817351,0.01235857,0.05296119,-0.01771849,-0.01963901,-0.06699996,-0.03923101,-0.06186247,0.04290655,0.03713382,0.02815325,0.01691217,-0.06663269,0.02410346,-0.05078572,0.15207261,0.01702013,0.03218563,0.00355435,0.0886635,0.00514493,0.01971555,-0.07101094,0.01973507,0.07261413,-0.00756286,-0.01041573,-0.063053,0.02042017,-0.10396782,0.03014719,0.00055412,-0.07432642,-0.02821946,0.01054768,0.00204489,0.01317623,0.00125885,0.02989312,0.032514,-0.00633271,0.00152157,0.01066926,0.04677732,0.00778235,-0.05094438,0.03593571,-0.01099362,0.01292737,-0.02683164,-0.0739326,0.01937096,-0.01552628,0.01913661,0.02300523,0.0246519,-0.018746,0.03048118,-0.00540667,0.00907744,0.06443443,-0.01192778,0.06828246,0.0258869,0.04449202,0.08313927,-0.06305744,0.00091767,-0.02119141,-0.03332772,0.01770141,0.03956456,0.06065591,0.06866788,0.01253099,0.04140464,0.0486546,0.00777257,-0.04065688,-0.00709079,0.02069156,-0.01729032,-0.00244893,0.05000754,0.04019271,-0.25899971,0.01108472,-0.00945859,0.04928566,-0.00766523,-0.0036206,0.00719376,-0.039139,0.00090296,-0.01448022,0.06421256,-0.00104013,-0.02007117,-0.06472076,-0.00152539,-0.02485201,0.01960137,-0.02990422,0.09049923,-0.01896258,0.05634828,0.04308356,0.23401688,0.02274981,0.01130987,0.0135166,0.0136845,0.03392945,0.01968625,0.04993435,-0.00156926,0.05452615,0.07714941,-0.00746322,-0.00240413,0.02895045,-0.027702,-0.00901089,0.00501831,0.01217854,-0.04778922,0.03136852,-0.03480259,0.01301721,0.07764512,0.02958819,-0.00576272,-0.10091744,0.02971531,0.07329585,-0.06432267,-0.04128541,-0.0815521,-0.04280896,0.02244177,0.02703385,0.05195972,-0.02622609,0.01547848,-0.01219444,0.03672305,-0.04059453,0.07663584,0.01030116,-0.00412238],"last_embed":{"hash":"ogqlxj","tokens":429}}},"text":null,"length":0,"last_read":{"hash":"ogqlxj","at":1753423549512},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)","lines":[297,325],"size":3056,"outlinks":[{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":5},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":7},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":9},{"title":"Lotto, Lottery Strategy Tutorial","target":"https://saliu.com/bbs/messages/818.html","line":10},{"title":"_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_","target":"https://saliu.com/mdi_lotto.html","line":11},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"Lottery Utility Software","target":"https://saliu.com/lottery-utility.html","line":13},{"title":"Practical lottery and lotto filtering in software","target":"https://saliu.com/filters.html","line":14},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":15},{"title":"Updates to the Bright lotto, lottery, and horseracing software","target":"https://saliu.com/forum/software-updates.html","line":17},{"title":"_Markov Chains_ Lottery Software, Lotto, Program, Algorithms","target":"https://saliu.com/Markov_Chains.html","line":19},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":20},{"title":"Lottery Wonder Grid reports a strategy to beat random play.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":22},{"title":"Lotto systems of pairs beat random expectation consistently by 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":24},{"title":"Forums","target":"https://forums.saliu.com/","line":26},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":26},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":26},{"title":"Contents","target":"https://saliu.com/content/index.html","line":26},{"title":"Home","target":"https://saliu.com/index.htm","line":26},{"title":"Software","target":"https://saliu.com/infodown.html","line":26},{"title":"Search","target":"https://saliu.com/Search.htm","line":26},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":26},{"title":"The wonder grid lotto strategy beats lottery random picks by better than 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":28}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07948946,-0.03916073,-0.02746245,-0.02943247,-0.02995781,0.04144541,0.00136383,-0.03898118,0.03508947,-0.00547552,0.01642279,-0.00361186,0.06342866,0.00281374,-0.01359706,0.00084755,0.00402114,0.03338791,-0.0325198,-0.00435874,0.05447614,-0.03459381,-0.05594249,-0.09850343,0.03945623,0.01776093,-0.06445302,-0.06768655,-0.03278067,-0.16240595,0.02670459,-0.01178172,-0.00584229,-0.09200818,-0.07833458,-0.05047451,-0.06018796,0.045154,-0.11902975,0.0232714,0.02925506,0.01257723,-0.02849832,-0.03501688,-0.01742225,-0.05236877,0.05739345,-0.00401328,0.07628974,0.01004067,-0.08094427,0.01802384,0.00060408,0.03057293,0.02962621,0.04200929,0.02011243,0.10576604,-0.02595398,0.03708234,0.03275166,0.0586628,-0.14657883,0.03050652,0.01032147,0.05191928,0.02127382,-0.01046886,0.00021789,0.05654265,0.02575271,0.04662315,0.00851446,0.05134656,0.01936975,-0.00797823,-0.05149775,-0.05346384,-0.05295137,0.02269333,-0.06698121,-0.02073956,0.0060851,0.05776208,-0.02489529,0.03864734,0.0486597,0.04357338,0.09225864,-0.06158109,-0.00615159,0.07778919,0.01063203,0.0529737,0.0145534,0.00662501,0.05055387,-0.02263463,0.0115473,0.16661769,-0.01770189,-0.02336785,-0.0062615,0.01008389,0.01611109,-0.02591265,0.0048388,-0.01761226,-0.04314401,0.05052619,0.06394336,-0.00575196,0.04128024,-0.0313815,-0.06170744,-0.03409941,-0.00760813,0.01043211,-0.00338015,-0.00070221,-0.07963126,0.03238506,0.00681363,-0.03026807,0.01715421,0.00676079,0.00312608,0.06310966,0.00076302,0.02474466,0.02793584,-0.02208744,-0.14424632,-0.03539249,0.0029752,-0.03349676,0.02825465,-0.00354794,-0.05023165,0.00622816,-0.02744311,0.0259867,0.09257729,-0.13156244,-0.03337711,0.03249352,-0.01837301,-0.01167729,-0.01072279,-0.00585693,0.02854916,0.01004411,-0.02626927,-0.06170514,-0.00044821,-0.01298942,0.16529024,0.02624279,-0.02635288,0.0308485,-0.03630326,-0.0046027,0.00666628,0.12580045,0.00051165,-0.09486644,-0.00146727,0.0591097,-0.03472212,-0.09819715,-0.0383345,0.02220064,-0.07350465,-0.00525378,0.09015906,-0.02471236,-0.10817218,-0.04710472,-0.00358063,-0.02179825,-0.00158959,-0.00903802,0.01229247,-0.01891422,-0.02167931,-0.09247513,0.00046361,-0.01399305,0.00723718,0.02663232,-0.0105585,0.01217154,-0.01587273,0.04653004,-0.03269828,-0.0413717,-0.07262017,-0.08355898,0.10703085,-0.01320478,-0.02233169,0.00268039,0.0232624,0.00604439,-0.00980416,0.02946914,-0.00175913,-0.05965662,0.08654672,0.05706784,-0.06909744,0.01990878,0.06285653,0.05694874,-0.07434452,0.06874496,0.01206922,0.01610599,-0.0044994,-0.01186346,-0.00311179,0.0307678,-0.08069124,-0.18887191,-0.04639742,-0.05878901,0.00096333,0.02186816,-0.02548601,0.04056633,-0.03549332,0.05792585,0.10012244,0.0966154,-0.07656963,0.01620896,0.06144871,-0.02156902,-0.02022202,-0.065038,-0.04735316,-0.03914867,0.02418223,0.02841825,0.01920114,0.03796424,-0.09403937,0.03069759,-0.04080836,0.15474802,0.01765429,0.03701768,0.00348069,0.06094035,-0.0140096,0.00888282,-0.04972808,0.02119874,0.07376882,-0.02001096,-0.02776648,-0.05955581,0.01878596,-0.08371048,0.01161273,0.00910087,-0.08164122,-0.00470018,-0.00694026,-0.01328168,0.0236354,0.00294745,0.0401746,0.02938799,-0.00455323,0.00678208,0.01221707,0.05062951,0.00953663,-0.05287689,0.02365255,-0.00506577,0.00328454,-0.00879035,-0.06863773,0.00947736,-0.01968302,0.0231096,0.03121331,0.00696732,-0.04401693,0.01831383,-0.02959419,0.01806505,0.06865912,-0.00308162,0.06242672,0.03494132,0.04554027,0.09217241,-0.06554926,0.00117436,-0.00687777,-0.01742274,0.01466429,0.03977594,0.08102198,0.05873383,0.00960901,0.03504292,0.06824632,-0.01336359,-0.03733008,0.00444258,0.03408267,-0.03682264,-0.00842687,0.05027475,0.02799844,-0.24727236,0.02095268,-0.01188301,0.05370729,-0.00970792,-0.01938351,0.01484639,-0.00980488,-0.00705865,-0.01905262,0.06166421,0.00949685,-0.01602596,-0.07215837,0.01477972,-0.0078226,0.02581574,-0.04179736,0.09665338,-0.00880869,0.05644532,0.04480446,0.2375073,0.02216586,0.03203794,0.00782025,0.00620029,0.03606614,0.00609048,0.05488323,0.00858829,0.03331176,0.0580431,-0.00465558,0.00172403,0.0422796,-0.0114977,-0.00950259,-0.00500888,0.01944892,-0.05878836,0.01455678,-0.04482132,0.03130815,0.07129172,0.01320328,-0.01485035,-0.09903423,0.01509102,0.0650188,-0.06555057,-0.04149499,-0.08011963,-0.05124715,0.03263536,0.02037023,0.05516912,-0.03541702,0.01284318,-0.02856718,0.01849758,-0.04424477,0.07423561,-0.01028913,-0.01783744],"last_embed":{"hash":"1m22jsz","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423549689},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}","lines":[300,300],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07097729,-0.00762121,-0.01975004,-0.06211267,-0.00172262,0.04416967,0.00482725,-0.03401824,0.04255343,-0.02558254,0.03002393,0.00019633,0.07518738,-0.02262446,-0.03994393,-0.00851757,-0.01264824,0.01433051,-0.02462237,-0.0270371,0.04358133,-0.02000508,-0.03663023,-0.09868716,0.05183725,0.00053097,-0.08449797,-0.06921318,-0.06320792,-0.18608283,0.03469164,-0.02021863,0.00384493,-0.08417737,-0.06410166,-0.02332568,-0.044612,0.02968594,-0.07300758,0.01991938,0.03817713,-0.00345538,-0.03413291,-0.01786341,-0.01429486,-0.04623917,0.04125703,-0.01491812,0.03331554,0.01686546,-0.11903682,0.0067408,0.02118185,0.02012724,0.02616628,0.04564255,0.00732196,0.09711958,0.00299826,0.07202599,0.04562982,0.05439144,-0.16610819,0.02895077,0.04518245,0.03789338,0.00551111,-0.02791929,-0.01981281,0.03691446,-0.00114764,0.03411299,0.00640638,0.04759051,0.03827126,-0.01886341,-0.07536011,-0.05715731,-0.07040779,0.02530183,-0.05484713,0.00597084,0.00964669,0.05758373,-0.03350257,0.02771029,0.0616526,0.03069832,0.09864115,-0.04413595,0.00488165,0.09696116,-0.00508632,0.0459592,0.0080254,0.00672739,0.04443725,-0.03329821,0.0115531,0.16942705,-0.0296345,-0.03559271,-0.01365842,0.01580071,0.00416678,-0.02816631,0.0228527,-0.00099686,-0.02363463,0.02482004,0.03776894,-0.00927109,0.00857471,-0.04102982,-0.07129098,-0.02973852,-0.0058016,-0.01218795,-0.01116862,0.01519969,-0.06922176,0.00639955,0.0144729,-0.03270571,0.01591092,0.00441222,0.03448023,0.06024195,-0.00205735,0.01485385,0.03369071,-0.02336655,-0.1514575,-0.03401867,-0.00069866,-0.01164045,0.02226607,-0.00519875,-0.02445706,-0.00231291,-0.02618061,0.03401942,0.08093598,-0.11947053,-0.04496406,0.05532892,-0.01329147,-0.03547971,-0.01086422,-0.01763261,0.05837993,0.00710429,-0.02722819,-0.038256,0.00471208,-0.01061475,0.1647992,0.04728868,-0.00711686,0.01563838,-0.02897658,-0.02740374,0.01408617,0.13719465,-0.00216925,-0.10196958,0.01281068,0.0461487,-0.03810747,-0.10562358,-0.00421906,0.00799233,-0.07954747,0.00439732,0.03661017,-0.01537699,-0.07575198,-0.04230518,0.01187799,-0.02111484,0.00731305,-0.00842446,0.00248837,0.00144707,-0.03936139,-0.09793353,-0.00291551,0.01836446,-0.01583111,0.02780515,-0.00432692,-0.0016278,-0.02970468,0.0623524,-0.01448256,-0.05210756,-0.05417801,-0.08956227,0.10756264,-0.04567813,-0.01248182,-0.00167609,0.02641238,0.00918939,0.03185967,0.02749339,-0.03258088,-0.04299857,0.08048769,0.04581825,-0.06898873,0.03047763,0.04832792,0.04711658,-0.05605154,0.07334025,0.00933938,-0.01361546,0.01590772,0.01121386,0.00822417,0.0331868,-0.05069423,-0.20921451,-0.04637322,-0.05783184,-0.00793394,0.00675998,-0.02676005,0.0551132,-0.03169542,0.0520553,0.10947289,0.08115256,-0.04782801,0.01824692,0.04171512,-0.01902425,-0.0187996,-0.06427987,-0.05219736,-0.07697291,0.04666271,0.02293294,0.02457995,0.00730813,-0.06154683,0.04053793,-0.0457879,0.14624132,0.01616357,0.03386408,0.00020912,0.07818893,-0.00749243,0.02822977,-0.06701427,0.01686493,0.07924663,-0.00699933,-0.01317108,-0.06495024,0.02814188,-0.11458021,0.04868547,0.01152398,-0.08195357,-0.00060388,0.01665212,0.02967233,-0.01001669,0.00426518,0.02119524,0.0257974,-0.03232307,-0.00681797,0.03239201,0.06184342,0.00209756,-0.03751357,0.05401463,-0.00518876,-0.00930285,-0.0375244,-0.08506015,0.03769357,-0.00975438,0.01502455,0.02537443,0.01997345,-0.03083679,0.01996915,-0.01781454,0.01691834,0.06857664,-0.01382854,0.04907319,0.02124709,0.06131255,0.0923067,-0.06479174,-0.00081816,-0.00303658,0.00116928,0.01159136,0.04235486,0.03836155,0.0523359,0.01121545,0.04073482,0.04895333,0.00548739,-0.03257127,0.01427478,0.00938892,-0.02084344,0.00041675,0.05771193,0.02431972,-0.24287872,0.02075535,-0.01107544,0.05270928,-0.0020235,-0.00171093,0.00720909,-0.02417902,0.00515217,-0.00299381,0.06696909,0.00635729,-0.01382564,-0.08085644,-0.00623266,-0.01700977,0.05558321,-0.0516253,0.08394796,-0.00740194,0.05483894,0.03987317,0.23962915,0.02682139,0.0190126,0.01355824,0.00777696,0.02565873,0.00753117,0.04562331,0.0112511,0.02646415,0.07280304,-0.01665919,-0.00594736,0.01788344,-0.01485642,0.00650981,0.02724464,0.01616261,-0.04231393,0.02273924,-0.05906201,-0.01706737,0.0707171,0.03909826,-0.01652054,-0.09239756,0.0053225,0.054117,-0.04784334,-0.06215658,-0.09533812,-0.0495686,0.02362249,0.01219144,0.04208126,-0.01565313,0.02616444,-0.01347634,0.04958855,-0.03592305,0.06523971,0.00835824,0.00120534],"last_embed":{"hash":"1sdoqa9","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"1sdoqa9","at":1753423549736},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{4}","lines":[302,302],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08854661,-0.01973606,-0.01763303,0.00084634,0.02364537,0.04878049,-0.00465686,-0.0256613,0.04045047,-0.01801004,-0.01666572,-0.01256999,0.04406206,-0.01789879,-0.01288785,0.00402755,-0.01000375,-0.01536573,-0.02916691,0.03279206,0.04888453,-0.00985776,-0.00632154,-0.07923,0.03969208,-0.00305344,-0.06065328,-0.06339309,-0.05639785,-0.20631807,0.01621099,-0.04115264,0.02052366,-0.09870219,-0.09033956,-0.03008411,-0.0398083,0.05753342,-0.06368886,0.03163303,0.02704337,-0.02160613,-0.02837086,-0.05542886,-0.02094421,-0.03427773,0.01367164,-0.02951222,0.0339129,0.02400432,-0.11292479,0.00529465,-0.01087844,0.01109244,0.04791866,0.06510981,0.02087137,0.09917949,-0.02482293,0.07525028,0.03897741,0.06212303,-0.17309751,0.02005733,0.02836003,0.03151974,-0.00943111,-0.01199585,-0.01704553,0.07041344,0.00675805,0.05682921,0.00580607,0.04333458,0.01135484,-0.00213898,-0.04392058,-0.04651276,-0.06426875,0.01427382,-0.06485187,-0.01707827,-0.03800431,0.02481336,0.00559517,0.02497029,0.04734294,0.05064701,0.06543823,-0.0518311,-0.00190517,0.07347081,-0.01337934,0.04834339,0.00842317,0.03743791,0.02358912,-0.0004743,-0.01494758,0.14221261,0.00072722,-0.0262604,0.00513963,0.05351609,0.05201751,-0.01899163,0.01417274,-0.0053949,-0.02419483,0.02990722,0.06605347,-0.02067987,0.0355491,-0.0532972,-0.06490423,0.01388749,0.02405729,0.00127699,0.0105673,0.0023739,-0.08233782,0.05476091,0.0351917,-0.0577526,0.03095929,0.00173224,0.01549918,0.06651708,0.03580169,0.02419282,-0.00343005,-0.02246495,-0.15034086,-0.04407709,-0.0452603,-0.02831838,0.01046375,0.02692629,-0.01766943,-0.02348584,-0.00972832,-0.02178257,0.06517146,-0.11731976,-0.06503785,0.03250602,-0.03338929,-0.01936382,0.01194976,-0.00130489,0.01892192,0.02181903,-0.00782417,-0.04715512,-0.00493263,-0.00725664,0.13685773,0.06444134,-0.01460204,0.02099197,-0.04094285,-0.02165703,0.01094093,0.10648031,0.03077937,-0.07262549,-0.00955952,0.04004875,-0.0177037,-0.0979491,-0.0371888,0.00585844,-0.05941553,0.00778882,0.01266904,-0.03252609,-0.10820024,-0.04732091,0.00587183,0.00070928,-0.02693075,0.02221948,0.0035084,-0.00210119,-0.03124848,-0.10214488,0.01090246,-0.00732395,0.00619136,0.01824299,-0.01420131,-0.04479111,-0.006929,0.10844757,-0.0318809,-0.03404614,-0.05795848,-0.09281522,0.10017429,0.0087902,-0.0500594,-0.01475797,0.00934448,-0.01356439,-0.02011305,0.04713792,-0.00101033,-0.05332863,0.07216834,0.00899556,-0.05577102,0.04200692,0.06668206,0.0509243,-0.06589062,0.04856335,0.0112644,0.011488,-0.00022756,0.00506648,-0.00088622,0.01932218,-0.06899195,-0.21664429,-0.03975485,-0.05600543,-0.02015694,0.02590199,-0.04032634,0.04969778,-0.01136037,0.08509238,0.10046712,0.06640698,-0.03281423,0.02285855,0.02163884,-0.00551297,0.00466182,-0.07223941,-0.02631166,-0.04589676,0.06066643,0.01249832,0.05160979,0.02090689,-0.04979424,0.05804921,-0.04814507,0.15319911,0.05860627,0.02272202,-0.00629084,0.05615764,0.02912346,0.01683607,-0.05754906,0.0600303,0.07149883,-0.01748842,-0.02834188,-0.08691099,-0.00403304,-0.11108062,0.04526946,-0.01497505,-0.071786,-0.03152559,0.0146513,-0.01335325,-0.01640607,-0.01921639,0.00672402,0.05872826,-0.03311965,0.00278788,-0.00449806,0.07511687,0.01159976,-0.05615076,0.02868214,-0.00304435,-0.00674214,-0.00716345,-0.03842043,0.03255326,-0.02177205,0.00608075,0.0242293,0.04158535,-0.02222741,0.01403614,0.00143436,0.00668219,0.05431574,-0.0169204,0.06376902,0.04814242,0.02480736,0.03661017,-0.08785062,0.01111476,0.00535644,0.0285627,0.01972225,0.04959341,0.06101543,0.06570027,-0.02498897,0.01987316,0.04569609,0.03670244,-0.04266392,0.00525453,0.04479346,-0.01755267,0.00525469,0.0738177,0.03349878,-0.25740504,-0.00043603,-0.00496951,0.02354719,-0.00289367,-0.00154422,0.01637129,-0.0542216,-0.02070178,-0.01725949,0.08895022,0.01486641,-0.02518817,-0.06033629,-0.00983613,-0.02254964,-0.00979371,-0.04099493,0.09245584,-0.03199412,0.04095798,0.04765324,0.24799983,0.03647291,0.00938501,0.00158212,0.00977142,0.02811981,0.00451196,0.02733732,-0.00894733,0.05068299,0.08861312,-0.01333582,0.0063629,0.05757499,-0.01712199,-0.01753866,0.00744167,-0.01536515,-0.04181685,0.03197153,-0.02013389,0.02716732,0.09270789,-0.00765227,-0.00030681,-0.06702771,0.0351827,0.07298698,-0.05684322,-0.05161872,-0.06293513,-0.02216105,0.02476849,0.03221964,0.03796771,-0.0315977,0.0197175,-0.00624185,0.03242472,-0.02083564,0.057483,0.00914852,0.00203533],"last_embed":{"hash":"hnk07o","tokens":349}}},"text":null,"length":0,"last_read":{"hash":"hnk07o","at":1753423549780},"key":"notes/saliu/Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD.md#Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{18}","lines":[318,325],"size":790,"outlinks":[{"title":"Lottery Wonder Grid reports a strategy to beat random play.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"Lotto systems of pairs beat random expectation consistently by 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":3},{"title":"Forums","target":"https://forums.saliu.com/","line":5},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":5},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":5},{"title":"Contents","target":"https://saliu.com/content/index.html","line":5},{"title":"Home","target":"https://saliu.com/index.htm","line":5},{"title":"Software","target":"https://saliu.com/infodown.html","line":5},{"title":"Search","target":"https://saliu.com/Search.htm","line":5},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":5},{"title":"The wonder grid lotto strategy beats lottery random picks by better than 3 standard deviations.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":7}],"class_name":"SmartBlock"},
