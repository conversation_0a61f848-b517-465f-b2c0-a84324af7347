"smart_sources:PromptingTools.jl/Reference - PromptingTools.jl.md": {"path":"PromptingTools.jl/Reference - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11820896,0.00617443,-0.03460325,-0.05641861,-0.027842,0.02345408,-0.02035932,0.04863997,0.04700553,-0.0239736,-0.01557816,-0.00213386,-0.00570505,0.04124679,-0.02137915,0.02215011,-0.04207469,0.03058766,-0.06960075,-0.02602791,0.08507519,0.05129442,0.01501077,-0.03433119,0.02575901,0.05383169,0.00954975,-0.03990134,-0.0070478,-0.19015816,0.04031398,-0.01995142,-0.00301429,0.00436979,-0.0499601,-0.01541113,-0.04948184,0.04621572,-0.0097008,0.00205644,0.03943062,0.01430665,-0.02405236,0.00017308,-0.06015204,-0.09505841,-0.01469496,-0.01703018,-0.00367119,-0.06003342,-0.01346848,-0.03014296,0.05305655,-0.01989638,0.00703314,0.01874053,0.01365828,0.09248819,0.02075248,0.01152921,0.03323003,0.0164219,-0.1951797,0.15815006,0.00640949,0.01697441,-0.00531088,0.00718612,0.03183613,0.03623186,-0.03605425,0.04108704,-0.01039777,0.07228418,0.01807877,-0.03310375,-0.02076669,-0.02987204,-0.0137656,-0.09974706,-0.03654423,0.00120719,-0.01649534,-0.01191582,-0.04375565,0.01926104,0.01867428,-0.01618772,0.0751114,0.00173633,0.00610236,-0.09314967,-0.00057746,0.0313988,-0.0567623,-0.0156479,0.03290559,-0.03162934,-0.09592458,0.12669307,-0.02841526,-0.01843976,0.01644175,-0.01133119,0.03216013,-0.05959176,0.0136132,-0.02730217,-0.02923717,0.03437456,-0.00899362,-0.03691651,-0.00091993,-0.06788942,-0.00176805,-0.02505059,-0.00412947,-0.01458605,0.01282774,-0.06082807,0.01949376,0.01722092,0.01977411,0.00440875,-0.00102415,0.04011191,0.02435422,0.03764596,-0.00576966,0.01561101,0.05503584,0.01515264,-0.06617777,-0.03441659,-0.00302828,0.0571716,0.04974201,-0.07130837,0.03030857,-0.00837438,0.03306112,-0.00960969,0.00207722,-0.10926114,-0.00014243,0.05084939,-0.03367003,-0.02398838,-0.01882756,-0.04155556,0.03932296,0.05052856,-0.05628737,0.01141369,0.0544901,0.04019567,0.07585116,0.08234566,0.00268013,0.02428564,-0.05220734,-0.03674564,-0.00937573,0.13271175,0.01689229,-0.05600108,-0.09509172,-0.03510571,-0.04597417,-0.04085038,0.01092538,0.05511249,-0.03624746,-0.01347251,0.07484819,0.04524248,-0.05776722,0.03552748,0.01021513,0.02537206,0.02254931,-0.07247923,-0.04951705,0.02236729,-0.02287582,-0.01347094,-0.0300843,-0.08933718,-0.00369941,0.06184031,-0.07017911,0.02617982,0.04874174,-0.03004191,-0.00122581,0.00138155,-0.01231568,-0.06289124,0.07226817,-0.04383856,0.06594714,0.04401417,0.0276943,0.02850278,-0.00815733,0.05788388,0.05263096,-0.01395372,0.10456736,0.03981219,-0.10045581,-0.05834033,0.05830277,0.00386648,-0.01743532,-0.02120017,-0.0491842,0.01470915,0.00870812,0.06125182,-0.02251203,-0.00074517,-0.04172484,-0.23355971,0.02330982,0.02275253,-0.051916,0.05150965,-0.08186769,0.1048205,-0.04644165,-0.05063852,0.04205638,0.09462142,-0.03326679,0.04817263,-0.00556405,-0.02810455,-0.01455787,-0.03557956,0.00834359,-0.00461247,0.0385843,0.00884309,0.00210467,0.02450255,-0.08341601,0.00479874,-0.02372353,0.10604004,0.03311462,0.03821811,-0.08666763,0.01974957,-0.00515887,0.06477844,-0.12814473,-0.01915967,0.03600246,-0.0383917,0.05860169,0.10113664,0.01678916,-0.02572919,0.04161225,-0.00491687,-0.02719483,-0.00811475,-0.01895791,-0.05428958,-0.0582692,-0.03503796,0.02317279,0.01559516,-0.04548445,0.01003403,-0.0070646,0.01641381,-0.01442651,-0.02729081,-0.06136774,0.02047329,0.00371839,0.01639906,-0.01850496,-0.00009013,-0.05394811,0.05142608,0.05029948,0.01835096,0.00321941,0.05840553,0.01483596,-0.01802664,0.0907021,0.04040731,0.07386939,-0.00595676,0.06217024,-0.01129776,-0.05793096,-0.01906966,-0.04922665,-0.04538137,0.0249045,0.00430025,0.03361114,0.02607589,0.05533161,0.04078004,-0.03611119,0.08439172,-0.05440083,-0.02416074,0.01431359,0.01356606,-0.04150827,0.04303876,0.02186008,-0.24712078,0.02747456,0.01428332,0.0182478,-0.01891745,0.08323166,0.04666533,0.00176478,0.00259679,0.05480154,-0.03462122,-0.00493285,0.01484082,-0.02277992,0.04395254,0.05147349,0.03618974,-0.00985591,0.02471299,-0.06947955,0.03655102,0.01722419,0.23952015,-0.02700223,0.00826626,-0.00778995,0.00643135,-0.05390666,0.08653723,0.04433108,0.01324575,0.05671244,0.15223902,-0.01385957,0.04211841,0.0044279,-0.05051615,-0.01058093,0.02606564,0.01434828,-0.02441244,0.04529005,-0.05289149,-0.0070681,0.05601163,-0.00895456,-0.0129215,-0.07878988,-0.06149871,0.02684654,-0.01615255,0.00260116,-0.02698741,-0.01575681,0.00649699,-0.01672131,0.05694328,0.00371169,-0.05894567,-0.01265202,0.07409796,-0.0090018,0.08686836,-0.00681718,-0.00308253],"last_embed":{"hash":"a0f6690f1833cb210cf20858b8d154ab32b95efbc8b8390b1ecb1ff43b162f9a","tokens":463}}},"last_read":{"hash":"a0f6690f1833cb210cf20858b8d154ab32b95efbc8b8390b1ecb1ff43b162f9a","at":1745995224962},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/reference/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"​","target":"#Reference","line":243},{"title":"`PromptingTools.Experimental`","target":"#PromptingTools.Experimental","line":245},{"title":"`PromptingTools.Experimental.AgentTools`","target":"#PromptingTools.Experimental.AgentTools","line":246},{"title":"`PromptingTools.Experimental.RAGTools`","target":"#PromptingTools.Experimental.RAGTools","line":247},{"title":"`PromptingTools.ALLOWED_PREFERENCES`","target":"#PromptingTools.ALLOWED_PREFERENCES","line":248},{"title":"`PromptingTools.ALTERNATIVE_GENERATION_COSTS`","target":"#PromptingTools.ALTERNATIVE_GENERATION_COSTS","line":249},{"title":"`PromptingTools.ANTHROPIC_TOOL_PROMPT`","target":"#PromptingTools.ANTHROPIC_TOOL_PROMPT","line":250},{"title":"`PromptingTools.CONV_HISTORY`","target":"#PromptingTools.CONV_HISTORY","line":251},{"title":"`PromptingTools.MODEL_ALIASES`","target":"#PromptingTools.MODEL_ALIASES","line":252},{"title":"`PromptingTools.MODEL_REGISTRY`","target":"#PromptingTools.MODEL_REGISTRY","line":253},{"title":"`PromptingTools.OPENAI_TOKEN_IDS`","target":"#PromptingTools.OPENAI_TOKEN_IDS","line":254},{"title":"`PromptingTools.PREFERENCES`","target":"#PromptingTools.PREFERENCES","line":255},{"title":"`PromptingTools.RESERVED_KWARGS`","target":"#PromptingTools.RESERVED_KWARGS","line":256},{"title":"`PromptingTools.AICode`","target":"#PromptingTools.AICode","line":257},{"title":"`PromptingTools.AIMessage`","target":"#PromptingTools.AIMessage","line":258},{"title":"`PromptingTools.AITemplate`","target":"#PromptingTools.AITemplate","line":259},{"title":"`PromptingTools.AITemplateMetadata`","target":"#PromptingTools.AITemplateMetadata","line":260},{"title":"`PromptingTools.AbstractPromptSchema`","target":"#PromptingTools.AbstractPromptSchema","line":261},{"title":"`PromptingTools.AnthropicSchema`","target":"#PromptingTools.AnthropicSchema","line":262},{"title":"`PromptingTools.ChatMLSchema`","target":"#PromptingTools.ChatMLSchema","line":263},{"title":"`PromptingTools.CustomOpenAISchema`","target":"#PromptingTools.CustomOpenAISchema","line":264},{"title":"`PromptingTools.DataMessage`","target":"#PromptingTools.DataMessage","line":265},{"title":"`PromptingTools.DatabricksOpenAISchema`","target":"#PromptingTools.DatabricksOpenAISchema","line":266},{"title":"`PromptingTools.Experimental.AgentTools.AICall`","target":"#PromptingTools.Experimental.AgentTools.AICall","line":267},{"title":"`PromptingTools.Experimental.AgentTools.AICodeFixer`","target":"#PromptingTools.Experimental.AgentTools.AICodeFixer","line":268},{"title":"`PromptingTools.Experimental.AgentTools.RetryConfig`","target":"#PromptingTools.Experimental.AgentTools.RetryConfig","line":269},{"title":"`PromptingTools.Experimental.AgentTools.SampleNode`","target":"#PromptingTools.Experimental.AgentTools.SampleNode","line":270},{"title":"`PromptingTools.Experimental.AgentTools.ThompsonSampling`","target":"#PromptingTools.Experimental.AgentTools.ThompsonSampling","line":271},{"title":"`PromptingTools.Experimental.AgentTools.UCT`","target":"#PromptingTools.Experimental.AgentTools.UCT","line":272},{"title":"`PromptingTools.Experimental.RAGTools.AbstractCandidateChunks`","target":"#PromptingTools.Experimental.RAGTools.AbstractCandidateChunks","line":273},{"title":"`PromptingTools.Experimental.RAGTools.AbstractChunkIndex`","target":"#PromptingTools.Experimental.RAGTools.AbstractChunkIndex","line":274},{"title":"`PromptingTools.Experimental.RAGTools.AbstractGenerator`","target":"#PromptingTools.Experimental.RAGTools.AbstractGenerator","line":275},{"title":"`PromptingTools.Experimental.RAGTools.AbstractIndexBuilder`","target":"#PromptingTools.Experimental.RAGTools.AbstractIndexBuilder","line":276},{"title":"`PromptingTools.Experimental.RAGTools.AbstractMultiIndex`","target":"#PromptingTools.Experimental.RAGTools.AbstractMultiIndex","line":277},{"title":"`PromptingTools.Experimental.RAGTools.AbstractRetriever`","target":"#PromptingTools.Experimental.RAGTools.AbstractRetriever","line":278},{"title":"`PromptingTools.Experimental.RAGTools.AdvancedGenerator`","target":"#PromptingTools.Experimental.RAGTools.AdvancedGenerator","line":279},{"title":"`PromptingTools.Experimental.RAGTools.AdvancedRetriever`","target":"#PromptingTools.Experimental.RAGTools.AdvancedRetriever","line":280},{"title":"`PromptingTools.Experimental.RAGTools.AnnotatedNode`","target":"#PromptingTools.Experimental.RAGTools.AnnotatedNode","line":281},{"title":"`PromptingTools.Experimental.RAGTools.AnyTagFilter`","target":"#PromptingTools.Experimental.RAGTools.AnyTagFilter","line":282},{"title":"`PromptingTools.Experimental.RAGTools.BatchEmbedder`","target":"#PromptingTools.Experimental.RAGTools.BatchEmbedder","line":283},{"title":"`PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity`","target":"#PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity","line":284},{"title":"`PromptingTools.Experimental.RAGTools.CandidateChunks`","target":"#PromptingTools.Experimental.RAGTools.CandidateChunks","line":285},{"title":"`PromptingTools.Experimental.RAGTools.ChunkIndex`","target":"#PromptingTools.Experimental.RAGTools.ChunkIndex","line":286},{"title":"`PromptingTools.Experimental.RAGTools.CohereReranker`","target":"#PromptingTools.Experimental.RAGTools.CohereReranker","line":287},{"title":"`PromptingTools.Experimental.RAGTools.ContextEnumerator`","target":"#PromptingTools.Experimental.RAGTools.ContextEnumerator","line":288},{"title":"`PromptingTools.Experimental.RAGTools.CosineSimilarity`","target":"#PromptingTools.Experimental.RAGTools.CosineSimilarity","line":289},{"title":"`PromptingTools.Experimental.RAGTools.FileChunker`","target":"#PromptingTools.Experimental.RAGTools.FileChunker","line":290},{"title":"`PromptingTools.Experimental.RAGTools.HTMLStyler`","target":"#PromptingTools.Experimental.RAGTools.HTMLStyler","line":291},{"title":"`PromptingTools.Experimental.RAGTools.HyDERephraser`","target":"#PromptingTools.Experimental.RAGTools.HyDERephraser","line":292},{"title":"`PromptingTools.Experimental.RAGTools.JudgeAllScores`","target":"#PromptingTools.Experimental.RAGTools.JudgeAllScores","line":293},{"title":"`PromptingTools.Experimental.RAGTools.JudgeRating`","target":"#PromptingTools.Experimental.RAGTools.JudgeRating","line":294},{"title":"`PromptingTools.Experimental.RAGTools.MultiIndex`","target":"#PromptingTools.Experimental.RAGTools.MultiIndex","line":295},{"title":"`PromptingTools.Experimental.RAGTools.NoPostprocessor`","target":"#PromptingTools.Experimental.RAGTools.NoPostprocessor","line":296},{"title":"`PromptingTools.Experimental.RAGTools.NoRefiner`","target":"#PromptingTools.Experimental.RAGTools.NoRefiner","line":297},{"title":"`PromptingTools.Experimental.RAGTools.NoRephraser`","target":"#PromptingTools.Experimental.RAGTools.NoRephraser","line":298},{"title":"`PromptingTools.Experimental.RAGTools.NoReranker`","target":"#PromptingTools.Experimental.RAGTools.NoReranker","line":299},{"title":"`PromptingTools.Experimental.RAGTools.NoTagFilter`","target":"#PromptingTools.Experimental.RAGTools.NoTagFilter","line":300},{"title":"`PromptingTools.Experimental.RAGTools.NoTagger`","target":"#PromptingTools.Experimental.RAGTools.NoTagger","line":301},{"title":"`PromptingTools.Experimental.RAGTools.OpenTagger`","target":"#PromptingTools.Experimental.RAGTools.OpenTagger","line":302},{"title":"`PromptingTools.Experimental.RAGTools.PassthroughTagger`","target":"#PromptingTools.Experimental.RAGTools.PassthroughTagger","line":303},{"title":"`PromptingTools.Experimental.RAGTools.RAGConfig`","target":"#PromptingTools.Experimental.RAGTools.RAGConfig","line":304},{"title":"`PromptingTools.Experimental.RAGTools.RAGResult`","target":"#PromptingTools.Experimental.RAGTools.RAGResult","line":305},{"title":"`PromptingTools.Experimental.RAGTools.SimpleAnswerer`","target":"#PromptingTools.Experimental.RAGTools.SimpleAnswerer","line":306},{"title":"`PromptingTools.Experimental.RAGTools.SimpleGenerator`","target":"#PromptingTools.Experimental.RAGTools.SimpleGenerator","line":307},{"title":"`PromptingTools.Experimental.RAGTools.SimpleIndexer`","target":"#PromptingTools.Experimental.RAGTools.SimpleIndexer","line":308},{"title":"`PromptingTools.Experimental.RAGTools.SimpleRefiner`","target":"#PromptingTools.Experimental.RAGTools.SimpleRefiner","line":309},{"title":"`PromptingTools.Experimental.RAGTools.SimpleRephraser`","target":"#PromptingTools.Experimental.RAGTools.SimpleRephraser","line":310},{"title":"`PromptingTools.Experimental.RAGTools.SimpleRetriever`","target":"#PromptingTools.Experimental.RAGTools.SimpleRetriever","line":311},{"title":"`PromptingTools.Experimental.RAGTools.Styler`","target":"#PromptingTools.Experimental.RAGTools.Styler","line":312},{"title":"`PromptingTools.Experimental.RAGTools.TextChunker`","target":"#PromptingTools.Experimental.RAGTools.TextChunker","line":313},{"title":"`PromptingTools.Experimental.RAGTools.TrigramAnnotater`","target":"#PromptingTools.Experimental.RAGTools.TrigramAnnotater","line":314},{"title":"`PromptingTools.FireworksOpenAISchema`","target":"#PromptingTools.FireworksOpenAISchema","line":315},{"title":"`PromptingTools.GoogleSchema`","target":"#PromptingTools.GoogleSchema","line":316},{"title":"`PromptingTools.ItemsExtract`","target":"#PromptingTools.ItemsExtract","line":317},{"title":"`PromptingTools.LocalServerOpenAISchema`","target":"#PromptingTools.LocalServerOpenAISchema","line":318},{"title":"`PromptingTools.MaybeExtract`","target":"#PromptingTools.MaybeExtract","line":319},{"title":"`PromptingTools.MistralOpenAISchema`","target":"#PromptingTools.MistralOpenAISchema","line":320},{"title":"`PromptingTools.ModelSpec`","target":"#PromptingTools.ModelSpec","line":321},{"title":"`PromptingTools.NoSchema`","target":"#PromptingTools.NoSchema","line":322},{"title":"`PromptingTools.OllamaManagedSchema`","target":"#PromptingTools.OllamaManagedSchema","line":323},{"title":"`PromptingTools.OllamaSchema`","target":"#PromptingTools.OllamaSchema","line":324},{"title":"`PromptingTools.OpenAISchema`","target":"#PromptingTools.OpenAISchema","line":325},{"title":"`PromptingTools.ShareGPTSchema`","target":"#PromptingTools.ShareGPTSchema","line":326},{"title":"`PromptingTools.TestEchoAnthropicSchema`","target":"#PromptingTools.TestEchoAnthropicSchema","line":327},{"title":"`PromptingTools.TestEchoGoogleSchema`","target":"#PromptingTools.TestEchoGoogleSchema","line":328},{"title":"`PromptingTools.TestEchoOllamaManagedSchema`","target":"#PromptingTools.TestEchoOllamaManagedSchema","line":329},{"title":"`PromptingTools.TestEchoOllamaSchema`","target":"#PromptingTools.TestEchoOllamaSchema","line":330},{"title":"`PromptingTools.TestEchoOpenAISchema`","target":"#PromptingTools.TestEchoOpenAISchema","line":331},{"title":"`PromptingTools.TogetherOpenAISchema`","target":"#PromptingTools.TogetherOpenAISchema","line":332},{"title":"`PromptingTools.UserMessageWithImages`","target":"#PromptingTools.UserMessageWithImages-Tuple{AbstractString}","line":333},{"title":"`PromptingTools.X123`","target":"#PromptingTools.X123","line":334},{"title":"`OpenAI.create_chat`","target":"#OpenAI.create_chat-Tuple{PromptingTools.MistralOpenAISchema, AbstractString, AbstractString, Any}","line":335},{"title":"`OpenAI.create_chat`","target":"#OpenAI.create_chat-Tuple{PromptingTools.CustomOpenAISchema, AbstractString, AbstractString, Any}","line":336},{"title":"`OpenAI.create_chat`","target":"#OpenAI.create_chat-Tuple{PromptingTools.LocalServerOpenAISchema, AbstractString, AbstractString, Any}","line":337},{"title":"`PromptingTools.Experimental.APITools.create_websearch`","target":"#PromptingTools.Experimental.APITools.create_websearch-Tuple{AbstractString}","line":338},{"title":"`PromptingTools.Experimental.APITools.tavily_api`","target":"#PromptingTools.Experimental.APITools.tavily_api-Tuple{}","line":339},{"title":"`PromptingTools.Experimental.AgentTools.AIClassify`","target":"#PromptingTools.Experimental.AgentTools.AIClassify-Tuple","line":340},{"title":"`PromptingTools.Experimental.AgentTools.AIEmbed`","target":"#PromptingTools.Experimental.AgentTools.AIEmbed-Tuple","line":341},{"title":"`PromptingTools.Experimental.AgentTools.AIExtract`","target":"#PromptingTools.Experimental.AgentTools.AIExtract-Tuple","line":342},{"title":"`PromptingTools.Experimental.AgentTools.AIGenerate`","target":"#PromptingTools.Experimental.AgentTools.AIGenerate-Tuple","line":343},{"title":"`PromptingTools.Experimental.AgentTools.AIScan`","target":"#PromptingTools.Experimental.AgentTools.AIScan-Tuple","line":344},{"title":"`PromptingTools.Experimental.AgentTools.add_feedback!`","target":"#PromptingTools.Experimental.AgentTools.add_feedback!-Tuple{AbstractVector{<:PromptingTools.AbstractMessage}, PromptingTools.Experimental.AgentTools.SampleNode}","line":345},{"title":"`PromptingTools.Experimental.AgentTools.aicodefixer_feedback`","target":"#PromptingTools.Experimental.AgentTools.aicodefixer_feedback-Tuple{AbstractVector{<:PromptingTools.AbstractMessage}}","line":346},{"title":"`PromptingTools.Experimental.AgentTools.airetry!`","target":"#PromptingTools.Experimental.AgentTools.airetry!","line":347},{"title":"`PromptingTools.Experimental.AgentTools.backpropagate!`","target":"#PromptingTools.Experimental.AgentTools.backpropagate!-Tuple{PromptingTools.Experimental.AgentTools.SampleNode}","line":348},{"title":"`PromptingTools.Experimental.AgentTools.beta_sample`","target":"#PromptingTools.Experimental.AgentTools.beta_sample-Tuple{Real, Real}","line":349},{"title":"`PromptingTools.Experimental.AgentTools.collect_all_feedback`","target":"#PromptingTools.Experimental.AgentTools.collect_all_feedback-Tuple{PromptingTools.Experimental.AgentTools.SampleNode}","line":350},{"title":"`PromptingTools.Experimental.AgentTools.error_feedback`","target":"#PromptingTools.Experimental.AgentTools.error_feedback-Tuple{Any}","line":351},{"title":"`PromptingTools.Experimental.AgentTools.evaluate_condition!`","target":"#PromptingTools.Experimental.AgentTools.evaluate_condition!","line":352},{"title":"`PromptingTools.Experimental.AgentTools.expand!`","target":"#PromptingTools.Experimental.AgentTools.expand!-Tuple{PromptingTools.Experimental.AgentTools.SampleNode, Any}","line":353},{"title":"`PromptingTools.Experimental.AgentTools.extract_config`","target":"#PromptingTools.Experimental.AgentTools.extract_config-Union{Tuple{T}, Tuple{Any, T}} where T","line":354},{"title":"`PromptingTools.Experimental.AgentTools.find_node`","target":"#PromptingTools.Experimental.AgentTools.find_node-Tuple{PromptingTools.Experimental.AgentTools.SampleNode, Integer}","line":355},{"title":"`PromptingTools.Experimental.AgentTools.gamma_sample`","target":"#PromptingTools.Experimental.AgentTools.gamma_sample-Tuple{Real, Real}","line":356},{"title":"`PromptingTools.Experimental.AgentTools.print_samples`","target":"#PromptingTools.Experimental.AgentTools.print_samples-Tuple{PromptingTools.Experimental.AgentTools.SampleNode}","line":357},{"title":"`PromptingTools.Experimental.AgentTools.remove_used_kwargs`","target":"#PromptingTools.Experimental.AgentTools.remove_used_kwargs-Tuple{NamedTuple, AbstractVector{<:PromptingTools.AbstractMessage}}","line":358},{"title":"`PromptingTools.Experimental.AgentTools.reset_success!`","target":"#PromptingTools.Experimental.AgentTools.reset_success!","line":359},{"title":"`PromptingTools.Experimental.AgentTools.run!`","target":"#PromptingTools.Experimental.AgentTools.run!-Tuple{AICodeFixer}","line":360},{"title":"`PromptingTools.Experimental.AgentTools.run!`","target":"#PromptingTools.Experimental.AgentTools.run!-Tuple{PromptingTools.Experimental.AgentTools.AICallBlock}","line":361},{"title":"`PromptingTools.Experimental.AgentTools.score`","target":"#PromptingTools.Experimental.AgentTools.score-Tuple{PromptingTools.Experimental.AgentTools.SampleNode, PromptingTools.Experimental.AgentTools.ThompsonSampling}","line":362},{"title":"`PromptingTools.Experimental.AgentTools.score`","target":"#PromptingTools.Experimental.AgentTools.score-Tuple{PromptingTools.Experimental.AgentTools.SampleNode, PromptingTools.Experimental.AgentTools.UCT}","line":363},{"title":"`PromptingTools.Experimental.AgentTools.select_best`","target":"#PromptingTools.Experimental.AgentTools.select_best","line":364},{"title":"`PromptingTools.Experimental.AgentTools.split_multi_samples`","target":"#PromptingTools.Experimental.AgentTools.split_multi_samples-Tuple{Any}","line":365},{"title":"`PromptingTools.Experimental.AgentTools.truncate_conversation`","target":"#PromptingTools.Experimental.AgentTools.truncate_conversation-Tuple{AbstractVector{<:PromptingTools.AbstractMessage}}","line":366},{"title":"`PromptingTools.Experimental.AgentTools.unwrap_aicall_args`","target":"#PromptingTools.Experimental.AgentTools.unwrap_aicall_args-Tuple{Any}","line":367},{"title":"`PromptingTools.Experimental.RAGTools._normalize`","target":"#PromptingTools.Experimental.RAGTools._normalize","line":368},{"title":"`PromptingTools.Experimental.RAGTools.add_node_metadata!`","target":"#PromptingTools.Experimental.RAGTools.add_node_metadata!-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AnnotatedNode}","line":369},{"title":"`PromptingTools.Experimental.RAGTools.airag`","target":"#PromptingTools.Experimental.RAGTools.airag-Tuple{PromptingTools.Experimental.RAGTools.AbstractRAGConfig, PromptingTools.Experimental.RAGTools.AbstractChunkIndex}","line":370},{"title":"`PromptingTools.Experimental.RAGTools.align_node_styles!`","target":"#PromptingTools.Experimental.RAGTools.align_node_styles!-Tuple{TrigramAnnotater, AbstractVector{<:PromptingTools.Experimental.RAGTools.AnnotatedNode}}","line":371},{"title":"`PromptingTools.Experimental.RAGTools.annotate_support`","target":"#PromptingTools.Experimental.RAGTools.annotate_support-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":372},{"title":"`PromptingTools.Experimental.RAGTools.annotate_support`","target":"#PromptingTools.Experimental.RAGTools.annotate_support-Tuple{TrigramAnnotater, AbstractString, AbstractVector}","line":373},{"title":"`PromptingTools.Experimental.RAGTools.answer!`","target":"#PromptingTools.Experimental.RAGTools.answer!-Tuple{PromptingTools.Experimental.RAGTools.SimpleAnswerer, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":374},{"title":"`PromptingTools.Experimental.RAGTools.build_context`","target":"#PromptingTools.Experimental.RAGTools.build_context-Tuple{PromptingTools.Experimental.RAGTools.ContextEnumerator, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, CandidateChunks}","line":375},{"title":"`PromptingTools.Experimental.RAGTools.build_index`","target":"#PromptingTools.Experimental.RAGTools.build_index-Tuple{PromptingTools.Experimental.RAGTools.AbstractIndexBuilder, Vector{<:AbstractString}}","line":376},{"title":"`PromptingTools.Experimental.RAGTools.build_qa_evals`","target":"#PromptingTools.Experimental.RAGTools.build_qa_evals-Tuple{Vector{<:AbstractString}, Vector{<:AbstractString}}","line":377},{"title":"`PromptingTools.Experimental.RAGTools.build_tags`","target":"#PromptingTools.Experimental.RAGTools.build_tags","line":378},{"title":"`PromptingTools.Experimental.RAGTools.build_tags`","target":"#PromptingTools.Experimental.RAGTools.build_tags-Tuple{PromptingTools.Experimental.RAGTools.AbstractTagger, Nothing}","line":379},{"title":"`PromptingTools.Experimental.RAGTools.cohere_api`","target":"#PromptingTools.Experimental.RAGTools.cohere_api-Tuple{}","line":380},{"title":"`PromptingTools.Experimental.RAGTools.find_closest`","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity, AbstractMatrix{<:Bool}, AbstractVector{<:Real}}","line":381},{"title":"`PromptingTools.Experimental.RAGTools.find_closest`","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.AbstractSimilarityFinder, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractVector{<:Real}}","line":382},{"title":"`PromptingTools.Experimental.RAGTools.find_closest`","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.CosineSimilarity, AbstractMatrix{<:Real}, AbstractVector{<:Real}}","line":383},{"title":"`PromptingTools.Experimental.RAGTools.find_tags`","target":"#PromptingTools.Experimental.RAGTools.find_tags-Union{Tuple{T}, Tuple{PromptingTools.Experimental.RAGTools.NoTagFilter, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, Union{AbstractVector{<:T}, T}}} where T<:Union{Regex, AbstractString}","line":384},{"title":"`PromptingTools.Experimental.RAGTools.find_tags`","target":"#PromptingTools.Experimental.RAGTools.find_tags-Tuple{PromptingTools.Experimental.RAGTools.AnyTagFilter, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, Union{Regex, AbstractString}}","line":385},{"title":"`PromptingTools.Experimental.RAGTools.generate!`","target":"#PromptingTools.Experimental.RAGTools.generate!-Tuple{PromptingTools.Experimental.RAGTools.AbstractGenerator, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":386},{"title":"`PromptingTools.Experimental.RAGTools.get_chunks`","target":"#PromptingTools.Experimental.RAGTools.get_chunks-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunker, Vector{<:AbstractString}}","line":387},{"title":"`PromptingTools.Experimental.RAGTools.get_embeddings`","target":"#PromptingTools.Experimental.RAGTools.get_embeddings-Tuple{PromptingTools.Experimental.RAGTools.BatchEmbedder, AbstractVector{<:AbstractString}}","line":388},{"title":"`PromptingTools.Experimental.RAGTools.get_tags`","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.PassthroughTagger, AbstractVector{<:AbstractString}}","line":389},{"title":"`PromptingTools.Experimental.RAGTools.get_tags`","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.NoTagger, AbstractVector{<:AbstractString}}","line":390},{"title":"`PromptingTools.Experimental.RAGTools.get_tags`","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.OpenTagger, AbstractVector{<:AbstractString}}","line":391},{"title":"`PromptingTools.Experimental.RAGTools.hamming_distance`","target":"#PromptingTools.Experimental.RAGTools.hamming_distance-Tuple{AbstractMatrix{<:Bool}, AbstractVector{<:Bool}}","line":392},{"title":"`PromptingTools.Experimental.RAGTools.load_text`","target":"#PromptingTools.Experimental.RAGTools.load_text-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunker, Any}","line":393},{"title":"`PromptingTools.Experimental.RAGTools.print_html`","target":"#PromptingTools.Experimental.RAGTools.print_html-Tuple{IO, PromptingTools.Experimental.RAGTools.AbstractAnnotatedNode}","line":394},{"title":"`PromptingTools.Experimental.RAGTools.refine!`","target":"#PromptingTools.Experimental.RAGTools.refine!-Tuple{PromptingTools.Experimental.RAGTools.SimpleRefiner, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":395},{"title":"`PromptingTools.Experimental.RAGTools.refine!`","target":"#PromptingTools.Experimental.RAGTools.refine!-Tuple{PromptingTools.Experimental.RAGTools.NoRefiner, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":396},{"title":"`PromptingTools.Experimental.RAGTools.rephrase`","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.NoRephraser, AbstractString}","line":397},{"title":"`PromptingTools.Experimental.RAGTools.rephrase`","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.SimpleRephraser, AbstractString}","line":398},{"title":"`PromptingTools.Experimental.RAGTools.rephrase`","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.HyDERephraser, AbstractString}","line":399},{"title":"`PromptingTools.Experimental.RAGTools.rerank`","target":"#PromptingTools.Experimental.RAGTools.rerank-Tuple{PromptingTools.Experimental.RAGTools.CohereReranker, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractString, PromptingTools.Experimental.RAGTools.AbstractCandidateChunks}","line":400},{"title":"`PromptingTools.Experimental.RAGTools.retrieve`","target":"#PromptingTools.Experimental.RAGTools.retrieve-Tuple{PromptingTools.Experimental.RAGTools.AbstractRetriever, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractString}","line":401},{"title":"`PromptingTools.Experimental.RAGTools.run_qa_evals`","target":"#PromptingTools.Experimental.RAGTools.run_qa_evals-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractVector{<:PromptingTools.Experimental.RAGTools.QAEvalItem}}","line":402},{"title":"`PromptingTools.Experimental.RAGTools.run_qa_evals`","target":"#PromptingTools.Experimental.RAGTools.run_qa_evals-Tuple{PromptingTools.Experimental.RAGTools.QAEvalItem, RAGResult}","line":403},{"title":"`PromptingTools.Experimental.RAGTools.score_retrieval_hit`","target":"#PromptingTools.Experimental.RAGTools.score_retrieval_hit-Tuple{AbstractString, Vector{<:AbstractString}}","line":404},{"title":"`PromptingTools.Experimental.RAGTools.score_retrieval_rank`","target":"#PromptingTools.Experimental.RAGTools.score_retrieval_rank-Tuple{AbstractString, Vector{<:AbstractString}}","line":405},{"title":"`PromptingTools.Experimental.RAGTools.set_node_style!`","target":"#PromptingTools.Experimental.RAGTools.set_node_style!-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AnnotatedNode}","line":406},{"title":"`PromptingTools.Experimental.RAGTools.split_into_code_and_sentences`","target":"#PromptingTools.Experimental.RAGTools.split_into_code_and_sentences-Tuple{Union{SubString{String}, String}}","line":407},{"title":"`PromptingTools.Experimental.RAGTools.tags_extract`","target":"#PromptingTools.Experimental.RAGTools.tags_extract-Tuple{PromptingTools.Experimental.RAGTools.Tag}","line":408},{"title":"`PromptingTools.Experimental.RAGTools.token_with_boundaries`","target":"#PromptingTools.Experimental.RAGTools.token_with_boundaries-Tuple{Union{Nothing, AbstractString}, AbstractString, Union{Nothing, AbstractString}}","line":409},{"title":"`PromptingTools.Experimental.RAGTools.tokenize`","target":"#PromptingTools.Experimental.RAGTools.tokenize-Tuple{Union{SubString{String}, String}}","line":410},{"title":"`PromptingTools.Experimental.RAGTools.trigram_support!`","target":"#PromptingTools.Experimental.RAGTools.trigram_support!-Union{Tuple{F2}, Tuple{F1}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector, F1}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector, F1, F2}} where {F1<:Function, F2<:Function}","line":411},{"title":"`PromptingTools.Experimental.RAGTools.trigrams`","target":"#PromptingTools.Experimental.RAGTools.trigrams-Tuple{AbstractString}","line":412},{"title":"`PromptingTools.Experimental.RAGTools.trigrams_hashed`","target":"#PromptingTools.Experimental.RAGTools.trigrams_hashed-Tuple{AbstractString}","line":413},{"title":"`PromptingTools.aiclassify`","target":"#PromptingTools.aiclassify-Union{Tuple{T}, Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}} where T<:Union{AbstractString, Tuple{var\"#s115\", var\"#s109\"} where {var\"#s115\"<:AbstractString, var\"#s109\"<:AbstractString}}","line":414},{"title":"`PromptingTools.aiembed`","target":"#PromptingTools.aiembed-Union{Tuple{F}, Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, AbstractVector{<:AbstractString}}}, Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, AbstractVector{<:AbstractString}}, F}} where F<:Function","line":415},{"title":"`PromptingTools.aiembed`","target":"#PromptingTools.aiembed-Union{Tuple{F}, Tuple{PromptingTools.AbstractOllamaManagedSchema, AbstractString}, Tuple{PromptingTools.AbstractOllamaManagedSchema, AbstractString, F}} where F<:Function","line":416},{"title":"`PromptingTools.aiextract`","target":"#PromptingTools.aiextract-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":417},{"title":"`PromptingTools.aiextract`","target":"#PromptingTools.aiextract-Tuple{PromptingTools.AbstractAnthropicSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":418},{"title":"`PromptingTools.aigenerate`","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractOllamaManagedSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":419},{"title":"`PromptingTools.aigenerate`","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractAnthropicSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":420},{"title":"`PromptingTools.aigenerate`","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractOllamaSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":421},{"title":"`PromptingTools.aigenerate`","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractGoogleSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":422},{"title":"`PromptingTools.aigenerate`","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":423},{"title":"`PromptingTools.aiimage`","target":"#PromptingTools.aiimage-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":424},{"title":"`PromptingTools.aiscan`","target":"#PromptingTools.aiscan-Tuple{PromptingTools.AbstractOllamaSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":425},{"title":"`PromptingTools.aiscan`","target":"#PromptingTools.aiscan-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":426},{"title":"`PromptingTools.aitemplates`","target":"#PromptingTools.aitemplates-Tuple{AbstractString}","line":427},{"title":"`PromptingTools.aitemplates`","target":"#PromptingTools.aitemplates","line":428},{"title":"`PromptingTools.aitemplates`","target":"#PromptingTools.aitemplates-Tuple{Symbol}","line":429},{"title":"`PromptingTools.aitemplates`","target":"#PromptingTools.aitemplates-Tuple{Regex}","line":430},{"title":"`PromptingTools.anthropic_api`","target":"#PromptingTools.anthropic_api","line":431},{"title":"`PromptingTools.auth_header`","target":"#PromptingTools.auth_header-Tuple{Union{Nothing, AbstractString}}","line":432},{"title":"`PromptingTools.build_template_metadata`","target":"#PromptingTools.build_template_metadata","line":433},{"title":"`PromptingTools.call_cost`","target":"#PromptingTools.call_cost-Tuple{Int64, Int64, String}","line":434},{"title":"`PromptingTools.call_cost_alternative`","target":"#PromptingTools.call_cost_alternative-Tuple{Any, Any}","line":435},{"title":"`PromptingTools.create_template`","target":"#PromptingTools.create_template-Tuple{AbstractString, AbstractString}","line":436},{"title":"`PromptingTools.decode_choices`","target":"#PromptingTools.decode_choices-Tuple{PromptingTools.OpenAISchema, AbstractVector{<:AbstractString}, AIMessage}","line":437},{"title":"`PromptingTools.detect_base_main_overrides`","target":"#PromptingTools.detect_base_main_overrides-Tuple{AbstractString}","line":438},{"title":"`PromptingTools.distance_longest_common_subsequence`","target":"#PromptingTools.distance_longest_common_subsequence-Tuple{AbstractString, AbstractString}","line":439},{"title":"`PromptingTools.encode_choices`","target":"#PromptingTools.encode_choices-Tuple{PromptingTools.OpenAISchema, AbstractVector{<:AbstractString}}","line":440},{"title":"`PromptingTools.eval!`","target":"#PromptingTools.eval!-Tuple{PromptingTools.AbstractCodeBlock}","line":441},{"title":"`PromptingTools.extract_code_blocks`","target":"#PromptingTools.extract_code_blocks-Tuple{T} where T<:AbstractString","line":442},{"title":"`PromptingTools.extract_code_blocks_fallback`","target":"#PromptingTools.extract_code_blocks_fallback-Union{Tuple{T}, Tuple{T, AbstractString}} where T<:AbstractString","line":443},{"title":"`PromptingTools.extract_function_name`","target":"#PromptingTools.extract_function_name-Tuple{AbstractString}","line":444},{"title":"`PromptingTools.extract_function_names`","target":"#PromptingTools.extract_function_names-Tuple{AbstractString}","line":445},{"title":"`PromptingTools.extract_julia_imports`","target":"#PromptingTools.extract_julia_imports-Tuple{AbstractString}","line":446},{"title":"`PromptingTools.finalize_outputs`","target":"#PromptingTools.finalize_outputs-Tuple{Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}, Any, Union{Nothing, PromptingTools.AbstractMessage, AbstractVector{<:PromptingTools.AbstractMessage}}}","line":447},{"title":"`PromptingTools.find_subsequence_positions`","target":"#PromptingTools.find_subsequence_positions-Tuple{Any, Any}","line":448},{"title":"`PromptingTools.function_call_signature`","target":"#PromptingTools.function_call_signature-Tuple{Type}","line":449},{"title":"`PromptingTools.get_preferences`","target":"#PromptingTools.get_preferences-Tuple{String}","line":450},{"title":"`PromptingTools.ggi_generate_content`","target":"#PromptingTools.ggi_generate_content","line":451},{"title":"`PromptingTools.has_julia_prompt`","target":"#PromptingTools.has_julia_prompt-Tuple{T} where T<:AbstractString","line":452},{"title":"`PromptingTools.last_message`","target":"#PromptingTools.last_message-Tuple{PromptingTools.Experimental.AgentTools.AICallBlock}","line":453},{"title":"`PromptingTools.last_message`","target":"#PromptingTools.last_message-Tuple{AbstractVector{<:PromptingTools.AbstractMessage}}","line":454},{"title":"`PromptingTools.last_output`","target":"#PromptingTools.last_output-Tuple{PromptingTools.Experimental.AgentTools.AICallBlock}","line":455},{"title":"`PromptingTools.last_output`","target":"#PromptingTools.last_output-Tuple{AbstractVector{<:PromptingTools.AbstractMessage}}","line":456},{"title":"`PromptingTools.length_longest_common_subsequence`","target":"#PromptingTools.length_longest_common_subsequence-Tuple{Any, Any}","line":457},{"title":"`PromptingTools.list_aliases`","target":"#PromptingTools.list_aliases-Tuple{}","line":458},{"title":"`PromptingTools.list_registry`","target":"#PromptingTools.list_registry-Tuple{}","line":459},{"title":"`PromptingTools.load_conversation`","target":"#PromptingTools.load_conversation-Tuple{Union{AbstractString, IO}}","line":460},{"title":"`PromptingTools.load_template`","target":"#PromptingTools.load_template-Tuple{Union{AbstractString, IO}}","line":461},{"title":"`PromptingTools.load_templates!`","target":"#PromptingTools.load_templates!","line":462},{"title":"`PromptingTools.ollama_api`","target":"#PromptingTools.ollama_api","line":463},{"title":"`PromptingTools.pprint`","target":"#PromptingTools.pprint-Tuple{IO, PromptingTools.Experimental.RAGTools.AbstractAnnotatedNode}","line":464},{"title":"`PromptingTools.pprint`","target":"#PromptingTools.pprint-Tuple{IO, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":465},{"title":"`PromptingTools.pprint`","target":"#PromptingTools.pprint","line":466},{"title":"`PromptingTools.pprint`","target":"#PromptingTools.pprint-Tuple{IO, PromptingTools.AbstractMessage}","line":467},{"title":"`PromptingTools.pprint`","target":"#PromptingTools.pprint-Tuple{IO, AbstractVector{<:PromptingTools.AbstractMessage}}","line":468},{"title":"`PromptingTools.preview`","target":"#PromptingTools.preview","line":469},{"title":"`PromptingTools.push_conversation!`","target":"#PromptingTools.push_conversation!-Tuple{Vector{<:Vector}, AbstractVector, Union{Nothing, Int64}}","line":470},{"title":"`PromptingTools.recursive_splitter`","target":"#PromptingTools.recursive_splitter-Tuple{AbstractString, Vector{String}}","line":471},{"title":"`PromptingTools.recursive_splitter`","target":"#PromptingTools.recursive_splitter-Tuple{String}","line":472},{"title":"`PromptingTools.register_model!`","target":"#PromptingTools.register_model!","line":473},{"title":"`PromptingTools.remove_julia_prompt`","target":"#PromptingTools.remove_julia_prompt-Tuple{T} where T<:AbstractString","line":474},{"title":"`PromptingTools.remove_templates!`","target":"#PromptingTools.remove_templates!-Tuple{}","line":475},{"title":"`PromptingTools.remove_unsafe_lines`","target":"#PromptingTools.remove_unsafe_lines-Tuple{AbstractString}","line":476},{"title":"`PromptingTools.render`","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractOpenAISchema, Vector{<:PromptingTools.AbstractMessage}}","line":477},{"title":"`PromptingTools.render`","target":"#PromptingTools.render-Tuple{PromptingTools.NoSchema, Vector{<:PromptingTools.AbstractMessage}}","line":478},{"title":"`PromptingTools.render`","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractOllamaSchema, Vector{<:PromptingTools.AbstractMessage}}","line":479},{"title":"`PromptingTools.render`","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractAnthropicSchema, Vector{<:PromptingTools.AbstractMessage}}","line":480},{"title":"`PromptingTools.render`","target":"#PromptingTools.render-Tuple{AITemplate}","line":481},{"title":"`PromptingTools.render`","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractOllamaManagedSchema, Vector{<:PromptingTools.AbstractMessage}}","line":482},{"title":"`PromptingTools.render`","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractGoogleSchema, Vector{<:PromptingTools.AbstractMessage}}","line":483},{"title":"`PromptingTools.replace_words`","target":"#PromptingTools.replace_words-Tuple{AbstractString, Vector{<:AbstractString}}","line":484},{"title":"`PromptingTools.resize_conversation!`","target":"#PromptingTools.resize_conversation!-Tuple{Any, Union{Nothing, Int64}}","line":485},{"title":"`PromptingTools.response_to_message`","target":"#PromptingTools.response_to_message-Union{Tuple{T}, Tuple{PromptingTools.AbstractPromptSchema, Type{T}, Any, Any}} where T","line":486},{"title":"`PromptingTools.response_to_message`","target":"#PromptingTools.response_to_message-Tuple{PromptingTools.AbstractOpenAISchema, Type{AIMessage}, Any, Any}","line":487},{"title":"`PromptingTools.save_conversation`","target":"#PromptingTools.save_conversation-Tuple{Union{AbstractString, IO}, AbstractVector{<:PromptingTools.AbstractMessage}}","line":488},{"title":"`PromptingTools.save_conversations`","target":"#PromptingTools.save_conversations-Tuple{PromptingTools.AbstractPromptSchema, AbstractString, Vector{<:AbstractVector{<:PromptingTools.AbstractMessage}}}","line":489},{"title":"`PromptingTools.save_template`","target":"#PromptingTools.save_template-Tuple{Union{AbstractString, IO}, AbstractVector{<:PromptingTools.AbstractChatMessage}}","line":490},{"title":"`PromptingTools.set_preferences!`","target":"#PromptingTools.set_preferences!-Tuple{Vararg{Pair{String}}}","line":491},{"title":"`PromptingTools.wrap_string`","target":"#PromptingTools.wrap_string","line":492},{"title":"`PromptingTools.@aai_str`","target":"#PromptingTools.@aai_str-Tuple{Any, Vararg{Any}}","line":493},{"title":"`PromptingTools.@ai!_str`","target":"#PromptingTools.@ai!_str-Tuple{Any, Vararg{Any}}","line":494},{"title":"`PromptingTools.@ai_str`","target":"#PromptingTools.@ai_str-Tuple{Any, Vararg{Any}}","line":495},{"title":"`PromptingTools.@timeout`","target":"#PromptingTools.@timeout-Tuple{Any, Any, Any}","line":496},{"title":"#","target":"#PromptingTools.ALLOWED_PREFERENCES","line":498},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L54","line":502},{"title":"#","target":"#PromptingTools.ALTERNATIVE_GENERATION_COSTS","line":506},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L553-L557","line":516},{"title":"#","target":"#PromptingTools.ANTHROPIC_TOOL_PROMPT","line":520},{"title":"https://docs.anthropic.com/claude/docs/functions-external-tools","target":"https://docs.anthropic.com/claude/docs/functions-external-tools","line":524},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/extraction.jl#L187-L192","line":526},{"title":"#","target":"#PromptingTools.CONV_HISTORY","line":530},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L177-L186","line":544},{"title":"#","target":"#PromptingTools.MODEL_ALIASES","line":548},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L652-L666","line":572},{"title":"#","target":"#PromptingTools.MODEL_REGISTRY","line":576},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L582-L609","line":615},{"title":"#","target":"#PromptingTools.OPENAI_TOKEN_IDS","line":619},{"title":"https://platform.openai.com/tokenizer","target":"https://platform.openai.com/tokenizer","line":621},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L621","line":623},{"title":"#","target":"#PromptingTools.PREFERENCES","line":627},{"title":"OpenAI's documentation","target":"https://platform.openai.com/docs/quickstart?context=python","line":641},{"title":"Mistral AI's documentation","target":"https://docs.mistral.ai/","line":643},{"title":"Cohere's documentation","target":"https://docs.cohere.com/docs/the-cohere-platform","line":645},{"title":"Databricks' documentation","target":"https://docs.databricks.com/en/machine-learning/foundation-models/api-reference.html","line":647},{"title":"Databricks' documentation","target":"https://docs.databricks.com/en/machine-learning/foundation-models/api-reference.html","line":649},{"title":"here","target":"https://docs.tavily.com/docs/tavily-api/rest_api","line":651},{"title":"here","target":"https://tavily.com/","line":651},{"title":"here","target":"https://ai.google.dev/","line":653},{"title":"here","target":"https://www.anthropic.com/","line":655},{"title":"here","target":"https://dash.voyageai.com/api-keys","line":657},{"title":"here","target":"https://docs.tavily.com/docs/tavily-api/rest_api","line":688},{"title":"here","target":"https://tavily.com/","line":688},{"title":"here","target":"https://ai.google.dev/","line":690},{"title":"here","target":"https://www.anthropic.com/","line":692},{"title":"here","target":"https://dash.voyageai.com/api-keys","line":694},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L4-L51","line":701},{"title":"#","target":"#PromptingTools.RESERVED_KWARGS","line":705},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/PromptingTools.jl#L17","line":709},{"title":"#","target":"#PromptingTools.AICode","line":713},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_eval.jl#L18-L106","line":835},{"title":"#","target":"#PromptingTools.AIMessage","line":839},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L65-L81","line":870},{"title":"#","target":"#PromptingTools.AITemplate","line":874},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L8-L74","line":970},{"title":"#","target":"#PromptingTools.AITemplateMetadata","line":974},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L77","line":978},{"title":"#","target":"#PromptingTools.AbstractPromptSchema","line":982},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L20","line":986},{"title":"#","target":"#PromptingTools.AnthropicSchema","line":990},{"title":"here","target":"https://docs.anthropic.com/claude/reference/getting-started-with-the-api","line":998},{"title":"here","target":"https://docs.anthropic.com/claude/docs/use-xml-tags","line":1008},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L256-L270","line":1010},{"title":"#","target":"#PromptingTools.ChatMLSchema","line":1014},{"title":"tiktokenizer","target":"https://tiktokenizer.vercel.app/","line":1018},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L203-L217","line":1031},{"title":"#","target":"#PromptingTools.CustomOpenAISchema","line":1035},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L48-L65","line":1059},{"title":"#","target":"#PromptingTools.DataMessage","line":1063},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L95-L111","line":1094},{"title":"#","target":"#PromptingTools.DatabricksOpenAISchema","line":1098},{"title":"API Reference","target":"https://docs.databricks.com/en/machine-learning/foundation-models/api-reference.html","line":1106},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L139-L147","line":1115},{"title":"#","target":"#PromptingTools.FireworksOpenAISchema","line":1119},{"title":"Fireworks.ai","target":"https://fireworks.ai/","line":1127},{"title":"Get your API key","target":"https://fireworks.ai/api-keys","line":1131},{"title":"API Reference","target":"https://readme.fireworks.ai/reference/createchatcompletion","line":1133},{"title":"Available models","target":"https://fireworks.ai/models","line":1135},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L150-L162","line":1142},{"title":"#","target":"#PromptingTools.GoogleSchema","line":1146},{"title":"here","target":"https://aistudio.google.com/","line":1148},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L243","line":1150},{"title":"#","target":"#PromptingTools.ItemsExtract","line":1154},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/extraction.jl#L233-L235","line":1158},{"title":"#","target":"#PromptingTools.LocalServerOpenAISchema","line":1162},{"title":"Llama.jl","target":"https://github.com/marcom/Llama.jl","line":1197},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L68-L114","line":1221},{"title":"#","target":"#PromptingTools.MaybeExtract","line":1225},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/extraction.jl#L220-L226","line":1236},{"title":"#","target":"#PromptingTools.MistralOpenAISchema","line":1240},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L117-L136","line":1267},{"title":"#","target":"#PromptingTools.ModelSpec","line":1271},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L197-L232","line":1322},{"title":"#","target":"#PromptingTools.NoSchema","line":1326},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L23","line":1330},{"title":"#","target":"#PromptingTools.OllamaManagedSchema","line":1334},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L223-L230","line":1342},{"title":"#","target":"#PromptingTools.OllamaSchema","line":1346},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L182-L191","line":1358},{"title":"#","target":"#PromptingTools.OpenAISchema","line":1362},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L28-L39","line":1376},{"title":"#","target":"#PromptingTools.ShareGPTSchema","line":1380},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L281-L285","line":1390},{"title":"#","target":"#PromptingTools.TestEchoAnthropicSchema","line":1394},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L271","line":1398},{"title":"#","target":"#PromptingTools.TestEchoGoogleSchema","line":1402},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L246","line":1406},{"title":"#","target":"#PromptingTools.TestEchoOllamaManagedSchema","line":1410},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L233","line":1414},{"title":"#","target":"#PromptingTools.TestEchoOllamaSchema","line":1418},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L194","line":1422},{"title":"#","target":"#PromptingTools.TestEchoOpenAISchema","line":1426},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L40","line":1430},{"title":"#","target":"#PromptingTools.TogetherOpenAISchema","line":1434},{"title":"Together.ai","target":"https://www.together.ai/","line":1442},{"title":"Get your API key","target":"https://api.together.xyz/settings/api-keys","line":1446},{"title":"API Reference","target":"https://docs.together.ai/docs/openai-api-compatibility","line":1448},{"title":"Available models","target":"https://docs.together.ai/docs/inference-models","line":1450},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L165-L177","line":1457},{"title":"#","target":"#PromptingTools.UserMessageWithImages-Tuple{AbstractString}","line":1461},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L142","line":1465},{"title":"#","target":"#PromptingTools.X123","line":1469},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/precompilation.jl#L22","line":1473},{"title":"#","target":"#OpenAI.create_chat-Tuple{PromptingTools.CustomOpenAISchema, AbstractString, AbstractString, Any}","line":1477},{"title":"http://localhost:8080","target":"http://localhost:8080","line":1485},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L98-L111","line":1493},{"title":"#","target":"#OpenAI.create_chat-Tuple{PromptingTools.LocalServerOpenAISchema, AbstractString, AbstractString, Any}","line":1497},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L124-L134","line":1512},{"title":"#","target":"#OpenAI.create_chat-Tuple{PromptingTools.MistralOpenAISchema, AbstractString, AbstractString, Any}","line":1516},{"title":"https://api.mistral.ai/v1","target":"https://api.mistral.ai/v1","line":1524},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L144-L155","line":1530},{"title":"#","target":"#PromptingTools.aiclassify-Union{Tuple{T}, Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}} where T<:Union{AbstractString, Tuple{var\"#s115\", var\"#s109\"} where {var\"#s115\"<:AbstractString, var\"#s109\"<:AbstractString}}","line":1534},{"title":"AAAzzam","target":"https://twitter.com/AAAzzam/status/1669753721574633473","line":1553},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L776-L843","line":1627},{"title":"#","target":"#PromptingTools.aiembed-Union{Tuple{F}, Tuple{PromptingTools.AbstractOllamaManagedSchema, AbstractString}, Tuple{PromptingTools.AbstractOllamaManagedSchema, AbstractString, F}} where F<:Function","line":1631},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_ollama_managed.jl#L241-L314","line":1729},{"title":"#","target":"#PromptingTools.aiembed-Union{Tuple{F}, Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, AbstractVector{<:AbstractString}}}, Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, AbstractVector{<:AbstractString}}, F}} where F<:Function","line":1733},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L537-L589","line":1808},{"title":"#","target":"#PromptingTools.aiextract-Tuple{PromptingTools.AbstractAnthropicSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":1812},{"title":"here","target":"https://docs.anthropic.com/claude/docs/tool-use#tool-use-best-practices-and-limitations","line":1834},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_anthropic.jl#L264-L390","line":1964},{"title":"#","target":"#PromptingTools.aiextract-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":1968},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L909-L1040","line":2121},{"title":"#","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractAnthropicSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2125},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_anthropic.jl#L140-L217","line":2231},{"title":"#","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractGoogleSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2235},{"title":"here","target":"https://ai.google.dev/","line":2250},{"title":"here","target":"https://ai.google.dev/pricing","line":2254},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_google.jl#L75-L147","line":2343},{"title":"#","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractOllamaManagedSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2347},{"title":" Info: Tokens: 111 in 2.1 seconds\n# AIMessage(\"Strong the attachment is, it leads to suffering it may. Focus on the force within you must, ...<continues>\")\n```\n\nNote: Managed Ollama currently supports at most 1 User Message and 1 System Message given the API limitations. If you want more, you need to use the `ChatMLSchema`.\n\n[source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_ollama_managed.jl#L124-L198","line":2445},{"title":"#","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractOllamaSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2455},{"title":" Info: Tokens: 111 in 2.1 seconds\n# AIMessage(\"Strong the attachment is, it leads to suffering it may. Focus on the force within you must, ...<continues>\")\n```\n\nNote: Managed Ollama currently supports at most 1 User Message and 1 System Message given the API limitations. If you want more, you need to use the `ChatMLSchema`.\n\n[source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_ollama.jl#L67-L141","line":2553},{"title":"#","target":"#PromptingTools.aigenerate-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2563},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L405-L478","line":2673},{"title":"#","target":"#PromptingTools.aiimage-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2677},{"title":"API docs","target":"https://platform.openai.com/docs/api-reference/images/create","line":2709},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L1275-L1360","line":2797},{"title":"#","target":"#PromptingTools.aiscan-Tuple{PromptingTools.AbstractOllamaSchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2801},{"title":"OpenAI Vision Guide","target":"https://platform.openai.com/docs/guides/vision","line":2838},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_ollama.jl#L192-L288","line":2932},{"title":"#","target":"#PromptingTools.aiscan-Tuple{PromptingTools.AbstractOpenAISchema, Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}}","line":2936},{"title":"OpenAI Vision Guide","target":"https://platform.openai.com/docs/guides/vision","line":2974},{"title":" Info: Tokens: 362 @ Cost: $0.0045 in 2.5 seconds\n# AIMessage(\"```sql\n# update Orders <continue>\n\n# You can add syntax highlighting of the outputs via Markdown\nusing Markdown\nmsg.content |> Markdown.parse\n```\n\nNotice that we enforce `max_tokens = 2500`. That's because OpenAI seems to default to ~300 tokens, which provides incomplete outputs. Hence, we set this value to 2500 as a default. If you still get truncated outputs, increase this value.\n\n[source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L1117-L1207","line":3047},{"title":"#","target":"#PromptingTools.aitemplates","line":3062},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L258-L306","line":3130},{"title":"#","target":"#PromptingTools.aitemplates-Tuple{AbstractString}","line":3134},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L317","line":3138},{"title":"#","target":"#PromptingTools.aitemplates-Tuple{Regex}","line":3142},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L328","line":3146},{"title":"#","target":"#PromptingTools.aitemplates-Tuple{Symbol}","line":3150},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L307","line":3154},{"title":"#","target":"#PromptingTools.anthropic_api","line":3158},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_anthropic.jl#L73-L97","line":3199},{"title":"#","target":"#PromptingTools.auth_header-Tuple{Union{Nothing, AbstractString}}","line":3203},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L615-L630","line":3228},{"title":"#","target":"#PromptingTools.build_template_metadata","line":3232},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L122-L132","line":3248},{"title":"#","target":"#PromptingTools.call_cost-Tuple{Int64, Int64, String}","line":3252},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L367-L411","line":3309},{"title":"#","target":"#PromptingTools.call_cost_alternative-Tuple{Any, Any}","line":3313},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L443-L447","line":3319},{"title":"#","target":"#PromptingTools.create_template-Tuple{AbstractString, AbstractString}","line":3323},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L379-L463","line":3435},{"title":"#","target":"#PromptingTools.decode_choices-Tuple{PromptingTools.OpenAISchema, AbstractVector{<:AbstractString}, AIMessage}","line":3439},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L748-L756","line":3453},{"title":"#","target":"#PromptingTools.detect_base_main_overrides-Tuple{AbstractString}","line":3457},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L425-L431","line":3469},{"title":"#","target":"#PromptingTools.distance_longest_common_subsequence-Tuple{AbstractString, AbstractString}","line":3473},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L298-L340","line":3526},{"title":"#","target":"#PromptingTools.encode_choices-Tuple{PromptingTools.OpenAISchema, AbstractVector{<:AbstractString}}","line":3530},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L647-L688","line":3591},{"title":"#","target":"#PromptingTools.eval!-Tuple{PromptingTools.AbstractCodeBlock}","line":3595},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_eval.jl#L213-L242","line":3639},{"title":"#","target":"#PromptingTools.extract_code_blocks-Tuple{T} where T<:AbstractString","line":3643},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L173-L219","line":3706},{"title":"#","target":"#PromptingTools.extract_code_blocks_fallback-Union{Tuple{T}, Tuple{T, AbstractString}} where T<:AbstractString","line":3710},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L274-L301","line":3747},{"title":"#","target":"#PromptingTools.extract_function_name-Tuple{AbstractString}","line":3751},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L344-L371","line":3792},{"title":"#","target":"#PromptingTools.extract_function_names-Tuple{AbstractString}","line":3796},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L394-L404","line":3810},{"title":"#","target":"#PromptingTools.extract_julia_imports-Tuple{AbstractString}","line":3814},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L23-L29","line":3826},{"title":"#","target":"#PromptingTools.finalize_outputs-Tuple{Union{AbstractString, PromptingTools.AbstractMessage, Vector{<:PromptingTools.AbstractMessage}}, Any, Union{Nothing, PromptingTools.AbstractMessage, AbstractVector{<:PromptingTools.AbstractMessage}}}","line":3830},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_shared.jl#L66-L82","line":3856},{"title":"#","target":"#PromptingTools.find_subsequence_positions-Tuple{Any, Any}","line":3860},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L132-L150","line":3891},{"title":"#","target":"#PromptingTools.function_call_signature-Tuple{Type}","line":3895},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/extraction.jl#L96-L164","line":3989},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L99-L110","line":4015},{"title":"#","target":"#PromptingTools.ggi_generate_content","line":4019},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_google.jl#L64","line":4023},{"title":"#","target":"#PromptingTools.has_julia_prompt-Tuple{T} where T<:AbstractString","line":4027},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L92","line":4031},{"title":"#","target":"#PromptingTools.last_message-Tuple{AbstractVector{<:PromptingTools.AbstractMessage}}","line":4035},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L197","line":4039},{"title":"#","target":"#PromptingTools.last_output-Tuple{AbstractVector{<:PromptingTools.AbstractMessage}}","line":4043},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L202","line":4047},{"title":"#","target":"#PromptingTools.length_longest_common_subsequence-Tuple{Any, Any}","line":4051},{"title":"https://cn.julialang.org/LeetCode.jl/dev/democards/problems/problems/1143.longest-common-subsequence/","target":"https://cn.julialang.org/LeetCode.jl/dev/democards/problems/problems/1143.longest-common-subsequence/","line":4061},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L249-L285","line":4104},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L649","line":4117},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L647","line":4130},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/serialization.jl#L55-L59","line":4146},{"title":"#","target":"#PromptingTools.load_template-Tuple{Union{AbstractString, IO}}","line":4150},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/serialization.jl#L25-L29","line":4160},{"title":"#","target":"#PromptingTools.load_templates!","line":4164},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L181-L212","line":4213},{"title":"#","target":"#PromptingTools.ollama_api","line":4217},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_ollama_managed.jl#L57-L81","line":4258},{"title":"#","target":"#PromptingTools.pprint","line":4262},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L602","line":4266},{"title":"#","target":"#PromptingTools.pprint-Tuple{IO, AbstractVector{<:PromptingTools.AbstractMessage}}","line":4270},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L314-L318","line":4280},{"title":"#","target":"#PromptingTools.pprint-Tuple{IO, PromptingTools.AbstractMessage}","line":4284},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L283-L289","line":4296},{"title":"#","target":"#PromptingTools.preview","line":4300},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L599","line":4304},{"title":"#","target":"#PromptingTools.push_conversation!-Tuple{Vector{<:Vector}, AbstractVector, Union{Nothing, Int64}}","line":4308},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L502-L524","line":4344},{"title":"#","target":"#PromptingTools.recursive_splitter-Tuple{AbstractString, Vector{String}}","line":4348},{"title":"`RecursiveCharacterTextSplitter`","target":"https://python.langchain.com/docs/modules/data_connection/document_transformers/recursive_text_splitter","line":4360},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L119-L182","line":4436},{"title":"#","target":"#PromptingTools.recursive_splitter-Tuple{String}","line":4440},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L34-L71","line":4496},{"title":"#","target":"#PromptingTools.register_model!","line":4500},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L244-L264","line":4530},{"title":"#","target":"#PromptingTools.remove_julia_prompt-Tuple{T} where T<:AbstractString","line":4534},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L95-L99","line":4544},{"title":"#","target":"#PromptingTools.remove_templates!-Tuple{}","line":4548},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L174-L178","line":4558},{"title":"#","target":"#PromptingTools.remove_unsafe_lines-Tuple{AbstractString}","line":4562},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_parsing.jl#L77","line":4566},{"title":"#","target":"#PromptingTools.render-Tuple{AITemplate}","line":4570},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/templates.jl#L110","line":4574},{"title":"#","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractAnthropicSchema, Vector{<:PromptingTools.AbstractMessage}}","line":4578},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_anthropic.jl#L7-L19","line":4599},{"title":"#","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractGoogleSchema, Vector{<:PromptingTools.AbstractMessage}}","line":4603},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_google.jl#L2-L13","line":4620},{"title":"#","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractOllamaManagedSchema, Vector{<:PromptingTools.AbstractMessage}}","line":4624},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_ollama_managed.jl#L9-L21","line":4643},{"title":"#","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractOllamaSchema, Vector{<:PromptingTools.AbstractMessage}}","line":4647},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_ollama.jl#L10-L21","line":4664},{"title":"#","target":"#PromptingTools.render-Tuple{PromptingTools.AbstractOpenAISchema, Vector{<:PromptingTools.AbstractMessage}}","line":4668},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L2-L15","line":4689},{"title":"#","target":"#PromptingTools.render-Tuple{PromptingTools.NoSchema, Vector{<:PromptingTools.AbstractMessage}}","line":4693},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_shared.jl#L2-L20","line":4724},{"title":"#","target":"#PromptingTools.replace_words-Tuple{AbstractString, Vector{<:AbstractString}}","line":4728},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L3-L19","line":4757},{"title":"#","target":"#PromptingTools.resize_conversation!-Tuple{Any, Union{Nothing, Int64}}","line":4761},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L533-L556","line":4796},{"title":"#","target":"#PromptingTools.response_to_message-Tuple{PromptingTools.AbstractOpenAISchema, Type{AIMessage}, Any, Any}","line":4800},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_openai.jl#L344-L367","line":4838},{"title":"#","target":"#PromptingTools.response_to_message-Union{Tuple{T}, Tuple{PromptingTools.AbstractPromptSchema, Type{T}, Any, Any}} where T","line":4842},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/llm_interface.jl#L322","line":4846},{"title":"#","target":"#PromptingTools.save_conversation-Tuple{Union{AbstractString, IO}, AbstractVector{<:PromptingTools.AbstractMessage}}","line":4850},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/serialization.jl#L45-L50","line":4861},{"title":"#","target":"#PromptingTools.save_conversations-Tuple{PromptingTools.AbstractPromptSchema, AbstractString, Vector{<:AbstractVector{<:PromptingTools.AbstractMessage}}}","line":4865},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/serialization.jl#L64-L92","line":4902},{"title":"#","target":"#PromptingTools.save_template-Tuple{Union{AbstractString, IO}, AbstractVector{<:PromptingTools.AbstractChatMessage}}","line":4906},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/serialization.jl#L2-L11","line":4921},{"title":"#","target":"#PromptingTools.set_preferences!-Tuple{Vararg{Pair{String}}}","line":4925},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/user_preferences.jl#L71-L84","line":4947},{"title":"#","target":"#PromptingTools.wrap_string","line":4951},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L204-L218","line":4973},{"title":"#","target":"#PromptingTools.@aai_str-Tuple{Any, Vararg{Any}}","line":4977},{"title":" Info: Tokens: 29 @ Cost: 0.0011\n in 2.7 seconds**\n\n**[ Info: AIMessage> Hello! How can I assist you today?**\n\n\n[source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/macros.jl#L99-L116","line":4999},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/macros.jl#L45-L80","line":5064},{"title":"#","target":"#PromptingTools.@ai_str-Tuple{Any, Vararg{Any}}","line":5068},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/macros.jl#L1-L33","line":5119},{"title":"#","target":"#PromptingTools.@timeout-Tuple{Any, Any, Any}","line":5123},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L569-L583","line":5145},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/reference.md","line":5149},{"title":"Previous pageRAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":5151},{"title":"Next pageExperimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":5153},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":5155},{"title":"Icons8","target":"https://icons8.com","line":5155},{"title":"**VitePress**","target":"https://vitepress.dev","line":5155}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,242],"##Reference#{1}":[209,242],"#Reference [​](#Reference)":[243,5157],"#Reference [​](#Reference)#{1}":[245,245],"#Reference [​](#Reference)#{2}":[246,246],"#Reference [​](#Reference)#{3}":[247,247],"#Reference [​](#Reference)#{4}":[248,248],"#Reference [​](#Reference)#{5}":[249,249],"#Reference [​](#Reference)#{6}":[250,250],"#Reference [​](#Reference)#{7}":[251,251],"#Reference [​](#Reference)#{8}":[252,252],"#Reference [​](#Reference)#{9}":[253,253],"#Reference [​](#Reference)#{10}":[254,254],"#Reference [​](#Reference)#{11}":[255,255],"#Reference [​](#Reference)#{12}":[256,256],"#Reference [​](#Reference)#{13}":[257,257],"#Reference [​](#Reference)#{14}":[258,258],"#Reference [​](#Reference)#{15}":[259,259],"#Reference [​](#Reference)#{16}":[260,260],"#Reference [​](#Reference)#{17}":[261,261],"#Reference [​](#Reference)#{18}":[262,262],"#Reference [​](#Reference)#{19}":[263,263],"#Reference [​](#Reference)#{20}":[264,264],"#Reference [​](#Reference)#{21}":[265,265],"#Reference [​](#Reference)#{22}":[266,266],"#Reference [​](#Reference)#{23}":[267,267],"#Reference [​](#Reference)#{24}":[268,268],"#Reference [​](#Reference)#{25}":[269,269],"#Reference [​](#Reference)#{26}":[270,270],"#Reference [​](#Reference)#{27}":[271,271],"#Reference [​](#Reference)#{28}":[272,272],"#Reference [​](#Reference)#{29}":[273,273],"#Reference [​](#Reference)#{30}":[274,274],"#Reference [​](#Reference)#{31}":[275,275],"#Reference [​](#Reference)#{32}":[276,276],"#Reference [​](#Reference)#{33}":[277,277],"#Reference [​](#Reference)#{34}":[278,278],"#Reference [​](#Reference)#{35}":[279,279],"#Reference [​](#Reference)#{36}":[280,280],"#Reference [​](#Reference)#{37}":[281,281],"#Reference [​](#Reference)#{38}":[282,282],"#Reference [​](#Reference)#{39}":[283,283],"#Reference [​](#Reference)#{40}":[284,284],"#Reference [​](#Reference)#{41}":[285,285],"#Reference [​](#Reference)#{42}":[286,286],"#Reference [​](#Reference)#{43}":[287,287],"#Reference [​](#Reference)#{44}":[288,288],"#Reference [​](#Reference)#{45}":[289,289],"#Reference [​](#Reference)#{46}":[290,290],"#Reference [​](#Reference)#{47}":[291,291],"#Reference [​](#Reference)#{48}":[292,292],"#Reference [​](#Reference)#{49}":[293,293],"#Reference [​](#Reference)#{50}":[294,294],"#Reference [​](#Reference)#{51}":[295,295],"#Reference [​](#Reference)#{52}":[296,296],"#Reference [​](#Reference)#{53}":[297,297],"#Reference [​](#Reference)#{54}":[298,298],"#Reference [​](#Reference)#{55}":[299,299],"#Reference [​](#Reference)#{56}":[300,300],"#Reference [​](#Reference)#{57}":[301,301],"#Reference [​](#Reference)#{58}":[302,302],"#Reference [​](#Reference)#{59}":[303,303],"#Reference [​](#Reference)#{60}":[304,304],"#Reference [​](#Reference)#{61}":[305,305],"#Reference [​](#Reference)#{62}":[306,306],"#Reference [​](#Reference)#{63}":[307,307],"#Reference [​](#Reference)#{64}":[308,308],"#Reference [​](#Reference)#{65}":[309,309],"#Reference [​](#Reference)#{66}":[310,310],"#Reference [​](#Reference)#{67}":[311,311],"#Reference [​](#Reference)#{68}":[312,312],"#Reference [​](#Reference)#{69}":[313,313],"#Reference [​](#Reference)#{70}":[314,314],"#Reference [​](#Reference)#{71}":[315,315],"#Reference [​](#Reference)#{72}":[316,316],"#Reference [​](#Reference)#{73}":[317,317],"#Reference [​](#Reference)#{74}":[318,318],"#Reference [​](#Reference)#{75}":[319,319],"#Reference [​](#Reference)#{76}":[320,320],"#Reference [​](#Reference)#{77}":[321,321],"#Reference [​](#Reference)#{78}":[322,322],"#Reference [​](#Reference)#{79}":[323,323],"#Reference [​](#Reference)#{80}":[324,324],"#Reference [​](#Reference)#{81}":[325,325],"#Reference [​](#Reference)#{82}":[326,326],"#Reference [​](#Reference)#{83}":[327,327],"#Reference [​](#Reference)#{84}":[328,328],"#Reference [​](#Reference)#{85}":[329,329],"#Reference [​](#Reference)#{86}":[330,330],"#Reference [​](#Reference)#{87}":[331,331],"#Reference [​](#Reference)#{88}":[332,332],"#Reference [​](#Reference)#{89}":[333,333],"#Reference [​](#Reference)#{90}":[334,334],"#Reference [​](#Reference)#{91}":[335,335],"#Reference [​](#Reference)#{92}":[336,336],"#Reference [​](#Reference)#{93}":[337,337],"#Reference [​](#Reference)#{94}":[338,338],"#Reference [​](#Reference)#{95}":[339,339],"#Reference [​](#Reference)#{96}":[340,340],"#Reference [​](#Reference)#{97}":[341,341],"#Reference [​](#Reference)#{98}":[342,342],"#Reference [​](#Reference)#{99}":[343,343],"#Reference [​](#Reference)#{100}":[344,344],"#Reference [​](#Reference)#{101}":[345,345],"#Reference [​](#Reference)#{102}":[346,346],"#Reference [​](#Reference)#{103}":[347,347],"#Reference [​](#Reference)#{104}":[348,348],"#Reference [​](#Reference)#{105}":[349,349],"#Reference [​](#Reference)#{106}":[350,350],"#Reference [​](#Reference)#{107}":[351,351],"#Reference [​](#Reference)#{108}":[352,352],"#Reference [​](#Reference)#{109}":[353,353],"#Reference [​](#Reference)#{110}":[354,354],"#Reference [​](#Reference)#{111}":[355,355],"#Reference [​](#Reference)#{112}":[356,356],"#Reference [​](#Reference)#{113}":[357,357],"#Reference [​](#Reference)#{114}":[358,358],"#Reference [​](#Reference)#{115}":[359,359],"#Reference [​](#Reference)#{116}":[360,360],"#Reference [​](#Reference)#{117}":[361,361],"#Reference [​](#Reference)#{118}":[362,362],"#Reference [​](#Reference)#{119}":[363,363],"#Reference [​](#Reference)#{120}":[364,364],"#Reference [​](#Reference)#{121}":[365,365],"#Reference [​](#Reference)#{122}":[366,366],"#Reference [​](#Reference)#{123}":[367,367],"#Reference [​](#Reference)#{124}":[368,368],"#Reference [​](#Reference)#{125}":[369,369],"#Reference [​](#Reference)#{126}":[370,370],"#Reference [​](#Reference)#{127}":[371,371],"#Reference [​](#Reference)#{128}":[372,372],"#Reference [​](#Reference)#{129}":[373,373],"#Reference [​](#Reference)#{130}":[374,374],"#Reference [​](#Reference)#{131}":[375,375],"#Reference [​](#Reference)#{132}":[376,376],"#Reference [​](#Reference)#{133}":[377,377],"#Reference [​](#Reference)#{134}":[378,378],"#Reference [​](#Reference)#{135}":[379,379],"#Reference [​](#Reference)#{136}":[380,380],"#Reference [​](#Reference)#{137}":[381,381],"#Reference [​](#Reference)#{138}":[382,382],"#Reference [​](#Reference)#{139}":[383,383],"#Reference [​](#Reference)#{140}":[384,384],"#Reference [​](#Reference)#{141}":[385,385],"#Reference [​](#Reference)#{142}":[386,386],"#Reference [​](#Reference)#{143}":[387,387],"#Reference [​](#Reference)#{144}":[388,388],"#Reference [​](#Reference)#{145}":[389,389],"#Reference [​](#Reference)#{146}":[390,390],"#Reference [​](#Reference)#{147}":[391,391],"#Reference [​](#Reference)#{148}":[392,392],"#Reference [​](#Reference)#{149}":[393,393],"#Reference [​](#Reference)#{150}":[394,394],"#Reference [​](#Reference)#{151}":[395,395],"#Reference [​](#Reference)#{152}":[396,396],"#Reference [​](#Reference)#{153}":[397,397],"#Reference [​](#Reference)#{154}":[398,398],"#Reference [​](#Reference)#{155}":[399,399],"#Reference [​](#Reference)#{156}":[400,400],"#Reference [​](#Reference)#{157}":[401,401],"#Reference [​](#Reference)#{158}":[402,402],"#Reference [​](#Reference)#{159}":[403,403],"#Reference [​](#Reference)#{160}":[404,404],"#Reference [​](#Reference)#{161}":[405,405],"#Reference [​](#Reference)#{162}":[406,406],"#Reference [​](#Reference)#{163}":[407,407],"#Reference [​](#Reference)#{164}":[408,408],"#Reference [​](#Reference)#{165}":[409,409],"#Reference [​](#Reference)#{166}":[410,410],"#Reference [​](#Reference)#{167}":[411,411],"#Reference [​](#Reference)#{168}":[412,412],"#Reference [​](#Reference)#{169}":[413,413],"#Reference [​](#Reference)#{170}":[414,414],"#Reference [​](#Reference)#{171}":[415,415],"#Reference [​](#Reference)#{172}":[416,416],"#Reference [​](#Reference)#{173}":[417,417],"#Reference [​](#Reference)#{174}":[418,418],"#Reference [​](#Reference)#{175}":[419,419],"#Reference [​](#Reference)#{176}":[420,420],"#Reference [​](#Reference)#{177}":[421,421],"#Reference [​](#Reference)#{178}":[422,422],"#Reference [​](#Reference)#{179}":[423,423],"#Reference [​](#Reference)#{180}":[424,424],"#Reference [​](#Reference)#{181}":[425,425],"#Reference [​](#Reference)#{182}":[426,426],"#Reference [​](#Reference)#{183}":[427,427],"#Reference [​](#Reference)#{184}":[428,428],"#Reference [​](#Reference)#{185}":[429,429],"#Reference [​](#Reference)#{186}":[430,430],"#Reference [​](#Reference)#{187}":[431,431],"#Reference [​](#Reference)#{188}":[432,432],"#Reference [​](#Reference)#{189}":[433,433],"#Reference [​](#Reference)#{190}":[434,434],"#Reference [​](#Reference)#{191}":[435,435],"#Reference [​](#Reference)#{192}":[436,436],"#Reference [​](#Reference)#{193}":[437,437],"#Reference [​](#Reference)#{194}":[438,438],"#Reference [​](#Reference)#{195}":[439,439],"#Reference [​](#Reference)#{196}":[440,440],"#Reference [​](#Reference)#{197}":[441,441],"#Reference [​](#Reference)#{198}":[442,442],"#Reference [​](#Reference)#{199}":[443,443],"#Reference [​](#Reference)#{200}":[444,444],"#Reference [​](#Reference)#{201}":[445,445],"#Reference [​](#Reference)#{202}":[446,446],"#Reference [​](#Reference)#{203}":[447,447],"#Reference [​](#Reference)#{204}":[448,448],"#Reference [​](#Reference)#{205}":[449,449],"#Reference [​](#Reference)#{206}":[450,450],"#Reference [​](#Reference)#{207}":[451,451],"#Reference [​](#Reference)#{208}":[452,452],"#Reference [​](#Reference)#{209}":[453,453],"#Reference [​](#Reference)#{210}":[454,454],"#Reference [​](#Reference)#{211}":[455,455],"#Reference [​](#Reference)#{212}":[456,456],"#Reference [​](#Reference)#{213}":[457,457],"#Reference [​](#Reference)#{214}":[458,458],"#Reference [​](#Reference)#{215}":[459,459],"#Reference [​](#Reference)#{216}":[460,460],"#Reference [​](#Reference)#{217}":[461,461],"#Reference [​](#Reference)#{218}":[462,462],"#Reference [​](#Reference)#{219}":[463,463],"#Reference [​](#Reference)#{220}":[464,464],"#Reference [​](#Reference)#{221}":[465,465],"#Reference [​](#Reference)#{222}":[466,466],"#Reference [​](#Reference)#{223}":[467,467],"#Reference [​](#Reference)#{224}":[468,468],"#Reference [​](#Reference)#{225}":[469,469],"#Reference [​](#Reference)#{226}":[470,470],"#Reference [​](#Reference)#{227}":[471,471],"#Reference [​](#Reference)#{228}":[472,472],"#Reference [​](#Reference)#{229}":[473,473],"#Reference [​](#Reference)#{230}":[474,474],"#Reference [​](#Reference)#{231}":[475,475],"#Reference [​](#Reference)#{232}":[476,476],"#Reference [​](#Reference)#{233}":[477,477],"#Reference [​](#Reference)#{234}":[478,478],"#Reference [​](#Reference)#{235}":[479,479],"#Reference [​](#Reference)#{236}":[480,480],"#Reference [​](#Reference)#{237}":[481,481],"#Reference [​](#Reference)#{238}":[482,482],"#Reference [​](#Reference)#{239}":[483,483],"#Reference [​](#Reference)#{240}":[484,484],"#Reference [​](#Reference)#{241}":[485,485],"#Reference [​](#Reference)#{242}":[486,486],"#Reference [​](#Reference)#{243}":[487,487],"#Reference [​](#Reference)#{244}":[488,488],"#Reference [​](#Reference)#{245}":[489,489],"#Reference [​](#Reference)#{246}":[490,490],"#Reference [​](#Reference)#{247}":[491,491],"#Reference [​](#Reference)#{248}":[492,492],"#Reference [​](#Reference)#{249}":[493,493],"#Reference [​](#Reference)#{250}":[494,494],"#Reference [​](#Reference)#{251}":[495,495],"#Reference [​](#Reference)#{252}":[496,497],"#Reference [​](#Reference)#{253}":[498,640],"#Reference [​](#Reference)#{254}":[641,642],"#Reference [​](#Reference)#{255}":[643,644],"#Reference [​](#Reference)#{256}":[645,646],"#Reference [​](#Reference)#{257}":[647,648],"#Reference [​](#Reference)#{258}":[649,650],"#Reference [​](#Reference)#{259}":[651,652],"#Reference [​](#Reference)#{260}":[653,654],"#Reference [​](#Reference)#{261}":[655,656],"#Reference [​](#Reference)#{262}":[657,658],"#Reference [​](#Reference)#{263}":[659,660],"#Reference [​](#Reference)#{264}":[661,662],"#Reference [​](#Reference)#{265}":[663,664],"#Reference [​](#Reference)#{266}":[665,666],"#Reference [​](#Reference)#{267}":[667,668],"#Reference [​](#Reference)#{268}":[669,671],"#Reference [​](#Reference)#{269}":[672,675],"#Reference [​](#Reference)#{270}":[676,677],"#Reference [​](#Reference)#{271}":[678,679],"#Reference [​](#Reference)#{272}":[680,681],"#Reference [​](#Reference)#{273}":[682,683],"#Reference [​](#Reference)#{274}":[684,685],"#Reference [​](#Reference)#{275}":[686,687],"#Reference [​](#Reference)#{276}":[688,689],"#Reference [​](#Reference)#{277}":[690,691],"#Reference [​](#Reference)#{278}":[692,693],"#Reference [​](#Reference)#{279}":[694,696],"#Reference [​](#Reference)#{280}":[697,734],"#Reference [​](#Reference)#{281}":[735,736],"#Reference [​](#Reference)#{282}":[737,738],"#Reference [​](#Reference)#{283}":[739,740],"#Reference [​](#Reference)#{284}":[741,742],"#Reference [​](#Reference)#{285}":[743,744],"#Reference [​](#Reference)#{286}":[745,746],"#Reference [​](#Reference)#{287}":[747,749],"#Reference [​](#Reference)#{288}":[750,751],"#Reference [​](#Reference)#{289}":[752,753],"#Reference [​](#Reference)#{290}":[754,755],"#Reference [​](#Reference)#{291}":[756,757],"#Reference [​](#Reference)#{292}":[758,759],"#Reference [​](#Reference)#{293}":[760,761],"#Reference [​](#Reference)#{294}":[762,764],"#Reference [​](#Reference)#{295}":[765,766],"#Reference [​](#Reference)#{296}":[767,768],"#Reference [​](#Reference)#{297}":[769,770],"#Reference [​](#Reference)#{298}":[771,772],"#Reference [​](#Reference)#{299}":[773,774],"#Reference [​](#Reference)#{300}":[775,776],"#Reference [​](#Reference)#{301}":[777,778],"#Reference [​](#Reference)#{302}":[779,780],"#Reference [​](#Reference)#{303}":[781,782],"#Reference [​](#Reference)#{304}":[783,784],"#Reference [​](#Reference)#{305}":[785,787],"#Reference [​](#Reference)#{306}":[788,789],"#Reference [​](#Reference)#{307}":[790,791],"#Reference [​](#Reference)#{308}":[792,850],"#Reference [​](#Reference)#{309}":[851,852],"#Reference [​](#Reference)#{310}":[853,854],"#Reference [​](#Reference)#{311}":[855,856],"#Reference [​](#Reference)#{312}":[857,858],"#Reference [​](#Reference)#{313}":[859,860],"#Reference [​](#Reference)#{314}":[861,862],"#Reference [​](#Reference)#{315}":[863,864],"#Reference [​](#Reference)#{316}":[865,866],"#Reference [​](#Reference)#{317}":[867,869],"#Reference [​](#Reference)#{318}":[870,885],"#Reference [​](#Reference)#{319}":[886,887],"#Reference [​](#Reference)#{320}":[888,895],"#Reference [​](#Reference)#{321}":[896,898],"#Reference [​](#Reference)#{322}":[899,1074],"#Reference [​](#Reference)#{323}":[1075,1076],"#Reference [​](#Reference)#{324}":[1077,1078],"#Reference [​](#Reference)#{325}":[1079,1080],"#Reference [​](#Reference)#{326}":[1081,1082],"#Reference [​](#Reference)#{327}":[1083,1084],"#Reference [​](#Reference)#{328}":[1085,1086],"#Reference [​](#Reference)#{329}":[1087,1088],"#Reference [​](#Reference)#{330}":[1089,1090],"#Reference [​](#Reference)#{331}":[1091,1093],"#Reference [​](#Reference)#{332}":[1094,1109],"#Reference [​](#Reference)#{333}":[1110,1111],"#Reference [​](#Reference)#{334}":[1112,1114],"#Reference [​](#Reference)#{335}":[1115,1130],"#Reference [​](#Reference)#{336}":[1131,1132],"#Reference [​](#Reference)#{337}":[1133,1134],"#Reference [​](#Reference)#{338}":[1135,1137],"#Reference [​](#Reference)#{339}":[1138,1139],"#Reference [​](#Reference)#{340}":[1140,1141],"#Reference [​](#Reference)#{341}":[1142,1230],"#Reference [​](#Reference)#{342}":[1231,1232],"#Reference [​](#Reference)#{343}":[1233,1235],"#Reference [​](#Reference)#{344}":[1236,1282],"#Reference [​](#Reference)#{345}":[1283,1284],"#Reference [​](#Reference)#{346}":[1285,1286],"#Reference [​](#Reference)#{347}":[1287,1288],"#Reference [​](#Reference)#{348}":[1289,1290],"#Reference [​](#Reference)#{349}":[1291,1293],"#Reference [​](#Reference)#{350}":[1294,1445],"#Reference [​](#Reference)#{351}":[1446,1447],"#Reference [​](#Reference)#{352}":[1448,1449],"#Reference [​](#Reference)#{353}":[1450,1452],"#Reference [​](#Reference)#{354}":[1453,1454],"#Reference [​](#Reference)#{355}":[1455,1456],"#Reference [​](#Reference)#{356}":[1457,1556],"#Reference [​](#Reference)#{357}":[1557,1558],"#Reference [​](#Reference)#{358}":[1559,1560],"#Reference [​](#Reference)#{359}":[1561,1563],"#Reference [​](#Reference)#{360}":[1564,1652],"#Reference [​](#Reference)#{361}":[1653,1654],"#Reference [​](#Reference)#{362}":[1655,1656],"#Reference [​](#Reference)#{363}":[1657,1658],"#Reference [​](#Reference)#{364}":[1659,1660],"#Reference [​](#Reference)#{365}":[1661,1662],"#Reference [​](#Reference)#{366}":[1663,1664],"#Reference [​](#Reference)#{367}":[1665,1666],"#Reference [​](#Reference)#{368}":[1667,1668],"#Reference [​](#Reference)#{369}":[1669,1671],"#Reference [​](#Reference)#{370}":[1672,1673],"#Reference [​](#Reference)#{371}":[1674,1675],"#Reference [​](#Reference)#{372}":[1676,1754],"#Reference [​](#Reference)#{373}":[1755,1756],"#Reference [​](#Reference)#{374}":[1757,1758],"#Reference [​](#Reference)#{375}":[1759,1760],"#Reference [​](#Reference)#{376}":[1761,1762],"#Reference [​](#Reference)#{377}":[1763,1764],"#Reference [​](#Reference)#{378}":[1765,1766],"#Reference [​](#Reference)#{379}":[1767,1768],"#Reference [​](#Reference)#{380}":[1769,1770],"#Reference [​](#Reference)#{381}":[1771,1773],"#Reference [​](#Reference)#{382}":[1774,1775],"#Reference [​](#Reference)#{383}":[1776,1777],"#Reference [​](#Reference)#{384}":[1778,1839],"#Reference [​](#Reference)#{385}":[1840,1841],"#Reference [​](#Reference)#{386}":[1842,1843],"#Reference [​](#Reference)#{387}":[1844,1845],"#Reference [​](#Reference)#{388}":[1846,1847],"#Reference [​](#Reference)#{389}":[1848,1849],"#Reference [​](#Reference)#{390}":[1850,1851],"#Reference [​](#Reference)#{391}":[1852,1853],"#Reference [​](#Reference)#{392}":[1854,1855],"#Reference [​](#Reference)#{393}":[1856,1857],"#Reference [​](#Reference)#{394}":[1858,1859],"#Reference [​](#Reference)#{395}":[1860,1861],"#Reference [​](#Reference)#{396}":[1862,1864],"#Reference [​](#Reference)#{397}":[1865,1868],"#Reference [​](#Reference)#{398}":[1869,1870],"#Reference [​](#Reference)#{399}":[1871,1872],"#Reference [​](#Reference)#{400}":[1873,1874],"#Reference [​](#Reference)#{401}":[1875,1994],"#Reference [​](#Reference)#{402}":[1995,1996],"#Reference [​](#Reference)#{403}":[1997,1998],"#Reference [​](#Reference)#{404}":[1999,2000],"#Reference [​](#Reference)#{405}":[2001,2002],"#Reference [​](#Reference)#{406}":[2003,2004],"#Reference [​](#Reference)#{407}":[2005,2006],"#Reference [​](#Reference)#{408}":[2007,2008],"#Reference [​](#Reference)#{409}":[2009,2010],"#Reference [​](#Reference)#{410}":[2011,2012],"#Reference [​](#Reference)#{411}":[2013,2014],"#Reference [​](#Reference)#{412}":[2015,2017],"#Reference [​](#Reference)#{413}":[2018,2020],"#Reference [​](#Reference)#{414}":[2021,2024],"#Reference [​](#Reference)#{415}":[2025,2026],"#Reference [​](#Reference)#{416}":[2027,2028],"#Reference [​](#Reference)#{417}":[2029,2030],"#Reference [​](#Reference)#{418}":[2031,2141],"#Reference [​](#Reference)#{419}":[2142,2143],"#Reference [​](#Reference)#{420}":[2144,2145],"#Reference [​](#Reference)#{421}":[2146,2147],"#Reference [​](#Reference)#{422}":[2148,2149],"#Reference [​](#Reference)#{423}":[2150,2151],"#Reference [​](#Reference)#{424}":[2152,2153],"#Reference [​](#Reference)#{425}":[2154,2155],"#Reference [​](#Reference)#{426}":[2156,2157],"#Reference [​](#Reference)#{427}":[2158,2159],"#Reference [​](#Reference)#{428}":[2160,2162],"#Reference [​](#Reference)#{429}":[2163,2165],"#Reference [​](#Reference)#{430}":[2166,2167],"#Reference [​](#Reference)#{431}":[2168,2169],"#Reference [​](#Reference)#{432}":[2170,2253],"#Reference [​](#Reference)#{433}":[2254,2255],"#Reference [​](#Reference)#{434}":[2256,2258],"#Reference [​](#Reference)#{435}":[2259,2260],"#Reference [​](#Reference)#{436}":[2261,2262],"#Reference [​](#Reference)#{437}":[2263,2264],"#Reference [​](#Reference)#{438}":[2265,2266],"#Reference [​](#Reference)#{439}":[2267,2268],"#Reference [​](#Reference)#{440}":[2269,2270],"#Reference [​](#Reference)#{441}":[2271,2272],"#Reference [​](#Reference)#{442}":[2273,2274],"#Reference [​](#Reference)#{443}":[2275,2276],"#Reference [​](#Reference)#{444}":[2277,2278],"#Reference [​](#Reference)#{445}":[2279,2280],"#Reference [​](#Reference)#{446}":[2281,2283],"#Reference [​](#Reference)#{447}":[2284,2287],"#Reference [​](#Reference)#{448}":[2288,2289],"#Reference [​](#Reference)#{449}":[2290,2293],"#Reference [​](#Reference)#{450}":[2294,2295],"#Reference [​](#Reference)#{451}":[2296,2363],"#Reference [​](#Reference)#{452}":[2364,2365],"#Reference [​](#Reference)#{453}":[2366,2367],"#Reference [​](#Reference)#{454}":[2368,2369],"#Reference [​](#Reference)#{455}":[2370,2371],"#Reference [​](#Reference)#{456}":[2372,2373],"#Reference [​](#Reference)#{457}":[2374,2375],"#Reference [​](#Reference)#{458}":[2376,2377],"#Reference [​](#Reference)#{459}":[2378,2379],"#Reference [​](#Reference)#{460}":[2380,2381],"#Reference [​](#Reference)#{461}":[2382,2383],"#Reference [​](#Reference)#{462}":[2384,2386],"#Reference [​](#Reference)#{463}":[2387,2388],"#Reference [​](#Reference)#{464}":[2389,2390],"#Reference [​](#Reference)#{465}":[2391,2471],"#Reference [​](#Reference)#{466}":[2472,2473],"#Reference [​](#Reference)#{467}":[2474,2475],"#Reference [​](#Reference)#{468}":[2476,2477],"#Reference [​](#Reference)#{469}":[2478,2479],"#Reference [​](#Reference)#{470}":[2480,2481],"#Reference [​](#Reference)#{471}":[2482,2483],"#Reference [​](#Reference)#{472}":[2484,2485],"#Reference [​](#Reference)#{473}":[2486,2487],"#Reference [​](#Reference)#{474}":[2488,2489],"#Reference [​](#Reference)#{475}":[2490,2491],"#Reference [​](#Reference)#{476}":[2492,2494],"#Reference [​](#Reference)#{477}":[2495,2496],"#Reference [​](#Reference)#{478}":[2497,2498],"#Reference [​](#Reference)#{479}":[2499,2581],"#Reference [​](#Reference)#{480}":[2582,2583],"#Reference [​](#Reference)#{481}":[2584,2585],"#Reference [​](#Reference)#{482}":[2586,2587],"#Reference [​](#Reference)#{483}":[2588,2589],"#Reference [​](#Reference)#{484}":[2590,2591],"#Reference [​](#Reference)#{485}":[2592,2593],"#Reference [​](#Reference)#{486}":[2594,2595],"#Reference [​](#Reference)#{487}":[2596,2597],"#Reference [​](#Reference)#{488}":[2598,2599],"#Reference [​](#Reference)#{489}":[2600,2609],"#Reference [​](#Reference)#{490}":[2610,2612],"#Reference [​](#Reference)#{491}":[2613,2616],"#Reference [​](#Reference)#{492}":[2617,2618],"#Reference [​](#Reference)#{493}":[2619,2622],"#Reference [​](#Reference)#{494}":[2623,2624],"#Reference [​](#Reference)#{495}":[2625,2704],"#Reference [​](#Reference)#{496}":[2705,2706],"#Reference [​](#Reference)#{497}":[2707,2708],"#Reference [​](#Reference)#{498}":[2709,2710],"#Reference [​](#Reference)#{499}":[2711,2712],"#Reference [​](#Reference)#{500}":[2713,2714],"#Reference [​](#Reference)#{501}":[2715,2716],"#Reference [​](#Reference)#{502}":[2717,2718],"#Reference [​](#Reference)#{503}":[2719,2720],"#Reference [​](#Reference)#{504}":[2721,2722],"#Reference [​](#Reference)#{505}":[2723,2724],"#Reference [​](#Reference)#{506}":[2725,2726],"#Reference [​](#Reference)#{507}":[2727,2728],"#Reference [​](#Reference)#{508}":[2729,2734],"#Reference [​](#Reference)#{509}":[2735,2737],"#Reference [​](#Reference)#{510}":[2738,2741],"#Reference [​](#Reference)#{511}":[2742,2743],"#Reference [​](#Reference)#{512}":[2744,2747],"#Reference [​](#Reference)#{513}":[2748,2749],"#Reference [​](#Reference)#{514}":[2750,2753],"#Reference [​](#Reference)#{515}":[2754,2755],"#Reference [​](#Reference)#{516}":[2756,2757],"#Reference [​](#Reference)#{517}":[2758,2760],"#Reference [​](#Reference)#{518}":[2761,2829],"#Reference [​](#Reference)#{519}":[2830,2831],"#Reference [​](#Reference)#{520}":[2832,2833],"#Reference [​](#Reference)#{521}":[2834,2835],"#Reference [​](#Reference)#{522}":[2836,2837],"#Reference [​](#Reference)#{523}":[2838,2839],"#Reference [​](#Reference)#{524}":[2840,2841],"#Reference [​](#Reference)#{525}":[2842,2843],"#Reference [​](#Reference)#{526}":[2844,2845],"#Reference [​](#Reference)#{527}":[2846,2847],"#Reference [​](#Reference)#{528}":[2848,2849],"#Reference [​](#Reference)#{529}":[2850,2851],"#Reference [​](#Reference)#{530}":[2852,2853],"#Reference [​](#Reference)#{531}":[2854,2855],"#Reference [​](#Reference)#{532}":[2856,2857],"#Reference [​](#Reference)#{533}":[2858,2860],"#Reference [​](#Reference)#{534}":[2861,2864],"#Reference [​](#Reference)#{535}":[2865,2866],"#Reference [​](#Reference)#{536}":[2867,2870],"#Reference [​](#Reference)#{537}":[2871,2872],"#Reference [​](#Reference)#{538}":[2873,2876],"#Reference [​](#Reference)#{539}":[2877,2878],"#Reference [​](#Reference)#{540}":[2879,2881],"#Reference [​](#Reference)#{541}":[2882,2965],"#Reference [​](#Reference)#{542}":[2966,2967],"#Reference [​](#Reference)#{543}":[2968,2969],"#Reference [​](#Reference)#{544}":[2970,2971],"#Reference [​](#Reference)#{545}":[2972,2973],"#Reference [​](#Reference)#{546}":[2974,2975],"#Reference [​](#Reference)#{547}":[2976,2977],"#Reference [​](#Reference)#{548}":[2978,2979],"#Reference [​](#Reference)#{549}":[2980,2981],"#Reference [​](#Reference)#{550}":[2982,2983],"#Reference [​](#Reference)#{551}":[2984,2985],"#Reference [​](#Reference)#{552}":[2986,2987],"#Reference [​](#Reference)#{553}":[2988,2989],"#Reference [​](#Reference)#{554}":[2990,2991],"#Reference [​](#Reference)#{555}":[2992,2993],"#Reference [​](#Reference)#{556}":[2994,2996],"#Reference [​](#Reference)#{557}":[2997,3000],"#Reference [​](#Reference)#{558}":[3001,3002],"#Reference [​](#Reference)#{559}":[3003,3006],"#Reference [​](#Reference)#{560}":[3007,3008],"#Reference [​](#Reference)#{561}":[3009,3012],"#Reference [​](#Reference)#{562}":[3013,3014],"#Reference [​](#Reference)#{563}":[3015,3017],"#Reference [​](#Reference)#{564}":[3018,3073],"#Reference [​](#Reference)#{565}":[3074,3075],"#Reference [​](#Reference)#{566}":[3076,3077],"#Reference [​](#Reference)#{567}":[3078,3080],"#Reference [​](#Reference)#{568}":[3081,3082],"#Reference [​](#Reference)#{569}":[3083,3084],"#Reference [​](#Reference)#{570}":[3085,3177],"#Reference [​](#Reference)#{571}":[3178,3179],"#Reference [​](#Reference)#{572}":[3180,3181],"#Reference [​](#Reference)#{573}":[3182,3183],"#Reference [​](#Reference)#{574}":[3184,3185],"#Reference [​](#Reference)#{575}":[3186,3187],"#Reference [​](#Reference)#{576}":[3188,3189],"#Reference [​](#Reference)#{577}":[3190,3191],"#Reference [​](#Reference)#{578}":[3192,3193],"#Reference [​](#Reference)#{579}":[3194,3195],"#Reference [​](#Reference)#{580}":[3196,3198],"#Reference [​](#Reference)#{581}":[3199,3220],"#Reference [​](#Reference)#{582}":[3221,3222],"#Reference [​](#Reference)#{583}":[3223,3224],"#Reference [​](#Reference)#{584}":[3225,3227],"#Reference [​](#Reference)#{585}":[3228,3270],"#Reference [​](#Reference)#{586}":[3271,3272],"#Reference [​](#Reference)#{587}":[3273,3274],"#Reference [​](#Reference)#{588}":[3275,3276],"#Reference [​](#Reference)#{589}":[3277,3278],"#Reference [​](#Reference)#{590}":[3279,3281],"#Reference [​](#Reference)#{591}":[3282,3283],"#Reference [​](#Reference)#{592}":[3284,3285],"#Reference [​](#Reference)#{593}":[3286,3338],"#Reference [​](#Reference)#{594}":[3339,3340],"#Reference [​](#Reference)#{595}":[3341,3342],"#Reference [​](#Reference)#{596}":[3343,3345],"#Reference [​](#Reference)#{597}":[3346,3490],"#Reference [​](#Reference)#{598}":[3491,3492],"#Reference [​](#Reference)#{599}":[3493,3495],"#Reference [​](#Reference)#{600}":[3496,3497],"#Reference [​](#Reference)#{601}":[3498,3499],"#Reference [​](#Reference)#{602}":[3500,3502],"#Reference [​](#Reference)#{603}":[3503,3546],"#Reference [​](#Reference)#{604}":[3547,3548],"#Reference [​](#Reference)#{605}":[3549,3550],"#Reference [​](#Reference)#{606}":[3551,3553],"#Reference [​](#Reference)#{607}":[3554,3555],"#Reference [​](#Reference)#{608}":[3556,3557],"#Reference [​](#Reference)#{609}":[3558,3559],"#Reference [​](#Reference)#{610}":[3560,3562],"#Reference [​](#Reference)#{611}":[3563,3612],"#Reference [​](#Reference)#{612}":[3613,3614],"#Reference [​](#Reference)#{613}":[3615,3616],"#Reference [​](#Reference)#{614}":[3617,3618],"#Reference [​](#Reference)#{615}":[3619,3620],"#Reference [​](#Reference)#{616}":[3621,3622],"#Reference [​](#Reference)#{617}":[3623,3624],"#Reference [​](#Reference)#{618}":[3625,3627],"#Reference [​](#Reference)#{619}":[3628,3629],"#Reference [​](#Reference)#{620}":[3630,3631],"#Reference [​](#Reference)#{621}":[3632,3633],"#Reference [​](#Reference)#{622}":[3634,3635],"#Reference [​](#Reference)#{623}":[3636,3638],"#Reference [​](#Reference)#{624}":[3639,3660],"#Reference [​](#Reference)#{625}":[3661,3662],"#Reference [​](#Reference)#{626}":[3663,3664],"#Reference [​](#Reference)#{627}":[3665,3666],"#Reference [​](#Reference)#{628}":[3667,3760],"#Reference [​](#Reference)#{629}":[3761,3762],"#Reference [​](#Reference)#{630}":[3763,3765],"#Reference [​](#Reference)#{631}":[3766,3771],"#Reference [​](#Reference)#{632}":[3772,3773],"#Reference [​](#Reference)#{633}":[3774,3775],"#Reference [​](#Reference)#{634}":[3776,3777],"#Reference [​](#Reference)#{635}":[3778,3846],"#Reference [​](#Reference)#{636}":[3847,3848],"#Reference [​](#Reference)#{637}":[3849,3850],"#Reference [​](#Reference)#{638}":[3851,3852],"#Reference [​](#Reference)#{639}":[3853,3855],"#Reference [​](#Reference)#{640}":[3856,3873],"#Reference [​](#Reference)#{641}":[3874,3875],"#Reference [​](#Reference)#{642}":[3876,3878],"#Reference [​](#Reference)#{643}":[3879,3880],"#Reference [​](#Reference)#{644}":[3881,3882],"#Reference [​](#Reference)#{645}":[3883,3910],"#Reference [​](#Reference)#{646}":[3911,3912],"#Reference [​](#Reference)#{647}":[3913,3914],"#Reference [​](#Reference)#{648}":[3915,3916],"#Reference [​](#Reference)#{649}":[3917,3919],"#Reference [​](#Reference)#{650}":[3920,5157],"#---frontmatter---":[3976,null]},"last_import":{"mtime":1712727978387,"size":208081,"at":1740449883580,"hash":"a0f6690f1833cb210cf20858b8d154ab32b95efbc8b8390b1ecb1ff43b162f9a"},"key":"PromptingTools.jl/Reference - PromptingTools.jl.md"},