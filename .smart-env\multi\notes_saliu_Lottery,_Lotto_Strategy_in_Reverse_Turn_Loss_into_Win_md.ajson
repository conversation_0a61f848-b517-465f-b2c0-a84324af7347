
"smart_sources:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md": {"path":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08952227,-0.02410809,-0.03370043,0.05836658,-0.06125567,0.07090731,0.01850503,0.01464362,0.03886431,0.01334085,-0.00718308,-0.03059139,0.07257146,-0.03680813,-0.02089291,-0.07083727,-0.03020317,0.03087594,-0.02364494,-0.01092814,0.06602357,-0.05143714,-0.02689487,-0.03758134,0.063067,-0.0142177,0.00928005,-0.06375571,-0.04382071,-0.23503986,0.01021167,0.00343883,-0.0315607,-0.05525677,-0.06449428,-0.00217268,-0.06546468,0.07071432,-0.03061131,0.06098838,0.02076028,0.01099587,0.02574694,-0.01008518,-0.00878415,0.02669052,-0.03407099,-0.01302013,0.05097494,0.00373336,-0.04355826,0.05115543,0.00681364,0.05134023,0.09737496,0.05864001,0.0867349,0.10984498,0.02037466,0.01715386,0.03706753,0.02863336,-0.19881894,0.02082155,-0.02249979,0.01853511,0.00311588,0.02200549,-0.0085343,0.07949152,-0.01080333,-0.01422045,-0.02572936,0.03563834,0.05006584,-0.02297175,-0.03408048,-0.05952281,-0.06471396,-0.01204453,0.01064622,-0.03161959,-0.02093279,-0.00263691,-0.00596806,0.02500079,0.02953196,0.03608788,0.06257832,-0.09454624,0.04604601,0.01602221,0.07271247,0.03074312,0.01551838,0.01041419,0.0329612,0.03214479,0.02190345,0.11388307,0.0161028,0.04247598,-0.00943123,0.01937689,0.09266789,-0.077154,-0.0194288,-0.04152942,0.01754262,0.03857274,0.02623658,0.0143302,0.08849087,-0.05862826,-0.00537408,0.04938809,0.03806348,0.04503285,0.00517633,-0.00852992,-0.03493889,0.00594423,0.0171313,0.00466337,-0.01994132,-0.01916961,0.00052932,0.06069172,0.026272,0.00525838,0.01330066,-0.03358464,-0.14687689,-0.06293635,-0.03897431,-0.00318302,0.00730018,-0.02950018,-0.00266768,-0.01968676,-0.02361902,-0.04289108,0.03262633,-0.08813393,-0.02387634,0.02882602,0.06152661,0.00352022,-0.00612241,0.00247898,-0.00354047,0.01093784,-0.02950265,-0.05677162,-0.01140671,-0.00579154,0.05488619,0.09154616,-0.02140931,0.0108536,-0.02340786,-0.03227523,-0.07685653,0.13426967,-0.02298293,-0.08153842,-0.02824052,0.04560933,-0.03048225,-0.07421374,-0.05123184,-0.02045892,-0.02026702,-0.05024154,0.10785927,0.02679794,-0.09758212,-0.12006118,-0.04237587,0.01297036,0.0004203,-0.03403253,-0.0338969,0.01841865,-0.03234414,-0.048124,0.0320194,-0.05186067,0.03754843,0.06586996,-0.06274682,-0.03196111,-0.05588979,0.0097019,0.00477056,-0.03994284,-0.02088295,-0.06559435,0.00923263,0.00821641,-0.04400875,-0.0141646,0.01248,-0.00043716,-0.05278917,0.05380176,0.03488012,-0.06468494,0.09207295,0.03959529,-0.03936718,0.04100544,0.01796589,0.03985781,-0.0126627,-0.01571136,0.02359382,0.03104614,-0.00576644,0.01250502,-0.00260467,0.03132484,-0.04685818,-0.17232104,-0.06916261,-0.05795786,-0.03012462,0.02613204,-0.05605911,-0.00095225,-0.01363112,0.03060607,0.04326988,0.0674149,-0.00413235,-0.03493684,0.07688155,0.00014646,0.01508764,-0.04249445,-0.00774463,-0.03229624,0.05157077,-0.03169375,0.02604012,-0.01124458,-0.05924515,0.0400903,-0.02324137,0.13880265,0.04628225,0.02974654,0.01791851,0.0584933,0.04771089,-0.0441897,-0.00960736,0.01620965,0.03329463,0.00892438,-0.0393792,-0.04241876,0.00431621,-0.07802031,0.00803983,-0.02246334,-0.06249478,-0.05312129,0.03173998,-0.06284137,0.03214077,0.02464966,0.10600187,0.05870381,0.00352965,0.03937501,0.03483969,0.09702141,0.0131354,-0.0738003,-0.04133052,-0.01747201,0.00596324,-0.03370394,0.01382354,0.02452182,-0.05484597,0.05402688,0.02356608,-0.02107477,-0.04451033,0.00370944,-0.00607012,-0.01142955,0.09158815,0.02716949,-0.01575188,-0.04358943,0.04405175,0.03322516,-0.08782937,-0.03311989,-0.00340423,0.01581637,-0.07947758,0.07311036,0.03969958,0.06908621,0.05537878,0.03687174,-0.0181537,0.00945958,-0.0147916,-0.03143112,0.02391362,-0.04954766,0.01808291,0.02159314,-0.01369226,-0.25885621,-0.00395926,-0.0008388,0.0655627,-0.00324627,0.01947488,0.02164087,-0.01686639,0.03479825,-0.0405528,0.03782298,0.04051599,-0.00725187,-0.01571324,0.01335775,-0.00478457,-0.00671289,-0.02740139,0.07364317,0.02709268,0.05583759,0.05553289,0.21778665,-0.009586,-0.02532138,-0.00380258,-0.02215299,0.01503493,-0.02654232,0.05936323,0.01358846,-0.00467842,0.08421271,0.00478841,-0.00395983,0.01127241,-0.0258148,0.0508571,-0.00619687,0.00588561,-0.11513711,-0.03191494,0.01540224,-0.00000156,0.15002345,-0.00415686,-0.00678995,-0.08994655,0.03436041,0.04613264,-0.08116112,-0.02867028,-0.0572724,-0.01283528,-0.00538408,0.06800833,-0.00493918,-0.02417419,0.04662141,0.01838333,0.04372069,0.02288617,0.05705769,0.01929538,0.01837646],"last_embed":{"hash":"1ah3jt6","tokens":477}}},"last_read":{"hash":"1ah3jt6","at":1753423513553},"class_name":"SmartSource","last_import":{"mtime":1753363610689,"size":18338,"at":1753423416500,"hash":"1ah3jt6"},"blocks":{"#---frontmatter---":[1,6],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win":[8,178],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#{1}":[10,15],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win##I. [A Brief History of Lottery Strategies in _Reverse_](https://saliu.com/reverse-strategy.html#history)":[16,24],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win##I. [A Brief History of Lottery Strategies in _Reverse_](https://saliu.com/reverse-strategy.html#history)#{1}":[17,24],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>I. A Brief History of Lottery Strategies in <i>Reverse</i></u>":[25,36],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>I. A Brief History of Lottery Strategies in <i>Reverse</i></u>#{1}":[27,36],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>":[37,56],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>#{1}":[39,40],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>#{2}":[41,42],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>#{3}":[43,56],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>III. Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads, etc.</u>":[57,78],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>III. Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads, etc.</u>#{1}":[59,78],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>IV. Reversed Lottery Strategy Example: Skips, Decades, Frequencies</u>":[79,103],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>IV. Reversed Lottery Strategy Example: Skips, Decades, Frequencies</u>#{1}":[81,103],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>":[104,143],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{1}":[106,129],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{2}":[130,130],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{3}":[131,131],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{4}":[132,132],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{5}":[133,134],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{6}":[135,143],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>":[144,178],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{1}":[146,147],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{2}":[148,148],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{3}":[149,149],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{4}":[150,150],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{5}":[151,151],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{6}":[152,152],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{7}":[153,153],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{8}":[154,154],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{9}":[155,155],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{10}":[156,156],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{11}":[157,157],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{12}":[158,158],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{13}":[159,159],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{14}":[160,160],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{15}":[161,161],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{16}":[162,162],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{17}":[163,163],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{18}":[164,164],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{19}":[165,165],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{20}":[166,166],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{21}":[167,167],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{22}":[168,168],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{23}":[169,169],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{24}":[170,170],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{25}":[171,172],"#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{26}":[173,178]},"outlinks":[{"title":"Reversed lottery systems, strategies aka LIE elimination are the creation of Ion Saliu in the 1990s.","target":"https://saliu.com/images/lottery.gif","line":14},{"title":"A Brief History of Lottery Strategies in _Reverse_","target":"https://saliu.com/reverse-strategy.html#history","line":16},{"title":"Reversed Lotto Strategy _Redivivus_","target":"https://saliu.com/reverse-strategy.html#strategy","line":17},{"title":"Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads","target":"https://saliu.com/reverse-strategy.html#reverse","line":18},{"title":"Reversed Lottery Strategy Example: Skips, Decades, Frequencies","target":"https://saliu.com/reverse-strategy.html#skips","line":19},{"title":"A Lottery Strategy is _Trinity_: _Straight_, _Purge_, _Reversed_","target":"https://saliu.com/reverse-strategy.html#trinity","line":20},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/reverse-strategy.html#links","line":21},{"title":"In lie or NOT elimination lottery strategy we rely on having non-winners in the first output.","target":"https://saliu.com/images/lotto.gif","line":23},{"title":"New lotto strategy in reverse, lottery 2010, 2011.","target":"https://saliu.com/HLINE.gif","line":35},{"title":"_**Removing Lotto Combinations**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":41},{"title":"Code name of this reversed lottery strategy is LIE lotto strategy.","target":"https://saliu.com/ScreenImgs/reverse-strategy.gif","line":55},{"title":"This is a powerful lottery strategy: reverse the loss to win.","target":"https://saliu.com/HLINE.gif","line":77},{"title":"This is a LIE strategy for pick-3 digit lottery.","target":"https://saliu.com/ScreenImgs/loss-win-pick3.gif","line":90},{"title":"A lotto strategy is many reductions in one: Straight software, purge, reversed.","target":"https://saliu.com/HLINE.gif","line":102},{"title":"We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing.","target":"https://saliu.com/HLINE.gif","line":128},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":132},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":133},{"title":"This program applies the LIE ELIMINATION reversed lottery strategy feature introduced in Bright lotto software, in combination generators.","target":"https://saliu.com/ScreenImgs/LieIDLotto.gif","line":140},{"title":"\n\n## <u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>\n\n","target":"https://saliu.com/content/lottery.html","line":142},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":148},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":150},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":152},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":154},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":155},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":156},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":157},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":158},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":159},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":160},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":161},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":162},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":163},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":164},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":165},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":166},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":167},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":168},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":169},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":170},{"title":"**Lottery Software, Lotto Software Applications**","target":"https://saliu.com/infodown.html","line":171},{"title":"Apply the lottery strategy in reverse: Not-lose to win.","target":"https://saliu.com/HLINE.gif","line":173},{"title":"Forums","target":"https://forums.saliu.com/","line":175},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":175},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":175},{"title":"Contents","target":"https://saliu.com/content/index.html","line":175},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":175},{"title":"Home","target":"https://saliu.com/index.htm","line":175},{"title":"Search","target":"https://saliu.com/Search.htm","line":175},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":175},{"title":"This impressive lottery software and strategy are based on the science of logic.","target":"https://saliu.com/images/HLINE.gif","line":177}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["strategy","reverse strategy","strategies","lottery","lotto","hit","miss","win","lose","drawing","file","software","LIE files"],"source":"https://saliu.com/reverse-strategy.html","author":null}},"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09980203,-0.04974435,-0.02263038,0.04187759,-0.06716447,0.0833263,0.01155558,0.01824928,0.00944952,0.01046321,-0.02010317,-0.02170753,0.06435799,-0.02629932,-0.02170511,-0.05709281,-0.02647169,0.01646003,-0.0158694,0.00374966,0.06750368,-0.0197586,-0.05517734,-0.04776299,0.04146406,-0.02287562,0.01157109,-0.05791912,-0.06188118,-0.17420812,0.0132117,-0.00367644,-0.03269662,-0.03934553,-0.04868031,-0.02092021,-0.04983754,0.06189695,-0.01176788,0.0563979,0.01850112,0.00901333,-0.01256914,-0.00688157,0.02515942,0.00427911,-0.02706871,0.0084603,0.06725958,0.01886052,-0.01966543,0.0601588,0.01001528,0.04586737,0.11990345,0.02534171,0.08056097,0.09858854,0.06785592,0.03675506,0.0323851,0.03124906,-0.20739628,0.00821067,-0.03880741,0.02486108,-0.00279013,0.02205528,-0.02780271,0.06869921,0.01015353,-0.03349439,-0.01827866,0.03238906,0.05429417,-0.00807246,-0.03098639,-0.05591179,-0.05290924,-0.01904755,0.00188468,-0.02276577,-0.03824898,-0.00817577,-0.01077547,0.01902636,0.04651067,0.09659696,0.08446631,-0.06524684,0.04387116,0.00686754,0.09098218,0.02001769,-0.02671787,0.01270916,0.03882312,0.03230752,0.00969107,0.12062884,-0.03109833,0.02624692,0.00410649,0.00977813,0.06278741,-0.08147152,-0.00536015,-0.02603813,0.02063346,0.04299644,0.0071954,0.03028226,0.10244673,-0.08556399,0.0224075,0.03471396,0.01816448,0.05743856,-0.02716435,0.01414502,-0.0269033,0.01316606,0.00042579,-0.00372031,-0.004339,-0.00587418,0.00212022,0.06526949,0.03264379,0.00748496,0.00287345,-0.01091619,-0.15160371,-0.0314431,-0.01065572,-0.00998344,0.00240393,-0.0620847,0.01102637,-0.06091261,-0.01493238,-0.03347589,0.03633654,-0.08026008,-0.01430317,0.03863652,0.0566545,-0.02472837,0.02014103,-0.01054034,-0.00632382,-0.00235749,0.00323672,-0.06048474,-0.01128468,0.00256006,0.06979883,0.09453762,-0.01367757,-0.00810539,-0.00306387,-0.02967379,-0.06182153,0.10896562,-0.0251534,-0.10109531,-0.03162022,0.03965649,-0.01422468,-0.08653896,-0.05805677,-0.01786873,-0.02452359,-0.05068284,0.12525329,0.02271788,-0.03288511,-0.10925818,-0.03234088,-0.00835716,0.00962775,-0.02099399,-0.02583586,0.01416863,-0.05116251,-0.03311696,0.00055743,-0.0594906,0.05864285,0.05891185,-0.05010654,-0.07150598,-0.06021734,0.00505432,0.01811483,-0.04406497,-0.02971977,-0.05185235,0.02509023,-0.00383658,-0.02767868,-0.02719152,-0.01700693,0.01043578,-0.05345742,0.01989788,0.02659863,-0.0849512,0.08722702,0.03368473,-0.02350982,0.03422346,0.04983978,0.0573532,-0.0341783,-0.02229305,0.00281371,0.01924483,-0.00089792,0.01579291,-0.02083084,-0.00269544,-0.04527809,-0.1886407,-0.04722252,-0.05740864,-0.03565455,-0.01314474,-0.04131564,0.00673616,-0.01932354,0.04760699,0.06043631,0.09135382,-0.01004358,-0.03569275,0.03973774,0.00791438,0.00262105,-0.05563508,-0.03396615,-0.00668763,0.04287673,-0.02119195,0.0155947,-0.03286034,-0.08603256,0.05760186,0.01351129,0.14224125,0.06920907,0.03387291,0.00824316,0.08141888,0.04144372,-0.02098593,-0.04859,0.02528316,0.00787026,0.01731984,-0.00577408,-0.06653669,-0.01594162,-0.08331537,0.00212393,0.01575379,-0.07779256,-0.02895458,0.01744449,-0.02784795,0.02083454,0.00750923,0.10318756,0.04280348,-0.02007865,0.03391465,0.0712672,0.0838689,0.00955944,-0.05811679,-0.04774728,-0.00900674,-0.02673445,-0.02888383,0.00425947,0.03574621,-0.07604621,0.00941456,-0.00300315,-0.0062055,-0.05669494,-0.0203946,-0.01246076,0.01194817,0.04445652,0.03104395,-0.02604842,-0.03143061,0.0314738,0.02323117,-0.05416017,-0.01504619,-0.02916702,0.02535507,-0.07465023,0.09332802,0.03005186,0.07852811,0.06781147,0.01005215,-0.03818551,0.02626956,-0.00765911,-0.0139729,0.00903761,-0.04091156,-0.00764134,0.0267596,-0.03575117,-0.2645981,0.01871274,0.01114206,0.0770483,-0.00375418,0.02055173,0.01548182,0.00220555,0.00777643,-0.03714678,0.04920842,0.04777838,-0.01638975,-0.00294729,0.00709765,-0.02244558,0.00635447,-0.01567142,0.09104431,0.05033823,0.03214904,0.05635089,0.23740932,0.00600971,-0.01550826,-0.00694935,-0.01758411,0.01584835,0.02051787,0.05517817,0.02165647,-0.00880751,0.0852508,0.01787735,0.01511045,0.01424993,-0.02874134,0.06457502,0.01280283,0.00607414,-0.10036436,-0.00699469,-0.01081926,0.00743925,0.12179448,0.00597215,-0.02951113,-0.07131666,-0.01253417,0.01485334,-0.07828476,-0.04433821,-0.07021067,-0.03185085,-0.0365925,0.08396694,0.02456512,-0.03314361,0.06261107,0.00350919,0.07189181,-0.00909271,0.0768121,0.0198131,0.01138162],"last_embed":{"hash":"e4eioe","tokens":96}}},"text":null,"length":0,"last_read":{"hash":"e4eioe","at":1753423510846},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#---frontmatter---","lines":[1,6],"size":216,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08530594,-0.02021893,-0.03613681,0.05740423,-0.06025404,0.0755806,0.0243472,0.01469333,0.02611725,0.01521478,0.00017138,-0.01931414,0.06771386,-0.03771364,-0.01661184,-0.06513982,-0.03471546,0.02972097,-0.03518927,-0.01476173,0.06392895,-0.05733249,-0.02896797,-0.0305737,0.06994196,-0.01439142,0.00653297,-0.06082439,-0.04677939,-0.24214974,0.01328861,0.00512684,-0.02884076,-0.05056104,-0.06227708,0.00997049,-0.06784357,0.07172842,-0.04264673,0.05672576,0.02327179,0.009012,0.03986859,-0.01235777,-0.01148935,0.02349203,-0.03179793,-0.01739248,0.05787536,0.00527094,-0.03596842,0.06116532,0.00343287,0.04732668,0.07929727,0.06171977,0.10253837,0.11138746,0.01887567,0.00734442,0.03111005,0.03050191,-0.20039463,0.01350702,-0.01510938,0.01465694,-0.00021986,0.01548146,-0.00163715,0.06902241,-0.00842422,-0.01122351,-0.02647433,0.03414099,0.04898153,-0.01971599,-0.02858843,-0.04955776,-0.06004664,-0.01424471,0.0108036,-0.02080213,-0.01761599,0.00135388,0.00209647,0.02110843,0.0210794,0.03054484,0.05743648,-0.1049863,0.05123319,0.02299517,0.07051274,0.03964731,0.01542536,0.00383525,0.0264398,0.04229245,0.03930143,0.12100038,0.03063675,0.0377247,-0.01387021,0.02930366,0.08329535,-0.07475475,-0.02606237,-0.03765455,0.01821313,0.04533523,0.02120973,0.02192847,0.08699912,-0.04709725,-0.01567152,0.04297003,0.02910578,0.04347783,0.00156417,-0.01214505,-0.03432227,0.01326014,0.01607237,0.01075751,-0.02307297,-0.02556571,0.00706183,0.05894969,0.03309181,0.00286666,0.00351375,-0.03117149,-0.14136025,-0.06901748,-0.03675098,0.001273,0.0105634,-0.03096538,0.00595868,-0.0129156,-0.02414277,-0.04033174,0.02703735,-0.0763815,-0.02628592,0.02936954,0.05676235,0.01193778,-0.00746469,0.01448156,0.00259161,0.00793211,-0.03273657,-0.05654338,-0.00807397,-0.00564599,0.04668661,0.08275038,-0.02478442,0.01959195,-0.02237582,-0.03379515,-0.06475033,0.13923472,-0.01934972,-0.07784321,-0.03572349,0.03485424,-0.02367465,-0.06382465,-0.04810041,-0.0106521,-0.01455082,-0.05205019,0.09537601,0.0276627,-0.10352173,-0.11309457,-0.03889255,0.00966027,-0.00517733,-0.04370227,-0.03526902,0.01533763,-0.0286453,-0.055828,0.02459379,-0.04790093,0.0387649,0.08066431,-0.06466793,-0.02938771,-0.05969016,0.00830625,-0.0018316,-0.04474921,-0.01913761,-0.06218464,0.00803962,0.00794771,-0.0568814,-0.0192839,0.0251051,0.00360311,-0.0553762,0.06200247,0.02811618,-0.05850676,0.10048985,0.04258539,-0.04943794,0.03754647,0.01280861,0.03524084,-0.01996709,-0.0117451,0.02143465,0.01923453,-0.0082389,0.00827052,0.0114168,0.023092,-0.04364623,-0.17989102,-0.06208994,-0.06070447,-0.02182828,0.04011044,-0.06055437,0.00635204,-0.01995348,0.03377178,0.04440008,0.05957685,-0.00899504,-0.0380316,0.08655605,-0.00333805,0.0185176,-0.02766549,-0.01695007,-0.03252271,0.05240896,-0.03511267,0.02435521,-0.01132304,-0.05207743,0.04580269,-0.02708764,0.14232168,0.03524631,0.0307088,0.01262799,0.04978784,0.04113967,-0.04741177,-0.00563596,0.01258824,0.03064319,0.00965488,-0.0479313,-0.04011668,0.00686429,-0.08572136,0.0048182,-0.02750617,-0.05534408,-0.05119952,0.02256818,-0.06097772,0.0323133,0.03601543,0.10470929,0.06261674,-0.00062194,0.03272992,0.03157185,0.08849032,0.00739422,-0.06977525,-0.03123943,-0.01901243,0.01221849,-0.02927097,0.02710045,0.01890685,-0.04380245,0.04817613,0.02080812,-0.02546208,-0.04312633,0.00833852,-0.01288249,-0.0187154,0.10176393,0.02883288,-0.01144229,-0.03578427,0.04862785,0.03346434,-0.10242191,-0.03383881,-0.01112221,0.01723705,-0.08232342,0.0714498,0.03581372,0.06712021,0.05073969,0.04327269,-0.0169219,-0.00051622,-0.02275731,-0.03680276,0.0324246,-0.04881757,0.02175419,0.02173391,-0.01694749,-0.24646696,0.00353569,-0.01312056,0.07492968,0.00440677,0.01516053,0.01926719,-0.00832475,0.05227628,-0.03792562,0.04079196,0.03694045,0.00112947,-0.02193697,0.01999575,-0.0045852,-0.00884179,-0.03606395,0.07333326,0.01607368,0.05988206,0.05156547,0.21323813,-0.0118405,-0.02510673,-0.00341304,-0.01790768,0.01799573,-0.04139561,0.0560513,0.02013146,-0.01068047,0.07316484,0.00542762,-0.00157567,0.00222199,-0.03213994,0.0437944,-0.013705,0.01067461,-0.1248634,-0.04025348,0.01067487,-0.00382703,0.15879162,0.00040453,-0.00973057,-0.0940077,0.04516538,0.06113632,-0.08205072,-0.02624452,-0.05485497,-0.01359448,0.00006858,0.06715261,-0.01901816,-0.02556443,0.04045843,0.01107218,0.04162028,0.02248013,0.05729565,0.01504938,0.01972601],"last_embed":{"hash":"14fmfo9","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"14fmfo9","at":1753423510886},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win","lines":[8,178],"size":18098,"outlinks":[{"title":"Reversed lottery systems, strategies aka LIE elimination are the creation of Ion Saliu in the 1990s.","target":"https://saliu.com/images/lottery.gif","line":7},{"title":"A Brief History of Lottery Strategies in _Reverse_","target":"https://saliu.com/reverse-strategy.html#history","line":9},{"title":"Reversed Lotto Strategy _Redivivus_","target":"https://saliu.com/reverse-strategy.html#strategy","line":10},{"title":"Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads","target":"https://saliu.com/reverse-strategy.html#reverse","line":11},{"title":"Reversed Lottery Strategy Example: Skips, Decades, Frequencies","target":"https://saliu.com/reverse-strategy.html#skips","line":12},{"title":"A Lottery Strategy is _Trinity_: _Straight_, _Purge_, _Reversed_","target":"https://saliu.com/reverse-strategy.html#trinity","line":13},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/reverse-strategy.html#links","line":14},{"title":"In lie or NOT elimination lottery strategy we rely on having non-winners in the first output.","target":"https://saliu.com/images/lotto.gif","line":16},{"title":"New lotto strategy in reverse, lottery 2010, 2011.","target":"https://saliu.com/HLINE.gif","line":28},{"title":"_**Removing Lotto Combinations**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":34},{"title":"Code name of this reversed lottery strategy is LIE lotto strategy.","target":"https://saliu.com/ScreenImgs/reverse-strategy.gif","line":48},{"title":"This is a powerful lottery strategy: reverse the loss to win.","target":"https://saliu.com/HLINE.gif","line":70},{"title":"This is a LIE strategy for pick-3 digit lottery.","target":"https://saliu.com/ScreenImgs/loss-win-pick3.gif","line":83},{"title":"A lotto strategy is many reductions in one: Straight software, purge, reversed.","target":"https://saliu.com/HLINE.gif","line":95},{"title":"We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing.","target":"https://saliu.com/HLINE.gif","line":121},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":125},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":126},{"title":"This program applies the LIE ELIMINATION reversed lottery strategy feature introduced in Bright lotto software, in combination generators.","target":"https://saliu.com/ScreenImgs/LieIDLotto.gif","line":133},{"title":"\n\n## <u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>\n\n","target":"https://saliu.com/content/lottery.html","line":135},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":141},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":143},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":145},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":147},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":148},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":149},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":150},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":151},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":152},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":153},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":154},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":155},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":156},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":157},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":158},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":159},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":160},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":161},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":162},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":163},{"title":"**Lottery Software, Lotto Software Applications**","target":"https://saliu.com/infodown.html","line":164},{"title":"Apply the lottery strategy in reverse: Not-lose to win.","target":"https://saliu.com/HLINE.gif","line":166},{"title":"Forums","target":"https://forums.saliu.com/","line":168},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":168},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":168},{"title":"Contents","target":"https://saliu.com/content/index.html","line":168},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":168},{"title":"Home","target":"https://saliu.com/index.htm","line":168},{"title":"Search","target":"https://saliu.com/Search.htm","line":168},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":168},{"title":"This impressive lottery software and strategy are based on the science of logic.","target":"https://saliu.com/images/HLINE.gif","line":170}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06774092,-0.01572352,-0.02596254,0.05114033,-0.05714161,0.07167468,0.05741626,0.01559438,0.0397578,0.0247976,0.00421571,-0.02298677,0.0680128,-0.02178284,-0.01559331,-0.07090419,-0.02626219,0.0313843,-0.04015474,-0.0190128,0.0771153,-0.04575326,-0.04507217,-0.04959328,0.06119171,-0.02473457,0.00462536,-0.05467592,-0.03671489,-0.22388704,0.01466689,0.00676303,-0.02313251,-0.04976825,-0.06022931,-0.00400447,-0.06918547,0.07209854,-0.03531599,0.06165769,0.02522363,0.00093862,0.03003956,-0.02191466,-0.00026068,0.02206955,-0.01852042,-0.00837623,0.06815328,0.00589591,-0.00925624,0.06678367,0.02082978,0.05413896,0.08380364,0.05128781,0.08559803,0.11215592,0.04551294,0.00479252,0.0282444,0.0201188,-0.18493566,0.00321834,-0.02148345,0.00804178,0.01810285,0.02448925,-0.01076451,0.08159178,-0.00578058,-0.01350864,-0.02680888,0.03490602,0.05437578,-0.01717615,-0.03476846,-0.04476713,-0.04814008,0.00588664,0.00591466,-0.05450317,-0.01080444,0.00137268,-0.00018748,0.00300864,0.02634873,0.04027121,0.06572001,-0.10389674,0.04446585,0.01897133,0.06745975,0.03401196,0.00316726,0.01497034,0.01704823,0.03610817,0.0212204,0.14790685,0.03510668,0.03528546,-0.01427115,0.01618643,0.07960246,-0.07597797,-0.03277345,-0.02062395,0.0178803,0.04466246,0.01983862,0.01367941,0.08472674,-0.04412323,-0.02014706,0.05885422,0.0419258,0.04782789,-0.01099314,-0.00374588,-0.04196899,0.01350391,0.01437046,0.00119235,-0.02211665,-0.00806752,-0.00071284,0.07151628,0.01956077,-0.00873412,0.01968124,-0.03449669,-0.13205409,-0.07012413,-0.02077734,-0.00387267,0.01988912,-0.04754507,0.01832247,-0.01567428,-0.02620143,-0.00638662,0.02649032,-0.09127997,-0.01493694,0.04171953,0.06258208,0.00163384,-0.00755552,-0.01066273,-0.01113393,-0.00126487,-0.0306885,-0.05705749,-0.00332107,0.002452,0.02252755,0.08146827,-0.01936697,0.00538728,-0.02727146,-0.0050583,-0.0872398,0.14280514,-0.03442908,-0.03733194,-0.03759456,0.00914139,-0.02727799,-0.05990401,-0.07377817,-0.00487151,-0.02005189,-0.05298088,0.0996677,0.01372482,-0.10599516,-0.11301552,-0.05907453,-0.00466727,0.0230029,-0.02562435,-0.02485512,0.02797108,-0.02669416,-0.05883676,0.01194154,-0.04393748,0.04980736,0.08280407,-0.05407762,-0.01202635,-0.05761867,0.00892587,0.01491442,-0.05701996,-0.01426956,-0.06416994,-0.00801538,-0.00149001,-0.04703273,-0.00709806,0.0051178,0.00679299,-0.05160803,0.05907786,0.02281762,-0.0578198,0.09297422,0.05611356,-0.05985863,0.043081,0.01540711,0.03541236,-0.01672404,-0.01706428,0.01883582,0.02276204,-0.00501203,0.00390435,0.00391429,0.03914789,-0.03068379,-0.1895504,-0.07080971,-0.04747178,-0.02210824,0.04174723,-0.0547262,-0.01819506,-0.02029761,0.03394803,0.0370202,0.06482172,-0.02320628,-0.02661479,0.08514062,-0.0099271,0.02492844,-0.05461401,-0.01900264,-0.01856645,0.04460955,-0.02300999,0.02916435,-0.0068609,-0.07248144,0.03342661,0.00449247,0.14638908,0.04213733,0.05013407,0.00582404,0.04616453,0.03522424,-0.04332105,0.00038884,0.00536005,0.02142795,-0.005143,-0.03574471,-0.04652272,0.00274227,-0.07060232,0.00363189,-0.03992688,-0.05271108,-0.03869691,0.03760427,-0.06554343,0.04628507,0.03035383,0.09389544,0.06169499,-0.00068264,0.03618264,0.03781838,0.09944651,0.00727666,-0.06851332,-0.03361065,-0.01811446,-0.00365773,-0.02904863,0.02120773,0.01628909,-0.04441396,0.03863182,0.01894271,-0.01800159,-0.04782654,-0.00181154,-0.00585537,-0.02183663,0.08431169,0.01253495,-0.01156393,-0.0393308,0.05838204,0.03955336,-0.09366743,-0.0556627,-0.01346849,0.04612154,-0.07609805,0.07377405,0.03109561,0.06439763,0.04299964,0.01907039,-0.01124092,-0.02040507,-0.02144553,-0.03569244,0.03190453,-0.06103964,0.01246245,-0.00322721,-0.02835271,-0.24785525,0.01238781,-0.02951952,0.07750818,-0.01348731,0.02044308,0.01252468,0.00475729,0.03734377,-0.03625327,0.04226676,0.03773995,-0.00081288,-0.0032473,0.02300339,-0.01362189,0.01007108,-0.04142882,0.08110019,0.00796231,0.0562226,0.04708282,0.2347146,-0.0069467,-0.01499476,-0.0153171,-0.01208128,-0.00351215,-0.02584619,0.05216243,0.02812749,-0.02474041,0.06439652,0.01601643,0.01494299,-0.00425381,-0.04455603,0.07152557,0.00452094,0.00065408,-0.1262116,-0.03661925,0.00733473,-0.00613128,0.13408746,-0.00702901,-0.02869961,-0.08431076,0.04326347,0.05006433,-0.08809597,-0.01004777,-0.0660184,-0.03188037,-0.01830878,0.06427094,-0.01343032,-0.03198139,0.04556348,0.01625036,0.04459392,0.03530928,0.0432068,-0.00735762,0.00743871],"last_embed":{"hash":"wqnaxy","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"wqnaxy","at":1753423511087},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#{1}","lines":[10,15],"size":352,"outlinks":[{"title":"Reversed lottery systems, strategies aka LIE elimination are the creation of Ion Saliu in the 1990s.","target":"https://saliu.com/images/lottery.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win##I. [A Brief History of Lottery Strategies in _Reverse_](https://saliu.com/reverse-strategy.html#history)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08734225,-0.03248907,-0.03002334,0.04503115,-0.07648851,0.08555915,0.02701221,0.01577311,0.00685937,-0.0056933,-0.01404081,-0.02915104,0.05953354,-0.04996895,-0.0042749,-0.05821589,-0.03775381,0.04679436,-0.02092965,-0.00538955,0.07213493,-0.03493242,-0.0374288,-0.02983362,0.05842504,-0.01259712,0.00530526,-0.05928612,-0.04632743,-0.22005151,0.02675406,-0.00398174,-0.05506612,-0.04449213,-0.05713253,-0.00720344,-0.06805497,0.06163865,-0.05629817,0.0695064,0.02763072,0.00800117,0.01655524,0.00252197,0.01051218,0.00352371,-0.02818983,-0.01099027,0.0580034,-0.00068426,-0.03970853,0.08234323,-0.00096922,0.03311384,0.09086026,0.06561383,0.10958609,0.12615196,0.01565941,0.01531391,0.02958238,0.05468812,-0.19857976,0.0190922,0.00688858,0.03125357,0.01174211,0.0196278,-0.01266139,0.08059835,-0.00731841,-0.02790074,-0.01671125,0.03959567,0.03561541,-0.01240876,-0.04076432,-0.05524431,-0.05470726,-0.01976565,0.00175029,-0.00442494,-0.02291744,-0.02013884,0.01282722,0.03316656,0.03070447,0.03285258,0.069529,-0.08383565,0.04475575,0.02679251,0.05727177,0.01835078,0.02988954,0.0114947,0.03439356,0.01385837,0.05041808,0.11012957,0.00427809,0.03344384,-0.02671211,0.02732697,0.0771746,-0.09341905,-0.01279292,-0.04863394,0.03947428,0.04142595,0.01763251,0.03111356,0.09191334,-0.04158013,0.00108098,0.03519163,0.01618286,0.04758549,-0.0071064,-0.01055706,-0.03385016,0.01104667,0.01619929,0.02034113,-0.01124522,-0.04845699,-0.00254588,0.05345297,0.043536,0.01129338,0.00345789,-0.0363603,-0.15173005,-0.04416309,-0.04926463,-0.00046408,0.02149338,-0.02266444,0.01301776,-0.01314671,-0.01888009,-0.0811478,0.03527432,-0.07316241,-0.01729198,0.03292716,0.03289773,0.01163438,-0.00246,0.02461844,0.01504309,0.00497078,-0.02852808,-0.06764678,-0.02197694,-0.00666392,0.04975337,0.08954773,-0.01136881,0.0171288,-0.03935878,-0.03785538,-0.04574231,0.13337339,-0.00961023,-0.08612075,-0.02023928,0.03934485,-0.03087934,-0.0826795,-0.03489748,-0.03794057,-0.03438384,-0.04846303,0.10606109,0.04381976,-0.08353385,-0.11750689,-0.01919138,0.00956556,-0.02006147,-0.06527959,-0.03413414,0.00942562,-0.02973082,-0.03000168,0.02941986,-0.06020625,0.03889699,0.05387126,-0.07153995,-0.03961724,-0.06993926,-0.01190972,0.00748933,-0.02541832,-0.03158148,-0.06669883,0.03017258,0.01693421,-0.03787509,-0.01418494,0.02306991,0.01020292,-0.05829413,0.05288196,0.04118899,-0.06663054,0.09205721,0.03254102,-0.03052609,0.04698976,0.00757426,0.045403,-0.00350921,-0.01009643,0.02612641,0.02281709,-0.00146871,0.03081949,0.01879549,0.01369014,-0.03974063,-0.16837503,-0.05552322,-0.07867325,-0.04831647,0.02206784,-0.05172008,0.0056775,-0.01792262,0.03890076,0.04419069,0.07079402,-0.01306084,-0.03309159,0.09013383,-0.00643725,0.01122892,-0.03277478,-0.01537977,-0.02498649,0.03382467,-0.04430469,0.03045942,-0.01246146,-0.07352038,0.05731428,-0.02348971,0.13910304,0.0129281,0.02938636,0.02568188,0.05545064,0.04118992,-0.04492433,-0.01748429,0.01725869,0.02855056,0.02403719,-0.05895157,-0.04957082,0.01281033,-0.08011778,0.01447294,0.00214591,-0.05837557,-0.04989891,0.02880127,-0.06500563,0.04211345,0.03465937,0.11846227,0.05376551,-0.00006649,0.06069215,0.06362174,0.08993474,0.00090421,-0.05428072,-0.03992696,-0.01680423,-0.00203366,-0.03764992,0.01272517,0.01359147,-0.05192067,0.02700808,0.01709313,-0.01668422,-0.05929633,0.00325325,0.00322498,-0.0121348,0.08574726,0.03845989,-0.01223893,-0.03911039,0.04261988,0.03087978,-0.0808916,-0.03284534,-0.01887927,0.01066672,-0.08158921,0.07016353,0.03198566,0.069296,0.01913762,0.05528725,-0.02668657,0.0164379,-0.01211642,-0.0274042,0.01473568,-0.03552445,0.04637741,0.01732026,-0.01309667,-0.25408202,-0.01410998,-0.01527463,0.07462997,-0.00581567,0.00699463,0.01508138,-0.02114916,0.01754782,-0.04458434,0.03522088,0.0262486,0.00223853,-0.011047,0.02199389,-0.01020665,0.00995169,-0.03992881,0.06744374,0.03501397,0.05795215,0.05791282,0.19712354,-0.00762392,-0.003478,0.00883015,-0.03184614,0.01932662,-0.04841729,0.06233592,0.02416624,-0.02703968,0.07478581,-0.00213848,0.014582,0.00168916,-0.00938679,0.03478792,0.01447422,0.02044939,-0.10322689,-0.04555622,-0.006302,-0.00578028,0.15924034,-0.00135371,0.00422919,-0.07799861,0.0052713,0.04139332,-0.07481124,-0.02930208,-0.05433077,-0.00594296,-0.00090044,0.05776892,-0.03422664,-0.02427013,0.05112385,0.00279353,0.06848723,-0.00707829,0.05704245,0.02251066,-0.0003879],"last_embed":{"hash":"14d1zu4","tokens":313}}},"text":null,"length":0,"last_read":{"hash":"14d1zu4","at":1753423511126},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win##I. [A Brief History of Lottery Strategies in _Reverse_](https://saliu.com/reverse-strategy.html#history)","lines":[16,24],"size":814,"outlinks":[{"title":"A Brief History of Lottery Strategies in _Reverse_","target":"https://saliu.com/reverse-strategy.html#history","line":1},{"title":"Reversed Lotto Strategy _Redivivus_","target":"https://saliu.com/reverse-strategy.html#strategy","line":2},{"title":"Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads","target":"https://saliu.com/reverse-strategy.html#reverse","line":3},{"title":"Reversed Lottery Strategy Example: Skips, Decades, Frequencies","target":"https://saliu.com/reverse-strategy.html#skips","line":4},{"title":"A Lottery Strategy is _Trinity_: _Straight_, _Purge_, _Reversed_","target":"https://saliu.com/reverse-strategy.html#trinity","line":5},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/reverse-strategy.html#links","line":6},{"title":"In lie or NOT elimination lottery strategy we rely on having non-winners in the first output.","target":"https://saliu.com/images/lotto.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win##I. [A Brief History of Lottery Strategies in _Reverse_](https://saliu.com/reverse-strategy.html#history)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08718164,-0.03015327,-0.03675148,0.04597629,-0.07741393,0.07936827,0.0236514,0.01737595,0.00944787,-0.00555218,-0.0093854,-0.03137487,0.06161309,-0.05418647,-0.00141606,-0.05909206,-0.03755121,0.05059794,-0.02469745,-0.0021289,0.07158907,-0.03421368,-0.04165376,-0.03535141,0.05867483,-0.00455821,0.00564379,-0.06084169,-0.04513717,-0.22347277,0.02391461,-0.008726,-0.05825888,-0.0466844,-0.05836436,-0.0039538,-0.07032715,0.06115698,-0.05819905,0.06752922,0.02864265,0.00581414,0.01482449,-0.00044709,0.00818027,0.0028903,-0.03329523,-0.00629839,0.06289603,0.00000797,-0.03449136,0.08113156,-0.0011682,0.03311268,0.08649293,0.06432658,0.11150832,0.12737298,0.01073158,0.01546002,0.0310872,0.05623352,-0.19453645,0.01955931,0.01045275,0.03055063,0.01440431,0.0180927,-0.01783336,0.0876267,-0.00733287,-0.02950365,-0.0181727,0.04171605,0.03946762,-0.00731376,-0.04026023,-0.05882829,-0.0512238,-0.01998146,0.00041446,-0.00611102,-0.02532591,-0.01907858,0.01417466,0.03591143,0.02925526,0.03174457,0.06500329,-0.08249499,0.0452507,0.01942757,0.05693315,0.01506963,0.02950474,0.01084729,0.03611592,0.01075584,0.05853567,0.11098181,0.00427391,0.03162872,-0.02446966,0.02555351,0.07490847,-0.10124107,-0.01335587,-0.0478025,0.03676489,0.04153494,0.0208174,0.02343684,0.09168594,-0.04144893,-0.0004504,0.03670597,0.01619759,0.04998191,-0.00617311,-0.01487114,-0.03082656,0.00967073,0.01927693,0.02080381,-0.01165265,-0.04873883,-0.00311383,0.05572782,0.03843118,0.01607872,0.00213655,-0.03782706,-0.14995971,-0.04644831,-0.05515107,-0.00151171,0.02235459,-0.02163921,0.00728056,-0.00852396,-0.01850048,-0.08239892,0.02982442,-0.07286069,-0.00709646,0.03496283,0.02770955,0.01691154,-0.01109456,0.02707519,0.01363532,0.00382192,-0.02692907,-0.06666677,-0.02502167,-0.00940051,0.04903097,0.08122994,-0.01245253,0.02095303,-0.04092672,-0.03699235,-0.04984988,0.13103062,-0.00807451,-0.08484321,-0.01288397,0.04000672,-0.03134692,-0.08391076,-0.03681466,-0.03925554,-0.03851469,-0.04897007,0.10683583,0.04498915,-0.08747192,-0.12204456,-0.01434472,0.01233168,-0.02271825,-0.06053596,-0.03425371,0.00751371,-0.02774121,-0.02827525,0.02847341,-0.05546838,0.03545778,0.0532057,-0.06930714,-0.03506226,-0.07196459,-0.01411734,0.00740218,-0.02415368,-0.02884188,-0.06798609,0.02845912,0.01389427,-0.0266333,-0.01558886,0.02714385,0.01136163,-0.05273936,0.048645,0.03903265,-0.06568149,0.09289727,0.03432544,-0.030617,0.04375919,0.00470071,0.044381,-0.00794592,-0.0045052,0.0257262,0.02749828,-0.00392464,0.03486452,0.02366502,0.01490854,-0.03648495,-0.17082186,-0.05381019,-0.07453024,-0.04242698,0.02502615,-0.04950044,0.00526028,-0.0125896,0.03509833,0.04709227,0.06880383,-0.00769631,-0.03143702,0.09579071,-0.00565957,0.01319395,-0.03011382,-0.01330214,-0.0224767,0.03404967,-0.04734778,0.03105431,-0.01669415,-0.07092952,0.0595314,-0.02443084,0.13747686,0.01375218,0.03353808,0.02878056,0.05268603,0.04660648,-0.0477474,-0.01948098,0.01939772,0.03446056,0.02600073,-0.06113724,-0.04819273,0.00993585,-0.07838946,0.01477671,0.00402306,-0.06130572,-0.04265081,0.02943311,-0.07024179,0.03865379,0.03829102,0.116197,0.05216939,-0.00033118,0.05812113,0.06328874,0.08961484,0.00228682,-0.05610649,-0.03979048,-0.01763968,-0.00616159,-0.03213346,0.01081714,0.01000406,-0.04783083,0.02595454,0.01974776,-0.01744951,-0.05979454,0.0101878,0.00343749,-0.00701849,0.08403531,0.03848077,-0.0095617,-0.04175526,0.04220121,0.0271691,-0.08768464,-0.03102813,-0.01779564,0.00843317,-0.08228341,0.07242963,0.03219447,0.06821918,0.0128568,0.05699506,-0.02666345,0.01428392,-0.01086233,-0.02732028,0.01570027,-0.03679032,0.05228194,0.02310226,-0.00950248,-0.25174209,-0.02128636,-0.01574327,0.07004884,-0.01010487,0.00857854,0.01120333,-0.02454598,0.01487994,-0.03992765,0.03286256,0.02926416,0.00287508,-0.01146082,0.02341038,-0.00622421,0.00823116,-0.03612826,0.06834586,0.03271708,0.05854963,0.0588101,0.19907564,-0.00508807,-0.00293686,0.00645626,-0.03704355,0.01536406,-0.05037477,0.06333417,0.02149771,-0.02680513,0.07199463,-0.00825987,0.01562501,0.00331791,-0.0061032,0.03348632,0.01817625,0.0205526,-0.1038934,-0.04529218,-0.00962685,-0.0088648,0.15616637,-0.00348336,0.00420233,-0.07273743,0.00418661,0.04080487,-0.07522217,-0.02208853,-0.05673913,-0.00572298,0.00288132,0.05506491,-0.03790009,-0.02432049,0.04931789,0.00420151,0.06896096,-0.00304634,0.06113613,0.0238534,-0.00173694],"last_embed":{"hash":"1y82m7u","tokens":281}}},"text":null,"length":0,"last_read":{"hash":"1y82m7u","at":1753423511261},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win##I. [A Brief History of Lottery Strategies in _Reverse_](https://saliu.com/reverse-strategy.html#history)#{1}","lines":[17,24],"size":703,"outlinks":[{"title":"Reversed Lotto Strategy _Redivivus_","target":"https://saliu.com/reverse-strategy.html#strategy","line":1},{"title":"Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads","target":"https://saliu.com/reverse-strategy.html#reverse","line":2},{"title":"Reversed Lottery Strategy Example: Skips, Decades, Frequencies","target":"https://saliu.com/reverse-strategy.html#skips","line":3},{"title":"A Lottery Strategy is _Trinity_: _Straight_, _Purge_, _Reversed_","target":"https://saliu.com/reverse-strategy.html#trinity","line":4},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/reverse-strategy.html#links","line":5},{"title":"In lie or NOT elimination lottery strategy we rely on having non-winners in the first output.","target":"https://saliu.com/images/lotto.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>I. A Brief History of Lottery Strategies in <i>Reverse</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07761442,-0.01912726,-0.01657727,0.02210439,-0.0528397,0.07685793,0.02718015,-0.00816237,0.0035496,0.01039899,-0.00319158,0.00077049,0.06090795,-0.03925543,-0.01291281,-0.03403185,-0.03420643,0.00561453,-0.04162188,0.00258637,0.085641,-0.0486905,-0.04724829,-0.01970433,0.06002937,-0.02739137,0.00405753,-0.04950271,-0.07803512,-0.24925332,0.03247125,-0.00192821,-0.02904053,-0.04931797,-0.05481593,-0.0089856,-0.04536135,0.09792288,-0.06019371,0.06028589,0.02541185,-0.00495297,0.02960985,-0.03142926,-0.00489148,0.01371025,-0.0257118,-0.00219937,0.05553889,0.00160319,-0.00851415,0.06088542,-0.01211538,0.01953091,0.04459547,0.02989776,0.08621494,0.11785286,0.04196857,0.0174031,0.03141087,0.05174596,-0.19046552,0.00749121,-0.02649324,0.03711197,-0.02434729,-0.00901821,-0.01331451,0.09808702,-0.0015865,0.00250792,-0.03782428,0.02054138,0.02878141,-0.01866754,-0.02081579,-0.02933832,-0.08169223,-0.00129058,-0.02164611,-0.00956054,-0.01422134,0.01347216,0.00413844,0.00546052,0.02031413,0.04242208,0.05620708,-0.06769624,0.05772979,0.01566167,0.0571212,0.04624561,0.00470426,-0.00239771,0.01973817,0.05377946,0.0553554,0.11027252,0.01858784,0.02154123,-0.00035479,-0.00358862,0.04818635,-0.07677832,-0.03077185,-0.03912895,0.02771299,0.05800923,0.00744575,0.03597875,0.0913936,-0.04995746,-0.01025552,0.05375772,0.02360677,0.05035638,-0.04268281,-0.0014529,-0.03763604,0.02409964,0.00520277,-0.00332066,-0.00568061,-0.0286958,0.01008236,0.07057999,0.0560382,0.02667506,-0.00901854,-0.01788885,-0.1571914,-0.05195961,-0.01612771,0.00200076,0.01052985,-0.01257813,0.02544608,-0.0328494,-0.04622097,-0.02129655,0.03728945,-0.03707719,-0.02308753,0.07393087,0.02863029,-0.01304469,0.01695562,-0.00932909,0.02985263,0.01690432,-0.04608681,-0.06487305,-0.0065432,-0.0095602,0.03517045,0.07162198,-0.04554196,0.01478144,-0.01767186,-0.02604441,-0.03929209,0.10862969,-0.0065243,-0.06632113,-0.04719903,0.01659302,-0.02434681,-0.0629504,-0.03752806,-0.02399103,-0.01490087,-0.06029378,0.06420343,0.02382416,-0.103026,-0.08368365,-0.03444097,-0.01451102,-0.00239547,-0.04475007,-0.03747892,0.00122188,-0.03537467,-0.05655335,0.00503581,-0.03746921,0.04313934,0.09592037,-0.05981458,-0.03812191,-0.07508883,-0.00069475,0.02768486,-0.04046777,-0.0074362,-0.02635091,0.01459498,-0.01480607,-0.04699656,-0.05025376,0.01259462,0.02579406,-0.05762324,0.04134982,0.00588757,-0.08717467,0.12113547,0.02845846,-0.0571946,0.01302157,0.02993598,0.05434046,-0.029627,-0.01204829,0.00451219,0.01010493,-0.01189722,-0.01013046,0.01856773,0.00615158,-0.0259051,-0.18690236,-0.0430709,-0.06920552,0.0118866,0.0399416,-0.04021184,0.00368688,-0.00837443,0.02843144,0.07791322,0.07307207,-0.0160831,-0.03406321,0.0748745,-0.01459045,0.00496348,-0.03425572,-0.00369695,-0.04611569,0.03675849,-0.01696652,0.00737282,-0.02428903,-0.07186122,0.08201303,-0.02019487,0.15516521,0.02687774,0.02971737,0.02316297,0.06412356,0.02143565,-0.02578308,-0.00725896,0.02282184,0.01941092,0.01539883,-0.04955459,-0.05333517,0.00718003,-0.06349143,-0.013942,-0.03349947,-0.08383427,-0.05948338,0.03010844,-0.06408406,0.07729193,0.03173718,0.09365579,0.05319647,-0.04934959,0.03761204,0.04015787,0.08233578,-0.0132936,-0.05782176,-0.03260009,0.00478466,0.01955939,-0.00973081,0.02204673,0.01289176,-0.04231738,0.01268621,0.0274164,0.00551207,-0.04416803,0.01288539,-0.00868058,-0.01629179,0.07950451,0.02055233,0.00887291,-0.03554223,0.05204686,0.00844215,-0.05648013,-0.00806068,-0.07075735,0.06166779,-0.08409819,0.05992629,0.03792195,0.05297181,0.05163606,0.03477797,0.00404971,0.00849179,-0.02337158,-0.0457361,0.0331689,-0.0350709,0.04291784,0.04679452,-0.02240135,-0.23751622,0.01518607,-0.0464175,0.09488584,0.01666443,-0.00516421,0.02403039,0.01386351,0.07909585,-0.07492702,0.03433377,0.05539009,0.00468637,-0.0146447,0.01978196,-0.03883347,-0.01162209,-0.02224486,0.08879189,0.03650619,0.07385124,0.05073971,0.22601646,0.00301882,-0.0329051,-0.01039053,-0.02020249,0.02364668,-0.05063235,0.03054783,0.00419484,-0.03727208,0.087272,0.01820708,0.01271308,-0.02332229,-0.0279252,0.04742464,0.00567176,0.01870345,-0.0762945,-0.03668026,-0.00215542,-0.01632708,0.1560443,0.02434627,-0.00247764,-0.07883473,0.00977977,0.05865492,-0.07694092,-0.02641916,-0.04713917,-0.04484138,0.01083516,0.0816355,-0.01681891,-0.03148121,0.02318476,0.01384831,0.07054941,-0.01263693,0.06308635,0.0180674,-0.00133266],"last_embed":{"hash":"1k68i29","tokens":394}}},"text":null,"length":0,"last_read":{"hash":"1k68i29","at":1753423511368},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>I. A Brief History of Lottery Strategies in <i>Reverse</i></u>","lines":[25,36],"size":1374,"outlinks":[{"title":"New lotto strategy in reverse, lottery 2010, 2011.","target":"https://saliu.com/HLINE.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>I. A Brief History of Lottery Strategies in <i>Reverse</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07759461,-0.0215588,-0.01630274,0.02069398,-0.04969307,0.07503057,0.02636314,-0.00827705,0.00571891,0.00884261,-0.00267895,0.0001553,0.06095623,-0.03799415,-0.01189792,-0.03259578,-0.03608102,0.00351824,-0.04265368,0.00262811,0.08693913,-0.04852102,-0.04683637,-0.01925099,0.05919078,-0.02600858,0.00435802,-0.05177805,-0.078457,-0.25191653,0.0331067,-0.00351884,-0.02877871,-0.04932202,-0.05429826,-0.00799759,-0.04369622,0.0967145,-0.05794685,0.0595667,0.0255872,-0.00525431,0.03023026,-0.03309438,-0.00721672,0.01300004,-0.02621664,-0.0037215,0.05472659,0.00143508,-0.00871525,0.05923334,-0.01443893,0.01882188,0.04307069,0.02823798,0.08810246,0.11742494,0.04043839,0.01797102,0.02976945,0.051607,-0.19092931,0.00974495,-0.02876556,0.03708807,-0.02467049,-0.01079204,-0.01185858,0.09746487,-0.00183311,0.00398355,-0.03831402,0.0221948,0.02874641,-0.0195808,-0.0183099,-0.03050077,-0.08078329,0.00028346,-0.0228227,-0.00969543,-0.01321707,0.01309185,0.00592844,0.00334708,0.02028684,0.04141342,0.05357074,-0.06733646,0.05883456,0.0157249,0.05597259,0.04641533,0.0063066,-0.00285167,0.01998996,0.05369701,0.05452221,0.10918778,0.01913795,0.02183159,0.00092709,-0.00428743,0.05041456,-0.07356963,-0.03221518,-0.03972594,0.02639917,0.05746972,0.00905455,0.03630915,0.0896899,-0.05066057,-0.00779521,0.05417534,0.02562387,0.04913433,-0.04285042,-0.00166408,-0.03822735,0.02319438,0.00662625,-0.00241413,-0.00641014,-0.02780024,0.01200723,0.0695627,0.05607106,0.02782312,-0.00791073,-0.01878159,-0.15829404,-0.05452719,-0.01656934,0.00158014,0.01084224,-0.01458726,0.02468126,-0.03223707,-0.04666734,-0.01903401,0.03615871,-0.0373006,-0.02410861,0.07688256,0.02754273,-0.01332289,0.0159048,-0.01141996,0.02984563,0.01836542,-0.04638308,-0.06568538,-0.00574212,-0.00764533,0.03674405,0.0720059,-0.04522499,0.01609719,-0.01869074,-0.02773843,-0.03707464,0.10814034,-0.00674596,-0.06611484,-0.04676105,0.01699959,-0.02232972,-0.06301354,-0.03659524,-0.02314319,-0.01319972,-0.05840421,0.06475851,0.02390218,-0.104319,-0.0811841,-0.03480316,-0.01424663,-0.00275235,-0.04416254,-0.03748611,-0.00009623,-0.03580788,-0.05732316,0.00785656,-0.03566175,0.0420074,0.09705187,-0.06251926,-0.03702454,-0.07636496,-0.00043261,0.02615374,-0.0385815,-0.00762461,-0.02626496,0.01444343,-0.0159002,-0.04537325,-0.05076205,0.01299772,0.02576387,-0.05602334,0.04172437,0.00325942,-0.08632109,0.11991567,0.02756108,-0.05742291,0.01500116,0.03049864,0.05302566,-0.02955842,-0.01360882,0.00328745,0.00921164,-0.0129866,-0.01172927,0.01937415,0.00534558,-0.02624057,-0.1882516,-0.04334677,-0.06937303,0.01344488,0.0395358,-0.04072031,0.00342304,-0.00795441,0.02929375,0.08041554,0.0722064,-0.01499161,-0.03284976,0.07433582,-0.0142205,0.00510635,-0.03255241,-0.00158557,-0.04589714,0.03713822,-0.01665912,0.00602123,-0.0235434,-0.07030887,0.08121568,-0.02043826,0.15624836,0.02488836,0.02831257,0.02261604,0.06307609,0.02193695,-0.02528058,-0.0083862,0.02371899,0.01866776,0.01352088,-0.05156237,-0.0515384,0.00390916,-0.06346779,-0.01520083,-0.03248457,-0.08440509,-0.05929121,0.03112867,-0.06387518,0.07686213,0.03023084,0.0909859,0.05586807,-0.05035277,0.03751161,0.04031647,0.08091034,-0.01658099,-0.05855094,-0.03149194,0.00518578,0.02120991,-0.00920523,0.01985752,0.01450345,-0.04320315,0.0154621,0.02644793,0.0049109,-0.04102146,0.0144478,-0.00727156,-0.01759576,0.08083244,0.01970757,0.01030225,-0.0347098,0.04953495,0.00694216,-0.05683647,-0.00814062,-0.0714554,0.06283644,-0.0835743,0.05997721,0.03795789,0.05491387,0.05323915,0.0373441,0.00364833,0.00838598,-0.02373642,-0.04363966,0.03320965,-0.03310548,0.0443265,0.04845003,-0.02276443,-0.23902941,0.01584483,-0.04665853,0.09611357,0.01908378,-0.00675339,0.0240205,0.01417674,0.08064147,-0.07505083,0.03396421,0.05476284,0.00528962,-0.01751094,0.01737206,-0.03770095,-0.01294959,-0.02064522,0.08903518,0.03621211,0.07304236,0.04967734,0.22612149,0.00352587,-0.0334751,-0.01004168,-0.01914489,0.02317202,-0.0530252,0.03149484,0.0039167,-0.03683501,0.08842319,0.01862276,0.01009119,-0.02095738,-0.02612013,0.04724501,0.00369628,0.01906742,-0.07372979,-0.03645375,-0.00170579,-0.01775701,0.15571594,0.0246022,-0.00312311,-0.07747601,0.00976507,0.05896465,-0.07591495,-0.02774873,-0.04656161,-0.04481221,0.01327856,0.08364386,-0.01603172,-0.0302575,0.02188281,0.01276433,0.0702545,-0.01170452,0.06211504,0.0181799,-0.00109388],"last_embed":{"hash":"1ttyxmz","tokens":393}}},"text":null,"length":0,"last_read":{"hash":"1ttyxmz","at":1753423511507},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>I. A Brief History of Lottery Strategies in <i>Reverse</i></u>#{1}","lines":[27,36],"size":1304,"outlinks":[{"title":"New lotto strategy in reverse, lottery 2010, 2011.","target":"https://saliu.com/HLINE.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07372355,-0.03535458,-0.01892743,0.02046576,-0.044417,0.06637058,0.01012826,-0.00816664,-0.00169236,0.0151119,-0.02148148,0.02070845,0.06592195,-0.02628423,-0.01581267,-0.05744596,-0.02627665,0.0230195,-0.06877107,-0.01431771,0.03841716,-0.04237863,-0.03003622,-0.04250406,0.05264291,0.00068137,-0.01750886,-0.04824509,-0.08226014,-0.25215018,0.02563647,0.01253109,-0.02123402,-0.05785214,-0.04119289,-0.00529159,-0.06358244,0.05403082,-0.06357778,0.05354864,0.04035021,-0.00346553,0.03403819,-0.00646384,0.01768364,0.02362508,-0.04054268,0.00916768,0.04694284,0.00298657,-0.00449444,0.03522279,0.00199535,0.044034,0.0536632,0.02365491,0.08936556,0.10081458,0.03510197,0.03666404,0.04195993,0.08541755,-0.17163846,0.00459237,0.00020631,0.02153671,-0.01541571,-0.01864105,-0.01938273,0.0775881,-0.0043049,-0.01694505,-0.03469943,0.02290887,0.04706102,-0.03329136,-0.02125378,-0.0183272,-0.04885473,0.0070337,-0.00470984,-0.002672,-0.0124843,0.01709685,0.01357767,0.02760861,0.02779461,0.07065706,0.05510487,-0.10442395,0.05877847,0.01184447,0.06156687,0.03858384,0.02246433,0.00901304,0.00956361,0.05635745,0.0176281,0.10899591,-0.01760478,0.00533181,-0.01849154,-0.00091648,0.05487965,-0.10305313,-0.01336265,-0.00953266,-0.00290199,0.04675361,0.02423406,-0.00593252,0.08311902,-0.04432535,-0.0103461,0.04063813,-0.00779347,0.04364677,-0.05228155,0.00393088,-0.01736647,0.03370975,0.00540786,0.01220242,-0.02474901,-0.04610079,-0.00953393,0.07942996,0.04245799,-0.00528901,-0.00440749,-0.01560688,-0.12905709,-0.06171881,-0.02668458,-0.00293814,0.02188539,-0.00830823,0.03087812,-0.01928442,-0.00863125,-0.0533774,0.03736662,-0.04863637,0.00052992,0.05901721,0.01458001,-0.00824984,0.0212168,0.0149478,0.00374296,0.0164747,-0.06593174,-0.07597441,0.01141309,-0.03059966,0.03485577,0.0597356,-0.05825034,0.00814453,-0.00358332,-0.00300327,-0.02991036,0.10867238,-0.03206039,-0.08679744,-0.0226989,0.03719329,-0.02729968,-0.07877915,-0.05993921,-0.03065464,-0.00645734,-0.05932389,0.1091513,-0.00905149,-0.07480595,-0.12159744,-0.01205924,0.00119188,0.01845269,-0.03199852,-0.03751854,0.00156487,-0.03884942,-0.06344491,-0.00750751,-0.03516416,0.04765909,0.06583526,-0.06913356,-0.02381672,-0.08016196,0.0081433,0.0012568,-0.04342437,-0.00760773,-0.04119425,0.01784416,-0.01861846,-0.01815757,-0.01653429,0.02041925,0.01736694,-0.0318596,0.06210526,0.01676281,-0.08115131,0.13994485,0.04144743,-0.04737612,0.0093659,0.0206961,0.05025407,-0.05225801,0.01964122,0.0150243,0.01084239,-0.00102046,0.00374483,0.01033645,0.00974212,-0.02087958,-0.18676093,-0.04680612,-0.05531772,-0.00759852,0.03710037,-0.05807199,-0.01177774,0.01442185,0.02939947,0.08249185,0.07774977,-0.03317543,-0.04342782,0.07392091,-0.01416777,-0.02539044,-0.04173344,-0.03053671,-0.02292947,0.04839621,-0.02598758,0.01732738,0.00755125,-0.08256824,0.05742301,-0.00598732,0.13422829,0.04999815,0.05161228,0.03456126,0.0561614,0.05165331,-0.01778562,-0.0496634,0.04485251,0.01649168,0.02599068,-0.0425653,-0.0451601,0.0163572,-0.06372302,-0.02326908,-0.0006176,-0.10240487,-0.00959995,0.0105798,-0.04024761,0.03950487,0.01425633,0.13772865,0.04449839,-0.01120947,0.03047135,0.0822593,0.08977898,-0.02631264,-0.05227659,-0.01701849,0.02630214,0.00207625,0.01294518,0.01096847,0.02572143,-0.04597159,0.01220802,0.0217177,-0.02962197,-0.01384488,0.02228769,0.00591847,-0.00388293,0.0860485,0.02138087,-0.00732635,-0.04046755,0.06602748,0.0516945,-0.05530169,-0.01799486,-0.047818,0.0318899,-0.0689142,0.07440207,0.05150101,0.0277042,0.00964655,0.03414284,-0.00151406,-0.00825046,0.00120208,-0.03607848,-0.0058192,-0.02560237,0.02456095,0.01483961,0.00472607,-0.24590221,-0.01501649,-0.04066181,0.04584408,-0.01744822,0.02153764,0.03734574,-0.0002739,0.02359164,-0.05127783,0.02482138,0.06001952,0.03234948,-0.04127196,0.03291088,-0.04804677,-0.00050861,-0.03739892,0.08478874,0.0401454,0.08813926,0.05426835,0.24341361,-0.00216243,-0.0102162,-0.00270494,-0.03998947,0.0348609,-0.02384876,0.03163212,-0.01524988,-0.03495528,0.05859591,-0.00649273,0.02374635,-0.03232412,-0.01149926,0.03282304,0.0167067,0.02335013,-0.1024691,-0.05840062,-0.02843719,0.00307456,0.15740439,0.00602918,-0.00007632,-0.07203157,0.03055812,0.05376341,-0.07106201,-0.02407111,-0.04819657,-0.04430659,-0.01105554,0.05346522,-0.02264609,-0.04160024,0.05802856,0.00943392,0.07850832,0.0066256,0.08191839,0.02720087,-0.02350467],"last_embed":{"hash":"1glotb3","tokens":441}}},"text":null,"length":0,"last_read":{"hash":"1glotb3","at":1753423511656},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>","lines":[37,56],"size":2484,"outlinks":[{"title":"_**Removing Lotto Combinations**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":5},{"title":"Code name of this reversed lottery strategy is LIE lotto strategy.","target":"https://saliu.com/ScreenImgs/reverse-strategy.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07962643,-0.04093496,-0.02492224,0.01630755,-0.05667778,0.03696389,0.03419155,0.03788242,0.00927147,0.01447882,-0.03516667,-0.00100135,0.05590721,-0.0431053,-0.03009727,-0.036428,-0.04160662,0.0517703,-0.05359132,-0.00981276,0.03827577,-0.00497252,-0.04863926,-0.03809206,0.04325527,-0.00113591,-0.00405505,-0.05568224,-0.06751645,-0.2236474,0.04972689,0.00572366,-0.02685343,-0.04422376,-0.03249617,-0.01268234,-0.06261517,0.03774633,-0.03770037,0.04996014,0.03358042,0.01314394,-0.0030394,-0.009399,0.02808859,-0.00616189,-0.03148064,0.00474932,0.07161327,-0.00060851,0.04235976,0.08170908,0.02010548,0.05246174,0.08506629,0.0467622,0.10431897,0.09516761,0.04216385,0.02882097,0.01458583,0.06429856,-0.18821298,0.0037935,-0.00095876,0.04526418,0.00847603,-0.01812155,-0.02557573,0.0927412,0.01000939,-0.0233202,-0.01427783,0.0195717,0.04193291,-0.02838552,-0.03846805,-0.01548563,-0.03208845,0.00706218,0.01477968,-0.00298718,-0.01063436,0.01290327,0.01026653,0.01679614,0.02500589,0.06706092,0.08086465,-0.07763162,0.04719025,-0.00515092,0.07793498,0.0207121,0.01877508,0.00622721,-0.00359837,0.0335608,0.02061131,0.12531061,-0.01098842,0.00766175,-0.03467518,0.0030785,0.04693648,-0.08878786,-0.01793771,-0.03672945,0.00422086,0.04913034,0.03362762,0.00583401,0.08923029,-0.0623027,-0.01675051,0.04122369,-0.03647137,0.0588825,-0.02909405,0.00885296,-0.02585395,0.02052133,0.02025351,0.0121894,-0.04466347,-0.04394068,-0.00290701,0.08427133,0.01582125,-0.01355097,0.00987628,-0.0437353,-0.13255554,-0.0580058,-0.01500211,0.00793561,0.03101032,-0.0580662,0.04901949,-0.01226127,-0.00923522,-0.05826187,0.0103751,-0.06618352,-0.00648727,0.02062341,0.03739109,-0.00520593,0.01736418,0.02174042,0.03455992,-0.01082273,-0.03379004,-0.05434114,-0.00197371,-0.00014537,0.0626552,0.07794472,-0.02050421,-0.00350417,-0.02931819,-0.02492943,-0.04146007,0.10900009,-0.03112157,-0.07775805,-0.04824064,0.01864562,-0.0594836,-0.04043436,-0.05186982,-0.0327769,-0.02746143,-0.04393846,0.10873686,0.0265748,-0.06317133,-0.10097326,-0.0217448,-0.02753823,0.00780157,-0.05278978,-0.03486033,0.0054394,-0.04316505,-0.0544156,-0.01323893,-0.03288522,0.03867217,0.05530301,-0.06350303,-0.02952862,-0.0611517,0.00769541,0.01475378,-0.02716102,-0.02797593,-0.05971824,0.03529467,-0.02902224,-0.01743682,-0.00405076,-0.00782808,0.03310218,-0.04950591,0.05565885,0.01004004,-0.07942223,0.09418906,0.0523287,-0.02688823,0.0243513,0.0317966,0.0414995,-0.0022069,-0.00324069,0.01801515,-0.0049381,-0.00213122,0.01178942,0.01883925,0.04555535,-0.01114358,-0.17891513,-0.0603487,-0.08109228,-0.01731299,0.01306073,-0.03834357,-0.02265657,-0.00404746,0.04652053,0.06832718,0.06594573,-0.0287276,-0.03842794,0.0888249,-0.02718711,0.00790647,-0.02751926,-0.0235203,-0.01538744,0.02619022,-0.0317433,0.03031975,-0.01463499,-0.0729607,0.06181913,0.01477882,0.15771186,0.06058127,0.03710147,0.04053429,0.06952909,0.05518807,0.01842529,-0.04383883,0.04299619,0.00702833,0.03533749,-0.06048033,-0.03703651,0.01539986,-0.08570135,-0.01109961,0.00898081,-0.10065953,0.0036254,-0.00253026,-0.03819809,0.04740619,0.02718185,0.14778854,0.04264059,-0.02045207,0.02751469,0.08573614,0.08967207,-0.0027893,-0.06122974,0.00310938,0.03132132,-0.00114853,-0.01549474,0.00158168,0.03494477,-0.04949977,-0.00844734,0.00395633,0.00318266,-0.03969719,0.02041102,0.00198044,-0.00887222,0.05290218,-0.00048098,-0.027125,-0.03183918,0.06726597,0.02217637,-0.04065225,-0.01908998,-0.0587205,0.03701671,-0.09611847,0.0725315,0.04675916,0.05551,-0.00708721,0.02854916,-0.03146991,0.00591551,-0.0168844,-0.03712588,-0.00281247,-0.04379138,0.03333676,0.00691618,0.00038615,-0.25810164,-0.02089781,-0.02755176,0.06411352,-0.00906058,0.0430802,0.02019039,-0.01131016,0.00761199,-0.05345516,0.04052899,0.035246,0.04549213,-0.00921518,0.04597796,-0.05475978,0.00240095,-0.03092106,0.07019038,0.02738744,0.07145248,0.04981923,0.23529193,-0.01299431,-0.01521041,-0.00134094,-0.04345629,0.01280682,-0.01693056,0.02182146,-0.00233018,-0.02218435,0.04450695,-0.02888522,0.02039611,-0.04036702,0.00422772,0.06026243,0.01937974,0.02309418,-0.13183415,-0.04331601,-0.03552972,-0.00621209,0.12651396,0.00090092,-0.02770616,-0.06839806,0.03013045,0.04476311,-0.0606433,-0.01113382,-0.07123622,-0.02534376,-0.05232965,0.03820251,-0.0239404,-0.05070956,0.06695833,-0.01925345,0.09313299,0.02223613,0.07328057,0.02693696,-0.00678117],"last_embed":{"hash":"1dtju2x","tokens":106}}},"text":null,"length":0,"last_read":{"hash":"1dtju2x","at":1753423511824},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>#{1}","lines":[39,40],"size":261,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07513317,-0.0403527,-0.00999705,0.0195157,-0.06105015,0.07474969,0.01616757,-0.01464496,-0.01214549,0.0104585,-0.00336513,0.01637352,0.07236396,-0.03626138,-0.01219487,-0.06422969,-0.01936682,0.01393585,-0.06681702,-0.02444823,0.05451379,-0.05388112,-0.03075893,-0.04091309,0.05367856,0.0113809,-0.03104711,-0.04516651,-0.08856382,-0.25258017,0.02729939,0.01510654,-0.01941889,-0.05405775,-0.05396505,-0.01710157,-0.07009637,0.07098811,-0.07411255,0.06054045,0.03792143,-0.0146549,0.03685164,-0.00811545,0.02553147,0.01295861,-0.03559908,0.00906148,0.04245801,0.01262571,0.00157039,0.02757556,0.00320417,0.05106028,0.05104426,0.02405562,0.08763511,0.10947601,0.05584935,0.03280879,0.05271353,0.07572501,-0.17856701,0.00185594,-0.01071131,0.01173355,-0.02223086,-0.01703492,-0.01756104,0.08591235,-0.00026344,-0.01606166,-0.02771527,0.02902866,0.04304353,-0.02921307,-0.01361247,-0.03947005,-0.05167838,0.00922267,-0.0097416,0.00156581,-0.0229963,0.00021933,0.01110122,0.0160277,0.03800602,0.05197579,0.0596553,-0.0695928,0.07255162,0.01707674,0.05417485,0.04806227,0.01830796,0.00585582,0.02115913,0.04738181,0.02688043,0.10984819,0.00132962,0.01230555,-0.01352391,0.00851161,0.05898631,-0.1133039,-0.0105216,-0.00391118,0.00056735,0.03752906,0.01621333,-0.00106092,0.086799,-0.03301425,-0.01265156,0.03670614,0.0077019,0.02749343,-0.04222617,0.00567897,-0.02270882,0.02441275,0.00334695,0.0057844,-0.01354351,-0.03080321,0.00489428,0.06518897,0.04212909,0.00490191,0.00507002,-0.00691997,-0.12172589,-0.04867555,-0.0097887,-0.00025072,0.02565096,-0.00138707,0.01992628,-0.02762339,-0.01732775,-0.03741147,0.04890295,-0.046012,0.00150174,0.08597248,0.00749953,-0.00243337,0.0078566,-0.00252079,-0.01261722,0.01918513,-0.06479874,-0.08339141,0.00676986,-0.0265243,0.02906106,0.05151125,-0.05501831,0.00855165,-0.01227038,0.00535436,-0.02549539,0.09416588,-0.03454377,-0.08225269,-0.02031115,0.0318513,-0.01667661,-0.10183447,-0.06362424,-0.02255587,-0.01544102,-0.04937564,0.09904111,-0.0070663,-0.08737795,-0.12822728,0.00216588,-0.00654851,0.03720208,-0.03677464,-0.0347141,-0.00629933,-0.04328513,-0.05362532,-0.01771195,-0.04061388,0.03736598,0.06239493,-0.0703447,-0.01175241,-0.07820303,-0.00379286,0.01407118,-0.05710858,-0.01549058,-0.02096288,0.01364673,-0.02675148,-0.0045124,-0.01711733,0.03294153,0.01731155,-0.02885791,0.04951229,0.02169713,-0.08310775,0.14926153,0.03449379,-0.06505198,0.01911258,0.01975362,0.07085295,-0.05793593,0.01274874,0.00937779,0.01583252,-0.01806594,0.00704392,0.01182978,0.01310188,-0.01884542,-0.19289233,-0.04906232,-0.04393351,-0.00462708,0.03458701,-0.0495082,-0.00559597,0.00582137,0.03852039,0.08483392,0.0877875,-0.01636197,-0.04799412,0.07899054,-0.00952959,-0.00944833,-0.05124962,-0.03366958,-0.02422156,0.03741329,-0.02514089,0.01348167,0.00482583,-0.09590391,0.06126213,-0.00404896,0.13296153,0.02734386,0.04786001,0.02700946,0.04675546,0.03384589,-0.01827979,-0.02706265,0.04272924,0.03200035,0.02326019,-0.02999377,-0.05604659,0.01096459,-0.05138224,-0.01920278,-0.00946027,-0.09496528,-0.0331622,0.02791098,-0.04611604,0.04493519,0.02125995,0.11084127,0.04462836,-0.01846094,0.04032579,0.07594995,0.09663398,-0.03596185,-0.03592993,-0.02125072,0.01658308,-0.00332036,0.01971652,0.00769555,0.00592927,-0.03457333,0.01574972,0.00813157,-0.02854974,-0.01554047,0.02632056,-0.0100161,-0.00699585,0.08497165,0.01591332,0.00097896,-0.03712323,0.05933932,0.05822804,-0.05826563,-0.02131656,-0.04409268,0.03693164,-0.05770432,0.07391469,0.04313086,0.02651208,0.00535351,0.02923126,0.01985011,0.00639968,0.00416078,-0.04188832,-0.00104012,-0.03235845,0.02045025,0.00592157,-0.01398524,-0.24289553,-0.00690013,-0.05012063,0.05876795,-0.03423228,0.00698797,0.0287598,0.00803746,0.02187383,-0.05457203,0.02216535,0.06477354,0.02300336,-0.04274293,0.02656434,-0.03574779,0.01961231,-0.03668767,0.07966654,0.04431582,0.0793021,0.07131248,0.2322104,-0.01276238,-0.0103836,0.00552284,-0.02942954,0.03627858,-0.03351739,0.04069697,-0.01076532,-0.0532205,0.08648362,-0.00575415,0.02712354,-0.03475633,-0.02641683,0.02186468,0.02791146,0.02412865,-0.07904661,-0.04779804,-0.02267201,0.00332826,0.15936439,0.00749991,0.00527872,-0.07557079,0.00317365,0.05808892,-0.0790362,-0.02373808,-0.05242402,-0.04450446,0.00891801,0.06638083,-0.02930999,-0.01570315,0.03565276,0.01167271,0.06692468,-0.01553169,0.0906108,0.01486389,-0.02957544],"last_embed":{"hash":"7tjryw","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"7tjryw","at":1753423511861},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>II. Reversed Lotto Strategy Redivivus</u>#{3}","lines":[43,56],"size":2078,"outlinks":[{"title":"Code name of this reversed lottery strategy is LIE lotto strategy.","target":"https://saliu.com/ScreenImgs/reverse-strategy.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>III. Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads, etc.</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07964897,-0.04345824,-0.01829701,0.0011378,-0.07321928,0.08074578,0.01966573,0.00491027,0.01444426,0.00587931,-0.01368063,-0.01997028,0.10676368,-0.02530691,-0.01758865,-0.04502648,-0.04144772,0.01912289,-0.0833438,0.01261527,0.06364866,-0.03258371,-0.06411049,-0.05523255,0.03744094,-0.00731055,0.00665872,-0.05954387,-0.05676558,-0.25371084,0.03710795,0.04312591,-0.00911999,-0.03153458,-0.07071127,-0.03284407,-0.0689719,0.0880723,-0.05074802,0.02952914,0.02422578,-0.00801264,0.02685148,-0.01259601,-0.00146782,0.01190559,-0.03061663,0.00066552,0.0688778,0.01224982,0.0162968,0.048868,-0.00071976,0.05509141,0.05726688,0.05910005,0.07637881,0.09969772,0.03514165,0.04362693,0.02512565,0.08191608,-0.18579242,-0.00260071,0.02976017,0.01312667,-0.01029325,0.02110199,-0.01082544,0.09257919,-0.00023025,-0.00341036,-0.0175219,0.00982746,0.00934868,0.006349,-0.05469229,-0.05614177,-0.04344025,0.02173816,-0.04475761,-0.02800882,-0.01543788,0.01538281,0.01261505,0.01370169,0.06232701,0.04514194,0.06455703,-0.04088744,0.04033997,0.02100408,0.03472067,0.01787991,0.02517502,0.02624188,0.00327863,0.00697834,0.02209745,0.11341618,0.00052812,0.01795916,-0.01113015,0.00671168,0.04156681,-0.11022651,-0.03318474,-0.03323386,0.01477805,0.02306193,0.00867101,0.00516596,0.06568295,-0.05358874,-0.01377462,0.05947173,0.02673365,0.04373032,-0.02188086,-0.00908201,-0.02727859,0.02550816,0.01262882,0.00048112,0.00409755,-0.0029646,0.01737672,0.08854139,0.02729074,0.03098205,0.01521909,-0.02277134,-0.13766348,-0.03898991,-0.03370579,-0.01892447,0.02063943,-0.01529717,0.04028063,-0.02020742,0.00037834,-0.04627939,0.05934035,-0.06412786,0.03129382,0.07129779,0.01255477,-0.00924795,0.01320642,-0.00156071,0.01262603,-0.00349617,-0.04084641,-0.0642136,-0.00192748,-0.00174264,0.04012967,0.06612115,-0.0370207,0.02815659,-0.03499366,-0.03310785,-0.05271055,0.09233426,0.00596992,-0.09081314,-0.01674348,0.02208111,-0.06414956,-0.07025389,-0.04838404,-0.00993282,-0.01861141,-0.03108239,0.07892559,0.03017733,-0.10813827,-0.09740181,-0.00108975,-0.01646234,-0.00348056,-0.01074513,-0.06211927,-0.00733,-0.05537205,-0.05052876,0.00110893,-0.02543401,0.01422669,0.05903538,-0.04814131,-0.04130232,-0.06442381,0.02826544,0.01922576,-0.03548924,-0.00462083,-0.04802392,0.01933958,-0.0076071,0.01647942,-0.01473236,-0.00242685,0.02201408,-0.03051434,0.05472508,0.00501345,-0.07774984,0.11416576,0.04460349,-0.03529945,-0.00933744,0.02147144,0.08164996,-0.02273747,-0.0045844,0.01779913,0.01153483,-0.00464696,0.01284213,0.01720143,-0.02320278,-0.01847135,-0.19224954,-0.03901033,-0.04265636,-0.00811301,0.03309562,-0.03099505,-0.0142452,-0.01467828,0.00275477,0.08762772,0.0911966,-0.00830075,-0.03443886,0.06504104,-0.02318276,0.02810414,-0.05760046,-0.01474502,-0.02077476,0.02745788,-0.01519634,0.03766922,-0.01923972,-0.06447731,0.08449826,-0.00296784,0.17145504,0.01805946,0.00421589,0.02702643,0.0571273,0.03271296,-0.00490408,0.01560938,0.03670881,0.02502011,0.01885337,-0.06066803,-0.03591935,0.00275619,-0.09473242,-0.02652379,0.00552218,-0.09954847,-0.01589207,0.02248637,-0.07386655,0.04854435,0.00636504,0.10101642,0.05649159,-0.02439629,0.05405347,0.04004467,0.07094515,-0.01254749,-0.05953373,-0.02240525,0.0108052,-0.02234501,0.01796645,0.00960404,0.01576536,-0.02756805,-0.01709684,0.02641545,-0.00988713,-0.01220832,-0.01006655,-0.00774708,-0.00442112,0.00968758,0.02657296,0.03449262,-0.03344975,0.04816372,0.02225614,-0.08227765,-0.00885124,-0.04839695,0.05839319,-0.07211842,0.05174511,0.02696582,0.05370952,-0.00193539,0.05127824,0.0469641,0.04093713,-0.00737934,-0.05096965,0.00604938,-0.02764873,0.04028511,0.01596658,-0.05295777,-0.25798249,0.02166481,-0.05203141,0.05857645,-0.03637934,-0.01534046,0.01813926,0.00575285,0.01176105,-0.05725039,0.0347719,0.05954979,0.01648083,-0.03838354,0.03303754,-0.02754627,-0.0356744,-0.03368966,0.07292506,0.04914972,0.09447423,0.0535107,0.23811454,0.0057396,0.00920837,0.01620645,-0.06152217,0.02561085,-0.03528198,0.02961866,-0.0049494,-0.05182653,0.10010257,-0.02011769,0.03214539,-0.02413536,-0.00539523,0.02461279,0.01943862,0.02242683,-0.08038414,-0.03586273,-0.01738357,-0.03188841,0.16106898,0.01192757,-0.00455983,-0.05419028,0.00083205,0.03675723,-0.08613372,-0.0000552,-0.0322147,-0.0523788,-0.01780508,0.04849225,-0.02480882,0.00142573,0.00719273,0.01866289,0.06469142,-0.00230248,0.06615615,0.01822051,-0.02439399],"last_embed":{"hash":"1fzhnk6","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"1fzhnk6","at":1753423512008},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>III. Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads, etc.</u>","lines":[57,78],"size":2117,"outlinks":[{"title":"This is a powerful lottery strategy: reverse the loss to win.","target":"https://saliu.com/HLINE.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>III. Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads, etc.</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08144262,-0.04604818,-0.01796696,-0.00075143,-0.06981355,0.07907532,0.01650958,0.00505771,0.01562864,0.00735658,-0.0123489,-0.01915166,0.10705799,-0.02670232,-0.01436737,-0.0431968,-0.04272738,0.01957651,-0.08580024,0.01422896,0.06515448,-0.03276348,-0.06407753,-0.05429769,0.03764587,-0.00629246,0.00480666,-0.06228495,-0.05497633,-0.25681934,0.03832627,0.04438331,-0.01045508,-0.03023547,-0.07206605,-0.03446661,-0.06938305,0.08707181,-0.04873298,0.02802626,0.02544029,-0.00544468,0.02743725,-0.01497228,-0.00362141,0.01252067,-0.03211267,-0.00004471,0.06741581,0.01357472,0.01847951,0.05015088,-0.0016931,0.05579096,0.0562428,0.05696617,0.07892087,0.09837541,0.0299661,0.04346044,0.02460916,0.08298411,-0.18629296,-0.00222099,0.03303624,0.01168264,-0.00916037,0.02026862,-0.00920706,0.0930621,0.00019133,-0.00066008,-0.01694144,0.01214572,0.00590176,0.00872705,-0.05384902,-0.05758893,-0.04158909,0.02690481,-0.0444754,-0.02676494,-0.01706376,0.01565245,0.01191214,0.01268693,0.0622645,0.04317913,0.06229924,-0.04035366,0.03929209,0.01933544,0.03285518,0.01788079,0.0262227,0.02689317,0.0034494,0.004876,0.02326443,0.11060943,0.00030447,0.01688807,-0.0108447,0.00474343,0.04189732,-0.10659847,-0.03512109,-0.03306904,0.01551275,0.02098586,0.01145412,0.00655136,0.0651922,-0.05652518,-0.01403649,0.05717425,0.02680241,0.04165754,-0.02047449,-0.01138116,-0.0289681,0.02654128,0.01293221,0.00003801,0.00367306,-0.00412895,0.0185551,0.08863249,0.02601235,0.02982361,0.01427627,-0.02489462,-0.13811567,-0.04045226,-0.03576502,-0.01895908,0.02015455,-0.01565998,0.04053589,-0.01687458,0.00194002,-0.04514598,0.05867813,-0.06544755,0.03195094,0.0709694,0.01311641,-0.00917571,0.01241078,-0.00042729,0.01376752,-0.00143886,-0.03823886,-0.06503414,-0.00048589,0.0015811,0.04102993,0.06549948,-0.03629849,0.03107987,-0.03668377,-0.035277,-0.05150743,0.09308111,0.0051543,-0.09487657,-0.01202661,0.02150245,-0.06359231,-0.0705713,-0.04808319,-0.0075416,-0.01836251,-0.02863699,0.07920634,0.02892781,-0.10832789,-0.09740285,0.00038038,-0.01425334,-0.00381806,-0.00827617,-0.06390756,-0.00705468,-0.05719691,-0.05069339,0.00258628,-0.02215975,0.01364095,0.0570388,-0.05188685,-0.03844547,-0.06654405,0.02814516,0.01667702,-0.03406527,-0.00304494,-0.04805914,0.02007584,-0.0085254,0.01726784,-0.01282187,-0.00279118,0.02212095,-0.02752786,0.05654654,0.00465466,-0.07563134,0.11236416,0.04546954,-0.03649658,-0.00936697,0.02024827,0.08083431,-0.02328294,-0.00637383,0.01732106,0.00907777,-0.00719326,0.01286792,0.01815277,-0.02487298,-0.02121456,-0.1924586,-0.0400775,-0.04200343,-0.01057505,0.03565146,-0.03061986,-0.01561086,-0.01560149,0.00171368,0.08831973,0.09065513,-0.0080776,-0.03146802,0.06375678,-0.02246561,0.02970421,-0.05795979,-0.01387101,-0.02129986,0.02678063,-0.01539916,0.04047126,-0.01864385,-0.06339731,0.08447466,-0.00283613,0.17286971,0.01843058,0.00224638,0.02572296,0.05583497,0.03175179,-0.00413777,0.0167929,0.03752261,0.02379004,0.01948615,-0.06207181,-0.03379144,0.0006637,-0.0967429,-0.02553096,0.00982317,-0.09892347,-0.01477689,0.02089517,-0.07303666,0.04818216,0.00749274,0.10096257,0.05744598,-0.02457809,0.05321176,0.04018974,0.07010117,-0.0128028,-0.06156556,-0.02076499,0.01315938,-0.02119416,0.01976936,0.00857589,0.01531962,-0.02435801,-0.01762858,0.02709703,-0.01168921,-0.0083369,-0.01273065,-0.00619803,-0.00418752,0.00970209,0.02575313,0.03556061,-0.03359244,0.04556503,0.02219537,-0.08234376,-0.00669408,-0.04740231,0.05971603,-0.07088768,0.05167366,0.02642495,0.05549104,-0.00344367,0.05205154,0.0475488,0.04263972,-0.00580053,-0.05127265,0.0050084,-0.02658661,0.04106982,0.01791815,-0.05114523,-0.25853437,0.02038234,-0.05119723,0.0571503,-0.03604993,-0.01538523,0.01784625,0.00481363,0.00953521,-0.05762805,0.03442527,0.05819734,0.01858925,-0.03866493,0.03313359,-0.02610594,-0.03616436,-0.03244,0.072047,0.04799502,0.0944216,0.05393202,0.23940882,0.00444795,0.01022514,0.01816804,-0.06269384,0.02811984,-0.03920405,0.02757589,-0.00511966,-0.05117293,0.0980148,-0.02107274,0.03024251,-0.02204662,-0.00305232,0.02346809,0.01918369,0.02234621,-0.08143665,-0.03501462,-0.01762857,-0.03240333,0.16047426,0.0110823,-0.00467642,-0.05380117,0.00048579,0.03513966,-0.08382355,-0.00129989,-0.03037843,-0.05244163,-0.01531439,0.0481456,-0.02545013,0.00002955,0.00662015,0.01711271,0.06135739,0.00111925,0.06543598,0.01839004,-0.02679738],"last_embed":{"hash":"11a4wm8","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"11a4wm8","at":1753423512194},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>III. Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads, etc.</u>#{1}","lines":[59,78],"size":2027,"outlinks":[{"title":"This is a powerful lottery strategy: reverse the loss to win.","target":"https://saliu.com/HLINE.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>IV. Reversed Lottery Strategy Example: Skips, Decades, Frequencies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0622164,-0.04049875,-0.03169351,0.02352379,-0.09034595,0.11991468,-0.0251622,-0.00630703,0.00609251,0.01951328,-0.00417906,-0.01557218,0.08257696,-0.05631568,-0.01865531,-0.03070891,-0.02990924,0.00973086,-0.05363202,-0.01195444,0.07045053,-0.01486926,-0.02722416,-0.05887842,0.06233363,-0.032571,0.01257022,-0.061115,-0.06957083,-0.24195135,0.04214945,0.01694747,-0.02602217,-0.03828029,-0.05584684,-0.00728877,-0.08242153,0.09779085,-0.06189673,0.06252363,0.02184246,0.00950799,0.01318102,-0.01729164,-0.00641267,0.00565292,-0.06579992,-0.00256826,0.05955759,0.01873642,-0.02069442,0.04847916,0.01270732,0.03194918,0.05573269,0.00627291,0.0876891,0.09671663,0.02373201,0.02235608,0.0476566,0.05541423,-0.17629321,0.00878379,-0.00012196,0.02677291,0.01034398,0.01800135,-0.00311187,0.08829684,-0.01727111,-0.02608413,-0.01719784,0.03413766,0.05675623,0.00930286,-0.05314749,-0.05623323,-0.05732898,0.00426351,-0.02121464,-0.00470928,-0.02733438,-0.00077585,0.02233328,0.02543422,0.01656117,0.04270786,0.05127092,-0.05416055,0.04884414,0.03448806,0.02023773,0.0250305,-0.01781634,0.00111135,0.03523317,0.02284355,0.02144871,0.10061845,0.02952079,-0.00206707,0.00641407,0.04078785,0.02909047,-0.10634271,-0.04205554,-0.03107174,0.01175831,0.03043772,0.02786881,0.02714592,0.07315136,-0.05737571,-0.04054122,0.04695699,0.00001582,0.06132976,-0.03417943,-0.00217856,-0.01890964,0.02343587,-0.01306711,0.01006099,0.01158126,-0.0074875,0.01492598,0.08302392,0.07742163,0.02040266,0.00566055,-0.00787765,-0.13265862,-0.03176204,-0.04706848,-0.02803466,0.01170521,0.0151735,0.02473166,-0.03152484,0.00311295,-0.03765349,0.05352864,-0.06248346,0.00716207,0.05553394,0.02204755,0.01344153,-0.0051844,-0.00338527,0.01643968,-0.0090484,-0.0226301,-0.07149541,-0.00499046,0.02548673,0.03102093,0.07904193,-0.03037903,0.0139933,-0.03566761,-0.01287802,-0.04400487,0.12236744,-0.01610407,-0.08458723,-0.0441502,0.02722182,-0.04411821,-0.07917327,-0.0433477,-0.00998733,-0.01120098,-0.05241232,0.08950929,0.02429224,-0.09466348,-0.10028997,0.00221831,0.00893285,0.02850994,-0.02022043,-0.05478855,-0.00707504,-0.0297339,-0.04700432,-0.01673528,-0.04176424,0.04495625,0.06226538,-0.05950759,-0.0367004,-0.07503063,0.00794341,0.00427093,-0.06298333,-0.00175626,-0.03042531,0.0269096,-0.01018476,0.02051223,-0.02760749,0.02957443,0.01922503,-0.02163206,0.04423915,0.05585653,-0.07506299,0.13654615,0.01840246,-0.02612271,0.01403005,0.04904535,0.06151821,-0.04890605,-0.01332939,0.00260114,0.03541379,0.01174344,0.02287028,0.01051678,-0.01182455,-0.03190369,-0.19998509,-0.0606662,-0.0215484,0.01756496,0.04361554,-0.03719622,0.00934202,-0.01633353,0.03512612,0.05631093,0.05514426,-0.0157498,-0.06419256,0.06798025,-0.02171385,0.00754437,-0.07411557,-0.03515492,0.00680668,0.05542561,-0.0348186,0.0371495,-0.03290614,-0.07402698,0.09659037,-0.03417976,0.13462843,-0.01400748,0.01426846,-0.00507577,0.04358387,0.03259214,-0.00618599,0.01886068,0.02825625,0.0257625,0.04194266,-0.00686675,-0.05350522,-0.01087626,-0.04605477,0.00115473,-0.00983577,-0.07547002,-0.04661411,-0.01478107,-0.04568722,0.02869838,0.01835808,0.10610286,0.05914708,-0.03516095,0.05513938,0.05481784,0.07163414,-0.01592371,-0.07333514,-0.00603436,0.0079226,-0.02944534,0.01730613,0.00323283,0.02638244,-0.03781368,-0.00894615,0.01759331,-0.01045138,-0.02629093,0.00395603,-0.00470042,0.01870831,0.06258564,0.03437033,0.00388383,-0.02943512,0.04274038,-0.0039181,-0.08959433,-0.00875229,-0.04966689,0.04714138,-0.03709096,0.04522859,0.02262381,0.05259036,0.01832327,0.05485349,0.01462854,0.03670506,-0.00673922,-0.03860351,0.01464677,-0.01809518,0.0287349,0.01227485,-0.03777876,-0.26437879,-0.00143535,-0.07139125,0.06652413,-0.01654271,0.00753023,0.00911569,-0.00789258,0.02261267,-0.03937327,0.02369036,0.04928345,-0.003299,-0.05024343,0.03414247,-0.01571702,0.01046577,-0.04363603,0.06666391,-0.00110739,0.09508212,0.05204323,0.23532879,-0.00357892,0.00380488,0.00818679,-0.01693042,0.02754259,-0.01093176,0.0265181,0.00612695,-0.03889861,0.11248492,0.00441984,0.02852855,-0.00257767,-0.02707497,0.01328034,-0.01127833,0.00576674,-0.06945621,-0.04532121,-0.03055415,-0.00021709,0.14279248,0.0080386,-0.0061156,-0.07547033,0.00812794,0.04990863,-0.10777453,0.00646179,-0.06420286,-0.05290277,-0.01278814,0.07826759,-0.01338874,-0.02760767,0.05726803,0.01566467,0.05058173,-0.02251889,0.0881004,0.02594167,-0.0172263],"last_embed":{"hash":"13fj25d","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"13fj25d","at":1753423512371},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>IV. Reversed Lottery Strategy Example: Skips, Decades, Frequencies</u>","lines":[79,103],"size":2185,"outlinks":[{"title":"This is a LIE strategy for pick-3 digit lottery.","target":"https://saliu.com/ScreenImgs/loss-win-pick3.gif","line":12},{"title":"A lotto strategy is many reductions in one: Straight software, purge, reversed.","target":"https://saliu.com/HLINE.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>IV. Reversed Lottery Strategy Example: Skips, Decades, Frequencies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06436768,-0.04180626,-0.03127627,0.02314741,-0.08818448,0.1198569,-0.0271261,-0.00664541,0.00885846,0.02034351,-0.00367365,-0.01663703,0.08165798,-0.05720199,-0.01672386,-0.02822443,-0.03164632,0.00861621,-0.05402637,-0.01192818,0.07355867,-0.01553701,-0.02555393,-0.06068261,0.06288324,-0.03194262,0.01275477,-0.06244218,-0.06971321,-0.24313666,0.04400007,0.01427499,-0.02819823,-0.03728604,-0.05692859,-0.0083228,-0.08217485,0.09794964,-0.06047364,0.06234422,0.02205428,0.01035777,0.01419931,-0.019948,-0.00793731,0.00452288,-0.06787787,-0.00469372,0.05902577,0.01889443,-0.02021549,0.0478026,0.01159745,0.03200244,0.05505401,0.0025202,0.08935508,0.0975252,0.02277905,0.02163147,0.04660346,0.05613483,-0.1754106,0.00805247,0.0003057,0.02548138,0.01175087,0.0145569,-0.00117431,0.08844564,-0.01713798,-0.02426923,-0.01770054,0.03643378,0.05557311,0.01034744,-0.05108198,-0.05824269,-0.05592931,0.00785313,-0.02327263,-0.00427749,-0.02723192,-0.00156617,0.02362483,0.02461155,0.01403482,0.03955406,0.0488074,-0.05286459,0.04856158,0.03346995,0.0182308,0.0244738,-0.01559699,0.00243722,0.03504857,0.02254365,0.02169789,0.09848632,0.03267937,-0.00118813,0.00835941,0.0417297,0.02826278,-0.1045567,-0.04527319,-0.03125294,0.009296,0.03061265,0.02775432,0.02642464,0.07180001,-0.05707623,-0.03989265,0.048159,0.00082675,0.05905728,-0.03328804,-0.0030419,-0.01890351,0.02287406,-0.01228718,0.00919634,0.00954644,-0.00714007,0.01557993,0.08347843,0.07541452,0.02147252,0.00791594,-0.01126065,-0.13279001,-0.03321015,-0.0480037,-0.02749701,0.01147232,0.01463719,0.02320646,-0.03137069,0.00361452,-0.03806753,0.05219244,-0.06051351,0.0051701,0.05628741,0.02239653,0.01368588,-0.00474565,-0.00427057,0.01713249,-0.0084512,-0.02273549,-0.07166311,-0.00431254,0.02731241,0.03059127,0.07880943,-0.02876829,0.01666348,-0.03888291,-0.01347905,-0.04191121,0.12418263,-0.01813059,-0.08422115,-0.04122079,0.02800828,-0.04305816,-0.07849383,-0.0417549,-0.00874077,-0.01108502,-0.05233555,0.09060469,0.02430338,-0.09508098,-0.09951683,0.00443002,0.00932292,0.02779106,-0.02269885,-0.055959,-0.00899184,-0.03140264,-0.04806218,-0.01497733,-0.03921315,0.04567387,0.06179311,-0.05983941,-0.03483506,-0.07490436,0.0089459,0.00249648,-0.06204018,-0.00185667,-0.02823527,0.02708669,-0.01036529,0.02267209,-0.02778569,0.03095538,0.01788821,-0.01771676,0.04568742,0.0570716,-0.07325407,0.13613673,0.01741834,-0.02724178,0.01391643,0.04940835,0.0597766,-0.05092744,-0.01295345,0.00220627,0.03347339,0.01055163,0.02080897,0.01117705,-0.01154716,-0.03202,-0.20204756,-0.0600124,-0.01983934,0.01773229,0.04421309,-0.0379863,0.00934855,-0.0138651,0.03582896,0.05678597,0.05301811,-0.01557606,-0.06324326,0.06874306,-0.02196601,0.01093261,-0.07395147,-0.03581373,0.00692638,0.05541563,-0.03676726,0.03627886,-0.03245795,-0.0740725,0.09910427,-0.03465274,0.13515928,-0.01620056,0.011813,-0.00507023,0.04132539,0.03056744,-0.00448747,0.01793521,0.02917671,0.02495757,0.04156811,-0.00693188,-0.05140454,-0.01153933,-0.04508658,0.00148706,-0.00952556,-0.07427883,-0.04477914,-0.01471311,-0.0444537,0.02746816,0.02018038,0.10474222,0.06007666,-0.03600663,0.05351145,0.05514399,0.07082815,-0.01774209,-0.0738763,-0.00486357,0.0071719,-0.02681881,0.01779406,0.00275006,0.02503337,-0.03540543,-0.00944436,0.01760508,-0.00893216,-0.02431608,0.00464005,-0.00294774,0.02006633,0.06574439,0.03481786,0.00257356,-0.03046973,0.04025492,-0.00489807,-0.08897296,-0.00756012,-0.04869984,0.04718779,-0.03258758,0.04348016,0.02267513,0.05426747,0.01767325,0.05663143,0.01560283,0.03741052,-0.00441449,-0.03777751,0.01569305,-0.01754677,0.02924737,0.01332446,-0.038572,-0.26633182,-0.00390284,-0.07473046,0.06778245,-0.01444122,0.00471801,0.00873617,-0.00750747,0.02240633,-0.0404397,0.02243793,0.04927068,-0.00208792,-0.04926952,0.03263417,-0.01304573,0.01020796,-0.04396243,0.0671328,-0.0029335,0.09691351,0.0520923,0.23647504,-0.00508271,0.00419072,0.00915668,-0.01633778,0.02840919,-0.01139183,0.02610284,0.0039031,-0.03837658,0.11267665,0.00640048,0.02708608,-0.00103834,-0.02791889,0.01420706,-0.0127671,0.00481987,-0.06980148,-0.0470709,-0.03214666,-0.00038984,0.14101771,0.00853183,-0.00710992,-0.0754754,0.00810071,0.05023848,-0.10681073,0.00683727,-0.06391045,-0.0537225,-0.01075981,0.07751371,-0.01336139,-0.02807933,0.05553768,0.01678049,0.04964587,-0.02162401,0.08710184,0.02528239,-0.01639019],"last_embed":{"hash":"15aymy7","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"15aymy7","at":1753423512540},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>IV. Reversed Lottery Strategy Example: Skips, Decades, Frequencies</u>#{1}","lines":[81,103],"size":2107,"outlinks":[{"title":"This is a LIE strategy for pick-3 digit lottery.","target":"https://saliu.com/ScreenImgs/loss-win-pick3.gif","line":10},{"title":"A lotto strategy is many reductions in one: Straight software, purge, reversed.","target":"https://saliu.com/HLINE.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06391861,-0.02854257,-0.01599937,0.02390245,-0.05139048,0.10482522,0.00930814,-0.00454944,0.01562004,-0.00722939,-0.00546791,0.02140591,0.05950765,-0.02973952,-0.01597867,-0.02637966,-0.02535253,-0.01035711,-0.04682454,0.00267249,0.09815709,-0.05817913,-0.04399972,-0.04557224,0.04818472,-0.02897269,0.00640628,-0.07146907,-0.063985,-0.2238128,0.05493997,0.0054908,-0.02031335,-0.05224163,-0.05795122,-0.02551328,-0.06617498,0.1071993,-0.03668412,0.05010163,0.00733758,0.01817705,0.00717833,-0.02283672,-0.01489521,0.00480755,-0.04302026,0.022673,0.06121575,0.01967518,-0.01371056,0.0431467,0.02297997,0.01950382,0.05892868,0.00589354,0.08408673,0.11043098,0.03660231,0.02515403,0.04187151,0.06005745,-0.17562668,0.00192056,-0.01781876,0.01865656,0.00020397,0.01876733,0.00389614,0.10976521,-0.01310338,-0.00202162,-0.01706493,0.02486539,0.04539083,-0.03174759,-0.05632636,-0.04897171,-0.0686905,0.01224159,-0.0421622,0.00178795,-0.01127064,0.01983892,-0.01271464,0.02397483,0.03039345,0.05221387,0.05763195,-0.06218708,0.05750753,0.04958058,0.02446664,0.00609136,0.0064879,-0.00573407,0.03276545,0.00410826,0.02328612,0.11729898,0.03902164,0.02175434,0.0073891,0.0078975,0.04324129,-0.11472178,-0.06032274,-0.04957965,0.01183375,0.02632171,0.03074913,0.00713574,0.06571624,-0.07120395,-0.00944515,0.0567828,0.01021128,0.0373206,-0.02939416,0.02378383,-0.02817478,0.02058552,-0.01860093,-0.00465598,-0.01520032,-0.00936664,0.00174904,0.08810535,0.0597911,0.00627731,-0.00137531,-0.0242482,-0.15151013,-0.02673522,-0.02003912,-0.04145616,0.03153996,0.00819272,0.02056912,-0.01871306,-0.02291405,-0.0240536,0.05353974,-0.06528703,-0.00820617,0.08447618,0.03439638,0.0024546,0.00247056,-0.02656543,0.01438051,0.00777928,-0.04244568,-0.07398833,-0.01316132,0.00589327,0.04948467,0.08030489,-0.04334015,-0.01431966,-0.03927175,-0.00104654,-0.03464523,0.1139185,-0.02421951,-0.06783324,-0.02915682,0.00265686,-0.04504539,-0.09340686,-0.05203186,-0.01996024,-0.02130292,-0.0491803,0.09622172,0.02447251,-0.1201554,-0.10741783,0.01831208,-0.00914218,0.02012905,-0.01396816,-0.03922131,0.00584481,-0.03363315,-0.04213552,0.01179039,-0.04822941,0.05040922,0.06441087,-0.07378605,-0.02655767,-0.06335263,-0.00295829,0.01568544,-0.03993729,-0.01473217,-0.03552856,0.037224,0.00794968,0.02392318,-0.05242233,-0.00394324,0.01904125,-0.01017454,0.03819507,0.01216185,-0.08608674,0.13776366,0.00909404,-0.05770992,0.02276793,0.03227809,0.07641386,-0.03649342,-0.00587225,0.01667575,0.03013321,-0.00143731,0.00760904,0.00072126,-0.00065419,-0.01350546,-0.15926111,-0.07298848,-0.04536922,0.01332648,0.02453696,-0.02086372,-0.01593956,-0.03059214,0.0342524,0.06647265,0.06711667,-0.02184794,-0.04150639,0.08599545,-0.00380221,0.01245403,-0.05631941,-0.01342631,-0.034087,0.04735435,-0.0204429,0.00803354,-0.03649956,-0.07782832,0.08720421,0.00484237,0.15170148,-0.03380458,0.02193323,-0.00089872,0.05943316,0.01696451,0.01048406,0.02534831,0.02310474,0.02965772,0.03751343,-0.0408221,-0.0648066,-0.00576348,-0.06549912,-0.00991151,-0.00619956,-0.09240216,-0.04480183,0.03929378,-0.05702679,0.07820328,0.00831916,0.08123384,0.06106981,-0.02548712,0.05497788,0.04948587,0.08191931,-0.00191514,-0.07278952,-0.01215129,-0.01807581,0.00602872,-0.00793608,-0.0014259,0.03067851,-0.03497786,0.03117919,0.00404509,0.0127192,-0.03700354,0.01198853,-0.02307518,-0.01399436,0.04570729,0.0151204,0.0055501,-0.03666558,0.03702119,0.0120194,-0.03781585,-0.02488011,-0.0420138,0.05235049,-0.06147721,0.02889811,0.03937473,0.02106257,0.0284223,0.03952182,-0.01426298,0.02235764,-0.01171062,-0.0623251,0.00729262,-0.0491292,0.02756077,-0.02605674,-0.00739586,-0.25993699,0.02018952,-0.05682214,0.10432819,0.00891132,-0.03019464,0.02911626,0.03501393,0.04360956,-0.0651073,0.05441868,0.06123064,-0.02344931,-0.03169362,0.0341336,-0.05194261,0.02997334,-0.05135059,0.07500658,0.01687746,0.08009105,0.03796138,0.22123182,-0.00917255,0.026257,-0.01591693,-0.02001398,0.05201416,-0.02620414,0.03574781,0.01382017,-0.02077572,0.08730366,-0.01677055,0.01615181,-0.00558199,-0.01014349,0.04575913,0.00237846,0.00271363,-0.07864574,-0.03770456,-0.00407876,-0.01250873,0.13363273,0.02238844,-0.01544171,-0.09158553,-0.00831688,0.07057578,-0.08617326,0.00201428,-0.03933214,-0.06051687,-0.004589,0.08531936,-0.01006043,-0.00722955,0.03552149,0.01091389,0.05349303,0.00399224,0.0500121,0.02601121,-0.00491622],"last_embed":{"hash":"wi9rsl","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"wi9rsl","at":1753423512720},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>","lines":[104,143],"size":4667,"outlinks":[{"title":"We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing.","target":"https://saliu.com/HLINE.gif","line":25},{"title":"_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":29},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":30},{"title":"This program applies the LIE ELIMINATION reversed lottery strategy feature introduced in Bright lotto software, in combination generators.","target":"https://saliu.com/ScreenImgs/LieIDLotto.gif","line":37}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06676117,-0.02888077,-0.01351642,0.01897666,-0.04441553,0.10366794,0.00159979,-0.01104717,0.01659446,-0.0044196,-0.00407866,0.02074881,0.06014241,-0.02989863,-0.01274548,-0.02733188,-0.02433587,-0.01014022,-0.04630687,0.00465441,0.09419439,-0.06027243,-0.03773563,-0.04495848,0.04480464,-0.03085576,0.0023598,-0.07204465,-0.06550035,-0.22759789,0.05523356,0.00248334,-0.01957947,-0.05260012,-0.05552019,-0.02878841,-0.065667,0.10706288,-0.04050888,0.04997443,0.00817883,0.01529251,0.01037018,-0.02425971,-0.01739726,0.00874308,-0.04452091,0.02550246,0.05827159,0.01903493,-0.0157035,0.04084341,0.01641382,0.01819485,0.05285024,0.00111822,0.08667113,0.11009476,0.03357935,0.03009947,0.04111372,0.06172502,-0.16963369,-0.00145927,-0.02004559,0.01737377,-0.00328711,0.01566991,0.00483207,0.10928022,-0.02067939,-0.00333721,-0.0195614,0.03130259,0.04401286,-0.03440687,-0.0496152,-0.04697272,-0.07075121,0.01564194,-0.04529441,0.00246588,-0.01236328,0.02430824,-0.01125539,0.02263827,0.03384985,0.05310559,0.05207941,-0.06652426,0.06225169,0.04735822,0.02630573,0.001691,0.00420641,-0.00505768,0.02863276,0.01332206,0.02327739,0.11728766,0.03409625,0.01807456,0.01149459,0.00279674,0.04320339,-0.11256061,-0.05606553,-0.04824905,0.01170212,0.0320944,0.02952853,0.00567778,0.0582369,-0.07241534,-0.00315162,0.0600976,0.00922424,0.03786715,-0.04018419,0.02267395,-0.03043807,0.02259346,-0.02004834,-0.0043953,-0.01771188,-0.01367576,0.00264537,0.08596746,0.06594431,0.00798335,-0.00824946,-0.02467045,-0.15318418,-0.03300923,-0.01973642,-0.04000432,0.02646962,0.00991185,0.01991752,-0.020018,-0.02226559,-0.01933755,0.05246508,-0.060983,-0.00926497,0.0851953,0.03468046,0.00412541,0.00297075,-0.02702873,0.01797431,0.01111774,-0.04289435,-0.07521791,-0.00701243,0.00161816,0.04696792,0.07678755,-0.04496384,-0.00952698,-0.03757932,-0.00127146,-0.03001361,0.11994106,-0.02795701,-0.06838551,-0.02578919,0.009017,-0.0379118,-0.09177995,-0.05013602,-0.02285671,-0.01442324,-0.05071029,0.09675469,0.01817786,-0.1177328,-0.10506171,0.01698261,-0.00670248,0.01894991,-0.0131368,-0.04069048,0.01050142,-0.03567727,-0.04394113,0.01246637,-0.04166896,0.05149479,0.06775774,-0.07736042,-0.02192924,-0.06606357,-0.00529351,0.01123237,-0.04078383,-0.01303075,-0.03453952,0.03330403,0.00809978,0.02564294,-0.05687357,-0.00357383,0.01800874,-0.00922255,0.03749171,0.00963402,-0.08518136,0.13867207,0.00726285,-0.06165826,0.01986232,0.03362305,0.07444578,-0.041429,-0.00616184,0.01280683,0.02755351,-0.00238605,0.004135,-0.00176653,-0.00419702,-0.01139686,-0.16631909,-0.07153446,-0.04404977,0.01356019,0.02843114,-0.02016477,-0.01460641,-0.02551923,0.03072606,0.06900943,0.06734974,-0.02476385,-0.04500998,0.08467699,0.00080854,0.0095868,-0.05284121,-0.01234067,-0.03462837,0.04835654,-0.02111442,0.00349884,-0.03332887,-0.07649393,0.08981395,0.00784352,0.15270936,-0.03190745,0.02110957,-0.00097748,0.05950361,0.01798473,0.01335267,0.01936195,0.02480846,0.0247394,0.03536249,-0.03595502,-0.06075498,-0.00227419,-0.06081123,-0.01217756,-0.00542003,-0.09251327,-0.03867603,0.03837701,-0.05443739,0.0773818,0.00861995,0.07683657,0.06271093,-0.02722431,0.05265646,0.04894145,0.08210475,-0.00968293,-0.07018522,-0.01315235,-0.01367645,0.00692352,-0.00438085,-0.00234738,0.03183381,-0.0335215,0.03122844,0.00544793,0.00764958,-0.03005465,0.01488685,-0.01767413,-0.01540904,0.05066117,0.01488259,0.00702945,-0.03490341,0.03408625,0.01078249,-0.03781359,-0.02711208,-0.04598353,0.05549481,-0.05449266,0.02728201,0.03725242,0.02217472,0.02858163,0.03840647,-0.0123911,0.02548157,-0.01163746,-0.06107897,0.00267213,-0.0479175,0.03160877,-0.02064297,-0.00422629,-0.26097223,0.01336815,-0.06073841,0.10365948,0.00670641,-0.03155824,0.03151809,0.03774766,0.0489541,-0.06559824,0.04903468,0.06330597,-0.01835513,-0.03502329,0.03417045,-0.05203343,0.031402,-0.0484954,0.08367391,0.01760739,0.07908598,0.03531718,0.228302,-0.00969259,0.02097865,-0.01140387,-0.01968357,0.05722378,-0.02154468,0.03482182,0.01193168,-0.01835754,0.08297389,-0.0081101,0.0138041,-0.00367929,-0.0156552,0.04855997,0.00176772,0.00492704,-0.08175652,-0.04146747,-0.0044994,-0.01078578,0.13151619,0.01882474,-0.01285394,-0.09267355,-0.01010056,0.06843143,-0.08214965,0.00281062,-0.03847726,-0.06326728,0.00099372,0.08845366,-0.00854933,-0.01078548,0.04048655,0.00949832,0.05547978,0.00569022,0.05312037,0.02658902,-0.00701515],"last_embed":{"hash":"h8dhmb","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"h8dhmb","at":1753423512878},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{1}","lines":[106,129],"size":3228,"outlinks":[{"title":"We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing.","target":"https://saliu.com/HLINE.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09931006,-0.032285,0.00149128,-0.00133371,-0.06826893,0.06153075,0.02341445,0.02248924,0.01667122,0.00631024,-0.01302824,-0.02254785,0.06590481,-0.06027536,-0.02355428,-0.03932673,-0.02070141,0.03328204,-0.02327504,-0.02063334,0.08599091,-0.04213417,-0.05409969,-0.05273258,0.03891736,0.00618741,-0.03542456,-0.06660582,-0.05849013,-0.20578644,0.0500542,-0.0158712,0.00506212,-0.05435289,-0.04560228,-0.02112166,-0.05647974,0.0527641,-0.07523646,0.05290888,-0.00265781,-0.00009864,-0.01139898,-0.03829462,0.02169083,-0.00518758,-0.02486224,0.01512777,0.04826979,-0.00078995,0.01489972,0.0673724,-0.01882767,0.06283759,0.06088045,0.02821433,0.08722211,0.10840884,0.02212078,0.0357592,0.0595958,0.05295104,-0.18459566,0.00793478,0.002006,0.03650185,0.02201302,-0.00627185,-0.00818513,0.09415906,-0.0241493,-0.03085517,-0.00697165,0.04610137,0.04066541,-0.01303658,-0.01546029,-0.04970505,-0.05299937,0.00566766,0.00366202,-0.01872161,0.0004516,-0.00471265,-0.02920904,0.02042229,0.03781438,0.05436683,0.08105729,-0.06715282,0.02983013,0.02005345,0.08013003,0.02191779,0.02942176,-0.01081915,0.04161193,0.02953814,0.02242887,0.13274235,0.00801482,0.00280372,-0.02167674,0.0059903,0.07839581,-0.08409275,-0.01188639,-0.0440504,0.02988747,0.0255068,0.03809743,0.0058719,0.08521271,-0.04926351,-0.00169912,0.04509736,0.0027665,0.05092609,-0.03128536,0.02007934,-0.03209207,0.03997637,-0.01349583,-0.01538448,-0.02394067,-0.01616248,-0.00890482,0.06831107,0.01374306,0.00860825,0.02839179,-0.0133352,-0.14876409,-0.04510308,-0.00597162,-0.01713697,0.01115075,-0.05279691,0.01864465,0.00726558,-0.04389481,-0.01748171,0.03139248,-0.07693988,0.00106443,0.06109941,0.04171505,-0.01758594,0.02140133,0.01615898,0.01230177,0.00396432,-0.04980715,-0.07628401,0.00640975,-0.0194047,0.04697876,0.09135042,-0.0283866,0.01271757,-0.01031294,0.01296135,-0.03089786,0.10419849,-0.01471296,-0.07510817,-0.02066062,0.03892723,-0.02704607,-0.08958372,-0.06337807,-0.01180335,-0.055784,-0.0537479,0.10310105,0.00902994,-0.09370124,-0.12587787,-0.00287725,-0.02633164,0.02287367,-0.03723818,-0.01186619,0.02128671,-0.03548731,-0.02849734,-0.00878503,-0.04725863,0.01407233,0.03968235,-0.04933811,-0.0026521,-0.09776527,-0.0301171,0.04806652,-0.05345092,-0.0479131,-0.03489592,0.03584851,0.00141916,-0.01845653,-0.02330935,0.0092865,0.03169081,-0.02786323,0.04071509,0.00259314,-0.08554167,0.09905815,0.05823294,-0.046069,0.04994993,0.02525955,0.07148772,-0.02041689,-0.00641769,0.01135054,0.03326229,-0.00374399,0.01986452,-0.00114566,0.07316975,-0.03702285,-0.1849667,-0.05258966,-0.07265486,-0.0174541,0.02573465,-0.03233517,-0.01890078,-0.02416015,0.03694442,0.02599727,0.09873185,-0.01696338,-0.0287949,0.10234457,-0.02260341,0.00921393,-0.04592744,-0.00770604,-0.03452215,0.01133696,-0.03703877,0.01894322,-0.0137336,-0.09026297,0.06942534,0.02012435,0.14615555,0.00706685,0.04188539,0.03417914,0.04911863,0.02723777,-0.01840346,-0.05416908,0.02880781,0.05968595,0.04298206,-0.04116594,-0.06422079,0.01098076,-0.07867457,-0.0013824,0.01882496,-0.08565286,-0.04912459,0.03189091,-0.05832073,0.0359909,0.04234717,0.1211711,0.04218793,-0.01263018,0.04549107,0.07952307,0.07704246,-0.0066557,-0.03905874,-0.01349977,0.02084272,-0.00605931,-0.02006541,-0.01401647,0.00685959,-0.04942828,0.02132544,-0.00653367,0.0110308,-0.0435056,0.01738651,-0.02359053,-0.00793539,0.06977936,0.00151223,0.00167964,-0.04053322,0.05354759,0.05084977,-0.0271896,-0.05773047,-0.05093049,0.03036349,-0.06450294,0.06112072,0.03927314,0.05843164,0.00584278,0.03470989,0.01482804,0.00340728,-0.00246517,-0.01188918,-0.00919749,-0.0432129,0.00131175,-0.01548633,-0.01164175,-0.24663949,-0.01961076,-0.0639974,0.10497857,-0.01838647,0.0264216,0.01589379,0.0097666,0.00925166,-0.04744855,0.03919351,0.04762997,-0.00056379,-0.00914005,0.03848832,-0.04558678,0.02650039,-0.05186065,0.06459004,0.04584855,0.04473483,0.07238217,0.2084821,-0.00955452,-0.00374055,0.0205254,-0.03092267,0.03536046,-0.02329403,0.04168233,0.00132614,-0.04381423,0.0750014,-0.01307448,0.01458184,-0.03430758,-0.01642206,0.02943534,0.03846439,0.01607394,-0.08857597,-0.02600542,-0.00529146,-0.01532803,0.14954187,0.01580209,-0.01321274,-0.07125172,0.00343928,0.06382585,-0.08103278,-0.0257427,-0.08895804,-0.03652061,0.00931335,0.06855258,-0.02357811,-0.01072788,0.01638872,-0.01013971,0.07203574,0.00558031,0.04979632,0.01512429,-0.02482749],"last_embed":{"hash":"r89qxu","tokens":117}}},"text":null,"length":0,"last_read":{"hash":"r89qxu","at":1753423513058},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{2}","lines":[130,130],"size":306,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12194667,-0.04122022,-0.01153976,0.02760119,-0.04675816,0.06979822,-0.01162401,0.00072508,0.01639325,0.03009497,0.00127574,-0.00476071,0.06140243,-0.0247074,-0.00688579,-0.04166515,-0.00624034,0.02304205,-0.01270621,-0.02393785,0.07736591,-0.03416455,-0.06013739,-0.03723708,0.06280889,0.02432505,-0.02953935,-0.07089263,-0.05391718,-0.21367298,0.05085472,-0.01346418,-0.03188745,-0.05959706,-0.0438669,-0.0149253,-0.04312197,0.05082382,-0.06557576,0.03885501,0.00859905,-0.00102141,-0.0196305,-0.02701571,0.01204602,-0.02408213,-0.02345009,0.01236228,0.03400755,-0.01016047,-0.01008247,0.05054599,0.02019747,0.0501205,0.05898253,0.0263665,0.05935949,0.10955348,0.01404661,0.02351617,0.08147492,0.04095076,-0.17737028,0.00312129,-0.00513276,0.01896635,-0.01234853,-0.0163903,0.01318158,0.0981554,-0.02529924,-0.02086973,-0.02074716,0.03789293,0.02863459,-0.02157959,-0.00550958,-0.0452789,-0.04378898,0.0050732,-0.02043447,-0.04198514,-0.01409321,-0.02764832,-0.01145862,0.01967519,0.02563422,0.08142982,0.05575301,-0.07863432,0.03627359,0.02501522,0.09298723,0.02188497,0.01852752,0.00773501,0.02831387,0.03354892,0.01714398,0.12100701,0.01813916,0.00514094,-0.03348599,-0.00309511,0.09572956,-0.07578823,-0.00668014,-0.06120487,0.00763654,0.02514285,0.03129654,0.01129622,0.08387308,-0.03617242,-0.00497658,0.04065897,0.00676158,0.05581012,-0.04620403,0.02896783,-0.04517189,0.02733697,0.00931654,-0.00570527,-0.0400448,-0.02501362,0.00044772,0.06642634,0.02121132,-0.01073766,0.04252267,-0.02422446,-0.14238118,-0.03687173,0.00806911,-0.00304168,-0.01469151,-0.02881868,0.02711067,0.00760212,-0.04899964,-0.01462724,0.03009974,-0.08883657,-0.00164566,0.07234132,0.01485979,-0.01298775,-0.00245975,0.01924055,0.01197892,0.00447701,-0.02785204,-0.10844086,0.00173545,-0.02076558,0.04075865,0.09275647,-0.05261775,-0.00613209,0.00705481,0.01051067,-0.05087671,0.14869854,-0.0207372,-0.07608143,-0.01413081,0.02898109,-0.00401308,-0.07540381,-0.06462969,-0.01248669,-0.04072525,-0.03102184,0.0976082,0.00219867,-0.06802833,-0.11809931,0.00039328,-0.03145375,0.01459295,-0.04392412,-0.02967702,0.01440704,-0.02415015,-0.05939747,-0.01155201,-0.02308295,0.02910493,0.01836491,-0.0605199,-0.00020643,-0.05460062,-0.01716852,0.01069084,-0.04104808,-0.05156474,-0.02430778,0.03480747,-0.00283401,-0.00385004,-0.01927726,0.01997477,0.02500668,-0.01700602,0.05757803,-0.00212861,-0.08278682,0.12032556,0.04007115,-0.05391759,0.03474096,0.05123285,0.0798873,-0.03042942,-0.00814939,-0.01354291,0.02719152,-0.03524334,0.01043937,-0.0225286,0.09346189,-0.00135866,-0.20260273,-0.02276387,-0.04928643,-0.03523466,0.03320165,-0.03322702,-0.00708722,-0.01632764,0.05057512,0.06217088,0.07420287,-0.02001151,-0.01959737,0.06488287,0.00833194,-0.00896655,-0.0447958,-0.02035282,-0.04215683,0.01438686,-0.02966521,0.03472374,-0.02253918,-0.11206985,0.06331582,0.0056494,0.13943453,0.01689919,0.06100229,0.00630731,0.05466841,0.02449593,-0.02482672,-0.05327278,0.0329433,0.0644212,0.04214094,-0.02462531,-0.07141391,0.00810943,-0.0751342,0.01040891,-0.00255005,-0.09964621,-0.01356727,0.03351052,-0.04719555,0.05667849,0.03326554,0.11279553,0.01868535,0.0052613,0.02795794,0.064674,0.08706195,-0.03091273,-0.05411395,-0.03963514,0.02892375,-0.00238559,-0.01260773,0.00230453,0.00126416,-0.04162718,0.03021298,-0.01785941,-0.00142269,-0.05774913,0.01824965,-0.03049665,-0.0181403,0.07660452,0.01158238,-0.02073905,-0.02854517,0.05095104,0.03934577,-0.03554032,-0.04309236,-0.04530198,-0.00036061,-0.06437343,0.04649303,0.04670045,0.04407521,0.02137575,0.03757645,0.0106502,-0.03129734,-0.00154681,-0.01271384,-0.01060954,-0.04915764,0.032456,0.00937003,-0.00920672,-0.25557032,-0.01295698,-0.02735074,0.05652908,-0.01300775,0.02273197,0.02833183,0.0136377,0.00049326,-0.05303544,0.03103868,0.05497239,0.00325923,-0.00014687,0.00637118,-0.04569872,0.06326979,-0.03108818,0.07800352,0.04140899,0.03649448,0.05528443,0.22998863,0.00043174,-0.00279767,0.02623075,-0.01164579,0.04765583,-0.01901078,0.03705595,0.00977347,-0.01491648,0.07766107,-0.00201177,0.00713138,-0.02133368,-0.03882366,0.03365017,0.03880782,0.00271864,-0.07336742,-0.03465932,-0.01312825,-0.00046595,0.14541167,0.02417459,0.00268686,-0.07934438,0.00832224,0.08044793,-0.07338969,-0.03672608,-0.08681376,-0.02910794,0.02165894,0.06283081,-0.01707377,-0.03215893,0.03700512,-0.01295206,0.07282759,-0.01362323,0.07459581,0.03300652,-0.00198232],"last_embed":{"hash":"1fkwbd6","tokens":169}}},"text":null,"length":0,"last_read":{"hash":"1fkwbd6","at":1753423513095},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{3}","lines":[131,131],"size":411,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07104735,-0.03992901,-0.00660526,0.03594827,-0.04980737,0.05527651,0.0348781,0.0081837,-0.00058608,0.01136935,-0.02748723,-0.04302959,0.05990601,-0.0329592,-0.00404097,-0.04981699,-0.01771776,0.05570059,-0.03053851,-0.00102181,0.09620934,-0.03373681,-0.06555154,-0.02828534,0.07288672,0.00717502,-0.01922148,-0.0437958,-0.06523947,-0.23399138,0.06743693,-0.01243364,-0.02817782,-0.05146309,-0.04777681,-0.02033956,-0.07533031,0.06090616,-0.08080352,0.04038394,0.04642171,-0.00897978,0.00972679,-0.01190591,0.01162136,-0.00464933,-0.02677207,0.00929314,0.05658142,0.00177183,0.00448671,0.06023883,0.013486,0.03455013,0.04462726,0.04820389,0.0772635,0.12530676,0.04370874,0.0374911,0.04476978,0.05982141,-0.17226544,-0.00229338,-0.01335489,0.02396139,0.01203592,-0.0301854,-0.00535066,0.08194135,-0.01059604,-0.01349033,-0.00414457,0.03648444,0.01878579,-0.02796482,-0.01301258,-0.04758657,-0.04404074,0.01693365,-0.02144239,-0.03847706,-0.00702435,-0.01034332,0.01244591,0.00303802,0.02088028,0.05457564,0.06432089,-0.08540986,0.03396792,0.01696567,0.06135175,0.01748784,0.01043076,-0.00506019,0.01359875,0.02440724,0.02555594,0.12887502,0.01218555,0.00281942,-0.02061369,-0.00334229,0.07948194,-0.09657984,-0.02493978,-0.04690271,0.02057694,0.04110259,0.00647785,0.00068136,0.08440477,-0.03681697,-0.00569293,0.04136115,0.00529261,0.03942316,-0.0319486,0.00943464,-0.03522206,0.02295244,-0.02408071,0.00158962,-0.03586952,-0.05021563,0.00691275,0.07170095,0.03922889,-0.00416189,0.02068494,-0.03659706,-0.1223819,-0.05351438,-0.01839322,-0.01377856,0.00773801,-0.00386566,0.03190432,-0.04171855,-0.04122956,-0.05060367,0.03566708,-0.07372654,-0.01093956,0.04369985,0.03336383,-0.01448701,0.00249473,0.0205457,-0.00698534,0.003584,-0.03582739,-0.07601132,-0.00086152,-0.04070627,0.03369345,0.1010254,-0.0159499,0.00776285,-0.00334427,0.01439037,-0.03129332,0.09262094,-0.02966588,-0.07687767,-0.02559893,0.03736022,-0.02831167,-0.07293664,-0.07736075,-0.01758196,-0.03825869,-0.06576934,0.08066733,0.00239629,-0.07130349,-0.0991685,-0.02540559,-0.00249897,0.00024082,-0.06428477,-0.02360014,0.02090514,-0.03047359,-0.0426808,0.01355218,-0.05011889,0.05111494,0.01773665,-0.09591041,-0.0275129,-0.09916826,-0.01703825,0.00589094,-0.02993898,-0.03052743,-0.06807358,0.00976134,0.01686334,-0.00269194,-0.00095098,-0.00318058,0.03876499,-0.02492049,0.04686109,0.0105743,-0.09295715,0.11979303,0.02501703,-0.0378473,0.0332063,0.03146088,0.06332473,-0.02501507,-0.00759577,-0.00599321,0.01605464,-0.01620934,0.00244195,0.00815012,0.04859823,-0.0408006,-0.1836938,-0.03588751,-0.03993604,-0.02620208,0.01237872,-0.04719907,0.00159604,-0.00085542,0.0566767,0.04276953,0.08481316,-0.01045654,-0.02643203,0.06760118,-0.00023972,-0.02503082,-0.02294068,-0.01309718,-0.02010324,0.02594092,-0.03643012,0.01712442,0.00214277,-0.08724161,0.07011615,0.02272809,0.15338358,0.04793,0.053812,0.01361937,0.04806017,0.02896007,-0.01113938,-0.03602561,0.03161166,0.03223933,0.0360772,-0.05571944,-0.04033371,0.01414496,-0.07311879,0.01298133,-0.01097366,-0.12350217,-0.02627028,0.0274369,-0.04440614,0.06149672,0.0521141,0.12725729,0.06488799,-0.03365804,0.05053301,0.08693475,0.09985074,-0.01622881,-0.04924118,-0.03169128,0.0106326,-0.0080071,-0.0252956,0.01819432,0.02073653,-0.04535931,0.03996243,0.01950236,0.00070966,-0.06695148,0.04394105,0.01603765,-0.02590193,0.07451689,0.02104813,-0.0177387,-0.01341039,0.06449349,0.05778943,-0.05680794,-0.05612024,-0.03223783,0.04947229,-0.05235854,0.0435014,0.03174329,0.04406805,0.02493775,0.02466998,-0.01091958,0.00948894,-0.01414872,-0.01720123,0.02353687,-0.02726677,0.03363403,-0.00069366,-0.01995388,-0.26409301,-0.01557733,-0.08622672,0.06938052,-0.01789434,0.00667305,0.03418804,0.00749593,0.02662103,-0.0773158,-0.00511224,0.04294078,0.0230151,-0.00693113,0.02648893,-0.03432143,0.03440634,-0.04108351,0.0687734,0.03933017,0.05407167,0.05363414,0.22030415,0.00160948,-0.02357835,0.02910604,-0.02003425,-0.00049848,0.00316707,0.0369158,-0.01035792,-0.02938952,0.06155531,0.0083795,0.04372454,-0.01787258,-0.04403273,0.04878026,0.03933517,0.03389046,-0.07814518,-0.03567319,0.01202505,-0.01478865,0.13623382,0.00878342,0.00882161,-0.06760389,0.00601062,0.04081521,-0.07253113,-0.01670084,-0.06736369,-0.04580718,0.00358452,0.07715691,-0.02427502,-0.01013067,0.0499986,0.01905457,0.06316906,0.00038074,0.07442097,0.03122611,-0.00108606],"last_embed":{"hash":"3zji0g","tokens":174}}},"text":null,"length":0,"last_read":{"hash":"3zji0g","at":1753423513157},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>#{6}","lines":[135,143],"size":344,"outlinks":[{"title":"This program applies the LIE ELIMINATION reversed lottery strategy feature introduced in Bright lotto software, in combination generators.","target":"https://saliu.com/ScreenImgs/LieIDLotto.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08806879,-0.0259899,-0.03308295,-0.00011878,-0.0826977,0.08372929,-0.00022925,0.00451608,0.00736061,0.01376634,-0.0007277,-0.02144752,0.06671186,-0.05834296,-0.02368747,-0.06826512,-0.00058065,0.050864,-0.03478156,-0.02474455,0.06736497,-0.02973672,-0.07707069,-0.07274517,0.04641226,0.01795113,-0.02119451,-0.03947606,-0.05027298,-0.19962186,0.0362688,-0.00152023,-0.01323932,-0.05559005,-0.07070448,0.00021027,-0.05415931,0.02475929,-0.06805143,0.04205092,0.0160697,-0.00044237,0.02441518,0.01077063,0.05657977,-0.02136223,-0.00684105,0.02931363,0.08854867,-0.001797,-0.04359642,0.0572524,0.01113094,0.04279305,0.08907928,0.05004448,0.07944142,0.12741999,0.03157486,0.02779896,0.03385583,0.07283578,-0.2055051,0.03914979,0.01547729,0.03842657,0.00659399,0.00841876,0.01431774,0.05433341,-0.02714594,-0.02208646,0.0022608,0.0479429,0.04764565,-0.01082423,-0.05734848,-0.04279352,-0.03325025,-0.01254078,-0.01994616,-0.00295519,0.00739962,0.01731934,-0.00780048,0.03470503,0.04912641,0.04823643,0.11090595,-0.08982512,0.02990209,0.04066473,0.06199605,0.02795244,0.01571108,-0.01237595,0.05028735,0.00490116,0.02895863,0.11498968,0.01137647,0.00466214,0.00144188,0.00542209,0.04831182,-0.12160251,-0.01256618,-0.03373297,0.0196148,0.03761674,0.03292328,0.01558376,0.04188769,-0.0516662,-0.02496727,0.01991778,-0.00470533,0.03581431,-0.01163951,0.0024023,-0.03733424,0.00918938,0.00837461,0.00288537,0.00397281,-0.02925849,-0.01190807,0.07068536,0.02579063,0.02777837,0.03804997,0.02679774,-0.16053765,-0.03617813,-0.00473895,-0.01139064,0.02145151,-0.05261398,0.01661997,0.01027904,-0.02312891,-0.03809742,0.03673534,-0.08869734,-0.02312127,0.05768934,0.02772316,-0.00962028,-0.0042915,-0.00791973,0.0180164,-0.03023684,-0.02249525,-0.06397342,0.01278182,0.00869178,0.0874796,0.06553954,-0.03929763,0.00154754,-0.04475009,-0.01287654,-0.05112299,0.13538948,-0.0035825,-0.09377473,-0.01480888,0.04749463,-0.02139461,-0.09390177,-0.05206783,-0.01559106,-0.0547785,-0.03191039,0.11996191,0.00198529,-0.08186489,-0.09722137,-0.00070267,-0.02192775,0.01512719,-0.04403393,-0.00579496,0.01514313,-0.05279946,-0.05236061,-0.01554746,-0.04745683,0.0106633,0.04796257,-0.02566661,-0.01019676,-0.06616729,-0.02447561,-0.00770619,-0.03724428,-0.05026444,-0.06194849,0.04632236,-0.01503513,0.00313938,-0.00665921,0.00882347,0.01768633,-0.02623,0.04275884,0.0010471,-0.08155509,0.08577807,0.03899398,-0.03148723,0.02697073,0.04963434,0.04313116,-0.02667046,0.01347364,0.03361626,0.01850596,-0.00968199,0.04723097,-0.00061253,0.01231776,-0.05588774,-0.16652711,-0.04832313,-0.06460984,-0.02144275,0.00434201,-0.03320326,0.01217131,-0.0494933,0.02773332,0.05484723,0.10914033,-0.03373274,-0.01820103,0.0906724,-0.01136796,-0.01286678,-0.05855002,-0.0342298,-0.0505484,0.03685729,-0.01928001,0.00822103,-0.0054492,-0.08715598,0.02897045,-0.00452031,0.12498628,-0.00903962,0.04033696,0.03606759,0.07510151,0.01905345,-0.0262172,-0.03222508,0.01885264,0.04688855,0.01256489,-0.02254348,-0.05254967,0.01396162,-0.0811706,0.01855521,0.02535106,-0.07428534,-0.06831058,0.01402369,-0.0262478,0.02184993,0.02258044,0.09473512,0.01926716,-0.00957313,0.05032977,0.05070918,0.05298852,0.00276585,-0.03744535,-0.01421124,-0.02820088,0.00191304,-0.04445641,-0.03143547,0.0212518,-0.03222282,0.03151444,-0.00705279,-0.0133389,-0.04311384,0.01121212,-0.00998567,-0.01070772,0.07162686,0.02039311,0.03410542,-0.01246448,0.04679673,0.06484179,-0.06315608,-0.0336532,-0.0277337,-0.03960726,-0.09086667,0.07031114,0.0557714,0.09222384,0.03512569,0.05126291,0.00405848,0.00495609,-0.00549507,-0.02038836,-0.01571411,-0.04806861,0.03066925,0.01277204,0.02198296,-0.25990617,0.01053685,-0.03550956,0.06845934,-0.02463172,-0.02863775,0.02733571,-0.02791189,0.02628619,-0.04489528,0.0413537,0.01105675,0.00151638,-0.02951113,0.04368456,-0.03810644,0.02955213,-0.02977566,0.07795526,0.04876832,0.03206887,0.06850043,0.22858065,-0.00414066,0.0095902,0.01724827,-0.02522138,0.02417614,0.00334668,0.06831595,0.02944702,-0.01537089,0.07545124,0.00247231,-0.00972827,0.00008523,-0.04111813,0.03568225,0.02809462,0.00244828,-0.07075445,-0.032425,-0.01378253,0.00840984,0.12691276,0.02820397,-0.00864744,-0.09279484,0.03717672,0.05229216,-0.08213957,0.00581059,-0.08450524,-0.02889497,-0.01060344,0.03277532,-0.0129508,-0.02085792,0.0361437,-0.01447714,0.07838792,-0.02235461,0.06582514,0.03185597,-0.01356265],"last_embed":{"hash":"1pbj5pz","tokens":405}}},"text":null,"length":0,"last_read":{"hash":"1pbj5pz","at":1753423513224},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>","lines":[144,178],"size":4040,"outlinks":[{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":9},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":11},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":12},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":13},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":14},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":15},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":16},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":17},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":18},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":19},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":20},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":21},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":22},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":23},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":24},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":25},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":26},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":27},{"title":"**Lottery Software, Lotto Software Applications**","target":"https://saliu.com/infodown.html","line":28},{"title":"Apply the lottery strategy in reverse: Not-lose to win.","target":"https://saliu.com/HLINE.gif","line":30},{"title":"Forums","target":"https://forums.saliu.com/","line":32},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":32},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":32},{"title":"Contents","target":"https://saliu.com/content/index.html","line":32},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":32},{"title":"Home","target":"https://saliu.com/index.htm","line":32},{"title":"Search","target":"https://saliu.com/Search.htm","line":32},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":32},{"title":"This impressive lottery software and strategy are based on the science of logic.","target":"https://saliu.com/images/HLINE.gif","line":34}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06404151,-0.04931621,-0.00657431,0.00232658,-0.07421368,0.06534287,0.02842934,-0.00141376,0.01380583,0.01954737,0.00127956,-0.01133683,0.06519148,-0.04694513,-0.01454591,-0.04951677,0.00178535,0.06551915,-0.03369481,-0.02175929,0.07779036,-0.02377745,-0.09225515,-0.07483876,0.02342837,0.02609076,-0.02125341,-0.0520955,-0.03373633,-0.16045314,0.04237722,0.00428599,-0.01813835,-0.04832552,-0.06184018,-0.03740135,-0.0652099,0.04034035,-0.09029634,0.04076698,0.02699181,0.01569873,0.01138182,0.00339096,0.02857506,-0.0236961,0.0014803,0.02367884,0.10069446,-0.01237825,-0.00576523,0.07930046,0.00715498,0.05320428,0.07189827,0.03888693,0.08255938,0.11953065,0.02504255,0.01399303,0.03227136,0.07270183,-0.17292044,0.01412113,0.00852992,0.05504179,0.02262627,0.02697873,0.00347511,0.08207066,-0.00088139,-0.02685292,0.01284021,0.04511483,0.04338337,-0.0000381,-0.04095978,-0.04937853,-0.02309719,-0.01563045,-0.01878124,-0.02450087,0.00463287,0.03956892,-0.02848172,0.01860173,0.04859634,0.04028149,0.10841241,-0.09514274,0.0209923,0.0402705,0.04980582,0.02761912,0.02555374,-0.02560147,0.04576671,0.01442068,0.04512982,0.14494939,-0.0020883,0.00570141,0.00819478,-0.02172469,0.04594282,-0.11065945,-0.03348946,-0.02899554,0.00990599,0.06405302,0.04921673,0.02823992,0.07497318,-0.04118032,-0.00929752,0.00873822,-0.01857013,0.04573204,-0.01463202,-0.00590754,-0.0457358,0.02924659,0.00301874,-0.00612402,-0.00381753,-0.0249325,-0.01540401,0.08318269,0.01640405,0.01164693,0.0288505,-0.00919705,-0.15410279,-0.02821944,-0.00386293,-0.02029608,0.02970883,-0.05800883,-0.01064134,0.02376889,-0.02127093,-0.01312594,0.0584439,-0.10351611,-0.01251185,0.05302567,0.02576093,0.01029175,-0.01394366,-0.0025507,0.00588072,-0.01734929,-0.02220121,-0.06619243,0.00527299,0.0068109,0.0676916,0.03385496,-0.02884493,0.01778915,-0.05076706,0.0131956,-0.04524393,0.12503624,-0.00371485,-0.05723787,-0.01196723,0.04093539,-0.03033859,-0.08266155,-0.06624361,-0.0032419,-0.06348035,-0.05054055,0.13352366,-0.00339057,-0.10737751,-0.09308717,-0.01381847,-0.03654517,0.02902073,-0.03314625,0.00424613,0.00646055,-0.02876068,-0.0418722,-0.01683825,-0.04612866,0.01826415,0.05200712,-0.03010128,-0.00031332,-0.05996225,-0.03804252,0.0003296,-0.03023745,-0.06851521,-0.0462654,0.05023289,-0.01788802,-0.00883754,-0.00230159,0.00283068,0.03637882,-0.04944374,0.04117107,0.01176239,-0.08297817,0.07464354,0.05890539,-0.0550952,0.02032443,0.04177967,0.05088774,-0.04280436,-0.00565513,0.03785561,0.04217117,-0.01913875,0.02137177,-0.00889336,0.03457915,-0.04727957,-0.16158429,-0.0676407,-0.03908871,-0.00847674,0.02362936,-0.02256909,-0.01400632,-0.03890735,0.02610701,0.04787442,0.09881749,-0.04553648,-0.0239119,0.10330684,-0.02644464,-0.00318879,-0.05892606,-0.02963176,-0.02653391,0.01885983,-0.02120323,0.00455357,0.00350904,-0.11657749,0.02374396,0.01590764,0.1323638,-0.0284622,0.05132676,0.04050222,0.04952038,0.0067645,-0.0330087,-0.01464554,0.03178512,0.03749466,0.0007231,-0.05103308,-0.0465011,0.02363545,-0.0677393,-0.01578542,0.01826214,-0.06801314,-0.04454895,0.01385109,-0.05129732,0.02178997,0.03676705,0.10571116,0.00560399,0.00955115,0.05966393,0.05490445,0.05804425,0.00458638,-0.04425839,-0.01888562,-0.01414292,-0.00716025,-0.02541596,-0.03517201,-0.00565297,-0.02745522,0.03985061,-0.0169834,-0.02117219,-0.04433507,0.00443581,-0.03494512,-0.00207502,0.06326994,0.00790199,0.01914814,-0.01307302,0.04560525,0.07993139,-0.07278614,-0.04410758,-0.02951502,-0.01261141,-0.0894543,0.07395054,0.07394093,0.08664933,0.01378052,0.03728146,0.01087153,-0.00889745,-0.01776904,-0.02472337,0.00251506,-0.05636118,0.01074516,-0.00912441,-0.00376,-0.24708468,0.01476114,-0.03494065,0.07657851,-0.05407365,-0.03824352,0.03142531,0.01800176,0.01854682,-0.04184661,0.04570032,0.00710556,-0.00726594,-0.0173452,0.05326772,-0.02138918,0.05136054,-0.040363,0.0871526,0.05056924,0.03092785,0.08130284,0.23277402,-0.00797608,0.03578729,0.01455413,-0.04031389,0.02994616,-0.0111471,0.0713938,0.03928743,-0.01799368,0.05739172,-0.00281536,-0.00179293,0.00064655,-0.01562336,0.04108894,0.02594849,0.01917864,-0.08807727,-0.03681538,-0.03351446,0.00629726,0.11575083,0.00221794,-0.01187059,-0.10596546,0.02291455,0.04696815,-0.09095453,0.02324965,-0.08204631,-0.04750473,-0.0014413,0.04399388,0.00444958,-0.03371714,0.03784398,-0.0302583,0.05717721,-0.04107112,0.05173993,0.01546747,-0.035078],"last_embed":{"hash":"1m22jsz","tokens":89}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423513421},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{3}","lines":[149,149],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06364825,-0.01553486,-0.00592569,-0.02374472,-0.0622527,0.07234584,0.02538898,0.00601972,0.01889179,0.00965061,0.01006589,-0.01088399,0.08185535,-0.06939302,-0.03337548,-0.07066154,-0.01736756,0.04725951,-0.02408784,-0.03528509,0.06522445,-0.00833043,-0.08319964,-0.07381298,0.0470322,0.00802997,-0.02716399,-0.05604973,-0.07074624,-0.18798275,0.04865253,-0.0020587,-0.01609453,-0.03891901,-0.0483922,-0.00197644,-0.04573311,0.02218249,-0.03647151,0.04020641,0.02892796,-0.00892923,0.01762058,0.02457373,0.04074823,-0.01591226,-0.01290642,0.0233,0.06547675,-0.00131346,-0.04054691,0.07120662,0.03136614,0.03762126,0.06714857,0.04718878,0.07420056,0.11739469,0.05388819,0.036051,0.04431784,0.05585404,-0.19827688,0.01483241,0.03491938,0.0394592,0.00450884,0.01535861,-0.01721228,0.0670971,-0.02935614,-0.03280959,0.00832949,0.04212352,0.06507742,-0.0100988,-0.06765807,-0.04981698,-0.03707759,-0.00705097,-0.0114731,0.00341561,0.01504374,0.03047623,-0.03629655,0.01308735,0.05472316,0.03246063,0.1195891,-0.07781231,0.04324461,0.05202021,0.03641723,0.02341929,0.0184526,-0.0332967,0.02894431,0.00432308,0.05438991,0.14194669,-0.01802545,0.0006371,-0.00294957,-0.01891517,0.04166443,-0.12053958,-0.02323766,-0.02299328,0.02865861,0.03584129,0.02038283,0.02857445,0.03773913,-0.05448466,-0.0133231,0.02248141,-0.01653583,0.02193249,-0.02327624,0.00916845,-0.03479703,-0.00231704,0.00883465,0.00236961,-0.00208093,-0.03193591,0.01218717,0.0802461,0.0173156,0.0050725,0.03114081,0.00393695,-0.1738119,-0.03358343,-0.01236589,0.00757456,0.03107397,-0.06807039,0.023088,0.01369751,-0.03015421,-0.00571149,0.04484143,-0.08321719,-0.01122267,0.07946925,0.03866716,-0.01522503,-0.02001154,-0.01520615,0.02761791,-0.02792641,-0.01519273,-0.05214635,0.01240227,0.01032035,0.06139804,0.06052195,-0.01197746,-0.00401522,-0.0464147,-0.00821573,-0.04102828,0.13687447,-0.00484946,-0.07204982,-0.00924996,0.03092623,-0.02711308,-0.09560245,-0.03002875,-0.02405991,-0.0655698,-0.04359769,0.0803507,0.01421837,-0.07442199,-0.09442152,-0.00220212,-0.0301225,0.04073441,-0.03511692,-0.01145844,0.02878581,-0.04564028,-0.03939176,-0.01853778,-0.02089133,-0.00354432,0.06065316,-0.01990274,-0.01150841,-0.08185647,-0.02245035,0.02376375,-0.04447248,-0.04835032,-0.0503781,0.04914724,-0.04518517,0.00183524,-0.01096136,-0.00008288,0.0285085,-0.00465331,0.04668602,-0.01593312,-0.07105143,0.07458741,0.04230497,-0.04837629,0.02838575,0.02947216,0.03998031,-0.02572919,-0.00760402,0.03746297,0.00931292,0.00421483,0.04264964,0.00197509,0.03485592,-0.0148735,-0.17801787,-0.06724148,-0.03820724,-0.02173186,0.00298338,-0.01909781,-0.00669832,-0.03916157,0.02106488,0.05680704,0.09141942,-0.02112189,-0.02092156,0.09026937,-0.01570314,-0.00340359,-0.06259399,-0.03962611,-0.06152385,0.04177858,-0.03205829,0.01376452,-0.02762618,-0.07884081,0.03237252,0.00782522,0.11442598,-0.03944038,0.05022431,0.03231792,0.06975998,0.01583998,-0.02297181,-0.0315087,0.02723961,0.05042053,0.01727857,-0.03519048,-0.05284589,0.02884157,-0.09257621,0.01944676,0.01829727,-0.07047216,-0.05296044,0.0401022,-0.00890384,-0.00040221,0.02519102,0.09431347,0.01333015,-0.02191412,0.05326001,0.07839274,0.06915621,-0.00269872,-0.02475392,0.01158121,-0.01986729,-0.0203656,-0.05504028,-0.05165709,0.02083007,-0.02106741,0.0326357,-0.02286353,-0.00937012,-0.0369004,0.00738511,-0.0228569,-0.01020466,0.07318772,-0.00755953,0.01084647,-0.02984717,0.06388453,0.07568132,-0.07925666,-0.05745643,-0.02785958,0.00294317,-0.09773263,0.0768757,0.02938164,0.08047047,0.0273265,0.04359048,-0.01135158,0.01243816,-0.00664145,-0.02191461,-0.03494886,-0.04388359,0.02905518,0.00268063,-0.00412547,-0.23861155,0.01141612,-0.03576529,0.07386246,-0.0455883,-0.02147118,0.02900624,-0.00235173,0.0344454,-0.02351245,0.04438819,0.00542446,-0.013717,-0.03816338,0.03976996,-0.03637316,0.07204809,-0.03502157,0.07748666,0.05737374,0.03224863,0.07498103,0.23665878,-0.00362296,0.02141716,0.01604624,-0.02748881,0.01711972,-0.01185375,0.06494175,0.05229918,-0.02745531,0.0731221,-0.01055051,-0.01186192,-0.02712034,-0.02601147,0.06479088,0.05872004,0.01487042,-0.07489106,-0.03441457,-0.03625866,-0.04465855,0.11860728,0.02483164,-0.01791336,-0.09086466,0.01784746,0.03701499,-0.07786249,0.00701923,-0.09744003,-0.04301464,-0.01166974,0.04250655,-0.01705592,-0.01624728,0.06238538,-0.00825033,0.09697517,-0.02650825,0.04338558,0.03704059,-0.01557244],"last_embed":{"hash":"1sdoqa9","tokens":96}}},"text":null,"length":0,"last_read":{"hash":"1sdoqa9","at":1753423513473},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{5}","lines":[151,151],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07324257,-0.06009037,-0.02356044,0.01367566,-0.08958803,0.09437797,0.04233951,0.0052734,0.01298812,0.01338037,-0.00453484,-0.03511921,0.0639637,-0.04177307,-0.01705287,-0.05382984,-0.02676935,0.05680786,-0.04292656,-0.00920208,0.0917164,-0.04134314,-0.07662256,-0.0569379,0.02859579,0.01471271,-0.00782908,-0.04446305,-0.04153777,-0.18191418,0.02569278,0.01013899,-0.03204881,-0.05126994,-0.07351794,-0.02630376,-0.06121257,0.06211227,-0.05866489,0.06114523,0.02907974,-0.00208163,0.00157383,0.00740635,0.03615434,-0.02125698,-0.02693238,0.00963861,0.07281504,-0.01398846,-0.0054687,0.07434545,0.00431862,0.05150998,0.09108525,0.04431708,0.10486685,0.12088043,0.04243648,0.02581407,0.03433968,0.06108358,-0.18970741,0.01209492,0.01714818,0.03264142,0.01366063,0.02034522,-0.00877785,0.08276645,-0.00598242,-0.02516249,0.00507136,0.04721248,0.04195796,0.00095797,-0.04522885,-0.0577425,-0.018512,-0.00178709,-0.01484742,-0.01679186,-0.01329553,0.01816715,0.00370772,0.02133017,0.04129277,0.04374626,0.09062465,-0.07798968,0.03312352,0.03487762,0.04589791,0.03179279,0.01607464,-0.01725184,0.03049426,0.01186154,0.05351147,0.12574367,0.011729,0.0193942,0.00056677,0.01336407,0.04796081,-0.11518913,-0.03015433,-0.02791181,0.03035605,0.05115021,0.03556678,0.02165414,0.07295681,-0.03472485,-0.00343954,0.02310036,0.00824389,0.05244225,-0.01338784,0.0033081,-0.03638731,0.03189665,0.02107866,-0.00328012,-0.00296828,-0.02418497,-0.0296752,0.08545341,0.03047982,0.03573691,0.02703342,-0.01461016,-0.14997581,-0.03615003,-0.02603211,0.01064379,0.04449804,-0.06849341,0.00057717,0.02705146,-0.0100649,-0.03142222,0.04899647,-0.07927834,-0.0144232,0.06726621,0.02032091,0.00908206,0.01027263,0.00750126,0.00147532,-0.02218716,-0.01536363,-0.05809339,-0.01690349,0.00284554,0.04553752,0.06089225,-0.02657319,0.02099128,-0.04999049,-0.02322986,-0.04701996,0.12882625,-0.00318596,-0.04831835,-0.01725262,0.01849551,-0.0224423,-0.09636157,-0.05132755,-0.01046824,-0.05273557,-0.03311234,0.12768598,0.00612338,-0.08518945,-0.10392074,-0.02196738,-0.0118249,0.01480386,-0.04263388,-0.0017261,0.01012531,-0.05432256,-0.02632613,-0.00599152,-0.04523923,0.02374281,0.054594,-0.04338323,-0.01998912,-0.08392885,-0.03628802,0.02496458,-0.03698134,-0.05216715,-0.05436911,0.04400164,-0.02334464,-0.0147657,-0.00428169,-0.00634664,0.02560352,-0.04618019,0.04985431,0.01180792,-0.06611201,0.06726822,0.04012353,-0.03195596,0.02639904,0.02420269,0.04041823,-0.01554025,-0.01299348,0.05880507,0.03364467,0.01107656,0.01376901,0.00886952,0.01040598,-0.03247994,-0.16804019,-0.06481985,-0.05156401,-0.01731839,0.01522772,-0.03723444,-0.01730558,-0.03223484,0.03193727,0.05347473,0.0780483,-0.02462892,-0.03138588,0.079811,-0.01310941,-0.00051133,-0.05950614,-0.04583361,-0.02575652,0.03046424,-0.04407516,0.03315818,-0.02336268,-0.1083723,0.03082606,0.0203473,0.13787769,0.00229074,0.02428609,0.03161301,0.05130053,0.03392235,-0.03962417,0.0078262,0.0385147,0.0262595,0.00051708,-0.04330038,-0.06571473,0.00091483,-0.08511138,0.02132724,0.00828819,-0.05613232,-0.04629717,0.02106025,-0.05366468,0.03459904,0.03623284,0.10483255,0.00943142,-0.00922037,0.05178666,0.05507078,0.06063442,0.00924189,-0.0314161,-0.02390456,-0.02138864,-0.00788527,-0.03337268,-0.01048229,0.01280121,-0.04222822,0.02845646,-0.01122678,-0.00795601,-0.05146881,-0.00648125,-0.01251049,-0.01324345,0.06416027,0.03130949,0.01279678,-0.019853,0.0380673,0.04662912,-0.06211527,-0.03654785,-0.03591355,0.00964568,-0.10950975,0.06385833,0.05603666,0.09252901,0.00855912,0.02583645,-0.00694738,0.00217001,-0.00967607,-0.03622579,0.00590442,-0.05289556,0.02284874,0.00118829,-0.00605341,-0.26564792,0.01457963,-0.04714733,0.07750081,-0.04051299,-0.01950985,0.03825756,0.00835085,0.01659872,-0.04477342,0.0641737,0.01031392,0.001245,-0.01267386,0.03352292,-0.03219422,0.02917594,-0.04264707,0.0888063,0.05100003,0.03749757,0.08195356,0.23488812,-0.00721696,0.00890567,-0.00943503,-0.04182069,0.02540647,-0.02286522,0.06687013,0.03668598,-0.02500124,0.06903073,-0.01865413,0.00449881,0.00935136,-0.02479856,0.04035322,0.05031939,0.00100439,-0.08485147,-0.06071175,-0.02699163,0.01406603,0.12726158,0.02592388,0.00515826,-0.09207661,0.02601327,0.04187852,-0.07517168,-0.00174607,-0.09249909,-0.04702366,-0.01424429,0.04158538,-0.01121268,-0.03098486,0.04002775,-0.02225752,0.06388115,-0.01154565,0.05439426,0.01620802,-0.03332888],"last_embed":{"hash":"7kdphw","tokens":114}}},"text":null,"length":0,"last_read":{"hash":"7kdphw","at":1753423513508},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{9}","lines":[155,155],"size":227,"outlinks":[{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{26}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09239891,-0.03230095,-0.02598275,0.00762828,-0.06645418,0.1003909,0.01215381,0.02672538,-0.0006306,0.00292387,-0.02899488,-0.04093044,0.06302379,-0.0460132,-0.0187748,-0.07802218,-0.01787136,0.05007818,-0.02851246,-0.00079733,0.08618179,-0.00926108,-0.05669229,-0.05056458,0.03956886,-0.00693634,-0.00833658,-0.04296221,-0.05035703,-0.20255528,0.04667797,-0.02421815,-0.03025336,-0.0409255,-0.07882883,0.01009875,-0.04200231,0.04407647,-0.05101356,0.06333306,0.02782285,-0.00450026,0.01119137,0.00584372,0.04202389,-0.00831229,-0.01459374,0.00154839,0.07755968,-0.00014603,-0.0397355,0.0788881,-0.00438991,0.03565853,0.08853687,0.05892165,0.09312309,0.12298335,0.03917595,0.02344791,0.03500935,0.06031742,-0.21595693,0.01996615,0.0096027,0.01806886,-0.00183317,0.01954101,0.01116004,0.06516048,-0.01467131,-0.02145973,-0.00410956,0.04160535,0.05968323,-0.03337202,-0.04487083,-0.04570562,-0.02091679,-0.0255054,-0.02516473,-0.01826721,-0.02433907,-0.00463527,0.01514163,0.0354855,0.02118763,0.05974464,0.07927368,-0.08553018,0.04998282,0.03459603,0.05023799,0.02175879,0.00993038,-0.01446348,0.02068699,0.00604854,0.0323979,0.1225751,0.00737015,0.01586778,-0.01756718,0.02927313,0.07437228,-0.10609792,-0.03157168,-0.02246848,0.0236468,0.04656412,0.04115104,0.03342425,0.05914424,-0.04457905,-0.02175776,0.01470469,0.0168884,0.05110679,-0.02178819,0.00583011,-0.05720888,0.0244172,0.01009309,-0.00108679,-0.00468244,-0.03903979,-0.0163656,0.0636692,0.04130645,0.03616859,0.01598506,-0.00557966,-0.15952636,-0.05512556,-0.01911997,-0.00307997,0.00241313,-0.04880391,0.00731183,0.00592446,-0.02309334,-0.04500806,0.03656663,-0.0785011,-0.01705858,0.04660578,0.02388069,0.00615302,-0.01372147,0.00780745,-0.00624625,-0.0221585,-0.00292223,-0.0631758,-0.00308075,-0.00392815,0.06121422,0.08319639,-0.04250025,0.02086591,-0.04293033,-0.01264309,-0.02999324,0.12628075,-0.00154955,-0.06643211,-0.02678381,0.03068548,-0.00042854,-0.09219319,-0.05259972,-0.01028231,-0.04123909,-0.04921724,0.10834353,0.00640497,-0.08351193,-0.11178409,-0.01074451,-0.01207999,0.00554078,-0.0270982,-0.00066532,0.00713355,-0.06265452,-0.05467324,0.00957888,-0.06835192,0.01152885,0.04782711,-0.0517154,-0.03209724,-0.06784652,-0.0220583,0.01554887,-0.02964691,-0.0513084,-0.05866358,0.04648099,0.01327869,-0.01901751,-0.00645611,0.010701,0.01298752,-0.05114606,0.04969069,0.00395798,-0.08377151,0.08269282,0.02718909,-0.01606688,0.05486699,0.04405822,0.05509917,-0.03454144,-0.00087778,0.04008074,0.02096379,0.01014309,0.02421643,0.01484682,0.02387976,-0.03916759,-0.17788699,-0.04031999,-0.0859819,-0.04692692,0.01610318,-0.02638556,0.00724646,-0.039212,0.03308286,0.04301069,0.0978926,-0.01100853,-0.02114153,0.09531461,-0.00706004,-0.00758926,-0.05318645,-0.03787173,-0.04273671,0.03930412,-0.032974,0.02703064,-0.00744274,-0.09328392,0.04304158,0.00212975,0.12719418,0.01178272,0.05442797,0.01944073,0.06461535,0.03478087,-0.02738994,-0.03223752,0.03469099,0.03917183,0.00399968,-0.02201821,-0.05559089,0.01761126,-0.08361582,0.03123578,0.00836434,-0.06823441,-0.07202443,0.03098583,-0.0465301,0.02128031,0.01787617,0.09887911,0.04914679,-0.00094893,0.04843978,0.05870104,0.07245606,-0.00199795,-0.04713425,-0.02345181,-0.01675955,-0.00700015,-0.04192789,-0.00224312,0.04453989,-0.03741854,0.02077301,-0.0253194,-0.00424586,-0.05137641,0.00944839,-0.01114866,-0.01853353,0.08008254,0.01225229,0.02487717,0.00021328,0.0441412,0.03290866,-0.06052919,-0.03939084,-0.02975313,-0.00770199,-0.10235923,0.07691894,0.03819651,0.08588819,0.02095591,0.03271322,-0.02735224,0.00865735,0.00022949,-0.02196318,-0.00231099,-0.05228738,0.04492558,0.01223885,-0.00288401,-0.25645554,0.00448549,-0.03311527,0.06443467,-0.0272715,-0.02217824,0.03918358,-0.01810478,0.01319402,-0.03868251,0.06388178,0.02123843,-0.01770055,-0.03572858,0.02935111,-0.03491352,0.02218429,-0.02556108,0.1021467,0.04146109,0.0345213,0.07784677,0.22482148,-0.01651918,-0.00398342,-0.00548758,-0.01656701,0.01923915,-0.00184817,0.0572737,0.02882046,-0.02198523,0.08027967,-0.01399572,0.00540061,-0.00194483,-0.01663665,0.03058442,0.03946942,-0.01997136,-0.07885561,-0.01391308,-0.00162967,0.00812204,0.13661748,0.01050502,0.00858421,-0.06875167,0.03643019,0.0425044,-0.07229429,-0.00495349,-0.06621891,-0.01331299,-0.01098221,0.0321481,-0.00497493,-0.02594276,0.03528988,-0.00821349,0.07553069,-0.00103889,0.05723022,0.0145366,-0.01106969],"last_embed":{"hash":"191w6fk","tokens":286}}},"text":null,"length":0,"last_read":{"hash":"191w6fk","at":1753423513553},"key":"notes/saliu/Lottery, Lotto Strategy in Reverse Turn Loss into Win.md#Lottery, Lotto Strategy in Reverse: Turn Loss into Win#<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>#{26}","lines":[173,178],"size":601,"outlinks":[{"title":"Apply the lottery strategy in reverse: Not-lose to win.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"This impressive lottery software and strategy are based on the science of logic.","target":"https://saliu.com/images/HLINE.gif","line":5}],"class_name":"SmartBlock"},
