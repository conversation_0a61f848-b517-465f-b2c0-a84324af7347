
"smart_sources:notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md": {"path":"notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1ud4agg","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753366197395,"size":6302,"at":1753423416500,"hash":"1ud4agg"},"blocks":{"#---frontmatter---":[1,6],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis":[8,48],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{1}":[10,15],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{2}":[16,17],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{3}":[18,48]},"outlinks":[{"title":"Software to calculate the combination lexicographical order of drawings in any lottery results file.","target":"https://saliu.com/ScreenImgs/DrawIndex.gif","line":41}],"metadata":{"created":"2025-07-24T22:09:56 (UTC +08:00)","tags":["lotto","lottery","software","odds","formula","probability","strategy","software","system","statistics","drawings","analysis","numbers","winning","combinations","randomness","lotto jackpot","random play"],"source":"https://saliu.com/bbs/messages/919.html","author":null}},
"smart_sources:notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md": {"path":"notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12232819,-0.0095383,-0.01540957,-0.03759241,-0.01939019,0.03618806,0.02905623,-0.00912189,0.04085758,-0.03971917,-0.00131855,-0.04365995,0.08018669,0.01415196,0.01289935,-0.05374468,0.01237925,-0.03288138,-0.02673228,-0.02374185,0.08353258,-0.00584926,-0.05514,-0.07324088,0.02827058,-0.03741198,-0.01363372,-0.03490164,-0.00685126,-0.17093271,0.01506587,0.00075676,0.02945149,-0.03646296,-0.07011548,-0.03363581,-0.01341358,0.0765393,-0.02564748,0.0412542,0.00868415,0.02379899,0.0048113,-0.04662034,0.03361022,-0.03401043,0.01877534,-0.02386485,0.00069,0.02563234,-0.0860294,0.01404392,0.05222639,0.05596331,0.02769746,0.01493172,0.03750068,0.0967583,0.09136447,0.04523863,0.02315646,0.04676273,-0.18478473,0.03267004,-0.00540716,0.00808817,-0.03927793,0.00516445,0.03380453,0.04391219,0.02175068,0.10681549,-0.02019738,0.05576079,0.0473214,-0.0094597,-0.05134465,-0.08815063,-0.06524077,0.0166229,-0.03504411,-0.00608225,-0.0035175,-0.02436652,-0.00283179,0.0281601,0.03831399,-0.02067075,0.05994703,-0.02406656,0.01248346,0.01903997,0.03581852,0.06406329,-0.0010317,0.00716071,0.06603621,0.01751863,0.01045236,0.11470429,-0.02324149,0.04111735,0.00143521,-0.04580223,0.04599816,-0.05669611,-0.03131188,-0.01285831,-0.03880778,0.04856593,0.03450194,0.01465053,0.05833331,-0.04443091,-0.03524584,-0.00323616,0.01869214,-0.00174207,0.02032278,-0.02520258,-0.04461807,-0.00818088,0.01506516,-0.04335798,0.02234046,0.02309299,0.05224698,0.08542771,0.05192797,0.01345477,0.03021688,0.02410423,-0.16245827,-0.02592248,-0.01851789,-0.02790875,-0.00093913,-0.00779299,-0.03668378,0.01936652,-0.0567393,-0.03795725,0.02193714,-0.11319131,-0.06269018,0.08014869,0.01101894,0.00315235,0.02027082,-0.0386834,0.00600016,0.01464684,-0.02530754,-0.0360162,-0.01801695,0.01110924,0.09658924,0.12492778,-0.03675302,-0.05214075,-0.0173162,-0.06119674,-0.06529343,0.10333344,0.00820121,-0.09258065,-0.01480337,0.03406397,-0.03222958,-0.06629842,-0.03381024,-0.00765904,-0.05695679,0.04212113,0.06240351,-0.00778236,-0.01637688,-0.05771057,-0.06253956,-0.02233035,0.02115561,-0.00617248,-0.05535543,0.02097374,-0.05276477,-0.09526785,0.019691,-0.01143663,0.0282521,0.0399616,-0.03602885,-0.03772688,-0.04661495,0.04929734,-0.02189271,-0.04572731,0.0043966,-0.04025486,0.0768011,-0.06204078,0.02054103,-0.0277043,-0.01056895,0.00381771,-0.06804861,0.01317279,-0.0260307,-0.02509898,0.03596154,0.02391434,-0.00673684,0.02357912,0.02630734,0.07195196,-0.00180186,0.02310852,-0.01334836,0.02820707,0.02894195,-0.0057377,-0.00944552,-0.00014874,-0.08466194,-0.24095148,-0.03369704,-0.02341401,0.0162637,-0.0032168,-0.0140798,-0.02353484,-0.03655849,0.10522771,0.12247472,0.08383866,-0.02306668,-0.00690142,0.00777207,0.01038297,0.00075123,-0.05763682,-0.0083868,-0.04664974,0.01084276,0.02468554,0.04174549,-0.02113637,-0.04256997,0.0286874,-0.04455182,0.10564996,0.01047672,0.00476596,-0.00856529,0.08603402,-0.02331031,-0.01869541,-0.05363609,0.02289278,0.06207671,-0.05921039,-0.01961327,-0.06871612,-0.02715074,-0.04606446,-0.01399462,-0.0020926,-0.1069403,-0.04113212,0.03696277,-0.00758364,0.00277312,-0.04982504,0.03390787,0.03018306,-0.01480339,0.07508311,0.03951518,0.05757732,-0.05889517,-0.05716927,-0.02040997,-0.00739513,0.0413546,-0.03356807,-0.03699731,0.03236362,-0.0146799,0.00754811,0.01495246,0.00127872,-0.01263122,-0.01352135,-0.00509711,-0.02049966,0.12209602,0.00912722,0.01672733,-0.0019993,0.01563935,0.04716985,-0.07799694,0.01374254,-0.01687724,0.04285111,-0.01563485,0.06278408,0.08967109,0.05270073,0.00118645,0.03992287,0.02693781,0.0220062,-0.0114271,-0.00467,-0.01094648,-0.06379832,0.00816482,0.02131107,0.0464717,-0.27765009,0.03589061,0.01007418,0.05343854,0.02387493,-0.012235,0.012309,-0.03992046,0.0220733,-0.00964244,0.02598028,0.06990874,0.02655144,-0.06409575,-0.00037609,-0.0147973,-0.00158122,-0.02014227,0.07352845,0.02517219,0.02713097,0.04370213,0.26528069,0.0250843,0.00349999,0.00574937,0.01468145,0.00739763,-0.00767433,0.02403979,0.03986935,-0.00588456,0.10110506,0.01259608,-0.02712744,0.00261844,-0.00487914,0.02736957,-0.02246526,0.00093163,-0.02374222,0.02911687,-0.05781496,0.00178574,0.11477518,0.05552467,-0.02986236,-0.10290615,0.00722436,0.0848141,-0.05483325,-0.05058159,-0.04472692,0.00163766,0.02101202,0.04462279,0.01736532,0.03385477,-0.01016152,-0.01288271,0.0022393,0.03811754,0.07098984,0.04337307,0.04549143],"last_embed":{"hash":"1ud4agg","tokens":204}}},"last_read":{"hash":"1ud4agg","at":1753423677169},"class_name":"SmartSource","last_import":{"mtime":1753366197395,"size":6302,"at":1753423416500,"hash":"1ud4agg"},"blocks":{"#---frontmatter---":[1,6],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis":[8,48],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{1}":[10,15],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{2}":[16,17],"#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{3}":[18,48]},"outlinks":[{"title":"Software to calculate the combination lexicographical order of drawings in any lottery results file.","target":"https://saliu.com/ScreenImgs/DrawIndex.gif","line":41}],"metadata":{"created":"2025-07-24T22:09:56 (UTC +08:00)","tags":["lotto","lottery","software","odds","formula","probability","strategy","software","system","statistics","drawings","analysis","numbers","winning","combinations","randomness","lotto jackpot","random play"],"source":"https://saliu.com/bbs/messages/919.html","author":null}},"smart_blocks:notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12231829,-0.03919426,-0.01895275,-0.03359893,-0.01360411,0.03976846,0.03108476,-0.00151082,0.05080157,-0.02494751,-0.00801667,-0.04785508,0.06888241,0.01949026,0.00750027,-0.03381241,0.00616392,-0.04978118,-0.04032135,-0.01498704,0.09950248,-0.02060779,-0.05158696,-0.09154988,0.04506292,-0.03226891,-0.01360281,-0.04583734,-0.01157137,-0.16001338,0.00463127,0.01767752,0.02159866,-0.0387402,-0.05336457,-0.03706513,-0.02580541,0.07658271,-0.01469542,0.0439427,0.00276215,0.02204243,-0.00820431,-0.03356785,0.02901596,-0.02923669,0.00515465,-0.00837672,0.00561299,0.02017194,-0.08147652,0.02303581,0.03351677,0.04863184,0.04970382,0.00453411,0.04911906,0.08016808,0.08504939,0.04420996,0.02512931,0.02713678,-0.18205537,0.03247258,-0.02016008,0.0020141,-0.03614428,0.00015305,0.02609557,0.0505386,0.03175488,0.09233379,-0.02483432,0.06243024,0.0400484,-0.00683053,-0.05032973,-0.08264402,-0.06312151,0.00869241,-0.03087902,0.00373703,-0.02704441,-0.02074765,-0.00282848,0.03255721,0.03797579,0.01105125,0.05177212,-0.02323696,0.01798403,0.03302342,0.04521415,0.0644446,-0.02298384,0.01371709,0.05668394,0.00251867,0.00239206,0.11259861,-0.03717731,0.03375939,-0.00350233,-0.02328683,0.03835691,-0.04053915,-0.03357263,-0.03185923,-0.04462476,0.03747978,0.02478221,0.01844776,0.06758346,-0.07559647,-0.0313922,-0.00257934,0.02841793,-0.0058093,0.03115341,-0.0202175,-0.04929419,-0.00362424,0.01732806,-0.03486218,0.02221867,0.02898279,0.06157668,0.08256654,0.05255525,0.02214785,0.02690217,0.01783863,-0.1515018,-0.01903182,-0.02083801,-0.03117515,0.00639746,-0.02677829,-0.02007145,0.00620801,-0.03471765,-0.04766224,0.03656592,-0.11985003,-0.06069444,0.07735696,0.0244367,-0.00301693,0.03185626,-0.03244632,-0.0005727,0.0119206,-0.0043584,-0.03684502,-0.01218832,0.0007555,0.10187613,0.12627786,-0.03130194,-0.04225197,-0.00069174,-0.06625941,-0.06673194,0.11616761,-0.0007531,-0.1060106,-0.01668009,0.03329309,-0.02365668,-0.08654231,-0.03087043,-0.01007659,-0.06574192,0.03859942,0.07906094,0.00244505,0.00354685,-0.06420609,-0.06356033,-0.00827899,0.00310247,-0.01641333,-0.05677217,0.02128278,-0.05886179,-0.09992406,0.01905531,-0.015894,0.03275207,0.04782536,-0.03190574,-0.04002376,-0.03855591,0.05407174,-0.02666141,-0.045666,-0.00983564,-0.05206024,0.06772001,-0.0550683,0.01840559,-0.03496658,-0.02492633,0.00245607,-0.04656038,0.01002547,-0.01120941,-0.03224117,0.02238178,0.02336294,0.00507083,0.02457243,0.02372707,0.06587221,-0.01090744,0.01600546,-0.0164296,0.02140615,0.01850318,-0.01191989,-0.01566335,-0.00978322,-0.08887976,-0.23915569,-0.03229878,-0.02385239,-0.00137507,-0.00419374,-0.00470636,-0.01705993,-0.02877607,0.10210003,0.13808811,0.09211624,-0.02497708,-0.00461679,0.00109684,0.01048148,-0.01120569,-0.06218183,-0.03242652,-0.02757242,0.02666715,0.03054014,0.02817227,-0.02254312,-0.04681455,0.02429008,-0.03844733,0.11471424,0.03605767,0.00218139,-0.00001949,0.08645582,-0.00076086,-0.01374875,-0.05532945,0.02949836,0.04741983,-0.06860683,0.00507265,-0.07344084,-0.02889825,-0.04754335,0.01645906,-0.00544223,-0.09622063,-0.02585681,0.01279721,-0.011475,-0.00391243,-0.04874393,0.03609018,0.0312239,-0.01367697,0.06134381,0.04686908,0.071434,-0.04292006,-0.05292884,-0.01369262,-0.00263774,0.03364144,-0.02983977,-0.0337715,0.04592216,-0.02113959,-0.01327315,0.02244084,0.0010021,-0.02288461,-0.01763433,0.00207963,-0.03064105,0.11019278,0.02237369,0.00457116,0.00360675,0.01094237,0.04627299,-0.06394104,0.02412156,-0.0105037,0.03827742,-0.02401957,0.05978323,0.08366041,0.05755125,0.01037723,0.03369728,0.02305754,0.03889988,-0.00516664,-0.00610318,0.00235452,-0.05541207,-0.00671855,0.02917575,0.02395383,-0.28532451,0.03924709,0.01461408,0.06439738,0.02251305,-0.00659028,0.00921714,-0.03732241,-0.0090279,-0.00818649,0.03805082,0.07824077,0.00753737,-0.06035549,-0.02459363,-0.02310475,-0.00190962,-0.02765984,0.07504681,0.0389463,0.03043853,0.04856396,0.26661617,0.04271188,0.00333688,-0.00341362,0.01424361,0.01520368,0.00060443,0.02424393,0.02191217,-0.00425034,0.09393249,0.01182516,-0.02706941,0.02237388,-0.00169514,0.03552088,-0.01843154,0.0051583,-0.05624488,0.03097167,-0.05237681,0.0129877,0.11152701,0.05900294,-0.03698461,-0.09925801,0.00777383,0.07247472,-0.04983552,-0.05785367,-0.04172735,-0.00834273,0.00497056,0.05094871,0.03611347,0.01513008,0.00025874,-0.00853104,0.00914811,0.02917491,0.06989923,0.03947735,0.03804927],"last_embed":{"hash":"1u4gxvt","tokens":130}}},"text":null,"length":0,"last_read":{"hash":"1u4gxvt","at":1753423676958},"key":"notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#---frontmatter---","lines":[1,6],"size":283,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10629468,0.0086368,0.01059171,-0.03164526,-0.02674004,0.04015022,0.02914692,-0.00966619,0.02908976,-0.03517682,0.01246076,-0.05199587,0.08226792,0.03209018,0.01882589,-0.03490299,0.00928741,-0.02099144,-0.04419896,-0.02126237,0.06552504,-0.00451147,-0.04408257,-0.05910293,0.01533405,-0.02891163,-0.01841268,-0.0341123,-0.00350515,-0.17893203,0.0363862,-0.01245752,0.02401903,-0.02583382,-0.05024773,-0.03527958,-0.02196039,0.05912941,-0.05565275,0.02766783,0.01685777,0.02447404,0.03695783,-0.05960092,0.0323068,-0.06443466,0.0207839,-0.02433646,0.01856932,0.0121577,-0.07095458,-0.00435443,0.07417622,0.06082584,0.0028413,0.01822953,0.01405927,0.08260696,0.10462613,0.04231695,0.00787389,0.04588095,-0.19115858,0.01047083,0.00126566,-0.00033548,-0.04449099,0.01478165,0.03773887,0.03730737,0.0151995,0.09131724,-0.02231331,0.04727941,0.06215956,-0.02321119,-0.05229793,-0.09410802,-0.0786002,0.01979388,-0.02393985,-0.00830115,0.01619173,-0.03021282,-0.01106104,0.0258063,0.03200028,-0.06566165,0.04851859,-0.01117086,0.01811427,0.00951322,0.00054631,0.0728058,0.0126573,-0.01839951,0.05469228,0.02214695,0.00989952,0.11341662,-0.01153394,0.015905,-0.01459716,-0.04468807,0.0455642,-0.07103141,-0.03903307,-0.01708022,-0.06385712,0.0419635,0.04281177,0.00357595,0.05628523,-0.01108331,-0.05438824,-0.00342017,-0.0122726,0.00177605,0.00781136,-0.026174,-0.02157393,0.01558554,-0.00468142,-0.04783276,0.00881994,0.01039317,0.04929636,0.08797807,0.04476919,0.01746408,0.0597032,0.0188209,-0.14226244,-0.03908984,-0.00701248,-0.01481962,-0.01560364,0.03693794,-0.02692811,0.01926067,-0.04626493,-0.03985171,0.01301294,-0.10593395,-0.03936226,0.0761795,-0.02184884,0.00183589,0.01470865,-0.03705835,0.00939443,0.00367657,-0.01865206,-0.03919645,0.00603144,0.01499534,0.09686492,0.13132511,-0.04079865,-0.05534301,-0.02493997,-0.046051,-0.05156437,0.09780741,0.02389976,-0.06722069,-0.01755466,0.02167276,-0.03101354,-0.05541169,-0.02374363,-0.02169506,-0.05056662,0.05041058,0.07557831,-0.00391752,-0.03380868,-0.05064943,-0.03231954,-0.0155912,0.02306063,0.00155102,-0.03501118,0.03391581,-0.04846807,-0.09812918,0.02247557,-0.00579203,0.0194486,0.01459762,-0.05678964,-0.00851714,-0.05779736,0.04047188,-0.02836437,-0.03735713,0.00568344,-0.02859501,0.07402928,-0.06144276,0.05096264,-0.00003025,0.00188615,0.00306412,-0.05648053,0.01007671,-0.03657858,-0.02840006,0.06135399,0.03097055,-0.00678597,0.0321288,-0.00600719,0.07962575,-0.0018049,0.03977156,-0.01644613,0.03701389,0.03201431,0.00826418,0.02432905,0.00338712,-0.08847555,-0.2637288,-0.0331731,-0.01645387,0.00317586,0.0019492,0.00372675,-0.03591273,-0.03065038,0.1072465,0.09738379,0.08153974,-0.01874259,-0.01287483,0.02920501,0.01338906,0.00904965,-0.02758825,0.01358874,-0.04829507,-0.00836883,0.00553031,0.06639199,-0.01558836,-0.03102171,0.04977426,-0.02706292,0.1071474,-0.0335599,-0.00887229,-0.02262313,0.07499764,-0.03380029,-0.02200159,-0.04993375,0.01914566,0.0737843,-0.05545076,-0.0368951,-0.07267611,-0.02319321,-0.03133736,-0.00861226,-0.00210525,-0.10081366,-0.05474767,0.04077604,0.00159904,0.01304237,-0.04933013,0.05084411,0.03268731,-0.01408724,0.07830925,0.03328426,0.04444093,-0.07684109,-0.04297361,-0.0291935,-0.00027309,0.05172338,-0.01170236,-0.02308955,0.02215474,0.00354417,0.01468607,0.00207769,0.0087835,0.00009058,-0.02206119,0.00281443,-0.04063737,0.13911144,0.00457725,-0.00331449,0.0274847,0.01010124,0.02550971,-0.07014227,0.01515602,-0.02595596,0.05403603,0.00619895,0.0397829,0.08714475,0.04874593,-0.01891101,0.03981611,0.03768599,-0.01612984,-0.01338961,-0.00737528,-0.01342763,-0.05903129,0.0261824,0.01879882,0.05782582,-0.29313293,0.01993521,0.00925698,0.03257989,0.02756892,-0.02178495,0.03280923,-0.01030365,0.04323589,-0.01910274,0.00857281,0.06515149,0.0404065,-0.09515925,0.00667562,-0.03756028,0.0007693,-0.01902227,0.0725365,0.01739016,0.01310837,0.04147785,0.26605245,0.02354798,0.02887672,-0.00345458,0.02253083,-0.00735673,-0.00789835,-0.00172611,0.01788563,-0.00909009,0.07637334,0.00036424,-0.01764193,-0.01348192,-0.01147324,0.02664571,-0.00888514,-0.01118697,-0.00706347,0.04581018,-0.07052942,-0.00577392,0.12184392,0.06289276,-0.02202487,-0.10060518,0.00599505,0.09188506,-0.03375048,-0.03332505,-0.02672528,0.01191651,0.01582028,0.00585394,-0.01327749,0.05630567,-0.0081984,0.00894128,0.01393717,0.03259732,0.0674798,0.03712758,0.03390176],"last_embed":{"hash":"1aak6xe","tokens":413}}},"text":null,"length":0,"last_read":{"hash":"1aak6xe","at":1753423677000},"key":"notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis","lines":[8,48],"size":3569,"outlinks":[{"title":"Software to calculate the combination lexicographical order of drawings in any lottery results file.","target":"https://saliu.com/ScreenImgs/DrawIndex.gif","line":34}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10022496,0.00447699,0.00101615,-0.03876396,-0.04209843,0.04073381,0.03464902,-0.01738769,0.01689115,-0.03898044,0.00537313,-0.03864235,0.09497312,0.0155983,0.00746808,-0.052634,0.00984424,-0.00292271,-0.03649406,-0.02076905,0.08940669,0.00453993,-0.05708827,-0.05348408,0.01511371,-0.02593536,-0.03145885,-0.03336817,0.00056795,-0.17122971,0.03265141,-0.02194757,0.02377604,-0.0358783,-0.07490876,-0.04058165,-0.02005235,0.07480381,-0.05221637,0.03727306,0.01564621,0.02139803,0.0109186,-0.06007371,0.02539183,-0.05772271,0.03531979,-0.02278492,0.00866743,0.01221034,-0.07400485,0.00365043,0.07826814,0.06287551,0.00789244,0.02497539,0.0156039,0.08800289,0.09755094,0.04798923,0.01665097,0.05395499,-0.19235878,0.01972019,-0.00411363,0.00454117,-0.0332698,0.00895115,0.03793382,0.03765873,0.02670666,0.10650947,-0.01407611,0.05630481,0.05819681,-0.0079436,-0.05942753,-0.09883405,-0.06236562,0.01832125,-0.03419967,-0.01768065,0.02881937,-0.02836685,-0.00013576,0.02434728,0.03507142,-0.0546048,0.06715928,-0.01267543,0.02066746,0.01510472,0.01106442,0.06835464,0.01648821,0.00233217,0.06667612,0.01232116,0.03258372,0.12523916,-0.01322067,0.023561,0.0105517,-0.04788629,0.03692719,-0.07912029,-0.03232812,-0.01673832,-0.04703737,0.05279621,0.04547486,0.01441192,0.06202236,-0.01208288,-0.04446927,-0.01272599,-0.01501396,-0.00367433,0.01291197,-0.02808283,-0.01749564,0.00546508,0.00470335,-0.04249676,0.02833394,0.01709126,0.05049646,0.08774368,0.03277714,0.02157168,0.04217754,0.02190219,-0.15630522,-0.02350548,-0.00120473,-0.00899661,-0.00834599,0.01808047,-0.03772459,0.03934588,-0.05186344,-0.02895196,0.0079235,-0.11541974,-0.05050838,0.08435826,-0.01293196,0.01936995,0.01273806,-0.04544961,0.00331404,0.00361688,-0.03824653,-0.04307073,-0.01674945,0.02873823,0.07985803,0.11714717,-0.03986086,-0.05366031,-0.04496357,-0.0516596,-0.05738394,0.09852144,0.01210231,-0.07770592,-0.00936696,0.01857027,-0.04900577,-0.03739605,-0.02211273,-0.0016804,-0.05526007,0.0565195,0.07055047,-0.0089043,-0.02360996,-0.04727638,-0.03615524,-0.03477661,0.0359156,-0.00494634,-0.04744029,0.02875779,-0.04006801,-0.08316725,0.02386066,-0.00323858,0.02535185,0.0407105,-0.05081349,-0.0193426,-0.06238228,0.03104498,-0.01377971,-0.0350948,-0.00347903,-0.01792269,0.07157625,-0.06785638,0.04112834,-0.01156121,0.0158279,0.01336239,-0.06755496,0.0204838,-0.03500659,-0.02892878,0.04972387,0.02315415,-0.01938821,0.01978203,0.01631118,0.07425353,-0.00776711,0.01906484,-0.01725243,0.03572438,0.03098704,0.00016454,0.01601715,-0.00173929,-0.07502396,-0.24780856,-0.0273704,-0.01839025,0.0222043,-0.00108115,-0.00292098,-0.03071846,-0.05386353,0.09132633,0.09927912,0.08699565,-0.02191336,-0.01633903,0.02411851,0.00313175,0.01616006,-0.0516052,-0.00804693,-0.05621911,-0.01719256,-0.00119205,0.05080161,-0.02089528,-0.0458583,0.04561511,-0.03464444,0.10555166,-0.02788277,0.00280671,-0.02598768,0.07050117,-0.03066939,-0.02968712,-0.03578895,0.00486265,0.07912613,-0.04516308,-0.0299709,-0.06621302,-0.02644305,-0.04048519,-0.0273104,0.00049302,-0.09407615,-0.03739959,0.04368158,0.00297282,0.02124771,-0.03680785,0.04230344,0.02958433,-0.01763187,0.08526496,0.04383719,0.04648671,-0.06878494,-0.05079092,-0.00866982,-0.00449642,0.05709589,-0.02754303,-0.0419003,0.01282575,0.00079929,0.02620225,0.01070661,-0.00424633,-0.00369505,-0.00851919,0.00040696,-0.01814958,0.11605801,0.0044104,0.00808229,-0.00348725,0.02849332,0.03637881,-0.07210255,0.00770978,-0.0265461,0.0471456,0.0009231,0.05439952,0.09192989,0.04596667,-0.01447709,0.03884561,0.03164941,-0.00834876,-0.01881569,-0.00834948,-0.0074859,-0.06781239,0.02582453,0.00929439,0.06223293,-0.28460583,0.02250227,-0.00981695,0.05086719,0.02370005,-0.0147737,0.01777414,-0.01260406,0.04378797,-0.02269495,0.0078122,0.0587328,0.0475533,-0.07850407,0.0146853,-0.01805275,0.00852532,-0.0288317,0.07576136,0.00609373,0.02737355,0.0525646,0.26097932,0.01791639,0.01249052,0.00908942,0.01694429,0.0036318,-0.02226109,0.00420806,0.04634732,-0.00736581,0.10257731,0.00424456,-0.03310825,-0.02629178,-0.00703884,0.0294376,-0.01991511,-0.01275976,-0.01295974,0.03588116,-0.05786444,-0.00196868,0.11473354,0.05122913,-0.03024668,-0.10200736,-0.00182654,0.08222415,-0.0510658,-0.04669399,-0.03817065,0.00583916,0.02533827,0.0189858,-0.01866339,0.04345052,-0.01796798,-0.01239167,-0.00714299,0.03276679,0.07190964,0.04133415,0.0365381],"last_embed":{"hash":"f9jxwh","tokens":118}}},"text":null,"length":0,"last_read":{"hash":"f9jxwh","at":1753423677133},"key":"notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{1}","lines":[10,15],"size":238,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10787725,0.00377065,-0.01263013,-0.04752662,-0.00615727,0.04786403,0.03763609,-0.00931504,0.03013501,-0.04223004,-0.00295329,-0.06085209,0.07895494,0.02529783,0.030484,-0.02463415,0.02876386,-0.02818405,-0.05576261,-0.03333074,0.07077421,-0.00172387,-0.01834759,-0.04902624,0.01985489,-0.03421717,-0.02321703,-0.03455657,0.00402368,-0.15927546,0.02196133,0.00485911,0.02145865,-0.02149509,-0.03262039,-0.02331567,-0.00755862,0.01939461,-0.0626682,0.02900436,0.00346879,0.03211814,0.04128023,-0.06924672,0.0245782,-0.04713348,-0.00946047,-0.03705968,0.01655122,0.00081946,-0.05778212,0.01624889,0.05125123,0.06915268,-0.03123891,0.03134711,0.03488889,0.09207845,0.08795716,0.01943787,-0.01111398,0.01985888,-0.18507485,0.00367462,-0.00627637,-0.00558252,-0.0426232,-0.00830709,0.04707078,0.04385762,-0.00768859,0.08121162,-0.04440076,0.04021535,0.04439748,-0.02569898,-0.04693375,-0.06328999,-0.06391885,0.01950772,-0.01470761,-0.00111786,0.00176378,-0.01446913,0.01895519,0.04315459,0.02028627,-0.05468318,0.03847145,-0.03002841,-0.01125105,-0.00483618,0.01303309,0.07399041,-0.01011975,-0.02888268,0.05635514,0.03528231,-0.02209723,0.12459005,-0.0225656,0.01057545,-0.01740477,-0.05180503,0.03335103,-0.05950466,-0.02594259,-0.01912273,-0.0610406,0.03651828,0.02078915,-0.01594265,0.06988356,-0.02269056,-0.04398839,-0.00040728,-0.01091328,-0.00201346,0.00344438,-0.02913967,-0.01342744,-0.00849571,0.00508047,-0.02551255,0.01912178,0.00524911,0.04926574,0.08234018,0.05627786,0.03461155,0.06770946,0.00911108,-0.12365489,-0.03748753,-0.00568332,-0.02634608,-0.01101359,0.01643431,0.00023031,0.02031905,-0.03609091,-0.05883256,0.02452086,-0.11649562,-0.05848479,0.1114613,-0.02613418,-0.01762106,0.02053366,-0.02184611,0.00119325,0.02293404,0.01616008,-0.01338693,0.00001229,0.01876935,0.10959698,0.13175206,-0.05367223,-0.03309964,0.01614345,-0.05021516,-0.05053972,0.11760303,0.02914542,-0.06694748,-0.00475115,0.01251962,-0.02806914,-0.04328005,-0.03201785,-0.01745319,-0.04289,0.03411275,0.07646465,-0.01433177,0.00640432,-0.04623698,-0.05483394,0.00012005,0.01513604,-0.00346938,-0.04007464,0.04140046,-0.04706473,-0.11324392,0.01801856,-0.00542573,0.03511789,0.00202248,-0.09857847,-0.02013902,-0.01325741,0.06453851,-0.03855937,-0.04675661,0.02607315,-0.03964898,0.06070031,-0.06436317,0.07901759,0.00263378,-0.00800519,-0.00882544,-0.0326677,0.0005473,-0.04665465,-0.02332441,0.02869389,0.02438476,0.01397105,0.05291082,-0.00115477,0.06500605,-0.01001821,0.02654768,-0.04318083,0.03242173,0.02403776,0.0221583,0.02650835,0.00644554,-0.11924659,-0.26579556,-0.01192913,-0.01724334,-0.01848647,-0.00167635,0.00774165,-0.05421712,-0.03177167,0.11158907,0.10029007,0.08671861,0.0141472,-0.02058285,0.03042019,0.00733289,0.02235126,0.00759282,0.02442471,-0.02939379,-0.00675164,0.01013003,0.04912584,0.00873009,-0.00995017,0.05102032,-0.0340554,0.11346392,-0.02160084,0.0011653,0.00057066,0.0530787,-0.03016235,-0.00560949,-0.08281402,0.02046197,0.08261314,-0.0749203,-0.02989051,-0.05717418,-0.03369521,-0.0242577,0.00702482,-0.00966913,-0.09723169,-0.03398114,-0.00273458,-0.00050889,-0.0153698,-0.04005207,0.03107022,0.04238534,-0.04084112,0.04936414,0.04112436,0.04711427,-0.07672562,-0.04078758,-0.03745447,-0.00928312,0.03839331,-0.01815496,-0.01681817,0.02646661,-0.01851518,0.0062428,-0.01054273,0.0167934,0.01316043,-0.02646418,0.02912745,-0.06052068,0.15520993,0.02279358,0.00202903,0.03832664,0.00152879,-0.00120168,-0.0673161,0.0083875,-0.0363223,0.05086739,0.02145727,0.04477466,0.0742377,0.05957508,-0.00463163,0.04671464,0.01612026,-0.01964492,-0.02545699,-0.00420785,-0.00149364,-0.0615515,0.01272988,0.02971905,0.03570532,-0.3043353,0.04599206,0.0312111,0.00547459,0.03662616,-0.01038641,0.03123556,-0.02479801,0.00377581,-0.01929149,0.00654314,0.06697513,0.01583445,-0.08318336,-0.01056345,-0.02674687,0.02883047,-0.04701181,0.07622619,0.0092709,0.00046223,0.03416954,0.25844175,0.03346516,0.02724483,-0.01675101,0.01484204,0.00145111,0.01238789,-0.00613068,0.02903968,-0.03036997,0.0703642,0.02967206,-0.00398014,0.02076815,0.00475116,0.03089533,0.01273215,-0.01879383,-0.02300469,0.04676383,-0.06463826,-0.01005487,0.10690019,0.06498886,-0.03159367,-0.07183173,-0.02510705,0.09668527,-0.01621128,-0.01464574,-0.00530913,0.01271078,0.02606042,0.00363313,-0.01999764,0.03875066,0.0010648,-0.00425224,0.02676208,0.05403222,0.05008525,0.0456211,0.07140807],"last_embed":{"hash":"1v6noi8","tokens":432}}},"text":null,"length":0,"last_read":{"hash":"1v6noi8","at":1753423677169},"key":"notes/saliu/動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis.md#動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis#{3}","lines":[18,48],"size":3094,"outlinks":[{"title":"Software to calculate the combination lexicographical order of drawings in any lottery results file.","target":"https://saliu.com/ScreenImgs/DrawIndex.gif","line":24}],"class_name":"SmartBlock"},
