"smart_sources:Obsidian/how to show math in Obsidian notes 202403290938.md": {"path":"Obsidian/how to show math in Obsidian notes 202403290938.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03668294,-0.04170761,0.02417069,-0.01232262,-0.01035485,0.01174049,-0.03134773,0.06519724,0.05850543,-0.00468819,0.01821337,-0.08519331,0.00721239,0.00265368,0.01189148,0.00113172,-0.03730192,0.04345753,-0.03981524,0.0280218,0.14334655,-0.07990611,-0.00264665,-0.00461646,0.07732346,0.04277692,0.02286574,-0.00214886,0.0429328,-0.22604701,0.01324078,-0.00226289,-0.03100978,-0.01892108,-0.0254614,-0.05732002,0.00166591,-0.01119725,-0.03424749,0.06303819,-0.00600143,0.03254137,-0.01009341,-0.03177887,-0.05122057,-0.08060352,-0.10537561,-0.00456371,0.09137184,-0.01855952,0.00004439,-0.05419125,0.01381057,-0.00287946,0.01134988,0.00381349,0.0821268,-0.03957542,0.00935159,0.05614536,0.0843711,0.00154547,-0.17954262,0.0946115,0.02599276,0.0033818,-0.01475289,-0.00345367,0.00872307,0.09106751,0.01669564,0.01232687,0.04075832,-0.05156289,0.0496713,-0.04886485,-0.01128284,0.00030644,-0.00350987,-0.01335801,-0.07467684,0.04861249,-0.06109911,0.02143357,0.01290931,-0.06540009,0.05119051,-0.00449373,0.0248407,0.00032799,-0.03752025,-0.00316856,-0.03858532,0.01862192,-0.00303467,0.03053716,0.03789892,0.03681403,0.00445449,0.10692213,0.04098679,-0.01785859,0.01382751,-0.04413333,0.05480048,-0.02164639,-0.02420441,-0.04330051,-0.02242829,0.05362674,0.01296811,0.0117838,-0.04027893,-0.12161297,0.02401576,-0.00590008,-0.03150951,-0.02735977,0.01079004,-0.02263574,0.01838164,0.0331576,0.05645382,-0.00750349,0.05890589,0.01038196,0.01276129,0.07636204,0.03374854,0.07419593,0.04372458,-0.0495188,-0.01517484,-0.04140076,0.02915947,-0.01235783,-0.01817467,0.06346467,0.02917104,0.08617188,-0.02399783,-0.02554519,0.00219432,-0.01893389,0.00076154,0.07596684,-0.00348888,-0.026503,0.01528101,-0.01450893,-0.02567553,0.02004855,-0.04946816,-0.02719329,-0.004974,-0.00865939,0.04313313,0.07390957,-0.0720865,0.02421469,-0.00089577,-0.05723043,-0.01180373,0.03325204,-0.01871228,-0.0342889,0.00700432,0.06631519,-0.00340981,-0.08749998,-0.02271548,0.02957951,-0.01401877,0.00546151,0.18261544,0.01485143,-0.0410073,-0.02363537,-0.03614723,0.00374,0.00341592,-0.04083737,-0.02276257,0.00671808,-0.04688125,-0.00691391,-0.006281,-0.04392597,-0.03071195,0.02030194,-0.00550276,0.01070024,-0.11610665,0.05305015,0.01007469,0.00463015,0.02384632,0.02505978,0.02741047,0.06284291,0.06973775,-0.07724614,0.00687059,-0.0105188,0.01959588,0.04196662,0.02129051,-0.00536872,0.05017557,0.05132752,0.00794125,-0.00646742,0.04463718,-0.00360677,-0.0822942,0.04701004,0.05109105,0.02546531,-0.04694822,0.03999681,-0.02147653,-0.11317047,-0.08421212,-0.17978539,-0.04553073,0.03685015,-0.04654615,0.06142633,-0.12800397,0.07158875,0.00677805,-0.00208614,0.09345577,0.0758317,-0.00471998,-0.01564082,-0.0522898,-0.03068356,-0.04044714,-0.01938131,-0.01187876,0.0388159,0.02362878,-0.05262651,-0.02927088,-0.00293,-0.07400326,-0.01641781,-0.00546529,0.16895741,0.04167477,-0.00024205,-0.02580958,-0.04505684,-0.02872221,-0.07476404,0.017087,0.00330229,0.05007087,-0.02403035,0.07484838,-0.04114096,-0.08849753,-0.04307334,0.05049739,0.02025137,-0.04402728,-0.04066758,0.03740535,-0.03545162,-0.00901664,0.01260518,0.03076306,0.0141922,0.02688733,0.01075666,0.04655923,0.01032724,-0.04248419,-0.01357931,-0.03621725,-0.03854769,0.05822641,0.00515248,0.01167748,-0.0000774,-0.05120238,0.01290724,0.05702471,0.02324839,-0.04737823,0.05663518,-0.03249088,0.01234968,0.06207115,0.04669995,0.01076649,0.07226591,-0.02211712,0.03656261,0.00498818,0.00994799,0.03332276,0.03041849,-0.08118547,0.05430447,-0.00175695,0.01075363,0.05943562,0.03492102,-0.02997754,0.00668166,-0.02625619,-0.05085456,0.03037787,-0.02252773,0.00664696,-0.03091465,0.05503588,-0.30496627,0.04350128,0.00774457,0.01371445,0.0091693,-0.02136042,0.04988051,-0.02166655,-0.07320362,-0.07899104,-0.02031509,0.02456024,-0.00097153,-0.0178166,0.03372828,0.03151691,0.00208256,-0.01247184,-0.00224656,-0.02005262,0.05575116,0.00187338,0.21592285,0.00335687,-0.00668686,-0.03392076,-0.04792896,0.04908626,0.14539279,0.01380682,-0.05960895,-0.04560375,0.06283754,-0.01789398,0.03499181,0.05712164,-0.04726334,-0.01765204,0.04670706,0.03632548,0.07553395,-0.01818272,0.00688179,0.01197842,0.07076824,-0.02590081,-0.02550275,-0.0032136,0.02582333,-0.02944816,-0.09193367,0.03019376,0.02603344,-0.0321531,-0.01683615,0.06142207,-0.06135926,-0.02475032,-0.01458872,-0.03960884,-0.03666002,-0.0009632,0.01687542,-0.01091795,0.00572142],"last_embed":{"hash":"e394d7b231b7ef85a4ee5facd8f4da6cf4455a5a593ac930bf18b5654f6cafaa","tokens":279}}},"last_read":{"hash":"e394d7b231b7ef85a4ee5facd8f4da6cf4455a5a593ac930bf18b5654f6cafaa","at":1745995207385},"class_name":"SmartSource2","outlinks":[{"title":"https://huggingface.co › TheBloke › dolphin-2.0-mistral-7B-GGUF","target":"https://huggingface.co/TheBloke/dolphin-2.0-mistral-7B-GGUF","line":5}],"blocks":{"#":[2,21]},"last_import":{"mtime":1711692021090,"size":427,"at":1740449883031,"hash":"e394d7b231b7ef85a4ee5facd8f4da6cf4455a5a593ac930bf18b5654f6cafaa"},"key":"Obsidian/how to show math in Obsidian notes 202403290938.md"},