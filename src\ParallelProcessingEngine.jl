# src/ParallelProcessingEngine.jl

module ParallelProcessingEngine

using Dates
using Statistics
using Printf
using Base.Threads
using ..SaliuLottery: Drawing
using ..WonderGridEngine: WonderGridConfig, WonderGridError

export ParallelConfig, TaskDistributor, LoadBalancer, ThreadPoolManager,
       create_parallel_engine, distribute_tasks, balance_load,
       execute_parallel_generation, monitor_thread_performance,
       optimize_thread_allocation, get_parallel_statistics

# 並行配置結構
struct ParallelConfig
    max_threads::Int
    chunk_size::Int
    load_balance_enabled::Bool
    dynamic_allocation::Bool
    performance_monitoring::Bool
    timeout_seconds::Int
    
    function ParallelConfig(;
        max_threads::Int = min(Threads.nthreads(), 8),
        chunk_size::Int = 100,
        load_balance_enabled::Bool = true,
        dynamic_allocation::Bool = true,
        performance_monitoring::Bool = true,
        timeout_seconds::Int = 300
    )
        new(max_threads, chunk_size, load_balance_enabled,
            dynamic_allocation, performance_monitoring, timeout_seconds)
    end
end

# 任務結構
struct ParallelTask
    id::Int
    task_type::Symbol
    data::Dict{String, Any}
    priority::Int
    estimated_time::Float64
    
    function ParallelTask(
        id::Int,
        task_type::Symbol,
        data::Dict{String, Any},
        priority::Int = 1,
        estimated_time::Float64 = 1.0
    )
        new(id, task_type, data, priority, estimated_time)
    end
end

# 任務分配器結構
mutable struct TaskDistributor
    pending_tasks::Vector{ParallelTask}
    active_tasks::Dict{Int, ParallelTask}  # thread_id => task
    completed_tasks::Vector{ParallelTask}
    failed_tasks::Vector{ParallelTask}
    task_counter::Int
    
    function TaskDistributor()
        new(Vector{ParallelTask}(), Dict{Int, ParallelTask}(),
            Vector{ParallelTask}(), Vector{ParallelTask}(), 0)
    end
end

# 負載平衡器結構
mutable struct LoadBalancer
    thread_loads::Dict{Int, Float64}  # thread_id => current_load
    thread_performance::Dict{Int, Vector{Float64}}  # thread_id => performance_history
    rebalance_threshold::Float64
    last_rebalance::DateTime
    rebalance_interval::Int  # 秒
    
    function LoadBalancer(
        rebalance_threshold::Float64 = 0.3,
        rebalance_interval::Int = 30
    )
        thread_loads = Dict{Int, Float64}()
        thread_performance = Dict{Int, Vector{Float64}}()
        
        for i in 1:Threads.nthreads()
            thread_loads[i] = 0.0
            thread_performance[i] = Float64[]
        end
        
        new(thread_loads, thread_performance, rebalance_threshold,
            now(), rebalance_interval)
    end
end

# 線程池管理器結構
mutable struct ThreadPoolManager
    config::ParallelConfig
    distributor::TaskDistributor
    load_balancer::LoadBalancer
    active_threads::Set{Int}
    thread_status::Dict{Int, Symbol}  # :idle, :busy, :error
    performance_stats::Dict{String, Any}
    start_time::DateTime
    
    function ThreadPoolManager(config::ParallelConfig = ParallelConfig())
        distributor = TaskDistributor()
        load_balancer = LoadBalancer()
        active_threads = Set{Int}()
        thread_status = Dict{Int, Symbol}()
        
        for i in 1:config.max_threads
            thread_status[i] = :idle
        end
        
        performance_stats = Dict{String, Any}(
            "total_tasks_processed" => 0,
            "total_execution_time" => 0.0,
            "average_task_time" => 0.0,
            "thread_utilization" => 0.0,
            "error_rate" => 0.0
        )
        
        new(config, distributor, load_balancer, active_threads,
            thread_status, performance_stats, now())
    end
end

"""
創建並行處理引擎
"""
function create_parallel_engine(
    max_threads::Int = min(Threads.nthreads(), 8),
    chunk_size::Int = 100
)::ThreadPoolManager
    
    config = ParallelConfig(
        max_threads = max_threads,
        chunk_size = chunk_size
    )
    
    manager = ThreadPoolManager(config)
    
    println("🚀 並行處理引擎已創建")
    println("  - 最大線程數: $(config.max_threads)")
    println("  - 可用線程數: $(Threads.nthreads())")
    println("  - 塊大小: $(config.chunk_size)")
    println("  - 負載平衡: $(config.load_balance_enabled ? "啟用" : "停用")")
    
    return manager
end

"""
分配任務
"""
function distribute_tasks(
    manager::ThreadPoolManager,
    total_combinations::Int,
    generation_function::Function,
    data_context::Dict{String, Any}
)::Vector{ParallelTask}
    
    println("📋 開始任務分配...")
    
    chunk_size = manager.config.chunk_size
    num_chunks = ceil(Int, total_combinations / chunk_size)
    tasks = Vector{ParallelTask}()
    
    for i in 1:num_chunks
        start_idx = (i - 1) * chunk_size + 1
        end_idx = min(i * chunk_size, total_combinations)
        chunk_combinations = end_idx - start_idx + 1
        
        task_data = Dict{String, Any}(
            "start_idx" => start_idx,
            "end_idx" => end_idx,
            "combinations_count" => chunk_combinations,
            "generation_function" => generation_function,
            "data_context" => data_context
        )
        
        estimated_time = estimate_task_time(chunk_combinations)
        priority = calculate_task_priority(i, num_chunks)
        
        manager.distributor.task_counter += 1
        task = ParallelTask(
            manager.distributor.task_counter,
            :combination_generation,
            task_data,
            priority,
            estimated_time
        )
        
        push!(tasks, task)
        push!(manager.distributor.pending_tasks, task)
    end
    
    println("  - 總任務數: $(length(tasks))")
    println("  - 平均塊大小: $(round(total_combinations / num_chunks, digits=1))")
    
    return tasks
end

"""
執行並行生成
"""
function execute_parallel_generation(
    manager::ThreadPoolManager,
    tasks::Vector{ParallelTask}
)::Vector{Vector{Int}}
    
    println("⚡ 開始並行執行...")
    
    start_time = time()
    all_combinations = Vector{Vector{Int}}()
    results_lock = Threads.SpinLock()
    
    # 並行執行任務
    Threads.@threads for task in tasks
        thread_id = Threads.threadid()
        
        try
            # 更新線程狀態
            manager.thread_status[thread_id] = :busy
            push!(manager.active_threads, thread_id)
            
            # 執行任務
            task_start = time()
            combinations = execute_single_task(task)
            task_time = time() - task_start
            
            # 更新負載信息
            update_thread_load(manager.load_balancer, thread_id, task_time)
            
            # 安全地合併結果
            lock(results_lock) do
                append!(all_combinations, combinations)
                push!(manager.distributor.completed_tasks, task)
            end
            
            # 更新統計
            update_performance_stats(manager, task_time, true)
            
        catch e
            println("❌ 線程 $thread_id 任務執行失敗: $e")
            
            lock(results_lock) do
                push!(manager.distributor.failed_tasks, task)
            end
            
            update_performance_stats(manager, 0.0, false)
            manager.thread_status[thread_id] = :error
            
        finally
            # 清理線程狀態
            manager.thread_status[thread_id] = :idle
            delete!(manager.active_threads, thread_id)
        end
    end
    
    execution_time = time() - start_time
    
    println("✅ 並行執行完成")
    println("  - 總執行時間: $(round(execution_time, digits=2)) 秒")
    println("  - 生成組合數: $(length(all_combinations))")
    println("  - 完成任務數: $(length(manager.distributor.completed_tasks))")
    println("  - 失敗任務數: $(length(manager.distributor.failed_tasks))")
    
    return unique(all_combinations)
end

"""
平衡負載
"""
function balance_load(manager::ThreadPoolManager)::Nothing
    
    if !manager.config.load_balance_enabled
        return
    end
    
    current_time = now()
    time_since_last = current_time - manager.load_balancer.last_rebalance
    
    if time_since_last < Second(manager.load_balancer.rebalance_interval)
        return
    end
    
    println("⚖️  執行負載平衡...")
    
    load_balancer = manager.load_balancer
    
    # 計算負載差異
    loads = collect(values(load_balancer.thread_loads))
    if isempty(loads)
        return
    end
    
    max_load = maximum(loads)
    min_load = minimum(loads)
    load_difference = max_load - min_load
    
    if load_difference > load_balancer.rebalance_threshold
        println("  - 檢測到負載不平衡: $(round(load_difference, digits=3))")
        
        # 重新分配待處理任務
        rebalance_pending_tasks(manager)
        
        load_balancer.last_rebalance = current_time
        println("  - 負載平衡完成")
    end
    
    return nothing
end

"""
監控線程效能
"""
function monitor_thread_performance(manager::ThreadPoolManager)::Dict{String, Any}
    
    performance_report = Dict{String, Any}()
    
    # 線程狀態統計
    status_counts = Dict{Symbol, Int}()
    for status in values(manager.thread_status)
        status_counts[status] = get(status_counts, status, 0) + 1
    end
    
    performance_report["thread_status"] = status_counts
    performance_report["active_threads"] = length(manager.active_threads)
    performance_report["total_threads"] = manager.config.max_threads
    
    # 負載統計
    loads = collect(values(manager.load_balancer.thread_loads))
    if !isempty(loads)
        performance_report["load_stats"] = Dict(
            "average_load" => mean(loads),
            "max_load" => maximum(loads),
            "min_load" => minimum(loads),
            "load_variance" => var(loads)
        )
    end
    
    # 任務統計
    distributor = manager.distributor
    performance_report["task_stats"] = Dict(
        "pending_tasks" => length(distributor.pending_tasks),
        "active_tasks" => length(distributor.active_tasks),
        "completed_tasks" => length(distributor.completed_tasks),
        "failed_tasks" => length(distributor.failed_tasks)
    )
    
    # 整體效能統計
    performance_report["overall_stats"] = manager.performance_stats
    
    return performance_report
end

"""
優化線程分配
"""
function optimize_thread_allocation(manager::ThreadPoolManager)::Nothing
    
    if !manager.config.dynamic_allocation
        return
    end
    
    println("🔧 優化線程分配...")
    
    # 分析線程效能歷史
    load_balancer = manager.load_balancer
    thread_efficiencies = Dict{Int, Float64}()
    
    for (thread_id, performance_history) in load_balancer.thread_performance
        if !isempty(performance_history)
            # 計算效率（任務完成速度的倒數）
            avg_time = mean(performance_history)
            efficiency = avg_time > 0 ? 1.0 / avg_time : 0.0
            thread_efficiencies[thread_id] = efficiency
        end
    end
    
    if !isempty(thread_efficiencies)
        # 根據效率調整任務分配權重
        total_efficiency = sum(values(thread_efficiencies))
        
        for (thread_id, efficiency) in thread_efficiencies
            weight = efficiency / total_efficiency
            # 這裡可以實現更複雜的權重調整邏輯
            println("  - 線程 $thread_id 效率權重: $(round(weight, digits=3))")
        end
    end
    
    println("  - 線程分配優化完成")
    
    return nothing
end

"""
獲取並行統計
"""
function get_parallel_statistics(manager::ThreadPoolManager)::Dict{String, Any}
    
    runtime = now() - manager.start_time
    
    stats = Dict{String, Any}(
        "runtime_seconds" => runtime.value / 1000,
        "configuration" => Dict(
            "max_threads" => manager.config.max_threads,
            "chunk_size" => manager.config.chunk_size,
            "load_balance_enabled" => manager.config.load_balance_enabled,
            "dynamic_allocation" => manager.config.dynamic_allocation
        ),
        "performance" => manager.performance_stats,
        "thread_utilization" => calculate_thread_utilization(manager),
        "load_balance_efficiency" => calculate_load_balance_efficiency(manager)
    )
    
    return stats
end

# 輔助函數
function execute_single_task(task::ParallelTask)::Vector{Vector{Int}}
    
    data = task.data
    start_idx = data["start_idx"]
    end_idx = data["end_idx"]
    combinations_count = data["combinations_count"]
    generation_function = data["generation_function"]
    data_context = data["data_context"]
    
    # 執行生成函數
    combinations = generation_function(data_context, combinations_count)
    
    return combinations
end

function estimate_task_time(combinations_count::Int)::Float64
    # 基於組合數量估算任務時間（秒）
    base_time = 0.001  # 每個組合的基礎時間
    return combinations_count * base_time
end

function calculate_task_priority(task_index::Int, total_tasks::Int)::Int
    # 簡單的優先級計算：前面的任務優先級較高
    return total_tasks - task_index + 1
end

function update_thread_load(load_balancer::LoadBalancer, thread_id::Int, task_time::Float64)::Nothing
    
    # 更新當前負載（使用指數移動平均）
    alpha = 0.3
    current_load = get(load_balancer.thread_loads, thread_id, 0.0)
    new_load = alpha * task_time + (1 - alpha) * current_load
    load_balancer.thread_loads[thread_id] = new_load
    
    # 記錄效能歷史
    if !haskey(load_balancer.thread_performance, thread_id)
        load_balancer.thread_performance[thread_id] = Float64[]
    end
    
    push!(load_balancer.thread_performance[thread_id], task_time)
    
    # 限制歷史長度
    max_history = 100
    if length(load_balancer.thread_performance[thread_id]) > max_history
        load_balancer.thread_performance[thread_id] = 
            load_balancer.thread_performance[thread_id][end-max_history+1:end]
    end
    
    return nothing
end

function update_performance_stats(manager::ThreadPoolManager, task_time::Float64, success::Bool)::Nothing
    
    stats = manager.performance_stats
    
    if success
        stats["total_tasks_processed"] += 1
        stats["total_execution_time"] += task_time
        
        if stats["total_tasks_processed"] > 0
            stats["average_task_time"] = stats["total_execution_time"] / stats["total_tasks_processed"]
        end
    else
        # 記錄錯誤
        total_attempts = stats["total_tasks_processed"] + get(stats, "total_errors", 0)
        stats["total_errors"] = get(stats, "total_errors", 0) + 1
        stats["error_rate"] = stats["total_errors"] / max(1, total_attempts + 1)
    end
    
    return nothing
end

function rebalance_pending_tasks(manager::ThreadPoolManager)::Nothing
    
    distributor = manager.distributor
    load_balancer = manager.load_balancer
    
    if isempty(distributor.pending_tasks)
        return
    end
    
    # 按線程負載重新排序任務
    sorted_threads = sort(collect(keys(load_balancer.thread_loads)), 
                         by = x -> load_balancer.thread_loads[x])
    
    # 重新分配任務優先級
    for (i, task) in enumerate(distributor.pending_tasks)
        # 根據線程負載調整任務優先級
        thread_idx = (i - 1) % length(sorted_threads) + 1
        target_thread = sorted_threads[thread_idx]
        
        # 這裡可以實現更複雜的重新分配邏輯
        # 目前只是調整優先級
        task.priority = length(distributor.pending_tasks) - i + 1
    end
    
    # 按新優先級排序
    sort!(distributor.pending_tasks, by = x -> x.priority, rev = true)
    
    return nothing
end

function calculate_thread_utilization(manager::ThreadPoolManager)::Float64
    
    active_count = length(manager.active_threads)
    total_count = manager.config.max_threads
    
    return total_count > 0 ? active_count / total_count : 0.0
end

function calculate_load_balance_efficiency(manager::ThreadPoolManager)::Float64
    
    loads = collect(values(manager.load_balancer.thread_loads))
    
    if length(loads) < 2
        return 1.0
    end
    
    # 計算負載分佈的均勻性
    mean_load = mean(loads)
    if mean_load == 0
        return 1.0
    end
    
    variance = var(loads)
    coefficient_of_variation = sqrt(variance) / mean_load
    
    # 變異係數越小，負載平衡效率越高
    efficiency = 1.0 / (1.0 + coefficient_of_variation)
    
    return clamp(efficiency, 0.0, 1.0)
end

end # module ParallelProcessingEngine
