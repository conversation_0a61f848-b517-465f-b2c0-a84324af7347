
"smart_sources:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md": {"path":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11576357,0.00808918,-0.07743806,0.01740197,-0.00249219,0.03208523,0.03727107,-0.00495592,0.05154332,-0.02528528,0.01357107,0.01419483,-0.01252156,-0.01977057,-0.02900417,-0.05848279,0.03995019,-0.00798751,-0.00297142,0.03793338,0.01260644,-0.04818417,-0.07891517,-0.04751008,0.04553292,-0.02043748,-0.05792801,-0.04080579,-0.07899432,-0.21863735,-0.00281047,-0.05974359,0.061451,-0.03450994,-0.06409783,-0.03603195,-0.04776438,0.0368435,-0.04329405,0.00019726,0.02004576,0.05227841,0.00802753,0.03762503,0.00562709,-0.03248257,0.03398353,0.03951713,0.04591499,-0.00803437,-0.04592362,0.00354636,-0.05306069,-0.008247,0.05582586,-0.01329992,0.05403252,0.08245776,-0.03424313,0.03743318,0.02601646,0.0256728,-0.17848751,0.05536802,-0.01397651,0.04126883,-0.01516567,-0.011097,0.01582985,0.03996561,-0.00475422,0.03277731,-0.04334316,0.06719229,0.050173,-0.03221412,0.02419556,-0.0438244,-0.00390857,-0.00579608,0.02917739,-0.05733966,0.03401054,-0.03640696,-0.00488879,-0.00801501,-0.00764683,0.02154869,0.05855661,-0.07922062,0.00830897,0.05007157,0.05956158,-0.03445766,0.01614006,-0.03186311,0.02856763,-0.0076903,-0.04534337,0.105838,0.01054229,-0.03154222,-0.05515714,-0.00864494,0.07386707,-0.01212616,0.0351677,-0.03215273,-0.06586719,0.02457451,0.02253509,0.02172866,0.10765673,0.00689119,-0.05952962,0.0158864,-0.01533049,-0.01115988,0.04843929,0.01432922,0.00541487,0.066781,0.0337694,-0.01771717,-0.0277033,-0.04449821,0.02445083,0.03471575,-0.01850398,0.0011412,0.00707499,-0.08752444,-0.0793183,-0.05543283,-0.04034484,-0.05102253,0.01318186,0.04542718,0.00183195,0.02103722,-0.09251343,0.01259882,0.06186311,-0.10341696,-0.02064555,0.02362055,0.0226708,-0.00035257,-0.02496135,0.0199907,0.00232133,0.00812966,-0.01027352,-0.01776792,-0.02870484,-0.03703523,0.08903878,0.07761142,-0.06165158,-0.01176832,-0.04806693,-0.0621157,-0.04436664,0.08932671,-0.00672391,-0.09893085,-0.03598259,0.03861777,-0.02832209,-0.0436624,-0.02446178,-0.02627028,-0.08799089,-0.04531424,0.02227666,-0.05349996,-0.09458424,-0.02885771,0.0088565,0.0223189,0.01273212,-0.03066765,-0.06800702,-0.00236697,0.02089388,-0.06675216,0.01436201,-0.05251889,0.05420541,-0.01987005,-0.08357323,-0.01316821,-0.07491672,0.03184745,-0.04056417,-0.00320329,0.01416191,-0.04196381,0.06620067,-0.03572518,-0.00404651,-0.02471568,-0.0140608,0.0050737,-0.04424408,0.07265937,-0.01094049,-0.12463783,0.04892102,0.00876116,0.01882726,0.01769865,0.03943193,0.05757356,-0.01607067,0.04407465,0.00413622,0.04350296,-0.05933069,0.07928067,-0.011499,0.0611822,-0.03135232,-0.2110737,-0.02619526,-0.04538354,-0.00975349,0.05650781,-0.01876104,0.00314648,0.00036745,0.08054025,0.04677771,0.08114695,-0.05462046,0.01057814,0.03448549,0.01170762,0.05638319,-0.08200455,-0.01734284,-0.03995574,0.05229026,-0.02156479,0.00650617,-0.01124583,-0.05521765,0.05726225,-0.00880254,0.15820651,0.05261713,0.04439079,0.04767624,0.04465321,0.04016462,0.00974651,-0.10586075,0.03273113,0.03690018,0.05069416,0.01160981,-0.03237246,-0.02566034,-0.07953877,0.02797451,0.04594953,-0.12382398,-0.04558269,-0.01656356,0.01993731,0.05077908,-0.00965904,0.03871523,0.07332828,0.00832266,0.02973812,0.02464249,0.06765419,-0.01599312,-0.07863078,0.00160671,0.01580653,0.03342419,-0.04103377,-0.0052976,0.03135797,-0.01768494,0.06272323,0.00960653,0.02749831,0.00481119,0.07164293,-0.02838006,0.00252337,0.07692228,0.03557342,0.02834938,0.0682177,0.02572715,0.09489274,-0.10417379,0.03662318,-0.00959741,-0.04124071,-0.06538653,0.06884155,0.06643911,0.05854262,0.00973397,0.06150565,0.02155735,0.05367283,-0.0121608,-0.01206722,0.0480189,-0.07772449,-0.04446449,0.01892436,0.08832398,-0.25661299,-0.0085122,0.01464933,0.06379347,-0.01959076,-0.04003406,0.03057614,-0.03359349,-0.03247479,0.04925094,0.02269264,0.02304353,0.03465001,-0.01317388,0.00030288,-0.02828154,0.00435406,-0.01350256,0.09184792,0.06041858,-0.02694644,0.0669957,0.21443544,-0.04076184,0.02169628,-0.00233862,0.00019318,-0.01840037,0.02499717,0.05245624,0.01612595,0.02924646,0.02685606,0.00098567,0.01967192,0.04247274,-0.02745087,-0.01884978,0.00277566,0.01683606,-0.05126594,0.02137212,0.01799669,0.02494226,0.08107914,0.02024162,-0.01809879,-0.05984514,0.01000636,0.02012395,-0.03823873,-0.06003894,0.02380637,-0.03354124,0.05611615,0.06073464,0.01147056,-0.03443025,0.02413465,-0.02216565,-0.00658757,0.0166595,0.07972921,0.03225102,0.02379916],"last_embed":{"hash":"ldgs79","tokens":470}}},"last_read":{"hash":"ldgs79","at":1753423435709},"class_name":"SmartSource","last_import":{"mtime":1753363607317,"size":13993,"at":1753423416052,"hash":"ldgs79"},"blocks":{"#---frontmatter---":[1,6],"#Blackjack Strategy, System Test: High Roller Software App":[8,129],"#Blackjack Strategy, System Test: High Roller Software App#{1}":[10,57],"#Blackjack Strategy, System Test: High Roller Software App#{2}":[58,58],"#Blackjack Strategy, System Test: High Roller Software App#{3}":[59,59],"#Blackjack Strategy, System Test: High Roller Software App#{4}":[60,60],"#Blackjack Strategy, System Test: High Roller Software App#{5}":[61,62],"#Blackjack Strategy, System Test: High Roller Software App#{6}":[63,72],"#Blackjack Strategy, System Test: High Roller Software App##Consistency":[73,82],"#Blackjack Strategy, System Test: High Roller Software App##Consistency#{1}":[75,82],"#Blackjack Strategy, System Test: High Roller Software App##Verdict":[83,105],"#Blackjack Strategy, System Test: High Roller Software App##Verdict#{1}":[85,98],"#Blackjack Strategy, System Test: High Roller Software App##Verdict#{2}":[99,100],"#Blackjack Strategy, System Test: High Roller Software App##Verdict#{3}":[101,101],"#Blackjack Strategy, System Test: High Roller Software App##Verdict#{4}":[102,103],"#Blackjack Strategy, System Test: High Roller Software App##Verdict#{5}":[104,105],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)":[106,129],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{1}":[108,109],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{2}":[110,111],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{3}":[112,113],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{4}":[114,114],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{5}":[115,115],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{6}":[116,116],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{7}":[117,117],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{8}":[118,118],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{9}":[119,119],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{10}":[120,120],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{11}":[121,121],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{12}":[122,123],"#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{13}":[124,129]},"outlinks":[{"title":"_**The Best Blackjack Strategy:**_ **Shoe-Start System**","target":"https://forums.saliu.com/card-counting-blackjack.html#shoe","line":27},{"title":"_**Blackjack System for Streaks Betting, Martingale Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":29},{"title":"_**real casinos are rigged (programmed to beat-to-bankruptcy blackjack players)**_","target":"https://www.facebook.com/FreeSoftwareBlackjackRouletteLottery/posts/****************","line":31},{"title":"![Casino Gambling Software: Blackjack, Roulette, Baccarat, Craps, Systems.","target":"https://saliu.com/images/blackjack-highroller.gif","line":43},{"title":"The mental blackjack gambling system beat the casino and house advantage HA big time.","target":"https://saliu.com/images/blackjack-winning-system.gif","line":63},{"title":"_**Blackjack Probability, Odds Natural, Insurance, Double Down**_","target":"https://forums.saliu.com/blackjack-natural-odds-probability.html#double-down","line":65},{"title":"Gambling streaks strategies beat unbiased blackjack app to win 20000 dollars in one week.","target":"https://saliu.com/images/blackjack-winning-strategy.gif","line":71},{"title":"Mental blackjack strategy system wins consistently thousands of dollars a day in heads-up play.","target":"https://saliu.com/images/blackjack-winning-consistency.gif","line":77},{"title":"The best and free blackjack strategy beats the casino dealer with no doubt.","target":"https://saliu.com/images/blackjack-highroller-strategy.gif","line":81},{"title":"_**Fearing Losses, Casinos Bar, Ban Winning Gamblers, Skilled Gambling Players**_","target":"https://saliu.com/winning.html","line":89},{"title":"How other players apply Ion Saliu's blackjack system based on gambling streaks.","target":"https://saliu.com/HLINE.gif","line":91},{"title":"There is a good and fair blackjack online app at Wizard of Odds site.","target":"https://saliu.com/images/WizardofOddsBlackjack.jpg","line":93},{"title":"_**Card Counting Quirks?**_","target":"https://wizardofvegas.com/forum/gambling/blackjack/39431-card-counting-quirks/2/#post934618","line":99},{"title":"Players can really win at blackjack in negative counts because of more double-downs.","target":"https://saliu.com/HLINE.gif","line":104},{"title":"<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>","target":"https://saliu.com/content/blackjack.html","line":106},{"title":"<u><b>Blackjack</b></u>: _**Basic Strategy, Card Counting, Charts, Probability, Odds, Software**_","target":"https://saliu.com/blackjack.html","line":110},{"title":"_**Casinos, Gambling, Win in Casino with Mathematical Systems**_","target":"https://saliu.com/keywords/casino.htm","line":114},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":115},{"title":"**_The Best Blackjack Basic Strategy: Free Cards, Charts_**","target":"https://saliu.com/bbs/messages/399.html","line":116},{"title":"_**Gambling Mathematics in Blackjack Proves Deception of Card-Counting Systems**_","target":"https://saliu.com/bbs/messages/274.html","line":118},{"title":"_**Calculate Blackjack Probability, Odds: Natural 21, Insurance, Double-Down Hands, Pairs**_","target":"https://forums.saliu.com/blackjack-natural-odds-probability.html","line":119},{"title":"_**New Casino Games by Ion Saliu:**_ **Parpaluck Blackjack**","target":"https://forums.saliu.com/new-blackjack-game.html","line":120},{"title":"_**<u>ABC</u>: The Best Blackjack Card-Counting System by Ion Saliu**_","target":"https://forums.saliu.com/card-counting-blackjack.html#abc-system","line":121},{"title":"**Software**","target":"https://saliu.com/infodown.html","line":122},{"title":"This is the blackjack site for all humans plus casino gamblers with the goal to win big-time.","target":"https://saliu.com/HLINE.gif","line":124},{"title":"Forums","target":"https://forums.saliu.com/","line":126},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":126},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":126},{"title":"Contents","target":"https://saliu.com/content/index.html","line":126},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":126},{"title":"Home","target":"https://saliu.com/index.htm","line":126},{"title":"Search","target":"https://saliu.com/Search.htm","line":126},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":126},{"title":"Learn correct blackjack odds, house edge, advantage, mathematical calculations by accurate program.","target":"https://saliu.com/HLINE.gif","line":128}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["blackjack","mental blackjack system","basic strategy","software","3rd blackjack app","casino","dealer","player","odds","gambling","streaks","house advantage","Windows 10"],"source":"https://saliu.com/blackjack-strategy-system-win.html","author":null}},"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14289033,-0.00082669,-0.07140304,-0.00240943,0.00057361,0.01788452,0.01930206,0.00443702,0.03952947,-0.02124356,0.01841947,-0.01359109,0.00815248,-0.01234953,-0.02840452,-0.03086018,0.02268064,-0.01940719,0.00970278,0.01721656,0.03151569,-0.03738954,-0.08696984,-0.04926227,0.03900517,-0.01524578,-0.04331798,-0.0283227,-0.05199584,-0.18935232,-0.01833227,-0.03152198,0.05519724,-0.03962675,-0.05080064,-0.0441871,-0.04347819,0.06839067,0.00329511,0.00471453,0.0185081,0.05559806,-0.02870894,0.03916646,-0.01472229,-0.03596483,0.01902897,0.02694589,0.04087811,-0.01715074,-0.03941113,-0.00627828,-0.05508061,-0.01962548,0.07307056,-0.01313238,0.06871387,0.05335727,-0.02972705,0.06434207,0.0120331,0.00887434,-0.21654841,0.07277252,0.00416092,0.03846969,-0.02160743,-0.02380754,0.03229327,0.03988049,0.00232456,0.02836005,-0.02371707,0.07805638,0.03647676,-0.01352693,0.00341678,-0.07172127,0.00637267,-0.03330174,0.02553947,-0.07693689,-0.00651235,-0.04829038,0.01000043,-0.03936661,0.00578703,0.04687238,0.05827779,-0.06661095,0.01159586,0.03153613,0.07682541,-0.02272327,-0.0159137,-0.03341687,0.02796398,-0.01617778,-0.05728596,0.13756965,-0.01565407,-0.0168364,-0.0286355,0.00283922,0.05079913,0.01498041,0.03402971,-0.03721518,-0.06974129,0.00779155,0.01999504,0.02908342,0.07999512,-0.02004662,-0.02255065,-0.00622132,0.00532321,-0.01097028,0.04829148,0.0218407,0.01303302,0.0411345,0.06369261,-0.04997788,-0.00741433,-0.0118427,0.03796457,0.03375017,-0.00714148,-0.00263861,0.00716108,-0.06551889,-0.07097653,-0.04729748,-0.02104014,-0.03111295,0.03595149,0.00444887,-0.01105237,-0.00894992,-0.09403844,0.00487733,0.06471886,-0.10869782,-0.0152625,0.03642337,0.00352766,0.00604857,-0.01715543,0.01203834,-0.02803163,-0.00820963,0.0164785,-0.01278844,-0.0155244,-0.02387814,0.1221145,0.08794324,-0.01810412,0.00219672,-0.03675966,-0.05875832,-0.06639324,0.12470687,0.00527049,-0.10924905,-0.03692298,0.0426162,-0.02437349,-0.04053968,-0.01645818,-0.02387068,-0.07094366,-0.02282175,0.05274154,-0.03765466,-0.0347715,-0.01422093,0.00523744,0.01556067,0.01325463,-0.02118781,-0.08529281,-0.01151725,0.00897412,-0.04822997,0.00514549,-0.03984725,0.04862556,-0.0051562,-0.10098814,-0.03754209,-0.03737802,0.02445695,-0.02485208,0.01406241,0.01150663,-0.03334497,0.06545873,-0.0588134,0.00682642,-0.01484129,-0.02796187,0.02385972,-0.04527086,0.04603178,-0.02453863,-0.11407584,0.01813703,-0.0010678,0.03821572,0.0331108,0.08224251,0.05469115,-0.01544644,0.02201112,-0.05703308,0.03514358,-0.04170544,0.06444855,-0.01964975,0.03734924,-0.03309479,-0.22049077,0.00696733,-0.01488499,-0.02929516,0.01577388,-0.00628083,0.03059075,0.02179498,0.0567212,0.04048879,0.06969137,-0.04360181,0.00320568,-0.00441109,0.00824875,0.06324223,-0.06847502,-0.02928142,-0.01900164,0.07641468,-0.01347472,-0.01299999,-0.02608173,-0.06734277,0.04831276,-0.01540775,0.14666337,0.05999504,0.02756883,0.02697694,0.05638984,0.03266755,0.00334093,-0.09398121,0.0320236,0.02905805,-0.00343882,0.03909264,-0.02605116,-0.04511821,-0.07821389,0.03320251,0.02933897,-0.12716201,-0.01240509,-0.03642902,0.04776247,0.01284115,-0.02464982,0.0105173,0.0402059,-0.00143705,-0.00845805,0.03761367,0.06916437,-0.01135722,-0.09160256,-0.0065341,0.01602313,0.01473504,-0.02618109,0.01519745,0.03163344,-0.0314849,0.0308562,0.00959675,0.03192831,-0.02792203,0.03469912,-0.03876704,0.01586048,0.07863981,0.04179573,0.01925976,0.0329936,0.02277189,0.06840064,-0.08457302,0.04009201,-0.02616237,-0.05353332,-0.08158007,0.07800023,0.05543179,0.07267823,0.01641055,0.03870182,-0.00837011,0.10133701,-0.01426475,-0.0295351,0.04361386,-0.05392792,-0.0480429,0.0615222,0.06010547,-0.26823878,0.01407595,0.05482278,0.05870865,-0.01394795,-0.01512227,0.03609591,-0.02289622,-0.03603158,0.05160929,0.02551581,0.03641235,0.03190859,0.0084355,-0.02378371,-0.01419107,0.03145995,-0.00922228,0.07970785,0.06761782,-0.03961925,0.06840905,0.22955284,0.00521761,0.02232477,0.01091598,-0.01080297,-0.00025976,0.02145913,0.06659059,0.01104644,0.02308944,0.0420248,-0.0026843,0.00705879,0.08253099,0.00231546,-0.03695464,0.00504475,0.04519893,-0.07570194,0.01380554,-0.01090358,0.02631622,0.08434971,0.02223119,-0.04481121,-0.05945725,-0.01461699,-0.00745382,-0.04166168,-0.08891341,0.01136938,-0.00017841,0.07570798,0.07571118,0.04379721,-0.0391097,0.00366038,-0.02094663,0.0190357,-0.00836643,0.08929333,0.02528587,0.04129002],"last_embed":{"hash":"hhznnm","tokens":107}}},"text":null,"length":0,"last_read":{"hash":"hhznnm","at":1753423434566},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#---frontmatter---","lines":[1,6],"size":274,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10228316,0.01161039,-0.07539742,0.01774096,0.00258165,0.03826142,0.04787292,-0.00060388,0.05437612,-0.03145704,0.00797248,0.01690998,-0.02055341,-0.0067392,-0.02695108,-0.06056478,0.04749302,-0.00314905,-0.0081859,0.03375798,0.00582489,-0.05554932,-0.07595591,-0.05077187,0.04436236,-0.01664857,-0.06370267,-0.054037,-0.0819231,-0.22480597,0.00359864,-0.06151875,0.05895066,-0.03550464,-0.06623619,-0.03469585,-0.04378506,0.02644486,-0.04986868,0.00393201,0.02862723,0.05053952,0.01132105,0.02522428,0.00791653,-0.03990185,0.03824996,0.0377601,0.04704492,0.00167678,-0.04787717,0.0039718,-0.05136792,-0.0003043,0.04825964,-0.01762352,0.04515579,0.0902736,-0.0356913,0.02659542,0.01863062,0.02765217,-0.17036545,0.05179777,-0.02770309,0.0402252,-0.01618887,-0.0076897,0.00021141,0.04373842,-0.00487331,0.03507765,-0.04839958,0.05923612,0.05252265,-0.03729689,0.02239024,-0.0331913,0.00208534,0.00644631,0.02820488,-0.05441403,0.03819442,-0.04338913,-0.00621825,0.00246385,-0.00817276,0.01123379,0.06030042,-0.07813051,0.01606398,0.05229709,0.05245655,-0.04158473,0.01693055,-0.0272399,0.0249311,-0.01269155,-0.03908893,0.10972837,0.01152593,-0.0387359,-0.06663781,-0.00820748,0.07251589,-0.01445079,0.03423861,-0.0265802,-0.05908633,0.02370642,0.02177679,0.01661718,0.10859768,0.00773022,-0.05613593,0.01759157,-0.02026946,-0.01973715,0.04152127,0.01717921,-0.00515718,0.06719596,0.02045233,-0.00281709,-0.03561823,-0.04915878,0.01758736,0.0440304,-0.02589584,-0.00079355,0.00679068,-0.0763896,-0.083994,-0.05295921,-0.03883599,-0.05151587,0.01031465,0.05483554,0.01008087,0.03180284,-0.08959289,0.01230526,0.06944217,-0.10407622,-0.01755999,0.02614093,0.02710902,-0.0065511,-0.0304604,0.01935078,0.00268594,0.00439265,-0.01860504,-0.01963403,-0.02990473,-0.03433779,0.06834286,0.07992227,-0.07087112,-0.00931898,-0.05171949,-0.06344171,-0.03575015,0.09202483,-0.01206751,-0.0911222,-0.03659829,0.03686625,-0.02461391,-0.03940063,-0.02620198,-0.02426966,-0.08882005,-0.04327065,0.01656834,-0.05342253,-0.10339243,-0.02891835,-0.00022616,0.01702977,0.01258771,-0.02768654,-0.06324156,0.00681596,0.02625186,-0.07823941,0.01946285,-0.05462594,0.05521956,-0.02552108,-0.07341649,-0.01210046,-0.08613879,0.0278301,-0.04008207,-0.01813594,0.01550076,-0.03874841,0.06539796,-0.03678478,-0.00764363,-0.03039001,-0.00814688,0.00508405,-0.03026416,0.07624733,-0.01186647,-0.12605081,0.05066055,0.01413956,0.01963796,0.01158091,0.02886566,0.06352627,-0.02035599,0.0505366,0.02124419,0.04771285,-0.06109742,0.08901318,0.00271237,0.06309873,-0.02849234,-0.21281782,-0.02497796,-0.04423693,-0.00024575,0.06723458,-0.02313111,0.00007071,-0.00169971,0.08761773,0.05787983,0.07669398,-0.05487866,0.00913977,0.03522649,0.01231694,0.05256133,-0.08246591,-0.02022035,-0.04131628,0.04106807,-0.02551273,0.00103887,-0.01478147,-0.05065659,0.06729892,-0.0005974,0.15844885,0.054179,0.04434492,0.04696711,0.04176359,0.03650347,0.01237941,-0.11252794,0.03869663,0.03399983,0.05936971,0.00977208,-0.03433911,-0.01540486,-0.07120682,0.02117226,0.04152554,-0.11215185,-0.05118842,-0.00020344,0.01493035,0.05555405,-0.00528243,0.04506766,0.07180472,0.0124772,0.0351151,0.01728892,0.06442585,-0.01757432,-0.07533327,0.0038547,0.01955555,0.04305661,-0.04059536,-0.01603404,0.02170496,-0.01688737,0.07838067,0.01280148,0.02635797,0.00898278,0.07897608,-0.01490987,-0.00097865,0.07278784,0.02571769,0.02412468,0.08106079,0.01926821,0.09584906,-0.10223247,0.03784104,-0.00670968,-0.03227451,-0.06248693,0.0608624,0.0717104,0.05161006,0.00960935,0.0629605,0.02837011,0.04750703,-0.00402345,-0.00393658,0.04199606,-0.07921869,-0.04156049,0.0083318,0.09963106,-0.2516008,-0.0108643,0.00251324,0.06240157,-0.01670583,-0.04149707,0.03384379,-0.03592959,-0.03696739,0.05274662,0.02468673,0.02279218,0.04081493,-0.01545314,-0.00183853,-0.03183122,0.00303563,-0.02588419,0.09055234,0.05489676,-0.0232697,0.05760001,0.20945781,-0.05275077,0.02693578,-0.0031675,0.00345339,-0.02686535,0.02095258,0.04397045,0.02147363,0.02731802,0.01666873,0.00103609,0.02630028,0.04437928,-0.03840505,-0.0130822,0.01141733,0.01533184,-0.03780358,0.02146393,0.01875689,0.01786139,0.08225861,0.02278452,-0.02321931,-0.06711831,0.01692399,0.0171768,-0.03681206,-0.04796418,0.03057801,-0.03129646,0.04137424,0.05574328,-0.00089353,-0.02865406,0.02561801,-0.01911617,-0.00414703,0.02750257,0.06790759,0.02950053,0.01102145],"last_embed":{"hash":"zkhlt3","tokens":389}}},"text":null,"length":0,"last_read":{"hash":"zkhlt3","at":1753423434602},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App","lines":[8,129],"size":13669,"outlinks":[{"title":"_**The Best Blackjack Strategy:**_ **Shoe-Start System**","target":"https://forums.saliu.com/card-counting-blackjack.html#shoe","line":20},{"title":"_**Blackjack System for Streaks Betting, Martingale Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":22},{"title":"_**real casinos are rigged (programmed to beat-to-bankruptcy blackjack players)**_","target":"https://www.facebook.com/FreeSoftwareBlackjackRouletteLottery/posts/****************","line":24},{"title":"![Casino Gambling Software: Blackjack, Roulette, Baccarat, Craps, Systems.","target":"https://saliu.com/images/blackjack-highroller.gif","line":36},{"title":"The mental blackjack gambling system beat the casino and house advantage HA big time.","target":"https://saliu.com/images/blackjack-winning-system.gif","line":56},{"title":"_**Blackjack Probability, Odds Natural, Insurance, Double Down**_","target":"https://forums.saliu.com/blackjack-natural-odds-probability.html#double-down","line":58},{"title":"Gambling streaks strategies beat unbiased blackjack app to win 20000 dollars in one week.","target":"https://saliu.com/images/blackjack-winning-strategy.gif","line":64},{"title":"Mental blackjack strategy system wins consistently thousands of dollars a day in heads-up play.","target":"https://saliu.com/images/blackjack-winning-consistency.gif","line":70},{"title":"The best and free blackjack strategy beats the casino dealer with no doubt.","target":"https://saliu.com/images/blackjack-highroller-strategy.gif","line":74},{"title":"_**Fearing Losses, Casinos Bar, Ban Winning Gamblers, Skilled Gambling Players**_","target":"https://saliu.com/winning.html","line":82},{"title":"How other players apply Ion Saliu's blackjack system based on gambling streaks.","target":"https://saliu.com/HLINE.gif","line":84},{"title":"There is a good and fair blackjack online app at Wizard of Odds site.","target":"https://saliu.com/images/WizardofOddsBlackjack.jpg","line":86},{"title":"_**Card Counting Quirks?**_","target":"https://wizardofvegas.com/forum/gambling/blackjack/39431-card-counting-quirks/2/#post934618","line":92},{"title":"Players can really win at blackjack in negative counts because of more double-downs.","target":"https://saliu.com/HLINE.gif","line":97},{"title":"<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>","target":"https://saliu.com/content/blackjack.html","line":99},{"title":"<u><b>Blackjack</b></u>: _**Basic Strategy, Card Counting, Charts, Probability, Odds, Software**_","target":"https://saliu.com/blackjack.html","line":103},{"title":"_**Casinos, Gambling, Win in Casino with Mathematical Systems**_","target":"https://saliu.com/keywords/casino.htm","line":107},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":108},{"title":"**_The Best Blackjack Basic Strategy: Free Cards, Charts_**","target":"https://saliu.com/bbs/messages/399.html","line":109},{"title":"_**Gambling Mathematics in Blackjack Proves Deception of Card-Counting Systems**_","target":"https://saliu.com/bbs/messages/274.html","line":111},{"title":"_**Calculate Blackjack Probability, Odds: Natural 21, Insurance, Double-Down Hands, Pairs**_","target":"https://forums.saliu.com/blackjack-natural-odds-probability.html","line":112},{"title":"_**New Casino Games by Ion Saliu:**_ **Parpaluck Blackjack**","target":"https://forums.saliu.com/new-blackjack-game.html","line":113},{"title":"_**<u>ABC</u>: The Best Blackjack Card-Counting System by Ion Saliu**_","target":"https://forums.saliu.com/card-counting-blackjack.html#abc-system","line":114},{"title":"**Software**","target":"https://saliu.com/infodown.html","line":115},{"title":"This is the blackjack site for all humans plus casino gamblers with the goal to win big-time.","target":"https://saliu.com/HLINE.gif","line":117},{"title":"Forums","target":"https://forums.saliu.com/","line":119},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":119},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":119},{"title":"Contents","target":"https://saliu.com/content/index.html","line":119},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":119},{"title":"Home","target":"https://saliu.com/index.htm","line":119},{"title":"Search","target":"https://saliu.com/Search.htm","line":119},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":119},{"title":"Learn correct blackjack odds, house edge, advantage, mathematical calculations by accurate program.","target":"https://saliu.com/HLINE.gif","line":121}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10106412,0.0114555,-0.07532693,0.01935628,0.00342998,0.03944492,0.04751755,-0.00070516,0.05472524,-0.03176463,0.00810371,0.01747073,-0.02185343,-0.00624808,-0.0278813,-0.06005201,0.04714943,-0.00283546,-0.00905066,0.03441161,0.00504125,-0.05670869,-0.07596751,-0.04967627,0.04498297,-0.01659375,-0.06288499,-0.05357763,-0.08196341,-0.2244098,0.0037813,-0.06327888,0.05870131,-0.0358747,-0.06560899,-0.0361276,-0.04390133,0.02615124,-0.04930838,0.00371147,0.02912516,0.0503286,0.01219612,0.0251824,0.00748563,-0.04021623,0.0386842,0.03778834,0.04806695,0.00066602,-0.04795215,0.0043314,-0.05152,0.0001732,0.0482833,-0.01813394,0.04471078,0.09046745,-0.03620864,0.02449127,0.01871142,0.02809898,-0.17048818,0.05065186,-0.02839301,0.04089651,-0.01570544,-0.00759242,-0.00115323,0.04352091,-0.00572186,0.03432282,-0.04851764,0.05887927,0.05234436,-0.03760238,0.02190826,-0.03298776,0.0010126,0.0073569,0.02785933,-0.05410596,0.03888591,-0.04453719,-0.00658421,0.00228531,-0.00793561,0.00952838,0.0601411,-0.07733644,0.0156546,0.05227455,0.05078836,-0.04184715,0.01716273,-0.02738314,0.02503151,-0.01285851,-0.03772222,0.10891703,0.01268217,-0.0381135,-0.06679987,-0.00822564,0.07312182,-0.01538784,0.03432852,-0.0263774,-0.05852696,0.02333452,0.02120646,0.01616411,0.10856412,0.00907902,-0.05620938,0.01866534,-0.02119915,-0.02015174,0.04168248,0.01768303,-0.00598072,0.06648725,0.02047275,-0.00236537,-0.03573664,-0.04958131,0.01730241,0.04491866,-0.02544025,-0.00051678,0.00652523,-0.07782573,-0.0838581,-0.05268818,-0.03978554,-0.05200429,0.0102223,0.05593409,0.01069444,0.03073445,-0.0875554,0.01178415,0.0694099,-0.10417936,-0.01742855,0.02609797,0.02729865,-0.00662135,-0.02982755,0.01963935,0.00281947,0.00434652,-0.01908388,-0.01975073,-0.03087686,-0.03339764,0.06734212,0.07990608,-0.07101429,-0.00898871,-0.05268538,-0.06377234,-0.03510602,0.09242172,-0.01275012,-0.08985222,-0.03762646,0.03574296,-0.02450476,-0.0398419,-0.02707963,-0.02494035,-0.08876798,-0.04300755,0.01570536,-0.0526837,-0.10498255,-0.02876262,-0.00071335,0.01670267,0.01202512,-0.02753744,-0.06309224,0.00780161,0.02580473,-0.0782406,0.01942767,-0.05392515,0.05478619,-0.02503284,-0.07270065,-0.0107051,-0.0862731,0.02833274,-0.03977018,-0.01952006,0.01592302,-0.03944772,0.06464273,-0.03692617,-0.0075188,-0.0303443,-0.00790598,0.00448319,-0.03009421,0.07585823,-0.01030228,-0.12585019,0.05117372,0.0136994,0.0191702,0.01065192,0.02692993,0.0635852,-0.01995022,0.0509866,0.02250873,0.04727226,-0.06114279,0.08946259,0.00215504,0.06289458,-0.02841156,-0.21200308,-0.0262599,-0.04431589,-0.00080807,0.06859359,-0.02360376,-0.00039608,-0.00185673,0.08701162,0.05778522,0.07614336,-0.05520348,0.00959687,0.03585963,0.01144781,0.05361081,-0.08164798,-0.01993835,-0.04098511,0.04093046,-0.02596899,0.00080177,-0.01470457,-0.04973745,0.06854645,-0.00117291,0.15828423,0.05349092,0.04416509,0.04708794,0.04131052,0.03685436,0.0123726,-0.11142532,0.0401394,0.033714,0.05998832,0.00911558,-0.03399561,-0.01536107,-0.0708678,0.0209464,0.04185221,-0.11093888,-0.05206687,-0.00053573,0.01486324,0.05658907,-0.00571826,0.04574491,0.07257257,0.01404756,0.03539645,0.01753292,0.06446347,-0.01673757,-0.07476219,0.00453156,0.02025264,0.04378385,-0.0405684,-0.01650455,0.02010272,-0.01716092,0.0788982,0.01351111,0.02635336,0.00910178,0.07961136,-0.01440469,-0.00033241,0.07211369,0.02567172,0.02430631,0.082031,0.01959206,0.095179,-0.10328458,0.03814451,-0.00588842,-0.03126707,-0.06218698,0.0600776,0.07275318,0.05191201,0.00909203,0.06260069,0.02834586,0.04821484,-0.00413908,-0.00337435,0.04214976,-0.08007597,-0.04078392,0.00871659,0.10022231,-0.25228631,-0.01140048,0.00116347,0.06231821,-0.01737435,-0.04178115,0.03337298,-0.03568269,-0.03730833,0.052403,0.02400422,0.02291724,0.04013764,-0.01419584,-0.00240881,-0.03230963,0.00239491,-0.02609104,0.09141915,0.05496973,-0.02296484,0.05737741,0.20897312,-0.05250663,0.02644165,-0.0042916,0.00371813,-0.02808454,0.0197982,0.04436815,0.02312706,0.02725208,0.01753871,0.0008635,0.02617075,0.04510774,-0.03933389,-0.01256911,0.01165326,0.01494033,-0.03788929,0.02156214,0.01840028,0.01746511,0.08315305,0.02321941,-0.02259576,-0.06794144,0.01720599,0.016847,-0.03678194,-0.04606612,0.03189244,-0.03172894,0.04064658,0.05573843,-0.00111039,-0.02883092,0.02629715,-0.0182227,-0.00450051,0.02801053,0.06768375,0.02912394,0.0104759],"last_embed":{"hash":"ikz033","tokens":389}}},"text":null,"length":0,"last_read":{"hash":"ikz033","at":1753423434743},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{1}","lines":[10,57],"size":5188,"outlinks":[{"title":"_**The Best Blackjack Strategy:**_ **Shoe-Start System**","target":"https://forums.saliu.com/card-counting-blackjack.html#shoe","line":18},{"title":"_**Blackjack System for Streaks Betting, Martingale Progressions**_","target":"https://saliu.com/occult-science-gambling.html#GamblingSystem","line":20},{"title":"_**real casinos are rigged (programmed to beat-to-bankruptcy blackjack players)**_","target":"https://www.facebook.com/FreeSoftwareBlackjackRouletteLottery/posts/****************","line":22},{"title":"![Casino Gambling Software: Blackjack, Roulette, Baccarat, Craps, Systems.","target":"https://saliu.com/images/blackjack-highroller.gif","line":34}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,-0.********,-0.********,-0.0029363,0.********,0.********,-0.********,0.********,-0.********,0.********,0.********,0.0238931,0.********,-0.********,-0.********,0.0352776,-0.016059,0.********,0.0454377,-0.********,-0.********,-0.********,-0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,0.02878523,0.07645735,0.00119962,0.01478593,-0.02134408,-0.02017745,0.00188771,0.0366711,0.03696527,0.01074275,-0.02996108,-0.06107917,-0.0598046,-0.01206192,0.05718556,-0.00356579,0.06568924,0.07454549,-0.07613856,0.08469758,0.01348895,0.03144482,-0.1734302,0.02569674,0.02363147,0.04676331,-0.03786335,0.00075374,0.03068289,0.07162374,0.01924399,0.03628765,-0.03055344,0.05361064,0.00832409,-0.0264747,-0.00586803,-0.06572595,0.03424421,0.01370936,-0.00324041,-0.04956888,-0.01510755,-0.04058873,-0.0168245,-0.03167597,-0.00432734,0.01506525,0.01190554,-0.07724614,0.02035569,0.06564531,0.00004943,-0.05555629,0.02236475,-0.02447143,-0.00611913,-0.03907903,-0.0036829,0.14950553,0.01443511,-0.05029007,-0.05175387,0.01217617,0.00858281,0.01204626,0.02526759,-0.01445673,-0.03131498,0.00708733,0.03325189,0.00427445,0.06256834,0.00941653,-0.05784609,-0.0095214,0.00110892,0.00180572,0.02270033,-0.02557449,0.02871242,0.07677608,0.06550059,-0.06352085,-0.00346396,-0.04457266,0.04502847,0.03843597,-0.00246694,0.00235487,-0.02811648,-0.0706315,-0.04752232,-0.04144183,-0.02788645,-0.05209721,0.06536422,0.0555678,-0.00763304,0.02321332,-0.09000757,-0.00647099,0.10544488,-0.10837072,-0.02030255,0.05789983,-0.04587558,0.03214497,-0.00534821,-0.01316591,-0.01323997,-0.04928878,-0.00945661,-0.01778723,-0.01926902,0.00467883,0.04496782,0.08646701,-0.02042026,-0.01245765,-0.06240128,-0.06241014,-0.04468424,0.0680544,-0.03667716,-0.08825813,-0.05981107,0.04525774,-0.01189683,-0.04666457,-0.01884228,-0.02372937,-0.1072281,0.0378284,0.05444578,-0.03214822,-0.08500881,-0.03880611,0.03540939,0.02180619,0.04492371,0.01361643,-0.06374269,0.01292842,0.05030894,-0.03299892,-0.01985608,-0.02367561,0.08493917,-0.02131605,-0.10104546,-0.00713802,-0.06160628,0.05606288,0.02648469,0.01140936,0.02576843,0.00010137,0.07796787,-0.05058516,0.01142315,-0.01996922,-0.04399386,0.01010505,0.00205235,0.06876542,-0.04023969,-0.12537289,0.00698743,-0.00347069,0.03135364,0.01463553,0.05477275,0.06802896,0.01706534,0.05215654,-0.0122397,0.02575869,-0.02456855,0.07896131,0.02666973,0.02308307,-0.00398857,-0.20682453,-0.02317797,-0.00686851,0.00275097,0.03720542,0.00913255,0.01578666,0.02294283,0.06382727,0.07597074,0.05101098,-0.04754036,-0.02369339,0.02101984,-0.00789267,0.0686852,-0.09229095,-0.00789688,-0.03962404,0.06067566,0.01398134,0.00356605,-0.07560442,-0.02468153,0.04699767,0.01051464,0.15361761,-0.0048642,-0.02499023,0.04084864,0.03262853,0.05497107,0.00624337,-0.05882852,0.0609679,0.02035427,-0.00257986,-0.03315924,-0.03006694,-0.01168618,-0.05556926,-0.00651785,0.0071969,-0.13372006,0.01506666,-0.02649622,0.0439422,0.03841655,-0.0036291,-0.01280616,0.03737643,-0.02095876,0.00649247,0.0503764,0.07324145,-0.02991587,-0.05974696,0.00042298,0.00490785,0.01811003,-0.03102088,0.00781051,0.00268648,-0.01241719,0.0719393,0.01253621,0.00858596,-0.03265556,0.04801904,-0.03467248,-0.0032538,0.03807466,0.0254862,0.04914549,0.03416462,0.01196421,0.08315256,-0.06748098,0.03825642,-0.01501807,-0.02261994,-0.04830227,0.02001328,0.06597345,0.05442623,-0.03585258,0.05387551,-0.00181004,0.07515249,-0.02263325,-0.03814691,0.03735401,-0.06128129,-0.0117897,-0.0265582,0.08679364,-0.260717,0.01137142,0.01642179,0.06341072,-0.06271757,-0.02969242,0.05961609,-0.00646769,-0.01127206,0.04053752,0.03893504,0.04288732,0.07242439,0.01783749,-0.02391733,-0.02787282,0.0183366,-0.06286426,0.07750228,0.02264362,0.0014089,0.0730655,0.23350075,-0.0730782,0.03737709,0.00294338,-0.00310821,0.03069225,0.00991761,0.07070838,-0.00160389,0.01653176,0.05741491,-0.02695853,0.00522284,0.0965954,-0.00473832,-0.02022711,0.00525518,0.03155969,-0.03254265,-0.00951475,-0.03853745,0.0277298,0.07333479,-0.00089383,-0.05325312,-0.05826836,0.04794198,-0.02287349,-0.04902247,-0.03077906,0.05117515,-0.0117557,0.05190208,0.05526607,-0.01488954,0.007265,0.02515547,-0.02147863,0.01506813,0.00390927,0.05969735,0.0592104,0.01927383],"last_embed":{"hash":"ds05wt","tokens":73}}},"text":null,"length":0,"last_read":{"hash":"ds05wt","at":1753423434876},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{3}","lines":[59,59],"size":203,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11295549,-0.00065597,-0.06410988,-0.00588926,0.00462738,-0.01594172,0.06184969,-0.00416865,0.04306499,-0.01925702,0.03082154,0.0025046,0.02445428,-0.01165385,-0.06459986,-0.03894933,0.04429473,-0.02945973,0.00509152,0.02289434,-0.02129813,-0.02180284,-0.1063631,-0.05777579,0.03470028,0.03615706,-0.02285477,-0.05430786,-0.06177469,-0.23470592,0.01323262,-0.00850338,0.05129514,-0.03607928,-0.04869078,-0.03363563,-0.04450171,0.04782647,-0.00217515,0.03543558,0.04060517,0.05368927,-0.00340512,0.03061322,-0.03375987,-0.04710065,0.02739822,0.02138984,0.05903583,-0.03029724,-0.0507843,-0.05461168,-0.04041214,-0.014876,0.05168338,-0.02827269,0.08890347,0.07048324,-0.03612109,0.06391487,-0.00694923,0.0296474,-0.19131757,0.02895349,0.02629042,0.05079842,-0.02248169,-0.0003676,0.04595614,0.10960849,0.03589014,0.0097037,-0.05794397,0.09250129,0.02397596,0.02226539,-0.00163312,-0.04812755,0.03541751,0.00617587,0.02458215,-0.06978752,-0.0005761,-0.0317858,-0.02597548,-0.04682934,0.00736731,0.0237107,0.06560706,-0.08893615,0.05578616,0.02244037,0.02151482,-0.03219885,-0.01154576,-0.04962743,0.0222059,-0.05181172,-0.06913234,0.13320203,0.00364029,-0.04477428,-0.0412561,0.03383931,0.01008682,-0.01057979,0.04478455,-0.04885262,-0.04708619,0.01158909,0.06815135,-0.0038612,0.06684226,-0.0210657,-0.02227849,0.0325302,-0.00883557,-0.00259807,-0.00440569,0.00878019,0.00186273,0.09073224,0.07141793,-0.03206446,-0.0497741,-0.05978176,0.03345626,0.05428603,-0.03435296,-0.02597879,-0.02370521,-0.09177299,-0.07620887,-0.05475735,-0.03513879,-0.05207523,0.06752779,0.03504232,0.01257093,0.02664998,-0.10768821,-0.00281217,0.05480335,-0.08046226,-0.00726166,0.06628755,-0.03940959,0.04737845,0.0056926,-0.02257611,-0.01263223,-0.02825108,0.01027633,-0.00051818,0.00346966,-0.02809406,0.0993125,0.10190945,-0.01831586,0.01707602,-0.05595255,-0.03675414,-0.03488016,0.0781729,-0.00066157,-0.06676977,-0.03119458,0.01052513,-0.02326863,-0.01871918,-0.02148941,-0.0112011,-0.06728225,0.00656291,0.04401556,-0.02394345,-0.04192767,-0.02377857,0.02095002,0.05053026,0.04875799,0.01215118,-0.07639202,0.02029729,0.04938827,-0.05975742,-0.0182818,-0.02902168,0.06179837,-0.00590167,-0.10057524,-0.02358218,-0.06495373,0.02558241,-0.01061777,0.00294217,0.01467627,-0.0107493,0.04764371,-0.05957053,0.01123976,-0.04008566,-0.01200345,-0.00832302,-0.00838085,0.07693877,-0.02017647,-0.1407901,0.03803381,0.00321565,0.0256727,0.03433497,0.08779175,0.08457984,0.00948497,0.00633196,-0.02337555,0.07108141,-0.0607185,0.04147421,0.01792881,0.00386405,0.00181577,-0.21640311,-0.05094392,0.00673351,0.01833734,0.01352956,-0.00537061,0.02098882,0.02472956,0.04203772,0.06119654,-0.00471961,-0.02117024,-0.01505947,-0.00230394,0.03171631,0.04344711,-0.10675029,-0.01677789,0.0035209,0.05012468,-0.05197893,-0.02327429,-0.05547993,-0.03938892,0.04607765,0.00849225,0.17009252,0.04732138,-0.02313625,0.0437705,0.0471516,0.03755125,0.02835783,-0.09369624,0.05265783,0.0286101,0.00586501,0.00472514,-0.01105929,-0.03876935,-0.0616822,-0.00268099,-0.00405202,-0.11629959,-0.01404651,-0.05689356,0.03736115,0.00690413,-0.01074861,0.03160276,0.0349126,-0.02684893,0.00672533,0.04561036,0.09723818,-0.0367343,-0.10127638,0.0074687,0.00946705,0.00907859,-0.03472759,0.0180829,0.00941412,-0.04861523,0.04218167,0.0138744,-0.01014829,-0.00635479,0.00253855,-0.03842022,0.01321706,0.07686128,0.0407099,-0.01490919,0.0514614,0.01366886,0.05750138,-0.08528439,0.02195676,-0.01548856,-0.05041489,-0.04839307,0.04654818,0.01393974,0.03930997,-0.00922598,0.0425068,-0.008666,0.08395039,0.00002923,-0.02264401,0.03198027,-0.04911505,-0.02644343,0.0670187,0.08221064,-0.24514471,-0.00061706,0.02213862,0.06601614,-0.02510762,-0.01127767,0.05817829,-0.02707682,0.00306381,0.07470606,-0.01347378,0.03214752,0.06532382,-0.01473041,-0.00751095,-0.0225499,-0.00543448,-0.01926899,0.06948525,0.02341199,-0.03092153,0.05987009,0.20124432,-0.02869825,0.02846209,0.01291867,0.00205092,0.03596472,0.01174337,0.05304688,0.02242063,0.0278139,0.04422014,-0.01220558,0.00983554,0.04772395,-0.01110641,-0.03783857,0.02270635,0.0318631,-0.0341826,0.00222123,-0.01036988,0.02021667,0.09115673,-0.01062064,-0.0240382,-0.07125263,0.03223953,-0.00862126,-0.02925497,-0.0483225,0.02358606,0.0017108,0.06715526,0.10320693,-0.00795024,-0.00757027,0.0032466,-0.01533473,0.02151684,-0.02014519,0.04655912,0.03855983,0.03227075],"last_embed":{"hash":"145nwq5","tokens":92}}},"text":null,"length":0,"last_read":{"hash":"145nwq5","at":1753423434907},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{4}","lines":[60,60],"size":231,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10994981,0.01790208,-0.05321879,0.0136452,-0.03984985,0.0204511,0.04852126,0.00968061,0.06899422,-0.04013605,-0.00383368,0.00897412,0.02637064,-0.01449149,-0.06661987,-0.03126385,0.06585732,-0.00066145,-0.02177606,0.02766855,-0.01942411,-0.05202906,-0.11668672,-0.06161841,0.0534179,0.01062282,-0.04297586,-0.06661823,-0.0462191,-0.2376668,-0.0197661,-0.02662812,0.06484133,-0.05283541,-0.03970689,-0.0402653,-0.06033754,0.0465731,-0.01688977,0.01647973,0.02528392,0.03681626,-0.01520661,0.05519357,-0.0035372,-0.03990662,0.01973722,0.02219067,0.05697711,-0.01375272,-0.01658482,-0.03036496,-0.04431607,-0.02262259,0.03465758,-0.03071039,0.05041448,0.08406294,-0.04118124,0.02849995,0.02112731,0.00553575,-0.20621715,0.03612223,-0.01750784,0.03405036,-0.03290179,0.00183619,0.01628916,0.1128727,0.03962141,0.02400989,-0.08200003,0.06932255,0.03124265,0.00180915,0.01069926,-0.04492678,0.00411867,0.00540636,0.04466207,-0.1076913,0.00444948,-0.05728456,-0.00597675,-0.03147042,-0.00500367,0.00865547,0.0959138,-0.04619901,0.06862265,0.05077134,0.04355028,-0.03620902,-0.01776698,-0.05487837,0.02901039,-0.02517984,-0.03120663,0.10299316,0.00373586,-0.03939039,-0.04246006,0.00542816,-0.00220104,0.02093418,0.00712941,-0.02099996,-0.07293342,0.00285881,0.05989692,0.02463117,0.08690438,-0.02158513,-0.0237869,0.03233645,-0.00286881,0.00464675,0.00583193,0.03071958,-0.01646324,0.09613533,0.05061616,-0.01611489,-0.03147871,-0.02145232,0.0392328,0.07306422,-0.02587708,-0.03483174,-0.00284118,-0.07002956,-0.07590652,-0.05061744,-0.03863122,-0.02463238,0.06888314,0.04308356,0.00838709,0.03911786,-0.10011643,0.01745138,0.06948116,-0.09140337,-0.00452477,0.05621419,-0.01420064,0.01982126,-0.01989578,0.01332864,-0.0126698,-0.02897813,0.00174909,-0.04248482,-0.01164702,-0.03501324,0.09411386,0.08921003,-0.02812205,-0.00667836,-0.06691055,-0.04671649,-0.04974697,0.10437248,0.01584069,-0.07193821,-0.02576562,0.01644579,-0.01560332,-0.03521553,0.0009503,-0.01215064,-0.07120516,-0.0113836,0.03412851,-0.07856915,-0.06719324,-0.02267298,0.00251886,0.01352636,0.02890273,-0.01489956,-0.04900489,0.02846617,0.05653822,-0.04439764,-0.00994366,-0.03742646,0.0636062,-0.02413142,-0.10694883,-0.03478448,-0.07280312,0.04978945,-0.00344969,-0.00022759,0.00765318,-0.03359738,0.06084412,-0.06427644,0.00644169,-0.04031282,-0.01739286,0.01202644,-0.04917385,0.06358411,-0.04478107,-0.11094771,0.04157099,0.02587546,0.02261572,0.01740724,0.07121929,0.04449887,0.00499883,0.02580452,-0.01110845,0.07026784,-0.04486515,0.04367324,0.02163618,0.04616943,-0.01292095,-0.21359016,-0.03564211,-0.02189726,0.00523194,0.05329929,-0.00664064,0.00322795,0.02994771,0.07558907,0.0200317,0.03913067,-0.0145361,-0.00297597,0.01452967,0.01217215,0.04211879,-0.07560021,-0.04649943,-0.02332866,0.03576957,-0.01870645,-0.00396772,-0.04578076,-0.03867814,0.05503728,-0.0132341,0.15436007,0.04906616,0.03731912,0.03988913,0.04337123,0.04352611,0.01083662,-0.08333552,0.05635567,0.02027526,0.02328132,-0.00546828,-0.01351066,-0.03249722,-0.05341991,-0.00572545,0.0181262,-0.12126171,-0.01310506,-0.04065246,0.01838452,0.04016491,-0.00985446,0.04529928,0.06361167,-0.01195859,0.02724802,0.01141052,0.08087489,-0.00336435,-0.10548989,0.0074805,0.02219305,0.02719619,-0.04189673,0.01352556,0.0350224,-0.02946912,0.08799466,0.01536688,0.0050931,0.00348196,0.01600279,-0.04559732,-0.00394663,0.05777764,0.02508152,0.00683594,0.0411174,0.03257028,0.0738621,-0.10292033,0.02781511,-0.00372197,-0.03215928,-0.0776982,0.06695743,0.05672419,0.05312793,0.00599602,0.06089883,0.01035598,0.07712959,0.00645774,-0.00025599,0.03653034,-0.05066757,-0.03046244,0.03050448,0.08573555,-0.24019298,-0.00820455,0.01945589,0.06601124,-0.04842048,-0.00433087,0.04395939,-0.04160791,-0.01084521,0.07612374,-0.00282814,0.04937851,0.06329378,-0.01109866,0.01765397,-0.03441931,-0.02087009,-0.03962543,0.08569654,0.06584947,-0.00194578,0.05747841,0.18815021,-0.03108145,0.02327012,0.00752674,-0.00435619,-0.00279834,0.0149666,0.02898199,0.01294989,0.00954739,0.00994116,-0.00221348,0.00261149,0.04768726,-0.00021613,-0.01996541,0.031941,0.03947079,-0.03000786,-0.00242452,-0.00153999,-0.00874917,0.1025518,-0.02341992,-0.02183487,-0.08027447,0.00525136,-0.00235125,-0.02493051,-0.08408844,0.02320852,-0.02488341,0.04103879,0.08881003,0.0061659,-0.01546704,0.01666882,-0.03767951,-0.00631591,-0.00402564,0.06232236,0.01339751,0.01964616],"last_embed":{"hash":"1pgv1wn","tokens":87}}},"text":null,"length":0,"last_read":{"hash":"1pgv1wn","at":1753423434936},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{5}","lines":[61,62],"size":214,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10617764,-0.00615574,-0.05429429,0.00733619,-0.02276281,0.04288468,0.06084407,-0.0115226,0.0499374,-0.03374688,-0.000218,0.04133027,-0.00288036,-0.00134261,-0.01827141,-0.04268467,0.01989204,0.00275473,-0.00680663,0.04910987,-0.00092388,-0.03452182,-0.07941295,-0.06172834,0.01998913,-0.04572605,-0.03766873,-0.04982254,-0.06624655,-0.24242912,0.00247175,-0.02470043,0.04254166,-0.0632259,-0.07093774,-0.05079696,-0.03796785,0.0540185,-0.02188686,0.02437906,0.03441375,0.03748075,0.00137035,0.04164055,-0.00300582,-0.03226282,0.02169762,0.0614522,0.03848953,0.00890634,-0.04470365,0.0060893,-0.03867825,-0.02943458,0.06718889,0.00657799,0.0855299,0.0832831,-0.03803061,0.04125404,0.02850331,0.04486367,-0.17372626,0.06107977,-0.01563839,0.03408334,-0.03435497,-0.01537739,0.05547777,0.0820933,0.02139495,0.03995162,-0.03039601,0.05802227,0.02985907,-0.02147986,-0.00814008,-0.08150002,0.01621673,0.01511822,0.00892504,-0.09273838,0.02203416,-0.05422892,0.00728284,0.00909874,-0.01529855,0.03246154,0.06730847,-0.08010329,0.05627277,0.05352285,0.01977365,-0.03728957,0.01810463,-0.05104835,0.04731858,-0.0239125,-0.01748246,0.11398751,0.01776509,-0.03133651,-0.02994444,0.00848486,0.04107869,-0.00756666,0.00170903,-0.01178482,-0.03897146,0.01007789,0.05273545,0.00524178,0.08883626,0.01066115,-0.06079833,0.02939773,0.008809,-0.00595682,0.05457264,0.03033645,0.0102937,0.06917644,0.06387842,-0.02331843,-0.04548809,-0.05066755,0.02450836,0.0433092,-0.00335079,0.00059612,0.03132894,-0.08545046,-0.06656488,-0.07966941,-0.00907713,-0.03766225,0.04191907,0.05426805,0.00794117,0.02535036,-0.05146191,0.00807058,0.06747444,-0.11784434,-0.00858903,0.03424655,-0.00473584,0.00305428,-0.00414266,0.00167694,0.00542634,-0.04558147,0.00750379,-0.02847155,-0.02787856,-0.02864991,0.12224007,0.08574207,-0.03773374,-0.01102225,-0.08251744,-0.0662571,-0.033698,0.088048,0.02843843,-0.07064676,-0.04083305,-0.00396226,-0.04759483,-0.07202575,-0.01547248,-0.04282367,-0.05297137,-0.01930488,0.01683891,-0.04054185,-0.10266588,-0.05014665,0.01034332,0.03154935,0.01343851,-0.01459788,-0.05051353,0.00228823,0.02075381,-0.05116085,-0.00291563,-0.04191738,0.04639793,-0.0292884,-0.06753749,-0.02167419,-0.07165401,0.03154412,-0.02285268,-0.00171042,0.00444603,-0.05825801,0.0807392,-0.04307178,-0.03927623,-0.00934216,-0.0183716,0.00388284,-0.01685033,0.04592275,-0.02309318,-0.11928512,0.03712878,0.00605686,0.02452195,0.0030283,0.0750723,0.029034,0.00257312,0.0481537,-0.01007345,0.04825164,-0.02401901,0.06028851,0.00851766,0.03899992,-0.00789082,-0.21871059,-0.03665832,-0.02849619,-0.03069966,0.03757776,-0.01403898,-0.02240236,-0.01795081,0.07145408,0.03707439,0.05366604,-0.05238002,0.00663021,0.04367295,-0.01823272,0.03190769,-0.094553,-0.00412337,-0.04373543,0.09099837,-0.02178362,0.02611506,-0.01016581,-0.06980218,0.06133971,-0.03088279,0.1452399,0.04991777,0.03108165,0.01600127,0.03088358,0.07428578,-0.00271087,-0.08833098,0.04819981,0.01518437,0.02735575,-0.05336971,-0.02296997,-0.006187,-0.09325462,0.03092356,0.01951293,-0.13401623,-0.05696561,-0.0214058,0.00360429,0.03907227,-0.02814701,0.02767857,0.06350772,-0.02279767,0.02507093,0.0148005,0.05656292,-0.03113632,-0.05891054,-0.01386428,0.0323121,0.03336666,-0.02849984,-0.01112886,0.05823851,-0.02464567,0.09095609,0.00853896,0.0233925,-0.02208428,0.04719937,-0.03662387,-0.00069388,0.06923842,0.04440017,0.05672903,0.06922312,0.02435081,0.07584604,-0.08865693,0.02946621,0.02235387,-0.02295162,-0.08340225,0.04277646,0.08406886,0.04287029,-0.01768628,0.05802261,-0.00308571,0.06353105,-0.03395918,-0.01950436,0.04589838,-0.05007304,-0.00828024,-0.00274848,0.0752342,-0.2527113,0.00475658,0.01471903,0.04021835,-0.02275461,-0.04637613,0.05093996,-0.00947525,-0.00143764,0.05261769,0.00495402,0.03471949,0.04832749,0.0211839,0.01074758,-0.04965318,0.00905722,-0.01950587,0.08853143,0.03979319,0.00786445,0.09232478,0.20135154,-0.00487257,0.03097246,-0.0005504,-0.00640559,0.0059822,0.00624834,0.06311774,0.00013186,0.03129793,0.0234953,-0.01828128,0.01138597,0.05604646,-0.01815221,-0.02600582,0.00130861,0.01819102,-0.03704724,-0.01277713,0.02167319,0.01160545,0.10761699,-0.01360589,-0.0244376,-0.04290136,0.02372799,-0.00343931,-0.03248047,-0.04914075,0.03168871,-0.03019062,0.06323814,0.05958976,-0.01443539,-0.01935498,0.02299673,-0.03750819,0.01046745,-0.02316737,0.05565259,0.00328794,0.01360401],"last_embed":{"hash":"1vtw78k","tokens":365}}},"text":null,"length":0,"last_read":{"hash":"1vtw78k","at":1753423434965},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#{6}","lines":[63,72],"size":1311,"outlinks":[{"title":"The mental blackjack gambling system beat the casino and house advantage HA big time.","target":"https://saliu.com/images/blackjack-winning-system.gif","line":1},{"title":"_**Blackjack Probability, Odds Natural, Insurance, Double Down**_","target":"https://forums.saliu.com/blackjack-natural-odds-probability.html#double-down","line":3},{"title":"Gambling streaks strategies beat unbiased blackjack app to win 20000 dollars in one week.","target":"https://saliu.com/images/blackjack-winning-strategy.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Consistency": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08841433,0.0078973,-0.06802643,0.0096368,-0.02361095,0.01899213,0.05274907,0.00142734,0.04759762,-0.02374491,0.00609355,-0.00026032,0.00250501,-0.00680425,-0.02914792,-0.04776531,0.04545498,-0.01809121,-0.01710229,0.00841577,0.00265423,-0.03836655,-0.09508839,-0.06817155,0.04781189,0.00709357,-0.03073652,-0.03010944,-0.05079952,-0.28167388,-0.00279242,-0.05181472,0.01704262,-0.04669034,-0.05204421,-0.0304873,-0.03962065,0.04847944,0.0164276,0.0276319,0.01633118,0.08703373,-0.00903522,-0.00178105,0.00606913,-0.02974792,0.0066693,0.02953283,0.05922802,0.00942676,-0.0489218,-0.02300894,-0.03573667,-0.01292007,0.07724175,0.01898805,0.08722115,0.08863866,-0.03308001,0.006122,0.00942135,0.05491661,-0.18892835,0.0759073,-0.00689581,0.0456772,-0.00949904,-0.043838,-0.00126881,0.06510869,0.028241,0.0456608,-0.029109,0.04657653,0.05829523,-0.00710839,0.01173673,-0.05006293,0.00832678,0.02212306,0.0415484,-0.04886991,0.01284394,-0.01340685,0.00082401,-0.01803502,-0.01914989,0.03056465,0.05837543,-0.07611088,0.04646991,0.05422213,0.04622064,-0.03940639,-0.00218809,-0.01518805,0.0099679,-0.01462226,-0.04618581,0.10761123,0.00774478,-0.0326253,-0.0336941,0.00152971,0.01944654,0.01970625,0.00730772,-0.01408491,-0.05983527,0.02824074,0.03457825,-0.00895102,0.0878772,-0.03194222,-0.06505332,0.0422378,-0.0057069,0.00176697,0.02409269,0.00729224,0.00357609,0.09476718,0.03312488,-0.01991735,-0.02019979,-0.05249505,0.0417616,0.04170745,-0.00156897,-0.00103157,-0.00663814,-0.06377275,-0.0459142,-0.07947126,-0.05833464,-0.02910845,0.05234118,0.01713402,0.02096596,0.03803138,-0.08969864,-0.00378222,0.0652228,-0.09789702,-0.02532613,0.0407692,-0.0314873,0.04060371,0.00251191,0.05198246,-0.02559479,-0.05708903,0.01751139,-0.02782186,-0.01924779,-0.01218895,0.09468587,0.06893586,-0.03488588,-0.02301086,-0.06235305,-0.05173974,-0.03402369,0.09059928,-0.00403655,-0.09479389,-0.04106796,0.02659009,-0.02127812,-0.02983985,0.01255148,-0.01744737,-0.04838056,-0.02583906,0.03282712,-0.07421792,-0.06881989,0.00522516,-0.01090437,0.04467111,0.03768282,-0.02095947,-0.08727888,0.01807241,0.02275866,-0.04581806,-0.02066551,-0.04261982,0.08912867,-0.0041469,-0.07522634,-0.0022946,-0.06751376,0.08421595,-0.01893019,-0.00526183,0.01001007,-0.0781112,0.0990093,-0.03415995,-0.01612842,-0.01488771,-0.00300332,-0.02188225,-0.01947577,0.0644396,0.0039773,-0.0776469,0.041141,0.03148189,0.00648624,0.02624516,0.06144733,0.05607589,-0.02137556,0.05269397,0.00400625,0.06931338,-0.07410374,0.08880825,0.01007017,0.03824073,-0.00800772,-0.20093426,-0.01846461,-0.0205404,-0.03429843,0.06372362,-0.02031712,0.02082413,0.01054932,0.07276727,0.04094838,0.02990917,-0.00006802,-0.00512906,0.0310449,-0.00827849,0.03552097,-0.07073792,-0.01237398,0.00384564,0.05174555,-0.05255548,-0.02337787,-0.03019989,-0.0484608,0.06879391,-0.03484047,0.15063626,0.03189092,0.04080872,0.03213618,0.00922009,0.03717401,0.01668853,-0.11389589,0.03788726,0.0329166,-0.00253946,-0.00412538,-0.02063536,-0.04448719,-0.04424057,0.01003357,0.01515024,-0.13854758,-0.03467876,0.0083376,-0.00119225,0.01333421,-0.05746506,0.00414267,0.05777199,-0.07882776,0.00645456,-0.0078196,0.08112966,-0.02131116,-0.08336207,-0.0109142,0.02225186,0.05401675,-0.04263682,0.02712721,0.0168642,-0.0148883,0.08708492,0.03110933,0.02621942,-0.02546624,0.01816411,-0.05661329,-0.03381728,0.03493856,0.04004447,0.02771705,0.05390988,0.02463668,0.08104586,-0.06013675,0.04953765,0.03079062,-0.03247317,-0.04916504,0.09778684,0.03901899,0.09055652,-0.00676027,0.05433369,-0.01046146,0.0973715,-0.01066706,0.00484359,0.0556113,-0.07085739,-0.03112358,0.00306268,0.06192654,-0.24982916,0.01970234,-0.00825817,0.07012012,-0.03990858,-0.03952768,0.04996848,-0.06202526,-0.02798298,0.0504111,0.01870884,0.04595101,0.05889477,-0.02924568,-0.00572832,-0.01189652,-0.00409878,-0.04054683,0.07414734,0.02120121,0.00363476,0.06507684,0.19127791,-0.03812552,0.01291119,-0.01904893,-0.01001544,0.04758061,0.01344801,0.04190129,-0.00946603,0.03460607,-0.02607175,0.02138963,-0.0163516,0.04881314,-0.02715746,0.01525991,0.01465176,0.04061747,-0.03262341,0.01338643,0.02372718,-0.02506847,0.09956438,-0.0086864,-0.02146393,-0.07083416,0.00820207,0.00094513,-0.058355,-0.06901336,0.0456105,-0.01356048,0.05266261,0.06927639,-0.00574154,-0.02277553,0.01614743,-0.02633713,0.006319,0.0025826,0.03766654,-0.01548798,0.00975223],"last_embed":{"hash":"g9om5y","tokens":250}}},"text":null,"length":0,"last_read":{"hash":"g9om5y","at":1753423435089},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Consistency","lines":[73,82],"size":862,"outlinks":[{"title":"Mental blackjack strategy system wins consistently thousands of dollars a day in heads-up play.","target":"https://saliu.com/images/blackjack-winning-consistency.gif","line":5},{"title":"The best and free blackjack strategy beats the casino dealer with no doubt.","target":"https://saliu.com/images/blackjack-highroller-strategy.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Consistency#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08609255,0.0084913,-0.06881995,0.01032303,-0.02230621,0.01991693,0.05179974,-0.00154322,0.05205204,-0.02412666,0.00562808,0.00317662,0.00124865,-0.00748474,-0.02962467,-0.04923211,0.04613584,-0.01768361,-0.01819426,0.00881699,-0.00089251,-0.0410373,-0.09651525,-0.06828176,0.04770563,0.00999243,-0.02871224,-0.03031192,-0.04989367,-0.2807928,-0.00138962,-0.05273384,0.0146934,-0.04926322,-0.05091171,-0.03200482,-0.03998496,0.05016317,0.01610806,0.02884875,0.01583725,0.08685321,-0.00830302,-0.00179548,0.00716079,-0.02958351,0.00359705,0.02923073,0.06172174,0.0096448,-0.04918746,-0.02105192,-0.03589541,-0.01167237,0.07593107,0.01931322,0.08807461,0.09085898,-0.03156586,0.00470315,0.00883411,0.05604158,-0.18963103,0.07597538,-0.00663695,0.04653828,-0.01083695,-0.04515699,-0.00193183,0.06751052,0.02598312,0.04425458,-0.02982297,0.04821368,0.05972256,-0.00842014,0.01294664,-0.04884322,0.00640635,0.02576389,0.03931284,-0.04873458,0.01466192,-0.01203863,0.00209231,-0.01653426,-0.01635527,0.03066341,0.05591339,-0.07462167,0.04726207,0.05346733,0.04614927,-0.03983561,-0.00260973,-0.01453883,0.01062674,-0.01459032,-0.04489174,0.10732723,0.00644619,-0.0311754,-0.03541503,0.00314354,0.01902096,0.01990523,0.00403638,-0.01357075,-0.06258772,0.02857082,0.03304001,-0.00761246,0.08488671,-0.03099168,-0.06647477,0.04601882,-0.00695702,0.00191389,0.02390517,0.00804712,0.00195274,0.0950966,0.03291704,-0.01833757,-0.02123351,-0.05300805,0.0436984,0.04226435,-0.00347494,-0.00146837,-0.00812394,-0.06697512,-0.04559418,-0.07944158,-0.05724116,-0.03086744,0.05163536,0.01789071,0.02158019,0.03878474,-0.09171999,-0.00296037,0.06472204,-0.09532931,-0.02452995,0.04192159,-0.02981962,0.04038117,0.00322918,0.05102019,-0.02483093,-0.05848628,0.01633711,-0.02963228,-0.0209682,-0.01289555,0.09103856,0.0682569,-0.03505523,-0.02429272,-0.0629769,-0.05338367,-0.03018837,0.09078947,-0.00528328,-0.09034447,-0.04132017,0.02691752,-0.02080748,-0.02869877,0.01411091,-0.01752625,-0.04731301,-0.02738653,0.03513847,-0.07502577,-0.0694162,0.00498155,-0.0088908,0.04265344,0.03857498,-0.02006807,-0.08783889,0.02159351,0.0214753,-0.0456275,-0.01963975,-0.04100756,0.08821004,-0.0011547,-0.07491047,0.00138295,-0.06804194,0.08564959,-0.01930541,-0.00606903,0.01053849,-0.08096973,0.10117728,-0.03413507,-0.01937915,-0.01445001,-0.00563015,-0.02455392,-0.01872369,0.06540132,0.00305924,-0.07461851,0.04259576,0.03170029,0.00734548,0.02552568,0.05952895,0.05426945,-0.01896265,0.05322221,0.00586253,0.06661163,-0.07284114,0.08760349,0.00625076,0.03697801,-0.00457548,-0.19968204,-0.02042469,-0.02046711,-0.03407852,0.06568396,-0.02100723,0.01873208,0.00926769,0.06981251,0.04236392,0.02707382,-0.00097868,-0.00581674,0.03110729,-0.00842908,0.03753509,-0.06721934,-0.01090341,0.00271428,0.05460654,-0.05596596,-0.02489873,-0.02990584,-0.04751799,0.07077184,-0.03330627,0.1515912,0.03155724,0.039271,0.03088868,0.00669069,0.03577624,0.01801774,-0.11349563,0.03801902,0.0336817,-0.00179444,-0.00544451,-0.02222236,-0.04383306,-0.04589221,0.01147754,0.01717125,-0.1376716,-0.03332661,0.01044468,0.00083094,0.00968089,-0.05925841,0.00556244,0.05972082,-0.07882769,0.00552752,-0.00966228,0.08144893,-0.0212375,-0.08164056,-0.00918091,0.02338456,0.05248442,-0.04217566,0.02692479,0.0156422,-0.01580286,0.08822024,0.03306157,0.02577185,-0.0275313,0.01737575,-0.05691698,-0.03718354,0.03284141,0.03766635,0.02494097,0.05367075,0.02444069,0.08092878,-0.05839682,0.04841924,0.03425565,-0.03175534,-0.05351633,0.09971698,0.04152252,0.09054366,-0.00846876,0.05324358,-0.01066245,0.09689511,-0.00940695,0.00642231,0.05783593,-0.07095348,-0.02945855,0.00187734,0.06062236,-0.25051942,0.02228213,-0.01316243,0.07172866,-0.04043284,-0.04181146,0.05233619,-0.06170861,-0.02985517,0.04949779,0.02060593,0.04784131,0.05934454,-0.02780059,-0.00632233,-0.01182209,-0.00413645,-0.0414759,0.07146671,0.02092055,0.00756967,0.06416831,0.19123366,-0.04058819,0.01305832,-0.01836778,-0.00787552,0.04651944,0.01204013,0.04092367,-0.00880667,0.03412277,-0.02549467,0.0205922,-0.01625329,0.04930059,-0.02713614,0.01704215,0.01367727,0.03917912,-0.03100167,0.01078164,0.0243738,-0.02342392,0.09932613,-0.00682165,-0.02144977,-0.07033139,0.00999064,-0.00019869,-0.05837593,-0.06756819,0.04300581,-0.01490496,0.04901727,0.06860161,-0.00537089,-0.02149673,0.0187239,-0.02489885,0.00403152,0.00160244,0.03502739,-0.01748858,0.00787359],"last_embed":{"hash":"mumc2j","tokens":248}}},"text":null,"length":0,"last_read":{"hash":"mumc2j","at":1753423435160},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Consistency#{1}","lines":[75,82],"size":845,"outlinks":[{"title":"Mental blackjack strategy system wins consistently thousands of dollars a day in heads-up play.","target":"https://saliu.com/images/blackjack-winning-consistency.gif","line":3},{"title":"The best and free blackjack strategy beats the casino dealer with no doubt.","target":"https://saliu.com/images/blackjack-highroller-strategy.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10464257,-0.01200766,-0.08226062,0.0062833,-0.03755505,0.01933199,0.06873773,-0.00051436,0.05630736,-0.02952351,-0.00544272,0.01562529,-0.00735588,0.01781712,-0.02994557,-0.051642,0.03880477,0.01339543,-0.00177579,0.02178091,-0.00188978,-0.06803785,-0.06761385,-0.06714395,0.01756781,-0.03947577,-0.02470411,-0.03265475,-0.07579979,-0.23297825,-0.02179076,-0.05571514,0.05139171,-0.04666714,-0.07921335,-0.03742065,-0.03436965,0.04944317,-0.0211151,0.02394181,0.03785683,0.04254002,-0.00059971,0.03018377,0.00515387,-0.03538935,0.02460848,0.0426925,0.04655797,-0.00133318,-0.03524978,-0.00184666,-0.04379381,-0.0440442,0.06527032,0.00544374,0.0845671,0.09740357,-0.03652856,0.0205187,0.02045609,0.03131287,-0.18233797,0.06038829,-0.02359729,0.03438853,-0.03006583,-0.00584441,0.01909929,0.0653945,0.01482473,0.05099147,-0.05452075,0.06161853,0.03136695,-0.02477895,0.02409768,-0.03344339,0.02844456,-0.0011157,0.02832815,-0.08217394,0.00238792,-0.03642238,0.01154781,-0.00071311,-0.00966062,0.0052924,0.04956565,-0.05237121,0.03431331,0.05733842,0.06511506,-0.04104918,0.02194851,-0.05199638,0.01182291,-0.03114837,-0.0351276,0.08951699,0.03833221,-0.01976082,-0.03721609,0.01627484,0.04613336,0.00387525,0.00833804,-0.02367124,-0.04000482,0.02300914,0.04317661,0.00080803,0.05881735,0.00498017,-0.06202161,0.0308419,0.01290255,0.02183761,0.02221413,0.00790073,-0.01189996,0.09048974,0.04967361,0.01065386,-0.02845546,-0.04201727,0.02887322,0.04526328,0.01203925,-0.00634306,-0.0104853,-0.08155908,-0.07449387,-0.07477699,-0.04898097,-0.03812348,0.02624965,0.03637291,0.02529984,0.04687311,-0.07306993,-0.00241838,0.05937374,-0.09163225,0.00486642,0.04884667,-0.00612776,0.0082323,-0.00430151,-0.00340265,0.00700055,-0.03017579,-0.02091089,-0.03635161,-0.05613212,-0.0230604,0.06685324,0.0983948,-0.0418658,-0.0041933,-0.05262608,-0.06584811,-0.02510515,0.09440961,0.01729793,-0.07707975,-0.04546907,0.01282254,-0.03241089,-0.06752825,-0.01662053,-0.0538133,-0.05944955,-0.02596553,0.02314992,-0.05307367,-0.08569856,-0.04714064,-0.01197665,0.02669922,-0.00415696,-0.02380083,-0.04445901,-0.00731325,0.028376,-0.06660211,0.01515327,-0.04660871,0.07951312,-0.0396641,-0.07237142,-0.01282753,-0.09303457,0.0322851,-0.01749419,-0.02221001,-0.00194464,-0.05579031,0.0486106,-0.03596748,-0.04428121,-0.01473675,-0.0077489,0.01032908,-0.03139633,0.08324398,-0.02302638,-0.10984118,0.04625576,-0.00509101,0.0222486,-0.00679914,0.03087153,0.04720207,-0.01091267,0.0376835,0.0085734,0.05439417,-0.03863892,0.06956266,0.00761255,0.08127056,0.00661004,-0.21224366,-0.02887151,-0.05136952,-0.00436929,0.03830017,-0.0294583,0.01016128,-0.01660973,0.06480791,0.03661149,0.06949423,-0.06250817,0.0298039,0.04434881,-0.00950778,0.04063531,-0.09080114,-0.03928118,-0.03836099,0.05467242,-0.00788872,0.01438231,-0.02369572,-0.05452534,0.0521558,-0.0060979,0.17252807,0.07656384,0.04114343,0.03814495,0.04777378,0.05559382,0.00141481,-0.1223082,0.04243516,0.00365236,0.04259508,-0.02439592,-0.01623658,-0.011064,-0.07677812,0.01871755,0.01934842,-0.13444777,-0.04418618,-0.00085052,0.00533292,0.06714629,-0.02894235,0.02606279,0.09295499,-0.01390458,0.04383195,0.01341045,0.08095264,-0.02216495,-0.08461099,0.01074802,0.02343163,0.05375607,-0.0568927,-0.00189171,0.0347406,-0.03987956,0.08995182,0.02351318,0.02148711,-0.0000276,0.08546308,-0.02682669,-0.00916268,0.07751296,0.02711011,0.0354539,0.07334911,0.01584028,0.05485702,-0.08507027,0.02750667,-0.00253137,-0.02225663,-0.10211658,0.04784248,0.04097175,0.03768066,0.01972491,0.04366828,0.01686035,0.06471734,-0.00548083,-0.02931263,0.036832,-0.05817262,-0.02601086,0.01272887,0.07418498,-0.24649344,0.00124721,-0.01380508,0.07024052,-0.00433666,-0.04683954,0.06759764,-0.04922775,-0.0137971,0.05286399,0.02218534,0.04582087,0.0310449,0.01697895,-0.01739719,-0.03624256,0.00203613,-0.03582797,0.08771451,0.02662497,0.02522221,0.0688602,0.21299662,-0.03338318,0.026288,0.02158915,-0.00573463,-0.01313999,-0.01799826,0.04190146,0.00792323,0.03228169,0.02406633,0.01413574,0.01663163,0.02880956,-0.02198654,0.00373218,0.01385282,-0.00553763,-0.03832117,0.00318743,0.03539051,0.02599477,0.10865874,0.0057571,-0.02099871,-0.04031324,0.04011792,0.00999878,-0.03126144,-0.0420909,0.02287393,-0.04114132,0.0361595,0.05562193,-0.00617451,-0.02400487,0.03627704,-0.03058907,-0.00060155,0.04073883,0.05626924,0.01783716,0.00525219],"last_embed":{"hash":"15qvdfo","tokens":446}}},"text":null,"length":0,"last_read":{"hash":"15qvdfo","at":1753423435222},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict","lines":[83,105],"size":3035,"outlinks":[{"title":"_**Fearing Losses, Casinos Bar, Ban Winning Gamblers, Skilled Gambling Players**_","target":"https://saliu.com/winning.html","line":7},{"title":"How other players apply Ion Saliu's blackjack system based on gambling streaks.","target":"https://saliu.com/HLINE.gif","line":9},{"title":"There is a good and fair blackjack online app at Wizard of Odds site.","target":"https://saliu.com/images/WizardofOddsBlackjack.jpg","line":11},{"title":"_**Card Counting Quirks?**_","target":"https://wizardofvegas.com/forum/gambling/blackjack/39431-card-counting-quirks/2/#post934618","line":17},{"title":"Players can really win at blackjack in negative counts because of more double-downs.","target":"https://saliu.com/HLINE.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10213192,-0.01017272,-0.08409585,0.00610113,-0.03682775,0.01828183,0.06922681,-0.00107124,0.06099828,-0.03292397,-0.00861421,0.02193367,-0.0100762,0.02005339,-0.02763757,-0.0523412,0.03985843,0.00715721,-0.00038366,0.02577836,0.00264837,-0.07240389,-0.06877332,-0.06826384,0.02211795,-0.04215097,-0.02511759,-0.03227416,-0.07177684,-0.23199238,-0.0160288,-0.0561572,0.05364765,-0.04777532,-0.08427567,-0.03582177,-0.03715609,0.04767148,-0.02004498,0.02364532,0.03763273,0.05066773,0.00215336,0.029764,0.00287827,-0.03372718,0.02116743,0.04319624,0.04831755,0.00280725,-0.03685651,-0.00541753,-0.04501937,-0.04231403,0.06129711,-0.00321111,0.08326744,0.10172538,-0.03858054,0.01958828,0.01317096,0.03240624,-0.17537439,0.05468074,-0.02663718,0.02923532,-0.02545964,-0.00581916,0.01687036,0.06767009,0.01882712,0.05102177,-0.04656573,0.05446152,0.03091446,-0.03108801,0.01772808,-0.030169,0.02799641,0.00609284,0.02816454,-0.08370293,0.00553419,-0.03669738,0.01552854,-0.00359772,-0.00835148,0.00933506,0.05159456,-0.05415389,0.03946817,0.05921841,0.05824112,-0.04223458,0.01801343,-0.04335077,0.01150294,-0.0328089,-0.03786705,0.08853763,0.0428274,-0.02280812,-0.03886674,0.01202901,0.04881018,0.00334739,0.0012209,-0.02319474,-0.0431159,0.02417808,0.0375425,0.00242137,0.06421186,0.00207346,-0.06617243,0.03741106,0.01345808,0.0235236,0.02482071,0.00911782,-0.01343825,0.08604917,0.04602169,0.0142891,-0.03594875,-0.04201677,0.03140587,0.04902966,0.01264598,0.00461583,-0.00956299,-0.08622941,-0.08039116,-0.07131782,-0.04357088,-0.04299398,0.02566664,0.0417578,0.02611622,0.04415289,-0.07201197,-0.00188888,0.05975113,-0.09331264,0.0007966,0.0488046,-0.00310071,0.01042062,0.00057719,-0.00947717,0.00279322,-0.02813952,-0.02341818,-0.04130018,-0.05557534,-0.0236108,0.07115948,0.09650881,-0.04688276,-0.00490908,-0.05577601,-0.06571892,-0.02266674,0.09369627,0.01826739,-0.07884338,-0.04521133,0.00791268,-0.03812899,-0.0662953,-0.01785175,-0.05147265,-0.05771015,-0.0254832,0.02785166,-0.05219399,-0.0855217,-0.04602369,-0.01167169,0.02628822,-0.00485839,-0.02262321,-0.04384463,-0.00685441,0.02420557,-0.06428709,0.01361488,-0.0439056,0.08213975,-0.03734856,-0.07515097,-0.01608796,-0.09374108,0.03980739,-0.01579058,-0.02121611,0.0000187,-0.05743988,0.04848268,-0.03662965,-0.03879485,-0.01911838,-0.01384887,0.0079196,-0.02642869,0.08056562,-0.02515729,-0.10454498,0.04631038,-0.00591346,0.02556729,-0.00869152,0.03528109,0.04896219,-0.01016347,0.04045204,0.00792344,0.05536623,-0.03172373,0.07098656,0.00912295,0.07141163,0.01329371,-0.21125296,-0.02614311,-0.05126997,-0.00275919,0.04524343,-0.02779952,0.0054661,-0.02091977,0.0612618,0.03579772,0.06781084,-0.06899864,0.02928107,0.0440648,-0.00584539,0.04444779,-0.0841276,-0.03282556,-0.03396885,0.04875991,-0.00708387,0.01362815,-0.02071911,-0.05331786,0.05443857,-0.00429817,0.17391552,0.07101661,0.03603813,0.03554493,0.04966496,0.05315261,-0.00059301,-0.12265349,0.03407221,0.00370966,0.0471475,-0.01834952,-0.01764296,-0.0084271,-0.07746254,0.01894794,0.02458501,-0.13438259,-0.03944823,0.00749024,0.00151755,0.06970964,-0.03106947,0.02494198,0.09744055,-0.01510002,0.04522151,0.01178641,0.08208824,-0.02751153,-0.08433635,0.00771792,0.02165242,0.04851664,-0.05679837,-0.01046997,0.04138702,-0.03785113,0.09076265,0.02395129,0.02589608,0.00468618,0.08138661,-0.02691627,-0.00909264,0.07458973,0.03265927,0.03857236,0.07612102,0.01351326,0.06020081,-0.0897247,0.03095468,-0.00000275,-0.02095174,-0.09696397,0.04581793,0.04586786,0.03789996,0.02043965,0.05169937,0.01572218,0.05706333,-0.00390017,-0.03361946,0.03342697,-0.05940052,-0.01980486,0.0069175,0.07176314,-0.25107718,0.00443488,-0.02094261,0.06957287,-0.00231924,-0.04821702,0.06845373,-0.0501257,-0.01530037,0.04934825,0.0233924,0.05112187,0.02816914,0.01473899,-0.00599898,-0.04189035,-0.00435355,-0.03400222,0.08445882,0.02858164,0.02163452,0.07020808,0.20717098,-0.03154077,0.02547381,0.0202922,-0.00842653,-0.01451912,-0.0073894,0.037754,0.00053055,0.02907729,0.02992539,0.0118942,0.01635035,0.02370755,-0.02391828,0.00380581,0.01045124,-0.00293585,-0.03544327,-0.00038798,0.03927204,0.02558441,0.11413274,0.00759509,-0.02309376,-0.04875425,0.03942554,0.00778185,-0.02908582,-0.04052668,0.02907812,-0.04286208,0.0333358,0.0586445,-0.00810558,-0.0230942,0.04190333,-0.03346327,-0.00373763,0.03824401,0.05421605,0.01424966,0.00127701],"last_embed":{"hash":"1pz7yz8","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"1pz7yz8","at":1753423435370},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict#{1}","lines":[85,98],"size":2060,"outlinks":[{"title":"_**Fearing Losses, Casinos Bar, Ban Winning Gamblers, Skilled Gambling Players**_","target":"https://saliu.com/winning.html","line":5},{"title":"How other players apply Ion Saliu's blackjack system based on gambling streaks.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"There is a good and fair blackjack online app at Wizard of Odds site.","target":"https://saliu.com/images/WizardofOddsBlackjack.jpg","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08154001,-0.02531097,-0.07161089,-0.00264843,-0.07182077,0.02920377,0.05423903,-0.0031136,0.09044884,-0.00953287,0.01850954,0.01677987,0.03473112,-0.0149897,-0.05488747,-0.03559482,0.03240644,-0.01734975,-0.04174734,0.03946641,0.02401733,-0.05574476,-0.07009346,-0.03674113,0.03362905,-0.01000767,-0.0332298,-0.05523203,-0.03664659,-0.22698736,-0.01246306,-0.06141826,0.10764335,-0.06328347,-0.04543955,-0.05133045,-0.01436673,0.03956727,-0.00656824,0.0294964,0.03031935,0.08739712,0.00537588,0.02720205,-0.02458824,-0.0512171,-0.0080377,0.05296874,0.02471731,-0.00135219,-0.04704064,-0.01350873,-0.0568865,-0.00721745,0.07346571,-0.02268934,0.0550037,0.0641728,-0.02712923,0.0083103,0.01111213,0.03445332,-0.19099213,0.0253459,-0.01841717,0.01306894,-0.0189369,0.00646321,0.00157236,0.09770374,0.05852393,0.0145471,-0.02307233,0.04691461,0.02037389,-0.00702295,0.00033708,-0.02759199,0.01116364,0.05063194,0.03445292,-0.10376514,-0.00387833,-0.05002499,0.00460644,-0.03413499,0.01027931,0.0348147,0.08359141,-0.06471786,0.07804509,0.05445008,-0.01167701,-0.02769335,0.0340295,-0.03667279,0.01114908,-0.02861163,-0.05476466,0.11221306,-0.00345516,-0.00442638,-0.06701763,-0.00265373,0.04058249,0.00287324,0.01682003,-0.00228262,-0.03349751,0.00014437,0.07843109,0.00430201,0.05765523,-0.00317318,-0.02228755,0.0203946,0.03835503,0.0069586,0.00016283,0.00079649,0.00929028,0.07496015,0.073262,-0.02282364,-0.02447328,-0.00913312,0.05073398,0.07954077,-0.00581077,0.01044988,-0.01267351,-0.0588759,-0.11488555,-0.0604797,-0.03606148,-0.03521821,0.08974119,0.04585038,-0.0032845,0.02934817,-0.03460505,0.02064572,0.06882504,-0.12540418,-0.0201879,0.05522771,0.00677267,0.0281317,-0.00733747,-0.01901841,0.00344469,0.01539644,-0.00784828,-0.07757962,-0.02769909,-0.01277288,0.06188121,0.05363767,-0.04675945,-0.03811264,-0.07352632,-0.0053081,-0.03939664,0.06893875,0.01731465,-0.07048184,-0.04140937,-0.01740118,-0.02754358,-0.08103454,-0.03393541,-0.04130404,-0.08616562,-0.02643391,0.06578378,-0.05183616,-0.11551571,-0.0521997,0.00653058,0.00405879,0.03544117,0.00724768,-0.03187733,0.00522029,0.03394705,-0.03465194,0.01240093,-0.03267882,0.05607358,0.00553578,-0.0716159,0.04135088,-0.04376264,0.06073824,-0.00529719,-0.02023813,-0.02465763,-0.02644249,0.08448323,-0.04394763,-0.00374658,-0.04052749,-0.05014181,-0.01491819,0.01213236,0.04438691,-0.03621464,-0.07763761,0.03702981,-0.00903819,0.03177577,0.02175324,0.05481783,0.05323771,-0.04724386,0.04964886,0.00449017,0.07981274,-0.05122313,-0.00110048,0.02725314,0.01667525,0.00992483,-0.20915507,-0.02751655,0.00875819,-0.04002482,0.0390638,-0.00198698,0.02236122,0.0052933,0.04646258,0.04120815,0.0545392,-0.00963192,-0.04065519,0.02418794,-0.0096858,0.038164,-0.11041994,-0.00833485,0.01765909,0.03985206,-0.02533001,0.03202293,-0.0503118,-0.01888348,0.04993996,-0.01261141,0.16488938,0.01970221,0.04173565,0.017802,0.03397527,0.05835206,-0.01745735,-0.09052508,0.03090594,0.03305016,0.00004457,-0.000035,-0.03552239,-0.02038432,-0.04117288,-0.01391167,-0.00827769,-0.12844536,-0.03073792,-0.00203833,0.02018546,0.00168525,-0.03129325,0.01235884,0.05182721,-0.00930797,0.05041585,0.04512285,0.07723424,-0.02251963,-0.08108068,-0.00860976,0.02539929,0.02168495,-0.05033456,0.00428308,-0.016031,-0.03987755,0.06638401,-0.00068448,0.01667638,0.01865078,0.01661115,-0.08190867,0.01421243,0.05437559,0.04018025,0.02868197,0.01517078,0.02866649,0.0960137,-0.09016894,0.04549815,-0.0068241,-0.03012923,-0.04968318,0.04676247,0.05102204,0.06463558,0.00578317,0.07083314,0.02080516,0.00716701,-0.00261529,0.02125701,0.0357516,-0.06720922,-0.04382293,-0.00872576,0.1087763,-0.25128397,0.00505657,-0.01020714,0.0447292,-0.06741199,-0.04772525,0.05855678,0.00089321,-0.01365815,0.07607644,0.00069312,0.03478635,0.02160668,-0.01983416,0.00416155,-0.05852922,-0.001419,-0.01222565,0.09138808,0.05844938,0.03279044,0.04999977,0.20128104,-0.00173751,0.04249417,-0.01269528,-0.03381352,0.00094326,-0.01087049,0.03190058,0.01114233,0.03442292,0.03390422,-0.05210022,0.00191107,0.08332096,-0.00832342,-0.0072541,0.03940032,0.0524286,0.00907953,-0.0131968,0.04247576,0.04524691,0.11938548,-0.01355532,0.00077893,-0.0806248,0.00941116,-0.00660848,-0.07375321,-0.04981206,0.0389596,-0.02633875,0.0588515,0.08116749,-0.00428311,-0.01511171,-0.0031777,-0.03080944,-0.02674924,-0.00732046,0.03041108,0.0261741,0.01501607],"last_embed":{"hash":"199ji56","tokens":125}}},"text":null,"length":0,"last_read":{"hash":"199ji56","at":1753423435502},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict#{3}","lines":[101,101],"size":314,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06389131,-0.01047678,-0.07402178,0.0184809,-0.03967708,0.04055063,0.06658918,0.0295897,0.08916712,-0.04131221,-0.00813519,0.00290522,0.0258351,0.01709807,-0.0444465,-0.05316548,0.05420833,0.00387722,-0.02592208,0.03974813,-0.01455681,-0.06787047,-0.08324602,-0.05796871,0.04519339,-0.0094961,-0.04401802,-0.03901901,-0.06536537,-0.23329222,-0.01292183,-0.01387123,0.02255548,-0.05249332,-0.04930906,-0.04487141,-0.04325666,0.04863081,-0.00537609,0.02085046,0.00679837,0.03376727,-0.01561519,0.00279933,0.02169038,-0.05177283,0.03825317,0.01595879,0.02589772,0.00040315,-0.02707965,-0.05388293,-0.02571914,-0.0157773,0.03936407,-0.01714608,0.05870444,0.06959028,-0.04643938,0.03664045,-0.00032854,0.02518589,-0.21276593,0.02390052,-0.00930256,0.03266092,-0.02164229,0.02657311,0.04231637,0.09690411,0.03308164,0.05133889,-0.04458607,0.09906024,0.02415027,-0.02289258,0.03372547,-0.06291837,0.00923034,0.02009091,0.04995416,-0.0743632,0.00068645,-0.04087295,0.00787923,-0.0638727,0.0046514,-0.0165152,0.04948985,-0.04689835,0.06420588,0.01496344,0.01873867,-0.04123798,0.00349799,-0.02583937,0.02725285,-0.02475414,-0.08114457,0.12829988,0.02806512,-0.01835114,-0.05354585,0.04615364,0.02219346,0.0049053,0.00137596,-0.02540205,-0.05648376,-0.03721889,0.0514653,-0.01496747,0.0628641,0.01031606,-0.023051,0.03407362,-0.01132583,-0.00610252,-0.0026111,0.00172352,-0.00343367,0.08254487,0.09002586,0.00050745,-0.02785137,-0.02844993,0.01767472,0.06653256,0.01333188,-0.00773542,0.00632896,-0.11051717,-0.037726,-0.0564253,-0.04502486,-0.0557519,0.03973339,0.04371573,-0.00887105,0.01234933,-0.07204048,-0.01304453,0.05249428,-0.10439497,-0.01749598,0.05050434,-0.01200228,0.02726015,-0.01268776,0.00256066,0.00855275,-0.03511404,-0.02835819,-0.01892415,-0.00545088,-0.04179408,0.04014805,0.07003852,-0.04507808,-0.02031798,-0.05949981,-0.07778993,-0.04467117,0.10446625,-0.00175754,-0.04037467,-0.0429039,0.00778482,-0.00497152,-0.05246202,-0.01835376,-0.02218694,-0.06115601,0.00144278,0.06355359,-0.02821143,-0.05913905,-0.03780276,-0.02954472,-0.00246931,0.03396193,0.00401768,-0.03718968,0.01219628,0.04750611,-0.02567332,-0.03386538,-0.03663635,0.07572106,-0.01576497,-0.12586117,-0.03123191,-0.05145014,0.05758882,-0.01279868,0.0007238,-0.00239146,-0.01103174,0.04056174,-0.05260724,0.03483017,-0.00939301,0.02227597,-0.00001178,0.00262963,0.08013657,-0.04727269,-0.11366758,0.03888375,-0.03027884,0.05008164,-0.00714003,0.07373518,0.0646144,-0.01816496,0.03246132,0.00307058,0.06899731,-0.04674916,-0.00544819,0.03397992,0.05594458,0.02056881,-0.23428349,-0.02337567,-0.01130486,-0.0026679,0.03825758,0.00280158,-0.00281139,-0.00398597,0.04702729,0.02245244,0.02691425,-0.04778006,-0.01898385,-0.00326595,0.02151254,0.07513665,-0.09077083,-0.05979886,-0.01786839,0.05460061,-0.03930251,0.03696709,-0.02874912,-0.03041903,0.08763409,0.01617496,0.174933,0.01807703,0.0211856,0.02246652,0.0401481,0.02862201,-0.0105981,-0.08596925,0.04863368,0.02518713,-0.01475171,0.01028504,-0.06192818,-0.03585876,-0.0548295,-0.00708799,0.03761515,-0.10776016,-0.03071262,-0.02702668,0.03431816,0.04425859,-0.0290846,0.06362179,0.06071632,-0.0063756,0.01652105,0.04733296,0.11447914,-0.04172221,-0.09971653,-0.01608822,0.02141014,0.06787682,-0.06439093,0.02587468,0.01580064,-0.07123306,0.07193401,0.01032261,-0.00225332,-0.02628368,0.02522708,-0.00818627,-0.00182213,0.08754137,0.06417628,0.02874905,0.02203639,0.03568203,0.04479532,-0.06459618,0.03076398,-0.00142467,-0.04826495,-0.04551109,0.08093946,0.05452421,0.04443452,-0.00529894,0.08037137,0.00135665,0.03479094,-0.00158653,-0.02852016,0.01141227,-0.06994241,-0.01996075,0.03930541,0.05589153,-0.2492678,-0.01575017,-0.02438362,0.046439,-0.03461954,-0.03009421,0.05497047,-0.01817433,-0.02858235,0.09321494,0.01952112,0.01657724,0.05003509,0.00645267,-0.00833867,-0.0310147,0.01092635,-0.03741853,0.05629872,0.01863725,0.01258389,0.06379002,0.20750248,-0.00911281,0.01494578,-0.00408476,0.01225394,0.02522821,-0.00806984,0.06636851,0.01645162,0.02529019,0.05041441,-0.01604301,0.00659637,0.07435948,-0.04219837,-0.00447334,0.02456154,0.02394873,-0.00668845,-0.00924334,0.03367114,0.03408116,0.08809277,-0.00118107,-0.01014571,-0.06004736,0.05499652,0.01303532,-0.04205357,-0.04191001,0.06156018,-0.00690248,0.04556872,0.0643611,0.01904854,-0.01833221,0.02413538,-0.01460247,0.00548245,-0.00766835,0.03309619,0.00329094,-0.0157928],"last_embed":{"hash":"w086wm","tokens":133}}},"text":null,"length":0,"last_read":{"hash":"w086wm","at":1753423435537},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App##Verdict#{4}","lines":[102,103],"size":400,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10876219,-0.01047345,-0.06639487,0.03082918,-0.03570921,0.02363402,0.02589015,0.01605693,0.05010933,-0.01383061,0.0027788,-0.01462908,-0.00102067,0.01806601,-0.0517629,-0.06131933,0.0332689,0.02337157,0.01525599,0.03124573,0.06056356,-0.04622135,-0.11838096,-0.08031858,0.05034975,-0.00527366,-0.04104609,-0.03252067,-0.04492832,-0.22984588,-0.00848399,-0.03599854,0.06116681,-0.05464134,-0.08846639,-0.03257015,-0.01529966,0.03794072,-0.006913,0.01125055,0.0151838,0.03250247,0.0010077,0.04590239,0.00258189,-0.02545725,0.01375025,0.019608,0.05345466,-0.0111889,-0.06652745,-0.00701091,-0.05706061,-0.04697201,0.07031425,-0.02208563,0.07857528,0.06287048,-0.04107055,0.03569349,-0.0192439,0.04005422,-0.2085952,0.07133524,-0.00987779,0.03837807,-0.02193853,-0.0018256,0.05839526,0.08117723,0.01126584,0.0362368,-0.01857978,0.06277974,0.02131357,-0.01292889,0.01109021,-0.06705389,0.02632537,0.00564287,-0.00639526,-0.05798686,0.02106383,-0.03259967,0.05212481,-0.00620363,-0.01625662,0.01919872,0.08960672,-0.08090297,0.05999466,0.05397177,0.01931299,-0.02063668,0.0057331,-0.0332852,0.02490129,-0.04461087,-0.02178016,0.11569791,0.01715845,-0.02813138,-0.02658432,0.01816888,0.02455079,-0.03408866,-0.00549926,-0.04146053,-0.04137157,0.02490681,0.03568723,-0.03349804,0.05887,0.00389606,-0.04967603,0.01862985,0.00705265,-0.00926405,0.03750097,0.00745305,-0.02023791,0.0525901,0.06318103,-0.01864931,-0.00892528,-0.04070721,0.02647613,0.05606117,-0.02012647,0.04347137,0.01395498,-0.0382025,-0.09028782,-0.06274889,-0.05551149,-0.03994473,0.0506536,0.07161841,-0.01653452,0.03252107,-0.07509911,-0.00310971,0.05757158,-0.09410144,-0.03211474,0.06736314,-0.00190742,0.03426889,-0.02316181,-0.01029199,0.00411963,-0.01659728,0.01104647,-0.03715004,-0.006604,-0.03388077,0.08527943,0.06913387,-0.05085216,0.00396302,-0.09677837,-0.0492381,-0.04399273,0.12393012,-0.00530334,-0.07584065,-0.02171276,0.00437101,-0.01352038,-0.08287615,-0.01689614,-0.03077638,-0.07296088,0.00000479,0.07541945,-0.04532007,-0.05168006,-0.04038163,0.00982762,0.02769786,0.00860407,-0.030712,-0.07094272,0.01472811,0.02529204,-0.02302548,0.00565624,-0.05608054,0.06550768,0.01430574,-0.08038086,-0.01719783,-0.04118112,0.03383663,-0.04880904,0.00605096,-0.01896587,-0.04665426,0.05328379,-0.00684421,0.00586806,-0.01688071,-0.01742313,-0.01492078,-0.03025599,0.07091405,-0.00854275,-0.11195252,0.0270158,-0.01422148,0.03575962,0.01159064,0.02566226,0.04577302,-0.00952856,0.05380157,-0.00846514,0.05979485,-0.07118461,0.04900252,0.03213356,-0.00205371,-0.01826722,-0.1920324,-0.03636093,-0.02223067,0.01022534,0.05602166,-0.00636267,0.01331878,-0.00309134,0.06906756,0.07993833,0.05399339,-0.04130401,-0.01656643,0.00770565,0.01170505,0.04532558,-0.10138442,-0.04664558,-0.02498152,0.05750662,-0.00527577,-0.02360654,0.00622493,-0.06085033,0.0334956,-0.00040129,0.17066012,0.01037475,0.0337591,0.05255298,0.02228916,0.03119396,-0.02611186,-0.05140758,0.05067091,0.00296827,-0.01466536,0.01496511,-0.01107007,-0.01811473,-0.03120472,0.02122492,0.04529058,-0.11570745,-0.04577322,-0.0227802,0.02678506,0.02065217,-0.02239349,0.00822676,0.05647739,0.00349605,0.01851013,0.03057534,0.06977321,-0.0160228,-0.07548914,-0.04048826,-0.00388244,0.03292885,-0.04109887,0.02060008,0.0200921,-0.02675794,0.05834738,0.00159687,0.02249566,-0.01295352,0.03552858,-0.02700172,-0.02281068,0.07667574,0.06550918,0.03639936,0.04631199,0.04093029,0.11109164,-0.09998836,0.02968565,-0.01753618,-0.03681554,-0.06984237,0.06291837,0.05389243,0.04692073,0.00738096,0.08217689,0.00723297,0.04691677,-0.01205519,-0.01843131,0.01121007,-0.05818907,-0.0174041,0.00665388,0.07978904,-0.26635283,0.00119403,-0.02552058,0.04447437,-0.02349701,-0.06563766,0.04858229,-0.05898584,-0.01738585,0.02414863,0.00618085,0.01489816,0.06899439,-0.01580329,-0.02761293,-0.01613724,0.01635282,-0.0371476,0.08311999,0.0564706,-0.00166576,0.07314561,0.20248409,-0.01811754,0.04234529,-0.00300951,-0.01022269,0.00448548,0.01159798,0.04742672,0.01667219,0.03983027,0.03279674,-0.02992715,-0.01421625,0.10005371,0.00453868,-0.01756797,0.00358213,0.01518033,-0.02457869,-0.01894964,0.00951062,0.0524627,0.10003792,0.00835924,-0.03640705,-0.05355336,0.05411038,-0.00023169,-0.06282614,-0.05275767,0.02406211,-0.00057664,0.06322042,0.06476788,0.02346677,-0.04130902,0.00925014,-0.02867685,0.00578055,-0.00534816,0.08912853,0.04631165,0.01800708],"last_embed":{"hash":"116tr1h","tokens":413}}},"text":null,"length":0,"last_read":{"hash":"116tr1h","at":1753423435579},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)","lines":[106,129],"size":2406,"outlinks":[{"title":"<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>","target":"https://saliu.com/content/blackjack.html","line":1},{"title":"<u><b>Blackjack</b></u>: _**Basic Strategy, Card Counting, Charts, Probability, Odds, Software**_","target":"https://saliu.com/blackjack.html","line":5},{"title":"_**Casinos, Gambling, Win in Casino with Mathematical Systems**_","target":"https://saliu.com/keywords/casino.htm","line":9},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":10},{"title":"**_The Best Blackjack Basic Strategy: Free Cards, Charts_**","target":"https://saliu.com/bbs/messages/399.html","line":11},{"title":"_**Gambling Mathematics in Blackjack Proves Deception of Card-Counting Systems**_","target":"https://saliu.com/bbs/messages/274.html","line":13},{"title":"_**Calculate Blackjack Probability, Odds: Natural 21, Insurance, Double-Down Hands, Pairs**_","target":"https://forums.saliu.com/blackjack-natural-odds-probability.html","line":14},{"title":"_**New Casino Games by Ion Saliu:**_ **Parpaluck Blackjack**","target":"https://forums.saliu.com/new-blackjack-game.html","line":15},{"title":"_**<u>ABC</u>: The Best Blackjack Card-Counting System by Ion Saliu**_","target":"https://forums.saliu.com/card-counting-blackjack.html#abc-system","line":16},{"title":"**Software**","target":"https://saliu.com/infodown.html","line":17},{"title":"This is the blackjack site for all humans plus casino gamblers with the goal to win big-time.","target":"https://saliu.com/HLINE.gif","line":19},{"title":"Forums","target":"https://forums.saliu.com/","line":21},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":21},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":21},{"title":"Contents","target":"https://saliu.com/content/index.html","line":21},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":21},{"title":"Home","target":"https://saliu.com/index.htm","line":21},{"title":"Search","target":"https://saliu.com/Search.htm","line":21},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":21},{"title":"Learn correct blackjack odds, house edge, advantage, mathematical calculations by accurate program.","target":"https://saliu.com/HLINE.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12788746,0.0036613,-0.08168697,0.02830245,-0.02321176,0.0288648,0.0154124,0.00482643,0.04254604,-0.02660287,0.00137561,-0.01750815,0.0132839,-0.00121102,-0.02931608,-0.06712909,0.02265883,-0.00421911,-0.00119012,0.04563082,0.04441046,-0.02904324,-0.1055587,-0.06421691,0.03223948,-0.0065803,-0.03120509,-0.04483163,-0.02249428,-0.21836057,0.00288905,-0.03273118,0.06486383,-0.05890982,-0.06937314,-0.03453663,-0.00032144,0.04972667,0.00227167,0.03482899,0.03110798,0.03540201,0.00788288,0.04274571,-0.00878823,-0.02870223,0.00831632,0.01508338,0.02762339,-0.00190536,-0.06236821,-0.00338545,-0.05411255,-0.04359673,0.08585536,-0.03216778,0.09124935,0.07891795,-0.03924665,0.0384785,-0.00109957,0.04171016,-0.23980604,0.07351957,-0.01908541,0.0393781,-0.04856848,0.01341661,0.04115235,0.06509342,0.00960026,0.03087817,-0.04770337,0.07171833,0.03910672,-0.01346298,0.00120356,-0.07154523,0.02642344,0.01108855,0.01978342,-0.07387476,0.00104931,-0.03284267,0.03898681,-0.00641567,-0.00961549,0.03609239,0.06456859,-0.07684644,0.05857805,0.02248874,0.02026976,-0.0250157,0.00394195,-0.04156473,0.02226465,-0.03084003,-0.03701129,0.10715178,0.0256066,-0.01866828,-0.03504109,0.03055119,0.02870793,0.00081153,-0.00565136,-0.03096656,-0.04306868,0.00167036,0.03460285,-0.00939042,0.03073363,0.00053548,-0.03228151,-0.00211846,0.0042845,-0.01755877,0.029982,0.0081962,-0.00616178,0.05175736,0.06697785,-0.01974274,-0.01284049,-0.02961365,0.03024387,0.03922183,-0.01731999,0.02602389,0.01851593,-0.02392941,-0.08359733,-0.07307042,-0.06010885,-0.02637564,0.03921479,0.02541857,-0.02503818,0.01739069,-0.06897619,-0.0197772,0.04084192,-0.09060227,-0.02118298,0.04334033,-0.0043863,0.05222258,-0.02132686,0.00957662,-0.01698877,-0.0386202,0.0187498,-0.02287117,-0.0298939,-0.02708698,0.10339549,0.06748362,-0.04276075,-0.01175345,-0.08074559,-0.03942135,-0.05052527,0.12367588,0.00847409,-0.0782837,-0.03543331,0.02656952,0.01445324,-0.07779943,-0.00892641,-0.05312806,-0.06685089,-0.00301557,0.07078411,-0.02082492,-0.06237785,-0.01654242,0.01206744,0.03590973,0.00555008,-0.0228324,-0.06464365,0.00766005,0.01234512,-0.06245742,0.01586533,-0.04755779,0.05326231,0.00931394,-0.09082086,-0.01224982,-0.04785257,0.02729332,-0.02397268,-0.00365676,-0.00355919,-0.03750668,0.09297948,-0.02747848,-0.00761063,-0.01229206,-0.01436912,-0.02298632,-0.03746888,0.06317263,-0.03013256,-0.1085201,0.00777939,-0.01319141,0.05516165,0.03147854,0.04933591,0.05819859,-0.003322,0.05095657,-0.00520272,0.06151191,-0.04411992,0.05149605,0.0301595,0.02668102,-0.00664276,-0.1979548,-0.01476356,-0.02866143,-0.02430348,0.02742003,0.00264457,0.02640397,-0.00188389,0.05491092,0.05076079,0.08982625,-0.04272266,0.00288854,0.0034351,-0.00008976,0.04601268,-0.11027246,-0.05900792,0.00349781,0.06997717,-0.02625114,-0.01923931,-0.02902335,-0.05220611,0.04506607,-0.02507257,0.1622372,0.0387897,0.03474583,0.02742099,0.0350362,0.05527369,-0.01057571,-0.08156694,0.06895869,0.02316667,-0.01507074,0.03115291,-0.01697832,-0.02839428,-0.05040229,0.02651343,0.01386148,-0.10181854,-0.03221533,-0.01554682,0.04614653,0.01720767,-0.0356059,0.00579313,0.08582094,-0.01562159,-0.00113242,0.04012868,0.05856779,-0.01278993,-0.06319745,-0.02945764,0.00713953,0.03416948,-0.05302883,0.0391921,0.02539894,-0.03478493,0.06715591,0.00422446,0.02191911,-0.02410138,0.04135154,-0.05341591,-0.00525299,0.07764943,0.03575795,0.06027427,0.05165543,0.02264973,0.07593901,-0.08286376,0.05263802,0.0121675,-0.03552367,-0.08710289,0.06160906,0.05198004,0.09510745,-0.01173305,0.03952181,-0.01678287,0.07095209,-0.00150921,-0.01006825,0.01790088,-0.04846822,-0.01930867,-0.00893573,0.09030954,-0.26503173,0.00863389,-0.0090989,0.05174637,-0.03746018,-0.06817158,0.07060558,-0.04727923,-0.02091265,0.05650055,0.02593755,0.03304121,0.04876854,-0.01420135,-0.01011881,-0.01123243,-0.00768632,0.01458503,0.07684861,0.04390789,-0.0101588,0.06465454,0.21392928,-0.01427308,0.01701769,-0.00257539,-0.00301154,-0.03665889,0.02815394,0.05389838,-0.00142989,0.03523499,0.03855799,-0.02644811,-0.03053341,0.08770381,-0.00393117,-0.03682173,0.02365331,0.02668127,-0.02735008,-0.02125091,0.01136158,0.0390452,0.09588962,0.01823117,-0.02349122,-0.05626114,0.02932859,-0.0080999,-0.05205364,-0.09008585,0.02996658,-0.00033159,0.05908867,0.06286836,0.01875311,-0.02389193,0.00210444,-0.0229007,0.00056203,0.00920793,0.06511344,0.01191744,0.01036387],"last_embed":{"hash":"1pfw48g","tokens":317}}},"text":null,"length":0,"last_read":{"hash":"1pfw48g","at":1753423435709},"key":"notes/saliu/Blackjack Strategy, System Test High Roller Software App.md#Blackjack Strategy, System Test: High Roller Software App#[<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)#{13}","lines":[124,129],"size":651,"outlinks":[{"title":"This is the blackjack site for all humans plus casino gamblers with the goal to win big-time.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Learn correct blackjack odds, house edge, advantage, mathematical calculations by accurate program.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
