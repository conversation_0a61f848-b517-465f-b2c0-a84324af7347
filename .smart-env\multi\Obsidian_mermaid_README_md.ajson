"smart_sources:Obsidian/mermaid_README.md": {"path":"Obsidian/mermaid_README.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02423912,-0.0041485,0.02499669,-0.04283107,0.04340093,-0.01150799,-0.04522154,0.10272975,-0.02624381,0.04129468,0.03275924,-0.02841034,-0.05362606,0.01778974,-0.03643182,0.01891513,-0.01419136,0.05426384,0.02698781,0.02833284,0.05928214,-0.03395317,-0.00462364,-0.0407084,0.01489061,0.06454518,-0.02976199,-0.06587524,-0.0371713,-0.20164278,-0.03504687,-0.03208001,-0.01590204,0.01109868,-0.08336593,-0.00800258,0.00731613,0.0180047,0.02188085,0.02594024,-0.04456483,-0.06826993,-0.04535519,-0.03094717,-0.00052221,-0.04014302,-0.03626178,-0.01294814,-0.01055784,-0.05883098,-0.09236892,-0.04536432,0.0616043,-0.02626738,0.05345776,0.07625458,0.05624216,0.04907873,-0.01835452,0.00095255,0.05996466,0.06681648,-0.155221,0.10611081,0.03944715,0.05287986,-0.03919509,0.04249956,0.0229191,-0.00955021,-0.03057869,0.01390638,0.03698405,0.0247757,0.04108721,0.00969319,0.04845595,-0.05888252,-0.02094897,-0.01883758,-0.06281316,-0.02203772,0.00409124,-0.01787148,0.01389691,0.01459445,0.076516,0.00244388,0.07048485,0.01319444,-0.06272487,-0.04090327,0.04560791,0.02173293,-0.10057067,0.01141614,0.03886789,-0.0267102,0.00742336,0.12227701,-0.05145948,0.03786722,-0.01474433,-0.06650942,0.08578097,-0.03311715,0.00472836,-0.06383614,-0.02454288,0.00775668,-0.02976358,-0.01815408,-0.01426136,-0.03340792,0.03172304,0.01534497,-0.00231615,0.0265822,0.00838518,-0.02367613,-0.0081459,0.05731921,0.08005969,-0.00863255,-0.00833749,0.02902224,0.06719347,0.03309838,0.04557875,0.01776838,0.06878162,0.03432082,-0.09837338,-0.03283182,0.028926,-0.01240814,-0.00797569,-0.00315219,0.04472693,0.03742911,-0.01110356,-0.01945527,0.00689957,-0.08665052,-0.03238751,-0.03594796,0.0027804,0.10030454,-0.02044792,-0.03420646,-0.01050373,0.07637791,-0.08013038,-0.02305575,0.0223373,-0.00618406,0.02094309,0.09413827,-0.05335635,0.0295003,0.01171786,-0.05167277,0.01792639,0.01483996,-0.00270466,-0.09552417,0.00738725,0.05812186,-0.04253982,-0.04203691,-0.04603741,0.05275023,-0.01907606,0.03167042,0.07441833,0.01053629,-0.0715134,-0.03983333,0.03031062,0.05071954,-0.01623226,-0.0647205,-0.04643643,0.05935505,0.05542463,-0.07533924,-0.01150018,-0.03972147,0.0082784,-0.01115804,0.04202504,-0.03098568,0.0027835,0.07090716,0.03525156,-0.0083648,-0.00774135,-0.01627714,-0.01781461,-0.02302524,0.01034687,-0.0171611,-0.01879034,0.03951737,-0.03206883,0.01595487,-0.0025268,-0.07054242,0.02872132,0.00109138,-0.07027371,-0.03718952,0.00075488,0.02489385,-0.03610027,-0.01637815,0.04795425,0.07641453,-0.02146608,0.02372395,0.01412062,-0.0580847,-0.05529032,-0.23306495,-0.04692408,0.01563375,-0.02654327,0.00849336,-0.08375704,0.0023817,-0.05752927,-0.02407989,0.04423098,0.1082839,-0.02087111,0.00083921,-0.03341803,-0.03720172,-0.03186095,-0.05863137,0.00077196,-0.03098132,0.02453173,0.03157203,0.01966873,0.01062902,-0.05740473,0.01569651,-0.01210939,0.21420056,0.10023729,0.03329463,0.04862228,0.01127415,0.04983431,-0.03719678,-0.10141358,-0.01945663,0.05333,-0.01984362,0.00926835,-0.06432139,-0.03249873,-0.07277845,0.00998902,0.03312336,-0.08120289,-0.04324318,-0.0095656,-0.03077741,-0.01556916,0.0474623,0.04463413,0.00451543,0.06524859,0.00588623,0.04836852,0.00294547,0.03797179,-0.01702587,-0.05056623,-0.03748161,0.01160879,0.00392381,0.01698565,-0.00511912,-0.01215215,0.04314979,0.03682886,-0.04576508,-0.00601163,0.06819279,-0.08702266,-0.08075334,0.13790393,0.024934,-0.00798676,0.05527352,0.01196295,0.00067219,-0.06436366,0.03170708,-0.01351837,0.00485074,-0.00239574,0.03614323,0.04376255,-0.05014246,0.05423539,-0.00436672,-0.02600737,-0.02546969,-0.01188558,-0.03664406,0.05504111,-0.04846941,-0.02532759,0.03150284,0.08427803,-0.25411472,0.04535716,0.03852625,0.03075012,0.01569066,0.0041404,0.06086472,-0.04199982,0.02586815,0.0415026,-0.04683371,0.0260155,-0.03809834,-0.02856289,0.01583494,0.05446025,0.01602661,0.04029399,0.06762779,-0.07000855,0.05438181,0.0845582,0.23840696,-0.00027199,0.00644047,0.02087347,0.00193722,0.05283387,0.04272236,0.01764453,-0.05428538,0.03432414,0.03226533,-0.03384404,-0.02827247,0.00412865,-0.01736575,-0.00220089,0.04083588,-0.04323684,0.01910315,0.0571477,-0.00963798,0.0328108,0.08205365,-0.02326721,-0.01742818,-0.09052399,0.00992057,0.0267338,-0.07417812,0.0244224,-0.00084121,-0.00967235,-0.03207705,0.05060142,-0.01093296,-0.0359704,-0.03822272,-0.03956453,0.01022143,0.04118336,0.00832812,0.09099814,0.01583928],"last_embed":{"hash":"a951b6a090dc765322eb2fd338ba4a0636d32718d6dc6537e761b5cc3cfac697","tokens":461}}},"last_read":{"hash":"a951b6a090dc765322eb2fd338ba4a0636d32718d6dc6537e761b5cc3cfac697","at":1745995208063},"class_name":"SmartSource2","outlinks":[{"title":"JS Open Source Awards (2019)","target":"https://osawards.com/javascript/2019","line":2},{"title":"About","target":"#about","line":13},{"title":"Examples","target":"#examples","line":14},{"title":"Release","target":"#release","line":15},{"title":"Related projects","target":"#related-projects","line":16},{"title":"Contributors","target":"#contributors","line":17},{"title":"Security and safe diagrams","target":"#security-and-safe-diagrams","line":18},{"title":"Reporting vulnerabilities","target":"#reporting-vulnerabilities","line":19},{"title":"Appreciation","target":"#appreciation","line":20},{"title":"Mermaid Live Editor","target":"https://mermaid.live/","line":37},{"title":"Tutorials","target":"./docs/config/Tutorials.md","line":38},{"title":"Integrations and Usages of Mermaid","target":"./docs/ecosystem/integrations-community.md","line":39},{"title":"Integrations and Usages of Mermaid","target":"./docs/ecosystem/integrations-community.md","line":41},{"title":"GitHub","target":"https://github.blog/2022-02-14-include-diagrams-markdown-files-mermaid/","line":41},{"title":"Tutorials","target":"./docs/config/Tutorials.md","line":43},{"title":"Usage","target":"./docs/config/usage.md","line":43},{"title":"Beginner's Guide","target":"./docs/intro/getting-started.md","line":43},{"title":"applitools","target":"https://applitools.com/","line":45},{"title":"text syntax","target":"https://mermaid-js.github.io/mermaid/#/n00b-syntaxReference","line":55},{"title":"Command Line Interface","target":"https://github.com/mermaid-js/mermaid-cli","line":361},{"title":"Live Editor","target":"https://github.com/mermaid-js/mermaid-live-editor","line":362},{"title":"HTTP Server","target":"https://github.com/TomWright/mermaid-server","line":363},{"title":"![Commits","target":"https://img.shields.io/github/commit-activity/m/mermaid-js/mermaid","line":365},{"title":"![Contributors","target":"https://img.shields.io/github/contributors/mermaid-js/mermaid","line":365},{"title":"![Good first issue","target":"https://img.shields.io/github/labels/mermaid-js/mermaid/Good%20first%20issue%21","line":365},{"title":"this issue","target":"https://github.com/mermaid-js/mermaid/issues/866","line":367},{"title":"contribution guide","target":"CONTRIBUTING.md","line":369},{"title":"d3","target":"https://d3js.org/","line":387},{"title":"dagre-d3","target":"https://github.com/cpettitt/dagre-d3","line":387},{"title":"js-sequence-diagram","target":"https://bramp.github.io/js-sequence-diagrams","line":389},{"title":"Tyler Long","target":"https://github.com/tylerlong","line":391},{"title":"contributors","target":"https://github.com/knsv/mermaid/graphs/contributors","line":393}],"blocks":{"#":[2,7],"##Table of content":[8,23],"##Table of content#{1}":[10,12],"##Table of content#{2}":[13,13],"##Table of content#{3}":[14,14],"##Table of content#{4}":[15,15],"##Table of content#{5}":[16,16],"##Table of content#{6}":[17,17],"##Table of content#{7}":[18,18],"##Table of content#{8}":[19,19],"##Table of content#{9}":[20,21],"##Table of content#{10}":[22,23],"##About":[24,52],"##About#{1}":[26,52],"##Examples":[53,346],"##Examples#{1}":[55,58],"##Examples#Flowchart [<a href=\"https://mermaid-js.github.io/mermaid/#/flowchart\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNpNkMtqwzAQRX9FzKqFJK7t1km8KDQP6KJQSLOLvZhIY1tgS0GWmgbb_165IaFaiXvOFTPqgGtBkEJR6zOv0Fj2scsU8-ft8I5G5Gw6fe339GN7tnrYaafE45WvRsLW3Ya4bKVWwzVe_xU-FfVsc9hR62rLwvw_2591z7Y3FuUwgYZMg1L4ObrRzMBW1FAGqb8KKtCLGWRq8Ko7CbS0FdJqA2mBdUsTQGf110VxSK1xdJM2EkuDzd2qNQrypQ7s5TQuXcrW-ie5VoUsx9yZ2seVtac2DYIRz0ppK3eccd0ErRTjD1XfyyRIomSBUUzJPMaXOBb8GC4XRfQcFmL-FEYIwzD8AggvcHE\">live editor</a>]":[59,78],"##Examples#Flowchart [<a href=\"https://mermaid-js.github.io/mermaid/#/flowchart\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNpNkMtqwzAQRX9FzKqFJK7t1km8KDQP6KJQSLOLvZhIY1tgS0GWmgbb_165IaFaiXvOFTPqgGtBkEJR6zOv0Fj2scsU8-ft8I5G5Gw6fe339GN7tnrYaafE45WvRsLW3Ya4bKVWwzVe_xU-FfVsc9hR62rLwvw_2591z7Y3FuUwgYZMg1L4ObrRzMBW1FAGqb8KKtCLGWRq8Ko7CbS0FdJqA2mBdUsTQGf110VxSK1xdJM2EkuDzd2qNQrypQ7s5TQuXcrW-ie5VoUsx9yZ2seVtac2DYIRz0ppK3eccd0ErRTjD1XfyyRIomSBUUzJPMaXOBb8GC4XRfQcFmL-FEYIwzD8AggvcHE\">live editor</a>]#{1}":[61,78],"##Examples#Sequence diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/sequenceDiagram\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNo9kMluwjAQhl_F-AykQMuSA1WrbuLQQ3v1ZbAnsVXHkzrjVhHi3etQwKfRv4w-z0FqMihL2eF3wqDxyUEdoVHhwTuNk-12RzaU4g29JzHMY2HpV0BE0VO6V8ETtdkGz1Zb1F8qiPyG5LX84mrLAmpwoWNh-5a0pWCiAxUwGBXeiVHEU4oq8V_6AHYUwAu2lLLTjVQ4bc1rT2yleI0IfJG320faZ9ABbk-Jz3hZnFxBduR9L2oiM5Jj2WBswJn8-cMArSRbbFDJMo8GK0ielVThmKOpNcD4bBxTlGUFvsOxhMT02QctS44JL6HzAS-iJzCYOwfJfTscunYd542aQuXqQU_RZ9kyt11ZFIM9rR3btJ9qaorOGQuR7c9mWSznyzXMF7hcLeBusTB6P9usq_ntrDKrm9kc5PF4_AMJE56Z\">live editor</a>]":[79,104],"##Examples#Sequence diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/sequenceDiagram\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNo9kMluwjAQhl_F-AykQMuSA1WrbuLQQ3v1ZbAnsVXHkzrjVhHi3etQwKfRv4w-z0FqMihL2eF3wqDxyUEdoVHhwTuNk-12RzaU4g29JzHMY2HpV0BE0VO6V8ETtdkGz1Zb1F8qiPyG5LX84mrLAmpwoWNh-5a0pWCiAxUwGBXeiVHEU4oq8V_6AHYUwAu2lLLTjVQ4bc1rT2yleI0IfJG320faZ9ABbk-Jz3hZnFxBduR9L2oiM5Jj2WBswJn8-cMArSRbbFDJMo8GK0ielVThmKOpNcD4bBxTlGUFvsOxhMT02QctS44JL6HzAS-iJzCYOwfJfTscunYd542aQuXqQU_RZ9kyt11ZFIM9rR3btJ9qaorOGQuR7c9mWSznyzXMF7hcLeBusTB6P9usq_ntrDKrm9kc5PF4_AMJE56Z\">live editor</a>]#{1}":[81,104],"##Examples#Gantt chart [<a href=\"https://mermaid-js.github.io/mermaid/#/gantt\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNp90cGOgyAQBuBXIZxtFbG29bbZ3fsmvXKZylhJEAyOTZrGd1_sto3xsHMBhu-HBO689hp5xS_giJQbsCbjHTv9jcp9-q63SKhZpb3DhMXSOIiE5ZkoNpnYZGXynh6U-4jBK7JnVfBYJo9QvgjtEya1cj8QwFq0TMz4lZqxTBg0hOF5m1jifI2Lf7Bc490CyxUu1rhc4GLGPOEdhg6Mjq92V44xxanFDhWv4lRjA6MlxZWbIh17DYTf2pAPvGrADphwGMmfbq7mFYURX-jLwCVA91bWg8YYunO69Y8vMgPFI2vvGnOZ-2Owsd0S9UOVpvP29mKoHc_b2nfpYHQLgdrrsUzLvDxALrHcS9hJqeuzOB6avBCN3mciBz5N0y_wxZ0J\">live editor</a>]":[105,128],"##Examples#Gantt chart [<a href=\"https://mermaid-js.github.io/mermaid/#/gantt\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNp90cGOgyAQBuBXIZxtFbG29bbZ3fsmvXKZylhJEAyOTZrGd1_sto3xsHMBhu-HBO689hp5xS_giJQbsCbjHTv9jcp9-q63SKhZpb3DhMXSOIiE5ZkoNpnYZGXynh6U-4jBK7JnVfBYJo9QvgjtEya1cj8QwFq0TMz4lZqxTBg0hOF5m1jifI2Lf7Bc490CyxUu1rhc4GLGPOEdhg6Mjq92V44xxanFDhWv4lRjA6MlxZWbIh17DYTf2pAPvGrADphwGMmfbq7mFYURX-jLwCVA91bWg8YYunO69Y8vMgPFI2vvGnOZ-2Owsd0S9UOVpvP29mKoHc_b2nfpYHQLgdrrsUzLvDxALrHcS9hJqeuzOB6avBCN3mciBz5N0y_wxZ0J\">live editor</a>]#{1}":[107,128],"##Examples#Class diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/classDiagram\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNpdkTFPwzAQhf-K5QlQ2zQJJG1UBaGWDYmBgYEwXO1LYuTEwXYqlZL_jt02asXm--690zvfgTLFkWaUSTBmI6DS0BTt2lfzkKx-p1PytEO9f1FtdaQkI2ulZNGuVqK1qEtgmOfk7BitSzKdOhg59XuNGgk0RDxed-_IOr6uf8cZ6UhTZ8bvHqS5ub1mr9svZPbjk6DEBlu7AQuXyBkx4gcvDk9cUMJq0XT_YaW0kNK5j-ufAoRzcihaQvLcoN4Jv50vvVxw_xrnD3RCG9QNCO4-8OgpqK1dpoJm7smxhF7agp6kfcfB4jMXVmmalW4tnFDorXrbt4xmVvc4is53GKFUwNF5DtTuO3-sShjrJjLVlqLyvNfS4drazmRB4NuzSti6386YagIjeA3a1rtlEiRRsoAoxiSN4SGOOduGy0UZ3YclT-dhBHQYhj8dc6_I\">live editor</a>]":[129,170],"##Examples#Class diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/classDiagram\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNpdkTFPwzAQhf-K5QlQ2zQJJG1UBaGWDYmBgYEwXO1LYuTEwXYqlZL_jt02asXm--690zvfgTLFkWaUSTBmI6DS0BTt2lfzkKx-p1PytEO9f1FtdaQkI2ulZNGuVqK1qEtgmOfk7BitSzKdOhg59XuNGgk0RDxed-_IOr6uf8cZ6UhTZ8bvHqS5ub1mr9svZPbjk6DEBlu7AQuXyBkx4gcvDk9cUMJq0XT_YaW0kNK5j-ufAoRzcihaQvLcoN4Jv50vvVxw_xrnD3RCG9QNCO4-8OgpqK1dpoJm7smxhF7agp6kfcfB4jMXVmmalW4tnFDorXrbt4xmVvc4is53GKFUwNF5DtTuO3-sShjrJjLVlqLyvNfS4drazmRB4NuzSti6386YagIjeA3a1rtlEiRRsoAoxiSN4SGOOduGy0UZ3YclT-dhBHQYhj8dc6_I\">live editor</a>]#{1}":[131,170],"##Examples#State diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/stateDiagram\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNpdkEFvgzAMhf8K8nEqpYSNthx22Xbcqcexg0sCiZQQlDhIFeK_L8A6TfXp6fOz9ewJGssFVOAJSbwr7ByadGR1n8T6evpO0vQ1uZDSekOrXGFsPqJPO6q-2-imH8f_0TeHXm50lfelsAMjnEHFY6xpMdRAUhhRQxUlFy0GTTXU_RytYeAx-AdXZB1ULWovdoCB7OXWN1CRC-Ju-r3uz6UtchGHJqDbsPygU57iysb2reoWHpyOWBINvsqypb3vFMlw3TfWZF5xiY7keC6zkpUnZIUojwW-FAVvrvn51LLnvOXHQ84Q5nn-AVtLcwk\">live editor</a>]":[171,192],"##Examples#State diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/stateDiagram\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNpdkEFvgzAMhf8K8nEqpYSNthx22Xbcqcexg0sCiZQQlDhIFeK_L8A6TfXp6fOz9ewJGssFVOAJSbwr7ByadGR1n8T6evpO0vQ1uZDSekOrXGFsPqJPO6q-2-imH8f_0TeHXm50lfelsAMjnEHFY6xpMdRAUhhRQxUlFy0GTTXU_RytYeAx-AdXZB1ULWovdoCB7OXWN1CRC-Ju-r3uz6UtchGHJqDbsPygU57iysb2reoWHpyOWBINvsqypb3vFMlw3TfWZF5xiY7keC6zkpUnZIUojwW-FAVvrvn51LLnvOXHQ84Q5nn-AVtLcwk\">live editor</a>]#{1}":[173,192],"##Examples#Pie chart [<a href=\"https://mermaid-js.github.io/mermaid/#/pie\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNo9jsFugzAMhl8F-VzBgEEh13Uv0F1zcYkTIpEEBadShXj3BU3dzf_n77e8wxQUgYDVkvQSbsFsEgpRtEN_5i_kvzx05XiC-xvUHVzAUXRoVe7v0heFBJ7JkQSRR0Ua08ISpD-ymlaFTN_KcoggNC4bXQATh5-Xn0BwTPSWbhZNRPdvLQEV5dIO_FrPZ43dOJ-cgtfWnDzFJeOZed1EVZ3r0lie06Ocgqs2q2aMPD_HvuqbfsCmpf7aYte2anrU46Cbz1qr60fdIBzH8QvW9lkl\">live editor</a>]":[193,208],"##Examples#Pie chart [<a href=\"https://mermaid-js.github.io/mermaid/#/pie\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNo9jsFugzAMhl8F-VzBgEEh13Uv0F1zcYkTIpEEBadShXj3BU3dzf_n77e8wxQUgYDVkvQSbsFsEgpRtEN_5i_kvzx05XiC-xvUHVzAUXRoVe7v0heFBJ7JkQSRR0Ua08ISpD-ymlaFTN_KcoggNC4bXQATh5-Xn0BwTPSWbhZNRPdvLQEV5dIO_FrPZ43dOJ-cgtfWnDzFJeOZed1EVZ3r0lie06Ocgqs2q2aMPD_HvuqbfsCmpf7aYte2anrU46Cbz1qr60fdIBzH8QvW9lkl\">live editor</a>]#{1}":[195,208],"##Examples#Git graph [experimental - <a href=\"https://mermaid.live/edit#pako:eNqNkMFugzAMhl8F-VyVAR1tOW_aA-zKxSSGRCMJCk6lCvHuNZPKZdM0n-zf3_8r8QIqaIIGMqnB8kfEybQ--y4VnLP8-9RF9Mpkmm40hmlnDKmvkPiH_kfS7nFo_VN0FAf6XwocQGgxa_nGsm1bYEOOWmik1dRjGrmF1q-Cpkkj07u2HCI0PY4zHQATh8-7V9BwTPSE3iwOEd1OjQE1iWkBvk_bzQY7s0Sq4Hs7bHqKo8iGeZqbPN_WR7mpSd1RHpvPVhuMbG7XOq_L-oJlRfW5wteq0qorrpe-PBW9Pr8UJcK6rg-BLYPQ\">live editor</a>]":[209,210],"##Examples#Bar chart (using gantt chart) [<a href=\"https://mermaid-js.github.io/mermaid/#/gantt\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNptkU1vhCAQhv8KIenNugiI4rkf6bmXpvEyFVxJFDYyNt1u9r8X63Z7WQ9m5pknLzieaBeMpQ3dg0dsPUkPOhwteXZIXmJcbCT3xMAxkuh8Z8kIEclyMIB209fqKcwTICFvG4IvFy_oLrZ-g9F26ILfQgvNFN94VaRXQ1iWqpumZBcu1J8p1E1TXDx59eQNr5LyEqjJn6hv5QnGNlxevZJmdLLpy5xJSzut45biYCfb0iaVxvawjNjS1p-TCguG16PvaIPzYjO67e3BwX6GiTY9jPFKH43DMF_hGMDY1J4oHg-_f8hFTJFd8L3br3yZx4QHxENsdrt1nO8dDstH3oVpF50ZYMbhU6ud4qoGLqyqBJRCmO6j0HXPZdGbihUc6Pmc0QP49xD-b5X69ZQv2gjO81IwzWqhC1lKrjJ6pA3nVS7SMiVjrKirWlYp5fs3osgrWeo00lorLWvOzz8JVbXm\">live editor</a>]":[211,248],"##Examples#Bar chart (using gantt chart) [<a href=\"https://mermaid-js.github.io/mermaid/#/gantt\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNptkU1vhCAQhv8KIenNugiI4rkf6bmXpvEyFVxJFDYyNt1u9r8X63Z7WQ9m5pknLzieaBeMpQ3dg0dsPUkPOhwteXZIXmJcbCT3xMAxkuh8Z8kIEclyMIB209fqKcwTICFvG4IvFy_oLrZ-g9F26ILfQgvNFN94VaRXQ1iWqpumZBcu1J8p1E1TXDx59eQNr5LyEqjJn6hv5QnGNlxevZJmdLLpy5xJSzut45biYCfb0iaVxvawjNjS1p-TCguG16PvaIPzYjO67e3BwX6GiTY9jPFKH43DMF_hGMDY1J4oHg-_f8hFTJFd8L3br3yZx4QHxENsdrt1nO8dDstH3oVpF50ZYMbhU6ud4qoGLqyqBJRCmO6j0HXPZdGbihUc6Pmc0QP49xD-b5X69ZQv2gjO81IwzWqhC1lKrjJ6pA3nVS7SMiVjrKirWlYp5fs3osgrWeo00lorLWvOzz8JVbXm\">live editor</a>]#{1}":[213,248],"##Examples#User Journey diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/user-journey\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNplkMFuwjAQRH9l5TMiTVIC-FqqnjhxzWWJN4khsSN7XRSh_HsdKBVt97R6Mzsj-yoqq0hIAXCywRkaSwNxWHNHsB_hYt1ZmwYUfiueKtbWwIcFtjf5zgH2eCZgQgkrCXt64GgMg2fUzkvIn5Xd_V5COtMFvCH_62ht_5yk7MU8sn61HDTfxD8VYiF6cj1qFd94nWkpuKWYKWRcFdUYOi5FaaZoDYNCpnel2Toha-w8LQQGtofRVEKyC_Qw7TQ2DvsfV2dRUTy6Ch6H-UMb7TlGVtbUupl5cF3ELfPgZZLM8rLR3IbjsrJ94rVq0XH7uS2SIis2mOVUrHNc5bmqjul2U2evaa3WL2mGYpqmL2BGiho\">live editor</a>]":[249,274],"##Examples#User Journey diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/user-journey\">docs</a> - <a href=\"https://mermaid.live/edit#pako:eNplkMFuwjAQRH9l5TMiTVIC-FqqnjhxzWWJN4khsSN7XRSh_HsdKBVt97R6Mzsj-yoqq0hIAXCywRkaSwNxWHNHsB_hYt1ZmwYUfiueKtbWwIcFtjf5zgH2eCZgQgkrCXt64GgMg2fUzkvIn5Xd_V5COtMFvCH_62ht_5yk7MU8sn61HDTfxD8VYiF6cj1qFd94nWkpuKWYKWRcFdUYOi5FaaZoDYNCpnel2Toha-w8LQQGtofRVEKyC_Qw7TQ2DvsfV2dRUTy6Ch6H-UMb7TlGVtbUupl5cF3ELfPgZZLM8rLR3IbjsrJ94rVq0XH7uS2SIis2mOVUrHNc5bmqjul2U2evaa3WL2mGYpqmL2BGiho\">live editor</a>]#{1}":[251,274],"##Examples#C4 diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/c4c\">docs</a>]":[275,346],"##Examples#C4 diagram [<a href=\"https://mermaid-js.github.io/mermaid/#/c4c\">docs</a>]#{1}":[277,346],"##Release":[347,358],"##Release#{1}":[349,358],"##Related projects":[359,364],"##Related projects#{1}":[361,361],"##Related projects#{2}":[362,362],"##Related projects#{3}":[363,364],"##Contributors [![Good first issue](https://img.shields.io/github/labels/mermaid-js/mermaid/Good%20first%20issue%21)](https://github.com/mermaid-js/mermaid/issues?q=is%3Aissue+is%3Aopen+label%3A%22Good+first+issue%21%22) [![Contributors](https://img.shields.io/github/contributors/mermaid-js/mermaid)](https://github.com/mermaid-js/mermaid/graphs/contributors) [![Commits](https://img.shields.io/github/commit-activity/m/mermaid-js/mermaid)](https://github.com/mermaid-js/mermaid/graphs/contributors)":[365,370],"##Contributors [![Good first issue](https://img.shields.io/github/labels/mermaid-js/mermaid/Good%20first%20issue%21)](https://github.com/mermaid-js/mermaid/issues?q=is%3Aissue+is%3Aopen+label%3A%22Good+first+issue%21%22) [![Contributors](https://img.shields.io/github/contributors/mermaid-js/mermaid)](https://github.com/mermaid-js/mermaid/graphs/contributors) [![Commits](https://img.shields.io/github/commit-activity/m/mermaid-js/mermaid)](https://github.com/mermaid-js/mermaid/graphs/contributors)#{1}":[367,370],"##Security and safe diagrams":[371,378],"##Security and safe diagrams#{1}":[373,378],"##Reporting vulnerabilities":[379,382],"##Reporting vulnerabilities#{1}":[381,382],"##Appreciation":[383,398],"##Appreciation#{1}":[385,398],"#---frontmatter---":[395,null]},"last_import":{"mtime":1717375412494,"size":20964,"at":1740449882658,"hash":"a951b6a090dc765322eb2fd338ba4a0636d32718d6dc6537e761b5cc3cfac697"},"key":"Obsidian/mermaid_README.md"},