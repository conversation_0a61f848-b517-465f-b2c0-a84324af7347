
"smart_sources:筆記/202506300940.md": {"path":"筆記/202506300940.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"122jrl1","at":1751264965794},"class_name":"SmartSource","last_import":{"mtime":1751247841291,"size":5616,"at":1751264965794,"hash":"122jrl1"},"blocks":{"#":[1,2],"#helay Linlady":[3,6],"#helay Linlady#Todo List":[5,6],"#- [ ] min_probability 過濾機率值改用最小與最大範圍值":[7,8],"#- [ ] min_appearances 過濾出現次數改用最小與最大範圍值":[9,12],"#- [ ] min_appearances 過濾出現次數改用最小與最大範圍值#{1}":[11,12],"#Gemini":[13,100],"#Gemini#{1}":[15,95],"#Gemini#{2}":[96,96],"#Gemini#{3}":[97,97],"#Gemini#{4}":[98,100]},"outlinks":[{"title":"A0031_0.0.0.3_統計所有指定期數內gx_wins_cum7_14_21patterns的下n期中球機率排名_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\src\\A0031_0.0.0.3_統計所有指定期數內gx_wins_cum7_14_21patterns的下n期中球機率排名_standalone.jl \"f:\\work\\JuliaProject\\StandAlone\\src\\A0031_0.0.0.3_統計所有指定期數內gx_wins_cum7_14_21patterns的下n期中球機率排名_standalone.jl\"","line":21}]},