#!/usr/bin/env julia

# 測試 WonderGridEngine 模組
println("開始測試 WonderGridEngine 模組...")

try
    using Dates
    include("src/WonderGridEngine.jl")
    println("✓ WonderGridEngine 模組載入成功")

    # 測試基本功能
    using .WonderGridEngine
    
    # 創建測試數據
    test_drawings = [
        WonderGridEngine.Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
        WonderGridEngine.Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
        WonderGridEngine.Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47]),
        WonderGridEngine.Drawing(4, :Lotto6_49, Date(2024, 1, 4), [4, 10, 17, 30, 37, 48]),
        WonderGridEngine.Drawing(5, :Lotto6_49, Date(2024, 1, 5), [1, 11, 18, 31, 38, 49])
    ]
    
    println("✓ 測試數據創建成功")
    
    # 測試配置創建
    config = WonderGridEngine.WonderGridConfig()
    println("✓ 配置創建成功")
    
    # 測試關鍵號碼選擇
    key_result = WonderGridEngine.select_key_number(test_drawings, :ffg)
    println("✓ 關鍵號碼選擇成功: $(key_result.number)")
    
    println("✓ 所有基本測試通過")
    
catch e
    println("✗ 測試失敗: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

println("測試完成")
