"smart_sources:notes/saliu/Duke Keno Strategy, Systems, Best Pairs, Winning Numbers.md": {"path":"notes/saliu/Duke Keno Strategy, Systems, Best Pairs, Winning Numbers.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.15304385,-0.0052571,-0.06023407,-0.0100347,-0.07775148,0.04250959,0.05181723,0.02101227,0.07363176,-0.00670977,-0.02104886,-0.01286765,0.05791397,0.03784164,-0.02401018,-0.01912503,-0.00755403,-0.00460506,-0.07833334,0.01959205,0.04691948,-0.09974816,-0.05675219,-0.0427581,0.03165251,-0.07308922,0.00943507,-0.02887821,-0.02942225,-0.21286006,0.00532216,0.00201081,0.01868547,-0.04995917,-0.08043337,-0.02182115,-0.01185123,0.05517295,-0.07554422,0.00860076,-0.00307813,0.00934617,0.02651946,-0.0006492,0.01148852,-0.02240172,-0.03045239,0.01153422,0.04409691,-0.01359574,0.0056462,0.00327507,-0.03820204,0.01350022,0.09025349,0.07783129,0.05969872,0.02921309,0.0078361,0.04864606,0.05542171,0.05185622,-0.19793247,0.04680936,0.0245783,0.01276929,0.01615053,0.01229038,-0.05139443,0.05381618,0.01918814,0.04082322,-0.02893181,0.01587106,0.00135795,0.03160375,-0.11257759,-0.04005674,-0.04444338,0.01855991,-0.05586249,-0.01505673,0.04187724,0.00214305,0.02946977,0.0483186,0.0343022,0.03946633,0.03698763,-0.02563521,0.03896151,0.06182091,0.05845711,0.01393418,-0.00910489,0.03292985,0.03035542,-0.03926484,-0.04332574,0.11287115,-0.02662455,0.00154182,-0.01388197,-0.03122446,0.02447383,-0.05190572,-0.02321707,-0.04507821,-0.00867887,-0.00229609,0.01561417,-0.01566342,0.0248086,-0.01294,-0.01561224,0.05194705,0.04517921,0.03700863,0.05458713,0.01504171,-0.03873711,0.05500279,0.01721357,-0.01611392,0.0186934,0.01228319,0.05673471,0.05061818,0.03262389,0.03604812,0.02045165,-0.02763882,-0.08512753,-0.06399415,-0.06212747,-0.02489852,0.0283196,0.00304227,0.00536283,-0.00394558,0.02391155,-0.03801151,0.08810753,-0.10607842,-0.00182309,0.04888268,-0.00913782,-0.02897307,-0.03897461,0.02856635,0.00958161,-0.01302651,-0.01127759,-0.06972598,-0.03143652,-0.0032711,0.05512938,0.10603477,-0.03202367,0.03956531,-0.00749551,-0.01835239,-0.03367762,0.08653985,0.03209279,-0.09056328,-0.02924783,0.01003138,-0.02221633,-0.08683886,0.00358163,0.06324344,-0.05756803,0.01342123,0.0854091,-0.02201104,-0.07644789,-0.0358911,-0.01520079,0.00384174,-0.00351653,0.01963267,-0.03620357,0.00597611,0.0073458,-0.06524575,0.00116807,-0.02709018,0.02870835,0.07326227,-0.05181529,0.02152857,0.01168734,0.08765316,-0.00705482,-0.03679055,-0.0567131,-0.06902721,0.02588712,0.03737563,-0.04824122,-0.0611378,-0.02881332,-0.0237145,-0.02682061,0.04306043,0.0089907,-0.06571236,0.07710684,0.0376846,0.00146163,-0.00915726,0.06101146,0.0913073,-0.07638121,0.04357878,-0.01485989,0.04967702,-0.02973865,0.00732112,0.03401488,0.02367991,-0.05064825,-0.22216134,-0.00667342,-0.06119305,-0.02429277,0.05521304,0.01425712,0.05127082,-0.02046142,0.00311795,0.0911004,0.0305169,-0.05937889,-0.03293461,0.02661531,-0.01394942,-0.01105247,-0.09134391,-0.05703849,-0.01897723,0.0202898,0.02952475,0.05650237,-0.05053748,-0.05508563,0.06430786,0.00149856,0.14028156,0.05153859,0.01004256,-0.00897423,0.10529198,0.02215223,-0.02275762,-0.00950293,0.02647895,-0.0197183,0.00681934,-0.04391304,-0.05413245,-0.0208761,-0.10639138,-0.0046768,0.01874017,-0.09170088,-0.03406687,0.00256745,-0.04266961,0.06026382,0.02362869,0.03253823,0.07014479,0.02164163,0.04442828,0.02149421,0.05821332,-0.01330219,-0.09990782,0.00219414,-0.00441841,0.05938814,-0.00999375,-0.0045643,0.00987592,-0.0247415,-0.01609547,-0.0204147,-0.0093401,0.04004249,0.02198908,-0.01814731,0.0086941,0.00230673,-0.00139287,0.0674454,0.02701229,0.0192405,0.03937822,-0.03107344,0.01187874,-0.00618958,0.05145872,-0.09362151,0.0524012,0.02527125,0.00864704,0.03606772,0.0687988,0.06685704,-0.00833474,-0.00860814,-0.03685706,-0.00774474,-0.0147511,0.01820222,0.04499109,0.00555228,-0.25593659,0.00942884,-0.02886936,0.0159577,-0.01877647,-0.00278636,0.03587094,-0.01289253,-0.01803762,-0.05120876,0.07768586,0.0576423,-0.03210407,-0.02428667,-0.03345225,0.01156964,-0.03992023,-0.06604544,0.04610124,0.01083865,0.09285793,0.00260583,0.23516238,0.02885206,0.04352117,0.01258051,-0.0323509,0.0660876,-0.04243448,0.0056043,-0.00064111,0.08773973,0.05254762,-0.07838035,-0.01311079,0.03082553,-0.02663664,-0.00389808,-0.02669597,-0.01029303,-0.07470068,0.01776974,-0.01150793,0.01023584,0.14034441,0.0171032,-0.01998924,-0.06388611,0.04015151,0.04302558,-0.05643883,-0.06333285,-0.00731002,-0.03853877,0.04784948,0.05238502,0.0224113,-0.0418487,-0.00463213,-0.00525492,0.00058598,0.05211181,0.03568605,0.00969693,-0.00415877],"last_embed":{"hash":"yhczft","tokens":474}}},"last_read":{"hash":"yhczft","at":1753423454453},"class_name":"SmartSource","last_import":{"mtime":1735905299000,"size":7107,"at":1753230880657,"hash":"yhczft"},"blocks":{"#---frontmatter---":[1,6],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers":[8,77],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#{1}":[10,29],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#{2}":[30,31],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#{3}":[32,33],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#{4}":[34,34],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#{5}":[35,35],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#{6}":[36,37],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#{7}":[38,41],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>":[42,77],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{1}":[44,45],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{2}":[46,46],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{3}":[47,47],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{4}":[48,48],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{5}":[49,49],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{6}":[50,50],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{7}":[51,51],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{8}":[52,52],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{9}":[53,53],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{10}":[54,54],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{11}":[55,55],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{12}":[56,56],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{13}":[57,58],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{14}":[59,62],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{15}":[63,63],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{16}":[64,64],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{17}":[65,65],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{18}":[66,66],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{19}":[67,67],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{20}":[68,68],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{21}":[69,69],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{22}":[70,71],"#Duke Keno Strategy, Systems, Best Pairs, Winning Numbers#<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>#{23}":[72,77]},"outlinks":[{"title":"Duke","target":"mailto:<EMAIL>","line":14},{"title":"_**5% Frequency: The lowest lotto number in a lottery drawing is OVER 19**_","target":"https://saliu.com/bbs/messages/898.html","line":16},{"title":"Keno systems from the top pairs of the best keno numbers can win half million dollars.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":28},{"title":"The best lotto software analyzes keno drawings to generate winning numbers, systems.","target":"https://saliu.com/ScreenImgs/keno.gif","line":32},{"title":"_**Odds Calculator, Number Combination Generator**_ **for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Keno**","target":"https://saliu.com/gambling-lottery-lotto/odds-generator.html","line":36},{"title":"The best Keno random numbers, combinations generator is free to download and run on your computer.","target":"https://saliu.com/ScreenImgs/keno-generator.gif","line":38},{"title":"\n\n## <u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>\n\n","target":"https://saliu.com/content/lottery.html","line":40},{"title":"**Lottery, Software, Strategies**","target":"https://saliu.com/LottoWin.htm","line":46},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":48},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":50},{"title":"_**Book, Tutorial, Manual of Lotto, Lottery Software, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":52},{"title":"**Skips Lottery, Gambling Systems**","target":"https://saliu.com/skip-strategy.html","line":53},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":54},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":55},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":57},{"title":"The idea is to apply the Power Lotto Strategy to the Keno game.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":59},{"title":"_**Lotto system: Play top 10% of lotto number pairs**_","target":"https://saliu.com/bbs/messages/916.html","line":63},{"title":"_**Lottery system, strategy: Lotto sum-total, sums parameter**_","target":"https://saliu.com/bbs/messages/915.html","line":64},{"title":"_**Winning lotto numbers in the last 10 lottery drawings**_","target":"https://saliu.com/bbs/messages/914.html","line":65},{"title":"_**Eliminate the last 4 or 5 lotto numbers to create a starting point for a lotto system**_","target":"https://saliu.com/bbs/messages/913.html","line":66},{"title":"_**Lottery players use the sums of lotto combinations as system**_","target":"https://saliu.com/bbs/messages/912.html","line":67},{"title":"_**Statistics of highest lotto 6-49 number UNDER 33, Sum-total UNDER 100**_","target":"https://saliu.com/bbs/messages/910.html","line":68},{"title":"_**Lotto system: Eliminate lowest 5 and highest 5 lotto numbers**_","target":"https://saliu.com/bbs/messages/909.html","line":69},{"title":"_**Lottery software generates fewer than 10 lotto combinations for jackpot**_","target":"https://saliu.com/bbs/messages/899.html","line":70},{"title":"The idea is to apply the Wonder-Grid lotto strategy to Keno lottery games drawing 20 numbers.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":72},{"title":"Forums","target":"https://forums.saliu.com/","line":74},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":74},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":74},{"title":"Contents","target":"https://saliu.com/content/index.html","line":74},{"title":"Home","target":"https://saliu.com/index.htm","line":74},{"title":"Software","target":"https://saliu.com/infodown.html","line":74},{"title":"Search","target":"https://saliu.com/Search.htm","line":74},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":74},{"title":"Winning lotto strategy, systems to Keno games was suggested by a player named Duke.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":76}],"metadata":{"created":"2025-01-03T19:54:58 (UTC +08:00)","tags":["keno","strategy","system","wonder grid","lotto","software","lottery","pairs","numbers","Duke","Duke of Gambling"],"source":"https://saliu.com/bbs/messages/907.html","author":null},"key":"notes/saliu/Duke Keno Strategy, Systems, Best Pairs, Winning Numbers.md"},