"smart_sources:PromptingTools.jl/Reference for RAGTools - PromptingTools.jl.md": {"path":"PromptingTools.jl/Reference for RAGTools - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12096047,0.00767196,-0.02002908,-0.05507692,-0.02938532,0.01707192,-0.0253212,0.05019225,0.04060506,-0.02792107,-0.00970303,-0.01127082,-0.00305893,0.04253466,-0.02081881,0.02680981,-0.04195989,0.04043617,-0.06760138,-0.03240288,0.09320279,0.05583356,0.02185465,-0.03569382,0.03481455,0.05376096,0.00654029,-0.03021543,-0.0168573,-0.1893684,0.04641161,-0.01669294,0.0053871,0.00214624,-0.0442345,-0.0208846,-0.04683715,0.05187893,-0.01517376,0.00606599,0.03224799,0.01223629,-0.0192503,-0.00471939,-0.05924715,-0.08047532,-0.01923916,-0.02086233,-0.00837998,-0.05170661,-0.0123363,-0.03297335,0.05135924,-0.01794574,0.01542301,0.00724672,0.00306515,0.10109735,0.03027894,0.00887956,0.03167339,0.0164119,-0.19379564,0.1487696,0.01574723,0.01570911,-0.01028653,0.00338579,0.02813784,0.03492281,-0.0381488,0.03329406,-0.00393107,0.07379108,0.01531575,-0.03038101,-0.014553,-0.02843177,-0.0109433,-0.09742109,-0.03523438,-0.00783337,-0.02215254,-0.02179228,-0.04727443,0.01143828,0.02040752,-0.0179069,0.0719023,-0.00293636,-0.00324315,-0.09404826,0.00716183,0.02514176,-0.05944025,-0.01744602,0.02247505,-0.02940292,-0.10199742,0.12495317,-0.0208209,-0.01498669,0.01878041,-0.01040818,0.027971,-0.06487561,0.00169484,-0.02553724,-0.03120658,0.03681549,-0.00681624,-0.02971119,0.00147258,-0.06346238,0.00353805,-0.03696466,0.00586365,-0.02232803,0.02131092,-0.05098095,0.00997786,0.01839054,0.0191574,0.00330912,-0.00952236,0.04197216,0.0237696,0.05153567,-0.00222114,0.00407878,0.0500136,0.00845388,-0.06209796,-0.03333435,0.00066531,0.06316667,0.04884767,-0.06823695,0.03020058,-0.01136005,0.02741301,-0.01219556,0.00660397,-0.10351265,-0.00381992,0.04086152,-0.02795724,-0.0185896,-0.01350717,-0.04099263,0.03634362,0.05482208,-0.05482907,0.01260608,0.05697238,0.0439217,0.07408783,0.08458696,-0.010904,0.0147086,-0.05734317,-0.0432753,-0.00782935,0.1439407,0.01445123,-0.05061275,-0.09711903,-0.04165518,-0.05333868,-0.04130235,0.01356751,0.04736701,-0.04818188,-0.00986684,0.07621827,0.04170788,-0.06056654,0.03077291,0.00199915,0.02850802,0.03340924,-0.06876074,-0.04889107,0.02210205,-0.01945814,-0.01529388,-0.03077243,-0.08738484,-0.00506563,0.06227909,-0.07666895,0.02546144,0.06279022,-0.0303399,0.00508605,-0.00010451,-0.01449117,-0.06570195,0.07168391,-0.04419558,0.06285747,0.05019842,0.03547401,0.02918811,-0.00925358,0.05543925,0.05586746,-0.01868509,0.10455177,0.03738721,-0.09696859,-0.0610407,0.06389077,0.00040818,-0.0092875,-0.01041786,-0.0484184,0.01177272,0.00878383,0.06350682,-0.03514171,0.00358046,-0.04989165,-0.23050624,0.02600894,0.02795376,-0.04442706,0.05511498,-0.07602483,0.09431511,-0.04448463,-0.04927355,0.04592786,0.08973621,-0.03157263,0.04234413,-0.00682408,-0.02731652,-0.00603997,-0.03514139,0.00622318,-0.01017087,0.04617138,0.00936644,-0.00064972,0.03842793,-0.07995422,0.00964248,-0.02532429,0.10477441,0.04348741,0.03621858,-0.09267467,0.01227561,-0.0019506,0.06287017,-0.1212184,-0.02124878,0.02935797,-0.03300437,0.05609925,0.09918015,0.02764335,-0.03192446,0.03976952,-0.00760456,-0.03588314,-0.00510322,-0.01806418,-0.04228914,-0.0547124,-0.0307257,0.02046805,0.01941224,-0.0510744,0.01426694,-0.01317714,0.02459344,-0.01074513,-0.0302882,-0.06027736,0.00989136,0.00306928,0.0267668,-0.02516168,-0.00415174,-0.05169163,0.04589159,0.0415683,0.0260685,0.00716754,0.06108389,0.01779186,-0.01860838,0.09069848,0.03625574,0.06959711,-0.01307059,0.0603733,-0.00439207,-0.06780598,-0.01625527,-0.04677702,-0.04294529,0.03191919,0.01407258,0.0307525,0.01993535,0.04542386,0.0363782,-0.03423851,0.0944913,-0.05178735,-0.02602215,0.01080124,0.01525172,-0.03971872,0.04216493,0.02976454,-0.24456857,0.03426432,0.00838266,0.01839011,-0.02188163,0.08085717,0.04714914,-0.00740079,0.00707987,0.05150025,-0.03603425,0.00188374,0.01409316,-0.01948107,0.04242532,0.04901571,0.03599187,-0.00585168,0.02652665,-0.07005394,0.03075101,0.02169304,0.24174201,-0.02141988,0.00302695,-0.00593989,0.00909473,-0.0626341,0.081903,0.04326649,0.0100989,0.06104109,0.14942603,-0.009793,0.04004376,0.00169571,-0.04956006,-0.00813561,0.03245947,0.01288468,-0.0323304,0.04281383,-0.06739037,-0.00165585,0.0574338,-0.01548143,-0.01201541,-0.0871276,-0.06024862,0.03521483,-0.02002847,-0.0007527,-0.03340764,-0.00919402,0.00102893,-0.01435449,0.05315243,0.01637414,-0.07248089,-0.00553639,0.06712274,-0.00797729,0.09256925,-0.0028408,-0.01253289],"last_embed":{"hash":"c6332306fa96ad911a9827e21ad9bba56d267ff7c9bd80a76e1728c9507d585d","tokens":465}}},"last_read":{"hash":"c6332306fa96ad911a9827e21ad9bba56d267ff7c9bd80a76e1728c9507d585d","at":1745995225465},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/reference_ragtools/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"​","target":"#Reference-for-RAGTools","line":243},{"title":"`PromptingTools.Experimental.RAGTools.AbstractCandidateChunks`","target":"#PromptingTools.Experimental.RAGTools.AbstractCandidateChunks","line":245},{"title":"`PromptingTools.Experimental.RAGTools.AbstractChunkIndex`","target":"#PromptingTools.Experimental.RAGTools.AbstractChunkIndex","line":246},{"title":"`PromptingTools.Experimental.RAGTools.AbstractGenerator`","target":"#PromptingTools.Experimental.RAGTools.AbstractGenerator","line":247},{"title":"`PromptingTools.Experimental.RAGTools.AbstractIndexBuilder`","target":"#PromptingTools.Experimental.RAGTools.AbstractIndexBuilder","line":248},{"title":"`PromptingTools.Experimental.RAGTools.AbstractMultiIndex`","target":"#PromptingTools.Experimental.RAGTools.AbstractMultiIndex","line":249},{"title":"`PromptingTools.Experimental.RAGTools.AbstractRetriever`","target":"#PromptingTools.Experimental.RAGTools.AbstractRetriever","line":250},{"title":"`PromptingTools.Experimental.RAGTools.AdvancedGenerator`","target":"#PromptingTools.Experimental.RAGTools.AdvancedGenerator","line":251},{"title":"`PromptingTools.Experimental.RAGTools.AdvancedRetriever`","target":"#PromptingTools.Experimental.RAGTools.AdvancedRetriever","line":252},{"title":"`PromptingTools.Experimental.RAGTools.AnnotatedNode`","target":"#PromptingTools.Experimental.RAGTools.AnnotatedNode","line":253},{"title":"`PromptingTools.Experimental.RAGTools.AnyTagFilter`","target":"#PromptingTools.Experimental.RAGTools.AnyTagFilter","line":254},{"title":"`PromptingTools.Experimental.RAGTools.BatchEmbedder`","target":"#PromptingTools.Experimental.RAGTools.BatchEmbedder","line":255},{"title":"`PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity`","target":"#PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity","line":256},{"title":"`PromptingTools.Experimental.RAGTools.CandidateChunks`","target":"#PromptingTools.Experimental.RAGTools.CandidateChunks","line":257},{"title":"`PromptingTools.Experimental.RAGTools.ChunkIndex`","target":"#PromptingTools.Experimental.RAGTools.ChunkIndex","line":258},{"title":"`PromptingTools.Experimental.RAGTools.CohereReranker`","target":"#PromptingTools.Experimental.RAGTools.CohereReranker","line":259},{"title":"`PromptingTools.Experimental.RAGTools.ContextEnumerator`","target":"#PromptingTools.Experimental.RAGTools.ContextEnumerator","line":260},{"title":"`PromptingTools.Experimental.RAGTools.CosineSimilarity`","target":"#PromptingTools.Experimental.RAGTools.CosineSimilarity","line":261},{"title":"`PromptingTools.Experimental.RAGTools.FileChunker`","target":"#PromptingTools.Experimental.RAGTools.FileChunker","line":262},{"title":"`PromptingTools.Experimental.RAGTools.HTMLStyler`","target":"#PromptingTools.Experimental.RAGTools.HTMLStyler","line":263},{"title":"`PromptingTools.Experimental.RAGTools.HyDERephraser`","target":"#PromptingTools.Experimental.RAGTools.HyDERephraser","line":264},{"title":"`PromptingTools.Experimental.RAGTools.JudgeAllScores`","target":"#PromptingTools.Experimental.RAGTools.JudgeAllScores","line":265},{"title":"`PromptingTools.Experimental.RAGTools.JudgeRating`","target":"#PromptingTools.Experimental.RAGTools.JudgeRating","line":266},{"title":"`PromptingTools.Experimental.RAGTools.MultiIndex`","target":"#PromptingTools.Experimental.RAGTools.MultiIndex","line":267},{"title":"`PromptingTools.Experimental.RAGTools.NoPostprocessor`","target":"#PromptingTools.Experimental.RAGTools.NoPostprocessor","line":268},{"title":"`PromptingTools.Experimental.RAGTools.NoRefiner`","target":"#PromptingTools.Experimental.RAGTools.NoRefiner","line":269},{"title":"`PromptingTools.Experimental.RAGTools.NoRephraser`","target":"#PromptingTools.Experimental.RAGTools.NoRephraser","line":270},{"title":"`PromptingTools.Experimental.RAGTools.NoReranker`","target":"#PromptingTools.Experimental.RAGTools.NoReranker","line":271},{"title":"`PromptingTools.Experimental.RAGTools.NoTagFilter`","target":"#PromptingTools.Experimental.RAGTools.NoTagFilter","line":272},{"title":"`PromptingTools.Experimental.RAGTools.NoTagger`","target":"#PromptingTools.Experimental.RAGTools.NoTagger","line":273},{"title":"`PromptingTools.Experimental.RAGTools.OpenTagger`","target":"#PromptingTools.Experimental.RAGTools.OpenTagger","line":274},{"title":"`PromptingTools.Experimental.RAGTools.PassthroughTagger`","target":"#PromptingTools.Experimental.RAGTools.PassthroughTagger","line":275},{"title":"`PromptingTools.Experimental.RAGTools.RAGConfig`","target":"#PromptingTools.Experimental.RAGTools.RAGConfig","line":276},{"title":"`PromptingTools.Experimental.RAGTools.RAGResult`","target":"#PromptingTools.Experimental.RAGTools.RAGResult","line":277},{"title":"`PromptingTools.Experimental.RAGTools.SimpleAnswerer`","target":"#PromptingTools.Experimental.RAGTools.SimpleAnswerer","line":278},{"title":"`PromptingTools.Experimental.RAGTools.SimpleGenerator`","target":"#PromptingTools.Experimental.RAGTools.SimpleGenerator","line":279},{"title":"`PromptingTools.Experimental.RAGTools.SimpleIndexer`","target":"#PromptingTools.Experimental.RAGTools.SimpleIndexer","line":280},{"title":"`PromptingTools.Experimental.RAGTools.SimpleRefiner`","target":"#PromptingTools.Experimental.RAGTools.SimpleRefiner","line":281},{"title":"`PromptingTools.Experimental.RAGTools.SimpleRephraser`","target":"#PromptingTools.Experimental.RAGTools.SimpleRephraser","line":282},{"title":"`PromptingTools.Experimental.RAGTools.SimpleRetriever`","target":"#PromptingTools.Experimental.RAGTools.SimpleRetriever","line":283},{"title":"`PromptingTools.Experimental.RAGTools.Styler`","target":"#PromptingTools.Experimental.RAGTools.Styler","line":284},{"title":"`PromptingTools.Experimental.RAGTools.TextChunker`","target":"#PromptingTools.Experimental.RAGTools.TextChunker","line":285},{"title":"`PromptingTools.Experimental.RAGTools.TrigramAnnotater`","target":"#PromptingTools.Experimental.RAGTools.TrigramAnnotater","line":286},{"title":"`PromptingTools.Experimental.RAGTools._normalize`","target":"#PromptingTools.Experimental.RAGTools._normalize","line":287},{"title":"`PromptingTools.Experimental.RAGTools.add_node_metadata!`","target":"#PromptingTools.Experimental.RAGTools.add_node_metadata!-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AnnotatedNode}","line":288},{"title":"`PromptingTools.Experimental.RAGTools.airag`","target":"#PromptingTools.Experimental.RAGTools.airag-Tuple{PromptingTools.Experimental.RAGTools.AbstractRAGConfig, PromptingTools.Experimental.RAGTools.AbstractChunkIndex}","line":289},{"title":"`PromptingTools.Experimental.RAGTools.align_node_styles!`","target":"#PromptingTools.Experimental.RAGTools.align_node_styles!-Tuple{TrigramAnnotater, AbstractVector{<:PromptingTools.Experimental.RAGTools.AnnotatedNode}}","line":290},{"title":"`PromptingTools.Experimental.RAGTools.annotate_support`","target":"#PromptingTools.Experimental.RAGTools.annotate_support-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":291},{"title":"`PromptingTools.Experimental.RAGTools.annotate_support`","target":"#PromptingTools.Experimental.RAGTools.annotate_support-Tuple{TrigramAnnotater, AbstractString, AbstractVector}","line":292},{"title":"`PromptingTools.Experimental.RAGTools.answer!`","target":"#PromptingTools.Experimental.RAGTools.answer!-Tuple{PromptingTools.Experimental.RAGTools.SimpleAnswerer, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":293},{"title":"`PromptingTools.Experimental.RAGTools.build_context`","target":"#PromptingTools.Experimental.RAGTools.build_context-Tuple{PromptingTools.Experimental.RAGTools.ContextEnumerator, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, CandidateChunks}","line":294},{"title":"`PromptingTools.Experimental.RAGTools.build_index`","target":"#PromptingTools.Experimental.RAGTools.build_index-Tuple{PromptingTools.Experimental.RAGTools.AbstractIndexBuilder, Vector{<:AbstractString}}","line":295},{"title":"`PromptingTools.Experimental.RAGTools.build_qa_evals`","target":"#PromptingTools.Experimental.RAGTools.build_qa_evals-Tuple{Vector{<:AbstractString}, Vector{<:AbstractString}}","line":296},{"title":"`PromptingTools.Experimental.RAGTools.build_tags`","target":"#PromptingTools.Experimental.RAGTools.build_tags","line":297},{"title":"`PromptingTools.Experimental.RAGTools.build_tags`","target":"#PromptingTools.Experimental.RAGTools.build_tags-Tuple{PromptingTools.Experimental.RAGTools.AbstractTagger, Nothing}","line":298},{"title":"`PromptingTools.Experimental.RAGTools.cohere_api`","target":"#PromptingTools.Experimental.RAGTools.cohere_api-Tuple{}","line":299},{"title":"`PromptingTools.Experimental.RAGTools.find_closest`","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity, AbstractMatrix{<:Bool}, AbstractVector{<:Real}}","line":300},{"title":"`PromptingTools.Experimental.RAGTools.find_closest`","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.AbstractSimilarityFinder, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractVector{<:Real}}","line":301},{"title":"`PromptingTools.Experimental.RAGTools.find_closest`","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.CosineSimilarity, AbstractMatrix{<:Real}, AbstractVector{<:Real}}","line":302},{"title":"`PromptingTools.Experimental.RAGTools.find_tags`","target":"#PromptingTools.Experimental.RAGTools.find_tags-Union{Tuple{T}, Tuple{PromptingTools.Experimental.RAGTools.NoTagFilter, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, Union{AbstractVector{<:T}, T}}} where T<:Union{Regex, AbstractString}","line":303},{"title":"`PromptingTools.Experimental.RAGTools.find_tags`","target":"#PromptingTools.Experimental.RAGTools.find_tags-Tuple{PromptingTools.Experimental.RAGTools.AnyTagFilter, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, Union{Regex, AbstractString}}","line":304},{"title":"`PromptingTools.Experimental.RAGTools.generate!`","target":"#PromptingTools.Experimental.RAGTools.generate!-Tuple{PromptingTools.Experimental.RAGTools.AbstractGenerator, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":305},{"title":"`PromptingTools.Experimental.RAGTools.get_chunks`","target":"#PromptingTools.Experimental.RAGTools.get_chunks-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunker, Vector{<:AbstractString}}","line":306},{"title":"`PromptingTools.Experimental.RAGTools.get_embeddings`","target":"#PromptingTools.Experimental.RAGTools.get_embeddings-Tuple{PromptingTools.Experimental.RAGTools.BatchEmbedder, AbstractVector{<:AbstractString}}","line":307},{"title":"`PromptingTools.Experimental.RAGTools.get_tags`","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.PassthroughTagger, AbstractVector{<:AbstractString}}","line":308},{"title":"`PromptingTools.Experimental.RAGTools.get_tags`","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.NoTagger, AbstractVector{<:AbstractString}}","line":309},{"title":"`PromptingTools.Experimental.RAGTools.get_tags`","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.OpenTagger, AbstractVector{<:AbstractString}}","line":310},{"title":"`PromptingTools.Experimental.RAGTools.hamming_distance`","target":"#PromptingTools.Experimental.RAGTools.hamming_distance-Tuple{AbstractMatrix{<:Bool}, AbstractVector{<:Bool}}","line":311},{"title":"`PromptingTools.Experimental.RAGTools.load_text`","target":"#PromptingTools.Experimental.RAGTools.load_text-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunker, Any}","line":312},{"title":"`PromptingTools.Experimental.RAGTools.print_html`","target":"#PromptingTools.Experimental.RAGTools.print_html-Tuple{IO, PromptingTools.Experimental.RAGTools.AbstractAnnotatedNode}","line":313},{"title":"`PromptingTools.Experimental.RAGTools.refine!`","target":"#PromptingTools.Experimental.RAGTools.refine!-Tuple{PromptingTools.Experimental.RAGTools.SimpleRefiner, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":314},{"title":"`PromptingTools.Experimental.RAGTools.refine!`","target":"#PromptingTools.Experimental.RAGTools.refine!-Tuple{PromptingTools.Experimental.RAGTools.NoRefiner, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":315},{"title":"`PromptingTools.Experimental.RAGTools.rephrase`","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.NoRephraser, AbstractString}","line":316},{"title":"`PromptingTools.Experimental.RAGTools.rephrase`","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.SimpleRephraser, AbstractString}","line":317},{"title":"`PromptingTools.Experimental.RAGTools.rephrase`","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.HyDERephraser, AbstractString}","line":318},{"title":"`PromptingTools.Experimental.RAGTools.rerank`","target":"#PromptingTools.Experimental.RAGTools.rerank-Tuple{PromptingTools.Experimental.RAGTools.CohereReranker, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractString, PromptingTools.Experimental.RAGTools.AbstractCandidateChunks}","line":319},{"title":"`PromptingTools.Experimental.RAGTools.retrieve`","target":"#PromptingTools.Experimental.RAGTools.retrieve-Tuple{PromptingTools.Experimental.RAGTools.AbstractRetriever, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractString}","line":320},{"title":"`PromptingTools.Experimental.RAGTools.run_qa_evals`","target":"#PromptingTools.Experimental.RAGTools.run_qa_evals-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractVector{<:PromptingTools.Experimental.RAGTools.QAEvalItem}}","line":321},{"title":"`PromptingTools.Experimental.RAGTools.run_qa_evals`","target":"#PromptingTools.Experimental.RAGTools.run_qa_evals-Tuple{PromptingTools.Experimental.RAGTools.QAEvalItem, RAGResult}","line":322},{"title":"`PromptingTools.Experimental.RAGTools.score_retrieval_hit`","target":"#PromptingTools.Experimental.RAGTools.score_retrieval_hit-Tuple{AbstractString, Vector{<:AbstractString}}","line":323},{"title":"`PromptingTools.Experimental.RAGTools.score_retrieval_rank`","target":"#PromptingTools.Experimental.RAGTools.score_retrieval_rank-Tuple{AbstractString, Vector{<:AbstractString}}","line":324},{"title":"`PromptingTools.Experimental.RAGTools.set_node_style!`","target":"#PromptingTools.Experimental.RAGTools.set_node_style!-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AnnotatedNode}","line":325},{"title":"`PromptingTools.Experimental.RAGTools.split_into_code_and_sentences`","target":"#PromptingTools.Experimental.RAGTools.split_into_code_and_sentences-Tuple{Union{SubString{String}, String}}","line":326},{"title":"`PromptingTools.Experimental.RAGTools.tags_extract`","target":"#PromptingTools.Experimental.RAGTools.tags_extract-Tuple{PromptingTools.Experimental.RAGTools.Tag}","line":327},{"title":"`PromptingTools.Experimental.RAGTools.token_with_boundaries`","target":"#PromptingTools.Experimental.RAGTools.token_with_boundaries-Tuple{Union{Nothing, AbstractString}, AbstractString, Union{Nothing, AbstractString}}","line":328},{"title":"`PromptingTools.Experimental.RAGTools.tokenize`","target":"#PromptingTools.Experimental.RAGTools.tokenize-Tuple{Union{SubString{String}, String}}","line":329},{"title":"`PromptingTools.Experimental.RAGTools.trigram_support!`","target":"#PromptingTools.Experimental.RAGTools.trigram_support!-Union{Tuple{F2}, Tuple{F1}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector, F1}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector, F1, F2}} where {F1<:Function, F2<:Function}","line":330},{"title":"`PromptingTools.Experimental.RAGTools.trigrams`","target":"#PromptingTools.Experimental.RAGTools.trigrams-Tuple{AbstractString}","line":331},{"title":"`PromptingTools.Experimental.RAGTools.trigrams_hashed`","target":"#PromptingTools.Experimental.RAGTools.trigrams_hashed-Tuple{AbstractString}","line":332},{"title":"#","target":"#PromptingTools.Experimental.RAGTools","line":334},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/RAGTools.jl#L1-L9","line":348},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AbstractCandidateChunks","line":352},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/rag_interface.jl#L140-L151","line":373},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AbstractChunkIndex","line":377},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/rag_interface.jl#L122-L135","line":404},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AbstractGenerator","line":408},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/rag_interface.jl#L187-L197","line":429},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AbstractIndexBuilder","line":433},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/rag_interface.jl#L93-L102","line":452},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AbstractMultiIndex","line":456},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/rag_interface.jl#L115-L119","line":466},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AbstractRetriever","line":470},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/rag_interface.jl#L158-L168","line":491},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AdvancedGenerator","line":495},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L260-L266","line":507},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AdvancedRetriever","line":511},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L488-L501","line":536},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AnnotatedNode","line":540},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L53-L65","line":563},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.AnyTagFilter","line":567},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L57-L61","line":577},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.BatchEmbedder","line":581},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L24-L28","line":591},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity","line":595},{"title":"HuggingFace: Embedding Quantization","target":"https://huggingface.co/blog/embedding-quantization#binary-quantization-in-vector-databases","line":612},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L36-L46","line":614},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.CandidateChunks","line":618},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/types.jl#L96-L106","line":637},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.ChunkIndex","line":641},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/types.jl#L5-L18","line":668},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.CohereReranker","line":672},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L363-L367","line":682},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.ContextEnumerator","line":686},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L2-L6","line":696},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.CosineSimilarity","line":700},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L29-L33","line":710},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.FileChunker","line":714},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L5-L13","line":728},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.HTMLStyler","line":732},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L39-L43","line":742},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.HyDERephraser","line":746},{"title":"Arxiv paper","target":"https://arxiv.org/abs/2212.10496","line":758},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L17-L26","line":760},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.JudgeAllScores","line":764},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/evaluation.jl#L32","line":768},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.JudgeRating","line":772},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/evaluation.jl#L26","line":776},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.MultiIndex","line":780},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/types.jl#L74","line":784},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.NoPostprocessor","line":788},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L226-L232","line":800},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.NoRefiner","line":804},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L135-L139","line":814},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.NoRephraser","line":818},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L3-L7","line":828},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.NoReranker","line":832},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L356-L360","line":842},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.NoTagFilter","line":846},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L49-L54","line":856},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.NoTagger","line":860},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L32-L36","line":870},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.OpenTagger","line":874},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L46-L51","line":884},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.PassthroughTagger","line":888},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L39-L43","line":898},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.RAGConfig","line":902},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L390-L396","line":914},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.RAGResult","line":918},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/types.jl#L276-L297","line":957},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.SimpleAnswerer","line":961},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L72-L76","line":971},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.SimpleGenerator","line":975},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L246-L252","line":987},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.SimpleIndexer","line":991},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L64-L70","line":1003},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.SimpleRefiner","line":1007},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L142-L146","line":1017},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.SimpleRephraser","line":1021},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L10-L14","line":1031},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.SimpleRetriever","line":1035},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L464-L478","line":1062},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.Styler","line":1066},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L27-L31","line":1076},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.TextChunker","line":1080},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L16-L20","line":1090},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.TrigramAnnotater","line":1094},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L138-L144","line":1106},{"title":"#","target":"#PromptingTools.Experimental.RAGTools._normalize","line":1110},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L82","line":1114},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.add_node_metadata!-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AnnotatedNode}","line":1118},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L314-L324","line":1134},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.airag-Tuple{PromptingTools.Experimental.RAGTools.AbstractRAGConfig, PromptingTools.Experimental.RAGTools.AbstractChunkIndex}","line":1138},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L403-L501","line":1275},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.align_node_styles!-Tuple{TrigramAnnotater, AbstractVector{<:PromptingTools.Experimental.RAGTools.AnnotatedNode}}","line":1279},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L187-L193","line":1291},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.annotate_support-Tuple{TrigramAnnotater, AbstractString, AbstractVector}","line":1295},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L412-L455","line":1363},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.annotate_support-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":1367},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L505-L524","line":1395},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.answer!-Tuple{PromptingTools.Experimental.RAGTools.SimpleAnswerer, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":1399},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L85-L107","line":1435},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.build_context-Tuple{PromptingTools.Experimental.RAGTools.ContextEnumerator, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, CandidateChunks}","line":1439},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L9-L38","line":1482},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.build_index-Tuple{PromptingTools.Experimental.RAGTools.AbstractIndexBuilder, Vector{<:AbstractString}}","line":1486},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L339-L405","line":1574},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.build_qa_evals-Tuple{Vector{<:AbstractString}, Vector{<:AbstractString}}","line":1578},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/evaluation.jl#L65-L100","line":1634},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.build_tags","line":1638},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/rag_interface.jl#L232","line":1642},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.build_tags-Tuple{PromptingTools.Experimental.RAGTools.AbstractTagger, Nothing}","line":1646},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L328-L334","line":1658},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.cohere_api-Tuple{}","line":1662},{"title":"https://cohere.com/docs","target":"https://cohere.com/docs","line":1675},{"title":"https://dashboard.cohere.com/welcome/register","target":"https://dashboard.cohere.com/welcome/register","line":1679},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/api_services.jl#L1-L17","line":1690},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.AbstractSimilarityFinder, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractVector{<:Real}}","line":1694},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L171-L180","line":1709},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.BinaryCosineSimilarity, AbstractMatrix{<:Bool}, AbstractVector{<:Real}}","line":1713},{"title":"HuggingFace: Embedding Quantization","target":"https://huggingface.co/blog/embedding-quantization#binary-quantization-in-vector-databases","line":1735},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L233-L255","line":1747},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.find_closest-Tuple{PromptingTools.Experimental.RAGTools.CosineSimilarity, AbstractMatrix{<:Real}, AbstractVector{<:Real}}","line":1751},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L143-L156","line":1769},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.find_tags-Tuple{PromptingTools.Experimental.RAGTools.AnyTagFilter, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, Union{Regex, AbstractString}}","line":1773},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L304-L312","line":1787},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.find_tags-Union{Tuple{T}, Tuple{PromptingTools.Experimental.RAGTools.NoTagFilter, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, Union{AbstractVector{<:T}, T}}} where T<:Union{Regex, AbstractString}","line":1791},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L335-L340","line":1802},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.generate!-Tuple{PromptingTools.Experimental.RAGTools.AbstractGenerator, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":1806},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L274-L339","line":1905},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.get_chunks-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunker, Vector{<:AbstractString}}","line":1909},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L110-L130","line":1941},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.get_embeddings-Tuple{PromptingTools.Experimental.RAGTools.BatchEmbedder, AbstractVector{<:AbstractString}}","line":1945},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L165-L192","line":1984},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.NoTagger, AbstractVector{<:AbstractString}}","line":1988},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L253-L258","line":1999},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.OpenTagger, AbstractVector{<:AbstractString}}","line":2003},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L279-L293","line":2029},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.get_tags-Tuple{PromptingTools.Experimental.RAGTools.PassthroughTagger, AbstractVector{<:AbstractString}}","line":2033},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L264-L271","line":2045},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.hamming_distance-Tuple{AbstractMatrix{<:Bool}, AbstractVector{<:Bool}}","line":2049},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L206-L212","line":2061},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.load_text-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunker, Any}","line":2065},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L85-L94","line":2083},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.print_html-Tuple{IO, PromptingTools.Experimental.RAGTools.AbstractAnnotatedNode}","line":2087},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L537-L598","line":2163},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.refine!-Tuple{PromptingTools.Experimental.RAGTools.NoRefiner, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":2167},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L155-L161","line":2179},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.refine!-Tuple{PromptingTools.Experimental.RAGTools.SimpleRefiner, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":2183},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/generation.jl#L172-L196","line":2222},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.HyDERephraser, AbstractString}","line":2226},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L109-L128","line":2256},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.NoRephraser, AbstractString}","line":2260},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L69-L73","line":2270},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.rephrase-Tuple{PromptingTools.Experimental.RAGTools.SimpleRephraser, AbstractString}","line":2274},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L78-L94","line":2302},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.rerank-Tuple{PromptingTools.Experimental.RAGTools.CohereReranker, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractString, PromptingTools.Experimental.RAGTools.AbstractCandidateChunks}","line":2306},{"title":"https://cohere.com/rerank","target":"https://cohere.com/rerank","line":2323},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L386-L412","line":2346},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.retrieve-Tuple{PromptingTools.Experimental.RAGTools.AbstractRetriever, PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractString}","line":2350},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/retrieval.jl#L511-L618","line":2490},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.run_qa_evals-Tuple{PromptingTools.Experimental.RAGTools.AbstractChunkIndex, AbstractVector{<:PromptingTools.Experimental.RAGTools.QAEvalItem}}","line":2494},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/evaluation.jl#L221-L260","line":2548},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.run_qa_evals-Tuple{PromptingTools.Experimental.RAGTools.QAEvalItem, RAGResult}","line":2552},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/evaluation.jl#L145-L181","line":2608},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.score_retrieval_hit-Tuple{AbstractString, Vector{<:AbstractString}}","line":2612},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/evaluation.jl#L131","line":2616},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.score_retrieval_rank-Tuple{AbstractString, Vector{<:AbstractString}}","line":2620},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/evaluation.jl#L138","line":2624},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.set_node_style!-Tuple{TrigramAnnotater, PromptingTools.Experimental.RAGTools.AnnotatedNode}","line":2628},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L147-L157","line":2644},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.split_into_code_and_sentences-Tuple{Union{SubString{String}, String}}","line":2648},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/utils.jl#L194-L201","line":2660},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.tags_extract-Tuple{PromptingTools.Experimental.RAGTools.Tag}","line":2664},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/preparation.jl#L234-L245","line":2684},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.token_with_boundaries-Tuple{Union{Nothing, AbstractString}, AbstractString, Union{Nothing, AbstractString}}","line":2688},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/utils.jl#L114-L120","line":2700},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.tokenize-Tuple{Union{SubString{String}, String}}","line":2704},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/utils.jl#L39-L45","line":2716},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.trigram_support!-Union{Tuple{F2}, Tuple{F1}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector, F1}, Tuple{PromptingTools.Experimental.RAGTools.AnnotatedNode, AbstractVector, F1, F2}} where {F1<:Function, F2<:Function}","line":2720},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L211-L240","line":2763},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/utils.jl#L52-L58","line":2781},{"title":"#","target":"#PromptingTools.Experimental.RAGTools.trigrams_hashed-Tuple{AbstractString}","line":2785},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/utils.jl#L82-L90","line":2799},{"title":"#","target":"#PromptingTools.pprint-Tuple{IO, PromptingTools.Experimental.RAGTools.AbstractAnnotatedNode}","line":2803},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/annotation.jl#L101-L108","line":2816},{"title":"#","target":"#PromptingTools.pprint-Tuple{IO, PromptingTools.Experimental.RAGTools.AbstractRAGResult}","line":2820},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/RAGTools/types.jl#L346-L356","line":2836},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/reference_ragtools.md","line":2840},{"title":"Previous pageExperimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":2842},{"title":"Next pageAgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":2844},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":2846},{"title":"Icons8","target":"https://icons8.com","line":2846},{"title":"**VitePress**","target":"https://vitepress.dev","line":2846}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,242],"##Reference#{1}":[209,242],"#Reference for RAGTools [​](#Reference-for-RAGTools)":[243,2848],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{1}":[245,245],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{2}":[246,246],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{3}":[247,247],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{4}":[248,248],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{5}":[249,249],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{6}":[250,250],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{7}":[251,251],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{8}":[252,252],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{9}":[253,253],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{10}":[254,254],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{11}":[255,255],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{12}":[256,256],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{13}":[257,257],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{14}":[258,258],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{15}":[259,259],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{16}":[260,260],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{17}":[261,261],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{18}":[262,262],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{19}":[263,263],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{20}":[264,264],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{21}":[265,265],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{22}":[266,266],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{23}":[267,267],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{24}":[268,268],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{25}":[269,269],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{26}":[270,270],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{27}":[271,271],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{28}":[272,272],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{29}":[273,273],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{30}":[274,274],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{31}":[275,275],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{32}":[276,276],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{33}":[277,277],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{34}":[278,278],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{35}":[279,279],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{36}":[280,280],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{37}":[281,281],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{38}":[282,282],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{39}":[283,283],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{40}":[284,284],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{41}":[285,285],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{42}":[286,286],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{43}":[287,287],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{44}":[288,288],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{45}":[289,289],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{46}":[290,290],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{47}":[291,291],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{48}":[292,292],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{49}":[293,293],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{50}":[294,294],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{51}":[295,295],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{52}":[296,296],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{53}":[297,297],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{54}":[298,298],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{55}":[299,299],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{56}":[300,300],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{57}":[301,301],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{58}":[302,302],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{59}":[303,303],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{60}":[304,304],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{61}":[305,305],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{62}":[306,306],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{63}":[307,307],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{64}":[308,308],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{65}":[309,309],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{66}":[310,310],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{67}":[311,311],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{68}":[312,312],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{69}":[313,313],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{70}":[314,314],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{71}":[315,315],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{72}":[316,316],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{73}":[317,317],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{74}":[318,318],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{75}":[319,319],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{76}":[320,320],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{77}":[321,321],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{78}":[322,322],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{79}":[323,323],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{80}":[324,324],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{81}":[325,325],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{82}":[326,326],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{83}":[327,327],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{84}":[328,328],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{85}":[329,329],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{86}":[330,330],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{87}":[331,331],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{88}":[332,333],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{89}":[334,365],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{90}":[366,367],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{91}":[368,369],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{92}":[370,372],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{93}":[373,388],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{94}":[389,390],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{95}":[391,392],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{96}":[393,394],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{97}":[395,396],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{98}":[397,398],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{99}":[399,400],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{100}":[401,403],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{101}":[404,419],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{102}":[420,421],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{103}":[422,423],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{104}":[424,425],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{105}":[426,428],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{106}":[429,444],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{107}":[445,446],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{108}":[447,448],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{109}":[449,451],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{110}":[452,481],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{111}":[482,483],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{112}":[484,485],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{113}":[486,487],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{114}":[488,490],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{115}":[491,522],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{116}":[523,524],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{117}":[525,526],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{118}":[527,528],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{119}":[529,530],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{120}":[531,532],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{121}":[533,535],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{122}":[536,553],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{123}":[554,555],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{124}":[556,557],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{125}":[558,559],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{126}":[560,562],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{127}":[563,606],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{128}":[607,608],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{129}":[609,611],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{130}":[612,629],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{131}":[630,631],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{132}":[632,633],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{133}":[634,636],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{134}":[637,652],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{135}":[653,654],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{136}":[655,656],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{137}":[657,658],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{138}":[659,660],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{139}":[661,662],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{140}":[663,664],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{141}":[665,667],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{142}":[668,931],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{143}":[932,933],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{144}":[934,935],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{145}":[936,937],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{146}":[938,939],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{147}":[940,941],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{148}":[942,943],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{149}":[944,945],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{150}":[946,947],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{151}":[948,949],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{152}":[950,951],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{153}":[952,954],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{154}":[955,1048],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{155}":[1049,1050],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{156}":[1051,1052],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{157}":[1053,1054],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{158}":[1055,1056],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{159}":[1057,1058],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{160}":[1059,1061],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{161}":[1062,1161],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{162}":[1162,1163],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{163}":[1164,1165],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{164}":[1166,1167],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{165}":[1168,1169],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{166}":[1170,1171],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{167}":[1172,1173],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{168}":[1174,1175],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{169}":[1176,1185],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{170}":[1186,1187],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{171}":[1188,1203],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{172}":[1204,1206],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{173}":[1207,1208],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{174}":[1209,1210],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{175}":[1211,1213],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{176}":[1214,1314],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{177}":[1315,1316],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{178}":[1317,1318],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{179}":[1319,1320],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{180}":[1321,1323],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{181}":[1324,1325],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{182}":[1326,1327],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{183}":[1328,1329],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{184}":[1330,1331],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{185}":[1332,1333],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{186}":[1334,1335],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{187}":[1336,1337],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{188}":[1338,1339],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{189}":[1340,1341],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{190}":[1342,1343],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{191}":[1344,1345],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{192}":[1346,1348],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{193}":[1349,1415],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{194}":[1416,1417],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{195}":[1418,1419],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{196}":[1420,1421],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{197}":[1422,1423],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{198}":[1424,1425],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{199}":[1426,1427],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{200}":[1428,1429],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{201}":[1430,1431],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{202}":[1432,1434],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{203}":[1435,1456],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{204}":[1457,1458],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{205}":[1459,1460],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{206}":[1461,1462],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{207}":[1463,1464],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{208}":[1465,1467],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{209}":[1468,1469],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{210}":[1470,1471],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{211}":[1472,1513],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{212}":[1514,1515],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{213}":[1516,1517],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{214}":[1518,1519],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{215}":[1520,1521],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{216}":[1522,1523],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{217}":[1524,1525],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{218}":[1526,1528],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{219}":[1529,1530],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{220}":[1531,1533],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{221}":[1534,1535],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{222}":[1536,1543],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{223}":[1544,1545],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{224}":[1546,1548],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{225}":[1549,1550],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{226}":[1551,1552],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{227}":[1553,1571],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{228}":[1572,1573],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{229}":[1574,1591],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{230}":[1592,1593],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{231}":[1594,1595],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{232}":[1596,1597],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{233}":[1598,1599],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{234}":[1600,1601],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{235}":[1602,1603],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{236}":[1604,1606],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{237}":[1607,1612],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{238}":[1613,1614],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{239}":[1615,1616],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{240}":[1617,1618],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{241}":[1619,1621],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{242}":[1622,1678],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{243}":[1679,1680],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{244}":[1681,1682],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{245}":[1683,1684],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{246}":[1685,1686],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{247}":[1687,1689],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{248}":[1690,1727],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{249}":[1728,1729],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{250}":[1730,1732],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{251}":[1733,1832],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{252}":[1833,1834],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{253}":[1835,1836],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{254}":[1837,1838],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{255}":[1839,1840],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{256}":[1841,1842],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{257}":[1843,1844],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{258}":[1845,1847],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{259}":[1848,1849],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{260}":[1850,1851],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{261}":[1852,1853],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{262}":[1854,1855],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{263}":[1856,1857],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{264}":[1858,1859],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{265}":[1860,1861],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{266}":[1862,1863],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{267}":[1864,1865],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{268}":[1866,1871],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{269}":[1872,1873],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{270}":[1874,1879],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{271}":[1880,1881],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{272}":[1882,1883],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{273}":[1884,1886],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{274}":[1887,1924],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{275}":[1925,1926],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{276}":[1927,1929],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{277}":[1930,1931],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{278}":[1932,1933],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{279}":[1934,1935],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{280}":[1936,1937],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{281}":[1938,1940],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{282}":[1941,1963],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{283}":[1964,1965],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{284}":[1966,1968],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{285}":[1969,1970],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{286}":[1971,1972],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{287}":[1973,1974],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{288}":[1975,1976],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{289}":[1977,1978],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{290}":[1979,1980],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{291}":[1981,1983],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{292}":[1984,2017],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{293}":[2018,2019],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{294}":[2020,2021],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{295}":[2022,2023],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{296}":[2024,2025],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{297}":[2026,2028],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{298}":[2029,2077],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{299}":[2078,2079],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{300}":[2080,2082],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{301}":[2083,2108],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{302}":[2109,2110],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{303}":[2111,2113],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{304}":[2114,2202],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{305}":[2203,2204],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{306}":[2205,2206],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{307}":[2207,2208],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{308}":[2209,2210],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{309}":[2211,2212],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{310}":[2213,2214],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{311}":[2215,2216],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{312}":[2217,2218],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{313}":[2219,2221],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{314}":[2222,2244],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{315}":[2245,2246],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{316}":[2247,2248],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{317}":[2249,2250],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{318}":[2251,2252],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{319}":[2253,2255],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{320}":[2256,2290],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{321}":[2291,2292],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{322}":[2293,2294],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{323}":[2295,2296],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{324}":[2297,2298],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{325}":[2299,2301],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{326}":[2302,2326],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{327}":[2327,2328],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{328}":[2329,2330],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{329}":[2331,2332],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{330}":[2333,2334],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{331}":[2335,2336],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{332}":[2337,2338],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{333}":[2339,2340],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{334}":[2341,2342],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{335}":[2343,2345],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{336}":[2346,2383],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{337}":[2384,2385],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{338}":[2386,2393],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{339}":[2394,2395],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{340}":[2396,2397],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{341}":[2398,2399],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{342}":[2400,2401],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{343}":[2402,2403],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{344}":[2404,2405],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{345}":[2406,2407],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{346}":[2408,2409],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{347}":[2410,2415],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{348}":[2416,2417],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{349}":[2418,2419],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{350}":[2420,2421],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{351}":[2422,2423],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{352}":[2424,2425],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{353}":[2426,2428],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{354}":[2429,2430],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{355}":[2431,2432],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{356}":[2433,2434],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{357}":[2435,2437],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{358}":[2438,2440],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{359}":[2441,2511],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{360}":[2512,2513],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{361}":[2514,2515],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{362}":[2516,2517],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{363}":[2518,2519],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{364}":[2520,2521],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{365}":[2522,2524],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{366}":[2525,2565],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{367}":[2566,2567],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{368}":[2568,2569],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{369}":[2570,2571],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{370}":[2572,2573],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{371}":[2574,2575],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{372}":[2576,2577],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{373}":[2578,2580],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{374}":[2581,2586],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{375}":[2587,2588],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{376}":[2589,2590],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{377}":[2591,2593],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{378}":[2594,2736],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{379}":[2737,2738],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{380}":[2739,2740],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{381}":[2741,2742],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{382}":[2743,2744],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{383}":[2745,2746],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{384}":[2747,2748],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{385}":[2749,2751],"#Reference for RAGTools [​](#Reference-for-RAGTools)#{386}":[2752,2848]},"last_import":{"mtime":1712728016360,"size":118609,"at":1740449883683,"hash":"c6332306fa96ad911a9827e21ad9bba56d267ff7c9bd80a76e1728c9507d585d"},"key":"PromptingTools.jl/Reference for RAGTools - PromptingTools.jl.md"},