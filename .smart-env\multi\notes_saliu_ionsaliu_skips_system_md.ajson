
"smart_sources:notes/saliu/ionsaliu_skips_system.md": {"path":"notes/saliu/ionsaliu_skips_system.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"5jut7q","at":1753230880692},"class_name":"SmartSource","last_import":{"mtime":1735897996000,"size":108748,"at":1753230880741,"hash":"5jut7q"},"blocks":{"#":[1,1012],"##{1}":[293,293],"##{2}":[294,294],"##{3}":[295,295],"##{4}":[296,296],"##{5}":[297,297],"##{6}":[298,298],"##{7}":[299,299],"##{8}":[300,300],"##{9}":[301,304],"##{10}":[305,305],"##{11}":[306,306],"##{12}":[307,307],"##{13}":[308,308],"##{14}":[309,328],"##{15}":[329,329],"##{16}":[330,330],"##{17}":[331,472],"##{18}":[473,473],"##{19}":[474,474],"##{20}":[475,475],"##{21}":[476,476],"##{22}":[477,563],"##{23}":[564,564],"##{24}":[565,565],"##{25}":[566,569],"##{26}":[570,570],"##{27}":[571,571],"##{28}":[572,622],"##{29}":[623,626],"##{30}":[627,631],"##{31}":[632,647],"##{32}":[648,662],"##{33}":[663,739],"##{34}":[740,743],"##{35}":[744,761],"##{36}":[762,775],"##{37}":[776,1012],"#---frontmatter---":[794,1012],"##hit_counts目前只記錄該期所有預測組數命中數的最大值":[1013,1601],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{1}":[1014,1150],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{2}":[1151,1155],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{3}":[1156,1160],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{4}":[1161,1165],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{5}":[1166,1295],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{6}":[1296,1303],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{7}":[1297,1303],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{8}":[1304,1309],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{9}":[1305,1309],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{10}":[1310,1387],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{11}":[1311,1387],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{12}":[1388,1388],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{13}":[1389,1389],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{14}":[1390,1391],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{15}":[1392,1579],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{16}":[1580,1583],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{17}":[1584,1588],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{18}":[1589,1592],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{19}":[1593,1593],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{20}":[1594,1594],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{21}":[1595,1595],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{22}":[1596,1597],"##hit_counts目前只記錄該期所有預測組數命中數的最大值#{23}":[1598,1601],"##設計一個函數來轉換開獎號碼和其對應的跳號值。":[1602,1614],"##設計一個函數來轉換開獎號碼和其對應的跳號值。#{1}":[1604,1614],"#或":[1615,1879],"#或#{1}":[1616,1741],"#或#{2}":[1742,1746],"#或#{3}":[1747,1751],"#或#{4}":[1752,1756],"#或#{5}":[1757,1761],"#或#{6}":[1762,1848],"#或#{7}":[1849,1849],"#或#{8}":[1850,1850],"#或#{9}":[1851,1851],"#或#{10}":[1852,1853],"#或#{11}":[1854,1879],"#計算每個號碼的連續跳號值":[1880,1898],"#計算每個號碼的連續跳號值#{1}":[1881,1898],"#建立結果矩陣：期數 × 號碼範圍":[1899,1901],"#建立結果矩陣：期數 × 號碼範圍#{1}":[1900,1901],"#從第二期開始計算":[1902,1904],"#從第二期開始計算#{1}":[1903,1904],"#如果號碼j在第i期出現":[1905,1908],"#如果號碼j在第i期出現#{1}":[1906,1908],"#未出現則累加上期的跳號值":[1909,1920],"#未出現則累加上期的跳號值#{1}":[1910,1920],"#載入資料":[1921,1923],"#載入資料#{1}":[1922,1923],"#計算連續跳號值":[1924,1926],"#計算連續跳號值#{1}":[1925,1926],"#檢視最新一期的連續跳號值":[1927,2760],"#檢視最新一期的連續跳號值#{1}":[1928,1964],"#檢視最新一期的連續跳號值#{2}":[1965,1965],"#檢視最新一期的連續跳號值#{3}":[1966,1966],"#檢視最新一期的連續跳號值#{4}":[1967,1967],"#檢視最新一期的連續跳號值#{5}":[1968,1969],"#檢視最新一期的連續跳號值#{6}":[1970,1970],"#檢視最新一期的連續跳號值#{7}":[1971,1971],"#檢視最新一期的連續跳號值#{8}":[1972,1972],"#檢視最新一期的連續跳號值#{9}":[1973,1973],"#檢視最新一期的連續跳號值#{10}":[1974,1975],"#檢視最新一期的連續跳號值#{11}":[1976,2062],"#檢視最新一期的連續跳號值#{12}":[2063,2063],"#檢視最新一期的連續跳號值#{13}":[2064,2064],"#檢視最新一期的連續跳號值#{14}":[2065,2065],"#檢視最新一期的連續跳號值#{15}":[2066,2067],"#檢視最新一期的連續跳號值#{16}":[2068,2068],"#檢視最新一期的連續跳號值#{17}":[2069,2069],"#檢視最新一期的連續跳號值#{18}":[2070,2071],"#檢視最新一期的連續跳號值#{19}":[2072,2078],"#檢視最新一期的連續跳號值#將開獎號碼序列轉換為跳號值序列":[2079,2192],"#檢視最新一期的連續跳號值#將開獎號碼序列轉換為跳號值序列#{1}":[2081,2167],"#檢視最新一期的連續跳號值#將開獎號碼序列轉換為跳號值序列#{2}":[2168,2170],"#檢視最新一期的連續跳號值#將開獎號碼序列轉換為跳號值序列#{3}":[2171,2173],"#檢視最新一期的連續跳號值#將開獎號碼序列轉換為跳號值序列#{4}":[2174,2180],"#檢視最新一期的連續跳號值#將開獎號碼序列轉換為跳號值序列#{5}":[2181,2185],"#檢視最新一期的連續跳號值#將開獎號碼序列轉換為跳號值序列#{6}":[2186,2192],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：":[2193,2290],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{1}":[2195,2263],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{2}":[2264,2264],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{3}":[2265,2269],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{4}":[2270,2274],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{5}":[2275,2278],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{6}":[2279,2279],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{7}":[2280,2280],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{8}":[2281,2281],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{9}":[2282,2282],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{10}":[2283,2284],"#檢視最新一期的連續跳號值#計算每期跳號值小於等於中位數的號碼數量：#{11}":[2285,2290],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：":[2291,2399],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{1}":[2292,2376],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{2}":[2377,2380],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{3}":[2381,2385],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{4}":[2386,2390],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{5}":[2391,2391],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{6}":[2392,2392],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{7}":[2393,2393],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{8}":[2394,2395],"#檢視最新一期的連續跳號值#寫一個函數來分析每期符合跳號中位數條件的號碼：#{9}":[2396,2399],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：":[2400,2746],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{1}":[2402,2487],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{2}":[2488,2488],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{3}":[2489,2489],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{4}":[2490,2490],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{5}":[2491,2491],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{6}":[2492,2493],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{7}":[2494,2494],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{8}":[2495,2495],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{9}":[2496,2496],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{10}":[2497,2498],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{11}":[2499,2499],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{12}":[2500,2500],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{13}":[2501,2501],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{14}":[2502,2503],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{15}":[2504,2604],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{16}":[2605,2605],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{17}":[2606,2606],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{18}":[2607,2607],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{19}":[2608,2608],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{20}":[2609,2610],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{21}":[2611,2611],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{22}":[2612,2612],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{23}":[2613,2613],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{24}":[2614,2614],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{25}":[2615,2616],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{26}":[2617,2731],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{27}":[2732,2732],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{28}":[2733,2733],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{29}":[2734,2734],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{30}":[2735,2735],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{31}":[2736,2737],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{32}":[2738,2738],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{33}":[2739,2739],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{34}":[2740,2740],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{35}":[2741,2742],"#檢視最新一期的連續跳號值#我們是要用當期的連續跳號值來預測下一期的開獎號碼。讓我來修改這部分的代碼：#{36}":[2743,2746],"#檢視最新一期的連續跳號值#建立一個通用的分析函數，可以接受不同的條件函數來篩選號碼：":[2747,2760],"#檢視最新一期的連續跳號值#建立一個通用的分析函數，可以接受不同的條件函數來篩選號碼：#{1}":[2749,2760],"#使用連續跳號條件":[2761,3591],"#使用連續跳號條件#{1}":[2762,2906],"#使用連續跳號條件#{2}":[2907,2910],"#使用連續跳號條件#{3}":[2911,2914],"#使用連續跳號條件#{4}":[2915,2918],"#使用連續跳號條件#{5}":[2919,2923],"#使用連續跳號條件#{6}":[2924,2924],"#使用連續跳號條件#{7}":[2925,2925],"#使用連續跳號條件#{8}":[2926,2926],"#使用連續跳號條件#{9}":[2927,2927],"#使用連續跳號條件#{10}":[2928,2928],"#使用連續跳號條件#{11}":[2929,3085],"#使用連續跳號條件#{12}":[3086,3090],"#使用連續跳號條件#{13}":[3091,3095],"#使用連續跳號條件#{14}":[3096,3100],"#使用連續跳號條件#{15}":[3101,3105],"#使用連續跳號條件#{16}":[3106,3106],"#使用連續跳號條件#{17}":[3107,3107],"#使用連續跳號條件#{18}":[3108,3108],"#使用連續跳號條件#{19}":[3109,3110],"#使用連續跳號條件#{20}":[3111,3114],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：":[3115,3591],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{1}":[3117,3217],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{2}":[3218,3224],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{3}":[3225,3230],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{4}":[3231,3235],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{5}":[3236,3240],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{6}":[3241,3241],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{7}":[3242,3242],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{8}":[3243,3243],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{9}":[3244,3244],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{10}":[3245,3246],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{11}":[3247,3320],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{12}":[3321,3321],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{13}":[3322,3322],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{14}":[3323,3324],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{15}":[3325,3453],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{16}":[3454,3458],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{17}":[3459,3463],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{18}":[3464,3468],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{19}":[3469,3473],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{20}":[3474,3474],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{21}":[3475,3475],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{22}":[3476,3476],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{23}":[3477,3478],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{24}":[3479,3571],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{25}":[3572,3572],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{26}":[3573,3573],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{27}":[3574,3575],"#使用連續跳號條件#添加繪圖功能，用來視覺化展示各個星級的價值變化：#{28}":[3576,3591]},"outlinks":[{"title":"0,0,0,0,0","target":"0,0,0,0,0","line":2525},{"title":"0,0,0,0,0","target":"0,0,0,0,0","line":2639},{"title":"0,0,0,0,0","target":"0,0,0,0,0","line":2782}]},