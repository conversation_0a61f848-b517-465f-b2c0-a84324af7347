
"smart_sources:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md": {"path":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1p06pjd","at":1753230880448},"class_name":"SmartSource","last_import":{"mtime":1735897996000,"size":25608,"at":1753230880657,"hash":"1p06pjd"},"blocks":{"#":[1,2],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_":[3,247],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#By <PERSON>, _Founder of Lottery Mathematics_":[5,17],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#By <PERSON>, _Founder of Lottery Mathematics_#{1}":[7,8],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#By Ion Saliu, _Founder of Lottery Mathematics_#I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](#Mathematics)":[9,17],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#By Ion Saliu, _Founder of Lottery Mathematics_#I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](#Mathematics)#{1}":[10,17],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#1. Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_":[18,68],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#1. Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_#{1}":[20,21],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#1. Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_#{2}":[22,32],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#1. Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_#{3}":[33,34],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#1. Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_#{4}":[35,35],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#1. Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_#{5}":[36,37],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#1. Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_#{6}":[38,68],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency":[69,86],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency#{1}":[71,72],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency#{2}":[73,74],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency#{3}":[75,86],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#3. Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency":[87,104],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#3. Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency#{1}":[89,90],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#3. Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency#{2}":[91,104],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#4. _Generate_ Combinations or _Purge_ Files for _Dedicated LIE Elimination_ Strategies":[105,159],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#4. _Generate_ Combinations or _Purge_ Files for _Dedicated LIE Elimination_ Strategies#{1}":[107,111],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#4. _Generate_ Combinations or _Purge_ Files for _Dedicated LIE Elimination_ Strategies#{2}":[112,159],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#5. Lottery Strategies for _LIE Elimination_ Based on Pairs, Number Frequency":[160,208],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#5. Lottery Strategies for _LIE Elimination_ Based on Pairs, Number Frequency#{1}":[162,208],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#[6. Resources in Lottery Lotto Software, Strategies, Systems](content/lottery.html)":[209,247],"#_LIE Elimination_: Lottery, Lotto Strategy in Reverse for _Pairs_, _Number Frequency_#[6. Resources in Lottery Lotto Software, Strategies, Systems](content/lottery.html)#{1}":[211,247]},"outlinks":[{"title":"![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/software-lottery-gambling.gif","line":1},{"title":"Read an introduction to reduction of lotto tickets with reversed lottery strategy on pairs.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlHLINE.gif","line":7},{"title":"Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_","target":"#Mathematics","line":9},{"title":"Software for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"#Software","line":10},{"title":"Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"#Reports","line":11},{"title":"Generate Combinations or Purge Files for _Dedicated LIE Elimination_ Strategies","target":"#Combinations","line":12},{"title":"Strategies for _Dedicated LIE Elimination_ on Pairs, Number Frequency","target":"#Strategies","line":13},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"#Resources","line":14},{"title":"We know that all lottery strategies miss more often than hit winners therefore we eliminate output.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlHLINE.gif","line":16},{"title":"_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"reverse-strategy.html","line":38},{"title":"_**LIE Strategy Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"lie-lotto-strategies-decades.html","line":51},{"title":"Lie lottery strategies are designed for any parpalucks, both for lotto pairs and frequencies.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/parpaluck-report.gif","line":61},{"title":"The ultimate lottery software has reverse strategy specialized programs.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/ultimate-lotto-software-60.gif","line":71},{"title":"The reversed lotto software is based on number frequencies and lottery pairings to win the jackpot.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/lie-lottery-pairs-software.gif","line":75},{"title":"The lotto pairs and hot cold numbers act as filters, restrictions to eliminate bad combinations.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/lie-lottery-pairs-filters.gif","line":85},{"title":"The reverse lotto strategy software generates first comprehensive reports to select filters from.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/lie-lottery-pairs-report.gif","line":91},{"title":"There are special ranges of lottery draws for analyses and report generating.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/lie-lottery-pairs-draws.gif","line":95},{"title":"The pairing and frequency parpalucks can be calculated by math.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/parpaluck-report.gif","line":99},{"title":"Reverse lie elimination strategy generates lottery combinations to play right away.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlimages/lie-lottery-pairs-combinations.gif","line":158},{"title":"**Cross-Reference Lottery Strategy Files Created by _Command Prompt Software_ and _MDIEditor Lotto WE_**","target":"cross-lines.html","line":201},{"title":"6. Resources in Lottery Lotto Software, Strategies, Systems","target":"content/lottery.html","line":209},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"LottoWin.htm","line":211},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"Newsgroups.htm","line":213},{"title":"_**MDIEditor Lotto WE: Lottery Software Manual, Book, ebook, Help**_","target":"MDI-lotto-guide.html","line":216},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"forum/lotto-book.html","line":218},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"gambling-lottery-lotto/lottery-math.htm","line":220},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"filters.html","line":221},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"skip-strategy.html","line":222},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"frequency-lottery.html","line":224},{"title":"_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"reverse-strategy.html","line":225},{"title":"_**LIE Elimination, Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"lie-lotto-strategies-decades.html","line":226},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"strategy.html","line":228},{"title":"_**Lottery Utility Software**_","target":"lottery-utility.html","line":229},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"delta-lotto-software.html","line":231},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"decades.html","line":232},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"markov-chains-lottery.html","line":233},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"lie-lotto-strategies-decades.html","line":234},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"lotto-groups.html","line":236},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"strategy-gambling-lottery.html","line":237},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":238},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":239},{"title":"**Lottery Software, Lotto Software**","target":"infodown.html","line":241},{"title":"The first lotto software to apply reversed lottery strategies with the best chance to win.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlHLINE.gif","line":243},{"title":"New Writings","target":"bbs/index.html","line":245},{"title":"Odds, Generator","target":"calculator_generator.html","line":245},{"title":"Contents","target":"content/index.html","line":245},{"title":"Forums","target":"https://forums.saliu.com/","line":245},{"title":"Home","target":"index.htm","line":245},{"title":"Search","target":"Search.htm","line":245},{"title":"Sitemap","target":"sitemap/index.html","line":245},{"title":"You read about strategy, lottery, lotto, software, algorithms, number, pairings, pairs, frequency.","target":"https://saliu.com/lie-lottery-strategies-pairs.htmlHLINE.gif","line":247}]},
"smart_sources:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md": {"path":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12529197,-0.03185341,-0.02878089,0.03364551,-0.04602962,0.0761233,0.01638435,0.01427404,0.03649401,-0.02007066,-0.031967,-0.02170344,0.10730146,-0.02659626,0.00103949,-0.04574055,-0.0276247,0.04120573,-0.00576866,-0.0141897,0.09425959,-0.06459617,-0.04387724,-0.05426536,0.06199977,0.02352933,-0.03123922,-0.08029327,-0.04136831,-0.23256201,0.03363155,0.0393392,-0.00018148,-0.03918245,-0.07231057,-0.01636744,-0.04897376,0.05117391,-0.05264191,0.05047924,0.01171654,-0.00500896,0.04822461,-0.02665122,0.01696687,0.01812835,-0.05724524,0.01266098,0.01552785,-0.00654763,-0.03464654,0.05357128,-0.0440516,0.04239989,0.09757011,0.09203513,0.07557508,0.07339777,0.02595126,0.06569171,0.03482052,0.04110356,-0.19423097,0.01628198,-0.00821743,0.02677794,0.01412347,0.00651234,0.01689334,0.07240054,0.01529631,0.01714532,-0.01551846,0.01777865,0.05455377,-0.01499412,-0.05555952,-0.04414782,-0.01814444,-0.01360537,-0.02366907,0.00139737,-0.01940617,-0.0064606,0.02366349,0.01612299,0.04384995,0.02747862,0.06332566,-0.10173239,0.01199104,0.04863065,0.06298233,0.01020827,0.02785045,0.03902644,0.02421281,0.02360293,0.03406212,0.1181407,-0.01106501,0.02213597,-0.02383955,0.02927976,0.05833565,-0.07809551,-0.01863939,-0.05870219,0.0460532,0.03145045,0.03246246,-0.00255018,0.07153332,-0.05696993,-0.01524419,0.06133763,0.04551434,0.07987715,0.0194939,-0.00927886,-0.05525614,0.00420175,0.00731642,-0.00452567,0.00686723,-0.00278634,0.00002097,0.08101153,0.03970851,0.05756656,0.0141365,-0.03843543,-0.1252383,-0.05446742,-0.06887856,-0.00973444,-0.00491932,-0.02283784,0.0254853,0.03803086,-0.01552345,-0.07941884,0.05168608,-0.06428245,0.00297569,0.09034152,0.01404285,0.01221625,-0.00833411,0.03109214,-0.00311183,0.01343647,-0.02357482,-0.10011303,-0.00436343,0.00657095,0.05118733,0.06455306,-0.01334876,0.04544358,-0.00800669,-0.00658454,-0.06217155,0.13767175,-0.02102987,-0.11967753,-0.00045211,0.05721257,-0.05612222,-0.0677392,-0.04856272,-0.01163725,-0.00792018,-0.02098729,0.09034826,0.0037589,-0.05596376,-0.09861053,0.00161502,0.00895459,-0.03572103,-0.0445513,-0.04684511,-0.00237407,-0.0285868,-0.04136119,-0.00520767,-0.05488705,0.03579139,0.04614108,-0.07858325,-0.06157823,-0.05205691,-0.00664479,-0.00664433,-0.04276593,-0.02310651,-0.04474779,0.0130741,0.01675807,-0.03644041,-0.00952731,-0.00224157,-0.00867825,-0.01629654,0.06199549,0.01993845,-0.05616698,0.04655189,0.02649607,0.00341838,0.04282061,0.00171879,0.03351349,-0.0381094,-0.02059039,-0.00468106,0.02017151,-0.02372009,0.03186679,-0.00821253,0.00177474,-0.06181569,-0.18696453,-0.02631844,-0.02752538,-0.00849584,0.02183227,-0.04829703,0.00981893,-0.02552256,0.0336539,0.07176789,0.06027219,-0.01808294,-0.06480277,0.03431277,-0.01280042,0.00235732,-0.04293904,-0.0317531,-0.01813069,0.02312859,-0.02251907,0.0200094,-0.01211248,-0.06287281,0.06074176,-0.02008432,0.13222334,0.02143831,-0.00359167,0.04362396,0.04498646,0.01718931,-0.0379307,-0.03175991,0.01705658,0.0251772,0.03437522,-0.04727741,-0.04009577,-0.00473062,-0.08608074,0.02366265,0.00412071,-0.0823224,-0.05420507,0.01849527,-0.02996427,-0.01509038,0.03078435,0.08380435,0.03165266,-0.00855273,0.02787453,0.04035904,0.06828555,0.00591891,-0.06183238,-0.04159258,-0.0451594,0.00242242,-0.02579912,0.01510797,0.00453499,-0.03412405,-0.00683886,0.02800069,0.00103444,-0.00811183,0.00110202,0.02179398,-0.00707544,0.10705373,0.04807353,0.02529902,-0.02564744,0.01202029,0.02527253,-0.09507232,-0.02771508,-0.02616652,0.0378889,-0.06660575,0.07150532,0.04519314,0.05785829,-0.01175663,0.07688898,0.03367709,0.02039576,0.00828706,-0.02808632,0.00416182,-0.00597814,0.01789033,0.05133154,0.01634769,-0.26632243,-0.00445217,-0.05515144,0.05713975,-0.01284057,-0.01218389,0.03047151,-0.02181604,0.00135915,-0.08089088,0.01602141,0.04849922,0.00310025,0.00326574,0.0283517,0.0045092,0.04082648,-0.08106754,0.05427483,0.02992717,0.03713585,0.02087693,0.2276203,-0.00173997,0.01024823,0.02369755,-0.00607865,0.05408483,-0.00631642,0.03566725,-0.01224713,-0.054671,0.06782284,0.00974657,-0.01253706,0.04780785,-0.02729919,0.01006452,0.02524593,0.01897611,-0.09022169,-0.05033759,0.02109411,-0.01368598,0.18380685,0.00532072,-0.02100234,-0.05292777,0.02240764,0.03511492,-0.08876168,-0.03514254,-0.05414489,0.01812867,-0.00621638,0.0415099,-0.01571011,-0.00777829,0.02326066,-0.00250665,0.05669713,0.00596703,0.02860123,0.04214545,-0.00519696],"last_embed":{"hash":"oaex7f","tokens":453}}},"last_read":{"hash":"oaex7f","at":1753423495904},"class_name":"SmartSource","last_import":{"mtime":1753363612350,"size":26386,"at":1753423416052,"hash":"oaex7f"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Strategy in Reverse for Pairs, Number Frequency":[8,245],"#Lottery Strategy in Reverse for Pairs, Number Frequency#{1}":[10,15],"#Lottery Strategy in Reverse for Pairs, Number Frequency##I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics)":[16,24],"#Lottery Strategy in Reverse for Pairs, Number Frequency##I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics)#{1}":[17,24],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>":[25,73],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{1}":[27,36],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{2}":[37,37],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{3}":[38,38],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{4}":[39,40],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{5}":[41,73],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>":[74,107],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{1}":[76,77],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{2}":[78,79],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{3}":[80,91],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{4}":[92,93],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{5}":[94,107],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>":[108,162],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>#{1}":[110,114],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>#{2}":[115,162],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>5. Lottery Strategies for <i>LIE Elimination</i> Based on Pairs, Number Frequency</u>":[163,212],"#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>5. Lottery Strategies for <i>LIE Elimination</i> Based on Pairs, Number Frequency</u>#{1}":[165,212],"#Lottery Strategy in Reverse for Pairs, Number Frequency#[<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>](https://saliu.com/content/lottery.html)":[213,245],"#Lottery Strategy in Reverse for Pairs, Number Frequency#[<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>](https://saliu.com/content/lottery.html)#{1}":[215,245]},"outlinks":[{"title":"Read an introduction to reduction of lotto tickets with reversed lottery strategy on pairs.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics","line":16},{"title":"Software for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Software","line":17},{"title":"Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Reports","line":18},{"title":"Generate Combinations or Purge Files for _Dedicated LIE Elimination_ Strategies","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Combinations","line":19},{"title":"Strategies for _Dedicated LIE Elimination_ on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Strategies","line":20},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Resources","line":21},{"title":"We know that all lottery strategies miss more often than hit winners therefore we eliminate output.","target":"https://saliu.com/HLINE.gif","line":23},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":41},{"title":"_**LIE Strategy Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":54},{"title":"Lie lottery strategies are designed for any parpalucks, both for lotto pairs and frequencies.","target":"https://saliu.com/images/parpaluck-report.gif","line":64},{"title":"The ultimate lottery software has reverse strategy specialized programs.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":76},{"title":"The reversed lotto software is based on number frequencies and lottery pairings to win the jackpot.","target":"https://saliu.com/images/lie-lottery-pairs-software.gif","line":80},{"title":"The lotto pairs and hot cold numbers act as filters, restrictions to eliminate bad combinations.","target":"https://saliu.com/images/lie-lottery-pairs-filters.gif","line":90},{"title":"The reverse lotto strategy software generates first comprehensive reports to select filters from.","target":"https://saliu.com/images/lie-lottery-pairs-report.gif","line":94},{"title":"There are special ranges of lottery draws for analyses and report generating.","target":"https://saliu.com/images/lie-lottery-pairs-draws.gif","line":98},{"title":"The pairing and frequency parpalucks can be calculated by math.","target":"https://saliu.com/images/parpaluck-report.gif","line":102},{"title":"Reverse lie elimination strategy generates lottery combinations to play right away.","target":"https://saliu.com/images/lie-lottery-pairs-combinations.gif","line":161},{"title":"**Cross-Reference Lottery Strategy Files Created by _Command Prompt Software_ and _MDIEditor Lotto WE_**","target":"https://saliu.com/cross-lines.html","line":207},{"title":"<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>","target":"https://saliu.com/content/lottery.html","line":213},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":215},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":217},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":219},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":221},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":222},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":223},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":224},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":225},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":226},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":227},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":228},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":229},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":230},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":231},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":232},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":233},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":234},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":235},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":236},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":237},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":238},{"title":"The first lotto software to apply reversed lottery strategies with the best chance to win.","target":"https://saliu.com/HLINE.gif","line":240},{"title":"Forums","target":"https://forums.saliu.com/","line":242},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":242},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":242},{"title":"Contents","target":"https://saliu.com/content/index.html","line":242},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":242},{"title":"Home","target":"https://saliu.com/index.htm","line":242},{"title":"Search","target":"https://saliu.com/Search.htm","line":242},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":242},{"title":"You read about strategy, lottery, lotto, software, algorithms, number, pairings, pairs, frequency.","target":"https://saliu.com/HLINE.gif","line":244}],"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md","metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["strategy","lottery","lotto","software","algorithms","number","pairings","pairs","frequency","reports","jackpot"],"source":"https://saliu.com/lie-lottery-strategies-pairs.html","author":null}},"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.138558,-0.04775324,-0.04220387,0.03311022,-0.05217775,0.08186509,0.00960386,0.02935071,0.04063619,-0.01996925,-0.0233709,-0.01242911,0.06633002,-0.01375458,0.0128182,-0.02913279,-0.03960448,0.01504365,-0.01596305,-0.0165379,0.1079483,-0.0419599,-0.05191144,-0.05799564,0.04166203,0.00878419,-0.01701146,-0.07424816,-0.03844636,-0.19317044,0.03169872,0.03555442,-0.00212383,-0.01606935,-0.05766361,-0.02519648,-0.05479948,0.0545351,-0.01491724,0.0625809,0.0106573,-0.00139798,0.03528998,-0.0241295,0.00374127,0.00484517,-0.05327415,0.02204719,0.02206955,0.00752403,-0.00961779,0.04652328,-0.02815534,0.05385308,0.11281031,0.07820584,0.08664572,0.05315909,0.03734116,0.0786166,0.02515115,0.01398228,-0.21239553,0.01192201,-0.00173741,0.02012118,0.01449396,0.01106994,-0.00401606,0.05933533,0.03777209,0.0218922,-0.00587689,0.01671326,0.06266474,0.00579408,-0.06368586,-0.04005977,-0.02937249,-0.00535945,-0.02782636,-0.01045266,-0.03089676,-0.02011316,0.03297449,0.00266732,0.04404246,0.05640446,0.0670213,-0.06220548,-0.00117416,0.03417556,0.07937711,0.00669064,-0.00822394,0.03805498,0.01476701,0.02443407,0.00979279,0.11929168,-0.02747212,0.0316723,-0.01252731,0.02936267,0.0590373,-0.07382791,-0.03199283,-0.05717996,0.04030268,0.02617357,0.02325214,0.01871011,0.09123871,-0.09172152,-0.00450851,0.05167422,0.02959516,0.0662357,0.03051119,-0.00211359,-0.05191093,0.00026107,0.00753384,-0.01218258,0.0099995,0.00931445,0.00578694,0.07055882,0.02486458,0.05731244,0.0040083,-0.03525482,-0.1251964,-0.02534753,-0.05781419,-0.00991819,0.00277116,-0.05959098,0.03039263,0.02202426,-0.017698,-0.07753029,0.0491807,-0.06868929,0.00859222,0.09145107,0.03341891,0.00663948,0.02821946,0.0306539,-0.02546252,-0.00071919,0.00121749,-0.08637361,0.00336618,0.02284114,0.05612744,0.07526453,0.00500423,0.0388509,-0.0017902,-0.02022839,-0.06918927,0.14727628,-0.01082293,-0.14834158,-0.01604406,0.04037707,-0.03921987,-0.07710519,-0.04385182,-0.01579943,0.00780093,-0.00012697,0.11323575,0.00556807,-0.03398708,-0.08303796,-0.01964449,0.00647113,-0.03400264,-0.02907546,-0.04739142,0.01300887,-0.05220607,-0.03051743,-0.00962272,-0.04681,0.05147522,0.04142128,-0.06029626,-0.08077216,-0.0538028,0.00689727,-0.00007581,-0.05029651,-0.02468078,-0.03571091,0.00517256,-0.00711819,-0.04657976,-0.01859955,-0.00209848,0.00661133,-0.02359335,0.0441545,0.01767666,-0.04444069,0.04690969,0.0276378,0.01573153,0.04171446,0.02721675,0.02954692,-0.05354273,-0.01915807,-0.02425979,0.0086027,-0.00650923,0.03108321,-0.02737154,-0.01220356,-0.05058858,-0.20459092,-0.01528102,-0.02685427,-0.00289952,-0.00380644,-0.03571777,0.01233563,-0.01308486,0.03271866,0.07538913,0.03629673,-0.01504976,-0.0671299,0.01410559,-0.01780431,0.00920461,-0.02851415,-0.04260233,0.0061538,0.02912749,-0.02305224,0.01548125,-0.03159771,-0.07130999,0.07777572,-0.01109419,0.1378306,0.04970465,-0.03058465,0.02202948,0.06379425,0.00965353,-0.03393788,-0.04719073,0.02673313,0.00031183,0.02234642,-0.027376,-0.04505406,-0.01252643,-0.10644075,0.01257222,0.02375888,-0.07852414,-0.04098003,0.00489385,-0.01596854,-0.03022132,0.02056379,0.05446295,0.02486082,-0.02848455,0.02821815,0.040992,0.05736172,0.00984849,-0.0708829,-0.05260066,-0.03025836,-0.01276515,-0.02092812,0.02401479,0.01549409,-0.03023192,-0.03005113,0.01634176,0.00131477,-0.01737767,-0.02298849,0.02223423,0.00775565,0.09853593,0.05597527,0.00805445,-0.03672553,-0.01343626,0.01950762,-0.0888942,-0.00618786,-0.01668127,0.03249469,-0.06321339,0.07382341,0.04246178,0.06156157,-0.00849272,0.05854483,0.02624222,0.03106257,0.00384329,-0.0127149,0.00471212,-0.00749951,0.00239046,0.0713949,-0.0169369,-0.2804251,0.01488273,-0.03014504,0.07367979,0.0175916,0.00254892,0.02227999,-0.01246221,-0.03170793,-0.04518338,0.03677869,0.04685133,-0.00232523,0.01065298,0.03168703,0.00591934,0.03931835,-0.07796785,0.05164655,0.0324385,0.03570873,0.03884856,0.24460934,0.00859669,0.0161389,0.02229553,-0.00583321,0.05921781,-0.00037185,0.0277342,0.00116103,-0.04180375,0.05472577,-0.00862584,-0.00223969,0.05388858,-0.02530327,0.02045954,0.00425541,0.03411017,-0.10641384,-0.0395551,0.01440314,-0.00288447,0.16487914,0.02209807,-0.0268698,-0.04923001,-0.00962072,0.0278845,-0.10193235,-0.02843956,-0.04644695,0.01824105,-0.01840748,0.04717006,-0.00614048,-0.01491457,0.03926108,-0.00640207,0.06504102,-0.00804086,0.03404475,0.02463214,-0.01082491],"last_embed":{"hash":"163m4yt","tokens":94}}},"text":null,"length":0,"last_read":{"hash":"163m4yt","at":1753423493817},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#---frontmatter---","lines":[1,6],"size":218,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11718389,-0.02585159,-0.02742438,0.02799951,-0.05414195,0.08110132,0.02558786,0.01130449,0.03912526,-0.03025601,-0.02922913,-0.02721291,0.11460259,-0.02441985,-0.00184196,-0.03692612,-0.0209886,0.05836498,-0.00446322,-0.01952899,0.09884548,-0.06699274,-0.04906591,-0.05384878,0.06459524,0.02293208,-0.03576038,-0.0806651,-0.04521938,-0.24176267,0.04107679,0.03015993,0.00022163,-0.04090982,-0.07856421,-0.01593763,-0.04347873,0.04164575,-0.06508,0.05329337,0.00969447,-0.00518299,0.04190051,-0.02874688,0.02035756,0.02071449,-0.05476011,0.00880562,0.02273318,-0.00695617,-0.02756981,0.05862219,-0.0499755,0.03201354,0.07815209,0.08998773,0.0798458,0.08875394,0.02406541,0.06388561,0.02670971,0.0542109,-0.18864696,0.01703215,-0.01058277,0.02375143,0.01173228,-0.00522539,0.02601606,0.07189933,0.00601808,0.02371598,-0.01586198,0.02008023,0.04148882,-0.01817453,-0.04970011,-0.04250984,-0.0092249,-0.01034647,-0.02428857,0.00214846,-0.01544019,-0.01474853,0.03265111,0.02395921,0.03546594,0.01792441,0.05985788,-0.10051019,0.01325823,0.05917612,0.06004867,0.00814979,0.03906606,0.03448834,0.0265167,0.01708221,0.04917704,0.13044426,-0.00684168,0.02309313,-0.02988193,0.03721573,0.05621885,-0.07446067,-0.02472007,-0.06389794,0.04004692,0.02701657,0.03160765,-0.0047113,0.06530246,-0.04467488,-0.01320546,0.06492084,0.03571903,0.0740376,0.01213522,-0.00860201,-0.05318849,0.00104674,0.01080535,0.00312959,-0.00189628,-0.00452046,0.00815473,0.07535605,0.0428667,0.06123023,0.02123268,-0.03873682,-0.11707037,-0.06754176,-0.0692092,-0.00214591,-0.0076707,-0.01024897,0.02399961,0.04291833,-0.0183857,-0.08066366,0.05795112,-0.05990288,0.00241008,0.08609844,0.00145169,0.01419279,-0.02122091,0.03701966,-0.00142018,0.01316299,-0.02365754,-0.09813358,-0.01200546,-0.00132439,0.04148775,0.05758949,-0.01223286,0.04257968,-0.02095508,-0.00368866,-0.05595899,0.13313328,-0.02340163,-0.10546952,0.00128778,0.05310797,-0.06392285,-0.0589023,-0.04635148,-0.0049583,-0.01259212,-0.03236449,0.07986548,0.00751268,-0.06950045,-0.09256438,0.00381572,-0.00020432,-0.04432385,-0.04329818,-0.03916152,-0.00245804,-0.03056522,-0.04592604,0.00437102,-0.05258414,0.03781204,0.04288903,-0.08156835,-0.04928754,-0.05370195,-0.01872224,-0.00270959,-0.03643626,-0.02975015,-0.03835552,0.0192555,0.02761295,-0.03054001,-0.01063151,0.00207355,-0.01013178,-0.01402629,0.068353,0.01756633,-0.0521858,0.04122629,0.03456786,0.00085524,0.04099222,-0.00353342,0.02896902,-0.03060959,-0.01279379,0.00305278,0.00786458,-0.03109422,0.0359057,0.00311786,0.00638874,-0.04940484,-0.18680641,-0.01943702,-0.02684685,-0.0166658,0.03150235,-0.05110979,0.00963548,-0.02193263,0.0371497,0.0672141,0.06902494,-0.0143134,-0.05637135,0.04826527,-0.01692632,0.01157331,-0.03911581,-0.03219193,-0.03059625,0.02008391,-0.02692956,0.0168621,-0.00734629,-0.05637041,0.06256068,-0.01705328,0.13331914,0.00465402,0.00866226,0.04733103,0.03786496,0.01427138,-0.03944241,-0.02963652,0.00879777,0.0247616,0.03614827,-0.05426542,-0.0223104,-0.0095554,-0.08126452,0.02439281,-0.00560193,-0.09292316,-0.04061135,0.02355206,-0.03360602,-0.00329278,0.05006205,0.0869766,0.04377746,-0.00946066,0.03279454,0.03915481,0.06239202,-0.00606571,-0.05908958,-0.03308281,-0.0463804,0.01464667,-0.02537522,0.00789916,-0.00278223,-0.03203973,-0.00450696,0.02672454,0.0054979,-0.00786715,0.00920723,0.01949766,-0.00846703,0.10341115,0.04373985,0.02769005,-0.02201216,0.01889448,0.02231299,-0.09322356,-0.03941027,-0.03257375,0.04194673,-0.06935766,0.07070366,0.04799367,0.05628713,-0.01974847,0.08278807,0.03427757,0.01077647,0.00489761,-0.02642696,0.00545326,-0.00965136,0.02546376,0.0430937,0.01887265,-0.26541191,0.00130905,-0.07709559,0.05109433,-0.02286188,-0.02103298,0.02416517,-0.01110252,0.00169712,-0.09709576,0.01386352,0.04590001,0.00999276,-0.00056847,0.01683895,-0.0014352,0.04886001,-0.08377732,0.05638503,0.0195906,0.03847753,0.01628883,0.22194046,0.00163673,0.01809088,0.02220886,-0.00970828,0.05120562,-0.01112055,0.03131772,-0.00875314,-0.0525403,0.06045523,-0.00459401,-0.00869113,0.04693751,-0.0285076,0.01692997,0.03408654,0.01351504,-0.08671708,-0.04776452,0.01937816,-0.01912144,0.18522178,0.00719163,-0.02402076,-0.04963527,0.02969807,0.04435132,-0.08734709,-0.03596786,-0.06388536,0.02082525,0.00161486,0.03890089,-0.02061542,-0.00739787,0.01827877,0.00311251,0.05399399,0.01231921,0.02194652,0.04692318,-0.00570106],"last_embed":{"hash":"sjdkcz","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"sjdkcz","at":1753423493848},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency","lines":[8,245],"size":26154,"outlinks":[{"title":"Read an introduction to reduction of lotto tickets with reversed lottery strategy on pairs.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics","line":9},{"title":"Software for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Software","line":10},{"title":"Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Reports","line":11},{"title":"Generate Combinations or Purge Files for _Dedicated LIE Elimination_ Strategies","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Combinations","line":12},{"title":"Strategies for _Dedicated LIE Elimination_ on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Strategies","line":13},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Resources","line":14},{"title":"We know that all lottery strategies miss more often than hit winners therefore we eliminate output.","target":"https://saliu.com/HLINE.gif","line":16},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":34},{"title":"_**LIE Strategy Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":47},{"title":"Lie lottery strategies are designed for any parpalucks, both for lotto pairs and frequencies.","target":"https://saliu.com/images/parpaluck-report.gif","line":57},{"title":"The ultimate lottery software has reverse strategy specialized programs.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":69},{"title":"The reversed lotto software is based on number frequencies and lottery pairings to win the jackpot.","target":"https://saliu.com/images/lie-lottery-pairs-software.gif","line":73},{"title":"The lotto pairs and hot cold numbers act as filters, restrictions to eliminate bad combinations.","target":"https://saliu.com/images/lie-lottery-pairs-filters.gif","line":83},{"title":"The reverse lotto strategy software generates first comprehensive reports to select filters from.","target":"https://saliu.com/images/lie-lottery-pairs-report.gif","line":87},{"title":"There are special ranges of lottery draws for analyses and report generating.","target":"https://saliu.com/images/lie-lottery-pairs-draws.gif","line":91},{"title":"The pairing and frequency parpalucks can be calculated by math.","target":"https://saliu.com/images/parpaluck-report.gif","line":95},{"title":"Reverse lie elimination strategy generates lottery combinations to play right away.","target":"https://saliu.com/images/lie-lottery-pairs-combinations.gif","line":154},{"title":"**Cross-Reference Lottery Strategy Files Created by _Command Prompt Software_ and _MDIEditor Lotto WE_**","target":"https://saliu.com/cross-lines.html","line":200},{"title":"<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>","target":"https://saliu.com/content/lottery.html","line":206},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":208},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":210},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":212},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":214},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":215},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":216},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":217},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":218},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":219},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":220},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":221},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":222},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":223},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":224},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":225},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":226},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":227},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":228},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":229},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":230},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":231},{"title":"The first lotto software to apply reversed lottery strategies with the best chance to win.","target":"https://saliu.com/HLINE.gif","line":233},{"title":"Forums","target":"https://forums.saliu.com/","line":235},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":235},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":235},{"title":"Contents","target":"https://saliu.com/content/index.html","line":235},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":235},{"title":"Home","target":"https://saliu.com/index.htm","line":235},{"title":"Search","target":"https://saliu.com/Search.htm","line":235},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":235},{"title":"You read about strategy, lottery, lotto, software, algorithms, number, pairings, pairs, frequency.","target":"https://saliu.com/HLINE.gif","line":237}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1235966,-0.03078781,-0.01789474,0.01412173,-0.05995537,0.08200973,0.04047421,0.01939891,0.02520459,-0.02977019,-0.01795541,-0.02433996,0.09947914,-0.02550905,-0.01263076,-0.02743687,-0.01947713,0.03067953,-0.00309776,-0.00656811,0.09842104,-0.05838541,-0.06693957,-0.06353428,0.05530891,0.01494008,-0.03741264,-0.06445199,-0.04542933,-0.20818448,0.03999778,0.04496541,-0.0005212,-0.02615261,-0.07686054,-0.03015481,-0.03891771,0.03797074,-0.0497218,0.056447,0.01370521,-0.00552665,0.03402525,-0.03408851,0.01579786,0.01697694,-0.04978358,0.00935149,0.02507959,-0.01186608,0.00446141,0.08709967,-0.03804801,0.02607907,0.0868099,0.10058697,0.07739484,0.08659267,0.03307139,0.06394073,0.01973081,0.03232023,-0.18288286,0.01338986,-0.00338603,0.01818204,0.03521205,0.00234082,0.01163175,0.06876206,0.03025522,0.02497619,-0.00447996,0.0255967,0.05257857,-0.00582708,-0.07297979,-0.0365071,-0.01167999,-0.004067,-0.02014193,-0.01918175,-0.01460191,-0.0059251,0.0385914,0.01662345,0.03856334,0.00268516,0.05160597,-0.09114179,0.00139805,0.05279111,0.04400643,-0.00513994,0.03273605,0.04324125,0.0327342,0.00189532,0.06027823,0.13714582,0.00459083,0.01696516,-0.02406704,0.06109715,0.03469488,-0.08068921,-0.04041568,-0.06019039,0.0421354,0.01439525,0.03695092,-0.00785439,0.08960675,-0.05404893,-0.00990401,0.04359849,0.0337046,0.0819502,0.03789562,0.00067188,-0.06453944,0.00548545,0.02089728,-0.00673697,-0.00559389,0.00673983,-0.00551157,0.09098287,0.0305362,0.05269727,0.02716732,-0.05386664,-0.10203807,-0.05547218,-0.05596311,-0.01531276,0.00270289,-0.04289792,0.03060756,0.04541651,0.00459551,-0.07592648,0.06075608,-0.08381408,0.03049848,0.07272785,0.00295332,0.01552256,-0.01291462,0.0418164,0.00352547,-0.02206427,-0.03118758,-0.10930656,-0.02182451,0.00626403,0.04259489,0.05035134,-0.00068597,0.05323272,-0.01804348,0.00097204,-0.05955103,0.12967318,-0.02812346,-0.10436495,0.00447788,0.01523285,-0.06937996,-0.0747547,-0.04853664,0.00112339,-0.0293563,-0.01524113,0.08815882,0.01281046,-0.06524967,-0.10111006,-0.00984058,-0.0099774,-0.02308273,-0.05466686,-0.03509248,-0.00216352,-0.01294083,-0.03244858,-0.02341704,-0.054316,0.02057811,0.05562039,-0.05840531,-0.05227164,-0.04481577,-0.02411185,0.00859665,-0.04575101,-0.02423607,-0.03054649,0.02624999,0.01897791,-0.047599,-0.00243444,-0.0026415,0.00146583,-0.01978622,0.07177285,0.02612651,-0.0612364,0.01961672,0.02312986,0.01689156,0.04817234,0.00225073,0.03971901,-0.04189051,-0.02461728,0.00001058,0.01678923,-0.01475355,0.0358781,0.00102469,0.00082724,-0.03344287,-0.18882976,-0.03522959,-0.03341833,-0.01512797,0.03240521,-0.03206323,0.01460985,-0.03788182,0.03266086,0.05094867,0.06735265,-0.03162657,-0.05633786,0.05210476,-0.03905555,0.01659115,-0.05561656,-0.04709728,-0.01059061,0.02980437,-0.03110288,0.03153175,-0.02158663,-0.07063352,0.08780038,-0.008383,0.13880025,-0.0017043,0.00724847,0.03785067,0.05890352,-0.0114819,-0.03744658,-0.03266844,0.01880569,0.01663198,0.03649962,-0.05254878,-0.04968804,0.00513347,-0.08632673,0.02112336,0.00675627,-0.06143986,-0.05702943,0.02410499,-0.02102554,-0.01680216,0.04463707,0.07466379,0.02002494,-0.0122571,0.00832766,0.05233365,0.05853798,0.01909896,-0.05833145,-0.01858142,-0.03711439,-0.00637039,-0.01751207,0.02433492,0.00396404,-0.02703292,-0.01254891,0.01894332,0.01061883,-0.01366613,0.0008633,0.02298099,-0.01185481,0.08898016,0.03703616,0.04795479,-0.026443,0.00675407,0.03344879,-0.08299392,-0.03696476,-0.02285467,0.04417748,-0.07588425,0.05791565,0.02965377,0.06960626,-0.02404668,0.07898024,0.03458351,-0.01144226,0.00195252,-0.02421714,-0.00198201,-0.00023227,0.01996993,0.02984547,-0.02767647,-0.26247588,0.01096981,-0.08150223,0.07194091,-0.04059402,-0.03465424,0.00057381,-0.01701602,-0.01389981,-0.07303022,0.03086442,0.03965246,0.00779964,0.0073659,0.03867322,-0.00413734,0.05897343,-0.08859871,0.04820008,0.02635927,0.05732228,0.02469032,0.24123183,-0.00251501,0.04294373,0.0348177,0.00136026,0.04950724,-0.02738291,0.02675675,0.00575557,-0.06209864,0.04916075,-0.01063926,0.00481,0.05301331,-0.00644717,0.02411028,0.02867485,0.01796617,-0.08802208,-0.05842381,0.00623338,-0.01872104,0.17150101,0.02264284,-0.0185556,-0.03008701,0.01014616,0.05009881,-0.0863926,-0.0158417,-0.06680563,0.01972596,-0.02349847,0.02629895,-0.02940991,0.00503475,0.01859157,0.00020049,0.06929759,0.00961369,0.0151053,0.02868819,-0.01790759],"last_embed":{"hash":"12ppgdn","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"12ppgdn","at":1753423494010},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#{1}","lines":[10,15],"size":288,"outlinks":[{"title":"Read an introduction to reduction of lotto tickets with reversed lottery strategy on pairs.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency##I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1142515,-0.02359057,-0.0375353,0.03599077,-0.05114719,0.09553657,0.03834675,-0.00121276,0.03319672,-0.02539775,-0.03741531,-0.02845019,0.10301539,-0.02613396,0.01398048,-0.03337371,-0.03248528,0.05969477,-0.008399,-0.02583104,0.09589775,-0.05989926,-0.0305155,-0.04246591,0.06252366,0.0200246,-0.03036057,-0.07751911,-0.04051664,-0.24109447,0.03841979,0.03324958,-0.01342361,-0.04412382,-0.07401995,-0.02635358,-0.05136103,0.06540272,-0.06483801,0.05367224,0.00905538,-0.0112604,0.04803469,-0.01807292,0.01184681,0.01508812,-0.05648116,0.00372385,0.01667134,-0.00794298,-0.03726275,0.04839412,-0.04977243,0.0402023,0.08790604,0.08429562,0.08887017,0.08450542,0.01244907,0.05601146,0.03139811,0.05062785,-0.18669729,0.0149864,-0.01161857,0.02654459,0.005138,-0.00233855,0.01030806,0.07680194,0.00273223,0.00950835,-0.01588417,0.01827405,0.04822968,-0.01941251,-0.05522285,-0.03780254,-0.01485261,0.008446,-0.02484002,0.00715833,-0.01094949,-0.01423433,0.02393747,0.02169791,0.03047461,0.02804289,0.0595545,-0.09439253,0.02883159,0.06599846,0.05686091,0.0163018,0.03507034,0.02963379,0.02061941,0.02331422,0.03609144,0.13982499,-0.01098594,0.02387826,-0.01792627,0.02117571,0.05894811,-0.07417545,-0.0168344,-0.05290942,0.05491284,0.0405204,0.01813334,-0.00656954,0.05348453,-0.04187089,-0.00667914,0.07610276,0.02787231,0.07223791,0.00355763,-0.016497,-0.04622555,-0.00432994,0.01263562,0.00431411,0.00381135,-0.02331324,0.00312205,0.06844731,0.06046276,0.06379389,0.00576149,-0.03868577,-0.12853651,-0.06339709,-0.07789998,0.0015743,-0.00025392,-0.00683381,0.0277267,0.03836602,-0.02156835,-0.07618998,0.06132379,-0.05442731,-0.00589545,0.09719587,0.00333972,0.0207914,-0.00188304,0.03670821,-0.01129161,0.02658194,-0.00668715,-0.084118,-0.01040676,-0.01615082,0.04733948,0.05531359,-0.00799743,0.03671058,-0.02246882,-0.01461225,-0.05580503,0.1551827,-0.01890399,-0.09186484,-0.00585434,0.04178766,-0.05652385,-0.05493162,-0.03822491,-0.01578184,0.00762611,-0.03652578,0.09123406,0.01175935,-0.05993138,-0.09309193,-0.00495857,0.00957152,-0.05358056,-0.04732538,-0.03998164,0.00490631,-0.04422204,-0.05118785,0.0024856,-0.05498706,0.04930859,0.04656349,-0.09242241,-0.05253628,-0.07173763,-0.00828438,-0.0037788,-0.03170447,-0.0308097,-0.05432241,0.00481115,0.01932901,-0.02267913,-0.00651155,0.00648589,-0.00623175,-0.0258164,0.04806621,0.02352784,-0.04824102,0.05437381,0.03474791,-0.00847126,0.04432683,-0.00561844,0.01547717,-0.02707266,-0.01994774,0.00640072,0.00855116,-0.03150973,0.04358027,0.00325217,0.00196284,-0.05480134,-0.19113931,-0.01832907,-0.02742796,-0.01766616,0.02653217,-0.05425911,0.0080107,-0.01188581,0.02440158,0.06470437,0.06751981,-0.0073073,-0.05971504,0.04906576,-0.00810323,0.00977707,-0.03078941,-0.02606379,-0.02243909,0.01797452,-0.03175076,0.02212648,0.00197454,-0.04546069,0.05160316,-0.02520918,0.13341415,0.00913324,0.0051429,0.04445394,0.02773079,0.03027717,-0.04511908,-0.0346215,0.0158201,0.02064426,0.02723912,-0.05267648,-0.01305126,-0.01502255,-0.0704501,0.01858729,-0.01243379,-0.08066025,-0.04029099,0.0203153,-0.04448281,0.01374165,0.04603207,0.08457711,0.03993831,-0.01325838,0.0396495,0.04131703,0.06791345,-0.00935174,-0.06148659,-0.03835365,-0.05528134,0.01183385,-0.03636626,0.01438987,-0.00571682,-0.03955538,-0.00867353,0.02651216,0.0031641,-0.02464888,0.00720329,0.02241416,0.00021411,0.10295176,0.04326179,0.01630938,-0.02593571,0.02681546,0.01107858,-0.09329743,-0.03021809,-0.03751766,0.04428364,-0.06221004,0.07178421,0.04853143,0.05128222,-0.0210951,0.07505567,0.02796494,0.02646297,0.0031764,-0.0350388,0.02538901,-0.01373977,0.0298711,0.04435757,0.02511411,-0.26725394,0.00404871,-0.06050346,0.04820402,0.00390369,-0.01350947,0.03341131,-0.02042394,-0.00154228,-0.09492433,0.01313682,0.03284248,0.00823182,0.00704639,0.01706115,0.00114281,0.05223393,-0.08242001,0.06165941,0.00987422,0.03108325,0.01307193,0.21648739,-0.00126326,0.00191014,0.00503186,-0.00777401,0.04814636,-0.01024804,0.03158067,0.00285568,-0.05334864,0.05433865,-0.00064842,0.00091756,0.05181231,-0.02577826,0.02622249,0.04036568,0.01423256,-0.09009265,-0.05210332,0.01565392,-0.00779561,0.18488915,-0.00156494,-0.02545158,-0.05536445,0.02546027,0.03365952,-0.08745803,-0.03807467,-0.04473591,0.01571487,0.0068676,0.04014636,-0.02018464,-0.01943848,0.02455475,0.00055392,0.0528563,0.0101533,0.03096191,0.04910697,-0.00288184],"last_embed":{"hash":"1ir9odo","tokens":344}}},"text":null,"length":0,"last_read":{"hash":"1ir9odo","at":1753423494043},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency##I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics)","lines":[16,24],"size":1020,"outlinks":[{"title":"Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics","line":1},{"title":"Software for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Software","line":2},{"title":"Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Reports","line":3},{"title":"Generate Combinations or Purge Files for _Dedicated LIE Elimination_ Strategies","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Combinations","line":4},{"title":"Strategies for _Dedicated LIE Elimination_ on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Strategies","line":5},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Resources","line":6},{"title":"We know that all lottery strategies miss more often than hit winners therefore we eliminate output.","target":"https://saliu.com/HLINE.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency##I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11301371,-0.02208187,-0.03544317,0.03370721,-0.04716653,0.08836159,0.03364187,-0.003241,0.03391496,-0.01978201,-0.03365551,-0.03039745,0.10584652,-0.03018567,0.00883844,-0.03554032,-0.02727854,0.05811467,-0.01081783,-0.02579192,0.09341621,-0.05837924,-0.03062432,-0.04619069,0.05995854,0.0198702,-0.0370355,-0.07958193,-0.03907593,-0.24003029,0.03981767,0.03070917,-0.01288362,-0.04751122,-0.0726259,-0.02517227,-0.05290404,0.06677087,-0.06855638,0.05126664,0.00940111,-0.01073902,0.05020802,-0.02382008,0.00918519,0.01712478,-0.0552554,0.00618857,0.01974676,-0.00601748,-0.03782326,0.04916559,-0.04962271,0.04157123,0.08414545,0.07775031,0.0845983,0.08331988,0.00946371,0.05890426,0.03573571,0.05223282,-0.18917577,0.01072889,-0.01337503,0.02870025,0.00387981,-0.00222395,0.01021269,0.08628216,-0.00197471,0.00535293,-0.01694828,0.02340779,0.05416162,-0.02195555,-0.05484954,-0.03711393,-0.0211677,0.00260153,-0.0292169,0.00303635,-0.01365469,-0.00117757,0.01860945,0.02477665,0.03361222,0.03133738,0.06573337,-0.09175526,0.02538401,0.06090118,0.05761585,0.01502249,0.03752521,0.02578074,0.02635019,0.02481141,0.03886158,0.14135854,-0.01354713,0.01659989,-0.01825255,0.01687025,0.05834787,-0.07882168,-0.02008521,-0.05110249,0.05061141,0.03815246,0.02646502,-0.00188053,0.05378649,-0.04593088,-0.0086688,0.07432456,0.02802842,0.07666282,0.00032216,-0.01159343,-0.0468257,-0.0054991,0.01275453,0.00152384,0.00575045,-0.01955389,0.00188394,0.07277944,0.05449136,0.06289945,-0.0001333,-0.04002238,-0.13401832,-0.06019101,-0.07447844,-0.00381996,-0.00216796,-0.00914075,0.01763105,0.04217923,-0.02125293,-0.06801839,0.06331242,-0.05497183,0.00362609,0.09746044,0.00222627,0.01615466,0.0011371,0.03482854,-0.00632735,0.02876218,-0.01091064,-0.08733433,-0.00678488,-0.00981454,0.05162775,0.05213021,-0.01253979,0.03779634,-0.02194884,-0.01046535,-0.05117307,0.15751685,-0.01789431,-0.08969419,-0.00799536,0.05339859,-0.05469084,-0.05394297,-0.03650574,-0.01637402,0.00591294,-0.04095413,0.09051546,0.00663529,-0.06284042,-0.0947258,0.00248294,0.00707776,-0.04773399,-0.04518672,-0.03426732,0.00429474,-0.04427322,-0.05136836,0.00538127,-0.0502654,0.04933972,0.04932546,-0.09051517,-0.05135968,-0.06799655,-0.01525596,-0.00198273,-0.03363356,-0.03061374,-0.05161531,0.00688783,0.01170802,-0.02604373,-0.00595711,0.01163078,-0.00148485,-0.01846197,0.04927707,0.01726326,-0.05202849,0.05582986,0.03007832,-0.00866748,0.04365565,-0.00505984,0.02127261,-0.03382021,-0.02050231,0.00501818,0.01177637,-0.03374129,0.04089381,-0.00102269,0.00097834,-0.05830679,-0.19115306,-0.02007247,-0.02549694,-0.01066898,0.02657881,-0.05699757,0.00232844,-0.01331343,0.02185194,0.06683105,0.06292456,-0.0065309,-0.06229926,0.0495242,-0.00950148,0.00450719,-0.03144382,-0.02650402,-0.02321252,0.01355508,-0.03502139,0.01323122,-0.00132324,-0.04367882,0.05758519,-0.0299968,0.13105665,0.01041331,0.01318171,0.04724689,0.02554016,0.03283888,-0.04383467,-0.03242475,0.019591,0.02753882,0.02756639,-0.05634455,-0.01309677,-0.01513567,-0.06848868,0.01779733,-0.00689132,-0.08293168,-0.03472065,0.01992697,-0.04261948,0.00663405,0.0409091,0.08332007,0.03679304,-0.01169636,0.03329873,0.03969003,0.06821819,-0.0046722,-0.06418823,-0.03904057,-0.05120527,0.00603784,-0.02676959,0.01205575,-0.00179168,-0.0383851,-0.00558052,0.02735319,0.00122289,-0.02266634,0.00893285,0.02730503,0.00553508,0.10790243,0.03751942,0.01904881,-0.02453856,0.02609126,0.01670028,-0.09900372,-0.03205313,-0.03680691,0.04424813,-0.0627491,0.07306893,0.04876193,0.05393692,-0.0232942,0.0741922,0.02654611,0.02595282,0.00536265,-0.03204166,0.02400749,-0.01208068,0.03448419,0.03889687,0.02915939,-0.26291502,0.00113405,-0.05956372,0.05411819,0.00046321,-0.01545956,0.03149179,-0.01824093,0.00270723,-0.09269467,0.01576097,0.02956882,0.00707234,0.00281363,0.02001457,0.00155458,0.05736001,-0.08357619,0.06409207,0.01477341,0.03159178,0.01271509,0.22260697,-0.00144263,0.00466658,0.00655097,-0.011418,0.04752186,-0.00956877,0.03450787,0.00325766,-0.05400063,0.05520866,0.00357249,-0.00470814,0.0537446,-0.02153685,0.0225531,0.03656795,0.01005451,-0.09246322,-0.05346616,0.01004499,-0.01125759,0.18178008,-0.00199197,-0.02348297,-0.05263778,0.02117187,0.03556763,-0.08697892,-0.03434061,-0.04976032,0.01252308,0.01117544,0.04233941,-0.01982529,-0.01875142,0.02261315,-0.00526213,0.05190711,0.0138412,0.03072406,0.04398548,-0.0062289],"last_embed":{"hash":"1yysqqg","tokens":298}}},"text":null,"length":0,"last_read":{"hash":"1yysqqg","at":1753423494136},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency##I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics)#{1}","lines":[17,24],"size":841,"outlinks":[{"title":"Software for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Software","line":1},{"title":"Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Reports","line":2},{"title":"Generate Combinations or Purge Files for _Dedicated LIE Elimination_ Strategies","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Combinations","line":3},{"title":"Strategies for _Dedicated LIE Elimination_ on Pairs, Number Frequency","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Strategies","line":4},{"title":"Resources in Lottery Lotto Software, Strategies, Systems","target":"https://saliu.com/lie-lottery-strategies-pairs.html#Resources","line":5},{"title":"We know that all lottery strategies miss more often than hit winners therefore we eliminate output.","target":"https://saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10052571,-0.06267088,-0.01644207,0.02171787,-0.05788418,0.09283417,-0.00485333,0.00949441,0.02989377,-0.02721567,-0.03362417,-0.02304102,0.0923513,-0.01602389,-0.00488147,-0.03291944,-0.01926846,0.0629327,-0.02796906,-0.03079043,0.09386805,-0.05679882,-0.03570859,-0.04174316,0.04464052,0.02877515,-0.04854434,-0.06572403,-0.05620795,-0.23931004,0.04865776,0.02616095,0.01771175,-0.02085962,-0.08194061,-0.01582456,-0.04052099,0.01751823,-0.05135506,0.05855241,0.01723253,-0.00959809,0.02895664,-0.03272802,0.01715581,-0.00173363,-0.05684721,0.00992477,0.03857333,0.00332951,-0.02311216,0.03238428,-0.02335376,0.03841983,0.06974643,0.05941013,0.08026098,0.08408758,0.01866068,0.07653638,0.02172014,0.06086202,-0.18832353,0.02143284,-0.01333659,0.01631619,-0.0025499,-0.04032426,0.03229697,0.06361243,-0.00748405,0.01735507,-0.01588834,0.01031424,0.01530625,-0.01610679,-0.01861626,-0.04320844,-0.01784347,-0.00124061,-0.03139926,0.00727674,-0.00400187,-0.0393269,0.03175298,0.03450996,0.02863704,0.04194152,0.06355643,-0.07271156,0.00892911,0.03691188,0.0727962,0.02179852,0.03808027,0.02530684,0.01342163,0.02311949,0.02378142,0.11133306,0.01848678,0.00678271,-0.05017392,0.02446361,0.06664673,-0.06723434,-0.03772887,-0.07153422,0.02459994,0.03334406,0.01671059,0.00270288,0.08792855,-0.03833225,-0.00910754,0.08147728,0.00131665,0.03447176,0.02506216,-0.00610742,-0.04894266,0.00909069,0.02407053,0.01425161,0.00867979,0.00220333,0.04091757,0.07019421,0.02399314,0.05464565,0.01614292,-0.02612571,-0.12985703,-0.06002743,-0.04774748,0.00473433,-0.01696706,-0.00826153,0.02358546,0.05505761,-0.04230402,-0.08469732,0.08119498,-0.04999593,-0.02919418,0.07947783,0.02327993,-0.00567089,-0.01606525,0.03064562,-0.00115853,0.02543507,-0.0373082,-0.04789126,0.01329807,-0.00117414,0.03274834,0.06415419,-0.0291351,0.02481953,-0.03910943,-0.011422,-0.0506504,0.14129043,-0.02434309,-0.1240451,0.00791206,0.05514017,-0.06620933,-0.04321417,-0.04898199,-0.01548416,-0.0292885,-0.01668736,0.08808058,0.00221824,-0.09596856,-0.07939076,0.00842329,-0.00593576,-0.02430268,-0.01821187,-0.04458422,0.01309627,-0.05921791,-0.05514481,0.01781582,-0.02693472,0.03239634,0.03605336,-0.07274842,-0.04705634,-0.06684717,-0.02888797,0.00740313,-0.04669024,-0.03911112,-0.04149507,0.02238378,0.00221068,-0.01274403,-0.00961155,0.03066781,0.01602382,-0.00941719,0.07051189,-0.00784338,-0.04888364,0.05702084,0.04107688,-0.00609971,0.04140952,0.00040672,0.03254274,-0.01656349,-0.00001228,-0.00370385,0.00168147,-0.02364401,0.04376512,0.00249894,0.03711019,-0.02499875,-0.2098466,0.01340584,-0.02308425,0.00004779,0.04479482,-0.05517501,0.02216951,-0.00964443,0.05391405,0.08551599,0.09253787,0.00468453,-0.07005925,0.04570682,-0.02846649,0.03981035,-0.04429221,-0.03360143,-0.03182917,0.00857066,-0.02318633,0.02410749,0.00108046,-0.05202565,0.06965157,-0.02256262,0.12640534,0.00338715,0.01608324,0.02685101,0.03293277,0.00976595,-0.01390504,-0.02670833,0.01506442,0.02107311,0.03890191,-0.04069657,-0.02529306,-0.01699332,-0.08831536,0.01945163,0.01362967,-0.11646482,-0.02368893,0.02636182,-0.02476308,0.00979845,0.05911022,0.06136622,0.04772329,-0.02431517,0.03477512,0.0431654,0.04993739,-0.02257459,-0.04952307,-0.03862103,-0.01546277,0.02310049,-0.02303942,-0.01101605,0.00500417,-0.05262931,-0.01238942,0.01109823,-0.01006815,0.00621361,0.03571283,0.02264412,-0.00515718,0.07487046,0.04611588,0.01132049,-0.04797647,0.02131173,0.03350847,-0.08426331,-0.00542409,-0.01835412,0.02526633,-0.05317431,0.07992809,0.05241473,0.07101677,-0.01822235,0.07525747,0.04504599,0.04187448,-0.00392824,-0.02165408,0.01498473,-0.02806568,0.02800883,0.0535584,0.01002512,-0.27962312,-0.02329432,-0.09242884,0.05901036,-0.00411002,0.00285886,0.03312912,-0.01072445,-0.01250115,-0.11856785,-0.00016088,0.05643393,-0.00160263,-0.01365403,0.00706325,0.00032693,0.04804322,-0.08265173,0.06112578,-0.00043334,0.02478534,0.03761959,0.2079725,0.00002785,0.00785166,0.04068943,-0.02840702,0.06018627,-0.00422262,0.0354368,-0.01422871,-0.05723363,0.06650725,-0.02994478,-0.01085792,0.05063752,-0.03212484,0.02159368,0.03330827,0.03397585,-0.07901655,-0.05280113,0.00875041,-0.03226073,0.14418469,0.01110614,-0.03672061,-0.0460198,0.01355999,0.05713391,-0.09294469,-0.03280022,-0.07297699,-0.00418404,0.0192076,0.0623969,-0.01183212,0.00453782,0.01583211,0.00300518,0.05456655,-0.02191624,0.02677078,0.03644693,-0.01936526],"last_embed":{"hash":"dg7ysb","tokens":472}}},"text":null,"length":0,"last_read":{"hash":"dg7ysb","at":1753423494223},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>","lines":[25,73],"size":6818,"outlinks":[{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":17},{"title":"_**LIE Strategy Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":30},{"title":"Lie lottery strategies are designed for any parpalucks, both for lotto pairs and frequencies.","target":"https://saliu.com/images/parpaluck-report.gif","line":40}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09871516,-0.05994427,-0.01834208,0.03822409,-0.05985141,0.10025348,0.00823817,0.00962567,0.03408255,-0.0178975,-0.03297908,-0.02959781,0.07857328,-0.01688299,-0.00225468,-0.03209885,-0.03700215,0.05273711,-0.02363072,-0.02752847,0.09547673,-0.05376777,-0.04569203,-0.04358638,0.04804597,0.01456239,-0.04971845,-0.06898861,-0.05502902,-0.2433814,0.05212548,0.0303105,0.00844071,-0.01325836,-0.07668844,-0.00686038,-0.04931256,0.02796715,-0.03714737,0.06052452,0.01701759,-0.0052119,0.03198594,-0.02729784,0.00810268,0.00907572,-0.06967666,0.01228282,0.03986636,-0.00014193,-0.02186822,0.0398203,-0.02679595,0.0298665,0.08301704,0.0643851,0.09287138,0.08732711,0.0283145,0.07236152,0.02708981,0.05377638,-0.18639362,0.02315476,-0.01002327,0.01280241,0.00616191,-0.02406203,0.01680313,0.0757049,-0.00176083,0.01045494,-0.0077864,0.00875543,0.01985882,-0.00902042,-0.02915962,-0.04902792,-0.02103815,-0.00495777,-0.02864663,0.00299651,-0.00812414,-0.02291615,0.02630479,0.0251464,0.01468148,0.03305731,0.05964555,-0.06569089,0.01133312,0.04336334,0.06775217,0.03337418,0.02777025,0.01561104,0.01174163,0.02540105,0.041375,0.1187794,0.02293537,0.01320547,-0.03643936,0.03865865,0.0546518,-0.07215339,-0.05360335,-0.08584171,0.03286607,0.03479155,0.02566123,0.0004735,0.08759546,-0.04390552,-0.00376124,0.07831215,-0.00122084,0.04039765,0.02545635,0.00230753,-0.05157268,0.00830368,0.01931718,0.01325373,0.01101291,0.0011852,0.05220463,0.0731639,0.02844862,0.05079611,0.00369152,-0.03445253,-0.12703499,-0.04795989,-0.04845303,0.00285186,-0.00303843,-0.0159814,0.02082557,0.04444095,-0.02902116,-0.09397867,0.08603603,-0.05316265,-0.01303722,0.0776675,0.02982794,-0.00193726,-0.00405413,0.03976222,-0.01460431,0.02261796,-0.03590114,-0.06022159,0.002269,0.00488875,0.02112185,0.05062931,-0.01634806,0.03713218,-0.04196316,-0.01315388,-0.05816917,0.14001942,-0.02409737,-0.12058264,-0.00113767,0.04381289,-0.06856449,-0.04369851,-0.03971624,-0.01601915,-0.02438276,-0.01341939,0.08537009,0.00349227,-0.10425456,-0.08777976,-0.00427984,-0.00700052,-0.02442295,-0.01978762,-0.04188211,0.01170649,-0.06114519,-0.04757059,0.00907471,-0.03571326,0.02491918,0.04963288,-0.0590096,-0.05612542,-0.07170704,-0.02439213,0.00841875,-0.05277834,-0.03943633,-0.03557498,0.02216439,0.00203769,-0.03071304,-0.00347008,0.02976523,0.02037971,-0.00535218,0.05477609,0.00414406,-0.05336028,0.04965989,0.03676955,0.00740128,0.03688059,-0.00518552,0.01983049,-0.01579967,-0.00018606,-0.0012261,0.00823514,-0.02089948,0.03710958,0.01233351,0.0185084,-0.01088645,-0.20057333,0.00112323,-0.01708973,-0.00066588,0.04288528,-0.05118311,0.0231715,-0.00810419,0.05552895,0.0764911,0.06812934,0.00736028,-0.07470202,0.03759326,-0.02707943,0.05042832,-0.04361379,-0.03667291,-0.02385231,0.00753811,-0.02770678,0.03117037,-0.0165054,-0.04530633,0.07831825,-0.03040072,0.12947784,0.00110042,-0.00250596,0.03318977,0.03245503,0.00133557,-0.02293453,-0.01346716,0.03383256,0.01070945,0.04330152,-0.0562376,-0.04329936,-0.02289622,-0.09605096,0.01639145,0.01361518,-0.10474292,-0.04149526,0.01817572,-0.02884014,0.00691513,0.06417475,0.06501434,0.03482948,-0.02118806,0.03748458,0.03795749,0.0513413,-0.00969595,-0.05363337,-0.02478556,-0.01431548,0.01560742,-0.01592407,-0.00155206,0.00150837,-0.04510599,-0.01147367,0.00542076,-0.00182345,-0.00811449,0.0225991,0.02614678,-0.00544275,0.07816844,0.05315305,0.02129219,-0.04210055,0.0103284,0.02689487,-0.1037754,-0.0004834,-0.01639716,0.02965779,-0.06483887,0.07252222,0.05427736,0.08269179,-0.01934002,0.07710943,0.03864878,0.0361372,-0.0119518,-0.01841788,0.01092886,-0.01347493,0.02948709,0.05363018,-0.00044566,-0.27423772,-0.00231396,-0.08909486,0.06991291,0.00111766,0.00468235,0.02754851,-0.00852348,-0.01417078,-0.11698149,0.01375176,0.05576767,0.00620053,-0.00816501,0.01310197,-0.00389944,0.05209655,-0.09063324,0.05058369,0.00037983,0.03329646,0.03591495,0.20522338,-0.00382335,0.01292127,0.02887422,-0.02607009,0.06293086,-0.00747457,0.02952866,-0.01041052,-0.05675879,0.06467388,-0.03557123,-0.00839533,0.0591527,-0.01762359,0.02193142,0.027709,0.03675948,-0.08041462,-0.0520677,0.00635104,-0.03165369,0.15334979,0.01532246,-0.02943139,-0.05445678,0.01612617,0.05354063,-0.09379227,-0.02702044,-0.07792892,0.00232614,0.0060458,0.05595791,-0.00790178,0.01447094,0.02082708,0.01105994,0.05334628,-0.01945426,0.02333387,0.02506847,-0.02660744],"last_embed":{"hash":"1rdz9cm","tokens":264}}},"text":null,"length":0,"last_read":{"hash":"1rdz9cm","at":1753423494362},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{1}","lines":[27,36],"size":655,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13100985,-0.04991213,-0.02358894,0.00291957,-0.05609133,0.0981305,-0.00318281,0.02804335,0.02399817,-0.03129446,-0.0315608,-0.01002529,0.08650478,-0.0115172,0.00986968,-0.01949437,-0.02093682,0.03039682,-0.01083291,-0.01520957,0.10420436,-0.04218896,-0.02201895,-0.05109397,0.06560539,0.02672446,-0.05381022,-0.08103653,-0.03605571,-0.21681342,0.036093,0.02798802,0.00658277,-0.02429931,-0.06285994,-0.02828164,-0.02914692,-0.00196165,-0.05314344,0.04102987,0.00574843,-0.00249894,0.04452519,-0.04309691,0.01706406,-0.00206503,-0.04586442,0.01253864,0.04178023,0.00709787,-0.00287188,0.08287426,-0.02891376,0.05572328,0.05300779,0.06516299,0.08087207,0.07350632,-0.0030169,0.08469826,0.01462946,0.01722023,-0.19740038,0.03277973,-0.00995106,0.02173572,0.00201636,-0.00844816,0.03617743,0.05167227,-0.00651115,0.00447114,-0.02129532,0.03501448,0.05215359,-0.02977189,-0.03714489,-0.02062256,-0.00954541,0.01739077,-0.02715879,-0.00308475,0.0099372,-0.04224512,0.02617433,0.01656249,0.04732019,0.0365067,0.06265295,-0.08668086,0.01007498,0.0288131,0.07192165,0.00841464,0.02850067,0.03075184,0.02536908,0.01846679,0.02173987,0.11969394,-0.02236364,-0.01497293,-0.02780904,0.03511685,0.05829472,-0.06299039,-0.02161617,-0.05554601,0.02933622,0.01569935,0.01835848,0.01369542,0.0757316,-0.05212092,0.01223592,0.06849629,0.00309228,0.06565468,0.04573077,-0.02381645,-0.04955557,0.01323175,0.01932986,-0.00251124,0.00671117,-0.02221823,0.00605173,0.06200776,0.02889961,0.06743167,0.03371583,-0.02039275,-0.12019878,-0.07270735,-0.04531007,-0.00210873,-0.01993155,-0.02824778,0.02779915,0.08172055,-0.01357507,-0.06144705,0.03915677,-0.05745497,-0.02212346,0.0951139,0.01945236,-0.00179002,0.0053578,0.04278051,0.00912817,0.00676453,-0.01437375,-0.07766933,0.00515382,0.00343191,0.08988674,0.0682473,-0.01965284,0.0263063,-0.0229683,-0.01770027,-0.03560759,0.16619775,-0.00487781,-0.11795989,0.02158481,0.04890079,-0.04613896,-0.05745507,-0.04385835,-0.01318247,-0.00275802,-0.01185804,0.10686035,-0.00332261,-0.06424264,-0.08875626,-0.00365604,-0.01410162,-0.03191186,-0.04544209,-0.03313566,0.01935319,-0.05201417,-0.05268727,-0.01063481,-0.04103487,0.03001054,0.02327321,-0.09444178,-0.0379893,-0.07952176,-0.01321967,-0.00021397,-0.02814546,-0.03942244,-0.04759411,0.00877006,-0.00983009,-0.01331559,-0.02184806,0.0067799,0.00619188,-0.00254595,0.07975491,0.00832885,-0.03502237,0.07165026,0.01755695,-0.01218687,0.04437547,0.01563593,0.02169584,-0.03621635,-0.02939391,0.00465715,0.01290805,-0.01902022,0.05614349,-0.00619487,0.04123403,-0.0533585,-0.20823996,0.01873735,-0.05541869,-0.02326846,0.03673805,-0.05293533,0.02397598,-0.01369351,0.04207548,0.07055054,0.10513318,0.00655582,-0.05186524,0.04359175,-0.02804455,-0.00845956,-0.04177466,-0.01838751,-0.01671313,0.00727211,-0.04012473,0.00684914,0.00334077,-0.03411032,0.07594863,-0.01374252,0.11434381,0.02359794,0.02742687,0.02022032,0.03761728,0.01075971,-0.02569911,-0.08005398,-0.00268423,0.04162608,0.01239737,-0.05223766,-0.02411736,-0.01273401,-0.07939952,0.01237725,0.02227915,-0.08530098,-0.05685373,0.01486986,-0.03372093,0.01140044,0.05768777,0.06501433,0.02924008,-0.03061191,0.03534348,0.06356715,0.03899844,-0.02274121,-0.04897617,-0.025293,-0.03726477,0.00878394,-0.00212012,0.00357561,0.01682539,-0.06070983,-0.01815729,0.00927086,-0.01325533,-0.0075781,0.03277573,0.01338862,-0.01168674,0.08757561,0.0322349,0.0063298,-0.04034527,0.00163249,0.02133695,-0.07563703,-0.02843685,-0.03404441,-0.01515918,-0.05806794,0.08652943,0.04634512,0.08474413,-0.00000575,0.07600368,0.037649,-0.00362257,0.01826215,-0.02110235,0.01116705,-0.02388296,0.02730803,0.03850762,-0.00690226,-0.27855256,-0.00778944,-0.0718798,0.06687911,-0.00821838,-0.00579414,0.02814296,-0.03890892,-0.02758753,-0.09526113,0.02987568,0.03117032,-0.03166119,-0.00461933,0.02290061,0.02961092,0.06568001,-0.07805698,0.05550858,0.00740058,0.02348344,0.03635546,0.22282942,0.00904073,0.01227456,0.04741017,-0.01091631,0.07039734,0.00680851,0.03693865,-0.00587189,-0.06120828,0.05055835,-0.01762725,-0.01261138,0.04184172,-0.02409023,0.01892841,0.05118515,0.02043154,-0.07744999,-0.07701284,0.01933034,-0.01349314,0.15091281,0.01115101,-0.03099347,-0.04210607,-0.00194715,0.04755412,-0.07295341,-0.02130051,-0.06070909,0.00834372,0.00703802,0.03499522,-0.01797199,-0.0215576,0.01321829,-0.00757052,0.06682041,0.00385476,0.02395979,0.04101013,-0.0132763],"last_embed":{"hash":"tz48ck","tokens":176}}},"text":null,"length":0,"last_read":{"hash":"tz48ck","at":1753423494433},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{3}","lines":[38,38],"size":485,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10074162,-0.0263423,-0.00870216,0.01831233,-0.0675853,0.09668705,0.04750516,0.00732887,0.03310277,-0.03413596,-0.02659858,-0.02367759,0.10422124,-0.01704495,0.00798931,-0.02734394,-0.03037031,0.0691327,-0.01543745,-0.02235974,0.11539182,-0.05286768,-0.05218431,-0.05405054,0.06490573,0.0262052,-0.02492503,-0.07035179,-0.03748742,-0.24140789,0.05513489,0.02069987,0.00519381,-0.03540056,-0.0629722,-0.04203048,-0.04705384,0.04772972,-0.08084069,0.05619207,0.02290539,-0.01397813,0.04712792,-0.02800159,0.01775942,0.01693325,-0.07014948,0.02082118,0.0274717,0.01257312,0.00219905,0.05123164,-0.03444162,0.04072317,0.07652298,0.07506829,0.07923138,0.07448059,0.02296901,0.08188291,0.01583294,0.03277082,-0.15934396,0.01504336,-0.01483373,0.02070862,0.02238753,-0.02799472,0.02380483,0.08028618,-0.00558411,0.01640026,0.00289104,0.0164933,0.0253105,-0.02216774,-0.0525337,-0.04874257,-0.00865162,0.0194837,-0.03237924,-0.00946005,-0.01159253,-0.03898123,0.02739006,0.0229431,0.02504714,0.0382923,0.04348321,-0.07738443,0.02971313,0.06246307,0.03982948,-0.00067787,0.03389988,0.03850516,0.00873187,0.02742741,0.02916401,0.14565082,-0.00619352,0.01178162,-0.04185984,0.01628409,0.06297413,-0.06643184,-0.03667706,-0.06598435,0.03050119,0.03040391,0.01528308,0.00181076,0.06400541,-0.04359159,0.01216777,0.08809061,0.02810329,0.05644209,0.00189699,-0.01519706,-0.037707,-0.0044961,0.01857396,0.0065795,0.00091357,-0.02467364,0.01979602,0.0819956,0.03617487,0.05422185,0.02239774,-0.05032774,-0.11918776,-0.08638754,-0.05002327,0.00511068,-0.00500199,-0.00850759,0.01070772,0.03651458,-0.03964831,-0.04734567,0.05889118,-0.05582111,-0.003184,0.07046638,0.00893957,0.00942127,0.00106172,0.03306982,-0.00412046,0.01444428,-0.01812486,-0.08225739,-0.00453831,-0.00304508,0.03701957,0.04865821,0.01471271,0.03076724,-0.05108174,-0.01388721,-0.04457324,0.16047288,-0.02719115,-0.08096952,-0.00529375,0.02738744,-0.05041081,-0.0605796,-0.04273976,-0.03292676,0.00373024,-0.02112609,0.08105434,0.02211554,-0.07262246,-0.07225496,-0.00982622,-0.00308239,-0.0382134,-0.03975409,-0.04813905,0.00448206,-0.05315809,-0.06084535,0.00497717,-0.04022144,0.04094005,0.03706812,-0.08050449,-0.03320743,-0.08950851,-0.0167351,0.00565592,-0.04721674,-0.0425186,-0.05286952,-0.0003713,0.01332251,0.01458136,-0.00737898,0.00443588,0.01584906,-0.00943522,0.05396815,0.0157625,-0.04691636,0.04974524,0.04808622,-0.01186755,0.04164575,0.00832135,0.0306718,-0.01271358,-0.02148136,0.00063578,-0.00954211,-0.03448338,0.03677933,0.00157048,0.01311673,-0.02781663,-0.21028283,-0.00072221,-0.00781212,-0.0204676,0.02830317,-0.0613756,0.00323191,-0.01697545,0.03152053,0.06777417,0.09117752,-0.00407867,-0.05572923,0.06166289,-0.0243388,0.02974588,-0.03467055,-0.03555301,-0.01316092,0.01107533,-0.02829166,0.02133949,0.00234247,-0.0495656,0.05270638,-0.01369013,0.13579635,-0.00677807,0.02817026,0.03088499,0.04044795,0.01513975,-0.02964132,-0.01589159,0.01087338,0.01333991,0.02512042,-0.07158439,-0.01069574,-0.02050829,-0.07714833,0.01383455,-0.01460663,-0.08204587,-0.01772773,0.02859841,-0.04367643,0.01034521,0.06268793,0.08801789,0.05148113,-0.03344291,0.04876278,0.04237416,0.0578939,-0.0230457,-0.05884277,-0.03879343,-0.04625612,0.01276985,-0.03462544,0.0077228,-0.0133406,-0.03053868,-0.02062853,0.02121469,-0.00626118,-0.01803665,0.03021429,0.01729766,-0.000565,0.08755647,0.03293313,-0.00456454,-0.0314992,0.01480785,0.00588997,-0.07830925,-0.02797087,-0.04466273,0.06185806,-0.04181053,0.05932806,0.03747663,0.05550709,-0.03507338,0.07342792,0.04521473,0.01219648,0.01157581,-0.02098045,0.03313734,-0.01698993,0.04533544,0.02870723,0.00061367,-0.28288415,-0.01023798,-0.10226575,0.05359232,-0.00214583,-0.01272967,0.02423848,0.00573431,-0.01633957,-0.1056414,0.0104578,0.04704944,0.00755214,0.01447016,0.02725761,-0.00253905,0.05591578,-0.07979632,0.05837972,-0.00407186,0.04543704,0.02466433,0.21655068,-0.00621725,0.00613157,0.03012146,-0.00630942,0.03656128,0.00379744,0.02379442,-0.01331023,-0.05579656,0.05211272,-0.02012623,0.00691914,0.04442155,-0.01891114,0.05141775,0.03630956,0.03052662,-0.09384527,-0.05463215,0.03221666,-0.03098282,0.17144866,0.00248499,-0.03636364,-0.04982804,0.00999993,0.04925195,-0.07612301,-0.01188009,-0.05922057,-0.00945551,0.0030485,0.050731,-0.03119779,-0.00054325,0.0239018,0.01907488,0.05470578,0.01535412,0.03542463,0.03454795,-0.02480851],"last_embed":{"hash":"4mowhz","tokens":132}}},"text":null,"length":0,"last_read":{"hash":"4mowhz","at":1753423494480},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{4}","lines":[39,40],"size":244,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10390974,-0.05115712,-0.02580768,0.0363648,-0.05697699,0.08081149,0.01776349,-0.01249849,0.04432343,-0.02581371,-0.04075153,-0.00348291,0.09091923,-0.01402232,0.00469634,-0.0336568,-0.03648821,0.07002161,-0.04036627,-0.02311307,0.06176781,-0.04468383,-0.04994522,-0.04291094,0.05860234,0.00268436,-0.03935567,-0.07256044,-0.05422965,-0.24993607,0.04897998,0.02874305,-0.00506748,-0.04634907,-0.0684485,-0.00728577,-0.05847135,0.03136415,-0.06153637,0.05809633,0.0308767,-0.00680402,0.0449531,-0.01725899,0.00320614,0.02576494,-0.066448,-0.00208577,0.0420377,0.01532117,-0.00795611,0.04451021,-0.02969592,0.04399581,0.06877272,0.07886031,0.08138821,0.09089289,0.00480473,0.05807902,0.00927288,0.05319887,-0.16862813,0.03147052,-0.00945876,0.00490119,-0.00071607,-0.02308539,0.03502024,0.10433203,-0.00828,0.01468864,-0.04147628,0.0133943,0.01381846,-0.02558598,-0.02677513,-0.01221142,-0.01282964,0.01838867,-0.02917306,0.00174685,-0.03127659,-0.01621489,0.02445837,0.02425904,0.0222776,0.0431739,0.05065724,-0.07886224,0.00527844,0.03365217,0.07954314,0.01059297,0.03370032,0.0085852,0.01190321,0.05591342,0.03855099,0.11447514,-0.00531478,0.01472437,-0.01543883,0.0034381,0.04906431,-0.08499875,-0.04802264,-0.05049155,0.04922452,0.0411613,0.03575157,-0.00850288,0.07313576,-0.05457788,0.01063598,0.08443158,0.00497404,0.05340295,0.00080855,-0.01597886,-0.03957639,0.01142014,0.02268437,0.01328396,-0.03194173,-0.02444353,0.03240014,0.0657707,0.02625805,0.02106283,0.01657434,-0.04552331,-0.1261481,-0.08012541,-0.04616196,-0.01006054,-0.00008791,-0.00876095,0.02991196,0.02462792,-0.03063157,-0.08015361,0.08285227,-0.05295071,0.00075286,0.05672745,0.02532761,-0.01248588,0.00297473,0.04198102,-0.01475189,0.02761723,-0.03760881,-0.0869389,0.01423477,0.00514305,0.04794899,0.05939953,-0.00418275,0.0350055,-0.02967696,-0.01679371,-0.06083114,0.12828776,-0.03230371,-0.12248021,-0.00321336,0.05375588,-0.06345165,-0.04281144,-0.03212866,-0.02329635,0.01074464,-0.04050089,0.09483195,0.00372277,-0.08836565,-0.09581976,-0.0173217,0.01020577,-0.02415743,-0.04070355,-0.04426057,0.01791125,-0.05199733,-0.05149937,0.01251464,-0.04054528,0.03475545,0.02781516,-0.08121746,-0.05160031,-0.07193594,0.00562582,0.00285571,-0.03099152,-0.02761271,-0.05359472,0.00723128,0.00499143,-0.0327242,-0.01497461,0.01356566,0.01529191,-0.02142764,0.05900407,0.00383233,-0.04818774,0.08563444,0.03346995,-0.01425457,0.02258288,-0.00233441,0.0343536,-0.0124593,-0.01548011,-0.00693431,0.00673964,-0.01641599,0.01922519,-0.00503473,0.00323439,-0.02186197,-0.2047977,0.00188344,-0.03492191,-0.01104848,0.03049765,-0.06420416,0.00378052,-0.00023228,0.0188177,0.0761597,0.06992282,-0.00387599,-0.06083729,0.04774807,-0.01011805,0.00272962,-0.03605571,-0.03434227,-0.03738482,0.02352902,-0.02520477,0.01566719,-0.00470268,-0.04633665,0.05344208,-0.01578964,0.14150904,0.02952165,0.03735244,0.04208691,0.04401851,0.01821145,-0.00733787,-0.02942015,0.0202686,0.01971746,0.04982757,-0.06548474,-0.00873819,-0.00157241,-0.08695412,-0.01127289,-0.01002677,-0.11502713,-0.02734065,0.01797974,-0.04955506,0.01621296,0.05999762,0.10589644,0.05372143,-0.03371014,0.02999812,0.06265482,0.05452457,-0.01918414,-0.06674482,-0.01790613,-0.0169013,0.00804304,-0.00836896,0.02064575,0.02020497,-0.04105864,-0.00437561,0.05058751,-0.01457306,-0.00837577,0.02481457,0.01089881,-0.01533635,0.09914581,0.04597697,0.01851685,-0.02144497,0.02888773,0.01705081,-0.08143799,-0.03801385,-0.03454289,0.01199785,-0.06903692,0.06499733,0.04720191,0.0539526,-0.02489057,0.06018249,0.0124133,0.03061765,0.00159834,-0.01997009,0.00324128,0.00735131,0.04811825,0.0580645,0.00329188,-0.26357698,0.00223531,-0.06581029,0.0452903,-0.00770616,0.01850832,0.03939122,-0.00414044,-0.00543421,-0.09568154,0.01208212,0.03893974,0.01776638,0.00266349,0.04142847,-0.00578272,0.02295841,-0.06326185,0.06369124,0.01611942,0.05741373,0.02831517,0.21983154,-0.01054877,-0.00129622,0.03091252,-0.02363495,0.05276304,0.00039543,0.01960385,-0.00008723,-0.04322748,0.04719081,-0.03345598,0.00843688,0.01514814,-0.01342625,0.04654158,0.02616921,0.01597233,-0.10913348,-0.04336885,0.02471472,-0.02544967,0.17387109,0.00515333,-0.00932606,-0.06302562,0.02120768,0.04469103,-0.07320205,-0.0104013,-0.04502503,-0.01544833,0.00966819,0.04328372,-0.01816186,-0.02041701,0.03741246,0.00007698,0.06755346,0.01129114,0.05085129,0.04463427,-0.02710612],"last_embed":{"hash":"1nrkkso","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"1nrkkso","at":1753423494525},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>#{5}","lines":[41,73],"size":5161,"outlinks":[{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":1},{"title":"_**LIE Strategy Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":14},{"title":"Lie lottery strategies are designed for any parpalucks, both for lotto pairs and frequencies.","target":"https://saliu.com/images/parpaluck-report.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09251596,-0.03593306,-0.0380748,-0.00129657,-0.04448631,0.07791439,0.01102028,-0.00106264,0.02014773,-0.01678356,-0.0393283,-0.00852844,0.10282801,-0.03217098,0.0108944,-0.0165296,-0.00653171,0.02634798,-0.04202366,-0.01842027,0.10228635,-0.05729047,-0.04766663,-0.07495137,0.04403124,0.01139887,-0.0521813,-0.08222777,-0.05335516,-0.24917726,0.06138969,0.04134969,0.02113359,-0.04109665,-0.06580458,-0.02554314,-0.05816215,0.02659706,-0.06084039,0.0251314,0.00017944,-0.00383186,0.05829645,-0.03816914,0.02175103,0.00833207,-0.04721669,0.01651704,0.063917,0.00770462,0.00036827,0.04085597,-0.03041869,0.058817,0.0689371,0.08033148,0.06623776,0.08204409,0.01213899,0.07392961,0.02863395,0.06079143,-0.17175007,0.00311355,-0.01139147,0.02881341,0.01625582,-0.04088386,0.02719103,0.04580803,0.00331256,0.03731147,-0.03081705,0.01906182,0.05765717,-0.02358498,-0.06730732,-0.0227898,-0.01304319,0.01337772,-0.03937556,-0.01143643,-0.01884929,-0.03069777,0.04740731,0.00758987,0.05041056,0.03712982,0.08252815,-0.075648,0.00439211,0.0486495,0.04775639,-0.0052297,0.04983381,0.04141118,0.03455611,0.01285879,0.02327797,0.1093974,-0.00477917,-0.00625254,-0.02329467,0.01417343,0.05936847,-0.08259497,-0.03885671,-0.03210899,0.01452431,0.03021066,0.0354487,-0.00010756,0.09147123,-0.03508105,-0.01216817,0.04621214,0.0060646,0.06151552,0.0120297,0.01030126,-0.05165018,0.0087552,0.01228844,-0.02400178,0.0060237,-0.00566701,0.00046558,0.06947342,0.02261423,0.06475098,0.01908388,-0.0272971,-0.11321677,-0.04066482,-0.045252,-0.01872419,-0.01448012,-0.02322973,0.00621452,0.03841578,-0.02846347,-0.05045946,0.08225659,-0.0691048,0.03074443,0.07732292,0.00278772,0.00098371,-0.00043117,0.02282096,0.01278031,-0.0067725,-0.04445482,-0.08467297,0.006466,0.00486668,0.05944234,0.07658978,-0.01654592,0.02988181,-0.00631428,0.0029536,-0.02878294,0.10844956,0.00058575,-0.10788826,-0.00170445,0.05844319,-0.07839084,-0.065786,-0.06268426,0.00722242,-0.02801441,-0.02264352,0.08517958,-0.01713925,-0.09260833,-0.08427089,0.02884309,-0.00280767,-0.01482182,-0.03419034,-0.02908509,-0.00078733,-0.04590947,-0.0604952,0.0108362,-0.03972824,0.02992787,0.02579307,-0.07193751,-0.02275055,-0.06271277,0.00896166,0.02014598,-0.04715658,-0.03170994,-0.02554748,0.03776596,0.0017253,-0.02132101,-0.01661816,0.02057995,0.00943406,-0.01514878,0.05873216,-0.01263704,-0.05558252,0.06446999,0.03485794,-0.00434641,0.02586837,0.02937537,0.06564833,-0.06995274,-0.008422,-0.0262288,0.0162199,-0.0137505,0.04939251,-0.00392313,0.01565244,-0.02958421,-0.21515998,0.00165611,-0.03521374,0.00981049,0.01756961,-0.04513453,-0.00053076,-0.02883746,0.04451897,0.06288195,0.08532061,-0.02185121,-0.06982565,0.0626607,-0.03446252,0.02263343,-0.06701456,-0.03393299,-0.03337219,0.03497718,-0.02252638,0.00528663,-0.01001803,-0.06508123,0.07718572,-0.02044976,0.14041376,0.0171767,0.0056399,0.02230352,0.06286293,0.00400045,-0.03190326,-0.03251712,0.01852072,0.02405895,0.04730007,-0.04977596,-0.03174686,0.02527752,-0.09775498,0.01390405,0.02074425,-0.10778568,-0.01960824,0.02154284,-0.03841694,-0.01053847,0.05104734,0.0701782,0.04552178,-0.02825054,0.01663049,0.05045256,0.06447162,-0.02897539,-0.07267181,-0.01764955,-0.0178802,-0.01598391,-0.005185,-0.01982464,0.02571164,-0.03223528,-0.0030648,0.00412984,-0.0069526,-0.0023968,0.03366715,0.0082792,-0.00408054,0.09233397,0.0289931,0.04715141,-0.02842514,0.0123125,0.04898541,-0.0817055,-0.02334712,-0.01758231,0.02364398,-0.04394233,0.06340442,0.02870573,0.06792399,-0.04329085,0.07850958,0.05526198,0.02587011,0.01119776,-0.01285611,0.01284763,-0.01530377,0.02157255,0.02177225,-0.00283792,-0.26757115,-0.00880626,-0.08470447,0.0474735,-0.04084229,-0.02652245,0.03530252,-0.02463001,-0.00205295,-0.06654863,0.01326382,0.04216775,0.01559999,-0.01570307,0.04281808,-0.00072875,0.04205804,-0.06492371,0.06778039,0.0224423,0.0606057,0.04482389,0.22703435,-0.01747621,0.02171933,0.0445019,-0.00979764,0.05729687,0.01603668,0.04158719,-0.01124925,-0.04457914,0.05238896,-0.0216358,0.00688806,0.03544987,0.00017734,0.00196468,0.00488775,0.02886166,-0.09663676,-0.05524153,-0.00640063,-0.03249745,0.15508947,0.0161095,-0.01334674,-0.04519204,-0.00288752,0.04789266,-0.09277468,-0.0187765,-0.05560947,-0.0177058,0.00684786,0.03113917,-0.02242416,0.00327751,0.01761265,-0.00099429,0.06564777,0.00780117,0.04711677,0.03860065,-0.02096519],"last_embed":{"hash":"w6n745","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"w6n745","at":1753423494691},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>","lines":[74,107],"size":3955,"outlinks":[{"title":"The ultimate lottery software has reverse strategy specialized programs.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":3},{"title":"The reversed lotto software is based on number frequencies and lottery pairings to win the jackpot.","target":"https://saliu.com/images/lie-lottery-pairs-software.gif","line":7},{"title":"The lotto pairs and hot cold numbers act as filters, restrictions to eliminate bad combinations.","target":"https://saliu.com/images/lie-lottery-pairs-filters.gif","line":17},{"title":"The reverse lotto strategy software generates first comprehensive reports to select filters from.","target":"https://saliu.com/images/lie-lottery-pairs-report.gif","line":21},{"title":"There are special ranges of lottery draws for analyses and report generating.","target":"https://saliu.com/images/lie-lottery-pairs-draws.gif","line":25},{"title":"The pairing and frequency parpalucks can be calculated by math.","target":"https://saliu.com/images/parpaluck-report.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08704001,-0.03322689,-0.03698734,-0.00886821,-0.03604137,0.07354166,0.01097545,-0.00541034,0.02888285,-0.02158103,-0.04292854,-0.01033934,0.11732011,-0.03191005,0.01986215,-0.01444888,-0.01937238,0.02008526,-0.04582776,-0.02011554,0.08286781,-0.05428537,-0.06105271,-0.07024854,0.0411128,0.01125551,-0.04127138,-0.08106057,-0.05824609,-0.25950173,0.07029837,0.043779,0.02097651,-0.04503036,-0.0662625,-0.02452184,-0.05804554,0.04512736,-0.06578091,0.01604383,0.00755418,-0.01156554,0.0651018,-0.03502067,0.01094059,0.02147202,-0.04024706,0.01345172,0.04643194,0.00578651,-0.00084532,0.02348535,-0.01871178,0.06525862,0.06406287,0.0856254,0.05843763,0.09303308,0.01825032,0.07453758,0.02753009,0.06145117,-0.17150705,0.00529824,-0.0131305,0.02135078,0.01068649,-0.03652637,0.01642481,0.05153547,0.00258692,0.04739672,-0.02617509,0.01040123,0.04723856,-0.01976014,-0.06181367,-0.02597792,-0.00968396,0.01414777,-0.0463547,-0.00644307,-0.02391723,-0.02577349,0.04679712,-0.00222059,0.05165139,0.02655703,0.06833538,-0.07556498,-0.00129464,0.0482346,0.0363047,-0.00208698,0.04001099,0.0535909,0.01913348,0.02505616,0.01704498,0.10482896,0.00423425,-0.00685688,-0.02951245,0.00150251,0.05318471,-0.09289907,-0.03426979,-0.02328157,0.01706982,0.03891984,0.03321292,-0.0096358,0.07807702,-0.02798796,-0.01331628,0.056401,0.01452621,0.06199952,0.00985033,0.00794565,-0.05874017,0.00802664,0.01935007,-0.01625629,0.0095864,0.0081657,0.01224075,0.08127452,0.01727352,0.05901936,0.01128847,-0.02669123,-0.11391656,-0.03545394,-0.04424339,-0.00716449,-0.01315629,-0.01434414,0.00111644,0.04074405,-0.0287012,-0.04980784,0.07778838,-0.06736283,0.03866846,0.08956051,0.0092625,0.00331731,-0.00989553,0.01665525,0.00603346,-0.00691955,-0.04741931,-0.08593698,0.01056049,-0.00262144,0.04942359,0.06137123,-0.01903288,0.03322316,-0.00263976,0.00223587,-0.03102181,0.10705875,-0.00523187,-0.10777391,-0.00163598,0.05349196,-0.08484821,-0.05448841,-0.06663267,0.00633183,-0.01716122,-0.02254135,0.07676565,-0.01852693,-0.09920093,-0.0748233,0.02959422,-0.00934158,-0.00657368,-0.02215546,-0.03220083,-0.01357552,-0.03216553,-0.06365274,0.00966458,-0.02842015,0.02585479,0.03728424,-0.07173647,-0.02724108,-0.05937881,0.02390589,0.0100512,-0.05394289,-0.02094815,-0.02474464,0.03575811,-0.00442877,-0.01426793,-0.01094382,0.01495426,0.01539215,-0.00984346,0.05395189,-0.0164464,-0.0546127,0.07023251,0.03089758,-0.01454318,0.01420797,0.03042208,0.06396947,-0.05884504,-0.00852999,-0.03187049,0.01540295,-0.01692455,0.04236834,-0.00309622,-0.00008509,-0.01885987,-0.23075651,-0.00028489,-0.02310849,0.01743758,0.01905286,-0.04913361,-0.00807808,-0.02933078,0.03597777,0.08434927,0.08237614,-0.01678346,-0.07304097,0.06050153,-0.03193654,0.02155455,-0.05729083,-0.03277323,-0.03468894,0.03196625,-0.01453852,-0.00467891,-0.0038433,-0.0660171,0.08296998,-0.01958713,0.14567041,0.012693,-0.00319034,0.0208803,0.05372554,0.0156624,-0.02730659,-0.03033471,0.02538907,0.02366629,0.05386829,-0.05003197,-0.03011218,0.01173826,-0.09748676,0.01252454,0.01585757,-0.11686594,-0.01324823,0.0278985,-0.04187074,0.00394373,0.04392834,0.07028709,0.04688431,-0.02818437,0.02264427,0.03331011,0.06282646,-0.03571654,-0.0719828,-0.01331123,-0.02206726,-0.01885984,-0.00944092,-0.02201549,0.02103457,-0.0332418,-0.00413044,0.01822132,-0.00271688,0.00460333,0.0303768,0.0208044,-0.0182402,0.07820319,0.01478075,0.04979721,-0.0310493,0.01031652,0.04284345,-0.08944091,-0.00453534,-0.01885447,0.04366367,-0.03112742,0.05540344,0.02992392,0.05964985,-0.0417849,0.07887202,0.07094803,0.02698993,0.00666205,-0.02410964,0.01648154,-0.00311785,0.02273263,0.02036853,-0.01284061,-0.26727352,-0.00389372,-0.07448706,0.05221466,-0.05017073,-0.03248381,0.03446482,-0.02487788,-0.00599692,-0.06780046,0.00690587,0.05088793,0.02112637,-0.00788559,0.03471366,0.00476541,0.03437445,-0.0694403,0.06391469,0.02271609,0.07193606,0.03963166,0.23306717,-0.02813219,0.021355,0.04893405,-0.00448827,0.06027371,0.01127801,0.03780137,-0.01085029,-0.04519787,0.06734175,-0.00683763,0.00843808,0.03702606,-0.013599,-0.005868,0.00764992,0.03706386,-0.08675541,-0.05512762,-0.00876979,-0.0282922,0.15518588,0.02010543,-0.02145549,-0.0431766,-0.00852215,0.04480347,-0.09104547,-0.01840584,-0.04307789,-0.01669986,0.00318428,0.03133549,-0.01432581,0.01316211,0.01967375,-0.00516904,0.05919142,0.00436313,0.04976048,0.03291322,-0.01608839],"last_embed":{"hash":"pozssj","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"pozssj","at":1753423494835},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{3}","lines":[80,91],"size":2087,"outlinks":[{"title":"The reversed lotto software is based on number frequencies and lottery pairings to win the jackpot.","target":"https://saliu.com/images/lie-lottery-pairs-software.gif","line":1},{"title":"The lotto pairs and hot cold numbers act as filters, restrictions to eliminate bad combinations.","target":"https://saliu.com/images/lie-lottery-pairs-filters.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08924843,-0.04494534,-0.03246963,-0.00159529,-0.04011577,0.09086532,0.01162485,0.00103356,0.00925225,-0.02351734,-0.03960329,-0.02726971,0.0939045,-0.02369275,-0.00220757,-0.02596449,-0.01481384,0.02627907,-0.01791454,-0.02718505,0.12439895,-0.06232553,-0.04355562,-0.08974925,0.05715849,0.01973524,-0.02807184,-0.07599172,-0.06259,-0.24976394,0.05187792,0.0295829,0.00042002,-0.02548267,-0.07599548,-0.01555578,-0.04938971,0.04406547,-0.03787074,0.03602314,-0.00222025,-0.02377545,0.06180901,-0.03225354,-0.00076553,0.01295025,-0.06397353,0.0053666,0.0267243,-0.00154465,-0.02254895,0.01947421,-0.024784,0.04686318,0.07696097,0.0764915,0.07751696,0.04050118,0.03220669,0.07714237,0.03011663,0.05017287,-0.20193128,-0.01417853,-0.00308423,0.02745648,-0.01016255,0.00653498,0.03119631,0.05114461,0.00796787,0.01406558,-0.02500721,0.01816276,0.06260762,-0.02458828,-0.07705773,-0.01204793,-0.03030177,0.01965396,-0.03627368,0.01687887,-0.00162462,0.00744404,0.04528465,0.01942986,0.05204881,0.01640192,0.05320682,-0.07806345,-0.00349236,0.03531325,0.03457322,-0.01026348,0.02467681,0.04964117,0.03151248,-0.00002138,0.05171112,0.11499061,-0.00853973,0.01178712,-0.00794112,0.0415064,0.04521069,-0.0980927,-0.04290221,-0.04180984,0.03547259,0.02115,0.03672731,-0.00435398,0.04899605,-0.05733604,-0.00793012,0.03199024,0.00106636,0.06808155,0.01385412,0.0023012,-0.04025821,0.01595805,-0.00082413,-0.01685143,0.02298598,0.00018835,-0.00853549,0.07965475,0.05634272,0.05386628,0.02042776,-0.00205987,-0.1201099,-0.0358436,-0.05407258,-0.01952725,0.00029087,-0.00432754,0.02922154,0.0451772,-0.01352025,-0.04046969,0.05460972,-0.04972356,0.01317037,0.10918465,-0.0196796,0.01917362,0.00714517,0.05459258,-0.00311594,-0.00596264,-0.02895362,-0.08910839,0.01584726,0.00307422,0.03936356,0.05522076,0.00668617,0.03416918,0.00460657,-0.01155473,-0.0313342,0.11762243,-0.00523218,-0.11267167,0.00262298,0.0668174,-0.07108554,-0.05075792,-0.01977913,0.00649839,-0.00006923,-0.04105696,0.06732236,-0.01486312,-0.07883675,-0.10017314,0.02588954,0.00614313,-0.0240282,-0.01571411,-0.04590543,0.0064665,-0.03200125,-0.04474613,-0.00498294,-0.05440971,0.04937947,0.05185917,-0.04997054,-0.04928044,-0.05288382,0.01333714,0.00754972,-0.06013672,-0.02028942,-0.0016814,0.00859654,0.00297902,-0.00800943,-0.01030282,0.0052737,-0.00057017,-0.00579274,0.06133683,-0.01349489,-0.0309539,0.04207593,0.02853421,-0.00182452,0.03201414,0.01093613,0.03952876,-0.07850555,-0.01117552,-0.02586422,-0.0047435,-0.04417621,0.05384091,-0.00374457,0.00670407,-0.04772179,-0.22251169,-0.01309813,-0.02092334,-0.00156633,0.04868149,-0.0534817,0.00053789,-0.02720899,0.02579658,0.06709286,0.08510601,-0.00017124,-0.08408447,0.05190546,-0.02830215,0.02136504,-0.06315549,-0.03196639,-0.02802859,0.04616683,-0.03399981,0.01962178,-0.01320379,-0.02515966,0.08238972,-0.01980178,0.13012719,0.01940748,-0.00555571,0.01582401,0.04340385,0.00932367,-0.02531881,-0.0267553,0.02819388,0.02022417,0.02180237,-0.05214078,-0.02961923,0.01051694,-0.08658154,0.00162122,0.03134979,-0.07848039,-0.0204997,0.01569981,-0.02992926,-0.03488987,0.02750665,0.07522199,0.02792738,-0.05325321,0.01928796,0.03334256,0.02868362,-0.02214892,-0.06317193,-0.03274851,-0.0196052,-0.02551459,0.0058829,0.00054329,0.03453702,-0.03649607,0.00051303,0.00977964,-0.01212887,-0.00062397,0.02503563,0.03087249,0.00604228,0.10774415,0.03985195,0.04882482,-0.01375054,0.01809553,0.0262899,-0.08802143,-0.02529544,-0.02733825,0.0343434,-0.06354017,0.05438437,0.01436548,0.05291677,-0.01800437,0.09141274,0.04560865,0.03838145,0.00545626,-0.01738229,-0.00759889,0.00860383,0.02889024,0.04640793,0.00316566,-0.27741188,0.01633884,-0.08833707,0.03455656,0.00145847,-0.02380314,0.05156209,-0.02024567,-0.00169251,-0.09622805,0.03647783,0.03677368,0.01759068,-0.02003057,0.02864654,0.01009688,0.05440553,-0.06198852,0.07885502,-0.00789147,0.04954458,0.01902393,0.24285503,-0.01830913,0.02485758,0.00529155,-0.00668684,0.0555997,-0.00745425,0.04168978,-0.00284209,-0.05959943,0.05777699,-0.01252499,-0.00556858,0.07962707,-0.01488252,0.01848082,0.01213048,0.02848132,-0.06948148,-0.05866244,-0.0045611,-0.01892673,0.16273896,-0.01357131,-0.0163028,-0.02705309,0.01402528,0.04721633,-0.10522496,-0.02364576,-0.0505804,-0.0012277,0.01182905,0.04159178,-0.02089596,0.00577323,0.0361521,-0.00238205,0.06285678,-0.00360259,0.02047311,0.02794439,-0.00358169],"last_embed":{"hash":"13j2men","tokens":363}}},"text":null,"length":0,"last_read":{"hash":"13j2men","at":1753423494992},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>#{5}","lines":[94,107],"size":1328,"outlinks":[{"title":"The reverse lotto strategy software generates first comprehensive reports to select filters from.","target":"https://saliu.com/images/lie-lottery-pairs-report.gif","line":1},{"title":"There are special ranges of lottery draws for analyses and report generating.","target":"https://saliu.com/images/lie-lottery-pairs-draws.gif","line":5},{"title":"The pairing and frequency parpalucks can be calculated by math.","target":"https://saliu.com/images/parpaluck-report.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0843595,-0.03151509,-0.0127352,-0.01300554,-0.01232616,0.10039183,0.03142302,-0.00702792,0.02081346,-0.00695042,-0.04534717,-0.02482842,0.10436277,0.0061186,0.01367293,-0.00946712,-0.01934846,0.00539397,-0.07326681,-0.04621108,0.07628048,-0.06188999,-0.03418953,-0.07226711,0.04478314,-0.01938626,-0.03892065,-0.05625498,-0.06104651,-0.24822102,0.03975308,0.03275739,0.02081646,-0.03801985,-0.07429083,-0.03984571,-0.06931401,0.06761856,-0.04216805,0.0357596,0.01539859,-0.00377252,0.03333514,-0.00799414,-0.00880937,0.02059669,-0.08610233,0.02893608,0.01798106,0.00108297,-0.01320506,0.02679236,-0.03297599,0.03976929,0.06134091,0.05455928,0.08432822,0.06782984,0.01464809,0.08627183,0.007759,0.03903056,-0.21057186,-0.00165053,-0.0150535,0.02021297,-0.004952,-0.01053348,-0.0029212,0.07728205,-0.0206129,0.04307839,-0.02384469,0.02617859,0.04036163,-0.01456761,-0.05304166,-0.02900794,-0.04014843,0.02471287,-0.04189064,0.00841935,-0.03786576,0.00262351,0.05045066,0.00868182,0.02728951,0.04301617,0.0395696,-0.04030049,0.0110536,0.04768886,0.03801575,0.00912548,0.00613057,0.03101972,0.01432869,0.03446622,0.00999996,0.12145303,0.01785417,0.01186268,-0.01382155,0.03173669,0.03320621,-0.07527227,-0.05931256,-0.05297203,0.02992823,0.019601,0.02530984,-0.02756471,0.06792709,-0.09585363,-0.01813593,0.0812516,0.01674947,0.06077214,-0.01087551,-0.01484673,-0.04731615,-0.00661414,0.01638018,0.01058293,0.02822861,0.02598068,0.0150428,0.09159179,0.06322207,0.06703754,0.00486825,-0.00335229,-0.12521131,-0.03768788,-0.05913835,-0.02623858,-0.01407285,-0.01132564,0.01122998,0.01578687,-0.00540951,-0.04957522,0.04109441,-0.06798833,0.03722573,0.120944,-0.01977209,0.01481639,0.01057537,0.02046911,0.00472541,0.01447182,0.00028398,-0.08443991,0.023273,0.01618022,0.01759395,0.0533252,-0.01801138,0.04769611,-0.03837084,-0.00300176,-0.0216902,0.15062292,-0.00977571,-0.09021097,-0.02246978,0.0364524,-0.06397801,-0.03063477,-0.0111873,-0.00589168,0.02983183,-0.02503149,0.0934379,-0.01150354,-0.04503754,-0.06469744,0.00605579,0.02531514,-0.02244762,-0.05428853,-0.0509848,0.02158979,-0.05722288,-0.05369599,0.0032183,-0.03031358,0.03385751,0.05379496,-0.10157784,-0.05415961,-0.03067278,-0.0018139,0.00678558,-0.04695776,-0.01887745,-0.01320221,-0.00025475,-0.01070369,0.01730451,0.00492302,0.0140973,0.01394228,-0.03189616,0.04358753,0.00874196,-0.04688984,0.07596891,0.03253546,0.00501281,0.0098349,0.04078329,0.03385219,-0.0257438,-0.03154255,-0.01889337,-0.00517372,-0.00249856,0.01706727,-0.01832125,-0.01621266,-0.0364525,-0.21963535,0.00936511,-0.01580675,-0.01080683,-0.00357433,-0.05012621,0.00288619,-0.02311112,0.02117692,0.06663521,0.07988366,0.01071997,-0.06873506,0.04300236,-0.00325217,-0.00706932,-0.04862146,-0.03262226,-0.01519459,0.02363114,-0.00382695,-0.00710856,-0.00780218,-0.04825058,0.06133727,-0.0215269,0.1657538,0.02385922,0.00592041,0.02448162,0.06537689,0.02460269,0.00955027,-0.01276332,0.03020891,0.02480603,0.0324534,-0.00670392,-0.01192046,-0.01769957,-0.05915212,0.01286385,0.00973726,-0.09361818,-0.03034185,-0.00460165,-0.03647266,0.01558061,0.02148218,0.05859251,0.02992485,-0.06689533,0.05211184,0.0130324,0.07037679,-0.03252536,-0.08400501,-0.03312548,-0.02703931,0.00085286,-0.00630976,-0.00891407,0.04148239,-0.02860141,-0.02665585,0.00432343,-0.01466223,0.01434039,0.00596747,0.02610477,0.00213109,0.065369,0.03607224,0.05129183,-0.01711286,0.00371176,-0.02344734,-0.05876188,-0.0311304,-0.05653245,0.03928684,-0.04169016,0.04329532,0.01171098,0.06886677,-0.02404514,0.04655842,0.03505835,0.05209291,0.0026714,-0.04094823,0.03508735,-0.00490019,0.03990772,0.05221936,-0.01481931,-0.29694667,0.00033963,-0.08802868,0.05428482,0.01524482,-0.02335031,0.02525411,0.01222051,-0.00384936,-0.05431034,0.01269318,0.04201801,0.02585424,-0.01917166,0.01545211,-0.01188291,0.02235358,-0.07529647,0.0794423,0.01738662,0.06987987,0.02945934,0.24339558,-0.00936154,0.01878577,0.02297279,-0.01065791,0.03148272,0.00958169,0.01605068,0.0105175,-0.04536522,0.06875629,-0.01681521,0.00807048,0.05250939,0.00079631,0.05065518,0.00636344,0.00163185,-0.11463565,-0.064739,0.03080309,-0.02243347,0.15746433,-0.00442447,-0.0197113,-0.03432685,-0.00838177,0.0431959,-0.07973418,0.00853405,-0.02658346,-0.0189612,-0.01203504,0.03932551,-0.00492131,-0.00893322,0.03498972,0.00326849,0.05780489,-0.00510533,0.05567415,0.03136825,-0.03758856],"last_embed":{"hash":"5et4al","tokens":471}}},"text":null,"length":0,"last_read":{"hash":"5et4al","at":1753423495109},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>","lines":[108,162],"size":5636,"outlinks":[{"title":"Reverse lie elimination strategy generates lottery combinations to play right away.","target":"https://saliu.com/images/lie-lottery-pairs-combinations.gif","line":54}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07015572,-0.02219776,-0.01931353,-0.01056872,-0.00396065,0.09979672,0.02965935,-0.00971407,0.01540747,-0.00520757,-0.05704595,-0.01311283,0.11187841,0.00202955,0.00871195,-0.00991407,-0.02099127,0.01270651,-0.0562481,-0.0483509,0.08805672,-0.06093617,-0.03854371,-0.06967264,0.04324266,-0.01947154,-0.04018732,-0.05057469,-0.05303876,-0.25367352,0.04384306,0.01379545,0.0083927,-0.05227207,-0.07544214,-0.04971097,-0.06787449,0.06482445,-0.05201441,0.04190321,0.01102179,-0.00675416,0.04124391,-0.01062341,-0.00390464,0.02196573,-0.07937175,0.02402393,0.0249254,-0.00984566,-0.00193723,0.04001532,-0.03854905,0.03708258,0.04736023,0.05667066,0.09186531,0.08388177,0.02176135,0.0907827,0.01424933,0.03925664,-0.19371432,-0.00422958,-0.02556907,0.01709153,-0.00417102,-0.01435101,-0.00442096,0.08205828,-0.01907461,0.03971662,-0.02049306,0.03133647,0.04809518,-0.02750187,-0.06677938,-0.02773894,-0.04099735,0.03198966,-0.03986465,0.01059442,-0.03787389,0.01224827,0.04691144,0.00760376,0.02930075,0.02997045,0.04250209,-0.04510402,0.01204389,0.04066706,0.0310892,0.02022173,0.02295816,0.03463476,0.01516499,0.03367068,0.01440487,0.12222281,0.02242831,0.00293719,-0.01005228,0.01710004,0.0306011,-0.0730734,-0.05376836,-0.04745946,0.02969711,0.02257783,0.02157938,-0.03082993,0.05298165,-0.08841998,-0.00783796,0.0891028,0.00195983,0.06124961,-0.01933842,-0.00973949,-0.04434189,-0.01410533,0.01061461,0.01004132,0.04452983,0.01671156,0.01469879,0.08627621,0.06763459,0.06287356,-0.00651612,0.00204066,-0.13510722,-0.0423641,-0.06001893,-0.03551323,-0.0115319,-0.02515687,0.00861807,0.02137953,-0.00477361,-0.03025918,0.03077387,-0.0671536,0.04622267,0.12155957,-0.02147273,0.00855114,0.00937354,0.02172949,0.00879943,0.01958208,-0.00569496,-0.08894106,0.02068601,0.03003992,0.01306635,0.04644183,-0.01359774,0.05225452,-0.04572365,0.00178548,-0.02405127,0.16250409,-0.02361628,-0.07947513,-0.02348012,0.03874857,-0.05794372,-0.03771003,-0.01298012,-0.01107038,0.02381303,-0.03493359,0.08794651,-0.01842811,-0.04800498,-0.0693634,-0.00732093,0.01546082,-0.01802516,-0.05532298,-0.04647409,0.01598643,-0.05062968,-0.05223906,-0.00030134,-0.03794318,0.03106616,0.06262048,-0.10048841,-0.03712737,-0.03867728,-0.0134647,0.00874793,-0.04329101,-0.0076712,-0.02407081,-0.00162623,-0.01852529,0.01822135,0.00578171,0.02011031,0.03116669,-0.02783821,0.04450827,0.00739236,-0.04607928,0.07478647,0.0327369,-0.00665921,0.00674452,0.03828858,0.03797242,-0.03860356,-0.03414978,-0.0213086,-0.00864253,-0.02031587,0.00594727,-0.01151808,0.00158582,-0.03171227,-0.21536484,0.00805206,-0.0164308,-0.00358618,0.00675225,-0.05044843,0.00022551,-0.0192165,0.01588353,0.0581126,0.0895533,0.01421569,-0.07105087,0.05142886,-0.00805834,-0.00409485,-0.05036909,-0.04068504,-0.01615858,0.02248201,-0.01062529,-0.01046029,-0.001831,-0.0483521,0.0724131,-0.02093529,0.15376151,0.00282464,0.02586202,0.04028548,0.06243876,0.01336901,0.00586179,-0.02281832,0.03544372,0.02938632,0.02065757,-0.00666025,-0.01560131,-0.00806968,-0.04375366,0.00863498,0.01319464,-0.09029357,-0.02449379,-0.00391913,-0.03578241,0.01480321,0.0292469,0.05934754,0.02244245,-0.06780509,0.0458158,0.01680412,0.07200024,-0.03170317,-0.07067502,-0.02350332,-0.02626799,-0.01479666,-0.00072863,0.00053275,0.0319799,-0.03088129,-0.0074181,0.00636302,-0.01927346,0.00946686,0.0233065,0.03632816,0.00183329,0.08294247,0.01810326,0.04603954,-0.0260451,0.00852891,-0.02625796,-0.07299156,-0.03189616,-0.06433474,0.0440915,-0.03133481,0.05034514,0.01207774,0.07616825,-0.02416819,0.04252291,0.03141519,0.06023139,-0.001197,-0.04768625,0.03099968,-0.01112504,0.03964325,0.0395928,-0.01200283,-0.28014874,-0.00150367,-0.08600011,0.06018054,0.00638726,-0.03709693,0.01051494,0.0151602,-0.00122474,-0.05184316,0.00094598,0.02885558,0.02851676,-0.01161093,0.01292836,-0.0025619,0.04307666,-0.07435481,0.080203,0.01216851,0.07524924,0.01881822,0.25384414,-0.01103168,0.02144026,0.01976237,-0.01409501,0.03245395,0.00769533,0.0268111,0.02266804,-0.04648199,0.05691687,-0.00683048,0.00828265,0.05404671,0.00962623,0.04092645,0.00250376,0.00790854,-0.10742001,-0.06565928,0.03171508,-0.03598392,0.14917739,-0.01348085,-0.02243848,-0.03748689,-0.02619521,0.04759741,-0.08333936,0.01475822,-0.03040904,-0.02304538,-0.00284767,0.04633369,0.00259231,-0.00415,0.04028382,0.00325762,0.05779137,-0.00100789,0.05812117,0.03185255,-0.04301923],"last_embed":{"hash":"b6wjtg","tokens":225}}},"text":null,"length":0,"last_read":{"hash":"b6wjtg","at":1753423495268},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>#{1}","lines":[110,114],"size":602,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11977673,-0.04444771,-0.01102319,0.00947501,-0.02915865,0.104886,0.04798697,0.00620906,0.03090451,-0.01234694,-0.02501356,-0.04533974,0.08004588,0.00173701,0.01498432,-0.00358472,-0.02930765,0.00487346,-0.06606263,-0.01448177,0.0837262,-0.06615253,-0.03659766,-0.06888158,0.06240633,-0.00192599,-0.03398496,-0.07363756,-0.07956509,-0.21809578,0.04979693,0.03086868,0.0220989,-0.01565482,-0.0618333,-0.01566231,-0.07207306,0.0647773,-0.02644254,0.02802999,0.01810218,0.00229829,0.03198952,-0.02887656,-0.01819188,0.00676035,-0.08328676,0.01414675,0.04206811,0.00647293,-0.01002397,0.02596702,-0.02103376,0.0417161,0.08019888,0.05983795,0.07706829,0.04584201,0.00254105,0.06059632,0.0003391,0.02766164,-0.21622582,0.01089088,0.00102469,0.0137971,0.00983337,-0.00262846,-0.00026378,0.07550237,-0.0010209,0.03758111,-0.00273176,0.02792589,0.03856092,-0.00233578,-0.05037454,-0.03523948,-0.0371658,0.00823219,-0.05227755,-0.01222834,-0.01570627,-0.03121509,0.04288287,0.02009053,0.01820158,0.04009767,0.04506782,-0.04698081,0.00648283,0.05925861,0.04893084,-0.00925626,-0.00880898,0.0157888,0.00556088,0.01901661,0.01643147,0.127681,0.00939778,0.02517268,-0.0276949,0.0609921,0.03734216,-0.06734008,-0.06065234,-0.07095046,0.0375618,0.01507308,0.02089131,-0.01227666,0.07498673,-0.08347352,-0.0186651,0.06585575,0.036238,0.06687619,-0.00375024,-0.0220103,-0.04784898,0.0140388,0.02546682,0.01342532,-0.00763854,0.00336968,0.0302619,0.09975436,0.05708998,0.06000861,0.02777125,-0.01716212,-0.11344182,-0.04018807,-0.06340542,-0.01327327,-0.01002122,-0.01527994,0.03628215,0.01792826,-0.00117641,-0.08167815,0.04789292,-0.07587727,0.02392331,0.09937254,-0.01084275,0.01273911,0.02400934,0.02934328,-0.0046486,0.00359889,-0.00330219,-0.08047239,0.00923627,0.00232423,0.02637435,0.0601114,-0.01736569,0.05001812,-0.02104581,-0.01257388,-0.02638839,0.13197446,0.00751696,-0.10647289,-0.02546441,0.01287027,-0.0722931,-0.03006865,-0.02590675,-0.01272482,0.01341919,-0.01465048,0.08840472,0.00326996,-0.05175286,-0.07260685,-0.00303102,0.03034797,-0.03142357,-0.04307385,-0.05319441,0.01493851,-0.05669132,-0.04249731,0.00880545,-0.02529475,0.03366166,0.0374409,-0.08438201,-0.06764563,-0.05149946,0.01324054,0.01371107,-0.05022563,-0.0377406,-0.0140564,-0.00242843,0.01234409,-0.00877412,-0.00319969,0.00103538,-0.00148683,-0.0208565,0.04399,0.01876772,-0.05450637,0.07602484,0.02144057,0.01630128,0.0169245,0.03176901,0.03267932,-0.00992852,-0.03194922,-0.01089293,0.00626169,0.025172,0.03592024,-0.02130747,-0.03255608,-0.02628524,-0.21289282,0.00801955,-0.01718653,-0.0311549,-0.00775793,-0.04455329,0.0048716,-0.00947638,0.0453648,0.06520232,0.06926877,-0.00668137,-0.05827568,0.04864116,-0.01458222,0.00423837,-0.05328594,-0.01162022,-0.018149,0.02123896,-0.00488475,0.00551038,-0.00806468,-0.04466936,0.06129647,-0.00800865,0.17834705,0.04719427,-0.01023264,0.01009589,0.06083661,0.01849349,-0.00616052,-0.02917046,0.02442266,0.01621658,0.02672307,-0.03713007,-0.0294321,-0.03337268,-0.09781908,0.02093601,0.00211763,-0.09582549,-0.06023714,0.00215662,-0.05474341,0.01152195,0.02574674,0.06266776,0.03920977,-0.05293246,0.05047831,0.01088972,0.06327439,-0.01424344,-0.08689502,-0.04684168,-0.02307664,0.01013745,-0.00486707,-0.00420788,0.04261412,-0.0216541,-0.04105555,-0.00382848,-0.00345633,0.00775932,-0.02207825,0.01327292,-0.00815269,0.05700146,0.05493938,0.03500564,-0.00487907,0.00125481,-0.02965075,-0.05167186,-0.02342588,-0.04183714,0.03733512,-0.06620715,0.04429569,0.02905798,0.06171309,-0.00508518,0.05877147,0.02948187,0.02529334,0.01223634,-0.01289977,0.02910849,0.00726569,0.0607309,0.05452885,-0.02677964,-0.29739651,0.01044001,-0.07449779,0.05228381,0.02942108,-0.00529271,0.02846756,0.01460772,-0.00562929,-0.06120738,0.04478415,0.05487714,0.00692331,-0.01317967,0.02561208,-0.02234147,0.00723622,-0.08334464,0.0633478,0.01425134,0.04855708,0.03443264,0.22535907,0.00992663,0.02809396,0.02816005,-0.00078977,0.05331546,-0.00500609,0.00992102,0.00183267,-0.04845776,0.06239566,-0.02279812,0.01004763,0.04765592,0.00515987,0.0635979,0.0237412,0.00343655,-0.11680727,-0.06634437,0.03438388,-0.0045374,0.17020203,0.02424489,-0.02472628,-0.02686143,0.03068024,0.04425191,-0.075527,-0.00700801,-0.04537896,0.00629498,-0.02864013,0.02800415,-0.03588979,-0.00166567,0.03821861,0.01292701,0.06098963,0.00596229,0.03968122,0.01923003,-0.02085754],"last_embed":{"hash":"1m8on5m","tokens":506}}},"text":null,"length":0,"last_read":{"hash":"1m8on5m","at":1753423495336},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>#{2}","lines":[115,162],"size":4912,"outlinks":[{"title":"Reverse lie elimination strategy generates lottery combinations to play right away.","target":"https://saliu.com/images/lie-lottery-pairs-combinations.gif","line":47}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>5. Lottery Strategies for <i>LIE Elimination</i> Based on Pairs, Number Frequency</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0960869,-0.03892983,-0.02567533,-0.00879112,-0.0388511,0.08134313,0.03849611,0.01090252,0.05214695,-0.02340547,-0.02836568,-0.01678526,0.11488099,-0.00321859,0.00892085,-0.02829242,-0.02588382,0.04239581,-0.05555572,-0.03990334,0.07627376,-0.05944945,-0.06478813,-0.06842846,0.06506236,-0.00157667,-0.02587235,-0.08415221,-0.05009943,-0.25957382,0.03969677,0.04151117,0.02013523,-0.05368105,-0.06673636,-0.03555397,-0.0588333,0.05145327,-0.05697633,0.03177073,0.01136935,-0.02354392,0.04874013,-0.02948811,-0.01858285,0.00114442,-0.05759864,0.01215385,0.03280179,0.00247996,-0.03236805,0.03571263,-0.02457498,0.04802319,0.04794573,0.09105428,0.09048552,0.09508867,0.00861945,0.08923351,0.02315929,0.03219564,-0.17752576,0.01390885,-0.01717944,0.00628272,0.00800323,-0.02656653,0.02965263,0.09557138,0.00665273,0.03211517,-0.03474517,0.01510995,0.03120865,-0.02924166,-0.05852163,-0.03405865,-0.00376672,0.03908818,-0.05981002,0.0027066,-0.02473079,0.00472184,0.03363027,0.01022601,0.02860565,0.03016524,0.04265186,-0.06015063,0.0150817,0.05879459,0.01054107,0.0077503,0.03278098,0.03658137,0.01797204,0.02151179,0.02147712,0.12140736,-0.01342502,0.01153013,-0.0238753,0.0145981,0.05302457,-0.08958369,-0.03253946,-0.04424033,0.02495619,0.01392653,0.01528115,-0.01992263,0.07437275,-0.04832909,-0.00393195,0.08713149,0.02414936,0.04826011,0.00888923,0.00624925,-0.05472438,-0.0063417,0.02040119,-0.00042679,0.0104883,0.01444944,0.01536018,0.08924836,0.03154877,0.05877233,0.02893183,-0.02328997,-0.12080038,-0.0614748,-0.04057144,-0.00138911,-0.0137395,-0.02301771,0.01429668,0.05421505,-0.03174823,-0.05376288,0.07193176,-0.06947152,0.00993314,0.11975072,-0.00781685,0.01003729,-0.00939678,0.00797884,-0.00397902,0.00430325,-0.02689774,-0.09165497,0.01598149,0.00544173,0.0542303,0.05305609,-0.02281412,0.0342305,-0.03513049,0.00204855,-0.04085694,0.12927477,-0.00475055,-0.09370641,0.00121868,0.04128914,-0.07315501,-0.05175915,-0.0433977,-0.00277687,0.00073204,-0.02015846,0.08576927,-0.02027151,-0.09168713,-0.07995572,0.0143679,-0.00189304,-0.00614189,-0.02299402,-0.04312092,0.01395853,-0.05979586,-0.05015372,0.03001308,-0.02611259,0.03209956,0.02605975,-0.10535872,-0.02552261,-0.03736924,0.02214825,-0.01287246,-0.04317517,-0.02733068,-0.02787141,0.02136707,-0.01350656,-0.02045579,-0.02465938,0.02314148,0.02126012,-0.01432298,0.06352114,-0.0043217,-0.05589435,0.08417773,0.02075984,-0.01565594,0.02371988,0.01168607,0.05152284,-0.03025093,-0.03087712,-0.02062289,0.00822409,-0.02485305,0.0231956,-0.00555619,0.01867275,-0.03296353,-0.20966303,0.00550454,-0.02386283,0.01914104,0.01700754,-0.04389222,-0.01621246,-0.03560686,0.00936433,0.10784727,0.08489779,-0.01891638,-0.05287608,0.05541589,0.00164687,0.00692029,-0.05656629,-0.01939775,-0.03115909,0.01196211,0.00630368,-0.0092254,0.01132551,-0.06465831,0.05736427,-0.01084302,0.14742906,0.01395471,0.00812279,0.02151503,0.0362475,-0.00928412,-0.00748286,0.00438883,0.01609907,0.02826848,0.03094824,-0.05770833,0.0070182,0.00204181,-0.07139382,0.00677552,-0.01195512,-0.10013761,-0.02420263,0.02175925,-0.04557236,0.02876391,0.04853406,0.06211754,0.05025701,-0.03895608,0.03592466,0.01829622,0.06174455,-0.04525929,-0.07464606,-0.03272457,-0.02693192,0.00306458,-0.02070457,0.00253664,0.0107415,-0.02647856,0.01004437,0.01876624,-0.00118818,0.01406318,0.02199073,0.0377617,-0.01540153,0.07377521,0.01888247,0.03094425,-0.01135073,0.0190106,0.02517083,-0.06500735,-0.02351192,-0.03619309,0.05675415,-0.06551606,0.05680656,0.02946729,0.05967952,-0.03658949,0.06021591,0.05053158,0.03535672,0.01227942,-0.03582014,0.01691601,-0.01515134,0.04615024,0.02907709,-0.01673127,-0.26718447,0.01031537,-0.08803138,0.03528523,-0.01444875,-0.0376306,0.03994375,-0.00288922,-0.00364717,-0.07557977,0.01206127,0.04588808,0.03600366,-0.01876942,0.01039414,0.00967858,0.03184655,-0.08397459,0.06246489,0.02971915,0.07715773,0.02767814,0.2307384,-0.01857991,0.02763072,0.04037951,-0.01454233,0.02855698,-0.0214587,0.02193845,-0.00463471,-0.04098764,0.05645223,-0.01829618,0.00710867,0.04408803,0.01554195,0.03230438,0.01521414,0.01483445,-0.09315544,-0.04018303,0.02389008,-0.04235043,0.16459259,-0.00684768,-0.0243921,-0.04715353,-0.00672759,0.03998927,-0.09105884,0.00612862,-0.02969148,-0.02053708,0.01917681,0.0430658,-0.01478744,0.00024982,0.02603821,-0.00122154,0.04763602,0.01074465,0.03863496,0.03964613,-0.01739643],"last_embed":{"hash":"18jynur","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"18jynur","at":1753423495505},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>5. Lottery Strategies for <i>LIE Elimination</i> Based on Pairs, Number Frequency</u>","lines":[163,212],"size":4207,"outlinks":[{"title":"**Cross-Reference Lottery Strategy Files Created by _Command Prompt Software_ and _MDIEditor Lotto WE_**","target":"https://saliu.com/cross-lines.html","line":45}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>5. Lottery Strategies for <i>LIE Elimination</i> Based on Pairs, Number Frequency</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09663314,-0.0398431,-0.02641458,-0.00780387,-0.0352433,0.08583818,0.03952563,0.00993029,0.05412613,-0.0200984,-0.02977259,-0.01926294,0.11349174,-0.00144845,0.01276712,-0.02934357,-0.02459618,0.04419368,-0.05418615,-0.03978481,0.07085396,-0.0587813,-0.06291846,-0.07009363,0.06544115,-0.00179052,-0.02603463,-0.08386783,-0.04573411,-0.25946856,0.04108946,0.03974794,0.01486023,-0.05019742,-0.06555808,-0.03369346,-0.05901018,0.05182839,-0.05560741,0.03038611,0.01419431,-0.02645746,0.0489664,-0.03138263,-0.01975914,0.00233332,-0.06073157,0.0097722,0.03054241,0.00364155,-0.03316054,0.03478064,-0.02625993,0.04689703,0.05002147,0.08544493,0.09369804,0.09892151,0.00501301,0.09219579,0.02413326,0.02948552,-0.17612545,0.01285534,-0.01889306,0.00822178,0.00761967,-0.0255587,0.02681875,0.09649665,0.00627724,0.03515574,-0.0340471,0.01910741,0.02980702,-0.02687172,-0.05731564,-0.03755574,-0.00184098,0.04110796,-0.06044679,0.00766659,-0.02589822,0.00362432,0.03086274,0.00837027,0.02820316,0.02789199,0.042027,-0.06071575,0.01538824,0.05563548,0.00991093,0.00944113,0.03657768,0.03367368,0.01693801,0.0209677,0.02015185,0.11946483,-0.0110638,0.0156145,-0.02394016,0.01266183,0.05376066,-0.09035801,-0.03688727,-0.04628646,0.02135261,0.01867866,0.01766991,-0.02220459,0.07195561,-0.04841555,-0.00777705,0.08918989,0.03042713,0.04701417,0.00855456,0.00274416,-0.05685309,-0.00457445,0.02260088,-0.00025535,0.00832424,0.01366001,0.0146637,0.09087396,0.03107143,0.05551589,0.02926883,-0.0250275,-0.12048041,-0.06628073,-0.03568852,-0.00550233,-0.01490084,-0.02450247,0.01306188,0.05248529,-0.03294283,-0.05411184,0.0730553,-0.07052141,0.00654938,0.12200427,-0.00799943,0.01012517,-0.0071359,0.00480598,-0.00389116,0.00507824,-0.02693328,-0.0909168,0.01300913,0.00506727,0.05034491,0.05448913,-0.02315071,0.03684387,-0.03894941,0.00034331,-0.04309204,0.1298286,-0.00796021,-0.09300324,0.00151869,0.04006859,-0.06691368,-0.04973885,-0.04153554,-0.00182554,-0.00113304,-0.01792189,0.08558064,-0.02159474,-0.08930533,-0.0829358,0.01292552,0.00195616,-0.00500851,-0.02183075,-0.04413749,0.01537591,-0.05382745,-0.04942815,0.03026803,-0.02714244,0.03269958,0.02194738,-0.1049697,-0.02589636,-0.03828302,0.01620701,-0.01143353,-0.04438807,-0.02458198,-0.02482202,0.02224839,-0.00986969,-0.01851827,-0.02476615,0.02251387,0.02171926,-0.01392402,0.06288302,-0.00463895,-0.05402933,0.08702791,0.02084031,-0.01483826,0.0204616,0.00580845,0.04907639,-0.02514557,-0.02680659,-0.01867128,0.00747975,-0.02707518,0.02183607,-0.00848705,0.01909001,-0.03031193,-0.20765869,0.00309536,-0.02086971,0.01711852,0.02090549,-0.04689653,-0.02219209,-0.03511141,0.01210215,0.10827377,0.08571375,-0.01433604,-0.05344615,0.05554674,0.00464625,0.00886619,-0.05506116,-0.01501668,-0.02886841,0.01016952,0.00695881,-0.01080895,0.00756672,-0.07027218,0.05435573,-0.00679033,0.14999251,0.01419044,0.00895932,0.02286008,0.03545226,-0.00933855,-0.00542798,0.00372527,0.02253107,0.03375586,0.02974577,-0.05766845,0.01038897,0.00451933,-0.06934974,0.00340171,-0.01657558,-0.10001,-0.02424575,0.02309725,-0.04728831,0.02816697,0.0471051,0.06319886,0.05292459,-0.03837234,0.03367087,0.01682455,0.05995362,-0.04618265,-0.07458828,-0.03522636,-0.01946804,0.00628682,-0.02297222,0.00347719,0.01022394,-0.0247487,0.01088538,0.01500948,0.0000845,0.0183314,0.0229421,0.03809976,-0.02210165,0.07327221,0.0163217,0.03166123,-0.01088485,0.016384,0.02323458,-0.0630146,-0.02113972,-0.03483699,0.05378859,-0.06296588,0.05649623,0.02952963,0.05473975,-0.03671454,0.05425965,0.05372602,0.03994548,0.0131941,-0.03775644,0.01595705,-0.0169798,0.0463677,0.02964534,-0.01561247,-0.26951644,0.00920759,-0.08605544,0.03339338,-0.01584179,-0.03861333,0.04059668,-0.00252076,-0.00098108,-0.07911695,0.01349782,0.04611862,0.03369439,-0.02263339,0.00831057,0.00991631,0.03180919,-0.08564528,0.06266976,0.02977942,0.07570083,0.02589312,0.23190226,-0.01793531,0.02754788,0.03849452,-0.01389033,0.02885732,-0.02455529,0.01758145,-0.00375969,-0.04046274,0.05836323,-0.01437243,0.00429514,0.04397317,0.01273117,0.03541142,0.01214279,0.01721517,-0.09555176,-0.03980492,0.02157779,-0.03961223,0.16832325,-0.0081909,-0.02264535,-0.04700589,-0.00755396,0.04224346,-0.08754809,0.00878058,-0.03183925,-0.02444103,0.02729977,0.04476354,-0.01455065,0.0008612,0.02898723,-0.00248405,0.0444233,0.01050358,0.03692542,0.03946577,-0.01754302],"last_embed":{"hash":"1a5ttv","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"1a5ttv","at":1753423495659},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#<u>5. Lottery Strategies for <i>LIE Elimination</i> Based on Pairs, Number Frequency</u>#{1}","lines":[165,212],"size":4106,"outlinks":[{"title":"**Cross-Reference Lottery Strategy Files Created by _Command Prompt Software_ and _MDIEditor Lotto WE_**","target":"https://saliu.com/cross-lines.html","line":43}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#[<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1186652,-0.02971601,-0.04454058,-0.00278132,-0.07641785,0.08472589,-0.01062227,0.01400166,0.02795769,-0.01279321,-0.01665174,-0.02344064,0.07559697,-0.04755869,-0.00142692,-0.04754242,-0.00790082,0.04682017,-0.03760472,-0.02403346,0.08844201,-0.04956703,-0.06079485,-0.08006268,0.05932638,0.01874561,-0.0447179,-0.05827608,-0.03185873,-0.21323943,0.04201945,0.03773834,-0.00705963,-0.04165519,-0.07216274,-0.0038819,-0.05058409,0.03555514,-0.04663995,0.03550797,0.00547891,-0.00189448,0.05160299,-0.00758847,0.03246392,-0.01557327,-0.0359972,0.0210695,0.06463181,0.00436253,-0.0348819,0.03848657,-0.01146326,0.03621501,0.08262755,0.07299814,0.07705563,0.09287666,0.02589882,0.06133951,0.02293904,0.06202422,-0.20938113,0.04885687,0.023556,0.02958716,0.0177328,0.00154593,0.02137729,0.06214676,-0.0074429,0.01153335,0.00535794,0.04288806,0.05964136,-0.00322406,-0.0816099,-0.03844256,-0.03195108,-0.00760882,-0.0387052,-0.00056531,0.00601574,0.01082695,0.00923693,0.03126559,0.04503012,0.02835897,0.10702871,-0.07773849,0.00094116,0.04633116,0.06717355,0.00919145,0.02706477,0.00270934,0.0502311,-0.00248104,0.02634312,0.11174852,0.00547941,0.01229737,0.00012422,0.01997279,0.03760887,-0.1058635,-0.03676983,-0.04677885,0.03443281,0.02658273,0.04012752,0.00208485,0.04530965,-0.06325699,-0.03059955,0.03192228,-0.00358079,0.0333788,0.03108409,-0.01256032,-0.0581656,-0.00800014,0.01555351,-0.01024378,0.01285726,-0.01230048,0.00101619,0.0734063,0.023583,0.05207847,0.03414861,0.00378727,-0.12708281,-0.04033443,-0.03822858,-0.01280012,0.01842226,-0.04842405,0.02700271,0.05128161,-0.01461812,-0.07006995,0.05358257,-0.07969551,-0.00949651,0.08481938,0.01746552,-0.0016904,0.01328631,0.02173368,0.00628238,-0.02018561,-0.0127904,-0.06677835,0.02022117,0.0221798,0.08086675,0.05719128,-0.02017928,0.02910921,-0.04031989,-0.0096673,-0.05611765,0.15345322,-0.00061233,-0.12163777,-0.00614047,0.04325498,-0.06021484,-0.07490373,-0.03128251,-0.01344384,-0.02921656,-0.00775323,0.12212086,-0.00349076,-0.07811947,-0.08446591,0.00307329,-0.01809632,-0.00601844,-0.04445743,-0.02257227,0.02028484,-0.05392445,-0.05333699,-0.0165649,-0.03435863,0.01466876,0.0543458,-0.03027522,-0.031387,-0.05574241,-0.0113833,-0.01822594,-0.04094573,-0.04405613,-0.04857365,0.02835563,-0.01529043,-0.00979319,-0.00836642,0.00556561,0.01005046,-0.00508689,0.05579709,0.00345587,-0.05578741,0.05656199,0.0321095,0.00135609,0.02472442,0.02661563,0.03053618,-0.03696034,-0.00267063,0.01285209,0.02069522,-0.01821361,0.06098663,-0.00684846,0.01274301,-0.06229477,-0.17818378,-0.02603114,-0.0384264,-0.00400621,0.00763345,-0.02960012,0.02729857,-0.03610838,0.01520158,0.06707427,0.08024332,-0.0291224,-0.04399947,0.06039995,-0.0353579,-0.00255887,-0.05161657,-0.03243386,-0.04236965,0.02271435,-0.01876543,0.01400626,-0.01322879,-0.064595,0.06701493,-0.02472094,0.12362306,-0.00603562,-0.00352316,0.0390796,0.06890437,0.004291,-0.03015078,-0.03568273,0.0214693,0.03839475,0.01979866,-0.03270958,-0.04887945,0.01418531,-0.10044976,0.01311469,0.02644125,-0.07649285,-0.07353614,0.00076715,-0.01051336,-0.00552106,0.03918093,0.06368022,0.01313664,-0.01243784,0.02965991,0.04131303,0.04145339,0.01104422,-0.04592549,-0.01355478,-0.04538542,-0.00228853,-0.03611673,-0.01878037,0.01008012,-0.01948072,-0.00036136,0.01140905,0.00278957,-0.02784215,0.00764614,0.02485579,-0.00105617,0.08476198,0.03365543,0.04653671,-0.02575273,0.01977349,0.05549157,-0.08275078,-0.01972047,-0.01826262,-0.03210345,-0.08251397,0.06687553,0.06163773,0.09294989,-0.01417386,0.08321508,0.02930561,0.00994792,-0.00986569,-0.01641561,-0.01964829,-0.01659947,0.03724603,0.03466539,0.01600551,-0.27724031,0.00833748,-0.06512422,0.06948397,-0.01924447,-0.0271063,0.01647731,-0.04013861,0.00417913,-0.0595375,0.03868828,0.00831542,-0.00048831,-0.01557109,0.04633775,-0.02107446,0.05258193,-0.05707825,0.06624179,0.03207124,0.03063536,0.04417942,0.23755242,0.00568823,0.03322901,0.03534237,-0.02029227,0.05104997,0.00404099,0.04323663,0.01547098,-0.0261645,0.05912962,-0.02802701,-0.02392506,0.04039107,-0.02645933,0.0225061,0.0249689,0.02082397,-0.08130152,-0.0538246,-0.00143853,-0.00750689,0.15436265,0.03294475,-0.0033165,-0.06595211,0.0330154,0.05756873,-0.0902925,0.00555759,-0.07928164,0.00836675,-0.00822439,0.01275503,-0.02552769,-0.00681158,0.02844382,-0.01418206,0.0679351,-0.01716589,0.04075963,0.02906128,-0.01877591],"last_embed":{"hash":"bafacb","tokens":400}}},"text":null,"length":0,"last_read":{"hash":"bafacb","at":1753423495792},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#[<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>](https://saliu.com/content/lottery.html)","lines":[213,245],"size":4165,"outlinks":[{"title":"<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":5},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":7},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":9},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":10},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":11},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":13},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":14},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":15},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":16},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":17},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":18},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":19},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":20},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":21},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":22},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":23},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":24},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":25},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":26},{"title":"The first lotto software to apply reversed lottery strategies with the best chance to win.","target":"https://saliu.com/HLINE.gif","line":28},{"title":"Forums","target":"https://forums.saliu.com/","line":30},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":30},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":30},{"title":"Contents","target":"https://saliu.com/content/index.html","line":30},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":30},{"title":"Home","target":"https://saliu.com/index.htm","line":30},{"title":"Search","target":"https://saliu.com/Search.htm","line":30},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":30},{"title":"You read about strategy, lottery, lotto, software, algorithms, number, pairings, pairs, frequency.","target":"https://saliu.com/HLINE.gif","line":32}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#[<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>](https://saliu.com/content/lottery.html)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11313513,-0.02758054,-0.04325919,-0.00522437,-0.07403872,0.08226077,-0.01107936,0.01128648,0.0323128,-0.01017226,-0.01341491,-0.02151579,0.07281253,-0.04961987,-0.00376082,-0.05007473,-0.00789429,0.04680504,-0.0373864,-0.0254693,0.0851386,-0.04902415,-0.06501747,-0.08335388,0.05706803,0.02084658,-0.04557496,-0.05807105,-0.03256678,-0.21263869,0.04198768,0.03603652,-0.0083028,-0.04244889,-0.07197374,-0.00502132,-0.04872948,0.03070124,-0.04696138,0.03421262,0.00420271,-0.00150822,0.04999904,-0.00459417,0.03489242,-0.0162266,-0.03382896,0.0223627,0.06535456,0.006122,-0.03767613,0.03511255,-0.01083779,0.03568612,0.08133321,0.0708492,0.07678473,0.09572691,0.02318519,0.0605296,0.02160817,0.06501244,-0.20818317,0.04759905,0.02352269,0.03142549,0.01502738,0.00000569,0.02228536,0.05722702,-0.00840478,0.01407818,0.00515855,0.04612596,0.06134176,-0.00490343,-0.08202953,-0.03940551,-0.03196207,-0.00529668,-0.03861191,0.00156898,0.00748709,0.01369445,0.00384582,0.03407059,0.04684432,0.02735532,0.1097307,-0.0800824,0.00525068,0.0456313,0.06669388,0.01289861,0.02721973,0.00203294,0.05146817,-0.00323697,0.03168942,0.11253426,0.0050889,0.01152606,0.00485197,0.01667958,0.03591982,-0.10544401,-0.03689943,-0.04712458,0.03117171,0.0261013,0.04026692,0.0026259,0.03994598,-0.06339513,-0.03061239,0.028463,-0.0074133,0.03254233,0.02789653,-0.0110253,-0.05783888,-0.00816347,0.01469888,-0.01001485,0.01034983,-0.01325343,0.00213045,0.07401168,0.02184288,0.04963726,0.03608382,0.00696628,-0.12988445,-0.04270835,-0.03550829,-0.01386442,0.01963073,-0.04864661,0.02484363,0.0520763,-0.01740864,-0.06739852,0.05488596,-0.08272476,-0.01092833,0.0840569,0.01919079,-0.00114017,0.01121234,0.01872114,0.00531936,-0.02209427,-0.0147594,-0.0657832,0.02095024,0.01925286,0.08237322,0.0557286,-0.01937759,0.02822244,-0.04205408,-0.01267898,-0.05549366,0.15417977,-0.00159491,-0.12084524,-0.00444828,0.04483029,-0.05786797,-0.07592658,-0.0305942,-0.01362676,-0.03284163,-0.00747822,0.12126463,-0.00425936,-0.08009522,-0.08143005,0.00476127,-0.01878006,-0.00580941,-0.04655128,-0.02395499,0.02261344,-0.05343445,-0.05735415,-0.01638736,-0.03438265,0.0095557,0.05490452,-0.02947276,-0.02636199,-0.05778598,-0.01431432,-0.01918171,-0.03929561,-0.04208234,-0.05199817,0.03108038,-0.01633952,-0.00878332,-0.00495117,0.00743073,0.00750186,-0.00460156,0.05455372,0.00004731,-0.05584014,0.05825123,0.03319483,-0.00155667,0.02498963,0.02644549,0.03059206,-0.03925761,0.00164362,0.01512113,0.01829042,-0.01864111,0.06065034,-0.00683591,0.01266419,-0.06064516,-0.17781599,-0.02792013,-0.03960869,-0.00553538,0.00709573,-0.0263624,0.02416548,-0.03800691,0.01419587,0.06590721,0.08524945,-0.03077678,-0.03945041,0.0638744,-0.03479698,-0.00288522,-0.05319612,-0.03336693,-0.04623996,0.0269952,-0.01731359,0.01077281,-0.00883624,-0.06537496,0.06149883,-0.02248288,0.12400954,-0.00993455,-0.00035318,0.04030035,0.0707563,0.00281233,-0.03067947,-0.03887259,0.02103155,0.04293105,0.01658744,-0.03217189,-0.04743754,0.01604674,-0.10062892,0.01233721,0.02662066,-0.07551226,-0.07145662,0.00206843,-0.00812311,-0.00681557,0.03790531,0.06304836,0.01161,-0.01435514,0.0314188,0.04302415,0.04208957,0.00819623,-0.04342593,-0.00981517,-0.04386684,0.0016064,-0.03638532,-0.0214465,0.01057584,-0.01802554,0.00479772,0.00616798,-0.00059493,-0.02743885,0.00838079,0.02440907,-0.00537417,0.08622839,0.0306895,0.04848118,-0.02594824,0.02070459,0.05905955,-0.08048195,-0.01871872,-0.01457554,-0.03497504,-0.0847137,0.06606347,0.06413498,0.09350061,-0.00985928,0.08229461,0.02837265,0.0087292,-0.01197193,-0.0150315,-0.020277,-0.01941356,0.04171785,0.03522149,0.02051518,-0.27679282,0.01005864,-0.06174342,0.0684313,-0.01852498,-0.02995253,0.01841052,-0.04242756,0.00927003,-0.05985231,0.04182778,0.00941415,0.00043997,-0.02000654,0.04575017,-0.02096444,0.0564604,-0.05469187,0.06490345,0.03251731,0.03040171,0.0487114,0.23860613,0.00493293,0.03252535,0.03599612,-0.02048366,0.04499158,0.00117159,0.04658009,0.0172668,-0.02415122,0.05724644,-0.02529777,-0.02692418,0.04117525,-0.02609854,0.0247652,0.0247037,0.01995942,-0.08075357,-0.05090943,-0.0026095,-0.00670149,0.15065378,0.0352862,-0.00718485,-0.06972362,0.03467719,0.05528361,-0.0876358,0.00489833,-0.08020312,0.00652566,-0.00632544,0.01194837,-0.02172402,-0.00579522,0.03037712,-0.01400393,0.06876609,-0.01626658,0.04192572,0.03092786,-0.01643916],"last_embed":{"hash":"weskff","tokens":399}}},"text":null,"length":0,"last_read":{"hash":"weskff","at":1753423495904},"key":"notes/saliu/Lottery Strategy in Reverse for Pairs, Number Frequency.md#Lottery Strategy in Reverse for Pairs, Number Frequency#[<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>](https://saliu.com/content/lottery.html)#{1}","lines":[215,245],"size":4044,"outlinks":[{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":1},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":3},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":5},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":7},{"title":"**Lottery Mathematics, Lotto Mathematics, Math, Maths**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":8},{"title":"_**Lottery and Lotto Filtering in Software**_","target":"https://saliu.com/filters.html","line":9},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":10},{"title":"_**Lottery Strategy, Systems Based on Number, Digit Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":11},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":12},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":13},{"title":"_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_","target":"https://saliu.com/strategy.html","line":14},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":15},{"title":"**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**","target":"https://saliu.com/delta-lotto-software.html","line":16},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":17},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":18},{"title":"_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":19},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":20},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":21},{"title":"_**Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":22},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":23},{"title":"**Lottery Software, Lotto Software**","target":"https://saliu.com/infodown.html","line":24},{"title":"The first lotto software to apply reversed lottery strategies with the best chance to win.","target":"https://saliu.com/HLINE.gif","line":26},{"title":"Forums","target":"https://forums.saliu.com/","line":28},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":28},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":28},{"title":"Contents","target":"https://saliu.com/content/index.html","line":28},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":28},{"title":"Home","target":"https://saliu.com/index.htm","line":28},{"title":"Search","target":"https://saliu.com/Search.htm","line":28},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":28},{"title":"You read about strategy, lottery, lotto, software, algorithms, number, pairings, pairs, frequency.","target":"https://saliu.com/HLINE.gif","line":30}],"class_name":"SmartBlock"},
