
"smart_sources:筆記/202506251155.md": {"path":"筆記/202506251155.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1bzdobb","at":1750828860000},"class_name":"SmartSource","last_import":{"mtime":1750823821400,"size":4554,"at":1750828860004,"hash":"1bzdobb"},"blocks":{"#":[1,4],"###程式碼品質與清晰度分析":[5,8],"###程式碼品質與清晰度分析#{1}":[7,8],"###程式碼修改建議":[9,62],"###程式碼修改建議#{1}":[11,62],"###修改說明":[63,75],"###修改說明#{1}":[65,68],"###修改說明#{2}":[69,74],"###修改說明#{3}":[75,75]},"outlinks":[{"title":"A0032_0.0.0.3_A0015_0007_05_0.0.0.1_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\G3winscumsPatterns\\src\\A0032_0.0.0.3_A0015_0007_05_0.0.0.1_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl \"f:\\work\\JuliaProject\\G3winscumsPatterns\\src\\A0032_0.0.0.3_A0015_0007_05_0.0.0.1_all_pattern_analysis_參數最佳化_獲利評估_standalone.jl\"","line":11},{"title":"report.md","target":"code-assist-path:f:\\work\\JuliaProject\\G3winscumsPatterns\\report.md \"f:\\work\\JuliaProject\\G3winscumsPatterns\\report.md\"","line":73},{"title":"report.md","target":"code-assist-path:f:\\work\\JuliaProject\\G3winscumsPatterns\\report.md \"f:\\work\\JuliaProject\\G3winscumsPatterns\\report.md\"","line":75}]},