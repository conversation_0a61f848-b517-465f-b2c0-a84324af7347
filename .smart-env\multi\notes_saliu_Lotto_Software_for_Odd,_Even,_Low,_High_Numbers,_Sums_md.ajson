
"smart_sources:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md": {"path":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"u9gr6x","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363610794,"size":17555,"at":1753423416500,"hash":"u9gr6x"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Software for Odd, Even, Low, High Numbers, Sums":[8,209],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{1}":[10,15],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{2}":[16,16],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{3}":[17,17],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{4}":[18,18],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{5}":[19,19],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{6}":[20,22],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{7}":[23,52],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>":[53,59],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>#{1}":[55,59],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>":[60,66],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>#{1}":[62,66],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>":[67,74],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>#{1}":[69,74],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>A = Generate <i>All Numbers in One Line</i></u>":[75,78],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>A = Generate <i>All Numbers in One Line</i></u>#{1}":[77,78],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>":[79,106],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>#{1}":[81,106],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>":[107,123],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{1}":[109,118],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{2}":[119,119],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{3}":[120,121],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{4}":[122,123],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>":[124,145],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>#{1}":[126,145],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>":[146,155],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>#{1}":[148,155],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>":[156,163],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>#{1}":[158,163],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>":[164,186],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{1}":[166,176],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{2}":[177,178],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{3}":[179,180],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{4}":[181,181],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{5}":[182,182],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{6}":[183,184],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{7}":[185,186],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>":[187,209],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{1}":[189,190],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{2}":[191,191],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{3}":[192,192],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{4}":[193,193],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{5}":[194,194],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{6}":[195,195],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{7}":[196,196],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{8}":[197,197],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{9}":[198,198],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{10}":[199,199],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{11}":[200,200],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{12}":[201,201],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{13}":[202,203],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{14}":[204,209]},"outlinks":[{"title":"Pick lottery software divides the 0 to 9 digits in odd, even, low, high, sums, frequency groups.","target":"https://saliu.com/ScreenImgs/pick-4-lottery-groups.gif","line":23},{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":47},{"title":"The main menu of UserLottoGroups, lotto software for odd / even, low / high, sums, sum-totals.","target":"https://saliu.com/ScreenImgs/UserGroups6.gif","line":51},{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":105},{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/odd-even-lotto.gif","line":115},{"title":"These are static filters, do not repeat from lottery draw to next drawing: eliminate them from play.","target":"https://saliu.com/HLINE.gif","line":117},{"title":"The lotto software generates lottery combinations from several lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":122},{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/UserGroups63.gif","line":126},{"title":"Menu 4: Software generates all types of lotto combinations, including all 6/49 games.","target":"https://saliu.com/ScreenImgs/UserGroups64.gif","line":144},{"title":"Program 4: Software generates lotto combinations from groups of lotto numbers created by a player.","target":"https://saliu.com/ScreenImgs/UserGroups62.gif","line":148},{"title":"_**lotto decades**_","target":"https://saliu.com/decades.html","line":152},{"title":"Let's create lottery strategies and generate lotto combinations for winning drawings.","target":"https://saliu.com/HLINE.gif","line":154},{"title":"Lotto #5: Software checks strategies for odd/even, low/high, lottery sums.","target":"https://saliu.com/ScreenImgs/UserGroups65.gif","line":158},{"title":"Lotto #6: Software analyses how many combinations the lottery strategy generated.","target":"https://saliu.com/ScreenImgs/UserGroups66.gif","line":166},{"title":"Integrated lottery software pick-6 or 6-number lotto games.","target":"https://saliu.com/HLINE.gif","line":172},{"title":"**Lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":177},{"title":"When it comes to lotto software for 6-number games, the best program is Bright.","target":"https://saliu.com/HLINE.gif","line":179},{"title":"_**Lotto Software for 5-Number Games**_","target":"https://saliu.com/lotto5-software.html","line":182},{"title":"_**Lottery Software for 6-Number Lotto**_","target":"https://saliu.com/lotto6-software.html","line":183},{"title":"\n\n## <u>Resources in Lottery Software, Lotto Strategies, Systems</u>\n\n","target":"https://saliu.com/content/lottery.html","line":185},{"title":"**Visual Book, Manual for Lotto Software**","target":"https://saliu.com/forum/lotto-book.html","line":191},{"title":"**<u>MDIEditor Lotto</u>**: _**Software Tutorial, User Guide, Instructions**_","target":"https://saliu.com/MDI-lotto-guide.html","line":192},{"title":"_**Lotto, Lottery Software Tutorial**_","target":"https://saliu.com/bbs/messages/818.html","line":193},{"title":"_**Filtering, Filters in Lottery Software, Lotto Programs**_","target":"https://saliu.com/filters.html","line":194},{"title":"_**Lotto, Lottery Strategy in Reverse: <u>Not-to-Win</u> Leads to <u>Not-to-Lose</u> or <u>WIN</u>**_","target":"https://saliu.com/reverse-strategy.html","line":195},{"title":"_**Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_","target":"https://saliu.com/strategy.html","line":196},{"title":"_**Lottery Strategy, Systems Based on Number <u>Frequency</u>**_","target":"https://saliu.com/frequency-lottery.html","line":197},{"title":"_**The Best Strategy for Lottery, Lotto, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":198},{"title":"_**Cross-Reference Lottery, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":199},{"title":"_**Lottery, Lotto Sums, Sum-Totals**_","target":"https://saliu.com/forum/lottery-sums.html","line":200},{"title":"_**Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":201},{"title":"_**Lotto Program for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":202},{"title":"Finally, lotto software to work with all groups of numbers: odd, even, low, high, frequency, sums, sum totals, decades.","target":"https://saliu.com/HLINE.gif","line":204},{"title":"Forums","target":"https://forums.saliu.com/","line":206},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":206},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":206},{"title":"Contents","target":"https://saliu.com/content/index.html","line":206},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":206},{"title":"Home","target":"https://saliu.com/index.htm","line":206},{"title":"Search","target":"https://saliu.com/Search.htm","line":206},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":206},{"title":"Get now this powerful lotto 5, 6 software for your number groups.","target":"https://saliu.com/HLINE.gif","line":208}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto","software","numbers","groups","odd","even","low","high","frequency","sums","sum totals","software user"],"source":"https://saliu.com/lotto-groups.html","author":null}},
"smart_sources:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md": {"path":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10367503,-0.042269,-0.05602653,-0.0557631,-0.05971708,0.00726343,-0.04653922,-0.01431608,0.04055999,-0.04719912,0.0372263,-0.01932134,0.03498791,-0.00251955,-0.03849788,-0.04647977,-0.00414273,-0.03483671,-0.05794011,0.00160854,0.08811268,-0.03877348,-0.04280647,-0.06180514,0.06675233,-0.01156734,-0.0735821,-0.06680559,-0.05028665,-0.22237407,0.02791379,0.06077014,0.06710178,-0.04368418,-0.04368365,-0.03992655,-0.01759229,0.03405766,-0.04274179,0.02757137,0.00803695,0.04743909,-0.01303909,-0.0265632,0.020776,-0.04031921,-0.03602544,0.02235601,0.01987661,0.02412429,-0.0477553,-0.00233353,0.0012148,0.08422427,0.03939569,0.00709545,0.03461268,0.06368676,0.01920109,0.05445592,0.03297442,0.04342467,-0.12748013,0.02295832,0.02857439,0.02079435,-0.01787157,-0.05764369,0.0268067,0.00697311,0.02369941,0.05940191,-0.01937362,0.04630192,0.0597496,-0.0499578,-0.03050236,-0.05119016,-0.06277006,-0.01886263,-0.06874274,-0.05119137,-0.02259877,-0.02338597,0.03758749,0.03463904,0.07613144,0.02022557,0.07364345,-0.0740818,-0.0022655,0.06620862,0.04241119,0.04769232,0.06657484,0.00569399,0.06989192,-0.0009496,-0.00823121,0.12275267,0.0097558,-0.01915567,-0.01771075,-0.00580122,0.00770839,-0.01764696,-0.03481091,-0.0553158,-0.05812955,0.01781937,0.04715349,0.03144237,0.0834917,-0.05007465,-0.0393878,-0.04289497,-0.02069441,0.01736223,0.01767124,-0.00825131,-0.0255423,0.00979503,0.01274259,-0.01162083,0.03218418,0.03437247,0.01494016,0.07419807,0.03708079,0.03150047,0.04448973,0.01491321,-0.1189949,-0.03777176,0.01527986,-0.02066986,-0.01545083,0.00516619,0.00400734,0.02761797,-0.02175889,-0.03509916,0.06883824,-0.0962324,-0.00024653,0.06999898,-0.01110139,-0.03125312,0.03431488,0.00455828,0.01160008,-0.00832662,-0.05307632,-0.07044771,-0.01376115,-0.01417966,0.11779644,0.08943041,-0.05695959,0.00038309,-0.00465654,-0.03512037,-0.01328458,0.11030261,-0.01277252,-0.10184352,-0.03231209,0.06217589,-0.04102077,-0.0478112,-0.01550776,0.0018529,-0.02149324,0.06304049,0.10181224,-0.01982756,-0.07605178,-0.05443197,0.004521,-0.00518355,-0.00238573,0.00429604,-0.04249403,-0.0176319,-0.02955951,-0.07108501,-0.00361376,-0.02053421,0.00494667,0.0295005,-0.03940665,-0.00352866,-0.03835969,-0.0253254,-0.01943096,-0.03227109,-0.02677368,-0.02180249,0.07800383,-0.01891158,-0.00977322,0.00454784,0.05687105,0.01909606,-0.00164743,0.05005708,0.00124948,-0.06462026,0.10031091,0.03098004,-0.00563183,-0.00753268,0.04192705,0.11494889,-0.05488638,0.04781829,-0.00963973,0.05333498,0.02739631,0.03401802,-0.00756133,0.00008143,-0.08487121,-0.19382671,-0.02266536,0.01094573,0.01044946,0.0047689,-0.02128767,0.06694211,-0.03267718,0.04121902,0.08334757,0.1085624,-0.05355864,-0.05103463,0.02832738,-0.02326573,0.02659209,-0.10211992,-0.00868895,-0.02917431,0.04498545,-0.01209592,0.04655478,-0.02583051,-0.07399987,0.02250042,-0.02172911,0.11107735,0.01322106,-0.00566363,-0.05826349,0.04705311,-0.00880977,-0.03772707,-0.01258483,-0.00699953,0.04381172,-0.03531449,0.00221572,-0.00239922,-0.02640405,-0.10361075,-0.0133069,-0.00135767,-0.10378131,0.00301534,0.04169573,-0.01524998,-0.0009572,0.02077083,0.02419017,0.03427728,-0.02323708,0.05096718,0.04426347,0.03978102,-0.0357232,-0.06268887,-0.01658192,0.01899831,0.06357503,0.00918806,-0.06072424,0.05014315,-0.02129249,0.02818059,0.02937914,-0.0083686,-0.01913362,0.03424195,-0.02315779,-0.00282472,0.10527863,-0.00468798,0.05471438,-0.03018471,0.02414389,0.05387062,-0.03082689,0.01450563,-0.02785411,-0.00666184,-0.06454927,0.04381676,0.07987124,0.04730157,0.02218386,0.02561204,0.06399272,0.01493289,0.00959435,0.04699633,0.02445381,-0.03102247,0.0405258,0.00442328,0.01852041,-0.25682408,0.02270558,-0.04792836,0.06298997,0.0174995,0.00197854,0.03085143,0.00743634,0.01090775,-0.01253003,0.05864702,0.0266078,0.0368486,-0.10508355,0.01052766,-0.00139392,-0.01806194,-0.04396894,0.01670527,-0.00610432,0.05314752,0.03419796,0.26345325,0.00790735,-0.0072623,0.03379556,0.02119227,0.01984836,0.02737994,0.02821734,-0.00587631,0.00056933,0.11183134,-0.0334781,-0.04650623,-0.00085838,-0.02511327,-0.05176961,-0.00201784,0.05148176,-0.05699271,-0.01129959,-0.04974071,-0.00996021,0.1284042,0.01023949,-0.05057444,-0.07361846,0.06799139,0.06865146,-0.07279737,-0.07475651,-0.05550154,-0.02387569,0.05569243,0.0424435,-0.01875675,0.04343996,-0.03751529,0.0297422,-0.02042371,-0.0002111,0.04937703,0.02986938,-0.02367693],"last_embed":{"hash":"u9gr6x","tokens":490}}},"last_read":{"hash":"u9gr6x","at":1753423532862},"class_name":"SmartSource","last_import":{"mtime":1753363610794,"size":17555,"at":1753423416500,"hash":"u9gr6x"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Software for Odd, Even, Low, High Numbers, Sums":[8,209],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{1}":[10,15],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{2}":[16,16],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{3}":[17,17],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{4}":[18,18],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{5}":[19,19],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{6}":[20,22],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#{7}":[23,52],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>":[53,59],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>#{1}":[55,59],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>":[60,66],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>#{1}":[62,66],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>":[67,74],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>#{1}":[69,74],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>A = Generate <i>All Numbers in One Line</i></u>":[75,78],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>A = Generate <i>All Numbers in One Line</i></u>#{1}":[77,78],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>":[79,106],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>#{1}":[81,106],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>":[107,123],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{1}":[109,118],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{2}":[119,119],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{3}":[120,121],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{4}":[122,123],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>":[124,145],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>#{1}":[126,145],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>":[146,155],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>#{1}":[148,155],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>":[156,163],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>#{1}":[158,163],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>":[164,186],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{1}":[166,176],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{2}":[177,178],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{3}":[179,180],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{4}":[181,181],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{5}":[182,182],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{6}":[183,184],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{7}":[185,186],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>":[187,209],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{1}":[189,190],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{2}":[191,191],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{3}":[192,192],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{4}":[193,193],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{5}":[194,194],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{6}":[195,195],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{7}":[196,196],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{8}":[197,197],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{9}":[198,198],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{10}":[199,199],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{11}":[200,200],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{12}":[201,201],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{13}":[202,203],"#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{14}":[204,209]},"outlinks":[{"title":"Pick lottery software divides the 0 to 9 digits in odd, even, low, high, sums, frequency groups.","target":"https://saliu.com/ScreenImgs/pick-4-lottery-groups.gif","line":23},{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":47},{"title":"The main menu of UserLottoGroups, lotto software for odd / even, low / high, sums, sum-totals.","target":"https://saliu.com/ScreenImgs/UserGroups6.gif","line":51},{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":105},{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/odd-even-lotto.gif","line":115},{"title":"These are static filters, do not repeat from lottery draw to next drawing: eliminate them from play.","target":"https://saliu.com/HLINE.gif","line":117},{"title":"The lotto software generates lottery combinations from several lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":122},{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/UserGroups63.gif","line":126},{"title":"Menu 4: Software generates all types of lotto combinations, including all 6/49 games.","target":"https://saliu.com/ScreenImgs/UserGroups64.gif","line":144},{"title":"Program 4: Software generates lotto combinations from groups of lotto numbers created by a player.","target":"https://saliu.com/ScreenImgs/UserGroups62.gif","line":148},{"title":"_**lotto decades**_","target":"https://saliu.com/decades.html","line":152},{"title":"Let's create lottery strategies and generate lotto combinations for winning drawings.","target":"https://saliu.com/HLINE.gif","line":154},{"title":"Lotto #5: Software checks strategies for odd/even, low/high, lottery sums.","target":"https://saliu.com/ScreenImgs/UserGroups65.gif","line":158},{"title":"Lotto #6: Software analyses how many combinations the lottery strategy generated.","target":"https://saliu.com/ScreenImgs/UserGroups66.gif","line":166},{"title":"Integrated lottery software pick-6 or 6-number lotto games.","target":"https://saliu.com/HLINE.gif","line":172},{"title":"**Lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":177},{"title":"When it comes to lotto software for 6-number games, the best program is Bright.","target":"https://saliu.com/HLINE.gif","line":179},{"title":"_**Lotto Software for 5-Number Games**_","target":"https://saliu.com/lotto5-software.html","line":182},{"title":"_**Lottery Software for 6-Number Lotto**_","target":"https://saliu.com/lotto6-software.html","line":183},{"title":"\n\n## <u>Resources in Lottery Software, Lotto Strategies, Systems</u>\n\n","target":"https://saliu.com/content/lottery.html","line":185},{"title":"**Visual Book, Manual for Lotto Software**","target":"https://saliu.com/forum/lotto-book.html","line":191},{"title":"**<u>MDIEditor Lotto</u>**: _**Software Tutorial, User Guide, Instructions**_","target":"https://saliu.com/MDI-lotto-guide.html","line":192},{"title":"_**Lotto, Lottery Software Tutorial**_","target":"https://saliu.com/bbs/messages/818.html","line":193},{"title":"_**Filtering, Filters in Lottery Software, Lotto Programs**_","target":"https://saliu.com/filters.html","line":194},{"title":"_**Lotto, Lottery Strategy in Reverse: <u>Not-to-Win</u> Leads to <u>Not-to-Lose</u> or <u>WIN</u>**_","target":"https://saliu.com/reverse-strategy.html","line":195},{"title":"_**Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_","target":"https://saliu.com/strategy.html","line":196},{"title":"_**Lottery Strategy, Systems Based on Number <u>Frequency</u>**_","target":"https://saliu.com/frequency-lottery.html","line":197},{"title":"_**The Best Strategy for Lottery, Lotto, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":198},{"title":"_**Cross-Reference Lottery, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":199},{"title":"_**Lottery, Lotto Sums, Sum-Totals**_","target":"https://saliu.com/forum/lottery-sums.html","line":200},{"title":"_**Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":201},{"title":"_**Lotto Program for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":202},{"title":"Finally, lotto software to work with all groups of numbers: odd, even, low, high, frequency, sums, sum totals, decades.","target":"https://saliu.com/HLINE.gif","line":204},{"title":"Forums","target":"https://forums.saliu.com/","line":206},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":206},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":206},{"title":"Contents","target":"https://saliu.com/content/index.html","line":206},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":206},{"title":"Home","target":"https://saliu.com/index.htm","line":206},{"title":"Search","target":"https://saliu.com/Search.htm","line":206},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":206},{"title":"Get now this powerful lotto 5, 6 software for your number groups.","target":"https://saliu.com/HLINE.gif","line":208}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto","software","numbers","groups","odd","even","low","high","frequency","sums","sum totals","software user"],"source":"https://saliu.com/lotto-groups.html","author":null}},"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09382931,-0.04561853,-0.05159189,-0.05677969,-0.05811381,-0.00509075,-0.02940269,-0.02132161,0.02807002,-0.04617482,0.03990708,-0.0104567,0.06060948,-0.00663372,-0.031297,-0.05459103,-0.00443888,-0.02572686,-0.06297128,-0.00603471,0.08407125,-0.02522405,-0.05917405,-0.0546363,0.05530156,-0.00347232,-0.06990963,-0.05871471,-0.04285008,-0.23056027,0.02830091,0.05022285,0.06635392,-0.04699018,-0.04876874,-0.04441587,-0.01975674,0.03747855,-0.05940106,0.02897855,0.00473928,0.03971277,-0.00122882,-0.03900171,0.01663144,-0.03697496,-0.01480323,0.01114224,0.01645697,0.01612705,-0.04499861,0.01022759,0.00750516,0.09366249,0.0083772,0.00733147,0.03037117,0.08443518,0.02443608,0.04815583,0.01112846,0.03591638,-0.12426497,0.03442298,-0.00150785,0.01448287,-0.02071637,-0.05674273,0.03017218,0.02581884,0.0336653,0.07082337,-0.03391902,0.0388824,0.04203894,-0.05578224,-0.02955052,-0.05352035,-0.06559338,-0.00438392,-0.06621127,-0.06803425,-0.01085048,-0.0199757,0.03731905,0.03188306,0.0679945,0.00199041,0.06823868,-0.05827343,-0.01309032,0.05709167,0.03696251,0.05700665,0.0747897,0.00442816,0.0739315,0.00767263,-0.00002238,0.13142212,0.01369132,-0.00940982,-0.01014708,-0.02521337,0.00408253,-0.02606554,-0.03042086,-0.03973661,-0.05476804,0.02599502,0.04428531,0.02599287,0.08559485,-0.04062941,-0.04841406,-0.03206022,-0.02118445,0.00439882,0.02616872,-0.00820374,-0.03410804,-0.00436734,0.00716819,-0.01797445,0.04142547,0.04769009,0.01459492,0.07182863,0.02533384,0.03957349,0.05023512,0.02188822,-0.11912902,-0.03513644,0.02227269,-0.01044809,-0.01764306,0.00806136,-0.00657394,0.03136848,-0.03504575,-0.02907941,0.06080909,-0.09159797,-0.00548094,0.08196488,-0.01374051,-0.02643516,0.01517977,-0.00692797,0.01046281,0.00450252,-0.06423869,-0.05336734,-0.01570865,-0.01547258,0.11107127,0.08371964,-0.07379023,-0.00282255,-0.01339438,-0.03335668,-0.01849901,0.09836274,-0.00360099,-0.09261129,-0.02872725,0.05600581,-0.05141831,-0.04364999,-0.02172364,0.01227713,-0.03477995,0.06109333,0.08653788,-0.02448536,-0.09608,-0.0565717,-0.01047778,-0.0152015,-0.00678655,0.00015565,-0.03698626,-0.02160673,-0.03088432,-0.08375137,0.00348357,-0.00767533,0.00273525,0.04102172,-0.03532072,-0.0023258,-0.03385939,-0.01781051,-0.01713675,-0.04087278,-0.01034596,-0.02733151,0.07261398,-0.0439407,0.00172409,-0.00077973,0.06599455,0.04236626,-0.01484939,0.05449515,-0.02324302,-0.06546406,0.11361642,0.02993822,-0.014495,-0.01046446,0.04084097,0.10464645,-0.03521336,0.04071037,-0.0140492,0.04621401,0.0330466,0.02904701,0.00510658,0.01459136,-0.08048426,-0.19948323,-0.01575818,0.00413586,0.02104087,0.00833478,-0.02490021,0.03969082,-0.0360777,0.03904686,0.11106692,0.1136709,-0.05455866,-0.04130156,0.03649521,-0.02685479,0.03918893,-0.09260343,-0.00094815,-0.04192596,0.02747645,-0.00399334,0.03570082,-0.02044414,-0.06976885,0.02745855,-0.01775465,0.11115374,0.00388129,0.00746841,-0.04335076,0.03916001,-0.00910901,-0.034717,0.00375699,-0.008925,0.06306406,-0.0362307,-0.01461148,0.01142265,-0.02226104,-0.08709075,-0.01758536,-0.00207423,-0.11600529,0.01292397,0.04905811,-0.01284672,0.01506875,0.01530869,0.02412242,0.03890528,-0.03833871,0.05513646,0.03826652,0.04612463,-0.05113886,-0.0610889,-0.0229122,0.01779901,0.06442683,0.0032991,-0.05888183,0.04118286,-0.02061751,0.03285187,0.02221622,0.00491365,-0.01447423,0.03881119,-0.009523,-0.01187163,0.10272949,-0.01500874,0.05996979,-0.03513853,0.03505689,0.05527357,-0.0255032,0.01627799,-0.03819954,0.00827842,-0.05921883,0.05725892,0.08362833,0.05415227,0.01214668,0.02302882,0.0649012,0.02635647,0.00459381,0.04247026,0.02734518,-0.03034138,0.03983516,0.00431979,0.0106115,-0.25100425,0.01673688,-0.04517011,0.05545439,0.01945315,-0.01408969,0.02702676,0.00667667,0.02067439,-0.03125878,0.05498365,0.03342694,0.04754014,-0.10042936,0.01136726,0.00163169,-0.02217516,-0.04599345,0.03316993,0.00642568,0.06052935,0.0405312,0.26520982,0.00728643,-0.0081445,0.02534821,0.01278075,0.00406899,0.00774244,0.03193196,0.00619875,0.00026385,0.12842882,-0.0358701,-0.03850765,-0.01432205,-0.017894,-0.03860517,0.00222442,0.04766621,-0.05572343,-0.00544861,-0.05069893,-0.02294621,0.12364352,0.01281617,-0.04856576,-0.06578469,0.05574167,0.07665741,-0.06066037,-0.0567908,-0.0505125,-0.02568747,0.06280833,0.03591165,-0.03666994,0.04019586,-0.04691612,0.01230662,-0.01502604,-0.00326998,0.04068774,0.03337647,-0.0171414],"last_embed":{"hash":"1guy1l4","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1guy1l4","at":1753423528958},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums","lines":[8,209],"size":17344,"outlinks":[{"title":"Pick lottery software divides the 0 to 9 digits in odd, even, low, high, sums, frequency groups.","target":"https://saliu.com/ScreenImgs/pick-4-lottery-groups.gif","line":16},{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":40},{"title":"The main menu of UserLottoGroups, lotto software for odd / even, low / high, sums, sum-totals.","target":"https://saliu.com/ScreenImgs/UserGroups6.gif","line":44},{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":98},{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/odd-even-lotto.gif","line":108},{"title":"These are static filters, do not repeat from lottery draw to next drawing: eliminate them from play.","target":"https://saliu.com/HLINE.gif","line":110},{"title":"The lotto software generates lottery combinations from several lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":115},{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/UserGroups63.gif","line":119},{"title":"Menu 4: Software generates all types of lotto combinations, including all 6/49 games.","target":"https://saliu.com/ScreenImgs/UserGroups64.gif","line":137},{"title":"Program 4: Software generates lotto combinations from groups of lotto numbers created by a player.","target":"https://saliu.com/ScreenImgs/UserGroups62.gif","line":141},{"title":"_**lotto decades**_","target":"https://saliu.com/decades.html","line":145},{"title":"Let's create lottery strategies and generate lotto combinations for winning drawings.","target":"https://saliu.com/HLINE.gif","line":147},{"title":"Lotto #5: Software checks strategies for odd/even, low/high, lottery sums.","target":"https://saliu.com/ScreenImgs/UserGroups65.gif","line":151},{"title":"Lotto #6: Software analyses how many combinations the lottery strategy generated.","target":"https://saliu.com/ScreenImgs/UserGroups66.gif","line":159},{"title":"Integrated lottery software pick-6 or 6-number lotto games.","target":"https://saliu.com/HLINE.gif","line":165},{"title":"**Lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":170},{"title":"When it comes to lotto software for 6-number games, the best program is Bright.","target":"https://saliu.com/HLINE.gif","line":172},{"title":"_**Lotto Software for 5-Number Games**_","target":"https://saliu.com/lotto5-software.html","line":175},{"title":"_**Lottery Software for 6-Number Lotto**_","target":"https://saliu.com/lotto6-software.html","line":176},{"title":"\n\n## <u>Resources in Lottery Software, Lotto Strategies, Systems</u>\n\n","target":"https://saliu.com/content/lottery.html","line":178},{"title":"**Visual Book, Manual for Lotto Software**","target":"https://saliu.com/forum/lotto-book.html","line":184},{"title":"**<u>MDIEditor Lotto</u>**: _**Software Tutorial, User Guide, Instructions**_","target":"https://saliu.com/MDI-lotto-guide.html","line":185},{"title":"_**Lotto, Lottery Software Tutorial**_","target":"https://saliu.com/bbs/messages/818.html","line":186},{"title":"_**Filtering, Filters in Lottery Software, Lotto Programs**_","target":"https://saliu.com/filters.html","line":187},{"title":"_**Lotto, Lottery Strategy in Reverse: <u>Not-to-Win</u> Leads to <u>Not-to-Lose</u> or <u>WIN</u>**_","target":"https://saliu.com/reverse-strategy.html","line":188},{"title":"_**Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_","target":"https://saliu.com/strategy.html","line":189},{"title":"_**Lottery Strategy, Systems Based on Number <u>Frequency</u>**_","target":"https://saliu.com/frequency-lottery.html","line":190},{"title":"_**The Best Strategy for Lottery, Lotto, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":191},{"title":"_**Cross-Reference Lottery, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":192},{"title":"_**Lottery, Lotto Sums, Sum-Totals**_","target":"https://saliu.com/forum/lottery-sums.html","line":193},{"title":"_**Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":194},{"title":"_**Lotto Program for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":195},{"title":"Finally, lotto software to work with all groups of numbers: odd, even, low, high, frequency, sums, sum totals, decades.","target":"https://saliu.com/HLINE.gif","line":197},{"title":"Forums","target":"https://forums.saliu.com/","line":199},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":199},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":199},{"title":"Contents","target":"https://saliu.com/content/index.html","line":199},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":199},{"title":"Home","target":"https://saliu.com/index.htm","line":199},{"title":"Search","target":"https://saliu.com/Search.htm","line":199},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":199},{"title":"Get now this powerful lotto 5, 6 software for your number groups.","target":"https://saliu.com/HLINE.gif","line":201}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10723352,-0.04828056,-0.03478525,-0.05260845,-0.0670618,-0.02415874,-0.03480307,-0.02212545,0.00247316,-0.07383001,0.06223118,-0.00962137,0.05466557,0.00360463,-0.05367059,-0.0471756,-0.0038503,-0.0053732,-0.04945113,-0.01450055,0.09968176,-0.00939133,-0.04737931,-0.04884659,0.08817615,0.03300513,-0.05894095,-0.04279927,-0.01198227,-0.194536,0.01668393,0.05224283,0.08865035,-0.01838046,-0.02002816,-0.0301623,-0.02675269,0.04428037,-0.05417655,0.04166111,0.01271718,0.03674404,-0.01718051,-0.02663709,0.01872488,-0.03444032,0.00987049,0.01999795,0.032494,0.00782707,-0.02192845,0.0254266,-0.00622959,0.06880103,0.0094673,0.00643733,0.0346076,0.05608035,0.03566364,0.05102378,0.0418116,0.02431913,-0.12784433,0.02831432,0.03110944,0.0279914,-0.01555509,-0.07093482,0.03449158,0.02041882,0.01370891,0.013594,-0.05281345,0.03546234,0.07295402,-0.05868736,-0.02240764,-0.03393858,-0.05459438,-0.03418849,-0.05277324,-0.08032841,-0.01161634,0.01094822,0.01866744,0.00422021,0.07773521,0.0031402,0.08812302,-0.07122279,-0.02443001,0.05811298,0.0398664,0.05255009,0.08445712,-0.02050453,0.07054379,0.02536343,0.00521342,0.1523408,-0.00377112,-0.02593706,0.01146441,-0.01732942,0.00293257,-0.03174721,-0.02049647,-0.05285248,-0.05557805,0.01179069,0.03022486,0.03608251,0.06914593,-0.05375626,-0.01757293,-0.09251232,-0.01190087,0.02526267,0.01220479,-0.01692366,-0.0023063,0.00312268,0.02674368,-0.03094498,0.05366227,0.04061866,-0.00202107,0.067312,0.03316363,0.0458855,0.04033475,0.00290284,-0.087656,-0.03472091,0.03396643,-0.02158495,-0.00271495,0.0088543,0.00817214,0.00303909,-0.03122555,-0.01231188,0.03299275,-0.10078833,0.00725287,0.06776361,-0.02286862,-0.02943556,0.01933221,0.01306543,0.00094529,-0.00219903,-0.072391,-0.05652248,-0.01459822,-0.0203035,0.12883069,0.07706913,-0.05069183,0.00895132,-0.02164285,-0.01523113,-0.0277598,0.09067933,-0.00983249,-0.0725383,-0.03126227,0.05745721,-0.03009288,-0.04699357,-0.00091176,-0.00173656,-0.01250814,0.05298121,0.0989852,-0.02855773,-0.0860623,-0.05858435,-0.00388031,-0.01636871,-0.02740275,-0.00513491,-0.03928254,-0.00725295,-0.04078117,-0.06874715,-0.01062233,0.00818977,0.01824715,0.0644872,0.00089971,-0.00032487,-0.04892234,-0.03090555,-0.02148897,-0.04223957,-0.04336681,-0.00198195,0.05696947,-0.0510447,0.00062424,0.00250723,0.08290475,0.02607873,0.00795356,0.01642331,-0.00988019,-0.04999625,0.09697938,0.01944949,-0.04374767,0.01004443,0.03673666,0.09264937,-0.03737437,0.02989484,-0.02302458,0.06845216,0.01398765,0.02428334,-0.00492487,-0.02619602,-0.08607216,-0.21825352,-0.03144178,0.01926384,-0.0141056,0.00720422,-0.03274935,0.04517598,-0.01564754,-0.00011011,0.06181194,0.08056905,-0.06515393,-0.03960303,0.00604325,-0.04965644,0.04895099,-0.06339008,-0.02329378,-0.03171171,0.03735708,-0.01020956,0.04758726,-0.04028824,-0.06777769,-0.01037855,-0.00355379,0.11166752,0.01170219,0.04464346,-0.0389968,0.02588453,-0.02169655,-0.05492117,-0.00456629,-0.00693315,0.0406463,-0.04116575,-0.01473956,0.00420601,-0.03405184,-0.09551425,-0.02425463,0.0173357,-0.08270694,-0.00319009,0.03670395,-0.0125217,-0.04391802,0.01605854,0.01174771,0.00828355,0.0077833,0.01900801,0.07632249,0.00752575,-0.01357595,-0.05208237,-0.01207969,-0.00897954,0.05623968,0.02681707,-0.06293169,0.02491987,-0.01959213,0.03876887,0.03898162,-0.00911685,-0.04610588,0.07795411,-0.00257154,-0.02130178,0.14422294,-0.00805852,0.04861391,-0.03767698,0.02626567,0.05192822,-0.04134437,0.00204475,-0.02515836,-0.00531197,-0.06533501,0.07613933,0.06760237,0.03664242,0.02248564,0.02125309,0.01437594,0.02608721,-0.00683263,0.03182538,0.01657681,-0.01181326,0.02668282,0.03283549,0.00059316,-0.25061235,0.01004462,-0.05128609,0.0615965,-0.00159376,0.00607567,0.01148045,0.02253679,0.01298738,0.00349446,0.07068017,0.00432822,0.03883031,-0.09925943,0.0140087,-0.0113843,0.00089759,-0.0526899,0.05148229,0.00818057,0.04132906,0.04008112,0.27896157,0.02662179,-0.00469967,0.02533718,0.00277199,0.01918896,0.03096373,0.05805432,0.00251011,-0.00383941,0.12999338,-0.01812066,-0.01865624,0.00355103,-0.03520789,-0.04019568,0.01090821,0.06865414,-0.05330613,-0.0025069,-0.07890341,-0.01226852,0.13789597,0.03038127,-0.05713031,-0.07901391,0.05186206,0.05854882,-0.06287898,-0.04641461,-0.06472653,-0.00156263,0.06024466,0.03192157,-0.05984858,0.04471125,-0.05185817,0.02085635,-0.02405825,0.00656671,0.05311997,0.05367151,-0.03314967],"last_embed":{"hash":"1pj9oyz","tokens":101}}},"text":null,"length":0,"last_read":{"hash":"1pj9oyz","at":1753423529143},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#{1}","lines":[10,15],"size":250,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10166623,-0.04117008,-0.02676936,-0.0392037,-0.05548368,0.00725837,-0.01743521,-0.00488896,0.02394606,-0.02869186,0.02975281,-0.00066426,0.04675709,0.01233699,-0.0313117,-0.05102411,0.01949719,-0.00144675,-0.04273373,-0.00056437,0.07295389,-0.02558103,-0.06593338,-0.04691984,0.05930898,0.02057174,-0.09000758,-0.05384902,-0.03485431,-0.19633782,0.01394603,0.05695035,0.04843196,-0.05332313,-0.04173157,-0.04327147,-0.01470116,0.02338513,-0.04069638,0.02874888,-0.0060594,0.03570412,-0.02614746,-0.0192772,0.01666031,-0.07595224,-0.02733887,0.03396592,0.03451346,0.01454702,-0.03245904,0.0210524,-0.0033152,0.06206938,0.02187129,-0.00163505,0.01661517,0.06486806,-0.0200801,0.0546466,0.02020035,0.05532176,-0.13250643,0.03010499,0.022961,0.03624121,-0.01332466,-0.0641143,0.02800522,0.03168315,0.03165299,0.06328651,-0.02734577,0.05291237,0.02313394,-0.0536114,-0.01087366,-0.0513457,-0.06199792,0.01044557,-0.09173339,-0.06948026,-0.03182202,-0.00570725,0.01132555,0.04376736,0.04293434,0.02061705,0.06286722,-0.08156899,-0.00302827,0.04095291,0.0455953,0.05043468,0.07430027,0.00327772,0.07604804,-0.03226633,-0.02651652,0.1415873,0.00859265,-0.02185005,-0.02412501,-0.00642829,0.02903005,-0.00776522,-0.03217816,-0.05608901,-0.06498352,0.02648333,0.05096998,0.03990275,0.07709511,-0.05585349,-0.04635867,-0.03309633,-0.04258476,-0.0097989,0.03383794,0.02859381,-0.04023388,0.00687447,0.02003876,-0.01524348,0.0153289,0.0289435,0.02127637,0.05954953,-0.01078977,0.02287107,0.0527885,0.01037632,-0.10648411,-0.01534784,0.02406375,-0.01299182,-0.00804855,0.01710068,0.00131882,0.03425288,-0.04338988,-0.02460804,0.09531493,-0.09514819,0.00836113,0.04177976,-0.02127375,-0.01321091,0.02388749,0.00176281,-0.00153687,0.00701147,-0.04215867,-0.0322509,-0.01025136,-0.04510731,0.10755175,0.06580299,-0.07101413,-0.00936278,-0.02364383,-0.02818489,0.0000624,0.13234863,-0.02072745,-0.10515485,-0.02253493,0.0570561,-0.03429733,-0.05464064,-0.02798336,0.01568155,-0.05404089,0.042059,0.09842335,-0.0271956,-0.08431443,-0.04806331,0.0160441,-0.00537623,0.01378012,-0.00767155,-0.02419047,0.0073297,-0.02549516,-0.07986274,-0.00418486,-0.03824005,0.00466949,0.04523486,-0.04224182,0.01399377,-0.03593941,-0.0678701,-0.01892806,-0.0550797,-0.03941249,-0.03130354,0.0729645,-0.02433097,0.01019241,0.02397903,0.06921559,0.03787003,0.02183474,0.06377467,-0.02881614,-0.06824011,0.10153478,0.03443122,0.00028982,-0.00418247,0.0385222,0.10445626,-0.04886225,0.06197261,-0.01560103,0.0411274,0.02170881,0.02136622,0.00676274,0.0589197,-0.0535486,-0.21952786,0.00136749,-0.00499214,0.00451842,0.00665161,-0.02612457,0.05460454,-0.02263912,0.03545949,0.11566855,0.10748874,-0.05584846,-0.05277236,0.0387756,-0.03627298,0.05428044,-0.10369626,-0.04932433,-0.02846656,0.02835646,-0.00883901,0.04026505,-0.03526212,-0.07525569,0.04177386,-0.00721658,0.1158342,0.01670088,0.03260686,-0.07559326,0.04372644,-0.01676131,-0.03995151,-0.02351708,-0.0174122,0.07560614,-0.02256186,0.00726847,0.01236797,-0.02937908,-0.08881205,0.0084491,-0.00313322,-0.0979948,0.03353518,0.02661649,0.02391655,0.0209675,0.03680706,0.04475245,0.03590315,-0.00012696,0.05008511,0.04795529,0.04287769,-0.01883883,-0.06178987,-0.00527683,0.01312474,0.05980121,-0.00315032,-0.07401142,0.03121375,-0.01813025,0.01563326,0.003872,0.0124455,-0.03386373,0.05578036,-0.01234796,0.00254781,0.08252469,-0.00716491,0.0418601,-0.03541663,0.02275873,0.07700589,-0.02126509,0.02333519,-0.02308197,-0.02283152,-0.05971698,0.05104672,0.06659992,0.0435359,0.02904024,0.03825385,0.05121219,0.00411381,0.00024247,0.05282629,0.03544682,-0.03794593,0.03716203,0.02822177,0.02573265,-0.26274553,0.01048171,-0.04190303,0.04292579,0.02618987,-0.01214048,0.01739273,-0.02087863,-0.01795265,-0.03759961,0.05827648,0.00349707,0.03025856,-0.10282061,-0.00213393,0.00034965,0.03466815,-0.02336192,0.03631613,-0.00526104,0.0473223,0.0554429,0.24210583,0.01162453,-0.01590589,0.03357345,-0.00074228,-0.0012213,0.01769562,0.05198097,0.00153642,0.0097618,0.09731697,-0.06595957,-0.04101152,-0.00166521,-0.01112107,-0.02772566,0.02033594,0.06649077,-0.08070979,0.00551442,-0.074468,-0.03008793,0.09011517,-0.02542618,-0.03930002,-0.04229073,0.03525227,0.08543679,-0.06590518,-0.07882291,-0.06333604,-0.04675026,0.06262387,0.04046524,-0.02700545,0.0312466,-0.03920458,0.01042749,0.0004318,0.00827412,0.03534256,0.01893032,-0.02738483],"last_embed":{"hash":"1fu87we","tokens":116}}},"text":null,"length":0,"last_read":{"hash":"1fu87we","at":1753423529191},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#{6}","lines":[20,22],"size":280,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08744689,-0.03354426,-0.04612716,-0.07413597,-0.06074334,0.0150168,-0.01976814,-0.01478467,0.03959951,-0.04265257,0.02619234,-0.03723805,0.06045279,0.00431495,-0.01489472,-0.0362014,-0.00123358,-0.03768735,-0.06404749,-0.01658739,0.10772965,-0.02920467,-0.05008645,-0.06603777,0.06654162,-0.01371694,-0.05610245,-0.06387833,-0.03977459,-0.2442057,0.03401906,0.04606101,0.0420763,-0.06007767,-0.06742484,-0.048126,-0.01258306,0.05817755,-0.0684792,0.02935009,0.00399183,0.04802296,0.02147125,-0.0539397,0.00943492,-0.04808792,-0.01092658,0.00760735,0.01737725,0.02884933,-0.04294053,0.01585001,0.01184035,0.09182327,0.013029,0.00831436,0.03758224,0.07939782,0.03080009,0.06727247,0.03304887,0.05610686,-0.12656084,0.03030714,0.00456835,0.01018088,-0.02166454,-0.03847835,0.02738619,0.02620071,0.03167577,0.0828142,-0.02553439,0.03447804,0.04085142,-0.03512548,-0.05554315,-0.07084669,-0.05194852,0.00095054,-0.09575478,-0.05065589,-0.01144217,-0.02802858,0.05930077,0.02081726,0.06223945,-0.00240849,0.06582764,-0.05145244,-0.00818701,0.07350668,0.03109418,0.02706301,0.06408947,0.0217017,0.05810553,-0.01489851,0.01114306,0.13395697,0.01892168,-0.00132751,-0.00557578,-0.01112066,0.01268324,-0.04279194,-0.04485694,-0.04068031,-0.07669398,0.02995473,0.02845789,0.01985991,0.07491186,-0.04136036,-0.05529572,-0.02348557,-0.01328663,0.00994408,0.03898526,0.00033486,-0.00878306,-0.01290955,0.00360191,-0.01709943,0.04347211,0.04028949,0.00324026,0.07001459,0.03896317,0.04381442,0.04361822,0.02573792,-0.1138647,-0.04665646,-0.00266969,-0.02270312,-0.01582468,0.0166294,-0.01834232,0.03564265,-0.02903143,-0.02276122,0.07575243,-0.08945774,0.00478313,0.08112648,-0.03829681,-0.01088318,0.01450468,-0.01510776,0.01292878,-0.00690702,-0.05689486,-0.06037962,-0.00501402,0.00851519,0.11316261,0.06653131,-0.06630322,-0.00822022,-0.0085283,-0.03073231,0.00543794,0.11092334,-0.01440917,-0.07217009,-0.0190811,0.03187191,-0.05163813,-0.04613997,-0.02337467,0.0213948,-0.02350031,0.05050015,0.09774368,-0.03918815,-0.072893,-0.03843343,0.00608288,-0.0228517,0.01230494,0.01999246,-0.04925776,-0.01339748,-0.02957582,-0.07938977,0.01370721,-0.01853704,-0.01034383,0.02464966,-0.04599993,0.00262976,-0.02185406,-0.00205323,-0.01392652,-0.02277512,-0.01488516,-0.01685003,0.07832759,-0.03055647,0.0171923,-0.00506078,0.0589847,0.02484307,0.00176664,0.05515406,0.01427605,-0.053525,0.12160704,0.03518024,-0.02550429,-0.01610066,0.04263013,0.10884747,-0.03287908,0.04932129,-0.00839947,0.03794998,0.02085938,0.02458507,-0.00887027,-0.01327195,-0.07795545,-0.2006399,-0.0200976,0.01626308,0.03340423,0.01740987,-0.00772575,0.02802165,-0.05059063,0.03436655,0.10205584,0.09399356,-0.04237614,-0.04138403,0.03646006,-0.02734514,0.02080115,-0.09998509,-0.00408126,-0.03917515,0.0508222,-0.00219281,0.0154777,-0.03012758,-0.07108702,0.03047192,-0.02079912,0.09997007,-0.01240666,-0.0062,-0.06143913,0.04379822,-0.02464396,-0.02164998,0.03168646,0.00755078,0.04507845,-0.02368174,-0.02868073,-0.01180685,-0.00955547,-0.07734447,-0.01050021,-0.01939296,-0.11224059,0.00702638,0.04804721,-0.03188945,0.02338219,0.01027299,0.01349061,0.03000085,-0.02724087,0.0626887,0.01173479,0.0467934,-0.05071374,-0.06202455,-0.02417152,0.02696422,0.03373281,0.01383199,-0.07630231,0.04870478,-0.02061204,0.02865624,0.03999173,0.01751557,-0.02710798,0.03406896,-0.00932275,-0.00833365,0.09957188,-0.02067534,0.04776669,-0.03623531,0.02960839,0.04766382,-0.02793423,0.00918911,-0.05706055,0.02333718,-0.04847734,0.03489193,0.07693161,0.03532949,0.00544049,0.04181165,0.06657523,0.00666011,0.00367841,0.02714261,0.01751327,-0.01520493,0.03074102,-0.01137064,0.01673546,-0.25243035,0.01808817,-0.04944456,0.06575589,0.0148638,-0.02804125,0.01568515,0.00514441,0.02014454,-0.01486495,0.05575109,0.02610829,0.04419002,-0.09820589,0.01367182,-0.00240409,-0.01533248,-0.0617088,0.03419703,-0.00848499,0.07698256,0.03415393,0.27017137,-0.01008189,0.01548595,0.0241271,0.03201361,0.02496293,-0.00410136,0.02231219,0.00942208,-0.00054968,0.13267075,-0.00852918,-0.02891691,0.0043583,-0.01357288,-0.03309168,-0.01445578,0.0196827,-0.04438882,-0.01449468,-0.05276394,0.0006772,0.11923019,0.00635854,-0.07024978,-0.0740259,0.03636833,0.06971031,-0.08421249,-0.0463936,-0.04363861,-0.03282947,0.06436169,0.04286865,-0.00438721,0.0343129,-0.03180678,0.00725066,-0.016163,0.00650415,0.04645908,0.02295933,-0.01627439],"last_embed":{"hash":"1sqhg1x","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1sqhg1x","at":1753423529234},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#{7}","lines":[23,52],"size":2770,"outlinks":[{"title":"Pick lottery software divides the 0 to 9 digits in odd, even, low, high, sums, frequency groups.","target":"https://saliu.com/ScreenImgs/pick-4-lottery-groups.gif","line":1},{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":25},{"title":"The main menu of UserLottoGroups, lotto software for odd / even, low / high, sums, sum-totals.","target":"https://saliu.com/ScreenImgs/UserGroups6.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09777443,-0.04394256,-0.02388845,-0.07236236,-0.03302056,-0.01759933,-0.06807446,-0.00949238,0.01500803,-0.04144209,0.02652188,-0.04703282,0.04794901,0.00323567,-0.01447168,-0.05792439,-0.01767152,-0.04877887,-0.10067637,0.01802772,0.1064554,-0.00842716,-0.03325308,-0.05888387,0.06288042,0.04071452,-0.07111278,-0.0446474,-0.00748329,-0.22273354,0.02822436,0.05761365,0.0799255,-0.02502969,-0.02856013,-0.05751818,-0.02793217,0.04074951,-0.03210058,0.02357075,-0.00780312,0.02886351,-0.01551297,-0.03415234,0.03599646,-0.03882933,-0.0208289,0.03405459,0.03812991,0.00777774,-0.03384291,0.03174024,0.00213187,0.07497294,0.02547644,-0.01596466,0.02179222,0.06718922,0.03781161,0.06497151,0.02354385,0.03325215,-0.12615581,0.02898957,0.02220523,0.0111418,-0.0279965,-0.04084273,0.01419671,0.00556674,-0.00419176,0.01711294,-0.02931914,0.04896216,0.05298611,-0.05840303,-0.02456376,-0.05466895,-0.04776164,-0.01734705,-0.08152372,-0.0617497,-0.03238782,0.01898174,0.03677672,0.01411517,0.08941708,0.03371258,0.05570164,-0.05560729,-0.00026891,0.06577639,0.03992573,0.02675606,0.07168391,-0.01701817,0.03720781,0.03052073,0.00623851,0.12953615,-0.01536759,-0.02140388,0.04959805,-0.02360537,-0.00590013,-0.0494679,-0.02545574,-0.03956277,-0.07174092,0.00014797,0.02054481,0.03611176,0.06460128,-0.05840442,-0.01739492,-0.07571374,-0.00063924,0.04269772,0.00106762,-0.01718043,-0.003096,-0.00077829,0.01200075,-0.02007996,0.04124095,0.01663182,0.015671,0.05290196,0.03727358,0.02613526,0.03054024,-0.00199357,-0.08220042,-0.05075913,0.0076228,-0.01164416,-0.02319686,0.03383783,-0.00006127,-0.02300639,-0.00478841,-0.02911512,0.02127639,-0.08907065,0.00099089,0.07148761,-0.05640149,-0.03322657,0.03134636,-0.00561619,-0.0061446,0.01110077,-0.03658088,-0.0638911,0.00707833,-0.01581442,0.13100018,0.07973129,-0.04372993,0.0044153,-0.00856467,-0.01912444,-0.00464684,0.12925337,0.01174954,-0.04863407,-0.0418166,0.03970505,-0.02293669,-0.04708833,0.00306287,0.01765886,-0.01036079,0.05619008,0.10166959,-0.04988348,-0.04090854,-0.0709618,-0.00199607,-0.00177941,-0.0201868,0.00939117,-0.03194902,-0.00139817,-0.03199785,-0.07472949,-0.0089779,0.01350683,-0.00307914,0.06272425,-0.01138391,0.0079688,-0.0620272,-0.00973409,-0.02589681,-0.01383314,-0.01947221,0.00617631,0.03904646,-0.04329787,0.08014797,0.00526333,0.05171058,0.01622674,0.00910258,0.05260887,0.03018202,-0.03152728,0.12305846,0.02546206,-0.01857777,-0.0157355,0.05192649,0.08072574,-0.0105271,0.04207652,0.0048612,0.05690328,0.05393027,0.05118958,-0.00295435,-0.04970049,-0.07512078,-0.20937036,-0.01375284,0.01568889,-0.02115534,0.00004,-0.0133332,0.02781852,-0.04476437,0.0473378,0.06808158,0.09409232,-0.04100676,-0.05253931,0.00316252,-0.05734992,0.02554272,-0.0907097,-0.00208687,-0.02842518,0.09504332,-0.017921,0.04348496,-0.04529726,-0.04515233,0.00363102,-0.01546608,0.13491583,0.02821837,0.04885005,-0.0820255,0.03766653,-0.01506048,-0.02759841,0.00324182,0.02083619,0.01918554,-0.0706754,-0.00154124,-0.0138736,-0.04172545,-0.07312945,-0.03722766,-0.001952,-0.08029515,0.01754491,0.00940849,-0.01621103,-0.01781107,0.03089404,0.00872619,0.04435567,-0.01634212,0.05212557,0.02606017,0.02391476,-0.00787926,-0.07962921,-0.02251139,-0.00262604,0.01361175,0.01044532,-0.06813959,0.04893445,-0.01995797,0.01857465,0.05311598,-0.00976302,-0.02297557,0.03764628,-0.01096357,-0.01121731,0.11754263,-0.03044343,0.02574903,-0.05862947,0.00830521,0.03631736,-0.01251573,0.0005488,-0.03368773,-0.01723828,-0.06659114,0.05685735,0.04978445,0.00567862,0.01588355,0.00371357,0.02881663,0.02120031,-0.00070308,0.05599557,-0.00845771,-0.00386274,0.05108465,0.00196809,-0.00274664,-0.27833733,0.02152447,-0.04630426,0.02932874,0.00317391,0.03162771,0.00051192,0.04059208,0.00456053,0.01060083,0.05202888,0.02692037,0.07044511,-0.12339479,-0.0162941,-0.00680409,-0.00834939,-0.05453166,0.04147178,0.01151108,0.05219899,0.02781343,0.28096581,0.01746346,-0.00753754,0.03069631,0.00353795,0.03540998,0.04361778,0.04056993,0.00341065,0.00088463,0.11732477,-0.03982511,0.00237902,0.00143651,-0.01778802,-0.02502865,0.03588731,0.06290364,-0.03909241,-0.02105051,-0.08278852,-0.00041652,0.1125,0.02395014,-0.03562158,-0.05085684,0.04698699,0.04936122,-0.06661604,-0.02731656,-0.0598278,-0.01143017,0.04602954,0.03577766,-0.05177636,0.04542652,-0.0587148,0.0278368,-0.02217544,-0.02224475,0.0648191,0.05242137,-0.03246973],"last_embed":{"hash":"7jmd5c","tokens":168}}},"text":null,"length":0,"last_read":{"hash":"7jmd5c","at":1753423529403},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>","lines":[53,59],"size":340,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09478658,-0.04618798,-0.02142316,-0.07291123,-0.03150519,-0.01663468,-0.0726563,-0.00995619,0.01368553,-0.04243807,0.02476569,-0.0483906,0.05150981,0.00330024,-0.01255086,-0.05891992,-0.0201777,-0.04848541,-0.10298368,0.01695858,0.10645177,-0.00670272,-0.0334505,-0.05712784,0.05990412,0.04151536,-0.06802277,-0.04609227,-0.0089532,-0.22239172,0.03011892,0.05621484,0.08056362,-0.02315796,-0.0290399,-0.05762717,-0.02786818,0.03883826,-0.03027638,0.02512629,-0.00788198,0.02915535,-0.01315931,-0.03582453,0.03558439,-0.03786174,-0.02186901,0.03299598,0.03647806,0.00711272,-0.03111786,0.03253362,-0.00061077,0.0776955,0.02446459,-0.01906957,0.021407,0.06581637,0.03791057,0.06527404,0.02389717,0.03529385,-0.12949827,0.03171627,0.01690681,0.00937222,-0.02691296,-0.03943123,0.01692775,0.00527782,-0.00612933,0.01708279,-0.02740265,0.05144271,0.05188667,-0.05637587,-0.02457429,-0.05722754,-0.04767874,-0.01289651,-0.08223815,-0.05859806,-0.03200808,0.01856461,0.0349232,0.01131825,0.08810304,0.03197961,0.05446463,-0.05216288,-0.00022152,0.06654154,0.03774996,0.02929903,0.07029792,-0.02070638,0.03335587,0.03228264,0.00590178,0.1267958,-0.01857243,-0.0203978,0.05472523,-0.02311422,-0.00522035,-0.04925259,-0.02872183,-0.0393,-0.07474416,0.00394014,0.02154039,0.03551621,0.06013152,-0.05795999,-0.01555832,-0.07477004,-0.00115232,0.0434648,-0.00194889,-0.01911957,-0.00450999,-0.00265092,0.01121459,-0.02068595,0.04120919,0.01527234,0.01777675,0.05416442,0.03659144,0.02404498,0.03267138,-0.00220645,-0.08486652,-0.05239528,0.00712884,-0.01137561,-0.02290954,0.03062049,-0.00131415,-0.02378126,-0.00163209,-0.02818464,0.02278351,-0.09064637,-0.00174639,0.07493705,-0.05407906,-0.03269062,0.02762562,-0.00658433,-0.0071858,0.00977995,-0.03804862,-0.06550592,0.00547247,-0.01331166,0.13023272,0.07877932,-0.04237432,0.00434828,-0.01085735,-0.01875871,-0.00292892,0.12992735,0.00822438,-0.0489627,-0.04264362,0.04141602,-0.02394004,-0.04778407,0.00413545,0.01900761,-0.00727346,0.05594189,0.10154311,-0.04895544,-0.04289021,-0.07017756,-0.00567017,0.00029155,-0.01833739,0.01200254,-0.03020964,-0.00145194,-0.03248288,-0.07519698,-0.00726348,0.01677164,-0.0020228,0.06019124,-0.01236535,0.01067572,-0.06024184,-0.00896709,-0.0263666,-0.01241503,-0.01818927,0.00493658,0.03659799,-0.04622896,0.07944999,0.00524257,0.04979624,0.01113337,0.01018681,0.05270999,0.02952222,-0.02920848,0.12227012,0.02427984,-0.01954793,-0.01604871,0.05274714,0.08042046,-0.0095581,0.04181897,0.00512064,0.05379808,0.0526537,0.05028788,0.00042242,-0.0469095,-0.07213096,-0.2100977,-0.01481285,0.01537917,-0.02081498,-0.00195664,-0.01329851,0.02463121,-0.04377564,0.04985188,0.06667771,0.09526651,-0.03705468,-0.05043938,0.00852598,-0.05768743,0.02759258,-0.089826,-0.00056185,-0.03005562,0.09449111,-0.0182025,0.04355906,-0.04437668,-0.04622597,0.00665798,-0.0123679,0.13568644,0.02661336,0.04604061,-0.08235314,0.0379127,-0.01216989,-0.02602503,0.00246402,0.02238184,0.01853536,-0.06920581,-0.0057055,-0.00852927,-0.04120263,-0.07116231,-0.0357004,-0.00173782,-0.08253675,0.01859936,0.00922903,-0.01576564,-0.01482558,0.03127949,0.00794738,0.04447759,-0.01787057,0.05436328,0.0262897,0.0229666,-0.00910812,-0.08191366,-0.02182959,0.00000643,0.01024047,0.01170627,-0.06854354,0.04969975,-0.01978778,0.01776365,0.05072108,-0.01047221,-0.02126116,0.03951954,-0.00969006,-0.01268773,0.11448333,-0.03447224,0.02696148,-0.06065551,0.00582239,0.03312717,-0.0164284,0.00055756,-0.03533483,-0.01663567,-0.06450199,0.05594469,0.05210734,0.00770471,0.01652307,0.0061713,0.03001934,0.02162558,-0.0016501,0.05715154,-0.00972171,-0.00311899,0.05310856,0.00447728,-0.00346184,-0.2802318,0.0196825,-0.0482928,0.02876223,0.00305032,0.02964228,0.00092492,0.04366868,0.00435913,0.0098674,0.05203756,0.02904487,0.07085731,-0.12602088,-0.01387028,-0.00430832,-0.00663734,-0.05505697,0.04350858,0.0090058,0.05316835,0.02746095,0.2816723,0.01529323,-0.00632612,0.03357252,0.00200956,0.03605825,0.0416708,0.04171091,0.00365934,0.00106262,0.11808258,-0.0408612,0.00311695,0.00141018,-0.01682664,-0.02850659,0.03427112,0.06224203,-0.03863458,-0.02055644,-0.08156606,-0.00241314,0.11390805,0.02696637,-0.03358998,-0.05112761,0.04340111,0.05079487,-0.06473821,-0.02578993,-0.06484766,-0.01129878,0.0474876,0.03584268,-0.05207234,0.04168477,-0.05675801,0.02930014,-0.01967991,-0.01729729,0.06380896,0.05370126,-0.03218943],"last_embed":{"hash":"181wanz","tokens":167}}},"text":null,"length":0,"last_read":{"hash":"181wanz","at":1753423529459},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>E = Generate <i>Odd/Even</i> Groups</u>#{1}","lines":[55,59],"size":293,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09456199,-0.03828166,-0.0358716,-0.05977887,-0.03821884,-0.02217439,-0.06408355,-0.00622306,0.02685763,-0.04602837,0.03059946,-0.04779681,0.05739805,0.01035142,-0.01868642,-0.0658262,-0.00524603,-0.04912366,-0.09707978,0.0127787,0.10688093,-0.0136412,-0.04329098,-0.05161633,0.07683336,0.02283053,-0.08008842,-0.04145455,-0.02031186,-0.22474071,0.02554462,0.05896038,0.0914394,-0.02898986,-0.02865496,-0.05282563,-0.02436402,0.04236695,-0.03696943,0.02628989,-0.00448359,0.02111854,-0.01420843,-0.02771479,0.03749981,-0.05592412,-0.01591982,0.0241983,0.0417762,0.00935044,-0.03927949,0.02983751,-0.00395039,0.066914,0.02242712,-0.02478651,0.03128009,0.06904836,0.0426739,0.06310116,0.02434436,0.03283528,-0.1406737,0.02815218,0.02165274,0.01299331,-0.02950717,-0.04286594,0.00835695,0.00803108,-0.00253926,0.01873071,-0.01247102,0.03695725,0.04712432,-0.07412814,-0.02850007,-0.05533317,-0.04376318,-0.01564922,-0.05213895,-0.05908597,-0.03156993,0.01700482,0.02001093,0.00783093,0.09282205,0.03623363,0.0513144,-0.06157751,0.00539079,0.06150802,0.03608744,0.03049716,0.046808,-0.00673344,0.03620748,0.02659643,-0.00237635,0.13610204,-0.01575494,-0.02465443,0.05712129,-0.01514535,-0.0027975,-0.05629094,-0.02772732,-0.02965432,-0.07339675,-0.00475317,0.01995331,0.03689132,0.05968049,-0.05527104,-0.01461341,-0.08668452,0.00021127,0.0382565,-0.01154475,-0.0142961,-0.00500612,0.00442745,0.00764322,-0.0364962,0.04944951,0.00857327,0.02262008,0.05017337,0.04871025,0.04082139,0.03856826,0.0109214,-0.07642266,-0.05473065,0.01344904,-0.02039442,-0.02627588,0.03633128,-0.00537632,-0.03156447,-0.01404143,-0.02537614,0.02600791,-0.08178762,0.01548762,0.07127547,-0.0632196,-0.02475054,0.03457233,-0.00232566,-0.00739569,0.00859974,-0.04345144,-0.06733537,0.00891397,-0.02526212,0.13060132,0.07545586,-0.04247013,0.01585606,-0.00639457,-0.01663072,-0.01345155,0.12281668,-0.00143818,-0.0378821,-0.03605716,0.04415315,-0.02142455,-0.04049266,0.00272996,0.02569718,-0.0153954,0.05132048,0.10666165,-0.04266486,-0.04627553,-0.06545212,-0.01021919,-0.0078731,-0.02228144,0.00970414,-0.0365001,-0.00451625,-0.02608985,-0.06540616,-0.02205727,0.01723946,0.00276316,0.07014016,-0.02399213,0.00690107,-0.06207177,-0.01506655,-0.02718086,-0.00590673,-0.03040931,-0.00542315,0.03797394,-0.03006185,0.07034881,0.01025958,0.04443294,0.01369515,0.0169403,0.05440133,0.03394208,-0.03510767,0.1224587,0.02312489,-0.01103325,-0.01387705,0.05619002,0.08049063,-0.01258109,0.04094964,0.0051418,0.05290748,0.02630574,0.04621126,0.00491857,-0.04521568,-0.07143597,-0.21814069,-0.00346811,0.01851114,-0.02115881,0.00116428,-0.00876707,0.0218885,-0.02635258,0.04465318,0.07707634,0.11251726,-0.04816677,-0.06223462,0.00761235,-0.05672348,0.01903762,-0.08823257,-0.00211006,-0.01914431,0.08795987,-0.01802087,0.0338059,-0.03416817,-0.05296468,0.00497712,-0.00566277,0.13499178,0.02482046,0.04722251,-0.07141078,0.03995639,-0.02188877,-0.02222762,0.00460352,0.01705203,0.01202433,-0.0721465,0.0081905,-0.0176535,-0.0496378,-0.07834843,-0.02795454,0.00210339,-0.08854005,0.01761584,0.0132648,-0.01463631,-0.01100077,0.02032942,0.00255395,0.03796977,-0.01366203,0.0329061,0.03846994,0.02919092,-0.01577723,-0.09850194,-0.0239076,-0.00049456,0.00828409,0.0008959,-0.06909296,0.06064121,-0.01361172,0.01865639,0.04530061,-0.01167481,-0.02160887,0.02602748,-0.00494795,-0.00828806,0.10075672,-0.03099378,0.03735638,-0.0510571,0.01507095,0.02544944,-0.01350716,-0.00327287,-0.02867765,-0.0025328,-0.06402002,0.05438515,0.05140121,0.02172286,0.01694127,0.015392,0.02747273,0.01539855,-0.00071679,0.04976244,-0.00204681,-0.00402822,0.0585471,-0.00493208,-0.00683715,-0.28141278,0.01683351,-0.03400023,0.02366577,0.00014313,0.02455384,0.01262355,0.03740612,0.00765877,0.02281578,0.0602523,0.02864145,0.07767121,-0.11358768,-0.0178195,-0.01959146,-0.01822795,-0.05399567,0.03480567,0.01138013,0.04402381,0.02674202,0.27243397,0.01521134,-0.00795956,0.03238283,0.00005253,0.0316122,0.03063396,0.04771657,-0.01229988,0.01730781,0.13218115,-0.03191718,-0.00400822,0.01811616,-0.02126349,-0.02784088,0.04070448,0.07382241,-0.03517697,-0.01193897,-0.08362443,0.00449965,0.11411512,0.01386393,-0.03734468,-0.0545177,0.06114933,0.04834572,-0.06691297,-0.02384218,-0.05111844,-0.00446715,0.04273425,0.03475771,-0.03682308,0.04729865,-0.04609791,0.03146217,-0.01903803,-0.02761552,0.06663235,0.04104472,-0.02860782],"last_embed":{"hash":"4imoqq","tokens":168}}},"text":null,"length":0,"last_read":{"hash":"4imoqq","at":1753423530061},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>","lines":[60,66],"size":343,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09262984,-0.04088756,-0.03317903,-0.05969523,-0.03597717,-0.02189173,-0.06668814,-0.00698519,0.02531779,-0.04484776,0.02878175,-0.04913637,0.06143754,0.00951628,-0.01793384,-0.06609376,-0.00824249,-0.04880809,-0.09880523,0.01207352,0.10593911,-0.01199687,-0.04412467,-0.05212075,0.07402427,0.02321219,-0.07748266,-0.04215227,-0.0224229,-0.22483693,0.0275838,0.05818865,0.09149656,-0.02819602,-0.02934985,-0.05291743,-0.02408231,0.03909415,-0.03534682,0.02674266,-0.00491038,0.02289735,-0.01294833,-0.02911434,0.03774266,-0.05499579,-0.01857094,0.02153458,0.03986875,0.01040095,-0.03615985,0.03157368,-0.00687147,0.06884691,0.02185376,-0.02734487,0.0326975,0.06735293,0.04204035,0.06430765,0.0250536,0.03377241,-0.14244527,0.03090808,0.0166917,0.01015599,-0.02903911,-0.04115599,0.01133575,0.00812075,-0.00235241,0.01880614,-0.01082277,0.03823896,0.04633894,-0.07278185,-0.0296308,-0.06027824,-0.04178056,-0.01094931,-0.05124998,-0.05631745,-0.03137527,0.01550132,0.01868556,0.00248286,0.09270623,0.03396364,0.04835598,-0.05876908,0.00561902,0.06317122,0.03178024,0.03074687,0.0463632,-0.0091433,0.03177051,0.02830196,-0.00276103,0.13406877,-0.01998797,-0.02471679,0.06212912,-0.0146852,-0.00304022,-0.05579752,-0.0281477,-0.0271976,-0.07508079,-0.00114586,0.01971955,0.03516299,0.05403762,-0.05441371,-0.01417913,-0.08466751,0.00054776,0.03666676,-0.01480444,-0.01654121,-0.0055119,0.00428186,0.00650424,-0.03853982,0.04871166,0.00741344,0.02526888,0.05018738,0.04909603,0.03947353,0.04007927,0.0104885,-0.07776702,-0.05650717,0.01215688,-0.02166446,-0.0247413,0.03318781,-0.00707997,-0.03137296,-0.01119544,-0.0234877,0.02826385,-0.08348779,0.01485421,0.0733799,-0.06361952,-0.02387288,0.03054192,-0.00235766,-0.00941025,0.00608498,-0.04540094,-0.06881424,0.00759149,-0.02357747,0.13069698,0.07544897,-0.03976668,0.01544074,-0.00874723,-0.0149963,-0.01275048,0.12309643,-0.004371,-0.03761878,-0.03729802,0.0456496,-0.02156147,-0.03861754,0.00408616,0.02814086,-0.01333357,0.05279507,0.10743196,-0.04246743,-0.04979034,-0.06353231,-0.01538917,-0.00614622,-0.02023919,0.01178306,-0.03628933,-0.00476729,-0.02575324,-0.06665612,-0.02169968,0.02017049,0.00383995,0.06797588,-0.02579314,0.0109926,-0.06130414,-0.01490799,-0.0252345,-0.00443455,-0.02929021,-0.00506859,0.03670185,-0.03258685,0.07035998,0.01151849,0.0423789,0.00922808,0.01816252,0.0551909,0.03333707,-0.03375554,0.12033083,0.02178055,-0.01139308,-0.01400292,0.05683496,0.08046638,-0.01026621,0.03913187,0.00409165,0.05157671,0.0247519,0.04679494,0.00782305,-0.04293432,-0.06934674,-0.21997176,-0.00325829,0.02034215,-0.02088595,-0.00220463,-0.00681841,0.02092071,-0.02490029,0.04821583,0.07543517,0.11395358,-0.04366786,-0.06297729,0.01193065,-0.05556816,0.0213359,-0.08887723,0.00021172,-0.01981754,0.08767458,-0.01783873,0.03269128,-0.03312302,-0.05288355,0.00669117,-0.00257689,0.13528343,0.0230743,0.0428934,-0.07125708,0.03991827,-0.02059032,-0.02050393,0.00475199,0.01973714,0.01097853,-0.07110402,0.00436651,-0.01298138,-0.05166669,-0.07655598,-0.02807339,0.00005559,-0.09134635,0.01842888,0.01172638,-0.01376065,-0.00675004,0.02081187,0.00174676,0.03799083,-0.01496693,0.03312271,0.03913912,0.02743732,-0.01869454,-0.10179252,-0.02376732,0.0008252,0.00639225,0.00188969,-0.06896892,0.06082188,-0.0127982,0.020098,0.04192099,-0.01155704,-0.01901796,0.0269421,-0.00306435,-0.01010203,0.09855159,-0.03432849,0.03940548,-0.05117911,0.01348528,0.02326905,-0.01595163,-0.00110628,-0.02890355,-0.00209501,-0.06212072,0.05341038,0.05244501,0.02427299,0.01634932,0.01698337,0.02761251,0.01539956,-0.00184493,0.05019479,-0.00302195,-0.00287171,0.06126468,-0.00340371,-0.00773386,-0.28334439,0.01369085,-0.03513792,0.02319089,0.00016896,0.02217489,0.01386149,0.04011139,0.00844344,0.02403596,0.06092156,0.02975726,0.07957932,-0.11460721,-0.01649863,-0.01753221,-0.01788743,-0.05427084,0.03607628,0.01056949,0.0447765,0.0266335,0.27373666,0.0145669,-0.00580586,0.03465231,-0.00224281,0.03229618,0.0276088,0.04649035,-0.01258756,0.01772564,0.13347943,-0.03354628,-0.00427278,0.02007431,-0.01850132,-0.0311019,0.04105218,0.07308402,-0.03395306,-0.01175936,-0.08303946,0.00058703,0.11485261,0.0156398,-0.03606486,-0.0532314,0.05933174,0.04960755,-0.06557421,-0.02274467,-0.05606286,-0.00280577,0.04243229,0.03378258,-0.03614065,0.04397091,-0.04335235,0.03290498,-0.01757368,-0.02054794,0.06611339,0.04233943,-0.02808793],"last_embed":{"hash":"77toi3","tokens":167}}},"text":null,"length":0,"last_read":{"hash":"77toi3","at":1753423530469},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>L = Generate <i>Low/High</i> Groups</u>#{1}","lines":[62,66],"size":296,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08344645,-0.04917246,-0.02891578,-0.06694345,-0.02280145,0.01213177,-0.07561052,-0.00565283,0.02782958,-0.04514011,0.0374952,-0.04731744,0.04835951,-0.0217274,-0.02605216,-0.04965879,0.00789563,-0.04790011,-0.07962927,-0.00715123,0.10474965,-0.01040724,-0.03957086,-0.03640072,0.06558522,0.02142695,-0.06389081,-0.05164256,-0.03259818,-0.2130494,0.03198863,0.06821198,0.11013962,-0.03713154,-0.02835755,-0.05374127,-0.01901937,0.04000416,-0.0437608,0.04530778,-0.01066371,0.03869995,0.00994216,-0.01907061,0.00927806,-0.02411946,-0.03586452,0.01555545,0.03197493,0.01651218,-0.04816456,0.0014739,-0.0077326,0.08106972,0.00881785,-0.02423133,0.04211601,0.02362209,0.05034228,0.06016576,0.02528055,0.03154735,-0.1617813,0.04650591,0.01573739,0.01153559,-0.00386877,-0.0290117,0.01521608,0.00761132,-0.04110146,0.03180515,-0.01418049,0.04223047,0.05743866,-0.04767025,-0.03215933,-0.05766638,-0.05766783,-0.02126961,-0.06040571,-0.05922069,-0.03484889,0.00313186,0.03036611,0.00612906,0.08224346,0.03355932,0.04267471,-0.03114882,-0.00912897,0.07687648,0.02740786,0.03107845,0.03538422,-0.02904464,0.03633349,0.03268874,-0.00112111,0.12918212,-0.01030983,-0.02871773,0.05181721,0.00345959,-0.02437991,-0.02887123,-0.03873673,-0.01442696,-0.05991559,0.01344803,0.03770333,0.04670224,0.05492453,-0.0937862,-0.02507926,-0.07985348,0.00190624,0.01463103,0.01189515,-0.0133453,-0.00274951,-0.00704529,0.00984515,-0.0113781,0.072074,0.03231498,0.01958594,0.06015247,0.05742272,0.02484196,0.01458714,0.00956567,-0.09012193,-0.03299623,0.0006521,0.00283688,-0.02743292,0.0214387,-0.02108076,-0.00718068,-0.01736293,-0.03214048,0.04212075,-0.08944217,0.01275525,0.06476961,-0.05230743,-0.00461858,0.01522855,0.02151398,0.00035055,0.01943748,-0.04109433,-0.05757135,0.00367229,0.0021728,0.12912764,0.05920251,-0.04922897,0.00792813,-0.0278452,-0.01980702,0.00171841,0.13070223,-0.00602396,-0.05167835,-0.05009525,0.03651668,-0.03693626,-0.07100883,0.00157486,0.00869449,0.01140183,0.05805473,0.0990678,-0.02523559,-0.03145033,-0.0359955,0.00517329,-0.01200817,-0.01140308,0.02054522,-0.03541378,-0.00523097,-0.03401506,-0.07234218,-0.00500063,0.01621049,0.01140727,0.05227792,-0.02098238,0.00036996,-0.028154,-0.01717171,-0.0378723,-0.03226136,-0.02855544,0.014637,0.04411383,-0.04604746,0.07599365,0.00684363,0.05815771,0.01897272,0.01169543,0.05157271,0.01137593,-0.02248284,0.12103871,0.03045025,-0.00382395,0.00241374,0.06616137,0.06837984,-0.02593882,0.03526544,-0.00508163,0.03630711,0.03350499,0.05893978,0.00227019,-0.03892589,-0.09839942,-0.21909793,-0.01249946,0.03586888,0.01049449,0.00332508,-0.0124032,0.03336823,-0.02948294,0.05419814,0.07114641,0.08391275,-0.03624888,-0.06820799,0.01306997,-0.05099489,-0.00074431,-0.0749009,-0.02029619,-0.0226384,0.07830638,-0.03130825,0.03747389,-0.0621372,-0.06016569,0.04180446,-0.02136544,0.1286139,0.00298628,0.02458966,-0.07006808,0.05910666,-0.02069093,-0.01500324,0.02676033,0.01385926,0.0108254,-0.04180389,0.01309349,-0.00611624,-0.04237777,-0.08241603,-0.03090063,0.01863175,-0.09185157,0.00078121,-0.00099594,-0.00587591,-0.02375206,0.00448412,-0.01438112,0.04678559,-0.0191113,0.03939598,0.03454921,0.02427373,-0.01949231,-0.09796032,-0.01089353,-0.02683712,0.02071693,0.01481853,-0.06206505,0.06253608,-0.01543972,-0.00937374,0.05598611,-0.00942176,-0.01541746,0.03908702,0.00217595,0.0088035,0.11174764,-0.02798277,0.01406655,-0.05188162,-0.00618562,0.01494924,-0.03832822,0.00808534,-0.03766137,0.00733982,-0.03895872,0.041223,0.03098341,0.01325225,0.02270897,0.02578782,0.0538788,0.02955868,0.00456226,0.04849282,0.02887296,0.01016801,0.04392429,0.03051552,-0.00070156,-0.28455028,-0.00974254,-0.07556957,0.06070183,-0.00627469,0.01153119,0.01036692,0.04941947,0.00516876,0.02344575,0.03768687,0.02820012,0.05321117,-0.11979772,0.02922337,-0.01095471,-0.00672412,-0.04675121,0.03924792,-0.01234725,0.03675331,0.02690895,0.27496073,0.00357303,-0.00784222,0.02958621,0.02085752,0.01639406,0.03045675,0.0365671,0.00726953,-0.02038941,0.13210233,-0.04524846,-0.00888695,-0.00206881,-0.0103721,-0.03204032,0.00168216,0.05146438,-0.03288297,-0.03408188,-0.06652746,0.00144727,0.12519217,0.01450326,-0.04704738,-0.07760525,0.03488186,0.05947915,-0.07183537,-0.01785899,-0.08148087,-0.0129978,0.0508871,0.03705607,-0.02571409,0.04931446,-0.01480164,0.02467576,-0.02106903,-0.03241645,0.07009835,0.04595532,-0.04219978],"last_embed":{"hash":"18n8ryg","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"18n8ryg","at":1753423530547},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>","lines":[67,74],"size":392,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08190158,-0.05228574,-0.02795836,-0.06703177,-0.01939919,0.0144813,-0.07765377,-0.00449969,0.02750299,-0.04462083,0.03815027,-0.04923366,0.04919935,-0.021598,-0.02536393,-0.0505003,0.00496699,-0.04921649,-0.07911276,-0.00639526,0.10700908,-0.00994641,-0.03849288,-0.03787064,0.06379507,0.02165149,-0.06069964,-0.05123007,-0.03281938,-0.21080151,0.03298402,0.06736487,0.11155531,-0.03769746,-0.02705215,-0.05763841,-0.01893668,0.03855826,-0.04252437,0.04718029,-0.01159024,0.03704002,0.01303172,-0.0223944,0.00912784,-0.02201481,-0.03793862,0.0137828,0.03190578,0.01739007,-0.04659707,0.00432215,-0.01120843,0.08155261,0.00823103,-0.02516137,0.04613492,0.02423488,0.04890228,0.05981084,0.02478743,0.03258404,-0.16466309,0.05018716,0.01289833,0.01039887,-0.00343682,-0.02828932,0.01792362,0.00890537,-0.04329207,0.03269425,-0.01439955,0.04318848,0.05701442,-0.04751336,-0.03392119,-0.05957852,-0.05594997,-0.01552968,-0.0602078,-0.05576694,-0.03525297,0.00184303,0.02788431,0.00324586,0.08083452,0.0311948,0.04130621,-0.02916982,-0.01046155,0.07664263,0.02376842,0.03306494,0.03591212,-0.03208743,0.03410664,0.03365394,-0.0000373,0.12696624,-0.01259316,-0.02825635,0.0550422,0.00383768,-0.02295049,-0.02866968,-0.04244993,-0.01518913,-0.06242374,0.01437623,0.03939389,0.04781621,0.05174357,-0.09450742,-0.02475082,-0.07652701,0.00227196,0.01323573,0.01142061,-0.01605173,-0.00466607,-0.0070731,0.00841229,-0.0118225,0.07279311,0.0301326,0.02314023,0.05965996,0.05753381,0.02555466,0.01486052,0.00581503,-0.09181266,-0.03507626,-0.00043275,0.0044544,-0.02624484,0.01817977,-0.02326164,-0.00889467,-0.01608684,-0.03234511,0.04232601,-0.08927516,0.01174994,0.0676422,-0.04911806,-0.00536186,0.01496022,0.02017239,0.00153939,0.02036917,-0.03986115,-0.05993357,0.00215614,0.00450762,0.12771124,0.05748887,-0.04739794,0.00903701,-0.02922922,-0.02032698,0.00359534,0.1291963,-0.00968475,-0.05020546,-0.05178462,0.03731419,-0.03784752,-0.0729387,0.00261637,0.00974835,0.01194214,0.05725384,0.10025048,-0.02578371,-0.03294233,-0.03625008,0.00211681,-0.01026305,-0.01125348,0.02179776,-0.0322874,-0.00558046,-0.03383899,-0.07369632,-0.004547,0.01747695,0.01310316,0.05053444,-0.02256901,0.00293918,-0.0279833,-0.01659654,-0.03959728,-0.02920222,-0.02905172,0.0132181,0.04450131,-0.04741739,0.07866211,0.00789533,0.05568342,0.01643184,0.01344301,0.05219128,0.01251493,-0.01970294,0.12033067,0.03086853,-0.00405968,0.00314346,0.06703433,0.06605326,-0.02446944,0.03475492,-0.00488744,0.03596928,0.0324253,0.05628229,0.00531434,-0.03840345,-0.09628206,-0.21879362,-0.0144579,0.03395672,0.00832774,0.00482042,-0.01137647,0.03151683,-0.03069877,0.05548673,0.06967198,0.08281653,-0.03419237,-0.06713867,0.01691831,-0.0498196,-0.00004778,-0.07379869,-0.02084592,-0.02378448,0.0789526,-0.03338931,0.03730621,-0.06205232,-0.05858966,0.04338047,-0.01965385,0.12825057,0.00015948,0.02371573,-0.0707681,0.05963754,-0.02005938,-0.01271077,0.02911642,0.01695577,0.00820994,-0.04065042,0.0085504,-0.00465132,-0.04273817,-0.08142415,-0.02992688,0.02003423,-0.09062319,-0.00148063,-0.00183255,-0.00777678,-0.02538549,0.00472204,-0.01229383,0.04782465,-0.01911101,0.04026722,0.03576552,0.02373821,-0.02165268,-0.09743451,-0.00965666,-0.02614904,0.01931133,0.01510365,-0.06317988,0.06321784,-0.01426036,-0.00928713,0.05208509,-0.01162595,-0.01439788,0.04088932,0.00630813,0.00930956,0.11304665,-0.02985943,0.01479671,-0.05024903,-0.01087494,0.0148779,-0.03740551,0.00754731,-0.03801304,0.00823223,-0.03768834,0.04216294,0.03017614,0.01458316,0.0223067,0.03060705,0.05449124,0.02906673,0.00473059,0.04785649,0.02504681,0.01389062,0.04330835,0.03201414,-0.00269389,-0.28686014,-0.01234816,-0.07586268,0.0602061,-0.00685287,0.00912135,0.01208051,0.05182047,0.00212761,0.02434661,0.03530514,0.02765324,0.05471135,-0.12040428,0.03093898,-0.00948652,-0.00653144,-0.04690136,0.03993715,-0.01081399,0.03811096,0.02658811,0.2749939,0.0011791,-0.00577625,0.03119693,0.02052058,0.01915308,0.02839325,0.03757155,0.00957421,-0.02073184,0.1309438,-0.04574924,-0.01075737,-0.00035248,-0.00838486,-0.03193434,0.00227076,0.05077429,-0.03174933,-0.03564143,-0.06559506,0.00230394,0.12357223,0.01515807,-0.0462607,-0.07813022,0.03205919,0.05994307,-0.06952322,-0.01570156,-0.08328746,-0.01424738,0.05129383,0.03713771,-0.02622286,0.04811392,-0.01343984,0.02475635,-0.02201563,-0.02858873,0.06914589,0.04695086,-0.04300597],"last_embed":{"hash":"do2j2t","tokens":154}}},"text":null,"length":0,"last_read":{"hash":"do2j2t","at":1753423530609},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>F = Generate <i>Frequency Groups</i></u>#{1}","lines":[69,74],"size":344,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06586241,-0.05397358,-0.03231257,-0.08439843,-0.05535048,0.02342094,-0.06532859,-0.03192345,0.00965776,-0.03913702,0.04133512,-0.02805144,0.05065947,0.00149572,-0.01950846,-0.03629161,0.01452331,-0.00271805,-0.12398513,0.00470253,0.06714288,-0.02336534,-0.03494842,-0.06548272,0.039917,0.02387549,-0.06530645,-0.03917218,-0.03049632,-0.22138838,0.0265631,0.04190681,0.07782029,-0.03274481,-0.05147847,-0.04396182,-0.041992,0.03957674,-0.05629661,0.00747165,-0.02914557,0.03733912,0.02353345,-0.01957927,0.01856056,-0.02854314,-0.03496082,0.01560682,0.08371234,0.02000986,-0.04022057,0.00392333,-0.01909714,0.05162752,0.03389018,-0.00803725,0.036978,0.06953917,0.01170702,0.05860939,0.02696287,0.04485033,-0.10650473,0.04096599,0.00616924,0.02540932,-0.0301077,-0.02679706,0.02379745,0.03718477,-0.043993,0.05088994,-0.01955395,0.08840001,0.03918192,-0.03345288,-0.04292536,-0.04963272,-0.03137698,-0.00126809,-0.11759184,-0.0384815,-0.0257107,0.02492223,0.04527487,-0.01247627,0.07799598,0.03782346,0.0134697,-0.03610432,-0.00013674,0.05854871,0.03506676,-0.00086004,0.04826177,-0.00942524,0.04185879,-0.00137003,-0.03087223,0.11700047,0.01421881,-0.03644169,0.00744351,0.00559538,-0.00931279,-0.01233539,-0.03994484,-0.01243522,-0.05322451,0.01198113,0.02716901,0.01313137,0.05448033,-0.07312307,-0.03477721,-0.00356077,-0.01260511,0.03062775,-0.00135804,0.01247074,-0.00757623,-0.00644031,0.01334526,-0.01114439,0.05235134,0.01832667,-0.00483051,0.06044382,0.05228122,0.01757165,0.01709924,-0.00327098,-0.09219842,-0.01882139,0.02633663,-0.02170625,-0.02052465,0.01813257,-0.0074835,-0.0439184,-0.01518876,-0.04589446,0.06811247,-0.08888641,0.02972592,0.0509722,-0.0478332,-0.02700603,0.01775128,-0.01220117,-0.00928001,-0.03981986,-0.07382824,-0.0554457,0.01499624,-0.01949754,0.15559302,0.05594501,-0.07855906,-0.00157188,-0.04494672,-0.02927492,-0.00168066,0.11754903,0.00212469,-0.05305012,-0.01386454,0.05310274,-0.03864121,-0.06624402,-0.01113891,-0.00875204,-0.02053507,0.07499845,0.12882155,-0.01645966,-0.09427268,-0.0352873,0.01879471,0.02180239,-0.00564295,0.00930666,-0.02730731,-0.02376481,-0.05124547,-0.07756744,0.02293551,0.023011,-0.00490121,0.01270091,-0.03834616,0.04529829,-0.0293418,-0.0279329,-0.02820593,-0.02383295,0.01424784,-0.03641316,0.03568679,-0.0532941,0.06078988,0.02005612,0.0543505,0.05443937,-0.02146555,0.03444288,0.01913292,-0.06414016,0.13081734,0.00852395,-0.04116487,-0.01484134,0.05559726,0.08405545,-0.01382002,0.05651511,-0.00868708,0.0359617,0.04054492,0.04202779,0.00579825,-0.03921258,-0.06175583,-0.1862147,-0.03027773,0.02696154,-0.00616005,-0.00371311,-0.01579281,0.04219919,-0.05998332,0.02153475,0.06388231,0.11891421,-0.06464755,-0.03491867,0.02701087,-0.04658436,0.0176999,-0.10855178,-0.01074292,-0.02787528,0.09348115,-0.01083686,0.01599787,-0.03681222,-0.05728729,0.02319458,-0.02080812,0.14434959,0.01243049,0.02028589,-0.06025326,0.07272873,-0.02399558,-0.05064127,0.00726058,0.00813495,0.02841991,-0.06593463,0.00843621,-0.00384433,-0.01934488,-0.04242644,-0.02747392,-0.00417537,-0.10228965,0.00838929,0.01143651,-0.02132004,0.02847624,0.02258065,-0.0028819,0.04746097,-0.01480205,0.05717665,0.01196535,0.01377123,-0.03328541,-0.06141557,0.00968328,0.00689199,0.04010997,0.01767603,-0.05956605,0.04085907,-0.03564273,0.02130092,0.05563333,-0.02031419,-0.02226032,0.0630639,-0.00245573,-0.00594215,0.0419005,-0.00245014,0.05140803,-0.036211,0.0028586,0.02861052,-0.02182995,0.00420973,-0.03422189,-0.01720038,-0.04430935,0.04621188,0.08630708,0.01630247,0.00492027,0.03029147,0.0628018,0.0634049,0.01479891,0.01897515,0.00318364,-0.00115633,0.07738344,0.01459372,-0.033077,-0.26130095,0.00429751,-0.04380601,0.06736705,0.01827953,-0.01069766,0.03645413,0.04518891,-0.00007747,-0.0162391,0.05597368,0.04000641,0.04033742,-0.09635793,0.0342298,-0.03159099,-0.00921677,-0.02050582,0.05603854,-0.01810838,0.072055,0.02052603,0.27787751,-0.01075685,-0.0037903,0.05840228,0.02201807,0.03752552,0.02121269,0.04335415,0.00376374,-0.01339668,0.11101492,-0.02811521,-0.00261431,-0.0105625,-0.00178975,-0.03096343,-0.02379028,0.04890522,-0.07206059,-0.04179421,-0.06790024,0.00560046,0.0979571,0.01317924,-0.04072178,-0.06584628,-0.00354922,0.0404709,-0.07033107,-0.01864368,-0.05758579,-0.04915084,0.05111675,0.05351714,0.00393531,0.04217542,-0.02713151,0.0401209,-0.00169529,-0.02804909,0.09975119,0.03913682,-0.0508514],"last_embed":{"hash":"1vdtiis","tokens":472}}},"text":null,"length":0,"last_read":{"hash":"1vdtiis","at":1753423530678},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>","lines":[79,106],"size":3127,"outlinks":[{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":27}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06535178,-0.05507189,-0.02984962,-0.08377659,-0.05607178,0.02307151,-0.0655752,-0.02994786,0.00979668,-0.0391239,0.04159551,-0.0276144,0.05247433,0.00254504,-0.0204063,-0.03712904,0.01197386,-0.00454388,-0.1255793,0.00552629,0.06667671,-0.02301543,-0.03540942,-0.0637265,0.03738651,0.02487787,-0.06486461,-0.04173511,-0.03092992,-0.22087045,0.0270497,0.04335017,0.07960481,-0.03152763,-0.05025715,-0.04322955,-0.04058865,0.03913575,-0.05789895,0.00788669,-0.02916972,0.03857807,0.02461351,-0.02037473,0.01866862,-0.02781126,-0.03630452,0.01511845,0.08151549,0.02122834,-0.03804001,0.00386115,-0.0221252,0.05249752,0.0340067,-0.00850585,0.03839735,0.06819301,0.01299795,0.05690202,0.02720513,0.04403125,-0.10831606,0.0415233,0.00325872,0.02356017,-0.03088524,-0.02511557,0.02325349,0.03585107,-0.0454019,0.05104332,-0.01872856,0.08989391,0.03678386,-0.03299881,-0.04437343,-0.04909374,-0.0308068,0.00125953,-0.11829589,-0.03597275,-0.02448473,0.02503142,0.04406437,-0.01165284,0.07852371,0.0372707,0.01242895,-0.03404199,-0.00036166,0.05815377,0.03294731,-0.00016007,0.04645052,-0.01114586,0.03991925,-0.00042427,-0.03166817,0.115646,0.01318517,-0.03559152,0.00946497,0.00431034,-0.00965135,-0.0111749,-0.03955657,-0.01350206,-0.05252779,0.01358616,0.0260159,0.01301243,0.05413247,-0.07567359,-0.03411304,-0.00054445,-0.01186222,0.02956358,-0.00295576,0.01195896,-0.00642035,-0.00750039,0.01213669,-0.01038853,0.05373942,0.01767747,-0.00473162,0.06114597,0.05282416,0.01820934,0.01869347,-0.00325022,-0.09218897,-0.01818252,0.02732638,-0.02232449,-0.02003019,0.01772425,-0.00954,-0.04576075,-0.01433615,-0.04542012,0.06932301,-0.0886164,0.03028015,0.05304857,-0.04713961,-0.02596996,0.01731068,-0.01097883,-0.00917662,-0.039852,-0.07263512,-0.05644022,0.01607434,-0.01747803,0.15623552,0.05393172,-0.08004514,-0.00000291,-0.04431107,-0.02783865,-0.00186974,0.1179406,0.00244175,-0.05266056,-0.01515675,0.05516445,-0.03835688,-0.06586836,-0.00965669,-0.00647846,-0.02057671,0.07799928,0.12923735,-0.01743717,-0.09532996,-0.03426582,0.01683421,0.02391577,-0.00461753,0.01115787,-0.0260209,-0.0244195,-0.05203603,-0.07764582,0.02307222,0.02180697,-0.00583179,0.01251901,-0.03950333,0.04698189,-0.02847517,-0.02656175,-0.028617,-0.02520357,0.01425365,-0.03763989,0.03317747,-0.05169246,0.06081401,0.01934287,0.05324665,0.05424612,-0.02241095,0.03439167,0.02070346,-0.06382475,0.13142891,0.00927936,-0.04083671,-0.0152036,0.05557959,0.08265686,-0.01454687,0.05653313,-0.01089352,0.03753879,0.04041045,0.04206784,0.0061491,-0.03934245,-0.06191421,-0.18310539,-0.03085978,0.02685367,-0.00691983,-0.00431663,-0.01535974,0.0431051,-0.06122405,0.02316917,0.06329371,0.11850674,-0.0645412,-0.03478278,0.0288053,-0.04646409,0.01752876,-0.10776871,-0.01084771,-0.02636714,0.09169503,-0.01237325,0.01468322,-0.03626607,-0.05684385,0.02084517,-0.01912055,0.14350365,0.01404369,0.01660487,-0.05901648,0.07178408,-0.02348588,-0.05034897,0.00687173,0.0101562,0.02850441,-0.06599306,0.00615188,-0.00221466,-0.01861988,-0.04081164,-0.02787478,-0.00442549,-0.1005732,0.00831051,0.01098873,-0.02225069,0.02988808,0.02039122,-0.00356991,0.04612461,-0.01490292,0.05614012,0.01162093,0.01301661,-0.03416617,-0.06232454,0.01035806,0.00650315,0.04017713,0.01824086,-0.06034191,0.03960966,-0.03636569,0.02173378,0.05402146,-0.02378054,-0.0212121,0.06368028,-0.00195164,-0.00602687,0.03980507,-0.00101848,0.0520863,-0.03601159,0.00018094,0.02716947,-0.02170786,0.00302504,-0.03600805,-0.01844896,-0.0447553,0.04465707,0.08600304,0.01599512,0.00409231,0.03192178,0.06394576,0.06366315,0.01453027,0.0183737,0.00252199,-0.00082313,0.07575708,0.0166371,-0.03299606,-0.2622526,0.00434037,-0.04297288,0.06675661,0.01975354,-0.00954028,0.03712653,0.04644528,-0.00073384,-0.01676782,0.05500592,0.03938724,0.04186835,-0.09558105,0.03563739,-0.03063717,-0.00782309,-0.02201644,0.05557719,-0.01797494,0.0716062,0.01903125,0.27868956,-0.00948183,-0.00209593,0.05891486,0.02106691,0.03935979,0.02162981,0.04436228,0.00357371,-0.0127917,0.11253132,-0.02936711,-0.00325586,-0.00946785,-0.0004525,-0.03221362,-0.0239349,0.04712376,-0.07292849,-0.04311471,-0.06641262,0.00562422,0.09959774,0.01268889,-0.0428987,-0.0664238,-0.006132,0.04169869,-0.06887586,-0.01768126,-0.05802145,-0.05277717,0.05166977,0.05522628,0.00165847,0.04201587,-0.02601301,0.03918683,-0.00261723,-0.02804001,0.1008757,0.03847897,-0.05066564],"last_embed":{"hash":"8y5ppb","tokens":472}}},"text":null,"length":0,"last_read":{"hash":"8y5ppb","at":1753423530945},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>C = Create Your <i>File of Number Groups</i></u>#{1}","lines":[81,106],"size":3071,"outlinks":[{"title":"The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04175352,-0.01540605,-0.03018313,-0.04432806,-0.02045931,0.0246584,-0.01453274,0.01548423,0.03214693,-0.01741471,0.00675488,-0.01251605,0.06291049,-0.00033104,-0.02631471,-0.03736927,-0.00634622,-0.01356335,-0.06848798,-0.02188994,0.10520525,-0.01666204,-0.04002404,-0.0838489,0.08492067,-0.00483942,-0.04666572,-0.03485168,-0.02652411,-0.26027316,0.00574881,0.02925732,0.0118486,-0.04853137,-0.05431157,-0.03434427,-0.01272787,0.05223561,-0.03389027,0.02930563,0.01493765,0.01176811,0.00503067,-0.04340791,0.00410242,-0.03456027,-0.00456964,-0.01447428,0.00543167,0.01390286,-0.03855423,-0.01421416,0.0191747,0.06225532,0.01388922,0.01423636,0.04468514,0.07067367,0.03595947,0.03494522,0.01122142,0.05078041,-0.15356241,0.00757425,-0.0293958,0.02841859,-0.02782458,-0.04191886,0.04524556,0.01125077,0.01243699,0.03693516,-0.04722993,0.01524002,0.05078672,-0.06318372,-0.05387194,-0.05283623,-0.0414985,0.0167719,-0.07550819,-0.03237103,-0.04793508,0.00552535,0.06987087,0.01094362,0.05367764,0.02006625,0.0741514,-0.05385647,0.00265567,0.02800527,0.02080103,0.06538466,0.04846772,0.01412582,0.06104609,-0.03144909,-0.01876829,0.12521429,0.02715696,0.00654966,0.02720317,-0.0120965,0.00185389,-0.06457259,-0.04052171,-0.0219475,-0.06422819,0.0144312,0.04249258,0.00614388,0.04938319,-0.07119849,-0.03584116,-0.0052938,-0.00698893,0.02088307,0.00695667,0.00243475,-0.03463135,-0.01876574,0.01526592,-0.01329553,0.04190405,0.03174666,0.03266199,0.06955081,0.05082281,0.02688218,0.01968971,0.00505039,-0.12969647,-0.03464743,-0.0151925,-0.03915893,-0.01236629,0.02846511,0.01798485,0.03548313,-0.02551337,0.01909523,0.00872231,-0.10476256,0.00710755,0.09661222,-0.05213421,-0.01477285,0.02100541,-0.01655047,-0.02854687,-0.00323768,-0.02477597,-0.06766501,-0.02148927,0.03079673,0.09749673,0.11869102,-0.0446216,0.00284464,-0.03758998,-0.02789288,-0.0107451,0.09115596,-0.01788053,-0.06775539,-0.03536266,0.04856205,-0.04919008,-0.04372768,-0.03632996,0.02686011,0.00415909,-0.00175901,0.09319947,-0.0464919,-0.07972381,-0.04488898,-0.01349693,-0.01121178,-0.0002107,0.00328071,-0.06614745,-0.00537298,-0.03437115,-0.09558954,0.01651279,0.00343421,0.03379164,0.06297266,-0.0886647,-0.00284195,-0.02993713,0.00668341,-0.00955346,-0.02510684,0.00922962,-0.0016069,0.04725873,-0.03918021,0.04906163,0.00419646,0.05794655,0.00943456,0.01039761,0.04905794,-0.01799007,-0.04151375,0.13890983,0.00059635,-0.02809321,0.00894577,0.04154693,0.14565721,-0.0408624,0.01144018,-0.04188275,0.06408025,-0.00950161,-0.01186765,0.01374486,0.03560815,-0.08914508,-0.21324906,-0.01740016,-0.00588069,0.02924895,0.00566301,-0.03447382,0.03869054,-0.01583349,0.0195404,0.1166971,0.08317774,-0.00311755,-0.05653687,0.02027143,-0.02923084,0.01510954,-0.08929195,-0.02000146,-0.03714778,0.06258139,-0.00641757,0.0343106,0.01079366,-0.0624295,0.01314414,-0.04309306,0.12785092,0.02167069,0.02503162,-0.05246209,0.02922521,-0.022535,-0.01751665,0.0071097,-0.01581701,0.1004011,-0.04537014,0.00578352,-0.01773733,-0.03075148,-0.04489054,-0.01471448,-0.01680992,-0.10736427,0.01990305,0.03753512,-0.01653232,0.02421766,0.01541898,0.01432751,0.04278915,-0.05568723,0.05260541,0.02897265,0.0644801,-0.05485518,-0.08325571,-0.02714958,0.00757652,0.0273817,0.0104382,-0.05615543,0.08246767,-0.02898484,0.03697208,0.02838632,-0.00284803,-0.02313865,0.06221475,0.02655257,-0.01780018,0.09932498,-0.0338659,0.05631931,-0.05282851,0.02295463,0.03110872,-0.05324485,-0.00720928,-0.04246857,0.02826854,-0.0577614,0.04444151,0.04326309,0.02798135,0.02077195,0.02338759,0.00867514,0.04354007,0.0001858,-0.0045209,0.00484369,-0.00977981,0.04681349,-0.00836524,0.01171352,-0.25912955,-0.01586422,-0.0671201,0.02382635,0.01935026,-0.03629629,0.02885706,0.01147254,0.03400544,-0.03565353,0.0478229,0.03128701,0.0480887,-0.11692384,0.00466247,0.00995026,0.01423617,-0.0225094,0.06248825,0.00710468,0.06616516,0.03766651,0.27320927,0.01947158,-0.01110833,0.0205198,0.02228176,-0.03106802,-0.00635673,0.03400081,0.01766668,0.00920587,0.12117502,0.00650339,-0.00403986,0.02518173,-0.01167336,-0.0364191,0.00451311,0.04357563,-0.04627957,0.01613702,-0.03875822,-0.02044062,0.10663866,0.01916903,-0.0272282,-0.03866817,0.034175,0.05336577,-0.08082193,-0.03588431,-0.02800542,-0.02820949,0.05106648,0.03330098,-0.01228274,0.03256006,-0.01746157,0.02371266,-0.02829278,0.00282134,0.05758171,0.02768767,-0.00342441],"last_embed":{"hash":"12jpl5w","tokens":425}}},"text":null,"length":0,"last_read":{"hash":"12jpl5w","at":1753423531127},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>","lines":[107,123],"size":1507,"outlinks":[{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/odd-even-lotto.gif","line":9},{"title":"These are static filters, do not repeat from lottery draw to next drawing: eliminate them from play.","target":"https://saliu.com/HLINE.gif","line":11},{"title":"The lotto software generates lottery combinations from several lotto groups and sum-totals.","target":"https://saliu.com/HLINE.gif","line":16}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04359544,-0.00651598,-0.01641042,-0.05915558,-0.00659416,0.0122848,-0.01913906,0.02870579,0.02942813,-0.02423993,0.01157554,-0.01365504,0.05762025,0.00444584,-0.02434569,-0.03254206,-0.00014726,-0.0105998,-0.06503839,-0.0219121,0.1191206,-0.01137469,-0.04073882,-0.07890558,0.0739306,0.00083788,-0.05107198,-0.0204753,-0.01651922,-0.25488499,0.00148714,0.02573068,0.04191936,-0.0497056,-0.04622973,-0.0426711,-0.00607763,0.03797675,-0.03102859,0.02615219,0.01503153,0.01405022,0.01653739,-0.03959439,0.00079018,-0.03675792,0.00732279,-0.01475733,-0.00679168,0.00300692,-0.0418021,-0.008427,0.01183988,0.06222635,0.01707363,0.01644175,0.04363814,0.06290904,0.03323282,0.04101434,0.00330721,0.03940335,-0.1653416,0.00933395,-0.02624151,0.02607277,-0.03027173,-0.04652089,0.03969729,0.00307709,0.0018917,0.03897243,-0.03561362,0.01701962,0.05464894,-0.06553187,-0.05746719,-0.06071227,-0.03529516,0.00629934,-0.07657609,-0.03293942,-0.0584949,0.00422988,0.06405316,0.00760605,0.05594461,0.01761179,0.07132249,-0.0533018,-0.00076399,0.02853111,0.01710222,0.07061691,0.05517947,0.00908752,0.0489639,-0.02240283,-0.03025158,0.1285097,0.02187336,-0.00450086,0.03631238,-0.02708259,-0.00647064,-0.0505647,-0.03862773,-0.01992274,-0.07649475,0.01184659,0.04121863,0.00915131,0.03566872,-0.07901745,-0.01781191,-0.01703517,-0.0109763,0.01476304,0.00381435,0.00695474,-0.02843188,-0.02319902,0.02958999,-0.01059724,0.05422638,0.03762534,0.04109338,0.05452499,0.04646627,0.02431852,0.017669,0.00168442,-0.12105638,-0.0250105,-0.00608357,-0.04012944,-0.001811,0.02326205,0.01331777,0.04165503,-0.03453274,0.02333057,-0.00379956,-0.10610317,0.00531927,0.10196097,-0.06765088,-0.02127764,0.0246736,-0.01265664,-0.02270596,0.00510053,-0.01252964,-0.07733621,-0.01390039,0.03931403,0.09774572,0.11514392,-0.04055383,-0.00544833,-0.04327111,-0.01745061,-0.00976828,0.09371107,-0.01650918,-0.07002456,-0.04396404,0.04809723,-0.03862318,-0.0337626,-0.0227022,0.02361459,0.01541842,0.00764414,0.08592126,-0.03913482,-0.06470746,-0.03227525,-0.02701518,-0.00969213,0.00116921,0.0016184,-0.06313687,0.00190855,-0.03606637,-0.09127443,0.02053291,0.01858684,0.03778775,0.05683822,-0.08441318,0.0059207,-0.02919397,-0.01048273,-0.01319768,-0.02754495,0.00653039,0.01292163,0.0373831,-0.0470801,0.05029911,0.01949016,0.05983651,0.01395678,0.01196235,0.04361641,-0.02100017,-0.03403408,0.12959518,0.00282022,-0.02166316,0.00636095,0.05341469,0.14647771,-0.0477869,0.00842264,-0.05252382,0.06801839,-0.01101215,-0.01744032,0.01531201,0.02883208,-0.08725293,-0.21439037,-0.00353731,0.00205616,0.02534878,-0.00127696,-0.03666339,0.0423989,-0.01640096,0.02995322,0.09955473,0.08394027,0.00433578,-0.05360851,0.01597113,-0.02450274,0.01922316,-0.07018106,-0.02795379,-0.03629781,0.05456133,-0.00454269,0.0382739,0.00952048,-0.06196221,0.00731025,-0.05129275,0.12660535,0.02162099,0.03797201,-0.05711213,0.02714538,-0.03584483,-0.01735323,0.01273448,-0.01754313,0.09973811,-0.05132765,0.00078851,-0.01501317,-0.03068803,-0.04198378,-0.01430054,-0.01396508,-0.10894803,0.02164873,0.04263589,-0.00506699,0.00878281,0.01308354,0.00648277,0.04103464,-0.05441141,0.05318597,0.02862267,0.05480002,-0.05923768,-0.08529937,-0.03090565,0.00991207,0.01336692,0.00527502,-0.05957082,0.08155524,-0.03697404,0.03650107,0.02157895,-0.00918511,-0.02336656,0.0714483,0.01076542,-0.02339873,0.10312441,-0.05103643,0.05854171,-0.05813539,0.01431869,0.01864205,-0.06511241,-0.01318902,-0.04208077,0.02992354,-0.04765559,0.04725911,0.03367577,0.02384826,0.00946087,0.01795242,0.00874294,0.06182925,-0.00027287,0.00685043,-0.00349395,0.00124954,0.04068848,-0.00075117,0.01403651,-0.27032611,-0.02821867,-0.06946532,0.02591605,0.01941774,-0.03014299,0.0314679,0.0169361,0.02351717,-0.01187136,0.03894534,0.01548675,0.04945273,-0.10334356,0.00535401,0.01062438,0.01697103,-0.02032662,0.0699524,0.00231741,0.06294887,0.03904177,0.28085861,0.02564492,-0.00880261,0.02439215,0.0273079,-0.04064185,0.00018639,0.04025804,0.03596621,0.02034261,0.11746373,-0.00862313,-0.00103256,0.02963952,-0.0023805,-0.0408816,0.00299491,0.05141689,-0.03586854,0.01559653,-0.0380878,-0.02449184,0.10524514,0.0170576,-0.02438936,-0.04483124,0.02850146,0.05286525,-0.07931124,-0.02492677,-0.03111411,-0.02546235,0.06000118,0.03326102,-0.01182579,0.02805802,-0.02750324,0.01932276,-0.03168364,0.0059125,0.06083094,0.03320273,-0.00986281],"last_embed":{"hash":"pazsog","tokens":263}}},"text":null,"length":0,"last_read":{"hash":"pazsog","at":1753423531320},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{1}","lines":[109,118],"size":770,"outlinks":[{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/odd-even-lotto.gif","line":7},{"title":"These are static filters, do not repeat from lottery draw to next drawing: eliminate them from play.","target":"https://saliu.com/HLINE.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04945629,-0.04622122,-0.03796224,-0.02245459,-0.04649395,0.02733597,0.00255159,0.00180867,0.03984058,-0.02851386,0.02902673,-0.00632324,0.05940714,0.01355857,-0.04330352,-0.01621473,-0.00423591,-0.01126675,-0.057314,-0.0160038,0.10438461,-0.00314692,-0.03362197,-0.0685588,0.09867755,0.01862423,-0.04834162,-0.0655139,-0.03096173,-0.24251013,0.01298916,0.03030874,-0.00454918,-0.04574864,-0.03769813,-0.04747671,-0.03367549,0.09729595,-0.01475609,0.02875271,0.00401646,-0.00070062,-0.02471035,-0.06550255,-0.00294028,-0.0446257,-0.01672704,-0.01982625,0.05214096,0.0166829,-0.02352619,-0.01190526,0.05125709,0.06114284,-0.01206994,-0.0070816,0.0348473,0.05602878,0.05452872,0.01539599,0.02758847,0.03461866,-0.11400912,0.02619705,-0.02393698,0.02844451,-0.03636979,-0.03814284,0.02626212,0.05724966,0.03838229,0.01593855,-0.08257837,0.03604966,0.04098602,-0.05801702,-0.04505134,-0.04302479,-0.07529301,0.01726629,-0.08177906,-0.08439215,-0.00147658,-0.00909486,0.02550125,0.01199167,0.0455586,-0.00027905,0.09379306,-0.03140789,-0.01212099,0.01982999,0.0179925,0.06431753,0.01229356,0.00052168,0.09362978,-0.0191977,0.0006223,0.13926664,0.03533934,0.00641022,0.00471062,0.01613687,0.01845852,-0.05544591,-0.02751518,-0.01935866,-0.06391177,0.03115035,0.03290135,0.00553352,0.06758041,-0.05915238,-0.04650665,-0.01906347,0.01217567,0.01623425,0.01653877,0.00771031,-0.04346904,-0.00959065,-0.00357213,-0.03243147,0.03568704,0.04206005,0.03418659,0.07669858,0.03914997,0.0367882,0.04156948,-0.00672687,-0.10316753,-0.03635102,-0.00978409,-0.00130734,0.00249645,0.01342505,0.03268085,0.00850053,-0.017274,0.03534261,0.00733539,-0.07357042,0.00344773,0.07131263,-0.0281772,-0.00835258,0.03841073,-0.04222567,-0.04065034,-0.0042154,-0.04004466,-0.03220211,-0.02200216,-0.00590947,0.10154487,0.11502778,-0.04596388,0.0208823,-0.02580708,-0.06502897,-0.00680807,0.07816515,-0.01853838,-0.025825,-0.03646589,0.01580924,-0.05345928,-0.04344601,-0.03430285,0.01607515,-0.00633352,-0.00599694,0.08129446,-0.0327245,-0.11546431,-0.0748699,-0.01756359,-0.01434915,-0.0087007,0.00766852,-0.05393878,0.00936023,-0.02840313,-0.09626726,0.00189095,-0.02110144,0.02930605,0.07670284,-0.05484306,0.00030071,-0.04370638,0.03454434,-0.00884539,-0.04138367,0.00938707,-0.01102861,0.02458947,-0.0709285,0.03944448,-0.02965567,0.06609665,0.0278362,0.00849412,0.04031252,-0.0120422,-0.03785928,0.14415856,-0.00444731,-0.0620085,-0.01524167,0.03189574,0.11093383,-0.00832125,0.01376694,-0.03770069,0.06783106,0.01638056,-0.01615765,-0.00636371,0.0242619,-0.08258258,-0.23466532,-0.0310269,0.01095849,0.0333111,0.05301681,-0.02787579,0.02000994,0.02917028,0.01538745,0.13873312,0.06311373,-0.03841538,-0.04933422,0.01075067,-0.04474303,0.04911644,-0.08928275,-0.01138859,-0.02010927,0.05012875,-0.00581451,0.04466311,-0.00221318,-0.06452215,0.02850647,-0.00609695,0.12204264,0.01092004,0.00941859,-0.0392709,0.02101276,0.00501521,-0.031338,-0.01800147,-0.01433084,0.11095514,-0.0639233,0.02133481,-0.01893622,-0.05181347,-0.07712758,-0.02092553,-0.00275824,-0.06552854,-0.01584332,0.03562891,-0.01129139,0.00764915,0.00419864,0.03111886,0.01007969,-0.06031422,0.03613326,0.03749312,0.0574463,-0.01691595,-0.0815576,-0.02916067,0.01546744,0.04569859,0.03141285,-0.05054288,0.06474021,-0.03768095,0.04057835,0.03047527,0.01479413,-0.03260739,0.06393754,0.06577551,0.01129805,0.13303667,-0.03121615,0.00476827,-0.04230037,0.05788019,0.05415098,-0.0153841,0.00258281,-0.04456311,0.02146529,-0.06834976,0.05119739,0.05184506,0.04178334,0.06374537,0.00430594,-0.01221352,0.01668415,0.00178226,0.01416293,0.02252938,-0.05282582,0.0489986,-0.0070647,-0.00012674,-0.23494729,0.00571264,-0.05149132,0.04822038,0.01651743,-0.01621983,-0.00718894,-0.00220787,0.01615661,-0.03791006,0.05917025,0.04643951,0.03039077,-0.11259199,-0.01621924,0.00691107,0.01156354,-0.02192719,0.06430903,0.01285588,0.05093312,0.02829425,0.26474297,0.01892453,-0.02677427,-0.01519418,0.00244099,-0.021279,-0.02364707,0.04429532,-0.00087891,-0.01818011,0.13262077,0.04073673,0.00050681,0.02206934,-0.02873243,0.00194667,0.01822586,0.02803176,-0.03906065,0.02647037,-0.07135373,-0.023216,0.08215838,0.02850416,-0.0523138,-0.01384375,0.01246284,0.06824912,-0.0594017,-0.05315028,-0.04131472,-0.02054034,0.03258183,0.02655335,-0.04386571,0.04253823,-0.0121932,0.00646723,-0.04279133,0.00936351,0.04768134,0.00700381,0.00258838],"last_embed":{"hash":"1gwhvop","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"1gwhvop","at":1753423531426},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{2}","lines":[119,119],"size":265,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09009057,-0.03123739,0.0043676,-0.04714531,-0.0226452,0.01204131,-0.0018767,-0.00023632,0.01451819,-0.01938619,0.01046623,-0.02019452,0.07334355,-0.00030108,0.00196397,-0.04791869,-0.00504126,0.00528295,-0.04472927,0.00565328,0.11792747,0.01159316,-0.05418035,-0.06363358,0.09114471,0.02101372,-0.04889167,-0.06570378,-0.01954619,-0.2327828,0.02126834,0.0255215,-0.00489935,-0.0424805,-0.00580292,-0.04253123,-0.018652,0.04903144,-0.05286299,0.02795826,0.00735192,0.01003577,0.01073408,-0.05145893,0.01722516,-0.03053435,-0.02407745,0.00281536,0.0182842,0.04274049,-0.02334229,0.05676211,0.0125997,0.04990238,0.01077389,0.0008455,0.04037008,0.08821501,0.02514148,0.05505918,0.03005235,0.03841062,-0.13608214,-0.00614417,-0.01227079,0.02483705,-0.02091926,-0.04891793,0.01039406,0.06237877,0.02775962,0.01785922,-0.05568715,0.04145275,0.01709284,-0.04882759,-0.03401227,-0.05067457,-0.03363036,0.03248205,-0.05572864,-0.05030539,-0.02775096,-0.011103,0.05392862,-0.01182583,0.04260673,0.04625825,0.06156511,-0.07300361,0.00442152,0.03558002,0.05802939,0.07239585,0.07359938,0.01966524,0.03420364,0.02207499,0.00921669,0.12992604,0.02304549,0.0112525,-0.00203581,-0.0161266,0.0354576,-0.04833522,-0.01626446,-0.02146207,-0.03249342,0.01326089,0.02739053,0.00605214,0.09119945,-0.04621012,-0.02446567,0.00176835,0.02089142,0.03180859,0.00790773,-0.00836632,-0.06451279,-0.01493857,0.00794548,-0.00919073,0.00508586,-0.01587207,0.01456411,0.08585838,-0.00574818,0.04852624,0.01648419,-0.05815479,-0.10784222,-0.05311475,-0.01496632,-0.01844865,0.0005431,0.01878717,0.03436174,0.02474874,-0.05898793,-0.02750321,0.02376207,-0.08804209,-0.01475306,0.06750712,-0.00995234,-0.01415753,0.01383725,-0.02014034,-0.00301912,0.00939462,-0.04008991,-0.08004004,-0.0108998,-0.02335002,0.09620722,0.09966682,-0.07087748,0.01331986,-0.01189148,-0.02269351,-0.01446022,0.14678903,-0.03960551,-0.04890295,-0.01553329,0.04451326,-0.0464778,-0.04954492,-0.05116082,0.0176405,-0.01820445,0.00636582,0.09524931,-0.03547342,-0.08054902,-0.0723584,-0.01793715,-0.03237557,-0.02008047,-0.04152015,-0.05738356,-0.00484286,-0.03538529,-0.07671762,0.00529058,-0.00406084,0.02222387,0.03720113,-0.09777636,-0.03640614,-0.07664572,-0.00357872,-0.00098932,-0.03178411,-0.0046225,-0.03680115,0.04185041,-0.04637435,0.03161946,-0.00352708,0.04282358,0.02748923,0.01016832,0.06438963,-0.01012625,-0.06665667,0.13293363,0.01071387,-0.05629057,0.02693805,0.02450487,0.09492964,-0.00765664,-0.00081098,-0.02878086,0.03830315,0.00522298,0.00675391,-0.00821184,0.04391905,-0.0634841,-0.24160697,-0.02730979,-0.03360205,0.00310096,0.03430035,-0.03846166,-0.01337781,-0.01793752,0.0315785,0.15410233,0.05533243,-0.0421668,-0.05292757,0.0314265,-0.0276408,0.01590456,-0.09131449,-0.03503906,-0.03989405,0.04555502,-0.01545795,0.02152783,0.01458944,-0.07219601,0.02548615,0.00431766,0.13764203,0.04125959,0.03212618,-0.05179462,0.01691834,-0.01788178,-0.01835565,-0.01980519,0.00308824,0.07829417,-0.02398351,-0.02949729,-0.00648881,-0.04920349,-0.06528638,-0.01588045,-0.02073447,-0.10929561,0.00548568,0.0560978,-0.03593935,0.01244838,0.04254205,0.07633562,0.02877111,-0.03943451,0.0386068,0.04380437,0.07121764,-0.01869641,-0.07346179,-0.02006312,0.00981986,0.05034465,-0.00220994,-0.04357301,0.05769515,-0.03841213,-0.00417371,0.03654525,0.01393469,-0.04884774,0.04832302,0.03866376,-0.02692172,0.10125666,-0.01531109,0.02462627,-0.0388177,0.03015094,0.06095849,-0.00813589,0.01142728,-0.03878741,0.01088489,-0.01717805,0.06360766,0.04421658,0.03653113,0.03516564,0.03543315,0.00069345,0.0266761,-0.00371318,-0.00144679,0.02433055,-0.03858133,0.0550333,0.03714927,-0.02650556,-0.25039828,-0.0095472,-0.02111597,0.03575144,0.00402065,-0.0317011,0.00041085,-0.00762517,-0.00606557,-0.05775183,0.06738578,0.04077227,0.04179008,-0.07793365,0.01597147,0.0099529,0.02804805,-0.04602946,0.07819232,0.02564299,0.06969624,0.03972477,0.26125839,-0.00018673,-0.03221083,0.03209973,0.02189629,0.02096885,-0.00769538,0.03341442,0.00711691,-0.02874072,0.10379809,0.0299464,0.01331522,0.00470277,-0.01782045,-0.00968118,0.02989579,0.02376018,-0.08862609,-0.0142148,-0.05148909,-0.01979433,0.11503448,0.03818841,-0.03746853,-0.03086262,0.03951747,0.0386457,-0.05798608,-0.03988507,-0.02776026,-0.03900469,0.04405899,0.07841486,-0.02032765,0.02374795,-0.02179758,0.00377671,0.01753393,0.02353945,0.02483608,0.03541715,-0.00801131],"last_embed":{"hash":"lqg16z","tokens":124}}},"text":null,"length":0,"last_read":{"hash":"lqg16z","at":1753423531522},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>R = Reports for <i>Past Drawings</i></u>#{3}","lines":[120,121],"size":297,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03942797,-0.03030193,-0.02631971,-0.07618742,-0.0385728,0.01458183,-0.02354453,-0.00137628,0.03297294,-0.01814834,0.01143435,-0.03060031,0.08410478,-0.01225185,-0.00570444,-0.04429193,-0.00329528,-0.01686576,-0.09961437,-0.04287345,0.11950549,-0.00859082,-0.04442276,-0.07517584,0.04017281,0.00342806,-0.04646362,-0.04838486,-0.01287541,-0.26114616,0.03465373,0.06384843,0.05384569,-0.04915566,-0.0366294,-0.04380201,-0.02259587,0.05020701,-0.06630632,0.02694853,-0.00726681,0.04343498,0.03673986,-0.024543,-0.00558323,-0.0147465,-0.01981089,-0.00588064,0.01686965,0.02402407,-0.02903319,-0.02429296,-0.0083127,0.08076476,-0.00252658,0.00475417,0.03038292,0.05291514,0.02612142,0.05023066,0.02516688,0.05486575,-0.15708508,0.04183636,-0.01188426,0.0147775,-0.03245313,-0.04391234,0.00733613,0.04172261,-0.0323055,0.05665834,-0.03827625,0.03684104,0.06068533,-0.03839811,-0.06614124,-0.06161021,-0.02956653,0.00223925,-0.06258571,-0.02419067,-0.05745021,0.00925014,0.05892726,0.00580339,0.05693217,0.02044948,0.04742461,-0.01907395,-0.00788563,0.06265779,0.01988113,0.05468943,0.0640871,-0.00661621,0.04984657,-0.00868837,-0.01140572,0.11694934,0.02865591,-0.00228571,0.0333906,-0.02914526,-0.02767028,-0.04743491,-0.0399626,-0.00519598,-0.04796009,0.00967632,0.04878595,0.01914528,0.05067906,-0.11028804,-0.04297337,-0.00563892,0.01850453,-0.01345578,0.00852462,-0.01101288,-0.0174614,-0.0193682,0.01528107,-0.01577837,0.09562649,0.0481473,0.01204299,0.08829825,0.04520631,0.03867479,0.01222934,0.01200171,-0.10952929,-0.02616014,-0.001448,-0.034238,0.00447058,0.00008143,-0.00861858,0.01596763,-0.0619722,-0.00719272,0.03329985,-0.09301079,0.01969202,0.11165096,-0.03461722,-0.00873322,-0.01283583,-0.01627792,0.00969383,0.01456114,-0.03073402,-0.06274357,0.0030002,0.00683217,0.10587443,0.06814514,-0.06178012,0.00733894,-0.03855948,-0.00936183,0.00030127,0.08174179,0.01525012,-0.06564707,-0.01787286,0.0390281,-0.05334756,-0.05111269,-0.01254783,0.01724549,-0.00667443,0.01882963,0.10001865,-0.02104761,-0.08861873,-0.00051604,-0.00148379,-0.01456199,-0.01871828,0.0076606,-0.05392779,-0.01609585,-0.04731156,-0.09693205,0.03562358,0.01246502,0.01920805,0.05190374,-0.03489378,0.00592577,-0.00602836,0.00260435,0.00559091,-0.02463275,-0.01128198,-0.0246261,0.0578686,-0.05004023,0.05619463,0.00916551,0.05378863,0.01610707,0.02096326,0.05368634,-0.02342866,-0.03434518,0.11617146,0.02621321,-0.02520736,0.01832549,0.0544863,0.12273254,-0.03707092,0.0400303,-0.01928759,0.02461544,0.0117191,0.0133981,0.02945219,-0.00737065,-0.07362057,-0.19946624,-0.00409914,0.00825717,0.04497973,-0.00330014,-0.03411547,0.0257376,-0.04630647,0.0200215,0.08543965,0.06795089,-0.01032823,-0.04201085,0.03803211,-0.02990822,0.00122129,-0.09678847,-0.01758408,-0.06660301,0.06833889,-0.00627114,0.02650107,0.01106214,-0.05834584,0.01859074,-0.02907008,0.12475027,-0.00266837,0.00796214,-0.0305072,0.04039604,0.00407578,-0.00657316,0.02236071,0.02132105,0.05721653,-0.04602415,-0.01956565,-0.00576834,-0.02449267,-0.04486413,-0.01411719,-0.01636519,-0.12030397,0.01528334,0.03596724,-0.02121166,0.01936211,0.00923009,-0.00077,0.03720976,-0.03425943,0.04840346,0.00220821,0.0589778,-0.05627903,-0.07687202,0.00606214,0.00103201,0.00622648,-0.01753416,-0.05748558,0.05238789,-0.02294229,0.01329739,0.0235815,-0.00225085,-0.01537745,0.05097021,0.00387117,-0.0179324,0.10298043,-0.04468824,0.05414118,-0.05810138,-0.00611456,0.02004658,-0.03108489,-0.00885081,-0.05982885,0.02145214,-0.06965862,0.0234872,0.05230177,0.04102636,-0.00319334,0.04995029,0.04157411,0.06522454,-0.01974,-0.01666582,0.0181779,0.02032582,0.04319934,0.02279032,0.00850158,-0.28036162,-0.01318716,-0.05950619,0.05352715,0.00974184,-0.02787574,0.03420237,0.02456124,0.02052817,-0.00374592,0.04278247,0.0169944,0.04626241,-0.11350819,0.04078178,0.00034375,0.00067838,-0.0429927,0.04814203,-0.00397861,0.08803491,0.02813631,0.26764545,-0.00323554,0.00146186,0.02467789,0.00965009,-0.0157138,-0.00339126,0.04683299,-0.003887,-0.00333688,0.12875186,-0.04580715,-0.02272945,0.01080518,0.01382105,-0.02846818,-0.01895983,0.0358841,-0.05408663,-0.00945896,-0.05412805,-0.01530201,0.11870786,0.01189324,-0.02855277,-0.07814565,0.00957692,0.05049853,-0.08543783,-0.00372491,-0.03163923,-0.04950909,0.06881274,0.03112574,0.01207319,0.03883199,-0.0303781,0.03922761,-0.01101896,-0.0043885,0.05854092,0.03669815,-0.02853921],"last_embed":{"hash":"1w81w1g","tokens":511}}},"text":null,"length":0,"last_read":{"hash":"1w81w1g","at":1753423531568},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>","lines":[124,145],"size":1785,"outlinks":[{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/UserGroups63.gif","line":3},{"title":"Menu 4: Software generates all types of lotto combinations, including all 6/49 games.","target":"https://saliu.com/ScreenImgs/UserGroups64.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03845973,-0.02972595,-0.02461019,-0.07730095,-0.03592936,0.01484728,-0.02367356,-0.00049098,0.0330224,-0.01879764,0.01261511,-0.03052109,0.08367812,-0.01361516,-0.00606659,-0.04510515,-0.00406261,-0.01541493,-0.09959327,-0.04464941,0.1191937,-0.00875191,-0.04227975,-0.07443795,0.03887347,0.00468969,-0.04515923,-0.04761835,-0.01266323,-0.25871035,0.03487111,0.06350785,0.05616852,-0.05010017,-0.03697694,-0.04312613,-0.0243479,0.05071059,-0.06795084,0.02754236,-0.00841261,0.04255958,0.03912789,-0.02397105,-0.00545757,-0.01384634,-0.02181936,-0.00818596,0.01651488,0.02459394,-0.02967089,-0.02358956,-0.00912274,0.08034356,-0.00295482,0.00468545,0.03210963,0.05047699,0.02754609,0.04965616,0.02428641,0.05449001,-0.15996641,0.04299758,-0.01177714,0.01376421,-0.03183245,-0.04423694,0.00596845,0.04113106,-0.0345115,0.05449745,-0.0366372,0.03723741,0.062282,-0.0378619,-0.0677003,-0.06113205,-0.02861237,0.00299507,-0.0623159,-0.02259696,-0.05901361,0.00994823,0.0606226,0.00576791,0.05670613,0.02106778,0.04751187,-0.01735708,-0.00975867,0.06294977,0.017954,0.05553049,0.06321844,-0.00809591,0.04698294,-0.0088076,-0.01334364,0.11682191,0.02826204,-0.00126874,0.03413734,-0.02991899,-0.02656689,-0.0491467,-0.04140684,-0.00328115,-0.04863167,0.00985857,0.0486435,0.02081058,0.0498559,-0.11110096,-0.04146296,-0.00481785,0.01912559,-0.01343661,0.00794362,-0.01190695,-0.01723211,-0.0202279,0.01457053,-0.01531227,0.09629242,0.04767659,0.01221521,0.08922012,0.0480582,0.03921617,0.01201823,0.01149095,-0.11140177,-0.02719593,-0.00376945,-0.03346088,0.00332096,-0.00201119,-0.01044872,0.01687655,-0.061289,-0.00819805,0.03087411,-0.09079506,0.01747264,0.11348429,-0.0352552,-0.00720065,-0.01285217,-0.01493522,0.00964834,0.01456957,-0.02979742,-0.06320346,0.00346282,0.00776843,0.1034416,0.06697527,-0.06264822,0.00713282,-0.04006238,-0.01013866,0.00071979,0.08322991,0.01752075,-0.06558258,-0.01773939,0.04018293,-0.05172677,-0.05113252,-0.00970176,0.01805365,-0.00749695,0.01777732,0.09871868,-0.02098479,-0.08633807,0.0010048,-0.00184559,-0.01423842,-0.01735416,0.0062951,-0.05355156,-0.01522425,-0.04662231,-0.09828234,0.03825973,0.01236895,0.02065633,0.05277229,-0.03395597,0.00675966,-0.00774214,0.00173271,0.00448627,-0.02607461,-0.01177389,-0.02674541,0.05771006,-0.05166547,0.05779172,0.0110195,0.05382639,0.01365246,0.02070608,0.05431952,-0.0234942,-0.03320405,0.1137977,0.02720645,-0.02508235,0.01806587,0.05582233,0.12160591,-0.038507,0.04071257,-0.01878812,0.02327676,0.01136675,0.01224555,0.0306656,-0.00584542,-0.07146854,-0.20005798,-0.00156992,0.00784787,0.0440265,-0.00318667,-0.03408951,0.02570618,-0.04847573,0.02191866,0.0820217,0.06657363,-0.00807407,-0.03946136,0.04005476,-0.03014551,0.00031051,-0.09432247,-0.01846346,-0.06773555,0.06759694,-0.0079408,0.02730924,0.01039565,-0.05740888,0.01749042,-0.03128518,0.12556736,-0.00243029,0.00862352,-0.03130113,0.04266217,0.00596208,-0.00704096,0.0211884,0.02254942,0.05804162,-0.04353929,-0.02058709,-0.00657016,-0.0239648,-0.04399085,-0.01288522,-0.01572031,-0.11949768,0.01464999,0.03409392,-0.01958936,0.01769581,0.01163668,0.00106038,0.03735218,-0.03566154,0.05067696,0.00270829,0.06023506,-0.05674228,-0.07655553,0.00648548,0.00011012,0.00515985,-0.01718828,-0.05745519,0.05310433,-0.0226098,0.01317806,0.02119893,-0.00318496,-0.01391848,0.05323175,0.0049124,-0.01842989,0.10185071,-0.04652989,0.05325188,-0.05906648,-0.009181,0.02057598,-0.03216085,-0.01105888,-0.05955439,0.02040215,-0.06901884,0.02279438,0.04893829,0.04150904,-0.00362138,0.05024727,0.04294184,0.06578089,-0.02084078,-0.01686736,0.01790104,0.02074922,0.04276171,0.02568194,0.00880886,-0.28301755,-0.01426256,-0.05929799,0.05375817,0.01051467,-0.02808986,0.03520403,0.0251937,0.01870584,-0.00388961,0.04257883,0.01563866,0.04567444,-0.11448091,0.04273989,0.00018853,0.0038245,-0.04397039,0.04701598,-0.00423342,0.0866969,0.02816773,0.26696557,-0.00440266,0.00177465,0.02548489,0.01159728,-0.01739971,-0.00222002,0.04812244,-0.00383825,-0.00187148,0.12666154,-0.04496726,-0.02442496,0.01257405,0.01501699,-0.02646934,-0.02031155,0.03408155,-0.0530508,-0.01121995,-0.05174869,-0.01458784,0.11900571,0.01377161,-0.02592868,-0.07706783,0.0085113,0.05049343,-0.08532503,-0.00184077,-0.03488314,-0.05131263,0.06965339,0.03097611,0.01148786,0.03645961,-0.02868291,0.03983608,-0.01372599,-0.00320724,0.05808528,0.03699021,-0.02714938],"last_embed":{"hash":"btt661","tokens":510}}},"text":null,"length":0,"last_read":{"hash":"btt661","at":1753423531762},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>G = Generate Combinations based on Reports</u>#{1}","lines":[126,145],"size":1731,"outlinks":[{"title":"Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.","target":"https://saliu.com/ScreenImgs/UserGroups63.gif","line":1},{"title":"Menu 4: Software generates all types of lotto combinations, including all 6/49 games.","target":"https://saliu.com/ScreenImgs/UserGroups64.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07909682,-0.04810544,-0.05366402,-0.0829844,-0.05908644,0.02279739,-0.04926424,-0.01664474,0.00530363,-0.04499647,0.02807229,-0.01665695,0.04955377,-0.01704671,-0.00987331,-0.05417069,-0.00214112,-0.00727339,-0.09465224,-0.01232952,0.09509732,-0.0155251,-0.02191494,-0.06917129,0.04126778,0.00356754,-0.05333612,-0.06157606,-0.01399706,-0.24301215,0.03869515,0.06788764,0.0577041,-0.03796104,-0.04575802,-0.03975019,-0.03054053,0.02604105,-0.06311657,0.04616824,-0.00695681,0.04336845,0.02044961,-0.03708256,0.00558001,-0.01689182,-0.03059092,0.00398534,0.07074668,0.03258403,-0.03698723,-0.00824897,0.00574732,0.06859402,0.02039425,0.00814083,0.02674516,0.06597976,0.01424058,0.04663609,0.02990662,0.04829293,-0.1395383,0.05984186,-0.00532155,0.01590179,-0.02864479,-0.01807463,0.039285,0.01375998,-0.03370208,0.04112521,-0.03364525,0.04262089,0.06277061,-0.05100274,-0.06944492,-0.04734799,-0.03909102,0.00284007,-0.07763361,-0.03857989,-0.03771979,-0.00944192,0.0578856,0.04289169,0.07383385,0.03285737,0.03790394,-0.04762544,-0.02499895,0.07376935,0.03925132,0.03259262,0.06534778,-0.02022375,0.07254668,-0.00085061,-0.01166165,0.11477563,-0.00869995,-0.02702432,0.0297722,0.00486903,-0.02491769,-0.02235051,-0.06154695,-0.002997,-0.05278871,0.02040878,0.03699081,0.03046944,0.04515605,-0.07617381,-0.0403606,-0.02555,-0.03621515,0.02228625,0.00850018,-0.01253546,-0.01295686,-0.02376139,-0.00769021,-0.00802942,0.03921093,0.00905363,0.00842814,0.05442976,0.04327665,0.01606566,0.03027094,0.01187009,-0.1159993,-0.03190343,0.00985222,-0.01179554,-0.01442899,-0.00215815,-0.01762814,-0.01727695,-0.02545979,-0.02781913,0.0793448,-0.09249446,0.02540916,0.07483648,-0.03312312,-0.02253077,0.01567615,-0.00247849,0.00869389,-0.02108398,-0.04015571,-0.06351589,0.0056434,0.006311,0.12924603,0.07168175,-0.05179157,0.00125114,-0.03484363,-0.03422417,-0.01180784,0.09615371,0.00581964,-0.06265898,-0.0430795,0.04997568,-0.05386942,-0.07715367,-0.00099855,-0.00954642,0.01156925,0.05691065,0.12787925,-0.03434369,-0.07940032,-0.03445383,-0.00496638,0.01597882,-0.01887414,0.00697888,-0.03771014,-0.01613475,-0.04880488,-0.08722256,0.02240703,-0.01132775,0.01563372,0.04076439,-0.04529261,0.0246934,-0.03035777,-0.00234453,-0.02061009,-0.0251542,-0.01110711,-0.02678139,0.05812545,-0.03830759,0.0488059,-0.00226678,0.03558227,0.03754031,-0.00660747,0.06335948,0.0088361,-0.03056602,0.1248749,0.0058757,-0.01699953,0.00695228,0.06141433,0.09450364,-0.02915514,0.05247536,-0.02444725,0.03706362,0.04267191,0.036369,0.00684689,-0.02592457,-0.07999853,-0.1985939,-0.02275744,0.01972743,0.00970578,0.00072888,-0.02939924,0.04267739,-0.04649333,0.01634524,0.07738381,0.09749006,-0.03673636,-0.02815103,0.02958864,-0.04520537,0.00490786,-0.0874913,-0.01905027,-0.04493934,0.07818357,-0.02196417,0.02993396,-0.01480254,-0.06062786,0.04103602,-0.02625486,0.11996549,0.01164213,0.00734953,-0.0579032,0.08317762,-0.01635075,-0.04084896,-0.00041286,0.00386877,0.03050265,-0.04210587,0.02464881,0.00620966,-0.00157423,-0.06490426,-0.02583198,0.00272407,-0.09750432,0.00663635,0.03454567,-0.02395669,0.00405806,0.02119545,0.00173946,0.03838335,-0.0126944,0.05419607,0.0272186,0.03127301,-0.0489351,-0.06266138,0.01117723,0.00651387,0.03736911,-0.00009647,-0.05974993,0.04622478,-0.01683787,0.02184837,0.05202439,-0.00657669,-0.02447186,0.06441665,-0.00535566,-0.00380189,0.08673327,-0.01748653,0.06163662,-0.0365276,-0.00129987,0.03100836,-0.03111146,-0.01044822,-0.02118249,-0.00807926,-0.06785611,0.02823512,0.05872106,0.01688613,0.00051404,0.04798199,0.05964855,0.05002073,-0.00539993,0.03918055,0.02003001,0.00376358,0.07157765,-0.0001964,0.00030029,-0.27312869,0.00230369,-0.04982655,0.0577388,-0.00128966,-0.01282924,0.0299566,0.01381051,0.01569269,-0.01555506,0.06282563,0.01023919,0.03693152,-0.11391135,0.05727445,-0.00404498,-0.00565741,-0.02265235,0.05143237,-0.01464275,0.08044746,0.04325243,0.28428435,-0.01104287,0.00189241,0.04695283,0.02413695,0.00657669,0.03217375,0.04045434,0.0188213,-0.01987182,0.0948654,-0.03410668,-0.02140847,-0.00010539,-0.00361892,-0.02369544,-0.03521074,0.04623194,-0.08170567,-0.0274106,-0.06071724,-0.00141858,0.11856696,0.02394579,-0.02098005,-0.07951175,0.00473478,0.04488282,-0.06287462,-0.02246511,-0.03914154,-0.02815507,0.05638971,0.03319724,-0.00491233,0.02675777,-0.02148774,0.03862162,-0.00669467,-0.00449316,0.07368828,0.03386938,-0.0472203],"last_embed":{"hash":"15owo1j","tokens":422}}},"text":null,"length":0,"last_read":{"hash":"15owo1j","at":1753423531967},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>","lines":[146,155],"size":1373,"outlinks":[{"title":"Program 4: Software generates lotto combinations from groups of lotto numbers created by a player.","target":"https://saliu.com/ScreenImgs/UserGroups62.gif","line":3},{"title":"_**lotto decades**_","target":"https://saliu.com/decades.html","line":7},{"title":"Let's create lottery strategies and generate lotto combinations for winning drawings.","target":"https://saliu.com/HLINE.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07722432,-0.04912642,-0.05540099,-0.08036023,-0.0574169,0.02420615,-0.0535952,-0.01701234,0.00380141,-0.0444906,0.02505789,-0.01645565,0.05176861,-0.01793843,-0.00795606,-0.05402912,-0.00448809,-0.00829307,-0.09554145,-0.0123995,0.09686989,-0.01527949,-0.0198132,-0.07044274,0.04005192,0.00256562,-0.04988066,-0.06276153,-0.01238319,-0.24459668,0.04019349,0.06667233,0.05582277,-0.03764823,-0.04511425,-0.03706406,-0.03127959,0.0247525,-0.06392973,0.04764043,-0.00730003,0.04602586,0.02301817,-0.03825409,0.00366901,-0.01435625,-0.03191041,0.00470037,0.06990163,0.03132161,-0.03295168,-0.00913381,0.00599506,0.07076072,0.01839254,0.00876277,0.02794421,0.06613466,0.0137886,0.04497135,0.03086622,0.04996475,-0.14188774,0.06075696,-0.00736009,0.01520038,-0.02945083,-0.01679194,0.03802035,0.01574005,-0.0350388,0.04064797,-0.03452906,0.04327909,0.05960053,-0.0482078,-0.06909482,-0.04700727,-0.0386548,0.00520766,-0.0787835,-0.03627642,-0.03717024,-0.01145481,0.05677897,0.04309564,0.07307043,0.03036972,0.03657284,-0.0443575,-0.0252974,0.07180133,0.03695716,0.03305231,0.0647476,-0.02287315,0.07097399,-0.00035548,-0.01128268,0.11217305,-0.01002525,-0.02540273,0.03344194,0.0070501,-0.0269238,-0.02138236,-0.06253719,-0.00233289,-0.05189393,0.02037267,0.03799706,0.03030998,0.04332205,-0.07708337,-0.04116124,-0.02423513,-0.03665672,0.02120971,0.00834783,-0.01339325,-0.01301522,-0.0254137,-0.0083072,-0.00812437,0.04097112,0.00921508,0.00814826,0.05586757,0.04505472,0.01499992,0.0303624,0.0111298,-0.11541391,-0.03125785,0.00724699,-0.01199272,-0.01325236,-0.00436567,-0.01882965,-0.01912951,-0.02729624,-0.02748694,0.07709451,-0.09244694,0.02622509,0.07695458,-0.03208201,-0.02359868,0.01446002,-0.00053672,0.00961041,-0.02132614,-0.0383235,-0.06516817,0.00719711,0.0088977,0.12690632,0.07107867,-0.05237697,0.00367495,-0.03522478,-0.03275718,-0.01418705,0.09777022,0.00586672,-0.06204751,-0.04347942,0.05143997,-0.05544584,-0.07709616,0.00027425,-0.00900594,0.01378166,0.05588899,0.12818521,-0.03487961,-0.07968815,-0.03218136,-0.00749471,0.01858581,-0.01725046,0.00714155,-0.03709569,-0.01761215,-0.04948407,-0.08724537,0.02214786,-0.01188058,0.01637106,0.04067536,-0.04620806,0.02495224,-0.03111533,-0.00039018,-0.02031458,-0.02502619,-0.00875561,-0.02923781,0.05719957,-0.03751477,0.04818788,-0.00249222,0.03493857,0.0362762,-0.00700497,0.06347578,0.0114206,-0.0270102,0.12438387,0.00806282,-0.01649121,0.00705072,0.06284026,0.09181663,-0.02917926,0.05074456,-0.02457515,0.03701944,0.04220523,0.03525803,0.00626279,-0.02764288,-0.07981903,-0.19755992,-0.02124565,0.01974504,0.0078987,0.00271068,-0.0318009,0.04291688,-0.04407398,0.01625248,0.07687314,0.0956653,-0.03503206,-0.02584315,0.03147594,-0.04532623,0.00631811,-0.08574173,-0.01842926,-0.04816544,0.07777131,-0.02205582,0.03009751,-0.01382721,-0.06056142,0.04160088,-0.02468802,0.12071119,0.01126844,0.00605365,-0.05556776,0.08449162,-0.01700449,-0.03900959,0.00122484,0.00636008,0.02916706,-0.04246866,0.02221679,0.00731944,-0.00023852,-0.06400909,-0.02512979,0.0033372,-0.09791368,0.00675508,0.03288729,-0.02485966,0.00365787,0.02138695,0.00063383,0.03694306,-0.01503583,0.05344071,0.02607846,0.03102147,-0.05101047,-0.06371075,0.01028837,0.00839941,0.03538544,0.00130527,-0.05979045,0.04773664,-0.01836042,0.02185573,0.04996009,-0.00931137,-0.02392775,0.06348278,-0.00547009,-0.00153626,0.08685999,-0.01875596,0.06171374,-0.03721518,-0.00107241,0.02984947,-0.03203041,-0.01060613,-0.02337759,-0.00810143,-0.069067,0.02764614,0.05700909,0.01711461,0.00013704,0.04938551,0.05911575,0.05145609,-0.00643862,0.03997606,0.01991729,0.00696986,0.07023763,0.00216953,-0.00096774,-0.27239302,0.00380643,-0.04855571,0.05753122,-0.00065591,-0.01478761,0.02810185,0.01398778,0.0167141,-0.01655563,0.06247615,0.00902907,0.03620535,-0.11649466,0.05963175,-0.00123551,-0.00581477,-0.02486681,0.05154705,-0.01474198,0.07987501,0.04449958,0.28444541,-0.01084986,0.00412965,0.04810761,0.02359667,0.00717809,0.03268636,0.04071473,0.02030186,-0.02126212,0.09463658,-0.03363639,-0.02274502,0.00228313,-0.00264449,-0.02160731,-0.03710736,0.0447583,-0.0847379,-0.02895618,-0.06049716,-0.00239201,0.11934724,0.02486526,-0.02036652,-0.08034438,0.00285467,0.04720508,-0.06110568,-0.0212725,-0.03923056,-0.0301559,0.05767047,0.03491224,-0.00448161,0.02534643,-0.02190455,0.03894813,-0.00773406,-0.00395982,0.07552771,0.03406271,-0.04860459],"last_embed":{"hash":"1gr9zfv","tokens":421}}},"text":null,"length":0,"last_read":{"hash":"1gr9zfv","at":1753423532118},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>U = Generate Combinations from <i>User's Groups</i></u>#{1}","lines":[148,155],"size":1310,"outlinks":[{"title":"Program 4: Software generates lotto combinations from groups of lotto numbers created by a player.","target":"https://saliu.com/ScreenImgs/UserGroups62.gif","line":1},{"title":"_**lotto decades**_","target":"https://saliu.com/decades.html","line":5},{"title":"Let's create lottery strategies and generate lotto combinations for winning drawings.","target":"https://saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.076322,-0.03163332,-0.01740677,-0.0951334,-0.01170555,0.03031131,0.00158798,0.02185784,0.01704471,-0.04436767,0.03013257,-0.02126929,0.0333523,0.00015926,-0.02519317,-0.02532465,0.02080654,-0.0220873,-0.01816241,-0.00849793,0.09858924,-0.02203821,-0.04907315,-0.08631058,0.07649807,0.0260973,-0.07912657,-0.02573101,-0.01361535,-0.24630877,0.02870318,0.01188432,0.02640191,-0.05953945,-0.00321796,-0.08782071,-0.02566458,0.03598811,0.00367705,-0.00094702,-0.00216824,0.00729586,0.01095217,-0.02017081,0.01479014,-0.06934035,0.01552541,-0.0068857,0.02089274,0.02387929,-0.06155343,0.0238808,0.00591279,0.02104458,0.04827872,0.02405266,0.06512012,0.04243857,0.01618508,0.04636818,0.01941996,0.0425443,-0.15811336,0.01039202,0.01017986,0.0305589,-0.03518987,-0.04993599,0.0214159,0.02635618,0.01582316,0.01939879,-0.03812995,0.05146335,0.07852895,-0.03673312,-0.0601436,-0.06729917,-0.01567223,0.02627245,-0.0930563,-0.03638856,-0.01749278,0.02411414,0.06543931,0.03322629,0.0400524,0.04905627,0.09820241,-0.05531169,0.05547056,0.04603751,0.01687734,0.03711112,0.05647744,0.01452173,0.06102872,-0.01545479,-0.02674835,0.12570171,0.00491476,-0.04111202,0.02238975,0.0110488,0.02224811,-0.0345675,-0.06941092,-0.02930205,-0.05211329,0.00013874,0.04044669,0.03079496,0.07278174,-0.06177783,-0.00398808,0.00113127,-0.01898775,0.00501729,0.00168589,0.0128809,-0.02678479,-0.02505489,0.00142896,-0.0218136,0.04638791,-0.00136901,0.00949463,0.03105338,0.01544993,0.02585291,0.01546717,-0.02117441,-0.11367449,-0.04484878,-0.01853386,-0.03422829,0.00381697,0.02151944,0.01818645,0.00547463,-0.08135889,0.00203406,0.01341766,-0.11019046,-0.02174465,0.10462443,-0.05133158,-0.00542112,0.02222913,-0.04788769,-0.00069838,-0.00553111,-0.02038032,-0.07057885,-0.00079813,0.00541524,0.07784431,0.10431729,-0.04285793,-0.02628622,-0.03444928,-0.01809349,-0.00464595,0.09511234,-0.00678055,-0.08384953,-0.01429426,0.03002344,-0.02124326,-0.07825564,0.00166135,0.02105199,-0.00053328,-0.01382602,0.0649617,-0.04509306,-0.03503731,-0.03010153,-0.03808924,0.00487338,-0.01446378,0.00511695,-0.04479353,0.04642595,-0.0323359,-0.08248363,0.03913456,-0.01789061,0.03103917,0.02664547,-0.09274816,-0.00262904,-0.05128236,0.02581661,-0.01899012,-0.01477755,-0.00494587,0.02674701,0.06561078,-0.03950436,0.11182342,0.0359896,0.05656454,0.00292685,0.00792227,0.06763341,-0.02271264,-0.0298209,0.10700639,0.02889746,-0.012408,-0.00234852,0.04955274,0.09933703,-0.03254853,0.01355059,-0.01021426,0.05398399,-0.00977035,0.01686601,0.02004763,0.05000168,-0.05876281,-0.2263301,-0.05858273,-0.01382569,0.03473607,-0.01865775,-0.01345986,0.03963094,-0.03861779,0.02701737,0.07219434,0.08322047,-0.00790376,-0.02483045,0.03131175,-0.01920689,0.03161324,-0.1021934,-0.04355065,-0.05539794,0.07007381,-0.01505422,0.02766492,-0.02542732,-0.04904953,0.02087192,-0.01387175,0.13037643,0.00654466,0.0494758,-0.0493097,0.04880748,-0.03055206,-0.01117222,0.00089236,0.00647295,0.06808653,-0.06198828,0.03002086,-0.02144029,-0.02213856,-0.0647817,-0.01637412,0.00266734,-0.09078842,-0.02546731,0.06248839,0.01545072,-0.02439239,0.03585772,-0.00991603,0.01760101,-0.00956591,0.01992735,0.03204173,0.0561501,-0.04069629,-0.08314487,-0.02135193,0.00758934,0.00764159,-0.01776974,-0.08300953,0.06387267,-0.00188156,0.07372463,0.00695124,-0.00690095,-0.00398328,0.06741399,0.02585818,0.00143346,0.10494332,-0.05689101,0.03692194,-0.04966765,0.0143747,0.08594826,-0.03108096,-0.0411793,-0.02568594,-0.01244633,-0.02223065,0.06597159,-0.00163459,0.03184785,0.0214278,0.0513223,-0.01367412,0.06282395,0.00467869,0.02267,-0.00024352,-0.01139189,0.00887205,-0.00314653,0.01526134,-0.26514384,-0.04356874,-0.06873986,0.03790615,-0.01116854,-0.032891,0.05544438,0.01945705,-0.01038644,0.00400256,0.05158384,-0.01128645,0.05836351,-0.12016042,0.0060587,0.0109328,0.02324297,-0.03573126,0.06559701,-0.01827078,0.08982297,0.051299,0.26300079,-0.01031709,-0.01848104,0.01752226,0.0171269,0.01183879,0.01228825,0.03194154,0.03252716,0.02574826,0.08981157,-0.01297164,-0.02486909,0.01812846,0.00920334,-0.01970647,0.02534437,0.04186576,-0.04789964,0.00182752,-0.0390948,-0.02709784,0.11959443,-0.00341397,-0.04554473,-0.03607168,0.0179574,0.06567179,-0.07335852,-0.04145174,-0.02230614,-0.01852875,0.05455554,0.04343618,0.0066342,0.01831234,-0.00738224,0.00682137,-0.02272562,0.00521079,0.04678076,0.02329171,-0.02188813],"last_embed":{"hash":"qc1p3n","tokens":175}}},"text":null,"length":0,"last_read":{"hash":"qc1p3n","at":1753423532273},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>","lines":[156,163],"size":533,"outlinks":[{"title":"Lotto #5: Software checks strategies for odd/even, low/high, lottery sums.","target":"https://saliu.com/ScreenImgs/UserGroups65.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07745712,-0.03216928,-0.01609251,-0.09395944,-0.00988081,0.0301201,0.00216732,0.02448362,0.0162877,-0.04292916,0.03126795,-0.02388882,0.03326417,0.00394737,-0.02475295,-0.02642481,0.02062205,-0.02464547,-0.01494921,-0.00830867,0.0990208,-0.02372178,-0.04699963,-0.08533164,0.07648329,0.02656708,-0.07700363,-0.0293864,-0.01575056,-0.24526148,0.02653903,0.01215156,0.02458474,-0.06001803,-0.00278088,-0.0899303,-0.0262415,0.0337874,0.00512908,-0.00026574,-0.00462159,0.00611263,0.01277314,-0.02281734,0.01387394,-0.0665947,0.01487987,-0.00771004,0.0237728,0.02349092,-0.06180113,0.02412563,0.00446464,0.02095594,0.04757705,0.02718051,0.06729485,0.04445931,0.01677694,0.04558678,0.01767747,0.04076721,-0.16078036,0.01298442,0.00991889,0.03008418,-0.03371223,-0.05016269,0.01918624,0.02380184,0.01428034,0.02023706,-0.03901663,0.05154051,0.07920069,-0.03705656,-0.06219365,-0.0686005,-0.01434239,0.0274874,-0.09177417,-0.03618706,-0.01692796,0.02404844,0.06362026,0.03328041,0.04000627,0.05139231,0.09734889,-0.05602155,0.05431044,0.04516393,0.01310655,0.03970638,0.05794971,0.01324572,0.06078057,-0.01368361,-0.02689992,0.12414228,0.00439365,-0.04082224,0.02127181,0.01183786,0.02454858,-0.03580574,-0.07203574,-0.02863941,-0.05165196,-0.00006564,0.04050949,0.03223337,0.07151981,-0.06447329,-0.00598628,0.00273041,-0.01634009,0.00466021,0.00060957,0.00947962,-0.02754496,-0.02629903,0.00327669,-0.021197,0.04804852,-0.00394964,0.01140335,0.03227665,0.01437464,0.02824409,0.0154881,-0.02396937,-0.11511781,-0.04699251,-0.01717909,-0.03459356,0.00308593,0.01968852,0.0170864,0.00520454,-0.08308877,0.00161091,0.01586178,-0.10964885,-0.02322655,0.1070012,-0.05005804,-0.00459629,0.02434537,-0.04650481,-0.00165109,-0.00414467,-0.02165507,-0.06991271,-0.00367097,0.004572,0.07748504,0.10204192,-0.04339303,-0.02386429,-0.0309292,-0.01878762,-0.00700503,0.09642687,-0.00785202,-0.08409613,-0.01757017,0.02901942,-0.02128159,-0.07910354,0.00115293,0.02172979,-0.00022795,-0.01279157,0.0629801,-0.04219865,-0.03569242,-0.02824054,-0.04152447,0.00511208,-0.01618132,0.00658109,-0.04320054,0.04624493,-0.0298239,-0.08320958,0.03833538,-0.02072423,0.03282888,0.02597295,-0.09446158,-0.00271906,-0.05653312,0.02758689,-0.01647201,-0.0124308,-0.00547488,0.02548412,0.06601413,-0.03667914,0.10982227,0.03642786,0.05441293,0.00209297,0.00955783,0.06712049,-0.02084727,-0.02674119,0.10658819,0.0283564,-0.01248829,-0.00126805,0.04867582,0.09692383,-0.03450876,0.01445977,-0.00954845,0.05287572,-0.00980077,0.0175857,0.02383086,0.05116908,-0.05806738,-0.22632807,-0.05921027,-0.01550496,0.03769904,-0.01939117,-0.01381879,0.03958552,-0.0370757,0.02840216,0.07094554,0.084319,-0.00349079,-0.02497156,0.03105093,-0.0166981,0.02902518,-0.10253651,-0.0428769,-0.05628967,0.07061286,-0.0177703,0.02855027,-0.02305729,-0.04755007,0.02264171,-0.0135052,0.13146231,0.00532446,0.04761944,-0.047995,0.0484041,-0.02716843,-0.01056284,-0.00163295,0.00797018,0.06943569,-0.06208988,0.02698713,-0.02359902,-0.02189616,-0.06476615,-0.01798619,0.00137315,-0.0928521,-0.02518411,0.06148097,0.01541724,-0.02354994,0.03620872,-0.00860178,0.01956412,-0.0128313,0.0216767,0.02984851,0.05603307,-0.04023987,-0.08347524,-0.02195674,0.00511291,0.00386535,-0.0196304,-0.08014051,0.06421943,-0.00077688,0.07372136,0.00260974,-0.00921043,-0.00414439,0.06912579,0.02656323,0.00183191,0.10670753,-0.05789288,0.03716531,-0.0475562,0.01313301,0.08616963,-0.03193933,-0.04091675,-0.02617423,-0.01166814,-0.02209945,0.06587466,0.00107023,0.03562724,0.02274958,0.05282047,-0.01355527,0.06369234,0.00316977,0.02271757,-0.00221678,-0.01328498,0.00937336,0.00021467,0.01318257,-0.26704291,-0.04098733,-0.06863604,0.03671706,-0.01038949,-0.03310836,0.05440157,0.01848846,-0.0103487,0.00400647,0.05275629,-0.0118047,0.05738907,-0.11905356,0.00693908,0.01333238,0.02471805,-0.03888573,0.06392079,-0.01634188,0.08860724,0.04883417,0.26082292,-0.00974034,-0.01566303,0.01862212,0.01878519,0.00999316,0.01129666,0.0341819,0.03357727,0.02325922,0.08892864,-0.01231629,-0.02680162,0.02041517,0.01024708,-0.01882039,0.02496941,0.04004869,-0.04756093,-0.00042735,-0.03562308,-0.02533938,0.12073194,0.00077757,-0.04313585,-0.03487414,0.01862365,0.06675678,-0.07241953,-0.04165925,-0.02419594,-0.01800378,0.05479068,0.0433151,0.00600888,0.01865216,-0.00544077,0.00625953,-0.02258042,0.00825481,0.04517398,0.02288367,-0.02056356],"last_embed":{"hash":"1o139cy","tokens":174}}},"text":null,"length":0,"last_read":{"hash":"1o139cy","at":1753423532334},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>S = <i>Strategy</i> Checking</u>#{1}","lines":[158,163],"size":493,"outlinks":[{"title":"Lotto #5: Software checks strategies for odd/even, low/high, lottery sums.","target":"https://saliu.com/ScreenImgs/UserGroups65.gif","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08742005,-0.01992363,-0.02993281,-0.08178481,-0.05469358,0.03465478,-0.00001383,-0.00865226,0.02012737,-0.01205557,0.00344626,-0.00708028,0.05334068,-0.00977248,-0.02881987,-0.04342653,0.01226133,-0.03501274,-0.08617109,-0.02375553,0.09394739,-0.02425872,-0.02136621,-0.09193124,0.03370308,-0.01237749,-0.0436351,-0.0566009,-0.02854668,-0.24446806,0.04997434,0.05664959,0.03489558,-0.05242983,-0.06141223,-0.0228405,-0.03621377,0.06469228,-0.05542959,0.02812973,0.00770399,0.03799145,0.03678553,-0.03291408,-0.01238763,-0.0384521,-0.00710182,-0.00392107,0.04568193,0.03593729,-0.04744162,-0.0319668,0.01627946,0.03551861,0.04279913,-0.00186903,0.03824817,0.09228531,0.02949026,0.06745674,0.02069763,0.06192023,-0.14891252,0.03423607,-0.00321435,0.01833674,-0.05088431,-0.02849295,0.01656363,0.02275927,-0.01985328,0.04008707,-0.02773258,0.04606101,0.06748953,-0.06086742,-0.04775551,-0.04814862,-0.03739154,0.02115,-0.05268967,-0.03548017,-0.0307861,0.02213535,0.04899204,0.03635046,0.04214424,0.0444524,0.0480726,-0.0538353,0.02088569,0.0483313,0.03199795,0.03883716,0.04751559,0.01003036,0.06924511,-0.0345464,-0.02345998,0.11962353,-0.0045248,-0.01498477,0.01632569,-0.01096446,0.02059847,-0.03723482,-0.06432983,-0.03520964,-0.05981106,0.03019236,0.04519896,0.01627339,0.05390726,-0.06982405,-0.05274127,-0.00245708,-0.0120641,0.00169819,0.01805632,-0.00024957,-0.02656176,-0.0003459,-0.01948702,-0.01845631,0.02918571,0.02782972,0.00058707,0.07208125,0.01771927,0.01773196,0.03072054,0.00715855,-0.1377279,-0.04309855,-0.01407511,-0.01945745,-0.03323857,-0.00653403,-0.03807966,0.0093516,-0.05392969,-0.01226687,0.07710023,-0.08750936,0.01105232,0.07681323,-0.01069621,-0.0043135,0.00992829,-0.03073475,0.00312483,-0.0007659,-0.0418399,-0.02175235,0.01092925,0.00813004,0.07983015,0.07956401,-0.05222167,-0.00108409,-0.0311886,-0.02781597,-0.01199547,0.09639184,-0.0071934,-0.07579532,-0.01860303,0.05949008,-0.04908092,-0.04731406,-0.02508549,0.03416391,-0.01005054,0.01541802,0.10025828,-0.04294907,-0.08151301,-0.03905003,0.00806434,0.00087931,-0.00548903,-0.00834338,-0.03744189,0.00140934,-0.04046346,-0.12319808,0.04569411,-0.02846525,0.03463184,0.05430903,-0.03241109,0.01390003,-0.01851063,0.00994418,-0.01142239,-0.01787524,-0.0260571,-0.03508958,0.07067004,-0.03740492,0.0338616,0.00698161,0.04307694,0.01623516,0.00288132,0.05484396,-0.03281838,-0.05125685,0.10772529,0.01645712,-0.02544531,0.01929518,0.07225103,0.11895219,-0.05570426,0.03923333,-0.02005082,0.02507387,0.01814341,0.010401,0.01205612,0.01508647,-0.04661433,-0.19695964,-0.01343171,-0.01088992,0.00831136,0.00788547,-0.03651914,0.01821334,-0.04658722,0.04677571,0.07716139,0.10982732,-0.03684805,-0.01991173,0.04671055,-0.02367186,0.00090743,-0.10837295,-0.02041065,-0.06914192,0.0847474,-0.01760113,-0.00563802,-0.01112333,-0.05114353,0.02109692,-0.01104245,0.11541135,0.02787439,0.01228446,-0.04479844,0.06967419,0.0152328,-0.01342798,-0.02198506,0.01478969,0.04162982,-0.03586529,0.0068832,-0.0156846,0.00223003,-0.06805046,-0.01990269,-0.00829399,-0.11820307,0.02796347,0.04776442,-0.02732629,0.00853793,0.01878506,0.02193511,0.04675289,-0.00923239,0.01686666,0.01985492,0.07835488,-0.0626316,-0.06607689,0.02712767,0.00384315,0.01414226,-0.02234132,-0.05791598,0.06570224,-0.03276514,0.04443006,0.02370367,0.01601348,-0.02949201,0.03760572,-0.00339808,-0.00898318,0.07733136,-0.01966524,0.060031,-0.04768428,0.0171531,0.06682398,-0.02041867,-0.0302983,-0.02707588,0.00455457,-0.05465504,0.03584794,0.06754902,0.02026392,0.02540394,0.059859,0.02831654,0.03295188,-0.02558204,0.00012693,0.01698625,0.00297413,0.03890675,-0.00840005,-0.00413716,-0.27977347,0.01124602,-0.0360624,0.06988409,-0.0038865,-0.02135482,0.04180263,0.01655438,0.03230004,-0.01592396,0.05412395,0.01195494,0.04451766,-0.12358169,0.01362873,-0.00502365,0.00155863,-0.03263846,0.07239957,-0.02607772,0.08728933,0.0644606,0.2652925,-0.00814118,0.00324799,0.02406069,0.01123223,-0.00707076,0.00541816,0.05427444,-0.00669365,0.01982522,0.09018971,-0.01853284,-0.03078338,0.02282504,0.01223626,-0.00888844,-0.03113031,0.03078805,-0.08028629,-0.00377853,-0.06534176,-0.02059814,0.09819608,0.0173895,-0.03784644,-0.06259285,0.02127954,0.06623166,-0.06958209,-0.05701486,-0.04358533,-0.05072593,0.05934422,0.04503032,0.02250165,0.0338284,-0.02752774,0.04673456,0.02068439,0.02991794,0.07565139,0.01999356,-0.00865755],"last_embed":{"hash":"1vln43e","tokens":414}}},"text":null,"length":0,"last_read":{"hash":"1vln43e","at":1753423532390},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>","lines":[164,186],"size":1948,"outlinks":[{"title":"Lotto #6: Software analyses how many combinations the lottery strategy generated.","target":"https://saliu.com/ScreenImgs/UserGroups66.gif","line":3},{"title":"Integrated lottery software pick-6 or 6-number lotto games.","target":"https://saliu.com/HLINE.gif","line":9},{"title":"**Lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":14},{"title":"When it comes to lotto software for 6-number games, the best program is Bright.","target":"https://saliu.com/HLINE.gif","line":16},{"title":"_**Lotto Software for 5-Number Games**_","target":"https://saliu.com/lotto5-software.html","line":19},{"title":"_**Lottery Software for 6-Number Lotto**_","target":"https://saliu.com/lotto6-software.html","line":20}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08686557,-0.01939717,-0.02826137,-0.08164665,-0.05499253,0.03661566,-0.00022882,-0.00896679,0.01976239,-0.0124735,0.00327614,-0.00598501,0.05397043,-0.00983428,-0.03046398,-0.0438362,0.01120025,-0.03480721,-0.08433316,-0.02294148,0.09116035,-0.02462982,-0.02257767,-0.0893858,0.03127885,-0.01418326,-0.04120067,-0.05683065,-0.03079234,-0.24511379,0.0505191,0.05621368,0.03692945,-0.05308678,-0.06307784,-0.02347591,-0.03654231,0.06507547,-0.05424159,0.02880398,0.00919249,0.03736072,0.03862539,-0.03166703,-0.01210187,-0.03752967,-0.00497963,-0.00441636,0.0473537,0.03466728,-0.04807489,-0.03055383,0.01675979,0.03526447,0.0429546,-0.00212061,0.0383071,0.0914999,0.02927965,0.06682263,0.02148073,0.06300116,-0.1509676,0.03486023,-0.00209647,0.01708522,-0.05004735,-0.02708568,0.01417988,0.02130497,-0.02015983,0.03780278,-0.02627043,0.04603773,0.06693611,-0.06009001,-0.05208232,-0.0503768,-0.03805721,0.02172367,-0.05378538,-0.03325434,-0.03080572,0.02268304,0.04754531,0.03502833,0.04210667,0.04482935,0.0453094,-0.05204445,0.0216068,0.04911463,0.02929276,0.03926412,0.04807064,0.00889862,0.0659619,-0.0343047,-0.02269358,0.12028836,-0.00502331,-0.01341941,0.01777045,-0.0132628,0.02082743,-0.03850918,-0.06474842,-0.03513029,-0.06127195,0.03049453,0.04515149,0.01634631,0.05308824,-0.07245557,-0.05262921,-0.00271574,-0.01167708,0.00075809,0.01613947,-0.00157208,-0.02764319,-0.00005893,-0.01856769,-0.01870869,0.02695809,0.02857154,0.00360915,0.07339911,0.01894357,0.01556892,0.02907316,0.00560338,-0.13931905,-0.04266669,-0.01432935,-0.01828536,-0.03158446,-0.00818041,-0.03909409,0.01057579,-0.05447157,-0.01260264,0.07706892,-0.08821424,0.0107827,0.07773833,-0.01028354,-0.00374077,0.01040675,-0.02806621,0.00147272,-0.00028503,-0.0412949,-0.02171828,0.01214007,0.00724499,0.0769332,0.07863669,-0.04995139,0.00116445,-0.0328733,-0.02855548,-0.01082225,0.09748029,-0.0059046,-0.07600503,-0.01866474,0.05800224,-0.04587596,-0.04814523,-0.02566902,0.03604783,-0.00871789,0.01558318,0.10078508,-0.0407988,-0.08246722,-0.0392018,0.00632028,0.00159867,-0.00689989,-0.0081215,-0.03925673,0.00132048,-0.03978394,-0.12454847,0.04724719,-0.02886771,0.03565885,0.05605301,-0.03095188,0.01485346,-0.01834632,0.01197366,-0.00997433,-0.01617319,-0.02641554,-0.03673942,0.07058723,-0.04032308,0.03178227,0.00816682,0.04158972,0.01440454,0.00162437,0.05594115,-0.02985649,-0.05174126,0.1063548,0.01691933,-0.02658343,0.02051218,0.07118323,0.11846219,-0.05441278,0.03797904,-0.01787342,0.02183615,0.0183432,0.00943545,0.01326602,0.01517777,-0.04291754,-0.1968096,-0.01562386,-0.01101545,0.00775624,0.00578004,-0.03564369,0.01632686,-0.04608301,0.05055245,0.07276382,0.11034309,-0.03531839,-0.01862954,0.04948778,-0.02231987,0.00148087,-0.10788074,-0.01995529,-0.07057197,0.08515349,-0.01681657,-0.00664817,-0.0099732,-0.05248858,0.02307861,-0.00913125,0.11681983,0.02609659,0.01077807,-0.04339847,0.07054072,0.01319478,-0.01309978,-0.0214289,0.01659057,0.04163354,-0.03406849,0.00656187,-0.01521348,0.00201113,-0.07066698,-0.01983283,-0.0095308,-0.11760139,0.02640084,0.04846849,-0.02763217,0.00989663,0.01912685,0.02094694,0.04538113,-0.00850871,0.01703364,0.02139047,0.07780646,-0.06178802,-0.06535834,0.02544168,0.00455897,0.01404592,-0.02402449,-0.05781817,0.06675617,-0.03155945,0.04445396,0.02159825,0.01535502,-0.02834085,0.03812005,-0.00303955,-0.00979704,0.07679734,-0.02292159,0.06226746,-0.05060447,0.01530672,0.06557745,-0.02171739,-0.02965115,-0.02593628,0.00202972,-0.05487105,0.03370309,0.06838647,0.02010317,0.02388343,0.05842143,0.02853102,0.03206058,-0.02632377,-0.00089955,0.01634657,0.00246247,0.03891074,-0.00878354,-0.0055129,-0.27896658,0.01310298,-0.03666094,0.0706359,-0.00423352,-0.02157551,0.04099135,0.01770914,0.03317027,-0.01708049,0.05671642,0.01165973,0.04444974,-0.12325957,0.01478808,-0.0063099,0.00385805,-0.03213301,0.07002012,-0.02615001,0.0882175,0.06459253,0.26592544,-0.00695747,0.00421878,0.02456049,0.01194781,-0.00705498,0.00224708,0.0538591,-0.00675408,0.02267354,0.08966815,-0.01864059,-0.03057486,0.02286423,0.01325234,-0.00702389,-0.03269321,0.03265327,-0.08214628,-0.00606408,-0.06345323,-0.02070093,0.09903456,0.02079826,-0.03659457,-0.06376921,0.02052024,0.06660142,-0.06717195,-0.05699065,-0.04397045,-0.05055208,0.06005171,0.04589335,0.02241609,0.03348214,-0.02588621,0.04889414,0.02041117,0.03241228,0.07722428,0.01908965,-0.00962103],"last_embed":{"hash":"2adkwg","tokens":403}}},"text":null,"length":0,"last_read":{"hash":"2adkwg","at":1753423532537},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>H = Strategy Hits in the Past</u>#{1}","lines":[166,176],"size":1380,"outlinks":[{"title":"Lotto #6: Software analyses how many combinations the lottery strategy generated.","target":"https://saliu.com/ScreenImgs/UserGroups66.gif","line":1},{"title":"Integrated lottery software pick-6 or 6-number lotto games.","target":"https://saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.128206,-0.0274507,-0.02009579,-0.04285277,-0.03424072,0.00041503,-0.03273391,0.00103952,0.01807196,-0.03046302,0.02216991,-0.03146817,0.039437,-0.01402447,-0.01659486,-0.06181826,0.01600844,0.02422124,-0.02104674,0.00233433,0.11225744,-0.01046355,-0.04717426,-0.06137549,0.07145299,-0.00323982,-0.05233843,-0.03981382,-0.02704104,-0.2289694,0.02068192,0.00922001,0.01851259,-0.05345536,-0.04508175,-0.02322737,-0.0253061,0.03551875,-0.04056428,0.0272489,0.01790968,0.02376053,0.01356846,-0.03765659,0.02081065,-0.0535574,0.01056425,-0.00420567,0.03936709,0.00009241,-0.0822258,0.03958676,0.01844911,0.03033917,0.06078588,0.02374712,0.05437612,0.11250285,0.03251449,0.06092302,0.00776887,0.04196465,-0.16546173,0.04394155,0.00352887,0.01719433,-0.00534172,-0.02506475,0.03087267,0.03726119,0.01711108,0.0296295,-0.02601649,0.04470089,0.04044102,-0.06101934,-0.01435947,-0.06478842,-0.03932125,-0.01581816,-0.06486998,-0.06156274,-0.01173866,-0.017066,0.05263522,0.05644015,0.04647997,0.02570918,0.08355076,-0.07225123,0.01121159,0.04285771,0.06955931,0.03429562,0.06127963,0.02097449,0.05025853,0.00124352,-0.01000229,0.12509063,0.04112017,-0.01364293,-0.01349274,-0.00151984,0.05749002,-0.07582453,-0.02650157,-0.05047844,-0.017883,0.05035085,0.04876823,0.01407248,0.04819228,-0.0432782,-0.02313712,-0.02584708,0.00277627,0.02039908,0.01809901,0.01148632,-0.03645948,-0.01543765,0.02105339,0.00428768,0.02452214,0.01364377,-0.00750981,0.05515179,0.00594792,0.0344732,0.05004693,-0.00684211,-0.13442382,-0.07064296,0.01135881,-0.00317816,-0.01678196,-0.01149333,0.00183154,0.01558613,-0.05089453,-0.03069769,0.03000477,-0.09615193,-0.02006874,0.06963767,0.00562494,-0.00521943,0.01781995,-0.02326142,0.01305653,-0.00634657,-0.02908018,-0.06169,-0.01316334,0.0000827,0.09839749,0.11177511,-0.04949466,-0.02188663,-0.04166003,-0.04147699,-0.02576453,0.107797,-0.0260334,-0.09394843,-0.04013692,0.04579896,-0.05368816,-0.08480999,-0.03530372,-0.0034917,-0.02496332,0.04362115,0.12299523,-0.04610422,-0.05249235,-0.06904384,-0.0076226,-0.01550907,-0.00897567,-0.03404816,-0.03096638,0.0039804,-0.03016757,-0.07684286,0.02294401,-0.0271911,0.01605261,0.0315493,-0.04724054,-0.02125281,-0.02796646,-0.01341602,-0.02700568,-0.02590028,-0.04742116,-0.03705553,0.05055122,-0.01744017,0.02179996,0.02064471,0.04479336,0.01506919,-0.01985359,0.05682737,-0.01834302,-0.05985231,0.11889338,0.01373394,-0.03032998,0.03662206,0.04625469,0.0919916,-0.00070879,0.0367818,0.0025903,0.06951308,-0.02263479,0.03296919,-0.02603998,0.01910109,-0.0969406,-0.21788967,-0.02777511,-0.04533787,0.00296143,0.00760638,-0.02411186,0.03130708,-0.04142002,0.04392513,0.07162032,0.09804588,-0.05699261,-0.04187904,0.05545506,-0.02962422,0.01128784,-0.07025681,-0.03120002,-0.06372784,0.02208481,0.0103847,0.02872367,-0.01561147,-0.06749113,0.00689116,-0.00465139,0.11158174,-0.01044856,0.05358095,-0.03332621,0.04726661,0.00293411,-0.02579748,-0.02570206,0.00949435,0.05534511,-0.03138095,0.00162763,-0.04103895,-0.02420215,-0.07460861,-0.00108246,0.01377308,-0.08705649,-0.04380594,0.04753,0.0048581,0.01639226,0.00603271,0.03735177,0.03435653,0.0211686,0.03298128,0.03707416,0.02734064,-0.00064753,-0.07241682,-0.02966901,-0.01433628,0.04672687,-0.032049,-0.05897668,0.04689504,-0.01483586,0.03429715,0.01385249,0.02645485,-0.01980674,0.06361096,0.02466941,-0.02510167,0.09904259,-0.01226676,0.05293496,-0.02472831,0.05803646,0.05272087,-0.02313659,-0.01794048,-0.02557173,-0.01534557,-0.04639321,0.0751958,0.06649726,0.04853294,0.03271466,0.05729147,0.00960311,0.00960794,0.00687923,0.01766974,0.00472123,-0.02395368,0.03053142,0.01609574,0.02860603,-0.28479266,-0.02070911,-0.04152689,0.05444713,-0.00607743,-0.03108252,0.02279837,-0.01543995,0.01825646,-0.02321544,0.06939471,0.00943812,0.0313834,-0.08140668,-0.03161022,-0.036509,-0.00200151,-0.01701685,0.08243953,0.02193796,0.04423871,0.05560668,0.26171464,0.01252609,-0.02760419,0.01864416,0.01481225,0.02025455,0.01358025,0.04788622,0.00832966,0.03196846,0.11062244,0.00723529,-0.01822785,0.00617467,-0.03870237,-0.01234553,0.02129056,0.03801869,-0.05139358,0.02635249,-0.02432556,0.01013521,0.10870121,0.0088489,-0.04614839,-0.05650719,0.05895058,0.04963077,-0.07430226,-0.04230694,-0.06358265,-0.00097699,0.03885552,0.04512841,-0.02047943,0.0172641,-0.02386509,-0.01397523,0.02044097,0.00887949,0.05905388,0.03801798,-0.01465059],"last_embed":{"hash":"sj8kgl","tokens":459}}},"text":null,"length":0,"last_read":{"hash":"sj8kgl","at":1753423532689},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>","lines":[187,209],"size":2188,"outlinks":[{"title":"**Visual Book, Manual for Lotto Software**","target":"https://saliu.com/forum/lotto-book.html","line":5},{"title":"**<u>MDIEditor Lotto</u>**: _**Software Tutorial, User Guide, Instructions**_","target":"https://saliu.com/MDI-lotto-guide.html","line":6},{"title":"_**Lotto, Lottery Software Tutorial**_","target":"https://saliu.com/bbs/messages/818.html","line":7},{"title":"_**Filtering, Filters in Lottery Software, Lotto Programs**_","target":"https://saliu.com/filters.html","line":8},{"title":"_**Lotto, Lottery Strategy in Reverse: <u>Not-to-Win</u> Leads to <u>Not-to-Lose</u> or <u>WIN</u>**_","target":"https://saliu.com/reverse-strategy.html","line":9},{"title":"_**Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_","target":"https://saliu.com/strategy.html","line":10},{"title":"_**Lottery Strategy, Systems Based on Number <u>Frequency</u>**_","target":"https://saliu.com/frequency-lottery.html","line":11},{"title":"_**The Best Strategy for Lottery, Lotto, Gambling**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":12},{"title":"_**Cross-Reference Lottery, Lotto Strategy Files**_","target":"https://saliu.com/cross-lines.html","line":13},{"title":"_**Lottery, Lotto Sums, Sum-Totals**_","target":"https://saliu.com/forum/lottery-sums.html","line":14},{"title":"_**Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_","target":"https://saliu.com/bbs/messages/626.html","line":15},{"title":"_**Lotto Program for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":16},{"title":"Finally, lotto software to work with all groups of numbers: odd, even, low, high, frequency, sums, sum totals, decades.","target":"https://saliu.com/HLINE.gif","line":18},{"title":"Forums","target":"https://forums.saliu.com/","line":20},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":20},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":20},{"title":"Contents","target":"https://saliu.com/content/index.html","line":20},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":20},{"title":"Home","target":"https://saliu.com/index.htm","line":20},{"title":"Search","target":"https://saliu.com/Search.htm","line":20},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":20},{"title":"Get now this powerful lotto 5, 6 software for your number groups.","target":"https://saliu.com/HLINE.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12905571,-0.05194081,-0.02965956,-0.05301676,-0.05040067,0.00756913,-0.03623101,0.00510706,0.0152748,-0.06619713,0.0325454,-0.04128441,0.03167835,0.00516933,-0.01655824,-0.05789624,0.01318999,0.02100107,-0.03933073,0.00463231,0.10230685,-0.00057115,-0.04325666,-0.05414961,0.07661597,-0.00441093,-0.07247002,-0.04322667,-0.02717312,-0.22728318,0.02143295,0.0319067,0.04647814,-0.04020757,-0.0496439,-0.02455043,-0.00774969,0.04784267,-0.02775307,0.04033196,0.01668386,0.02666603,-0.01765758,-0.03029636,0.00748077,-0.05095554,0.01342336,0.01271443,0.03191049,0.01133798,-0.07111316,0.03385061,-0.00480108,0.05153853,0.02666561,0.01398506,0.04344891,0.07885536,0.01621009,0.06277511,0.02996163,0.04906853,-0.15963222,0.03935066,0.03354201,0.02239349,-0.01865873,-0.03455193,0.03424447,0.01190558,0.00800896,0.0312289,-0.03408266,0.0534046,0.07319001,-0.0624291,-0.0504594,-0.0612088,-0.04140937,-0.02486034,-0.07386343,-0.0698231,-0.03928001,-0.01621867,0.05486657,0.03576351,0.06880711,0.03051302,0.08254354,-0.08287704,0.00004364,0.07235629,0.0366895,0.0394955,0.07186344,-0.00487193,0.05983308,-0.01385482,-0.00305951,0.13714121,0.01533377,-0.02879799,-0.00564754,0.00473631,0.01959326,-0.03378933,-0.04902471,-0.03472596,-0.04789992,0.0281805,0.04852539,0.02703831,0.04984726,-0.05236441,-0.03658676,-0.07404252,-0.01301324,0.00379294,0.02569068,-0.00674394,-0.0471484,-0.0064269,0.02395858,0.00377214,0.03479093,0.01374215,-0.01112331,0.05753007,0.01704184,0.048985,0.05283504,0.01117466,-0.11794737,-0.06018402,0.00389087,-0.00532382,-0.02117197,0.01039459,-0.00313686,0.02874245,-0.02971452,-0.03715746,0.03488294,-0.0938438,0.00436845,0.07222299,-0.03575243,-0.00839104,0.02503424,0.00911609,-0.01283775,-0.01284233,-0.01604652,-0.05555915,-0.00538302,-0.01786023,0.13923158,0.08644661,-0.05209989,0.00603146,-0.03534124,-0.03849259,-0.01436404,0.12076622,0.00157763,-0.07551565,-0.03348903,0.04908628,-0.03054445,-0.08256855,-0.00172965,-0.00587056,-0.0061999,0.04165467,0.10838405,-0.04436165,-0.04372677,-0.06341564,-0.00810421,-0.00038748,-0.01820668,-0.00516368,-0.01984377,0.00934402,-0.05413749,-0.08004183,0.02076281,-0.02526046,0.00713372,0.03280081,-0.04283328,-0.00641273,-0.04786925,-0.01357374,-0.03551088,-0.02515996,-0.05296826,-0.01501526,0.07717282,-0.01068648,0.01126236,0.0138276,0.07049304,0.01101222,0.0072531,0.05093087,-0.02401089,-0.05826029,0.0966423,0.00037572,-0.00134548,0.02953548,0.05939202,0.10058101,-0.02994277,0.04944949,0.00131762,0.06306455,0.00797345,0.02873276,-0.00071745,-0.00123237,-0.10676966,-0.20778744,-0.02188832,-0.02848637,-0.02147171,0.00358644,-0.01202068,0.06241289,-0.04113656,0.0141298,0.07564403,0.09334803,-0.05788231,-0.01918401,0.02587549,-0.0274997,0.00549254,-0.0785531,-0.02970458,-0.03938082,0.06600928,-0.00143616,0.03166711,-0.0250362,-0.06766938,0.01401508,0.00165813,0.11111061,0.01474525,0.02299547,-0.06379878,0.04064348,-0.02982958,-0.04178034,-0.00361459,0.02103519,0.05189748,-0.05375575,0.01168345,-0.00873689,-0.02045,-0.09736092,-0.00148262,0.00406204,-0.07655006,-0.02723577,0.05281841,-0.00553402,-0.01791509,0.01339949,0.02156151,0.0428826,0.02508732,0.03794698,0.0428111,0.02286272,-0.01204459,-0.0610622,-0.02007108,-0.01435859,0.05439529,-0.01188987,-0.05839182,0.05145555,-0.00585053,0.02706064,0.03055567,0.01171321,-0.03703522,0.05713839,-0.00203049,-0.01207476,0.11807691,-0.01798049,0.06044757,-0.00072327,0.02853414,0.04130396,-0.02864017,-0.00629283,-0.00226397,-0.00827037,-0.08200306,0.0762407,0.05751535,0.03958813,0.02440864,0.03589628,0.02698573,0.0149006,0.0219649,0.04034049,-0.00260321,-0.02123863,0.05389336,0.00328029,0.02019255,-0.25635949,0.0022526,-0.05069233,0.0377804,-0.01566323,-0.0216115,0.01849485,-0.01118169,0.00014833,-0.00574211,0.08690217,0.00582698,0.04637706,-0.10368045,-0.00797222,-0.01924127,-0.00892473,-0.03051359,0.05959691,0.00631923,0.04118403,0.05180528,0.27663159,0.01255871,-0.02169008,0.02170183,0.03310865,0.0186174,0.03809743,0.02282155,0.00958112,0.0043657,0.09130792,-0.01314554,-0.02889823,-0.00165475,-0.02716321,-0.04437705,0.013684,0.04556026,-0.0427595,0.01112784,-0.04512437,0.01589118,0.12301819,0.01526786,-0.03596469,-0.06192786,0.04847765,0.04909401,-0.06243258,-0.05463101,-0.04075423,0.0003084,0.06667984,0.022468,-0.02458447,0.03124224,-0.03208185,-0.00715951,-0.00798337,0.01282517,0.04272025,0.01422393,-0.03098374],"last_embed":{"hash":"n2sv5j","tokens":305}}},"text":null,"length":0,"last_read":{"hash":"n2sv5j","at":1753423532862},"key":"notes/saliu/Lotto Software for Odd, Even, Low, High Numbers, Sums.md#Lotto Software for Odd, Even, Low, High Numbers, Sums#<u>Resources in Lottery Software, Lotto Strategies, Systems</u>#{14}","lines":[204,209],"size":643,"outlinks":[{"title":"Finally, lotto software to work with all groups of numbers: odd, even, low, high, frequency, sums, sum totals, decades.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Get now this powerful lotto 5, 6 software for your number groups.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
