"smart_sources:Obsidian/Embedded queries in obsidian.md": {"path":"Obsidian/Embedded queries in obsidian.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04662908,-0.0100309,-0.00766765,-0.02786254,-0.02425001,0.01351132,-0.04969261,0.00420307,-0.03605327,0.08572523,0.02378681,0.02345094,0.05023258,0.0210134,0.0266291,0.05723012,-0.03713471,0.01086075,0.07167847,0.00969386,0.05629513,-0.03776087,0.0271477,-0.05342859,-0.02120093,0.07009846,-0.00932256,-0.06590945,-0.01483038,-0.22823982,0.0523608,-0.01455536,-0.00927131,-0.02225544,-0.0364003,-0.08087008,0.01897101,0.00315379,-0.03565669,0.07831497,-0.02132771,-0.03793614,-0.06020961,-0.0148686,-0.06634659,-0.01506707,-0.01585146,-0.0310332,0.08031352,-0.01925849,-0.00873628,0.00145195,-0.0054039,-0.03956059,0.04863635,-0.02332573,0.08057871,0.06129387,-0.00699239,0.04639342,0.09805013,0.01976646,-0.10932561,0.1260986,-0.00740795,0.00442778,0.00262441,-0.03880072,-0.01458175,-0.03139557,0.02909583,0.05045066,-0.04238477,0.05416391,0.01530802,-0.03801928,0.00733744,0.02913275,-0.00934694,-0.00438179,-0.05442677,0.03045464,-0.00220482,-0.01084902,-0.00837222,-0.08661001,-0.02720878,0.02500942,0.0940537,0.00014759,-0.01803667,-0.03875016,-0.02105741,0.01442193,-0.01126552,-0.00435012,0.06386784,0.01723677,0.01135477,0.15927847,-0.0091094,0.06000447,0.02166702,-0.01632557,-0.01230865,0.00413994,-0.02304056,-0.01726108,-0.03242085,0.02058909,-0.00240907,0.03720173,0.01210543,-0.08160991,0.02860711,-0.01276228,-0.02597485,-0.01487732,-0.03081339,0.00742165,-0.02649161,0.00768277,0.0065151,-0.00734433,0.08447376,-0.01616175,0.03191433,0.02897925,0.00673412,0.03673389,0.07525115,-0.02398351,-0.05965974,-0.05721033,-0.05085894,-0.01188875,0.0163085,-0.01617087,0.01662335,-0.01770622,-0.06454234,-0.01320733,0.09084884,-0.03934834,-0.02112501,0.08798771,-0.03144864,0.00168464,-0.0276986,-0.11602739,-0.0550656,0.04276235,-0.04154254,-0.03107327,-0.01346903,0.02573603,0.01319876,0.04460396,-0.11644075,0.00310354,-0.00882463,-0.02769584,0.01646476,0.11335882,-0.03771797,-0.08344501,-0.0592113,0.06294625,0.00184174,-0.0536875,-0.019178,0.00134587,-0.07117544,0.08761641,0.07524368,-0.0224933,-0.04764696,-0.06195757,-0.01341621,0.03540304,0.00171029,-0.01855544,0.01348792,0.01136701,-0.0442559,-0.06996111,-0.0257817,-0.02476653,0.03959518,0.02179723,-0.0072279,-0.05119327,-0.05532904,0.04807691,0.00467058,0.01769928,0.06183026,0.00509798,0.01830818,-0.00962465,0.11453403,-0.04409043,-0.01130438,0.05466294,-0.04766207,0.03039184,-0.05220501,-0.02956133,-0.02063012,0.02859057,-0.05763074,-0.00386408,-0.01187518,-0.01773763,-0.02991501,-0.06033531,0.02262291,0.0413154,0.00559711,0.08883321,-0.02326051,-0.07301399,0.0236806,-0.21088617,-0.03326185,-0.01046046,0.02768498,0.00867897,-0.10240225,0.02979624,0.00840876,-0.06636967,0.09892894,0.05917284,0.12534329,0.0353288,0.03753645,-0.08126761,0.0034123,0.03001475,-0.0458229,-0.0640467,-0.02484502,-0.01578763,-0.0037605,-0.0242029,-0.07675263,0.01718174,-0.00486773,0.20098332,0.04911879,0.03845621,-0.05667989,-0.00511283,0.05343847,-0.01687323,-0.10482554,-0.03748159,-0.0008988,-0.00978829,0.14592178,-0.05048598,-0.01741179,-0.06518937,0.01832782,-0.00014526,-0.0907514,0.02471601,0.01119113,0.00185721,-0.00116425,0.00274807,0.06933523,0.01466113,0.00237846,0.05335011,0.07397592,0.02121721,-0.02060802,-0.03532699,-0.08930521,-0.03056278,0.05725187,-0.06831081,0.00238383,0.00495519,-0.04072125,0.03519013,0.03591836,0.02735369,-0.00731038,0.06902412,-0.05108018,-0.07659883,0.06412767,-0.05141652,0.04534774,-0.01052503,0.0209692,0.0109738,0.00653216,0.02544232,0.03691655,0.01135653,-0.05527819,0.10293194,0.00150425,0.01808102,0.03606023,0.04057794,0.01879988,0.02240315,0.00195398,-0.04315506,0.02106148,-0.05990982,-0.06780424,0.08464862,0.05416378,-0.2371472,0.02245962,0.04869971,-0.01894694,-0.00167573,-0.01112214,0.01391443,-0.01173768,0.0647371,0.06837171,0.04268527,0.01829578,-0.00706191,-0.02643405,-0.01750269,0.07704343,0.02410244,0.00308774,0.0220015,-0.00936521,0.04489757,0.04510598,0.2073296,0.0150285,0.02784403,0.02010453,-0.01388146,-0.00263611,-0.01160221,0.02667934,-0.00552303,-0.01181861,0.12338383,-0.01098116,0.01237104,0.03039414,0.01674199,0.04036124,0.04129596,-0.02023662,-0.02484218,-0.00213555,-0.0535278,0.07924355,0.04642151,0.02150202,-0.05263971,-0.05626933,-0.02428446,0.0031508,-0.06891564,0.00670756,0.00763275,0.01150476,0.00599637,-0.00155599,-0.05057438,0.00150721,-0.05598376,0.01685414,-0.00974253,0.01504933,0.02011346,0.02221609,-0.00711079],"last_embed":{"hash":"684e9e57ba93a7feb3313810bf7d28aca5e2f39a2532d2b7791e0fef0a4e9607","tokens":262}}},"last_read":{"hash":"684e9e57ba93a7feb3313810bf7d28aca5e2f39a2532d2b7791e0fef0a4e9607","at":1745995207230},"class_name":"SmartSource2","outlinks":[{"title":"Obsidian","target":"https://notes.nicolevanderhoeven.com/Obsidian","line":2},{"title":"Obsidian Dataview","target":"https://github.com/blacksmithgu/obsidian-dataview","line":9},{"title":"Obsidian Dataview","target":"https://notes.nicolevanderhoeven.com/Obsidian+Dataview","line":11},{"title":"Dataview table output.png","target":"https://publish-01.obsidian.md/access/186a0d1b800fa85e50d49cb464898e4c/assets/Dataview%20table%20output.png","line":22}],"blocks":{"#":[2,22]},"last_import":{"mtime":1706142787891,"size":814,"at":1740449883023,"hash":"684e9e57ba93a7feb3313810bf7d28aca5e2f39a2532d2b7791e0fef0a4e9607"},"key":"Obsidian/Embedded queries in obsidian.md"},