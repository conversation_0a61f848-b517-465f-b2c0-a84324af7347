
"smart_sources:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md": {"path":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09172382,-0.04790052,-0.04615688,-0.03892022,-0.01342724,0.04034325,-0.03165306,-0.01084215,0.06134988,-0.0011808,-0.01302883,-0.00234441,0.04687115,0.01112089,-0.02821694,0.0181953,-0.011947,0.03614271,-0.07624657,-0.0148146,0.10357714,-0.05805334,-0.02612204,-0.11522485,0.03746331,0.00944613,-0.01913307,-0.09145777,-0.02824337,-0.24903803,-0.01915172,0.0381857,0.03776379,-0.06035733,-0.06340688,-0.00481262,-0.04289495,0.00581033,-0.05847637,0.01181917,0.03293589,0.02006231,-0.00147831,-0.01113043,0.02912956,-0.02509848,0.00779785,-0.0256701,0.01339811,0.01010979,-0.06476087,0.01709956,-0.01407473,0.08939818,0.04985378,0.04656852,0.0368912,0.05820762,-0.04340446,0.0439215,0.05283109,0.0509083,-0.17789654,0.0556407,-0.00069074,-0.0228764,0.01082273,-0.05445466,0.01294809,0.0321124,0.02682141,0.05563561,-0.01698562,0.05367357,0.01154988,-0.03410674,-0.0192045,-0.04247595,-0.00789633,0.03282868,-0.01671139,-0.00497758,-0.02869667,-0.03047309,0.03880517,0.00945502,0.10952168,-0.00150629,0.05303695,-0.08332176,0.005326,0.00874017,0.04489651,0.03319352,0.01206906,0.02281415,0.04877409,0.01525713,0.00171401,0.11396274,0.01140476,0.01714715,-0.05082076,0.03792578,0.05328376,-0.04927069,-0.01044221,-0.01202796,-0.0500549,-0.00040833,0.07235317,0.02088728,0.08442165,-0.07443708,-0.04737939,0.01920763,0.00563235,0.00766378,0.07039029,0.01039386,-0.01851946,0.06303371,0.01158277,0.00574313,0.0195195,0.0321986,0.03844241,0.04044036,0.01145186,0.06874645,0.0460575,-0.011991,-0.08736144,-0.03718709,-0.01472515,0.01782554,-0.02634195,0.01790637,-0.00106087,0.0229288,0.0045261,-0.05274256,0.07724786,-0.10016639,0.00064963,0.10476298,-0.02441378,0.02422625,0.04307107,0.02661801,-0.02539777,0.00005155,-0.03101968,-0.05740444,0.05691808,0.00018048,0.09172463,0.08950462,-0.05039687,0.04767278,0.01126122,-0.01369242,-0.00649514,0.09905864,-0.00234313,-0.11305032,-0.0038412,0.04092139,-0.04140941,-0.05075315,-0.03400717,0.05008001,-0.0168585,0.00132982,0.06740501,-0.02403198,-0.04061567,-0.03802221,-0.0110499,0.01122879,-0.0269376,-0.06134783,-0.01506795,-0.01546496,-0.07315272,-0.07213067,0.03077159,-0.02029945,0.00216243,0.00050642,-0.01527157,-0.0482747,-0.04067652,-0.00309122,-0.01005973,-0.02021388,-0.03284189,-0.0352165,0.05935244,0.01100626,-0.05491107,-0.01720734,-0.02075122,-0.02285539,0.03217051,0.03603591,0.01909195,-0.06445435,0.08221525,0.03104282,-0.02638366,-0.00525925,0.05363747,0.04671924,-0.06612606,0.01464626,-0.03378392,0.01449361,0.00973134,-0.01043372,0.04748627,0.06322046,-0.10017039,-0.22313228,-0.00843914,-0.06102678,-0.03424232,0.00870169,-0.01390893,0.01501838,-0.00243342,0.08500882,0.09099977,0.04682977,-0.03582641,-0.03538874,0.02490566,-0.00474848,-0.00202048,-0.05801297,-0.07860031,-0.0246343,0.00362873,0.02429437,0.0292659,-0.05366217,-0.06254514,0.05254167,-0.03184833,0.13553146,0.06464341,-0.02033,0.00910213,0.06414831,-0.04582353,-0.01436352,-0.03217017,0.01214716,0.05797443,-0.02531634,-0.00938335,-0.02323817,0.0026945,-0.06313599,0.05453073,-0.01700575,-0.10389342,-0.003169,-0.02413495,-0.03807297,-0.02089404,0.05270181,0.07521804,0.03950222,-0.03236461,0.04127942,0.05065268,0.04100326,-0.03485224,-0.06759322,-0.01973279,-0.02256563,0.04449415,-0.0231219,-0.05002193,0.05841017,-0.06356078,0.06082428,0.02496525,-0.0368521,0.01782019,0.02225635,-0.00208472,-0.01966249,0.06518192,0.00695139,0.02466364,0.01860566,-0.01868777,0.04968687,-0.02147865,-0.00348729,-0.03156011,0.04354507,-0.02752132,0.0594091,0.02299955,0.07451814,-0.00751742,0.07176286,0.05950652,-0.00790664,0.01556214,0.01203659,-0.00410846,-0.00386876,-0.03808438,0.06113239,0.01235936,-0.26536259,0.04071898,-0.05194512,-0.00808279,-0.04135935,-0.01460371,0.01128657,-0.03909034,-0.04203256,-0.03211381,0.0241235,0.04018512,0.03608053,-0.04275252,-0.01760155,0.01859951,0.03681643,-0.053402,0.03238939,-0.02732031,0.0790277,0.04301751,0.27321136,0.03237226,-0.01089724,0.02920766,0.0237303,0.02378627,0.00175598,0.04662979,0.00214241,-0.00655772,0.02175776,-0.01133949,-0.02556191,0.07520505,-0.00109178,0.01685053,-0.02435229,-0.00351449,-0.0950914,-0.00389774,-0.02790534,-0.00393454,0.13559486,-0.00939335,-0.04451713,-0.04059704,0.08558238,0.02319202,-0.05480177,-0.04556172,-0.02371453,-0.02996556,-0.03101959,0.0016117,0.01359359,-0.01616387,-0.02317354,-0.00591272,0.02801758,0.03563236,0.03280301,0.00591238,0.01195174],"last_embed":{"hash":"1bqzpll","tokens":479}}},"last_read":{"hash":"1bqzpll","at":1753423457925},"class_name":"SmartSource","last_import":{"mtime":1753363611992,"size":16213,"at":1753423416052,"hash":"1bqzpll"},"blocks":{"#---frontmatter---":[1,6],"#Favorite Lottery Numbers in Fixed Positions: Software":[8,185],"#Favorite Lottery Numbers in Fixed Positions: Software#{1}":[10,16],"#Favorite Lottery Numbers in Fixed Positions: Software#{2}":[17,17],"#Favorite Lottery Numbers in Fixed Positions: Software#{3}":[18,18],"#Favorite Lottery Numbers in Fixed Positions: Software#{4}":[19,19],"#Favorite Lottery Numbers in Fixed Positions: Software#{5}":[20,20],"#Favorite Lottery Numbers in Fixed Positions: Software#{6}":[21,21],"#Favorite Lottery Numbers in Fixed Positions: Software#{7}":[22,23],"#Favorite Lottery Numbers in Fixed Positions: Software#{8}":[24,27],"#Favorite Lottery Numbers in Fixed Positions: Software#{9}":[28,29],"#Favorite Lottery Numbers in Fixed Positions: Software#{10}":[30,35],"#Favorite Lottery Numbers in Fixed Positions: Software#{11}":[36,37],"#Favorite Lottery Numbers in Fixed Positions: Software#{12}":[38,47],"#Favorite Lottery Numbers in Fixed Positions: Software#{13}":[48,48],"#Favorite Lottery Numbers in Fixed Positions: Software#{14}":[49,49],"#Favorite Lottery Numbers in Fixed Positions: Software#{15}":[50,51],"#Favorite Lottery Numbers in Fixed Positions: Software#{16}":[52,60],"#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.":[61,75],"#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{1}":[63,66],"#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{2}":[67,67],"#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{3}":[68,68],"#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{4}":[69,69],"#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{5}":[70,71],"#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{6}":[72,75],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>":[76,117],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{1}":[78,78],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{2}":[79,79],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{3}":[80,81],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{4}":[82,83],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{5}":[84,84],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{6}":[85,90],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{7}":[91,96],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{8}":[97,97],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{9}":[98,98],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{10}":[99,100],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{11}":[101,102],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{12}":[103,103],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{13}":[104,104],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{14}":[105,105],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{15}":[106,107],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{16}":[108,117],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>":[118,131],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>#{1}":[120,121],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>#{2}":[122,123],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>#{3}":[124,125],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>#{4}":[126,127],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>#{5}":[128,129],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>#{6}":[130,131],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>":[132,145],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>#{1}":[134,135],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>#{2}":[136,137],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>#{3}":[138,139],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>#{4}":[140,141],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>#{5}":[142,143],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>#{6}":[144,145],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>":[146,156],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>#{1}":[148,148],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>#{2}":[149,149],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>#{3}":[150,150],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>#{4}":[151,152],"#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>#{5}":[153,156],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)":[157,185],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{1}":[159,160],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{2}":[161,161],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{3}":[162,162],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{4}":[163,163],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{5}":[164,164],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{6}":[165,165],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{7}":[166,166],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{8}":[167,167],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{9}":[168,168],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{10}":[169,169],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{11}":[170,170],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{12}":[171,171],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{13}":[172,172],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{14}":[173,173],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{15}":[174,174],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{16}":[175,175],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{17}":[176,176],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{18}":[177,177],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{19}":[178,179],"#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{20}":[180,185]},"outlinks":[{"title":"_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_","target":"https://saliu.com/cross-lines.html","line":28},{"title":"_**Neural Networking, Neural Networks, AI, Axiomatic Intelligence AxI in Lottery, Lotto**_","target":"https://saliu.com/neural-networking-lottery.html","line":36},{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreeNumbersPosition.OUT","line":63},{"title":"Skip lottery software creates positional systems for Powerball, Mega Millions favorite numbers.","target":"https://saliu.com/images/skips-generator.gif","line":82},{"title":"Generate lottery combinations with favorite lotto numbers or bankers strictly in-position.","target":"https://saliu.com/images/skips-input.gif","line":91},{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreePosEU.OUT","line":116},{"title":"The positional frequency systems are powerful lottery tools to crack the jackpot.","target":"https://saliu.com/images/pair-grid.gif","line":122},{"title":"Place the hottest lotto numbers in their positions as favorite bankers to greatly improve chances.","target":"https://saliu.com/images/pair-grid-combinations.gif","line":126},{"title":"Simple text files can generate fast combinations with favorite lotto numbers in fixed positions.","target":"https://saliu.com/images/pair-grid-input.gif","line":130},{"title":"Ranges in lottery refer to lotto numbers in ascending order or ranked by frequency.","target":"https://saliu.com/images/ranges-non-contiguous.gif","line":136},{"title":"Apply positional ranges with fixed favorite numbers to win lottery big-time.","target":"https://saliu.com/images/ranges-input.gif","line":140},{"title":"One input file with favorites or banker numbers works across multiple lottery programs.","target":"https://saliu.com/images/ranges-combinations.gif","line":144},{"title":"The super lottery utility application has dozens of very useful functions to increase chances.","target":"https://saliu.com/images/ranges-generator.gif","line":153},{"title":"Best software tools and utilities in lotto are the creation of lottery grand-master Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":155},{"title":"Resources in Lottery Software, Strategies, Systems, Lotto Wheels","target":"https://saliu.com/content/lottery.html","line":157},{"title":"_**Combinations Generator: Lottery, Powerball, Mega Millions, Euromillions, Two-In-One Lotto Games**_","target":"https://saliu.com/combinations.html","line":161},{"title":"_**Calculate combination lexicographic order of lotto data files**_","target":"https://saliu.com/combination.html","line":162},{"title":"**<u>Skip Systems</u>** _**for Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":163},{"title":"_**Lottery Filtering, Lotto Filters in Software**_","target":"https://saliu.com/filters.html","line":164},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":165},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":166},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":167},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":168},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":169},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":170},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":171},{"title":"_**The Best Lottery Strategies: Foundation, Application of the <u>Lotto Strategy</u> Concept**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":172},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":173},{"title":"_**<u>Bright Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":174},{"title":"_**<u>Ultimate Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/ultimate-software-code.html","line":175},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":176},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":177},{"title":"**<u>lottery software, lotto programs</u>**","target":"https://saliu.com/infodown.html","line":178},{"title":"Lotto numbers with the best positional frequency reduce the odds and volume of tickets to play.","target":"https://saliu.com/HLINE.gif","line":180},{"title":"Forums","target":"https://forums.saliu.com/","line":182},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":182},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":182},{"title":"Contents","target":"https://saliu.com/content/index.html","line":182},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":182},{"title":"Home","target":"https://saliu.com/index.htm","line":182},{"title":"Search","target":"https://saliu.com/Search.htm","line":182},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":182},{"title":"Exiting site of the most excellent lottery software, strategies, systems to win lotto jackpots.","target":"https://saliu.com/HLINE.gif","line":184}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["favorite lottery numbers","fixed positions","lotto","software","generate","combinations","reduce","odds","tickets"],"source":"https://saliu.com/favorite-lottery-numbers-positions.html","author":null}},"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10424928,-0.05444576,-0.03246408,-0.03426528,-0.02089296,0.02291592,0.00845556,0.0043446,0.04543252,0.00856407,-0.00515383,-0.01692545,0.02001174,0.03206919,-0.01423425,0.02310332,-0.00432972,-0.01136078,-0.06726932,0.00273351,0.06019802,-0.04335285,-0.02787957,-0.11965194,0.04592905,-0.00662336,-0.015345,-0.09712248,-0.00973226,-0.19130974,-0.02112337,0.04408127,0.02867351,-0.05795062,-0.0496441,-0.01375218,-0.05511856,0.02444994,-0.03735316,0.04870769,0.01049919,0.01879351,-0.03507517,-0.01212041,0.04051186,-0.01387204,0.0151843,-0.01682835,0.03111928,0.03551949,-0.03427434,-0.00118186,-0.01528319,0.10983066,0.05787935,0.03368285,0.04086861,0.0519115,-0.03819634,0.03644821,0.06773285,0.03590307,-0.17206869,0.06553775,-0.01445365,-0.01311438,0.02261846,-0.03402147,-0.05151473,0.02852597,0.03478843,0.04176612,-0.02349018,0.0605802,0.0233871,-0.02594257,-0.04156944,-0.05576839,-0.00957046,0.0407581,-0.05317818,-0.02762914,-0.03972026,-0.04061542,0.02317973,-0.00347539,0.11786214,0.04516887,0.05880338,-0.05368876,0.0242657,0.00321005,0.06126625,0.04936181,-0.02458345,0.0243958,0.0444567,0.01470645,-0.01072734,0.1434875,-0.01494311,0.04806872,-0.06017855,0.03045956,0.05324609,-0.00279653,-0.00851632,-0.00098721,-0.04925594,-0.0130547,0.04305909,0.03852476,0.10472973,-0.0881674,-0.03736509,0.00417083,0.00761988,0.00455834,0.08395845,0.02349372,-0.03333287,0.03757236,-0.02204414,-0.00867189,-0.00836087,0.03926759,0.04011523,0.0353275,0.00413946,0.07540911,0.04251157,0.00525077,-0.12868688,-0.02653072,-0.05292954,0.01248894,-0.00450045,-0.02080102,-0.01301357,-0.01233858,0.01834564,-0.07347932,0.05210708,-0.11491786,-0.00972394,0.07676998,-0.01781627,0.02249576,0.09642933,-0.00004596,-0.06180919,-0.00453323,-0.03637912,-0.05228628,0.02403341,0.01772387,0.10662064,0.07118073,-0.0348332,0.04714876,0.04868595,-0.04507073,-0.00930748,0.11799445,-0.00256726,-0.11383675,-0.02744596,0.02690188,-0.0291954,-0.06042312,-0.02548832,0.0455263,-0.02764293,0.03022197,0.08590186,0.00433981,-0.00412936,-0.02555015,0.00907487,0.03841818,-0.0136799,-0.04071025,-0.02163858,-0.00779141,-0.07293459,-0.06393536,0.00691107,-0.03396979,0.0071044,0.00418731,0.01902773,-0.06599748,-0.0288476,0.00149379,0.0061669,-0.04403508,-0.0306201,-0.0165392,0.06965329,-0.00928059,-0.0468706,0.00204295,-0.03874183,0.00004054,0.00464635,0.04650597,0.00268969,-0.06269079,0.05347989,0.02711417,-0.02234154,0.0155047,0.06696849,0.02084101,-0.06561086,0.0185168,-0.0292466,0.01022528,0.05078561,0.01253298,0.03734856,0.04932499,-0.10032658,-0.21019106,-0.00430965,-0.05421278,-0.0200555,-0.01708921,-0.02017014,0.02152853,-0.00730162,0.07925928,0.10778551,0.04195645,-0.07374816,-0.01701213,-0.00951775,0.01233094,-0.03365466,-0.05899939,-0.0811694,0.00131544,0.00026261,0.01992218,-0.0193535,-0.04873541,-0.06773434,0.07085633,-0.02010601,0.11813416,0.07178663,-0.05694101,-0.00596075,0.10641398,-0.0501215,-0.02249822,-0.06105002,0.00615139,0.05271725,-0.07284992,0.00988055,-0.04023017,0.00788465,-0.05919905,0.07065737,-0.01360888,-0.09477453,-0.01313023,-0.04066994,-0.02948396,-0.03802495,0.03162526,0.05442566,-0.00645253,-0.01657664,0.03331563,0.05879791,0.0858824,-0.00557607,-0.06258432,-0.03627308,-0.03680219,0.03626021,-0.03009196,-0.05947008,0.0572868,-0.03127298,0.0364525,0.0229171,-0.021098,-0.02980375,-0.00242503,-0.00282709,0.00199936,0.06575491,0.02308474,0.0070144,0.00083115,-0.02058725,0.02324065,-0.00310061,0.03286075,-0.01014598,0.02960431,-0.06020442,0.05703712,0.02422698,0.07196551,0.02686467,0.06000307,0.06270823,-0.01113344,0.04246718,0.01455375,0.01658085,-0.01713214,-0.00514763,0.05333521,0.0138483,-0.23840825,0.05005465,-0.03137913,0.00880572,0.00536443,0.00824258,0.00525809,-0.02591802,-0.04871558,-0.01385114,0.05645829,0.04930421,0.02288016,-0.01950677,-0.02568022,0.01790855,-0.00541336,-0.04744343,0.02218218,-0.01832124,0.06900027,0.03418481,0.25661075,0.05679781,0.00204618,0.02449626,0.02589227,0.02128769,0.00497831,0.04393518,-0.02632816,-0.0207244,0.03306921,0.00499421,-0.05068138,0.071877,-0.02896958,0.026494,-0.02415374,0.01120503,-0.1208646,0.00376759,-0.04777736,-0.00563288,0.11572866,0.03512771,-0.07041211,-0.04139948,0.05549757,0.00321852,-0.04959956,-0.08935787,-0.04162527,-0.00720637,-0.03719779,0.01233896,0.02121666,-0.01357263,-0.01729487,0.00246113,0.04547937,0.03884172,0.05446384,0.01056341,0.0155516],"last_embed":{"hash":"13zz82v","tokens":89}}},"text":null,"length":0,"last_read":{"hash":"13zz82v","at":1753423456370},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#---frontmatter---","lines":[1,6],"size":231,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09852947,-0.04911725,-0.04673202,-0.04838677,-0.01831413,0.03461974,-0.02407102,-0.01964993,0.07228713,0.00636682,-0.01965731,0.01149103,0.05049744,0.00254801,-0.02593779,0.02889977,-0.00887578,0.03529185,-0.08874863,-0.01481219,0.08329912,-0.05509631,-0.03299209,-0.10897437,0.04597948,0.00965428,-0.02352512,-0.09049033,-0.04824226,-0.24929565,-0.00701231,0.05802602,0.04560446,-0.0539315,-0.06528553,-0.00662507,-0.04516084,0.00698465,-0.05296571,0.00219897,0.0328664,0.01021347,-0.00525583,-0.01511992,0.0218813,-0.05018486,0.00791129,-0.0206029,0.01789784,0.00677277,-0.05307093,-0.00327725,-0.0062509,0.07991837,0.04341134,0.06180734,0.03179893,0.06037738,-0.02685457,0.04259681,0.05552099,0.05200707,-0.17579371,0.05193299,-0.01445559,-0.02481491,0.01289942,-0.04262775,0.00891269,0.04457182,0.02941685,0.04260928,-0.01826076,0.05613843,0.01215359,-0.04466176,-0.0099709,-0.03634468,-0.00108515,0.04243811,-0.01807607,0.00742806,-0.02778986,-0.01951175,0.02383715,0.01580002,0.10777786,0.00703624,0.06057049,-0.07077339,0.00712407,0.01293115,0.03529352,0.01586603,0.01877836,0.00789094,0.03208123,-0.00675732,-0.00817866,0.12272505,0.00505769,0.01235191,-0.05192064,0.02990112,0.04845908,-0.04671274,-0.01359148,-0.01624305,-0.05829635,0.00522122,0.06971876,0.02165353,0.07166158,-0.07238366,-0.04635413,0.01990052,-0.01450153,-0.00083759,0.0641038,0.00119585,-0.0265439,0.07445311,0.01530844,0.01201349,0.00437339,0.02905658,0.04974778,0.0398699,0.00427191,0.05372357,0.05204855,-0.00268197,-0.09366191,-0.03557365,-0.01799947,0.02865107,-0.0229615,-0.01133841,0.00481756,0.030949,0.01691809,-0.0612396,0.08364692,-0.10845968,-0.00829615,0.09698407,-0.02736111,0.01898366,0.03682351,0.0300371,-0.0092638,-0.00361831,-0.03731389,-0.04085894,0.05474594,-0.00042892,0.09917276,0.09639922,-0.05323869,0.04048648,0.01199889,-0.01253426,-0.01397914,0.09496288,-0.00735875,-0.11175197,0.00248274,0.03776563,-0.05377571,-0.04087649,-0.02024852,0.04467733,-0.04280942,-0.00608238,0.0786526,-0.03383057,-0.04228332,-0.04167726,-0.01555666,0.00417266,-0.0228879,-0.06099051,-0.01012676,-0.00583897,-0.06068752,-0.07872906,0.03296605,-0.02721388,0.00922001,-0.00485993,-0.01358459,-0.02672107,-0.02791569,0.00413896,-0.0137408,-0.02997488,-0.04625638,-0.0363603,0.05821714,0.0046739,-0.05405413,-0.01106789,-0.01645956,-0.0114156,0.04273446,0.03955591,0.00877659,-0.06205987,0.09006328,0.03598684,-0.02893427,-0.00991476,0.06493541,0.04457665,-0.07313678,0.01793455,-0.02895089,0.02277831,0.01228781,-0.00381948,0.03466305,0.0847178,-0.09798422,-0.21800575,-0.01793925,-0.0715437,-0.01578731,0.01091401,0.01273478,0.02223503,0.00637843,0.07878557,0.09532095,0.05811296,-0.0403716,-0.02365917,0.01791698,-0.01051264,-0.00821959,-0.05200222,-0.08056109,-0.02619815,0.00142701,0.01061236,0.02579404,-0.04919814,-0.05011269,0.05969204,-0.02999248,0.13921247,0.05942911,-0.0342027,0.01378481,0.06275431,-0.04552416,0.00696891,-0.03688641,0.01086434,0.06633633,-0.01786743,-0.02295349,-0.0199031,0.00752121,-0.07080197,0.05662161,-0.02074253,-0.11080334,0.00342287,-0.03612332,-0.03005981,-0.02851339,0.04514002,0.07633089,0.04453477,-0.03447857,0.0294372,0.05589829,0.05532267,-0.03144411,-0.07836454,0.00375026,0.00288389,0.04612483,-0.01455699,-0.06684574,0.05343095,-0.05820208,0.06505143,0.01904391,-0.0256295,0.0159806,0.02059557,-0.00307897,-0.02029831,0.04624477,-0.01376585,0.03750707,0.02029662,-0.00078432,0.05295027,-0.030079,0.00749141,-0.02968581,0.04390708,-0.03481336,0.0569856,0.02979669,0.07908908,-0.01957968,0.06636067,0.06620018,0.0022459,0.01231241,-0.00303956,-0.014461,-0.00216136,-0.04713238,0.02980781,0.02584063,-0.25421318,0.03409624,-0.06030429,-0.00542499,-0.04320464,-0.00504243,0.02101457,-0.04825066,-0.04636132,-0.02653491,0.02995329,0.04940625,0.04829194,-0.03977039,-0.01148652,0.00585339,0.04579671,-0.04546443,0.0253975,-0.04236569,0.08529459,0.05250824,0.27159938,0.02170024,-0.00929095,0.03489974,0.02110577,0.03243329,0.00550495,0.03380217,-0.0055148,0.01125953,0.03427399,-0.00853099,-0.01540474,0.0593757,0.00023766,0.01219857,-0.01752451,0.00554013,-0.07787745,0.00416584,-0.03372601,-0.00068432,0.12775593,-0.01650327,-0.02943593,-0.05260556,0.07998331,0.02526602,-0.06816556,-0.03854052,-0.03480735,-0.03677054,-0.0256305,0.00375541,0.00885816,-0.01757395,-0.02095005,-0.01281431,0.0240039,0.02355688,0.03577947,0.00885924,-0.00263224],"last_embed":{"hash":"7uuz73","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"7uuz73","at":1753423456400},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software","lines":[8,185],"size":15976,"outlinks":[{"title":"_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_","target":"https://saliu.com/cross-lines.html","line":21},{"title":"_**Neural Networking, Neural Networks, AI, Axiomatic Intelligence AxI in Lottery, Lotto**_","target":"https://saliu.com/neural-networking-lottery.html","line":29},{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreeNumbersPosition.OUT","line":56},{"title":"Skip lottery software creates positional systems for Powerball, Mega Millions favorite numbers.","target":"https://saliu.com/images/skips-generator.gif","line":75},{"title":"Generate lottery combinations with favorite lotto numbers or bankers strictly in-position.","target":"https://saliu.com/images/skips-input.gif","line":84},{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreePosEU.OUT","line":109},{"title":"The positional frequency systems are powerful lottery tools to crack the jackpot.","target":"https://saliu.com/images/pair-grid.gif","line":115},{"title":"Place the hottest lotto numbers in their positions as favorite bankers to greatly improve chances.","target":"https://saliu.com/images/pair-grid-combinations.gif","line":119},{"title":"Simple text files can generate fast combinations with favorite lotto numbers in fixed positions.","target":"https://saliu.com/images/pair-grid-input.gif","line":123},{"title":"Ranges in lottery refer to lotto numbers in ascending order or ranked by frequency.","target":"https://saliu.com/images/ranges-non-contiguous.gif","line":129},{"title":"Apply positional ranges with fixed favorite numbers to win lottery big-time.","target":"https://saliu.com/images/ranges-input.gif","line":133},{"title":"One input file with favorites or banker numbers works across multiple lottery programs.","target":"https://saliu.com/images/ranges-combinations.gif","line":137},{"title":"The super lottery utility application has dozens of very useful functions to increase chances.","target":"https://saliu.com/images/ranges-generator.gif","line":146},{"title":"Best software tools and utilities in lotto are the creation of lottery grand-master Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":148},{"title":"Resources in Lottery Software, Strategies, Systems, Lotto Wheels","target":"https://saliu.com/content/lottery.html","line":150},{"title":"_**Combinations Generator: Lottery, Powerball, Mega Millions, Euromillions, Two-In-One Lotto Games**_","target":"https://saliu.com/combinations.html","line":154},{"title":"_**Calculate combination lexicographic order of lotto data files**_","target":"https://saliu.com/combination.html","line":155},{"title":"**<u>Skip Systems</u>** _**for Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":156},{"title":"_**Lottery Filtering, Lotto Filters in Software**_","target":"https://saliu.com/filters.html","line":157},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":158},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":159},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":160},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":161},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":162},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":163},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":164},{"title":"_**The Best Lottery Strategies: Foundation, Application of the <u>Lotto Strategy</u> Concept**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":165},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":166},{"title":"_**<u>Bright Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":167},{"title":"_**<u>Ultimate Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/ultimate-software-code.html","line":168},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":169},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":170},{"title":"**<u>lottery software, lotto programs</u>**","target":"https://saliu.com/infodown.html","line":171},{"title":"Lotto numbers with the best positional frequency reduce the odds and volume of tickets to play.","target":"https://saliu.com/HLINE.gif","line":173},{"title":"Forums","target":"https://forums.saliu.com/","line":175},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":175},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":175},{"title":"Contents","target":"https://saliu.com/content/index.html","line":175},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":175},{"title":"Home","target":"https://saliu.com/index.htm","line":175},{"title":"Search","target":"https://saliu.com/Search.htm","line":175},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":175},{"title":"Exiting site of the most excellent lottery software, strategies, systems to win lotto jackpots.","target":"https://saliu.com/HLINE.gif","line":177}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07338822,-0.05464725,-0.02364425,-0.03808168,-0.03965037,0.06335027,-0.01662409,-0.01899655,0.05479788,0.01260471,0.00793025,0.01048579,0.0302766,0.01830481,-0.02626099,0.02323147,-0.00576362,0.00683708,-0.0883634,-0.00639471,0.07074555,-0.03734279,-0.02856556,-0.10079192,0.06203448,-0.00861473,-0.03005865,-0.09497014,-0.04494484,-0.21258897,-0.01007856,0.0778229,0.01888354,-0.04320244,-0.06263174,-0.01961103,-0.059246,0.04545742,-0.06039419,0.04259862,0.01083895,0.01983172,-0.02741625,-0.01995933,0.03329711,-0.02326006,0.03770502,-0.02895322,0.05073739,0.01410035,-0.0410834,0.00574854,-0.00896754,0.08471496,0.03625604,0.02781408,0.03260382,0.06750648,-0.0094532,0.05282853,0.07316527,0.0304666,-0.18830144,0.061548,-0.0217925,-0.00635141,0.02388348,-0.03117893,-0.03762057,0.04794382,0.01570396,0.03932849,-0.0251393,0.0564628,0.03503271,-0.04360979,-0.02277543,-0.04153245,-0.012527,0.02945211,-0.04270833,-0.014938,-0.02259917,-0.02152488,0.0300411,0.00346453,0.10920224,0.01482245,0.06639895,-0.05452541,0.02190048,0.02383154,0.02399698,0.04126085,0.00068553,0.02777506,0.02448169,0.00097156,0.02743893,0.153974,0.0005383,0.02919593,-0.06586054,0.04003629,0.02344539,-0.03636305,-0.00990941,-0.0226915,-0.05858118,-0.00980737,0.05681496,0.03960318,0.11471225,-0.07698994,-0.07980584,0.00998649,0.00671084,0.02523173,0.07293561,0.00831905,-0.02686349,0.04572045,-0.00154725,0.01235017,0.00420341,0.04795229,0.05863635,0.05543708,0.00962712,0.05045528,0.03490456,-0.00542733,-0.13554716,-0.036352,-0.03512194,0.01273784,-0.00802737,-0.01589733,-0.0059798,0.0052968,0.02188459,-0.09872235,0.06556189,-0.10905313,-0.01553659,0.0817852,-0.02711521,0.02212602,0.06277198,0.00522788,-0.04380735,0.00208546,-0.05574048,-0.04807376,0.02936793,0.02281798,0.07472595,0.06403241,-0.04573076,0.04356167,0.012571,-0.0316176,-0.01720981,0.10587876,-0.02097076,-0.09687317,-0.0289762,0.01980592,-0.04115259,-0.03515351,-0.00653768,0.03267844,-0.04590777,0.01463059,0.08184781,0.00254696,-0.0245737,-0.02130802,-0.00856301,0.02705793,-0.0297625,-0.05475469,-0.02065609,-0.00256401,-0.05551765,-0.05592534,0.03120525,-0.02641985,0.03526999,0.00015288,0.02124966,-0.05782856,-0.00737569,-0.00367122,0.02551199,-0.0426557,-0.05548973,-0.01830063,0.07545651,-0.00731657,-0.07792085,-0.00528167,-0.03386654,0.01282143,0.04179647,0.04359962,0.01734251,-0.05458248,0.07647062,0.02601032,-0.04249324,-0.01006266,0.08739885,0.00951324,-0.05988848,0.0237338,-0.02554609,-0.0084434,0.02734494,0.00877758,0.04708411,0.06471229,-0.09225371,-0.20233563,-0.04716251,-0.04771288,-0.01165655,0.01812944,0.00214209,0.00980966,0.00976697,0.10170497,0.10546283,0.0400609,-0.05098351,-0.03190245,0.0139042,-0.00425704,-0.02347165,-0.06232857,-0.09372848,-0.01933231,0.00318063,0.01750525,-0.00680719,-0.06566851,-0.03562879,0.08334762,-0.02022745,0.11149929,0.02736834,-0.04418511,0.0160652,0.06747412,-0.04802532,-0.00712998,0.00082265,-0.00008081,0.04172602,-0.05515286,-0.02299638,-0.04362246,0.0091226,-0.05631172,0.06853377,-0.01587033,-0.10159332,-0.01714641,-0.06035497,-0.03494833,-0.01091176,0.05034842,0.06022222,0.02308498,-0.02756823,0.036422,0.0471742,0.07900935,-0.01608304,-0.05115533,0.00486073,-0.00879323,0.0524312,-0.02026681,-0.06610627,0.0554351,-0.05140788,0.04116827,0.02777671,-0.01327233,-0.01540439,0.00155834,0.00967015,-0.01826827,0.04615508,0.01632145,0.03434594,0.02253535,-0.01019202,0.02251565,-0.02748252,0.01351734,-0.02493376,0.05132551,-0.04623344,0.0566062,0.01980124,0.06701232,0.00240653,0.06180187,0.05895916,-0.01477102,0.00578848,-0.00307643,0.01071738,-0.01582961,-0.01992567,0.03204133,0.01862374,-0.23452213,0.04485843,-0.0647382,0.03586872,-0.03061447,-0.00146894,0.00319658,-0.01333177,-0.04699714,-0.01845726,0.05065066,0.04570323,0.01882628,-0.01930189,-0.00928889,0.00180751,0.02388761,-0.08090122,0.01205709,-0.06434174,0.09414592,0.04187357,0.25684467,0.05019542,-0.00063443,0.01255457,0.03511406,0.03749819,-0.00036329,0.02748006,-0.01567879,-0.02725461,0.03532435,0.01261503,-0.03289985,0.05475609,-0.01396507,0.03408312,-0.02848426,0.00917175,-0.0924017,-0.00272502,-0.04663018,-0.01014833,0.13102494,-0.00861927,-0.06006133,-0.06321687,0.08304469,0.02144209,-0.07098566,-0.05587488,-0.0497262,-0.03668702,-0.05927223,0.01274951,0.02875444,0.00190687,-0.01744916,-0.00117637,0.04967544,0.02324576,0.03874839,0.00828782,0.00198767],"last_embed":{"hash":"10m9xfs","tokens":90}}},"text":null,"length":0,"last_read":{"hash":"10m9xfs","at":1753423456537},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{1}","lines":[10,16],"size":302,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10081669,-0.06713494,-0.04098068,-0.04230014,-0.00211155,0.02339002,0.0541525,-0.02694787,0.05182659,-0.01970355,-0.00902805,-0.00039492,0.02623387,-0.00051224,-0.02288205,0.03205447,-0.03511417,0.03735014,-0.04323382,-0.02449037,0.09115592,-0.02647687,-0.02280675,-0.11212482,0.02880869,0.01781041,-0.04420431,-0.11369702,-0.04683357,-0.22898859,-0.022664,0.00041271,0.06778501,-0.07624481,-0.02552932,-0.01615202,-0.06099069,0.02370964,-0.04478884,0.00706664,0.00741098,0.00212334,-0.01577491,-0.01993405,0.01796642,-0.05967155,0.01762594,-0.03696579,-0.0010979,-0.00605382,-0.05620535,0.03670905,-0.0096966,0.08348859,0.04820271,0.04375202,0.05503375,0.03655024,-0.03720486,0.00445296,0.05654543,0.03790102,-0.13716692,0.04415402,0.01084093,-0.01567413,0.01151114,-0.06692751,-0.00408079,0.04660047,0.04706949,0.03628997,-0.021571,0.06006569,0.03267889,-0.02833057,-0.00505831,-0.01067556,-0.0082414,0.03325154,0.00520351,0.00776875,-0.01106355,-0.02992184,0.00345097,0.01470837,0.09092665,-0.01588059,0.07646199,-0.06159009,-0.00202036,0.03170373,0.0434395,0.02072692,0.00267016,-0.00446436,0.03994607,0.03941727,-0.01744499,0.11958215,0.02406819,-0.01372334,-0.07415815,0.00990845,0.05279087,-0.06154468,0.00306503,0.01271676,-0.0281004,0.02928661,0.06680729,0.00422073,0.1040118,-0.05662243,-0.03420884,0.05226478,-0.00884011,0.01300249,0.07957556,0.01066065,-0.01616791,0.0816073,0.00953624,0.00487838,-0.04211765,0.0172921,0.03372356,0.03989289,-0.0248869,0.06010655,0.08072627,-0.01441594,-0.06144555,-0.03806308,0.00638321,0.01947505,-0.00985552,0.03105584,0.00131327,0.03834718,-0.00762737,-0.03803002,0.05596847,-0.08913462,-0.03557048,0.06802434,-0.00811451,0.01680146,0.06518781,0.04781232,-0.01528708,0.0036594,-0.06211621,-0.04325195,0.06124545,-0.02244727,0.07231485,0.08362005,-0.06490184,0.03836614,0.01866717,-0.02867455,-0.01302366,0.0873289,-0.02045783,-0.1161555,0.0012945,0.01355097,-0.02963939,-0.04248031,-0.00382962,0.034031,-0.04382647,0.00030296,0.04561701,-0.04344765,-0.03689067,-0.06533752,-0.02994102,-0.02409483,-0.00788718,-0.07184231,-0.02229841,0.01078659,-0.06298329,-0.07598404,0.03043026,-0.04797985,0.01693016,-0.03303581,-0.00372947,-0.01747298,-0.07842965,0.01418339,-0.00524776,-0.00729185,-0.01346907,-0.03745027,0.04200561,0.00235812,-0.08480974,-0.00594775,-0.00263856,0.02675427,0.02723002,0.02629282,0.02167007,-0.06560303,0.09757217,0.07221986,-0.0333173,-0.01367365,0.02581826,0.03827105,-0.03688421,0.02701856,-0.02918158,-0.00328867,0.03740208,-0.03964219,0.04338656,0.07726549,-0.11208647,-0.22442621,-0.00435933,-0.08314893,-0.01465068,0.03100351,0.02350614,0.00084673,-0.00839805,0.08517894,0.08983243,0.03903824,-0.06759255,-0.02434064,0.03266773,-0.00294354,0.01998681,-0.06002771,-0.07068934,-0.01819325,0.01144476,-0.00327289,0.02171343,-0.07664049,-0.05240223,0.02420919,-0.01405483,0.15027943,0.09776721,-0.01599722,0.0271758,0.05801968,-0.03960334,-0.00536076,-0.08068803,0.00139424,0.04912448,-0.02013198,-0.0100174,-0.03747518,0.01724494,-0.03329509,0.03610984,-0.01274111,-0.07875019,-0.01725128,-0.03230582,-0.04621516,-0.00543798,0.05235684,0.09891707,0.04161834,-0.00957724,0.04835343,0.07898816,0.02202092,-0.00909618,-0.05227527,-0.01255762,0.00387809,0.10024203,-0.02090883,-0.03091621,0.03648284,-0.07792697,0.06045854,0.01926652,-0.02090815,-0.03253863,0.03507963,-0.03139796,0.00376129,0.06800963,-0.00503011,-0.04241633,0.02082823,-0.00632283,0.08198571,-0.00271614,-0.02130315,-0.04470308,0.08328103,-0.01179792,0.04969061,0.00453418,0.05413477,0.02452241,0.05993102,0.04166571,-0.01815453,0.02864687,0.00938363,-0.03560119,0.00144683,-0.07098068,0.0222637,0.00932446,-0.23564032,0.06940316,-0.03640489,-0.02271056,-0.0230806,0.01339382,0.02418144,-0.05912672,-0.05274411,-0.02556313,0.02782062,0.04409746,0.05683751,-0.02950207,0.00149126,0.00357507,0.05291476,-0.06176083,0.03374958,-0.04538701,0.07948446,0.05498471,0.27384248,0.00873013,-0.0093867,0.00325602,0.02402117,0.03615532,0.00661702,0.01179476,-0.00517232,-0.00027025,-0.0044627,0.00989996,-0.0261506,0.04656675,0.01383002,0.04039576,-0.01623997,-0.01635539,-0.09474872,-0.03133938,-0.03192242,0.03366641,0.10133606,-0.01379154,-0.04541198,-0.00962737,0.09528778,0.03278023,-0.04989523,-0.04976758,-0.01509632,-0.03759926,-0.00644447,0.01262491,0.00170764,-0.0450008,-0.02171735,-0.0288437,0.00383178,0.05797262,0.02338861,0.01730293,0.01640814],"last_embed":{"hash":"3z1imt","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"3z1imt","at":1753423456568},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{8}","lines":[24,27],"size":472,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12587243,-0.03067039,-0.0289682,-0.04272528,-0.00602405,0.02211813,0.01546346,-0.00357339,0.06627904,0.00504013,-0.02971949,0.01728719,0.03648789,0.00984845,-0.02615292,0.01756134,0.00427165,-0.04247354,-0.06598496,0.0038115,-0.0093055,-0.06542709,-0.01213919,-0.09262767,0.04750081,-0.01122417,-0.05477412,-0.0885455,-0.02777866,-0.25313759,-0.01521079,0.04997155,0.01732433,-0.06954527,-0.08735604,0.00435411,0.00007728,0.02886868,-0.05558408,0.03984286,-0.01014916,0.02269015,-0.02558791,-0.01962792,0.03485096,-0.02755432,0.01258828,-0.0111459,0.00207896,0.00757849,-0.03807704,0.01339719,-0.04154343,0.07328335,0.04076033,0.04776129,0.01243868,0.06119697,-0.03815222,0.02027052,0.05259356,0.06714749,-0.13893555,0.04837928,-0.00688994,0.00436751,-0.00733937,-0.04067557,-0.02188827,0.12128448,0.03200177,0.03347284,-0.02605267,0.04593524,0.02523109,-0.0392614,-0.01833413,0.00243952,-0.00159968,0.0692799,-0.00491796,-0.03965968,-0.00920366,-0.01298396,-0.02085875,0.02648442,0.05793036,0.03554521,0.06850634,-0.06173849,0.06377476,0.01537495,0.03254011,0.01413443,0.00374906,0.00506887,0.03786762,0.0090903,-0.05086047,0.11013252,0.01921161,-0.03631045,-0.02877401,0.03075771,0.07774074,0.00101179,-0.00919175,-0.00886112,-0.03693102,-0.00428535,0.03635915,0.01084047,0.10457622,-0.01396344,-0.05001016,0.05022199,-0.02512436,0.01721787,0.05203914,0.01035287,-0.02524537,0.06655499,-0.0019065,-0.00593695,-0.06411185,-0.0056617,0.04915601,0.01944575,-0.03203775,0.08332458,0.01732011,-0.0156423,-0.06050524,-0.04306321,-0.02132416,0.03635965,-0.02873468,0.00439012,0.00479016,0.05016535,-0.00921416,-0.025764,0.01992706,-0.13089505,-0.00775188,0.09294857,0.03203885,0.01186205,0.06076771,0.02124289,-0.02214822,0.00133314,-0.03549768,-0.0647866,0.0064969,-0.03486564,0.09537986,0.08085597,-0.06951914,0.00559029,0.01872942,-0.0336753,-0.00760754,0.05656956,0.02749196,-0.11120792,-0.03674403,-0.00364178,-0.03134989,-0.05050838,-0.06375849,0.04938238,-0.03335615,0.00115713,0.05550507,-0.01708435,-0.08245173,-0.03447428,0.01201449,0.01584533,0.06948902,-0.04755304,-0.01648111,0.00654917,-0.03610716,-0.10875338,0.00723469,-0.00581801,0.01030846,-0.00735375,-0.02432222,0.00822766,-0.0664266,0.03119078,-0.02102244,-0.05991694,-0.00952681,-0.00771108,0.04113837,-0.03860637,-0.07522487,-0.03084658,-0.01872526,0.00088178,-0.02860623,0.07471734,-0.00137868,-0.07780366,0.12325963,0.07070215,-0.0286169,-0.01741518,0.0590523,0.04873087,-0.05361895,0.0415126,-0.00515814,0.02798638,-0.00067176,0.03029379,0.06982009,0.07683308,-0.07840872,-0.22422601,-0.00896259,-0.08969425,-0.02250983,0.00753117,-0.02792538,0.02233984,-0.01732096,0.06364332,0.05071591,0.05436775,-0.07900887,-0.00931217,0.03155302,0.00336316,-0.04357042,-0.06001272,-0.05330497,-0.01422491,0.00504244,-0.0039484,0.02311499,-0.0739219,-0.07619262,0.04340201,-0.03883269,0.16868289,0.04304837,0.00722488,0.01062917,0.05952499,-0.01959854,-0.02499626,-0.11696766,0.03299699,0.08363248,0.0265188,0.02132536,-0.0365059,0.00233277,-0.06827077,0.03225879,-0.04247158,-0.0851913,-0.05348922,0.00419308,-0.01761856,-0.02048203,0.02865373,0.08346379,0.02561717,-0.0204479,0.04160991,0.04596566,0.06458827,-0.0291329,-0.0671396,0.01347339,0.0071308,0.08939062,0.02322849,-0.03450561,0.03103607,-0.01858798,0.07817239,0.00470245,-0.01263631,0.01041264,0.0103146,-0.00853205,0.00150493,0.05774815,-0.01809138,-0.01964267,0.04148942,0.02318427,0.06923262,-0.0202172,0.00458178,-0.02479735,0.00699888,-0.09866589,0.05635823,0.0189858,0.05922287,0.00221093,0.0617322,0.05000011,-0.03885027,0.04403939,-0.01113493,0.00225384,-0.05150403,-0.02609839,0.07154374,0.03398705,-0.22394772,0.0425272,0.01006218,-0.00690184,-0.01309831,0.031378,0.01260582,-0.00071428,-0.03091915,-0.01541912,0.02278174,0.01036664,0.0488593,-0.02421585,-0.01181107,-0.02814882,0.00732816,-0.03181252,0.0499973,0.00816855,0.0846318,0.06533831,0.24149571,0.02075221,0.01576002,-0.00791483,0.02455891,0.07429927,0.00692168,0.0105066,0.00259392,-0.00380189,-0.00904814,-0.03465447,-0.01638199,0.01501971,-0.02067089,0.04048263,0.00090536,0.01942611,-0.07914867,0.00340163,-0.04563996,0.00665261,0.13275217,0.01034549,-0.02630475,-0.04773196,0.03751926,0.0249437,-0.02790634,-0.08068693,-0.04068039,-0.02339359,0.00065479,0.02141063,-0.02257692,-0.02441015,-0.01491702,-0.00505056,0.02420835,0.05005214,0.08837298,0.01898066,-0.01153454],"last_embed":{"hash":"gxjqw","tokens":319}}},"text":null,"length":0,"last_read":{"hash":"gxjqw","at":1753423456613},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{10}","lines":[30,35],"size":1231,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08968837,-0.04096024,-0.03998074,-0.06596608,-0.06668895,0.02887067,-0.01088923,-0.02164375,0.06599686,0.00765453,0.00345463,0.01504814,0.05714346,0.00455757,-0.01706167,0.02843424,0.02546262,-0.01633883,-0.08182316,-0.00132953,0.06540149,-0.04594331,-0.01907261,-0.11752295,0.03664284,-0.00216191,-0.0357784,-0.08828539,-0.03662345,-0.27515587,0.01371766,0.06354371,0.04958405,-0.06069753,-0.09051054,-0.00798591,-0.05154973,0.03534007,-0.07871508,0.02372975,-0.01211811,0.02987637,0.00637488,-0.02296071,0.0220667,-0.01932675,0.01645528,-0.00611432,0.00500998,0.01892259,-0.03162018,-0.00291845,-0.006548,0.09765679,0.03208364,0.02451834,0.00515973,0.0725968,-0.04138419,0.027024,0.06687448,0.0650349,-0.14370956,0.05068767,-0.00317369,0.00735984,0.02025352,-0.02804006,-0.02951227,0.05481637,0.00355626,0.03944665,-0.02038359,0.06237999,0.04067471,-0.03248386,-0.0240076,-0.02614651,-0.02213488,0.0601298,-0.04174695,-0.03422751,-0.02372593,-0.017911,0.01698638,0.02132846,0.0873972,0.01720124,0.05585445,-0.04924658,0.02821124,0.01313631,0.03016794,0.01525758,0.02711816,-0.00128329,0.03981974,-0.0130274,-0.01632055,0.10143599,0.01491338,0.01660534,-0.05822816,-0.00491298,0.0091391,-0.01810841,-0.03682002,-0.00582804,-0.06027947,-0.01480535,0.06195839,0.03188948,0.08618275,-0.07858654,-0.04320747,0.02243034,-0.00558683,-0.00446557,0.07313009,0.03945876,-0.03617787,0.03715881,-0.01777959,0.02541315,-0.00745916,0.01624659,0.05220631,0.03579311,0.01869863,0.0673874,0.05149798,0.01660357,-0.11058941,-0.0331987,-0.04829427,0.0246991,-0.02425809,0.00316625,-0.00349828,-0.00509415,-0.00942226,-0.0416045,0.08067692,-0.0926679,0.00423688,0.08106447,-0.01821908,0.04673948,0.05818113,0.01202703,-0.03935349,-0.02046682,-0.05587818,-0.03828004,0.02704761,0.02664962,0.07454669,0.07329793,-0.05868607,0.01938127,-0.00944465,-0.01388652,0.0084039,0.08163542,0.01948964,-0.09518888,-0.05472257,0.02071168,-0.05277617,-0.03731006,-0.03224056,0.04614272,-0.03448178,0.02699468,0.04506662,-0.00562949,-0.07207274,-0.02057473,0.03736152,0.02620232,0.04013276,-0.04728736,-0.00963718,-0.00010584,-0.06135266,-0.07990634,0.01557091,-0.02405319,-0.02217993,0.01278732,0.00630196,-0.00883464,-0.02705667,-0.00693492,-0.01246822,-0.04826198,-0.01363681,0.00731131,0.05404186,-0.02213632,-0.03199823,-0.02812308,-0.02022821,0.02181381,0.00674447,0.04638671,0.01757189,-0.06492074,0.12330771,0.03830463,-0.03197808,-0.02518942,0.06244851,0.03567384,-0.07031422,0.01178265,-0.01925679,0.01546156,0.0554477,0.01765701,0.05266146,0.04583239,-0.08279112,-0.21737799,-0.02437854,-0.05147655,0.02486743,0.02753489,-0.01456108,0.00322058,-0.0222545,0.04987773,0.07353801,0.04998997,-0.07762123,-0.03678239,0.02785219,-0.02755713,-0.0194333,-0.0948648,-0.06440958,-0.03295532,0.0096504,0.01767161,-0.00001274,-0.08258461,-0.0589838,0.08095612,-0.0357955,0.12361933,0.0316637,-0.00271503,0.01379874,0.0861815,-0.05041435,0.01404124,-0.01393928,0.02480599,0.06813318,-0.01535607,0.01614315,-0.01056236,0.02265738,-0.07180329,0.03216788,-0.02165878,-0.1029883,-0.04237526,-0.03779157,-0.04550491,-0.00710628,0.05962998,0.0720392,0.00769968,-0.02249837,0.027626,0.02619771,0.06836057,-0.036701,-0.05115977,0.01015919,-0.02056499,0.05377551,0.00712635,-0.06769794,0.05470226,-0.02344175,0.0347405,0.01943367,-0.00768573,0.02313385,0.03888965,-0.00919694,-0.00763712,0.06245781,-0.00724719,0.00305623,0.02130716,0.0255624,0.05412525,-0.01064416,-0.00611289,-0.02991055,0.02442811,-0.05582606,0.02931162,0.02290066,0.04271463,-0.00506327,0.09034945,0.09276865,0.00516001,0.03100655,-0.00153204,0.03620823,-0.01430976,0.01345455,0.05737016,0.01720375,-0.23593304,0.02329941,-0.07218253,0.03640364,-0.03488524,-0.00517619,-0.01457068,0.00271499,-0.01227758,-0.00371493,0.03522634,0.01941418,0.06688374,-0.05534517,-0.01618409,-0.00956402,0.0111507,-0.04476534,0.04490168,-0.04612276,0.10217068,0.04300147,0.27109534,0.03126401,-0.01174681,0.02191417,0.01444523,0.05426565,0.00348647,0.02688763,0.0012684,-0.01731308,0.03920244,-0.03841802,-0.04482031,0.02168848,0.00230547,0.03788995,-0.03937487,0.01100686,-0.11460588,-0.00479362,-0.07238919,-0.00481671,0.12893531,0.01946568,-0.04192356,-0.06371684,0.05547927,0.02999081,-0.04802031,-0.05370268,-0.02986228,-0.01190671,-0.01644184,0.01004352,0.00475218,0.00850186,-0.04536228,-0.00697961,0.04001692,0.02411381,0.08678627,0.01971141,-0.01996055],"last_embed":{"hash":"tpgl1t","tokens":457}}},"text":null,"length":0,"last_read":{"hash":"tpgl1t","at":1753423456708},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{12}","lines":[38,47],"size":2515,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10301992,-0.05020532,-0.0198881,-0.04758872,0.00057577,0.0033223,0.03691206,-0.01929021,0.04147205,0.01837167,0.01840123,-0.0366317,0.01735692,0.02009628,-0.03246802,0.00825307,0.0188573,-0.03212262,-0.08096298,-0.00238725,0.07655585,-0.04587379,-0.02412392,-0.10670168,0.05051083,0.00234262,-0.02697361,-0.09058821,-0.04837204,-0.1875042,-0.00417519,0.04016071,0.06140287,-0.07962976,-0.05199819,0.00120016,-0.05873637,0.00806525,-0.06106237,0.00691739,0.00266469,0.00826577,-0.01947958,-0.01640929,0.00481282,-0.04246661,-0.030626,-0.01267408,0.07127147,0.02810062,-0.05256008,-0.00207991,0.01698086,0.10210156,0.03695004,0.03101558,0.04868437,0.08778859,-0.04454273,0.03576421,0.04821134,0.05118126,-0.1665674,0.05855526,-0.0050254,-0.01122579,0.03991995,-0.03311569,-0.0409781,0.03802426,-0.01176003,0.04499613,-0.02516565,0.07003871,0.01311187,-0.05726853,-0.0243563,-0.05455606,-0.01110867,0.05122041,-0.06014613,-0.01967377,-0.01288071,-0.00934877,0.00269814,-0.00456861,0.09324906,0.02583897,0.04401222,-0.04432013,0.00383942,0.03546295,0.02550139,0.02116376,-0.03800641,0.00492514,0.03013201,0.01983776,-0.02718878,0.14012508,0.0091504,0.02274891,-0.05808872,0.03303701,0.0588964,-0.00816137,0.00010281,-0.02075867,-0.05300822,-0.00975184,0.03815592,0.01338944,0.07026453,-0.07527666,-0.05394943,0.02655212,-0.00471112,0.00203181,0.09469967,0.01136613,-0.0067221,0.05871834,-0.00904567,0.01001796,-0.01960905,0.01135091,0.0537972,0.02816886,0.01614241,0.09392101,0.05953693,0.03466719,-0.11493041,-0.03080226,-0.05786208,0.05494865,-0.03595521,0.0067868,-0.02706262,-0.01452801,0.00218635,-0.10084584,0.02817509,-0.08856388,-0.01664016,0.08955428,-0.06349935,0.03196564,0.11134588,0.00863913,-0.03739162,0.01791429,-0.05671519,-0.03928833,0.0222208,-0.00103924,0.10268255,0.05924284,-0.06623501,0.04644196,0.0270517,-0.03615151,0.01993207,0.09572054,0.03141994,-0.09877918,-0.019851,0.02599631,-0.04405101,-0.02943598,-0.02319261,0.03668278,-0.03935299,0.01088328,0.0559563,-0.00848862,-0.00021043,-0.05107204,0.02284477,0.04329748,-0.00548059,-0.0505504,-0.00438445,-0.00973494,-0.0539266,-0.05043562,0.00605986,-0.02216914,0.01362269,-0.00129825,0.0152029,-0.05363506,-0.0369356,-0.01176795,-0.00915975,-0.04572862,-0.03154777,0.0026785,0.03660116,-0.01647771,0.00825686,0.01927625,-0.00714753,-0.0023677,0.00320285,0.04675453,0.01311197,-0.05411771,0.08120283,0.01060085,-0.01811795,-0.00470256,0.08224531,0.01988272,-0.04241858,0.00454988,-0.01503138,-0.00409888,0.04767708,0.05539278,0.02631164,0.04484422,-0.08586042,-0.22372754,0.01735143,-0.05015721,-0.0355095,0.01339346,-0.00555527,0.00940379,-0.00072627,0.09804842,0.09785053,0.04592363,-0.05714561,-0.02052221,0.01909968,0.00523741,-0.02902518,-0.07535111,-0.04287451,-0.02057224,-0.01003955,0.01605195,-0.01013374,-0.05336742,-0.02495222,0.0752009,-0.02786102,0.14019902,0.06511068,-0.03710076,-0.00660677,0.09783673,-0.04618024,-0.02941607,-0.07346225,0.02475197,0.05561244,-0.03872023,0.01380545,-0.03370089,0.00160298,-0.08302449,0.05754598,-0.00455389,-0.08645719,-0.0344989,-0.04127399,-0.04268882,-0.02877647,0.04786677,0.04927098,0.00972213,-0.02322814,0.02407371,0.0487974,0.08715476,-0.01291053,-0.07793019,-0.04021852,-0.03143513,0.02882305,-0.01682369,-0.07370214,0.0760735,-0.02764054,0.03923909,-0.00340301,-0.03191929,-0.0101746,0.00804894,-0.00562882,-0.00134493,0.05626544,0.01730855,0.00604941,0.00930671,0.00418789,0.03741292,-0.00746344,0.01678248,-0.04481559,0.01018385,-0.02469609,0.06019108,0.00706812,0.08409041,0.03331049,0.07132462,0.07065692,0.00192834,0.03554706,0.02635503,0.01509304,-0.00000909,0.02148196,0.06671444,0.00256423,-0.27769759,0.03371605,-0.06140952,-0.02015553,-0.00602719,0.01669552,-0.00054185,-0.00987183,-0.02733229,-0.02801201,0.04743305,0.06027044,0.03513797,-0.05382684,-0.03768595,-0.0255552,-0.01127158,-0.04848751,0.05704088,-0.02099004,0.08090782,0.02179945,0.25404662,0.02419887,0.01957446,0.05191274,0.04258721,0.02503618,0.02651557,0.02840039,-0.00693304,-0.02592467,0.0377912,0.00433232,-0.02929097,0.07159953,-0.03050764,0.04549412,-0.01180708,0.00046819,-0.08481468,-0.006939,-0.05381953,-0.01354699,0.11149348,0.02835625,-0.0421662,-0.03166388,0.06339965,0.01703289,-0.02848494,-0.06243818,-0.04524614,-0.02616665,-0.02642631,0.00692818,-0.01933486,-0.00985758,-0.01971322,-0.01482396,0.0278511,0.01542298,0.06296645,0.04265075,0.00883446],"last_embed":{"hash":"ovujmt","tokens":200}}},"text":null,"length":0,"last_read":{"hash":"ovujmt","at":1753423456861},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#{16}","lines":[52,60],"size":486,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10094742,-0.0349122,-0.0276888,-0.06668001,-0.04542969,0.04340882,-0.01458151,-0.02891198,0.05839312,-0.01111533,0.00393674,-0.0045273,0.03193951,0.00918978,-0.03953415,0.01106602,0.01656272,-0.01324656,-0.08144088,0.01092264,0.05571244,-0.05798868,-0.00552035,-0.10759728,0.03855208,-0.0061559,-0.04162755,-0.09827816,-0.03181643,-0.27091423,-0.00594219,0.0658732,0.03472829,-0.06690539,-0.07926604,-0.01305327,-0.06241788,0.04272616,-0.05733446,0.00934905,-0.00056494,0.0160172,0.00750882,-0.00305375,0.02682145,-0.01346304,-0.00215999,-0.01377927,0.05067594,0.00100696,-0.04432503,-0.01125199,-0.00891546,0.06950109,0.05827473,0.04236469,0.03901666,0.08106342,-0.03083942,0.06613961,0.05585969,0.05677873,-0.15238859,0.07738297,0.00627217,-0.03183778,0.00711663,-0.03024219,-0.0616507,0.06031688,-0.01092254,0.05087582,-0.02239105,0.05481926,0.0255087,-0.04425814,-0.044542,-0.04570313,-0.02932639,0.05451013,-0.07389724,-0.02766953,0.00335573,-0.00645059,0.01821842,0.03170617,0.10580052,0.03437533,0.02993881,-0.0527719,0.03465654,0.03624572,0.02416075,0.0202091,0.00294062,0.02395642,0.02079704,-0.0057651,0.01884232,0.10760435,0.0009061,0.01295007,-0.04416506,0.03750475,0.03338057,-0.02068471,-0.01479416,-0.00447443,-0.03233913,-0.01119789,0.05745134,0.00029131,0.0747555,-0.08248439,-0.04514441,0.03894246,0.00736819,0.00455659,0.07859536,0.03119451,-0.01824182,0.03099343,-0.01280381,0.00423815,-0.00225696,0.01149357,0.05663216,0.02961683,0.03751615,0.06064716,0.03053052,0.00349503,-0.12367171,-0.04989421,-0.07108855,0.03139598,-0.01637903,0.00697885,-0.0184493,-0.02373507,-0.00997436,-0.07069639,0.07098757,-0.09934583,0.01129193,0.10184887,-0.0186365,0.02926136,0.07613963,0.00724567,-0.04306102,-0.01244212,-0.06043644,-0.04127785,0.02141879,-0.01279703,0.08757867,0.04687448,-0.07338984,0.03894844,-0.01548929,-0.02972062,0.00718022,0.12396196,-0.00300853,-0.07863571,-0.01520921,0.01781158,-0.06790046,-0.08714762,-0.02806087,0.0130211,-0.01945402,0.02911235,0.05256084,-0.00380022,-0.06171703,-0.03703624,0.02577969,0.04097443,0.00026723,-0.0299236,-0.00750447,0.01194158,-0.04857352,-0.04968393,0.01687771,-0.00976087,0.00816875,0.01423003,-0.01966565,-0.03206909,-0.0204568,-0.00056352,-0.00942365,-0.04288647,-0.018286,-0.037451,0.03754804,-0.01215091,0.00103977,0.01112119,-0.03236106,0.01028069,0.01536623,0.06926697,0.00449366,-0.05355285,0.10266054,0.01314957,-0.03794735,-0.001148,0.04914344,0.01261049,-0.06670453,0.02163717,-0.00038538,0.00886535,0.0287201,0.03088745,0.04412493,0.0154717,-0.04652754,-0.2040929,-0.02255162,-0.04690335,0.00197802,0.02676374,-0.0150321,0.01245584,-0.02283368,0.0784927,0.11483019,0.06046607,-0.0646499,-0.03602933,0.03551749,-0.01617935,-0.01715086,-0.08609745,-0.06301551,-0.03449235,0.04134003,0.02094187,0.00366121,-0.08204383,-0.04154162,0.08917632,-0.02447762,0.12896699,0.03260871,-0.04221064,0.01741966,0.09005504,-0.00671978,-0.01229517,-0.00194145,0.03703078,0.0471298,-0.03497167,-0.01997957,-0.04084676,0.01795536,-0.06404289,0.05282284,-0.00149503,-0.09001855,-0.05177814,-0.02197603,-0.04189378,-0.00129449,0.0450171,0.03858859,0.00766688,-0.01423639,0.03225442,0.02850426,0.0816067,-0.01356899,-0.05579629,-0.01542575,-0.0284981,0.02044862,-0.03121288,-0.0571259,0.06440492,-0.00598331,0.03943238,0.03984248,0.00050911,0.00726777,0.04372301,0.01146167,-0.00458425,0.01686685,-0.01453936,0.02146411,-0.00955343,0.01348326,0.07198149,-0.01449076,0.00464792,-0.02669914,0.04276148,-0.04050437,0.03754422,0.02703916,0.03846314,-0.02434771,0.08452662,0.09044462,-0.00678101,0.03591244,0.00820595,0.04044429,0.01026829,0.01532129,0.03866252,0.00620971,-0.27039617,0.01146062,-0.0833433,0.04639348,-0.01657766,-0.00995683,-0.00105753,0.00500768,-0.01607153,-0.01909275,0.01180196,0.06011933,0.04180125,-0.06667921,-0.02636728,-0.02126722,0.00429195,-0.04947376,0.05423248,-0.0315913,0.10940533,0.00819337,0.25408483,0.01286482,-0.00481936,0.03203553,0.03445295,0.02851901,0.00171772,0.03153626,-0.01203196,-0.04337893,0.05096347,-0.01669763,-0.03811391,0.0478842,-0.00057054,0.0534376,-0.02810517,0.0247332,-0.09267252,-0.00693371,-0.06390866,-0.03564946,0.12006263,0.0268379,-0.03928977,-0.02967375,0.03748158,0.01936583,-0.02717721,-0.05964382,-0.02771761,-0.01593065,-0.01734953,0.02329363,0.00950502,0.00947714,-0.02887757,0.01285191,0.0356414,0.01391261,0.07868326,0.03628099,-0.0121679],"last_embed":{"hash":"1dn519l","tokens":441}}},"text":null,"length":0,"last_read":{"hash":"1dn519l","at":1753423456916},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.","lines":[61,75],"size":1627,"outlinks":[{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreeNumbersPosition.OUT","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09540477,-0.04267605,-0.02066178,-0.07506317,-0.03574425,0.03594229,-0.02344684,-0.02237597,0.0402774,-0.00847411,0.01352072,-0.01050675,0.00908653,0.00276593,-0.05718417,0.01352383,0.03098409,-0.02301297,-0.09268764,0.02394746,0.07476641,-0.03346403,0.00829745,-0.10300726,0.04118306,-0.00434843,-0.04766318,-0.08674046,-0.03973574,-0.26694006,-0.00868336,0.06709898,0.04415155,-0.07524004,-0.05887445,-0.00735998,-0.05905884,0.03751834,-0.04507212,0.00807107,-0.00393186,0.02535295,-0.00402571,-0.00188674,0.03561715,-0.02762435,-0.00507049,-0.00931889,0.07303285,-0.00227128,-0.03778994,-0.00150436,-0.01191213,0.06200409,0.04256015,0.01285835,0.03598827,0.08424547,-0.02637567,0.05938381,0.06001197,0.05113011,-0.16176173,0.09215385,-0.00149078,-0.03685709,-0.00790115,-0.02976997,-0.07698229,0.05398652,-0.03601484,0.04106592,-0.02611567,0.06347819,0.02276376,-0.04744473,-0.04138391,-0.03988196,-0.03183479,0.04338774,-0.06712253,-0.03807474,-0.00017388,0.00325,0.00103821,0.03227843,0.09869576,0.04629976,0.02565522,-0.04875381,0.03018996,0.0239109,0.02764399,0.02323515,-0.01109116,0.00496567,0.00697603,-0.001587,0.02087166,0.1037294,0.00772227,-0.00540061,-0.03509415,0.03408759,0.03029066,0.00378837,-0.01617767,0.00057546,-0.04316423,-0.01349371,0.06841411,0.00602163,0.07076123,-0.09096643,-0.0392774,0.02620854,-0.00166155,-0.01121381,0.0828163,0.03406907,-0.00808231,0.02381682,-0.02720279,0.012245,0.00029704,0.01300357,0.06542958,0.02665332,0.03445348,0.0564324,0.02468739,0.00996626,-0.12357841,-0.04250666,-0.0614231,0.03541334,-0.00691618,0.00085996,-0.02172399,-0.04504166,-0.0175854,-0.07663321,0.06232324,-0.08179925,0.00325285,0.08752956,-0.02476565,0.03181724,0.08161776,0.00998894,-0.04610311,-0.01219799,-0.05037919,-0.03260051,0.02573811,-0.00953308,0.10001192,0.03517824,-0.08195087,0.0228555,0.00676703,-0.02173855,0.01578834,0.12740125,-0.01777897,-0.05686968,-0.02649057,0.02077656,-0.04880119,-0.10490138,-0.02265583,0.00635893,-0.02328244,0.03554508,0.0547388,0.00058073,-0.04974716,-0.02659433,0.01743786,0.0466392,-0.0094384,-0.02648616,-0.00150431,0.01856629,-0.04947755,-0.04394258,0.01411263,-0.01924228,0.0123746,0.01624595,-0.0067297,-0.01754421,-0.02331244,-0.00318828,-0.01417369,-0.04842929,-0.03084812,-0.03557875,0.04173477,-0.0165047,0.01557834,0.03075033,-0.03104927,0.00422557,0.02215758,0.0596307,0.00448982,-0.05115841,0.10081057,0.00673977,-0.04132677,0.00401853,0.05661741,0.00370116,-0.07265157,0.03457662,0.01494048,0.00105644,0.0367156,0.03287924,0.04887207,-0.00138567,-0.06139441,-0.202039,-0.02537266,-0.037809,-0.01235333,0.0361046,-0.00776506,0.01306179,-0.01907537,0.08996227,0.11762336,0.05128837,-0.06661256,-0.03143293,0.02548187,-0.02393005,-0.01940332,-0.09195016,-0.07422619,-0.02403953,0.05651094,0.01215252,-0.00322708,-0.08832595,-0.02807497,0.08397307,-0.02986349,0.1209253,0.03350392,-0.02501373,0.01872465,0.09469856,-0.00357571,-0.0190064,-0.0094514,0.04859756,0.04019393,-0.06366972,-0.00208177,-0.04444266,0.01542591,-0.03121483,0.06638899,0.00169813,-0.09108206,-0.05489777,-0.04728098,-0.03974649,-0.01777017,0.04703787,0.03502095,-0.0016107,-0.00764731,0.02808319,0.04162231,0.09033345,0.00066778,-0.06736769,-0.0020229,-0.02755547,0.01642694,-0.04168819,-0.06044887,0.07189194,-0.00809114,0.03722847,0.05043196,0.00314061,0.00717519,0.05408353,-0.00571708,-0.00600088,0.01977973,-0.02021978,0.01754871,-0.00405957,0.01565791,0.07452014,-0.01078381,0.00005416,-0.01482878,0.02925179,-0.04317967,0.03539471,0.02781112,0.03239588,-0.01476587,0.07613716,0.08228412,0.00242157,0.03554913,0.02601403,0.04329432,0.0122564,0.0195872,0.0329286,0.01303018,-0.2568152,0.01478442,-0.0703592,0.04614615,-0.0094682,0.0064636,-0.00368977,0.00511626,-0.01702281,-0.00120317,0.00191364,0.05710611,0.03279791,-0.07581752,-0.02568347,-0.03451617,0.00180541,-0.05277655,0.06750768,-0.0394735,0.10591925,0.01661172,0.25452989,0.01169668,-0.01809433,0.02692076,0.04757135,0.03248926,0.01816421,0.02969585,-0.00967383,-0.05062141,0.04736667,-0.00920307,-0.03631961,0.05987281,-0.00284417,0.05901747,-0.03865951,0.03103044,-0.08888058,0.00007846,-0.08574905,-0.03367967,0.09926669,0.04307169,-0.04134785,-0.02730604,0.03758781,0.0132453,-0.0067548,-0.04725087,-0.02916599,-0.01815441,-0.01828946,0.0225515,0.01304972,0.00210718,-0.03300785,0.00393347,0.02960279,-0.00001131,0.09069764,0.0346759,-0.01971216],"last_embed":{"hash":"1w43a6q","tokens":249}}},"text":null,"length":0,"last_read":{"hash":"1w43a6q","at":1753423457060},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{1}","lines":[63,66],"size":574,"outlinks":[{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreeNumbersPosition.OUT","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09167737,-0.03854552,-0.02449075,-0.07894642,-0.05411079,0.05567513,-0.00438171,-0.02208212,0.05299613,-0.01549279,0.00754968,0.00444413,0.02191514,0.00709149,-0.05010013,0.01927393,0.01520868,-0.01556717,-0.07088459,-0.00122321,0.07706727,-0.04454958,-0.02520501,-0.08050295,0.05137867,-0.00903372,-0.06242722,-0.09253475,-0.04031044,-0.26208097,0.00523595,0.06560047,0.04544402,-0.06196036,-0.08122585,-0.03593705,-0.07982737,0.06587091,-0.07314075,0.0114308,0.00249351,0.00630056,0.00284827,-0.00420765,0.01523534,-0.03354085,-0.00546441,-0.01786707,0.03776904,-0.00898264,-0.03628782,0.00124409,-0.00799377,0.06560447,0.05370774,0.03339846,0.03365012,0.07112844,0.00707466,0.06615671,0.04853231,0.02485829,-0.16335915,0.07602055,-0.00314266,-0.02480215,0.00616591,-0.02722289,-0.04961178,0.08195062,-0.00912564,0.04660052,-0.00665384,0.05360729,0.02953109,-0.05707158,-0.05072864,-0.04926696,-0.01704568,0.04437533,-0.08090455,-0.03495039,0.01639133,0.00095895,0.00699131,0.03703947,0.08744002,0.03853189,0.02968748,-0.03544024,0.04456719,0.04779772,0.01628429,0.02269159,-0.01095268,0.01476174,0.00100341,-0.00921117,0.01974877,0.11548536,0.02529344,0.0027492,-0.02431972,0.05230324,0.01907674,-0.02184402,-0.02248162,-0.00712761,-0.03788617,-0.01092262,0.05474904,0.00391949,0.08497554,-0.0580425,-0.03945832,0.02976579,0.0037086,0.00500983,0.06813555,0.03994604,-0.01988672,0.01847314,-0.0142808,-0.00286398,0.00583347,0.0370161,0.05733667,0.04842016,0.03000782,0.04944495,0.03035728,-0.0146622,-0.1128407,-0.03296119,-0.05543304,0.02155207,0.00616513,0.005616,-0.02259003,-0.02042067,-0.01542252,-0.05841151,0.07402943,-0.09987841,0.00626104,0.09915522,-0.02209738,0.03011025,0.04872143,0.00405945,-0.04707806,0.00178118,-0.0477551,-0.04859815,0.01301462,-0.01375568,0.07027844,0.03581962,-0.06400201,0.0298309,-0.01752576,-0.02116895,-0.00185758,0.14980043,-0.01086093,-0.06161229,-0.02097105,0.00653552,-0.06003133,-0.1076896,-0.04121891,0.0114968,-0.03058022,0.0216369,0.04430103,-0.01802677,-0.07774238,-0.039303,0.02888217,0.032475,0.02272013,-0.01655476,0.00300458,0.01316613,-0.03495017,-0.05432383,0.02041142,-0.01251821,0.01171586,-0.00399352,-0.04835008,-0.01913831,-0.01707322,-0.00462472,-0.01669677,-0.0400274,-0.01387745,-0.05104043,0.0541231,-0.03620227,0.02547045,0.02335312,-0.01551408,0.02499856,0.00423239,0.07994778,0.00299956,-0.06327846,0.10582506,0.02746275,-0.04347598,-0.00051105,0.06121692,0.01942268,-0.05484962,0.01744402,-0.00584977,0.0071914,0.02148425,0.02739577,0.04313585,0.0229071,-0.05458507,-0.20994112,-0.02999469,-0.02982008,-0.0029644,0.01750751,0.00200804,-0.0069091,-0.03087829,0.07279912,0.13133572,0.07410245,-0.05787845,-0.03555445,0.02665365,-0.02719669,-0.01450522,-0.10341728,-0.06676467,-0.03452545,0.02887765,0.01192573,0.01423022,-0.07351699,-0.04684091,0.07636778,-0.01754432,0.12912729,0.01532074,-0.0378788,0.01383742,0.10234826,-0.01912215,-0.0086897,0.03010812,0.03595249,0.03789001,-0.05123785,-0.0146589,-0.04636597,0.0174811,-0.05558993,0.04586821,0.00392671,-0.08256413,-0.0480644,-0.01351534,-0.03681168,0.01256739,0.04900807,0.02110737,0.00507364,-0.0303863,0.02953528,0.03300681,0.0935009,-0.00056581,-0.05947516,-0.01073025,-0.0236901,0.02840616,-0.03788407,-0.04555408,0.05108939,-0.00642824,0.03460065,0.05775451,0.00905676,0.00484936,0.03553689,-0.00057611,-0.00571755,0.03343112,-0.01351907,0.00071751,-0.00256783,0.02841941,0.06587686,-0.01855506,0.00781109,-0.02290156,0.05812071,-0.03920683,0.031549,0.03297426,0.0436857,-0.02122462,0.05330494,0.08767672,-0.01022414,0.04056735,0.02003697,0.05216362,0.00703531,0.01981922,0.03893919,-0.00806502,-0.27047202,0.01773388,-0.06439842,0.03962827,-0.01679557,-0.0063391,-0.00603091,0.03920467,-0.01207347,-0.02133086,0.00127801,0.0518783,0.06326855,-0.06113221,-0.02223805,-0.02456055,0.01892305,-0.06065824,0.06762268,-0.03074632,0.10455988,0.01717894,0.25550392,-0.0019139,0.00033651,0.01449672,0.0426071,0.02735115,-0.01449479,0.03591841,-0.00319701,-0.04311971,0.06782328,-0.02289654,-0.02574374,0.02627003,0.01385439,0.0548107,-0.03133354,0.01417362,-0.10544354,0.0048023,-0.07537066,-0.02880511,0.09972573,0.02206426,-0.03396396,-0.04712347,0.03811963,0.023371,-0.03195589,-0.03666111,-0.03861389,-0.02948002,-0.02282199,0.02831174,0.01692633,0.01820046,-0.0170085,0.01489518,0.01924212,0.00359532,0.07417719,0.01937967,-0.01819202],"last_embed":{"hash":"tv46b7","tokens":227}}},"text":null,"length":0,"last_read":{"hash":"tv46b7","at":1753423457135},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##_Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.#{6}","lines":[72,75],"size":558,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08551776,-0.02842927,-0.03675963,-0.06670307,-0.04479218,0.04529619,-0.02091618,-0.03642344,0.04837633,0.02873825,-0.01077614,-0.00648254,0.0529244,0.00286898,-0.0181497,0.00203232,0.00758091,0.00259896,-0.09583543,-0.00135392,0.05771828,-0.04930213,0.0040367,-0.12324543,0.0215783,-0.01653524,-0.02531284,-0.05217414,-0.03352947,-0.25045979,0.00894266,0.04383803,0.00589525,-0.06762568,-0.09699641,-0.01505294,-0.06371745,0.01346928,-0.07813699,0.02488833,0.01174041,0.00725532,-0.00506169,-0.03057237,0.04076083,-0.04081984,0.01657843,0.00082734,0.07560614,0.01309206,-0.04472232,-0.00364506,-0.01522487,0.07565336,0.0556163,0.02789913,0.05488612,0.09983706,-0.04665798,0.0233445,0.07284848,0.08303842,-0.14708686,0.06857602,-0.01699856,0.00956234,0.03244629,-0.02404872,-0.0271745,0.04099046,-0.02094929,0.04849418,-0.0381723,0.08382307,0.02411478,-0.02074875,-0.04582538,-0.02602102,-0.03287571,0.06892921,-0.05086732,-0.03447869,-0.03077448,-0.02146134,0.02818777,0.03465488,0.08226934,0.02164518,0.04422911,-0.05716522,0.02378821,-0.02952198,0.03299956,0.0489286,0.01704296,0.01316949,0.05238353,-0.01784216,-0.02347145,0.11481449,0.02481367,0.0200325,-0.04751372,0.01814386,0.0238508,-0.02751187,-0.04034122,-0.00665339,-0.06213184,-0.0115436,0.04230063,0.01384101,0.06558888,-0.04211409,-0.06245054,0.03021593,-0.01573094,-0.01175222,0.0568202,0.02241279,-0.02842604,0.04733585,-0.02573313,0.02226263,-0.00938973,0.00721524,0.03919062,0.01355665,0.03681611,0.0696661,0.01875653,0.035716,-0.13185874,-0.05432581,-0.03091169,0.01778591,-0.02917076,-0.00152029,-0.00423654,-0.02512033,0.01472234,-0.05313361,0.0678609,-0.10714976,0.00029725,0.07382434,-0.03567797,0.02931209,0.06231606,-0.01343551,-0.05243922,-0.01357889,-0.05644397,-0.01949263,0.01569285,0.04205599,0.1086871,0.05444655,-0.06034654,0.02760218,-0.0183197,-0.01272757,-0.00031926,0.10683435,0.0307526,-0.09377361,-0.03360783,0.01570375,-0.05994375,-0.04598471,-0.03932841,0.04798004,-0.05718554,0.01959687,0.08373404,-0.01242759,-0.05453929,-0.04288097,0.02457872,0.01625679,0.02585579,-0.04664563,-0.005463,-0.01699923,-0.0598461,-0.10620957,0.01126439,-0.05096455,0.00562313,0.0179201,0.01814188,-0.00733069,-0.01704595,-0.02021552,-0.01178795,-0.03725848,-0.02464421,-0.03112115,0.06703681,-0.01579314,-0.00963992,0.01414879,-0.01088234,-0.00158939,0.01485767,0.03142667,0.01325387,-0.06827947,0.10471328,0.00721692,-0.03076879,-0.02364022,0.07924405,0.05028266,-0.08049902,0.05240688,-0.01080921,-0.00274401,0.03223999,0.00662453,0.05022859,0.06521019,-0.07996687,-0.19502018,-0.02121735,-0.06370631,0.00895519,0.00703773,-0.03594185,0.0368969,-0.02112217,0.05792701,0.06761906,0.07274289,-0.07212398,-0.03421294,0.05280939,-0.01375395,-0.00275637,-0.08617856,-0.07460332,-0.00881797,0.03201144,0.02294567,-0.00061401,-0.04343541,-0.03430209,0.08518837,-0.05402835,0.14234714,0.040704,-0.01891287,0.00230481,0.10793795,-0.02498876,0.00813385,-0.04635327,0.01667103,0.06509402,-0.02644352,0.01887308,-0.02948178,0.02182588,-0.04264776,0.05229416,-0.02892065,-0.09653551,-0.00466868,-0.02553728,-0.04707927,-0.03604681,0.03847674,0.07813544,0.03199793,-0.01212305,0.03754009,0.03945003,0.04610147,-0.03127604,-0.04824097,-0.00573885,-0.02024008,0.02520831,-0.00680827,-0.07611372,0.04009172,-0.02960918,0.0450919,0.01171684,-0.0159013,0.01768334,0.05988373,0.01826715,0.00360998,0.06227037,0.02939122,0.05246513,0.00986197,0.03142393,0.04361435,0.00037059,0.00078238,-0.02044455,-0.00562934,-0.01097542,0.04332401,0.04270515,0.06981733,-0.01680435,0.08941358,0.04232271,0.02444936,0.00233973,-0.02148167,0.02948022,-0.01407438,0.03168344,0.02821945,0.00520309,-0.25237674,0.04674315,-0.0496973,0.02262385,-0.00755787,-0.02673973,-0.01224517,-0.04678799,-0.01575699,0.00420194,0.03471065,0.00797869,0.05832135,-0.0564157,-0.03383648,-0.00232604,-0.00678333,-0.01704132,0.07377358,-0.05402166,0.097731,0.03218155,0.25325283,0.01953839,-0.00129741,0.03372952,0.01720909,0.01955932,0.02898172,0.04615338,-0.02388098,0.00122265,0.05590772,-0.00070304,-0.04588414,0.04810974,-0.01865841,0.05232793,-0.0191689,0.00189314,-0.10772888,0.00189956,-0.04358252,-0.01981148,0.08785676,0.00756425,-0.02958422,-0.05580711,0.037662,0.0334556,-0.05692479,-0.04124136,-0.03414401,-0.03798398,0.00434598,-0.00599525,0.00635884,0.00603432,-0.02138023,0.03119732,0.04293865,0.00266169,0.06644376,0.00645373,-0.01597441],"last_embed":{"hash":"10x4319","tokens":443}}},"text":null,"length":0,"last_read":{"hash":"10x4319","at":1753423457203},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>","lines":[76,117],"size":2445,"outlinks":[{"title":"Skip lottery software creates positional systems for Powerball, Mega Millions favorite numbers.","target":"https://saliu.com/images/skips-generator.gif","line":7},{"title":"Generate lottery combinations with favorite lotto numbers or bankers strictly in-position.","target":"https://saliu.com/images/skips-input.gif","line":16},{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreePosEU.OUT","line":41}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,-0.********,0.********,0.********,0.********,0.********,0.********,0.0049344,-0.********,0.********,-0.********,0.********,-0.********,0.********,0.********,-0.********,0.0113523,-0.********,0.********,-0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.********,0.********,-0.********,-0.********,0.0022969,-0.********,0.********,-0.********,0.01562675,0.00893828,-0.0111854,0.00407104,-0.03543641,0.02259259,-0.05887679,0.01934334,-0.01006997,0.10410538,0.02741745,-0.03773084,-0.01315515,0.01034873,0.06051323,0.03677761,0.00710805,0.04636587,0.10489701,-0.04271969,0.01835728,0.06143061,0.08028176,-0.1462055,0.09276737,-0.03004533,-0.01331832,0.03417271,-0.01812078,-0.04308959,0.03995043,-0.04605944,0.04777608,-0.04706516,0.09693994,0.02430145,-0.04516184,-0.04446789,-0.02932214,-0.01559814,0.06860737,-0.08014697,-0.01889969,-0.02831142,0.00062126,0.00397308,0.03766316,0.09811588,0.02710317,0.033613,-0.04765522,0.02029109,-0.04223564,0.02919967,0.04069141,-0.01510081,0.00296874,0.04752458,0.00718699,-0.04695228,0.11645987,0.0043269,0.02224469,-0.0628312,-0.00607191,0.03441043,-0.00935624,-0.03019413,0.00453463,-0.07541043,0.01009616,0.05281232,0.01356769,0.06596276,-0.05251959,-0.04157406,0.04583251,-0.01770263,0.01313022,0.04877035,0.01104971,-0.00890326,0.03818267,-0.02717874,0.01190427,-0.01744684,-0.00432381,0.04596882,0.00688303,0.04248146,0.0560242,0.03903076,0.04742128,-0.12808709,-0.02663572,-0.04633536,0.04660796,-0.0363188,-0.03314612,-0.02113985,-0.07128618,0.03439523,-0.05477418,0.03948706,-0.07292824,0.00163552,0.09198174,-0.06290744,0.01476653,0.08217184,-0.03619749,-0.05329822,-0.00150577,-0.06477137,-0.01206651,0.01691458,0.03445898,0.1355689,0.03613529,-0.03853089,0.04133638,-0.01300876,-0.02845487,-0.0093228,0.11579647,0.02494684,-0.05986477,-0.05299097,0.00619131,-0.04633745,-0.05139438,-0.01912502,0.02651053,-0.04506855,0.04287476,0.08927239,-0.00214834,-0.02613245,-0.04421445,0.03503556,0.00989599,0.02772742,-0.06438673,-0.00198994,-0.0032903,-0.04853195,-0.0946965,0.00603326,-0.04896864,0.01130448,0.04324102,0.02411804,-0.02407765,-0.00270533,-0.02543815,-0.03623677,-0.04595931,-0.02363676,-0.03225644,0.04957204,-0.04952701,0.01355841,0.02055731,-0.02210038,0.00252101,-0.00654093,0.03599025,0.01650723,-0.06262412,0.13239348,0.01518024,-0.05598116,-0.00668534,0.09179928,0.01273944,-0.06903648,0.03536439,-0.00434006,0.00691332,0.04167417,0.02372224,0.04289187,0.05764163,-0.0875035,-0.18755706,-0.02507316,-0.04142553,-0.02154252,0.00535414,-0.0334243,0.03110498,-0.00697736,0.06031399,0.05682497,0.05633064,-0.06777515,-0.02258958,0.03133446,-0.01469607,-0.01416343,-0.0613614,-0.08422912,0.02741269,0.01796764,0.00314058,0.00974318,-0.06608514,-0.01328441,0.10667645,-0.03766642,0.12210353,0.04337486,0.01757677,-0.00819517,0.11023527,-0.01993904,0.0071976,-0.0851564,0.01664052,0.07440243,-0.06529389,0.04119867,-0.0275262,0.02501781,-0.03019703,0.02937145,-0.02909234,-0.08490498,-0.00682065,-0.04091709,-0.04746876,-0.05235786,0.03359597,0.06962501,0.00954061,0.0051852,0.03021931,0.05262401,0.04071986,-0.02065989,-0.03849612,-0.00117362,-0.00486052,-0.00590573,-0.00159842,-0.07409135,0.05113189,-0.03283102,0.0287835,0.00609982,-0.0207455,0.01544741,0.04840905,0.01284299,-0.00203903,0.05749463,0.02574098,0.05203954,0.00030705,0.02056784,0.01989147,0.01809732,-0.0034516,-0.03200578,0.00097555,-0.00910121,0.06383131,0.03375908,0.06242018,0.00069646,0.06962911,0.04595843,0.03289825,0.00932215,0.00001413,0.00509723,0.00720265,0.04586349,0.03076213,-0.00651961,-0.23208462,0.03878478,-0.0467501,0.03231696,0.00670758,0.00376883,-0.02418255,-0.05455795,-0.03115617,0.0117622,0.00266682,0.01325366,0.02390952,-0.07084409,-0.05770721,-0.01708312,-0.0005563,-0.02330262,0.09096441,-0.06910447,0.08740288,0.01905037,0.26103044,0.03325979,0.00043984,0.02647983,0.03484174,0.0302122,0.06072785,0.04285245,-0.00338913,-0.01101212,0.04763334,0.0042381,-0.03535774,0.04425836,-0.01682578,0.05997969,-0.00085476,0.01746786,-0.10430466,0.00052655,-0.07510922,-0.02588684,0.07247964,0.00088132,-0.02974299,-0.02748699,0.03503525,0.03475881,-0.04658741,-0.04213762,-0.04184966,-0.02885066,-0.00608751,-0.01474695,-0.00096942,0.00172748,-0.02213832,0.0394764,0.03016915,-0.02871719,0.10069818,0.00601436,-0.0204764],"last_embed":{"hash":"1o60wie","tokens":228}}},"text":null,"length":0,"last_read":{"hash":"1o60wie","at":1753423457345},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{6}","lines":[85,90],"size":570,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07633302,-0.04352195,-0.03648941,-0.05301217,-0.04600058,0.04152701,-0.03869298,-0.02225777,0.06173037,0.02142334,-0.00763903,-0.02420606,0.0350834,0.00568684,-0.02375267,-0.00846627,0.01903534,-0.0138162,-0.08188858,0.00252877,0.06223341,-0.03710406,-0.00189152,-0.13362032,0.02266031,-0.00845787,-0.03361207,-0.05421235,-0.0251534,-0.24605714,0.01455742,0.04138375,0.00856754,-0.06147057,-0.1015772,-0.02770251,-0.05498909,0.01121237,-0.09072595,0.0456847,0.02077834,0.01140951,-0.0178374,-0.03233034,0.02956514,-0.01392623,0.00901865,0.00291599,0.02385955,0.02629731,-0.0353091,0.00618427,-0.0074708,0.0694183,0.06080189,0.03206285,0.03766199,0.0858845,-0.04494323,0.04198909,0.06766201,0.0885903,-0.15998363,0.04714974,-0.02165546,0.03366519,0.04350372,-0.01364874,-0.02913365,0.05838633,0.00563019,0.01793395,-0.02026927,0.07550275,0.02787652,-0.02209462,-0.03086865,-0.04999714,-0.0394877,0.0773905,-0.03355952,-0.04534636,-0.01705133,-0.04394452,0.02698264,0.03202555,0.07452532,0.01776836,0.05914134,-0.06204211,0.02503088,-0.01954441,0.02192287,0.04578914,0.02370897,0.01846397,0.04848898,-0.00057113,0.00909215,0.12283057,0.02894464,0.00868164,-0.03437418,0.02945454,0.02297968,-0.0107722,-0.02915544,-0.00751893,-0.03712005,-0.01403469,0.03004332,0.02076302,0.07008697,-0.03879516,-0.06528096,0.01204079,-0.0080818,-0.03659021,0.07060327,0.04377635,-0.04223727,0.04824705,-0.00643236,0.03280588,-0.00684318,0.00442977,0.02739028,0.02220378,0.02663273,0.05487507,0.01494369,0.00324106,-0.13481697,-0.05262533,-0.03676101,0.00422397,-0.01800557,0.01324017,0.00560664,0.00813505,-0.00415266,-0.06194669,0.08204799,-0.11675186,0.00097445,0.06537477,0.00318235,0.02323623,0.05624057,-0.00124106,-0.03917746,0.00706892,-0.04765768,-0.04354685,0.00055721,0.04321484,0.07430197,0.08053428,-0.04834602,0.0080076,-0.01754243,-0.01715401,-0.00653152,0.12479886,-0.0004803,-0.11594617,-0.03400654,0.00933571,-0.05202883,-0.04293059,-0.0339438,0.05619159,-0.05267952,0.02180099,0.07073337,-0.0056531,-0.06653515,-0.04556238,0.00503772,0.02271849,0.01913397,-0.04677226,-0.0029404,-0.00196644,-0.05520982,-0.09666988,0.00494952,-0.03001658,-0.00557262,0.00141386,0.00641865,-0.0079867,-0.04322903,-0.01101964,-0.01305296,-0.03323764,-0.02304851,-0.00488123,0.07045714,-0.00483027,-0.01594603,0.03436996,-0.00783636,0.02363067,0.04218466,0.04946912,-0.00468579,-0.05797677,0.09390557,0.01440388,-0.02330626,-0.00943909,0.07414049,0.04783648,-0.07127532,0.02974869,0.00382925,0.00466936,0.03349042,0.01137667,0.056094,0.04401133,-0.09023398,-0.20850989,-0.04057925,-0.05458582,0.02428692,0.02798698,-0.02619831,0.03082314,-0.0159888,0.06152464,0.08246829,0.04682893,-0.08202092,-0.05098656,0.04036023,-0.01501209,-0.00141186,-0.10296334,-0.07809505,-0.01887422,0.02209578,-0.00285361,0.00479596,-0.03230466,-0.03344879,0.08501779,-0.06211291,0.13849732,0.02539506,-0.03083324,-0.01409066,0.08292284,-0.03001608,0.01010292,-0.03210819,0.021571,0.05947992,-0.01664671,-0.00847853,-0.03189032,0.00626154,-0.05098776,0.03745609,-0.0210392,-0.09756442,-0.02391708,-0.00384402,-0.04515635,-0.03679627,0.05356024,0.07554708,0.01969475,-0.00193703,0.04494532,0.06678198,0.0664463,-0.04241283,-0.07160653,-0.01085762,-0.01382102,0.04530866,-0.00156629,-0.05245399,0.04055989,-0.04230953,0.06083525,0.02093782,-0.01686752,0.00007725,0.04686607,0.01568025,0.00068466,0.06914924,0.01722318,0.02147307,0.00501161,0.03551434,0.04457856,0.01064144,-0.00345048,-0.01014779,0.02984305,-0.01853361,0.04301586,0.01381895,0.06570683,0.00734041,0.07071195,0.05616717,0.00954049,0.01993112,-0.01648322,0.04042983,-0.02051792,0.0288106,0.03112878,0.01097157,-0.25186425,0.03925541,-0.061823,0.02113863,-0.02836567,-0.0355309,-0.0141499,-0.03311149,-0.0306957,-0.02272966,0.06125147,0.01356148,0.05593274,-0.03675456,-0.01626531,-0.01045765,0.00700416,-0.03449717,0.07736868,-0.0609148,0.10710458,0.03683691,0.26911378,0.03205557,-0.00367243,0.02646046,0.0158095,0.02875053,0.02026725,0.02861457,-0.02473797,0.00005156,0.04380829,-0.00806062,-0.05230165,0.04647867,-0.01734523,0.05780958,-0.02558095,-0.00473097,-0.09312358,-0.01055156,-0.04347626,-0.01528646,0.09887685,0.01588165,-0.03231235,-0.06205568,0.06121223,0.03204333,-0.0501502,-0.05637047,-0.0503012,-0.01612011,0.00123398,0.00418511,-0.00757249,0.00146051,-0.0201628,0.02076347,0.02766307,0.027969,0.********,0.********,-0.********],"last_embed":{"hash":"sg63h1","tokens":194}}},"text":null,"length":0,"last_read":{"hash":"sg63h1","at":*************},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{7}","lines":[91,96],"size":517,"outlinks":[{"title":"Generate lottery combinations with favorite lotto numbers or bankers strictly in-position.","target":"https://saliu.com/images/skips-input.gif","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,-0.0370421,0.********,0.********,0.********,-0.0471549,0.********,0.********,-0.0042434,-0.********,0.********,-0.********,-0.********,-0.********,0.********,-0.********,-0.0320412,-0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,0.********,0.********,-0.********,-0.********,0.********,-0.********,-0.********,-0.********,0.********,0.03360664,-0.06033545,-0.01155761,0.02498327,0.08397534,0.03145354,0.01083217,0.04595931,0.08577736,-0.07617152,0.03645467,0.06542947,0.06746394,-0.16854842,0.02222792,-0.0132079,-0.0126424,0.02609086,-0.04951635,-0.03031002,0.05356001,-0.01329411,0.04571603,-0.03163004,0.066305,0.02047041,-0.03057047,-0.03853962,-0.05994538,-0.01682552,0.05791463,-0.06601046,-0.02074612,-0.02587624,0.00171692,0.01372742,0.00852267,0.08537035,0.03877342,0.02263224,-0.04879675,0.0268229,0.01449234,0.01526062,0.04507763,0.00270051,0.00631293,0.04096423,-0.00178579,0.00719858,0.1228606,0.00567544,0.01473467,-0.03229598,0.03074488,0.0195614,-0.01115135,-0.02270489,-0.01126108,-0.04957555,-0.01251503,0.04352086,0.00578388,0.09015227,-0.0800731,-0.05304946,0.01121123,-0.00101149,-0.00508083,0.06442349,0.02344162,-0.01915663,0.04203135,-0.01182162,0.01350054,0.00812217,-0.01075599,0.04034328,0.02803194,0.03707602,0.07442703,0.01835387,0.02562724,-0.11371179,-0.05317537,-0.05270899,0.01278404,-0.00294163,0.0196607,-0.02640958,-0.00636667,0.02547921,-0.07241079,0.0507495,-0.10243963,0.03205906,0.10448475,-0.03202524,0.04620213,0.10088208,0.00490986,-0.03164737,0.01699835,-0.0458418,-0.0495427,0.01083174,0.0026301,0.07743712,0.06288099,-0.05950444,0.05995559,0.01140294,-0.05465708,0.02151056,0.09065651,0.00399311,-0.08556522,-0.02128323,0.03465892,-0.03804433,-0.03866122,-0.01439411,0.05016979,-0.01572736,0.01387372,0.09127751,-0.00359391,-0.03070535,-0.03776041,0.00615611,0.04135687,-0.01550951,-0.05005478,-0.03193161,-0.00452046,-0.07583651,-0.09464064,0.02498461,-0.01464813,-0.01790977,0.0002335,0.01061138,-0.03287777,-0.06642269,-0.00404892,0.00055093,-0.04326578,-0.01746895,0.00316373,0.05092968,-0.01013183,0.01222449,0.00175589,-0.00875856,-0.00737451,0.02398666,0.04218023,0.00877337,-0.04338269,0.07760147,0.02669694,-0.03108404,-0.01299614,0.07156341,0.03481399,-0.05235734,0.02646586,-0.00118606,-0.0030056,0.04731711,0.0250929,0.03352099,-0.00313244,-0.0809672,-0.21279354,-0.00563481,-0.03088696,-0.02198497,0.00059082,-0.0338223,0.01102204,-0.00075349,0.08686819,0.08855601,0.06699317,-0.04262637,-0.01687073,0.04847979,0.00687652,-0.03384266,-0.07163364,-0.05389573,-0.01935017,0.02852614,0.00228379,-0.00759603,-0.04053375,-0.04198627,0.08018163,-0.04569835,0.14989389,0.05783906,-0.02787135,0.00128828,0.08754015,-0.0137637,-0.02218594,-0.04596784,0.03750734,0.05348322,-0.04426131,-0.02296436,-0.02008504,-0.00316721,-0.06630191,0.04795454,-0.00579682,-0.07412259,-0.01253647,-0.03281903,-0.03946086,-0.01792507,0.04645044,0.06709024,0.01538124,-0.03713702,0.04474397,0.04994188,0.07886833,-0.02296172,-0.07833796,-0.02183112,-0.02658302,0.03832105,-0.01962414,-0.05564586,0.07235376,-0.02581145,0.02606588,0.01495797,-0.03072769,-0.01540705,0.01569126,0.00096551,-0.00115304,0.05048371,0.01920617,0.02871691,0.01130725,-0.00427553,0.05567566,-0.00675292,0.01422062,-0.02709824,0.03549875,-0.05378683,0.0306053,0.01138344,0.06618112,-0.01157162,0.08802921,0.0626621,-0.01563913,0.02776669,0.00065428,0.03583536,0.01430528,0.03243383,0.04657809,0.01362226,-0.27665904,0.03295437,-0.0774233,0.03409598,0.0175418,-0.01558987,-0.00159992,-0.0295313,-0.02860111,-0.01014387,0.065671,0.04254351,0.01935634,-0.08736339,-0.00535677,-0.01545419,-0.01388361,-0.03871381,0.04926445,-0.04341269,0.09029255,0.02603706,0.25308314,0.03290329,0.00538553,0.02439282,0.02188929,0.04070537,0.01057098,0.03859356,-0.01853213,-0.0335245,0.04587572,0.00377571,-0.03949114,0.06888615,-0.01281968,0.04237448,-0.01654868,0.0065158,-0.09786776,-0.03607807,-0.0645398,-0.00014801,0.11913459,0.01857325,-0.04937061,-0.04784858,0.07097668,0.03655077,-0.04469818,-0.07186894,-0.05102391,-0.01093402,-0.0057,0.02232497,-0.00642253,-0.01414153,-0.02774757,0.01196679,0.04780556,0.01626356,0.06323975,0.02031823,-0.00029267],"last_embed":{"hash":"137e2fk","tokens":202}}},"text":null,"length":0,"last_read":{"hash":"137e2fk","at":1753423457471},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>#{16}","lines":[108,117],"size":400,"outlinks":[{"title":"_download (right-click) or view here_","target":"https://saliu.com/freeware/ThreePosEU.OUT","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,-0.********,0.********,0.********,-0.********,-0.********,0.********,0.********,0.********,0.********,0.********,0.0204595,-0.********,-0.********,0.********,-0.********,-0.********,-0.********,0.********,-0.********,-0.********,-0.054769,-0.********,-0.231922,0.********,0.********,-0.0029932,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,0.********,0.********,0.********,-0.0167534,0.********,-0.********,0.********,-0.********,0.0315599,0.********,-0.********,-0.********,-0.********,0.********,0.0550474,0.********,0.********,0.********,-0.********,0.********,0.********,0.********,-0.********,0.********,-0.********,-0.0034035,0.********,-0.********,-0.********,0.0455705,0.********,0.********,-0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.07183366,-0.03529362,-0.02645081,-0.03232054,-0.022853,0.01728334,0.05499899,0.09345118,-0.00351544,0.04738751,-0.05066895,-0.01133714,0.0017989,0.04377237,0.0305964,-0.00716262,0.01660975,0.04351046,-0.00845044,0.00940141,0.12899798,0.00399187,0.03564726,0.00133129,0.02756425,0.01858044,-0.02395136,-0.05753461,0.0059275,-0.06837046,-0.0015907,0.04338393,0.01268057,0.06114412,-0.06243471,-0.0682987,0.02218948,-0.01668491,-0.00910178,0.05904701,0.02960682,-0.030685,0.05450938,0.00555459,0.0253666,0.01976878,0.00059927,0.01960329,0.02298229,0.04106184,0.06818998,0.01672849,0.00651243,-0.1029469,-0.00855349,-0.06604683,0.00296695,-0.02755754,0.01596197,-0.01846467,0.00466688,0.01065901,-0.06461725,0.08492488,-0.09740245,0.0401425,0.08864481,-0.04931382,0.05275586,0.00859838,0.00076445,-0.00904216,-0.01191808,-0.01056973,-0.04152905,0.01635596,0.02626383,0.09429008,0.06743513,0.00457546,0.04694483,-0.04000022,-0.03440319,-0.00735244,0.08997602,0.02437236,-0.09679199,-0.02204766,0.01143057,-0.05576765,-0.04475665,-0.02440757,0.06549094,-0.02836424,0.02170485,0.07222226,-0.01838399,-0.03795408,-0.01241549,-0.00302115,0.03962235,-0.02853811,-0.05089795,-0.01657089,-0.02428372,-0.05542503,-0.08005406,0.00266904,-0.06042234,-0.00570239,0.02040607,-0.02000194,-0.01324344,-0.03760533,0.00355837,-0.02774147,-0.04937728,-0.03199062,-0.02796283,0.03629737,0.00797872,-0.02583198,0.03104404,-0.03327169,0.00691225,0.01949942,0.03479626,0.00430213,-0.07188711,0.08616494,0.03470803,-0.00385611,0.00451829,0.06731202,0.03714142,-0.06649924,0.01245119,-0.00192523,0.00590207,0.01361777,0.00972708,0.06117834,0.04705057,-0.09213782,-0.2116857,-0.01809042,-0.05001584,-0.01474458,0.00943791,-0.03215443,0.03672078,-0.0132466,0.04608306,0.07136409,0.04161497,-0.03700092,-0.01708962,0.02579588,-0.00496337,-0.03325365,-0.05955658,-0.07742268,-0.00098576,0.01336838,0.02422118,0.03930173,-0.06597625,-0.05091406,0.08876166,-0.03637616,0.14273033,0.02540159,0.00084257,0.0252102,0.09672384,-0.03402316,-0.01317469,-0.0055028,0.04571803,0.05926297,-0.00426113,0.01644821,-0.04035316,0.01740289,-0.06139503,0.04806452,-0.00370597,-0.09380852,-0.0124764,-0.03598398,-0.03781363,-0.02738278,0.03794373,0.03798504,0.02667418,-0.00163315,0.0342765,0.02660352,0.05978051,-0.02524497,-0.05765621,0.00358485,-0.04548203,0.03510839,-0.00286569,-0.06945739,0.0673297,-0.02948349,0.06035545,0.02451368,-0.03910888,0.01855096,0.05088615,0.02952143,-0.02036959,0.06197263,0.02339765,0.06536747,0.04796292,-0.01750788,0.00545781,-0.04554167,0.02096872,-0.03095433,0.01653477,-0.04619732,0.04877789,-0.00445508,0.05878651,-0.03223801,0.07646979,0.07649583,0.0051778,-0.00221496,-0.02115612,0.04646035,0.00761007,0.01046944,0.0564537,0.01299191,-0.25816423,0.01772746,-0.06411572,0.03171644,-0.05134517,-0.02211588,0.01741756,-0.03501645,-0.03055286,0.0030915,0.01345103,-0.00130035,0.04318398,-0.03807124,-0.00623872,0.00470593,0.02243282,-0.05785784,0.047447,-0.06274222,0.07017431,0.02833413,0.27442798,0.04283253,0.02517824,0.01292329,0.02303216,0.01155124,0.01847404,0.03556935,0.05013902,-0.01521142,0.00179642,-0.04193315,-0.04283043,0.07939307,-0.00548662,0.02813554,-0.02241863,-0.00652317,-0.11403845,0.00676483,-0.05121882,-0.01618081,0.11382615,-0.03360336,-0.03531656,-0.0705519,0.********,0.********,-0.********,-0.********,-0.********,-0.0184173,-0.********,-0.********,0.********,0.********,-0.0039746,0.********,0.********,0.********,0.********,-0.006622,-0.********],"last_embed":{"hash":"1fjd1rk","tokens":232}}},"text":null,"length":0,"last_read":{"hash":"1fjd1rk","at":*************},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>","lines":[118,131],"size":766,"outlinks":[{"title":"The positional frequency systems are powerful lottery tools to crack the jackpot.","target":"https://saliu.com/images/pair-grid.gif","line":5},{"title":"Place the hottest lotto numbers in their positions as favorite bankers to greatly improve chances.","target":"https://saliu.com/images/pair-grid-combinations.gif","line":9},{"title":"Simple text files can generate fast combinations with favorite lotto numbers in fixed positions.","target":"https://saliu.com/images/pair-grid-input.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,-0.********,-0.********,-0.********,0.0428174,0.********,-0.********,0.********,-0.********,-0.********,-0.********,0.********,0.0269359,-0.********,0.********,0.********,0.********,-0.********,-0.********,0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,0.********,0.********,-0.********,-0.********,0.********,-0.********,0.********,-0.********,0.********,0.********,-0.********,-0.0217632,-0.********,0.********,0.********,0.********,0.********,0.********,-0.********,0.********,0.********,0.********,-0.********,0.********,-0.********,0.********,0.********,0.0029466,-0.********,0.********,-0.********,0.********,-0.********,0.********,0.********,-0.********,-0.********,-0.0415857,-0.********,0.05899969,-0.06916179,-0.02797188,-0.03698518,-0.02231165,0.03392597,0.03768092,0.09016331,0.03178784,0.03866664,-0.06239759,0.01984209,-0.01319181,0.00596974,0.02062402,0.00334199,-0.02048315,0.04425898,-0.0003523,0.00417997,0.12410942,0.00426872,0.02762012,-0.04386704,0.03640929,0.0223994,-0.01064721,-0.05366168,-0.0069803,-0.05653081,-0.02257847,0.0408412,0.00097318,0.08626959,-0.10015591,-0.05214532,0.03202404,-0.02196405,-0.02275996,0.06801104,0.01756538,-0.02267301,0.04587046,-0.02383078,-0.01215401,-0.01604988,0.01103314,0.0323934,0.03724673,0.03889816,0.06668603,0.02913767,0.01167885,-0.14961351,-0.01339641,-0.05291696,-0.00118501,-0.01923865,0.02112763,0.00481941,-0.02052338,0.00588462,-0.06478779,0.09472617,-0.09814828,0.03236485,0.08651465,-0.0515107,0.05373129,0.06180637,-0.01503212,-0.02738032,-0.02080373,-0.02763524,-0.05978169,0.01682462,0.02137539,0.08519513,0.05534822,-0.03855289,0.03304394,-0.00251481,-0.03443895,0.00205696,0.0887297,-0.00182356,-0.07639546,-0.02027657,0.04125471,-0.03740107,-0.03401842,-0.00399473,0.04884011,-0.02669968,0.03775885,0.05970847,0.00022571,-0.05238307,-0.02923832,0.02163139,0.0547087,-0.02554384,-0.04761293,-0.01974755,-0.00108164,-0.05996093,-0.06732038,0.00291659,-0.03693376,0.03338835,0.01783004,-0.02223979,-0.02729916,-0.05938338,-0.0093551,-0.0142423,-0.06439851,-0.0414976,-0.01532311,0.04337243,0.00411778,0.00539328,0.02809414,-0.02670068,0.02753289,0.01874678,0.05629094,0.00072361,-0.06702068,0.1021741,0.00933839,-0.03484564,-0.00002042,0.05124555,0.04795871,-0.07479849,0.01873191,0.01452875,0.01424677,0.03344955,0.03581668,0.06195419,0.02940485,-0.09008439,-0.19943127,0.00690969,-0.05717644,-0.00451699,-0.02086717,-0.02390275,0.02743089,-0.01474299,0.05397603,0.08045273,0.04705881,-0.02513782,-0.01413165,0.05050321,-0.02688124,0.00036245,-0.08170899,-0.04762105,-0.00570872,0.0143514,0.00650161,0.02345113,-0.06833577,-0.04334402,0.0795852,-0.03957912,0.12672715,0.0103672,-0.0080811,0.01043712,0.12175855,-0.02973657,-0.02086521,0.00558893,0.02858633,0.0512143,-0.06439596,0.02418689,-0.04433077,0.03020815,-0.05562837,0.05674742,-0.00159768,-0.08665771,-0.01118815,-0.03670679,-0.03066976,0.01181303,0.04630392,0.04258144,0.00660235,-0.01316739,0.03429403,0.06304769,0.05421899,-0.04814263,-0.0544499,-0.00892237,-0.04622681,0.03021841,-0.00370226,-0.05578207,0.05509634,-0.03049671,0.04315736,0.03732244,-0.01582455,-0.01318976,0.03303729,0.01098738,-0.02235904,0.04284674,0.01735079,0.02563195,0.00387,0.0124113,0.03573073,-0.01743461,0.00178579,-0.02304813,0.02439735,-0.05393372,0.05266405,-0.01603391,0.05340534,-0.00913968,0.05802536,0.05484907,-0.00409935,0.02320158,0.01901453,0.04489131,-0.0062795,0.02762072,0.04594916,0.00780268,-0.26014894,0.02810845,-0.07402358,0.01167093,0.01799324,-0.01334864,0.02243589,-0.03384311,-0.05988546,-0.01184197,0.03414486,0.01576773,0.02927092,-0.04397133,-0.0167933,-0.01306703,0.00352183,-0.03194864,0.07472715,-0.04493442,0.09349623,0.00963278,0.27842736,0.03715148,-0.00003295,0.00249399,0.01455574,-0.00054551,0.02074781,0.02804632,0.02539018,-0.02299988,0.03270115,-0.02056099,-0.04816589,0.07388375,0.00659054,0.06474318,-0.03752025,-0.00691179,-0.10888501,0.00029889,-0.04852045,0.00364099,0.11284257,0.00767469,-0.03514249,-0.********,0.********,0.********,-0.********,-0.********,-0.********,0.********,0.000355,0.********,0.********,0.********,-0.********,0.********,0.********,0.********,0.********,0.********,-0.********],"last_embed":{"hash":"9d0e2s","tokens":253}}},"text":null,"length":0,"last_read":{"hash":"9d0e2s","at":*************},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>","lines":[132,145],"size":817,"outlinks":[{"title":"Ranges in lottery refer to lotto numbers in ascending order or ranked by frequency.","target":"https://saliu.com/images/ranges-non-contiguous.gif","line":5},{"title":"Apply positional ranges with fixed favorite numbers to win lottery big-time.","target":"https://saliu.com/images/ranges-input.gif","line":9},{"title":"One input file with favorites or banker numbers works across multiple lottery programs.","target":"https://saliu.com/images/ranges-combinations.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,-0.********,0.********,-0.********,-0.0126259,-0.********,0.********,0.0354216,-0.********,0.********,0.********,0.********,-0.********,-0.********,0.086716,-0.********,-0.********,-0.********,0.********,-0.********,-0.********,-0.********,-0.********,-0.********,-0.********,0.********,0.********,-0.********,-0.********,-0.********,-0.********,0.********,-0.********,0.********,0.********,0.0088603,-0.********,-0.********,0.********,-0.********,0.********,-0.01799065,0.04752819,0.02387137,-0.04878115,-0.00270898,-0.02242207,0.093437,0.0572876,0.01886757,0.05043505,0.08401512,-0.03922508,0.03037911,0.06289636,0.09002487,-0.17374407,0.05858329,-0.00157786,-0.01875286,0.0049871,-0.02269885,-0.02604621,0.01603067,-0.01173881,0.03242513,-0.02026546,0.06907496,0.01665008,-0.04555886,-0.05291948,-0.05023692,-0.01554934,0.06977092,-0.04600681,-0.0401437,-0.02455411,-0.01464969,0.03680604,0.02650062,0.08834726,0.03324503,0.04401865,-0.08169883,0.02247174,-0.02026694,0.02866533,0.0439813,0.00474555,-0.00551408,0.02721949,-0.02422244,-0.02787011,0.13210997,-0.00352631,0.0420412,-0.04505096,0.03687683,0.04605962,0.01719028,-0.03460816,0.01186463,-0.04996863,-0.0229773,0.03944605,-0.00294328,0.08194059,-0.09696999,-0.056446,0.02472569,-0.01515604,-0.01507563,0.0544148,0.01088775,-0.01109747,0.04293303,-0.02022923,-0.00126986,0.006547,0.0026722,0.05341307,0.0215413,0.00028495,0.05628166,0.03347305,0.01287842,-0.13387907,-0.02504041,-0.0328076,0.00857683,-0.0212773,-0.0258132,0.00372754,0.01284107,0.00170067,-0.0463003,0.07876764,-0.10802962,0.04160619,0.0882467,-0.0522862,0.03333735,0.06726975,-0.01376313,-0.02164621,-0.00654329,-0.00914543,-0.03574035,0.04076938,0.01578291,0.07952219,0.05071163,-0.04919345,0.0432365,-0.02445085,-0.0354189,0.00719984,0.11655075,0.01571407,-0.08353625,-0.02316306,0.04386086,-0.03434351,-0.03728402,-0.00484597,0.06388339,-0.02534724,0.02329736,0.0867053,-0.01014816,-0.03139459,-0.03967515,0.00293533,0.04021212,-0.00151007,-0.04572652,-0.03425762,-0.01537552,-0.06163496,-0.08915532,0.01056991,-0.05253597,0.02818671,0.01118554,-0.02796462,-0.02103604,-0.0379088,-0.02183294,-0.00415948,-0.05936562,-0.04463826,-0.03400551,0.04058248,0.01029392,-0.02085779,0.02860945,-0.04029261,0.009473,-0.00402446,0.03817122,0.00555948,-0.06297154,0.08688612,0.02215301,-0.04929955,0.01290573,0.09460122,0.04428154,-0.07656314,0.00139295,-0.00739521,-0.00338856,0.02325424,0.01384565,0.06345697,0.0626967,-0.10530633,-0.2012786,0.01086674,-0.06317171,-0.02777932,0.00449529,-0.01348193,0.04731753,-0.03075186,0.06196972,0.04405302,0.07946553,-0.04020709,-0.00500449,0.04235601,-0.01558527,-0.01087438,-0.05515632,-0.05825964,0.00238265,0.00622928,0.01010743,0.03193938,-0.0425758,-0.03412659,0.07224188,-0.01075582,0.13318317,0.03779409,-0.0011475,-0.01384948,0.13426964,-0.0515041,-0.00797608,-0.01766014,0.02525354,0.05141394,-0.0685494,0.01311705,-0.01752108,0.0127396,-0.04227081,0.06830558,-0.00220293,-0.08053669,0.00159491,-0.05769496,-0.03190409,-0.00386018,0.04247107,0.04077684,0.02640554,-0.02368796,0.02775112,0.03400335,0.05223776,-0.03640304,-0.05838352,-0.01014996,-0.04143147,0.02472857,-0.00326478,-0.05152542,0.05621713,-0.01941206,0.05097298,0.01260144,-0.00038467,0.00111221,0.03274041,-0.00451285,-0.00954791,0.03657924,0.01954922,0.05083939,-0.01188031,0.00371263,0.02960398,-0.03036598,0.00944944,-0.02291134,0.01258112,-0.03385694,0.03484684,-0.01219319,0.04855447,-0.00905314,0.05064983,0.05035888,0.00558205,0.01326308,-0.00582619,0.02858715,-0.01290758,0.02255529,0.03846626,0.00469631,-0.25183195,0.0601731,-0.04335469,-0.00209163,-0.01240543,-0.02207244,0.00616527,-0.08040226,-0.03307764,-0.01421379,0.03918155,0.03333806,0.03067986,-0.03459431,0.00926372,0.00285096,0.01815579,-0.02263076,0.06281781,-0.05314741,0.10150073,0.05006124,0.27059868,0.02282841,0.00903755,0.04409555,0.0105417,-0.0142278,-0.00032968,0.03234892,0.02337648,-0.01582783,0.02712246,-0.01552484,-0.02784631,0.07484203,-0.00156673,0.04294851,-0.02071344,-0.00401656,-0.09785534,0.0091641,-0.05601711,-0.00203679,0.11642856,-0.02640024,-0.02820965,-0.03650587,0.06281594,0.0159693,-0.0746399,-0.05884246,-0.04422869,-0.01936024,-0.00993853,0.01201989,0.00764144,0.00765866,-0.03593341,0.01993831,0.04284266,0.05543202,0.08221493,0.00530111,-0.00290855],"last_embed":{"hash":"1uwn5dj","tokens":208}}},"text":null,"length":0,"last_read":{"hash":"1uwn5dj","at":1753423457664},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>","lines":[146,156],"size":658,"outlinks":[{"title":"The super lottery utility application has dozens of very useful functions to increase chances.","target":"https://saliu.com/images/ranges-generator.gif","line":8},{"title":"Best software tools and utilities in lotto are the creation of lottery grand-master Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":10}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10149988,-0.02465701,-0.0158431,-0.06769141,-0.02039248,0.03536361,0.00707403,-0.01150581,0.03392434,-0.00178454,-0.01577087,-0.02109025,0.05307952,0.01772554,-0.02896746,0.0164843,0.02245352,0.01886795,-0.10588332,-0.0166772,0.10317906,-0.02194161,-0.00857593,-0.11951754,0.04465132,-0.03065438,-0.01219518,-0.07091179,-0.00508964,-0.22107068,-0.01274952,0.05715111,0.00716333,-0.05049872,-0.1122953,-0.01148003,-0.03483105,0.01531088,-0.08028314,0.02717488,0.0179719,0.02149089,-0.02258936,-0.03683898,0.02893842,-0.04073934,0.01649928,-0.02363268,0.0284914,0.02548783,-0.01535787,0.01582285,-0.0152852,0.09279809,0.03851617,0.02303649,0.05156008,0.07543907,-0.03726706,0.03064555,0.06396485,0.09740608,-0.17521045,0.03959233,0.00574187,-0.02499934,0.01355518,-0.02709439,-0.03130273,0.01446895,0.01443776,0.03187164,-0.02409575,0.06823761,0.02261642,-0.03509674,-0.04486246,-0.0558353,-0.0314711,0.05890329,-0.03253069,-0.04383258,-0.01012574,-0.00820509,0.02420444,0.01751819,0.07442978,0.02201499,0.06106645,-0.08893617,0.0284273,0.0004387,0.04357412,0.05838019,0.022554,0.01846999,0.0173401,-0.03437478,-0.02386561,0.14868289,-0.01143607,0.00993313,-0.03256763,0.0391167,0.06255443,0.02166919,-0.02363125,0.0082291,-0.0493148,-0.00636025,0.0402543,0.00713953,0.08665466,-0.0872004,-0.05447524,-0.00949864,-0.02205985,-0.01781421,0.05337247,0.01530953,-0.03198441,0.04906301,-0.00264512,-0.00374145,0.00781763,-0.00326535,0.03718736,0.01870641,-0.0087054,0.06396762,0.01066762,0.00821658,-0.11083502,-0.0435789,-0.02480906,0.00821948,-0.01496304,-0.0231553,-0.00596626,0.04288295,0.01647901,-0.01609894,0.06129961,-0.11831482,0.04331505,0.06586425,-0.02231097,0.0474304,0.06273101,0.00583484,-0.02811469,-0.00584746,-0.00163273,-0.04379243,0.04916193,0.00408592,0.09900574,0.06508766,-0.04934932,0.04294942,-0.00428734,-0.02674664,-0.00702433,0.11633899,-0.0011893,-0.09352326,-0.03453377,0.02549475,-0.04392185,-0.0307203,-0.01021297,0.07468259,-0.01218974,0.02395813,0.07765558,-0.02383009,-0.05132233,-0.03750111,-0.01376679,0.01787263,-0.01388648,-0.03340747,-0.03981038,-0.01521239,-0.05505339,-0.09477405,0.0187513,-0.04852062,0.02414698,0.00917253,-0.03729851,-0.0162124,-0.03376026,-0.01888969,0.02213464,-0.0462922,-0.05582263,-0.0405675,0.05657693,0.00212815,-0.0750142,0.01822136,-0.01678396,-0.01116539,0.00300351,0.04084,-0.02749395,-0.06265599,0.0692196,0.03850015,-0.04166622,0.01446938,0.08584356,0.06403814,-0.09061457,0.02822602,-0.00709234,0.01317494,0.02353516,0.01649195,0.0605725,0.06348068,-0.11736615,-0.20852067,-0.02102235,-0.08122241,-0.047994,0.03092494,-0.02575806,0.04599174,-0.02645509,0.04484364,0.04627595,0.07420859,-0.05386493,0.00264913,0.03847848,0.00742893,-0.00500188,-0.06094883,-0.06388076,-0.00210162,0.01150749,0.00445175,0.03829455,-0.01322765,-0.03365503,0.07071184,-0.01409017,0.12434839,0.02341965,-0.00491798,-0.01002906,0.09984917,-0.0392703,-0.00509139,-0.04307882,0.02304273,0.04917021,-0.0690276,0.00305824,-0.01078181,0.00570296,-0.05206625,0.07110218,-0.006499,-0.08242127,0.00735487,-0.03151804,-0.03132214,-0.02225327,0.03033304,0.04247623,0.02148687,-0.02312112,0.02349037,0.03900403,0.07172074,-0.03725997,-0.07583805,0.00573291,-0.02110254,0.040383,-0.00503088,-0.04932793,0.07376327,-0.03187107,0.06221622,0.00026994,-0.01436224,-0.00221298,0.0071299,-0.00186845,-0.00630335,0.06997406,-0.00210644,0.05639265,-0.01756932,0.00999276,0.03828061,-0.03732484,0.00604037,-0.02579954,0.01886035,-0.04159905,0.03971435,0.00115249,0.05239034,0.00430558,0.04900281,0.04352396,-0.010285,0.02310909,0.00041581,0.0054764,-0.00978005,0.00381818,0.03636274,0.01971719,-0.24218468,0.04977267,-0.03666745,-0.01044495,-0.03250404,-0.02570566,-0.00733889,-0.07285663,-0.01393601,-0.00852829,0.06995751,0.03396812,0.03390199,-0.05014315,0.0208294,0.01523854,0.02478984,-0.02937156,0.0563559,-0.04287766,0.09352562,0.05390158,0.27363631,0.03257138,0.00724875,0.03643034,0.00860294,0.01060275,-0.00967883,0.03583026,0.01108231,-0.0069074,0.025849,-0.00846438,-0.02341624,0.08304733,-0.00113638,0.03443741,-0.02092988,0.00618093,-0.09955809,0.01694876,-0.06558116,-0.00038144,0.10642756,-0.01078771,-0.02674584,-0.02955549,0.06247759,0.02601746,-0.0677626,-0.07223918,-0.05118661,-0.03104766,0.00285141,0.00306655,-0.00640638,-0.00305805,-0.02956096,0.00567621,0.05039001,0.08916423,0.05504569,-0.00125399,-0.01564484],"last_embed":{"hash":"1ycj9ww","tokens":128}}},"text":null,"length":0,"last_read":{"hash":"1ycj9ww","at":1753423457723},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software##<u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>#{5}","lines":[153,156],"size":273,"outlinks":[{"title":"The super lottery utility application has dozens of very useful functions to increase chances.","target":"https://saliu.com/images/ranges-generator.gif","line":1},{"title":"Best software tools and utilities in lotto are the creation of lottery grand-master Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08996881,-0.03334608,-0.04002949,-0.05104255,-0.03822565,0.03478184,-0.01544875,-0.0186626,0.04298368,0.00911872,-0.01101284,-0.03011062,0.05950059,0.00480822,-0.00392661,0.01493139,0.01823073,0.01033658,-0.07702236,-0.02233417,0.08673229,-0.05165798,-0.03253369,-0.11608772,0.03759341,-0.00640622,-0.00627224,-0.0829088,-0.0067995,-0.25047633,-0.01551385,0.02728491,0.01474653,-0.08376312,-0.07842878,0.0063612,-0.04507888,0.02103996,-0.08087643,0.02335885,-0.00370661,0.01576132,-0.01107795,-0.01320287,0.04188036,-0.02407221,0.02021449,-0.00795403,0.03267909,0.01611477,-0.032272,0.01931654,-0.00880749,0.10059262,0.0528901,0.03461952,0.04677913,0.08034877,-0.03749929,0.04039478,0.05477398,0.08885458,-0.17480318,0.06704446,-0.0008792,0.00618542,0.02482361,-0.02909689,-0.03324211,0.0712567,0.0077912,0.04745006,-0.02655292,0.05997316,0.0040284,-0.02641253,-0.04212828,-0.07502473,-0.02564667,0.05776257,-0.0568984,-0.01810613,-0.03178471,-0.02711566,0.05075977,0.01872806,0.09507254,0.02221239,0.07237387,-0.065496,0.0065098,0.02026899,0.05789535,0.04801261,0.01964776,0.01658742,0.03454416,-0.00354046,0.02235214,0.12456982,0.03565503,0.03608365,-0.05856236,0.01555829,0.03921423,-0.02491025,-0.00724352,-0.00835035,-0.03903114,0.01545249,0.05802511,0.01455026,0.06959015,-0.05782495,-0.03606244,0.03664693,0.0087589,-0.01282758,0.06695605,0.02061745,-0.05541639,0.02219456,0.01534653,0.009291,-0.01537461,0.01057484,0.04908206,0.02290975,0.02729218,0.07336631,0.02055696,0.02227197,-0.13654733,-0.04673591,-0.04990808,0.02010444,0.00420251,-0.0093671,-0.02098476,0.00612595,-0.00901039,-0.09586208,0.06316512,-0.11435883,-0.01126211,0.09695407,-0.01994387,0.02169396,0.05357932,0.00992443,-0.04078979,0.00611168,-0.0154036,-0.04266046,0.02662153,0.03402798,0.10680244,0.05803542,-0.05883384,0.02797946,0.01234839,-0.01883056,-0.0224735,0.13145006,0.01129672,-0.09579841,-0.00005415,0.02657702,-0.04277357,-0.05354746,-0.03131694,0.05582428,-0.0316267,0.00682335,0.07738048,-0.01333479,-0.04074563,-0.04865277,0.00311109,0.02357958,0.00048267,-0.04564624,-0.00547156,-0.00043178,-0.05833367,-0.06252566,0.02490277,-0.03321097,0.02441397,0.01228004,-0.006547,-0.03143885,0.00110429,-0.00426466,-0.01124217,-0.02867001,-0.03189809,-0.0085638,0.0549635,-0.00956714,-0.02833004,0.03163476,-0.01994395,-0.0065433,0.00818802,0.07146357,-0.02785615,-0.05463678,0.08994168,0.01180779,-0.03568778,-0.01316939,0.06255493,0.03449504,-0.05952927,0.01288414,-0.00488646,0.0113306,0.02418687,0.02459385,0.04465873,0.0484095,-0.10068031,-0.18878736,-0.02439794,-0.06187896,0.01833678,0.01584871,-0.02871257,0.02671323,-0.02128914,0.03411263,0.10494915,0.05367975,-0.04750673,-0.01515054,0.05677891,-0.00896554,-0.01282365,-0.06803871,-0.07695576,-0.04381706,0.00514074,0.03822368,-0.00325409,-0.03534839,-0.0738415,0.05688639,-0.03793267,0.13167322,0.02082597,-0.04731338,0.0243579,0.07853508,-0.03542779,-0.01684512,-0.00336519,0.02931432,0.04878559,-0.0422088,-0.03848658,-0.03433681,0.01099047,-0.05964044,0.05860356,-0.02518181,-0.08821713,-0.03206156,-0.01903351,-0.04606972,-0.01240237,0.04445433,0.05113923,-0.00797922,0.01196923,0.03951311,0.02785203,0.0610345,0.00143808,-0.06004001,-0.023542,-0.02862675,0.02438016,-0.02666138,-0.06558008,0.05597256,-0.03639096,0.06470304,0.00828779,-0.02031956,-0.01282474,0.03801721,0.01233992,-0.02042658,0.0681536,0.01640259,0.02892766,-0.01020504,0.02109531,0.04352438,-0.01461508,0.0027303,-0.0364501,0.01277268,-0.03774187,0.05254029,0.02005346,0.0933795,-0.0157412,0.07607405,0.05396691,-0.01452357,0.02691787,-0.01560545,0.02150909,-0.00446822,0.01389062,0.0285254,0.04430004,-0.25800043,0.02779412,-0.06949934,0.01435437,-0.01836298,-0.01727085,0.00698044,-0.02880359,-0.00550556,-0.02716333,0.03464898,0.0328821,0.05638399,-0.03349688,-0.02520458,-0.0158662,0.01711143,-0.03680442,0.07751364,-0.0135068,0.08163039,0.03196992,0.25892481,0.03531922,0.00532088,0.02685084,0.00560028,0.01785578,-0.01359067,0.02831248,-0.01043482,-0.01243632,0.0433454,-0.01173252,-0.05384798,0.04911746,-0.02707668,0.04504185,-0.01132475,0.0062464,-0.11207659,-0.01137602,-0.03094909,-0.02180569,0.12521103,0.0088225,-0.04601642,-0.07293825,0.0442671,0.01260666,-0.06432456,-0.0518571,-0.05905388,-0.02909271,0.00024634,0.01478333,0.00668046,-0.01605083,-0.02052049,-0.01979817,0.03191219,0.03288485,0.04896371,0.01325706,0.01112882],"last_embed":{"hash":"113ut4z","tokens":422}}},"text":null,"length":0,"last_read":{"hash":"113ut4z","at":1753423457760},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)","lines":[157,185],"size":3429,"outlinks":[{"title":"Resources in Lottery Software, Strategies, Systems, Lotto Wheels","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Combinations Generator: Lottery, Powerball, Mega Millions, Euromillions, Two-In-One Lotto Games**_","target":"https://saliu.com/combinations.html","line":5},{"title":"_**Calculate combination lexicographic order of lotto data files**_","target":"https://saliu.com/combination.html","line":6},{"title":"**<u>Skip Systems</u>** _**for Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":7},{"title":"_**Lottery Filtering, Lotto Filters in Software**_","target":"https://saliu.com/filters.html","line":8},{"title":"_**Lottery Strategy, Systems Based on Number Frequency**_","target":"https://saliu.com/frequency-lottery.html","line":9},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":10},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":11},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":12},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":13},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":14},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":15},{"title":"_**The Best Lottery Strategies: Foundation, Application of the <u>Lotto Strategy</u> Concept**_","target":"https://software.saliu.com/lottery-strategy-lotto-strategies.html","line":16},{"title":"_**Play a Lotto Strategy, Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":17},{"title":"_**<u>Bright Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":18},{"title":"_**<u>Ultimate Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/ultimate-software-code.html","line":19},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":20},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":21},{"title":"**<u>lottery software, lotto programs</u>**","target":"https://saliu.com/infodown.html","line":22},{"title":"Lotto numbers with the best positional frequency reduce the odds and volume of tickets to play.","target":"https://saliu.com/HLINE.gif","line":24},{"title":"Forums","target":"https://forums.saliu.com/","line":26},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":26},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":26},{"title":"Contents","target":"https://saliu.com/content/index.html","line":26},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":26},{"title":"Home","target":"https://saliu.com/index.htm","line":26},{"title":"Search","target":"https://saliu.com/Search.htm","line":26},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":26},{"title":"Exiting site of the most excellent lottery software, strategies, systems to win lotto jackpots.","target":"https://saliu.com/HLINE.gif","line":28}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{20}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11509799,-0.04078527,-0.02441866,-0.02970357,-0.02620546,0.05732826,-0.002131,0.02322329,0.04728542,-0.00520933,-0.0293469,-0.01944266,0.03089825,0.01108165,-0.01659454,-0.00457772,0.0288865,0.02958422,-0.05372016,0.00327263,0.07100397,-0.03246066,-0.03106812,-0.09509906,0.03589975,-0.0246203,-0.02633588,-0.09010532,-0.02328231,-0.21749662,-0.0137058,0.03479306,0.01340952,-0.05644016,-0.09593122,0.00887336,-0.04118914,0.02799268,-0.04891386,0.05676245,0.013512,0.03046152,0.00720748,-0.00132397,0.03446352,-0.01787583,0.02900572,-0.01710357,0.04743273,0.01689636,-0.06641089,0.0240802,-0.02489362,0.06765983,0.05534783,0.03593527,0.04061149,0.08760004,-0.02519877,0.03254807,0.05313575,0.06669521,-0.1921248,0.08878496,0.00304137,-0.01521405,0.02750765,-0.02676176,-0.02313764,0.04634686,0.01668688,0.03632309,-0.01424117,0.06154392,0.02573968,-0.04827614,-0.0478069,-0.07218042,-0.01650029,0.02175613,-0.05838306,-0.0270188,-0.03053745,-0.04429528,0.04075022,0.01981506,0.09972192,0.01943458,0.06724156,-0.07737836,0.02020243,0.02897245,0.03580578,0.04792339,0.00145041,0.01239123,0.01456605,-0.00739135,0.01559953,0.13663563,0.02699299,0.02868223,-0.05869684,0.05140273,0.04228779,-0.02075447,-0.01680873,0.01559626,-0.04649093,0.01682662,0.07444142,0.02776238,0.07235713,-0.0572218,-0.07456296,0.00735954,0.00700727,0.00252433,0.06061737,0.00818683,-0.05128064,0.04883217,0.02612192,0.00960442,0.0130492,0.00361578,0.04807343,0.01865671,0.00162242,0.07784259,0.03763625,0.01180889,-0.12475404,-0.04458269,-0.06147536,0.02321179,-0.01846987,0.01007519,-0.03043942,0.03132837,0.00731836,-0.10484735,0.04735626,-0.10980009,-0.02108907,0.06909083,-0.04304548,0.03303749,0.05270219,0.01175113,-0.05118503,-0.00877996,-0.02552111,-0.04195428,0.01802304,0.02167627,0.09850566,0.07697823,-0.0575809,0.03171554,-0.017396,-0.04662785,0.00694418,0.12502547,-0.00204968,-0.07814236,-0.04466277,0.01890674,-0.03412569,-0.06021748,-0.03691805,0.02676733,-0.03044832,0.01464339,0.06929701,0.00470571,-0.0421014,-0.02753638,-0.00576359,0.02899854,-0.00429177,-0.05563959,-0.00672185,-0.0151778,-0.05051912,-0.05291459,0.02431013,-0.0423915,0.02474735,0.00349469,0.01418912,-0.05577941,-0.03962409,-0.01183091,-0.03066806,-0.03528389,-0.05401042,-0.01852505,0.07086525,0.02417366,-0.05553207,0.01106505,-0.04674095,-0.00159027,-0.00029438,0.0787541,-0.01165476,-0.06628133,0.08134598,0.0105495,-0.0234821,0.00869682,0.0694611,0.01241516,-0.05234384,0.02349805,-0.00144537,0.01015795,0.01725608,0.00388181,0.05865257,0.05848838,-0.11519924,-0.20525005,-0.01261876,-0.06901064,-0.02343446,0.02873431,-0.02509338,0.04175323,-0.01480943,0.08263197,0.0845167,0.04863343,-0.07704489,-0.01478123,0.0262931,0.00663967,-0.03613612,-0.07557992,-0.0729269,-0.0293224,0.00466066,0.04132776,0.00621029,-0.07211068,-0.05577939,0.07197957,-0.03185397,0.11650349,0.02239949,-0.00683688,0.01184014,0.09081248,-0.06124828,-0.02455258,-0.01068595,0.02524811,0.04010053,-0.04465977,-0.01612782,-0.05146527,0.01438011,-0.05626465,0.06970759,-0.01794809,-0.08921997,-0.04956071,-0.020575,-0.03970578,-0.02649687,0.02910352,0.06614404,0.02804279,0.02566094,0.0375408,0.02936845,0.06812878,-0.00155689,-0.05182338,-0.03732949,-0.06031957,0.03984363,-0.04370565,-0.05125872,0.07638819,-0.02963616,0.04463093,0.02457165,-0.00622315,-0.01974842,0.02536462,0.02579318,-0.00972112,0.05983503,0.00871912,0.03418352,0.03245867,-0.00432278,0.0308949,-0.01798576,0.01631335,-0.00377761,0.0071998,-0.04512971,0.06641732,0.01603704,0.07559618,0.00072221,0.08111984,0.04801445,-0.03590348,0.03014518,0.02634982,0.02169525,-0.0129719,0.00675366,0.04442129,0.02748561,-0.24194829,0.02246763,-0.06047329,0.01398979,-0.04664433,-0.00754478,0.01745991,-0.01934381,-0.02441807,-0.01803637,0.04931499,0.03704591,0.017055,-0.01444619,-0.02278834,-0.01061721,-0.00600326,-0.03908172,0.0641126,-0.016017,0.06017984,0.03491231,0.25642845,0.0311458,0.00308152,0.02547549,0.04027549,0.02705015,-0.01836299,0.01779451,-0.01272851,-0.01161515,0.03888375,0.00918175,-0.05934219,0.04501223,-0.02993438,0.017944,-0.01273482,0.0069255,-0.06788097,-0.00096751,-0.04239345,-0.00287486,0.1466675,0.00520074,-0.03212743,-0.06587684,0.08087704,0.01918921,-0.04172575,-0.06699251,-0.04651849,-0.01345391,-0.02968559,-0.00352952,0.01877228,-0.00580511,0.00457702,-0.01454101,0.03661704,0.03887843,0.0489483,-0.00990322,0.0136984],"last_embed":{"hash":"ifgbcy","tokens":299}}},"text":null,"length":0,"last_read":{"hash":"ifgbcy","at":1753423457925},"key":"notes/saliu/Favorite Lottery Numbers in Fixed Positions Software.md#Favorite Lottery Numbers in Fixed Positions: Software#[Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)#{20}","lines":[180,185],"size":649,"outlinks":[{"title":"Lotto numbers with the best positional frequency reduce the odds and volume of tickets to play.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Exiting site of the most excellent lottery software, strategies, systems to win lotto jackpots.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
