
"smart_sources:筆記/202507011014.md": {"path":"筆記/202507011014.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"pmbrnv","at":1751347016689},"class_name":"SmartSource","last_import":{"mtime":1751336079854,"size":6214,"at":1751347016690,"hash":"pmbrnv"},"blocks":{"#":[1,4],"###程式碼修改":[5,74],"###程式碼修改#{1}":[7,74],"###修改說明":[75,97],"###修改說明#{1}":[77,82],"###修改說明#{2}":[83,86],"###修改說明#{3}":[87,90],"###修改說明#{4}":[91,97]},"outlinks":[{"title":"analyze_triggered_pattern_performance_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl \"f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl\"","line":3},{"title":"analyze_triggered_pattern_performance_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl \"f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl\"","line":7},{"title":"SCRIPT_FUTURE_DIRECTIONS.md","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\SCRIPT_FUTURE_DIRECTIONS.md \"f:\\work\\JuliaProject\\StandAlone\\SCRIPT_FUTURE_DIRECTIONS.md\"","line":89},{"title":"投資效益評估與參數紀錄.md","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\投資效益評估與參數紀錄.md \"f:\\work\\JuliaProject\\StandAlone\\投資效益評估與參數紀錄.md\"","line":91}]},