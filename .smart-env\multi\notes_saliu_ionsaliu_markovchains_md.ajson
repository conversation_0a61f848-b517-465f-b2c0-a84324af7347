
"smart_sources:notes/saliu/ionsaliu_markovchains.md": {"path":"notes/saliu/ionsaliu_markovchains.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"v9uu8d","at":1753230880679},"class_name":"SmartSource","last_import":{"mtime":1735897996000,"size":88644,"at":1753230880690,"hash":"v9uu8d"},"blocks":{"##實現逆向策略的相關功能。首先讓我們查看原始文件的核心概念，然後實現相應的函數：":[1,376],"##實現逆向策略的相關功能。首先讓我們查看原始文件的核心概念，然後實現相應的函數：#{1}":[3,376],"#---frontmatter---":[207,375],"##在`ionsaliu_markovchains.jl`中實現馬可夫鏈相關的分析功能：":[377,2637],"##在`ionsaliu_markovchains.jl`中實現馬可夫鏈相關的分析功能：#{1}":[379,2637],"#載入歷史開獎資料":[2638,2644],"#載入歷史開獎資料#{1}":[2639,2644],"#計算轉移矩陣":[2645,2647],"#計算轉移矩陣#{1}":[2646,2647],"#預測下一期可能出現的號碼":[2648,2655],"#預測下一期可能出現的號碼#{1}":[2649,2655],"#計算號碼序列的馬可夫鏈特徵":[2656,2666],"#計算號碼序列的馬可夫鏈特徵#{1}":[2657,2666],"#進行多步預測分析":[2667,2678],"#進行多步預測分析#{1}":[2668,2678],"#分析號碼間的條件依賴關係":[2679,2693],"#分析號碼間的條件依賴關係#{1}":[2680,2693],"#分析馬可夫鏈的穩定性":[2694,2699],"#分析馬可夫鏈的穩定性#{1}":[2695,2699],"#分析穩態分布":[2700,2713],"#分析穩態分布#{1}":[2701,2713],"#完整的分析流程":[2714,2715],"#完整的分析流程#{1}":[2715,2715],"#1. 載入資料":[2716,2718],"#1. 載入資料#{1}":[2717,2718],"#2. 基本馬可夫鏈預測":[2719,2723],"#2. 基本馬可夫鏈預測#{1}":[2720,2723],"#3. 分析跟隨者模式":[2724,2730],"#3. 分析跟隨者模式#{1}":[2725,2730],"#4. 分析配對強度":[2731,2746],"#4. 分析配對強度#{1}":[2732,2746],"#進階分析流程":[2747,2748],"#進階分析流程#{1}":[2748,2748],"#1. 載入資料[2]":[2749,2751],"#1. 載入資料[2]#{1}":[2750,2751],"#2. 分析馬可夫鏈特性":[2752,2754],"#2. 分析馬可夫鏈特性#{1}":[2753,2754],"#3. 分析回歸時間":[2755,2762],"#3. 分析回歸時間#{1}":[2756,2762],"#4. 多步預測":[2763,2814],"#4. 多步預測#{1}":[2764,2814]},"outlinks":[{"title":"簡介","target":"#簡介","line":2596},{"title":"基本概念","target":"#基本概念","line":2597},{"title":"系統安裝","target":"#系統安裝","line":2598},{"title":"基本使用方法","target":"#基本使用方法","line":2599},{"title":"進階分析","target":"#進階分析","line":2600},{"title":"實戰範例","target":"#實戰範例","line":2601}]},