"smart_sources:We can determine the pattern window size from the keys in filtered_pattern_win_info..md": {"path":"We can determine the pattern window size from the keys in filtered_pattern_win_info..md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0692855,-0.01181869,0.03222135,-0.02573117,-0.00235406,0.06661426,0.02230389,0.01490309,0.06193511,-0.01905222,-0.01355486,-0.0101498,0.06489655,0.02338619,-0.01447648,-0.02543325,0.03699095,0.0117486,-0.08948005,-0.03283221,0.06692238,-0.027549,-0.04856097,-0.10811983,0.03571116,0.09066841,-0.01962891,-0.03282204,-0.0020666,-0.20896101,-0.00838282,0.02590786,0.00485024,-0.01474792,-0.00040654,-0.04360791,-0.07292149,0.0829246,-0.03501255,0.01404495,0.02195126,0.04570445,0.01631684,-0.00508481,-0.0233557,-0.04726542,-0.08732394,0.00394023,-0.03873676,-0.00570204,-0.10891772,0.01196272,0.05751754,0.01406419,0.00136323,0.02643014,0.02930727,0.06699192,0.02798225,0.01994327,-0.03613802,0.00810699,-0.17906743,0.09523246,-0.0155066,0.00299665,0.0108597,-0.0397628,0.07027664,0.11131348,-0.02005179,0.02813064,-0.04874682,0.04669797,-0.01466245,-0.04243446,-0.03123849,-0.0126918,-0.0285819,0.04004512,-0.04090125,-0.00795753,0.02338381,-0.01434246,-0.03868559,-0.01697431,0.05612555,0.0406878,0.08404238,-0.01676024,-0.02338208,-0.05045198,-0.02182244,0.04548274,-0.01942411,-0.00256989,0.0010043,0.07742346,-0.008681,0.15066126,-0.03363417,0.04367473,-0.04450809,-0.02956128,0.0426877,-0.01805696,-0.0017511,-0.01017847,-0.03683034,-0.02751633,0.01173597,-0.03506301,0.06900054,-0.0494152,-0.01456025,0.03139957,0.01759496,0.000963,0.04393963,0.01876739,-0.01733807,-0.02531683,0.0598684,-0.04382822,0.04847704,0.06303039,-0.05951314,0.01215105,-0.03463641,0.05075404,0.04588669,-0.00409412,-0.04724661,0.05377649,-0.02046656,-0.00938537,0.0163817,0.03687977,-0.03587643,-0.0713098,-0.03123412,-0.00861907,0.08151893,-0.02946044,-0.02037663,0.13481663,-0.08359879,-0.00766956,-0.03629271,-0.11393558,0.01846615,0.08853595,-0.04559852,-0.12809451,0.00260617,0.03400779,0.05998028,0.05659678,-0.0695473,0.02546935,-0.01140213,0.02158232,0.02325433,0.07495131,-0.00035122,-0.04810711,-0.04714951,0.06090783,-0.01044304,0.00527417,0.02572249,0.04150662,-0.05727977,0.02713079,0.06068298,0.00465344,-0.07022804,0.00360701,-0.01126224,-0.00125931,0.00127863,-0.03007688,-0.07401717,0.02289835,0.03846743,-0.06099882,0.0178696,-0.02092227,0.00348078,0.01377186,-0.08078452,-0.02705324,0.03892874,0.00096993,-0.00533653,-0.02919781,-0.03257247,-0.03177214,0.03107666,-0.02966624,0.09570354,-0.00787979,-0.06263471,0.0098777,-0.04770619,0.01857513,0.01333756,-0.05427889,0.00145089,0.07900618,-0.05995632,-0.01206342,0.00758079,-0.03658948,-0.01942218,-0.02444992,0.02209645,0.03699321,-0.05191211,0.02361259,0.00137352,-0.06178975,-0.12174331,-0.21948546,-0.01779095,-0.00117285,0.04950346,0.01517354,-0.06122363,0.07236321,0.00984772,0.02083512,0.05226252,0.06951,0.04657788,-0.08995995,0.00024381,-0.03427569,-0.00872305,0.02486807,-0.00772319,-0.05247782,-0.02646294,0.01251663,0.01345099,-0.04677758,-0.03427593,0.05263563,-0.02015324,0.15279694,0.01110896,0.04544696,-0.00544357,0.03983168,0.00139774,-0.02559694,-0.04392342,0.04208957,0.10990077,-0.03680608,0.08099595,-0.02134651,-0.04856022,0.00602071,-0.04265388,-0.02023487,-0.07833536,0.00951062,0.01862006,-0.02150475,-0.02508002,-0.0366919,0.00608517,0.08660557,0.02401669,0.02872667,0.01225129,0.01093919,-0.00376374,-0.01461968,-0.05432886,-0.01481278,-0.02487511,-0.00208496,0.02241255,0.00100505,-0.03698141,0.0332601,-0.00089306,0.01523244,0.00858978,0.01362731,-0.00870873,-0.01580366,0.08288376,-0.02013505,-0.00572066,0.04838475,0.00376105,-0.0113088,-0.00544241,-0.01281045,-0.03535235,0.04343524,-0.04936212,0.02141451,0.00296523,0.0700058,0.04340776,0.00261057,0.04646621,0.06575166,-0.03891376,0.02178026,0.04929326,-0.03825646,-0.02703686,0.01296957,-0.03109792,-0.24612257,0.06099676,-0.00820327,0.06239882,-0.05800087,-0.05480781,0.08048055,-0.03921905,0.00239875,0.01574176,-0.09322464,0.00706197,-0.00828799,-0.05975557,-0.00185246,0.01347861,0.0326181,-0.00783101,0.04750848,0.05231388,0.04105486,0.01163213,0.25175753,-0.03869609,0.08097561,-0.02247394,0.03879773,-0.0796842,0.03356655,0.00349903,0.05592288,-0.03370112,0.0947351,-0.07029861,0.02511875,0.00569373,-0.00702334,0.03842237,0.00190108,0.03667137,0.04283461,0.01842776,-0.0372012,0.0078747,0.07627511,-0.05262509,0.02190367,-0.022533,-0.03962009,0.01906908,-0.04892093,0.05006574,0.03725212,-0.00127456,0.0260771,0.02761267,0.00619746,-0.06044706,-0.00280715,0.06481818,0.03456241,-0.06927945,0.01818442,0.01857183,0.04481277],"last_embed":{"hash":"f71348e49019275b60a8228e9e602541e669b13eaf23c395bff324bf4826f596","tokens":384}}},"last_read":{"hash":"f71348e49019275b60a8228e9e602541e669b13eaf23c395bff324bf4826f596","at":1745995160182},"class_name":"SmartSource2","outlinks":[],"blocks":{"#":[2,42],"####":[43,57],"#####{1}":[45,47],"#####{2}":[48,48],"#####{3}":[49,49],"#####{4}":[50,50],"#####{5}":[51,52],"#####{6}":[53,57]},"last_import":{"mtime":1743043397324,"size":2577,"at":1743044225505,"hash":"f71348e49019275b60a8228e9e602541e669b13eaf23c395bff324bf4826f596"},"key":"We can determine the pattern window size from the keys in filtered_pattern_win_info..md"},