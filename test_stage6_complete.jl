#!/usr/bin/env julia

# 階段 6 完整功能測試和演示
println("🎯 開始階段 6 測試與優化系統完整演示...")

try
    using Dates
    using Statistics
    
    # 載入所有階段 6 模組
    include("src/WonderGridEngine.jl")
    include("src/BenchmarkSystem.jl")
    include("src/TestCoverageAnalyzer.jl")
    include("src/AdvancedPerformanceAnalyzer.jl")
    include("src/AutomatedTestSuite.jl")
    include("src/SystemOptimizationAdvisor.jl")
    include("src/ContinuousIntegrationSystem.jl")
    
    using .WonderGridEngine
    using .BenchmarkSystem
    using .TestCoverageAnalyzer
    using .AdvancedPerformanceAnalyzer
    using .AutomatedTestSuite
    using .SystemOptimizationAdvisor
    using .ContinuousIntegrationSystem
    
    println("✅ 所有階段 6 模組載入成功")
    
    # ==================== 1. 基準測試系統演示 ====================
    println("\n" * "="^60)
    println("🏁 1. 基準測試系統演示")
    println("="^60)
    
    # 創建基準測試配置
    benchmark_config = BenchmarkSystem.BenchmarkConfig(
        name = "Wonder Grid 性能基準測試",
        iterations = 8,
        warmup_iterations = 2,
        data_sizes = [10, 25, 50, 100],
        strategies = [:ffg, :skip, :frequency]
    )
    
    # 創建並運行基準測試
    benchmark_suite = BenchmarkSystem.create_benchmark_suite(benchmark_config)
    benchmark_results = BenchmarkSystem.run_benchmark(benchmark_suite, "wonder_grid_performance")
    
    # 算法性能比較
    comparison_results = BenchmarkSystem.compare_algorithms(benchmark_suite, :ffg, [:skip, :frequency])
    
    # 生成基準測試報告
    BenchmarkSystem.generate_benchmark_report(benchmark_suite, "benchmark_report.md")
    
    println("✅ 基準測試系統演示完成")
    
    # ==================== 2. 測試覆蓋率分析演示 ====================
    println("\n" * "="^60)
    println("🔍 2. 測試覆蓋率分析演示")
    println("="^60)
    
    # 創建覆蓋率分析配置
    coverage_config = TestCoverageAnalyzer.CoverageConfig(
        source_directories = ["src"],
        test_directories = ["test"],
        minimum_coverage_threshold = 75.0,
        report_format = :html
    )
    
    # 運行覆蓋率分析
    coverage_result = TestCoverageAnalyzer.analyze_test_coverage(coverage_config)
    
    # 生成覆蓋率報告
    coverage_report = TestCoverageAnalyzer.generate_coverage_report(coverage_config, coverage_result)
    
    # 識別未測試代碼
    untested_code = TestCoverageAnalyzer.identify_untested_code(coverage_result)
    
    println("✅ 測試覆蓋率分析演示完成")
    
    # ==================== 3. 高級性能分析演示 ====================
    println("\n" * "="^60)
    println("⚡ 3. 高級性能分析演示")
    println("="^60)
    
    # 創建性能分析配置
    profile_config = AdvancedPerformanceAnalyzer.ProfileConfig(
        enable_memory_tracking = true,
        enable_cpu_profiling = true,
        sampling_interval = 0.01,
        memory_threshold_mb = 512.0
    )
    
    # 創建性能分析器
    profiler = AdvancedPerformanceAnalyzer.create_performance_profiler(profile_config)
    
    # 創建測試函數
    function test_wonder_grid_performance()
        # 創建測試數據
        drawings = [
            Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
            Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
            Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47]),
            Drawing(4, :Lotto6_49, Date(2024, 1, 4), [4, 10, 17, 30, 37, 48]),
            Drawing(5, :Lotto6_49, Date(2024, 1, 5), [1, 11, 18, 31, 38, 49])
        ]
        
        config = WonderGridConfig(combination_limit = 10)
        return execute_wonder_grid_strategy(drawings, config)
    end
    
    # 運行性能分析
    analysis_result = AdvancedPerformanceAnalyzer.profile_function_execution(
        profiler, test_wonder_grid_performance
    )
    
    # 生成優化建議
    optimization_suggestions = AdvancedPerformanceAnalyzer.generate_optimization_suggestions(analysis_result)
    
    # 導出性能報告
    AdvancedPerformanceAnalyzer.export_profiling_data(analysis_result, :html, "performance_analysis.html")
    
    println("✅ 高級性能分析演示完成")
    
    # ==================== 4. 自動化測試套件演示 ====================
    println("\n" * "="^60)
    println("🧪 4. 自動化測試套件演示")
    println("="^60)
    
    # 運行完整的自動化測試套件
    test_runner = AutomatedTestSuite.run_all_tests()
    
    # 生成測試報告
    AutomatedTestSuite.generate_test_report(test_runner, "automated_test_report.html")
    
    # 模擬回歸測試（使用當前結果作為基準）
    baseline_results = copy(test_runner.results)
    regression_report = AutomatedTestSuite.run_regression_tests(test_runner, baseline_results)
    
    println("✅ 自動化測試套件演示完成")
    
    # ==================== 5. 系統優化建議演示 ====================
    println("\n" * "="^60)
    println("💡 5. 系統優化建議演示")
    println("="^60)
    
    # 創建優化配置
    optimization_config = SystemOptimizationAdvisor.OptimizationConfig(
        performance_threshold = 1.0,
        memory_threshold_mb = 512.0,
        analysis_depth = :comprehensive,
        focus_areas = [:performance, :memory, :scalability, :maintainability]
    )
    
    # 創建優化顧問
    advisor = SystemOptimizationAdvisor.create_optimization_advisor(optimization_config)
    
    # 分析系統性能
    system_analysis = SystemOptimizationAdvisor.analyze_system_performance(optimization_config)
    
    # 生成優化建議
    optimization_suggestions = SystemOptimizationAdvisor.generate_optimization_suggestions(
        system_analysis, optimization_config
    )
    
    # 優先排序建議
    prioritized_suggestions = SystemOptimizationAdvisor.prioritize_optimizations(optimization_suggestions)
    
    # 導出優化報告
    optimization_report = SystemOptimizationAdvisor.export_optimization_report(
        optimization_config, system_analysis, prioritized_suggestions, "optimization_report.html"
    )
    
    println("✅ 系統優化建議演示完成")
    
    # ==================== 6. 持續集成系統演示 ====================
    println("\n" * "="^60)
    println("🔄 6. 持續集成系統演示")
    println("="^60)
    
    # 創建CI配置
    ci_config = ContinuousIntegrationSystem.CIConfig(
        project_name = "SaliuSystem Wonder Grid",
        test_environments = [:unit, :integration, :performance],
        quality_gates = Dict(
            "test_coverage" => 70.0,
            "performance_threshold" => 2.0,
            "memory_threshold" => 1024.0
        )
    )
    
    # 創建CI管道
    ci_pipeline = ContinuousIntegrationSystem.create_ci_pipeline(ci_config)
    
    # 運行CI管道
    ci_result = ContinuousIntegrationSystem.run_ci_pipeline(
        ci_pipeline,
        "abc123def456",  # 模擬提交哈希
        "main",          # 分支
        "push"           # 觸發事件
    )
    
    println("✅ 持續集成系統演示完成")
    
    # ==================== 7. 綜合報告生成 ====================
    println("\n" * "="^60)
    println("📋 7. 生成綜合報告")
    println("="^60)
    
    # 生成綜合報告
    generate_comprehensive_report(
        benchmark_results,
        coverage_result,
        analysis_result,
        test_runner,
        optimization_report,
        ci_result
    )
    
    println("✅ 綜合報告生成完成")
    
    # ==================== 8. 最終摘要 ====================
    println("\n" * "="^60)
    println("🎉 階段 6 測試與優化系統演示完成")
    println("="^60)
    
    println("📊 演示摘要:")
    println("  ✅ 基準測試系統 - 性能基準建立和比較")
    println("  ✅ 測試覆蓋率分析 - 代碼質量監控")
    println("  ✅ 高級性能分析 - 瓶頸檢測和優化建議")
    println("  ✅ 自動化測試套件 - 全面的測試自動化")
    println("  ✅ 系統優化建議 - 智能優化指導")
    println("  ✅ 持續集成系統 - 完整的CI/CD流程")
    
    println("\n📁 生成的報告文件:")
    println("  - benchmark_report.md")
    println("  - coverage_report.html")
    println("  - performance_analysis.html")
    println("  - automated_test_report.html")
    println("  - optimization_report.html")
    println("  - comprehensive_report.html")
    
    println("\n🚀 階段 6 所有功能已成功實現並測試完成！")
    
catch e
    println("❌ 演示過程中發生錯誤: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

"""
生成綜合報告
"""
function generate_comprehensive_report(
    benchmark_results,
    coverage_result,
    analysis_result,
    test_runner,
    optimization_report,
    ci_result
)
    
    println("📋 生成綜合報告...")
    
    open("comprehensive_report.html", "w") do io
        println(io, "<!DOCTYPE html>")
        println(io, "<html><head><title>階段 6 綜合報告</title>")
        println(io, "<style>")
        println(io, "body { font-family: Arial, sans-serif; margin: 20px; }")
        println(io, "h1, h2 { color: #333; }")
        println(io, ".section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }")
        println(io, ".metric { background-color: #f8f9fa; padding: 10px; margin: 5px 0; }")
        println(io, "</style></head><body>")
        
        println(io, "<h1>階段 6 測試與優化系統綜合報告</h1>")
        println(io, "<p><strong>生成時間:</strong> $(now())</p>")
        
        # 基準測試摘要
        println(io, "<div class=\"section\">")
        println(io, "<h2>🏁 基準測試摘要</h2>")
        println(io, "<div class=\"metric\">測試案例數量: $(length(benchmark_results))</div>")
        if !isempty(benchmark_results)
            avg_time = mean([r.mean_time for r in benchmark_results])
            println(io, "<div class=\"metric\">平均執行時間: $(round(avg_time, digits=3)) 秒</div>")
        end
        println(io, "</div>")
        
        # 測試覆蓋率摘要
        println(io, "<div class=\"section\">")
        println(io, "<h2>🔍 測試覆蓋率摘要</h2>")
        println(io, "<div class=\"metric\">總覆蓋率: $(round(coverage_result.overall_coverage, digits=1))%</div>")
        println(io, "<div class=\"metric\">未測試函數: $(length(coverage_result.untested_functions)) 個</div>")
        println(io, "</div>")
        
        # 性能分析摘要
        println(io, "<div class=\"section\">")
        println(io, "<h2>⚡ 性能分析摘要</h2>")
        println(io, "<div class=\"metric\">執行時間: $(round(analysis_result.execution_time, digits=3)) 秒</div>")
        println(io, "<div class=\"metric\">檢測瓶頸: $(length(analysis_result.bottlenecks)) 個</div>")
        println(io, "</div>")
        
        # 測試結果摘要
        println(io, "<div class=\"section\">")
        println(io, "<h2>🧪 自動化測試摘要</h2>")
        total_tests = length(test_runner.results)
        passed_tests = count(r -> r.status == :passed, test_runner.results)
        println(io, "<div class=\"metric\">總測試數: $total_tests</div>")
        println(io, "<div class=\"metric\">通過測試: $passed_tests</div>")
        if total_tests > 0
            success_rate = (passed_tests / total_tests) * 100
            println(io, "<div class=\"metric\">成功率: $(round(success_rate, digits=1))%</div>")
        end
        println(io, "</div>")
        
        # CI結果摘要
        println(io, "<div class=\"section\">")
        println(io, "<h2>🔄 持續集成摘要</h2>")
        println(io, "<div class=\"metric\">管道狀態: $(ci_result.overall_status)</div>")
        println(io, "<div class=\"metric\">執行階段: $(length(ci_result.stage_results)) 個</div>")
        passed_stages = count(r -> r.status == :passed, ci_result.stage_results)
        println(io, "<div class=\"metric\">通過階段: $passed_stages</div>")
        println(io, "</div>")
        
        println(io, "</body></html>")
    end
    
    println("✓ 綜合報告已保存到: comprehensive_report.html")
end

println("階段 6 測試腳本載入完成")
