"smart_sources:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md": {"path":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07317369,-0.02767125,-0.01687632,0.00240583,-0.05176057,0.07791829,0.04705007,0.00884509,0.04107445,-0.04435426,0.01463644,-0.02076269,0.02027876,0.00763119,-0.03735354,-0.03782508,-0.01129489,0.00488138,-0.04151168,0.02759605,0.09105128,-0.03451191,-0.02547707,-0.07609723,0.08281673,0.06505861,0.01075838,-0.0470144,-0.01337677,-0.20268799,0.00367773,0.01847817,-0.00551911,-0.03948475,-0.0644559,-0.01705463,-0.04019418,0.11379177,-0.03158724,0.07209249,0.02161202,-0.00394732,-0.00451146,0.00963747,0.02454315,-0.00362515,-0.00087008,0.01371953,0.02191648,-0.00302162,-0.08983462,0.01267661,-0.01206812,-0.02055966,0.10153571,0.02762838,0.07952903,0.09779731,0.04838138,0.02194357,0.04267186,0.04617384,-0.21208906,0.06579364,-0.01675516,0.0433906,-0.00229599,0.02803117,-0.03109044,0.04932759,0.05682953,0.02729579,-0.02189879,0.04732764,0.02339037,-0.03819593,-0.03742074,-0.04826888,-0.02219186,0.03488242,-0.01556773,-0.00546661,-0.05257373,-0.01262112,-0.05019208,0.05948427,0.0198656,0.04882145,0.06796492,-0.10020147,0.03301242,0.00616608,0.00161133,0.03571735,0.01635196,-0.00120819,0.01363359,-0.0085213,-0.03256147,0.1010609,-0.01818228,0.01131105,-0.00619567,0.02795169,0.06147935,-0.03136787,-0.00436732,-0.04741136,-0.02052439,0.04630949,-0.00050739,0.01701432,0.08174173,-0.06446239,-0.00281071,0.01442593,0.00246757,-0.00607768,0.01677493,0.01214751,-0.00713574,0.06725491,0.00163993,-0.02747113,-0.04252961,-0.0102452,0.02800229,0.08936534,0.03992939,-0.01025174,0.02014997,0.0217921,-0.1285118,-0.01850067,-0.02369015,-0.04085244,0.01524344,0.01365595,0.01274943,-0.00123405,-0.01562838,-0.02564617,0.04580884,-0.10721074,-0.06539833,0.04447082,-0.00147011,-0.00896347,0.02051124,0.04687504,0.00172524,-0.01406128,-0.04095222,-0.10477529,-0.03237533,-0.02884634,0.08782206,0.08405386,-0.02242798,-0.01177403,-0.06218779,-0.07424188,0.00334792,0.07136619,0.0273059,-0.07734302,-0.03405543,0.00542238,-0.0271055,-0.12889004,-0.0173114,-0.04203362,-0.06968204,0.01092384,0.08905668,0.00031217,-0.04209881,-0.08397999,-0.04687897,0.03940127,0.01623706,-0.04715948,-0.03403312,-0.03598493,-0.05621742,-0.0617906,-0.00374873,-0.06662115,0.06418046,0.02337168,-0.0413927,0.02411309,-0.0474702,-0.00324583,-0.03141568,-0.02575742,-0.04877712,-0.04477423,0.05747154,-0.03141798,-0.05267299,0.00763695,0.00150811,0.03114897,-0.05094652,0.01862964,0.03816342,-0.04016292,0.10166527,0.03222734,-0.03545414,-0.0270029,0.01457954,0.04313367,-0.04123615,0.03012586,0.02439773,0.01022037,-0.00590522,0.05311568,-0.01245168,0.02992959,-0.06529828,-0.15510488,-0.01267523,-0.06586362,-0.02551398,-0.00939864,0.00734752,0.01368375,-0.0115074,0.03124058,0.00122606,0.10013653,-0.05263385,0.01506034,0.05329596,0.01954267,-0.00887134,-0.09595335,-0.0218519,-0.01558695,0.04593302,-0.01664314,0.01512027,-0.04393537,-0.03525999,-0.00790334,0.0166069,0.12616597,0.06916324,0.02519362,-0.00023836,0.06717403,0.04044558,-0.03538947,-0.09492716,0.00187767,-0.02060869,0.00022561,-0.02711784,-0.06281375,-0.01292777,-0.07305256,0.02216501,-0.01643329,-0.06550106,-0.05688431,0.00086207,0.00996209,0.06650981,-0.00745552,0.0619518,0.07206237,0.04500534,0.04627365,0.01768904,0.05530267,-0.03518757,-0.07706275,0.00003644,-0.01049858,0.05095178,-0.02465567,-0.00353686,0.0470704,-0.00282712,0.04790452,0.01434133,-0.00706457,-0.03063719,0.02515086,0.0003301,-0.00377273,0.06782121,0.00081174,-0.01545233,-0.01579954,-0.00104893,0.04702844,-0.07430454,0.04263837,0.00672177,0.01749886,-0.07148465,0.0379005,0.06260888,0.05358985,0.01493712,0.01223496,0.02453941,0.00703687,-0.01900454,-0.01695034,0.0665378,-0.042511,0.00869705,0.0240449,0.00146246,-0.28704947,0.02369682,-0.00702278,0.03271686,-0.0094591,0.02048401,0.04743303,0.01756163,-0.02416799,-0.01650611,0.05989851,0.02415566,-0.00759279,-0.02205219,-0.02655285,-0.03566441,0.00032931,-0.03607072,0.08060532,0.00752198,0.08165219,0.02157989,0.26205012,-0.02833716,0.02232729,-0.01420664,0.03470546,-0.00614193,0.00263485,0.069362,-0.01850485,0.02105913,0.06344025,0.01474862,-0.02696016,-0.00968639,0.00261602,0.0392573,-0.02963089,0.00099989,-0.07259453,-0.01601359,0.0240403,0.05381909,0.12177204,0.00565592,-0.01011497,-0.10162681,0.01723101,0.06671757,-0.10306655,-0.0729131,-0.06095851,-0.00500454,-0.02923472,0.05085156,0.06203524,-0.00877565,0.02906399,0.0191771,0.03241524,0.02531714,0.07541353,0.03065321,0.02327618],"last_embed":{"hash":"1gl6khg","tokens":472}}},"last_read":{"hash":"1gl6khg","at":1753423595624},"class_name":"SmartSource","last_import":{"mtime":1753363608634,"size":22021,"at":1753423416500,"hash":"1gl6khg"},"blocks":{"#---frontmatter---":[1,6],"#Playing Lottery Strategies, Lotto Strategy Means Start":[8,265],"#Playing Lottery Strategies, Lotto Strategy Means Start#{1}":[10,17],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>The <i>Start</i> Is the Hardest Part in <i>Lottery Strategies</i></u>":[18,20],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>The <i>Start</i> Is the Hardest Part in <i>Lottery Strategies</i></u>#{1}":[19,20],"#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_":[21,32],"#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_#{1}":[23,24],"#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_#I. [Lottery Strategy Head Start](https://forums.saliu.com/lottery-strategies-start.html#strategy)":[25,32],"#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_#I. [Lottery Strategy Head Start](https://forums.saliu.com/lottery-strategies-start.html#strategy)#{1}":[26,32],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>":[33,124],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{1}":[35,38],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{2}":[39,40],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{3}":[41,91],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{4}":[92,93],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{5}":[94,121],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{6}":[122,122],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{7}":[123,124],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>":[125,143],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{1}":[127,128],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{2}":[129,129],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{3}":[130,131],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{4}":[132,143],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>3. Procrastination: Enemy of Lottery Strategies</u>":[144,184],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>3. Procrastination: Enemy of Lottery Strategies</u>#{1}":[146,184],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>":[185,265],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{1}":[187,200],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{2}":[201,202],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{3}":[203,204],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{4}":[205,206],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{5}":[207,263],"#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{6}":[264,265]},"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheeling strategies.","target":"https://saliu.com/AxiomIon.jpg","line":16},{"title":"Start playing lottery strategies, lotto strategies win real money, in actual play.","target":"https://forums.saliu.com/HLINE.gif","line":23},{"title":"Lottery Strategy Head Start","target":"https://forums.saliu.com/lottery-strategies-start.html#strategy","line":25},{"title":"Software to Create Reports of Lotto Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#reports","line":26},{"title":"Procrastination: Enemy of Lottery Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#procrastinate","line":27},{"title":"Software to Manage Lottery Data Files","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":28},{"title":"My Experience with Lottery Strategies (2013)","target":"https://forums.saliu.com/lottery-strategies-start.html#experience","line":29},{"title":"It is important to start working with lottery software to discover good playing strategies.","target":"https://forums.saliu.com/HLINE.gif","line":31},{"title":"The report shows the filters for the pick 3 lottery game.","target":"https://forums.saliu.com/lotto-filter.gif","line":45},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":92},{"title":"The checking of the lotto strategy is a neat report.","target":"https://forums.saliu.com/strategy-1.gif","line":102},{"title":"The next report shows the filters in MDIEditor and Lotto.","target":"https://forums.saliu.com/strategy-2.gif","line":104},{"title":" The reports are valuable tools to find new lotto strategies to win the lottery.","target":"https://forums.saliu.com/strategy-3.gif","line":106},{"title":"The reports are similar for every layer of the lotto data file of drawings.","target":"https://forums.saliu.com/strategy-4.gif","line":108},{"title":"The lotto pairs are essential in creating lottery strategies.","target":"https://forums.saliu.com/lotto-pairs.gif","line":118},{"title":"Sorted MD6 Winning Report","target":"https://saliu.com/freeware/MD6.1-SOR","line":122},{"title":"Lotto 649 Strategy Ver6\\_1=1000","target":"https://saliu.com/freeware/st6.rep","line":123},{"title":"We must check any lottery strategies against past drawings.","target":"https://forums.saliu.com/check-strategy-1.gif","line":132},{"title":"Back-testing a lottery strategy is done layer by layer in the draw file.","target":"https://forums.saliu.com/check-strategy-2.gif","line":134},{"title":"The reports show different data from layer to layer, which offers more choices.","target":"https://forums.saliu.com/check-strategy-3.gif","line":136},{"title":" Procrastination is the main obstacle in winning the lottery with Ion lotto software programs.","target":"https://forums.saliu.com/check-strategy-4.gif","line":140},{"title":"This is a real-life pick lottery strategy that hit the first prize.","target":"https://forums.saliu.com/play-1.gif","line":162},{"title":"The winning pick lottery strategy had a few tickets to play.","target":"https://forums.saliu.com/play-2.gif","line":164},{"title":"Lottery Data Files is a collection of software to work with past winning lotto numbers.","target":"https://forums.saliu.com/LotteryData.gif","line":191},{"title":"The lottery software works best at the command prompt run by a desktop shortcut.","target":"https://forums.saliu.com/LotteryDataShortcut.gif","line":197},{"title":"_**Run Lottery Software at Command Prompt in Windows XP, Vista, Windows 7, 8, 10**_","target":"https://saliu.com/gambling-lottery-lotto/command-prompt.htm","line":201},{"title":"_**Home of Notepad++**_","target":"https://notepad-plus-plus.org/","line":205},{"title":"My lottery software is enhanced nicely by the best text editor: Notepad plus plus.","target":"https://forums.saliu.com/NotepadLottery.gif","line":216},{"title":"_**Download LotteryData.zip**_","target":"https://saliu.com/pub/LotteryData.zip","line":263}],"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md","metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lottery","strategy","lotto strategies","start play","procrastination","win","Pennsylvania Lottery"],"source":"https://forums.saliu.com/lottery-strategies-start.html","author":null}},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08943702,-0.0307828,-0.00027538,-0.01139795,-0.03613041,0.07060434,0.05025844,0.02386545,0.0534871,-0.04273827,0.02218768,-0.01331814,0.00657074,0.01235475,-0.03325789,-0.03402555,-0.00348228,-0.02446732,-0.03229902,0.01579735,0.10168752,-0.02295072,-0.03517044,-0.05244137,0.07199075,0.04728573,0.01475323,-0.04724869,-0.02722248,-0.14476012,0.00496387,0.00998387,-0.01803612,-0.01848536,-0.05875822,-0.03534209,-0.03551649,0.12494829,-0.01141525,0.08311377,0.01763424,0.00092221,-0.02507498,0.02461972,0.00100015,-0.00158189,-0.01427371,0.01122093,0.01254997,0.00230943,-0.06101276,0.02049644,-0.00959196,-0.01525922,0.09384575,0.02639681,0.07615147,0.08409658,0.06353779,0.02187316,0.03022872,0.04107969,-0.22783236,0.05549508,-0.0110302,0.04248,0.00110494,0.03915484,-0.04003204,0.0418798,0.05551811,0.0496387,-0.01615179,0.04555972,0.03061818,-0.0237512,-0.05690638,-0.04235153,-0.03011329,0.01812438,-0.02425339,-0.03214649,-0.06267741,-0.04087626,-0.04519833,0.0322255,0.00483377,0.08382889,0.06949863,-0.07585508,0.03195073,-0.00088012,0.00129484,0.03627802,-0.02385069,0.01386264,0.01219213,-0.00130126,-0.03697421,0.12659962,-0.03200997,0.03973087,-0.00133928,0.02046622,0.04576503,-0.01103661,-0.01389446,-0.02866581,-0.02077468,0.033303,0.01318364,0.03114155,0.09848709,-0.09967344,0.02389567,0.01147327,0.01267631,0.01847977,0.04420402,0.01163445,-0.0199248,0.03245503,-0.00628518,-0.03298358,-0.03902348,0.00174979,0.04041417,0.08867619,0.03812261,-0.03124289,-0.01835262,0.01027543,-0.128923,-0.02618914,-0.01456009,-0.01282097,0.04152969,-0.01355049,0.01850085,-0.03368885,-0.01432772,-0.0451941,0.04698489,-0.12273284,-0.0534989,0.0545119,0.02646109,0.00145082,0.02648081,0.03208942,-0.02395561,-0.00100121,-0.04033991,-0.0901189,-0.04701297,-0.00838829,0.07850073,0.07001311,0.00027699,-0.0137098,-0.04939149,-0.0743011,-0.01739986,0.0572481,-0.00913309,-0.10606112,-0.05628513,-0.01142086,-0.03388231,-0.13068257,-0.02327478,-0.03118723,-0.06679735,0.00007136,0.09568156,0.00935321,-0.012189,-0.06304003,-0.04989021,0.02048488,0.01975449,-0.0246366,-0.0573299,-0.01480925,-0.05461493,-0.06245882,-0.01411857,-0.05000471,0.08014025,0.02321856,-0.0428079,-0.04327152,-0.0285398,0.0016597,-0.01293197,-0.01852947,-0.06487832,-0.02514456,0.07745603,-0.04874106,-0.03982899,-0.01082852,0.00272958,0.03858389,-0.04334208,0.02801421,0.03518812,-0.03789214,0.08979893,0.00030588,-0.02814005,0.00283686,0.03200926,0.04196715,-0.04975982,0.02685216,-0.00288872,-0.01160222,-0.01362932,0.0307737,-0.00724977,0.017997,-0.04759494,-0.17105329,-0.0046813,-0.04886799,-0.0318989,-0.02473174,0.00607462,0.00716521,-0.00077139,0.02942562,0.03039337,0.06625549,-0.04122251,0.0077428,0.04642756,0.00065164,0.00372348,-0.07123678,-0.04197467,-0.00007392,0.0463403,0.00988571,0.00014237,-0.05810822,-0.05839807,0.01623956,0.01706827,0.1242536,0.08803539,-0.00993772,-0.01788173,0.07003786,0.02616617,-0.03062484,-0.10043629,0.00661666,-0.02288213,-0.01028183,-0.00456642,-0.07935046,-0.00887261,-0.09044971,0.01960348,-0.02645663,-0.06830788,-0.02159968,0.01385617,0.00889427,0.06084932,-0.00674188,0.05844259,0.05981065,0.02482992,0.05469587,0.02286563,0.0647856,-0.02311749,-0.07664789,-0.00701407,-0.01778743,0.02003327,-0.01886535,-0.00825189,0.0353295,-0.00472533,0.02414243,0.01576461,-0.00601839,-0.05181351,0.00490583,-0.00448166,-0.00002059,0.04918692,0.01176642,-0.04088431,-0.02041751,-0.00462884,0.04305159,-0.05453357,0.04465548,-0.00217911,0.04892699,-0.07713004,0.04318957,0.06643766,0.04913031,0.01524292,-0.00270933,0.03087049,0.03314126,-0.01929821,-0.01649914,0.07395639,-0.03361566,-0.00414715,0.03476392,-0.03928443,-0.28574648,0.03949601,0.01585908,0.05503327,0.01027971,0.03390435,0.03018799,0.04219006,-0.03897939,0.00582528,0.06560093,0.03570644,-0.0098347,-0.01218663,-0.04558636,-0.0447147,0.03380592,-0.04502802,0.07111988,0.00712742,0.09548152,0.01759177,0.26160157,-0.00571973,0.029925,-0.02014389,0.04073304,-0.00832539,0.00598781,0.06290857,-0.0131591,0.03232,0.05396583,0.01077777,-0.01391104,-0.00469172,-0.0047748,0.06069196,-0.02924987,0.01162764,-0.10218904,-0.00854771,0.00123879,0.03889524,0.11889401,0.01980124,-0.02280603,-0.09582435,-0.0101631,0.06289744,-0.08938926,-0.06681249,-0.04964983,-0.00478752,-0.03109402,0.06267259,0.07330433,-0.0092779,0.05406644,0.03705381,0.05154474,0.03566207,0.07273173,0.02916014,0.03428203],"last_embed":{"hash":"1p40vsp","tokens":90}}},"text":null,"length":0,"last_read":{"hash":"1p40vsp","at":1753423593260},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#---frontmatter---","lines":[1,6],"size":216,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06358689,-0.0321298,-0.01001626,-0.01120624,-0.04374731,0.08899966,0.05082678,-0.00293919,0.03635348,-0.04611248,0.00807753,-0.01345904,0.03670335,0.01835641,-0.026298,-0.02378896,-0.03103405,0.01224662,-0.05510319,0.02006587,0.08383511,-0.03007556,-0.02277806,-0.08419473,0.08141205,0.04771668,-0.00761454,-0.0549787,-0.00963929,-0.20464361,0.00403722,0.0108061,0.00198464,-0.0406049,-0.08025143,-0.0180752,-0.04468335,0.10102695,-0.04380241,0.06254268,0.0178215,-0.00659242,0.00631705,-0.00484526,0.02776515,-0.01495572,0.02499983,0.0207013,0.03538374,-0.00775963,-0.09482706,0.0019342,-0.02773083,-0.01716966,0.10210996,0.02252198,0.08226983,0.09871088,0.05584225,0.00801632,0.03255383,0.05245572,-0.19782369,0.06275092,-0.01089082,0.0286183,0.01161109,0.03368253,-0.02663933,0.06352289,0.0510642,0.0244237,-0.01579498,0.06794946,0.02570643,-0.03811623,-0.03169359,-0.04863122,-0.01336177,0.04890181,-0.0141076,0.00394874,-0.03243576,0.01063002,-0.04914192,0.06226125,0.02515293,0.03292308,0.07198896,-0.09203926,0.02202364,0.02868495,-0.0006843,0.02213246,0.01554793,-0.02730944,0.00393717,-0.01027332,-0.02608205,0.10985058,-0.01119956,-0.00627276,-0.00600671,0.02999391,0.07793608,-0.03503757,-0.01454438,-0.04297704,-0.01908215,0.055418,0.0024444,0.01794984,0.06064893,-0.04608981,-0.01318212,0.01697941,-0.00366438,-0.01385511,-0.00203619,0.00428195,-0.01607437,0.06196504,0.01506377,-0.02076465,-0.04360212,-0.02208298,0.03559773,0.08822564,0.03147092,-0.01097755,0.01641925,0.02314388,-0.12594622,-0.02040148,-0.03557683,-0.0442652,0.01495839,0.01987564,0.00757322,0.0083858,-0.0264694,-0.00912339,0.05426522,-0.09904909,-0.05455406,0.02651784,-0.0095125,-0.02307741,0.00204782,0.03808497,0.00509308,-0.0305399,-0.03855099,-0.09911803,-0.02073034,-0.04015401,0.09525276,0.07609754,-0.03374007,-0.02603973,-0.08103137,-0.06189916,0.00669789,0.08718939,0.03564431,-0.06840357,-0.02088423,0.01401784,-0.03852369,-0.12254678,-0.01845769,-0.03000139,-0.06634234,0.01823641,0.09150103,-0.01183561,-0.06432997,-0.0787593,-0.06519917,0.04186485,0.02107724,-0.05108671,-0.0251562,-0.03898636,-0.04302922,-0.06940509,0.00842916,-0.06021236,0.05944693,0.01317196,-0.04111502,0.04063855,-0.05487521,-0.00648383,-0.02889344,-0.02534158,-0.04561929,-0.04921322,0.057783,-0.01208895,-0.04350824,0.00033703,0.01468058,0.03598468,-0.04380485,0.01535107,0.02299507,-0.04197608,0.11070617,0.04883652,-0.03983347,-0.02270979,0.02075179,0.03662847,-0.03340919,0.02501139,0.03537104,0.02638636,-0.0015919,0.06560022,-0.01314851,0.02711145,-0.06446464,-0.1567843,-0.02127062,-0.06996982,-0.01929503,-0.00282872,0.01658146,0.03611769,-0.01892835,0.03500303,-0.00966052,0.10756699,-0.0683626,0.02300994,0.06506205,0.01688042,-0.01637727,-0.0972213,-0.02626333,-0.02939025,0.04592974,-0.01460517,0.01377309,-0.0410807,-0.03887727,-0.01178336,0.0206517,0.13855428,0.04209024,0.03819946,0.00684354,0.06591639,0.03855982,-0.03050988,-0.09324209,-0.00314424,-0.02232121,0.01664176,-0.05978938,-0.04866047,-0.01029574,-0.06641752,0.02353327,-0.01793258,-0.07353875,-0.06301223,-0.00383294,-0.0018993,0.06354102,0.00631674,0.05136385,0.07770164,0.03945219,0.04420784,0.02860026,0.04211737,-0.03874592,-0.07457024,0.00548801,-0.00586048,0.06314587,-0.02056888,0.00501952,0.03350888,-0.01166478,0.05646902,0.01476961,-0.00269478,-0.02314003,0.03768291,-0.00290717,-0.01084128,0.06351097,-0.01572249,-0.00448489,-0.01418574,-0.00439316,0.04063177,-0.07290652,0.0311951,0.01167952,0.0054272,-0.07124283,0.04480479,0.06153104,0.04155515,0.00524027,0.03113551,0.02001627,-0.00093624,-0.01487641,-0.00674046,0.04826003,-0.04699166,0.02273707,0.01455187,0.00425836,-0.27949455,0.03109258,-0.02538427,0.04693744,-0.02513552,0.0053522,0.05484661,0.02725248,-0.00754921,-0.01934456,0.07115494,0.02349341,-0.00986292,-0.02372405,-0.00958252,-0.0238324,-0.00485608,-0.03489189,0.09060707,0.00013835,0.07219453,0.02930067,0.26126203,-0.03462176,0.04017657,-0.00154645,0.02668454,-0.00373923,-0.0092115,0.05235446,-0.01240204,0.0078725,0.06647322,0.00373935,-0.03004881,-0.01747536,0.02074056,0.03783209,-0.02455627,0.00079957,-0.068202,-0.00138287,0.0281655,0.05579806,0.12565804,-0.00017389,-0.00829805,-0.10154364,0.02245401,0.05924411,-0.11914179,-0.06964285,-0.05413055,-0.01085601,-0.02418256,0.03913997,0.05755942,-0.00597245,0.01534628,0.01408117,0.02686966,0.01985504,0.06469534,0.01890907,0.01445451],"last_embed":{"hash":"5hd0n7","tokens":494}}},"text":null,"length":0,"last_read":{"hash":"5hd0n7","at":1753423593303},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start","lines":[8,265],"size":21789,"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheeling strategies.","target":"https://saliu.com/AxiomIon.jpg","line":9},{"title":"Start playing lottery strategies, lotto strategies win real money, in actual play.","target":"https://forums.saliu.com/HLINE.gif","line":16},{"title":"Lottery Strategy Head Start","target":"https://forums.saliu.com/lottery-strategies-start.html#strategy","line":18},{"title":"Software to Create Reports of Lotto Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#reports","line":19},{"title":"Procrastination: Enemy of Lottery Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#procrastinate","line":20},{"title":"Software to Manage Lottery Data Files","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":21},{"title":"My Experience with Lottery Strategies (2013)","target":"https://forums.saliu.com/lottery-strategies-start.html#experience","line":22},{"title":"It is important to start working with lottery software to discover good playing strategies.","target":"https://forums.saliu.com/HLINE.gif","line":24},{"title":"The report shows the filters for the pick 3 lottery game.","target":"https://forums.saliu.com/lotto-filter.gif","line":38},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":85},{"title":"The checking of the lotto strategy is a neat report.","target":"https://forums.saliu.com/strategy-1.gif","line":95},{"title":"The next report shows the filters in MDIEditor and Lotto.","target":"https://forums.saliu.com/strategy-2.gif","line":97},{"title":" The reports are valuable tools to find new lotto strategies to win the lottery.","target":"https://forums.saliu.com/strategy-3.gif","line":99},{"title":"The reports are similar for every layer of the lotto data file of drawings.","target":"https://forums.saliu.com/strategy-4.gif","line":101},{"title":"The lotto pairs are essential in creating lottery strategies.","target":"https://forums.saliu.com/lotto-pairs.gif","line":111},{"title":"Sorted MD6 Winning Report","target":"https://saliu.com/freeware/MD6.1-SOR","line":115},{"title":"Lotto 649 Strategy Ver6\\_1=1000","target":"https://saliu.com/freeware/st6.rep","line":116},{"title":"We must check any lottery strategies against past drawings.","target":"https://forums.saliu.com/check-strategy-1.gif","line":125},{"title":"Back-testing a lottery strategy is done layer by layer in the draw file.","target":"https://forums.saliu.com/check-strategy-2.gif","line":127},{"title":"The reports show different data from layer to layer, which offers more choices.","target":"https://forums.saliu.com/check-strategy-3.gif","line":129},{"title":" Procrastination is the main obstacle in winning the lottery with Ion lotto software programs.","target":"https://forums.saliu.com/check-strategy-4.gif","line":133},{"title":"This is a real-life pick lottery strategy that hit the first prize.","target":"https://forums.saliu.com/play-1.gif","line":155},{"title":"The winning pick lottery strategy had a few tickets to play.","target":"https://forums.saliu.com/play-2.gif","line":157},{"title":"Lottery Data Files is a collection of software to work with past winning lotto numbers.","target":"https://forums.saliu.com/LotteryData.gif","line":184},{"title":"The lottery software works best at the command prompt run by a desktop shortcut.","target":"https://forums.saliu.com/LotteryDataShortcut.gif","line":190},{"title":"_**Run Lottery Software at Command Prompt in Windows XP, Vista, Windows 7, 8, 10**_","target":"https://saliu.com/gambling-lottery-lotto/command-prompt.htm","line":194},{"title":"_**Home of Notepad++**_","target":"https://notepad-plus-plus.org/","line":198},{"title":"My lottery software is enhanced nicely by the best text editor: Notepad plus plus.","target":"https://forums.saliu.com/NotepadLottery.gif","line":209},{"title":"_**Download LotteryData.zip**_","target":"https://saliu.com/pub/LotteryData.zip","line":256}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06015847,-0.00481584,-0.00861749,0.0042812,-0.06783661,0.08611414,0.08136035,-0.0051973,0.03441345,-0.03454653,0.0198666,-0.02408358,0.03089201,0.01930602,-0.03953671,-0.02561386,-0.03089238,0.02477452,-0.04643457,0.00212319,0.10200489,-0.02740799,-0.04995184,-0.08368142,0.07239714,0.0369311,0.00235008,-0.05289354,-0.01256177,-0.18873398,0.01398546,0.00793717,-0.02135023,-0.03119026,-0.06531656,-0.02036491,-0.03877212,0.10993437,-0.03045144,0.07052323,0.02766646,0.00479421,0.00372018,-0.00142616,0.02914637,-0.00680266,0.01691557,0.02709433,0.02248178,-0.00596598,-0.0791791,0.01147061,-0.00887606,-0.02909672,0.08730619,0.02375402,0.0756056,0.09479029,0.05502788,0.00785051,0.04085618,0.05873465,-0.20781364,0.04917435,-0.00824301,0.02912108,0.0163231,0.04381384,-0.04725884,0.0773714,0.06062388,0.02043688,-0.0087886,0.06660448,0.03284238,-0.02167756,-0.0455111,-0.04456107,-0.01033944,0.05581256,-0.01502588,-0.00828235,-0.04253574,-0.01509615,-0.05632005,0.05901271,0.02009684,0.02888448,0.08104794,-0.09120198,0.03404732,0.02630991,-0.01249236,0.02626358,0.00613717,-0.00962991,0.00556735,-0.01571792,-0.01649173,0.12978812,-0.0212607,0.01788699,-0.00002126,0.02876668,0.05673006,-0.04082048,-0.01958032,-0.03085033,-0.01889707,0.05903646,0.00312119,0.01012957,0.08102711,-0.04533429,-0.00939967,0.00867052,-0.01374139,-0.00959535,0.00116375,0.00694589,-0.02025068,0.05169638,0.02256854,-0.02298367,-0.0497554,-0.02317248,0.0236378,0.10690624,0.02479827,-0.02051795,0.02122635,0.03175111,-0.11645439,-0.01363816,-0.02172564,-0.04951022,0.02832555,0.01174559,0.00025287,0.00814234,-0.01741424,0.00925614,0.05106694,-0.10550988,-0.06217695,0.03872716,-0.00052702,-0.0097501,0.01511263,0.05015583,-0.00416282,-0.03708351,-0.03358376,-0.10049747,-0.02709024,-0.02046144,0.07831457,0.07916698,-0.03014441,-0.01771441,-0.08174725,-0.05212495,-0.01120247,0.09126398,0.01363963,-0.04924654,-0.0260782,0.00235515,-0.0184775,-0.13664056,-0.01795521,-0.03299578,-0.074053,0.00832415,0.09299219,0.02197626,-0.05111673,-0.08555462,-0.07717696,0.01558207,0.03996156,-0.04974236,-0.03330204,-0.02933607,-0.04166866,-0.0570997,-0.00127323,-0.0612957,0.06190184,0.02677403,-0.03260642,0.0443472,-0.05799979,-0.02610755,-0.02728326,-0.02648638,-0.04310144,-0.04202266,0.04755475,-0.01621915,-0.0615031,0.01466096,-0.00292822,0.03838415,-0.03245621,0.01815161,0.0292244,-0.03996874,0.09086122,0.0495785,-0.03200955,-0.01632909,0.00265482,0.05567622,-0.02938814,0.04064472,0.03145973,0.01588254,-0.0126842,0.03226355,-0.00619374,0.02034857,-0.05180731,-0.15645216,-0.01181671,-0.0508822,-0.02425098,0.00032894,0.03239811,0.01304806,-0.00805299,0.03136022,-0.01939991,0.08473658,-0.07821824,0.02498457,0.08145151,0.00754521,-0.01649804,-0.10857395,-0.02977377,-0.02069225,0.02844494,-0.01120906,0.0242314,-0.04442681,-0.04870778,0.00392401,0.02407841,0.11640657,0.05322273,0.03448449,0.00564446,0.06732178,0.04024136,-0.03350915,-0.07021718,-0.01353142,-0.02687595,0.00983549,-0.05596085,-0.07236405,-0.02739242,-0.06913733,0.02401179,-0.03544775,-0.06261344,-0.04384407,-0.00442041,0.01297709,0.07044497,-0.00108009,0.05109843,0.07502002,0.05205143,0.0514093,0.02957388,0.0591604,-0.02527164,-0.06779461,0.01647032,-0.01914323,0.03543124,-0.01496697,-0.00623786,0.03034286,0.00263028,0.05504582,0.0068965,-0.00399018,-0.04364794,0.00646784,-0.00365215,0.01141558,0.06353346,-0.02151765,-0.02811717,-0.01818256,0.00810243,0.0492697,-0.09489989,0.02142963,0.00497758,0.02257144,-0.06298717,0.03033678,0.06215499,0.05064628,-0.00423742,0.03328543,0.02674115,-0.01878104,-0.02034073,-0.01584579,0.05874266,-0.05564821,0.00837744,-0.00300151,-0.00706553,-0.27883655,0.03972887,-0.02947237,0.04086498,-0.01710157,0.00689962,0.03795064,0.02640465,-0.0193753,-0.00610983,0.06946238,0.02651308,-0.01186334,-0.01460045,-0.02322245,-0.03423664,0.02459886,-0.04763257,0.09057559,-0.00696599,0.07666432,0.03225035,0.26234484,-0.03197048,0.04080918,0.01015043,0.02593539,-0.01219899,-0.01345903,0.05356194,-0.00905045,0.00319821,0.07533827,-0.00997957,-0.02166818,-0.0047167,0.0193774,0.06525323,-0.02693868,0.0005909,-0.06640422,-0.02590184,0.02832199,0.06421384,0.11828999,0.00883822,-0.01434568,-0.08951818,0.01940192,0.07279891,-0.1103178,-0.06009268,-0.07055992,-0.02666847,-0.01989747,0.03138896,0.03794787,-0.01340345,0.02285295,0.02789632,0.02441791,0.00909401,0.06048876,0.02062168,0.00957807],"last_embed":{"hash":"1byb3d2","tokens":136}}},"text":null,"length":0,"last_read":{"hash":"1byb3d2","at":1753423593523},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#{1}","lines":[10,17],"size":390,"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheeling strategies.","target":"https://saliu.com/AxiomIon.jpg","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07323943,-0.02691767,0.00008122,-0.0144443,-0.03050242,0.06242073,0.080809,0.00659327,0.04028019,-0.0402977,0.02339639,-0.0316088,0.03135451,0.00162182,-0.02277621,-0.04232279,-0.01250617,-0.01578248,-0.03902873,0.00632921,0.09254226,-0.015466,-0.00680838,-0.07237801,0.07138748,0.06849618,-0.01309205,-0.05677667,-0.0180827,-0.17084014,-0.00344363,0.01689465,0.00046223,-0.04214796,-0.09039288,-0.01433681,-0.02967552,0.10246674,-0.03142292,0.07533278,0.01756954,-0.02551222,0.00448205,-0.00796893,0.00778852,-0.01605629,0.02542436,0.0202402,0.00959281,-0.00696759,-0.09134851,0.01257437,-0.05101177,-0.02492305,0.09537295,0.0396507,0.07680683,0.10167915,0.04642414,0.00689212,0.05250649,0.06340431,-0.21226276,0.04978283,-0.0297218,0.03978964,-0.00802416,0.03215443,-0.02230152,0.05802883,0.04796741,0.02725242,-0.02873838,0.04870615,0.03731794,-0.05280877,-0.05464103,-0.03991264,-0.02444273,0.0261517,-0.03443626,-0.00212193,-0.02816178,-0.0159238,-0.03868151,0.06415906,0.02346901,0.05445859,0.06283184,-0.10645973,0.03090939,0.02902517,-0.0274489,0.04114773,0.01939382,0.00153986,0.01450039,-0.02203394,-0.04068565,0.13515483,-0.01048002,-0.01051624,-0.00537129,0.04464645,0.05949338,-0.01169869,-0.00451962,-0.02360933,-0.01011742,0.04292038,0.01412621,0.00123135,0.04910777,-0.06457442,-0.02188389,0.0145309,-0.01007106,-0.01169053,0.02579237,-0.00687645,-0.00977008,0.03763065,0.00775967,-0.02712729,-0.03688266,-0.02874706,0.0348882,0.07678553,0.03585174,-0.01347618,-0.00124233,0.01586476,-0.15230039,-0.03713885,-0.05162358,-0.02434795,0.03158486,0.00885092,-0.0085963,0.00107166,-0.02485209,-0.03036338,0.03908287,-0.11017352,-0.06173037,0.05190846,0.0020408,-0.01314311,0.02048116,0.05243361,-0.00840405,0.00251969,-0.04285665,-0.07356824,-0.04561967,-0.02174887,0.08871508,0.06845841,-0.0451366,-0.02213394,-0.05897353,-0.06900584,-0.01626222,0.07379487,0.00771447,-0.06885107,-0.03717679,0.00171247,-0.03319695,-0.12126181,-0.03192675,-0.02162542,-0.06558827,-0.00014397,0.07709742,-0.00681619,-0.0358463,-0.084383,-0.0696502,0.02126605,0.03228531,-0.03205067,-0.03190926,-0.01455225,-0.05561895,-0.07807337,-0.00056741,-0.04525942,0.07753401,0.01027835,-0.04722308,0.01540145,-0.03478053,-0.01638115,-0.02099399,-0.01743057,-0.07377417,-0.03707634,0.06795283,-0.01732137,-0.04114242,0.01122039,0.03613489,0.04364625,-0.05405736,0.03399632,0.02043675,-0.04689145,0.09509294,0.00154606,-0.0527319,0.0033052,0.01800066,0.04828381,-0.042686,0.030097,0.02707187,0.02114232,-0.00870815,0.04701512,-0.00238752,0.04446924,-0.066288,-0.15687242,-0.00718546,-0.06834017,-0.03075544,-0.0199896,0.01562752,0.02695794,-0.01046023,0.03456635,0.0021338,0.09167966,-0.06855306,0.02864854,0.0579214,0.01228315,-0.00230346,-0.08680888,-0.04498531,-0.02947773,0.06518763,-0.01998226,0.00837508,-0.03573131,-0.04402661,-0.0082067,0.01889787,0.12224676,0.07249372,0.02017616,-0.00393611,0.04620287,0.04127346,-0.03562903,-0.11242565,0.00899203,0.00233444,-0.00732633,-0.02033284,-0.06651301,0.00132037,-0.08491536,0.02993467,-0.02862922,-0.04889417,-0.04581823,0.02597089,0.00359907,0.0526342,0.00389462,0.04240277,0.06231116,0.05174805,0.02829087,0.04864288,0.06869143,-0.02173593,-0.06109122,0.00435418,-0.01835004,0.03604984,-0.02455349,0.01364018,0.0442584,-0.02385447,0.0470813,0.00053903,0.00490985,-0.06254915,0.05036559,0.00612577,-0.01280234,0.07307819,-0.00694596,-0.02086953,-0.01105954,-0.00347358,0.06415369,-0.08377911,0.01015435,0.01790925,0.02744234,-0.08443805,0.04040134,0.06306162,0.07387634,0.00905524,0.01811786,0.02454897,0.0317719,-0.01606716,-0.02224348,0.05918756,-0.02742631,0.01089429,0.01196388,0.00044196,-0.26871693,0.02806704,0.00379432,0.06102632,-0.00085411,0.01511566,0.06799094,0.01299146,-0.03152175,-0.01023376,0.06602845,0.01126451,0.00304629,-0.02648632,-0.03344152,-0.03256093,0.03643693,-0.04677742,0.08475424,0.01814553,0.08755255,0.01580067,0.25975949,-0.02910043,0.03086224,-0.0147016,0.02531465,-0.00808619,0.00608403,0.08144929,0.00481542,0.01236587,0.06986692,0.02008305,-0.01958442,-0.00741699,0.010152,0.02764226,-0.00757853,0.00114735,-0.08172644,-0.00845775,0.00462515,0.04047556,0.11279773,0.01000926,0.00719183,-0.09108098,-0.00149802,0.06999666,-0.11588736,-0.06683122,-0.04043471,-0.00909155,0.01524579,0.0446224,0.04976114,0.00169827,0.02965878,0.02416491,0.03121114,0.02708934,0.06243277,0.02301135,0.02041803],"last_embed":{"hash":"1a5txl3","tokens":288}}},"text":null,"length":0,"last_read":{"hash":"1a5txl3","at":1753423593579},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_","lines":[21,32],"size":908,"outlinks":[{"title":"Start playing lottery strategies, lotto strategies win real money, in actual play.","target":"https://forums.saliu.com/HLINE.gif","line":3},{"title":"Lottery Strategy Head Start","target":"https://forums.saliu.com/lottery-strategies-start.html#strategy","line":5},{"title":"Software to Create Reports of Lotto Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#reports","line":6},{"title":"Procrastination: Enemy of Lottery Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#procrastinate","line":7},{"title":"Software to Manage Lottery Data Files","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":8},{"title":"My Experience with Lottery Strategies (2013)","target":"https://forums.saliu.com/lottery-strategies-start.html#experience","line":9},{"title":"It is important to start working with lottery software to discover good playing strategies.","target":"https://forums.saliu.com/HLINE.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_#I. [Lottery Strategy Head Start](https://forums.saliu.com/lottery-strategies-start.html#strategy)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07266191,-0.02843912,0.00294399,-0.01293106,-0.03048292,0.05808307,0.09320877,0.00439327,0.04518604,-0.03722743,0.02135556,-0.03569162,0.03665312,0.00152649,-0.0224393,-0.03512857,-0.0222686,-0.01824536,-0.03608366,-0.0056195,0.08950265,-0.01165207,-0.00380011,-0.06789239,0.06489699,0.06851654,-0.00958217,-0.05599402,-0.02045775,-0.16614015,-0.00156474,0.01535058,-0.00286365,-0.03796833,-0.09682608,-0.01765189,-0.02949791,0.09626326,-0.03233448,0.07509976,0.02011084,-0.02631958,0.00748084,-0.00853127,0.00218832,-0.01509628,0.02517012,0.01153901,0.00724253,-0.00628678,-0.08677403,0.00105547,-0.04903566,-0.02273719,0.09645071,0.04195542,0.07773289,0.09759434,0.04560691,0.00993218,0.05293808,0.05773466,-0.21706678,0.05276448,-0.02673173,0.03846805,-0.02097351,0.03387096,-0.03080057,0.05101512,0.04126766,0.02795713,-0.02692949,0.04940689,0.03898145,-0.04810321,-0.0554065,-0.04117727,-0.02286985,0.02532668,-0.03783979,-0.00588173,-0.02901004,-0.02186704,-0.02877104,0.06224576,0.02196236,0.04641459,0.06707286,-0.10120083,0.03124316,0.02700541,-0.02031354,0.04231531,0.0195837,-0.0008169,0.01646255,-0.02160004,-0.04950815,0.13231854,-0.00580336,-0.00929783,-0.00756312,0.04546313,0.06274915,-0.00681539,-0.00538852,-0.02513132,-0.0060495,0.04026963,0.01648337,0.00319998,0.04157458,-0.06555592,-0.01758717,0.01020273,-0.01337846,-0.0082157,0.02000118,-0.0072402,-0.01281524,0.03181766,0.0137906,-0.0258015,-0.03173089,-0.02117309,0.03704545,0.07909553,0.03446801,-0.01548544,0.00305536,0.01175673,-0.15070692,-0.04533676,-0.05104874,-0.02311369,0.03299413,-0.00094902,-0.01295239,0.00140298,-0.02247328,-0.02969147,0.0445963,-0.10856242,-0.05808521,0.05746006,0.00258511,-0.01301177,0.01966872,0.05717701,-0.01122356,0.00334517,-0.03760188,-0.07218854,-0.04288255,-0.02109405,0.08624586,0.06831076,-0.04239241,-0.01096093,-0.0561766,-0.07298049,-0.01551754,0.07781171,0.00837382,-0.07101074,-0.04316876,0.00150266,-0.02927105,-0.12107914,-0.02739133,-0.02036989,-0.05933188,0.00864568,0.07668727,-0.00441158,-0.03477242,-0.08048753,-0.07013568,0.02333659,0.03409478,-0.03064289,-0.02900719,-0.0155151,-0.05288784,-0.07298821,0.00502131,-0.04789776,0.08011048,0.0131128,-0.04917005,0.02403568,-0.03190023,-0.01947372,-0.02277786,-0.01385817,-0.07577617,-0.03622166,0.06722503,-0.02220051,-0.0444281,0.01437816,0.03458659,0.04600947,-0.05206236,0.03439275,0.01842334,-0.0372167,0.08781015,0.00567918,-0.05248056,0.008009,0.01773629,0.04951762,-0.03763733,0.0330199,0.02108913,0.02017872,-0.00367405,0.04389972,-0.00080174,0.0460264,-0.06648799,-0.16328947,-0.00722416,-0.07620841,-0.02880018,-0.02256597,0.01287798,0.03086474,-0.01027629,0.03507845,-0.00210996,0.09407222,-0.06749204,0.02972006,0.05782965,0.00311253,-0.00327922,-0.08026717,-0.04446942,-0.03753711,0.0615579,-0.0110507,0.00116647,-0.0366516,-0.04134555,-0.01463034,0.01606912,0.12666734,0.07573052,0.01908473,-0.0036581,0.04546751,0.04335656,-0.03780182,-0.11438356,0.01083012,0.00413039,-0.01189265,-0.02298106,-0.06345172,-0.00240208,-0.08941016,0.02849014,-0.02141075,-0.04975516,-0.04339936,0.02316017,0.01639522,0.04873309,-0.00206702,0.04108661,0.0611023,0.05374065,0.02710784,0.04637657,0.07112943,-0.01962532,-0.06025386,0.00966256,-0.02369528,0.03476594,-0.02933272,0.01426062,0.04693502,-0.02909886,0.04661896,0.00474221,0.00803003,-0.05756887,0.04820925,0.00416562,-0.01598128,0.07781503,-0.01522495,-0.02316805,-0.01508299,-0.00922899,0.05672432,-0.08163121,0.01154379,0.01749329,0.02071028,-0.08607315,0.04033964,0.06148365,0.0751064,0.00446799,0.01062609,0.02450139,0.03107778,-0.01846618,-0.02516745,0.05865568,-0.02973634,0.01123934,0.01311972,0.00546393,-0.2774803,0.03434007,-0.00009594,0.06206989,0.00924568,0.02236206,0.06608153,0.01020158,-0.02262494,-0.01070805,0.0639785,0.0095486,-0.00220791,-0.02331006,-0.04089913,-0.02529406,0.03854791,-0.0467697,0.0882965,0.01805737,0.07704072,0.02168267,0.26432979,-0.02601871,0.02701897,-0.00577768,0.02205916,-0.00784866,0.00270807,0.08308177,-0.00087201,0.005441,0.07676718,0.02060661,-0.02185972,-0.00855332,0.00590037,0.01779792,-0.00755533,0.00524189,-0.07759286,-0.01628253,0.00379034,0.04381918,0.11339665,0.01539347,-0.00267739,-0.07969381,0.00439644,0.07087231,-0.1219961,-0.06116831,-0.04415397,-0.00354063,0.0177407,0.04402885,0.05633101,0.00667963,0.02955358,0.02034635,0.0385989,0.02136299,0.06365068,0.01962614,0.02398567],"last_embed":{"hash":"1wrqz2v","tokens":278}}},"text":null,"length":0,"last_read":{"hash":"1wrqz2v","at":1753423593694},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_#I. [Lottery Strategy Head Start](https://forums.saliu.com/lottery-strategies-start.html#strategy)","lines":[25,32],"size":707,"outlinks":[{"title":"Lottery Strategy Head Start","target":"https://forums.saliu.com/lottery-strategies-start.html#strategy","line":1},{"title":"Software to Create Reports of Lotto Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#reports","line":2},{"title":"Procrastination: Enemy of Lottery Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#procrastinate","line":3},{"title":"Software to Manage Lottery Data Files","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":4},{"title":"My Experience with Lottery Strategies (2013)","target":"https://forums.saliu.com/lottery-strategies-start.html#experience","line":5},{"title":"It is important to start working with lottery software to discover good playing strategies.","target":"https://forums.saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_#I. [Lottery Strategy Head Start](https://forums.saliu.com/lottery-strategies-start.html#strategy)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06907741,-0.02965357,0.00133279,-0.01268662,-0.02497428,0.05756996,0.09111782,0.00345561,0.04071959,-0.03885533,0.02582867,-0.03224352,0.03840204,0.00070617,-0.02235303,-0.03565081,-0.01840911,-0.01707867,-0.03376486,-0.00573891,0.0896995,-0.01300841,-0.00480618,-0.07094562,0.06774756,0.07156132,-0.01825971,-0.05635368,-0.02289653,-0.16942307,-0.00421613,0.01455816,-0.0021861,-0.04074988,-0.09045602,-0.01994496,-0.03090018,0.10058224,-0.03819458,0.07219817,0.0164074,-0.02729073,0.00838702,-0.01328165,0.00297757,-0.01708771,0.02650023,0.00886093,0.00902342,-0.00960207,-0.08887371,0.00529342,-0.04911156,-0.01705681,0.09189577,0.04271877,0.07554349,0.10111907,0.04540479,0.01110575,0.05735516,0.05572787,-0.215546,0.05233438,-0.0307188,0.04951596,-0.01871013,0.0338032,-0.02980648,0.05557628,0.03286247,0.02903561,-0.02754462,0.05344788,0.03899315,-0.04811549,-0.05303534,-0.03737329,-0.02607119,0.02339394,-0.04040532,-0.0050188,-0.028876,-0.01937415,-0.03336628,0.05708433,0.02265031,0.04579545,0.06910826,-0.1006441,0.0305668,0.02526215,-0.01283101,0.04024302,0.01531636,-0.00478902,0.01753076,-0.0163766,-0.04770145,0.13330826,-0.00947698,-0.01091113,-0.00545262,0.04661531,0.06170501,-0.00867762,-0.0068167,-0.02542482,-0.01008721,0.03958903,0.01927456,0.00368564,0.03780851,-0.06279027,-0.01791959,0.01360357,-0.0131607,-0.00919293,0.01984275,-0.00269337,-0.01655414,0.02799254,0.01778932,-0.02391623,-0.03101095,-0.02432781,0.03534022,0.07776079,0.03581674,-0.01716747,0.00299495,0.01515082,-0.15688944,-0.03984779,-0.04924249,-0.02656485,0.0331751,-0.00572927,-0.01358027,-0.00128921,-0.02690234,-0.02401172,0.04838302,-0.10348616,-0.05472151,0.06135878,0.00512555,-0.01584242,0.02011153,0.05335784,-0.01179528,0.00278529,-0.03948466,-0.07430994,-0.04259329,-0.01736855,0.09310256,0.07013928,-0.04250703,-0.01115722,-0.05894399,-0.06566733,-0.0144376,0.08437584,0.00923976,-0.06708942,-0.04123766,0.01104038,-0.02924416,-0.11665086,-0.02735877,-0.02121014,-0.05679586,0.00544445,0.07414557,-0.00389327,-0.03329011,-0.08146623,-0.06851433,0.02300415,0.03815456,-0.03178471,-0.02615443,-0.0177054,-0.05345241,-0.07718341,0.00373808,-0.04209275,0.07604701,0.01406591,-0.05174177,0.0250542,-0.03360686,-0.02598681,-0.02746809,-0.01713271,-0.07346879,-0.03371302,0.06095223,-0.02840856,-0.04227293,0.01528926,0.039031,0.04854697,-0.05387637,0.03020325,0.01650754,-0.03663654,0.08748764,0.00460427,-0.05000935,0.0068847,0.0163781,0.05039629,-0.04120887,0.03748191,0.01934573,0.02576328,-0.0057894,0.04743178,-0.00597017,0.04873506,-0.0677756,-0.16303053,-0.00509539,-0.07728502,-0.02253097,-0.01994074,0.01585994,0.02807648,-0.00996661,0.02836831,0.00282876,0.08874788,-0.07329923,0.03124702,0.05780415,0.00542009,-0.01008609,-0.07895587,-0.04630882,-0.03768819,0.0607603,-0.01668842,-0.00106696,-0.03219523,-0.03573864,-0.01451125,0.01473891,0.12210871,0.07518528,0.01822576,0.00005398,0.04597221,0.03991722,-0.03668446,-0.1132537,0.00698006,0.00425774,-0.01280143,-0.02277616,-0.05830475,-0.00269516,-0.09145582,0.02944576,-0.01864304,-0.05267665,-0.04128952,0.02276887,0.01994207,0.04654611,0.00138758,0.0446079,0.06302866,0.05959899,0.02637119,0.04290089,0.07483704,-0.02193783,-0.06442346,0.01518212,-0.01842158,0.0289855,-0.02682259,0.00566531,0.04865622,-0.02981702,0.04786497,0.00340067,0.00726729,-0.05650356,0.04793627,0.003202,-0.00999309,0.08542293,-0.02182557,-0.02665864,-0.01558133,-0.00407487,0.05564393,-0.08808281,0.00725376,0.01088397,0.02206999,-0.08408685,0.04255787,0.0626716,0.07857513,0.0027336,0.01168337,0.03128909,0.03370187,-0.01726996,-0.02257389,0.05411993,-0.03018688,0.01565199,0.0112083,0.00921671,-0.27519798,0.02918653,0.00043393,0.06126807,0.00756348,0.02008816,0.06363824,0.01237321,-0.02230056,-0.00921792,0.06547748,0.00716756,-0.00213192,-0.02849537,-0.03750107,-0.02581553,0.04110419,-0.04615732,0.08918612,0.01382466,0.08130886,0.01778517,0.26215446,-0.02370907,0.02698954,0.00035609,0.02364745,-0.00941768,-0.00180929,0.08619637,-0.00111108,0.00145863,0.07742106,0.02234194,-0.02083771,-0.00761939,0.00930285,0.01937494,-0.00943033,0.00578142,-0.07473835,-0.01906598,-0.00119665,0.04442438,0.11209939,0.01820055,-0.00342424,-0.07902241,0.00435307,0.07105052,-0.12187627,-0.065439,-0.04900686,-0.00399225,0.02028077,0.04732889,0.05313214,0.00630778,0.02706767,0.01750243,0.03552224,0.01940468,0.06318701,0.01601686,0.02171322],"last_embed":{"hash":"zc8hx","tokens":248}}},"text":null,"length":0,"last_read":{"hash":"zc8hx","at":1753423593795},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#By Ion Saliu, _Founder of Lotto Mathematics, Lottery Programming Science_#I. [Lottery Strategy Head Start](https://forums.saliu.com/lottery-strategies-start.html#strategy)#{1}","lines":[26,32],"size":603,"outlinks":[{"title":"Software to Create Reports of Lotto Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#reports","line":1},{"title":"Procrastination: Enemy of Lottery Strategies","target":"https://forums.saliu.com/lottery-strategies-start.html#procrastinate","line":2},{"title":"Software to Manage Lottery Data Files","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":3},{"title":"My Experience with Lottery Strategies (2013)","target":"https://forums.saliu.com/lottery-strategies-start.html#experience","line":4},{"title":"It is important to start working with lottery software to discover good playing strategies.","target":"https://forums.saliu.com/HLINE.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0671777,-0.03502592,0.03367257,0.01209542,-0.05313035,0.08211812,0.07817194,-0.00842852,0.05358291,-0.02112079,0.02228704,0.01030734,0.02035845,0.0131608,-0.04340193,-0.00471983,-0.03789456,-0.0476685,-0.03541359,0.01500575,0.09909903,-0.0429936,-0.00863395,-0.05728794,0.07006434,0.03545646,-0.02687482,-0.0764055,-0.05192706,-0.20960096,0.01539548,0.00052791,-0.00460765,-0.02857819,-0.09220988,-0.01306354,-0.05538143,0.09962426,-0.02317735,0.09449431,0.02307138,-0.00970369,0.0163781,-0.021012,-0.01605063,-0.0147268,0.01358849,0.03544123,-0.00518072,0.01349725,-0.05909692,0.00315698,-0.0252296,-0.00145529,0.10008622,0.00798332,0.07446419,0.09282361,0.08233641,0.02414776,0.07088827,0.05656822,-0.19554707,0.07196811,0.00198917,0.04236292,-0.02156871,0.04465516,-0.02972455,0.05012629,0.01004584,0.04994677,-0.02814561,0.03895701,0.07482757,-0.03285564,-0.06266832,-0.04039447,-0.05391619,0.02798159,-0.01690023,0.01114135,-0.03510958,-0.03300267,-0.05728026,0.03799779,0.03456585,0.0200795,0.05361478,-0.0362728,0.01531926,0.01857777,0.00752915,0.02317497,0.00534482,-0.00380399,0.02707274,0.0242765,-0.02897948,0.0915954,0.01799608,0.01990473,0.01169664,0.04273923,0.05368011,-0.03710504,-0.04107614,-0.04621682,-0.02115073,0.04109259,0.01494296,0.00245561,0.05991065,-0.04162576,-0.01108319,0.01734245,-0.00812578,0.02252905,0.03939741,-0.01159272,-0.0164337,0.03848989,-0.00768012,-0.04030705,-0.00810214,0.0087285,0.03857182,0.10496154,0.03640695,-0.04231534,0.00524003,0.01739244,-0.15792347,-0.04101713,-0.05652275,-0.01731019,0.01984102,0.01260603,0.01587923,-0.0063729,-0.02784836,-0.02332644,0.03483488,-0.11246561,-0.04380662,0.06086465,0.03107325,-0.01085271,0.05070736,0.048935,0.00471513,-0.01232294,-0.07010463,-0.04240759,-0.03730901,-0.0091782,0.04576761,0.07875876,-0.03506741,-0.02942691,-0.0659181,-0.04072203,-0.03655891,0.10451078,0.00497422,-0.0934753,-0.046542,-0.02318209,-0.03705006,-0.11081777,-0.02073974,-0.01622563,-0.06262048,0.00453952,0.08380715,-0.00737253,-0.10260841,-0.04762464,-0.06495555,0.0282861,0.02685139,-0.04520375,-0.06985519,-0.00676185,-0.04108113,-0.08843148,0.02373029,-0.02857277,0.05771809,0.05219418,-0.01242591,0.0310692,-0.02778651,0.0178733,-0.0086709,-0.0285403,-0.05541337,-0.0404561,0.06208131,-0.03739988,-0.05501598,0.00000688,0.04050172,0.05145193,-0.02832454,0.03085935,0.03176722,-0.03708376,0.09507698,0.00815003,-0.0530769,-0.00795911,0.00141166,0.05852469,-0.02006909,0.02760721,0.01765338,-0.00176956,0.00825065,0.02522442,0.02255497,0.02137978,-0.04276546,-0.1684666,-0.02422344,-0.06065494,-0.02895571,0.00226451,0.03228435,0.00954433,-0.01071543,0.05485783,-0.00458149,0.02412362,-0.05869828,0.00827313,0.05006517,-0.0142417,-0.01476982,-0.07641749,-0.0272536,-0.0571614,0.04308212,-0.01528578,0.01708928,-0.06164125,-0.04175733,0.0187611,-0.03123049,0.14050664,0.05300723,0.01846306,-0.01480038,0.04957833,0.02737161,-0.01590604,-0.04476018,0.02293082,0.02522523,-0.00617369,-0.02886518,-0.09589225,0.00724632,-0.04718837,-0.00617446,-0.03329764,-0.08260996,-0.0689685,0.02228664,-0.03330829,0.07245132,0.00794442,0.03162319,0.05734217,-0.01036654,0.04103576,0.03213879,0.02903347,-0.02152491,-0.05015583,-0.00667485,0.03308297,0.06913875,-0.01586864,0.01165673,-0.00400112,-0.01715669,0.03022451,0.03596597,0.01352891,-0.04884294,0.03334392,-0.02886511,-0.00750578,0.08221038,0.00519487,-0.00718507,-0.0120508,0.00200317,0.03934398,-0.0453331,0.02339618,0.01575074,0.03513891,-0.04687811,0.03056473,0.08241571,0.03086352,-0.02311556,0.0418873,0.05758978,0.04667426,-0.03451556,-0.06124249,0.03499934,-0.06189858,-0.01169208,-0.00986221,-0.01383094,-0.2618528,0.06867238,-0.01729565,0.08641206,0.00514172,0.00299161,0.03429244,0.04714511,0.02038357,-0.03030307,0.06464475,0.05095665,0.02416707,-0.0104682,-0.03029627,-0.04791221,0.0235854,-0.0318884,0.06799345,-0.01156772,0.08896646,0.043899,0.24645281,-0.00750977,0.02695366,-0.02830355,0.02300377,0.01111904,-0.02082918,0.03611536,-0.00596076,0.00781572,0.07794725,-0.01657092,-0.00823645,-0.04124243,0.01094565,0.04383127,-0.02259983,0.02135177,-0.08052208,-0.01609949,-0.00421232,0.02107365,0.14999768,0.04958224,-0.02160243,-0.0996299,0.0264236,0.09045313,-0.09902241,-0.05519347,-0.03639485,-0.03913775,-0.02615638,0.04353746,0.00725484,0.00578647,-0.00038668,0.01280169,0.00043954,0.01843949,0.06587715,-0.00401158,0.00837839],"last_embed":{"hash":"10qifjy","tokens":436}}},"text":null,"length":0,"last_read":{"hash":"10qifjy","at":1753423593883},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>","lines":[33,124],"size":8149,"outlinks":[{"title":"The report shows the filters for the pick 3 lottery game.","target":"https://forums.saliu.com/lotto-filter.gif","line":13},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":60},{"title":"The checking of the lotto strategy is a neat report.","target":"https://forums.saliu.com/strategy-1.gif","line":70},{"title":"The next report shows the filters in MDIEditor and Lotto.","target":"https://forums.saliu.com/strategy-2.gif","line":72},{"title":" The reports are valuable tools to find new lotto strategies to win the lottery.","target":"https://forums.saliu.com/strategy-3.gif","line":74},{"title":"The reports are similar for every layer of the lotto data file of drawings.","target":"https://forums.saliu.com/strategy-4.gif","line":76},{"title":"The lotto pairs are essential in creating lottery strategies.","target":"https://forums.saliu.com/lotto-pairs.gif","line":86},{"title":"Sorted MD6 Winning Report","target":"https://saliu.com/freeware/MD6.1-SOR","line":90},{"title":"Lotto 649 Strategy Ver6\\_1=1000","target":"https://saliu.com/freeware/st6.rep","line":91}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06937943,-0.0278877,0.03134374,-0.01480402,-0.0240296,0.06006993,0.10225163,0.01885958,0.06757901,-0.03192797,0.02736044,-0.0090039,0.03457101,0.03247742,-0.02221201,-0.02835753,-0.03130103,-0.03767148,-0.04632809,-0.00410813,0.10411859,-0.03277823,-0.02099991,-0.05204774,0.07335275,0.02550775,-0.01369051,-0.04952073,-0.03737397,-0.15928857,0.01622209,-0.01511418,-0.00925073,-0.0306203,-0.09909416,-0.02129182,-0.0379587,0.10310981,-0.03697442,0.09496079,-0.00273134,-0.01668408,0.00367209,-0.00343752,-0.01241873,-0.04001419,0.01493024,0.04617968,-0.00175279,0.00029885,-0.07204132,0.00512788,-0.04719737,-0.01739236,0.0951466,0.00942263,0.07886606,0.08737439,0.08104552,0.00453572,0.04225636,0.05917045,-0.20211887,0.03162622,-0.02550044,0.02122247,0.00790203,0.05978577,-0.03345311,0.05770206,0.06359252,0.03600215,-0.00217517,0.05264026,0.03590708,-0.02929764,-0.05651937,-0.04966701,-0.05052634,0.04875319,-0.00331458,-0.00848085,-0.01587417,-0.02878892,-0.06241294,0.03598975,0.02791181,0.00597849,0.06261856,-0.05084677,0.00323382,0.03961445,-0.01975345,0.01905234,-0.01880236,-0.03401534,0.00077778,-0.00392938,-0.02868772,0.11891774,-0.00324266,-0.00732012,0.00727094,0.0358662,0.06890594,-0.01874021,-0.03273158,-0.0440642,-0.01260928,0.03964316,0.00957893,0.03159324,0.05411454,-0.0557391,0.00481558,0.02571551,-0.00596937,0.0206445,0.02506081,-0.02230413,-0.02653949,0.04031138,-0.00283077,-0.01988501,-0.03288982,-0.01889583,0.05691848,0.09197687,0.03035454,-0.0384017,-0.03096588,0.02007218,-0.1368541,-0.04735375,-0.04226114,0.01466344,0.03476075,-0.01000437,0.00145719,0.01575676,-0.01359007,-0.02079198,0.0266025,-0.10726322,-0.0218755,0.03465178,0.03698642,-0.01773102,0.03538281,0.05447759,-0.00633106,-0.00501902,-0.03916318,-0.04245764,-0.02599879,-0.04625194,0.04378786,0.06457369,-0.01672425,-0.05056629,-0.07298446,-0.05368989,-0.03711351,0.08643685,-0.01646183,-0.09733865,-0.0306341,-0.0181444,-0.01620215,-0.13807437,-0.01725369,-0.0131737,-0.0654188,0.00223323,0.07516159,-0.00404988,-0.07685029,-0.06351085,-0.09702011,0.01314302,0.04956364,-0.02961561,-0.06835688,-0.00025665,-0.03233847,-0.06563466,0.00596265,-0.04857919,0.07074244,0.02144591,-0.02017558,0.04491546,-0.05413667,-0.02005195,0.00283439,-0.01366881,-0.06727254,-0.02450417,0.07130931,-0.03361433,-0.0459698,0.00562589,0.01718579,0.06377471,-0.02003315,0.02560619,0.02310983,-0.05822852,0.08117862,0.01019644,-0.05459123,0.00112045,0.03503435,0.05528644,-0.01935959,0.00657988,0.02233165,0.01662519,-0.01657473,0.03537055,0.01095284,0.03464172,-0.04489406,-0.16456787,-0.0165381,-0.05876989,-0.0194389,-0.00961309,0.03201191,0.04261324,-0.02131983,0.06425915,0.00485463,0.04773358,-0.08009137,0.0261433,0.06322385,-0.00183006,-0.01672547,-0.10682502,-0.02946089,-0.03088094,0.05843141,-0.00048058,0.02324448,-0.07471874,-0.06596597,0.00376067,0.01292804,0.15184438,0.07227373,-0.01779727,-0.02961617,0.0609169,0.0442042,-0.00289656,-0.10444929,0.02061429,0.00161088,0.03140815,-0.05887355,-0.0806253,0.00909883,-0.06984677,0.0262116,-0.03628312,-0.07669453,-0.04758666,0.0135837,-0.02168047,0.07207343,0.01336242,0.05250389,0.04908547,-0.00054796,0.06257063,0.05572227,0.05536108,-0.02399142,-0.06000008,-0.00296229,0.00814863,0.03128283,-0.00243882,-0.00655746,0.01275927,-0.0260209,0.05425642,0.02207753,0.02263065,-0.03509218,0.04431927,0.00039371,-0.03953518,0.05607668,0.00525172,-0.03358141,-0.00023818,-0.00037595,0.04564425,-0.05661029,0.00297528,0.01408711,0.051493,-0.06276961,0.04524778,0.05672303,0.02494024,-0.01073858,0.03158873,0.03164285,0.02665289,-0.01671522,-0.06609762,0.04103383,-0.03650863,-0.00784852,0.02174946,-0.0066546,-0.26274762,0.04434267,-0.02135909,0.08973563,0.01128377,0.0130085,0.04494721,0.05100981,0.0049025,-0.01860091,0.05856309,0.0464789,0.00898473,-0.00271652,-0.03100258,-0.03632624,0.05015064,-0.03937717,0.07811267,0.01396601,0.08133241,0.03773084,0.26539251,-0.02443243,0.04217582,-0.02875431,0.01437096,-0.00682331,-0.01693018,0.03453623,-0.0146895,0.01907153,0.06225165,-0.00580119,-0.00669814,-0.01645948,0.0310588,0.04485352,-0.00370363,0.02278617,-0.07044474,-0.00214523,0.00133356,0.0383426,0.12345666,0.02641091,-0.01504811,-0.10843642,0.01304905,0.05234708,-0.11015663,-0.05278477,-0.02767138,-0.05270496,-0.01373564,0.03457506,0.03322291,0.00181285,0.01334592,0.02934303,0.02971581,0.03405775,0.03762624,0.01278185,-0.01925421],"last_embed":{"hash":"1186trg","tokens":150}}},"text":null,"length":0,"last_read":{"hash":"1186trg","at":1753423594060},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{1}","lines":[35,38],"size":417,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06959605,-0.04367502,-0.00295244,-0.00348194,-0.07231145,0.1046875,0.06092664,-0.01798656,0.05877324,-0.02997068,0.03038245,-0.00317129,0.02093709,-0.00119366,-0.03970675,-0.03600948,-0.0157951,-0.02912136,-0.04553055,0.00429907,0.09934901,-0.05274642,-0.02302823,-0.04957788,0.08502188,0.02254473,-0.00569864,-0.03884736,-0.05002296,-0.21001293,0.0319992,0.00831741,-0.00320445,-0.0244713,-0.08621673,-0.01143494,-0.04173071,0.11359122,0.00438621,0.10273568,0.01750061,-0.01076076,0.03535037,-0.02136331,-0.02036784,-0.01291139,-0.00827368,0.02122836,0.01472923,0.01440075,-0.06054296,-0.0232219,-0.00783212,0.00843979,0.08027129,0.0198864,0.08539403,0.09642579,0.06974614,0.0304655,0.06574075,0.07467902,-0.19345953,0.05489341,0.00744443,0.02750463,-0.02483406,0.02970433,-0.02064411,0.04840257,0.02115864,0.03831022,-0.03530974,0.03454234,0.05271795,-0.03076399,-0.07457706,-0.04236983,-0.03565987,0.03462259,-0.05350171,-0.02351442,-0.03049392,-0.01935408,-0.02415333,0.04150021,0.01442868,0.030832,0.03959163,-0.05007802,0.02766215,0.01853608,-0.03958948,0.00540157,-0.00332607,0.02137109,0.03525744,-0.02177617,-0.01585409,0.10011659,0.03589562,0.04090732,-0.00238637,0.04666762,0.03805489,-0.02976594,-0.04140992,-0.05146529,-0.02243775,0.04921675,0.02567134,-0.0053719,0.06313184,-0.04351723,-0.01499759,0.00819873,-0.00377308,0.01223958,0.03669245,-0.00329848,-0.01585767,0.03271845,-0.02014765,-0.05413017,-0.01062124,0.02452758,0.04492787,0.10458477,0.04905028,-0.02012366,0.01827786,0.02371383,-0.15863484,-0.05000001,-0.01576563,-0.0167006,0.02697311,0.02540215,0.00738956,0.01102932,-0.01628684,-0.03511252,0.04714149,-0.11991464,-0.03658926,0.07572591,0.02372222,0.02458326,0.03640725,0.03227695,0.00612383,-0.01018797,-0.06612105,-0.05592145,-0.03171029,0.01214756,0.02266352,0.06813838,-0.02860443,-0.02194808,-0.04358154,-0.06647293,-0.02707838,0.08887883,0.00325237,-0.05649336,-0.03937264,-0.02303787,-0.03094787,-0.11002777,-0.01197291,-0.00457368,-0.05799614,0.01103126,0.07335497,-0.00660672,-0.09359507,-0.07253586,-0.02296803,0.02683585,0.03619607,-0.01058348,-0.07158287,-0.00805508,-0.0545162,-0.07492964,0.00899732,-0.02028872,0.06340453,0.05692219,-0.02325939,0.03604181,-0.002448,0.01321599,-0.00905871,-0.04248083,-0.05003937,-0.02668999,0.07577977,-0.02636558,0.00328845,0.00217837,0.04488946,0.04187403,-0.03440245,0.03699028,0.0397345,-0.04641837,0.08791142,0.00351614,-0.03833607,-0.0026419,-0.01996093,0.06199844,-0.01874462,0.03175659,0.02462894,0.00083035,0.01744277,0.01121533,0.00901732,0.00237916,-0.03438675,-0.16533749,-0.03402127,-0.0494383,-0.01022626,0.01465341,0.03394069,-0.0019119,-0.01030432,0.04095183,0.03149928,0.05456956,-0.04480187,-0.00258775,0.0610691,-0.01434554,-0.00402366,-0.0958664,-0.02120095,-0.04404159,0.03883486,0.00232568,0.02423332,-0.07826116,-0.06045992,0.02207659,-0.0141319,0.13962948,0.03114779,-0.01490665,-0.00227161,0.04079321,0.01869984,-0.02909007,-0.02819228,0.01185264,0.01360045,-0.03441364,-0.01735603,-0.09646589,0.01223439,-0.04744847,-0.01239841,-0.03395389,-0.07022526,-0.06487104,0.02749379,-0.02233391,0.07745577,-0.01210507,0.02925255,0.03793399,-0.00025024,0.03567737,0.02622559,0.05312781,-0.03203473,-0.06808677,-0.00309212,0.0231092,0.03737007,-0.00835898,-0.01605057,0.04022415,-0.00631502,0.02258222,0.0228915,0.01979812,-0.04990616,0.0246074,-0.02824442,0.00846555,0.06939527,0.01715476,-0.01655706,-0.02368756,0.00280831,0.04084431,-0.04262631,0.00646897,0.00072748,0.04975605,-0.06037424,0.01839585,0.06894107,0.04011527,-0.02528359,0.02801758,0.04552263,0.02669136,-0.02861221,-0.02992908,0.0531303,-0.05141678,0.00150281,-0.02736289,-0.03207125,-0.27376437,0.04897047,-0.06398732,0.08607273,-0.00649855,0.01985888,0.02115501,0.07063643,0.02667582,-0.05194967,0.04613799,0.03947616,0.01360512,-0.04434776,-0.02308051,-0.04032465,0.01926072,-0.04070747,0.05449792,-0.0258769,0.11797345,0.01567214,0.25046399,-0.01507913,0.01182345,-0.02980567,0.01102058,0.0111823,-0.0333555,0.04691946,-0.02134905,0.00662117,0.08726427,-0.0112702,-0.00486482,-0.02808941,0.01059528,0.04394501,-0.01881561,0.01157828,-0.09495784,-0.01364925,-0.02340712,0.02410866,0.14516053,0.03409865,-0.00738379,-0.10411564,0.01161528,0.12568232,-0.10029712,-0.04932965,-0.04102797,-0.03833879,-0.03442056,0.07024904,0.00029252,0.02662935,0.00744913,0.04038066,0.02359216,0.02477492,0.07126629,0.02276207,-0.00841023],"last_embed":{"hash":"sp8597","tokens":440}}},"text":null,"length":0,"last_read":{"hash":"sp8597","at":1753423594128},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{3}","lines":[41,91],"size":3713,"outlinks":[{"title":"The report shows the filters for the pick 3 lottery game.","target":"https://forums.saliu.com/lotto-filter.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08065998,-0.0066028,-0.00743759,-0.04414185,-0.0139581,0.05044563,0.01157803,0.0061965,0.03611682,-0.04581633,0.03353231,0.01933827,0.0556103,-0.00757201,-0.00382972,-0.01577711,0.00716718,-0.05083952,-0.03162307,-0.02634871,0.07453825,-0.02858547,-0.0399581,-0.09113134,0.05957782,0.02042672,-0.00491129,-0.04655577,-0.03867331,-0.25205317,0.0320812,-0.00385596,0.03154259,-0.06840543,-0.09918559,0.00009967,-0.02401345,0.1046655,-0.02618453,0.02634931,0.03283924,-0.01543326,0.02166104,0.00343784,-0.01397784,-0.01934871,0.01980876,-0.00772484,-0.00342296,-0.00341055,-0.07110109,-0.02594145,0.01568284,0.01124663,0.06061582,0.03437407,0.05380796,0.10857454,0.0568013,0.01275422,0.04782999,0.09734707,-0.1855118,0.05678735,-0.04017672,0.00899085,-0.01245724,-0.01177873,-0.02761434,0.050508,0.03143458,0.04778929,-0.03432691,0.05040004,0.0313307,-0.05022251,-0.03837953,-0.05059659,-0.02153061,0.0217792,-0.02637542,-0.04387581,-0.00033947,-0.00663166,-0.01727603,0.05556337,0.01648382,0.04807257,0.05006642,-0.05455832,0.06249087,0.04628895,-0.00582189,0.03558349,-0.00976244,0.01477188,0.03507841,-0.01006827,-0.0315614,0.11263644,0.02865817,0.02365521,0.02328728,0.00323948,0.04955375,-0.02892693,-0.01841249,-0.03403929,-0.03514651,0.03968013,0.03034695,0.00173009,0.0498567,-0.06277732,0.00306136,-0.00070048,-0.01169969,0.0014482,0.00565661,0.00857754,-0.03156145,0.01012625,-0.00001101,-0.01689504,0.00086073,0.00597644,0.03427063,0.09469019,0.02784386,-0.00297805,-0.0020223,0.01987925,-0.13835466,-0.02938789,-0.00996909,-0.00606178,0.048106,-0.01926569,-0.01168678,0.01058119,-0.06509212,0.02321013,0.05926201,-0.11537395,-0.00089205,0.0818277,0.02017851,0.01123862,-0.00979732,-0.01046036,0.0070454,0.00498873,-0.05516696,-0.03747494,-0.01265796,0.00193475,0.05169399,0.09645168,-0.04418874,-0.05840195,-0.05441776,-0.02957488,-0.02021538,0.09131214,-0.00905142,-0.08025224,-0.02653435,0.01803273,-0.05180764,-0.11303672,-0.04770396,0.008932,-0.04611425,-0.00544662,0.05563861,-0.0393218,-0.09985597,-0.05353039,-0.05431165,-0.01780873,0.04496218,0.01495968,-0.07423601,0.00545905,-0.03655468,-0.09607993,0.04628359,0.00540872,0.0402368,0.03937877,-0.04452292,-0.0051348,-0.01508212,0.02267339,-0.02430467,-0.01940108,-0.04774297,-0.02499305,0.07279811,-0.0332826,0.01132038,-0.00974007,0.02126739,0.03089132,-0.02900546,0.04389058,-0.00344336,-0.05294867,0.1099809,0.01393871,-0.08226825,0.02081146,0.05677468,0.07163161,-0.06345683,0.02412749,-0.00022215,0.03236353,-0.01651673,0.00042826,0.02511377,0.04672714,-0.0379145,-0.18795648,-0.02870696,-0.0478753,0.00869309,0.00461509,-0.00498851,0.01371887,-0.02997421,0.04629903,0.0600592,0.06720002,-0.04785014,0.00711616,0.04538073,-0.01782244,-0.03130592,-0.10313765,-0.04792291,-0.06321403,0.03325066,0.03654033,0.02219146,-0.03398275,-0.04597364,0.01268882,-0.02444948,0.13761252,0.01490837,0.01717691,-0.01637862,0.06199335,0.00486073,-0.0140235,-0.04399383,-0.00144644,0.02306035,-0.00743604,-0.00964515,-0.03918541,-0.00351914,-0.07670651,0.01855377,-0.04239428,-0.11022823,-0.03332041,0.04907546,-0.01164811,0.06695662,-0.03745325,0.02441395,0.02706514,0.00714175,0.01825725,0.05970094,0.07252764,-0.0646288,-0.08334393,0.00531442,-0.00483534,0.01575322,-0.02213429,-0.0404457,0.05061784,-0.02840545,0.04567834,0.0242142,0.01879577,-0.0402302,0.03391604,0.00175789,-0.02895445,0.07569564,-0.04965477,0.03648709,-0.02473216,0.02503937,0.05065259,-0.04479429,0.00308191,-0.01009507,0.06586161,-0.04117625,0.01755806,0.07070695,0.04962831,0.00425134,0.05200544,0.01383144,0.02890439,-0.01742437,-0.01855537,0.01659587,-0.0165916,0.01198859,0.00414971,0.01002247,-0.24585755,0.01982049,-0.03003474,0.06977832,-0.0481248,-0.0139706,0.06963757,0.02048962,0.02752284,-0.04395423,0.02580462,0.05336202,0.01055802,-0.04691791,-0.01560211,-0.05529212,0.02358513,0.01613725,0.07976775,-0.00618846,0.11340917,0.0227433,0.26265717,-0.01574806,-0.01358748,0.01071012,0.0189237,-0.03110033,-0.04060787,0.04666113,-0.01290209,0.03014236,0.09720194,-0.02710179,-0.00059423,-0.00067555,-0.00320707,0.03396224,-0.00911696,0.02497835,-0.04596156,0.00943999,-0.03222946,0.01731065,0.13885355,0.03276044,0.02549275,-0.08926652,0.01032436,0.09359276,-0.10276821,-0.06283121,-0.04970566,-0.03357559,0.0068547,0.04506239,0.030005,0.03302587,0.03440687,0.01978042,0.04617975,0.00448025,0.06806365,0.02596403,-0.01884848],"last_embed":{"hash":"1nek2od","tokens":443}}},"text":null,"length":0,"last_read":{"hash":"1nek2od","at":1753423594290},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>1. Head Start to Lottery Strategies</u>#{5}","lines":[94,121],"size":3563,"outlinks":[{"title":"The checking of the lotto strategy is a neat report.","target":"https://forums.saliu.com/strategy-1.gif","line":9},{"title":"The next report shows the filters in MDIEditor and Lotto.","target":"https://forums.saliu.com/strategy-2.gif","line":11},{"title":" The reports are valuable tools to find new lotto strategies to win the lottery.","target":"https://forums.saliu.com/strategy-3.gif","line":13},{"title":"The reports are similar for every layer of the lotto data file of drawings.","target":"https://forums.saliu.com/strategy-4.gif","line":15},{"title":"The lotto pairs are essential in creating lottery strategies.","target":"https://forums.saliu.com/lotto-pairs.gif","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05066495,-0.02517578,-0.00238929,-0.02650881,-0.04075822,0.08399252,0.06016131,-0.01592085,0.04700582,-0.0235883,0.03087494,0.01366989,0.0633718,0.01348797,-0.0208261,-0.03222805,-0.00347452,-0.04832517,-0.06061149,-0.02262107,0.09795674,-0.03788235,-0.0204365,-0.08875518,0.07929794,0.01281288,0.01016234,-0.05332923,-0.03886405,-0.22036014,0.00843372,-0.00014231,-0.01228944,-0.05046264,-0.10073798,-0.04758457,-0.02196741,0.1228918,-0.03695499,0.06173357,0.02259066,-0.02250625,0.013035,-0.02655355,-0.01884421,0.00240491,0.02331496,-0.02301013,0.03635143,-0.00269816,-0.07332704,0.00720765,0.01601519,-0.00257554,0.0682968,0.01396138,0.04631894,0.10111792,0.03594664,0.0024494,0.03222391,0.06334379,-0.18377908,0.05232575,-0.05634138,0.04373261,-0.00505499,0.01134535,-0.03671987,0.09012265,0.06899031,0.04473544,-0.03822784,0.0464252,0.03606117,-0.03891939,-0.05848493,-0.03192837,-0.03532054,0.03884318,-0.0565692,-0.0271391,-0.01620355,-0.02157144,-0.0094439,0.04371906,0.01810366,0.05015479,0.05502532,-0.05304048,0.06150918,0.0239376,-0.03239093,0.04314071,0.01140841,0.02176055,0.01859593,-0.02988783,-0.03274832,0.1214697,0.03282519,0.02942573,0.00762946,0.03620762,0.04581861,-0.04947587,-0.03702938,-0.02907444,-0.01597896,0.04632298,0.01786234,-0.01467301,0.08536793,-0.04307293,-0.04315168,0.04109341,0.02087371,0.01370298,0.02186945,0.0045875,-0.02407435,-0.00123471,-0.010041,-0.03866975,-0.01454377,0.03464548,0.04065177,0.0856603,0.03904277,0.00584746,-0.00389033,-0.01773363,-0.14007889,-0.03844222,-0.04453593,-0.02022033,0.02128464,0.02147718,-0.0115883,-0.0016981,-0.03964984,-0.01770425,0.0447698,-0.11631522,-0.0487949,0.07076798,0.01290062,0.00282646,0.00546636,-0.0180809,0.0001288,-0.00422553,-0.04620491,-0.07028119,-0.0280006,-0.00645515,0.03490705,0.09640267,-0.05713703,-0.02648827,-0.0501895,-0.05391044,-0.03256531,0.06844905,0.01704485,-0.06018431,-0.04148401,-0.01928102,-0.06147152,-0.10984134,-0.04340074,-0.00979812,-0.07030485,-0.0164598,0.08341805,-0.00893287,-0.08170107,-0.08105727,-0.06578895,-0.0001007,0.00783555,-0.00633853,-0.04642859,0.00192743,-0.04399129,-0.08747276,0.03828469,-0.02953075,0.07458302,0.04184163,-0.04690811,-0.00372866,0.00777122,0.02314889,-0.0064958,-0.03418799,-0.04123986,-0.03092116,0.06012871,-0.03344251,0.01204462,-0.00186061,0.02820431,0.04824493,-0.05944771,0.03474396,0.0096649,-0.04446125,0.13367733,0.01657476,-0.06438564,0.00158932,0.03074793,0.04774868,0.00175519,0.007563,-0.00504648,0.02061147,-0.00405466,-0.01294914,0.00511628,0.06133697,-0.03010834,-0.18016379,-0.02409227,-0.05567977,0.02453717,0.00806285,-0.01010452,0.0087164,-0.02367111,0.03810174,0.06537972,0.08188757,-0.04548747,0.00323375,0.08477278,-0.00218147,0.00898113,-0.10379815,-0.02754815,-0.0248289,0.05668664,0.02855102,-0.00322154,-0.02762192,-0.04046807,0.01978534,-0.01317792,0.14028767,0.02951817,0.0111433,-0.00849687,0.04606772,0.04161194,0.00648655,-0.01167757,-0.00646438,0.03259027,-0.03160102,-0.03093819,-0.0649126,-0.00429966,-0.05709741,0.01636285,-0.04632509,-0.1078422,-0.04653112,0.04211389,-0.00969124,0.0897248,-0.02808961,0.02830124,0.04516906,-0.01399416,0.0406772,0.03390268,0.09113611,-0.03420264,-0.07273164,-0.02000243,0.01454962,0.01169827,-0.0222275,-0.01890546,0.04912461,-0.03255132,0.03377615,0.02461431,0.01926776,-0.02961107,0.05079427,0.00800477,-0.00946053,0.05626612,-0.02933574,0.00530387,-0.04417799,0.04492144,0.04914499,-0.04861363,-0.00126169,-0.01516235,0.06732978,-0.04602865,0.02997957,0.04966947,0.03957185,0.03216563,0.0430371,0.0149106,0.02861212,-0.02085106,-0.03256255,0.05617249,-0.02671682,0.01411951,0.00112252,-0.02370565,-0.24818833,0.0178437,-0.03771789,0.06147308,-0.0043588,-0.02136542,0.04969081,0.04687003,0.00933025,-0.04561744,0.05564683,0.04782961,0.00052485,-0.04806832,-0.0384739,-0.04483727,0.01185353,-0.02501687,0.08944777,0.00468338,0.1381246,0.01167964,0.24479644,-0.00187966,0.00071078,-0.01708534,0.01277669,-0.02599609,-0.04841868,0.04284536,-0.01670872,0.02324875,0.09549814,0.00942646,-0.02084257,-0.00766559,-0.01019442,0.04744342,-0.00687094,-0.00223463,-0.06075009,0.00582495,0.0032603,0.00329226,0.10872149,0.00888768,-0.00435938,-0.08117319,0.00039604,0.08328297,-0.0984519,-0.04132375,-0.03295929,-0.03543996,0.01981092,0.06438987,0.03610579,-0.00188795,0.02364504,0.0265871,0.02842591,0.0288475,0.06281961,0.01236771,0.01220364],"last_embed":{"hash":"qu00oe","tokens":493}}},"text":null,"length":0,"last_read":{"hash":"qu00oe","at":1753423594473},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>","lines":[125,143],"size":1801,"outlinks":[{"title":"We must check any lottery strategies against past drawings.","target":"https://forums.saliu.com/check-strategy-1.gif","line":8},{"title":"Back-testing a lottery strategy is done layer by layer in the draw file.","target":"https://forums.saliu.com/check-strategy-2.gif","line":10},{"title":"The reports show different data from layer to layer, which offers more choices.","target":"https://forums.saliu.com/check-strategy-3.gif","line":12},{"title":" Procrastination is the main obstacle in winning the lottery with Ion lotto software programs.","target":"https://forums.saliu.com/check-strategy-4.gif","line":16}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02590836,-0.04939994,0.01802942,-0.05649703,-0.04186383,0.04944864,0.06400187,-0.01168261,0.03609518,-0.0192084,0.02896667,0.00899839,0.05754458,0.01341288,-0.02004946,-0.02117932,-0.00641913,-0.0534717,-0.06672922,-0.02563801,0.14053769,-0.04348801,-0.04487342,-0.07339113,0.06545223,0.01806778,0.0089002,-0.0548171,-0.029363,-0.2167488,0.02269451,0.00343552,0.01986204,-0.04125851,-0.07358346,-0.05256218,-0.01215991,0.11484149,-0.05133153,0.05790932,0.0190505,-0.01638304,0.03184902,-0.01640736,-0.03724732,-0.00875132,0.03289863,-0.01523218,0.01418202,-0.01065407,-0.04396787,0.0150351,0.01331125,0.02495821,0.04190745,0.00660875,0.02786249,0.08747243,0.03255767,0.00088988,0.0453747,0.05813627,-0.1941493,0.04727418,-0.05785942,0.03743145,0.00371113,0.0045578,-0.03453919,0.08167998,0.06417939,0.05106266,-0.02127938,0.03102481,0.02722412,-0.0326522,-0.07248331,-0.04424471,-0.03476601,0.0360837,-0.06389969,-0.03736125,-0.01876542,-0.03279522,-0.02294203,0.03595558,0.02868234,0.02502367,0.05501468,-0.02786409,0.05025077,0.02958398,-0.03529011,0.05551472,0.01105717,0.03343151,-0.00011308,-0.0163284,-0.02483094,0.13776262,0.02800415,0.0272169,0.00081698,0.00486249,0.03808684,-0.02311504,-0.03737709,-0.02582429,-0.00568674,0.05148924,0.02037838,-0.01119716,0.05599799,-0.04966063,-0.03237615,0.03485456,0.02051237,0.00968894,0.02689352,0.00382922,-0.03363533,-0.01297898,-0.00363965,-0.047877,-0.01592337,0.0606825,0.05148641,0.08960734,0.03093801,-0.00305021,-0.01531483,-0.02043034,-0.1333843,-0.02353635,-0.02319003,-0.00963955,0.04147194,0.02518898,-0.01653831,0.03158236,-0.050438,-0.00918443,0.01745702,-0.09952459,-0.04459852,0.09885923,0.00680425,0.00067175,-0.00356313,-0.02000448,-0.00062882,0.01209962,-0.04242033,-0.07578526,-0.00575078,0.0175146,0.01882222,0.06639145,-0.04234108,-0.02458869,-0.03070403,-0.05722526,-0.03419623,0.08090273,0.020997,-0.06434975,-0.04353677,-0.01817288,-0.04960739,-0.09130806,-0.0406228,-0.01599631,-0.06064533,-0.01226776,0.07469675,0.00100183,-0.0717543,-0.07171991,-0.08080196,-0.00436661,0.00912952,0.00356853,-0.06780961,-0.00043648,-0.04539948,-0.08291624,0.02216508,-0.01376269,0.0558981,0.05156149,-0.02954729,0.02757583,0.01969385,-0.00872457,0.00717656,-0.0301515,-0.03172873,-0.00852137,0.057733,-0.08448751,0.02397541,0.01842518,0.03343551,0.06278259,-0.05673475,0.02808497,-0.00348881,-0.04162517,0.1264852,0.00131262,-0.06315476,0.01210998,0.03086182,0.05034846,-0.01715106,0.01003563,-0.00289606,0.01333099,-0.00561633,-0.01254602,0.00036196,0.06241693,-0.03100788,-0.19155326,0.00072833,-0.03351503,0.03591772,0.00472377,0.00382619,0.00300736,-0.02115361,0.04244665,0.08204632,0.06696237,-0.034443,0.00165932,0.09165169,-0.00631689,0.01356126,-0.10318223,-0.02788039,-0.02174418,0.04209058,0.04413623,-0.00904806,-0.03743772,-0.05398124,0.03447438,-0.01316562,0.13294834,0.0091051,0.01681856,-0.01638573,0.02760499,0.02465487,0.02163294,0.01331065,-0.01653353,0.0264366,-0.05059782,-0.04061092,-0.05623651,0.01330988,-0.05152328,0.00977215,-0.06688932,-0.08755697,-0.03235557,0.04743039,0.01172252,0.10739175,-0.03193466,0.04473025,0.0132712,-0.02623173,0.06493343,0.03664954,0.10163705,-0.04640901,-0.07494742,-0.01519864,0.03502278,-0.00420852,-0.02260046,-0.01958786,0.0456599,-0.02884949,0.02825477,0.01265303,0.00989402,-0.03304001,0.03985803,-0.00232988,-0.0318348,0.05414862,-0.06613263,-0.00054649,-0.04936707,0.03953069,0.03471376,-0.05354075,0.00500479,-0.04105132,0.08108655,-0.05123959,0.02600809,0.05876079,0.04634979,0.03084759,0.02594502,0.03751394,0.04611841,-0.02405966,-0.04144154,0.03769332,-0.02228704,-0.00524759,0.00383081,-0.02932866,-0.24348333,0.01197225,-0.05976133,0.07616034,-0.00867818,-0.01798175,0.03726402,0.09421103,0.00004106,-0.05091321,0.03228275,0.0487123,0.00423101,-0.03223535,-0.05489024,-0.05698045,0.03302856,-0.03436923,0.08212303,0.01793079,0.14583622,0.00770058,0.23682001,0.00744611,-0.00379788,-0.01014507,0.00298293,-0.024611,-0.06714585,0.03205262,-0.00132898,0.01824968,0.12166068,-0.0035183,-0.01182774,0.00334396,-0.01540588,0.02680202,0.0095138,0.01524232,-0.05101961,0.01054118,-0.003633,-0.0086655,0.11414829,0.02730029,-0.01201337,-0.09371751,0.00401942,0.07145614,-0.11512316,-0.02070349,-0.03119495,-0.03071661,0.0142019,0.05645585,0.03835437,0.01382838,0.00800686,0.01803282,0.01883081,0.04091818,0.04903597,0.00575672,-0.00577733],"last_embed":{"hash":"4nstd9","tokens":178}}},"text":null,"length":0,"last_read":{"hash":"4nstd9","at":1753423594663},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{1}","lines":[127,128],"size":523,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06164462,0.00099453,0.01126987,-0.0022196,-0.05304522,0.09887684,0.04885441,-0.01479669,0.08349771,-0.00552944,0.04052256,0.02466941,0.07652269,0.01402292,-0.03963653,-0.03347489,-0.01446894,-0.0370111,-0.06106875,-0.00295806,0.077356,-0.0259085,-0.02177704,-0.07141976,0.08642067,0.02395234,-0.02248963,-0.04557876,-0.0502469,-0.19491686,0.02715888,-0.0071963,-0.03445231,-0.02368775,-0.10314296,-0.05204178,-0.03300664,0.13617395,-0.01898324,0.06596754,0.01219768,0.00046738,0.00301369,-0.02889085,-0.00954148,0.0040966,0.00635503,0.0078275,0.03653616,0.00011952,-0.08515005,0.00615082,-0.00537873,-0.02633759,0.07202447,-0.00655046,0.06208484,0.0795003,0.03191042,0.02546461,0.05463469,0.05998889,-0.16090733,0.02290685,-0.00838638,0.03869077,-0.00916237,0.03520627,-0.03396071,0.11933976,0.0578916,0.02721201,-0.01139259,0.06452125,0.04471186,-0.04658506,-0.05939032,-0.02794481,-0.02533756,0.03770116,-0.05414567,-0.02391963,-0.02228203,-0.01693233,-0.01702614,0.01138239,0.01116813,0.03726165,0.064299,-0.06232162,0.07796502,0.01945255,-0.06848884,0.00939156,0.01544433,0.00617015,0.02279659,-0.06789931,-0.06516165,0.139939,0.01985986,0.01993876,0.0202209,0.06673346,0.02315537,-0.06573274,-0.0566976,-0.04633968,-0.04077712,0.03091054,0.01156933,-0.00558337,0.08973575,-0.04241163,-0.0009592,0.0116933,0.01244907,0.04043771,0.01382114,0.01744453,-0.01746698,0.02198542,-0.00290987,-0.03281327,-0.02348652,-0.0152373,0.02303005,0.08668411,0.04416331,-0.00962254,0.00944032,-0.04402073,-0.12119149,-0.03808536,-0.02905898,-0.03155891,0.02321175,0.04115355,0.00071828,-0.03381561,0.0019353,-0.02807824,0.05580465,-0.12126303,-0.02426947,0.06465085,0.02649553,0.01158765,0.01123281,-0.00942508,-0.00490096,-0.02375437,-0.06557737,-0.08002015,-0.0454498,-0.00816252,0.03979573,0.06812537,-0.04004724,-0.01444356,-0.0986879,-0.04306787,-0.03665825,0.05410335,-0.00750026,-0.04471166,-0.0317067,-0.02341406,-0.06231186,-0.10878048,-0.02328168,0.0055543,-0.07100638,-0.01334929,0.10894646,0.0100868,-0.06639717,-0.09445577,-0.0369915,0.00150122,0.05765522,-0.00926177,-0.0517703,0.0133373,-0.03697534,-0.05458719,0.00053123,-0.02869219,0.06826697,0.02210454,-0.06382912,0.00224269,-0.01155466,0.00934338,-0.01528254,-0.02872143,-0.03431235,-0.02931238,0.05276066,-0.01748078,0.02583393,-0.02642559,0.03938689,0.05254253,-0.00764043,0.0371259,0.05467391,-0.05570546,0.12036801,0.00089721,-0.06488555,-0.03826649,0.03077354,0.05278793,0.00929766,0.03714308,-0.00810388,0.02633696,-0.01620665,-0.0191647,0.01333429,0.05642975,-0.00420619,-0.16173312,-0.04267576,-0.03057121,0.01355367,0.00070926,-0.0080633,-0.00964656,0.00536513,0.04013428,0.03392611,0.0727879,-0.08440557,-0.00278065,0.08886309,-0.03056511,0.01352191,-0.11313443,-0.02761077,-0.03022629,0.06129277,0.00602394,-0.00079115,-0.04453578,-0.07917154,0.02771075,0.01503101,0.13892286,0.01866543,-0.01458056,-0.04744396,0.01669256,0.04212772,-0.01951099,0.0000114,0.00130796,0.01742785,0.00602359,-0.05716927,-0.06581675,-0.01910551,-0.06034414,0.02815829,-0.03401868,-0.0815549,-0.04952879,0.03717515,-0.01358056,0.07068701,-0.00961442,0.04364936,0.05790422,-0.0105959,0.04904207,0.05738301,0.07117102,-0.02264643,-0.06339434,0.02298631,0.00549579,0.01982844,-0.00987446,-0.04100579,0.03450331,-0.02833218,0.01690704,0.01380739,0.02371242,-0.0436659,0.03849353,-0.00080072,0.00069544,0.05459378,-0.00509678,-0.02980641,-0.05979475,0.0380586,0.03799932,-0.042052,-0.01444974,0.00328177,0.06522504,-0.02824624,0.02440453,0.03966544,0.01418689,-0.01213009,0.06177485,0.0084805,0.01302922,-0.00926097,-0.04105436,0.05835699,-0.02490425,0.02764445,-0.00017302,-0.04263767,-0.24813761,0.02991978,-0.03133847,0.05527163,0.00503724,-0.0019543,0.05976823,0.05238555,0.00006638,-0.02134387,0.05471677,0.03643776,0.00764623,-0.03147363,-0.0283388,-0.03405711,0.03219284,-0.0271776,0.05516034,-0.01140335,0.12486332,0.02325616,0.25603113,-0.01554198,0.02154743,-0.010682,-0.00165742,0.01884207,-0.03272391,0.05016452,-0.03407947,0.03164174,0.08715591,0.00723652,-0.01794864,-0.03396994,0.00130364,0.08368433,-0.005952,0.00758856,-0.07475034,-0.01093474,0.00209731,0.03524146,0.10379189,-0.01114556,0.00151422,-0.10916194,-0.00485047,0.09081382,-0.08795339,-0.02356794,-0.02954384,-0.04834209,0.02833402,0.0470268,0.02335254,0.00299505,0.02010785,0.05665158,0.03517612,0.00871396,0.06251819,0.02498438,0.00495127],"last_embed":{"hash":"1wp5p8q","tokens":169}}},"text":null,"length":0,"last_read":{"hash":"1wp5p8q","at":1753423594723},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{2}","lines":[129,129],"size":584,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05721704,-0.00720717,0.00165826,0.0190207,-0.03274098,0.10542393,0.08149298,-0.01244989,0.02682405,-0.04700846,0.02966005,-0.01463204,0.03521017,0.02305595,-0.02172623,-0.03644763,-0.00736035,-0.03645515,-0.03534868,-0.02777944,0.09587226,-0.03167417,-0.0173705,-0.0922411,0.08729643,0.04372744,-0.01210206,-0.04711216,-0.03851196,-0.19946222,-0.01195889,0.00813591,-0.03128102,-0.01532154,-0.07934649,-0.03223581,-0.04340839,0.09246423,-0.02269244,0.07496909,0.0328609,-0.02719833,-0.01187318,-0.00224759,0.00252655,0.00258478,0.00704009,0.00981362,0.02493417,0.00724739,-0.06170087,0.00791561,-0.01609587,-0.02845885,0.08485293,0.02313828,0.07904709,0.09010954,0.05331595,0.00926662,0.03497618,0.07680793,-0.19931553,0.04211557,-0.03107171,0.05771348,-0.01822849,0.04308874,-0.02767697,0.05286386,0.05771052,0.01633701,-0.03978815,0.05409855,0.0516407,-0.04132399,-0.05387018,-0.03556143,-0.02011366,0.03716267,-0.05605975,-0.00507563,-0.016577,0.00487437,-0.02916711,0.05093613,0.00420957,0.07670579,0.0759697,-0.08259104,0.05876145,0.01087022,-0.01178406,0.0351667,-0.01048593,-0.01068118,0.02269533,-0.02995935,-0.03533033,0.13544177,-0.00373524,-0.00026511,0.01000418,0.05554495,0.06138312,-0.01803845,-0.01964429,-0.02160619,-0.0236363,0.0377921,0.01392481,0.0118861,0.0628406,-0.03513122,-0.04400814,0.01331647,0.01092173,0.00669361,0.01109024,0.00833078,-0.02465101,0.00808327,-0.00962468,-0.03839774,-0.02294024,-0.01850392,0.05489993,0.07175551,0.05607183,-0.0139636,-0.01807072,0.0121781,-0.16320725,-0.03610325,-0.05460268,-0.04222399,0.02372142,-0.00470774,-0.00089233,-0.02170225,-0.01792405,0.00740883,0.0636363,-0.11108056,-0.04704125,0.02397342,0.00347553,-0.00174359,0.02656849,0.02617462,-0.02233153,-0.00034059,-0.02556588,-0.0693095,-0.01924151,-0.01195652,0.03218802,0.09277071,-0.04037106,-0.0101956,-0.0561171,-0.05787847,-0.036662,0.08328559,0.00386752,-0.05617515,-0.05868539,-0.00532582,-0.02018383,-0.12585521,-0.03022823,-0.00152798,-0.06290715,-0.0389563,0.04437951,-0.02355961,-0.06501397,-0.08030454,-0.07082636,0.02307262,0.02900301,-0.02118012,-0.03610675,0.01346893,-0.0546401,-0.10363401,0.0196294,-0.05692123,0.08175262,0.03889649,-0.05267148,-0.00642103,-0.02972957,0.01177954,-0.01593786,-0.03283606,-0.07198957,-0.0224692,0.04547848,-0.01328296,-0.00824158,0.00736115,0.0245742,0.04212929,-0.05442402,0.02634384,0.02501879,-0.03256144,0.11319061,0.02929447,-0.04343898,-0.00392939,0.02755733,0.04556457,-0.03440342,0.00871081,-0.0044714,0.03332247,0.00582338,0.02688498,0.00402747,0.05686711,-0.04074252,-0.15713239,-0.03070823,-0.0583672,0.00267703,0.00658543,0.01395866,0.04133996,-0.01076048,0.00934289,0.01153356,0.08423632,-0.03117188,0.0012804,0.06470208,0.01077509,0.00389651,-0.09961151,-0.05338809,0.00030423,0.0613928,-0.01609567,0.00796478,-0.03710382,-0.02053131,0.01370342,0.00135489,0.12953329,0.04794215,0.01408207,0.00357331,0.05317219,0.03351822,-0.0117611,-0.07375129,0.02687303,0.01536549,-0.02846782,-0.01997409,-0.07843963,-0.0053393,-0.07445883,0.0158536,-0.02322601,-0.09165129,-0.05186059,0.00594657,0.0124942,0.04097996,-0.01390986,0.02032628,0.07091297,0.02292405,0.02502885,0.05383119,0.09789475,-0.02842758,-0.07139011,-0.01144542,0.00271106,0.00912149,0.01743188,0.01355086,0.0390541,-0.03496014,0.06828531,0.01740106,-0.00668733,-0.05697814,0.05078094,0.00551486,0.02246906,0.09247135,-0.01392246,-0.0200676,-0.00342593,0.03908278,0.06466926,-0.07559553,0.01016378,0.00515949,0.03443301,-0.06291825,0.04666565,0.03550621,0.05385568,0.03347832,0.01630213,-0.01073483,0.02538048,-0.02179578,-0.0142165,0.05544604,-0.03628322,0.01639356,-0.00679408,0.00381628,-0.26158339,0.01848353,-0.02212377,0.05501831,-0.01078624,-0.00984032,0.06076823,0.02949614,-0.01831486,-0.01412997,0.09128498,0.00654406,0.00604277,-0.06767765,-0.02414603,-0.02553239,0.05708821,-0.05921087,0.10636932,-0.00152181,0.10595033,0.00112397,0.26387551,-0.01293161,0.00434827,-0.02406918,0.0045374,-0.02098509,0.00026171,0.06471378,-0.0056514,0.01567051,0.05095915,0.0104438,-0.03381797,0.01376002,0.03179238,0.04426716,-0.01646121,-0.01847221,-0.07193623,-0.01299162,-0.0189641,0.02883758,0.09426744,0.02472396,-0.00277491,-0.06513981,-0.02097108,0.08268061,-0.09700441,-0.06566217,-0.06277978,-0.02830257,0.00741864,0.06067937,0.03678411,-0.01509311,0.03923003,0.02410181,0.03474278,0.03045109,0.06795401,0.00059712,-0.00139235],"last_embed":{"hash":"gjmppf","tokens":204}}},"text":null,"length":0,"last_read":{"hash":"gjmppf","at":1753423594787},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>2. Generate Reports for Lotto Strategies</u>#{4}","lines":[132,143],"size":521,"outlinks":[{"title":"We must check any lottery strategies against past drawings.","target":"https://forums.saliu.com/check-strategy-1.gif","line":1},{"title":"Back-testing a lottery strategy is done layer by layer in the draw file.","target":"https://forums.saliu.com/check-strategy-2.gif","line":3},{"title":"The reports show different data from layer to layer, which offers more choices.","target":"https://forums.saliu.com/check-strategy-3.gif","line":5},{"title":" Procrastination is the main obstacle in winning the lottery with Ion lotto software programs.","target":"https://forums.saliu.com/check-strategy-4.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>3. Procrastination: Enemy of Lottery Strategies</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08524931,-0.03640451,-0.00485879,-0.01000582,-0.02653582,0.05497269,0.07232277,-0.00138966,0.04601607,-0.0320712,0.03776386,-0.00055183,0.02511375,0.00294429,-0.01999858,-0.02639296,-0.00004447,-0.05217126,-0.06041792,0.03365795,0.02907761,-0.04599371,0.00143144,-0.06073117,0.08793333,0.02435204,-0.00510345,-0.05605364,-0.069378,-0.22876653,0.01571714,0.0202518,-0.00263195,-0.0504253,-0.08956564,-0.03779022,-0.05536791,0.08499769,-0.02455239,0.05865974,0.03223936,0.00324342,-0.00627079,-0.02390589,-0.00216804,-0.00666458,0.00004745,0.0243404,0.05319929,0.00143771,-0.06561546,0.01363887,-0.01927585,0.01134718,0.06862944,0.01058249,0.05242857,0.09908318,0.04232194,0.01293285,0.06118678,0.04839584,-0.1679028,0.05279328,-0.01032496,0.03058197,-0.01169989,-0.00190384,-0.0322901,0.08233141,0.04056528,0.05491921,-0.01439616,0.06116843,0.03140365,-0.02498563,-0.03598077,-0.01875514,-0.01956077,0.04793776,-0.00869042,-0.05000122,-0.0413058,-0.02903275,-0.01392893,0.04631994,0.00670555,0.06871721,0.05491792,-0.0873875,0.05252812,0.02453045,0.01221277,0.02368011,0.01213396,-0.02890049,0.03199471,-0.00646254,-0.0637776,0.09267537,0.00739226,0.01001349,0.00510947,0.03106129,0.05234084,-0.01887307,-0.04239645,-0.02284224,-0.04773042,0.04848695,0.04712152,0.02393195,0.10980841,-0.06066178,-0.01265,0.04807877,0.02727106,-0.0083838,0.02194646,-0.00314179,-0.0385025,0.04187373,0.00160832,-0.00978585,-0.0430216,-0.01902486,0.04897408,0.08520701,0.01420234,-0.02398201,-0.00183034,-0.01391914,-0.16232912,-0.06773905,-0.04324129,-0.00828525,0.01794911,-0.00781397,0.03930191,-0.00916377,-0.05100783,-0.04824703,0.04666566,-0.10986073,-0.05047274,0.04658431,0.02425047,-0.00421756,0.05366922,0.04246001,-0.00229464,-0.00170631,-0.03502901,-0.09368623,-0.03766199,-0.03585741,0.07197172,0.08922843,-0.05323791,-0.01154207,-0.02718518,-0.03364409,-0.0023917,0.03409354,0.01940021,-0.09538634,-0.04875065,-0.01493929,-0.025348,-0.10473707,-0.02461246,-0.00614356,-0.06526331,-0.0230312,0.07281091,-0.00068387,-0.05999868,-0.05798794,-0.03184711,0.01092424,0.02610695,-0.03700506,-0.01604524,-0.00543499,-0.04455277,-0.09397355,0.02080392,-0.04123845,0.05995422,0.00236953,-0.0469936,-0.01106454,-0.07247157,-0.0011358,-0.02762048,-0.01503105,-0.04288644,-0.04889103,0.05985535,-0.0003079,-0.02855743,-0.02885035,0.02749,0.02070288,-0.03203283,0.0594386,0.01550761,-0.07502983,0.1023619,0.0372287,-0.0292159,0.00513959,0.05181865,0.05074861,-0.0390024,0.01803231,0.01135456,0.00540587,0.00490737,0.0161786,0.0110921,0.05171774,-0.04168424,-0.17798249,-0.00517452,-0.08772635,-0.01987452,-0.00329794,-0.00016837,0.01503328,-0.009718,0.04480229,0.02971999,0.07934189,-0.04610571,0.02357191,0.03609546,0.02938023,-0.02580675,-0.07742387,-0.03942373,-0.0456204,0.04547243,-0.01582361,0.00113301,-0.04853619,-0.04808228,0.01074243,-0.00497662,0.14201407,0.09511157,-0.00384092,-0.00430645,0.07133414,0.04096201,-0.00796304,-0.10090037,-0.0042603,0.0127245,0.03372101,-0.03705566,-0.07715099,0.00661263,-0.1012419,0.02555635,-0.03031049,-0.1080948,-0.06021975,0.0125692,0.01748929,0.08759955,0.00244757,0.07288907,0.08492447,0.01642153,0.03840416,0.01557214,0.06916946,-0.02304947,-0.06967973,0.02494551,0.01340477,0.0565875,-0.01872293,0.00276074,0.03747225,0.00620281,0.0199991,0.04060421,0.01133298,-0.02124239,0.05292803,0.00511878,-0.02013808,0.06602751,0.00485488,-0.04686009,0.02121175,0.03675391,0.05905316,-0.03920913,0.03075082,-0.01071098,0.03163177,-0.08028637,0.02382095,0.05118116,0.04854894,-0.00180491,0.02652602,0.03145809,0.01110687,0.00354076,-0.01294643,0.05808741,-0.04231444,-0.00003822,0.05584869,0.00150434,-0.25209492,0.02025963,-0.01423735,0.05357809,0.02052875,0.02401669,0.04978894,0.00129556,-0.0165335,-0.02806993,0.07868813,0.02869491,0.00102654,-0.04524064,-0.00374578,-0.04904375,-0.01827409,-0.01342801,0.0685375,0.01147705,0.08468279,0.02751464,0.23607577,-0.00718688,-0.00751966,-0.00609603,0.03674182,0.00574429,-0.02672866,0.03569382,-0.0314969,0.02060765,0.03963913,-0.012553,-0.02565622,-0.05708209,0.00781527,0.04801974,-0.01459528,-0.00101554,-0.06794062,-0.01608828,0.0156804,0.01738636,0.13841285,-0.00079285,0.00751569,-0.08956297,0.03923454,0.08299327,-0.09765133,-0.0779152,-0.04637367,-0.02224913,0.00655338,0.06312434,0.0206457,-0.01373603,0.02197811,0.05540276,0.03228666,0.02194819,0.05228733,-0.0028499,0.00819355],"last_embed":{"hash":"1mljg98","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"1mljg98","at":1753423594887},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>3. Procrastination: Enemy of Lottery Strategies</u>","lines":[144,184],"size":3849,"outlinks":[{"title":"This is a real-life pick lottery strategy that hit the first prize.","target":"https://forums.saliu.com/play-1.gif","line":19},{"title":"The winning pick lottery strategy had a few tickets to play.","target":"https://forums.saliu.com/play-2.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>3. Procrastination: Enemy of Lottery Strategies</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0855616,-0.03783711,-0.00491386,-0.00867347,-0.02439654,0.05383734,0.07283461,-0.00251351,0.04513729,-0.03208007,0.03958813,-0.00190203,0.02771646,0.00007483,-0.01928509,-0.02614665,-0.00248617,-0.05274498,-0.06128189,0.0324687,0.02989636,-0.04642015,0.00281223,-0.06059982,0.08923586,0.02531642,-0.00435878,-0.05522493,-0.06993705,-0.22969392,0.01755624,0.02050562,-0.00499913,-0.05010879,-0.08725382,-0.0389377,-0.05444248,0.08263829,-0.02404168,0.05793942,0.03281733,0.00440053,-0.00699284,-0.02529816,-0.0021372,-0.00681616,-0.00327303,0.0228976,0.05245838,0.00343126,-0.06432466,0.01476774,-0.01742991,0.01136688,0.06948561,0.00775164,0.05077338,0.10113183,0.04354114,0.01283262,0.06199663,0.0486274,-0.16652189,0.05199074,-0.01046618,0.03000638,-0.01344437,-0.00484098,-0.03136237,0.08246101,0.03922156,0.05414993,-0.01453704,0.06237146,0.03204685,-0.02349874,-0.03597233,-0.02078291,-0.01916898,0.04864478,-0.00706442,-0.04873005,-0.04215541,-0.03144855,-0.01227795,0.0446426,0.00686783,0.06614131,0.05599663,-0.0866011,0.05101249,0.02287486,0.01296004,0.02469507,0.01254303,-0.03021203,0.03164378,-0.00462257,-0.06463574,0.09159937,0.0086408,0.00984917,0.00596719,0.03123356,0.05277399,-0.01864152,-0.04321982,-0.02291879,-0.04850915,0.04842995,0.04688689,0.02589217,0.10877224,-0.05929377,-0.01249824,0.04689088,0.02918832,-0.00843303,0.02233151,-0.00090097,-0.0400939,0.04062026,0.00141519,-0.0075774,-0.04321705,-0.01950598,0.04881977,0.0846352,0.01382492,-0.02457074,-0.00273741,-0.01616777,-0.16168655,-0.06882706,-0.04323866,-0.00737507,0.01707396,-0.0097035,0.03823781,-0.01146245,-0.05004416,-0.04697407,0.04689857,-0.10696046,-0.05195626,0.04479487,0.02569515,-0.00424707,0.05470443,0.04275272,-0.00255248,-0.00217719,-0.03485026,-0.0941346,-0.03719592,-0.03430408,0.07294202,0.08943442,-0.05212259,-0.0104868,-0.02591737,-0.0336601,-0.00364355,0.03599977,0.01931558,-0.0945477,-0.0488488,-0.01402393,-0.02611134,-0.10484696,-0.0243085,-0.00571553,-0.06535596,-0.02322596,0.07250809,-0.00022217,-0.06187151,-0.05715848,-0.0327166,0.00964284,0.02738891,-0.03795388,-0.01629121,-0.0049294,-0.04515416,-0.09199255,0.02204147,-0.03970039,0.05991226,0.00369287,-0.04830287,-0.00928575,-0.07325456,-0.00194374,-0.02820396,-0.01378172,-0.04304519,-0.04874447,0.05971094,-0.00101201,-0.03018495,-0.02775161,0.02706138,0.01848246,-0.0321848,0.05957592,0.01431888,-0.07439151,0.10172804,0.03844052,-0.02924764,0.00592085,0.05236023,0.05272887,-0.03891846,0.01951474,0.01190455,0.0059027,0.00424876,0.01441588,0.01012766,0.05394901,-0.04061862,-0.17823088,-0.00690041,-0.08888682,-0.02024294,-0.00094287,-0.00059571,0.01389255,-0.01045777,0.0455575,0.03008277,0.07730258,-0.04615342,0.02308082,0.03636107,0.02985106,-0.02254731,-0.07594658,-0.03865689,-0.04780219,0.04478326,-0.01834815,0.0025704,-0.04758995,-0.04702862,0.01134693,-0.00567352,0.14306128,0.09715839,-0.00384398,-0.00508115,0.07096563,0.04083457,-0.00539699,-0.10331982,-0.00275723,0.01368146,0.03445587,-0.0391341,-0.07706895,0.00546759,-0.10368413,0.02465961,-0.02867197,-0.10891278,-0.05848583,0.01247787,0.01961476,0.08616018,0.00172912,0.07467969,0.08454405,0.01703005,0.03604848,0.01569932,0.06887664,-0.02197035,-0.06964025,0.02659698,0.01282671,0.05784941,-0.01898743,0.000484,0.03861839,0.00587646,0.02117064,0.03987136,0.01175935,-0.02029949,0.05291187,0.00562357,-0.02042666,0.06849428,0.00330562,-0.04705277,0.02012491,0.03712679,0.05809883,-0.03961973,0.03130595,-0.01146664,0.03160926,-0.07839147,0.02423963,0.05049802,0.04968277,-0.00415752,0.02637437,0.03102189,0.01210938,0.00472435,-0.01351515,0.05882261,-0.04284536,-0.00056731,0.05679153,0.00122393,-0.25186011,0.01977951,-0.01417102,0.05216551,0.02052075,0.02425077,0.04821657,0.00147091,-0.01800206,-0.02931214,0.07997908,0.02764509,0.00039194,-0.04687183,-0.00175718,-0.04843649,-0.01853428,-0.01256939,0.0691973,0.01236976,0.08353241,0.02712204,0.23660251,-0.00784422,-0.0101488,-0.00450188,0.03543886,0.00766915,-0.02899726,0.0356596,-0.03313923,0.0210321,0.04108657,-0.0121712,-0.02517592,-0.05646436,0.00650883,0.04592986,-0.01399336,-0.00063118,-0.06672355,-0.01810493,0.0157413,0.01681915,0.13867104,0.00051798,0.00774659,-0.08796912,0.04003067,0.0832543,-0.095518,-0.07963692,-0.04872868,-0.02196611,0.00869998,0.06381972,0.02238544,-0.01377743,0.02196517,0.05384165,0.03002772,0.02184855,0.05160546,-0.00330426,0.00665394],"last_embed":{"hash":"1vj825e","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"1vj825e","at":1753423595075},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>3. Procrastination: Enemy of Lottery Strategies</u>#{1}","lines":[146,184],"size":3790,"outlinks":[{"title":"This is a real-life pick lottery strategy that hit the first prize.","target":"https://forums.saliu.com/play-1.gif","line":17},{"title":"The winning pick lottery strategy had a few tickets to play.","target":"https://forums.saliu.com/play-2.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09829326,-0.01771093,0.02640518,-0.02588839,-0.01710542,0.06551111,-0.00254761,-0.03054924,0.02625648,-0.01323492,0.03969124,0.01280255,0.0436721,-0.00775512,-0.02061709,-0.01820536,0.00668073,-0.03067751,-0.06499631,0.00155393,0.0421477,-0.03255501,-0.00882046,-0.07817242,0.05617697,0.00271405,-0.0246418,-0.05398428,-0.08490334,-0.20473807,0.03238652,0.00489594,0.02486911,-0.05034469,-0.0607214,-0.03298253,-0.01112386,0.08016826,-0.07220466,0.04018631,0.02896545,-0.01094872,-0.01256865,0.00434338,-0.00788072,-0.05567025,0.03442686,-0.00027113,0.08205633,0.03403738,-0.06761334,0.01588117,-0.0075953,0.01861027,0.04608217,0.00187832,0.05957435,0.09324307,0.01692625,0.03442263,0.06300019,0.07054209,-0.17539385,0.08908664,-0.03740655,0.02134488,-0.00572357,-0.01475283,-0.01081435,0.0158237,-0.0146771,0.04669082,-0.02115376,0.0727228,0.02791831,-0.06294083,-0.05178215,0.00236729,-0.04297054,0.02369407,-0.08045007,-0.0013424,0.00190301,-0.0220074,-0.02253079,0.05685975,0.02320703,0.03510994,0.05637115,-0.08757825,0.0250301,-0.01058401,0.00519615,0.03982145,-0.02035255,-0.00117719,0.04004162,-0.01267914,-0.04502973,0.11366901,-0.02204604,-0.01455914,-0.04470968,0.04640938,0.03400988,-0.02122142,-0.03145099,-0.01744235,-0.03891103,0.07124148,0.04895515,0.00344661,0.03147267,-0.06389678,-0.03126593,0.0059639,-0.06389878,-0.02563986,0.02128549,0.00139682,-0.04677201,0.00010521,-0.01517198,-0.00928888,-0.01965391,-0.00390435,0.05775678,0.02823209,0.04781616,0.02219978,0.02281517,0.0403749,-0.16412561,-0.02928703,-0.00638474,0.01940772,0.02138534,-0.0099392,-0.01783739,-0.03941283,-0.01218314,-0.00081992,0.05414308,-0.09301605,-0.01701958,0.09049369,0.01276727,-0.01063922,0.02168313,-0.01116217,0.00264933,-0.02625217,-0.0541423,-0.04097532,-0.02485947,-0.00886852,0.1239658,0.08413182,-0.05370937,-0.01218938,-0.04587322,-0.04325605,-0.01072584,0.08752257,-0.01730401,-0.0960456,-0.04335951,0.02983746,-0.02583303,-0.10014706,-0.0218013,0.00172052,-0.03255281,0.0166995,0.08284137,-0.03409982,-0.06586992,-0.08622457,-0.02484455,-0.007568,0.01439644,-0.02188012,-0.03756089,-0.00805827,-0.02878576,-0.0954634,0.01432585,-0.02132862,0.05250477,0.0352956,-0.064621,0.02355582,0.01509863,-0.04816158,-0.03173261,-0.02804836,-0.06725354,-0.03521779,0.06253533,-0.02143837,0.01987827,0.00146875,0.04719467,0.07280603,-0.05920894,0.02131295,0.01588443,-0.06186738,0.11517426,-0.01773584,-0.04093732,0.01217563,0.05014043,0.06445167,-0.07407361,0.0298711,-0.00011638,0.01908265,0.02006651,0.02797754,-0.00169998,0.04069581,-0.02773991,-0.18052191,-0.00588011,-0.07779857,-0.00979554,-0.03741885,0.00612459,0.05467892,-0.01109717,0.05148207,0.03338295,0.13460582,-0.10657086,0.0263742,0.07381123,0.00491801,-0.01124176,-0.06196846,-0.04696378,-0.02427682,0.03504873,-0.02163562,-0.01716148,-0.02776507,-0.04835763,0.031517,-0.03438,0.13312471,0.03494605,0.0325119,-0.01131748,0.06005429,0.03803949,-0.00998911,-0.10247684,0.01198135,0.03766994,-0.02169963,0.01453032,-0.05249171,0.03458519,-0.08020952,0.00419752,-0.00014631,-0.10922144,-0.01142961,0.01325435,-0.00055778,0.04958839,-0.01057551,0.03248899,0.05371427,0.01811766,0.02531777,0.03197262,0.06778271,-0.03099747,-0.02687011,0.014756,0.04432793,0.00271518,-0.00534444,-0.06317358,0.05699646,-0.026142,0.02386587,0.00665347,0.01857734,-0.06136203,0.05897852,-0.04337161,-0.01145364,0.05653982,-0.01935739,0.03720468,-0.00918397,0.04616902,0.06168352,-0.03166566,-0.02065935,-0.01249676,0.01523074,-0.02451712,0.06311639,0.08029696,0.04462001,0.01404407,0.06546985,0.05026416,0.0166405,-0.02166908,-0.00665733,0.01588036,-0.01219139,0.02616541,0.00065591,0.01518592,-0.24757098,0.00450143,-0.02680628,0.05219443,-0.0068777,-0.02336669,0.06226066,0.02540556,-0.00007879,-0.03160756,0.03030651,0.03225161,-0.00670428,-0.07640056,-0.03195889,-0.046685,0.04783358,0.01052359,0.08058559,0.00997098,0.09851961,0.02228079,0.26321822,-0.01882236,0.02042958,0.02942324,0.02543975,-0.000697,0.03086589,0.05887665,0.01404178,0.01193513,0.063088,-0.00842457,-0.00594698,-0.01127229,-0.00157451,0.03237712,0.00311834,0.02173007,-0.06558743,0.00432007,-0.03937071,0.01212662,0.07343807,-0.00703599,0.01929217,-0.07841025,-0.00082223,0.09921232,-0.09700505,-0.04931935,-0.08889762,-0.03652709,0.03505506,0.01773232,0.00003859,0.01613367,0.02987478,0.04871624,0.05016376,-0.02441977,0.08674492,0.01042057,0.00640387],"last_embed":{"hash":"tph3ud","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"tph3ud","at":1753423595262},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>","lines":[185,265],"size":6540,"outlinks":[{"title":"Lottery Data Files is a collection of software to work with past winning lotto numbers.","target":"https://forums.saliu.com/LotteryData.gif","line":7},{"title":"The lottery software works best at the command prompt run by a desktop shortcut.","target":"https://forums.saliu.com/LotteryDataShortcut.gif","line":13},{"title":"_**Run Lottery Software at Command Prompt in Windows XP, Vista, Windows 7, 8, 10**_","target":"https://saliu.com/gambling-lottery-lotto/command-prompt.htm","line":17},{"title":"_**Home of Notepad++**_","target":"https://notepad-plus-plus.org/","line":21},{"title":"My lottery software is enhanced nicely by the best text editor: Notepad plus plus.","target":"https://forums.saliu.com/NotepadLottery.gif","line":32},{"title":"_**Download LotteryData.zip**_","target":"https://saliu.com/pub/LotteryData.zip","line":79}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09645749,-0.01930182,0.03553792,-0.02377852,-0.01779355,0.05986303,0.00406651,-0.03522671,0.0275093,-0.01478409,0.0415121,0.01577479,0.04734433,-0.00551511,-0.01513807,-0.01618448,0.00713343,-0.02993944,-0.06804557,-0.00094541,0.047004,-0.03109203,-0.01078747,-0.07332421,0.06097895,0.00712418,-0.02084994,-0.05673675,-0.08735598,-0.20669413,0.03173863,-0.00044703,0.03221014,-0.04472774,-0.0531761,-0.03847789,-0.01057922,0.08377476,-0.06625204,0.04367002,0.02413847,-0.00845458,-0.01418941,0.00563443,-0.01280389,-0.04072301,0.02866551,0.00539561,0.07448234,0.03518416,-0.07342065,0.01950782,-0.01705025,0.01786507,0.04710848,0.00371545,0.0651366,0.09569447,0.02144493,0.03401349,0.0709378,0.076842,-0.17101336,0.08795134,-0.03713089,0.02456025,-0.00958854,-0.01854618,-0.00780668,0.01872382,-0.01441968,0.04311495,-0.01984221,0.07058755,0.02912253,-0.06461897,-0.0527641,0.00316185,-0.04282706,0.02870877,-0.08967044,-0.0039316,0.00014849,-0.01916826,-0.02583931,0.05191763,0.02352194,0.03041441,0.05533382,-0.08850399,0.02708539,-0.01315404,0.00331202,0.04402748,-0.01929804,-0.00102243,0.03231564,-0.00647733,-0.04470899,0.11283742,-0.03120474,-0.00793223,-0.03439599,0.04946465,0.02463149,-0.01768464,-0.03416575,-0.01220242,-0.03856785,0.07546545,0.05208402,0.00893788,0.0224879,-0.06830317,-0.02003825,0.00577766,-0.05848666,-0.02154433,0.01637054,0.005433,-0.04903578,0.00040848,-0.01462641,-0.01500892,-0.018634,-0.00339402,0.0571934,0.02633506,0.05067471,0.02473412,0.02092522,0.04874346,-0.16670109,-0.02407439,-0.00689118,0.01861381,0.02836602,-0.01317842,-0.01770332,-0.03970746,-0.00528302,-0.00077501,0.05087875,-0.08977413,-0.01313458,0.0978254,0.01048025,-0.00758348,0.02487293,-0.01601565,-0.00045644,-0.02539811,-0.05582057,-0.04567834,-0.02780006,-0.01134614,0.11971962,0.07397746,-0.05367109,-0.01034155,-0.05468479,-0.04202627,-0.01313357,0.08318207,-0.01849932,-0.08854581,-0.0437425,0.02907772,-0.02146984,-0.10246719,-0.01753555,0.00588518,-0.02509846,0.0098381,0.08151809,-0.02889007,-0.06736335,-0.09123372,-0.02889307,-0.00824931,0.01959493,-0.02379422,-0.03799919,-0.01156965,-0.03292919,-0.0944352,0.01147009,-0.01834815,0.05352518,0.03667873,-0.06879272,0.03134912,0.01455893,-0.04807338,-0.03117345,-0.02747496,-0.05908341,-0.03623734,0.05558291,-0.02762432,0.01728214,0.00398322,0.0492286,0.0783306,-0.06123399,0.01856736,0.01790227,-0.05897946,0.1141525,-0.01906849,-0.04100612,0.01410293,0.03934752,0.0664746,-0.06779569,0.02816762,0.00613195,0.01861397,0.02106762,0.02469181,-0.00284747,0.03414044,-0.02198936,-0.17811389,-0.00600269,-0.0721882,-0.0113043,-0.03747168,0.01026605,0.05169258,0.00068106,0.04616682,0.02800274,0.13402502,-0.1077778,0.02945723,0.07532609,0.00674703,-0.0123504,-0.06520916,-0.03985623,-0.02785448,0.02989952,-0.02956383,-0.02071433,-0.0287697,-0.05246393,0.03786752,-0.0323495,0.1334776,0.0356873,0.03346111,-0.01915517,0.06106371,0.03474673,-0.01630506,-0.10542091,0.01517347,0.02838138,-0.02481497,0.00836075,-0.05791645,0.0301223,-0.07657678,-0.00388917,0.00119261,-0.10710073,-0.01007023,0.01560644,-0.00173772,0.04909798,-0.00766225,0.0306435,0.05004115,0.01694616,0.02987539,0.03919648,0.07281162,-0.03396707,-0.02713924,0.02008234,0.0484575,0.00170909,-0.00600806,-0.0661015,0.05603713,-0.02224459,0.02864535,0.00227101,0.01608139,-0.0615669,0.05412338,-0.04338291,-0.01245427,0.05874775,-0.02965853,0.03701039,-0.01401008,0.03728741,0.05811137,-0.0364853,-0.01760457,-0.01074264,0.01942801,-0.03148348,0.06453708,0.07192968,0.04306581,0.01351061,0.06865922,0.04903704,0.01295124,-0.02089157,-0.0011675,0.01757291,-0.01335868,0.02971746,-0.00170302,0.00384653,-0.24801417,0.0053179,-0.02561473,0.05368998,-0.00970759,-0.0201296,0.06775512,0.03057922,0.00590997,-0.02966348,0.02458306,0.02980071,0.00046768,-0.07987164,-0.0251856,-0.03726652,0.04659682,0.01719979,0.08497578,0.01361946,0.10371737,0.01927448,0.26525986,-0.02002843,0.02187978,0.03545167,0.01974881,0.00584392,0.01575449,0.06021239,0.0117675,0.01202995,0.06225477,-0.01292335,-0.00483273,-0.01814964,-0.00184289,0.03195235,0.00793579,0.02359734,-0.06616279,-0.00531232,-0.04776987,0.01730605,0.07592873,-0.01600331,0.02162768,-0.07754019,-0.01430456,0.09429014,-0.0956404,-0.04789329,-0.08823597,-0.03294403,0.0337423,0.02282265,-0.00341125,0.01592216,0.02823516,0.05301272,0.05439208,-0.0242608,0.07992347,0.00055469,0.00516256],"last_embed":{"hash":"1pr46ai","tokens":379}}},"text":null,"length":0,"last_read":{"hash":"1pr46ai","at":1753423595456},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{1}","lines":[187,200],"size":1283,"outlinks":[{"title":"Lottery Data Files is a collection of software to work with past winning lotto numbers.","target":"https://forums.saliu.com/LotteryData.gif","line":5},{"title":"The lottery software works best at the command prompt run by a desktop shortcut.","target":"https://forums.saliu.com/LotteryDataShortcut.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07989993,-0.04006623,0.02563772,-0.04740852,-0.03184286,0.056662,0.01524335,-0.00882657,0.01306883,-0.03293732,0.02184765,0.0376478,0.04524759,0.00617863,-0.0363092,-0.03974735,-0.01157919,-0.01248894,-0.06586225,0.02971128,0.04979856,-0.06065824,-0.0096982,-0.06821135,0.05871245,0.03308597,-0.02463257,-0.05087566,-0.07386318,-0.19885749,0.04389293,0.01603512,0.00576673,-0.04538026,-0.07684091,-0.03558635,-0.03191019,0.09182672,-0.05450106,0.05062838,0.00931569,-0.02097738,0.00929433,0.0171405,0.00750949,-0.05953939,0.0045479,0.0098376,0.08496875,0.00758672,-0.0571208,0.02136763,-0.00121597,0.00031404,0.0776991,0.00455593,0.05823574,0.09910064,0.01935834,0.04485142,0.04646013,0.06087004,-0.18087818,0.10027412,-0.01232641,0.04079306,0.00222171,0.02169179,-0.02469472,0.04078709,-0.0187376,0.06307625,-0.0347998,0.0601047,0.02663571,-0.05200337,-0.05604289,-0.00977891,-0.05655482,0.04075437,-0.07898252,-0.03613333,-0.01730853,-0.00295493,-0.03312986,0.07383306,0.0066347,0.05235936,0.04087146,-0.06394122,0.04179471,-0.00895139,-0.01571303,0.02540383,0.00961352,-0.00561502,0.03924533,0.00682065,-0.04498771,0.10827506,0.00262751,-0.02687,-0.04459161,0.02158789,0.04186292,-0.01691585,-0.0449867,-0.0380839,-0.0295107,0.04787952,0.03740743,0.00336671,0.02949587,-0.05706658,0.00033083,0.01401412,-0.03991515,-0.00063404,0.02175275,0.00869903,-0.05114116,0.00613075,-0.02435573,0.00771301,-0.02448972,-0.01677753,0.04141517,0.06532074,0.04540907,0.00489872,-0.00621776,0.04986941,-0.1716954,-0.04660146,-0.01910671,0.00649033,0.02245138,0.00443558,0.01421435,-0.0481663,-0.01956106,-0.0223065,0.08051153,-0.08812083,-0.02743294,0.08911233,0.00974406,-0.00136591,0.02264616,-0.00272518,0.00600296,0.01013133,-0.06319966,-0.03891746,-0.01497807,0.00077584,0.0977731,0.0566743,-0.03674768,-0.02018713,-0.07309648,-0.04133804,0.00423856,0.09167462,-0.02394435,-0.09627579,-0.04780097,0.01380936,-0.0183315,-0.11548963,-0.03523194,-0.03170677,-0.05235883,0.03428936,0.09159524,-0.03315654,-0.06732092,-0.07299251,-0.01319748,0.01749639,0.01328313,0.00607679,-0.03827668,-0.00227271,-0.04026537,-0.09911335,0.01821599,-0.04263762,0.04855257,0.02776232,-0.05749539,-0.00446537,-0.02282771,-0.02382315,-0.03202151,-0.04557136,-0.08896521,-0.05129788,0.06302068,-0.02559095,0.02520923,-0.00632594,0.00529841,0.06805301,-0.04124032,0.02585792,0.00974522,-0.05302246,0.11124057,-0.00861102,-0.02380068,0.02892427,0.05162816,0.05709164,-0.0704216,0.01507751,0.02284537,0.01061222,-0.00169638,0.04701697,-0.00614798,0.047793,-0.03469503,-0.17455554,-0.02574791,-0.06613195,-0.01045932,-0.0253501,0.00083202,0.02882143,-0.02721048,0.02942466,0.0370384,0.10220693,-0.07284661,0.00990037,0.06033204,-0.01974168,-0.00839204,-0.06915318,-0.0343922,-0.02903424,0.03088049,-0.00044465,-0.00789117,-0.06301844,-0.02525494,0.04914233,-0.01579527,0.1366495,0.04030296,0.03849637,0.00343305,0.07372031,0.04178714,-0.00346503,-0.07542183,0.02556305,0.02523869,-0.01640492,0.0268844,-0.0539748,0.02690447,-0.05826362,0.00396976,-0.00300843,-0.10059196,-0.02765679,0.0333028,-0.00076201,0.03197964,-0.01112341,0.05745151,0.07431909,0.02854885,0.04463696,0.04477482,0.04610214,-0.0296863,-0.04657841,0.02549849,0.02627041,0.03083956,-0.01277953,-0.065768,0.0265943,-0.00057964,0.0277279,0.02151115,0.00675863,-0.03084129,0.05873882,-0.02600301,0.00075867,0.03350439,0.00333324,0.02586137,-0.00965237,0.03506672,0.05307388,-0.00989333,0.03753639,-0.01966176,0.03246292,-0.03383407,0.03498878,0.07203133,0.02704885,0.00219404,0.0427019,0.02066157,0.0259371,-0.01782746,-0.00479024,0.04639681,-0.01846875,0.03408344,-0.01114287,-0.00003261,-0.27418235,0.00884271,-0.04718312,0.0500938,-0.00195921,-0.02530803,0.036947,0.03664789,0.00154238,-0.00618092,0.02493927,0.02925732,-0.01673581,-0.11150573,-0.04775133,-0.0487299,0.02456215,0.00238093,0.09088156,-0.00757171,0.09518575,0.00819422,0.26027343,-0.00421042,0.01038032,0.00642063,0.009067,-0.02236977,0.02860674,0.05043174,-0.01278332,0.00683458,0.06924059,-0.02641435,-0.03924678,-0.00272502,-0.01444389,0.0359882,-0.00929541,0.01706002,-0.0577121,0.00453532,-0.01538749,0.01053067,0.10215542,0.03509719,-0.01117639,-0.07441697,0.03775517,0.11345695,-0.09877036,-0.04693316,-0.08159525,-0.01524087,0.00377994,0.04743545,0.01009291,0.03002608,0.04443916,0.04365662,0.06580689,-0.00531434,0.07915943,0.03448685,-0.008207],"last_embed":{"hash":"f8xjf5","tokens":435}}},"text":null,"length":0,"last_read":{"hash":"f8xjf5","at":1753423595624},"key":"notes/saliu/Playing Lottery Strategies, Lotto Strategy Means Start.md#Playing Lottery Strategies, Lotto Strategy Means Start#<u>4. Software to Manage Lottery Data Files</u>#{5}","lines":[207,263],"size":4618,"outlinks":[{"title":"My lottery software is enhanced nicely by the best text editor: Notepad plus plus.","target":"https://forums.saliu.com/NotepadLottery.gif","line":10},{"title":"_**Download LotteryData.zip**_","target":"https://saliu.com/pub/LotteryData.zip","line":57}],"class_name":"SmartBlock"},