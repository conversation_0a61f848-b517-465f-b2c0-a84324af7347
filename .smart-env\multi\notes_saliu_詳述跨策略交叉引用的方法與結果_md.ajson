
"smart_sources:notes/saliu/詳述跨策略交叉引用的方法與結果.md": {"path":"notes/saliu/詳述跨策略交叉引用的方法與結果.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12301616,-0.01559279,-0.00148527,-0.03175954,0.02396911,-0.00185474,-0.00335078,-0.02997936,0.06691696,-0.03984113,-0.00279936,-0.0615184,0.03896865,0.02250975,0.01836384,-0.03486659,0.03502391,-0.02104932,-0.08116543,-0.03679775,0.07381466,-0.04073208,-0.02185285,-0.07498936,0.04416897,-0.00278777,0.01561069,-0.03491731,-0.02055679,-0.18116686,-0.04562779,0.01583103,0.03574646,-0.00861107,-0.04416605,-0.02094621,-0.00538213,0.01113074,-0.02835728,-0.00534607,0.0298051,-0.01885213,0.04437152,-0.02817552,0.01872352,-0.01474607,0.00902673,-0.03190999,0.03070281,-0.01960461,-0.04391551,-0.03021295,0.02216765,0.0117251,0.02095072,0.01360203,0.06613573,0.08025528,0.04408213,0.01674715,0.01555085,0.02289114,-0.21916471,0.01491393,-0.00228517,-0.08940642,-0.01465168,-0.05826896,0.02101594,-0.00602768,-0.04978388,0.01446519,-0.03614919,0.07735925,0.02721749,-0.06048882,0.02469943,-0.04721883,-0.02728688,-0.00326716,-0.0321643,0.03199072,-0.01632737,0.01113174,0.02467409,0.06061026,-0.00733548,-0.00906511,-0.0075977,-0.06472988,0.01344427,-0.03974015,0.0110443,0.05977197,-0.01781917,0.00032676,0.00091495,0.04188655,-0.07230241,0.10120357,0.01107833,0.03579763,-0.0012988,-0.02505964,0.0325182,-0.01364426,0.00017013,-0.05702803,-0.04290653,0.01028436,-0.01763145,0.01707177,0.02747767,-0.0484465,0.00151908,0.0517331,0.01157536,0.00401514,-0.02785114,-0.01521647,-0.02726429,0.01123123,0.00749111,-0.02953917,0.01547544,-0.03385484,0.01980923,0.03086117,0.07323083,0.03258051,0.0532284,-0.00286345,-0.08288611,-0.05514456,-0.02199924,-0.0158329,-0.00278862,-0.04604375,0.01151495,-0.04619372,-0.00970078,-0.07230251,0.01466974,-0.12429421,-0.04996436,0.12581602,-0.0515578,-0.02110822,-0.01899569,-0.05484737,-0.01861307,0.02106668,0.01272737,-0.02978609,0.0280036,-0.01446374,0.08874343,0.14872342,-0.02869287,-0.0042827,0.01318714,-0.03231768,-0.04018421,0.14919116,-0.005297,-0.05150609,-0.03509897,0.04784925,0.00752251,-0.06219714,0.00628492,-0.00312955,-0.00324587,0.02263761,0.09994925,-0.01153118,0.06141743,-0.07009095,-0.05256878,0.00287867,0.01657662,-0.04534362,-0.08650275,0.0288729,-0.0109662,-0.06007373,0.04089667,0.01027505,0.05303452,0.04083141,-0.07377969,0.04491972,0.03422955,0.03142221,-0.0718288,-0.0345074,-0.04676768,-0.06284966,0.0154984,-0.00547629,0.08254458,0.01369418,-0.02588474,-0.01848402,-0.0075961,0.03647185,-0.00518576,-0.00777936,0.00833296,0.04165894,-0.02984652,0.06956743,0.02337631,0.04163939,-0.0115765,0.00583718,-0.02062803,0.0590106,0.00478693,0.04242642,0.05905091,-0.02627538,-0.09189043,-0.22558823,-0.02543437,-0.02260091,-0.04428366,0.02265239,-0.01373322,0.00960487,0.01513027,0.07908642,0.06747361,0.10350088,0.07363767,-0.04587559,-0.00290662,-0.00159729,-0.0127561,0.03338675,-0.02006036,-0.02259525,0.01481876,0.02186321,0.04559483,0.0273166,0.03478948,0.06208082,-0.05749128,0.12660857,-0.01065717,0.02388324,0.00760737,0.0640661,0.03166199,0.02311871,-0.10934833,0.01876928,0.02674774,-0.11121756,0.02712066,-0.04795803,-0.04074354,-0.00863645,0.027836,-0.01662502,-0.0569161,-0.00288755,-0.03365558,0.01919771,-0.03651255,-0.04439317,-0.02155969,0.04058643,-0.01141127,-0.00627704,0.07597094,0.01233458,-0.05350963,-0.03083248,-0.03649389,-0.0466415,-0.0506995,-0.00232777,-0.02856575,0.08315977,-0.01697675,-0.02067635,0.01268442,-0.01108434,0.03089794,-0.00250458,0.04394239,-0.10396698,0.12092425,-0.01008231,0.02327572,0.02012646,0.02539689,-0.02330473,-0.01317712,0.01436138,0.01202974,0.06316249,0.02220262,0.03860408,0.02813031,0.08051478,0.03881031,0.02963803,-0.00270934,-0.00035757,-0.0046038,0.01246463,-0.01346576,-0.03815737,-0.00158547,0.05435545,0.03445378,-0.31411418,0.01681703,-0.03019563,0.01283435,-0.01690575,0.01620098,0.04512985,-0.01449028,0.01570691,0.0332422,-0.004634,0.09653672,0.01740451,-0.0684538,-0.0316014,-0.01821624,0.02411129,-0.00915637,0.05924204,0.00033507,-0.00234417,0.02228637,0.23793833,0.0469672,0.00646313,0.01507683,0.00127792,-0.03751325,0.02826712,0.03715491,-0.00300918,-0.01213943,0.08351608,0.03851273,0.0508267,0.07834563,-0.01732122,0.02448202,-0.00732526,0.01386444,-0.0182908,0.0091206,-0.09621564,0.00514389,0.10378949,0.01978476,-0.02982348,-0.08266232,0.00958632,0.05099218,-0.03247207,-0.02437374,0.02037008,0.0250022,-0.02279589,0.04428335,0.03148989,0.01085939,0.0391662,-0.00619308,0.06312943,0.01236171,0.01940179,0.06990149,0.09346112],"last_embed":{"hash":"1hyhvk2","tokens":484}}},"last_read":{"hash":"1hyhvk2","at":1753495719497},"class_name":"SmartSource","last_import":{"mtime":1753429673031,"size":6260,"at":1753495676981,"hash":"1hyhvk2"},"blocks":{"#":[1,2],"###核心軟體工具":[3,10],"###核心軟體工具#{1}":[5,6],"###核心軟體工具#{2}":[7,7],"###核心軟體工具#{3}":[8,8],"###核心軟體工具#{4}":[9,10],"###方法與流程":[11,45],"###方法與流程#{1}":[13,14],"###方法與流程#{2}":[15,19],"###方法與流程#{3}":[20,31],"###方法與流程#{4}":[32,36],"###方法與流程#{5}":[37,40],"###方法與流程#{6}":[41,45],"###結果與效益":[46,57],"###結果與效益#{1}":[48,49],"###結果與效益#{2}":[50,50],"###結果與效益#{3}":[51,51],"###結果與效益#{4}":[52,52],"###結果與效益#{5}":[53,53],"###結果與效益#{6}":[54,54],"###結果與效益#{7}":[55,55],"###結果與效益#{8}":[56,56],"###結果與效益#{9}":[57,57]},"outlinks":[]},"smart_blocks:notes/saliu/詳述跨策略交叉引用的方法與結果.md###核心軟體工具": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11877631,-0.00557622,-0.01134219,-0.01660694,0.04646067,-0.00621767,-0.00149873,-0.01255603,0.0649903,-0.02098786,0.00995003,-0.0767392,0.03431275,0.04228306,0.01365337,-0.02402808,0.04007897,-0.01184793,-0.11124439,-0.02661225,0.06721693,-0.03493017,-0.00746539,-0.06919704,0.0479787,-0.01344137,0.00577215,-0.05379551,-0.03273864,-0.18851686,-0.07707766,0.02125069,0.0441559,-0.01102467,-0.03899479,-0.01736868,0.00286559,-0.01759802,-0.01510136,-0.0230979,0.02025513,-0.01300525,0.04148062,-0.01566918,0.0022743,-0.01828385,-0.0004307,-0.01607972,0.02852159,-0.02201153,-0.03739668,-0.01619549,0.01127927,0.01311584,-0.00047018,-0.00952818,0.0725254,0.09035981,0.03385551,0.04121383,0.01485586,0.0193936,-0.20988743,0.01263618,0.00048159,-0.08938736,-0.03746012,-0.04958812,0.02370225,-0.01782409,-0.06051863,0.0265824,-0.0205772,0.09424013,0.02551568,-0.07753728,0.03279332,-0.04855507,-0.01172896,0.00520042,-0.02406052,0.05017081,-0.03802773,0.01059488,0.03626086,0.04547488,-0.0194162,-0.00242635,-0.01431941,-0.04938865,0.00169001,-0.0392873,0.02649375,0.04165378,-0.01460005,-0.00336801,-0.0120413,0.03291457,-0.0546715,0.10835444,-0.0063472,0.02288579,0.0076652,-0.02427756,0.04868914,-0.01252697,-0.00327666,-0.03733167,-0.04303573,0.01323319,-0.02417609,0.0118457,0.02764695,-0.05115223,0.00739294,0.07083268,-0.00648892,0.00873213,-0.03220484,-0.01403157,-0.03523754,0.01001971,0.010824,-0.03940193,0.0172863,-0.03307606,0.02874252,0.0163314,0.06105855,0.05825512,0.04368372,-0.02240363,-0.07435244,-0.05559045,-0.03117481,-0.0093742,-0.02020917,-0.05599782,-0.02087652,-0.03348997,0.01184049,-0.08007967,0.02158464,-0.11868756,-0.01139977,0.12145543,-0.05075496,-0.02664881,-0.00526129,-0.03694084,-0.03057406,0.00467797,0.02176651,-0.04085049,0.03835983,-0.00893246,0.09061676,0.1281084,-0.04690101,0.01182001,0.01511411,-0.04923502,-0.02929911,0.13983437,-0.00713333,-0.02419945,-0.03924665,0.04207401,-0.0130518,-0.06043096,-0.00437214,0.01035807,0.01480553,0.01142967,0.09077862,-0.0233975,0.05708844,-0.08252802,-0.04250502,0.00210604,0.01903416,-0.06707419,-0.06042178,0.03528693,-0.01291367,-0.05620432,0.02858321,0.03087118,0.05360351,0.03208331,-0.09028231,0.04328821,0.0208097,0.0116674,-0.06645039,-0.01184466,-0.01890914,-0.04657546,0.00589404,-0.01294891,0.08146694,0.0104551,-0.02260184,-0.01265275,-0.00733897,0.0227216,0.00260184,0.01096215,0.01170694,0.02405832,-0.03347519,0.05034151,0.0278488,0.02328477,-0.03664947,-0.02292968,-0.02068958,0.06982999,0.00181303,0.05312023,0.06631698,-0.0396574,-0.09437703,-0.23646933,-0.00472808,-0.01694731,-0.05747828,0.03886025,-0.01221808,0.00134952,0.04148087,0.08150677,0.06373088,0.12617652,0.06230741,-0.02805254,0.00501714,0.00849646,-0.00707843,0.02905008,-0.02809962,-0.0157715,0.00672184,0.02806079,0.02076375,0.03294032,0.0308706,0.07403822,-0.04854768,0.11223045,0.02669959,0.03595657,-0.01102195,0.04723046,0.01719235,0.00632143,-0.127657,0.01515692,0.02680405,-0.0969989,0.02655609,-0.01860756,-0.05131447,-0.0028105,0.0278403,-0.01479885,-0.04570804,0.00400266,-0.04801916,0.0189162,-0.02428828,-0.01883589,-0.01288748,0.04716244,-0.01369444,0.01276654,0.09228718,0.02847994,-0.06257962,-0.0383073,-0.03057002,-0.05154491,-0.0591835,-0.00176875,-0.0290709,0.07712667,-0.01448474,0.00309412,0.01368374,-0.0014973,0.03247707,0.00455451,0.05433974,-0.09660874,0.10493131,-0.01744261,0.02096069,0.04227583,0.03209522,-0.02000915,-0.0272799,0.0116306,0.00678261,0.07976912,0.01591425,0.06865189,0.00275553,0.08779374,0.03482175,0.03827967,-0.00101526,-0.00966638,-0.00489522,0.00056396,-0.00035741,-0.04303139,0.00523699,0.05347892,0.019614,-0.31306642,0.01913255,-0.02134982,-0.01331015,-0.00764202,0.00875725,0.05038903,-0.0153141,0.0084437,0.04272832,-0.01830352,0.09187419,0.02183265,-0.06300116,-0.04491491,-0.01432427,0.02492125,-0.00115094,0.08061598,0.02292355,-0.01167193,0.02317863,0.23815039,0.02702984,0.00045803,0.01332149,-0.00130452,-0.06029507,0.02510923,0.04930877,0.01637454,0.00072017,0.04975115,0.02306631,0.04781225,0.07632973,-0.00523808,0.04367912,-0.02073281,0.00098936,-0.01780313,0.01586253,-0.08107422,-0.00749768,0.09461174,0.00748025,-0.04037083,-0.0655904,-0.00820541,0.01958309,-0.03739299,-0.00461148,0.03981453,0.02306121,-0.01946765,0.04941952,0.02055178,-0.0018336,0.03748957,-0.02890073,0.07567327,0.00993433,0.02635646,0.06091693,0.10086207],"last_embed":{"hash":"1hiut92","tokens":286}}},"text":null,"length":0,"last_read":{"hash":"1hiut92","at":1753495718826},"key":"notes/saliu/詳述跨策略交叉引用的方法與結果.md###核心軟體工具","lines":[3,10],"size":332,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08688805,0.00730048,0.01922014,-0.02228622,0.03118918,-0.01692348,-0.02977073,-0.03062178,0.06263883,-0.03520272,-0.00115766,-0.05322967,0.03587667,-0.00780347,0.0249982,0.0050487,0.00481605,-0.05046019,-0.07522751,-0.03395968,0.07769616,-0.05656551,-0.00738295,-0.07155308,0.04562365,0.01352597,0.01962809,0.00841548,0.01590241,-0.18609171,-0.03369047,0.03003607,0.03400902,0.0069517,-0.04268501,-0.02747842,-0.00631814,0.0308843,-0.03395018,0.01200128,0.05439626,0.0099958,0.06419881,0.01286992,0.01132587,-0.01705721,-0.00589314,-0.03362966,0.01835182,-0.00684137,-0.03919004,-0.04613189,0.02725947,0.00801275,0.04880375,0.02585956,0.06969783,0.09392432,0.06106392,-0.01943067,0.02409547,0.03604537,-0.20978877,0.03135962,-0.03934027,-0.06974662,-0.02048121,-0.07659337,0.04828831,0.00112449,-0.05084917,0.01714497,-0.0259464,0.06933364,0.03106374,-0.03483672,0.00516607,-0.01857157,0.00964987,-0.02695912,-0.01901146,0.02827943,0.01247162,0.01237167,0.02171484,0.03846644,-0.00541161,-0.01458906,-0.0200366,-0.03781449,0.01395535,0.00324904,-0.01095808,0.06121468,-0.05836118,0.0016538,0.02973392,0.0583908,-0.07266929,0.10851821,-0.00771102,0.02289616,0.00980002,-0.03343427,0.02436967,-0.05411214,-0.02924036,-0.04608195,-0.05883522,-0.03511059,0.00805263,0.02272754,0.03150778,-0.08492246,-0.0103205,0.00966784,0.01134056,0.00099211,-0.05725811,-0.01597954,0.00275518,0.05021611,0.02440899,-0.02354711,0.01293251,-0.02161748,0.03776774,0.06378485,0.04637774,0.03180332,0.04442053,0.03096356,-0.06448675,-0.03001318,-0.00149985,-0.01126469,-0.01144227,-0.04732266,-0.02791517,-0.04359812,-0.02806778,-0.04430562,0.02727794,-0.11916053,-0.02666638,0.08844894,-0.03086941,0.02200829,-0.01653656,-0.03569263,-0.02357094,0.03004612,0.01710442,-0.0374445,0.0320448,-0.00607213,0.0690328,0.15888326,-0.00536769,-0.01596973,0.00543795,-0.01613107,-0.03536087,0.14388271,-0.00871973,-0.04353284,-0.01632151,0.06256481,0.03374777,-0.065103,0.01023111,0.01297746,-0.0326056,-0.01602937,0.10787518,-0.03428073,0.03723206,-0.03157426,-0.06517749,0.01590488,0.01261048,-0.03596326,-0.08424705,0.04140113,-0.00310527,-0.07404926,0.04645247,0.03309048,0.01821819,0.02716177,-0.07316852,0.06571993,0.00122074,0.03201107,-0.07237555,-0.01337204,-0.05965167,-0.04746895,0.06616535,-0.04269955,0.06820367,-0.00142335,-0.02729006,-0.04859807,-0.01316936,0.01094713,0.02861422,-0.05493971,0.00886533,0.05714393,-0.02694135,0.08246867,0.05623053,0.06304687,-0.06616802,0.01992608,-0.03305902,0.06262805,-0.03430487,0.00148105,0.05788627,-0.02060227,-0.07579862,-0.22670013,-0.02600741,0.00855624,-0.06604259,0.00198377,-0.03201589,0.02558098,-0.00135556,0.10443535,0.04527178,0.07889489,0.07010384,-0.03936858,-0.00946547,0.01932848,-0.05475831,-0.00172729,-0.01605994,-0.03474777,0.03977914,0.02452203,0.02156382,0.01962925,0.02285756,0.03177358,-0.07814962,0.11123677,-0.01339732,0.03320852,-0.00212942,0.05279333,-0.0063067,0.0414363,-0.08452986,0.0359753,0.02573081,-0.08885389,0.04311325,-0.04695338,-0.07457165,0.03740446,0.03837227,-0.05243825,-0.06422242,0.01097127,-0.01146581,-0.04440985,-0.01959321,-0.06144301,0.01752126,0.00664436,0.01900919,-0.00393317,0.07505727,0.02011931,-0.08529768,-0.08477087,-0.01304589,-0.04070543,-0.02086485,-0.05223568,-0.03715878,0.07466543,-0.00242674,0.01166327,0.00619379,0.00808463,-0.03539879,-0.01296849,0.03080013,-0.09120297,0.13355736,-0.00714498,0.01262402,0.00445302,-0.02832792,-0.05016628,-0.06357002,0.01933607,0.01229858,0.05674451,0.00335413,0.01969699,0.06882325,0.07077499,0.03077239,0.01824334,0.00742921,0.02908763,-0.03428291,-0.00660786,-0.03026829,-0.01330636,-0.03288839,0.03426001,0.01949573,-0.30247003,0.00311707,-0.01682296,-0.00298045,-0.04136345,0.00545556,0.04455192,-0.00758636,-0.03718952,0.00789605,-0.01792808,0.06622781,0.04918412,-0.05393063,0.00163238,-0.0278971,0.04831888,0.02716324,0.01620677,-0.03365843,-0.01147239,0.03757426,0.22358909,0.0502639,-0.00225299,0.01758259,0.03865452,-0.01598512,0.04734412,0.02923097,-0.04376052,0.00291478,0.08262731,0.01603775,0.04144727,0.07963621,-0.03140675,-0.02675491,-0.00836211,0.029643,-0.00575109,0.02933478,-0.07059473,0.04652915,0.14481534,0.03304883,0.0008949,-0.07054248,-0.00162202,0.05377566,-0.02189196,-0.01379351,0.0167424,0.02293009,-0.00374234,0.03501831,0.02652,0.01854724,0.0568209,0.02220513,0.02404804,0.00698664,0.02391079,0.08301864,0.03812283],"last_embed":{"hash":"1yi9tx3","tokens":428}}},"text":null,"length":0,"last_read":{"hash":"1yi9tx3","at":1753495718969},"key":"notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程","lines":[11,45],"size":1538,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09237603,0.01143957,0.01912484,-0.01527183,0.02472571,-0.01051599,-0.03757087,-0.02093502,0.06840733,-0.03560855,0.00007275,-0.07286268,0.03835073,-0.00026743,0.02870987,0.01547583,0.01769302,-0.06594165,-0.07910805,-0.02161097,0.08155457,-0.05571389,-0.00806476,-0.08101446,0.04574567,0.01819862,0.01334063,0.00580546,0.02295094,-0.17182736,-0.04673391,0.03600224,0.03755898,0.01762463,-0.0223977,-0.03349788,-0.01160604,0.02070255,-0.02408179,0.0026274,0.03409208,0.01140915,0.04967624,0.02045311,0.00712922,-0.02359355,-0.01724915,-0.01624768,0.03332888,-0.01420835,-0.03096344,-0.02130437,-0.00279161,-0.00091893,0.04136218,0.02721807,0.07645276,0.08344434,0.03845029,-0.00945655,0.03651965,0.02748878,-0.20399658,0.03551845,-0.03166658,-0.06449781,-0.01773765,-0.08649668,0.04623502,-0.00077931,-0.07164812,0.01851365,-0.02751724,0.07062173,0.03460887,-0.03985376,0.01858728,-0.01481115,0.00732733,-0.0192319,-0.01993818,0.0472423,-0.00446778,0.00964069,0.02230111,0.03064491,-0.00919085,-0.01410261,-0.02013108,-0.03883666,-0.00819519,0.01946402,-0.0200491,0.04446882,-0.06118235,-0.00169358,0.02479705,0.04146998,-0.06603642,0.10785194,-0.03116171,0.0140844,-0.0104775,-0.02349201,0.02776461,-0.04668823,-0.03071065,-0.04500847,-0.05405586,-0.04763807,0.01372583,0.01799511,0.03272116,-0.07578564,-0.01143405,0.01263229,0.00669539,-0.00149505,-0.05550687,-0.01715304,0.00015442,0.04729366,0.04704129,-0.02479543,0.01184821,-0.02471617,0.0446043,0.04824599,0.0255425,0.02553855,0.05016335,0.02534973,-0.05595606,-0.03381877,-0.00353966,-0.02239825,-0.01906722,-0.03661082,-0.02124054,-0.02453218,-0.01408468,-0.05509778,0.02241396,-0.1184283,-0.02728612,0.08761062,-0.02307763,0.02525983,-0.01530708,-0.01174911,-0.03019219,0.03709895,0.01940138,-0.04204731,0.04592203,-0.00309775,0.07863575,0.15419403,-0.00909674,-0.00764796,0.01654907,-0.03107103,-0.03899428,0.13052614,-0.01256465,-0.03930342,-0.0069199,0.06522755,0.043779,-0.06950125,0.02625212,0.00128885,-0.01725058,-0.02674838,0.11667503,-0.03634295,0.05547774,-0.04253812,-0.06169612,0.02073186,-0.00168387,-0.05866004,-0.07480305,0.0253002,0.00329813,-0.06542195,0.03320202,0.03651058,0.02798354,0.01143225,-0.08338035,0.06835461,-0.03468841,0.03440417,-0.07015632,-0.01397043,-0.04385218,-0.05237041,0.05005564,-0.0501469,0.03913646,0.0070729,-0.02423905,-0.04066173,0.00615642,0.01099185,0.03781374,-0.04945114,-0.00100246,0.06136419,-0.01820073,0.07491281,0.05815392,0.0517817,-0.06967774,0.01439159,-0.02514031,0.07188486,-0.03022184,0.01038404,0.04531137,-0.03585828,-0.06713516,-0.22956821,-0.03287368,0.01196173,-0.07147387,-0.00009702,-0.03474897,0.03176761,0.01127531,0.1076453,0.03340046,0.07774612,0.07008985,-0.03104067,-0.01722126,0.01941894,-0.05258549,0.00597067,-0.00670321,-0.01502083,0.04155572,0.01751316,0.01112149,0.03958514,0.01224706,0.03645407,-0.07094006,0.11581585,-0.00858257,0.04363628,0.0018615,0.04805917,-0.0234285,0.03073836,-0.12428685,0.04881995,0.01822293,-0.10242501,0.04444489,-0.05992888,-0.07963965,0.04481399,0.03560142,-0.04528161,-0.06119372,0.01983089,-0.02284932,-0.05280851,-0.01854709,-0.0569285,0.02125446,0.01794515,0.00696509,0.00491731,0.08705536,0.02303818,-0.08126097,-0.08986239,0.00382124,-0.05800769,-0.02885229,-0.05443813,-0.01807722,0.07456966,0.01890304,0.02043734,0.00203331,0.00138389,-0.02757724,-0.01430046,0.02112673,-0.09423976,0.13991454,0.00301743,0.00319852,0.01720932,-0.0268704,-0.04681406,-0.08414987,0.01318914,0.02175756,0.04129402,-0.01460173,0.01546213,0.04613402,0.07847129,0.04510489,0.0355453,0.02804109,0.02117101,-0.03914955,-0.01790488,-0.02715737,-0.00873181,-0.02356478,0.0386431,0.00731426,-0.2969394,0.00807997,-0.01815849,-0.00045907,-0.03668587,0.0034325,0.06243201,-0.01278852,-0.05042829,0.01175047,-0.01187018,0.06194459,0.05145818,-0.04699953,-0.00007938,-0.00696295,0.06149894,0.02392162,-0.00105199,-0.03779675,-0.01474421,0.04170439,0.2220529,0.0568561,-0.00025861,0.01665002,0.04564663,-0.02185101,0.05470283,0.02896398,-0.05465646,-0.0111752,0.06561044,0.01885108,0.03495938,0.08934584,-0.01606485,-0.01655867,-0.00435656,0.03262666,-0.00486002,0.02781238,-0.05598512,0.05406804,0.13697067,0.03738744,0.00672421,-0.05287999,-0.02018762,0.03087021,-0.02801555,-0.01990372,0.03615417,0.02554609,0.01154646,0.03596386,0.02267632,0.01785041,0.05905539,-0.00392317,0.05050233,0.01087263,0.01564479,0.0764043,0.04774439],"last_embed":{"hash":"1g06xj1","tokens":205}}},"text":null,"length":0,"last_read":{"hash":"1g06xj1","at":1753495719114},"key":"notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{2}","lines":[15,19],"size":227,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08848625,0.00496468,-0.00560131,0.00317381,0.02678772,-0.00977892,-0.03586249,-0.01153696,0.04270976,-0.04981422,-0.0109361,-0.06098299,0.0480381,-0.0117273,0.02327968,-0.01638111,-0.01119423,-0.00787434,-0.07845667,-0.04511366,0.08062708,-0.03372516,-0.01296052,-0.07034305,0.03646887,0.00616678,0.02172197,0.000246,0.01027375,-0.18694428,-0.02311186,0.0230642,0.01775094,-0.01041938,-0.03518465,-0.03066591,-0.00380264,0.02186048,-0.05051082,0.026673,0.05097502,-0.01304288,0.07219677,-0.00563624,0.04451115,-0.00371176,-0.01539244,-0.02118784,0.01028496,-0.02944521,-0.05816683,-0.03164445,0.04484901,-0.00394978,0.04361023,0.02652569,0.05341206,0.09718239,0.06162038,-0.01141634,0.02142953,0.03163586,-0.21117119,0.00881043,-0.04475786,-0.04456047,0.00787698,-0.06064398,0.04742491,0.02127813,-0.00462971,0.00448233,-0.02562498,0.06476334,-0.0070741,-0.03247618,0.02110998,-0.04013072,-0.00998836,-0.01390607,-0.02352723,0.01058559,0.01133635,0.01510279,0.02216443,0.0521683,0.01220858,0.01216091,-0.00057275,-0.03532327,0.03956914,-0.04041796,0.00048664,0.04594086,-0.04669041,0.0319188,0.03859058,0.05933884,-0.07968788,0.10555811,0.02838169,0.03834465,0.03833104,-0.04303282,0.01993838,-0.05323404,-0.01510544,-0.05767116,-0.06403706,-0.0092506,-0.00753686,0.02541922,0.01821245,-0.04159911,-0.01916059,-0.00481947,0.00503889,0.01839099,-0.0756629,-0.0243933,0.00486096,0.03765333,0.02135596,-0.02794682,0.01282031,-0.03751391,0.04828025,0.04693701,0.07002515,0.01491097,0.02037588,0.00165535,-0.07483105,-0.04650768,0.01129368,-0.02522163,-0.00619713,-0.01473066,0.00286848,-0.05577625,0.01369832,-0.04124833,0.01960159,-0.10210191,-0.00104553,0.09397119,-0.02493611,0.00041308,-0.01303771,-0.0390472,-0.01107061,0.00556442,0.00044916,-0.04834468,0.02946707,0.01020803,0.07081105,0.14858952,0.00003493,-0.01583893,-0.00000749,0.00116025,-0.05537996,0.13122724,-0.0255412,-0.04816067,-0.01570841,0.0455115,0.03181211,-0.05472009,-0.00261506,0.02454657,-0.03637958,0.0131027,0.10585193,-0.01918268,0.02153035,-0.02878196,-0.04230747,0.00058638,0.04265884,-0.0266207,-0.08563025,0.02189637,-0.00879679,-0.10855321,0.0406162,0.01402996,0.01331805,0.05951053,-0.06855922,0.05672814,0.00448006,0.03393982,-0.07441051,-0.03666542,-0.0634495,-0.03469194,0.05802605,-0.03712491,0.10620736,-0.01741204,-0.04315479,-0.06271294,0.00245858,0.00329539,0.06522679,-0.04349702,0.00297938,0.06012024,-0.03802508,0.07805826,0.03812933,0.07897922,-0.04009417,0.03852683,-0.05019938,0.07507531,-0.03005183,-0.00605571,0.05200224,-0.02924163,-0.10204083,-0.22591789,-0.0057448,-0.01120584,-0.08372141,0.00194181,-0.01977818,0.00946288,0.0017055,0.06398121,0.05172433,0.06912168,0.04719667,-0.05159074,-0.01057917,0.00104203,-0.0227191,-0.00146602,-0.01912646,0.0064855,0.01542178,0.0388949,0.03776213,0.03475532,-0.00382832,0.05556068,-0.07107336,0.12404773,0.00484424,0.01906721,-0.01585591,0.05801881,-0.01024185,0.03878218,-0.07223697,0.00735436,0.0216823,-0.0804174,0.02957506,-0.04626044,-0.0652256,0.04594024,0.03365485,-0.06173986,-0.04965019,0.02264874,-0.01545463,-0.00018583,-0.02273985,-0.03945775,0.01587185,0.01653062,0.01908145,0.01440726,0.06931907,-0.00803509,-0.05667191,-0.07189403,-0.00599745,-0.04184882,-0.00457595,-0.02683027,-0.03066941,0.06573911,-0.03738468,-0.01558208,0.0261182,-0.00932131,-0.04296514,-0.00368341,0.03542008,-0.08929788,0.11482842,-0.01001146,0.00683743,0.02014643,-0.00203474,-0.06128625,-0.06722213,0.00810883,-0.00018062,0.04303747,0.02363995,0.0225441,0.07601839,0.06444915,0.01441628,-0.00333959,-0.01265678,0.02669908,-0.04951629,0.00207762,-0.01854457,-0.02428327,-0.01077838,0.01677191,0.01389735,-0.31491283,-0.01153476,-0.04078577,-0.00889902,-0.03507388,0.01627804,0.03346572,-0.00915431,-0.02366412,-0.00968873,-0.00229501,0.07614238,0.04113082,-0.04457473,-0.0103994,-0.05719183,0.05065629,0.01240149,0.04488482,-0.02287655,-0.00648616,0.01923075,0.25047928,0.05992523,0.00838106,0.0169891,0.027225,-0.02367892,0.04947127,0.02704116,-0.00206328,0.00919856,0.08942402,0.00762909,0.05972698,0.07650262,-0.05192914,-0.01217782,-0.00967842,0.03901629,-0.01115816,0.01564655,-0.08603236,0.05621292,0.13960205,0.01857868,-0.00388142,-0.08871345,0.00574242,0.06245601,-0.02551155,0.00125449,0.01515856,0.02452788,-0.01916142,0.03442942,0.01207751,0.00746856,0.0410543,0.02738942,0.03855696,0.0042572,0.06129948,0.095797,0.05015447],"last_embed":{"hash":"1x2azys","tokens":318}}},"text":null,"length":0,"last_read":{"hash":"1x2azys","at":1753495719185},"key":"notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{3}","lines":[20,31],"size":437,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12255454,-0.01538746,0.02298329,0.0262429,0.01113982,0.00929903,0.0160184,-0.04150278,0.04980766,-0.03574394,-0.00222284,-0.06480838,0.05743662,-0.00349935,0.04589818,0.00181215,0.00936981,-0.01940202,-0.06798207,-0.04064186,0.09973112,-0.0273017,-0.00515496,-0.05586877,0.06967128,0.00724332,0.00618004,-0.01762591,0.00335249,-0.16643831,-0.0482933,0.02422279,0.01274103,-0.01212541,-0.0245161,0.00875822,-0.02773421,0.01396124,-0.04147824,0.04069681,-0.00760529,0.05793808,0.07811096,-0.0223543,0.02201677,0.0017985,-0.00534571,-0.02817993,0.0474343,0.00338571,-0.0669513,0.01068712,-0.02101233,-0.00272841,0.02270059,0.01959778,0.07862654,0.08162337,0.00713707,-0.00959904,0.01343784,0.03551532,-0.21662243,0.00253998,0.00316456,-0.04175249,-0.00463377,-0.04268983,0.03143095,0.0657433,-0.07204179,0.02478322,-0.04282602,0.06567307,0.01070138,-0.02548391,0.00219771,-0.02527264,-0.02775518,-0.02309713,-0.04851225,0.02748506,-0.03682735,0.03331923,-0.00374374,0.04115019,-0.00301467,-0.02420146,-0.00031746,-0.01205507,-0.00240019,-0.04652645,-0.02518863,0.04488941,-0.05370304,0.02028793,0.03969904,0.06098903,-0.08868247,0.09592213,-0.02401306,0.07331918,-0.03335256,-0.04202209,0.02020525,-0.02117662,-0.01412165,-0.02647633,-0.03263838,-0.02104271,0.01145282,0.03043951,0.00231218,-0.06223866,-0.03458506,0.03019966,0.05460638,0.00371687,-0.03836261,-0.05992648,-0.01395505,0.03288178,0.02884649,-0.04141472,0.01451142,-0.02633444,0.02959805,0.03034724,0.0561144,0.02247122,0.02285161,-0.01423156,-0.08766524,-0.04990323,-0.07775655,-0.0576249,-0.0373322,0.0027102,0.00212981,-0.04126132,0.00347599,-0.05997122,-0.00711934,-0.114373,-0.08461285,0.07858989,-0.04871548,0.01497881,-0.01361488,-0.03053388,0.01534533,0.04246638,-0.01521015,-0.05710636,0.0194585,-0.02007628,0.07930347,0.12244996,0.01103109,0.02472823,-0.00288636,-0.02705037,-0.06036925,0.11112302,-0.00067951,-0.03109711,-0.01132166,0.06351294,0.03622621,-0.02684042,0.03681365,-0.00499108,-0.02122288,-0.01170161,0.07588824,0.012016,0.04622682,-0.03724075,-0.03856561,0.04637794,-0.00452895,-0.05581068,-0.08799098,-0.01431588,-0.0291546,-0.09127615,0.04176904,-0.00594933,0.03616244,0.02671897,-0.06944922,0.06242082,0.00631981,0.04062664,-0.05545878,-0.06410276,-0.02301311,-0.06141255,0.05254405,-0.00776771,0.05249899,0.02910483,-0.03172991,-0.02581886,-0.00493101,0.01870428,0.03650041,-0.02753869,0.01439041,0.07393159,0.00408677,0.0828032,0.02441127,-0.00597227,-0.00667746,0.02458868,0.00966875,0.03324224,-0.0217862,0.04388531,0.00743461,-0.05286752,-0.08214583,-0.23521711,-0.01417863,-0.03262243,-0.03976027,0.04762249,-0.0348543,0.01576072,0.02253455,0.07515494,0.02319868,0.05802732,0.06442323,-0.04319341,0.01153387,0.02402343,-0.01195443,0.00254521,0.01078522,-0.00259723,0.05114893,-0.01421256,-0.00708122,0.01849482,0.01662295,0.05550418,-0.08418778,0.1295798,-0.01061344,0.04218353,0.00475646,0.05413972,0.019134,0.01958879,-0.13942549,0.03562341,0.06613988,-0.09221268,0.02056849,-0.05816337,-0.04059041,0.04426219,0.04069909,-0.01114789,-0.04481982,0.0261409,-0.06413856,0.0043991,-0.0331267,-0.00088617,0.0266836,0.02820054,-0.01178449,-0.01106177,0.04411768,-0.03523055,-0.00817557,-0.08410081,0.00610217,-0.05696504,-0.02410676,-0.0035384,0.03691385,0.08628717,-0.03261297,-0.02731522,0.02206667,0.01619549,0.00985022,-0.01568408,0.02675141,-0.07199574,0.12468568,-0.00299499,-0.00014567,0.02136859,0.02189024,-0.08144082,-0.08635689,0.01449999,-0.01707262,0.04586155,0.00576194,0.01014613,0.00964033,0.06007954,0.03281987,0.02795068,-0.013931,0.03633365,-0.03092063,-0.00125744,-0.0126932,-0.04320694,-0.0017178,0.08364178,-0.00429356,-0.316477,0.01183818,-0.00569657,0.02102481,0.02706833,0.03393691,0.02910693,-0.05499227,-0.05600472,0.00509242,0.03240699,0.05718623,0.00241721,-0.03890216,0.01236403,0.0118952,0.06601807,-0.00977671,0.02339017,-0.04144839,-0.02093545,0.03247126,0.22429119,0.07643073,0.04598056,-0.01678512,0.01373625,-0.01699715,0.02845499,0.00224792,-0.01390578,-0.06191231,0.07974672,0.02852853,0.03063338,0.10213225,-0.02268908,0.0110555,-0.03013747,0.02812956,-0.01875754,0.00606881,-0.06459614,0.03960757,0.11462859,0.00982917,-0.00066727,-0.07138409,-0.01434821,0.06645706,-0.0322537,-0.0052595,0.01668637,0.04102352,0.03910436,0.04012688,0.04252508,-0.01434916,-0.000633,0.00745065,0.05493237,0.04410904,0.01936967,0.090362,0.0924673],"last_embed":{"hash":"1gb3b0i","tokens":221}}},"text":null,"length":0,"last_read":{"hash":"1gb3b0i","at":1753495719290},"key":"notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{4}","lines":[32,36],"size":224,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12676051,-0.02644995,0.01294567,-0.04753379,0.03822424,0.00638045,-0.02954087,-0.04816461,0.0219786,-0.04393345,0.0055576,-0.02276069,0.05864061,-0.02533796,0.01206079,-0.04258597,0.0013346,-0.03744445,-0.06445676,0.00930584,0.06980444,-0.04825074,0.00013253,-0.0795981,0.01616088,0.03047407,-0.00733591,-0.03133093,-0.01276164,-0.17256358,-0.02854297,0.00217002,-0.0030694,-0.00179416,-0.02123033,0.00178682,0.01100882,0.04151886,-0.03188698,0.04158499,-0.01064251,0.01697128,0.05326038,-0.0294862,0.00384849,-0.03201187,-0.03261539,-0.02805218,0.05453679,-0.00409513,-0.05762975,-0.03632187,0.029613,-0.01559901,0.03298744,-0.00254886,0.03498127,0.05107992,0.00539678,0.02349909,0.0270602,0.03222442,-0.22679231,0.03901877,-0.01534948,-0.03631713,-0.0286562,-0.03138408,0.00242004,0.06524028,-0.09231687,0.03055734,-0.05071009,0.06923892,-0.00840561,-0.05516428,-0.00490747,-0.01552111,-0.05973677,-0.0202818,-0.03587279,0.0386491,-0.00814476,0.00110483,-0.01571267,0.08574657,-0.02280736,-0.00245034,-0.00196155,-0.01990882,-0.01052405,-0.02895783,0.00375784,0.05372888,-0.04491701,-0.01670458,0.05545565,0.07106036,-0.09016495,0.07283287,-0.02133806,-0.01310899,-0.0107122,-0.02718636,0.02655239,-0.00259855,0.00360782,-0.02452001,-0.01088771,0.01918539,0.00187787,-0.00413632,0.01393841,-0.07249779,-0.00882957,0.00873056,-0.00591517,-0.00922378,-0.06090707,-0.01397451,0.00428441,0.00354024,0.02349437,0.0059966,0.0289416,-0.04094566,0.01715958,0.04205522,0.09127055,0.02330567,0.05914074,0.00684462,-0.0668945,-0.02774874,-0.04111604,0.00617698,-0.01333443,-0.01326689,0.00675236,-0.08027684,-0.02321111,-0.07885741,-0.01414767,-0.09077831,-0.04269719,0.10351612,-0.05688493,-0.0159921,-0.00421807,-0.05856346,0.01259833,0.07172877,-0.01438474,-0.00528543,-0.00107788,-0.00019742,0.10061858,0.10888279,-0.04966286,0.02306846,-0.01614163,-0.02749247,-0.05877797,0.16051216,-0.00386902,-0.04287805,-0.022499,0.03618393,0.0216918,-0.0555376,0.00249794,-0.05052978,0.00247723,-0.00010846,0.08608253,-0.01424791,0.04064838,-0.03449892,-0.06805858,0.01666982,0.0170744,0.0037121,-0.09480963,0.01871666,-0.03276654,-0.05338717,0.01428676,-0.01330132,0.05501611,0.01768257,-0.06663301,0.0489368,0.03835465,-0.01027383,-0.06116867,-0.04012562,-0.03421989,-0.05086893,0.03676997,-0.0265533,0.11659005,0.04451995,-0.04430512,-0.0057667,0.00788052,0.01624934,0.01000119,0.01562502,0.00460527,0.06321029,-0.00527263,0.0942606,0.04812676,0.01245316,-0.01085273,0.01597255,0.0021874,0.04454212,-0.02229033,0.06200947,0.04937783,-0.04879132,-0.10014789,-0.22044571,-0.01198732,-0.03194647,-0.08025418,0.02767851,-0.03343355,0.03247072,0.02938717,0.05192239,0.03865412,0.09469751,0.04946399,-0.03816427,-0.01248832,0.02678946,-0.00463094,0.02962838,-0.00585942,-0.04399348,0.03774916,0.0291259,0.02642904,-0.03255161,0.02662198,0.07091226,-0.04574417,0.109309,0.00138386,0.04993952,0.04060839,0.06958149,0.0640612,-0.0098714,-0.13954251,0.03055798,0.03306769,-0.08317085,0.02717938,-0.05689408,-0.0146516,0.02070959,0.02102515,-0.01327654,-0.0358097,-0.04962702,-0.02473122,0.00603679,-0.05050657,-0.04000527,-0.00056463,0.00792321,-0.01397684,0.00312051,0.02275648,0.04226908,-0.06145578,-0.02648303,-0.03557524,-0.0322581,-0.03629978,0.00044833,-0.01473043,0.06437851,0.01430722,-0.02812944,0.00469798,0.01517371,0.02337855,-0.00774466,0.03123423,-0.05806978,0.10024802,0.01312369,-0.01340907,0.01271528,0.00434373,-0.034773,-0.0091557,-0.01170351,-0.06006936,0.05039233,0.02961076,0.05821821,0.0118162,0.03070861,0.03445021,0.02859634,-0.02877096,0.02784006,-0.02206051,0.00771671,-0.03316464,-0.02801343,-0.01955727,0.07170878,0.02827005,-0.31907737,0.0133064,0.01478632,0.00560181,-0.00114806,0.02458247,-0.00225594,-0.02620261,-0.01701313,0.01034576,-0.02669195,0.08223086,-0.03760861,-0.09300356,-0.01588004,-0.02095807,0.03805089,0.03207417,0.09886155,0.0100695,-0.0424883,0.00489956,0.23246202,0.03138297,0.07740504,-0.01432411,0.04052767,0.00526587,0.10056029,0.03795132,0.02326676,-0.03655633,0.07406082,0.0355522,0.04241451,0.10930761,-0.0095943,0.02832764,0.02065846,0.01901028,-0.02191126,0.03530774,-0.06139538,-0.01684352,0.09334513,0.01786303,0.00110822,-0.0613465,0.02568669,0.0540393,-0.00164247,-0.00840252,0.01759194,0.03413609,-0.00910476,0.06889883,0.04170801,-0.01405916,0.02297328,0.03452506,0.05439429,-0.01910584,0.04834334,0.08756449,0.09229359],"last_embed":{"hash":"xy81y8","tokens":355}}},"text":null,"length":0,"last_read":{"hash":"xy81y8","at":1753495719363},"key":"notes/saliu/詳述跨策略交叉引用的方法與結果.md###方法與流程#{5}","lines":[37,40],"size":431,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/詳述跨策略交叉引用的方法與結果.md###結果與效益": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09776874,-0.0323884,0.01629288,-0.00891332,0.01347756,0.0025087,0.01295889,-0.0219233,0.09242411,-0.03997096,-0.02302262,-0.07556683,0.05982981,0.01970372,0.0299611,-0.04815045,0.02216004,-0.01783985,-0.05590706,-0.05068375,0.09480463,-0.03117534,-0.009934,-0.05556021,0.04727379,0.02102247,0.00128524,-0.05602703,0.00252497,-0.15979001,-0.03951176,0.03282476,0.01415384,-0.02275929,-0.01783884,0.00316003,-0.03374496,0.0075934,-0.0375004,0.04386395,0.01760257,0.03166004,0.06186811,-0.04282831,0.02775608,-0.00107584,-0.031108,-0.0117593,0.02480864,-0.01582047,-0.05236221,-0.01878567,-0.01974243,0.01604203,0.01400058,0.04371729,0.06358064,0.04668339,0.0135326,0.01948087,0.02793189,0.00177789,-0.20839544,0.00411499,0.00699429,-0.02750242,0.00336949,-0.04155033,0.05319129,0.02883023,-0.06303683,0.01900867,-0.03253006,0.02699764,0.02137464,-0.03734824,0.02741794,-0.01663953,-0.04918861,0.00558347,-0.01821681,0.06249614,0.00004743,0.00683919,0.024221,0.05390323,0.00746827,-0.02120713,-0.00008297,-0.0301618,0.00513044,-0.05859924,-0.01813336,0.0511485,-0.00340888,0.01037369,0.03406513,0.04302983,-0.06389992,0.08373293,-0.01285288,0.02454932,-0.04309393,-0.06493367,0.02766621,-0.02599538,0.01542381,-0.03661468,-0.00306486,-0.01613516,-0.01707244,0.00678171,0.0034511,-0.05352603,-0.01864823,0.04517244,0.034045,-0.00408299,-0.04677644,-0.03172798,-0.00234415,0.0231002,0.0101919,-0.02423314,0.02475544,-0.03599522,0.02150754,0.03798676,0.05333208,-0.00637974,0.04350534,-0.01322961,-0.04868214,-0.08971841,-0.03554225,-0.02496129,-0.01594793,0.03661739,0.05208404,0.00227415,-0.02112077,-0.09022047,-0.00408872,-0.10331023,-0.07459829,0.13047756,-0.04781895,-0.00782782,-0.0146837,-0.03000895,0.00132032,0.07337624,0.00483006,-0.05668516,0.03624143,-0.02010861,0.0652533,0.15646976,0.01020614,-0.0183289,-0.00516101,-0.04798388,-0.06927919,0.12704439,-0.0143788,-0.0428089,-0.00485358,0.06088148,0.01599606,-0.03715165,0.01711508,-0.07153071,-0.00872383,-0.02012795,0.11012604,-0.00341648,0.08535058,-0.03404019,-0.03996201,0.02543416,-0.00315274,-0.01402925,-0.09924498,0.01283194,-0.02557052,-0.10253932,0.01201255,-0.0466942,0.03160689,0.01801497,-0.11326217,0.03031645,-0.00225256,0.03931095,-0.06271865,-0.03946745,-0.03192235,-0.03867019,0.02524615,-0.01346424,0.09721046,0.01742628,-0.03028468,-0.03753415,-0.01205924,0.02678069,0.03730704,-0.02065881,-0.02512169,0.08229201,-0.01477022,0.08314773,0.01308924,0.02506498,-0.00255915,0.01878382,-0.01920271,0.03149154,-0.0241052,0.04572504,0.03388383,-0.04514042,-0.09990662,-0.24043553,-0.0105325,-0.02765688,-0.0497742,0.02010724,-0.0628162,0.01680538,0.00776938,0.05019922,0.10198225,0.07182571,0.07804883,-0.05923927,0.00017411,0.01704205,-0.0243164,0.02819276,0.00613694,-0.03273279,0.0440942,0.01306293,0.03336271,0.01908136,0.03244934,0.03452357,-0.03963373,0.1134809,-0.04054608,0.03575104,0.02601606,0.06275189,0.01999,-0.00018819,-0.06951579,0.02553813,0.02761945,-0.13259859,-0.00985183,-0.0296858,-0.03023386,0.03492111,0.03750535,-0.01189632,-0.072068,0.00884281,-0.05528107,-0.00566336,-0.03061187,-0.03615743,0.04180488,0.03442194,-0.01974113,0.02609686,0.04174818,0.0146237,-0.04537517,-0.03901213,-0.0260468,-0.06531172,-0.03671339,-0.00794146,-0.00446651,0.06322429,0.02791418,-0.01335593,0.02123385,0.00262606,0.05368776,0.00850553,0.04622297,-0.09101342,0.15262334,0.0201495,-0.00833415,0.02255391,0.02633998,-0.0643284,-0.05115617,0.01827765,-0.00641677,0.06301779,0.00491426,0.02229818,0.02608926,0.0298147,0.04589461,0.03666867,-0.01039691,0.0042673,-0.04658189,-0.00902966,-0.00891966,-0.02317208,-0.00675711,0.06952634,0.01270163,-0.31070784,0.01195963,0.00537367,-0.02828545,0.02516015,0.01209764,0.02542234,-0.03154087,-0.05228663,0.01041401,-0.01160211,0.07498185,0.02276384,-0.05917589,-0.0082662,0.01205906,0.04012891,-0.00911503,0.03901362,-0.00768849,-0.01646333,0.00260656,0.22809274,0.04235496,0.03662187,-0.01150987,0.01826241,-0.00181414,0.07107822,0.00858591,-0.01381169,-0.02175883,0.08379466,0.0344238,0.02826593,0.10565827,-0.03112401,-0.00389588,0.02071017,0.01705452,-0.01385638,-0.00594303,-0.06704009,0.02445016,0.1202415,0.00141417,-0.02909481,-0.06905802,-0.00320969,0.01493542,-0.02490476,-0.0097841,0.0268652,0.03930709,0.02748975,0.04898363,0.01393053,0.01017052,0.02589491,-0.00593258,0.05246764,0.03905655,0.00127772,0.11609846,0.0739142],"last_embed":{"hash":"lmavxc","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"lmavxc","at":1753495719497},"key":"notes/saliu/詳述跨策略交叉引用的方法與結果.md###結果與效益","lines":[46,57],"size":760,"outlinks":[],"class_name":"SmartBlock"},
