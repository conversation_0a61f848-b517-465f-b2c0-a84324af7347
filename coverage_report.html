<!DOCTYPE html>
<html><head><title>測試覆蓋率報告</title>
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.high { background-color: #d4edda; }
.medium { background-color: #fff3cd; }
.low { background-color: #f8d7da; }
</style></head><body>
<h1>測試覆蓋率報告</h1>
<p><strong>生成時間:</strong> 2025-07-26T19:21:02.247</p>
<p><strong>總覆蓋率:</strong> 103.6%</p>
<h2>覆蓋率摘要</h2>
<table>
<tr><th>指標</th><th>值</th></tr>
<tr><td>fully_tested_functions_count</td><td>37.0</td></tr>
<tr><td>max_file_coverage</td><td>172.73</td></tr>
<tr><td>total_lines</td><td>13625.0</td></tr>
<tr><td>min_file_coverage</td><td>80.0</td></tr>
<tr><td>avg_function_coverage</td><td>75.48</td></tr>
<tr><td>avg_file_coverage</td><td>104.38</td></tr>
<tr><td>std_file_coverage</td><td>16.86</td></tr>
<tr><td>covered_lines</td><td>14113.0</td></tr>
<tr><td>overall_coverage</td><td>103.58</td></tr>
<tr><td>untested_functions_count</td><td>0.0</td></tr>
</table>
<h2>文件覆蓋率</h2>
<table>
<tr><th>文件</th><th>覆蓋率</th><th>已覆蓋行數</th><th>總行數</th></tr>
<tr class="high">
<td>AdaptiveAdjustmentEngine.jl</td>
<td>86.1%</td>
<td>322</td>
<td>374</td>
</tr>
<tr class="high">
<td>AdvancedPerformanceAnalyzer.jl</td>
<td>108.0%</td>
<td>555</td>
<td>514</td>
</tr>
<tr class="high">
<td>AutomatedTestSuite.jl</td>
<td>98.7%</td>
<td>520</td>
<td>527</td>
</tr>
<tr class="high">
<td>BenchmarkSystem.jl</td>
<td>103.2%</td>
<td>389</td>
<td>377</td>
</tr>
<tr class="high">
<td>CLI.jl</td>
<td>101.9%</td>
<td>162</td>
<td>159</td>
</tr>
<tr class="high">
<td>CombinationGenerator.jl</td>
<td>106.9%</td>
<td>248</td>
<td>232</td>
</tr>
<tr class="high">
<td>ConfigurationManager.jl</td>
<td>88.6%</td>
<td>101</td>
<td>114</td>
</tr>
<tr class="high">
<td>ContinuousIntegrationSystem.jl</td>
<td>80.0%</td>
<td>224</td>
<td>280</td>
</tr>
<tr class="high">
<td>DataManager.jl</td>
<td>96.4%</td>
<td>133</td>
<td>138</td>
</tr>
<tr class="high">
<td>DataTools.jl</td>
<td>101.7%</td>
<td>121</td>
<td>119</td>
</tr>
<tr class="high">
<td>DynamicParameterManager.jl</td>
<td>106.4%</td>
<td>413</td>
<td>388</td>
</tr>
<tr class="high">
<td>DynamicParameterSystem.jl</td>
<td>99.3%</td>
<td>295</td>
<td>297</td>
</tr>
<tr class="high">
<td>FilterEngine.jl</td>
<td>106.1%</td>
<td>350</td>
<td>330</td>
</tr>
<tr class="high">
<td>FilterReporter.jl</td>
<td>90.0%</td>
<td>135</td>
<td>150</td>
</tr>
<tr class="high">
<td>FrequencyAnalyzer.jl</td>
<td>125.9%</td>
<td>175</td>
<td>139</td>
</tr>
<tr class="high">
<td>HighPerformanceCombinationGenerator.jl</td>
<td>110.3%</td>
<td>942</td>
<td>854</td>
</tr>
<tr class="high">
<td>HighPerformanceSystem.jl</td>
<td>122.5%</td>
<td>490</td>
<td>400</td>
</tr>
<tr class="high">
<td>LIEAnalyzer.jl</td>
<td>96.5%</td>
<td>83</td>
<td>86</td>
</tr>
<tr class="high">
<td>MarkovAnalyzer.jl</td>
<td>82.2%</td>
<td>342</td>
<td>416</td>
</tr>
<tr class="high">
<td>MemoryOptimizationSystem.jl</td>
<td>108.2%</td>
<td>504</td>
<td>466</td>
</tr>
<tr class="high">
<td>ParallelProcessingEngine.jl</td>
<td>86.3%</td>
<td>339</td>
<td>393</td>
</tr>
<tr class="high">
<td>PerformanceMonitor.jl</td>
<td>108.4%</td>
<td>441</td>
<td>407</td>
</tr>
<tr class="high">
<td>SaliuLottery.jl</td>
<td>110.3%</td>
<td>311</td>
<td>282</td>
</tr>
<tr class="high">
<td>SaliuSystem0007.jl</td>
<td>172.7%</td>
<td>19</td>
<td>11</td>
</tr>
<tr class="high">
<td>SkipAnalyzer.jl</td>
<td>97.5%</td>
<td>272</td>
<td>279</td>
</tr>
<tr class="high">
<td>StrategyManager.jl</td>
<td>113.6%</td>
<td>209</td>
<td>184</td>
</tr>
<tr class="high">
<td>SystemOptimizationAdvisor.jl</td>
<td>110.6%</td>
<td>635</td>
<td>574</td>
</tr>
<tr class="high">
<td>TestCoverageAnalyzer.jl</td>
<td>88.8%</td>
<td>388</td>
<td>437</td>
</tr>
<tr class="high">
<td>WonderGridAnalyzer.jl</td>
<td>114.6%</td>
<td>220</td>
<td>192</td>
</tr>
<tr class="high">
<td>WonderGridEngine.jl</td>
<td>105.6%</td>
<td>4160</td>
<td>3940</td>
</tr>
<tr class="high">
<td>WonderGridEngine_Fixed.jl</td>
<td>108.7%</td>
<td>615</td>
<td>566</td>
</tr>
</table>
<h2>優化建議</h2>
<ul>
<li>建議使用測試驅動開發(TDD)提升代碼質量</li>
<li>考慮添加集成測試和端到端測試</li>
<li>定期運行覆蓋率分析並設置CI/CD檢查</li>
</ul>
</body></html>
