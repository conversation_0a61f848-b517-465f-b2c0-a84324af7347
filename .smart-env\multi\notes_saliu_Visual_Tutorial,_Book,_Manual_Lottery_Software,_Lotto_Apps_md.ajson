
"smart_sources:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md": {"path":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"he8rag","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753366216136,"size":29618,"at":1753423416500,"hash":"he8rag"},"blocks":{"#---frontmatter---":[1,6],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps":[8,309],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps#{1}":[10,17],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps#{2}":[18,19],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)":[20,25],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{1}":[22,22],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}":[23,23],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{3}":[24,25],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son":[26,33],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son#{1}":[28,33],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!":[34,75],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{1}":[36,45],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{2}":[46,46],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{3}":[47,48],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{4}":[49,50],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{5}":[51,52],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{6}":[53,54],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{7}":[55,56],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{8}":[57,58],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{9}":[59,70],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{10}":[71,71],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{11}":[72,73],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{12}":[74,75],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu":[76,81],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu#{1}":[78,81],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files":[82,89],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files#{1}":[84,89],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files":[90,101],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files#{1}":[92,101],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6":[102,111],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6#{1}":[104,111],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file":[112,131],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file#{1}":[114,131],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis":[132,141],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis#{1}":[134,141],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>":[142,173],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{1}":[144,149],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{2}":[150,150],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{3}":[151,151],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{4}":[152,152],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{5}":[153,153],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{6}":[154,155],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{7}":[156,173],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report":[174,187],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report#{1}":[176,187],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past":[188,213],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past#{1}":[190,213],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations":[214,229],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations#{1}":[216,229],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters":[230,241],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters#{1}":[232,241],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options":[242,259],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options#{1}":[244,259],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities":[260,284],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities#{1}":[262,284],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages":[285,309],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{1}":[287,300],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{2}":[301,301],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{3}":[302,303],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{4}":[304,309]},"outlinks":[{"title":"**XCOPY** _**Command: The Best Backup Procedure, Method, Software in Windows**_","target":"https://saliu.com/best-backup.html","line":16},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":20},{"title":"_**Updates to the**_ **Bright** _**lotto, lottery, and horse racing software bundles**_","target":"https://saliu.com/forum/software-updates.html","line":23},{"title":"_**Software to Manage Lottery Data Files**_","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":72},{"title":"The lottery manual consists of a screen-by-screen lotto tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":74},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":78},{"title":"The lottery data is a file of past lotto results, drawings, draws.","target":"https://saliu.com/forum/HLINE.gif","line":80},{"title":"Lottery software book #2: Create files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":88},{"title":"Lottery software book #3: edit, maintain files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":100},{"title":"There is no better lottery manual than this page.","target":"https://saliu.com/ScreenImgs/lotto4.gif","line":110},{"title":"Bright lotto software require data files of 12 million lotto combinations. For 5-number lotto: a drawings file of at least 4 million lottery combinations.","target":"https://saliu.com/ScreenImgs/lotto5.gif","line":118},{"title":"Lottery software manual: Use all draws in your lotto winning numbers file.","target":"https://saliu.com/ScreenImgs/lotto6.gif","line":120},{"title":"Software book requests to enter the biggest number in lotto game; e.g. 45, 49, 54, 59.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":122},{"title":"Lotto software saves the winning reports or filter files to disk.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":124},{"title":"You too can write a good lotto book and lottery manual.","target":"https://saliu.com/ScreenImgs/lotto9.gif","line":126},{"title":"Lottery winning reports for the special filters named Ion: Lotto sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto10.gif","line":128},{"title":"Lotto software informs on the success of generating the reports for past lottery drawings.","target":"https://saliu.com/ScreenImgs/lotto11.gif","line":130},{"title":"The program shuffles the deck of lotto cards and arranges the lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto12.gif","line":138},{"title":"An MD past draw lotto report: Lottery sum-totals, sums, root sums.","target":"https://saliu.com/ScreenImgs/lotto13.gif","line":140},{"title":"_**lottery filtering or combination reduction**_","target":"https://saliu.com/filters.html","line":150},{"title":"_**Lottery Utility, Lotto Software**_","target":"https://saliu.com/lottery-utility.html","line":151},{"title":"_**Cross-reference strategy files**_","target":"https://saliu.com/cross-lines.html","line":152},{"title":"_**Strategy-in-reverse for lottery and lotto**_","target":"https://saliu.com/reverse-strategy.html","line":153},{"title":"_**Basics of a Lottery, Lotto Strategy Based on Sums (Sum-Totals), Odd / Even, Low / High Numbers**_","target":"https://saliu.com/strategy.html","line":154},{"title":"The function of lotto software that checks a lottery strategy in the past.","target":"https://saliu.com/ScreenImgs/lotto14.gif","line":156},{"title":"The lottery strategy checking software is based on the W winning reports.","target":"https://saliu.com/ScreenImgs/lotto15.gif","line":158},{"title":"The lottery strategy checking software input: screen prompt or disk files.","target":"https://saliu.com/ScreenImgs/lotto16.gif","line":160},{"title":"Skip all input screens for lotto filters. Generate lotto combinations with no favorite numbers.","target":"https://saliu.com/ScreenImgs/lotto17.gif","line":162},{"title":"Type the filter values for each filter in your lotto strategy: minimum and maximum levels.","target":"https://saliu.com/ScreenImgs/lotto18.gif","line":164},{"title":"Ion Saliu's book of lottery: Lotto filters work with sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto19.gif","line":166},{"title":"Nobody has ever written better visuallotto book, lottery ebook for software use.","target":"https://saliu.com/ScreenImgs/lotto20.gif","line":168},{"title":"Lotto software saves the data of a lottery strategy to a disk file ST.","target":"https://saliu.com/ScreenImgs/lotto21.gif","line":170},{"title":"Lottery software also creates a useful file to show how many lotto combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto22.gif","line":172},{"title":"Your lotto software informs of successfully creating the lottery strategy.","target":"https://saliu.com/ScreenImgs/lotto23.gif","line":180},{"title":"The lottery strategy report shows the filters and parameters of your lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto24.gif","line":182},{"title":"A loto strategy has numbers of hits in the past, frequency, skips of loterie drawings between hits.","target":"https://saliu.com/ScreenImgs/lotto25.gif","line":184},{"title":"The software shows lottery strategy reports for inter-related filters: W, MD, 1 to 6-number groups, sums.","target":"https://saliu.com/ScreenImgs/lotto26.gif","line":186},{"title":"Lottery software checking how the strategy would have fared in the past lotto drawings.","target":"https://saliu.com/ScreenImgs/lotto27.gif","line":200},{"title":"The same lottery data file is required to check the strategy combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto28.gif","line":202},{"title":"The same biggest lotto is required to check the lottery strategy hits in the past.","target":"https://saliu.com/ScreenImgs/lotto29.gif","line":204},{"title":"The lotto software can enable special inner or innate filters.","target":"https://saliu.com/ScreenImgs/lotto30.gif","line":206},{"title":"Lottery software can screen input filters or use a filter file created by lotto strategy checking.","target":"https://saliu.com/ScreenImgs/lotto31.gif","line":208},{"title":"The best lotto software can use the hits file created by the check function.","target":"https://saliu.com/ScreenImgs/lotto32.gif","line":210},{"title":"The strategy hits in the past generates and counts lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto33.gif","line":212},{"title":"The greatest feature of the best lottery software: generate winning lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto34.gif","line":224},{"title":"Lottery software uses the same results file, with all past winning numbers.","target":"https://saliu.com/ScreenImgs/lotto35.gif","line":226},{"title":"Best lottery software generates combinations for the same lotto game format as W reports.","target":"https://saliu.com/ScreenImgs/lotto36.gif","line":228},{"title":"Generating lotto combinations can also employ special inner filters.","target":"https://saliu.com/ScreenImgs/lotto37.gif","line":236},{"title":"Software to generate lottery combinations can manually input filters or use ST filters files.","target":"https://saliu.com/ScreenImgs/lotto38.gif","line":238},{"title":"The lotto combinations can be displayed to screen or generated to a disk file.","target":"https://saliu.com/ScreenImgs/lotto39.gif","line":240},{"title":"_**lotto jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":252},{"title":"The lotto software has multiple options: favorite numbers, shuffle all lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto40.gif","line":254},{"title":"The user of lotto software can see the combination generating process.","target":"https://saliu.com/ScreenImgs/lotto41.gif","line":256},{"title":"Lottery software informs the player when all combinations were generated by the lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto42.gif","line":258},{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":279},{"title":"The lotto manual consists of a screen-by-screen lottery tutorials.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":281},{"title":"The Lottery Software Utility is an important part of Bright lotto software.","target":"https://saliu.com/ScreenImgs/lotto44.gif","line":283},{"title":"Lotto software has very useful additional programs: sums, check winners, frequency, skip systems.","target":"https://saliu.com/ScreenImgs/lotto45.gif","line":293},{"title":"Lottery software additional programs: calculate odds, lotto wheels, lexicographic order generation.","target":"https://saliu.com/ScreenImgs/lotto46.gif","line":295},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-b62.gif","line":297},{"title":"_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":301},{"title":"_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":302},{"title":"Read a concise book, tutorial of best lotto software, lottery software programs visual tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":304},{"title":"Forums","target":"https://forums.saliu.com/","line":306},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":306},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":306},{"title":"Contents","target":"https://saliu.com/content/index.html","line":306},{"title":"Help","target":"https://saliu.com/Help.htm","line":306},{"title":"Home","target":"https://saliu.com/index.htm","line":306},{"title":"Software","target":"https://saliu.com/infodown.html","line":306},{"title":"Search","target":"https://saliu.com/Search.htm","line":306},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":306},{"title":"The lottery manual consists of screen-by-screen lotto software tutorial in Windows Command Prompt.","target":"https://saliu.com/forum/HLINE.gif","line":308}],"metadata":{"created":"2025-07-24T22:10:02 (UTC +08:00)","tags":["software","lottery","lotto","manual","tutorial","help","tips","visual","screen","Bright software","eBook","book","online","Windows","operating systems","Command Prompt","editor"],"source":"https://saliu.com/forum/lotto-book.html","author":null}},
"smart_sources:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md": {"path":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08641519,-0.0074975,-0.02096996,-0.02167341,-0.01456191,0.02481737,-0.06203394,-0.02850826,-0.01293213,-0.00855569,-0.02050731,-0.01534481,0.01847773,-0.00154242,-0.00736165,-0.02404309,0.01971417,0.00754442,-0.02695069,-0.00735585,0.06478702,-0.03739849,-0.04836809,-0.08135593,0.04371957,0.02200932,-0.02632337,-0.04783708,-0.03945828,-0.19360311,0.02327031,0.04397431,0.02196096,-0.02960231,-0.0803925,-0.01976115,-0.02294458,0.02337392,-0.02887206,0.00339722,0.03135758,0.02074119,-0.01645928,-0.00620385,0.03158449,-0.06522892,0.00968562,-0.05057236,0.07379167,-0.01603165,-0.05071149,-0.02787725,0.04071771,-0.00002184,0.04811374,0.01506073,0.06493884,0.09166313,0.04702917,0.01952777,0.03747529,0.07022874,-0.18736233,0.09375986,0.00754735,-0.02759004,-0.01793803,-0.04256273,0.05034799,-0.01381524,-0.00075795,0.02239428,-0.03929373,0.06694601,0.01971709,-0.0614577,0.01147575,-0.08548125,-0.02976858,-0.05456118,-0.04189656,0.02293099,0.01336586,0.02506877,0.00185229,0.06827806,0.03543698,0.07155553,0.05577667,-0.06258354,0.03906973,-0.01977324,0.06407503,0.0558519,-0.0313923,-0.00173745,0.03473662,-0.01595701,-0.02467201,0.10514396,0.03558494,-0.02657533,0.00434404,0.00515088,0.06450781,-0.02841508,0.00581826,-0.02748168,-0.06613182,0.0549269,0.06173719,0.01660037,0.06003704,-0.05491804,-0.00642284,-0.02252364,-0.04339301,-0.02146447,0.02136847,0.01352319,-0.02934366,0.01504824,0.00823843,0.01710903,0.00413514,-0.01479364,0.03657359,0.04735792,0.02843354,0.00707029,0.06616042,0.03915397,-0.1337609,-0.04186447,0.02356008,0.00875676,-0.00382678,-0.04157349,-0.02974121,-0.004621,-0.0175017,-0.01208406,0.05701727,-0.0864793,-0.02222224,0.06221862,0.0239586,-0.00584224,-0.00709053,-0.01191941,0.02525057,-0.00018073,-0.02047347,-0.02943221,0.01258078,0.00461474,0.11493462,0.12292427,-0.03986265,-0.01240393,0.01025231,-0.03148016,-0.03081909,0.07913153,-0.05027124,-0.14897253,-0.02696518,0.05825292,-0.05898755,-0.07949316,-0.0336803,0.01387575,-0.06206555,0.03173986,0.09791064,-0.01650307,-0.03570998,-0.0452075,-0.02307716,-0.0149222,0.014842,-0.07044942,-0.05184909,0.01577915,-0.00285674,-0.07899178,0.03992345,-0.04718224,0.03223421,0.04765297,0.00696611,0.00323379,-0.01443188,-0.00709652,-0.02206467,0.02558515,-0.0744387,-0.06606238,0.03032446,-0.01485006,-0.01575489,-0.0069631,-0.02717958,-0.03159494,-0.00420963,0.00664125,-0.04908014,-0.04034235,0.10096588,0.0073551,-0.07615142,0.052731,0.06474968,0.05365903,-0.10171387,0.02688545,-0.02818259,0.0423451,-0.0150842,0.01828709,0.0340422,0.00286404,-0.05742848,-0.19010082,-0.02873906,-0.04872986,0.00803506,-0.01435475,-0.03293635,0.08289223,-0.0260441,0.07227549,0.06417266,0.12390094,-0.07417586,0.00295239,-0.00043273,-0.00294877,-0.02325052,-0.06185176,-0.06154994,-0.05745892,-0.00091619,0.00149831,0.03416658,-0.01339206,-0.05306114,-0.00179728,-0.04960533,0.11519485,0.02746119,0.01285716,-0.0237889,0.10384567,0.02537408,-0.00059201,-0.06112112,-0.03780525,0.06179636,-0.02053216,0.05593822,-0.01962367,-0.01789597,-0.04315453,0.02836409,0.00663025,-0.08042095,-0.01945028,0.00929204,0.04606877,0.020894,-0.03085999,0.02698629,-0.00268645,0.00989547,-0.01743357,0.03339382,0.00440466,-0.00466542,-0.06401865,-0.00446862,-0.00430314,0.03671975,0.01225384,-0.04763572,0.0418767,-0.04423549,0.06804205,0.00389155,-0.01540564,-0.03697542,0.06153009,-0.01175469,-0.05168638,0.05972233,-0.02084412,0.06899846,-0.02904498,0.04234203,0.08480119,-0.03183321,-0.00070614,-0.01787157,-0.0555316,-0.03162558,0.02047591,0.08139418,0.08107752,0.04629946,0.05180656,0.00392469,0.02567634,0.01289235,-0.01228147,0.01601484,-0.04271189,0.02322608,0.03615798,0.07049856,-0.24753453,0.01057468,-0.02970232,0.09603849,-0.02306374,0.00212017,0.07295434,-0.06592146,0.03848418,-0.05000911,0.04790709,0.03117985,0.02119869,-0.07052296,-0.0177552,-0.00623617,0.05583217,-0.0158578,0.03948608,-0.00854918,0.0298534,0.03952065,0.2339637,0.01172536,-0.01960958,0.06819553,0.00630247,-0.01309607,0.03251317,0.07158108,0.0099584,0.04707824,0.0854961,0.03140995,0.00443403,0.03934754,-0.05997048,0.01441454,0.03283316,0.01638757,-0.03704992,0.02669713,-0.0558292,-0.00999493,0.1128857,0.02366015,-0.03217417,-0.07207875,0.02199853,0.0467793,-0.07489487,-0.10822668,-0.09023593,-0.00549037,0.01172915,0.02215457,0.03702206,-0.0136145,-0.0063409,0.00145245,0.05646663,-0.03248593,0.11121169,-0.00192404,0.02156959],"last_embed":{"hash":"he8rag","tokens":462}}},"last_read":{"hash":"he8rag","at":1753423668883},"class_name":"SmartSource","last_import":{"mtime":1753366216136,"size":29618,"at":1753423416500,"hash":"he8rag"},"blocks":{"#---frontmatter---":[1,6],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps":[8,309],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps#{1}":[10,17],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps#{2}":[18,19],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)":[20,25],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{1}":[22,22],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}":[23,23],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{3}":[24,25],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son":[26,33],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son#{1}":[28,33],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!":[34,75],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{1}":[36,45],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{2}":[46,46],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{3}":[47,48],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{4}":[49,50],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{5}":[51,52],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{6}":[53,54],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{7}":[55,56],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{8}":[57,58],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{9}":[59,70],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{10}":[71,71],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{11}":[72,73],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{12}":[74,75],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu":[76,81],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu#{1}":[78,81],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files":[82,89],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files#{1}":[84,89],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files":[90,101],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files#{1}":[92,101],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6":[102,111],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6#{1}":[104,111],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file":[112,131],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file#{1}":[114,131],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis":[132,141],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis#{1}":[134,141],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>":[142,173],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{1}":[144,149],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{2}":[150,150],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{3}":[151,151],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{4}":[152,152],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{5}":[153,153],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{6}":[154,155],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{7}":[156,173],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report":[174,187],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report#{1}":[176,187],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past":[188,213],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past#{1}":[190,213],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations":[214,229],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations#{1}":[216,229],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters":[230,241],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters#{1}":[232,241],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options":[242,259],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options#{1}":[244,259],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities":[260,284],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities#{1}":[262,284],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages":[285,309],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{1}":[287,300],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{2}":[301,301],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{3}":[302,303],"#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{4}":[304,309]},"outlinks":[{"title":"**XCOPY** _**Command: The Best Backup Procedure, Method, Software in Windows**_","target":"https://saliu.com/best-backup.html","line":16},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":20},{"title":"_**Updates to the**_ **Bright** _**lotto, lottery, and horse racing software bundles**_","target":"https://saliu.com/forum/software-updates.html","line":23},{"title":"_**Software to Manage Lottery Data Files**_","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":72},{"title":"The lottery manual consists of a screen-by-screen lotto tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":74},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":78},{"title":"The lottery data is a file of past lotto results, drawings, draws.","target":"https://saliu.com/forum/HLINE.gif","line":80},{"title":"Lottery software book #2: Create files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":88},{"title":"Lottery software book #3: edit, maintain files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":100},{"title":"There is no better lottery manual than this page.","target":"https://saliu.com/ScreenImgs/lotto4.gif","line":110},{"title":"Bright lotto software require data files of 12 million lotto combinations. For 5-number lotto: a drawings file of at least 4 million lottery combinations.","target":"https://saliu.com/ScreenImgs/lotto5.gif","line":118},{"title":"Lottery software manual: Use all draws in your lotto winning numbers file.","target":"https://saliu.com/ScreenImgs/lotto6.gif","line":120},{"title":"Software book requests to enter the biggest number in lotto game; e.g. 45, 49, 54, 59.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":122},{"title":"Lotto software saves the winning reports or filter files to disk.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":124},{"title":"You too can write a good lotto book and lottery manual.","target":"https://saliu.com/ScreenImgs/lotto9.gif","line":126},{"title":"Lottery winning reports for the special filters named Ion: Lotto sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto10.gif","line":128},{"title":"Lotto software informs on the success of generating the reports for past lottery drawings.","target":"https://saliu.com/ScreenImgs/lotto11.gif","line":130},{"title":"The program shuffles the deck of lotto cards and arranges the lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto12.gif","line":138},{"title":"An MD past draw lotto report: Lottery sum-totals, sums, root sums.","target":"https://saliu.com/ScreenImgs/lotto13.gif","line":140},{"title":"_**lottery filtering or combination reduction**_","target":"https://saliu.com/filters.html","line":150},{"title":"_**Lottery Utility, Lotto Software**_","target":"https://saliu.com/lottery-utility.html","line":151},{"title":"_**Cross-reference strategy files**_","target":"https://saliu.com/cross-lines.html","line":152},{"title":"_**Strategy-in-reverse for lottery and lotto**_","target":"https://saliu.com/reverse-strategy.html","line":153},{"title":"_**Basics of a Lottery, Lotto Strategy Based on Sums (Sum-Totals), Odd / Even, Low / High Numbers**_","target":"https://saliu.com/strategy.html","line":154},{"title":"The function of lotto software that checks a lottery strategy in the past.","target":"https://saliu.com/ScreenImgs/lotto14.gif","line":156},{"title":"The lottery strategy checking software is based on the W winning reports.","target":"https://saliu.com/ScreenImgs/lotto15.gif","line":158},{"title":"The lottery strategy checking software input: screen prompt or disk files.","target":"https://saliu.com/ScreenImgs/lotto16.gif","line":160},{"title":"Skip all input screens for lotto filters. Generate lotto combinations with no favorite numbers.","target":"https://saliu.com/ScreenImgs/lotto17.gif","line":162},{"title":"Type the filter values for each filter in your lotto strategy: minimum and maximum levels.","target":"https://saliu.com/ScreenImgs/lotto18.gif","line":164},{"title":"Ion Saliu's book of lottery: Lotto filters work with sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto19.gif","line":166},{"title":"Nobody has ever written better visuallotto book, lottery ebook for software use.","target":"https://saliu.com/ScreenImgs/lotto20.gif","line":168},{"title":"Lotto software saves the data of a lottery strategy to a disk file ST.","target":"https://saliu.com/ScreenImgs/lotto21.gif","line":170},{"title":"Lottery software also creates a useful file to show how many lotto combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto22.gif","line":172},{"title":"Your lotto software informs of successfully creating the lottery strategy.","target":"https://saliu.com/ScreenImgs/lotto23.gif","line":180},{"title":"The lottery strategy report shows the filters and parameters of your lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto24.gif","line":182},{"title":"A loto strategy has numbers of hits in the past, frequency, skips of loterie drawings between hits.","target":"https://saliu.com/ScreenImgs/lotto25.gif","line":184},{"title":"The software shows lottery strategy reports for inter-related filters: W, MD, 1 to 6-number groups, sums.","target":"https://saliu.com/ScreenImgs/lotto26.gif","line":186},{"title":"Lottery software checking how the strategy would have fared in the past lotto drawings.","target":"https://saliu.com/ScreenImgs/lotto27.gif","line":200},{"title":"The same lottery data file is required to check the strategy combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto28.gif","line":202},{"title":"The same biggest lotto is required to check the lottery strategy hits in the past.","target":"https://saliu.com/ScreenImgs/lotto29.gif","line":204},{"title":"The lotto software can enable special inner or innate filters.","target":"https://saliu.com/ScreenImgs/lotto30.gif","line":206},{"title":"Lottery software can screen input filters or use a filter file created by lotto strategy checking.","target":"https://saliu.com/ScreenImgs/lotto31.gif","line":208},{"title":"The best lotto software can use the hits file created by the check function.","target":"https://saliu.com/ScreenImgs/lotto32.gif","line":210},{"title":"The strategy hits in the past generates and counts lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto33.gif","line":212},{"title":"The greatest feature of the best lottery software: generate winning lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto34.gif","line":224},{"title":"Lottery software uses the same results file, with all past winning numbers.","target":"https://saliu.com/ScreenImgs/lotto35.gif","line":226},{"title":"Best lottery software generates combinations for the same lotto game format as W reports.","target":"https://saliu.com/ScreenImgs/lotto36.gif","line":228},{"title":"Generating lotto combinations can also employ special inner filters.","target":"https://saliu.com/ScreenImgs/lotto37.gif","line":236},{"title":"Software to generate lottery combinations can manually input filters or use ST filters files.","target":"https://saliu.com/ScreenImgs/lotto38.gif","line":238},{"title":"The lotto combinations can be displayed to screen or generated to a disk file.","target":"https://saliu.com/ScreenImgs/lotto39.gif","line":240},{"title":"_**lotto jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":252},{"title":"The lotto software has multiple options: favorite numbers, shuffle all lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto40.gif","line":254},{"title":"The user of lotto software can see the combination generating process.","target":"https://saliu.com/ScreenImgs/lotto41.gif","line":256},{"title":"Lottery software informs the player when all combinations were generated by the lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto42.gif","line":258},{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":279},{"title":"The lotto manual consists of a screen-by-screen lottery tutorials.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":281},{"title":"The Lottery Software Utility is an important part of Bright lotto software.","target":"https://saliu.com/ScreenImgs/lotto44.gif","line":283},{"title":"Lotto software has very useful additional programs: sums, check winners, frequency, skip systems.","target":"https://saliu.com/ScreenImgs/lotto45.gif","line":293},{"title":"Lottery software additional programs: calculate odds, lotto wheels, lexicographic order generation.","target":"https://saliu.com/ScreenImgs/lotto46.gif","line":295},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-b62.gif","line":297},{"title":"_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":301},{"title":"_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":302},{"title":"Read a concise book, tutorial of best lotto software, lottery software programs visual tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":304},{"title":"Forums","target":"https://forums.saliu.com/","line":306},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":306},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":306},{"title":"Contents","target":"https://saliu.com/content/index.html","line":306},{"title":"Help","target":"https://saliu.com/Help.htm","line":306},{"title":"Home","target":"https://saliu.com/index.htm","line":306},{"title":"Software","target":"https://saliu.com/infodown.html","line":306},{"title":"Search","target":"https://saliu.com/Search.htm","line":306},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":306},{"title":"The lottery manual consists of screen-by-screen lotto software tutorial in Windows Command Prompt.","target":"https://saliu.com/forum/HLINE.gif","line":308}],"metadata":{"created":"2025-07-24T22:10:02 (UTC +08:00)","tags":["software","lottery","lotto","manual","tutorial","help","tips","visual","screen","Bright software","eBook","book","online","Windows","operating systems","Command Prompt","editor"],"source":"https://saliu.com/forum/lotto-book.html","author":null}},"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09220721,-0.0288876,-0.00655228,-0.01616215,-0.00843266,0.01224078,-0.05593689,-0.00129457,0.01589742,-0.00159916,-0.02231626,-0.01748899,0.02476683,0.02723147,0.00982354,-0.01816838,0.01511716,-0.02648879,-0.01936937,0.00175596,0.0794669,-0.00975487,-0.05119276,-0.0919436,0.04335137,0.02139428,-0.04370803,-0.04955674,-0.0139574,-0.15963991,0.00187122,0.05797912,0.01625738,-0.0124451,-0.0538347,-0.03694253,-0.04055113,0.03608174,-0.00507329,0.01290904,0.02567715,0.03203066,-0.03465396,0.00419008,0.0152406,-0.06277673,-0.01077948,-0.01756802,0.04494765,-0.01379838,-0.04813248,-0.0254602,0.01864213,-0.00598326,0.05280006,-0.00175765,0.07373983,0.06953754,0.05291288,0.04899015,0.06209991,0.04004051,-0.19491838,0.10599834,0.00217498,-0.00821019,-0.01080932,-0.02170269,0.03151405,-0.02520072,0.01019288,0.02185217,-0.016677,0.07124498,0.04234791,-0.02676758,-0.02650578,-0.07679997,-0.03354395,-0.06271958,-0.03758992,0.01845085,0.00005824,0.00701772,-0.00503123,0.05502703,0.04773571,0.08521606,0.06365521,-0.04358781,0.02656754,-0.0356187,0.07418931,0.05043928,-0.03853751,-0.01512282,0.03053241,-0.02235314,-0.03743406,0.11973269,-0.01509739,-0.0107743,0.00793569,-0.00529385,0.07353762,-0.01134341,0.02595201,-0.02826606,-0.05664985,0.06407006,0.0504479,0.04056314,0.0483005,-0.09492103,0.01125586,-0.01926483,-0.01299929,-0.01098427,0.04647159,0.02101404,-0.04751128,-0.00113847,-0.0157832,0.0113432,0.00507699,0.00603025,0.02842929,0.05716206,0.0266778,0.00248319,0.05018023,0.02608154,-0.12195102,-0.04750308,0.01628654,0.00891901,-0.00471055,-0.06569051,-0.01365602,-0.03049788,-0.02235743,-0.00471064,0.02194878,-0.08723124,-0.01775275,0.04828188,0.03531252,-0.00803615,0.00651386,-0.01910681,0.00822873,-0.02071794,-0.02809031,-0.01627188,0.01885151,0.00914698,0.14032519,0.10776002,-0.01294794,-0.00391394,0.0154039,-0.04325408,-0.03776988,0.09142775,-0.06090941,-0.160752,-0.02292612,0.06471056,-0.05338814,-0.07975156,-0.03300465,0.01455577,-0.05430496,0.0351787,0.12264387,-0.0019314,0.0089574,-0.03994654,-0.05058971,-0.01524034,-0.00018469,-0.06971928,-0.0605455,0.02796694,-0.03156821,-0.07416922,0.01054374,-0.06547031,0.02972822,0.0559332,0.00473423,-0.01558444,-0.0440506,0.01161125,-0.01933195,0.01743083,-0.05731281,-0.06939278,0.02633083,-0.03732884,-0.0052521,-0.00659145,-0.02661536,-0.00466544,-0.01102137,0.00755984,-0.03723516,-0.0400314,0.10785441,0.00873878,-0.05994506,0.04641393,0.06756898,0.03633516,-0.10227286,0.01242214,-0.03964743,0.03613091,-0.00876817,0.01413173,-0.00513107,-0.00220697,-0.07859448,-0.19780542,-0.00639649,-0.03639289,-0.02376438,-0.04139255,-0.02618958,0.06684285,-0.00462846,0.05443824,0.06499127,0.12219699,-0.08754794,-0.00376869,-0.01360874,0.01243494,-0.04601372,-0.04949341,-0.06382561,-0.03088984,0.01970182,0.01544549,0.01253241,-0.010767,-0.06013547,-0.00796044,-0.00987201,0.10289326,0.05123837,0.01005524,-0.03891326,0.11147681,0.04336832,-0.0009758,-0.10034744,-0.03050053,0.05537745,-0.03166728,0.06699733,-0.04306431,-0.03278975,-0.05274149,0.02682012,0.00700562,-0.05993018,-0.02496312,0.0005223,0.07961293,0.00275066,-0.04583798,0.04139304,0.0059338,-0.00078032,0.00585059,0.04116862,0.02848925,0.00569343,-0.07580169,-0.00231687,-0.01289876,0.02844708,0.01111893,-0.03631029,0.04256188,-0.03414327,0.0389464,0.02491183,-0.05488374,-0.06619423,0.04797693,-0.03260332,-0.02951296,0.07159582,-0.02059969,0.03520206,-0.02848846,0.01678563,0.07575401,-0.00778755,0.01509061,-0.02549521,-0.06770577,-0.0366944,0.0490889,0.067226,0.06701595,0.05177468,0.02683775,-0.01769538,0.04265826,-0.00559072,-0.02324056,0.02517524,-0.02331591,0.02705025,0.04461752,0.05863037,-0.23915303,0.02308174,0.01936011,0.0970858,-0.00299601,0.03002019,0.04229854,-0.07357638,0.01811812,-0.03154067,0.05836843,0.02383352,0.0159412,-0.06104955,-0.01840954,-0.00757554,0.05929369,-0.03608463,0.04619556,0.00003996,0.01811035,0.02225681,0.25580737,0.03504381,-0.04009088,0.04343239,0.01367276,-0.00647864,0.04847912,0.08383136,0.00709515,0.02807454,0.08295526,0.02819897,-0.00421484,0.03269485,-0.0435483,0.02926439,0.02551593,0.02499521,-0.06397115,0.01818612,-0.02652252,0.00443031,0.08342086,0.04875844,-0.05766259,-0.0580399,-0.00522376,0.01931977,-0.06249581,-0.1000144,-0.06850057,0.01905411,0.02054972,0.02903323,0.0335945,-0.00523449,-0.01050245,-0.02168806,0.05590799,-0.02139255,0.12070473,-0.01153237,0.04709486],"last_embed":{"hash":"5szd6r","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"5szd6r","at":1753423664712},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#---frontmatter---","lines":[1,6],"size":260,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09172577,-0.00572062,-0.01676212,-0.03225268,-0.03515987,0.05055887,-0.03999295,-0.03921613,-0.0243417,-0.01657348,0.00281201,-0.00331695,0.02217861,-0.00146383,-0.02720811,-0.00514485,0.02726196,0.00471881,-0.03025431,-0.00608137,0.06767754,-0.05226358,-0.04066081,-0.0867767,0.03250116,0.01403966,-0.03141817,-0.06918046,-0.06311078,-0.20721282,0.04381866,0.03904032,0.00350849,-0.03969228,-0.07213753,-0.02534181,-0.01262364,0.02696521,-0.06043351,-0.00191648,0.02135059,0.02368783,-0.00802372,-0.01121848,0.02525351,-0.09427544,0.01951135,-0.05950991,0.09063184,0.01362255,-0.02086524,0.00163841,0.05242966,0.01601785,0.0149345,0.01296981,0.04450325,0.0763841,0.03859803,0.01855667,0.02583494,0.0516469,-0.18626381,0.08952552,0.00954139,-0.00848498,-0.00566533,-0.03903781,0.02185692,0.00045203,-0.00896532,0.02373712,-0.0553518,0.07438274,0.03814508,-0.08504624,-0.00979688,-0.06321992,-0.02621525,-0.04696164,-0.05665956,0.02756873,0.02549993,0.00673948,-0.03104815,0.04731532,0.02456086,0.06653213,0.0668952,-0.05330591,0.0166657,-0.01526719,0.04879309,0.05196911,0.00717417,-0.00360451,0.03978151,-0.01092875,-0.03547482,0.10248224,0.02858765,-0.04220166,-0.03138199,-0.00471446,0.052273,-0.02860616,0.01166921,-0.01152947,-0.07066217,0.05795941,0.04605916,0.00525993,0.06080881,-0.03299523,-0.01925798,-0.02255334,-0.0457574,-0.02399919,0.03014025,0.00927444,-0.02754621,-0.00300414,0.02084219,0.0142485,0.00229268,-0.03700782,0.02853928,0.03680272,0.0156883,-0.00246821,0.06755117,-0.00094096,-0.12360325,-0.02684718,0.03496332,0.00411425,-0.0061721,-0.02985443,-0.02703024,-0.02927334,-0.02650798,-0.00017253,0.07072508,-0.0894785,-0.03043489,0.06429707,-0.00263628,-0.00522683,-0.01089837,-0.01672822,0.03756714,-0.00347304,-0.02868229,-0.03449876,0.02349701,0.00084903,0.10934796,0.1192681,-0.03108426,-0.01197314,0.00193621,-0.01915648,-0.00563568,0.08750789,-0.02577712,-0.11341861,-0.05001784,0.05808897,-0.05257897,-0.07007293,-0.02419299,-0.00190126,-0.08098564,0.04876148,0.06381007,-0.02168308,-0.05103099,-0.0361076,-0.03450577,-0.00266752,0.00549343,-0.04913503,-0.04068823,0.00125225,0.00388261,-0.06174451,0.0419821,-0.05395112,0.03479808,0.0333143,0.01600543,0.01456624,0.00319374,-0.0437995,-0.00326433,0.02353549,-0.0747171,-0.05227081,0.02644492,-0.00182474,-0.01576973,-0.01131023,-0.0092641,-0.02620925,-0.00238532,0.01177754,-0.06018721,-0.02821942,0.13250251,0.01437867,-0.05722623,0.038457,0.07046843,0.05386716,-0.10418715,0.04028622,-0.02293364,0.01997124,-0.00584495,0.04333948,0.03067433,0.02041618,-0.0668189,-0.20319588,-0.03121273,-0.0690491,0.01792555,-0.01507978,-0.04621867,0.07618837,-0.03441146,0.06409967,0.05269075,0.12776096,-0.08926594,0.02340516,0.00709586,-0.01307668,0.01905886,-0.05503902,-0.03998352,-0.06172364,-0.00257498,0.00387357,0.0460523,-0.03561836,-0.04736056,-0.00890606,-0.0342781,0.11537685,0.00804571,0.03929216,0.00729633,0.11113723,0.04216912,0.01251445,-0.05832937,-0.04594821,0.07329895,-0.00464079,0.05879119,-0.01424062,0.00310936,-0.07120601,0.01741138,0.0183252,-0.07246947,-0.02783525,-0.0019025,0.03006524,0.04702971,-0.02267462,0.0284148,-0.00665378,0.00025169,-0.01992318,0.02180991,0.01344522,-0.00022634,-0.04781996,-0.01804792,0.00922529,0.01719025,0.02324009,-0.0552915,0.03469926,-0.05633442,0.04763716,-0.0182879,0.00651859,-0.03597356,0.08891819,-0.01600176,-0.03882046,0.04690354,-0.01193824,0.05095377,-0.01325377,0.05542868,0.06835867,-0.01191265,-0.03157343,-0.04583115,-0.03899259,-0.03901354,0.02769734,0.08240416,0.06610243,0.03994761,0.0624483,0.04237856,0.03131989,0.02173392,-0.01043165,0.02213009,-0.04812921,0.00403561,0.03864339,0.07078742,-0.24141434,0.02348093,-0.03516594,0.09224813,-0.01119388,-0.03042123,0.05357083,-0.03340252,0.02668333,-0.03488123,0.05047952,0.03318049,0.02410787,-0.06532995,-0.02110443,-0.00819359,0.06083213,-0.02168385,0.04805405,0.00012408,0.03390018,0.04700564,0.22432806,-0.01072532,0.0005381,0.05003284,0.0191962,-0.00633536,0.05245569,0.0633247,0.03726585,0.0306224,0.0971138,0.00300927,0.00629056,0.02401594,-0.05970579,0.03834651,0.04313366,0.02051041,-0.05027594,0.04336801,-0.02910732,-0.04714411,0.12200826,0.0229039,-0.03018406,-0.07549503,0.03297834,0.07781715,-0.0915495,-0.09425794,-0.08987509,-0.03078437,0.0190428,0.00807528,0.020818,-0.03343542,-0.01728502,0.01816534,0.0571477,-0.00658397,0.12694329,0.00895988,0.01702083],"last_embed":{"hash":"1ar9319","tokens":472}}},"text":null,"length":0,"last_read":{"hash":"1ar9319","at":1753423664748},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps","lines":[8,309],"size":29322,"outlinks":[{"title":"**XCOPY** _**Command: The Best Backup Procedure, Method, Software in Windows**_","target":"https://saliu.com/best-backup.html","line":9},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":13},{"title":"_**Updates to the**_ **Bright** _**lotto, lottery, and horse racing software bundles**_","target":"https://saliu.com/forum/software-updates.html","line":16},{"title":"_**Software to Manage Lottery Data Files**_","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":65},{"title":"The lottery manual consists of a screen-by-screen lotto tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":67},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":71},{"title":"The lottery data is a file of past lotto results, drawings, draws.","target":"https://saliu.com/forum/HLINE.gif","line":73},{"title":"Lottery software book #2: Create files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":81},{"title":"Lottery software book #3: edit, maintain files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":93},{"title":"There is no better lottery manual than this page.","target":"https://saliu.com/ScreenImgs/lotto4.gif","line":103},{"title":"Bright lotto software require data files of 12 million lotto combinations. For 5-number lotto: a drawings file of at least 4 million lottery combinations.","target":"https://saliu.com/ScreenImgs/lotto5.gif","line":111},{"title":"Lottery software manual: Use all draws in your lotto winning numbers file.","target":"https://saliu.com/ScreenImgs/lotto6.gif","line":113},{"title":"Software book requests to enter the biggest number in lotto game; e.g. 45, 49, 54, 59.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":115},{"title":"Lotto software saves the winning reports or filter files to disk.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":117},{"title":"You too can write a good lotto book and lottery manual.","target":"https://saliu.com/ScreenImgs/lotto9.gif","line":119},{"title":"Lottery winning reports for the special filters named Ion: Lotto sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto10.gif","line":121},{"title":"Lotto software informs on the success of generating the reports for past lottery drawings.","target":"https://saliu.com/ScreenImgs/lotto11.gif","line":123},{"title":"The program shuffles the deck of lotto cards and arranges the lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto12.gif","line":131},{"title":"An MD past draw lotto report: Lottery sum-totals, sums, root sums.","target":"https://saliu.com/ScreenImgs/lotto13.gif","line":133},{"title":"_**lottery filtering or combination reduction**_","target":"https://saliu.com/filters.html","line":143},{"title":"_**Lottery Utility, Lotto Software**_","target":"https://saliu.com/lottery-utility.html","line":144},{"title":"_**Cross-reference strategy files**_","target":"https://saliu.com/cross-lines.html","line":145},{"title":"_**Strategy-in-reverse for lottery and lotto**_","target":"https://saliu.com/reverse-strategy.html","line":146},{"title":"_**Basics of a Lottery, Lotto Strategy Based on Sums (Sum-Totals), Odd / Even, Low / High Numbers**_","target":"https://saliu.com/strategy.html","line":147},{"title":"The function of lotto software that checks a lottery strategy in the past.","target":"https://saliu.com/ScreenImgs/lotto14.gif","line":149},{"title":"The lottery strategy checking software is based on the W winning reports.","target":"https://saliu.com/ScreenImgs/lotto15.gif","line":151},{"title":"The lottery strategy checking software input: screen prompt or disk files.","target":"https://saliu.com/ScreenImgs/lotto16.gif","line":153},{"title":"Skip all input screens for lotto filters. Generate lotto combinations with no favorite numbers.","target":"https://saliu.com/ScreenImgs/lotto17.gif","line":155},{"title":"Type the filter values for each filter in your lotto strategy: minimum and maximum levels.","target":"https://saliu.com/ScreenImgs/lotto18.gif","line":157},{"title":"Ion Saliu's book of lottery: Lotto filters work with sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto19.gif","line":159},{"title":"Nobody has ever written better visuallotto book, lottery ebook for software use.","target":"https://saliu.com/ScreenImgs/lotto20.gif","line":161},{"title":"Lotto software saves the data of a lottery strategy to a disk file ST.","target":"https://saliu.com/ScreenImgs/lotto21.gif","line":163},{"title":"Lottery software also creates a useful file to show how many lotto combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto22.gif","line":165},{"title":"Your lotto software informs of successfully creating the lottery strategy.","target":"https://saliu.com/ScreenImgs/lotto23.gif","line":173},{"title":"The lottery strategy report shows the filters and parameters of your lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto24.gif","line":175},{"title":"A loto strategy has numbers of hits in the past, frequency, skips of loterie drawings between hits.","target":"https://saliu.com/ScreenImgs/lotto25.gif","line":177},{"title":"The software shows lottery strategy reports for inter-related filters: W, MD, 1 to 6-number groups, sums.","target":"https://saliu.com/ScreenImgs/lotto26.gif","line":179},{"title":"Lottery software checking how the strategy would have fared in the past lotto drawings.","target":"https://saliu.com/ScreenImgs/lotto27.gif","line":193},{"title":"The same lottery data file is required to check the strategy combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto28.gif","line":195},{"title":"The same biggest lotto is required to check the lottery strategy hits in the past.","target":"https://saliu.com/ScreenImgs/lotto29.gif","line":197},{"title":"The lotto software can enable special inner or innate filters.","target":"https://saliu.com/ScreenImgs/lotto30.gif","line":199},{"title":"Lottery software can screen input filters or use a filter file created by lotto strategy checking.","target":"https://saliu.com/ScreenImgs/lotto31.gif","line":201},{"title":"The best lotto software can use the hits file created by the check function.","target":"https://saliu.com/ScreenImgs/lotto32.gif","line":203},{"title":"The strategy hits in the past generates and counts lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto33.gif","line":205},{"title":"The greatest feature of the best lottery software: generate winning lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto34.gif","line":217},{"title":"Lottery software uses the same results file, with all past winning numbers.","target":"https://saliu.com/ScreenImgs/lotto35.gif","line":219},{"title":"Best lottery software generates combinations for the same lotto game format as W reports.","target":"https://saliu.com/ScreenImgs/lotto36.gif","line":221},{"title":"Generating lotto combinations can also employ special inner filters.","target":"https://saliu.com/ScreenImgs/lotto37.gif","line":229},{"title":"Software to generate lottery combinations can manually input filters or use ST filters files.","target":"https://saliu.com/ScreenImgs/lotto38.gif","line":231},{"title":"The lotto combinations can be displayed to screen or generated to a disk file.","target":"https://saliu.com/ScreenImgs/lotto39.gif","line":233},{"title":"_**lotto jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":245},{"title":"The lotto software has multiple options: favorite numbers, shuffle all lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto40.gif","line":247},{"title":"The user of lotto software can see the combination generating process.","target":"https://saliu.com/ScreenImgs/lotto41.gif","line":249},{"title":"Lottery software informs the player when all combinations were generated by the lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto42.gif","line":251},{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":272},{"title":"The lotto manual consists of a screen-by-screen lottery tutorials.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":274},{"title":"The Lottery Software Utility is an important part of Bright lotto software.","target":"https://saliu.com/ScreenImgs/lotto44.gif","line":276},{"title":"Lotto software has very useful additional programs: sums, check winners, frequency, skip systems.","target":"https://saliu.com/ScreenImgs/lotto45.gif","line":286},{"title":"Lottery software additional programs: calculate odds, lotto wheels, lexicographic order generation.","target":"https://saliu.com/ScreenImgs/lotto46.gif","line":288},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-b62.gif","line":290},{"title":"_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":294},{"title":"_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":295},{"title":"Read a concise book, tutorial of best lotto software, lottery software programs visual tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":297},{"title":"Forums","target":"https://forums.saliu.com/","line":299},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":299},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":299},{"title":"Contents","target":"https://saliu.com/content/index.html","line":299},{"title":"Help","target":"https://saliu.com/Help.htm","line":299},{"title":"Home","target":"https://saliu.com/index.htm","line":299},{"title":"Software","target":"https://saliu.com/infodown.html","line":299},{"title":"Search","target":"https://saliu.com/Search.htm","line":299},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":299},{"title":"The lottery manual consists of screen-by-screen lotto software tutorial in Windows Command Prompt.","target":"https://saliu.com/forum/HLINE.gif","line":301}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07815994,-0.01004043,-0.0145861,-0.03740343,-0.01195929,0.03579299,-0.03004843,-0.02847464,-0.05344037,-0.03859917,-0.01201165,-0.00309698,0.02328097,0.01265069,-0.00620498,-0.03307225,0.02778566,0.01862717,-0.00674835,0.00267295,0.07231217,-0.04420296,-0.04220646,-0.07169598,0.0551967,0.01751158,-0.03461804,-0.08590148,-0.04035392,-0.18937685,0.03563812,0.0105296,0.00056512,-0.0240631,-0.06221516,-0.02024136,-0.01202267,0.02927051,-0.04490411,0.01762753,0.00394026,0.0348988,-0.01097188,-0.02053393,0.05122693,-0.09528243,0.03946352,-0.05333606,0.10064783,0.01348543,-0.02745743,0.00698944,0.03659338,-0.00330992,0.02169484,0.01545761,0.06529065,0.07587794,0.03309354,0.00525599,0.01794448,0.04093419,-0.17957978,0.08525606,0.00634444,-0.00259932,-0.00600488,-0.03004639,0.03723419,0.00003349,-0.00365356,0.00683479,-0.05968622,0.08018371,0.04055863,-0.07724466,-0.01048435,-0.08120868,-0.04081853,-0.0475219,-0.04139828,0.03552735,0.02739467,0.01938963,-0.02274521,0.05285242,0.02568295,0.06291116,0.06348773,-0.03989571,0.0715908,-0.02089353,0.04059275,0.06289721,-0.02847966,-0.00573895,0.041267,-0.00915858,0.00298844,0.12291359,0.03211731,-0.03907227,0.00394904,-0.00449729,0.05644897,-0.04001065,0.02131581,-0.01281833,-0.04626692,0.0583213,0.0438445,0.00688392,0.03926124,-0.02947405,-0.01634701,-0.0239102,-0.03214564,-0.02040963,0.02907141,0.01742744,-0.03948342,-0.00545872,0.00275254,0.02168714,0.01173736,-0.04081885,0.01125307,0.04036608,0.01280515,0.00898676,0.08255112,0.01273895,-0.1314414,-0.04679732,0.0209418,-0.00300944,0.01417415,-0.02542242,-0.018995,-0.02317648,-0.02363766,0.00060821,0.0366626,-0.07332019,-0.0301584,0.07900979,0.00885927,0.00397789,-0.00561562,-0.03605851,0.02948284,-0.00524645,-0.03210427,-0.04143451,0.01784492,0.02009026,0.10343713,0.0998021,-0.03453238,-0.00287411,-0.01366257,-0.01677722,-0.02262174,0.10230464,-0.03375871,-0.12830862,-0.06127049,0.04650205,-0.05167843,-0.06864341,-0.02159153,-0.01362141,-0.07278791,0.04590421,0.08155201,-0.0029882,-0.03382797,-0.05475393,-0.02896828,-0.01864717,0.00582836,-0.07065811,-0.04534292,0.01658317,0.00453468,-0.05249467,0.02992239,-0.07706849,0.03597992,0.05328568,0.01234968,0.01874064,-0.01810973,-0.01741564,-0.00360811,0.02177354,-0.09575272,-0.06385299,-0.00505152,-0.0050768,0.00010767,0.00375944,-0.0280687,-0.02154019,-0.00395694,0.01018287,-0.06589293,-0.012343,0.12010073,0.01624877,-0.07871134,0.06268095,0.07803795,0.03993352,-0.0820272,0.0235586,0.00506625,0.0235862,-0.00746353,0.04433208,0.04155531,0.01076589,-0.05741748,-0.20877461,-0.04481962,-0.07228579,0.02809081,-0.009521,-0.05329305,0.0705773,-0.03301627,0.05327091,0.07248464,0.1297451,-0.08917686,0.02570276,0.02223248,-0.02318568,0.01470408,-0.06199094,-0.03776069,-0.08442305,-0.00696029,-0.00540308,0.03079133,-0.02852144,-0.04031296,0.0011275,-0.00642168,0.1076314,-0.01023208,0.05029602,-0.0014012,0.12136471,0.04342445,-0.01112308,-0.05743007,-0.04192341,0.06189403,-0.01201056,0.03676231,-0.01765058,-0.00140677,-0.02505911,0.01476397,0.05328397,-0.06194701,-0.03674094,-0.00880581,0.03266145,0.0390931,-0.02325067,0.01290389,-0.00926641,0.01077549,-0.02333825,0.01180732,0.00808775,0.04609537,-0.04588301,-0.01149949,-0.00843824,0.02485535,0.03463598,-0.02934951,0.02928269,-0.06229228,0.05706988,-0.01295563,-0.0099107,-0.04300474,0.0910168,-0.01687149,-0.04752064,0.05320665,-0.02374399,0.06635376,-0.02001572,0.04946874,0.06891488,-0.02676946,-0.04111278,-0.0508779,-0.05675752,-0.02390752,0.01727583,0.04960525,0.07660224,0.05841196,0.04746288,0.00482797,0.01582544,0.02474988,0.00264455,0.02193689,-0.06758273,0.0150899,0.05028408,0.06317534,-0.25114763,0.02466175,-0.0517448,0.1227625,-0.00992631,-0.01248957,0.04708121,-0.05370513,0.02183159,-0.02724057,0.06287658,0.02169823,-0.00709232,-0.05757017,0.00599846,0.01120687,0.06004603,-0.04768775,0.05531302,-0.01670523,0.02394754,0.01920317,0.21130323,-0.00086663,-0.00583008,0.05709231,0.01681679,-0.00723186,0.04339259,0.04887546,0.03529479,0.01886863,0.08153202,0.01996032,0.00435861,0.02760675,-0.04508311,0.04581867,0.04102067,-0.00183589,-0.04950561,0.02690804,-0.03779017,-0.01515688,0.11727893,0.01702089,-0.05236789,-0.07687002,0.02007214,0.0657365,-0.08187599,-0.09673873,-0.08117191,-0.01792855,0.00277743,0.0094081,0.04962303,-0.01714806,-0.02799951,0.01980196,0.06474413,-0.01999964,0.11818917,-0.00351416,0.02469848],"last_embed":{"hash":"q6mwvo","tokens":181}}},"text":null,"length":0,"last_read":{"hash":"q6mwvo","at":1753423664894},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps#{1}","lines":[10,17],"size":555,"outlinks":[{"title":"**XCOPY** _**Command: The Best Backup Procedure, Method, Software in Windows**_","target":"https://saliu.com/best-backup.html","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09471066,-0.0413251,0.01681669,0.007671,-0.00944068,0.04200006,-0.05785442,-0.02663053,0.0220514,-0.00875654,-0.01749617,-0.01579016,0.01680232,0.0068873,-0.00688619,-0.00612738,0.0332096,0.00402469,-0.03463586,-0.04398251,0.04209739,0.00433887,-0.04846912,-0.05636571,0.03457257,0.01472962,-0.03936371,-0.02953692,-0.0420079,-0.18168612,-0.00898802,0.06099948,0.00613678,-0.02337846,-0.08607881,-0.03478854,-0.02856213,0.024385,-0.00662844,0.02339053,0.04822678,0.01879633,-0.05403819,-0.00765902,0.0104293,-0.05355529,-0.00137692,-0.0133282,0.06887885,-0.07024958,-0.05938178,-0.02868532,0.0133836,0.00541683,0.05717938,0.01395351,0.08552346,0.11709414,0.03504455,0.02031011,0.05993502,0.0835822,-0.18616955,0.10025196,-0.00235147,-0.02260768,-0.0185603,-0.03535487,0.06279279,0.02020296,0.0029351,0.02996108,-0.02446377,0.05770917,0.0188598,-0.04171919,0.01513031,-0.0524306,0.00096235,-0.03634891,-0.05250703,0.00705437,0.0096421,0.00537959,0.04877701,0.0348209,0.04321584,0.06106156,0.05570583,-0.08460694,0.00691971,-0.01720753,0.06001646,0.05905417,-0.04425138,-0.00664556,0.02592535,-0.04858312,-0.00005027,0.12665178,0.01825436,-0.01022611,-0.00949094,0.04588963,0.05383727,-0.01845038,-0.00928482,-0.02243216,-0.06406759,0.0744259,0.06898106,0.0337353,0.04187463,-0.0481548,-0.00382424,-0.02323567,-0.05820398,-0.01878439,0.01621613,0.01371686,-0.0469128,0.0065733,0.04753515,0.02603044,-0.01008086,-0.00066941,0.05717583,0.04263937,0.02013705,0.03984996,0.045772,0.08314516,-0.13541278,-0.05473643,0.01645971,0.02371908,0.00750983,-0.01950805,-0.00986417,0.05420563,-0.03788693,-0.05640511,0.0886678,-0.11104986,-0.02305348,0.07405707,0.00305518,0.02296382,0.00543721,0.00953388,-0.01389476,0.0096542,-0.00822032,-0.0077664,0.00717447,-0.00842344,0.10937521,0.0800667,-0.04700379,0.00708581,0.00679149,-0.03866974,-0.03556333,0.11460404,-0.06102876,-0.12585393,-0.02289323,0.04405774,-0.03973634,-0.08059233,-0.03066452,0.05173397,-0.07289382,0.01963419,0.11869115,-0.03539963,-0.03601449,-0.040084,-0.01455252,-0.01829664,0.02558199,-0.04426952,-0.05471062,0.03227458,-0.0189108,-0.08922375,0.01738802,-0.03703834,0.0375336,0.04369508,-0.03158199,-0.00694004,-0.01818474,-0.02537778,-0.05513454,0.03046547,-0.06513587,-0.04640408,0.04880832,-0.0217154,-0.02188622,0.02466315,-0.00924452,-0.02299458,-0.00033568,0.01276443,-0.05808242,-0.06959172,0.07137393,-0.00386625,-0.06058922,0.04403275,0.05527147,0.03850135,-0.0905569,0.03289321,-0.00354739,0.05331044,-0.01963644,0.00044787,0.04276172,0.01138823,-0.03195022,-0.19602576,-0.01452632,-0.01638114,-0.0118252,-0.00067323,-0.01906233,0.07209308,-0.0070785,0.04657327,0.09323367,0.11719022,-0.09345863,0.01112573,-0.01205683,0.02949478,-0.03854385,-0.05730424,-0.09098005,-0.03422045,0.00572824,-0.00290798,-0.00389791,0.00542718,-0.0789867,0.00669029,-0.04592676,0.10332522,0.01414766,-0.02844298,-0.03933037,0.04457159,0.00349976,-0.0199808,-0.06901186,-0.00521947,0.05261914,-0.03488235,0.03318682,-0.01754142,-0.02088164,-0.02674476,0.03922591,-0.02273597,-0.07563122,-0.00440167,0.01400608,0.06512683,-0.00010231,-0.009196,0.02143242,0.01662473,0.00742895,0.02471517,0.04675562,0.00672358,-0.02107916,-0.04727173,-0.00351626,-0.00640146,0.05065228,-0.01323552,-0.05037671,0.03860924,-0.03164967,0.07470198,0.02186012,-0.01541486,-0.08562209,0.04452102,-0.03198507,-0.03465571,0.05297115,0.00459601,0.053511,-0.0176172,0.04898556,0.05262061,-0.04070847,0.03460469,0.0152593,-0.05636238,-0.05871679,0.03352775,0.0813354,0.07134286,0.02897541,0.04010107,-0.02590421,0.01251934,0.01099198,-0.01244178,0.02419757,-0.0295797,0.03522384,0.04625335,0.07370581,-0.23524152,0.00327615,-0.00292423,0.04264748,-0.02749642,0.013823,0.07027409,-0.07496808,0.03690034,-0.06251629,0.07507947,0.0234078,0.01766681,-0.07301809,-0.02158236,-0.00680543,0.0919296,-0.02839875,0.03454775,-0.00457916,0.02174322,0.05244511,0.23358719,0.02577751,-0.0252395,0.04904488,-0.01473103,-0.00068832,0.02347674,0.05728213,0.00627177,0.03371289,0.06341684,0.02856971,-0.00696147,0.06447641,-0.05059,-0.00426635,0.02053072,0.03892837,-0.04176747,0.02156158,-0.06740067,0.0355414,0.08672601,0.02317303,-0.04810045,-0.07949089,0.00797298,0.00845983,-0.07055481,-0.10978176,-0.08039869,-0.01573022,0.03439794,0.02976582,0.01532798,-0.01839981,-0.00922354,-0.01575689,0.07802475,-0.04530932,0.06425627,-0.02984858,0.01604622],"last_embed":{"hash":"p4ciy2","tokens":172}}},"text":null,"length":0,"last_read":{"hash":"p4ciy2","at":1753423664946},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)","lines":[20,25],"size":466,"outlinks":[{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Updates to the**_ **Bright** _**lotto, lottery, and horse racing software bundles**_","target":"https://saliu.com/forum/software-updates.html","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08069208,0.00224208,0.00216294,-0.05399556,-0.05560819,0.04004938,0.00091853,-0.03189061,0.00888879,-0.0112052,0.01490649,0.00139755,0.03237898,0.00194403,-0.00821382,0.01232604,0.00333744,-0.01142512,-0.06925293,0.00907802,0.06723911,-0.07011587,-0.04016779,-0.10726345,0.00914593,0.01874053,-0.02225601,-0.05131695,-0.05155751,-0.22536992,0.0766089,0.04738634,0.02183416,-0.07228312,-0.03305232,-0.05496515,-0.02010312,0.05755071,-0.1014291,-0.02857519,0.05308029,0.02613861,0.01447405,0.01389447,0.00215465,-0.07707785,-0.00279497,-0.0468243,0.05663703,0.01838036,-0.02045565,0.00090762,0.0738391,0.05279974,-0.00841503,-0.01829654,0.01736604,0.03333886,0.04508597,0.03311801,0.04316255,0.02326564,-0.17973746,0.06468364,0.0135006,0.04134091,-0.01627133,-0.04666558,-0.01611339,0.02989657,-0.01658,0.01659386,-0.02011878,0.08033986,0.02779743,-0.05961706,-0.02494815,-0.0542156,-0.02970722,-0.02930491,-0.08003488,0.00335337,-0.03006834,-0.00028873,-0.10066912,0.04880301,0.00571718,0.03870557,0.05633099,-0.00806175,-0.02941573,-0.00694655,0.06481483,0.01723629,0.05518154,0.00883087,0.05615467,-0.0007913,-0.08929189,0.09522569,0.02681524,-0.03736961,-0.05476367,-0.01873355,0.0540378,0.00937191,0.00310812,-0.01364479,-0.07167271,0.04404161,0.05526158,0.00857972,0.0766422,-0.03655477,-0.00667009,-0.0551155,-0.01873257,-0.02688106,0.00911073,0.02516309,-0.03836228,-0.00061495,0.01509781,-0.01517441,0.02110663,-0.03455522,0.01477773,0.06626716,0.02914749,-0.0144048,0.05351555,-0.03096606,-0.09857596,0.00136936,0.04146671,0.01144966,-0.01340612,-0.04700875,-0.01749626,-0.0263946,-0.03894194,0.00060764,0.04056507,-0.07143109,-0.02202454,0.0499116,0.01468059,-0.00979228,-0.01843124,0.02043855,0.03077759,0.00533044,-0.02648572,-0.05309018,-0.01045949,0.00453719,0.10051308,0.13475125,-0.01806031,-0.04018151,0.00764535,0.00126241,0.01121316,0.0726955,0.00745717,-0.09792873,-0.01956412,0.05268323,-0.01865501,-0.0763648,-0.02201434,0.01365041,-0.08140597,0.03386145,0.04186107,-0.02532216,-0.08442924,-0.00126153,-0.01433053,0.02828651,0.02733815,-0.0493278,-0.01269981,-0.02088452,0.00393153,-0.07103036,0.04391152,-0.01913736,0.02728203,0.00293202,0.02960675,0.04004486,0.01930518,-0.05945317,-0.00479539,-0.02519234,-0.01314593,-0.03443929,0.04818552,-0.03204821,-0.02167499,-0.04237396,-0.00066298,-0.00180752,-0.0314449,0.01137875,-0.04803102,-0.01625002,0.1397799,0.02905821,-0.03408092,-0.00598105,0.03535482,0.06938383,-0.11960784,0.0289139,-0.02730349,0.0047043,0.00454751,0.05054391,0.00566289,0.04874014,-0.06985763,-0.20586985,-0.02952381,-0.07514749,0.01926578,-0.02837653,-0.03700835,0.03847693,-0.02962618,0.08184314,0.00707262,0.07357119,-0.05298228,0.01524947,0.02238382,-0.01097154,0.01794535,-0.04369119,-0.01714518,-0.02127377,-0.01026079,-0.02688837,0.0790111,-0.06323732,-0.07158876,-0.0239532,-0.02887413,0.13160557,0.04258727,0.06675044,0.01544946,0.09810822,0.0666837,0.03708582,-0.06554987,-0.0358269,0.08944597,0.0186315,0.03837227,-0.00488092,0.00542195,-0.14127199,0.03055611,-0.01923615,-0.07043901,-0.04531084,0.01241926,0.00773451,0.02788189,0.00116729,0.0533703,-0.01346856,-0.03723618,0.00900903,0.01990635,0.02503026,-0.04311635,-0.07102106,0.02726892,0.00313157,0.03283801,0.01108723,-0.05551433,0.03599799,-0.04317775,0.02790684,-0.00951789,0.01900346,-0.01886988,0.0820855,-0.03018426,-0.00131959,0.05955984,-0.0466115,-0.00714005,0.00848815,0.06205282,0.06629626,-0.01886306,-0.03860247,-0.0310469,-0.02074112,-0.04722445,0.01007747,0.09562454,0.02390741,0.01146145,0.06592821,0.06160252,0.01416471,-0.00789478,-0.016131,0.02114114,-0.03161594,-0.00620523,0.01219932,0.0495774,-0.22251005,0.03950912,-0.01827348,0.07486202,-0.02814979,-0.03216194,0.06027445,0.01066916,0.00468207,-0.01399462,0.03714456,0.03507818,0.05765769,-0.05928868,-0.00603734,-0.01757606,0.02807845,0.00349811,0.04316341,0.01761989,0.04691037,0.06612756,0.22897705,-0.02626631,-0.00637493,0.03525847,0.02198757,0.02417709,0.06391709,0.06187668,0.03895016,0.0205316,0.09001606,-0.01876398,0.00891528,-0.01527117,-0.04034767,0.05317821,0.03077161,0.02832346,-0.05117942,0.03647537,-0.02214976,-0.07526375,0.12996927,0.02089914,0.02542202,-0.06918323,0.0542069,0.09988502,-0.08170082,-0.08427552,-0.05926018,-0.04090905,0.03155695,0.02166717,-0.00638552,-0.04159689,-0.00803853,-0.00102342,0.03146632,0.01999404,0.10268885,0.01936372,-0.01306412],"last_embed":{"hash":"jb4fo8","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"jb4fo8","at":1753423664996},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son","lines":[26,33],"size":1800,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07993305,0.0006548,0.00234804,-0.053929,-0.05386646,0.04131975,0.0044983,-0.03277503,0.00850062,-0.01068005,0.01447933,0.00386664,0.03339029,0.00086204,-0.00987473,0.01392582,0.0033457,-0.01369402,-0.06853554,0.01072971,0.06876232,-0.07028246,-0.04170234,-0.10494173,0.00961306,0.01896587,-0.02076184,-0.05304243,-0.05288026,-0.22434133,0.07441454,0.04550356,0.01861317,-0.07444154,-0.0354714,-0.05780244,-0.02055306,0.05623779,-0.09829553,-0.0297572,0.05157607,0.02371644,0.01683082,0.0144686,0.00073523,-0.07697351,-0.00363357,-0.04840504,0.05521016,0.01828331,-0.0195311,0.00190835,0.07467948,0.05228596,-0.00989943,-0.01946012,0.01568739,0.03541161,0.04495554,0.03595292,0.04201356,0.02401158,-0.18144378,0.06408914,0.01151259,0.03923405,-0.01540047,-0.04418281,-0.01519686,0.03243184,-0.01399841,0.01573817,-0.02164371,0.08094766,0.02951788,-0.06085007,-0.02486569,-0.05193261,-0.03253298,-0.02601392,-0.07813744,0.00068849,-0.02778426,0.00207765,-0.0995252,0.04436283,0.00676957,0.03697837,0.05817758,-0.00774108,-0.02946479,-0.00560107,0.06381267,0.0167363,0.05541718,0.01064171,0.05625782,-0.00024238,-0.09151821,0.09519639,0.02694122,-0.03702763,-0.05563102,-0.02087682,0.05423551,0.00766682,0.00262081,-0.01322701,-0.07107478,0.04357269,0.05381209,0.00873523,0.07558324,-0.03394685,-0.00812617,-0.05405388,-0.01789746,-0.02555018,0.00787868,0.02401919,-0.03795008,-0.00042285,0.01581415,-0.0142169,0.01950571,-0.03440314,0.01254602,0.06681807,0.02872815,-0.01574944,0.05241071,-0.03066755,-0.10114158,0.00039817,0.0405877,0.01000578,-0.01473998,-0.04589933,-0.01690528,-0.02682031,-0.03877816,0.0046502,0.04288758,-0.07354196,-0.0226318,0.04909335,0.01227315,-0.01053811,-0.01850729,0.0185111,0.03031535,0.00724853,-0.02855612,-0.05101685,-0.01147855,0.00426318,0.09904794,0.1341998,-0.01403603,-0.0418146,0.0046134,0.00022343,0.01208065,0.07271513,0.00600746,-0.0947873,-0.01967232,0.05282065,-0.01935621,-0.0767433,-0.02367,0.01023111,-0.08327022,0.03728318,0.04290989,-0.02458746,-0.0864476,-0.00210525,-0.01657812,0.02928732,0.02797077,-0.04740246,-0.01295127,-0.02141181,0.00267544,-0.06814772,0.04496574,-0.01918857,0.02646138,0.00348943,0.02960253,0.04201845,0.01842138,-0.06075796,-0.00494971,-0.0257955,-0.01346025,-0.03548867,0.04994331,-0.03086145,-0.02292138,-0.04460848,-0.00096412,-0.00199862,-0.03118585,0.01141152,-0.04869065,-0.01746983,0.14085616,0.02794708,-0.03207698,-0.00499299,0.03527535,0.06984802,-0.11758452,0.0308331,-0.0265634,0.00561806,0.00303131,0.04969405,0.00430498,0.04779324,-0.07036734,-0.20632054,-0.03054919,-0.07622791,0.01861038,-0.02762357,-0.03621506,0.03691556,-0.02824864,0.07957049,0.00771772,0.07395352,-0.05501705,0.01510697,0.0241112,-0.01201616,0.01827901,-0.04213677,-0.01557192,-0.02224077,-0.01028853,-0.02649626,0.07949855,-0.06427705,-0.06953662,-0.02314393,-0.02765867,0.13258411,0.04081339,0.06650419,0.01579489,0.10033876,0.06847193,0.03671537,-0.0626938,-0.03497296,0.0890223,0.01826035,0.03966897,-0.00780766,0.00470377,-0.14128947,0.02927773,-0.01940601,-0.07006709,-0.04572684,0.01317694,0.00986724,0.02801873,0.00091296,0.05553503,-0.01421958,-0.03736926,0.00999559,0.0206921,0.02725773,-0.04323473,-0.06911457,0.0257172,0.00462065,0.03175401,0.01208537,-0.05768361,0.03599252,-0.0426683,0.02686275,-0.0116765,0.0206069,-0.01665913,0.08089609,-0.02855074,-0.00004446,0.05839808,-0.04635495,-0.00881692,0.00817769,0.06171137,0.06514487,-0.01662344,-0.04005774,-0.03177039,-0.0202417,-0.04500499,0.01095159,0.0987461,0.02224399,0.00835523,0.06709976,0.06205011,0.01474242,-0.00612511,-0.01552127,0.02037061,-0.03020777,-0.00770558,0.01149845,0.0468578,-0.22340962,0.03886118,-0.0198369,0.07358278,-0.02844873,-0.03427973,0.0580745,0.01475168,0.00443532,-0.01199265,0.03762465,0.03403763,0.05800552,-0.05800049,-0.00915611,-0.01832619,0.02864642,0.00404107,0.04384965,0.01840343,0.04920976,0.06661161,0.22898142,-0.02616592,-0.00510093,0.03525649,0.02217186,0.02389488,0.06120488,0.06342977,0.03820488,0.02159143,0.09062062,-0.02021522,0.00697431,-0.01453287,-0.04249342,0.05607629,0.03168892,0.02845078,-0.05118911,0.03790199,-0.02084264,-0.07556424,0.13136975,0.02179647,0.02448689,-0.0689771,0.05666954,0.10068148,-0.08148587,-0.08224574,-0.06099462,-0.04139994,0.03278344,0.0202096,-0.00818606,-0.03973909,-0.00733336,0.00092633,0.028127,0.02149457,0.10295289,0.02230492,-0.01295284],"last_embed":{"hash":"18zp1zt","tokens":456}}},"text":null,"length":0,"last_read":{"hash":"18zp1zt","at":1753423665146},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Like father, like son#{1}","lines":[28,33],"size":1773,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05536296,-0.02372272,0.02174319,-0.03527715,-0.02676505,0.01158837,-0.03468735,-0.01098259,-0.00112352,-0.00696355,0.012692,0.03454444,0.0253166,-0.00409058,0.01377543,0.0544042,0.00584364,0.00373146,-0.04715855,0.00690562,0.02936363,-0.06113451,-0.06335831,-0.09444478,0.02229918,0.00640537,-0.04172361,-0.0233307,-0.04735078,-0.22164555,0.03196847,0.004652,0.06778278,-0.06786302,-0.02199131,-0.05632559,-0.01541182,0.03646255,-0.08620609,-0.03615927,0.07917322,-0.00337007,-0.00134656,0.0280639,-0.00075065,-0.10743032,0.03691508,0.00167771,0.0646816,0.01213378,-0.01626651,-0.01851883,0.03435639,0.01044505,-0.01767622,0.00952603,0.04402078,0.08563957,0.05841773,0.01186262,0.05423397,0.04771114,-0.15416776,0.07810971,0.02058166,0.00237449,-0.00081593,-0.04208716,0.01200255,0.01289049,-0.01234758,0.009492,-0.03776514,0.07205161,0.01843959,-0.08575141,-0.0081804,-0.02250829,0.03129068,-0.05702332,-0.06174943,0.02354804,0.00917543,0.01791397,-0.02639358,0.04060274,0.04383352,0.06138084,0.0424398,-0.04839791,0.02396525,-0.00459878,0.05276146,0.02722123,-0.01564956,-0.02602766,0.03003196,-0.00015969,-0.04245747,0.1155163,0.0204621,-0.03435734,-0.03171291,-0.03766152,0.03196665,-0.01313945,0.00058054,0.02563138,-0.03991032,0.03417661,0.07962356,0.01266963,0.04668292,-0.08933578,0.00326466,-0.04566399,-0.04965502,-0.01665249,0.00783856,0.0122663,-0.03434664,-0.011136,0.0152557,0.00481147,0.00614299,-0.02104672,0.01672393,0.03813147,0.06377934,-0.02188464,0.02491492,0.00420286,-0.09441511,-0.00068649,0.05287367,0.04737667,-0.0001628,-0.0272218,-0.03433869,-0.02602067,-0.02665378,0.02160677,0.04248466,-0.07092584,0.0230064,0.03639203,-0.02295975,-0.01367258,-0.01884517,0.00615474,0.01710996,-0.03250786,-0.0253869,-0.03073914,0.02059001,-0.00949065,0.15863052,0.11455765,-0.03280971,-0.02717602,-0.0160446,-0.01251929,-0.00019577,0.11025301,-0.01662902,-0.07056922,-0.01087189,0.03434253,-0.03533907,-0.08214951,-0.04198159,0.03405079,-0.03414027,-0.00198459,0.07370163,-0.07375831,-0.10316587,-0.00938967,-0.01718946,-0.02426526,0.00853407,-0.05060695,-0.05139662,0.01143148,-0.01251459,-0.09438533,0.04603445,-0.00526649,-0.01592505,0.04373524,-0.0406494,0.05866623,-0.0310098,-0.0498406,-0.05028354,-0.01180588,-0.03312496,-0.05217524,0.04033533,-0.0300403,0.00544601,-0.02551432,-0.01304586,0.01197119,-0.03849349,0.03418612,-0.02202907,-0.06412653,0.14813121,0.00537942,-0.079741,0.01711086,0.08756876,0.066617,-0.13697925,-0.01006912,0.00352187,0.05691428,0.00470449,0.01136395,0.01701881,0.03152312,-0.05259158,-0.19273449,-0.00308676,-0.02524789,0.00225232,-0.01168788,-0.0759002,0.07209607,-0.03243196,0.03985134,0.0433721,0.12410845,-0.09536527,0.01815961,0.02673652,-0.00720857,-0.02167278,-0.06852045,-0.03980579,-0.03294709,0.02654478,0.00176737,-0.00220742,-0.01388424,-0.06942954,0.0226961,-0.01700563,0.11654764,0.02898305,0.05639871,0.00792499,0.0656917,-0.02138522,-0.00933921,-0.10481381,-0.02488567,0.05254143,-0.01368196,-0.0002447,0.01475582,0.01775926,-0.08927175,0.01395086,-0.03274938,-0.07107607,-0.01022642,0.00734303,0.00996705,0.08851462,-0.02677868,0.05897012,0.01283636,-0.0017276,-0.01751782,0.06215993,0.02677243,-0.05678243,-0.05373425,0.01968575,0.01638215,0.05304356,-0.01703534,-0.0809891,0.03576307,-0.03829941,0.05053882,0.02730202,-0.02401562,-0.06007193,0.09890517,-0.035984,-0.0207828,0.06070086,-0.03924795,0.04488435,-0.00446386,0.03422216,0.05509883,-0.01573651,-0.03945947,-0.02527835,-0.0130108,-0.0317505,0.02734105,0.05917059,0.04412068,0.01477445,0.02661747,0.02426102,0.04141078,-0.0217524,-0.02349137,0.00163087,-0.01783467,0.00474172,0.0132078,0.0586761,-0.21519253,0.00262944,0.01115983,0.10297597,-0.05652079,-0.02454489,0.10847444,-0.01161643,0.01231654,-0.03481254,0.01515054,0.00527836,0.03530874,-0.06904724,0.01710441,-0.0650094,0.08748074,0.03986299,0.0498229,-0.0102875,0.04650916,0.03657182,0.23750262,-0.01533457,-0.02770597,0.05406339,0.04926462,-0.0157583,0.05329478,0.06213076,0.04453419,0.02429126,0.07251987,-0.0329954,0.04352461,-0.00351453,-0.02992773,0.01575391,0.0227484,0.01652579,-0.05166351,0.01441884,-0.01416593,-0.01547822,0.11656804,0.01666359,0.03150917,-0.07709666,0.01672478,0.08389265,-0.07050999,-0.05127005,-0.07086417,-0.03477984,0.02172714,-0.00678573,0.01374656,-0.0045994,0.02678367,-0.02392031,0.03249765,-0.0398731,0.12582248,0.00757146,0.00796791],"last_embed":{"hash":"11xt8vo","tokens":436}}},"text":null,"length":0,"last_read":{"hash":"11xt8vo","at":1753423665278},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!","lines":[34,75],"size":4450,"outlinks":[{"title":"_**Software to Manage Lottery Data Files**_","target":"https://forums.saliu.com/lottery-strategies-start.html#software","line":39},{"title":"The lottery manual consists of a screen-by-screen lotto tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":41}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05599261,-0.02087735,0.02057983,-0.03298521,-0.02155983,0.0136892,-0.03334759,-0.01142051,-0.00179655,-0.00881925,0.01070618,0.03169965,0.02255416,-0.00435675,0.01520044,0.05686031,0.00418083,0.00214846,-0.05156238,0.00513556,0.02714952,-0.05688347,-0.06339824,-0.09129538,0.02047505,0.00910361,-0.04191095,-0.02078103,-0.04440124,-0.22318579,0.03260268,0.00480122,0.06802783,-0.06793538,-0.02432925,-0.05168828,-0.01431249,0.03594841,-0.08409564,-0.03636886,0.0822653,-0.00327331,-0.00376874,0.02857429,-0.0038975,-0.10796404,0.03813403,0.00385955,0.06142132,0.0095194,-0.01796257,-0.0165451,0.03710224,0.00817134,-0.01600526,0.00975026,0.04799122,0.08687267,0.05746057,0.01158705,0.05050884,0.0492487,-0.15408695,0.07824834,0.02111445,-0.00053747,-0.0026472,-0.03995129,0.01299685,0.011962,-0.0132904,0.01114491,-0.03958967,0.06810398,0.01782898,-0.0849345,-0.00575201,-0.02397321,0.03354939,-0.05246832,-0.0595089,0.02337252,0.01005048,0.01910979,-0.02404404,0.03782082,0.04503576,0.06312095,0.04025115,-0.04886439,0.0244318,-0.0018271,0.04985945,0.02577361,-0.01323796,-0.02519866,0.0273216,0.00444006,-0.04077457,0.11843644,0.02077837,-0.03759549,-0.02783649,-0.03682553,0.03213388,-0.01293809,0.00325385,0.02488386,-0.03594781,0.03173659,0.0787036,0.01264916,0.04489993,-0.09253342,0.00386928,-0.04689455,-0.05023077,-0.01734006,0.00691532,0.01570863,-0.03548222,-0.01100602,0.01904403,0.00542844,0.00333439,-0.02251886,0.0162589,0.03787947,0.06389104,-0.02579181,0.02437981,0.00406374,-0.09832072,-0.00456603,0.0518425,0.0463469,0.00025236,-0.0275263,-0.03454366,-0.02310099,-0.02470144,0.02179766,0.04640521,-0.07221381,0.02156264,0.03702334,-0.02502301,-0.01296879,-0.01756707,0.00871809,0.01698192,-0.03069652,-0.02293525,-0.03376747,0.02361961,-0.01066817,0.16038373,0.11719698,-0.0357693,-0.02986222,-0.01504547,-0.01295906,0.00185981,0.11199644,-0.02177829,-0.06943987,-0.00754576,0.02900631,-0.03750202,-0.07894788,-0.04044371,0.03215908,-0.03019512,-0.00231872,0.07267161,-0.07690605,-0.10153105,-0.0083581,-0.01641069,-0.02561371,0.00503444,-0.05153694,-0.05145607,0.01272521,-0.01263077,-0.09113512,0.04733973,-0.00277088,-0.01397439,0.04520058,-0.03950373,0.05825688,-0.03455465,-0.04935064,-0.04992551,-0.01121725,-0.03626784,-0.04770419,0.04134937,-0.03021538,0.00126723,-0.02658614,-0.01205218,0.0104505,-0.03738349,0.03289128,-0.01741994,-0.06229185,0.1460553,0.00354174,-0.08299591,0.02056321,0.08380806,0.06678677,-0.14004233,-0.01280898,0.00130607,0.06174032,-0.00027688,0.01176272,0.01770625,0.03002665,-0.05038974,-0.19407566,0.00168161,-0.02743865,0.00336806,-0.01196375,-0.08123305,0.07676484,-0.03020698,0.03944021,0.04533528,0.12440035,-0.09510661,0.01578076,0.02397078,-0.00471044,-0.02171965,-0.07026982,-0.04041812,-0.03052307,0.02955011,0.00122388,-0.00310305,-0.01667086,-0.06713424,0.02109265,-0.01669039,0.11389355,0.03415649,0.05438128,0.00447382,0.06453987,-0.02475841,-0.0086935,-0.10561224,-0.02346958,0.04981962,-0.01127538,0.00317069,0.01695519,0.01183859,-0.08500737,0.01444374,-0.03291635,-0.0702041,-0.0081317,0.00816267,0.00645291,0.08463497,-0.02822754,0.05499822,0.01188038,-0.00063869,-0.01791957,0.06073305,0.02461485,-0.05757666,-0.0575511,0.01642141,0.01661268,0.05147678,-0.01726793,-0.07865992,0.03718881,-0.04029625,0.05088092,0.02429515,-0.02431004,-0.05901728,0.09949511,-0.03214131,-0.02141007,0.05889778,-0.0379006,0.04279841,-0.00681411,0.03312363,0.05548921,-0.01945862,-0.0396977,-0.02361971,-0.01294266,-0.03225828,0.0297235,0.05528366,0.04561261,0.01325734,0.02761751,0.02277478,0.04109868,-0.0180275,-0.02204613,0.00318316,-0.01678388,0.00640365,0.01261149,0.061366,-0.2161345,0.00362014,0.00705304,0.10396388,-0.05659203,-0.02148533,0.10970025,-0.01947068,0.01216274,-0.03846527,0.01763215,0.00709035,0.03503975,-0.06968994,0.01299836,-0.0669982,0.09162156,0.04084164,0.04878809,-0.00984484,0.04493573,0.03364234,0.23831539,-0.01294795,-0.03023792,0.05418561,0.0475672,-0.01905338,0.05224187,0.0611526,0.04410138,0.02816967,0.07121754,-0.02843208,0.04676116,0.00176455,-0.02732663,0.01525264,0.02402072,0.01498326,-0.04713342,0.01435597,-0.01580608,-0.01723023,0.11956843,0.01858712,0.03306158,-0.07651809,0.01363747,0.08177988,-0.07051481,-0.05568301,-0.07099635,-0.03616712,0.01921451,-0.0071244,0.01272941,-0.00651302,0.02706798,-0.02349141,0.03389575,-0.03709272,0.12858391,0.00812489,0.01008257],"last_embed":{"hash":"1ygf0cg","tokens":408}}},"text":null,"length":0,"last_read":{"hash":"1ygf0cg","at":1753423665404},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{1}","lines":[36,45],"size":1584,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1150487,0.00080549,-0.01016685,-0.04355516,-0.0155173,0.00068253,-0.03934961,-0.02964022,-0.00308528,-0.03452244,0.01550619,0.00659289,0.01218856,0.01463787,-0.00059102,0.00465287,0.00961311,-0.00684511,-0.04297971,-0.00590253,0.03201937,-0.04498665,-0.04049994,-0.0595092,0.02321971,0.03952182,-0.03461698,-0.0214802,-0.03482037,-0.21965082,0.06900039,0.04897446,0.06624206,-0.03265135,-0.05321249,-0.04057494,-0.02333478,0.0479134,-0.04533264,0.00213725,0.10831744,0.00399882,0.01413123,0.01034069,0.0141545,-0.12745345,0.0485053,-0.01896652,0.07644252,-0.00679976,-0.01133928,-0.03460617,0.04495455,-0.03303628,-0.02180265,0.00737899,0.03158319,0.10839219,0.03953557,0.02201684,0.08005393,0.04460594,-0.16562641,0.10954686,-0.00737294,-0.01517152,-0.01659668,-0.05049591,0.04462961,0.00327372,-0.05579867,0.048667,-0.01893917,0.08574735,0.01785211,-0.0800419,-0.0066048,-0.0235146,-0.01993171,-0.03765317,-0.05762687,0.00718062,0.01396472,-0.00034597,-0.03643204,0.0570445,0.04004477,0.0610716,0.00738242,-0.04539254,0.03258421,-0.01713457,0.01396772,0.02807823,-0.00431123,-0.01777609,0.0374072,-0.00797968,-0.0202991,0.11066966,-0.00601254,-0.04825552,-0.00950171,-0.00298963,0.0550282,-0.00710618,-0.04446574,-0.00787092,-0.05533653,0.05077144,0.04074478,0.00713401,0.03765497,-0.06497948,-0.00301313,-0.04154539,-0.0782955,-0.05855247,0.05349926,0.00194473,-0.0260635,-0.00870713,-0.00043292,0.00632448,-0.02953226,-0.0153625,0.06510496,0.03734756,0.04428657,-0.02399263,0.02162319,0.02377585,-0.12706016,-0.03447951,0.03050822,0.05814841,0.01546101,-0.0207852,-0.00957728,-0.05125196,-0.02023631,0.02435565,0.07873289,-0.09366825,0.03927608,0.05052673,-0.02685423,-0.00733521,-0.01451605,-0.01334454,0.0139397,-0.04334931,-0.04998586,-0.027655,0.01839692,-0.00075041,0.1194933,0.10866916,-0.04505714,-0.01398135,-0.04045064,-0.0177771,0.01672503,0.06712156,-0.03477183,-0.07868696,-0.01161932,0.02625688,-0.05074679,-0.07003955,-0.02625506,0.01335167,-0.03943603,-0.0142203,0.08956682,-0.06128611,-0.05169729,-0.01062285,-0.01446928,-0.01330463,0.01425912,-0.04727503,-0.04858158,0.00371409,0.01106116,-0.08246244,0.03717383,-0.01847469,-0.01806573,0.05814064,-0.04698588,0.04415905,-0.00783236,-0.0329574,-0.00882478,-0.02868296,-0.04858933,-0.05494671,0.04097179,-0.03736052,0.03657813,0.00776142,0.00951125,0.03319344,0.00284715,0.00875064,-0.03878826,-0.03851868,0.14637904,-0.03405366,-0.07356481,0.01080011,0.11287502,0.05900433,-0.14801152,0.02605803,-0.01254029,0.04709311,0.02151433,-0.00257426,0.01797258,0.03167952,-0.01189358,-0.18873411,-0.00541342,-0.01347091,-0.01784894,-0.06152991,-0.05814376,0.06984127,0.00425484,0.04077353,0.02469455,0.12277102,-0.09130801,0.02366849,0.03494139,0.00495037,-0.02519798,-0.05551016,-0.04104495,-0.00832136,0.0147328,-0.00397215,-0.00257744,-0.00972766,-0.03957296,0.01931581,-0.0329164,0.11244062,0.00862977,0.02861622,-0.04230838,0.06392118,-0.00653814,0.00405646,-0.08116807,-0.03060442,0.05600016,0.00613796,0.04223242,0.01128952,0.00284048,-0.05888687,-0.00268439,-0.03797388,-0.10890209,-0.02047343,0.00296335,0.02796998,0.00279761,-0.03291524,0.00353157,0.01706101,-0.00108225,-0.02547837,0.05400272,0.02103361,-0.07754553,-0.05299258,0.02365822,0.02022894,0.00908515,-0.00932853,-0.04987219,-0.02369319,-0.04185769,0.06539221,0.05425458,0.00494458,-0.07172249,0.07715534,-0.03868993,-0.03821976,0.06419212,-0.02593617,0.03732575,0.001966,0.03542791,0.07726365,-0.00832787,-0.01513442,-0.01052532,-0.01777915,-0.05110062,0.03432072,0.08160074,0.05444058,0.0516109,0.06708058,-0.00458934,0.03320607,-0.03253537,-0.01214289,0.0063722,-0.00793428,0.02518215,0.03370503,0.04147528,-0.22795862,-0.0020823,-0.03124779,0.0767753,-0.05298421,-0.00856555,0.06518418,-0.02556957,0.01590944,-0.02595268,0.00153936,0.01101408,0.02931163,-0.02999474,0.02556324,-0.05763721,0.06597828,0.02883492,0.05056105,-0.04841153,0.03934316,0.01259806,0.22661987,-0.01299296,-0.03496381,0.08762356,0.03049203,-0.03145456,0.05687331,0.0776781,0.04565167,0.01989761,0.10513616,0.02057606,0.04823065,0.00586842,-0.02604333,0.01587746,0.01856851,0.03722848,-0.02867984,0.02558337,-0.04863919,-0.01592303,0.08339365,0.02090557,0.00683658,-0.06757445,0.01233561,0.08385409,-0.06781685,-0.05298819,-0.07379317,-0.00939484,0.00367399,0.02351996,0.00982927,0.05354038,0.01655202,0.03794754,0.09590954,-0.05693831,0.08584885,0.04014817,-0.00048978],"last_embed":{"hash":"17u6f8t","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"17u6f8t","at":1753423665519},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{9}","lines":[59,70],"size":1851,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0875424,-0.03449729,0.01713318,-0.04099818,-0.01260407,0.033534,-0.01214225,-0.00518192,0.01420481,0.00812953,-0.01084701,-0.008817,0.04187994,0.02370292,0.0118707,-0.0162171,0.03146882,-0.00590139,-0.04240897,-0.00181877,0.04707129,-0.01791644,-0.05659468,-0.08906785,0.06943592,0.04961436,-0.04814292,-0.03373621,-0.00921053,-0.19239058,0.0060111,0.02677728,0.0400448,-0.03703399,-0.05861647,-0.06690454,-0.03692307,0.04746046,-0.04926122,0.01534677,-0.00701632,0.02210298,-0.05025957,-0.01831692,0.00386031,-0.06657045,-0.00749599,0.0135225,0.05598101,-0.00736139,-0.05225405,0.00286542,-0.01423071,0.01114773,0.04475893,0.02122063,0.08648956,0.07061622,0.01960672,0.02396848,0.06021399,0.05351204,-0.15452665,0.09291023,-0.02415069,0.04383849,-0.00558331,-0.02308204,0.03359776,0.01709219,-0.005593,0.00985803,-0.02850831,0.07074741,0.0531361,-0.05771704,-0.045759,-0.06522034,-0.05092948,-0.02038214,-0.06220939,-0.00760462,0.02054331,0.02152739,-0.00757488,0.06620919,0.01210255,0.06146761,0.05746648,-0.04944611,0.0567676,-0.015812,0.03760827,0.0579905,-0.00601153,-0.00857867,0.03605686,-0.00762033,-0.01918736,0.13358378,-0.01837191,-0.03040158,0.01525256,0.00973975,0.04564011,-0.01636812,0.02238617,-0.01522972,-0.01605539,0.06395084,0.06300019,0.01678527,0.00490054,-0.06149129,0.0102476,-0.03628933,-0.04717129,-0.01745624,0.02524609,0.0259907,-0.05044118,-0.0032622,-0.01650219,0.02680774,-0.00125956,-0.03574163,0.01438939,0.04113198,0.02321328,0.00821277,0.07171495,0.07256489,-0.16426854,-0.04134397,0.01294396,-0.01314343,0.01405607,-0.0394653,0.00223289,0.02402546,-0.04933597,0.0168924,0.02558898,-0.10397782,-0.01111064,0.07422478,0.00301656,0.01749535,0.02300986,-0.04044741,0.01447703,-0.01331258,-0.0432988,-0.03427907,0.00249776,0.00974471,0.13446639,0.06018449,-0.03709491,-0.00490247,-0.05895461,-0.03241403,-0.02345104,0.09214787,-0.07244232,-0.16187902,-0.03549625,0.07601855,-0.02957778,-0.06251135,-0.02210732,0.03999167,-0.04595241,0.030942,0.10172833,-0.04290477,-0.03016005,-0.07281034,-0.03891993,-0.00292904,0.0201433,-0.06754614,-0.04706122,0.01610241,-0.02699923,-0.07738417,0.00162148,-0.05707911,0.01854043,0.04067226,-0.01025519,0.00335185,-0.0658028,-0.03171732,-0.01902484,-0.01184853,-0.02178862,-0.0566486,0.01176353,-0.06100314,0.01879227,-0.01358845,0.01733746,0.04325086,-0.0280367,0.0120299,-0.05581419,-0.02264027,0.13858779,0.01964684,-0.06831256,0.02415713,0.05302832,0.02922018,-0.08025911,-0.00476651,-0.00135562,0.03845476,-0.00969881,0.06081205,0.0140402,0.02028613,-0.06591122,-0.2136232,-0.03109634,-0.04644473,0.02109302,-0.0298275,-0.01382274,0.0515909,0.0263788,0.01068063,0.06397861,0.14062953,-0.09052818,-0.01744359,0.02445802,-0.00483753,-0.06284318,-0.06940532,-0.03202269,-0.04193311,0.01302819,-0.00860679,-0.01314337,0.01317426,-0.05665072,-0.00360226,-0.00685878,0.10978519,0.0135438,0.02621204,-0.00206348,0.08871273,0.01841468,-0.03504758,-0.08281425,-0.00858511,0.03467496,-0.02530993,0.05319022,-0.03607908,0.00572445,-0.0364675,0.03167448,0.01418567,-0.06195594,-0.02941683,-0.00717054,0.03075728,0.01243152,-0.02001959,0.05616352,0.02194997,-0.00921986,0.0285876,0.03775779,0.04922524,-0.00792127,-0.07506692,0.03137455,-0.01902168,0.05193085,0.03194962,-0.04499444,0.04915239,-0.00897253,0.03778093,0.0058724,-0.0464444,-0.05329297,0.03544554,-0.04824498,-0.02360547,0.08818944,-0.0207169,0.03858488,0.00183693,0.05936668,0.04747794,-0.03792075,-0.00837935,-0.02134988,-0.0732512,-0.0427028,0.04466801,0.05205623,0.05922384,0.06500067,0.07354821,-0.01017734,0.03311091,-0.0081701,0.01354686,0.02456016,-0.03235125,0.03207776,0.03880318,0.05817718,-0.24460153,-0.00101757,-0.00665175,0.08047867,-0.00874919,0.0093379,0.04121039,-0.05682664,0.04527308,-0.01708294,0.04726585,-0.00319294,0.02233414,-0.04646969,0.01324512,-0.01694648,0.06971063,-0.0293559,0.06013057,0.00924035,0.03772351,0.04492531,0.25329614,-0.01235129,-0.03031243,0.0474931,-0.00324343,0.00658552,0.04519782,0.05898742,0.00459469,-0.00378254,0.08751151,0.04221463,0.00138556,0.04733746,-0.00595677,0.02772909,0.02392674,0.01326509,-0.0480972,0.00264476,-0.0241524,0.02415451,0.08998996,0.02889168,-0.0438685,-0.0963685,0.00292371,0.03344802,-0.08131565,-0.0982179,-0.08232764,0.00169752,0.01577838,0.03124032,0.02204566,0.00826365,0.01302635,-0.02244982,0.06659181,-0.02194344,0.08264122,-0.00624459,0.01242424],"last_embed":{"hash":"5sl9qj","tokens":93}}},"text":null,"length":0,"last_read":{"hash":"5sl9qj","at":1753423665670},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##Start me up!#{10}","lines":[71,71],"size":244,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08267623,-0.01955153,-0.00023971,-0.03145784,-0.03158491,0.05285888,-0.03072547,-0.03564843,0.02350685,-0.00257882,0.01355875,-0.02106579,0.02802498,0.01378852,-0.00123404,-0.04208507,0.02290472,-0.02683229,-0.05840663,-0.01233923,0.0941649,-0.04059193,-0.06096171,-0.09135182,0.04609938,0.02180467,-0.0154843,-0.04156595,-0.00758046,-0.19490837,0.0164037,0.00896499,0.00871445,-0.03911259,-0.05995951,-0.02761083,-0.03170325,0.01998138,-0.07801951,0.03002095,0.04771437,0.02815722,-0.02544532,-0.02317647,0.03955173,-0.07283379,0.02294429,-0.01287992,0.09958234,0.00627897,-0.01700851,0.00829884,0.00396718,0.00910019,0.10024372,0.00783536,0.10104049,0.05221484,0.01825478,0.03105181,0.05094984,0.09625291,-0.17793162,0.09130119,0.00282783,0.02589321,0.00760391,-0.01082572,0.03696954,-0.01369401,0.00668516,0.03594977,-0.02220855,0.06355482,0.04691179,-0.05544952,-0.07475312,-0.06114922,-0.07972374,-0.0083231,-0.05579794,0.01985248,0.04444212,-0.00317368,0.01902599,0.07229371,0.01543869,0.05098253,0.07779975,-0.04265239,0.01887332,-0.02927781,0.04331859,0.07542723,-0.02465922,0.00836518,0.05078147,-0.03387716,0.02034844,0.12138723,-0.01280193,-0.01691236,0.02330377,0.02550248,0.05348149,-0.02927802,0.01130604,-0.01543526,-0.03768252,0.06479146,0.04460882,0.00970641,0.03104255,-0.02780846,-0.0266856,-0.0490326,-0.02873437,-0.03363521,0.03955422,0.02980816,-0.05036566,0.01902559,-0.00155893,0.02638506,0.01415775,-0.01890106,0.02348297,0.03599742,0.04987006,0.00390452,0.04842844,0.03564557,-0.15462823,-0.06467733,-0.02071961,-0.00973906,0.0116412,-0.0195554,0.00610211,-0.02036696,-0.03635124,-0.04247189,0.0409273,-0.08619776,-0.03498244,0.04944348,0.01425576,0.01215104,0.01192686,-0.01753191,-0.00098774,0.00234111,-0.03969184,-0.0294413,0.01077401,0.03186664,0.08253473,0.09373388,-0.03669533,-0.01523049,-0.01945891,-0.06008662,-0.02055987,0.09681903,-0.04136631,-0.14265651,-0.02014213,0.04995058,-0.01821383,-0.07734184,-0.03775448,0.0463369,-0.09263594,0.00044841,0.09623834,-0.02311836,-0.01687596,-0.06639747,-0.06086713,-0.011151,-0.03219401,-0.06183674,-0.03324655,0.01068328,-0.0253899,-0.09115618,0.00786022,-0.07775082,0.045902,0.08308894,-0.00368653,0.01057877,-0.04688055,-0.00443006,-0.06026932,-0.00311708,-0.05000553,-0.04650651,0.04037616,-0.03352753,0.00994987,0.00786241,-0.00164912,-0.00505808,-0.03523508,0.03338804,-0.03935821,-0.0416846,0.11343557,0.00390025,-0.05542007,0.02846777,0.06137335,0.07424483,-0.06609254,0.03089657,0.02650554,0.00633874,-0.03499261,0.02508076,0.02622149,0.00337505,-0.08756585,-0.19324757,-0.0147784,-0.05495361,-0.01505562,-0.00707265,-0.02468082,0.05724994,-0.0249345,0.04440634,0.07473806,0.11420632,-0.09435359,-0.01051309,0.04405805,-0.00992967,-0.01243552,-0.07881177,-0.04872566,-0.04065839,0.06430124,0.01151863,0.01652066,-0.01611462,-0.06673811,0.02985895,-0.02657608,0.11941382,0.03828547,0.02039587,-0.03071451,0.09749571,0.01173371,-0.02891957,-0.05065003,-0.02344654,0.01846938,-0.04563878,0.02409314,-0.03026504,-0.01699232,-0.04501829,-0.00399944,0.03573516,-0.05545919,-0.01708287,0.0102839,0.02473267,0.01510419,-0.04014111,0.05426711,0.04988436,0.00479434,0.0314374,0.04222874,0.04744974,0.01367962,-0.05545034,-0.00794509,-0.02249019,0.06899382,0.03674624,-0.03296387,0.04114346,-0.05445789,0.05715863,0.01329553,-0.03836894,-0.03341387,0.0578208,-0.02061752,-0.01894147,0.06304187,0.03227508,0.05336574,-0.00410125,0.03878372,0.08598264,-0.01495272,0.01899248,-0.020335,-0.04700897,-0.03169752,0.03025851,0.04866721,0.05776477,0.06936181,0.06662178,-0.00204994,-0.00614941,-0.01462835,-0.00051543,0.01280221,-0.04618853,0.04765827,0.01957411,0.06642029,-0.24996842,0.01534441,0.00661963,0.07666187,0.02287123,-0.00173933,0.03627669,-0.07065863,0.03088642,-0.04598833,0.08039107,0.0056822,0.03670395,-0.0988305,0.01531324,-0.01219768,0.03206733,-0.03074557,0.08972897,0.01103338,0.05453221,0.02163758,0.21886496,0.00447514,-0.04300167,0.01927709,0.00029233,-0.02515969,0.04681889,0.04281637,0.00115212,0.03040277,0.07522767,0.02701396,-0.01755165,0.05143629,-0.05170516,0.03212331,-0.00613748,-0.00604463,-0.04714644,0.01692884,-0.05100687,-0.01023764,0.0823373,0.02907774,-0.03904855,-0.06578093,0.01020016,0.03878684,-0.05810218,-0.08232684,-0.06616245,-0.00932338,0.03425803,0.02475849,-0.01436472,-0.02649329,-0.01155762,-0.01397495,0.04113567,0.02116047,0.06962604,-0.03121642,0.00998215],"last_embed":{"hash":"1vb67d4","tokens":156}}},"text":null,"length":0,"last_read":{"hash":"1vb67d4","at":1753423665699},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu","lines":[76,81],"size":329,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":3},{"title":"The lottery data is a file of past lotto results, drawings, draws.","target":"https://saliu.com/forum/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08183396,-0.01907234,-0.00019155,-0.032379,-0.02963985,0.05639645,-0.02874344,-0.0368333,0.02349157,-0.00470405,0.0120605,-0.01985128,0.02745428,0.01319698,-0.00043,-0.04414082,0.02492937,-0.02846758,-0.06049624,-0.01232681,0.09406523,-0.04081326,-0.0599795,-0.08859103,0.04484124,0.01810692,-0.0142087,-0.04157981,-0.00598908,-0.19448009,0.01597098,0.00344714,0.00644611,-0.0411011,-0.05837025,-0.02748757,-0.02978413,0.01780363,-0.08147441,0.03141961,0.04660392,0.02832275,-0.02361246,-0.02539819,0.04227039,-0.07205398,0.02322234,-0.01330702,0.1048889,0.00801174,-0.01394515,0.00988717,0.00434442,0.01039738,0.10000198,0.00806606,0.10032565,0.0509688,0.01829488,0.03175814,0.05192937,0.10104419,-0.17931248,0.08972381,0.00175542,0.02911001,0.00851005,-0.01256956,0.0339481,-0.0138343,0.0023272,0.03568465,-0.02229992,0.06314816,0.04487714,-0.05496519,-0.07655832,-0.05871908,-0.08375426,-0.00474987,-0.0542029,0.02005272,0.0467022,-0.00438543,0.01927265,0.07076792,0.01456111,0.05150673,0.07774375,-0.04169793,0.01939864,-0.02945293,0.04479618,0.07698441,-0.0246207,0.00575932,0.05060313,-0.03194521,0.02358238,0.12231507,-0.01867945,-0.01755653,0.02311778,0.02462843,0.05285861,-0.02797173,0.01099369,-0.01202205,-0.0372744,0.06561191,0.04286904,0.00951846,0.02578156,-0.02490888,-0.02651482,-0.0503107,-0.03240578,-0.03584417,0.0382317,0.03339141,-0.05182358,0.01795829,-0.00010384,0.02434824,0.01431131,-0.02027684,0.02444846,0.03344695,0.05057904,0.00472659,0.04600397,0.03117375,-0.15655521,-0.06607398,-0.02257122,-0.00954709,0.0148304,-0.02062853,0.00505584,-0.02386056,-0.03617004,-0.04410584,0.04429612,-0.08418124,-0.03397626,0.05140086,0.01114365,0.01014716,0.01042848,-0.01781602,-0.00277566,0.00433095,-0.03660981,-0.03003259,0.01028656,0.03397483,0.0812856,0.09259421,-0.03914664,-0.01327089,-0.02119221,-0.05868494,-0.02004042,0.09646152,-0.03834101,-0.13804235,-0.01886205,0.05131189,-0.01771511,-0.07519443,-0.03667578,0.04857155,-0.09518133,0.00121021,0.09593011,-0.02203041,-0.01797287,-0.0660563,-0.06521389,-0.01062309,-0.03389252,-0.06049424,-0.02996809,0.01052717,-0.02537649,-0.09069955,0.00753176,-0.07874481,0.04632506,0.08575041,-0.00522079,0.0155901,-0.0455125,-0.00580523,-0.05903339,-0.00301385,-0.0513541,-0.04499057,0.04188765,-0.03300092,0.00686155,0.00693623,-0.00018048,-0.00788532,-0.03668863,0.03105882,-0.03911969,-0.04372579,0.11307433,-0.00017165,-0.0542062,0.02914489,0.06146586,0.07515469,-0.06618444,0.03008833,0.02593424,0.00460618,-0.03910507,0.02586215,0.02879446,0.00508841,-0.08834836,-0.19197926,-0.01063842,-0.05616594,-0.01678083,-0.00476717,-0.02513908,0.05637114,-0.02462489,0.04577832,0.07861516,0.1088594,-0.0938466,-0.01473389,0.0450398,-0.00919771,-0.01064816,-0.07909701,-0.04812513,-0.04216252,0.06570929,0.00916473,0.01640402,-0.0185109,-0.06511473,0.03331975,-0.02752667,0.11679436,0.03642065,0.02250662,-0.03247402,0.098707,0.01226847,-0.03081728,-0.05020676,-0.01796173,0.01651755,-0.04413978,0.01926569,-0.02941325,-0.02070935,-0.04219013,-0.00676666,0.0360685,-0.05502231,-0.01337805,0.01015676,0.02571779,0.01172031,-0.0377374,0.05575001,0.05223481,0.00565589,0.02913557,0.04288467,0.04788851,0.01197568,-0.05280935,-0.00879513,-0.02255533,0.0683288,0.03713321,-0.0297172,0.03912554,-0.0557153,0.05990664,0.00923009,-0.03789867,-0.03343267,0.05971959,-0.01793371,-0.01829883,0.06497625,0.03222956,0.05526298,-0.0010124,0.03735534,0.08613135,-0.01459203,0.01953381,-0.02076296,-0.04378068,-0.0312349,0.03062042,0.0504091,0.05837518,0.0697946,0.06607281,-0.00356908,-0.00997145,-0.0169422,0.00045398,0.01047701,-0.04507105,0.05160635,0.0176667,0.0656768,-0.24824952,0.0167616,0.00442348,0.07531889,0.0232677,-0.00248553,0.03287069,-0.06981231,0.02984678,-0.04528707,0.08029789,0.00285489,0.03675781,-0.10019825,0.01315871,-0.01049649,0.02925984,-0.02841283,0.09387266,0.01095026,0.05400062,0.01919386,0.21845438,0.00243406,-0.04312598,0.01985682,-0.00090677,-0.02590695,0.04462019,0.04222441,0.00292313,0.03138626,0.07387627,0.0279958,-0.01690075,0.05603749,-0.05237257,0.0329349,-0.0105812,-0.00969885,-0.04561679,0.01738959,-0.05278972,-0.01140668,0.08235914,0.02706141,-0.03646407,-0.06601784,0.01027512,0.0408356,-0.05551117,-0.0797563,-0.06395155,-0.00840568,0.04035055,0.02450901,-0.01725079,-0.02845922,-0.00966975,-0.01267016,0.04176885,0.02389227,0.06693884,-0.03200352,0.01018476],"last_embed":{"hash":"bb48yf","tokens":154}}},"text":null,"length":0,"last_read":{"hash":"bb48yf","at":1753423665743},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##1.- The Main Menu#{1}","lines":[78,81],"size":306,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/ultimate-lotto-software-60.gif","line":1},{"title":"The lottery data is a file of past lotto results, drawings, draws.","target":"https://saliu.com/forum/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06315634,-0.02657425,-0.02845624,-0.04556483,-0.01352151,0.05911488,-0.01292791,-0.0323723,0.01127349,0.00439619,0.00381215,0.00875998,0.02536565,-0.00461785,-0.02661074,-0.0226846,0.02553173,-0.01828212,-0.05380087,0.02795013,0.05143479,-0.0644251,-0.04772619,-0.09121965,0.0387817,0.02358142,-0.01445303,-0.04135529,-0.05532239,-0.22596265,0.00760073,0.03094493,0.00260645,-0.0307272,-0.07944016,-0.06007936,-0.02506885,0.04799844,-0.02515765,0.00844843,0.02408592,-0.0095024,-0.00490361,-0.00849624,0.00797961,-0.08335311,-0.04673325,-0.03954514,0.10218571,0.00709334,-0.06238078,-0.00253344,0.02976978,0.00839612,0.0698369,0.000707,0.07844171,0.09200469,0.03797424,0.03302548,0.04416515,0.05641319,-0.15053527,0.07558101,-0.03593123,0.02145067,-0.03510643,-0.03957668,0.0223613,-0.02981587,-0.01582081,0.01844716,-0.06380699,0.08343146,0.03828507,-0.08814796,-0.02392837,-0.04005953,-0.04690993,-0.00084795,-0.08101792,-0.02138206,0.01815383,0.02379136,0.00339074,0.0477476,0.02637371,0.05042541,0.02675582,-0.06165913,0.03999557,0.00461742,0.03351667,0.04090532,-0.0065721,0.01552441,0.06638385,0.01607071,-0.00601461,0.11049558,0.01055034,-0.02864026,-0.00735734,0.03275292,0.02001321,-0.04591656,-0.02320848,-0.01929379,-0.02247652,0.05203534,0.07587511,0.01621409,0.01679129,-0.06796297,-0.00718723,-0.01583141,-0.04415642,-0.00490747,0.00800817,0.03776468,-0.04467106,0.00895155,-0.01623555,0.0382454,0.00812001,-0.03494545,0.05542048,0.02000896,0.05872521,0.0334602,0.03395147,0.04271244,-0.14748582,-0.05926614,0.00285097,0.02536246,-0.0063486,-0.00656099,-0.02218691,-0.0345799,-0.00251814,-0.00501571,0.06558036,-0.0753582,-0.01057057,0.0664258,-0.01105113,0.03817933,0.03109645,-0.03567498,-0.02391894,0.00449429,-0.06694677,-0.02939676,0.0278059,0.00354042,0.09733149,0.07703368,-0.03902865,0.00380736,-0.00811617,-0.06139871,0.01573937,0.08641376,-0.06222769,-0.09995034,-0.02281362,0.03814478,-0.02413112,-0.10363334,-0.01831548,0.00615657,-0.01764286,0.00989673,0.09838019,-0.04757571,-0.06312758,-0.08047388,-0.03179833,0.00104684,-0.02717644,-0.02986142,-0.02901454,-0.00493238,-0.03142022,-0.09829776,0.03495092,-0.03111751,0.0213313,0.06061201,-0.00355018,0.02743139,-0.04614046,0.00163338,-0.04386179,-0.00564525,-0.0211818,-0.02461674,0.02831166,-0.05691569,0.02816378,-0.01658774,0.02855009,0.01620874,-0.01980146,0.02766783,-0.02363675,-0.04383969,0.10073834,-0.02156978,-0.03632884,0.00916047,0.06158831,0.05750581,-0.09058731,0.04509105,0.02014365,0.02234906,0.00324151,0.03011325,0.01626264,-0.01395398,-0.03159492,-0.20486252,-0.02359403,-0.04881835,0.01689353,0.01544282,-0.03344776,0.02741125,0.00262371,0.02626061,0.10156808,0.13500226,-0.06963529,-0.03404923,0.02577227,-0.02282348,-0.02016126,-0.05942728,-0.03207975,-0.05304858,0.04959008,-0.00538907,-0.00593009,-0.02999309,-0.03508645,0.05395207,-0.02380764,0.13139088,0.02685276,0.00448039,-0.01221628,0.10251677,0.01726394,-0.04479835,-0.09911819,-0.00301761,-0.00474538,-0.05842053,0.09745143,-0.04729141,-0.0194981,-0.04831947,-0.00059288,0.00624977,-0.08520921,0.02052587,0.01189133,0.02550312,0.01978895,-0.03478616,0.0237521,0.03177615,0.01035364,0.04761074,0.03987231,0.05116946,-0.04788098,-0.06238917,0.01630295,-0.00788302,0.04494012,0.00119002,-0.07155155,0.06205055,-0.01100459,0.05486396,0.03980232,-0.01314463,-0.04150197,0.05394208,0.01671867,-0.00364599,0.07164048,-0.01519558,0.0520759,-0.0139023,0.0450904,0.08435502,-0.02435429,0.02452563,-0.01776512,0.00771619,-0.02604151,0.01995903,0.07581887,0.06248896,0.0506374,0.07289271,0.02052364,0.02964586,0.00161569,0.00227721,0.02945218,-0.01800062,0.04921262,0.00043552,0.03533959,-0.25292987,-0.01388872,-0.04764206,0.06646223,-0.00960782,-0.0192622,0.04797989,-0.00125626,0.04989906,-0.04119526,0.05889342,0.02576104,0.05518901,-0.12411489,-0.00258205,-0.01985368,0.02300384,0.02294146,0.07355168,-0.00149825,0.07910588,0.02740813,0.23866268,-0.02206576,-0.05802002,0.01568551,0.00242332,-0.02310766,0.04261995,0.05914016,-0.0167349,-0.00568175,0.06908301,0.04343988,-0.01125046,0.04097087,-0.06375806,0.04116319,-0.00866243,0.03038366,-0.0509598,-0.01593069,-0.0514372,0.01932088,0.08273081,0.04984042,-0.02391509,-0.0790043,0.02278167,0.06679707,-0.04298709,-0.07521977,-0.06407047,-0.01531487,-0.01040028,0.02412914,-0.01033522,0.00608136,0.02953379,0.00963623,0.06500502,-0.01762307,0.0820073,0.0112215,0.01411257],"last_embed":{"hash":"xq241f","tokens":339}}},"text":null,"length":0,"last_read":{"hash":"xq241f","at":1753423665787},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files","lines":[82,89],"size":1132,"outlinks":[{"title":"Lottery software book #2: Create files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06267985,-0.02707285,-0.03083619,-0.04624124,-0.01296656,0.0589416,-0.01556116,-0.03397323,0.01037705,0.00518344,0.00490484,0.00895751,0.02900749,-0.00524516,-0.02637975,-0.02068182,0.02404113,-0.01947386,-0.05230308,0.02871479,0.04960056,-0.06461945,-0.04414659,-0.09032429,0.03504246,0.02677518,-0.01591955,-0.04253567,-0.0553364,-0.22583032,0.00874148,0.03142439,0.00019911,-0.03112768,-0.07977426,-0.06491605,-0.02230059,0.04753233,-0.02318964,0.00687316,0.02055176,-0.00989881,-0.00637899,-0.01074838,0.00995193,-0.08584325,-0.04739539,-0.03888764,0.10527471,0.00562291,-0.06242356,0.00095155,0.03198316,0.00751384,0.07030784,0.00120371,0.07542901,0.09154771,0.03528184,0.03402358,0.04583224,0.05285443,-0.15058501,0.07594658,-0.0388492,0.02278742,-0.03544744,-0.04194108,0.02075345,-0.02892115,-0.01358203,0.01800804,-0.0650638,0.08514241,0.0372361,-0.09385788,-0.02140809,-0.03973454,-0.04449309,-0.00061533,-0.08311791,-0.02108998,0.02077246,0.02625439,0.00097621,0.04916105,0.02460715,0.05232788,0.02572623,-0.06246475,0.04214676,0.00241476,0.03084177,0.03836327,-0.0056406,0.01560216,0.06636481,0.01998762,-0.00475052,0.10934407,0.01151656,-0.02843671,-0.00732306,0.03463078,0.01919305,-0.04642648,-0.02552661,-0.01833453,-0.02406872,0.05305043,0.07870414,0.01867244,0.01588272,-0.06659675,-0.00551295,-0.01446216,-0.04281378,-0.00277855,0.00411783,0.03953259,-0.04331044,0.00875362,-0.01328139,0.03702231,0.01011832,-0.03718854,0.05868277,0.01739094,0.05806826,0.03077702,0.03178467,0.03986758,-0.1486239,-0.05841862,0.00182487,0.02461471,-0.00379959,-0.00596605,-0.02136099,-0.03654239,0.00022996,-0.00563522,0.06525612,-0.07413452,-0.01080147,0.06786732,-0.01165249,0.03798252,0.0304749,-0.03592169,-0.02355519,0.00702001,-0.06855873,-0.02974911,0.02818273,0.00199429,0.10184158,0.07411295,-0.04048737,0.00188382,-0.00870594,-0.06185587,0.01527889,0.08509677,-0.06549788,-0.09845015,-0.02134469,0.03685727,-0.02107721,-0.10334031,-0.01700617,0.00610461,-0.01909401,0.00926249,0.09811191,-0.04675202,-0.06429069,-0.08145455,-0.03061722,0.00394821,-0.02834073,-0.02570005,-0.02811215,-0.00697727,-0.02995361,-0.09897384,0.03233495,-0.03106756,0.02138205,0.06343859,-0.00482129,0.02998824,-0.04677241,0.00261825,-0.04314085,-0.00513071,-0.0214144,-0.02153791,0.02747036,-0.05900516,0.03145919,-0.01772504,0.02836139,0.01524876,-0.01895611,0.02520939,-0.02197721,-0.04261807,0.09734755,-0.02359941,-0.03326958,0.00665674,0.05880883,0.05402155,-0.08958431,0.04825979,0.01930438,0.02469784,0.00370541,0.03275767,0.01497013,-0.013067,-0.03060608,-0.20554823,-0.02254022,-0.04779846,0.01579939,0.0159137,-0.03213299,0.02382409,0.00339959,0.0234453,0.10186611,0.13321841,-0.07017419,-0.03443822,0.02579224,-0.0252422,-0.01866518,-0.05812306,-0.0302129,-0.05033202,0.04721333,-0.00523508,-0.00545525,-0.03175077,-0.03166346,0.05640289,-0.02280216,0.131767,0.02649472,0.00421088,-0.01392661,0.10353214,0.02036513,-0.04554952,-0.09699222,-0.00349173,-0.00609741,-0.06037384,0.09815203,-0.04826734,-0.02190113,-0.04584531,-0.00064408,0.0061781,-0.08642125,0.02281029,0.01096329,0.02676377,0.01784155,-0.03510921,0.02571901,0.03389746,0.01422308,0.04742481,0.03687842,0.05267499,-0.04681447,-0.06466441,0.01624757,-0.00688329,0.04154816,0.00171174,-0.07120401,0.05685147,-0.01077094,0.05476035,0.0393156,-0.01228584,-0.04259997,0.05224395,0.01562189,-0.00208319,0.07218877,-0.01676825,0.0508277,-0.01373717,0.04294762,0.08499452,-0.02512253,0.02722427,-0.02081826,0.00826807,-0.02422505,0.01791913,0.07525549,0.05994148,0.04732067,0.07219169,0.02066118,0.02913032,-0.00156418,-0.00010563,0.03006205,-0.01798448,0.0512077,0.0000417,0.03727482,-0.25117272,-0.01594527,-0.04648614,0.06506626,-0.00926583,-0.02017592,0.05140407,0.00071237,0.04976807,-0.0396429,0.0561315,0.0270595,0.05781009,-0.12299486,-0.00711822,-0.01870253,0.02467838,0.02283105,0.07277077,-0.00313889,0.07986546,0.02635951,0.23907132,-0.02091139,-0.05675445,0.01459763,-0.00238955,-0.02331709,0.0434485,0.06114141,-0.0147096,-0.00691341,0.07058054,0.04214766,-0.01282862,0.04280506,-0.06533574,0.0453028,-0.00801181,0.03133373,-0.04942591,-0.01437687,-0.05439471,0.02127202,0.08390329,0.04943479,-0.02442049,-0.07836535,0.02119901,0.06898818,-0.04083031,-0.07346108,-0.06478795,-0.01378676,-0.00998609,0.02428761,-0.01191842,0.00570089,0.03219558,0.01204584,0.0641428,-0.01467844,0.07950577,0.01023147,0.0153306],"last_embed":{"hash":"1xx1zfn","tokens":337}}},"text":null,"length":0,"last_read":{"hash":"1xx1zfn","at":1753423665891},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2a.- The Data Files#{1}","lines":[84,89],"size":1107,"outlinks":[{"title":"Lottery software book #2: Create files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.074876,-0.03611323,-0.01581571,-0.07343507,-0.02606215,0.06820064,-0.0425576,-0.04559136,0.01732713,0.01535741,0.02400673,0.00559745,0.00362747,0.01450116,-0.05894801,-0.02588316,0.01345144,-0.00437306,-0.09017833,-0.00342222,0.02259114,-0.04073604,-0.03934708,-0.06449413,0.05722516,0.03194801,-0.0327325,-0.04097705,-0.06331666,-0.25811872,0.02417869,0.02131618,0.00199136,-0.01069012,-0.07874796,-0.05659354,-0.04374702,0.06933984,-0.02687837,0.02516368,0.03711629,-0.00787072,-0.02068882,-0.02175003,0.01508079,-0.07662285,-0.03729761,-0.03300946,0.10553934,0.01088847,-0.04665154,-0.01449584,0.02023854,0.03566494,0.08596586,0.00413213,0.07737514,0.07910579,0.04244158,0.03242122,0.07311573,0.05482856,-0.15934427,0.10517012,-0.00359907,-0.00425208,-0.05621037,-0.05449646,-0.00165839,0.01290548,-0.02348057,0.02671688,-0.05060946,0.06724762,0.05721389,-0.06728606,-0.05158757,-0.03066414,-0.03501893,0.01781921,-0.0895187,-0.01597692,-0.01321077,0.01990504,-0.00110808,0.04156531,0.02044169,0.05494454,0.03491473,-0.03096814,0.07089625,-0.00696624,0.04200102,0.07309711,-0.0150791,0.01878126,0.03726532,-0.00819741,0.00849644,0.10423999,0.00937951,-0.01290712,-0.02114944,0.0436401,0.03771849,-0.02317289,-0.03392681,0.00504182,-0.03931212,0.05433673,0.00884981,0.04476495,0.0444209,-0.00488196,-0.01434025,0.02509938,-0.02833861,0.02220185,0.00358635,0.05099932,-0.0417354,0.02246532,-0.07582355,-0.01382574,0.00566264,0.00791352,0.05439031,0.0503063,0.04660562,0.0137435,0.05712155,0.03829617,-0.184936,-0.0553796,-0.04418281,0.04390578,0.00328353,0.02490198,0.01703249,-0.04994535,-0.02360125,-0.04375895,0.02167075,-0.05960803,-0.00411587,0.10826604,-0.02823105,0.03246859,0.03610563,-0.02810657,-0.00243201,0.00901717,-0.06039763,-0.01987974,0.02555013,0.00661026,0.08192731,0.04217831,-0.03131052,-0.00269446,-0.04662048,-0.05496046,-0.00729141,0.07993135,-0.0314527,-0.08989511,-0.0592239,0.01520311,-0.02185367,-0.08332894,-0.00406391,-0.01811497,-0.04138742,-0.00179389,0.09170756,-0.02017881,-0.05123173,-0.09301529,0.02777091,-0.00310782,0.00870598,-0.02777471,-0.0379336,0.00052579,-0.01216211,-0.10751282,0.02476396,-0.04509771,0.05294411,0.07912316,0.00830271,0.02797198,0.0026525,0.025126,-0.05034336,-0.00627032,-0.03464399,-0.02665297,0.02332338,-0.06212848,0.05508697,0.03169581,0.01265132,-0.0367762,-0.04981488,0.01733253,0.00089627,-0.07505666,0.13104872,0.00902565,-0.06027182,-0.03644668,0.06195463,0.02919334,-0.0451184,0.04182638,0.02861386,-0.02032106,0.00385942,0.03694136,0.04380072,0.00137084,-0.02096901,-0.21274582,-0.03180882,-0.00974476,-0.02098081,0.02820609,-0.01768395,0.03083148,0.0011392,0.02929765,0.06434755,0.12599783,-0.04856092,-0.01959671,0.02506083,-0.01687459,-0.03101592,-0.04749574,-0.05561519,-0.02633765,0.02805222,-0.01698095,0.01234483,-0.04855665,-0.06444506,0.06649076,-0.00509371,0.12258202,0.00067016,0.01347017,-0.01940262,0.09645475,0.0259912,-0.04758075,-0.05908748,0.00417524,0.01373061,-0.05762663,0.05731384,-0.08972336,0.02182052,-0.03499381,-0.02341432,0.03230477,-0.09060583,-0.01648883,0.0039074,0.00128314,-0.00306677,-0.00793013,0.04066851,0.03457566,0.00532663,0.04839514,0.00994873,0.03729184,-0.03486216,-0.00348023,0.03113186,0.01740007,0.03691012,-0.01301167,-0.04959751,0.03812378,0.00992959,0.0364405,0.02600001,0.01429689,-0.02395601,0.07498273,-0.00748498,-0.02214183,0.02947473,-0.02816713,0.02623821,-0.00349328,0.0901951,0.05311689,0.0110783,0.03752691,-0.01638011,0.00502137,-0.02720921,0.01845032,0.05021039,0.0309114,0.04924546,0.06172285,0.05318324,-0.00580707,-0.01666725,-0.01579838,0.02043352,-0.03001707,0.03443919,0.03915494,0.02201086,-0.23523983,0.01400491,-0.02656785,0.04361592,-0.03349138,0.00106491,0.03864128,0.02377413,0.04437924,-0.01561945,0.02346269,0.05658377,0.03691651,-0.10829154,-0.0115668,-0.01928613,-0.01798443,-0.00663555,0.07954241,-0.030778,0.083947,0.02993738,0.23966695,-0.00369954,-0.02458148,-0.01395084,0.02870202,-0.00062744,0.03730403,0.02132667,-0.02876102,0.00496638,0.08004551,0.02625415,-0.0221262,0.00601649,-0.04262553,0.0390426,-0.02786339,0.03750421,-0.07568255,0.01126286,-0.08195237,-0.01427168,0.09073769,0.03380266,-0.04230773,-0.03674316,0.00513608,0.05509165,-0.04394658,-0.07751022,-0.04594265,-0.04045449,-0.01099235,0.01384248,0.01289612,0.01257858,0.02282061,0.05410571,0.04345217,-0.01207562,0.10056008,-0.01469506,0.0104019],"last_embed":{"hash":"1pgghrx","tokens":396}}},"text":null,"length":0,"last_read":{"hash":"1pgghrx","at":1753423665994},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files","lines":[90,101],"size":1445,"outlinks":[{"title":"Lottery software book #3: edit, maintain files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0750965,-0.03600444,-0.01651043,-0.07414279,-0.02587746,0.06731915,-0.04244926,-0.04602498,0.01767561,0.01509258,0.02340339,0.00543607,0.00644718,0.01242605,-0.0578757,-0.02341667,0.01281857,-0.00686456,-0.08960984,-0.00332085,0.01842033,-0.04031687,-0.03978983,-0.06178974,0.05494497,0.03551378,-0.03621563,-0.0391117,-0.06478417,-0.25809786,0.02957708,0.02207516,0.00178486,-0.00974757,-0.07961889,-0.0573238,-0.04310232,0.07199905,-0.02513782,0.02518793,0.03496923,-0.01039528,-0.0213949,-0.02334514,0.01635613,-0.07752165,-0.037631,-0.03106052,0.10557143,0.00893213,-0.04443043,-0.01249626,0.02402203,0.03701231,0.08702883,0.00389518,0.0748998,0.08048984,0.04358277,0.03675221,0.07574191,0.05567669,-0.15911134,0.10695921,-0.00472449,-0.00234423,-0.06136801,-0.05602224,-0.00115074,0.0130937,-0.02265722,0.02695582,-0.04964448,0.06568778,0.05682685,-0.06869137,-0.05111402,-0.02863369,-0.03355834,0.01883553,-0.09075319,-0.01813328,-0.01263914,0.02019659,-0.00019638,0.0408468,0.02085974,0.05362106,0.03422254,-0.02668878,0.07268021,-0.00883638,0.04307219,0.07242611,-0.0148814,0.02039097,0.03390661,-0.00729516,0.01016795,0.10465232,0.0127169,-0.01352599,-0.02230159,0.04499413,0.03574071,-0.0257143,-0.03481941,0.00285871,-0.04094205,0.05378845,0.00831118,0.04512473,0.04631007,-0.00256632,-0.01251733,0.02620387,-0.03049325,0.02395323,0.00312806,0.05054585,-0.04080534,0.02229173,-0.07446028,-0.01405255,0.00700481,0.00927988,0.05666161,0.0503636,0.04671313,0.01353235,0.05526632,0.04172799,-0.1855012,-0.05505914,-0.04372936,0.042851,0.00307212,0.02708921,0.01753483,-0.05030509,-0.02043922,-0.04338181,0.02296375,-0.05964274,-0.00451006,0.10922839,-0.02883031,0.03432621,0.0352236,-0.0299054,-0.00365651,0.0114719,-0.06169182,-0.02023982,0.03030674,0.00561497,0.08214191,0.04030747,-0.03095415,-0.00272224,-0.04821156,-0.0517066,-0.00734917,0.07724413,-0.03262435,-0.08861862,-0.06021799,0.01367358,-0.01975336,-0.0825254,-0.00483586,-0.01533213,-0.04322192,-0.00257968,0.09004334,-0.01966999,-0.05160347,-0.09510018,0.03095944,-0.00317364,0.0108457,-0.02699088,-0.03898433,0.00030223,-0.01405073,-0.10719303,0.02367341,-0.04683749,0.04871364,0.078264,0.00699322,0.03144297,0.00426295,0.02508548,-0.05007375,-0.00994535,-0.03562107,-0.02447968,0.02339269,-0.06420183,0.05797575,0.03160093,0.01266027,-0.03824046,-0.04930756,0.01745563,0.00054282,-0.07372031,0.12922248,0.00655019,-0.05942833,-0.0385738,0.05909846,0.02864919,-0.04344748,0.03895339,0.02771874,-0.01744097,0.00225423,0.03779068,0.04302882,0.00359164,-0.01988582,-0.21030669,-0.03200274,-0.00730224,-0.02047312,0.02804032,-0.015142,0.0293288,-0.00081778,0.02658722,0.06799211,0.12550257,-0.04753327,-0.02023949,0.02530369,-0.01943741,-0.02926298,-0.04298797,-0.0554609,-0.02691968,0.02250904,-0.01655958,0.01222088,-0.05076854,-0.06380336,0.06837617,-0.00248341,0.12079953,-0.0005465,0.01161226,-0.01953869,0.09705368,0.02730569,-0.0467624,-0.05560733,0.00466097,0.01148945,-0.05846943,0.05778009,-0.0901739,0.02350838,-0.0364074,-0.02267371,0.0307996,-0.09446526,-0.01774164,0.00295448,0.00258681,-0.00228864,-0.00690994,0.03878229,0.0356609,0.00830393,0.05036753,0.00849719,0.03597119,-0.03281372,-0.00172582,0.0331962,0.01931743,0.03698844,-0.01361417,-0.05151179,0.03571191,0.01207092,0.03711493,0.02498989,0.01620869,-0.02365129,0.07562246,-0.00542171,-0.02035131,0.02649138,-0.03067395,0.02515233,-0.0028989,0.09194113,0.05370927,0.01180425,0.03791976,-0.01920041,0.00696086,-0.02519513,0.01917019,0.05235143,0.02666101,0.0478962,0.05949667,0.05202688,-0.00705235,-0.01902231,-0.01988089,0.01955454,-0.02925352,0.03358591,0.03644736,0.02287474,-0.23542213,0.01212669,-0.02921293,0.04224029,-0.03914744,0.00260023,0.03920548,0.02706251,0.04243258,-0.0168967,0.02320705,0.05475437,0.03564826,-0.10877404,-0.0144544,-0.01858828,-0.01471597,-0.00770116,0.07979351,-0.03416831,0.08012303,0.02834632,0.23937482,-0.00353495,-0.02538976,-0.01755409,0.02429488,0.00248804,0.03576104,0.02081436,-0.0305295,0.00580563,0.08113847,0.02330866,-0.02387118,0.00407,-0.04436031,0.03686969,-0.0274082,0.04000558,-0.0742171,0.01271044,-0.08029456,-0.01275703,0.08933582,0.03439046,-0.04196678,-0.03627456,0.00524863,0.05912609,-0.04566484,-0.07530827,-0.04507372,-0.0397373,-0.01256768,0.0150246,0.01274296,0.01464143,0.02439138,0.05550023,0.04379411,-0.00980828,0.10044641,-0.01556182,0.0090629],"last_embed":{"hash":"1140nm7","tokens":394}}},"text":null,"length":0,"last_read":{"hash":"1140nm7","at":1753423666104},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##2b.- Update Data Files#{1}","lines":[92,101],"size":1417,"outlinks":[{"title":"Lottery software book #3: edit, maintain files of past results, drawings, winning number.","target":"https://saliu.com/ScreenImgs/lotto2.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04982269,-0.01112718,-0.02074675,-0.03914344,-0.00874477,0.06011216,0.00835327,0.01648812,0.01407813,0.0129537,0.0029048,0.00863426,0.04654199,0.01861544,-0.00824678,-0.01266444,0.01096097,-0.01986986,-0.06972605,-0.02155412,0.08300578,-0.06281885,-0.05673979,-0.08886172,0.06685302,0.02477318,-0.01862253,-0.04644834,-0.03185704,-0.23996551,-0.02376453,0.00102263,0.02680791,-0.08945517,-0.03754822,-0.03243538,-0.02734142,0.02976035,-0.05207067,0.01782188,0.02416411,0.00234872,0.04473743,-0.01620089,-0.04267229,-0.05336705,0.01527455,-0.04157601,0.0218233,-0.00107628,-0.07729297,-0.02553654,0.03803971,0.05391001,0.03603533,0.02685214,0.04484934,0.09481248,0.00878675,0.01714731,0.03465989,0.03961173,-0.19203548,0.08312164,-0.04610241,0.01184577,-0.02939626,-0.05579374,-0.01391903,0.03548492,0.00748002,0.0262648,-0.03961089,0.05310344,0.05464106,-0.06358786,-0.06083051,-0.0456497,-0.01086499,0.00251596,-0.05141943,0.00500437,0.00211599,-0.02399683,-0.00954384,0.05248621,0.0270674,0.0309956,0.05638569,-0.06936634,0.00545471,0.00644972,0.02745707,0.10050483,-0.00090214,0.03076119,0.02673133,0.00222545,-0.02254549,0.11795045,0.03096041,-0.00039768,0.03608583,-0.04941791,0.04684944,-0.03605391,-0.00311064,-0.01029588,-0.01779183,0.02110226,0.06217235,0.0024529,0.0567778,-0.07745315,-0.04575777,0.01873209,0.00676622,-0.02826052,0.01506058,0.02483585,-0.0518148,-0.02634235,0.00969872,-0.00199692,0.02093971,0.03824987,0.02926363,0.06834842,0.00553126,0.03393705,0.03753923,0.01410451,-0.14318207,-0.0184577,-0.02047377,-0.0176986,0.02245142,-0.04959721,-0.00124397,0.02734183,-0.05605836,-0.00331989,-0.01450423,-0.09196456,-0.05454125,0.07769765,0.00906411,0.00995197,0.01565536,-0.01117905,-0.01565162,0.01365303,-0.01435552,-0.04582206,0.01238528,0.01309096,0.06251794,0.09767631,-0.0712002,-0.01805156,0.02360789,-0.04361096,-0.04806824,0.15578273,-0.01462268,-0.06756359,-0.04689954,0.02482368,-0.04391137,-0.04702285,-0.0287214,0.03439834,-0.01191764,-0.00590923,0.06825774,-0.03108399,-0.08767343,-0.02791409,-0.05559758,-0.03693743,-0.03413655,-0.03996373,-0.04335563,0.02272521,-0.01973554,-0.10514361,0.04016902,-0.03081476,0.06105583,0.04676691,-0.00899491,0.00667562,-0.03177478,0.03475978,0.02784614,-0.01280801,0.00540812,-0.02669458,0.03972598,-0.04978194,-0.03424368,0.02092793,0.0209118,0.02150812,-0.02660665,0.03574187,-0.06684821,-0.01169795,0.12145832,0.03956589,-0.05675991,0.02828114,0.03353653,0.04032345,-0.06402801,-0.00093109,0.03788392,0.0100895,-0.01318877,-0.01092196,0.02110038,0.04695382,-0.05012611,-0.2403537,-0.02794748,-0.0530782,0.02242906,0.02382065,-0.07447384,0.00525998,-0.02833045,0.03353999,0.1020624,0.12473577,-0.02229471,-0.01609224,0.03776309,0.0141668,-0.02054066,-0.07297639,-0.02265873,-0.0772403,0.0613195,-0.01543183,0.0332546,-0.00511878,-0.0498843,-0.008691,-0.01000987,0.13721761,0.03623296,0.04365643,0.00907253,0.08942663,0.02960464,-0.0160071,-0.04029618,-0.0157149,0.07451782,-0.07014613,0.01932003,-0.05250122,-0.01617486,-0.04641719,0.02470973,0.01087381,-0.09413155,-0.02761116,0.0273134,-0.00248546,0.04103756,-0.03450539,0.04888504,0.02420365,-0.00942091,0.02524805,0.02166255,0.08697397,-0.0205173,-0.07746321,-0.02592435,0.01284104,0.00803078,-0.03825595,-0.01047918,0.07917526,-0.02809487,0.04330644,0.0273693,-0.00892995,-0.03810852,0.03988635,-0.0381051,-0.02241988,0.10661945,-0.0381596,0.0352115,-0.03336035,0.0318263,0.054114,-0.03994167,-0.00506181,-0.0330767,0.00350776,-0.05001424,0.02231306,0.05529628,0.07571463,0.07589833,0.05627813,0.02358856,0.02701841,0.00642937,0.00054138,0.0164932,-0.04719504,0.00552048,0.03226164,0.03308865,-0.23082481,-0.00128844,0.00698402,0.07047354,-0.00783988,-0.02421857,0.04457282,-0.03335879,0.03734699,-0.03387474,0.04730718,0.04300375,0.04057252,-0.08104511,0.03707098,-0.01691389,-0.00578143,-0.02644462,0.05300386,0.01863251,0.06957405,0.03768117,0.2413194,-0.00111934,-0.02806988,0.00367564,0.05314687,-0.02848227,-0.03219573,0.03280453,-0.00744542,-0.00782977,0.09220625,0.01561605,-0.0230148,0.02575581,-0.03719307,0.03175993,0.0087171,0.00057895,-0.03744217,0.02690771,-0.01690182,-0.00581396,0.11861389,0.06152119,-0.03324595,-0.05542991,0.0469093,0.07726979,-0.04961854,-0.07153884,-0.03958051,-0.03419969,-0.02670921,0.03308342,0.02099472,-0.02198817,0.00704302,-0.00659199,0.03476344,0.01034653,0.05525903,-0.00310034,0.01634493],"last_embed":{"hash":"bqcioj","tokens":345}}},"text":null,"length":0,"last_read":{"hash":"bqcioj","at":1753423666231},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6","lines":[102,111],"size":1343,"outlinks":[{"title":"There is no better lottery manual than this page.","target":"https://saliu.com/ScreenImgs/lotto4.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04344608,-0.00510542,-0.02055885,-0.03587172,-0.00702531,0.05935219,0.00821421,0.01750955,0.01396672,0.01598422,0.00416706,0.01192004,0.04927013,0.01681091,-0.00625552,-0.00942213,0.00797661,-0.02048465,-0.06895588,-0.02074153,0.08007716,-0.0590458,-0.0559181,-0.08568719,0.06636488,0.02634347,-0.01588392,-0.04710487,-0.034733,-0.24150413,-0.02669484,-0.00278876,0.02656781,-0.08941437,-0.03434086,-0.03376288,-0.02960532,0.03272122,-0.04878876,0.01381867,0.02476786,0.00227041,0.0486954,-0.01752634,-0.04731528,-0.05193655,0.01705086,-0.03835909,0.02131913,-0.0035804,-0.0760212,-0.02662325,0.03990964,0.05944982,0.03314461,0.02429108,0.04163339,0.09430525,0.00771258,0.01928743,0.03757017,0.03942592,-0.19368292,0.08096066,-0.05256252,0.01245046,-0.03145336,-0.05459605,-0.01896543,0.03813112,0.00448879,0.02366322,-0.04075479,0.05051423,0.05269028,-0.06469183,-0.06328991,-0.04470176,-0.01008023,0.00433236,-0.05169367,0.00419004,0.00072769,-0.02424093,-0.0116419,0.04934458,0.03191699,0.02788001,0.05862812,-0.07122678,0.00541122,0.00541466,0.02129504,0.10089516,-0.00099952,0.02848752,0.02602375,0.00422179,-0.02028238,0.12015691,0.02898598,0.00137341,0.03511944,-0.05414492,0.04469424,-0.0367425,0.00041276,-0.00837029,-0.01717308,0.02330836,0.06248138,-0.00009538,0.05740317,-0.07908551,-0.04576329,0.02135848,0.00625205,-0.02821378,0.01263372,0.02500942,-0.04937541,-0.02814771,0.01057072,-0.00591628,0.02193744,0.04144649,0.03182685,0.06868059,0.00326371,0.03332869,0.03533334,0.01333965,-0.14803645,-0.01582159,-0.02109508,-0.02025158,0.02429704,-0.0514617,0.00005471,0.02775113,-0.05441085,0.0001977,-0.01515894,-0.09398894,-0.0546097,0.08124908,0.00588736,0.00754464,0.01351547,-0.01659251,-0.0165275,0.01994913,-0.01335055,-0.0473025,0.0130324,0.01437895,0.06128646,0.09611797,-0.07078385,-0.0187573,0.02095508,-0.0420205,-0.04762285,0.15653564,-0.01394122,-0.0655733,-0.04979068,0.02506512,-0.04067389,-0.04631236,-0.02678014,0.03654115,-0.01129192,-0.00901289,0.0660006,-0.03113772,-0.09179454,-0.02987822,-0.05364852,-0.03564327,-0.03181402,-0.03973842,-0.04456898,0.0225064,-0.01817607,-0.10287271,0.03551528,-0.02827715,0.06263671,0.04651846,-0.00926973,0.00517826,-0.03007394,0.03483585,0.02697373,-0.01366512,0.00680943,-0.0238678,0.03855323,-0.05069871,-0.0329719,0.01980351,0.0215166,0.02336236,-0.02548905,0.0318058,-0.06910534,-0.00846487,0.12272353,0.03736701,-0.05760629,0.02469128,0.03344223,0.03696826,-0.06553701,-0.00040534,0.03571638,0.01388079,-0.01731359,-0.01402336,0.02119262,0.05096315,-0.04893934,-0.24273375,-0.02826195,-0.05091494,0.0253201,0.02474199,-0.07175963,0.00413726,-0.02691086,0.03120307,0.10436295,0.12501039,-0.01753484,-0.01856545,0.03904601,0.01687926,-0.02004334,-0.07116466,-0.02304415,-0.07625113,0.06136765,-0.01671814,0.03090213,-0.0083203,-0.04645962,-0.01178131,-0.00671055,0.13419995,0.03356842,0.04388555,0.0091577,0.08735289,0.03078454,-0.0166751,-0.03601527,-0.01082009,0.07708541,-0.0717823,0.01688218,-0.05284999,-0.01579252,-0.04540886,0.02689199,0.00801795,-0.09528378,-0.02894734,0.02565337,-0.00021461,0.03804638,-0.03316698,0.04975308,0.02637176,-0.00812885,0.02861864,0.01996928,0.08881597,-0.0228873,-0.0761377,-0.02420076,0.01450661,0.00182422,-0.03568408,-0.01028562,0.07850388,-0.02969496,0.04358129,0.02570347,-0.00936261,-0.03485448,0.03951502,-0.03794961,-0.02198652,0.10903179,-0.04452974,0.03547108,-0.03011781,0.03257976,0.05306787,-0.04439592,-0.00590033,-0.0336642,0.00650616,-0.05053357,0.02306656,0.05634045,0.07400416,0.07885317,0.05567181,0.02543668,0.03004669,0.00695889,0.00264486,0.01106631,-0.04302208,0.00486289,0.03087129,0.03388743,-0.22918093,-0.00326876,0.00732457,0.06717929,-0.00804008,-0.02561028,0.04442022,-0.03008886,0.03738139,-0.02572888,0.04442787,0.04156975,0.03877334,-0.08192061,0.03444273,-0.01621897,-0.00680892,-0.02358416,0.05297717,0.01995472,0.07079618,0.03495938,0.24107219,-0.0008407,-0.02521201,0.00381025,0.05003243,-0.02934288,-0.03491968,0.03123445,-0.00262682,-0.00950017,0.09431697,0.01516913,-0.02135295,0.02998147,-0.03357909,0.03171543,0.00558108,0.0007485,-0.03717417,0.02879081,-0.01648523,-0.00941281,0.11827134,0.06148054,-0.03104448,-0.05753153,0.04377501,0.07839885,-0.05178861,-0.06956832,-0.0393336,-0.03487404,-0.02793077,0.03692233,0.02213265,-0.02250273,0.00963001,-0.00828546,0.03229478,0.01029206,0.05241302,-0.00443047,0.01705714],"last_embed":{"hash":"1vnagb3","tokens":343}}},"text":null,"length":0,"last_read":{"hash":"1vnagb3","at":1753423666343},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3a.- The Winning Reports: W6 and MD6#{1}","lines":[104,111],"size":1301,"outlinks":[{"title":"There is no better lottery manual than this page.","target":"https://saliu.com/ScreenImgs/lotto4.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03383986,-0.01076685,-0.01682094,-0.04467698,-0.01607911,0.05123076,-0.03973207,-0.01575005,0.02875021,-0.00020877,0.00546512,-0.01504227,0.04047797,0.00749512,-0.01104891,-0.02342314,0.02816653,-0.04448711,-0.08084909,0.01690991,0.10191701,-0.0557808,-0.04181859,-0.1039796,0.06317282,0.01156508,-0.03761376,-0.04252351,-0.05408528,-0.25510412,0.0042318,0.03627818,0.02271118,-0.03748555,-0.07588874,-0.05110088,-0.03893869,0.05308565,-0.04611326,-0.00291314,0.03224823,0.02096479,0.01006711,-0.01029168,-0.01058627,-0.07328407,-0.04033467,-0.02228848,0.07443366,0.01288903,-0.05168026,-0.04836829,0.01833764,0.02946891,0.04497334,0.01270594,0.07796852,0.09279987,0.04422513,0.01976209,0.05028882,0.07743375,-0.15360589,0.04275939,-0.02281026,0.01081637,-0.04308164,-0.02906005,0.03147532,0.01526369,-0.02037767,0.03758314,-0.03100083,0.07348736,0.03025479,-0.06046779,-0.03669044,-0.0454625,-0.01795278,-0.02417296,-0.09015223,0.01549995,-0.00223532,0.02733661,0.01466592,0.04871451,0.04783704,0.04540241,0.04245099,-0.07638396,0.04629441,0.02228619,0.02363699,0.02589628,-0.02582435,-0.02436665,0.06586266,-0.03020042,0.0103277,0.11417947,0.01920966,-0.01119362,0.02292068,0.01707527,0.04471092,-0.04001634,-0.03208538,-0.01952572,-0.03716387,0.04687258,0.05764544,0.01279578,0.05846488,-0.08040801,-0.02911197,-0.00918412,-0.02709963,-0.04766083,-0.00494019,0.036216,-0.03526933,0.01882545,-0.01564154,0.01010145,0.01920736,-0.01009647,0.03592884,0.0458548,0.05389867,0.03299213,0.04129584,0.06365331,-0.14270695,-0.04342254,-0.0102344,-0.01262047,0.0158211,-0.02403937,-0.0313059,-0.05030229,-0.01681639,-0.0017723,0.08217962,-0.07019794,0.00188318,0.07768542,-0.01048216,0.01474762,0.00448383,-0.04020483,0.0053257,0.03277597,-0.04284061,-0.03424499,0.01654684,0.00088754,0.08466973,0.10924002,-0.05583664,-0.00819671,0.01631062,-0.02704992,0.01583645,0.07363199,-0.04448455,-0.08566708,-0.02002067,0.06919905,-0.05082913,-0.09235078,-0.0321308,0.02728857,-0.04353332,-0.00679765,0.07990567,-0.03521823,-0.05173155,-0.05665136,-0.01658124,0.00132599,-0.0130878,-0.0269127,-0.03894383,0.00717984,-0.00492718,-0.06802331,0.02386773,-0.0076752,0.03734883,0.05238859,-0.01835288,0.04043967,-0.03357346,0.02817297,-0.03064753,0.00125235,-0.03266193,-0.04262232,0.02619574,-0.03132306,0.03095322,0.00404931,0.01002194,0.0001985,0.01068738,0.01152005,-0.02892431,-0.04198472,0.11919845,0.00713259,-0.07346856,-0.01895899,0.05601062,0.04731212,-0.10435018,0.04530267,0.00616861,0.03953908,-0.01913369,0.02094633,0.02162284,-0.01222835,-0.04796888,-0.20311449,-0.05163222,-0.01288209,0.00752085,0.01368803,-0.03398199,0.04382068,-0.01116281,0.03588694,0.07206389,0.1340632,-0.0720695,-0.02754154,0.02247807,-0.00251669,-0.05207461,-0.11516475,-0.03639233,-0.05738201,0.07744173,0.000228,0.01148313,-0.01064874,-0.01040925,0.02334344,-0.02539249,0.12923111,0.01783586,-0.01009987,-0.0312209,0.06488301,0.03850177,-0.04117309,-0.03644126,0.00197919,0.02827338,-0.06383495,0.06600498,-0.02789767,-0.01436934,-0.04209385,0.01291786,-0.01568091,-0.0921533,-0.00122368,0.0142324,0.00952381,0.01371249,-0.01784179,0.04599638,0.03753141,0.01148246,0.02997413,0.02490347,0.06263576,-0.04629031,-0.08547638,0.00910994,-0.00628539,0.01463086,0.01689494,-0.06311066,0.04654931,-0.0221457,0.07032469,0.03143863,-0.01308324,-0.02752248,0.03477831,-0.01214586,-0.00148255,0.06963387,-0.02020397,0.06827214,-0.00262608,0.04072999,0.06441813,-0.04062914,0.00887888,-0.01654985,-0.0018138,-0.05593736,0.01661432,0.06376001,0.06216136,0.06369043,0.07656363,0.03852702,0.02293026,0.00876306,0.01933929,0.01357002,-0.00471508,0.03460162,-0.00896586,0.02978523,-0.2531023,-0.00330056,-0.0311921,0.07189509,-0.04579291,-0.01120248,0.0686065,-0.00574153,0.05788445,-0.03755129,0.0433636,0.01722473,0.05809889,-0.1181387,0.02596759,-0.02234594,0.00551587,0.00730644,0.04632167,-0.00963936,0.07347727,0.02611078,0.23875326,-0.00829375,-0.04869382,0.04070415,0.00251082,-0.01107108,0.02195362,0.04759755,-0.01782088,-0.0130006,0.09809128,0.02751868,0.00031961,0.05151724,-0.03349694,-0.00372077,0.01267938,0.04596258,-0.04911446,0.00169245,-0.04628555,0.00949546,0.10260882,0.03191134,-0.03161794,-0.07655638,0.01662713,0.05060473,-0.07563177,-0.08497319,-0.04932592,-0.05201174,0.01854115,0.0255101,0.03723006,0.01655747,0.01146053,-0.0154086,0.03067971,-0.0363817,0.07135958,-0.02021089,0.00902978],"last_embed":{"hash":"57ppb6","tokens":480}}},"text":null,"length":0,"last_read":{"hash":"57ppb6","at":1753423666445},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file","lines":[112,131],"size":1482,"outlinks":[{"title":"Bright lotto software require data files of 12 million lotto combinations. For 5-number lotto: a drawings file of at least 4 million lottery combinations.","target":"https://saliu.com/ScreenImgs/lotto5.gif","line":7},{"title":"Lottery software manual: Use all draws in your lotto winning numbers file.","target":"https://saliu.com/ScreenImgs/lotto6.gif","line":9},{"title":"Software book requests to enter the biggest number in lotto game; e.g. 45, 49, 54, 59.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":11},{"title":"Lotto software saves the winning reports or filter files to disk.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":13},{"title":"You too can write a good lotto book and lottery manual.","target":"https://saliu.com/ScreenImgs/lotto9.gif","line":15},{"title":"Lottery winning reports for the special filters named Ion: Lotto sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto10.gif","line":17},{"title":"Lotto software informs on the success of generating the reports for past lottery drawings.","target":"https://saliu.com/ScreenImgs/lotto11.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03319024,-0.01109402,-0.02033453,-0.04138932,-0.01604713,0.05334095,-0.04004221,-0.01561336,0.02881289,0.00379502,0.00717869,-0.01424078,0.0417468,0.00680638,-0.010337,-0.02143755,0.02861477,-0.04550756,-0.08103206,0.01742769,0.09999375,-0.05482916,-0.04187802,-0.10255431,0.06580103,0.01409645,-0.03673407,-0.04459319,-0.05476072,-0.25630769,0.00258084,0.03515841,0.02369001,-0.03911211,-0.07297064,-0.05017832,-0.03738895,0.05320477,-0.0478628,-0.00091155,0.03171122,0.02254809,0.01042343,-0.00961479,-0.01051591,-0.07160102,-0.04063135,-0.01957004,0.07395951,0.01239689,-0.05040366,-0.04694423,0.01884787,0.02990696,0.04473275,0.01298329,0.07441776,0.09274097,0.04307517,0.0192391,0.05094209,0.07772317,-0.15511173,0.04029033,-0.0229527,0.01553982,-0.04435651,-0.02796832,0.02807519,0.01836312,-0.02066907,0.03662651,-0.02928261,0.07269946,0.02778519,-0.06078189,-0.03735301,-0.04306761,-0.01929943,-0.02230539,-0.09255618,0.01460439,-0.00447898,0.02581755,0.01117865,0.04518889,0.0485398,0.04838386,0.04584795,-0.07975376,0.04533736,0.02352276,0.02238823,0.02489751,-0.02570286,-0.02257857,0.06525552,-0.02912113,0.00980925,0.11565985,0.02263713,-0.00788331,0.02096194,0.01709594,0.04416236,-0.03980645,-0.03192561,-0.01858737,-0.03638585,0.04793211,0.05723699,0.00975704,0.05724182,-0.07751203,-0.03040162,-0.01058159,-0.02661492,-0.04966254,-0.00711407,0.03602351,-0.03476979,0.02047089,-0.01355854,0.00784283,0.01735738,-0.00922895,0.03789903,0.04620668,0.05153045,0.03238076,0.04018192,0.06298981,-0.14760622,-0.04328614,-0.01198053,-0.01794586,0.01654515,-0.0201111,-0.0286898,-0.05202411,-0.0161955,-0.00217241,0.08226658,-0.0716802,-0.00033037,0.07683107,-0.01068742,0.01519163,0.00493598,-0.04228872,0.00643998,0.03437168,-0.04537499,-0.03935074,0.01521732,0.00323534,0.08221249,0.10729689,-0.05474445,-0.00788051,0.01660721,-0.02633926,0.01502057,0.07281807,-0.0437354,-0.08353151,-0.02029258,0.06894702,-0.04757644,-0.08963551,-0.03086865,0.02672978,-0.0437554,-0.00876171,0.07623549,-0.03572319,-0.05142873,-0.05851966,-0.01444772,0.00326636,-0.01238027,-0.02439155,-0.03978361,0.00593552,-0.0065307,-0.06651091,0.02303307,-0.00413069,0.0384278,0.05179727,-0.01744365,0.04421113,-0.03156711,0.02924039,-0.02817134,-0.00250608,-0.03139671,-0.04188322,0.02635718,-0.03353779,0.03269102,0.00557076,0.0139087,0.00193015,0.01183022,0.00992332,-0.03062765,-0.04291644,0.12351385,0.00776205,-0.07016733,-0.02146326,0.05522823,0.04542137,-0.1027507,0.04386336,0.00552706,0.04091766,-0.02035193,0.02370388,0.01848888,-0.00920103,-0.04883554,-0.20535985,-0.05238372,-0.01497999,0.00932761,0.01542943,-0.03151003,0.04399082,-0.01045331,0.03224892,0.07263593,0.1325518,-0.07101524,-0.02969903,0.02469829,-0.00386421,-0.05324128,-0.11091213,-0.0338339,-0.05547945,0.07655624,-0.00010619,0.01124251,-0.01143499,-0.00905026,0.02399015,-0.02763176,0.13186406,0.01607534,-0.01241302,-0.03148348,0.06494561,0.03823862,-0.04390716,-0.03568845,0.00198904,0.03161921,-0.06441452,0.06532738,-0.03167607,-0.00959151,-0.04360205,0.01216122,-0.01260146,-0.09284648,-0.00178501,0.01371732,0.01280682,0.01324069,-0.01659432,0.04655634,0.03864025,0.01381677,0.03085767,0.02307659,0.06288487,-0.04631002,-0.08392172,0.00974031,-0.00408419,0.0139574,0.01908131,-0.06380212,0.04432634,-0.02300115,0.07027373,0.03056597,-0.01498648,-0.02707458,0.03380254,-0.01315898,0.00175823,0.07054833,-0.01974702,0.06562088,0.00025115,0.04315958,0.06611194,-0.04442716,0.00774963,-0.01682639,-0.00273049,-0.05690723,0.0194234,0.06205439,0.06117875,0.06537728,0.07687984,0.04033121,0.02243626,0.00837049,0.02083144,0.01235545,-0.00481262,0.0332607,-0.00994602,0.02858777,-0.25411516,-0.00686649,-0.03369696,0.06782739,-0.04404669,-0.00879171,0.06727436,-0.00124941,0.0557042,-0.03395521,0.04656246,0.01609232,0.05614667,-0.11845096,0.02583401,-0.02296638,0.00477886,0.00499723,0.0462054,-0.01060428,0.07277784,0.02406473,0.23961335,-0.00767153,-0.0498134,0.03740828,0.00138455,-0.01095816,0.01766897,0.0439402,-0.01891062,-0.01096104,0.09708022,0.02764282,-0.00203784,0.05623428,-0.03381944,-0.00473841,0.01100184,0.046902,-0.04795771,0.00154627,-0.04569199,0.01050534,0.10159634,0.03488683,-0.0334378,-0.07924496,0.01720899,0.05085886,-0.07761521,-0.08477283,-0.05042676,-0.05514189,0.01974644,0.02780607,0.03782612,0.01799121,0.01226505,-0.01580015,0.0309553,-0.03563097,0.06843919,-0.01927407,0.00903758],"last_embed":{"hash":"yc43gy","tokens":478}}},"text":null,"length":0,"last_read":{"hash":"yc43gy","at":1753423666586},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##3b.- The Winning Reports: The minimum size of the data file#{1}","lines":[114,131],"size":1417,"outlinks":[{"title":"Bright lotto software require data files of 12 million lotto combinations. For 5-number lotto: a drawings file of at least 4 million lottery combinations.","target":"https://saliu.com/ScreenImgs/lotto5.gif","line":5},{"title":"Lottery software manual: Use all draws in your lotto winning numbers file.","target":"https://saliu.com/ScreenImgs/lotto6.gif","line":7},{"title":"Software book requests to enter the biggest number in lotto game; e.g. 45, 49, 54, 59.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":9},{"title":"Lotto software saves the winning reports or filter files to disk.","target":"https://saliu.com/ScreenImgs/lotto7.gif","line":11},{"title":"You too can write a good lotto book and lottery manual.","target":"https://saliu.com/ScreenImgs/lotto9.gif","line":13},{"title":"Lottery winning reports for the special filters named Ion: Lotto sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto10.gif","line":15},{"title":"Lotto software informs on the success of generating the reports for past lottery drawings.","target":"https://saliu.com/ScreenImgs/lotto11.gif","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02947376,-0.01164165,-0.00387288,-0.02069681,-0.03184514,0.06430618,0.02056568,0.01747625,0.01721376,-0.01248838,-0.01621557,0.02189556,0.04418419,0.03837948,-0.02299126,-0.009113,0.00909863,-0.02210793,-0.05153793,-0.00844054,0.11073682,-0.02354603,-0.05413766,-0.0826223,0.05535601,0.02574065,-0.03540168,-0.07321219,-0.03839518,-0.22591661,0.01866779,-0.00283512,-0.00142859,-0.0502409,-0.07308339,-0.02824274,-0.0384425,0.07969817,-0.00562085,0.01620712,0.03561594,-0.00989374,0.01258999,-0.02320041,0.00805533,-0.05377928,0.01484953,-0.01219462,0.03905298,-0.02999268,-0.07527372,-0.03406304,0.01827888,0.03007131,0.03069924,0.01974448,0.06548283,0.06793218,0.03245139,0.01462209,0.03110455,0.05417945,-0.20164895,0.07220975,-0.04733934,0.07211294,-0.04144451,0.00910324,0.01347675,0.07461624,0.00487205,0.01945146,-0.04554262,0.02329041,0.06486837,-0.024024,-0.03841951,-0.05034387,-0.03704595,0.01766838,-0.06635333,-0.02081798,0.01130493,0.01332586,0.01508497,0.04535726,0.05284338,0.03551175,0.08833846,-0.02418418,0.06503621,0.00270355,0.00418291,0.05421786,0.00098359,0.00198655,0.0134498,-0.03036298,0.01538026,0.13240652,0.03312458,0.02448816,-0.00071223,-0.01659038,0.01974958,-0.05982942,-0.00284349,-0.00354945,-0.03259154,0.05519494,0.02501009,0.00928847,0.01870641,-0.0678302,-0.01789316,0.03890354,0.00821346,0.01536833,-0.01170208,0.01665153,-0.048645,-0.02627897,-0.01476273,0.00152749,0.02338442,0.00845802,0.05596952,0.07467616,0.01395241,-0.0033659,0.04500762,0.04949845,-0.19856958,-0.03148203,-0.05138325,-0.00116282,0.04449839,0.00674459,-0.0007349,0.0326926,-0.04267775,0.029282,0.01999439,-0.08995596,-0.04277691,0.13406821,-0.02555194,0.01437728,0.01155086,-0.05869027,0.00148629,0.0025146,-0.05445218,-0.05893603,-0.01323434,0.02671861,0.07785244,0.08491591,-0.05434328,-0.0309031,-0.05377769,-0.01298114,-0.00721059,0.08706547,0.00193306,-0.08490296,-0.04899117,0.02716283,-0.04818938,-0.11116958,-0.01887322,0.00288929,-0.04739166,0.02430538,0.06635696,-0.00750025,-0.07005613,-0.03785014,-0.04468993,-0.01320988,0.01369833,-0.02629092,-0.0708724,0.01693197,-0.02305838,-0.09552091,0.00802744,-0.05049922,0.0305941,0.06857677,-0.05336151,-0.01814714,-0.04252535,0.01654551,-0.01813509,-0.02378245,0.01005525,-0.03099428,0.0176617,-0.01359696,0.01573777,-0.00600453,0.0024936,0.0501637,-0.0474369,0.01921241,-0.05689453,-0.04511291,0.14515004,0.02604079,-0.05464593,0.03050058,0.0432825,0.03173976,-0.05999037,-0.0091646,0.0172941,0.02097436,-0.04348027,-0.00069775,0.02952194,0.01904736,-0.05748497,-0.19490181,-0.06092155,-0.04504854,0.03804788,-0.0300981,-0.02753975,0.00883199,-0.02982586,0.04358306,0.11195371,0.09537549,-0.04319008,-0.01699134,0.05161164,0.00158538,-0.04857168,-0.09339914,-0.02655698,-0.04565916,0.04223102,0.03032771,-0.00347671,0.01678455,-0.04027748,0.00537297,-0.01280039,0.11237372,-0.0024126,0.01993508,-0.01070133,0.05202479,0.0297788,-0.03774136,-0.00895496,-0.01714047,0.05209325,-0.04904137,0.03200655,-0.05664349,0.00367299,-0.01983942,-0.00973238,0.01278048,-0.08467621,-0.03992774,0.03940724,0.0430309,0.03535493,-0.02612853,0.01726626,0.02181832,-0.02316694,0.03742467,0.0387403,0.06249348,-0.02963647,-0.08243542,-0.02242872,-0.00470289,0.0080178,-0.02143313,-0.03111129,0.05692839,-0.03099593,0.09459296,0.00513283,-0.00756803,-0.04020213,0.02280479,-0.03516129,0.00391391,0.05695733,-0.04960751,0.05887493,-0.02117872,0.0575856,0.06799142,-0.05667146,0.01048485,-0.04305783,0.01980347,-0.06587424,0.0217111,0.04210526,0.03529559,0.05430682,0.04734979,0.00329121,0.05327266,0.00604363,-0.03026086,0.05105599,-0.00533431,0.03901568,-0.00527206,0.04688494,-0.23214336,0.00833551,-0.03450069,0.07242983,0.03129865,-0.04492505,0.05575664,-0.01061796,0.05617194,-0.03699658,0.03957158,0.01843483,0.01353715,-0.08420792,-0.01069754,-0.02439021,0.03650907,-0.01762252,0.08411298,0.02324748,0.09096582,0.02215214,0.2488036,0.02457149,-0.02032914,0.00069062,0.02924783,-0.04900139,-0.04796524,0.05224458,0.04758604,-0.02672911,0.11230022,0.01145867,-0.02356895,0.02140836,0.0039838,0.03704495,0.01319025,0.03827961,-0.01446804,0.01865833,-0.03977655,-0.01566336,0.09892386,-0.00330711,-0.05309173,-0.0550405,0.04047231,0.06223292,-0.10328706,-0.06224058,-0.03569272,-0.00575648,-0.00119227,0.03718816,0.02404335,-0.00066953,0.00532712,-0.00052111,0.00615987,-0.02171397,0.06536952,0.00265103,0.00840202],"last_embed":{"hash":"15qnc73","tokens":242}}},"text":null,"length":0,"last_read":{"hash":"15qnc73","at":1753423666739},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis","lines":[132,141],"size":777,"outlinks":[{"title":"The program shuffles the deck of lotto cards and arranges the lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto12.gif","line":7},{"title":"An MD past draw lotto report: Lottery sum-totals, sums, root sums.","target":"https://saliu.com/ScreenImgs/lotto13.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02497976,-0.01218714,-0.00542681,-0.01759546,-0.02955137,0.06291639,0.01908499,0.01673302,0.01800711,-0.01349813,-0.01524613,0.02614614,0.04451804,0.03863931,-0.02507928,-0.00730129,0.01016848,-0.02435749,-0.05213763,-0.00573083,0.11037083,-0.02562249,-0.05213681,-0.08127014,0.05722122,0.02655843,-0.03277043,-0.0749837,-0.03981791,-0.22429442,0.0142696,-0.00624551,0.00070786,-0.04867005,-0.07303531,-0.02758968,-0.03711032,0.08109066,-0.00290927,0.01604332,0.0365153,-0.00866365,0.01478688,-0.02329006,0.00794396,-0.05283453,0.01148735,-0.00930458,0.04022442,-0.03093651,-0.0744929,-0.0323197,0.01702269,0.03297596,0.02925751,0.01636039,0.06315344,0.06953481,0.03126348,0.01327455,0.03505784,0.05790718,-0.20263045,0.0710602,-0.05021467,0.07496508,-0.04309082,0.01104662,0.01107071,0.0757743,0.00317099,0.01678602,-0.04615592,0.02319737,0.0629364,-0.02245405,-0.03884013,-0.0476194,-0.03778886,0.0191841,-0.06460714,-0.02292444,0.0122066,0.01222222,0.01163257,0.04288537,0.05371638,0.03509028,0.08911493,-0.0220814,0.06437673,0.00071282,0.00295583,0.05236724,0.0009089,0.00121915,0.01254666,-0.02732646,0.01482634,0.13224307,0.03021209,0.02795068,-0.00301385,-0.0156786,0.01718595,-0.05839197,-0.00338258,-0.00016406,-0.03185201,0.0556622,0.02348781,0.00647349,0.01852311,-0.06593511,-0.0184348,0.0403631,0.00976985,0.01477827,-0.01648588,0.01693752,-0.0488023,-0.02719582,-0.01472256,0.00275867,0.02419228,0.01007898,0.05606513,0.07676049,0.01674572,-0.00319834,0.04433896,0.05159472,-0.20398287,-0.02996196,-0.05236305,-0.00402649,0.04470599,0.0069586,0.00075842,0.03081432,-0.0422896,0.02691103,0.02022805,-0.08888268,-0.04275374,0.13177443,-0.0286813,0.01338794,0.01172439,-0.06001405,-0.00218994,0.00761278,-0.05539285,-0.06074569,-0.01423831,0.02273775,0.07711982,0.08356039,-0.05581617,-0.03211872,-0.05417168,-0.01088174,-0.00449878,0.08403382,0.00349971,-0.07940149,-0.04846248,0.02721033,-0.04839805,-0.10980795,-0.02087975,0.00096437,-0.04885776,0.02169666,0.06353902,-0.00781595,-0.07131635,-0.0361053,-0.04629812,-0.0118966,0.01329709,-0.0245679,-0.071994,0.01639649,-0.02131318,-0.09490767,0.00673676,-0.04911254,0.03332531,0.06989653,-0.05115907,-0.01522665,-0.04314815,0.0184514,-0.0177963,-0.02467842,0.01018097,-0.03233175,0.01829527,-0.01414414,0.01605355,-0.00472523,0.00095189,0.0479637,-0.04502495,0.01790856,-0.05892783,-0.04369412,0.14649615,0.02498083,-0.05294004,0.03000025,0.04313721,0.02807718,-0.06125411,-0.00810313,0.018624,0.0229885,-0.04558036,-0.00314827,0.02810871,0.01860335,-0.05670213,-0.19544707,-0.06177051,-0.04301678,0.03743991,-0.02641109,-0.027768,0.00882311,-0.02846372,0.04371594,0.11533392,0.09536949,-0.04206753,-0.01730645,0.05174994,0.0039078,-0.04361933,-0.09229501,-0.02488643,-0.04286426,0.04268826,0.02809003,-0.00597321,0.0163895,-0.0378065,0.00216626,-0.01305118,0.11398311,-0.00264851,0.01618015,-0.01144821,0.05220596,0.02950241,-0.03898444,-0.00994659,-0.01433704,0.05364237,-0.04905014,0.032986,-0.05623718,0.00228401,-0.02039438,-0.01126649,0.01319741,-0.08464959,-0.03839401,0.04118407,0.04509479,0.03184316,-0.02630612,0.0188002,0.02315696,-0.021721,0.0390976,0.03840952,0.06034712,-0.03080207,-0.08158216,-0.02291986,-0.00398466,0.00728361,-0.02007901,-0.03058596,0.05607932,-0.03044267,0.09556334,0.00585319,-0.01136208,-0.03962271,0.02014806,-0.03722945,0.00249634,0.05657875,-0.05192704,0.05814086,-0.02184412,0.06101237,0.06727322,-0.05804254,0.0120391,-0.04211381,0.02237155,-0.06901949,0.02240091,0.04173126,0.03399302,0.05584242,0.04650288,0.00377689,0.05252994,0.00554045,-0.02454194,0.05284292,-0.00607374,0.0387444,-0.00473659,0.04733522,-0.23309438,0.00704849,-0.03500104,0.07006682,0.03087292,-0.04715494,0.05478645,-0.0080531,0.05602316,-0.03491976,0.04006615,0.01869987,0.01079246,-0.08693674,-0.0107697,-0.02125341,0.03300516,-0.01625596,0.08504746,0.02347189,0.09117434,0.01937393,0.25016075,0.02553756,-0.0190734,0.00301544,0.02828298,-0.05223405,-0.05130952,0.05129568,0.04638887,-0.02787565,0.11202971,0.01318255,-0.02264909,0.02623462,0.0061334,0.03455028,0.01160467,0.03825513,-0.01161092,0.01849852,-0.03938681,-0.01835958,0.09799565,-0.00267423,-0.05315419,-0.05637375,0.03892931,0.06239913,-0.10215097,-0.06322634,-0.03648513,-0.00659201,-0.00280565,0.03841934,0.02334365,0.00201731,0.00534511,-0.00271459,0.00259338,-0.02112541,0.06392059,0.0013407,0.01165395],"last_embed":{"hash":"145p0zl","tokens":240}}},"text":null,"length":0,"last_read":{"hash":"145p0zl","at":1753423666807},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##4a.- The Winning Reports: Analysis#{1}","lines":[134,141],"size":737,"outlinks":[{"title":"The program shuffles the deck of lotto cards and arranges the lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto12.gif","line":5},{"title":"An MD past draw lotto report: Lottery sum-totals, sums, root sums.","target":"https://saliu.com/ScreenImgs/lotto13.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06394424,-0.05115382,-0.01758669,-0.02690627,-0.02845917,0.08059562,0.01619183,0.01906039,0.0053322,-0.02690814,0.01000359,-0.00078932,0.04611221,0.01405926,-0.01365947,-0.03661945,-0.00294424,0.00372629,-0.0387339,-0.03219528,0.07910037,-0.03092237,-0.04436469,-0.06027521,0.06486249,0.0247037,-0.02850361,-0.04420226,-0.03118578,-0.23018675,0.04188951,0.00474398,0.00704331,-0.05427087,-0.08561586,-0.0340637,-0.03250058,0.0721491,-0.01992971,0.03608814,0.04512945,-0.00104374,0.01241506,-0.01155265,0.00434997,-0.05192917,0.03724473,-0.00230621,0.05668855,0.00195251,-0.08979911,-0.02275665,-0.00970775,-0.01474311,0.06227128,0.04561971,0.08546846,0.10200359,0.0481592,0.04644061,0.01955088,0.07846673,-0.18754436,0.10830382,-0.04237394,0.03226735,-0.01140502,-0.01446752,0.00859253,0.07409635,0.01839137,0.03709102,-0.02282605,0.07107381,0.06346078,-0.03470015,-0.06430873,-0.0486889,-0.01648455,0.01319201,-0.05601599,0.01100855,0.01769684,0.00014577,0.00347843,0.06550487,0.01764146,0.05880824,0.02237755,-0.03480552,0.05474201,-0.01461673,0.04643216,0.05623655,0.02078335,0.01076425,0.0188687,-0.02358358,-0.04214387,0.10314599,-0.00387166,0.00526262,-0.00694866,0.00562873,0.06389599,-0.05366468,-0.01934687,-0.00351477,-0.02394427,0.01780218,0.03358688,0.01959218,0.06269114,-0.06997733,-0.02189754,0.0531123,-0.00059191,-0.01168122,0.02930966,0.01916553,-0.02204793,-0.00124769,-0.0031592,0.01307775,-0.00919711,-0.01540871,0.00220568,0.04663689,0.02587195,0.02949611,0.04616631,0.03371993,-0.16161284,-0.05300537,-0.03505666,0.02458371,-0.02600927,-0.0560174,-0.0121859,0.01217379,-0.0376185,0.00556333,0.0217511,-0.09355035,-0.04964715,0.06746306,0.00174235,0.02098421,0.00904116,-0.02645817,0.01764458,0.01348679,-0.03842862,-0.02344242,0.00110107,0.01636712,0.10127883,0.05023886,-0.04939553,-0.01235978,-0.07128946,-0.02525343,-0.03372488,0.08662684,0.00884142,-0.11327966,-0.02961125,0.0076954,-0.05974584,-0.13348489,-0.00823564,-0.00569208,-0.06098191,0.03804315,0.08265609,-0.01194224,-0.05560078,-0.04000178,-0.00437405,0.01070269,0.00787537,-0.07868453,-0.04793883,0.01702446,-0.06630391,-0.10671686,0.04742634,-0.07932729,0.03415665,0.07901105,-0.02291469,0.02021022,-0.01617002,-0.00258775,-0.04850833,0.00366123,-0.03568898,-0.05209025,0.02119214,-0.01974922,-0.00344364,0.02664064,0.00163241,0.00317326,-0.07574008,0.02920367,-0.01094426,-0.03797195,0.13690256,0.03379641,-0.0561851,0.06024206,0.06548262,0.0464125,-0.05461873,0.0000585,0.02776711,-0.00504887,-0.03976862,0.01223636,0.03027572,0.04642069,-0.05598809,-0.20249552,-0.05908771,-0.07171265,0.02535607,-0.00032618,-0.03915478,0.05909134,-0.03926682,0.02816575,0.05610239,0.0923665,-0.05010176,0.0230057,0.06667556,-0.01016972,-0.01230909,-0.0573939,-0.05652248,-0.07192597,0.052312,-0.04009604,0.0410238,-0.0017024,-0.08752888,0.03430173,-0.02760617,0.1467402,0.00778642,0.05182892,0.01258576,0.10164338,0.02929925,-0.00218343,-0.09688933,0.00575099,0.07019303,0.00365549,0.02547882,-0.05166917,-0.03817153,-0.0256503,0.00973639,-0.00506409,-0.09297092,-0.07323324,0.01792916,0.00212594,0.02806821,0.00384089,0.0269809,0.03112727,0.00008867,0.00031526,0.04893677,-0.00439363,-0.02110917,-0.07192229,-0.01517198,-0.03948494,0.01191826,-0.01714183,-0.02236439,0.03456219,-0.02485675,0.06327199,-0.00406038,-0.00347852,-0.00842286,0.06796359,0.0147677,-0.01963712,0.04473875,-0.01369687,0.05830119,-0.02819441,0.03512165,0.05799616,-0.02802035,-0.0294035,-0.03421112,0.00405524,-0.03368579,0.02701005,0.05293977,0.04175407,0.03220318,0.04435177,-0.0282148,0.03223718,-0.01127938,-0.02737035,0.01978121,-0.03348918,0.01986317,0.0483851,0.02826985,-0.23760155,-0.00061729,-0.00531101,0.08606299,-0.0053516,0.00140394,0.08041283,-0.05734476,0.05159583,-0.03593588,0.02125353,0.01145928,0.03502873,-0.09233923,0.0262112,-0.01290475,0.03140011,-0.02573951,0.04850444,-0.00436542,0.06508199,0.05251535,0.25611472,-0.00903044,-0.01976471,0.01018826,0.02364384,-0.03027463,0.01455298,0.03265881,0.01263468,-0.00535124,0.05675162,0.0181685,-0.03940277,0.00710244,-0.02622018,0.0529089,0.0211714,-0.00316474,-0.04220218,-0.00383488,0.00490312,-0.01403154,0.11031276,0.00612157,-0.02923202,-0.05219482,-0.01523487,0.0672249,-0.04085029,-0.05052678,-0.03709327,-0.02449426,0.01492673,0.01924746,0.03220195,0.00248602,0.00565862,0.00825286,0.02120986,-0.02062599,0.07006685,0.02833875,0.00239611],"last_embed":{"hash":"1o3q3mq","tokens":417}}},"text":null,"length":0,"last_read":{"hash":"1o3q3mq","at":1753423666871},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>","lines":[142,173],"size":2798,"outlinks":[{"title":"_**lottery filtering or combination reduction**_","target":"https://saliu.com/filters.html","line":9},{"title":"_**Lottery Utility, Lotto Software**_","target":"https://saliu.com/lottery-utility.html","line":10},{"title":"_**Cross-reference strategy files**_","target":"https://saliu.com/cross-lines.html","line":11},{"title":"_**Strategy-in-reverse for lottery and lotto**_","target":"https://saliu.com/reverse-strategy.html","line":12},{"title":"_**Basics of a Lottery, Lotto Strategy Based on Sums (Sum-Totals), Odd / Even, Low / High Numbers**_","target":"https://saliu.com/strategy.html","line":13},{"title":"The function of lotto software that checks a lottery strategy in the past.","target":"https://saliu.com/ScreenImgs/lotto14.gif","line":15},{"title":"The lottery strategy checking software is based on the W winning reports.","target":"https://saliu.com/ScreenImgs/lotto15.gif","line":17},{"title":"The lottery strategy checking software input: screen prompt or disk files.","target":"https://saliu.com/ScreenImgs/lotto16.gif","line":19},{"title":"Skip all input screens for lotto filters. Generate lotto combinations with no favorite numbers.","target":"https://saliu.com/ScreenImgs/lotto17.gif","line":21},{"title":"Type the filter values for each filter in your lotto strategy: minimum and maximum levels.","target":"https://saliu.com/ScreenImgs/lotto18.gif","line":23},{"title":"Ion Saliu's book of lottery: Lotto filters work with sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto19.gif","line":25},{"title":"Nobody has ever written better visuallotto book, lottery ebook for software use.","target":"https://saliu.com/ScreenImgs/lotto20.gif","line":27},{"title":"Lotto software saves the data of a lottery strategy to a disk file ST.","target":"https://saliu.com/ScreenImgs/lotto21.gif","line":29},{"title":"Lottery software also creates a useful file to show how many lotto combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto22.gif","line":31}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05195538,-0.05594484,-0.00698806,-0.03958134,-0.03580841,0.0818036,0.00919471,0.01722999,0.00448697,-0.0280923,0.00809793,0.01307871,0.03766741,0.03138656,-0.01964449,-0.01542913,-0.01405266,-0.0032941,-0.0438566,-0.03141568,0.06622941,-0.03528563,-0.03999404,-0.04232436,0.06810488,0.03118205,-0.04652628,-0.04352392,-0.03369701,-0.21869496,0.05234851,0.00440278,0.0156984,-0.04113306,-0.0706861,-0.04681271,-0.04347807,0.08008724,-0.01060971,0.02236075,0.0487609,0.00042867,0.01021741,-0.01086739,0.01454865,-0.07020766,0.03307122,0.009031,0.0698022,-0.00141256,-0.08248045,-0.03566469,-0.01983506,-0.01715056,0.06121387,0.02481084,0.08400527,0.09223626,0.05491089,0.0513472,0.03576609,0.0777813,-0.18701503,0.1182105,-0.03896691,0.04343228,-0.01764606,-0.00751757,0.01239739,0.07610782,0.01724022,0.03655206,-0.0078284,0.08183645,0.05897819,-0.02625992,-0.06601972,-0.03893163,-0.0029837,0.01916786,-0.06567968,0.01987946,0.02717928,0.00664075,-0.02191181,0.05591286,0.0239772,0.04180215,0.00326393,-0.01998289,0.06685102,-0.00950224,0.02951358,0.0409249,0.01323498,-0.00393632,0.01761333,-0.02717671,-0.06583656,0.11193436,-0.0224037,-0.00109266,-0.00649812,-0.00188159,0.05987875,-0.04446371,-0.01964083,0.00206351,-0.04107097,0.01137137,0.04248904,0.02172866,0.05978631,-0.06347997,-0.01058014,0.05061437,0.00357843,-0.00815887,0.01906085,0.03183968,-0.01563472,-0.00309516,-0.00546934,0.02357606,-0.0263792,-0.02755567,0.00493892,0.04528076,0.02571596,0.01745086,0.04572855,0.0338479,-0.15901847,-0.03621766,-0.03104806,0.0293089,-0.02259729,-0.0598496,-0.00859788,0.00412125,-0.0142533,0.02468967,0.03070074,-0.08615386,-0.03889892,0.07490401,-0.00063809,0.03349662,0.00273428,-0.02398572,0.01112695,0.01029676,-0.03781737,-0.02219493,-0.0046871,0.02445803,0.10898878,0.02887418,-0.04243111,-0.01724999,-0.08673362,-0.02140835,-0.02603235,0.08402757,0.01139444,-0.1215593,-0.03549763,-0.00264733,-0.048671,-0.1518482,0.00051774,0.00814067,-0.07348978,0.04964164,0.07064871,-0.01306354,-0.04961571,-0.03606109,0.026641,0.01964947,0.04188617,-0.07609128,-0.04176489,0.03372972,-0.0567232,-0.10491794,0.03831467,-0.07652528,0.03995885,0.0722972,-0.01891652,0.04525956,-0.01488985,-0.00953423,-0.06666688,-0.0024574,-0.04555731,-0.06139603,0.02446268,-0.03863284,0.00359574,0.02613244,0.00250043,0.00294841,-0.06793942,0.0199553,-0.00405895,-0.05184336,0.15291217,0.02900293,-0.05764828,0.0491161,0.06249283,0.03604121,-0.08381508,-0.00789159,0.03731482,0.01672513,-0.05499121,0.01378931,0.02442725,0.05552349,-0.03436822,-0.19499311,-0.07799159,-0.05411409,0.02798481,-0.00525959,-0.02444687,0.07139608,-0.03316166,0.01770579,0.04655417,0.08168772,-0.05642532,0.02978017,0.05819888,-0.01377288,-0.01366838,-0.05883344,-0.06312353,-0.05721079,0.05395601,-0.05407026,0.03560199,-0.02907377,-0.09398142,0.04066573,-0.01372505,0.13585073,-0.00577149,0.05671786,-0.01523128,0.09250607,0.03010623,-0.00345382,-0.10091202,0.01336653,0.06658603,0.0207345,0.02085391,-0.04726926,-0.04137969,-0.01186108,0.01379975,-0.02332858,-0.09291003,-0.09046215,0.00258767,-0.00050454,0.02713907,0.00083807,0.03775422,0.02841793,0.01671145,0.00405176,0.06548849,-0.02081556,-0.01677352,-0.05787186,0.01648174,-0.02898316,0.02099582,-0.00416559,-0.02429964,0.01447093,-0.02316389,0.06454105,-0.01160073,0.00131694,-0.00517055,0.09091543,-0.00398911,-0.01777191,0.03826375,-0.01028961,0.05725506,-0.01249022,0.03242091,0.05165229,-0.03686266,-0.02430125,-0.03763763,-0.00981224,-0.03148613,0.03224546,0.05524219,0.01900374,0.02997398,0.04090258,-0.01823946,0.02966098,-0.0116481,-0.02861214,0.01754535,-0.03675521,0.00582755,0.03766154,0.02158645,-0.22039312,0.00921546,-0.00296483,0.09561254,-0.019154,0.00096537,0.09961957,-0.04618527,0.03689258,-0.01407381,0.00415532,0.00548944,0.02946086,-0.10271376,0.03249423,-0.01753578,0.04951005,-0.02248139,0.03067011,-0.02129799,0.06227373,0.05611268,0.24713388,-0.00886837,-0.01676058,0.0093655,0.03277463,-0.00709573,0.032415,0.0215911,0.02420433,-0.0051996,0.04817568,0.01315635,-0.0491827,-0.01748088,-0.01660192,0.05043178,0.02908737,-0.00062861,-0.03932212,-0.0051714,-0.00574135,-0.02256783,0.10027498,0.00256018,-0.0113991,-0.06833572,-0.03727388,0.06642251,-0.03588381,-0.04716557,-0.01865165,-0.03681868,0.01724484,0.01705462,0.03702122,0.00626541,0.00008275,0.01659695,0.00047758,-0.03104088,0.06911312,0.02879205,-0.01716647],"last_embed":{"hash":"1ofijoh","tokens":255}}},"text":null,"length":0,"last_read":{"hash":"1ofijoh","at":1753423667009},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{1}","lines":[144,149],"size":971,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08943889,-0.03159685,0.00518736,-0.0403783,-0.02199198,0.05447714,0.01256528,-0.00492679,0.01294374,-0.02741836,-0.00359027,-0.02455766,0.06959052,0.02150435,-0.01909442,-0.04232925,0.01522618,-0.02278634,-0.02949345,-0.04263026,0.10136547,-0.02708854,-0.04756691,-0.10371368,0.05476887,0.02329781,-0.04711867,-0.06257164,-0.04669964,-0.23130989,0.02248498,0.02948883,-0.01011053,-0.04485548,-0.06332806,-0.05806324,-0.02499884,0.04863547,-0.04657124,0.04523715,0.01620734,0.00915583,-0.01718962,-0.03287903,-0.00335576,-0.05728495,0.00489224,-0.01673104,0.04615936,0.01365981,-0.0589295,-0.0098334,0.00666028,-0.00071079,0.01918644,0.02457523,0.07020181,0.10634212,0.02531938,0.04833883,0.05058949,0.06241821,-0.17846149,0.06382617,-0.03001954,0.04580627,-0.01047824,-0.03309881,0.01973047,0.0451042,0.00643568,0.02258869,-0.03410027,0.06852688,0.05412951,-0.04243506,-0.04686766,-0.05354086,-0.03597248,0.01207502,-0.06894149,0.02625186,0.02654507,0.029107,0.01068486,0.06503894,0.02798533,0.05036433,0.07436597,-0.03799984,0.05571951,-0.01319178,0.03338264,0.04563852,0.01370448,-0.00385018,0.05981634,-0.02709586,-0.00491969,0.12300198,-0.00537634,-0.01696311,0.01003101,0.02239635,0.05046393,-0.03721447,-0.01487556,-0.00274238,-0.02573225,0.05985947,0.03038969,0.00262252,0.07036745,-0.0511312,-0.04029008,0.0304085,-0.00358777,-0.03556444,0.01231339,0.01610093,-0.03261736,-0.00904249,-0.00335664,-0.00006923,0.01982643,-0.00762427,0.02685869,0.04476451,0.02280614,0.03223533,0.04815673,0.0203705,-0.15636104,-0.06197247,-0.00888888,-0.01030072,-0.02240443,-0.02855358,-0.0411516,-0.02439238,-0.0534358,-0.00436849,0.05696991,-0.08002245,-0.05423032,0.08104622,-0.02119512,0.0112051,0.01841003,-0.06555486,0.03912959,0.01180784,-0.04564915,-0.02106119,-0.00569937,0.01693841,0.08445828,0.09072126,-0.06253202,0.01390055,-0.04553267,0.00157187,-0.02185968,0.05448733,-0.03127873,-0.09871889,-0.02519676,0.03727398,-0.06421185,-0.09506767,-0.01004332,0.01176394,-0.06840721,0.01018435,0.06963072,-0.01610002,-0.04621201,-0.0148894,-0.04042144,-0.00150181,0.00201132,-0.04712731,-0.03983952,0.02039612,-0.04360364,-0.09163367,0.02365017,-0.08200075,0.03470603,0.04585718,-0.02609845,-0.02521373,-0.02603828,0.01311688,-0.02576529,-0.01390514,-0.05731213,-0.02973176,0.02708054,-0.01795071,0.03342256,-0.01374152,0.02278551,0.02325197,-0.01595314,0.01808982,-0.0435198,-0.03759497,0.1251877,0.04488707,-0.05431538,0.037041,0.0480019,0.06339067,-0.03277727,0.03016012,-0.01050111,0.0163077,-0.01152007,0.02450311,0.02310014,0.03450733,-0.08519091,-0.18629551,-0.04627072,-0.04595941,0.04356769,0.00600657,-0.03020965,0.0342407,-0.01385044,0.01240416,0.06971073,0.12103214,-0.07190037,-0.00569804,0.05454751,0.00522153,-0.01522816,-0.10231248,-0.04145979,-0.06209168,0.04807636,-0.01400212,0.01240651,0.00974523,-0.05176189,0.01605859,-0.02904614,0.12527218,0.01859669,0.0229242,0.01990769,0.10246755,0.04185953,-0.01313228,-0.03580005,0.02221403,0.06389029,-0.0607143,0.03721066,-0.05122156,-0.01548803,-0.02183417,0.02259862,0.00631257,-0.0939899,-0.04239307,0.02083556,0.03696324,0.02984023,-0.00935648,0.01747878,0.04083287,0.0072807,0.01781081,0.02225122,0.06839617,-0.03674853,-0.05086289,-0.02802043,-0.01864749,0.03009205,0.00718993,-0.04986921,0.03688183,-0.02491996,0.0986179,0.00011252,-0.00805576,-0.03826263,0.06730656,0.02078742,-0.00663721,0.08009823,-0.05476974,0.05383244,-0.01342011,0.07685009,0.08032381,-0.02182622,-0.03009586,-0.05206387,-0.01504292,-0.02915098,0.04722773,0.02932587,0.05848816,0.02573655,0.06284405,0.0038172,0.04416044,-0.00346369,-0.00514935,0.01444234,-0.01839699,0.03048012,0.00256107,0.04731273,-0.2547088,-0.00401661,-0.03676863,0.06766991,-0.01965862,-0.01457035,0.04092986,-0.0297707,0.0337366,-0.02985459,0.06356149,0.00532274,0.02829259,-0.13153225,0.00867019,-0.00114472,0.03187358,-0.04427662,0.0947778,-0.00288445,0.06778434,0.02396984,0.24695696,-0.01601711,0.00736652,0.01540313,0.02030893,-0.01832597,0.02586104,0.05841459,-0.00150832,0.0003088,0.11834664,0.00608121,-0.04810709,0.02877532,-0.00133452,0.03409059,0.01350252,0.00789401,-0.04156655,0.00378229,-0.02249063,-0.00853713,0.08237111,-0.00581543,-0.0509529,-0.06997087,0.01645446,0.0585118,-0.07004242,-0.0579299,-0.06149798,-0.030546,0.03905569,0.03467961,0.02520267,-0.00150052,-0.01642925,-0.0145625,0.02026227,-0.00209851,0.0991402,0.00166188,-0.00344086],"last_embed":{"hash":"1ef5xy8","tokens":416}}},"text":null,"length":0,"last_read":{"hash":"1ef5xy8","at":1753423667080},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##<u>5a.- Strategy Checking</u>#{7}","lines":[156,173],"size":1151,"outlinks":[{"title":"The function of lotto software that checks a lottery strategy in the past.","target":"https://saliu.com/ScreenImgs/lotto14.gif","line":1},{"title":"The lottery strategy checking software is based on the W winning reports.","target":"https://saliu.com/ScreenImgs/lotto15.gif","line":3},{"title":"The lottery strategy checking software input: screen prompt or disk files.","target":"https://saliu.com/ScreenImgs/lotto16.gif","line":5},{"title":"Skip all input screens for lotto filters. Generate lotto combinations with no favorite numbers.","target":"https://saliu.com/ScreenImgs/lotto17.gif","line":7},{"title":"Type the filter values for each filter in your lotto strategy: minimum and maximum levels.","target":"https://saliu.com/ScreenImgs/lotto18.gif","line":9},{"title":"Ion Saliu's book of lottery: Lotto filters work with sums, sum-totals, root sums.","target":"https://saliu.com/ScreenImgs/lotto19.gif","line":11},{"title":"Nobody has ever written better visuallotto book, lottery ebook for software use.","target":"https://saliu.com/ScreenImgs/lotto20.gif","line":13},{"title":"Lotto software saves the data of a lottery strategy to a disk file ST.","target":"https://saliu.com/ScreenImgs/lotto21.gif","line":15},{"title":"Lottery software also creates a useful file to show how many lotto combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto22.gif","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03604468,-0.02292393,-0.01544231,-0.02883666,-0.02932465,0.07729031,0.05354863,-0.00522432,0.02522931,-0.0018103,0.00950318,0.04638452,0.04641837,0.01976903,-0.03742703,-0.01017776,0.00238107,-0.01068118,-0.02404022,-0.03640389,0.0371454,-0.03088004,-0.02681747,-0.0511551,0.07773998,0.01539106,-0.01369284,-0.0641215,-0.05201937,-0.24105871,0.01526847,0.01128083,0.02953972,-0.07557707,-0.0657288,-0.04250436,-0.06560554,0.10771801,-0.00577185,0.02655466,0.00753863,0.00871309,0.02395615,-0.01556681,-0.02955881,-0.02213822,0.0290507,-0.03861384,0.06507991,0.00134731,-0.07929894,-0.05419132,0.02241874,0.02268237,0.03564481,0.02584395,0.06829575,0.06474974,0.06589332,0.0126818,0.03885476,0.03900291,-0.20023078,0.06539286,-0.06538458,0.03992384,-0.04651126,0.02166793,-0.00964533,0.07822198,0.00338877,-0.00897032,-0.03158273,0.03829098,0.0996252,-0.05504081,-0.07316052,-0.04129719,-0.00209216,0.00551175,-0.04920684,0.02418087,0.00042055,0.02294932,-0.00604721,0.05321774,0.04329145,0.05844869,0.09658197,-0.00698468,0.09467871,0.00707256,-0.0059014,0.03978969,-0.01097114,0.01031404,0.06395625,-0.04010687,-0.03431045,0.11708766,0.00958718,0.01400966,0.00795886,0.02040023,0.03789527,-0.05858511,-0.03656509,-0.02038224,-0.02621888,0.05765866,0.05668528,0.01283244,0.04259326,-0.08306722,-0.0177498,0.01842822,-0.01365758,-0.00082792,-0.00104859,-0.00470452,-0.03313394,-0.02886358,-0.01093742,-0.00209887,0.00868194,0.04918833,0.04208808,0.06536569,0.02827666,-0.00716285,0.01076661,-0.01176306,-0.17207588,-0.02210265,-0.05401315,-0.00874336,0.00384191,-0.01456947,-0.02927915,0.00053206,-0.04664234,-0.00457753,0.03880284,-0.08849762,-0.0395231,0.11936351,-0.03152874,0.02376126,-0.01460837,-0.06230359,0.00548277,-0.00031237,-0.06884941,-0.03293913,-0.02721627,0.02582535,0.04142202,0.08827151,-0.03039365,-0.02015765,-0.02732813,-0.0210878,-0.04189733,0.07138028,0.00612272,-0.08941254,-0.04753545,0.0376416,-0.03723398,-0.10500822,-0.03217493,0.01671564,-0.03271278,-0.00663535,0.05726843,-0.00611625,-0.05784783,-0.03319169,-0.03587307,0.00670974,0.02884362,-0.0227507,-0.04551044,0.02716119,-0.02957243,-0.07731099,0.04418045,-0.06875312,0.06441952,0.03396083,-0.04473631,-0.00773404,-0.03624138,0.03073922,-0.02349062,-0.01707253,-0.00584957,-0.02107998,0.05894484,-0.03655049,0.04597315,0.00774259,0.01240405,-0.0034066,-0.03598019,0.01962916,-0.0443197,-0.04226807,0.14359909,0.01669769,-0.05517041,0.04294805,0.05837938,0.05049257,-0.05582232,-0.01637947,-0.0124388,0.0172396,-0.06450005,-0.02263171,0.02953284,0.05225255,-0.03430669,-0.19748428,-0.07582273,0.00514477,0.07721642,-0.04494292,-0.03552668,0.02865586,-0.02001228,0.08208387,0.09228729,0.11419512,-0.00883351,-0.04648073,0.03690725,0.00637311,-0.00516033,-0.08434546,-0.04052861,-0.05319663,0.05170001,-0.01801579,-0.02164933,-0.04429113,-0.01914131,0.01281367,-0.02092469,0.12003993,0.0177907,0.00003892,0.00352521,0.07206503,0.02780169,-0.01076857,-0.01954151,0.0317005,0.04605212,-0.03137901,0.02148665,-0.05887698,-0.02899147,-0.04580045,-0.00267166,0.00618903,-0.09945979,-0.02902788,0.02795748,0.02976442,0.01388762,-0.01731391,0.03718493,0.01923006,0.00936609,0.01344712,0.05190078,0.07001518,-0.02685892,-0.07853483,-0.01562629,0.00262593,0.00728902,-0.05537948,-0.03047976,0.05471465,-0.04776588,0.09254158,0.02557794,-0.01495565,-0.0188346,0.04870856,-0.00232455,0.00719154,0.05635948,-0.04898782,0.0236015,-0.03809534,0.03275142,0.05588732,-0.06796101,-0.01881246,-0.0151156,0.01006973,-0.04768923,0.03163091,0.04433015,0.05043942,0.03929051,0.03326609,-0.010093,0.04623034,-0.00217281,-0.03206096,0.01857127,-0.00576252,0.01637846,-0.03141831,0.02233044,-0.24347109,0.02341856,-0.01174229,0.07552739,-0.02165571,-0.02208157,0.06667578,-0.01162145,0.0569369,-0.00426589,0.05033771,0.01937742,0.03496196,-0.09332235,-0.02497711,-0.00729174,0.02039162,-0.01270975,0.09532154,0.00183909,0.08408962,0.03159117,0.22787586,0.00033765,0.00859257,-0.02293182,0.01990072,-0.01719447,-0.03382719,0.03591273,0.01180112,-0.00717261,0.10360173,0.01480543,-0.03298935,0.01950195,-0.00372818,0.04728277,-0.02188415,0.02067322,-0.02343513,0.01296507,-0.01687002,-0.02102945,0.10629643,-0.00335818,-0.02690563,-0.08570518,0.00863205,0.05438605,-0.07160108,-0.08270799,-0.04810816,-0.01086253,0.0295944,0.04223883,0.03811628,0.0063511,-0.00509857,0.02327713,-0.02101373,0.00261852,0.07609533,0.00725206,0.02778072],"last_embed":{"hash":"8qx4fo","tokens":425}}},"text":null,"length":0,"last_read":{"hash":"8qx4fo","at":1753423667203},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report","lines":[174,187],"size":1476,"outlinks":[{"title":"Your lotto software informs of successfully creating the lottery strategy.","target":"https://saliu.com/ScreenImgs/lotto23.gif","line":7},{"title":"The lottery strategy report shows the filters and parameters of your lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto24.gif","line":9},{"title":"A loto strategy has numbers of hits in the past, frequency, skips of loterie drawings between hits.","target":"https://saliu.com/ScreenImgs/lotto25.gif","line":11},{"title":"The software shows lottery strategy reports for inter-related filters: W, MD, 1 to 6-number groups, sums.","target":"https://saliu.com/ScreenImgs/lotto26.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0313454,-0.02108035,-0.01781209,-0.02496168,-0.03039738,0.0785013,0.0518245,-0.00828682,0.02643844,-0.000819,0.01016681,0.04849349,0.04393928,0.01966592,-0.0390101,-0.01128309,0.00300731,-0.01245842,-0.02141259,-0.03688844,0.03858354,-0.03066225,-0.02468655,-0.04729146,0.07721242,0.01632223,-0.01419048,-0.066054,-0.05293635,-0.24120203,0.01373829,0.00988842,0.03047078,-0.07428875,-0.06391431,-0.04321843,-0.0680138,0.10688937,-0.00478636,0.02755488,0.00795226,0.01011494,0.02394937,-0.01158667,-0.02749581,-0.02340041,0.02834925,-0.03614151,0.06627354,-0.00138402,-0.07658121,-0.05680554,0.0206186,0.02387729,0.03612039,0.02794565,0.07084978,0.06426112,0.06701485,0.01269108,0.03941828,0.03910468,-0.20126845,0.06774764,-0.06727739,0.04150351,-0.04561363,0.02100599,-0.01216983,0.07914794,0.00530161,-0.01104547,-0.03024576,0.03975442,0.10191228,-0.05471105,-0.07230437,-0.04044975,-0.0001502,0.0066579,-0.04971716,0.02409186,0.00371309,0.02399744,-0.00801816,0.05162977,0.04280512,0.05851725,0.09923829,-0.0052481,0.09475756,0.00975218,-0.00310287,0.03883421,-0.01132151,0.00795846,0.06404064,-0.03941767,-0.03480528,0.11562961,0.01285476,0.01671325,0.00965657,0.02007099,0.03513084,-0.05854465,-0.03431337,-0.02043413,-0.02520075,0.05815631,0.05831297,0.01296491,0.04530245,-0.08325414,-0.01820305,0.0155964,-0.01201972,0.00042455,-0.00189792,-0.00420967,-0.02924422,-0.02633463,-0.01005205,-0.00242868,0.01185497,0.04621669,0.03968935,0.06423192,0.02965266,-0.00550672,0.00836918,-0.01177817,-0.17147511,-0.02316617,-0.05577834,-0.01068006,0.00663631,-0.01687489,-0.02903647,0.00089079,-0.04903801,-0.00477002,0.03881978,-0.08914208,-0.04039024,0.12196595,-0.03093812,0.02523021,-0.01668294,-0.06038298,0.00411623,-0.00101373,-0.06966736,-0.03083195,-0.02800999,0.02518979,0.0373874,0.08808611,-0.03230722,-0.02027383,-0.02959887,-0.02124006,-0.04125607,0.06851368,0.00573378,-0.08904862,-0.04609877,0.03608076,-0.0371828,-0.10617228,-0.032715,0.01616671,-0.03380119,-0.00825046,0.05522486,-0.00493889,-0.0551551,-0.03027965,-0.03493383,0.00651224,0.02708022,-0.01948453,-0.0467435,0.03199705,-0.02947929,-0.07294595,0.04773854,-0.07041144,0.06576669,0.03385003,-0.04431568,-0.00627999,-0.03443126,0.03494467,-0.02448622,-0.01619441,-0.00813248,-0.01911399,0.05984984,-0.04151858,0.04780027,0.00969361,0.01191204,-0.00545632,-0.03563382,0.01780631,-0.04456646,-0.04219769,0.14109735,0.01968394,-0.05628181,0.04333098,0.05922668,0.05038378,-0.05566975,-0.01712402,-0.01156054,0.01589997,-0.06580952,-0.0238752,0.03375225,0.05140356,-0.03015154,-0.19771776,-0.0750572,0.00559968,0.07780697,-0.04271968,-0.03392377,0.02583932,-0.02039809,0.08043743,0.09422707,0.10902823,-0.00388161,-0.04993954,0.03720052,0.00261178,-0.00435525,-0.08453591,-0.0414028,-0.05322777,0.05306749,-0.02031039,-0.020295,-0.04651075,-0.01771796,0.01136884,-0.01944852,0.11853568,0.0146958,-0.00237327,0.00398058,0.07225971,0.02916939,-0.01013936,-0.01887371,0.03401338,0.04455512,-0.03124798,0.01850272,-0.05879791,-0.03195488,-0.04492428,-0.00047407,0.00443699,-0.10078266,-0.03202442,0.02574057,0.03133355,0.01381542,-0.01729891,0.03271423,0.01878293,0.01075457,0.01298669,0.05158138,0.07032768,-0.02884414,-0.07694421,-0.01328738,-0.00052892,0.01027094,-0.05789233,-0.03333369,0.05809015,-0.04563781,0.09389184,0.02421564,-0.01663844,-0.01653926,0.04782195,0.0001212,0.00528795,0.05930275,-0.05274117,0.02160007,-0.03704899,0.03207683,0.05902567,-0.06950172,-0.02274515,-0.01411884,0.00936507,-0.04749996,0.02898866,0.04548548,0.0474267,0.03880757,0.03252084,-0.01293635,0.04523546,-0.00379609,-0.03172112,0.01582488,-0.00371143,0.01403303,-0.02904892,0.02178933,-0.24575186,0.0232211,-0.0095706,0.07177967,-0.02439048,-0.02099524,0.06735593,-0.01118875,0.05831424,0.00005198,0.04958097,0.02148099,0.03620425,-0.09533905,-0.02291001,-0.00761467,0.01961216,-0.0150739,0.09748326,0.00119946,0.08350436,0.03168336,0.22735083,-0.00562472,0.00710959,-0.02284608,0.02039386,-0.01854832,-0.03729172,0.03407953,0.00914864,-0.00605769,0.10193098,0.01297249,-0.0329493,0.02231013,0.00005617,0.04900422,-0.02321631,0.02049512,-0.02501961,0.01143585,-0.0186252,-0.01891869,0.10576015,-0.00188782,-0.02758071,-0.08607125,0.00762734,0.05317419,-0.06762671,-0.07825236,-0.0468321,-0.0112727,0.02918976,0.04268215,0.0404789,0.00427615,-0.00369316,0.02489985,-0.02138173,-0.00029648,0.07184502,0.00740049,0.02566161],"last_embed":{"hash":"1we7cx9","tokens":423}}},"text":null,"length":0,"last_read":{"hash":"1we7cx9","at":1753423667337},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##6a.- Strategy Checking: Analysis of the report#{1}","lines":[176,187],"size":1424,"outlinks":[{"title":"Your lotto software informs of successfully creating the lottery strategy.","target":"https://saliu.com/ScreenImgs/lotto23.gif","line":5},{"title":"The lottery strategy report shows the filters and parameters of your lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto24.gif","line":7},{"title":"A loto strategy has numbers of hits in the past, frequency, skips of loterie drawings between hits.","target":"https://saliu.com/ScreenImgs/lotto25.gif","line":9},{"title":"The software shows lottery strategy reports for inter-related filters: W, MD, 1 to 6-number groups, sums.","target":"https://saliu.com/ScreenImgs/lotto26.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06637608,-0.00063583,-0.01949567,-0.05973385,-0.04285653,0.07487985,-0.0057566,-0.04129818,0.05539846,-0.01580242,0.00402931,0.00711934,0.05716263,-0.01307787,-0.02202767,-0.00098467,0.00186033,-0.02581791,-0.08158217,-0.03730273,0.05446614,-0.03771092,-0.03543299,-0.07849517,0.04817801,0.01607397,-0.01705137,-0.06175221,-0.02854061,-0.24071196,0.06122181,0.02248275,0.02184066,-0.05247983,-0.06184645,-0.04731843,-0.05264204,0.11594939,-0.03114496,0.0320631,-0.00128814,0.03798064,0.02143162,-0.00267975,-0.02835796,-0.02829915,-0.01298218,0.01502694,0.07291835,-0.00751975,-0.05197233,-0.06769841,0.00703639,-0.00821088,0.04056509,0.03109453,0.04214194,0.07164948,0.02797547,0.03066419,0.05432855,0.07594997,-0.17759319,0.03808524,0.00489032,0.04008204,-0.03287265,-0.00703871,-0.00884154,0.08681694,-0.00820405,0.02673324,-0.01427804,0.04824098,0.05642082,-0.03935416,-0.0816989,-0.0207044,-0.03187929,0.01494614,-0.06844226,0.03177478,-0.02477721,0.01997849,0.00206848,0.04558078,0.02392573,0.0806825,0.04753302,-0.02421761,0.09366044,0.04614275,-0.00174951,0.02188722,0.01659402,-0.01790468,0.01988976,-0.04003032,-0.01701476,0.09866676,-0.01066286,-0.00649429,0.03030752,0.02954834,0.01018682,-0.06673066,-0.06556603,-0.01162919,-0.0448559,0.04186591,0.04465411,0.02056335,0.08324182,-0.10409323,-0.03323599,0.00644973,-0.01755735,0.00922182,-0.01846568,0.0266627,0.01002724,-0.01274068,-0.01579231,-0.01654975,0.010967,-0.01899495,0.01800362,0.10038534,0.04334661,0.00126055,-0.00197016,0.0147448,-0.14874309,-0.03780046,-0.02466684,-0.0049468,-0.00941783,-0.01473429,-0.03346213,-0.02429641,-0.03835499,-0.02620946,0.08878822,-0.09174094,0.02411421,0.10734158,0.02288834,0.04467743,-0.01944014,-0.02750408,0.01648117,-0.00919062,-0.03170159,-0.05351489,0.00199591,0.02680536,0.06495529,0.06487247,-0.02981903,-0.00280133,-0.0984865,-0.01231096,-0.01163391,0.0981089,-0.00954291,-0.07751624,-0.02205904,0.04554635,-0.04882181,-0.1084516,-0.00820806,0.01451477,-0.01859208,-0.00704542,0.11038047,-0.02676371,-0.06748479,-0.03980649,0.00254913,0.0231641,0.04797812,-0.02930225,-0.06527847,0.01390137,-0.02522346,-0.11415486,0.03766832,-0.02633839,0.02550657,0.01090655,-0.05376334,-0.0175886,-0.02207615,0.03382382,-0.0413559,-0.02608351,-0.04846971,-0.0446847,0.07950815,-0.03734259,0.0512985,-0.00655227,0.02003662,0.03161233,-0.03761766,0.02634326,-0.00845771,-0.03085629,0.11541086,0.03251489,-0.07266502,0.0191406,0.05095209,0.05966192,-0.07920375,0.0443381,0.01300091,0.03225812,-0.02554533,0.0063372,0.02006779,0.0232708,-0.00881536,-0.1732526,-0.07899966,-0.00761793,0.04431526,-0.01775711,-0.01128712,0.02125059,-0.0119607,0.03729887,0.04961708,0.09280031,-0.0367885,-0.01246886,0.05030892,-0.02350215,-0.02789003,-0.09049074,-0.01629018,-0.06359389,0.06653906,-0.0015255,-0.02676719,-0.03304085,-0.07213422,0.04225981,-0.01737605,0.13709,-0.00347043,0.00519424,-0.02527321,0.08412606,0.03670032,-0.03016823,0.01028338,0.01837246,0.02482422,0.01443087,-0.00058314,-0.00589215,-0.01011324,-0.03224268,0.02177334,-0.00241933,-0.09295161,-0.02949594,0.01018819,-0.00117357,0.03151786,0.01427397,0.06365087,0.02957169,0.00965248,0.04183697,0.05774883,0.05396994,-0.0523269,-0.07335782,0.02705966,-0.03329795,0.0004415,-0.03113286,-0.04487892,0.03425827,0.00575884,0.06934726,0.00238992,-0.0222784,0.00659275,0.02172221,-0.03152199,0.00602733,0.04260596,-0.03744636,0.03364011,-0.00001432,0.02242265,0.04145063,-0.06660664,-0.01415177,-0.02763829,0.03523991,-0.04853809,0.00048125,0.04550826,0.00535451,-0.0121454,0.07649245,0.03895042,0.03173196,-0.00951204,-0.03089614,0.02455981,0.00377877,0.04749521,-0.00495152,0.01807745,-0.24069567,0.0302363,-0.02170858,0.10830416,-0.03796007,-0.0256888,0.09559441,0.0073856,0.02489412,-0.00605525,0.01694241,0.01098739,0.04020167,-0.13061687,0.03073273,-0.0282202,0.01682019,-0.00467566,0.05047763,-0.05558706,0.08809602,0.0339759,0.22946015,-0.00517082,-0.00246563,0.04360134,0.00678384,0.0389943,0.02386594,0.02781116,0.00409434,-0.01656123,0.05201005,-0.00318711,-0.04722891,0.00764996,0.01025093,0.03991612,-0.01958446,0.00665431,-0.05350691,-0.00313453,-0.02849905,-0.02408037,0.13142736,-0.00079689,-0.01524306,-0.088184,-0.03148781,0.05947008,-0.07719731,-0.07277256,-0.04660043,-0.04513931,0.03607263,0.06089439,0.0445615,0.0335793,0.02095704,0.01338641,0.02697134,-0.0236489,0.07874886,0.02420498,-0.03916156],"last_embed":{"hash":"186lrxb","tokens":420}}},"text":null,"length":0,"last_read":{"hash":"186lrxb","at":1753423667471},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past","lines":[188,213],"size":2297,"outlinks":[{"title":"Lottery software checking how the strategy would have fared in the past lotto drawings.","target":"https://saliu.com/ScreenImgs/lotto27.gif","line":13},{"title":"The same lottery data file is required to check the strategy combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto28.gif","line":15},{"title":"The same biggest lotto is required to check the lottery strategy hits in the past.","target":"https://saliu.com/ScreenImgs/lotto29.gif","line":17},{"title":"The lotto software can enable special inner or innate filters.","target":"https://saliu.com/ScreenImgs/lotto30.gif","line":19},{"title":"Lottery software can screen input filters or use a filter file created by lotto strategy checking.","target":"https://saliu.com/ScreenImgs/lotto31.gif","line":21},{"title":"The best lotto software can use the hits file created by the check function.","target":"https://saliu.com/ScreenImgs/lotto32.gif","line":23},{"title":"The strategy hits in the past generates and counts lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto33.gif","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0690608,0.00459586,-0.01708261,-0.05976402,-0.04231393,0.0767986,-0.00811936,-0.04254051,0.05448288,-0.01627729,0.00726442,0.00457571,0.05639587,-0.01517851,-0.02239103,-0.0021954,0.00018183,-0.02443581,-0.07740555,-0.03781422,0.05383524,-0.03531365,-0.0371593,-0.07504458,0.04840596,0.01728719,-0.01626252,-0.06110197,-0.02765704,-0.24234636,0.0591458,0.02406936,0.02453826,-0.05199058,-0.06083064,-0.04143508,-0.05298949,0.11676729,-0.02784626,0.03467783,-0.00191035,0.03994176,0.02450543,-0.00013237,-0.02797138,-0.0292124,-0.01175246,0.01412271,0.0711517,-0.00661719,-0.05150362,-0.06676992,0.00482583,-0.00650472,0.03663341,0.0263771,0.04332726,0.0708586,0.03010548,0.02994782,0.05735005,0.07137845,-0.18160522,0.04127723,0.00691634,0.03992179,-0.03725522,-0.00380452,-0.01268874,0.08641479,-0.0091017,0.03042788,-0.01361376,0.04780016,0.05486422,-0.04082144,-0.084226,-0.01942852,-0.03243101,0.016556,-0.0666223,0.03316419,-0.02493723,0.0222718,-0.00130431,0.04310564,0.02043558,0.07968386,0.04821011,-0.02259764,0.09522678,0.0463615,-0.00253341,0.02149079,0.01844301,-0.01734838,0.01909644,-0.03812186,-0.0184246,0.09797361,-0.0127097,-0.00291634,0.02866095,0.03124698,0.00894697,-0.06528292,-0.06276187,-0.01408621,-0.04642822,0.04204191,0.04230746,0.01917125,0.08055583,-0.10793983,-0.03197059,0.00564631,-0.01812168,0.0098237,-0.01860609,0.02380466,0.00943663,-0.01161196,-0.01640192,-0.01428389,0.01274411,-0.01614723,0.01614605,0.09932559,0.04445925,0.00451826,-0.002419,0.01360078,-0.14858919,-0.03688659,-0.01987881,-0.0098587,-0.00602956,-0.01429753,-0.03426249,-0.02485131,-0.04050508,-0.02410692,0.09009749,-0.09078915,0.02374458,0.11129437,0.02174,0.04687466,-0.02315247,-0.0262006,0.01505032,-0.00910387,-0.03400064,-0.05367776,0.00127353,0.02726214,0.06071965,0.0635227,-0.02639491,-0.00555125,-0.0983481,-0.00912551,-0.01238456,0.09817083,-0.00743417,-0.0756303,-0.02237157,0.04142962,-0.04803318,-0.10727637,-0.00975162,0.0165079,-0.01866595,-0.00728492,0.10723551,-0.03162464,-0.06651677,-0.03614316,0.00265559,0.02365008,0.04835273,-0.03128243,-0.06254423,0.01435791,-0.02364276,-0.11465947,0.0365678,-0.02592109,0.02753467,0.01030481,-0.0546736,-0.01716663,-0.02479212,0.03436074,-0.04288092,-0.02962593,-0.05177468,-0.040711,0.08039823,-0.04004218,0.05317882,-0.0077726,0.01805372,0.0298907,-0.03791717,0.02615668,-0.01127399,-0.03177114,0.11644215,0.03710514,-0.07159442,0.01923362,0.05129872,0.06052844,-0.07977087,0.04551799,0.01298173,0.03074979,-0.02988116,0.0066958,0.0224532,0.0263646,-0.00662151,-0.17217603,-0.07846679,-0.0075757,0.04669404,-0.01953365,-0.01211123,0.01605018,-0.00879492,0.03871331,0.0505693,0.0906427,-0.03511614,-0.01749332,0.04898744,-0.02699231,-0.02503469,-0.09047029,-0.0173037,-0.0663261,0.06541841,-0.0024629,-0.02704978,-0.03599042,-0.06969012,0.03892578,-0.01593684,0.13568242,-0.00532603,0.0061405,-0.0233346,0.08290157,0.04033361,-0.03434958,0.00780365,0.02263818,0.0242034,0.01584836,-0.00152308,-0.00599308,-0.0110629,-0.02835777,0.02357279,-0.00703658,-0.0907035,-0.02857893,0.00471424,-0.00023393,0.03183309,0.01564935,0.06415048,0.02930675,0.01087432,0.03988241,0.05661837,0.05094754,-0.055663,-0.07246686,0.02733339,-0.03947319,0.00095058,-0.03241098,-0.04422081,0.03527946,0.00688238,0.06999721,-0.00138365,-0.02331936,0.00862829,0.02368812,-0.03135755,0.00677753,0.04532975,-0.03996964,0.03325568,0.00297487,0.02418026,0.04354021,-0.0673371,-0.01410872,-0.02738204,0.0360446,-0.04796538,-0.00306465,0.04663227,0.00730961,-0.01341954,0.0784009,0.04165975,0.0321404,-0.01093707,-0.03231471,0.02361654,0.00375146,0.04556887,-0.00399945,0.01809942,-0.2429799,0.02843899,-0.02145607,0.10496927,-0.03782044,-0.02317244,0.09491851,0.00720747,0.02420095,-0.00401014,0.01689454,0.0122521,0.03967572,-0.12877202,0.03148095,-0.02683011,0.01692733,-0.00528135,0.05355892,-0.05675813,0.0882633,0.03108574,0.22887567,-0.00765252,-0.00164776,0.03771753,0.00843421,0.03931876,0.02048882,0.02651234,0.00100503,-0.01264146,0.05451033,-0.00448522,-0.04571886,0.00786779,0.01368619,0.04076021,-0.0218043,0.00791909,-0.05199793,-0.00480875,-0.02912481,-0.02553834,0.13230126,-0.0027321,-0.01514935,-0.09128597,-0.03204969,0.0598667,-0.07440092,-0.06925759,-0.04674716,-0.04283401,0.03443799,0.06633646,0.04459027,0.03518951,0.02265099,0.01220555,0.02833172,-0.0194535,0.07735201,0.02736563,-0.03894615],"last_embed":{"hash":"5swd0s","tokens":420}}},"text":null,"length":0,"last_read":{"hash":"5swd0s","at":1753423667589},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past#{1}","lines":[190,213],"size":2198,"outlinks":[{"title":"Lottery software checking how the strategy would have fared in the past lotto drawings.","target":"https://saliu.com/ScreenImgs/lotto27.gif","line":11},{"title":"The same lottery data file is required to check the strategy combinations in the past.","target":"https://saliu.com/ScreenImgs/lotto28.gif","line":13},{"title":"The same biggest lotto is required to check the lottery strategy hits in the past.","target":"https://saliu.com/ScreenImgs/lotto29.gif","line":15},{"title":"The lotto software can enable special inner or innate filters.","target":"https://saliu.com/ScreenImgs/lotto30.gif","line":17},{"title":"Lottery software can screen input filters or use a filter file created by lotto strategy checking.","target":"https://saliu.com/ScreenImgs/lotto31.gif","line":19},{"title":"The best lotto software can use the hits file created by the check function.","target":"https://saliu.com/ScreenImgs/lotto32.gif","line":21},{"title":"The strategy hits in the past generates and counts lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto33.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05679872,-0.03070679,-0.00852797,-0.05873175,-0.04233913,0.06331722,-0.0330659,-0.00558572,0.01890302,0.00578979,-0.00911143,-0.01397546,0.0465151,-0.00614516,-0.00565545,-0.01227319,0.02074615,-0.00929213,-0.09896345,-0.03961974,0.07641942,-0.03163214,-0.0473175,-0.13131569,0.01503678,0.01582289,-0.03987389,-0.04694719,-0.02395016,-0.2382074,0.0661025,0.056461,-0.00420349,-0.05473266,-0.06988832,-0.05250963,-0.04245028,0.07131744,-0.08490226,0.00360397,0.01091089,0.01963724,0.01807574,-0.00701739,-0.00425282,-0.06324783,-0.01136544,0.00597871,0.08810414,0.04212719,-0.02356051,-0.03314593,0.0107611,0.00792916,0.03344756,0.03243376,0.03174561,0.07505555,0.01483833,0.03844152,0.05162355,0.08515948,-0.16634005,0.06001621,0.0037508,0.02930955,-0.01254255,-0.01685722,0.00952002,0.06099955,0.00063275,0.03463333,-0.04045933,0.06474473,0.05833047,-0.05570507,-0.09250294,-0.05052483,-0.04437687,0.03127965,-0.07708388,0.0383574,-0.02839704,0.03397357,-0.0157693,0.0373546,0.0321241,0.04162444,0.03123637,-0.01010433,0.04164757,0.01896897,0.01796637,0.04913289,-0.01386993,0.01531321,0.04841025,-0.04947714,-0.00200019,0.10180192,-0.01559565,0.00596057,0.03913948,-0.05245399,0.01041535,-0.04988354,-0.03438109,0.00720713,-0.05320285,0.04759346,0.06579594,0.02592225,0.03653637,-0.07631722,-0.07584533,0.01315527,-0.03181119,-0.00500627,-0.01354201,0.0162191,-0.00010489,0.01200798,-0.02094207,-0.00372076,0.01672837,-0.00038125,0.02766995,0.06993123,0.04054084,0.00027859,0.01089289,0.04321223,-0.13322495,-0.03599571,-0.00361653,-0.0190783,-0.02188217,-0.0150721,-0.02478671,-0.02304645,-0.0202514,-0.03670253,0.05061334,-0.06380162,0.01344876,0.06742531,-0.00667953,0.04534183,0.00523409,-0.01384849,0.01310021,0.00076966,-0.03109297,-0.03431548,0.04124762,0.04084248,0.10330435,0.07221057,-0.02799517,0.01703798,-0.07981297,-0.04192436,-0.01348172,0.07965501,-0.01223077,-0.10187536,-0.01865173,0.06539351,-0.03962981,-0.08362791,-0.01199626,0.00012232,-0.03754827,-0.00717903,0.11077987,-0.02389957,-0.07293289,-0.03550776,0.000607,-0.01437795,-0.00804546,-0.06060673,-0.06232388,-0.01156365,-0.03065123,-0.11732262,0.02248487,-0.0392466,0.00243149,0.05650062,0.00961401,0.00152303,-0.01634247,0.00369757,-0.01886719,-0.02867761,-0.03814596,-0.05330727,0.06112231,-0.05597787,0.03669446,0.01104324,0.00991903,0.03432681,-0.0047965,-0.00911799,-0.03580076,-0.05874718,0.11704888,0.03149951,-0.07287312,0.00879727,0.0734654,0.1132862,-0.1100753,0.06753777,0.01219718,0.00049755,-0.01991896,0.00594669,0.06095831,-0.00718735,-0.0397042,-0.19091962,-0.03242702,-0.04706486,0.03007329,-0.02217773,-0.02778767,0.00301535,-0.02804534,0.01172566,0.04477655,0.10119315,-0.06870151,0.01835855,0.06184969,-0.0110101,-0.02053297,-0.11079683,-0.02322647,-0.03722586,0.07769896,-0.01185873,0.00357188,-0.00807688,-0.08553476,0.03476664,-0.00780236,0.1365677,0.00906884,0.03556322,-0.01768328,0.09538743,0.05148345,-0.01612764,-0.01615674,0.02255368,0.02156357,-0.01508153,0.0212529,-0.02626503,-0.00543233,-0.03033113,0.03241884,0.00808477,-0.10486469,0.00953778,-0.0199248,-0.00740422,0.04002539,-0.00614488,0.06218268,0.0085706,0.01569161,0.05641736,0.02586622,0.05763878,-0.05117032,-0.05501299,0.02165819,-0.01496944,0.00644433,0.03374882,-0.03940713,0.05295587,-0.01419039,0.04945617,0.03345784,-0.03167546,-0.02123425,0.03582275,-0.01956678,-0.0163921,0.03821309,-0.02598067,0.08628946,0.01125483,0.02538343,0.04230829,-0.04456042,0.01102289,-0.03659162,0.00125953,-0.03644208,0.01016874,0.07157792,0.02023398,0.01572012,0.07117099,0.06409539,0.05746149,-0.01980186,-0.0404607,0.01088155,0.01401925,0.03647884,0.02453335,0.03746885,-0.24617879,0.00721821,-0.01243444,0.0902597,-0.03292068,-0.02195677,0.08222201,0.01884958,0.02534138,-0.00541207,0.00689104,-0.00352891,0.01420959,-0.1099559,0.04767692,-0.03193919,0.02360004,-0.00218567,0.04763483,-0.04327674,0.08485827,0.04575555,0.24264362,-0.0207941,-0.00144482,0.00676657,0.01897022,0.02180413,0.0406663,0.01101757,0.0094751,-0.00128044,0.0380089,0.01400594,-0.04968112,0.01568799,0.00256321,0.02228922,-0.0074248,0.022377,-0.05647092,0.00805516,-0.05260852,-0.00670561,0.10348179,0.00754375,-0.00002587,-0.06586502,0.01157368,0.02758685,-0.08045352,-0.04877258,-0.0438554,-0.04569145,0.0195667,0.01671743,0.06317648,0.02508575,0.01302866,0.04183779,0.02937413,-0.01480721,0.08532009,0.00896421,-0.04401659],"last_embed":{"hash":"fgrppz","tokens":384}}},"text":null,"length":0,"last_read":{"hash":"fgrppz","at":1753423667708},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations","lines":[214,229],"size":1347,"outlinks":[{"title":"The greatest feature of the best lottery software: generate winning lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto34.gif","line":11},{"title":"Lottery software uses the same results file, with all past winning numbers.","target":"https://saliu.com/ScreenImgs/lotto35.gif","line":13},{"title":"Best lottery software generates combinations for the same lotto game format as W reports.","target":"https://saliu.com/ScreenImgs/lotto36.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05834612,-0.03189409,-0.00773239,-0.05396876,-0.04168954,0.0648224,-0.03497935,-0.00749501,0.02065219,0.00590054,-0.00890145,-0.01379907,0.04561591,-0.00613935,-0.00700107,-0.01081027,0.0170495,-0.00995774,-0.10028867,-0.03945246,0.07626805,-0.03067486,-0.04607439,-0.1289726,0.01521971,0.01705548,-0.04155313,-0.04802277,-0.02539719,-0.23933992,0.06702075,0.05778281,-0.00293959,-0.05468375,-0.06805696,-0.05228224,-0.04265961,0.07343746,-0.08547564,0.00369146,0.01206042,0.02224582,0.01771324,-0.00556507,-0.00664102,-0.06124758,-0.01167623,0.00974072,0.08539356,0.04392729,-0.01817228,-0.03422705,0.00990281,0.00825647,0.02938369,0.03300495,0.03031683,0.07396892,0.01348457,0.04091642,0.05258426,0.08285959,-0.16827616,0.06123881,0.00472025,0.02845421,-0.01314498,-0.01501773,0.00690051,0.06231266,0.0023706,0.03600509,-0.03838843,0.06338195,0.05586112,-0.05722946,-0.09185521,-0.04837672,-0.04394035,0.03427624,-0.07912921,0.04065045,-0.03038106,0.03535599,-0.02066284,0.03444668,0.03028298,0.0391487,0.03230219,-0.00683813,0.04193623,0.01855263,0.01753695,0.0499957,-0.01433756,0.01327936,0.04807561,-0.04768581,0.00039907,0.10046436,-0.01623653,0.0059517,0.0403029,-0.05233679,0.00820861,-0.04993936,-0.03564127,0.00695048,-0.05441305,0.04952263,0.06611586,0.02559273,0.03626395,-0.07649911,-0.07625092,0.01251779,-0.03059272,-0.00565075,-0.01272992,0.01521122,0.00246628,0.01090732,-0.02177829,-0.00219803,0.01620144,0.00004208,0.02928931,0.07008383,0.04095133,-0.00169834,0.01048938,0.04238084,-0.13048057,-0.03637071,-0.00209381,-0.02094784,-0.020925,-0.01362497,-0.02516637,-0.02344129,-0.02010157,-0.03797013,0.05257224,-0.06269852,0.01555361,0.07030118,-0.00578622,0.04604287,0.00489141,-0.01424773,0.01286681,-7.7e-7,-0.03299278,-0.03487626,0.040441,0.04090655,0.10241415,0.07229556,-0.02842446,0.01666041,-0.08233096,-0.04130711,-0.01382945,0.07718172,-0.01392582,-0.1003906,-0.01842148,0.06569199,-0.03839935,-0.08326481,-0.01257537,0.00001323,-0.03698427,-0.00666412,0.10996456,-0.02554961,-0.07216629,-0.03650016,0.00309769,-0.01408724,-0.00715023,-0.05749213,-0.06120027,-0.01310889,-0.03134206,-0.11669736,0.02079749,-0.03775935,0.0008774,0.05534919,0.00882047,0.00142904,-0.01573002,0.00178438,-0.01879595,-0.03064177,-0.03688621,-0.05074006,0.05931941,-0.05959494,0.03884006,0.01059366,0.0089421,0.03616302,-0.00213677,-0.01167132,-0.03508145,-0.06095669,0.11805284,0.03146174,-0.07411214,0.00917466,0.07131277,0.11406429,-0.11150043,0.06751893,0.01199827,0.00258545,-0.02003809,0.00549646,0.06180467,-0.00689678,-0.036987,-0.18960096,-0.0331498,-0.04662681,0.03116013,-0.02452074,-0.02633293,0.00148404,-0.02818425,0.01222312,0.04629171,0.10037365,-0.06756938,0.01628253,0.06158935,-0.01291238,-0.02106667,-0.1112514,-0.0224773,-0.03656628,0.07655664,-0.01061457,0.00266452,-0.00868007,-0.08567999,0.03276161,-0.00649276,0.13475597,0.01110745,0.03460252,-0.0184516,0.09330392,0.05496005,-0.01820157,-0.01340749,0.02602033,0.0200875,-0.01422635,0.01762887,-0.02561015,-0.00794836,-0.03036145,0.03295311,0.00816847,-0.10541764,0.01042778,-0.02295833,-0.00707464,0.04053172,-0.00716661,0.06159981,0.01007064,0.0167988,0.05778718,0.02550275,0.05998057,-0.05137278,-0.05330754,0.02270999,-0.01538098,0.006595,0.0350873,-0.039325,0.05324043,-0.01402116,0.0472764,0.0326761,-0.03180837,-0.0206426,0.03252392,-0.01949943,-0.01550322,0.03762081,-0.02743057,0.08623021,0.01468028,0.02570419,0.04110418,-0.04576595,0.01308791,-0.03580073,0.00656078,-0.03798546,0.0109697,0.07511083,0.01672898,0.01264118,0.07254098,0.06615393,0.05858902,-0.02178062,-0.0416396,0.01059175,0.01572724,0.03531978,0.02290192,0.03743561,-0.24698168,0.00862632,-0.01050008,0.08847509,-0.03407777,-0.02096535,0.07878514,0.02176622,0.02487438,-0.00459869,0.00423649,-0.00382788,0.01229133,-0.10956518,0.0477023,-0.03295783,0.02510552,-0.00107509,0.05014073,-0.04379702,0.08541381,0.04558434,0.24310192,-0.02376403,0.00051832,0.0043594,0.01817194,0.02556088,0.040748,0.00680005,0.00849609,-0.0010455,0.03938323,0.01376483,-0.04814922,0.01649387,0.00686366,0.02254809,-0.0100582,0.02215617,-0.05650792,0.00831006,-0.05401399,-0.00711784,0.10246766,0.00451165,0.00005066,-0.06590202,0.00911408,0.02647309,-0.08302625,-0.04626403,-0.04433716,-0.04658356,0.01901992,0.01847707,0.06277186,0.02637603,0.01423734,0.0424546,0.02855057,-0.00960433,0.08290336,0.01084134,-0.04770883],"last_embed":{"hash":"j4ehs1","tokens":382}}},"text":null,"length":0,"last_read":{"hash":"j4ehs1","at":1753423667812},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8a.- Generate Lotto Combinations#{1}","lines":[216,229],"size":1309,"outlinks":[{"title":"The greatest feature of the best lottery software: generate winning lotto combinations.","target":"https://saliu.com/ScreenImgs/lotto34.gif","line":9},{"title":"Lottery software uses the same results file, with all past winning numbers.","target":"https://saliu.com/ScreenImgs/lotto35.gif","line":11},{"title":"Best lottery software generates combinations for the same lotto game format as W reports.","target":"https://saliu.com/ScreenImgs/lotto36.gif","line":13}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04299356,-0.03202362,-0.03097206,-0.07795741,-0.02417321,0.06098277,0.03441915,-0.04480915,-0.01532602,0.00604775,-0.04926388,-0.00863826,0.05930069,0.00279568,-0.01446463,0.00523684,0.01449155,0.01205598,-0.11144144,0.02553336,0.05983307,-0.06091203,-0.05898835,-0.09353044,0.04824147,0.00809162,-0.00531352,-0.05917701,-0.03873783,-0.24359612,0.03968025,0.03928844,0.00836031,-0.06206581,-0.07766081,-0.05316637,-0.05012459,0.08626199,-0.06951211,0.01909762,0.03979744,0.00871771,-0.01390805,-0.02936071,-0.00390425,-0.06776941,0.00803367,-0.06464919,0.02663929,0.04379995,-0.03441758,-0.05073836,0.00543849,0.08932494,-0.00406533,0.00745385,0.03223161,0.08740018,0.00907853,0.02475399,-0.0039474,0.07541391,-0.16760109,0.0506139,-0.03338531,0.01734339,-0.04048777,-0.06320034,0.0201846,0.03799605,0.02086697,0.05001028,-0.03601399,0.05140882,0.01918187,-0.02505205,-0.03753015,-0.04430551,-0.02342561,0.03351286,-0.04591575,0.01291855,-0.00460979,0.00142609,0.00008429,0.05426487,0.04277087,0.00976606,0.04703726,-0.02864767,0.01312632,-0.01656212,-0.01593705,0.07229443,0.03679189,-0.01946827,0.04109472,-0.02389185,0.01833341,0.1168329,0.04523023,0.04268522,0.01468547,-0.0556855,0.02125806,-0.0537443,-0.00695724,0.02609251,-0.04291433,0.05077578,0.06184573,0.00544316,0.0493263,-0.04187773,-0.0672652,0.01307999,0.02514328,-0.06421363,0.04860469,-0.01066206,-0.04588791,-0.02119862,0.00303953,-0.02555325,0.01922672,0.0175023,0.02749729,0.09263502,0.0166233,0.01307854,0.04018909,0.03494476,-0.10722547,-0.03488152,-0.00735134,-0.02767921,0.00245243,-0.00062185,0.00642616,-0.00820377,-0.05201855,-0.0268265,0.05060333,-0.07320154,-0.01114934,0.10354931,-0.004298,-0.00257618,-0.03393479,-0.0714493,0.00882699,0.01983487,-0.05944184,-0.02500545,0.01985181,-0.01084746,0.06087287,0.10509627,-0.04358982,0.03293984,-0.01592315,-0.036084,-0.05413011,0.09612276,-0.02959601,-0.07820595,-0.03478472,0.03923154,-0.02526447,-0.04875712,-0.0227589,0.03697769,-0.03130341,-0.0057231,0.08896368,-0.01352251,-0.09498926,-0.02233082,-0.02115736,-0.00150491,-0.03127776,-0.05746469,-0.04851201,0.01785331,-0.00250468,-0.10434199,0.07042773,-0.00469955,0.02249919,0.05409297,-0.0128429,0.00328276,-0.06236275,0.03226619,0.01465664,0.01139478,-0.00731254,-0.04983006,0.05021516,-0.06867886,0.01479026,-0.0439179,0.01431366,0.01365382,-0.00507124,0.04673176,-0.04278702,-0.069608,0.15708396,0.03786509,-0.03332233,0.02050018,0.05493084,0.07534299,-0.01296864,-0.00577995,0.0113631,0.01492309,0.01777419,0.02896807,0.01881755,0.01525108,-0.09171502,-0.20361921,-0.02971144,-0.02733414,0.03199631,0.02352566,-0.04939795,-0.01214203,-0.01316639,0.0406329,0.09467015,0.10814684,-0.04270389,-0.02791652,0.05889207,-0.00535161,0.00299152,-0.09445229,-0.01398346,-0.08212669,0.06908906,-0.01690747,0.04949342,0.01627667,-0.04364963,0.02869857,0.00430757,0.10457233,-0.00309268,0.0106686,0.01477865,0.07165113,0.07101434,0.01763964,-0.00441373,-0.00681478,0.05611885,-0.0452903,0.02016572,-0.03329088,0.02360634,-0.00447548,-0.00495984,-0.01001431,-0.11472999,-0.01511385,0.02356391,-0.02251521,0.07303687,-0.03970975,0.03447111,0.03432247,-0.0156653,0.02741214,0.04842394,0.06807954,-0.03939949,-0.08322233,-0.02167451,-0.00267322,0.04607012,-0.02568286,-0.05268391,0.06124586,-0.07403067,0.04761037,0.03705941,-0.01587477,-0.02412006,0.03692806,0.00639416,-0.00199864,0.08215285,-0.03297144,0.03375446,-0.05403173,0.01895986,0.0484151,-0.02646619,0.02373348,-0.04509407,0.02870687,-0.06160532,0.05508394,0.04945422,0.02598854,0.0502472,0.06100862,0.01527891,0.06245755,0.00553621,0.00192455,0.04578358,-0.02846557,0.0394473,0.01998257,0.00554951,-0.24726363,0.01814599,-0.0232892,0.07964461,-0.03535729,-0.04186432,0.02440706,-0.01381196,0.03300644,-0.03192149,0.03039265,-0.00215169,0.03462717,-0.07694842,-0.01062723,-0.02400737,0.00635769,-0.03139092,0.08203052,0.00090073,0.07934295,0.02243228,0.24093032,0.04957302,-0.0094715,0.02561101,-0.0228752,-0.04031692,-0.03321062,0.02136789,0.03351322,-0.03826043,0.07583566,0.03288548,0.0178137,0.02283066,-0.03002176,0.01247588,-0.02013773,-0.00073646,-0.05077276,-0.00007201,-0.05463503,-0.00965899,0.10533853,0.00269306,-0.04525925,0.00385444,0.02424097,0.06962111,-0.06519652,-0.03665152,-0.02213852,-0.06558458,0.02097265,0.03105032,0.02519918,0.00367994,-0.01978844,0.01898031,0.04005725,0.01128677,0.05197008,-0.00732833,-0.01067825],"last_embed":{"hash":"1ktctqn","tokens":318}}},"text":null,"length":0,"last_read":{"hash":"1ktctqn","at":1753423667928},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters","lines":[230,241],"size":1140,"outlinks":[{"title":"Generating lotto combinations can also employ special inner filters.","target":"https://saliu.com/ScreenImgs/lotto37.gif","line":7},{"title":"Software to generate lottery combinations can manually input filters or use ST filters files.","target":"https://saliu.com/ScreenImgs/lotto38.gif","line":9},{"title":"The lotto combinations can be displayed to screen or generated to a disk file.","target":"https://saliu.com/ScreenImgs/lotto39.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04538668,-0.03307945,-0.03245894,-0.07461128,-0.02324888,0.06279331,0.03439699,-0.04630689,-0.01459331,0.00870683,-0.05306219,-0.00670939,0.05526206,0.00278705,-0.0176894,0.00995487,0.01282986,0.01088636,-0.11016107,0.02644426,0.05855022,-0.06358534,-0.058398,-0.09128785,0.05044446,0.00635952,-0.00584251,-0.06118499,-0.03975559,-0.24278508,0.03818462,0.04082593,0.00914179,-0.06266788,-0.07591306,-0.054215,-0.04996932,0.08418631,-0.0697609,0.02176693,0.03897091,0.01311807,-0.01085009,-0.02888356,-0.00273358,-0.06683659,0.00933204,-0.06415557,0.02780797,0.04439702,-0.03139874,-0.04969542,0.00492911,0.09246559,-0.00325707,0.00631639,0.03063866,0.08766877,0.00836977,0.02462911,-0.00336026,0.07689528,-0.16821951,0.04853476,-0.03439644,0.01709354,-0.0434785,-0.06038456,0.0170307,0.0389223,0.02243381,0.04909546,-0.03188208,0.05328571,0.01839407,-0.02665032,-0.03814599,-0.04284538,-0.02469793,0.0362157,-0.04736592,0.01344157,-0.00589385,-0.00001746,-0.00024248,0.05052697,0.04271878,0.00783922,0.04762799,-0.02637346,0.01776422,-0.01577589,-0.02005903,0.07152009,0.03696598,-0.02264899,0.04083927,-0.02442249,0.02145617,0.11478434,0.04637462,0.04333186,0.01296834,-0.05544417,0.01929342,-0.0528423,-0.00996137,0.02658536,-0.04426434,0.04939853,0.06251606,0.00207728,0.05265239,-0.04283942,-0.0643983,0.01433594,0.02317562,-0.06587454,0.05035012,-0.01260821,-0.04679645,-0.02180502,0.00142922,-0.02798056,0.01688219,0.01840384,0.02612582,0.09151177,0.01773869,0.01221484,0.03925177,0.03452605,-0.10535304,-0.03639532,-0.00804855,-0.03033468,0.00198296,-0.00155961,0.0078234,-0.00996316,-0.04728729,-0.02731782,0.04928834,-0.07465484,-0.0135546,0.10256109,-0.00398608,-0.00479443,-0.03561498,-0.07310276,0.00923291,0.01806453,-0.05955187,-0.02834783,0.01599387,-0.0087685,0.05882916,0.10522378,-0.04416073,0.03233565,-0.01705645,-0.03506911,-0.05703171,0.09683906,-0.03156721,-0.07401598,-0.03529554,0.03952349,-0.02409063,-0.04811259,-0.02335634,0.04055321,-0.02956353,-0.00211293,0.08880114,-0.01461991,-0.0940861,-0.0231888,-0.02131976,-0.00218972,-0.02971072,-0.05609864,-0.05019399,0.01551809,-0.00349034,-0.10062934,0.06940855,-0.0039212,0.02249222,0.05402754,-0.01498228,0.00799844,-0.06554406,0.03356758,0.01508565,0.01321487,-0.00638612,-0.04813408,0.05453521,-0.06740101,0.01657826,-0.04534477,0.01367328,0.01454455,-0.00184252,0.04612116,-0.0402059,-0.07273645,0.15607817,0.03580168,-0.02990911,0.01727014,0.0529207,0.07327585,-0.01345785,-0.0102886,0.01141503,0.02054543,0.01483216,0.02888315,0.01847158,0.01472505,-0.09200819,-0.20354217,-0.03133635,-0.0253241,0.03271602,0.02460428,-0.04833802,-0.01211739,-0.01620707,0.04264193,0.09553617,0.1080225,-0.0424675,-0.03150111,0.05880126,-0.0071392,0.00326672,-0.09640876,-0.0148662,-0.08212393,0.07042247,-0.01639622,0.05237748,0.01391221,-0.03970047,0.03081826,0.0027758,0.10368845,-0.00506341,0.01013129,0.01395284,0.0700854,0.0716987,0.02064497,-0.00267516,-0.00413256,0.05683975,-0.04453006,0.02299598,-0.03186329,0.02469415,-0.00235722,-0.00275109,-0.01082907,-0.11660646,-0.01522421,0.01942359,-0.01877431,0.07417511,-0.0380344,0.0361335,0.0333615,-0.01738786,0.02821241,0.04934078,0.06912015,-0.0404093,-0.08249256,-0.02058545,-0.00414619,0.04702036,-0.02374863,-0.05072647,0.05831628,-0.07486619,0.04939562,0.03563222,-0.01439475,-0.02409752,0.03840478,0.00626442,-0.00358032,0.08029418,-0.0324114,0.03292494,-0.05296605,0.01472976,0.04776076,-0.02686416,0.02320397,-0.04128766,0.03090344,-0.06192321,0.05567224,0.05094909,0.02618332,0.04909127,0.06166238,0.01714995,0.05998243,0.00359349,0.00306115,0.04742106,-0.02898225,0.04101223,0.02262241,0.00305225,-0.24941917,0.01990288,-0.02796015,0.0755738,-0.03781757,-0.04268786,0.02587375,-0.01276652,0.02988574,-0.03014689,0.03164974,-0.00247708,0.03443179,-0.07484815,-0.01126891,-0.02527458,0.00491063,-0.02924027,0.08213858,0.00032953,0.08192805,0.02316546,0.24087702,0.05152307,-0.00793024,0.02190983,-0.02354675,-0.03805584,-0.03623429,0.01686742,0.03502012,-0.03972206,0.07661075,0.03325921,0.01870549,0.02563276,-0.03125045,0.01254042,-0.02267972,-0.00254895,-0.04887479,0.00049735,-0.05534523,-0.01014455,0.10673377,0.00169975,-0.04702958,0.00063913,0.02577116,0.07016668,-0.06296036,-0.03259365,-0.02003998,-0.06662711,0.01766072,0.02986042,0.02706692,0.00312502,-0.01912569,0.02031817,0.04075238,0.01370257,0.05177759,-0.0055265,-0.01001559],"last_embed":{"hash":"13x176s","tokens":316}}},"text":null,"length":0,"last_read":{"hash":"13x176s","at":1753423668021},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8b.- The Inner Filters#{1}","lines":[232,241],"size":1112,"outlinks":[{"title":"Generating lotto combinations can also employ special inner filters.","target":"https://saliu.com/ScreenImgs/lotto37.gif","line":5},{"title":"Software to generate lottery combinations can manually input filters or use ST filters files.","target":"https://saliu.com/ScreenImgs/lotto38.gif","line":7},{"title":"The lotto combinations can be displayed to screen or generated to a disk file.","target":"https://saliu.com/ScreenImgs/lotto39.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07615072,-0.03542333,-0.01897893,-0.04891625,-0.04397504,0.04769365,-0.0233528,-0.04293214,0.04374138,0.00372264,-0.00105358,0.0063947,0.042028,0.00098286,0.00551525,-0.02148825,0.01468283,-0.02348328,-0.10426814,-0.02219805,0.07334337,-0.05827892,-0.03878621,-0.13229577,0.03148101,-0.00488989,-0.04294088,-0.05909827,-0.04964708,-0.23347357,0.04147369,0.03416392,0.02294285,-0.09150636,-0.08572189,-0.03258318,-0.02869705,0.09738354,-0.09323163,0.03699395,-0.01459637,0.01923141,-0.01029953,-0.03594892,-0.00420702,-0.01788704,0.00104094,-0.00095212,0.06028735,0.02077152,-0.03423155,-0.01541938,-0.00247603,0.04355262,0.01386645,-0.00345157,0.03946082,0.10014441,-0.03365756,0.04414349,0.05550605,0.09679212,-0.14254944,0.0329413,0.01076763,-0.00518611,-0.02231182,-0.0097783,0.00273027,0.05339995,-0.01857625,0.03962833,-0.03494952,0.08457612,0.03837467,-0.02933043,-0.06035395,-0.038953,-0.04994653,0.02577718,-0.07540777,-0.0170824,-0.03580301,0.00965822,0.00113444,0.02244673,0.00651937,0.04890968,0.01966425,-0.04319189,0.05517891,0.01246564,0.02933412,0.02923156,0.02553558,0.00721548,0.03958526,-0.01040701,-0.0005905,0.12216719,0.01380472,-0.02759884,0.00596677,-0.02089675,0.02109159,-0.03172652,-0.03132592,0.00625631,-0.02565906,0.02609546,0.07991815,-0.00921206,0.05266064,-0.0651214,-0.06762547,0.02456471,-0.03242717,-0.00761837,-0.00704303,0.03705835,-0.02591327,0.01207915,0.00146083,-0.01768034,0.01471342,-0.01597168,0.02237942,0.07733344,0.05045488,0.00710289,0.03461387,0.0243657,-0.12127864,-0.04919775,-0.00085119,-0.06390091,0.00855008,0.01814358,0.00070406,-0.01264004,-0.00622124,-0.01191258,0.06896581,-0.08721225,0.03080817,0.11129309,-0.0034236,0.01825498,0.02884827,-0.01697546,0.00084545,-0.01494987,-0.06657482,-0.05456907,0.03982246,-0.00338157,0.0835681,0.08141801,-0.05937736,0.02483944,-0.01024886,-0.01989249,0.01368743,0.10295389,-0.03131664,-0.07413857,-0.05904343,0.03145684,-0.04736716,-0.07296941,-0.03661798,0.00276957,-0.0103712,0.0450339,0.09832985,-0.02791354,-0.08162503,-0.04867022,0.00306506,-0.00282596,-0.01879972,-0.04115923,-0.03479747,-0.00381518,-0.03541756,-0.09539946,0.04227487,-0.03295618,0.01348459,0.07486941,-0.00950596,0.00847719,-0.01539939,-0.02232029,0.00211198,-0.02005282,-0.03597254,-0.03690085,0.05191233,-0.04555955,0.00795231,0.00422372,0.01498405,0.03560659,0.02745924,0.02625263,-0.02262947,-0.06139651,0.12326655,0.01721986,-0.0608086,0.02868976,0.05731102,0.10363137,-0.07152961,0.05811616,0.00115927,0.02392001,0.03805439,-0.01495684,0.04153205,-0.01715337,-0.04080798,-0.19507435,-0.0252641,-0.04792993,0.0194748,0.02055955,-0.06726727,-0.00285522,-0.03072139,0.01791451,0.08564948,0.08805475,-0.07417054,-0.01550215,0.06459834,0.0033117,-0.03948357,-0.09582802,-0.01638974,-0.0486941,0.07645878,0.00870712,-0.00658075,0.01087496,-0.05998825,0.05660942,-0.02404101,0.13061814,0.02507352,0.01454102,0.02099059,0.07655046,0.04161431,-0.00151865,-0.02648426,0.00236008,0.04871624,-0.0293282,-0.01017955,-0.02461389,0.01417918,-0.0337114,0.01193125,-0.0045428,-0.11373632,0.02101885,-0.01818799,-0.03636754,0.05611811,-0.00109966,0.04146992,0.0276849,0.01979135,0.04705753,0.00809264,0.08001049,-0.03787851,-0.0542483,0.00713331,0.01144781,0.03725826,0.01283339,-0.04049659,0.03135157,-0.03711344,0.03343693,0.051945,-0.01737707,-0.02168245,0.01008954,-0.03622123,0.00173387,0.05453283,-0.01494191,0.04456573,-0.03303307,0.03660978,0.02637363,-0.01644376,-0.005172,-0.05554096,0.01785329,-0.080562,-0.0053081,0.09296992,0.01265954,0.0089872,0.07607783,0.06160946,0.02140662,-0.00287809,-0.02809327,0.02185385,-0.01356008,0.04443894,0.00614592,0.03308231,-0.23620915,0.00373784,-0.03858902,0.09414946,0.01021898,-0.02036799,0.03698169,0.02016679,0.03695926,-0.01306454,0.05031425,0.0203572,0.03144043,-0.09281091,0.01948972,-0.02354053,0.03021231,-0.03976281,0.06078503,-0.01488565,0.10844807,0.00616073,0.24916966,0.00459965,-0.01061346,0.01466518,0.01461615,0.04599966,-0.03040805,0.03252306,-0.0065143,-0.02260589,0.06047864,-0.04987155,-0.03801959,0.01055479,-0.0080194,0.00773986,-0.02413112,0.01290177,-0.0681901,-0.01532299,-0.09492175,-0.0060511,0.11834511,0.01879504,-0.04571247,-0.05288304,0.02849572,0.05918158,-0.06268717,-0.03284683,-0.05583304,-0.03409168,0.03778549,0.04892775,0.02341475,-0.00858296,0.00823085,0.01817111,0.08414828,0.0232404,0.08345494,0.00765723,-0.00414028],"last_embed":{"hash":"1ts5f7y","tokens":442}}},"text":null,"length":0,"last_read":{"hash":"1ts5f7y","at":1753423668105},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options","lines":[242,259],"size":2056,"outlinks":[{"title":"_**lotto jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":11},{"title":"The lotto software has multiple options: favorite numbers, shuffle all lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto40.gif","line":13},{"title":"The user of lotto software can see the combination generating process.","target":"https://saliu.com/ScreenImgs/lotto41.gif","line":15},{"title":"Lottery software informs the player when all combinations were generated by the lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto42.gif","line":17}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07457856,-0.03578468,-0.02085078,-0.04977094,-0.0439808,0.0498763,-0.02441026,-0.04552691,0.04541253,0.00436457,-0.003375,0.00423403,0.04068643,-0.00283447,0.00364967,-0.02074294,0.01650546,-0.02393224,-0.10738699,-0.02382672,0.07443067,-0.06003937,-0.0379457,-0.13433877,0.03013724,-0.00214904,-0.0418346,-0.06245049,-0.04835312,-0.23295079,0.03911177,0.03273951,0.02495821,-0.09361547,-0.08380256,-0.0310137,-0.02797887,0.09752402,-0.09455013,0.03749181,-0.01550213,0.01922551,-0.00717757,-0.0336551,-0.00103397,-0.01880776,0.00091537,-0.00279016,0.06051517,0.020846,-0.0326649,-0.01436364,-0.00179799,0.046274,0.01238867,-0.00321394,0.03616414,0.09906132,-0.03479121,0.04424416,0.05442022,0.09740844,-0.14506941,0.0330696,0.00753306,-0.00610905,-0.02225936,-0.00745821,0.00314863,0.05516822,-0.01900004,0.03897516,-0.03680657,0.08398543,0.03712035,-0.03011256,-0.06068731,-0.03886716,-0.05015732,0.02660386,-0.07604323,-0.01515533,-0.04002836,0.00933665,0.00345985,0.01780282,0.0044963,0.04801153,0.01884142,-0.03962089,0.05406777,0.01180263,0.02201626,0.02915872,0.02268112,0.00788331,0.03910648,-0.01258713,0.00061761,0.12229391,0.01504404,-0.02713848,0.00792618,-0.02306368,0.01957971,-0.03368933,-0.03046639,0.0080516,-0.02834384,0.02663561,0.07723523,-0.01051887,0.05145821,-0.06377116,-0.06902811,0.02637419,-0.03130141,-0.00791387,-0.00655609,0.03952785,-0.02450617,0.01169006,0.00223539,-0.0195589,0.01659057,-0.01462716,0.02239142,0.07697592,0.05247206,0.00577468,0.03487037,0.02330915,-0.11940956,-0.04674827,-0.00167547,-0.06614801,0.00763038,0.02000363,0.00016186,-0.0119483,-0.0049957,-0.01201975,0.06478436,-0.08707215,0.03108741,0.11033572,-0.00232009,0.01905664,0.02510899,-0.02018021,-0.00168572,-0.01408681,-0.06730922,-0.05187409,0.03925021,-0.0016392,0.08025268,0.07858122,-0.05744093,0.02430728,-0.00953823,-0.01881943,0.01322492,0.10369746,-0.03046378,-0.07399561,-0.05765885,0.03284268,-0.04650757,-0.07447685,-0.03581953,0.00214409,-0.00842765,0.04227735,0.09778109,-0.02916272,-0.08095311,-0.04616961,0.00642543,-0.00408859,-0.01828599,-0.04173848,-0.03332279,-0.00406684,-0.0366513,-0.09455415,0.04180281,-0.03149236,0.01409391,0.07768562,-0.00550683,0.00971895,-0.01544574,-0.02104224,0.00125752,-0.0202927,-0.03501343,-0.03674122,0.05379216,-0.04983633,0.01242299,0.00420299,0.01657276,0.03567099,0.02779144,0.02658106,-0.02230319,-0.06050854,0.123542,0.01534848,-0.06269762,0.02718549,0.05838165,0.10418967,-0.07492039,0.05866488,0.00339661,0.02622696,0.03538092,-0.01454687,0.04046318,-0.01667024,-0.03915641,-0.19651127,-0.02324909,-0.046294,0.02101146,0.0218037,-0.06799309,-0.00413006,-0.034292,0.01785454,0.08513926,0.0893401,-0.07084214,-0.01482665,0.06382923,0.00237675,-0.03692447,-0.09578151,-0.01889747,-0.04652202,0.07804058,0.010198,-0.00628729,0.00811376,-0.05935572,0.05425551,-0.02717191,0.12954165,0.02626405,0.01365949,0.02199323,0.07593994,0.04114124,-0.00170269,-0.02809822,0.00627071,0.04880894,-0.03083164,-0.00813973,-0.02313368,0.01403453,-0.03094793,0.01220168,-0.00629841,-0.11270972,0.02301022,-0.02103226,-0.03492026,0.05488698,0.00112516,0.04183158,0.02574551,0.02136903,0.04786326,0.00849064,0.07787862,-0.04109833,-0.05532093,0.00602018,0.01069682,0.03744215,0.01357452,-0.04291447,0.02969355,-0.03749786,0.03491903,0.0521647,-0.01794088,-0.02386316,0.01291573,-0.03561898,0.0027881,0.05537469,-0.01552898,0.04440305,-0.03329707,0.03519346,0.02583368,-0.01632059,-0.0045176,-0.05497373,0.01863842,-0.07962529,-0.00484277,0.09434783,0.01139862,0.00658371,0.07681248,0.06476723,0.02256651,-0.00246733,-0.03017494,0.02087541,-0.01297011,0.0438292,0.00552844,0.03277138,-0.2364819,0.00501251,-0.0397097,0.09144776,0.00658244,-0.0250705,0.03891784,0.02457738,0.03505507,-0.01399157,0.04873438,0.01913043,0.03021696,-0.08983117,0.01860002,-0.02098925,0.03240072,-0.03830656,0.0636863,-0.01559972,0.11354994,0.00607537,0.25006706,0.00098545,-0.00798453,0.01244362,0.01719275,0.04795671,-0.02831256,0.03181598,-0.00575177,-0.01940332,0.06016826,-0.05178398,-0.03654157,0.0137796,-0.00517733,0.00471285,-0.02580423,0.01093669,-0.06536851,-0.01595137,-0.09854187,-0.00484373,0.11691198,0.01960128,-0.0446228,-0.05310266,0.02821434,0.05784236,-0.06002099,-0.03056635,-0.05497139,-0.03361961,0.03722702,0.05019777,0.02599067,-0.00721998,0.0095305,0.01970446,0.08086196,0.02254468,0.0828867,0.01202764,-0.00334492],"last_embed":{"hash":"c8fjo9","tokens":444}}},"text":null,"length":0,"last_read":{"hash":"c8fjo9","at":1753423668244},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##8c.- Generate Lotto Combinations: Additional options#{1}","lines":[244,259],"size":1998,"outlinks":[{"title":"_**lotto jackpot**_","target":"https://saliu.com/all-lotto-numbers.html","line":9},{"title":"The lotto software has multiple options: favorite numbers, shuffle all lotto numbers.","target":"https://saliu.com/ScreenImgs/lotto40.gif","line":11},{"title":"The user of lotto software can see the combination generating process.","target":"https://saliu.com/ScreenImgs/lotto41.gif","line":13},{"title":"Lottery software informs the player when all combinations were generated by the lotto strategy.","target":"https://saliu.com/ScreenImgs/lotto42.gif","line":15}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04931925,-0.02573221,-0.01928652,-0.05213889,-0.02336613,0.02920534,-0.02627306,-0.03959039,0.04792817,-0.01216772,0.01259016,0.00732471,0.03173604,-0.01222687,-0.03753375,-0.00146553,0.02080881,-0.01577765,-0.08641503,-0.00930658,0.06267541,-0.06600289,-0.055676,-0.06912254,0.05339769,0.03205095,-0.04008047,0.00056403,-0.04761729,-0.24915661,0.04668759,0.03643527,0.03586621,-0.05765787,-0.05761542,-0.06293754,-0.04341718,0.070173,-0.04506535,-0.01186349,0.04309665,0.01350732,0.0140094,0.02562424,-0.00823185,-0.05599802,-0.01005485,0.01403999,0.06984622,-0.00072751,-0.04622649,-0.06905037,0.02840135,-0.00504044,0.06269556,-0.00582113,0.07328792,0.112276,0.06179781,0.00196174,0.05478085,0.0912768,-0.15769278,0.05620626,0.01216398,-0.01695694,-0.03359739,-0.0511636,0.02632146,0.01843864,-0.01875626,0.03833553,0.00227528,0.06920714,0.01729291,-0.05101905,-0.02312797,-0.07194132,-0.03244815,-0.00595922,-0.06502938,0.02125231,0.00358952,0.02059078,0.01746189,0.0211609,0.0344219,0.06812806,0.04231055,-0.06662124,0.03349261,0.05257405,0.02607842,0.02730028,-0.02383433,-0.03187538,0.02548916,-0.02459039,0.02267517,0.11231536,-0.00910481,-0.01397921,-0.00142738,0.00411413,0.02693962,-0.04423432,-0.03587407,-0.0149031,-0.08594309,0.01621144,0.04974547,0.03685553,0.07397788,-0.07912365,-0.04749255,-0.00538106,-0.03681748,-0.01170495,-0.00803203,0.00971752,-0.03405704,0.02564241,-0.00024691,0.00231844,0.00545648,0.00230841,0.06554007,0.06017008,0.04204625,-0.00309356,0.03676448,0.02803459,-0.1132214,-0.02906191,-0.00173901,0.01692618,0.00910267,-0.01539234,-0.02801223,0.01165479,0.00495707,-0.01187065,0.07007523,-0.07783522,0.03475973,0.07124963,-0.01957451,0.04215975,-0.00790298,-0.01240303,0.00440175,0.00604939,-0.01783623,-0.04135288,0.04945078,0.00778779,0.09985074,0.09189257,-0.05141031,-0.00617219,-0.04812823,-0.02100598,0.00881521,0.07455192,-0.04905689,-0.0873187,0.00797335,0.05512559,-0.04777855,-0.10760312,-0.03113751,0.00523198,-0.02706895,-0.0106131,0.07343089,-0.03805276,-0.06733728,-0.06366299,0.00733242,-0.01031806,0.00710218,-0.03541887,-0.0664703,0.00511237,-0.00097193,-0.08499236,0.0281121,-0.00033776,0.02255288,0.06586307,-0.0251962,0.04697295,-0.02117022,0.01420037,-0.03422529,-0.03205656,-0.06291541,-0.03014641,0.05319098,-0.05564535,0.02651276,-0.00081176,0.01063393,0.00476538,0.01415401,0.03110252,-0.03103683,-0.04718906,0.08867701,0.00719621,-0.05758336,-0.01641305,0.07593939,0.08496822,-0.13368529,0.02429072,0.00840163,0.03063795,-0.02574393,0.00253512,0.02661192,-0.03439196,-0.02057359,-0.19216824,-0.05398422,0.00577033,0.02314854,0.03313129,-0.01698826,0.04094403,-0.02697134,0.04388773,0.08791681,0.11104245,-0.04696687,-0.01211319,0.02922397,-0.02457989,-0.02356,-0.09418349,-0.06609783,-0.05484219,0.06418264,-0.00356119,0.01153868,-0.01506873,-0.04919018,0.05758745,-0.0435853,0.1178788,0.00758341,0.00039386,-0.04400227,0.0520606,-0.00089347,-0.00732369,0.01615274,-0.01693076,0.00277397,0.00350574,0.03698872,0.00127844,-0.00944996,-0.06559835,0.02983543,-0.02947328,-0.10765366,0.01713229,0.01060602,-0.01924205,0.03605907,-0.03975088,0.05789847,0.0434939,0.02074915,0.00666521,0.04023663,0.07255431,-0.03724154,-0.05656624,0.0344267,-0.01215764,0.01619376,0.00803619,-0.06026429,0.04477039,0.00433627,0.06961671,0.04565424,-0.02393877,-0.05093394,0.02634903,-0.0189792,-0.00206257,0.05172867,-0.00866227,0.07496541,-0.01972519,0.02971374,0.09499043,-0.06302878,0.02886983,-0.01012823,0.02004701,-0.02343399,0.00300255,0.07918278,0.03833312,0.00211299,0.04830312,0.06042735,0.03377295,0.0103915,-0.02119179,0.02265936,0.00507486,0.0183904,-0.02808812,0.03351888,-0.25687385,-0.00009472,-0.0428905,0.07530624,-0.06188455,-0.01761764,0.08328851,-0.01014135,0.04742231,-0.01731062,0.00536209,0.04427269,0.06083766,-0.09347928,0.03187635,-0.04211113,0.04350213,0.03809396,0.04013114,-0.02328679,0.0865259,0.05252743,0.24591759,-0.01063759,-0.01612805,0.05781662,0.01613757,-0.00421273,0.0221464,0.03235519,-0.01926841,-0.00280304,0.07166041,-0.02903636,-0.03198762,0.01860272,-0.02485291,-0.0110856,0.00327639,0.02155033,-0.02910853,0.01051798,-0.05591074,-0.01535352,0.12128081,0.02060613,-0.02318867,-0.07606006,0.00930481,0.03706181,-0.08511624,-0.06429305,-0.06612904,-0.07497098,-0.00060029,0.01314277,0.02761381,0.02641409,0.02242552,0.0196546,0.01836852,-0.03598,0.07416536,0.00732055,-0.05119929],"last_embed":{"hash":"1ec4c2o","tokens":435}}},"text":null,"length":0,"last_read":{"hash":"1ec4c2o","at":1753423668375},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities","lines":[260,284],"size":2072,"outlinks":[{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":20},{"title":"The lotto manual consists of a screen-by-screen lottery tutorials.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":22},{"title":"The Lottery Software Utility is an important part of Bright lotto software.","target":"https://saliu.com/ScreenImgs/lotto44.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04993043,-0.02294913,-0.01992244,-0.05383001,-0.02448847,0.03024179,-0.0302243,-0.03832848,0.04760564,-0.01394354,0.01262236,0.00833363,0.03012254,-0.01691901,-0.03901863,0.00129971,0.02224722,-0.0157587,-0.08542119,-0.01370742,0.06480902,-0.06725901,-0.05820927,-0.06576058,0.05379543,0.03017006,-0.03955277,0.000583,-0.04639898,-0.25026083,0.04421943,0.03619394,0.04078871,-0.05775462,-0.05454973,-0.06398432,-0.04396243,0.06468044,-0.04453722,-0.01024718,0.04615207,0.01712371,0.01328501,0.02628754,-0.00950152,-0.05552164,-0.00722086,0.01508531,0.06865793,-0.00101753,-0.04681737,-0.06926864,0.03114933,-0.00142761,0.06226005,-0.00331623,0.07388173,0.10828456,0.06301286,0.00286856,0.05621554,0.09383834,-0.15757024,0.05694805,0.01320873,-0.01715483,-0.03497683,-0.054261,0.02513226,0.01900725,-0.01498664,0.0384866,0.00244698,0.06710827,0.01574262,-0.05026799,-0.02364313,-0.07007128,-0.03209851,-0.00476668,-0.06454875,0.02168368,0.00294845,0.0163167,0.01981947,0.01751711,0.03305375,0.06724773,0.04108892,-0.0694143,0.03288968,0.05049776,0.02871051,0.02486126,-0.02309115,-0.03356267,0.02443804,-0.02348686,0.02067471,0.11267695,-0.00965379,-0.01446677,-0.00285198,0.00194664,0.02401684,-0.04210156,-0.03290944,-0.01718287,-0.0849425,0.01597533,0.05151794,0.036297,0.07292303,-0.07691582,-0.0462083,-0.0054657,-0.03761112,-0.01364282,-0.00741942,0.01049888,-0.03418249,0.02410711,0.00244619,0.00009964,0.00402407,0.00473387,0.06550614,0.06089286,0.04111611,-0.00463926,0.03514498,0.02885838,-0.1154494,-0.02728417,-0.00132682,0.01901996,0.00979953,-0.01501172,-0.02624137,0.01220412,0.00363497,-0.01231329,0.06877862,-0.08035603,0.03500985,0.07245126,-0.01776135,0.04080598,-0.00694593,-0.01126945,0.00196848,0.00439668,-0.01812234,-0.04297807,0.04887018,0.00929717,0.09881207,0.09275646,-0.05212323,-0.00779709,-0.04808534,-0.01613053,0.00994471,0.07432994,-0.04904203,-0.08674373,0.00746871,0.05071837,-0.04940338,-0.10892484,-0.03251125,0.00958231,-0.02742358,-0.01312633,0.07230487,-0.04356784,-0.06523199,-0.06246323,0.00555883,-0.01109712,0.01013738,-0.0344603,-0.06868169,0.00560841,0.0015984,-0.08384706,0.03251322,0.00173399,0.02062127,0.06168035,-0.02620407,0.04757798,-0.02010287,0.01552745,-0.03319755,-0.0326854,-0.06179925,-0.02848104,0.05661754,-0.06065772,0.02548283,0.00185873,0.00774485,0.00656869,0.01435201,0.03199793,-0.03183749,-0.04615162,0.08739677,0.0084625,-0.0572633,-0.0182566,0.08046684,0.08481605,-0.13475351,0.02153041,0.01017431,0.03359487,-0.02466778,0.00155056,0.028877,-0.03567158,-0.02366314,-0.1907275,-0.05404438,0.00612869,0.0238236,0.03107464,-0.01982022,0.04157566,-0.02393078,0.04572935,0.08754128,0.10979373,-0.04534822,-0.01505805,0.02817001,-0.02602131,-0.02168142,-0.09679636,-0.06641266,-0.05098959,0.06353968,-0.00031549,0.01245636,-0.01365047,-0.05005111,0.05643132,-0.04700875,0.11716464,0.00815593,-0.00249072,-0.04500258,0.05226098,-0.00406277,-0.00726158,0.01546745,-0.01509803,0.0016259,0.00036674,0.03890738,0.0024913,-0.00825738,-0.06569578,0.03084064,-0.03154031,-0.10777303,0.01962951,0.01268008,-0.01934373,0.03843145,-0.03882433,0.05873138,0.04701518,0.02269348,0.00539516,0.03897678,0.07360648,-0.03958008,-0.05474113,0.0344309,-0.01602851,0.01376656,0.00856171,-0.06083616,0.04337432,0.00073005,0.06879689,0.04567106,-0.02159305,-0.05369118,0.02738533,-0.02012018,0.00062478,0.05178789,-0.00881276,0.07491767,-0.01709824,0.03052481,0.0982376,-0.06287359,0.02878319,-0.01026683,0.01932748,-0.01821667,0.0016594,0.08101178,0.04067603,0.00219669,0.05051609,0.06063515,0.03066384,0.00834303,-0.02029023,0.02230582,0.00435781,0.01442082,-0.02887539,0.0329144,-0.25503388,-0.00100398,-0.04226772,0.07362289,-0.06672994,-0.01865479,0.08163242,-0.0079576,0.04778383,-0.01626144,0.00615185,0.04773607,0.06341937,-0.09193004,0.03068476,-0.04567301,0.04229834,0.04107165,0.04077922,-0.02381873,0.08842687,0.05351457,0.24396957,-0.01189267,-0.0179526,0.05668861,0.01536728,-0.00475921,0.02143469,0.02759491,-0.01702456,-0.00175806,0.07122054,-0.02917833,-0.02896977,0.01920856,-0.02467945,-0.01115197,0.00424762,0.02434661,-0.02759851,0.00929897,-0.05614253,-0.01759686,0.12100768,0.0226004,-0.02219683,-0.07603946,0.00609383,0.03710205,-0.0839875,-0.06049861,-0.06907935,-0.07504044,0.00183337,0.015587,0.02851115,0.02944344,0.0256765,0.0175713,0.01424938,-0.03278982,0.0714346,0.00701112,-0.05088259],"last_embed":{"hash":"utqq6n","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"utqq6n","at":1753423668517},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##9a.- Great Lotto Utilities#{1}","lines":[262,284],"size":2040,"outlinks":[{"title":"_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_","target":"https://saliu.com/lottery-utility.html","line":18},{"title":"The lotto manual consists of a screen-by-screen lottery tutorials.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":20},{"title":"The Lottery Software Utility is an important part of Bright lotto software.","target":"https://saliu.com/ScreenImgs/lotto44.gif","line":22}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10601297,-0.06280155,0.01511746,-0.03315942,-0.04195004,0.01561845,-0.01154964,0.02666194,0.0396803,-0.02565392,-0.00921375,0.01188313,0.02878222,-0.03015414,0.01434653,-0.01878412,0.02343194,0.00832755,-0.0740234,-0.02606694,0.05786221,-0.03499335,-0.03570577,-0.09643417,0.01803132,0.0525789,-0.0653754,-0.06813805,-0.03394667,-0.23738787,0.02291074,0.06235467,0.00202839,-0.05067051,-0.07187702,-0.06613459,-0.02410348,0.0361147,-0.05157025,0.03613326,0.01498232,0.02062963,-0.02992393,-0.01743511,0.01126937,-0.07892122,-0.0097139,-0.01110529,0.04613414,0.01960425,-0.04587262,0.00035486,0.0178229,0.01228849,0.03379252,-0.01322969,0.0264457,0.08866063,0.00491317,0.04458988,0.07663076,0.05574564,-0.15843752,0.08331012,-0.00316524,0.02051393,-0.023681,-0.0486667,0.04014659,0.0506384,0.00839911,0.016674,0.00793849,0.07032233,0.04991941,-0.05813614,-0.03963378,-0.06964982,-0.05208338,-0.01527151,-0.02508407,-0.03346758,0.03764827,-0.00862189,0.01064157,0.05376321,0.04408901,0.05446504,0.06351794,-0.05852162,0.05970527,0.01884367,0.03962022,0.06121597,0.0115436,0.01585205,0.08462396,-0.00891483,-0.00681362,0.11188309,-0.00842893,-0.03663152,0.00167587,0.02088895,0.05140989,-0.03021027,-0.0132999,0.00780163,-0.05740468,0.02726788,0.05421897,0.0391599,0.06776088,-0.04684353,-0.03514458,-0.01319174,-0.01241887,-0.01735002,0.02901835,0.03009352,-0.06226817,0.01975879,0.00549396,-0.02187376,-0.00210568,-0.03305197,0.01186198,0.0325562,0.0160534,0.03396674,0.08132597,0.0070124,-0.11742152,0.00510811,0.0042927,-0.0058614,-0.01684226,0.00100218,-0.0053965,0.03610621,-0.00723618,0.01810468,0.04203026,-0.08843654,-0.02856351,0.08247264,-0.00687014,0.02699425,0.02952304,-0.02958148,0.01391727,0.01926701,-0.05344813,-0.0491576,0.04672268,-0.01588903,0.16776457,0.07408109,-0.05166787,0.00369885,-0.03564932,-0.01158831,-0.01757194,0.07683857,-0.02342737,-0.12432224,-0.01298341,0.03754509,-0.04778302,-0.10132845,-0.01107083,-0.01708223,-0.04703102,-0.00152828,0.0769048,-0.04699766,-0.061802,-0.03173811,-0.01464308,0.02834574,-0.01578967,-0.05854966,-0.06094882,0.00650871,-0.05410286,-0.1037989,0.02083526,-0.0500411,0.01387324,0.02989363,-0.00801046,0.00154537,-0.03127133,-0.01181486,-0.02674127,-0.02153022,-0.07245282,-0.03453261,0.02579737,-0.05080059,-0.01741143,-0.02521591,0.00953114,0.00363676,-0.00864872,0.03829477,-0.0286347,-0.04602346,0.07620941,0.02111572,-0.04678997,0.00721837,0.06456212,0.06830277,-0.07672201,0.050657,0.00973778,0.04859687,-0.01648116,0.0131352,0.0239795,0.02364996,-0.05996732,-0.20450103,-0.02611976,-0.06447416,0.00002677,-0.00961991,-0.0491005,0.02988927,-0.01195022,0.01318784,0.07826881,0.13187377,-0.10517187,-0.0024174,0.0289542,0.00425805,-0.03914547,-0.09606295,-0.04433761,-0.04850438,0.04065444,-0.00270208,0.0294647,0.01696933,-0.06783003,-0.00469432,-0.02005065,0.11748623,0.03675278,0.03923154,-0.02820698,0.07975758,0.01720221,0.00299221,-0.04357199,-0.00049104,0.05768911,-0.02979804,0.03379263,-0.01335264,-0.01402703,-0.05480708,0.04331959,0.01619077,-0.08428764,-0.02144713,0.01723986,0.03895669,0.02082223,-0.00994479,0.07925124,0.02068396,0.01205827,0.03097577,0.02918597,0.05819595,-0.01333215,-0.07091585,0.01645104,-0.0118509,0.05889337,0.005898,-0.04506429,0.03790722,-0.03714444,0.08784256,0.01288762,-0.0035837,-0.06199497,0.03708163,-0.03703846,-0.01658866,0.08907797,-0.04370868,0.04929124,-0.01169762,0.01717133,0.08045883,-0.01082634,0.00201932,-0.01602576,-0.03898186,-0.07823422,0.01769265,0.05366872,0.01730145,0.02303155,0.04891184,0.01786188,-0.00554909,0.03744768,-0.00980381,0.03293039,-0.02451207,0.06638882,0.02475557,0.07308202,-0.23243304,0.02184672,-0.02788157,0.03776965,-0.02198729,-0.01099105,0.07280039,-0.06619602,0.05497621,-0.03414251,0.03245545,0.02365665,0.02745039,-0.06221198,0.03817736,-0.00651212,0.04031188,0.02179278,0.04677907,0.00531102,0.06872319,0.03384296,0.24644701,-0.0121564,-0.00638736,0.05460597,0.02674492,-0.01893692,0.03872908,0.02668126,0.0219365,0.00771556,0.05947616,-0.02624536,-0.01750711,0.00856771,-0.02933778,0.0374148,0.01072033,0.01025586,0.01015493,-0.02558989,-0.05719093,0.0176655,0.11035477,0.01486008,-0.03474434,-0.07172367,0.04147113,0.05547598,-0.06154052,-0.09850863,-0.10407329,-0.01313078,0.03154629,0.00022382,0.00882719,0.0019296,-0.00834556,0.02519269,0.03636047,-0.03736571,0.06398465,0.01684665,0.00738093],"last_embed":{"hash":"44ab8x","tokens":404}}},"text":null,"length":0,"last_read":{"hash":"44ab8x","at":1753423668655},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages","lines":[285,309],"size":2252,"outlinks":[{"title":"Lotto software has very useful additional programs: sums, check winners, frequency, skip systems.","target":"https://saliu.com/ScreenImgs/lotto45.gif","line":9},{"title":"Lottery software additional programs: calculate odds, lotto wheels, lexicographic order generation.","target":"https://saliu.com/ScreenImgs/lotto46.gif","line":11},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-b62.gif","line":13},{"title":"_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bright-software-code.html","line":17},{"title":"_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_","target":"https://saliu.com/ultimate-software-code.html","line":18},{"title":"Read a concise book, tutorial of best lotto software, lottery software programs visual tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":20},{"title":"Forums","target":"https://forums.saliu.com/","line":22},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":22},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":22},{"title":"Contents","target":"https://saliu.com/content/index.html","line":22},{"title":"Help","target":"https://saliu.com/Help.htm","line":22},{"title":"Home","target":"https://saliu.com/index.htm","line":22},{"title":"Software","target":"https://saliu.com/infodown.html","line":22},{"title":"Search","target":"https://saliu.com/Search.htm","line":22},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":22},{"title":"The lottery manual consists of screen-by-screen lotto software tutorial in Windows Command Prompt.","target":"https://saliu.com/forum/HLINE.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10376535,-0.06387042,0.016371,-0.03215448,-0.04367479,0.01675218,-0.01505665,0.02772185,0.03679802,-0.02745097,-0.00941407,0.0155104,0.02608864,-0.03453313,0.01425314,-0.01741632,0.02412849,0.00641144,-0.07231127,-0.02693798,0.05835105,-0.03186671,-0.03437198,-0.09631716,0.01162424,0.0570837,-0.06399386,-0.06867731,-0.03482323,-0.24047706,0.02394146,0.0639457,0.00012927,-0.05020682,-0.0698313,-0.06785297,-0.02307405,0.0364994,-0.05301869,0.03323962,0.01583631,0.02016891,-0.03112464,-0.01882267,0.01307656,-0.08219431,-0.00858058,-0.01180873,0.0469909,0.02083155,-0.04282643,0.00080157,0.02102727,0.01230337,0.03330153,-0.01391946,0.0243368,0.09027115,0.00547305,0.0439227,0.08072212,0.05505084,-0.15590562,0.08526189,-0.00536155,0.02163073,-0.02183524,-0.04761056,0.04299895,0.05533537,0.01348414,0.01067088,0.00734765,0.06896184,0.04733635,-0.060859,-0.04018585,-0.07247863,-0.05312256,-0.01596943,-0.02164361,-0.03519125,0.0397576,-0.01210752,0.0073352,0.04888705,0.04414538,0.05319587,0.05911921,-0.05819707,0.05802084,0.01473116,0.04495145,0.06152273,0.00706046,0.01471471,0.08383681,-0.00962276,-0.01131294,0.11142711,-0.0096948,-0.03453788,0.00793167,0.01975161,0.04934489,-0.02789182,-0.0098508,0.01034906,-0.05771339,0.02475563,0.04902462,0.03895094,0.06738075,-0.04375651,-0.03341452,-0.01014114,-0.0119267,-0.01408331,0.02925079,0.03285081,-0.05971155,0.02241408,0.00280402,-0.02305551,-0.00754127,-0.03519456,0.01164371,0.0286777,0.01743021,0.03327397,0.07943451,0.00716291,-0.11028668,0.00845368,0.00889157,-0.00430776,-0.01882444,0.00201208,-0.00395909,0.0388815,-0.00706986,0.01781818,0.04306635,-0.0877108,-0.02621814,0.08404961,-0.00996683,0.02562657,0.02802255,-0.02764096,0.01271526,0.02159922,-0.05385892,-0.04941923,0.05018346,-0.01908131,0.17323351,0.07480288,-0.05175753,0.00207831,-0.03494434,-0.00578786,-0.01562634,0.07999049,-0.02101118,-0.12323388,-0.01074441,0.03411344,-0.04960815,-0.09809326,-0.01180369,-0.01509843,-0.0492994,0.00060885,0.0798188,-0.04964442,-0.06342567,-0.02809068,-0.01328254,0.02967016,-0.01548311,-0.06089469,-0.0587598,0.00440958,-0.05550926,-0.10084651,0.01934696,-0.04990254,0.01213281,0.02724835,-0.00916142,0.00719586,-0.02739429,-0.01219633,-0.02579031,-0.02053421,-0.07076961,-0.03046779,0.02199507,-0.05820689,-0.01919544,-0.02476861,0.00635392,0.00180106,-0.00315764,0.03747495,-0.02643996,-0.0488306,0.07214517,0.02162362,-0.04790638,0.00695608,0.06455596,0.06704946,-0.07846241,0.05228085,0.0080474,0.04878556,-0.02149511,0.01126647,0.03127591,0.02213862,-0.06382909,-0.20485838,-0.02359361,-0.06558403,-0.00279449,-0.00892312,-0.04837458,0.02962529,-0.01239468,0.01247614,0.0796509,0.13004313,-0.10677889,-0.00532144,0.02694582,0.00558897,-0.03481694,-0.09614576,-0.04411494,-0.04681113,0.03986012,-0.00450919,0.03114574,0.01547334,-0.062846,-0.00591484,-0.02117883,0.119637,0.03760146,0.03961874,-0.03290632,0.08115772,0.01648331,0.00798201,-0.04453515,-0.00141754,0.0572255,-0.02822656,0.03865818,-0.00956531,-0.01717174,-0.05293846,0.04421436,0.01431696,-0.08464578,-0.01415632,0.01790048,0.03336206,0.01821101,-0.00736692,0.082046,0.02114206,0.01107263,0.02816825,0.03249992,0.06038296,-0.01463493,-0.07388074,0.01521165,-0.00985307,0.05569039,0.00491631,-0.04309267,0.03496969,-0.04134806,0.08669899,0.01265879,-0.00544569,-0.0622875,0.03504702,-0.03981962,-0.02200415,0.08619334,-0.04848526,0.04846141,-0.0135812,0.02132481,0.07912028,-0.01172584,0.00088225,-0.01677361,-0.03956354,-0.08022895,0.02219991,0.05474434,0.01433583,0.02469214,0.04514304,0.01752296,-0.00611944,0.04043057,-0.01043,0.0340157,-0.02837769,0.06713188,0.02781625,0.06988859,-0.23311135,0.02347678,-0.02182627,0.03268883,-0.02440145,-0.00967733,0.07308348,-0.06589857,0.05630642,-0.03602508,0.02701539,0.02604775,0.02545515,-0.05561427,0.04124332,-0.00139324,0.04602763,0.0254812,0.04650118,0.00524225,0.06551685,0.03204086,0.24602169,-0.01573469,-0.00152043,0.05437392,0.02740485,-0.01594911,0.03948216,0.03056787,0.02551749,0.00759781,0.05612893,-0.02848352,-0.01470619,0.00725546,-0.02848649,0.03711791,0.01303992,0.01036767,0.01104277,-0.02650717,-0.06255971,0.01648567,0.10476132,0.01374945,-0.03281013,-0.06936317,0.04710382,0.05326161,-0.06165062,-0.09625611,-0.10628677,-0.01187444,0.03354678,-0.00318445,0.00907873,-0.00310801,-0.01080655,0.02629269,0.03143888,-0.03745997,0.06167287,0.01776039,0.00998335],"last_embed":{"hash":"17d9toy","tokens":382}}},"text":null,"length":0,"last_read":{"hash":"17d9toy","at":1753423668768},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{1}","lines":[287,300],"size":1222,"outlinks":[{"title":"Lotto software has very useful additional programs: sums, check winners, frequency, skip systems.","target":"https://saliu.com/ScreenImgs/lotto45.gif","line":7},{"title":"Lottery software additional programs: calculate odds, lotto wheels, lexicographic order generation.","target":"https://saliu.com/ScreenImgs/lotto46.gif","line":9},{"title":"![Lottery Utilities Software, Tools, Lotto Wheels, Systems.","target":"https://saliu.com/ScreenImgs/lotto-b62.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10835075,-0.0305153,0.00693134,-0.0342584,-0.00762689,0.0578957,-0.05019405,0.00353413,0.00421694,-0.01045522,-0.03673694,-0.01492263,0.01296096,0.00363882,0.01885609,-0.02912583,0.01397129,-0.00546248,-0.03368583,-0.00305582,0.08504468,-0.00838523,-0.03733158,-0.09054354,0.05408787,0.01280157,-0.05445901,-0.05411061,-0.03161375,-0.2024032,0.01015922,0.04515017,0.00815119,-0.03680235,-0.07382192,-0.04701325,-0.03459735,0.05315195,-0.03478818,0.03803403,0.00438797,0.02021599,-0.01412329,-0.00663273,0.01385107,-0.08189746,0.0218637,-0.03394609,0.08095265,-0.00843627,-0.05599026,-0.00943196,0.00215343,0.00860517,0.02802806,0.00327407,0.07750048,0.08454751,0.00752218,0.02755765,0.04200351,0.03109821,-0.20928216,0.08733337,0.0251272,0.0015064,-0.0287785,-0.02324016,0.0546155,0.00679251,-0.01389895,0.02471788,-0.01721322,0.07039642,0.05288097,-0.09016342,-0.04193386,-0.07169779,-0.03652393,-0.03431625,-0.04964941,0.01967782,0.03376323,-0.00442896,0.02507693,0.04566021,0.00936297,0.06624341,0.04505853,-0.05355795,0.05138135,-0.04039959,-0.00459837,0.06837123,-0.02153749,-0.00392208,0.05050636,-0.04335148,-0.01696751,0.12304328,0.0203155,-0.0421345,-0.00329196,0.03469929,0.06591382,-0.01879007,-0.00775047,-0.00377679,-0.06094531,0.04345797,0.03700851,0.02837879,0.03755455,-0.05674549,-0.05788115,-0.03500973,-0.03018834,-0.04516675,0.03723041,0.00679869,-0.08428738,-0.00492027,0.00919016,-0.00154751,0.02356508,-0.03482013,0.0072445,0.03586621,0.04016405,0.03402226,0.0919058,0.03100993,-0.11530574,-0.05291349,-0.00764432,0.00178841,-0.02952283,0.00006557,-0.00847019,-0.00503713,-0.03096541,-0.01784204,0.01799347,-0.07897302,-0.02215397,0.0684572,-0.00015227,0.02627248,0.01132372,0.00340197,0.01566146,0.0070408,-0.02888007,-0.03765044,0.01150118,0.01590607,0.13747792,0.08788581,-0.04117019,0.01269817,-0.00499651,-0.05309271,-0.00861086,0.11509725,-0.02861528,-0.11708994,-0.05431931,0.03827949,-0.03305082,-0.07319778,-0.01341649,-0.00365384,-0.05894916,0.03814852,0.08186129,-0.02404595,-0.02751768,-0.02518138,-0.04518107,0.02361116,-0.02212516,-0.05946985,-0.05668845,0.02321964,-0.01374648,-0.07998599,0.04670979,-0.10020728,0.01967997,0.03302614,-0.02777242,0.01029315,-0.05031621,0.00010042,-0.03290994,0.02116082,-0.05310464,-0.05178777,0.03969628,-0.003836,0.0007364,0.01125269,-0.02848116,-0.02122853,-0.03455948,0.03839263,-0.05908286,-0.03358681,0.09871783,-0.00385487,-0.05404786,0.04157304,0.08577267,0.04287341,-0.053898,0.02996835,-0.00963584,0.03676046,-0.02928606,0.01395282,0.01040656,0.00821669,-0.0642131,-0.20566244,-0.02607969,-0.06177014,-0.02038469,-0.01548613,-0.05175729,0.06314486,-0.00732129,0.03863965,0.06412119,0.13797063,-0.08158835,0.00453045,0.01992489,0.02560405,-0.01541076,-0.08302205,-0.02960739,-0.04450234,0.03429003,-0.0076144,0.02373576,0.01379436,-0.03388588,-0.01246445,-0.02162044,0.11287331,0.0277152,0.02304692,-0.02556163,0.09296037,0.06978092,-0.01107513,-0.04038924,0.00653234,0.05944237,-0.05776297,0.03973823,-0.01684429,-0.01757407,-0.01582506,0.05773778,0.02274287,-0.0404346,-0.02920916,-0.00030269,0.06346931,0.02497256,-0.03206798,0.06460778,0.00630474,-0.00103058,0.00821841,0.02604099,0.03759879,0.02225723,-0.06638848,-0.01914757,-0.00672674,0.03026641,0.02320661,-0.01750697,0.05321931,-0.0545541,0.0402268,0.02212737,-0.00536507,-0.05658341,0.08139496,-0.03501016,-0.0243924,0.07468088,-0.00784589,0.05222854,-0.01334061,0.02830053,0.05174442,-0.01250171,-0.01753633,-0.02217737,-0.06378933,-0.06263746,0.02774729,0.03597011,0.06237136,0.02936299,0.04379861,-0.02116036,0.03766619,0.03213547,0.01242403,0.03953795,-0.03348576,0.07151175,0.03907128,0.06780773,-0.25490823,0.006258,-0.01266762,0.05195786,0.00371928,0.01648528,0.05055914,-0.08536179,0.03066832,-0.06849921,0.07693198,0.00113013,0.02332883,-0.0880983,-0.00178948,0.00480696,0.05239533,-0.02473974,0.06178222,-0.00546871,0.03136329,0.02287374,0.22878456,0.02095797,-0.02802891,0.04275684,0.0370471,-0.01596482,0.05867502,0.0415531,0.02241321,0.01877486,0.09308521,0.01177687,-0.00464315,0.05925199,-0.03264473,0.03862215,0.01175435,-0.01580271,-0.03692284,0.01779334,-0.03791999,0.01041148,0.08679522,0.01113888,-0.02438343,-0.05268487,0.0305159,0.02542772,-0.05786844,-0.08681549,-0.06332441,0.0094988,0.04505685,-0.02444448,0.03490043,-0.03492565,-0.01267687,0.01052615,0.06029885,0.00104199,0.10061363,-0.01795954,0.05690892],"last_embed":{"hash":"qj712w","tokens":312}}},"text":null,"length":0,"last_read":{"hash":"qj712w","at":1753423668883},"key":"notes/saliu/Visual Tutorial, Book, Manual Lottery Software, Lotto Apps.md#Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps##10.- Extra Software Packages#{4}","lines":[304,309],"size":693,"outlinks":[{"title":"Read a concise book, tutorial of best lotto software, lottery software programs visual tutorial.","target":"https://saliu.com/forum/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Help","target":"https://saliu.com/Help.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Software","target":"https://saliu.com/infodown.html","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"The lottery manual consists of screen-by-screen lotto software tutorial in Windows Command Prompt.","target":"https://saliu.com/forum/HLINE.gif","line":5}],"class_name":"SmartBlock"},
