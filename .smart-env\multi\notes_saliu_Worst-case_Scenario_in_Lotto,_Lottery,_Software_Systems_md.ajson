
"smart_sources:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md": {"path":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1efeapq","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753266116447,"size":16319,"at":1753423416500,"hash":"1efeapq"},"blocks":{"#---frontmatter---":[1,6],"#Worst-case Scenario in Lotto, Lottery, Software Systems":[8,178],"#Worst-case Scenario in Lotto, Lottery, Software Systems#{1}":[10,15],"#Worst-case Scenario in Lotto, Lottery, Software Systems#By <PERSON>, ★ _Founder of Lotto Mathematics_":[16,158],"#Worst-case Scenario in Lotto, Lottery, Software Systems#By <PERSON>, ★ _Founder of Lotto Mathematics_#{1}":[18,158],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)":[159,178],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{1}":[161,161],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{2}":[162,162],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{3}":[163,163],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{4}":[164,164],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{5}":[165,165],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{6}":[166,166],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{7}":[167,167],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{8}":[168,168],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{9}":[169,169],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{10}":[170,170],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{11}":[171,172],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{12}":[173,178]},"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lottery-software-systems.gif","line":14},{"title":"Wonder grid is lotto strategy system based on pairs and can win the lotto jackpot.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":18},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":24},{"title":"The Wonder Grid Revisited: New Pairing Research.","target":"https://saliu.com/bbs/messages/grid.html","line":78},{"title":"<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","target":"https://saliu.com/content/lottery.html","line":159},{"title":"_**Lotto, Lottery, Software, Strategy Systems**_","target":"https://saliu.com/LottoWin.htm","line":161},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":163},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":165},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":167},{"title":"_**<u>Skip Systems</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":168},{"title":"**<u>Lottery Utility Software</u>**","target":"https://saliu.com/lottery-utility.html","line":169},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":170},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":171},{"title":"Lotto wonder grid was analyzed under the worst-case scenarios.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":173},{"title":"Forums","target":"https://forums.saliu.com/","line":175},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":175},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":175},{"title":"Contents","target":"https://saliu.com/content/index.html","line":175},{"title":"Home","target":"https://saliu.com/index.htm","line":175},{"title":"Software","target":"https://saliu.com/infodown.html","line":175},{"title":"Search","target":"https://saliu.com/Search.htm","line":175},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":175},{"title":"Check lotto wonder grids with Super Utilities for lottery software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":177}],"metadata":{"created":"2025-07-23T18:21:56 (UTC +08:00)","tags":["strategy","strategies","wonder","grid","wonder-grid","lotto","pairs","pairings","software","lottery","lotto","systems","frequency","statistics","numbers","lotto jackpot","combinations","random","analysis"],"source":"https://saliu.com/bbs/messages/720.html","author":null}},
"smart_sources:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md": {"path":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10797074,-0.01401529,-0.05266254,0.00715799,-0.03725287,0.02415235,-0.00765769,-0.03054167,0.06315839,-0.0253448,0.00621859,-0.01558716,0.05991156,-0.01216488,-0.01224731,-0.03819225,0.00406544,0.01702345,-0.01568508,0.03770396,0.08529367,-0.0853437,-0.06728791,-0.08829815,0.04485227,-0.0151614,-0.02397095,-0.03301494,-0.04907567,-0.23549417,-0.00944805,0.00209238,-0.0377925,-0.06507475,-0.06705413,0.00115028,-0.0199261,0.04342157,-0.06833862,0.03140419,0.00101242,0.02807529,0.01263632,-0.0478435,-0.0006658,-0.06156937,0.00266185,0.00999941,0.02214764,-0.00744284,-0.0296856,-0.01713309,-0.03730305,0.02375941,0.06281059,0.05668023,0.06445077,0.03638092,-0.00556649,0.08487316,0.03052675,0.02851647,-0.17331953,0.02056589,0.0472799,0.02230967,-0.01500219,-0.01137781,-0.01381339,0.02107476,0.00809646,0.02155088,-0.01155007,0.08573374,0.00648364,0.00333962,-0.0452213,-0.0266887,-0.05171069,0.0177675,-0.0627491,0.00452238,0.01313442,0.01260803,0.03991239,0.05112811,0.01000988,0.02476045,0.08001796,-0.05966974,-0.01122571,-0.01983606,0.05728826,0.03639634,0.02470375,0.04387492,0.04936222,0.00200847,-0.00910578,0.0961905,-0.00511456,-0.01866336,0.03968779,0.02778083,0.07396264,-0.05531767,-0.05043131,-0.07479418,-0.00704044,-0.00619096,-0.00128743,-0.03651948,0.08276075,-0.0088323,-0.01192119,-0.00393838,0.04486024,0.00822258,-0.01810661,0.01363235,-0.07251794,0.07000948,0.01585708,-0.01794873,0.02382199,-0.02471712,0.01714625,0.06316829,-0.00636051,-0.00116356,0.02214004,-0.04421254,-0.11458455,-0.00324431,-0.02291423,-0.02811681,0.01879228,0.00759554,-0.00571156,0.01159587,0.00042285,-0.07812133,0.07736665,-0.06596097,-0.03050271,0.06425774,-0.02553805,-0.02863324,-0.03482689,-0.00144359,0.0133857,0.00012666,0.00231981,-0.05728969,-0.00607983,-0.04530094,0.0761268,0.07453582,-0.04264948,0.01890159,-0.00000419,-0.01491914,-0.03704343,0.13260992,0.04254202,-0.08970235,0.01200586,0.03479049,-0.00987691,-0.03103938,-0.00747428,0.02234758,-0.01878482,-0.02557593,0.06230969,-0.02835982,-0.04916009,-0.00386251,-0.05790333,-0.014389,-0.0315271,0.03672334,-0.01487206,-0.04126518,0.02090921,-0.07504949,0.00446784,-0.02051857,0.03642797,0.0395541,-0.05733113,-0.03848343,0.00046729,0.07289508,-0.03253265,-0.04583573,-0.05519165,-0.08324073,0.05059294,0.02905184,-0.02162385,-0.02235482,-0.01580078,-0.03579878,-0.01382803,0.0382652,-0.01347489,-0.07480655,0.05651269,0.04557659,-0.0421768,-0.03527402,0.06377207,0.05536224,-0.05700783,0.02803939,0.02264256,0.04127859,0.00025692,0.02623251,0.0290511,0.03629255,-0.05427409,-0.21504401,-0.02385951,-0.0587022,-0.03767852,0.0237936,-0.01162136,0.03095638,-0.03980691,0.00976041,0.11056593,0.08801983,-0.07242353,0.02601206,0.01853927,-0.01637209,-0.04263597,-0.09730098,-0.06427859,-0.04160572,0.05713068,-0.01398247,0.06802437,0.00956771,-0.08550259,0.0349611,-0.00944132,0.14979161,0.06506762,0.02490474,0.00225426,0.07738787,-0.00655913,-0.02939461,-0.02469699,0.02766806,0.01894177,0.02274024,-0.01745106,-0.04910664,-0.00265583,-0.11975504,0.01853689,0.01042451,-0.10125661,-0.03550455,0.03951748,-0.04334603,0.04245808,-0.05347398,0.08827506,0.06950571,0.0023613,0.01397381,0.02516774,0.08213651,0.00880892,-0.09460743,-0.01298251,0.00887921,0.00614265,0.00548234,-0.02355074,0.04349358,-0.04763725,-0.00017129,-0.00189376,0.02993109,-0.00147675,0.00105806,-0.02954822,-0.019868,0.09540091,0.01530568,0.06727985,0.02836214,0.06143738,0.0699001,-0.05820714,-0.03652768,0.01250944,0.00288101,-0.01841972,0.04930575,0.00551537,0.06456448,0.0335207,0.05415746,0.06123494,0.0060064,-0.03253888,-0.061222,-0.01247374,-0.04154156,0.03531747,0.05534685,0.0465766,-0.25052348,0.02010861,-0.00482591,0.02296527,-0.04281979,-0.00665437,0.03021219,-0.00544373,0.03743732,-0.0231989,0.05137461,0.01361202,0.03369825,-0.07288048,0.01314218,0.00746856,-0.01723883,-0.02671191,0.10409041,-0.00353332,0.02852443,0.05894421,0.24532337,-0.03744499,0.00377455,0.00894024,-0.00223033,0.02762797,-0.02808453,0.04880854,0.04610956,0.06314136,0.07448426,0.00279334,-0.00272085,0.03134904,-0.00245211,-0.01348055,0.00753981,0.03044503,-0.07495772,0.01051623,0.00623719,0.00339702,0.13083987,-0.00201262,-0.01461183,-0.10308582,0.03922003,0.07319348,-0.07261623,-0.0448562,-0.01451777,-0.01366233,0.01652274,0.02593173,0.07125726,-0.0240214,0.03117558,-0.0229909,0.04380234,0.01961447,0.03584929,-0.00214826,0.01058561],"last_embed":{"hash":"1efeapq","tokens":477}}},"last_read":{"hash":"1efeapq","at":1753423675854},"class_name":"SmartSource","last_import":{"mtime":1753266116447,"size":16319,"at":1753423416500,"hash":"1efeapq"},"blocks":{"#---frontmatter---":[1,6],"#Worst-case Scenario in Lotto, Lottery, Software Systems":[8,178],"#Worst-case Scenario in Lotto, Lottery, Software Systems#{1}":[10,15],"#Worst-case Scenario in Lotto, Lottery, Software Systems#By Ion Saliu, ★ _Founder of Lotto Mathematics_":[16,158],"#Worst-case Scenario in Lotto, Lottery, Software Systems#By Ion Saliu, ★ _Founder of Lotto Mathematics_#{1}":[18,158],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)":[159,178],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{1}":[161,161],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{2}":[162,162],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{3}":[163,163],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{4}":[164,164],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{5}":[165,165],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{6}":[166,166],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{7}":[167,167],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{8}":[168,168],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{9}":[169,169],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{10}":[170,170],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{11}":[171,172],"#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{12}":[173,178]},"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lottery-software-systems.gif","line":14},{"title":"Wonder grid is lotto strategy system based on pairs and can win the lotto jackpot.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":18},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":24},{"title":"The Wonder Grid Revisited: New Pairing Research.","target":"https://saliu.com/bbs/messages/grid.html","line":78},{"title":"<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","target":"https://saliu.com/content/lottery.html","line":159},{"title":"_**Lotto, Lottery, Software, Strategy Systems**_","target":"https://saliu.com/LottoWin.htm","line":161},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":163},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":165},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":167},{"title":"_**<u>Skip Systems</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":168},{"title":"**<u>Lottery Utility Software</u>**","target":"https://saliu.com/lottery-utility.html","line":169},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":170},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":171},{"title":"Lotto wonder grid was analyzed under the worst-case scenarios.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":173},{"title":"Forums","target":"https://forums.saliu.com/","line":175},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":175},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":175},{"title":"Contents","target":"https://saliu.com/content/index.html","line":175},{"title":"Home","target":"https://saliu.com/index.htm","line":175},{"title":"Software","target":"https://saliu.com/infodown.html","line":175},{"title":"Search","target":"https://saliu.com/Search.htm","line":175},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":175},{"title":"Check lotto wonder grids with Super Utilities for lottery software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":177}],"metadata":{"created":"2025-07-23T18:21:56 (UTC +08:00)","tags":["strategy","strategies","wonder","grid","wonder-grid","lotto","pairs","pairings","software","lottery","lotto","systems","frequency","statistics","numbers","lotto jackpot","combinations","random","analysis"],"source":"https://saliu.com/bbs/messages/720.html","author":null}},"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10155985,-0.03151871,-0.04017416,-0.00204809,-0.02401758,0.02261073,-0.008573,0.00666508,0.07234368,-0.01970741,0.01531613,-0.01396638,0.05831087,0.00541756,0.00356115,-0.01547027,-0.00215639,0.00521903,-0.00608959,0.0397147,0.10165516,-0.07376359,-0.07600971,-0.09746042,0.04071587,-0.01552344,-0.03026032,-0.06145329,-0.04213121,-0.19307804,0.0027384,0.01629401,-0.00991805,-0.05159891,-0.04783484,-0.00776865,-0.02086663,0.08683407,-0.04025178,0.0522095,-0.01410695,0.03179858,-0.01259702,-0.0330064,0.00085928,-0.05238333,0.00672357,0.00982453,-0.01143104,-0.00390164,-0.04765138,-0.01001604,-0.04821289,0.02624826,0.06642497,0.03917761,0.06303288,0.00517815,0.00727359,0.09420754,0.02372999,-0.01143601,-0.18865138,0.02594947,0.05785254,0.02610203,-0.01955201,0.00972944,-0.02379501,0.02618678,0.02134016,0.02971216,-0.01717645,0.09261907,0.00844355,0.02258293,-0.04679365,-0.03418983,-0.04491468,0.00837687,-0.04823497,-0.01198013,-0.01233154,0.00925251,0.01763616,0.0212174,0.02607218,0.04184105,0.06619097,-0.03096936,-0.00425988,0.00941359,0.07025462,0.02873166,-0.00722082,0.05970142,0.02343836,-0.00708489,-0.02998217,0.09894625,-0.02641604,-0.00579727,0.04217232,0.01769668,0.07410717,-0.02687757,-0.05665704,-0.10479496,-0.00748864,0.00197515,0.00440378,-0.01172615,0.0730106,-0.05385209,0.01297215,-0.0061664,0.07309905,0.03085357,-0.01532584,0.01549249,-0.081618,0.0425705,0.01229331,-0.00244198,0.04976488,-0.00006941,-0.00172973,0.08841769,-0.0124393,0.00167522,0.01676618,-0.02764709,-0.09465569,0.02998522,-0.01697575,-0.03565281,0.02707997,-0.02117053,0.01426367,-0.00229972,0.00410104,-0.05660309,0.06102616,-0.07743546,-0.03454445,0.07849952,0.00345321,-0.03421126,-0.02192012,0.00159394,0.00626619,-0.01818366,0.01075099,-0.05623969,-0.00351097,-0.0323102,0.08641633,0.06681072,-0.01614117,0.02402898,-0.00244106,-0.03727122,-0.03567881,0.16982442,0.02601114,-0.1141569,-0.00896112,0.04019743,0.00376938,-0.03721717,-0.01672343,0.00160713,-0.00526684,0.00945308,0.07850924,-0.00800067,-0.01170258,-0.00060814,-0.05249528,0.0017573,-0.03282345,0.03261043,-0.04790625,-0.03350161,-0.00451648,-0.0526854,-0.01356887,-0.0152914,0.04627547,0.05339305,-0.05278467,-0.06998586,-0.0069002,0.06437708,-0.02547584,-0.06169834,-0.03166908,-0.08418345,0.04176737,0.01375287,-0.03202314,-0.03885821,-0.03385976,-0.01175375,0.00023519,0.03839318,-0.0139619,-0.06119987,0.03163101,0.03998972,-0.01845813,-0.02315874,0.07703408,0.04508126,-0.05534106,0.01829658,0.01273425,0.02705136,0.01317408,0.014318,0.00492105,0.00793194,-0.06219794,-0.23884839,-0.01455613,-0.04652021,-0.03563591,0.00570943,-0.00052997,0.0196664,-0.02895226,0.01710944,0.11915911,0.06004062,-0.07055587,0.01394555,-0.01088132,-0.01417263,-0.05309007,-0.07070154,-0.07921467,-0.03644865,0.04609263,-0.01610751,0.05900982,-0.00437803,-0.10495909,0.02734519,0.00624222,0.14837269,0.07617011,0.01131301,-0.0025585,0.08437155,0.00882504,-0.0291618,-0.0151512,0.01129375,0.00864608,0.01179233,0.00174231,-0.05579916,-0.01984911,-0.12837321,0.03406284,0.02203726,-0.09176867,-0.03384923,0.03309039,-0.03100199,-0.00285213,-0.05341026,0.06729501,0.04107278,-0.01578974,0.02509786,0.02209227,0.08536898,0.02114631,-0.1034246,-0.0055212,-0.00046456,0.00129847,-0.00462071,-0.01368592,0.05997279,-0.05144494,-0.03870229,0.00841168,0.02471253,-0.00849117,-0.0414691,-0.03944878,-0.01603474,0.08576634,0.02762353,0.01985064,0.00502337,0.02608471,0.04560698,-0.04757969,-0.04558977,0.00318827,0.01250797,-0.02713645,0.05472578,0.001558,0.05338492,0.03514051,0.04914632,0.03829779,0.02505988,-0.02066185,-0.0501415,-0.01523415,-0.02817167,0.01692353,0.0833472,0.01551784,-0.2662411,0.04133909,0.01699529,0.04816034,-0.01643455,0.01813708,0.01622842,0.02109541,0.01096912,-0.00388245,0.05649944,0.03641196,0.01923258,-0.04188985,-0.0087504,0.00543244,0.00146451,-0.04510043,0.07872704,0.00811978,0.02327442,0.06562477,0.25475767,-0.0201619,0.00656694,0.01083719,0.01088981,0.0623906,-0.04249511,0.05473031,0.04347168,0.04075037,0.07015599,0.00386601,-0.00253398,0.02942118,0.00319829,0.00019544,0.00729913,0.04461198,-0.10592829,0.00914478,0.00629808,0.01826776,0.12800409,0.02694795,-0.030401,-0.10123179,0.01866198,0.04577766,-0.0914323,-0.04999845,-0.02155336,0.0006576,0.00277702,0.03082559,0.09237254,-0.03228529,0.03811941,-0.00982394,0.05017071,-0.00116732,0.03600687,-0.00386429,-0.0028952],"last_embed":{"hash":"7s78ap","tokens":116}}},"text":null,"length":0,"last_read":{"hash":"7s78ap","at":1753423675108},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#---frontmatter---","lines":[1,6],"size":282,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09306829,-0.00270092,-0.04949807,-0.00667682,-0.04876207,0.03817054,0.01718571,-0.03326787,0.05385863,0.00069076,0.01402057,0.012337,0.03839814,-0.00063165,-0.03442266,-0.038081,0.00098565,0.02133225,-0.02406452,0.03685459,0.07397186,-0.09515241,-0.0696177,-0.08820909,0.06176727,-0.00562971,-0.0239899,-0.02715761,-0.05829798,-0.25299871,-0.00401487,-0.00881813,-0.0322074,-0.05969852,-0.05774771,-0.00518876,-0.02976305,0.0504966,-0.06775357,0.02891484,0.00686175,0.02000755,0.00470227,-0.05288003,0.00652504,-0.06640506,-0.00608028,0.00431223,0.04398027,-0.00024637,-0.01063818,-0.00842868,-0.0140614,0.03383122,0.06333464,0.03739644,0.06685223,0.0448412,0.02285437,0.05688438,0.0402336,0.0295065,-0.16543686,0.02765716,0.03610756,0.03188263,-0.02959742,-0.00762566,-0.01233582,0.0225701,-0.00351701,0.01155728,-0.02578258,0.08056925,0.01392779,-0.02708245,-0.03357182,-0.02144546,-0.05819668,0.01484768,-0.06349,-0.00136171,0.01222839,0.02655033,0.03017782,0.04930989,0.00400406,0.02083815,0.08701137,-0.05642973,0.00319939,-0.0298783,0.04748403,0.04608754,0.02133967,0.04770762,0.06122734,0.00572675,-0.00565526,0.10189129,0.01648405,-0.01173625,0.02813731,0.03750863,0.06412417,-0.06365062,-0.05324182,-0.05841046,-0.00805814,0.0113973,-0.00286193,-0.03873828,0.07859804,0.00156229,-0.00995476,-0.01445608,0.04326049,0.01240118,-0.0241433,0.01972939,-0.06416506,0.07933889,-0.0133834,-0.02396864,0.01394641,-0.03865614,0.02608884,0.05699213,0.00691108,-0.0064274,0.01884637,-0.04435226,-0.13412732,-0.02021288,-0.00488167,-0.02563881,0.00848735,0.03681317,0.00612653,-0.00904327,0.00668086,-0.05552703,0.05807402,-0.07607699,-0.03157127,0.05382031,-0.02706938,-0.02350791,-0.02337858,-0.01344229,0.01918747,-0.01091708,-0.01999008,-0.05596563,-0.01550623,-0.02647598,0.0632696,0.07475287,-0.04559997,0.00319363,-0.00312584,-0.02553042,-0.01906691,0.11639682,0.02934551,-0.08559769,-0.00523037,0.03372151,-0.00094904,-0.03267742,-0.00989616,0.0193839,-0.03048547,-0.04254651,0.04905866,-0.0411578,-0.07083337,-0.0325295,-0.04236388,-0.02236966,-0.00311544,0.03711475,-0.00995596,-0.04443791,0.03254987,-0.08298858,0.0076185,-0.02066469,0.03826972,0.04399786,-0.02987474,-0.01509493,-0.00482607,0.06839684,-0.04745794,-0.04944481,-0.04650933,-0.06359711,0.04238277,0.01454997,-0.00998303,-0.01858934,0.00262493,-0.03467649,-0.00711303,0.04606125,-0.00528468,-0.07938085,0.09351408,0.04604844,-0.05262528,-0.06167664,0.05730866,0.06406903,-0.05028152,0.02909676,0.03762835,0.04453603,-0.00562724,0.02618206,0.05025781,0.03166072,-0.05549963,-0.22122543,-0.02909002,-0.0654503,-0.02798493,0.0397966,-0.01589267,0.0382638,-0.0418407,0.00658911,0.09480844,0.10772603,-0.05476709,0.01854987,0.02164015,-0.03464969,-0.03726034,-0.1046754,-0.05162154,-0.03694389,0.04987789,-0.00546446,0.05354094,-0.00259849,-0.07762568,0.03656865,-0.00732982,0.14814775,0.06128092,0.03285883,0.00321915,0.0687702,-0.01917861,-0.04355269,-0.02041918,0.01857636,0.01899993,0.01090342,-0.01984447,-0.05964707,0.01312026,-0.11183334,-0.00534625,0.00696715,-0.08094347,-0.04187002,0.03947194,-0.0344199,0.04430691,-0.04511566,0.10003085,0.0599657,-0.02073322,0.02799874,0.02357345,0.06440049,0.0012254,-0.08235566,0.00091431,0.01714232,0.0261896,0.02789338,-0.02827784,0.05383174,-0.03334869,0.01146861,-0.01834675,0.01894774,-0.00026788,0.03084695,-0.03385275,-0.01650118,0.09032203,0.01029354,0.05275209,0.03062494,0.08904114,0.08312971,-0.03709791,-0.01570824,0.0074653,-0.00119359,-0.03175057,0.04235305,0.01333592,0.05668453,0.05439683,0.03725078,0.07243212,-0.01871653,-0.02733805,-0.0626692,-0.00509649,-0.06529356,0.02410874,0.03386621,0.0426513,-0.24246694,0.03177699,-0.01372773,0.0253726,-0.02626809,-0.0061114,0.03206844,0.0158684,0.06709872,-0.01499456,0.06197705,0.02694681,0.04558677,-0.09946784,0.0011875,-0.00922989,-0.03147034,-0.00174924,0.10899247,-0.00613223,0.04135236,0.06785925,0.23479481,-0.03845122,0.00225794,-0.00479355,0.00451207,0.01062251,-0.03310953,0.0299063,0.02539994,0.0576226,0.06220113,0.02091528,-0.00687671,0.01896376,-0.02265176,-0.01098174,-0.000617,0.03662811,-0.08011476,0.01774709,-0.00890746,-0.00312956,0.11641149,0.00435384,-0.02044335,-0.10948586,0.04784144,0.09303888,-0.07224574,-0.05727081,-0.02323713,-0.0362875,0.01961653,0.0295057,0.0504379,-0.02051972,0.03385273,-0.02428378,0.04094395,0.01965082,0.06041253,-0.01716855,0.02510813],"last_embed":{"hash":"15359kz","tokens":503}}},"text":null,"length":0,"last_read":{"hash":"15359kz","at":1753423675143},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems","lines":[8,178],"size":16001,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lottery-software-systems.gif","line":7},{"title":"Wonder grid is lotto strategy system based on pairs and can win the lotto jackpot.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":11},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":17},{"title":"The Wonder Grid Revisited: New Pairing Research.","target":"https://saliu.com/bbs/messages/grid.html","line":71},{"title":"<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","target":"https://saliu.com/content/lottery.html","line":152},{"title":"_**Lotto, Lottery, Software, Strategy Systems**_","target":"https://saliu.com/LottoWin.htm","line":154},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":156},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":158},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":160},{"title":"_**<u>Skip Systems</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":161},{"title":"**<u>Lottery Utility Software</u>**","target":"https://saliu.com/lottery-utility.html","line":162},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":163},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":164},{"title":"Lotto wonder grid was analyzed under the worst-case scenarios.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":166},{"title":"Forums","target":"https://forums.saliu.com/","line":168},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":168},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":168},{"title":"Contents","target":"https://saliu.com/content/index.html","line":168},{"title":"Home","target":"https://saliu.com/index.htm","line":168},{"title":"Software","target":"https://saliu.com/infodown.html","line":168},{"title":"Search","target":"https://saliu.com/Search.htm","line":168},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":168},{"title":"Check lotto wonder grids with Super Utilities for lottery software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":170}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10089811,-0.00742459,-0.04101643,-0.01987779,-0.03296993,0.01534292,0.0142603,-0.02626239,0.04541318,-0.0295635,0.00451695,-0.01066162,0.05533751,-0.01168844,-0.02218014,-0.0284982,0.00146506,0.04055084,-0.02543019,0.04242988,0.09570932,-0.07543979,-0.06313108,-0.09232876,0.04442509,-0.02139351,-0.02912274,-0.04197293,-0.03622594,-0.20656365,-0.00668599,-0.00191837,-0.04052531,-0.06342679,-0.04472505,-0.01295568,-0.02024393,0.04486084,-0.09585069,0.01460388,-0.00336589,0.03663459,-0.00563129,-0.05554067,0.00904233,-0.08140074,0.02337081,0.00488889,0.01857062,-0.01317613,-0.023573,0.01216655,-0.03324125,0.02470011,0.05711384,0.03082924,0.06973197,0.03705489,0.01627822,0.05387453,0.03408051,0.02010615,-0.16120461,0.01918869,0.06735442,0.04286581,-0.00633488,0.0191929,-0.03082116,0.0445932,0.00456921,0.01883182,-0.02030811,0.10412204,0.00457994,-0.00581493,-0.05138904,-0.01930265,-0.04949706,0.02371267,-0.04589674,0.00217374,0.00482835,0.01461222,0.01881094,0.05317693,0.01094288,0.0082222,0.08890241,-0.05825375,-0.01305625,-0.0304107,0.0590897,0.05000259,0.01530243,0.04680291,0.05254228,-0.00664821,0.00865571,0.11546683,0.00195835,-0.03389686,0.03244457,0.01072946,0.07510992,-0.04590595,-0.0354558,-0.08410192,-0.01918589,0.00353574,-0.01101536,-0.02371537,0.07028755,0.00232968,-0.00138715,-0.01927334,0.04232288,0.00903206,-0.02327492,0.01838958,-0.07601958,0.07339076,0.00318444,-0.01292241,0.01930458,-0.03606283,0.01085691,0.05991285,-0.00920136,-0.01004932,0.01817344,-0.04716046,-0.10796776,0.00402345,0.00669099,-0.0233785,0.01446631,0.0055451,-0.00880116,-0.00044105,0.00226156,-0.07597359,0.06386635,-0.09017051,-0.04175948,0.05975257,-0.01483506,-0.04196306,-0.04929403,-0.00965212,0.02943473,-0.00669363,-0.0017685,-0.04929602,-0.0029551,-0.03367363,0.06769873,0.06171604,-0.03769886,0.00746892,0.00834551,-0.01035931,-0.02515679,0.16332006,0.03687696,-0.07850764,-0.00424705,0.03326983,-0.00846058,-0.01122666,-0.01394705,0.02519234,-0.05039832,-0.01643817,0.07648131,-0.01745036,-0.04880744,-0.01626457,-0.05629849,-0.01894232,-0.03184443,0.02427496,-0.00469575,-0.04318582,0.01767515,-0.07215887,-0.01615656,-0.03411659,0.05104333,0.04371844,-0.046347,-0.01180396,-0.00525011,0.04212007,-0.04210072,-0.04263125,-0.05204793,-0.08529939,0.05149287,0.00286798,-0.02403563,0.00156732,-0.01843725,-0.01926938,0.00314351,0.03451336,-0.03012701,-0.07357778,0.05900347,0.04800257,-0.05313275,-0.03633829,0.07303429,0.05297133,-0.05360047,0.0099258,0.02692694,0.04696447,-0.00506868,0.03092402,0.03774172,0.04621513,-0.06238969,-0.22962962,-0.00993267,-0.07378746,-0.05310541,0.02067872,0.00670937,0.01734029,-0.04116516,0.00565618,0.09868932,0.10308257,-0.08884833,0.02506729,0.0476784,-0.0186558,-0.02743376,-0.11293998,-0.06602071,-0.04516033,0.04941589,-0.01356233,0.07138535,0.00334924,-0.09475093,0.02036898,0.01266321,0.15024298,0.05928083,0.04973676,-0.01410008,0.07733814,0.00194778,-0.04509026,-0.00501059,0.01996637,0.0198132,0.03153105,-0.02947074,-0.03851055,0.01561913,-0.10063919,0.02489301,0.00563168,-0.06519343,-0.03248135,0.03056739,-0.04939695,0.0435247,-0.0324586,0.11797414,0.04224369,-0.00120177,0.03164365,0.0246977,0.06449573,0.01924111,-0.09798755,0.00537469,0.00638959,0.02858885,0.02080361,-0.04001768,0.04724333,-0.05021981,0.0066902,-0.01381108,0.01246485,-0.00791768,-0.00323523,-0.04857561,-0.02442079,0.07792249,0.01572726,0.04181293,0.02639118,0.07158324,0.07068228,-0.03912822,-0.04217272,0.00964738,-0.00148488,-0.00606996,0.04472598,0.018306,0.05770155,0.0481846,0.03349235,0.05861657,-0.00583285,-0.01966406,-0.05769238,-0.00187447,-0.05664525,0.04569282,0.04804353,0.04924853,-0.25503898,0.03250719,0.03181051,0.03458375,-0.02023396,0.00186017,0.02190268,-0.00950312,0.04773924,-0.01438757,0.05256028,0.01409221,0.04153809,-0.06010928,0.00575127,0.00288185,0.01940555,-0.0217071,0.10207523,0.00460975,0.01385868,0.06979053,0.24118398,-0.05367597,0.01758121,0.00020863,-0.00503018,0.02660879,-0.02455536,0.04132368,0.05409505,0.04615582,0.06146286,0.01195519,-0.00363423,0.02069172,-0.00185383,-0.00648296,0.00821428,0.03387462,-0.08511651,0.02126108,-0.01090424,-0.00495394,0.10461695,-0.00486946,-0.03956747,-0.11239742,0.04073564,0.08044744,-0.07005698,-0.04061557,-0.01902964,-0.03219818,0.03406684,0.01060167,0.07256453,-0.04389218,0.02042378,-0.01897849,0.04210815,0.03244258,0.02041042,-0.01816523,0.01569166],"last_embed":{"hash":"fjcyb9","tokens":143}}},"text":null,"length":0,"last_read":{"hash":"fjcyb9","at":1753423675319},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#{1}","lines":[10,15],"size":379,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/images/lottery-software-systems.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#By Ion Saliu, ★ _Founder of Lotto Mathematics_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07960743,-0.00287798,-0.03876511,-0.0014227,-0.0480678,0.05313451,0.02098331,-0.02610562,0.055328,0.01143772,0.02410518,0.01579903,0.02599319,0.00471606,-0.03857251,-0.03341426,-0.00646932,0.00391422,-0.02445496,0.0392115,0.06955701,-0.10511494,-0.06560704,-0.08963425,0.06868386,-0.00069204,-0.02242297,-0.01134479,-0.0711715,-0.26114464,-0.00391848,-0.00375945,-0.03384803,-0.06005574,-0.05749079,-0.01325616,-0.03308554,0.07023217,-0.04080539,0.04723585,0.02184979,0.01020939,0.00792436,-0.04391899,0.00879861,-0.06467114,-0.00984246,-0.00181994,0.04920914,0.00880566,-0.0079721,-0.01262712,-0.0066066,0.03105535,0.0613958,0.02579008,0.06943754,0.03983523,0.03664006,0.05763781,0.02964295,0.02580286,-0.17291532,0.01754146,0.0336061,0.0146173,-0.03669176,-0.02092419,-0.01072038,0.01072753,-0.00599188,0.01974902,-0.02743186,0.07900406,0.01615971,-0.03029226,-0.02604335,-0.02394309,-0.05836803,0.01465922,-0.07635002,-0.0082833,0.00796416,0.02878274,0.03227523,0.04095035,0.00142132,0.03607418,0.08028824,-0.05240836,0.02161208,-0.01437908,0.03273704,0.04827183,0.0065494,0.06273203,0.05460374,0.01350543,-0.01511863,0.10625854,0.02430441,-0.00387081,0.02491851,0.04526746,0.0562575,-0.05903097,-0.0669726,-0.05143896,-0.01039439,0.00490722,-0.00003727,-0.03855663,0.09064063,-0.00403921,-0.01000537,-0.00917019,0.05007444,0.01494694,-0.03725495,0.0216024,-0.05962451,0.08623321,-0.01324079,-0.02101921,0.01061351,-0.04326747,0.02428254,0.07039976,0.01322553,-0.01107998,0.00756268,-0.04588287,-0.1280216,-0.02150245,0.00030741,-0.02635007,0.01899553,0.04156786,0.00949701,-0.0182196,0.0054829,-0.0519791,0.04505722,-0.06444715,-0.03101271,0.05348369,-0.0352982,-0.02057475,-0.00869509,-0.01926677,0.01862689,-0.00834585,-0.022406,-0.05542077,-0.01839504,-0.03469225,0.05520485,0.07741096,-0.04012065,0.00259477,-0.0140209,-0.03689886,-0.00511311,0.11198493,0.01179777,-0.06276634,-0.00812068,0.01103489,0.00944721,-0.04114572,-0.01006083,0.01425543,-0.02091478,-0.03758284,0.03968145,-0.03852254,-0.06895114,-0.02744181,-0.04500519,-0.02501148,-0.00768597,0.0422693,-0.02081929,-0.04228915,0.02710984,-0.07955074,0.02065545,-0.00357152,0.0373833,0.04843195,-0.02666181,-0.02867065,-0.01963254,0.07363226,-0.03932935,-0.05456716,-0.04560813,-0.06796081,0.04715313,0.00709014,-0.00103711,-0.02821654,0.00024261,-0.03383692,-0.00669925,0.05286007,0.00265098,-0.07512505,0.09275541,0.05239956,-0.05369776,-0.06817899,0.04862074,0.05445258,-0.04788845,0.03787345,0.04152299,0.03964529,0.01007826,0.01826348,0.05167439,0.01443813,-0.04228804,-0.2118991,-0.0305971,-0.06028759,-0.03291668,0.0390531,-0.02337757,0.04052004,-0.03631815,0.01697676,0.10144759,0.10497219,-0.04121523,0.02146854,0.01145764,-0.03098441,-0.04479531,-0.09475718,-0.05900018,-0.03351288,0.05690316,-0.01830567,0.05182138,-0.0151405,-0.0638987,0.0446518,-0.01498795,0.14936997,0.05926558,0.02553745,-0.00484098,0.07101304,-0.01670643,-0.03763832,-0.01029554,0.01421911,0.00381168,-0.0030123,-0.00733071,-0.07124593,0.00566356,-0.10772049,-0.00313479,-0.00817131,-0.08556353,-0.03853578,0.04257508,-0.03093209,0.06154063,-0.05212702,0.09176349,0.07160058,-0.02823261,0.03411542,0.02510119,0.07510274,0.00000477,-0.07205454,0.00493071,0.02014015,0.02416285,0.02803085,-0.01725544,0.05305136,-0.03033251,0.00557453,-0.01002844,0.02208347,-0.00875689,0.0230317,-0.03898177,-0.00987439,0.0851344,0.0149501,0.04911707,0.02565192,0.07883963,0.0834784,-0.03802459,-0.01185259,0.01141184,0.02198414,-0.02706606,0.03212978,0.00878559,0.05413639,0.04664477,0.02927517,0.06972364,-0.01699428,-0.02631168,-0.05737366,-0.00514949,-0.06846038,0.01552368,0.02445924,0.0260836,-0.23750101,0.03256307,-0.03428689,0.03254262,-0.02366043,0.00223416,0.02797431,0.02759041,0.06654524,-0.01509549,0.06658366,0.03976415,0.05266402,-0.10645743,0.00541987,-0.03012388,-0.05769837,-0.00764656,0.10141676,-0.01426773,0.06515282,0.07090186,0.2344788,-0.0264833,-0.0086625,-0.00380859,0.01161626,-0.00240788,-0.04417813,0.02641095,0.01294137,0.05238814,0.06482618,0.02105983,0.0049787,0.01092874,-0.02781869,-0.00768949,-0.00787039,0.02551643,-0.07406124,-0.00251143,-0.01390183,0.00570744,0.12732124,0.01727463,-0.01435131,-0.11120798,0.04645984,0.09611233,-0.06514198,-0.05987621,-0.02504145,-0.03811311,-0.00633073,0.03402804,0.05106479,-0.01574775,0.04287721,-0.02817236,0.04447211,0.01339147,0.07790799,-0.0347906,0.01604215],"last_embed":{"hash":"xtcgip","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"xtcgip","at":1753423675362},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#By Ion Saliu, ★ _Founder of Lotto Mathematics_","lines":[16,158],"size":13304,"outlinks":[{"title":"Wonder grid is lotto strategy system based on pairs and can win the lotto jackpot.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":3},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":9},{"title":"The Wonder Grid Revisited: New Pairing Research.","target":"https://saliu.com/bbs/messages/grid.html","line":63}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#By Ion Saliu, ★ _Founder of Lotto Mathematics_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08071204,0.00038918,-0.03758994,0.00131336,-0.04800178,0.05470022,0.02346428,-0.02670378,0.05628465,0.01054822,0.02636328,0.01307906,0.02598002,0.00826143,-0.04163893,-0.03288668,-0.00825768,0.00451106,-0.0277353,0.03745206,0.07018732,-0.1080656,-0.06408003,-0.08834089,0.07016222,-0.00121966,-0.02196662,-0.01113412,-0.07047736,-0.26161563,-0.00240727,-0.00598461,-0.03650182,-0.05676401,-0.05729139,-0.01209345,-0.03532368,0.0713435,-0.04106334,0.04654532,0.02432897,0.01166298,0.00782821,-0.03974915,0.00659285,-0.06597807,-0.01200256,-0.00011271,0.05177715,0.010803,-0.00955034,-0.01167026,-0.00787843,0.02826117,0.06435275,0.0273476,0.0696208,0.04033725,0.03603505,0.06092275,0.02551361,0.02465814,-0.17601769,0.01829136,0.03233104,0.01156143,-0.03717374,-0.02157606,-0.01291734,0.00931382,-0.00766453,0.01913764,-0.02484669,0.07462413,0.01749334,-0.0315886,-0.03027152,-0.02448489,-0.05862399,0.01577966,-0.07363727,-0.00828359,0.0087991,0.02969258,0.03206341,0.04123557,0.00346743,0.03531148,0.07912526,-0.0532232,0.02170468,-0.01268349,0.02924188,0.04718509,0.00641254,0.0655248,0.05441591,0.01346821,-0.01702632,0.10546336,0.02366564,-0.00315934,0.02258097,0.04774039,0.05355341,-0.05979087,-0.06340171,-0.05001822,-0.00816898,0.00346145,-0.00091435,-0.03861828,0.08725827,-0.00657191,-0.0083485,-0.00954112,0.04715343,0.01379618,-0.03994555,0.02490408,-0.05820546,0.08587994,-0.01024255,-0.02147905,0.01150483,-0.03885011,0.02798381,0.06868467,0.01452343,-0.00982749,0.00865457,-0.04687983,-0.12907368,-0.02481701,-0.00087361,-0.02524981,0.01868213,0.04272215,0.00837053,-0.01737703,0.0062644,-0.04968844,0.04678615,-0.06427333,-0.03076451,0.05257834,-0.03851178,-0.01705193,-0.00745063,-0.01780052,0.0188109,-0.00491865,-0.02127611,-0.05851539,-0.0203712,-0.03407409,0.05429292,0.07784147,-0.0364753,0.00543782,-0.01675048,-0.03499685,-0.00452935,0.11290111,0.01215503,-0.06292582,-0.00893866,0.01039279,0.00726735,-0.04273032,-0.00925738,0.0104982,-0.01976613,-0.03699363,0.03930634,-0.040247,-0.07020567,-0.02697138,-0.04229483,-0.02259715,-0.00811279,0.03981652,-0.02027411,-0.04235407,0.02513125,-0.07640313,0.02184328,-0.00319593,0.03782293,0.04951926,-0.0288428,-0.02470301,-0.01663113,0.07477912,-0.03924509,-0.05339593,-0.04534212,-0.06876205,0.0466043,0.00617591,-0.00083939,-0.02775972,0.00110192,-0.03197421,-0.0114663,0.05417522,0.00326487,-0.07154632,0.09321034,0.05204491,-0.05229356,-0.06712501,0.04487177,0.05315807,-0.04938305,0.03793481,0.04085613,0.0384341,0.00814433,0.01968779,0.05010357,0.01034713,-0.04335993,-0.21327575,-0.03032604,-0.06160513,-0.0330064,0.03734189,-0.02732478,0.04264475,-0.03606899,0.01496226,0.1004944,0.10467257,-0.03782628,0.02021214,0.01202807,-0.03183651,-0.04370078,-0.09057821,-0.05553479,-0.03337685,0.0548938,-0.0171008,0.05261255,-0.01682373,-0.06301391,0.04368887,-0.01523494,0.15033236,0.06159076,0.02593744,-0.00650657,0.07183266,-0.01495476,-0.03678857,-0.01264086,0.01456434,0.00275231,-0.00350171,-0.00673974,-0.07409859,0.00526697,-0.10489603,-0.00335811,-0.0044819,-0.08663469,-0.03633561,0.04340544,-0.02877067,0.06174811,-0.05123644,0.08747551,0.07319891,-0.02842807,0.03459011,0.02391156,0.07272941,-0.00198836,-0.07155732,0.00473623,0.02003518,0.02185376,0.02669393,-0.01782091,0.05282783,-0.02717709,0.00872108,-0.00784149,0.02485994,-0.00573634,0.02618106,-0.03682496,-0.00965488,0.08306082,0.01430171,0.05079358,0.02644138,0.07892183,0.08106396,-0.03899531,-0.0088204,0.00963788,0.02401577,-0.02594339,0.03316797,0.00941919,0.05482433,0.04354294,0.0295343,0.06840074,-0.01557465,-0.02699267,-0.05780527,-0.00307729,-0.06673342,0.01572979,0.02677372,0.02645619,-0.24305086,0.02962057,-0.03900073,0.03424123,-0.02464962,0.00469838,0.02745339,0.03029998,0.06790703,-0.01717201,0.06585295,0.03902044,0.05337584,-0.10838019,0.00194477,-0.02813661,-0.06049648,-0.01037028,0.10341624,-0.01413541,0.06698613,0.06852195,0.23571007,-0.02523396,-0.00986126,-0.005413,0.00988819,-0.00485192,-0.04610363,0.02669568,0.00955054,0.05519127,0.06424493,0.02049187,0.00752281,0.01259954,-0.02810435,-0.00745928,-0.01018871,0.02334201,-0.07247711,-0.00372424,-0.01496826,0.00730735,0.12714148,0.01541673,-0.01278949,-0.1124661,0.04379117,0.09610232,-0.06239266,-0.05862473,-0.0257571,-0.03624751,-0.01138612,0.03356301,0.05061668,-0.01621011,0.04388031,-0.02837581,0.04347159,0.01411025,0.07947058,-0.03458838,0.0155617],"last_embed":{"hash":"1jwqvyz","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"1jwqvyz","at":1753423675501},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#By Ion Saliu, ★ _Founder of Lotto Mathematics_#{1}","lines":[18,158],"size":13253,"outlinks":[{"title":"Wonder grid is lotto strategy system based on pairs and can win the lotto jackpot.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/wonder-grid-check.gif","line":7},{"title":"The Wonder Grid Revisited: New Pairing Research.","target":"https://saliu.com/bbs/messages/grid.html","line":61}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07769292,-0.02668527,-0.02928002,-0.04269564,-0.04366818,0.00785522,-0.02867611,-0.01834195,0.04640756,-0.02207049,0.02887046,-0.01095879,0.04903683,-0.04189277,-0.00932186,-0.05545119,0.03512082,0.04867091,-0.01733759,0.0216589,0.07063936,-0.04902442,-0.08676922,-0.10771096,0.04822136,0.0106111,-0.03689468,-0.02235841,-0.03749849,-0.21786229,-0.00194023,0.00058649,-0.0303291,-0.06532849,-0.05197003,-0.00065406,-0.00697258,0.05940472,-0.07340246,0.03408147,-0.02458071,0.03654453,0.04366476,-0.03834854,0.04580466,-0.04351078,0.0227925,0.03083066,0.02785436,-0.01786548,-0.04531928,-0.01978905,-0.01014192,0.01108932,0.02377076,0.01161344,0.06682823,0.06812941,0.02314531,0.06755929,0.03527625,0.03522506,-0.17287667,0.04307534,0.05316565,0.05519104,-0.0169148,-0.00454266,-0.00063618,0.02884668,-0.02968155,0.00019807,-0.02303082,0.13161582,0.01421931,-0.01336181,-0.03496197,-0.02005159,-0.03862745,0.00220892,-0.05248313,0.02017307,-0.00044922,0.04112293,0.01616731,0.04623586,0.01089306,0.01021325,0.10424165,-0.07619526,0.01421349,-0.0063169,0.05752467,0.03491742,0.01801798,0.0220507,0.05970534,-0.00699084,-0.01828363,0.09522258,0.00869134,-0.03337226,0.05601199,-0.00934192,0.06559546,-0.04633067,-0.05112971,-0.07718964,-0.02124468,0.00611958,0.00241412,-0.04418957,0.04189366,-0.03714,-0.02156655,-0.00580632,0.02217044,-0.0063147,-0.03313005,0.01277674,-0.06172503,0.02968869,0.00183183,0.00609077,0.01749819,-0.04397598,-0.00007107,0.07106652,-0.00772235,0.0042863,0.05068841,0.00385353,-0.11574348,0.00330559,0.01338906,-0.04432461,0.00794611,0.00860398,0.00503895,0.00640477,0.00028708,-0.08191266,0.05218923,-0.08482408,-0.06298085,0.07338694,-0.00405807,-0.04451539,-0.03649831,-0.03099726,0.03160379,-0.03812593,-0.02037673,-0.04474919,0.00891186,-0.03585977,0.09499825,0.04514187,-0.06356168,0.01172626,-0.03342273,-0.04377309,-0.02217476,0.17170973,0.0251853,-0.07637861,-0.00936994,0.04427684,0.01580541,-0.0270067,-0.009259,-0.0070415,-0.05434621,0.0046601,0.09045049,-0.01663615,-0.03546124,-0.00552499,-0.04195387,-0.00816751,-0.00729362,0.00376117,-0.01703689,-0.02320582,0.00739025,-0.05653607,-0.01799418,-0.01070416,0.03462827,0.04856138,-0.02468783,-0.00467583,-0.0339677,0.01585226,-0.05490683,-0.03797736,-0.04354939,-0.06675278,0.04828743,0.01947637,0.02597997,0.00251667,-0.01381831,-0.00374617,0.03418182,0.04266937,-0.01555549,-0.08042759,0.05555606,0.04541167,-0.04940117,-0.03168906,0.07291427,0.0328237,-0.03161621,0.0226093,0.02050116,0.04397127,-0.0107492,0.04804119,0.01587307,0.04412865,-0.06086255,-0.21662487,-0.02961476,-0.04942114,-0.03093356,0.0048457,0.006836,0.01504954,-0.05584338,0.00174542,0.08302099,0.13280077,-0.05887431,0.02932032,0.04724527,-0.01128166,-0.04052943,-0.10369354,-0.06073722,-0.05733981,0.06826501,-0.02585214,0.01256758,0.0148145,-0.08426842,0.01168459,-0.00349046,0.13992576,0.00190247,0.03295422,-0.00017642,0.08443706,0.00013193,-0.05345033,-0.02468625,0.00555396,0.01926071,0.00154894,-0.01467758,-0.01875448,0.00486587,-0.0833272,0.03180147,0.01393586,-0.07215731,-0.05603146,0.01987081,-0.03150973,0.01541758,-0.02631677,0.11041555,0.01929652,0.01707315,0.04845883,0.04354829,0.04755896,-0.00509565,-0.06509618,0.00580283,-0.01791566,0.03317392,-0.01167771,-0.04652169,0.04601751,-0.05006246,0.01859273,-0.00902934,0.00023401,-0.02475136,0.00442152,-0.03318607,-0.02212225,0.09796239,0.01405474,0.04844696,0.00342829,0.04518471,0.07384776,-0.03070144,-0.04602595,0.02268029,-0.06154217,-0.04469741,0.04258126,0.02491787,0.05329788,0.06387456,0.06026858,0.02466586,-0.01157067,0.00293808,-0.05095955,-0.03055994,-0.05249708,0.05228062,0.0249952,0.0466861,-0.25899968,0.04039183,0.00249719,0.01988713,-0.01980909,-0.0239751,0.01531521,-0.02092526,0.03696852,-0.0213928,0.05477845,0.0157917,0.05394472,-0.05317577,0.03122912,-0.03102981,0.01322679,-0.01133095,0.08680665,0.00936231,0.02797561,0.07832438,0.25070155,-0.04868448,0.028927,0.0262596,-0.00165161,0.03033124,-0.01204985,0.04683849,0.0492951,0.04137021,0.08518244,0.02580929,-0.04663843,0.01373367,-0.02271428,0.01820992,0.01312062,0.05292663,-0.06980188,-0.00739508,0.00806743,-0.00177983,0.11768845,0.02914626,-0.02656677,-0.12476765,0.05623629,0.07783328,-0.07406217,-0.01457025,-0.03897776,-0.04051664,0.02550353,0.02285529,0.05764243,-0.0173331,0.03137783,-0.02400281,0.05288359,-0.00948417,0.03425717,0.01931877,0.02684605],"last_embed":{"hash":"1q8m2g","tokens":415}}},"text":null,"length":0,"last_read":{"hash":"1q8m2g","at":1753423675656},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)","lines":[159,178],"size":2257,"outlinks":[{"title":"<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy Systems**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":5},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":7},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":9},{"title":"_**<u>Skip Systems</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":10},{"title":"**<u>Lottery Utility Software</u>**","target":"https://saliu.com/lottery-utility.html","line":11},{"title":"Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings","target":"https://saliu.com/lottery-lotto-pairs.html","line":12},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":13},{"title":"Lotto wonder grid was analyzed under the worst-case scenarios.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":15},{"title":"Forums","target":"https://forums.saliu.com/","line":17},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":17},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":17},{"title":"Contents","target":"https://saliu.com/content/index.html","line":17},{"title":"Home","target":"https://saliu.com/index.htm","line":17},{"title":"Software","target":"https://saliu.com/infodown.html","line":17},{"title":"Search","target":"https://saliu.com/Search.htm","line":17},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":17},{"title":"Check lotto wonder grids with Super Utilities for lottery software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07240312,-0.05754526,-0.02482382,-0.0324055,-0.04465766,0.01330613,0.00938795,-0.0125539,0.04355,-0.01445291,0.01090322,-0.00492754,0.03856248,-0.02417628,-0.00161549,-0.03160844,0.03348048,0.06865185,-0.0139651,0.01489681,0.08096487,-0.0364584,-0.10007545,-0.11045506,0.03514956,0.02243074,-0.03710442,-0.04895878,-0.01284407,-0.17291978,0.00166838,0.01178729,-0.04772675,-0.06156154,-0.0592724,-0.0356597,-0.01132497,0.06352261,-0.09003506,0.03701203,-0.01877575,0.05071621,0.0240557,-0.04192988,0.0164672,-0.05130673,0.031723,0.02408165,0.03954847,-0.01738726,-0.01866688,0.00397505,-0.01395343,0.02919564,0.01121986,0.01309235,0.07331295,0.07380165,0.00898925,0.04686992,0.03475324,0.03332482,-0.14054504,0.02137952,0.03877623,0.06768616,-0.00075844,0.0003728,0.00357844,0.04588312,0.00749333,0.00020309,-0.01804084,0.12373743,0.02018947,-0.01257678,-0.0164046,-0.03474956,-0.03002742,0.00672481,-0.05478531,-0.00584895,0.00204435,0.05532964,-0.00816513,0.04485936,0.01183055,0.01100256,0.10917798,-0.09694947,-0.00303507,0.00030582,0.05928687,0.04614419,0.02692658,0.01376644,0.05059748,-0.00422445,0.02008191,0.12130308,-0.00595997,-0.02545651,0.04752041,-0.01502862,0.07252632,-0.03114342,-0.06878869,-0.07397024,-0.04081869,0.03332986,0.02132709,-0.03120342,0.07448227,-0.03291718,-0.01307952,-0.0301934,-0.00068226,0.01375327,-0.01986526,-0.00081567,-0.06247177,0.05280128,-0.01375485,-0.00886058,0.00781123,-0.04096806,-0.0104296,0.08402926,-0.02868965,0.00348748,0.03690229,-0.03499438,-0.11358061,0.00424906,0.008599,-0.06041939,0.01569784,0.00800578,-0.02997825,0.03495501,-0.00587063,-0.05857041,0.0712151,-0.09915362,-0.05282633,0.06110163,-0.00431497,-0.01325216,-0.02820618,-0.0171627,0.0117239,-0.02589952,-0.03099659,-0.04842694,0.00122583,-0.03793106,0.09424558,0.01847603,-0.0636516,0.03455879,-0.0399738,-0.03570231,-0.01496267,0.16184026,0.00269499,-0.04907457,-0.01771632,0.05460616,0.0070144,-0.0271624,-0.01250058,0.00960554,-0.06042783,-0.00390587,0.1177887,-0.01268989,-0.076752,-0.01862736,-0.04715769,-0.01635674,-0.00669533,0.00724751,-0.00873054,-0.02796807,0.00711308,-0.05636197,-0.02074686,-0.02314201,0.03716584,0.05138102,-0.02872303,0.0055762,-0.02119422,-0.00363249,-0.04413278,-0.02926167,-0.06189254,-0.04964646,0.05250277,0.0151518,-0.01299587,0.00469527,-0.00942831,0.00735956,0.00963248,0.03511938,-0.01015129,-0.08639672,0.04478781,0.06177295,-0.06677196,-0.03483899,0.06531166,0.03811622,-0.04654152,0.01401087,0.02250022,0.05830604,-0.02017567,0.02307642,0.00339216,0.05409116,-0.06108575,-0.2105177,-0.0322238,-0.04076094,-0.0179686,0.03002059,0.01891739,-0.01455099,-0.03577912,0.00976283,0.08748578,0.13970912,-0.06539374,0.03282373,0.0624144,-0.01277935,-0.03128372,-0.10208311,-0.05973176,-0.03395632,0.04833671,-0.02293303,0.00574468,0.02530565,-0.11082395,-0.00162458,0.00587714,0.14075749,-0.00645203,0.05485556,0.00080427,0.05839764,-0.0237224,-0.06103774,-0.02434459,0.01125597,0.02188925,-0.02433619,-0.03438014,-0.01348369,0.01004636,-0.06918498,-0.00036089,0.01579756,-0.07231741,-0.02097775,0.01252958,-0.04641965,0.0172211,-0.02138156,0.11925936,0.00249874,0.03004483,0.04805475,0.04353362,0.04106055,-0.00476322,-0.06011298,0.00558963,-0.01445936,0.03261972,0.00894202,-0.03749627,0.02673224,-0.03835471,0.02827353,-0.0044891,-0.01466621,-0.04760647,0.00245909,-0.05494877,-0.01490841,0.10064718,0.00681988,0.04447002,0.00283278,0.0375788,0.08832215,-0.04228692,-0.0448067,0.03257206,-0.03923372,-0.04786733,0.03840536,0.05621487,0.04938344,0.05125502,0.04545318,0.03892096,-0.03166576,-0.00283786,-0.03763518,-0.00426656,-0.05913764,0.0249139,0.01717214,0.03318453,-0.24798314,0.04996263,-0.00680178,0.02294885,-0.02556554,-0.03909164,0.02543195,0.00892261,0.03956519,-0.02804808,0.06879608,0.01532429,0.02762965,-0.05097585,0.04182922,-0.01568086,0.02498208,-0.0284358,0.08591922,0.01161622,0.0315632,0.08510484,0.26293632,-0.03344921,0.05756263,0.01054767,-0.01400221,0.03366381,-0.03270461,0.05395025,0.04943316,0.01858574,0.06768301,0.02713089,-0.04192552,0.03638794,-0.00934565,0.02420039,-0.00858915,0.06559613,-0.086512,-0.01690399,-0.0074547,0.03118345,0.10440779,0.02234512,-0.02725767,-0.12570098,0.05616416,0.07539903,-0.0746166,-0.01943361,-0.03538202,-0.05519279,0.02983638,0.02698242,0.06651807,-0.02628474,0.02106709,-0.03619388,0.03682179,-0.01431135,0.03589276,0.00342082,0.00507822],"last_embed":{"hash":"1m22jsz","tokens":106}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423675788},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{2}","lines":[162,162],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05849348,-0.0175273,-0.01429372,-0.06490306,-0.01426541,0.00392315,0.01862611,-0.00521004,0.06171518,-0.03249003,0.03831361,-0.00440521,0.05227604,-0.049096,-0.02858023,-0.0502305,0.02018546,0.04891942,-0.00881932,-0.00113484,0.07141556,-0.02027241,-0.08511779,-0.10766783,0.05512365,-0.00280851,-0.05963186,-0.05494055,-0.05911613,-0.20069499,0.00076859,0.00379592,-0.04264709,-0.04513823,-0.03536251,0.00702662,0.01226393,0.04348569,-0.02550103,0.03108343,-0.01116059,0.0355521,0.02249543,-0.01071707,0.02887549,-0.04590255,0.01102546,0.01961128,-0.00181341,-0.00876368,-0.0579608,-0.00496816,0.01806805,0.01256971,-0.00007637,0.02019383,0.06514073,0.05804919,0.03730758,0.0891284,0.05328374,0.0193205,-0.16675584,0.02069587,0.07952409,0.04686847,-0.0264823,-0.02878322,-0.01736755,0.02213612,-0.02332685,-0.01576357,-0.02265953,0.12361067,0.0397756,-0.02840864,-0.0474752,-0.03409686,-0.04299427,0.0081375,-0.0445764,0.03395179,0.02005167,0.04910646,-0.01023809,0.04555922,0.01689883,-0.00176355,0.12160952,-0.077953,0.02216462,0.00853232,0.04994994,0.04483277,0.02409981,0.01554209,0.04111353,-0.01831173,0.03255249,0.12016931,-0.02649478,-0.0409351,0.04397416,-0.01042986,0.06084291,-0.04889168,-0.06441173,-0.068547,-0.01296662,-0.0027432,-0.02185775,-0.04305886,0.03851925,-0.04130562,-0.02477177,-0.02373126,0.01305396,-0.02080219,-0.03173551,0.0225403,-0.05211104,0.01567329,-0.00093484,0.00397647,0.01404895,-0.05212837,0.03361854,0.08120279,-0.03692732,-0.00987343,0.04638338,-0.03051844,-0.12796538,0.00886409,0.0036271,-0.02434089,0.01873304,0.0040934,0.00974347,0.01942743,-0.01392763,-0.06152498,0.05303955,-0.07709607,-0.0587303,0.08699854,0.00151781,-0.03188094,-0.03863287,-0.0351378,0.03376064,-0.0415922,-0.03538791,-0.02310656,0.01053479,-0.0430078,0.08251775,0.03656118,-0.03942845,0.0092599,-0.02922156,-0.06282289,-0.0123753,0.16963804,0.0032538,-0.05691292,-0.00851976,0.03138005,0.00926867,-0.03377917,0.04032919,-0.01732843,-0.05990755,0.00936647,0.04824302,0.00074608,-0.03099028,-0.00573679,-0.03464623,-0.01791988,0.00211762,0.00333925,-0.03259959,-0.00273653,-0.00713879,-0.0563044,-0.02363237,0.0118689,0.01145682,0.05294488,-0.01392831,-0.01462169,-0.04893784,0.02424055,-0.02829556,-0.04339442,-0.0472657,-0.0498479,0.04901104,-0.02021803,0.01677496,-0.00212643,-0.01503807,0.00154371,0.06929164,0.04106575,-0.04461873,-0.06384155,0.03464802,0.04087935,-0.05934357,-0.031858,0.05442545,0.01815344,-0.01273639,0.01728991,0.02667274,0.02800887,0.00795642,0.05129563,0.01891606,0.05814412,-0.02346523,-0.23339622,-0.02937772,-0.03737984,-0.02592371,0.009962,0.00944778,-0.00541259,-0.03217948,-0.00024934,0.10521509,0.12686241,-0.03014551,0.03918826,0.04061231,-0.00131034,-0.01812521,-0.10560613,-0.07353679,-0.0792928,0.07423708,-0.03583182,0.00820557,-0.01828222,-0.06994296,0.01391891,0.00672301,0.12116822,-0.02608168,0.04144266,-0.00193519,0.08323938,-0.01857538,-0.04181295,-0.03690408,0.00493565,0.02910811,-0.00458841,-0.01935649,-0.01877311,0.0194601,-0.110594,0.0491899,0.01975841,-0.07148017,-0.02750146,0.04061322,0.00483783,-0.0197116,-0.03400473,0.09747449,-0.00305642,-0.00179414,0.03567783,0.07339162,0.06861903,-0.00967132,-0.04193193,0.04009077,-0.02827076,0.01764479,-0.02877315,-0.05976824,0.05765408,-0.03137214,0.01700877,-0.01616892,0.00102996,-0.03023614,0.00557685,-0.03322025,-0.02460009,0.10427712,-0.00992187,0.02553587,-0.01509649,0.06519569,0.08506455,-0.04303786,-0.05757764,0.03869018,-0.02127732,-0.06188021,0.04519731,-0.00292526,0.03579611,0.06539052,0.06257946,0.00928887,-0.00522447,0.00145473,-0.0217461,-0.03753036,-0.04237022,0.03646185,0.02406626,0.03737366,-0.23537081,0.03625841,-0.01621685,0.0166889,-0.01765855,-0.02293277,0.01997088,-0.01585682,0.05064142,-0.01234352,0.06742033,0.01695495,0.02792413,-0.07308395,0.02364827,-0.0184832,0.05402606,-0.02930708,0.07185337,0.01740385,0.02378211,0.07762451,0.27181625,-0.03484095,0.03608303,0.01393686,-0.01049736,0.02239308,-0.04153207,0.03812413,0.06562285,0.01700804,0.07878509,0.02689406,-0.05674152,0.00037597,-0.02064002,0.05391469,0.03049538,0.06907456,-0.06927237,-0.01285474,-0.02478504,-0.03239452,0.10602338,0.04884618,-0.03793584,-0.11542112,0.05288542,0.06296198,-0.05014972,-0.03066806,-0.05172189,-0.05261571,0.01885564,0.01787391,0.04187403,-0.0059539,0.05692561,-0.01848863,0.0851723,0.00336646,0.02472685,0.02550673,0.02796268],"last_embed":{"hash":"pazr3d","tokens":112}}},"text":null,"length":0,"last_read":{"hash":"pazr3d","at":1753423675821},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{4}","lines":[164,164],"size":231,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0804681,-0.02289892,-0.03265998,-0.02186708,0.01053715,0.02606292,-0.00778692,-0.00061382,0.02244276,-0.02511624,-0.0058181,-0.02862525,0.02424184,-0.00764497,-0.00921208,-0.03730532,0.02658771,0.04432144,-0.01395267,0.05194965,0.09209105,-0.05532445,-0.05351038,-0.09611411,0.03933191,-0.02729926,-0.0516996,-0.02446254,-0.0416375,-0.21600524,0.00353082,-0.01625266,-0.04319091,-0.04601484,-0.05118497,0.01526715,0.00560936,0.06850828,-0.05513598,0.05640227,-0.00166823,0.03052072,0.01077816,-0.0438542,0.00352619,-0.05645639,0.0283772,-0.01210582,0.00581642,-0.00507219,-0.04211002,-0.01376389,-0.02026419,0.00603845,0.02116261,0.02184774,0.0702175,0.02226155,0.0084421,0.07055114,0.01741605,-0.00626754,-0.18436463,0.01730686,0.07464971,0.02605996,-0.03064704,-0.00723372,-0.00161384,0.024328,-0.01353656,0.01024603,-0.02632597,0.11118599,0.00152409,-0.01913433,-0.03635558,-0.01450071,-0.04005538,-0.00768733,-0.04391379,0.00173901,-0.01243371,0.03406357,0.03427687,0.03876664,-0.00677575,0.02214324,0.08022235,-0.05815574,0.01273298,-0.02184832,0.04900337,0.07075206,0.0075583,0.04761017,0.0109858,-0.00814893,-0.02296131,0.10839788,0.01133392,-0.03421398,0.05337789,0.02497166,0.09027089,-0.02470047,-0.05699379,-0.07078592,-0.00524055,-0.0028803,-0.0279166,-0.0498466,0.04802053,-0.02072446,-0.00890549,-0.03608971,0.05759512,0.00823773,-0.06096257,0.03574375,-0.10894702,0.05305433,0.01094492,-0.01101125,0.03932242,-0.04894743,0.00036464,0.07606602,-0.00423087,0.00062023,0.02366628,-0.0544666,-0.09071154,0.00560588,0.01230886,-0.04002148,0.0208034,0.0199593,-0.0149109,0.01585651,0.01208955,-0.07636435,0.04353341,-0.06593709,-0.06202979,0.06832934,-0.03364861,-0.03197827,-0.03521595,-0.01360543,0.0291366,0.00083854,0.00675858,-0.03523267,0.00431417,-0.05191118,0.07464176,0.05999042,-0.04367127,0.04258579,-0.00458649,-0.05497513,0.0003919,0.17244348,0.04371598,-0.04512016,-0.02234029,0.02763651,0.02755478,-0.03001618,0.00542967,0.0082179,-0.0206423,-0.00705425,0.05030494,-0.02681932,-0.04278208,0.0008099,-0.06481755,-0.00258759,-0.0350858,0.05018624,-0.00575272,-0.02957022,0.00096219,-0.07469539,-0.00018703,-0.02304457,0.0241438,0.04274549,-0.06441729,-0.05894311,-0.00239518,0.04524376,-0.02872446,-0.05484435,-0.03664688,-0.08904044,0.0467726,0.0332157,0.00395458,-0.00326894,-0.01995575,-0.01801733,0.00375993,0.05966153,-0.02516048,-0.06962854,0.04221338,0.05067316,-0.04529351,-0.01960265,0.07276086,0.02501429,-0.02634499,0.01929122,0.03630167,0.03078216,0.02958264,0.03771979,0.03853484,0.03327304,-0.05131529,-0.2338524,-0.01131687,-0.0676214,-0.06357571,0.02670084,-0.0208091,0.01828371,-0.04177465,0.01964458,0.09216333,0.10833325,-0.04326742,0.05059749,0.01501233,-0.00038972,-0.03957897,-0.08694883,-0.06855338,-0.05211767,0.05894536,-0.03867991,0.07191826,0.02809906,-0.09460416,0.01611637,0.00411626,0.15971033,0.0492472,0.0436624,-0.01688593,0.08234251,-0.00460158,-0.0384077,-0.0203819,0.03344711,0.00497424,-0.02085591,-0.0015754,-0.04142452,0.00353962,-0.11337189,0.05654661,0.00730624,-0.06153666,-0.03786706,0.0385823,-0.03193875,-0.00412224,-0.0672614,0.08896571,0.04431972,-0.02260764,0.03739841,0.04296825,0.08420394,0.01354371,-0.0904224,0.00155382,0.00062922,-0.00092845,0.00666893,-0.00238391,0.06490281,-0.0632751,0.00119259,-0.00722589,0.00148513,-0.00720385,-0.03665174,-0.03631475,-0.03343617,0.09656072,0.01675412,0.0477492,0.02711419,0.05946592,0.03037098,-0.01989986,-0.0418503,0.03841617,-0.01073393,-0.03379723,0.04085908,0.0017424,0.05473115,0.06031438,0.01426108,0.01968071,0.00440518,-0.00333736,-0.05731196,-0.00934057,-0.07174956,0.03054657,0.05650377,0.036567,-0.24385862,0.03990794,-0.0045286,0.01104692,-0.01693158,0.0095325,0.00406435,0.00362296,0.05545644,-0.02588109,0.08230025,0.03113105,0.04440272,-0.0664805,-0.01303579,-0.00489516,0.00365637,-0.00677776,0.09495266,-0.0252269,0.02466719,0.07280381,0.2499745,-0.03363407,0.00528169,-0.0160008,0.00073023,0.02546072,-0.04153422,0.03869501,0.04872437,0.02972046,0.08982553,0.0205489,-0.00427439,0.02388892,-0.00922393,-0.00939629,0.01129677,0.02255551,-0.08295194,0.01944121,-0.004288,0.0065869,0.10962074,0.0258321,-0.00758186,-0.10825449,0.05557045,0.08182796,-0.05605822,-0.02479857,-0.01139423,-0.01340308,0.01683878,0.01962475,0.07352488,-0.04477846,0.0419433,-0.04103353,0.04966455,0.01991803,0.0449931,-0.02489342,0.01928095],"last_embed":{"hash":"16h7frw","tokens":309}}},"text":null,"length":0,"last_read":{"hash":"16h7frw","at":1753423675854},"key":"notes/saliu/Worst-case Scenario in Lotto, Lottery, Software Systems.md#Worst-case Scenario in Lotto, Lottery, Software Systems#[<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)#{12}","lines":[173,178],"size":605,"outlinks":[{"title":"Lotto wonder grid was analyzed under the worst-case scenarios.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Software","target":"https://saliu.com/infodown.html","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Check lotto wonder grids with Super Utilities for lottery software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":5}],"class_name":"SmartBlock"},
