
"smart_sources:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md": {"path":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"10xnq7k","at":1753423416781},"class_name":"SmartSource","last_import":{"mtime":1753368432860,"size":16606,"at":1753423416792,"hash":"10xnq7k"},"blocks":{"#---frontmatter---":[1,6],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program":[8,134],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}":[10,15],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program##一、 [第一個基於_馬可夫鏈_的樂透程式： _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery) II. [Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法](https://saliu.com/Markov_Chains.html#Algorithms)":[16,19],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program##一、 [第一個基於_馬可夫鏈_的樂透程式： _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery) II. [Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法](https://saliu.com/Markov_Chains.html#Algorithms)#{1}":[18,19],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>":[20,69],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{1}":[22,23],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{2}":[24,25],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{3}":[26,52],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{4}":[53,54],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{5}":[55,55],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{6}":[56,61],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{7}":[62,62],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{8}":[63,63],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{9}":[64,64],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{10}":[65,65],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{11}":[66,67],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{12}":[68,69],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>":[70,110],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{1}":[72,75],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{2}":[76,76],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{3}":[77,77],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{4}":[78,78],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{5}":[79,79],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{6}":[80,80],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{7}":[81,81],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{8}":[82,82],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{9}":[83,83],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{10}":[84,85],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{11}":[86,97],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{12}":[98,98],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{13}":[99,99],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{14}":[100,101],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{15}":[102,110],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>":[111,134],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{1}":[113,113],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{2}":[114,114],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{3}":[115,115],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{4}":[116,116],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{5}":[117,117],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{6}":[118,118],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{7}":[119,119],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{8}":[120,120],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{9}":[121,121],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{10}":[122,122],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{11}":[123,123],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{12}":[124,124],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{13}":[125,125],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{14}":[126,126],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{15}":[127,128],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{16}":[129,134]},"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":16},{"title":"第一個基於_馬可夫鏈_的樂透程式： _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":16},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":18},{"title":"_**，Cristiano Lopes 的程式貼**_","target":"https://saliu.com/programming.html","line":24},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":68},{"title":"**「神奇網格**樂透策略」","target":"https://saliu.com/bbs/messages/9.html","line":77},{"title":"_**馬可夫鏈、追隨者、配對、彩票、樂透、軟體**_","target":"https://saliu.com/markov-chains-lottery.html","line":84},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":86},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":90},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":94},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":96},{"title":"**_markov.exe_ 樂透程式**","target":"https://saliu.com/freeware/markov.exe","line":98},{"title":"_**馬可夫彩票程式：幫助、說明**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":100},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":102},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":109},{"title":"**機率論**","target":"https://saliu.com/theory-of-probability.html","line":113},{"title":"軟體","target":"https://saliu.com/infodown.html","line":114},{"title":"_**賭博的基本公式**_","target":"https://saliu.com/Saliu2.htm","line":115},{"title":"_**賭博 基本 公式**_的 數學","target":"https://saliu.com/formula.htm","line":116},{"title":"_**最佳賭場賭博系統：二十一點、輪盤、有限馬丁格爾投注、累進投注**_","target":"https://saliu.com/occult-science-gambling.html","line":117},{"title":"_**軟體，公式計算樂透賠率，超幾何分佈機率**_","target":"https://saliu.com/oddslotto.html","line":118},{"title":"_**線上機率、賠率計算器**_","target":"https://saliu.com/online_odds.html","line":119},{"title":"_**軟體、計算樂透賠率的公式、超幾何分佈機率**_","target":"https://saliu.com/bbs/messages/266.html","line":120},{"title":"_**標準差、高斯、常態、二項分佈、分佈**_","target":"https://saliu.com/formula.html","line":121},{"title":"_**樂透**_**<u>奇蹟網格 </u>** 、 _**超級樂透策略、系統**_ 。","target":"https://saliu.com/bbs/messages/grid.html","line":123},{"title":"_**隨機性、隨機性程度、確定性程度**_ 。","target":"https://saliu.com/bbs/messages/683.html","line":124},{"title":"_**CoolRevGui**_ ： _最終檔案反轉器、隨機播放器、文字檢視器軟體_","target":"https://saliu.com/programming.html","line":126},{"title":"_**UPDOWN**_ ： _彩票結果、文字檔案中的逆序_","target":"https://saliu.com/bbs/messages/539.html","line":127},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":129},{"title":"論壇","target":"https://forums.saliu.com/","line":131},{"title":"新文章","target":"https://saliu.com/bbs/index.html","line":131},{"title":"賠率產生器","target":"https://saliu.com/calculator_generator.html","line":131},{"title":"目錄","target":"https://saliu.com/content/index.html","line":131},{"title":"基本公式","target":"https://saliu.com/formula.htm","line":131},{"title":"首頁","target":"https://saliu.com/index.htm","line":131},{"title":"搜尋","target":"https://saliu.com/Search.htm","line":131},{"title":"網站地圖","target":"https://saliu.com/sitemap/index.html","line":131},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":133}],"metadata":{"created":"2025-07-24T22:47:07 (UTC +08:00)","tags":["Markov Chains","games","gambling","software","formula","probability","lottery","lotto","mathematics","random","lotto-6","source code","programming","algorithm","number","follower","lotto pair"],"source":"https://saliu.com/Markov_Chains.html","author":null}},
"smart_sources:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md": {"path":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.16796385,-0.05020202,-0.05059958,-0.03821991,-0.03337938,0.04546932,0.01062917,0.00724032,0.06715015,0.00552565,-0.00001332,-0.04420219,0.07272363,0.02258105,0.02469233,-0.00979792,-0.01178106,0.01327569,-0.04179529,-0.05366221,0.08347248,-0.08741911,-0.04554819,-0.09010886,0.06835078,0.02919071,-0.00019487,-0.0610006,0.01355712,-0.16858973,0.01086237,0.01791229,0.05934918,-0.0101634,-0.07015581,-0.00447227,-0.0032013,0.03064818,-0.05841995,0.02766854,0.01083583,0.01477467,0.02985035,-0.01727011,0.05636981,-0.06072788,-0.00509235,-0.01756414,-0.01087963,-0.0329333,-0.11407994,0.00306766,0.01158713,0.01628554,0.07367287,-0.01007129,0.06735934,0.05160828,0.02219407,0.03081368,0.01507841,0.04958879,-0.20600836,0.04734541,0.01124887,0.02948517,-0.01754989,-0.03062788,0.06283814,0.07792655,0.0009847,0.02852846,-0.03649463,0.05775712,0.0305818,-0.02913093,0.00747355,-0.02503841,-0.0353954,-0.01461874,-0.04619158,0.00572829,-0.01577611,-0.02409971,-0.04109964,0.0684597,0.02230448,0.00069796,-0.00697985,-0.03158072,0.01650208,0.039837,0.04317746,0.04978562,-0.01253219,0.02636626,0.04413141,0.00932264,0.00466113,0.11474694,-0.00477295,-0.00323411,-0.02135582,-0.00028123,0.05911534,-0.00436347,-0.00050501,-0.05672182,-0.00678523,0.0027901,-0.00519209,-0.00935004,0.05623656,-0.07695615,-0.03045074,0.01662286,0.00125494,0.03187382,0.02327335,0.020458,-0.02053816,0.02444063,0.03488652,0.01977858,-0.02417201,0.01166919,-0.02297125,0.08333375,0.04689684,0.00037233,0.05633381,0.01498571,-0.11946002,-0.01332711,-0.01647921,-0.03272495,-0.00478876,-0.04003886,-0.00109347,-0.02273056,-0.0235501,-0.03644471,0.03339831,-0.10226033,-0.06201405,0.09824663,0.02968806,-0.02115536,-0.00826538,-0.03127626,0.02692931,-0.00416364,-0.02573306,-0.01466287,0.02416915,-0.00127966,0.101512,0.10259642,-0.01145039,0.01165213,-0.02234136,-0.06585371,-0.0483614,0.18407772,0.02043344,-0.08163733,0.01060905,0.03057718,-0.00249267,-0.07207367,0.03691404,0.04132766,-0.05349925,0.05152738,0.07354666,-0.05233075,-0.02814282,-0.05098626,-0.02039103,0.02662316,0.00320428,-0.04042558,-0.0324933,0.01181243,-0.05335515,-0.07789766,0.0027726,-0.00728662,0.00797739,0.00709885,0.00263488,0.01229079,-0.00528735,-0.00410468,-0.04912903,-0.0571561,-0.03487567,-0.05100041,0.04003026,-0.07058751,0.0128252,-0.00325857,-0.02069363,-0.00005534,-0.06314637,-0.01685323,-0.01863409,-0.01733918,-0.01098251,0.04155641,0.01026871,0.03508164,0.01724396,0.0730788,-0.02664987,0.03164318,0.02633943,0.03035414,0.0213042,0.02961817,-0.03022136,0.01475229,-0.08410529,-0.19767256,-0.0288096,-0.02502396,-0.00166224,0.03413873,-0.03659908,-0.00572462,-0.05534547,0.06602761,0.07247409,0.09435455,-0.04573271,-0.04203075,-0.00824444,0.00486229,0.02443362,-0.07191519,-0.02440166,-0.02422539,0.05348358,0.01097685,0.04614403,-0.0589154,-0.06540722,0.03284523,-0.01790602,0.11194414,0.04430959,-0.01366004,0.04137491,0.06165645,0.003989,-0.0245081,-0.08927459,0.00173665,0.02185638,-0.04769165,0.007073,-0.0296894,-0.04014077,-0.02630653,0.05627194,0.03135925,-0.05955315,-0.0422631,-0.0125095,-0.01313052,-0.03338483,-0.00023045,0.0190867,-0.0274035,0.03118907,0.03120899,0.02535214,0.02717928,-0.04130292,-0.04675287,-0.04554258,-0.00993705,0.04194456,-0.01119643,-0.06144009,0.04583218,-0.01442771,0.01677903,-0.00799402,0.00321587,-0.02496996,-0.00009012,0.02929692,-0.03661199,0.13368689,0.04592694,-0.03081284,0.03623998,-0.01301235,0.00500946,-0.06810164,0.00150156,-0.02335687,0.0063392,-0.06534363,0.05091324,0.06852769,0.08196155,0.00993055,0.05769361,0.03769676,0.04091425,0.02468667,0.0185142,-0.01002737,-0.02648257,0.02585946,0.05646,0.04076511,-0.30150408,0.04501965,0.00127272,0.06549229,0.00262636,-0.00943791,0.0568575,-0.03562488,-0.02684492,-0.06557983,0.02167321,0.06651826,0.05043725,-0.04436645,-0.01103663,-0.03667558,0.00556939,-0.04356169,0.05657674,0.03316659,0.01345357,0.02038398,0.24339202,-0.01845478,0.0398514,-0.00207413,0.00670325,0.01603327,0.0143839,0.02913732,0.00820553,0.00197674,0.08399439,-0.01677517,0.01117987,0.04402641,-0.01343601,0.01831885,-0.00799923,0.01702708,-0.05545623,-0.02766892,-0.06267102,0.01539833,0.14759032,0.08780357,-0.00186462,-0.09256329,0.03928915,0.08997698,-0.04334112,-0.08738048,-0.02371562,0.00876237,-0.01102119,0.04231336,0.0022994,-0.02297426,-0.02329812,-0.00256599,0.03509493,-0.01211095,0.04881157,0.05178194,0.00992397],"last_embed":{"hash":"10xnq7k","tokens":342}}},"last_read":{"hash":"10xnq7k","at":1753423724053},"class_name":"SmartSource","last_import":{"mtime":1753368432860,"size":16606,"at":1753423416792,"hash":"10xnq7k"},"blocks":{"#---frontmatter---":[1,6],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program":[8,134],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}":[10,15],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program##一、 [第一個基於_馬可夫鏈_的樂透程式： _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery) II. [Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法](https://saliu.com/Markov_Chains.html#Algorithms)":[16,19],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program##一、 [第一個基於_馬可夫鏈_的樂透程式： _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery) II. [Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法](https://saliu.com/Markov_Chains.html#Algorithms)#{1}":[18,19],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>":[20,69],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{1}":[22,23],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{2}":[24,25],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{3}":[26,52],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{4}":[53,54],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{5}":[55,55],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{6}":[56,61],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{7}":[62,62],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{8}":[63,63],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{9}":[64,64],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{10}":[65,65],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{11}":[66,67],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{12}":[68,69],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>":[70,110],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{1}":[72,75],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{2}":[76,76],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{3}":[77,77],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{4}":[78,78],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{5}":[79,79],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{6}":[80,80],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{7}":[81,81],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{8}":[82,82],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{9}":[83,83],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{10}":[84,85],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{11}":[86,97],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{12}":[98,98],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{13}":[99,99],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{14}":[100,101],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{15}":[102,110],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>":[111,134],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{1}":[113,113],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{2}":[114,114],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{3}":[115,115],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{4}":[116,116],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{5}":[117,117],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{6}":[118,118],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{7}":[119,119],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{8}":[120,120],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{9}":[121,121],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{10}":[122,122],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{11}":[123,123],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{12}":[124,124],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{13}":[125,125],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{14}":[126,126],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{15}":[127,128],"#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{16}":[129,134]},"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":16},{"title":"第一個基於_馬可夫鏈_的樂透程式： _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":16},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":18},{"title":"_**，Cristiano Lopes 的程式貼**_","target":"https://saliu.com/programming.html","line":24},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":68},{"title":"**「神奇網格**樂透策略」","target":"https://saliu.com/bbs/messages/9.html","line":77},{"title":"_**馬可夫鏈、追隨者、配對、彩票、樂透、軟體**_","target":"https://saliu.com/markov-chains-lottery.html","line":84},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":86},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":90},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":94},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":96},{"title":"**_markov.exe_ 樂透程式**","target":"https://saliu.com/freeware/markov.exe","line":98},{"title":"_**馬可夫彩票程式：幫助、說明**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":100},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":102},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":109},{"title":"**機率論**","target":"https://saliu.com/theory-of-probability.html","line":113},{"title":"軟體","target":"https://saliu.com/infodown.html","line":114},{"title":"_**賭博的基本公式**_","target":"https://saliu.com/Saliu2.htm","line":115},{"title":"_**賭博 基本 公式**_的 數學","target":"https://saliu.com/formula.htm","line":116},{"title":"_**最佳賭場賭博系統：二十一點、輪盤、有限馬丁格爾投注、累進投注**_","target":"https://saliu.com/occult-science-gambling.html","line":117},{"title":"_**軟體，公式計算樂透賠率，超幾何分佈機率**_","target":"https://saliu.com/oddslotto.html","line":118},{"title":"_**線上機率、賠率計算器**_","target":"https://saliu.com/online_odds.html","line":119},{"title":"_**軟體、計算樂透賠率的公式、超幾何分佈機率**_","target":"https://saliu.com/bbs/messages/266.html","line":120},{"title":"_**標準差、高斯、常態、二項分佈、分佈**_","target":"https://saliu.com/formula.html","line":121},{"title":"_**樂透**_**<u>奇蹟網格 </u>** 、 _**超級樂透策略、系統**_ 。","target":"https://saliu.com/bbs/messages/grid.html","line":123},{"title":"_**隨機性、隨機性程度、確定性程度**_ 。","target":"https://saliu.com/bbs/messages/683.html","line":124},{"title":"_**CoolRevGui**_ ： _最終檔案反轉器、隨機播放器、文字檢視器軟體_","target":"https://saliu.com/programming.html","line":126},{"title":"_**UPDOWN**_ ： _彩票結果、文字檔案中的逆序_","target":"https://saliu.com/bbs/messages/539.html","line":127},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":129},{"title":"論壇","target":"https://forums.saliu.com/","line":131},{"title":"新文章","target":"https://saliu.com/bbs/index.html","line":131},{"title":"賠率產生器","target":"https://saliu.com/calculator_generator.html","line":131},{"title":"目錄","target":"https://saliu.com/content/index.html","line":131},{"title":"基本公式","target":"https://saliu.com/formula.htm","line":131},{"title":"首頁","target":"https://saliu.com/index.htm","line":131},{"title":"搜尋","target":"https://saliu.com/Search.htm","line":131},{"title":"網站地圖","target":"https://saliu.com/sitemap/index.html","line":131},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":133}],"metadata":{"created":"2025-07-24T22:47:07 (UTC +08:00)","tags":["Markov Chains","games","gambling","software","formula","probability","lottery","lotto","mathematics","random","lotto-6","source code","programming","algorithm","number","follower","lotto pair"],"source":"https://saliu.com/Markov_Chains.html","author":null}},"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.16032679,-0.05909369,-0.04013748,-0.01732448,-0.01309057,0.03590529,0.01125847,0.01165605,0.0705248,0.01652064,0.00831936,-0.04568268,0.06080909,0.03376638,0.02035434,-0.00291763,-0.00854503,-0.00408814,-0.02634675,-0.03274233,0.09860714,-0.06948462,-0.05232628,-0.08984572,0.05866594,0.02710202,0.00087396,-0.05735998,0.01250268,-0.14813894,-0.00219532,0.01758746,0.04069434,-0.00561604,-0.05141384,-0.02849716,-0.0208407,0.03387757,-0.0387945,0.03266925,-0.00055697,0.03411838,0.01143624,-0.02700841,0.05259549,-0.0456254,-0.00449727,-0.01094993,-0.01251091,-0.02002745,-0.10241685,0.00907932,-0.00205812,0.02334541,0.07429589,-0.01111717,0.06416681,0.04215315,0.03015996,0.03301302,0.02284854,0.04788531,-0.20932692,0.04434023,0.00808177,0.01383249,-0.02915578,-0.01983915,0.03395538,0.08074329,0.01211687,0.03521044,-0.03035374,0.06373066,0.03483815,-0.02418661,0.00194009,-0.03691426,-0.04358509,-0.01611162,-0.04698735,0.00294834,-0.03101597,-0.04126319,-0.03380318,0.05917006,0.0367285,0.01928126,0.01000765,-0.01992949,0.006755,0.03871657,0.06747241,0.05573873,-0.0292627,0.03246254,0.04722323,0.0190902,-0.0043948,0.11544636,-0.03223043,0.00911821,-0.02362206,-0.00997758,0.05762735,-0.00286072,-0.00913819,-0.0529388,-0.02549457,-0.00792822,0.00506139,0.01392835,0.0728258,-0.0959594,-0.01282056,0.01468142,0.02045386,0.03281641,0.01204951,0.01764924,-0.03684082,0.00944909,0.04110349,0.01749306,-0.01277926,0.02025778,-0.01287367,0.08179263,0.05332819,0.01141252,0.05529079,0.0020455,-0.10975698,-0.00148455,-0.01500487,-0.04101422,0.00582479,-0.05276996,0.01145373,-0.03396831,-0.02855084,-0.04435614,0.02383516,-0.11715336,-0.05888057,0.09156846,0.03364903,-0.01896412,0.01091551,-0.03379526,0.00981163,0.00867032,0.00256205,-0.02880282,0.01870991,-0.00456377,0.1013644,0.11035402,-0.01077761,-0.00052745,-0.01143652,-0.06674697,-0.0523535,0.19209705,0.01014069,-0.09728743,-0.00801363,0.03099101,0.00360726,-0.08030098,0.02614491,0.0266517,-0.05049857,0.05937288,0.07932632,-0.03882115,-0.00143335,-0.03552885,-0.02507351,0.0233766,0.00374885,-0.04373187,-0.0448014,0.02717007,-0.04121715,-0.06639919,-0.00826206,-0.01116873,0.02478019,0.02002439,0.00499286,-0.00666763,0.00046633,0.00867975,-0.04724695,-0.04848026,-0.03491691,-0.04919199,0.03679537,-0.05873673,0.02525499,-0.00812005,-0.03354206,-0.01341974,-0.05492399,-0.02178105,-0.01465041,-0.02149551,-0.02556746,0.04449929,0.01336784,0.0359446,0.02107782,0.0755253,-0.04607522,0.01054612,0.00575795,0.0252082,0.00740208,0.00646567,-0.04298645,0.01374111,-0.09165748,-0.21145363,-0.01854725,-0.02543108,-0.00126264,0.01711234,-0.02838744,-0.00796204,-0.03950037,0.07620781,0.08199766,0.08564761,-0.0437245,-0.04657834,-0.03030979,-0.00142092,0.02537068,-0.05351496,-0.0456761,-0.01305393,0.05100856,-0.00418591,0.03802278,-0.05734776,-0.08440429,0.03968242,-0.02526619,0.12011704,0.0536858,-0.01175982,0.03127321,0.06893267,0.01303962,-0.01946215,-0.09477321,0.01104308,0.01999808,-0.05508635,0.01775969,-0.03930637,-0.04281797,-0.02628673,0.05196913,0.02347337,-0.06597278,-0.02385921,-0.02535831,-0.0081729,-0.03635979,-0.00267413,0.02716766,-0.02259849,0.01942315,0.02547225,0.02942013,0.02437364,-0.01961928,-0.04151072,-0.04738137,0.00260164,0.03338082,-0.00082427,-0.06178126,0.03358559,-0.00989072,-0.00156292,-0.00619504,0.00822838,-0.03607887,-0.01273957,0.02306131,-0.02009711,0.13039108,0.0457374,-0.04089051,0.02347795,-0.01481356,-0.00009341,-0.06292115,0.0037579,-0.01858154,0.017041,-0.04042813,0.0594536,0.08904023,0.07297438,0.00372667,0.03834829,0.04036447,0.04844999,0.01675631,0.01592417,-0.00068806,-0.03018052,0.00323954,0.06982082,0.02519903,-0.3078374,0.05129385,0.01115498,0.0607128,0.00428478,0.01539055,0.04717172,-0.02580554,-0.03969891,-0.04172676,0.02914589,0.08234584,0.03794257,-0.03677506,-0.02334031,-0.02923593,0.01245301,-0.03790968,0.05616452,0.03817105,0.00500391,0.02200301,0.2480865,0.00306243,0.03558005,-0.00188431,-0.00037774,0.02540681,0.02329382,0.02448235,-0.00325655,0.00544458,0.08032284,-0.00748677,0.02054143,0.04102663,-0.01093671,0.02401075,-0.00923328,0.03042742,-0.07857969,-0.01897982,-0.060399,0.02324858,0.13575161,0.08956873,-0.03292777,-0.09129489,0.03034716,0.07357098,-0.04658654,-0.09312528,-0.03306042,0.00348021,-0.02453389,0.05271759,0.02478082,-0.03403495,-0.01825928,0.00117814,0.02192105,-0.01814556,0.04428107,0.04916065,0.01170253],"last_embed":{"hash":"12yu4bd","tokens":137}}},"text":null,"length":0,"last_read":{"hash":"12yu4bd","at":1753423722324},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#---frontmatter---","lines":[1,6],"size":272,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.15412879,-0.03536705,-0.04603671,-0.04154766,-0.03807387,0.05849263,0.01773686,0.01312778,0.05645103,0.00386779,-0.01150533,-0.04561997,0.06858971,0.02032447,0.02239354,-0.0195644,0.00030101,0.04401552,-0.04055072,-0.07112718,0.08292951,-0.07908956,-0.05923174,-0.08097798,0.07024802,0.02510463,-0.0187913,-0.0733563,0.00929827,-0.17770989,0.02563423,0.00332223,0.06289429,-0.0148171,-0.07733458,0.00071125,0.0093495,0.03566148,-0.07441647,0.01570703,0.01459185,0.02102385,0.02830102,-0.03138103,0.05018251,-0.06741618,-0.00475593,-0.01273089,-0.00350163,-0.03621479,-0.11229388,-0.0009973,0.01727612,0.01316596,0.06443119,-0.01581684,0.06656729,0.06544045,0.01311438,0.02100073,0.01629772,0.05102838,-0.19942507,0.03472012,0.00563124,0.01931347,-0.00645165,-0.03827285,0.06448203,0.08271795,-0.00775596,0.03162563,-0.03677773,0.05684667,0.0309483,-0.03605173,0.0015369,-0.03126827,-0.02519459,-0.00712716,-0.05303438,-0.00016365,0.01234664,-0.02344372,-0.02934185,0.06452031,0.00068139,-0.00638383,0.00380764,-0.03345244,0.0210599,0.05111913,0.03050445,0.0233229,0.00336529,0.0283892,0.03965075,-0.0096578,0.00740777,0.12247363,0.00329651,-0.01081871,-0.01743224,-0.00268389,0.05374622,-0.00215715,-0.00657435,-0.0479713,-0.01286234,0.01049653,-0.01650049,-0.0348738,0.05375863,-0.0498011,-0.05027552,0.02215625,-0.00976207,0.04472115,0.01092271,0.03009716,-0.01451741,0.02011719,0.0299129,0.02360477,-0.01548023,-0.02609533,-0.02137824,0.08273493,0.03147897,0.01414456,0.05398585,0.02035955,-0.12269244,-0.01362692,-0.00443335,-0.02766639,-0.01931429,-0.02639963,-0.00980858,-0.00405885,-0.01671698,-0.03289623,0.04304197,-0.10201894,-0.04468693,0.11246493,0.02016582,-0.01368498,-0.01333187,-0.04830886,0.03242657,-0.00681987,-0.03573098,-0.03372026,0.00785184,0.0051982,0.10420733,0.09348141,-0.01861767,0.01212923,-0.04258444,-0.04313291,-0.03382938,0.19318745,0.0097437,-0.07057766,0.02384053,0.02595973,-0.01538506,-0.06684784,0.04049378,0.05099095,-0.06453595,0.07033964,0.07033421,-0.06876475,-0.04985355,-0.05492984,-0.01418604,0.02361204,0.01724925,-0.04440358,-0.02747759,0.01348295,-0.03221323,-0.06863894,0.0159694,0.00936807,0.01222696,-0.00964889,-0.00322722,0.01953874,-0.00625548,-0.01193775,-0.04469559,-0.05247752,-0.02935167,-0.0482306,0.02873709,-0.04953538,0.01447785,0.00425665,-0.00937152,0.01814058,-0.04812615,-0.00270507,-0.02110594,-0.02833963,0.00855669,0.04672361,-0.00210867,0.02591294,0.01332393,0.06522431,-0.02199863,0.03476559,0.03843115,0.03224004,0.0178542,0.05372774,-0.00355567,0.00951908,-0.07456808,-0.19221947,-0.0512569,-0.02102834,0.00563173,0.05477634,-0.04733199,0.00010562,-0.05843962,0.0628771,0.08000341,0.08142351,-0.05610871,-0.04502506,0.01088417,0.00672575,0.02588139,-0.08085483,-0.01597096,-0.03434885,0.03614161,0.0161461,0.04495484,-0.03474021,-0.05888283,0.03685191,-0.01436015,0.11978251,0.01378915,-0.01550192,0.05641718,0.04667664,-0.00631428,-0.03897003,-0.07944039,-0.01637536,0.02598618,-0.04156794,0.0059653,-0.00607596,-0.02429498,-0.02445944,0.04845176,0.03493021,-0.06674924,-0.03867447,0.00085977,-0.02163638,-0.02481108,0.01110035,0.01353195,-0.00947575,0.03867124,0.03983673,0.01933331,0.02845369,-0.04321129,-0.04512744,-0.03666614,-0.01515552,0.05402545,-0.01953463,-0.07112777,0.03726824,-0.0121087,0.02913429,-0.02490514,-0.00504009,-0.02128911,-0.01508126,0.03582224,-0.05032111,0.11725708,0.04433425,-0.02144516,0.04297407,0.0079677,0.01151026,-0.068344,-0.00627674,-0.03444874,0.01780494,-0.06388057,0.04310896,0.05867894,0.07126613,0.00870741,0.06276584,0.04830287,0.01784548,0.02487372,0.02120548,-0.01889156,-0.03141227,0.04154204,0.02053289,0.03952896,-0.30315709,0.03989228,-0.03067725,0.06135315,0.00373513,-0.02629939,0.0592125,-0.02805809,-0.00252811,-0.06936556,0.01345061,0.06506591,0.05679471,-0.05677641,-0.01252198,-0.03350069,0.02986377,-0.06193568,0.0603575,0.02785823,0.02469783,0.02173343,0.24734871,-0.03384683,0.05409082,0.01247048,0.01581455,0.00728809,0.00851994,0.02102471,0.02539737,-0.01486151,0.06935251,-0.01028065,0.01915817,0.04757597,-0.01681062,0.00966406,-0.00876614,0.00364796,-0.04097271,-0.03341853,-0.06935674,0.01557596,0.14785227,0.08102466,-0.00316926,-0.09574866,0.04539303,0.0971322,-0.04313749,-0.08203518,-0.01829357,0.00770536,-0.01017611,0.03781346,-0.01446862,-0.0143324,-0.01925669,-0.01917473,0.04380358,-0.01074416,0.04776792,0.05228465,0.0168043],"last_embed":{"hash":"1cay1ef","tokens":263}}},"text":null,"length":0,"last_read":{"hash":"1cay1ef","at":1753423722366},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program","lines":[8,134],"size":8989,"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":9},{"title":"第一個基於_馬可夫鏈_的樂透程式： _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":9},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":11},{"title":"_**，Cristiano Lopes 的程式貼**_","target":"https://saliu.com/programming.html","line":17},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":61},{"title":"**「神奇網格**樂透策略」","target":"https://saliu.com/bbs/messages/9.html","line":70},{"title":"_**馬可夫鏈、追隨者、配對、彩票、樂透、軟體**_","target":"https://saliu.com/markov-chains-lottery.html","line":77},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":79},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":83},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":87},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":89},{"title":"**_markov.exe_ 樂透程式**","target":"https://saliu.com/freeware/markov.exe","line":91},{"title":"_**馬可夫彩票程式：幫助、說明**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":93},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":95},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":102},{"title":"**機率論**","target":"https://saliu.com/theory-of-probability.html","line":106},{"title":"軟體","target":"https://saliu.com/infodown.html","line":107},{"title":"_**賭博的基本公式**_","target":"https://saliu.com/Saliu2.htm","line":108},{"title":"_**賭博 基本 公式**_的 數學","target":"https://saliu.com/formula.htm","line":109},{"title":"_**最佳賭場賭博系統：二十一點、輪盤、有限馬丁格爾投注、累進投注**_","target":"https://saliu.com/occult-science-gambling.html","line":110},{"title":"_**軟體，公式計算樂透賠率，超幾何分佈機率**_","target":"https://saliu.com/oddslotto.html","line":111},{"title":"_**線上機率、賠率計算器**_","target":"https://saliu.com/online_odds.html","line":112},{"title":"_**軟體、計算樂透賠率的公式、超幾何分佈機率**_","target":"https://saliu.com/bbs/messages/266.html","line":113},{"title":"_**標準差、高斯、常態、二項分佈、分佈**_","target":"https://saliu.com/formula.html","line":114},{"title":"_**樂透**_**<u>奇蹟網格 </u>** 、 _**超級樂透策略、系統**_ 。","target":"https://saliu.com/bbs/messages/grid.html","line":116},{"title":"_**隨機性、隨機性程度、確定性程度**_ 。","target":"https://saliu.com/bbs/messages/683.html","line":117},{"title":"_**CoolRevGui**_ ： _最終檔案反轉器、隨機播放器、文字檢視器軟體_","target":"https://saliu.com/programming.html","line":119},{"title":"_**UPDOWN**_ ： _彩票結果、文字檔案中的逆序_","target":"https://saliu.com/bbs/messages/539.html","line":120},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":122},{"title":"論壇","target":"https://forums.saliu.com/","line":124},{"title":"新文章","target":"https://saliu.com/bbs/index.html","line":124},{"title":"賠率產生器","target":"https://saliu.com/calculator_generator.html","line":124},{"title":"目錄","target":"https://saliu.com/content/index.html","line":124},{"title":"基本公式","target":"https://saliu.com/formula.htm","line":124},{"title":"首頁","target":"https://saliu.com/index.htm","line":124},{"title":"搜尋","target":"https://saliu.com/Search.htm","line":124},{"title":"網站地圖","target":"https://saliu.com/sitemap/index.html","line":124},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":126}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14727731,-0.03708418,-0.04012858,-0.04385897,-0.04050473,0.05738666,0.03200502,0.00145408,0.05310629,-0.00301031,-0.00485879,-0.04652837,0.07004897,0.01463957,0.01972254,-0.01551757,-0.00476799,0.03810723,-0.03626423,-0.06366365,0.1042412,-0.0757983,-0.0521524,-0.08482458,0.06935053,0.02676068,-0.02251217,-0.06729691,0.01665495,-0.15994115,0.01715195,0.01981564,0.07258779,-0.01461781,-0.06146733,-0.0020447,-0.01135642,0.04516577,-0.07742964,0.02949623,0.01461716,0.00806986,0.02335767,-0.02536569,0.04757844,-0.07246126,-0.00175428,-0.00575415,-0.01172052,-0.04133169,-0.11041038,0.00350914,0.01325131,0.01942286,0.06673948,-0.01501461,0.07286019,0.06294668,0.01624529,0.02542008,0.03416834,0.0424744,-0.20066047,0.03218821,0.00901334,0.01970294,-0.00778396,-0.03506245,0.06012039,0.09330359,-0.00525901,0.03014714,-0.0380358,0.05994515,0.02008779,-0.03340462,-0.00652693,-0.02811769,-0.02310351,-0.01092332,-0.04818561,0.01080793,0.00899098,-0.01934378,-0.0279862,0.07444812,0.01365726,-0.00296734,-0.0033199,-0.039272,0.01852826,0.04927912,0.02907597,0.03473022,-0.0143914,0.01488221,0.03985856,-0.01323761,0.01977817,0.12885924,-0.00614661,-0.01835207,-0.01410539,-0.00456787,0.04233341,0.00167011,-0.01053267,-0.0628521,-0.00934011,0.00788056,-0.01379577,-0.01858081,0.05547355,-0.05553084,-0.04161523,0.01312774,-0.00466581,0.04082801,0.01897231,0.01436057,-0.01350626,0.02414507,0.01963858,0.01620441,-0.0146677,-0.01172903,-0.01467903,0.08909208,0.03080204,0.00800408,0.04311416,0.00973628,-0.12670293,-0.01425511,-0.00393589,-0.02181576,-0.01870834,-0.02173604,-0.01355958,0.00195552,-0.02161391,-0.03817573,0.04376123,-0.10515618,-0.04298582,0.10898563,0.013976,-0.01092823,-0.00091026,-0.0485543,0.02640049,-0.01056303,-0.04088456,-0.02994431,0.00816631,0.0042648,0.09853311,0.10140469,-0.01804151,0.01691268,-0.03615743,-0.0515432,-0.03841357,0.19775586,0.01383864,-0.06383406,0.02349226,0.0258413,-0.0162064,-0.05696673,0.03430026,0.04357417,-0.06930904,0.06091097,0.08142146,-0.05847903,-0.0232954,-0.05104523,-0.02566309,0.02595126,0.01498169,-0.05551645,-0.03215542,0.01643045,-0.04011216,-0.07109237,0.00300106,0.00597874,0.01238009,-0.00719791,-0.00716063,0.024004,-0.01917335,-0.01912701,-0.03653698,-0.05791089,-0.03763949,-0.04812088,0.03213053,-0.0680673,0.01614352,0.00962078,-0.00985455,0.00683777,-0.04654744,0.00021313,-0.02673434,-0.0246962,0.00502886,0.0412859,-0.0036439,0.02712798,0.02262363,0.06640674,-0.01147866,0.03384116,0.03393731,0.02528658,0.00997107,0.05615989,-0.01516887,0.01866735,-0.07122517,-0.20279734,-0.04822091,-0.01862518,-0.00273996,0.03757782,-0.03259288,-0.00020154,-0.06030174,0.06534698,0.08272179,0.09546573,-0.05311057,-0.04910668,0.00320013,0.00742326,0.01701618,-0.08429231,-0.0310353,-0.0246766,0.03530411,0.00402222,0.04827197,-0.03647619,-0.07379285,0.02901304,-0.01496814,0.11218988,0.02834683,-0.01736025,0.04111002,0.04530681,-0.01249907,-0.03499809,-0.07954904,-0.00684893,0.0261193,-0.05107474,0.01239646,-0.01304149,-0.02654179,-0.0314286,0.05732875,0.02539713,-0.04731993,-0.017217,-0.00334514,-0.01464674,-0.02897188,0.01477227,0.02007908,-0.02097453,0.035303,0.03428623,0.03706309,0.0256366,-0.04546674,-0.04707317,-0.0359978,-0.01574291,0.05068035,-0.0180927,-0.07088587,0.03880997,-0.01938681,0.03396679,-0.01014589,-0.00821164,-0.01686625,0.00019818,0.03213237,-0.043144,0.10733103,0.04622763,-0.02699464,0.0354714,0.00421104,0.01870676,-0.06242466,0.00415971,-0.03078172,0.01543187,-0.07394401,0.03455412,0.05998917,0.06620689,0.00990498,0.05470833,0.03406334,0.02972453,0.024826,0.02008754,-0.00790607,-0.02748142,0.04424376,0.02216228,0.04167305,-0.30907458,0.04512336,-0.0213508,0.06995111,0.00476469,-0.02163203,0.0543154,-0.02007928,-0.0253379,-0.07470261,0.02347727,0.05977724,0.05578876,-0.06447925,-0.01868879,-0.02531166,0.04480323,-0.05355524,0.06608549,0.02478549,0.02624889,0.03131352,0.25676194,-0.02338225,0.05341808,0.00153297,0.01334838,0.01121128,0.01500771,0.02383178,0.02345003,-0.01593595,0.07814427,0.0060774,0.01807924,0.0412112,-0.020741,0.02079233,0.00104182,0.01116659,-0.05652026,-0.02744211,-0.07190609,0.01581192,0.13817023,0.08477315,-0.00446293,-0.09129826,0.03134726,0.08062276,-0.04120095,-0.08799732,-0.02341456,-0.00640058,-0.00528308,0.03599783,-0.00816743,-0.01534834,-0.03146516,-0.0118604,0.02846346,-0.00763665,0.05342046,0.04365064,0.00645786],"last_embed":{"hash":"1r0z96g","tokens":155}}},"text":null,"length":0,"last_read":{"hash":"1r0z96g","at":1753423722452},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#{1}","lines":[10,15],"size":300,"outlinks":[{"title":"See here the first application of Markov Chains mathematics to lottery software, lotto games.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program##一、 [第一個基於_馬可夫鏈_的樂透程式： _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery) II. [Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法](https://saliu.com/Markov_Chains.html#Algorithms)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.17783996,-0.040926,-0.03441249,-0.01413613,-0.02175124,0.05309149,-0.00402054,0.01415471,0.07452621,0.01211373,0.00149392,-0.05421542,0.0794676,0.02387561,0.0494842,-0.02945544,0.00605018,0.02576555,-0.06448109,-0.05582283,0.06236079,-0.07278336,-0.03224186,-0.05507256,0.0698051,0.01187106,-0.00800876,-0.0696971,-0.0003467,-0.19086081,0.02434874,-0.00795584,0.04729082,-0.0087034,-0.08023585,-0.0146405,0.01493027,0.0159781,-0.05830607,0.018378,0.01600538,0.03094086,0.03000952,-0.03754788,0.04286661,-0.06899174,0.00029406,-0.01264577,0.02185924,-0.037807,-0.09880656,-0.01925225,0.01687218,0.00585566,0.06736461,0.01224768,0.05937646,0.06601267,0.00808525,0.00213917,0.02429295,0.0667998,-0.19172624,0.03476091,-0.00925358,0.00284215,-0.02016864,-0.04112513,0.04666486,0.07232804,0.00615668,0.04002396,-0.04827892,0.05961307,0.03060848,-0.03769114,-0.00530747,-0.01262276,-0.03915387,-0.00583379,-0.06935027,0.00249968,0.00347247,-0.03987042,-0.01834984,0.06212213,0.01782578,-0.01725662,-0.009531,-0.02311736,0.00375888,0.02107481,0.0188355,0.03047346,0.00866706,0.02841129,0.03402349,-0.0017436,-0.00321586,0.11436243,-0.00232597,0.00959684,-0.01244994,-0.01475033,0.05967676,-0.00104067,-0.00458454,-0.02384222,-0.01333357,0.00114162,0.00383089,-0.02219509,0.04370777,-0.06523971,-0.04841288,0.03331186,-0.00083509,0.03975038,-0.01352076,0.02917252,-0.02543536,-0.00523735,0.04379899,0.02388211,-0.02251615,-0.02065835,-0.00435569,0.06338253,0.04950298,0.02326042,0.04968074,0.03785601,-0.12159207,-0.02179934,-0.03789775,-0.03926514,-0.01836768,-0.04917434,-0.0013189,-0.02761349,-0.0111016,-0.05619461,0.01793225,-0.11353113,-0.04013208,0.12552287,0.02293348,-0.00423941,-0.01203562,-0.052146,0.02394896,0.01185536,-0.01761021,-0.03933701,0.01203083,0.00226436,0.10833006,0.09792697,0.00052842,0.01925945,-0.01131738,-0.04370529,-0.04589634,0.18218438,0.02712683,-0.0903881,-0.00683754,0.00417716,-0.00224846,-0.06406516,0.05007561,0.05249896,-0.03948787,0.06640756,0.06553601,-0.06112204,-0.04592736,-0.05305086,-0.01268665,0.02764947,0.00332415,-0.03948596,-0.03635741,0.0243452,-0.0278975,-0.06313536,0.03179734,-0.00342107,0.01814208,0.01163098,-0.00261517,0.0394638,0.02022238,0.00994541,-0.05315148,-0.04029626,-0.0242377,-0.03491755,0.0317634,-0.03296908,0.02724291,0.00011699,-0.03092329,-0.00644541,-0.05589528,-0.02329292,-0.00821778,-0.02049938,0.00426742,0.04406022,0.0017811,0.04148008,0.02318478,0.07185768,-0.03927297,0.02874025,0.02321164,0.03417305,0.02413548,0.01629584,-0.00090994,0.01639178,-0.08982939,-0.18519963,-0.04646527,-0.02227418,-0.00175724,0.05016076,-0.05666424,0.00567557,-0.02560024,0.07345056,0.05943577,0.07050311,-0.06818344,-0.03901951,0.00306332,0.00994323,0.05661129,-0.05662125,-0.00890148,-0.02150414,0.04996817,0.00820841,0.0866606,-0.02707958,-0.02695163,0.05621199,-0.01531984,0.12014061,0.01344795,-0.01406545,0.04944053,0.04837447,0.03283714,-0.04760495,-0.0933478,-0.00797427,0.0262305,-0.04635397,0.00449929,-0.01800741,-0.03491514,-0.00921784,0.03913104,0.03258028,-0.09577142,-0.02961698,-0.01510104,-0.00646971,-0.02794146,-0.00105473,0.00380545,-0.0104468,0.04506885,0.04693264,0.00721937,0.0200919,-0.04933763,-0.05310888,-0.04787946,-0.0042973,0.0462601,-0.01184677,-0.0598931,0.03063539,-0.01127468,0.01047335,-0.03467801,-0.00823642,-0.02148913,-0.03501087,0.05228915,-0.04470119,0.12790854,0.04814229,-0.00875577,0.03164171,-0.00301649,-0.01789443,-0.07867149,-0.02359303,-0.02736629,0.03152746,-0.04857646,0.04210323,0.06781501,0.07974231,0.01331601,0.06378986,0.0372644,0.01842465,0.01436103,0.01690207,-0.03100814,-0.02358646,0.03646513,0.04644123,0.02518068,-0.3076669,0.03299461,-0.03102545,0.04350315,-0.0131768,0.0014692,0.05205648,-0.0258876,0.02633199,-0.04580147,0.00075008,0.09557094,0.04532107,-0.05447224,-0.02716745,-0.03554446,0.00715748,-0.04354351,0.04454822,0.01520094,0.01133022,-0.00944307,0.24654476,-0.00465601,0.0429618,0.00950515,0.00902927,-0.00239306,0.00403776,0.0022339,0.02690897,-0.00475968,0.05834736,-0.0140921,0.02779193,0.05622076,0.00304254,-0.00050932,-0.02269517,0.00369131,-0.05016705,-0.02617298,-0.07572988,0.02371287,0.15934323,0.0630784,-0.01721489,-0.07227556,0.05410661,0.1108653,-0.03434507,-0.08599503,-0.00958877,0.02211666,-0.00695054,0.03002946,-0.00197839,-0.02741575,-0.00881444,-0.00988457,0.04467988,-0.01980975,0.02720094,0.05579305,0.02772141],"last_embed":{"hash":"x87nth","tokens":304}}},"text":null,"length":0,"last_read":{"hash":"x87nth","at":1753423722496},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program##一、 [第一個基於_馬可夫鏈_的樂透程式： _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery) II. [Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法](https://saliu.com/Markov_Chains.html#Algorithms)","lines":[16,19],"size":303,"outlinks":[{"title":"Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法","target":"https://saliu.com/Markov_Chains.html#Algorithms","line":1},{"title":"第一個基於_馬可夫鏈_的樂透程式： _markov.exe_","target":"https://saliu.com/Markov_Chains.html#MarkovLottery","line":1},{"title":"Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09826468,-0.05161179,-0.04150583,-0.0073676,-0.03286555,0.01156904,0.00995639,0.01384433,0.04892351,0.03293598,0.02261483,-0.06734148,0.01587375,0.02545834,0.03988456,-0.01135012,0.03459616,0.02843186,-0.03268188,-0.00432507,0.0810194,-0.06189687,-0.02712343,-0.06146729,0.06475274,0.03587743,0.00495512,-0.02582642,0.00304795,-0.19799471,-0.01455793,0.00501763,0.0352689,0.01472235,-0.02079983,-0.01928815,-0.04174681,0.03206879,-0.04427378,0.02767777,0.06293542,0.01273966,0.00984653,-0.02890453,0.01957774,-0.0747399,-0.00542219,-0.00513458,-0.00290983,-0.00069606,-0.08851556,0.01757355,0.04708812,0.04478989,0.04546059,0.03632227,0.09458463,0.06186839,0.02921485,0.00279588,-0.0134978,0.07455898,-0.22588962,0.08039878,-0.01063289,-0.01525813,-0.04227715,-0.0187161,0.01386189,0.05231359,-0.05958189,-0.02316577,0.00896874,0.0637091,0.03041839,-0.06772185,0.0010819,-0.06677572,-0.05177238,-0.05606378,-0.02904272,-0.01238353,-0.04317928,-0.04935343,-0.00286613,0.08151378,-0.00383983,-0.02324779,0.03058931,-0.00128697,-0.02141139,-0.04851047,0.02937624,0.03338561,-0.03922285,-0.00835919,0.01952097,0.06963989,-0.00033551,0.11103036,0.01620447,-0.03488389,0.0122269,-0.06215051,0.06905698,0.01990432,-0.00279546,-0.00854087,-0.07152133,0.03463478,-0.06110196,-0.05445159,0.01026237,-0.04510254,-0.04229289,0.00319281,0.00839057,0.02809053,0.03920291,-0.02980009,-0.01429422,0.0101523,0.01877228,-0.00521088,0.00582354,-0.00873293,0.03613431,0.07993206,0.06022521,0.02448455,0.02689565,0.03042941,-0.09715404,-0.00396469,-0.02648935,0.00181779,0.01023205,-0.03068699,-0.00715412,-0.0402315,-0.01382651,-0.05415922,0.00260759,-0.08424441,-0.04627242,0.08060832,-0.01417692,-0.01859563,0.00088242,-0.03341782,0.0359243,0.05580921,-0.02106315,-0.00440916,-0.00668545,0.027215,0.07636118,0.06726103,-0.01519469,0.00080548,-0.02012227,-0.07885183,-0.04947803,0.19897074,0.02926265,-0.05776424,-0.01791498,0.03568676,0.01330211,-0.08377379,0.04938988,0.03101557,-0.03510711,0.06163407,0.08973092,-0.0333066,-0.05152079,-0.05113699,-0.0423147,0.0325422,0.02868904,-0.01879241,-0.04087537,0.03410487,-0.02765166,-0.04811702,0.02881414,-0.01716226,0.05290648,-0.00580316,-0.00050208,0.01988689,0.02334458,-0.00964368,-0.0314289,-0.01980195,-0.0343409,-0.02250337,0.03000355,-0.073121,0.10677154,-0.01014955,-0.06317391,0.00117239,-0.05084778,-0.01369912,0.01315905,-0.00589723,-0.00149907,0.06701665,0.00558366,0.05664209,0.04031242,0.07087906,-0.01134626,0.00096988,0.01291758,-0.01328744,0.01073626,0.03843449,-0.02042624,-0.02519348,-0.10059305,-0.21398239,0.00669596,0.00763805,-0.02614142,0.02790474,-0.00795083,-0.00532696,-0.02215555,0.12309563,-0.0009922,0.0826118,-0.03609947,-0.06886546,-0.01107131,-0.01534713,-0.00846181,-0.00259999,-0.01062815,0.01963185,0.01099712,0.01325438,0.06660803,-0.03818197,-0.02824827,0.02919758,-0.00939489,0.12407997,0.01132653,-0.02445574,0.03004394,0.04396733,0.03079668,-0.01933858,-0.07664859,0.02065082,0.0535765,-0.06093274,0.00166408,-0.02518565,-0.07371172,0.05109682,0.05472033,0.00877614,-0.07853173,-0.00690384,-0.0227534,-0.02369933,-0.05765366,-0.0324329,0.04338579,0.00653763,0.03439104,0.05619416,0.03684207,-0.01669812,-0.02865877,-0.05689102,-0.03178865,-0.00522074,-0.02743894,-0.01987048,-0.08224066,0.06123707,-0.04665278,0.04111928,-0.03079471,0.02649997,0.01001144,0.00518634,0.03589083,-0.04692422,0.11575404,0.03050687,-0.03514163,0.03012856,0.05426975,-0.04946192,-0.05532308,0.00653328,-0.00980974,0.03274029,0.04047969,0.09219286,0.04044672,0.07760461,0.00978677,0.04676515,-0.041972,0.01375353,0.00145934,0.01790143,-0.03326736,-0.01405491,0.03108951,0.04498983,-0.0217129,-0.32311895,0.02685855,-0.02242082,0.02631354,-0.02484949,0.03891202,-0.00122913,-0.02346388,-0.04262752,0.0050302,-0.02421479,0.01909501,0.05975202,-0.06437778,-0.03935705,-0.0100532,0.08541416,-0.02749888,0.02641714,0.00109297,-0.02045155,0.01260308,0.24044688,0.01673197,0.02162566,-0.0039518,0.00803198,-0.00418619,0.05494798,0.0586486,0.00891299,-0.00162047,0.09702232,0.01588366,-0.02273811,0.07970969,-0.05481225,-0.00846895,-0.02426553,0.0176762,-0.03789507,0.03633113,-0.03653536,-0.00160188,0.11895714,0.05820868,-0.01695903,-0.01474317,0.0049521,0.05797923,-0.02532162,-0.04268911,-0.0209747,-0.00525747,-0.01661847,0.05834457,0.00185154,-0.00059233,-0.02594432,0.01671665,0.02824308,0.00026729,-0.02411302,0.04749445,-0.01009776],"last_embed":{"hash":"14roczs","tokens":284}}},"text":null,"length":0,"last_read":{"hash":"14roczs","at":1753423722575},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>","lines":[20,69],"size":3186,"outlinks":[{"title":"_**，Cristiano Lopes 的程式貼**_","target":"https://saliu.com/programming.html","line":5},{"title":"Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.","target":"https://saliu.com/HLINE.gif","line":49}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11432717,-0.03243782,-0.03276683,-0.00257572,-0.02673827,0.01204307,0.00821044,0.00889714,0.04931509,0.02788789,0.02123633,-0.05774712,0.03365118,0.02352746,0.0417159,0.00549534,0.03192835,0.01141671,-0.04699266,-0.01331344,0.07953902,-0.06263731,-0.02946937,-0.05896991,0.07079381,0.03710241,0.003361,-0.01694964,0.02228398,-0.18903156,-0.01015159,0.01023873,0.03725504,0.02201029,-0.0282865,-0.01328658,-0.04231384,0.01045056,-0.04541093,0.0255124,0.06002206,0.01931617,0.02134134,-0.02045861,0.0228797,-0.08039035,-0.0139512,-0.01422523,0.00825648,0.01028113,-0.09087088,0.01370531,0.03173027,0.04757857,0.0358365,0.03363617,0.08701696,0.06651691,0.02209696,0.01321323,-0.01005589,0.06336966,-0.21211004,0.07421313,0.00123001,-0.02047587,-0.03950882,-0.02183746,0.03024183,0.05912002,-0.05534368,-0.01491978,-0.00982895,0.05810151,0.03556513,-0.06299569,0.00639422,-0.05855301,-0.05308093,-0.04495385,-0.03406671,-0.01844003,-0.02723129,-0.03645323,-0.00835758,0.08765363,-0.01999106,-0.02281265,0.02994972,-0.00505074,-0.01958373,-0.03333997,0.01961572,0.03452211,-0.04101923,-0.00618523,0.02690387,0.06887683,-0.01208693,0.10099424,0.01184673,-0.02599492,-0.00940543,-0.06640424,0.07276677,0.02218297,-0.00481307,-0.021935,-0.06798846,0.02296262,-0.04623139,-0.04388743,0.01553866,-0.05159093,-0.03231129,0.00014167,0.00546309,0.03950518,0.03420113,-0.03373432,-0.00866003,0.01173401,0.02571151,0.00640926,-0.00292522,-0.00336626,0.02322378,0.07625306,0.05284538,0.00886426,0.03562895,0.02701706,-0.09016258,-0.00904994,-0.02199431,0.00098401,-0.00340616,-0.02181536,0.00274222,-0.03728179,-0.01324102,-0.05758899,0.00553716,-0.09292423,-0.05472372,0.08585094,-0.01281803,-0.02119055,-0.01641703,-0.04178612,0.03892064,0.05098944,-0.01634435,-0.01229502,0.01603546,0.02444329,0.09091405,0.08220258,-0.01821886,0.00135341,-0.01695039,-0.07654192,-0.06102243,0.1932206,0.02727192,-0.05849424,-0.01924409,0.02980812,0.01456861,-0.09074977,0.04501165,0.01908251,-0.04241362,0.05845273,0.09569715,-0.03800276,-0.04405562,-0.05855289,-0.04262394,0.0309645,0.03043982,-0.01426199,-0.0444662,0.03593803,-0.02596119,-0.04895057,0.00815297,-0.015619,0.06129045,-0.03616731,-0.00921645,0.02301374,0.03458633,0.00580847,-0.0431289,-0.02863717,-0.02573061,-0.02570152,0.03941037,-0.08061223,0.11397238,-0.01960458,-0.05789021,0.00397714,-0.04632064,-0.02496411,0.00145777,-0.0039941,-0.00606951,0.06654815,-0.00160806,0.05620482,0.03086731,0.06681062,-0.01491829,-0.00616467,0.01031582,0.00089817,0.00953193,0.0483621,-0.01790316,-0.01752857,-0.1157646,-0.22265111,-0.01297013,0.00889755,-0.02192817,0.02482791,-0.01021899,-0.00889214,-0.02340756,0.11278506,0.00498806,0.0680485,-0.02769192,-0.06549144,-0.01714869,-0.02310375,-0.00052235,-0.00451646,-0.01074293,-0.00089206,0.01429702,0.01402306,0.07329564,-0.04049567,-0.01460066,0.04162944,-0.01948954,0.11980128,-0.00255925,-0.01125935,0.03691656,0.06438642,0.03773317,-0.01695571,-0.08293478,0.01786636,0.04871915,-0.07175735,0.00588221,-0.04098647,-0.07923264,0.04954394,0.04689251,0.01391079,-0.08646441,-0.0120112,-0.02764814,-0.0177198,-0.07327282,-0.03007474,0.04697431,0.01129252,0.04293707,0.03701182,0.03648592,-0.01779205,-0.03542555,-0.04481149,-0.03328227,-0.00496934,-0.02289959,-0.01380435,-0.06711263,0.059116,-0.03450021,0.02614996,-0.02710951,0.03073477,0.0050236,-0.01592474,0.03546543,-0.04564004,0.13480023,0.03660304,-0.03699058,0.04120835,0.03825623,-0.04798767,-0.05861764,0.01149397,-0.01536695,0.02228642,0.04341045,0.0798241,0.04331532,0.08062006,0.01303594,0.04472385,-0.02895453,0.02148068,0.01233213,0.0243545,-0.04001363,-0.01791242,0.02101357,0.05651796,-0.00841346,-0.31427833,0.03732137,-0.02273706,0.01941075,-0.02558886,0.04099788,0.01238768,-0.02654628,-0.03244295,0.01380053,-0.02034107,0.02682088,0.05857147,-0.06797523,-0.03593423,-0.02383995,0.07464672,-0.02939775,0.03879482,0.01164513,-0.02986025,0.00936592,0.25782293,0.01051305,0.03791974,-0.01676364,-0.0010553,-0.00968293,0.04709062,0.02840644,0.01084191,-0.00772274,0.08511113,0.02351351,-0.01403197,0.0618854,-0.05300983,-0.00838524,-0.01701781,0.01224276,-0.04168485,0.03583385,-0.05113855,0.00453983,0.11615993,0.07679718,-0.01390458,-0.02691287,0.00548741,0.0817151,-0.01966096,-0.04412531,-0.01328323,0.00607868,-0.01511506,0.04410449,0.00104032,-0.004899,-0.01029279,0.00988223,0.03039701,0.01425992,-0.01926826,0.06253531,0.00306102],"last_embed":{"hash":"olqovp","tokens":332}}},"text":null,"length":0,"last_read":{"hash":"olqovp","at":1753423722710},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{3}","lines":[26,52],"size":2002,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1155616,-0.0291597,-0.02778633,-0.001558,-0.02538529,0.00933072,0.002067,0.00168461,0.05648338,0.03064228,0.02270459,-0.06811182,0.03046055,0.02382792,0.0467304,0.00741841,0.03204488,0.01020222,-0.04559526,-0.01657066,0.08076321,-0.06499331,-0.02835101,-0.05929944,0.07069602,0.0315235,0.00194915,-0.01892044,0.01513519,-0.18048404,-0.0031752,0.01154402,0.03967012,0.02285884,-0.02711335,-0.0172312,-0.03222885,0.01137009,-0.05087206,0.02241278,0.05714861,0.02272537,0.02918843,-0.02185716,0.03046081,-0.07624321,-0.01009904,-0.01416445,0.01293946,0.00485943,-0.09687028,0.00613365,0.03422835,0.04039362,0.03931712,0.033838,0.07939329,0.06671565,0.01995836,0.00662906,0.00299474,0.06447991,-0.2150574,0.07002496,0.00566207,-0.0183842,-0.04420626,-0.02503937,0.03370289,0.06260467,-0.05054003,-0.01101751,-0.01056062,0.05238253,0.028453,-0.0661064,0.01440849,-0.04768102,-0.0598229,-0.04708802,-0.03792927,-0.00582027,-0.02920256,-0.0352473,-0.01222274,0.09099476,-0.01261351,-0.02066604,0.01221065,-0.00025021,-0.01663814,-0.03289017,0.01664557,0.04539593,-0.03905847,0.00463484,0.02639215,0.05876867,-0.02332396,0.10391902,0.01001616,-0.02434612,-0.01564307,-0.0632275,0.07340487,0.01704775,-0.00812778,-0.02310218,-0.06289275,0.02697018,-0.04374862,-0.04215423,0.02303715,-0.06010411,-0.03896895,0.00438754,0.00909737,0.03239062,0.02484985,-0.02599247,-0.01693012,0.01827143,0.02028443,0.00877735,-0.00243556,0.00095255,0.02531057,0.07498937,0.04989955,0.00644068,0.03413457,0.03007172,-0.08834441,-0.01072862,-0.02748417,-0.00271932,-0.00099198,-0.01946153,0.00677012,-0.03799925,-0.01759357,-0.06052283,0.0004728,-0.09658884,-0.04729359,0.08506492,-0.01161836,-0.02204235,-0.01722626,-0.0459367,0.039398,0.04588603,-0.01857087,-0.00591933,0.01236054,0.02865394,0.08279715,0.09053463,-0.02413503,-0.00125541,-0.01873495,-0.0739418,-0.05501157,0.19482742,0.04274893,-0.05872232,-0.02291982,0.02183166,0.01027416,-0.08887031,0.04384264,0.0182065,-0.04611109,0.0569015,0.09050354,-0.0450657,-0.0373105,-0.04907082,-0.03651289,0.02970113,0.01873313,-0.00889209,-0.04195766,0.03963236,-0.03984856,-0.05015394,0.01340556,-0.0141775,0.05662528,-0.03606226,-0.00774321,0.02517106,0.04263364,-0.00095979,-0.0440666,-0.0292054,-0.02816839,-0.02733997,0.04765025,-0.07816681,0.11693694,-0.01448955,-0.06107797,-0.01250429,-0.05378423,-0.02069601,-0.00315478,-0.00164739,-0.00684602,0.07296926,-0.00045626,0.06291538,0.02479936,0.06200694,-0.01523401,0.00098177,0.01585232,-0.00240015,0.00245625,0.04143646,-0.00935789,-0.01339887,-0.11380388,-0.22611345,-0.02561087,0.01439013,-0.02614349,0.02771197,-0.00505516,-0.01590875,-0.02048746,0.1196005,0.01342113,0.07786299,-0.02410129,-0.07150569,-0.00574931,-0.01758746,0.0002535,-0.00247358,-0.01303266,-0.00391196,0.0177659,0.01516852,0.07459885,-0.0374287,-0.00974696,0.04323075,-0.02154921,0.11296684,0.00363859,-0.0094056,0.03823927,0.06204294,0.03163485,-0.0103944,-0.08392408,0.01636433,0.05455627,-0.0746709,-0.00258949,-0.03582817,-0.07160217,0.04625063,0.0534732,0.01134614,-0.08852497,-0.02025812,-0.02554944,-0.02436655,-0.05648431,-0.03428453,0.03818626,0.00604198,0.03496132,0.03673245,0.02970631,-0.00509168,-0.0397421,-0.04190106,-0.04316892,-0.00756758,-0.01761262,-0.01728262,-0.07887893,0.06036756,-0.02537198,0.02180764,-0.02912446,0.02571536,0.00523558,-0.01538804,0.04462504,-0.05755819,0.13491565,0.03632896,-0.04071075,0.0431718,0.02596958,-0.04685281,-0.06136777,0.01473787,-0.01467792,0.0320662,0.0318923,0.0686911,0.04409588,0.08465429,0.01519128,0.04693681,-0.0211551,0.01826996,0.01056942,0.02210538,-0.03916109,-0.02422144,0.02435218,0.05884712,-0.01281901,-0.31930888,0.03763651,-0.01970681,0.01681828,-0.02763869,0.03449757,0.02430165,-0.01963495,-0.03252982,0.01390248,-0.01456393,0.04272885,0.05306548,-0.07715996,-0.02609606,-0.02388679,0.06294619,-0.02370712,0.0334149,0.01687252,-0.03397095,0.02043841,0.25303313,0.01016243,0.03986546,-0.00776636,0.00753668,-0.00829129,0.04463615,0.02947149,0.00495551,0.00164158,0.08372965,0.01550781,-0.01754774,0.05931853,-0.04075698,-0.00047097,-0.01716272,0.01291691,-0.04696818,0.03246726,-0.05491499,0.00268968,0.12223063,0.07054625,-0.01276129,-0.02694753,0.01020623,0.08831396,-0.01729219,-0.04974933,-0.01038255,0.00995139,-0.01480188,0.05168959,0.00672804,-0.00539436,-0.01272122,0.01508554,0.03798043,-0.00127663,-0.01885601,0.04989182,0.00413418],"last_embed":{"hash":"1qtjmqs","tokens":437}}},"text":null,"length":0,"last_read":{"hash":"1qtjmqs","at":1753423722818},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color=\"#ff0000\"><i>markov.exe</i></span></span></span></span></u>#{6}","lines":[56,61],"size":251,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1199728,-0.02326124,-0.01994948,-0.01486743,-0.01521933,0.00446524,0.00727221,0.0067988,0.05384726,0.01353346,0.01789964,-0.06171586,0.05073434,0.00295345,0.04606458,0.00401579,0.03100667,0.00266096,-0.05165212,-0.02478257,0.08931335,-0.07685024,-0.00417457,-0.0683063,0.07217099,0.04452118,0.01741944,-0.0276025,0.01950708,-0.17365374,-0.00067602,-0.01010975,0.03744877,0.02347032,-0.04643652,-0.00636843,-0.02461871,0.0005326,-0.04878448,0.02571771,0.04128895,0.02458836,0.03514777,-0.02426286,0.0323121,-0.08301482,-0.00830921,-0.03470785,0.01984836,0.00979973,-0.09606789,-0.00227246,0.0335494,0.0284421,0.04992889,0.03670741,0.06856845,0.05628784,0.04109006,0.00545796,-0.01060277,0.06596288,-0.216124,0.05338738,-0.01320333,-0.00171355,-0.03829355,-0.03177635,0.05325305,0.06216819,-0.04668196,0.00199656,-0.03695381,0.03953147,0.03729958,-0.05717956,0.03922781,-0.02850585,-0.06686457,-0.04326148,-0.02139074,0.01087298,-0.02510441,-0.03587472,-0.01364287,0.08181647,-0.01248057,-0.02452453,-0.00041874,0.00257905,-0.03184918,-0.02956562,0.01464622,0.05534821,-0.04701739,0.00211137,0.04485242,0.04091123,-0.02911504,0.0968979,0.01778414,-0.00868754,-0.02899944,-0.05613673,0.08273049,0.00014036,-0.00392978,-0.02455125,-0.05320783,0.02640026,-0.05194841,-0.02021562,0.02256449,-0.07740033,-0.03200193,0.02102916,0.01685911,0.03029748,0.02892913,-0.00380314,-0.00685698,0.01979079,0.02144567,0.01487091,0.00676144,0.00423913,0.02347717,0.05863788,0.0572256,-0.00825933,0.0454081,0.02380315,-0.0981005,-0.03165341,-0.03006686,0.01495784,-0.01032482,-0.04070998,0.01441142,-0.02990671,-0.00626354,-0.05412979,0.00695166,-0.09986282,-0.05777558,0.09666795,0.00542185,-0.03480452,-0.03220414,-0.05270198,0.03144798,0.04913254,-0.01027637,-0.00241335,0.03712717,0.00912176,0.0878728,0.11058949,-0.02725366,-0.01442072,-0.00981321,-0.07581489,-0.04571954,0.18363221,0.05238012,-0.04480769,-0.03282906,0.01000308,0.00223429,-0.09263214,0.0395582,0.00687557,-0.05286437,0.05112946,0.08776084,-0.04722146,-0.0187875,-0.0588235,-0.04515792,0.03342928,0.01044384,-0.01250386,-0.03362592,0.03301704,-0.04366992,-0.04273293,-0.00371716,-0.0251997,0.04318811,-0.0258698,-0.01414871,0.04716219,0.04576764,0.0129193,-0.0420159,-0.03036118,-0.03101324,-0.01927166,0.0211321,-0.08717246,0.10731295,-0.00207649,-0.06115754,-0.0197114,-0.05702373,-0.02955226,-0.00602798,0.00782444,-0.01394111,0.07220524,-0.00844038,0.06443319,0.03706374,0.04655455,-0.03174414,0.00307778,-0.00575044,-0.00310589,0.00594846,0.04854692,-0.01413989,-0.00236707,-0.11168513,-0.23235522,-0.0444115,0.00439619,-0.02438789,0.01335746,-0.02040936,0.00260277,-0.02365262,0.10267153,0.02076905,0.08082839,-0.02886835,-0.06875237,0.00468294,-0.01158441,0.01737706,-0.01480381,-0.0080955,-0.01383679,0.02411779,0.013943,0.08265394,-0.03837286,-0.00218459,0.02678283,-0.02562811,0.11426265,0.01174206,-0.00486143,0.04198823,0.06511688,0.05520345,-0.00506587,-0.07449778,0.00520815,0.03440451,-0.07256075,0.00696918,-0.03171228,-0.06345499,0.04497196,0.05661397,0.01788122,-0.09210882,-0.01890724,-0.02892714,-0.01611559,-0.06964181,-0.02398936,0.0308437,0.01458508,0.02604275,0.03510267,0.03541807,0.00139873,-0.04496489,-0.04500509,-0.0361303,-0.03089847,-0.03038233,-0.01323527,-0.06133654,0.05599652,-0.00980465,0.01088231,-0.01894855,0.02393609,0.02825435,-0.02569275,0.04229192,-0.03712752,0.12286595,0.02919326,-0.03750341,0.03553854,0.02284221,-0.06067826,-0.05014952,-0.00837583,-0.01592633,0.0512679,0.03678216,0.07394931,0.03696785,0.0959048,0.01099659,0.05613966,-0.00722707,0.02898155,0.00142481,0.01078395,-0.02360245,-0.03290952,0.00768641,0.06285112,0.00328082,-0.31973815,0.02563052,0.00346742,0.0223295,-0.02598422,0.02654886,0.02626638,-0.03755732,-0.0371109,0.01189915,-0.02788777,0.05180254,0.04762297,-0.07183582,-0.02025049,-0.00889325,0.05219173,-0.020013,0.03613487,-0.00468098,-0.03130598,0.02641619,0.2510868,0.00737184,0.04312357,-0.02314625,0.00071959,-0.00903067,0.03130545,0.01024275,0.00553342,0.00574231,0.08258966,0.02371789,0.00220066,0.0518406,-0.04520775,0.00462615,-0.00566475,0.01305254,-0.04314319,0.04057642,-0.06513657,-0.00296976,0.13139923,0.05964221,-0.01125614,-0.01957884,0.01971315,0.09065549,-0.0290348,-0.02452144,-0.01401742,0.01243381,-0.01471797,0.03646101,0.00867984,-0.00623926,-0.00809926,0.02320805,0.02773637,0.01945087,-0.01153693,0.062635,0.01318283],"last_embed":{"hash":"99g3z8","tokens":473}}},"text":null,"length":0,"last_read":{"hash":"99g3z8","at":1753423722966},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>","lines":[70,110],"size":3240,"outlinks":[{"title":"**「神奇網格**樂透策略」","target":"https://saliu.com/bbs/messages/9.html","line":8},{"title":"_**馬可夫鏈、追隨者、配對、彩票、樂透、軟體**_","target":"https://saliu.com/markov-chains-lottery.html","line":15},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":17},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":21},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":25},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":27},{"title":"**_markov.exe_ 樂透程式**","target":"https://saliu.com/freeware/markov.exe","line":29},{"title":"_**馬可夫彩票程式：幫助、說明**_","target":"https://saliu.com/freeware/markov-lotto-help.html","line":31},{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":33},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":40}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1152408,-0.01901264,-0.00942707,-0.00753259,-0.0153535,0.00125476,-0.00052737,0.01211149,0.04873264,0.01181983,0.01697146,-0.06249909,0.04041416,0.00431252,0.05074698,0.00011566,0.02503806,-0.00134159,-0.04213466,-0.01499548,0.09914421,-0.06278498,-0.0099468,-0.06622161,0.07995142,0.04215556,0.01216457,-0.02246998,0.02106172,-0.17462194,-0.00601171,0.00572468,0.0376488,0.03273895,-0.04230699,-0.00393087,-0.02662951,0.00357083,-0.0456763,0.03677304,0.05219073,0.01968996,0.01893546,-0.02190133,0.02769883,-0.07726561,-0.01930318,-0.025394,0.02192048,0.01195387,-0.08749212,0.00846644,0.03519968,0.03018665,0.03809115,0.03005792,0.07030834,0.06120187,0.03367124,0.01369773,-0.01498962,0.06405631,-0.22218648,0.0649213,-0.00096722,-0.01068164,-0.03942723,-0.02759551,0.0528424,0.06134393,-0.05718521,-0.00021754,-0.03159405,0.03823768,0.03295,-0.06032217,0.02290687,-0.03014934,-0.0610092,-0.03624476,-0.03204529,-0.00077217,-0.01820266,-0.03609619,-0.01900077,0.07841358,-0.02073697,-0.01878606,0.0167983,0.00113514,-0.03310264,-0.03533017,0.01287527,0.04506623,-0.05174516,0.00760613,0.04354126,0.05675576,-0.03211678,0.10206711,0.01199512,-0.0165965,-0.01924905,-0.06472386,0.07820623,0.0135629,-0.004486,-0.01907603,-0.05347423,0.02429713,-0.05779609,-0.02115346,0.01456925,-0.06932257,-0.02837441,0.01234052,0.01058538,0.03167323,0.03137422,-0.01727711,-0.01136027,0.02426302,0.00700418,0.0184064,0.00716763,0.00305328,0.01965419,0.05850825,0.0525369,-0.01189725,0.03518336,0.02645028,-0.10112913,-0.03070319,-0.02530427,0.01865248,-0.00606059,-0.02663681,0.01163416,-0.02193828,-0.00077077,-0.06170613,0.01075261,-0.09754402,-0.06073171,0.09705392,0.00212638,-0.03800129,-0.02851513,-0.04725197,0.03160607,0.05115805,-0.00835578,-0.01562308,0.03354219,0.00447238,0.09171296,0.10771585,-0.02855302,-0.01198704,-0.00934055,-0.07635926,-0.04876814,0.19523831,0.04138552,-0.04241247,-0.03578702,0.0122463,0.00231987,-0.09732394,0.04155334,0.00086698,-0.04477864,0.05624552,0.08786418,-0.04712329,-0.01616357,-0.06115941,-0.05947435,0.03364664,0.01202845,-0.01165828,-0.03479495,0.03958331,-0.02769444,-0.03685623,-0.00756223,-0.02537401,0.05041023,-0.02842848,-0.01908867,0.03783885,0.05321531,0.0176847,-0.04701474,-0.02865728,-0.02718274,-0.01847483,0.0252948,-0.08515553,0.11480804,-0.00435686,-0.06369262,-0.01267468,-0.04738371,-0.02829641,-0.00613191,0.00542874,-0.01315188,0.07716786,-0.00668666,0.06163115,0.03293777,0.04232601,-0.02446842,0.00020465,0.00078868,-0.00288523,0.00226632,0.05458063,-0.01101041,-0.00979425,-0.11803968,-0.23218259,-0.04362486,0.00071462,-0.02671869,0.01567069,-0.01249176,0.01186924,-0.0211099,0.10242306,0.01977398,0.07012045,-0.02644447,-0.07948165,-0.00627313,-0.01409876,-0.00197046,-0.00976765,-0.01544175,-0.01144413,0.01903435,0.01593897,0.07966748,-0.03473147,0.00245915,0.03001798,-0.02510482,0.11833648,0.0019831,0.00257841,0.04003625,0.07225213,0.05056636,-0.00481227,-0.0799575,0.00770377,0.03677067,-0.08358137,0.01930844,-0.03469058,-0.06219643,0.04653315,0.03966784,0.02098109,-0.09397557,-0.01495578,-0.02722509,-0.02283721,-0.07434308,-0.02141005,0.03480315,0.01831385,0.0306503,0.0403522,0.02969532,-0.00793678,-0.03628272,-0.04343029,-0.04053999,-0.0273542,-0.02931248,-0.0082206,-0.05886858,0.06209594,-0.01726432,0.01714799,-0.00821656,0.02373889,0.01853845,-0.02548641,0.04410673,-0.03684773,0.12643608,0.0241713,-0.03627682,0.0417385,0.03442979,-0.05129652,-0.051725,0.0012976,-0.01637319,0.04219506,0.03536661,0.07887722,0.04302336,0.0851907,0.02248117,0.05304199,-0.0156317,0.02181253,0.00180299,0.01304927,-0.03111837,-0.03031121,0.01488904,0.05447798,-0.00934665,-0.32007575,0.03264031,-0.00466638,0.01735087,-0.02473931,0.03230701,0.02251897,-0.04007468,-0.04489492,0.01946865,-0.02972669,0.03825754,0.05503032,-0.07508831,-0.02736012,-0.00692149,0.0628437,-0.02295423,0.04234538,-0.00276651,-0.03197945,0.0209144,0.2487435,0.01042201,0.03929029,-0.01861629,-0.00262092,-0.01279714,0.04178174,0.01371736,0.01273212,0.00401942,0.07372479,0.03757259,-0.00115824,0.0510245,-0.04943529,-0.00639815,-0.00450515,0.0175383,-0.03490144,0.03628225,-0.06080135,-0.00715788,0.11687179,0.06104235,-0.02286538,-0.01122668,0.02349995,0.08007085,-0.02645842,-0.02716731,-0.01637446,0.00515334,-0.01320318,0.03148232,0.00896813,-0.0054628,-0.00671795,0.01051602,0.03542944,0.03042912,-0.02004584,0.05834206,0.00950072],"last_embed":{"hash":"1sgl0hg","tokens":369}}},"text":null,"length":0,"last_read":{"hash":"1sgl0hg","at":1753423723136},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{1}","lines":[72,75],"size":204,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12028581,-0.02381968,-0.03113728,-0.01800271,-0.0263772,0.01078706,0.01316282,0.00553738,0.04821715,0.02201435,0.02251433,-0.0551852,0.05715689,0.00519733,0.04630749,0.00385408,0.02658782,0.01458775,-0.05687525,-0.01630791,0.07165948,-0.08558584,0.00490398,-0.05849209,0.07728699,0.05475232,0.0133931,-0.02907486,0.01674458,-0.17469674,0.00222892,-0.03216509,0.04210222,0.0172515,-0.05189006,-0.00636034,-0.02687343,-0.00215176,-0.03812061,0.02646191,0.02967724,0.03089969,0.03226422,-0.02326589,0.02394014,-0.07677332,0.00353244,-0.0476135,0.01479973,0.01653947,-0.10423986,-0.00289622,0.03299011,0.03848325,0.06188691,0.02731701,0.07179025,0.05936537,0.03470522,-0.00277013,-0.00280212,0.06559593,-0.20982341,0.05962726,-0.01294188,-0.00235834,-0.03381867,-0.03277721,0.04006308,0.0643483,-0.02398934,-0.00793182,-0.04348909,0.05212618,0.03725426,-0.0489953,0.03934784,-0.02597833,-0.04759269,-0.05570571,-0.02880298,0.00276267,-0.027069,-0.02978201,-0.01260845,0.07476767,-0.00136227,-0.02096909,-0.00863856,-0.00844459,-0.0270099,-0.03093451,0.0143127,0.05998509,-0.0446489,-0.00130363,0.037443,0.03524432,-0.02023914,0.09732623,0.0254504,-0.00345041,-0.0314826,-0.04653406,0.07538622,-0.00262761,-0.00146494,-0.04337002,-0.05655583,0.03488751,-0.04859461,-0.02288568,0.01410988,-0.0802699,-0.02957128,0.01171085,0.02586803,0.02362794,0.03508567,-0.00580409,-0.00835542,0.0110701,0.04058731,0.02476799,0.01241557,0.00564029,0.03549496,0.06709939,0.05366095,0.00619469,0.05013925,0.02689057,-0.10018504,-0.01836742,-0.0304356,-0.00070182,-0.00170455,-0.05878622,0.01047488,-0.03802146,-0.01261733,-0.04373374,0.00846849,-0.10728665,-0.04225348,0.1001769,0.01282102,-0.03071375,-0.03046841,-0.05246678,0.03960168,0.03861736,-0.01555052,0.00321679,0.0259543,0.01870466,0.08729461,0.08822871,-0.02844162,-0.00325748,-0.00467637,-0.08171029,-0.04300723,0.18860698,0.04967709,-0.04883412,-0.03055629,0.01211407,0.00570475,-0.08495743,0.0452451,0.0250184,-0.05390972,0.05168824,0.09492704,-0.04482511,-0.04584029,-0.05998673,-0.04245528,0.03406272,0.01396211,-0.01211638,-0.038736,0.03456287,-0.04435361,-0.03889281,0.00885594,-0.01924337,0.04244921,-0.01987859,-0.00308485,0.04688144,0.02383573,0.00563845,-0.02708931,-0.03649639,-0.04136677,-0.02284831,0.02739245,-0.07989588,0.09662876,0.00022423,-0.06170923,-0.01390114,-0.0655348,-0.03236965,-0.00509286,0.01498795,-0.00359629,0.06134342,-0.01309557,0.06254528,0.04028806,0.05059066,-0.02882915,-0.00639803,-0.00418051,-0.00452298,0.01867193,0.05131873,-0.02227576,-0.01133603,-0.09965364,-0.23646238,-0.0325674,0.0069851,-0.01760997,0.01388062,-0.03580972,-0.0024393,-0.03636583,0.10496756,-0.00464099,0.09725406,-0.02784873,-0.05524881,0.0037524,-0.01138644,0.02867113,-0.01683756,0.00032203,-0.00728849,0.03813172,0.00293311,0.07559434,-0.03608857,-0.0136962,0.03169436,-0.0236294,0.1085883,0.02134952,-0.01698535,0.0418818,0.05915082,0.06381988,-0.0009612,-0.07907522,-0.00323124,0.03132852,-0.06046878,-0.0103409,-0.02418903,-0.07230175,0.03802192,0.06170736,0.01928638,-0.09025702,-0.0124172,-0.02565822,-0.011404,-0.06736982,-0.02407548,0.02522716,0.02054564,0.0238633,0.02839029,0.03985539,-0.00396569,-0.04890317,-0.0454609,-0.01480693,-0.03711297,-0.02577101,-0.02820747,-0.05949456,0.04170945,-0.01624674,0.00408159,-0.02112609,0.03010124,0.02624447,-0.02140314,0.0349784,-0.03609582,0.10992945,0.02421004,-0.02615125,0.01182869,0.01769692,-0.06638515,-0.0518158,-0.02278832,-0.00407203,0.04001452,0.02775679,0.08062071,0.03965176,0.10170511,0.01873885,0.05273274,-0.01156253,0.03852027,0.00659688,0.0229334,-0.01693138,-0.02672081,-0.00492655,0.05935155,0.00427085,-0.31602907,0.02150045,-0.0125645,0.03384221,-0.02859627,0.02852786,0.02766349,-0.04427814,-0.03081455,0.01109666,-0.01425785,0.06483923,0.04438918,-0.06086934,-0.00956714,0.00298311,0.0455405,-0.03427323,0.02865365,-0.01450138,-0.01613971,0.02762093,0.25355604,0.00721281,0.04267037,-0.02116388,-0.00490905,-0.01747951,0.02049543,0.01585771,0.00046492,0.00099432,0.10987763,0.01451057,0.00241776,0.04294023,-0.04652535,0.01029605,-0.01432803,0.00190123,-0.0482861,0.02966175,-0.05117359,0.00662972,0.14799175,0.07001963,-0.00575921,-0.03371283,0.01150896,0.09744766,-0.04129359,-0.03425123,-0.02821669,0.02171061,-0.0085151,0.05316279,0.00785661,-0.01808705,-0.01837431,0.02715351,0.02116177,0.00174322,-0.00092546,0.04891402,0.00234905],"last_embed":{"hash":"icrzju","tokens":396}}},"text":null,"length":0,"last_read":{"hash":"icrzju","at":1753423723253},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{2}","lines":[76,76],"size":264,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11904729,-0.01552109,-0.02248454,-0.00780092,-0.02264402,0.00812837,-0.00245585,0.00330216,0.05301785,0.01674795,0.01539806,-0.06241648,0.03368606,0.00813169,0.06008269,0.00055465,0.0298429,0.00523914,-0.04666145,-0.01517208,0.07820476,-0.06208315,-0.00610589,-0.05155559,0.08234622,0.03898379,0.00873071,-0.01367016,0.02106544,-0.1786077,-0.0029316,-0.00796192,0.03190645,0.02907465,-0.04069069,-0.00833113,-0.02739081,0.003332,-0.04560598,0.03437455,0.05349027,0.02664504,0.01844993,-0.02084932,0.02514471,-0.08210501,-0.00811766,-0.03076031,0.02835977,0.01673897,-0.09405813,0.00438417,0.03164898,0.03638257,0.04330584,0.02047749,0.0770341,0.07022013,0.01003309,0.00799788,-0.00464402,0.06391149,-0.21536568,0.07681306,0.00395011,-0.01494409,-0.040946,-0.0279715,0.04358159,0.06214675,-0.05143837,-0.01541594,-0.02597699,0.05201908,0.0277331,-0.05372843,0.01930548,-0.0363802,-0.04167532,-0.05121883,-0.04730328,-0.00937964,-0.02294054,-0.03219445,-0.01682883,0.07322609,-0.0144253,-0.0068025,0.01589802,-0.01811453,-0.02981512,-0.03813447,0.01083636,0.04626858,-0.04823122,0.0067348,0.03226586,0.05324332,-0.03211555,0.0983967,0.01965804,-0.0117933,-0.02796113,-0.05793251,0.07541987,0.01716992,-0.00742523,-0.03127533,-0.05834974,0.0247792,-0.04837568,-0.02961585,0.01377708,-0.06602699,-0.03063229,0.00922074,0.01588483,0.02902715,0.0389003,-0.02804136,-0.01616666,0.01896248,0.02130132,0.02242711,-0.00029384,0.00135393,0.02824856,0.07007834,0.04555777,-0.00455401,0.0391847,0.02702732,-0.10528821,-0.02180415,-0.0329609,0.00580179,-0.00403694,-0.03315628,0.00640463,-0.02649777,-0.00945084,-0.06468464,0.00575247,-0.09723005,-0.05561406,0.0925771,0.00927256,-0.02870259,-0.03609974,-0.04020105,0.04410118,0.04773227,-0.0135064,-0.00528664,0.02615216,0.01142468,0.0925137,0.0881993,-0.03418015,0.00144682,0.00203503,-0.07590999,-0.04911865,0.19995683,0.04740471,-0.05395364,-0.0299839,0.01739565,0.00996782,-0.09427936,0.04419554,0.01678842,-0.0416283,0.04955377,0.08785302,-0.04831377,-0.0388241,-0.06141965,-0.06182409,0.032573,0.00690359,-0.01485539,-0.04148556,0.04559001,-0.02639708,-0.04490837,0.01100594,-0.02134246,0.05557284,-0.03011766,-0.01103035,0.0413309,0.04695472,0.01486805,-0.0351105,-0.03024768,-0.034046,-0.01767873,0.04099691,-0.07217389,0.10714222,-0.00130482,-0.06824124,-0.00951903,-0.05496219,-0.02734655,-0.01129798,0.01179447,-0.01236458,0.07471254,-0.00073035,0.06651025,0.03421821,0.04965656,-0.02388255,-0.00159868,0.00793516,-0.00357084,0.00406041,0.05222926,-0.01767937,-0.00859372,-0.10916656,-0.23068359,-0.02453652,0.00716951,-0.02800991,0.02083104,-0.02200643,-0.00217504,-0.02629051,0.10984164,-0.00035323,0.07874328,-0.02456862,-0.0698365,-0.00497278,-0.01254817,0.00319805,-0.0131543,-0.01038322,-0.00844079,0.03599718,0.0065484,0.07379374,-0.02504282,-0.00189191,0.03415068,-0.02866573,0.11309408,0.00089914,0.00081011,0.0486333,0.06882531,0.05102721,0.00286951,-0.09569108,0.00534211,0.03917684,-0.07280539,0.01899053,-0.03013643,-0.06498903,0.04367704,0.03857294,0.02240985,-0.09898672,-0.00451395,-0.0210442,-0.02415786,-0.07594418,-0.03018394,0.02263448,0.02181774,0.03568124,0.03002831,0.03054573,-0.00921581,-0.04391164,-0.04304864,-0.03404579,-0.02174188,-0.0240276,-0.02298252,-0.0564658,0.06132065,-0.02799728,0.0042309,-0.01593317,0.02689023,0.0078957,-0.02701387,0.03620996,-0.04627724,0.12399206,0.0268743,-0.02861775,0.03509624,0.03048684,-0.04440918,-0.05761333,0.00263421,-0.00156054,0.01873637,0.02075017,0.0779691,0.0437017,0.09637967,0.03560457,0.05131458,-0.02411398,0.02979017,0.00919582,0.02229926,-0.03436532,-0.02474158,0.01156348,0.05778613,-0.00840042,-0.31418088,0.031755,-0.02440866,0.01925466,-0.03053568,0.03532075,0.03283307,-0.04027044,-0.03512193,0.01842314,-0.01837452,0.0475913,0.05333398,-0.06476299,-0.01929482,-0.00605672,0.05769534,-0.03446196,0.04190966,-0.00851875,-0.02822359,0.02059581,0.25202373,0.01947077,0.03943395,-0.01866746,-0.00416572,-0.02270534,0.03811326,0.0249549,0.01724645,0.00296894,0.0783041,0.03546327,-0.00117493,0.05013262,-0.04899375,-0.00561125,-0.01744624,0.01768669,-0.04486794,0.02481018,-0.05587045,0.00227173,0.12164831,0.07460453,-0.02334206,-0.02195537,0.02230959,0.08339667,-0.02903444,-0.04190619,-0.01019356,0.0085401,-0.01057293,0.05024323,0.01972242,-0.01008024,-0.01004018,0.01087219,0.04282242,0.01513897,-0.02321435,0.04443595,0.00981531],"last_embed":{"hash":"1bicyv9","tokens":377}}},"text":null,"length":0,"last_read":{"hash":"1bicyv9","at":1753423723365},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{3}","lines":[77,77],"size":236,"outlinks":[{"title":"**「神奇網格**樂透策略」","target":"https://saliu.com/bbs/messages/9.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11629345,-0.01557646,-0.01414096,-0.01039644,-0.02656931,0.00252828,0.00257898,0.0121335,0.04501116,0.01331451,0.01658649,-0.05506607,0.03173938,0.00951467,0.05192613,-0.00082445,0.02810685,0.00842891,-0.04603598,-0.0094493,0.08729999,-0.06559929,-0.00307895,-0.06788,0.08345638,0.049339,0.00468673,-0.00835448,0.02368776,-0.17765966,-0.00961586,-0.00826614,0.03483577,0.03512512,-0.0406059,-0.01173462,-0.03669658,0.00055068,-0.04575657,0.03993081,0.04446704,0.01712911,0.01792616,-0.02036018,0.0290061,-0.07325298,-0.00682366,-0.03291551,0.03197213,0.01891694,-0.09991602,0.001885,0.02562047,0.03821926,0.03952715,0.01414052,0.07349063,0.06725249,0.01510217,0.00999971,-0.00435832,0.0624211,-0.21459344,0.0754779,0.00668309,-0.01954097,-0.03842198,-0.02524283,0.03910288,0.05823249,-0.04876241,-0.01178757,-0.02979569,0.05209172,0.03184743,-0.0548301,0.01115068,-0.03826376,-0.03683655,-0.04192353,-0.04705226,-0.0129833,-0.01867709,-0.03170767,-0.01549214,0.08141374,-0.01320737,-0.01050584,0.01979629,-0.00468501,-0.02969903,-0.03232045,0.00376666,0.04350684,-0.04114464,0.01691013,0.03184829,0.05435004,-0.02510592,0.10101201,0.01633484,-0.01337016,-0.02862067,-0.05513134,0.06757548,0.01628913,-0.00867225,-0.03257644,-0.0495827,0.01990011,-0.04005513,-0.02462541,0.01662059,-0.074093,-0.0379367,0.00964753,0.01238339,0.02425581,0.03385322,-0.03169201,-0.01339335,0.0171497,0.01478519,0.03235618,0.00931157,0.00490677,0.02527701,0.06712624,0.03865518,-0.00462244,0.03796404,0.01999447,-0.10483917,-0.01899736,-0.01941576,0.0046956,0.00289254,-0.03212613,0.00879872,-0.03211726,-0.01587554,-0.06314755,0.00866818,-0.09903131,-0.05371266,0.09787919,0.00636109,-0.03022622,-0.03571357,-0.0375506,0.03814076,0.04791994,-0.02016238,-0.00405353,0.0229891,0.01546378,0.09011693,0.08879703,-0.03457176,0.00629203,-0.00111722,-0.07888898,-0.04897892,0.1988176,0.03979877,-0.05323526,-0.03907619,0.02069127,0.01000687,-0.0982666,0.04124296,0.02136759,-0.0400218,0.05362137,0.09522554,-0.04794168,-0.04405221,-0.06429426,-0.06135174,0.02803993,0.00833384,-0.011277,-0.0428462,0.04771908,-0.02042433,-0.03498571,0.01377706,-0.02243762,0.05744263,-0.02586316,-0.01373083,0.03741879,0.0454415,0.02391562,-0.03024496,-0.02925675,-0.03560654,-0.02261703,0.04788369,-0.06870734,0.10851194,0.00422052,-0.06503821,-0.00742559,-0.04404424,-0.03086297,-0.01758163,0.00736279,-0.0122323,0.08069545,-0.0035323,0.06415002,0.02999232,0.05404828,-0.02544317,0.00006196,0.0094673,0.00381958,0.01035137,0.04956688,-0.01371198,-0.01791655,-0.09749715,-0.23767368,-0.02339089,0.00634518,-0.02419048,0.02032657,-0.01729296,-0.00398369,-0.03917084,0.10979043,-0.00685693,0.08742969,-0.01349482,-0.06977671,-0.00955617,-0.01574581,-0.00109811,-0.01707922,-0.01383935,-0.00982753,0.03995066,0.00771084,0.06475649,-0.02056729,-0.00214055,0.04578463,-0.02171834,0.12043735,-0.00594949,0.00784545,0.04748523,0.0667916,0.05905778,0.00241492,-0.09610093,0.00235263,0.04000274,-0.07943892,0.01336347,-0.03381472,-0.0696616,0.04126908,0.03937423,0.02401121,-0.09972636,-0.00733545,-0.02145829,-0.02360556,-0.0730934,-0.02154123,0.02411463,0.02312206,0.03599232,0.03606223,0.02590096,-0.01145805,-0.0430237,-0.04239188,-0.03116959,-0.02681896,-0.02977105,-0.02958235,-0.05922191,0.05748814,-0.02519898,0.00783937,-0.01309556,0.03521715,0.00915188,-0.02985503,0.02764546,-0.04395298,0.12663996,0.02734659,-0.03052812,0.02800813,0.02832127,-0.04263087,-0.05489355,0.00153458,0.00281885,0.01292637,0.02977688,0.07707334,0.05130154,0.08817095,0.03524054,0.04759227,-0.0235296,0.02102624,0.00431194,0.02436368,-0.03518929,-0.02316691,0.01289978,0.05101492,-0.01392616,-0.31632218,0.03280995,-0.02126035,0.01983438,-0.03351213,0.03038966,0.02723761,-0.04546097,-0.03325295,0.01555383,-0.02185523,0.04700054,0.05887382,-0.06099868,-0.01450475,0.00236951,0.0584713,-0.0381102,0.03932796,-0.0099593,-0.02153105,0.02932063,0.25014186,0.01030154,0.03498658,-0.01152855,-0.00014897,-0.02322274,0.04478615,0.02359547,0.0132028,-0.00566725,0.0777181,0.03800677,-0.00988005,0.04432688,-0.05647661,-0.01010638,-0.01447259,0.01846691,-0.04062386,0.02224809,-0.04497796,-0.00497103,0.12639715,0.07694346,-0.01740629,-0.02551001,0.0181177,0.0972174,-0.02238182,-0.04145486,-0.01238765,0.00496625,-0.01050678,0.04559105,0.01426459,-0.00685022,-0.01126434,0.00979485,0.03561398,0.01337103,-0.02692999,0.04218239,0.00635283],"last_embed":{"hash":"nzcofl","tokens":372}}},"text":null,"length":0,"last_read":{"hash":"nzcofl","at":1753423723484},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{7}","lines":[81,81],"size":218,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11744859,-0.04723032,-0.03708652,0.0072717,0.00119547,0.02257571,-0.01201861,0.03879382,0.04176621,0.01386223,0.016867,-0.02729351,0.0646365,0.00934123,0.03827754,-0.0220465,0.02717306,0.03748425,-0.05622548,-0.03317508,0.07474614,-0.06583046,-0.00123476,-0.056734,0.07791432,0.01934162,-0.02794147,-0.03066501,0.00487003,-0.20202571,0.02085967,-0.04217776,0.05315891,-0.00502269,-0.06432275,0.00810362,-0.00288468,0.00314588,-0.04978012,0.01440078,0.03337385,0.04138192,0.02894363,-0.04672394,0.00806417,-0.05886186,0.00234421,-0.01548981,-0.00170604,0.01766989,-0.08096733,-0.00353728,0.01666011,0.03140408,0.0735122,-0.00456188,0.06704392,0.0845179,0.00589169,0.01176072,0.0050123,0.09584539,-0.19416474,0.06874473,-0.01318852,-0.00015781,-0.02946958,-0.04010873,0.04214562,0.0486653,-0.02387815,0.01603559,-0.04926711,0.06008363,0.03760628,-0.05281192,0.02700204,-0.02217624,-0.0507891,-0.06352894,-0.03868018,-0.06390456,0.01044388,-0.06286791,-0.02770249,0.074914,0.00933144,-0.02431009,0.00871177,-0.04393401,-0.01523287,0.00725474,0.01697466,0.03012674,0.01878477,0.01329427,0.05221706,0.03531224,-0.00685404,0.11158156,-0.01863468,0.00822764,-0.01590897,-0.02692615,0.09893321,0.00142832,0.01063169,0.00386989,-0.03912191,0.06112934,-0.01496157,-0.01410234,0.00187378,-0.06025045,-0.04098368,0.0272102,0.04219904,0.02685446,0.01702497,0.01335956,-0.00832337,0.00786234,0.04241163,0.00089977,-0.0149663,-0.01111033,0.00957509,0.06321979,0.05106228,0.01055882,0.04267758,0.01672291,-0.1210098,0.01039379,-0.00296543,-0.02912947,-0.01851974,-0.04432087,-0.01456296,-0.00363794,-0.01769142,-0.01355351,0.01454631,-0.11048544,-0.04987986,0.08242545,0.01338524,-0.00499178,-0.02382684,-0.05334079,0.05179921,0.03741059,-0.0186888,-0.03188016,0.0047505,0.02135416,0.09874244,0.0705571,-0.03879961,-0.02311662,-0.02775296,-0.04660271,-0.03924767,0.16273421,-0.00457423,-0.06671051,-0.02366174,0.01305349,0.0056653,-0.09028307,0.05854047,0.03327471,-0.03704517,0.04753597,0.05448418,-0.06581432,-0.09356002,-0.07496156,-0.0285301,0.02924472,-0.01316617,-0.00950664,-0.01560779,0.01853809,-0.03621639,-0.04788381,0.01925298,0.00272529,0.04100914,0.00234679,0.01989523,0.0255316,0.03229798,0.00452827,-0.02035119,-0.04108689,-0.04487051,-0.02464809,0.03007711,-0.05350645,0.05464353,-0.00297806,-0.00488226,0.00599438,-0.0390164,-0.02227392,-0.00034769,-0.0034374,-0.01274862,0.07052527,-0.04361125,0.04017005,0.01903153,0.07407062,-0.0530386,0.01699769,0.01980811,0.03195595,0.01501796,0.0386141,-0.01744888,-0.0082796,-0.12103793,-0.21039894,-0.03869961,-0.01760779,0.01476565,0.07227726,-0.08726843,0.01905001,-0.04944931,0.08691327,0.01391568,0.06922679,-0.04537991,-0.03356893,0.03651562,0.02389235,0.03926108,-0.03909716,0.0019606,-0.00900122,0.04291782,-0.00453742,0.08012322,-0.02876156,-0.01973283,0.0470627,-0.02081425,0.11160239,0.0100163,0.00544311,0.06886568,0.04681411,0.0588766,-0.03681811,-0.08457046,-0.00178559,0.02920356,-0.04028521,-0.02189428,-0.00427637,-0.0257455,0.02228805,0.05019356,0.02109627,-0.14425977,-0.03091392,0.01037638,-0.02305662,-0.04341824,-0.04139023,0.01829422,0.01578026,0.05921911,0.0311248,0.03322314,-0.00720323,-0.02879755,-0.06508442,-0.02620821,0.00126321,-0.0008743,-0.02306157,-0.0785817,0.03698757,0.00121866,0.03070685,-0.02064874,0.00299616,-0.01292563,-0.01212831,0.03863426,-0.02910663,0.12751395,0.00467014,-0.00332439,0.02620924,0.03558405,-0.03768884,-0.05117796,-0.03191427,-0.02030536,0.0463866,0.00391025,0.07719899,0.05090029,0.08128051,0.00544116,0.06790248,0.00857891,-0.00356057,-0.01959357,0.04095383,-0.03163327,-0.04232143,-0.00161651,0.03780827,0.00428659,-0.30646008,-0.00365428,-0.0461223,0.0452025,-0.0567156,0.008692,0.02525966,-0.0496151,0.00188034,-0.00183961,-0.01745352,0.07472263,0.0554723,-0.0719317,-0.02642359,-0.02158208,0.03430409,-0.05851184,0.0641479,-0.00604582,0.01932734,-0.01135857,0.25346404,-0.01063866,0.04170886,0.0040532,-0.0090281,-0.03327872,0.02340457,0.0114901,-0.01695453,0.00324504,0.07767913,0.02429717,-0.00948191,0.05142353,-0.03736928,0.0073202,-0.04819144,-0.01418427,-0.03347902,0.02076331,-0.03105075,0.01140513,0.14767334,0.04042085,0.01986557,-0.05676581,0.03231525,0.09605083,-0.03483965,-0.05205887,-0.03492003,0.0385197,-0.00620188,0.04214388,-0.00479698,-0.00962373,-0.02948918,0.00008414,0.04322917,0.02244625,0.01432865,0.07130028,0.01073862],"last_embed":{"hash":"1miqkai","tokens":447}}},"text":null,"length":0,"last_read":{"hash":"1miqkai","at":1753423723587},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{11}","lines":[86,97],"size":705,"outlinks":[{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsLottery.gif","line":1},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":5},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairs.gif","line":9},{"title":"Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.","target":"https://saliu.com/images/MarkovChainsPairsHotNumbers.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13643691,-0.02090218,-0.03118363,-0.01709633,-0.02978135,0.02373966,-0.01630501,-0.00790548,0.06641648,0.00525146,0.01220089,-0.0476252,0.05452616,0.0090259,0.0474962,0.01010627,0.02446358,0.00611126,-0.05204587,-0.01395005,0.08129422,-0.07269318,-0.00395601,-0.07752655,0.06423245,0.03574259,-0.00195817,-0.02565093,-0.00860103,-0.17872691,-0.00097757,-0.01285441,0.04234681,0.02791683,-0.04120421,-0.02619838,-0.0200049,0.01742073,-0.05460183,0.02804242,0.02030705,0.02163126,0.02361804,-0.02546025,0.03379049,-0.07576816,-0.0198809,-0.02516937,0.02410774,0.00820247,-0.09866407,0.0129242,0.0302167,0.04999243,0.04224623,0.02123539,0.04412422,0.06447723,0.03943139,0.01586445,0.00432147,0.04979744,-0.22011001,0.06447496,0.01667666,-0.01779595,-0.03215418,-0.02659984,0.03970074,0.05939929,-0.04122742,-0.00031839,-0.03310387,0.04560304,0.0307241,-0.06305917,0.00060571,-0.02088679,-0.03939981,-0.03351801,-0.04121127,0.0032942,-0.00651984,-0.03029253,-0.03981648,0.07094114,0.01546597,0.00590165,0.0077267,-0.03596959,-0.03275248,-0.01809984,0.01525666,0.03789894,-0.02409962,0.00434295,0.04407513,0.05160609,-0.02514179,0.09910962,0.00680127,-0.00774968,-0.03065688,-0.04906481,0.06983931,0.00110428,-0.0155175,-0.03377899,-0.07211777,0.03350247,-0.03788669,-0.01546622,0.03266289,-0.06578333,-0.03564254,-0.00636657,0.01074087,0.03006899,0.01990581,-0.01379898,-0.02649454,0.00854429,0.0341112,0.01163432,0.01105549,0.00404134,0.01027888,0.04716923,0.06760376,0.00422978,0.03819362,0.01008446,-0.10192909,-0.01048204,-0.01322611,-0.00186678,-0.01082083,-0.03370785,0.00851554,-0.0455649,-0.0143713,-0.05332,0.02631241,-0.10370687,-0.03185317,0.11580405,0.01167318,-0.02320937,-0.02666463,-0.03881129,0.05364309,0.04467782,-0.03053377,-0.02836434,0.01585671,-0.00080469,0.10426005,0.09036003,-0.02274148,0.00366366,0.01957419,-0.07602737,-0.05322771,0.17412873,0.03304336,-0.06155282,-0.02609806,0.01281943,-0.00467109,-0.07690583,0.04051765,0.02558247,-0.04870538,0.06248001,0.08299615,-0.04862041,-0.05763678,-0.05254212,-0.04533195,0.02944762,0.00038121,0.00753982,-0.04254872,0.02001416,-0.01163898,-0.04155325,0.01504261,-0.0276702,0.05520973,-0.02352585,-0.0132396,0.0190061,0.0366676,0.01378516,-0.02378191,-0.0337848,-0.03605231,-0.01770925,0.04282209,-0.0560145,0.08420499,-0.01048386,-0.06423099,-0.01757249,-0.06031378,-0.01064227,-0.02284112,0.00064352,0.01960077,0.07503236,-0.00558681,0.04627886,0.028772,0.07454442,-0.02916287,-0.00794625,-0.00279699,0.00610498,0.01500781,0.05215549,-0.03298161,0.00037285,-0.12420622,-0.2208281,-0.02742272,-0.00873271,-0.01512927,0.014985,-0.01784511,-0.01203518,-0.02149617,0.0950222,0.02313512,0.0878571,-0.03092585,-0.04702488,-0.01264203,-0.0133581,0.01632035,-0.03441655,-0.00541602,-0.01214378,0.02558126,0.00985194,0.08602028,-0.04134179,-0.01037506,0.03310971,-0.02182351,0.11190944,0.01653322,0.00572458,0.05673639,0.06456356,0.05616003,-0.00198474,-0.09021991,-0.01009608,0.04694077,-0.07686006,0.01014657,-0.02730162,-0.06188701,0.01606617,0.03000843,0.03085555,-0.09537566,-0.01186038,-0.02219833,-0.00797474,-0.05441998,-0.0237934,0.04425003,0.0093589,0.0528011,0.03329859,0.02798452,-0.02244836,-0.03922421,-0.01973247,-0.02965751,-0.00491343,-0.02916542,0.00027191,-0.08132573,0.05036978,-0.02169354,0.00303531,-0.02693574,0.01817728,0.00693308,-0.00964115,0.02641838,-0.03602152,0.12158162,0.02839721,-0.02318295,0.03329241,0.0354143,-0.04399498,-0.05848101,0.00041074,-0.02604569,0.01961886,0.01908598,0.0759901,0.05360613,0.08073136,0.01128786,0.0594123,0.02773953,0.01931466,0.01825226,0.03497398,-0.02471506,-0.04228863,0.01935079,0.05399334,-0.00047152,-0.31746233,0.04258326,-0.01244911,0.02820557,-0.03265898,0.00462397,0.03148094,-0.0406742,-0.01951014,-0.00909994,-0.01283686,0.04688186,0.04661155,-0.08580471,-0.02134858,-0.02616533,0.03954374,-0.02152726,0.06290402,0.00840311,-0.00262735,0.00406914,0.27965945,0.02077797,0.04991759,0.00285921,0.00491505,-0.00468993,0.0530332,0.01782236,0.01210229,-0.01202232,0.08939706,0.00972468,-0.00129682,0.03208807,-0.05228795,0.00964357,-0.02186958,0.01813714,-0.03159994,0.03001411,-0.05886643,0.00175937,0.13858595,0.05880564,-0.0045186,-0.03959908,0.02386103,0.10321166,-0.03614733,-0.04949473,-0.0194761,0.00912194,-0.00200677,0.03710927,0.00222261,-0.04187909,-0.0128994,0.0046382,0.03969,0.00888866,-0.003158,0.06797031,0.02422639],"last_embed":{"hash":"1wobi4l","tokens":487}}},"text":null,"length":0,"last_read":{"hash":"1wobi4l","at":1753423723729},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>#{15}","lines":[102,110],"size":609,"outlinks":[{"title":"Markov-chains software algorithms should consider pair of lotto numbers instead of followers.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.","target":"https://saliu.com/HLINE.gif","line":8}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14412884,-0.03035252,-0.0326147,0.00125825,-0.04097006,0.02488539,0.01615256,0.01262428,0.05766618,0.02431759,0.00182408,-0.04898375,0.0386382,0.0235537,0.03668166,0.01876019,0.00113205,0.01226086,-0.0341656,0.00177009,0.07128059,-0.06714799,-0.01005839,-0.0865787,0.08230148,0.04323796,0.01191372,-0.02642467,0.00887298,-0.19480434,-0.00854549,-0.01165723,0.01300265,-0.00175311,-0.05183181,-0.01088458,-0.02489634,0.02083211,-0.02807177,0.0303272,0.04809972,0.03482565,-0.00030314,-0.01944092,0.03385737,-0.07054324,-0.01179895,-0.02311571,0.00094081,0.01743042,-0.09315649,-0.00027905,0.03787483,0.0326933,0.03745442,-0.00178486,0.05619161,0.075386,0.02244013,0.03542567,-0.03043592,0.06761152,-0.2384055,0.05477232,0.01038664,-0.02570517,-0.04815905,-0.02442358,0.0544632,0.0518739,-0.0412476,0.00260912,-0.01472332,0.0682941,0.02550092,-0.04535504,0.01004455,-0.06405587,-0.04310377,-0.03407696,-0.03891359,-0.00150935,-0.04072915,-0.0427301,-0.01827836,0.07767569,0.01061903,-0.00786568,0.00583115,-0.00640349,-0.02016199,-0.00469955,0.01280087,0.05364466,-0.0213818,0.03237307,0.05571462,0.03630324,-0.00071484,0.0974893,0.01528472,0.00302815,-0.0299211,-0.02856652,0.06877685,0.00801353,-0.0161401,-0.02998207,-0.05530716,0.02245401,-0.0281169,-0.02092655,0.02933652,-0.05740953,-0.05368082,0.01306581,0.01083108,0.0305876,0.02045284,-0.0305065,-0.0340292,0.0123207,0.02214674,0.035635,-0.008423,-0.0032165,0.02555562,0.06987331,0.04852315,0.00295695,0.05047505,0.00210936,-0.11070212,-0.01421383,-0.015144,0.00406184,-0.00267004,-0.03326073,-0.00564112,-0.0291223,-0.0057902,-0.05343973,0.02198164,-0.0961021,-0.04900653,0.08979484,-0.00696141,-0.02647838,0.00050884,-0.05180167,0.03413996,0.03027127,-0.01882879,-0.00119343,0.03068938,-0.00090275,0.08788013,0.09029064,-0.01215798,-0.01034766,-0.00004331,-0.08094218,-0.04946938,0.20022683,0.03097861,-0.06014581,-0.03875679,0.04017333,0.01466632,-0.10522774,0.03675428,0.02565553,-0.02791809,0.05257573,0.07836276,-0.04578761,-0.03451303,-0.059786,-0.03486473,0.0342776,0.01531928,-0.02953615,-0.04681804,0.03005538,-0.03977738,-0.05285468,0.01799856,-0.02917307,0.06494529,-0.0109746,-0.0102871,0.00741822,0.01684068,0.00523944,-0.0411313,-0.04153775,-0.0598003,-0.02609275,0.04757417,-0.05368054,0.06359766,-0.01139953,-0.06399425,-0.0117241,-0.06530631,-0.01643533,-0.00463595,-0.01071042,-0.01638297,0.0507407,0.00404156,0.0485466,0.02619077,0.05953137,-0.00529841,0.01330839,0.01428015,0.00779803,0.01267005,0.04440023,-0.01630809,0.00012191,-0.09564244,-0.20845224,-0.03363536,-0.01173478,-0.03474509,0.01308002,-0.0046443,-0.011186,-0.0381347,0.11149592,0.02814325,0.10016413,-0.03898554,-0.06763298,-0.00259961,-0.00830132,-0.00356999,-0.04751682,-0.01739058,-0.02067309,0.02888038,0.00269036,0.07007103,-0.04740943,-0.04657635,0.02787641,-0.02624494,0.11721323,0.01046458,-0.01637292,0.03491434,0.06316248,0.03959703,-0.00641478,-0.06932084,0.03117358,0.03491696,-0.0765411,0.00890116,-0.04693465,-0.06712517,0.01892357,0.03658089,0.04672734,-0.08770394,-0.00904752,-0.03449164,-0.01827613,-0.05000834,-0.0036911,0.04098922,0.02935538,0.03233608,0.04326824,0.02597558,-0.00777753,-0.03040754,-0.02764034,-0.02719757,-0.01535433,-0.01391437,-0.01873604,-0.05421086,0.06089728,-0.03445317,0.0175148,-0.00960991,0.03204558,-0.01873478,-0.01973408,0.04473358,-0.04033016,0.13217282,0.02783368,-0.02993726,0.04215537,0.02447179,-0.04185731,-0.04806105,0.0157142,0.00173528,0.02751096,0.00434499,0.07835569,0.06936902,0.08950317,0.0088647,0.06193651,-0.01564774,0.03480257,0.01572386,0.01880528,-0.02923117,-0.03698693,0.02273729,0.04047387,0.01547201,-0.32017964,0.04754956,-0.04135741,0.03240922,-0.00029948,0.01737457,0.04858433,-0.04843403,-0.03890806,0.0091267,0.00972297,0.04595707,0.06440374,-0.05781785,-0.02273952,-0.00365865,0.03818629,-0.02161283,0.04044408,0.00495613,-0.01452313,0.03868369,0.25228584,0.00456529,0.02746417,-0.0096975,0.00818981,-0.01364919,0.04609659,0.01764869,-0.00217981,0.00329087,0.08992584,0.00539528,-0.00214028,0.05538484,-0.0421314,-0.0129965,-0.02055009,-0.00661357,-0.04216667,0.02001948,-0.03016568,0.00533016,0.15369494,0.07444586,-0.01363159,-0.03383637,0.02932069,0.08117792,-0.0349455,-0.04113508,-0.01653177,-0.00290935,-0.01248824,0.04064985,0.02247877,-0.01065454,-0.02811995,0.01037565,0.02589246,0.02004332,0.00343591,0.05128456,0.00449617],"last_embed":{"hash":"adgy0h","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"adgy0h","at":1753423723895},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>","lines":[111,134],"size":1867,"outlinks":[{"title":"**機率論**","target":"https://saliu.com/theory-of-probability.html","line":3},{"title":"軟體","target":"https://saliu.com/infodown.html","line":4},{"title":"_**賭博的基本公式**_","target":"https://saliu.com/Saliu2.htm","line":5},{"title":"_**賭博 基本 公式**_的 數學","target":"https://saliu.com/formula.htm","line":6},{"title":"_**最佳賭場賭博系統：二十一點、輪盤、有限馬丁格爾投注、累進投注**_","target":"https://saliu.com/occult-science-gambling.html","line":7},{"title":"_**軟體，公式計算樂透賠率，超幾何分佈機率**_","target":"https://saliu.com/oddslotto.html","line":8},{"title":"_**線上機率、賠率計算器**_","target":"https://saliu.com/online_odds.html","line":9},{"title":"_**軟體、計算樂透賠率的公式、超幾何分佈機率**_","target":"https://saliu.com/bbs/messages/266.html","line":10},{"title":"_**標準差、高斯、常態、二項分佈、分佈**_","target":"https://saliu.com/formula.html","line":11},{"title":"_**樂透**_**<u>奇蹟網格 </u>** 、 _**超級樂透策略、系統**_ 。","target":"https://saliu.com/bbs/messages/grid.html","line":13},{"title":"_**隨機性、隨機性程度、確定性程度**_ 。","target":"https://saliu.com/bbs/messages/683.html","line":14},{"title":"_**CoolRevGui**_ ： _最終檔案反轉器、隨機播放器、文字檢視器軟體_","target":"https://saliu.com/programming.html","line":16},{"title":"_**UPDOWN**_ ： _彩票結果、文字檔案中的逆序_","target":"https://saliu.com/bbs/messages/539.html","line":17},{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":19},{"title":"論壇","target":"https://forums.saliu.com/","line":21},{"title":"新文章","target":"https://saliu.com/bbs/index.html","line":21},{"title":"賠率產生器","target":"https://saliu.com/calculator_generator.html","line":21},{"title":"目錄","target":"https://saliu.com/content/index.html","line":21},{"title":"基本公式","target":"https://saliu.com/formula.htm","line":21},{"title":"首頁","target":"https://saliu.com/index.htm","line":21},{"title":"搜尋","target":"https://saliu.com/Search.htm","line":21},{"title":"網站地圖","target":"https://saliu.com/sitemap/index.html","line":21},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{16}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13632046,-0.02791457,-0.04373484,-0.01685471,-0.02590306,0.05255825,-0.00505317,0.00993748,0.05084283,0.01948095,0.00405728,-0.0475097,0.03444511,0.00866933,0.03223954,-0.00862574,0.00691368,0.01766245,-0.03448698,-0.01656136,0.09030984,-0.05742203,-0.02642561,-0.06942691,0.09451643,0.03506603,-0.00357534,-0.03213757,0.00189651,-0.19332962,0.0179399,-0.011911,0.02644776,0.00762882,-0.06257001,-0.00899787,-0.0279741,0.03791187,-0.0483628,0.03437662,0.04298884,0.02516202,0.02723315,-0.01786475,0.040324,-0.06330238,-0.00992043,-0.01663813,0.02445189,0.00551962,-0.10039889,0.01781379,0.03614167,0.02778183,0.07661516,-0.00643776,0.07243432,0.08576971,0.01587576,0.0112354,0.0039961,0.07612907,-0.22673209,0.07141794,0.00313544,-0.00479666,-0.02425085,-0.00589013,0.03792984,0.06740542,-0.02317532,-0.00575543,-0.02637989,0.05806836,0.03358041,-0.05337681,-0.01570971,-0.04292704,-0.03721941,-0.03168153,-0.05656751,-0.0252875,-0.01377406,-0.050602,-0.02596692,0.09990288,0.01153821,0.00233213,0.01208473,-0.0151277,0.00115028,0.01401771,0.01255197,0.04699706,-0.03235492,0.02402838,0.03837828,0.02499119,-0.00261566,0.1087401,0.03053292,-0.01027583,-0.02836722,-0.02984509,0.07873753,0.00649323,-0.01844603,-0.03153364,-0.05095776,0.02391309,-0.02976092,-0.0131021,0.04148925,-0.06813436,-0.05283147,0.00696953,-0.00282338,0.03153045,0.04014415,-0.01540272,-0.02009442,0.0269932,0.01541834,0.03337323,-0.01803814,0.01627895,0.00242461,0.0698081,0.05713103,0.0055453,0.03424504,0.023572,-0.12799284,-0.0136951,-0.00768582,-0.00518989,-0.00956705,-0.03185648,-0.00316843,-0.00802939,-0.01811742,-0.05747997,0.01030291,-0.10264093,-0.04875138,0.08193026,-0.00394113,-0.02627387,-0.0221652,-0.02432865,0.03545903,0.02484741,-0.03164581,-0.01315546,0.00832126,0.00540964,0.09835757,0.07369556,-0.03081208,-0.02734889,-0.02659038,-0.07398149,-0.04983062,0.18190914,0.02780582,-0.06156853,-0.02556802,0.0266835,0.00457696,-0.11456869,0.04577119,0.02041118,-0.05284208,0.05317732,0.08391288,-0.05571215,-0.04366564,-0.06529925,-0.04318724,0.0294249,0.00033093,-0.02821724,-0.03574709,0.02065969,-0.03740773,-0.05803978,0.02241033,-0.03290873,0.04324417,-0.01703503,-0.00230715,0.00782795,0.00385712,0.00368473,-0.03405093,-0.04609033,-0.05060894,-0.03654033,0.06692042,-0.05907834,0.06922826,0.00442739,-0.04193795,-0.01001776,-0.05617649,-0.01031146,-0.01587299,-0.02905935,0.01680598,0.06178886,-0.00566893,0.04745502,0.03448397,0.06471584,-0.02665413,0.01615718,0.02870322,-0.00109957,0.00817318,0.04483493,-0.01723324,0.003325,-0.10096754,-0.20467761,-0.03059258,-0.01402843,-0.01808043,0.02932717,-0.00891669,-0.00741956,-0.04030763,0.09743924,0.02335153,0.092151,-0.03980829,-0.06378007,0.01776475,0.00725165,-0.00090679,-0.05535291,-0.00556314,-0.01076857,0.0466168,0.00152838,0.06080871,-0.05429117,-0.03475863,0.04306428,-0.02971379,0.12670811,0.02632348,-0.02096964,0.03604385,0.0728977,0.03085539,-0.01806158,-0.07172466,0.01192564,0.02984334,-0.07197706,0.01383145,-0.04127636,-0.04598357,0.01742146,0.02913429,0.02349154,-0.07742682,-0.01802048,-0.01294669,-0.03735258,-0.04549442,-0.00684629,0.04600203,0.00051771,0.05712274,0.03874267,0.02101604,0.00832786,-0.03797247,-0.04924152,-0.03895089,-0.00805582,-0.00117772,-0.0287262,-0.07504647,0.06489301,-0.01368402,0.03318294,-0.01484678,0.02222269,-0.01971221,0.00677633,0.02963319,-0.04182546,0.10173514,0.04348215,-0.00936367,0.03507771,0.0186908,-0.02416879,-0.06037343,0.01507446,-0.01500356,-0.00041987,-0.01560636,0.0708962,0.06776075,0.08786787,0.02986831,0.06450656,0.0077878,0.02680218,0.02913837,0.03024487,-0.02546155,-0.01322659,0.03665581,0.02651971,0.00762006,-0.31027314,0.03498917,-0.01789874,0.04709565,-0.02084534,0.00912585,0.03673712,-0.02132146,-0.03638086,-0.02606845,0.00510068,0.0378118,0.07153077,-0.06945986,-0.02793323,-0.03112524,0.01851206,-0.04088673,0.06834443,0.00848756,0.00568794,0.02466446,0.25818631,-0.0119346,0.0395617,-0.01447647,0.00923923,-0.01681991,0.04794156,0.02197482,-0.00156303,0.00929383,0.08106854,0.02294932,0.00290098,0.0368159,-0.04578749,-0.00699999,-0.02944653,0.0013515,-0.0478569,0.01689979,-0.04408687,-0.00223063,0.13884369,0.0809445,-0.00531066,-0.0621888,0.02753183,0.08671065,-0.03536227,-0.05726099,-0.0096557,-0.01149065,-0.01985563,0.03750137,0.01674352,-0.01627907,-0.01733378,0.01293533,0.02867152,0.01340399,0.01772276,0.04410616,-0.0064931],"last_embed":{"hash":"yfe9y8","tokens":423}}},"text":null,"length":0,"last_read":{"hash":"yfe9y8","at":1753423724053},"key":"notes/saliu/馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program.md#馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program#<u><span lang=\"zh-TW\"><span data-immersive-translate-translation-element-mark=\"1\"><span data-immersive-translate-translation-element-mark=\"1\">機率論、數學、統計學、組合學資源</span></span></span></u>#{16}","lines":[129,134],"size":588,"outlinks":[{"title":"The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"論壇","target":"https://forums.saliu.com/","line":3},{"title":"新文章","target":"https://saliu.com/bbs/index.html","line":3},{"title":"賠率產生器","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"目錄","target":"https://saliu.com/content/index.html","line":3},{"title":"基本公式","target":"https://saliu.com/formula.htm","line":3},{"title":"首頁","target":"https://saliu.com/index.htm","line":3},{"title":"搜尋","target":"https://saliu.com/Search.htm","line":3},{"title":"網站地圖","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
