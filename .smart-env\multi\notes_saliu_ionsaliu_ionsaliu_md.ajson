
"smart_sources:notes/saliu/ionsaliu_ionsaliu.md": {"path":"notes/saliu/ionsaliu_ionsaliu.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1evbnnu","at":1753230880448},"class_name":"SmartSource","last_import":{"mtime":1735897996000,"size":12036,"at":1753230880657,"hash":"1evbnnu"},"blocks":{"##實現逆向策略的相關功能。首先讓我們查看原始文件的核心概念，然後實現相應的函數：":[1,377],"##實現逆向策略的相關功能。首先讓我們查看原始文件的核心概念，然後實現相應的函數：#{1}":[3,377],"#---frontmatter---":[207,375]},"outlinks":[]},