"smart_sources:根據目前代碼現況，我認為應該首先實現以下部分.md": {"path":"根據目前代碼現況，我認為應該首先實現以下部分.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13078262,-0.01295399,0.03132052,-0.06799299,0.01087084,0.05949418,-0.02152888,-0.00034866,0.06978507,-0.01580524,-0.01483595,-0.02998102,0.07448574,0.05333171,0.00657474,-0.00618739,0.01251088,-0.01353959,-0.04453732,-0.04742106,0.06977795,-0.05668921,0.00266374,-0.05700511,0.03473546,0.04205846,-0.03616612,-0.02501421,0.02138248,-0.21214028,0.01241342,0.04452564,0.01129677,-0.003257,-0.02706701,-0.0843945,-0.05825805,0.0612848,-0.03324224,-0.0002966,-0.00623163,0.0558994,0.02607156,-0.0073338,0.0241973,-0.05015216,-0.06413592,0.00249586,-0.00746577,-0.03777508,-0.07010558,0.00574098,-0.00135955,-0.01338406,0.02407113,0.02937453,0.05465541,0.08830928,0.01597363,0.04819863,0.02025767,0.0431867,-0.16341838,0.05778559,-0.02427913,0.00675079,-0.02401539,0.00190699,0.06715729,0.11539779,-0.0472298,0.04237036,-0.00667617,0.00810771,-0.00548985,-0.04857481,-0.00621237,-0.04285502,-0.02608322,-0.02184714,-0.03529134,0.02153843,-0.0129767,0.01748177,0.03242183,-0.00114869,0.02967696,0.00394624,0.0379652,0.01899884,0.04400256,-0.01002256,-0.00823363,0.02777272,-0.02512326,0.0232556,0.01273989,0.00550999,-0.04611412,0.12793708,-0.02830797,-0.0040553,-0.04066886,0.00050107,0.03574092,-0.06231406,-0.01427028,-0.01278581,-0.03649249,-0.04700893,-0.01534052,-0.01663978,0.06680205,-0.02505375,0.01452741,0.07666039,0.01716052,-0.00563037,0.00802023,0.02638863,0.02798833,0.00536835,0.02979649,-0.05436748,-0.0265272,0.01339378,0.02078489,0.01636717,0.04304216,0.03066123,0.05067517,0.05027263,-0.08458184,-0.003844,-0.04464037,-0.03663586,-0.00696182,0.02490619,-0.02136621,-0.0299669,-0.01531299,-0.02123977,0.04454141,-0.09592602,-0.0327111,0.11080412,-0.06934835,-0.00439609,0.04045253,-0.08570551,0.03566094,0.04445873,-0.04147957,-0.05799615,0.0160512,0.03154139,0.09367795,0.05978585,-0.07648572,-0.04301518,-0.0583505,-0.00815392,-0.0349644,0.08118723,0.00734756,-0.05188146,-0.00594993,0.04423256,-0.00536632,-0.0600238,0.02303794,0.00377834,-0.05803914,0.00457539,0.12932058,0.0191578,-0.03381189,-0.02826742,0.00313242,0.04735389,0.06838,-0.02764698,-0.03963672,0.03727098,0.00076682,-0.11577957,-0.04048464,0.00149535,0.02657231,0.00511904,-0.10351893,0.01980373,0.00634224,0.03536468,-0.03940511,-0.01111665,-0.01628372,-0.00905657,0.02774898,-0.05015053,0.11659348,-0.03494193,-0.02213373,-0.01440806,-0.05536847,-0.00930467,0.04985029,-0.05286571,-0.02515944,0.06989666,-0.04490792,-0.02696134,0.02051315,0.02888749,-0.01733109,0.01621751,0.00780732,0.02650814,-0.06704113,0.05416345,0.01179228,-0.0261899,-0.07856461,-0.22885709,-0.04291494,0.04069829,-0.02203658,0.00460317,-0.04540879,0.02242695,-0.03527411,0.05000711,0.06681347,0.12408006,0.01723556,-0.06428869,0.00249067,-0.01759064,-0.03816621,0.01889398,-0.03860024,-0.02814141,0.00640038,0.04983087,0.02865857,-0.03275021,-0.03232022,0.01916805,-0.03249972,0.13076392,0.00968876,0.05650703,0.01501327,0.0541691,-0.04739389,-0.01933713,-0.04726479,0.08056588,0.04079076,-0.03253105,0.03171122,-0.04124943,-0.03492233,0.0048483,0.00275046,-0.01206867,-0.09014395,-0.02463841,-0.00839961,-0.05450717,-0.02653577,-0.03179534,0.02354834,0.07362451,-0.00051689,0.04535932,0.0638546,0.02424811,-0.03292688,-0.0248383,-0.02314473,-0.02168179,0.00572929,-0.01802243,-0.03979376,0.01942066,0.00173645,0.01219246,-0.01301781,0.01053516,-0.01084592,0.0292141,0.00936077,-0.02340998,0.0953043,0.00460225,-0.01091024,0.05367766,0.00022582,0.00985159,-0.06905673,-0.01036378,-0.02392159,0.02203192,-0.00153998,-0.00117675,0.04260678,0.00986513,-0.00421263,0.03715219,0.04456389,0.06967984,0.00464539,-0.04241038,0.00866708,-0.02157967,-0.00370233,0.0169881,-0.01609674,-0.2930828,0.01846988,-0.00041848,0.03427061,-0.02450333,-0.01391575,0.07646206,-0.03592449,-0.0717825,0.04458576,-0.08227606,0.06378741,0.03218849,-0.06274892,-0.02729522,-0.03841271,0.04076637,-0.01126955,0.09267058,0.02490973,0.05691226,0.05361706,0.26732457,-0.03669018,0.07981889,-0.03092886,0.0029803,0.01615089,0.07874052,0.02627706,-0.027279,-0.01690841,0.09781872,-0.02678451,0.01792203,0.01842598,0.03275098,0.02908848,0.02354881,0.03306751,-0.0360161,0.02209289,-0.06771249,0.04040364,0.11817725,-0.01676844,-0.03813616,-0.07809155,-0.04085616,0.03521491,-0.05702506,0.03342116,0.0276021,0.00932053,-0.01171612,0.05748576,0.00025321,-0.00605041,-0.02870545,0.00548383,0.05009335,0.00457363,0.05637933,0.05464913,0.00038618],"last_embed":{"hash":"5ad0865641039a90987e54ec160483f548950a43b3a8687d8d5256a4ba4ff0ad","tokens":482}}},"last_read":{"hash":"5ad0865641039a90987e54ec160483f548950a43b3a8687d8d5256a4ba4ff0ad","at":1745995165643},"class_name":"SmartSource2","outlinks":[{"title":"根據目前代碼現況，我認為應該首先實現以下部分","target":"根據目前代碼現況，我認為應該首先實現以下部分","line":82},{"title":"review the code for Julia-specific standards and function usage","target":"review the code for Julia-specific standards and function usage","line":100}],"blocks":{"#":[1,79],"####":[80,103],"#####{1}":[82,83],"#####{2}":[84,88],"#####{3}":[89,93],"#####{4}":[94,97],"#####{5}":[98,99],"#####{6}":[100,103]},"last_import":{"mtime":1743055283197,"size":4811,"at":1743059564970,"hash":"5ad0865641039a90987e54ec160483f548950a43b3a8687d8d5256a4ba4ff0ad"},"key":"根據目前代碼現況，我認為應該首先實現以下部分.md"},