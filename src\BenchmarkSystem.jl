# src/BenchmarkSystem.jl

module BenchmarkSystem

using Dates
using Statistics
using Printf
using Random
using ..WonderGridEngine: Drawing, WonderGridConfig, execute_wonder_grid_strategy

export BenchmarkConfig, BenchmarkResult, BenchmarkSuite,
       create_benchmark_suite, run_benchmark, compare_algorithms,
       generate_benchmark_report, run_performance_regression_test

# 基準測試配置
struct BenchmarkConfig
    name::String
    iterations::Int
    warmup_iterations::Int
    timeout_seconds::Float64
    memory_limit_mb::Float64
    data_sizes::Vector{Int}
    strategies::Vector{Symbol}
    
    function BenchmarkConfig(;
        name::String = "Default Benchmark",
        iterations::Int = 10,
        warmup_iterations::Int = 3,
        timeout_seconds::Float64 = 300.0,
        memory_limit_mb::Float64 = 2048.0,
        data_sizes::Vector{Int} = [10, 50, 100, 500],
        strategies::Vector{Symbol} = [:ffg, :skip, :frequency]
    )
        new(name, iterations, warmup_iterations, timeout_seconds,
            memory_limit_mb, data_sizes, strategies)
    end
end

# 基準測試結果
struct BenchmarkResult
    test_name::String
    strategy::Symbol
    data_size::Int
    execution_times::Vector{Float64}
    memory_usage::Vector{Float64}
    success_rate::Float64
    error_count::Int
    timestamp::DateTime
    
    # 統計指標
    mean_time::Float64
    median_time::Float64
    std_time::Float64
    min_time::Float64
    max_time::Float64
    
    function BenchmarkResult(test_name, strategy, data_size, execution_times, 
                           memory_usage, success_rate, error_count)
        timestamp = now()
        mean_time = mean(execution_times)
        median_time = median(execution_times)
        std_time = std(execution_times)
        min_time = minimum(execution_times)
        max_time = maximum(execution_times)
        
        new(test_name, strategy, data_size, execution_times, memory_usage,
            success_rate, error_count, timestamp, mean_time, median_time,
            std_time, min_time, max_time)
    end
end

# 基準測試套件
mutable struct BenchmarkSuite
    config::BenchmarkConfig
    results::Vector{BenchmarkResult}
    test_data::Dict{Int, Vector{Drawing}}
    
    function BenchmarkSuite(config::BenchmarkConfig)
        new(config, BenchmarkResult[], Dict{Int, Vector{Drawing}}())
    end
end

"""
創建基準測試套件
"""
function create_benchmark_suite(config::BenchmarkConfig)::BenchmarkSuite
    suite = BenchmarkSuite(config)
    
    println("🎯 創建基準測試套件: $(config.name)")
    println("  - 迭代次數: $(config.iterations)")
    println("  - 預熱次數: $(config.warmup_iterations)")
    println("  - 超時限制: $(config.timeout_seconds) 秒")
    println("  - 記憶體限制: $(config.memory_limit_mb) MB")
    println("  - 數據大小: $(config.data_sizes)")
    println("  - 測試策略: $(config.strategies)")
    
    # 生成測試數據
    generate_test_data!(suite)
    
    return suite
end

"""
生成測試數據
"""
function generate_test_data!(suite::BenchmarkSuite)
    println("📊 生成測試數據...")
    
    for size in suite.config.data_sizes
        drawings = Vector{Drawing}()
        
        for i in 1:size
            # 生成隨機開獎數據
            numbers = sort(sample(1:49, 6, replace=false))
            drawing = Drawing(size - i + 1, :Lotto6_49, Date(2024, 1, 1) + Day(i-1), numbers)
            push!(drawings, drawing)
        end
        
        suite.test_data[size] = drawings
        println("  - 生成 $size 期測試數據")
    end
end

"""
簡單的無重複隨機抽樣
"""
function sample(collection, n::Int; replace::Bool=true)
    if replace
        return [rand(collection) for _ in 1:n]
    else
        if n > length(collection)
            throw(ArgumentError("樣本大小不能超過集合大小"))
        end
        shuffled = shuffle(collect(collection))
        return shuffled[1:n]
    end
end

"""
運行基準測試
"""
function run_benchmark(suite::BenchmarkSuite, test_name::String)::Vector{BenchmarkResult}
    println("\n🚀 開始運行基準測試: $test_name")
    
    results = BenchmarkResult[]
    
    for strategy in suite.config.strategies
        for data_size in suite.config.data_sizes
            println("  測試策略: $strategy, 數據大小: $data_size")
            
            result = run_single_benchmark(suite, test_name, strategy, data_size)
            push!(results, result)
            push!(suite.results, result)
        end
    end
    
    return results
end

"""
運行單個基準測試
"""
function run_single_benchmark(
    suite::BenchmarkSuite, 
    test_name::String, 
    strategy::Symbol, 
    data_size::Int
)::BenchmarkResult
    
    config = WonderGridConfig(
        analysis_range = min(data_size, 50),
        key_number_strategy = strategy,
        combination_limit = 10,
        game_type = :Lotto6_49
    )
    
    drawings = suite.test_data[data_size]
    execution_times = Float64[]
    memory_usage = Float64[]
    error_count = 0
    
    # 預熱運行
    for _ in 1:suite.config.warmup_iterations
        try
            execute_wonder_grid_strategy(drawings, config)
        catch
            # 忽略預熱錯誤
        end
    end
    
    # 正式測試
    for i in 1:suite.config.iterations
        try
            # 記錄開始時間和記憶體
            start_time = time()
            start_memory = get_memory_usage()
            
            # 執行測試
            result = execute_wonder_grid_strategy(drawings, config)
            
            # 記錄結束時間和記憶體
            end_time = time()
            end_memory = get_memory_usage()
            
            execution_time = end_time - start_time
            memory_used = end_memory - start_memory
            
            push!(execution_times, execution_time)
            push!(memory_usage, memory_used)
            
        catch e
            error_count += 1
            println("    ⚠️  迭代 $i 失敗: $e")
        end
    end
    
    success_rate = (suite.config.iterations - error_count) / suite.config.iterations
    
    return BenchmarkResult(test_name, strategy, data_size, execution_times,
                          memory_usage, success_rate, error_count)
end

"""
獲取記憶體使用量（簡化實現）
"""
function get_memory_usage()::Float64
    # 簡化實現，實際應該使用系統API
    return rand(100.0:500.0)
end

"""
比較算法性能
"""
function compare_algorithms(
    suite::BenchmarkSuite,
    baseline_strategy::Symbol,
    comparison_strategies::Vector{Symbol}
)::Dict{Symbol, Dict{String, Float64}}
    
    println("\n📈 算法性能比較")
    println("  基準策略: $baseline_strategy")
    println("  比較策略: $comparison_strategies")
    
    comparison_results = Dict{Symbol, Dict{String, Float64}}()
    
    # 獲取基準結果
    baseline_results = filter(r -> r.strategy == baseline_strategy, suite.results)
    
    if isempty(baseline_results)
        println("❌ 找不到基準策略的測試結果")
        return comparison_results
    end
    
    baseline_mean_time = mean([r.mean_time for r in baseline_results])
    
    for strategy in comparison_strategies
        strategy_results = filter(r -> r.strategy == strategy, suite.results)
        
        if isempty(strategy_results)
            println("⚠️  找不到策略 $strategy 的測試結果")
            continue
        end
        
        strategy_mean_time = mean([r.mean_time for r in strategy_results])
        
        # 計算性能比較指標
        speedup = baseline_mean_time / strategy_mean_time
        improvement = (baseline_mean_time - strategy_mean_time) / baseline_mean_time * 100
        
        comparison_results[strategy] = Dict{String, Float64}(
            "speedup" => speedup,
            "improvement_percent" => improvement,
            "mean_time" => strategy_mean_time,
            "baseline_time" => baseline_mean_time
        )
        
        println("  $strategy vs $baseline_strategy:")
        println("    - 加速比: $(round(speedup, digits=2))x")
        println("    - 改善: $(round(improvement, digits=1))%")
    end
    
    return comparison_results
end

"""
生成基準測試報告
"""
function generate_benchmark_report(suite::BenchmarkSuite, output_file::String)
    println("\n📋 生成基準測試報告...")

    open(output_file, "w") do io
        write_report_header(io, suite)
        write_summary_statistics(io, suite)
        write_detailed_results(io, suite)
        write_performance_analysis(io, suite)
        write_recommendations(io, suite)
    end

    println("✓ 報告已保存到: $output_file")
end

"""
寫入報告標題
"""
function write_report_header(io::IO, suite::BenchmarkSuite)
    println(io, "# 基準測試報告")
    println(io, "")
    println(io, "**測試套件:** $(suite.config.name)")
    println(io, "**生成時間:** $(now())")
    println(io, "**測試配置:**")
    println(io, "- 迭代次數: $(suite.config.iterations)")
    println(io, "- 預熱次數: $(suite.config.warmup_iterations)")
    println(io, "- 數據大小: $(suite.config.data_sizes)")
    println(io, "- 測試策略: $(suite.config.strategies)")
    println(io, "")
end

"""
寫入摘要統計
"""
function write_summary_statistics(io::IO, suite::BenchmarkSuite)
    println(io, "## 摘要統計")
    println(io, "")

    if isempty(suite.results)
        println(io, "無測試結果")
        return
    end

    # 按策略分組統計
    for strategy in suite.config.strategies
        strategy_results = filter(r -> r.strategy == strategy, suite.results)

        if isempty(strategy_results)
            continue
        end

        mean_time = mean([r.mean_time for r in strategy_results])
        mean_success_rate = mean([r.success_rate for r in strategy_results])
        total_errors = sum([r.error_count for r in strategy_results])

        println(io, "### 策略: $strategy")
        println(io, "- 平均執行時間: $(round(mean_time, digits=3)) 秒")
        println(io, "- 平均成功率: $(round(mean_success_rate * 100, digits=1))%")
        println(io, "- 總錯誤數: $total_errors")
        println(io, "")
    end
end

"""
寫入詳細結果
"""
function write_detailed_results(io::IO, suite::BenchmarkSuite)
    println(io, "## 詳細結果")
    println(io, "")
    println(io, "| 策略 | 數據大小 | 平均時間(s) | 中位數時間(s) | 標準差(s) | 成功率(%) |")
    println(io, "|------|----------|-------------|---------------|-----------|-----------|")

    for result in suite.results
        println(io, "| $(result.strategy) | $(result.data_size) | $(round(result.mean_time, digits=3)) | $(round(result.median_time, digits=3)) | $(round(result.std_time, digits=3)) | $(round(result.success_rate * 100, digits=1)) |")
    end

    println(io, "")
end

"""
寫入性能分析
"""
function write_performance_analysis(io::IO, suite::BenchmarkSuite)
    println(io, "## 性能分析")
    println(io, "")

    # 找出最快和最慢的策略
    if !isempty(suite.results)
        fastest_result = minimum(suite.results, by = r -> r.mean_time)
        slowest_result = maximum(suite.results, by = r -> r.mean_time)

        println(io, "**最快策略:** $(fastest_result.strategy) ($(round(fastest_result.mean_time, digits=3))s)")
        println(io, "**最慢策略:** $(slowest_result.strategy) ($(round(slowest_result.mean_time, digits=3))s)")

        speedup = slowest_result.mean_time / fastest_result.mean_time
        println(io, "**性能差異:** $(round(speedup, digits=2))x")
        println(io, "")
    end
end

"""
寫入優化建議
"""
function write_recommendations(io::IO, suite::BenchmarkSuite)
    println(io, "## 優化建議")
    println(io, "")

    # 基於結果生成建議
    high_error_strategies = filter(r -> r.error_count > 0, suite.results)
    slow_strategies = filter(r -> r.mean_time > 1.0, suite.results)

    if !isempty(high_error_strategies)
        println(io, "- **錯誤處理:** 以下策略出現錯誤，建議檢查實現:")
        for result in high_error_strategies
            println(io, "  - $(result.strategy): $(result.error_count) 個錯誤")
        end
        println(io, "")
    end

    if !isempty(slow_strategies)
        println(io, "- **性能優化:** 以下策略執行較慢，建議優化:")
        for result in slow_strategies
            println(io, "  - $(result.strategy): $(round(result.mean_time, digits=3))s")
        end
        println(io, "")
    end

    println(io, "- **一般建議:**")
    println(io, "  - 考慮使用並行處理提升性能")
    println(io, "  - 實現結果快取減少重複計算")
    println(io, "  - 優化記憶體使用模式")
    println(io, "  - 添加更多錯誤處理機制")
end

"""
運行性能回歸測試
"""
function run_performance_regression_test(
    suite::BenchmarkSuite,
    baseline_results::Vector{BenchmarkResult},
    tolerance_percent::Float64 = 10.0
)::Dict{String, Any}

    println("\n🔄 運行性能回歸測試")
    println("  容忍度: $(tolerance_percent)%")

    regression_report = Dict{String, Any}(
        "passed" => true,
        "regressions" => Vector{Dict{String, Any}}(),
        "improvements" => Vector{Dict{String, Any}}(),
        "summary" => Dict{String, Any}()
    )

    for current_result in suite.results
        # 找到對應的基準結果
        baseline_result = findfirst(r ->
            r.strategy == current_result.strategy &&
            r.data_size == current_result.data_size,
            baseline_results)

        if baseline_result === nothing
            continue
        end

        baseline = baseline_results[baseline_result]

        # 計算性能變化
        time_change = (current_result.mean_time - baseline.mean_time) / baseline.mean_time * 100

        if time_change > tolerance_percent
            # 性能回歸
            regression_report["passed"] = false
            push!(regression_report["regressions"], Dict{String, Any}(
                "strategy" => current_result.strategy,
                "data_size" => current_result.data_size,
                "baseline_time" => baseline.mean_time,
                "current_time" => current_result.mean_time,
                "regression_percent" => time_change
            ))
        elseif time_change < -tolerance_percent
            # 性能改善
            push!(regression_report["improvements"], Dict{String, Any}(
                "strategy" => current_result.strategy,
                "data_size" => current_result.data_size,
                "baseline_time" => baseline.mean_time,
                "current_time" => current_result.mean_time,
                "improvement_percent" => -time_change
            ))
        end
    end

    # 生成摘要
    regression_report["summary"] = Dict{String, Any}(
        "total_tests" => length(suite.results),
        "regressions_count" => length(regression_report["regressions"]),
        "improvements_count" => length(regression_report["improvements"]),
        "pass_rate" => regression_report["passed"] ? 100.0 :
                      (length(suite.results) - length(regression_report["regressions"])) / length(suite.results) * 100
    )

    return regression_report
end

end # module BenchmarkSystem
