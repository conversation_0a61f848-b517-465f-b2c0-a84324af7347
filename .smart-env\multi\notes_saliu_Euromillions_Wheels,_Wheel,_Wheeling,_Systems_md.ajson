
"smart_sources:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md": {"path":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08812796,-0.04663878,-0.01692528,-0.01539532,-0.06181379,0.03530787,-0.02622331,0.02071154,0.0600988,-0.00589179,-0.0174678,-0.05062995,0.02871763,0.00801558,-0.03196652,-0.0278974,0.00702911,0.00243703,-0.10990255,-0.00254906,0.11565503,-0.04755608,-0.0360056,-0.07806066,0.00101994,0.01545357,-0.01050055,-0.01234854,-0.00092792,-0.24060355,0.02854147,-0.01420776,-0.03348761,-0.01954533,-0.05612721,-0.03515799,-0.05708399,0.02314756,-0.09946655,0.00356541,0.01758535,0.0454204,0.00105481,0.00237471,0.054978,-0.00948021,-0.04439129,0.04566948,0.02472095,0.00406483,-0.0225998,-0.01625313,0.00603492,0.01624144,0.03529205,0.05568916,0.02560396,0.08434402,0.05038672,0.06557535,0.09106404,0.06190597,-0.196104,0.03424048,-0.01243773,0.02379596,-0.00855155,-0.07039888,0.01463692,0.03893869,0.05607611,0.01310529,0.01160536,0.08236224,0.03395376,-0.00682657,-0.04721487,-0.09248271,-0.08979266,0.04865789,-0.01236561,0.01871854,-0.00033858,-0.01019972,0.01303409,0.03032872,0.04912503,0.01732261,0.05680219,-0.07393049,-0.02975787,-0.02624155,0.06630244,0.0515612,-0.03239015,0.00271186,0.01932971,0.00329686,0.00671863,0.10717975,0.00706421,0.04376063,0.03688253,0.00218557,0.02700218,0.02299554,0.00468133,-0.01633075,-0.04274029,0.02234393,0.04549792,0.00572202,0.0375393,-0.01906315,-0.06353821,-0.01828712,0.01493936,-0.0115542,0.01549435,0.05228899,-0.03628141,0.04978245,0.02884016,-0.02010487,0.00934746,0.05175069,0.0372268,0.03985444,0.02620828,0.01476912,0.02652288,-0.00678449,-0.1221191,-0.02794455,0.0071187,-0.02932957,-0.00132015,-0.00746822,0.03512883,0.04036717,-0.00912935,-0.06097655,0.02787064,-0.10693398,-0.06910793,0.06307054,-0.0092771,-0.03448734,0.00063899,0.01375464,0.00212816,0.00106358,-0.00133097,-0.04405708,0.007683,0.04510789,0.11853346,0.08620737,-0.05251018,0.02019696,-0.02752414,-0.03509975,-0.06928572,0.15597145,-0.05410384,-0.11698914,-0.02123893,0.08830765,0.00571115,-0.10035128,-0.00272245,0.0077622,-0.08099212,0.02626125,0.08971034,-0.00667276,-0.0749594,-0.01096058,-0.02949395,-0.02992854,0.01567636,-0.0389357,-0.04779584,0.02485067,-0.01168285,-0.05090972,-0.02857635,-0.02571805,0.04437567,0.04765345,-0.02011135,0.03985154,0.02802077,0.02326547,-0.03443501,-0.03653572,-0.01727606,-0.01366624,0.04951512,-0.02119833,0.01388444,0.0660346,0.01227301,0.04994382,-0.00353207,0.00785227,-0.04026271,-0.05925881,0.03218937,0.03999478,-0.04047316,0.00035073,0.03277368,0.04971363,-0.07815014,0.04639545,0.01178878,0.03099226,-0.01532711,-0.01947867,-0.00273887,-0.00965409,-0.09829539,-0.18624027,-0.05092397,-0.02769141,0.01379078,0.03943808,0.0061333,0.01807045,-0.02931736,0.03406028,0.01463693,0.06340931,-0.03611049,-0.02031046,0.00376125,-0.00764803,0.05814833,-0.10861045,-0.01939387,-0.00043479,0.04122274,0.03119638,0.04818036,0.00284974,-0.10962068,-0.00275045,-0.03674252,0.13741033,0.00187212,-0.00588985,-0.05386901,0.08736712,-0.02756536,0.00239095,0.01562821,0.02187834,0.02465795,-0.08295748,-0.02191729,-0.049511,-0.01846244,-0.05430984,-0.01883969,0.00262652,-0.10778669,0.00893346,-0.028503,0.00326478,-0.04075345,0.01594118,0.05802894,-0.00044647,0.03515477,0.03103619,0.02018818,0.07818029,-0.01121292,-0.05410415,-0.00245246,-0.01396194,0.01293042,-0.01670522,-0.03011439,0.07405105,-0.05889452,0.0565416,0.00762477,-0.0456457,-0.0499679,-0.01069636,-0.02263637,-0.00857978,0.10206672,0.0335054,0.071993,-0.03881761,0.04946002,0.0113718,0.03678219,0.03661791,0.01601188,-0.04838406,-0.0163164,0.04259872,0.07581125,0.02884189,0.06052272,0.03986311,0.023917,-0.02606437,0.02951204,0.01014776,0.04568734,-0.04312439,0.03478638,0.03944987,0.04800768,-0.25423038,0.05156247,-0.00710043,0.0218809,-0.00013589,0.01494002,0.0189782,0.01313884,0.02444063,-0.05686667,0.05695675,0.06892314,0.01508684,-0.01119224,0.00261519,-0.0015295,0.02026882,-0.02977433,0.09231508,-0.00998084,0.01951225,0.01514968,0.2351258,0.02523505,0.01695692,0.03278286,0.00085545,-0.00622091,-0.02254998,0.00138202,-0.03506922,0.01906911,0.06954938,0.00514254,-0.03522161,0.03955765,-0.06744034,0.01009551,-0.02753729,0.01700395,-0.06141006,0.01070539,0.02332863,0.00023224,0.11482316,0.02584905,-0.00297919,-0.12447775,0.05382067,0.0180649,-0.04270575,-0.03983624,-0.09155946,-0.05598425,0.01484751,0.01330302,0.02303047,-0.00154339,-0.0072731,-0.00413734,-0.01944071,-0.03384439,0.01576566,0.00906446,-0.01857016],"last_embed":{"hash":"1zdcnk","tokens":485}}},"last_read":{"hash":"1zdcnk","at":1753423456123},"class_name":"SmartSource","last_import":{"mtime":1753366217206,"size":25159,"at":1753423416052,"hash":"1zdcnk"},"blocks":{"#---frontmatter---":[1,6],"#Euromillions Wheels, Wheel, Wheeling, Systems":[8,968],"#Euromillions Wheels, Wheel, Wheeling, Systems#{1}":[10,931],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)":[932,968],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{1}":[934,935],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{2}":[936,936],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{3}":[937,937],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{4}":[938,938],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{5}":[939,940],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{6}":[941,942],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{7}":[943,944],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{8}":[945,946],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{9}":[947,947],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{10}":[948,948],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{11}":[949,949],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{12}":[950,950],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{13}":[951,951],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{14}":[952,952],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{15}":[953,953],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{16}":[954,954],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{17}":[955,955],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{18}":[956,956],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{19}":[957,957],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{20}":[958,958],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{21}":[959,959],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{22}":[960,960],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{23}":[961,962],"#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{24}":[963,968]},"outlinks":[{"title":"The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.","target":"https://saliu.com/ScreenImgs/lexico-lottowheels.gif","line":16},{"title":"The software function creates lotto wheels for Euromillions lottery game in Europe.","target":"https://saliu.com/ScreenImgs/euromillions-wheels.gif","line":20},{"title":"Euromillions, Euromillones: Wheels, Wheel, Wheeling.","target":"https://saliu.com/HLINE.gif","line":928},{"title":"![Euro Millions, EuroMillions, Euromilhoes Wheels, Combinations.","target":"https://saliu.com/back_cmp_valueadd110_back_a.gif","line":930},{"title":"<u>Resources in Euromillions, Lottery Software, Wheels</u>","target":"https://saliu.com/content/lottery.html","line":932},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":936},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":949},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":951},{"title":"The myth of lotto wheels or abbreviated lotto systems","target":"https://saliu.com/bbs/messages/11.html","line":953},{"title":"WHEEL-632 available as free lotto wheeling software","target":"https://saliu.com/bbs/messages/wheel.html","line":955},{"title":"lottery software for players of lotto wheels","target":"https://saliu.com/bbs/messages/857.html","line":956},{"title":"Software to verify lotto wheels","target":"https://saliu.com/check-wheels.html","line":957},{"title":"Wheels, balanced lotto wheels, lexicographic order","target":"https://saliu.com/bbs/messages/772.html","line":958},{"title":"Check WHEEL and lotto wheels for winners","target":"https://saliu.com/bbs/messages/90.html","line":959},{"title":"Genuine Powerball wheels","target":"https://saliu.com/powerball_wheels.html","line":960},{"title":"Genuine Mega Millions wheels","target":"https://saliu.com/megamillions_wheels.html","line":961},{"title":"The reduced Euromillions systems are created by free lotto wheeling software.","target":"https://saliu.com/HLINE.gif","line":963},{"title":"Forums","target":"https://forums.saliu.com/","line":965},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":965},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":965},{"title":"Contents","target":"https://saliu.com/content/index.html","line":965},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":965},{"title":"Home","target":"https://saliu.com/index.htm","line":965},{"title":"Search","target":"https://saliu.com/Search.htm","line":965},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":965},{"title":"The site for wheels, wheeling numbers for Euromillions.","target":"https://saliu.com/HLINE.gif","line":967}],"metadata":{"created":"2025-07-24T22:10:03 (UTC +08:00)","tags":["wheels","wheel","wheeling","software","balanced wheels","lotto wheels","Euromillions","Euromillónes","Euromilhões","Euromillionen","lexicographic","lexicographical","index"],"source":"https://saliu.com/euro_millions_wheels.html","author":null}},"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09773418,-0.07577892,-0.00288489,-0.0062276,-0.01502221,0.01312861,-0.04887945,0.05109255,0.02989653,-0.01004734,-0.0178671,-0.06078194,0.01327068,-0.00044705,-0.0008494,0.00095943,0.01585047,0.01264244,-0.06922402,-0.00856279,0.10727134,-0.03135932,-0.03247719,-0.05116199,0.0089332,0.03444878,-0.00116483,-0.02547978,-0.00095794,-0.19951497,-0.01195444,0.01113039,-0.00799541,0.02219308,-0.03157981,-0.00586504,-0.03893429,0.02985632,-0.04382521,0.00921196,0.0198491,0.06105667,-0.02965006,0.00516314,0.02983647,-0.00325186,-0.0220804,0.04217679,0.02069114,-0.00320369,0.01068946,-0.0242324,-0.01042453,0.01811933,0.02844184,0.05661527,0.02575015,0.02003111,0.05185046,0.06203763,0.07011762,0.02605382,-0.20907164,0.05745683,-0.01011993,0.02330418,0.0016689,-0.07696547,-0.00540259,0.06749931,0.05915146,-0.00733517,-0.01049553,0.06127901,0.02689821,0.01166918,-0.03792483,-0.0928299,-0.07339389,0.00570649,0.00003984,0.03538453,-0.03182949,-0.03236418,-0.03269387,0.00030933,0.03329372,0.03185496,0.05229308,-0.07466451,-0.04298205,-0.05351575,0.11561713,0.05755359,-0.07231467,-0.00330805,0.01282069,0.0204782,-0.00882968,0.11063876,-0.02046617,0.05262762,0.06399737,-0.01706806,0.03368332,0.04500893,0.01539897,-0.01575096,-0.04076673,0.02227238,0.05531606,0.02052245,0.01782112,-0.05112804,0.0173659,-0.05287204,0.03403356,-0.00569611,0.01222669,0.05185791,-0.0301447,0.01790848,0.0660008,-0.01660536,0.02091215,0.02832378,0.02818787,0.04012544,0.0168869,0.01561172,0.02142145,0.01531707,-0.08852439,-0.01356346,-0.00617753,-0.02175238,0.02967267,-0.03334808,0.04053844,0.01917202,-0.03180873,-0.09452609,0.00304252,-0.11085317,-0.06968671,0.08198647,0.00868417,-0.04638318,-0.00504299,0.02153352,-0.02194717,0.03195204,0.02757729,-0.03139807,0.02475786,0.04401118,0.1332538,0.08055624,-0.03421448,0.03064147,0.00260764,-0.03946587,-0.09910396,0.18261904,-0.02195903,-0.13439764,-0.03605846,0.0746488,0.00635,-0.07741875,-0.00005074,-0.01133151,-0.06792706,0.02837536,0.13279092,-0.00743537,-0.02109225,0.00036179,-0.05747344,-0.02584138,-0.00322476,-0.03345426,-0.07337432,0.01910639,-0.00943598,-0.03836402,-0.04099423,-0.01954123,0.03645145,0.02539631,-0.02004601,0.01239202,0.0294817,0.02593458,-0.05499973,-0.02116761,-0.00580159,-0.01004599,0.04037345,-0.04095638,0.01200331,0.0839585,-0.0096083,0.0372455,-0.01121564,-0.00260318,-0.0555123,-0.04411485,0.02115265,0.0430505,-0.02024113,0.03235287,0.04848663,0.01106729,-0.0819171,0.01856282,0.00996166,0.02831763,-0.01182924,0.00275574,-0.01269875,-0.040796,-0.11023629,-0.19770792,-0.00544248,0.02870376,0.02918155,0.03859182,-0.00466881,0.02661913,-0.00334442,0.02340152,0.03054389,0.03907506,-0.03248402,-0.00303359,-0.06543449,0.00116886,0.07001065,-0.0567508,-0.03301827,0.00167783,0.04425786,0.0222184,0.03371558,-0.03125637,-0.10837118,0.00758438,-0.01915751,0.13303068,0.02611944,-0.00178446,-0.04320037,0.08017365,-0.01694407,0.00660072,-0.04309507,0.01268262,0.00489528,-0.06681348,-0.02467439,-0.05290411,-0.03423757,-0.04418587,-0.01081276,-0.00149888,-0.08567858,0.01755303,-0.02239719,0.013157,-0.09696575,0.00824696,0.01815244,-0.00638002,0.02634292,0.02537875,0.02472004,0.04972359,0.00980577,-0.07365472,-0.01789274,-0.02503352,0.02043876,-0.03219997,0.01144063,0.07938372,-0.07609225,0.02457152,0.01252513,-0.06648152,-0.05150925,-0.06397945,-0.01370272,0.00297587,0.11591566,0.03878739,0.02843899,-0.04232666,0.01006358,-0.03773963,0.06655614,0.03959253,0.01616806,-0.05926291,-0.01968545,0.05015652,0.07654156,0.04449082,0.07621478,-0.0011964,-0.02333787,0.00294,0.0193969,0.01790813,0.0458748,-0.04347088,-0.00892804,0.08545303,0.03498285,-0.25033855,0.06986187,0.03570103,0.02131373,0.00794582,0.03762849,-0.00086384,0.00832501,0.00812454,-0.04017581,0.0838201,0.06436393,-0.00560224,0.05136941,0.00422613,0.01387622,0.03401111,-0.01804086,0.05874942,-0.01017425,-0.00391986,-0.01802339,0.22724923,0.04206914,0.02371476,0.03074873,-0.02453853,-0.00403478,-0.01419448,0.01353204,-0.04974626,0.00400742,0.04576762,0.02380638,-0.02449927,0.07033832,-0.08424257,0.03068913,-0.02050666,0.056495,-0.08096797,0.03043711,0.02981193,0.00840083,0.09640448,0.02182228,0.00075183,-0.12122598,0.03248155,0.00665091,-0.01712519,-0.04054375,-0.09939894,-0.0072331,0.0368062,0.03391451,0.03506182,-0.02926831,-0.01307795,-0.01654264,-0.00190031,-0.04561207,-0.00479895,0.01251349,-0.00846807],"last_embed":{"hash":"178gy68","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"178gy68","at":1753423455575},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#---frontmatter---","lines":[1,6],"size":266,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08040071,-0.04087706,-0.00952008,-0.02053175,-0.07005516,0.03168846,-0.01819388,0.0184844,0.06169486,0.00098245,-0.00932849,-0.04932256,0.0298084,0.01181963,-0.02999848,-0.02349167,0.01011291,-0.0055038,-0.12489804,-0.00027523,0.10954598,-0.0530455,-0.04048813,-0.0752335,-0.00741213,0.00168184,-0.01474275,-0.0192913,0.00366823,-0.23837391,0.03662993,-0.01235235,-0.03202651,-0.01996573,-0.05140531,-0.04156036,-0.06348145,0.02265835,-0.1032154,0.00750499,0.01979762,0.04398265,0.00747002,-0.00078972,0.05454456,-0.00733029,-0.04747095,0.04600603,0.03316085,0.01019125,-0.01853634,-0.01082952,0.01487298,0.00926884,0.02301537,0.04357189,0.02390298,0.08851234,0.05601598,0.06128478,0.08103742,0.07058039,-0.19327955,0.02320085,-0.00316204,0.02404898,-0.00380597,-0.06605976,0.01197192,0.04202609,0.05704391,0.01245386,0.01987208,0.08282231,0.0333791,-0.01867074,-0.0489967,-0.0790253,-0.08859993,0.06172058,-0.0223166,0.00877897,0.00388394,-0.015926,0.02070405,0.02742898,0.04922898,0.02175368,0.05574852,-0.07120732,-0.02843998,-0.00837915,0.05041165,0.05250867,-0.0267556,0.01095267,0.0118019,-0.00741886,0.01211018,0.11573498,0.01342588,0.03404691,0.03328888,0.00379501,0.01563294,0.02196819,0.00808084,-0.00922285,-0.0376874,0.02811618,0.04486451,0.00526011,0.03890991,-0.01450579,-0.07070193,-0.02513331,0.0122738,-0.00821201,0.00476231,0.05362855,-0.04025024,0.04933978,0.02115928,-0.02578748,0.01461104,0.05022879,0.03255017,0.04490168,0.03074409,0.01895759,0.02153464,-0.00874279,-0.12195848,-0.03024419,0.00858959,-0.02849794,-0.00289065,0.00102842,0.03365739,0.05116662,-0.00724952,-0.05765746,0.02464142,-0.10836159,-0.05915687,0.05421286,-0.0033509,-0.0297759,0.00169583,0.01142516,0.00681915,0.00116216,0.00060439,-0.04673985,0.00994196,0.05181491,0.11849417,0.0768378,-0.05129616,0.01822695,-0.03461754,-0.03261103,-0.06751767,0.15500805,-0.0526126,-0.10615171,-0.01949979,0.07907744,4.2e-7,-0.10038272,-0.00440128,0.01642625,-0.07634354,0.03780666,0.08078534,-0.00040695,-0.08735264,-0.0074268,-0.03686678,-0.02977323,0.02410877,-0.04266312,-0.0396058,0.03670585,-0.01384886,-0.05519349,-0.02031747,-0.02147346,0.04968436,0.04708151,-0.02481555,0.05072115,0.02646777,0.01516048,-0.0291878,-0.03527091,-0.00978139,-0.01262162,0.05085455,-0.0160371,0.0175914,0.06388676,0.01697204,0.05887453,0.00655028,0.01739897,-0.04137205,-0.0551489,0.03849794,0.04439287,-0.04164457,-0.00642036,0.03406167,0.05766906,-0.08542888,0.04810556,0.00874829,0.02928482,-0.01253273,-0.02036605,0.00209192,-0.00932902,-0.09390231,-0.18456633,-0.04974356,-0.03228154,0.0138213,0.04398495,0.00546895,0.01014324,-0.02566964,0.03367285,0.00801424,0.0580369,-0.04156759,-0.01315955,0.00795542,-0.0083806,0.06346966,-0.11157531,-0.0111152,-0.00054395,0.03528322,0.02922253,0.04698284,0.0034764,-0.11734129,0.00611372,-0.0473508,0.14404398,-0.00348957,0.00004886,-0.06406166,0.08709342,-0.02973527,0.00574874,0.03354653,0.0180296,0.01673839,-0.0855102,-0.02895813,-0.04664837,-0.01387724,-0.05638374,-0.02577866,-0.00639983,-0.10905176,0.0078053,-0.0244087,0.00507647,-0.02988259,0.01839514,0.06559592,-0.00279007,0.03288408,0.02853736,0.02126562,0.08826836,-0.01176991,-0.05377749,0.0106168,-0.01462782,0.00872935,-0.01179058,-0.03119551,0.07430026,-0.05574755,0.05560627,0.01366678,-0.03845466,-0.05629062,-0.00977066,-0.0338923,-0.00787407,0.09591572,0.03079022,0.07409397,-0.04723825,0.04956323,0.0053912,0.03065812,0.02332794,0.01575961,-0.04053802,-0.01075964,0.03463981,0.0759888,0.0142308,0.05517726,0.03565768,0.03190902,-0.03210598,0.03019407,0.00493683,0.04876518,-0.04355878,0.04730267,0.02723202,0.04489127,-0.25702131,0.04786351,-0.0260811,0.01988212,0.00541918,0.00975521,0.02387545,0.02000884,0.03186319,-0.05109742,0.06129364,0.05878854,0.02295698,-0.02135716,0.0025874,-0.00579448,0.02129703,-0.03004687,0.09512636,-0.01630861,0.02970289,0.01300841,0.2279547,0.01994163,0.01599405,0.02871798,-0.00229287,-0.01045816,-0.02868349,-0.00383831,-0.03170482,0.02225469,0.0695889,-0.00532445,-0.02396656,0.0276496,-0.06536671,0.00538367,-0.03419697,0.01269165,-0.05452303,-0.00197915,0.02344446,-0.00364647,0.12116668,0.02907714,-0.00718362,-0.1294868,0.05645258,0.01649813,-0.04598551,-0.02920189,-0.09521714,-0.05508225,0.02924672,0.00937769,0.00779706,0.00683302,-0.00157712,-0.00012452,-0.02446316,-0.03552103,0.00512269,0.0045094,-0.02269481],"last_embed":{"hash":"1sgvro7","tokens":415}}},"text":null,"length":0,"last_read":{"hash":"1sgvro7","at":1753423455610},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems","lines":[8,968],"size":24885,"outlinks":[{"title":"The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.","target":"https://saliu.com/ScreenImgs/lexico-lottowheels.gif","line":9},{"title":"The software function creates lotto wheels for Euromillions lottery game in Europe.","target":"https://saliu.com/ScreenImgs/euromillions-wheels.gif","line":13},{"title":"Euromillions, Euromillones: Wheels, Wheel, Wheeling.","target":"https://saliu.com/HLINE.gif","line":921},{"title":"![Euro Millions, EuroMillions, Euromilhoes Wheels, Combinations.","target":"https://saliu.com/back_cmp_valueadd110_back_a.gif","line":923},{"title":"<u>Resources in Euromillions, Lottery Software, Wheels</u>","target":"https://saliu.com/content/lottery.html","line":925},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":929},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":942},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":944},{"title":"The myth of lotto wheels or abbreviated lotto systems","target":"https://saliu.com/bbs/messages/11.html","line":946},{"title":"WHEEL-632 available as free lotto wheeling software","target":"https://saliu.com/bbs/messages/wheel.html","line":948},{"title":"lottery software for players of lotto wheels","target":"https://saliu.com/bbs/messages/857.html","line":949},{"title":"Software to verify lotto wheels","target":"https://saliu.com/check-wheels.html","line":950},{"title":"Wheels, balanced lotto wheels, lexicographic order","target":"https://saliu.com/bbs/messages/772.html","line":951},{"title":"Check WHEEL and lotto wheels for winners","target":"https://saliu.com/bbs/messages/90.html","line":952},{"title":"Genuine Powerball wheels","target":"https://saliu.com/powerball_wheels.html","line":953},{"title":"Genuine Mega Millions wheels","target":"https://saliu.com/megamillions_wheels.html","line":954},{"title":"The reduced Euromillions systems are created by free lotto wheeling software.","target":"https://saliu.com/HLINE.gif","line":956},{"title":"Forums","target":"https://forums.saliu.com/","line":958},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":958},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":958},{"title":"Contents","target":"https://saliu.com/content/index.html","line":958},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":958},{"title":"Home","target":"https://saliu.com/index.htm","line":958},{"title":"Search","target":"https://saliu.com/Search.htm","line":958},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":958},{"title":"The site for wheels, wheeling numbers for Euromillions.","target":"https://saliu.com/HLINE.gif","line":960}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07788794,-0.03862328,-0.01025702,-0.01979511,-0.07153697,0.03101663,-0.01885177,0.01668038,0.05999263,-0.00056591,-0.00964324,-0.04975907,0.03033119,0.01333499,-0.02978902,-0.022614,0.00866062,-0.00878511,-0.12512173,0.00188123,0.11078272,-0.05206686,-0.0397853,-0.07456008,-0.00613074,0.00075749,-0.01419714,-0.01935299,0.00492037,-0.23848331,0.03849046,-0.01083798,-0.03136326,-0.01972216,-0.04938329,-0.04266913,-0.06703179,0.02428601,-0.1037898,0.00925135,0.02153414,0.04375176,0.00818182,-0.00089549,0.05476157,-0.00816667,-0.0460203,0.04529711,0.0338264,0.00899446,-0.02114771,-0.01039822,0.01349338,0.00819558,0.02316798,0.04403932,0.02334042,0.08897562,0.05665959,0.06369976,0.08130143,0.07120913,-0.19147025,0.02321093,-0.00584126,0.02439905,-0.00577153,-0.06383064,0.01127526,0.04100045,0.0560794,0.01463588,0.01993264,0.08216617,0.03321085,-0.01992387,-0.04967952,-0.07937258,-0.08995104,0.06293937,-0.02445796,0.00902117,0.00444562,-0.01645279,0.02044007,0.02734948,0.05090707,0.02154025,0.0562151,-0.07097202,-0.0271256,-0.00673986,0.04721378,0.05281745,-0.02716923,0.01170012,0.01346745,-0.00902443,0.01350837,0.11627956,0.01361223,0.03347476,0.03307095,0.00409702,0.01499282,0.02064716,0.00691913,-0.00983473,-0.03752652,0.02561452,0.04263404,0.00583105,0.03948751,-0.01545548,-0.07418479,-0.02596233,0.01145069,-0.00951008,0.00712031,0.05600329,-0.04005379,0.04949022,0.01702855,-0.02695851,0.01519785,0.05023478,0.03275565,0.04349322,0.03015952,0.01871655,0.02208822,-0.00807131,-0.12229214,-0.03088447,0.00817317,-0.02849197,-0.00509305,0.00118665,0.03514137,0.04916061,-0.00678756,-0.05642993,0.02552278,-0.10931318,-0.05953679,0.0542139,-0.00188747,-0.0285402,0.00302052,0.01120991,0.00562479,0.00166277,-0.00366624,-0.04810533,0.01069645,0.05271356,0.11802445,0.07767086,-0.05228868,0.01630756,-0.03366237,-0.03225192,-0.06653689,0.15352617,-0.05412301,-0.1080519,-0.01842946,0.07641494,-0.00114023,-0.10153357,-0.00568949,0.01733893,-0.07561535,0.03810949,0.08145883,0.0002243,-0.08761264,-0.00913248,-0.03332905,-0.02849803,0.02338943,-0.04221889,-0.03989741,0.03672173,-0.01658794,-0.05576278,-0.01903985,-0.01984165,0.05092055,0.04667641,-0.02390717,0.04992807,0.02647699,0.01469421,-0.03001199,-0.03525881,-0.00997376,-0.01320727,0.05166341,-0.0175205,0.01806499,0.06262831,0.01592161,0.05835065,0.00697677,0.01823088,-0.03874507,-0.05479817,0.03869238,0.04455893,-0.04239709,-0.00504352,0.03337422,0.06038936,-0.08676491,0.04876129,0.01019787,0.0283341,-0.01058477,-0.02006037,0.00236453,-0.01114303,-0.09352839,-0.18440454,-0.05257387,-0.03360078,0.01342449,0.04202236,0.00509774,0.0111959,-0.02667306,0.03302781,0.0078565,0.05936611,-0.04342422,-0.01445353,0.00990235,-0.00889744,0.06347616,-0.11117457,-0.01083685,0.00090588,0.03580143,0.02843668,0.04695068,0.00221557,-0.11543266,0.00707196,-0.04683045,0.14326937,-0.00209217,0.00043414,-0.0666615,0.0879026,-0.03147003,0.00704848,0.03531911,0.01689195,0.01742591,-0.08369743,-0.02857656,-0.04582802,-0.01236826,-0.05626502,-0.02389038,-0.00602048,-0.10681138,0.00758434,-0.02354956,0.00622713,-0.0258592,0.01736664,0.06770096,-0.00351901,0.03238685,0.02880552,0.0233167,0.08721462,-0.01399596,-0.05450704,0.00988391,-0.01427055,0.00780191,-0.00981976,-0.03390684,0.07318692,-0.05494009,0.05483688,0.01628949,-0.03876498,-0.05616499,-0.00784298,-0.03451153,-0.00611137,0.09409209,0.02878139,0.07500009,-0.04681649,0.05022492,0.00567739,0.03028281,0.02386799,0.01426785,-0.0396191,-0.00996903,0.03568316,0.07619841,0.01198059,0.05587903,0.03775085,0.03066227,-0.03078714,0.03057075,0.00543195,0.04813337,-0.04249985,0.04897307,0.02568058,0.04542115,-0.25909823,0.04864012,-0.02628447,0.019382,0.00489026,0.01220641,0.02443037,0.01842263,0.03121736,-0.0528341,0.05994354,0.05817769,0.02452276,-0.02249752,0.0017038,-0.0080998,0.022186,-0.03133286,0.09502829,-0.01603167,0.03144485,0.01378759,0.22805409,0.01986514,0.01761741,0.02744799,-0.0014489,-0.01077297,-0.0260252,-0.00479146,-0.03098819,0.02081706,0.0711849,-0.00650753,-0.0247456,0.02845388,-0.06227539,0.00703915,-0.03331155,0.00911015,-0.05333022,-0.00194258,0.02176106,-0.00458947,0.12179402,0.03082453,-0.01014811,-0.12791204,0.05463814,0.01620909,-0.04625752,-0.03006217,-0.09605975,-0.05581957,0.0272394,0.00913328,0.00806712,0.00784132,-0.00168896,0.00263616,-0.02346984,-0.03310532,0.00354851,0.00475247,-0.02186734],"last_embed":{"hash":"1fogx8b","tokens":420}}},"text":null,"length":0,"last_read":{"hash":"1fogx8b","at":1753423455750},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#{1}","lines":[10,931],"size":21049,"outlinks":[{"title":"The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.","target":"https://saliu.com/ScreenImgs/lexico-lottowheels.gif","line":7},{"title":"The software function creates lotto wheels for Euromillions lottery game in Europe.","target":"https://saliu.com/ScreenImgs/euromillions-wheels.gif","line":11},{"title":"Euromillions, Euromillones: Wheels, Wheel, Wheeling.","target":"https://saliu.com/HLINE.gif","line":919},{"title":"![Euro Millions, EuroMillions, Euromilhoes Wheels, Combinations.","target":"https://saliu.com/back_cmp_valueadd110_back_a.gif","line":921}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08610058,-0.06317412,-0.00929065,-0.03359552,-0.04655487,0.03099269,-0.0057904,0.00328643,0.02357135,-0.02181638,0.00608594,-0.02309999,0.03086445,0.00367767,-0.00781066,0.00494342,0.02483495,0.01900083,-0.10665128,-0.03372271,0.08563816,-0.04736572,-0.05373281,-0.07739122,0.00031594,0.01360499,-0.03332564,-0.03063696,-0.02857125,-0.22406925,0.03615592,0.01944707,-0.0298,-0.0268539,-0.07308491,-0.0221247,-0.04598928,0.01207718,-0.10128883,-0.01543001,0.02829496,0.05218817,-0.00782029,-0.0119633,0.04075436,-0.02823528,0.0217085,0.02475401,0.0772562,0.02185214,-0.01980454,-0.00908847,0.0318836,0.01476508,0.03552489,0.03351773,0.037708,0.083722,0.03962155,0.05255473,0.044076,0.08590886,-0.15825143,0.063756,-0.00297368,0.02899642,0.01825708,-0.09082174,0.0150052,0.04962286,0.04636458,0.04217886,-0.0031902,0.08811799,0.03083907,-0.01097972,-0.06258804,-0.08841837,-0.07281305,0.05154732,-0.05576427,0.02157625,-0.00580155,-0.00762372,0.01619669,0.03960714,0.02306722,0.02206628,0.07083804,-0.07969829,-0.0278888,0.01276632,0.05928011,0.05188801,-0.02650096,0.00337993,0.04156924,-0.02307972,0.0341539,0.10794865,-0.01151344,0.01858428,0.00981495,-0.01478189,0.00774873,-0.02814821,-0.01678351,-0.00785392,-0.02947386,0.03765185,0.05983493,0.00069721,0.05334471,0.00571294,-0.05292047,-0.01831886,0.00583839,-0.01316349,0.01714061,0.03594719,-0.06457985,0.00589888,0.06529196,0.01836312,0.01124938,0.0042347,0.03579303,0.04240428,0.02168852,0.03011163,0.02989648,0.0086867,-0.12784748,-0.04269169,0.01406285,-0.03063833,0.0132029,0.00835928,0.00537659,0.03259891,-0.0072297,-0.06697102,0.06826153,-0.09819403,-0.04648499,0.04409287,-0.00535298,-0.02333243,-0.015732,-0.01924602,-0.00126524,0.00580078,0.01792027,-0.02104789,0.0373861,0.02870971,0.13952875,0.07641438,-0.05319681,0.03666924,-0.05460799,-0.04355616,-0.05227939,0.14080118,-0.02592517,-0.09282832,-0.00632859,0.05314102,-0.02500156,-0.07790737,-0.00776417,0.04242801,-0.0772991,0.02111435,0.11444511,-0.02015731,-0.06360029,-0.01259151,-0.02300781,-0.02594412,0.00646856,-0.04000054,-0.03591875,0.00675938,-0.01394057,-0.07753146,0.01605316,-0.00859532,0.01798125,0.04005009,-0.02989236,0.04308365,0.03968152,-0.0059865,-0.03173728,-0.02155576,-0.061186,-0.00049044,0.06619833,-0.0357725,0.01228978,0.05079685,0.0353886,0.04089667,0.0124728,0.03087912,-0.05732763,-0.08422915,0.06536028,0.02346945,-0.04263245,-0.0021306,0.06015953,0.06599355,-0.09827183,0.03590395,0.03014179,0.02486553,-0.02824226,0.00940498,0.01258793,-0.00539882,-0.09218788,-0.19548717,-0.03463478,-0.02150728,0.07808577,0.05363569,-0.02559163,0.01307496,-0.04254625,0.02247603,0.06639578,0.04721547,-0.07434791,0.0000712,0.03034264,0.00262909,0.047841,-0.11411013,-0.04641294,-0.05780698,0.06315681,0.04095306,0.0432804,-0.00564574,-0.11496949,0.04311601,-0.03580088,0.13303834,-0.00792788,0.00442299,-0.03881038,0.06793977,-0.0164731,0.01561324,0.00292945,0.02641461,0.03280145,-0.03217182,-0.04858559,0.00493919,-0.02180757,-0.05717413,-0.00747121,-0.00941941,-0.10956426,0.02465792,-0.0293816,-0.00564594,-0.04133455,0.03642097,0.0373373,-0.0043082,0.04159892,0.03727666,-0.00133429,0.06795447,-0.0221742,-0.04701499,0.01371203,-0.00436086,0.04194363,-0.0337143,-0.02558038,0.07076414,-0.0819271,0.06567546,-0.00256005,-0.03148505,-0.03408987,0.01732574,-0.02263566,-0.00683031,0.07302687,0.01421172,0.09870294,-0.04329788,0.03820655,0.02526282,0.04683023,0.02395733,-0.00102939,-0.04220484,-0.02103275,0.03790261,0.09721703,0.00905008,0.02402166,0.05202952,0.02716452,-0.00923906,0.02758719,0.01418825,0.03919138,-0.04916937,0.03673941,0.03792365,0.0673365,-0.24040791,0.03226053,-0.03161013,0.03599319,-0.02648681,-0.0261151,-0.00347674,-0.00751006,0.04311866,-0.06947181,0.04631283,0.03759032,0.03120596,-0.02153671,0.02149665,0.02064829,0.04603647,-0.03354394,0.06274872,-0.03773129,0.04129454,-0.00166143,0.23798662,0.00778124,0.01235209,0.03834283,-0.0299064,-0.00296154,-0.0272365,-0.01988643,-0.0164453,0.02424822,0.0515369,-0.01677427,-0.04719266,0.01431779,-0.06518826,0.01188981,-0.03330842,0.00714551,-0.06202213,0.01284767,-0.01694647,-0.0173134,0.09744364,0.01364281,0.00920972,-0.12134414,0.04935683,0.05565859,-0.05405243,-0.02688592,-0.08133835,-0.04678532,0.03944377,0.0334168,-0.00224828,-0.03076852,-0.01764098,-0.01977642,0.00327577,-0.04999176,0.03269533,-0.00300738,-0.00857947],"last_embed":{"hash":"662y1q","tokens":399}}},"text":null,"length":0,"last_read":{"hash":"662y1q","at":1753423455882},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)","lines":[932,968],"size":3786,"outlinks":[{"title":"<u>Resources in Euromillions, Lottery Software, Wheels</u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lottery Software Tools, Lotto Wheeling Software Wheels**_","target":"https://saliu.com/free-lotto-tools.html","line":5},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":18},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":20},{"title":"The myth of lotto wheels or abbreviated lotto systems","target":"https://saliu.com/bbs/messages/11.html","line":22},{"title":"WHEEL-632 available as free lotto wheeling software","target":"https://saliu.com/bbs/messages/wheel.html","line":24},{"title":"lottery software for players of lotto wheels","target":"https://saliu.com/bbs/messages/857.html","line":25},{"title":"Software to verify lotto wheels","target":"https://saliu.com/check-wheels.html","line":26},{"title":"Wheels, balanced lotto wheels, lexicographic order","target":"https://saliu.com/bbs/messages/772.html","line":27},{"title":"Check WHEEL and lotto wheels for winners","target":"https://saliu.com/bbs/messages/90.html","line":28},{"title":"Genuine Powerball wheels","target":"https://saliu.com/powerball_wheels.html","line":29},{"title":"Genuine Mega Millions wheels","target":"https://saliu.com/megamillions_wheels.html","line":30},{"title":"The reduced Euromillions systems are created by free lotto wheeling software.","target":"https://saliu.com/HLINE.gif","line":32},{"title":"Forums","target":"https://forums.saliu.com/","line":34},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":34},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":34},{"title":"Contents","target":"https://saliu.com/content/index.html","line":34},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":34},{"title":"Home","target":"https://saliu.com/index.htm","line":34},{"title":"Search","target":"https://saliu.com/Search.htm","line":34},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":34},{"title":"The site for wheels, wheeling numbers for Euromillions.","target":"https://saliu.com/HLINE.gif","line":36}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07686275,-0.07455996,-0.00609379,-0.05220171,-0.04830613,0.02021313,-0.00845765,-0.00637558,0.04043107,-0.0146032,0.00005566,-0.0236927,0.02044961,-0.00613787,0.00617949,0.0043597,0.02502744,0.03512477,-0.09366836,-0.00570816,0.0478683,-0.05627828,-0.04321772,-0.08342631,0.00495809,0.0479465,-0.03199493,-0.03915621,-0.00939963,-0.20227867,0.04068607,0.0364784,-0.05247843,-0.01030875,-0.07833584,-0.0230722,-0.05989558,0.00117478,-0.09530784,0.01218332,0.0332872,0.03337898,-0.00944699,0.00641949,0.02006519,-0.01655052,0.03795766,0.01984817,0.0914904,0.0300137,0.01252447,-0.02386041,0.03344211,0.01557064,0.04069466,0.04945124,0.03150303,0.05687558,0.02562926,0.04838704,0.0445801,0.07977431,-0.15172549,0.06096083,-0.00047487,0.04140049,0.02152679,-0.07382861,0.00029531,0.05064912,0.01810068,0.01594592,-0.00941425,0.05773659,0.0280061,-0.02574376,-0.04981739,-0.09112977,-0.05532261,0.04424411,-0.03193326,0.00455295,0.00468414,-0.00488593,-0.0232564,0.02821188,0.03963363,0.02035338,0.06307035,-0.09269524,-0.03913097,0.02111818,0.07132684,0.05678686,-0.03835373,0.02000712,0.0486565,0.00409417,0.02321164,0.13062094,-0.03100703,0.00313073,0.02711281,-0.00901511,0.03550952,0.00283312,-0.0114702,0.00958827,-0.04650828,0.06101321,0.06291458,0.01112365,0.04890522,0.01058368,-0.05257688,-0.03672398,-0.01139568,0.01648505,0.01372084,0.05447945,-0.06060782,-0.01915646,0.02697334,0.00941874,0.00715006,-0.00991058,0.03908065,0.04545641,0.0014893,0.0360215,0.02228227,-0.01587646,-0.12485842,-0.02556028,0.00593606,-0.01629449,0.02060495,0.03559695,-0.01518094,0.03430184,-0.01330754,-0.09579138,0.02871693,-0.1035888,-0.03662166,0.05344251,-0.00269795,-0.01623672,0.02431925,-0.00133076,-0.01676351,0.0110387,0.0002973,-0.02448812,0.04003005,0.01167434,0.13123141,0.05365484,-0.04010226,0.03486115,-0.05286155,-0.06237974,-0.03818432,0.1336803,-0.03224126,-0.09300898,-0.03567443,0.01506685,-0.00976085,-0.06772389,-0.01881827,0.03928083,-0.08666133,-0.00056418,0.15290622,0.00192461,-0.06739549,-0.01816839,-0.04598564,-0.03852613,-0.00274356,-0.03374884,-0.03100865,0.00547942,-0.01435255,-0.07750037,-0.0008519,0.01605537,0.00063025,0.04919232,-0.00916022,0.02515312,0.00642947,-0.02076471,-0.03071275,-0.0209355,-0.06919526,0.00819345,0.05779433,-0.03693679,-0.00467142,0.05373409,0.01009803,0.07370336,0.01802504,0.03678288,-0.04670528,-0.07979228,0.09153284,0.03142058,-0.05100827,0.00368193,0.04638939,0.04931809,-0.07856184,0.054464,0.03144505,-0.00327305,-0.02824082,0.01148799,0.00965141,0.00189311,-0.10817006,-0.18608622,-0.0390694,-0.00584919,0.0480864,0.07049815,-0.02628803,0.00804074,-0.02661546,0.06862458,0.05979035,0.06321867,-0.06675965,-0.00922603,0.01637959,-0.00108339,0.03611824,-0.07591745,-0.05741736,-0.04117885,0.05665714,0.04674794,0.03969368,-0.02263267,-0.10897427,0.04815914,-0.0297304,0.12062755,-0.01916062,-0.0069123,-0.03034538,0.04211656,-0.02211909,0.01125432,-0.0001864,0.01417369,0.01294328,-0.05383593,-0.04565295,0.00916324,0.00016037,-0.06533442,-0.02286933,-0.01186283,-0.08506885,0.04095527,0.01344948,0.00519282,-0.04363865,0.01225768,0.03222708,-0.01866129,0.05693851,0.05390038,0.03073495,0.05741857,-0.03423449,-0.02100192,0.01123981,0.00135687,0.05690084,-0.06034489,0.00804389,0.05882049,-0.06616888,0.06109327,-0.01559497,-0.04866797,-0.06537711,0.00466606,-0.03899785,-0.00434969,0.04299017,-0.0054773,0.06515318,-0.06993634,0.03612388,0.01691862,0.08126616,0.03022648,-0.00207896,-0.05123984,-0.01867653,0.03307296,0.09743376,0.03189287,0.03432859,0.04390157,0.02012152,-0.04246766,0.05690178,0.0257967,0.03650126,-0.02688056,0.0203654,0.05629374,0.0429476,-0.24103284,0.05025095,-0.01960385,0.02696905,-0.02352279,-0.03776582,-0.02594658,0.03984946,0.01902106,-0.07179732,0.10760789,0.03575208,-0.01095596,-0.01053626,0.02297042,-0.00794846,0.04912966,-0.02291419,0.07338921,-0.03767331,0.047033,0.02158405,0.26299429,0.02033785,0.03647149,0.036692,-0.02559474,-0.00551376,-0.00682485,0.00232998,-0.00452812,0.00958582,0.06232404,0.00086375,-0.06349406,0.00425232,-0.06239101,0.01151183,-0.04300148,0.03531243,-0.0595857,0.00825011,-0.0306701,-0.01322248,0.07529165,0.02493195,0.01177599,-0.11045147,0.05250522,0.06825156,-0.0468664,-0.01526716,-0.10496757,-0.05286158,0.01764745,0.03026641,-0.00498514,-0.02985831,0.00962265,-0.03748262,-0.00278499,-0.06638525,0.03364396,0.00913231,-0.02129477],"last_embed":{"hash":"rx2fpm","tokens":132}}},"text":null,"length":0,"last_read":{"hash":"rx2fpm","at":1753423456011},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{9}","lines":[947,947],"size":290,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06896387,-0.08307332,0.01023669,-0.03376415,-0.04302061,0.06198176,-0.00014444,-0.01046701,0.04107854,-0.01677345,0.00504612,-0.00032318,-0.00503125,0.00702523,-0.01213249,0.0136972,-0.00613492,0.03134091,-0.12160418,-0.03315333,0.05203636,-0.01372577,-0.06655253,-0.08082979,-0.00516701,0.00406595,-0.03456699,-0.02714008,-0.04726262,-0.20114827,0.06096407,0.00302042,-0.06370147,-0.03761629,-0.07366238,-0.05162418,-0.03211677,0.03214572,-0.0764564,0.00836388,0.04819559,0.04016624,-0.01860684,-0.03000258,0.02713124,-0.00006243,0.00491593,0.0324546,0.05245028,0.03746993,-0.03203838,-0.01230173,0.03204195,-0.01629451,0.0174961,0.02516529,0.0272747,0.07956509,0.04215281,0.05794477,0.02328706,0.08273648,-0.12588283,0.03537018,-0.02159268,0.00609876,0.02321205,-0.05996163,0.00955868,0.08421972,0.07880328,0.0359298,-0.00711323,0.10213633,0.05063546,-0.0175376,-0.05737801,-0.08572841,-0.03966879,0.08463939,-0.05658253,-0.01736799,-0.01043248,0.0116348,-0.00777565,0.01001011,0.00783504,-0.01714185,0.05275349,-0.08618347,-0.01150673,0.02489658,-0.00712599,0.07082853,-0.01049232,-0.03066413,0.03298311,-0.03937929,0.05852464,0.13122694,-0.02044208,0.02231022,0.02491796,0.04799322,-0.02661626,0.00773224,-0.04686587,0.00266034,-0.03371162,0.03434956,0.08017375,-0.0247578,0.05854416,-0.03036511,-0.04517049,-0.00775637,-0.01078974,0.01659268,0.01471971,0.01203521,-0.05246182,0.02077308,0.05829902,0.01633386,0.01413083,0.01171337,0.03935425,0.06605308,0.00738645,0.01999816,0.04286696,-0.04315034,-0.13779223,-0.05226951,-0.00912634,-0.02063199,-0.00526153,-0.00479491,0.02605736,0.01492569,0.02256265,-0.04714339,0.05028351,-0.11013269,-0.04267841,0.05629219,-0.04386707,-0.01408862,0.02465429,-0.01096672,-0.00366994,-0.02440553,0.04007599,-0.0433244,0.03136868,-0.01163383,0.1570749,0.04165247,-0.01689088,0.03616622,-0.09030547,-0.05383236,-0.00907634,0.14725281,-0.02942512,-0.064232,-0.01512544,0.03014896,0.00319521,-0.1089957,-0.0243598,0.02620484,-0.04005713,0.06858952,0.09614528,0.03348301,-0.08014993,-0.0241769,-0.01955672,-0.01746139,0.0235969,-0.03090287,-0.03619915,0.02196085,-0.02879367,-0.07248443,0.00397818,-0.01037519,0.01102218,0.03413325,-0.04730752,0.04326154,-0.00460566,-0.03013156,-0.01714318,-0.03694315,-0.02310969,-0.02700583,0.04879788,-0.00684219,-0.01303544,0.05404035,0.02645771,0.07936626,0.03400176,0.01713687,-0.01118428,-0.10611833,0.05703453,0.03471821,-0.03468822,-0.00731049,0.04212854,0.0621119,-0.07371823,0.05688491,0.01039455,0.04569671,0.032151,-0.06174393,0.04191413,-0.03906786,-0.07872708,-0.17886142,-0.05282561,0.0061094,0.08087877,0.06681281,-0.00809565,-0.00523927,-0.02289824,0.02804882,0.03151342,0.02431993,-0.07885225,0.01980182,0.05052786,0.01531542,0.05582054,-0.11564073,-0.01969754,-0.04488118,0.03430804,0.02842735,0.07771411,-0.02166449,-0.10729254,0.06729917,-0.02091449,0.13912289,-0.01117689,0.02654336,-0.04732668,0.06595153,-0.01662221,0.01219345,-0.01569884,0.00533809,0.04374893,-0.06054819,-0.0733517,-0.04059205,-0.01365707,-0.03266872,-0.00894896,0.02177205,-0.09909175,0.00027119,-0.02697229,-0.0284389,-0.01130772,0.05092591,0.03944094,-0.01119209,0.00626538,0.03326761,0.01399568,0.07431632,-0.02964129,-0.01528745,-0.02200959,0.01091851,0.01974683,-0.00938208,-0.01544709,0.07769953,-0.08177968,0.03324879,-0.00225658,-0.00657569,-0.03162343,-0.00994279,-0.01949712,0.0236996,0.09622869,0.03726019,0.08631644,-0.03208339,0.03972159,-0.01432126,0.04680052,0.03091929,0.0056063,-0.01083082,-0.01923692,0.01552996,0.10581128,0.01944713,0.00544356,0.03051226,0.03793836,-0.03093984,0.03048691,0.0253858,0.04320177,-0.07133958,0.07171049,0.0284806,0.00575002,-0.23530881,0.04892204,-0.02933827,0.03975769,0.03014081,-0.01914161,0.00486439,0.02840883,0.03752045,-0.01525127,0.03028272,0.0232063,0.04777187,-0.02092333,0.01665884,0.0088775,0.05491703,-0.02543879,0.06306478,-0.05828403,0.0646556,-0.00728124,0.23669983,0.04193106,0.03699141,0.04719134,-0.01690005,0.00317675,-0.05136901,-0.02693253,0.00267226,0.00302528,0.02366136,-0.02878557,-0.03662317,-0.01727579,-0.049621,0.02987394,-0.03342317,-0.00406344,-0.07467882,-0.00858655,-0.00297256,-0.02385104,0.09253971,0.01525861,0.00945833,-0.09712745,0.0278307,0.05770349,-0.05827935,-0.02557279,-0.08538927,-0.074771,0.00596574,0.01887478,0.01508054,-0.01522646,0.0195086,0.00605935,0.00845947,-0.07536283,0.01367671,-0.03152314,-0.03931248],"last_embed":{"hash":"1wzy36m","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"1wzy36m","at":1753423456055},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{10}","lines":[948,948],"size":220,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06820376,-0.066166,-0.01068716,-0.01548255,-0.05005057,0.02849104,-0.00661778,0.01063562,0.03562217,0.0033267,-0.00622662,-0.03892997,0.01963405,0.00103184,-0.01589949,-0.01692286,0.03045592,0.06008486,-0.08709375,-0.01207779,0.06482806,-0.03329808,-0.0907113,-0.10125708,0.01198143,0.0332846,-0.02687339,-0.04209987,0.00196679,-0.16185969,0.01371101,0.00110451,-0.04509398,-0.04047541,-0.06250963,-0.03551481,-0.0419988,0.01831327,-0.11368928,0.0033758,0.03511204,0.04480264,-0.00654071,-0.00176004,0.02470317,-0.02773928,0.027546,0.05323558,0.06928212,0.00009435,0.00258618,0.0111942,0.00535451,0.01030021,0.02414324,0.04962474,0.03615495,0.08484659,0.00150156,0.02247685,0.02567362,0.06977882,-0.16435085,0.04773109,-0.01211312,0.0598749,0.02865423,-0.02698999,0.02586202,0.04480094,0.05201287,0.01438755,-0.00176985,0.08886331,0.03859157,-0.01209938,-0.04544149,-0.08134692,-0.04478891,0.03189794,-0.04360423,0.01368208,-0.00034542,0.03232274,-0.01853083,0.05052653,0.02642149,0.02062865,0.09420771,-0.11068559,-0.02577356,0.00787758,0.04581003,0.04835102,-0.01353913,-0.03038458,0.04300923,-0.02178356,0.0331769,0.1330982,-0.00871354,0.00135497,0.04783793,-0.03100291,0.03348364,-0.02134324,-0.02040059,-0.021219,-0.0529527,0.06865275,0.07829879,0.01719665,0.02170388,-0.02161452,-0.03553862,-0.0452697,-0.02008512,-0.00239449,0.02000824,0.02641217,-0.05868415,0.02725123,0.02201968,0.001129,0.01192503,-0.00268475,0.00616641,0.06354988,-0.00042268,0.01348756,0.04825683,-0.00804775,-0.13908009,-0.03615249,-0.0104467,-0.03174419,0.02056897,0.00253699,0.00207636,0.07583044,-0.02114156,-0.06932487,0.05157559,-0.13840775,-0.03316896,0.05160456,0.00752047,-0.0145266,-0.01318946,0.01063044,-0.01484042,-0.00537164,0.01176237,-0.0459406,0.00980306,0.0117714,0.12080119,0.02638801,-0.04017029,0.03851061,-0.04988829,-0.03926039,-0.05907363,0.17453296,-0.0314527,-0.10810093,-0.03388336,0.06392899,-0.01775478,-0.08540226,-0.00556461,0.02892993,-0.09205434,0.00432274,0.15516086,-0.02549068,-0.10182093,-0.02516146,-0.04251148,-0.02921374,0.0095086,-0.04388083,-0.02169598,0.00442095,-0.00240533,-0.0479167,-0.01365764,-0.02046185,0.02148269,0.05071362,-0.01949595,0.04756556,0.00150428,-0.02303169,-0.0616238,-0.00843778,-0.06192635,-0.01825573,0.07919563,-0.00718004,-0.01003977,0.06864011,0.00498742,0.04957199,-0.00629013,0.02069684,-0.03805678,-0.05937913,0.06425004,0.04995289,-0.05139592,0.01623137,0.05408675,0.02660243,-0.08682899,0.0278613,0.04767409,0.04043868,-0.02849937,0.00076409,-0.00300102,-0.0012284,-0.10369433,-0.17398898,-0.04156965,-0.00800589,0.02298584,0.04356395,0.00127868,0.0110384,-0.0320565,0.017424,0.04712849,0.08995473,-0.06546864,0.01581318,0.02972309,-0.00123208,0.02283731,-0.09290634,-0.04070453,-0.02993239,0.03259075,0.03014672,0.00773161,0.0145172,-0.1195549,0.00918224,-0.00956902,0.12822597,-0.02748729,0.03093978,-0.02562445,0.05247206,-0.02689273,-0.00626202,-0.02135688,0.00300912,0.04394354,-0.0730337,-0.04303563,-0.01175704,-0.00801854,-0.03547772,-0.04263839,0.01399338,-0.06500264,-0.00674838,-0.01166161,-0.00741279,-0.02194869,0.028666,0.05443862,0.00026012,0.04847512,0.06042591,0.01894443,0.04278902,0.00607302,-0.05494963,0.00760124,-0.02005697,0.05204092,-0.01951863,-0.02537961,0.03451404,-0.03667497,0.07687074,-0.00373608,-0.05702601,-0.05391825,-0.0072076,-0.0335936,0.00541349,0.08984023,0.01294059,0.06993222,-0.02009853,0.02711977,0.03362866,0.01478381,0.01512157,0.02953856,-0.074368,-0.03783468,0.03670745,0.09891672,0.05536587,0.06415831,0.03930731,0.03529656,-0.04986935,0.00459403,0.01210666,0.02896534,-0.05874263,0.0138303,0.03315945,0.04468424,-0.24286105,0.05095586,-0.02199701,0.02221789,-0.02551898,-0.05195741,0.0114332,0.0158212,0.02969435,-0.05877229,0.10474116,0.01019226,-0.00040647,-0.0132899,0.03444772,-0.00484718,0.03770414,-0.02651862,0.0809283,0.01080036,0.03746684,0.03896319,0.2504929,0.0289831,0.05089749,0.0304337,-0.0099569,-0.00305154,-0.01306681,0.00652468,0.00614692,0.02386757,0.02574999,0.01926031,-0.05830982,0.04555666,-0.04586436,0.02246325,-0.02216515,0.0334169,-0.06424442,0.01207883,-0.00044115,0.03127937,0.08216146,0.01170093,0.01209871,-0.1258221,0.05920915,0.05083251,-0.05785397,-0.00880918,-0.08825692,-0.05311467,0.0319871,0.02179488,0.02854859,-0.04620857,0.01537467,-0.02208588,-0.00179972,-0.04834236,0.01726127,-0.00496852,-0.02907598],"last_embed":{"hash":"1m22jsz","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423456090},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{12}","lines":[950,950],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{24}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09147818,-0.05327982,-0.00432623,-0.0107541,-0.03661964,0.04876866,-0.05629608,0.05786011,0.05769353,-0.00666096,-0.02624345,-0.06741712,0.02697404,0.00681131,-0.03029047,-0.04713905,0.01622179,0.0197755,-0.09003965,0.00189947,0.07729529,-0.04922823,-0.05614924,-0.07677668,0.01350362,0.00690807,-0.01851638,-0.02034514,0.00007343,-0.20488776,0.02434099,-0.01173721,-0.01822782,-0.00612911,-0.05792905,-0.00970655,-0.03878961,0.01177419,-0.08463997,0.01836043,0.04676417,0.04316621,0.00856764,0.02494011,0.04366423,0.01050953,-0.0021376,0.05127452,0.02873666,-0.01079203,-0.01349539,0.00595244,0.00351867,-0.01353391,0.04950931,0.04328568,0.03495609,0.08871039,0.0134695,0.04263785,0.03127919,0.05276747,-0.20477988,0.04977053,0.00160898,0.03599878,-0.00748194,-0.04510919,0.05243346,0.02282255,0.03249619,0.00824188,-0.00353426,0.07761012,0.02501126,-0.0305519,-0.06390271,-0.07491719,-0.04768972,0.00522726,-0.01670445,0.0211385,-0.02708868,-0.0009988,0.00808336,0.04348367,0.02413542,0.02286647,0.06126368,-0.10647319,-0.02021595,-0.02789523,0.0652283,0.05663883,-0.05216502,-0.010938,0.00984762,0.00195499,0.00523103,0.11173785,0.0102293,0.01108222,0.04581665,0.00165193,0.03342396,0.00168321,-0.00820367,-0.01477205,-0.03165299,0.04548306,0.06908977,0.01563185,0.01426509,-0.01839234,-0.03632488,-0.06233718,0.00397516,0.01249336,0.00525641,0.05842957,-0.08207037,0.01515383,0.06318704,0.00435481,0.04044244,0.02488798,-0.00730017,0.03109887,0.03026748,0.03737811,0.04291156,-0.012628,-0.12177434,-0.06450864,-0.00373651,-0.02331797,0.00020672,0.00329124,0.02197286,0.0573444,-0.00909619,-0.08512922,-0.00084757,-0.11499007,-0.03648782,0.04690018,0.01498254,-0.02185658,0.01462032,0.03214771,-0.02101729,0.0059863,0.0337569,-0.0314397,0.02518701,0.02392876,0.10271937,0.0630087,-0.03804306,0.06428235,-0.02449785,-0.07219139,-0.05972059,0.19322245,-0.02922652,-0.09234793,-0.04992391,0.0813356,0.01579006,-0.08171427,0.00880187,0.02061083,-0.07967816,0.01546765,0.12790096,-0.02915093,-0.06350874,-0.01277821,-0.03396556,-0.01504153,-0.0099243,-0.04179497,-0.02889879,0.02367827,-0.04029974,-0.04596398,-0.00154944,-0.02189613,0.0141616,0.05404016,-0.05058162,0.01337445,0.01073647,-0.01235995,-0.06766486,-0.00824579,-0.02494854,-0.01365404,0.0586583,0.01393073,-0.00316958,0.0682202,-0.01698354,0.04476093,-0.02873861,0.05347765,-0.02578204,-0.04689423,0.05436281,0.03284709,-0.01463182,0.02887056,0.05696102,0.01138769,-0.05790686,0.02650141,0.03752303,0.01110039,-0.01721473,-0.04371587,0.01227551,-0.00957586,-0.11212252,-0.19345759,-0.02408047,-0.02016846,-0.01966073,0.04431373,-0.01518076,0.00932411,-0.03856836,0.03030804,0.02718782,0.08954746,-0.04510014,-0.00182994,-0.01517934,0.01657548,0.03648381,-0.08751572,-0.05830416,-0.02404775,0.04280927,0.02208575,0.03272267,0.00996472,-0.11278174,0.00430794,-0.02737604,0.1188327,0.00152593,0.03443213,-0.0463523,0.06594993,-0.00409985,0.00672331,0.00115566,0.01198023,0.03639536,-0.08640258,-0.02597798,-0.0083418,-0.02297916,-0.06229228,-0.00374799,-0.0224481,-0.05377867,-0.01223234,-0.01880076,-0.00547696,-0.04885656,0.030109,0.05653126,0.01533857,0.04021168,0.04503611,0.01602162,0.05770648,0.00144862,-0.08162757,0.00907211,-0.02604026,0.04841091,-0.03773525,-0.00563637,0.07299707,-0.06180919,0.0582977,0.008927,-0.08501334,-0.04535791,-0.01893644,-0.01720109,-0.01239081,0.09874708,0.03421029,0.06259859,-0.02654983,0.00703616,-0.04851815,0.04636428,0.0117853,0.02437748,-0.07887244,-0.03024901,0.03993111,0.08074612,0.02965692,0.07473057,0.02769722,-0.01123362,-0.0304571,0.00649316,0.01827342,0.02616017,-0.05801403,0.04265173,0.07561323,0.03542675,-0.24912865,0.06364579,-0.00150227,-0.00330629,-0.00920813,0.00271241,0.00826746,0.01967113,0.03170811,-0.06288411,0.1105798,0.02858734,-0.00474909,0.00636501,0.00967766,0.01755196,0.00189783,-0.02282492,0.08042596,-0.01645597,0.0180852,0.03592842,0.23895253,0.02669363,0.00454225,0.03840083,0.01550828,0.01455296,0.00960437,0.00580466,-0.02803071,0.0095343,0.07738456,0.00534798,-0.04723636,0.02969591,-0.0866955,0.0219465,0.00017413,0.00064848,-0.07423379,0.01432195,0.02363647,-0.00090912,0.11414617,0.02866418,0.00953816,-0.11586304,0.07474443,0.05332781,-0.02257802,-0.0109493,-0.06530774,-0.01932685,0.03713931,0.01379959,0.02467159,-0.03914968,0.03556865,-0.02353792,-0.00784232,-0.04013402,0.00632997,-0.01390076,-0.02252826],"last_embed":{"hash":"d4k5f0","tokens":297}}},"text":null,"length":0,"last_read":{"hash":"d4k5f0","at":1753423456123},"key":"notes/saliu/Euromillions Wheels, Wheel, Wheeling, Systems.md#Euromillions Wheels, Wheel, Wheeling, Systems#[<u>Resources in Euromillions, Lottery Software, Wheels</u>](https://saliu.com/content/lottery.html)#{24}","lines":[963,968],"size":591,"outlinks":[{"title":"The reduced Euromillions systems are created by free lotto wheeling software.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"The site for wheels, wheeling numbers for Euromillions.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
