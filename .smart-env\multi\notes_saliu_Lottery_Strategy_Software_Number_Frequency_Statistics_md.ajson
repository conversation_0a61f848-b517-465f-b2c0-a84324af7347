
"smart_sources:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md": {"path":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12432769,-0.03698491,-0.04018862,-0.00658988,-0.04872377,0.06964018,-0.0441104,0.01151001,0.08130514,0.01360137,0.01731008,-0.00704587,0.06278271,-0.02256531,-0.0150392,-0.0177199,0.01090356,-0.0080599,-0.01731523,-0.00470653,0.07608349,-0.03649168,-0.01462984,-0.09634387,0.03237357,0.00070445,-0.05837107,-0.06086117,-0.04453295,-0.22562948,0.02402394,0.02819112,0.03224636,-0.06517139,-0.08509558,-0.05144298,-0.04226464,0.03924011,-0.04391846,0.05110349,-0.02880736,0.04196352,0.00287766,-0.04314983,-0.01594603,-0.01555911,-0.04934297,0.02369091,0.0381885,-0.00956744,-0.09800859,-0.00774511,0.00393775,0.02858718,0.05919868,0.02374276,0.03185861,0.06408794,-0.01231722,0.04084982,0.04643612,0.0244032,-0.20647581,0.02747025,-0.02049132,0.0387589,-0.01253232,-0.02660698,0.03916079,0.03170506,-0.01307929,0.03216242,-0.02071764,0.05962983,0.03799231,-0.01026049,-0.01246384,-0.02960679,-0.03574513,0.01946332,-0.02416339,-0.03070419,-0.0143907,-0.0406724,0.05166807,0.0507509,0.03410294,0.04829863,0.07319774,-0.06928908,-0.00037021,0.02470083,0.05256408,0.02983175,0.01041684,0.00848044,0.05596339,-0.02740733,-0.00855857,0.09361935,0.01834049,-0.00333342,-0.03695793,0.04500784,0.04411991,-0.02781761,-0.05503695,-0.06679338,-0.05241932,0.01951206,0.07510172,0.04504198,0.07224251,-0.06704642,-0.03597495,0.01592116,-0.02073613,0.0136532,0.04545574,0.01033338,-0.03335463,0.01627857,0.004144,0.00065289,0.04078137,0.03450763,0.0322129,0.06055025,0.02631509,0.03637661,0.05106104,0.00459557,-0.15638261,-0.02591337,-0.03262866,-0.02638347,-0.02057475,-0.00674743,0.00094823,0.0280918,-0.03123592,-0.03719066,0.08401244,-0.10813157,-0.02204337,0.099029,0.02464525,-0.00277253,-0.00114122,0.00067171,-0.00855287,0.01225519,-0.02832998,-0.06338542,-0.02510153,0.02503519,0.08089928,0.07554614,-0.04370998,-0.0160052,-0.02604661,-0.03704031,-0.02487491,0.12469219,0.01955953,-0.12031558,-0.03218106,0.07069501,-0.03546223,-0.05370763,-0.03248,0.00535013,-0.04218524,0.01878982,0.08812171,-0.03294931,-0.06832539,-0.02647009,0.00334546,0.00131259,0.0430894,-0.01123782,-0.03937925,-0.00342804,-0.04981818,-0.10280409,0.02463447,-0.04607886,0.01460918,0.02115463,-0.03652745,-0.00766316,-0.03216855,-0.01897346,-0.03370101,-0.04082674,-0.02971169,-0.00958755,0.088358,-0.01196091,-0.00920412,-0.01294759,0.04600253,0.0167282,-0.02041965,0.03357931,0.00430423,-0.05285565,0.06810426,0.00860794,-0.00324922,-0.00315196,0.04984765,0.04483667,-0.0473546,0.04683318,-0.03696481,0.05282128,0.01213748,0.01317437,0.00722876,0.05534169,-0.07357448,-0.20505716,-0.0030963,-0.02439743,0.04676754,0.00252306,-0.0081663,0.04314806,-0.00713309,0.0736867,0.09666405,0.08204689,-0.04234296,-0.04424416,0.04436368,-0.00555263,-0.0016511,-0.0718439,-0.05498851,-0.02346948,0.03653995,0.00254234,0.00792025,-0.02817051,-0.05562986,0.04362329,-0.06247068,0.12577552,-0.0050388,-0.01167779,-0.0164664,0.0508503,-0.01412267,-0.01917445,-0.03229745,0.01865687,0.05212659,-0.00392483,0.03223924,-0.01678018,-0.01670787,-0.0870508,0.04243308,0.01558002,-0.10698644,-0.024918,0.01732714,0.00244906,-0.01031194,0.0161296,0.03940886,0.04997069,-0.01835857,0.04235824,0.00749206,0.04614911,-0.04374513,-0.08080702,0.02410063,-0.0058182,0.04008554,-0.00717779,-0.06509755,0.06892861,-0.02653535,0.00508681,0.02086887,-0.00362668,0.00008741,0.03489304,-0.02110037,0.02614994,0.11660734,0.02958232,0.02610489,0.00092775,0.006323,0.0304269,-0.07534551,-0.01476871,-0.00602247,-0.00013786,-0.05267909,0.0391338,0.06999379,0.06908306,0.00690904,0.07282303,0.07078963,0.04244114,-0.00596484,-0.00217131,0.02106323,-0.010345,0.03106722,0.04334021,0.04300649,-0.27199629,-0.00401485,-0.03631073,0.03326977,0.02162069,-0.02301328,0.02361604,-0.0156965,0.0069935,-0.02476182,0.01908872,0.02105317,0.01760447,-0.0668366,-0.01274618,0.01136459,0.00996624,-0.03974897,0.05309138,-0.03066388,0.02470865,0.04849655,0.23290756,0.02336987,-0.00189971,0.03236771,0.0216719,0.03713144,-0.0004545,0.03636473,0.00971303,0.01644388,0.09376959,-0.00059315,-0.07460777,0.04724833,-0.00509247,-0.01266268,-0.0467578,0.00725099,-0.06512453,0.0003215,0.00369146,0.02228381,0.1254268,-0.01200178,-0.03179947,-0.08466858,0.04220604,0.07021038,-0.1197067,-0.05431325,-0.05805515,0.01152391,0.05219932,0.0455105,0.00459687,-0.00042513,-0.00369228,-0.01521739,0.00105255,-0.02154931,0.04924629,0.04097201,0.00043916],"last_embed":{"hash":"j7hco3","tokens":475}}},"last_read":{"hash":"j7hco3","at":1753423499280},"class_name":"SmartSource","last_import":{"mtime":1753363606864,"size":21512,"at":1753423416052,"hash":"j7hco3"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Strategy Software Number Frequency Statistics":[8,198],"#Lottery Strategy Software Number Frequency Statistics#{1}":[10,17],"#Lottery Strategy Software Number Frequency Statistics#{2}":[18,18],"#Lottery Strategy Software Number Frequency Statistics#{3}":[19,23],"#Lottery Strategy Software Number Frequency Statistics#{4}":[24,24],"#Lottery Strategy Software Number Frequency Statistics#{5}":[25,28],"#Lottery Strategy Software Number Frequency Statistics#{6}":[29,29],"#Lottery Strategy Software Number Frequency Statistics#{7}":[30,30],"#Lottery Strategy Software Number Frequency Statistics#{8}":[31,31],"#Lottery Strategy Software Number Frequency Statistics#{9}":[32,32],"#Lottery Strategy Software Number Frequency Statistics#{10}":[33,33],"#Lottery Strategy Software Number Frequency Statistics#{11}":[34,34],"#Lottery Strategy Software Number Frequency Statistics#{12}":[35,36],"#Lottery Strategy Software Number Frequency Statistics#{13}":[37,38],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.":[39,173],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{1}":[41,42],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{2}":[43,43],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{3}":[44,45],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{4}":[46,57],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{5}":[58,58],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{6}":[59,66],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{7}":[67,86],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{8}":[87,87],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{9}":[88,88],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{10}":[89,89],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{11}":[90,90],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{12}":[91,91],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{13}":[92,92],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{14}":[93,93],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{15}":[94,94],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{16}":[95,95],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{17}":[96,97],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{18}":[98,99],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{19}":[100,100],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{20}":[101,101],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{21}":[102,103],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{22}":[104,105],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{23}":[106,107],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{24}":[108,113],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{25}":[114,114],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{26}":[115,115],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{27}":[116,116],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{28}":[117,117],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{29}":[118,118],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{30}":[119,119],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{31}":[120,121],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{32}":[122,131],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{33}":[132,133],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{34}":[134,135],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{35}":[136,137],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{36}":[138,139],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{37}":[140,141],"#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{38}":[142,173],"#Lottery Strategy Software Number Frequency Statistics#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>":[174,198],"#Lottery Strategy Software Number Frequency Statistics#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>#{1}":[176,198]},"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lotto-decades-skips.gif","line":37},{"title":"_**Piracy of Fundamental Formula of Gambling on eBay**_","target":"https://forums.saliu.com/gambling-formula-pirated-ebay.html","line":43},{"title":"_**Steve Players Piracy of Lottery Wonder Grid Systems**_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html","line":44},{"title":"Pick-4 frequency statistical report divides the digits in 3 groups: hot, mild, cold.","target":"https://saliu.com/images/pick-4-lottery-strategy.gif","line":54},{"title":"The pick 4 lottery strategy based on frequency, stats has a good winning rate.","target":"https://saliu.com/images/lottery-strategy-stats.gif","line":69},{"title":"Every lottery strategy has a pivot: Main filter or restriction or condition.","target":"https://saliu.com/images/lottery-strategy-pivot.gif","line":73},{"title":"A winning lottery strategy always applies the reverse as in the case of lotto decades.","target":"https://saliu.com/images/lottery-strategy-decades.gif","line":77},{"title":"The winning lottery strategy always applies the reverse as in the case of number skips.","target":"https://saliu.com/images/lottery-strategy-skips.gif","line":79},{"title":"This winning lottery strategy also applies the odd, even, low, high lottery numbers.","target":"https://saliu.com/images/lottery-strategy-odd-even.gif","line":81},{"title":"Combining more lottery strategies and playing the aggregated result increase the profits.","target":"https://saliu.com/HLINE.gif","line":85},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":91},{"title":"LieStrategies.txt","target":"https://saliu.com/freeware/LieStrategies.txt","line":100},{"title":"ST5-Any1-1-MX-1","target":"https://saliu.com/freeware/ST5-Any1-1-MX-1","line":101},{"title":"ST5-Any2-1-MX-1","target":"https://saliu.com/freeware/ST5-Any2-1-MX-1","line":102},{"title":"Pick 4 lottery strategies hit in the same drawings, multiplying total winnings by a lot of money.","target":"https://saliu.com/HLINE.gif","line":104},{"title":"_**lotto strategies are based on filters**_","target":"https://saliu.com/filters.html","line":106},{"title":"Lottery strategies based on number frequency can be very effective only with Ion lotto software.","target":"https://saliu.com/ScreenImgs/lottery-frequency.gif","line":112},{"title":"_**Frequency groups pick-3, pick-4 lotteries, lotto 5/39, 649 lotto**_","target":"https://saliu.com/frequency-tables.html","line":142},{"title":"_**Statistical Frequency Reports for Pennsylvania 5/39 Lotto**_","target":"https://saliu.com/frequency-reports.html","line":144},{"title":"Rank lottery numbers by frequency from hot to cold for Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":146},{"title":"Lottery software generates combinations from systems, strategies based on skips of numbers.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":166},{"title":"_**Skip Systems, Software for Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":168},{"title":"Lottery players use statistics such as frequency to reduce the amount of lotto combinations to play.","target":"https://saliu.com/HLINE.gif","line":170},{"title":"\n    \n    ## <u>Resources in Lottery Software, Strategies, Lotto Systems</u>\n    \n    ","target":"https://saliu.com/content/lottery.html","line":172},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":178},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":180},{"title":"_**<u>Lottery Mathematics, Lotto Mathematics</u>**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":181},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":182},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":183},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":184},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":185},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":186},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":187},{"title":"_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_","target":"https://saliu.com/cross-lines.html","line":188},{"title":"_**<u>Lottery Strategies</u>**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":189},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":190},{"title":"**<u>Lottery Software, Lotto Programs</u>**","target":"https://saliu.com/infodown.html","line":191},{"title":"Frequency Rank is unique lottery software to analyze the drawings in many lotto games in the world.","target":"https://saliu.com/HLINE.gif","line":193},{"title":"Forums","target":"https://forums.saliu.com/","line":195},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":195},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":195},{"title":"Contents","target":"https://saliu.com/content/index.html","line":195},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":195},{"title":"Home","target":"https://saliu.com/index.htm","line":195},{"title":"Search","target":"https://saliu.com/Search.htm","line":195},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":195},{"title":"Download software for lottery strategy, systems based on lotto number frequency.","target":"https://saliu.com/HLINE.gif","line":197}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["software","system","systems","lotto","lottery","numbers","sets","frequency","analysis","straight","pick-3","pick 4","patterns","odd","even","low","high"],"source":"https://saliu.com/frequency-lottery.html","author":null}},"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12483326,-0.05643662,-0.05917333,-0.03076055,-0.01780538,0.059573,0.01220652,0.0474454,0.07134499,-0.02017529,0.01632132,-0.01120118,0.02855118,-0.01207051,-0.01007254,0.01475864,0.01462797,-0.03842675,-0.01165363,0.00214556,0.106532,-0.02994462,-0.04542192,-0.08956788,0.06298817,-0.01062889,-0.04899317,-0.07915835,-0.04455209,-0.18494126,0.00808861,0.03828144,0.04334128,-0.07762177,-0.04188617,-0.0481385,-0.0278285,0.05864123,-0.01259129,0.04785002,-0.01325353,0.03985237,0.0013413,-0.01676279,-0.02049412,-0.01655731,-0.03172787,0.01191226,-0.00224711,-0.00104879,-0.0950172,0.00384487,-0.00449722,0.0492995,0.0662434,0.03455671,0.0640552,0.02710418,0.03254116,0.05296919,0.03188633,-0.00295015,-0.21787488,0.05154398,0.0043095,0.01812764,-0.0094236,-0.03414438,0.0126409,0.01183871,0.01765147,0.0579813,-0.00882694,0.04645912,0.04266686,-0.01060178,-0.03267081,-0.06811348,-0.02455526,0.01471818,-0.03142006,-0.02069023,-0.01593587,-0.04338756,0.0472514,0.03177539,0.03909834,0.06798913,0.07482127,-0.04793311,0.00905674,0.0448014,0.04587103,0.02539224,-0.00661932,0.02148906,0.03760033,-0.0142237,-0.01509632,0.1175696,-0.01890504,0.00464035,-0.03585247,0.04833525,0.05559166,-0.0024585,-0.03664182,-0.05684074,-0.05046154,0.02963684,0.05833645,0.03421549,0.07621876,-0.09531897,-0.01226535,-0.01061272,0.0083836,-0.0032813,0.0727974,-0.00750416,-0.0328108,-0.01221097,0.02157191,-0.00121332,0.04592162,0.0583227,0.03911788,0.07340471,0.01023527,0.02179502,0.03204948,0.00242927,-0.16174859,-0.01933723,-0.04023446,-0.00480327,0.02128737,-0.02505432,-0.00860929,0.00646605,-0.04117258,-0.03901153,0.05312256,-0.11808016,-0.02930229,0.0997093,0.01559668,0.0047874,0.01900389,0.0004942,-0.01683591,0.01733764,0.00293342,-0.06898697,-0.02876806,0.03285488,0.09376419,0.07296843,-0.01886603,0.00966805,-0.00660589,-0.05143232,-0.02984714,0.14057134,-0.01610851,-0.12146399,-0.03460291,0.05883725,-0.01043304,-0.07310265,-0.02530386,0.01080521,-0.0068401,0.01785679,0.10124283,-0.01205034,0.00708914,-0.0399259,-0.00257255,-0.01449097,0.00722416,-0.0052665,-0.05156511,0.01961155,-0.04936811,-0.07230499,0.01171133,-0.03772375,0.03111221,0.02759948,-0.05790659,-0.04698874,-0.03489504,0.01278508,-0.04318655,-0.03753584,-0.02928297,-0.01714639,0.06787058,-0.00830503,-0.02740066,-0.01744674,0.02560302,0.02344812,-0.01093836,0.04488715,-0.01265525,-0.05223113,0.04820083,0.01022936,0.01726379,0.02741684,0.0570077,0.03756387,-0.0647577,0.01947571,-0.03871378,0.04201868,0.0202162,0.01022479,-0.02844899,0.01779375,-0.1155222,-0.22817671,-0.01128008,-0.01178333,0.026544,-0.004628,0.01469974,0.04386294,-0.01927512,0.07057142,0.11159997,0.05895042,-0.03234815,-0.04126505,-0.00216504,-0.00011244,-0.02224094,-0.05463466,-0.07455597,-0.02776016,0.04544537,0.0003637,0.00507059,-0.05852107,-0.07476977,0.05556509,-0.03824606,0.11659954,0.01267646,-0.02488879,-0.02790264,0.07714214,-0.01487447,-0.0225859,-0.02566278,0.01269537,0.04306885,-0.04172371,0.043524,-0.03887049,-0.0132793,-0.09287894,0.03232005,0.02308356,-0.09127767,-0.02947141,0.00048563,0.01299039,-0.02360275,-0.01465566,0.01209464,0.0402218,0.00324447,0.02497112,0.02684269,0.05095472,-0.00604969,-0.08377033,0.00111899,-0.00689026,0.0182135,-0.00818942,-0.04546375,0.0786506,-0.03008011,-0.01990265,0.0481278,-0.00479315,-0.03289127,-0.01190821,-0.01236339,0.02948739,0.1246541,0.02430294,-0.00912439,-0.00992722,-0.00682242,0.0259291,-0.05848124,0.00042763,-0.01378729,0.01235693,-0.06241893,0.02608901,0.05475731,0.06241658,0.03759665,0.04340588,0.05347309,0.01761082,0.01287973,0.02477146,0.01738547,-0.0026606,-0.00522106,0.03712614,0.00535941,-0.26605195,0.03191479,-0.00733384,0.06779166,0.03638399,-0.00981315,0.02065473,-0.01386434,-0.0145213,-0.0041893,0.02947159,0.0306427,0.00485406,-0.04703801,-0.00840843,-0.00641777,0.01209414,-0.05247191,0.07043688,0.01494843,0.03357714,0.03735441,0.24487425,0.02766662,0.01115553,0.0284863,0.04645419,0.01263341,-0.00274842,0.04118908,0.01387984,0.00617772,0.08243345,0.0052979,-0.0580322,0.04931608,-0.01406524,-0.00716672,-0.04101896,0.01253431,-0.0818593,0.00045864,-0.02175901,0.02266771,0.126279,0.00139774,-0.05120282,-0.07407483,0.02799987,0.0557203,-0.10640402,-0.07273521,-0.05534166,0.00262831,0.02200339,0.06214374,0.02492483,-0.00678055,0.02263139,-0.02658729,0.00574529,0.01002392,0.05375981,0.00882433,0.00844429],"last_embed":{"hash":"1hyhmnb","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"1hyhmnb","at":1753423497469},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#---frontmatter---","lines":[1,6],"size":236,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10164379,-0.03191252,-0.02845408,-0.0102436,-0.05995085,0.06608125,-0.05225137,-0.00501302,0.0763736,0.01034208,0.0099824,0.00347081,0.06811146,-0.01987388,-0.01565193,-0.01160557,0.01229905,-0.008896,-0.01067381,-0.00090059,0.0743498,-0.0362507,-0.00698254,-0.08461498,0.02544688,0.01004494,-0.06172347,-0.0578535,-0.04150252,-0.22297131,0.0446659,0.02217844,0.03680434,-0.04840835,-0.07951249,-0.05659954,-0.04616352,0.0287049,-0.06569006,0.04398496,-0.01709531,0.03200396,0.01162643,-0.05329853,-0.00592162,-0.02698185,-0.03336282,0.02829691,0.04827866,-0.01017994,-0.08932079,0.00483626,0.00593187,0.02732019,0.04875192,0.01903112,0.0213647,0.07238515,-0.0065428,0.04194516,0.06048501,0.02298468,-0.21413681,0.01771359,-0.03428315,0.03927609,-0.01589935,-0.0204462,0.0423522,0.0327066,-0.01069817,0.01162593,-0.01579636,0.0718519,0.03714906,-0.019239,-0.01968632,-0.00891694,-0.0226707,0.01421158,-0.021888,-0.03024706,-0.00781293,-0.03843998,0.03634199,0.0453084,0.04622228,0.04590436,0.07943641,-0.06064551,0.00629338,0.0101441,0.04861888,0.03356921,0.01122087,-0.00328459,0.06041475,-0.02711955,-0.00446411,0.0975393,0.02704374,-0.01800124,-0.03439099,0.03583209,0.02798856,-0.02888358,-0.05250613,-0.06026033,-0.04400407,0.02113554,0.0773173,0.05690684,0.08049247,-0.06102172,-0.04654376,0.00860489,-0.03261486,0.01974724,0.03810716,0.0115063,-0.034619,0.02708097,-0.01171708,0.00029901,0.04751804,0.02750226,0.02590155,0.05933108,0.03679728,0.04522842,0.04669409,0.01302034,-0.15361431,-0.02673409,-0.01787441,-0.02965033,-0.02573463,-0.01130675,0.00955797,0.0405882,-0.01282077,-0.03665154,0.06039498,-0.10581362,-0.0126041,0.10220729,0.02286207,-0.00557254,-0.01270873,-0.00258079,-0.00064938,0.00133606,-0.04317813,-0.06545308,-0.01809997,0.02902877,0.07527649,0.06775987,-0.05247083,-0.02132501,-0.03359775,-0.02525824,-0.01259014,0.12346471,0.0212971,-0.1151403,-0.03445226,0.08038338,-0.04722375,-0.05427375,-0.0351925,0.00962537,-0.05190043,0.01863133,0.078737,-0.0442997,-0.07437974,-0.02155951,0.00471385,-0.0020126,0.05902351,-0.02465149,-0.03018486,-0.00527962,-0.05579162,-0.1087328,0.01637372,-0.04248942,0.0146353,0.01820898,-0.03054757,-0.0030586,-0.02603187,-0.02647999,-0.02951072,-0.04604461,-0.03819298,-0.00573585,0.09362137,-0.03375807,0.00964683,-0.00052027,0.0493775,0.03052792,-0.01669038,0.02786678,0.00116822,-0.04461588,0.06976052,0.00356822,-0.02634548,-0.01738358,0.04636891,0.04293495,-0.05091587,0.04374731,-0.03673843,0.0543938,0.00192795,0.02108214,0.0117876,0.06954142,-0.06828516,-0.21142079,-0.00254014,-0.02314227,0.05285906,0.00722423,-0.01375607,0.03318319,0.00083385,0.06481783,0.08556739,0.09059153,-0.05447525,-0.03870893,0.0549879,-0.0180364,-0.00998175,-0.07795087,-0.05257031,-0.01946921,0.01406348,-0.00159856,-0.01453162,-0.01592996,-0.05544142,0.04202875,-0.06050394,0.12208825,-0.00174009,0.00706375,-0.01760292,0.04596462,-0.01905599,-0.01817834,-0.03354872,0.01050303,0.06351922,-0.01222972,0.02548188,-0.00660657,-0.01352296,-0.07567216,0.04314126,0.01053085,-0.10681681,-0.03828806,0.01185203,0.00804541,-0.0156677,0.02262176,0.0631523,0.02994542,-0.02355077,0.05246408,0.01642226,0.05031718,-0.05978967,-0.07420119,0.03710395,-0.00993159,0.04493625,-0.0016697,-0.06421726,0.06079295,-0.0276693,0.01372602,0.02028948,-0.00969324,0.01007618,0.05450155,-0.02576606,0.01288934,0.10931237,0.01026696,0.02135669,0.0199343,0.01345635,0.03667008,-0.08904888,-0.01890882,0.00127104,-0.01023987,-0.04875446,0.05827903,0.0805994,0.07328835,0.00198667,0.07408015,0.07209832,0.04601873,0.00655607,-0.00817246,0.02165492,-0.01984897,0.03198677,0.02819915,0.04996929,-0.26949149,-0.00982279,-0.04926873,0.04301002,0.00423779,-0.02659954,0.02667033,-0.00446341,-0.00504414,-0.02456501,0.02909579,0.01615503,0.02622873,-0.06805252,-0.01936236,0.01575552,0.01427165,-0.04219129,0.05368396,-0.03811714,0.02596383,0.04934178,0.2264325,0.0161231,-0.00155784,0.03111197,0.01644203,0.04870299,-0.00408462,0.02613122,0.01001146,0.00404077,0.09291432,0.00275016,-0.06275741,0.03037558,-0.01281982,-0.00687379,-0.03436511,0.00248427,-0.05631713,-0.0000475,0.0081483,0.02653557,0.12935285,0.00576336,-0.03114184,-0.09116459,0.03601556,0.07298033,-0.11529761,-0.04993747,-0.05093437,0.02464884,0.05136775,0.032135,0.00070096,-0.00222576,-0.00274838,-0.01603825,-0.00388752,-0.03222621,0.04961254,0.04377159,-0.00664456],"last_embed":{"hash":"1rgczhl","tokens":436}}},"text":null,"length":0,"last_read":{"hash":"1rgczhl","at":1753423497501},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics","lines":[8,198],"size":21258,"outlinks":[{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lotto-decades-skips.gif","line":30},{"title":"_**Piracy of Fundamental Formula of Gambling on eBay**_","target":"https://forums.saliu.com/gambling-formula-pirated-ebay.html","line":36},{"title":"_**Steve Players Piracy of Lottery Wonder Grid Systems**_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html","line":37},{"title":"Pick-4 frequency statistical report divides the digits in 3 groups: hot, mild, cold.","target":"https://saliu.com/images/pick-4-lottery-strategy.gif","line":47},{"title":"The pick 4 lottery strategy based on frequency, stats has a good winning rate.","target":"https://saliu.com/images/lottery-strategy-stats.gif","line":62},{"title":"Every lottery strategy has a pivot: Main filter or restriction or condition.","target":"https://saliu.com/images/lottery-strategy-pivot.gif","line":66},{"title":"A winning lottery strategy always applies the reverse as in the case of lotto decades.","target":"https://saliu.com/images/lottery-strategy-decades.gif","line":70},{"title":"The winning lottery strategy always applies the reverse as in the case of number skips.","target":"https://saliu.com/images/lottery-strategy-skips.gif","line":72},{"title":"This winning lottery strategy also applies the odd, even, low, high lottery numbers.","target":"https://saliu.com/images/lottery-strategy-odd-even.gif","line":74},{"title":"Combining more lottery strategies and playing the aggregated result increase the profits.","target":"https://saliu.com/HLINE.gif","line":78},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":84},{"title":"LieStrategies.txt","target":"https://saliu.com/freeware/LieStrategies.txt","line":93},{"title":"ST5-Any1-1-MX-1","target":"https://saliu.com/freeware/ST5-Any1-1-MX-1","line":94},{"title":"ST5-Any2-1-MX-1","target":"https://saliu.com/freeware/ST5-Any2-1-MX-1","line":95},{"title":"Pick 4 lottery strategies hit in the same drawings, multiplying total winnings by a lot of money.","target":"https://saliu.com/HLINE.gif","line":97},{"title":"_**lotto strategies are based on filters**_","target":"https://saliu.com/filters.html","line":99},{"title":"Lottery strategies based on number frequency can be very effective only with Ion lotto software.","target":"https://saliu.com/ScreenImgs/lottery-frequency.gif","line":105},{"title":"_**Frequency groups pick-3, pick-4 lotteries, lotto 5/39, 649 lotto**_","target":"https://saliu.com/frequency-tables.html","line":135},{"title":"_**Statistical Frequency Reports for Pennsylvania 5/39 Lotto**_","target":"https://saliu.com/frequency-reports.html","line":137},{"title":"Rank lottery numbers by frequency from hot to cold for Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":139},{"title":"Lottery software generates combinations from systems, strategies based on skips of numbers.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":159},{"title":"_**Skip Systems, Software for Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":161},{"title":"Lottery players use statistics such as frequency to reduce the amount of lotto combinations to play.","target":"https://saliu.com/HLINE.gif","line":163},{"title":"\n    \n    ## <u>Resources in Lottery Software, Strategies, Lotto Systems</u>\n    \n    ","target":"https://saliu.com/content/lottery.html","line":165},{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":171},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":173},{"title":"_**<u>Lottery Mathematics, Lotto Mathematics</u>**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":174},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":175},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":176},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":177},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":178},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":179},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":180},{"title":"_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_","target":"https://saliu.com/cross-lines.html","line":181},{"title":"_**<u>Lottery Strategies</u>**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":182},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":183},{"title":"**<u>Lottery Software, Lotto Programs</u>**","target":"https://saliu.com/infodown.html","line":184},{"title":"Frequency Rank is unique lottery software to analyze the drawings in many lotto games in the world.","target":"https://saliu.com/HLINE.gif","line":186},{"title":"Forums","target":"https://forums.saliu.com/","line":188},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":188},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":188},{"title":"Contents","target":"https://saliu.com/content/index.html","line":188},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":188},{"title":"Home","target":"https://saliu.com/index.htm","line":188},{"title":"Search","target":"https://saliu.com/Search.htm","line":188},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":188},{"title":"Download software for lottery strategy, systems based on lotto number frequency.","target":"https://saliu.com/HLINE.gif","line":190}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12021342,-0.02459374,-0.05225822,-0.02374173,-0.05231059,0.05154702,-0.03780055,-0.00661087,0.06283507,-0.03024582,0.0453041,0.0070726,0.07405611,-0.02451506,-0.01938508,-0.00057334,0.00173848,-0.00381972,-0.01031807,0.02670289,0.09398218,-0.02843527,-0.0300814,-0.08061676,0.04635892,-0.00696531,-0.05073902,-0.06902655,-0.03920868,-0.20913725,0.05427011,0.02734471,0.04998593,-0.04197056,-0.04765946,-0.05895045,-0.02389326,0.04412758,-0.06589786,0.05660011,0.00890674,0.01897383,0.01311301,-0.03866513,-0.01377294,-0.02622377,-0.02363078,0.04914143,0.03036695,-0.01127574,-0.07961612,0.04198017,-0.00648195,0.00673523,0.0478707,0.03261399,0.05607582,0.07255856,0.03491572,0.03415799,0.0450621,0.0023022,-0.21743748,0.01253183,-0.02966423,0.02327069,0.00935568,-0.02240425,0.0433132,0.01562273,-0.01241714,0.03049412,-0.01230504,0.06668966,0.02932332,-0.01139634,-0.03705638,-0.04000527,-0.02329046,0.02649841,-0.00819407,-0.04892791,0.00330799,-0.02154367,0.02480649,0.05208843,0.03351193,0.03850621,0.08996025,-0.0653234,-0.00317156,0.02426063,0.05249375,0.03906843,0.02705987,-0.00558631,0.06137883,-0.00869284,0.02508906,0.13663176,0.00828375,-0.01568283,-0.02551741,0.04840644,0.04377231,-0.01153513,-0.05227787,-0.06725579,-0.0391161,0.04511221,0.08096925,0.05354791,0.07884354,-0.04824945,-0.01374883,-0.02859356,-0.0343665,0.01241339,0.03858794,0.00499901,-0.02424168,0.014972,-0.00743777,-0.02806033,0.03782932,0.04224578,0.01742532,0.0516896,0.03409595,0.03606312,0.03374588,-0.02793019,-0.15482357,-0.04039545,0.00478791,-0.00738983,-0.01252158,-0.0202953,-0.00706469,0.04035963,-0.02942987,-0.0279824,0.03541363,-0.12074419,-0.01605291,0.10701152,0.03899034,-0.01251082,-0.01448881,0.00734095,0.00533389,-0.01198317,-0.04657256,-0.07746677,-0.0154297,0.02422616,0.0838642,0.07864912,-0.03277098,-0.01026738,-0.02507886,-0.02377869,-0.02632676,0.11208053,0.0025409,-0.10909627,-0.04058119,0.045743,-0.0365225,-0.06305589,-0.05212934,0.00388293,-0.03031893,0.02187122,0.08770262,-0.0402657,-0.04743394,-0.0392876,-0.03010541,-0.02460452,0.03085495,-0.03421441,-0.03253094,0.00104786,-0.04759928,-0.08970462,0.01740247,-0.05517989,0.03434283,0.03964813,-0.03535061,0.0126344,-0.03440216,-0.00961102,-0.02469155,-0.04795541,-0.07070483,0.00166256,0.0815167,-0.05342937,-0.03724013,0.00346699,0.03185661,0.02348372,-0.00750646,0.01766404,-0.00321021,-0.03474563,0.04748146,0.00702778,-0.03154198,-0.00489195,0.044349,0.05439742,-0.05885378,0.02010974,-0.04224295,0.03143577,0.00668171,0.0123812,-0.00624341,0.04784364,-0.12006844,-0.200008,-0.02637791,-0.02715671,0.00369773,0.01394505,0.00738328,0.03738857,0.00078641,0.05311969,0.06342433,0.09587517,-0.04807467,-0.01482142,0.05227079,-0.028773,0.00444462,-0.06581733,-0.05723728,-0.01053045,0.01626534,0.00145538,-0.01360164,-0.03564991,-0.05510828,0.02996757,-0.07086925,0.13079299,-0.03215867,0.0205533,-0.01594845,0.02365219,-0.03271197,-0.03504423,-0.01408575,-0.01671137,0.03954724,-0.00906975,0.02575845,-0.02934894,0.00433409,-0.09870946,0.0070665,-0.00934607,-0.10013375,-0.0400392,0.01833099,0.01388041,-0.01140424,0.01357483,0.05241716,0.03305013,-0.02138878,0.03680325,0.03056105,0.05341907,-0.03776684,-0.0824907,0.05560612,-0.01756452,0.07085942,0.00530587,-0.0402862,0.04746082,-0.03746187,0.0036769,0.00482239,0.00168778,-0.00469553,0.02641899,-0.03496437,0.00348273,0.12505282,0.01500776,0.05269031,0.03412907,0.01180106,0.04306081,-0.08331655,-0.03251279,-0.00477041,0.00545335,-0.05151731,0.05310547,0.07661599,0.07728135,0.03656853,0.06053521,0.0657996,0.00823657,0.00289682,0.00635953,0.01561974,-0.02771447,0.02193958,0.03501888,0.03911049,-0.25819805,0.02158492,-0.03905529,0.06074177,0.00250328,-0.02348524,0.01942493,0.00254013,0.00236562,-0.02155698,0.05249091,0.02297758,0.01419767,-0.04716773,0.00230481,-0.03127121,0.01845943,-0.04018254,0.07919188,-0.00844496,0.03384471,0.04222037,0.22384523,0.01638459,0.01226375,0.01733707,0.03583959,0.0510267,-0.01106906,0.01084072,0.0366644,-0.00479035,0.09229726,-0.00788292,-0.06262899,0.0309018,-0.00617157,0.00343468,-0.02747687,0.01086181,-0.05779517,-0.00110391,0.01179613,0.02279449,0.1469052,0.00642069,-0.0417994,-0.09515648,0.03794282,0.08180073,-0.09676795,-0.05258925,-0.05733264,0.01040132,0.0512041,0.04494612,0.01026331,0.00799769,0.00236025,-0.03213668,0.00314444,-0.00346294,0.05869994,0.02298094,0.00749772],"last_embed":{"hash":"6edjku","tokens":101}}},"text":null,"length":0,"last_read":{"hash":"6edjku","at":1753423497632},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#{1}","lines":[10,17],"size":277,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10992204,-0.02723274,-0.03532524,-0.02386596,-0.05545207,0.07313632,-0.0070537,0.00344802,0.06058572,-0.00014072,0.03037889,0.00024371,0.0753734,-0.03009115,-0.00643915,-0.00114597,0.00411904,-0.02599084,-0.01770135,-0.02826132,0.0470311,-0.03043125,-0.01025617,-0.07601818,0.03443265,-0.00931592,-0.05753697,-0.09741718,-0.04389765,-0.21174179,0.01588439,0.03325279,0.03397863,-0.06999424,-0.06807668,-0.04622915,-0.02550003,0.06129408,-0.06789947,0.06899557,-0.01647934,0.03209661,0.01626021,-0.03729307,-0.03246575,-0.02850731,-0.03208888,0.03922053,0.01964695,-0.00034629,-0.08207218,0.00889156,-0.00400446,0.05807433,0.05327324,0.03853805,0.02732119,0.0413406,-0.00127224,0.04301037,0.05264984,0.0136901,-0.22376956,0.03690843,-0.02587936,0.03083818,0.01156122,-0.027853,0.03013967,0.01986159,-0.01006166,0.02783256,-0.02427378,0.03121925,0.06872426,0.0021339,-0.05154082,-0.03065876,-0.01644774,0.05020029,-0.02994532,-0.03865568,-0.02391617,-0.0221867,0.0611678,0.00997838,0.04906666,0.05203541,0.07266925,-0.04498108,0.0017928,0.06966875,0.00895574,0.01565083,0.03540174,0.00912065,0.05544838,-0.03761117,-0.0064002,0.14026511,-0.00414602,-0.02635737,-0.02514473,0.04733262,0.03988766,-0.03853155,-0.05396095,-0.04203097,-0.03301446,0.01571753,0.064987,0.02539278,0.06835864,-0.08662343,-0.04560874,-0.01919315,-0.01187489,0.00316201,0.04403135,0.00721784,-0.03089561,-0.00284235,0.01933711,-0.00169792,0.06098522,0.02341603,0.00815224,0.08323401,0.03140738,0.02672662,0.04285995,0.00840645,-0.16364439,-0.0558143,-0.04788589,-0.01809746,-0.01678498,-0.02182625,-0.01204831,0.05009563,-0.01802985,-0.0270466,0.09535464,-0.11244902,-0.00484162,0.13470538,-0.00904931,0.02124151,0.00125348,0.02534165,-0.01200326,0.00867178,-0.0318856,-0.0585232,-0.03171505,0.03118056,0.06364539,0.05328236,-0.04383596,-0.00310103,-0.01520896,-0.01581409,-0.00561595,0.15466358,0.01667004,-0.09504841,-0.01634949,0.05805616,-0.03267589,-0.04180307,-0.02444381,0.0033157,0.01323527,0.02394365,0.09153196,-0.03725481,-0.02557056,-0.02862617,-0.00255756,-0.01037861,0.02781414,-0.01550454,-0.02507736,0.00733965,-0.02095865,-0.0975156,0.0188864,-0.02843206,0.04871533,0.01240766,-0.05300335,-0.00109375,0.00730137,0.00364262,-0.03119367,-0.02786446,-0.04150939,-0.0181102,0.09437444,-0.02872176,-0.00375455,-0.00909712,0.0293483,0.02519022,-0.00853887,0.04108438,-0.00458383,-0.04122243,0.06665861,-0.00511884,-0.00462289,-0.00805093,0.08748001,0.04562828,-0.04745232,0.03659917,-0.05901057,0.02747582,0.0271003,0.02285576,0.00823271,0.06648215,-0.10039629,-0.20674263,-0.00625077,0.00433665,0.01893157,-0.01568475,-0.01050523,0.03623329,-0.01307762,0.06521905,0.07534871,0.05632601,-0.03500631,-0.02113765,0.05329935,-0.01450781,-0.05978391,-0.06644224,-0.06631256,-0.01802168,0.01985669,0.00488523,0.00300593,-0.04115604,-0.06599162,0.0465343,-0.07278705,0.1074142,-0.0144883,-0.00251123,-0.00845517,0.04889989,0.01450674,-0.0203175,-0.01592034,0.04357792,0.05284891,-0.03817192,0.01501342,-0.0189168,0.00946408,-0.06360716,0.04234123,0.01544195,-0.10685931,-0.00775934,-0.01204196,0.00142696,0.00187891,0.00434936,0.01789636,0.0336124,0.00666325,0.04756217,0.01007038,0.06737283,-0.0339247,-0.09208485,0.04031473,-0.00619352,0.02973126,-0.02115418,-0.04961243,0.05706921,-0.02054444,-0.00083224,0.00735307,-0.01224098,-0.02064584,0.01320124,-0.01178425,0.02693811,0.12184273,-0.00615624,-0.00832293,-0.00814572,0.00880317,0.03123717,-0.07537012,-0.03147024,-0.02151586,-0.01330701,-0.05040338,0.01289818,0.04725642,0.06684815,-0.02321822,0.07052875,0.0910872,0.01093103,0.01842378,-0.01185251,0.00633272,0.00896185,0.02355593,0.01746153,0.03028708,-0.25288066,0.02467324,-0.05439788,0.03430972,0.0087184,-0.02302667,0.00791102,0.0124464,-0.02923713,-0.01321444,0.04512313,0.01247267,0.01444717,-0.0690935,0.01719722,0.01998075,0.05352511,-0.05886508,0.04694869,-0.00921685,0.04808465,0.04345579,0.23250641,0.01343512,0.02524382,0.02769109,0.04144736,0.0461637,0.00207303,0.02434708,0.01066933,-0.00139014,0.07022441,-0.00833273,-0.08022679,0.04124273,0.00119984,-0.01168041,-0.05168117,0.01905581,-0.07784293,-0.02221501,-0.02026566,0.02550957,0.15880026,-0.0075453,-0.0302552,-0.08064225,0.02666304,0.07375651,-0.10324018,-0.0673748,-0.04657663,-0.01153812,0.05845855,0.02235424,0.00062111,-0.01036391,-0.00006728,-0.01227741,0.02022241,0.00249435,0.04078621,0.01815237,-0.01145526],"last_embed":{"hash":"s84z9t","tokens":79}}},"text":null,"length":0,"last_read":{"hash":"s84z9t","at":1753423497669},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#{5}","lines":[25,28],"size":245,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11655074,-0.06943414,-0.02547103,-0.04152124,-0.02624717,0.06752218,-0.02926514,0.04432901,0.04572166,-0.03554795,0.01332209,0.01129255,0.05603346,0.00153823,-0.00742956,0.02701024,0.03165465,-0.02385288,0.00088552,-0.01287689,0.07260787,-0.00879606,0.01980291,-0.05756614,0.04342322,0.02094463,-0.07378303,-0.08433209,-0.03446567,-0.17194398,-0.00074527,0.01051702,0.03149119,-0.07172962,-0.04278738,-0.05463448,-0.01042209,0.00585376,-0.02824086,0.05893572,0.00050072,0.02037347,0.01419651,-0.04414076,0.00144435,-0.02883939,0.01762255,0.02036799,0.04772123,-0.0124313,-0.05647173,0.06226747,-0.0110899,0.03673982,0.01889656,0.01668585,0.05308184,0.0434153,-0.00695422,0.08119946,0.01979473,0.01900282,-0.22938752,0.06463901,-0.03224565,0.02330613,-0.01531132,-0.01801905,0.03682857,0.00112947,-0.02046085,0.00775247,-0.03406336,0.06838926,0.06452157,-0.04722895,-0.00228005,-0.01603357,-0.01426931,0.02773436,-0.0494748,-0.00753844,0.02740916,-0.04588325,0.03881561,0.0374098,0.07241636,0.0588221,0.07079956,-0.06376792,0.01348094,0.02186852,0.03875628,0.01633761,0.00959538,0.01488057,0.04460107,-0.03374329,-0.01902699,0.12704872,-0.03390692,-0.05108252,-0.04937414,0.04145471,0.05064078,-0.01240356,-0.02440383,-0.04182123,-0.04080236,0.01335326,0.06103161,0.02687791,0.06296258,-0.07313381,-0.00357637,0.00252083,-0.03581034,0.00095925,0.05629662,-0.01582953,-0.05626857,-0.00071044,0.04120291,0.0057095,0.05114418,0.01075782,0.00939258,0.05890979,0.01947512,0.05046959,0.05121942,0.01618899,-0.16876225,-0.06022591,-0.04659957,-0.00027178,0.00129346,-0.04003989,-0.00369059,0.08330394,-0.01959635,-0.05240245,0.02956825,-0.084787,-0.03973829,0.13329351,0.00815526,0.00024401,-0.00039572,0.01059857,0.00959723,0.01239032,-0.02858058,-0.06206474,-0.00697165,0.02631683,0.12423491,0.07853964,-0.05518531,-0.01572233,-0.01481071,-0.04462884,0.00967452,0.13476662,-0.0072641,-0.10513441,0.00835881,0.05436611,-0.01147209,-0.07134785,-0.02618373,0.00372061,-0.03919955,0.01759571,0.10322533,-0.04221294,-0.01053082,-0.03840405,0.00173366,-0.00738686,0.00321684,-0.01951703,-0.01465459,0.02946239,-0.03716487,-0.0686829,0.0090145,-0.03572517,0.04108037,0.00815224,-0.08203319,-0.01953094,-0.03604792,-0.01239917,-0.04284127,-0.00646866,-0.06221214,-0.02652112,0.07907543,-0.03305429,-0.01510938,-0.01448125,0.01015352,0.02888418,-0.01696703,0.08316742,-0.02400435,-0.02256338,0.07770456,-0.01646846,-0.02836062,0.02865739,0.05452104,0.04270064,-0.06932995,0.03143805,0.0115125,0.03154956,0.00589645,0.029215,-0.00431218,0.07577036,-0.10964016,-0.20984823,0.02425356,-0.05139407,-0.00465427,0.00135178,0.00040979,0.05739453,-0.00170661,0.06135869,0.1045296,0.11709655,-0.03329367,-0.0106287,0.02158979,-0.0193979,-0.06780482,-0.06900901,-0.05899622,-0.02953098,0.01255682,-0.00578081,-0.03068302,-0.02961734,-0.03032603,0.05030025,-0.0324338,0.08707739,0.01104898,0.0302084,-0.01886118,0.04683627,-0.00103396,-0.02276958,-0.08724517,-0.01704052,0.04613606,-0.06344542,0.0251495,-0.01741655,0.00785308,-0.05127081,0.04610506,0.04059355,-0.09198775,-0.03542108,0.00528195,0.0130751,0.02520635,-0.00877011,0.01097471,0.02872894,0.02383614,0.05398187,0.05603539,0.03962636,-0.04112369,-0.04948762,0.01431093,-0.02293591,0.05824411,0.00326726,-0.04881581,0.0851668,-0.06747133,0.00791257,0.00934923,-0.00863909,-0.00622298,0.04874505,-0.01509498,-0.00199549,0.09931426,0.00541893,0.00534329,-0.01841989,-0.00155748,0.06799601,-0.05795546,-0.02246549,-0.01429112,-0.04983404,-0.05813277,0.0682442,0.05574543,0.06984247,0.0312734,0.04894296,0.05933518,-0.03262533,0.03465657,-0.00274284,0.00186118,-0.0074633,0.00947018,0.01408875,0.03124446,-0.24979712,0.00375528,-0.03987171,0.05929378,-0.00875386,-0.01889784,0.04228437,-0.04048081,-0.03724579,-0.03076548,0.04685645,0.01869758,-0.04218099,-0.06810968,-0.01408052,0.01620137,0.04262202,-0.0469887,0.05415828,0.01253667,0.0259681,0.03890019,0.22843802,0.02952833,-0.00974334,0.04571206,0.03063988,0.05879845,0.02612124,0.04115408,0.01034537,-0.0237607,0.06407852,-0.00702203,-0.07766034,0.03436275,-0.01322034,-0.0106328,-0.00594353,-0.00964793,-0.03003418,-0.03896473,-0.01415525,0.02621499,0.11318603,-0.00388816,-0.04969212,-0.0680908,-0.01209829,0.07842059,-0.08706743,-0.07794338,-0.05690593,0.00605398,0.03835104,0.0284632,0.03276593,-0.02396339,-0.00472539,-0.04292126,0.02240005,-0.01940768,0.03029168,0.02791322,0.00832136],"last_embed":{"hash":"q0amfu","tokens":127}}},"text":null,"length":0,"last_read":{"hash":"q0amfu","at":1753423497700},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#{11}","lines":[34,34],"size":493,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10099112,-0.04094909,-0.03941217,-0.05988209,-0.01151939,0.04823916,-0.00930372,0.00977523,0.05581486,0.04147038,0.03227715,0.03231443,0.01186077,-0.00537733,-0.0161706,-0.00729535,0.00223869,-0.02506376,-0.01861064,0.0284873,0.05629827,-0.05066508,-0.01338254,-0.062997,0.03253251,0.00279552,-0.05334872,-0.0818444,-0.08098483,-0.22175607,0.01840363,0.03744719,0.02097229,-0.05297509,-0.0512369,-0.0702332,-0.06081157,0.05815239,-0.05565445,0.04429055,-0.01675293,0.04136177,0.01030277,-0.02014967,-0.02382937,-0.04396749,-0.07118394,0.03499336,0.04260445,-0.01324002,-0.07776804,0.01165586,-0.02553484,0.04867856,0.02664693,0.00612075,0.08906997,0.09206662,0.02679425,0.06083113,0.07408519,0.0327348,-0.17182417,0.04425028,0.05092292,0.03797841,-0.00665344,0.00370322,-0.0267315,0.06702208,-0.03107976,0.04186031,-0.03462646,0.06236202,0.00125852,-0.00519195,-0.03447284,-0.01823238,-0.05322949,-0.01139126,-0.05643794,-0.03421556,-0.01647468,-0.03381908,0.00452194,0.02650268,0.04303001,0.03430203,0.07371525,-0.06053546,0.04849933,0.07943884,0.00894773,-0.01340962,0.00166298,0.01456971,0.04538444,0.00347704,-0.01405904,0.09381536,0.05813035,-0.007822,-0.0375559,0.00123192,0.04693067,-0.02483344,-0.02072886,-0.03823056,-0.03791513,0.02288482,0.0480891,0.03977656,0.07809221,-0.05609151,-0.03390507,-0.01042247,-0.01615433,-0.00435238,0.05492625,-0.02226796,-0.06575999,-0.00917526,0.01720486,0.00905324,0.01250558,0.05300703,0.0318609,0.0893921,0.04750884,0.02368191,-0.00583522,-0.01650406,-0.1215553,-0.01550432,-0.04164949,0.00823084,0.00645844,0.00606922,0.00837754,0.06793405,-0.04035733,-0.0101334,0.05617195,-0.1324894,-0.00120128,0.05397959,0.02862102,-0.02882646,0.01521793,0.03716556,0.01266193,0.01901539,-0.03961984,-0.08549915,-0.02755987,-0.02014752,0.11280727,0.08455028,-0.05775791,0.02332739,0.00222251,-0.0407328,0.01995062,0.13797294,0.02090011,-0.06307552,-0.02467998,0.02345301,-0.0498274,-0.1132412,-0.0303946,0.01901539,-0.04669675,0.0230195,0.07229812,-0.038117,-0.05132251,-0.02318865,0.00377224,-0.02104931,0.02918936,0.00651396,-0.04675481,-0.00293053,-0.01427987,-0.08975858,0.01570447,-0.00815473,-0.00542976,0.04550835,-0.02847783,-0.01867163,-0.05229108,0.0378404,-0.03314804,-0.01618321,-0.03380745,-0.01687269,0.06549729,0.01516636,-0.01431984,-0.02041592,0.02716197,0.01718154,-0.06506342,0.08126893,0.04408807,-0.08652815,0.05645696,0.02154285,-0.0174438,0.0277089,0.01418808,0.04455571,-0.02952795,0.0120679,0.03394883,0.02631705,0.00675672,0.03521496,-0.01350645,0.01432427,-0.0564789,-0.1708183,-0.01645587,-0.0329894,0.00630748,0.01066716,-0.02142691,0.05682085,0.00595345,0.07234331,0.08914435,0.06773214,-0.06284186,0.00652559,0.01711294,-0.01388127,-0.03812551,-0.09998883,-0.02525686,-0.02199029,0.06233155,-0.0350349,0.00871448,-0.07064444,-0.04139014,0.07990301,-0.03948799,0.1339843,0.03717498,-0.03019431,0.02496941,0.06719995,0.01172345,-0.04767161,-0.04000387,0.01437327,0.00805924,0.00450165,-0.01143537,-0.05514555,-0.0048664,-0.09982401,0.0178762,0.01108466,-0.08963405,-0.0499428,0.00700979,-0.05387199,0.04351956,-0.00142357,0.05667787,0.07187779,-0.00011247,0.01848656,0.01545224,0.04278504,-0.01436382,-0.06090407,0.02236147,-0.00554814,0.02456921,0.00440701,-0.05090894,0.06021588,-0.03713623,-0.00301443,0.00653692,-0.00648037,-0.00435217,0.0195352,-0.01903366,0.00209529,0.12594819,0.01385951,-0.01520713,-0.02268605,0.02281172,0.05087236,-0.06759672,0.02489884,-0.01265274,0.0110314,-0.03171221,0.00211751,0.07098011,0.05580977,-0.01782459,0.02574285,0.0857165,0.00200253,0.0012789,-0.01237739,0.00343005,-0.02356901,0.00505017,-0.01341678,0.0384902,-0.24134256,0.02882886,-0.03342709,0.10387667,0.01709324,0.01157354,-0.00323261,0.01377238,0.00501108,0.00601377,0.02826406,0.02014564,-0.00038386,-0.06530681,-0.02843261,0.00114828,-0.02180999,-0.05314965,0.05839157,0.02719617,0.07837998,0.02791958,0.24385446,0.00064275,0.02759846,0.0211397,0.05126071,0.04946972,-0.08271557,0.02868515,-0.01268554,-0.00322224,0.07036378,-0.02266789,-0.05566077,0.02110278,-0.01635862,-0.04453212,-0.02460038,-0.02512933,-0.07429031,-0.03119234,-0.04957746,0.03955536,0.1351932,0.00164582,-0.01154191,-0.10654052,0.03318924,0.10508265,-0.09071505,-0.06473766,-0.0629577,-0.03070296,0.0306281,0.07179838,-0.01879557,0.01639257,0.01107415,-0.01002351,-0.00362728,-0.01078165,0.02705148,0.01852002,0.02612208],"last_embed":{"hash":"1hfa638","tokens":450}}},"text":null,"length":0,"last_read":{"hash":"1hfa638","at":1753423497745},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.","lines":[39,173],"size":16367,"outlinks":[{"title":"_**Piracy of Fundamental Formula of Gambling on eBay**_","target":"https://forums.saliu.com/gambling-formula-pirated-ebay.html","line":5},{"title":"_**Steve Players Piracy of Lottery Wonder Grid Systems**_","target":"https://forums.saliu.com/steve-player-lottery-piracy.html","line":6},{"title":"Pick-4 frequency statistical report divides the digits in 3 groups: hot, mild, cold.","target":"https://saliu.com/images/pick-4-lottery-strategy.gif","line":16},{"title":"The pick 4 lottery strategy based on frequency, stats has a good winning rate.","target":"https://saliu.com/images/lottery-strategy-stats.gif","line":31},{"title":"Every lottery strategy has a pivot: Main filter or restriction or condition.","target":"https://saliu.com/images/lottery-strategy-pivot.gif","line":35},{"title":"A winning lottery strategy always applies the reverse as in the case of lotto decades.","target":"https://saliu.com/images/lottery-strategy-decades.gif","line":39},{"title":"The winning lottery strategy always applies the reverse as in the case of number skips.","target":"https://saliu.com/images/lottery-strategy-skips.gif","line":41},{"title":"This winning lottery strategy also applies the odd, even, low, high lottery numbers.","target":"https://saliu.com/images/lottery-strategy-odd-even.gif","line":43},{"title":"Combining more lottery strategies and playing the aggregated result increase the profits.","target":"https://saliu.com/HLINE.gif","line":47},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":53},{"title":"LieStrategies.txt","target":"https://saliu.com/freeware/LieStrategies.txt","line":62},{"title":"ST5-Any1-1-MX-1","target":"https://saliu.com/freeware/ST5-Any1-1-MX-1","line":63},{"title":"ST5-Any2-1-MX-1","target":"https://saliu.com/freeware/ST5-Any2-1-MX-1","line":64},{"title":"Pick 4 lottery strategies hit in the same drawings, multiplying total winnings by a lot of money.","target":"https://saliu.com/HLINE.gif","line":66},{"title":"_**lotto strategies are based on filters**_","target":"https://saliu.com/filters.html","line":68},{"title":"Lottery strategies based on number frequency can be very effective only with Ion lotto software.","target":"https://saliu.com/ScreenImgs/lottery-frequency.gif","line":74},{"title":"_**Frequency groups pick-3, pick-4 lotteries, lotto 5/39, 649 lotto**_","target":"https://saliu.com/frequency-tables.html","line":104},{"title":"_**Statistical Frequency Reports for Pennsylvania 5/39 Lotto**_","target":"https://saliu.com/frequency-reports.html","line":106},{"title":"Rank lottery numbers by frequency from hot to cold for Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":108},{"title":"Lottery software generates combinations from systems, strategies based on skips of numbers.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":128},{"title":"_**Skip Systems, Software for Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":130},{"title":"Lottery players use statistics such as frequency to reduce the amount of lotto combinations to play.","target":"https://saliu.com/HLINE.gif","line":132}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11903106,-0.05334021,-0.02834032,-0.05940557,-0.00827061,0.01816458,-0.00155324,0.00499686,0.05679594,0.02375344,0.04789918,0.03944035,0.01007297,-0.01646247,0.00365346,0.01310252,0.01538207,-0.01965469,-0.00095367,0.01917493,0.07778182,-0.01288545,-0.01774908,-0.05521061,0.03530984,0.01379712,-0.04689011,-0.07491935,-0.06348948,-0.21023826,0.01912948,0.02268892,0.02823188,-0.0516493,-0.01614654,-0.07830901,-0.05762678,0.0594462,-0.05049841,0.04685516,-0.04519531,0.02350886,0.00948414,-0.04054968,-0.01774732,-0.04594588,-0.05025772,0.0411078,0.02625994,-0.03173599,-0.08043416,0.01231588,-0.01957135,0.02577989,0.01804888,0.02119284,0.08052082,0.10141634,0.03005075,0.06314594,0.07630603,0.04045286,-0.17250951,0.0352773,0.02912024,0.06289908,0.0085618,-0.00049924,-0.03457425,0.08422719,-0.02815511,0.02289537,-0.03517569,0.08305352,0.0176791,0.00758598,-0.01309904,0.01232489,-0.05299557,-0.00023921,-0.04990651,-0.05373873,-0.00925998,-0.02417138,-0.00957819,0.01159198,0.04560338,0.02017102,0.08943722,-0.05412342,0.04467066,0.06841668,0.02413519,-0.02038733,-0.01561299,-0.00971892,0.05258386,0.01797369,-0.00384074,0.10611437,0.03912995,-0.01060296,-0.02472858,-0.00893358,0.05042842,-0.02585815,-0.02845202,-0.03051487,-0.03734019,0.02805861,0.05676036,0.05065835,0.07211021,-0.06741086,-0.02948549,-0.0015239,-0.02017533,-0.02010008,0.04727116,-0.03669128,-0.07304886,-0.02403673,0.01904342,-0.01292121,0.00060905,0.04696102,0.02778141,0.09424021,0.01983049,0.01654356,-0.01786003,-0.04164083,-0.12618393,-0.00371655,-0.03235648,0.01794345,0.00963982,-0.02737589,0.01513144,0.06803753,-0.06702695,-0.01676289,0.04068309,-0.12142086,-0.00838841,0.05103051,0.04733935,-0.01602686,0.01575559,0.03950869,-0.02408306,0.0070538,-0.02672242,-0.08862461,-0.02687436,-0.01016592,0.10165139,0.09264839,-0.0586506,-0.00113363,0.0124417,-0.01710014,-0.00721107,0.14134966,0.01912905,-0.08801183,-0.00847442,0.03174311,-0.02724876,-0.08793394,-0.03548944,0.03355645,-0.04549066,-0.00275492,0.0671626,-0.02160775,-0.0321338,-0.01326186,0.00912952,-0.02625083,0.01417377,-0.02564405,-0.04334214,0.02138039,-0.01281766,-0.10560024,0.00558637,0.00653352,0.02063701,0.06252074,-0.03935959,-0.00869057,-0.05674395,0.02940163,-0.0379693,-0.02790626,-0.03712955,-0.00914472,0.07636676,0.00110922,-0.03306472,-0.00456433,0.02778548,0.02947946,-0.07366923,0.06017607,0.05016059,-0.08408153,0.04003106,0.02913727,-0.01753716,0.05457295,0.00295369,0.04120453,-0.02888891,0.00104131,0.01180861,0.02884056,-0.00179965,0.02648531,-0.02525958,0.04034929,-0.03669047,-0.17778391,-0.02741662,-0.03836814,0.00160294,0.00285282,-0.01827439,0.05199183,0.01655438,0.05451573,0.08260264,0.06619368,-0.07615801,0.0111292,0.01231259,-0.0176076,-0.06176475,-0.07914101,-0.03279351,-0.02032621,0.05250646,-0.05712518,-0.00013013,-0.06019801,-0.031925,0.08838515,-0.03157859,0.12730363,0.03888569,-0.01717533,0.04495598,0.05377277,0.00633947,-0.05200024,-0.06769726,0.01135253,0.0423505,0.02036117,-0.0099882,-0.02738885,-0.00743868,-0.09018611,0.01699399,0.01349075,-0.086846,-0.04643189,0.00435914,-0.03839537,0.03164901,0.01223233,0.08156724,0.05340878,0.00920872,0.00365633,0.04315739,0.04062635,-0.00663067,-0.07006083,0.03826145,-0.0118692,0.02149448,0.00881884,-0.03524082,0.06433132,-0.04411011,-0.00886831,0.00567623,0.0077698,-0.00181231,0.00356238,-0.02622797,0.00230728,0.17446513,0.02081764,-0.03432551,-0.02304238,0.01155598,0.0489314,-0.07923088,0.00167631,-0.00208003,-0.01320289,-0.02343336,0.00698627,0.06359062,0.05730832,-0.00823186,0.03430797,0.05899069,-0.00435408,0.00093758,-0.01647346,-0.0136532,-0.0290039,-0.00230032,0.00088996,0.05037476,-0.22090976,0.01153732,-0.02434405,0.11345868,0.02793331,0.02692436,-0.00331358,0.00781011,0.00519607,0.01107856,-0.00123056,0.01587675,-0.01067559,-0.04886987,-0.00423941,-0.00093394,0.01229964,-0.0471028,0.06301461,0.03139867,0.0529885,0.03638667,0.24889471,0.0107659,0.02096538,0.02587506,0.06770239,0.07260658,-0.08264588,0.02594695,0.00947575,-0.01769639,0.04407987,-0.02268109,-0.07404458,0.01115336,-0.00825664,-0.02339084,-0.00976309,-0.02806976,-0.08299457,-0.03067169,-0.06886592,0.02504306,0.12776311,0.00714353,-0.01566914,-0.08884259,0.02563076,0.10764594,-0.08870804,-0.07988659,-0.06742574,-0.03465653,0.0401077,0.05826009,-0.03747743,0.01242197,-0.00443957,-0.02951034,0.01861195,0.00356794,-0.00230362,0.01145925,0.01639575],"last_embed":{"hash":"io6en7","tokens":117}}},"text":null,"length":0,"last_read":{"hash":"io6en7","at":1753423497913},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{1}","lines":[41,42],"size":350,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05653155,-0.04729768,-0.04286586,-0.01833173,-0.03046696,0.08061536,0.0058282,0.01404426,0.06721998,-0.00463282,0.01594997,0.004621,0.02487986,-0.00732819,0.00238318,-0.02041952,-0.01171222,-0.04862443,-0.05472057,-0.00197774,0.04894123,-0.06962296,-0.01611822,-0.0482018,0.07431934,-0.01297906,-0.03893376,-0.09490719,-0.0823204,-0.2199993,0.02741095,0.05934015,0.04231288,-0.08193708,-0.08209468,-0.05616438,-0.05101286,0.08149976,-0.06194739,0.05647534,-0.01289647,0.02382274,0.02277545,-0.01771559,-0.02401908,-0.02686363,-0.07074112,0.00961023,0.03900238,-0.02571003,-0.05624353,-0.00005917,-0.01871462,0.05493964,0.05435325,0.02529813,0.05569445,0.05816035,0.03864729,0.05258269,0.05397458,0.03226788,-0.17418224,0.05097242,-0.00294139,0.00999262,-0.00830861,-0.02062386,-0.00232911,0.04484501,-0.00138743,0.05466757,-0.00794036,0.01716993,0.01995136,-0.02404375,-0.05314316,-0.05026414,-0.04436456,0.00815993,-0.06520475,-0.01471744,-0.01618319,-0.05509673,0.04274506,0.01839137,0.03778308,0.03738162,0.03665322,-0.03812545,0.05527811,0.09460706,-0.02603922,-0.00948029,0.03830987,0.03611984,0.03637881,-0.03915983,-0.028545,0.11342625,0.03687053,-0.00099967,-0.02423092,0.04034004,0.03979456,-0.02481681,-0.03622125,-0.07074124,-0.03735473,0.0339974,0.05516977,0.02773741,0.06773879,-0.05256711,-0.03362551,-0.01400324,-0.002776,0.00082827,0.06252316,0.00521676,-0.01892426,-0.00825835,-0.00223193,0.01731282,0.02935166,0.03939817,0.02789703,0.08368573,0.05749803,0.00638149,0.02655022,0.0177406,-0.16641094,-0.02143647,-0.05879848,-0.01832385,-0.00294272,-0.00718767,-0.0082875,0.05190509,0.0027106,-0.02953804,0.07795078,-0.13799198,-0.00704609,0.09741845,-0.00836165,-0.00073919,0.01394645,0.04365069,0.02492396,0.01523698,-0.05509103,-0.07286134,-0.04135145,-0.00279093,0.07031002,0.05520418,-0.03695722,0.02288217,-0.02774323,-0.05337592,0.00868899,0.13417503,-0.00435889,-0.06849529,-0.0408756,0.01640487,-0.05793968,-0.11264679,-0.03015705,0.00703072,-0.026818,0.02934096,0.07824337,0.00034398,-0.04263345,-0.05650192,0.0174916,-0.00700044,0.03659592,0.01894366,-0.0273662,0.00473543,-0.01092896,-0.05136122,0.04126053,-0.02276614,0.01371881,0.02754777,-0.04499239,0.00779757,-0.03960146,0.02301835,-0.04777329,-0.01440389,-0.0307819,-0.01612018,0.0461943,0.00931698,-0.02512233,-0.01521103,0.03263894,0.02907848,-0.03044604,0.07152685,0.03444353,-0.05906267,0.08659181,0.01147038,0.01004983,-0.003304,0.05247853,0.02717319,-0.04369847,0.01172771,0.01354154,0.02221034,-0.00720502,0.04131838,0.00618278,0.01462576,-0.07735302,-0.17221317,-0.04060554,0.00734697,0.01234828,0.01006359,0.01381234,0.0291175,-0.00238728,0.06396983,0.08428676,0.0836185,-0.02558592,-0.02821887,0.0345937,-0.0122007,-0.01917147,-0.10357049,-0.01911445,-0.02466296,0.05790111,-0.00359607,-0.00484709,-0.06036786,-0.05881194,0.06660296,-0.05558769,0.11885956,-0.00628638,-0.01361929,-0.01717785,0.06907859,-0.00744408,-0.03372975,0.02342843,0.02775607,-0.00331246,-0.02252383,-0.02151694,-0.09141762,0.00058035,-0.07288267,0.0268324,-0.00094134,-0.09765792,-0.03273314,-0.00592137,-0.04413729,0.05674583,0.00872186,0.01474971,0.07216068,-0.01754464,0.03972481,0.01812931,0.06432032,-0.0278894,-0.07889276,0.01910664,-0.02656059,0.03841662,-0.01128889,-0.05722688,0.07361443,-0.02693573,0.01946891,0.03849149,-0.01951673,-0.01545198,0.01227669,0.01125634,0.01631522,0.07037831,0.01437984,-0.00676486,-0.02595056,0.01529295,0.04949077,-0.04547656,0.00693165,-0.02083099,0.04963775,-0.06861704,-0.01822037,0.05697162,0.03985454,-0.01024053,0.04569475,0.09729865,-0.0027363,0.00412661,0.00871944,0.04350244,-0.01247266,0.02675478,-0.01874878,-0.00964504,-0.25769302,0.04443011,-0.06456892,0.06055068,0.00464598,-0.00043969,0.01152905,0.01779054,0.00197954,-0.01575662,0.07291791,0.03843459,0.01126406,-0.07146806,-0.00833193,-0.01502381,-0.01493427,-0.07003171,0.04318184,0.03133574,0.08986497,0.03661795,0.22912249,-0.01822946,0.02695844,0.02777971,0.04819529,0.02272192,-0.05346842,0.03927703,-0.0125609,-0.00759737,0.10831044,-0.01455411,-0.06320683,0.01500231,0.00355711,-0.03036325,-0.04707499,-0.00418202,-0.06160385,-0.02603989,-0.02952919,0.02738377,0.15648191,-0.00752261,-0.03167886,-0.09379865,0.04994792,0.08392789,-0.08539802,-0.06278104,-0.06281575,-0.01537003,0.03104189,0.05927277,0.01659665,0.01180542,0.03075489,0.00156521,-0.00596407,-0.0117923,0.04424187,0.0256303,0.01623209],"last_embed":{"hash":"1gnrn5p","tokens":400}}},"text":null,"length":0,"last_read":{"hash":"1gnrn5p","at":1753423497952},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{4}","lines":[46,57],"size":1256,"outlinks":[{"title":"Pick-4 frequency statistical report divides the digits in 3 groups: hot, mild, cold.","target":"https://saliu.com/images/pick-4-lottery-strategy.gif","line":9}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09492528,-0.05175606,-0.04029967,-0.01796053,-0.03882108,0.11283377,0.00689405,0.00708486,0.06855863,-0.00156795,0.05079028,0.02529463,0.02461169,-0.00750927,-0.00832101,0.02621538,-0.02263386,-0.11235055,-0.05209604,0.02669598,0.07161327,-0.00855863,-0.02377437,-0.02922308,0.07471863,-0.03865443,-0.04374075,-0.09736516,-0.08521271,-0.23252691,0.02690331,0.00576736,0.02353323,-0.07785272,-0.06153861,-0.06335393,-0.04794966,0.08502787,-0.02073006,0.06315969,-0.00142477,0.04435203,0.02043419,-0.06664429,-0.0426373,-0.03123858,-0.03624691,-0.0107111,0.05393431,-0.02747066,-0.06972717,-0.01651608,0.01201487,0.0316372,0.05744058,0.00992417,0.03694697,0.04444379,0.04733159,0.05073649,0.03690013,-0.00443226,-0.19785368,0.02791343,-0.04127268,0.01172767,-0.00621109,-0.0490156,0.0207727,0.05302904,0.01535983,0.03726257,0.02122802,0.03112216,0.03893341,-0.02151733,-0.01121147,-0.0209409,-0.04544009,0.02855041,-0.06234537,0.00899422,0.00162204,-0.02903719,0.04430943,0.01878347,0.03630415,0.0318835,0.04913449,-0.05963815,0.06158337,0.08390693,0.00160129,0.00220415,-0.00430261,0.02264056,0.04124923,-0.04628586,-0.00826024,0.10948902,-0.0020361,-0.00260706,-0.0517728,0.04890066,0.02690812,-0.0342567,-0.06094158,-0.060534,-0.05450173,0.02716505,0.05266107,0.01120793,0.12317448,-0.03838804,-0.00801965,0.00667464,-0.0020954,0.00397596,0.05951691,0.02384499,-0.00557492,0.00030623,-0.00295462,0.00187251,-0.00924217,0.07931674,0.04608438,0.09446549,0.01041544,-0.03265935,0.04416601,-0.02896231,-0.16440588,-0.02117835,-0.06490511,-0.00384962,-0.01537849,0.00131179,0.01615965,0.03788977,-0.02263233,-0.0320757,0.04865266,-0.13191652,-0.00993695,0.06205516,0.00723627,0.00695747,0.03937327,0.01989296,-0.00702757,0.02833863,-0.02820592,-0.07243478,-0.04555544,0.02403435,0.04789835,0.09021258,-0.05162773,0.01101947,-0.01986993,-0.05895606,-0.01234954,0.10383847,-0.02747313,-0.1041915,-0.01383352,0.02594177,-0.02470577,-0.08072565,-0.01475114,0.01483555,-0.01032078,-0.01412242,0.06449776,-0.01121539,-0.01926479,-0.05127795,-0.00682195,-0.00771732,0.03778306,-0.00661793,-0.02231359,0.05208116,-0.01976671,-0.05931393,0.04555728,-0.04528029,0.03983649,0.03750565,-0.07594105,-0.00665401,-0.04920783,0.0485297,-0.0515704,-0.04051395,-0.00776051,-0.01749126,0.07025652,0.01707263,-0.02795629,-0.0302262,0.02814562,-0.00651187,-0.01025499,0.05387124,0.0345374,-0.04976578,0.09822373,0.02617007,0.01136844,0.01562555,0.05516493,0.01710484,-0.01804533,0.0062619,-0.03914588,0.02457297,-0.0161794,-0.00286119,0.02530036,0.03061965,-0.05393727,-0.19230142,-0.02596874,-0.01011069,-0.03696674,0.02711221,0.0342693,0.01927101,-0.01818884,0.09412667,0.09869751,0.03946728,-0.00902072,-0.04066538,0.01251526,-0.02252686,-0.02601106,-0.09979279,-0.03325862,-0.03465777,0.0522479,-0.01344005,-0.02331889,-0.08597001,-0.06466193,0.05585173,-0.05017116,0.12315,0.01017884,-0.02127158,-0.04625902,0.06732441,-0.00377568,-0.01920568,0.02139832,0.01560134,0.02938409,-0.00092686,0.00196392,-0.06633161,-0.02107991,-0.06927188,0.03165465,0.00944876,-0.10106225,-0.00847786,0.02466039,-0.03319072,0.07310063,0.02211412,0.0511579,0.07961346,-0.02219487,-0.00646632,0.0456918,0.08753917,-0.014442,-0.09236449,0.05127929,-0.00707911,0.03390467,-0.0034866,-0.03070522,0.06930704,-0.03531764,-0.02619055,0.0295388,0.00936271,-0.03629818,-0.01522528,-0.02546328,0.0047662,0.11239938,0.03384654,-0.03225078,-0.00340134,0.02029229,0.02330584,-0.04207733,-0.02170576,0.00310503,0.04010512,-0.05789979,0.00558492,0.05386833,0.03460733,0.0118038,0.04380685,0.07144195,-0.00682557,0.00090844,0.01134328,0.00401101,-0.02934812,0.0075153,-0.01201204,0.00506161,-0.23871355,0.08226832,-0.04079063,0.05907148,0.02073105,-0.00369786,0.00178758,0.03569409,-0.00084088,-0.025194,0.04285203,0.04126788,0.02932194,-0.04831742,0.00861584,-0.01547591,-0.00859813,-0.0727941,0.04688922,0.03659306,0.05583028,0.0425946,0.21697119,0.00186257,0.0257143,0.02260454,0.05307814,0.04679177,0.01545745,0.02916586,-0.00524511,0.02008982,0.05932293,-0.01277801,-0.05953217,-0.01113392,-0.01927504,0.02079374,-0.02438498,-0.0070744,-0.08212842,-0.01245566,-0.00670029,0.02021568,0.12463062,-0.00561683,-0.03777807,-0.09015276,0.03333771,0.07091873,-0.06962291,-0.06393109,-0.01692995,-0.02975689,0.03701987,0.07279128,0.00287552,0.00617464,0.00639288,-0.03149668,-0.00018988,-0.00533941,0.01592224,-0.00145626,0.01579429],"last_embed":{"hash":"1ncmqu","tokens":169}}},"text":null,"length":0,"last_read":{"hash":"1ncmqu","at":1753423498076},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{5}","lines":[58,58],"size":549,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10626852,-0.04986877,-0.03663975,-0.02676693,-0.03992138,0.05928842,0.02384737,0.03026874,0.07828446,-0.0132949,0.04832814,0.03850253,0.011306,-0.00350764,-0.0177315,-0.01711172,0.01065413,-0.05717125,-0.05103165,0.03531713,0.04181227,-0.0634376,-0.01242037,-0.05000903,0.08335503,-0.00242381,-0.03456787,-0.06204274,-0.09142851,-0.25366035,0.01649842,0.00898264,0.01465566,-0.04536815,-0.07299614,-0.02814811,-0.0421332,0.07062814,-0.00840927,0.06298775,0.00348939,0.0604519,0.00130684,-0.03217292,-0.03324808,-0.05201336,-0.08408739,-0.04141759,0.04721371,-0.00349133,-0.08613829,0.01337666,0.01089318,0.03530208,0.07770024,0.01641117,0.05534663,0.0653742,0.03617957,0.04289447,0.0348484,0.00420595,-0.22302876,0.02765998,-0.04993785,0.03901598,-0.01583192,-0.02757622,-0.00713494,0.04264922,0.01163893,0.04304925,-0.00698646,0.01284051,0.04498675,-0.03325215,-0.02242768,-0.00111947,-0.0321531,0.02782744,-0.0209834,-0.01740384,-0.02186018,-0.02035601,0.032077,0.02017412,0.0112338,0.06299406,0.0554109,-0.06116321,0.06427049,0.04531002,-0.00297168,-0.03246213,0.03717555,0.06132238,0.04580294,-0.02746798,-0.05300238,0.08181286,0.01397847,0.01403349,-0.04247131,0.00538832,0.04177538,-0.01878643,-0.04358239,-0.03224476,-0.04065614,0.03812594,0.04803912,0.00460836,0.11283872,-0.08127941,-0.01233612,0.02769927,-0.00749873,0.01419461,0.05718294,0.00504094,-0.00095453,0.01029417,0.00208118,0.02479823,-0.0145511,0.04024909,0.02214724,0.08923922,0.03412933,0.02216015,0.02825245,-0.02235498,-0.14975254,-0.02884274,-0.02387048,-0.01669719,-0.00098878,-0.01238628,0.01861044,0.03579254,-0.03035782,-0.058316,0.04285376,-0.12804328,-0.04896844,0.07511172,0.02225728,0.01265396,0.0367666,0.00764544,-0.00022667,0.04179909,-0.03208093,-0.07901982,-0.04403388,0.01776947,0.05068139,0.07047445,-0.02762747,0.03015744,-0.02693887,-0.04156874,-0.02045252,0.10090131,-0.01090634,-0.10962419,-0.02382835,0.00371374,-0.0413605,-0.09696538,-0.01170464,-0.00887698,-0.03250387,0.00248493,0.06609949,-0.02772208,-0.02904485,-0.03774466,-0.00436646,0.00833902,0.03134788,-0.00166132,-0.03680962,0.00936105,-0.03081197,-0.06634632,0.03744138,-0.05289501,0.05362097,-0.00020409,-0.05737277,-0.01830281,-0.03674354,0.04887249,-0.04835924,-0.01267532,-0.03447481,-0.0122171,0.08370164,-0.00308138,-0.03200043,-0.03090957,0.02280187,0.01491111,0.0072095,0.0549852,0.05287304,-0.02313883,0.09617108,-0.01382757,0.01201553,0.01291058,0.06467726,0.01363084,-0.04953733,0.00003087,-0.00942728,0.03376772,-0.02156194,0.05304929,-0.02767025,0.04201299,-0.04944817,-0.1976696,-0.03932749,-0.04637126,-0.02212591,0.02059882,0.01614889,0.03870891,-0.00637872,0.08530328,0.08873004,0.05538383,-0.02432512,-0.01937163,0.0207324,-0.01515688,-0.03311443,-0.07398558,-0.015102,-0.03683979,0.02194748,-0.0090144,-0.02141458,-0.06663533,-0.03613685,0.05013775,-0.03531321,0.11679097,0.04086211,-0.01167931,-0.02297982,0.07580858,0.00515763,-0.0244236,-0.02826398,0.01055774,-0.0000784,0.0310967,0.02793362,-0.07951763,0.00518038,-0.06808658,0.01351946,0.03007013,-0.12139262,-0.01257064,0.02753577,-0.04329641,0.07138715,0.00581558,0.0546358,0.09899411,-0.01411579,0.0659363,0.00819631,0.07936455,-0.05062121,-0.08968015,0.03357146,-0.01982394,0.06411687,-0.03717572,-0.07282794,0.06701149,-0.00256061,0.03398701,0.02716402,0.02131866,-0.02130503,-0.00544544,-0.01767162,0.0006696,0.05705621,0.03788754,0.00912993,0.01822483,0.03038125,0.04524098,-0.02123596,0.0189314,0.00556912,0.04826164,-0.03105463,0.02665287,0.06129967,0.03813281,0.00118457,0.04249268,0.05933107,0.00175675,-0.02074579,-0.02146555,0.02126035,-0.02815938,-0.01664019,-0.02087028,-0.01150611,-0.25893417,0.03484127,-0.05234765,0.0459724,0.00505693,0.01007489,0.01742684,0.00835396,0.00055462,0.00307546,0.05779846,0.05276264,0.0089613,-0.07777997,0.01031157,-0.02724452,-0.03272374,-0.04506006,0.04843754,0.02175546,0.06957372,0.03504766,0.20581982,-0.04408302,0.01172562,-0.01516631,0.03165611,0.0321539,-0.00015358,0.01625704,-0.02545423,0.02195028,0.0623906,-0.01391382,-0.03525941,-0.01694383,-0.02887399,0.02771556,-0.06095547,-0.00969667,-0.03218341,-0.02762358,-0.00970128,0.05638493,0.13528745,-0.01145343,-0.01951529,-0.10151306,0.04790287,0.1084808,-0.07570422,-0.06339555,-0.01723488,-0.00646658,0.00467154,0.08445649,0.00873627,0.01077263,0.02903002,-0.04253813,0.02140652,0.0242433,0.02641678,0.0218455,0.01438003],"last_embed":{"hash":"g4cgmm","tokens":361}}},"text":null,"length":0,"last_read":{"hash":"g4cgmm","at":1753423498130},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{6}","lines":[59,66],"size":1263,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07058544,-0.04224543,-0.02844132,-0.00020651,-0.03428389,0.07029396,0.04351214,0.01450458,0.05440752,0.01223626,0.00325479,-0.00034399,0.03667548,-0.03575427,-0.01454903,-0.01637284,0.00976591,0.01362274,-0.03099399,-0.00644154,0.08942877,-0.05197685,-0.0158668,-0.02884604,0.10808453,-0.00896352,-0.04138147,-0.0823405,-0.06046733,-0.24194853,0.03083907,0.02249829,-0.01359284,-0.05930404,-0.06090307,-0.04707135,-0.0722518,0.10256521,-0.0487996,0.08609948,0.02197787,0.01364639,0.04375133,-0.0026358,-0.00333715,0.000126,-0.08718428,-0.00506197,0.00645057,-0.0042272,-0.06622622,0.04954229,-0.01699374,0.02981136,0.06110879,0.04427408,0.07643085,0.09799329,0.03699606,0.04556143,0.05461195,0.05068235,-0.19064218,0.04356852,-0.02566155,0.02024391,-0.01348897,-0.0205481,0.00021074,0.04385502,-0.00688602,0.01481633,-0.03914836,0.02688109,0.01843489,-0.04480283,-0.03469813,-0.02211708,-0.03911698,0.01587358,-0.02972567,-0.01885682,-0.01510273,-0.03084761,0.03368022,0.01906856,0.01765317,0.03111481,0.07286741,-0.07444179,0.05868095,0.05895487,-0.00687575,0.00883124,0.03536778,0.02536972,0.04831115,0.01406716,0.00454404,0.10884095,-0.00890473,0.00443062,-0.03337186,0.06360479,0.06669023,-0.04804058,-0.0329347,-0.04459869,-0.0361206,0.04361065,0.02780033,0.01882173,0.06778947,-0.05987523,-0.01138866,0.00872632,-0.00829775,0.01056926,0.05574955,-0.01813725,-0.03346928,0.01009571,-0.00953514,-0.00642094,0.03548744,0.01391491,0.01776804,0.07702256,0.04223305,-0.01507539,0.03745305,-0.01908352,-0.14979294,-0.0379145,-0.04901042,0.00307481,-0.0091177,0.01124562,0.00593346,0.03252643,-0.02154063,-0.0509658,0.04902625,-0.100324,-0.02060791,0.11447934,-0.01644542,0.05620594,0.00699431,0.01660893,-0.00271854,0.02568831,-0.05625078,-0.08472035,-0.04552342,0.02049071,0.0381523,0.08220253,-0.01438027,0.00815012,-0.04024059,-0.0346509,-0.01620834,0.11159019,-0.02033073,-0.06685244,-0.03402869,0.01855835,-0.04301064,-0.0861905,-0.05459793,-0.00739155,-0.03863018,-0.01294748,0.07689183,-0.00171867,-0.04875171,-0.07140267,0.00062497,-0.00563242,0.01465149,-0.03060957,-0.04977448,-0.00123913,-0.04018307,-0.04366039,0.01534644,-0.03469155,0.05950867,0.01458832,-0.1002043,-0.00123269,-0.06307073,0.0222395,-0.03543713,-0.03020481,-0.04147154,-0.04197752,0.05773534,-0.03130372,-0.06204497,-0.02180806,0.032159,0.01596384,-0.03577089,0.06448083,0.02760072,-0.05622381,0.08071345,0.00829378,-0.01332552,0.02735639,0.02233681,0.02954569,-0.02560681,0.01138537,-0.00645471,0.03728882,-0.02468118,-0.00074315,-0.00682053,0.04019697,-0.06590202,-0.19153741,-0.03425089,-0.01640115,0.01597327,0.04454852,-0.03353706,0.00851563,0.0012106,0.03468083,0.08122706,0.06613225,-0.02227851,-0.04600073,0.04593185,-0.00581971,-0.02873106,-0.08177362,-0.03294135,-0.02864175,0.01964721,0.00958013,0.02074062,-0.02389972,-0.0635252,0.06781564,-0.02580701,0.09675474,0.02401113,0.01399503,0.00359143,0.06195768,-0.01291993,-0.05342863,0.00562059,0.03818415,0.01085636,-0.01667174,-0.01644756,-0.0855224,-0.00263045,-0.04075772,0.01815842,-0.00056279,-0.09996196,-0.02850786,0.01016278,-0.01902059,0.06190516,0.00798085,0.07043696,0.05352902,0.00296472,0.0312158,0.01447299,0.06274312,-0.05108134,-0.06476036,-0.00797714,-0.02548786,0.05090261,-0.0454987,-0.03804205,0.07182758,-0.01413889,0.05984637,0.04081132,0.00548537,-0.00458671,0.02834226,0.02654204,0.02150928,0.13692877,0.05261141,0.00082996,0.00243252,0.05177245,0.0567182,-0.0300374,0.00736706,-0.02658531,0.03156162,-0.06006473,0.04420992,0.05488545,0.05586219,-0.0286492,0.06003372,0.05889997,0.00370648,0.00743577,0.00793301,0.04204685,-0.02100995,0.04228378,-0.01599668,-0.00789111,-0.2601929,0.00795414,-0.0718725,0.03444258,0.01161575,-0.01369663,0.00926581,-0.00172419,-0.00187162,-0.02711469,0.02539653,0.03045883,0.03145291,-0.05350504,-0.00871711,-0.01051499,-0.00466608,-0.0599314,0.0997003,0.02672081,0.09166321,0.03785516,0.22797225,-0.02144126,0.00613819,0.0043877,0.03805715,0.00889634,-0.03083686,0.02362668,-0.02008273,-0.03113312,0.0793632,0.01998952,-0.05461841,0.00840138,-0.02527178,0.01743585,-0.02696631,-0.01738988,-0.07378419,-0.03895392,-0.01181078,0.00281891,0.15442646,-0.04385484,0.0063685,-0.09251736,0.03209232,0.08166367,-0.05673937,-0.06504732,-0.0693621,-0.01669848,0.02228566,0.05777175,-0.00618149,0.00504983,0.04660403,-0.01873792,0.01823769,0.0161413,0.03950865,0.052394,-0.00743463],"last_embed":{"hash":"1nur7n0","tokens":377}}},"text":null,"length":0,"last_read":{"hash":"1nur7n0","at":1753423498250},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{7}","lines":[67,86],"size":1214,"outlinks":[{"title":"The pick 4 lottery strategy based on frequency, stats has a good winning rate.","target":"https://saliu.com/images/lottery-strategy-stats.gif","line":3},{"title":"Every lottery strategy has a pivot: Main filter or restriction or condition.","target":"https://saliu.com/images/lottery-strategy-pivot.gif","line":7},{"title":"A winning lottery strategy always applies the reverse as in the case of lotto decades.","target":"https://saliu.com/images/lottery-strategy-decades.gif","line":11},{"title":"The winning lottery strategy always applies the reverse as in the case of number skips.","target":"https://saliu.com/images/lottery-strategy-skips.gif","line":13},{"title":"This winning lottery strategy also applies the odd, even, low, high lottery numbers.","target":"https://saliu.com/images/lottery-strategy-odd-even.gif","line":15},{"title":"Combining more lottery strategies and playing the aggregated result increase the profits.","target":"https://saliu.com/HLINE.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0850226,-0.03739718,-0.04377092,-0.03204258,-0.02194355,0.0941423,-0.00869012,-0.01072961,0.07063793,-0.00238826,0.02625624,0.00219987,0.03576023,-0.00720364,0.02789232,0.00852071,0.01293057,-0.02189408,-0.00731125,-0.02160434,0.09405634,-0.02370987,-0.01841121,-0.05253191,0.07021955,-0.00859351,-0.04831228,-0.10511456,-0.03823385,-0.23175834,0.05741221,0.01754549,0.01974439,-0.07535177,-0.09321388,-0.06348129,-0.05299234,0.09217741,-0.0342712,0.03770775,-0.01806303,0.02105407,0.03260739,-0.01895425,-0.0509572,0.00263729,-0.03049842,0.00590622,0.03560685,-0.01823216,-0.06701637,-0.00277912,-0.03645467,0.00246343,0.04886846,0.02478071,0.0395437,0.04190938,0.01922412,0.06914224,0.00561076,0.05316726,-0.20526391,0.05789988,-0.02659171,0.00985584,-0.01535983,0.0115119,0.00747639,0.082539,-0.01150754,0.0149741,-0.01826017,0.0452176,0.02964719,-0.04118378,-0.06595725,-0.03118354,-0.01210226,0.04203688,-0.03077816,-0.02015303,-0.00025785,-0.01596739,0.04078452,0.01910403,0.01426683,0.02311973,0.07625993,-0.042468,0.04100271,0.07298414,0.01472934,0.00935,0.00389999,0.01171436,0.0053579,-0.03468618,0.00865793,0.11290953,-0.00963974,-0.04560596,-0.03165591,0.04227829,0.04419651,-0.02738778,-0.04378304,-0.03658604,-0.02829284,0.01664898,0.05565294,-0.00008833,0.04332577,-0.08504686,-0.03552496,0.00671418,-0.00543783,-0.0145678,0.06669852,-0.02739031,-0.0271262,-0.02530649,-0.00469904,0.00765611,0.03966181,0.02485786,0.05050721,0.05442064,0.01552705,0.01595139,0.02729953,-0.01694599,-0.15216844,-0.03821525,-0.06583188,-0.00125202,-0.01110314,-0.02853135,-0.02954582,0.02091487,0.00187418,-0.03162614,0.0798791,-0.11337877,0.00216144,0.10692301,-0.02467343,-0.00222224,-0.01424833,0.04437488,-0.02038315,0.0113447,-0.03172124,-0.06921648,-0.02937222,0.02833353,0.05276758,0.0606032,-0.0510145,-0.02750383,-0.06093866,-0.06050219,0.00621815,0.14502214,0.00183575,-0.0819226,-0.02354577,0.03258281,-0.0359161,-0.12794274,-0.03025659,-0.01379436,0.01081407,0.01762258,0.10624861,-0.00397612,-0.01101567,-0.05550443,-0.02177827,0.00041513,0.02051254,-0.0334142,-0.02551507,0.00422984,-0.04628194,-0.07507201,0.03494807,-0.02117522,0.04202556,0.01562727,-0.11388106,0.01578132,-0.03148507,0.00857151,-0.04630572,-0.02924127,-0.02927187,-0.01479211,0.07833202,-0.00286477,0.02017984,0.00330431,0.01696578,0.03299146,-0.01264579,0.05159325,0.00085889,-0.05042177,0.08085972,0.01630488,-0.00166931,0.0258201,0.0803407,0.0367615,-0.03215962,0.01434528,-0.01006939,-0.00940823,-0.02075371,0.02867256,0.01387457,0.0281831,-0.08030812,-0.18771432,-0.00964433,0.00087292,-0.01327143,0.01050377,0.02054032,0.01697613,-0.01036992,0.07982748,0.06474417,0.07713372,-0.0284679,-0.00904767,0.02840249,-0.01162849,-0.03643702,-0.06909096,-0.07408939,-0.01391433,0.02915626,-0.01426114,0.01132172,-0.03288251,-0.02493514,0.0599838,-0.05109169,0.1262702,-0.00388497,0.02978868,0.02096538,0.06330047,-0.00491044,-0.02569806,-0.02011237,0.02437428,0.00816579,0.0296753,0.00649395,-0.03625514,-0.00982426,-0.07071442,0.06710212,0.03226309,-0.11562619,-0.01497283,0.00239193,0.00812924,0.01698596,-0.00217287,0.0072591,0.03541986,0.00999577,0.00711537,0.05661703,0.08061409,-0.04753991,-0.09066288,0.02917455,-0.0278908,0.02486149,-0.03609279,-0.0304416,0.08228976,-0.01744463,0.01232132,0.03353773,0.00473937,0.00687559,0.01332816,0.00109825,-0.00682566,0.07859441,0.01942438,0.01563123,-0.00987151,0.00738438,0.07045877,-0.0556821,-0.0256591,-0.00033569,0.02767247,-0.06963458,0.02601034,0.0495093,0.04852299,-0.01366955,0.07091486,0.0541269,0.02684266,0.02980043,-0.02100214,0.00526305,0.02695145,0.0539997,-0.00402435,-0.01158428,-0.27559003,0.04376738,-0.04577411,0.05799117,-0.02813781,-0.04044088,0.02993317,-0.02608511,-0.04277527,-0.00107716,0.02234946,0.03124519,0.03228937,-0.02632456,-0.01288917,-0.00937054,0.04087746,-0.03620131,0.07375613,0.0374364,0.07370611,0.05635809,0.25387728,-0.03267615,0.02224575,0.0306682,0.02982851,0.0374636,-0.01906854,0.02348883,0.0208317,-0.02405488,0.05723123,-0.01290685,-0.05777132,0.02854745,0.03369217,0.00099399,-0.03479882,0.0047457,-0.05313436,-0.01713154,-0.01428362,0.01461564,0.16297436,-0.00227438,-0.0234243,-0.06840156,0.00214334,0.06374926,-0.08057735,-0.04764652,-0.04132894,-0.0159053,0.03314368,0.03497772,0.01961156,-0.0437452,0.00402734,-0.01270994,0.03972706,-0.01466639,0.04024432,0.04132796,-0.0160269],"last_embed":{"hash":"7fljcs","tokens":176}}},"text":null,"length":0,"last_read":{"hash":"7fljcs","at":1753423498372},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{8}","lines":[87,87],"size":424,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08448904,-0.05614175,-0.03188583,-0.01168472,-0.01818346,0.06676044,-0.00529757,-0.01115468,0.09667648,-0.00307072,0.02670965,-0.0025509,0.05198111,-0.02588136,0.02235747,0.01205022,0.00433874,-0.04701998,-0.05455654,-0.01424891,0.0638243,-0.0412843,-0.0169706,-0.05348449,0.0717334,-0.01039362,-0.04977912,-0.09123184,-0.04116339,-0.22457729,0.04514118,0.02175671,-0.00351706,-0.10371383,-0.04199694,-0.06900703,-0.04726005,0.11068564,-0.04822692,0.05840357,-0.01388374,0.02173249,-0.00953395,-0.04159702,-0.04416871,-0.01927732,-0.05416146,0.01536476,0.02274644,-0.01290391,-0.04798514,-0.00062316,-0.03295415,0.01915,0.04006639,0.01459752,0.03668835,0.08088651,-0.00052106,0.04791052,0.05558725,0.04650606,-0.19810708,0.04790414,-0.01584656,-0.00944093,0.01005543,0.00000525,-0.00772289,0.1052215,-0.0193222,0.01539721,0.0012679,0.04062996,0.05434064,-0.02989123,-0.06534245,-0.02855644,-0.03066749,0.05880609,-0.04383882,-0.03854354,-0.01749387,-0.02156735,0.03485433,0.01164779,0.03225856,0.03702372,0.03951095,-0.0512513,0.04735161,0.06839223,-0.00738924,-0.00293004,0.0188507,0.01590701,0.04754904,0.00795777,0.0066544,0.11951387,0.00969268,-0.02665112,-0.0049155,0.01571336,0.03483801,-0.02069842,-0.05175994,-0.03398815,-0.03844531,0.00718036,0.08117525,0.01319932,0.0544952,-0.0859751,-0.03232083,0.03758627,-0.0144784,-0.0140336,0.05882198,0.00259489,-0.01918891,-0.02610186,-0.01044872,-0.00155106,0.03721325,0.03183574,0.03215728,0.0806346,0.03190787,0.01439315,0.04245924,0.00081661,-0.15715073,-0.01794468,-0.07094867,-0.0136045,0.00973416,-0.01890333,-0.01217188,0.05782663,0.00223478,-0.02566386,0.05970854,-0.13764144,0.02036107,0.14074555,-0.01225958,0.00745403,0.01630059,0.01130521,-0.01534284,0.01084258,-0.03516943,-0.09297388,-0.03375405,0.00941662,0.07726542,0.03905636,-0.04543293,-0.02761658,-0.0342837,-0.00928977,-0.01215456,0.13319702,0.02607892,-0.08231491,-0.03628561,0.0361583,-0.03838264,-0.1104551,-0.04252998,0.00840248,0.018039,0.01549319,0.09173384,-0.01829555,-0.0555191,-0.05181446,0.01271639,-0.00074102,0.01398452,-0.01580176,-0.03417655,0.02690081,-0.02980733,-0.08788586,0.01136805,-0.01926125,0.03750565,0.06959417,-0.09561305,0.02981769,-0.0272152,0.02010039,-0.05169031,-0.0395081,-0.03449558,-0.01702941,0.07806366,-0.01688812,-0.00969586,-0.02700578,0.02199424,0.0281026,0.01336362,0.05408153,-0.00367469,-0.02829951,0.08613436,0.01006972,0.00310166,0.02141908,0.03051099,0.04282673,-0.05499477,0.02299291,-0.00544467,0.04321374,0.01259681,0.01769641,0.00829843,0.00723073,-0.06507873,-0.19387616,-0.02308753,0.01257425,-0.00556229,0.00805763,0.00753969,0.01159401,0.02761668,0.05722973,0.06414748,0.04635753,-0.0283857,-0.02101364,0.03593656,-0.02362837,-0.0528636,-0.07024528,-0.02704341,-0.0147734,0.05784809,-0.01047012,0.00378922,-0.02924848,-0.05765579,0.08050827,-0.05080723,0.10726475,-0.01787011,0.0148778,0.00232154,0.04959243,0.02571383,-0.03228739,-0.00695812,0.03300736,0.02964067,-0.01823067,-0.00763308,-0.06625428,0.00872342,-0.06530073,0.02749424,0.01175437,-0.10459868,-0.0106473,-0.00281272,-0.02961342,0.0366513,-0.00714311,0.03394374,0.05331199,0.00637879,0.02722954,0.02936226,0.080204,-0.03197137,-0.09748331,0.03455535,-0.00472858,0.02596686,-0.00630932,-0.02677586,0.07193125,-0.02519518,0.04752319,0.04617272,0.00008696,0.00367617,-0.00002347,-0.0005365,0.0107343,0.11731927,0.00951843,-0.01070379,-0.02297081,0.0107414,0.05803062,-0.05141954,-0.04133307,0.00500947,0.02409282,-0.05362285,0.00565665,0.05258626,0.03962228,-0.01961008,0.06196243,0.07913481,0.01548561,0.02726343,0.00806133,0.03168274,-0.00128194,0.05126938,-0.03550076,0.02158048,-0.26531073,0.02397941,-0.05732775,0.04885995,-0.01620201,0.00366284,0.01888229,0.01340694,-0.00040341,0.00166563,0.02207446,0.03867353,-0.00727986,-0.07379938,0.01758522,0.00711464,0.00094541,-0.04257191,0.04688029,0.03505117,0.06034566,0.03682029,0.24043372,-0.01014658,0.01685629,0.0221577,0.03699908,0.06888027,-0.00645295,0.02735366,-0.00081669,-0.01890572,0.06163438,-0.01554806,-0.08138292,0.02616515,0.02110986,0.00229827,-0.01838052,-0.01419056,-0.06116092,-0.01799427,-0.01429995,-0.0032034,0.15408489,-0.00877937,-0.02749172,-0.09682269,-0.0184577,0.05671658,-0.09490889,-0.06362178,-0.0270838,-0.03082818,0.05640094,0.04284708,-0.01340641,-0.00116107,0.01943728,-0.00818639,-0.00007339,-0.01109917,0.0039034,0.01774237,-0.02312239],"last_embed":{"hash":"1nwcw1f","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"1nwcw1f","at":1753423498420},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{9}","lines":[88,88],"size":253,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07762481,-0.03645277,-0.01978136,-0.01472007,-0.0131741,0.07134806,-0.00933567,0.00252285,0.09581245,0.02002406,0.01898284,0.01029228,0.05136222,-0.00894547,0.00455306,-0.01905212,0.02140872,-0.05063248,0.0019241,0.00727197,0.07297026,-0.01833227,-0.04257867,-0.06010851,0.05961919,0.00225421,-0.0419751,-0.10611855,-0.09130105,-0.22525045,0.02859181,0.00989129,-0.00820588,-0.07979669,-0.07272129,-0.06614987,-0.03531317,0.10277883,-0.04766557,0.06371224,0.0090523,0.00421077,-0.00218317,-0.04118148,-0.01262477,-0.02500629,-0.04652774,0.02477726,0.03557608,-0.01290921,-0.06671467,0.01854371,-0.01426938,0.0104669,0.02419554,0.00257283,0.03844799,0.05982591,0.03791624,0.04104265,0.05310494,0.00615463,-0.17407276,0.03134733,-0.03141332,0.02112522,-0.02005299,-0.02145039,0.01464187,0.08460501,-0.03769619,0.02163135,-0.01762692,0.05656106,0.03649109,-0.05447185,-0.0323586,-0.00850413,-0.03687426,0.0337399,-0.02027607,-0.03425058,-0.03344134,-0.01193802,0.04144923,0.01209499,-0.00157965,0.03036945,0.04084597,-0.04700872,0.05700582,0.04869374,0.02260648,-0.01053634,0.00847121,0.03647498,0.05074222,-0.0356945,-0.02045425,0.11022755,0.03465637,-0.00081788,-0.00034244,0.05295878,0.05020085,-0.06623193,-0.03211077,-0.06222612,-0.0440918,0.00016876,0.04683378,0.01022242,0.09111656,-0.03151468,-0.01120381,0.01523439,0.0067403,0.02837078,0.01173331,0.0011273,-0.01337743,-0.00276966,-0.01782978,0.0148265,0.04587302,0.0390351,0.02633582,0.06826129,0.02617492,0.02594068,0.01879019,-0.03405916,-0.15058559,-0.0497941,-0.03673969,0.01020613,-0.04093588,0.03562486,-0.0050629,0.04339637,0.00599931,-0.02364399,0.0488338,-0.10730205,-0.02565845,0.09660294,-0.04097021,0.00579729,-0.00281366,0.00665783,0.00902144,0.00041342,-0.04386705,-0.0977212,-0.0583584,0.01498596,0.04710918,0.09037642,-0.05336227,0.00100629,-0.03882387,-0.03223853,-0.01554389,0.15697421,0.00723398,-0.0509201,-0.01263776,0.03149473,-0.0180945,-0.09297976,-0.04684184,-0.01346389,-0.06003755,-0.01577737,0.09558662,-0.02923741,-0.02246076,-0.04827251,0.00408707,-0.00545457,0.02325425,-0.0151718,-0.01165433,-0.00105732,-0.01561648,-0.07874332,0.00383135,-0.03907818,0.02460256,0.0199417,-0.08976407,-0.01549871,-0.03041294,0.01196943,-0.04168022,-0.04519282,-0.02263171,-0.00455757,0.04894862,-0.00203417,0.02632324,-0.00481677,0.04672982,0.02760075,-0.00510223,0.08155761,0.02724671,-0.06030678,0.1218073,0.00993963,-0.01664997,-0.02824955,0.06111726,0.02411903,-0.01807921,0.02246989,-0.02398515,0.00898814,-0.02208368,0.01110293,-0.02182246,0.07522344,-0.07701063,-0.2209492,-0.03469694,0.00661032,-0.01194646,0.01022632,-0.01029856,0.01671216,0.01387397,0.07549287,0.12714757,0.0694515,-0.06063564,-0.0172443,0.04029319,-0.00605148,-0.02511062,-0.1047745,-0.03875299,-0.01538995,0.06618888,-0.00093519,0.01780211,-0.03035712,-0.08524444,0.03367065,-0.03756736,0.13561486,-0.00349199,0.03621918,-0.03335511,0.06380972,0.01458978,-0.05044734,0.01479181,0.02240367,0.02693117,-0.01336423,0.00291355,-0.05202877,-0.01354535,-0.06543569,0.04033032,0.00730664,-0.10148289,-0.01786374,-0.02149874,-0.02388324,0.01410972,0.00763909,0.05877713,0.04096842,-0.01320992,0.01637288,0.02752628,0.11650246,-0.02095396,-0.09121389,0.02078226,-0.03984889,0.04449931,-0.00320531,-0.04778494,0.05544462,-0.03976686,0.00574941,0.04355713,-0.02143656,0.00147704,0.02449966,0.0251226,0.01782539,0.12862805,0.00872327,-0.0410533,-0.01574392,0.03905375,0.04109918,-0.02917489,-0.03920667,-0.00486113,0.05918581,-0.0152411,0.01601669,0.02830198,0.0155242,-0.01433231,0.06831785,0.06883752,-0.01042401,0.00452482,-0.00988725,0.03396038,-0.02307414,0.0482014,0.02006944,0.00334889,-0.25279781,0.02319728,-0.05556649,0.0236692,-0.01932266,-0.02478542,0.02336303,0.0162183,-0.00541243,-0.01980417,0.0167168,0.03924174,0.01255389,-0.04738771,-0.01686637,0.01853984,0.01510845,-0.03975392,0.07454173,0.01200415,0.05568196,0.03282212,0.24610484,-0.01148058,0.00529595,0.02751674,0.03781501,0.04338685,-0.01921552,0.03179618,-0.00924979,-0.01058176,0.08272854,0.03065195,-0.05117498,0.00999672,-0.01774088,0.01757946,0.01288626,-0.00759848,-0.05261086,-0.01861811,-0.02810785,0.01234789,0.13037361,-0.01459891,-0.02605242,-0.07024506,0.06562932,0.09569856,-0.07749155,-0.02968325,-0.04305985,-0.05565495,0.04235819,0.04122153,-0.02082552,0.00352712,0.0170635,-0.01563567,0.01588822,-0.02320405,0.03930625,0.02296487,-0.01049657],"last_embed":{"hash":"1kq82cl","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"1kq82cl","at":1753423498453},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{11}","lines":[90,90],"size":494,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07310662,-0.03817201,-0.06143869,-0.00089311,-0.02502387,0.07008424,-0.0189422,-0.02115245,0.03418664,0.00169831,0.01849744,0.00303155,0.07090691,-0.01347821,-0.00933866,-0.02078975,0.00652675,-0.01803359,-0.04439446,-0.00937749,0.08543827,-0.01601592,-0.02235292,-0.04342074,0.05648535,0.00224071,-0.02207753,-0.10534491,-0.06585761,-0.24665441,-0.00736391,0.02087008,0.00473138,-0.10122843,-0.0538931,-0.03995913,-0.0305203,0.10527129,-0.04830185,0.0781855,0.00019749,-0.01159201,0.02063535,-0.05456087,-0.02870812,-0.00941209,-0.04699248,0.01036944,0.02249143,-0.00723334,-0.04751141,-0.01688075,-0.00122198,0.0546054,0.01413221,0.02298313,0.05331323,0.06241851,0.03687974,0.06277521,0.04549873,0.005146,-0.18839751,0.03323051,-0.05709938,0.04347061,-0.01092277,-0.01459931,0.0282927,0.09848254,-0.01348451,0.04201859,-0.04389877,0.03683864,0.02782528,-0.02108398,-0.03495516,-0.06438453,-0.04383247,0.039646,-0.03180469,-0.01152376,-0.02165315,-0.01171982,0.03392918,0.00794121,0.00861083,0.02791774,0.02906338,-0.03614828,0.03848206,0.05200696,0.01415808,0.0113444,0.00906205,0.00446896,0.05988784,-0.00311789,0.01071488,0.11387596,0.03532457,0.00595569,0.0327149,0.01210967,0.04546528,-0.06145271,-0.05931151,-0.05482514,-0.0201684,0.02809784,0.06713708,-0.00361242,0.05663553,-0.06075901,-0.02062248,0.01800674,0.01833932,-0.00074554,0.02573283,0.00004488,-0.05091041,-0.03468232,0.01127377,0.01373287,0.04292206,0.04933659,0.02047721,0.07653993,0.04944366,0.02648464,-0.00179323,-0.00139835,-0.15666789,-0.03962734,-0.05232854,-0.00560968,0.00143629,0.01118308,-0.00330822,0.00866247,-0.01248759,-0.0318797,0.05604817,-0.08994128,-0.01823724,0.11381727,-0.03435523,-0.00340345,-0.02211409,0.00846488,-0.01502141,0.04407977,-0.06275881,-0.06015822,-0.02974139,0.00122861,0.05753694,0.05093071,-0.03503629,0.02100717,-0.01178717,-0.02222037,-0.04086758,0.1512706,0.00547361,-0.05523773,0.01003408,0.03898183,-0.02158315,-0.08253806,-0.05405286,0.02302658,0.00508477,0.00453305,0.07977217,-0.0123008,-0.05042529,-0.04252448,-0.0243772,-0.02663997,-0.00143181,0.01032491,-0.05122458,0.02366733,-0.03329805,-0.07283829,0.0460394,-0.03214123,0.05590766,0.04436503,-0.07799175,-0.01491999,-0.03191872,0.04129592,-0.01306856,-0.03032325,-0.02038077,-0.00271725,0.05080187,-0.03072499,-0.00439806,-0.02860004,0.02048128,0.01310951,-0.01775844,0.07667828,0.02201059,-0.05193274,0.14172424,0.00419301,-0.0372664,-0.00002595,0.05555824,0.02917181,0.00306038,0.00045985,-0.02576166,0.02503505,-0.0602092,-0.00036289,-0.00003975,0.03426928,-0.07808898,-0.21126905,-0.03698634,0.02342482,0.02724157,0.01143961,-0.02440405,0.01061614,0.01931014,0.05320795,0.15993072,0.07780299,0.00846591,-0.02606571,0.04528707,-0.01080761,-0.00517962,-0.07682093,-0.04769345,-0.05037022,0.07656305,0.00425719,0.00090464,-0.0290355,-0.06423066,0.05586781,-0.06210543,0.11530188,0.00649805,-0.01222467,-0.0044411,0.05801318,0.00360993,-0.03807427,0.01714203,0.05901311,0.05224355,-0.03796767,-0.01915551,-0.03825444,-0.02770967,-0.04968742,0.00751399,-0.0135682,-0.11084712,-0.01362598,0.0139021,-0.05341286,0.0317016,-0.00469277,0.01297085,0.02687582,-0.03182647,0.01543621,0.03121537,0.09306419,-0.02308252,-0.08638404,0.00006087,-0.0281691,0.01169471,-0.02046378,-0.00560013,0.0586206,-0.04155713,0.01401009,0.0243808,-0.01685046,-0.01102659,0.02834929,0.00865907,0.00974633,0.12280174,0.00215396,-0.00809965,-0.02899767,0.03682084,0.01542319,-0.08503853,-0.01593995,-0.03266207,0.049968,-0.05329577,0.02171973,0.04744029,0.03996517,0.02302312,0.03281328,0.04512798,0.0120007,0.01508181,-0.00566664,0.00652226,-0.01534565,0.0347668,0.00714371,-0.00323985,-0.24513631,0.02849077,-0.05735109,0.03972195,-0.00484708,-0.04331242,0.02077061,-0.0050053,0.02867605,-0.03977786,0.01638977,0.04798722,0.03149296,-0.06409787,-0.01631542,0.00542113,-0.00214419,-0.04742328,0.08303127,0.0479721,0.06197148,0.03754987,0.25608772,0.01704635,0.02421118,0.02392796,0.0274969,0.01047611,-0.05679504,0.03168981,0.01614691,-0.0202701,0.09424403,0.00652116,-0.05245732,0.02503666,0.00264407,0.01373615,-0.02807277,0.00216945,-0.06375376,-0.01260965,-0.01076648,-0.00749525,0.14244522,-0.00204267,-0.02585096,-0.0742543,0.03704553,0.08335926,-0.06984795,-0.03145465,-0.03630318,-0.04862774,0.03983545,0.05487806,0.01194993,0.00075024,0.01482542,-0.0066553,0.0018296,0.00322894,0.04499941,0.01640042,0.00657047],"last_embed":{"hash":"13sf6wq","tokens":184}}},"text":null,"length":0,"last_read":{"hash":"13sf6wq","at":1753423498503},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{12}","lines":[91,91],"size":542,"outlinks":[{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0890721,-0.07889169,-0.04223444,-0.00032499,-0.02014334,0.05017026,0.02634929,0.00786781,0.07715204,0.00639984,0.00181608,-0.01467469,0.03810443,0.00373653,0.00010461,-0.00242274,-0.00179371,-0.03022964,-0.02223842,0.00245755,0.09173784,-0.02568142,-0.00305995,-0.05821707,0.08465776,-0.00731142,-0.04365969,-0.10650886,-0.05093496,-0.24634428,0.01536998,0.01740137,0.02164304,-0.10591363,-0.04503778,-0.0508415,-0.01881983,0.05265599,-0.02728959,0.05123663,0.01053487,0.02104986,0.02469962,-0.04507536,-0.0248184,-0.01433205,-0.03555794,0.02484095,-0.02451034,-0.01830937,-0.05442269,0.02294166,-0.00184553,0.05968662,0.00858164,0.03690551,0.05662017,0.05916817,0.03735241,0.07358097,0.03837913,0.03059441,-0.19240913,0.03424134,-0.02445053,-0.00068929,0.00007396,-0.04996037,0.01113485,0.05874344,-0.01825023,0.03847784,-0.01109834,0.06532824,0.03402943,-0.04224145,0.01630859,-0.04610904,-0.04124495,0.06456561,-0.02063309,-0.03350257,-0.04409862,-0.04526009,0.01914755,0.01043467,0.03137898,0.02565885,0.02701537,-0.06570859,0.0562127,0.08419207,0.01185551,0.00802595,0.02978648,0.01905179,0.04259345,0.00689468,-0.03918792,0.09708304,-0.00096962,-0.03219271,-0.01359703,0.03784722,0.05602852,-0.04930237,-0.01753855,-0.06159449,-0.00589343,-0.01103483,0.0514123,0.02167167,0.0775635,-0.0401866,0.00484448,0.04721169,0.0289329,-0.00621556,0.03670609,-0.02103676,-0.02777017,0.00589314,0.0057414,-0.00436178,0.02413282,-0.01427433,0.01481593,0.07144057,0.00561561,-0.00447943,0.03021082,-0.03942024,-0.10723864,-0.04602776,-0.04545246,0.00798474,-0.00632469,0.03404185,0.02742111,0.04504684,-0.00532955,-0.07268508,0.02021216,-0.11224113,-0.04258914,0.09896794,-0.00238221,-0.00015231,0.02865785,0.01580709,-0.01870922,0.04941765,-0.05328992,-0.07308304,-0.01160751,-0.00035841,0.06518918,0.04717547,-0.05366202,-0.02372776,-0.02792471,-0.0165535,-0.02134332,0.18976951,-0.02986249,-0.04886813,-0.00247982,0.03151408,-0.02303023,-0.11343753,-0.04129273,-0.01210127,0.00518729,0.01085653,0.09093712,-0.02545694,-0.01706237,-0.02291048,0.01155883,-0.00403558,-0.00107924,-0.03703772,0.00023387,0.04065953,-0.01888757,-0.06585552,0.00513853,-0.02059891,0.02938627,0.0102464,-0.0954969,-0.00193758,-0.04889606,0.02992409,-0.05712117,-0.02838569,-0.01772845,-0.03539117,0.04135751,-0.00088081,-0.03538688,-0.0240345,0.03648272,0.02892028,-0.00812278,0.0992699,0.02116385,-0.04356567,0.06655799,0.03544867,-0.00680889,-0.00133804,0.01297115,0.00950173,-0.01654033,0.01961768,-0.00380207,0.04786997,-0.03834379,0.00432558,0.00505663,0.04602892,-0.05955377,-0.23348984,-0.03767487,-0.03794139,-0.0152518,0.02387438,0.01666805,0.03107621,-0.01966475,0.03552935,0.06705655,0.04863914,-0.01289446,-0.04458233,0.01495716,-0.01491166,-0.06431788,-0.10641022,-0.04462525,-0.07561154,0.05049499,-0.03468192,0.02763436,-0.08551463,-0.07826595,0.03857799,-0.02247714,0.13383214,0.01737,0.04087577,-0.01009233,0.05374722,-0.01145906,-0.02453452,0.00141651,0.04617281,0.03345571,-0.03292584,-0.01667828,-0.03975099,-0.00378075,-0.04028617,0.01745831,-0.02094101,-0.08273219,-0.04583435,-0.03003926,-0.02464529,0.09495002,-0.00828267,0.07331334,0.06068232,-0.00225445,0.02540596,0.04309707,0.07394715,-0.0118245,-0.08460205,0.01271483,-0.02733698,0.05671974,-0.01368472,-0.00968314,0.09337894,-0.01467481,0.02587914,0.05986303,-0.02589138,-0.010317,0.02620737,0.00636109,0.00969439,0.15306284,0.01345182,-0.0453191,0.03415118,0.0189396,0.03731933,-0.04163525,-0.02868096,0.00321897,0.04162264,-0.03826306,0.05196799,0.04924623,0.03494959,-0.00821723,0.04866578,0.07041111,-0.01356518,0.00217058,-0.00907108,0.01896184,-0.03318288,0.0046382,0.02241656,0.00318838,-0.23908737,0.02828705,-0.06779102,0.04534652,-0.02315456,-0.03087872,0.05856757,0.01465264,-0.03534357,-0.00887906,-0.01755314,0.04179406,0.01958569,-0.03192553,-0.00394622,-0.01007151,0.00907946,-0.06595485,0.04905543,0.04449699,0.10548929,0.06472431,0.22854197,0.01803795,0.00039144,0.04452813,0.05373298,0.05039933,-0.02226753,0.01221599,-0.00711552,-0.00466948,0.05160545,-0.01843586,-0.01265553,-0.01601515,0.00711818,0.01350323,-0.00596143,-0.02882626,-0.03476195,-0.02152736,0.04419598,0.00623656,0.15718421,-0.01416308,-0.00467597,-0.07605395,0.04032148,0.05699991,-0.07229114,-0.05641706,-0.01647106,-0.0405236,0.01872797,0.04409086,-0.00543824,0.00796306,0.01423477,-0.06157365,0.01026683,0.03217234,0.02646316,0.03490943,-0.0307372],"last_embed":{"hash":"zief5","tokens":191}}},"text":null,"length":0,"last_read":{"hash":"zief5","at":1753423498555},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{13}","lines":[92,92],"size":517,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08325137,-0.04483568,-0.0467123,-0.02660167,-0.03024382,0.09904097,0.00951197,-0.00302251,0.08515545,0.00815399,0.02510695,-0.00876707,0.03090194,-0.02468948,-0.0064364,0.0167314,0.00222462,-0.08211448,-0.0428442,0.00629275,0.09204052,-0.04087824,-0.03203595,-0.06535941,0.0737243,-0.00818216,-0.03046957,-0.10387894,-0.05317483,-0.22241795,0.0364566,0.04351379,0.02740853,-0.09242467,-0.05902921,-0.0505421,-0.03730621,0.09822525,-0.02688541,0.05231313,-0.01395259,0.00589145,0.02275668,-0.04490084,-0.04794129,-0.03269694,-0.0589566,0.00891046,0.05205591,-0.03211223,-0.05441432,-0.00854897,-0.01753241,0.02634193,0.05915897,0.0282948,0.02965432,0.04878371,0.03435146,0.04290786,0.05236023,0.01199653,-0.20023495,0.03160276,-0.05766465,0.01549307,-0.01656869,-0.02515422,0.01137408,0.06513354,-0.01344109,0.01762374,-0.01837535,0.03812709,0.03742319,-0.02292094,-0.03868722,-0.03641005,-0.04456934,0.02188671,-0.05108668,-0.00652302,-0.00827071,-0.01434015,0.04066173,0.0358084,0.04062868,0.02741508,0.06374884,-0.03683094,0.04455823,0.07152101,-0.01222498,0.00510853,0.00263463,0.02350103,0.0645957,-0.03135036,0.00202172,0.11940864,0.01706994,-0.01816275,-0.04958382,0.06277512,0.02138492,-0.04542936,-0.05791506,-0.05475812,-0.06747191,0.02938068,0.07313792,0.00716119,0.08944996,-0.07315636,-0.02791998,0.0079749,0.00779233,-0.01242705,0.06531905,0.01920009,-0.00634743,-0.01829513,0.0043188,-0.0053602,0.03571265,0.07217958,0.04011264,0.08032336,0.03021797,0.00401012,0.03129035,0.00287335,-0.16376665,-0.03345244,-0.04854503,-0.00570737,0.00636814,-0.01808403,-0.02350086,0.02812788,-0.00026436,-0.03124728,0.05207759,-0.11667828,0.00081998,0.11485209,-0.00862214,0.0090809,0.03666701,0.01669412,-0.01621235,0.02596204,-0.02869349,-0.06813566,-0.05941494,0.03582318,0.0639341,0.07699654,-0.05007001,0.00213903,-0.02869554,-0.06232679,-0.01112958,0.11282306,-0.00907403,-0.08877067,-0.04104839,0.05363051,-0.02870371,-0.1000016,-0.02817843,0.01885097,-0.00445226,-0.00886956,0.09982678,-0.00661254,-0.01478759,-0.0602007,0.02645076,-0.0174097,0.0259579,-0.01695215,-0.03916244,0.04918443,-0.02774072,-0.0655657,0.03982209,-0.04093168,0.03695957,0.05528759,-0.07456828,0.00793974,-0.04140493,0.03258305,-0.05560188,-0.04738055,-0.0398165,-0.01681213,0.0734847,0.00079577,-0.01558351,-0.01793193,0.05387132,0.01406589,-0.00496753,0.04909262,0.00735217,-0.04526729,0.07886384,0.00789909,-0.01568861,0.01698218,0.04749159,0.02386991,-0.03778221,0.00446692,-0.01527517,0.02386394,-0.01578754,0.00001566,0.02269343,0.01350045,-0.07176756,-0.17607355,-0.03974222,0.00135206,0.00532976,0.0091204,0.01618689,0.03811541,0.00514917,0.06441942,0.09024411,0.07245897,-0.00538919,-0.02754092,0.02072862,-0.03931518,-0.01909741,-0.07699656,-0.0616715,-0.04504955,0.04788624,-0.00326578,0.00379723,-0.07739341,-0.0427337,0.06885391,-0.06418072,0.11886474,-0.0174238,0.00855427,-0.01891794,0.06069147,0.00688919,-0.04250038,-0.00801542,0.03334772,0.02038285,-0.03523437,0.00957,-0.07547819,-0.01059728,-0.04652102,0.01343525,0.0009953,-0.11427163,-0.0141574,-0.01164301,-0.02683852,0.03213245,0.00083626,0.02336193,0.04387944,-0.01212481,-0.00152086,0.04371889,0.07064489,-0.01371467,-0.08746792,0.05455539,-0.02802053,0.04293008,0.00257724,-0.02830755,0.07471633,0.0028316,-0.00999061,0.03472518,-0.00685214,-0.0146034,-0.00684802,-0.00809341,0.03440769,0.1087447,0.03807762,-0.02628553,-0.0145728,-0.00312006,0.06497164,-0.05634944,-0.02310417,-0.0081051,0.03828116,-0.07386201,0.00522577,0.03908768,0.05813785,0.02427324,0.07646497,0.06884396,-0.00947731,0.02113411,0.01771583,0.02887172,-0.00700211,0.00202439,-0.00915937,0.011662,-0.24295914,0.05616275,-0.04397914,0.06435303,0.02582808,-0.01719221,0.01270908,0.00727355,0.00141407,-0.01347561,0.03234248,0.03610442,0.01874611,-0.06469295,-0.00431111,0.00833607,0.00713532,-0.05493439,0.05615351,0.01946974,0.0616597,0.03984858,0.22690539,-0.00029112,0.0198346,0.02809627,0.04983244,0.03201536,-0.02075751,0.04183868,0.00066595,-0.03051837,0.09009509,0.0167137,-0.06817013,0.03998641,0.00788441,-0.00162758,-0.00505835,-0.00954665,-0.06666305,-0.03578579,-0.02862251,0.01768708,0.12972556,-0.00949102,-0.0491491,-0.08652902,0.0143302,0.08119226,-0.08390762,-0.0767775,-0.04078271,-0.00945506,0.03333606,0.05515246,0.00569748,0.00357274,0.01753959,-0.01551376,-0.00693694,-0.01926176,0.04368293,0.00974246,0.00180249],"last_embed":{"hash":"szl8wr","tokens":106}}},"text":null,"length":0,"last_read":{"hash":"szl8wr","at":1753423498607},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{14}","lines":[93,93],"size":290,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{17}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0687398,-0.04927132,-0.02826004,-0.00788766,-0.02428275,0.04668211,0.01049704,-0.00370473,0.06112677,0.00289244,0.02936064,-0.00111978,0.06345592,-0.03071222,0.00756549,-0.00890448,0.00861899,-0.06215689,-0.04383609,0.01335138,0.0865118,-0.02665168,-0.07386635,-0.04673257,0.0814639,-0.02683614,-0.04285119,-0.08771528,-0.03337296,-0.22077368,0.04361757,0.01904855,0.03509795,-0.0878535,-0.03996919,-0.03487251,-0.02113856,0.09979986,-0.06890155,0.05638592,0.01851697,0.00622935,0.02496543,-0.04006001,-0.04216091,-0.02993674,-0.01381221,-0.00688413,0.01290813,-0.00549567,-0.04065831,0.0081658,-0.02440165,0.05122607,0.03337449,0.03164288,0.01196775,0.08126364,-0.00503254,0.02849814,0.00862231,-0.01528334,-0.22001736,0.02376057,-0.05954022,0.02724429,0.01117947,-0.02242543,-0.01469971,0.09393595,0.01483307,0.01414238,-0.00286718,0.01083076,0.02074552,-0.01946774,-0.01865774,-0.04222135,-0.04154219,0.03910556,-0.04375637,-0.03638147,0.00387169,0.00335772,0.04430117,0.01951226,0.05887583,0.0335184,0.05139895,-0.03590227,0.05285086,0.08463207,-0.01767514,0.02389309,0.04278508,0.01803789,0.02171632,0.01326819,0.01211805,0.1386009,0.04187271,-0.01509714,-0.0471974,-0.02892246,0.0667114,-0.04069716,-0.04355448,-0.01375209,-0.04681658,0.01667156,0.03412756,0.04362164,0.09943031,-0.09684537,-0.03301321,0.04651492,-0.00477469,-0.02280723,0.03611558,0.00522129,-0.02782775,-0.01161758,-0.00560178,0.01594765,0.01250595,0.05251793,0.04304587,0.07755169,0.01729676,0.03429263,0.04668128,-0.03760704,-0.15519486,-0.03155948,-0.02968485,-0.00158452,0.00247127,-0.01199295,-0.01069561,0.05987596,-0.05362155,-0.02633899,0.0065606,-0.11434666,-0.01041258,0.12882307,0.00822965,-0.00479239,-0.00050064,-0.00955478,-0.02773557,0.03238411,-0.01714154,-0.10150343,-0.04666379,0.01938146,0.08082455,0.05832052,-0.04234109,-0.02766588,-0.04766701,-0.0210072,-0.01981609,0.12832069,0.01949431,-0.05841126,-0.03288254,0.01478622,-0.03083603,-0.08679984,-0.04050704,0.01804037,-0.01306232,0.00626221,0.09839248,-0.02245753,-0.02461076,-0.04258997,-0.01107995,-0.04126661,0.01350523,-0.02515745,-0.03273315,0.0242771,-0.02809965,-0.05916172,0.03296323,-0.02069083,0.03523243,0.03056123,-0.08351939,0.00983978,-0.05083651,0.05919931,-0.04553251,-0.05397855,-0.01471367,0.01230863,0.04369041,-0.01144337,0.00119789,-0.00993201,0.03634484,0.02993521,-0.02024111,0.08597175,0.00325741,-0.04935252,0.10591147,-0.00164752,-0.0203721,0.03910312,0.05069826,0.00301452,-0.01273441,0.00238736,-0.01447486,0.00621809,-0.02372831,0.01169781,-0.02549532,0.0632641,-0.05063124,-0.18040426,-0.01707857,-0.01072959,0.00051625,0.02422736,0.00934956,-0.00911655,-0.01737172,0.05544995,0.06396797,0.08456084,-0.03277586,-0.02586516,0.02447286,-0.01513822,-0.01983442,-0.07527813,-0.04464709,-0.03321886,0.00993005,0.01491871,0.01343571,-0.02679254,-0.07477953,0.03683254,-0.04249709,0.11345153,-0.0290745,0.00608853,-0.01714805,0.06168121,-0.00687507,-0.04480628,-0.00827815,0.01957687,0.02503818,-0.02439881,-0.01588203,-0.07095242,-0.01086253,-0.03362864,0.01577362,0.00620931,-0.09683433,-0.00594128,0.04151772,-0.02862556,0.07951112,-0.0045137,0.01787763,0.04749131,-0.0104278,0.01782216,0.04989444,0.1074523,-0.0494284,-0.06397562,0.01144233,-0.0548887,0.02555291,-0.01191821,-0.01725629,0.06770056,-0.00916547,0.03823901,0.0221242,-0.00186853,-0.02836862,-0.00284521,0.00027207,0.00893722,0.11702143,-0.01172283,-0.03627827,-0.00448748,0.01053801,0.05331851,-0.06677467,-0.03021703,0.00904827,0.0400718,-0.07725623,0.03023875,0.07723753,0.06017415,0.01328801,0.0626442,0.06395757,-0.01650393,0.02109046,0.00292699,0.03267006,-0.03690932,0.03236721,0.0054158,0.02557426,-0.25217935,0.03178281,-0.05581016,0.03121759,0.02309212,-0.0241196,0.01824494,0.03868887,-0.0488403,-0.02012021,0.03699967,0.04586792,0.03176946,-0.04652167,0.02084562,-0.01805652,-0.01118384,-0.06727069,0.06146176,0.03685867,0.07329288,0.04352225,0.22507776,-0.00595222,0.0033782,0.01880603,0.04381428,0.05431593,0.00802865,0.0285308,0.02026159,-0.02885912,0.1159547,0.01347496,-0.05342723,0.00741272,-0.03949753,0.04562442,-0.03731805,-0.01116678,-0.06778134,-0.01026622,0.0193892,0.01021184,0.14917117,-0.01840536,-0.02640134,-0.11013364,0.00066526,0.07336221,-0.06989764,-0.07302126,-0.02275031,-0.02302657,0.03117135,0.03691448,-0.00413066,0.0073908,0.0505987,-0.03072047,-0.01377706,-0.01789048,0.01165092,0.05123901,-0.02494423],"last_embed":{"hash":"18eshas","tokens":103}}},"text":null,"length":0,"last_read":{"hash":"18eshas","at":1753423498641},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{17}","lines":[96,97],"size":313,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{18}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09157236,-0.04570908,-0.02769905,-0.00868249,-0.02832059,0.07362521,-0.02384435,-0.01683343,0.03913492,-0.04193584,0.017978,0.02950437,0.02242169,-0.00023112,0.0061805,-0.00667365,0.01521021,-0.00244518,-0.02924212,-0.00625243,0.10513536,-0.02829278,-0.01737594,-0.02130432,0.02452276,0.0148877,-0.03242078,-0.06501699,-0.04725169,-0.18125407,0.03527085,0.03485679,0.00005049,-0.06798699,-0.04691384,-0.07650857,-0.06215904,0.10466463,-0.07725107,0.09455656,0.00393672,0.01137731,0.03380153,0.00539591,-0.01825905,-0.02877634,-0.04680786,0.01116329,0.05198076,-0.04871126,-0.07522715,0.03772894,-0.01780252,0.01787807,0.05707267,0.01009247,0.05757326,0.0566996,0.04203564,0.06822,0.05200271,0.01394037,-0.18369137,0.01852771,-0.0192635,0.02142637,-0.03506911,-0.00031521,0.0330598,0.01670202,-0.03170205,0.03142387,-0.0287325,0.0498006,0.02989249,-0.03987232,-0.02634822,-0.01821669,-0.0506397,0.01738055,-0.02572543,-0.00791933,-0.0219216,-0.01744421,0.02011175,0.02709633,0.05817892,0.043784,0.03105621,-0.04595321,0.04049915,0.04905366,0.01066343,-0.01524332,0.01450086,0.01158651,0.02827756,0.01776526,0.01967835,0.11311832,0.013298,-0.0368532,-0.01794178,-0.00206613,0.01974199,-0.03101944,-0.02416702,-0.03555221,-0.03504895,0.07407808,0.04546779,0.01323592,0.04282779,-0.07604931,0.01923953,-0.01416601,-0.01106273,0.00230616,0.03337853,-0.00856068,-0.00294927,0.00808092,0.01630772,0.01792524,0.05177176,0.00631226,-0.00221077,0.07271971,0.04678378,0.0182926,0.0094927,0.0026458,-0.15007909,-0.02779133,-0.03509561,-0.03345959,0.00560528,0.02477448,-0.01584644,0.03126821,-0.03728462,-0.02417715,0.08280557,-0.10706802,-0.06183774,0.09234196,-0.00189047,0.02207748,0.00329622,0.0643331,0.02201975,0.03964749,-0.05250841,-0.07584458,-0.01224952,0.01696037,0.08786264,0.05348266,-0.01071791,0.00334709,-0.02844275,-0.05572493,0.0056615,0.13855654,-0.03722479,-0.12096264,-0.01967369,0.04356287,-0.02506795,-0.1050979,0.00885947,-0.0036436,-0.01765974,0.01687487,0.08529557,-0.00740394,-0.03460589,-0.08733994,0.02744475,0.01315977,0.00843072,-0.0359172,-0.03796024,0.02810701,-0.03661069,-0.06478985,0.01850801,-0.032196,0.06285035,0.00010927,-0.03443524,0.0101668,-0.03843286,0.03479055,-0.05551418,-0.01042177,-0.0425752,-0.01125355,0.07160316,-0.00948862,-0.01751572,-0.02917908,0.03393086,0.0460965,-0.0514778,0.06609856,0.00459646,-0.03538884,0.11478744,-0.01628879,-0.02333952,0.01014155,0.02563674,0.03503706,-0.07098999,0.01519829,-0.00364372,-0.00538381,-0.0172967,0.03367908,-0.0230723,-0.00232635,-0.06859089,-0.19817632,-0.02221273,0.00417531,-0.04441069,-0.00432403,0.03319399,0.01470479,0.01930911,0.04774308,0.10246236,0.08549783,-0.06652833,-0.03816429,0.03343365,-0.01260226,-0.03296943,-0.09174652,-0.04903422,-0.04610454,0.04902142,-0.00066443,-0.02158077,-0.06769975,-0.07493915,0.06392507,-0.02790153,0.10639139,-0.01745422,0.02340694,0.00289921,0.04617893,-0.00127736,0.00134468,-0.00250958,0.0167919,-0.02179706,-0.00876853,0.01764262,-0.08049402,0.01950991,-0.04822608,0.02733181,0.01825908,-0.09589071,0.00529992,-0.01822866,-0.02039237,0.0297847,0.01888967,0.03032572,0.05570479,0.00132123,0.03730298,0.02989729,0.02827212,-0.04558204,-0.08056354,0.01636906,-0.04275017,0.04023637,-0.02665234,-0.02039261,0.06100571,-0.02727203,0.02335994,0.06638429,-0.03231642,-0.0180578,0.02816839,-0.02469688,0.01773288,0.11909091,0.03937778,-0.0125906,-0.01488202,0.02148333,0.08036148,-0.05222715,-0.00811676,-0.02154714,0.00984714,0.0076239,0.01834576,0.05532345,0.01388109,-0.01410462,0.04764382,0.06834636,0.01581964,0.01524501,0.00951078,0.01768187,0.01654176,0.00618062,0.01688183,-0.00551046,-0.27311033,0.04053615,-0.04257268,0.05398566,-0.02827642,-0.02270565,0.0030814,0.02267963,0.0206324,-0.01887384,0.02237197,0.02658524,0.01564937,-0.07569823,0.01163618,-0.01627699,0.00367797,-0.0576527,0.1009398,-0.00231031,0.09228525,0.03097298,0.26501042,-0.01785302,-0.01405342,0.02875726,0.03178603,0.05430792,-0.05930449,0.04619395,0.03272267,-0.02674348,0.05718724,0.01188195,-0.05966091,0.02065879,-0.00445295,0.02844745,-0.03931344,0.0244738,-0.07913908,-0.04004743,-0.06338649,0.02713141,0.13995202,0.00355681,-0.04517139,-0.0875029,-0.01256519,0.08865999,-0.08051283,-0.05639717,-0.02730535,-0.05334893,0.05250941,0.06366987,0.03868138,-0.01086653,0.02051621,-0.04899656,0.03313259,-0.02837074,0.06010308,0.0137873,-0.00060836],"last_embed":{"hash":"14oqg2l","tokens":92}}},"text":null,"length":0,"last_read":{"hash":"14oqg2l","at":1753423498676},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{18}","lines":[98,99],"size":228,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{23}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07758104,-0.05360613,-0.05666121,-0.04539304,-0.04867027,0.06513832,0.00829178,-0.00527727,0.05038943,-0.00297829,0.02622055,-0.02061139,0.06815116,-0.01555001,-0.00505893,-0.01440622,0.01452313,-0.02432799,-0.02536569,-0.02448242,0.11225358,-0.01210545,-0.04137975,-0.06206,0.04844765,0.00654864,-0.02377832,-0.08303137,-0.05536912,-0.21059607,0.03742962,0.01805272,0.01843706,-0.08532712,-0.05334344,-0.05553181,-0.02898038,0.11403587,-0.06672394,0.06156676,-0.01709102,-0.00229621,-0.00392799,-0.02822858,-0.02901172,-0.02686144,-0.01355289,0.01351455,0.02806256,-0.03269634,-0.07348622,0.02093836,-0.02072909,0.04254971,0.03289016,0.0234089,0.0199606,0.0481839,0.02800988,0.05470434,0.04841845,0.02197904,-0.19524787,0.05745193,-0.05183129,0.01182429,-0.0010793,-0.0041567,0.01503301,0.06484478,0.0208498,0.05054872,-0.02823609,0.03863293,0.01751857,-0.03778673,-0.06534763,-0.04115687,-0.03834812,0.04829396,-0.04570322,-0.02633692,-0.00898595,-0.01846661,0.01895666,0.02318032,0.03982953,0.04188211,0.03103772,-0.04119393,0.03342619,0.08702502,-0.02164696,0.00134367,0.02636354,0.0177118,0.05688784,-0.02264392,0.014799,0.12860908,0.02434234,-0.00938649,-0.02853895,0.02799622,0.03422621,-0.05243112,-0.02670168,-0.05656259,-0.02184008,0.02415772,0.0765351,0.02674572,0.06428408,-0.08311492,-0.01320616,0.0053015,-0.0220082,-0.0179499,0.0632962,-0.00756715,-0.02067773,-0.01154576,-0.00339659,0.00637177,0.05522959,0.05624916,0.03732238,0.09385755,0.05281517,0.03111862,0.04217762,-0.0014798,-0.14710389,-0.03919878,-0.04245586,-0.00609097,0.00243205,-0.02861759,-0.01755446,0.04143057,-0.04983623,-0.03254543,0.04178427,-0.12139839,0.00267524,0.11918302,-0.00013899,-0.00548498,-0.01570326,0.02878772,0.0119385,0.01514977,-0.04864983,-0.09364128,-0.03559072,-0.00778462,0.06851484,0.07416949,-0.04083525,-0.01893513,-0.03013673,-0.03361945,-0.00329291,0.12408883,-0.0059586,-0.0748547,-0.03701092,0.01973503,-0.06368168,-0.10186636,-0.04616325,0.00753669,-0.03475884,0.00809174,0.09202557,-0.00118586,-0.03578609,-0.03994762,-0.01893853,-0.02692944,-0.01747585,0.00201222,-0.02840604,-0.01866982,-0.03876408,-0.05721578,0.04535268,-0.02607302,0.05957705,0.03545607,-0.05728202,-0.01819512,-0.02523342,0.04502846,-0.03006377,-0.02580627,-0.04838616,0.00558473,0.05968158,-0.02284435,-0.02149458,-0.01218459,0.01492892,0.04244996,-0.01861227,0.05987984,-0.01023567,-0.05086777,0.05679204,0.02405737,-0.01701722,0.0359004,0.04999815,0.02615634,-0.01938757,0.00295868,-0.02843012,-0.00474205,-0.00731435,0.03285976,0.0103844,0.05337534,-0.09891468,-0.19753994,-0.01933012,-0.00546259,0.01614177,0.01692746,0.00106284,0.01244549,-0.01941406,0.05693975,0.08995314,0.09648748,-0.03813403,-0.01283137,0.07438304,-0.00415089,-0.02145359,-0.08664141,-0.0462347,-0.02855255,0.04645022,0.01972434,0.01511393,-0.01490666,-0.04030629,0.06756995,-0.0407079,0.10256323,-0.0077402,0.0118256,-0.01201671,0.05236969,0.00179374,-0.03993814,0.00067041,0.03791791,0.01896822,-0.01361783,-0.00366398,-0.03932994,-0.01830238,-0.0290051,0.0291364,-0.00564478,-0.10840248,-0.01654924,-0.01067454,-0.03846974,0.07109758,-0.00402412,0.03047122,0.06140266,-0.02920106,0.04076866,0.04909785,0.051066,-0.03151388,-0.06811312,0.01180323,-0.03589246,0.02993855,-0.0322407,-0.02882102,0.0523917,-0.02930691,0.01812633,0.01943204,-0.00101732,-0.03214079,0.01273875,0.01073374,0.00518755,0.09981158,0.01076405,0.03157869,-0.00782173,0.02635337,0.03628304,-0.02504527,-0.01620199,-0.0142312,0.03523417,-0.04312132,0.00556963,0.04402056,0.04506174,-0.01955967,0.07981838,0.08368891,0.013104,0.02391011,0.00972663,0.02874263,-0.01047754,0.0655588,0.02276574,0.00172181,-0.25909677,0.0353974,-0.0815558,0.05945477,0.0188113,-0.04421078,0.01615441,-0.00927837,0.01343334,-0.04613457,0.02445077,0.05634597,0.02931903,-0.05871816,-0.01390114,-0.02411292,0.01538394,-0.05243984,0.07764905,0.01057466,0.08473123,0.03074361,0.25482389,-0.00905133,0.03131735,0.02983596,0.0321925,0.01759131,-0.04027624,0.02928901,0.00474853,-0.04045636,0.10239398,-0.0269157,-0.08255105,0.01490021,-0.00333044,0.00875918,-0.03132526,-0.00156335,-0.05953796,-0.00781074,-0.02415287,0.01139717,0.13973016,-0.01102418,-0.04599417,-0.0812421,0.04078914,0.08314304,-0.08258016,-0.06340683,-0.06065733,-0.03528982,0.02063791,0.04855742,0.0198645,-0.00145033,0.0148507,-0.01504065,0.02771685,0.00763096,0.0246846,0.04184202,0.01291006],"last_embed":{"hash":"1i7cbai","tokens":149}}},"text":null,"length":0,"last_read":{"hash":"1i7cbai","at":1753423498707},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{23}","lines":[106,107],"size":395,"outlinks":[{"title":"_**lotto strategies are based on filters**_","target":"https://saliu.com/filters.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{24}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06039417,-0.01053127,-0.05301166,-0.01745449,-0.0579382,0.07635484,0.00989664,0.00823593,0.05732517,0.00924047,0.02240206,0.00059479,0.02371741,-0.01312697,-0.00088305,-0.0024843,0.00036277,-0.01139661,-0.05783689,-0.01440488,0.07168252,-0.02951149,-0.04781992,-0.05078096,0.07967467,-0.00784316,-0.02851635,-0.09907769,-0.05233642,-0.21252923,0.03252508,0.04195071,0.02455202,-0.09028678,-0.0656638,-0.07993415,-0.05691279,0.09966085,-0.077644,0.0422725,0.00950319,-0.00319425,0.00918576,-0.0369381,-0.0094422,-0.04025349,-0.03650501,0.01123728,0.04269602,-0.01113936,-0.06746609,0.00742224,-0.01065251,0.04602283,0.06137193,0.02967268,0.04166291,0.07094735,0.00437319,0.04951982,0.05380921,0.05667482,-0.17655671,0.04751863,-0.02367651,0.01658785,-0.01813621,0.01294357,-0.0001378,0.04644999,0.00308931,0.02701627,-0.01404764,0.03382041,0.05310434,-0.02725897,-0.05854096,-0.02330395,-0.03394628,-0.00372884,-0.05078773,-0.0197348,0.00633703,-0.00894536,0.03868668,0.03049192,0.06506373,0.01317101,0.05249824,-0.04557921,0.02828745,0.09108271,-0.01561436,-0.01566971,0.00526891,-0.01401379,0.05495737,-0.04324613,0.00875081,0.10604198,0.04908295,-0.02186299,0.00065392,0.03023984,0.03509938,-0.04634628,-0.05178881,-0.05205402,-0.04073225,0.01811063,0.06440463,0.02930621,0.05172434,-0.0790149,-0.04058153,-0.00612454,0.01445746,-0.00609124,0.06186306,0.00322035,-0.02381663,-0.01322467,-0.01160327,0.00518571,0.04125291,0.03713185,0.01767105,0.07875094,0.03743728,0.00213703,0.02850956,0.02486225,-0.15293448,-0.02945313,-0.05611893,-0.01257699,0.01337473,-0.01282334,-0.00604973,0.03639051,0.01983747,-0.02029955,0.08298028,-0.14415413,0.00386895,0.08964694,-0.00567661,0.00869046,-0.0074231,0.02834149,-0.00172745,-0.00639272,-0.03943616,-0.08186979,-0.01846662,0.01190034,0.06328923,0.07733983,-0.05271065,0.0074163,-0.03105032,-0.04359565,-0.01006163,0.15101455,0.00253672,-0.0709313,-0.01852327,0.02055443,-0.02161886,-0.1087321,-0.02706358,0.0362365,-0.01754517,-0.00122301,0.08684635,-0.01055619,-0.06804549,-0.07806451,0.00412379,-0.00719637,0.00999582,0.01008132,-0.02277838,0.0375845,-0.01391992,-0.06972917,0.04819547,-0.02562727,0.0232752,0.04763103,-0.04605303,0.01989721,-0.02693686,0.03622808,-0.0549036,-0.03531395,-0.04555047,-0.02278923,0.06237388,0.00345774,-0.00436033,-0.00947703,0.01866227,0.01795404,-0.02238944,0.05970462,0.03641703,-0.06712734,0.07655338,0.04511791,-0.01148628,-0.00257979,0.03580194,0.03346612,-0.03577072,0.01334849,0.01687176,0.03065781,0.01580638,0.02660345,0.0368032,0.00581007,-0.076391,-0.17167176,-0.06293434,0.00547988,0.0123934,0.01819922,0.01197424,0.04488514,-0.01495424,0.04861565,0.0685339,0.09731891,-0.03577052,-0.0046075,0.0587997,-0.03479762,-0.03795413,-0.09444868,-0.02593695,-0.0148188,0.06081285,0.00301961,0.02189452,-0.02929643,-0.04744688,0.06669767,-0.0276926,0.09243322,-0.0208567,-0.00310695,-0.01825805,0.07330839,0.01286934,-0.04554098,0.03909223,0.03285242,0.0184837,-0.04146231,-0.01419909,-0.04323869,0.01498778,-0.0699307,0.0267661,0.00951921,-0.10021424,-0.05108257,-0.01422305,-0.04916108,0.0491556,-0.00084745,0.01041297,0.06603328,0.01125945,0.03359481,0.02936374,0.10400829,0.00157108,-0.08949613,0.02657081,-0.03197939,0.03218388,-0.02460799,-0.03930579,0.06246943,-0.01217162,0.00766102,0.04657998,-0.00806055,-0.02215733,-0.00094469,-0.00598443,0.01045835,0.10755738,0.00406475,0.00835482,-0.02454307,0.01102239,0.07253312,-0.05574937,-0.02413262,-0.00610494,0.01632525,-0.08033906,-0.00208593,0.04005999,0.03990479,0.01453266,0.04525733,0.08887291,-0.00992683,0.02385523,-0.00567659,0.02505522,-0.00076094,0.04693743,-0.0043016,0.01313557,-0.24708994,0.02829723,-0.05131853,0.06874962,-0.03032539,-0.01531185,0.02510949,-0.0162881,-0.00186311,-0.0267824,0.0377054,0.04663548,0.02034035,-0.07846091,0.02199029,-0.01594066,-0.00272975,-0.0630324,0.04544533,0.00617141,0.08268727,0.03782195,0.25222823,-0.01232927,0.00573466,0.00744309,0.03187364,0.03541942,-0.07021686,0.04133312,0.00620329,-0.02363285,0.0718529,-0.02668012,-0.09250558,0.00841803,0.0163573,-0.01472173,-0.02786949,-0.00377337,-0.0791713,-0.02158968,-0.06363442,0.01433256,0.15491576,-0.00784429,-0.03129481,-0.09568679,0.0421307,0.07763173,-0.09543209,-0.05917576,-0.03956297,-0.03939519,0.02384551,0.04088299,0.02388052,-0.0191961,0.00531214,-0.00389096,0.01236644,-0.01978162,0.04767365,0.00328432,-0.00467968],"last_embed":{"hash":"uv191z","tokens":184}}},"text":null,"length":0,"last_read":{"hash":"uv191z","at":1753423498751},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{24}","lines":[108,113],"size":615,"outlinks":[{"title":"Lottery strategies based on number frequency can be very effective only with Ion lotto software.","target":"https://saliu.com/ScreenImgs/lottery-frequency.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{25}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05320806,-0.02640049,-0.05686595,-0.03251388,-0.04194124,0.08064596,0.01114772,0.00126954,0.0808094,-0.01153889,0.02905953,0.00086316,0.03667911,-0.00010748,-0.01141467,-0.00015419,0.01211268,-0.04834542,-0.05238338,-0.00308572,0.07881118,-0.04380833,-0.04060114,-0.04560358,0.07923484,0.00425468,-0.05593364,-0.11127188,-0.06157839,-0.19142857,0.03034809,0.04045884,0.04999538,-0.07799446,-0.07891427,-0.07514719,-0.02838332,0.06205729,-0.06061792,0.05490525,-0.02745322,0.01054269,0.02738835,-0.0471611,-0.01178407,-0.03975738,-0.00655994,0.01188117,0.05639356,-0.00718984,-0.06719079,0.03143579,-0.02749593,0.04185295,0.03924824,0.03570142,0.0281663,0.0254377,-0.00293509,0.06194977,0.04485605,0.0021799,-0.16411005,0.04032603,-0.03279891,0.01132035,0.01163548,-0.02772904,-0.00165833,0.05735696,0.01871386,0.05253432,-0.01382751,0.02964532,0.03221818,-0.02714795,-0.06467618,-0.02425467,-0.01544025,0.02840146,-0.08216801,-0.00846169,-0.01310073,-0.03280813,0.06430092,-0.00998216,0.04688713,0.05316676,0.05578117,-0.03638831,0.036818,0.08725403,-0.00633336,-0.02111209,0.04025384,0.00060929,0.05745923,-0.04165169,-0.01978116,0.13648489,0.02269205,-0.00194735,-0.04321842,0.05042147,0.06060643,-0.03536579,-0.06556803,-0.07136732,-0.01566058,0.02591882,0.03808541,0.02030779,0.10846363,-0.06889911,-0.01114137,0.00538403,0.01972858,0.00382077,0.06284391,0.02856109,-0.02511599,-0.01393191,-0.0014015,-0.00333791,0.03434156,0.0425566,0.01520302,0.05778459,0.01842942,0.00711598,0.05392493,0.00569429,-0.16977343,-0.01239832,-0.04078159,-0.02474407,0.0109999,-0.00634305,-0.0263292,0.01646286,0.00916976,-0.04369706,0.08731124,-0.14021091,-0.0025545,0.12209382,-0.03161133,-0.01438934,-0.00616592,-0.00150227,0.01168668,0.00144501,-0.06723229,-0.09349259,-0.03502638,0.00880619,0.10074563,0.04367801,-0.03442105,-0.00416418,-0.04521347,-0.03769738,0.01162583,0.12107038,0.00246712,-0.08493961,-0.02378988,0.02125822,-0.0530232,-0.07699737,-0.01694269,0.01198843,-0.01672598,0.03948274,0.11068675,-0.01040463,-0.00799527,-0.04837997,-0.00236904,0.01254602,0.01746916,0.00515268,-0.03881724,0.00750035,-0.0122243,-0.02853419,0.03751883,-0.01709134,0.04395571,0.00769099,-0.06312287,0.04271985,-0.02396412,0.02119814,-0.06304745,-0.03806888,-0.03216327,-0.00715338,0.06519058,-0.0209778,-0.00685823,-0.01326552,0.02678554,0.04622867,-0.01982293,0.05606893,0.02428788,-0.05685665,0.09621713,0.00735864,0.00046982,-0.01446279,0.05356516,-0.0001382,-0.02106784,0.00877402,-0.01078357,0.02131001,-0.01177778,0.03968117,0.04435447,0.02960462,-0.10088859,-0.16120583,-0.04212829,0.01937366,-0.00803794,0.00186609,0.00298126,0.04132424,0.00038638,0.06127443,0.07680961,0.06520696,-0.05379269,-0.02193156,0.04000646,-0.03115747,-0.02446643,-0.08977757,-0.03017136,0.00581672,0.03056335,0.0089901,-0.01790455,-0.06273197,-0.07635295,0.03539185,-0.038665,0.10955372,-0.01280417,-0.00969242,-0.02529594,0.0412796,-0.01341727,-0.06548481,0.05730017,0.0227069,0.00848249,-0.01915595,-0.0376169,-0.06034077,0.01426075,-0.06517887,0.03690327,0.01880222,-0.09934129,-0.05357216,-0.01696978,-0.02654202,0.02871361,0.00167211,-0.001553,0.05414756,-0.00746448,0.03005324,0.05335717,0.07356334,-0.03546885,-0.08391719,0.00897675,-0.03974013,0.03472313,-0.01455398,-0.03781739,0.06207867,-0.0176782,-0.02878909,0.00844265,-0.01875741,-0.01054705,0.02490203,-0.01245871,0.01568524,0.10136373,0.01595712,0.00734202,-0.01837786,0.00425982,0.05783286,-0.0473818,-0.00860121,-0.00998412,0.03318663,-0.08101697,0.01592772,0.00942573,0.03962379,0.00813482,0.07413359,0.08966527,-0.01246534,0.03005967,0.02875877,0.02324796,-0.00678139,0.02045708,-0.02559546,-0.00102536,-0.24672389,0.04405608,-0.08811747,0.0770899,0.00674786,-0.00520141,0.03853144,0.01515356,-0.027592,-0.02388629,0.05192672,0.03772443,0.02638004,-0.03580886,0.01119951,-0.02529359,-0.00145917,-0.07393511,0.02219252,0.03890543,0.07866769,0.03057751,0.23065503,-0.02345326,0.04595535,0.02530524,0.0429,0.07909079,-0.03428084,0.03412753,0.01718623,-0.01163338,0.10604715,-0.01528126,-0.06626631,0.00587341,-0.00556611,-0.0063491,-0.03103503,0.00193322,-0.05951688,-0.04132953,-0.02737682,0.02171862,0.16422154,-0.0324226,-0.04529642,-0.08406423,0.03906728,0.07453762,-0.08903171,-0.04847412,-0.05808517,-0.0151419,0.03891252,0.04273058,0.0190981,0.00385162,0.02492848,-0.03151825,-0.00326383,-0.00730531,0.04124307,0.02832206,0.01402139],"last_embed":{"hash":"1qwkmjt","tokens":90}}},"text":null,"length":0,"last_read":{"hash":"1qwkmjt","at":1753423498800},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{25}","lines":[114,114],"size":219,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{30}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06535678,-0.04496739,-0.03149858,-0.04041214,-0.06298441,0.08303738,0.02592053,-0.00407748,0.07285505,0.0017273,0.00710517,-0.00920928,0.0574422,-0.01507539,0.00726065,0.00919699,0.01982262,-0.05498375,-0.06547045,-0.0056234,0.08906969,-0.04217361,-0.03487417,-0.05888554,0.06069554,0.00352318,-0.0538072,-0.09653507,-0.039453,-0.20268051,0.0331324,0.0699102,0.01931408,-0.09145179,-0.04778276,-0.0872893,-0.03421538,0.08406631,-0.07570902,0.05489637,-0.00633995,0.02146696,0.02590367,-0.0331896,-0.02226295,-0.01940242,-0.04765961,0.04030622,0.04393277,-0.00906514,-0.03457257,-0.00124975,-0.0189949,0.03518618,0.02213923,0.0291556,0.02426576,0.05280298,0.01653535,0.0631045,0.05288668,0.04954432,-0.18714286,0.02092698,-0.0036054,0.02533518,-0.01613914,-0.00948226,0.01726955,0.06440857,-0.006464,0.0447609,-0.0181556,0.03184,0.02496241,-0.01712705,-0.06456998,-0.02974368,-0.02335955,0.00920121,-0.05851106,-0.01263343,-0.03833013,-0.01626826,0.04952159,0.01462599,0.06724292,0.03771627,0.05184786,-0.04733712,0.0265336,0.08569349,-0.03166421,-0.02348637,0.02922053,0.02578888,0.04097966,-0.04491149,0.01896829,0.11927453,0.01137171,-0.04075748,-0.00182687,0.01572483,0.0034189,-0.02273216,-0.05789315,-0.0509264,-0.02407874,0.01618884,0.0795485,0.02484814,0.0683679,-0.10283124,-0.04182483,0.00562677,0.00187512,-0.01887341,0.05809017,0.00366869,-0.01783232,-0.02759505,0.01719826,0.01450994,0.03585147,0.03676047,0.02519477,0.08310784,0.03836329,0.0187986,0.03234139,0.02166901,-0.14787507,-0.0108646,-0.03903905,-0.0407077,0.01022517,0.00869539,-0.01331332,0.0299751,-0.00076127,-0.03631669,0.09792556,-0.12101184,0.01898357,0.10778832,-0.02497796,0.00771803,0.00808962,0.0308237,0.01677633,0.03671342,-0.02290453,-0.08844966,-0.03850309,0.01983967,0.0809763,0.03996842,-0.039668,0.00757409,-0.01366099,-0.04543503,-0.00474257,0.1406028,0.00582118,-0.08510601,0.00370762,0.04472055,-0.04482577,-0.0908152,-0.01703533,0.01745621,-0.01322485,0.0064433,0.09596478,-0.00869465,-0.04704266,-0.02635472,0.00985828,-0.00883105,0.01337858,0.01123853,-0.03682355,0.04003593,-0.0376499,-0.07844577,0.01149313,-0.00492136,0.02588848,0.04059552,-0.05046597,0.00772105,-0.02089613,0.03503677,-0.01859292,-0.04105317,-0.04056449,-0.02465983,0.08327019,0.01756347,0.00898387,-0.02984507,0.01534967,0.03572575,0.00720505,0.05371283,-0.00760239,-0.04900663,0.06879784,0.02402577,0.00722415,0.01285037,0.02919132,0.03893502,-0.01054253,0.02730832,-0.01007764,0.02976162,0.01674369,0.02195583,-0.00702614,-0.01029356,-0.07703981,-0.17211831,-0.03906566,0.02184903,-0.00950831,-0.01387715,0.03507399,0.02205923,0.0030323,0.0246148,0.07992087,0.05836735,-0.03328572,-0.04315193,0.02944118,-0.03973637,-0.05948759,-0.08881813,-0.04539111,-0.03215608,0.07200077,-0.01211666,0.00852851,-0.03320017,-0.04961642,0.04853647,-0.04703562,0.11648552,-0.02113483,-0.02617259,-0.00174908,0.04635902,0.01617867,-0.04123953,0.0444578,0.04369029,0.01487357,-0.04689631,-0.01408488,-0.05768483,0.02228981,-0.0683206,0.03046524,-0.01053636,-0.09205531,0.00035717,-0.02142258,-0.0337304,0.02442254,0.01162106,0.03165496,0.03206884,0.01042698,0.02988746,0.04921963,0.07985989,-0.02467592,-0.10734881,0.03341309,-0.03239946,0.01285582,-0.01552128,-0.04858582,0.07214808,-0.00735729,-0.03315912,0.04582174,-0.00380943,-0.02372576,-0.01182386,-0.02123025,0.01939143,0.11822055,0.03974745,-0.01351799,-0.00337003,-0.02214488,0.06157145,-0.04409774,-0.01796614,-0.02410618,0.01930597,-0.07213714,-0.01251307,0.04353575,0.04450494,-0.04268777,0.0782824,0.07718648,0.02114192,0.01902826,-0.01381699,0.01737401,0.00733447,0.02631424,-0.0196088,0.01379622,-0.25137144,0.05163762,-0.0814681,0.08051124,-0.01202035,-0.01037067,0.01349764,0.01593998,-0.00829577,-0.01894119,0.03066908,0.0045462,0.01843227,-0.06817947,0.02803941,-0.03103876,0.03195102,-0.0981217,0.05289557,0.03267203,0.0962825,0.04811971,0.2256266,-0.0067264,0.03433505,0.03204242,0.02429685,0.07038151,-0.02469824,0.04542982,-0.00640838,-0.03453957,0.09863544,-0.04387571,-0.06891233,0.0196316,0.01829558,-0.01820683,-0.03010963,0.02456697,-0.07083845,-0.04873102,-0.05129268,0.00594344,0.15966454,0.00573381,-0.03447713,-0.09719732,0.01878482,0.05961346,-0.09405015,-0.03597589,-0.0157424,-0.02893175,0.02999701,0.02535872,0.00004722,0.00492997,-0.00308452,-0.03580454,0.01787513,-0.01764314,0.03526045,0.03547866,-0.03443424],"last_embed":{"hash":"1re2dak","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"1re2dak","at":1753423498829},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{30}","lines":[119,119],"size":215,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{32}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06468207,-0.03974026,-0.0510219,-0.03187004,-0.07191378,0.07181305,0.03479468,0.0097761,0.05757528,0.00538644,0.00575567,-0.0215736,0.05804862,-0.00730776,-0.02556831,0.00694069,-0.01241681,-0.02827142,-0.03610934,-0.00979877,0.09780277,-0.0372672,-0.04764295,-0.10144126,0.0716941,0.03737056,-0.01917401,-0.09790105,-0.07080315,-0.23540965,0.04813841,0.03315502,0.02837161,-0.08491067,-0.06821961,-0.0292863,-0.04278211,0.09448922,-0.02276237,0.03078408,-0.01586395,0.00864957,-0.00127044,-0.01898837,-0.01332688,-0.04624888,-0.03323152,0.01167525,0.05770721,-0.01452409,-0.0650382,-0.01989629,0.004822,0.01112784,0.04077854,0.01518517,0.0261603,0.03097932,0.02683632,0.08872454,0.06153978,0.04647491,-0.20374607,0.03561263,-0.01094769,0.00764631,-0.04958025,-0.01080565,0.02940638,0.09529443,0.00383466,0.03546128,-0.02504843,0.05915524,0.03769509,-0.0315116,-0.05101189,-0.03841905,-0.03689433,0.03669043,-0.0553095,0.00872448,-0.01440472,0.0093831,0.04345592,0.00643814,0.04190688,0.02585969,0.04239323,-0.04874661,0.0548004,0.08080741,-0.04820535,-0.02816824,0.00783908,0.0306195,0.0849317,-0.04656614,0.01429534,0.10273301,0.0325861,-0.01955142,-0.04520486,0.02830937,0.03196001,-0.09262357,-0.0803307,-0.06697354,-0.02765072,0.00198498,0.07791412,0.03692154,0.03492427,-0.07286979,-0.0513486,0.02065515,-0.00583702,-0.01826137,0.05049642,-0.01712683,-0.01683618,0.00051562,-0.00017147,0.01153554,0.04455758,0.02699906,0.02209796,0.08076613,0.04605554,0.03907318,0.08032171,-0.00510726,-0.13341595,-0.01911213,-0.01777533,-0.01963751,-0.00512741,0.02238906,-0.00766388,0.02238225,-0.00417197,-0.02942875,0.06229603,-0.08579966,-0.00452202,0.12172098,-0.03642717,0.02269836,0.0081318,-0.00357774,0.00181198,0.01452683,-0.04454917,-0.06161378,-0.0160726,-0.02122529,0.09675144,0.03291533,-0.02174581,0.00411852,-0.02266637,-0.05730943,0.01770744,0.1046676,0.01321292,-0.04829095,-0.0010573,0.0391676,-0.04816232,-0.09471767,-0.0126999,0.02724313,-0.03412322,0.00936513,0.09081859,-0.02181301,-0.0690507,-0.03890964,0.05829261,-0.00053139,0.01306328,0.00017847,-0.02980543,0.0146471,-0.03387428,-0.07016382,0.03962157,-0.05355584,0.0065772,0.04198102,-0.01960505,-0.00189788,-0.02413825,0.0582665,-0.03205051,-0.01796766,-0.05677094,-0.0120774,0.06289376,-0.00039623,0.01053221,-0.04159533,0.02584067,0.01377228,-0.01882681,0.0721117,0.00956334,-0.05016064,0.05253477,0.02446084,-0.01501401,0.00266393,0.02562311,0.03965573,-0.02344666,0.02909533,0.00333976,0.01102908,0.01072421,0.02486237,0.00136918,0.01495577,-0.05670959,-0.16992779,-0.03004374,0.01115945,0.00426666,0.02697251,-0.00648782,0.03423367,-0.03497311,0.03880677,0.07758205,0.07096895,-0.06271119,-0.02747229,0.04098068,-0.00398328,-0.03254632,-0.12084458,-0.0424755,-0.06063806,0.08138233,0.01599262,-0.00334096,-0.04644707,-0.03519335,0.02722138,-0.043862,0.09706562,-0.00917151,-0.00158495,-0.01202575,0.04073196,-0.03234431,-0.03229741,0.01053715,0.03115153,0.02391465,-0.02693273,-0.00341163,-0.0500189,0.00009994,-0.05078988,0.02431381,0.00140911,-0.08195979,-0.04196354,-0.01173353,-0.04321777,0.04210349,0.00010104,0.05470048,0.05497014,0.00065566,0.03488955,0.03481593,0.03007763,-0.03664668,-0.08166524,0.03422653,-0.01619727,0.04309547,-0.00353521,-0.06140963,0.07924373,-0.04903406,0.010793,0.0156467,-0.00502993,-0.02611069,0.02726146,-0.01185644,0.00524181,0.10429129,0.03925147,0.04792212,0.02641072,0.03047307,0.04195595,-0.03629116,0.00468713,-0.0167584,0.01075543,-0.08829966,-0.00030353,0.02948563,0.02104238,-0.00911294,0.10846624,0.08309718,0.01729717,0.04600408,0.00259111,-0.01584742,-0.00139765,0.04808122,-0.01354797,0.01761343,-0.25722608,0.05778164,-0.06935243,0.05817785,0.0211463,-0.0503694,0.06147161,-0.01455088,-0.02132438,-0.04597645,0.04320954,0.03045296,0.01425116,-0.08108811,0.01065571,-0.03244521,0.00320807,-0.05065333,0.0539305,-0.01363527,0.09202386,0.00892591,0.22843672,-0.01152488,0.06624922,0.0407202,0.03359031,0.05302795,0.02538741,0.00151918,-0.00230863,-0.02793638,0.08622199,-0.05188427,-0.07168237,0.03450601,0.00843977,-0.01642244,-0.01603173,-0.01161231,-0.01874324,-0.02644207,-0.038207,-0.0084882,0.14849834,-0.01585794,-0.03529688,-0.06904478,0.04317245,0.05688868,-0.10120743,-0.05133914,-0.01680996,-0.04429458,0.00873152,0.03434931,0.00412106,0.02440584,0.00527414,-0.03661833,0.00804897,0.0010903,0.05469182,0.03701853,-0.00360429],"last_embed":{"hash":"izxf2n","tokens":478}}},"text":null,"length":0,"last_read":{"hash":"izxf2n","at":1753423498873},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{32}","lines":[122,131],"size":1623,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{38}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07466251,-0.0356417,-0.04670879,-0.03191159,-0.03632773,0.06696853,-0.00712387,0.01726816,0.05634435,-0.02413625,0.03103045,0.0095526,0.05023847,-0.01174413,-0.00607211,-0.0257339,0.0361146,-0.03601841,-0.03415144,0.00631662,0.06134665,-0.07851259,-0.02966764,-0.06673158,0.07852308,0.00226115,-0.03888258,-0.03373114,-0.07530986,-0.23791701,0.05147151,0.041513,0.0297653,-0.07647817,-0.06970032,-0.05240538,-0.04318094,0.09204017,-0.0575252,0.05959934,-0.01997801,0.00888189,0.01097251,-0.00524379,-0.00118748,-0.03368596,-0.03945842,0.01572036,0.0258489,-0.00939699,-0.05979435,-0.0012319,-0.03316264,0.04796599,0.06753148,0.03236619,0.07845318,0.04942032,0.03331545,0.08589735,0.05199037,0.04783637,-0.20540541,0.03736029,-0.00412998,0.03594861,-0.02144363,-0.01123684,0.0151671,0.01927917,-0.00460915,0.05214358,-0.02385527,0.04612684,0.01903461,-0.04272287,-0.05638784,-0.0349993,-0.05692784,0.03172437,-0.04125595,-0.0133682,-0.03823634,-0.03067979,0.04723212,0.03494582,0.03720386,0.03251663,0.04804506,-0.06551145,0.0263756,0.04346234,-0.002904,-0.01658534,0.0249708,0.0457746,0.0574348,-0.02355607,-0.00442206,0.09671009,0.03665006,-0.00754401,-0.02472879,0.02695799,0.03259188,-0.00610609,-0.0302588,-0.07316455,-0.03335417,0.03651372,0.05661772,0.0277914,0.03883782,-0.07023168,-0.00756937,0.00784442,-0.00001937,0.01199753,0.04557946,0.02682427,-0.03853057,0.00197146,0.00780589,0.02400818,0.04286398,0.0322487,0.0165879,0.07293135,0.06010234,0.01264735,0.01606444,0.00101657,-0.15261024,-0.02862738,-0.03196243,-0.02984898,0.01960431,0.00322221,0.02086192,0.03224026,-0.01270303,-0.05994352,0.05791767,-0.13739645,-0.00713576,0.12316374,-0.01594135,0.00998962,-0.00202063,0.02753156,0.02375626,0.0286372,-0.04822516,-0.10200618,-0.0210922,0.00166818,0.06704954,0.05565307,-0.02194334,-0.00008513,-0.01702456,-0.0382605,0.01280312,0.10389826,-0.01160139,-0.09938145,-0.03780463,0.00938503,-0.05409766,-0.11205518,-0.02584319,-0.00201377,-0.0342809,0.01936672,0.05783875,-0.01367515,-0.02437302,-0.05394951,0.02301114,0.0141686,0.00481282,-0.0203668,-0.02033249,-0.0076444,-0.02752921,-0.05666628,0.01513188,-0.05242537,0.03585264,0.01605558,-0.06019624,-0.00690784,-0.02448602,0.03758398,-0.0434715,-0.01133288,-0.01128266,0.0176134,0.09027562,-0.00007367,-0.02177027,-0.00347748,0.02366456,0.02450947,-0.02015152,0.06298082,0.01876453,-0.07047644,0.09203787,-0.01541789,-0.00477626,0.00604272,0.0413629,0.04051365,-0.05769647,0.00402035,0.0116272,0.00354833,-0.01317637,0.04453791,-0.00700956,0.01374501,-0.08043786,-0.18712044,-0.04293207,-0.00957329,0.01501109,0.00685866,-0.01326355,0.03140538,-0.01871518,0.0337032,0.09699207,0.07003494,-0.04152432,-0.03759717,0.02754027,-0.00588905,-0.02907584,-0.10527227,-0.06802706,-0.04242952,0.07065016,-0.01209638,0.00511837,-0.05009945,-0.06007739,0.08726486,-0.03104613,0.11840732,-0.00196264,-0.01495914,-0.02973966,0.06702943,-0.01591044,-0.02208895,-0.01198368,0.03619101,-0.01965398,0.00927604,0.00227635,-0.08351386,0.01020098,-0.07610217,0.01737853,0.00590866,-0.09166917,-0.02752774,-0.00002875,-0.00569763,0.01362555,0.00139385,0.04989581,0.06018732,0.00185778,0.03584425,0.00936364,0.04484726,-0.04888099,-0.07143097,0.02251194,-0.03415428,0.03220227,-0.0156908,-0.06143618,0.0884921,-0.00193712,0.02024058,0.00388462,0.00318219,0.03754094,0.03164547,0.00341727,-0.00162576,0.08478837,0.03595694,0.05003345,0.04328063,0.02097151,0.03880208,-0.03332366,0.03117961,-0.02184639,0.03342508,-0.03287182,0.01119905,0.05711611,0.01885318,0.00042369,0.05921794,0.0729292,0.00767412,0.01764094,-0.01946147,0.02886313,0.00137072,0.01609577,0.0014998,0.01458738,-0.25823322,0.02121162,-0.05989023,0.03940593,0.01354273,-0.03274639,-0.00181476,-0.00831265,0.01583362,0.00175615,0.04795599,0.02899741,0.04868563,-0.08048511,-0.00140245,-0.04426588,-0.01899564,-0.06299009,0.04561938,0.02266483,0.07994694,0.01191141,0.24987106,-0.02285456,0.02549844,0.01946702,0.04445006,0.01812135,-0.03441125,0.02581302,0.00751582,-0.02304156,0.08609673,0.00518427,-0.05664619,0.01451176,-0.01045925,-0.00047777,-0.0684387,0.00425792,-0.04566997,-0.0286919,-0.02563507,-0.00397004,0.15323688,-0.01327484,-0.04228793,-0.08504595,0.02840469,0.09689928,-0.08980682,-0.05818138,-0.04121011,-0.01321799,-0.0019983,0.07840849,0.03538265,0.02256851,0.03440991,-0.03271303,0.01000644,0.01123768,0.03020917,0.0318075,0.01750576],"last_embed":{"hash":"1soynz7","tokens":436}}},"text":null,"length":0,"last_read":{"hash":"1soynz7","at":1753423499026},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics##This new strategy is about lottery numbers divided into three categories based on frequency.#{38}","lines":[142,173],"size":3794,"outlinks":[{"title":"_**Frequency groups pick-3, pick-4 lotteries, lotto 5/39, 649 lotto**_","target":"https://saliu.com/frequency-tables.html","line":1},{"title":"_**Statistical Frequency Reports for Pennsylvania 5/39 Lotto**_","target":"https://saliu.com/frequency-reports.html","line":3},{"title":"Rank lottery numbers by frequency from hot to cold for Powerball, Mega Millions, Euromillions.","target":"https://saliu.com/ScreenImgs/frequency-lottery.gif","line":5},{"title":"Lottery software generates combinations from systems, strategies based on skips of numbers.","target":"https://saliu.com/ScreenImgs/lottery-systems-skips.gif","line":25},{"title":"_**Skip Systems, Software for Lotto, Lottery, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/skip-strategy.html","line":27},{"title":"Lottery players use statistics such as frequency to reduce the amount of lotto combinations to play.","target":"https://saliu.com/HLINE.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11483087,-0.03368256,-0.04626892,-0.02603076,-0.0537128,0.070707,-0.01374254,0.00971911,0.03040027,-0.00246623,0.00206161,-0.02153494,0.0615487,-0.05086209,-0.02206415,-0.02114988,0.01093378,0.02970099,-0.01251864,-0.03263525,0.1019663,-0.03504627,-0.0123544,-0.05367823,0.05003695,0.00579652,-0.04744447,-0.06612614,-0.05216965,-0.22125676,0.03977695,0.0085531,0.01421603,-0.07730811,-0.06446655,-0.01541058,-0.06158641,0.04133979,-0.05196814,0.04434956,0.01218869,0.00892847,0.05135953,-0.03693886,0.01450446,-0.02715938,-0.03173177,0.00774052,0.04274309,-0.00365251,-0.07257306,0.04633348,-0.00034508,0.05426607,0.06875432,0.05223061,0.07740164,0.09477864,0.0355369,0.06388321,0.04359214,0.05794057,-0.21831195,0.06687155,-0.00025738,0.01345165,0.00714302,-0.01012769,0.02753534,0.04598827,-0.01476101,0.01585452,-0.02267822,0.05727867,0.03631632,-0.02283152,-0.03581601,-0.06257173,-0.01922168,0.00151709,-0.03330619,-0.01792677,0.00910241,-0.03303259,0.03576464,0.03220813,0.0528555,0.05607212,0.07809795,-0.06660477,0.00711411,0.05045731,0.04044829,0.02460286,0.02070904,0.00242607,0.0444259,0.01848556,-0.01170956,0.10004376,0.0268599,-0.01499372,-0.04182907,0.03743356,0.07530548,-0.06331138,-0.0086423,-0.05980596,0.00173114,0.04631368,0.06414423,0.02288694,0.05402365,-0.04814798,-0.01085898,0.01381612,0.00184577,0.01181767,0.01868288,-0.00634114,-0.05479169,-0.00283045,0.01537567,0.01691053,0.020264,0.00385799,0.01831764,0.06846951,0.02371484,0.04052539,0.04096593,-0.00975998,-0.18280551,-0.05746379,-0.02658016,0.00630957,0.00579811,-0.02299746,0.00075616,0.02402108,-0.02299622,-0.06081841,0.0480757,-0.07760159,-0.01915355,0.10397462,0.01791603,-0.00724041,0.01382809,0.00407324,0.01539029,0.0163268,-0.02543856,-0.07312509,-0.00294829,0.03425138,0.08414412,0.07709812,-0.04872693,0.00077277,-0.02326631,-0.02770203,-0.03223794,0.13885891,-0.01683971,-0.09329463,-0.01403083,0.06732436,-0.03699282,-0.07721638,-0.03891551,-0.01262692,-0.02531347,0.00533115,0.10680631,-0.03694816,-0.04195936,-0.06888029,-0.02112789,-0.00238928,-0.00600441,-0.03287847,-0.00808823,0.00103341,-0.04358767,-0.05020535,0.02143307,-0.04904785,0.04580912,0.01936005,-0.05745926,-0.03713793,-0.03655659,0.00357662,-0.03311818,-0.02376448,-0.06090613,-0.05352784,0.06907623,-0.01848462,-0.00071257,-0.00619954,0.00794808,0.02489184,-0.01175674,0.081861,-0.00180516,-0.05438953,0.08636312,0.00058454,-0.00963892,0.01584734,0.05038083,0.04262221,-0.0330305,0.03104547,0.0036263,0.04245879,-0.01631888,0.02576858,-0.01149733,0.0434129,-0.10988579,-0.20430535,-0.0151397,-0.04128369,0.00822189,-0.00207081,-0.0293359,0.02698007,-0.03046693,0.05118349,0.06943239,0.06745542,-0.02983885,-0.05721886,0.05858881,-0.01510264,-0.02996961,-0.05245171,-0.05905737,-0.05630801,0.0131576,0.00367041,0.00216191,-0.04686726,-0.0585659,0.05302044,-0.04367684,0.10046384,-0.02429105,0.00125918,0.01506563,0.06903949,0.0230518,-0.03424983,0.00466517,0.01755702,0.02647047,-0.02070645,-0.02162531,-0.03773346,0.00487211,-0.08804194,0.05317494,0.00181067,-0.083941,-0.05990011,0.0063526,0.00805011,0.01225944,-0.00305836,0.05593454,0.01456605,0.02842793,0.04111798,0.03647449,0.05596945,-0.02547519,-0.05954815,-0.01645238,-0.02248117,0.02690825,-0.03793655,-0.04293346,0.0684195,-0.04859275,0.01564931,0.02094587,0.00415721,-0.00717046,0.04687988,0.01809117,0.00491894,0.12004737,0.02995333,0.02402321,0.00222494,0.02839501,0.03263463,-0.0630414,-0.02595401,-0.03679635,-0.03774876,-0.03254272,0.05155202,0.07193597,0.09178548,-0.00563586,0.05112115,0.03832221,0.00744126,0.01338531,-0.0000646,0.00896448,0.00575688,0.0281068,0.00359356,0.04157962,-0.2712422,-0.00067194,-0.06252097,0.05154135,-0.02393819,-0.02410767,0.02869844,-0.01568243,-0.00921741,-0.02911123,0.02720534,0.01755017,0.02377977,-0.03835022,-0.003624,-0.03607241,0.01266945,-0.03987866,0.09274613,0.02978623,0.04203882,0.02674776,0.23032317,-0.00569649,-0.00976346,0.0386286,0.00693037,0.04061215,0.02589736,0.0291,0.00807718,-0.00264938,0.09279528,0.01554634,-0.01988365,0.01466215,-0.03642103,0.00279196,-0.00654907,-0.01205715,-0.0585118,-0.02072561,0.00917655,0.01246317,0.15352613,0.00843523,-0.01343731,-0.08597283,0.04334907,0.06838666,-0.07892926,-0.04181021,-0.0848991,-0.00392751,0.01429428,0.04295143,-0.00086115,-0.01701304,0.01649714,-0.0386706,0.02798158,0.01187941,0.03582149,0.00889851,-0.01586437],"last_embed":{"hash":"i15e8d","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"i15e8d","at":1753423499153},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>","lines":[174,198],"size":2846,"outlinks":[{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":5},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":7},{"title":"_**<u>Lottery Mathematics, Lotto Mathematics</u>**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":8},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":9},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":10},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":11},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":12},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":13},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":14},{"title":"_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_","target":"https://saliu.com/cross-lines.html","line":15},{"title":"_**<u>Lottery Strategies</u>**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":16},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":17},{"title":"**<u>Lottery Software, Lotto Programs</u>**","target":"https://saliu.com/infodown.html","line":18},{"title":"Frequency Rank is unique lottery software to analyze the drawings in many lotto games in the world.","target":"https://saliu.com/HLINE.gif","line":20},{"title":"Forums","target":"https://forums.saliu.com/","line":22},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":22},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":22},{"title":"Contents","target":"https://saliu.com/content/index.html","line":22},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":22},{"title":"Home","target":"https://saliu.com/index.htm","line":22},{"title":"Search","target":"https://saliu.com/Search.htm","line":22},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":22},{"title":"Download software for lottery strategy, systems based on lotto number frequency.","target":"https://saliu.com/HLINE.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11701057,-0.03086435,-0.0458547,-0.02526254,-0.05199314,0.06950761,-0.01097501,0.00595033,0.02794426,-0.00213805,0.00158258,-0.02283705,0.06364705,-0.05161186,-0.02040933,-0.02305327,0.01025837,0.03369319,-0.01430461,-0.03110072,0.10302423,-0.03138926,-0.01742128,-0.05510918,0.04898924,0.00690337,-0.04463543,-0.06584897,-0.04932987,-0.21894202,0.04311083,0.00575973,0.00982061,-0.07497967,-0.06549468,-0.01122648,-0.05945011,0.04058351,-0.05201644,0.04257519,0.01590887,0.00762503,0.04960499,-0.03969431,0.01703168,-0.03031092,-0.03397573,0.0092929,0.0463178,-0.00083487,-0.07263813,0.05218741,0.00363378,0.05356407,0.06866208,0.05534837,0.07925898,0.09997609,0.03303644,0.06343881,0.04195526,0.0604584,-0.21866563,0.06416336,-0.0003652,0.01512687,0.00742751,-0.01091701,0.02766027,0.04267486,-0.01493759,0.01396772,-0.02329701,0.05975566,0.03693841,-0.02239056,-0.03593352,-0.06407758,-0.02115676,0.00201728,-0.03591994,-0.01845711,0.00924551,-0.03245676,0.03628449,0.03531065,0.05083793,0.05486215,0.07828977,-0.06857292,0.00737295,0.04644411,0.03996889,0.02396317,0.02167377,0.00101513,0.04735546,0.01734322,-0.01041129,0.09970008,0.02682239,-0.01343107,-0.04043284,0.03295282,0.07768065,-0.06568623,-0.00866253,-0.05786344,0.00593592,0.04604657,0.06096677,0.02249453,0.04882678,-0.04181226,-0.01094357,0.01640273,-0.00318692,0.01671913,0.0140805,-0.00522822,-0.05511961,-0.00461998,0.01272739,0.0177862,0.01272674,0.0031121,0.01665362,0.06695145,0.02265285,0.04087812,0.04521042,-0.0107682,-0.18458475,-0.0624457,-0.02578735,0.00734744,0.00512855,-0.02659886,0.00134154,0.02920608,-0.02301783,-0.05899733,0.04725107,-0.0737166,-0.01886857,0.10260393,0.01797029,-0.00436045,0.01573854,0.00287831,0.01470557,0.01268167,-0.02626711,-0.07137451,-0.00373412,0.03107196,0.08563152,0.07904821,-0.04825556,0.00335203,-0.02387065,-0.03024045,-0.03487319,0.14104174,-0.01376674,-0.09455115,-0.01278111,0.0684261,-0.03550264,-0.07763655,-0.03828984,-0.01372172,-0.02549917,0.00725507,0.10625371,-0.0396977,-0.04631939,-0.06934439,-0.02045828,-0.00105706,-0.0068084,-0.03723808,-0.00782476,0.00052071,-0.04539562,-0.05096622,0.02286184,-0.04648967,0.04392089,0.01871978,-0.05838314,-0.0346094,-0.03559523,-0.00013825,-0.03113612,-0.021872,-0.06048384,-0.05589284,0.06923083,-0.01984773,-0.00229823,-0.00547112,0.01017332,0.02219387,-0.01189888,0.0806632,-0.00282469,-0.05193001,0.08578052,-0.0016131,-0.01322671,0.01749275,0.04990701,0.04098572,-0.03178948,0.03005573,0.00684853,0.04093763,-0.01513482,0.02554082,-0.01088423,0.04344435,-0.10707944,-0.20441496,-0.01592712,-0.0482824,0.00615791,-0.0047553,-0.03123694,0.0235329,-0.0321111,0.04428226,0.06909943,0.07102701,-0.03119623,-0.055021,0.06321134,-0.01353431,-0.02859774,-0.04794544,-0.05351966,-0.05685134,0.00988101,0.00289564,0.00202955,-0.04485707,-0.05797571,0.05305949,-0.03896155,0.10089743,-0.02444223,0.00116622,0.01232581,0.06928183,0.02182551,-0.03495847,-0.00169035,0.01702565,0.02978569,-0.02120585,-0.02337864,-0.03566341,0.00319704,-0.08848947,0.05275358,0.00347948,-0.08256169,-0.05697547,0.00776456,0.00788631,0.00977854,0.00091707,0.05914268,0.01215077,0.024025,0.04033061,0.03730667,0.05539633,-0.02500364,-0.05840182,-0.01582542,-0.02140219,0.0274354,-0.03735207,-0.04470992,0.06520997,-0.04870745,0.01882811,0.01751849,0.00415788,-0.00767322,0.0458624,0.02055657,0.00442489,0.12092469,0.02983062,0.02787539,-0.00127339,0.02632344,0.02956828,-0.061416,-0.02186631,-0.03758433,-0.03877556,-0.03253331,0.05151119,0.07501727,0.09355728,-0.00490653,0.05379105,0.03186474,0.0081602,0.01127693,0.00151557,0.0094206,0.00286493,0.03048413,0.00667759,0.04056424,-0.27361301,0.00272165,-0.06521864,0.04907382,-0.02547732,-0.02638013,0.02875513,-0.01651117,-0.00860764,-0.03278776,0.02939987,0.01792784,0.02183937,-0.03680943,-0.00560157,-0.03246447,0.01588087,-0.03821333,0.09224999,0.03059743,0.04082426,0.03245813,0.23277423,-0.00710869,-0.01048034,0.03657906,0.00381713,0.03909145,0.02462862,0.02990025,0.01011557,-0.00215777,0.09090968,0.01733078,-0.01744798,0.01466835,-0.03729951,0.00356743,-0.00081567,-0.01235416,-0.06142309,-0.01840036,0.00711205,0.01085327,0.15114264,0.01153739,-0.00973562,-0.08616342,0.04473849,0.06888514,-0.07557188,-0.03827796,-0.08493374,-0.0045858,0.01530393,0.04048685,-0.00118682,-0.02092361,0.01908563,-0.03616631,0.02889998,0.01441548,0.03434423,0.00900781,-0.01807357],"last_embed":{"hash":"1b5n6bx","tokens":449}}},"text":null,"length":0,"last_read":{"hash":"1b5n6bx","at":1753423499280},"key":"notes/saliu/Lottery Strategy Software Number Frequency Statistics.md#Lottery Strategy Software Number Frequency Statistics#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>#{1}","lines":[176,198],"size":2770,"outlinks":[{"title":"_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_","target":"https://saliu.com/MDI-lotto-guide.html","line":3},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":5},{"title":"_**<u>Lottery Mathematics, Lotto Mathematics</u>**_","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":6},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":7},{"title":"_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_","target":"https://saliu.com/lie-lottery-strategies-pairs.html","line":8},{"title":"_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_","target":"https://saliu.com/lie-lotto-strategies-decades.html","line":9},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":10},{"title":"_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_","target":"https://saliu.com/favorite-lottery-numbers-positions.html","line":11},{"title":"<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_","target":"https://saliu.com/cross-lines.html","line":13},{"title":"_**<u>Lottery Strategies</u>**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":14},{"title":"_**Strategy Lotto Software on Positional Frequency**_","target":"https://forums.saliu.com/lotto-software-position-frequency.html","line":15},{"title":"**<u>Lottery Software, Lotto Programs</u>**","target":"https://saliu.com/infodown.html","line":16},{"title":"Frequency Rank is unique lottery software to analyze the drawings in many lotto games in the world.","target":"https://saliu.com/HLINE.gif","line":18},{"title":"Forums","target":"https://forums.saliu.com/","line":20},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":20},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":20},{"title":"Contents","target":"https://saliu.com/content/index.html","line":20},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":20},{"title":"Home","target":"https://saliu.com/index.htm","line":20},{"title":"Search","target":"https://saliu.com/Search.htm","line":20},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":20},{"title":"Download software for lottery strategy, systems based on lotto number frequency.","target":"https://saliu.com/HLINE.gif","line":22}],"class_name":"SmartBlock"},
