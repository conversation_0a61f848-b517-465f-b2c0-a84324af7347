"smart_sources:樂透隨機如何研究與可能的提高預測準確度的方法，並以Julia project實現.md": {"path":"樂透隨機如何研究與可能的提高預測準確度的方法，並以Julia project實現.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04222737,0.02167467,-0.03124178,-0.06438059,-0.03338026,0.0450904,-0.02354204,0.02762641,0.04410548,-0.00642997,0.03424541,-0.06314644,0.04861771,0.03477563,0.0133877,0.01801817,0.06752732,-0.02688739,-0.03771683,0.03449176,0.10396123,-0.05173773,0.04026101,-0.05774539,0.05749224,0.05285865,-0.02673119,-0.03133963,0.00888362,-0.22418997,0.01640145,0.02921341,0.02861469,0.02067346,0.0301753,-0.04143099,-0.04540193,0.05703269,-0.05043139,0.02459789,0.04099342,-0.00362464,0.02221028,-0.00323391,0.02575042,-0.07845856,-0.05327906,-0.01519901,-0.01643748,-0.03055406,-0.0268493,-0.04549291,0.02990767,0.0289443,-0.03823248,0.0218438,0.05081586,0.020833,0.05977602,0.03370987,0.00885675,0.03585566,-0.22799081,0.10452016,0.00881287,-0.02612504,-0.000003,-0.03119529,0.07405049,0.08926635,-0.06169953,0.02639717,-0.00150815,0.03470076,-0.0130204,-0.06926218,0.04559748,-0.05391244,-0.06920587,-0.06629764,-0.01520555,0.03285217,-0.02973733,-0.03274027,-0.01132291,0.05480461,-0.00903296,-0.04509823,0.06221967,0.01408233,0.00921496,-0.01838306,-0.01574381,0.05217498,-0.04063769,-0.0179607,-0.00827241,-0.0327771,-0.03533489,0.12925845,-0.04685435,-0.02022317,0.01616919,0.00170513,0.03056414,-0.01513842,0.02178633,-0.03769222,-0.04614991,-0.02519343,-0.00296964,-0.00858975,0.0764993,-0.05488853,-0.02104349,0.03932925,0.0428459,0.06923404,-0.00419706,-0.00265128,0.04053387,0.01611917,0.00051257,-0.02698427,0.00872443,-0.0038269,0.00329571,0.056376,0.00484502,0.0394315,0.05967567,0.04076761,0.02664893,0.03255428,-0.02843078,-0.04064693,-0.0229625,0.04446957,0.01704217,-0.03588211,0.00012567,-0.03750613,0.01284989,-0.06539389,-0.04321907,0.06622874,-0.08173461,0.04238026,0.00060096,-0.06462689,0.05527613,0.03782625,-0.01150883,-0.0136586,0.08665244,0.00551182,0.08200195,0.10495871,-0.02202539,-0.03047718,0.00229163,-0.04985279,0.00116734,0.07869148,-0.00045942,-0.06340756,0.01315744,0.06203638,0.01006161,-0.05339461,0.04154355,0.01381492,-0.00511043,-0.00736203,0.08211544,0.04387067,-0.02636275,-0.00765889,0.01182799,0.01043794,0.00657653,-0.00798721,-0.02212593,0.06795612,-0.02634717,-0.06849636,0.00046771,-0.00797011,0.01908157,0.0174593,-0.08101186,0.04360539,-0.00241186,0.08867369,-0.01499777,-0.02748914,0.02375434,-0.02519165,0.0441993,-0.02748192,0.13210823,-0.02230347,-0.01550735,-0.02707049,0.01495129,-0.04884488,0.05838197,-0.06562912,-0.00334897,0.07370716,-0.11743055,0.01782486,0.00377022,-0.00558633,-0.04889612,0.03447575,0.01183377,0.00501475,-0.01176023,0.04318968,0.01595617,-0.02368782,-0.14274834,-0.2648173,-0.0140784,0.0328786,-0.08290214,0.06110774,-0.06248025,0.00146841,-0.02261451,0.05172602,0.07953784,0.11215925,0.02871967,-0.06485273,0.0152584,-0.01306611,-0.01336547,0.03568613,0.01941596,-0.01929694,-0.01508416,-0.03381349,0.04281918,-0.0101141,-0.04504829,0.03190431,0.00650212,0.12674218,-0.00260785,0.07197563,0.0188901,0.05071189,0.01654396,-0.034031,-0.08294278,0.01666633,0.02938068,-0.05614717,-0.00038372,-0.03961562,-0.04325881,0.00976639,0.03567028,-0.02268774,-0.0852084,-0.00050693,0.01695972,-0.04647546,-0.043983,-0.08212487,0.04001082,0.05666008,-0.01990286,0.00024698,0.08616674,-0.02800755,-0.00979393,-0.0512323,-0.01446164,-0.0381958,0.02579752,-0.02964675,-0.00072581,0.02714177,-0.07340157,0.01658971,-0.03101726,-0.02148201,0.00692606,0.01911965,0.01652657,0.00572626,0.13075766,0.01787241,-0.00949273,0.06092976,-0.0276326,-0.05640867,-0.07985637,0.02199355,-0.00211075,0.0773502,-0.00368783,-0.00021244,0.06068536,0.01263615,0.01798298,0.00679513,0.00874978,0.06343894,-0.00624557,-0.02429025,0.0334222,-0.02956968,0.0171347,0.0154197,-0.05574487,-0.24594375,0.02138757,-0.06650887,0.0616096,-0.06373026,0.01658676,0.02834953,-0.00633105,-0.0469399,0.06002812,-0.04657182,0.02253857,0.04385909,-0.03440768,0.01070038,-0.0351261,0.00795294,-0.01514642,0.02422574,0.00036966,-0.064836,-0.03405691,0.22409509,-0.01434456,0.03221837,-0.0319173,0.01039182,0.02486449,0.0226001,-0.01668116,-0.04491303,-0.02130353,0.07237312,-0.02716078,0.03764753,-0.03489387,-0.01571888,-0.02101645,-0.02694723,-0.02235912,-0.03777483,0.02956206,-0.05451545,-0.00089937,0.11912344,0.03941945,0.00580429,-0.02475884,-0.07101177,0.04501575,-0.04183727,0.04233025,0.00611,0.00976171,-0.01928687,0.05100286,0.02753935,-0.0316455,-0.02034889,-0.00568597,0.05139384,0.01723718,0.01994252,0.04905169,0.00682814],"last_embed":{"hash":"4c6ab729166c139ef2820d47f9367d52bc8c7d011b120b7a55e64cd54a323ee4","tokens":460}}},"last_read":{"hash":"4c6ab729166c139ef2820d47f9367d52bc8c7d011b120b7a55e64cd54a323ee4","at":1745995165919},"class_name":"SmartSource2","outlinks":[],"blocks":{"#":[1,2],"#樂透隨機性研究與預測準確度提升之數學模型建構及Julia實作":[3,345],"#---frontmatter---":[5,null]},"last_import":{"mtime":1740041561486,"size":11324,"at":1740449881683,"hash":"4c6ab729166c139ef2820d47f9367d52bc8c7d011b120b7a55e64cd54a323ee4"},"key":"樂透隨機如何研究與可能的提高預測準確度的方法，並以Julia project實現.md"},