
"smart_sources:Obsidian Companion.md": {"path":"Obsidian Companion.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"277f83e1142e8be4f77b082edae6eb02c24d9fa6df03333a8f975541b9f6ef4f","at":1741247660406},"class_name":"SmartSource2","outlinks":[{"title":"codemirror-extension-inline-suggestion","target":"https://github.com/saminzadeh/codemirror-extension-inline-suggestion","line":7},{"title":"codemirror-companion-extension","target":"https://www.npmjs.com/package/codemirror-companion-extension","line":7},{"title":"demo","target":"https://raw.githubusercontent.com/rizerphe/obsidian-companion/main/screenshots/demo.gif","line":11},{"title":"Obsidian Community Plugin Directory","target":"https://obsidian.md/plugins?id=companion","line":15},{"title":"Companion","target":"https://obsidian.md/plugins?id=companion","line":17},{"title":"OpenAI API Keys","target":"https://platform.openai.com/account/api-keys","line":26},{"title":"plugin's GitHub repository","target":"https://github.com/rizerphe/obsidian-companion","line":48}],"blocks":{"#Obsidian Companion":[1,8],"#Obsidian Companion#{1}":[3,8],"#Demo":[9,12],"#Demo#{1}":[11,12],"#Installation":[13,21],"#Installation#{1}":[15,16],"#Installation#{2}":[17,17],"#Installation#{3}":[18,18],"#Installation#{4}":[19,19],"#Installation#{5}":[20,21],"#How to Use":[22,57],"#How to Use#{1}":[24,25],"#How to Use#{2}":[26,26],"#How to Use#{3}":[27,27],"#How to Use#{4}":[28,28],"#How to Use#{5}":[29,29],"#How to Use#{6}":[30,30],"#How to Use#{7}":[31,31],"#How to Use#{8}":[32,32],"#How to Use#{9}":[33,34],"#How to Use#{10}":[35,36],"#How to Use#How to Use (Mobile)":[37,49],"#How to Use#How to Use (Mobile)#{1}":[39,40],"#How to Use#How to Use (Mobile)#{2}":[41,41],"#How to Use#How to Use (Mobile)#{3}":[42,42],"#How to Use#How to Use (Mobile)#{4}":[43,43],"#How to Use#How to Use (Mobile)#{5}":[44,44],"#How to Use#How to Use (Mobile)#{6}":[45,45],"#How to Use#How to Use (Mobile)#{7}":[46,47],"#How to Use#How to Use (Mobile)#{8}":[48,49],"#How to Use#Groq":[50,53],"#How to Use#Groq#{1}":[52,53],"#How to Use#Ollama":[54,57],"#How to Use#Ollama#{1}":[56,57],"#Presets":[58,70],"#Presets#{1}":[60,62],"#Presets#{2}":[63,63],"#Presets#{3}":[64,64],"#Presets#{4}":[65,65],"#Presets#{5}":[66,66],"#Presets#{6}":[67,68],"#Presets#{7}":[69,70],"#Completion providers":[71,80],"#Completion providers#{1}":[73,74],"#Completion providers#{2}":[75,75],"#Completion providers#{3}":[76,76],"#Completion providers#{4}":[77,77],"#Completion providers#{5}":[78,79],"#Completion providers#{6}":[80,80]},"last_import":{"mtime":1741246867223,"size":6420,"at":1741247660407,"hash":"277f83e1142e8be4f77b082edae6eb02c24d9fa6df03333a8f975541b9f6ef4f"}},