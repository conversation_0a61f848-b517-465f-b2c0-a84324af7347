
"smart_sources:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md": {"path":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"12lpigh","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363612081,"size":22505,"at":1753423416500,"hash":"12lpigh"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Wheels: Create, Make Lottery Wheeling Systems":[8,216],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#{1}":[10,17],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>Wheeling Lotto Numbers, Playing <i>Lotto Wheels</i>, Make Your Own Lottery Systems</u>":[18,19],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#By <PERSON>, ★ _Founder of Lotto Wheeling Mathematics_":[20,23],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#By <PERSON>, ★ _Founder of Lotto Wheeling Mathematics_#{1}":[22,23],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>":[24,113],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{1}":[26,27],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{2}":[28,29],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{3}":[30,35],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>":[36,57],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}":[38,57],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>":[58,113],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}":[60,113],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>":[114,170],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{1}":[116,128],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{2}":[129,130],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{3}":[131,162],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{4}":[163,163],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{5}":[164,164],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{6}":[165,165],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{7}":[166,166],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{8}":[167,168],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{9}":[169,170],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)":[171,216],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}":[173,176],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{2}":[177,178],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{3}":[179,179],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{4}":[180,180],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{5}":[181,182],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{6}":[183,183],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{7}":[184,185],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{8}":[186,186],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{9}":[187,187],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{10}":[188,188],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{11}":[189,189],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{12}":[190,190],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{13}":[191,191],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{14}":[192,192],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{15}":[193,193],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{16}":[194,194],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{17}":[195,195],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{18}":[196,196],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{19}":[197,210],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{20}":[211,216]},"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheels mathematically with combinatorics.","target":"https://saliu.com/AxiomIon.jpg","line":16},{"title":"You make lotto wheels manually or use the best lottery wheeling software to create lotto wheels.","target":"https://saliu.com/HLINE.gif","line":22},{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":122},{"title":"These are the only winning strategies applicable to lotto wheels for 12 and 18 numbers.","target":"https://saliu.com/HLINE.gif","line":161},{"title":"_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":166},{"title":"Selecting the lotto numbers to play a wheel is the most important element. The best are the most frequent lottery numbers.","target":"https://saliu.com/HLINE.gif","line":169},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":171},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":177},{"title":"_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":180},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":181},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":183},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":184},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":186},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":188},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":189},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":191},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":192},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":193},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":194},{"title":"_**Powerball Wheels**_","target":"https://saliu.com/powerball_wheels.html","line":195},{"title":"_**Mega Millions Wheels**_","target":"https://saliu.com/megamillions_wheels.html","line":196},{"title":"_**Euromillions Wheels**_","target":"https://saliu.com/euro_millions_wheels.html","line":197},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":198},{"title":"Lottery wheeling master teaches how to create the best 12-number lotto wheel, 18 numbers 6 per line.","target":"https://saliu.com/HLINE.gif","line":211},{"title":"Forums","target":"https://forums.saliu.com/","line":213},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":213},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":213},{"title":"Contents","target":"https://saliu.com/content/index.html","line":213},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":213},{"title":"Home","target":"https://saliu.com/index.htm","line":213},{"title":"Search","target":"https://saliu.com/Search.htm","line":213},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":213},{"title":"The 12 and 9-number lotto wheels assure best chances for high lottery prizes, including the jackpot.","target":"https://saliu.com/HLINE.gif","line":215}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto wheels","lotto wheeling","abbreviated","reduced","lotto","systems","lottery wheel","free","software","lottery","wheeler","Powerball","Mega Millions","Euromillions","create","make"],"source":"https://saliu.com/lottowheel.html","author":null}},
"smart_sources:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md": {"path":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0787127,-0.05102939,0.00377232,-0.01584992,-0.09940495,0.06257673,-0.04268896,0.01499151,0.03835891,0.00420943,0.0132464,-0.02247728,0.04318301,0.00080452,-0.01201225,-0.02435965,0.01377212,0.02642169,-0.07340953,0.00353588,0.07638278,-0.07354163,-0.10042173,-0.06051946,0.03487198,0.02186998,-0.02878647,-0.01995001,0.02090052,-0.23008254,0.03650681,0.03862883,-0.01357381,-0.01054226,-0.0730688,-0.04088312,-0.05259638,0.03149419,-0.09512691,0.00612979,0.01777925,0.04096488,0.0418439,-0.00515645,0.05848944,0.00510434,0.01320142,0.04767969,0.06958954,0.00729323,-0.03131407,-0.02581251,0.00196185,-0.0068672,0.04703221,0.02625329,0.04561289,0.10041076,0.04149815,0.0212427,0.0445868,0.05920698,-0.17453213,0.05851077,-0.00089331,0.01059001,0.0119846,-0.0558234,0.04760578,0.04228764,0.04786435,0.00205962,0.00454137,0.06063878,0.00003299,-0.01157587,-0.04267045,-0.08326209,-0.03462454,0.0055046,-0.03166509,0.00725212,-0.0055847,0.024624,-0.03704918,0.04606062,0.0309111,0.00819924,0.0630056,-0.09502036,0.00665463,-0.00928835,0.08862856,0.05088422,-0.02292151,0.00395989,0.00544998,0.01550147,0.02097174,0.11231051,0.00819433,0.01165699,0.02297407,-0.01763803,-0.00357817,-0.01522476,-0.02625963,-0.06274603,-0.04513754,0.05785083,0.05547317,0.00102607,0.0494422,-0.01952802,-0.06271142,-0.03674342,-0.00364858,0.02006238,-0.00280899,0.01864165,-0.03103041,0.04107259,0.03109344,-0.00586526,0.00830472,0.0076711,0.00309125,0.04781245,0.01140076,0.03731567,0.04225278,-0.00190473,-0.09327497,-0.03666973,0.03968494,-0.05162385,0.0152218,0.02632693,0.00882412,0.01057329,-0.01520567,-0.03146011,0.00874104,-0.11472906,-0.0346573,0.02747875,-0.01031002,-0.01971675,0.02215059,0.00165588,0.01141472,-0.04837091,-0.05590778,-0.07697462,0.04309382,0.02806462,0.12672704,0.05555441,-0.04500403,0.04343434,-0.04865538,-0.05426227,-0.04918468,0.09662928,-0.03771352,-0.09677761,-0.03816717,0.06485666,0.00866161,-0.07557741,-0.03421798,0.00192743,-0.07344248,0.04248226,0.11893473,-0.00149533,-0.08183195,-0.02664949,-0.04831797,-0.00469471,0.02513946,-0.05180358,-0.0386501,-0.01427872,-0.02174265,-0.06300203,-0.02910517,-0.02257475,-0.00120226,0.06003154,-0.02879849,0.06637559,-0.02107533,0.00864525,-0.04249316,-0.03423082,-0.0166152,-0.03638516,0.03963337,0.00394894,-0.03845065,0.01894604,-0.00034741,0.04244016,-0.0368449,0.02415514,0.02136681,-0.05390918,0.09248038,0.08059061,-0.03329229,-0.03495754,0.04368279,0.06111732,-0.07220111,0.06072504,0.00001051,0.0089318,-0.0152354,-0.01329675,-0.01367313,-0.01924314,-0.09026675,-0.17510884,-0.05695297,-0.01140836,0.0047949,0.05633347,-0.03163233,0.00859842,-0.04258796,0.02722089,0.03883844,0.10015348,-0.05857753,0.01552718,0.02545868,-0.01959271,0.05250073,-0.08498693,-0.01454382,-0.01908769,0.03913137,0.01288468,0.03227128,0.01767734,-0.11694703,-0.01265529,-0.02096145,0.13609469,-0.02158018,0.04086094,0.0049079,0.09523562,-0.03598133,0.00278924,0.00250058,-0.03820468,0.01168143,-0.04776604,-0.0213664,-0.01758684,-0.0133406,-0.07099912,-0.00467316,-0.00072165,-0.10939138,0.00180021,-0.0224434,-0.01408311,-0.01119774,-0.00368465,0.0555201,0.03949858,0.01137577,0.02634677,-0.02005492,0.02398079,-0.00812187,-0.05149495,0.027848,-0.02418251,0.06229332,-0.02264641,-0.02431652,0.0380136,-0.03227323,0.04296929,0.02702304,-0.05653414,-0.03835297,-0.03581298,-0.01523462,-0.0123633,0.10116865,0.04658639,0.08019222,-0.02382341,0.01624629,0.00840614,-0.0085455,0.01409979,0.00547232,-0.06183846,-0.02730132,0.04857153,0.12034164,0.02870147,0.07124352,0.04445581,0.01523928,-0.04711864,-0.01889659,0.00297835,0.01938939,-0.04091258,0.02584926,0.03599913,0.01189648,-0.24631374,0.07200844,-0.01272019,0.03157241,-0.01702861,-0.00884392,0.02046422,0.02371238,0.05382123,-0.03,0.05089673,0.04174644,0.03390638,0.00456796,0.05942512,0.01058989,-0.01813997,-0.02705019,0.0636138,-0.03080259,0.03263393,0.0268028,0.23002946,0.01130035,0.04200349,0.05368498,-0.00273687,0.02015318,-0.02991742,0.05071705,-0.0308941,0.00528593,0.07748531,0.02093315,-0.02318347,0.0302731,-0.10175425,0.02119137,-0.04194864,0.03220054,-0.08235196,-0.00641447,0.0297566,0.0535089,0.12805121,0.03609751,-0.02729207,-0.12292656,0.07319926,0.05285191,-0.0691211,-0.00793403,-0.07748133,-0.03125694,-0.00180899,0.01660915,0.03746271,-0.00138257,0.02755116,-0.0254061,0.00174559,-0.03718995,0.07050384,0.01739943,-0.00799905],"last_embed":{"hash":"12lpigh","tokens":509}}},"last_read":{"hash":"12lpigh","at":1753423547054},"class_name":"SmartSource","last_import":{"mtime":1753363612081,"size":22505,"at":1753423416500,"hash":"12lpigh"},"blocks":{"#---frontmatter---":[1,6],"#Lotto Wheels: Create, Make Lottery Wheeling Systems":[8,216],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#{1}":[10,17],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>Wheeling Lotto Numbers, Playing <i>Lotto Wheels</i>, Make Your Own Lottery Systems</u>":[18,19],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#By Ion Saliu, ★ _Founder of Lotto Wheeling Mathematics_":[20,23],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#By Ion Saliu, ★ _Founder of Lotto Wheeling Mathematics_#{1}":[22,23],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>":[24,113],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{1}":[26,27],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{2}":[28,29],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{3}":[30,35],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>":[36,57],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}":[38,57],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>":[58,113],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}":[60,113],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>":[114,170],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{1}":[116,128],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{2}":[129,130],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{3}":[131,162],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{4}":[163,163],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{5}":[164,164],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{6}":[165,165],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{7}":[166,166],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{8}":[167,168],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{9}":[169,170],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)":[171,216],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}":[173,176],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{2}":[177,178],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{3}":[179,179],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{4}":[180,180],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{5}":[181,182],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{6}":[183,183],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{7}":[184,185],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{8}":[186,186],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{9}":[187,187],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{10}":[188,188],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{11}":[189,189],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{12}":[190,190],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{13}":[191,191],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{14}":[192,192],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{15}":[193,193],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{16}":[194,194],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{17}":[195,195],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{18}":[196,196],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{19}":[197,210],"#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{20}":[211,216]},"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheels mathematically with combinatorics.","target":"https://saliu.com/AxiomIon.jpg","line":16},{"title":"You make lotto wheels manually or use the best lottery wheeling software to create lotto wheels.","target":"https://saliu.com/HLINE.gif","line":22},{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":122},{"title":"These are the only winning strategies applicable to lotto wheels for 12 and 18 numbers.","target":"https://saliu.com/HLINE.gif","line":161},{"title":"_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":166},{"title":"Selecting the lotto numbers to play a wheel is the most important element. The best are the most frequent lottery numbers.","target":"https://saliu.com/HLINE.gif","line":169},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":171},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":177},{"title":"_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":180},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":181},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":183},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":184},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":186},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":188},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":189},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":191},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":192},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":193},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":194},{"title":"_**Powerball Wheels**_","target":"https://saliu.com/powerball_wheels.html","line":195},{"title":"_**Mega Millions Wheels**_","target":"https://saliu.com/megamillions_wheels.html","line":196},{"title":"_**Euromillions Wheels**_","target":"https://saliu.com/euro_millions_wheels.html","line":197},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":198},{"title":"Lottery wheeling master teaches how to create the best 12-number lotto wheel, 18 numbers 6 per line.","target":"https://saliu.com/HLINE.gif","line":211},{"title":"Forums","target":"https://forums.saliu.com/","line":213},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":213},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":213},{"title":"Contents","target":"https://saliu.com/content/index.html","line":213},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":213},{"title":"Home","target":"https://saliu.com/index.htm","line":213},{"title":"Search","target":"https://saliu.com/Search.htm","line":213},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":213},{"title":"The 12 and 9-number lotto wheels assure best chances for high lottery prizes, including the jackpot.","target":"https://saliu.com/HLINE.gif","line":215}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["lotto wheels","lotto wheeling","abbreviated","reduced","lotto","systems","lottery wheel","free","software","lottery","wheeler","Powerball","Mega Millions","Euromillions","create","make"],"source":"https://saliu.com/lottowheel.html","author":null}},"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08716464,-0.06111258,0.00429901,-0.0051452,-0.06851156,0.04804432,-0.04252452,0.01983865,0.0424306,-0.00056518,0.0318324,-0.02774634,0.03322978,-0.00676074,-0.00433844,-0.00046888,0.027699,-0.01264221,-0.06987014,0.01071587,0.08349477,-0.05827532,-0.08806073,-0.05896338,0.04120691,0.02504433,-0.00856576,-0.01920319,0.02065556,-0.18462631,0.01706354,0.03504432,-0.04049289,0.00220982,-0.03689547,-0.0521286,-0.05774663,0.03900693,-0.07067919,0.02344817,0.01794433,0.04186386,-0.00478313,0.01018791,0.04521039,-0.00532704,0.01548875,0.02797591,0.05064393,0.01292186,0.00040897,-0.03744377,0.01125436,0.01884895,0.05877487,0.0142638,0.03605193,0.06354646,0.0671584,0.01169381,0.04235919,0.05525245,-0.20408672,0.05926237,0.02555908,0.02711154,0.0169004,-0.04277574,0.01671197,0.033946,0.06336922,0.01304107,-0.00117508,0.06408067,0.00300782,0.00784892,-0.0542866,-0.08574049,-0.06574199,-0.00721012,-0.02925285,-0.00787656,-0.03340036,-0.01125369,-0.05358338,0.02387608,0.05583642,0.05073398,0.05469816,-0.06636292,-0.01793208,-0.02650537,0.10508741,0.06792904,-0.08275878,0.01127155,0.01187999,0.02115357,0.01062063,0.12358536,-0.02814969,0.04317313,0.02572436,-0.02062704,0.02145467,0.01097427,-0.01860169,-0.02875265,-0.05447406,0.05022717,0.05169607,0.03412545,0.06851758,-0.04741254,-0.02352271,-0.05223515,0.00127399,0.01948024,-0.00786683,0.03148967,-0.06096566,0.02186351,0.03171371,0.00909758,-0.00076452,0.04576787,0.03479052,0.05528142,0.0210538,0.00467503,0.02235774,-0.00199917,-0.07555058,-0.02791487,0.05157997,-0.04915558,0.03719606,0.00167156,0.01215399,-0.00780993,-0.01986139,-0.05622731,-0.01342157,-0.13422845,-0.02750953,0.02140145,-0.00139668,-0.01816403,0.0145179,0.0173276,-0.04417276,-0.01843407,-0.02250944,-0.06281929,0.03927082,0.03383359,0.12169593,0.05315265,-0.01862915,0.03539791,-0.01378783,-0.0462542,-0.06657504,0.11294929,-0.04381475,-0.13119274,-0.05728628,0.05805854,0.00676481,-0.08453254,-0.05351819,0.00471444,-0.07067683,0.01671649,0.12676544,0.0061668,-0.0411225,-0.00483132,-0.08040542,-0.01304185,0.00950272,-0.03898203,-0.06637054,-0.01181005,-0.03590395,-0.06947917,-0.05934444,-0.02215153,0.03787666,0.05523781,-0.04779934,0.0175476,-0.00208229,0.00607198,-0.03744139,-0.04010666,-0.01578574,-0.03541501,0.06237939,-0.03914789,-0.02017556,0.03768552,-0.02018493,0.04843356,-0.02779224,0.02337476,0.00092446,-0.04515797,0.05908578,0.05589526,-0.0242663,-0.01246916,0.07370689,0.05135404,-0.0746187,0.05049154,-0.02092974,0.00267434,-0.00860926,-0.04415571,-0.03553195,-0.00321923,-0.08763972,-0.18234403,-0.02314194,0.01269971,-0.00982458,0.0183308,-0.02275133,0.01286723,-0.02578282,0.02672142,0.05216631,0.09186943,-0.0425403,0.01078596,0.00554017,-0.01168151,0.06432951,-0.06265968,-0.03794223,-0.00068822,0.05079526,0.03481077,0.03722989,-0.00442371,-0.12841308,0.00127204,-0.01391985,0.12538026,0.02120801,0.02346841,-0.01877019,0.1088081,-0.02175615,0.02096551,-0.02918241,-0.02728524,-0.00125539,-0.08221675,-0.00537721,-0.03700155,-0.02229721,-0.08142532,-0.01119655,-0.01721011,-0.11511832,0.03876281,-0.01309652,0.01024004,-0.0336837,-0.01835199,0.06513941,0.03570835,0.00769144,0.0421832,0.01488375,0.06233824,0.01684149,-0.05649262,0.00525235,-0.02653621,0.04813141,-0.01170129,-0.00880616,0.02459149,-0.04224865,0.01925717,0.03811089,-0.05592239,-0.05554423,-0.0570838,-0.02416115,-0.00084576,0.09057879,0.05619796,0.02648836,-0.0447236,-0.00172242,0.00108589,0.02055593,0.03813499,0.00820232,-0.0598296,-0.01576013,0.05712044,0.11796793,0.02966276,0.09520543,0.01268655,-0.00704553,-0.0230052,-0.01309606,-0.00408945,0.02869267,-0.02978432,0.00900827,0.05777826,-0.00608381,-0.25119877,0.07559402,0.04082933,0.01623653,0.00743715,0.02143145,-0.00419322,0.04169245,0.01579165,-0.00690856,0.06905799,0.04708815,0.00939391,0.02163235,0.04054976,0.02285659,0.00682482,-0.02251765,0.049192,-0.01827469,0.0132699,0.00169856,0.22803189,0.03224797,0.03535723,0.04754304,-0.02685235,0.02612856,-0.03365002,0.04029688,-0.03152259,0.02198396,0.07949481,0.01082007,-0.02716865,0.02715504,-0.08847935,0.05491097,-0.0375879,0.04564849,-0.1117883,0.01941,0.02407068,0.0269858,0.10405876,0.04080983,-0.03042704,-0.11136283,0.03758698,0.03899794,-0.06606723,-0.00789006,-0.0872681,-0.01169676,0.00300457,0.03410238,0.05016208,-0.0116246,0.04270705,-0.02361253,-0.0042031,-0.03141265,0.04887358,0.01313217,-0.01367032],"last_embed":{"hash":"1ndzxfh","tokens":108}}},"text":null,"length":0,"last_read":{"hash":"1ndzxfh","at":1753423544164},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#---frontmatter---","lines":[1,6],"size":264,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07741344,-0.05354634,0.00753896,-0.01840518,-0.09783329,0.06933767,-0.02424815,0.0188718,0.03079709,0.01174289,0.008897,-0.00982377,0.03478071,0.00556384,-0.0152133,-0.00681755,0.00768977,0.04019524,-0.07267889,0.01245885,0.06213456,-0.0715474,-0.10532374,-0.06095991,0.02838091,0.01982017,-0.04120527,-0.03023124,0.02081407,-0.23281612,0.0275452,0.03542323,-0.01406828,-0.01467307,-0.08167359,-0.03993778,-0.04063991,0.04020449,-0.09077574,0.00889537,0.00963464,0.04712198,0.04836765,-0.01077623,0.0482184,0.00926513,0.0082863,0.04624846,0.08188848,0.01348125,-0.03616471,-0.01220504,0.0062767,-0.0112324,0.03230637,0.03980533,0.03880456,0.10280701,0.03096842,0.00969533,0.03208117,0.0476773,-0.17363112,0.05551625,-0.01433443,0.00305021,0.01467374,-0.04805388,0.04511246,0.05597381,0.04803123,-0.00007712,-0.00328476,0.05875413,0.00367687,-0.01234598,-0.03329726,-0.08518187,-0.01682233,0.01279313,-0.04367176,0.01331347,-0.00373011,0.03274159,-0.03630814,0.0432986,0.01528488,-0.00265194,0.05914991,-0.09614173,0.02242833,-0.0006377,0.08286571,0.04375596,-0.00644965,0.00550454,0.00305122,0.00300728,0.03303133,0.11779811,0.01055372,0.00795613,0.02489914,-0.00975825,-0.00497198,-0.01367861,-0.03810044,-0.06074071,-0.04325072,0.07000834,0.06852107,-0.00269561,0.04491122,-0.01822536,-0.06525572,-0.03797735,-0.00452019,0.02010619,-0.00369104,0.00891737,-0.02508139,0.04288025,0.039025,-0.00867183,-0.00045382,-0.00345368,-0.00165229,0.05585899,-0.00372765,0.03562238,0.05413082,0.00088289,-0.08491088,-0.032458,0.03146847,-0.05425185,0.00773235,0.02702226,0.00533106,0.0101063,-0.0134592,-0.03504131,0.02213087,-0.11020573,-0.03688221,0.03180855,-0.0111329,-0.01580148,0.01716884,0.00387295,0.02396514,-0.05662307,-0.05960773,-0.07580835,0.0398036,0.01223678,0.11747237,0.05386297,-0.03575568,0.04887275,-0.06360739,-0.05821567,-0.05234704,0.09712368,-0.02961437,-0.08040629,-0.0321245,0.06184932,0.00600622,-0.07405872,-0.02814359,0.00024662,-0.07109904,0.06214008,0.11574621,-0.00135014,-0.09964093,-0.03036598,-0.0479219,-0.00048382,0.02263891,-0.05286088,-0.03226315,-0.01020205,-0.01685546,-0.064206,-0.02221408,-0.02709133,-0.01385791,0.06487052,-0.02429375,0.07021393,-0.02403196,0.00851587,-0.03765942,-0.03597612,-0.01519102,-0.03963567,0.0327548,0.00503433,-0.03997367,0.00461615,0.0058447,0.04311769,-0.03641471,0.00840161,0.01245812,-0.05305682,0.09734021,0.08459044,-0.03889858,-0.03023958,0.02939492,0.04874657,-0.07293714,0.05855614,-0.00553681,0.02279251,-0.01278531,-0.00967118,-0.00364318,-0.01970287,-0.09170597,-0.17422973,-0.06025955,-0.01943899,0.00957326,0.07319473,-0.02765262,0.01002314,-0.03934983,0.02580246,0.03868385,0.09913943,-0.06191991,0.02351118,0.01652336,-0.01669745,0.04921787,-0.0858473,-0.0027623,-0.01285283,0.03487068,0.00462814,0.02325558,0.0085087,-0.10720197,-0.00029666,-0.02183685,0.14048217,-0.04060538,0.04427778,0.00625264,0.09641282,-0.04198621,-0.00207439,0.01053606,-0.02797603,0.01152117,-0.05531026,-0.0265692,-0.0175584,-0.01735497,-0.05628391,-0.01187566,0.01223084,-0.09956547,-0.0021198,-0.02921324,-0.02317509,-0.01293363,-0.0047314,0.04256039,0.03371515,0.01132783,0.02875488,-0.02133396,0.01732315,-0.01647449,-0.04827,0.04103269,-0.01807027,0.07221892,-0.02795559,-0.0131872,0.03766969,-0.02232866,0.0470511,0.01583737,-0.05019948,-0.04644777,-0.04242865,-0.00842247,-0.01128729,0.10063814,0.03514693,0.09026644,-0.01742607,0.01372734,0.00290534,-0.02463616,0.01374709,0.00840789,-0.06115256,-0.05076285,0.04182955,0.11619723,0.02865392,0.06447659,0.05173942,0.0239406,-0.04731118,-0.00498695,0.01584183,0.01980166,-0.04794857,0.02256306,0.02892333,0.00916799,-0.23289798,0.06346653,-0.03149283,0.0361471,-0.02229226,-0.02503994,0.0216055,0.0295167,0.06875433,-0.03819852,0.06582096,0.04605364,0.02825168,0.00678764,0.06144486,0.01123778,-0.01856519,-0.02497053,0.06661088,-0.03868318,0.04383212,0.02702219,0.23515414,0.00742815,0.04660738,0.04620262,-0.00024143,0.01759287,-0.04299811,0.04150133,-0.0171637,0.00399573,0.06529118,0.01688903,-0.02059609,0.04222995,-0.08757544,0.0154611,-0.05421763,0.04151805,-0.07970819,-0.00930748,0.02926376,0.07212757,0.1250613,0.03318414,-0.02204823,-0.12926719,0.08579729,0.0498579,-0.07637052,-0.00690003,-0.07567359,-0.03106034,-0.00239717,0.01648093,0.02945,-0.01001994,0.01949714,-0.02009773,0.01462942,-0.03192436,0.07724721,0.0134652,-0.00459285],"last_embed":{"hash":"1jovij7","tokens":427}}},"text":null,"length":0,"last_read":{"hash":"1jovij7","at":1753423544210},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems","lines":[8,216],"size":22207,"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheels mathematically with combinatorics.","target":"https://saliu.com/AxiomIon.jpg","line":9},{"title":"You make lotto wheels manually or use the best lottery wheeling software to create lotto wheels.","target":"https://saliu.com/HLINE.gif","line":15},{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":115},{"title":"These are the only winning strategies applicable to lotto wheels for 12 and 18 numbers.","target":"https://saliu.com/HLINE.gif","line":154},{"title":"_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":159},{"title":"Selecting the lotto numbers to play a wheel is the most important element. The best are the most frequent lottery numbers.","target":"https://saliu.com/HLINE.gif","line":162},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":164},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":170},{"title":"_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":173},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":174},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":176},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":177},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":179},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":181},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":182},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":184},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":185},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":186},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":187},{"title":"_**Powerball Wheels**_","target":"https://saliu.com/powerball_wheels.html","line":188},{"title":"_**Mega Millions Wheels**_","target":"https://saliu.com/megamillions_wheels.html","line":189},{"title":"_**Euromillions Wheels**_","target":"https://saliu.com/euro_millions_wheels.html","line":190},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":191},{"title":"Lottery wheeling master teaches how to create the best 12-number lotto wheel, 18 numbers 6 per line.","target":"https://saliu.com/HLINE.gif","line":204},{"title":"Forums","target":"https://forums.saliu.com/","line":206},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":206},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":206},{"title":"Contents","target":"https://saliu.com/content/index.html","line":206},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":206},{"title":"Home","target":"https://saliu.com/index.htm","line":206},{"title":"Search","target":"https://saliu.com/Search.htm","line":206},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":206},{"title":"The 12 and 9-number lotto wheels assure best chances for high lottery prizes, including the jackpot.","target":"https://saliu.com/HLINE.gif","line":208}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07240812,-0.06432953,0.00738397,-0.01425913,-0.09461377,0.05654119,-0.02169203,0.02145358,0.00962248,0.02001864,0.01219234,-0.03422032,0.03479251,0.02325165,0.00585096,0.00451746,0.02051304,0.05346086,-0.07812911,0.00424171,0.05629258,-0.05510269,-0.10396788,-0.0557233,0.01671637,0.02501229,-0.03012201,-0.03436104,0.02226787,-0.21247303,0.03394881,0.05235624,-0.0387459,-0.01672839,-0.07526319,-0.05763704,-0.03928089,0.04011247,-0.07840173,0.00271132,0.00202423,0.05670793,0.01340323,-0.01469881,0.04606085,-0.00892992,0.01463631,0.0428021,0.0930889,0.0177454,-0.02124568,-0.02938404,0.00961365,-0.01827169,0.03636865,0.04929329,0.03045705,0.09661394,0.02514864,-0.00063194,0.03006972,0.06264459,-0.16636772,0.04765283,-0.00716867,0.00055898,0.00421183,-0.0522362,0.02888719,0.06044841,0.05514332,-0.00369679,-0.00722856,0.07410912,0.0115846,0.0075424,-0.03325357,-0.08992768,-0.0155285,0.01844725,-0.05634146,0.01086744,-0.0067059,0.0253866,-0.04397042,0.03260463,0.02184314,0.00530031,0.05863015,-0.07405028,0.01223803,-0.00024199,0.09704549,0.03419563,-0.027417,0.01575489,-0.00657122,-0.0138498,0.02739511,0.13437597,0.00475874,0.01006651,0.01870775,-0.01417307,-0.00616006,-0.00134938,-0.02801391,-0.03104723,-0.04294735,0.07482826,0.06813189,-0.00684893,0.04753382,-0.03528585,-0.055289,-0.0431176,-0.01097979,0.0021013,-0.02382169,0.00483108,-0.03244571,0.0374805,0.04185636,0.00847873,0.01113356,-0.01349834,-0.0009176,0.06136366,-0.00774286,0.03698412,0.05201134,0.01301938,-0.07573181,-0.029511,0.0349697,-0.06779741,0.01164417,0.00614255,0.0122512,0.01681786,-0.01474198,-0.02396496,0.01875399,-0.12532461,-0.02522422,0.02955993,-0.01204109,-0.0083611,0.00475902,-0.00308634,-0.00117744,-0.054911,-0.04020256,-0.07891528,0.0417539,0.02058856,0.11005002,0.05604603,-0.03527564,0.03869125,-0.05433375,-0.04990432,-0.05007809,0.12582371,-0.06257781,-0.08186758,-0.0372393,0.07283294,0.00643563,-0.06624416,-0.03551738,-0.010302,-0.06974177,0.06040981,0.13392811,0.00275166,-0.09899466,-0.02063346,-0.04156199,-0.01382366,0.02445184,-0.05878439,-0.03802153,-0.00737193,-0.01029008,-0.08142648,-0.0306289,-0.03617516,-0.00816924,0.04617104,-0.03939379,0.06600298,-0.02578315,0.00203479,-0.0451978,-0.03077115,-0.01678219,-0.04574315,0.0220947,-0.00716603,-0.03491775,0.02860869,-0.00992734,0.04517005,-0.0302238,0.00943831,-0.00007224,-0.05600394,0.09348658,0.094393,-0.04546781,-0.01279587,0.04771473,0.05588391,-0.07910968,0.05200714,-0.00740508,0.03233682,-0.00934155,-0.0059892,-0.00845827,0.00040984,-0.09426343,-0.18217254,-0.0516288,-0.00845689,0.01687948,0.06381188,-0.01895284,0.02540635,-0.01790277,0.01520425,0.04579342,0.0972859,-0.0795175,0.04652286,0.01955479,-0.00560412,0.04671774,-0.08456398,-0.01709669,-0.00127155,0.02622025,0.00297887,0.02849654,0.02321914,-0.11456718,0.01817924,-0.00510727,0.13459571,-0.02163187,0.05839036,0.01336075,0.1098211,-0.04859207,0.00070517,-0.01139908,-0.02222709,0.02656458,-0.04694312,-0.02141272,-0.02696272,-0.03564041,-0.0417782,-0.00204558,0.0072301,-0.10334764,0.00509899,-0.04146026,-0.01616227,-0.00696854,-0.00164178,0.04697557,0.02115473,0.01413949,0.03846964,0.00084782,0.01796282,-0.00361118,-0.04655159,0.02490031,-0.04466481,0.06768859,-0.02125145,-0.00355328,0.03690067,-0.0178616,0.04903055,0.02166568,-0.06929714,-0.07192925,-0.04968923,-0.0091714,-0.00250857,0.08920693,0.02254978,0.08668836,-0.0197973,0.02949394,0.00811985,-0.01586745,0.0116664,0.00710057,-0.05898453,-0.03878738,0.04850704,0.10475464,0.02912266,0.0727176,0.04129804,0.02094009,-0.0384394,-0.002347,0.01700842,0.01458579,-0.04314382,0.02585536,0.03940466,0.01325116,-0.23445733,0.05575356,-0.0196157,0.04062548,-0.02169214,-0.02680199,0.02628966,0.0017904,0.0484186,-0.0210838,0.0716489,0.04798133,0.01777427,0.01378435,0.06488789,0.02090996,0.01499015,-0.0274976,0.06724585,-0.04097631,0.0128717,0.01342908,0.24338867,0.01277774,0.04717816,0.057291,-0.01561784,0.01182538,-0.04299839,0.02916738,-0.00856179,0.0005091,0.04248419,0.02372855,-0.01725753,0.05431114,-0.08103523,0.00578818,-0.05235174,0.04665893,-0.08938491,0.01383405,0.02699933,0.07613717,0.10243282,0.00576491,-0.04612936,-0.1016707,0.06573305,0.04474539,-0.0791452,-0.00856383,-0.09106703,-0.0289988,-0.01075061,0.01082085,0.03651243,-0.01217273,0.01789192,-0.0224455,0.01093456,-0.02993969,0.06197973,0.01080302,-0.01208155],"last_embed":{"hash":"1etrm0p","tokens":140}}},"text":null,"length":0,"last_read":{"hash":"1etrm0p","at":1753423544401},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#{1}","lines":[10,17],"size":396,"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheels mathematically with combinatorics.","target":"https://saliu.com/AxiomIon.jpg","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08221624,-0.05246936,0.013094,-0.01532365,-0.09793699,0.07134923,-0.00270237,0.01994777,0.02683148,-0.01095123,-0.00177296,0.01028578,0.02924681,-0.00812517,-0.02400325,-0.00511,0.00792444,0.01326911,-0.07083805,0.02116646,0.0446526,-0.07588345,-0.10312446,-0.05401043,0.03718401,0.03473393,-0.04138894,-0.01939026,-0.00008917,-0.22290497,0.03797279,0.03782945,-0.00554541,-0.00561358,-0.05919968,-0.04514714,-0.04777644,0.06160229,-0.07289971,0.01970368,0.02296629,0.02317177,0.04641464,0.00982986,0.04973089,-0.00200144,-0.00070108,0.04419964,0.06558084,-0.00433115,-0.02929725,-0.01297882,-0.00069646,-0.00561473,0.03331253,0.03810444,0.04180239,0.10893972,0.06028439,0.01222724,0.0363185,0.04070318,-0.18045698,0.0506631,0.00146684,0.01247113,0.01508358,-0.04429951,0.05498891,0.06403076,0.05078207,0.01231129,-0.00142848,0.06510747,0.00201286,-0.02245427,-0.0351149,-0.06742575,-0.03155005,0.01661499,-0.03839754,-0.00460537,-0.01628591,0.03426385,-0.06430866,0.02828183,0.02694529,0.00917536,0.05460091,-0.08749838,0.02594007,0.0008114,0.05700801,0.05294963,-0.02719205,-0.02408032,0.00523617,0.01871703,0.03347618,0.10923007,0.03223148,0.01864133,0.03013375,-0.00602471,-0.0047914,-0.01193493,-0.04837809,-0.07277196,-0.05275505,0.06873379,0.06674428,0.01910374,0.05138232,-0.00850878,-0.05588777,-0.02633361,-0.01359271,0.03811816,0.00586045,0.02150023,-0.02138777,0.04520404,0.02921598,-0.02065663,-0.03137353,0.01385338,0.00618104,0.07139211,0.00620378,0.03180401,0.02677754,-0.00504775,-0.08403095,-0.02668256,0.03775609,-0.02584677,0.00313439,0.02805211,0.01083852,0.02752982,-0.00471361,-0.03507881,0.00815861,-0.09253901,-0.05506365,0.0210019,0.00687777,-0.02046215,0.03002755,0.02033308,0.02265724,-0.06093518,-0.07669728,-0.05171955,0.02896993,0.0067527,0.1110891,0.06496896,-0.02687062,0.05351844,-0.04696759,-0.0616948,-0.0442191,0.0898518,-0.01547924,-0.09909192,-0.03289415,0.03218712,0.0062506,-0.08412202,-0.01856261,0.01503826,-0.07839131,0.03778319,0.0951091,0.00322683,-0.09212459,-0.03301533,-0.04931801,0.0013969,0.02120155,-0.05132798,-0.03144017,-0.01223633,-0.03922114,-0.06777304,-0.03606042,-0.01828885,-0.02939112,0.06729617,-0.0194869,0.0508466,-0.02917004,-0.00601659,-0.03064627,-0.06328619,-0.01418179,-0.0332498,0.05383723,-0.01755213,-0.04827091,-0.00253816,0.00581905,0.0574173,-0.04484024,0.01416286,0.01517231,-0.0572868,0.10222673,0.06953499,-0.02265619,-0.03032429,0.02056125,0.05007334,-0.06946899,0.05591929,0.00700653,0.01283107,-0.00019846,-0.02900171,-0.0087526,-0.01344615,-0.08389618,-0.18278816,-0.05016129,-0.0221561,0.00424199,0.06878131,-0.01744359,0.00079672,-0.02672346,0.02269838,0.03262473,0.10471945,-0.05240906,0.01283938,0.00748081,-0.02577592,0.04084917,-0.09247807,-0.01159585,-0.00426403,0.03606072,0.0109401,0.0189343,-0.03556984,-0.11949863,-0.01384457,-0.03018816,0.1503388,-0.03145348,0.02776621,-0.01118563,0.08164718,-0.02129096,0.01276116,0.01293934,-0.02711915,-0.00101921,-0.05965835,-0.02800588,-0.02383318,-0.00644462,-0.08286685,-0.00748751,-0.01566733,-0.08928776,-0.00908512,-0.00923335,-0.02090985,-0.01664875,-0.01279669,0.06775646,0.0372557,-0.00727449,0.00632229,-0.02061597,0.02232965,-0.02064352,-0.04581029,0.05597523,0.00408163,0.08037192,-0.01725533,-0.01948868,0.03951034,-0.02617242,0.0388325,0.01988075,-0.04384714,-0.04112152,-0.00909168,-0.0205519,-0.00821341,0.08893549,0.0396472,0.080795,-0.00282257,0.00946133,-0.00069567,-0.01318133,0.04005999,0.01738898,-0.04781257,-0.05437683,0.04388191,0.12724793,0.02773717,0.07336402,0.02080077,-0.00180403,-0.02761859,0.00139048,-0.00145408,0.03175193,-0.03615959,0.01915037,0.03679149,-0.00212006,-0.23943205,0.0622855,-0.03571675,0.04420206,-0.04078104,-0.00500154,0.01587658,0.07112482,0.0592389,-0.04580776,0.07714283,0.04937744,0.02231057,0.00325593,0.04609801,-0.00848539,-0.03012971,-0.01079758,0.05238743,-0.02871167,0.05616543,0.02306804,0.2332478,0.01798369,0.02727168,0.02346621,0.01160398,0.02518083,-0.04007283,0.02996819,-0.01842837,-0.00424932,0.07917161,0.00100996,-0.02521344,0.01072673,-0.07539857,0.05061247,-0.0373491,0.04394953,-0.08442667,-0.01769608,0.02732501,0.04240086,0.13370611,0.06844536,0.00322024,-0.15653351,0.08334225,0.05473896,-0.06440254,-0.01408911,-0.07813655,-0.04190497,0.00597372,0.03156234,0.02283836,-0.00227869,0.02615641,-0.00432001,0.01219427,-0.05386911,0.08439519,0.00919927,-0.01913971],"last_embed":{"hash":"1nygni6","tokens":399}}},"text":null,"length":0,"last_read":{"hash":"1nygni6","at":1753423544455},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>","lines":[24,113],"size":9992,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08258385,-0.04530536,0.02467591,-0.01715792,-0.09106778,0.08232124,0.01405389,0.01006082,0.05207663,0.0065561,-0.00136304,-0.00230021,0.02244087,-0.00366403,-0.0199704,-0.00861321,-0.0001491,0.04404886,-0.04916849,0.02351872,0.07804252,-0.06022843,-0.11677997,-0.04964897,0.02137283,0.04107793,-0.03255805,-0.03954768,0.0084443,-0.20591608,0.0313684,0.02389692,-0.00773602,0.0007921,-0.06341475,-0.07299346,-0.04914562,0.06138328,-0.08285094,0.02412852,0.02382628,0.02715366,0.05690097,0.01290219,0.05403937,-0.01188258,-0.010339,0.04701782,0.03157263,-0.00477901,-0.02446553,-0.02196662,0.00331518,-0.00254802,0.02270403,0.0254146,0.04352908,0.0843408,0.05396934,0.01191604,0.05342982,0.03364857,-0.17644154,0.05899101,-0.00612461,0.01088722,0.02779074,-0.03656368,0.04768899,0.0609669,0.04048692,0.00544812,-0.0014428,0.07521281,-0.00102992,-0.01386509,-0.04292667,-0.09401312,-0.0132441,0.02614179,-0.03375246,-0.00436832,-0.02200352,0.01255217,-0.05957956,0.04142199,0.02424055,-0.02315614,0.0437382,-0.09698719,0.01829152,0.00210047,0.04635374,0.05960296,-0.01698101,-0.01404954,-0.00463026,-0.01824418,0.02461198,0.13823661,0.01477409,0.01114367,0.02654011,0.00020967,-0.00266479,-0.00619741,-0.05191788,-0.06528138,-0.04398316,0.07574578,0.0586378,0.01566341,0.03670253,-0.00322506,-0.05322788,-0.02416549,-0.02056417,0.04525975,0.02226372,0.02076021,-0.01146439,0.0332441,0.03231787,-0.01020598,-0.01436957,0.02602494,-0.01173386,0.06381299,0.0040948,0.03787759,0.04101742,-0.00793507,-0.09526083,-0.02212824,0.03558969,-0.02080441,0.01064268,0.01942789,0.00093224,0.03480936,-0.00797369,-0.01601046,0.00421177,-0.10341232,-0.05754227,0.03271004,-0.01705831,-0.00393487,0.0203544,0.00496615,0.01828495,-0.05655653,-0.07556866,-0.05701501,0.01088421,0.01226054,0.1117325,0.03097541,-0.02136922,0.0561008,-0.07174005,-0.06683801,-0.0529708,0.12734149,-0.03139814,-0.09193587,-0.03020804,0.03619864,0.01723876,-0.1010393,-0.02593682,0.00076024,-0.07066324,0.04409136,0.11059213,0.00605561,-0.06541391,-0.02632898,-0.04698507,-0.00984939,0.05294294,-0.05011394,-0.03429826,-0.00193531,-0.02813237,-0.05334716,-0.03634537,-0.02106061,-0.02059627,0.0522528,-0.03293106,0.08017691,-0.03423877,-0.01967085,-0.01642522,-0.05192412,-0.01710798,-0.03533346,0.03840861,-0.01564548,-0.03033558,0.0185557,0.00073939,0.068908,-0.02778191,0.02131676,0.01017406,-0.04326157,0.09426524,0.05027039,-0.02921938,-0.03442841,0.0064841,0.02911982,-0.05471497,0.06347279,0.01172818,0.00492038,-0.01164979,-0.0259089,-0.00921557,0.01154758,-0.08062327,-0.181283,-0.06379426,-0.00404077,-0.00010974,0.07161234,-0.01277358,-0.01576664,-0.0401057,0.02113301,0.0222779,0.09509894,-0.03946953,0.01889786,0.02262224,-0.02002542,0.04735654,-0.09809235,-0.02661214,0.00066465,0.05152607,0.01500277,0.03410352,-0.01533452,-0.13897917,-0.02932211,-0.02407454,0.12483095,-0.03646248,0.01836371,-0.02392135,0.07320684,-0.02388396,0.00642307,0.0105663,-0.0224999,-0.00069112,-0.06178129,-0.02597201,-0.01839912,-0.01070315,-0.07604662,-0.00095792,-0.03145652,-0.06387877,-0.00537725,-0.00259529,-0.01034038,-0.00902186,0.00111174,0.05883796,0.03241939,-0.00636287,0.02667749,-0.00806574,0.02899687,-0.00900036,-0.04842674,0.06747241,-0.01154184,0.0733892,-0.02980595,-0.03383993,0.03752765,-0.02234755,0.039411,0.01524105,-0.07838168,-0.0410559,-0.02205676,-0.02390116,-0.00540697,0.10558663,0.02603711,0.05407725,-0.00932488,0.00213818,-0.0042542,-0.02412975,0.02927337,0.01685956,-0.04351791,-0.04430668,0.03025495,0.10905702,0.04349736,0.0586646,0.00923697,0.02093916,-0.02229971,0.00466334,0.00781763,0.03515682,-0.05086127,0.02018039,0.01074712,-0.00168069,-0.24664289,0.07880576,-0.02914338,0.04150125,-0.03444413,-0.00803244,0.02075861,0.07179095,0.05753212,-0.03089726,0.07824726,0.0394717,0.0026546,0.02381501,0.02083274,0.00019379,0.00449136,-0.03598234,0.0543598,-0.04718725,0.05033178,0.04333988,0.25213781,0.00523548,0.05351441,0.0286562,0.01270663,0.02116428,-0.04485592,0.03415302,-0.00420077,0.00081585,0.09543104,-0.0012702,-0.02149929,0.03559968,-0.07452155,0.04549374,-0.03896077,0.03641897,-0.06927522,-0.03906624,0.03599526,0.05102117,0.11663265,0.06037155,-0.00735605,-0.14843415,0.08106862,0.04101435,-0.06227661,0.00303369,-0.08114044,-0.03750182,0.01604653,0.01919406,0.03940929,-0.00705132,0.04594795,-0.01901884,0.00739731,-0.04071784,0.07124811,0.01956266,-0.02731382],"last_embed":{"hash":"1nwcndf","tokens":144}}},"text":null,"length":0,"last_read":{"hash":"1nwcndf","at":1753423544667},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{2}","lines":[28,29],"size":467,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06096047,-0.04487372,0.00604203,-0.02219143,-0.07870755,0.06584744,0.00522236,0.0398852,0.01309531,-0.02524514,0.00362179,0.02567916,0.03251882,0.02503726,-0.01816502,0.00821364,0.0097845,-0.00872045,-0.10716493,0.036422,0.02473223,-0.0654932,-0.08232608,-0.06402992,0.04056925,0.0437859,-0.04796812,0.01044964,-0.00729846,-0.24345706,0.03208771,0.02956566,-0.02128929,-0.02289542,-0.0437296,-0.0477629,-0.05754048,0.06171093,-0.02713549,0.01234289,0.02030493,0.01683325,0.03142543,0.02588626,0.03061041,0.00053728,0.00914935,0.01347749,0.06254419,-0.02967421,-0.02345508,-0.00128037,0.02357296,0.00276348,0.0055338,0.05153692,0.00347287,0.08774811,0.06639697,0.00531212,0.02599853,0.05937968,-0.19365923,0.04027186,0.0193404,-0.00811366,0.01044538,-0.05506417,0.01857158,0.05785168,0.04981297,-0.00685629,-0.01311916,0.09185682,0.01720641,-0.00084006,-0.0583903,-0.06220721,-0.05379281,0.04100006,-0.03867234,-0.00864895,-0.01643474,0.01374365,-0.05466444,0.02110054,0.0278236,0.00708652,0.04946509,-0.06818627,0.04764822,0.00572987,0.06047852,0.0417509,-0.04914848,-0.02287666,0.02097983,0.01226776,0.02925379,0.09797331,0.02068906,0.01466287,0.03893222,-0.00387064,0.00213737,0.011261,-0.01569585,-0.04294968,-0.04599662,0.08597686,0.09690213,0.0145733,0.04743479,-0.00614625,-0.04121263,-0.01879275,0.00220976,0.03508027,0.000771,-0.00736837,-0.0372788,0.04765633,0.04628646,0.00117179,-0.07197773,-0.00925981,0.02127992,0.09337731,0.02141716,0.01947506,0.03399784,0.01318079,-0.0577897,-0.02979885,0.0344038,-0.03462308,0.01077059,0.02816795,0.05918354,0.0199907,-0.00478162,-0.04084695,-0.00522085,-0.10456832,-0.06677338,0.01420105,0.02381994,-0.01280999,0.01842108,0.02127723,0.01643879,-0.0375558,-0.0321093,-0.05393048,0.01393366,-0.00085307,0.10001352,0.08651458,-0.05301683,0.01464949,-0.0298922,-0.05323405,-0.05042413,0.08511824,-0.00901226,-0.11725867,-0.04765835,0.01655654,-0.00661621,-0.07838269,-0.00001151,0.04062467,-0.07350919,0.04436301,0.09499117,0.01248347,-0.09414206,-0.038892,-0.06153487,0.02593863,0.00367843,-0.06658208,-0.04030513,0.0061977,-0.0625696,-0.07025343,-0.03516484,-0.02610754,-0.001308,0.0753241,-0.01298963,0.05041967,-0.02530641,0.01002731,-0.05611548,-0.0532464,-0.00575056,-0.04582469,0.04716157,-0.0166856,-0.05838351,-0.01423381,0.00610856,0.03424217,-0.02963096,0.02556796,-0.00083667,-0.05580989,0.10433374,0.0775225,0.00512422,0.00446694,0.03898536,0.08349525,-0.08671039,0.04001504,0.00893947,-0.00798278,0.00511245,-0.05256826,-0.00152678,-0.01732483,-0.09281075,-0.18389659,-0.05384755,-0.03539221,-0.00168593,0.05918829,-0.0051793,0.01762889,-0.01343699,-0.00152018,0.05393434,0.08163205,-0.07377058,-0.00275769,0.01097105,-0.00214976,0.02370551,-0.0897899,-0.0009683,-0.00942401,0.02229186,-0.00373231,0.01663252,-0.0430388,-0.09595501,0.01567012,0.01154837,0.13740796,0.01898892,0.00695891,-0.0577487,0.08259336,-0.01485246,0.0380861,-0.01158489,-0.01387477,0.01392302,-0.05478306,-0.03037827,-0.03162378,-0.0149564,-0.06318805,-0.02458039,0.00734748,-0.06964366,-0.02335767,-0.0188476,-0.00772586,-0.01739663,-0.02427405,0.07439402,0.04115886,-0.01300304,0.02663984,-0.01686135,0.04540448,-0.02393366,-0.0367006,0.03549743,0.03425258,0.08562346,-0.01306575,-0.00815425,0.05741387,-0.02366601,0.04798752,0.00537236,-0.03783845,-0.04758853,-0.01926543,-0.01170964,-0.00534563,0.08668379,0.04739928,0.08639853,-0.0189934,0.02880962,0.01214583,0.01236447,0.02373079,0.00087095,-0.03935463,-0.0471736,0.03187183,0.10856245,0.00429165,0.0723981,0.02811975,-0.02169698,-0.02836134,0.03131769,-0.00359803,0.03661037,-0.01335905,0.01051846,0.01872739,0.02931167,-0.24377239,0.03403723,-0.02701502,0.05318553,-0.04513305,-0.01898511,0.03684533,0.06189785,0.05336318,-0.04707723,0.10634723,0.06231712,0.05170905,-0.01759493,0.08311141,0.01837871,-0.02916494,0.0203329,0.03694082,-0.04521075,0.0332194,0.00914185,0.22645983,0.03513373,0.00364347,0.02437116,0.00211912,0.00641769,-0.03386001,0.01398479,-0.02120374,-0.00323007,0.06128206,-0.00235054,-0.04231874,-0.00327773,-0.08044627,0.05047243,-0.04879227,0.03273298,-0.09899852,-0.00806246,0.0182889,0.03901965,0.13477476,0.05091633,-0.00780608,-0.13235177,0.0699734,0.01530715,-0.05106814,-0.03199229,-0.08276414,-0.03659515,-0.00250289,0.03420274,0.01996747,-0.03025046,0.00052602,0.01299174,0.01439546,-0.05347342,0.07121224,0.00561031,-0.03052182],"last_embed":{"hash":"1upbtns","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"1upbtns","at":1753423544779},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#{3}","lines":[30,35],"size":2237,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.03659311,-0.06435814,-0.01224244,0.02021316,-0.09143731,0.10648286,-0.00954652,-0.01764364,0.05047783,0.01749578,0.00148378,-0.01765412,0.02467667,-0.00929488,-0.04209506,0.00969872,-0.02847743,0.01409889,-0.11301126,0.00657055,0.03053012,-0.09632095,-0.07701288,-0.05824887,0.03548704,0.0414786,-0.01461045,-0.05632484,-0.0282196,-0.22462858,0.00886194,0.01755888,0.00347632,-0.06166209,-0.05490497,-0.06302119,-0.07736816,0.07239541,-0.07047331,0.02261447,0.02018371,0.03346805,0.00514409,0.01330097,0.025113,-0.01056332,-0.03908122,0.02703471,0.04036511,-0.00214978,-0.03739587,-0.0352943,0.03238775,0.04654013,0.04997819,0.03173343,0.04083395,0.07021426,0.03893355,0.00989912,0.02714325,0.08153908,-0.17168485,0.03072908,0.00810062,0.00624667,0.00042646,-0.03166228,-0.00024503,0.08175538,0.03490642,-0.0035084,-0.00551407,0.05179977,-0.0001036,0.01338036,-0.05110402,-0.08184801,-0.04094768,0.01457476,-0.04560534,0.01021838,-0.00037488,0.01766439,-0.02982262,0.00472989,0.05981625,-0.00249942,0.0266589,-0.04402546,0.02779764,0.04769489,-0.00634886,0.0298229,-0.0231028,-0.02069228,0.01817042,-0.03658649,0.02253841,0.09586876,0.03629274,0.03444374,0.05057466,-0.00522697,-0.00277094,-0.0249047,-0.03832941,-0.05024977,-0.05478232,0.03204108,0.09601197,0.0058133,0.03931662,-0.04177686,-0.06237115,-0.031525,0.01367666,0.01302275,0.008411,-0.01934023,-0.01357671,0.04051608,0.01697683,-0.01189517,-0.01299649,0.03528541,0.01284375,0.08419055,0.04288292,0.0159574,0.01886768,0.01780646,-0.08152245,-0.0287255,-0.01915731,-0.03776125,0.00994262,0.03223358,0.0179343,0.01378715,0.03699902,-0.06474644,0.04049047,-0.115858,-0.02418888,0.04070365,-0.02194349,0.00521878,0.00802701,0.01150397,-0.0134852,-0.03445135,-0.06036187,-0.05521191,0.0034394,-0.01273567,0.05387637,0.04562189,-0.04909145,0.04996436,-0.04482355,-0.05521749,-0.03659987,0.14173752,-0.01243462,-0.03585435,-0.00307247,0.03720577,-0.00204619,-0.10910345,-0.035669,0.01007306,-0.0688563,0.04297495,0.10840688,0.01987851,-0.11772643,-0.0162608,-0.00914199,-0.01918663,0.02002511,0.00988518,-0.05477193,0.00773901,-0.01303308,-0.06215131,-0.00603609,-0.01038996,-0.0193672,0.06296711,-0.02086291,0.03878271,-0.04900483,0.02603269,-0.01592846,-0.05740594,-0.01320864,-0.040838,0.03718182,-0.00575691,-0.00627128,-0.0005718,-0.01178288,0.03170837,-0.04506192,0.02888975,-0.00032792,-0.06070577,0.07968049,0.07272049,-0.02519841,-0.04257061,0.02489342,0.04054413,-0.03144823,0.0568935,0.03333509,0.03286162,0.03440942,-0.03042324,0.02722016,-0.01298627,-0.02269113,-0.16434899,-0.06534776,0.03951607,0.01986197,0.05334436,0.01564049,0.02136597,-0.0037861,0.0250788,0.03585843,0.10808403,-0.02435855,-0.0077138,0.05841243,0.00077623,0.02983484,-0.14129896,0.00698009,0.00414821,0.08303852,0.01213755,0.03391941,-0.04378768,-0.0941559,0.01416124,-0.00669787,0.13228258,-0.02896156,-0.02637293,-0.01684797,0.09132926,0.02435405,-0.00544224,0.07121468,0.04122341,0.02200785,-0.10177863,-0.05272698,-0.06626321,-0.02818165,-0.03966867,0.02192199,-0.01773538,-0.08859088,-0.00618408,-0.0462418,-0.04526486,0.0433453,-0.00956691,0.04615939,0.02571491,0.00462122,0.0577195,-0.00347367,0.05258162,-0.0103191,-0.06410793,0.02844924,-0.03025155,0.0716822,-0.00965501,-0.018951,0.04388745,-0.00659243,0.04673866,0.01459449,-0.05200563,-0.05414628,-0.02002745,-0.00501655,-0.0050171,0.05829639,0.02875027,0.06247292,-0.02616072,0.01587721,0.02486029,-0.00790924,0.06506848,0.0192188,-0.01650236,-0.06533607,-0.00081868,0.10995899,0.04542265,0.0364266,0.01248304,0.06526682,-0.02002008,0.02550031,-0.00220459,0.04879728,-0.03296313,0.02279087,0.01102762,0.01402298,-0.25177258,0.06345448,-0.04770776,0.03495843,-0.03368167,-0.02245275,0.05853475,0.05675946,0.01522602,-0.02175858,0.08096618,0.08461002,0.00413972,-0.04737397,0.06034864,-0.003011,-0.01761192,-0.05695354,0.02637352,-0.01115272,0.06132606,0.03329325,0.23777893,0.01229277,0.03461345,0.02649828,-0.00752456,0.0167739,-0.04990915,0.01306181,-0.02540296,-0.01025367,0.09366688,-0.0322984,-0.0505484,0.02031896,-0.03109073,-0.00088426,-0.02532083,0.05408046,-0.0802635,0.0011797,0.01389442,0.0358342,0.1258948,0.02478897,0.00059256,-0.12939411,0.06942955,0.05027631,-0.05464119,-0.02016891,-0.07774123,-0.0779037,-0.00630504,0.01271956,0.02248314,-0.01425351,0.01864131,0.03777421,-0.01830045,-0.07894958,0.05494362,0.02237731,-0.03488883],"last_embed":{"hash":"8288to","tokens":503}}},"text":null,"length":0,"last_read":{"hash":"8288to","at":1753423544973},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>","lines":[36,57],"size":1499,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0355587,-0.0646474,-0.01197156,0.02009189,-0.09229748,0.10579172,-0.00907582,-0.01708806,0.05141871,0.01863434,0.00055273,-0.01710063,0.02376318,-0.0089389,-0.04249717,0.01009302,-0.03006541,0.01503577,-0.11361185,0.00587173,0.03132094,-0.09491441,-0.07691965,-0.05816151,0.03452073,0.04037111,-0.01553845,-0.05734041,-0.03011047,-0.22402893,0.00778335,0.01752243,0.00374583,-0.06208259,-0.05392192,-0.06272269,-0.07896511,0.072398,-0.07197552,0.02345446,0.02121151,0.03385435,0.00604835,0.0137138,0.02453866,-0.01111609,-0.03813437,0.02685618,0.04143105,-0.00412121,-0.03587166,-0.03443446,0.03151875,0.0471685,0.04988434,0.03098374,0.04137909,0.07185578,0.03777675,0.01115259,0.02730099,0.08009657,-0.17210394,0.03134184,0.00862328,0.00578481,0.00009259,-0.03369338,-0.00028276,0.0827971,0.03489088,-0.00421637,-0.00378891,0.05075529,-0.00040424,0.01193127,-0.05066413,-0.08186337,-0.04138615,0.01417279,-0.04545452,0.00950518,-0.0003305,0.01650232,-0.03013192,0.00440812,0.05963977,-0.00321537,0.0261771,-0.04346225,0.02823516,0.04857533,-0.00521514,0.02892252,-0.02321037,-0.01999176,0.01784728,-0.03582187,0.02233501,0.09648602,0.03658716,0.03387833,0.04995484,-0.00678355,-0.00313503,-0.02392341,-0.03850171,-0.05142001,-0.05433629,0.03145785,0.09429123,0.00616991,0.03947082,-0.04093554,-0.06274594,-0.03012037,0.0127534,0.01363718,0.00687854,-0.01872334,-0.01374553,0.04026946,0.01763394,-0.01259515,-0.01265851,0.03545054,0.01285915,0.08358977,0.0425583,0.01615131,0.02008528,0.01601245,-0.0812728,-0.0293173,-0.01973094,-0.03715051,0.01090686,0.0328018,0.01897099,0.01603192,0.03768626,-0.06393769,0.0421819,-0.11381539,-0.02324464,0.04058583,-0.02269478,0.0053696,0.0077895,0.01297284,-0.01425027,-0.03487615,-0.06081072,-0.05436579,0.00311172,-0.01359441,0.05536151,0.0462455,-0.04972182,0.04926014,-0.04523152,-0.05603464,-0.03540425,0.1419796,-0.013936,-0.0339727,-0.00369417,0.03851051,-0.00357353,-0.11055332,-0.03642241,0.00896721,-0.07026523,0.04342307,0.10930639,0.01944217,-0.11869625,-0.01647975,-0.00769706,-0.01935463,0.0205548,0.01031909,-0.05335554,0.00667631,-0.0115525,-0.06079284,-0.00587044,-0.0098556,-0.01853425,0.06276557,-0.02062444,0.03946413,-0.04937486,0.02796827,-0.01602937,-0.056647,-0.01311144,-0.0419541,0.03636047,-0.00469396,-0.0068801,-0.00088529,-0.01227112,0.03270034,-0.04551348,0.02973962,-0.00047208,-0.06151395,0.07943919,0.07212348,-0.02446791,-0.04367902,0.02416899,0.04182114,-0.02998436,0.05727386,0.03531926,0.0329546,0.0321134,-0.03084452,0.02678103,-0.01374614,-0.02247726,-0.16511618,-0.06503543,0.03980162,0.02061378,0.05154289,0.01745293,0.02066327,-0.0049778,0.02464498,0.03709369,0.10970879,-0.02305551,-0.00905228,0.05872694,0.001488,0.02910112,-0.14235383,0.00795778,0.00326881,0.08241419,0.01282169,0.03212357,-0.04214069,-0.09271714,0.01427614,-0.00624816,0.1327249,-0.02900009,-0.0255201,-0.01695571,0.09047342,0.02435759,-0.00500704,0.07356683,0.04224733,0.02087039,-0.10336173,-0.05505053,-0.0662052,-0.02889208,-0.04014132,0.02107442,-0.01695568,-0.08805978,-0.00449115,-0.04496745,-0.04539386,0.04332292,-0.00736084,0.04638451,0.02622907,0.00358081,0.05753368,-0.00236732,0.05266031,-0.00978208,-0.06423194,0.02901265,-0.02957838,0.07042759,-0.00903597,-0.01942768,0.04482693,-0.00724246,0.04683959,0.01421195,-0.05223272,-0.05340068,-0.02033869,-0.00481567,-0.00610584,0.05812996,0.02924557,0.06195845,-0.02604671,0.01572142,0.02492062,-0.00776551,0.06469134,0.01792259,-0.01810595,-0.06503296,-0.00226587,0.11017579,0.04518898,0.03447675,0.01162574,0.06431222,-0.02076266,0.02422062,-0.00091291,0.04832561,-0.03354291,0.02459369,0.01080904,0.01407925,-0.250824,0.06597331,-0.04733605,0.0333115,-0.03321658,-0.02163321,0.05707564,0.05757176,0.01538175,-0.02145562,0.08113745,0.08392623,0.00385177,-0.04812781,0.05817227,-0.00314157,-0.01821911,-0.05749315,0.02745938,-0.00889193,0.0632403,0.03358432,0.23775783,0.01016872,0.03707479,0.02593952,-0.0077153,0.01709958,-0.05014167,0.01175614,-0.02618178,-0.01023028,0.09311295,-0.03174429,-0.05122278,0.02015034,-0.02996092,-0.00203964,-0.02593276,0.05456234,-0.07989015,0.00293407,0.01348989,0.03455262,0.12615046,0.02451813,-0.00056978,-0.1287211,0.06927092,0.04928752,-0.05300291,-0.02139968,-0.07826356,-0.07726154,-0.00590897,0.0117932,0.02256185,-0.01438574,0.01913769,0.0376092,-0.01824505,-0.07813434,0.05428112,0.02201784,-0.03405246],"last_embed":{"hash":"1pmqme6","tokens":501}}},"text":null,"length":0,"last_read":{"hash":"1pmqme6","at":1753423545195},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}","lines":[38,57],"size":1423,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04329738,-0.06789308,-0.00807526,-0.00172138,-0.09815385,0.10615479,-0.00491878,-0.00161181,0.03754188,0.01039875,-0.00026679,-0.00780335,0.00494842,0.00618403,-0.05665439,0.01269502,-0.00897431,0.022633,-0.08980436,0.03319533,0.04306053,-0.08407579,-0.08062145,-0.07629612,0.02473484,0.03714895,-0.017496,-0.04870525,-0.02977547,-0.23125017,0.01729732,0.01723611,0.00629276,-0.03987493,-0.0740064,-0.06343878,-0.06815911,0.05985729,-0.05665337,0.01386298,0.0154889,0.03680867,0.01261355,0.02820655,0.03630248,-0.01654563,-0.03270094,0.02380838,0.05184581,0.01578458,-0.04464603,-0.03712235,0.02785511,0.02805145,0.05812306,0.03359064,0.02407289,0.0627513,0.04211273,-0.00033411,0.03445171,0.06316881,-0.16988382,0.03243394,-0.00507288,-0.00371162,-0.00878622,-0.03150355,0.01044782,0.07168762,0.04848247,0.00198432,-0.00918052,0.06445635,0.00138607,0.01684565,-0.05280119,-0.09183896,-0.03057094,0.01921269,-0.03888426,0.00566326,0.00579786,0.00043293,-0.02329668,0.01247453,0.0489221,0.00805115,0.04041718,-0.04139204,0.03612362,0.05844745,0.01525741,0.01629206,-0.02892752,-0.01873467,0.01125133,-0.03908522,0.03534075,0.09247983,0.03829833,0.0346598,0.04126285,-0.00272541,0.00838573,-0.02349861,-0.04607237,-0.03458074,-0.0595571,0.03339829,0.10658025,-0.00537762,0.05551334,-0.0287829,-0.05233453,-0.03115216,0.02553062,0.00350605,0.01223974,-0.006202,-0.02633028,0.04331218,0.02015323,-0.01800018,-0.01164721,0.03432187,0.01947029,0.07862083,0.03326615,0.0229395,0.02876207,0.01234848,-0.06366833,-0.0282089,-0.00077642,-0.04039025,0.01984971,0.04219722,0.014564,0.01436067,0.02650587,-0.06996915,0.03641459,-0.11927913,-0.0429093,0.01929022,-0.02463065,0.01105897,0.00856736,0.00852013,-0.01146025,-0.06169935,-0.06576217,-0.05396885,0.01973974,0.00631745,0.06115752,0.06394492,-0.03683128,0.03815285,-0.06221646,-0.0651558,-0.03840637,0.12517311,-0.02010785,-0.06944837,-0.01043323,0.03513566,-0.00697299,-0.11115119,-0.03342707,-0.003806,-0.06974558,0.05165935,0.1058044,0.00933342,-0.10956004,-0.01801079,-0.02147762,-0.01257033,0.02257299,-0.00827534,-0.06881026,0.01290411,0.00009278,-0.06178711,-0.00431477,-0.01397539,-0.00808904,0.04322781,-0.01287005,0.03411009,-0.05016904,0.03123059,-0.01791402,-0.05595146,-0.01701641,-0.04746022,0.04130511,-0.01344786,-0.00179859,-0.00669893,-0.00995444,0.02875551,-0.01690125,0.03760697,-0.01590055,-0.0593996,0.08707439,0.06735313,-0.02470498,-0.03425907,0.03159084,0.05098739,-0.05438597,0.04221312,0.0224344,0.03245544,0.02667257,-0.03745995,0.03121523,-0.00457872,-0.04263368,-0.17607144,-0.05989427,0.02603814,0.0199613,0.07936866,0.01638598,0.02978118,-0.02417994,0.04207761,0.04698916,0.0953303,-0.02848888,-0.00328301,0.0502152,-0.0164801,0.04158126,-0.14333935,-0.01440751,-0.00022413,0.06953118,0.01635463,0.04391898,-0.02957327,-0.09343433,0.00309994,-0.01468765,0.13797498,-0.03626924,-0.01727422,-0.01514354,0.10115085,0.0019391,0.00554654,0.06888978,0.03592652,0.02101473,-0.08907442,-0.03914252,-0.07449047,-0.03005929,-0.03417582,0.00566932,-0.02527835,-0.08870514,-0.02983915,-0.03703897,-0.05086302,0.02178915,-0.01686139,0.0574527,0.02286826,-0.00715082,0.05402695,0.00343705,0.04194435,-0.00724819,-0.06697429,0.01732169,-0.02113367,0.09038053,-0.03083087,-0.0131544,0.03915742,-0.00854742,0.04066409,0.01490639,-0.05201187,-0.05024203,-0.01069152,-0.01962507,-0.00939137,0.0570869,0.02219673,0.06330173,-0.02165535,0.01350705,0.03315324,0.00142185,0.05920809,0.01945289,-0.02834446,-0.0592728,0.00088647,0.09729338,0.04049984,0.04873966,0.02775345,0.05494902,-0.02473677,0.01236301,0.0105245,0.04167061,-0.03880547,0.0241508,0.0077295,0.01574701,-0.25488967,0.06238545,-0.04402242,0.03502411,-0.04842357,-0.03194078,0.04720948,0.0635995,0.03079855,-0.01805898,0.0965931,0.09517071,0.01436457,-0.03832774,0.05637303,-0.00721331,-0.02569336,-0.03939095,0.0307506,-0.01611937,0.0577102,0.04487268,0.22354953,0.00813838,0.02474548,0.02709532,-0.00349298,0.02513243,-0.05384413,0.00496737,-0.01973127,-0.00379098,0.08341707,-0.02988727,-0.04949625,0.03723273,-0.03872598,0.00305835,-0.03362653,0.03552637,-0.08295169,0.00808681,0.02393179,0.0458096,0.12672436,0.03619867,-0.00236651,-0.1206347,0.0891007,0.05065284,-0.04948319,-0.0146996,-0.06657873,-0.07311589,-0.02652308,0.01229711,0.03943114,-0.00618626,0.02830628,0.0177395,-0.00676626,-0.05452217,0.06999546,0.02304113,-0.02750537],"last_embed":{"hash":"1s2tzm8","tokens":505}}},"text":null,"length":0,"last_read":{"hash":"1s2tzm8","at":1753423545398},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>","lines":[58,113],"size":5609,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04169047,-0.0703812,-0.00926414,-0.00087899,-0.09550153,0.10608035,-0.00544143,-0.00291418,0.03818647,0.0116175,0.00162076,-0.00657167,0.0057124,0.00321255,-0.0555873,0.01595818,-0.01266802,0.02222341,-0.09133625,0.03039961,0.04656038,-0.08509826,-0.08060621,-0.07399954,0.02339512,0.03848664,-0.01843419,-0.05030997,-0.03000645,-0.23160222,0.01722014,0.01846592,0.0056208,-0.04095244,-0.07528361,-0.06369134,-0.07080228,0.05957536,-0.05832291,0.01548887,0.0157801,0.03704259,0.0135553,0.02782527,0.03471949,-0.01894464,-0.03637762,0.02093469,0.05122196,0.01566937,-0.04505501,-0.03670697,0.02660679,0.0282684,0.0570368,0.0327414,0.0265315,0.06283544,0.04062293,0.00247661,0.03343258,0.0602874,-0.17218001,0.03176409,-0.00408027,-0.00365669,-0.01109713,-0.03231608,0.01070886,0.07369647,0.04917235,0.00509438,-0.00721727,0.06478579,0.00043097,0.0172342,-0.05143228,-0.09131533,-0.0298642,0.0207429,-0.03779897,0.00645938,0.00459206,0.00041232,-0.02343558,0.01026317,0.04911718,0.00495651,0.03853474,-0.04219773,0.03330508,0.05940961,0.01393519,0.01541202,-0.02882698,-0.01937576,0.01021519,-0.03857418,0.03755818,0.09184942,0.04030573,0.03191264,0.04218183,-0.00325991,0.00865175,-0.02442083,-0.04557826,-0.03492724,-0.05901303,0.03404552,0.10449903,-0.00826637,0.05408228,-0.02663854,-0.05299752,-0.03056459,0.0240048,0.00442772,0.0105791,-0.00822728,-0.02477728,0.04241522,0.02094144,-0.01779215,-0.0111305,0.03462147,0.01801696,0.07979359,0.03255283,0.0219038,0.03012287,0.01073086,-0.0630059,-0.02915035,0.00001439,-0.03976841,0.01929171,0.04476466,0.01422464,0.01651284,0.02707065,-0.06969356,0.03964004,-0.11589865,-0.0434272,0.02322882,-0.02385323,0.00887842,0.00737194,0.00950504,-0.01024478,-0.06102451,-0.06576276,-0.05336082,0.0182592,0.00739921,0.06265768,0.06442682,-0.03606504,0.03839199,-0.0637049,-0.06669132,-0.03715763,0.12686381,-0.02181523,-0.069403,-0.00722821,0.03699552,-0.00608899,-0.11156473,-0.03169374,-0.00348944,-0.06841724,0.05269149,0.10523168,0.00834311,-0.10874878,-0.0167378,-0.02159839,-0.01022772,0.0220254,-0.00955701,-0.06851109,0.01236848,0.00135546,-0.05931183,-0.0044985,-0.01136008,-0.0094648,0.03924362,-0.01378092,0.03628066,-0.05096279,0.03196708,-0.01722986,-0.0552224,-0.01560637,-0.0469887,0.04048071,-0.01049425,-0.0014557,-0.00717011,-0.00953837,0.0276418,-0.0174908,0.0385224,-0.01707864,-0.05777678,0.08414888,0.06807356,-0.02270964,-0.0332463,0.02794155,0.05265416,-0.05052393,0.04182787,0.02290528,0.03324858,0.02632339,-0.03608788,0.03093618,-0.00620827,-0.04125273,-0.17762785,-0.05987214,0.0251709,0.02073858,0.0777516,0.01953361,0.0283935,-0.02278681,0.04123404,0.04673789,0.09714498,-0.03096942,-0.00423457,0.05357555,-0.01717847,0.03995196,-0.14416157,-0.01470852,-0.00227859,0.06934393,0.01643227,0.04247599,-0.02895383,-0.09349471,0.00383294,-0.01446457,0.13935266,-0.035142,-0.02022766,-0.01325944,0.10033949,0.00191177,0.0061066,0.07195247,0.03587675,0.02080961,-0.09097087,-0.04119509,-0.07437558,-0.03104442,-0.033124,0.00558152,-0.02419795,-0.08764666,-0.02974284,-0.03791701,-0.05033402,0.01949012,-0.01499594,0.05875681,0.02263281,-0.0092601,0.0537613,0.00476734,0.04274965,-0.00782301,-0.06725159,0.01921787,-0.02052943,0.08959368,-0.02918363,-0.01280025,0.04002018,-0.00934119,0.04146024,0.0146509,-0.05364935,-0.04999883,-0.01052736,-0.01985927,-0.00835826,0.05844408,0.02234945,0.06241495,-0.02004136,0.01261795,0.03148812,0.00385153,0.05949455,0.01716111,-0.02707977,-0.05617527,0.00194107,0.09810926,0.03962591,0.04491067,0.02660404,0.05445936,-0.02432928,0.0136089,0.01079559,0.04230068,-0.0359399,0.02500784,0.0078129,0.01413154,-0.25458151,0.06527072,-0.04419313,0.03517458,-0.04831732,-0.03354735,0.04732865,0.06396011,0.02954051,-0.01878348,0.09522547,0.09758859,0.01598299,-0.03960369,0.05575956,-0.00847899,-0.02468195,-0.04273395,0.03038132,-0.01821244,0.05865505,0.0455497,0.22410896,0.00864786,0.02293239,0.02866412,-0.00578604,0.02533876,-0.05528451,0.0038728,-0.0198226,-0.00471495,0.08210984,-0.03052969,-0.05021618,0.03839395,-0.0377573,0.00251485,-0.03358209,0.03435783,-0.08330736,0.00634754,0.02529657,0.04503885,0.12803912,0.03734865,-0.00364869,-0.11941157,0.08749641,0.05103733,-0.05014957,-0.01464922,-0.06522143,-0.07480543,-0.02339248,0.01195517,0.03761211,-0.00651136,0.02742821,0.01606,-0.0069671,-0.0514436,0.06803393,0.0237657,-0.02673781],"last_embed":{"hash":"cmflji","tokens":502}}},"text":null,"length":0,"last_read":{"hash":"cmflji","at":1753423545641},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>#<u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>#{1}","lines":[60,113],"size":5532,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07748959,-0.08097839,0.0040215,-0.0399356,-0.10258753,0.07693029,-0.00293331,-0.00338594,0.04952161,0.0005416,0.00727387,-0.00050363,0.03645401,-0.00267322,0.00536085,-0.00283577,0.00767929,0.02626191,-0.09528555,0.02961411,0.06973077,-0.07597663,-0.07348845,-0.08758444,0.03959351,0.05367811,-0.03989073,-0.06394789,-0.01297999,-0.25801861,0.02148949,0.05693301,-0.00649294,-0.03638759,-0.0554086,-0.04202866,-0.05316625,0.03159469,-0.08629309,-0.00037866,-0.01489148,0.04353909,0.05121229,-0.02336828,0.00670409,-0.00774511,0.00640521,0.05132659,0.07473355,-0.0094618,-0.0147134,-0.01598632,0.01327556,0.03374127,0.04272518,0.04259139,0.00292838,0.07690359,0.04379844,-0.00003506,0.05387293,0.07861333,-0.16881652,0.02843456,0.00548047,0.04464258,0.01432011,-0.07233569,0.01949337,0.0796584,0.04445664,0.01659877,-0.00721178,0.06524636,0.01268434,0.00694296,-0.04710618,-0.07840834,-0.05852081,0.05403298,-0.04113463,0.01584008,-0.03175535,0.00284882,-0.04598603,0.01445213,0.05634297,0.00087311,0.02879173,-0.06053098,0.02633408,0.02763454,0.05621649,0.04219357,-0.00797683,0.00205607,0.03904117,-0.0223024,0.02980926,0.11655547,0.02255789,0.01648808,0.00680717,0.01533143,-0.00689624,0.00098243,-0.03056298,-0.03877775,-0.07144804,0.05204994,0.0562162,0.01538752,0.03828193,-0.01616634,-0.06668936,0.00690394,-0.01066011,-0.00664202,0.01446699,0.00127995,-0.04987978,0.01641911,0.03898578,-0.01148235,-0.04986219,0.02782552,0.00906046,0.06365517,0.01352339,0.02260214,0.04040372,-0.01991113,-0.09283034,-0.01852337,0.012636,-0.05155274,0.0303546,0.03566536,-0.00247277,0.01214427,-0.02436178,-0.04808056,0.04818511,-0.08570627,-0.00962787,0.04868053,-0.02524295,-0.00011627,0.02405708,-0.00392744,-0.01634539,-0.04498028,-0.05427456,-0.05217273,0.01441362,0.01723292,0.13516983,0.01030265,-0.06945264,0.03249913,-0.03752296,-0.05163286,-0.03791022,0.10472051,-0.01111939,-0.07040572,-0.02621659,0.04877335,-0.010344,-0.08789366,-0.03877799,0.02081935,-0.05799434,0.02322277,0.09793573,0.00858348,-0.1147827,-0.01938658,-0.03406898,-0.01501467,0.02527328,-0.05412084,-0.04342945,-0.00621189,-0.03575188,-0.08183652,-0.00024543,-0.01066774,-0.01620921,0.05473313,-0.01765995,0.07489665,-0.02462252,-0.00974207,-0.0164215,-0.03336794,-0.01623317,-0.03538803,0.05546216,-0.03246739,-0.04664744,0.0123581,0.04168474,0.0374148,-0.04929334,0.02273826,0.01047686,-0.06737766,0.07905187,0.05156622,-0.03056045,-0.02798549,0.05091259,0.05470899,-0.06199343,0.06744564,0.02431661,0.02210584,-0.02147785,0.00380809,-0.02254313,-0.00797563,-0.05727739,-0.16974109,-0.0501884,-0.00649681,0.00956844,0.05622645,-0.02270113,-0.00418073,-0.00060339,-0.00768723,0.05206594,0.08565202,-0.05607512,-0.00692467,0.02908213,-0.00509213,0.04209842,-0.1167871,-0.01308827,-0.06067773,0.05344086,0.00547989,0.03496905,-0.01631315,-0.11958385,-0.01140405,-0.01655789,0.12397008,-0.02291363,0.01538765,-0.0260971,0.0864901,-0.02276401,-0.00437097,0.00702176,-0.02082598,0.01189785,-0.06355421,-0.04649584,-0.00171989,0.00344282,-0.05984216,0.00909732,-0.03740564,-0.10133467,0.02047079,-0.00515156,-0.03148402,-0.00091854,0.01616558,0.06452979,0.06730566,-0.00949719,0.01087068,-0.0184472,0.04847296,-0.02987532,-0.0675333,0.05287722,0.00544221,0.07943181,-0.00614646,-0.01191692,0.03420248,-0.0125784,0.04778537,0.02986616,-0.01793931,-0.04768771,-0.02547429,-0.04004574,0.00371523,0.05725453,0.03553635,0.06762597,-0.05228649,0.00907156,0.0093886,0.0174064,0.02652807,-0.0002246,-0.01912352,-0.05327607,0.02403752,0.12754269,0.04664206,0.04304415,0.04064738,0.04548336,-0.0085842,0.00584873,0.02434587,0.03579466,-0.03016365,0.02227582,0.0266731,0.01208158,-0.23139,0.05008857,-0.00956622,0.04295986,-0.03505645,-0.02694167,0.01140566,0.03565153,0.03018205,-0.02922306,0.07222409,0.05154027,0.00563534,-0.01138054,0.05370472,-0.01133527,0.01512714,-0.00889956,0.0692805,-0.04172013,0.05686826,0.03048675,0.24973428,-0.00979768,0.05132331,0.03694288,0.01377931,0.02180718,-0.03422951,0.05288922,-0.01707163,-0.000943,0.10101223,-0.01587717,-0.01002307,0.01922755,-0.05099666,0.03642045,-0.03898275,0.04264708,-0.07158075,-0.01744899,-0.01162495,0.0270552,0.11075836,0.04350467,-0.00187258,-0.12708776,0.0585296,0.04698558,-0.06759682,-0.01792353,-0.07481436,-0.04882826,0.02359053,0.0249083,0.04706915,-0.01054371,0.00332721,0.00680557,0.00231531,-0.0468498,0.07834864,0.02337815,-0.03343779],"last_embed":{"hash":"1jv6kdc","tokens":460}}},"text":null,"length":0,"last_read":{"hash":"1jv6kdc","at":1753423545895},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>","lines":[114,170],"size":6858,"outlinks":[{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":9},{"title":"These are the only winning strategies applicable to lotto wheels for 12 and 18 numbers.","target":"https://saliu.com/HLINE.gif","line":48},{"title":"_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":53},{"title":"Selecting the lotto numbers to play a wheel is the most important element. The best are the most frequent lottery numbers.","target":"https://saliu.com/HLINE.gif","line":56}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07697865,-0.08072621,0.00506577,-0.04111378,-0.10072976,0.07648266,-0.00004093,-0.0040207,0.05168048,-0.00140879,0.00597217,-0.00011877,0.03913636,-0.00090163,0.00502976,-0.00563549,0.00730523,0.02783635,-0.09805842,0.02732183,0.07090633,-0.07271662,-0.07556729,-0.08855578,0.04076795,0.05148774,-0.0425163,-0.06704686,-0.01713664,-0.25625032,0.02012686,0.05390345,-0.00695263,-0.03761469,-0.04983031,-0.04325272,-0.05011232,0.03247411,-0.08891534,0.00065811,-0.01317554,0.04604257,0.05140449,-0.02504583,0.00550041,-0.00959087,0.00519889,0.05208999,0.07047588,-0.00734981,-0.01341009,-0.01268859,0.00947874,0.03474643,0.03818573,0.04041656,0.00474893,0.07861917,0.04235575,0.00312929,0.05594628,0.07795083,-0.16719279,0.03015512,0.00404154,0.045222,0.01182011,-0.07204742,0.01944888,0.08317909,0.04975114,0.01883681,-0.00661023,0.06957681,0.01146071,0.00729188,-0.04615502,-0.07656476,-0.05686439,0.05947847,-0.04345556,0.01638219,-0.03143043,0.00076763,-0.04737204,0.01346072,0.05533353,-0.00094775,0.03279591,-0.05737718,0.03266594,0.02821417,0.05483867,0.04361051,-0.00341505,-0.00203126,0.03718211,-0.02618634,0.02960072,0.11801373,0.02216485,0.01566996,0.00368497,0.01425782,-0.00739667,0.00207244,-0.02948693,-0.03897803,-0.07390734,0.05261105,0.05078642,0.01593305,0.03365428,-0.01610995,-0.06358173,0.00787421,-0.00692097,-0.00855881,0.01228559,0.00141399,-0.05330819,0.0151018,0.04001087,-0.0117891,-0.04660575,0.02998754,0.00545471,0.06603291,0.0134287,0.02566234,0.0408061,-0.02311384,-0.09079074,-0.0208426,0.0120333,-0.05181427,0.02946767,0.03100521,0.00100873,0.01244413,-0.02499434,-0.04394803,0.05534402,-0.08507488,-0.01033258,0.04846464,-0.02503704,-0.00075079,0.02374552,-0.00397685,-0.01257861,-0.04694736,-0.05404584,-0.05248912,0.01089237,0.02194559,0.13572359,0.00558232,-0.06795162,0.02971493,-0.03855851,-0.05128445,-0.03589482,0.10696115,-0.01725245,-0.06908189,-0.02395382,0.04603884,-0.00918352,-0.09318257,-0.03768114,0.02053092,-0.05851958,0.02404965,0.09640478,0.00884709,-0.1142069,-0.0209546,-0.03320032,-0.01487649,0.02825219,-0.05237519,-0.04232289,-0.00620825,-0.03718743,-0.07510586,0.0027306,-0.01349409,-0.0149032,0.05161272,-0.01853667,0.07943141,-0.02706016,-0.01209456,-0.01529536,-0.03311742,-0.01773763,-0.03956725,0.05758484,-0.03343683,-0.04386288,0.01305463,0.04298245,0.0356365,-0.04430589,0.02002708,0.01131652,-0.06487568,0.07596193,0.04806623,-0.02653403,-0.0311545,0.04914229,0.05605346,-0.06274354,0.06247482,0.02897913,0.02308636,-0.02186043,0.00531172,-0.02286607,-0.0050206,-0.05015392,-0.17220604,-0.05014549,-0.00813688,0.0070062,0.05549335,-0.02213149,-0.00563608,-0.00009925,-0.01041395,0.05138969,0.08581911,-0.05409251,-0.0074048,0.0255981,-0.00434266,0.04676558,-0.11739656,-0.01002498,-0.062403,0.05369096,0.00538766,0.03350671,-0.01728256,-0.12219357,-0.00969795,-0.01524468,0.11814856,-0.02540016,0.02076623,-0.02997104,0.08226313,-0.02406472,-0.00539166,0.00653252,-0.02127729,0.01075547,-0.06706422,-0.05166792,-0.00033685,0.00184443,-0.05786477,0.00995591,-0.03589697,-0.10131249,0.019175,-0.00270985,-0.0295322,0.00313789,0.0170413,0.06307132,0.06796587,-0.01022147,0.00712451,-0.01515183,0.05172247,-0.03261108,-0.06749903,0.05384166,0.00509152,0.07761276,0.00089143,-0.01656963,0.03358131,-0.01104622,0.0505573,0.03035243,-0.01401494,-0.04508839,-0.02560964,-0.03867626,0.00184654,0.05760446,0.03568466,0.06671949,-0.05640652,0.01013351,0.00840518,0.01263808,0.02602731,0.00069801,-0.01667793,-0.05112655,0.0257006,0.12722458,0.04810847,0.03896545,0.03969131,0.05110687,-0.00739674,0.00615217,0.02420867,0.0345358,-0.02907628,0.02102794,0.02272923,0.0122523,-0.22968665,0.04923528,-0.01124643,0.04286736,-0.03632953,-0.02760181,0.01207547,0.03560084,0.027832,-0.03117195,0.07054698,0.05300865,0.00634589,-0.01234796,0.04921417,-0.0113874,0.01572417,-0.00787313,0.06912158,-0.03797212,0.058998,0.03248918,0.2541908,-0.01419204,0.05597604,0.03807209,0.01463673,0.02466086,-0.03539663,0.05457971,-0.01918721,-0.00199404,0.10266946,-0.02069027,-0.01014027,0.02236669,-0.04968745,0.03442224,-0.03723254,0.0381411,-0.06820474,-0.01865416,-0.01846094,0.02597044,0.11419358,0.04459021,-0.00289611,-0.12528495,0.0572819,0.0473856,-0.06692641,-0.02147428,-0.07516017,-0.05000724,0.02766974,0.02487933,0.04831466,-0.01341187,0.00126544,0.00689558,0.00252603,-0.04050019,0.0764671,0.02183966,-0.03736471],"last_embed":{"hash":"1kddl9o","tokens":444}}},"text":null,"length":0,"last_read":{"hash":"1kddl9o","at":1753423546139},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{1}","lines":[116,128],"size":1831,"outlinks":[{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08019317,-0.08110879,0.01278106,-0.03305645,-0.09878065,0.06355653,-0.01853251,0.02241875,0.01982508,0.00595553,0.0222271,-0.01573707,0.03294226,0.02697384,0.00220209,0.01212033,0.03199617,0.02971266,-0.07168279,0.02780562,0.05631126,-0.05235278,-0.09192398,-0.08494751,0.0129735,0.03084144,-0.03853583,0.01926129,0.02676298,-0.21082769,0.02449112,0.03864486,-0.04651702,-0.02145841,-0.06303266,-0.05494958,-0.03694421,0.0191486,-0.09097638,0.00676116,-0.00652462,0.04978838,0.02698502,-0.02834621,0.04157045,-0.02177161,0.02587416,0.05394504,0.07391936,0.01311155,0.00567881,-0.01593954,0.01794638,0.02231148,0.0218793,0.03171594,0.04777853,0.06702514,0.0628296,0.02480595,0.05526225,0.06738265,-0.18522,0.0457463,0.00877255,0.02507828,0.02658004,-0.0328423,0.02599176,0.06772724,0.05510916,-0.00215526,-0.00566945,0.07676185,-0.00034084,0.00138553,-0.03646712,-0.09165848,-0.05938014,0.03282303,-0.05043206,0.00948031,-0.00554683,0.02242832,-0.03056334,0.02658335,0.03463848,0.01966448,0.06407096,-0.05943641,0.02771877,-0.00658134,0.06522333,0.02187648,-0.04355843,0.01792251,0.01135839,-0.00983327,0.03468144,0.13048543,0.02246173,-0.02910761,0.00462757,-0.01585958,0.00850384,-0.00396453,-0.04702403,-0.02575263,-0.04973407,0.06453491,0.06615541,0.01114973,0.02652803,-0.01488921,-0.06512921,-0.03115815,-0.00774177,-0.01207819,-0.00910221,0.00127195,-0.08295833,0.03936182,0.04608442,0.00608316,-0.01139496,-0.01829526,0.03470151,0.05317354,0.00686886,0.00923029,0.04276013,-0.00585955,-0.08366102,-0.04720453,0.05019431,-0.0292496,0.0158738,0.03629071,-0.0095788,0.02336299,-0.00003715,-0.03424718,0.02917622,-0.12324539,-0.02693828,0.01895731,-0.0194332,-0.01312332,-0.01566334,-0.00506536,0.01209768,-0.06062757,-0.02672677,-0.04536949,0.03118642,0.03570142,0.09925533,0.05160778,-0.07641805,0.0214709,-0.05518011,-0.02771475,-0.03673226,0.13388965,-0.0350726,-0.09093437,-0.01306116,0.04139017,-0.00219908,-0.10805022,-0.06017744,0.01705645,-0.09611673,0.02752908,0.12334006,-0.01024871,-0.08618328,-0.02306577,-0.0563858,-0.02522719,0.06448068,-0.02982022,-0.02001625,-0.0184585,-0.03386248,-0.09685618,-0.03880158,-0.02999973,-0.00375226,0.04265912,-0.00577806,0.05795434,-0.02191493,-0.01658264,-0.03025672,-0.0332358,-0.02686668,-0.02766627,0.06147169,-0.04156187,-0.01204003,0.02179854,-0.00178057,0.05112706,-0.01474874,0.01793662,-0.00878762,-0.03698862,0.08281425,0.03850805,-0.03384539,-0.02894567,0.0660108,0.06389288,-0.0992099,0.05118796,0.00187273,0.03896394,-0.00659586,-0.00252505,-0.00395435,0.02934394,-0.0826189,-0.17572266,-0.06254131,-0.02523879,0.03495008,0.06670997,-0.01742326,0.0344639,-0.00809935,0.00659437,0.0662406,0.09431183,-0.07513292,0.02199272,0.03462664,-0.00977551,0.04415272,-0.1201789,-0.0376561,-0.03655369,0.02680067,0.00551365,0.03650276,-0.02268496,-0.12706114,0.01744049,-0.01863544,0.12772615,-0.01218357,0.00975434,-0.01899152,0.09185284,-0.05879668,0.00737579,0.00558179,-0.01726759,0.01206469,-0.05240747,-0.02940702,-0.03955538,-0.03713885,-0.06788889,-0.00108513,-0.01017845,-0.09313672,0.03037534,-0.02076714,0.00903322,-0.0042669,-0.01774267,0.05363514,0.03122015,0.00244953,0.03844763,-0.00768354,0.04860689,-0.00885487,-0.03529087,0.03349996,0.00046071,0.08704275,-0.00852088,-0.02198294,0.01356235,-0.01304929,0.06098319,0.023786,-0.04777631,-0.05720997,-0.02121878,-0.01990772,-0.00070157,0.06067009,0.02218543,0.08226672,-0.01945318,0.03596592,0.03531527,0.01267221,0.05694528,0.01757465,-0.05627706,-0.00710623,0.04896248,0.11992031,0.03502145,0.03382716,0.01817248,-0.00465958,-0.0206741,0.01705277,-0.01246783,0.02222956,-0.03220125,0.01131997,0.00944337,0.04223872,-0.23033874,0.04949228,-0.0211859,0.02037351,-0.04037325,-0.04287068,0.00518366,0.03465698,0.04482921,-0.01133428,0.09651648,0.03942513,0.06093064,0.0007556,0.0307447,-0.00836874,0.03889894,-0.00596392,0.08107783,-0.01077817,0.0149418,0.00140245,0.24192639,0.00571892,0.05658442,0.02749804,-0.03714629,0.02514344,-0.05127278,0.01483849,-0.00264651,0.00660202,0.06947224,-0.00958206,-0.04086834,0.02882809,-0.07807878,0.02478049,-0.03932794,0.03267158,-0.0622755,0.03629047,0.00531008,0.03233379,0.09938146,0.02091867,-0.00830033,-0.1033172,0.06488736,0.03405838,-0.09121922,-0.01216545,-0.09181064,-0.0465191,0.02333145,0.03301311,0.01524155,-0.01893701,-0.00124422,-0.02785178,0.0116863,-0.01154443,0.04959486,0.01401067,-0.01849473],"last_embed":{"hash":"xguidq","tokens":134}}},"text":null,"length":0,"last_read":{"hash":"xguidq","at":1753423546301},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{2}","lines":[129,130],"size":334,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06531776,-0.08320085,0.00315915,-0.01772092,-0.10480098,0.06728097,-0.00180546,0.00935085,0.03334633,0.007282,-0.00861337,-0.01583732,0.02958984,0.0065708,-0.0000515,0.00849022,0.00746073,0.02716349,-0.09486596,-0.00011782,0.05046492,-0.06619618,-0.07886297,-0.0688241,0.02132726,0.03993443,-0.02999598,-0.01592473,-0.00225085,-0.247821,0.01634322,0.02837216,0.0031757,-0.02896366,-0.05982296,-0.05236835,-0.04789242,0.0643684,-0.08152194,-0.00166896,-0.00524854,0.04951427,0.02284228,-0.00276116,0.02432285,-0.01431008,0.00512951,0.04225526,0.06197906,0.00608646,-0.01831854,-0.04383967,0.01411519,0.02541372,0.02541197,0.03692608,0.01997191,0.09155656,0.0375741,0.03448952,0.04346816,0.09267461,-0.16486722,0.02173579,0.00526478,0.02563498,-0.00507609,-0.05345405,0.02701801,0.09527764,0.05788693,0.0138235,-0.0089118,0.07813831,0.00472777,0.013576,-0.01740429,-0.09787267,-0.04635888,0.02575992,-0.04936216,0.01127438,-0.01521604,0.01798235,-0.05065951,-0.01215461,0.0310547,0.01602453,0.0505439,-0.05377297,0.02616966,0.02729287,0.05045894,0.02598248,-0.01059853,-0.00781026,0.03301193,-0.01958213,0.01678708,0.10115601,0.01907001,0.02480127,0.03130295,0.00711617,-0.00008296,-0.00581902,-0.03512257,-0.04688096,-0.06510123,0.04168482,0.09173178,0.00603161,0.02308138,-0.01165571,-0.05302592,-0.01281977,0.01592573,0.00950894,0.00656692,-0.00013718,-0.05595181,0.02258235,0.05242934,0.00013012,-0.02738445,-0.00764428,0.00835305,0.07777193,0.00152785,0.01116355,0.01684187,-0.00012321,-0.07137578,-0.00279806,0.00273401,-0.04127629,0.01997856,0.01643304,0.01095871,0.01214722,-0.0021015,-0.05305863,0.03941066,-0.11281256,-0.01952056,0.02754558,-0.00120296,0.00461121,0.01469987,-0.01096963,0.00951999,-0.04604816,-0.04646372,-0.04150604,0.04674777,-0.00882581,0.09375457,0.03573326,-0.04604872,0.03111399,-0.06566547,-0.0254266,-0.05644887,0.10831764,-0.0241191,-0.07265376,-0.00650715,0.05100255,-0.01128917,-0.11102816,-0.01457464,0.02017079,-0.06445384,0.04565731,0.10113259,0.00475843,-0.13220942,-0.00110078,-0.01646073,-0.00378675,0.04460146,-0.02297545,-0.0384946,0.01189979,-0.03095663,-0.07681113,-0.00067144,0.0149475,-0.02249563,0.04822087,-0.0315575,0.04003789,-0.01040134,0.01419183,-0.02391904,-0.05159764,-0.01816615,-0.03010873,0.04100485,-0.02775723,-0.01057843,-0.00851186,0.01265529,0.04146826,-0.02727811,0.01698162,0.00199044,-0.05093017,0.10779078,0.05947745,-0.05208152,-0.02692357,0.03586043,0.05555834,-0.05725716,0.05948078,0.00773624,0.06138882,-0.00465516,-0.0153317,0.00317574,-0.00973543,-0.0526077,-0.18078974,-0.06091743,-0.01000764,0.03716891,0.06485479,-0.02043366,0.01027426,-0.01103108,-0.00725043,0.04171535,0.07617051,-0.07922899,0.02556893,0.02783774,-0.0147377,0.02944851,-0.12975262,-0.026767,-0.03213307,0.04267611,-0.00474002,0.03951285,-0.01459074,-0.12141245,0.01719746,-0.01988904,0.1476924,-0.03907409,-0.00440748,-0.016074,0.09960888,-0.02170489,0.0028656,0.02914169,0.03229255,0.02517125,-0.05237705,-0.04745032,-0.02335488,-0.02277098,-0.05154665,0.00863498,-0.01669934,-0.11964741,-0.00592889,-0.0155054,-0.03384788,0.0269985,-0.00832003,0.0817378,0.02943256,0.01368353,0.02529229,-0.0126812,0.03202689,-0.02267754,-0.06902196,0.04824801,-0.00857716,0.08241516,-0.01464673,-0.02158611,0.0324931,-0.02794592,0.04316083,-0.0031361,-0.04653762,-0.06292013,-0.038794,-0.00712149,-0.00430486,0.08136346,0.01197108,0.08654749,-0.01533766,0.02902898,0.00422574,-0.00004991,0.04712698,0.00526763,-0.04106484,-0.04739676,0.01806465,0.12794253,0.02560793,0.02480207,0.04146682,0.04838226,-0.01454055,0.01657426,0.00002498,0.00717657,-0.0302286,0.01458581,0.04830996,0.01334517,-0.23314972,0.04939992,-0.04278733,0.05966859,-0.0527866,-0.04433473,0.03050142,0.05025735,0.0565652,-0.03173851,0.06862409,0.065155,0.03494286,-0.02516756,0.0713632,-0.0162186,-0.01843025,-0.02210021,0.04760178,-0.03238923,0.05254126,0.0307599,0.24989338,0.02233634,0.03063119,0.0542209,-0.01216038,0.03028565,-0.05049417,0.02589132,-0.02798497,-0.02188955,0.06502835,-0.01040071,-0.04418858,0.01967429,-0.05438373,0.01133588,-0.03410863,0.04260677,-0.07395953,0.01430306,-0.00836374,0.04784388,0.13027821,0.02286993,0.00706406,-0.13754585,0.05544617,0.05832805,-0.05487619,-0.03079708,-0.06744149,-0.05502217,0.01123621,0.03435883,0.03834292,-0.01160715,-0.00363434,-0.00832661,0.0119853,-0.04699526,0.06333791,0.01629164,-0.03419726],"last_embed":{"hash":"1tcv1u4","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"1tcv1u4","at":1753423546365},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{3}","lines":[131,162],"size":3624,"outlinks":[{"title":"These are the only winning strategies applicable to lotto wheels for 12 and 18 numbers.","target":"https://saliu.com/HLINE.gif","line":31}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08017703,-0.08351997,0.0276018,-0.01762482,-0.10476735,0.10077587,-0.00679184,0.01617007,0.0256902,-0.00418529,0.00453585,-0.00030537,0.02696121,0.00749279,-0.01094197,-0.00131588,0.0138695,0.03892857,-0.09370187,0.00721599,0.07570466,-0.03433156,-0.09878046,-0.04985033,0.01661988,0.04052959,-0.02882939,-0.0348268,0.01849567,-0.22644,0.02991172,0.03915692,-0.01596212,-0.02779051,-0.07826255,-0.07520199,-0.0523148,0.07933585,-0.05558714,0.00898875,-0.00411506,0.05082774,0.02298129,-0.03104619,0.02114328,-0.01388949,-0.00890392,0.03992265,0.04767417,-0.0201295,-0.03139647,-0.02284975,-0.00417671,0.00470089,0.03709935,0.02087976,0.01420127,0.06545768,0.06607106,-0.00575701,0.05055929,0.05420776,-0.18589738,0.04925307,0.02263748,-0.01145217,0.01092968,-0.0243592,0.00479194,0.10506719,0.04411307,-0.01301756,-0.00228658,0.07501391,0.02504306,0.01921864,-0.04531106,-0.07890236,-0.04327623,0.02192973,-0.03410033,-0.01041771,-0.01594564,0.0142258,-0.05315097,0.01654779,0.02653128,-0.00191976,0.05186091,-0.06292886,0.02526074,0.02726692,0.03778573,0.02338841,-0.01001252,-0.01891645,-0.02154276,-0.00731851,0.05497549,0.11258695,0.01930319,0.01845214,0.02898551,0.00645756,-0.00177227,0.01327748,-0.04407176,-0.02935417,-0.06111323,0.09121244,0.07476358,-0.00657989,0.01403515,-0.02145527,-0.0198213,-0.01234285,0.00134521,0.03728669,-0.01164552,-0.00011054,-0.01652941,0.0155256,0.05683315,-0.0054784,-0.03164918,-0.00177459,0.01566159,0.09417753,-0.01826465,0.00325241,0.04124765,-0.01096197,-0.07579584,-0.00886267,0.01324782,-0.06204409,0.02179518,0.02045483,0.00175304,0.04441109,-0.00974696,-0.01281049,0.04175932,-0.12906349,-0.03371316,0.06464989,-0.00767497,-0.00298003,0.00331149,0.00206774,0.0167736,-0.04793299,-0.03087082,-0.07794459,0.02476653,0.00972562,0.09349933,0.03916396,-0.021981,0.0527708,-0.07880698,-0.01443176,-0.07689804,0.14760031,-0.03915827,-0.0746521,-0.00165496,0.04159315,-0.00075684,-0.11386551,-0.02424406,0.01294346,-0.07648073,0.04637622,0.12261538,0.0049342,-0.10657638,-0.02290848,-0.04911311,0.00128987,0.06080112,-0.03076772,-0.03309073,0.00383169,-0.03269634,-0.06384568,-0.02368267,-0.01340735,-0.01297557,0.04852143,-0.04004359,0.08038569,-0.04197701,-0.01636075,-0.02079054,-0.06065259,-0.01241287,-0.03558727,0.0399248,-0.0188766,-0.02991834,-0.00309397,-0.00866356,0.04917095,-0.02893899,-0.00152006,-0.00427069,-0.04045824,0.07095188,0.07435441,-0.05202955,-0.00648577,0.0426238,0.05134862,-0.0701668,0.05514358,0.00224176,0.03775436,0.01688778,-0.01457242,0.00302577,0.01003074,-0.08179981,-0.1618114,-0.06544173,0.01464596,0.00286721,0.06408149,0.01495519,0.01791217,-0.00308518,0.01445158,0.03487515,0.05290299,-0.06214225,0.04140472,0.02675246,-0.01864287,0.04303303,-0.13664462,-0.04443432,-0.00792908,0.0461125,0.00858693,0.07383559,-0.02731701,-0.11786062,-0.01624841,-0.01989053,0.13003181,-0.0493778,-0.00488669,-0.00709111,0.08181954,-0.02750154,0.00932963,0.03516648,-0.0052807,0.01013276,-0.05936848,-0.02577482,-0.02895797,-0.03273393,-0.04748587,0.02095806,-0.0232069,-0.07140129,0.01008587,0.00312399,-0.01261799,-0.02063877,0.01192385,0.04458781,0.03436209,0.00367916,0.03635082,-0.00317559,0.02966519,-0.00154298,-0.04695028,0.04344818,-0.02401499,0.08177775,-0.02839295,0.00876698,0.02462045,-0.00577555,0.02984215,0.03733297,-0.04833598,-0.05162706,-0.06319178,-0.03259815,0.00674921,0.07952568,0.02834801,0.05558174,-0.02087246,0.01376339,0.00264716,0.00874637,0.02301608,0.01009856,-0.04460177,-0.04281742,0.03738029,0.10043439,0.01712022,0.01621968,0.01944468,0.01474448,-0.01912447,0.00721563,0.00500387,0.00555857,-0.02885617,0.00828306,0.03038369,0.00494878,-0.22461148,0.0559173,-0.04706832,0.02896765,-0.04700478,-0.02291187,0.01928827,0.04673186,0.06097792,-0.0133429,0.07241038,0.07684718,0.02476523,0.01877219,0.07237637,-0.02141975,0.01596066,-0.02269452,0.06001455,-0.04200341,0.05369682,0.01649956,0.25209671,0.02655804,0.04960781,0.03302428,-0.0236276,0.01676498,-0.07256351,0.01188128,0.00232708,-0.00559623,0.06702705,-0.01012024,-0.03216771,0.04495091,-0.05329211,0.0289845,-0.04540759,0.05091247,-0.08392344,0.00197454,0.02479184,0.05770122,0.13003652,0.02306711,0.00208173,-0.13338204,0.07252858,0.04232343,-0.07879846,-0.01825853,-0.07332627,-0.04589289,0.01895464,0.02691879,0.04035793,-0.00948091,-0.01391035,-0.01655706,0.00771885,-0.04374851,0.04239748,-0.00695224,-0.02074056],"last_embed":{"hash":"110rh2i","tokens":94}}},"text":null,"length":0,"last_read":{"hash":"110rh2i","at":1753423546552},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{4}","lines":[163,163],"size":214,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07899092,-0.07679953,0.01979573,-0.02560243,-0.09490282,0.10728861,-0.00981096,0.01650222,0.03933129,0.00673173,0.01715649,-0.01150953,0.01583668,0.01305863,-0.00099416,-0.00356825,0.03400486,0.0481781,-0.09634695,0.03671858,0.03978849,-0.04388611,-0.09211265,-0.05674108,-0.00042939,0.03087815,-0.03305754,-0.0296588,0.0166285,-0.19371945,0.01820101,0.0369787,-0.02694213,-0.00231349,-0.05086848,-0.07915889,-0.04546847,0.04232944,-0.07881822,0.01107393,0.01015363,0.02763263,0.03511601,-0.00094483,0.04270377,-0.00093433,-0.00193776,0.04402233,0.07336649,-0.00335733,-0.02685625,-0.01097429,0.0126775,0.02254516,0.02902738,0.04508505,0.04001052,0.07965802,0.05640027,0.00469457,0.05319765,0.04213381,-0.17201824,0.03995752,-0.00333497,0.0154642,0.02283029,-0.03726595,0.03363495,0.0678267,0.05239846,-0.00769388,-0.01561224,0.08574468,0.01388378,-0.02153053,-0.0473217,-0.08365355,-0.02256637,0.01182089,-0.05038127,0.01368734,-0.01323036,0.01447025,-0.06213909,0.02134556,0.04301438,0.00904027,0.04274598,-0.07776454,0.01369361,0.0058152,0.06296261,0.04406363,-0.03834522,-0.00678902,-0.00352899,-0.01367813,0.0415505,0.11542761,0.00553332,0.01788617,0.04154121,-0.01827674,-0.00617712,-0.00350944,-0.04868586,-0.0232841,-0.04985264,0.06095101,0.09824882,0.01500458,0.03793065,-0.03913979,-0.0471211,-0.0423077,-0.00806788,0.03397629,0.00227277,0.01239199,-0.04970343,0.03941831,0.05803435,-0.01348078,0.00378916,0.00042095,-0.00273173,0.07136623,0.00594876,0.03294118,0.01500479,-0.02576927,-0.07567704,-0.02199554,0.01142225,-0.02327016,0.00511007,0.00261185,-0.01606566,0.04905759,-0.01602632,-0.04037481,0.0113949,-0.12759864,-0.0208174,0.03047626,-0.01043141,0.00872969,0.00349514,0.01125884,-0.00540381,-0.06723192,-0.05806077,-0.05336434,0.03890897,-0.00235168,0.09355637,0.03147256,-0.03994869,0.05543203,-0.04854604,-0.07149755,-0.0514381,0.12550689,-0.03781691,-0.06722815,-0.01084969,0.05226405,0.01740214,-0.10860762,-0.03466341,0.01026756,-0.08578115,0.04151929,0.13819917,0.00760435,-0.11276048,-0.01551292,-0.05767088,-0.01059915,0.02339562,-0.04237282,-0.00508039,0.00423603,-0.04590634,-0.06917753,-0.02159297,-0.00492374,-0.00305328,0.04179815,-0.03051939,0.0629395,-0.01541592,-0.01255445,-0.02468274,-0.04282261,-0.02319253,-0.05432823,0.05883775,-0.03227033,-0.02044641,0.01628772,-0.01940048,0.0439254,-0.03921393,0.02132858,-0.00160496,-0.04950963,0.07224165,0.06197551,-0.0408735,-0.00904026,0.07191175,0.04103728,-0.06600172,0.05400141,0.01771148,0.00827929,0.00862261,-0.02402439,-0.0052751,0.03218879,-0.05980738,-0.17678916,-0.04530486,-0.00308846,-0.00074224,0.0679222,-0.02132377,-0.01794664,-0.01415401,0.04419725,0.04692996,0.10485973,-0.05230663,0.02286745,0.03080306,-0.01256055,0.05130823,-0.11403305,-0.03392049,-0.02056107,0.05689617,0.00249939,0.03354858,-0.02084886,-0.12629768,0.00591089,-0.00973774,0.11652552,-0.04019238,0.03209414,-0.02355475,0.09423381,0.00321915,0.00748308,0.00678225,0.00407897,0.0162177,-0.09531866,-0.03063665,-0.0137718,-0.01870744,-0.06908158,0.01071694,-0.02261053,-0.09210509,0.02876155,-0.01178993,0.00309925,0.00184369,-0.00162878,0.04874105,0.01611675,0.01621061,0.04469006,-0.00547197,0.03328791,-0.00265703,-0.0477473,0.04870132,-0.02031079,0.09003692,-0.05398528,-0.0285619,0.02026191,-0.00865453,0.038051,0.02831445,-0.08582893,-0.07148466,-0.02657801,-0.00127686,-0.00959044,0.05692265,0.03067489,0.0874932,-0.05789731,0.0070463,0.01664137,0.00018368,0.04835215,0.01365094,-0.06963366,-0.03883171,0.02932686,0.12477444,0.02540236,0.05534955,0.01283651,0.00643732,-0.02878858,0.00006254,-0.00606961,0.00955033,-0.03098306,0.00095107,0.03370221,0.01929101,-0.22216685,0.04329708,-0.01473944,0.04967869,-0.05490939,-0.02488076,0.04305136,0.05958942,0.05363625,-0.01091104,0.0901769,0.04198824,0.01470755,0.00159071,0.05915998,0.00056435,-0.01042374,-0.01335064,0.05585755,-0.01789288,0.04484151,0.02549095,0.24303028,0.01428162,0.0452075,0.0246616,-0.00140572,0.02300085,-0.06459236,0.03255106,-0.01315908,0.00107112,0.0585197,-0.02658498,-0.0594638,0.02923996,-0.09022821,0.05484685,-0.03575613,0.04471009,-0.0784572,-0.00735087,0.00982975,0.05191895,0.10835395,0.03611724,-0.00245825,-0.13328832,0.06929358,0.05009548,-0.05055114,-0.00276473,-0.08232559,-0.05844339,0.01761878,0.01697909,0.04727789,-0.02743066,0.03108176,0.00138803,0.00778069,-0.04057756,0.05540445,-0.00518507,-0.02232846],"last_embed":{"hash":"qu9cng","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"qu9cng","at":1753423546589},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#<u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>#{8}","lines":[167,168],"size":259,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07405683,-0.0538394,0.00260084,-0.01555871,-0.07132441,0.09492391,-0.01501254,-0.00119712,0.04971309,-0.01932241,0.00059639,-0.00808766,0.04023514,0.01902072,-0.00611264,-0.02883443,0.00665559,0.02825378,-0.07301466,0.0021077,0.04692833,-0.03835444,-0.07054027,-0.06559821,0.00123782,0.01224189,-0.0361105,-0.03877966,-0.00509626,-0.23548971,0.05377922,0.03688163,-0.00447117,-0.01993346,-0.0856327,-0.01661359,-0.04089206,0.05874446,-0.07984525,0.02732986,0.00805861,0.02239801,0.03076779,0.00140705,0.04607272,-0.0269011,-0.02652122,0.03499694,0.04423854,-0.01298621,-0.07050689,-0.04745005,0.00568803,0.01096262,0.05615398,0.05199448,0.05049759,0.11258099,0.04212715,0.0353819,0.05852071,0.07644579,-0.18154053,0.0349531,0.03106073,0.01909325,-0.00933553,-0.03359036,0.04238578,0.05418422,0.01960624,0.01619593,-0.00341979,0.06837301,0.00898337,0.00659079,-0.04807832,-0.09013036,-0.01768842,0.00513143,-0.02947861,0.01656669,-0.01264965,0.02090435,-0.0237333,0.06797174,0.01994309,0.0079694,0.04210441,-0.08519872,0.01427802,0.02374248,0.04085614,0.02800164,-0.01072245,0.00038526,0.00838028,-0.0252321,0.02987547,0.10412028,-0.0019298,0.01716225,0.0149038,0.011368,0.0288385,-0.01520177,-0.03462471,-0.0484248,-0.05967005,0.09073538,0.08672494,0.00386163,0.01439578,-0.02092987,-0.05273231,-0.01704894,0.00405617,0.01009991,-0.01556982,0.00606327,-0.0487748,0.00358249,0.02780265,0.02144231,-0.01228194,0.00850045,0.00268158,0.0861016,0.00415385,0.00285908,0.02006709,0.00206683,-0.0996008,-0.02513906,0.00049706,-0.03858107,0.00115089,0.01357725,-0.00617202,0.04468407,-0.02878964,-0.03323154,0.05018007,-0.11825424,-0.04139213,0.0531685,0.02179352,-0.00203107,0.02042677,0.01508057,-0.00640514,-0.02283012,-0.06155477,-0.05170905,0.01009505,0.00780569,0.07081807,0.05917225,-0.06109376,0.03945189,-0.04960803,-0.06930272,-0.03621045,0.15521924,-0.02273781,-0.06854989,-0.0221157,0.0776855,-0.00055646,-0.08021443,-0.03491763,0.00492371,-0.05638662,0.02784037,0.10005028,-0.00583987,-0.09384666,-0.04287135,-0.03634741,-0.01174187,0.01202175,-0.02949646,-0.03299339,0.01197238,-0.04341502,-0.08460892,-0.01273366,-0.03360207,-0.00756017,0.040446,-0.06154231,0.0236128,-0.01368668,0.00237325,-0.04129496,-0.04396778,-0.00940076,-0.03101383,0.05587397,-0.00110009,-0.01791283,0.01514593,-0.00477175,0.01939762,-0.04421901,0.03879339,-0.03468438,-0.06233491,0.0933636,0.06902125,-0.03315519,0.01167165,0.06447034,0.06773657,-0.05832947,0.05687135,0.01176023,0.02275983,0.01757099,0.00028403,-0.0137521,0.003866,-0.05568558,-0.19621404,-0.05329283,-0.00892962,-0.00969684,0.03923475,-0.02365492,-0.01353449,-0.00864059,0.04481097,0.04871111,0.10081212,-0.04140794,0.00377133,0.03160392,0.01169393,0.02416599,-0.10796157,-0.01440239,-0.04470264,0.06713074,-0.01376179,0.03279235,0.0092193,-0.11556958,-0.00249303,-0.02583639,0.12213717,-0.03455134,0.01506339,-0.00082789,0.07633597,-0.01260196,-0.01667271,-0.00336943,0.02467167,0.02838552,-0.04534911,-0.01561385,-0.04750967,-0.01931721,-0.07898205,0.01145693,0.00806701,-0.09149292,-0.00272134,0.01209688,-0.01319975,-0.02352428,-0.00213195,0.03455948,0.02427276,0.04094602,0.03904147,0.01065874,0.0372881,-0.02945639,-0.06768723,0.02358214,-0.01658802,0.05688507,-0.02870736,-0.04140485,0.05810177,-0.0351769,0.05453523,0.00929199,-0.05333802,-0.04780741,-0.01793814,0.00520786,-0.00456688,0.11203464,0.04578544,0.06214959,-0.02500909,0.01942396,0.02152531,-0.01088642,0.02279833,0.0275232,-0.06354342,-0.06204093,0.04349042,0.12321027,0.03115479,0.01279675,0.04090607,-0.01243042,0.01115006,0.00296468,-0.00722691,0.01780437,-0.03321901,0.03352944,0.03824068,0.0348975,-0.25864434,0.06558179,0.00099881,0.03433724,-0.02582106,-0.0125036,0.02340379,0.02727589,0.05795173,-0.04387224,0.06781311,0.05573093,0.01832265,-0.0367549,0.02449562,0.01849236,0.02557263,-0.04192589,0.06052555,-0.01292888,0.03080192,0.05722486,0.24674347,-0.00073951,0.03672921,0.04542586,-0.02708562,0.01934206,-0.04842845,0.04636879,-0.00502193,0.01186184,0.07182121,0.01025753,-0.03940433,0.04583267,-0.06289556,-0.01786268,-0.02370877,0.05201346,-0.08404789,0.02089776,0.02455408,0.04731188,0.1167606,0.0246357,-0.02110823,-0.12274975,0.06109718,0.05409499,-0.07174647,-0.04430926,-0.05887759,-0.03819666,0.02747982,0.03231916,0.02350843,-0.01272886,-0.01074463,-0.01999897,0.02663644,-0.05815635,0.04206117,0.00457879,-0.0149509],"last_embed":{"hash":"19wxoph","tokens":451}}},"text":null,"length":0,"last_read":{"hash":"19wxoph","at":1753423546627},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)","lines":[171,216],"size":4619,"outlinks":[{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":7},{"title":"_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":10},{"title":"_**Lottery, Software, Strategies**_","target":"https://saliu.com/LottoWin.htm","line":11},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":13},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":14},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":16},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":18},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":19},{"title":"_**Best <u>On-The-Fly Wheeling</u> Software**_","target":"https://saliu.com/bbs/messages/wheel.html","line":21},{"title":"_**Software to Verify Lotto Wheels**_","target":"https://saliu.com/check-wheels.html","line":22},{"title":"_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_","target":"https://saliu.com/copyright.html","line":23},{"title":"_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_","target":"https://saliu.com/bbs/messages/772.html","line":24},{"title":"_**Powerball Wheels**_","target":"https://saliu.com/powerball_wheels.html","line":25},{"title":"_**Mega Millions Wheels**_","target":"https://saliu.com/megamillions_wheels.html","line":26},{"title":"_**Euromillions Wheels**_","target":"https://saliu.com/euro_millions_wheels.html","line":27},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":28},{"title":"Lottery wheeling master teaches how to create the best 12-number lotto wheel, 18 numbers 6 per line.","target":"https://saliu.com/HLINE.gif","line":41},{"title":"Forums","target":"https://forums.saliu.com/","line":43},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":43},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":43},{"title":"Contents","target":"https://saliu.com/content/index.html","line":43},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":43},{"title":"Home","target":"https://saliu.com/index.htm","line":43},{"title":"Search","target":"https://saliu.com/Search.htm","line":43},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":43},{"title":"The 12 and 9-number lotto wheels assure best chances for high lottery prizes, including the jackpot.","target":"https://saliu.com/HLINE.gif","line":45}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09285695,-0.05376451,0.01692221,-0.02323627,-0.05964872,0.07750967,-0.00743072,0.00327561,0.01565514,-0.02528718,0.00716382,-0.00393823,0.02352169,0.00791961,0.00359646,-0.01624649,0.02878141,0.04643572,-0.0496729,0.00349938,0.04567736,-0.0168075,-0.09101324,-0.06385724,0.00455009,0.02560949,-0.02897379,-0.03185828,0.01904072,-0.17954092,0.03196884,0.03480154,-0.04464114,-0.01321659,-0.07345138,-0.0450889,-0.02784428,0.0414752,-0.0879219,0.02551978,0.01015121,0.01064194,0.00192119,-0.01081383,0.03092215,-0.03226506,-0.0016544,0.03118801,0.06826601,-0.01279434,-0.04583047,-0.0183771,0.01366924,-0.00562347,0.03175863,0.05521257,0.04468448,0.09522761,0.03673529,0.00198033,0.05542625,0.04998904,-0.17634949,0.04851002,0.01676699,0.03235207,0.02395588,-0.01075965,0.03911984,0.04503039,0.03924274,0.00030354,-0.0241561,0.09046899,0.02243415,-0.00261925,-0.02581624,-0.08392298,-0.04658213,0.01117536,-0.04446148,0.00328312,-0.0083603,0.03077807,-0.05165761,0.05403545,0.04096623,0.02184652,0.06331713,-0.08140051,0.0201747,-0.01564891,0.0647354,0.05221315,-0.04219968,-0.02285224,0.01978634,-0.00702888,0.05578437,0.13538305,-0.01097569,0.02861151,0.04589437,0.00172294,0.03096956,0.01015955,-0.02127553,-0.02825932,-0.05674076,0.1147408,0.08453253,0.02594458,0.00441371,-0.0275544,-0.05035666,-0.05548154,-0.01987915,0.02777952,-0.01408693,-0.00096351,-0.04642204,0.00894439,0.01267031,0.01554102,-0.02419594,-0.00423785,0.01377863,0.07034275,-0.00342829,0.01078264,0.02838451,0.00543255,-0.12873018,-0.04077645,0.00921187,-0.02552244,-0.00629845,-0.01900844,-0.01077582,0.03910735,-0.02346299,-0.03073934,0.02676731,-0.12705241,-0.04511379,0.0527265,-0.01224606,0.00604178,-0.01451177,0.00458028,0.00111593,-0.04227555,-0.04062974,-0.04584292,0.03065714,-0.0058781,0.08521026,0.04232791,-0.01361432,0.05718839,-0.04431903,-0.0512974,-0.05003333,0.15124017,-0.02780626,-0.08331821,-0.03954425,0.07115818,0.01524838,-0.07572532,-0.0367213,0.0069158,-0.08371571,0.02437933,0.11967982,-0.00016197,-0.09853902,-0.03217375,-0.04884963,-0.02616306,0.03516111,-0.03585117,-0.02583457,0.00311727,-0.04610454,-0.09512399,-0.00910643,-0.04441028,-0.00798653,0.06576134,-0.03744554,0.03774249,-0.0065039,-0.02558423,-0.03203949,-0.03683003,-0.04031593,-0.04295693,0.07144604,-0.04574842,-0.03970332,0.0406026,0.00565682,0.04855116,-0.02233112,0.01481397,-0.04347646,-0.05809003,0.08141205,0.05910024,-0.05362254,0.0313659,0.07147054,0.04072978,-0.07327724,0.04110384,0.00464001,0.03719572,0.00523987,-0.00999194,-0.00606535,0.01762396,-0.06775735,-0.18652754,-0.04756722,-0.01825241,-0.00707913,0.03103239,-0.01573406,-0.00626119,0.00292117,0.01486762,0.03655756,0.12350399,-0.06087627,0.04164613,0.02721248,0.00933226,0.0460185,-0.07702136,-0.02943959,-0.00603983,0.04356186,-0.00586528,0.01746328,0.00695171,-0.12775764,-0.00420772,-0.02395466,0.10958242,-0.04253653,0.03201701,-0.00811009,0.10314043,-0.03501384,-0.02380784,-0.03823583,-0.01101795,0.03906453,-0.07365537,-0.00363591,-0.01842746,-0.01211189,-0.06654184,-0.0161109,0.0064808,-0.06479304,0.02495814,0.01094644,0.01107742,-0.03680769,-0.00854885,0.03697286,0.01631789,0.01565544,0.04170605,0.01420194,0.02290932,-0.01146061,-0.03772509,0.02548839,-0.02083126,0.05716803,-0.03363718,-0.02809707,0.05043714,-0.03093753,0.05944768,-0.00393965,-0.05090421,-0.07511051,-0.03306735,-0.01310616,-0.00974192,0.09933513,0.02097017,0.05614103,-0.02470002,0.0388226,0.01484041,-0.01358222,0.02633313,0.03153005,-0.08247911,-0.07302693,0.05279078,0.12090585,0.04958374,0.06107631,0.01569983,-0.01477347,-0.00856763,0.01828278,-0.00408001,0.0156932,-0.03852589,0.01094122,0.0333528,0.02910885,-0.2309467,0.04595822,-0.01097845,0.03633424,-0.03153292,-0.02590419,0.01504992,0.04130213,0.06184136,-0.01850497,0.10275032,0.02452916,0.00386313,-0.00670832,0.03866728,0.01676835,0.04639741,-0.01345197,0.0643148,-0.00908412,0.01033122,0.02816574,0.25660741,0.0278966,0.0452309,0.03646925,-0.0304155,0.01532285,-0.06584544,0.04029582,0.01099138,0.01150508,0.06050702,0.0112424,-0.05377836,0.04570619,-0.06777422,0.01487169,-0.01645513,0.06404677,-0.08309247,0.02367749,0.00744782,0.03903309,0.09480544,0.0244323,0.00211638,-0.1305275,0.06085048,0.05193125,-0.0772308,-0.04332552,-0.08385929,-0.03044938,0.03317928,0.03136076,0.03387558,-0.02539859,-0.0013309,-0.01322124,0.04729519,-0.03286342,0.03497065,-0.00401985,-0.0024948],"last_embed":{"hash":"mkx4tu","tokens":114}}},"text":null,"length":0,"last_read":{"hash":"mkx4tu","at":1753423546824},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}","lines":[173,176],"size":210,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{19}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09021088,-0.06627942,-0.00373723,-0.03227321,-0.07441628,0.06401593,-0.0271947,-0.00231314,0.02619145,-0.01054874,0.00685382,-0.01321618,0.03970138,0.0047082,-0.00299807,0.01148866,0.0198753,0.01489427,-0.10360163,-0.0159763,0.07075556,-0.04652923,-0.0612207,-0.07377087,-0.01208324,0.01742486,-0.02870525,-0.01700837,-0.00795634,-0.233215,0.04515148,0.01473575,-0.02833916,-0.02300217,-0.07935554,-0.03482127,-0.04234036,0.02822652,-0.09213997,0.00022548,0.00450854,0.03811984,0.00636587,-0.01974668,0.04966826,-0.02020457,0.01154326,0.03411711,0.08576339,0.02634237,-0.04269401,-0.02484094,0.03947133,0.01299738,0.0398354,0.0327091,0.03599452,0.10130152,0.04912056,0.05085319,0.04616166,0.08732793,-0.15709679,0.04793682,-0.01142041,0.01881954,0.00969256,-0.0700048,0.0237625,0.0556504,0.03832295,0.02800829,0.00272308,0.09412283,0.02217295,-0.0070234,-0.05732848,-0.09475403,-0.05637303,0.05307264,-0.05912673,0.01104819,-0.01105753,0.00149854,-0.00562772,0.03160615,0.03295199,0.02106186,0.05436609,-0.06133291,0.00012555,0.01411302,0.04852033,0.04332444,-0.02495845,0.00298042,0.0242841,-0.03036369,0.03384994,0.10455476,-0.00538633,0.02609902,0.00374434,-0.01152429,0.0042283,-0.00953521,-0.02752849,-0.01261807,-0.03343313,0.04500573,0.06935665,-0.0010309,0.04614118,-0.02094638,-0.06229424,-0.00733786,-0.00792358,0.00693571,-0.00607497,0.02391771,-0.05352948,0.01278188,0.04539447,0.0123007,-0.00419134,0.00350542,0.04238555,0.04606013,0.01640951,0.03648265,0.02862755,0.01184939,-0.11837696,-0.04746326,0.01682347,-0.04061297,-0.00271906,0.00847327,-0.00594831,0.01079889,0.00201842,-0.03414702,0.05319016,-0.10032409,-0.04281638,0.03819359,-0.02459189,-0.01478333,-0.00465758,-0.01917869,0.0061762,-0.03003887,-0.01223017,-0.04619387,0.04261028,0.02065886,0.12920463,0.08519527,-0.05109472,0.04454768,-0.06732563,-0.04884501,-0.03623379,0.1183057,-0.02531564,-0.08132435,-0.009857,0.06889597,-0.01260818,-0.08076397,-0.03122864,0.02977022,-0.06231323,0.03929668,0.1161572,0.00376837,-0.09107685,-0.01504706,-0.02222547,-0.02535839,0.02146811,-0.0384609,-0.03500377,-0.00291195,-0.03575451,-0.10705376,0.0076034,-0.01469064,-0.00738445,0.04046394,-0.03198034,0.04454052,0.0205707,-0.01502318,-0.02125665,-0.04076203,-0.04745007,-0.01904444,0.06169058,-0.02858914,0.00938981,0.0312479,0.0320161,0.04056732,-0.01156528,0.02952636,-0.03089899,-0.08907367,0.08967285,0.03128716,-0.04642185,-0.00614463,0.0649558,0.09118148,-0.08183555,0.05705024,-0.00082644,0.02412512,-0.00217176,-0.00309193,0.01070688,0.00971992,-0.08083469,-0.19521292,-0.03268711,-0.01984263,0.0501628,0.04998122,-0.02280808,0.00808197,-0.02595946,0.03580588,0.05963948,0.06950952,-0.08106691,0.01365858,0.04846043,0.00172497,0.04191051,-0.12054991,-0.02160213,-0.03670808,0.05536711,0.02099174,0.04035894,0.00876188,-0.11310892,0.04799787,-0.03733208,0.13737233,-0.01833689,0.01517602,-0.02882918,0.10001108,-0.02702258,0.00309566,-0.0028101,0.02140855,0.02788876,-0.04175268,-0.03210878,-0.01883274,-0.02458828,-0.05738739,0.00514347,0.00460035,-0.11544738,0.03508998,-0.01488153,-0.01169205,-0.01506776,0.01304245,0.04236585,0.01259252,0.02496376,0.04577307,-0.00588112,0.05416983,-0.03711748,-0.03622392,0.01863213,0.00218251,0.04477748,-0.02840632,-0.03350704,0.06142054,-0.0624158,0.05086567,0.00098914,-0.03687508,-0.04881091,0.00650157,-0.01228089,-0.00989244,0.06802005,0.01455335,0.10343424,-0.0348525,0.03515894,0.01752301,0.01523164,0.02883699,-0.0008384,-0.05053654,-0.03243807,0.04702065,0.11662856,0.01300756,0.02792832,0.05172671,0.02537923,-0.00721064,0.02281564,0.00877079,0.03102937,-0.03914721,0.04515068,0.02605717,0.03764663,-0.25056148,0.01874657,-0.03556866,0.04077509,-0.02351475,-0.03591142,0.00676099,0.02697091,0.04881639,-0.04708955,0.03195725,0.0422276,0.03464916,-0.01610619,0.02819927,0.01937326,0.03332546,-0.02676991,0.06893153,-0.04145605,0.03429899,-0.00260517,0.24374671,0.01192891,0.02481476,0.04574862,-0.02951624,0.00862114,-0.03745925,0.00192478,-0.01362806,0.01999822,0.06288464,-0.01993558,-0.04574986,0.00970131,-0.07419991,0.0198213,-0.04825276,0.01570986,-0.07200292,0.01127811,0.01058901,0.00810442,0.09026417,0.02089783,-0.00155057,-0.12132563,0.04022781,0.0586173,-0.07410775,-0.03225159,-0.08277687,-0.04724728,0.02901599,0.03937032,0.0128247,-0.00719576,-0.00633097,-0.01265799,0.01224877,-0.05929092,0.04957179,0.01084123,-0.00882646],"last_embed":{"hash":"1039pd0","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"1039pd0","at":1753423546865},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{19}","lines":[197,210],"size":1476,"outlinks":[{"title":"_**Euromillions Wheels**_","target":"https://saliu.com/euro_millions_wheels.html","line":1},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":2}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{20}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08524692,-0.06671992,0.00098791,-0.02409614,-0.08675533,0.08193331,-0.04149861,0.02512053,0.02508356,-0.0031859,-0.02160103,-0.03078977,0.00491388,0.0080721,-0.02893194,-0.00264928,0.01850084,0.02193113,-0.08249924,0.03033206,0.07909381,-0.0675803,-0.09856987,-0.08261017,0.01982687,-0.00655693,-0.03846163,-0.02241833,0.00303857,-0.2125199,0.01335964,0.0202442,0.00271766,-0.02838592,-0.0822036,-0.03304988,-0.04824705,0.03465766,-0.04967456,0.02688977,-0.00374329,0.04338327,0.02017596,0.00637581,0.052087,-0.02552864,0.00160831,0.0278274,0.08567025,-0.00210234,-0.04841045,-0.01605589,0.00749373,0.01039735,0.06830547,0.0471034,0.02491637,0.09945793,0.0275389,0.02830348,0.0293249,0.07107407,-0.19647841,0.03765397,-0.00479879,0.00703224,-0.00754475,-0.0275678,0.02407085,0.04881612,0.05834937,0.00413516,-0.01316611,0.09100258,0.02325668,-0.02488591,-0.04600044,-0.08533146,-0.00138185,0.00560378,-0.0510973,0.00897875,-0.01911165,-0.00633075,-0.02899371,0.02496799,0.05092017,0.03866755,0.03504675,-0.0679392,0.0507893,0.01433231,0.03963647,0.02444666,-0.02818953,0.02129903,-0.00607541,-0.03613468,0.02818939,0.1069982,0.00861192,0.00851951,0.02706592,-0.0029836,0.0266746,0.0002721,-0.02581352,0.00177516,-0.04418265,0.06590201,0.09689989,-0.00201818,0.02431172,-0.02044912,-0.05528314,-0.0246052,0.01120236,-0.00272107,-0.03565003,0.02006589,-0.0556359,0.0271251,0.01870176,-0.0032552,0.00474793,0.00232901,0.03044689,0.0473538,0.01552149,0.04658085,0.03733271,0.02933897,-0.08842538,-0.02316274,0.00500386,-0.05829391,-0.00295457,0.00566804,-0.00921272,0.01949474,0.0137874,-0.05133943,0.01320281,-0.0960123,-0.01920323,0.02904258,-0.0322834,-0.0091551,-0.01838432,-0.00280281,-0.01938131,-0.05314124,-0.03455472,-0.06508671,0.03755172,-0.00117602,0.09488424,0.05638491,-0.02948633,0.06001521,-0.04077797,-0.06630729,-0.02364954,0.12472591,-0.01653265,-0.07745338,-0.04751045,0.0760251,0.02109649,-0.10228884,-0.03343814,0.00049236,-0.07637342,0.06998059,0.1216705,-0.01346978,-0.08849717,0.00100975,-0.04990798,-0.01213604,0.01287658,-0.04263202,-0.0319736,0.01083581,-0.04014571,-0.06642713,0.02262906,-0.05342786,-0.00060028,0.06030154,-0.03034219,0.05249581,-0.01603453,-0.00190278,-0.02849664,-0.02689102,-0.03186827,-0.03505539,0.05969837,-0.0168022,-0.04413907,0.00283272,-0.03300596,0.00706404,-0.04903403,0.04279133,-0.02450431,-0.05631556,0.07964781,0.07111344,-0.02540424,0.00569713,0.08358864,0.03014419,-0.08256657,0.0391724,0.00586748,0.04294284,0.01703203,-0.04018041,0.01752727,-0.01267357,-0.07558985,-0.18619816,-0.03262167,-0.00816277,-0.0094824,0.05309712,-0.00226132,0.03757164,-0.02376197,0.0497567,0.04294062,0.12270197,-0.06883633,0.02293147,0.00844923,0.02577354,0.04790206,-0.12404318,-0.02879603,0.01604238,0.06177289,0.0179859,0.01648916,-0.00740082,-0.10480407,-0.00037487,-0.0279163,0.12766206,-0.01671829,0.01945005,-0.01778825,0.10792364,-0.0428736,-0.00809593,0.00418762,0.02637174,-0.02436831,-0.10179457,-0.01606116,-0.04516428,-0.0324843,-0.0340809,0.01783573,-0.00253801,-0.07618906,-0.0202974,-0.02238336,-0.02255937,0.00001681,-0.00648525,0.03007555,0.02413743,0.03297173,0.04687262,-0.01844297,0.0079112,-0.00650425,-0.06014069,0.01056694,-0.039916,0.07473792,-0.05040511,-0.02156719,0.04884704,-0.02701787,0.04748398,-0.00207791,-0.05706373,-0.04413107,-0.01429011,-0.01630814,-0.02287806,0.05748451,0.03090113,0.08045144,-0.00507367,0.00311387,0.00774289,-0.02478161,0.01664071,0.01816281,-0.06497667,-0.0718845,0.03006349,0.11088816,0.065722,0.05396013,0.02895392,0.03020936,-0.01752947,0.03072463,0.02433198,0.03317215,-0.04429375,0.02317793,0.0415918,0.0146572,-0.24060696,0.04991067,-0.02305386,0.04447257,-0.04097756,-0.04335848,0.03559194,0.04036718,0.03636596,-0.00959414,0.11194,0.06036742,0.0244683,-0.00150413,0.0625424,-0.00175802,-0.03536388,-0.01298188,0.0508079,-0.00462047,0.02488318,0.01676755,0.23316288,0.02748637,0.0357488,0.03603476,0.00381177,0.03124262,-0.00130233,0.01469302,-0.00534939,0.00414721,0.06945743,-0.00801013,-0.02755281,0.06561863,-0.0596984,-0.00553081,-0.03935863,0.03777912,-0.07672055,0.03193222,0.02462728,0.05701592,0.11694045,0.00218878,-0.00692827,-0.12171023,0.07883666,0.03674246,-0.05747646,-0.0086883,-0.06322774,-0.01504517,-0.00162409,0.02222138,0.06619529,-0.01885519,0.00554013,-0.01010252,0.02733179,-0.01690182,0.06390923,0.00231649,-0.01409227],"last_embed":{"hash":"1jp1yjn","tokens":322}}},"text":null,"length":0,"last_read":{"hash":"1jp1yjn","at":1753423547054},"key":"notes/saliu/Lotto Wheels Create, Make Lottery Wheeling Systems.md#Lotto Wheels: Create, Make Lottery Wheeling Systems#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{20}","lines":[211,216],"size":659,"outlinks":[{"title":"Lottery wheeling master teaches how to create the best 12-number lotto wheel, 18 numbers 6 per line.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"The 12 and 9-number lotto wheels assure best chances for high lottery prizes, including the jackpot.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
