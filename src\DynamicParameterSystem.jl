# src/DynamicParameterSystem.jl

module DynamicParameterSystem

using Dates
using Statistics
using Printf
using ..SaliuLottery: Drawing
using ..WonderGridEngine: WonderGridConfig, WonderGridError

# 重新導出子模組
include("DynamicParameterManager.jl")
include("AdaptiveAdjustmentEngine.jl") 
include("PerformanceMonitor.jl")

using .DynamicParameterManager
using .AdaptiveAdjustmentEngine
using .PerformanceMonitor

export DynamicParameterController, SystemStatus,
       create_dynamic_system, initialize_system, update_system,
       get_system_status, optimize_parameters, shutdown_system

# 系統狀態枚舉
@enum SystemStatus begin
    INITIALIZING
    MONITORING
    ADJUSTING
    OPTIMIZING
    ERROR
    SHUTDOWN
end

# 動態參數系統控制器
mutable struct DynamicParameterController
    parameter_monitor::ParameterMonitor
    adjustment_engine::AdjustmentEngine
    performance_monitor::PerformanceMonitor
    current_config::WonderGridConfig
    system_status::SystemStatus
    last_update::DateTime
    update_interval::Int  # 分鐘
    auto_optimization::Bool
    
    function DynamicParameterController(
        initial_config::WonderGridConfig,
        update_interval::Int = 60,
        auto_optimization::Bool = true
    )
        # 創建子系統
        adaptive_config = AdaptiveConfig()
        param_monitor = create_parameter_monitor(adaptive_config)
        adj_engine = create_adjustment_engine(param_monitor, initial_config)
        perf_monitor = create_performance_monitor()
        
        new(param_monitor, adj_engine, perf_monitor, initial_config,
            INITIALIZING, now(), update_interval, auto_optimization)
    end
end

"""
創建動態參數系統
"""
function create_dynamic_system(
    initial_config::WonderGridConfig,
    update_interval::Int = 60,
    auto_optimization::Bool = true
)::DynamicParameterController
    
    println("🚀 創建動態參數系統...")
    
    controller = DynamicParameterController(initial_config, update_interval, auto_optimization)
    
    println("✅ 動態參數系統創建完成")
    println("  - 更新間隔: $(update_interval) 分鐘")
    println("  - 自動優化: $(auto_optimization ? "啟用" : "停用")")
    println("  - 系統狀態: $(controller.system_status)")
    
    return controller
end

"""
初始化系統
"""
function initialize_system(
    controller::DynamicParameterController,
    historical_data::Vector{Drawing}
)::Bool
    
    println("🔧 初始化動態參數系統...")
    
    try
        controller.system_status = INITIALIZING
        
        # 初始化參數監控器
        if !isempty(historical_data)
            initial_state = update_parameter_state(
                controller.parameter_monitor,
                historical_data,
                0.5  # 初始效率
            )
            println("📊 參數監控器初始化完成")
        end
        
        # 初始化效能監控器
        controller.performance_monitor.monitoring_active = true
        println("📈 效能監控器初始化完成")
        
        # 設置基準效率
        controller.adjustment_engine.baseline_efficiency = 0.5
        println("🎯 調整引擎初始化完成")
        
        controller.system_status = MONITORING
        controller.last_update = now()
        
        println("✅ 系統初始化成功")
        return true
        
    catch e
        println("❌ 系統初始化失敗: $e")
        controller.system_status = ERROR
        return false
    end
end

"""
更新系統
"""
function update_system(
    controller::DynamicParameterController,
    current_data::Vector{Drawing},
    current_efficiency::Float64
)::WonderGridConfig
    
    if controller.system_status == ERROR || controller.system_status == SHUTDOWN
        @warn "系統狀態異常，無法更新"
        return controller.current_config
    end
    
    # 檢查更新間隔
    time_since_update = now() - controller.last_update
    if time_since_update < Minute(controller.update_interval)
        return controller.current_config
    end
    
    println("🔄 開始系統更新...")
    
    try
        controller.system_status = MONITORING
        
        # 1. 監控資源使用
        monitor_resources(controller.performance_monitor)
        
        # 2. 追蹤效能
        execution_time = 1.0  # 模擬執行時間
        track_performance(controller.performance_monitor, "system_update", execution_time, true)
        
        # 3. 檢查是否需要參數調整
        if should_trigger_update(controller.parameter_monitor, current_data)
            controller.system_status = ADJUSTING
            
            # 更新參數狀態
            new_state = update_parameter_state(
                controller.parameter_monitor,
                current_data,
                current_efficiency
            )
            
            # 執行參數調整
            new_config = adjust_parameters(
                controller.adjustment_engine,
                current_data,
                current_efficiency
            )
            
            if new_config != controller.current_config
                controller.current_config = new_config
                println("🔧 參數已調整")
            end
        end
        
        # 4. 檢查警告
        alerts = check_alerts(controller.performance_monitor)
        if !isempty(alerts)
            println("⚠️  檢測到 $(length(alerts)) 個警告")
            for alert in alerts
                println("  - $(alert["message"])")
            end
        end
        
        # 5. 自動優化
        if controller.auto_optimization && current_efficiency < 0.6
            controller.system_status = OPTIMIZING
            optimized_config = optimize_parameters(controller, current_data, current_efficiency)
            if optimized_config != controller.current_config
                controller.current_config = optimized_config
                println("🚀 參數已優化")
            end
        end
        
        controller.system_status = MONITORING
        controller.last_update = now()
        
        println("✅ 系統更新完成")
        
    catch e
        println("❌ 系統更新失敗: $e")
        controller.system_status = ERROR
    end
    
    return controller.current_config
end

"""
獲取系統狀態
"""
function get_system_status(controller::DynamicParameterController)::Dict{String, Any}
    
    # 生成效能報告
    perf_report = generate_performance_report(controller.performance_monitor)
    
    # 獲取監控報告
    monitoring_report = get_monitoring_report(controller.parameter_monitor)
    
    # 獲取調整歷史
    adjustment_count = length(controller.adjustment_engine.adjustment_history)
    recent_adjustments = adjustment_count > 0 ? 
                        controller.adjustment_engine.adjustment_history[max(1, end-4):end] : 
                        []
    
    status = Dict{String, Any}(
        "system_status" => string(controller.system_status),
        "last_update" => controller.last_update,
        "current_config" => Dict(
            "analysis_range" => controller.current_config.analysis_range,
            "top_pair_percentage" => controller.current_config.top_pair_percentage,
            "key_number_strategy" => controller.current_config.key_number_strategy,
            "trend_weight" => controller.current_config.trend_weight
        ),
        "monitoring" => monitoring_report,
        "performance" => Dict(
            "summary" => perf_report.summary,
            "alerts_count" => length(perf_report.alerts_summary),
            "recommendations_count" => length(perf_report.recommendations)
        ),
        "adjustments" => Dict(
            "total_count" => adjustment_count,
            "recent_count" => length(recent_adjustments),
            "baseline_efficiency" => controller.adjustment_engine.baseline_efficiency
        ),
        "configuration" => Dict(
            "update_interval" => controller.update_interval,
            "auto_optimization" => controller.auto_optimization
        )
    )
    
    return status
end

"""
優化參數
"""
function optimize_parameters(
    controller::DynamicParameterController,
    current_data::Vector{Drawing},
    current_efficiency::Float64
)::WonderGridConfig
    
    println("🚀 開始參數優化...")
    
    try
        # 獲取優化建議
        recommendations = get_adjustment_recommendations(
            controller.adjustment_engine,
            current_efficiency
        )
        
        if isempty(recommendations)
            println("📊 無優化建議可用")
            return controller.current_config
        end
        
        # 選擇最佳建議
        best_recommendations = recommendations[1:min(3, length(recommendations))]
        
        # 應用優化
        optimized_config = apply_parameter_adjustments(
            controller.adjustment_engine,
            best_recommendations
        )
        
        # 評估優化效果
        estimated_efficiency = evaluate_efficiency(
            controller.adjustment_engine,
            current_data,
            optimized_config
        )
        
        if estimated_efficiency > current_efficiency
            println("✅ 優化成功，預期效率提升: $(round((estimated_efficiency - current_efficiency) * 100, digits=1))%")
            return optimized_config
        else
            println("📉 優化效果不佳，保持原配置")
            return controller.current_config
        end
        
    catch e
        println("❌ 參數優化失敗: $e")
        return controller.current_config
    end
end

"""
關閉系統
"""
function shutdown_system(controller::DynamicParameterController)::Nothing
    
    println("🔌 關閉動態參數系統...")
    
    # 停止監控
    controller.performance_monitor.monitoring_active = false
    
    # 生成最終報告
    final_report = generate_performance_report(controller.performance_monitor)
    
    println("📊 最終系統報告:")
    println("  - 總操作數: $(final_report.summary["total_operations"])")
    println("  - 平均執行時間: $(round(final_report.summary["average_execution_time"], digits=2)) 秒")
    println("  - 整體成功率: $(round(final_report.summary["overall_success_rate"] * 100, digits=1))%")
    println("  - 總調整次數: $(length(controller.adjustment_engine.adjustment_history))")
    
    # 設置狀態
    controller.system_status = SHUTDOWN
    
    println("✅ 系統已安全關閉")
    
    return nothing
end

"""
獲取系統統計信息
"""
function get_system_statistics(controller::DynamicParameterController)::Dict{String, Any}
    
    stats = Dict{String, Any}()
    
    # 運行時間統計
    runtime = now() - controller.parameter_monitor.current_state.last_update
    stats["runtime_hours"] = runtime.value / (1000 * 3600)  # 轉換為小時
    
    # 調整統計
    adjustment_engine = controller.adjustment_engine
    stats["total_adjustments"] = length(adjustment_engine.adjustment_history)
    
    if !isempty(adjustment_engine.adjustment_history)
        successful_adjustments = filter(
            adj -> adj.success_score > 0, 
            adjustment_engine.adjustment_history
        )
        stats["successful_adjustments"] = length(successful_adjustments)
        stats["adjustment_success_rate"] = length(successful_adjustments) / length(adjustment_engine.adjustment_history)
    else
        stats["successful_adjustments"] = 0
        stats["adjustment_success_rate"] = 0.0
    end
    
    # 效能統計
    perf_monitor = controller.performance_monitor
    if !isempty(perf_monitor.tracker.execution_times)
        stats["avg_execution_time"] = mean(perf_monitor.tracker.execution_times)
        stats["max_execution_time"] = maximum(perf_monitor.tracker.execution_times)
    else
        stats["avg_execution_time"] = 0.0
        stats["max_execution_time"] = 0.0
    end
    
    # 資源使用統計
    stats["peak_memory_mb"] = perf_monitor.resource_monitor.max_memory_mb
    stats["peak_cpu_percent"] = perf_monitor.resource_monitor.max_cpu_percent
    
    # 警告統計
    stats["total_alerts"] = length(perf_monitor.alert_system.alerts)
    
    return stats
end

"""
重置系統
"""
function reset_system(controller::DynamicParameterController)::Bool
    
    println("🔄 重置動態參數系統...")
    
    try
        # 重置監控器
        controller.parameter_monitor = create_parameter_monitor()
        
        # 重置調整引擎
        controller.adjustment_engine = create_adjustment_engine(
            controller.parameter_monitor,
            controller.current_config
        )
        
        # 重置效能監控器
        controller.performance_monitor = create_performance_monitor()
        
        # 重置狀態
        controller.system_status = INITIALIZING
        controller.last_update = now()
        
        println("✅ 系統重置完成")
        return true
        
    catch e
        println("❌ 系統重置失敗: $e")
        controller.system_status = ERROR
        return false
    end
end

end # module DynamicParameterSystem
