
"smart_sources:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md": {"path":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09423607,-0.04984662,-0.00610075,0.00561108,0.01818035,0.02661984,0.03674512,-0.02438623,0.06018028,-0.01658892,-0.0038577,-0.05499648,0.00855465,0.02033969,0.00617672,-0.04405168,0.04406928,-0.05568213,-0.08405496,-0.00013288,0.05842519,-0.06575087,-0.01791439,-0.05325638,0.05425634,-0.02862919,0.00885832,-0.04076297,-0.00267439,-0.18535638,-0.0387702,0.04546314,-0.00671239,-0.05097,-0.02552533,-0.00949273,0.0406335,0.03821459,0.00551987,0.00656171,0.0149885,-0.00008916,0.04844487,-0.02902023,0.00411534,-0.0406008,-0.04490912,-0.02093751,0.06474952,-0.01391758,-0.08343606,-0.03558557,0.0091524,0.00525901,0.02032519,0.04025224,0.03602659,0.07717358,0.01636413,0.00077112,0.04152859,-0.01594739,-0.20828222,0.04322564,-0.00737865,-0.03543643,-0.02881159,-0.01379222,0.0073924,0.02952313,-0.03388559,0.04571233,-0.02897589,0.06593788,0.06448957,-0.05653816,-0.02514621,-0.04973097,-0.05066901,-0.03042474,-0.0104398,0.04790998,0.01737438,-0.00186047,0.00972303,0.06967523,0.03832724,-0.00444911,-0.00780881,-0.04422349,-0.0055691,-0.03476941,-0.01919756,0.07188745,-0.04239624,-0.01067795,0.04622634,-0.00465617,-0.05560476,0.07629924,-0.0066425,0.0287562,-0.04115142,-0.03530077,0.01568661,0.00815841,-0.01310842,-0.07436881,-0.00466581,0.02809425,0.00016224,-0.00653561,0.02360212,-0.03859373,-0.05826264,0.00915105,0.06809597,-0.03753102,-0.02005568,-0.02240107,0.0022345,0.02675973,0.01769114,0.00158333,-0.00383941,0.0101422,0.03523053,0.02512755,0.05684911,0.02759445,0.04523746,-0.01011975,-0.08791031,-0.04742146,-0.0337203,-0.01039287,-0.03397177,-0.032674,0.01029235,-0.0155949,-0.04141614,-0.05872368,0.04849165,-0.10602023,-0.06504756,0.094499,-0.05483836,-0.01194853,0.0058857,-0.01642952,0.02632315,0.02919442,0.02588724,-0.04884607,0.03947249,-0.03633061,0.06781377,0.17995623,-0.06463259,0.03870782,0.01135006,-0.05619217,-0.05651322,0.14154465,0.04297305,-0.0424758,-0.01489701,0.01211058,0.02189261,-0.0584625,0.04181128,-0.00005535,-0.04257495,0.03748746,0.10553409,0.01560031,0.06710341,-0.07527665,-0.04971133,0.05519548,-0.02616868,-0.04725656,-0.0892252,0.03761127,0.00206248,-0.10187713,-0.00581488,-0.06035892,0.03549949,0.00930573,-0.04504731,0.0487885,0.06570647,0.0439627,-0.0245077,-0.07056449,-0.03592678,-0.06193182,0.02922025,0.0014113,0.09231378,0.02271125,-0.02403925,-0.03168494,0.00953355,0.08371061,-0.00470035,-0.0226345,0.02257113,0.03762318,0.00206979,0.06035813,0.01515434,0.01115822,-0.01840914,0.04837829,0.00027162,0.02129924,-0.0212982,0.03927316,0.0099492,-0.00899651,-0.06831533,-0.23106007,-0.03095566,-0.02915026,-0.0418587,0.02837513,0.01223931,0.02911186,-0.00181784,0.10735517,0.07440086,0.07599687,-0.00423522,-0.01547918,0.02970098,0.00237799,-0.02292862,0.00180975,-0.02276411,0.00601662,0.04830537,-0.00983538,0.04203655,0.00860993,0.02536478,0.04812439,-0.04813582,0.12809227,-0.02290973,0.01653999,-0.0009443,0.06853638,0.06347723,0.01725305,-0.062756,0.02902522,0.03930822,-0.13940054,-0.01338645,-0.03855221,-0.03902755,-0.05489103,0.05071322,-0.01146545,-0.05937351,-0.02677442,-0.04720127,-0.00624915,0.0111944,-0.04421842,0.01522311,-0.00767638,0.04509451,0.03142402,0.03484331,0.03416739,-0.06737141,-0.02232639,-0.01033454,-0.03552808,0.00890143,-0.00752366,0.01639525,0.00326444,-0.01650125,-0.05216088,-0.0075093,0.00245882,0.03395825,-0.04233895,0.00702148,-0.0705869,0.12160102,0.00066096,-0.0396311,0.05306535,-0.01974442,-0.00639214,-0.01781284,0.00385994,-0.00887585,0.02800932,-0.02214728,-0.02869926,0.04923423,0.00164431,0.07269356,0.0325379,-0.00293244,-0.02864605,-0.01935614,-0.02294207,0.00854403,-0.03803558,0.00250613,0.09027884,0.02460062,-0.31201944,0.00733091,-0.01052621,-0.01320438,0.03548484,0.04878322,0.03085186,-0.02795178,0.01752037,0.03123618,-0.00417761,0.07579841,-0.03288855,-0.05978262,-0.03935941,-0.00822694,0.01317453,-0.01923975,0.02106594,0.00784339,0.0044009,0.01821075,0.22782435,0.05989015,0.0246075,-0.00119395,0.02471461,0.01153663,0.00030649,0.06380235,-0.02126443,0.01302772,0.10177356,0.02461649,-0.03645601,0.1243652,-0.04698161,0.04074499,-0.02142312,0.01587742,0.0163611,0.00582568,-0.05072717,0.02427676,0.0980292,0.01161191,-0.02467234,-0.07284632,0.04366683,0.05161713,-0.05237049,-0.00223503,-0.01630836,0.01064044,0.02707146,0.07349624,-0.00228851,-0.03529743,0.01177528,0.01814228,0.03516602,0.03781368,0.00636035,0.03220085,0.08959159],"last_embed":{"hash":"1xakqv1","tokens":497}}},"last_read":{"hash":"1xakqv1","at":1753495723832},"class_name":"SmartSource","last_import":{"mtime":1753491829852,"size":7505,"at":1753495676981,"hash":"1xakqv1"},"blocks":{"#":[1,6],"###一、什麼是數字差（Deltas）？":[7,11],"###一、什麼是數字差（Deltas）？#{1}":[9,9],"###一、什麼是數字差（Deltas）？#{2}":[10,11],"###二、如何分析數字差 (Deltas)？":[12,29],"###二、如何分析數字差 (Deltas)？#{1}":[14,15],"###二、如何分析數字差 (Deltas)？#{2}":[16,21],"###二、如何分析數字差 (Deltas)？#{3}":[22,26],"###二、如何分析數字差 (Deltas)？#{4}":[27,29],"###三、運用數字差（Deltas）制定策略":[30,56],"###三、運用數字差（Deltas）制定策略#{1}":[32,33],"###三、運用數字差（Deltas）制定策略#{2}":[34,37],"###三、運用數字差（Deltas）制定策略#{3}":[38,41],"###三、運用數字差（Deltas）制定策略#{4}":[42,46],"###三、運用數字差（Deltas）制定策略#{5}":[47,50],"###三、運用數字差（Deltas）制定策略#{6}":[51,56],"###四、實施與優化技巧":[57,65],"###四、實施與優化技巧#{1}":[59,59],"###四、實施與優化技巧#{2}":[60,60],"###四、實施與優化技巧#{3}":[61,61],"###四、實施與優化技巧#{4}":[62,62],"###四、實施與優化技巧#{5}":[63,64],"###四、實施與優化技巧#{6}":[65,65]},"outlinks":[]},"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08375422,-0.04191718,-0.00844233,0.01183345,0.01652446,0.02367806,0.05084423,-0.02320902,0.0561418,-0.01837013,-0.01028118,-0.05528431,0.01591273,0.01558929,0.01687415,-0.04602823,0.04339627,-0.05385023,-0.07915962,0.00138289,0.05367141,-0.05939575,-0.01889241,-0.0543796,0.05174216,-0.02837268,0.02159258,-0.04634556,0.00614177,-0.1931843,-0.04056602,0.03814784,-0.0201402,-0.05074873,-0.02619193,0.0021483,0.04706419,0.03778861,0.00973206,0.01012156,0.01560956,-0.00017717,0.03850222,-0.02614992,0.00378528,-0.04316758,-0.03094132,-0.02281263,0.06622607,-0.01416221,-0.07040514,-0.02986447,0.01174412,-0.00047364,0.02270711,0.04395277,0.03357209,0.08328056,0.0004018,0.00247851,0.03146614,-0.01024811,-0.20464835,0.04800124,-0.01916881,-0.03210904,-0.03081917,-0.00587658,0.00260042,0.02772186,-0.0291491,0.04192386,-0.03605015,0.0739866,0.06659226,-0.05443567,-0.02738585,-0.05077514,-0.05728954,-0.01878657,-0.0097397,0.04093635,0.01208515,-0.00456858,0.00439219,0.06850985,0.03846984,-0.0020094,-0.00135613,-0.04864467,-0.00820565,-0.03235566,-0.01477644,0.07904764,-0.04353217,-0.00106783,0.04929184,-0.00716315,-0.04783835,0.08092801,-0.00022636,0.02475903,-0.0431763,-0.02559748,0.01483553,0.008797,-0.00498927,-0.07261317,0.00063795,0.02955852,-0.00042666,-0.0042611,0.0178633,-0.03846509,-0.06076987,0.01270211,0.06565057,-0.03617656,-0.01564269,-0.03069849,0.00349324,0.03469072,0.02071309,0.00945408,-0.00861263,0.0014167,0.03289077,0.02656455,0.06816363,0.02765554,0.04885347,-0.01017727,-0.08819841,-0.05243982,-0.03529338,-0.00958215,-0.02919378,-0.03279208,0.00925058,-0.01332163,-0.04625833,-0.04986288,0.05356007,-0.1028527,-0.06792599,0.0910074,-0.04877224,-0.00634149,0.0102519,-0.01240973,0.0228693,0.02780301,0.02370477,-0.04395835,0.04169041,-0.0384697,0.07682263,0.17854711,-0.06602894,0.04436851,0.00335295,-0.05394595,-0.05386249,0.13925996,0.03974371,-0.03481895,-0.01700104,0.01076993,0.02245279,-0.06433138,0.03396857,0.00575115,-0.04544674,0.02859197,0.10084484,0.02024125,0.05191873,-0.07487749,-0.05045333,0.0520569,-0.02720554,-0.05114425,-0.09483971,0.03849653,0.00564571,-0.10499773,-0.00912524,-0.06345169,0.04063265,0.01092582,-0.04369881,0.04664171,0.05888185,0.04136937,-0.02196995,-0.06747298,-0.0322359,-0.06955822,0.03160506,-0.00052112,0.08121875,0.02395738,-0.02579113,-0.02665555,0.02033338,0.08707009,-0.01075184,-0.02709678,0.00945113,0.03637881,-0.00394372,0.06798294,0.00851298,0.01739515,-0.01642198,0.04631056,0.00513252,0.02141532,-0.02517936,0.03948694,0.01275441,-0.00938094,-0.06390636,-0.23602483,-0.03319026,-0.04400132,-0.03718011,0.02559288,0.01206045,0.03137509,0.00212426,0.10428125,0.07519346,0.07596713,-0.00566801,-0.00549809,0.02692819,0.00283948,-0.02581475,0.00561933,-0.02055285,0.00972101,0.04542391,-0.01915223,0.03723269,0.01184652,0.0279325,0.0535074,-0.05203453,0.13672528,-0.01704495,0.02907886,-0.00644424,0.07301942,0.06221823,0.00952189,-0.06862524,0.03092147,0.04034549,-0.14020202,-0.01091979,-0.04566294,-0.04416921,-0.05052397,0.04822032,-0.01475351,-0.06570746,-0.0319111,-0.0452462,-0.00460022,0.01469985,-0.0535919,0.01155405,-0.0026607,0.04623431,0.03394314,0.03834104,0.03684422,-0.06688137,-0.02417134,-0.00721934,-0.03995301,-0.0015167,-0.00575246,0.02091552,-0.00222442,-0.01088523,-0.03991489,-0.00696133,0.00346713,0.02192754,-0.04215157,-0.00043873,-0.07182246,0.12549926,-0.01037114,-0.04710709,0.06039128,-0.0248191,-0.00641949,-0.0263363,-0.00389993,-0.00551581,0.0259387,-0.02134378,-0.02447172,0.04314921,-0.00055548,0.07652248,0.03278335,-0.01014087,-0.03116776,-0.01669093,-0.02214298,0.01141689,-0.03692132,-0.00009001,0.0817109,0.02015976,-0.30588955,0.00070357,-0.00464226,-0.01918045,0.03292197,0.0402557,0.01729888,-0.03547199,0.0219692,0.03094362,-0.00922069,0.07121842,-0.03717331,-0.05060469,-0.03512619,0.00742328,0.02796521,-0.01628949,0.01739414,0.00557906,0.0005529,0.01619015,0.23430349,0.06115722,0.01963972,-0.00012131,0.02522875,0.01547446,-0.0052024,0.06664295,-0.02315529,0.01228994,0.10068702,0.02763186,-0.03142135,0.13005297,-0.05385552,0.04537735,-0.01899229,0.0058973,0.02772416,0.01019385,-0.0506414,0.01927397,0.09270179,0.00782228,-0.01278474,-0.05986005,0.03698406,0.05134171,-0.05626882,-0.00979561,-0.01716928,0.01545699,0.01806708,0.07348266,-0.00749462,-0.0376477,0.0138049,0.01794978,0.03294243,0.03468998,0.00619131,0.02670087,0.09345572],"last_embed":{"hash":"no0r8y","tokens":389}}},"text":null,"length":0,"last_read":{"hash":"no0r8y","at":1753495722806},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md#","lines":[1,6],"size":408,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###一、什麼是數字差（Deltas）？": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08702713,-0.04094228,0.00101112,-0.00509808,0.03504064,0.00222498,0.03969326,-0.02493817,0.05805241,-0.00647302,0.00150077,-0.06105726,0.00844525,0.01252025,0.0082306,-0.05286849,0.05624267,-0.04069686,-0.08776803,0.01321292,0.06677961,-0.04775775,-0.03052763,-0.03243016,0.04478761,0.01557339,-0.00281543,-0.0559884,0.0259457,-0.1611838,-0.0521249,0.04185018,-0.01724023,-0.03938455,0.00890238,-0.00002104,0.04676374,0.0337162,0.00278298,0.00142539,0.01628119,-0.0035796,0.05637443,-0.03345078,-0.00288105,-0.0437936,-0.04612699,-0.02006888,0.04656575,-0.01914614,-0.08073511,-0.00867214,-0.00628787,0.00415862,0.01063914,0.050483,0.03284257,0.07081775,-0.00250824,-0.01353611,0.04395182,-0.04211044,-0.20897785,0.06039643,-0.00475716,-0.01564608,-0.01019568,-0.01488773,0.01563778,0.05655974,-0.05301818,0.03259747,-0.03891977,0.06780206,0.04668454,-0.03547835,-0.01378388,-0.02525319,-0.0406237,-0.06270875,0.00656586,0.03382616,-0.0035604,0.02042577,0.0023674,0.06087765,0.02311493,-0.00854235,-0.00221887,-0.02711351,-0.03127975,-0.0650842,0.00211564,0.05611842,-0.04927211,-0.00815068,0.06149413,0.01113813,-0.07013189,0.08333171,-0.01960886,0.05191631,-0.0120317,-0.05639957,0.03871337,-0.00765585,0.009521,-0.06828071,0.0055082,0.00564152,0.00706465,-0.02342138,0.00483346,-0.03369955,-0.04097734,-0.00735083,0.08382118,-0.0124425,-0.01769605,-0.05624032,0.00086268,0.03293098,0.03251057,0.01885586,0.00957306,0.00061154,0.03306421,0.00485134,0.03270548,0.04012898,0.08289316,-0.02972872,-0.04967191,-0.05230479,-0.06314997,-0.03274942,-0.0141739,0.0006441,0.03085047,-0.00751753,-0.03277745,-0.07370217,0.03238175,-0.09980287,-0.05481785,0.11887868,-0.07609886,-0.01967919,-0.02448713,-0.02990436,0.01646942,0.01467178,0.0120059,-0.053116,0.04103584,-0.01443991,0.06187345,0.16254899,-0.05989438,0.03250951,0.01109808,-0.05599302,-0.08503847,0.1340872,0.08499626,-0.01605641,-0.00924605,0.01170421,0.02483458,-0.06486563,0.03547918,0.00235873,-0.03255605,0.02700306,0.08698548,-0.00545517,0.08432942,-0.06605091,-0.06042188,0.06031005,-0.0095214,-0.04021252,-0.05429824,0.04210228,0.0131807,-0.10900708,-0.03357903,-0.04864617,0.02341995,-0.01763216,-0.05503576,0.02968472,0.04996114,0.0477512,-0.04826225,-0.06582475,-0.01725179,-0.05142834,-0.00146779,-0.01792398,0.1155578,0.00321584,-0.0440683,-0.02756963,0.01624338,0.05388033,0.01957176,-0.01411451,0.02046051,0.0512441,0.00713343,0.06542662,0.01408099,-0.01256821,-0.00325085,0.04575241,-0.00423792,0.03994087,-0.04912125,0.04638892,0.02479143,-0.00385656,-0.06647729,-0.22155716,-0.03208322,0.0112073,-0.06695706,0.0293324,0.01657821,0.00902744,0.01139051,0.105843,0.0652548,0.05515976,-0.0033593,-0.01581709,0.01646983,0.01849794,-0.0071649,0.01918117,-0.03230214,0.02596111,0.01864061,0.00691865,0.02768287,-0.0051014,0.01941109,0.01794386,-0.05546603,0.12037875,-0.01582118,0.06091555,0.0073299,0.0589846,0.03325567,0.02791011,-0.07879278,0.04976866,0.03648152,-0.14356068,-0.03032613,-0.02985616,-0.05326315,-0.03384968,0.05295852,-0.0191992,-0.02908455,-0.03505537,-0.04533698,-0.0123275,-0.03049827,-0.03244615,0.03248203,-0.03282351,0.04949638,0.01399098,0.03152965,0.03676404,-0.03348376,-0.05556374,-0.01978905,-0.04241988,0.03246449,-0.03318536,0.01489173,-0.00832875,-0.0166188,-0.04435512,-0.01083889,0.01088887,0.02608327,-0.03025085,0.03478179,-0.11347538,0.1500642,-0.0137159,-0.05487932,0.08103547,-0.01639892,-0.01580085,-0.02028144,-0.0069627,-0.01816708,0.01459649,-0.03933475,-0.02209936,0.02923386,-0.01125306,0.06644275,0.03850445,-0.02390378,-0.00079555,-0.02216209,-0.03610688,-0.01692108,-0.03446297,-0.01027394,0.10563932,0.00609799,-0.28517511,0.00122511,-0.01072477,-0.0511859,0.02748893,0.06256627,0.03748759,-0.03444705,-0.02992718,0.04783279,-0.0286038,0.07317517,-0.03714824,-0.04087646,-0.02248046,-0.00755142,0.04603745,-0.01076259,0.02279406,0.00848558,-0.01033416,-0.00185332,0.20887387,0.06831739,0.04493745,0.00235046,0.02792455,0.01150114,-0.00666177,0.07536457,0.00745038,0.01435945,0.08438697,0.01380436,-0.0323238,0.13059294,-0.05456505,0.05515206,0.00647041,0.0296658,0.01759342,-0.01451916,-0.02809377,0.03410452,0.11755618,0.03547617,-0.01825537,-0.07283118,0.03369603,0.0272405,-0.04342224,0.02550576,0.01279383,0.02933495,0.03735134,0.07078625,-0.02307516,-0.04934645,0.00464899,-0.00536473,0.04121567,0.04307207,0.00399481,0.02681096,0.08510057],"last_embed":{"hash":"4ur8hp","tokens":202}}},"text":null,"length":0,"last_read":{"hash":"4ur8hp","at":1753495722936},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###一、什麼是數字差（Deltas）？","lines":[7,11],"size":211,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###二、如何分析數字差 (Deltas)？": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1169164,-0.05210729,0.00794319,-0.01890787,0.01500468,-0.00716524,0.00778374,-0.00257021,0.03246513,-0.00153629,0.01242628,-0.04516612,0.05030158,0.01447199,0.01926855,-0.0511389,0.05345584,-0.0407553,-0.09848197,-0.01403866,0.07937598,-0.06347572,0.01267989,-0.06844054,0.02695813,0.01243174,0.0093926,-0.05671759,0.00121864,-0.18545562,-0.05518406,0.01629968,-0.00446109,-0.05433293,-0.01962012,0.01039516,0.02776222,0.02417169,-0.0308027,0.01700933,-0.00112174,-0.01685161,0.04214001,-0.03763929,0.01990296,-0.02651541,-0.02124937,-0.0403474,0.06362378,-0.0157197,-0.07977506,-0.02320209,-0.04314843,0.0128072,0.02060319,0.02965225,0.01605729,0.07097205,-0.02374117,-0.00995166,0.03228176,-0.01603461,-0.20957157,0.05906996,-0.02760896,0.0082526,-0.01575526,-0.01775393,0.02116316,0.04290661,-0.04156534,0.02112908,-0.02516094,0.07214278,0.0696167,-0.03845863,-0.05112426,-0.06863064,-0.03983971,-0.03651893,0.02522309,0.01958788,-0.02035423,0.00159201,0.0274958,0.07107671,0.04270346,0.00011513,0.01300416,-0.01123197,-0.03241783,-0.03465229,0.00267977,0.07210904,-0.03480013,0.01950243,0.06767329,-0.01133558,-0.07106484,0.08404513,-0.0345689,0.01564647,-0.00640903,-0.04465983,0.03855785,-0.00437107,-0.00847126,-0.05732591,0.00173281,0.0079384,0.02410633,-0.00703558,0.02795742,-0.07938932,-0.06227685,-0.0188974,0.04861519,-0.03722675,-0.05711252,-0.02653269,0.02192101,0.03583257,0.0163191,0.00020106,0.0231835,0.0190403,0.01243712,0.03093646,0.03353592,0.06068158,0.07813083,0.00145877,-0.04728192,-0.04494436,-0.00764536,-0.02397453,-0.02023475,-0.03508596,0.01184563,-0.04249655,-0.063696,-0.0412071,0.06239884,-0.08911579,-0.05670366,0.10630995,-0.04918215,0.00615277,-0.02359447,-0.01622815,0.01567377,0.02202611,0.02212697,-0.0043668,0.05950579,0.00177608,0.0746905,0.14820139,-0.08376621,0.02687601,0.00249518,-0.06074608,-0.05648894,0.15856431,0.06538176,-0.04168333,-0.03090943,0.03973486,0.03457233,-0.06306847,0.00690613,0.02313598,-0.04226107,0.02758693,0.1063185,-0.0023633,0.03787177,-0.07917526,-0.05907837,0.05206535,-0.03178364,-0.02623394,-0.05161064,0.03929193,-0.03331159,-0.12054814,0.00848812,-0.0642509,0.0438746,0.008436,-0.07825176,0.04038224,0.0628362,0.05431574,-0.0291023,-0.05039787,-0.0112329,-0.02466861,0.0437995,-0.00228352,0.09592685,0.02101678,-0.0246227,-0.02286727,0.01359448,0.05425332,-0.01325616,-0.01978085,0.02076469,0.06862258,-0.00808385,0.05474381,0.00480404,-0.0045174,-0.00737504,0.04420749,-0.00192301,0.02889626,-0.02881286,0.0395885,0.00608842,0.03885033,-0.06045105,-0.2397932,0.01973481,-0.02420364,-0.04410269,0.03634478,-0.03930221,-0.00327939,-0.01349651,0.10067271,0.04897147,0.07814428,0.00021076,0.00143082,0.03513921,0.00445026,-0.0067517,0.02970999,-0.01758476,-0.00195702,0.0282659,-0.00391027,0.00936111,0.03389911,0.0148137,0.04328861,-0.02666128,0.14283456,0.00415833,0.07518683,0.0094068,0.07893138,0.04647685,0.02988815,-0.07115215,0.03229103,0.05638408,-0.10552623,-0.02223294,-0.0121785,-0.04993604,-0.02066633,0.05484299,-0.00680451,-0.08679511,-0.02073675,-0.04359983,-0.04149197,-0.04693982,-0.01224061,0.02393574,-0.0214251,0.04833603,0.04043867,0.04867008,0.04084854,-0.05470559,-0.07883722,-0.01543474,-0.04601852,-0.00892911,-0.04229886,-0.00492801,-0.01050961,-0.02311234,-0.01467819,-0.01528195,0.01206245,0.04131626,0.00059637,-0.02450632,-0.05951242,0.15892082,0.01205833,-0.00500388,0.06239907,-0.02473321,-0.00429676,-0.05289236,-0.01825282,-0.00393363,0.00553615,-0.00343658,0.00120718,0.03561881,0.00449767,0.03868665,0.02804772,-0.01837627,-0.01561239,-0.03035994,-0.02228466,-0.03889374,-0.03563389,0.0018258,0.07996276,0.01812265,-0.29085237,0.00177438,0.01242489,-0.05460395,-0.00603797,0.05358153,0.04349028,-0.02753812,-0.00880556,0.039358,-0.05278637,0.05673075,-0.00964749,-0.05594519,-0.00507789,0.00631928,0.01295443,-0.02963539,0.02475047,0.01525806,-0.01061163,0.0274266,0.19709401,0.04752917,0.01933968,0.0286695,0.01991959,0.01746395,0.00310477,0.07727188,-0.03824589,0.01941545,0.06520654,0.00195708,-0.04144858,0.14108455,-0.06505553,-0.00663743,-0.00883944,0.02625484,0.00903595,0.01371555,-0.01301808,0.03868206,0.12157291,0.01903744,-0.01171773,-0.06522986,0.01154214,0.05977521,-0.03455814,-0.00323084,0.00826158,0.03682673,0.02731728,0.07258295,0.00803525,-0.03105066,0.00522769,0.0036662,0.04705663,0.02338463,-0.00633058,0.00904953,0.06443787],"last_embed":{"hash":"29r4a2","tokens":464}}},"text":null,"length":0,"last_read":{"hash":"29r4a2","at":1753495722996},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###二、如何分析數字差 (Deltas)？","lines":[12,29],"size":773,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###二、如何分析數字差 (Deltas)？#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10657237,-0.03803806,0.00735422,-0.00212756,0.01881775,0.00022306,0.01132749,-0.01507045,0.0349495,0.00850966,0.00562024,-0.06023547,0.04699352,0.0083964,0.02809135,-0.03555544,0.04260944,-0.04771753,-0.09413645,0.00596856,0.06396419,-0.04413676,-0.00470222,-0.05741619,0.03618195,0.00362176,0.01539985,-0.0594366,0.02096768,-0.17995308,-0.05114705,0.01960862,-0.02847775,-0.06041488,-0.01066582,-0.00606695,0.03807861,0.04259684,-0.00896322,-0.00225853,0.00953825,-0.01780042,0.04253647,-0.01018732,0.01203103,-0.01388302,-0.0355504,-0.03556104,0.0684948,-0.02398361,-0.08770599,-0.01863093,-0.01310344,-0.00269203,0.03485073,0.03986666,-0.00910157,0.05509968,-0.01245657,-0.03477606,0.04807647,-0.01568495,-0.20251451,0.05778167,-0.0390467,-0.00111011,-0.01674192,-0.01776969,0.00625168,0.0567847,-0.03830486,0.02008929,-0.01955048,0.08357623,0.05916014,-0.03489279,-0.05559667,-0.05161678,-0.04385889,-0.03888659,0.0212141,0.02859908,-0.01762556,0.00643409,0.01853701,0.06723761,0.03875017,0.00287603,0.00533199,-0.00863825,-0.02764015,-0.02727601,-0.00512937,0.06117238,-0.05424839,0.03824493,0.05010175,-0.01754445,-0.05696711,0.0746497,-0.01267726,0.03552617,-0.01711231,-0.03259882,0.01106795,-0.01093693,-0.01016951,-0.06406405,0.00148497,0.00387697,0.01654707,-0.0018672,0.03546689,-0.06378848,-0.0606756,-0.01199062,0.05408121,-0.01134784,-0.06078842,-0.0341142,0.0185492,0.03423957,0.01603169,0.00839182,0.01499782,0.01280224,0.02761522,0.04106114,0.04070083,0.0461873,0.06927655,-0.01298047,-0.05300876,-0.0350619,-0.03247912,-0.02213195,-0.02176497,-0.01339891,0.01143455,-0.04890623,-0.05791542,-0.04932179,0.03216546,-0.09027682,-0.04066574,0.09777265,-0.06463391,0.00377145,-0.01368079,-0.01855568,0.01906642,0.01653331,0.01097286,-0.02903705,0.04529672,0.0002848,0.06120593,0.15808754,-0.07502143,0.02481504,-0.00738149,-0.05651048,-0.06354873,0.14229658,0.06275935,-0.01527158,-0.01990262,0.02886699,0.03492862,-0.06243267,0.02073396,0.00881202,-0.04683532,0.02494703,0.10593168,0.00608228,0.05486663,-0.07282888,-0.06023548,0.0529446,-0.01147224,-0.04492451,-0.06479938,0.0166972,-0.01612852,-0.12930654,-0.01601493,-0.06063066,0.02873554,0.01023503,-0.07823834,0.02478042,0.03864428,0.05271016,-0.03676093,-0.05758498,0.00673881,-0.04219024,0.04753183,-0.00076803,0.10327036,0.01766793,-0.0296797,-0.02877443,0.01109216,0.04482924,0.0329266,-0.03451106,0.0080868,0.07284579,-0.01283861,0.0637644,0.01398457,-0.01742405,-0.01026453,0.05158711,-0.00175713,0.02801102,-0.0463712,0.0367854,0.0207344,0.01681181,-0.04846235,-0.23993449,-0.00419663,-0.01161367,-0.05717606,0.03826986,-0.02219447,0.00273008,0.02008465,0.10059738,0.06436617,0.06075064,-0.00761742,-0.01537847,0.02268279,0.00136186,-0.01541359,0.01182759,-0.02788605,0.01820718,0.00915552,-0.00991654,0.01695651,0.02748521,0.01377005,0.03755202,-0.05017382,0.14143506,0.00272108,0.08381178,-0.01199589,0.07900207,0.03905798,0.0397025,-0.06215823,0.03367104,0.03627557,-0.12023722,-0.02940756,-0.03574025,-0.05267189,-0.00952788,0.05933668,-0.0297788,-0.06217599,-0.02546888,-0.04626419,-0.03822,-0.02591364,-0.03399811,0.03966967,-0.04731095,0.04043712,0.05452538,0.05824503,0.04811855,-0.05869622,-0.07475154,-0.02092731,-0.05678445,0.00290931,-0.03924603,0.01537203,-0.00242814,-0.01426945,-0.02435379,-0.0094851,0.01332879,0.01508697,-0.01136361,-0.00443213,-0.07256144,0.1553525,0.00597732,-0.02953636,0.0825405,-0.03072503,-0.03284902,-0.04964098,-0.01421011,-0.01415837,0.01758799,-0.01942661,-0.01025215,0.03022184,0.00369479,0.05430573,0.03896244,-0.03491093,0.00047497,-0.03342176,-0.04357522,-0.01932546,-0.02914678,0.00529252,0.09095857,0.00838846,-0.29456654,0.00591206,0.01047134,-0.05417536,0.01310139,0.05338101,0.04552167,-0.02322553,-0.00322987,0.03582333,-0.04760329,0.05459069,-0.02564347,-0.04657965,-0.01192019,0.00122283,0.02619101,-0.00804575,0.02285603,-0.00705358,-0.01372718,0.02443808,0.20885988,0.07777775,0.02756652,0.01271965,0.04246542,0.02512428,0.00068609,0.07994524,-0.04641656,0.02049513,0.07497823,0.0068501,-0.03513613,0.14996479,-0.06531255,0.03576533,-0.00622492,0.01599176,0.01490201,0.0118259,-0.00457197,0.0423715,0.12229437,0.02653413,-0.00165432,-0.05644689,0.0209079,0.04123827,-0.03473531,0.02182135,0.0090474,0.0380309,0.01923038,0.09479744,-0.00412661,-0.02998118,0.01890474,0.01933004,0.0332121,0.00952249,0.00566135,0.02681944,0.06717001],"last_embed":{"hash":"w14e78","tokens":295}}},"text":null,"length":0,"last_read":{"hash":"w14e78","at":1753495723134},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###二、如何分析數字差 (Deltas)？#{2}","lines":[16,21],"size":320,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###二、如何分析數字差 (Deltas)？#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08864703,-0.04891726,0.02972668,-0.0183427,0.01559371,-0.0127985,0.04425915,-0.00868103,0.03808343,-0.03641317,0.01350892,-0.06047178,0.0276078,0.01300978,0.00582391,-0.0567407,0.03987655,-0.05130737,-0.08332068,0.02427697,0.09254558,-0.05477854,-0.01467108,-0.05218651,0.03908395,-0.00766394,0.01383775,-0.06036612,0.00344423,-0.19880399,-0.05793893,0.02722325,-0.01837656,-0.04371343,0.00156549,-0.01758365,0.03954494,0.05225137,-0.03496805,0.03094753,0.01172452,-0.03303121,0.04851852,-0.03210662,0.01162367,-0.0404837,-0.03397815,-0.05292065,0.06349826,-0.01594307,-0.07030012,0.00128854,-0.03169179,0.01948686,0.02382967,0.05698388,0.04728451,0.06321239,-0.0124129,0.0021624,0.03794103,-0.02163194,-0.21233226,0.06362154,-0.00568404,0.01573131,0.01467708,0.01537717,-0.00987325,0.07939094,-0.04628318,0.0189822,-0.03896997,0.06924527,0.04384873,-0.02814391,-0.04397175,-0.07945221,-0.05476398,-0.0259719,-0.00281551,0.0357408,-0.03161623,0.00583961,-0.01642789,0.05021012,0.04086735,-0.00672972,-0.00170655,-0.00845063,-0.01062793,-0.05418257,-0.00589743,0.0748972,-0.05681251,-0.00219675,0.07068957,0.01901176,-0.03900947,0.09181703,-0.02297827,0.04241604,0.00501068,-0.05822635,0.02344307,-0.00906622,-0.00013556,-0.06399077,0.01822096,0.00363461,0.00226016,-0.03176556,0.02932521,-0.06287125,-0.07138464,-0.00133332,0.07311569,-0.02788234,-0.04996883,-0.04523886,0.02509904,0.04657894,0.03656052,0.00263254,0.01640035,-0.00115879,0.02033268,0.00380696,0.03222761,0.04603516,0.06309452,0.01319921,-0.01541509,-0.06682044,-0.01258881,-0.03389291,0.00822423,0.01552916,0.00719659,-0.05201831,-0.06501324,-0.08345319,0.05409045,-0.08356708,-0.05209585,0.10426075,-0.05463818,-0.02759367,-0.01777153,-0.00040141,0.02978657,0.0327003,0.01834084,-0.03784382,0.03239459,0.00734571,0.08971179,0.13515717,-0.05818665,0.02430823,0.0126571,-0.05910753,-0.04759592,0.13494204,0.05429265,-0.02266728,-0.02370332,0.01283079,0.0313883,-0.08298149,0.01534721,0.01287086,-0.06246812,0.03649676,0.09656738,-0.01005599,0.05652858,-0.06968188,-0.0677784,0.06134076,-0.0205212,-0.01909005,-0.03150994,0.06067022,-0.01725734,-0.11881418,-0.03618491,-0.0546002,0.04151114,0.00491405,-0.09379842,0.03430536,0.02103527,0.06980751,-0.04773102,-0.07455148,-0.02590959,-0.03233496,0.02617527,-0.0064447,0.0936225,-0.00764229,-0.05029345,-0.03166084,0.02582063,0.05810935,0.01114685,-0.01885027,0.02012568,0.04281467,-0.00926911,0.06117217,-0.01086854,0.00038797,0.03321327,0.05272015,0.01627124,0.03900656,-0.04326445,0.01434244,0.02426486,-0.00957108,-0.03968491,-0.21185787,0.00879023,0.00883113,-0.071118,0.01239125,-0.02003728,0.00489812,0.00341225,0.08654602,0.09395593,0.03482008,-0.02776279,0.0031398,0.03762456,0.01524612,-0.02527904,0.01700846,-0.0159025,0.00928837,0.02674663,0.01175616,0.0133622,0.02383362,0.0102206,0.04543673,-0.02902418,0.14387088,-0.01114177,0.08059173,0.01826046,0.06570214,0.05620326,0.03403366,-0.04857573,0.0471352,0.05093593,-0.11832881,-0.03570292,-0.02163131,-0.05009957,-0.02364869,0.03522351,-0.03170887,-0.05000344,-0.03843857,-0.04567681,-0.01936244,-0.042262,-0.041531,0.03653,-0.04062689,0.07176022,0.05665244,0.02448087,0.04007794,-0.02923172,-0.05450118,-0.02948201,-0.06865788,0.01299577,-0.04558338,0.01738811,-0.03052008,-0.01735884,-0.04584083,0.00716117,0.00304896,0.01991498,-0.00269804,-0.00721876,-0.0700359,0.12870623,-0.01087076,-0.06790503,0.08179455,-0.00733707,-0.02144579,-0.0331854,-0.0070699,-0.00807991,0.02462355,-0.00646482,0.00396216,0.02633394,-0.00108726,0.05418731,0.02332912,-0.01691872,0.0009605,-0.02414846,-0.01585624,-0.01781626,-0.02043082,-0.02020627,0.07001955,-0.02833219,-0.28619125,-0.00293553,0.01089929,-0.03834339,0.01824692,0.06412572,0.0177428,-0.00046163,0.00206346,0.04143808,-0.04364645,0.05807607,-0.0124947,-0.06186503,-0.02088672,-0.0160617,0.01083498,-0.04331812,0.0351134,0.04113543,-0.00778197,0.00125991,0.20595454,0.06601599,0.01729188,0.0220133,0.04628252,0.01897379,-0.00366496,0.07407206,0.02792784,-0.00930299,0.08994663,-0.01014243,-0.04027825,0.10952716,-0.07083866,0.03242141,0.03984598,0.02471559,0.02081453,0.00715696,-0.02777113,0.01240639,0.11893035,0.03014824,-0.02259581,-0.08385736,0.03054592,0.04718243,-0.0228756,0.01771612,0.02124544,0.01085134,0.03608602,0.07166541,-0.0098818,-0.03902443,-0.01152622,0.01131256,0.00795827,0.02765835,0.01019013,0.01358075,0.08421158],"last_embed":{"hash":"2911db","tokens":218}}},"text":null,"length":0,"last_read":{"hash":"2911db","at":1753495723224},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###二、如何分析數字差 (Deltas)？#{3}","lines":[22,26],"size":243,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11144278,-0.03870697,0.01472891,0.01442412,0.02063726,0.00127405,0.03269242,-0.02132504,0.07018039,-0.00502872,-0.00600001,-0.0664077,0.03193909,0.00832672,0.02834191,-0.04196988,0.02540541,-0.03979471,-0.06730177,-0.01726323,0.08383629,-0.04670027,-0.00264425,-0.03864143,0.06539299,0.00906179,0.0044112,-0.06529094,0.01203145,-0.18410996,-0.05133845,0.02095252,-0.02178119,-0.03857823,-0.02182562,-0.00022251,0.03833712,0.02811309,-0.00648368,0.00782213,0.02157494,-0.00089529,0.06313773,-0.03228088,-0.01804089,-0.02690201,-0.0427536,-0.02784304,0.057779,-0.01238095,-0.0562121,-0.00548948,-0.00271129,0.01784124,0.01764846,0.04225124,0.02078098,0.07805987,-0.0302508,-0.01230698,0.02876603,-0.03580614,-0.21069539,0.04989851,-0.00505126,-0.01634524,-0.01660112,-0.01526583,0.02384854,0.03337268,-0.04375844,0.02854697,-0.01596777,0.06587481,0.04441844,-0.02796544,-0.02638015,-0.04131876,-0.04437643,-0.03457881,0.0035361,0.02558644,-0.01020879,0.0232893,0.03581027,0.05083691,0.03912012,-0.00872469,0.00613335,-0.03427279,-0.01939774,-0.06453785,-0.01892313,0.07404766,-0.04355628,0.0348711,0.0611098,0.0243008,-0.07078206,0.07646956,-0.00384886,0.04527711,-0.02670234,-0.05473809,0.05418334,-0.00459133,0.01357396,-0.04901397,0.0048103,0.01233172,0.00392962,-0.01582718,0.01139485,-0.07603648,-0.04967337,0.01435086,0.07628506,-0.02156005,-0.03619069,-0.05323214,0.02509219,0.03678461,0.01478459,-0.01084999,0.01537446,0.00116172,0.02542071,0.03521224,0.03139017,0.03559417,0.07244026,-0.01652251,-0.05589963,-0.04870072,-0.06075864,-0.02930521,-0.02634891,-0.00507922,0.01386198,-0.0262795,-0.04324163,-0.06291115,0.03097066,-0.09909339,-0.06302331,0.0938643,-0.04786246,-0.01032159,-0.01683966,-0.0291557,0.00988957,0.04244739,0.02073837,-0.04920663,0.03874835,-0.02317694,0.06342689,0.14907944,-0.0387249,0.05057219,0.00468098,-0.05225766,-0.06425972,0.13571401,0.0617063,-0.01465629,-0.0187489,0.04844571,0.03088375,-0.03300013,0.02154475,-0.01162516,-0.0215223,0.00973702,0.07974871,0.00223373,0.06773929,-0.0612297,-0.07755995,0.06084408,-0.02480214,-0.04084289,-0.08212256,0.03611283,-0.00670626,-0.11170604,-0.00373337,-0.0424665,0.02204294,0.00171769,-0.07784389,0.04790561,0.04192185,0.04195421,-0.05001425,-0.07759017,-0.00675623,-0.04720318,0.02040622,-0.00647357,0.10782858,-0.01081024,-0.04304797,-0.04374916,-0.01742882,0.06278756,-0.00400324,-0.02464416,0.00449479,0.03353338,-0.01219482,0.06991699,0.01097969,-0.0086809,0.00440919,0.06154052,-0.01927095,0.03789195,-0.03761813,0.04896228,0.01874472,-0.01499382,-0.08037941,-0.23366539,-0.01425252,-0.00973332,-0.05166144,0.02848632,-0.03165974,0.01688615,0.01396229,0.08584367,0.04897455,0.04922038,0.01799085,-0.01740634,0.01688255,0.01674933,-0.02977651,0.02972669,-0.02145341,0.01880834,0.0290335,-0.00895219,0.01776564,0.02865797,0.04120935,0.02839029,-0.06328112,0.11886968,-0.0202959,0.06124096,0.01026131,0.06555558,0.03543582,0.02695618,-0.06644174,0.03375165,0.04553116,-0.12678137,-0.01043431,-0.0330352,-0.05487585,-0.01094665,0.04957508,-0.01817937,-0.05021708,0.00030323,-0.06893603,-0.00700853,-0.03303209,-0.06465044,0.02728738,-0.00857092,0.04900092,0.04829975,0.03646915,0.0217281,-0.05932436,-0.05477211,-0.02569236,-0.04618182,-0.00804503,-0.02890307,0.00920779,0.01157552,-0.01151102,-0.02327106,0.01369744,0.01156878,0.04280531,-0.02611227,0.01514002,-0.10268243,0.14625202,-0.0068438,-0.04799258,0.06885306,-0.02677025,-0.03538134,-0.04835131,-0.00148242,0.00455979,0.02481866,-0.01620707,-0.00615522,0.03057402,0.00496923,0.07842677,0.03694182,-0.02211666,-0.01094975,-0.02991819,-0.02665759,-0.00596075,-0.04185076,-0.00600069,0.07082987,0.0229216,-0.30206946,0.00260828,-0.00549275,-0.05551992,0.0143274,0.04684201,0.03847334,-0.03498847,-0.02260756,0.03622705,-0.04308444,0.07279667,-0.01162759,-0.0392197,-0.01265073,0.01533383,0.02310403,-0.03390846,0.03198698,0.00784188,-0.01383029,-0.00224321,0.21458662,0.08130572,0.03783275,0.00883817,0.04313411,0.0180926,0.00783332,0.07031706,-0.01833012,0.02404779,0.09330489,0.04414058,-0.01690024,0.14064245,-0.0622602,0.01956579,-0.00908894,-0.0010708,0.03140568,0.00478704,-0.01769894,0.04587046,0.10930315,0.01635938,-0.02817607,-0.07111656,0.02248641,0.01885071,-0.03940207,0.02167393,0.00342658,0.02300919,0.03829161,0.08454734,-0.01173478,-0.03742214,0.02705508,0.00727169,0.04617407,0.06155028,-0.00380129,0.04203144,0.07857697],"last_embed":{"hash":"1s9g2hq","tokens":454}}},"text":null,"length":0,"last_read":{"hash":"1s9g2hq","at":1753495723288},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略","lines":[30,56],"size":1258,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09900254,-0.03975929,0.01297103,0.01130358,0.01874433,0.00526615,0.03333867,-0.02456424,0.06388784,-0.00523315,-0.00021993,-0.06648907,0.03022601,0.00785547,0.02463117,-0.04634,0.02942899,-0.04634999,-0.06266634,0.00026476,0.07489618,-0.03982041,-0.02793992,-0.02730169,0.05062164,0.0088258,0.00609749,-0.06910085,0.01206289,-0.18773217,-0.04879406,0.02770417,-0.02531363,-0.04670764,-0.00077671,0.00417326,0.04452561,0.0390192,-0.00946227,0.00703258,0.02416508,0.00011668,0.06680925,-0.02326826,-0.01023539,-0.02726929,-0.06075133,-0.01426514,0.05906128,-0.01294947,-0.06273158,-0.00682855,-0.00578219,0.01360213,0.01660953,0.05418205,0.00868047,0.07680039,-0.02211503,-0.00898404,0.03035842,-0.0385756,-0.2122429,0.05036377,-0.01205158,-0.01081016,-0.00780103,-0.01583062,0.01711912,0.04666996,-0.04956621,0.0261248,-0.00909525,0.06733528,0.04041925,-0.02593459,-0.02778827,-0.04775584,-0.05132959,-0.02978151,0.00350672,0.03166674,-0.02060168,0.0213842,0.02329236,0.04748661,0.04960737,-0.00593266,0.00427684,-0.02486427,-0.02285947,-0.060949,-0.01574456,0.06755505,-0.04577045,0.0263131,0.05968654,0.02218162,-0.06466043,0.07651047,-0.00055204,0.05578029,-0.02036747,-0.05981543,0.04603301,-0.00390717,0.00685694,-0.05162343,0.00442557,0.01586442,0.00872234,-0.02181304,0.01482043,-0.07207952,-0.04789721,-0.00489388,0.0752783,-0.01151061,-0.03277288,-0.06404708,0.02411184,0.04535284,0.02321895,-0.0010747,0.00833455,0.01135597,0.02952824,0.03822345,0.02674855,0.03626163,0.06886616,-0.01272685,-0.05244918,-0.05404315,-0.04914185,-0.03177092,-0.02764777,0.01245105,0.01422275,-0.03119724,-0.05833764,-0.06768578,0.04034969,-0.08580225,-0.0581033,0.10137689,-0.05946507,-0.01398049,-0.01435179,-0.03169825,0.01754679,0.02627648,0.01889206,-0.04913169,0.03659726,-0.01628312,0.05852029,0.14644116,-0.0441598,0.05210469,-0.0077772,-0.05465199,-0.06558325,0.13071135,0.06989732,-0.02368305,-0.01388922,0.05155808,0.03313886,-0.03005945,0.01937999,-0.0150252,-0.03067092,0.01810998,0.08597444,0.0029668,0.06608636,-0.05227241,-0.06424706,0.05548783,-0.01238547,-0.04164166,-0.08135357,0.03355784,0.00144314,-0.11096032,-0.01543425,-0.04547523,0.0179194,-0.00427547,-0.07612679,0.0281141,0.0456718,0.04248791,-0.04111735,-0.0813977,-0.00994464,-0.04198371,0.0283892,-0.01669256,0.11554548,-0.0123217,-0.04232109,-0.04269534,-0.00664895,0.07030797,0.00527964,-0.01687284,0.00773111,0.02663296,-0.00923172,0.07123562,0.01136074,-0.01590178,0.01628671,0.06495994,-0.01636746,0.04152637,-0.04837114,0.04542497,0.0216701,-0.01596596,-0.07413938,-0.22810788,-0.01803267,-0.00005985,-0.05563519,0.02674355,-0.01537148,0.00422564,0.02309944,0.09275004,0.05461198,0.03338701,-0.0005527,-0.01553521,0.02586247,0.00312363,-0.03296171,0.02494649,-0.01652951,0.03045038,0.0110637,-0.0063111,0.02124842,0.01721368,0.02439418,0.03543244,-0.06191505,0.11984634,-0.01587052,0.06855538,-0.00273968,0.06821265,0.02046989,0.03439619,-0.05534219,0.03676347,0.0447171,-0.12845236,-0.04150078,-0.03527033,-0.05061589,-0.00713715,0.0442879,-0.03126204,-0.0565186,-0.0067941,-0.05898502,-0.01354237,-0.02416044,-0.06549473,0.03791209,-0.032435,0.04208466,0.05658961,0.03538998,0.01904531,-0.05858937,-0.05797418,-0.02544872,-0.05788967,0.0116182,-0.03034966,0.0111149,0.00484634,-0.01014476,-0.02345729,0.01514837,-0.00314775,0.0324909,-0.02173063,0.01269652,-0.09987164,0.13537088,-0.00680276,-0.06571589,0.07253091,-0.02723905,-0.03586085,-0.05498542,-0.00128277,-0.00257056,0.01619594,-0.02308939,-0.01020349,0.03764464,0.00050572,0.07368866,0.0466705,-0.01634642,-0.00053437,-0.02993873,-0.03074018,-0.00797294,-0.03221743,-0.0022806,0.08019952,0.01183847,-0.29511917,0.00014681,-0.01876698,-0.05746591,0.02228397,0.05582979,0.03759235,-0.01979325,-0.01832152,0.03580571,-0.0365743,0.06328139,-0.02268551,-0.0343998,-0.01249512,0.01039266,0.01417934,-0.03511494,0.04219642,0.00977011,-0.01385732,-0.00146691,0.21337706,0.0831821,0.03252503,0.01363352,0.03850574,0.02545754,-0.00282973,0.08577301,-0.00498289,0.02889297,0.09324662,0.03262395,-0.02881356,0.15269144,-0.06915686,0.02918849,-0.00004473,0.01137911,0.0388078,0.00384852,-0.00625148,0.04266259,0.11500505,0.01605507,-0.02758513,-0.07367951,0.02836251,0.01690631,-0.0455799,0.02957053,-0.00066379,0.02144057,0.03822327,0.09701575,-0.0166223,-0.04058271,0.00942401,0.00647223,0.04339981,0.05537747,-0.00206877,0.03679765,0.0622362],"last_embed":{"hash":"exo80b","tokens":321}}},"text":null,"length":0,"last_read":{"hash":"exo80b","at":1753495723445},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略#{2}","lines":[34,37],"size":346,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08840484,-0.02853425,0.00446207,0.02880554,0.03297592,-0.00265129,0.05224422,-0.04137335,0.05884433,-0.01176932,-0.01707531,-0.08561952,0.03258225,0.01240706,0.04142537,-0.03610298,0.02902008,-0.00817267,-0.04669148,-0.01153767,0.08149291,-0.043325,-0.02238784,-0.0242074,0.05430724,0.0060918,0.00323657,-0.07659074,0.00291226,-0.18299286,-0.04800758,-0.00287001,-0.03776721,-0.06587761,0.0111348,0.01077312,0.04234894,0.03727126,-0.01970432,0.00406406,0.01093935,0.00298403,0.05846917,-0.04901135,-0.01122734,-0.01623606,-0.05476722,0.00225681,0.03951564,-0.02820115,-0.04588925,0.00457094,-0.01427132,0.01471991,0.00646133,0.05582261,0.01863616,0.0855525,-0.02299487,-0.00314618,0.0364351,-0.01700965,-0.18628521,0.02575856,0.01081006,-0.00580298,-0.01132739,-0.02275986,0.02591414,0.04442462,-0.062194,0.02314697,-0.02436242,0.08113392,0.01068744,-0.03361844,-0.00241958,-0.04353742,-0.06081688,-0.02845674,0.01310421,0.03354372,-0.01450407,0.01981758,0.01595682,0.0524651,0.02747246,0.00093078,-0.02083063,-0.03372072,-0.0194483,-0.05256107,0.00769115,0.05259357,-0.03467787,0.02606791,0.05237571,0.03828271,-0.05821373,0.08162731,0.01288339,0.05009273,-0.03658447,-0.0608556,0.05300696,-0.01006316,0.02159932,-0.04852146,0.00078617,0.01644672,0.00146407,-0.04113255,0.0099624,-0.05372261,-0.04029797,0.01467417,0.07920428,-0.00742259,-0.05606511,-0.05349403,0.01860734,0.05020095,0.02325052,-0.00471435,0.00469383,-0.00212034,0.01197601,0.04266368,0.03337702,0.0268167,0.06708361,-0.00741856,-0.06903192,-0.06521744,-0.0590222,-0.0315605,-0.02543549,0.0287813,0.02971072,-0.03294884,-0.05744557,-0.06584065,0.03925271,-0.07813812,-0.05978492,0.10291813,-0.0717635,-0.01664836,-0.01934865,-0.01153945,0.02305595,0.03874617,0.00082735,-0.0593767,0.04485666,-0.04199882,0.05691582,0.15782298,-0.049258,0.05786168,0.01023092,-0.03795884,-0.0694921,0.13541621,0.05328004,-0.01864839,0.01257369,0.04754826,0.03857983,-0.02425967,0.01237254,-0.00870196,-0.03761913,0.00175306,0.06268902,-0.00456006,0.07846918,-0.04648276,-0.04917545,0.04646164,-0.02060531,-0.05204162,-0.08258195,0.04156212,0.01027187,-0.09251478,-0.01188361,-0.04545693,0.02020306,0.00610802,-0.10817085,0.03539896,0.02096191,0.03726564,-0.03961897,-0.07444716,-0.00996018,-0.04217086,0.01908339,-0.00396626,0.10374061,-0.00969725,-0.03765967,-0.03508531,0.01456977,0.06345529,-0.01076625,-0.02528563,0.00616805,0.03893128,-0.03078247,0.07368343,-0.00229472,-0.01141519,0.03490157,0.06306414,-0.01253888,0.02731806,-0.06268699,0.02535493,0.00964205,-0.01366915,-0.07790711,-0.246115,-0.0106728,-0.02429822,-0.0555041,0.04436392,0.00236919,-0.00016677,0.03923251,0.06861449,0.06988297,0.04460122,0.00179607,-0.02051925,0.02948245,0.01702657,-0.02384349,0.0333579,-0.01254368,0.0047751,0.01511691,-0.01508709,0.00685242,0.02570384,0.02540795,0.0420431,-0.03999345,0.13511799,-0.00716964,0.06950727,-0.00446624,0.06186379,0.00923271,0.01778466,-0.0834909,0.04177441,0.03481168,-0.12630713,-0.04858252,-0.03725937,-0.03719228,-0.00571149,0.03590223,-0.01716692,-0.06186751,0.00907857,-0.0613169,-0.02262341,0.00160772,-0.05841454,0.03105973,-0.01630338,0.05127154,0.03818762,0.04206614,0.01698059,-0.04983678,-0.03619492,-0.03139168,-0.06112911,0.01338468,-0.01176456,0.02514475,-0.02661975,-0.01936752,-0.01937654,0.01100411,-0.00019897,0.02879474,-0.00541379,0.00520089,-0.10748219,0.14213617,-0.00507723,-0.07962484,0.07116572,-0.0146273,-0.04532912,-0.0566393,-0.0100321,-0.0200987,0.03342726,-0.01315593,-0.01322907,0.02600756,-0.00746159,0.08145377,0.02718453,-0.01648264,0.00636035,-0.0304333,-0.03166568,-0.00250918,-0.03991786,-0.00528132,0.07788629,0.01551707,-0.29288143,-0.01465835,-0.01353278,-0.04251318,0.02723653,0.03994123,0.04403979,-0.0228065,-0.00844529,0.02010534,-0.04739442,0.07084832,-0.02682165,-0.01849029,-0.01775621,0.015378,0.03443626,-0.04690393,0.03873585,0.01600801,-0.01843079,-0.0070284,0.21408936,0.072565,0.0379567,0.02669893,0.05370523,0.04580813,-0.01893638,0.06502147,0.01103023,-0.00386755,0.09107902,0.03762294,-0.01744424,0.12974116,-0.07060567,0.04983107,0.01112777,0.01181078,0.02641122,0.0006868,-0.00670114,0.046535,0.11603465,0.00806372,-0.01525339,-0.06597365,0.02209202,0.02748125,-0.0325463,0.03266817,-0.0045105,0.00917434,0.04788508,0.10859404,-0.01542115,-0.04643478,0.00224652,0.017738,0.04739039,0.05024673,-0.01846696,0.02830236,0.07691427],"last_embed":{"hash":"77nj8w","tokens":263}}},"text":null,"length":0,"last_read":{"hash":"77nj8w","at":1753495723567},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略#{4}","lines":[42,46],"size":275,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11631931,-0.03736851,0.0096314,-0.0223384,0.02262687,-0.00133611,0.0240978,-0.0173066,0.05511471,-0.0198159,0.02358713,-0.07109587,0.03594505,-0.00486152,0.03176205,-0.05523696,0.05918832,-0.04864651,-0.09015401,0.02070991,0.05706766,-0.06865716,0.0105544,-0.07308108,0.05612122,-0.01142462,-0.00312296,-0.07003893,0.00521836,-0.19649702,-0.06694233,0.01587701,-0.03615475,-0.01998723,-0.02796234,0.00168988,0.05013797,0.05138769,0.00283994,0.00951449,0.01364315,-0.00012945,0.0434872,-0.04451731,-0.01229246,-0.04337567,-0.02500947,-0.03326385,0.07633031,-0.00143656,-0.03714705,-0.01919297,-0.02837411,0.03366919,0.00252783,0.05145414,0.0445033,0.08507933,-0.01040963,0.02721368,0.02405425,-0.01169142,-0.20882338,0.04245456,0.00271915,-0.02851292,-0.02222717,0.0190932,0.00722179,0.02588897,-0.06714521,0.05220323,-0.03755174,0.08724637,0.03506371,-0.04931821,-0.01398598,-0.03566534,-0.06177004,-0.01496373,-0.00669681,0.04153354,-0.0235538,0.01387582,-0.00525865,0.07137962,0.0137296,-0.01127473,0.00335477,-0.03331441,-0.04488764,-0.05274943,0.00715058,0.06168206,-0.04410902,0.00183546,0.06131575,0.00483607,-0.05668483,0.07519079,-0.01667291,0.02700357,-0.01298164,-0.0323473,0.03600135,0.0117566,-0.00649795,-0.03446794,0.01323831,-0.00349324,0.01116548,-0.02066964,0.01035054,-0.07794432,-0.05968715,-0.00544817,0.04674926,-0.03035747,-0.03205188,-0.03718676,-0.00126417,0.02267897,0.01165105,0.01933324,0.00943551,-0.01089259,0.01016876,-0.00109272,0.0425662,0.04566728,0.07604282,-0.02637422,-0.04314149,-0.06600296,-0.04300489,-0.03107664,-0.01496813,-0.02224104,0.02224665,-0.02639316,-0.05802894,-0.06598504,0.0448778,-0.0798769,-0.05961201,0.08561362,-0.05226361,-0.0031012,-0.00067851,-0.0147786,0.00665843,0.04700487,0.01363264,0.00074759,0.04524065,-0.00844485,0.07089497,0.12465018,-0.07667159,0.06227344,0.02375342,-0.05457047,-0.07734119,0.17159607,0.04368383,-0.01914689,-0.03996512,0.01333008,0.00539886,-0.07998472,0.01265057,0.00391799,-0.01062908,0.01403489,0.09892257,-0.00779889,0.0593727,-0.07066029,-0.08825115,0.0515483,-0.02311519,-0.03908003,-0.0736791,0.04540894,0.00206059,-0.07423902,-0.02881096,-0.04643771,0.03209379,-0.01688306,-0.05894661,0.05696424,0.04237973,0.0412703,-0.04381166,-0.05279675,-0.01127255,-0.06520554,0.0016296,-0.0114396,0.11771326,0.02787407,-0.03735146,0.00126877,0.02752231,0.05158362,-0.00842874,0.01089316,-0.01063979,0.05721648,0.0090805,0.06055822,0.01688102,-0.01651616,0.00045655,0.03598399,0.00549985,0.04490171,-0.02213741,0.06139706,0.02165808,-0.01500254,-0.08154128,-0.22764511,-0.00944025,-0.03017789,-0.04765456,0.04359252,0.01247151,0.02148751,0.01488686,0.06928785,0.04003302,0.07120042,0.01382775,0.00118477,0.00269806,0.02354269,-0.02153687,0.03838571,-0.01664194,0.01139067,0.04732221,-0.00831063,0.01251906,0.00138473,0.05110741,0.06467915,-0.03595277,0.13850795,-0.00693321,0.07053009,-0.00425865,0.07851882,0.07678688,-0.00078082,-0.10275554,0.03571807,0.01887198,-0.13373122,0.00632418,-0.04442029,-0.0241874,-0.03128865,0.04608883,-0.00352498,-0.05939454,-0.01637966,-0.06251272,-0.00620068,-0.03194407,-0.04190902,-0.02803143,-0.00706385,0.04095407,0.02707642,0.02256978,0.05923864,-0.058435,-0.04723111,-0.00775942,-0.03716037,-0.03373162,-0.01556729,0.02451057,0.01103304,0.00994211,-0.02968088,-0.00974235,0.02762147,0.04890914,-0.01016622,0.00956728,-0.07510838,0.1446521,-0.02317625,-0.04162991,0.08331256,0.00471439,-0.02043292,-0.00835987,-0.02589187,0.00547329,0.01619973,-0.022129,-0.00451282,0.00920989,0.0054792,0.07096994,0.04606356,0.00043938,-0.01568136,-0.02311543,-0.02036341,-0.01457381,-0.02363908,0.01435876,0.07582201,0.01083282,-0.28959626,0.00242264,-0.00968501,-0.03780943,-0.00109243,0.0657059,0.01840715,-0.04393401,-0.00918917,0.04599642,-0.03992067,0.07088969,-0.02798163,-0.04758477,-0.02358967,0.01160868,0.04279485,-0.02447886,0.03277668,0.00714571,-0.00868717,-0.0063586,0.23121096,0.03836732,0.01096577,0.009033,0.02499293,-0.00357106,0.01366059,0.08112999,0.00758389,0.00714079,0.0883363,0.02895749,-0.01391302,0.13280909,-0.04556729,0.04708086,-0.01096554,0.01773998,0.00590038,0.00288664,-0.02083478,0.01441007,0.08309358,0.01112295,-0.01908114,-0.06431982,0.03077617,0.02401261,-0.03483975,0.01798911,-0.00705453,0.03279273,0.0258372,0.05957408,-0.01460895,-0.0347938,0.01329219,-0.01849968,0.04390705,0.03284796,-0.00619201,0.01376395,0.09784743],"last_embed":{"hash":"5h5wf1","tokens":264}}},"text":null,"length":0,"last_read":{"hash":"5h5wf1","at":1753495723710},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###三、運用數字差（Deltas）制定策略#{6}","lines":[51,56],"size":269,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###四、實施與優化技巧": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12134604,-0.04025901,0.00226545,-0.00178926,0.03018019,0.00599134,0.00344739,-0.01686642,0.06070383,-0.02007273,-0.00446598,-0.07629643,0.05159182,0.03148361,0.02036757,-0.04185096,0.05218718,-0.04716266,-0.05859124,-0.00892426,0.04739657,-0.03854663,-0.00678716,-0.05566713,0.05721041,-0.01275447,0.00720291,-0.07039236,-0.00127044,-0.19156848,-0.0483599,0.03673631,-0.04107651,-0.03748506,-0.03599839,-0.00659864,0.04363013,0.04989457,-0.00362046,0.00491053,0.00226804,0.01132305,0.0351968,-0.03528607,0.0084064,-0.02335747,-0.04389839,-0.01865542,0.07459578,-0.04819649,-0.06081884,-0.03252326,0.00979367,0.00022961,0.00938691,0.05025872,0.02355254,0.08258873,-0.00288511,-0.00946407,0.04704581,-0.01305877,-0.20525958,0.03221319,-0.01699592,-0.02957499,-0.02299605,-0.01503424,-0.00031856,0.02971366,-0.05474021,0.03210109,-0.01886075,0.07144114,0.06405809,-0.04928422,-0.01417687,-0.04497299,-0.06878074,-0.01134912,0.00124662,0.01924278,0.00067909,0.00402759,0.00732186,0.03378641,0.00510496,-0.00835132,0.00931573,-0.0501123,-0.02980663,-0.01291864,0.00303499,0.03513325,-0.01201523,0.01602786,0.04323718,-0.02545607,-0.04754914,0.08008678,0.01307135,0.04751625,-0.00787465,-0.03086013,0.02487293,-0.01770315,-0.00483917,-0.06414701,0.01347665,-0.0199586,0.02105398,-0.01002885,0.05609822,-0.05631253,-0.04268465,0.01292992,0.06313632,0.00282398,-0.02929648,-0.03644801,-0.00928485,0.00777001,0.02654973,0.01837056,-0.00612185,-0.00810092,0.03490815,0.02815155,0.05375392,0.02833085,0.08421528,0.00153828,-0.06695653,-0.05869913,-0.05123846,-0.02345395,-0.00946915,-0.03793385,0.02027745,-0.0121239,-0.02813498,-0.04735021,0.0518524,-0.12717678,-0.06391608,0.10662857,-0.03665312,-0.01004913,0.01481219,-0.00132704,0.00651804,0.01783708,0.01849128,-0.06068379,0.05472638,-0.01964822,0.09281919,0.17579359,-0.07394587,0.04849454,-0.01793789,-0.03037914,-0.05738435,0.14162119,0.04094015,-0.01569368,0.00270064,0.02496462,0.00409356,-0.07493456,0.01539895,-0.01019501,-0.00713945,0.03159945,0.14446963,0.00703382,0.05686104,-0.08225458,-0.06517376,0.04802134,0.00538468,-0.03824395,-0.07069765,0.05041058,0.03309903,-0.10381354,-0.00990228,-0.05228721,0.03976497,-0.00078668,-0.06321426,0.02666352,0.02858462,0.06627837,-0.04056145,-0.0757753,-0.00383969,-0.04743722,0.01978982,0.00338684,0.09119321,0.00340954,-0.02122895,-0.00468621,0.01740579,0.0567797,-0.00302642,-0.02116752,0.0177277,0.04766477,0.0019524,0.06226679,0.00177895,-0.00081744,-0.00303707,0.02976868,-0.02287111,0.05703631,-0.0183497,0.02714652,0.02100068,-0.02096635,-0.075511,-0.22557823,-0.03836233,-0.01103559,-0.04073759,0.04588233,0.01294313,0.00316025,0.00019791,0.09299042,0.03439181,0.05314649,0.00683437,-0.02789173,-0.00372671,-0.00206846,-0.03784582,0.01995921,0.00966327,0.00192937,0.02650375,0.0177918,0.02668215,0.02003557,0.02706353,0.03987842,-0.06392884,0.1352219,-0.00267194,0.05326666,-0.00536644,0.04813237,0.0404343,0.02505932,-0.07096879,0.04392061,0.0296249,-0.12965843,-0.02263603,-0.05030968,-0.03609562,-0.03868349,0.04460946,-0.01905275,-0.06788172,-0.03944989,-0.05598954,-0.0214736,-0.0295677,-0.01982132,-0.01319514,0.00063173,0.01904321,0.01331408,0.0318835,0.06948012,-0.06896029,-0.03850588,-0.03232106,-0.04272735,-0.00601801,-0.02256303,0.02003277,0.0016463,0.03108278,-0.01383223,-0.0231257,0.02873665,0.02285852,-0.04008105,0.00921173,-0.05255092,0.16805629,-0.0010775,-0.03415402,0.09503053,-0.00770007,-0.01028059,-0.03433707,-0.02575436,-0.00678893,0.01908012,-0.03870303,-0.00790214,0.05343429,-0.01114972,0.0466882,0.05725387,-0.00924352,-0.01936489,-0.03202077,-0.00727085,0.01345918,-0.03031532,0.01798163,0.06490195,0.02444626,-0.31518772,0.01603118,-0.02285423,-0.02573144,0.02803717,0.03778387,0.04234007,-0.04047638,0.00709882,0.03532107,-0.0301514,0.07033275,-0.02344333,-0.07143357,-0.01834079,0.01891153,0.02462835,-0.01390517,0.02147643,-0.01389574,0.00584323,0.00077429,0.21785723,0.05547414,0.02343966,0.02442768,0.02384314,0.03190872,-0.01172729,0.0745812,-0.02750485,-0.00127984,0.08103386,0.0173884,-0.03836582,0.11377557,-0.01368545,0.03972423,-0.00901681,0.0043837,0.01108077,-0.00350886,-0.03869924,0.0154281,0.11893439,-0.00543758,-0.02567007,-0.03876932,0.02858509,0.03403167,-0.03388131,0.00154638,0.00983864,0.02930797,0.01705352,0.05695788,-0.02968515,-0.02379817,0.02155611,-0.02540264,0.06204414,0.02710529,0.01942869,0.04462324,0.09612733],"last_embed":{"hash":"8og29p","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"8og29p","at":1753495723832},"key":"notes/saliu/運用數字差（deltas）來提升樂透中獎機率的策略.md###四、實施與優化技巧","lines":[57,65],"size":565,"outlinks":[],"class_name":"SmartBlock"},
