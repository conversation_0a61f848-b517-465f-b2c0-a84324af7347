
"smart_sources:筆記/202506301429.md": {"path":"筆記/202506301429.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"n43vd4","at":1751265904662},"class_name":"SmartSource","last_import":{"mtime":1751265131291,"size":10067,"at":1751265904663,"hash":"n43vd4"},"blocks":{"#":[1,2],"#helay Linlady":[3,8],"#helay Linlady#{1}":[5,8],"#Gemini":[9,46],"#Gemini#{1}":[11,46],"#---frontmatter---":[44,null]},"outlinks":[{"title":"analyze_triggered_pattern_performance_standalone.jl","target":"code-assist-path:f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl \"f:\\work\\JuliaProject\\StandAlone\\src\\analyze_triggered_pattern_performance_standalone.jl\"","line":13},{"title":"analyze_triggered_pattern_performance","target":"analyze_triggered_pattern_performance","line":42}]},