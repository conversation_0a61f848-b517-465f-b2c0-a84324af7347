{"internalDocumentIDStore": {"internalIdToId": []}, "index": {"indexes": {"id": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": false}, "title": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": false}, "path": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": false}, "content": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": false}, "embeddingModel": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": false}, "created_at": {"type": "AVL", "node": {"root": {"k": 0, "v": [], "l": null, "r": null, "h": 0}}, "isArray": false}, "ctime": {"type": "AVL", "node": {"root": {"k": 0, "v": [], "l": null, "r": null, "h": 0}}, "isArray": false}, "mtime": {"type": "AVL", "node": {"root": {"k": 0, "v": [], "l": null, "r": null, "h": 0}}, "isArray": false}, "tags": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": true}, "extension": {"type": "<PERSON><PERSON><PERSON>", "node": {"w": "", "s": "", "c": {}, "d": [], "e": false}, "isArray": false}}, "vectorIndexes": {"embedding": {"size": 1024, "vectors": {}}}, "searchableProperties": ["id", "title", "path", "content", "embedding", "embeddingModel", "created_at", "ctime", "mtime", "tags", "extension"], "searchablePropertiesWithTypes": {"id": "string", "title": "string", "path": "string", "content": "string", "embedding": "vector[1024]", "embeddingModel": "string", "created_at": "number", "ctime": "number", "mtime": "number", "tags": "string[]", "extension": "string"}, "frequencies": {"id": {}, "title": {}, "path": {}, "content": {}, "embeddingModel": {}, "tags": {}, "extension": {}}, "tokenOccurrences": {"id": {}, "title": {}, "path": {}, "content": {}, "embeddingModel": {}, "tags": {}, "extension": {}}, "avgFieldLength": {"id": 0, "title": 0, "path": 0, "content": 0, "embeddingModel": 0, "tags": 0, "extension": 0}, "fieldLengths": {"id": {}, "title": {}, "path": {}, "content": {}, "embeddingModel": {}, "tags": {}, "extension": {}}}, "docs": {"docs": {}, "count": 0}, "sorting": {"language": "english", "sortableProperties": ["id", "title", "path", "content", "embeddingModel", "created_at", "ctime", "mtime", "extension"], "sortablePropertiesWithTypes": {"id": "string", "title": "string", "path": "string", "content": "string", "embeddingModel": "string", "created_at": "number", "ctime": "number", "mtime": "number", "extension": "string"}, "sorts": {"id": {"docs": {}, "orderedDocs": [], "type": "string"}, "title": {"docs": {}, "orderedDocs": [], "type": "string"}, "path": {"docs": {}, "orderedDocs": [], "type": "string"}, "content": {"docs": {}, "orderedDocs": [], "type": "string"}, "embeddingModel": {"docs": {}, "orderedDocs": [], "type": "string"}, "created_at": {"docs": {}, "orderedDocs": [], "type": "number"}, "ctime": {"docs": {}, "orderedDocs": [], "type": "number"}, "mtime": {"docs": {}, "orderedDocs": [], "type": "number"}, "extension": {"docs": {}, "orderedDocs": [], "type": "string"}}, "enabled": true, "isSorted": true}, "language": "english", "schema": {"id": "string", "title": "string", "path": "string", "content": "string", "embedding": "vector[1024]", "embeddingModel": "string", "created_at": "number", "ctime": "number", "mtime": "number", "tags": "string[]", "extension": "string"}}