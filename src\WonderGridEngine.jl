# src/WonderGridEngine.jl

module WonderGridEngine

# 直接定義需要的結構體和常量，避免循環依賴
using Dates
using Statistics: mean, std, median
using Random: shuffle, rand

# 基本數據結構
struct Drawing
    drawing_id::Int
    game_type::Symbol
    date::Date
    numbers::Vector{Int}
end

struct GameConfiguration
    name::String
    min_number::Int
    max_number::Int
    numbers_per_draw::Int
    has_bonus::Bool
    bonus_range::Tuple{Int,Int}
end

# 遊戲配置常量
const GAME_CONFIGURATIONS = Dict{Symbol, GameConfiguration}(
    :Lotto6_49 => GameConfiguration("Lotto 6/49", 1, 49, 6, false, (0, 0)),
    :Pick3 => GameConfiguration("Pick 3", 0, 9, 3, false, (0, 0)),
    :Pick4 => GameConfiguration("Pick 4", 0, 9, 4, false, (0, 0)),
    :PowerBall => GameConfiguration("PowerBall", 1, 69, 5, true, (1, 26))
)

# 簡化的輔助函數
function calculate_skip_values(drawings::Vector{Drawing}, number::Int)::Vector{Int}
    skip_values = Int[]
    last_occurrence = -1

    for (i, drawing) in enumerate(drawings)
        if number in drawing.numbers
            if last_occurrence != -1
                push!(skip_values, i - last_occurrence - 1)
            end
            last_occurrence = i
        end
    end

    return skip_values
end

# FFG 統計結構
struct FFGStats
    median::Float64
    standard_deviation::Float64
    mean::Float64
end

function calculate_ffg(skip_values::Vector{Int})::FFGStats
    if isempty(skip_values)
        return FFGStats(0.0, 0.0, 0.0)
    end

    # 計算統計值
    mean_val = mean(skip_values)
    median_val = median(skip_values)
    std_val = std(skip_values)

    return FFGStats(median_val, std_val, mean_val)
end

function calculate_number_frequencies(drawings::Vector{Drawing}, max_number::Int)::Dict{Int, Int}
    frequencies = Dict{Int, Int}()

    # 初始化所有號碼的頻率為0
    for number in 1:max_number
        frequencies[number] = 0
    end

    # 計算每個號碼的出現頻率
    for drawing in drawings
        for number in drawing.numbers
            if haskey(frequencies, number)
                frequencies[number] += 1
            end
        end
    end

    return frequencies
end

function calculate_pairing_frequencies(drawings::Vector{Drawing}, max_number::Int)::Dict{Tuple{Int,Int}, Int}
    frequencies = Dict{Tuple{Int,Int}, Int}()

    for drawing in drawings
        numbers = sort(drawing.numbers)
        for i in 1:length(numbers)
            for j in (i+1):length(numbers)
                pair = (numbers[i], numbers[j])
                frequencies[pair] = get(frequencies, pair, 0) + 1
            end
        end
    end

    return frequencies
end
using Printf
using Dates
using Statistics

export WonderGridConfig, WonderGridResult, WonderGridStrategy,
       execute_wonder_grid_strategy, calculate_pair_frequencies,
       select_key_number, generate_wonder_combinations,
       WonderGridError, PairStatistics, analyze_pair_trends

# 錯誤處理
struct WonderGridError <: Exception
    message::String
    code::Symbol
    context::Dict{String, Any}
    timestamp::DateTime

    function WonderGridError(message::String, code::Symbol, context::Dict{String, Any} = Dict{String, Any}())
        new(message, code, context, now())
    end
end

Base.showerror(io::IO, e::WonderGridError) = begin
    print(io, "WonderGridError [$(e.code)] at $(e.timestamp): $(e.message)")
    if !isempty(e.context)
        print(io, "\nContext: ")
        for (key, value) in e.context
            print(io, "\n  $key: $value")
        end
    end
end

# 錯誤恢復策略
function handle_wonder_grid_error(error::WonderGridError, recovery_strategy::Symbol = :default)
    println("處理 Wonder Grid 錯誤: $(error.message)")

    if recovery_strategy == :retry
        println("嘗試重試操作...")
        return :retry
    elseif recovery_strategy == :fallback
        println("使用備用策略...")
        return :fallback
    elseif recovery_strategy == :skip
        println("跳過當前操作...")
        return :skip
    else
        println("使用默認錯誤處理...")
        return :default
    end
end

# 效率指標結構
struct EfficiencyMetrics
    hit_rate::Float64                    # 中獎率
    coverage_rate::Float64               # 覆蓋率
    cost_efficiency::Float64             # 成本效率
    roi::Float64                        # 投資回報率
    sharpe_ratio::Float64               # 夏普比率
    max_drawdown::Float64               # 最大回撤
    win_loss_ratio::Float64             # 盈虧比
    consistency_score::Float64          # 一致性評分
    risk_adjusted_return::Float64       # 風險調整回報
    expected_value::Float64             # 期望值
    variance::Float64                   # 方差
    confidence_interval::Tuple{Float64, Float64}  # 置信區間
    backtest_periods::Int               # 回測期數
    total_combinations::Int             # 總組合數
    
    function EfficiencyMetrics(
        hit_rate, coverage_rate, cost_efficiency, roi, sharpe_ratio,
        max_drawdown, win_loss_ratio, consistency_score, risk_adjusted_return,
        expected_value, variance, confidence_interval, backtest_periods, total_combinations
    )
        # 驗證指標範圍
        if !(0.0 <= hit_rate <= 1.0)
            throw(WonderGridError("中獎率必須在 0 到 1 之間", :invalid_hit_rate))
        end
        if !(0.0 <= coverage_rate <= 1.0)
            throw(WonderGridError("覆蓋率必須在 0 到 1 之間", :invalid_coverage_rate))
        end
        
        new(hit_rate, coverage_rate, cost_efficiency, roi, sharpe_ratio,
            max_drawdown, win_loss_ratio, consistency_score, risk_adjusted_return,
            expected_value, variance, confidence_interval, backtest_periods, total_combinations)
    end
end

# 回測結果結構
struct BacktestResult
    period_index::Int
    drawing::Drawing
    matched_combinations::Vector{Vector{Int}}
    match_counts::Vector{Int}
    prizes::Vector{Float64}
    total_prize::Float64
    net_profit::Float64
    hit_rate::Float64
    total_combinations::Int

    function BacktestResult(period_index, drawing, matched_combinations, match_counts, prizes, total_prize, net_profit, hit_rate, total_combinations)
        if period_index <= 0
            throw(WonderGridError("期數索引必須大於 0", :invalid_period))
        end
        
        new(period_index, drawing, matched_combinations, match_counts, prizes, total_prize, net_profit, hit_rate, total_combinations)
    end
end

# 配對統計數據結構
struct PairStatistics
    pair::Tuple{Int,Int}
    frequency::Int
    last_occurrence::Int
    skip_values::Vector{Int}
    avg_skip::Float64
    trend_score::Float64
    efficiency_rating::Float64
    recent_frequency::Int           # 最近頻率
    skip_variance::Float64          # 跳躍值變異數
    last_appearance::Int            # 最後出現位置

    function PairStatistics(pair, frequency, last_occurrence, skip_values, recent_frequency=0)
        avg_skip = isempty(skip_values) ? 0.0 : mean(skip_values)
        trend_score = calculate_trend_score(skip_values)
        efficiency_rating = calculate_efficiency_rating(frequency, avg_skip, trend_score)

        # 安全計算變異數，避免 NaN
        skip_variance = if isempty(skip_values) || length(skip_values) < 2
            0.0
        else
            variance_val = var(skip_values)
            isnan(variance_val) ? 0.0 : variance_val
        end

        last_appearance = last_occurrence

        new(pair, frequency, last_occurrence, skip_values,
            avg_skip, trend_score, efficiency_rating, recent_frequency,
            skip_variance, last_appearance)
    end
end

# 頂級配對結構
struct TopPair
    pair::Tuple{Int,Int}
    frequency::Int
    recent_frequency::Int
    average_skip::Float64
    last_appearance::Int
    quality_score::Float64
    rank::Int
    selection_reason::String

    function TopPair(pair, frequency, recent_frequency, average_skip, last_appearance,
                    quality_score, rank, selection_reason)
        new(pair, frequency, recent_frequency, average_skip, last_appearance,
            quality_score, rank, selection_reason)
    end
end

# 評分配對結構
struct ScoredPair
    pair::Tuple{Int,Int}
    statistics::PairStatistics
    quality_score::Float64
    frequency_score::Float64
    recent_score::Float64
    skip_score::Float64
    trend_score::Float64
    stability_score::Float64
    momentum_score::Float64
    specialty_bonus::Float64

    function ScoredPair(pair, statistics, quality_score, frequency_score, recent_score,
                       skip_score, trend_score, stability_score, momentum_score, specialty_bonus)
        new(pair, statistics, quality_score, frequency_score, recent_score,
            skip_score, trend_score, stability_score, momentum_score, specialty_bonus)
    end
end

# 組合結構
struct Combination
    numbers::Vector{Int}
    quality_score::Float64
    generation_method::Symbol
    metadata::Dict{String, Any}

    function Combination(numbers, quality_score=0.0, generation_method=:unknown, metadata=Dict{String, Any}())
        if length(numbers) != length(unique(numbers))
            throw(WonderGridError("組合中不能有重複號碼", :duplicate_numbers))
        end

        new(sort(numbers), quality_score, generation_method, metadata)
    end
end

# 配置系統
struct WonderGridConfig
    analysis_range::Int           # 分析範圍（建議為最大號碼的3倍）
    top_pair_percentage::Float64  # 頂級配對百分比（預設 25%）
    key_number_strategy::Symbol   # 關鍵號碼選擇策略 (:ffg, :skip, :frequency)
    combination_limit::Int        # 組合數量限制
    enable_purge::Bool           # 啟用 Purge 過濾
    enable_lie::Bool             # 啟用 LIE 消除
    game_type::Symbol            # 遊戲類型
    pair_analysis_depth::Int     # 配對分析深度
    trend_weight::Float64        # 趨勢權重
    
    function WonderGridConfig(;
        analysis_range::Int = 150,
        top_pair_percentage::Float64 = 0.25,
        key_number_strategy::Symbol = :ffg,
        combination_limit::Int = 1000,
        enable_purge::Bool = true,
        enable_lie::Bool = false,
        game_type::Symbol = :Lotto6_49,
        pair_analysis_depth::Int = 100,
        trend_weight::Float64 = 0.3
    )
        # 驗證參數
        if analysis_range <= 0
            throw(WonderGridError("分析範圍必須大於 0", :invalid_range))
        end
        if !(0.0 < top_pair_percentage <= 1.0)
            throw(WonderGridError("配對百分比必須在 0 到 1 之間", :invalid_percentage))
        end
        if !(key_number_strategy in [:ffg, :skip, :frequency])
            throw(WonderGridError("無效的關鍵號碼策略", :invalid_strategy))
        end
        if combination_limit <= 0
            throw(WonderGridError("組合限制必須大於 0", :invalid_limit))
        end
        if !(game_type in keys(GAME_CONFIGURATIONS))
            throw(WonderGridError("不支援的遊戲類型: $game_type", :invalid_game_type))
        end
        if pair_analysis_depth <= 0
            throw(WonderGridError("配對分析深度必須大於 0", :invalid_depth))
        end
        if !(0.0 <= trend_weight <= 1.0)
            throw(WonderGridError("趨勢權重必須在 0 到 1 之間", :invalid_weight))
        end
        
        new(analysis_range, top_pair_percentage, key_number_strategy,
            combination_limit, enable_purge, enable_lie, game_type,
            pair_analysis_depth, trend_weight)
    end
end

# 組合生成配置
struct CombinationConfig
    generation_method::Symbol     # 生成方法
    max_combinations::Int        # 最大組合數
    diversity_factor::Float64    # 多樣性因子
    balance_odd_even::Bool       # 平衡奇偶數
    balance_high_low::Bool       # 平衡高低數
    avoid_consecutive::Bool      # 避免連續數字
    min_sum::Int                # 最小和值
    max_sum::Int                # 最大和值
    enable_filtering::Bool       # 啟用過濾
    
    function CombinationConfig(;
        generation_method::Symbol = :balanced,
        max_combinations::Int = 1000,
        diversity_factor::Float64 = 0.7,
        balance_odd_even::Bool = true,
        balance_high_low::Bool = true,
        avoid_consecutive::Bool = false,
        min_sum::Int = 0,
        max_sum::Int = 999,
        enable_filtering::Bool = true
    )
        if !(generation_method in [:random, :systematic, :balanced, :weighted, :markov])
            throw(WonderGridError("無效的生成方法", :invalid_method))
        end
        if max_combinations <= 0
            throw(WonderGridError("最大組合數必須大於 0", :invalid_max))
        end
        if !(0.0 <= diversity_factor <= 1.0)
            throw(WonderGridError("多樣性因子必須在 0 到 1 之間", :invalid_diversity))
        end
        
        new(generation_method, max_combinations, diversity_factor,
            balance_odd_even, balance_high_low, avoid_consecutive,
            min_sum, max_sum, enable_filtering)
    end
end

# 組合生成結果
struct CombinationResult
    combinations::Vector{Vector{Int}}
    generation_method::Symbol
    statistics::Dict{String, Float64}
    quality_score::Float64
    generation_time::Float64
    
    function CombinationResult(combinations, method, statistics, quality_score, time)
        if isempty(combinations)
            throw(WonderGridError("組合不能為空", :empty_combinations))
        end
        
        new(combinations, method, statistics, quality_score, time)
    end
end

# 結果結構
struct WonderGridResult
    key_number::Int
    top_pairs::Vector{Int}
    combinations::Vector{Vector{Int}}
    efficiency_metrics::Dict{String, Float64}
    generation_time::Float64
    config::WonderGridConfig
    pair_statistics::Dict{Tuple{Int,Int}, PairStatistics}
    
    function WonderGridResult(key_number, top_pairs, combinations, 
                            efficiency_metrics, generation_time, config, pair_statistics)
        if key_number <= 0
            throw(WonderGridError("關鍵號碼無效", :invalid_key_number))
        end
        if isempty(top_pairs)
            throw(WonderGridError("頂級配對不能為空", :empty_pairs))
        end
        if isempty(combinations)
            throw(WonderGridError("組合不能為空", :empty_combinations))
        end
        
        new(key_number, top_pairs, combinations, efficiency_metrics, 
            generation_time, config, pair_statistics)
    end
end

# 策略結構
struct WonderGridStrategy
    key_number::Int
    top_pairs::Vector{Int}
    pair_frequencies::Dict{Tuple{Int,Int}, Int}
    analysis_range::Int
    created_at::DateTime
    
    function WonderGridStrategy(key_number, top_pairs, pair_frequencies, analysis_range)
        new(key_number, top_pairs, pair_frequencies, analysis_range, now())
    end
end

# 關鍵號碼選擇結果結構
struct KeyNumberResult
    number::Int
    strategy::Symbol
    score::Float64
    analysis_data::Dict{String, Any}
    confidence::Float64
    
    function KeyNumberResult(number, strategy, score, analysis_data, confidence)
        if number <= 0
            throw(WonderGridError("關鍵號碼無效", :invalid_key_number))
        end
        if !(0.0 <= confidence <= 1.0)
            throw(WonderGridError("信心度必須在 0 到 1 之間", :invalid_confidence))
        end
        
        new(number, strategy, score, analysis_data, confidence)
    end
end

# 關鍵號碼選擇配置
struct KeyNumberConfig
    strategy::Symbol              # 選擇策略
    analysis_depth::Int          # 分析深度
    ffg_threshold::Float64       # FFG 閾值
    skip_weight::Float64         # 跳躍權重
    frequency_weight::Float64    # 頻率權重
    trend_weight::Float64        # 趨勢權重
    enable_hybrid::Bool          # 啟用混合策略
    
    function KeyNumberConfig(;
        strategy::Symbol = :ffg,
        analysis_depth::Int = 100,
        ffg_threshold::Float64 = 0.5,
        skip_weight::Float64 = 0.4,
        frequency_weight::Float64 = 0.3,
        trend_weight::Float64 = 0.3,
        enable_hybrid::Bool = false
    )
        if !(strategy in [:ffg, :skip, :frequency, :hybrid])
            throw(WonderGridError("無效的關鍵號碼策略", :invalid_strategy))
        end
        if analysis_depth <= 0
            throw(WonderGridError("分析深度必須大於 0", :invalid_depth))
        end
        if !(0.0 <= ffg_threshold <= 1.0)
            throw(WonderGridError("FFG 閾值必須在 0 到 1 之間", :invalid_threshold))
        end
        
        # 權重總和檢查（允許更大的容差）
        total_weight = skip_weight + frequency_weight + trend_weight
        if abs(total_weight - 1.0) > 0.1
            throw(WonderGridError("權重總和必須接近 1.0，當前為 $total_weight", :invalid_weights))
        end
        
        new(strategy, analysis_depth, ffg_threshold, skip_weight, 
            frequency_weight, trend_weight, enable_hybrid)
    end
end

# 核心介面定義

"""
執行完整的 Wonder Grid 策略

# Arguments
- `drawings::Vector{Drawing}`: 歷史開獎數據
- `config::WonderGridConfig`: 策略配置

# Returns
- `WonderGridResult`: 策略執行結果
"""
function execute_wonder_grid_strategy(
    drawings::Vector{Drawing}, 
    config::WonderGridConfig
)::WonderGridResult
    start_time = time()
    
    try
        # 驗證輸入數據
        if isempty(drawings)
            throw(WonderGridError("開獎數據不能為空", :empty_data))
        end
        
        # 檢查遊戲類型一致性
        game_types = unique([d.game_type for d in drawings])
        if length(game_types) > 1
            throw(WonderGridError("數據包含多種遊戲類型", :mixed_game_types))
        end
        
        if game_types[1] != config.game_type
            throw(WonderGridError("數據遊戲類型與配置不符", :game_type_mismatch))
        end
        
        println("開始執行 Wonder Grid 策略...")
        println("配置: 分析範圍=$(config.analysis_range), 配對百分比=$(config.top_pair_percentage)")
        
        # 步驟 1: 計算配對頻率和統計
        println("步驟 1: 計算配對頻率和統計...")
        pair_statistics = calculate_advanced_pair_statistics(drawings, config)
        
        # 步驟 2: 選擇關鍵號碼
        println("步驟 2: 選擇關鍵號碼...")
        key_result = select_key_number(drawings, config.key_number_strategy)
        key_number = key_result.number
        
        # 步驟 3: 篩選頂級配對
        println("步驟 3: 篩選頂級配對...")
        top_pairs = select_top_pairs_advanced(pair_statistics, key_number, config)
        
        # 步驟 4: 生成組合
        println("步驟 4: 生成 Wonder Grid 組合...")
        game_config = GAME_CONFIGURATIONS[config.game_type]
        combinations = generate_wonder_combinations(key_number, top_pairs, config, game_config)
        
        # 步驟 5: 計算效率指標
        println("步驟 5: 計算效率指標...")

        # 轉換類型以匹配 WonderGridResult 的期望
        top_pairs_numbers = extract_partner_numbers(key_number, top_pairs)
        combinations_vectors = [combo.numbers for combo in combinations]

        efficiency_metrics_struct = calculate_efficiency_metrics(combinations_vectors, drawings, config, pair_statistics)
        efficiency_metrics = convert_efficiency_metrics_to_dict(efficiency_metrics_struct)

        generation_time = time() - start_time

        result = WonderGridResult(
            key_number, top_pairs_numbers, combinations_vectors,
            efficiency_metrics, generation_time, config, pair_statistics
        )
        
        println("Wonder Grid 策略執行完成!")
        println("關鍵號碼: $(key_number)")
        println("頂級配對數量: $(length(top_pairs))")
        println("生成組合數量: $(length(combinations))")
        println("執行時間: $(round(generation_time, digits=2)) 秒")
        
        return result
        
    catch e
        if isa(e, WonderGridError)
            rethrow(e)
        else
            # 簡化錯誤信息，避免打印大量數據
            error_type = typeof(e)
            error_msg = if isa(e, MethodError)
                "方法調用錯誤: $(e.f)"
            elseif isa(e, BoundsError)
                "索引越界錯誤"
            elseif isa(e, ArgumentError)
                "參數錯誤: $(e.msg)"
            else
                "未知錯誤: $error_type"
            end
            throw(WonderGridError("策略執行失敗: $error_msg", :execution_failed))
        end
    end
end

"""
計算進階配對統計
"""
function calculate_advanced_pair_statistics(
    drawings::Vector{Drawing}, 
    config::WonderGridConfig
)::Dict{Tuple{Int,Int}, PairStatistics}
    
    if isempty(drawings)
        throw(WonderGridError("開獎數據不能為空", :empty_data))
    end
    
    # 限制分析範圍
    analysis_drawings = length(drawings) > config.analysis_range ? 
                       drawings[1:config.analysis_range] : drawings
    
    pair_stats = Dict{Tuple{Int,Int}, PairStatistics}()
    game_config = GAME_CONFIGURATIONS[config.game_type]
    
    println("分析 $(length(analysis_drawings)) 期開獎數據...")
    
    # 初始化配對追蹤
    pair_occurrences = Dict{Tuple{Int,Int}, Vector{Int}}()
    
    # 遍歷開獎數據
    for (draw_index, drawing) in enumerate(analysis_drawings)
        numbers = drawing.numbers
        
        # 生成所有配對
        for i in 1:length(numbers)
            for j in (i+1):length(numbers)
                pair = (min(numbers[i], numbers[j]), max(numbers[i], numbers[j]))
                
                if !haskey(pair_occurrences, pair)
                    pair_occurrences[pair] = Int[]
                end
                push!(pair_occurrences[pair], draw_index)
            end
        end
    end
    
    println("找到 $(length(pair_occurrences)) 個不同配對")
    
    # 計算每個配對的統計數據
    for (pair, occurrences) in pair_occurrences
        frequency = length(occurrences)
        last_occurrence = minimum(occurrences)  # 最近出現（索引越小越近）
        
        # 計算跳躍值
        skip_values = Int[]
        if length(occurrences) > 1
            for i in 2:length(occurrences)
                skip = occurrences[i-1] - occurrences[i] - 1
                if skip >= 0
                    push!(skip_values, skip)
                end
            end
        end
        
        # 添加當前跳躍值（距離最後一次出現）
        current_skip = last_occurrence - 1
        if current_skip >= 0
            push!(skip_values, current_skip)
        end
        
        # 計算最近頻率（最近20期的出現次數）
        recent_frequency = 0
        recent_range = min(20, length(analysis_drawings))
        for i in 1:recent_range
            if pair in [(min(analysis_drawings[i].numbers[j], analysis_drawings[i].numbers[k]),
                        max(analysis_drawings[i].numbers[j], analysis_drawings[i].numbers[k]))
                       for j in 1:length(analysis_drawings[i].numbers)
                       for k in (j+1):length(analysis_drawings[i].numbers)]
                recent_frequency += 1
            end
        end

        pair_stats[pair] = PairStatistics(pair, frequency, last_occurrence, skip_values, recent_frequency)
    end
    
    println("完成配對統計計算")
    return pair_stats
end

"""
計算號碼配對頻率（簡化版本，向後相容）
"""
function calculate_pair_frequencies(
    drawings::Vector{Drawing}, 
    range::Int
)::Dict{Tuple{Int,Int}, Int}
    if isempty(drawings)
        throw(WonderGridError("開獎數據不能為空", :empty_data))
    end
    
    # 使用現有的配對頻率分析功能
    return calculate_pairing_frequencies(drawings)
end



"""
分析配對趨勢
"""
function analyze_pair_trends(pair_statistics::Dict{Tuple{Int,Int}, PairStatistics})::Dict{String, Float64}
    trends = Dict{String, Float64}()
    
    if isempty(pair_statistics)
        return trends
    end
    
    all_frequencies = [stats.frequency for stats in values(pair_statistics)]
    all_avg_skips = [stats.avg_skip for stats in values(pair_statistics)]
    all_trend_scores = [stats.trend_score for stats in values(pair_statistics)]
    
    trends["avg_frequency"] = mean(all_frequencies)
    trends["max_frequency"] = maximum(all_frequencies)
    trends["min_frequency"] = minimum(all_frequencies)
    trends["avg_skip"] = mean(all_avg_skips)
    trends["avg_trend_score"] = mean(all_trend_scores)
    trends["total_pairs"] = Float64(length(pair_statistics))
    
    return trends
end

# 輔助函數

function calculate_trend_score(skip_values::Vector{Int})::Float64
    if length(skip_values) < 2
        return 0.0
    end
    
    # 計算跳躍值的趨勢（遞減趨勢得分更高）
    trend = 0.0
    for i in 2:length(skip_values)
        if skip_values[i-1] > skip_values[i]
            trend += 1.0
        elseif skip_values[i-1] < skip_values[i]
            trend -= 0.5
        end
    end
    
    return trend / (length(skip_values) - 1)
end

function calculate_efficiency_rating(frequency::Int, avg_skip::Float64, trend_score::Float64)::Float64
    # 綜合評級：頻率權重 0.5，跳躍權重 0.3，趨勢權重 0.2
    freq_score = frequency / 10.0  # 標準化頻率分數
    skip_score = avg_skip > 0 ? 1.0 / avg_skip : 0.0  # 跳躍值越小越好
    trend_score_normalized = (trend_score + 1.0) / 2.0  # 標準化到 0-1
    
    return 0.5 * freq_score + 0.3 * skip_score + 0.2 * trend_score_normalized
end

function select_top_pairs(pair_frequencies::Dict{Tuple{Int,Int}, Int}, 
                         key_number::Int, percentage::Float64)::Vector{Int}
    # 向後相容的簡化版本
    key_pairs = filter(((pair, freq),) -> key_number in pair, pair_frequencies)
    
    if isempty(key_pairs)
        throw(WonderGridError("找不到包含關鍵號碼的配對", :no_key_pairs))
    end
    
    sorted_pairs = sort(collect(key_pairs), by = x -> x[2], rev = true)
    top_count = max(1, Int(round(length(sorted_pairs) * percentage)))
    top_pairs = Int[]
    
    for (pair, freq) in sorted_pairs[1:top_count]
        for num in pair
            if num != key_number && !(num in top_pairs)
                push!(top_pairs, num)
            end
        end
    end
    
    return sort(top_pairs)
end

"""
進階關鍵號碼選擇系統

# Arguments
- `drawings::Vector{Drawing}`: 歷史開獎數據
- `config::KeyNumberConfig`: 選擇配置

# Returns
- `KeyNumberResult`: 選擇結果
"""
function select_key_number_advanced(
    drawings::Vector{Drawing}, 
    config::KeyNumberConfig
)::KeyNumberResult
    
    if isempty(drawings)
        throw(WonderGridError("開獎數據不能為空", :empty_data))
    end
    
    println("開始進階關鍵號碼選擇...")
    println("策略: $(config.strategy), 分析深度: $(config.analysis_depth)")
    
    # 限制分析範圍
    analysis_drawings = length(drawings) > config.analysis_depth ? 
                       drawings[1:config.analysis_depth] : drawings
    
    if config.strategy == :hybrid || config.enable_hybrid
        return select_key_number_hybrid(analysis_drawings, config)
    elseif config.strategy == :ffg
        return select_key_number_ffg_advanced(analysis_drawings, config)
    elseif config.strategy == :skip
        return select_key_number_skip_advanced(analysis_drawings, config)
    elseif config.strategy == :frequency
        return select_key_number_frequency_advanced(analysis_drawings, config)
    else
        throw(WonderGridError("未知的關鍵號碼選擇策略: $(config.strategy)", :unknown_strategy))
    end
end

"""
基於 FFG 理論的進階關鍵號碼選擇
"""
function select_key_number_ffg_advanced(
    drawings::Vector{Drawing}, 
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("執行 FFG 理論分析...")
    
    # 計算跳躍值
    skip_values = calculate_skip_values(drawings)
    
    # 計算 FFG 分數和相關統計
    ffg_analysis = Dict{Int, Dict{String, Float64}}()
    
    for (number, skips) in skip_values
        if !isempty(skips)
            # 基本 FFG 計算
            ffg_score = calculate_ffg(length(skips))
            
            # 進階統計
            avg_skip = mean(skips)
            std_skip = std(skips)
            median_skip = median(skips)
            
            # 當前跳躍值（距離最後一次出現）
            current_skip = 0
            for (i, drawing) in enumerate(drawings)
                if number in drawing.numbers
                    current_skip = i - 1
                    break
                end
            end
            
            # FFG 中位數比較
            ffg_median = calculate_ffg_median(length(skips))
            ffg_deviation = abs(current_skip - ffg_median) / max(ffg_median, 1.0)
            
            # 綜合評分
            confidence = calculate_ffg_confidence(ffg_score, ffg_deviation, config.ffg_threshold)
            
            ffg_analysis[number] = Dict(
                "ffg_score" => ffg_score,
                "avg_skip" => avg_skip,
                "std_skip" => std_skip,
                "median_skip" => median_skip,
                "current_skip" => Float64(current_skip),
                "ffg_median" => ffg_median,
                "ffg_deviation" => ffg_deviation,
                "confidence" => confidence
            )
        end
    end
    
    if isempty(ffg_analysis)
        throw(WonderGridError("無法計算 FFG 分數", :ffg_calculation_failed))
    end
    
    # 選擇最佳號碼（基於信心度和 FFG 分數）
    best_number = 0
    best_score = -1.0
    best_confidence = 0.0
    
    for (number, analysis) in ffg_analysis
        combined_score = analysis["ffg_score"] * analysis["confidence"]
        if combined_score > best_score
            best_score = combined_score
            best_number = number
            best_confidence = analysis["confidence"]
        end
    end
    
    println("FFG 分析完成，選擇號碼: $best_number (分數: $(round(best_score, digits=3)))")
    
    return KeyNumberResult(
        best_number, :ffg, best_score, 
        ffg_analysis[best_number], best_confidence
    )
end

"""
基於跳躍分析的進階關鍵號碼選擇
"""
function select_key_number_skip_advanced(
    drawings::Vector{Drawing}, 
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("執行跳躍分析...")
    
    skip_values = calculate_skip_values(drawings)
    skip_analysis = Dict{Int, Dict{String, Float64}}()
    
    for (number, skips) in skip_values
        if !isempty(skips)
            # 跳躍統計
            avg_skip = mean(skips)
            min_skip = minimum(skips)
            max_skip = maximum(skips)
            std_skip = std(skips)
            
            # 活躍度評分（跳躍值越小越活躍）
            activity_score = 1.0 / (avg_skip + 1.0)
            
            # 穩定性評分（標準差越小越穩定）
            stability_score = 1.0 / (std_skip + 1.0)
            
            # 趨勢分析
            trend_score = calculate_skip_trend(skips)
            
            # 綜合評分
            combined_score = activity_score * 0.5 + stability_score * 0.3 + trend_score * 0.2
            confidence = min(1.0, combined_score)
            
            skip_analysis[number] = Dict(
                "avg_skip" => avg_skip,
                "min_skip" => Float64(min_skip),
                "max_skip" => Float64(max_skip),
                "std_skip" => std_skip,
                "activity_score" => activity_score,
                "stability_score" => stability_score,
                "trend_score" => trend_score,
                "combined_score" => combined_score,
                "confidence" => confidence
            )
        end
    end
    
    if isempty(skip_analysis)
        throw(WonderGridError("無法計算跳躍分析", :skip_calculation_failed))
    end
    
    # 選擇最佳號碼
    best_number = argmax(number -> skip_analysis[number]["combined_score"], 
                        keys(skip_analysis))
    best_analysis = skip_analysis[best_number]
    
    println("跳躍分析完成，選擇號碼: $best_number (活躍度: $(round(best_analysis["activity_score"], digits=3)))")
    
    return KeyNumberResult(
        best_number, :skip, best_analysis["combined_score"], 
        best_analysis, best_analysis["confidence"]
    )
end

"""
基於頻率分析的進階關鍵號碼選擇
"""
function select_key_number_frequency_advanced(
    drawings::Vector{Drawing}, 
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("執行頻率分析...")
    
    frequencies = calculate_number_frequencies(drawings, 49)
    frequency_analysis = Dict{Int, Dict{String, Float64}}()
    
    if isempty(frequencies)
        throw(WonderGridError("無法計算頻率", :frequency_calculation_failed))
    end
    
    total_draws = length(drawings)
    max_frequency = maximum(values(frequencies))
    avg_frequency = mean(values(frequencies))
    
    for (number, frequency) in frequencies
        # 頻率統計
        frequency_ratio = frequency / total_draws
        relative_frequency = frequency / max_frequency
        
        # 熱度評分
        heat_score = frequency / avg_frequency
        
        # 近期表現分析（最近 20 期）
        recent_count = 0
        recent_range = min(20, length(drawings))
        for i in 1:recent_range
            if number in drawings[i].numbers
                recent_count += 1
            end
        end
        recent_ratio = recent_count / recent_range
        
        # 綜合評分
        combined_score = relative_frequency * 0.6 + recent_ratio * 0.4
        confidence = min(1.0, heat_score / 2.0)
        
        frequency_analysis[number] = Dict(
            "frequency" => Float64(frequency),
            "frequency_ratio" => frequency_ratio,
            "relative_frequency" => relative_frequency,
            "heat_score" => heat_score,
            "recent_count" => Float64(recent_count),
            "recent_ratio" => recent_ratio,
            "combined_score" => combined_score,
            "confidence" => confidence
        )
    end
    
    # 選擇最佳號碼
    best_number = argmax(number -> frequency_analysis[number]["combined_score"], 
                        keys(frequency_analysis))
    best_analysis = frequency_analysis[best_number]
    
    println("頻率分析完成，選擇號碼: $best_number (頻率: $(Int(best_analysis["frequency"])))")
    
    return KeyNumberResult(
        best_number, :frequency, best_analysis["combined_score"], 
        best_analysis, best_analysis["confidence"]
    )
end

"""
混合策略關鍵號碼選擇
"""
function select_key_number_hybrid(
    drawings::Vector{Drawing}, 
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("執行混合策略分析...")
    
    # 執行各種分析
    ffg_result = select_key_number_ffg_advanced(drawings, config)
    skip_result = select_key_number_skip_advanced(drawings, config)
    frequency_result = select_key_number_frequency_advanced(drawings, config)
    
    # 收集所有候選號碼
    candidates = Set([ffg_result.number, skip_result.number, frequency_result.number])
    
    hybrid_analysis = Dict{Int, Dict{String, Float64}}()
    
    for number in candidates
        # 獲取各策略的分數
        ffg_score = number == ffg_result.number ? ffg_result.score : 0.0
        skip_score = number == skip_result.number ? skip_result.score : 0.0
        freq_score = number == frequency_result.number ? frequency_result.score : 0.0
        
        # 加權綜合評分
        combined_score = (ffg_score * (1.0 - config.skip_weight - config.frequency_weight) + 
                         skip_score * config.skip_weight + 
                         freq_score * config.frequency_weight)
        
        # 計算信心度（基於多策略一致性）
        strategy_count = sum([ffg_score > 0, skip_score > 0, freq_score > 0])
        confidence = strategy_count / 3.0
        
        hybrid_analysis[number] = Dict(
            "ffg_score" => ffg_score,
            "skip_score" => skip_score,
            "frequency_score" => freq_score,
            "combined_score" => combined_score,
            "confidence" => confidence,
            "strategy_count" => Float64(strategy_count)
        )
    end
    
    # 選擇最佳號碼
    best_number = argmax(number -> hybrid_analysis[number]["combined_score"], 
                        keys(hybrid_analysis))
    best_analysis = hybrid_analysis[best_number]
    
    println("混合策略分析完成，選擇號碼: $best_number (綜合分數: $(round(best_analysis["combined_score"], digits=3)))")
    
    return KeyNumberResult(
        best_number, :hybrid, best_analysis["combined_score"], 
        best_analysis, best_analysis["confidence"]
    )
end

function calculate_ffg_median(sample_size::Int)::Float64
    # 基於 FFG 理論計算中位數
    if sample_size <= 1
        return 0.0
    end
    
    # 簡化的 FFG 中位數計算
    return sqrt(sample_size * 0.693147)  # ln(2) ≈ 0.693147
end

function calculate_ffg_confidence(ffg_score::Float64, deviation::Float64, threshold::Float64)::Float64
    # 計算 FFG 信心度
    base_confidence = min(1.0, ffg_score / threshold)
    deviation_penalty = exp(-deviation)
    
    return base_confidence * deviation_penalty
end

function calculate_skip_trend(skips::Vector{Int})::Float64
    if length(skips) < 3
        return 0.5
    end
    
    # 計算跳躍值趨勢（遞減趨勢得分更高）
    trend_score = 0.0
    for i in 2:length(skips)
        if skips[i-1] > skips[i]
            trend_score += 1.0
        elseif skips[i-1] < skips[i]
            trend_score -= 0.5
        end
    end
    
    # 標準化到 0-1
    normalized_trend = (trend_score / (length(skips) - 1) + 1.0) / 2.0
    return max(0.0, min(1.0, normalized_trend))
end

"""
選擇關鍵號碼

# Arguments
- `drawings::Vector{Drawing}`: 歷史開獎數據
- `strategy::Symbol`: 選擇策略 (:ffg, :skip, :frequency, :hybrid)
- `config::KeyNumberConfig`: 關鍵號碼配置（可選）

# Returns
- `KeyNumberResult`: 關鍵號碼選擇結果
"""
function select_key_number(
    drawings::Vector{Drawing},
    strategy::Symbol,
    config::KeyNumberConfig = KeyNumberConfig(strategy=strategy)
)::KeyNumberResult
    
    if isempty(drawings)
        throw(WonderGridError("開獎數據不能為空", :empty_data))
    end
    
    println("開始選擇關鍵號碼，策略: $strategy")
    
    # 根據策略選擇關鍵號碼
    result = if strategy == :ffg
        select_key_number_by_ffg(drawings, config)
    elseif strategy == :skip
        select_key_number_by_skip(drawings, config)
    elseif strategy == :frequency
        select_key_number_by_frequency(drawings, config)
    elseif strategy == :hybrid
        select_key_number_by_hybrid(drawings, config)
    else
        throw(WonderGridError("無效的關鍵號碼選擇策略: $strategy", :invalid_strategy))
    end
    
    println("關鍵號碼選擇完成: $(result.number) (評分: $(round(result.score, digits=3)))")
    
    return result
end

"""
基於 FFG 理論選擇關鍵號碼
"""
function select_key_number_by_ffg(
    drawings::Vector{Drawing},
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("使用 FFG 理論選擇關鍵號碼...")
    
    # 限制分析深度
    analysis_drawings = length(drawings) > config.analysis_depth ? 
                       drawings[1:config.analysis_depth] : drawings
    
    # 計算每個號碼的 FFG 值
    game_config = GAME_CONFIGURATIONS[drawings[1].game_type]
    ffg_scores = Dict{Int, Float64}()
    
    for number in 1:game_config.max_number
        # 計算該號碼的跳躍值
        skip_values = calculate_number_skip_values(number, analysis_drawings)
        
        if !isempty(skip_values)
            # 計算 FFG 統計
            ffg_stats = calculate_ffg(skip_values)
            
            # FFG 評分：基於中位數和標準差
            median_score = ffg_stats.median > 0 ? 1.0 / ffg_stats.median : 0.0
            std_score = ffg_stats.standard_deviation > 0 ? 1.0 / ffg_stats.standard_deviation : 0.0
            
            # 綜合 FFG 評分
            ffg_score = 0.6 * median_score + 0.4 * std_score
            
            # 應用 FFG 閾值
            if ffg_score >= config.ffg_threshold
                ffg_scores[number] = ffg_score
            end
        end
    end
    
    if isempty(ffg_scores)
        throw(WonderGridError("沒有號碼滿足 FFG 閾值要求", :no_qualified_numbers))
    end
    
    # 選擇最高 FFG 評分的號碼
    best_number = argmax(ffg_scores)
    best_score = ffg_scores[best_number]
    
    # 生成分析數據
    analysis_data = Dict{String, Any}(
        "ffg_scores" => ffg_scores,
        "analysis_depth" => length(analysis_drawings),
        "threshold_used" => config.ffg_threshold,
        "qualified_numbers" => length(ffg_scores)
    )
    
    # 計算信心度
    confidence = calculate_ffg_confidence(best_score, ffg_scores, config)
    
    return KeyNumberResult(best_number, :ffg, best_score, analysis_data, confidence)
end

"""
基於跳躍分析選擇關鍵號碼
"""
function select_key_number_by_skip(
    drawings::Vector{Drawing},
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("使用跳躍分析選擇關鍵號碼...")
    
    analysis_drawings = length(drawings) > config.analysis_depth ? 
                       drawings[1:config.analysis_depth] : drawings
    
    game_config = GAME_CONFIGURATIONS[drawings[1].game_type]
    skip_scores = Dict{Int, Float64}()
    
    for number in 1:game_config.max_number
        skip_values = calculate_number_skip_values(number, analysis_drawings)
        
        if !isempty(skip_values)
            # 計算跳躍評分
            current_skip = skip_values[1]  # 當前跳躍值
            avg_skip = mean(skip_values)
            min_skip = minimum(skip_values)
            
            # 跳躍評分：當前跳躍值越大，平均跳躍值越小，評分越高
            current_skip_score = current_skip / max(1.0, avg_skip)
            consistency_score = min_skip > 0 ? avg_skip / min_skip : 1.0
            
            skip_score = 0.7 * current_skip_score + 0.3 * (1.0 / consistency_score)
            skip_scores[number] = skip_score
        end
    end
    
    if isempty(skip_scores)
        throw(WonderGridError("無法計算跳躍評分", :skip_calculation_failed))
    end
    
    best_number = argmax(skip_scores)
    best_score = skip_scores[best_number]
    
    analysis_data = Dict{String, Any}(
        "skip_scores" => skip_scores,
        "analysis_depth" => length(analysis_drawings),
        "avg_skip_values" => Dict(num => mean(calculate_number_skip_values(num, analysis_drawings)) 
                                 for num in keys(skip_scores))
    )
    
    confidence = calculate_skip_confidence(best_score, skip_scores, config)
    
    return KeyNumberResult(best_number, :skip, best_score, analysis_data, confidence)
end

"""
基於頻率分析選擇關鍵號碼
"""
function select_key_number_by_frequency(
    drawings::Vector{Drawing},
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("使用頻率分析選擇關鍵號碼...")
    
    analysis_drawings = length(drawings) > config.analysis_depth ? 
                       drawings[1:config.analysis_depth] : drawings
    
    # 計算號碼頻率（內聯實現）
    max_number = 49  # 假設是 Lotto 6/49
    number_frequencies = Dict{Int, Int}()

    # 初始化所有號碼的頻率為0
    for number in 1:max_number
        number_frequencies[number] = 0
    end

    # 計算每個號碼的出現頻率
    for drawing in analysis_drawings
        for number in drawing.numbers
            if haskey(number_frequencies, number)
                number_frequencies[number] += 1
            end
        end
    end
    
    if isempty(number_frequencies)
        throw(WonderGridError("無法計算號碼頻率", :frequency_calculation_failed))
    end
    
    # 計算頻率評分
    max_frequency = maximum(values(number_frequencies))
    min_frequency = minimum(values(number_frequencies))
    frequency_range = max_frequency - min_frequency
    
    frequency_scores = Dict{Int, Float64}()
    
    for (number, frequency) in number_frequencies
        # 標準化頻率評分
        if frequency_range > 0
            normalized_freq = (frequency - min_frequency) / frequency_range
        else
            normalized_freq = 1.0
        end
        
        # 計算最近出現位置
        recent_position = find_recent_position(number, analysis_drawings)
        recency_score = recent_position > 0 ? 1.0 / recent_position : 0.0
        
        # 綜合頻率評分
        frequency_score = 0.6 * normalized_freq + 0.4 * recency_score
        frequency_scores[number] = frequency_score
    end
    
    best_number = argmax(frequency_scores)
    best_score = frequency_scores[best_number]
    
    analysis_data = Dict{String, Any}(
        "frequency_scores" => frequency_scores,
        "raw_frequencies" => number_frequencies,
        "analysis_depth" => length(analysis_drawings),
        "frequency_range" => frequency_range
    )
    
    confidence = calculate_frequency_confidence(best_score, frequency_scores, config)
    
    return KeyNumberResult(best_number, :frequency, best_score, analysis_data, confidence)
end

"""
混合策略選擇關鍵號碼
"""
function select_key_number_by_hybrid(
    drawings::Vector{Drawing},
    config::KeyNumberConfig
)::KeyNumberResult
    
    println("使用混合策略選擇關鍵號碼...")
    
    if !config.enable_hybrid
        throw(WonderGridError("混合策略未啟用", :hybrid_disabled))
    end
    
    # 分別計算各策略的結果
    ffg_result = select_key_number_by_ffg(drawings, config)
    skip_result = select_key_number_by_skip(drawings, config)
    frequency_result = select_key_number_by_frequency(drawings, config)
    
    # 收集所有候選號碼
    candidates = Dict{Int, Float64}()
    
    # FFG 策略貢獻
    add_candidate_score!(candidates, ffg_result.number, ffg_result.score * config.skip_weight)
    
    # 跳躍策略貢獻
    add_candidate_score!(candidates, skip_result.number, skip_result.score * config.skip_weight)
    
    # 頻率策略貢獻
    add_candidate_score!(candidates, frequency_result.number, frequency_result.score * config.frequency_weight)
    
    # 計算趨勢權重
    trend_scores = calculate_trend_scores(drawings, config)
    for (number, trend_score) in trend_scores
        add_candidate_score!(candidates, number, trend_score * config.trend_weight)
    end
    
    if isempty(candidates)
        throw(WonderGridError("混合策略無法找到候選號碼", :no_hybrid_candidates))
    end
    
    # 選擇最高綜合評分的號碼
    best_number = argmax(candidates)
    best_score = candidates[best_number]
    
    # 生成混合分析數據
    analysis_data = Dict{String, Any}(
        "hybrid_scores" => candidates,
        "ffg_result" => ffg_result,
        "skip_result" => skip_result,
        "frequency_result" => frequency_result,
        "trend_scores" => trend_scores,
        "weights" => Dict(
            "ffg" => config.skip_weight,  # 注意：這裡使用 skip_weight 是因為 FFG 基於跳躍
            "skip" => config.skip_weight,
            "frequency" => config.frequency_weight,
            "trend" => config.trend_weight
        )
    )
    
    # 計算混合信心度
    confidence = calculate_hybrid_confidence(best_score, candidates, [ffg_result, skip_result, frequency_result], config)
    
    return KeyNumberResult(best_number, :hybrid, best_score, analysis_data, confidence)
end

"""
計算號碼的跳躍值序列
"""
function calculate_number_skip_values(number::Int, drawings::Vector{Drawing})::Vector{Int}
    skip_values = Int[]
    last_position = -1
    
    for (position, drawing) in enumerate(drawings)
        if number in drawing.numbers
            if last_position >= 0
                skip = position - last_position - 1
                push!(skip_values, skip)
            end
            last_position = position
        end
    end
    
    # 添加當前跳躍值（距離最後一次出現）
    if last_position >= 0
        current_skip = last_position - 1
        if current_skip >= 0
            pushfirst!(skip_values, current_skip)  # 插入到開頭
        end
    end
    
    return skip_values
end

"""
查找號碼最近出現位置
"""
function find_recent_position(number::Int, drawings::Vector{Drawing})::Int
    for (position, drawing) in enumerate(drawings)
        if number in drawing.numbers
            return position
        end
    end
    return length(drawings) + 1  # 如果沒找到，返回超出範圍的位置
end

"""
添加候選號碼評分
"""
function add_candidate_score!(candidates::Dict{Int, Float64}, number::Int, score::Float64)
    if haskey(candidates, number)
        candidates[number] += score
    else
        candidates[number] = score
    end
end

"""
計算趨勢評分
"""
function calculate_trend_scores(drawings::Vector{Drawing}, config::KeyNumberConfig)::Dict{Int, Float64}
    if length(drawings) < 10
        return Dict{Int, Float64}()
    end
    
    # 分析最近趨勢
    recent_drawings = drawings[1:min(20, length(drawings))]
    older_drawings = length(drawings) > 20 ? drawings[21:min(40, length(drawings))] : Drawing[]
    
    trend_scores = Dict{Int, Float64}()
    
    if !isempty(older_drawings)
        recent_freq = calculate_number_frequencies(recent_drawings, 49)
        older_freq = calculate_number_frequencies(older_drawings, 49)
        
        # 計算趨勢變化
        for number in keys(recent_freq)
            recent_count = get(recent_freq, number, 0)
            older_count = get(older_freq, number, 0)
            trend_score = (recent_count - older_count) / max(1, older_count)
            trend_scores[number] = trend_score
        end
    end
    
    return trend_scores
end

"""
篩選頂級配對

# Arguments
- `pair_statistics::Dict{Tuple{Int,Int}, PairStatistics}`: 配對統計數據
- `key_number::Int`: 關鍵號碼
- `config::WonderGridConfig`: 配置

# Returns
- `Vector{TopPair}`: 頂級配對列表
"""
function select_top_pairs_advanced(
    pair_statistics::Dict{Tuple{Int,Int}, PairStatistics},
    key_number::Int,
    config::WonderGridConfig
)::Vector{TopPair}
    
    if isempty(pair_statistics)
        throw(WonderGridError("配對統計數據為空", :empty_pair_statistics))
    end
    
    println("開始篩選頂級配對，關鍵號碼: $key_number")
    
    # 篩選包含關鍵號碼的配對
    key_pairs = filter_key_number_pairs(pair_statistics, key_number)
    
    if isempty(key_pairs)
        throw(WonderGridError("沒有找到包含關鍵號碼的配對", :no_key_pairs))
    end
    
    println("找到 $(length(key_pairs)) 個包含關鍵號碼的配對")
    
    # 計算配對質量評分
    scored_pairs = calculate_pair_quality_scores(key_pairs, config)
    
    # 應用多重篩選條件
    filtered_pairs = apply_multiple_filters(scored_pairs, config)
    
    # 排序並選擇頂級配對
    top_pairs = select_final_top_pairs(filtered_pairs, config)
    
    println("篩選完成，選出 $(length(top_pairs)) 個頂級配對")
    
    return top_pairs
end

"""
篩選包含關鍵號碼的配對
"""
function filter_key_number_pairs(
    pair_statistics::Dict{Tuple{Int,Int}, PairStatistics},
    key_number::Int
)::Dict{Tuple{Int,Int}, PairStatistics}
    
    key_pairs = Dict{Tuple{Int,Int}, PairStatistics}()
    
    for ((num1, num2), stats) in pair_statistics
        if num1 == key_number || num2 == key_number
            key_pairs[(num1, num2)] = stats
        end
    end
    
    return key_pairs
end

"""
計算配對質量評分
"""
function calculate_pair_quality_scores(
    pairs::Dict{Tuple{Int,Int}, PairStatistics},
    config::WonderGridConfig
)::Vector{ScoredPair}
    
    scored_pairs = ScoredPair[]
    
    # 計算全局統計用於標準化
    all_frequencies = [stats.frequency for stats in values(pairs)]
    all_recent_frequencies = [stats.recent_frequency for stats in values(pairs)]
    all_skip_values = [stats.avg_skip for stats in values(pairs)]
    
    max_freq = maximum(all_frequencies)
    min_freq = minimum(all_frequencies)
    max_recent_freq = maximum(all_recent_frequencies)
    min_recent_freq = minimum(all_recent_frequencies)
    max_skip = maximum(all_skip_values)
    min_skip = minimum(all_skip_values)
    
    for ((num1, num2), stats) in pairs
        # 計算各項評分
        frequency_score = normalize_score(Float64(stats.frequency), Float64(min_freq), Float64(max_freq))
        recent_score = normalize_score(Float64(stats.recent_frequency), Float64(min_recent_freq), Float64(max_recent_freq))
        skip_score = 1.0 - normalize_score(stats.avg_skip, Float64(min_skip), Float64(max_skip))  # 跳躍值越小越好
        trend_score = calculate_trend_score(stats)
        stability_score = calculate_stability_score(stats)
        momentum_score = calculate_momentum_score(stats)
        
        # 綜合質量評分
        quality_score = (
            0.25 * frequency_score +
            0.20 * recent_score +
            0.15 * skip_score +
            0.15 * trend_score +
            0.15 * stability_score +
            0.10 * momentum_score
        )
        
        # 應用配對特殊性加權
        specialty_bonus = calculate_specialty_bonus((num1, num2), stats, config)
        final_score = quality_score * (1.0 + specialty_bonus)
        
        scored_pair = ScoredPair(
            (num1, num2), stats, final_score, frequency_score, recent_score,
            skip_score, trend_score, stability_score, momentum_score, specialty_bonus
        )
        
        push!(scored_pairs, scored_pair)
    end
    
    return scored_pairs
end

"""
標準化評分到 0-1 範圍
"""
function normalize_score(value::Float64, min_val::Float64, max_val::Float64)::Float64
    if max_val <= min_val
        return 1.0
    end
    return (value - min_val) / (max_val - min_val)
end

"""
計算趨勢評分
"""
function calculate_trend_score(stats::PairStatistics)::Float64
    if stats.frequency == 0
        return 0.0
    end
    
    # 趨勢評分基於最近頻率相對於總頻率的比例
    trend_ratio = stats.recent_frequency / stats.frequency
    
    # 理想的趨勢比例應該略高於平均水平
    ideal_ratio = 0.3  # 假設最近30%的數據應該包含30%的出現次數
    
    if trend_ratio >= ideal_ratio
        return min(1.0, trend_ratio / ideal_ratio)
    else
        return trend_ratio / ideal_ratio * 0.8  # 低於理想比例的懲罰
    end
end

"""
計算穩定性評分
"""
function calculate_stability_score(stats::PairStatistics)::Float64
    if stats.skip_variance <= 0
        return 1.0  # 完全穩定
    end
    
    # 穩定性評分基於跳躍值的變異係數
    coefficient_of_variation = sqrt(stats.skip_variance) / max(1.0, stats.avg_skip)
    
    # 變異係數越小，穩定性越高
    stability = 1.0 / (1.0 + coefficient_of_variation)
    
    return min(1.0, stability)
end

"""
計算動量評分
"""
function calculate_momentum_score(stats::PairStatistics)::Float64
    if stats.last_appearance <= 0
        return 0.0
    end
    
    # 動量評分基於最後出現位置和平均跳躍值
    expected_next_appearance = stats.last_appearance + stats.avg_skip
    current_position = 1  # 當前位置（最新數據）

    # 如果當前位置接近預期出現位置，動量評分較高
    position_diff = abs(current_position - expected_next_appearance)
    momentum = 1.0 / (1.0 + position_diff / max(1.0, stats.avg_skip))
    
    return min(1.0, momentum)
end

"""
計算配對特殊性加權
"""
function calculate_specialty_bonus(
    pair::Tuple{Int,Int},
    stats::PairStatistics,
    config::WonderGridConfig
)::Float64
    
    num1, num2 = pair
    bonus = 0.0
    
    # 連續號碼加權
    if abs(num1 - num2) == 1
        bonus += 0.1
    end
    
    # 同奇偶性加權
    if (num1 % 2) == (num2 % 2)
        bonus += 0.05
    end
    
    # 特殊數字組合加權（如尾數相同）
    if (num1 % 10) == (num2 % 10)
        bonus += 0.08
    end
    
    # 黃金比例加權（斐波那契數列相關）
    fibonacci_numbers = Set([1, 2, 3, 5, 8, 13, 21, 34])
    if num1 in fibonacci_numbers && num2 in fibonacci_numbers
        bonus += 0.12
    end
    
    # 高頻配對額外加權
    if stats.frequency >= 10  # 假設10次以上為高頻
        bonus += 0.15
    end
    
    return min(0.5, bonus)  # 限制最大加權為50%
end

"""
應用多重篩選條件
"""
function apply_multiple_filters(
    scored_pairs::Vector{ScoredPair},
    config::WonderGridConfig
)::Vector{ScoredPair}
    
    filtered_pairs = scored_pairs
    
    # 篩選1: 最低質量評分（放寬條件）
    min_quality_threshold = 0.1
    filtered_pairs = filter(p -> p.quality_score >= min_quality_threshold, filtered_pairs)

    # 篩選2: 最低頻率要求（放寬條件）
    min_frequency = 1
    filtered_pairs = filter(p -> p.statistics.frequency >= min_frequency, filtered_pairs)

    # 篩選3: 最大跳躍值限制
    max_skip_threshold = 100
    filtered_pairs = filter(p -> p.statistics.avg_skip <= max_skip_threshold, filtered_pairs)

    # 篩選4: 趨勢篩選（放寬條件）
    min_trend_score = 0.0
    filtered_pairs = filter(p -> p.trend_score >= min_trend_score, filtered_pairs)

    # 篩選5: 穩定性篩選（放寬條件）
    min_stability = 0.0
    filtered_pairs = filter(p -> p.stability_score >= min_stability, filtered_pairs)
    
    println("多重篩選後剩餘 $(length(filtered_pairs)) 個配對")
    
    return filtered_pairs
end

"""
選擇最終頂級配對
"""
function select_final_top_pairs(
    filtered_pairs::Vector{ScoredPair},
    config::WonderGridConfig
)::Vector{TopPair}
    
    if isempty(filtered_pairs)
        throw(WonderGridError("篩選後沒有剩餘配對", :no_pairs_after_filter))
    end
    
    # 按質量評分排序
    sorted_pairs = sort(filtered_pairs, by = p -> p.quality_score, rev = true)
    
    # 計算選擇數量
    total_pairs = length(sorted_pairs)
    target_count = max(1, Int(round(total_pairs * config.top_pair_percentage)))
    target_count = min(target_count, 20)  # 限制最大數量
    
    # 應用多樣性選擇
    selected_pairs = apply_diversity_selection(sorted_pairs, target_count, config)
    
    # 轉換為 TopPair 結構
    top_pairs = TopPair[]
    
    for (rank, scored_pair) in enumerate(selected_pairs)
        top_pair = TopPair(
            scored_pair.pair,
            scored_pair.statistics.frequency,
            scored_pair.statistics.recent_frequency,
            scored_pair.statistics.avg_skip,
            scored_pair.statistics.last_appearance,
            scored_pair.quality_score,
            rank,
            generate_selection_reason(scored_pair)
        )
        
        push!(top_pairs, top_pair)
    end
    
    return top_pairs
end

"""
應用多樣性選擇策略
"""
function apply_diversity_selection(
    sorted_pairs::Vector{ScoredPair},
    target_count::Int,
    config::WonderGridConfig
)::Vector{ScoredPair}
    
    if length(sorted_pairs) <= target_count
        return sorted_pairs
    end
    
    selected = ScoredPair[]
    remaining = copy(sorted_pairs)
    
    # 首先選擇最高評分的配對
    push!(selected, popfirst!(remaining))
    
    # 使用多樣性策略選擇其餘配對
    while length(selected) < target_count && !isempty(remaining)
        best_candidate = nothing
        best_diversity_score = -1.0
        best_index = 0
        
        for (i, candidate) in enumerate(remaining)
            # 計算多樣性評分
            diversity_score = calculate_diversity_score(candidate, selected)
            combined_score = 0.7 * candidate.quality_score + 0.3 * diversity_score
            
            if combined_score > best_diversity_score
                best_diversity_score = combined_score
                best_candidate = candidate
                best_index = i
            end
        end
        
        if best_candidate !== nothing
            push!(selected, best_candidate)
            deleteat!(remaining, best_index)
        else
            break
        end
    end
    
    return selected
end

"""
計算多樣性評分
"""
function calculate_diversity_score(
    candidate::ScoredPair,
    selected::Vector{ScoredPair}
)::Float64
    
    if isempty(selected)
        return 1.0
    end
    
    candidate_num1, candidate_num2 = candidate.pair
    diversity_score = 1.0
    
    for selected_pair in selected
        selected_num1, selected_num2 = selected_pair.pair
        
        # 檢查號碼重疊
        overlap = 0
        if candidate_num1 == selected_num1 || candidate_num1 == selected_num2
            overlap += 1
        end
        if candidate_num2 == selected_num1 || candidate_num2 == selected_num2
            overlap += 1
        end
        
        # 重疊越多，多樣性越低
        if overlap > 0
            diversity_score *= (1.0 - overlap * 0.3)
        end
        
        # 檢查數字範圍相似性
        candidate_range = abs(candidate_num2 - candidate_num1)
        selected_range = abs(selected_num2 - selected_num1)
        range_similarity = 1.0 - abs(candidate_range - selected_range) / 49.0
        
        if range_similarity > 0.8
            diversity_score *= 0.9
        end
    end
    
    return max(0.0, diversity_score)
end

"""
生成選擇原因
"""
function generate_selection_reason(scored_pair::ScoredPair)::String
    reasons = String[]
    
    if scored_pair.frequency_score > 0.8
        push!(reasons, "高頻率")
    end
    
    if scored_pair.recent_score > 0.7
        push!(reasons, "近期活躍")
    end
    
    if scored_pair.skip_score > 0.8
        push!(reasons, "跳躍穩定")
    end
    
    if scored_pair.trend_score > 0.7
        push!(reasons, "趨勢良好")
    end
    
    if scored_pair.stability_score > 0.8
        push!(reasons, "表現穩定")
    end
    
    if scored_pair.momentum_score > 0.7
        push!(reasons, "動量強勁")
    end
    
    if scored_pair.specialty_bonus > 0.1
        push!(reasons, "特殊組合")
    end
    
    if isempty(reasons)
        push!(reasons, "綜合評分優秀")
    end
    
    return join(reasons, ", ")
end

"""
優化配對選擇參數
"""
function optimize_pair_selection_parameters(
    historical_data::Vector{Drawing},
    config::WonderGridConfig,
    optimization_periods::Int = 30
)::Dict{String, Float64}
    
    if length(historical_data) < optimization_periods + 50
        throw(WonderGridError("數據不足以進行參數優化", :insufficient_data_for_optimization))
    end
    
    println("開始優化配對選擇參數...")
    
    # 定義參數搜索空間
    parameter_ranges = Dict(
        "top_pair_percentage" => [0.1, 0.15, 0.2, 0.25, 0.3, 0.35],
        "min_quality_threshold" => [0.2, 0.25, 0.3, 0.35, 0.4],
        "min_frequency" => [1, 2, 3, 4, 5],
        "max_skip_threshold" => [30, 40, 50, 60, 70]
    )
    
    best_parameters = Dict{String, Float64}()
    best_performance = 0.0
    
    # 網格搜索最佳參數組合
    for top_percentage in parameter_ranges["top_pair_percentage"]
        for min_quality in parameter_ranges["min_quality_threshold"]
            for min_freq in parameter_ranges["min_frequency"]
                for max_skip in parameter_ranges["max_skip_threshold"]
                    
                    # 創建測試配置
                    test_config = WonderGridConfig(
                        analysis_range = config.analysis_range,
                        top_pair_percentage = top_percentage,
                        key_number_strategy = config.key_number_strategy,
                        combination_limit = config.combination_limit,
                        enable_purge = config.enable_purge,
                        enable_lie = config.enable_lie,
                        game_type = config.game_type
                    )
                    
                    # 評估參數組合
                    performance = evaluate_parameter_combination(
                        historical_data, test_config, optimization_periods,
                        min_quality, min_freq, max_skip
                    )
                    
                    if performance > best_performance
                        best_performance = performance
                        best_parameters = Dict(
                            "top_pair_percentage" => top_percentage,
                            "min_quality_threshold" => min_quality,
                            "min_frequency" => min_freq,
                            "max_skip_threshold" => max_skip
                        )
                    end
                end
            end
        end
    end
    
    println("參數優化完成，最佳性能: $(round(best_performance, digits=3))")
    println("最佳參數: $best_parameters")
    
    return best_parameters
end

"""
評估參數組合的性能
"""
function evaluate_parameter_combination(
    historical_data::Vector{Drawing},
    config::WonderGridConfig,
    periods::Int,
    min_quality::Float64,
    min_freq::Int,
    max_skip::Float64
)::Float64
    
    total_score = 0.0
    valid_evaluations = 0
    
    for i in 1:periods
        try
            # 使用歷史數據進行訓練
            training_data = historical_data[(i+20):end]
            
            if length(training_data) >= 50
                # 計算配對統計
                pair_stats = calculate_advanced_pair_statistics(training_data, config)
                
                # 選擇關鍵號碼
                key_result = select_key_number(training_data, config.key_number_strategy)
                key_number = key_result.number
                
                # 篩選頂級配對（使用測試參數）
                key_pairs = filter_key_number_pairs(pair_stats, key_number)
                scored_pairs = calculate_pair_quality_scores(key_pairs, config)
                
                # 應用多重篩選條件
                filtered_pairs = apply_multiple_filters(scored_pairs, config)
                
                # 排序並選擇頂級配對
                top_pairs = select_final_top_pairs(filtered_pairs, config)
                
                # 生成 Wonder Grid 組合
                game_config = GAME_CONFIGURATIONS[config.game_type]
                combinations = generate_wonder_combinations(key_number, top_pairs, config, game_config)
                
                # 評估組合性能（這裡使用簡單的組合數量作為評估指標）
                if !isempty(combinations)
                    total_score += length(combinations)
                    valid_evaluations += 1
                end
            end
        catch
            # 忽略錯誤的評估
        end
    end
    
    if valid_evaluations == 0
        return 0.0
    end
    
    return total_score / valid_evaluations
end

"""
生成 Wonder Grid 組合

# Arguments
- `key_number::Int`: 關鍵號碼
- `top_pairs::Vector{TopPair}`: 頂級配對
- `config::WonderGridConfig`: 配置
- `game_config::GameConfiguration`: 遊戲配置

# Returns
- `Vector{Combination}`: 生成的組合列表
"""
function generate_wonder_combinations(
    key_number::Int,
    top_pairs::Vector{TopPair},
    config::WonderGridConfig,
    game_config::GameConfiguration
)::Vector{Combination}
    
    if isempty(top_pairs)
        throw(WonderGridError("頂級配對列表為空", :empty_top_pairs))
    end
    
    println("開始生成 Wonder Grid 組合...")
    println("關鍵號碼: $key_number, 頂級配對數量: $(length(top_pairs))")
    
    # 提取配對夥伴號碼
    partner_numbers = extract_partner_numbers(key_number, top_pairs)
    
    # 如果配對夥伴不足，使用隨機號碼補充
    required_partners = game_config.numbers_per_draw - 1
    if length(partner_numbers) < required_partners
        println("配對夥伴不足 ($(length(partner_numbers))/$required_partners)，使用隨機號碼補充...")

        # 獲取所有可用號碼（排除關鍵號碼和已有的配對夥伴）
        all_numbers = collect(1:game_config.max_number)
        excluded_numbers = Set([key_number])
        union!(excluded_numbers, partner_numbers)

        available_numbers = filter(n -> !(n in excluded_numbers), all_numbers)

        # 隨機選擇額外的號碼
        needed_count = required_partners - length(partner_numbers)
        if length(available_numbers) >= needed_count
            shuffled_numbers = shuffle(available_numbers)
            additional_numbers = shuffled_numbers[1:needed_count]
            append!(partner_numbers, additional_numbers)
            println("添加了 $needed_count 個隨機號碼: $additional_numbers")
        else
            throw(WonderGridError("可用號碼不足以生成完整組合", :insufficient_numbers))
        end
    end
    
    # 生成基礎組合
    base_combinations = generate_base_combinations(key_number, partner_numbers, game_config, config)
    
    # 應用組合優化
    optimized_combinations = optimize_combinations(base_combinations, config, game_config)
    
    # 應用組合驗證和篩選
    validated_combinations = validate_and_filter_combinations(optimized_combinations, config, game_config)
    
    # 限制組合數量
    final_combinations = limit_combinations(validated_combinations, config)
    
    println("Wonder Grid 組合生成完成，共生成 $(length(final_combinations)) 個組合")
    
    return final_combinations
end

"""
提取配對夥伴號碼
"""
function extract_partner_numbers(key_number::Int, top_pairs::Vector{TopPair})::Vector{Int}
    partner_numbers = Int[]
    partner_weights = Dict{Int, Float64}()
    
    for pair in top_pairs
        num1, num2 = pair.pair
        partner = (num1 == key_number) ? num2 : num1
        
        if partner != key_number
            if haskey(partner_weights, partner)
                # 如果號碼出現在多個配對中，累加權重
                partner_weights[partner] += pair.quality_score
            else
                partner_weights[partner] = pair.quality_score
                push!(partner_numbers, partner)
            end
        end
    end
    
    # 按權重排序配對夥伴
    sort!(partner_numbers, by = num -> partner_weights[num], rev = true)
    
    println("提取到 $(length(partner_numbers)) 個配對夥伴號碼")
    
    return partner_numbers
end

"""
生成基礎組合
"""
function generate_base_combinations(
    key_number::Int,
    partner_numbers::Vector{Int},
    game_config::GameConfiguration,
    config::WonderGridConfig
)::Vector{Vector{Int}}
    
    combinations = Vector{Int}[]
    needed_numbers = game_config.numbers_per_draw - 1  # 除了關鍵號碼外還需要的號碼數
    
    # 策略1: 高權重配對夥伴組合
    high_priority_partners = partner_numbers[1:min(needed_numbers * 2, length(partner_numbers))]
    strategy1_combinations = generate_combinations_from_partners(
        key_number, high_priority_partners, needed_numbers, :high_priority
    )
    append!(combinations, strategy1_combinations)
    
    # 策略2: 平衡分佈組合
    if length(partner_numbers) >= needed_numbers
        strategy2_combinations = generate_balanced_combinations(
            key_number, partner_numbers, needed_numbers, game_config
        )
        append!(combinations, strategy2_combinations)
    end
    
    # 策略3: 多樣性組合
    strategy3_combinations = generate_diversity_combinations(
        key_number, partner_numbers, needed_numbers, game_config
    )
    append!(combinations, strategy3_combinations)
    
    # 去除重複組合
    unique_combinations = remove_duplicate_combinations(combinations)
    
    println("生成基礎組合 $(length(unique_combinations)) 個")
    
    return unique_combinations
end

"""
從配對夥伴生成組合
"""
function generate_combinations_from_partners(
    key_number::Int,
    partners::Vector{Int},
    needed_numbers::Int,
    strategy::Symbol
)::Vector{Vector{Int}}
    
    combinations = Vector{Int}[]
    
    if length(partners) < needed_numbers
        return combinations
    end
    
    if strategy == :high_priority
        # 使用最高權重的配對夥伴
        for i in 1:(min(10, length(partners) - needed_numbers + 1))
            combination = [key_number]
            append!(combination, partners[i:(i + needed_numbers - 1)])
            sort!(combination)
            push!(combinations, combination)
        end
    elseif strategy == :sliding_window
        # 滑動窗口策略
        window_size = needed_numbers
        for i in 1:(length(partners) - window_size + 1)
            combination = [key_number]
            append!(combination, partners[i:(i + window_size - 1)])
            sort!(combination)
            push!(combinations, combination)
        end
    end
    
    return combinations
end

"""
生成平衡分佈組合
"""
function generate_balanced_combinations(
    key_number::Int,
    partners::Vector{Int},
    needed_numbers::Int,
    game_config::GameConfiguration
)::Vector{Vector{Int}}
    
    combinations = Vector{Int}[]
    
    # 將號碼範圍分成幾個區間
    max_number = game_config.max_number
    num_segments = 3
    segment_size = max_number ÷ num_segments
    
    # 按區間分組配對夥伴
    segments = [Int[] for _ in 1:num_segments]
    for partner in partners
        segment_index = min(num_segments, (partner - 1) ÷ segment_size + 1)
        push!(segments[segment_index], partner)
    end
    
    # 從每個區間選擇號碼生成平衡組合
    for attempt in 1:5
        combination = [key_number]
        used_segments = Set{Int}()
        
        # 嘗試從不同區間選擇號碼
        for _ in 1:needed_numbers
            available_segments = [i for i in 1:num_segments if i ∉ used_segments && !isempty(segments[i])]
            
            if !isempty(available_segments)
                segment_idx = rand(available_segments)
                if !isempty(segments[segment_idx])
                    selected_number = rand(segments[segment_idx])
                    if selected_number ∉ combination
                        push!(combination, selected_number)
                        push!(used_segments, segment_idx)
                    end
                end
            else
                # 如果沒有可用區間，從所有配對夥伴中隨機選擇
                available_partners = [p for p in partners if p ∉ combination]
                if !isempty(available_partners)
                    push!(combination, rand(available_partners))
                end
            end
        end
        
        if length(combination) == game_config.numbers_per_draw
            sort!(combination)
            push!(combinations, combination)
        end
    end
    
    return combinations
end

"""
生成多樣性組合
"""
function generate_diversity_combinations(
    key_number::Int,
    partners::Vector{Int},
    needed_numbers::Int,
    game_config::GameConfiguration
)::Vector{Vector{Int}}
    
    combinations = Vector{Int}[]
    
    # 多樣性策略：確保號碼分佈的多樣性
    for attempt in 1:8
        combination = [key_number]
        available_partners = copy(partners)
        
        while length(combination) < game_config.numbers_per_draw && !isempty(available_partners)
            # 計算每個候選號碼的多樣性評分
            best_candidate = nothing
            best_diversity = -1.0
            best_index = 0
            
            for (i, candidate) in enumerate(available_partners)
                diversity_score = calculate_number_diversity(candidate, combination, game_config)
                
                if diversity_score > best_diversity
                    best_diversity = diversity_score
                    best_candidate = candidate
                    best_index = i
                end
            end
            
            if best_candidate !== nothing
                push!(combination, best_candidate)
                deleteat!(available_partners, best_index)
            else
                break
            end
        end
        
        if length(combination) == game_config.numbers_per_draw
            sort!(combination)
            push!(combinations, combination)
        end
    end
    
    return combinations
end

"""
計算號碼多樣性評分
"""
function calculate_number_diversity(
    candidate::Int,
    current_combination::Vector{Int},
    game_config::GameConfiguration
)::Float64
    
    if candidate in current_combination
        return 0.0
    end
    
    diversity_score = 1.0
    
    for existing_number in current_combination
        # 距離多樣性
        distance = abs(candidate - existing_number)
        distance_score = min(1.0, distance / 10.0)
        diversity_score *= distance_score
        
        # 奇偶多樣性
        if (candidate % 2) == (existing_number % 2)
            diversity_score *= 0.9
        end
        
        # 尾數多樣性
        if (candidate % 10) == (existing_number % 10)
            diversity_score *= 0.8
        end
    end
    
    return diversity_score
end

"""
去除重複組合
"""
function remove_duplicate_combinations(combinations::Vector{Vector{Int}})::Vector{Vector{Int}}
    unique_combinations = Vector{Int}[]
    seen_combinations = Set{Vector{Int}}()
    
    for combination in combinations
        sorted_combination = sort(copy(combination))
        if sorted_combination ∉ seen_combinations
            push!(seen_combinations, sorted_combination)
            push!(unique_combinations, sorted_combination)
        end
    end
    
    return unique_combinations
end

"""
優化組合
"""
function optimize_combinations(
    combinations::Vector{Vector{Int}},
    config::WonderGridConfig,
    game_config::GameConfiguration
)::Vector{Vector{Int}}
    
    if isempty(combinations)
        return combinations
    end
    
    println("開始優化組合...")
    
    optimized = Vector{Int}[]
    
    for combination in combinations
        # 應用各種優化策略
        optimized_combination = apply_combination_optimizations(combination, config, game_config)
        
        if optimized_combination !== nothing
            push!(optimized, optimized_combination)
        end
    end
    
    println("組合優化完成，保留 $(length(optimized)) 個組合")
    
    return optimized
end

"""
應用組合優化策略
"""
function apply_combination_optimizations(
    combination::Vector{Int},
    config::WonderGridConfig,
    game_config::GameConfiguration
)::Union{Vector{Int}, Nothing}
    
    # 檢查基本有效性
    if length(combination) != game_config.numbers_per_draw
        return nothing
    end
    
    if !all(1 <= num <= game_config.max_number for num in combination)
        return nothing
    end
    
    if length(unique(combination)) != length(combination)
        return nothing
    end
    
    # 應用數學優化
    optimized = copy(combination)
    
    # 優化1: 奇偶平衡
    optimized = optimize_odd_even_balance(optimized, game_config)
    
    # 優化2: 數字分佈
    optimized = optimize_number_distribution(optimized, game_config)
    
    # 優化3: 和值範圍
    optimized = optimize_sum_range(optimized, game_config)
    
    return optimized
end

"""
優化奇偶平衡
"""
function optimize_odd_even_balance(
    combination::Vector{Int},
    game_config::GameConfiguration
)::Vector{Int}
    
    odd_count = count(num -> num % 2 == 1, combination)
    even_count = length(combination) - odd_count
    
    # 理想的奇偶比例
    ideal_odd = game_config.numbers_per_draw ÷ 2
    ideal_even = game_config.numbers_per_draw - ideal_odd
    
    # 如果奇偶比例嚴重失衡，嘗試調整
    if abs(odd_count - ideal_odd) > 2
        # 這裡可以實現更複雜的調整邏輯
        # 暫時返回原組合
    end
    
    return combination
end

"""
優化數字分佈
"""
function optimize_number_distribution(
    combination::Vector{Int},
    game_config::GameConfiguration
)::Vector{Int}
    
    # 檢查數字分佈是否過於集中
    sorted_combination = sort(combination)
    max_gap = 0
    min_gap = game_config.max_number
    
    for i in 2:length(sorted_combination)
        gap = sorted_combination[i] - sorted_combination[i-1]
        max_gap = max(max_gap, gap)
        min_gap = min(min_gap, gap)
    end
    
    # 如果分佈過於集中或分散，可以進行調整
    # 暫時返回原組合
    return combination
end

"""
優化和值範圍
"""
function optimize_sum_range(
    combination::Vector{Int},
    game_config::GameConfiguration
)::Vector{Int}
    
    combination_sum = sum(combination)
    
    # 計算理想和值範圍
    min_possible_sum = sum(1:game_config.numbers_per_draw)
    max_possible_sum = sum((game_config.max_number - game_config.numbers_per_draw + 1):game_config.max_number)
    
    ideal_min = min_possible_sum + (max_possible_sum - min_possible_sum) * 0.3
    ideal_max = min_possible_sum + (max_possible_sum - min_possible_sum) * 0.7
    
    # 如果和值在合理範圍內，保持不變
    if ideal_min <= combination_sum <= ideal_max
        return combination
    end
    
    # 否則可以嘗試調整（這裡暫時返回原組合）
    return combination
end

"""
驗證和篩選組合
"""
function validate_and_filter_combinations(
    combinations::Vector{Vector{Int}},
    config::WonderGridConfig,
    game_config::GameConfiguration
)::Vector{Vector{Int}}
    
    if isempty(combinations)
        return combinations
    end
    
    println("開始驗證和篩選組合...")
    
    validated = Vector{Int}[]
    
    for combination in combinations
        if validate_combination(combination, config, game_config)
            push!(validated, combination)
        end
    end
    
    # 應用高級篩選
    filtered = apply_advanced_filtering(validated, config, game_config)
    
    println("驗證篩選完成，保留 $(length(filtered)) 個組合")
    
    return filtered
end

"""
驗證單個組合
"""
function validate_combination(
    combination::Vector{Int},
    config::WonderGridConfig,
    game_config::GameConfiguration
)::Bool
    
    # 基本驗證
    if length(combination) != game_config.numbers_per_draw
        return false
    end
    
    if !all(1 <= num <= game_config.max_number for num in combination)
        return false
    end
    
    if length(unique(combination)) != length(combination)
        return false
    end
    
    # 數學驗證
    if !validate_mathematical_properties(combination, game_config)
        return false
    end
    
    return true
end

"""
驗證數學屬性
"""
function validate_mathematical_properties(
    combination::Vector{Int},
    game_config::GameConfiguration
)::Bool
    
    # 驗證奇偶比例
    odd_count = count(num -> num % 2 == 1, combination)
    if odd_count == 0 || odd_count == length(combination)
        return false  # 全奇數或全偶數不太可能
    end
    
    # 驗證連續號碼
    sorted_combination = sort(combination)
    consecutive_count = 0
    for i in 2:length(sorted_combination)
        if sorted_combination[i] == sorted_combination[i-1] + 1
            consecutive_count += 1
        end
    end
    
    if consecutive_count >= length(combination) - 1
        return false  # 全部連續號碼不太可能
    end
    
    # 驗證數字分佈
    min_num = minimum(combination)
    max_num = maximum(combination)
    if max_num - min_num < game_config.numbers_per_draw
        return false  # 分佈過於集中
    end
    
    return true
end

"""
應用高級篩選
"""
function apply_advanced_filtering(
    combinations::Vector{Vector{Int}},
    config::WonderGridConfig,
    game_config::GameConfiguration
)::Vector{Vector{Int}}
    
    if isempty(combinations)
        return combinations
    end
    
    # 計算每個組合的質量評分
    scored_combinations = [(combination, calculate_combination_quality(combination, config, game_config)) 
                          for combination in combinations]
    
    # 按質量評分排序
    sort!(scored_combinations, by = x -> x[2], rev = true)
    
    # 應用多樣性篩選
    filtered = apply_combination_diversity_filter(scored_combinations, config)
    
    return [combo for (combo, score) in filtered]
end

"""
計算組合質量評分
"""
function calculate_combination_quality(
    combination::Vector{Int},
    config::WonderGridConfig,
    game_config::GameConfiguration
)::Float64
    
    quality_score = 0.0
    
    # 奇偶平衡評分
    odd_count = count(num -> num % 2 == 1, combination)
    ideal_odd = game_config.numbers_per_draw ÷ 2
    odd_even_score = 1.0 - abs(odd_count - ideal_odd) / game_config.numbers_per_draw
    quality_score += 0.2 * odd_even_score
    
    # 數字分佈評分
    sorted_combination = sort(combination)
    gaps = [sorted_combination[i] - sorted_combination[i-1] for i in 2:length(sorted_combination)]
    gap_variance = var(gaps)
    distribution_score = 1.0 / (1.0 + gap_variance / 10.0)
    quality_score += 0.3 * distribution_score
    
    # 和值評分
    combination_sum = sum(combination)
    min_sum = sum(1:game_config.numbers_per_draw)
    max_sum = sum((game_config.max_number - game_config.numbers_per_draw + 1):game_config.max_number)
    ideal_sum = (min_sum + max_sum) / 2
    sum_score = 1.0 - abs(combination_sum - ideal_sum) / (max_sum - min_sum)
    quality_score += 0.2 * sum_score
    
    # 連續性評分
    consecutive_pairs = 0
    for i in 2:length(sorted_combination)
        if sorted_combination[i] == sorted_combination[i-1] + 1
            consecutive_pairs += 1
        end
    end
    consecutive_score = 1.0 - consecutive_pairs / (length(combination) - 1)
    quality_score += 0.15 * consecutive_score
    
    # 尾數多樣性評分
    last_digits = [num % 10 for num in combination]
    unique_last_digits = length(unique(last_digits))
    last_digit_score = unique_last_digits / length(combination)
    quality_score += 0.15 * last_digit_score
    
    return quality_score
end

"""
應用組合多樣性篩選
"""
function apply_combination_diversity_filter(
    scored_combinations::Vector{Tuple{Vector{Int}, Float64}},
    config::WonderGridConfig
)::Vector{Tuple{Vector{Int}, Float64}}
    
    if length(scored_combinations) <= config.combination_limit
        return scored_combinations
    end
    
    selected = Tuple{Vector{Int}, Float64}[]
    remaining = copy(scored_combinations)
    
    # 選擇最高評分的組合
    push!(selected, popfirst!(remaining))
    
    # 使用多樣性策略選擇其餘組合
    while length(selected) < config.combination_limit && !isempty(remaining)
        best_candidate = nothing
        best_combined_score = -1.0
        best_index = 0
        
        for (i, (candidate_combo, candidate_score)) in enumerate(remaining)
            # 計算與已選組合的多樣性
            diversity_score = calculate_combination_diversity(candidate_combo, selected)
            combined_score = 0.7 * candidate_score + 0.3 * diversity_score
            
            if combined_score > best_combined_score
                best_combined_score = combined_score
                best_candidate = (candidate_combo, candidate_score)
                best_index = i
            end
        end
        
        if best_candidate !== nothing
            push!(selected, best_candidate)
            deleteat!(remaining, best_index)
        else
            break
        end
    end
    
    return selected
end

"""
計算組合多樣性
"""
function calculate_combination_diversity(
    candidate::Vector{Int},
    selected::Vector{Tuple{Vector{Int}, Float64}}
)::Float64
    
    if isempty(selected)
        return 1.0
    end
    
    total_diversity = 0.0
    
    for (selected_combo, _) in selected
        # 計算號碼重疊度
        overlap = length(intersect(candidate, selected_combo))
        overlap_penalty = overlap / length(candidate)
        
        # 計算分佈相似度
        candidate_sum = sum(candidate)
        selected_sum = sum(selected_combo)
        sum_similarity = 1.0 - abs(candidate_sum - selected_sum) / max(candidate_sum, selected_sum)
        
        # 綜合多樣性評分
        diversity = 1.0 - 0.6 * overlap_penalty - 0.4 * sum_similarity
        total_diversity += max(0.0, diversity)
    end
    
    return total_diversity / length(selected)
end

"""
限制組合數量
"""
function limit_combinations(
    combinations::Vector{Vector{Int}},
    config::WonderGridConfig
)::Vector{Combination}
    
    limited_combinations = combinations[1:min(config.combination_limit, length(combinations))]
    
    # 轉換為 Combination 結構
    result_combinations = Combination[]
    
    for (index, combo) in enumerate(limited_combinations)
        quality_score = calculate_combination_quality(combo, config, GAME_CONFIGURATIONS[config.game_type])
        metadata = Dict{String, Any}(
            "rank" => index,
            "quality_score" => quality_score,
            "generation_strategy" => "wonder_grid_advanced",
            "game_type" => config.game_type
        )
        combination = Combination(combo, quality_score, :WonderGrid, metadata)
        
        push!(result_combinations, combination)
    end
    
    return result_combinations
end

"""
生成 Wonder Grid 組合報告
"""
function generate_wonder_grid_report(
    combinations::Vector{Combination},
    key_number::Int,
    top_pairs::Vector{TopPair},
    config::WonderGridConfig
)::String
    
    report = IOBuffer()
    
    println(report, "=" ^ 70)
    println(report, "Wonder Grid 組合生成報告")
    println(report, "=" ^ 70)
    println(report)
    
    println(report, "生成配置:")
    println(report, "  關鍵號碼: $key_number")
    println(report, "  頂級配對數量: $(length(top_pairs))")
    println(report, "  組合限制: $(config.combination_limit)")
    println(report, "  遊戲類型: $(config.game_type)")
    println(report)
    
    println(report, "生成結果:")
    println(report, "  總組合數量: $(length(combinations))")
    println(report)
    
    # 顯示前10個組合
    println(report, "頂級組合 (前10個):")
    println(report, "排名  組合                    質量評分  生成策略")
    println(report, "-" ^ 70)
    
    for (i, combo) in enumerate(combinations[1:min(10, length(combinations))])
        numbers_str = join(combo.numbers, ", ")
        quality_score = get(combo.metadata, "quality_score", 0.0)
        strategy = get(combo.metadata, "generation_strategy", "unknown")
        
        println(report, @sprintf("%2d   %-20s   %8.3f   %s",
            i, numbers_str, quality_score, strategy))
    end
    
    if length(combinations) > 10
        println(report, "... 還有 $(length(combinations) - 10) 個組合")
    end
    
    println(report)
    
    # 統計分析
    quality_scores = [get(combo.metadata, "quality_score", 0.0) for combo in combinations]
    
    println(report, "統計摘要:")
    println(report, "  平均質量評分: $(round(mean(quality_scores), digits=3))")
    println(report, "  最高質量評分: $(round(maximum(quality_scores), digits=3))")
    println(report, "  最低質量評分: $(round(minimum(quality_scores), digits=3))")
    println(report, "  質量評分標準差: $(round(std(quality_scores), digits=3))")
    
    # 組合特性分析
    println(report)
    println(report, "組合特性分析:")
    
    # 奇偶分佈
    odd_even_distribution = analyze_odd_even_distribution(combinations)
    println(report, "  奇偶分佈: $odd_even_distribution")
    
    # 和值分佈
    sum_distribution = analyze_sum_distribution(combinations)
    println(report, "  和值範圍: $(minimum(sum_distribution)) - $(maximum(sum_distribution))")
    println(report, "  平均和值: $(round(mean(sum_distribution), digits=1))")
    
    println(report, "=" ^ 70)
    
    return String(take!(report))
end

"""
分析奇偶分佈
"""
function analyze_odd_even_distribution(combinations::Vector{Combination})::Dict{String, Int}
    distribution = Dict{String, Int}()
    
    for combo in combinations
        odd_count = count(num -> num % 2 == 1, combo.numbers)
        even_count = length(combo.numbers) - odd_count
        key = "$(odd_count)奇$(even_count)偶"
        
        distribution[key] = get(distribution, key, 0) + 1
    end
    
    return distribution
end

"""
分析和值分佈
"""
function analyze_sum_distribution(combinations::Vector{Combination})::Vector{Int}
    return [sum(combo.numbers) for combo in combinations]
end

"""
評估配對在測試期間的表現
"""
function evaluate_pairs_performance(
    pairs::Vector{ScoredPair},
    test_data::Vector{Drawing}
)::Float64
    
    if isempty(pairs) || isempty(test_data)
        return 0.0
    end
    
    total_hits = 0
    total_possible = 0
    
    for drawing in test_data
        winning_numbers = Set(drawing.numbers)
        
        for scored_pair in pairs
            num1, num2 = scored_pair.pair
            
            # 檢查配對是否在中獎號碼中
            if num1 in winning_numbers && num2 in winning_numbers
                total_hits += 2  # 兩個號碼都中
            elseif num1 in winning_numbers || num2 in winning_numbers
                total_hits += 1  # 一個號碼中
            end
            
            total_possible += 2
        end
    end
    
    return total_possible > 0 ? total_hits / total_possible : 0.0
end

"""
生成配對篩選報告
"""
function generate_pair_selection_report(
    top_pairs::Vector{TopPair},
    config::WonderGridConfig,
    key_number::Int
)::String
    
    report = IOBuffer()
    
    println(report, "=" ^ 60)
    println(report, "頂級配對篩選報告")
    println(report, "=" ^ 60)
    println(report)
    
    println(report, "篩選配置:")
    println(report, "  關鍵號碼: $key_number")
    println(report, "  配對百分比: $(config.top_pair_percentage * 100)%")
    println(report, "  遊戲類型: $(config.game_type)")
    println(report, "  分析範圍: $(config.analysis_range)")
    println(report)
    
    println(report, "篩選結果:")
    println(report, "  選出配對數量: $(length(top_pairs))")
    println(report)
    
    println(report, "頂級配對詳情:")
    println(report, "排名  配對      頻率  近期  跳躍  質量評分  選擇原因")
    println(report, "-" ^ 60)
    
    for (i, pair) in enumerate(top_pairs[1:min(10, length(top_pairs))])
        num1, num2 = pair.pair
        println(report, @sprintf("%2d   (%2d,%2d)   %3d   %3d   %4.1f   %6.3f   %s",
            i, num1, num2, pair.frequency, pair.recent_frequency,
            pair.average_skip, pair.quality_score, pair.selection_reason))
    end
    
    if length(top_pairs) > 10
        println(report, "... 還有 $(length(top_pairs) - 10) 個配對")
    end
    
    println(report)
    
    # 統計分析
    frequencies = [pair.frequency for pair in top_pairs]
    quality_scores = [pair.quality_score for pair in top_pairs]
    
    println(report, "統計摘要:")
    println(report, "  平均頻率: $(round(mean(frequencies), digits=1))")
    println(report, "  平均質量評分: $(round(mean(quality_scores), digits=3))")
    println(report, "  最高質量評分: $(round(maximum(quality_scores), digits=3))")
    println(report, "  最低質量評分: $(round(minimum(quality_scores), digits=3))")
    
    println(report, "=" ^ 60)
    
    return String(take!(report))
end

"""
計算 FFG 信心度
"""
function calculate_ffg_confidence(
    best_score::Float64,
    all_scores::Dict{Int, Float64},
    config::KeyNumberConfig
)::Float64
    
    if isempty(all_scores)
        return 0.0
    end
    
    scores = collect(values(all_scores))
    mean_score = mean(scores)
    std_score = std(scores)
    
    # 信心度基於最佳評分相對於平均值的標準差倍數
    if std_score > 0
        z_score = (best_score - mean_score) / std_score
        confidence = min(1.0, max(0.0, z_score / 3.0))  # 標準化到 0-1
    else
        confidence = best_score > config.ffg_threshold ? 0.8 : 0.3
    end
    
    return confidence
end

"""
計算跳躍信心度
"""
function calculate_skip_confidence(
    best_score::Float64,
    all_scores::Dict{Int, Float64},
    config::KeyNumberConfig
)::Float64
    
    if isempty(all_scores)
        return 0.0
    end
    
    scores = collect(values(all_scores))
    max_score = maximum(scores)
    min_score = minimum(scores)
    
    # 信心度基於評分在範圍中的相對位置
    if max_score > min_score
        relative_position = (best_score - min_score) / (max_score - min_score)
        confidence = 0.3 + 0.7 * relative_position  # 基礎信心度 0.3，最高 1.0
    else
        confidence = 0.5
    end
    
    return confidence
end

"""
計算頻率信心度
"""
function calculate_frequency_confidence(
    best_score::Float64,
    all_scores::Dict{Int, Float64},
    config::KeyNumberConfig
)::Float64
    
    if isempty(all_scores)
        return 0.0
    end
    
    scores = collect(values(all_scores))
    sorted_scores = sort(scores, rev=true)
    
    # 信心度基於最佳評分在排序中的位置
    top_10_percent = max(1, Int(round(length(sorted_scores) * 0.1)))
    
    if best_score in sorted_scores[1:top_10_percent]
        confidence = 0.8 + 0.2 * (1.0 - findfirst(x -> x == best_score, sorted_scores) / top_10_percent)
    else
        confidence = 0.4 + 0.4 * best_score
    end
    
    return min(1.0, confidence)
end

"""
計算混合信心度
"""
function calculate_hybrid_confidence(
    best_score::Float64,
    all_scores::Dict{Int, Float64},
    individual_results::Vector{KeyNumberResult},
    config::KeyNumberConfig
)::Float64
    
    if isempty(all_scores) || isempty(individual_results)
        return 0.0
    end
    
    # 基於個別策略的信心度
    individual_confidences = [result.confidence for result in individual_results]
    avg_individual_confidence = mean(individual_confidences)
    
    # 基於評分分佈的信心度
    scores = collect(values(all_scores))
    score_std = std(scores)
    score_mean = mean(scores)
    
    distribution_confidence = if score_std > 0
        z_score = (best_score - score_mean) / score_std
        min(1.0, max(0.0, 0.5 + z_score / 6.0))
    else
        0.5
    end
    
    # 策略一致性信心度
    selected_numbers = [result.number for result in individual_results]
    consistency_confidence = if length(unique(selected_numbers)) == 1
        0.9  # 所有策略選擇相同號碼
    elseif length(unique(selected_numbers)) == 2
        0.7  # 部分策略一致
    else
        0.5  # 策略分歧
    end
    
    # 綜合信心度
    hybrid_confidence = (
        0.4 * avg_individual_confidence +
        0.3 * distribution_confidence +
        0.3 * consistency_confidence
    )
    
    return min(1.0, hybrid_confidence)
end

"""
動態調整關鍵號碼選擇權重
"""
function adjust_selection_weights(
    historical_performance::Dict{Symbol, Float64},
    config::KeyNumberConfig
)::KeyNumberConfig
    
    if isempty(historical_performance)
        return config
    end
    
    println("動態調整選擇權重...")
    
    # 計算各策略的相對表現
    total_performance = sum(values(historical_performance))
    
    if total_performance <= 0
        return config  # 如果沒有正面表現，保持原權重
    end
    
    # 計算新權重
    new_skip_weight = get(historical_performance, :skip, 0.0) / total_performance
    new_frequency_weight = get(historical_performance, :frequency, 0.0) / total_performance
    new_trend_weight = get(historical_performance, :trend, 0.0) / total_performance
    
    # 確保權重總和為 1.0
    total_new_weight = new_skip_weight + new_frequency_weight + new_trend_weight
    
    if total_new_weight > 0
        new_skip_weight /= total_new_weight
        new_frequency_weight /= total_new_weight
        new_trend_weight /= total_new_weight
    else
        # 如果計算失敗，使用均等權重
        new_skip_weight = new_frequency_weight = new_trend_weight = 1.0 / 3.0
    end
    
    # 應用平滑因子，避免權重變化過於劇烈
    smoothing_factor = 0.3
    adjusted_skip_weight = (1 - smoothing_factor) * config.skip_weight + smoothing_factor * new_skip_weight
    adjusted_frequency_weight = (1 - smoothing_factor) * config.frequency_weight + smoothing_factor * new_frequency_weight
    adjusted_trend_weight = (1 - smoothing_factor) * config.trend_weight + smoothing_factor * new_trend_weight
    
    # 重新標準化
    total_adjusted = adjusted_skip_weight + adjusted_frequency_weight + adjusted_trend_weight
    adjusted_skip_weight /= total_adjusted
    adjusted_frequency_weight /= total_adjusted
    adjusted_trend_weight /= total_adjusted
    
    println("權重調整: Skip $(round(adjusted_skip_weight, digits=3)), " *
           "Frequency $(round(adjusted_frequency_weight, digits=3)), " *
           "Trend $(round(adjusted_trend_weight, digits=3))")
    
    # 創建新配置
    return KeyNumberConfig(
        strategy = config.strategy,
        analysis_depth = config.analysis_depth,
        ffg_threshold = config.ffg_threshold,
        skip_weight = adjusted_skip_weight,
        frequency_weight = adjusted_frequency_weight,
        trend_weight = adjusted_trend_weight,
        enable_hybrid = config.enable_hybrid
    )
end

"""
評估關鍵號碼選擇策略的歷史表現
"""
function evaluate_key_number_strategies(
    drawings::Vector{Drawing},
    evaluation_periods::Int = 50
)::Dict{Symbol, Float64}
    
    if length(drawings) < evaluation_periods + 10
        throw(WonderGridError("數據不足以進行策略評估", :insufficient_data))
    end
    
    println("評估關鍵號碼選擇策略的歷史表現...")
    
    performance = Dict{Symbol, Float64}()
    strategies = [:ffg, :skip, :frequency]
    
    for strategy in strategies
        strategy_score = 0.0
        valid_evaluations = 0
        
        for i in 1:evaluation_periods
            # 使用前面的數據選擇關鍵號碼
            training_data = drawings[(i+10):end]
            
            if length(training_data) >= 20
                try
                    # 選擇關鍵號碼
                    key_result = select_key_number(training_data, strategy)
                    
                    # 檢查在接下來的開獎中是否出現
                    test_drawings = drawings[i:(i+9)]  # 檢查接下來10期
                    
                    hits = 0
                    for test_drawing in test_drawings
                        if key_result.number in test_drawing.numbers
                            hits += 1
                        end
                    end
                    
                    # 計算該策略在這個時間段的表現
                    hit_rate = hits / length(test_drawings)
                    strategy_score += hit_rate * key_result.confidence
                    valid_evaluations += 1
                    
                catch e
                    # 如果策略失敗，跳過這個評估
                    continue
                end
            end
        end
        
        # 計算平均表現
        if valid_evaluations > 0
            performance[strategy] = strategy_score / valid_evaluations
        else
            performance[strategy] = 0.0
        end
    end
    
    println("策略評估完成:")
    for (strategy, score) in performance
        println("  $strategy: $(round(score, digits=3))")
    end
    
    return performance
end

"""
進階 Wonder Grid 組合生成

# Arguments
- `key_number::Int`: 關鍵號碼
- `top_pairs::Vector{Int}`: 頂級配對號碼
- `config::WonderGridConfig`: Wonder Grid 配置
- `combo_config::CombinationConfig`: 組合生成配置

# Returns
- `CombinationResult`: 生成結果
"""
function generate_wonder_combinations_advanced(
    key_number::Int, 
    top_pairs::Vector{Int}, 
    config::WonderGridConfig,
    combo_config::CombinationConfig = CombinationConfig()
)::CombinationResult
    
    start_time = time()
    
    if key_number <= 0
        throw(WonderGridError("關鍵號碼無效", :invalid_key_number))
    end
    if isempty(top_pairs)
        throw(WonderGridError("頂級配對不能為空", :empty_pairs))
    end
    
    println("開始生成 Wonder Grid 組合...")
    println("方法: $(combo_config.generation_method), 最大數量: $(combo_config.max_combinations)")
    
    game_config = GAME_CONFIGURATIONS[config.game_type]
    numbers_needed = game_config.numbers_per_combination
    max_number = game_config.max_number
    
    # 準備號碼池
    number_pool = prepare_number_pool(key_number, top_pairs, max_number, combo_config)
    
    # 根據生成方法選擇算法
    combinations = if combo_config.generation_method == :random
        generate_random_combinations(key_number, number_pool, numbers_needed, combo_config)
    elseif combo_config.generation_method == :systematic
        generate_systematic_combinations(key_number, number_pool, numbers_needed, combo_config)
    elseif combo_config.generation_method == :balanced
        generate_balanced_combinations(key_number, number_pool, numbers_needed, combo_config)
    elseif combo_config.generation_method == :weighted
        generate_weighted_combinations(key_number, top_pairs, number_pool, numbers_needed, combo_config)
    elseif combo_config.generation_method == :markov
        generate_markov_combinations(key_number, top_pairs, number_pool, numbers_needed, combo_config)
    else
        throw(WonderGridError("未實現的生成方法: $(combo_config.generation_method)", :unimplemented_method))
    end
    
    # 應用過濾器
    if combo_config.enable_filtering
        combinations = apply_combination_filters(combinations, combo_config, max_number)
    end
    
    # 確保不超過最大數量
    if length(combinations) > combo_config.max_combinations
        combinations = combinations[1:combo_config.max_combinations]
    end
    
    # 計算統計和質量評分
    statistics = calculate_combination_statistics(combinations, max_number)
    quality_score = calculate_quality_score(combinations, statistics, combo_config)
    
    generation_time = time() - start_time
    
    println("組合生成完成！")
    println("生成數量: $(length(combinations))")
    println("質量評分: $(round(quality_score, digits=3))")
    println("生成時間: $(round(generation_time, digits=2)) 秒")
    
    return CombinationResult(
        combinations, combo_config.generation_method, 
        statistics, quality_score, generation_time
    )
end

"""
準備號碼池
"""
function prepare_number_pool(
    key_number::Int, 
    top_pairs::Vector{Int}, 
    max_number::Int,
    config::CombinationConfig
)::Vector{Int}
    
    # 基礎號碼池：關鍵號碼 + 頂級配對
    core_numbers = unique([key_number; top_pairs])
    
    # 擴展號碼池
    extended_pool = collect(1:max_number)
    
    # 根據多樣性因子決定號碼池大小
    pool_size = Int(round(max_number * config.diversity_factor))
    pool_size = max(pool_size, length(core_numbers) + 5)  # 確保最小大小
    
    # 優先保留核心號碼，隨機添加其他號碼
    final_pool = copy(core_numbers)
    remaining_numbers = filter(x -> !(x in core_numbers), extended_pool)
    
    while length(final_pool) < pool_size && !isempty(remaining_numbers)
        next_number = rand(remaining_numbers)
        push!(final_pool, next_number)
        filter!(x -> x != next_number, remaining_numbers)
    end
    
    return sort(final_pool)
end

"""
隨機組合生成
"""
function generate_random_combinations(
    key_number::Int,
    number_pool::Vector{Int},
    numbers_needed::Int,
    config::CombinationConfig
)::Vector{Vector{Int}}
    
    combinations = Vector{Vector{Int}}()
    max_attempts = config.max_combinations * 3
    attempts = 0
    
    while length(combinations) < config.max_combinations && attempts < max_attempts
        attempts += 1
        
        combo = [key_number]
        available = filter(x -> x != key_number, number_pool)
        
        while length(combo) < numbers_needed && !isempty(available)
            next_num = rand(available)
            push!(combo, next_num)
            filter!(x -> x != next_num, available)
        end
        
        if length(combo) == numbers_needed
            sort!(combo)
            if !(combo in combinations)
                push!(combinations, combo)
            end
        end
    end
    
    return combinations
end

"""
系統化組合生成
"""
function generate_systematic_combinations(
    key_number::Int,
    number_pool::Vector{Int},
    numbers_needed::Int,
    config::CombinationConfig
)::Vector{Vector{Int}}
    
    combinations = Vector{Vector{Int}}()
    other_numbers = filter(x -> x != key_number, number_pool)
    
    if length(other_numbers) < numbers_needed - 1
        throw(WonderGridError("號碼池太小，無法生成系統化組合", :insufficient_numbers))
    end
    
    # 生成所有可能的組合（限制數量）
    for combo_indices in combinations_iterator(length(other_numbers), numbers_needed - 1)
        if length(combinations) >= config.max_combinations
            break
        end
        
        combo = [key_number]
        for i in combo_indices
            push!(combo, other_numbers[i])
        end
        
        sort!(combo)
        push!(combinations, combo)
    end
    
    return combinations
end

"""
平衡組合生成
"""
function generate_balanced_combinations(
    key_number::Int,
    number_pool::Vector{Int},
    numbers_needed::Int,
    config::CombinationConfig
)::Vector{Vector{Int}}
    
    combinations = Vector{Vector{Int}}()
    max_number = maximum(number_pool)
    mid_point = max_number ÷ 2
    
    # 分類號碼
    odd_numbers = filter(x -> x % 2 == 1, number_pool)
    even_numbers = filter(x -> x % 2 == 0, number_pool)
    low_numbers = filter(x -> x <= mid_point, number_pool)
    high_numbers = filter(x -> x > mid_point, number_pool)
    
    attempts = 0
    max_attempts = config.max_combinations * 5
    
    while length(combinations) < config.max_combinations && attempts < max_attempts
        attempts += 1
        
        combo = [key_number]
        remaining_slots = numbers_needed - 1
        available = filter(x -> x != key_number, number_pool)
        
        # 平衡奇偶數
        if config.balance_odd_even && remaining_slots >= 2
            target_odd = remaining_slots ÷ 2
            target_even = remaining_slots - target_odd
            
            # 調整關鍵號碼的奇偶性
            if key_number % 2 == 1
                target_odd -= 1
            else
                target_even -= 1
            end
            
            # 選擇奇數
            odd_available = filter(x -> x in available && x % 2 == 1, number_pool)
            for _ in 1:min(target_odd, length(odd_available))
                if !isempty(odd_available)
                    num = rand(odd_available)
                    push!(combo, num)
                    filter!(x -> x != num, available)
                    filter!(x -> x != num, odd_available)
                end
            end
            
            # 選擇偶數
            even_available = filter(x -> x in available && x % 2 == 0, number_pool)
            for _ in 1:min(target_even, length(even_available))
                if !isempty(even_available)
                    num = rand(even_available)
                    push!(combo, num)
                    filter!(x -> x != num, available)
                    filter!(x -> x != num, even_available)
                end
            end
        end
        
        # 填充剩餘位置
        while length(combo) < numbers_needed && !isempty(available)
            num = rand(available)
            push!(combo, num)
            filter!(x -> x != num, available)
        end
        
        if length(combo) == numbers_needed
            sort!(combo)
            if !(combo in combinations)
                push!(combinations, combo)
            end
        end
    end
    
    return combinations
end

"""
加權組合生成
"""
function generate_weighted_combinations(
    key_number::Int,
    top_pairs::Vector{Int},
    number_pool::Vector{Int},
    numbers_needed::Int,
    config::CombinationConfig
)::Vector{Vector{Int}}
    
    combinations = Vector{Vector{Int}}()
    
    # 計算權重
    weights = Dict{Int, Float64}()
    for num in number_pool
        if num == key_number
            weights[num] = 1.0  # 關鍵號碼權重最高
        elseif num in top_pairs
            weights[num] = 0.8  # 頂級配對權重較高
        else
            weights[num] = 0.3  # 其他號碼權重較低
        end
    end
    
    attempts = 0
    max_attempts = config.max_combinations * 3
    
    while length(combinations) < config.max_combinations && attempts < max_attempts
        attempts += 1
        
        combo = [key_number]
        available = filter(x -> x != key_number, number_pool)
        available_weights = [weights[x] for x in available]
        
        while length(combo) < numbers_needed && !isempty(available)
            # 加權隨機選擇
            if sum(available_weights) > 0
                probabilities = available_weights ./ sum(available_weights)
                selected_index = sample_weighted(probabilities)
                selected_num = available[selected_index]
                
                push!(combo, selected_num)
                
                # 更新可用號碼和權重
                filter_indices = findall(x -> x != selected_num, available)
                available = available[filter_indices]
                available_weights = available_weights[filter_indices]
            else
                break
            end
        end
        
        if length(combo) == numbers_needed
            sort!(combo)
            if !(combo in combinations)
                push!(combinations, combo)
            end
        end
    end
    
    return combinations
end

"""
馬可夫鏈組合生成
"""
function generate_markov_combinations(
    key_number::Int,
    top_pairs::Vector{Int},
    number_pool::Vector{Int},
    numbers_needed::Int,
    config::CombinationConfig
)::Vector{Vector{Int}}
    
    combinations = Vector{Vector{Int}}()
    
    # 建立簡化的轉移矩陣（基於配對關係）
    transition_probs = build_transition_matrix(top_pairs, number_pool)
    
    attempts = 0
    max_attempts = config.max_combinations * 3
    
    while length(combinations) < config.max_combinations && attempts < max_attempts
        attempts += 1
        
        combo = [key_number]
        current_number = key_number
        
        while length(combo) < numbers_needed
            # 根據轉移概率選擇下一個號碼
            next_candidates = filter(x -> !(x in combo), number_pool)
            
            if isempty(next_candidates)
                break
            end
            
            # 計算轉移概率
            probs = Float64[]
            for candidate in next_candidates
                prob = get(transition_probs, (current_number, candidate), 0.1)
                push!(probs, prob)
            end
            
            if sum(probs) > 0
                probs = probs ./ sum(probs)
                selected_index = sample_weighted(probs)
                next_number = next_candidates[selected_index]
                
                push!(combo, next_number)
                current_number = next_number
            else
                # 隨機選擇
                next_number = rand(next_candidates)
                push!(combo, next_number)
                current_number = next_number
            end
        end
        
        if length(combo) == numbers_needed
            sort!(combo)
            if !(combo in combinations)
                push!(combinations, combo)
            end
        end
    end
    
    return combinations
end

# 輔助函數

function combinations_iterator(n::Int, k::Int)
    # 簡化的組合迭代器
    if k > n || k < 0
        return []
    end
    
    result = Vector{Vector{Int}}()
    
    function generate_combinations(start::Int, current::Vector{Int})
        if length(current) == k
            push!(result, copy(current))
            return
        end
        
        for i in start:(n - k + length(current) + 1)
            push!(current, i)
            generate_combinations(i + 1, current)
            pop!(current)
        end
    end
    
    generate_combinations(1, Int[])
    return result
end

function sample_weighted(probabilities::Vector{Float64})::Int
    cumsum_probs = cumsum(probabilities)
    r = rand()
    
    for (i, cum_prob) in enumerate(cumsum_probs)
        if r <= cum_prob
            return i
        end
    end
    
    return length(probabilities)  # 備用
end

"""
計算效率指標

# Arguments
- `combinations::Vector{Vector{Int}}`: 生成的組合
- `drawings::Vector{Drawing}`: 歷史開獎數據
- `config::WonderGridConfig`: 配置
- `pair_statistics::Dict`: 配對統計（可選）

# Returns
- `EfficiencyMetrics`: 效率指標
"""
function calculate_efficiency_metrics(
    combinations::Vector{Vector{Int}},
    drawings::Vector{Drawing},
    config::WonderGridConfig,
    pair_statistics::Dict{Tuple{Int,Int}, PairStatistics} = Dict{Tuple{Int,Int}, PairStatistics}()
)::EfficiencyMetrics
    
    if isempty(combinations)
        throw(WonderGridError("組合不能為空", :empty_combinations))
    end
    if isempty(drawings)
        throw(WonderGridError("開獎數據不能為空", :empty_drawings))
    end
    
    println("開始計算效率指標...")
    
    # 執行回測分析
    backtest_results = perform_backtest_analysis(combinations, drawings, config)
    
    # 計算基本指標
    hit_rate = calculate_hit_rate(backtest_results)
    coverage_rate = calculate_coverage_rate(combinations, drawings, config)
    cost_efficiency = calculate_cost_efficiency(backtest_results, length(combinations))
    
    # 計算財務指標
    roi = calculate_roi(backtest_results, length(combinations))
    sharpe_ratio = calculate_sharpe_ratio(backtest_results)
    max_drawdown = calculate_max_drawdown(backtest_results)
    win_loss_ratio = calculate_win_loss_ratio(backtest_results)
    
    # 計算風險指標
    consistency_score = calculate_consistency_score(backtest_results)
    risk_adjusted_return = calculate_risk_adjusted_return(backtest_results)
    
    # 計算統計指標
    expected_value = calculate_expected_value(backtest_results, length(combinations))
    variance = calculate_variance(backtest_results)
    confidence_interval = calculate_confidence_interval(backtest_results)
    
    println("效率指標計算完成")
    println("中獎率: $(round(hit_rate * 100, digits=2))%")
    println("覆蓋率: $(round(coverage_rate * 100, digits=2))%")
    println("投資回報率: $(round(roi * 100, digits=2))%")
    
    return EfficiencyMetrics(
        hit_rate, coverage_rate, cost_efficiency, roi, sharpe_ratio,
        max_drawdown, win_loss_ratio, consistency_score, risk_adjusted_return,
        expected_value, variance, confidence_interval, length(backtest_results), length(combinations)
    )
end

"""
執行回測分析
"""
function perform_backtest_analysis(
    combinations::Vector{Vector{Int}},
    drawings::Vector{Drawing},
    config::WonderGridConfig
)::Vector{BacktestResult}
    
    println("執行回測分析，測試 $(length(drawings)) 期開獎...")
    
    backtest_results = BacktestResult[]
    game_config = GAME_CONFIGURATIONS[config.game_type]
    
    for (period_index, drawing) in enumerate(drawings)
        winning_numbers = Set(drawing.numbers)
        
        # 檢查每個組合的中獎情況
        matched_combinations = Vector{Int}[]
        match_counts = Int[]
        prizes = Float64[]
        
        for combo in combinations
            combo_set = Set(combo)
            matches = length(intersect(winning_numbers, combo_set))
            
            if matches >= 3  # 至少中3個號碼才算中獎
                push!(matched_combinations, combo)
                push!(match_counts, matches)
                
                # 計算獎金（簡化的獎金結構）
                prize = calculate_prize_amount(matches, game_config)
                push!(prizes, prize)
            end
        end
        
        total_prize = sum(prizes)
        investment_cost = length(combinations) * 2.0  # 假設每注2元
        net_profit = total_prize - investment_cost
        period_hit_rate = length(matched_combinations) / length(combinations)
        
        result = BacktestResult(
            period_index, drawing, matched_combinations, match_counts,
            prizes, total_prize, net_profit, period_hit_rate, length(combinations)
        )
        
        push!(backtest_results, result)
    end
    
    println("回測分析完成")
    return backtest_results
end

"""
計算獎金金額
"""
function calculate_prize_amount(matches::Int, game_config::GameConfiguration)::Float64
    # 簡化的獎金結構（實際應根據具體彩票規則）
    if matches == 6
        return 5000000.0  # 頭獎
    elseif matches == 5
        return 10000.0    # 二獎
    elseif matches == 4
        return 300.0      # 三獎
    elseif matches == 3
        return 10.0       # 四獎
    else
        return 0.0
    end
end

"""
計算中獎率
"""
function calculate_hit_rate(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end
    
    total_hits = sum([length(result.matched_combinations) for result in backtest_results])
    total_attempts = sum([result.total_combinations for result in backtest_results])
    
    return total_attempts > 0 ? total_hits / total_attempts : 0.0
end

"""
計算覆蓋率
"""
function calculate_coverage_rate(
    combinations::Vector{Vector{Int}},
    drawings::Vector{Drawing},
    config::WonderGridConfig
)::Float64
    
    if isempty(combinations) || isempty(drawings)
        return 0.0
    end
    
    # 計算組合覆蓋的號碼範圍
    all_combo_numbers = Set{Int}()
    for combo in combinations
        union!(all_combo_numbers, Set(combo))
    end
    
    # 計算實際出現的號碼範圍
    all_drawn_numbers = Set{Int}()
    for drawing in drawings
        union!(all_drawn_numbers, Set(drawing.numbers))
    end
    
    # 計算覆蓋率
    if isempty(all_drawn_numbers)
        return 0.0
    end
    
    covered_numbers = intersect(all_combo_numbers, all_drawn_numbers)
    return length(covered_numbers) / length(all_drawn_numbers)
end

"""
計算成本效率
"""
function calculate_cost_efficiency(
    backtest_results::Vector{BacktestResult},
    total_combinations::Int
)::Float64
    
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end
    
    total_investment = length(backtest_results) * total_combinations * 2.0  # 每注2元
    total_return = sum([result.total_prize for result in backtest_results])
    
    return total_investment > 0 ? total_return / total_investment : 0.0
end

"""
計算投資回報率 (ROI)
"""
function calculate_roi(backtest_results::Vector{BacktestResult}, total_combinations::Int)::Float64
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end
    
    total_investment = length(backtest_results) * total_combinations * 2.0
    total_profit = sum([result.net_profit for result in backtest_results])
    
    return total_investment > 0 ? total_profit / total_investment : 0.0
end

"""
計算夏普比率
"""
function calculate_sharpe_ratio(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end
    
    returns = [result.net_profit for result in backtest_results]
    mean_return = mean(returns)
    std_return = std(returns)
    
    # 假設無風險利率為 0
    return std_return > 0 ? mean_return / std_return : 0.0
end

"""
計算最大回撤
"""
function calculate_max_drawdown(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end
    
    cumulative_profits = cumsum([result.net_profit for result in backtest_results])
    peak = cumulative_profits[1]
    max_drawdown = 0.0
    
    for profit in cumulative_profits
        if profit > peak
            peak = profit
        else
            drawdown = peak != 0 ? (peak - profit) / abs(peak) : 0.0
            max_drawdown = max(max_drawdown, drawdown)
        end
    end
    
    return max_drawdown
end

"""
計算盈虧比
"""
function calculate_win_loss_ratio(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end
    
    wins = filter(result -> result.net_profit > 0, backtest_results)
    losses = filter(result -> result.net_profit < 0, backtest_results)
    
    if isempty(wins) || isempty(losses)
        return 0.0
    end
    
    avg_win = mean([result.net_profit for result in wins])
    avg_loss = abs(mean([result.net_profit for result in losses]))
    
    return avg_loss > 0 ? avg_win / avg_loss : 0.0
end

"""
計算一致性評分
"""
function calculate_consistency_score(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 3
        return 0.0
    end
    
    hit_rates = [result.hit_rate for result in backtest_results]
    mean_hit_rate = mean(hit_rates)
    std_hit_rate = std(hit_rates)
    
    # 一致性評分：標準差越小，一致性越高
    coefficient_of_variation = mean_hit_rate > 0 ? std_hit_rate / mean_hit_rate : 1.0
    return max(0.0, 1.0 - coefficient_of_variation)
end

"""
計算風險調整回報
"""
function calculate_risk_adjusted_return(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end
    
    returns = [result.net_profit for result in backtest_results]
    mean_return = mean(returns)
    std_return = std(returns)
    
    # 風險調整回報 = 平均回報 / 風險（標準差）
    return std_return > 0 ? mean_return / std_return : 0.0
end

"""
計算期望值
"""
function calculate_expected_value(
    backtest_results::Vector{BacktestResult},
    total_combinations::Int
)::Float64
    
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end
    
    total_profit = sum([result.net_profit for result in backtest_results])
    total_periods = length(backtest_results)
    
    return total_profit / total_periods
end

"""
計算方差
"""
function calculate_variance(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end
    
    profits = [result.net_profit for result in backtest_results]
    return var(profits)
end

"""
計算置信區間
"""
function calculate_confidence_interval(
    backtest_results::Vector{BacktestResult},
    confidence_level::Float64 = 0.95
)::Tuple{Float64, Float64}
    
    if length(backtest_results) < 2
        return (0.0, 0.0)
    end
    
    profits = [result.net_profit for result in backtest_results]
    mean_profit = mean(profits)
    std_profit = std(profits)
    n = length(profits)
    
    # 使用 t 分佈計算置信區間
    alpha = 1 - confidence_level
    t_value = 2.0  # 簡化的 t 值（實際應查表）
    
    margin_of_error = t_value * std_profit / sqrt(n)
    
    lower_bound = mean_profit - margin_of_error
    upper_bound = mean_profit + margin_of_error
    
    return (lower_bound, upper_bound)
end

"""
生成效率報告
"""
function generate_efficiency_report(
    metrics::EfficiencyMetrics,
    combinations::Vector{Vector{Int}},
    config::WonderGridConfig
)::String
    
    report = IOBuffer()
    
    println(report, "=" ^ 60)
    println(report, "Wonder Grid 策略效率報告")
    println(report, "=" ^ 60)
    println(report)
    
    # 基本信息
    println(report, "基本信息:")
    println(report, "  遊戲類型: $(config.game_type)")
    println(report, "  組合數量: $(metrics.total_combinations)")
    println(report, "  回測期數: $(metrics.backtest_periods)")
    println(report, "  分析範圍: $(config.analysis_range)")
    println(report)
    
    # 核心指標
    println(report, "核心效率指標:")
    println(report, "  中獎率: $(round(metrics.hit_rate * 100, digits=2))%")
    println(report, "  覆蓋率: $(round(metrics.coverage_rate * 100, digits=2))%")
    println(report, "  成本效率: $(round(metrics.cost_efficiency, digits=3))")
    println(report, "  投資回報率: $(round(metrics.roi * 100, digits=2))%")
    println(report)
    
    # 風險指標
    println(report, "風險評估指標:")
    println(report, "  夏普比率: $(round(metrics.sharpe_ratio, digits=3))")
    println(report, "  最大回撤: $(round(metrics.max_drawdown * 100, digits=2))%")
    println(report, "  盈虧比: $(round(metrics.win_loss_ratio, digits=3))")
    println(report, "  一致性評分: $(round(metrics.consistency_score, digits=3))")
    println(report, "  風險調整回報: $(round(metrics.risk_adjusted_return, digits=3))")
    println(report)
    
    # 統計指標
    println(report, "統計分析:")
    println(report, "  期望值: $(round(metrics.expected_value, digits=2))")
    println(report, "  方差: $(round(metrics.variance, digits=2))")
    ci_lower, ci_upper = metrics.confidence_interval
    println(report, "  95% 置信區間: [$(round(ci_lower, digits=2)), $(round(ci_upper, digits=2))]")
    println(report)
    
    # 策略評級
    overall_rating = calculate_overall_rating(metrics)
    println(report, "策略評級:")
    println(report, "  綜合評分: $(round(overall_rating, digits=3))/1.0")
    println(report, "  評級等級: $(get_rating_grade(overall_rating))")
    println(report)
    
    # 建議
    println(report, "優化建議:")
    suggestions = generate_optimization_suggestions(metrics)
    for suggestion in suggestions
        println(report, "  • $suggestion")
    end
    
    println(report, "=" ^ 60)
    
    return String(take!(report))
end

"""
計算綜合評級
"""
function calculate_overall_rating(metrics::EfficiencyMetrics)::Float64
    # 加權計算綜合評分
    weights = Dict(
        "hit_rate" => 0.25,
        "coverage_rate" => 0.15,
        "cost_efficiency" => 0.20,
        "consistency_score" => 0.15,
        "risk_adjusted_return" => 0.15,
        "roi" => 0.10
    )
    
    # 標準化指標到 0-1 範圍
    normalized_hit_rate = metrics.hit_rate
    normalized_coverage_rate = metrics.coverage_rate
    normalized_cost_efficiency = min(1.0, max(0.0, metrics.cost_efficiency))
    normalized_consistency = metrics.consistency_score
    normalized_risk_return = min(1.0, max(0.0, (metrics.risk_adjusted_return + 1.0) / 2.0))
    normalized_roi = min(1.0, max(0.0, (metrics.roi + 1.0) / 2.0))
    
    overall_score = (
        weights["hit_rate"] * normalized_hit_rate +
        weights["coverage_rate"] * normalized_coverage_rate +
        weights["cost_efficiency"] * normalized_cost_efficiency +
        weights["consistency_score"] * normalized_consistency +
        weights["risk_adjusted_return"] * normalized_risk_return +
        weights["roi"] * normalized_roi
    )
    
    return overall_score
end

"""
獲取評級等級
"""
function get_rating_grade(score::Float64)::String
    if score >= 0.9
        return "優秀 (A+)"
    elseif score >= 0.8
        return "良好 (A)"
    elseif score >= 0.7
        return "中等 (B)"
    elseif score >= 0.6
        return "及格 (C)"
    else
        return "需改進 (D)"
    end
end

"""
生成優化建議
"""
function generate_optimization_suggestions(metrics::EfficiencyMetrics)::Vector{String}
    suggestions = String[]
    
    if metrics.hit_rate < 0.1
        push!(suggestions, "中獎率偏低，建議增加組合數量或調整號碼選擇策略")
    end
    
    if metrics.coverage_rate < 0.7
        push!(suggestions, "覆蓋率不足，建議擴大號碼池範圍")
    end
    
    if metrics.cost_efficiency < 0.5
        push!(suggestions, "成本效率偏低，建議減少組合數量或提高中獎精度")
    end
    
    if metrics.consistency_score < 0.6
        push!(suggestions, "一致性不佳，建議優化策略參數以提高穩定性")
    end
    
    if metrics.max_drawdown > 0.3
        push!(suggestions, "最大回撤過大，建議加強風險控制措施")
    end
    
    if metrics.roi < 0
        push!(suggestions, "投資回報為負，建議重新評估策略可行性")
    end
    
    if isempty(suggestions)
        push!(suggestions, "策略表現良好，可考慮進一步優化參數以提升效果")
    end
    
    return suggestions
end
function build_transition_matrix(top_pairs::Vector{Int}, number_pool::Vector{Int})::Dict{Tuple{Int,Int}, Float64}
    matrix = Dict{Tuple{Int,Int}, Float64}()
    
    # 基於頂級配對建立轉移概率
    for i in top_pairs
        for j in top_pairs
            if i != j
                matrix[(i, j)] = 0.8  # 高概率
            end
        end
    end
    
    # 其他號碼的轉移概率
    for i in number_pool
        for j in number_pool
            if i != j && !haskey(matrix, (i, j))
                matrix[(i, j)] = 0.2  # 低概率
            end
        end
    end
    
    return matrix
end

# 更新原有的 generate_wonder_combinations 函數
function generate_wonder_combinations(
    key_number::Int, 
    top_pairs::Vector{Int}, 
    config::WonderGridConfig
)::Vector{Vector{Int}}
    
    # 使用進階生成系統
    combo_config = CombinationConfig(max_combinations=config.combination_limit)
    result = generate_wonder_combinations_advanced(key_number, top_pairs, config, combo_config)
    
    return result.combinations
end



"""
應用組合過濾器
"""
function apply_combination_filters(
    combinations::Vector{Vector{Int}},
    config::CombinationConfig,
    max_number::Int
)::Vector{Vector{Int}}
    
    println("開始應用組合過濾器...")
    original_count = length(combinations)
    
    filtered_combinations = combinations
    
    # 1. 和值過濾
    if config.min_sum > 0
        filtered_combinations = filter(combo -> sum(combo) >= config.min_sum, filtered_combinations)
    end
    if config.max_sum > 0
        filtered_combinations = filter(combo -> sum(combo) <= config.max_sum, filtered_combinations)
    end
    
    # 2. 避免連續數字
    if config.avoid_consecutive
        filtered_combinations = filter!(!has_consecutive_numbers, filtered_combinations)
    end

    # 3. 平衡奇偶數
    if config.balance_odd_even
        filtered_combinations = filter!(!has_imbalanced_odd_even, filtered_combinations)
    end

    # 4. 平衡高低數
    if config.balance_high_low
        filtered_combinations = filter!(!has_imbalanced_high_low, filtered_combinations)
    end
    
    println("過濾後的組合數量: $(length(filtered_combinations))")
    
    return filtered_combinations
end







"""
計算中獎率
"""
function calculate_hit_rate(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end
    
    total_hits = sum([length(result.matched_combinations) for result in backtest_results])
    total_attempts = sum([result.total_combinations for result in backtest_results])
    
    return total_attempts > 0 ? total_hits / total_attempts : 0.0
end

"""
計算覆蓋率
"""
function calculate_coverage_rate(
    combinations::Vector{Vector{Int}},
    drawings::Vector{Drawing},
    config::WonderGridConfig
)::Float64
    
    if isempty(combinations) || isempty(drawings)
        return 0.0
    end
    
    # 計算組合覆蓋的號碼範圍
    all_combo_numbers = Set{Int}()
    for combo in combinations
        union!(all_combo_numbers, Set(combo))
    end
    
    # 計算實際出現的號碼範圍
    all_drawn_numbers = Set{Int}()
    for drawing in drawings
        union!(all_drawn_numbers, Set(drawing.numbers))
    end
    
    # 計算覆蓋率
    if isempty(all_drawn_numbers)
        return 0.0
    end
    
    covered_numbers = intersect(all_combo_numbers, all_drawn_numbers)
    return length(covered_numbers) / length(all_drawn_numbers)
end

"""
計算成本效率
"""
function calculate_cost_efficiency(
    backtest_results::Vector{BacktestResult},
    total_combinations::Int
)::Float64
    
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end
    
    total_investment = length(backtest_results) * total_combinations * 2.0  # 每注2元
    total_return = sum([result.total_prize for result in backtest_results])
    
    return total_investment > 0 ? total_return / total_investment : 0.0
end

"""
計算投資回報率 (ROI)
"""
function calculate_roi(backtest_results::Vector{BacktestResult}, total_combinations::Int)::Float64
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end
    
    total_investment = length(backtest_results) * total_combinations * 2.0
    total_profit = sum([result.net_profit for result in backtest_results])
    
    return total_investment > 0 ? total_profit / total_investment : 0.0
end

"""
計算夏普比率
"""
function calculate_sharpe_ratio(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end
    
    returns = [result.net_profit for result in backtest_results]
    mean_return = mean(returns)
    std_return = std(returns)
    
    # 假設無風險利率為 0
    return std_return > 0 ? mean_return / std_return : 0.0
end

"""
計算最大回撤
"""
function calculate_max_drawdown(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end
    
    cumulative_profits = cumsum([result.net_profit for result in backtest_results])
    peak = cumulative_profits[1]
    max_drawdown = 0.0
    
    for profit in cumulative_profits
        if profit > peak
            peak = profit
        else
            drawdown = peak != 0 ? (peak - profit) / abs(peak) : 0.0
            max_drawdown = max(max_drawdown, drawdown)
        end
    end
    
    return max_drawdown
end

"""
計算盈虧比
"""
function calculate_win_loss_ratio(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end
    
    wins = filter(result -> result.net_profit > 0, backtest_results)
    losses = filter(result -> result.net_profit < 0, backtest_results)
    
    if isempty(wins) || isempty(losses)
        return 0.0
    end
    
    avg_win = mean([result.net_profit for result in wins])
    avg_loss = abs(mean([result.net_profit for result in losses]))
    
    return avg_loss > 0 ? avg_win / avg_loss : 0.0
end

"""
計算一致性評分
"""
function calculate_consistency_score(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 3
        return 0.0
    end
    
    hit_rates = [result.hit_rate for result in backtest_results]
    mean_hit_rate = mean(hit_rates)
    std_hit_rate = std(hit_rates)
    
    # 一致性評分：標準差越小，一致性越高
    coefficient_of_variation = mean_hit_rate > 0 ? std_hit_rate / mean_hit_rate : 1.0
    return max(0.0, 1.0 - coefficient_of_variation)
end

"""
計算風險調整回報
"""
function calculate_risk_adjusted_return(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end
    
    returns = [result.net_profit for result in backtest_results]
    mean_return = mean(returns)
    std_return = std(returns)
    
    # 風險調整回報 = 平均回報 / 風險（標準差）
    return std_return > 0 ? mean_return / std_return : 0.0
end

"""
計算期望值
"""
function calculate_expected_value(
    backtest_results::Vector{BacktestResult},
    total_combinations::Int
)::Float64
    
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end
    
    total_profit = sum([result.net_profit for result in backtest_results])
    total_periods = length(backtest_results)
    
    return total_profit / total_periods
end

"""
計算方差
"""
function calculate_variance(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end
    
    profits = [result.net_profit for result in backtest_results]
    return var(profits)
end

"""
計算置信區間
"""
function calculate_confidence_interval(
    backtest_results::Vector{BacktestResult},
    confidence_level::Float64 = 0.95
)::Tuple{Float64, Float64}
    
    if length(backtest_results) < 2
        return (0.0, 0.0)
    end
    
    profits = [result.net_profit for result in backtest_results]
    mean_profit = mean(profits)
    std_profit = std(profits)
    n = length(profits)
    
    # 使用 t 分佈計算置信區間
    alpha = 1 - confidence_level
    t_value = 2.0  # 簡化的 t 值（實際應查表）
    
    margin_of_error = t_value * std_profit / sqrt(n)
    
    lower_bound = mean_profit - margin_of_error
    upper_bound = mean_profit + margin_of_error
    
    return (lower_bound, upper_bound)
end

"""
生成效率報告
"""
function generate_efficiency_report(
    metrics::EfficiencyMetrics,
    combinations::Vector{Vector{Int}},
    config::WonderGridConfig
)::String
    
    report = IOBuffer()
    
    println(report, "=" ^ 60)
    println(report, "Wonder Grid 策略效率報告")
    println(report, "=" ^ 60)
    println(report)
    
    # 基本信息
    println(report, "基本信息:")
    println(report, "  遊戲類型: $(config.game_type)")
    println(report, "  組合數量: $(metrics.total_combinations)")
    println(report, "  回測期數: $(metrics.backtest_periods)")
    println(report, "  分析範圍: $(config.analysis_range)")
    println(report)
    
    # 核心指標
    println(report, "核心效率指標:")
    println(report, "  中獎率: $(round(metrics.hit_rate * 100, digits=2))%")
    println(report, "  覆蓋率: $(round(metrics.coverage_rate * 100, digits=2))%")
    println(report, "  成本效率: $(round(metrics.cost_efficiency, digits=3))")
    println(report, "  投資回報率: $(round(metrics.roi * 100, digits=2))%")
    println(report)
    
    # 風險指標
    println(report, "風險評估指標:")
    println(report, "  夏普比率: $(round(metrics.sharpe_ratio, digits=3))")
    println(report, "  最大回撤: $(round(metrics.max_drawdown * 100, digits=2))%")
    println(report, "  盈虧比: $(round(metrics.win_loss_ratio, digits=3))")
    println(report, "  一致性評分: $(round(metrics.consistency_score, digits=3))")
    println(report, "  風險調整回報: $(round(metrics.risk_adjusted_return, digits=3))")
    println(report)
    
    # 統計指標
    println(report, "統計分析:")
    println(report, "  期望值: $(round(metrics.expected_value, digits=2))")
    println(report, "  方差: $(round(metrics.variance, digits=2))")
    ci_lower, ci_upper = metrics.confidence_interval
    println(report, "  95% 置信區間: [$(round(ci_lower, digits=2)), $(round(ci_upper, digits=2))]")
    println(report)
    
    # 策略評級
    overall_rating = calculate_overall_rating(metrics)
    println(report, "策略評級:")
    println(report, "  綜合評分: $(round(overall_rating, digits=3))/1.0")
    println(report, "  評級等級: $(get_rating_grade(overall_rating))")
    println(report)
    
    # 建議
    println(report, "優化建議:")
    suggestions = generate_optimization_suggestions(metrics)
    for suggestion in suggestions
        println(report, "  • $suggestion")
    end
    
    println(report, "=" ^ 60)
    
    return String(take!(report))
end

"""
計算綜合評級
"""
function calculate_overall_rating(metrics::EfficiencyMetrics)::Float64
    # 加權計算綜合評分
    weights = Dict(
        "hit_rate" => 0.25,
        "coverage_rate" => 0.15,
        "cost_efficiency" => 0.20,
        "consistency_score" => 0.15,
        "risk_adjusted_return" => 0.15,
        "roi" => 0.10
    )
    
    # 標準化指標到 0-1 範圍
    normalized_hit_rate = metrics.hit_rate
    normalized_coverage_rate = metrics.coverage_rate
    normalized_cost_efficiency = min(1.0, max(0.0, metrics.cost_efficiency))
    normalized_consistency = metrics.consistency_score
    normalized_risk_return = min(1.0, max(0.0, (metrics.risk_adjusted_return + 1.0) / 2.0))
    normalized_roi = min(1.0, max(0.0, (metrics.roi + 1.0) / 2.0))
    
    overall_score = (
        weights["hit_rate"] * normalized_hit_rate +
        weights["coverage_rate"] * normalized_coverage_rate +
        weights["cost_efficiency"] * normalized_cost_efficiency +
        weights["consistency_score"] * normalized_consistency +
        weights["risk_adjusted_return"] * normalized_risk_return +
        weights["roi"] * normalized_roi
    )
    
    return overall_score
end

"""
獲取評級等級
"""
function get_rating_grade(score::Float64)::String
    if score >= 0.9
        return "優秀 (A+)"
    elseif score >= 0.8
        return "良好 (A)"
    elseif score >= 0.7
        return "中等 (B)"
    elseif score >= 0.6
        return "及格 (C)"
    else
        return "需改進 (D)"
    end
end

"""
生成優化建議
"""
function generate_optimization_suggestions(metrics::EfficiencyMetrics)::Vector{String}
    suggestions = String[]
    
    if metrics.hit_rate < 0.1
        push!(suggestions, "中獎率偏低，建議增加組合數量或調整號碼選擇策略")
    end
    
    if metrics.coverage_rate < 0.7
        push!(suggestions, "覆蓋率不足，建議擴大號碼池範圍")
    end
    
    if metrics.cost_efficiency < 0.5
        push!(suggestions, "成本效率偏低，建議減少組合數量或提高中獎精度")
    end
    
    if metrics.consistency_score < 0.6
        push!(suggestions, "一致性不佳，建議優化策略參數以提高穩定性")
    end
    
    if metrics.max_drawdown > 0.3
        push!(suggestions, "最大回撤過大，建議加強風險控制措施")
    end
    
    if metrics.roi < 0
        push!(suggestions, "投資回報為負，建議重新評估策略可行性")
    end
    
    if isempty(suggestions)
        push!(suggestions, "策略表現良好，可考慮進一步優化參數")
    end

    return suggestions
end

"""
和值過濾
"""
function filter_by_sum(
    combinations::Vector{Vector{Int}},
    min_sum::Int,
    max_sum::Int
)::Vector{Vector{Int}}
    
    return filter(combo -> begin
        combo_sum = sum(combo)
        min_sum <= combo_sum <= max_sum
    end, combinations)
end

"""
奇偶數平衡過濾
"""
function filter_by_odd_even_balance(
    combinations::Vector{Vector{Int}}
)::Vector{Vector{Int}}
    
    return filter(combo -> begin
        odd_count = count(x -> x % 2 == 1, combo)
        even_count = length(combo) - odd_count
        
        # 允許的奇偶比例：2:4, 3:3, 4:2
        abs(odd_count - even_count) <= 2
    end, combinations)
end

"""
高低數平衡過濾
"""
function filter_by_high_low_balance(
    combinations::Vector{Vector{Int}},
    max_number::Int
)::Vector{Vector{Int}}
    
    mid_point = max_number ÷ 2
    
    return filter(combo -> begin
        low_count = count(x -> x <= mid_point, combo)
        high_count = length(combo) - low_count
        
        # 允許的高低比例：2:4, 3:3, 4:2
        abs(low_count - high_count) <= 2
    end, combinations)
end

"""
連續數字過濾
"""
function filter_consecutive_numbers(
    combinations::Vector{Vector{Int}}
)::Vector{Vector{Int}}
    
    return filter(combo -> begin
        sorted_combo = sort(combo)
        consecutive_count = 0
        max_consecutive = 0
        
        for i in 2:length(sorted_combo)
            if sorted_combo[i] == sorted_combo[i-1] + 1
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count + 1)
            else
                consecutive_count = 0
            end
        end
        
        # 最多允許 3 個連續數字
        max_consecutive <= 3
    end, combinations)
end

"""
重複模式過濾
"""
function filter_duplicate_patterns(
    combinations::Vector{Vector{Int}}
)::Vector{Vector{Int}}
    
    unique_combinations = Vector{Vector{Int}}()
    seen_patterns = Set{Vector{Int}}()
    
    for combo in combinations
        sorted_combo = sort(combo)
        if !(sorted_combo in seen_patterns)
            push!(seen_patterns, sorted_combo)
            push!(unique_combinations, combo)
        end
    end
    
    return unique_combinations
end

"""
計算組合統計
"""
function calculate_combination_statistics(
    combinations::Vector{Vector{Int}},
    max_number::Int
)::Dict{String, Float64}
    
    if isempty(combinations)
        return Dict{String, Float64}()
    end
    
    statistics = Dict{String, Float64}()
    
    # 基本統計
    statistics["total_combinations"] = Float64(length(combinations))
    statistics["avg_combination_length"] = mean([length(combo) for combo in combinations])
    
    # 和值統計
    sums = [sum(combo) for combo in combinations]
    statistics["avg_sum"] = mean(sums)
    statistics["min_sum"] = Float64(minimum(sums))
    statistics["max_sum"] = Float64(maximum(sums))
    statistics["sum_std"] = std(sums)
    
    # 奇偶數統計
    odd_counts = [count(x -> x % 2 == 1, combo) for combo in combinations]
    statistics["avg_odd_count"] = mean(odd_counts)
    statistics["odd_balance_score"] = calculate_balance_score(odd_counts, length(combinations[1]))
    
    # 高低數統計
    mid_point = max_number ÷ 2
    low_counts = [count(x -> x <= mid_point, combo) for combo in combinations]
    statistics["avg_low_count"] = mean(low_counts)
    statistics["high_low_balance_score"] = calculate_balance_score(low_counts, length(combinations[1]))
    
    # 號碼分佈統計
    all_numbers = vcat(combinations...)
    number_frequencies = Dict{Int, Int}()
    for num in all_numbers
        number_frequencies[num] = get(number_frequencies, num, 0) + 1
    end
    
    if !isempty(number_frequencies)
        freq_values = collect(values(number_frequencies))
        statistics["number_distribution_std"] = std(freq_values)
        statistics["most_frequent_count"] = Float64(maximum(freq_values))
        statistics["least_frequent_count"] = Float64(minimum(freq_values))
    end
    
    # 多樣性統計
    statistics["diversity_score"] = calculate_diversity_score(combinations)
    
    return statistics
end

"""
計算平衡評分
"""
function calculate_balance_score(counts::Vector{Int}, total_length::Int)::Float64
    if isempty(counts)
        return 0.0
    end
    
    ideal_balance = total_length / 2.0
    deviations = [abs(count - ideal_balance) for count in counts]
    avg_deviation = mean(deviations)
    
    # 轉換為 0-1 評分（偏差越小評分越高）
    max_possible_deviation = total_length / 2.0
    return max(0.0, 1.0 - (avg_deviation / max_possible_deviation))
end

"""
計算多樣性評分
"""
function calculate_diversity_score(combinations::Vector{Vector{Int}})::Float64
    if length(combinations) <= 1
        return 0.0
    end
    
    total_similarity = 0.0
    comparison_count = 0
    
    for i in 1:length(combinations)
        for j in (i+1):length(combinations)
            similarity = calculate_combination_similarity(combinations[i], combinations[j])
            total_similarity += similarity
            comparison_count += 1
        end
    end
    
    avg_similarity = comparison_count > 0 ? total_similarity / comparison_count : 0.0
    return 1.0 - avg_similarity  # 相似度越低，多樣性越高
end

"""
計算組合相似度
"""
function calculate_combination_similarity(combo1::Vector{Int}, combo2::Vector{Int})::Float64
    set1 = Set(combo1)
    set2 = Set(combo2)
    
    intersection_size = length(intersect(set1, set2))
    union_size = length(union(set1, set2))
    
    return union_size > 0 ? intersection_size / union_size : 0.0
end

"""
計算質量評分
"""
function calculate_quality_score(
    combinations::Vector{Vector{Int}},
    statistics::Dict{String, Float64},
    config::CombinationConfig
)::Float64
    
    if isempty(combinations) || isempty(statistics)
        return 0.0
    end
    
    quality_components = Float64[]
    
    # 1. 平衡性評分 (30%)
    if haskey(statistics, "odd_balance_score") && haskey(statistics, "high_low_balance_score")
        balance_score = (statistics["odd_balance_score"] + statistics["high_low_balance_score"]) / 2.0
        push!(quality_components, balance_score * 0.3)
    end
    
    # 2. 多樣性評分 (25%)
    if haskey(statistics, "diversity_score")
        diversity_score = statistics["diversity_score"]
        push!(quality_components, diversity_score * 0.25)
    end
    
    # 3. 分佈均勻性評分 (20%)
    if haskey(statistics, "number_distribution_std")
        # 標準差越小，分佈越均勻
        max_std = statistics["total_combinations"] / 4.0  # 估計最大標準差
        distribution_score = max(0.0, 1.0 - (statistics["number_distribution_std"] / max_std))
        push!(quality_components, distribution_score * 0.2)
    end
    
    # 4. 和值分佈評分 (15%)
    if haskey(statistics, "sum_std")
        # 適中的標準差表示良好的和值分佈
        ideal_std = statistics["avg_sum"] * 0.1  # 理想標準差為平均值的10%
        sum_score = max(0.0, 1.0 - abs(statistics["sum_std"] - ideal_std) / ideal_std)
        push!(quality_components, sum_score * 0.15)
    end
    
    # 5. 數量達成率評分 (10%)
    target_combinations = Float64(config.max_combinations)
    actual_combinations = statistics["total_combinations"]
    quantity_score = min(1.0, actual_combinations / target_combinations)
    push!(quality_components, quantity_score * 0.1)
    
    return sum(quality_components)
end

"""
組合優化
"""
function optimize_combinations(
    combinations::Vector{Vector{Int}},
    config::WonderGridConfig,
    optimization_config::Dict{String, Any} = Dict{String, Any}()
)::Vector{Vector{Int}}
    
    if isempty(combinations)
        return combinations
    end
    
    println("開始組合優化...")
    
    # 獲取優化參數
    max_iterations = get(optimization_config, "max_iterations", 100)
    improvement_threshold = get(optimization_config, "improvement_threshold", 0.01)
    elite_percentage = get(optimization_config, "elite_percentage", 0.2)
    
    current_combinations = copy(combinations)
    game_config = GAME_CONFIGURATIONS[config.game_type]
    max_number = game_config.max_number
    
    # 計算初始質量評分
    initial_stats = calculate_combination_statistics(current_combinations, max_number)
    combo_config = CombinationConfig()  # 使用默認配置
    initial_quality = calculate_quality_score(current_combinations, initial_stats, combo_config)
    
    println("初始質量評分: $(round(initial_quality, digits=3))")
    
    best_combinations = copy(current_combinations)
    best_quality = initial_quality
    
    for iteration in 1:max_iterations
        # 選擇精英組合
        elite_count = max(1, Int(round(length(current_combinations) * elite_percentage)))
        elite_combinations = select_elite_combinations(current_combinations, elite_count, max_number)
        
        # 生成新的候選組合
        new_combinations = generate_optimized_combinations(
            elite_combinations, current_combinations, config, max_number
        )
        
        # 評估新組合
        new_stats = calculate_combination_statistics(new_combinations, max_number)
        new_quality = calculate_quality_score(new_combinations, new_stats, combo_config)
        
        # 檢查是否有改進
        if new_quality > best_quality + improvement_threshold
            best_combinations = copy(new_combinations)
            best_quality = new_quality
            current_combinations = new_combinations
            
            println("迭代 $iteration: 質量評分提升至 $(round(new_quality, digits=3))")
        else
            # 沒有顯著改進，提前停止
            if iteration > 10  # 至少運行10次迭代
                println("迭代 $iteration: 沒有顯著改進，停止優化")
                break
            end
        end
    end
    
    final_improvement = best_quality - initial_quality
    println("優化完成！質量評分提升: $(round(final_improvement, digits=3))")
    
    return best_combinations
end

"""
選擇精英組合
"""
function select_elite_combinations(
    combinations::Vector{Vector{Int}},
    elite_count::Int,
    max_number::Int
)::Vector{Vector{Int}}
    
    # 為每個組合計算個別質量評分
    combo_scores = Tuple{Vector{Int}, Float64}[]
    
    for combo in combinations
        # 計算單個組合的質量評分
        score = calculate_individual_combination_score(combo, max_number)
        push!(combo_scores, (combo, score))
    end
    
    # 按評分排序並選擇前elite_count個
    sort!(combo_scores, by = x -> x[2], rev = true)
    
    return [combo for (combo, score) in combo_scores[1:min(elite_count, length(combo_scores))]]
end

"""
計算單個組合的質量評分
"""
function calculate_individual_combination_score(combo::Vector{Int}, max_number::Int)::Float64
    score = 0.0
    
    # 1. 奇偶平衡 (25%)
    odd_count = count(x -> x % 2 == 1, combo)
    even_count = length(combo) - odd_count
    odd_even_balance = 1.0 - abs(odd_count - even_count) / length(combo)
    score += odd_even_balance * 0.25
    
    # 2. 高低平衡 (25%)
    mid_point = max_number ÷ 2
    low_count = count(x -> x <= mid_point, combo)
    high_count = length(combo) - low_count
    high_low_balance = 1.0 - abs(low_count - high_count) / length(combo)
    score += high_low_balance * 0.25
    
    # 3. 數字分佈 (25%)
    sorted_combo = sort(combo)
    gaps = [sorted_combo[i] - sorted_combo[i-1] for i in 2:length(sorted_combo)]
    avg_gap = mean(gaps)
    ideal_gap = max_number / length(combo)
    gap_score = max(0.0, 1.0 - abs(avg_gap - ideal_gap) / ideal_gap)
    score += gap_score * 0.25
    
    # 4. 和值合理性 (25%)
    combo_sum = sum(combo)
    expected_sum = (max_number + 1) * length(combo) / 2
    sum_score = max(0.0, 1.0 - abs(combo_sum - expected_sum) / expected_sum)
    score += sum_score * 0.25
    
    return score
end

"""
生成優化的組合
"""
function generate_optimized_combinations(
    elite_combinations::Vector{Vector{Int}},
    current_combinations::Vector{Vector{Int}},
    config::WonderGridConfig,
    max_number::Int
)::Vector{Vector{Int}}
    
    optimized_combinations = Vector{Vector{Int}}()
    
    # 保留精英組合
    append!(optimized_combinations, elite_combinations)
    
    # 通過交叉和變異生成新組合
    target_count = length(current_combinations)
    
    while length(optimized_combinations) < target_count
        if length(elite_combinations) >= 2
            # 交叉操作
            parent1 = rand(elite_combinations)
            parent2 = rand(elite_combinations)
            
            if parent1 != parent2
                child = crossover_combinations(parent1, parent2)
                if length(child) == length(parent1)
                    # 變異操作
                    mutated_child = mutate_combination(child, max_number, 0.1)
                    push!(optimized_combinations, mutated_child)
                end
            end
        end
        
        # 如果還需要更多組合，隨機生成
        if length(optimized_combinations) < target_count
            random_combo = generate_random_combination(max_number, length(elite_combinations[1]))
            push!(optimized_combinations, random_combo)
        end
    end
    
    return optimized_combinations[1:target_count]
end

"""
組合交叉操作
"""
function crossover_combinations(parent1::Vector{Int}, parent2::Vector{Int})::Vector{Int}
    if length(parent1) != length(parent2)
        return copy(parent1)
    end
    
    child = Int[]
    used_numbers = Set{Int}()
    
    # 隨機選擇交叉點
    crossover_point = rand(1:length(parent1)-1)
    
    # 從parent1取前半部分
    for i in 1:crossover_point
        if !(parent1[i] in used_numbers)
            push!(child, parent1[i])
            push!(used_numbers, parent1[i])
        end
    end
    
    # 從parent2取後半部分（避免重複）
    for num in parent2
        if !(num in used_numbers) && length(child) < length(parent1)
            push!(child, num)
            push!(used_numbers, num)
        end
    end
    
    # 如果還需要數字，從parent1補充
    for num in parent1
        if !(num in used_numbers) && length(child) < length(parent1)
            push!(child, num)
            push!(used_numbers, num)
        end
    end
    
    return sort(child)
end

"""
組合變異操作
"""
function mutate_combination(combo::Vector{Int}, max_number::Int, mutation_rate::Float64)::Vector{Int}
    mutated = copy(combo)

    for i in 1:length(mutated)
        if rand() < mutation_rate
            # 隨機替換一個數字
            new_number = rand(1:max_number)
            while new_number in mutated
                new_number = rand(1:max_number)
            end
            mutated[i] = new_number
        end
    end

    return sort(mutated)
end

"""
生成隨機組合
"""
function generate_random_combination(max_number::Int, combination_size::Int)::Vector{Int}
    if combination_size > max_number
        throw(WonderGridError("組合大小不能超過最大號碼數", :invalid_combination_size))
    end

    numbers = collect(1:max_number)
    selected = Int[]

    for _ in 1:combination_size
        if !isempty(numbers)
            index = rand(1:length(numbers))
            push!(selected, numbers[index])
            deleteat!(numbers, index)
        end
    end

    return sort(selected)
end

"""
計算所有號碼的跳躍值（重載版本）
"""
function calculate_skip_values(drawings::Vector{Drawing})::Dict{Int, Vector{Int}}
    if isempty(drawings)
        return Dict{Int, Vector{Int}}()
    end

    # 獲取遊戲配置
    game_type = drawings[1].game_type
    if !haskey(GAME_CONFIGURATIONS, game_type)
        throw(WonderGridError("不支援的遊戲類型: $game_type", :invalid_game_type))
    end

    game_config = GAME_CONFIGURATIONS[game_type]
    skip_values_dict = Dict{Int, Vector{Int}}()

    # 為每個號碼計算跳躍值
    for number in 1:game_config.max_number
        skip_values = calculate_skip_values(drawings, number)
        if !isempty(skip_values)
            skip_values_dict[number] = skip_values
        end
    end

    return skip_values_dict
end

"""
計算 FFG 值（簡化版本）
"""
function calculate_ffg(sample_size::Int)::Float64
    if sample_size <= 1
        return 0.0
    end

    # 簡化的 FFG 計算：基於樣本大小的對數函數
    return log(sample_size + 1) / log(10)
end

"""
將 EfficiencyMetrics 結構轉換為字典
"""
function convert_efficiency_metrics_to_dict(metrics::EfficiencyMetrics)::Dict{String, Float64}
    return Dict{String, Float64}(
        "hit_rate" => metrics.hit_rate,
        "coverage_rate" => metrics.coverage_rate,
        "cost_efficiency" => metrics.cost_efficiency,
        "roi" => metrics.roi,
        "sharpe_ratio" => metrics.sharpe_ratio,
        "max_drawdown" => metrics.max_drawdown,
        "win_loss_ratio" => metrics.win_loss_ratio,
        "consistency_score" => metrics.consistency_score,
        "risk_adjusted_return" => metrics.risk_adjusted_return,
        "expected_value" => metrics.expected_value,
        "variance" => metrics.variance,
        "confidence_interval_lower" => metrics.confidence_interval[1],
        "confidence_interval_upper" => metrics.confidence_interval[2],
        "backtest_periods" => Float64(metrics.backtest_periods),
        "total_combinations" => Float64(metrics.total_combinations)
    )
end

end # module WonderGridEngine





