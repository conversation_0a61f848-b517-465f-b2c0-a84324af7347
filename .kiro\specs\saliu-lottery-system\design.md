# Design Document

## Overview

The Saliu Lottery System is a comprehensive lottery analysis and strategy generation platform built on <PERSON>'s mathematical theories. The system implements advanced statistical analysis, filtering mechanisms, Markov chain analysis, skip systems, and Wonder Grid strategies to optimize lottery betting strategies across multiple game types.

The architecture follows a modular, layered approach with clear separation of concerns between data management, analysis engines, strategy optimization, and user interfaces. The system is designed to handle large datasets (up to 12 million records) and provide real-time analysis capabilities.

## Architecture

### System Architecture Pattern
The system follows a **layered architecture** with the following layers:

1. **Presentation Layer**: User interfaces and API endpoints
2. **Application Layer**: Business logic and orchestration services
3. **Domain Layer**: Core mathematical algorithms and business rules
4. **Infrastructure Layer**: Data persistence, file I/O, and external integrations

### Core Design Principles
- **Modularity**: Each major functionality is implemented as independent modules
- **Data-Driven**: All analysis and strategy generation based on historical data
- **Extensibility**: Support for adding new lottery types and analysis algorithms
- **Performance**: Optimized for large dataset processing and real-time analysis
- **Accuracy**: Strict data validation and mathematical precision

## Components and Interfaces

### 1. Data Management Layer

#### LotteryDataStore
```julia
struct LotteryDataStore
    connection::Database.Connection
    game_configs::Dict{String, GameConfiguration}
end
```

**Responsibilities:**
- Parse and validate lottery data files
- Store historical drawing data with proper indexing
- Manage game type configurations
- Provide data integrity checking (PARSEL-like functionality)

**Key Methods:**
- `import_data(file_path::String, game_type::GameType)`
- `validate_data_format(data::Vector{Drawing})`
- `merge_real_and_simulated_data(real_file::String, sim_file::String)`

#### GameConfiguration
```julia
struct GameConfiguration
    game_type::GameType
    number_count::Int
    number_range::Tuple{Int, Int}
    validation_rules::Vector{ValidationRule}
end
```

### 2. Statistical Analysis Engine

#### StatisticsEngine
```julia
struct StatisticsEngine
    data_store::LotteryDataStore
    calculators::Dict{String, AbstractCalculator}
end
```

**Responsibilities:**
- Generate frequency reports (hot/cold numbers)
- Calculate skip values and skip medians
- Compute filter statistics (median, average, standard deviation)
- Perform trend analysis

**Key Components:**
- `FrequencyCalculator`: Number frequency analysis
- `SkipCalculator`: Skip pattern analysis with FFG median calculation
- `FilterReportGenerator`: Generate WS-style filter reports
- `TrendAnalyzer`: Identify patterns and cyclical behavior

### 3. Filter Analysis Engine

#### FilterEngine
```julia
struct FilterEngine
    filters::Vector{AbstractFilter}
    efficiency_calculator::EfficiencyCalculator
end
```

**Filter Types:**
- `OneFilter`, `TwoFilter`, `ThreeFilter`, `FourFilter`, `FiveFilter`, `SixFilter`
- `DynamicFilter`: Adjusts based on data trends
- `StaticFilter`: Fixed parameters (odd/even, high/low)

**Key Methods:**
- `apply_filters(combinations::Vector{Combination}, filter_settings::FilterSettings)`
- `calculate_efficiency(filter::AbstractFilter, data::HistoricalData)`
- `generate_filter_report(data::HistoricalData, range::Int)`

### 4. Markov Chain Analysis Module

#### MarkovAnalyzer
```julia
struct MarkovAnalyzer
    follower_calculator::FollowerCalculator
    pairing_analyzer::PairingAnalyzer
end
```

**Core Files Generated:**
- `MarkovNumbers`: All numbers sorted hot to cold
- `MarkovPairsPiv`: All pairings with PIVOT, sorted hot to cold
- `MarkovPairsNoP`: All pairings without PIVOT, sorted hot to cold
- `MarkovFollowers`: Markov chain followers for all numbers
- `MarkovLikePairs`: Follower-like pairings for all numbers

**Combination Generation Methods:**
- `generate_hot_combinations()`: From hot-to-cold numbers
- `generate_pivot_combinations()`: From pairings with PIVOT
- `generate_no_pivot_combinations()`: From pairings without PIVOT
- `generate_markov_style_combinations()`: From follower-like pairings
- `generate_traditional_markov_combinations()`: From follower lists

### 5. Skip System Analyzer

#### SkipAnalyzer
```julia
struct SkipAnalyzer
    ffg_calculator::FFGCalculator
    skip_calculator::SkipCalculator
end
```

**Key Features:**
- Calculate skip values (drawings between wins)
- Compute FFG median for each number (50% certainty level)
- Generate positional and non-positional skip systems
- Support DC=1/e and custom skip strategies

**FFG Implementation:**
```julia
function calculate_ffg_median(probability::Float64)::Int
    # N = log(1-DC) / log(1-p) where DC = 0.5
    return ceil(Int, log(0.5) / log(1 - probability))
end
```

### 6. Wonder Grid Strategy Engine

#### WonderGridEngine
```julia
struct WonderGridEngine
    pairing_analyzer::PairingAnalyzer
    combination_generator::CombinationGenerator
end
```

**Strategy Components:**
1. **Key Number Selection**: Based on FFG median and current skip values
2. **Pairing Frequency Analysis**: Identify top pairings for each number
3. **Grid Generation**: Create wonder-grid files showing number + top pairings
4. **Combination Generation**: Generate combinations from key numbers and top pairings

**Efficiency Metrics:**
- 4-number hits: ~2x better than random
- 5-number hits: ~26x better than random  
- 6-number hits (jackpot): ~1669x better than random

### 7. Strategy Optimization Engine

#### StrategyOptimizer
```julia
struct StrategyOptimizer
    purge_engine::PurgeEngine
    lie_eliminator::LIEEliminator
    cost_calculator::CostCalculator
end
```

**Key Features:**
- **Purge Functionality**: Secondary filtering of generated combinations
- **LIE Elimination**: Reverse strategy to eliminate low-probability combinations
- **Favorite Numbers**: Include user-preferred numbers in combinations
- **Cost-Benefit Analysis**: Calculate expected returns and risk assessment

## Data Models

### Core Data Structures

```julia
# Basic drawing result
struct Drawing
    date::Date
    numbers::Vector{Int}
    game_type::GameType
    drawing_id::Int
end

# Combination with metadata
struct Combination
    numbers::Vector{Int}
    probability_score::Float64
    filter_values::Dict{String, Float64}
    generation_method::String
end

# Filter configuration
struct FilterSettings
    filter_type::String
    min_value::Union{Float64, Nothing}
    max_value::Union{Float64, Nothing}
    enabled::Bool
end

# Strategy configuration
struct StrategyConfiguration
    name::String
    filter_settings::Vector{FilterSettings}
    favorite_numbers::Vector{Int}
    favorite_positions::Vector{Int}
    purge_settings::PurgeSettings
    lie_elimination::Bool
end
```

### Database Schema

```sql
-- Historical drawings
CREATE TABLE drawings (
    id INTEGER PRIMARY KEY,
    game_type VARCHAR(50),
    draw_date DATE,
    numbers TEXT, -- JSON array of numbers
    created_at TIMESTAMP
);

-- Filter reports cache
CREATE TABLE filter_reports (
    id INTEGER PRIMARY KEY,
    game_type VARCHAR(50),
    filter_type VARCHAR(50),
    report_data TEXT, -- JSON data
    data_range INTEGER,
    created_at TIMESTAMP
);

-- Generated combinations
CREATE TABLE combinations (
    id INTEGER PRIMARY KEY,
    strategy_name VARCHAR(100),
    numbers TEXT, -- JSON array
    probability_score REAL,
    generation_method VARCHAR(50),
    created_at TIMESTAMP
);
```

## Error Handling

### Data Validation Errors
- **Format Mismatch**: Numbers don't match game type requirements
- **Sorting Errors**: Numbers not in ascending order
- **File Structure**: Newest results not at top
- **Mixed Formats**: Different game types in same file

### Calculation Errors
- **Insufficient Data**: Not enough historical data for reliable analysis
- **Memory Limitations**: Large dataset processing constraints
- **Numerical Overflow**: Extreme filter values causing calculation errors

### Business Logic Errors
- **Invalid Parameters**: Filter settings outside valid ranges
- **Strategy Conflicts**: Incompatible filter combinations
- **Empty Results**: No combinations generated after filtering

### Error Recovery Strategies
- Graceful degradation with warnings for insufficient data
- Automatic fallback to simulated data when needed
- Detailed error reporting with suggested fixes
- Transaction rollback for data integrity issues

## Testing Strategy

### Unit Testing
- **Mathematical Accuracy**: Verify FFG calculations, skip medians, filter efficiency
- **Data Validation**: Test parsing, format checking, and error detection
- **Algorithm Correctness**: Validate Markov chain analysis, Wonder Grid generation

### Integration Testing
- **Data Flow**: End-to-end data processing from import to strategy generation
- **Component Interaction**: Filter engine with strategy optimizer
- **Database Operations**: CRUD operations and data consistency

### Performance Testing
- **Large Dataset Handling**: Test with 12M+ record files
- **Memory Usage**: Monitor memory consumption during analysis
- **Response Times**: Ensure real-time analysis capabilities
- **Concurrent Access**: Multiple users accessing system simultaneously

### User Acceptance Testing
- **Strategy Effectiveness**: Backtest strategies against historical data
- **Usability**: Interface workflows and user experience
- **Report Accuracy**: Validate generated reports against known results

## Performance Optimization

### Data Processing
- **Indexing Strategy**: Optimize database queries for large datasets
- **Caching**: Cache frequently accessed filter reports and statistics
- **Parallel Processing**: Utilize Julia's parallel computing capabilities
- **Memory Management**: Efficient handling of large data structures

### Algorithm Optimization
- **Vectorization**: Use Julia's efficient array operations
- **Lazy Evaluation**: Compute results only when needed
- **Batch Processing**: Process combinations in batches to manage memory
- **Precomputation**: Cache expensive calculations like FFG medians

### System Architecture
- **Connection Pooling**: Efficient database connection management
- **Load Balancing**: Distribute analysis workload across cores
- **Resource Monitoring**: Track CPU, memory, and I/O usage
- **Scalability**: Design for horizontal scaling if needed