"smart_sources:PromptingTools.jl/Agent Tools Introduction - PromptingTools.jl.md": {"path":"PromptingTools.jl/Agent Tools Introduction - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10357526,-0.00284345,-0.00189025,-0.02650238,-0.0233776,0.01511417,-0.00961033,0.03946292,0.03402083,-0.01412784,0.04634286,-0.02767991,0.04048195,0.10848297,0.00868244,-0.00106241,-0.0753037,0.08590082,-0.01735985,-0.05572982,0.05767143,0.05864983,0.01463453,-0.03666767,0.01341852,0.05839825,0.01302673,-0.06273019,-0.01222925,-0.22156569,0.05501333,0.00984694,0.01245509,-0.00108787,-0.01848165,0.00081741,-0.05187536,0.01198954,-0.04655367,0.00424706,0.02781225,0.01064948,0.00500872,-0.04798236,0.0188294,-0.11950054,-0.01584133,-0.03059539,-0.03748139,-0.04366084,0.01175101,-0.03607434,0.041619,-0.01931426,0.03047832,-0.02618314,0.03053378,0.11394361,0.0791392,0.00296929,0.04191083,0.04650478,-0.17268832,0.11797797,0.03868557,0.01917395,-0.02815446,-0.02998726,0.07177895,0.0310726,-0.01956221,-0.02004075,0.01264341,0.03842905,0.01263875,-0.00335872,0.00759379,-0.04238778,0.06365517,-0.05657722,-0.02462672,-0.06283939,-0.05822791,0.01994293,-0.05882709,-0.05065719,-0.00363767,0.01207016,0.09923483,-0.00938747,-0.01005114,-0.02201973,0.01055035,0.0453572,-0.05884151,-0.01062125,-0.02687499,-0.00806628,-0.13452151,0.13017704,0.01568465,-0.00868253,0.00871813,-0.01724138,0.00057944,-0.06254297,-0.04622835,-0.05703918,-0.05797702,-0.00282079,-0.03665963,-0.0341184,0.03265386,-0.02815246,-0.00981457,-0.01312378,0.02217691,0.00340617,0.01295036,-0.04389002,-0.0006295,0.03074882,0.0504188,0.0070434,0.02588706,0.03837176,0.02135431,0.0727558,-0.02154231,0.04647889,0.06893637,0.02712342,-0.03028736,-0.00319863,0.00130772,0.04195342,-0.0286232,-0.03989776,0.02324842,-0.0017321,0.01077589,0.02717401,0.00781963,-0.06780068,-0.01992687,0.04286506,0.01817751,0.0009257,-0.01182917,-0.07718682,0.01749849,0.01832436,0.00444344,-0.01093083,0.04104969,0.04764792,0.06845029,0.06903071,-0.06834238,0.02039554,-0.06833903,-0.03037845,-0.05780241,0.13222368,-0.00250446,-0.0573867,-0.05036096,-0.01114513,-0.01038247,0.01426733,0.04598761,0.01652995,-0.05297277,-0.02312061,-0.01949823,-0.01446668,-0.04039518,0.00146729,0.03674182,0.01802187,0.02168489,-0.05355262,-0.02972473,0.02131692,0.01007571,-0.03767079,-0.04503688,-0.05273093,-0.00854109,-0.00257792,-0.08408836,0.01430287,0.058199,-0.01338504,-0.0249203,-0.03041507,-0.0148008,-0.05047401,0.00360675,-0.06031862,0.06843203,0.0221723,0.0313795,0.02692182,-0.04182586,0.00540209,0.03332362,-0.01094563,0.04647867,0.02580744,-0.0885859,-0.02407125,0.05550038,0.03716432,-0.0836884,-0.03436217,-0.03142628,-0.01303804,-0.01215939,0.04953215,-0.03357721,0.08658676,-0.00901082,-0.2229729,-0.0208745,0.03165445,-0.05011257,0.0750626,-0.09792636,0.05932919,-0.05771396,-0.01520139,0.08366828,0.06959181,-0.00247709,0.02641155,0.01671662,-0.01235638,-0.03662222,-0.00622556,0.00406987,-0.03244487,0.04604267,0.02649713,-0.02599478,0.04249777,-0.10217168,-0.00804291,-0.03980125,0.14142305,0.03225234,0.05895774,-0.00203002,-0.01440211,0.02010894,-0.00425154,-0.06354261,-0.01655436,0.01385173,0.02791724,0.06262348,0.08286756,-0.00375549,-0.02304767,0.03252694,-0.01413939,-0.08744245,0.00957002,-0.02700464,-0.05753144,-0.03497757,-0.06737806,0.00844592,0.02564719,-0.01503864,-0.00921728,0.0014289,0.00780582,-0.00679358,-0.00822182,-0.06658397,0.00718246,0.0036782,0.04329203,0.00038134,-0.00865384,-0.02637047,0.08576403,0.00292436,0.07118467,-0.02968741,0.04206461,-0.01486612,-0.00420069,0.19266164,-0.00010927,0.08048541,-0.02221218,0.00350193,-0.00650998,-0.09421881,0.00750981,-0.02222201,-0.02038455,0.01506351,0.03282849,-0.00814012,0.0649695,0.01733526,-0.0366727,-0.04743474,0.12511258,0.00198189,0.0063005,0.03878533,-0.04121936,0.0337926,0.04730625,-0.01245227,-0.22646394,0.00030192,0.02634894,0.05841717,-0.06156604,0.03370576,0.04920499,-0.01345193,0.00357888,0.02030357,-0.03499959,0.06254747,-0.01237571,0.01905465,0.02135656,0.01512727,0.04452925,-0.02515201,0.02976857,-0.08920131,-0.00721365,0.02141647,0.22181249,-0.0172456,0.01394611,0.02459061,0.01632535,-0.07609322,0.07079379,-0.040374,0.00460876,0.01506944,0.08865362,-0.05431033,0.08789506,0.02984465,-0.04919642,-0.02952746,0.05011303,0.00141875,-0.03453039,0.04155975,0.03041164,0.0140196,0.04723909,0.00120323,-0.05023288,-0.06456301,-0.02492252,0.0568653,-0.02032238,-0.01917551,-0.02969397,-0.00392374,0.0333093,0.00472249,0.01541942,0.05026928,-0.06313594,0.01432252,0.06415147,-0.032971,0.10524026,0.04180397,-0.05728932],"last_embed":{"hash":"a744568f4f89b2202a25cd299e3a8f102fb996dd389a08a71df5695db37c593a","tokens":466}}},"last_read":{"hash":"a744568f4f89b2202a25cd299e3a8f102fb996dd389a08a71df5695db37c593a","at":1745995216955},"class_name":"SmartSource2","outlinks":[{"title":"​","target":"#Agent-Tools-Introduction","line":1},{"title":"​","target":"#Highlights","line":19},{"title":"​","target":"#Highlights","line":34},{"title":"​","target":"#Examples","line":60},{"title":"​","target":"#Automatic-Fixing-of-AI-Calls","line":62},{"title":"​","target":"#References","line":122},{"title":"#","target":"#PromptingTools.Experimental.AgentTools.AIGenerate-extra_tools-agent_tools_intro","line":124},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/lazy_types.jl#L125-L133","line":136},{"title":"#","target":"#PromptingTools.Experimental.AgentTools.AICall-extra_tools-agent_tools_intro","line":140},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/lazy_types.jl#L50-L103","line":213},{"title":"#","target":"#PromptingTools.last_output-extra_tools-agent_tools_intro","line":217},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/lazy_types.jl#L331","line":221},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L202","line":225},{"title":"#","target":"#PromptingTools.last_message-extra_tools-agent_tools_intro","line":229},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/lazy_types.jl#L326","line":233},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/messages.jl#L197","line":237},{"title":"#","target":"#PromptingTools.Experimental.AgentTools.airetry!-extra_tools-agent_tools_intro","line":241},{"title":"Language Agent Tree Search paper","target":"https://arxiv.org/abs/2310.04406","line":558},{"title":"DSPy Assertions paper","target":"https://arxiv.org/abs/2312.13382","line":558},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/retry.jl#L1-L281","line":560},{"title":"#","target":"#PromptingTools.Experimental.AgentTools.print_samples-extra_tools-agent_tools_intro","line":564},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/mcts.jl#L215","line":568},{"title":"#","target":"#PromptingTools.AICode-extra_tools-agent_tools_intro","line":572},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/code_eval.jl#L18-L106","line":694},{"title":"#","target":"#PromptingTools.Experimental.AgentTools.aicodefixer_feedback-extra_tools-agent_tools_intro","line":698},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/code_feedback.jl#L10-L47","line":754},{"title":"#","target":"#PromptingTools.Experimental.AgentTools.error_feedback-extra_tools-agent_tools_intro","line":758},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/Experimental/AgentTools/code_feedback.jl#L162-L166","line":768},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/extra_tools/agent_tools_intro.md","line":772},{"title":"Previous pageText Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":774},{"title":"Next pageRAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":776},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":778},{"title":"Icons8","target":"https://icons8.com","line":778},{"title":"**VitePress**","target":"https://vitepress.dev","line":778}],"blocks":{"#Agent Tools Introduction [​](#Agent-Tools-Introduction)":[1,322],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#{1}":[3,18],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)":[19,59],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)#{1}":[21,22],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)#{2}":[23,24],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)#{3}":[25,26],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)#{4}":[27,28],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)#{5}":[29,30],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)#{6}":[31,32],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Highlights [​](#Highlights)#{7}":[33,59],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Examples [​](#Examples)":[60,121],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Examples [​](#Examples)#Automatic Fixing of AI Calls [​](#Automatic-Fixing-of-AI-Calls)":[62,121],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#Examples [​](#Examples)#Automatic Fixing of AI Calls [​](#Automatic-Fixing-of-AI-Calls)#{1}":[64,121],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)":[122,322],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{1}":[124,159],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{2}":[160,161],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{3}":[162,163],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{4}":[164,165],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{5}":[166,167],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{6}":[168,169],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{7}":[170,172],"#Agent Tools Introduction [​](#Agent-Tools-Introduction)#References [​](#References)#{8}":[173,322],"#API failure because of a non-existent model":[323,327],"#API failure because of a non-existent model#{1}":[324,327],"#we ask to wait 2s between retries and retry 2 times (can be set in `config` in aicall as well)":[328,339],"#we ask to wait 2s between retries and retry 2 times (can be set in `config` in aicall as well)#{1}":[329,339],"#No info message, you just see `success = false` in the properties of the AICall":[340,347],"#No info message, you just see `success = false` in the properties of the AICall#{1}":[341,347],"#Notice that we ask for two samples (`n_samples=2`) at each attempt (to improve our chances).":[348,348],"#Both guesses are scored at each time step, and the best one is chosen for the next step.":[349,349],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!":[350,365],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#{1}":[351,357],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Check that the output is 1 word only, third argument is the feedback that will be provided if the condition fails":[358,358],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Notice: functions operate on `aicall` as the only argument. We can use utilities like `last_output` and `last_message` to access the last message and output in the conversation.":[359,363],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Notice: functions operate on `aicall` as the only argument. We can use utilities like `last_output` and `last_message` to access the last message and output in the conversation.#{1}":[360,363],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Let's ensure that the output is in lowercase - simple and short":[364,365],"#And with OpenAI, we can set `api_kwargs = (;n=2)` to get both samples simultaneously (cheaper and faster)!#Let's ensure that the output is in lowercase - simple and short#{1}":[365,365],"#[ Info: Condition not met. Retrying...":[366,370],"#[ Info: Condition not met. Retrying...#Let's add final hint - it took us 2 retries":[369,370],"#[ Info: Condition not met. Retrying...#Let's add final hint - it took us 2 retries#{1}":[370,370],"#[ Info: Condition not met. Retrying...[2]":[371,371],"#[ Info: Condition not met. Retrying...[3]":[372,376],"#[ Info: Condition not met. Retrying...[3]#We end up with the correct answer":[375,376],"#[ Info: Condition not met. Retrying...[3]#We end up with the correct answer#{1}":[376,376],"#Output: \"yellow\"":[377,384],"#Output: \"yellow\"#{1}":[378,384],"#Root node:":[385,386],"#Root node:#{1}":[386,386],"#Output: SampleNode(id: 46839, stats: 6/12, length: 2)":[387,388],"#Active sample (our correct answer):":[389,390],"#Active sample (our correct answer):#{1}":[390,390],"#Output: 50086":[391,392],"#Let's obtain the active sample node with this ID  - use getindex notation or function find_node":[393,394],"#Let's obtain the active sample node with this ID  - use getindex notation or function find_node#{1}":[394,394],"#Output: SampleNode(id: 50086, stats: 1/1, length: 7)":[395,396],"#The SampleNode has two key fields: data and feedback. Data is where the conversation is stored:":[397,424],"#The SampleNode has two key fields: data and feedback. Data is where the conversation is stored:#{1}":[398,424],"#Output: SampleNode(id: 2733, stats: 1/2, length: 5)":[425,433],"#Output: SampleNode(id: 2733, stats: 1/2, length: 5)#{1}":[426,433],"#Data is the universal field for samples, we put `conversation` in there":[434,434],"#Last item in data is the last message in coversation":[435,437],"#Last item in data is the last message in coversation#{1}":[436,437],"#get only the message content, ie, the guess":[438,442],"#get only the message content, ie, the guess#{1}":[439,442],"#ID: 20493, Answer: yellow":[443,443],"#ID: 50086, Answer: yellow":[444,444],"#ID: 2733, Answer: red":[445,445],"#ID: 30088, Answer: blue":[446,446],"#ID: 44816, Answer: blue":[447,502],"#ID: 44816, Answer: blue#{1}":[448,467],"#ID: 44816, Answer: blue#2 samples at a time, max 5 fixing rounds":[468,470],"#ID: 44816, Answer: blue#2 samples at a time, max 5 fixing rounds#{1}":[469,470],"#ID: 44816, Answer: blue#Check the proper output format - must parse to Int, use do-syntax":[471,471],"#ID: 44816, Answer: blue#We can provide feedback via a function!":[472,479],"#ID: 44816, Answer: blue#We can provide feedback via a function!#{1}":[473,479],"#ID: 44816, Answer: blue#Give a hint on bounds":[480,487],"#ID: 44816, Answer: blue#Give a hint on bounds#{1}":[481,487],"#ID: 44816, Answer: blue#You can make at most 3x guess now -- if there is max_retries in `config.max_retries` left":[488,497],"#ID: 44816, Answer: blue#You can make at most 3x guess now -- if there is max_retries in `config.max_retries` left#{1}":[489,497],"#ID: 44816, Answer: blue#Evaluate the best guess":[498,502],"#ID: 44816, Answer: blue#Evaluate the best guess#{1}":[499,502],"#Let's play the game":[503,531],"#Let's play the game#{1}":[504,519],"#Let's play the game#SampleNode(id: 57694, stats: 6/14, score: 0.43, length: 2)":[520,520],"#Let's play the game#├─ SampleNode(id: 35603, stats: 5/10, score: 1.23, length: 4)":[521,521],"#Let's play the game#│  ├─ SampleNode(id: 55394, stats: 1/4, score: 1.32, length: 6)":[522,522],"#Let's play the game#│  │  ├─ SampleNode(id: 20737, stats: 0/1, score: 1.67, length: 7)":[523,523],"#Let's play the game#│  │  └─ SampleNode(id: 52910, stats: 0/1, score: 1.67, length: 7)":[524,524],"#Let's play the game#│  └─ SampleNode(id: 43094, stats: 3/4, score: 1.82, length: 6)":[525,525],"#Let's play the game#│     ├─ SampleNode(id: 14966, stats: 1/1, score: 2.67, length: 7)":[526,526],"#Let's play the game#│     └─ SampleNode(id: 32991, stats: 1/1, score: 2.67, length: 7)":[527,527],"#Let's play the game#└─ SampleNode(id: 20506, stats: 1/4, score: 1.4, length: 4)":[528,528],"#Let's play the game#├─ SampleNode(id: 37581, stats: 0/1, score: 1.67, length: 5)":[529,529],"#Let's play the game#└─ SampleNode(id: 46632, stats: 0/1, score: 1.67, length: 5)":[530,531],"#Lastly, let's check all the guesses AI made across all samples.":[532,532],"#Our winning guess was ID 32991 (`out.active_sample_id`)":[533,678],"#Our winning guess was ID 32991 (`out.active_sample_id`)#{1}":[535,538],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 20737, Guess: 50":[539,539],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 20737, Guess: 35":[540,540],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 20737, Guess: 37":[541,541],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 52910, Guess: 50":[542,542],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 52910, Guess: 35":[543,543],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 52910, Guess: 32":[544,544],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 14966, Guess: 50":[545,545],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 14966, Guess: 35":[546,546],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 14966, Guess: 33":[547,547],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 32991, Guess: 50":[548,548],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 32991, Guess: 35":[549,549],"#Our winning guess was ID 32991 (`out.active_sample_id`)#ID: 32991, Guess: 33":[550,550],"#Our winning guess was ID 32991 (`out.active_sample_id`)#etc...":[551,678],"#Our winning guess was ID 32991 (`out.active_sample_id`)#etc...#{1}":[552,678],"#Output: AICode(Success: True, Parsed: True, Evaluated: True, Error Caught: N/A, StdOut: True, Code: 2 Lines)":[679,680],"#show the code":[681,682],"#show the code#{1}":[682,682],"#Output:":[683,683],"#numbers = rand(10)":[684,684],"#numbers = rand(1:100, 10)":[685,686],"#or copy it to the clipboard":[687,689],"#or copy it to the clipboard#{1}":[688,689],"#or execute it in the current module (=Main)":[690,780],"#or execute it in the current module (=Main)#{1}":[691,780]},"last_import":{"mtime":1716251738389,"size":35768,"at":1740449882740,"hash":"a744568f4f89b2202a25cd299e3a8f102fb996dd389a08a71df5695db37c593a"},"key":"PromptingTools.jl/Agent Tools Introduction - PromptingTools.jl.md"},