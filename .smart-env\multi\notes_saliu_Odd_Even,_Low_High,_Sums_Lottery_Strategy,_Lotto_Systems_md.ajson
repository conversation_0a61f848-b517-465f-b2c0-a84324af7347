
"smart_sources:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md": {"path":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"1blja2l","at":1753423416091},"class_name":"SmartSource","last_import":{"mtime":1753363611915,"size":23434,"at":1753423416500,"hash":"1blja2l"},"blocks":{"#---frontmatter---":[1,6],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems":[8,195],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#{1}":[10,15],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)":[16,29],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)#{1}":[17,29],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>":[30,57],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>#{1}":[32,57],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>":[58,84],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>#{1}":[60,84],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>":[85,139],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{1}":[87,120],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{2}":[121,121],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{3}":[122,122],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{4}":[123,123],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{5}":[124,124],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{6}":[125,125],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{7}":[126,127],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{8}":[128,139],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>":[140,163],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>#{1}":[142,163],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling":[164,195],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{1}":[166,167],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{2}":[168,168],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{3}":[169,169],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{4}":[170,170],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{5}":[171,171],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{6}":[172,172],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{7}":[173,173],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{8}":[174,174],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{9}":[175,175],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{10}":[176,176],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{11}":[177,177],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{12}":[178,178],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{13}":[179,179],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{14}":[180,180],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{15}":[181,181],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{16}":[182,182],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{17}":[183,183],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{18}":[184,184],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{19}":[185,185],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{20}":[186,186],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{21}":[187,187],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{22}":[188,189],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{23}":[190,195]},"outlinks":[{"title":"This is the best lottery strategy on even odd, low, high, sums, sum totals in lotto.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Introduction to Lottery Strategies, Filtering, Number Grouping","target":"https://saliu.com/strategy.html#Strategy","line":16},{"title":"Pick-3 Lottery Software for _Low_ or _High_, _Odd_ or _Even_ Digit Grouping","target":"https://saliu.com/strategy.html#Pick3","line":17},{"title":"Lotto Software for _Low / High, Odd / Even_ Numbers, Plus _Lotto Skipping_","target":"https://saliu.com/strategy.html#Lotto","line":18},{"title":"True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems","target":"https://saliu.com/strategy.html#Software","line":19},{"title":"Essential Resources in Lotto, Lottery Strategy, Systems, Software","target":"https://saliu.com/strategy.html#Links","line":20},{"title":"Lottery lotto strategy, system on sum, odd, even, low, high numbers created by best software.","target":"https://saliu.com/HLINE.gif","line":22},{"title":"_**Software, formulas to calculate lotto odds, <u>hypergeometric distribution probability</u>**_","target":"https://saliu.com/oddslotto.html","line":42},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lottery-sums.gif","line":46},{"title":"Software calculates root sums, fadic addition for correct lottery strategies based on sums.","target":"https://saliu.com/ScreenImgs/lottery-root-sums.gif","line":50},{"title":"This is the best lotto, lottery strategy on sums, sum, even odd, high low.","target":"https://saliu.com/images/lottery-software.gif","line":56},{"title":"This is the best lotto, lottery strategy software by founder of lotto mathematics.","target":"https://saliu.com/images/lotto-software.gif","line":83},{"title":"Best lottery software for odd even, low high, sums, sum-totals, root sums, number increase decrease.","target":"https://saliu.com/ScreenImgs/lottery-increase.gif","line":111},{"title":"The lotto skips are true filters for effective lottery strategies, systems.","target":"https://saliu.com/ScreenImgs/lottery-skips-filters.gif","line":115},{"title":"_**Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player**_","target":"https://saliu.com/bbs/messages/188.html","line":132},{"title":"This lottery strategy is static because it always generates the same lotto combinations.","target":"https://saliu.com/images/lottery-software.gif","line":138},{"title":"_**Winning Gambling Combinations: Inside Median Bell (Gauss)**_","target":"https://saliu.com/random-picks.html","line":144},{"title":"Resources, links to the best in lotto software, lottery strategies.","target":"https://saliu.com/HLINE.gif","line":160},{"title":"\n\n## 5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling\n\n","target":"https://saliu.com/content/lottery.html","line":162},{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":168},{"title":"_**Lottery Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":169},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":171},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":173},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":175},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":176},{"title":"_**Sum-Totals for Lottery, Lotto Games — Pick 3 4 Lotteries, Lotto 5, 6, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/lottery-sums.html","line":177},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":178},{"title":"_**Lottery Software Sum-Totals, Sums: Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bbs/messages/626.html","line":179},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":180},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":181},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":182},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":183},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":184},{"title":"_**Lotto Software for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":185},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":186},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":187},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":188},{"title":"Lottery, lotto systems based on sum-total, odd, even, low, high runs best software.","target":"https://saliu.com/HLINE.gif","line":190},{"title":"Forums","target":"https://forums.saliu.com/","line":192},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":192},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":192},{"title":"Contents","target":"https://saliu.com/content/index.html","line":192},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":192},{"title":"Home","target":"https://saliu.com/index.htm","line":192},{"title":"Search","target":"https://saliu.com/Search.htm","line":192},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":192},{"title":"You should play dynamic lottery filters as only Ion Saliu's software can do.","target":"https://saliu.com/HLINE.gif","line":194}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["strategy","system","strategies","systems","lottery","lotto","sum","sums","sum-totals","odd","even","low","high","software","eliminate","filter","filtering","combinations"],"source":"https://saliu.com/strategy.html","author":null}},
"smart_sources:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md": {"path":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11469072,-0.03746421,-0.06093811,-0.03268807,-0.03963245,0.00574912,-0.01846506,0.01112951,0.05890415,-0.03438346,0.0502713,-0.0406577,0.05457193,0.00694296,-0.01838184,-0.04833879,-0.00144515,-0.02292216,-0.03999968,0.01563222,0.12315447,-0.02265523,-0.05903076,-0.05714556,0.09717272,-0.04365702,-0.03948516,-0.03501479,-0.01703627,-0.2399552,-0.00849656,0.02268247,0.02597923,-0.05648636,-0.05599847,-0.04997071,-0.01087217,0.07425264,-0.00720872,0.03137776,0.01010599,0.0172611,-0.01718491,-0.01637482,-0.00188078,-0.03538049,0.0082098,0.00794905,0.0012464,-0.00381488,-0.05659314,0.02188703,0.0043755,0.03954194,0.05157825,0.04152414,0.03674465,0.0996602,0.00160049,0.04283932,-0.00899569,0.03436826,-0.15993394,0.0284965,0.01853443,0.02958575,0.01118497,-0.04360373,0.03607586,0.04212524,0.03948472,0.0559622,-0.02559857,0.04883169,0.04396546,-0.04721428,-0.02734932,-0.06218653,-0.03231792,-0.00005107,-0.05729843,-0.05988213,-0.03634799,-0.00981103,0.06674106,0.03226251,0.05561513,-0.01243909,0.07465221,-0.09880587,-0.00880338,0.02730926,0.06361771,0.03811403,0.07615312,0.02304512,0.04537613,-0.01080081,0.01111094,0.1258959,0.00500473,0.0205976,0.01980617,-0.01635933,0.0443233,-0.04454188,-0.0240819,-0.04919486,-0.0343221,0.02914215,0.03873153,-0.00589844,0.08093013,-0.05223909,-0.00696034,-0.02174094,0.03296299,0.02716651,0.03495895,-0.00885459,-0.03854452,-0.00818375,0.02315053,-0.0100667,0.03339952,0.02864434,0.01234365,0.06336153,0.02214355,0.03276118,0.03656676,-0.02761087,-0.10370395,-0.06381025,0.00668947,-0.02301184,-0.00467695,0.00342022,0.00593124,0.01949449,-0.04761898,-0.05273789,0.02170474,-0.12225275,-0.02331126,0.08137327,-0.02149725,-0.01032285,0.00700627,-0.00737408,-0.00048973,-0.01095035,-0.01431466,-0.08582415,-0.0536883,-0.00206335,0.07629658,0.10023577,-0.02657052,-0.01030801,-0.03343144,-0.05606797,-0.040005,0.10655627,-0.01513794,-0.09204092,-0.02653309,0.03689196,-0.03261763,-0.05528704,-0.03866501,-0.01921069,-0.0294959,0.03870237,0.12254436,-0.02574362,-0.04761178,-0.04890445,-0.03445325,0.00672833,-0.02404863,-0.02395357,-0.06124062,-0.00231623,-0.04534175,-0.0576116,0.00798805,-0.0059542,0.02786895,0.03945699,-0.10781834,-0.00319939,-0.02198575,0.02153625,-0.039384,-0.01273757,0.02055754,-0.00948581,0.06217173,-0.00942364,0.00667789,-0.00290084,0.02855367,0.02538456,-0.02865304,0.04796513,-0.00409744,-0.07108868,0.10387379,0.006248,-0.00843305,0.01436048,0.03576815,0.10691962,-0.00235504,0.01762722,-0.00595947,0.04477594,-0.01125656,-0.01088847,-0.01165086,0.00424674,-0.12484206,-0.17313911,-0.03297755,-0.01746999,-0.01205775,0.01027982,-0.02750866,0.0291297,-0.0410478,0.04195681,0.08679667,0.0790976,-0.03825246,-0.00807,0.03996347,-0.02456331,0.02003135,-0.08544814,-0.01557494,-0.02477315,0.05067222,0.02011169,0.04392931,0.02129862,-0.07640016,0.00125903,-0.03033325,0.15386347,0.01088068,0.02953343,-0.02034718,0.01738366,-0.00264585,-0.04184625,0.00204286,-0.00053834,0.05080695,-0.05517035,-0.00605716,-0.03935789,-0.05448695,-0.07646826,-0.02297848,-0.02203433,-0.09138012,-0.02880681,0.06419539,-0.01696127,0.03251059,0.01343599,0.01433822,0.05056254,-0.02613615,0.05809126,0.01057624,0.05616084,-0.00734372,-0.10078966,-0.04790609,0.00149726,0.06358953,-0.04160797,-0.02636826,0.03350285,0.00017143,0.02365276,0.02486883,0.01250105,-0.01721634,0.02131314,0.01899842,-0.03558889,0.10439496,0.00020568,0.0505305,-0.02118015,0.03613811,0.03762005,-0.03839675,-0.00609629,-0.0218237,0.03644183,-0.06222931,0.06306645,0.06821474,0.03318577,0.02199255,0.04132294,0.00282275,0.02173191,-0.01712903,-0.00129952,0.01949877,-0.03973671,0.03135303,0.00929922,-0.01580347,-0.27269343,0.00242554,-0.02757839,0.02940272,0.02849946,-0.03182235,0.01204738,0.00107711,0.02132756,-0.00457275,0.05169059,0.02936439,0.05124794,-0.06845874,-0.03570938,-0.01012665,-0.0345499,-0.01928687,0.07259464,0.0212023,0.04980598,0.03357311,0.27764836,0.02231987,-0.02253213,0.01866918,0.02354115,0.01378485,-0.03314181,0.0329258,0.02784044,0.03470396,0.09261163,0.00693662,-0.05170679,0.00707878,-0.0213493,0.00080687,-0.01646577,0.05638113,-0.06947147,0.0180596,-0.02030091,0.02421917,0.12410695,-0.01327463,-0.04160328,-0.05793633,0.06749983,0.03293324,-0.06762753,-0.03478904,-0.01122542,-0.00257943,0.07016867,0.03235546,-0.00060733,0.03292429,-0.02561877,0.00964705,-0.0108019,0.03545045,0.04008634,0.06764731,0.00750296],"last_embed":{"hash":"1blja2l","tokens":486}}},"last_read":{"hash":"1blja2l","at":1753423579752},"class_name":"SmartSource","last_import":{"mtime":1753363611915,"size":23434,"at":1753423416500,"hash":"1blja2l"},"blocks":{"#---frontmatter---":[1,6],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems":[8,195],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#{1}":[10,15],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)":[16,29],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)#{1}":[17,29],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>":[30,57],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>#{1}":[32,57],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>":[58,84],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>#{1}":[60,84],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>":[85,139],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{1}":[87,120],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{2}":[121,121],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{3}":[122,122],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{4}":[123,123],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{5}":[124,124],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{6}":[125,125],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{7}":[126,127],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{8}":[128,139],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>":[140,163],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>#{1}":[142,163],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling":[164,195],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{1}":[166,167],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{2}":[168,168],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{3}":[169,169],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{4}":[170,170],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{5}":[171,171],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{6}":[172,172],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{7}":[173,173],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{8}":[174,174],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{9}":[175,175],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{10}":[176,176],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{11}":[177,177],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{12}":[178,178],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{13}":[179,179],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{14}":[180,180],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{15}":[181,181],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{16}":[182,182],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{17}":[183,183],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{18}":[184,184],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{19}":[185,185],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{20}":[186,186],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{21}":[187,187],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{22}":[188,189],"#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{23}":[190,195]},"outlinks":[{"title":"This is the best lottery strategy on even odd, low, high, sums, sum totals in lotto.","target":"https://saliu.com/HLINE.gif","line":14},{"title":"Introduction to Lottery Strategies, Filtering, Number Grouping","target":"https://saliu.com/strategy.html#Strategy","line":16},{"title":"Pick-3 Lottery Software for _Low_ or _High_, _Odd_ or _Even_ Digit Grouping","target":"https://saliu.com/strategy.html#Pick3","line":17},{"title":"Lotto Software for _Low / High, Odd / Even_ Numbers, Plus _Lotto Skipping_","target":"https://saliu.com/strategy.html#Lotto","line":18},{"title":"True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems","target":"https://saliu.com/strategy.html#Software","line":19},{"title":"Essential Resources in Lotto, Lottery Strategy, Systems, Software","target":"https://saliu.com/strategy.html#Links","line":20},{"title":"Lottery lotto strategy, system on sum, odd, even, low, high numbers created by best software.","target":"https://saliu.com/HLINE.gif","line":22},{"title":"_**Software, formulas to calculate lotto odds, <u>hypergeometric distribution probability</u>**_","target":"https://saliu.com/oddslotto.html","line":42},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lottery-sums.gif","line":46},{"title":"Software calculates root sums, fadic addition for correct lottery strategies based on sums.","target":"https://saliu.com/ScreenImgs/lottery-root-sums.gif","line":50},{"title":"This is the best lotto, lottery strategy on sums, sum, even odd, high low.","target":"https://saliu.com/images/lottery-software.gif","line":56},{"title":"This is the best lotto, lottery strategy software by founder of lotto mathematics.","target":"https://saliu.com/images/lotto-software.gif","line":83},{"title":"Best lottery software for odd even, low high, sums, sum-totals, root sums, number increase decrease.","target":"https://saliu.com/ScreenImgs/lottery-increase.gif","line":111},{"title":"The lotto skips are true filters for effective lottery strategies, systems.","target":"https://saliu.com/ScreenImgs/lottery-skips-filters.gif","line":115},{"title":"_**Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player**_","target":"https://saliu.com/bbs/messages/188.html","line":132},{"title":"This lottery strategy is static because it always generates the same lotto combinations.","target":"https://saliu.com/images/lottery-software.gif","line":138},{"title":"_**Winning Gambling Combinations: Inside Median Bell (Gauss)**_","target":"https://saliu.com/random-picks.html","line":144},{"title":"Resources, links to the best in lotto software, lottery strategies.","target":"https://saliu.com/HLINE.gif","line":160},{"title":"\n\n## 5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling\n\n","target":"https://saliu.com/content/lottery.html","line":162},{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":168},{"title":"_**Lottery Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":169},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":171},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":173},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":175},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":176},{"title":"_**Sum-Totals for Lottery, Lotto Games — Pick 3 4 Lotteries, Lotto 5, 6, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/lottery-sums.html","line":177},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":178},{"title":"_**Lottery Software Sum-Totals, Sums: Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bbs/messages/626.html","line":179},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":180},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":181},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":182},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":183},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":184},{"title":"_**Lotto Software for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":185},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":186},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":187},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":188},{"title":"Lottery, lotto systems based on sum-total, odd, even, low, high runs best software.","target":"https://saliu.com/HLINE.gif","line":190},{"title":"Forums","target":"https://forums.saliu.com/","line":192},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":192},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":192},{"title":"Contents","target":"https://saliu.com/content/index.html","line":192},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":192},{"title":"Home","target":"https://saliu.com/index.htm","line":192},{"title":"Search","target":"https://saliu.com/Search.htm","line":192},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":192},{"title":"You should play dynamic lottery filters as only Ion Saliu's software can do.","target":"https://saliu.com/HLINE.gif","line":194}],"metadata":{"created":"2025-07-24T21:26:41 (UTC +08:00)","tags":["strategy","system","strategies","systems","lottery","lotto","sum","sums","sum-totals","odd","even","low","high","software","eliminate","filter","filtering","combinations"],"source":"https://saliu.com/strategy.html","author":null}},"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12676246,-0.04358076,-0.03568579,-0.02901178,-0.03938771,-0.0013564,0.00299696,0.02745529,0.06896085,-0.03096181,0.04338717,-0.03497831,0.03291117,0.0221624,-0.01097461,-0.0242942,-0.00145115,-0.03315173,-0.0335521,0.01985471,0.12287753,-0.02597744,-0.07699229,-0.06739011,0.11194607,-0.04179557,-0.04619559,-0.04616876,-0.019623,-0.20974658,0.00246907,0.03069578,0.01318507,-0.04791905,-0.02387258,-0.05761692,-0.01935043,0.07880855,0.02046104,0.03051388,0.01886815,0.02930219,-0.03825764,0.00036519,-0.00110377,-0.03740218,-0.00458373,0.01573263,-0.00586597,0.01408633,-0.04642705,0.01836508,-0.00132973,0.03446251,0.05968992,0.02101883,0.04545207,0.07482059,0.01389842,0.04861431,0.00956902,0.02441386,-0.17249279,0.01589545,0.04748061,0.04056355,0.00032127,-0.03746605,0.01286676,0.04587321,0.05628562,0.04054824,-0.02102889,0.04019824,0.04849005,-0.03344456,-0.02840851,-0.06372213,-0.02976758,-0.0004419,-0.0461974,-0.072194,-0.05464634,-0.02368684,0.03806582,0.01907258,0.05722896,0.02562847,0.0720114,-0.09006672,-0.00347607,0.02727614,0.09696788,0.03238405,0.04512213,0.03414564,0.03762826,-0.00924497,0.00532125,0.12912902,-0.01415757,0.02230615,0.01841298,-0.0100502,0.03978174,-0.01621556,-0.03271139,-0.04672144,-0.0420906,0.02229089,0.02752248,0.00968018,0.08913235,-0.07768657,0.00948511,-0.02697082,0.03920583,0.0328603,0.0336307,0.00017014,-0.04221544,-0.00242373,0.02776214,-0.01273669,0.02292069,0.04321788,0.02548258,0.07490578,0.00779198,0.02508358,0.01835107,-0.02938306,-0.08611396,-0.05516407,0.01689398,-0.02545846,0.01928199,-0.01181121,0.02751662,0.01373396,-0.04169957,-0.02885688,0.02208868,-0.13481441,-0.02104285,0.09217989,-0.00008993,-0.02809291,0.03332735,-0.00063675,-0.02162167,-0.01938875,0.01468437,-0.08458254,-0.04151526,-0.00158692,0.09789192,0.09125408,-0.01315037,-0.01614431,-0.03539561,-0.04612365,-0.04239881,0.1156223,-0.02269643,-0.11104575,-0.03448182,0.02829966,-0.01543077,-0.07271536,-0.03752211,-0.02181297,-0.03339758,0.03331459,0.13042331,-0.03630248,-0.02130616,-0.03705802,-0.04697853,0.00442981,-0.02322439,-0.0203243,-0.06267067,0.02111502,-0.05410549,-0.06165375,-0.00772019,-0.00189279,0.02648875,0.03459958,-0.10860608,-0.02376887,-0.05000883,0.0199424,-0.03236875,-0.0176785,0.02080617,-0.01354787,0.06711686,-0.01494214,-0.00882651,-0.01315259,0.0213056,0.03733241,-0.00183281,0.02637599,-0.00200891,-0.0676546,0.08500701,0.01510205,0.00511861,0.00936362,0.02945164,0.10079456,-0.02175142,0.0202086,-0.02161178,0.0435202,-0.01322092,-0.02167778,-0.03321844,0.00600059,-0.11542545,-0.19744723,-0.02504644,-0.01215092,-0.02677689,-0.00909343,-0.01926509,0.02236138,-0.02081419,0.03399057,0.0899772,0.04788785,-0.04272879,-0.01812613,0.00233247,-0.02268505,0.01072991,-0.08252565,-0.03125075,-0.00730512,0.05829849,0.02453176,0.04405653,0.00323799,-0.08949049,-0.0091909,-0.0028257,0.15150069,0.03756291,0.02647565,-0.03434908,0.03426529,-0.00258678,-0.03522424,-0.00151312,-0.00750309,0.03804303,-0.05210721,0.01387561,-0.05428703,-0.06327009,-0.10454003,-0.01914639,-0.03561545,-0.08395799,-0.01354928,0.04970533,0.00124801,-0.00052067,0.0207954,0.02705916,0.04391595,-0.02487381,0.03906914,0.02532847,0.07846896,0.00094075,-0.10736572,-0.04246361,0.00374633,0.05654918,-0.03157725,-0.02202857,0.03218471,-0.01206891,0.00647619,0.03062777,0.01395725,-0.03818732,0.00148828,0.00023946,-0.03088866,0.10465307,0.00827553,0.02444437,-0.01186744,0.01642243,0.03155565,-0.00843607,0.00377876,-0.00925515,0.04139672,-0.06594045,0.06681227,0.06236055,0.02578068,0.04103183,0.01200069,-0.00712538,0.0277616,-0.01503948,-0.00358635,0.02161852,-0.03198379,0.02404473,0.02020734,-0.03487255,-0.27663803,0.0283918,-0.00323525,0.03344439,0.02633286,-0.0033653,0.00959106,0.01132081,-0.00079911,0.02699184,0.05590171,0.04306549,0.03598636,-0.04978753,-0.03824786,-0.01359959,-0.01945566,-0.01583594,0.06803357,0.02527064,0.0375462,0.0450106,0.27748764,0.04573074,-0.01843945,0.01801042,0.01653495,0.02806677,-0.02298479,0.03441913,0.01962836,0.02902223,0.07440297,-0.0102633,-0.03425434,0.01563049,-0.02646348,0.01530526,-0.00707305,0.06824674,-0.09527481,0.01955375,-0.02614191,0.02231037,0.13269065,-0.00272828,-0.04465615,-0.05868713,0.05060157,0.01571849,-0.07238591,-0.04472018,-0.01667265,0.00366884,0.05261077,0.04698438,0.01106048,0.01995911,-0.00236093,0.00447823,-0.01691773,0.02885742,0.03176068,0.04357702,-0.00473719],"last_embed":{"hash":"1hvekkc","tokens":105}}},"text":null,"length":0,"last_read":{"hash":"1hvekkc","at":1753423577093},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#---frontmatter---","lines":[1,6],"size":242,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11053664,-0.03222989,-0.07252093,-0.04006546,-0.03158392,0.00334039,-0.01449667,0.01094813,0.05529571,-0.03462331,0.05746806,-0.0332165,0.04877691,-0.00660974,-0.0182723,-0.03651712,0.00211429,-0.02574962,-0.04047063,0.00829262,0.11554306,-0.02330024,-0.06027589,-0.04915962,0.09539421,-0.03711615,-0.04498394,-0.0470612,-0.03298316,-0.24085309,-0.00042048,0.01532412,0.02766458,-0.0595093,-0.05471111,-0.04548514,-0.01864865,0.05920973,-0.01938953,0.01730199,0.00505371,0.02321809,-0.01049006,-0.02836326,-0.01020532,-0.0482295,0.00679226,0.01122315,0.01299616,-0.00982326,-0.05777292,0.0248588,0.01615617,0.04959755,0.03993549,0.04432583,0.04087198,0.10285052,-0.00134093,0.04615609,-0.00259622,0.04587314,-0.1453528,0.02892655,0.01944891,0.02355227,0.01014077,-0.04620486,0.04538995,0.04172364,0.04111137,0.06434263,-0.02357464,0.05864606,0.04010071,-0.04888813,-0.01815911,-0.05862726,-0.03509447,0.0007984,-0.07296377,-0.04888382,-0.02977496,-0.01299317,0.07803205,0.03270107,0.04518806,-0.01920735,0.07014114,-0.10347776,-0.00865874,0.05063011,0.04064561,0.03316543,0.08252924,0.00815426,0.06320232,-0.02320127,0.01365422,0.12728867,0.00314733,0.00498599,0.00479474,-0.00539525,0.03822801,-0.04645298,-0.03391697,-0.06359913,-0.05187837,0.0289868,0.04991416,-0.00333095,0.09664304,-0.03344931,-0.01510386,-0.01535153,0.01569069,0.0083804,0.04545341,-0.011107,-0.03564458,-0.00059242,0.02119759,-0.00997884,0.02235739,0.02416673,0.01850971,0.05936023,0.02741749,0.02362877,0.04385853,-0.01866021,-0.10557288,-0.06235772,0.00158841,-0.00809904,-0.01562905,0.00837077,-0.00971813,0.02586186,-0.04373831,-0.06380005,0.03814051,-0.11846156,-0.01637621,0.07136191,-0.02803812,0.00243161,0.00696901,-0.00840015,0.01412621,-0.00080266,-0.02145592,-0.07698884,-0.05764609,-0.00638641,0.07955664,0.09922155,-0.0426308,-0.00548158,-0.0268138,-0.0481088,-0.0259499,0.1040175,-0.0141603,-0.09211259,-0.01786496,0.03306224,-0.04445828,-0.0518291,-0.03601629,0.00576975,-0.03055439,0.04321901,0.10539232,-0.03591082,-0.06131864,-0.0527846,-0.00417283,0.00391652,-0.01390101,-0.01829235,-0.05259306,-0.00847789,-0.03373656,-0.06227887,0.00749566,0.0036863,0.02476337,0.03154677,-0.10417589,0.00489316,-0.02251401,0.01245711,-0.04173139,-0.00929962,0.00752131,-0.00933963,0.07361513,-0.00892363,0.01495257,0.00314211,0.04911292,0.02508591,-0.03009787,0.05395811,0.0016178,-0.08017276,0.10500819,0.00424862,-0.02273005,0.01039036,0.03452057,0.11011184,-0.00740311,0.01749528,-0.00224867,0.04920235,-0.01215799,0.00648186,0.00005614,0.00071412,-0.11298577,-0.17579192,-0.03445848,-0.01143379,0.00142531,0.01927444,-0.01453587,0.03895776,-0.04254514,0.04553837,0.08535072,0.09518658,-0.05041416,-0.0030641,0.06312364,-0.02063227,0.02750106,-0.096754,-0.01732854,-0.04690153,0.04942245,0.00772882,0.03898022,0.00335919,-0.07448546,0.01444011,-0.03657599,0.14633365,-0.00522923,0.02229542,-0.03524923,0.00953701,-0.01246136,-0.04833562,0.00235597,0.00814995,0.05734828,-0.03845072,-0.01444964,-0.03813684,-0.03728954,-0.07777714,-0.01799176,-0.01476165,-0.0904294,-0.02609761,0.0557857,-0.01376682,0.04453554,0.0309097,0.02073207,0.06108868,-0.0300434,0.04851497,0.00990965,0.04774734,-0.01462147,-0.09259735,-0.02881573,0.00489308,0.06822926,-0.02913388,-0.03716291,0.03688702,0.00880052,0.0235044,0.01717658,0.02037071,-0.01223593,0.03266541,0.01011234,-0.03165113,0.09749031,-0.00129879,0.05629804,-0.01838305,0.04495645,0.05395554,-0.04676555,-0.00132858,-0.02866867,0.03813748,-0.05621477,0.06120962,0.07583817,0.03501792,0.01766529,0.04583012,0.01718586,0.01183776,-0.00904214,0.01419136,0.01494152,-0.04442157,0.02882476,-0.00085374,-0.00386347,-0.26017204,0.00158525,-0.04866674,0.03492841,0.02081558,-0.03483102,0.01157167,0.00567504,0.02816343,-0.01600807,0.05323945,0.02083755,0.04560562,-0.09520149,-0.0296518,-0.01754053,-0.03234217,-0.02320274,0.06227655,0.0169018,0.04666158,0.04550144,0.27650425,0.01271454,-0.01776853,0.02879166,0.02578236,0.01818573,-0.03465363,0.01207204,0.03131832,0.02765972,0.0787046,-0.00198474,-0.04328042,-0.00556494,-0.01411344,-0.00714194,-0.01531533,0.04360284,-0.06430168,0.0161229,-0.03355079,0.01709315,0.11922992,-0.00409321,-0.0472637,-0.05711508,0.06710642,0.04857139,-0.07127281,-0.04015355,-0.03093454,-0.01865891,0.07219662,0.03412781,-0.01418537,0.02616156,-0.04476081,0.01086602,-0.00723747,0.03064757,0.04279337,0.06368221,0.00704684],"last_embed":{"hash":"13ij8gs","tokens":497}}},"text":null,"length":0,"last_read":{"hash":"13ij8gs","at":1753423577182},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems","lines":[8,195],"size":23126,"outlinks":[{"title":"This is the best lottery strategy on even odd, low, high, sums, sum totals in lotto.","target":"https://saliu.com/HLINE.gif","line":7},{"title":"Introduction to Lottery Strategies, Filtering, Number Grouping","target":"https://saliu.com/strategy.html#Strategy","line":9},{"title":"Pick-3 Lottery Software for _Low_ or _High_, _Odd_ or _Even_ Digit Grouping","target":"https://saliu.com/strategy.html#Pick3","line":10},{"title":"Lotto Software for _Low / High, Odd / Even_ Numbers, Plus _Lotto Skipping_","target":"https://saliu.com/strategy.html#Lotto","line":11},{"title":"True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems","target":"https://saliu.com/strategy.html#Software","line":12},{"title":"Essential Resources in Lotto, Lottery Strategy, Systems, Software","target":"https://saliu.com/strategy.html#Links","line":13},{"title":"Lottery lotto strategy, system on sum, odd, even, low, high numbers created by best software.","target":"https://saliu.com/HLINE.gif","line":15},{"title":"_**Software, formulas to calculate lotto odds, <u>hypergeometric distribution probability</u>**_","target":"https://saliu.com/oddslotto.html","line":35},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lottery-sums.gif","line":39},{"title":"Software calculates root sums, fadic addition for correct lottery strategies based on sums.","target":"https://saliu.com/ScreenImgs/lottery-root-sums.gif","line":43},{"title":"This is the best lotto, lottery strategy on sums, sum, even odd, high low.","target":"https://saliu.com/images/lottery-software.gif","line":49},{"title":"This is the best lotto, lottery strategy software by founder of lotto mathematics.","target":"https://saliu.com/images/lotto-software.gif","line":76},{"title":"Best lottery software for odd even, low high, sums, sum-totals, root sums, number increase decrease.","target":"https://saliu.com/ScreenImgs/lottery-increase.gif","line":104},{"title":"The lotto skips are true filters for effective lottery strategies, systems.","target":"https://saliu.com/ScreenImgs/lottery-skips-filters.gif","line":108},{"title":"_**Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player**_","target":"https://saliu.com/bbs/messages/188.html","line":125},{"title":"This lottery strategy is static because it always generates the same lotto combinations.","target":"https://saliu.com/images/lottery-software.gif","line":131},{"title":"_**Winning Gambling Combinations: Inside Median Bell (Gauss)**_","target":"https://saliu.com/random-picks.html","line":137},{"title":"Resources, links to the best in lotto software, lottery strategies.","target":"https://saliu.com/HLINE.gif","line":153},{"title":"\n\n## 5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling\n\n","target":"https://saliu.com/content/lottery.html","line":155},{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":161},{"title":"_**Lottery Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":162},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":164},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":166},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":168},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":169},{"title":"_**Sum-Totals for Lottery, Lotto Games — Pick 3 4 Lotteries, Lotto 5, 6, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/lottery-sums.html","line":170},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":171},{"title":"_**Lottery Software Sum-Totals, Sums: Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bbs/messages/626.html","line":172},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":173},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":174},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":175},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":176},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":177},{"title":"_**Lotto Software for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":178},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":179},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":180},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":181},{"title":"Lottery, lotto systems based on sum-total, odd, even, low, high runs best software.","target":"https://saliu.com/HLINE.gif","line":183},{"title":"Forums","target":"https://forums.saliu.com/","line":185},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":185},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":185},{"title":"Contents","target":"https://saliu.com/content/index.html","line":185},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":185},{"title":"Home","target":"https://saliu.com/index.htm","line":185},{"title":"Search","target":"https://saliu.com/Search.htm","line":185},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":185},{"title":"You should play dynamic lottery filters as only Ion Saliu's software can do.","target":"https://saliu.com/HLINE.gif","line":187}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0913564,-0.01892703,-0.04738942,-0.03682743,-0.04768737,0.02055347,0.02981641,0.01275576,0.06029768,-0.0405487,0.06095766,-0.03158097,0.03300982,0.02233851,-0.02163893,-0.02539998,-0.00190249,-0.01825823,-0.05201983,0.02813373,0.13741024,-0.01616978,-0.07566835,-0.05231046,0.10114119,-0.05539736,-0.05075197,-0.03995047,-0.02073886,-0.22150348,0.00720669,0.00926555,0.00277285,-0.04931448,-0.02981342,-0.06570712,-0.01876898,0.08795471,-0.00038805,0.02691901,0.0311956,0.01413589,-0.01829633,-0.01773842,-0.00169465,-0.03650517,0.00550298,0.0165746,0.00686188,0.00136018,-0.0193288,0.05028786,0.018277,0.01265958,0.03099003,0.04123914,0.03808879,0.08886247,0.01662407,0.04144743,-0.01326071,0.0516559,-0.12651591,0.01620538,0.04416954,0.02756149,0.0092031,-0.02966508,0.01750256,0.05136148,0.0708743,0.05330607,-0.03484751,0.04731409,0.03743667,-0.03930124,-0.0326714,-0.06067368,-0.03520877,0.00605096,-0.08573449,-0.08383714,-0.03189647,-0.0096636,0.05352831,0.02053852,0.04241233,-0.01825286,0.07582537,-0.09129554,0.00041254,0.04974724,0.03605797,0.02884174,0.09314086,0.00971545,0.0435074,-0.03088413,0.03605895,0.13895935,0.01083294,0.02334017,0.01864911,-0.01599435,0.02927576,-0.05529153,-0.04694292,-0.05531769,-0.04383419,0.02516959,0.03632723,-0.00538127,0.09756062,-0.0467637,0.0055821,-0.02238747,0.02895562,0.03562335,0.02327272,-0.00446164,-0.03347486,0.00780542,0.02029288,-0.01348319,0.0135043,0.00631017,0.01183401,0.06402823,0.0208641,0.02195251,0.03842995,-0.04365759,-0.08582974,-0.07017516,-0.00262262,-0.01992821,0.03015887,0.02547147,0.02098478,0.02295252,-0.03708728,-0.05139152,0.03456802,-0.1243303,-0.0171252,0.07689533,-0.01773266,0.01214753,0.02346421,-0.00557969,0.00073269,-0.01409893,-0.0160032,-0.09086978,-0.06085647,-0.0005874,0.05767914,0.09692892,-0.02725532,-0.02389579,-0.05156916,-0.0349012,-0.03607817,0.09191491,-0.02533307,-0.06774781,-0.02849166,-0.00639535,-0.03753261,-0.07181096,-0.02735347,-0.00217412,-0.04204943,0.04109803,0.10327,-0.03173366,-0.05979351,-0.05098915,-0.03009194,0.00974292,-0.02119219,-0.01646687,-0.05815151,0.021737,-0.03543399,-0.04774519,-0.00692771,0.00420314,0.0245907,0.03235224,-0.11184856,0.00096588,-0.04679656,0.02018884,-0.02887477,-0.02229781,0.02599196,0.00561121,0.0796932,-0.00607482,0.0040752,0.00173667,0.03001808,0.03603642,-0.01424038,0.03914521,0.00194022,-0.07605227,0.10977891,0.01383801,-0.01945367,0.01341149,0.00994775,0.10847817,-0.00459536,0.01639287,-0.00873284,0.04550328,-0.0102162,-0.02611733,-0.01235715,-0.03720809,-0.08857202,-0.18915054,-0.05004397,-0.00884171,-0.02347313,0.01859299,-0.0167338,0.00321147,-0.02601316,0.03690229,0.08652114,0.06473371,-0.07203298,-0.00329817,0.03871647,-0.02513,0.03061034,-0.1179599,-0.03225549,-0.01190292,0.05084194,0.01824997,0.04712592,0.00022242,-0.09012197,0.01481393,-0.00175858,0.1436106,0.00466057,0.04740937,-0.05172394,0.00206287,-0.02700927,-0.04129847,0.04045413,-0.00862984,0.02631146,-0.05001083,-0.03738767,-0.06526253,-0.05069055,-0.07816246,-0.02910881,-0.03681698,-0.07044123,-0.03861942,0.06517971,-0.0114609,0.03319864,0.02432395,0.0242317,0.04691066,-0.02126719,0.0549709,0.01054769,0.06949727,-0.00840705,-0.09521572,-0.0412012,-0.00255718,0.07245103,-0.01983832,-0.02443267,0.03085549,0.00728594,0.02977045,0.01397873,0.02624974,-0.03876901,0.0311587,0.01867006,-0.03148393,0.08983462,-0.00443418,0.05196429,-0.01301859,0.03895043,0.05540434,-0.02394793,-0.00185421,-0.0280273,0.06066944,-0.05257915,0.0465653,0.05211463,0.00395603,0.00410292,0.03239184,0.00685565,0.00409446,-0.01069172,-0.00281883,0.02270568,-0.02939804,0.01273182,-0.01723237,-0.02024864,-0.26332802,0.02473056,-0.05028329,0.0348574,0.01879584,-0.04632386,0.01284379,0.0132104,0.01570829,0.0151226,0.05816145,0.02569655,0.0470144,-0.06711147,-0.02264144,-0.03854359,-0.01031774,-0.03113608,0.06742188,0.02361142,0.07659793,0.04043164,0.2892209,0.02588394,-0.00229264,0.01564988,0.03130339,0.0255956,-0.04436133,0.01458645,0.04844854,0.01902573,0.066782,-0.00606932,-0.03473185,-0.01657072,-0.01204845,0.03586381,-0.00327635,0.06498901,-0.05760206,0.01470088,-0.05060507,0.02350681,0.12858137,-0.00311242,-0.03999145,-0.04609201,0.06426945,0.03510246,-0.06716125,-0.02090039,-0.01771363,-0.02365329,0.060214,0.03782334,-0.02305333,0.02758308,-0.02613362,0.01300614,-0.0071478,0.05225335,0.03550785,0.06299934,-0.01363377],"last_embed":{"hash":"1q1tly4","tokens":120}}},"text":null,"length":0,"last_read":{"hash":"1q1tly4","at":1753423577425},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#{1}","lines":[10,15],"size":295,"outlinks":[{"title":"This is the best lottery strategy on even odd, low, high, sums, sum totals in lotto.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12367715,-0.04453027,-0.07771352,-0.03863602,-0.0224124,0.01009099,-0.02475421,0.00329786,0.04669451,-0.03757079,0.05613973,-0.03435406,0.04283265,-0.00894459,-0.00999042,-0.0373063,0.00533392,-0.02959424,-0.04110126,-0.00001665,0.10040872,-0.01583188,-0.05558326,-0.04735414,0.09947286,-0.02001204,-0.04015326,-0.0424685,-0.02821266,-0.2429806,0.00111854,0.02992489,0.03120053,-0.05902937,-0.06080073,-0.03815036,-0.023963,0.05154688,-0.02859907,0.01389849,0.00496737,0.01831082,-0.00624046,-0.02612645,-0.01366577,-0.05420319,0.00595128,0.00563681,0.0145495,-0.0190396,-0.07758468,0.02410865,0.00604408,0.05672853,0.04459013,0.0389139,0.05205299,0.10809904,-0.00569153,0.04173192,0.0047689,0.02852269,-0.15092304,0.03081607,0.01554405,0.02116343,0.01071803,-0.04452499,0.04587276,0.03040783,0.02403494,0.05640863,-0.01950941,0.06139607,0.05050938,-0.05295032,-0.02590178,-0.05631355,-0.02991087,0.00145862,-0.07107972,-0.03476966,-0.02816688,-0.01110293,0.08093581,0.03468172,0.05198722,-0.01627235,0.06337921,-0.10441036,-0.01862155,0.05381534,0.03605134,0.03721437,0.07384444,0.00993582,0.07003379,-0.02768138,0.00143115,0.13433905,0.00169896,0.00058775,0.00121658,0.00149607,0.03968982,-0.03800649,-0.02439658,-0.05964254,-0.04245418,0.02336823,0.0438138,-0.00105274,0.08731161,-0.0414673,-0.02228371,-0.02712324,0.00444861,0.00288111,0.05613276,-0.0164749,-0.02470331,-0.00994626,0.03249898,-0.00596757,0.02435243,0.03112479,0.0144896,0.05718455,0.02883333,0.02277829,0.04132524,-0.00968401,-0.10368917,-0.05819896,0.00975678,-0.00726952,-0.0292511,0.00165624,-0.01136708,0.02500456,-0.0342759,-0.06168935,0.05170304,-0.12773992,-0.01013487,0.07010951,-0.04557225,-0.01121848,-0.00378743,-0.01105689,0.00908889,-0.00731431,-0.03075466,-0.06368577,-0.04836572,-0.01297824,0.08353072,0.09337468,-0.04090393,0.00811896,-0.02943664,-0.04921504,-0.01602216,0.12325143,-0.01115148,-0.08682013,-0.02193082,0.04026199,-0.03879402,-0.03628755,-0.03100736,0.00252819,-0.02860379,0.04782775,0.11251175,-0.03835181,-0.05933277,-0.06093166,-0.00391185,0.00032426,-0.01097651,-0.03058837,-0.05007984,-0.00399177,-0.02966741,-0.0647879,0.00565549,0.00084269,0.02117666,0.03336637,-0.10552269,0.00677738,-0.02759095,-0.00362468,-0.05282017,-0.00767883,-0.00174252,-0.01993432,0.06432104,-0.01880865,0.01209389,0.0126205,0.05829115,0.03305557,-0.02681348,0.0537514,0.00122728,-0.08286832,0.10402866,0.00028664,-0.02070548,0.01034714,0.04427541,0.10343886,-0.00262441,0.0165609,-0.00643759,0.05125694,-0.01905159,0.01547181,0.00912092,0.01902919,-0.13106306,-0.17462729,-0.02220756,-0.00708721,-0.00084922,0.02592137,-0.02725446,0.05266234,-0.05234228,0.04714822,0.08475967,0.10300777,-0.04228824,-0.00901807,0.07462569,-0.02273596,0.02671544,-0.07518324,-0.02198413,-0.04596988,0.05527155,-0.00154669,0.03876428,0.00515967,-0.06318466,0.00545803,-0.03787222,0.14125644,-0.00365448,0.02023676,-0.03653356,0.01822755,-0.00514105,-0.06098563,-0.01002793,0.01062755,0.07064578,-0.04137917,-0.00577995,-0.02642048,-0.03563867,-0.06882735,-0.00667933,-0.01179208,-0.08534411,-0.01993764,0.04441321,-0.01269853,0.03625391,0.03818922,0.01106116,0.04976123,-0.01849495,0.05150442,0.01693814,0.02725333,-0.01151099,-0.09072658,-0.02808268,0.00350638,0.06177491,-0.03250916,-0.04151123,0.03405103,-0.00608091,0.02201806,0.02129356,0.01421271,-0.02362563,0.04031239,0.0113328,-0.03597961,0.09865574,-0.00635989,0.04853192,-0.01852576,0.0492584,0.05134274,-0.05608132,-0.0052234,-0.0139954,0.02324889,-0.05084517,0.06084108,0.0774467,0.04392662,0.03237091,0.04397905,0.00981747,0.01824073,-0.00874931,0.01617163,0.0211679,-0.05000921,0.04017652,0.00944672,-0.01236365,-0.26306829,-0.00198259,-0.02844315,0.04119715,0.02868396,-0.01035057,0.01200189,-0.00411278,0.02199969,-0.01649076,0.06450383,0.01127991,0.04710769,-0.08459,-0.03462537,-0.00356122,-0.01629772,-0.02110428,0.06189448,0.01717296,0.02662058,0.04793721,0.2711919,0.00084843,-0.02698115,0.03062266,0.01910954,0.01369734,-0.03760325,0.01584579,0.02740985,0.02833663,0.08704253,-0.00125257,-0.03339643,0.00992864,-0.01507238,-0.01676201,-0.0094999,0.04630114,-0.0643366,0.00855015,-0.03203376,0.01776737,0.11332994,-0.01304338,-0.05010107,-0.05856181,0.0653313,0.05379438,-0.07216831,-0.0403916,-0.02543442,-0.01397003,0.07463115,0.02746616,-0.00908442,0.02540906,-0.04019129,0.0126051,-0.00871531,0.01941135,0.03942987,0.05954694,0.01049591],"last_embed":{"hash":"1178txf","tokens":387}}},"text":null,"length":0,"last_read":{"hash":"1178txf","at":1753423577476},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)","lines":[16,29],"size":1053,"outlinks":[{"title":"Introduction to Lottery Strategies, Filtering, Number Grouping","target":"https://saliu.com/strategy.html#Strategy","line":1},{"title":"Pick-3 Lottery Software for _Low_ or _High_, _Odd_ or _Even_ Digit Grouping","target":"https://saliu.com/strategy.html#Pick3","line":2},{"title":"Lotto Software for _Low / High, Odd / Even_ Numbers, Plus _Lotto Skipping_","target":"https://saliu.com/strategy.html#Lotto","line":3},{"title":"True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems","target":"https://saliu.com/strategy.html#Software","line":4},{"title":"Essential Resources in Lotto, Lottery Strategy, Systems, Software","target":"https://saliu.com/strategy.html#Links","line":5},{"title":"Lottery lotto strategy, system on sum, odd, even, low, high numbers created by best software.","target":"https://saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12558936,-0.04540991,-0.07555946,-0.03802127,-0.02566892,0.00368848,-0.02766251,0.00290604,0.04781095,-0.03374408,0.05777629,-0.03674188,0.04326413,-0.01337813,-0.00803364,-0.03576363,0.00500921,-0.02765441,-0.03778736,0.00156639,0.10096323,-0.0155645,-0.05633908,-0.04753245,0.09973219,-0.02667488,-0.04439238,-0.04379529,-0.03251306,-0.24000727,0.00002979,0.02963042,0.02834114,-0.06022099,-0.0581299,-0.0385174,-0.02331033,0.05095548,-0.02976254,0.00955439,0.00204024,0.02158663,-0.00847656,-0.02637124,-0.01326672,-0.05828787,0.00836316,0.00321316,0.01416252,-0.01838213,-0.07339248,0.02385774,0.00954837,0.05868044,0.04106387,0.03700809,0.0505772,0.11053451,-0.007149,0.03985703,0.00480913,0.02939243,-0.15017998,0.02452848,0.01637636,0.02443434,0.01238025,-0.05099507,0.04204298,0.03288947,0.02795233,0.05721731,-0.02232466,0.05891256,0.0476332,-0.05637997,-0.02301135,-0.05992068,-0.03320144,-0.00044062,-0.06708412,-0.03914893,-0.03311267,-0.01005752,0.07798994,0.0324595,0.04959888,-0.01984164,0.06297387,-0.10409407,-0.01968495,0.04730803,0.04226585,0.0395279,0.07103071,0.01622177,0.07301018,-0.02387013,0.00109081,0.13371651,0.00292269,-0.00306181,-0.00019461,-0.00278375,0.04027164,-0.03836089,-0.02376459,-0.05612378,-0.04451191,0.02446037,0.04274699,0.00144261,0.09331494,-0.03600575,-0.0231131,-0.03020912,0.00578221,0.00066436,0.04655417,-0.01219926,-0.02518404,-0.00895183,0.0349006,-0.00731306,0.02545129,0.03105355,0.01822818,0.05570206,0.02378986,0.02838223,0.04549088,-0.01206059,-0.10410277,-0.05488374,0.01404707,-0.00670543,-0.02977344,0.00452746,-0.0133204,0.02126314,-0.0378337,-0.05731504,0.04502754,-0.11960809,-0.0073288,0.066231,-0.04530795,-0.01215289,0.00029076,-0.01226754,0.014428,-0.0062404,-0.03158691,-0.05930721,-0.04713535,-0.01517318,0.09023611,0.09139656,-0.04388039,0.00677625,-0.02671668,-0.05268599,-0.020343,0.12366837,-0.01259061,-0.08420575,-0.02165322,0.04316451,-0.0360273,-0.03410709,-0.03116789,0.00515837,-0.03323134,0.04290067,0.11166995,-0.03879333,-0.05762266,-0.05377133,0.00017391,-0.00598441,-0.01135239,-0.02952578,-0.04917772,-0.00515146,-0.0326268,-0.06462929,0.00529843,0.00288089,0.02053798,0.03013875,-0.10450783,0.01371816,-0.02381561,-0.00116894,-0.05280462,-0.00428539,-0.0030974,-0.0190726,0.06591608,-0.02117891,0.0133627,0.0121854,0.06224025,0.03143491,-0.0239199,0.05677176,0.00346708,-0.08302508,0.10608338,-0.00078566,-0.02070586,0.00653697,0.05109753,0.10724231,-0.01054781,0.02003337,-0.00739734,0.04987215,-0.02470834,0.010751,0.00487552,0.02400026,-0.13456672,-0.17623541,-0.01800613,-0.00790418,0.00237879,0.02682883,-0.02334397,0.05209148,-0.0508346,0.04400788,0.08730154,0.10216691,-0.0439146,-0.00574333,0.07536,-0.02339642,0.02841078,-0.07577859,-0.01922216,-0.04166134,0.05188422,-0.00502581,0.04377195,0.00508536,-0.063167,0.00989837,-0.03732348,0.1390209,0.0019278,0.01887516,-0.03960231,0.01728161,-0.0060521,-0.05875046,-0.01611017,0.01193617,0.07459015,-0.03670211,-0.0073765,-0.02490421,-0.04162904,-0.07562482,-0.00465358,-0.01409051,-0.08719393,-0.01357292,0.0442548,-0.01013784,0.03585377,0.03914887,0.01369315,0.04782781,-0.01580188,0.04662592,0.01794273,0.03210764,-0.00795433,-0.09351496,-0.02674539,0.00743002,0.06316505,-0.03093872,-0.04078085,0.03043949,-0.00152679,0.02235285,0.02107719,0.01698248,-0.02176652,0.04320544,0.01119655,-0.0295632,0.09900492,-0.00897391,0.04316118,-0.01629768,0.05350707,0.05114488,-0.05552505,-0.00480308,-0.02000077,0.02060686,-0.04863077,0.06210923,0.07766565,0.04622568,0.03220626,0.0448969,0.00861562,0.01959942,-0.00865421,0.01482614,0.0247109,-0.04931422,0.03714455,0.01086243,-0.00830231,-0.2617743,-0.00783899,-0.02804308,0.04283744,0.02784285,-0.01175739,0.01027209,-0.00488898,0.02157702,-0.01358038,0.06583793,0.00986787,0.05043316,-0.08005193,-0.03497712,-0.00315627,-0.01894226,-0.01765029,0.06405044,0.01489348,0.02579445,0.04906952,0.27104208,0.00444927,-0.02808239,0.0287783,0.02065144,0.01127196,-0.0383395,0.01659526,0.0254742,0.03088572,0.08618979,0.00198561,-0.03368981,0.00942597,-0.02130758,-0.0194286,-0.00959204,0.04931978,-0.06280801,0.01292703,-0.03868035,0.01581679,0.10926569,-0.01352927,-0.05137258,-0.0577314,0.06557129,0.05280396,-0.07451576,-0.04466946,-0.02827486,-0.0160849,0.07824376,0.02720667,-0.01198703,0.0249836,-0.04196972,0.00946293,-0.01584056,0.01977127,0.04102444,0.05456022,0.00945108],"last_embed":{"hash":"wkvc63","tokens":358}}},"text":null,"length":0,"last_read":{"hash":"wkvc63","at":1753423577666},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems##I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)#{1}","lines":[17,29],"size":937,"outlinks":[{"title":"Pick-3 Lottery Software for _Low_ or _High_, _Odd_ or _Even_ Digit Grouping","target":"https://saliu.com/strategy.html#Pick3","line":1},{"title":"Lotto Software for _Low / High, Odd / Even_ Numbers, Plus _Lotto Skipping_","target":"https://saliu.com/strategy.html#Lotto","line":2},{"title":"True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems","target":"https://saliu.com/strategy.html#Software","line":3},{"title":"Essential Resources in Lotto, Lottery Strategy, Systems, Software","target":"https://saliu.com/strategy.html#Links","line":4},{"title":"Lottery lotto strategy, system on sum, odd, even, low, high numbers created by best software.","target":"https://saliu.com/HLINE.gif","line":6}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09602121,-0.04623844,-0.06296469,-0.05702821,-0.04457403,-0.02103258,0.01666901,0.00335464,0.04381234,-0.02133803,0.05908608,-0.0004766,0.08250936,-0.01189624,-0.02487134,-0.01988507,-0.00195232,-0.05598016,-0.04584697,-0.00445112,0.09154651,-0.00384451,-0.06847714,-0.06164169,0.0909746,-0.0320686,-0.04100287,-0.05983108,-0.03963327,-0.25198635,0.01258748,0.02229317,0.03645469,-0.05877989,-0.04032397,-0.04497079,-0.01634381,0.0733218,-0.0333559,0.03192185,0.01467529,0.0059327,-0.00566417,-0.01228028,-0.01223711,-0.02326182,-0.00009961,-0.00464049,-0.02038485,-0.02015393,-0.06047833,0.03068129,0.02259982,0.05782989,0.00687217,0.03632641,0.06372283,0.0895263,0.00641557,0.03964664,0.00172667,0.01764811,-0.13353245,0.0556159,0.01671723,0.02803017,0.01187874,-0.03388032,0.0252545,0.07000601,0.07086638,0.05715533,-0.02180396,0.07315727,0.03938894,-0.03915262,-0.02207928,-0.03531141,-0.03675697,0.03161166,-0.07174338,-0.0606731,-0.02865442,-0.02746398,0.06467549,0.03000828,0.04490478,-0.00532709,0.08269236,-0.07320577,0.01126147,0.02873575,0.03339133,0.04942099,0.06582159,0.00299062,0.0467305,-0.01368291,0.0000095,0.11883017,0.01408847,0.01623818,0.02474838,-0.00884385,0.02910061,-0.04507372,-0.03488581,-0.04845595,-0.02319571,0.03195605,0.03876776,-0.01422451,0.08673185,-0.0241022,-0.0120595,0.00066153,0.01134376,0.01697482,0.053151,-0.01527882,-0.02831897,-0.01202433,0.01293378,-0.01770982,0.01091986,0.05524208,0.01549682,0.07221083,0.03978448,0.02769029,0.03269206,-0.01841365,-0.11217121,-0.04385225,-0.00176559,-0.0130568,-0.00091383,-0.01617989,0.01562663,0.03280071,-0.03846771,-0.0457673,0.02152981,-0.10038497,-0.03545107,0.09348325,-0.00701048,-0.01912612,0.00526679,-0.01734496,0.00751366,-0.00753184,-0.06044807,-0.06215769,-0.04040788,-0.01101863,0.1011868,0.07720684,-0.04007634,-0.01124943,-0.03737675,-0.03463846,-0.04277757,0.08137986,0.01138665,-0.08928254,-0.03378168,-0.01137053,-0.05234977,-0.06108549,-0.03406851,0.00719345,-0.02322733,0.03533969,0.07859728,-0.03071227,-0.07940336,-0.05143522,-0.03559502,-0.00738596,0.01590108,-0.01947755,-0.05098445,0.00232522,-0.02637853,-0.0734079,-0.00021286,-0.01108223,0.01964325,0.05424815,-0.0955201,-0.01929901,-0.04022049,-0.00820899,-0.02705984,-0.02285225,0.02732741,-0.01587707,0.05711833,-0.04878883,0.01377064,-0.01442497,0.03507356,0.07000891,-0.0344871,0.05396897,0.01841119,-0.07731894,0.10901698,0.01156963,-0.03180271,-0.01188414,0.02084235,0.09435081,0.02837967,0.0142207,-0.00952241,0.06815068,0.00635803,0.00597511,0.01274943,0.02579833,-0.08847752,-0.19158648,-0.03790409,-0.00967677,0.02562289,0.01537288,-0.00731957,0.01135995,-0.04888548,0.01895601,0.1040702,0.08523338,-0.06089876,-0.00558223,0.0421722,-0.02352749,0.00317292,-0.09911209,-0.03055162,-0.04187273,0.04313807,0.00200387,0.02735137,-0.00032414,-0.08544239,0.01310222,-0.01013473,0.14504211,0.02523081,0.00117622,-0.04084969,0.00340499,-0.01923198,-0.02117252,0.00799774,0.01129731,0.05127401,-0.02133248,-0.00342579,-0.0287482,-0.03153825,-0.09059533,-0.03378768,-0.03571716,-0.08597589,-0.03690363,0.06821375,-0.03943321,0.05856242,0.01813767,0.03788015,0.04340649,-0.04474617,0.04763983,0.02776709,0.06445823,-0.0291283,-0.08062708,-0.01351119,0.01050309,0.05510482,-0.01940187,-0.04487477,0.05901456,-0.01137749,0.0489794,0.0368351,0.01454039,0.00077534,0.03915558,0.0169264,-0.06215036,0.1089556,-0.02387344,0.03537025,-0.02028141,0.04115725,0.0155352,-0.03785222,-0.00137469,-0.02689531,0.0577953,-0.06279286,0.07213508,0.07107472,0.04024816,0.03441168,0.02850445,0.01478903,0.01896081,-0.01183485,-0.00387907,0.02503371,-0.04551572,0.03865157,0.01984243,-0.03063398,-0.24789973,0.02356454,-0.03255013,0.05110648,0.01168127,-0.02887975,0.02762067,0.02962044,0.02239705,-0.00895828,0.01865728,0.01169389,0.05690213,-0.09302258,-0.03898807,-0.0052448,-0.05958116,-0.0360368,0.05353748,0.02326657,0.07047856,0.05127112,0.2812328,0.00367375,-0.00792759,-0.00002346,0.01574219,0.02519163,-0.04036657,0.0368847,0.02186384,0.0096856,0.07977066,-0.00949464,-0.02850009,-0.03922743,0.01026515,0.01537951,0.0115883,0.01840197,-0.05885483,0.00624956,-0.0142562,0.00489241,0.12556329,0.00906997,-0.032383,-0.05519682,0.0523202,0.06083389,-0.08975036,-0.03232644,-0.02974183,-0.0236704,0.07070545,0.0390573,-0.01942229,0.01165311,-0.01612653,0.01581131,-0.00918604,0.0195905,0.049351,0.04367592,-0.02135393],"last_embed":{"hash":"aphx8e","tokens":430}}},"text":null,"length":0,"last_read":{"hash":"aphx8e","at":1753423577812},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>","lines":[30,57],"size":4280,"outlinks":[{"title":"_**Software, formulas to calculate lotto odds, <u>hypergeometric distribution probability</u>**_","target":"https://saliu.com/oddslotto.html","line":13},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lottery-sums.gif","line":17},{"title":"Software calculates root sums, fadic addition for correct lottery strategies based on sums.","target":"https://saliu.com/ScreenImgs/lottery-root-sums.gif","line":21},{"title":"This is the best lotto, lottery strategy on sums, sum, even odd, high low.","target":"https://saliu.com/images/lottery-software.gif","line":27}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09425487,-0.04692471,-0.0610041,-0.0569713,-0.04358075,-0.02050944,0.01495686,0.00426679,0.04519415,-0.02034055,0.05850499,0.0020811,0.08408374,-0.01239042,-0.02531307,-0.01971353,-0.00342273,-0.05762852,-0.04574328,-0.00539589,0.09184069,-0.00212094,-0.06861059,-0.06025941,0.09130235,-0.03202302,-0.04098669,-0.06106943,-0.04051001,-0.25040093,0.01351122,0.02078757,0.03518957,-0.05988266,-0.04063184,-0.04569486,-0.01552297,0.07518264,-0.03501039,0.03381638,0.01519975,0.00525226,-0.00349364,-0.01278469,-0.011755,-0.02407537,0.0002008,-0.00579123,-0.02074838,-0.01881963,-0.05939997,0.03042831,0.02314517,0.05766876,0.0075986,0.03472133,0.06264628,0.08883535,0.00619951,0.040246,0.00073403,0.0174292,-0.1356065,0.05755985,0.014904,0.02855367,0.01188386,-0.03041473,0.02561273,0.07335569,0.07078373,0.05581799,-0.01992892,0.07552724,0.03922939,-0.03972592,-0.02260742,-0.03547497,-0.03752212,0.03347308,-0.07282545,-0.05764923,-0.02924811,-0.02811557,0.06086292,0.02910414,0.04559842,-0.00527376,0.0822188,-0.0721979,0.01153952,0.0262139,0.0309089,0.04927708,0.06414425,0.00247366,0.04416995,-0.01357963,0.00059454,0.11782427,0.01461997,0.01675379,0.02535493,-0.00969202,0.03058361,-0.04651915,-0.03363121,-0.04864026,-0.02332791,0.03282888,0.03896979,-0.0146381,0.08416031,-0.02429944,-0.01169395,0.00157216,0.01103705,0.01493907,0.05307904,-0.01525629,-0.03013755,-0.01051532,0.01257056,-0.01837841,0.01064359,0.05701983,0.01886417,0.07306121,0.04118329,0.02831202,0.03352246,-0.0170834,-0.11339822,-0.0442424,-0.00177108,-0.01362089,-0.00128116,-0.01748862,0.01518154,0.03305905,-0.03738959,-0.04394051,0.02237792,-0.09847263,-0.0375029,0.09476294,-0.00544148,-0.01930249,0.00486123,-0.0174826,0.00835633,-0.00402708,-0.06008172,-0.06148469,-0.04114381,-0.00966686,0.10291258,0.08023789,-0.03976158,-0.0118509,-0.03809012,-0.03540487,-0.04076178,0.07999894,0.01048512,-0.08970019,-0.03543226,-0.01308522,-0.053537,-0.0612783,-0.03374839,0.00842463,-0.0238945,0.03480802,0.07781532,-0.02908946,-0.08122019,-0.05182294,-0.03503019,-0.00843334,0.01717061,-0.02065397,-0.05019876,0.00270795,-0.02642826,-0.07325882,-0.0006431,-0.01166874,0.01950066,0.0550673,-0.09469845,-0.01825051,-0.03972461,-0.00943052,-0.02741246,-0.02361007,0.02491257,-0.01627215,0.05682494,-0.05143899,0.01528494,-0.0153741,0.03337861,0.06988743,-0.0344771,0.05252834,0.01851626,-0.07634687,0.10947061,0.01086052,-0.03319509,-0.01153588,0.02124783,0.09449313,0.0285438,0.01268442,-0.00853152,0.06836806,0.00500877,0.00503959,0.0124017,0.02705872,-0.0890083,-0.1912941,-0.03834641,-0.00824223,0.02710381,0.01494661,-0.00611563,0.00980411,-0.04812437,0.02002015,0.1040266,0.08677622,-0.06230915,-0.00699143,0.04171824,-0.02210922,0.00224548,-0.09962008,-0.03172598,-0.04050364,0.04280096,0.00057254,0.0269114,0.00177193,-0.08436426,0.0138424,-0.01177564,0.14387691,0.02519192,-0.00071449,-0.04063665,0.00431112,-0.01921539,-0.02120632,0.00764569,0.01184672,0.05083709,-0.02094524,-0.00159352,-0.02970838,-0.03108203,-0.09003189,-0.034435,-0.03574507,-0.08723321,-0.03721984,0.06749727,-0.03822405,0.05871619,0.01745858,0.03722589,0.04078868,-0.04616997,0.04562297,0.03012018,0.0651112,-0.03123135,-0.08107184,-0.01322924,0.01046732,0.05533353,-0.01946316,-0.04575384,0.05800939,-0.01280231,0.04954417,0.03641536,0.01413882,-0.00004336,0.03873164,0.01781742,-0.06113584,0.10855806,-0.02534569,0.03454392,-0.01992297,0.04092655,0.01494324,-0.03621558,-0.00204037,-0.02834743,0.05727828,-0.06262741,0.07223544,0.07164902,0.0419629,0.03496506,0.03113946,0.01555562,0.01824094,-0.01038894,-0.00514428,0.0253246,-0.04450615,0.03819644,0.01897602,-0.03170529,-0.24848457,0.02341519,-0.03435508,0.05401776,0.01161167,-0.02962646,0.02839535,0.03216568,0.0221115,-0.00951495,0.01897036,0.01139084,0.05726952,-0.09205577,-0.04015967,-0.0044152,-0.05894174,-0.03577145,0.05376244,0.02333526,0.06981001,0.04948713,0.27999687,0.00233177,-0.00758529,-0.00061851,0.01546378,0.0260261,-0.03949834,0.03873144,0.02086856,0.00926484,0.0823737,-0.00771012,-0.02893041,-0.03699805,0.01115966,0.01417138,0.01222932,0.0168733,-0.05652814,0.00552224,-0.01318901,0.00428002,0.12439523,0.01008838,-0.03443048,-0.05588085,0.05293498,0.06217262,-0.09003593,-0.03173386,-0.03203306,-0.02502046,0.06900124,0.03910402,-0.01926527,0.01046457,-0.01507702,0.01573728,-0.00889055,0.01947935,0.05016525,0.04251391,-0.02123411],"last_embed":{"hash":"1fs3aeb","tokens":429}}},"text":null,"length":0,"last_read":{"hash":"1fs3aeb","at":1753423578011},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>#{1}","lines":[32,57],"size":4194,"outlinks":[{"title":"_**Software, formulas to calculate lotto odds, <u>hypergeometric distribution probability</u>**_","target":"https://saliu.com/oddslotto.html","line":11},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lottery-sums.gif","line":15},{"title":"Software calculates root sums, fadic addition for correct lottery strategies based on sums.","target":"https://saliu.com/ScreenImgs/lottery-root-sums.gif","line":19},{"title":"This is the best lotto, lottery strategy on sums, sum, even odd, high low.","target":"https://saliu.com/images/lottery-software.gif","line":25}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10005546,-0.03419123,-0.08098167,-0.0290893,-0.04209599,0.02180534,-0.00423296,-0.02817948,0.04057883,-0.01478135,0.06646701,-0.03082043,0.04308962,-0.02437107,-0.02465266,-0.01715145,-0.01228226,-0.04383703,-0.07781178,0.01532278,0.07697404,-0.03104135,-0.05042328,-0.05724095,0.10217176,-0.01539231,-0.05456431,-0.04667379,-0.06222945,-0.24583368,0.01313142,0.05355268,0.06283852,-0.05420027,-0.04467584,-0.05057219,-0.05296895,0.06256271,-0.02777035,-0.02723167,-0.00292541,0.03461466,-0.00753138,-0.01910448,-0.02925839,-0.05501546,-0.02958279,0.01631551,0.01089997,-0.00220192,-0.02941859,-0.00222913,0.01776144,0.07422016,0.02368005,0.04743392,0.0490941,0.06915799,-0.00462813,0.03335845,0.02751812,0.04605863,-0.1122003,0.01069418,0.03386354,0.0204308,-0.02353538,-0.04917034,0.03473546,0.02077242,0.0396474,0.06106769,-0.01980463,0.04911507,0.05867679,-0.04366518,-0.04863796,-0.0554529,-0.03462597,0.02668351,-0.09873908,-0.01538456,-0.0327752,0.0020007,0.06120692,-0.00321077,0.02687994,-0.0248992,0.04176785,-0.07308667,-0.01644475,0.0600417,-0.01393333,0.0210739,0.08261514,-0.00798573,0.0713852,-0.04540484,-0.01580904,0.12030769,-0.00985162,0.00352164,0.03283332,-0.02330243,0.02213484,-0.04811595,-0.05268133,-0.08563815,-0.08007214,-0.00535019,0.05276063,0.01163428,0.08929868,-0.03321763,-0.03755478,-0.00818725,-0.0127675,0.01378503,0.05461083,-0.01977072,0.003418,0.0174483,0.00095078,-0.00290681,0.02985271,0.01378456,0.0235806,0.07829873,0.04777532,0.01353223,0.04528333,-0.02332648,-0.10175808,-0.04336255,0.01007198,-0.03998424,-0.01901418,0.02532786,0.00200259,0.0022445,0.00667978,-0.04291337,0.07294473,-0.10237546,0.01914273,0.07359979,-0.03780777,-0.03377529,0.02595832,0.0107516,0.02296443,-0.01554277,-0.03345096,-0.08500907,-0.04296495,-0.00996698,0.10747623,0.06711318,-0.02989459,0.01851406,-0.01730788,-0.02925544,-0.03206787,0.11203384,0.01700242,-0.08613696,-0.00860985,0.01286493,-0.03866837,-0.04627056,-0.01519001,0.04089629,-0.02191793,0.03164438,0.0732538,-0.03135348,-0.07155123,-0.04697576,0.03192515,-0.0128296,0.00068196,-0.00850664,-0.04571488,-0.0031637,-0.01268765,-0.05263818,-0.020696,0.00382927,0.00915865,0.01958692,-0.08584942,0.01726451,-0.05291685,0.0042084,-0.0389667,-0.02829584,-0.00247811,-0.01598784,0.06439673,-0.00209875,0.03720211,-0.00031974,0.03845866,0.04484737,0.00483092,0.04674374,0.0494503,-0.10653331,0.10503352,0.01037411,-0.02993241,-0.02429747,0.03401531,0.108194,-0.01260514,0.00943197,-0.02817366,0.05727572,-0.00497097,-0.00810848,-0.00106563,-0.0249916,-0.07961493,-0.16914696,-0.04144821,0.011673,0.01579093,0.03252115,0.01003002,0.05064816,-0.05779468,0.04520232,0.04871851,0.08437937,-0.03841378,-0.02812205,0.04595558,-0.0349591,0.03926349,-0.10741769,-0.01071455,-0.02890544,0.09169161,-0.01807662,0.07151904,-0.02700268,-0.06375275,0.03800051,-0.01344999,0.14008845,0.00222271,0.0030002,-0.05465568,0.01670514,-0.03420256,-0.03754808,0.00863543,0.04052534,0.06238241,-0.0291932,-0.01915917,-0.03195185,-0.03905629,-0.10531691,-0.01325922,-0.03237937,-0.09005197,0.01192109,0.0119742,-0.01773328,0.0352449,0.05049163,0.03301195,0.05734158,-0.03614737,0.03196605,0.01724364,0.04226708,0.01201218,-0.08545334,0.00496688,0.01515374,0.05069105,0.03377799,-0.04807484,0.04729474,0.00091752,0.02541382,0.03402292,-0.00710741,0.00170429,0.02432464,-0.0013167,-0.007631,0.09294243,0.0208289,0.03616086,-0.00652454,0.04513124,0.03644755,-0.06059934,0.02376692,-0.02312058,0.02914179,-0.05242046,0.01702437,0.08729381,0.01535151,0.02973908,0.03393892,0.04893045,0.03513755,-0.01393944,-0.00697569,0.01248258,-0.01479165,0.03934203,-0.01468185,-0.03101721,-0.26290235,0.01981982,-0.03998686,0.0379924,0.02826999,-0.01230433,0.034986,0.02213581,0.00181325,-0.01453115,0.06261937,0.0282894,0.04133365,-0.09616901,-0.01101824,-0.00649011,0.00781364,-0.04617441,0.0147742,-0.00513913,0.04446769,0.05260023,0.27510759,-0.00174835,-0.00685727,0.0250959,0.03341852,0.03581938,0.00055666,0.01709694,0.02063323,0.02932609,0.11739142,-0.0125828,-0.02282671,-0.00669895,-0.01724431,-0.04768586,-0.00893365,0.04776362,-0.05851035,-0.00371731,-0.0538973,-0.01296831,0.12521504,-0.00749339,-0.03172042,-0.04048784,0.07944108,0.06033155,-0.08012916,-0.01115273,-0.03758915,-0.05769976,0.05778065,0.02956502,0.00020291,0.00618491,-0.02362096,0.03788279,-0.04508397,0.01149232,0.03659524,0.05546788,-0.01971793],"last_embed":{"hash":"b8yt7h","tokens":497}}},"text":null,"length":0,"last_read":{"hash":"b8yt7h","at":1753423578176},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>","lines":[58,84],"size":3979,"outlinks":[{"title":"This is the best lotto, lottery strategy software by founder of lotto mathematics.","target":"https://saliu.com/images/lotto-software.gif","line":26}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10010339,-0.03548096,-0.08208463,-0.02964541,-0.03967396,0.02205073,-0.00361587,-0.02751997,0.03975216,-0.01432263,0.06705012,-0.03236418,0.04336882,-0.02679455,-0.02517695,-0.01811015,-0.01365087,-0.04609323,-0.07722552,0.01491376,0.07883952,-0.03098386,-0.04817221,-0.05761946,0.10174258,-0.0150967,-0.05349277,-0.04854425,-0.0628792,-0.24444692,0.01350767,0.05363108,0.0638637,-0.05376598,-0.0444527,-0.05072839,-0.05226607,0.06095767,-0.02767858,-0.02754072,-0.00292824,0.03455422,-0.00679849,-0.01977009,-0.02904016,-0.0551918,-0.03057778,0.0147685,0.01218479,-0.00202581,-0.02993849,-0.00069457,0.01811718,0.07335915,0.02297745,0.04502314,0.05059477,0.06951588,-0.00607121,0.03284987,0.02630732,0.04563997,-0.11359991,0.0124562,0.03275591,0.02035054,-0.02245388,-0.05002783,0.03400531,0.02028903,0.0397925,0.06206821,-0.01778552,0.04916101,0.05818449,-0.0433883,-0.04863949,-0.05655804,-0.03314034,0.02876388,-0.09818643,-0.01404245,-0.03324604,0.001105,0.05985162,-0.00561516,0.02737024,-0.02529976,0.04020888,-0.07240968,-0.01650143,0.06055877,-0.01515651,0.02169925,0.08219662,-0.00906271,0.07208414,-0.04413158,-0.01509158,0.1195944,-0.00946591,0.00241594,0.03243951,-0.02242395,0.02372172,-0.04783258,-0.05164822,-0.08627879,-0.08025847,-0.00585227,0.05119952,0.01335619,0.08907733,-0.03309851,-0.03776073,-0.00883196,-0.01212505,0.0129991,0.05552619,-0.01898031,0.00366842,0.01800234,0.00052305,-0.00473495,0.03196663,0.01310613,0.02451716,0.07771984,0.04859268,0.01397493,0.04758812,-0.02451281,-0.10425616,-0.04222265,0.0096376,-0.0415141,-0.01801446,0.02643055,-0.00116555,0.00155484,0.00640283,-0.04298323,0.07227634,-0.10171418,0.01840588,0.07457896,-0.03649522,-0.033536,0.02831168,0.0098709,0.02526173,-0.01344757,-0.03403769,-0.08465819,-0.04378233,-0.00815557,0.10845625,0.06501684,-0.03126116,0.01752224,-0.01681829,-0.02982234,-0.03059937,0.11032058,0.01660686,-0.08528794,-0.00942563,0.01260845,-0.03891768,-0.04578847,-0.01449416,0.04253296,-0.02352228,0.03220668,0.07300996,-0.0320413,-0.07174616,-0.04732127,0.03332316,-0.01259738,0.00175657,-0.00669913,-0.0430981,-0.00463825,-0.01274976,-0.05000329,-0.02161547,0.00372478,0.00841866,0.01922246,-0.0878329,0.01802441,-0.05268587,0.00335715,-0.03935147,-0.02733461,-0.00309216,-0.0170645,0.06533443,-0.00435119,0.03754815,-0.00047203,0.03894791,0.04480833,0.00567862,0.04698827,0.05006727,-0.10658317,0.10295687,0.01101877,-0.03200942,-0.02516161,0.03429026,0.10611313,-0.01217126,0.00810999,-0.02774982,0.05652303,-0.00639077,-0.00683596,-0.00200493,-0.02382149,-0.08073054,-0.16971138,-0.03945555,0.0113859,0.01687098,0.03391431,0.01106032,0.05117421,-0.05805849,0.0467746,0.04662296,0.08593922,-0.03827593,-0.02842234,0.04815122,-0.03490062,0.03907055,-0.10866325,-0.01032223,-0.02867567,0.09149722,-0.01715262,0.07031512,-0.02680038,-0.06168244,0.03864263,-0.01293847,0.13848013,0.00298908,0.00379246,-0.05630668,0.01672516,-0.03494284,-0.03812909,0.00701232,0.04301291,0.06270566,-0.03022714,-0.02036022,-0.03210115,-0.03921882,-0.10407475,-0.01267139,-0.03109541,-0.09040725,0.01330215,0.01125384,-0.01676175,0.03510484,0.05124477,0.03238857,0.05698268,-0.03497852,0.03102694,0.01755238,0.0417675,0.00984404,-0.08604668,0.00553358,0.01434918,0.04854821,0.03497482,-0.04891258,0.04702307,0.00098737,0.02526305,0.03285436,-0.00703842,0.00224407,0.02427468,-0.00183828,-0.00551875,0.09291711,0.02033091,0.034574,-0.00568859,0.04343158,0.03599988,-0.06151536,0.02298993,-0.02596892,0.02673172,-0.05133684,0.0163323,0.08703618,0.01572439,0.03088234,0.03480369,0.04971404,0.03514078,-0.01287601,-0.00588783,0.01267791,-0.01545976,0.03838539,-0.01481004,-0.03025429,-0.26329714,0.01933801,-0.0404837,0.0371833,0.03104202,-0.0117376,0.03480187,0.02121999,0.00180371,-0.01513999,0.06450364,0.02706075,0.04176502,-0.09536394,-0.01070998,-0.00589036,0.00767239,-0.04801575,0.01495146,-0.00470277,0.04530809,0.05251933,0.27518061,-0.00293346,-0.00559287,0.02500181,0.03302038,0.03636182,0.0018435,0.01865296,0.01929197,0.02862111,0.11834818,-0.01184135,-0.02270781,-0.00414889,-0.01803442,-0.04867394,-0.00912435,0.04625416,-0.05723155,-0.00429415,-0.05315804,-0.01220309,0.12458055,-0.00840519,-0.03249699,-0.03933505,0.07952654,0.06200357,-0.07784525,-0.0110925,-0.03878139,-0.05810867,0.0594594,0.02964194,-0.00001232,0.00466591,-0.02405351,0.03894613,-0.04559384,0.01198428,0.03657331,0.05813146,-0.01877901],"last_embed":{"hash":"k5z5mf","tokens":494}}},"text":null,"length":0,"last_read":{"hash":"k5z5mf","at":1753423578379},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>#{1}","lines":[60,84],"size":3880,"outlinks":[{"title":"This is the best lotto, lottery strategy software by founder of lotto mathematics.","target":"https://saliu.com/images/lotto-software.gif","line":24}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10277534,-0.02807201,-0.04643861,-0.04896959,-0.05736406,0.02263453,0.0208497,-0.00596812,0.06700008,-0.01972659,0.0703035,-0.02408898,0.0336524,-0.00146248,-0.04244718,-0.05210837,0.00314506,-0.03167931,-0.06893975,0.00119331,0.10310094,-0.02966463,-0.05479884,-0.07289797,0.10854208,-0.02074202,-0.0704904,-0.04606173,-0.05243287,-0.24903293,0.01635055,0.0468522,0.05286261,-0.03577046,-0.04592642,-0.05454665,-0.04462307,0.06691083,-0.00994706,0.00617413,0.01111646,0.03381389,-0.00868299,-0.02862081,0.00521053,-0.056433,-0.01766581,0.02270753,0.00594096,0.0001832,-0.05525138,0.01836553,0.02723785,0.0554213,0.04685035,0.01566343,0.05971913,0.06828006,0.01351559,0.0352037,0.04435512,0.03617739,-0.13571277,0.01240078,0.04883198,0.0094423,-0.03512293,-0.05361434,0.02107873,0.03596918,0.05222487,0.02532596,-0.03223195,0.04018328,0.05636819,-0.06783597,-0.0225728,-0.05115952,-0.03014548,-0.00622241,-0.0759639,-0.05738649,-0.02884014,-0.01069688,0.05764015,0.02176862,0.06127377,-0.0080517,0.06005848,-0.07480581,0.01600091,0.04316077,0.00376009,0.02343997,0.07093851,0.0037598,0.07728842,-0.02317975,-0.01544651,0.13000646,-0.00577435,0.00060395,-0.01294516,0.01549472,0.03315175,-0.05149954,-0.03805131,-0.06939527,-0.06158701,0.01037177,0.02817029,0.0078819,0.07925654,-0.01316097,-0.0379299,-0.0289644,0.00384132,0.03213767,0.03394548,-0.0054011,-0.00370689,0.02754335,0.00410521,-0.0293545,0.01525939,0.0098898,0.02186373,0.07794618,0.02482602,0.01829695,0.05535281,-0.02106831,-0.08131143,-0.04436084,0.02486046,-0.00571351,-0.03068487,0.05382698,0.00800668,0.00277105,-0.00880139,-0.03783731,0.05751166,-0.11028086,0.00051576,0.06759065,-0.03745281,-0.00266008,0.04711978,-0.00682846,-0.00426726,-0.01715405,-0.04625762,-0.06831168,-0.04720582,-0.02361757,0.06285451,0.09635638,-0.05027728,0.00332659,-0.02901739,-0.04440943,-0.02434552,0.09508325,-0.0024433,-0.06125807,-0.02701253,0.02321102,-0.03079438,-0.04182247,-0.02610148,0.03901181,-0.03936279,0.0262855,0.0956737,-0.04779733,-0.07292753,-0.06987915,0.01056502,-0.00306378,0.02373912,0.00668986,-0.05072604,0.00238091,-0.01537984,-0.05453891,-0.00078154,-0.00261404,0.01265672,0.03967133,-0.0617904,0.01456832,-0.05177415,0.01066718,-0.03401502,-0.02742117,-0.01278311,0.00502977,0.06085472,-0.00784272,0.05161889,0.00816416,0.05980177,0.03893737,0.01491757,0.04974885,0.03488852,-0.08260874,0.11177554,0.00427605,-0.02077901,-0.03907689,0.0200249,0.10503745,0.01082461,0.02159358,-0.01426121,0.05391087,0.01569947,-0.00614222,-0.01322279,-0.00013478,-0.07981595,-0.19523697,-0.02865889,0.00489411,0.00496171,0.02313711,-0.00443844,0.03595512,-0.03662545,0.06181494,0.0613375,0.09244487,-0.06675836,-0.03604898,0.03503953,-0.02662756,0.02889748,-0.09805811,-0.01151854,-0.01920685,0.07365477,-0.00042227,0.05916322,-0.03446352,-0.07101561,0.01439038,0.00725004,0.12937108,-0.00482483,-0.00544991,-0.06273783,0.00597047,-0.03300997,-0.03507675,0.03502084,0.02253885,0.07163417,-0.04151719,-0.02123635,-0.04503721,-0.0361018,-0.11487201,-0.01395535,-0.02701358,-0.06930874,-0.03612824,0.03543456,-0.00505403,0.03087906,0.02724297,0.03899408,0.05140773,-0.02476627,0.02599198,0.01830509,0.05396004,-0.00278884,-0.08999312,-0.01728719,0.01819061,0.06083684,0.00475198,-0.06324086,0.05740795,-0.00511303,0.00705632,0.02896419,0.00629405,-0.01225311,0.03168676,0.00255947,-0.00344164,0.09500904,0.00868346,0.02985747,-0.01705863,0.03705423,0.06239298,-0.0058748,0.00901087,-0.00680658,0.01676357,-0.06233341,0.04003531,0.0618233,0.02249034,0.02109632,0.03655315,0.02159535,0.00853473,-0.00018698,0.01743143,0.0269563,-0.04730126,0.02949219,-0.02676517,-0.01389901,-0.27787542,0.01768974,-0.04793181,0.0382742,0.00736917,-0.01050503,0.01539898,0.04298059,0.00910027,0.00395111,0.06548639,0.02669553,0.04697322,-0.09748614,-0.04017372,-0.02955191,-0.02726123,-0.03992468,0.02823167,-0.00088769,0.03946568,0.04799685,0.25490153,0.00425162,-0.00945221,0.0176871,0.01344792,0.03248854,-0.02352744,0.02362594,0.01556282,0.02754569,0.12165617,0.00641705,-0.01636062,-0.00010191,-0.02356268,-0.03739528,0.02153131,0.049052,-0.05980285,0.00012725,-0.05534168,0.01019929,0.13676624,0.00682011,-0.03615844,-0.0411385,0.08728949,0.07263234,-0.08046982,-0.03378812,-0.03578503,-0.03446613,0.05185318,0.02962872,-0.02144902,0.03944277,-0.05172236,0.01086326,-0.03089019,0.00004343,0.05559009,0.03732637,-0.01045766],"last_embed":{"hash":"dhsfaa","tokens":485}}},"text":null,"length":0,"last_read":{"hash":"dhsfaa","at":1753423578585},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>","lines":[85,139],"size":5349,"outlinks":[{"title":"Best lottery software for odd even, low high, sums, sum-totals, root sums, number increase decrease.","target":"https://saliu.com/ScreenImgs/lottery-increase.gif","line":27},{"title":"The lotto skips are true filters for effective lottery strategies, systems.","target":"https://saliu.com/ScreenImgs/lottery-skips-filters.gif","line":31},{"title":"_**Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player**_","target":"https://saliu.com/bbs/messages/188.html","line":48},{"title":"This lottery strategy is static because it always generates the same lotto combinations.","target":"https://saliu.com/images/lottery-software.gif","line":54}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09970553,-0.03173458,-0.04846669,-0.04707033,-0.05398479,0.01942058,0.02522427,-0.00996193,0.06401352,-0.02000099,0.07284465,-0.02551202,0.03214278,-0.0043977,-0.04129227,-0.05084674,-0.00064947,-0.0282504,-0.06915188,-0.00133162,0.10309444,-0.02852414,-0.05735976,-0.07026614,0.10395402,-0.01388518,-0.06802143,-0.04338184,-0.05294723,-0.24782954,0.01674249,0.04671818,0.05506049,-0.03597578,-0.04436184,-0.05792176,-0.04574236,0.07003012,-0.01381946,0.00561475,0.01089782,0.03413468,-0.0075215,-0.02918068,0.00315434,-0.05468161,-0.02244892,0.02386575,0.00448672,-0.00190825,-0.05790791,0.02230437,0.02993157,0.05497919,0.04440589,0.01703733,0.05908576,0.06564583,0.01574275,0.03638491,0.04910603,0.03566941,-0.13326326,0.01420275,0.0438869,0.00668584,-0.03609689,-0.05199776,0.01975174,0.03901836,0.05336453,0.02238338,-0.0329868,0.04530028,0.05673075,-0.06595904,-0.02340823,-0.05665721,-0.0348504,-0.00312,-0.07840746,-0.05287674,-0.02660763,-0.00939736,0.05172554,0.02132686,0.0590332,-0.00667677,0.05338392,-0.06999033,0.01426679,0.04446654,-0.00089795,0.02229064,0.06782886,-0.00198023,0.07697001,-0.02056351,-0.01850878,0.12989248,-0.00800935,0.00071293,-0.00884605,0.01625388,0.03268682,-0.04922893,-0.03700089,-0.07170656,-0.068386,0.00954516,0.02711646,0.0081853,0.08131608,-0.01112888,-0.03824878,-0.02628801,0.00255734,0.02977247,0.03955899,-0.0064351,-0.00416884,0.02819461,-0.00164036,-0.02783122,0.01424469,0.00786203,0.0223561,0.078268,0.02530613,0.01464331,0.05348093,-0.01916715,-0.08269191,-0.04272917,0.02327571,-0.00567678,-0.03210985,0.05269201,0.00384173,-0.00347172,-0.00598879,-0.03972543,0.06069706,-0.10847165,-0.00271406,0.06578997,-0.03729984,-0.00348937,0.04333431,-0.00566571,-0.00260912,-0.01352684,-0.04659731,-0.06596873,-0.04416792,-0.02349286,0.06220563,0.09521476,-0.05231372,0.00723416,-0.02918958,-0.042817,-0.02448861,0.09864008,-0.00612768,-0.06225985,-0.02670838,0.02358403,-0.03501686,-0.03928947,-0.02444659,0.04390673,-0.04541564,0.02697279,0.09154756,-0.04470812,-0.07584409,-0.07042222,0.01139054,0.00066856,0.02784671,0.00340102,-0.0467318,-0.00051693,-0.0147742,-0.05509018,0.00044011,0.00056779,0.01249364,0.04056233,-0.05552625,0.01928511,-0.04916834,0.00958119,-0.037003,-0.03178318,-0.01546508,0.00395138,0.06016228,-0.01459694,0.05693268,0.00892559,0.05841105,0.04249947,0.01056216,0.04780686,0.04012497,-0.08444145,0.1117776,0.00633419,-0.02373523,-0.03843807,0.01809243,0.1025048,0.01524263,0.02019913,-0.01490244,0.05324703,0.01448377,-0.00546312,-0.01272402,-0.00348296,-0.07900652,-0.19605683,-0.02640004,0.00494311,0.0072917,0.02582312,0.00265637,0.03545417,-0.03949628,0.06042688,0.06024541,0.09453052,-0.06530774,-0.03812519,0.03743725,-0.02576182,0.03437424,-0.09770592,-0.01220842,-0.02324298,0.07295694,0.00058615,0.05511979,-0.04368789,-0.06803483,0.01813895,0.01064461,0.13161553,-0.00228469,-0.00502402,-0.06612994,0.00648652,-0.03870237,-0.0345248,0.03800533,0.0267591,0.07401106,-0.03748019,-0.0224463,-0.03732575,-0.03630724,-0.11666126,-0.01767891,-0.02729659,-0.06525873,-0.03560946,0.03169362,-0.0060212,0.03224721,0.02644447,0.03872826,0.0531104,-0.02881869,0.02286703,0.02025138,0.05240152,-0.00083188,-0.08839353,-0.0126252,0.02058542,0.05944669,0.00789411,-0.06619167,0.05822655,-0.00792048,0.00248232,0.02553546,0.0061809,-0.00895711,0.03505062,0.00076779,-0.00039077,0.09273683,0.00903033,0.02622838,-0.01360798,0.03559605,0.06537675,-0.0080168,0.01493726,-0.00521076,0.0185552,-0.05954364,0.04338086,0.06382704,0.02363662,0.02138714,0.03529924,0.02301296,0.01171712,0.0015725,0.01803281,0.02645928,-0.04432809,0.02296895,-0.02539071,-0.01749585,-0.27889419,0.01761257,-0.0457814,0.04289498,0.00864851,-0.01119201,0.01479844,0.04398073,0.00779472,0.00203198,0.06695774,0.02436523,0.050296,-0.10071978,-0.03986992,-0.03154308,-0.02081029,-0.04399599,0.02775305,0.00208089,0.03681755,0.05036165,0.25631431,0.00029956,-0.00979526,0.02041949,0.01014225,0.03346275,-0.02803327,0.02458526,0.01665517,0.03089083,0.122922,0.00728018,-0.01327285,-0.00008451,-0.02122512,-0.03712937,0.02181988,0.04631276,-0.06189043,0.00203204,-0.0566265,0.00638305,0.13400649,0.01581985,-0.03590579,-0.04065287,0.07886247,0.07329623,-0.0807375,-0.03306057,-0.04144397,-0.04029613,0.04990616,0.03098064,-0.01921877,0.03633025,-0.05407818,0.01246347,-0.03013258,-0.00242409,0.05894822,0.03728966,-0.01089486],"last_embed":{"hash":"1nd25hw","tokens":453}}},"text":null,"length":0,"last_read":{"hash":"1nd25hw","at":1753423578778},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{1}","lines":[87,120],"size":2100,"outlinks":[{"title":"Best lottery software for odd even, low high, sums, sum-totals, root sums, number increase decrease.","target":"https://saliu.com/ScreenImgs/lottery-increase.gif","line":25},{"title":"The lotto skips are true filters for effective lottery strategies, systems.","target":"https://saliu.com/ScreenImgs/lottery-skips-filters.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13059698,-0.0440453,-0.04389223,-0.0542171,-0.03844522,-0.0272111,-0.00779845,-0.01308478,0.0710664,-0.00250392,0.03792185,0.03244848,0.0521891,-0.01160604,-0.0151209,-0.02751791,0.00144372,-0.03412567,-0.02387561,0.024577,0.0805897,-0.02362338,-0.0863748,-0.06242811,0.09678984,-0.00279127,-0.06481686,-0.03623834,-0.02923576,-0.24582167,0.02786489,0.03166712,0.01464418,-0.05381062,-0.00225257,-0.05731419,-0.03754656,0.01802154,-0.01108612,0.01042683,-0.01009955,0.03896523,-0.00855235,-0.00589551,-0.00174907,-0.06069268,-0.01322069,0.03432875,-0.0169095,0.00817794,-0.0540413,0.02864543,0.01180362,0.03290598,0.02466526,0.00509458,0.02684447,0.11587384,-0.00034376,0.05768064,0.03806913,0.04679587,-0.10567877,0.01843924,0.05605149,0.05528022,-0.03464086,-0.04270068,0.03553849,0.04370713,0.05045687,0.02536783,-0.02477751,0.0691442,0.0618195,-0.04149406,-0.01657479,-0.02446701,-0.04057458,-0.00375833,-0.06369096,-0.06647819,-0.02892041,0.00996434,0.00633948,0.02673363,0.04825856,-0.0029672,0.08357128,-0.09289498,0.01269908,0.03883418,0.0614554,0.03130184,0.07638428,0.00679532,0.05431897,-0.00965449,-0.01841647,0.10248741,0.01277151,-0.01197956,-0.02377892,-0.00544955,0.04662705,-0.05120626,-0.05811205,-0.05771774,-0.04828906,-0.00203459,0.0599199,0.02307598,0.11212979,-0.03672309,-0.04185664,-0.02392733,-0.02239312,0.00900044,0.03424743,-0.01736234,-0.06697267,0.02786417,0.00195385,0.00913674,-0.00374111,0.00643996,-0.01234938,0.0728097,-0.00906566,0.01194437,0.03354791,-0.04250342,-0.06711084,-0.04663037,0.00802464,-0.04292041,-0.04888867,0.03714082,0.02308962,0.02304772,-0.04484086,-0.0285713,0.04680628,-0.08691386,-0.00680847,0.03462184,0.00295079,0.00561097,0.03142175,0.01316378,-0.00020413,0.0000768,-0.04157404,-0.07487614,-0.01147734,-0.03876541,0.09969136,0.08844437,-0.05981139,-0.00458885,-0.03594449,-0.00458862,-0.00809079,0.11967859,-0.00031256,-0.07909665,-0.00749138,0.04395796,-0.03495473,-0.04769227,-0.02699809,0.00787515,-0.03710758,0.01045921,0.09094357,-0.05954022,-0.1042331,-0.04700708,0.02113977,0.00382943,0.01404174,-0.01195179,-0.03645062,0.0095718,-0.01780867,-0.08674308,-0.03046746,0.00028009,-0.00077566,0.03967929,-0.08126058,0.01409895,-0.05374746,0.00535566,-0.03375079,-0.0379554,-0.0195871,-0.00670543,0.08199453,-0.01231947,0.00000682,0.00221671,0.04726255,0.01527822,-0.00658686,0.05440592,0.02605879,-0.10112622,0.08792007,0.01229502,-0.02817522,-0.0024345,0.00769231,0.10558231,-0.01351386,0.03309199,0.01605291,0.08987987,-0.01875483,0.00124853,-0.0375273,0.03924417,-0.05539041,-0.19176321,-0.0540749,-0.04220154,-0.01483188,0.00311876,-0.00730889,0.04522496,-0.05660353,0.01041345,0.09353594,0.0812176,-0.07266881,-0.0312577,0.04794692,-0.01092666,0.00247323,-0.09485769,-0.01510666,-0.05155404,0.04663633,0.00754074,0.02610334,-0.03348598,-0.09497674,0.02881176,0.02265184,0.15004285,0.03563803,0.03601491,-0.02863011,0.01734794,-0.0302954,-0.00982942,-0.04237583,0.01616741,0.07240033,0.01878993,-0.02706947,-0.00640207,-0.02687332,-0.1177417,-0.01037496,-0.00657055,-0.09305608,-0.04855934,0.03620176,-0.01451608,0.03530385,0.01629176,0.07820372,0.03598072,0.01127531,0.02527749,0.03363178,0.05741248,-0.00105052,-0.08286788,0.02841073,0.02822252,0.07003913,0.00899206,-0.06938203,0.05204329,-0.0161489,0.02551849,0.0003699,0.03162887,-0.03140128,0.07067543,-0.00396511,-0.03596336,0.14148273,-0.00625396,0.04038709,-0.00828817,0.04584883,0.05583579,-0.03749568,0.01039088,0.00641079,-0.01720773,-0.01969049,0.05419356,0.07463366,0.0087065,0.00124984,0.03904778,0.00639803,-0.00901266,0.00060819,-0.02759359,0.02876597,-0.05039865,0.01305633,0.01549502,0.00834196,-0.23704679,-0.00021928,-0.0115961,0.05868448,0.00929711,-0.04223951,0.01727049,-0.01917812,0.00748296,0.0088322,0.02582292,0.02564362,0.02712991,-0.09934468,0.01779354,-0.02187603,0.00627062,-0.01041043,0.05251735,-0.00338908,0.03943716,0.08308369,0.26030561,0.00742595,-0.02648968,0.03640374,0.00901556,0.0601069,-0.01628866,0.00678832,0.00066966,0.01522212,0.05429478,-0.01576961,-0.02024414,-0.03387763,-0.00035155,-0.01392664,0.00667904,0.03967005,-0.07099921,0.02044897,-0.0359451,-0.00167234,0.11647549,0.00325184,-0.03783502,-0.05663739,0.06073207,0.07130437,-0.08037707,-0.05074269,-0.022332,-0.04165709,0.06170586,0.02354071,-0.01825118,0.03548965,-0.03370257,0.0043562,-0.01254288,0.01876207,0.04463852,0.07967477,-0.02612476],"last_embed":{"hash":"z9mli9","tokens":461}}},"text":null,"length":0,"last_read":{"hash":"z9mli9","at":1753423578955},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>#{8}","lines":[128,139],"size":2553,"outlinks":[{"title":"_**Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player**_","target":"https://saliu.com/bbs/messages/188.html","line":5},{"title":"This lottery strategy is static because it always generates the same lotto combinations.","target":"https://saliu.com/images/lottery-software.gif","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06891035,-0.05617495,-0.04377813,-0.02416379,-0.00564118,0.0015788,-0.00067906,-0.0098592,0.03707112,-0.01610506,0.02955297,-0.03895779,0.06124217,-0.01572366,-0.02427522,0.00909607,0.00328199,-0.02873246,-0.07504529,-0.01006708,0.08388004,-0.03078437,-0.08955516,-0.06787436,0.09218618,-0.02217709,-0.01232668,-0.06657747,-0.05067818,-0.2642813,0.01768257,0.02221731,-0.02224362,-0.07115607,-0.05758855,-0.03707169,-0.01670163,0.08973783,-0.03128486,0.02666952,0.03324417,0.00308657,0.00018206,-0.03044411,-0.02372727,-0.02352593,-0.016622,-0.00185076,0.02221001,0.0068377,-0.02191156,-0.00496352,0.04190365,0.00971551,-0.02777101,0.00911696,0.03629199,0.10643363,0.049783,0.03914367,-0.03164242,0.06329349,-0.14982373,0.01638141,-0.01056023,0.02789011,-0.00636284,-0.01660998,0.01352853,0.05232769,0.03185774,0.10140304,-0.01910181,0.0544063,0.02894768,-0.05483726,-0.01164992,-0.05089638,-0.06449878,0.04699595,-0.07795531,-0.05089854,-0.02258006,0.01072657,0.04678863,0.02477428,0.04425209,-0.01541398,0.04214186,-0.02594987,0.00587579,0.03092954,0.02646078,0.03611259,0.04284651,0.02102499,0.0629674,-0.02549558,0.03632308,0.10016556,0.02742978,0.03318216,0.02180314,-0.02145121,0.02286712,-0.06111285,-0.0195205,-0.01574193,-0.02974678,0.01716731,0.02579489,0.01126573,0.09130744,-0.04678326,-0.02056671,0.02620215,0.01624659,-0.01573233,0.02867562,-0.0143253,-0.04312472,0.00762571,-0.00179296,-0.01584483,0.04749141,0.04833828,0.00610382,0.08298787,0.06522916,0.03022712,0.02256081,-0.02925029,-0.11082124,-0.03923811,-0.0086675,0.01161983,-0.00724066,0.0179582,0.00036868,0.02593848,-0.05348926,-0.04040765,0.04537499,-0.07711095,-0.01159169,0.1014518,-0.03402677,-0.01109574,0.00167134,-0.07168736,0.00894296,0.00698289,-0.05329617,-0.04754847,-0.00758761,-0.02736491,0.07435182,0.09262089,-0.03837856,-0.01046361,-0.05148131,-0.04115903,-0.00995324,0.07544284,-0.01208633,-0.05504138,-0.03922354,0.01981486,-0.05027086,-0.0524974,-0.01582781,-0.02996781,-0.04122306,0.03330619,0.06638432,-0.02560147,-0.07419668,-0.0143053,-0.02389566,-0.01739459,-0.02272907,-0.01915385,-0.04258833,-0.00676335,-0.03984497,-0.07868911,0.00025948,-0.00922126,0.00881466,0.04085959,-0.10249107,-0.00756637,-0.04045768,0.02908085,-0.00318993,-0.02898021,0.03725266,-0.00403513,0.03411332,-0.04393238,0.03787511,-0.0114853,0.03166623,0.01978672,-0.01692878,0.05689609,-0.01313297,-0.08158201,0.16083091,0.01139945,-0.02825397,-0.03753668,0.03163362,0.10566919,-0.04204473,0.02615022,0.01117997,0.03695003,-0.00750628,0.00389736,0.0037156,0.03665708,-0.10235339,-0.1878387,-0.0404562,0.00649355,0.04484953,0.03650783,-0.01375738,0.01128892,-0.02160921,0.02718622,0.13650475,0.0807305,-0.04713107,-0.0318133,0.04796123,-0.02478277,0.00282342,-0.11480831,-0.01963619,-0.060223,0.03696627,0.02139274,0.05559101,-0.00283736,-0.04684279,0.0377736,-0.03105346,0.16317798,-0.01646323,0.00767321,-0.02366525,0.0287901,0.01272226,-0.03132608,0.03222811,0.01888689,0.05001044,-0.03334362,0.0128282,-0.02328366,-0.03805529,-0.04689765,-0.02240349,-0.03989066,-0.11242263,-0.01694599,0.04968255,-0.03046406,0.0667664,0.0124665,0.04282678,0.05582388,-0.04851734,0.07497938,-0.00403061,0.07882921,-0.04187777,-0.10347254,-0.02965412,0.01298322,0.06057573,0.0122072,-0.03498012,0.0433449,-0.01124513,0.06148538,0.03131902,0.01018119,-0.00495255,0.01013139,0.02952189,-0.02647931,0.06486811,-0.02531712,0.06050018,-0.02921903,0.04295072,0.02143567,-0.0489358,-0.00199891,-0.06994417,0.08836054,-0.05036816,0.03768841,0.06698614,0.01982473,0.01654733,0.0488676,0.04103018,0.04075861,-0.01098342,-0.04068993,0.03704202,-0.04229904,0.04310744,0.01835016,-0.00674847,-0.25663278,-0.00896168,-0.04956416,0.0501196,0.00998331,-0.05903882,0.03331473,0.02760934,0.00214847,0.00135857,-0.00228201,0.04084256,0.05138118,-0.07400048,-0.01735322,-0.00767403,-0.04233416,-0.0071363,0.06857226,0.00721529,0.07510908,0.03435633,0.25345528,0.00573711,-0.01801052,0.02772887,0.02197669,-0.01511404,-0.0172286,0.0214335,0.02607503,-0.00292188,0.09650863,-0.01242979,0.00199186,0.00016901,-0.01166012,0.01630584,0.00289317,0.0221158,-0.05237574,0.01705914,-0.02758148,-0.00914626,0.09923825,-0.04107361,-0.04292585,-0.05535063,0.04554305,0.06400672,-0.07458912,0.00405925,0.00728862,-0.05753414,0.03993712,0.0190026,-0.00037128,0.0400628,-0.01180636,0.02100372,-0.00820587,0.01238561,0.02951186,0.05271622,-0.04026519],"last_embed":{"hash":"110wwgu","tokens":479}}},"text":null,"length":0,"last_read":{"hash":"110wwgu","at":1753423579140},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>","lines":[140,163],"size":4368,"outlinks":[{"title":"_**Winning Gambling Combinations: Inside Median Bell (Gauss)**_","target":"https://saliu.com/random-picks.html","line":5},{"title":"Resources, links to the best in lotto software, lottery strategies.","target":"https://saliu.com/HLINE.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06717668,-0.05712083,-0.0436693,-0.02139737,-0.00366324,0.00312576,-0.00162825,-0.00922186,0.03471822,-0.01510039,0.02858659,-0.03818199,0.0633214,-0.01739527,-0.02358177,0.0116542,0.00177415,-0.02921213,-0.07623751,-0.00985346,0.08681647,-0.02968737,-0.08796772,-0.06581509,0.09216443,-0.01910901,-0.00964048,-0.06785223,-0.05118928,-0.26424092,0.01742646,0.02118693,-0.02419345,-0.07164995,-0.05834472,-0.03633345,-0.01712751,0.08834167,-0.03400188,0.02772332,0.0337432,0.0044714,0.00273198,-0.03287116,-0.02548814,-0.02301594,-0.01647316,-0.00190855,0.02172651,0.00829885,-0.02111218,-0.00409394,0.0406788,0.01025046,-0.02956967,0.00754468,0.03646162,0.10652486,0.0517392,0.04076021,-0.03090777,0.06518207,-0.15160029,0.01661936,-0.01568572,0.02698691,-0.00839683,-0.0193322,0.01498831,0.05040823,0.03113023,0.10145206,-0.01826258,0.05327558,0.0269849,-0.05317285,-0.01231373,-0.05129661,-0.06326383,0.05127637,-0.0779241,-0.04701329,-0.02055358,0.01115226,0.04498918,0.02344584,0.04579643,-0.0145153,0.03993191,-0.02462066,0.00508346,0.02929612,0.02296672,0.03597863,0.04110375,0.0216688,0.06233246,-0.02590902,0.03585448,0.10057754,0.02700479,0.03392673,0.0207132,-0.02118771,0.02192386,-0.0614361,-0.01645739,-0.01597001,-0.02746855,0.01679024,0.0265055,0.01138413,0.0930575,-0.04802027,-0.02086046,0.02693727,0.01600646,-0.02070143,0.03145359,-0.01417981,-0.04259951,0.00636973,-0.00095462,-0.01576012,0.04760246,0.05015511,0.00678572,0.08370526,0.06567815,0.02831575,0.02335254,-0.03011151,-0.11164817,-0.03762896,-0.00968191,0.01196916,-0.00910104,0.01629817,-0.00241068,0.02533443,-0.05350646,-0.03833302,0.0468761,-0.07603297,-0.01236084,0.10336208,-0.03314235,-0.00969046,0.00074514,-0.07283504,0.00736021,0.00855985,-0.05467691,-0.04776286,-0.00528327,-0.02533714,0.07264496,0.09254165,-0.03928011,-0.01070925,-0.05234684,-0.04157572,-0.00846131,0.07349334,-0.01407856,-0.05114241,-0.03671887,0.02094685,-0.05211048,-0.05265381,-0.01711082,-0.02991647,-0.04219408,0.03160411,0.0656769,-0.02502204,-0.07542694,-0.01346055,-0.0253957,-0.01845028,-0.02414648,-0.01931723,-0.04118459,-0.0064977,-0.04158008,-0.07661201,-0.00026957,-0.00884957,0.00840396,0.04105328,-0.10360597,-0.006738,-0.03950153,0.03041715,-0.00338399,-0.0274036,0.03688852,-0.00469573,0.03334438,-0.04394113,0.03937753,-0.00997732,0.0296699,0.01934869,-0.01666173,0.05606585,-0.01319752,-0.08050206,0.16143742,0.01309015,-0.0310727,-0.03665296,0.03172745,0.10396904,-0.04292861,0.02405452,0.01124735,0.03507708,-0.00838436,0.00259898,0.00496397,0.03806281,-0.10332545,-0.18849155,-0.03940739,0.00597502,0.04405931,0.03517933,-0.01441492,0.00995328,-0.02062479,0.02783591,0.1374819,0.07969291,-0.04734844,-0.0341107,0.04986681,-0.02452985,0.00255019,-0.11433216,-0.01861847,-0.06169871,0.03578793,0.02140578,0.05592274,-0.00178274,-0.04539286,0.0382889,-0.03035765,0.16249779,-0.01797216,0.00786352,-0.0246519,0.03183489,0.01403747,-0.03281563,0.03225342,0.02007269,0.05085215,-0.03678951,0.01197684,-0.02181637,-0.03699236,-0.04296095,-0.0223439,-0.0393353,-0.11325048,-0.01729684,0.04748138,-0.02877437,0.06997649,0.01303589,0.04275427,0.05412193,-0.05032643,0.07319805,-0.00005393,0.07737793,-0.04542448,-0.1040211,-0.03097357,0.01100647,0.05968366,0.01471795,-0.03585401,0.0404155,-0.01237663,0.0633209,0.03007872,0.00989554,-0.00403374,0.00742602,0.03039267,-0.0243048,0.06456695,-0.02614886,0.0595399,-0.02941405,0.04207063,0.02289004,-0.05274688,-0.00221501,-0.07204669,0.08946521,-0.04697402,0.03583216,0.06678031,0.02164266,0.01542782,0.05254855,0.04254126,0.04009495,-0.00939441,-0.04190829,0.03672773,-0.04252847,0.04179811,0.02094265,-0.00914309,-0.25654662,-0.00899533,-0.04797009,0.04985929,0.01173782,-0.05821702,0.03488802,0.0286663,-0.00083286,-0.00313746,-0.00322166,0.04204594,0.05113658,-0.07144528,-0.01560679,-0.0080546,-0.04275018,-0.00515765,0.06959809,0.00716997,0.07434039,0.03335796,0.25227252,0.0028184,-0.01809699,0.0279047,0.02229995,-0.01646526,-0.01762848,0.02227184,0.0272291,-0.00403485,0.09786719,-0.01206679,0.00232804,0.00107034,-0.0115437,0.01893546,0.00284001,0.02121269,-0.04999216,0.01806469,-0.02919278,-0.00955398,0.09881444,-0.04053536,-0.04307373,-0.05432706,0.0433629,0.06406914,-0.07329626,0.00514914,0.00772904,-0.05722746,0.03954448,0.01748937,0.00043972,0.03890267,-0.01068518,0.02122937,-0.0086068,0.01174585,0.02915487,0.05417299,-0.0390412],"last_embed":{"hash":"maab04","tokens":479}}},"text":null,"length":0,"last_read":{"hash":"maab04","at":1753423579333},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#<u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>#{1}","lines":[142,163],"size":4268,"outlinks":[{"title":"_**Winning Gambling Combinations: Inside Median Bell (Gauss)**_","target":"https://saliu.com/random-picks.html","line":3},{"title":"Resources, links to the best in lotto software, lottery strategies.","target":"https://saliu.com/HLINE.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12715179,-0.01456308,-0.04375576,-0.04204501,-0.05518125,0.01750686,-0.00921306,0.00281132,0.05971077,-0.03028605,0.0530387,-0.02381421,0.03388284,-0.02706605,-0.03747586,-0.04806568,0.01144742,0.00107678,-0.03751155,-0.00399135,0.08252587,-0.03249444,-0.07951472,-0.09202928,0.09745786,-0.01158045,-0.05759541,-0.02376241,-0.01573322,-0.2372497,0.00978117,0.02807585,0.01740379,-0.04907259,-0.03660356,-0.04047489,-0.03404431,0.05186106,-0.0177606,0.01444004,0.0240484,0.01350074,-0.00809009,0.00808382,0.03625715,-0.0450878,0.01800596,0.0336986,0.01871484,-0.00353245,-0.08971591,0.01273703,0.00969663,0.01292678,0.04981457,0.03159162,0.06358057,0.11011247,0.00934612,0.05101023,0.0180534,0.06331327,-0.16479069,0.05018876,0.0557534,0.02875594,-0.00468917,-0.02086188,0.03769789,0.0291669,0.01134862,0.02744201,-0.0096179,0.0641066,0.06075565,-0.03644009,-0.05060768,-0.07015257,-0.02578249,0.01254655,-0.05717524,-0.0124208,-0.0120977,0.01117398,0.01669409,0.05393828,0.04601122,0.00494547,0.08733456,-0.09785144,0.00046495,0.0448821,0.05318385,0.02176835,0.06182759,-0.00889045,0.06765568,-0.02876276,-0.00157461,0.12257421,-0.00230852,-0.00486635,0.02587206,0.00392817,0.02984467,-0.05841496,-0.02830533,-0.05027898,-0.02379193,0.0071991,0.04743901,-0.00689415,0.04788767,-0.03963209,-0.02683935,-0.02870027,-0.00256825,0.00452474,0.02345151,-0.01656724,-0.04283902,-0.00389277,0.01833207,0.00072916,-0.00213885,-0.00502646,-0.00642815,0.07455165,-0.00374469,0.0193433,0.04992782,0.00821586,-0.09450752,-0.06960654,0.01019492,-0.03071054,0.01206138,0.00378119,0.03059521,0.03538529,-0.02155448,-0.054292,0.03204303,-0.12828156,-0.03318323,0.07684313,-0.0097081,-0.01595105,0.0139675,0.0090642,0.013546,-0.03443594,-0.01311276,-0.0712852,-0.01497695,-0.01854259,0.10478137,0.08826181,-0.0260513,0.00061017,-0.06330923,-0.044364,-0.03729381,0.13216498,-0.00952685,-0.10647153,-0.02373165,0.03235603,-0.02522423,-0.08473393,-0.00985922,-0.01265597,-0.05851099,0.04070393,0.12305761,-0.05103252,-0.05859983,-0.06554274,-0.01913397,0.00553251,-0.0085326,-0.05458883,-0.0379353,0.02288021,-0.04036361,-0.05990999,-0.03041296,-0.01203885,0.00945645,0.04648112,-0.06235423,0.0102857,-0.05880031,-0.00364615,-0.05624897,-0.02888341,-0.02469532,-0.03140084,0.08010111,-0.03546029,0.01688352,0.00995763,0.02245189,0.0372217,0.0119575,0.03143925,0.00085997,-0.0582769,0.10699867,0.01882863,-0.01755023,-0.00352618,0.01523433,0.08183999,0.01087248,0.04293855,0.01326418,0.06027857,-0.02614411,0.01531899,-0.00454743,0.00805297,-0.09891012,-0.18627885,-0.04184092,-0.02801034,-0.00791042,-0.00090913,-0.00679066,0.05698122,-0.04910656,0.0181208,0.06065312,0.0811903,-0.05141308,-0.00148546,0.04327606,-0.01149133,0.00191778,-0.10051016,-0.02536743,-0.03002329,0.05874157,0.01168811,0.02214091,-0.00877864,-0.08454253,-0.00790307,-0.01120892,0.12456642,-0.00994339,0.02954249,-0.03228593,0.02187693,-0.01950083,-0.02394057,-0.01315737,0.01614789,0.06825608,-0.02401772,-0.0090671,-0.03364068,-0.02927552,-0.09939869,-0.00698258,-0.00285275,-0.0626432,-0.0576015,0.03848846,0.01281449,0.01524033,0.02530435,0.05002015,0.03251852,0.0158144,0.03969512,0.0244817,0.04305496,-0.01716877,-0.06130844,0.00336622,-0.01149143,0.07915298,-0.04137115,-0.05710313,0.037467,-0.00060349,0.04274846,0.01918238,0.00977127,-0.02456905,0.0375794,0.01392325,-0.02155992,0.09909023,0.01602439,0.06059258,-0.00358906,0.04195015,0.07845504,-0.04126439,-0.0005162,0.00987118,-0.02243732,-0.06804869,0.07578154,0.07743493,0.03466975,0.04683758,0.05192556,0.00591432,0.00504556,-0.01231878,0.00990117,-0.01309466,-0.02968436,0.03722053,-0.00478267,0.0215748,-0.28444895,0.01803338,-0.0226715,0.03090097,-0.00420889,-0.01858762,0.0359021,-0.02097312,0.01121857,-0.00854797,0.05878243,0.00540882,0.04032955,-0.07186853,-0.0259571,-0.0310293,0.00384769,-0.02876336,0.05345421,0.02323254,0.0264694,0.05431017,0.27508023,0.01778139,-0.01060314,0.03041934,0.0089104,0.02511658,-0.01133013,0.02525037,0.03887283,0.03779258,0.05465777,0.00489784,-0.03640435,0.01979156,-0.03897271,0.00991715,0.01890078,0.05220056,-0.06169007,0.00314803,-0.02683801,0.01294267,0.11651715,0.00772665,-0.02676216,-0.0752667,0.06525368,0.04283707,-0.06162903,-0.03532626,-0.05225489,-0.00962083,0.05841281,0.02069241,0.00276679,0.0195422,-0.01128534,-0.00373494,0.0065587,0.00919324,0.05166239,0.05408714,-0.01490906],"last_embed":{"hash":"f66py","tokens":383}}},"text":null,"length":0,"last_read":{"hash":"f66py","at":1753423579508},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling","lines":[164,195],"size":3735,"outlinks":[{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":5},{"title":"_**Lottery Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":6},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":8},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":10},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":12},{"title":"_**Basic Manual for Lotto Software, Lottery Software**_","target":"https://saliu.com/bbs/messages/818.html","line":13},{"title":"_**Sum-Totals for Lottery, Lotto Games — Pick 3 4 Lotteries, Lotto 5, 6, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/forum/lottery-sums.html","line":14},{"title":"_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_","target":"https://saliu.com/lotto-groups.html","line":15},{"title":"_**Lottery Software Sum-Totals, Sums: Lotto, Powerball, Mega Millions, Euromillions**_","target":"https://saliu.com/bbs/messages/626.html","line":16},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":17},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":18},{"title":"**Lottery Utility Software**","target":"https://saliu.com/lottery-utility.html","line":19},{"title":"_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":20},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":21},{"title":"_**Lotto Software for Groups of Numbers**_","target":"https://forums.saliu.com/lotto-software-odd-even-low-high.html","line":22},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":23},{"title":"_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_","target":"https://saliu.com/neural-networking-lottery.html","line":24},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":25},{"title":"Lottery, lotto systems based on sum-total, odd, even, low, high runs best software.","target":"https://saliu.com/HLINE.gif","line":27},{"title":"Forums","target":"https://forums.saliu.com/","line":29},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":29},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":29},{"title":"Contents","target":"https://saliu.com/content/index.html","line":29},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":29},{"title":"Home","target":"https://saliu.com/index.htm","line":29},{"title":"Search","target":"https://saliu.com/Search.htm","line":29},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":29},{"title":"You should play dynamic lottery filters as only Ion Saliu's software can do.","target":"https://saliu.com/HLINE.gif","line":31}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12212409,-0.0381825,-0.03870609,-0.0363656,-0.05802793,0.03203066,0.04416382,0.00073154,0.0544345,-0.04264853,0.04933216,-0.03881801,0.02216755,0.00151366,-0.04042942,-0.01459607,-0.00398218,0.00668372,-0.04435994,0.00702024,0.10771429,-0.03560648,-0.07833297,-0.07261734,0.09085092,-0.00806807,-0.0580036,-0.03457318,-0.00665586,-0.22472031,0.00505018,0.03201558,0.00085187,-0.03527621,-0.04428052,-0.05837008,-0.05477104,0.08313166,-0.01662067,0.02785781,0.03181919,0.01323787,-0.0381966,-0.00009994,0.01595346,-0.05507898,0.002657,0.01865925,-0.00244204,-0.01033377,-0.07455499,0.02993912,0.00386455,0.01767673,0.05408197,0.03761551,0.08707707,0.10282782,0.00721782,0.0547527,0.02063628,0.04322558,-0.14724079,0.02632305,0.07333843,0.03108931,0.00278761,-0.01946084,0.02424637,0.05966483,0.04658268,0.02623986,-0.01838396,0.06807739,0.03873197,-0.02601216,-0.03776378,-0.07270285,-0.01274927,0.01942051,-0.04849986,-0.03281455,-0.03072385,-0.00102821,0.02394085,0.03950936,0.03261882,0.00517006,0.06614517,-0.09128733,0.00489853,0.05203514,0.05288297,0.03199375,0.05747435,0.00812451,0.0519904,-0.03662197,0.00886919,0.12370899,0.00853712,0.01152232,0.01585419,0.01929131,0.04598452,-0.03533938,-0.04488971,-0.05692686,-0.02526621,0.00886442,0.04633399,-0.01373845,0.0757136,-0.03746898,-0.01679831,-0.03677793,0.01039775,0.01930933,0.01442874,-0.01556763,-0.03631994,0.0109787,0.03709751,-0.00446538,0.0071548,0.00407716,-0.01270757,0.08551012,-0.00002888,0.0363888,0.04383616,-0.0224474,-0.05971265,-0.06269572,0.00239897,-0.02133135,0.02259742,0.01176942,0.02810467,0.0514836,-0.00733266,-0.05255072,0.03912992,-0.12349036,-0.03427741,0.1031071,-0.01145449,0.0005517,0.03422881,0.01986256,-0.00948786,-0.02342534,-0.01141698,-0.0742699,-0.0305909,-0.0181078,0.07142967,0.08726852,-0.02816982,0.02198362,-0.05814362,-0.05210457,-0.02771433,0.13703598,-0.01238557,-0.06546932,-0.03850722,-0.00204384,-0.02351138,-0.08569121,-0.01417701,-0.00912384,-0.06046603,0.05114219,0.11932253,-0.04741516,-0.07267475,-0.06933843,-0.04151844,0.01024796,-0.01738139,-0.0476572,-0.03361358,0.01300971,-0.04472021,-0.05282829,-0.02335231,-0.01000495,0.02647632,0.04489035,-0.09354331,-0.01563398,-0.06017385,-0.0226706,-0.04627236,-0.02560117,-0.01148506,-0.02415118,0.07407635,-0.02978065,0.00163312,0.01233625,0.01680039,0.04883174,-0.00799234,0.04220196,-0.0035369,-0.04801056,0.09572921,0.01255831,-0.00682706,-0.0000635,-0.00623921,0.08457053,0.01222842,0.02442719,0.01630763,0.06983475,-0.00368835,-0.02404132,0.00772981,-0.00693852,-0.09116065,-0.19549613,-0.02766837,-0.01541589,-0.00683477,0.00819256,-0.00568364,0.02195435,-0.03578432,0.02413069,0.06136659,0.04914883,-0.0602491,-0.00392637,0.0248108,-0.00872751,0.01565751,-0.11110867,-0.04289481,-0.01110071,0.0551349,0.00603227,0.04341624,-0.02364373,-0.09596287,-0.01586176,0.01514204,0.13599004,-0.00401883,0.01574591,-0.0388438,0.01257625,-0.00256216,-0.04451754,0.02761992,0.01266651,0.04186721,-0.0518852,-0.01262968,-0.04523167,-0.04213132,-0.09833415,0.0008057,-0.02020166,-0.0449554,-0.05011284,0.03158028,0.00178373,0.01508973,0.02989634,0.04264219,0.03237765,0.02180915,0.03627994,0.01931877,0.05663199,-0.01388192,-0.06922523,0.0038654,-0.01636387,0.08655991,-0.02428791,-0.03232796,0.03048738,-0.01172792,0.03128701,0.01192304,0.00966744,-0.04635085,0.02211626,0.01046073,-0.03392642,0.0857757,0.03468841,0.04117344,-0.01804875,0.04004723,0.04794129,-0.03237732,0.00641561,0.00337981,0.02472023,-0.07010318,0.05614713,0.08658557,0.02365887,0.03700561,0.02647655,-0.00570615,0.01416678,-0.00337193,0.00073501,0.01940558,-0.03221177,0.0247728,-0.00488838,-0.00209963,-0.29482001,0.02457741,-0.03390805,0.03312717,0.0041697,0.0005352,0.03197215,0.00030838,-0.00241183,-0.0083143,0.08964118,0.0216166,0.03886005,-0.06017044,-0.03916428,-0.0256992,0.00966818,-0.04952212,0.05994979,0.01360317,0.03438951,0.06615078,0.28113294,0.02310577,-0.00809732,0.01716197,0.00718141,0.02680682,-0.03466687,0.01258551,0.03857499,0.02007716,0.06405725,-0.01518485,-0.02566392,0.0271677,-0.03132968,0.01490034,0.02842692,0.05164823,-0.06931958,-0.00493044,-0.03222352,0.03469687,0.12582542,0.00872856,-0.0172465,-0.06630734,0.06536052,0.03535984,-0.06464681,-0.0561776,-0.05069482,-0.02023421,0.06219304,0.02969391,0.0071077,0.01155319,-0.01708987,-0.00834272,-0.00373905,0.02696234,0.04481067,0.04175549,-0.02496177],"last_embed":{"hash":"1k6tsud","tokens":118}}},"text":null,"length":0,"last_read":{"hash":"1k6tsud","at":1753423579643},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{2}","lines":[168,168],"size":213,"outlinks":[{"title":"**Lottery Mathematics**","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0930099,-0.04316947,-0.01802123,-0.03293807,-0.06481746,0.00543814,0.01903546,-0.0079357,0.05710922,-0.02359856,0.04207475,-0.01441244,0.03605234,0.00136481,-0.01672852,-0.01509895,0.02909255,0.01983135,-0.05027506,0.00452411,0.08585518,-0.01652968,-0.09565356,-0.08470719,0.08458623,-0.00219295,-0.05844223,-0.04859909,-0.0063617,-0.17783816,0.02022299,0.02969678,-0.00535415,-0.04412146,-0.03561071,-0.07302751,-0.04979981,0.06395741,-0.06726626,0.01438029,0.02665026,0.03341147,-0.01130489,-0.00106628,0.01267256,-0.05139058,0.02973366,0.0296384,0.0505346,-0.00199253,-0.04220851,0.03982264,0.00761494,0.02998626,0.02587513,0.02190921,0.05768938,0.09401982,-0.00282334,0.03265836,0.01591123,0.06296193,-0.12819616,0.02027143,0.05410874,0.05988947,0.00959008,-0.01415048,0.02134538,0.04971994,0.06012138,0.01407086,-0.00079489,0.06273776,0.05882897,-0.03187213,-0.03159226,-0.06334639,-0.03267459,0.00498409,-0.07430414,-0.04537996,-0.01464029,0.02523499,0.00266986,0.03780691,0.05362733,-0.0078178,0.10624308,-0.09515266,-0.01098222,0.04938335,0.05128657,0.03752858,0.0726667,-0.00997948,0.06515529,-0.02108121,0.02035715,0.14993352,-0.01776703,-0.00421078,0.0238682,-0.03563831,0.01988267,-0.04729073,-0.04143883,-0.04477017,-0.05267804,0.04313428,0.05540409,0.00769611,0.09180678,-0.03275439,-0.02722384,-0.04967877,-0.02306317,0.02287978,0.00806175,-0.01220585,-0.0471332,0.01496966,0.00186122,-0.01802089,0.00637567,0.00199151,-0.00193237,0.08002254,-0.00959735,0.01206022,0.04518254,-0.01758935,-0.0921136,-0.05112785,0.02207877,-0.04699187,0.02078844,0.01558094,-0.00334427,0.04702995,-0.03118306,-0.02688435,0.06577263,-0.14585288,-0.02384797,0.07092701,-0.01204189,0.00864634,0.01967821,0.00100191,-0.0005015,-0.02242501,-0.02328696,-0.0790467,-0.01708951,-0.01635202,0.10494724,0.05173406,-0.03400537,0.02170329,-0.05673978,-0.0092525,-0.02722608,0.12219834,-0.02338229,-0.08203794,-0.03372054,0.03391537,-0.03058261,-0.07749904,-0.02682894,0.00596296,-0.07043712,0.02108149,0.12866127,-0.05106371,-0.10212601,-0.06183393,-0.01236163,-0.00850467,0.00973252,-0.0322346,-0.02855764,0.02158629,-0.02241433,-0.04480045,-0.02349654,-0.01359233,0.01832909,0.04650784,-0.06637062,0.01976228,-0.0421279,-0.0251116,-0.05348976,-0.03227432,-0.04315718,-0.01686142,0.09364858,-0.02325053,-0.01091906,0.01024603,0.03169254,0.05666415,-0.00406972,0.02969994,0.00621104,-0.07467498,0.09146111,0.04425363,-0.04336591,-0.01068018,0.03110911,0.09380975,-0.03432952,0.03090542,0.01201739,0.06954355,-0.03632888,-0.01525989,-0.02218988,0.01613986,-0.10255571,-0.18054086,-0.04931645,-0.00784363,0.00142502,0.02713936,0.00362075,0.01288807,-0.04298211,0.01952687,0.07452496,0.08295879,-0.09359494,0.00717817,0.05738597,-0.02633215,0.00993127,-0.1017494,-0.02258293,-0.01164226,0.04112101,0.01839018,0.00943227,0.01209212,-0.12837212,-0.01276408,0.01775496,0.13293204,-0.02177829,0.0415903,-0.03236179,0.01853491,-0.0364627,-0.03710187,0.00910302,0.01369396,0.05487316,-0.05115891,-0.0180683,-0.02641374,-0.017998,-0.07857511,-0.04099481,-0.01306776,-0.07032301,-0.03161808,0.01959568,-0.01041751,0.00404873,0.03415041,0.05562895,0.01583474,0.02760408,0.04970712,0.01771235,0.05642988,-0.00294443,-0.06563842,0.00501997,-0.0164125,0.07433744,-0.00962816,-0.0687333,0.00380761,0.00457474,0.05331861,0.01569554,0.00158657,-0.04494421,0.02812336,-0.02044282,-0.0152146,0.09596895,0.0105402,0.06049161,-0.00739157,0.04331964,0.08772711,-0.04128274,-0.0018572,0.00959127,-0.00929853,-0.05489547,0.06231536,0.10142162,0.0141209,0.03379251,0.03832595,0.02325616,-0.01948796,-0.01214308,0.00777731,0.01397722,-0.03358774,0.00091591,-0.01934436,0.00391507,-0.26020256,0.02979956,-0.0356852,0.03716746,-0.02714643,-0.03515167,0.02725098,0.02981595,0.0172207,0.00580678,0.06041262,-0.00124543,0.0172545,-0.06288753,0.00705842,-0.03130236,0.02635401,-0.04251358,0.06034725,0.03561366,0.04013186,0.06625376,0.2712872,0.01995697,0.0238946,0.02082128,-0.00928541,0.03716855,-0.02475328,0.02473009,0.04193975,0.01749184,0.05114969,-0.00152996,-0.03075897,0.00955063,-0.01761639,0.00562299,-0.00420418,0.06814243,-0.06346694,0.00318626,-0.05286365,0.02745306,0.1125048,-0.0098902,-0.02422579,-0.07494357,0.05495971,0.04980483,-0.08442742,-0.01526764,-0.04906653,-0.04287177,0.05598625,0.02794106,0.01736951,-0.00072271,-0.01188678,-0.02302356,-0.01430385,-0.01909635,0.03990348,0.03155435,-0.03127626],"last_embed":{"hash":"1m22jsz","tokens":89}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423579685},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{4}","lines":[170,170],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0948073,-0.00642634,-0.01148807,-0.06838162,-0.04599215,-0.00224007,0.01986268,0.00209433,0.0707484,-0.0362343,0.06236089,-0.0159886,0.05056042,-0.02601126,-0.04425985,-0.03996381,0.00798216,-0.00068671,-0.03595979,-0.01337691,0.07575572,0.00316928,-0.09296685,-0.083741,0.09961442,-0.03012596,-0.07502606,-0.04855841,-0.04199069,-0.21819566,0.02504998,0.02235738,0.00428831,-0.03019488,-0.0186056,-0.03979092,-0.02765033,0.04136701,0.00357376,0.0137591,0.02608215,0.01211799,-0.00801842,0.02555581,0.01925563,-0.04972594,0.00326732,0.02798711,0.00357888,0.00039405,-0.08979888,0.02647458,0.0378162,0.01361526,0.01342038,0.03160528,0.04865369,0.08552395,0.02896675,0.06387212,0.03476793,0.05349769,-0.15211622,0.02165263,0.09194035,0.04100664,-0.00632886,-0.03057942,0.01270939,0.03265866,0.03257374,0.00752505,-0.00624127,0.06533755,0.0829144,-0.0351117,-0.0584176,-0.07396512,-0.04719296,0.01237777,-0.05709903,-0.01209135,-0.00544912,0.02299343,-0.00424782,0.03677718,0.0589023,-0.0236328,0.10962807,-0.08550049,0.01306197,0.06262496,0.04968565,0.02522528,0.06503335,-0.00736289,0.06093781,-0.03858858,0.03295351,0.14788622,-0.03011844,-0.01305368,0.02705908,-0.02879294,0.01007634,-0.04967014,-0.0372337,-0.03779064,-0.03108812,0.01088846,0.03083437,-0.00253127,0.05403443,-0.04340137,-0.03010593,-0.04286724,-0.01281891,-0.00541477,0.0009393,0.0080908,-0.03600091,-0.02291368,0.00568507,-0.00410727,0.00408056,-0.00604086,0.02923713,0.08148902,-0.02790528,0.00419469,0.05254155,-0.01366954,-0.09845928,-0.05707034,0.02178775,-0.01874255,0.00561547,0.00840722,0.02538745,0.03941474,-0.03772795,-0.0202298,0.04885172,-0.12665556,-0.02576986,0.09785377,-0.00233042,-0.01836366,0.0072158,-0.00846342,0.02617583,-0.03479387,-0.01939301,-0.05466714,-0.00900271,-0.01694974,0.09742204,0.07571526,-0.01155992,-0.00630664,-0.05311511,-0.0360158,-0.03065432,0.13321325,-0.01260696,-0.0929019,-0.01510735,0.01857242,-0.0243635,-0.09013412,0.02116137,-0.00949785,-0.07379176,0.04515256,0.06936419,-0.0301893,-0.06238864,-0.06100887,-0.00685378,-0.00711572,0.02295655,-0.03945729,-0.04078917,0.04727828,-0.0402431,-0.05153127,-0.03342748,0.01471419,-0.00491711,0.04533795,-0.05422772,0.01111659,-0.06005123,-0.00367144,-0.0321785,-0.04017625,-0.02206278,-0.01166789,0.08678981,-0.0551898,0.01036553,0.00395613,0.02461964,0.05576569,0.05107531,0.0342944,-0.02564228,-0.0558186,0.08726377,0.02119779,-0.02985607,-0.00639797,0.01139004,0.07730643,-0.00286247,0.03099699,0.00392701,0.04738568,-0.01486902,0.00741839,-0.01691198,0.01297405,-0.07778288,-0.19499326,-0.04936652,-0.00329373,-0.01128382,0.00483153,-0.00724277,0.03106118,-0.04179504,0.01887004,0.08561464,0.07034537,-0.05592836,0.00866218,0.02899235,-0.01699506,0.02432716,-0.11013845,-0.03242721,-0.04988324,0.07273719,0.01342225,0.01721003,-0.0289144,-0.09443623,-0.0080617,0.00803914,0.11040819,-0.03991151,0.03277664,-0.03967615,0.03833411,-0.03414975,-0.02132401,-0.00598662,0.01050413,0.07099083,-0.03773772,-0.0002823,-0.02806924,-0.01563888,-0.113575,-0.00059097,-0.01669242,-0.07546943,-0.03282007,0.04881821,0.04573644,-0.03685428,0.02555107,0.02996654,0.01341383,-0.00671439,0.03459493,0.03826838,0.06967687,-0.01702588,-0.05594906,0.03972417,-0.02057045,0.06290594,-0.05004168,-0.08634281,0.03660022,0.01754668,0.04319649,0.00147899,0.01589934,-0.0250002,0.02828576,0.00133057,-0.02129761,0.10649137,-0.0083167,0.05755727,-0.02531247,0.07067196,0.08211722,-0.03929989,-0.01843138,0.01924814,0.00806342,-0.0638876,0.06964575,0.05436154,-0.00128097,0.03376585,0.04205315,-0.01094091,0.00794407,-0.00308583,0.01664895,-0.01784801,-0.01842201,0.0193517,-0.00326684,0.00692407,-0.24945998,0.02648723,-0.04472084,0.03349784,-0.00885057,-0.0190903,0.02256834,0.00294708,0.03512337,0.02547113,0.06407166,0.00227852,0.01720593,-0.0815615,-0.01157967,-0.03005829,0.04747332,-0.03965554,0.04366054,0.03405154,0.03715538,0.06251929,0.28599045,0.01918008,0.00804333,0.02429053,0.0008651,0.02687299,-0.03226356,0.0185858,0.05384216,0.0161232,0.06542466,-0.01206857,-0.05211368,-0.01939434,-0.02833327,0.0307068,0.03375898,0.07412661,-0.05298925,0.01289023,-0.06056426,-0.02126491,0.1111905,0.01745063,-0.03119395,-0.06834836,0.05481756,0.04044763,-0.06159745,-0.02615902,-0.06412753,-0.03398132,0.05754744,0.02713569,-0.01475673,0.0202409,0.00822767,-0.01111549,0.02385908,-0.00601192,0.03480779,0.0531439,-0.01440209],"last_embed":{"hash":"1sdoqa9","tokens":96}}},"text":null,"length":0,"last_read":{"hash":"1sdoqa9","at":1753423579720},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{6}","lines":[172,172],"size":233,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{23}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13154656,-0.02405461,-0.03147162,-0.04405168,-0.04872672,0.02389571,0.00196344,0.01053886,0.05320023,-0.04903375,0.03108784,-0.04769946,0.03281147,0.00928624,-0.00214042,-0.0593078,0.02795809,0.00400773,-0.04362474,0.01431063,0.10580865,-0.0067907,-0.04052039,-0.07987858,0.08203329,-0.04435044,-0.04822743,-0.01875563,-0.02329744,-0.23497444,0.00012842,0.00560499,-0.01117211,-0.03856789,-0.05662986,-0.03074854,-0.00046792,0.05911902,-0.01365986,0.03202266,0.0238008,0.01009944,0.00157188,-0.03360518,0.00056106,-0.04162916,0.0213363,0.01339755,0.0293161,0.01759435,-0.06401816,0.04284533,0.01327787,0.0375457,0.01145429,0.04141187,0.05983212,0.12180191,0.01145337,0.04791167,-0.01449516,0.03354403,-0.15576594,0.03641339,0.01404348,0.02337474,-0.01488899,-0.02032226,0.03811839,0.03743419,0.03207823,0.062831,-0.03503902,0.04828361,0.04654387,-0.0577778,-0.04073632,-0.05110202,-0.02658676,-0.00872644,-0.06810974,-0.07841424,-0.04761995,-0.01109655,0.07575539,0.05319713,0.03229268,0.00164042,0.07382543,-0.08465218,-0.00806677,0.02566636,0.07588606,0.03170164,0.05014298,0.03069377,0.05983396,-0.00330468,0.01871136,0.14359871,0.00254464,0.01851821,0.00993593,-0.00248104,0.03712802,-0.02366278,-0.03710798,-0.00990992,-0.02922677,0.03343028,0.05322724,-0.00809897,0.07660972,-0.04633295,-0.04275664,-0.03353844,0.01837141,-0.00235707,0.01337002,-0.00390699,-0.06541207,-0.01356519,0.03554866,-0.02310191,0.029118,0.00987768,0.01992548,0.04657051,-0.00101202,0.05840276,0.04059041,-0.01948057,-0.0997964,-0.08589567,0.01448207,-0.01664877,-0.0147728,0.00301405,0.00091666,0.05071348,-0.04197114,-0.04233667,0.02099188,-0.10935353,-0.01868196,0.07255374,-0.00945342,-0.00854952,0.00773893,-0.00745423,-0.00790027,-0.01551965,-0.00429122,-0.06044168,-0.04338244,-0.00423325,0.08739215,0.09543834,-0.04638289,0.00861871,-0.04454648,-0.04831405,-0.02992082,0.12019099,-0.00583734,-0.0625843,-0.04463675,0.02521165,-0.02181357,-0.08145488,-0.03631798,-0.01577662,-0.0498336,0.02562851,0.09817996,-0.05304958,-0.07112084,-0.04977559,-0.05032691,0.00404631,-0.01450164,-0.01793161,-0.01346282,0.01504349,-0.05456222,-0.07890359,0.01367667,-0.01116663,0.02619969,0.03117727,-0.09213058,-0.01490575,-0.03277452,0.01257344,-0.03868518,-0.02632851,-0.00410607,-0.01883715,0.07028622,-0.01308132,-0.00469254,0.00056067,0.03965901,0.04572231,-0.00430525,0.03180078,-0.04000248,-0.05552302,0.10850819,0.00626842,0.00406475,0.01402615,0.03986548,0.07536025,-0.0047562,0.01960296,-0.00016713,0.04571891,0.00650345,-0.00548101,-0.00422224,0.01230858,-0.1064189,-0.2124591,-0.01861553,-0.04864395,-0.00317438,0.02998068,-0.03782952,0.02347849,-0.05304458,0.03368152,0.08329292,0.08953284,-0.05036226,0.02901095,0.03987782,0.00255092,0.02631533,-0.08398121,-0.01396915,-0.02574883,0.05246712,0.02993423,0.00936103,0.01939172,-0.08487512,0.01387032,-0.01640346,0.1343907,0.00161841,0.05947869,-0.04893783,0.01436396,0.00057662,-0.03598821,0.00663221,0.01372303,0.06098233,-0.05087787,-0.01599143,-0.03678556,-0.03386548,-0.0889913,-0.0137329,-0.02676967,-0.08049008,-0.03517598,0.0475767,0.00545829,0.00435881,0.00300506,0.00552822,0.07417093,-0.0096232,0.05081182,0.00028938,0.06456042,-0.02876792,-0.0865819,-0.02262083,-0.00304483,0.0714054,-0.03503629,-0.01783337,0.03452118,0.00304651,0.03313493,0.01060128,0.03282056,-0.0304859,0.03684226,0.00797595,-0.03403268,0.10280971,-0.00160459,0.08832388,0.00509351,0.04254349,0.02625855,-0.02089695,-0.02065135,-0.00012372,0.02218122,-0.05775521,0.07885522,0.075136,0.01845822,0.02448455,0.02944039,-0.00394401,0.00347761,-0.00742223,0.01856602,0.00900377,-0.05967071,0.04066769,-0.00015434,-0.00104805,-0.2654503,0.01442472,-0.03648994,0.0201808,-0.00669837,-0.03980758,0.01822937,-0.04342455,0.02287812,0.00284199,0.0812868,0.04453335,0.02908448,-0.0676844,-0.03792321,-0.02236926,-0.02348034,-0.01528674,0.0832161,0.00382241,0.04966897,0.05844551,0.27821451,0.01702252,-0.01100683,0.00739322,0.02120382,0.01231923,-0.02695018,0.00073122,0.05607719,0.02046638,0.08588478,-0.00364244,-0.02568914,0.00582175,-0.03460761,0.00760855,-0.00400355,0.02250459,-0.04663353,0.03331684,-0.02641083,0.03141587,0.10532044,-0.01370544,-0.01205662,-0.04643051,0.05670302,0.05995169,-0.05611355,-0.02912292,-0.01689255,-0.01034049,0.08704983,0.02944837,-0.00968002,0.01880886,-0.02074038,-0.03480338,-0.00388504,0.03555748,0.03076309,0.02181905,0.011762],"last_embed":{"hash":"274wcs","tokens":296}}},"text":null,"length":0,"last_read":{"hash":"274wcs","at":1753423579752},"key":"notes/saliu/Odd Even, Low High, Sums Lottery Strategy, Lotto Systems.md#Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems#5\\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling#{23}","lines":[190,195],"size":618,"outlinks":[{"title":"Lottery, lotto systems based on sum-total, odd, even, low, high runs best software.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"You should play dynamic lottery filters as only Ion Saliu's software can do.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
