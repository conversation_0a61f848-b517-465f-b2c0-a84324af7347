"smart_sources:Obsidian/Obsidian_嵌入式查詢.md": {"path":"Obsidian/Obsidian_嵌入式查詢.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06745075,0.02108121,0.03814729,-0.0295904,-0.04084231,-0.01762235,-0.0333861,0.00019553,-0.02310034,0.08027587,-0.00057101,-0.0222523,0.03389816,-0.02582755,0.03604567,0.03328636,-0.03280484,0.00234563,0.04679393,-0.01759793,0.09499547,-0.04012205,0.01715732,-0.02776591,-0.03763638,0.00436848,-0.01497763,-0.05783153,-0.01324376,-0.20372859,0.0405584,-0.00675284,-0.01833949,0.00985679,-0.02828562,-0.03648718,-0.00953517,-0.01143932,-0.04542019,0.08554951,-0.00055201,0.01072953,-0.00768693,-0.02868466,-0.01816903,-0.06458297,-0.02010874,-0.04862834,0.03660778,-0.04556865,0.00305022,-0.01704303,-0.02480584,-0.04540757,0.08180549,0.02163553,0.10581122,0.02692307,0.00599774,-0.00684959,0.08766689,0.01626587,-0.14424865,0.15510112,-0.01506151,0.03408123,0.00029056,0.01148286,0.0231001,0.04161196,-0.00333824,0.05630436,-0.03877544,0.05844773,0.01620024,-0.02075092,0.01948314,0.01100714,-0.00945621,-0.02872681,-0.01716781,0.05884923,-0.00197467,-0.04030476,-0.0354878,-0.08580278,-0.03896058,0.00833693,0.07586641,0.02328514,-0.00941293,-0.04460517,0.04013694,0.02181965,-0.00009583,-0.06802074,0.07447394,-0.01597121,-0.05036278,0.11924779,-0.02869832,0.00625012,0.00778723,-0.04584756,0.00890176,0.00297979,-0.01870294,-0.04360247,-0.01720931,0.03823885,-0.01149847,0.00862374,-0.0076289,-0.06157473,0.05335711,0.01093437,-0.00453359,-0.01651251,-0.03343762,-0.01945449,-0.00755956,0.0355421,0.02038736,-0.01779047,0.11251958,-0.03501216,0.06327334,0.03956591,0.00558042,0.03210397,0.07290832,-0.03457054,-0.08353805,-0.05167707,-0.03649046,-0.00461186,-0.0100632,-0.00814264,0.0340286,0.01573071,-0.04784954,0.02924927,0.08231603,-0.09230413,0.02295784,0.08426675,-0.00849401,0.03270239,-0.03942227,-0.12280351,-0.00599802,0.03012991,-0.0154487,-0.04432251,-0.00596031,-0.01834702,0.05341654,0.06989708,-0.10855702,0.00548142,-0.04429886,-0.03780627,-0.04388338,0.0893875,-0.00629139,-0.07570448,-0.08422142,0.02347975,0.02243147,-0.07419068,-0.0159646,-0.04649395,-0.07634868,0.02143962,0.11727974,-0.0206806,-0.00899513,-0.05841594,-0.02357605,0.03688379,-0.00229081,-0.05473142,0.00699349,0.02554222,-0.03116476,-0.10344296,-0.02948927,-0.03985827,0.0454431,0.00573371,-0.00065639,0.02092943,-0.04552275,0.05312425,0.0134491,0.01944094,0.01739981,0.00796118,-0.00197252,-0.02467794,0.11282509,-0.02991519,-0.02535486,-0.0209635,-0.07901967,0.02395754,-0.04579008,-0.0083092,0.01570851,0.02744373,-0.04337527,-0.00300822,0.03173406,0.01805229,-0.00764838,-0.01354541,0.04725255,0.05465442,0.00509991,0.06000551,-0.0039447,-0.01469622,-0.03676176,-0.22642079,-0.03885495,0.00867472,-0.00077582,0.02416703,-0.09101204,0.05800161,0.02297049,-0.02329331,0.07138527,0.098286,0.06765648,0.05527866,0.03533437,-0.03292222,0.01238554,0.04770432,-0.03639894,-0.0308215,0.00343416,-0.03168191,0.03069372,-0.02821782,-0.09479255,0.00151442,-0.00410854,0.17398746,0.0199173,0.02464543,-0.05930511,0.00567019,0.0102735,-0.01113905,-0.15414162,-0.00358305,0.02451529,-0.02071279,0.08980004,-0.0201718,-0.02536377,-0.06125265,0.0728868,-0.00539024,-0.09840117,-0.00346233,0.01346845,-0.06527806,-0.00838063,-0.01372011,0.03553472,0.03600175,0.06597098,0.00320383,0.05017174,-0.01703124,-0.02209485,-0.02905075,-0.02529943,0.00603522,0.07530547,-0.06017837,-0.01660346,0.01906312,-0.04172234,0.04691217,0.0458882,0.04073798,0.02275505,0.06758934,-0.05517929,-0.07712215,0.11270763,-0.0238624,0.03335715,0.02562131,-0.00075195,0.01364207,-0.01350897,0.01739545,0.01383995,0.05344719,-0.06180884,0.06002175,0.04815939,-0.0442861,0.04568959,-0.00317365,0.04466997,0.02740507,-0.01510879,-0.09175258,-0.00005641,-0.03100691,-0.04944342,0.03880084,0.01990721,-0.25599295,0.0533753,0.06225921,-0.02653962,0.01396934,0.00118338,-0.00575569,-0.02213471,0.02913219,0.05419387,-0.00438099,0.00711839,-0.01687168,-0.03447476,0.02298839,0.04790901,-0.00749199,0.00885183,0.02400412,-0.08094585,0.04265195,0.02585432,0.2576834,0.00770151,0.01463916,0.02086209,-0.00689247,0.03727763,0.02738779,0.02617558,-0.0156346,0.02551145,0.08532228,-0.0130106,0.03701789,-0.03275461,0.00008637,0.02628013,0.05024875,0.01966481,-0.01695802,0.0180874,-0.07333744,0.0616871,0.05692958,-0.02764747,-0.04793685,-0.0540571,0.01789388,0.01424503,-0.02996986,0.0393242,0.02842972,0.04038253,0.02155248,0.01275716,-0.01666137,-0.01093398,-0.02363331,0.04482848,0.00656859,-0.00370172,0.00108898,0.01514279,0.02186847],"last_embed":{"hash":"f60a421e3356dfe7dfc808623a571a6167322074eac35327fdd2debec40d2c51","tokens":475}}},"last_read":{"hash":"f60a421e3356dfe7dfc808623a571a6167322074eac35327fdd2debec40d2c51","at":1745995211043},"class_name":"SmartSource2","outlinks":[{"title":"Knowledge-Management","target":"https://heymichellemac.com/tag/knowledge-management/","line":3},{"title":"What Is An Embedded Query?","target":"https://heymichellemac.com/embedded-queries-obsidian#what-is-an-embedded-query","line":10},{"title":"How Can I Set Up An Embedded Query?","target":"https://heymichellemac.com/embedded-queries-obsidian#how-can-i-set-up-an-embedded-query","line":11},{"title":"Pros And Cons Of Embedded Queries","target":"https://heymichellemac.com/embedded-queries-obsidian#pros-and-cons-of-embedded-queries","line":12},{"title":"Pros:","target":"https://heymichellemac.com/embedded-queries-obsidian#pros","line":13},{"title":"Cons:","target":"https://heymichellemac.com/embedded-queries-obsidian#cons","line":14},{"title":"Power User Tip: How To Extend The Functionality Of Embedded Queries With Query Control","target":"https://heymichellemac.com/embedded-queries-obsidian#power-user-tip-how-to-extend-the-functionality-of-embedded-queries-with-query-control","line":15},{"title":"Examples Of How I Use Embedded Queries","target":"https://heymichellemac.com/embedded-queries-obsidian#examples-of-how-i-use-embedded-queries","line":16},{"title":"1 - Content Processing Inbox","target":"https://heymichellemac.com/embedded-queries-obsidian#1---content-processing-inbox","line":17},{"title":"2 - Content Creation Hub","target":"https://heymichellemac.com/embedded-queries-obsidian#2---content-creation-hub","line":18},{"title":"3 - List of Articles To Include In My Weekly Newsletter","target":"https://heymichellemac.com/embedded-queries-obsidian#3---list-of-articles-to-include-in-my-weekly-newsletter","line":19},{"title":"4 - In my Yearly, Quarterly, Weekly, Daily notes","target":"https://heymichellemac.com/embedded-queries-obsidian#4---in-my-yearly-quarterly-weekly-daily-notes","line":20},{"title":"To Wrap Up","target":"https://heymichellemac.com/embedded-queries-obsidian#to-wrap-up","line":21},{"title":"embedded query","target":"https://help.obsidian.md/Plugins/Search#Embed+search+results+in+a+note","line":57},{"title":"code block","target":"https://help.obsidian.md/How+to/Format+your+notes#Code+blocks","line":59},{"title":"嵌入式查詢","target":"https://help.obsidian.md/Plugins/Search#Embed+search+results+in+a+note","line":65},{"title":"程式碼區塊","target":"https://help.obsidian.md/How+to/Format+your+notes#Code+blocks","line":67},{"title":"Obsidian Publish doesn’t support embedded search results","target":"https://help.obsidian.md/Plugins/Search#Embed+search+results+in+a+note","line":93},{"title":"Obsidian Publish 不支援內嵌搜尋結果","target":"https://help.obsidian.md/Plugins/Search#Embed+search+results+in+a+note","line":111},{"title":"the Query Control plugin","target":"https://github.com/nothingislost/obsidian-query-control","line":115},{"title":"Query Control In Action","target":"https://user-images.githubusercontent.com/89109712/154376835-08c1d3ab-b67c-4ca6-8261-abf41c38d7c1.gif","line":121},{"title":"details on how to do each is outlined here on the plugin page","target":"https://github.com/nothingislost/obsidian-query-control#installing-via-brat","line":125},{"title":"Matter","target":"https://hq.getmatter.com/","line":133},{"title":"Inbox Query","target":"https://heymichellemac.com/assets/images/2022/MXA22020/inbox-query.png","line":139},{"title":"Inbox Result","target":"https://heymichellemac.com/assets/images/2022/MXA22020/inbox-results.png","line":143},{"title":"Content Creation Hub Note","target":"https://heymichellemac.com/assets/images/2022/MXA22020/content-creation-hub.png","line":155},{"title":"the Figma file here","target":"https://www.figma.com/community/file/1168867974967146879","line":159},{"title":"Design Insight","target":"https://designinsight.substack.com/","line":163},{"title":"Design Insight Articles List","target":"https://heymichellemac.com/assets/images/2022/MXA22020/design-insight-query.png","line":171},{"title":"Yearly Note Query","target":"https://heymichellemac.com/assets/images/2022/MXA22020/years-query.png","line":181},{"title":"Yearly Note Result","target":"https://heymichellemac.com/assets/images/2022/MXA22020/years-results.png","line":185},{"title":"over on Twitter","target":"https://twitter.com/heymichellemac/status/1585980871248867328","line":195},{"title":"the Figma file here","target":"https://www.figma.com/community/file/1168867974967146879","line":197},{"title":"heymichellemac","target":"https://pkm.social/@heymichellemac","line":201},{"title":"GitHub","target":"https://github.com/heymichellemac","line":205},{"title":"Jekyll","target":"https://jekyllrb.com/","line":205},{"title":"Powered by","target":"https://pkm.social/@heymichellemac","line":205},{"title":"Netlify","target":"https://www.netlify.com/","line":205},{"title":"RSS\n\n","target":"https://heymichellemac.com/feed.xml","line":207},{"title":"Buy Me A Coffee","target":"https://cdn.buymeacoffee.com/widget/assets/coffee%20cup.svg","line":211}],"blocks":{"#":[1,32],"##{1}":[10,10],"##{2}":[11,11],"##{3}":[12,14],"##{4}":[15,15],"##{5}":[16,20],"##{6}":[21,32],"##What Is An Embedded Query?":[33,54],"##What Is An Embedded Query?#{1}":[35,54],"##How Can I Set Up An Embedded Query?":[55,78],"##How Can I Set Up An Embedded Query?#{1}":[57,58],"##How Can I Set Up An Embedded Query?#{2}":[59,59],"##How Can I Set Up An Embedded Query?#{3}":[60,60],"##How Can I Set Up An Embedded Query?#{4}":[61,61],"##How Can I Set Up An Embedded Query?#{5}":[62,63],"##How Can I Set Up An Embedded Query?#{6}":[64,78],"##Pros And Cons Of Embedded Queries":[79,112],"##Pros And Cons Of Embedded Queries#{1}":[81,82],"##Pros And Cons Of Embedded Queries#Pros:":[83,89],"##Pros And Cons Of Embedded Queries#Pros:#{1}":[85,85],"##Pros And Cons Of Embedded Queries#Pros:#{2}":[86,86],"##Pros And Cons Of Embedded Queries#Pros:#{3}":[87,87],"##Pros And Cons Of Embedded Queries#Pros:#{4}":[88,89],"##Pros And Cons Of Embedded Queries#Cons:":[90,112],"##Pros And Cons Of Embedded Queries#Cons:#{1}":[92,92],"##Pros And Cons Of Embedded Queries#Cons:#{2}":[93,95],"##Pros And Cons Of Embedded Queries#Cons:#{3}":[96,112],"##Power User Tip: How To Extend The Functionality Of Embedded Queries With Query Control":[113,126],"##Power User Tip: How To Extend The Functionality Of Embedded Queries With Query Control#{1}":[115,126],"##Examples Of How I Use Embedded Queries":[127,188],"##Examples Of How I Use Embedded Queries#{1}":[129,130],"##Examples Of How I Use Embedded Queries#1 - Content Processing Inbox":[131,150],"##Examples Of How I Use Embedded Queries#1 - Content Processing Inbox#{1}":[133,150],"##Examples Of How I Use Embedded Queries#2 - Content Creation Hub":[151,160],"##Examples Of How I Use Embedded Queries#2 - Content Creation Hub#{1}":[153,160],"##Examples Of How I Use Embedded Queries#3 - List of Articles To Include In My Weekly Newsletter":[161,172],"##Examples Of How I Use Embedded Queries#3 - List of Articles To Include In My Weekly Newsletter#{1}":[163,172],"##Examples Of How I Use Embedded Queries#4 - In my Yearly, Quarterly, Weekly, Daily notes":[173,188],"##Examples Of How I Use Embedded Queries#4 - In my Yearly, Quarterly, Weekly, Daily notes#{1}":[175,188],"##To Wrap Up":[189,211],"##To Wrap Up#{1}":[191,211]},"last_import":{"mtime":1716361812102,"size":11863,"at":1740449882665,"hash":"f60a421e3356dfe7dfc808623a571a6167322074eac35327fdd2debec40d2c51"},"key":"Obsidian/Obsidian_嵌入式查詢.md"},