
"smart_sources:結合 Cursor AI IDE、Obsidian 與深度研究來創建和組織筆記.md": {"path":"結合 Cursor AI IDE、Obsidian 與深度研究來創建和組織筆記.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"42f22cfcbab923f642855f2a527a329b5c6727ebb4812f7b9f85868cb117953d","at":1740967857759},"class_name":"SmartSource2","outlinks":[{"title":"有哪些推薦的Obsidian插件可以輔助深度研究和筆記整理","target":"有哪些推薦的Obsidian插件可以輔助深度研究和筆記整理","line":48}],"blocks":{"#":[2,50]},"last_import":{"mtime":1740962371313,"size":2055,"at":1740967857760,"hash":"42f22cfcbab923f642855f2a527a329b5c6727ebb4812f7b9f85868cb117953d"}},
"smart_sources:結合 Cursor AI IDE、Obsidian 與深度研究來創建和組織筆記.md": {"path":"結合 Cursor AI IDE、Obsidian 與深度研究來創建和組織筆記.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05414723,0.00963553,0.00454904,0.02591022,-0.03097051,0.00654096,-0.01775665,0.02743739,0.04386922,0.02671369,-0.0028449,-0.07209234,-0.0030576,0.02989542,0.00403712,0.01328873,-0.02898831,-0.01496476,0.0152408,-0.04467043,0.07279409,-0.05734877,0.00087626,-0.06656151,-0.03254344,0.02935618,0.01213588,-0.0581412,0.03272418,-0.16901696,0.06969552,-0.02297261,-0.0133698,-0.02926742,-0.02414503,-0.03159866,-0.051842,0.01880388,-0.04157863,0.00518334,-0.00573748,0.01666992,-0.01759339,0.01794995,0.04362127,-0.00941065,-0.08262047,-0.0150616,0.005326,-0.01548574,-0.01106441,-0.04341297,-0.04160313,0.01231886,-0.00610183,0.01511612,0.07246738,0.03974961,0.0624308,0.05078019,0.05150424,0.04278773,-0.18136257,0.07180867,0.02858055,-0.02787597,-0.02218972,-0.07830864,0.03537807,0.04235768,0.03291269,0.07029659,0.05005901,-0.01410534,0.016133,-0.0081901,0.00486826,-0.02936439,-0.00080111,-0.00940571,-0.03094934,0.09184538,-0.02707248,-0.00333583,-0.03193106,-0.00227493,-0.02124469,-0.04919476,0.05556004,-0.05205892,-0.01081742,-0.06256206,0.00287522,0.02932758,0.00387911,0.03378405,0.01177106,0.0124792,-0.06320811,0.13636495,-0.03953942,-0.0304093,-0.03726436,-0.0108385,0.05591322,-0.01804895,-0.04117736,-0.06313557,-0.04172569,0.05901996,-0.02035846,-0.01087936,0.0378352,-0.03679604,0.04888996,0.02662813,0.03395316,0.02784052,0.01795949,-0.0313368,0.00467089,0.02475832,0.00226494,-0.01592308,0.01087682,-0.00690952,0.0540926,0.05755255,0.00595703,0.0361071,0.0935244,-0.00170012,-0.02701372,-0.02211959,0.04802046,-0.00178961,-0.0476803,-0.0123137,-0.00554443,-0.01002215,-0.06487302,-0.02637882,0.0556577,-0.06848489,0.05749338,0.09076053,0.01276506,0.00345542,0.00967527,-0.09622794,0.02565772,0.06209564,-0.06987209,-0.03467826,-0.05384736,0.01811431,0.03014795,0.10249884,-0.08098335,-0.01744212,-0.00091096,-0.09730957,-0.01882856,0.11481129,-0.04408479,-0.0909135,-0.06013464,0.0678122,0.01127398,-0.00094434,-0.01317088,-0.00370302,-0.06073545,0.04698585,0.06861448,-0.01234313,-0.1033733,-0.00904719,-0.05687872,-0.00551571,0.03951295,-0.05719363,-0.0251416,0.01476715,-0.0272386,-0.06214352,0.00583932,-0.05839822,0.0119452,-0.03609471,-0.0308625,0.07149439,0.01356914,0.06978054,0.01371335,-0.04690952,0.02795836,-0.02385078,0.01294426,0.03116737,0.1136952,-0.01768019,-0.01692158,-0.03083083,-0.05982836,-0.03996971,-0.02874334,-0.04993577,-0.0048912,0.05652422,0.00061843,-0.05648403,0.06542491,0.00338836,-0.00776572,0.00367184,0.01105014,0.02876345,0.03312926,0.06296472,0.02079977,-0.03886861,-0.02754891,-0.2548131,-0.03747131,0.04384321,0.0032942,0.01891361,-0.06142394,0.04119091,-0.00091607,0.04234382,0.04659367,0.08607412,0.04358584,0.01921768,0.03105033,-0.04941987,0.04691045,0.05121331,0.01024282,-0.01867933,-0.00690443,-0.02800426,0.04564087,0.0236599,-0.07219569,0.01194393,0.00095471,0.13306378,0.02607199,0.01619585,0.02968934,-0.00401829,0.02758992,-0.00444748,-0.11958352,0.05556723,0.03192126,-0.03060028,0.02387662,-0.0745306,-0.02658942,-0.05043367,0.06495818,0.00898726,-0.11673288,0.00883057,-0.01522877,-0.07338023,-0.01646319,0.04300181,0.07833666,0.05538231,0.06396006,0.00405125,0.05580441,-0.02941443,-0.02838038,-0.05782672,0.01682825,0.01064652,0.02203779,-0.00547364,-0.0757593,0.01224004,-0.04365669,0.06865162,0.03864438,0.04372022,0.01245456,0.06530964,0.0165798,-0.08534542,0.15880857,-0.01776805,0.00380491,0.08575915,-0.03119418,0.01140532,-0.026576,0.02884489,0.00769425,0.02850828,0.03017161,0.02150704,0.0445485,0.01679594,0.02078205,0.01797543,0.0235264,0.06314786,-0.01936975,-0.01489763,0.04848155,-0.09901299,0.00265564,0.01643517,0.0255637,-0.28529575,0.06870828,0.02176291,0.01536136,0.02181018,-0.00106388,0.00306198,-0.03913871,-0.00425973,-0.0325735,-0.02655717,0.00517011,-0.00944935,-0.01384537,-0.00620269,0.02581124,0.06552339,-0.06992423,-0.01697826,0.00280741,-0.02970835,0.05502699,0.23309003,0.00786127,0.02036954,0.02689751,-0.02144907,0.01005403,0.02337549,-0.04058142,-0.06915154,-0.03485535,0.07891592,-0.03001321,0.04623285,0.08411352,-0.02879363,-0.01112684,-0.01100281,0.03485488,-0.01027747,0.04887027,-0.04491661,0.03174157,0.07741669,-0.00998795,-0.100799,0.0048808,-0.06747164,-0.02050996,-0.00459841,0.02688068,-0.03681994,0.01033986,0.01093295,0.00541726,-0.01849152,-0.03481779,-0.02560281,-0.00721964,-0.00096411,0.06185986,0.04192105,0.09111398,-0.02678534],"last_embed":{"hash":"7aac25677f7f0a5eb5a366cedc57cda2b37ca0f66e0b08df1f1590f0ad406631","tokens":408}}},"last_read":{"hash":"7aac25677f7f0a5eb5a366cedc57cda2b37ca0f66e0b08df1f1590f0ad406631","at":1745995166646},"class_name":"SmartSource2","outlinks":[{"title":"Obsidian","target":"Obsidian","line":28},{"title":"有哪些推薦的Obsidian插件可以輔助深度研究和筆記整理","target":"有哪些推薦的Obsidian插件可以輔助深度研究和筆記整理","line":58}],"blocks":{"#":[2,60]},"last_import":{"mtime":1740968065148,"size":4683,"at":1745995103426,"hash":"7aac25677f7f0a5eb5a366cedc57cda2b37ca0f66e0b08df1f1590f0ad406631"},"key":"結合 Cursor AI IDE、Obsidian 與深度研究來創建和組織筆記.md"},"smart_blocks:結合 Cursor AI IDE、Obsidian 與深度研究來創建和組織筆記.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05469266,0.00553299,0.00472069,0.03121817,-0.02753467,0.00337489,-0.02077017,0.02350863,0.04341683,0.02423088,-0.00185277,-0.07694797,-0.00076747,0.03113005,0.0023367,0.01542755,-0.0335637,-0.01507457,0.01845584,-0.04348922,0.07465526,-0.0557513,0.00393381,-0.07188463,-0.03309465,0.02965714,0.01394881,-0.05490051,0.03150531,-0.16825017,0.06947138,-0.0221824,-0.00934923,-0.03107881,-0.0222255,-0.03480424,-0.04916089,0.01734411,-0.04260405,0.0102875,-0.00266579,0.01771822,-0.01469089,0.01702123,0.04545576,-0.00528697,-0.08331368,-0.01465853,0.00334515,-0.01267936,-0.01406367,-0.04479036,-0.04431859,0.01534829,-0.00691651,0.01480489,0.07366317,0.0433412,0.06700292,0.04536122,0.0520056,0.0432605,-0.18230851,0.06874648,0.02613045,-0.03195916,-0.02217468,-0.07919019,0.03882369,0.03715132,0.03051998,0.07148018,0.05374472,-0.01352525,0.01922842,-0.0096317,0.00441099,-0.02969491,0.00375998,-0.00823647,-0.03261114,0.09283571,-0.02630649,-0.0026271,-0.0288896,-0.00203171,-0.02024937,-0.04931397,0.05390631,-0.04948464,-0.01279779,-0.06126205,-0.00451799,0.03396639,0.00327142,0.03431119,0.01201622,0.01129021,-0.06717066,0.13564254,-0.03770196,-0.0299838,-0.03851122,-0.01240255,0.05551248,-0.02034628,-0.04080254,-0.0605208,-0.03980886,0.06261975,-0.02021798,-0.00448986,0.03733203,-0.0300178,0.04398819,0.03205743,0.03416823,0.02552134,0.01541543,-0.02991522,0.00379437,0.02358374,0.00143076,-0.01490105,0.00676147,-0.00645929,0.05424533,0.0599195,0.00861142,0.03396805,0.09542513,-0.00043401,-0.0277268,-0.02036,0.04146919,-0.00393731,-0.0499628,-0.01174268,-0.00424685,-0.01263084,-0.0641714,-0.02978827,0.0551158,-0.06801295,0.05600042,0.0885525,0.01775235,0.00733272,0.00538813,-0.09566005,0.02635816,0.06253948,-0.07173213,-0.03173857,-0.05236464,0.01472599,0.02456089,0.10190616,-0.07730825,-0.01671746,-0.00226406,-0.09734858,-0.01986916,0.1142409,-0.04428788,-0.0883804,-0.05774598,0.06836446,0.01502693,0.00206421,-0.01079083,-0.00558305,-0.06640101,0.04716207,0.06762064,-0.0164891,-0.10377546,-0.00955995,-0.05626969,-0.0017142,0.04234478,-0.05822161,-0.02813401,0.01799424,-0.02640369,-0.06249941,0.00799676,-0.05926928,0.0120397,-0.03808983,-0.0278184,0.0721747,0.01156182,0.07312935,0.01448949,-0.04497847,0.02664461,-0.02557646,0.01163992,0.02714956,0.11283115,-0.01856999,-0.02330704,-0.02538652,-0.05808662,-0.03870588,-0.02773855,-0.05334177,-0.00404403,0.06084319,-0.00309246,-0.0530192,0.06160556,0.00334366,-0.00868555,0.00829668,0.01104083,0.02225871,0.03232811,0.0632426,0.02864064,-0.03806849,-0.02843037,-0.25211999,-0.03851918,0.04366431,0.00016847,0.01420212,-0.0611254,0.03997152,-0.00335818,0.04662482,0.04555568,0.08226958,0.04249791,0.02157536,0.03318849,-0.04329598,0.0457094,0.04976955,0.00904566,-0.01657719,-0.00852097,-0.02580196,0.04442401,0.02573078,-0.07346463,0.01134031,-0.00057172,0.13308947,0.02738954,0.01381222,0.02970002,-0.00336119,0.02593058,-0.00273842,-0.12504834,0.05105106,0.02993483,-0.02847717,0.02566651,-0.0761119,-0.02343342,-0.04521437,0.07244387,0.00835569,-0.11557394,0.01048119,-0.01414405,-0.07288497,-0.01517139,0.03985036,0.0770684,0.05501219,0.06203894,0.00851184,0.05558362,-0.03585662,-0.02965764,-0.05710761,0.02051691,0.00971683,0.02149362,-0.00272151,-0.07416203,0.01013527,-0.04407395,0.06681705,0.03666122,0.04406627,0.01597062,0.06633931,0.01187178,-0.08512265,0.15956162,-0.01943281,0.00359327,0.08412279,-0.03402734,0.0057615,-0.03332989,0.02886978,0.01171979,0.02825196,0.03118929,0.02019824,0.04535859,0.02021017,0.02261688,0.01415125,0.0227867,0.06622259,-0.02333172,-0.01123278,0.04885805,-0.1039245,-0.00121502,0.01521025,0.03214892,-0.28727567,0.06764133,0.01991486,0.020294,0.02610617,-0.00014474,-0.00104972,-0.03428215,-0.00476799,-0.02788252,-0.03002778,0.00222872,-0.00591979,-0.01692381,-0.00705159,0.029494,0.0657345,-0.06964465,-0.0199807,-0.00073447,-0.03178696,0.05289819,0.23146613,0.0077809,0.01553622,0.02719773,-0.01837485,0.01199144,0.02405432,-0.04144152,-0.07365417,-0.03727565,0.07881714,-0.02607626,0.04604646,0.08376011,-0.0309196,-0.01190787,-0.01739012,0.02910955,-0.01054381,0.05491964,-0.04013004,0.03570613,0.07481744,-0.01190454,-0.09867177,0.00514385,-0.06305534,-0.02092639,-0.00731872,0.02628063,-0.03552537,0.0099579,0.01358525,0.00940557,-0.01595281,-0.0354085,-0.02364098,-0.00430205,-0.00461466,0.05825457,0.04425637,0.09099531,-0.02927308],"last_embed":{"hash":"7aac25677f7f0a5eb5a366cedc57cda2b37ca0f66e0b08df1f1590f0ad406631","tokens":408}}},"text":null,"length":0,"last_read":{"hash":"7aac25677f7f0a5eb5a366cedc57cda2b37ca0f66e0b08df1f1590f0ad406631","at":1745995166646},"key":"結合 Cursor AI IDE、Obsidian 與深度研究來創建和組織筆記.md#","lines":[2,60],"size":1922,"outlinks":[{"title":"Obsidian","target":"Obsidian","line":27},{"title":"有哪些推薦的Obsidian插件可以輔助深度研究和筆記整理","target":"有哪些推薦的Obsidian插件可以輔助深度研究和筆記整理","line":57}],"class_name":"SmartBlock"},
