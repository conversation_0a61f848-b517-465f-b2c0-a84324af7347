# Saliu Lottery System Improvement Phase 1 - Design Document

## 架構設計

### 系統架構圖

```
┌─────────────────────────────────────────────────────────────┐
│                    Wonder Grid 整合層                        │
├─────────────────────────────────────────────────────────────┤
│  WonderGridEngine  │  DynamicParameterManager  │  UIManager │
├─────────────────────────────────────────────────────────────┤
│                      核心服務層                              │
├─────────────────────────────────────────────────────────────┤
│ PairFrequencyService │ KeyNumberService │ CombinationService │
├─────────────────────────────────────────────────────────────┤
│                      數據處理層                              │
├─────────────────────────────────────────────────────────────┤
│   DataProcessor   │   CacheManager   │   FileManager       │
├─────────────────────────────────────────────────────────────┤
│                      現有系統層                              │
├─────────────────────────────────────────────────────────────┤
│  Bright6  │  Super Utilities  │  SkipSystem  │  MDIEditor  │
└─────────────────────────────────────────────────────────────┘
```

## 核心組件設計

### 1. WonderGridEngine

```julia
module WonderGridEngine

struct WonderGridConfig
    analysis_range::Int           # 分析範圍（建議為最大號碼的3倍）
    top_pair_percentage::Float64  # 頂級配對百分比（預設 25%）
    key_number_strategy::Symbol   # 關鍵號碼選擇策略 (:ffg, :skip, :frequency)
    combination_limit::Int        # 組合數量限制
    enable_purge::Bool           # 啟用 Purge 過濾
    enable_lie::Bool             # 啟用 LIE 消除
end

struct WonderGridResult
    key_number::Int
    top_pairs::Vector{Int}
    combinations::Vector{Vector{Int}}
    efficiency_metrics::Dict{String, Float64}
    generation_time::Float64
end

# 主要介面
function execute_wonder_grid_strategy(
    data::Vector{Vector{Int}}, 
    config::WonderGridConfig
)::WonderGridResult
end

function calculate_pair_frequencies(
    data::Vector{Vector{Int}}, 
    range::Int
)::Dict{Tuple{Int,Int}, Int}
end

function select_key_number(
    data::Vector{Vector{Int}}, 
    strategy::Symbol
)::Int
end

function generate_wonder_combinations(
    key_number::Int, 
    top_pairs::Vector{Int}, 
    config::WonderGridConfig
)::Vector{Vector{Int}}
end
```

### 2. DynamicParameterManager

```julia
module DynamicParameterManager

struct ParameterState
    last_update::DateTime
    pair_frequencies::Dict{Tuple{Int,Int}, Int}
    key_number_scores::Dict{Int, Float64}
    efficiency_history::Vector{Float64}
    trend_indicators::Dict{String, Float64}
end

struct AdaptiveConfig
    update_threshold::Float64     # 更新閾值
    trend_sensitivity::Float64    # 趨勢敏感度
    efficiency_target::Float64    # 目標效率
    auto_adjust::Bool            # 自動調整開關
end

function monitor_data_changes(
    current_data::Vector{Vector{Int}}, 
    previous_state::ParameterState
)::Bool
end

function adjust_parameters(
    config::WonderGridConfig, 
    state::ParameterState, 
    adaptive_config::AdaptiveConfig
)::WonderGridConfig
end

function calculate_trend_indicators(
    data::Vector{Vector{Int}}, 
    window_size::Int
)::Dict{String, Float64}
end
```

### 3. 高效能組合生成器

```julia
module HighPerformanceCombinationGenerator

struct GenerationStrategy
    method::Symbol               # :hot, :pairs, :mixed, :markov
    filter_settings::Dict{String, Any}
    optimization_level::Int      # 1-5 優化等級
    parallel_processing::Bool    # 並行處理
end

struct GenerationResult
    combinations::Vector{Vector{Int}}
    generation_stats::Dict{String, Any}
    filter_efficiency::Float64
    memory_usage::Float64
end

function generate_combinations_parallel(
    strategy::GenerationStrategy,
    data_context::Dict{String, Any}
)::GenerationResult
end

function apply_purge_filter(
    combinations::Vector{Vector{Int}},
    purge_settings::Dict{String, Any}
)::Vector{Vector{Int}}
end

function apply_lie_elimination(
    combinations::Vector{Vector{Int}},
    lie_patterns::Vector{Vector{Int}}
)::Vector{Vector{Int}}
end
```

### 4. 智能回測系統

```julia
module IntelligentBacktestSystem

struct BacktestConfig
    start_date::Date
    end_date::Date
    strategy_params::WonderGridConfig
    cost_per_combination::Float64
    prize_structure::Dict{Int, Float64}  # 中獎等級 => 獎金
end

struct BacktestResult
    total_combinations::Int
    total_cost::Float64
    total_winnings::Float64
    net_profit::Float64
    hit_rates::Dict{Int, Float64}       # 各等級中獎率
    roi::Float64                        # 投資報酬率
    risk_metrics::Dict{String, Float64}
end

function execute_backtest(
    historical_data::Vector{Vector{Int}},
    config::BacktestConfig
)::BacktestResult
end

function calculate_risk_metrics(
    results::Vector{BacktestResult}
)::Dict{String, Float64}
end

function compare_strategies(
    strategies::Vector{WonderGridConfig},
    data::Vector{Vector{Int}}
)::Dict{String, BacktestResult}
end
```

## 數據流設計

### 1. 策略執行流程

```
數據輸入 → 格式驗證 → 快取檢查 → 配對分析 → 關鍵號碼選擇 
    ↓
參數調整 → 組合生成 → 過濾優化 → 結果驗證 → 輸出報告
```

### 2. 動態更新流程

```
數據監控 → 變化檢測 → 趨勢分析 → 參數評估 → 自動調整 
    ↓
效果驗證 → 回饋學習 → 策略優化 → 狀態更新 → 通知使用者
```

## 效能優化策略

### 1. 記憶體管理
- 分段處理大型數據集
- 智能快取機制
- 垃圾回收優化
- 記憶體池技術

### 2. 計算優化
- 並行處理算法
- 向量化運算
- 預計算常用結果
- 增量更新機制

### 3. I/O 優化
- 異步檔案操作
- 批次處理機制
- 壓縮存儲格式
- 索引加速查詢

## 整合策略

### 1. 現有系統整合
- 保持檔案格式相容性
- 提供 API 橋接層
- 漸進式功能遷移
- 向後相容保證

### 2. 使用者介面整合
- 統一操作介面
- 一鍵策略執行
- 即時狀態顯示
- 智能參數建議

### 3. 報告系統整合
- 標準化報告格式
- 多格式輸出支援
- 視覺化圖表生成
- 自動化報告排程