"smart_sources:黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md": {"path":"黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md","last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142"}}},"last_read":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142","at":1750403581867},"class_name":"SmartSource","last_import":{"mtime":1750381712580,"size":23987,"at":1750384454498,"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142"},"blocks":{"#":[1,350]},"outlinks":[],"key":"黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md"},
"smart_blocks:黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142","at":1750403581868},"key":"黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md#","lines":[1,350],"size":10201,"outlinks":[],"class_name":"SmartBlock"},"smart_blocks:黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142","at":1750403581868},"key":"黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md#","lines":[1,350],"size":10201,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"f2e94d7c2b66685b3c6543b2b6c84ebfa8ee1ee2d255662e61401687dcff8142","at":1750403581868},"key":"黃霑「加減乘除」創新法則 × ChatGPT：打造高效 AI 創作流程的技術解構 - Calpa 的煉金工房.md#","lines":[1,350],"size":10201,"outlinks":[],"class_name":"SmartBlock"},
