
"smart_sources:notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md": {"path":"notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13525616,-0.01703842,-0.05900499,0.00768744,-0.00813027,0.06724038,0.01216168,-0.00185578,0.05905531,0.01288973,0.00098638,-0.06341028,0.06846968,0.04608628,0.05749666,0.00320081,-0.00938403,-0.00842302,-0.05728506,-0.0436133,0.07019255,-0.08300562,-0.01538094,-0.0754412,0.07722645,0.01542467,0.00968725,-0.03229137,0.00796268,-0.17812166,-0.00604292,-0.00031336,0.04167597,-0.02789706,-0.02720038,0.00054963,-0.03755758,0.01866884,-0.03306529,0.00254633,0.0000042,0.0041417,0.04945998,-0.02481288,0.00891691,-0.05738228,-0.02263335,-0.04955016,0.03945328,-0.01014213,-0.08125301,-0.01899861,-0.00398619,0.05806931,0.0434451,0.02199084,0.03782641,0.05512357,-0.01214009,0.00452574,0.01160384,0.04566144,-0.19715005,0.02255893,-0.00869437,-0.02259183,-0.02145171,-0.01585069,0.04823439,0.03529537,-0.03617745,0.00889385,-0.06289891,0.04329297,0.03477242,-0.0371266,-0.0292197,-0.03931199,-0.05628628,0.0032903,-0.05714871,0.01699982,-0.01412173,-0.03283751,-0.02076285,0.07230261,0.04477078,-0.02114046,-0.03092124,-0.01584583,-0.04382485,-0.00028135,-0.00611481,0.03880524,-0.02921418,0.02411111,0.03839909,0.0422954,-0.05056692,0.11446849,0.00047382,0.01663177,-0.01797479,-0.02089176,0.06170537,-0.02270199,-0.00049158,-0.0287871,-0.00978962,0.00323908,0.02543759,-0.00981558,0.0090365,-0.09531862,-0.02585652,0.02436787,0.02591994,0.01670387,-0.02050075,-0.00318594,-0.01536959,0.01593856,0.04167889,-0.00564902,-0.00848781,0.00494583,0.03120255,0.04379326,0.07576844,0.0145451,0.02487706,0.02972589,-0.08661651,-0.00829602,-0.0199141,-0.04178143,-0.02306084,-0.02703181,0.03251731,-0.01125806,-0.00630097,-0.0225509,0.01056728,-0.12291057,-0.0699109,0.07782438,-0.0485236,0.01599579,-0.00870525,-0.01150887,0.04443929,0.05046248,0.00727789,-0.05070929,0.03190098,0.0292232,0.08282039,0.13011827,-0.00413946,0.01781403,-0.00380194,-0.05009291,-0.04833388,0.16828011,0.0150037,-0.08689851,-0.00354078,0.03995107,0.009474,-0.07148679,0.06750825,0.00925027,-0.01112859,0.02664158,0.06780783,-0.05475223,-0.04345387,-0.01252118,-0.0453632,0.02460457,-0.05366886,-0.02387203,-0.06936462,0.02609027,-0.02293491,-0.08615766,0.032641,0.0134452,0.02496703,-0.01177729,-0.01320416,0.02163214,0.04263293,0.05962959,-0.06164388,-0.0402658,-0.01434296,-0.0257599,0.01166014,-0.05856658,0.06829759,-0.01311794,-0.04773901,-0.01990495,-0.05881234,-0.00061231,-0.00560125,0.01681402,0.04362265,0.05834622,-0.02523138,0.02866878,-0.01879513,0.03520979,-0.02883921,0.01748544,0.01406126,0.02978684,0.04673363,0.01981496,0.00650958,0.00039602,-0.11044874,-0.22648357,-0.00425703,-0.03011872,0.00055547,0.0239136,-0.04552432,0.00087491,-0.02261828,0.10528328,0.02996813,0.08941992,0.02976849,-0.04897491,-0.01617186,0.01327227,0.02284788,-0.01719377,0.01655361,0.00473668,0.07487732,0.00504047,0.06776019,-0.00431604,0.00732942,0.07127836,-0.06507168,0.13000804,0.02284891,-0.0080485,0.04360706,0.06041805,0.0333586,-0.00994224,-0.09895781,0.0151552,0.05805723,-0.09378731,0.01361907,-0.06500967,-0.06351404,-0.02420795,0.01873524,0.00257063,-0.08559828,0.01108587,-0.05305286,-0.00740316,-0.00581474,-0.01215956,0.02032294,0.00199871,0.01658059,0.03241497,0.03862673,0.00534421,-0.03747869,-0.0328158,-0.03963268,-0.03154058,-0.00890761,-0.00631368,-0.03316522,0.05767248,0.01736162,-0.01176501,-0.0126036,-0.01944709,0.01329385,0.005309,0.05236659,-0.05237199,0.15995042,0.01692012,0.01013364,0.02939144,-0.00546248,-0.05323613,-0.11359567,0.00965514,-0.03439838,0.02049801,0.00122091,0.04554901,0.05504503,0.09244872,0.00934776,0.0549949,0.04918379,0.03064467,0.00661257,-0.00830388,-0.04405044,-0.00809568,0.00621342,0.08150248,0.01932585,-0.31768543,0.0186387,-0.02597817,0.03244163,0.00624936,0.00636086,0.04192464,-0.03543457,-0.04303228,-0.01366063,0.0386296,0.07152735,0.03312207,-0.04981074,-0.03004869,0.02539444,0.00990374,-0.04160864,0.05580576,0.00934754,-0.01830926,-0.03058719,0.24166001,0.02370577,0.04025517,-0.01227912,0.00076585,0.0211993,0.02445053,0.00925037,-0.0111538,0.00073437,0.05753333,0.01733826,0.02921149,0.08643553,-0.0245184,0.00238503,-0.03267163,0.00209661,-0.02397341,0.0151344,-0.04926276,0.00235763,0.14828838,0.0216236,0.00696689,-0.08005258,0.00432859,0.09710944,-0.01623645,-0.04513111,-0.00716906,0.02270859,-0.01832508,0.0398486,0.01601557,-0.05695331,0.02371917,-0.0107783,0.0355423,-0.00611493,-0.00120885,0.04090356,0.02739846],"last_embed":{"hash":"1c4cvx8","tokens":459}}},"last_read":{"hash":"1c4cvx8","at":1753495720208},"class_name":"SmartSource","last_import":{"mtime":1753428072154,"size":4518,"at":1753495676981,"hash":"1c4cvx8"},"blocks":{"#":[1,35],"##{1}":[7,7],"##{2}":[8,11],"##{3}":[12,12],"##{4}":[13,13],"##{5}":[14,23],"##{6}":[24,26],"##{7}":[27,30],"##{8}":[31,31],"##{9}":[32,32],"##{10}":[33,35]},"outlinks":[]},"smart_blocks:notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13648342,-0.00900847,-0.0552207,0.006059,-0.00793758,0.06627846,0.01677479,-0.00393549,0.05607756,0.01240092,0.00098968,-0.06115869,0.06858172,0.05012299,0.05717507,-0.00002293,-0.01421334,-0.00872235,-0.0598959,-0.03907119,0.06656967,-0.08776548,-0.01629249,-0.07851088,0.07819892,0.01901791,0.00971583,-0.03044142,0.00850738,-0.17635682,-0.00476642,-0.00273218,0.04027862,-0.03121651,-0.02796894,0.00067745,-0.03578095,0.01645898,-0.03144599,-0.00483635,-0.00084659,0.00234556,0.05102007,-0.02442503,0.0124733,-0.05859663,-0.01702477,-0.0506881,0.04008471,-0.01089987,-0.084798,-0.02012597,-0.00105827,0.05426254,0.0465493,0.02854079,0.03571965,0.05796751,-0.01487935,0.00124505,0.01166574,0.04327843,-0.19603579,0.01740392,-0.00907787,-0.01972321,-0.02324132,-0.01818698,0.04474711,0.03895433,-0.0315096,0.00698399,-0.06171439,0.0418096,0.03350885,-0.03880451,-0.0283893,-0.03418419,-0.05291767,0.00430731,-0.06269377,0.01656622,-0.01677556,-0.03352077,-0.02703561,0.07100885,0.04400638,-0.0234715,-0.03320979,-0.01553645,-0.03937205,-0.00376797,-0.00503978,0.04196758,-0.02805379,0.02718172,0.03977253,0.04158013,-0.05206472,0.11590195,-0.00192938,0.02027945,-0.01816456,-0.02624226,0.0658174,-0.02257467,0.00408227,-0.03488972,-0.00705294,0.0034266,0.02090658,-0.01198363,0.01057732,-0.09137206,-0.02246324,0.02778943,0.02573619,0.01995724,-0.02746051,-0.00247528,-0.01862558,0.01954325,0.03764084,-0.00873543,-0.0151842,-0.00176436,0.02547531,0.03828488,0.07801602,0.01060853,0.02116682,0.03043897,-0.08848862,-0.00961915,-0.0181059,-0.04241018,-0.02206843,-0.0232926,0.03319133,-0.01759447,-0.00673057,-0.02437385,0.00787053,-0.12455229,-0.07220524,0.0765449,-0.049071,0.01257194,-0.00483199,-0.01462567,0.0410772,0.04605594,0.00480689,-0.04876355,0.03412518,0.02481643,0.08113851,0.13180488,-0.00313874,0.02288693,-0.00253732,-0.04798898,-0.04989892,0.1643254,0.01984749,-0.09087849,-0.00411621,0.03841296,0.0150995,-0.06749178,0.06925359,0.01315228,-0.01254864,0.02650443,0.06162276,-0.05575956,-0.04059486,-0.01611212,-0.04145808,0.02607618,-0.05362437,-0.02165758,-0.07076941,0.02256219,-0.02158873,-0.08606417,0.03110556,0.01309896,0.02290299,-0.0085443,-0.00721221,0.02695488,0.04109811,0.06070131,-0.05749828,-0.04100274,-0.01414685,-0.02893662,0.01002515,-0.06010768,0.07018685,-0.01412842,-0.04923019,-0.02118503,-0.0629104,-0.00544707,-0.00901964,0.01383229,0.04539006,0.05438178,-0.02803329,0.02807246,-0.01936642,0.04200192,-0.02985615,0.01966527,0.0139845,0.02694445,0.04500452,0.01753873,0.00779103,0.00293986,-0.10459487,-0.22435957,-0.00563348,-0.0317711,-0.0018622,0.02418962,-0.04319846,-0.00104685,-0.02318331,0.1030942,0.03189679,0.09350773,0.02983361,-0.04381848,-0.01456138,0.01383643,0.02936302,-0.01793225,0.02065824,0.00066082,0.07622783,0.00469043,0.07094108,-0.00369324,0.00400574,0.06930947,-0.06108289,0.13354594,0.02870392,-0.01043075,0.04750593,0.05924013,0.03789369,-0.00853967,-0.10562415,0.01677677,0.05978447,-0.09577311,0.0039759,-0.06283637,-0.06410599,-0.01882783,0.01914537,0.00215202,-0.08672883,0.01427596,-0.05168742,-0.00785584,0.00176151,-0.01211133,0.02785422,-0.0015374,0.0148088,0.03415664,0.03626639,0.00214549,-0.03800862,-0.03087802,-0.04027213,-0.0296354,-0.00760442,-0.00324413,-0.03339621,0.05520438,0.02100333,-0.00746955,-0.01646552,-0.02221263,0.01094809,0.00403054,0.0518639,-0.05493918,0.15241309,0.01443994,0.01660707,0.02852438,-0.00262953,-0.05153348,-0.11341105,0.01099142,-0.0401351,0.02057407,-0.00019382,0.04475236,0.05463319,0.09266146,0.00657944,0.05411859,0.04586628,0.03597491,0.0070416,-0.01021056,-0.04736915,-0.0169851,0.00834563,0.08290139,0.02159833,-0.31591856,0.0235054,-0.02360467,0.03136779,0.00477556,0.00885501,0.04546332,-0.03302201,-0.03572467,-0.01906357,0.0422425,0.07500466,0.03485117,-0.05069503,-0.03136827,0.02095156,0.0104308,-0.04138114,0.05265979,0.01229332,-0.017101,-0.03209436,0.23888668,0.02637456,0.04217714,-0.01747503,0.00025357,0.01897595,0.0248084,0.00905555,-0.01042024,0.00113709,0.05968518,0.01696894,0.03150463,0.08640115,-0.02318377,0.0039147,-0.02899095,0.00409422,-0.02605258,0.01978013,-0.05003949,-0.00105183,0.15095255,0.02572874,0.00989513,-0.07707033,0.00611753,0.10049983,-0.01275374,-0.0454178,-0.00663407,0.02563182,-0.01713341,0.03736531,0.01556339,-0.05874171,0.01859734,-0.00539434,0.03508427,-0.00689022,0.00015721,0.04530048,0.02775917],"last_embed":{"hash":"1c4cvx8","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"1c4cvx8","at":1753495719855},"key":"notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md#","lines":[1,35],"size":2122,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md##{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12252495,-0.02287332,-0.05600896,-0.03914885,-0.03053343,0.04312368,-0.00347185,0.00007769,0.02857217,0.03155867,0.0141275,-0.06060456,0.06745233,0.0328188,0.04286775,-0.00366524,0.00745192,-0.01176781,-0.03291187,-0.05426132,0.02012815,-0.0819246,-0.02589782,-0.07595523,0.04945984,0.01666623,0.04350926,-0.04912694,0.00222411,-0.17335376,-0.00184922,-0.03756341,0.0309196,-0.00908642,0.00042364,0.02059182,-0.01756554,0.02088037,-0.00932433,0.00470884,-0.02103084,0.01105122,0.06834197,0.00289148,0.03394182,-0.06054372,-0.01516284,-0.02249155,0.02296078,-0.03106573,-0.08592132,-0.0274577,0.02369889,0.02204761,0.04751261,0.02422501,0.01556848,0.01977788,-0.03585587,-0.00241536,0.04384479,0.05540108,-0.19074532,0.04136165,-0.02050006,0.00014081,-0.04439078,-0.00898003,0.04812765,0.05525745,-0.01333582,0.00011976,-0.06277989,0.04106237,0.03937498,-0.06611536,-0.01688107,-0.02775473,-0.05997664,0.01263858,-0.04956578,0.03398586,-0.01575919,-0.03984511,-0.01343499,0.07067984,0.06147502,-0.06449362,-0.05628712,-0.01958192,-0.04884151,0.01026104,-0.01282683,0.01382208,-0.01767669,0.03225639,0.02782682,0.031076,-0.03435112,0.10535872,-0.04269128,0.04212525,-0.02402523,-0.03180589,0.06474099,-0.0478752,0.00692344,-0.03740677,-0.00276114,-0.00640806,-0.00259811,-0.00775257,0.00845512,-0.0922191,0.00222243,0.04517815,0.00900824,0.06240064,-0.03534623,0.00303914,0.01275807,0.0009435,0.06136148,0.02207507,-0.0068787,-0.01977837,-0.02085036,0.05223811,0.07655744,0.03690425,0.05423411,0.04025159,-0.04416393,0.00288007,0.0037671,-0.01844119,-0.05259022,-0.03197824,0.03172181,-0.02347598,-0.01370781,-0.05671269,-0.00747921,-0.09741604,-0.04389747,0.10810279,-0.06049962,0.02314968,-0.01293154,-0.00083252,0.02580854,0.02976618,-0.02222965,-0.0550593,0.02924328,-0.00014486,0.06583443,0.09000288,-0.01818121,-0.00261089,0.00010886,-0.06153914,-0.06180047,0.17350052,0.03404003,-0.08840887,0.0072579,0.01121334,0.01116751,-0.06710961,0.06755934,-0.01070859,-0.01683679,0.0277613,0.08458548,-0.04255198,-0.03460268,0.00168855,-0.01193715,0.02311876,-0.05069961,-0.01972774,-0.08313011,0.03462005,-0.03996209,-0.03172416,0.0193402,0.01304673,0.03769132,-0.01485791,-0.00613634,-0.01278542,-0.00633937,0.01268634,-0.06397285,-0.03109742,-0.01734442,-0.00342594,0.02108191,-0.07810164,0.06116462,0.01685496,-0.04818254,-0.03939104,-0.04716621,0.00185091,0.02115374,0.01254286,0.01610474,0.03231324,-0.0243881,0.01828549,0.000013,0.0116016,-0.05401443,0.01669999,0.02483845,0.03618288,0.033002,0.01632475,-0.02194791,-0.00394681,-0.08309423,-0.2153423,0.00498073,0.01640598,-0.02174736,0.03317901,-0.07258999,-0.03875563,-0.02592902,0.10059237,0.01589411,0.1099275,0.00543034,-0.03148137,0.00694855,0.02420532,0.06712802,0.00201015,0.04310573,0.00590155,0.08279371,-0.01258324,0.04919568,-0.03020925,-0.00551658,0.06710527,-0.04703118,0.1141893,0.06485905,-0.01372717,0.04363301,0.0572811,0.04031817,-0.03613738,-0.1395749,0.03787861,0.03256347,-0.06353141,-0.01755979,-0.01709263,-0.07885958,-0.00303011,0.01810629,-0.00485367,-0.07673386,0.00307319,-0.06657416,-0.04469465,0.01769798,-0.01335291,0.0497608,-0.00220635,0.01472373,0.04779234,0.03708882,-0.01016513,-0.06874818,-0.01816165,-0.05660698,-0.06022962,0.02067632,-0.01875552,-0.03743751,0.06319443,0.02906011,0.01297571,-0.02150065,-0.03606241,-0.00584642,0.0266333,0.05285584,-0.04519231,0.13493478,0.02398885,0.01230852,0.0210113,-0.0195707,-0.06990677,-0.09115366,-0.02092412,-0.05496865,0.01985919,-0.00437871,0.04474396,0.02934199,0.05993924,-0.00128423,0.05806816,0.0433583,0.04029811,0.01836115,-0.00060072,-0.03178691,-0.01141836,0.02812055,0.08844402,0.02010961,-0.31939405,0.0175298,-0.0085937,0.01745493,0.00698252,0.01098251,0.05518468,-0.04107096,-0.04515643,-0.01873645,0.0165361,0.08364256,0.03360668,-0.01898622,-0.00706617,0.0103671,0.00142201,-0.00203791,0.08827505,-0.01417418,0.00506379,-0.01991374,0.2440574,-0.00416495,0.05904787,-0.01421699,0.03542488,0.04899668,0.06369186,-0.00305147,-0.01753305,-0.01200126,0.05008747,0.01637291,0.04273583,0.0709713,-0.01171931,0.03885219,-0.01944389,0.02902131,-0.0266121,-0.00689257,-0.04239648,0.01238535,0.15935063,0.00652072,0.00773996,-0.06210175,0.01269813,0.08304396,0.00944287,-0.05142716,0.00052492,0.03756205,-0.01786036,0.0299042,0.02172616,-0.06411678,0.01358071,0.02602991,0.03756283,-0.01589471,0.0176686,0.09776118,0.02587016],"last_embed":{"hash":"qxo8he","tokens":361}}},"text":null,"length":0,"last_read":{"hash":"qxo8he","at":1753495720025},"key":"notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md##{5}","lines":[14,23],"size":488,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md##{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13849415,-0.00462578,-0.04112872,-0.00232508,-0.02368506,0.04627619,-0.02537642,-0.03054689,0.04825871,0.00509299,-0.00319504,-0.06565212,0.08800306,0.03293624,0.06680162,0.02164195,-0.01193999,0.00667032,-0.05522318,-0.05493037,0.03715714,-0.04762444,-0.05806297,-0.08566743,0.05015806,0.03836058,0.03798323,-0.02765358,0.03431509,-0.19468282,0.00341273,0.01069432,0.01854215,0.0046226,-0.03099739,0.00052833,-0.0203982,0.00505058,-0.04502466,0.025651,0.00452617,0.02325605,0.07485599,-0.0121633,0.02096346,-0.02582985,-0.02452778,-0.00307064,0.02070364,-0.02275326,-0.05599502,-0.02455892,0.0025757,0.00871537,0.03480951,0.04264345,0.04171315,0.05080315,-0.05994241,0.00159849,0.03545227,0.09346169,-0.16822375,0.02617138,0.00675852,-0.00084104,-0.0229659,0.00304337,0.07614408,0.0958849,-0.04105198,0.01542317,-0.06513184,0.05641998,0.01831051,-0.01170269,-0.01489683,-0.03942587,-0.03959195,0.03528947,-0.0853335,0.01972301,-0.02127201,-0.03597427,-0.01676236,0.06147679,0.0103013,-0.03082853,-0.0197603,-0.01462825,-0.05557094,0.00731125,-0.00030259,-0.00082025,-0.00659186,0.0262597,0.04772834,0.02389201,-0.01716264,0.13427019,-0.03579861,0.03211349,0.01140259,-0.04391538,0.02722444,-0.03978749,-0.03375578,-0.03777143,0.00662633,-0.04577611,0.01781948,-0.02110641,-0.0016396,-0.10970196,-0.04151616,0.03665577,0.01527746,0.0631961,-0.03826557,-0.01284169,-0.01141224,0.02332747,0.0365925,0.01160906,-0.00412003,-0.03780619,-0.03406899,0.0718467,0.06387298,-0.0031534,0.02239036,0.03462095,-0.03825521,-0.02487556,-0.02998493,-0.08435749,-0.03616839,-0.01919493,0.04849835,-0.0094136,-0.00312176,-0.05598615,-0.0085847,-0.08742294,-0.02356501,0.10252886,-0.03478667,0.03424369,-0.04319645,0.01618632,0.04444587,0.00962458,0.01175681,-0.04288886,0.03913128,0.03608686,0.08123241,0.09459119,0.00822052,0.03404507,-0.01519468,-0.0409704,-0.07339463,0.16737153,-0.00498747,-0.08794224,0.01561886,0.04596429,0.02435075,-0.05962352,0.05851028,0.01310232,0.01644376,0.02397573,0.06424601,-0.07607032,-0.05954029,0.0125163,-0.03044484,0.01981432,-0.04256092,-0.01419504,-0.08036114,0.03415941,-0.02490572,-0.06906927,-0.00010967,0.03319138,0.01910813,-0.00274519,-0.02160413,-0.0046874,0.0341803,0.03927749,-0.05684543,-0.03502531,-0.00298249,-0.01632194,0.04794662,-0.061761,0.08188302,-0.00081161,-0.0183468,-0.02211088,-0.03302907,-0.03303621,0.00789935,-0.02032398,0.00989796,0.0807412,-0.00112961,0.01307506,-0.00296037,0.05785242,-0.03201817,0.03381674,0.01605247,0.05839725,0.02143497,-0.00025516,-0.00149622,-0.03479705,-0.07752332,-0.19748096,0.01154194,-0.00415403,-0.00655449,0.03001287,-0.04912528,-0.03201706,-0.00487674,0.04837594,0.01560176,0.04934833,0.02766195,-0.05874443,-0.01983911,0.00184899,0.04113071,-0.03371763,0.01512513,0.00413451,0.09711909,-0.00933448,0.03654516,0.0025467,0.00108128,0.08136501,-0.0479677,0.12540206,0.01246766,-0.00740879,0.06503691,0.04654819,0.02402489,-0.01618986,-0.11967313,0.04094072,0.04219904,-0.04757244,-0.02310822,-0.00698068,-0.06927072,0.03153022,-0.0075504,-0.01998412,-0.10345222,0.01724259,-0.06644847,-0.05167928,-0.00508205,-0.00085061,0.02599415,-0.03236188,0.01295772,0.03619741,0.02669069,-0.02546605,-0.04312368,-0.05496665,-0.02353311,-0.03417065,0.00327231,0.00498811,-0.0329368,0.06417777,0.02267054,-0.00211651,-0.02859288,-0.02032091,-0.02733797,0.00277512,0.03644409,-0.06000082,0.15607896,0.02639053,-0.0190216,0.04464585,-0.01964802,-0.07757928,-0.11994065,-0.00544704,-0.03797776,0.01387997,-0.03195886,0.05726545,0.06844398,0.05939652,-0.01864408,0.02940312,0.04242314,0.04366073,-0.01362598,-0.00327731,-0.04455193,0.01952659,0.0318513,0.06157641,0.00592177,-0.30117863,0.04814819,-0.00599756,0.0403215,-0.0145411,0.0013339,0.06506471,-0.05274697,-0.07822379,-0.01008336,0.03036034,0.05220884,0.04630582,-0.04066426,0.03564525,0.01056922,0.04335089,-0.05671688,0.04491223,0.00235099,0.00682928,-0.00723075,0.24960956,0.02810176,0.06187356,0.01899051,0.01375425,0.0285025,0.06157476,-0.02142821,0.0079051,-0.00319299,0.02527979,-0.01858495,0.06326435,0.07621285,-0.00776235,-0.00780563,-0.0131675,0.02998003,-0.03260937,-0.01767457,-0.04365045,0.00632749,0.13681422,0.03164632,-0.00611117,-0.07894547,-0.00687698,0.06329536,-0.01132227,-0.03550043,0.03169099,0.01979022,0.00010372,0.03979377,0.0054289,-0.05191171,0.00410848,-0.00232226,0.07643853,-0.00714453,-0.00589088,0.08068388,-0.01006372],"last_embed":{"hash":"kbl6rc","tokens":220}}},"text":null,"length":0,"last_read":{"hash":"kbl6rc","at":1753495720146},"key":"notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md##{6}","lines":[24,26],"size":321,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md##{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12908924,-0.00669404,-0.0366493,0.0018292,-0.00858422,0.06013613,0.02680477,0.01574286,0.07601836,0.01648239,-0.02962906,-0.06927498,0.0357595,0.03283437,0.03837713,-0.02394041,-0.00222262,-0.0220325,-0.01961076,-0.06017661,0.08296999,-0.05271832,-0.03533487,-0.06805965,0.0633219,0.0104746,0.0200175,-0.02028355,0.00816074,-0.15679312,-0.01376443,-0.0148076,0.00709512,-0.01799219,-0.04331342,-0.0079923,-0.02186095,0.01852043,-0.03622319,0.04755986,0.02333868,0.01015769,0.0433367,-0.00701479,0.03971402,-0.05427263,-0.03133599,-0.01622128,0.04952655,-0.03835752,-0.05957795,-0.00699289,0.01078613,0.01536576,0.02853009,-0.01663036,0.04612122,0.06302227,0.00650269,0.0197685,0.00403553,0.05351745,-0.20619799,0.01938356,-0.00523864,-0.00449333,-0.05164415,0.00481953,0.05488954,0.05496163,-0.05643216,0.00881381,-0.0435365,0.01314147,0.0445141,-0.05891135,0.00053844,-0.03624988,-0.06879923,0.03587595,-0.03584684,0.03455797,0.0046471,-0.06938396,0.00983044,0.06289575,0.00994907,-0.05050962,0.00101952,-0.01119201,-0.01504494,0.02262027,-0.00130894,0.01315083,-0.03077307,0.02693987,0.04174874,0.0355289,-0.03495566,0.10382243,0.01185467,0.01944163,-0.01852851,-0.03098615,0.06749791,-0.04407753,0.00624013,-0.02157667,-0.00127021,-0.02385704,-0.01987745,0.00910838,0.03073019,-0.05733095,-0.01968143,0.04092404,0.01737818,0.01418575,-0.03860943,-0.00167402,0.01336844,0.02269907,0.00615546,0.01374476,-0.02049534,-0.0273044,-0.00616625,0.04178273,0.05491674,-0.00926387,0.04100358,0.00093434,-0.06757025,-0.01217342,-0.01563358,-0.01422663,-0.05218279,0.02455507,0.05236918,0.00829875,-0.02407937,-0.06144413,0.00130712,-0.11893892,-0.06277268,0.09368875,-0.01565364,-0.01614299,-0.04034753,-0.00300055,0.03262057,0.04665128,0.0007009,-0.04458931,0.02581477,-0.00185588,0.07384681,0.12919043,-0.00121471,-0.01249327,-0.03139479,-0.07106835,-0.02890485,0.1670911,-0.00292212,-0.07110918,0.00614063,0.02613976,0.00767191,-0.09072968,0.05567141,-0.03178485,-0.04716565,0.03646363,0.08140545,-0.04514576,0.00602666,0.00676507,-0.0366962,0.01206729,-0.02307679,-0.04231281,-0.08899236,0.02645332,-0.01189834,-0.06371006,-0.00220251,-0.01251694,0.02064624,-0.03567775,-0.03721812,-0.00505922,-0.01045083,0.04802227,-0.08908813,-0.03356343,-0.02560301,-0.00821947,0.0134607,-0.04498022,0.08135005,-0.01965528,-0.02403709,-0.03364851,-0.04525867,0.00703487,0.00210219,-0.01640476,0.01298854,0.05047321,-0.02593536,0.03303918,-0.02030665,0.03827506,0.004022,0.04210234,0.00237174,0.03960154,-0.00649898,0.02481201,0.02159819,-0.00269912,-0.09580483,-0.23340377,-0.02375332,-0.02004623,-0.02657525,0.01744249,-0.01499464,-0.01736067,-0.01703679,0.09770918,0.05559617,0.05748723,0.03040862,-0.06170596,-0.01851561,-0.01016039,0.02314894,-0.01966874,-0.00914543,-0.02252799,0.08165598,-0.00458588,0.03711632,-0.03226609,0.00067597,0.06782155,-0.03563622,0.14657629,-0.00858073,-0.00456008,0.04851573,0.06710355,0.01716479,-0.05235247,-0.09524325,0.01905293,0.01867925,-0.09182484,0.01687711,-0.04881078,-0.07623283,-0.00446139,0.03074718,-0.00331789,-0.06781553,-0.01542595,-0.04446949,-0.0340406,-0.00193299,0.00148734,0.06395441,0.02377199,0.01513125,0.07201377,0.02709865,0.00720109,-0.03454142,0.00446114,-0.06872569,-0.05955615,0.01702169,-0.00632613,-0.02179715,0.06383232,0.03846506,0.01761183,-0.01086826,-0.01150589,-0.0389798,0.00775675,0.05944672,-0.07213947,0.15472151,0.05000881,-0.04172265,0.05927517,0.00935094,-0.05616039,-0.111251,-0.00534973,-0.02043749,0.04152336,0.00699894,0.06969591,0.07763709,0.05975065,0.02817452,0.07136549,0.03783549,0.00672232,-0.00305893,-0.03106408,-0.03647616,-0.02754558,0.01835402,0.05794923,0.01889268,-0.33009151,0.02685713,0.00536347,0.01245721,0.02835674,-0.00505796,0.03158146,-0.03446336,-0.06702434,0.00368165,0.0345592,0.06927461,0.03832509,-0.02594041,-0.04568107,0.02839726,0.01652814,-0.01754111,0.08264747,0.02932845,-0.02361136,0.00751898,0.24629383,-0.00037921,0.0395041,-0.00423505,0.01182104,0.04897781,0.04594479,-0.00141378,0.00052793,0.00817948,0.0457042,0.05796377,0.06681186,0.05417383,-0.01753202,0.03494971,0.00476348,0.00414168,-0.00276851,0.0022107,-0.06350745,0.01236376,0.13535769,0.02297964,-0.00769359,-0.06125462,-0.00303454,0.04982035,-0.0089405,-0.05865104,0.03116597,0.00837234,-0.01771829,0.00585162,-0.00447937,-0.04686758,0.0435883,0.0211134,0.02396219,0.02625081,-0.00880872,0.09008113,0.02815947],"last_embed":{"hash":"zzw5gv","tokens":320}}},"text":null,"length":0,"last_read":{"hash":"zzw5gv","at":1753495720208},"key":"notes/saliu/說明 Markov Chains 軟體如何利用「追隨者」生成組合.md##{10}","lines":[33,35],"size":339,"outlinks":[],"class_name":"SmartBlock"},
