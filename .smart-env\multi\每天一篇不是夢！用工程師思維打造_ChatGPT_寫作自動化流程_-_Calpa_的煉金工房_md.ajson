"smart_sources:每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md": {"path":"每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md","last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2"}}},"last_read":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2","at":1750403579957},"class_name":"SmartSource","last_import":{"mtime":1750381751242,"size":29310,"at":1750384454495,"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2"},"blocks":{"#":[1,379]},"outlinks":[],"key":"每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md"},
"smart_blocks:每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2","at":1750403579958},"key":"每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md#","lines":[1,379],"size":11937,"outlinks":[],"class_name":"SmartBlock"},"smart_blocks:每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2","at":1750403579958},"key":"每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md#","lines":[1,379],"size":11937,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md#": {"path":null,"last_embed":{"hash":null},"embeddings":{"TaylorAI/bge-micro-v2":{"last_embed":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2"},"vec":null}},"text":null,"length":0,"last_read":{"hash":"ec63af74fe1e5ed41689a31957bfc20a0d9f290bca8d47b2b6982eb7eaa27bc2","at":1750403579958},"key":"每天一篇不是夢！用工程師思維打造 ChatGPT 寫作自動化流程 - Calpa 的煉金工房.md#","lines":[1,379],"size":11937,"outlinks":[],"class_name":"SmartBlock"},
