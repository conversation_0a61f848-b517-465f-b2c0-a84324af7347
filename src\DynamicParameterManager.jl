# src/DynamicParameterManager.jl

module DynamicParameterManager

using Dates
using Statistics
using Printf
using ..SaliuLottery: Drawing, GameConfiguration, GAME_CONFIGURATIONS
using ..WonderGridEngine: WonderGridConfig, WonderGridError

export ParameterState, AdaptiveConfig, ParameterMonitor, 
       monitor_data_changes, calculate_trend_indicators, 
       update_parameter_state, should_trigger_update,
       create_parameter_monitor, get_monitoring_report

# 參數狀態結構
struct ParameterState
    last_update::DateTime
    pair_frequencies::Dict{Tuple{Int,Int}, Int}
    key_number_scores::Dict{Int, Float64}
    efficiency_history::Vector{Float64}
    trend_indicators::Dict{String, Float64}
    data_checksum::UInt64
    update_count::Int
    
    function ParameterState(
        pair_frequencies::Dict{Tuple{Int,Int}, Int} = Dict{Tuple{Int,Int}, Int}(),
        key_number_scores::Dict{Int, Float64} = Dict{Int, Float64}(),
        efficiency_history::Vector{Float64} = Float64[],
        trend_indicators::Dict{String, Float64} = Dict{String, Float64}()
    )
        new(now(), pair_frequencies, key_number_scores, efficiency_history, 
            trend_indicators, 0x0, 0)
    end
end

# 自適應配置結構
struct AdaptiveConfig
    update_threshold::Float64     # 更新閾值
    trend_sensitivity::Float64    # 趨勢敏感度
    efficiency_target::Float64    # 目標效率
    auto_adjust::Bool            # 自動調整開關
    monitoring_window::Int       # 監控窗口大小
    min_update_interval::Int     # 最小更新間隔（小時）
    max_history_length::Int      # 最大歷史記錄長度
    
    function AdaptiveConfig(;
        update_threshold::Float64 = 0.15,
        trend_sensitivity::Float64 = 0.1,
        efficiency_target::Float64 = 0.75,
        auto_adjust::Bool = true,
        monitoring_window::Int = 50,
        min_update_interval::Int = 24,
        max_history_length::Int = 1000
    )
        new(update_threshold, trend_sensitivity, efficiency_target, 
            auto_adjust, monitoring_window, min_update_interval, max_history_length)
    end
end

# 參數監控器結構
mutable struct ParameterMonitor
    current_state::ParameterState
    config::AdaptiveConfig
    change_history::Vector{Dict{String, Any}}
    alert_log::Vector{String}
    last_alert_time::DateTime
    
    function ParameterMonitor(config::AdaptiveConfig = AdaptiveConfig())
        new(ParameterState(), config, Vector{Dict{String, Any}}(), 
            Vector{String}(), DateTime(1900))
    end
end

"""
創建參數監控器
"""
function create_parameter_monitor(config::AdaptiveConfig = AdaptiveConfig())::ParameterMonitor
    monitor = ParameterMonitor(config)
    println("📊 參數監控器已創建")
    println("  - 更新閾值: $(config.update_threshold)")
    println("  - 趨勢敏感度: $(config.trend_sensitivity)")
    println("  - 監控窗口: $(config.monitoring_window)")
    return monitor
end

"""
監控數據變化
"""
function monitor_data_changes(
    current_data::Vector{Drawing},
    previous_state::ParameterState,
    config::AdaptiveConfig
)::Bool
    
    if isempty(current_data)
        return false
    end
    
    # 計算數據校驗和
    current_checksum = calculate_data_checksum(current_data)
    
    # 檢查數據是否有變化
    if current_checksum == previous_state.data_checksum
        return false
    end
    
    println("🔍 檢測到數據變化")
    
    # 計算變化程度
    change_magnitude = calculate_change_magnitude(current_data, previous_state, config)
    
    # 判斷是否需要更新
    should_update = change_magnitude >= config.update_threshold
    
    if should_update
        println("📈 變化程度: $(round(change_magnitude, digits=3)) (閾值: $(config.update_threshold))")
        println("✅ 觸發參數更新")
    else
        println("📉 變化程度: $(round(change_magnitude, digits=3)) (未達閾值)")
    end
    
    return should_update
end

"""
計算趨勢指標
"""
function calculate_trend_indicators(
    data::Vector{Drawing},
    window_size::Int = 50
)::Dict{String, Float64}
    
    if length(data) < window_size
        @warn "數據量不足以計算趨勢指標"
        return Dict{String, Float64}()
    end
    
    recent_data = data[1:min(window_size, length(data))]
    indicators = Dict{String, Float64}()
    
    # 計算號碼頻率趨勢
    number_frequencies = calculate_number_frequency_trend(recent_data)
    indicators["avg_number_frequency"] = mean(values(number_frequencies))
    indicators["max_number_frequency"] = maximum(values(number_frequencies))
    indicators["frequency_variance"] = var(collect(values(number_frequencies)))
    
    # 計算配對頻率趨勢
    pair_frequencies = calculate_pair_frequency_trend(recent_data)
    if !isempty(pair_frequencies)
        indicators["avg_pair_frequency"] = mean(values(pair_frequencies))
        indicators["max_pair_frequency"] = maximum(values(pair_frequencies))
        indicators["pair_variance"] = var(collect(values(pair_frequencies)))
    end
    
    # 計算跳躍值趨勢
    skip_trends = calculate_skip_trend(recent_data)
    indicators["avg_skip_value"] = skip_trends["average"]
    indicators["skip_volatility"] = skip_trends["volatility"]
    indicators["skip_trend_direction"] = skip_trends["direction"]
    
    # 計算和值趨勢
    sum_trend = calculate_sum_trend(recent_data)
    indicators["avg_sum"] = sum_trend["average"]
    indicators["sum_variance"] = sum_trend["variance"]
    indicators["sum_trend"] = sum_trend["trend"]
    
    # 計算奇偶比例趨勢
    odd_even_trend = calculate_odd_even_trend(recent_data)
    indicators["odd_ratio"] = odd_even_trend["odd_ratio"]
    indicators["even_ratio"] = odd_even_trend["even_ratio"]
    indicators["balance_score"] = odd_even_trend["balance_score"]
    
    println("📊 趨勢指標計算完成，共 $(length(indicators)) 個指標")
    
    return indicators
end

"""
更新參數狀態
"""
function update_parameter_state(
    monitor::ParameterMonitor,
    current_data::Vector{Drawing},
    new_efficiency::Float64
)::ParameterState
    
    println("🔄 更新參數狀態...")
    
    # 計算新的趨勢指標
    new_indicators = calculate_trend_indicators(current_data, monitor.config.monitoring_window)
    
    # 計算新的配對頻率
    new_pair_frequencies = calculate_current_pair_frequencies(current_data)
    
    # 計算新的關鍵號碼評分
    new_key_scores = calculate_current_key_scores(current_data)
    
    # 更新效率歷史
    new_efficiency_history = copy(monitor.current_state.efficiency_history)
    push!(new_efficiency_history, new_efficiency)
    
    # 限制歷史長度
    if length(new_efficiency_history) > monitor.config.max_history_length
        new_efficiency_history = new_efficiency_history[end-monitor.config.max_history_length+1:end]
    end
    
    # 計算數據校驗和
    new_checksum = calculate_data_checksum(current_data)
    
    # 創建新狀態
    new_state = ParameterState(
        new_pair_frequencies,
        new_key_scores,
        new_efficiency_history,
        new_indicators
    )
    
    # 更新校驗和和計數
    new_state = ParameterState(
        now(),
        new_state.pair_frequencies,
        new_state.key_number_scores,
        new_state.efficiency_history,
        new_state.trend_indicators,
        new_checksum,
        monitor.current_state.update_count + 1
    )
    
    # 記錄變化歷史
    change_record = Dict{String, Any}(
        "timestamp" => now(),
        "efficiency_change" => new_efficiency - (isempty(monitor.current_state.efficiency_history) ? 0.0 : monitor.current_state.efficiency_history[end]),
        "indicator_changes" => calculate_indicator_changes(monitor.current_state.trend_indicators, new_indicators),
        "update_count" => new_state.update_count
    )
    
    push!(monitor.change_history, change_record)
    
    # 限制變化歷史長度
    if length(monitor.change_history) > monitor.config.max_history_length
        monitor.change_history = monitor.change_history[end-monitor.config.max_history_length+1:end]
    end
    
    monitor.current_state = new_state
    
    println("✅ 參數狀態更新完成 (第 $(new_state.update_count) 次更新)")
    
    return new_state
end

"""
判斷是否應該觸發更新
"""
function should_trigger_update(
    monitor::ParameterMonitor,
    current_data::Vector{Drawing}
)::Bool
    
    # 檢查最小更新間隔
    time_since_last_update = now() - monitor.current_state.last_update
    min_interval = Hour(monitor.config.min_update_interval)
    
    if time_since_last_update < min_interval
        return false
    end
    
    # 檢查數據變化
    data_changed = monitor_data_changes(current_data, monitor.current_state, monitor.config)
    
    if !data_changed
        return false
    end
    
    # 檢查效率趨勢
    if !isempty(monitor.current_state.efficiency_history)
        recent_efficiency = monitor.current_state.efficiency_history[end]
        if recent_efficiency < monitor.config.efficiency_target
            println("⚠️  效率低於目標，建議更新參數")
            return true
        end
    end
    
    return data_changed
end

"""
獲取監控報告
"""
function get_monitoring_report(monitor::ParameterMonitor)::Dict{String, Any}
    
    state = monitor.current_state
    config = monitor.config
    
    report = Dict{String, Any}(
        "monitoring_status" => "active",
        "last_update" => state.last_update,
        "update_count" => state.update_count,
        "current_indicators" => state.trend_indicators,
        "efficiency_history_length" => length(state.efficiency_history),
        "recent_efficiency" => isempty(state.efficiency_history) ? 0.0 : state.efficiency_history[end],
        "change_history_length" => length(monitor.change_history),
        "alert_count" => length(monitor.alert_log),
        "configuration" => Dict(
            "update_threshold" => config.update_threshold,
            "trend_sensitivity" => config.trend_sensitivity,
            "efficiency_target" => config.efficiency_target,
            "auto_adjust" => config.auto_adjust,
            "monitoring_window" => config.monitoring_window
        )
    )
    
    # 計算效率統計
    if !isempty(state.efficiency_history)
        report["efficiency_stats"] = Dict(
            "average" => mean(state.efficiency_history),
            "maximum" => maximum(state.efficiency_history),
            "minimum" => minimum(state.efficiency_history),
            "variance" => var(state.efficiency_history),
            "trend" => calculate_efficiency_trend(state.efficiency_history)
        )
    end
    
    return report
end

# 輔助函數
function calculate_data_checksum(data::Vector{Drawing})::UInt64
    # 簡單的校驗和計算
    checksum = 0x0
    for drawing in data
        for number in drawing.numbers
            checksum = checksum ⊻ hash(number)
        end
        checksum = checksum ⊻ hash(drawing.id)
    end
    return checksum
end

function calculate_change_magnitude(
    current_data::Vector{Drawing},
    previous_state::ParameterState,
    config::AdaptiveConfig
)::Float64
    
    if isempty(current_data)
        return 0.0
    end
    
    # 計算當前趨勢指標
    current_indicators = calculate_trend_indicators(current_data, config.monitoring_window)
    
    if isempty(previous_state.trend_indicators) || isempty(current_indicators)
        return 1.0  # 如果沒有歷史數據，認為變化很大
    end
    
    # 計算指標變化程度
    total_change = 0.0
    common_keys = intersect(keys(current_indicators), keys(previous_state.trend_indicators))
    
    for key in common_keys
        current_val = current_indicators[key]
        previous_val = previous_state.trend_indicators[key]
        
        if previous_val != 0
            relative_change = abs(current_val - previous_val) / abs(previous_val)
            total_change += relative_change
        end
    end
    
    return length(common_keys) > 0 ? total_change / length(common_keys) : 0.0
end

# 更多輔助函數
function calculate_number_frequency_trend(data::Vector{Drawing})::Dict{Int, Int}
    frequencies = Dict{Int, Int}()

    for drawing in data
        for number in drawing.numbers
            frequencies[number] = get(frequencies, number, 0) + 1
        end
    end

    return frequencies
end

function calculate_pair_frequency_trend(data::Vector{Drawing})::Dict{Tuple{Int,Int}, Int}
    frequencies = Dict{Tuple{Int,Int}, Int}()

    for drawing in data
        numbers = drawing.numbers
        for i in 1:length(numbers)
            for j in (i+1):length(numbers)
                pair = (min(numbers[i], numbers[j]), max(numbers[i], numbers[j]))
                frequencies[pair] = get(frequencies, pair, 0) + 1
            end
        end
    end

    return frequencies
end

function calculate_skip_trend(data::Vector{Drawing})::Dict{String, Float64}
    if length(data) < 2
        return Dict("average" => 0.0, "volatility" => 0.0, "direction" => 0.0)
    end

    # 簡化的跳躍值計算
    skip_values = Float64[]

    for i in 2:length(data)
        current_sum = sum(data[i].numbers)
        previous_sum = sum(data[i-1].numbers)
        skip_val = abs(current_sum - previous_sum)
        push!(skip_values, skip_val)
    end

    avg_skip = mean(skip_values)
    volatility = std(skip_values)

    # 計算趨勢方向（正值表示上升趨勢）
    direction = 0.0
    if length(skip_values) >= 3
        recent_avg = mean(skip_values[end-2:end])
        early_avg = mean(skip_values[1:3])
        direction = (recent_avg - early_avg) / early_avg
    end

    return Dict(
        "average" => avg_skip,
        "volatility" => volatility,
        "direction" => direction
    )
end

function calculate_sum_trend(data::Vector{Drawing})::Dict{String, Float64}
    sums = [sum(drawing.numbers) for drawing in data]

    return Dict(
        "average" => mean(sums),
        "variance" => var(sums),
        "trend" => length(sums) >= 2 ? (sums[end] - sums[1]) / length(sums) : 0.0
    )
end

function calculate_odd_even_trend(data::Vector{Drawing})::Dict{String, Float64}
    total_numbers = 0
    odd_count = 0

    for drawing in data
        for number in drawing.numbers
            total_numbers += 1
            if number % 2 == 1
                odd_count += 1
            end
        end
    end

    odd_ratio = total_numbers > 0 ? odd_count / total_numbers : 0.5
    even_ratio = 1.0 - odd_ratio
    balance_score = 1.0 - abs(odd_ratio - 0.5) * 2  # 越接近0.5平衡分數越高

    return Dict(
        "odd_ratio" => odd_ratio,
        "even_ratio" => even_ratio,
        "balance_score" => balance_score
    )
end

function calculate_current_pair_frequencies(data::Vector{Drawing})::Dict{Tuple{Int,Int}, Int}
    return calculate_pair_frequency_trend(data)
end

function calculate_current_key_scores(data::Vector{Drawing})::Dict{Int, Float64}
    frequencies = calculate_number_frequency_trend(data)
    scores = Dict{Int, Float64}()

    max_freq = isempty(frequencies) ? 1 : maximum(values(frequencies))

    for (number, freq) in frequencies
        # 簡單的評分：基於頻率的標準化分數
        scores[number] = freq / max_freq
    end

    return scores
end

function calculate_indicator_changes(
    old_indicators::Dict{String, Float64},
    new_indicators::Dict{String, Float64}
)::Dict{String, Float64}

    changes = Dict{String, Float64}()

    for key in keys(new_indicators)
        if haskey(old_indicators, key)
            old_val = old_indicators[key]
            new_val = new_indicators[key]
            change = old_val != 0 ? (new_val - old_val) / abs(old_val) : 0.0
            changes[key] = change
        else
            changes[key] = 1.0  # 新指標視為100%變化
        end
    end

    return changes
end

function calculate_efficiency_trend(efficiency_history::Vector{Float64})::Float64
    if length(efficiency_history) < 2
        return 0.0
    end

    # 簡單的線性趨勢計算
    n = length(efficiency_history)
    x_vals = collect(1:n)
    y_vals = efficiency_history

    # 計算斜率
    x_mean = mean(x_vals)
    y_mean = mean(y_vals)

    numerator = sum((x_vals .- x_mean) .* (y_vals .- y_mean))
    denominator = sum((x_vals .- x_mean).^2)

    return denominator != 0 ? numerator / denominator : 0.0
end

end # module DynamicParameterManager
