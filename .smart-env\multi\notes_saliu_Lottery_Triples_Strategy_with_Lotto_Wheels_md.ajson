
"smart_sources:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md": {"path":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08792707,-0.03857132,-0.0364085,0.03580112,-0.05700255,0.08337284,-0.01528874,0.01071439,0.03782403,-0.00783071,0.01643461,0.01678017,0.00867155,-0.00127533,0.0061628,0.00650178,0.00592547,-0.01422126,-0.06131113,-0.03198793,0.08624748,-0.05876186,-0.05567929,-0.0712415,0.06101533,-0.00499329,-0.03243653,-0.05012454,-0.04215129,-0.23434159,-0.00592377,0.02567356,-0.0173215,-0.07460729,-0.1084526,-0.02103466,-0.04084148,0.11773167,-0.03435536,0.02336153,-0.00379478,0.02715201,-0.02174936,-0.00582589,-0.01221833,-0.02865457,-0.00212898,0.0171372,0.05582069,0.01244115,-0.05987913,0.00424151,0.01131636,0.01385659,0.07468455,0.03787627,0.0465209,0.08740013,-0.00340704,0.01386242,0.03667708,0.07353445,-0.18637343,0.06208525,0.01600886,-0.00673353,0.00201401,-0.00374636,-0.02138629,0.08447065,0.03552221,-0.00016575,-0.01093391,0.06223634,0.04542216,-0.00720848,-0.04934464,-0.08868526,-0.05240991,0.0369433,-0.02983606,-0.01718076,-0.02460622,-0.04010474,0.00195871,0.01125837,0.03485026,0.02499599,0.03663078,-0.07421886,0.03449516,0.03374659,0.02123126,0.00936507,0.01682632,0.00443504,0.0161891,-0.01662618,0.00326531,0.10627057,0.02432448,0.02749328,0.00173611,0.01637375,0.04915804,-0.04874681,-0.03693206,-0.07011934,-0.02048465,0.05206715,0.05265586,-0.00619923,0.08515289,-0.03633655,-0.04662971,0.04129789,0.01484389,0.02578157,0.00138959,0.00183665,-0.0138798,0.02771775,0.03105346,0.00436036,-0.02329863,-0.01603226,0.01661859,0.08116657,-0.0114257,-0.00317536,0.06684947,0.01295518,-0.14316377,-0.02056255,-0.0326158,-0.06015773,0.00656149,-0.01392724,-0.00204603,0.01065866,-0.02757585,-0.07722911,0.06729335,-0.10920123,-0.04865062,0.05809142,0.01919569,-0.00896376,0.0469505,0.006377,-0.02063015,-0.00938243,-0.03849269,-0.07384428,-0.00442692,0.00312098,0.07812285,0.08286914,-0.02773448,0.01054788,-0.06641607,-0.03937157,-0.05909758,0.12749538,-0.02327627,-0.12425987,-0.02218289,0.0477048,-0.03254061,-0.09874078,-0.0219768,-0.0415284,-0.05333621,0.02646579,0.09434675,0.00204065,-0.09315161,-0.06045213,-0.04018807,0.04589403,-0.00366144,-0.0342299,-0.06007269,-0.00303276,-0.01481994,-0.03953173,0.01051047,-0.01202516,0.0187523,0.00299554,-0.04472215,0.03782911,-0.0590109,0.02427936,-0.02814259,-0.00964595,-0.01082642,-0.04702319,0.06383716,-0.00451918,-0.02564609,-0.00788502,0.0170003,0.03473133,-0.02376547,0.01251775,-0.0031119,-0.0554769,0.08161464,0.04929576,-0.02194671,0.00129315,0.02982464,0.06714781,-0.06334325,0.02584685,0.01167707,0.03780952,-0.01803207,-0.01645875,0.02125191,0.0170562,-0.05723296,-0.17232326,-0.04382587,-0.04243955,0.01281924,0.05372155,-0.00949028,0.04179623,-0.01003731,0.0456455,0.04707576,0.06735925,-0.05301894,0.02554549,0.0583997,0.00742571,0.02580059,-0.09444853,-0.01292039,-0.02136374,0.0739534,0.01258257,0.02967883,-0.04594049,-0.08178303,0.03662914,-0.01841792,0.13649432,0.04424661,-0.00366812,0.00438137,0.08621303,-0.00044843,-0.00225396,-0.00013649,0.02285371,0.00207679,-0.01584566,-0.04781748,-0.06419381,-0.03967229,-0.05678052,0.02379222,0.00483784,-0.10676342,-0.05010287,-0.0509929,-0.02987375,0.04073837,0.00463293,0.0517982,0.06508946,0.00863623,0.0550129,0.0329481,0.04767273,0.01825579,-0.08744565,0.00587712,-0.00120944,0.0707255,-0.0012209,-0.00883204,0.03498954,-0.0095969,0.05326399,0.02874938,-0.01458896,-0.04134389,-0.02375647,0.00308705,0.00230254,0.07929733,0.04160582,0.02350756,-0.0203626,0.00699602,0.03829788,-0.04897403,0.05235934,0.03467579,-0.01074623,-0.0646496,0.03372628,0.05673182,0.03973278,0.02582235,0.02160687,0.04783103,-0.00186593,-0.01168165,0.01798316,0.00562442,-0.04084298,0.01731535,0.0281667,0.00441945,-0.25089079,0.01538774,-0.03912174,0.03008738,-0.00468783,-0.00859278,0.02910659,-0.03413265,0.05320679,-0.01312182,0.0723915,0.12090942,-0.00042373,-0.00651109,0.04245381,-0.03707186,-0.02572108,0.00427371,0.05991269,0.00382438,0.0540161,0.03204003,0.23780249,0.0220966,0.01860512,0.03583069,-0.01419181,0.0255876,-0.07585631,0.02110104,-0.01275748,0.00074841,0.0246769,0.00064992,-0.02203693,0.04740908,-0.01401082,0.02427086,-0.04758938,0.049252,-0.0686083,-0.00658438,-0.0034908,0.04345074,0.12749028,0.01884473,-0.02659318,-0.10031958,0.05136275,0.02787792,-0.06005279,-0.0610551,-0.09880985,-0.06835295,-0.00177818,0.05478783,0.02973087,-0.00253754,0.00679372,0.00406553,0.03558304,0.01300761,0.05140468,0.05794304,0.03211406],"last_embed":{"hash":"15fvdz9","tokens":469}}},"last_read":{"hash":"15fvdz9","at":1753423502296},"class_name":"SmartSource","last_import":{"mtime":1753366215781,"size":17819,"at":1753423416052,"hash":"15fvdz9"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Triples Strategy with Lotto Wheels":[8,258],"#Lottery Triples Strategy with Lotto Wheels#{1}":[10,17],"#Lottery Triples Strategy with Lotto Wheels#<u>Wheeling Lotto Triples, or Just Playing Biases: <i>Low</i> or <i>High</i> Numbers</u>":[18,19],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,":[20,234],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{1}":[21,28],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{2}":[29,30],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{3}":[31,34],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{4}":[35,36],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{5}":[37,42],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. First option: <i>1 = Check Lotto-6 Combos</i></u>":[43,82],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. First option: <i>1 = Check Lotto-6 Combos</i></u>#{1}":[45,82],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. Second option: 2 = Check Groups of Numbers</u>":[83,116],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. Second option: 2 = Check Groups of Numbers</u>#{1}":[85,116],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. First option: <i>1 = Check Lotto-6 Combos</i></u>":[117,172],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. First option: <i>1 = Check Lotto-6 Combos</i></u>#{1}":[119,172],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>":[173,234],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{1}":[175,207],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{2}":[208,208],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{3}":[209,209],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{4}":[210,210],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{5}":[211,211],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{6}":[212,212],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{7}":[213,213],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{8}":[214,215],"#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{9}":[216,234],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)":[235,258],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}":[237,240],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{2}":[241,241],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{3}":[242,242],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{4}":[243,243],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{5}":[244,244],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{6}":[245,245],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{7}":[246,246],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{8}":[247,247],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{9}":[248,248],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{10}":[249,249],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{11}":[250,250],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{12}":[251,252],"#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{13}":[253,258]},"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheels and applied lottery strategies.","target":"https://saliu.com/AxiomIon.jpg","line":16},{"title":"Playing the most frequent lotto triplets is one of the very best lottery strategies.","target":"https://saliu.com/HLINE.gif","line":23},{"title":"_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":29},{"title":"Selecting the lotto numbers to play a wheel is the most important element, like lotto triples.","target":"https://saliu.com/HLINE.gif","line":113},{"title":"_**Draws in lotto 6/49 Romania**_","target":"https://saliu.com/freeware/6-49-RO","line":214},{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":233},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":235},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":239},{"title":"_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":241},{"title":"_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_","target":"https://saliu.com/bbs/messages/532.html","line":242},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":243},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":244},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":246},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":248},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":249},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":251},{"title":"Playing all-low or all-high lotto numbers leads to far greater chance to win the lottery big-time.","target":"https://saliu.com/HLINE.gif","line":253},{"title":"Forums","target":"https://forums.saliu.com/","line":255},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":255},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":255},{"title":"Contents","target":"https://saliu.com/content/index.html","line":255},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":255},{"title":"Home","target":"https://saliu.com/index.htm","line":255},{"title":"Search","target":"https://saliu.com/Search.htm","line":255},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":255},{"title":"The 12 and 18-number lotto wheels assure best chances for high lottery prizes, including da jackpot.","target":"https://saliu.com/HLINE.gif","line":257}],"metadata":{"created":"2025-07-24T22:10:02 (UTC +08:00)","tags":["lottery","strategy","triples","triplets","groups of lotto numbers","lotto wheels","lotto wheeling","win","jackpot","lottery prizes"],"source":"https://saliu.com/lotto-triples.html","author":null}},"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10830281,-0.04237565,-0.0129544,0.04206189,-0.05336503,0.07644834,-0.01223519,0.00443765,0.01844486,0.01004965,0.02761815,0.00170585,0.01398116,-0.01015797,0.02453389,-0.01667082,0.00231129,-0.02126772,-0.05667238,-0.01800887,0.09072167,-0.02715076,-0.06661073,-0.03859218,0.05759834,0.00175658,-0.02787685,-0.04527836,-0.04133826,-0.19415866,-0.01540289,0.02347487,-0.02500408,-0.04344737,-0.07197826,-0.03864413,-0.03662603,0.12316003,-0.02113123,0.04341059,0.01611814,0.03268684,-0.04920122,-0.00785217,-0.01753154,-0.02171219,-0.01540465,0.02791384,0.04016484,0.00568627,-0.02289656,0.00840368,0.00599115,0.02707961,0.07517825,0.03190736,0.04308973,0.05580542,0.00518587,0.00790324,0.03922087,0.06203228,-0.21128747,0.06507161,0.01874059,-0.00417242,0.01226979,-0.00040506,-0.04049915,0.09937824,0.04294581,-0.0066565,-0.01065492,0.0428702,0.04723945,-0.00057965,-0.05819577,-0.10567963,-0.07901523,0.02642613,-0.02928119,-0.01847567,-0.04458668,-0.04733756,-0.01172318,0.00962528,0.03456611,0.03725481,0.02433681,-0.04776848,0.02041214,0.00229665,0.04788831,0.02856125,-0.02731577,0.00523689,0.024733,0.00300734,-0.0069889,0.12413777,0.00363427,0.06302172,0.02878848,-0.00083005,0.0406527,-0.02247747,-0.03659346,-0.07108776,-0.02265703,0.03555207,0.06940743,0.03443339,0.0824147,-0.07157689,-0.01819478,0.02487274,0.01278805,0.03050336,0.01742944,0.00337019,-0.0072843,-0.00603161,0.02101699,-0.00112278,-0.02466134,0.0135372,0.0208713,0.08463749,-0.01640227,-0.00775965,0.03203223,0.0124734,-0.14483424,-0.02019048,-0.03927045,-0.0408844,0.02019553,-0.04449502,0.01236251,-0.01167656,-0.0296991,-0.08980612,0.03070456,-0.12261942,-0.01787753,0.05451235,0.02969184,-0.00909522,0.03614923,0.01347845,-0.04901205,-0.00179971,-0.00604492,-0.06386609,-0.02031996,0.0013917,0.09081862,0.06673922,-0.01463544,0.02113682,-0.03559664,-0.02775281,-0.09106895,0.14484896,-0.02140783,-0.14537252,-0.01600951,0.04919497,-0.00670721,-0.12432757,-0.03909105,-0.03405276,-0.0508011,0.01492674,0.1179699,0.02214884,-0.05750673,-0.0474721,-0.049859,0.02325016,-0.02101902,-0.03445589,-0.06439366,-0.00004483,-0.04821166,-0.06072026,-0.00730105,-0.02022118,0.01831636,0.03126081,-0.03489668,-0.0001288,-0.04350217,0.02527585,-0.02678801,-0.01401955,-0.01299521,-0.04629298,0.07840791,-0.02661331,-0.01236876,-0.00822983,0.01638454,0.04376723,-0.01760407,-0.00086165,-0.00321757,-0.05158777,0.05607229,0.04162101,-0.00824421,0.03568228,0.02437257,0.04707243,-0.05393383,0.03353466,0.00018196,0.01895461,-0.02628386,-0.01588241,0.00217765,0.00928094,-0.06336764,-0.18831584,-0.02171769,-0.02649904,0.00090146,0.01678878,-0.00030021,0.03318023,0.00277078,0.02014576,0.06046967,0.07137432,-0.03098503,0.02379207,0.02322677,-0.0032715,0.02990531,-0.05494494,-0.01725695,0.00617172,0.07771019,0.0193512,0.03902299,-0.05758699,-0.10712564,0.04533477,0.00396065,0.12603961,0.07355313,-0.01887903,-0.0177828,0.11153009,0.01878778,-0.01868681,-0.02629221,0.02689077,0.01668048,-0.04606931,-0.01572447,-0.06198562,-0.04869575,-0.07056703,0.00892424,0.0004965,-0.11279988,0.00535544,-0.02691699,-0.00733037,0.03839567,0.00808566,0.05773063,0.05226335,-0.00738577,0.03748983,0.04492237,0.06723369,0.03081115,-0.07809339,-0.00982475,-0.00602124,0.03404404,0.00359756,-0.00274844,0.03565562,-0.00569191,0.03071241,0.03747485,-0.04216738,-0.05677705,-0.05551805,0.0047766,0.01703005,0.0694517,0.06378405,0.01083165,-0.04609394,0.01355332,0.02273779,-0.02791758,0.06498039,0.03408364,-0.00930848,-0.08097964,0.03937677,0.0662255,0.04358067,0.04620465,-0.01510936,0.03327505,0.01827849,-0.0049093,-0.00110981,0.02047367,-0.02534502,0.02620401,0.05056328,-0.017655,-0.25685015,0.01748165,0.00129881,0.02384932,0.01671951,0.03381994,0.01832298,0.00612978,0.01931572,-0.00150238,0.0887372,0.11227179,-0.01876317,-0.01648442,0.02437524,-0.02211876,-0.00595836,-0.00492846,0.03943894,0.0279092,0.03723576,0.02561584,0.24243174,0.05549973,0.01929585,0.02463052,-0.01900802,0.01064557,-0.06504422,0.03524902,-0.01095959,-0.00246463,0.02169479,-0.0139446,-0.02013283,0.03449472,-0.01599727,0.04175616,-0.02884158,0.07361489,-0.09283165,0.00990845,-0.01324802,0.01908046,0.11084761,0.04398944,-0.0318439,-0.10223114,0.01525239,0.00389008,-0.04381373,-0.07054422,-0.10942508,-0.02817542,0.01053038,0.05499825,0.03015729,-0.00793096,0.02205726,0.00382949,0.03287093,-0.01584063,0.0473822,0.04160139,0.01883078],"last_embed":{"hash":"z868nh","tokens":98}}},"text":null,"length":0,"last_read":{"hash":"z868nh","at":1753423499588},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#---frontmatter---","lines":[1,6],"size":225,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07500222,-0.04035675,-0.02581272,0.02592977,-0.05326492,0.09452781,0.00462439,0.0186413,0.03878489,-0.00548754,0.01099172,0.02602892,-0.00092571,0.00174016,0.00786954,0.01011329,0.01079231,-0.01305576,-0.06227301,-0.03273672,0.08962216,-0.06561539,-0.04031758,-0.06846934,0.05646973,-0.01411596,-0.04293952,-0.04490694,-0.04508515,-0.24212542,0.00406423,0.02875122,-0.0223625,-0.06764759,-0.11584579,-0.02346078,-0.04539884,0.11408323,-0.0380955,0.02089833,-0.00340595,0.01959515,-0.00849307,-0.0039637,-0.0099779,-0.03008338,0.00490677,0.022166,0.06003654,0.00573912,-0.0548086,-0.00549797,0.01552272,0.00261238,0.06707314,0.04422804,0.04662978,0.09393972,-0.00171593,0.01091166,0.03553164,0.08180548,-0.19362375,0.05755037,0.00883638,-0.0046999,0.00569601,-0.01686034,-0.02209251,0.08233428,0.03930064,-0.00380391,-0.00203062,0.06756184,0.03703908,-0.01133226,-0.04602597,-0.08970521,-0.0451577,0.03273462,-0.03026015,-0.01884867,-0.00915383,-0.04787965,0.00752968,0.01453797,0.02920254,0.01984153,0.04133144,-0.07886611,0.0406941,0.03875695,0.01342562,0.00599145,0.01790158,0.00104667,0.01468591,-0.02683464,0.00467295,0.11339039,0.01600658,0.0293171,-0.00165627,0.01538699,0.05051315,-0.04024985,-0.03674094,-0.0638015,-0.01562158,0.06019536,0.05233696,-0.00700004,0.0781105,-0.02086981,-0.04732937,0.04029523,0.00220117,0.01868652,-0.00032543,0.00597378,-0.01910832,0.01633792,0.03000172,0.00762241,-0.02699908,-0.01159319,0.01671341,0.07213202,-0.0129517,-0.00218624,0.05909913,0.00573975,-0.13886015,-0.02705631,-0.02667625,-0.06461591,0.01615855,-0.00608075,-0.00908697,0.02017678,-0.03029029,-0.08505183,0.06154065,-0.11260936,-0.05025406,0.06321331,0.0076006,-0.00433251,0.0490023,0.02268694,-0.02017526,-0.01345904,-0.0401105,-0.06699622,-0.00456499,-0.00430319,0.0838379,0.0847157,-0.02408474,0.01161989,-0.07745353,-0.03790386,-0.05437687,0.12122167,-0.02611221,-0.12149839,-0.02546963,0.04287386,-0.03825874,-0.09583353,-0.02644548,-0.03846921,-0.05320522,0.02311756,0.08867262,-0.00430568,-0.10157961,-0.05797725,-0.03354136,0.04180994,0.00482985,-0.03764512,-0.06170604,-0.00310297,-0.00404137,-0.04006448,0.02441187,-0.00352891,0.01114007,-0.00567735,-0.05026435,0.04908021,-0.04815058,0.01833066,-0.03367482,-0.01651491,-0.02050045,-0.04200518,0.06204549,0.00085994,-0.01548982,-0.00209736,0.02596564,0.04235302,-0.02439187,0.02272882,-0.01010927,-0.04915246,0.083022,0.03508965,-0.02076456,0.00654833,0.02738468,0.07293468,-0.06968959,0.02852504,0.01191629,0.04481108,-0.01840867,-0.00962492,0.03350341,0.02050304,-0.06450008,-0.17229238,-0.04553151,-0.04383333,0.01467394,0.06524514,-0.00157692,0.03093828,-0.01074294,0.04357857,0.04802287,0.05657898,-0.05423202,0.02730429,0.06757249,-0.00174461,0.02987578,-0.10191417,-0.0050668,-0.0332619,0.06194812,0.01222653,0.02613474,-0.04011167,-0.08699788,0.03750126,-0.01272983,0.14775296,0.02643067,-0.00115711,0.00773624,0.08270734,-0.01080253,0.00536149,0.00885278,0.02488272,0.00259686,-0.00322946,-0.05899135,-0.06875233,-0.03873063,-0.04753807,0.02180283,-0.00007197,-0.11049198,-0.03947057,-0.03963587,-0.03153446,0.0355603,0.00703521,0.06367084,0.07377242,0.00480927,0.05660533,0.02469867,0.04112896,0.00787433,-0.0795171,0.01097554,-0.00077421,0.07837701,0.00182487,-0.0032049,0.02910791,-0.0131585,0.05017783,0.0225542,-0.01358279,-0.03974345,-0.01457384,0.0048527,-0.00558785,0.07894176,0.03915822,0.02649087,-0.00690906,0.00500405,0.04314428,-0.04481573,0.05234956,0.02589681,-0.01510868,-0.05557187,0.02652469,0.05015532,0.0345883,0.01657216,0.02415041,0.04333587,-0.01575893,-0.02419583,0.0143364,0.00703838,-0.03977533,0.01710195,0.0163349,0.00294803,-0.25099316,0.02961012,-0.05336148,0.03093463,0.00100919,-0.0154168,0.01921121,-0.01231803,0.06150251,-0.02215062,0.08170015,0.12236243,0.01013428,-0.01224493,0.03860093,-0.04088369,-0.02267589,-0.0007243,0.05510993,-0.00878337,0.05420307,0.02863589,0.23333223,0.02017816,0.03123194,0.03726979,-0.0155666,0.02680999,-0.08459672,0.02085442,-0.01528711,-0.00401233,0.02130321,-0.00658578,-0.00714558,0.04207078,-0.00456385,0.03478589,-0.05034426,0.04305933,-0.06410234,-0.00770533,0.00149049,0.04692782,0.12286822,0.01652933,-0.02220939,-0.09733225,0.05528305,0.03244186,-0.05885885,-0.05997659,-0.08899587,-0.06431042,0.00059724,0.04196424,0.03104613,0.00530102,0.00189186,-0.00617758,0.03893617,0.00491738,0.04772852,0.05609861,0.02632454],"last_embed":{"hash":"ljl8y4","tokens":462}}},"text":null,"length":0,"last_read":{"hash":"ljl8y4","at":1753423499621},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels","lines":[8,258],"size":17568,"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheels and applied lottery strategies.","target":"https://saliu.com/AxiomIon.jpg","line":9},{"title":"Playing the most frequent lotto triplets is one of the very best lottery strategies.","target":"https://saliu.com/HLINE.gif","line":16},{"title":"_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":22},{"title":"Selecting the lotto numbers to play a wheel is the most important element, like lotto triples.","target":"https://saliu.com/HLINE.gif","line":106},{"title":"_**Draws in lotto 6/49 Romania**_","target":"https://saliu.com/freeware/6-49-RO","line":207},{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":226},{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":228},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":232},{"title":"_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":234},{"title":"_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_","target":"https://saliu.com/bbs/messages/532.html","line":235},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":236},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":237},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":239},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":241},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":242},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":244},{"title":"Playing all-low or all-high lotto numbers leads to far greater chance to win the lottery big-time.","target":"https://saliu.com/HLINE.gif","line":246},{"title":"Forums","target":"https://forums.saliu.com/","line":248},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":248},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":248},{"title":"Contents","target":"https://saliu.com/content/index.html","line":248},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":248},{"title":"Home","target":"https://saliu.com/index.htm","line":248},{"title":"Search","target":"https://saliu.com/Search.htm","line":248},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":248},{"title":"The 12 and 18-number lotto wheels assure best chances for high lottery prizes, including da jackpot.","target":"https://saliu.com/HLINE.gif","line":250}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07650337,-0.01485284,-0.02770879,0.01887147,-0.06684704,0.06848814,0.01241443,0.01626271,0.02263828,0.00681791,0.01349395,0.00699454,0.01516638,0.00558779,0.03182749,0.00943373,0.01444934,0.00818661,-0.06190975,-0.02174848,0.08502548,-0.04199021,-0.06119556,-0.07044543,0.06809275,-0.00498368,-0.0344059,-0.04764605,-0.02600431,-0.2213013,-0.00103388,0.04014393,-0.02301712,-0.06644268,-0.10371823,-0.03572508,-0.04391704,0.12859419,-0.03111351,0.0197773,0.00598989,0.01689786,-0.02114278,-0.01360159,-0.01545026,-0.03553429,0.02223252,0.02324014,0.08014956,0.00109906,-0.05260071,-0.00185792,0.00239138,0.0027618,0.06666702,0.04056088,0.04555543,0.10587573,-0.01681018,0.00361553,0.02813746,0.0900157,-0.18005425,0.07075135,0.01721528,0.00716049,0.00126344,0.0113501,-0.02865276,0.09933792,0.02722801,-0.01150758,-0.00889951,0.0683205,0.0281709,-0.007726,-0.06387971,-0.09748097,-0.05461919,0.04651874,-0.04629241,-0.02819493,-0.01163748,-0.01975695,-0.00229824,0.0266165,0.02961676,0.02579704,0.03779621,-0.07591247,0.04377927,0.02467168,0.00283963,0.0070614,0.00780254,0.00298546,0.00971564,-0.05633444,-0.00365583,0.14031024,0.0162437,0.03228652,-0.00636304,0.00864743,0.04359457,-0.03696261,-0.03691773,-0.06121647,-0.00532181,0.05369825,0.04483382,0.00377997,0.06493242,-0.04659394,-0.06079072,0.03801383,-0.00533654,0.00226135,-0.0100716,-0.00003848,-0.02522844,0.00046119,0.03630737,0.00769728,-0.02429567,-0.03553534,0.01488193,0.07130764,-0.01931769,-0.00408928,0.05688456,0.02960041,-0.15326367,-0.02898983,-0.02381741,-0.04360123,0.02674012,-0.01740234,-0.00488452,0.02957797,-0.00358723,-0.05369635,0.0600154,-0.12951097,-0.03168405,0.05609601,0.01361034,0.0000303,0.02406706,0.014084,-0.02865194,-0.01628367,-0.00700272,-0.04944629,-0.00476752,-0.00466495,0.08789717,0.06359763,-0.02140445,-0.00895104,-0.08126566,-0.02314334,-0.03916594,0.15453976,-0.02895192,-0.10737907,-0.02771673,0.02587938,-0.03851135,-0.09491549,-0.0429292,-0.03834932,-0.06147202,0.0204666,0.11510412,0.00033549,-0.09002479,-0.0602921,-0.02370161,0.03191466,0.01608616,-0.04462496,-0.04635701,0.01073657,-0.00963045,-0.06216728,0.00470617,-0.00425076,0.01399208,0.01160334,-0.04179339,0.05009036,-0.03923612,0.00456756,-0.05065018,-0.01628829,-0.01331124,-0.06170599,0.0466361,-0.01159291,-0.03040251,0.01597391,0.00781956,0.03276933,-0.01652267,0.01778158,0.01761847,-0.0533064,0.08850668,0.04398078,-0.03455509,0.02573694,0.04051901,0.06344511,-0.06688312,0.02142788,0.02192898,0.0502429,-0.020443,-0.00958932,0.03096805,0.02468422,-0.07206482,-0.17157565,-0.04981595,-0.0544051,0.00461942,0.05704787,-0.0112421,0.03427316,-0.01747389,0.02363979,0.04520431,0.07141977,-0.08120198,0.03824989,0.07749055,-0.01409483,0.01058064,-0.09057515,-0.01467063,-0.00739471,0.04651058,0.00161046,0.02607679,-0.02469376,-0.08520554,0.03158241,-0.00979318,0.13883674,0.04701147,0.0059141,0.00640344,0.10113338,-0.01250278,-0.01831962,0.00057279,0.0094229,0.02251808,-0.00140681,-0.05542695,-0.05194062,-0.03951444,-0.04012167,0.01650267,-0.01547636,-0.0750049,-0.04819249,-0.04803844,-0.02110549,0.05500588,0.02201752,0.064529,0.0624063,0.0138173,0.05694641,0.02808609,0.03550092,0.01516627,-0.08256699,-0.00312334,-0.00186775,0.06181393,-0.01347294,-0.01773741,0.025309,-0.00317371,0.04895182,0.03225725,-0.02952773,-0.05806234,-0.02075126,0.01362845,0.01228579,0.07033784,0.02665086,0.00936232,-0.00837649,0.03254715,0.04636179,-0.0581287,0.04315564,0.02459543,-0.00213607,-0.06802199,0.02296172,0.05096065,0.04171446,0.02914106,0.02808098,0.03836951,-0.03060772,-0.01805701,-0.0057864,0.00060833,-0.04020931,0.04152071,0.00915868,-0.02256735,-0.25190395,0.01320807,-0.03113103,0.0454934,-0.01174386,-0.00916148,0.02339901,-0.03715825,0.04276529,-0.00386914,0.08559977,0.09468704,-0.00435454,-0.01166239,0.02651111,-0.03095556,0.00436016,0.00902004,0.07297204,-0.01416685,0.04819985,0.03149701,0.23367618,0.02071031,0.0329885,0.03916503,-0.01162027,0.01448105,-0.07388781,0.02801316,0.0091326,0.00045106,-0.00270171,-0.00017198,-0.01259772,0.05543757,0.00276859,0.04012787,-0.03661818,0.04794041,-0.07493544,-0.00152672,-0.02679678,0.03766697,0.11697265,-0.00041575,-0.04232509,-0.09608039,0.02472962,0.03759287,-0.06017602,-0.06397427,-0.10075549,-0.04462798,0.00207118,0.04704755,0.0337937,0.00163759,0.01779571,0.00969146,0.03735763,0.02335842,0.04155343,0.05312674,0.04587947],"last_embed":{"hash":"1w9qrfs","tokens":145}}},"text":null,"length":0,"last_read":{"hash":"1w9qrfs","at":1753423499779},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#{1}","lines":[10,17],"size":426,"outlinks":[{"title":"![Ion Saliu teaches you how to create the best lotto wheels and applied lottery strategies.","target":"https://saliu.com/AxiomIon.jpg","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08243346,-0.03678449,-0.03642244,0.0111457,-0.03852511,0.10275978,0.0061841,0.01364638,0.05023359,-0.02194473,0.01997706,0.02523379,-0.00714574,-0.0038223,-0.00359287,-0.00089014,0.01709087,-0.04124075,-0.06790812,-0.04561194,0.11321352,-0.08979844,-0.02910034,-0.06205675,0.04403532,-0.00475106,-0.04232525,-0.04990529,-0.06305845,-0.24479477,0.0076603,0.02337332,-0.00307991,-0.07141559,-0.11008372,-0.01819516,-0.04736693,0.10446572,-0.03822609,0.01870087,-0.01634371,0.01718364,0.00260355,-0.00268076,-0.00320324,-0.03840715,-0.01906708,0.01182264,0.05480645,0.00260382,-0.07158067,0.0037639,0.01782984,0.0029904,0.08057027,0.02975299,0.04747383,0.07985815,0.01983672,0.02018968,0.04149218,0.06289277,-0.19610973,0.04144052,0.00347827,-0.0116519,0.00127154,-0.03488971,-0.01666994,0.08276055,0.03130722,0.01223364,-0.00388456,0.05862847,0.03566549,-0.03519188,-0.03367636,-0.07731115,-0.05322332,0.03595035,-0.03574977,-0.02109804,-0.02268943,-0.05144514,0.00198395,0.01423312,0.01496942,0.0108082,0.03511679,-0.0593267,0.03344982,0.03428859,0.02160186,0.0045955,0.00201003,-0.00960504,0.00780603,-0.00875014,0.01655735,0.10566629,0.0183976,0.02342716,0.01101667,0.01804344,0.02581737,-0.0531533,-0.03797729,-0.05601209,-0.0130811,0.05713155,0.03998592,-0.0022216,0.07855373,-0.00418177,-0.02168596,0.0480283,0.01427801,0.02104839,0.00030036,0.00085835,0.01230379,0.02158682,0.03063194,0.01615678,-0.03125152,0.00708142,0.01492518,0.06567193,0.00073677,-0.00169871,0.03970623,0.01494785,-0.13118841,-0.03302042,-0.04179076,-0.05805243,0.01508823,-0.01699266,-0.0077677,0.00645748,-0.04037528,-0.09307984,0.07139634,-0.09913257,-0.05671096,0.06170409,0.00995264,-0.00006133,0.07030963,0.05129377,-0.02176447,-0.00876665,-0.04299956,-0.06040008,-0.00567627,-0.01361399,0.08629442,0.07586584,-0.01904814,0.01368049,-0.07931793,-0.03882058,-0.06171328,0.12143767,-0.03453236,-0.12865655,-0.01755278,0.04461838,-0.03006098,-0.09141855,-0.0146245,-0.03566524,-0.04164868,0.02260145,0.06885131,0.00264509,-0.09175188,-0.06298353,-0.0146313,0.04883067,-0.0081759,-0.02935141,-0.07047089,-0.00421679,-0.01695508,-0.04934243,0.03212946,-0.00594001,0.01059216,-0.01378555,-0.03267593,0.05722055,-0.05767278,0.01654349,-0.02722673,-0.00838612,-0.01716661,-0.03671019,0.06673014,-0.01406127,0.00933731,-0.00062863,0.03788758,0.04380973,-0.03460543,0.02429197,-0.0185336,-0.03772286,0.06885502,0.02619917,-0.01682958,-0.00188246,0.01987396,0.07459576,-0.06605367,0.03234272,0.02128527,0.02827414,-0.01633955,-0.002989,0.03012086,0.01680747,-0.04737429,-0.18425137,-0.0313753,-0.0401051,0.01724051,0.06041374,0.01761115,0.03492269,-0.01401432,0.04220023,0.03959915,0.05872897,-0.04601338,0.0156702,0.05744065,0.00440537,0.03628413,-0.10439874,-0.02611622,-0.03920957,0.07910106,0.00663124,0.02993202,-0.067947,-0.0693792,0.04864543,-0.01724405,0.15535052,0.03253601,-0.005708,0.01057697,0.08097988,0.00389201,0.02766564,-0.00472127,0.03548614,-0.00571272,0.00113815,-0.0378847,-0.075811,-0.03409394,-0.04135448,0.02339772,-0.00871006,-0.12012265,-0.03338532,-0.02967338,-0.05039544,0.03036383,0.01440034,0.05912143,0.05983863,0.0044124,0.04286995,0.02728768,0.04535436,0.00772316,-0.06078214,0.01819262,0.01111858,0.07107608,0.01994224,0.01860065,0.02830878,-0.02783708,0.04554304,0.0171168,-0.01886178,-0.03227882,0.00401676,-0.01528039,-0.00001168,0.07275606,0.05227989,0.02162834,-0.00023213,0.00050094,0.06134948,-0.02789136,0.05598969,0.01951678,-0.01382368,-0.04989121,0.02144104,0.04729939,0.0420938,0.00812994,0.02055285,0.04359291,-0.00672246,-0.02999472,0.03445955,0.01903761,-0.02618315,0.01856615,0.04197139,0.01084802,-0.26385075,0.0424515,-0.05050546,0.0391941,0.00851498,0.00212784,0.02017581,0.00914372,0.05472426,-0.02707128,0.07713736,0.12187541,0.01593476,-0.03009868,0.03225443,-0.051052,-0.02893632,-0.02336366,0.05597503,-0.01421134,0.06089786,0.03096254,0.23917082,0.01712563,0.00526215,0.03861248,0.00069729,0.0296289,-0.06611349,0.0258225,-0.01318376,0.00036129,0.0136969,-0.01211177,0.00141167,0.04098636,-0.00262704,0.03722037,-0.05833627,0.03344661,-0.07226606,-0.02414242,-0.00065419,0.02465625,0.10585441,0.02106379,-0.04590741,-0.08727247,0.05982925,0.02136433,-0.04673503,-0.06407098,-0.07012422,-0.0736178,0.01254119,0.04069392,0.03874829,0.01071276,-0.01476464,-0.01887111,0.04902637,0.00034537,0.06277366,0.04372627,0.0183034],"last_embed":{"hash":"1cbbck0","tokens":459}}},"text":null,"length":0,"last_read":{"hash":"1cbbck0","at":1753423499823},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,","lines":[20,234],"size":14297,"outlinks":[{"title":"Playing the most frequent lotto triplets is one of the very best lottery strategies.","target":"https://saliu.com/HLINE.gif","line":4},{"title":"_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_","target":"https://saliu.com/bbs/best-18-number-lotto-wheels.html","line":10},{"title":"Selecting the lotto numbers to play a wheel is the most important element, like lotto triples.","target":"https://saliu.com/HLINE.gif","line":94},{"title":"_**Draws in lotto 6/49 Romania**_","target":"https://saliu.com/freeware/6-49-RO","line":195},{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":214}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08042595,-0.02474303,-0.03879481,0.02046827,-0.03589166,0.08327335,0.0128808,0.00308778,0.03192154,-0.02440292,0.01638358,0.01862195,0.00702046,-0.00626963,0.00815413,-0.01387281,-0.00031967,-0.01763901,-0.05655141,-0.0540572,0.11343988,-0.06306048,-0.05301568,-0.0621559,0.03480934,-0.00408525,-0.03952529,-0.05882268,-0.05184769,-0.22500195,0.00669139,0.01266789,-0.0169598,-0.07355568,-0.10348918,-0.02468388,-0.02930472,0.11111205,-0.03677394,0.03816908,0.00116337,0.00962408,-0.01399389,-0.02004198,-0.0125787,-0.02798563,0.00708303,0.00175042,0.06474324,0.00087416,-0.06860285,0.0253663,0.02221667,-0.01620238,0.07569059,0.03867634,0.03384475,0.09398434,-0.00062127,0.00981638,0.04468725,0.0514593,-0.20070192,0.0301708,0.01282415,-0.01945961,-0.00066569,-0.02987858,-0.00832948,0.08627012,0.0310333,0.00662237,-0.02345628,0.06005389,0.05111081,-0.01795737,-0.03390194,-0.06189943,-0.04509512,0.03621586,-0.02093238,-0.0383986,-0.0351722,-0.05931089,0.01884968,0.0110364,0.01220296,0.02951604,0.03782566,-0.06068835,0.02383675,0.03777882,0.01906113,0.00794622,0.01527804,-0.02136307,0.01764958,-0.00726544,0.01087654,0.1393571,0.03879561,0.0159905,-0.00622017,0.01808498,0.02367817,-0.04455274,-0.03949067,-0.06796117,-0.00980133,0.05668361,0.03492317,-0.01373191,0.0825077,-0.01210412,-0.02601731,0.05737313,0.01542882,0.045028,0.00800514,0.00023113,-0.00455913,0.01509145,0.04190729,0.02050271,-0.01997289,-0.00742321,0.00276412,0.07975926,-0.01074965,0.00081027,0.05965746,0.0044434,-0.1433237,-0.03922005,-0.03883259,-0.05600213,0.02132778,-0.00394541,-0.01333003,0.02186346,-0.05317707,-0.07786117,0.07841983,-0.09991061,-0.0426438,0.07166206,0.01609728,-0.00913962,0.05499471,0.04291429,-0.01663822,0.00513326,-0.04320205,-0.06786538,-0.00640924,0.01963454,0.09390709,0.08367279,-0.01823893,0.01471762,-0.076464,-0.03267564,-0.06575808,0.1432085,-0.02839424,-0.13202083,-0.02407306,0.02982629,-0.0419265,-0.09107862,-0.01291314,-0.03426744,-0.03937585,0.00969515,0.07273851,-0.02309818,-0.09009834,-0.07781389,-0.05005587,0.06114265,-0.00822248,-0.04805654,-0.05884792,0.02181824,0.00234145,-0.05230308,0.03435734,-0.00027534,0.02358878,-0.02057271,-0.02608865,0.0481844,-0.07310486,0.03451155,-0.01970963,-0.00178403,-0.02002358,-0.02921191,0.05792132,0.00335061,-0.015952,0.0046668,0.03995748,0.05159786,-0.02338499,0.02356221,-0.03591299,-0.02276197,0.05887331,0.04828135,-0.0088451,0.02112518,0.01409768,0.07173449,-0.07264008,0.02406284,0.02673032,0.02911542,-0.01481212,-0.00187845,0.02664023,0.02919771,-0.05846554,-0.17959332,-0.03384147,-0.03168167,-0.01451696,0.05650164,0.00386787,0.04161635,-0.00989706,0.05740092,0.04817585,0.03803839,-0.04415946,0.02085062,0.0545678,-0.00838823,0.0397555,-0.07532605,-0.02709621,-0.05018115,0.06195267,-0.00021483,0.01489546,-0.04978162,-0.06071088,0.02684006,-0.02382903,0.1436308,0.03997339,0.00571305,0.02462354,0.04684552,0.01067328,0.01302124,-0.01045461,0.02198568,0.0004896,0.00368658,-0.06308828,-0.06568586,-0.03132684,-0.05201097,0.01402181,-0.00400616,-0.11156871,-0.0244376,-0.02900518,-0.04010644,0.00238298,0.02012426,0.03852503,0.08472451,-0.01568722,0.02214464,0.0305052,0.04702494,0.01840172,-0.06626116,0.01117883,-0.00221066,0.06735,0.00141019,0.01664922,0.01627012,-0.02704841,0.0402611,0.03038617,-0.01178296,-0.0527331,-0.00961562,-0.01750566,-0.00021695,0.09290893,0.0429652,-0.00232033,-0.0008374,-0.00919599,0.06591029,-0.02348796,0.0361756,0.02868634,0.00430965,-0.05026302,0.03180548,0.04201575,0.04286776,0.00327743,0.02123296,0.03613141,-0.01205971,-0.02506604,0.03900026,0.01644601,-0.03010839,0.01679096,0.02658928,-0.0098576,-0.24862732,0.04057363,-0.05029875,0.04319724,-0.00018212,0.00898706,0.00869378,-0.01340391,0.04862151,-0.01606983,0.08719071,0.1316829,0.0041893,-0.02223311,0.04194339,-0.05719182,0.00785635,-0.01964438,0.05345473,-0.03358318,0.05407793,0.02389784,0.24350873,0.0459294,-0.0027516,0.03705137,-0.01032178,0.03297689,-0.08231494,0.02012808,-0.001468,-0.01895099,-0.00592903,-0.00351594,-0.00942926,0.06161608,-0.00219141,0.04695255,-0.04616804,0.03279682,-0.08124337,0.00015325,-0.02847866,0.00915471,0.11237766,0.04379206,-0.03979748,-0.06791103,0.05316852,0.04290178,-0.03898876,-0.07311291,-0.07661862,-0.07362045,0.01473653,0.04476118,0.02598395,-0.00717577,-0.00633901,-0.02783829,0.04161865,0.03094203,0.05173728,0.03784902,0.04226002],"last_embed":{"hash":"1m73uh4","tokens":145}}},"text":null,"length":0,"last_read":{"hash":"1m73uh4","at":1753423499987},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{1}","lines":[21,28],"size":446,"outlinks":[{"title":"Playing the most frequent lotto triplets is one of the very best lottery strategies.","target":"https://saliu.com/HLINE.gif","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0728429,-0.03657113,-0.01152005,0.0029711,-0.05426216,0.07785228,0.03396945,0.02568384,0.0599005,0.01269795,0.02303798,0.01477278,-0.01412667,0.00401281,0.00251691,-0.00515418,0.01014923,-0.05246709,-0.06070953,-0.03313384,0.11105617,-0.08381002,-0.02182769,-0.0705082,0.06054099,0.01214846,-0.04665796,-0.03632688,-0.05624469,-0.24727516,0.02241988,0.05882256,0.01747421,-0.06518441,-0.09370062,-0.02346549,-0.06208108,0.10357893,-0.0505392,0.02667153,-0.00832839,-0.00502494,-0.01307332,0.00564213,-0.00590043,-0.03482296,-0.03528376,0.04149814,0.02448535,-0.01093387,-0.04795873,-0.01160367,0.02010105,0.01359872,0.05647269,0.02535616,0.05858355,0.07952794,0.03380449,0.01710578,0.03809086,0.06688004,-0.21116072,0.03817209,-0.02758049,0.01264634,0.00614428,-0.04037217,-0.02704564,0.07293634,0.02429874,-0.00423786,0.00621105,0.05786389,0.03954847,-0.04234884,-0.04233814,-0.0765689,-0.0452281,0.0303653,-0.02497695,-0.03311767,-0.00755417,-0.03668768,-0.00442498,0.02955822,0.02873831,0.00867885,0.04039766,-0.07881193,0.0379664,0.02568326,0.03169994,0.01258863,-0.02155088,0.01752462,0.0047892,0.00079945,-0.00821693,0.10556944,0.00106367,0.01958326,0.02419583,0.02647126,0.03558629,-0.01105505,-0.03316857,-0.06239976,-0.01150046,0.03958436,0.04444838,0.02155674,0.06416775,-0.00736881,-0.01451075,0.04665565,-0.00545903,0.00823426,0.00064814,0.0157768,0.02597048,-0.00705259,0.01604137,0.02372565,-0.02635599,0.02957147,0.00711081,0.04983961,-0.01334309,-0.001646,0.0190356,0.01705445,-0.11853092,-0.03221045,-0.02074048,-0.04333315,0.02182562,-0.04285993,0.00915115,0.01050584,-0.04296582,-0.08270087,0.02976806,-0.10231352,-0.03982881,0.07167157,0.01150209,0.00236956,0.07374964,0.07983541,-0.03863201,-0.0247242,-0.01887879,-0.05031776,-0.04030615,-0.04679136,0.08711684,0.06272019,-0.02218098,0.00892976,-0.05690481,-0.02647673,-0.07157452,0.15598296,-0.04009013,-0.12821889,-0.02683818,0.05027695,-0.03223753,-0.11181855,-0.02666525,-0.01291595,-0.02775293,0.02216535,0.07908075,0.02127283,-0.0847728,-0.03819247,0.00309584,0.02793984,-0.00432473,-0.04567626,-0.05998117,-0.00332017,-0.0458536,-0.06572209,0.01883849,-0.01724814,0.00225426,0.02499411,-0.03546271,0.06853801,-0.03132799,-0.00092545,-0.04324278,-0.02709999,-0.01167225,-0.0237215,0.06875382,-0.02450665,0.02871368,0.00833325,0.03258617,0.05127311,-0.04780633,0.02100472,-0.00076274,-0.03477251,0.06138477,0.0079843,-0.03257817,0.00959902,0.01347115,0.07330868,-0.04173777,0.03469204,0.00597119,0.02551716,-0.03087283,-0.01020435,0.03079177,-0.00076956,-0.07659997,-0.19317411,-0.03329256,-0.03806533,0.02122184,0.06170254,0.01357654,0.02769586,-0.01697451,0.0170386,0.03227006,0.06414733,-0.03427547,0.01496202,0.05642178,-0.00051948,0.04072419,-0.08385658,-0.03179946,-0.01047058,0.076759,0.01133651,0.02524044,-0.04552506,-0.07848834,0.05397992,-0.00948321,0.16258422,0.04099066,-0.0200011,-0.00760383,0.10196198,-0.01562192,0.02453623,-0.0136257,0.04507757,0.0146193,-0.00801658,-0.02607402,-0.05660073,-0.04111976,-0.05562498,0.01415515,-0.01334937,-0.09850498,-0.00497232,-0.03065667,-0.04237795,0.03279282,0.01869716,0.08198097,0.03342832,0.02334461,0.02818157,0.02382965,0.04999272,0.01257266,-0.05560451,0.0375292,0.00281453,0.06253782,0.00996097,0.00627248,0.03353491,-0.03551437,0.05696288,0.00659766,-0.0457893,-0.02027563,0.02126615,-0.00483632,-0.01037932,0.05074475,0.06272335,0.0166156,-0.00995459,0.02586883,0.0407516,-0.04565194,0.06539614,0.01501883,-0.03010724,-0.06755143,0.02181092,0.03933363,0.06298779,0.03503397,0.01700018,0.02191949,-0.00603427,-0.05290144,-0.01237032,0.04056312,-0.0387399,0.04354491,0.04003388,0.00035282,-0.27216777,0.0509753,-0.05804373,0.03642183,0.03497116,0.01618024,0.02029836,0.02389481,0.04894845,-0.03669988,0.08048016,0.08848015,0.0300826,-0.03342232,0.0307852,-0.01364268,-0.03704512,-0.02325257,0.04894867,0.00492253,0.06007235,0.03702411,0.23382725,0.00388589,0.01504816,0.02932448,0.00109651,0.02179007,-0.05740286,0.03279148,-0.03477237,-0.01229748,0.02817618,-0.01618586,0.0075978,0.01732728,0.01138869,0.0251576,-0.05134486,0.05345083,-0.06951118,-0.03841583,0.00574433,0.04460015,0.10278553,0.01388645,-0.05031892,-0.09193669,0.03271541,0.00245132,-0.04459168,-0.06444042,-0.07423974,-0.05580122,0.03091986,0.0242582,0.04289712,0.00962826,-0.00376052,-0.02449453,0.036322,-0.02568212,0.05597998,0.02705607,-0.00219668],"last_embed":{"hash":"j3u2e1","tokens":193}}},"text":null,"length":0,"last_read":{"hash":"j3u2e1","at":1753423500031},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{3}","lines":[31,34],"size":579,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09022359,-0.00056263,-0.01671077,-0.02112195,-0.04942432,0.09473814,0.01202724,0.02819832,0.05109211,0.01979339,0.03143295,0.01106374,-0.0128989,-0.02284759,-0.02907762,0.00142482,0.0306937,-0.04875534,-0.07667979,-0.02586687,0.07688332,-0.0599594,-0.03862149,-0.07093192,0.06438358,0.00323968,-0.02477154,-0.04637891,-0.06890824,-0.24229771,-0.00044525,0.01122415,-0.02595148,-0.06280645,-0.08054788,-0.02383119,-0.04805542,0.10756424,-0.02498497,-0.0278587,-0.00416152,0.00764345,-0.01507531,-0.02442227,-0.01805743,-0.05628073,-0.03637794,0.01169217,0.06135496,0.00850292,-0.05765104,-0.00530258,0.05120784,-0.00113229,0.05900529,-0.00564531,0.06228797,0.09798183,0.04256365,0.01110843,0.03799881,0.08207568,-0.18236059,0.05173155,-0.01059635,0.00430235,-0.02077436,-0.02108495,-0.03080372,0.08881498,0.00983564,-0.00243552,-0.0057614,0.03922477,0.03923307,-0.07155596,-0.04817847,-0.09197659,-0.0716681,0.04028807,-0.06395268,-0.00816591,-0.0207104,0.0080015,-0.0237644,0.0386477,0.00870951,0.02216011,0.02355079,-0.04436671,0.05905658,0.01252771,0.02042961,-0.0048059,-0.04757442,0.01456777,0.02853103,-0.02534235,0.02154187,0.12041105,-0.00336825,0.02115251,0.03206739,0.01671804,0.01444858,-0.07004146,-0.03445577,-0.02122865,-0.03729319,0.02459362,0.05338477,0.00370769,0.062254,-0.02493465,-0.01257319,0.05755406,0.01106174,0.00666377,0.00108181,0.01752554,0.01125564,0.01361253,0.01850885,0.00522436,-0.02845291,0.00851752,0.05163331,0.05836176,0.00529626,-0.01895346,0.01037655,0.04536191,-0.12575135,-0.04996722,-0.05138664,-0.02461044,-0.0058576,-0.0382845,-0.01193623,-0.00545776,-0.00853363,-0.05253702,0.05823258,-0.10769041,-0.02139913,0.05515633,0.0020942,0.01271547,0.06718988,0.04873605,-0.03453239,-0.01650549,0.00311479,-0.02275069,-0.01789128,-0.02531255,0.04721031,0.07901346,-0.03628786,0.01020054,-0.06287032,-0.02863421,-0.05734478,0.13667585,-0.02978723,-0.08185218,0.00676382,0.02473162,-0.02274682,-0.11178751,-0.03642454,-0.01388888,-0.04362551,-0.00490204,0.08580516,0.01914549,-0.09422959,-0.05885018,-0.00702483,0.00090121,0.01744899,-0.01710869,-0.06517214,0.01781229,-0.02956817,-0.08100316,0.03257424,-0.03065129,0.0260559,0.0404031,-0.01599433,0.04824298,-0.03836403,-0.00290533,-0.05041205,-0.03306265,-0.01651661,-0.05714647,0.05877535,-0.03877341,0.04516755,-0.00290787,0.01761748,0.02326953,-0.02901808,-0.00205661,0.00768035,-0.05672577,0.09983443,0.00220776,-0.04357729,-0.00962668,0.03563728,0.08384327,-0.0479046,0.01736735,0.01277747,0.02608846,-0.02440924,-0.03170851,0.0401328,0.00082405,-0.03977995,-0.19134091,-0.03257066,-0.03666494,0.03048542,0.05670009,0.00732359,0.03366177,-0.00605899,0.02598469,0.0144656,0.09689559,-0.02965589,0.0158519,0.02443174,-0.00227767,0.03103337,-0.10321461,-0.01164426,-0.00658853,0.08099327,0.01378248,0.03226523,-0.06523244,-0.06988759,0.06993051,-0.02037373,0.15309981,0.0611146,0.01223335,-0.01420935,0.12434141,0.00799005,0.0182934,-0.02496546,0.04054032,0.02684691,-0.03444587,0.00796333,-0.04372383,-0.03463932,-0.04984245,-0.00226739,-0.01993171,-0.0986656,-0.01259261,-0.02474571,-0.03501137,0.04938484,0.03902826,0.05899223,0.04131404,0.02386294,0.03419355,0.03236309,0.05058033,-0.00480293,-0.05471688,0.02798927,0.01536852,0.03467629,0.04541073,-0.01985132,0.03139682,-0.01220731,0.03846697,0.00971777,-0.03038253,-0.0284583,0.02492096,0.03180129,0.01762527,0.01976629,0.03181397,0.03467856,-0.01343673,0.06486967,0.09384938,-0.02495836,0.05178818,0.01529786,-0.02026377,-0.05252889,0.01240544,0.02226907,0.05158155,0.03449569,0.00809377,0.01664264,-0.00807849,-0.04054908,0.00079488,0.03418367,-0.01683065,0.03704634,0.02936361,0.01266581,-0.27283064,0.03503054,-0.02378638,0.00232957,0.00498003,0.02369631,0.03446867,0.00856542,0.03559392,-0.03903109,0.07446272,0.10704823,0.00886163,-0.08176038,0.00896156,-0.03953425,-0.00894646,-0.00068713,0.09368438,-0.00077257,0.06676799,0.05024157,0.24973528,0.01172282,-0.00227723,0.0200703,0.01119179,-0.009765,-0.01744914,0.02591864,-0.00486635,0.02571479,0.01943193,-0.02218342,0.00067753,0.07015575,0.01621596,0.01386809,-0.04724995,0.03576002,-0.05470259,-0.02115368,-0.01625308,-0.01095948,0.07044329,0.0262519,-0.06609517,-0.07073015,0.0233425,-0.00838953,-0.04495239,-0.05449447,-0.07120137,-0.08169995,0.01900288,0.0617552,0.02354556,0.00163596,0.0028248,-0.0054182,0.05601723,-0.03283588,0.07406164,0.0237269,0.0273859],"last_embed":{"hash":"1fdhcis","tokens":218}}},"text":null,"length":0,"last_read":{"hash":"1fdhcis","at":1753423500094},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#{5}","lines":[37,42],"size":637,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. First option: <i>1 = Check Lotto-6 Combos</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08879192,-0.04438826,-0.0061182,0.03329924,-0.05496109,0.09901533,0.04508907,0.01798374,0.0515149,0.03301427,0.02868619,-0.03567538,0.01116442,0.00068193,-0.01358586,-0.01135853,-0.00261502,-0.03877366,-0.07359581,-0.0153615,0.07776512,-0.06560221,-0.04087396,-0.0663646,0.07176409,-0.00926243,-0.01511674,-0.06430694,-0.07140107,-0.23353145,-0.00320565,0.03449654,-0.00500027,-0.06570645,-0.08417495,-0.01892043,-0.06397409,0.1072769,-0.00193277,0.00974303,0.02738631,0.01091792,-0.03890385,-0.02555704,-0.04729982,-0.03578078,-0.02493784,0.03295688,0.07001407,0.02988853,-0.04355894,-0.01494323,0.04256195,0.01198918,0.0643517,0.02448928,0.05669928,0.06583759,0.01156699,0.02465384,0.03354403,0.0826291,-0.19126892,0.0456727,-0.00746541,-0.0058908,-0.01920112,0.01024697,-0.02101223,0.1061907,0.0157892,-0.00556451,-0.01770172,0.04696735,0.02808961,-0.0396529,-0.06479635,-0.10000713,-0.05945063,0.04550801,-0.08298262,-0.03129563,-0.01062767,-0.01854581,-0.00489993,0.01423758,-0.0095551,0.02614916,0.00970515,-0.04551167,0.03904878,0.02657865,-0.01174735,0.00522797,-0.01972613,0.00632735,0.02699566,-0.03931396,-0.00579979,0.12939465,0.02728658,0.03172661,0.01093584,0.03841309,0.03763292,-0.06419875,-0.05348235,-0.05761597,-0.02154106,-0.0002377,0.06545939,0.00386672,0.04969826,-0.05472034,-0.0617804,0.05479407,0.03660797,0.00091775,0.0120771,-0.00164321,-0.02107714,0.01260408,0.0102752,0.01020239,-0.03955205,0.00663807,0.05130572,0.0839676,0.01474226,0.01873126,0.03074942,0.0005168,-0.13518372,-0.06651402,-0.06174133,-0.01751807,0.01205961,-0.00258121,0.01124172,-0.00201584,-0.00938901,-0.10214617,0.03587153,-0.11596727,0.00509374,0.04299877,0.00088306,0.01392705,0.06478005,0.00841861,-0.02464789,-0.00249186,0.00258109,-0.04054933,-0.01872502,-0.03238446,0.03690729,0.07984931,-0.0215355,0.02668817,-0.05232302,-0.02517159,-0.04738488,0.10858332,-0.01075773,-0.08467136,-0.00172015,0.00076428,-0.05146833,-0.09532887,-0.02475686,-0.0111434,-0.03414233,0.01834041,0.07328999,0.02525217,-0.07796252,-0.05013284,-0.02320554,0.03242953,-0.01504904,0.0018459,-0.07002955,0.02391873,-0.03362786,-0.06972023,0.04088415,0.00623356,0.02463502,0.0401043,-0.02661373,-0.00212342,-0.045495,0.03987388,-0.01718166,-0.03980346,-0.02854089,-0.03901784,0.0514277,-0.0045522,0.05371484,0.00472892,0.03009336,0.01149253,0.00289929,0.01604511,-0.01794889,-0.04327079,0.07991012,0.00984144,-0.01698355,0.00654581,0.01484147,0.06929491,-0.00878044,0.01497481,0.01636389,0.02743457,0.03886447,-0.00162848,0.0184701,-0.01577008,-0.0341576,-0.19732058,-0.02615542,-0.02553367,0.01809726,0.04041649,0.00396745,0.02942373,-0.00933879,0.05218808,0.05971966,0.07642236,-0.01772479,0.02777638,0.06773017,-0.00654026,0.00160157,-0.10592819,0.00325149,-0.02945718,0.0834059,0.00662883,0.05124979,-0.03980448,-0.05470592,0.05353174,-0.02220589,0.17559008,0.03894315,-0.03473255,-0.0281496,0.102108,0.02012276,-0.00069663,-0.00096577,0.05841768,0.03534242,-0.03994916,-0.01582515,-0.07207111,-0.04991122,-0.06821286,0.00133648,-0.0038558,-0.10990503,-0.02997432,-0.03067993,-0.03202112,0.05104895,-0.00038819,0.05422344,0.04630068,-0.00565991,0.04111988,0.01992304,0.06950545,-0.01120449,-0.08204305,-0.03003583,0.01199635,0.0401518,0.0041262,-0.01900654,0.07361168,-0.0249876,0.01972979,0.01053049,-0.00207799,-0.0469308,-0.02650821,0.03495507,0.01730184,0.02644745,0.03481346,0.03087437,-0.0109215,0.03927817,0.05951101,-0.01083652,0.03096651,0.02189513,0.02526962,-0.08153597,0.01123159,0.04417231,0.05215894,0.04309341,0.0242641,0.03862281,-0.00818352,-0.00392824,-0.00778121,0.02735518,-0.00672659,0.06299204,0.01254978,-0.00759252,-0.28606969,0.02647936,-0.06131274,0.02288174,-0.00480219,0.02470563,0.03436869,0.00473817,0.01621818,-0.05250655,0.0906735,0.10350006,0.01332673,-0.07785604,0.01831594,-0.0479519,-0.03464055,-0.02201996,0.05996303,-0.00641134,0.06194978,0.04164605,0.22951353,0.01359145,0.01722316,0.02701564,0.01418103,-0.01687751,-0.04448191,0.03042848,-0.02033717,-0.01456692,0.05890227,-0.01489301,-0.00773645,0.06177532,0.00277454,0.04151132,-0.02999605,0.03561512,-0.07533195,0.0005383,-0.00531109,0.02294015,0.08583651,0.03866039,-0.05274808,-0.04834552,0.04808698,0.0382998,-0.04013208,-0.05846943,-0.05196612,-0.04096688,-0.00088033,0.03275198,0.00413215,0.01439564,0.01461954,0.00839121,0.03765244,-0.00709777,0.04738652,0.00684815,0.01009399],"last_embed":{"hash":"1vtepfg","tokens":483}}},"text":null,"length":0,"last_read":{"hash":"1vtepfg","at":1753423500187},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. First option: <i>1 = Check Lotto-6 Combos</i></u>","lines":[43,82],"size":2529,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. First option: <i>1 = Check Lotto-6 Combos</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08801579,-0.04532078,-0.00562375,0.0326764,-0.05135661,0.09889814,0.04597575,0.02311326,0.05379699,0.03402839,0.02709902,-0.03837879,0.01038175,0.00139773,-0.01555208,-0.01246875,-0.00280077,-0.03789682,-0.07566614,-0.01786964,0.07863665,-0.06633753,-0.04124157,-0.06688955,0.07145148,-0.00975284,-0.01373404,-0.06268486,-0.06962034,-0.23313527,-0.00663565,0.032345,-0.00366245,-0.06491919,-0.08401325,-0.01702893,-0.06307107,0.10282367,-0.00370095,0.0061345,0.02492379,0.0121172,-0.0385857,-0.02726197,-0.04720233,-0.0357437,-0.02532634,0.03167942,0.06803268,0.03220728,-0.04434805,-0.01327529,0.04286705,0.01253659,0.06532203,0.02205605,0.0574887,0.06643105,0.01256368,0.0239272,0.03396636,0.08104552,-0.19044681,0.04507063,-0.01070761,-0.00545194,-0.01982629,0.01069931,-0.02248827,0.10551791,0.01786613,-0.0052885,-0.01774278,0.04707594,0.02611213,-0.04153336,-0.0665817,-0.10194269,-0.05817628,0.04543231,-0.08430514,-0.0312678,-0.01128606,-0.01977867,-0.00656781,0.01305002,-0.01075405,0.02412752,0.00927119,-0.04585082,0.038854,0.0273013,-0.00734368,0.00462555,-0.01928949,0.00719007,0.02397903,-0.03809356,-0.00247903,0.12874916,0.02564898,0.03155018,0.01206526,0.03711698,0.03720222,-0.06606416,-0.05203505,-0.05582218,-0.02203051,-0.00130505,0.06470397,0.00426763,0.04620909,-0.05332429,-0.06104534,0.05502358,0.03832848,0.00274363,0.01136795,0.00044159,-0.01962609,0.01144931,0.00922774,0.00838187,-0.03775965,0.00629091,0.05232824,0.08235995,0.01561164,0.01835409,0.03167408,0.00121393,-0.13419507,-0.06936585,-0.06339295,-0.01721532,0.01299939,-0.0039133,0.00861863,-0.00089134,-0.01041379,-0.10259658,0.03699512,-0.11578623,0.0042491,0.041961,0.00127195,0.01596077,0.06654626,0.00941496,-0.02588505,-0.00283812,0.00261603,-0.04327469,-0.01794856,-0.03412103,0.03711284,0.08007341,-0.0190737,0.02520106,-0.05120083,-0.02253491,-0.04850717,0.10708606,-0.01133685,-0.08344657,-0.00129209,0.00119163,-0.05023006,-0.09713911,-0.02430512,-0.0085567,-0.033301,0.01828677,0.07407473,0.02390995,-0.07869542,-0.04809054,-0.02633625,0.0326474,-0.01566376,0.00542639,-0.06971499,0.0203014,-0.02981966,-0.0681294,0.04080061,0.00901399,0.02726542,0.0398127,-0.02551496,-0.00125574,-0.04486699,0.04162757,-0.01752875,-0.03997557,-0.02856776,-0.04093436,0.05312678,-0.00604576,0.05555335,0.00659015,0.03009588,0.00921195,0.00248156,0.01714355,-0.01723527,-0.04210593,0.07757109,0.00851112,-0.01962249,0.00610875,0.01401263,0.06853878,-0.00572042,0.0152527,0.01575398,0.02728136,0.04001417,-0.00295425,0.01744318,-0.01951694,-0.03198039,-0.1985372,-0.02465741,-0.02529911,0.02101877,0.04240121,0.00092561,0.02646498,-0.010466,0.05314965,0.05679387,0.07836731,-0.01721414,0.02822652,0.06890761,-0.00661452,0.00331405,-0.10559562,0.00542669,-0.02928177,0.08087076,0.0047478,0.05290215,-0.03698582,-0.05139111,0.05306816,-0.02214756,0.17725603,0.03904079,-0.03139842,-0.03116617,0.10358702,0.01902277,0.00025054,-0.0010406,0.06106341,0.03381042,-0.03878864,-0.01808939,-0.07117688,-0.05087299,-0.06863263,0.00024317,-0.00100616,-0.10982087,-0.02809276,-0.03316212,-0.0328912,0.05062545,-0.00029328,0.05451236,0.04490717,-0.00311856,0.04213214,0.01944738,0.06686404,-0.00923081,-0.08198716,-0.03344877,0.01394864,0.03895427,0.004719,-0.01961365,0.07341409,-0.02545272,0.02033116,0.01137262,-0.00097804,-0.04412119,-0.02634972,0.03831938,0.01699177,0.02702652,0.03228035,0.03464514,-0.00962824,0.03966614,0.05984876,-0.01216352,0.03250321,0.02548041,0.02564418,-0.08234711,0.01010625,0.04293629,0.05165901,0.04126527,0.02665469,0.03717996,-0.00923811,-0.00514938,-0.00876196,0.02657561,-0.00502363,0.06093162,0.01147415,-0.00402972,-0.28766423,0.02825864,-0.05965239,0.02198026,-0.0072693,0.02468557,0.03423008,0.00447929,0.01744056,-0.05459853,0.08755784,0.10454763,0.01308101,-0.07793942,0.01878088,-0.04702026,-0.0365155,-0.02149318,0.0600932,-0.00594656,0.06410425,0.04421545,0.23079775,0.01188007,0.01704843,0.02508975,0.01567981,-0.01778544,-0.04534638,0.03233983,-0.02029312,-0.01329306,0.05815586,-0.01577865,-0.00641904,0.06513011,0.0024668,0.04205544,-0.03045877,0.03463956,-0.07472132,-0.0015152,-0.00379607,0.02042614,0.08718502,0.03843051,-0.05272266,-0.04987318,0.04852284,0.03629178,-0.03884881,-0.05809077,-0.05128356,-0.04250849,-0.0010156,0.03232379,0.00372644,0.01369955,0.0128821,0.0067345,0.03992032,-0.00461354,0.04542764,0.00666348,0.01045734],"last_embed":{"hash":"1njjg9k","tokens":481}}},"text":null,"length":0,"last_read":{"hash":"1njjg9k","at":1753423500394},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. First option: <i>1 = Check Lotto-6 Combos</i></u>#{1}","lines":[45,82],"size":2468,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. Second option: 2 = Check Groups of Numbers</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06890763,-0.05017398,-0.00037914,0.0306115,-0.06364582,0.10311884,0.041232,0.00957007,0.0494659,0.01472441,0.03134977,-0.02465399,0.01649821,-0.00709858,-0.02826934,-0.00287264,0.00457578,-0.02772606,-0.07339061,-0.00863517,0.06668353,-0.06978425,-0.04682858,-0.06703634,0.05864936,0.00473723,-0.05024889,-0.06003562,-0.07571324,-0.23810452,-0.00532857,0.04798879,0.00410635,-0.06540322,-0.09691688,-0.02570746,-0.04620001,0.11467294,-0.00697239,0.00956905,0.01830452,0.03420843,-0.0350658,-0.00444129,-0.03650711,-0.01735677,-0.02569271,0.03412514,0.07696535,0.02894872,-0.03183537,-0.01093804,0.04304814,0.01898491,0.07187242,0.03200928,0.06301241,0.06239111,0.01153564,0.0284208,0.03037347,0.08040301,-0.17398334,0.04207688,0.00703332,-0.00031301,-0.00125673,-0.01066406,-0.02619176,0.10921078,0.01228305,-0.01483603,-0.01659099,0.04489024,0.0470827,-0.02170596,-0.05431447,-0.09812328,-0.04198861,0.03167344,-0.06442869,-0.0290388,-0.01059008,-0.014188,0.0006055,0.02116106,0.00238164,0.03901501,0.02428917,-0.05484981,0.03233447,0.02704112,-0.02750674,0.01174732,0.0058531,0.02115445,0.03129637,-0.02665356,-0.01025176,0.11709013,0.04974304,0.03833744,0.02542898,0.03050398,0.03089662,-0.04873647,-0.04952479,-0.04163076,-0.03719647,0.01276839,0.09278379,-0.00548059,0.06936106,-0.03239029,-0.06300905,0.04456246,0.02715244,0.00582436,0.01057588,0.00139485,-0.006809,0.01606888,0.02502076,0.0025982,-0.02839782,-0.00373557,0.03186239,0.09008677,-0.00025018,0.02038403,0.02895002,-0.01501155,-0.13082032,-0.04679621,-0.04310724,-0.03509646,0.01005447,-0.00916226,0.01875267,-0.0057287,-0.02103657,-0.09505021,0.0461426,-0.12045269,-0.00173582,0.04682484,0.00643036,-0.0218278,0.05714891,0.02150008,-0.02218608,-0.01626337,-0.04154455,-0.03667292,-0.04272036,-0.01331024,0.05339653,0.0833735,-0.04053514,0.01972142,-0.05523721,-0.03237421,-0.07572836,0.11354157,-0.00672759,-0.06776697,-0.01746324,0.01818965,-0.06627233,-0.08974317,-0.04108567,0.00549709,-0.05270796,0.02617005,0.08500406,0.02194784,-0.08917669,-0.03469808,-0.04127747,0.02376925,-0.00871869,-0.0175285,-0.05011657,0.01271943,-0.0292345,-0.03872433,0.0271582,0.01308457,0.01492098,0.02213532,-0.04179958,0.0039628,-0.04570638,0.04765625,-0.02258592,-0.03685766,-0.01501442,-0.0156649,0.04747192,-0.00158658,0.02104029,-0.01706893,0.04144615,0.02950894,-0.01851852,0.01867177,-0.00502037,-0.03694826,0.07359959,0.0235508,-0.03158767,-0.01809341,0.0027064,0.07296322,-0.02605052,0.02277722,0.0157928,0.01303877,0.00204572,-0.00002298,0.03884792,-0.02994176,-0.05256298,-0.19484526,-0.06387955,-0.01540735,0.02047785,0.05946291,-0.00731697,0.03303884,0.01091594,0.04458836,0.06251736,0.07584242,-0.01570542,0.01724677,0.04272189,-0.02099693,0.03162434,-0.09894034,-0.01463075,-0.01684584,0.10695735,0.01597811,0.05582598,-0.06436859,-0.06845883,0.05887575,-0.03797366,0.16766691,0.0503649,-0.03065002,-0.00515347,0.09009062,0.01504334,-0.00281674,0.02781145,0.04351312,0.01420522,-0.04943677,-0.01887993,-0.07323506,-0.03246048,-0.06662101,0.00190012,-0.01132552,-0.11206558,-0.01369618,-0.04275617,-0.03072936,0.03400894,-0.00152217,0.01963167,0.0577405,-0.00504363,0.03316324,0.01462164,0.05807036,0.00758368,-0.06078505,-0.00695274,0.00108497,0.03996884,-0.00735829,-0.00671305,0.06044972,-0.03433198,0.03306451,0.01946907,-0.02958663,-0.03402948,-0.00152473,0.02367698,0.00736543,0.02287271,0.06387757,0.03757428,-0.03988223,0.02057264,0.03665524,-0.01575105,0.05957228,0.02345424,0.01077929,-0.07682102,0.0132704,0.05314381,0.05859535,0.04520357,0.00784056,0.04489787,-0.02149461,-0.00750641,-0.00610507,0.03832016,-0.04017624,0.03259081,0.01561932,-0.01583103,-0.2713899,0.0389388,-0.06355704,0.02187466,-0.02291724,0.01683511,0.01773162,0.0186857,0.01927737,-0.02955951,0.10571658,0.09976187,0.01387777,-0.06876685,0.01507705,-0.03157251,-0.04013867,-0.01396292,0.05102279,-0.00221318,0.07425812,0.03404405,0.24119477,0.01677702,0.03604006,0.0130462,-0.00767281,-0.01199154,-0.04689614,0.0353493,-0.0312719,-0.01713493,0.06437179,-0.02594154,-0.02365647,0.0317248,0.00702532,0.02506354,-0.03803336,0.0450201,-0.06481735,-0.01912119,-0.00465122,0.04716304,0.10326241,0.05752886,-0.02474285,-0.07586196,0.03841,0.04478766,-0.04305103,-0.05266312,-0.08543323,-0.05021869,0.01701583,0.04311189,-0.00051666,-0.0009794,0.00339206,0.01720548,0.00998253,-0.01198701,0.05680982,0.00819879,0.01745524],"last_embed":{"hash":"1plsjjk","tokens":448}}},"text":null,"length":0,"last_read":{"hash":"1plsjjk","at":1753423500594},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. Second option: 2 = Check Groups of Numbers</u>","lines":[83,116],"size":1936,"outlinks":[{"title":"Selecting the lotto numbers to play a wheel is the most important element, like lotto triples.","target":"https://saliu.com/HLINE.gif","line":31}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. Second option: 2 = Check Groups of Numbers</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06854764,-0.04988629,-0.0012614,0.03147899,-0.06206916,0.10323512,0.04139034,0.00976039,0.04968029,0.01498552,0.03150608,-0.02733196,0.01551901,-0.00688719,-0.02847326,-0.00406544,0.00480179,-0.02651844,-0.07330666,-0.01223099,0.06768483,-0.06888425,-0.04613318,-0.06748299,0.05945774,0.00533396,-0.0487235,-0.06013614,-0.07552668,-0.23784682,-0.00612724,0.04909573,0.00330856,-0.06647495,-0.0971946,-0.02526958,-0.04553522,0.11069675,-0.00308681,0.0104115,0.0173938,0.03417806,-0.03725244,-0.00673708,-0.03916376,-0.01663011,-0.02595763,0.03323974,0.08106462,0.0288005,-0.03105685,-0.01156299,0.04227979,0.01905364,0.07276325,0.03215735,0.06208543,0.06189399,0.01040482,0.02894812,0.02752326,0.08153475,-0.1765177,0.04169063,0.00356742,-0.00226567,-0.00347909,-0.00988358,-0.02488892,0.10930581,0.01048422,-0.0168085,-0.01835166,0.04700217,0.04888334,-0.02207199,-0.05691187,-0.0983561,-0.04069363,0.03095625,-0.06541365,-0.03136767,-0.00898851,-0.01473825,0.00182586,0.02089818,0.00176961,0.04057263,0.02360219,-0.05464743,0.03342788,0.02595352,-0.02569433,0.01361279,0.00660291,0.01817003,0.0316828,-0.02589192,-0.00797878,0.11706544,0.049522,0.03708943,0.02611011,0.03225207,0.02873367,-0.05027715,-0.04865962,-0.04207264,-0.03576604,0.01141331,0.08914293,-0.00604543,0.06746311,-0.03330327,-0.06214163,0.04613848,0.02495087,0.0050503,0.01014258,0.00034618,-0.00526344,0.01385672,0.02582028,0.00162212,-0.02800879,-0.00500785,0.0336605,0.08795483,0.0007256,0.02188166,0.03060995,-0.01539393,-0.13301715,-0.04955516,-0.04515615,-0.03215212,0.01083898,-0.01272921,0.0154892,-0.00650224,-0.02080172,-0.09486932,0.04619337,-0.11977561,0.00024447,0.0488517,0.00402709,-0.0227657,0.05763444,0.02229295,-0.02553011,-0.0148658,-0.04079478,-0.03532483,-0.04093682,-0.01286127,0.05396521,0.08197062,-0.03868838,0.02029239,-0.0532806,-0.03015061,-0.07607736,0.11504459,-0.0066468,-0.06655373,-0.01501984,0.01776356,-0.06646141,-0.08995306,-0.03770578,0.00639987,-0.05114377,0.02844294,0.08722511,0.02243159,-0.09082247,-0.03421909,-0.0441552,0.02412966,-0.01195037,-0.01657324,-0.04853132,0.01418951,-0.03009915,-0.03880351,0.02813543,0.01199612,0.01595882,0.02349523,-0.04445329,0.00404361,-0.04757129,0.04924529,-0.0242673,-0.03691481,-0.01477039,-0.01414547,0.04628902,-0.00176711,0.02418152,-0.0170236,0.0399511,0.02932933,-0.0181553,0.01829502,-0.00771251,-0.03351209,0.07187996,0.0235234,-0.02995477,-0.01811045,0.0033797,0.0711833,-0.02497704,0.02152593,0.01391958,0.01446446,0.00159312,0.00106502,0.03886281,-0.03111583,-0.05257253,-0.19539601,-0.06230343,-0.01565511,0.02175387,0.05681507,-0.00737084,0.03408137,0.01278629,0.04452087,0.06180299,0.07415654,-0.01658558,0.01593149,0.04247484,-0.02032503,0.03197627,-0.09834798,-0.01566502,-0.01701834,0.10743057,0.01537225,0.0563774,-0.06401687,-0.06375246,0.06057041,-0.03915381,0.16782841,0.05060827,-0.03187671,-0.00460492,0.09117693,0.01467104,-0.00126127,0.02786499,0.04330749,0.01469573,-0.05036885,-0.01589833,-0.07168292,-0.0319369,-0.06570827,0.00169517,-0.01161282,-0.1115511,-0.00994052,-0.04212854,-0.02959369,0.03355269,0.00161321,0.01645662,0.05999982,-0.00417243,0.03243707,0.01536003,0.05725287,0.00620047,-0.06229093,-0.00849347,-0.00011998,0.0375904,-0.00906322,-0.00862059,0.06082347,-0.03457226,0.03312293,0.01824391,-0.02915151,-0.0341452,-0.00163379,0.02435017,0.00991325,0.02368365,0.06403599,0.03960181,-0.04012366,0.01841627,0.03685238,-0.01634307,0.05751687,0.02315764,0.00931504,-0.07632751,0.01188372,0.05366937,0.0607576,0.04569128,0.01016277,0.04544916,-0.01883911,-0.00703531,-0.00581196,0.03735565,-0.03863873,0.03444364,0.01515246,-0.01585488,-0.27375734,0.03984845,-0.06581255,0.01986226,-0.0228587,0.01676,0.01847747,0.01717288,0.01855133,-0.02884422,0.1066975,0.09789675,0.01494574,-0.06913428,0.01452881,-0.02862738,-0.04019276,-0.0148188,0.05072801,-0.00283434,0.07416814,0.03449183,0.241226,0.01443067,0.0362522,0.0143016,-0.00972142,-0.01286605,-0.04454796,0.03771342,-0.03147814,-0.01562939,0.06407934,-0.02765652,-0.02481856,0.03500773,0.00723682,0.02533888,-0.03637778,0.04298171,-0.06468134,-0.01988809,-0.00364014,0.04471093,0.10410537,0.05694279,-0.02534484,-0.07650571,0.03750166,0.04612275,-0.040362,-0.05171594,-0.08352598,-0.04742046,0.01799863,0.0424218,-0.00293342,-0.00034637,0.00382516,0.01676809,0.01113421,-0.00905284,0.05572046,0.00724326,0.01893776],"last_embed":{"hash":"18rvh8e","tokens":446}}},"text":null,"length":0,"last_read":{"hash":"18rvh8e","at":1753423500829},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>A. Second option: 2 = Check Groups of Numbers</u>#{1}","lines":[85,116],"size":1878,"outlinks":[{"title":"Selecting the lotto numbers to play a wheel is the most important element, like lotto triples.","target":"https://saliu.com/HLINE.gif","line":29}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. First option: <i>1 = Check Lotto-6 Combos</i></u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.093702,-0.04013379,0.00031602,0.03097252,-0.05682243,0.09809265,0.03966176,0.01913381,0.04023461,0.04055566,0.03604976,-0.04119351,0.01156849,0.00590859,-0.00734142,-0.0139421,0.00101385,-0.03518234,-0.07717909,-0.02444846,0.08412712,-0.06234178,-0.0458403,-0.06698047,0.06860118,0.00040644,-0.01576614,-0.06324591,-0.06990922,-0.23019959,-0.00751563,0.03366641,-0.00078361,-0.05903701,-0.07909644,-0.01588587,-0.06620066,0.11400154,-0.00956692,0.01030983,0.03087074,0.00971922,-0.03830176,-0.02233654,-0.04580029,-0.04478768,-0.02002395,0.04149545,0.06842691,0.02230843,-0.04295254,-0.01697273,0.042178,0.01193187,0.06041494,0.02911937,0.05814029,0.06157826,0.00984408,0.01675326,0.03383853,0.08493315,-0.20181365,0.04478287,-0.01281368,-0.00208849,-0.01184109,0.00590854,-0.01475832,0.10433643,0.01468039,-0.01254451,-0.01146056,0.04663542,0.0271511,-0.02863934,-0.06154492,-0.10507081,-0.05920494,0.04716394,-0.08336623,-0.02878528,-0.00978732,-0.0214211,-0.00179589,0.02662536,-0.0157381,0.02004037,0.01069649,-0.04652363,0.03478011,0.01613009,-0.01245456,0.00456222,-0.02880383,-0.00144556,0.01592187,-0.04253712,-0.01353698,0.13254863,0.02713929,0.03278989,0.01318654,0.03565933,0.03770063,-0.06779547,-0.05153326,-0.06125199,-0.02263303,0.00145417,0.06329916,0.00683397,0.05022545,-0.05337899,-0.05925496,0.06098795,0.03139651,-0.00339866,0.00326846,0.00145269,-0.01619555,0.00597388,0.01385779,0.02019642,-0.0403742,-0.00053908,0.04598569,0.07852983,0.01560317,0.01976661,0.03511339,0.00488985,-0.12974153,-0.07291172,-0.06053717,-0.01517712,0.01464875,-0.01061754,0.01627447,-0.00384177,-0.01423199,-0.10745278,0.03054825,-0.11123261,0.01061054,0.04583314,-0.00631918,0.01422672,0.06821889,0.01733739,-0.03586261,-0.00914878,0.01321638,-0.03722512,-0.01543706,-0.03326628,0.03257416,0.08398732,-0.02297902,0.02916181,-0.05972623,-0.01375504,-0.05405733,0.11366807,-0.00397892,-0.09703569,0.00089626,0.00724554,-0.04255313,-0.09300558,-0.03012324,-0.01067782,-0.03499327,0.01955241,0.07421264,0.02506716,-0.07128837,-0.04942574,-0.03144819,0.0345039,-0.02428929,-0.01124932,-0.07027562,0.02413381,-0.03774334,-0.07538491,0.04854381,0.00031018,0.02342455,0.03506503,-0.0240439,-0.00185879,-0.03887718,0.03158378,-0.01811695,-0.0342386,-0.03303536,-0.03987367,0.04933658,-0.00382045,0.07524048,0.00895482,0.03214128,0.01871798,0.01222357,0.01570497,-0.02395699,-0.04478823,0.07492299,0.01087463,-0.01380036,0.01077467,0.01615276,0.06867919,-0.00440606,0.01381231,0.01584904,0.02692045,0.04153363,0.0005568,0.0222035,-0.0115415,-0.04194854,-0.19381902,-0.01452451,-0.024488,0.01306015,0.03866404,0.00584385,0.03194629,-0.014231,0.04948438,0.05271913,0.08336084,-0.00656163,0.03247742,0.06934236,-0.00569064,0.00516384,-0.09072004,0.00342526,-0.02690424,0.0798323,0.00165051,0.04943795,-0.04032414,-0.06441832,0.04951747,-0.02064014,0.17878029,0.0404017,-0.03464467,-0.0275528,0.10568957,0.02091508,0.00165593,-0.0039933,0.05728492,0.03905797,-0.04435598,-0.01048563,-0.05961999,-0.05700475,-0.06673481,0.00512721,-0.00553452,-0.11203953,-0.02810091,-0.02982571,-0.023706,0.05007101,0.00439774,0.0546601,0.04706861,-0.00183929,0.04712184,0.01670726,0.06710234,-0.01118638,-0.08251183,-0.02966674,0.01680192,0.03857324,0.00805406,-0.01944516,0.06416368,-0.01272498,0.01990199,0.00640369,-0.00374342,-0.04610264,-0.02858807,0.03716532,0.01756169,0.02298724,0.04122036,0.03183729,-0.00168169,0.04292736,0.06037084,-0.00795554,0.03037916,0.01800408,0.01342453,-0.08548447,0.00970579,0.03846163,0.05702822,0.04798217,0.01884169,0.03793965,-0.00161587,-0.0067139,-0.00596327,0.02163194,-0.00180621,0.07137549,0.02408071,-0.01087679,-0.28526604,0.02528387,-0.06529725,0.01967289,0.00079569,0.0296784,0.03552051,0.00394518,0.01061653,-0.04889703,0.08697427,0.09994649,0.00693854,-0.08030061,0.02146379,-0.0521751,-0.03304458,-0.02621447,0.05894477,-0.00687395,0.05146948,0.03665382,0.22473064,0.02152794,0.01960449,0.02360998,0.01203093,-0.01575726,-0.04561158,0.02921968,-0.01944784,-0.01120622,0.05277014,-0.01707411,-0.01078806,0.06143435,0.01142933,0.0384878,-0.02255716,0.03677483,-0.08144535,-0.00262344,-0.00606181,0.02508679,0.07357783,0.04076023,-0.05862473,-0.04353264,0.04758178,0.03629915,-0.03594702,-0.04738312,-0.05463554,-0.03358268,0.00385722,0.02897167,-0.00584428,0.00787552,0.01414448,0.00655922,0.04036504,-0.0168405,0.04386221,0.00688105,0.00268575],"last_embed":{"hash":"1ipevpu","tokens":471}}},"text":null,"length":0,"last_read":{"hash":"1ipevpu","at":1753423501192},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. First option: <i>1 = Check Lotto-6 Combos</i></u>","lines":[117,172],"size":3696,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. First option: <i>1 = Check Lotto-6 Combos</i></u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09296856,-0.04161968,0.00038269,0.03051475,-0.05403693,0.0972585,0.03989639,0.02128267,0.04234255,0.04105297,0.03514348,-0.04388142,0.01042141,0.0063233,-0.00733689,-0.01365454,-0.00022402,-0.03267314,-0.07913632,-0.02683083,0.08510552,-0.06200391,-0.04735145,-0.06525584,0.06856477,-0.00104559,-0.0141591,-0.06230104,-0.06846146,-0.22993791,-0.00839802,0.03119655,-0.00044876,-0.05936626,-0.07683163,-0.01452633,-0.06530609,0.10984661,-0.00988699,0.00846005,0.02926667,0.0112649,-0.03822316,-0.02480862,-0.0467226,-0.04543633,-0.02076717,0.04039413,0.06732443,0.02515704,-0.04345654,-0.01472088,0.04310305,0.01296773,0.06154443,0.0281692,0.05896599,0.06150795,0.00987615,0.01677531,0.03445451,0.08390285,-0.20056708,0.04361441,-0.01473005,-0.00154131,-0.01215895,0.00753738,-0.01363348,0.1045329,0.01590482,-0.01309096,-0.01178512,0.04645345,0.02451573,-0.02869881,-0.06355763,-0.10513555,-0.05899218,0.04871104,-0.08363294,-0.02785752,-0.00926738,-0.02432677,-0.00318009,0.02522074,-0.01649052,0.01781039,0.01010083,-0.04557374,0.03341053,0.01599329,-0.00999304,0.00385919,-0.0282002,-0.00161573,0.01404303,-0.04165881,-0.01074547,0.13297509,0.02648864,0.03354645,0.01340214,0.03394679,0.03674895,-0.06964441,-0.05148001,-0.05917837,-0.02259572,0.00073223,0.06164554,0.00730865,0.04788967,-0.05372833,-0.05780594,0.06044219,0.03121888,-0.0005568,0.0026079,0.00140129,-0.01544859,0.00492891,0.01274737,0.01828934,-0.03955724,-0.00004576,0.04571993,0.07676679,0.01627055,0.01978622,0.03694529,0.00511942,-0.12992243,-0.07551322,-0.06256717,-0.01477991,0.01453904,-0.01118752,0.01496084,-0.00279869,-0.01402592,-0.1090681,0.03183156,-0.11091628,0.00987214,0.04502027,-0.00508633,0.01598736,0.06996904,0.01776475,-0.03719249,-0.01044313,0.0124665,-0.04011512,-0.01496513,-0.03437898,0.03292331,0.08392462,-0.02071509,0.02743858,-0.05981246,-0.01152525,-0.05589871,0.1132026,-0.00334264,-0.09569207,0.00119265,0.00858514,-0.04196793,-0.09411701,-0.02974691,-0.00878836,-0.0342755,0.01804768,0.07452872,0.02435937,-0.0702314,-0.04770527,-0.03387359,0.03442791,-0.02472445,-0.00875416,-0.07010815,0.02240326,-0.03551728,-0.07455074,0.04762966,0.00083439,0.02523909,0.03414558,-0.0228682,-0.00043566,-0.03897972,0.03229875,-0.01771024,-0.03331888,-0.03364377,-0.04159425,0.05132639,-0.00552277,0.07676923,0.01145672,0.0316261,0.01709161,0.01057812,0.01698102,-0.02313836,-0.04447002,0.0730797,0.00949066,-0.01391148,0.01034021,0.01639561,0.0664828,-0.00059858,0.01428884,0.01589503,0.02669469,0.04245723,-0.00038421,0.02165896,-0.01315822,-0.04061993,-0.19590436,-0.0127081,-0.02474113,0.01427498,0.03791755,0.00366504,0.02935594,-0.01454339,0.05063797,0.05121304,0.08573338,-0.00724566,0.03380379,0.07071236,-0.00582608,0.00667034,-0.08826906,0.00488758,-0.02574962,0.07752306,-0.00027249,0.04953701,-0.03858212,-0.06357304,0.05014983,-0.01991401,0.18023525,0.04160302,-0.03435168,-0.03076796,0.10666739,0.01968709,0.00165591,-0.00445013,0.05970977,0.03855123,-0.04440449,-0.01238026,-0.05756093,-0.05786231,-0.06817811,0.00377803,-0.00415044,-0.11128888,-0.0252324,-0.03138937,-0.02403349,0.04997942,0.00468659,0.05666691,0.04601379,-0.00159931,0.04906959,0.01626006,0.06605969,-0.0109896,-0.08356169,-0.03177633,0.01943289,0.03791802,0.00852935,-0.02000887,0.0650587,-0.01412369,0.02033823,0.00755572,-0.00280745,-0.04512756,-0.02716499,0.03861337,0.01785727,0.02260719,0.04220337,0.03380375,-0.00107109,0.04419713,0.06035069,-0.01015012,0.03120863,0.02104508,0.0138607,-0.08760934,0.00939848,0.0384022,0.0567103,0.04778226,0.01980486,0.03792305,-0.00170198,-0.00719324,-0.00823007,0.02046761,-0.00026515,0.06978382,0.02417764,-0.0083797,-0.28576735,0.02742298,-0.06524926,0.01857772,0.00010761,0.02960048,0.03518657,0.00278305,0.00917915,-0.05049768,0.08359735,0.10074601,0.00754622,-0.07995097,0.02275726,-0.05013931,-0.0348193,-0.02504908,0.05768278,-0.00553976,0.05328427,0.03924306,0.22534059,0.02103918,0.01924984,0.02314642,0.01319714,-0.01824499,-0.04617617,0.02973858,-0.01943681,-0.00970255,0.05319682,-0.01762693,-0.00840694,0.06445967,0.01197898,0.03823151,-0.02379741,0.03643917,-0.08034037,-0.00384616,-0.00547329,0.02232233,0.07449561,0.0403809,-0.05824163,-0.04511216,0.04809552,0.03360571,-0.03614696,-0.04726825,-0.05347598,-0.03572943,0.00373527,0.02723098,-0.00498755,0.00637907,0.01277399,0.00645465,0.04171713,-0.01503963,0.04288212,0.00778838,0.00063283],"last_embed":{"hash":"fvifxx","tokens":470}}},"text":null,"length":0,"last_read":{"hash":"fvifxx","at":1753423501403},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. First option: <i>1 = Check Lotto-6 Combos</i></u>#{1}","lines":[119,172],"size":3635,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07771649,-0.04845157,0.00024497,0.04131325,-0.04819636,0.09422044,0.04627046,-0.00094677,0.04100658,0.02746034,0.03114044,-0.03782978,0.02106461,-0.00295869,-0.02113913,-0.00596327,-0.00401203,-0.03975964,-0.06635813,-0.00410645,0.05574385,-0.06235991,-0.04911687,-0.05964405,0.06609077,-0.00384705,-0.03002919,-0.06084206,-0.08179823,-0.23455955,-0.00732078,0.05483142,0.00338712,-0.07621632,-0.0796684,-0.03037116,-0.04119423,0.11458291,-0.00203395,0.01319171,0.01334509,0.03280482,-0.03912634,-0.01862977,-0.0299443,-0.0314902,-0.02948099,0.03757507,0.07282194,0.03358031,-0.03246256,-0.01141961,0.03634201,0.01144952,0.070741,0.03028096,0.07003857,0.05814862,-0.01341715,0.0214433,0.02694035,0.08471078,-0.18780893,0.04391008,-0.01377903,-0.00405686,-0.02682715,0.01147149,-0.01930704,0.10448314,0.01272467,-0.00506205,-0.01460599,0.03635324,0.04363801,-0.01673511,-0.05294293,-0.0987213,-0.05210797,0.04364121,-0.0613257,-0.03165726,-0.0026368,-0.02040743,0.01449607,0.01339468,-0.00118242,0.0426802,0.02360379,-0.0533751,0.03926396,0.02796631,-0.03564045,0.01397839,-0.00327886,0.00748804,0.03147067,-0.03236109,0.00412356,0.12786172,0.05979863,0.03430039,0.01930946,0.03654854,0.03520434,-0.0554138,-0.05133108,-0.05479099,-0.02798909,-0.00381009,0.08040723,0.00855598,0.07424078,-0.05269883,-0.06901869,0.05373147,0.02755279,-0.00310597,0.00690993,-0.00531787,-0.00892163,0.02115813,0.02258079,0.00546363,-0.03319818,-0.00114681,0.0440475,0.09356206,0.01363549,0.02831347,0.03028146,0.00199576,-0.1500739,-0.05692428,-0.04519941,-0.02289284,0.01075093,-0.01320284,0.01597318,0.00288246,-0.01401665,-0.10224494,0.03334886,-0.12314182,0.00436338,0.04929137,-0.00727862,-0.00609596,0.04550507,0.02201499,-0.0302591,-0.00610136,-0.01058718,-0.04789093,-0.03007737,-0.0290386,0.0545844,0.08423775,-0.03569176,0.01673159,-0.05951228,-0.02266346,-0.07317249,0.11503914,0.00005187,-0.07489119,-0.01372838,0.01285166,-0.05835924,-0.0822121,-0.03279813,0.00016646,-0.03952543,0.01862753,0.06471156,0.02223457,-0.06841923,-0.03970232,-0.03332594,0.0217329,-0.02215972,-0.01288454,-0.06205348,0.02273865,-0.03294144,-0.04454652,0.03879245,0.00957648,0.02750236,0.03740788,-0.04775726,-0.00820293,-0.05789355,0.0481541,-0.01859572,-0.03117321,-0.02394909,-0.03051075,0.04775455,0.00005829,0.0267992,-0.00648004,0.03553575,0.01657705,0.00199062,0.03053716,-0.01095324,-0.03355203,0.06598297,0.01360423,-0.02126542,-0.00559942,0.00919923,0.06470983,-0.01254267,0.02387169,0.01134443,0.02437784,0.01983405,0.00602369,0.03457266,-0.01383072,-0.05367428,-0.19504401,-0.04318915,-0.01396378,0.0156455,0.0469746,-0.00478439,0.03300021,0.00039454,0.0534883,0.06579819,0.07889796,-0.01294457,0.01916827,0.06509921,-0.02259957,0.00216591,-0.08851405,-0.01569269,-0.00548741,0.09508163,-0.00450452,0.05131111,-0.04124571,-0.05066497,0.06345081,-0.03478158,0.16515817,0.0500338,-0.05049102,-0.00903062,0.09472372,0.02630725,-0.01359584,0.01245853,0.0711396,0.02779702,-0.04396439,-0.00182825,-0.0720996,-0.04280125,-0.05489195,0.00325376,-0.00674567,-0.10483301,-0.03394671,-0.03415993,-0.02934393,0.04736344,-0.01211408,0.02381641,0.04225714,-0.01914648,0.03548881,0.02512042,0.05796311,0.00370119,-0.08146808,-0.01601915,0.00510572,0.03688331,0.00004659,-0.01415396,0.05800706,-0.02931411,0.03609498,0.01560191,-0.02698332,-0.03581013,-0.01285524,0.0239696,0.0200401,0.02730284,0.06194942,0.03050828,-0.02432354,0.03047189,0.03643739,-0.04066256,0.04811481,0.02028271,0.02238191,-0.10264832,0.01543299,0.04284479,0.06883706,0.0502242,0.01578347,0.04595901,-0.01651473,0.00215634,-0.01033067,0.02783705,-0.03387734,0.06329234,0.01868297,-0.00936517,-0.27237028,0.03303288,-0.07009623,0.01821772,-0.00766419,0.01807859,0.01851726,0.01439445,0.01924028,-0.03849898,0.10505156,0.09913829,0.02417347,-0.08165083,0.01371241,-0.03059111,-0.04034779,-0.0237781,0.05203994,-0.00211931,0.0594076,0.03335703,0.22727881,0.0271243,0.02282181,0.02151779,-0.00859285,-0.02837386,-0.03280533,0.03829566,-0.02482399,-0.00634126,0.06024642,-0.0287464,-0.02909017,0.04733063,0.00812751,0.03042811,-0.0292563,0.03540163,-0.07480025,-0.02286839,-0.01111564,0.02869532,0.09147628,0.05538277,-0.04853106,-0.05512303,0.04170029,0.05175811,-0.04049026,-0.06248517,-0.07110746,-0.03969367,0.01326974,0.05009769,-0.02024401,-0.00509713,0.0167036,0.01447663,0.00290786,-0.01432557,0.06561945,-0.00348743,0.0093493],"last_embed":{"hash":"1b4kiyd","tokens":351}}},"text":null,"length":0,"last_read":{"hash":"1b4kiyd","at":1753423501609},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>","lines":[173,234],"size":4227,"outlinks":[{"title":"_**Draws in lotto 6/49 Romania**_","target":"https://saliu.com/freeware/6-49-RO","line":42},{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":61}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08070073,-0.04875805,-0.00546619,0.04029758,-0.04864978,0.07550742,0.04380287,0.00177295,0.04826203,0.03536014,0.03286096,-0.03783629,0.03134727,0.01799403,-0.01890765,-0.00786101,-0.00982778,-0.03080369,-0.06628271,-0.00967128,0.05879118,-0.0678324,-0.03783742,-0.06458779,0.04677202,0.01270603,-0.02499567,-0.05071026,-0.08385225,-0.2355926,0.00357057,0.04632844,-0.01317801,-0.0658469,-0.0807407,-0.02859704,-0.04304724,0.10618376,-0.01204039,0.02366009,0.0101194,0.02902853,-0.0260914,-0.01932933,-0.0267596,-0.03425597,-0.04720416,0.04000709,0.04846458,0.03529879,-0.02836946,-0.0022593,0.04003694,0.00764535,0.07286921,0.03138255,0.07058644,0.06038222,0.01500782,0.03227386,0.03655366,0.07867002,-0.17306963,0.05197323,-0.00852608,0.00698781,-0.02475808,-0.00186055,-0.03927444,0.08962281,0.00674078,0.00436327,-0.00900216,0.03134551,0.04355115,-0.02492604,-0.04069774,-0.09657733,-0.04663651,0.04645986,-0.0624976,-0.03296803,-0.00100776,-0.01736443,0.0098029,0.00969841,0.00767409,0.05640808,0.02122927,-0.04644561,0.04487763,0.03137917,-0.02230399,0.01259871,0.01295524,0.01949438,0.03062496,-0.0443087,-0.00484255,0.11683524,0.06421375,0.02455864,-0.00423438,0.04463629,0.05223518,-0.05139642,-0.05632707,-0.06959578,-0.02968213,0.00786082,0.07018837,0.00396708,0.08324645,-0.05371708,-0.07169685,0.04592162,0.0214589,-0.0165244,0.00891156,-0.00605075,-0.0257323,0.03257608,0.01666793,-0.00624827,-0.02121638,-0.00521014,0.02588789,0.09275296,0.02814046,0.01448877,0.03675071,-0.02391034,-0.13620688,-0.05547019,-0.03847707,-0.02102401,0.00904707,-0.00681726,0.02354252,0.00432293,-0.02214066,-0.1088457,0.03710479,-0.11786863,0.00098283,0.04844331,0.00185766,-0.00774777,0.04721535,0.01397949,-0.03588325,-0.01911558,-0.02700003,-0.04956913,-0.0397991,-0.03714542,0.05699056,0.0986551,-0.04608795,0.01171698,-0.0537174,-0.03188514,-0.07246522,0.12613414,-0.01538151,-0.05739088,-0.01344803,0.02556307,-0.05110586,-0.07604254,-0.03711437,-0.00459131,-0.0365903,0.02279309,0.0709106,0.0082436,-0.07527137,-0.03741963,-0.04029742,0.02892061,-0.02297396,-0.0024319,-0.05182925,0.00997683,-0.03358323,-0.05369234,0.03246572,0.00589804,0.02319256,0.04280343,-0.04777423,-0.00774651,-0.05771131,0.06458618,-0.01495336,-0.03213017,-0.0218095,-0.03144402,0.03512855,0.00696762,0.01930097,-0.02140042,0.03019986,0.02462065,-0.00373594,0.04962787,-0.00798781,-0.04665572,0.08070213,0.00433171,-0.01989863,-0.01627444,-0.00264303,0.07597328,-0.02785723,0.02740919,0.02137258,0.010376,0.01213778,0.00489687,0.01351148,-0.0013001,-0.0424282,-0.19226396,-0.04795519,-0.03354722,0.01704296,0.0432141,-0.00457028,0.02948429,0.0053367,0.05512244,0.07441315,0.07848652,-0.01722617,0.00544701,0.06016302,-0.01269588,-0.00764496,-0.10047723,-0.0102467,-0.01255766,0.09336454,-0.00721308,0.05211354,-0.04449048,-0.05162397,0.06289343,-0.03214878,0.17104205,0.04841192,-0.03632611,-0.00656261,0.09005736,0.01988783,-0.00070408,0.01260556,0.06115664,0.01429955,-0.04574167,-0.0228874,-0.07678622,-0.04808659,-0.064492,0.01011722,-0.01433732,-0.10063766,-0.03053348,-0.01668862,-0.03536519,0.05476202,-0.01225025,0.03956103,0.03271988,-0.01006698,0.03953736,0.03407007,0.06750879,-0.00470722,-0.08427902,-0.00991997,0.00672667,0.0468116,0.00093391,-0.02044258,0.05557436,-0.0249328,0.03358413,0.02114307,-0.02133421,-0.03064632,0.01508957,0.02464853,0.0061956,0.03443274,0.04404742,0.02761122,-0.02726747,0.02942713,0.05091666,-0.03286503,0.04255524,0.02728869,0.02990776,-0.08070303,0.00305604,0.05409049,0.03817756,0.02642413,0.01241075,0.05283244,-0.01161885,0.00467839,-0.01848422,0.04106468,-0.04658129,0.05127722,0.01507341,0.00379039,-0.2716113,0.03003332,-0.05831406,0.02428576,0.00605991,-0.00441992,0.03602657,0.01093967,0.01252523,-0.04837641,0.1055808,0.09319177,0.02596862,-0.07596195,0.00345983,-0.04650942,-0.02951838,-0.02212911,0.06040803,0.01292908,0.06126539,0.03934847,0.23634575,0.01933338,0.03055085,0.03207015,0.00124952,-0.0206436,-0.04396759,0.0461902,-0.05015852,-0.0058448,0.07090886,-0.03414154,-0.02770113,0.03150995,0.01275363,0.02630906,-0.02829793,0.03189336,-0.07395264,-0.01389886,-0.01554302,0.0376567,0.10402548,0.05427598,-0.03983987,-0.06345784,0.04960071,0.04368392,-0.05370593,-0.04202421,-0.07212227,-0.04454881,0.01135281,0.05139247,-0.0192196,-0.00669644,0.0182604,0.02502239,-0.00381251,-0.00919112,0.07107633,-0.00772878,-0.00258407],"last_embed":{"hash":"7m6krd","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"7m6krd","at":1753423501737},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{1}","lines":[175,207],"size":2107,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11149951,-0.03787983,-0.02098447,0.03541075,-0.07750767,0.08326241,-0.016027,-0.00174749,0.02745403,0.01739084,0.05064686,-0.03180329,0.00919928,0.00265998,0.02643581,-0.02151436,0.02066985,-0.0010584,-0.07954743,-0.02721813,0.06623986,-0.07813153,-0.03448705,-0.05220245,0.03952513,0.00443092,-0.02709693,-0.04508016,-0.05578715,-0.21505786,0.01030834,0.03286892,-0.03099826,-0.05214993,-0.09434786,-0.02451085,-0.03622909,0.07773852,-0.01943514,0.02474475,0.01169184,0.01092244,-0.0571546,-0.01384267,-0.01259773,-0.00697006,0.02809394,0.0355141,0.12117448,0.03130814,0.00315113,0.03387433,-0.00276265,0.0186868,0.04702101,0.02356666,0.08612876,0.06358243,-0.02186692,0.00738033,0.01900033,0.05898296,-0.19828708,0.07147106,-0.01632386,-0.00333003,0.0110282,-0.01447708,0.00028777,0.06322756,-0.01014426,-0.01403859,-0.02412229,0.056899,0.044165,-0.0381739,-0.07106698,-0.06369197,-0.05089539,0.03550545,-0.05872289,-0.00410394,0.0109265,-0.01767673,0.01669353,0.02152552,0.00248263,0.04623278,0.0256146,-0.09033259,0.01987206,0.00505013,0.02271029,0.0379623,-0.02476981,-0.00577656,0.03846977,-0.03239558,0.00310701,0.12712438,0.04364295,0.0232128,0.02056108,0.03034754,0.01170987,-0.04297174,-0.04204292,-0.0248629,-0.0221995,0.02825408,0.05645256,0.02799294,0.06369744,-0.09334507,-0.02637729,0.0351249,-0.01012826,-0.00204644,-0.00343118,0.00634332,-0.03233764,-0.02180575,0.03815838,0.01322735,0.02885769,-0.02954236,0.02925673,0.05081892,0.00471004,0.02120191,0.01595035,0.00259232,-0.16449913,-0.07615392,-0.04224253,-0.03553317,-0.0010097,-0.01602983,-0.01138926,-0.00611482,-0.00287531,-0.09597153,0.04057511,-0.11666197,0.01086822,0.04250185,0.04100686,0.01543099,0.05634114,0.01583958,-0.04524589,-0.00663396,0.01089055,-0.03661498,-0.02727309,-0.01610138,0.09031645,0.06372777,-0.0368465,0.01751714,-0.0602993,-0.04975244,-0.06944088,0.13485576,-0.0156202,-0.09118388,0.00380835,0.03387605,-0.02470488,-0.09668311,-0.03623183,-0.00253364,-0.0335732,0.00587079,0.13511416,0.00358857,-0.09422159,-0.05597822,-0.06935336,0.02684051,-0.06187698,-0.04017314,-0.04007303,0.02708983,-0.0217805,-0.07695603,0.01430698,-0.02361847,0.02184873,0.04480098,-0.03345951,0.03832101,-0.05272369,0.02084486,-0.05220519,0.00681172,-0.03474262,-0.03178092,0.04756225,-0.0007993,0.05539976,0.0073551,0.021595,0.02919049,-0.00890859,0.03592695,0.00751611,-0.0377976,0.07526022,0.01189053,-0.01809859,0.0327597,0.04222749,0.05834378,-0.05357821,0.04234606,0.02927205,0.01681085,-0.01924478,0.00344463,0.04071836,-0.00393596,-0.05982959,-0.17171358,-0.02162453,-0.04498461,0.00254961,0.04187873,-0.02737282,0.08302715,-0.02690525,0.05430559,0.04828704,0.11608611,-0.02157003,0.03036557,0.08291584,-0.03761848,0.05057459,-0.04009628,-0.00711516,-0.01021461,0.08671944,0.00162995,0.01762914,-0.01630921,-0.05700342,0.06556821,-0.02823708,0.1554547,0.03463338,-0.01661283,-0.00369656,0.06721568,0.00634554,0.01368018,-0.05601589,0.04242654,0.02201662,-0.05988889,-0.02343939,-0.03369841,-0.02430971,-0.02644699,0.00097107,0.02774537,-0.08931255,-0.0050534,-0.03063812,-0.00659336,0.0653511,0.01303154,0.01455511,0.05467912,0.02574671,0.03140106,0.04871326,0.05032173,-0.00134113,-0.08155491,0.01053705,-0.02443917,0.01762264,-0.01028135,-0.01627822,0.05208079,-0.01818946,0.03964335,0.01658531,-0.03046775,-0.06653465,0.02905804,0.03333538,0.01288343,0.04491079,0.03022462,0.04044127,-0.02414624,0.00368388,0.06696794,-0.00450965,0.04563254,0.02289874,0.00016998,-0.07577643,0.00538891,0.0420883,0.05128073,0.03918876,0.02382109,0.01946742,-0.04326082,-0.00098596,-0.00093793,0.00976675,-0.02737381,0.07587826,0.02571192,-0.02581158,-0.26839831,0.04069659,-0.03926856,0.05223581,0.00787405,-0.01933691,0.01370256,-0.03380907,0.00021488,-0.05932149,0.07995968,0.07273888,0.00375154,-0.0767399,0.05801754,-0.01766791,-0.01021671,0.00200084,0.05515709,-0.00520551,0.05300909,0.0185822,0.23654813,0.03613776,0.02373427,0.00237139,-0.0176027,-0.02739905,-0.05306967,0.06177977,-0.04284284,-0.01919167,-0.00399113,-0.01952944,-0.03997139,0.0425331,-0.00883287,0.03549511,-0.02651243,0.05023563,-0.07561535,-0.0058144,-0.05313193,0.01990096,0.07734761,0.03336962,-0.0499605,-0.03635988,0.02183746,0.04779341,-0.01906255,-0.05241104,-0.09215224,-0.03047596,0.03138302,0.03908164,-0.00790937,-0.0242885,0.03679514,-0.00072105,0.03326702,-0.01985608,0.06972067,-0.00235696,0.01119011],"last_embed":{"hash":"avd5w1","tokens":124}}},"text":null,"length":0,"last_read":{"hash":"avd5w1","at":1753423501911},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{8}","lines":[214,215],"size":221,"outlinks":[{"title":"_**Draws in lotto 6/49 Romania**_","target":"https://saliu.com/freeware/6-49-RO","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08777366,-0.05662023,-0.04438836,0.01737174,-0.04553822,0.08937172,0.02557815,-0.01068504,0.04654695,0.01648671,0.01005693,-0.00287504,0.01672136,-0.01367611,-0.03712803,0.01666811,-0.01568495,-0.01457258,-0.08532983,0.00020762,0.06981,-0.05693069,-0.04151899,-0.08729181,0.04355697,0.02432579,-0.03334481,-0.0480766,-0.06387227,-0.24935904,0.01345664,0.01906567,0.00519257,-0.09192932,-0.07221605,-0.02931324,-0.04398802,0.10506801,-0.00134657,0.01987827,0.0078842,0.00475619,-0.01597057,-0.00213367,-0.01686769,-0.03534041,-0.03628586,0.01577525,0.04406304,0.01352398,-0.04490974,0.01687876,0.02658764,0.00995568,0.06479477,0.02328405,0.04928661,0.07723384,0.03030135,0.04486101,0.0264901,0.07042983,-0.17352335,0.04607364,-0.00542068,-0.01060824,-0.01560873,-0.02324937,-0.00639216,0.08054519,0.00645255,0.01608406,-0.02582696,0.06055747,0.04156113,-0.01354791,-0.04129507,-0.09266753,-0.02369945,0.02964151,-0.05186208,-0.03497944,-0.03639327,-0.01160898,0.00764719,-0.00186974,0.02306452,0.0495416,0.04941831,-0.05283945,0.03333791,0.04106816,-0.04611832,0.02041709,0.03377052,-0.00373719,0.03449093,-0.01992464,0.0260478,0.11442081,0.04633731,0.02240512,-0.00100161,0.02540885,0.04437974,-0.06266011,-0.06427006,-0.02263901,-0.03098284,0.03890739,0.05829447,-0.00043747,0.0469828,-0.03903794,-0.05662059,0.0327833,0.0138536,0.02142472,0.01959947,-0.02078786,-0.03759464,0.03128313,0.03014988,0.00059956,-0.02467526,-0.00296906,0.0336908,0.09331526,0.01186596,0.01059212,0.04836773,-0.00653389,-0.15715247,-0.03664936,-0.03662469,-0.03824881,0.02009975,0.0211686,-0.0161745,0.01279929,-0.00547817,-0.07262349,0.04359902,-0.07998774,-0.03840079,0.05754173,0.00497741,-0.00472411,0.05006966,0.01472523,-0.03378671,0.00531825,-0.04155052,-0.05809809,0.00925925,-0.03064794,0.07388216,0.05261673,-0.03781288,0.00058287,-0.0501348,-0.04602116,-0.03685475,0.11410453,0.00167205,-0.04887624,-0.00010535,0.03389973,-0.04739966,-0.11064578,-0.01311759,-0.01994385,-0.04090799,0.00610861,0.08664837,0.01488568,-0.09613927,-0.08789499,0.00341436,0.02371215,-0.00851832,-0.00762981,-0.03604241,0.00366951,-0.01799379,-0.06310367,0.0303054,-0.00010728,0.01009713,0.01293014,-0.02038581,0.01242664,-0.07843126,0.02984837,-0.01713357,-0.01965573,-0.04765934,-0.04311918,0.06705992,-0.00875842,-0.0053004,-0.00888099,0.01754574,0.00320216,-0.01805127,0.01976745,0.00571625,-0.04364567,0.07748223,0.02275362,-0.01871282,-0.00516531,0.02077158,0.08228862,-0.05014373,0.03231407,0.02312143,0.04016175,0.01277658,-0.02099218,0.0194291,0.01309764,-0.03083429,-0.16936989,-0.02375208,-0.02199933,0.01622246,0.03124006,0.01179441,0.02596292,-0.03764772,0.0753958,0.06221255,0.08465212,-0.03407335,0.00837732,0.07228898,-0.01278867,0.01042357,-0.1264995,-0.01260536,-0.05522121,0.08279124,-0.00859962,0.03687378,-0.06383371,-0.04983981,0.04110008,-0.00244349,0.15319255,0.03516421,-0.00657156,-0.00720592,0.07471804,0.00981314,0.00876809,0.03240572,0.05323677,0.02287902,-0.03904357,-0.06009081,-0.08210502,-0.02409702,-0.06398922,0.03627593,-0.01325717,-0.10892291,-0.04990679,-0.02665663,-0.031413,0.07062186,0.01337527,0.06264643,0.05462696,0.02736578,0.06901804,0.0364219,0.05630403,0.00025109,-0.08049022,0.00921598,0.02480846,0.05166129,0.00155946,-0.00706997,0.05350401,-0.03427869,0.03047371,0.00234126,-0.00368322,-0.05903025,0.00608728,-0.00455373,0.03853053,0.07634731,0.01734874,0.02166061,-0.01940826,0.02402366,0.06375546,-0.02405993,0.05438267,0.02390527,-0.00407295,-0.08562679,0.01093773,0.06361444,0.0760513,0.01938137,0.00749745,0.05316944,-0.01102955,0.01675712,0.01661555,0.02004799,-0.03650765,0.04260993,-0.01786987,-0.00707438,-0.26219991,0.01945477,-0.05477608,0.04910787,-0.00256333,-0.03007558,0.05399268,-0.00526923,0.00373688,-0.03548917,0.09074061,0.08465093,0.02651308,-0.06634486,0.03044017,-0.06058217,-0.00660152,-0.00622697,0.06510749,-0.00270262,0.0653656,0.02557095,0.24501756,0.01880317,0.01991716,0.03446167,0.00154625,-0.01854011,-0.05244052,0.040365,-0.01080629,-0.00391131,0.05414454,-0.04778548,-0.02265023,0.0357538,-0.00498402,-0.00527346,-0.04370826,0.02653902,-0.04847172,-0.00725217,-0.00981299,0.01880564,0.13691673,0.02484071,-0.0455284,-0.05733452,0.05108391,0.04162674,-0.05655572,-0.06961577,-0.05503406,-0.10437972,0.00363008,0.03939559,0.01787407,0.00632671,-0.0011015,0.00298724,0.01796998,0.0035339,0.0555899,0.01022361,-0.00687956],"last_embed":{"hash":"rixgvw","tokens":437}}},"text":null,"length":0,"last_read":{"hash":"rixgvw","at":1753423501953},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#By Ion Saliu,#<u>B. Second option: 2 = Check Groups of Numbers</u>#{9}","lines":[216,234],"size":1060,"outlinks":[{"title":"![LottoWheeler is the best software to convert lotto wheels to real lottery picks.","target":"https://saliu.com/ScreenImgs/lotto-wheeler.gif","line":18}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09442272,-0.04175971,-0.01477183,-0.00717321,-0.08093031,0.08344568,-0.01548986,-0.01841324,0.04001961,-0.00841368,0.0140638,-0.00207117,0.01873269,-0.00280294,0.01331474,-0.02496455,0.01848235,0.0046199,-0.04162899,-0.03235284,0.09117094,-0.0344262,-0.0651589,-0.06211199,0.02731003,0.01415408,-0.04026184,-0.06919466,-0.04277283,-0.23684086,0.03895502,0.04062402,-0.01061442,-0.04292485,-0.09283511,0.00142067,-0.04009727,0.09636348,-0.06236411,0.0407912,0.0065083,0.0162925,-0.00632702,-0.00670238,0.0066703,-0.05522953,-0.01287152,0.0393762,0.04537747,-0.02297323,-0.07005966,-0.01417544,0.01115396,0.02029439,0.05856041,0.05060823,0.05542448,0.10112293,0.00457941,0.03707394,0.05641351,0.0979038,-0.1867232,0.07003291,0.03428581,0.02067463,-0.01717243,-0.02756098,0.0116252,0.08627558,0.00620619,-0.00243872,-0.01320117,0.05239567,0.03153599,-0.01152555,-0.0453265,-0.08903726,-0.04176068,0.02399889,-0.02127853,0.00027768,-0.00914695,0.00676693,-0.01878474,0.0488359,0.01122767,0.00338125,0.03647851,-0.07575952,0.01178028,0.03565689,0.01850664,0.01204758,0.01082482,0.00154623,0.04106669,-0.01943985,0.01457893,0.10875205,0.01969555,0.01955894,0.00844503,0.02069359,0.03675644,-0.05751044,-0.04628183,-0.08442292,-0.03349001,0.06411161,0.08613886,0.0090614,0.04435129,-0.01707271,-0.04318807,0.0082093,-0.00730335,0.00131715,0.01415397,-0.00673945,-0.01855761,-0.00991523,0.029728,0.02304906,-0.01628398,-0.02171209,-0.01285597,0.09191749,-0.00108967,0.00640321,0.04907862,0.00947404,-0.12859966,-0.03583821,-0.02734935,-0.02644676,0.01062788,-0.00864929,-0.02010384,0.04818832,-0.03184479,-0.06379335,0.07604636,-0.10802727,-0.04023555,0.06994617,0.04566593,0.00209233,0.02989937,0.02975924,-0.01360019,-0.00636268,-0.03993007,-0.03096959,-0.01262977,-0.00737356,0.09697466,0.06281258,-0.06231171,0.03929833,-0.05096621,-0.04157157,-0.0491737,0.15683164,-0.00728276,-0.10294808,0.00524988,0.06462458,-0.01850122,-0.10303661,-0.03562758,-0.0082692,-0.05649372,0.00103675,0.08037176,-0.0053755,-0.10594071,-0.06633653,-0.0125502,0.01110926,-0.01870923,-0.03940496,-0.0292703,0.01552322,-0.02511788,-0.08208846,0.01488213,-0.02968797,-0.00119568,0.01617427,-0.03849581,0.03275327,-0.0164903,0.00539471,-0.03377634,-0.01258467,-0.02340301,-0.0173563,0.06800111,0.00206331,-0.00536695,-0.00644413,0.0374978,0.01576569,-0.01516135,0.03329437,-0.02567129,-0.04649634,0.0710621,0.04750209,-0.02340375,0.02862393,0.03745829,0.07320711,-0.04134453,0.05079372,0.02175778,0.0239035,-0.0309062,0.00521857,0.01302503,0.02387091,-0.06234574,-0.18504073,-0.046571,-0.03822352,-0.00506486,0.06525586,-0.02097988,0.01452087,-0.02553995,0.02430994,0.06041444,0.08275548,-0.05071684,0.02727597,0.04394095,-0.00655211,0.01759192,-0.08653033,-0.01741049,-0.06685077,0.07488011,0.00357069,0.03185156,-0.02741741,-0.10455561,0.00866906,-0.01445061,0.11546572,-0.00570152,0.00576431,0.02262395,0.05631658,0.01240654,-0.01819466,-0.00514184,0.04573776,0.03669473,-0.03641893,-0.02403169,-0.03669442,-0.02381092,-0.08379268,0.00564042,0.0056778,-0.10059579,-0.02827342,-0.00424014,-0.02685904,0.00464029,0.01970829,0.04554251,0.02465398,0.0300899,0.02435121,0.02391673,0.04729464,-0.0000603,-0.0778215,0.01796227,-0.01933371,0.05736357,-0.01833897,-0.04185987,0.03750679,-0.0162465,0.06710681,0.01109471,-0.02950495,-0.03592121,-0.01339264,0.01951099,0.00877932,0.10060248,0.05585537,0.03858856,-0.01992985,0.03537533,0.06144776,-0.01097056,0.02912385,0.03957462,-0.02462648,-0.09774958,0.02189332,0.0769927,0.03421905,0.01690353,0.0345293,0.00736738,0.00577882,0.01290862,-0.00657883,0.00727476,-0.03962408,0.0316513,0.04266319,0.0045461,-0.26035851,0.0591605,-0.01677795,0.03368469,-0.01612189,0.00290224,0.02439349,-0.02398529,0.06021488,-0.06019609,0.08036263,0.07722063,0.01258925,-0.05946837,0.01332792,-0.00523991,0.01967647,-0.02092158,0.06105534,0.00303455,0.0387444,0.05919155,0.23732316,0.00643989,0.02681617,0.03788229,-0.02019657,0.01654823,-0.06103821,0.03075123,-0.01307168,-0.01608453,0.04852586,-0.021842,-0.03323292,0.04582904,-0.01709974,0.00260662,0.00717521,0.06184486,-0.0764128,-0.00009719,-0.03533816,0.02989911,0.10902903,0.04622284,-0.03323235,-0.09227798,0.04736641,0.05043901,-0.04168308,-0.07157848,-0.07719654,-0.04503641,0.04574474,0.0397457,0.01440731,-0.00726165,-0.02768653,-0.02956757,0.04225011,-0.04924615,0.04074036,0.02489181,0.0058205],"last_embed":{"hash":"1n0h9k8","tokens":424}}},"text":null,"length":0,"last_read":{"hash":"1n0h9k8","at":1753423502109},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)","lines":[235,258],"size":2704,"outlinks":[{"title":"<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":5},{"title":"_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":7},{"title":"_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_","target":"https://saliu.com/bbs/messages/532.html","line":8},{"title":"_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":9},{"title":"**<u>Lotto Wheels</u>**","target":"https://saliu.com/lotto_wheels.html","line":10},{"title":"_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_","target":"https://saliu.com/bbs/messages/11.html","line":12},{"title":"_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_","target":"https://saliu.com/positional-lotto-wheels.html","line":14},{"title":"_**Lottery Wheeling Software for Players of Lotto Wheels**_","target":"https://saliu.com/bbs/messages/857.html","line":15},{"title":"**<u>Lottery Software</u>**","target":"https://saliu.com/infodown.html","line":17},{"title":"Playing all-low or all-high lotto numbers leads to far greater chance to win the lottery big-time.","target":"https://saliu.com/HLINE.gif","line":19},{"title":"Forums","target":"https://forums.saliu.com/","line":21},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":21},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":21},{"title":"Contents","target":"https://saliu.com/content/index.html","line":21},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":21},{"title":"Home","target":"https://saliu.com/index.htm","line":21},{"title":"Search","target":"https://saliu.com/Search.htm","line":21},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":21},{"title":"The 12 and 18-number lotto wheels assure best chances for high lottery prizes, including da jackpot.","target":"https://saliu.com/HLINE.gif","line":23}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09427267,-0.04605663,-0.00169755,-0.01818882,-0.06090507,0.0719584,-0.01093322,-0.01983966,0.00862255,-0.01446891,0.02183913,0.00844819,0.01035368,-0.01625405,0.02042359,-0.02272138,0.02728999,0.00903089,-0.04487412,-0.03337761,0.09405679,-0.01658494,-0.05575462,-0.0577164,0.03851318,0.01900055,-0.05532196,-0.07636908,-0.02935255,-0.22101068,0.03153602,0.03710511,-0.02112855,-0.04538199,-0.09435204,-0.01690635,-0.03495235,0.0922692,-0.06684691,0.03239496,0.00812827,0.01070755,-0.02056163,-0.01430484,-0.01241866,-0.05392643,-0.00418768,0.0313832,0.05646702,-0.01407999,-0.05756282,0.00502822,0.00896625,0.02062065,0.04872028,0.05330024,0.06300748,0.10226142,-0.01063151,0.03952962,0.06150283,0.08596227,-0.18076998,0.06807882,0.03509222,0.02338553,0.00904042,-0.01982125,-0.00152123,0.10155644,0.00943906,-0.00578213,-0.02386132,0.069235,0.02944116,-0.02674795,-0.04662712,-0.0874611,-0.05847682,0.01995466,-0.03746008,-0.00426085,-0.00283511,0.00443692,-0.01679117,0.05582431,0.01529123,0.02120604,0.0378388,-0.07801222,0.03690523,0.02344496,0.02861552,0.02391707,0.00213686,-0.01034613,0.0346126,-0.02510252,0.02504811,0.12384862,0.01442881,0.0182324,0.01476532,0.02259126,0.04836591,-0.03852322,-0.03618237,-0.06968837,-0.02657823,0.06903177,0.09235315,0.02002777,0.03960577,-0.03516345,-0.04816259,0.00087004,-0.01604686,0.00002738,0.0081402,-0.00118371,-0.01327985,-0.02529322,0.0252089,0.02364613,-0.01969506,-0.01829138,0.00727089,0.07596667,-0.01739428,0.00494816,0.03965652,0.00704353,-0.15242504,-0.04885413,-0.036432,-0.02716124,0.0012358,-0.02898337,-0.01119458,0.04404533,-0.03812453,-0.06758621,0.05541112,-0.11069455,-0.02882073,0.08845959,0.02256334,0.00457839,0.01466274,0.02338015,-0.00727565,0.00064515,-0.01817148,-0.04011672,-0.00146322,-0.0143733,0.09227952,0.05061738,-0.0410643,0.04209523,-0.05072284,-0.0262594,-0.04395428,0.15850121,-0.01989137,-0.10405515,0.00230486,0.06977272,-0.01329839,-0.11058808,-0.04208256,-0.00769457,-0.07472628,0.00844363,0.10310166,0.0100192,-0.08767677,-0.063893,-0.01438011,0.00007058,-0.0046502,-0.03404367,-0.0278881,0.01116508,-0.03504107,-0.07679645,0.00696757,-0.02302682,-0.00257206,0.02906106,-0.05386969,0.02139943,-0.0110852,-0.01515833,-0.0221067,0.0015557,-0.0419566,-0.04094435,0.08887987,-0.02047466,0.00392859,0.01492919,0.04665869,0.04278563,0.00811806,0.03214395,-0.0409625,-0.06071668,0.06036634,0.03830547,-0.03984893,0.05780409,0.03937124,0.06859563,-0.05414333,0.04174566,0.02309147,0.0374724,-0.02247469,0.00085359,0.01693161,0.03133404,-0.04202142,-0.18917216,-0.03632506,-0.04259408,0.00794655,0.0396886,-0.00787421,0.0102524,-0.01114201,0.0113052,0.05560077,0.09336713,-0.05260349,0.03420478,0.04709633,-0.00728577,0.01884687,-0.08252055,-0.03093421,-0.04757598,0.06806786,0.00734537,0.00943811,-0.03264612,-0.10700452,0.02685131,-0.01176194,0.10559892,0.00394192,-0.00280379,0.00516651,0.08242638,0.00059502,-0.02686695,-0.00785675,0.03045381,0.04252492,-0.04606707,-0.03735978,-0.01892694,-0.01919078,-0.07774092,0.0056009,0.01268721,-0.09551794,0.00275807,0.00171025,-0.00787752,0.01153784,0.03822534,0.04507358,0.01615213,0.03138944,0.01646519,0.0372702,0.05811275,-0.00368829,-0.06981069,0.02197294,-0.00668423,0.03686941,-0.02948594,-0.03510417,0.05953278,-0.01708293,0.06500695,0.0112475,-0.02377828,-0.05150351,-0.02237138,0.00662391,0.00537886,0.07720253,0.03824956,0.03394774,-0.05566137,0.04005064,0.04882899,-0.01698591,0.0317775,0.02140808,-0.03017352,-0.09885594,0.02287795,0.0889332,0.05612955,0.01881293,0.02135622,0.00730249,-0.00573669,0.00440507,-0.01062689,0.01311071,-0.03045904,0.03845373,0.02449192,0.00102384,-0.25175473,0.03477433,-0.03306626,0.03095851,-0.01934854,-0.00165656,0.01766866,-0.00794461,0.05187781,-0.03952904,0.08494974,0.06377764,0.00244142,-0.05550653,0.03350304,-0.00399327,0.04036778,-0.02038403,0.060001,0.01357863,0.04660189,0.05352126,0.24094254,0.02089679,0.03550483,0.05008252,-0.03350652,0.0129458,-0.06845467,0.04286161,-0.01625509,-0.02230598,0.03885032,-0.02187071,-0.03884068,0.02757097,-0.00400251,0.00808444,-0.00084307,0.07189585,-0.07929159,0.01175815,-0.03256565,0.01378952,0.10665631,0.04342511,-0.01565886,-0.10287918,0.03076886,0.0403331,-0.05017882,-0.07713895,-0.09030289,-0.03090697,0.05552008,0.04191274,0.01734195,-0.01326047,-0.02597674,-0.02467647,0.055922,-0.02273691,0.02976986,0.02445433,0.00195232],"last_embed":{"hash":"1tut9az","tokens":175}}},"text":null,"length":0,"last_read":{"hash":"1tut9az","at":1753423502242},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{1}","lines":[237,240],"size":429,"outlinks":[{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":3}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11266676,-0.02928389,-0.01784008,0.01367327,-0.0495807,0.07614287,-0.02944623,0.0116177,0.02986735,-0.01230206,-0.00177589,-0.0025162,0.02533003,-0.02148563,-0.00014847,-0.00836367,0.02698617,-0.00725203,-0.06284023,-0.00446375,0.0799171,-0.01159875,-0.05161505,-0.05520749,0.06576426,-0.03211303,-0.05835147,-0.04238728,-0.04948746,-0.22779024,0.00969197,0.01505334,-0.00583105,-0.06267639,-0.0861683,-0.00978148,-0.03774462,0.11296898,-0.01755158,0.02548946,0.02158415,0.02704903,-0.03593261,0.00625333,-0.01195307,-0.06944005,-0.00641496,0.02024587,0.047623,0.00985118,-0.05626398,0.02885905,-0.00734809,0.01005509,0.05951762,0.03244317,0.0694269,0.11242265,-0.01072582,0.04620467,0.03885169,0.1018067,-0.19804463,0.04980306,0.02828644,0.00794705,-0.01391276,-0.00709629,-0.0193692,0.10384601,0.03333236,-0.01444854,-0.01925799,0.07300231,0.03664332,-0.0447614,-0.06058202,-0.09453047,-0.03600836,0.02603821,-0.02461405,-0.01894274,-0.02269166,-0.02279901,-0.00162009,0.01223965,0.02477073,0.02956437,0.025925,-0.08662959,0.04436576,0.01388424,-0.01202683,0.02528493,0.00621857,0.00532725,0.03226302,-0.02363956,-0.00623125,0.11390905,0.01673435,0.00520151,0.00111297,0.02465239,0.06073609,-0.04912138,-0.03072545,-0.05271373,-0.01144013,0.04079797,0.08901416,0.00917851,0.05019396,-0.02561147,-0.06426673,0.00854454,-0.00207219,0.00033482,-0.01294923,-0.00612711,-0.03012341,0.0190917,0.03167145,-0.00577654,-0.02065311,-0.02155207,0.01599842,0.07208041,0.00067845,0.02547453,0.05266305,0.00969862,-0.15121707,-0.04853255,-0.05011227,-0.0487936,-0.00011864,0.00235001,-0.01299652,0.0175796,-0.0182769,-0.10134237,0.03190626,-0.10125766,-0.01301796,0.05166032,0.02522389,0.00929735,0.03389345,0.01026725,-0.02617298,-0.02126528,0.00450329,-0.06359817,-0.01480921,-0.0328081,0.09442978,0.06491831,-0.03355705,0.01950928,-0.07054266,-0.01406229,-0.04531699,0.12363171,-0.00830673,-0.08493916,-0.01642009,0.05572048,-0.02017255,-0.09697707,-0.02758523,-0.02808345,-0.06031623,0.01003042,0.10847507,0.00089445,-0.10435605,-0.0396387,-0.0279395,0.01654884,-0.01325887,-0.04733049,-0.03654629,0.00759979,-0.03549342,-0.05315529,0.01753452,-0.00050168,0.0053153,0.02074845,-0.06385842,0.02085958,-0.05240442,0.01001498,-0.03498443,-0.01087848,-0.04537987,-0.04998249,0.10128805,-0.0074055,-0.02881988,-0.01470456,0.01333495,0.03064891,-0.01316458,0.02795349,-0.00824164,-0.06093174,0.06443843,0.00272376,0.00349532,0.01677044,0.05275165,0.0742429,-0.04570891,0.0430919,0.01580674,0.06501462,-0.04662053,-0.03546447,0.04226894,0.02086052,-0.06063538,-0.19462359,-0.00760958,-0.06667639,-0.01098596,0.0473106,0.00715502,0.04437305,-0.01149794,0.03122106,0.03992186,0.09190947,-0.06407718,0.03214525,0.05490767,0.00450403,0.01458555,-0.11769672,-0.00601827,-0.002073,0.08198775,0.0149735,0.02797094,-0.04618158,-0.08529305,0.03910602,-0.01667401,0.11884967,0.03082659,0.02095878,-0.03109973,0.06948344,-0.00261406,-0.01863376,0.00764609,0.05625872,0.01936826,-0.04619533,-0.02399784,-0.06085693,-0.03904442,-0.07655898,0.02342292,-0.00716202,-0.09817,-0.02359592,-0.00796482,0.00255564,0.04981848,0.00925011,0.05966743,0.08006521,0.02312009,0.04299016,0.04787173,0.04016892,0.00801413,-0.08217239,0.00546904,-0.00557185,0.05243217,-0.01438777,0.00630881,0.05267914,0.00337616,0.04818546,0.01598679,-0.02285757,-0.03680015,-0.02365781,0.0145583,0.02483449,0.0649698,0.03312626,0.06725375,0.00909471,0.0300385,0.03329146,-0.03287073,0.05661951,0.0368485,0.00188404,-0.09943952,0.0334622,0.074021,0.07547684,0.02237194,0.00634913,0.01873509,-0.0188307,0.01332789,0.01745419,0.0170762,-0.04268439,0.04297207,0.0094905,0.00757548,-0.25500578,0.00077863,-0.0270836,0.01540024,-0.0363485,-0.01814228,0.04148977,-0.01921435,0.02269559,-0.01013436,0.09366984,0.08624101,-0.00192225,-0.04432147,0.00704986,-0.03330512,-0.03233379,0.02284326,0.05834831,0.02830788,0.04680462,0.04091822,0.23600663,0.02447994,0.01993324,0.03628702,-0.01375216,0.00132434,-0.05081686,-0.00019016,-0.01265446,0.01484246,0.0388478,-0.01723656,-0.00191436,0.04440622,-0.01832038,-0.00304812,0.0047643,0.05030865,-0.03978426,0.01272162,-0.01549993,0.02396519,0.11939761,0.0104613,-0.01667123,-0.07898415,0.0411259,0.0276634,-0.05503987,-0.07429928,-0.07289549,-0.02280094,0.02321113,0.0290369,0.02827764,0.00866604,0.0098409,-0.01402543,0.0229802,0.00031469,0.03817226,0.02709929,0.00143679],"last_embed":{"hash":"1mu93yz","tokens":321}}},"text":null,"length":0,"last_read":{"hash":"1mu93yz","at":1753423502296},"key":"notes/saliu/Lottery Triples Strategy with Lotto Wheels.md#Lottery Triples Strategy with Lotto Wheels#[<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)#{13}","lines":[253,258],"size":657,"outlinks":[{"title":"Playing all-low or all-high lotto numbers leads to far greater chance to win the lottery big-time.","target":"https://saliu.com/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"The 12 and 18-number lotto wheels assure best chances for high lottery prizes, including da jackpot.","target":"https://saliu.com/HLINE.gif","line":5}],"class_name":"SmartBlock"},
