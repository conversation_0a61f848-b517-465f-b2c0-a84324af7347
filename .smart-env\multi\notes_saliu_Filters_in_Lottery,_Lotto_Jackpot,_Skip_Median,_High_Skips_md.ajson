
"smart_sources:notes/saliu/Filters in Lottery, <PERSON><PERSON>, <PERSON><PERSON>, High Skips.md": {"path":"notes/saliu/Filters in Lottery, Lotto Jackpot, <PERSON><PERSON> Median, High Skips.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10271916,-0.02544075,-0.02672846,-0.00305236,-0.00641221,0.06305688,0.0293291,0.00823331,0.04325286,0.01697376,-0.0002022,-0.02606179,0.06598695,-0.00851185,-0.00796258,-0.02981267,-0.01077455,-0.02634015,-0.07546051,-0.01102241,0.05847539,-0.00619874,-0.06031383,-0.10461167,0.04772951,-0.00989865,0.01163859,-0.05916285,-0.06995881,-0.23241954,0.02792727,0.00633172,0.01218114,-0.06401145,-0.12441446,-0.02209083,-0.01066874,0.09863328,-0.0241352,0.04457944,0.02253756,-0.00895893,0.00052838,-0.03037908,-0.03823641,-0.03368364,-0.01728546,-0.03279582,0.01899733,0.03001532,-0.07730062,-0.02020743,0.02338713,0.02225494,0.06932868,0.01219449,0.04400701,0.07988918,0.03085987,0.07472749,0.0142725,0.06538204,-0.19008563,0.04157835,-0.00668324,0.01236275,-0.01105813,-0.01364284,-0.01179777,0.06183491,0.01733401,0.07907881,-0.01981378,0.06634556,0.06969456,-0.03237168,-0.03998327,-0.0723092,-0.05013322,0.03317909,-0.03659255,-0.02160693,-0.04004975,-0.02539668,0.01179974,0.01392577,0.03361597,-0.00196985,0.0298269,-0.03334492,0.04132037,-0.02057271,-0.01139108,0.01752257,-0.00003941,0.039316,0.04837618,-0.04421527,0.02477455,0.09845732,0.02091768,0.03069645,0.00624487,0.00713229,0.06154367,-0.07224805,-0.02037576,-0.01916694,-0.00439896,0.03775807,0.03066156,0.00721439,0.05952641,-0.02834811,-0.04678129,0.02488163,0.03646815,0.01245479,0.03201972,-0.00182533,-0.05354153,0.01980928,0.00270864,-0.04260147,0.0326223,0.0543409,0.00398646,0.11756285,0.03104225,0.00367902,0.01817249,-0.02089919,-0.13417162,-0.03672954,-0.03066037,-0.00596993,-0.01132176,-0.00080248,-0.01122495,0.02099793,-0.04771555,-0.05410889,0.03186657,-0.0883922,-0.04476206,0.09611404,-0.01216536,0.02671194,0.01929446,-0.07226913,-0.00818089,0.00131116,-0.05443347,-0.04905416,-0.016418,-0.0107318,0.03296195,0.10618575,-0.00886447,-0.02138236,-0.04769696,-0.04549424,-0.02448566,0.08502247,-0.00143214,-0.04234958,-0.03924122,0.01247926,-0.05393718,-0.02556815,-0.00835195,-0.00965262,-0.06215926,0.02667539,0.07235248,0.00470441,-0.07780772,-0.02725229,-0.01830802,0.00438185,-0.01155182,-0.02666939,-0.05401307,-0.02857391,-0.04800279,-0.10833293,0.00288352,-0.0246445,0.00774366,0.05869351,-0.02203255,-0.03554156,-0.04748726,0.02800115,0.01309006,-0.02872381,-0.00665806,-0.0324407,0.05392357,-0.03712913,-0.01172203,-0.00326581,0.01447279,0.00836128,-0.06988917,0.06454436,-0.00785331,-0.05320856,0.10974416,0.01003135,-0.02585386,-0.02122466,0.04593851,0.0693038,0.01497081,0.05565217,0.02272269,0.01449312,0.01631606,0.01913047,0.00754993,-0.00064387,-0.05063192,-0.19800219,-0.04811331,-0.02650464,0.03380976,0.00805608,-0.03755481,0.00871796,-0.00150274,0.04275071,0.13073957,0.05406863,-0.05463011,-0.0393423,0.07061121,0.00635254,0.0116261,-0.11754826,-0.00382412,-0.0623056,0.06416286,0.02172955,0.03714176,-0.03337138,-0.04565467,0.03513841,-0.01591349,0.14587599,0.02300552,-0.00923417,-0.00554633,0.07933947,0.04130853,0.01640694,0.0202502,0.00710054,0.03596207,-0.04829649,-0.00555398,-0.07241984,-0.0375209,-0.07051577,0.0201682,-0.00681934,-0.11787249,-0.04246706,0.03165369,-0.03085604,0.02314172,-0.01893554,0.04859051,0.03222853,-0.03838649,0.07390251,0.01260087,0.07588948,-0.04113606,-0.09818137,-0.02223035,0.00001184,0.0523302,-0.01272451,-0.03140754,0.06297027,-0.02770836,0.04135149,0.02303018,0.00627695,0.00571101,-0.02228049,0.01918305,-0.01852472,0.07000279,0.00069646,0.02867439,-0.02437427,0.04756788,0.01326902,-0.03163586,0.03657311,-0.00987563,0.08569691,-0.04974592,0.03438339,0.05056324,0.05304629,-0.02749468,0.08792166,0.0353679,0.04486302,0.00405109,-0.02241916,-0.00097587,-0.0344809,0.02441217,0.03164045,0.01261858,-0.24634366,0.00351353,0.00989596,0.05153444,0.00859724,-0.0555001,0.01734002,-0.00641921,0.05727159,-0.00915505,0.05393157,0.04558287,0.05508154,-0.06117313,-0.02482058,-0.00704578,-0.06984708,-0.0125698,0.06292333,0.00035689,0.05827731,0.0138838,0.24468429,-0.02298255,-0.0067927,0.00248957,-0.00587397,-0.01119656,-0.04049936,0.02664447,0.00411072,0.01693269,0.10272183,0.00025439,-0.0291946,0.03903532,-0.02268815,0.06181616,-0.03146862,-0.00670942,-0.03934162,0.02170604,-0.06122993,-0.01142428,0.12463139,-0.00827004,-0.01076844,-0.09024308,0.08075802,0.07598849,-0.06467768,-0.03110327,-0.01393491,-0.00960424,-0.01445391,0.01364949,0.01592038,0.03720189,-0.01322123,0.03995124,0.03542054,0.02873647,0.05809868,0.03005629,0.0144094],"last_embed":{"hash":"7xndg8","tokens":458}}},"last_read":{"hash":"7xndg8","at":1753423462800},"class_name":"SmartSource","last_import":{"mtime":1753311232939,"size":20828,"at":1753423416052,"hash":"7xndg8"},"blocks":{"#---frontmatter---":[1,6],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips":[8,138],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#{1}":[10,13],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#<u>FFG Median, <i>Skips</i>, Lottery, Lotto Filtering, Probability, Odds, Jackpot</u>":[14,15],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#Playing Combinations around the FFG Median Can Improve the Odds of a Lotto Jackpot":[16,17],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_":[18,110],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{1}":[20,64],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{2}":[65,65],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{3}":[66,67],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{4}":[68,69],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{5}":[70,70],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{6}":[71,71],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{7}":[72,72],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{8}":[73,73],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{9}":[74,74],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{10}":[75,75],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{11}":[76,76],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{12}":[77,77],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{13}":[78,78],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{14}":[79,79],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{15}":[80,80],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{16}":[81,81],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{17}":[82,82],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{18}":[83,84],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{19}":[85,85],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{20}":[86,87],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{21}":[88,110],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>":[111,138],"#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>#{1}":[113,138]},"outlinks":[{"title":"Apply filters, filtering, median, pairs in lotto, lottery, gambling, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":20},{"title":"_**Dynamic versus Static in Lotto, Lottery Analysis**_","target":"https://saliu.com/bbs/messages/919.html","line":24},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lotto-decades-skips.gif","line":63},{"title":"_**Birthday Paradox**_","target":"https://saliu.com/birthday.html","line":65},{"title":"_**Classical Occupancy Problem**_","target":"https://saliu.com/monty-paradox.html#Paradoxes","line":65},{"title":"_**Comparison of lotto software programs**_","target":"https://saliu.com/bbs/messages/278.html","line":72},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":85},{"title":"Playing lottery skips below median increases tremendously the chance to win the lotto jackpot.","target":"https://saliu.com/ScreenImgs/lotto-skips.gif","line":88},{"title":"_**gambling strategy offsets losses in minimum-bet situations**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":100},{"title":"Lotto software creates the best lottery strategies to win the jackpot multiple times.","target":"https://saliu.com/ScreenImgs/mdi-lotto-software.gif","line":104},{"title":"_**Dynamic or Static Filters in Lottery, Lotto Analysis, Mathematics, Software**_","target":"https://saliu.com/bbs/messages/919.html","line":106},{"title":"_**Internet, State Lotteries: Lotto Drawings, Lottery Results, Winning Numbers**_","target":"https://saliu.com/bbs/messages/920.html","line":107},{"title":"\n    \n    ## <u>Resources in Lottery Software, Strategies, Lotto Systems</u>\n    \n    ","target":"https://saliu.com/content/lottery.html","line":109},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":115},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":117},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":119},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":121},{"title":"_**<u>Skip System</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":122},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":123},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":124},{"title":"_**Lotto Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":125},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":126},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":127},{"title":"_**A Practical Essay on Artificial Intelligence, AI Chatbots Regarding Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":128},{"title":"_**Lotto wheels**_","target":"https://saliu.com/lotto_wheels.html","line":129},{"title":"**software**","target":"https://saliu.com/infodown.html","line":131},{"title":"Improve lotto odds my a great margin using skip, frequency, pairing strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":133},{"title":"Forums","target":"https://forums.saliu.com/","line":135},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":135},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":135},{"title":"Contents","target":"https://saliu.com/content/index.html","line":135},{"title":"Home","target":"https://saliu.com/index.htm","line":135},{"title":"Software","target":"https://saliu.com/infodown.html","line":135},{"title":"Search","target":"https://saliu.com/Search.htm","line":135},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":135},{"title":"A study on filtering, median, pairs in lotto, lottery software, systems, strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":137}],"metadata":{"created":"2025-07-24T06:53:50 (UTC +08:00)","tags":["lotto","lottery","filters","filtering","odds","probability","median","jackpot","winning","strategy","strategies","software"],"source":"https://saliu.com/bbs/messages/923.html","author":null}},"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12313948,-0.06008352,-0.0392874,0.02026133,0.00782845,0.04577215,0.03562744,0.0182378,0.03938558,0.02231889,0.00608183,-0.01400407,0.0457748,-0.00499929,0.0092628,0.00091454,-0.00113533,-0.02458056,-0.05601053,0.00804581,0.07794289,-0.00912078,-0.07805316,-0.08497214,0.05854043,-0.03390921,0.00946521,-0.04500171,-0.06738207,-0.18306641,0.00499601,-0.01279167,-0.0027852,-0.0668866,-0.08157031,-0.05179259,-0.01361995,0.08782808,0.00205186,0.06179868,0.03464229,0.00557341,-0.03208493,-0.03309151,-0.03390176,-0.02122251,0.00196602,-0.03739363,0.03617555,0.03653619,-0.07211979,0.00400508,0.01938852,0.03365202,0.07752773,-0.0035519,0.04945093,0.06685802,0.02659208,0.04830592,0.00779338,0.01968175,-0.19430923,0.0592902,-0.02986308,0.00113548,0.01232409,-0.02249079,-0.03630642,0.05403419,0.06214886,0.0606036,-0.01527464,0.08403931,0.04858373,-0.03303647,-0.04482009,-0.08249076,-0.04942513,0.03129137,-0.03862278,-0.03878734,-0.04147688,-0.06319556,0.00509702,0.00910291,0.04380878,0.0365063,0.04034782,-0.01495747,0.0396348,-0.0235492,0.01325072,0.05776879,-0.02319579,0.05043641,0.03635743,-0.0230211,0.03413395,0.12285133,-0.00038306,0.0445396,-0.00251585,-0.00135497,0.07517606,-0.04596495,-0.01593036,-0.01019575,-0.01676105,0.05675133,0.03015065,0.01007787,0.07750422,-0.05576961,-0.02723186,0.01871116,0.04645709,0.01406925,0.03990813,-0.01275156,-0.08050478,0.0072683,0.01240708,-0.02401406,0.01863016,0.05684873,0.04950059,0.09742131,0.01061504,0.0314988,0.01630376,-0.02212873,-0.14052971,-0.02265403,-0.01827061,-0.00588088,0.01057116,-0.04631262,-0.01610402,-0.02093046,-0.04841607,-0.05211545,0.0020211,-0.09468461,-0.04575833,0.06531783,-0.00179447,0.01776201,0.02221425,-0.07517073,-0.04223442,0.00407265,-0.0211779,-0.05069773,-0.01109427,0.01537323,0.04495735,0.10793035,-0.01381078,-0.00672904,-0.01673076,-0.04956177,-0.04946497,0.09797886,-0.01284458,-0.08778635,-0.05498095,0.00148433,-0.04017304,-0.04084867,-0.03552467,-0.00339048,-0.05927987,0.03727926,0.10268585,0.01366055,-0.05135974,-0.03858438,-0.06899979,-0.02417381,-0.05218681,-0.0214579,-0.07230165,-0.01158653,-0.05345644,-0.09781143,0.0166166,-0.05103002,0.0285691,0.07216987,-0.03006186,-0.05966428,-0.04910947,0.01184888,0.01499463,-0.03360569,-0.01557427,-0.03279626,0.05929188,-0.04138353,-0.03647622,-0.02353967,-0.01860857,0.03481028,-0.06374875,0.03912189,-0.01450382,-0.04637579,0.09125981,0.01186341,-0.01104011,0.00919356,0.06997279,0.04500019,-0.00031892,0.02239664,0.0026077,0.00488728,0.02319022,-0.01627516,0.0100193,0.01090534,-0.06956067,-0.20369595,-0.00880329,-0.01449556,0.01373801,-0.00317261,-0.03493652,0.00449129,0.0115098,0.05486884,0.15107356,0.06650877,-0.05759116,-0.01452387,0.04341413,0.0214585,0.02860245,-0.08384499,-0.0144533,-0.01856837,0.05784566,0.03771833,0.02540117,-0.01929841,-0.06020879,0.04261965,-0.00513533,0.13685371,0.05485744,-0.01105979,-0.01296667,0.09057672,0.03029913,0.00800401,-0.01468572,-0.00942026,0.01761173,-0.06845339,0.02543234,-0.0610493,-0.05304329,-0.06804707,0.02241008,-0.01649756,-0.10505722,-0.01140841,0.02210251,-0.00358432,0.04159855,-0.03521118,0.02420356,0.04052369,-0.04680018,0.05470983,0.02537996,0.08740325,-0.02301298,-0.08857647,-0.0556124,-0.0188949,0.04049971,-0.00590514,-0.02064952,0.03578132,-0.04195607,0.0093207,0.01989607,0.01008113,-0.01302879,-0.04716017,0.00450416,-0.01198242,0.07007354,0.00908641,0.00295123,-0.0395846,0.03713272,0.0270209,-0.02458562,0.05562144,0.00327694,0.07829609,-0.04952214,0.05464854,0.05465484,0.07383477,0.0353098,0.05135868,0.02361311,0.03497795,0.01744254,0.00685328,0.01013098,-0.04385904,0.01527797,0.04874501,-0.00633281,-0.24658512,0.01786154,0.0194117,0.05614073,0.03463886,-0.02931479,0.00363911,-0.0095305,0.03238568,0.02083208,0.07410929,0.06887968,0.02368278,-0.02795785,-0.05088888,-0.00346602,-0.06845349,0.00149648,0.0655926,0.03823424,0.04004022,0.01074597,0.24204457,0.02265063,-0.01865719,-0.01398227,-0.01866631,-0.02842044,-0.05832942,0.03134051,0.02418273,0.02622914,0.0990717,0.00511718,-0.03106765,0.04377997,-0.02556613,0.06333783,-0.02521374,0.0020509,-0.06820618,0.04550287,-0.04142502,-0.00894954,0.09690082,-0.00015771,-0.04468044,-0.08580176,0.06062996,0.05162035,-0.05230632,-0.05775473,-0.03241849,0.01346407,-0.01652978,0.03479536,0.02531643,0.01020523,0.01176603,0.03672406,0.03069448,0.01589583,0.04470635,0.02031367,0.03179004],"last_embed":{"hash":"z3yjry","tokens":102}}},"text":null,"length":0,"last_read":{"hash":"z3yjry","at":1753423461006},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#---frontmatter---","lines":[1,6],"size":216,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07081863,-0.02278254,-0.01713115,-0.01023121,-0.00486966,0.07469947,0.04035051,0.00296361,0.04953096,0.01862326,0.00629991,-0.0248808,0.04815709,-0.00198051,-0.0254416,-0.02747602,-0.01161627,-0.02780974,-0.07937998,-0.0055938,0.0398928,-0.02564158,-0.04894729,-0.10007575,0.04828911,0.00302532,0.01527543,-0.06567325,-0.07660898,-0.23428535,0.03215784,0.01231275,0.01951115,-0.05801129,-0.11453164,-0.02857495,-0.02160061,0.09538981,-0.01848471,0.03231744,0.03098685,-0.01724824,0.00517343,-0.0224351,-0.03259253,-0.03865002,-0.02136086,-0.02425883,0.01773301,0.03240837,-0.05896148,-0.02921984,0.03182654,0.02623324,0.07161415,0.00344533,0.05600123,0.07792597,0.04825525,0.07010646,0.01574376,0.06914271,-0.19629796,0.03016779,-0.00425812,-0.00246616,-0.02473584,-0.00562482,-0.01110516,0.05534434,0.00485628,0.0815553,-0.02195449,0.05183105,0.07134095,-0.03416313,-0.03586341,-0.07141384,-0.05659406,0.03910257,-0.0339194,-0.0139548,-0.03721367,-0.01833591,0.00519462,0.01620089,0.03264073,-0.00133367,0.03558702,-0.02113905,0.04943997,-0.01400705,-0.02962874,0.01746742,0.00581659,0.04059651,0.06013797,-0.04475644,0.03233541,0.09807114,0.03042317,0.02361009,0.00412147,0.0086973,0.05605165,-0.07565229,-0.01482798,-0.00960201,-0.00795766,0.03123758,0.03425648,0.01112316,0.05687739,-0.0287899,-0.0423388,0.02497242,0.03288184,0.01349395,0.02371821,-0.00594012,-0.04294672,0.02471374,-0.01301593,-0.03883388,0.04132722,0.05536907,-0.00260079,0.11859904,0.04420216,0.00941858,0.0101454,-0.02051647,-0.13063033,-0.03656181,-0.03333982,-0.00367446,-0.00606116,0.01181747,-0.00716063,0.03376679,-0.03874029,-0.05528404,0.02706251,-0.0932702,-0.03811124,0.11050586,-0.02412998,0.02927676,0.01480328,-0.07031501,0.00104516,0.01411947,-0.06904446,-0.04715199,-0.01326996,-0.01528013,0.01165168,0.09583439,-0.01025206,-0.02512139,-0.05754805,-0.05458589,-0.00700482,0.08043993,-0.00257578,-0.03065667,-0.03884999,0.00894556,-0.0568417,-0.03393129,-0.00133832,-0.01748062,-0.06034529,0.01819152,0.05758509,0.00086008,-0.07435026,-0.02930059,0.01188982,0.00812677,0.00136064,-0.01864188,-0.0446993,-0.02191579,-0.04661971,-0.09415691,0.00076773,-0.01404359,0.0110789,0.05361043,-0.02009896,-0.03449096,-0.05928396,0.03522165,0.00932283,-0.03820809,-0.00411007,-0.03686561,0.04499489,-0.04504829,-0.00737021,0.00637012,0.02001072,-0.00795861,-0.05660691,0.0639929,-0.02337595,-0.05092721,0.11752824,0.00580548,-0.03614854,-0.0397029,0.04057189,0.06745047,0.0054775,0.05548437,0.03200408,0.01070749,0.02013223,0.02496964,0.0166433,-0.00619258,-0.04386231,-0.19883285,-0.0543247,-0.02159842,0.04295153,0.01904765,-0.03887825,0.00818961,0.00059857,0.05018448,0.12669718,0.0545868,-0.03807145,-0.0458217,0.08186829,-0.00161031,0.00672648,-0.11463615,-0.01461845,-0.06772264,0.06122864,0.01210743,0.04061327,-0.04248394,-0.04343989,0.04022504,-0.01129663,0.1478636,0.00488589,-0.00818626,-0.01532038,0.07051402,0.046825,0.00171968,0.03273093,0.01754844,0.03532505,-0.03720498,-0.02004533,-0.08175392,-0.03227739,-0.06896938,0.01743192,-0.00134728,-0.11335739,-0.04702027,0.02146513,-0.0365924,0.02190557,-0.02514002,0.06215497,0.03357435,-0.03059112,0.08437354,0.01014364,0.07352425,-0.04776561,-0.09860397,-0.0104945,0.01169747,0.06404757,-0.00475526,-0.04358408,0.07796941,-0.02152843,0.05001841,0.01494348,0.00245209,-0.00022674,-0.01158821,0.02244972,-0.01607167,0.0528639,-0.00281019,0.03071153,-0.02114315,0.05213568,0.02340884,-0.03233027,0.04095569,-0.01109529,0.09559576,-0.04782114,0.02427275,0.04979279,0.03635644,-0.03456378,0.08664005,0.05127889,0.03724207,0.00467472,-0.02409967,-0.00044691,-0.03184637,0.02135342,0.00871368,0.01618698,-0.24912895,-0.00147449,0.00341991,0.05013209,0.00164426,-0.05615386,0.02203009,-0.00132234,0.05492686,-0.00891024,0.05083338,0.04665505,0.06208704,-0.08482801,-0.01351218,-0.01221855,-0.07427996,-0.01999219,0.06092596,-0.00401565,0.06417,0.0271523,0.23412873,-0.03197687,-0.0049393,0.00030118,-0.00481238,-0.01396499,-0.03205196,0.01758186,-0.00577402,0.00836534,0.10373656,-0.00432867,-0.0196771,0.03317067,-0.02724198,0.04815016,-0.03285512,-0.02408837,-0.02334419,0.0134468,-0.0686589,-0.02087005,0.12937188,-0.00920245,-0.01053098,-0.09297516,0.08223636,0.08255822,-0.05570705,-0.03151362,-0.00081722,-0.03502136,-0.01584056,0.00307683,0.0078226,0.03784351,-0.00930985,0.04525688,0.03002924,0.03001877,0.05994863,0.01659833,0.01463109],"last_embed":{"hash":"566p0o","tokens":486}}},"text":null,"length":0,"last_read":{"hash":"566p0o","at":1753423461044},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips","lines":[8,138],"size":20590,"outlinks":[{"title":"Apply filters, filtering, median, pairs in lotto, lottery, gambling, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":13},{"title":"_**Dynamic versus Static in Lotto, Lottery Analysis**_","target":"https://saliu.com/bbs/messages/919.html","line":17},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lotto-decades-skips.gif","line":56},{"title":"_**Birthday Paradox**_","target":"https://saliu.com/birthday.html","line":58},{"title":"_**Classical Occupancy Problem**_","target":"https://saliu.com/monty-paradox.html#Paradoxes","line":58},{"title":"_**Comparison of lotto software programs**_","target":"https://saliu.com/bbs/messages/278.html","line":65},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":78},{"title":"Playing lottery skips below median increases tremendously the chance to win the lotto jackpot.","target":"https://saliu.com/ScreenImgs/lotto-skips.gif","line":81},{"title":"_**gambling strategy offsets losses in minimum-bet situations**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":93},{"title":"Lotto software creates the best lottery strategies to win the jackpot multiple times.","target":"https://saliu.com/ScreenImgs/mdi-lotto-software.gif","line":97},{"title":"_**Dynamic or Static Filters in Lottery, Lotto Analysis, Mathematics, Software**_","target":"https://saliu.com/bbs/messages/919.html","line":99},{"title":"_**Internet, State Lotteries: Lotto Drawings, Lottery Results, Winning Numbers**_","target":"https://saliu.com/bbs/messages/920.html","line":100},{"title":"\n    \n    ## <u>Resources in Lottery Software, Strategies, Lotto Systems</u>\n    \n    ","target":"https://saliu.com/content/lottery.html","line":102},{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":108},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":110},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":112},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":114},{"title":"_**<u>Skip System</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":115},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":116},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":117},{"title":"_**Lotto Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":118},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":119},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":120},{"title":"_**A Practical Essay on Artificial Intelligence, AI Chatbots Regarding Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":121},{"title":"_**Lotto wheels**_","target":"https://saliu.com/lotto_wheels.html","line":122},{"title":"**software**","target":"https://saliu.com/infodown.html","line":124},{"title":"Improve lotto odds my a great margin using skip, frequency, pairing strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":126},{"title":"Forums","target":"https://forums.saliu.com/","line":128},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":128},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":128},{"title":"Contents","target":"https://saliu.com/content/index.html","line":128},{"title":"Home","target":"https://saliu.com/index.htm","line":128},{"title":"Software","target":"https://saliu.com/infodown.html","line":128},{"title":"Search","target":"https://saliu.com/Search.htm","line":128},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":128},{"title":"A study on filtering, median, pairs in lotto, lottery software, systems, strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":130}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10730065,-0.02495606,-0.02490781,-0.01512472,-0.02727012,0.08120435,0.03124845,-0.0016823,0.05881251,0.01040945,0.00065165,-0.02434092,0.0557967,-0.00109692,-0.01677926,-0.02946487,-0.0101647,-0.04064464,-0.0753675,0.0186646,0.04755554,-0.02296136,-0.04547433,-0.10421632,0.04610083,-0.01062187,0.0011918,-0.05841738,-0.07647554,-0.24970837,0.03512395,0.01712833,0.00426449,-0.06740115,-0.11015795,-0.03482318,-0.01371455,0.08825165,-0.02104622,0.04228153,0.01871862,-0.00958109,0.01100045,-0.04479313,-0.02867776,-0.03668946,-0.01405411,-0.01206658,0.03941415,0.03310826,-0.06457716,-0.01243769,0.02282776,0.0293857,0.05574039,0.00804072,0.04675771,0.08208321,0.02034857,0.0567963,0.00958801,0.04831858,-0.18189979,0.0189361,-0.00667405,-0.01073452,-0.01971637,-0.01873987,-0.03245362,0.06098545,0.0223668,0.06688099,-0.01318887,0.05758237,0.04799419,-0.03814192,-0.06039023,-0.07429897,-0.03963352,0.02264664,-0.04594867,-0.04105432,-0.02545307,-0.0113627,0.00691453,0.02585703,0.02437918,0.0250516,0.03392604,-0.03352224,0.05381965,-0.00128566,-0.02722955,0.02050269,0.01485959,0.04922232,0.04548476,-0.04044211,0.02297601,0.10614793,0.02804161,0.0229135,0.00197478,0.00768025,0.0461269,-0.06806996,-0.02553093,-0.01911317,-0.00300208,0.03010754,0.04642889,-0.00338123,0.06710726,-0.0418133,-0.04684304,0.02493259,0.03680069,0.00216422,0.03427444,-0.01131984,-0.04398894,0.03060141,-0.01059017,-0.04549022,0.00751825,0.0503555,0.0242657,0.1133806,0.02632561,0.01694827,0.01102526,-0.01007366,-0.14255664,-0.04631933,-0.03698508,-0.02217049,-0.00925506,0.01738295,-0.00170285,0.01863226,-0.03539351,-0.05386898,0.04583848,-0.08691783,-0.03468443,0.10936726,-0.00464994,0.02774304,0.00789045,-0.05297286,0.00210617,0.00321734,-0.05843112,-0.04514844,-0.01776719,-0.00283225,0.02368073,0.09970196,-0.02155324,-0.00587775,-0.03421499,-0.04681799,-0.01768798,0.09457662,-0.00942635,-0.04348337,-0.03388293,0.02062188,-0.05501989,-0.028339,-0.00700231,-0.00358847,-0.04002051,0.01158054,0.0714253,-0.01465456,-0.09991369,-0.03698872,-0.0153843,0.01327603,-0.01257372,-0.01154737,-0.0587834,-0.01835216,-0.0377163,-0.10115178,0.03055323,-0.01484794,0.02271285,0.05517129,-0.02671073,-0.02220338,-0.05804618,0.04710607,0.01463103,-0.03637632,0.00097057,-0.03144584,0.06508049,-0.03202014,-0.01598643,0.00339218,0.0118362,0.02194201,-0.05323531,0.05402044,-0.03532949,-0.0443266,0.11435165,0.01753518,-0.01958796,-0.01659902,0.02854464,0.07780556,-0.00202172,0.04171673,0.0286291,0.00297992,0.00710927,0.02043465,0.00626073,-0.00001609,-0.0332011,-0.20069918,-0.0368828,-0.01981667,0.04578504,0.04214067,-0.02862959,-0.0164037,-0.01106101,0.04603326,0.10859883,0.07729817,-0.04109976,-0.03323255,0.06930313,-0.00437212,0.00898408,-0.11142612,0.00586034,-0.0582259,0.0735318,0.03144884,0.01160293,-0.03946188,-0.04245382,0.03706509,-0.01685484,0.14171977,0.01666557,0.00088235,-0.00092745,0.07785115,0.03786538,0.0016789,0.02055321,0.02056394,0.02583244,-0.05163119,-0.02330332,-0.07416767,-0.0277169,-0.06697163,0.0114034,-0.00376257,-0.12098562,-0.0218429,0.01265545,-0.04641151,0.04756154,-0.01279631,0.04453639,0.06310035,-0.03641164,0.06701088,0.00243484,0.08354968,-0.04523387,-0.10610966,-0.0203904,-0.00305885,0.05639879,-0.00947183,-0.02543654,0.07028296,-0.02253816,0.03316411,0.01972003,0.01812444,0.00837072,-0.00917539,0.0120337,-0.01706099,0.04956119,0.01109307,0.03900315,-0.0255116,0.04585315,0.05561294,-0.03135916,0.05606641,-0.01317605,0.08028915,-0.05682571,0.02893919,0.06430934,0.03802432,-0.03091601,0.0805672,0.04389185,0.0360727,0.00697096,-0.01631285,0.01257268,-0.0227976,0.0423579,-0.00416015,0.00804056,-0.25773665,0.00697102,-0.01741007,0.04894903,-0.00693399,-0.04319945,0.01274452,0.00069743,0.04323084,-0.01004087,0.07117786,0.05362148,0.0643742,-0.07782304,-0.00648235,-0.01942299,-0.07052658,-0.02577715,0.06168912,0.00224385,0.06976771,0.02468553,0.23045275,-0.02060481,-0.01653718,0.00640435,0.0020645,-0.00976342,-0.04029233,0.02149747,0.00162895,0.01465024,0.09024746,-0.01596985,-0.01692263,0.03736272,-0.02504013,0.05591808,-0.03256297,-0.00207522,-0.03644975,-0.0085099,-0.05259253,-0.01354246,0.13113318,0.0162259,-0.02484699,-0.09033201,0.06995041,0.0693693,-0.06717224,-0.05085654,0.00843262,-0.02672592,-0.00120571,0.04043138,0.01352897,0.02023032,-0.02037638,0.03005289,0.04050193,0.02696865,0.06911832,0.00777385,0.01049219],"last_embed":{"hash":"1qvgk30","tokens":496}}},"text":null,"length":0,"last_read":{"hash":"1qvgk30","at":1753423461233},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_","lines":[18,110],"size":16927,"outlinks":[{"title":"Apply filters, filtering, median, pairs in lotto, lottery, gambling, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":3},{"title":"_**Dynamic versus Static in Lotto, Lottery Analysis**_","target":"https://saliu.com/bbs/messages/919.html","line":7},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lotto-decades-skips.gif","line":46},{"title":"_**Birthday Paradox**_","target":"https://saliu.com/birthday.html","line":48},{"title":"_**Classical Occupancy Problem**_","target":"https://saliu.com/monty-paradox.html#Paradoxes","line":48},{"title":"_**Comparison of lotto software programs**_","target":"https://saliu.com/bbs/messages/278.html","line":55},{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":68},{"title":"Playing lottery skips below median increases tremendously the chance to win the lotto jackpot.","target":"https://saliu.com/ScreenImgs/lotto-skips.gif","line":71},{"title":"_**gambling strategy offsets losses in minimum-bet situations**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":83},{"title":"Lotto software creates the best lottery strategies to win the jackpot multiple times.","target":"https://saliu.com/ScreenImgs/mdi-lotto-software.gif","line":87},{"title":"_**Dynamic or Static Filters in Lottery, Lotto Analysis, Mathematics, Software**_","target":"https://saliu.com/bbs/messages/919.html","line":89},{"title":"_**Internet, State Lotteries: Lotto Drawings, Lottery Results, Winning Numbers**_","target":"https://saliu.com/bbs/messages/920.html","line":90}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10500757,-0.0241143,-0.02848923,-0.0170915,-0.0289781,0.08303534,0.0279278,-0.00084884,0.06092844,0.01019711,0.00012736,-0.02759409,0.05409866,-0.00082838,-0.0152224,-0.02950129,-0.01177282,-0.03894898,-0.07592964,0.0184062,0.04475206,-0.02518505,-0.04254659,-0.10295211,0.04497481,-0.00988755,0.00139114,-0.05941103,-0.07409561,-0.25093952,0.03290788,0.01612136,0.00708017,-0.06418575,-0.10914173,-0.03248182,-0.01399825,0.08646444,-0.02156072,0.04067124,0.01591431,-0.00942232,0.01817217,-0.04757881,-0.02998136,-0.03478602,-0.01311492,-0.00797655,0.04228199,0.0357136,-0.06334236,-0.01413518,0.02091015,0.03154585,0.05547082,0.01034978,0.0477614,0.0838605,0.01678695,0.05875732,0.00859744,0.04697631,-0.18364649,0.01893839,-0.00519663,-0.01260512,-0.02128388,-0.01932648,-0.03602611,0.05745193,0.01833265,0.06757692,-0.01092226,0.05503586,0.0473257,-0.03773391,-0.0626005,-0.07342388,-0.03864065,0.02090756,-0.05119795,-0.04160166,-0.02324954,-0.00837335,0.00678373,0.02837634,0.02228779,0.02876691,0.03488864,-0.03510442,0.05290325,0.00232964,-0.0264818,0.02097999,0.01423864,0.04701169,0.04195423,-0.03896781,0.02259449,0.10548519,0.02804545,0.02354016,-0.00071898,0.00721853,0.04780114,-0.06440376,-0.02727203,-0.019329,-0.00256829,0.02963483,0.0486666,-0.00519326,0.06453314,-0.04682942,-0.0457615,0.02485018,0.03770078,-0.00081232,0.03494157,-0.01146785,-0.04440106,0.02872636,-0.01047249,-0.04482712,0.0052014,0.04881595,0.02422294,0.11302573,0.0232754,0.01893415,0.01101965,-0.00789944,-0.14443476,-0.04775438,-0.03821127,-0.02401043,-0.0092441,0.01493633,-0.00544396,0.01894655,-0.03697322,-0.05126866,0.04792454,-0.08574423,-0.03613518,0.11068433,-0.0004611,0.02611179,0.00851279,-0.05151557,0.0031123,0.00396452,-0.05464169,-0.04568117,-0.01575257,-0.00052834,0.0221226,0.09640925,-0.02304542,-0.00250954,-0.03369327,-0.04785778,-0.01703936,0.09621888,-0.00853103,-0.04199835,-0.03034728,0.02450979,-0.05549983,-0.0268247,-0.00406197,-0.000913,-0.03577827,0.01065428,0.07307916,-0.01569385,-0.10259195,-0.03823695,-0.01644797,0.01605848,-0.0119023,-0.01238697,-0.05792791,-0.01684759,-0.03737319,-0.09867049,0.03487558,-0.01388892,0.02434446,0.05400385,-0.02701429,-0.01845009,-0.0600203,0.04879568,0.01319037,-0.0353706,0.00190008,-0.03549572,0.06382785,-0.0291767,-0.01935834,0.00481949,0.0102908,0.02048264,-0.05442625,0.0522914,-0.03736823,-0.04033922,0.11324255,0.01882954,-0.01914869,-0.01412005,0.02567367,0.0775803,-0.00181068,0.04362699,0.03147665,0.00194643,0.010327,0.02458267,0.00568152,-0.0013814,-0.03217384,-0.19991203,-0.03286165,-0.02195035,0.04450292,0.04459847,-0.02980834,-0.02103254,-0.01252347,0.04691824,0.10507412,0.07795482,-0.03754902,-0.02910515,0.07007549,-0.00816899,0.00795855,-0.10756824,0.00737814,-0.0604624,0.0755194,0.02885128,0.00786044,-0.03805317,-0.04357124,0.03771379,-0.01708359,0.14063111,0.01416835,0.00046233,0.00194355,0.08113989,0.03952845,-0.00008128,0.01728806,0.02302377,0.02566517,-0.05149164,-0.02595851,-0.07318304,-0.02566835,-0.06632988,0.01144143,0.00077055,-0.12160638,-0.02186288,0.01276298,-0.04820189,0.04392526,-0.0120908,0.04183415,0.06834488,-0.03480128,0.06806988,-0.00040129,0.08319138,-0.04798241,-0.10648861,-0.01730272,-0.00045614,0.05337009,-0.01148851,-0.0253479,0.07216118,-0.02434058,0.03036525,0.01845424,0.01889653,0.0049769,-0.00866962,0.01188745,-0.01713661,0.04878561,0.00876052,0.04105613,-0.0256999,0.04010136,0.05761035,-0.03599432,0.0547684,-0.01448906,0.0782566,-0.06459171,0.03022134,0.06489803,0.03562232,-0.02669123,0.08095884,0.04387869,0.03516216,0.00684686,-0.01529527,0.00971376,-0.021562,0.04500824,-0.0053576,0.00860351,-0.25889546,0.00973821,-0.02282554,0.05146269,-0.00584776,-0.04249042,0.01571968,0.00073615,0.04249002,-0.01217399,0.07228696,0.05230414,0.06664654,-0.07778457,0.00059661,-0.01832296,-0.07084538,-0.03075788,0.06094522,0.00118591,0.07129385,0.02640884,0.22891678,-0.02218644,-0.01445175,0.00917819,0.00417543,-0.01412735,-0.03926371,0.0270134,0.00225142,0.01410789,0.09010282,-0.01607626,-0.01895176,0.03913984,-0.02614033,0.05650125,-0.03689498,-0.00344112,-0.03951224,-0.01216561,-0.04952073,-0.01330791,0.13280241,0.02006993,-0.02349041,-0.08992628,0.06726426,0.06697903,-0.0661112,-0.05103255,0.00850922,-0.02735968,0.00066345,0.04469296,0.01317665,0.01750323,-0.02113663,0.02983656,0.04377976,0.02685696,0.06796056,0.00469552,0.01206188],"last_embed":{"hash":"1orbb6z","tokens":486}}},"text":null,"length":0,"last_read":{"hash":"1orbb6z","at":1753423461404},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{1}","lines":[20,64],"size":6046,"outlinks":[{"title":"Apply filters, filtering, median, pairs in lotto, lottery, gambling, software.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"_**Dynamic versus Static in Lotto, Lottery Analysis**_","target":"https://saliu.com/bbs/messages/919.html","line":5},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/lotto-decades-skips.gif","line":44}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12284207,-0.05214794,-0.04840567,0.02312544,-0.07866446,0.05189031,0.06120374,0.03179361,0.04731775,0.03355793,0.04901621,-0.03551793,0.04611593,0.01295586,0.01310915,0.02903651,-0.04561197,-0.02652477,-0.08194536,-0.00797331,0.06464026,-0.03433138,-0.05614363,-0.10788587,0.04801379,0.02575598,0.03208729,-0.06747442,-0.04516681,-0.24073537,-0.00133847,0.00593359,-0.01157619,-0.08634775,-0.07026456,-0.03479246,0.0028737,0.08752514,-0.01020449,0.03817416,0.01938308,0.01790819,-0.02922614,-0.06897937,-0.03214,-0.01806081,-0.01761105,-0.01883218,0.03453002,0.03430826,-0.06046504,-0.01563286,0.054275,0.01461875,0.02932189,0.01397906,0.04311322,0.06442167,0.01167371,0.04337112,-0.00246856,0.03113043,-0.13805534,0.04749002,0.00666312,0.01093598,-0.02768737,-0.03158334,-0.02920273,0.08401918,0.00080446,0.0438907,-0.02427779,0.07949056,0.05382102,-0.03500346,-0.02788298,-0.06365526,-0.04740031,0.04947319,-0.05450561,-0.01536953,-0.04109718,-0.01742252,-0.00671763,0.01305693,0.01656893,0.00224224,-0.00252342,-0.08184851,0.02467146,0.00100704,-0.0096459,0.02471011,-0.01736811,0.06293449,0.07615505,-0.0376972,0.03307072,0.11255442,0.0269014,0.01232973,-0.00659137,0.04387648,0.04548683,-0.02522302,-0.07544951,-0.04883832,-0.02477351,0.03336151,0.03832966,-0.01330384,0.08859187,-0.03269157,-0.05594298,0.05220046,0.04497004,-0.03243648,0.01527535,-0.00730478,-0.04130595,0.02203808,0.00306077,-0.0382814,-0.01026181,0.02764167,0.0285919,0.1066227,0.02093708,0.01023038,0.05750864,-0.00179893,-0.0971577,-0.05357872,-0.043941,0.008741,0.01252387,-0.03212663,-0.01668688,-0.02664548,-0.00322706,-0.05379117,0.04915896,-0.06038735,-0.01530445,0.05111028,-0.0127501,0.03787466,0.03287,-0.08167086,-0.00115386,0.00564719,-0.01485087,-0.05791288,-0.0169085,-0.00495451,0.05280353,0.11058421,-0.02305092,0.03486789,-0.01306432,-0.0503712,-0.03549716,0.15285882,-0.0381836,-0.0440589,-0.03902483,-0.00271375,-0.04504661,-0.02726713,-0.01063026,0.04884919,-0.06146608,0.03491081,0.12228772,-0.00160791,-0.12889245,-0.03352519,-0.02309483,-0.00462336,-0.0294455,0.00099499,-0.06576554,-0.03666019,-0.02194812,-0.10700239,0.04979952,-0.03869911,0.03884648,0.07575637,-0.00835395,0.01620745,-0.06410484,0.03461821,-0.01199562,-0.01340565,-0.02684378,-0.04159248,0.04793832,-0.0026562,-0.04812641,-0.02211051,-0.00980654,0.03734858,-0.05884399,0.04932951,0.01828138,-0.04068106,0.10964485,0.01335966,-0.04269835,-0.04186668,-0.01049013,0.04304205,0.04264488,0.05389573,0.01577223,0.02900606,0.03256807,-0.01009362,0.04646496,-0.01340794,-0.01636486,-0.17671378,-0.03192585,-0.03237063,0.02410604,0.04047544,-0.02940579,0.01237192,0.01109366,0.02005214,0.10999724,0.05180726,-0.06426358,-0.01347288,0.06263105,0.01459101,0.03970037,-0.12274037,0.01137426,-0.02867664,0.08084112,0.01119467,0.04649461,-0.05571562,-0.03931057,0.06245364,-0.0318173,0.14360353,0.06216974,-0.02059523,-0.0026019,0.0797813,0.02652625,-0.00203975,0.00256538,0.01958281,0.04151251,-0.06032345,-0.01361179,-0.03741611,-0.06676767,-0.05918587,0.05536142,-0.03883739,-0.05682703,-0.03108743,-0.00135966,-0.0178815,0.05841722,0.02872835,0.0772481,0.06298242,-0.02002457,0.02589753,0.01864981,0.05739836,-0.02470996,-0.09759679,-0.00081223,-0.01781598,0.05248703,0.00924418,0.00265173,0.04627929,-0.03220385,0.02685692,0.03378712,0.00695553,-0.02626373,-0.01852819,-0.00833663,-0.00160773,0.08595882,0.0241184,-0.01271657,-0.03661485,0.0204251,0.04180077,-0.0228379,0.06987092,0.00228873,0.07900504,-0.08498366,-0.00024218,0.0360122,0.03269427,-0.01127645,0.03531646,0.02001527,-0.01517728,-0.02340338,-0.00851783,0.01958778,-0.04349287,0.03259867,0.01143756,0.01957294,-0.24871734,0.0320714,-0.00956249,0.06848998,0.030128,-0.02955062,-0.00093452,-0.03610526,0.04787491,-0.0356109,0.07444766,0.0383592,0.03770689,-0.05261569,-0.00503963,0.00331051,-0.04733659,-0.02454936,0.02788616,-0.00645126,0.0689915,0.05467889,0.22034638,-0.02138988,0.01364127,0.01222395,-0.00732571,0.00229932,-0.05208289,-0.00030281,-0.02077689,-0.0095126,0.06745914,-0.0439446,-0.01671999,0.05784767,-0.02224424,0.02981393,-0.01595658,-0.02056192,-0.06011567,-0.00851259,-0.05620563,-0.01387962,0.11548894,0.00937418,-0.02385581,-0.05715711,0.0561761,0.08140401,-0.06240508,-0.03591427,-0.03170975,-0.04266206,-0.02913407,0.0274704,0.01363173,0.00949586,-0.02442312,0.01228645,0.02583608,0.04520009,0.05268408,0.02474638,0.0306927],"last_embed":{"hash":"21u5jb","tokens":162}}},"text":null,"length":0,"last_read":{"hash":"21u5jb","at":1753423461576},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{2}","lines":[65,65],"size":380,"outlinks":[{"title":"_**Birthday Paradox**_","target":"https://saliu.com/birthday.html","line":1},{"title":"_**Classical Occupancy Problem**_","target":"https://saliu.com/monty-paradox.html#Paradoxes","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07272311,-0.04850965,-0.01886734,-0.01580527,-0.06825829,0.08543924,0.07923055,-0.01525092,0.06837966,0.01745454,0.03555914,-0.04298988,0.05635825,-0.02365393,-0.01011863,0.03601914,-0.05555438,-0.03496084,-0.06699654,0.02611965,0.07978895,-0.04467751,-0.05182545,-0.10997868,0.04961201,0.01474733,-0.00526772,-0.04847584,-0.05557122,-0.22698742,0.05880387,0.01529261,-0.05452459,-0.08784146,-0.08150139,-0.04365923,-0.005047,0.0648699,0.00370325,0.05463057,0.01478858,-0.00846412,0.00547562,-0.03391034,-0.00996893,-0.03074247,-0.00462102,-0.03790736,0.0432757,0.03097473,-0.02373179,0.0326192,0.05149412,-0.01037885,0.05239068,0.00368956,0.03130557,0.06196758,-0.00481836,0.06353045,-0.01920179,0.04405946,-0.1632017,0.04821301,0.045962,0.00259838,-0.03975429,-0.0021272,-0.02277821,0.06121758,0.0460612,0.03504347,0.00744409,0.09459537,0.04423193,-0.05191534,-0.02416275,-0.07576191,-0.02693167,0.03264296,-0.08885089,-0.00803338,-0.01805768,-0.00920107,-0.01514735,0.00799997,0.01991547,0.00191105,0.02701837,-0.06204369,0.09340183,0.06177269,-0.0306225,0.0440122,0.02317891,0.08940937,0.03941514,-0.03283306,0.05083778,0.10493473,0.02807288,-0.03268012,-0.02974942,0.00160546,0.03320275,-0.03665,-0.07230856,-0.05502871,0.00197156,0.00240739,0.06084978,-0.00603649,0.09052707,-0.03699189,-0.06769706,0.00198579,-0.00862189,-0.0004867,0.00372383,-0.02803158,-0.02371836,-0.00158155,0.0391444,-0.01299093,-0.00903896,0.02486538,0.02934394,0.09983321,0.00345133,0.03898279,0.07076719,-0.04678889,-0.12213996,-0.02499571,0.00222949,-0.00354464,-0.01774894,0.0485644,-0.02589565,0.00120729,0.00792716,-0.06946491,0.01025135,-0.07526757,-0.01459146,0.07589275,-0.01999976,-0.00204464,0.02936706,-0.0857602,-0.01881792,-0.02513513,-0.02583312,-0.04807676,0.01245309,-0.01564583,0.03213161,0.07738984,-0.03994581,0.01637123,-0.06956778,-0.06148977,-0.00188957,0.09113622,-0.01038675,-0.01204477,-0.01820571,-0.00347223,-0.00298324,-0.02232587,-0.01827324,0.00225856,-0.05866205,0.03479004,0.09643247,0.01918787,-0.10225952,-0.05522234,-0.0069123,-0.01320733,-0.02413411,-0.02682287,-0.05344793,-0.02385973,-0.05693298,-0.06196876,0.01714969,-0.0276134,-0.01559057,0.05749148,-0.04346024,-0.0389755,-0.09888587,0.00815721,-0.00735299,-0.03355905,-0.04490562,-0.01522008,0.04854809,0.01328492,-0.02395368,-0.02165928,-0.01749894,0.01749223,-0.01603894,0.04978343,0.01863806,-0.02328019,0.10816444,0.02405119,-0.00706282,-0.03991242,-0.00492286,0.03365331,0.02265263,0.0608007,0.01128634,0.01939781,0.08781465,-0.00224299,0.05039898,-0.03030312,-0.05199884,-0.18210794,-0.02326747,-0.0223695,0.01703836,0.04019052,-0.02080346,0.03277819,-0.00625236,0.06184661,0.07626808,0.06427178,-0.06652769,-0.03007355,0.04532877,-0.01457988,0.02497097,-0.10678487,0.00982234,-0.04470675,0.06521691,0.01376586,-0.00044274,-0.04616804,-0.05364076,0.04306752,-0.00413687,0.12542675,0.02363437,0.00321261,0.00627376,0.03668841,0.00745265,0.00204569,0.04863621,0.00672497,0.00776756,-0.05736534,-0.03093673,-0.06580204,-0.03335382,-0.06542584,0.05175985,-0.00405851,-0.07269163,-0.05372602,0.03705437,-0.03640502,0.06619723,0.02501123,0.04781086,0.04759681,-0.02581899,0.06682555,-0.00878459,0.06551989,-0.05652864,-0.03005185,-0.01216309,-0.02063226,0.07252581,-0.01451157,-0.01833404,0.04104426,-0.02030149,0.01627559,-0.01672026,0.02540056,-0.04579229,0.01712847,-0.01226522,-0.00098809,0.0740286,0.03062566,0.01459289,-0.01972483,0.00523599,0.04525528,-0.06752834,0.03751737,-0.00203396,0.05134319,-0.08354972,0.02190554,0.06445,0.02466717,0.00018388,0.06205923,0.05898305,-0.00250172,0.021292,-0.02709638,0.00990621,-0.03372509,0.0569506,-0.03491505,0.02274901,-0.2542533,0.05494416,-0.04905289,0.11104135,0.03720612,-0.05599042,0.07003067,0.01802177,0.00417906,-0.01090211,0.04905823,0.02710691,0.08083183,-0.01718431,-0.01087833,-0.02635517,-0.04293772,-0.05904827,0.03832562,-0.00131017,0.07004284,0.04288769,0.21785465,0.00249752,0.01939115,0.0013417,0.0095739,-0.00086181,0.02310321,0.00371142,0.0053454,-0.01618253,0.09220371,-0.07952151,-0.03713871,0.00638349,-0.03837163,0.06064303,0.01961436,-0.04716162,-0.02620645,-0.01579834,-0.01957221,-0.00233853,0.15120207,-0.05336421,-0.01868858,-0.0898727,0.04527399,0.07672227,-0.06194248,-0.01738772,-0.00312507,-0.01420989,-0.00568563,0.03450828,-0.00742657,0.03893955,-0.01784328,0.00109614,0.03433947,-0.02213115,0.09194934,0.02807836,0.00456546],"last_embed":{"hash":"1pzuy5z","tokens":189}}},"text":null,"length":0,"last_read":{"hash":"1pzuy5z","at":1753423461625},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{3}","lines":[66,67],"size":477,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05815351,-0.04977503,-0.02570846,-0.0012435,-0.06018177,0.07138205,0.11310272,-0.04169578,0.07196958,-0.00197752,0.0228705,-0.03979397,0.05555427,-0.01289983,-0.00043524,0.02518654,-0.04741229,-0.04742104,-0.08509072,0.03271144,0.08936275,-0.0250423,-0.04391621,-0.08591889,0.04156828,0.01601035,0.01480067,-0.03488794,-0.03674303,-0.20549564,0.05706322,-0.00978626,-0.07207005,-0.0643036,-0.08514935,-0.03651943,0.00155466,0.05225364,-0.00735127,0.09175946,0.03766407,-0.00589989,-0.00953418,-0.03400275,-0.00837374,-0.02962271,-0.00650574,0.00031228,0.0371474,0.02375936,-0.02805789,0.01615696,0.07624674,-0.01887413,0.07046632,0.01133564,0.04073386,0.07160697,-0.00243408,0.02237258,0.02015078,0.02992719,-0.16302137,0.04829845,0.04270193,-0.00611708,-0.03073283,-0.017798,-0.04768748,0.05488624,0.08263449,0.01250512,0.0033113,0.10415712,0.0418371,-0.05452339,-0.03646659,-0.07869821,-0.02428637,0.06100384,-0.08407251,-0.0103134,-0.01040251,-0.04525455,0.00923107,0.02124355,0.0022157,0.02461373,0.02261243,-0.06689209,0.05579419,0.01401014,-0.02634683,0.06123297,0.04223519,0.07215346,0.04305146,-0.0190673,0.04221529,0.10960744,0.02234909,-0.01492547,-0.03433992,0.02381052,0.04854122,-0.03956166,-0.06423913,-0.061579,-0.00893406,-0.0003813,0.05227969,-0.00969054,0.11740729,-0.04061585,-0.06598806,0.02633926,-0.01271108,0.00867636,0.00107243,-0.03244959,0.00348303,0.00489929,0.0129479,-0.04422483,-0.01844219,0.02885541,0.05847848,0.10296685,0.0350253,0.0433246,0.05642955,-0.07888292,-0.15207171,-0.04949372,-0.00735689,0.00493177,-0.01518007,0.0447059,-0.03383685,-0.02994105,0.01558275,-0.09824288,0.00812845,-0.08663771,-0.01513461,0.06916912,-0.01327004,-0.01535151,0.02301749,-0.07158142,-0.05819393,-0.03530758,-0.0220891,-0.06354959,-0.00873843,-0.01047474,0.02385648,0.07461655,-0.03604823,-0.00574891,-0.04925539,-0.05970587,-0.02313716,0.05833462,0.01639703,-0.02673346,-0.01966139,-0.00674384,-0.00332593,-0.03304381,-0.02451228,-0.00309765,-0.07553543,0.04301301,0.09885595,0.03243487,-0.11583587,-0.05626729,-0.05554661,-0.01141692,-0.03462999,-0.03429112,-0.05931493,-0.03111814,-0.03236315,-0.07114572,0.04210617,-0.04248118,0.03857159,0.05497032,-0.05321423,-0.03944451,-0.07242864,0.02022024,0.01207594,-0.03370266,-0.04819042,-0.01884386,0.07113674,0.00028463,-0.06423521,-0.01287358,-0.00253169,0.03434808,-0.00818366,0.04556723,-0.00556318,-0.01255178,0.0887882,0.02612513,-0.04502913,-0.02896477,0.01568934,0.02888677,0.0187299,0.0630013,-0.00024211,0.0268869,0.06121713,0.00783157,0.03440129,-0.00383268,-0.03829218,-0.20018831,-0.03342865,-0.03529102,-0.00548326,0.04174649,0.00264931,0.03540394,0.01408436,0.08802254,0.0433864,0.04960629,-0.04293349,-0.01360487,0.03489021,-0.00717324,0.06949886,-0.09826551,0.01822689,-0.02199673,0.05052095,0.00722035,0.03599088,-0.02640118,-0.04829568,0.04065821,-0.00680908,0.12120099,0.05472143,-0.02930944,-0.02688633,0.04233126,0.00915916,0.01272072,-0.00254792,0.009456,0.03245365,-0.06965657,-0.02755171,-0.07060091,-0.03407807,-0.06681429,0.03146394,-0.00822909,-0.07233037,-0.0347913,0.02827176,-0.01885563,0.04154939,0.01650458,0.02041413,0.05092572,-0.04508366,0.05348743,0.02874479,0.05934891,-0.03431971,-0.04685926,-0.03624777,-0.00032719,0.06139415,-0.01438151,-0.00850171,0.02930652,-0.02919934,0.02314997,0.00639214,-0.00125069,-0.03300007,0.01137809,-0.00776778,0.00300929,0.05580059,0.03613169,0.01446066,-0.04019131,0.00169228,0.03761099,-0.04956357,0.05920828,0.03590647,0.0424458,-0.07548863,0.01129205,0.04121669,0.03438582,0.00254356,0.04199805,0.05383211,0.00159311,0.00022369,-0.02087107,0.0376514,-0.05013708,0.03129266,-0.02439836,0.0106276,-0.26632056,0.05547323,-0.03456165,0.09169252,0.02858973,-0.03269144,0.0397782,0.03905041,0.00930472,-0.01192791,0.06017068,0.03758496,0.07046261,-0.02209648,-0.02972486,0.05090845,-0.03870234,-0.0504019,0.03238452,-0.01644844,0.0503888,0.06692436,0.22052322,0.02230643,0.02260591,-0.0008803,-0.00882004,-0.01987296,0.03666682,0.01209621,0.014253,-0.03367451,0.09885532,-0.03286909,-0.02402558,-0.00170484,-0.06778167,0.05393781,0.02690399,-0.01016405,-0.02087385,0.01547566,-0.02949825,-0.01975462,0.12364636,-0.03358505,-0.0322994,-0.09191365,0.03935554,0.08868349,-0.03738304,-0.03459379,-0.00919367,-0.02922556,-0.00676768,0.06473494,-0.01088844,0.05018613,0.00309909,0.02575393,0.0292656,-0.01450462,0.05994166,0.02483237,0.02287483],"last_embed":{"hash":"hx297o","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"hx297o","at":1753423461676},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{4}","lines":[68,69],"size":388,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{5}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07569052,-0.03605052,-0.00858294,-0.01815847,-0.05295581,0.10310736,0.09138869,-0.04318472,0.08186521,0.02130021,0.0482854,-0.02386448,-0.00390039,-0.04224451,-0.04844306,0.05171283,-0.04648096,-0.02297828,-0.08725885,0.01598374,0.05990149,-0.08708918,-0.02983421,-0.09274031,0.03341177,-0.01173127,-0.01515007,-0.09945367,-0.0481977,-0.23229608,0.01909335,0.04714757,0.01555494,-0.08873467,-0.06865956,-0.05319234,-0.02535698,0.08592805,0.00032617,0.0000748,0.03685356,0.02597873,-0.00009733,0.007309,0.00026543,-0.05539773,-0.01221901,-0.02538131,0.05664457,0.01583383,-0.02155635,-0.04703894,0.04573349,0.01977981,0.03330953,0.01568778,0.06022356,0.05197356,0.03981734,0.04295643,-0.02254413,0.01271313,-0.13849697,0.06298041,-0.01170846,-0.02462575,-0.01657241,-0.00591036,-0.05605717,0.07509116,0.03162779,0.03962917,0.01022152,0.06936565,0.051067,-0.03868333,-0.02193993,-0.08638409,-0.00283592,0.01417404,-0.08935982,0.00659504,-0.01818804,-0.0036631,-0.01932035,-0.01894262,0.06279959,-0.00521311,0.0157624,0.01390512,0.07129769,0.07817304,-0.07885649,0.00415266,-0.01434682,0.02505312,0.03209039,-0.01598759,0.02415131,0.1084614,-0.00344965,-0.02343253,0.00180568,0.02429294,0.02414518,-0.04385072,-0.03100293,-0.02474542,-0.03186245,0.0071462,0.04901325,-0.02565748,0.04693698,-0.04553296,-0.02526214,0.00907254,0.0219389,-0.00722562,-0.01066073,-0.03477952,-0.01496882,0.04832326,0.0222611,-0.04893862,-0.00633787,0.06062755,0.01499481,0.11515062,0.0182186,0.00241812,0.04861763,-0.05052542,-0.09410305,-0.02860434,-0.02301271,0.03097574,0.02342828,-0.00954719,-0.04854614,0.00674762,-0.03061555,-0.04111734,0.02463585,-0.07587316,-0.03660257,0.06830969,-0.03791865,0.06083784,0.00454269,-0.0600046,-0.02309388,-0.01538926,-0.04760381,0.00783555,0.0197877,-0.00587177,-0.00273541,0.06495088,-0.07261793,0.00760088,-0.04121705,-0.06019825,0.02098407,0.10746419,0.0012015,-0.00179543,-0.0097539,0.00054082,-0.02333432,-0.08461646,-0.00340283,0.00634102,-0.02703134,0.0329404,0.09470439,0.04581866,-0.08742347,-0.0403121,0.01644481,-0.01139649,-0.01865231,0.00115985,-0.04829405,-0.00663315,-0.03606077,-0.05624021,0.04677927,-0.01075712,0.01712262,0.01675851,-0.02887272,0.0060361,-0.07399256,0.01361847,0.02161543,-0.04150205,-0.02913617,-0.05593251,0.07404473,-0.08104477,-0.00228796,-0.01370206,-0.02883358,-0.00073581,0.00470119,0.04566284,-0.00697575,-0.02138447,0.07600541,0.00265926,-0.04706141,-0.0154897,0.00887111,0.04548812,-0.02271531,0.05866443,0.05682876,-0.00072949,0.0156308,-0.05343214,0.02598291,-0.0253509,-0.013192,-0.19595417,-0.02108796,-0.00855896,0.03600695,0.05494686,-0.00530742,0.00639675,-0.00606424,0.08484865,0.09048692,0.09561802,-0.04130997,-0.03824985,0.05922958,-0.02911495,0.00762084,-0.12220778,0.0035875,-0.04407481,0.07609902,0.0117307,0.0006809,-0.03810624,-0.0469128,0.03401323,0.0349672,0.14428604,0.02095014,0.0026818,0.01509206,0.04767415,0.02556428,0.01461567,0.05246052,0.06134709,0.0115131,-0.0888623,-0.02672231,-0.06580415,-0.036841,-0.05675908,0.07017074,0.01251363,-0.08484989,-0.07620628,0.03845663,-0.01669464,0.05562849,0.00565289,0.01705099,0.05673993,-0.0424358,0.03542611,-0.00226379,0.07930619,-0.07346462,-0.05297566,-0.0298013,-0.03462468,0.08934499,-0.05180711,-0.02227154,0.05848967,-0.03763675,0.01859851,0.03894677,-0.00429724,-0.04137901,0.00603358,-0.02399735,-0.00087416,0.07661799,0.01628068,-0.02178852,-0.01867975,-0.00816515,0.08885997,-0.04471927,0.03229157,0.00757616,0.05664872,-0.08393041,-0.00986473,0.06358135,0.06690677,-0.01266385,0.03273574,0.07949981,-0.00017831,0.03069895,0.00886668,0.02810691,-0.02202777,0.03254702,0.01131879,0.02442319,-0.25925067,0.02521018,-0.02436693,0.09534437,-0.03232763,-0.03848238,0.03789202,0.01850295,0.03510474,0.00058688,0.0467585,0.07271621,0.07400335,-0.04809907,0.04840739,-0.04330397,-0.05683962,-0.03129423,0.04708401,-0.00507951,0.07509573,0.04642422,0.21080163,-0.02105625,0.01834658,0.0008556,0.05091348,-0.01808231,-0.00789879,0.03958054,-0.02564806,-0.005944,0.05959414,-0.05042521,-0.03337693,0.06086811,-0.03323104,0.03427114,-0.00700415,-0.05378859,-0.0067952,-0.01501821,-0.02971106,0.00043272,0.13184455,0.00254671,-0.01471087,-0.07110608,0.06275643,0.07661997,-0.03738798,-0.03923832,0.00267893,-0.01931103,-0.01889564,0.02500854,0.02459081,0.01467,-0.04208409,0.01515686,0.03583563,-0.02436262,0.06562453,-0.01576524,-0.01909646],"last_embed":{"hash":"d2x4ir","tokens":231}}},"text":null,"length":0,"last_read":{"hash":"d2x4ir","at":1753423461725},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{5}","lines":[70,70],"size":640,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09880894,-0.04943282,-0.03933801,0.01378422,-0.05691195,0.05538827,0.06704049,0.0221388,0.06969433,0.00941849,0.04668559,-0.02055741,0.04506328,-0.00883368,-0.02814932,0.00806451,-0.05034157,-0.05021851,-0.08487213,0.00594565,0.09967015,-0.05100702,-0.06803472,-0.10323375,0.08270521,-0.02034028,-0.01157832,-0.07442389,-0.07360511,-0.24233134,0.01385286,0.03406162,0.01514779,-0.10051849,-0.0900469,-0.03056277,-0.01954987,0.06575327,-0.0418588,0.03781561,0.04619734,0.01089046,-0.00106575,-0.01640347,-0.03676936,-0.0353458,-0.01649889,-0.04374022,0.03723658,0.02158242,-0.04618026,-0.01309798,0.02052665,0.03568386,0.05632646,0.0009832,0.04867323,0.08436381,0.01211637,0.03385121,-0.02019284,0.07341087,-0.16063945,0.03495762,0.03704277,-0.02025148,-0.02230402,-0.03169284,-0.00964678,0.0488633,0.04749272,0.06145417,-0.00276789,0.07115058,0.02033235,-0.00944037,-0.02639382,-0.06984148,-0.04402009,0.01772975,-0.06382113,0.0020054,-0.054557,-0.01031635,-0.01299113,0.02347056,0.03274228,0.02155426,0.03475702,-0.02659713,0.02208865,0.0241868,-0.01977373,0.02975685,0.0032903,0.06355521,0.05202009,-0.06023946,0.05318679,0.10219339,0.04175828,0.00375065,-0.00625031,0.01548367,0.0284457,-0.04890857,-0.05191226,-0.04511315,-0.01269624,0.02045488,0.06949822,-0.0144226,0.06708794,-0.02738361,-0.03620324,0.03549473,0.02390801,-0.01581185,0.00051404,-0.02946786,-0.05924208,0.03246121,0.04835624,-0.00798678,0.01204779,0.02619038,0.05488926,0.10748105,0.01326147,0.0402922,0.08072045,-0.00350803,-0.1316765,-0.04733923,-0.00584956,0.00428405,0.01199819,0.00392019,-0.02054787,-0.00083974,-0.00309379,-0.03888005,0.01479328,-0.07417209,-0.00955225,0.05552705,-0.00283725,0.04054302,0.02881234,-0.05970646,-0.01252658,0.0021671,-0.03371918,-0.04886651,0.00445842,0.0055128,0.05171233,0.09097657,-0.04722073,0.01487153,-0.01473219,-0.02148865,-0.01506778,0.11993597,0.00985854,-0.02961675,-0.01633947,0.0062633,-0.03282951,-0.02495775,-0.00174185,0.02449737,-0.05524863,0.01416723,0.10475776,0.02255707,-0.08149806,-0.03606734,-0.00511396,-0.02238511,-0.01694042,-0.00999229,-0.0274562,0.00289464,-0.0493014,-0.06048658,0.00545983,-0.01471792,-0.00617447,0.03243735,-0.01026396,0.01998004,-0.07399312,0.03746036,-0.00418674,-0.03204145,-0.01836414,-0.04685864,0.03995826,-0.0438641,-0.02516247,-0.02127778,-0.0011172,0.03060074,-0.01674422,0.05113085,0.00310657,-0.00901863,0.08215918,-0.00252934,0.0050783,-0.06655841,0.03797184,0.0897055,-0.01370263,0.04722776,0.03706269,0.00662597,0.05459953,-0.02048171,0.04990298,-0.05033797,-0.0496149,-0.18906383,-0.02919934,-0.04943209,0.01835161,0.06088845,0.01624703,0.04262428,-0.00471188,0.02304642,0.08920909,0.07694812,-0.06168485,-0.01433338,0.0644217,-0.00510368,0.02023425,-0.13958853,-0.00186893,-0.04474419,0.07613733,0.02692911,0.05857573,-0.07099795,-0.03714067,0.03958492,-0.02229887,0.13907091,0.03462074,-0.01495754,-0.00539356,0.04774533,0.02062551,0.00162218,0.01954661,0.01803624,0.02425217,-0.05165026,-0.01765168,-0.09505109,-0.04569895,-0.06449106,0.04170924,-0.03103479,-0.09511522,-0.03904664,0.02551464,-0.0522575,0.09781336,0.00216907,0.05082711,0.05111717,-0.04455091,0.03666416,0.0104667,0.05505309,-0.02492225,-0.07384796,0.01074881,-0.02058308,0.08576841,0.01239758,-0.02567405,0.02946908,-0.00835253,0.00575654,0.01537026,0.01958433,-0.03199482,0.00191979,-0.02540679,-0.00161831,0.07596706,0.00473424,0.00963683,-0.0011561,0.0246481,0.06725819,-0.04453931,0.05188772,-0.02058838,0.06145473,-0.05616995,0.00187515,0.06483376,0.03689692,0.00807056,0.04917384,0.06432062,-0.0321797,0.01331215,-0.02738984,0.02334672,-0.04559647,0.02686838,0.00748034,0.02930893,-0.26096788,0.06179515,-0.05121065,0.08098394,0.02284642,-0.04643041,0.02434636,-0.00447906,0.03267526,-0.01615081,0.0527993,0.03924272,0.03212919,-0.06701469,0.01220615,-0.02758953,-0.06426597,-0.03327386,0.0275755,-0.00822051,0.09181207,0.01898894,0.2221527,0.03104607,-0.01347618,0.00909766,0.01660887,0.01861712,-0.03985314,-0.010225,-0.01889918,0.00888624,0.05621697,-0.05318351,-0.03603223,0.02894904,-0.00019634,0.04104704,-0.0082252,-0.04529531,-0.04392939,0.00422413,-0.03308741,-0.01193591,0.14378917,-0.03298296,-0.01443837,-0.06855775,0.02635803,0.05603898,-0.08764865,-0.02407616,-0.02074177,-0.04415753,-0.0299729,0.02781443,-0.00298961,0.03114819,-0.03833195,-0.01563824,0.03001058,0.02410264,0.07155921,0.00546836,-0.01693783],"last_embed":{"hash":"1bzpc9","tokens":249}}},"text":null,"length":0,"last_read":{"hash":"1bzpc9","at":1753423461793},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{6}","lines":[71,71],"size":750,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10239915,-0.05345998,-0.03667965,-0.00349765,-0.02524958,0.06821863,0.04586733,0.00247769,0.05093937,-0.02088735,-0.00020676,-0.01224059,0.05397511,-0.02318101,0.00044556,-0.01032702,0.00204818,-0.02543611,-0.06146197,-0.00373672,0.07836113,-0.01968684,-0.05978409,-0.09741768,0.06044524,0.00016499,-0.01687546,-0.05402501,-0.0497464,-0.23557228,0.01180789,0.01736083,-0.0287735,-0.06546656,-0.09673088,-0.02648031,-0.01325283,0.0783026,-0.04074939,0.06582725,0.03734126,-0.00862502,0.00033134,-0.02118745,-0.03346176,-0.00546907,-0.010057,-0.01928408,0.02702266,0.0168651,-0.0489959,-0.00717568,0.03294529,0.0182525,0.03015682,0.0195685,0.040038,0.07734169,0.06125522,0.04609293,0.02668286,0.06585707,-0.19424406,0.04165662,0.00974506,-0.00732516,-0.00062013,-0.03542823,-0.00279343,0.05159561,0.01009334,0.08030909,-0.00866846,0.0650841,0.04856548,-0.04737741,-0.02682837,-0.0731421,-0.07285068,0.0705399,-0.04187953,-0.00717409,-0.01261041,-0.0258371,-0.01189054,0.022985,0.00043542,0.01658276,0.02997625,-0.02896463,0.0494541,0.03107462,-0.00921847,0.0323558,0.00535407,0.02940838,0.04744293,-0.04728534,0.04329739,0.09151067,0.05662529,0.01883314,-0.00851182,0.02913678,0.04508355,-0.05951011,-0.04308165,-0.0171572,0.03200778,0.03586784,0.0348111,0.01353872,0.07090762,-0.03098518,-0.08465254,0.01943078,-0.00147138,-0.02959999,0.02352498,-0.00438192,-0.05187867,-0.00294166,-0.00603218,-0.04249201,0.03203676,0.0175065,0.01636588,0.10167015,0.03100633,-0.00024677,0.04525997,-0.04372723,-0.10473149,-0.03960767,-0.02197165,0.03330167,0.00756894,0.01518872,0.00603642,0.02899534,-0.04137111,-0.05734546,0.05032497,-0.0796477,-0.04033418,0.09303002,-0.00020875,0.01141391,0.0215786,-0.03890719,-0.02057785,0.00388937,-0.04613471,-0.04609584,0.01466847,-0.02518452,0.04584919,0.08368217,-0.02398167,0.00117492,-0.05548579,-0.02567838,-0.00333098,0.16255848,-0.02254927,-0.04048288,-0.02506677,-0.00294756,-0.04150686,-0.05224211,-0.00349861,-0.00490526,-0.06031676,0.03689769,0.07521281,-0.02971172,-0.07156331,-0.00869325,-0.05298312,0.00720173,0.00428611,-0.04242402,-0.06330389,-0.01583365,-0.01984219,-0.08878057,0.01542031,-0.02265411,0.01373618,0.055477,-0.04404596,-0.00580872,-0.05276081,0.04345468,-0.00794076,-0.03496649,-0.00284565,-0.05967274,0.05006265,-0.02975402,-0.04235278,-0.01657719,0.04355368,0.01012869,-0.03372838,0.09216201,-0.01275627,-0.04199333,0.10846342,0.02342462,-0.02905477,0.00322595,-0.00926186,0.06727427,-0.00264593,0.03627596,0.02506494,0.02874659,0.00608932,0.02183471,0.02091843,0.00630254,-0.05981836,-0.19608511,-0.0633982,-0.03848807,0.04803883,0.04586753,-0.0166975,0.01066069,-0.00387961,0.03723373,0.13806033,0.02721928,-0.03865321,-0.04470798,0.06145896,-0.01164291,-0.00486048,-0.10427158,-0.03175501,-0.07461658,0.04862594,-0.01374243,0.04492021,-0.05201434,-0.03846365,0.04333963,-0.01447993,0.14864309,0.01155607,-0.01191316,-0.01913382,0.06552393,0.01830556,0.01036102,0.02510874,0.0136674,0.0240019,-0.04231282,0.0199903,-0.05185619,-0.03150651,-0.04911828,0.01314374,-0.00124611,-0.09073684,-0.05981761,0.03760352,-0.03957573,0.03141696,-0.00193583,0.05207575,0.04650311,-0.04698667,0.04423533,0.01405848,0.06992499,-0.01450021,-0.07866317,0.00066085,-0.00906125,0.03527055,-0.00128356,-0.01637044,0.06235086,-0.0106251,0.047615,0.05620771,0.00320578,-0.01633379,-0.01396628,-0.00484976,-0.02263772,0.09347994,-0.00239891,-0.01223852,-0.02022943,0.06333774,0.02969397,-0.05309349,0.03340173,0.00284147,0.07739797,-0.03869915,0.02599769,0.03800908,0.0570551,-0.00841468,0.07583801,0.04507498,0.00944853,-0.00601435,-0.01144318,0.02936144,-0.03677789,0.00975371,-0.0085475,0.01288622,-0.24805754,0.01329846,-0.02932407,0.07254875,-0.00921327,-0.05474118,-0.01960071,-0.00073154,0.01466277,-0.02956215,0.04644732,0.0242569,0.05855653,-0.07364794,-0.02877961,0.00723644,-0.03239084,-0.03894248,0.06766406,-0.02367426,0.07551678,0.04977684,0.24950492,-0.03455321,-0.0291468,0.01179194,0.02566116,0.01386385,-0.06298701,0.01860209,-0.00416088,-0.00628798,0.10006358,-0.00377361,-0.00681121,0.02365999,-0.01070976,0.06464263,-0.01129685,-0.0034716,-0.0271339,-0.01816968,-0.08070297,-0.00061985,0.12488893,-0.01313627,-0.05307759,-0.08762096,0.04620839,0.08418129,-0.09028041,-0.05780784,-0.02065545,-0.00383983,-0.00861224,0.01241143,-0.002666,0.0580864,0.01070614,0.02472169,-0.00157495,0.03535653,0.06199958,0.04753445,-0.00214061],"last_embed":{"hash":"1e2wqxg","tokens":225}}},"text":null,"length":0,"last_read":{"hash":"1e2wqxg","at":1753423461862},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{7}","lines":[72,72],"size":710,"outlinks":[{"title":"_**Comparison of lotto software programs**_","target":"https://saliu.com/bbs/messages/278.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{8}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07185411,-0.05707309,-0.02851478,-0.00125379,-0.06484717,0.10295001,0.04989871,-0.0159518,0.0826106,-0.00470883,0.0221267,0.00890835,0.02189382,-0.01126269,-0.03851481,0.03246129,-0.0276239,-0.03926612,-0.11335891,0.0021155,0.07863415,-0.06783188,-0.01950333,-0.10307438,0.05442464,-0.01088799,-0.01473582,-0.04331425,-0.03755433,-0.2161606,0.01733729,0.00783075,0.00510697,-0.07199083,-0.10450186,-0.06460845,-0.04895816,0.08920176,-0.00978855,0.05102333,0.01867534,0.00547344,-0.00873046,-0.02516781,-0.00637793,-0.01849303,-0.0209458,-0.04164562,0.07015897,0.02466921,-0.03631736,-0.00994453,0.03461466,0.03741126,0.06172669,0.004697,0.06329925,0.07239225,0.04476494,0.0282282,0.004809,0.0412666,-0.16419785,0.06068119,-0.00169744,-0.01661029,-0.02569789,-0.02144088,-0.04316191,0.07502612,0.00706907,0.05943941,-0.01808001,0.07344044,0.05127896,-0.03976027,-0.06865532,-0.04672334,-0.01627996,0.02898332,-0.06908698,-0.02142391,-0.00077824,-0.03348857,0.00193566,-0.00739685,0.04846277,0.01962529,0.04811524,-0.01284808,0.05794602,0.05836489,-0.04514135,0.02861702,-0.00995554,0.04642468,0.05396528,-0.03425876,0.02775188,0.08721127,0.027996,0.01652566,-0.02082139,0.03602367,0.02449393,-0.06992561,-0.05206734,-0.00235212,-0.02266911,-0.00002996,0.06082984,-0.01206198,0.06963327,-0.03694613,-0.06097037,0.02046743,0.03738635,-0.00363899,0.0277483,0.01763463,-0.03028091,0.04175951,0.00353806,-0.0542987,0.00843863,0.03278274,0.03716857,0.12325219,0.03626179,0.03576164,0.03955664,-0.02916049,-0.09614374,-0.03312373,-0.01893482,0.01907735,0.01582213,-0.01275474,-0.00432355,-0.01173402,0.0027778,-0.04765858,0.01061607,-0.07521857,-0.01818023,0.06259224,-0.00740149,0.04102426,0.04064873,-0.05342602,-0.02964259,-0.03128277,-0.02733227,-0.06220652,0.01943037,-0.00611648,0.00532089,0.07415394,-0.04719903,0.01533103,-0.04343059,-0.05808188,-0.01405508,0.08280683,0.01167171,-0.00367119,-0.00676805,-0.01817998,-0.0512758,-0.06700414,-0.02091629,0.00226599,-0.03460278,0.01982181,0.09586695,0.0158819,-0.10604969,-0.04258088,0.00281282,-0.00499798,-0.01758693,0.00880468,-0.05266652,0.01648072,-0.04613318,-0.07276972,0.02371105,0.00252743,0.0150227,0.07735518,0.00624478,0.01392782,-0.0808813,0.032019,-0.00042172,-0.03777171,-0.01424427,-0.06208963,0.07709973,-0.05585134,-0.01658597,-0.02890593,0.00400621,0.0001849,-0.01953949,0.07190525,-0.00567214,-0.01964412,0.09436645,0.01220422,-0.02001656,-0.0319864,0.03302548,0.04780941,0.00079581,0.04291999,0.01598072,0.00871767,0.06222816,-0.02642068,0.03657549,-0.03053923,-0.00545025,-0.17647368,-0.02176059,0.00570294,0.02235197,0.05648338,-0.00698692,0.0106949,-0.01568986,0.06415848,0.08285599,0.04365658,-0.0496502,-0.03156687,0.06703409,-0.03382162,0.00359532,-0.1155891,-0.00845342,-0.04855187,0.09525806,-0.01415398,0.02701601,-0.03510712,-0.04808304,0.02809097,-0.01906105,0.13144439,0.01715187,-0.02584049,-0.00982602,0.07490058,0.03020733,-0.00425297,0.02461057,0.01242571,0.02860542,-0.07113723,-0.00420422,-0.08398192,-0.02416476,-0.06549221,0.04291441,-0.02807569,-0.07679819,-0.08015144,-0.00009012,-0.05240111,0.06844654,0.00818106,0.04404675,0.0283613,-0.03117126,0.0526969,0.02142213,0.06715431,-0.02386742,-0.07473559,0.00109085,0.00002007,0.07847245,-0.00690626,-0.03559597,0.03792144,-0.0028563,0.03630916,0.03190523,0.02547365,-0.00056407,-0.00892559,0.00238031,-0.01622116,0.06096948,0.01161705,-0.0227539,-0.0319597,0.011604,0.05194231,-0.05274113,0.02001325,0.02657883,0.05679538,-0.09055836,0.00145499,0.06531541,0.06684358,-0.02604583,0.03836819,0.08425894,-0.02199694,0.0070457,0.00111081,0.02615885,-0.05863815,0.03055487,-0.03750272,-0.00228483,-0.2653521,0.02784184,-0.06900559,0.11251363,-0.02201992,-0.02593706,0.04089114,0.02379826,0.02466928,-0.00332175,0.05701124,0.07174877,0.0601367,-0.06377106,0.02133488,-0.02501391,-0.07744703,-0.03655016,0.03912048,-0.0061604,0.09277489,0.06137506,0.20921685,-0.02735016,0.00672617,0.0098223,0.0257327,0.01224319,-0.01804157,0.02386983,-0.03764018,-0.02816638,0.07314288,-0.02164241,-0.04429983,0.0152152,-0.02554494,0.06052783,-0.01667986,-0.04028531,-0.0403595,-0.03056999,-0.07045507,-0.00367423,0.14431109,0.0354067,-0.03439119,-0.0935289,0.05684011,0.09119429,-0.04866432,-0.04248682,-0.00733052,-0.04790223,-0.04395886,0.01475264,-0.00809008,0.02897143,0.0004277,0.0465849,0.00556618,0.00321211,0.07344716,0.00704883,-0.00431298],"last_embed":{"hash":"3nwcib","tokens":288}}},"text":null,"length":0,"last_read":{"hash":"3nwcib","at":1753423461929},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{8}","lines":[73,73],"size":756,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{9}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08412047,-0.05222506,-0.01400391,-0.00358631,-0.034597,0.08683962,0.02577993,-0.00876256,0.05099052,0.00821358,0.04389478,-0.00918565,0.08503989,0.00462905,-0.01514061,-0.01247311,-0.00772602,-0.0128037,-0.07791982,-0.02183525,0.06095194,-0.03857228,-0.05420646,-0.10120297,0.06021442,0.01862107,-0.01696299,-0.04919494,-0.06823274,-0.22274137,0.0122779,-0.02301005,0.00149511,-0.05918697,-0.10141504,-0.05224409,-0.01425149,0.09049623,-0.04662749,0.0370075,0.01327377,-0.00513628,-0.00245888,-0.03305793,0.00558519,-0.03660021,0.02145074,-0.02294327,0.08293419,0.03269249,-0.07181902,0.00248802,0.02041459,0.04337594,0.05834197,0.00724218,0.03707108,0.0781878,0.02949362,0.04806563,0.03240507,0.02804671,-0.14818044,0.03629339,-0.04318418,0.01017363,-0.0092167,-0.03482762,-0.04437857,0.07837105,0.05545901,0.05036993,-0.00982432,0.07556932,0.04466152,-0.06008891,-0.04334165,-0.0453699,-0.02072063,0.0288055,-0.06137682,-0.02879362,-0.02977793,-0.02913554,-0.00376471,0.01854733,0.0258917,0.036786,0.03575267,-0.0435897,0.07700402,0.02283663,-0.01795073,0.04335034,0.02301797,0.03747604,0.02989076,-0.05576896,0.00502055,0.10831951,0.03751311,0.01282147,-0.01170566,0.03908525,0.02798499,-0.06803133,-0.02872661,-0.00547742,-0.00104661,0.03315337,0.05222028,-0.01884595,0.06336924,-0.04679501,-0.03941589,0.01784014,0.03282485,0.00556961,0.01963313,-0.0055328,-0.04148167,0.01757324,0.02174097,-0.02484537,0.03238548,0.03608911,0.02461315,0.0870444,0.02964265,0.04764776,0.03815758,-0.01476408,-0.12388367,-0.02814821,-0.01048622,0.00184026,0.011276,-0.02572694,-0.00410875,0.00063589,-0.03019929,-0.01632024,0.01241548,-0.07469641,-0.02817918,0.07354033,-0.00944396,0.04091407,0.01411026,-0.07976054,-0.02572368,-0.02950202,-0.06446729,-0.0562254,0.00306491,-0.00051807,0.04517392,0.09422948,-0.02954977,-0.00901962,-0.07616346,-0.04252858,-0.0222796,0.09675834,0.00592202,-0.03867409,-0.01748757,0.0094823,-0.04531959,-0.06675992,-0.02833357,-0.00156355,-0.07148807,0.04635752,0.13436484,0.00261893,-0.07451879,-0.05828609,-0.02937267,-0.04326668,-0.04190337,-0.02528198,-0.03291769,-0.01367164,-0.06254102,-0.0960838,0.04732857,-0.02472405,0.00701287,0.05870679,-0.00536817,0.00539966,-0.07588252,0.00235693,0.00480919,-0.03610319,-0.02267586,-0.04730505,0.06956772,-0.03214897,-0.01532796,-0.02823131,0.02563037,0.01994286,-0.04002561,0.05798818,-0.003108,-0.02787323,0.13470238,0.03467714,-0.04563928,-0.02980687,0.04953629,0.05847298,-0.00845504,0.05546111,0.01715755,0.01397105,0.03432298,-0.01324772,0.04027931,0.04280866,-0.04709569,-0.2165961,-0.01957835,-0.02456202,0.03443819,0.0266426,-0.06521603,-0.00112501,-0.02208536,0.08176085,0.12953715,0.08842947,-0.07070331,-0.04107762,0.05993068,-0.02628355,0.01083668,-0.07870276,-0.02389714,-0.0402265,0.05255904,0.00290855,0.02036524,-0.02844213,-0.03878761,0.04598362,-0.02076961,0.13673829,0.02376926,0.01043322,-0.03320869,0.07745601,0.02949294,0.0101959,0.00111817,-0.01261704,0.02975057,-0.04033984,-0.00507272,-0.08618885,-0.03827421,-0.063512,0.0197338,-0.02101128,-0.08778493,-0.03943576,0.02540766,-0.03454944,0.0795548,-0.02405929,0.03197698,0.0374185,-0.01890628,0.05070122,0.0156205,0.06690312,-0.04090892,-0.05534457,-0.01235758,-0.02675323,0.06429249,-0.02502514,-0.04414189,0.02606208,-0.04568382,0.01779772,0.01575476,0.01198283,-0.03637533,0.0153142,0.0082684,-0.01591682,0.05368553,-0.01219411,0.02340502,-0.06302522,0.0395052,0.03477272,-0.03031969,0.02257504,-0.00610738,0.05798708,-0.02685297,0.02378139,0.07257608,0.05593299,0.00417137,0.08675829,0.06235559,0.01210363,0.00245661,0.00024083,0.01154854,-0.04478544,0.0229637,0.00991422,-0.00232236,-0.24374016,0.01260337,-0.04808734,0.07806661,-0.00415489,-0.05977795,0.02479481,-0.00848863,0.03022695,-0.00170908,0.05416121,0.07373155,0.03358051,-0.07215773,-0.01353684,-0.01272828,-0.04554088,-0.00966818,0.05849298,-0.00196505,0.10206591,0.02926889,0.23971491,-0.00343823,-0.00262371,0.02391934,0.01093427,-0.01289999,-0.03347916,0.05183156,-0.01361472,0.00932328,0.09144168,-0.01046741,-0.02573159,0.01227354,-0.02934415,0.05893768,-0.01276044,-0.02360223,-0.02811377,-0.00896082,-0.06401307,0.01736845,0.10716905,-0.01353623,-0.00695814,-0.07673157,0.04558661,0.08846419,-0.0628309,-0.04628166,-0.03809889,-0.00000282,-0.01295244,0.03299565,-0.00792702,0.02592938,-0.00990315,0.01435476,0.00498541,0.01235582,0.07443596,0.00873613,-0.00668994],"last_embed":{"hash":"lxbhls","tokens":167}}},"text":null,"length":0,"last_read":{"hash":"lxbhls","at":1753423462002},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{9}","lines":[74,74],"size":431,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09918388,-0.03626795,-0.03108496,0.00766155,-0.0529898,0.07908914,0.06886074,0.002523,0.0502251,0.03147659,0.041587,0.00952317,0.03629436,-0.00474656,-0.01678775,-0.02038832,0.00476698,-0.01012932,-0.07020543,0.02577915,0.06084603,-0.05382119,-0.0407107,-0.09479953,0.08046457,0.00145987,0.02273712,-0.04270642,-0.06854738,-0.21394998,0.01618819,0.01333535,-0.01311582,-0.0410101,-0.06553102,-0.06712572,-0.03014881,0.10919766,-0.02289075,0.0604217,0.03942875,-0.00180035,-0.00533382,-0.01192611,-0.02177815,-0.02659462,-0.03065631,-0.02150488,0.06861026,0.01918997,-0.05407022,0.00124543,0.06565755,0.03560689,0.05802422,-0.0198846,0.06555688,0.05438359,0.03969784,0.02181079,0.01818471,0.01412166,-0.1556824,0.03031347,-0.01001961,-0.00367726,-0.00560615,-0.02773127,-0.03010629,0.03871172,0.043332,0.02404498,-0.03397146,0.09494334,0.05455833,-0.07405797,-0.03145941,-0.05158341,-0.04190636,0.04578013,-0.04142877,-0.06099994,-0.03319889,-0.0402053,-0.0054468,0.0358647,0.02158302,0.02031254,0.01998935,-0.03869219,0.0592014,-0.04465738,-0.03370309,0.04903067,0.00919112,0.07648642,0.06867417,-0.05271637,0.0040196,0.10322894,0.04293066,0.04318829,-0.00014632,0.04276601,0.01438645,-0.06092876,-0.02724979,-0.01215144,-0.00551996,0.03551215,0.04155035,0.00173466,0.10137663,-0.04776448,-0.01899512,0.02092502,0.03307537,0.02642689,0.01752187,0.01551558,-0.02412567,0.03469998,-0.00588709,-0.0196564,0.00622563,0.01619065,0.03118911,0.10468876,0.02291795,0.03226684,0.03514668,-0.02821321,-0.09603396,-0.04709053,-0.00349135,0.00763389,-0.00260867,0.0170406,0.00229961,-0.0151198,0.0031863,-0.05131026,0.00487403,-0.08437841,-0.02534194,0.05819867,-0.03242767,0.05136348,0.03596581,-0.05727208,-0.0255004,0.00270769,-0.06335295,-0.06412499,-0.02956918,0.00841224,0.00796572,0.09648466,-0.01981407,0.01765788,-0.04572503,-0.05939689,-0.01542125,0.09997388,0.00148088,-0.03183672,-0.03216371,-0.05408764,-0.05764011,-0.05537567,-0.01458893,-0.01008606,-0.06742837,0.02045435,0.10159586,0.00236934,-0.11481097,-0.05480606,-0.03603287,-0.02197676,-0.02996354,-0.01555639,-0.04646318,-0.03279163,-0.03305797,-0.0969098,0.04240389,-0.04353261,0.02405246,0.0573347,-0.01578744,0.00240629,-0.08524382,0.02051452,0.00106071,-0.03117516,-0.01006637,-0.02822254,0.07436242,-0.04622677,-0.00768772,-0.02774483,0.00273994,0.01482742,-0.0382146,0.05853928,0.00778079,-0.02515666,0.12236765,0.02653917,-0.04200559,-0.04651683,0.03537903,0.04229118,0.02652276,0.02662779,0.03051285,0.00380878,0.04339221,-0.00312849,0.04900951,0.01417853,-0.03397941,-0.20584516,-0.0262709,-0.03072644,0.02905343,0.02560551,-0.04885138,-0.01193321,0.01064132,0.04072397,0.12715599,0.04559256,-0.03879178,-0.03682942,0.08182993,-0.01127425,0.05789779,-0.10095853,0.00439659,-0.02632938,0.04674142,-0.01028663,0.04627902,-0.03576403,-0.04113171,0.06410655,0.00561136,0.13866474,0.03814438,-0.01570533,-0.03658471,0.05277053,0.02220547,0.0284421,-0.00553589,0.0071156,0.01381172,-0.05585907,-0.00448475,-0.07553822,-0.03421763,-0.05798023,0.022442,-0.02076735,-0.07476634,-0.02728206,0.00356157,-0.03221272,0.08087722,-0.01184695,0.05985544,0.05104556,-0.04594738,0.0709384,0.01711663,0.04910728,-0.04288676,-0.0924226,-0.01732149,-0.02788075,0.07721543,-0.01900789,-0.04743417,0.03326785,0.00309513,0.02851995,0.02640296,0.01217148,0.00054802,0.00266721,-0.01703046,-0.01170949,0.03889752,0.02062019,0.00520679,-0.03449085,0.05805937,0.04003208,0.00503818,0.07241047,-0.00295354,0.0774098,-0.0293303,0.00507668,0.04027757,0.04561453,0.00164001,0.06215909,0.03099512,-0.01818042,0.00082398,-0.02799873,0.03540811,-0.04933315,0.0206443,-0.00015767,-0.02338198,-0.24883187,0.01851947,-0.05122739,0.05219933,0.00244987,-0.03760793,-0.00912583,0.02007822,0.05766197,-0.00152493,0.09089186,0.06282756,0.06341643,-0.08661649,-0.01042239,-0.02356565,-0.10870345,0.00384021,0.04309484,-0.01579027,0.08942406,0.04245549,0.22660176,-0.0399452,-0.01873218,-0.0210325,-0.00415912,-0.03226709,-0.05854821,0.02335333,-0.02158118,0.00058522,0.0612547,-0.0184359,-0.0175097,0.02311502,-0.04812806,0.07272579,-0.0193396,-0.00118553,-0.05012409,-0.03578481,-0.07382061,0.02624391,0.10862092,-0.01287931,-0.02288466,-0.07743341,0.07139947,0.11559492,-0.01940673,-0.05328768,-0.00721178,-0.01785376,-0.02577592,0.03083603,0.00190461,0.00248146,0.01331347,0.01089976,0.02059026,0.03787086,0.05396857,0.00312971,0.02512002],"last_embed":{"hash":"1mfn2gt","tokens":135}}},"text":null,"length":0,"last_read":{"hash":"1mfn2gt","at":1753423462053},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{10}","lines":[75,75],"size":340,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09401153,-0.04102124,-0.0259696,0.00886036,-0.05441925,0.09295245,0.05230064,-0.00361756,0.077096,0.03187248,0.03102975,-0.01297751,0.06330492,0.00207485,-0.03234283,-0.02559813,-0.01040598,-0.02085756,-0.05916533,-0.00954743,0.08363755,-0.04616975,-0.03397802,-0.10780334,0.08554446,0.00671086,0.00171902,-0.06950513,-0.06393272,-0.22655508,0.01644908,-0.00613687,-0.00513961,-0.06166118,-0.10667843,-0.04304487,-0.0307734,0.09475797,-0.02047474,0.06222405,0.02421686,-0.00084897,-0.00687546,-0.05154037,-0.03823022,0.00007828,-0.01158857,-0.03201963,0.04659116,0.03635605,-0.07031528,-0.00839147,0.06038802,-0.00202169,0.06659108,-0.0054127,0.05112008,0.07123744,0.01588608,0.01362987,0.02264943,0.00911589,-0.15343821,0.03360523,-0.01706047,-0.01043104,0.00894092,-0.03094937,-0.04463243,0.08568662,0.04994088,0.02019424,-0.02586684,0.10039625,0.06393359,-0.0589753,-0.03242646,-0.03836558,-0.03722388,0.05017936,-0.05241316,-0.05127002,-0.01544895,-0.03611994,-0.00101062,0.00545053,-0.00087717,-0.01385014,0.01080404,-0.0287718,0.05804924,-0.02473179,-0.02718794,0.03338972,-0.00234226,0.04910755,0.04867714,-0.05529835,0.0102119,0.09887172,0.04284471,0.01947886,0.00611704,0.08105382,0.03495185,-0.05676061,-0.03129127,-0.03163996,-0.01451766,0.02183018,0.02374466,-0.01914151,0.08521503,-0.02769495,-0.03316882,0.02895918,0.01616802,0.00629353,0.03388656,0.01607723,-0.03326823,0.03861238,-0.01370514,-0.02297173,0.00248778,0.04549513,0.03199651,0.11305089,0.01528333,0.03138081,0.04132542,-0.04087338,-0.09945455,-0.02770258,-0.02520845,0.01248588,-0.02567127,0.02113746,-0.00306958,-0.03471203,-0.00540034,-0.03057703,0.00845187,-0.07548404,-0.01788482,0.06816896,-0.02068671,0.04884439,0.02237679,-0.08173542,-0.03173298,-0.01809945,-0.049286,-0.05005578,-0.03684266,0.00098415,0.01846832,0.10887031,-0.01985736,0.01645637,-0.05479631,-0.06610636,-0.04234777,0.1036877,0.00348865,-0.00986762,-0.01558022,-0.02119152,-0.06346417,-0.02968372,-0.01143992,0.00775515,-0.0743678,0.03787491,0.12132799,0.0009578,-0.08544533,-0.07605828,-0.05309457,-0.01224284,0.00049952,-0.02549985,-0.05286052,-0.03721286,-0.02230681,-0.07933365,0.01951721,-0.05768354,0.03943585,0.06228668,-0.04952589,0.005108,-0.07169358,0.00547753,0.00313321,-0.04737868,0.00836654,-0.02870489,0.04196109,-0.02770815,0.00258748,-0.0038586,0.01669116,0.03365036,-0.03289922,0.04581713,0.0082573,-0.00617301,0.10434216,0.03885141,-0.04331992,-0.05786983,0.01682105,0.05690718,0.03011608,0.05407349,0.01367789,0.00424491,0.04018659,-0.00266199,0.02710054,0.01678907,-0.04301489,-0.20896588,-0.01974439,-0.00811815,0.02535311,0.04436805,-0.01384626,0.01269149,0.00126456,0.04689997,0.10545943,0.06224158,-0.06024378,-0.04138478,0.06581712,-0.00438035,0.06442954,-0.10565963,0.01058501,-0.01542817,0.06069272,0.00005483,0.03578181,-0.03147625,-0.0214721,0.04181174,-0.00707177,0.14921479,0.05670863,-0.012539,-0.03553091,0.04220035,0.03006952,0.02756544,0.00171987,-0.02677105,0.03610819,-0.05235557,-0.01046803,-0.0656481,-0.05078261,-0.06017774,0.0366825,-0.00622034,-0.05754313,-0.02187252,0.03392265,-0.02329356,0.01962164,-0.00572675,0.04029975,0.06785763,-0.06309398,0.0244444,0.03720485,0.07284003,-0.03582576,-0.08144635,-0.03019701,-0.0269136,0.05075981,-0.00749088,-0.02769673,0.04372766,-0.03505189,0.00921267,0.03142327,0.02340008,-0.00703282,-0.00054643,0.01575222,-0.00548498,0.07068,0.00627933,-0.02810342,-0.04309652,0.05080985,0.01594234,-0.01823831,0.01329623,0.02023746,0.07655869,-0.03978242,0.01335195,0.03556142,0.05230328,-0.00696229,0.07248336,0.0197741,-0.00746022,0.00101975,-0.00583079,0.04895463,-0.04100304,0.02531666,0.0018823,-0.02408385,-0.26866606,0.05497833,-0.03412408,0.04123061,0.02671756,-0.04411857,-0.01540446,0.00714397,0.02655459,-0.00543836,0.05472325,0.07098181,0.0508417,-0.06759264,-0.02211856,0.01503415,-0.06729579,-0.02624352,0.05530802,-0.04384488,0.07141239,0.03635798,0.23516649,-0.02954636,-0.01010052,-0.00166582,-0.00731484,-0.01045958,-0.02733832,0.03287597,-0.03154667,0.01833424,0.11346449,0.01715433,-0.01322957,0.03206524,-0.04400884,0.07916219,0.01318538,-0.01766542,-0.06000425,0.00173421,-0.03573386,0.01862174,0.09656645,-0.03877318,-0.02665634,-0.09074804,0.05701933,0.09574296,-0.04573984,-0.04059709,-0.00799864,0.0160117,-0.00643212,0.03807728,-0.03760327,0.03913647,-0.01608804,0.03008359,-0.00454805,0.01110457,0.06219215,0.00939794,0.01876296],"last_embed":{"hash":"1pcsomv","tokens":163}}},"text":null,"length":0,"last_read":{"hash":"1pcsomv","at":1753423462094},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{11}","lines":[76,76],"size":421,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09599365,-0.04445117,-0.02588399,0.00094938,-0.03932615,0.0900899,0.04085426,-0.00319592,0.0888216,0.01835276,0.03839266,-0.01770458,0.06548607,0.00564752,-0.00620288,-0.01739779,-0.00505626,-0.04244642,-0.0733581,0.00892298,0.07355089,-0.04914973,-0.0428143,-0.10358173,0.05540199,-0.03455012,-0.01367949,-0.07611006,-0.07401126,-0.22991967,0.01548993,-0.00236714,-0.0084835,-0.05116476,-0.09848331,-0.0522404,-0.01001821,0.0811303,-0.03581543,0.05224648,0.01986951,0.01725182,0.00414717,-0.05429049,-0.02291569,-0.00465617,0.00404856,-0.00211729,0.08361144,0.03629056,-0.06202235,0.00768728,0.02632406,0.0118863,0.08478545,-0.01084804,0.04628448,0.06671176,0.01582407,0.03073457,0.02309761,0.00890343,-0.155293,0.02773135,-0.01330037,-0.0136126,-0.01003056,-0.02820761,-0.04245779,0.06030857,0.06572548,0.03725664,0.0009275,0.05875842,0.06256545,-0.04871588,-0.04096143,-0.04346576,-0.01929686,0.03752103,-0.04962765,-0.0533458,-0.00997067,-0.03014662,0.00858017,0.02485057,0.0141882,0.01881379,0.02919637,-0.06277686,0.06348549,0.01022397,-0.03160035,0.01610916,0.01979448,0.06091096,0.02237321,-0.06682108,0.00652447,0.12157447,0.05414678,0.00827042,-0.01625059,0.04017401,0.02874238,-0.05108165,-0.0098269,-0.01753003,0.00943942,0.02486159,0.03485893,-0.01016978,0.10409142,-0.01367555,-0.04588494,0.02397276,0.01289306,-0.00172684,0.01880194,0.02305728,-0.03970439,0.01376013,0.02749658,-0.03974525,-0.00013606,0.04955983,0.0363753,0.09996703,0.01942543,0.05577663,0.02538483,-0.04748414,-0.11373083,-0.0430892,-0.01998577,-0.01075353,-0.00298051,-0.01936853,-0.01869601,0.00143412,-0.0182731,-0.03346772,0.01284938,-0.10284285,-0.01732136,0.08700805,-0.01265548,0.02819634,0.0389966,-0.04913401,-0.00318114,-0.01662687,-0.04167114,-0.08335856,-0.03600201,0.00291018,0.02327667,0.11912691,-0.02966221,0.00064816,-0.06408478,-0.04957469,-0.03432035,0.10109094,-0.01672498,-0.00612721,-0.03023198,-0.04825291,-0.0656796,-0.01735937,-0.03247474,0.00753118,-0.0601754,0.0507866,0.12882604,-0.00018755,-0.09518566,-0.05951269,-0.05315765,-0.00342866,-0.021231,-0.01191676,-0.03414841,-0.02532436,-0.02497501,-0.06981388,0.04468643,-0.02559264,0.04773052,0.05470552,-0.07522608,0.00971225,-0.09686189,0.02218656,-0.0077944,-0.02920208,-0.00934813,-0.02810673,0.04952019,-0.00843395,-0.0343716,0.00641905,0.01894174,0.04567193,-0.05215386,0.06238294,-0.00458504,-0.01668433,0.09888613,0.03579836,-0.03724651,-0.04854271,0.00875479,0.05610798,0.02347044,0.05868923,0.02982841,-0.00567878,0.02723281,-0.00331809,0.04455004,0.00346487,-0.06475453,-0.20150232,-0.02919109,-0.03454612,0.00309649,0.04038613,-0.04526218,0.0057696,0.00652647,0.0575081,0.11893655,0.06050831,-0.0732407,-0.01222987,0.08204593,-0.01479367,0.04225123,-0.09312817,-0.00059826,-0.00592925,0.04841204,0.02646315,0.01335991,-0.02100506,-0.02524028,0.04403575,-0.02373756,0.14819889,0.01899364,-0.00309427,-0.00154199,0.05480564,0.0325138,0.00919359,0.01015163,-0.01228279,0.02651201,-0.05507255,-0.02008579,-0.07343345,-0.0344824,-0.04654837,0.02704809,-0.02144243,-0.09104852,-0.0356655,0.04506698,-0.02400182,0.05560725,-0.01747195,0.0077801,0.0541647,-0.03740363,0.0283348,0.00316302,0.07187731,-0.01596573,-0.08150681,-0.0059629,-0.0205379,0.04815306,-0.03080928,-0.0368855,0.03542987,-0.030981,0.01114322,0.02863597,0.00598676,-0.00735434,-0.02014267,0.00586902,-0.00371054,0.04769052,0.0154931,0.00464136,-0.05887301,0.04298909,0.02994579,-0.03681273,0.0492506,-0.00035175,0.07415826,-0.04489481,0.03236715,0.04194511,0.04932857,0.00255761,0.06159152,0.03154531,-0.02530099,-0.01291931,-0.00940638,0.03798316,-0.05037034,0.02597997,-0.00572355,-0.01282328,-0.25331837,0.04006637,-0.03525005,0.0471243,0.00463966,-0.03526365,0.01373251,0.02985088,0.02599114,0.00003449,0.08363515,0.0595869,0.04269778,-0.04172289,-0.02472156,-0.00434142,-0.06721783,-0.03825635,0.07701341,0.00463825,0.08959404,0.02717597,0.24328366,-0.02537557,-0.00896255,-0.0046656,-0.00275819,-0.00137116,-0.03430114,0.05560512,-0.01766266,0.02316995,0.10607494,-0.00859474,-0.03088457,0.01004722,-0.02760915,0.0683276,-0.01090423,-0.0032355,-0.0473192,-0.01683822,-0.05176201,0.00731054,0.09838819,-0.0297941,-0.02705921,-0.0856723,0.06626593,0.08495066,-0.04966326,-0.03491279,-0.01265123,0.01539319,-0.00704543,0.04471437,-0.03238766,0.01266168,-0.00763907,0.02560333,0.00343993,0.01184844,0.05828762,-0.00090173,0.0328562],"last_embed":{"hash":"1m9pjkd","tokens":115}}},"text":null,"length":0,"last_read":{"hash":"1m9pjkd","at":1753423462177},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{12}","lines":[77,77],"size":286,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{14}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0716251,-0.02681271,-0.02081771,-0.02702937,-0.05338338,0.05092229,0.02277921,0.0014556,0.02537792,0.03359593,0.0083087,-0.05074942,0.08036429,0.00765728,0.01786962,-0.00240128,0.01701038,-0.00847765,-0.09909865,-0.0305947,0.1344184,-0.01233862,-0.0619537,-0.10837851,0.04267874,-0.03230082,0.0283451,-0.06732876,-0.03523117,-0.22184023,0.00967166,0.00199222,0.00061217,-0.0336914,-0.08431236,-0.02618697,-0.0261744,0.06587823,-0.03802926,0.05518534,0.00110571,-0.00025464,-0.00704452,-0.04933668,-0.0318107,-0.04147983,0.00116204,-0.04076906,0.04682388,0.02367061,-0.05292485,0.00576199,0.03229508,0.06430981,0.01735931,0.00892676,0.03699195,0.06314383,-0.00236846,0.03268183,0.01036167,0.01994483,-0.18509239,0.03320036,-0.03168053,0.01964331,0.00238711,-0.03492302,-0.04702927,0.07433563,0.03717862,0.06688144,-0.01637282,0.07309528,0.06930514,-0.03691684,-0.08073353,-0.06234249,-0.02673827,0.04791778,-0.05353977,-0.04401445,-0.02982905,-0.02551633,0.02405709,0.01170761,0.00830774,0.00225988,0.00801031,-0.02530381,0.01981754,-0.0331992,-0.00155658,0.04958363,-0.00502757,0.04645973,0.03076285,-0.05855621,0.02750638,0.11948185,0.04382816,0.00569532,-0.00125668,0.00369108,0.02576134,-0.04746016,-0.02029618,-0.00321479,0.00652305,0.02730029,0.01586577,-0.02039132,0.06725834,-0.04949798,-0.05222063,0.0140689,0.02860752,-0.00724908,-0.00677534,-0.00775616,-0.05135405,0.00021942,-0.02987944,-0.04284135,0.06160503,0.04970538,0.02480622,0.11409763,0.00831915,0.06686098,0.01603613,0.00386955,-0.09872206,-0.0136409,-0.0332627,-0.01309321,0.00785869,-0.03428842,-0.01285969,0.01543834,-0.05879289,-0.0274264,0.0133643,-0.0724482,-0.00380958,0.08157011,-0.03657067,0.04982025,-0.01087306,-0.0755569,-0.01264843,-0.00007691,-0.00716311,-0.05227004,-0.00370445,0.03297236,0.04505046,0.09561566,-0.04273571,0.02263229,-0.021727,-0.01881729,-0.03806337,0.13472289,0.00985743,-0.04135859,-0.04144935,0.00375985,-0.06623659,0.00550059,-0.01872588,0.0269224,-0.06055674,0.05455275,0.10983339,-0.00048482,-0.10781189,0.00086721,-0.07040244,-0.01301249,-0.0550463,-0.03868496,-0.05576411,-0.02920255,-0.04537664,-0.1184799,0.067313,-0.05730756,0.06332308,0.06089809,-0.03253011,0.04147913,-0.04894462,-0.00027082,0.00872153,-0.01608867,-0.00566379,-0.02538938,0.05839339,-0.06203933,-0.00373957,0.02098636,0.01646268,0.04436732,-0.02265987,0.04562301,-0.03705491,-0.01225037,0.13079624,0.0236336,-0.04141022,-0.01587592,0.06787521,0.0768149,-0.00219257,0.03324926,0.00820068,-0.01144167,0.02608994,0.01360061,0.05601215,0.00989326,-0.06656159,-0.22793236,0.00956252,-0.02678393,0.04059582,-0.01108602,-0.044549,-0.00692352,-0.01935654,0.00439756,0.13568483,0.0625041,-0.01900426,0.01166552,0.0731179,0.00545484,0.03946305,-0.10329513,0.00260743,-0.03740532,0.05875124,0.018949,0.00545582,0.0086853,-0.04299891,0.01643619,-0.02004582,0.15314993,0.0212788,0.01294407,-0.01745537,0.08063675,0.01187856,0.01545672,0.01944844,0.00829982,0.02211219,-0.08138166,-0.00359228,-0.02565931,-0.03764698,-0.02134245,0.03826616,-0.02785344,-0.10779815,-0.01422388,-0.00846313,-0.02991682,0.05721324,-0.0246508,0.01989226,0.01577795,-0.05232539,0.06146102,-0.01832659,0.08939125,-0.05857299,-0.08354376,-0.01387112,-0.05083603,0.02544333,-0.02030067,-0.03206396,0.03222572,-0.0171336,0.01491112,0.03158095,0.02408477,-0.00340928,0.01675305,0.01256572,-0.04108289,0.06486949,-0.01491027,0.01244714,-0.05204809,0.03812389,0.0144889,-0.03893392,0.01963883,-0.02723614,0.07057095,-0.04763364,0.02905432,0.03683652,0.03900449,0.01185813,0.07231469,0.01966945,0.0176789,-0.00687135,-0.01191048,0.03676378,-0.0218793,0.04918206,0.04515582,-0.01213093,-0.25745973,0.02424109,-0.02673813,0.03632449,0.02640541,-0.0366546,0.01317622,0.0062508,0.03921529,0.00860598,0.04657593,0.04306456,0.04910878,-0.06223657,-0.01990385,0.03778709,-0.03354299,-0.02033359,0.07788465,-0.001347,0.06143289,0.03175944,0.23820981,-0.01437158,-0.01565782,-0.01878036,-0.00933664,-0.02372511,-0.06612259,0.00654525,0.01798034,0.02718599,0.09181747,-0.01668535,-0.01727396,0.07932967,-0.00869557,0.04963312,-0.01255829,-0.01207166,-0.05058019,0.01236497,-0.07065781,-0.00728569,0.07928035,-0.01397117,-0.01243627,-0.07441548,0.04069686,0.05101214,-0.04868094,-0.00359261,-0.01752625,0.01716904,0.01201506,0.0063663,0.00825449,0.00880154,0.01318915,0.02211855,0.00845973,0.0439118,0.04466513,0.0284818,0.01757353],"last_embed":{"hash":"1acfhd7","tokens":113}}},"text":null,"length":0,"last_read":{"hash":"1acfhd7","at":1753423462228},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{14}","lines":[79,79],"size":204,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{19}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08570921,-0.03106579,-0.02765218,0.00391206,-0.02941045,0.07570625,0.03741975,0.01488063,0.07078949,0.01630394,0.03617173,0.03004657,0.06514773,0.00598809,0.0002408,-0.00500423,0.01243412,-0.06439871,-0.06186391,0.02105669,0.0684178,-0.03168016,-0.0431869,-0.09058648,0.03785679,0.0146577,-0.00805419,-0.0531677,-0.06741779,-0.23019075,-0.00392157,-0.00729641,-0.00931656,-0.04376301,-0.10169754,-0.05091565,-0.00018771,0.09628563,-0.01085516,0.07150042,0.02814831,0.0094998,-0.03092199,-0.03132273,-0.00847235,-0.01458733,-0.01882057,-0.03063572,0.04737899,0.03295175,-0.07332393,0.03176269,0.04701105,0.04154189,0.03750977,0.01417304,0.05510721,0.07599426,0.01589143,0.03223252,0.01444564,0.00958969,-0.1489905,0.03804516,-0.01804344,0.01464729,0.00020383,-0.02607134,-0.01849047,0.0546528,0.0260317,0.06213436,-0.00752302,0.09738865,0.03545146,-0.06572267,-0.03690438,-0.03189866,-0.01747133,0.04651522,-0.02297295,-0.0572528,-0.04360417,-0.03006823,-0.00921664,-0.02423795,0.00213922,0.00893924,0.05063299,-0.07891161,0.07614265,-0.0147957,-0.01724024,0.01753493,0.0229317,0.05193678,0.06275895,-0.04922166,0.00696206,0.10115403,0.05074588,0.04147184,-0.00764156,0.07405664,0.02589243,-0.03979433,-0.03452898,-0.01566298,-0.02069199,0.02095386,0.02694328,-0.01365378,0.08564271,-0.01008619,-0.0327942,0.00494569,0.03333046,0.0196453,0.02911605,0.00378005,-0.04562025,0.03481789,0.02184048,-0.02958941,0.01829244,0.02241497,-0.00157874,0.10182819,0.01335984,0.02878798,0.02223,-0.05484757,-0.11838339,-0.04278777,-0.00792373,0.00073221,0.02448685,0.02687567,0.01903287,-0.04261693,0.0108122,-0.04881402,0.04285328,-0.08281784,-0.04262941,0.06347258,-0.00702417,0.05678272,0.02334109,-0.07045607,-0.0271223,-0.01219619,-0.08361913,-0.07608498,-0.02964346,-0.00185992,0.02071898,0.08809882,-0.00666315,-0.00485005,-0.04626582,-0.03537503,0.0038314,0.08457654,0.00215263,-0.0069738,-0.04179535,-0.04420515,-0.04958874,-0.06159446,-0.03773646,-0.02357921,-0.09871245,0.04551763,0.09000202,0.02036372,-0.1077147,-0.04308015,-0.03898932,-0.00974779,-0.01945038,0.0045893,-0.03453038,-0.04329308,-0.02513025,-0.10415952,0.00951125,-0.01911889,6e-7,0.0208706,-0.02676127,-0.00412633,-0.05130566,0.00133986,-0.00813778,-0.04058083,0.00349336,-0.02399018,0.07650951,-0.05008191,-0.01721058,-0.01571321,0.00461264,0.0253824,-0.04062722,0.06060604,0.03975112,-0.0423134,0.11622731,-0.01870283,-0.03768721,-0.05255665,0.04415286,0.05439521,0.02340727,0.04187626,0.01853555,0.0069473,0.04868094,-0.00567361,0.05744128,0.02728392,-0.04406149,-0.20069753,-0.05533117,-0.0270266,0.02328724,0.02404558,-0.04764741,-0.00014624,0.01740844,0.05198248,0.15229356,0.05500313,-0.06582637,-0.03185812,0.06068289,-0.02756333,0.05093791,-0.12067613,-0.01668858,-0.02186051,0.0561816,0.00407079,0.04453371,-0.0354152,-0.03969814,0.04312742,-0.02848565,0.14783366,0.00917021,-0.01168923,-0.04300919,0.07523896,0.02612024,0.01473055,0.03189833,0.01151317,0.01865107,-0.06284115,-0.02331368,-0.07028077,-0.0038952,-0.07127975,0.04819013,-0.01998006,-0.06930258,-0.04262455,0.02565021,-0.01749825,0.07906951,-0.0204628,0.07342476,0.03603101,-0.01925156,0.08078945,-0.00223371,0.07346753,-0.02622617,-0.06758198,-0.00859912,-0.02802639,0.08512487,-0.00331701,-0.03248055,0.0186087,-0.0321486,0.02694881,0.04737066,-0.00014289,-0.01232431,0.00396273,0.00581525,-0.01252178,0.07256655,-0.02442199,-0.00118949,-0.02920282,0.0433651,0.03255082,-0.01629006,0.05932771,-0.02228113,0.08534479,-0.02318917,0.00901488,0.05527406,0.03361366,-0.04803304,0.06269003,0.04712857,-0.02400622,-0.0069939,-0.02672696,0.0282557,-0.05931884,-0.00825688,0.00176067,-0.00348164,-0.2404778,0.01619939,-0.03012779,0.03935217,0.00628407,-0.03219856,-0.01684221,0.0216275,0.06366393,-0.00799226,0.06691749,0.06249741,0.03628596,-0.05742505,-0.02433706,0.01031808,-0.0676606,0.01736077,0.04377262,-0.02016347,0.08752346,-0.00886051,0.23787291,-0.03331814,-0.0274687,0.0049323,-0.002792,0.02013296,-0.0641339,0.00183654,-0.01882892,0.0172719,0.08085826,-0.02101997,-0.03006465,0.03003426,-0.04916261,0.06369114,-0.01801137,0.00590599,-0.028832,-0.04723106,-0.05457737,0.02269385,0.11226705,0.00206892,-0.00628584,-0.08718631,0.05693089,0.10880244,-0.04816049,-0.01204737,-0.03042374,-0.02511517,-0.00930909,0.03372564,-0.01876497,0.00625718,0.00187813,0.01681021,0.02537903,0.0361452,0.04161985,0.01736303,0.01215141],"last_embed":{"hash":"1oc7x9g","tokens":215}}},"text":null,"length":0,"last_read":{"hash":"1oc7x9g","at":1753423462284},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{19}","lines":[85,85],"size":623,"outlinks":[{"title":"<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>","target":"https://saliu.com/freeware/skips-lotto.html","line":1}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{20}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11754613,-0.03296174,-0.03365243,0.0137166,-0.0337825,0.07434427,0.0399783,-0.01918324,0.08507269,0.03763505,0.03080577,0.00185513,0.08076764,-0.01269348,-0.01223994,-0.00196636,0.00503825,-0.01867013,-0.04805305,-0.00708281,0.07465742,-0.04121108,-0.03739186,-0.09781581,0.07630774,0.00844025,-0.0117164,-0.03564191,-0.06621275,-0.24185199,0.00731378,-0.00836337,0.00462638,-0.06084649,-0.09831095,-0.05888468,-0.0105538,0.04562916,-0.04383922,0.05201596,0.03525666,-0.00320365,-0.00673202,-0.03999533,-0.02214325,-0.0364187,-0.02411695,-0.01812162,0.05656201,0.03544131,-0.08250177,0.01885559,0.04902292,0.02557993,0.04551013,0.00689471,0.05744262,0.07390662,0.02862251,0.04811159,0.01979413,-0.00396871,-0.17972718,0.05262221,-0.0103461,-0.01079589,0.01311149,-0.05055684,-0.02382565,0.04690976,0.05115045,0.0277066,-0.01376441,0.07905473,0.0568314,-0.04965378,-0.02442522,-0.0586459,-0.0320232,0.03911017,-0.04149648,-0.03832562,-0.02593491,-0.05905389,0.0281541,0.04783554,0.00445178,0.02012368,0.03285633,-0.06495657,0.03796145,-0.01740263,0.01157722,0.03758654,0.01189127,0.04915949,0.05466297,-0.06278858,0.01771516,0.11607406,0.0519296,0.00447619,-0.0140381,0.05194579,0.04400272,-0.04564555,-0.01960741,-0.03901072,-0.00621744,0.01822035,0.03399571,-0.0171392,0.09947856,-0.0187888,-0.03935447,-0.01452673,0.00450672,0.00669289,0.05670072,0.00051607,-0.02224726,0.02824121,0.00413559,-0.02077744,0.00844922,0.0373961,0.03050901,0.09189897,0.01000481,0.03342808,0.04727338,-0.01764876,-0.12644383,-0.040863,-0.02091425,0.01695382,-0.01699892,-0.01670724,-0.00724913,0.00791555,-0.02946534,-0.04575506,0.00287453,-0.08392334,-0.0428453,0.06417475,-0.03785933,0.03994657,0.02262202,-0.04130899,-0.02047306,-0.03769025,-0.02921902,-0.04020305,-0.01291375,0.03702009,0.05392845,0.12618691,-0.03978828,-0.01478381,-0.0473581,-0.05173716,-0.03256694,0.12582783,-0.00418749,-0.03570874,0.00268615,0.01520752,-0.0651057,-0.02957327,-0.02091341,0.00105192,-0.06929511,0.03742282,0.11802342,-0.00847747,-0.04946546,-0.04254252,-0.03869179,-0.01430004,-0.01438455,-0.0470008,-0.05781648,-0.01826407,-0.02235817,-0.09612434,0.04221554,-0.02206276,0.03107426,0.0369431,-0.01663344,0.01148827,-0.05775653,0.00139706,-0.01694196,-0.03526196,-0.01300254,-0.03156889,0.06998977,-0.06126305,-0.01106839,-0.02505808,0.04293685,0.03658697,-0.02137158,0.05365639,0.00676819,-0.01641442,0.12429181,0.00647533,-0.05169939,-0.03453477,0.02655167,0.06430535,-0.00461166,0.05069676,-0.00896841,0.02845306,0.02282441,0.00536583,0.03430893,0.04920114,-0.09513938,-0.21700837,-0.00193108,-0.02565996,0.04000838,0.03680067,-0.01846379,0.00610911,-0.01586563,0.07499599,0.12416899,0.06629065,-0.04271646,-0.04032154,0.05311558,-0.01049953,0.01483734,-0.07866446,-0.01800532,-0.01576105,0.05598938,-0.01498349,0.00784246,-0.04062972,-0.02855649,0.04306,-0.05145464,0.14029327,0.03415237,-0.00328003,-0.05038279,0.03936462,-0.00341762,0.03000821,0.03632111,-0.00561972,0.04310324,-0.04388968,-0.0051431,-0.07400911,-0.04004771,-0.06132615,0.02271592,-0.01232195,-0.08347897,-0.02437364,0.03493564,-0.01229841,0.02239731,-0.01937352,0.03575079,0.05532587,-0.05930887,0.03089384,0.0340085,0.07746554,-0.05010661,-0.06538808,-0.01705855,0.00375971,0.04814619,-0.01243004,-0.04248329,0.04201822,-0.04137447,0.02140027,0.03184822,0.00135513,-0.00196891,0.02939009,0.00523122,-0.01610989,0.0891251,-0.01592414,-0.01547817,-0.05471997,0.06170771,0.03123805,-0.03846214,0.04145498,0.02390596,0.05297959,-0.01300917,0.0303908,0.04467709,0.07012066,-0.00330333,0.08407186,0.04934964,0.00221251,0.01253894,0.00917662,0.03241397,-0.0566347,0.01585129,0.01588188,0.02180262,-0.25011984,0.02561475,-0.06105273,0.0317834,0.00627877,-0.03720782,-0.00593707,0.01013222,0.03414204,0.00207843,0.0544399,0.05432598,0.05875658,-0.0728241,-0.04784182,0.00675584,-0.02262231,-0.02523871,0.05644502,-0.00996558,0.07641914,0.02927038,0.2347751,-0.01352206,-0.01833909,0.01736786,0.01333107,-0.02834538,-0.0556284,0.03238621,-0.00647612,0.02590081,0.07457381,0.0280531,-0.02338754,0.04471849,-0.02348454,0.06888214,-0.02787763,-0.02059731,-0.0478384,-0.01225474,-0.05516101,0.00746667,0.09834393,-0.01927588,-0.03493662,-0.06828495,0.03911098,0.06959592,-0.06828545,-0.04874941,-0.03453678,-0.00115853,-0.02355118,0.02788924,-0.0305796,0.00771924,-0.01059807,-0.00278811,-0.01203348,0.00985825,0.07475115,-0.00003868,0.01238421],"last_embed":{"hash":"1ghzv8u","tokens":172}}},"text":null,"length":0,"last_read":{"hash":"1ghzv8u","at":1753423462371},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{20}","lines":[86,87],"size":473,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{21}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08898942,-0.05013995,0.00444788,-0.00104563,-0.0146664,0.06041013,0.06610301,-0.007492,0.08123007,0.02772688,0.04521785,0.00362075,0.03811285,-0.0106143,-0.00582923,-0.04044677,-0.03684182,-0.01379434,-0.09485509,0.0410698,0.03710677,-0.05908606,-0.01563064,-0.10051396,0.08872598,-0.01793014,-0.0176681,-0.05825511,-0.08135122,-0.22190411,0.00715461,0.0194488,-0.02075138,-0.05940036,-0.112102,-0.06478322,-0.03137627,0.05794236,-0.03181183,0.05877361,0.03898303,0.02280173,-0.02750763,-0.04587157,-0.04488558,-0.03491685,0.00154416,-0.0000089,0.00588459,0.05134797,-0.04428004,0.02454374,0.00895699,0.01076153,0.0830716,-0.00102938,0.03195693,0.0468147,-0.00547818,0.02200513,0.03386139,0.05817399,-0.17258109,0.03007239,0.01316453,-0.00752218,0.00139441,0.00944418,-0.01088513,0.06872933,0.0499753,0.04494264,-0.02007018,0.04681524,0.06629873,-0.05483431,-0.01577162,-0.06710062,-0.01020331,0.02452005,-0.0316214,-0.04029289,-0.03189968,-0.03376639,-0.00289948,-0.01406843,0.03614205,0.00238244,0.02097116,-0.03538769,0.06159123,0.00579191,-0.0392609,0.02896278,0.06115169,0.04612785,0.03491005,-0.05252131,0.0064071,0.08807386,0.02292019,0.00144454,-0.02264995,0.00517918,0.069585,-0.04587037,-0.03776713,0.00114045,-0.01433846,0.00957769,0.01348966,0.01631931,0.05208999,0.00174619,-0.0461529,0.01182876,0.05376472,-0.00829976,0.04243244,-0.02748987,-0.00386251,0.04848392,0.02708926,-0.04475391,-0.03899336,-0.00531436,0.04800208,0.09472543,0.02704594,0.01924738,0.03019003,-0.07540134,-0.10187577,-0.03015197,0.00031814,-0.00990666,0.01148999,0.0226419,-0.00036735,0.01122705,-0.01630025,-0.0529764,0.03522081,-0.08873712,-0.06306301,0.04939587,-0.00052624,0.05889479,0.03774001,-0.07163189,-0.01131747,0.00058452,-0.07848161,-0.05705558,-0.03412424,0.01217926,0.01774082,0.0730195,-0.01580776,-0.03177227,-0.04797538,-0.04047429,-0.02258825,0.08676241,-0.03406849,-0.05020085,-0.0708251,-0.00199796,-0.05706047,-0.04019029,0.0100739,-0.03187235,-0.09891603,0.07040759,0.09851209,-0.00999953,-0.10894652,-0.0733198,-0.04362973,-0.01156326,0.01302804,-0.02774696,-0.05428032,-0.03844777,-0.01784943,-0.09092002,0.00218704,-0.01032486,0.00765563,0.02944148,-0.03185048,0.02391291,-0.07721671,0.01442742,0.03366244,-0.00539298,-0.00975046,-0.02809823,0.06921981,-0.00335093,-0.04323672,-0.02062219,0.00935833,0.02574924,-0.04000419,0.017339,0.00975228,-0.01893726,0.11608122,0.0260087,-0.0333444,-0.04430873,0.02519095,0.03915894,0.02423078,0.06709902,0.03151287,0.01005423,0.03651692,-0.00055363,0.01057577,0.01389692,-0.01552615,-0.18157665,-0.02622934,-0.04309391,-0.00196768,0.02776127,-0.05115606,0.00973434,-0.03679791,0.01914015,0.08564956,0.06221137,-0.11308395,-0.01260508,0.0845224,-0.01221114,0.0201745,-0.08107115,-0.00960799,-0.05075839,0.07821197,0.0121357,0.06241999,-0.06755601,-0.04458695,0.03582262,-0.03081527,0.13596991,0.00323639,-0.00213316,-0.01342891,0.05505493,0.07913441,0.0250854,0.01144369,-0.00362131,0.03763136,-0.03366547,-0.04606893,-0.08169128,-0.03854552,-0.05476265,0.06739239,-0.00775252,-0.10104784,-0.05861937,0.03906466,-0.02982005,0.05983849,-0.02646289,0.05864421,0.09578153,-0.01643186,0.05626001,0.03306296,0.07598132,-0.04139586,-0.05223897,0.01435768,-0.00297126,0.08168939,0.03359627,-0.031368,0.04276685,-0.01228718,0.03220098,0.00243868,0.01050211,-0.02166105,0.00670682,-0.00570936,-0.01594665,0.04934692,0.00403035,0.03682366,-0.03462509,0.0520304,0.07171407,-0.01999266,0.01218068,0.0088974,0.08139289,-0.05995218,0.01181034,0.0523908,0.03939824,-0.02038053,0.05109243,0.04170892,0.00532757,0.00682799,-0.02596278,0.02546003,-0.06081272,0.00342206,-0.04014504,0.03660705,-0.24447748,0.01719536,-0.01722657,0.04325503,-0.00079507,-0.0601578,0.00614225,0.01175892,0.01476363,-0.03478636,0.08481864,0.0813467,0.0191568,-0.04722329,0.01955154,-0.05213084,-0.04292712,0.00406539,0.07656818,0.00273079,0.11235634,0.05330657,0.24101721,-0.02752858,0.01524523,-0.00771813,0.00229613,-0.01043218,-0.02079001,0.01256688,-0.03156975,0.04974243,0.09111176,-0.0122164,-0.00661708,0.03244423,-0.03548917,0.04634958,-0.04486207,-0.00071535,-0.02174955,-0.00068056,-0.04537008,0.03801919,0.11704073,-0.00379933,-0.01251027,-0.07906543,0.07622015,0.06648047,-0.05177231,-0.04908581,0.01148429,0.00007338,0.01400225,0.01625269,-0.03115853,0.01636588,0.02179093,0.01501113,0.00300547,0.0383485,0.06857203,0.02762755,0.01877159],"last_embed":{"hash":"nldhex","tokens":465}}},"text":null,"length":0,"last_read":{"hash":"nldhex","at":1753423462439},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#By Ion Saliu, _★ Founder of Lottery Mathematics_#{21}","lines":[88,110],"size":3293,"outlinks":[{"title":"Playing lottery skips below median increases tremendously the chance to win the lotto jackpot.","target":"https://saliu.com/ScreenImgs/lotto-skips.gif","line":1},{"title":"_**gambling strategy offsets losses in minimum-bet situations**_","target":"https://saliu.com/blackjack-strategy-system-win.html","line":13},{"title":"Lotto software creates the best lottery strategies to win the jackpot multiple times.","target":"https://saliu.com/ScreenImgs/mdi-lotto-software.gif","line":17},{"title":"_**Dynamic or Static Filters in Lottery, Lotto Analysis, Mathematics, Software**_","target":"https://saliu.com/bbs/messages/919.html","line":19},{"title":"_**Internet, State Lotteries: Lotto Drawings, Lottery Results, Winning Numbers**_","target":"https://saliu.com/bbs/messages/920.html","line":20}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11988024,-0.02583699,-0.03693296,-0.01577971,-0.02020921,0.03711167,-0.01954832,-0.00509937,0.03802579,0.01510893,0.03274207,-0.00648567,0.08788759,-0.03344696,-0.00168729,-0.04912212,0.02957003,0.0064927,-0.04478239,-0.03146921,0.07889666,-0.01206656,-0.08641174,-0.10721187,0.05288834,-0.00535812,-0.01433367,-0.04769109,-0.05401761,-0.21104823,0.02048739,-0.00382486,0.00249475,-0.07241839,-0.0875206,-0.0190386,-0.01495725,0.03922974,-0.0651309,0.03330986,0.01437927,0.00466906,0.00328644,-0.01547782,0.00798407,-0.0405357,0.02825566,-0.00357122,0.0623728,0.00698599,-0.0907281,0.01054905,0.02471483,0.021493,0.06863362,0.0177686,0.04254489,0.10330486,0.01259669,0.03390355,0.02049857,0.0618007,-0.19435169,0.0743706,-0.00454523,0.02754509,0.01677469,-0.02325272,0.00565245,0.032082,0.01787351,0.04384479,-0.01516325,0.09076162,0.04273901,-0.03695964,-0.04948489,-0.06756788,-0.04287727,0.03661387,-0.04305321,-0.01277625,0.01376664,-0.00765951,0.00230593,0.04086819,0.03620379,0.01273931,0.08099323,-0.05897238,0.01710868,-0.0119608,0.02677288,0.04244721,0.01731671,0.02492276,0.05928377,-0.04250643,0.01761295,0.11361822,0.02949241,0.00407583,0.01757292,-0.00564679,0.05082697,-0.07833141,-0.00047334,-0.01617088,-0.01392853,0.05488249,0.04016155,-0.01734682,0.01254141,-0.02439065,-0.03764049,0.00031597,-0.00257773,-0.01105993,0.02772738,-0.00742853,-0.0634313,-0.01041702,0.01126407,0.00086128,0.02165272,0.01974927,0.02308311,0.07547098,0.00192145,0.0306494,0.0486194,0.02790669,-0.15171814,-0.03833312,-0.00458077,-0.00634838,0.00127418,-0.03136492,-0.00935727,0.02600905,-0.03979647,-0.04677988,0.01604174,-0.1068369,-0.04437358,0.08316091,-0.01380791,0.00817269,-0.01632826,-0.06119902,-0.00183013,-0.01836551,-0.03084875,-0.04950235,0.00682412,0.02032327,0.08478034,0.07024077,-0.04380773,-0.01403138,-0.03507243,-0.0270037,-0.04907633,0.13461386,-0.00650464,-0.09434979,-0.02963098,0.0389826,-0.04114708,-0.05234477,-0.03349154,-0.00359905,-0.0870829,0.03598278,0.1129774,-0.02421819,-0.07176641,-0.04012104,-0.04592564,-0.03694671,-0.011,-0.03520942,-0.04147169,-0.01267995,-0.02894325,-0.10684382,0.0082033,-0.04363099,0.01441016,0.06210436,-0.01802654,0.00072968,-0.05620424,-0.00742636,-0.04265393,-0.02280293,-0.0376766,-0.0487666,0.06740586,-0.03888894,0.01061695,0.01703913,0.01596833,0.02311149,-0.02212003,0.0533257,-0.0482742,-0.05162683,0.11015669,0.02832959,-0.05140921,-0.00085162,0.07190675,0.05617177,-0.00384148,0.05255331,0.01914883,0.01044225,-0.0146975,0.02335005,0.01475889,0.02856721,-0.08984309,-0.18882944,-0.02449721,-0.03415909,0.01889778,-0.00137725,-0.03225512,0.03478606,-0.03500494,0.02367489,0.11859982,0.10933807,-0.07200024,0.00260159,0.07892632,0.00696844,0.01140311,-0.07996574,-0.02280338,-0.0589307,0.04102252,0.04812653,0.00024892,-0.00178246,-0.05813264,0.01542884,-0.02448854,0.12600023,-0.02048673,0.01713405,0.01752175,0.08690441,0.00003184,0.00400182,-0.03802346,-0.01715506,0.06074898,-0.04155933,0.00463967,-0.03126078,-0.01158934,-0.06485382,0.01193495,-0.00256108,-0.08726923,-0.02997003,0.02754917,0.00039125,0.02347785,-0.01263928,0.03511236,0.02577276,-0.01477952,0.05135157,0.02446394,0.04584158,-0.02332049,-0.0721069,-0.01575815,-0.03744216,0.05442627,-0.02538306,-0.0681954,0.03807197,-0.02979825,0.04489597,-0.00315719,0.00424221,0.00166708,0.00745876,0.01099142,-0.0230575,0.08636808,-0.00924132,0.05101035,-0.01862352,0.06457004,0.07007419,-0.02919079,0.01606043,-0.00670432,-0.00709397,-0.04108756,0.04965964,0.06658763,0.0838487,0.03114565,0.08234696,0.02895004,0.01031569,0.00010576,-0.00092112,-0.00695237,-0.03664588,0.03477058,0.03081481,0.04121648,-0.25306386,0.01406069,0.00502012,0.04141029,0.00931721,-0.06799705,0.00253427,-0.03973461,0.05179053,-0.00585366,0.05726545,0.01995358,0.03840921,-0.06134489,-0.021801,-0.00390131,-0.00297884,-0.00727137,0.08084007,0.03188416,0.02378494,0.01934346,0.25241777,-0.00621802,-0.00146615,0.02188203,-0.00618832,-0.01730254,-0.02336227,0.03755943,0.02380294,0.0499151,0.08699428,0.01776502,-0.05725785,0.02219147,-0.04411068,0.04960022,-0.00160813,0.01539398,-0.05374132,0.0283885,-0.04350104,-0.0078789,0.09264769,0.00286852,-0.02914952,-0.10521778,0.06682262,0.07310306,-0.07388394,-0.01801777,-0.05328484,-0.00013927,0.02059725,0.02019516,0.00867993,0.00631967,0.00081517,0.00739311,0.04640422,-0.00781909,0.05088082,0.02635214,0.02101003],"last_embed":{"hash":"efracm","tokens":426}}},"text":null,"length":0,"last_read":{"hash":"efracm","at":1753423462635},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>","lines":[111,138],"size":3249,"outlinks":[{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":5},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":7},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":9},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":11},{"title":"_**<u>Skip System</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":12},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":13},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":14},{"title":"_**Lotto Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":15},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":16},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":17},{"title":"_**A Practical Essay on Artificial Intelligence, AI Chatbots Regarding Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":18},{"title":"_**Lotto wheels**_","target":"https://saliu.com/lotto_wheels.html","line":19},{"title":"**software**","target":"https://saliu.com/infodown.html","line":21},{"title":"Improve lotto odds my a great margin using skip, frequency, pairing strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":23},{"title":"Forums","target":"https://forums.saliu.com/","line":25},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":25},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":25},{"title":"Contents","target":"https://saliu.com/content/index.html","line":25},{"title":"Home","target":"https://saliu.com/index.htm","line":25},{"title":"Software","target":"https://saliu.com/infodown.html","line":25},{"title":"Search","target":"https://saliu.com/Search.htm","line":25},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":25},{"title":"A study on filtering, median, pairs in lotto, lottery software, systems, strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":27}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11609862,-0.03099238,-0.03839023,-0.01897934,-0.01980855,0.03734619,-0.02458038,-0.00422356,0.03805685,0.01766217,0.03149829,-0.00391366,0.08666625,-0.03130914,-0.0028524,-0.04830949,0.02879267,0.00688699,-0.04528728,-0.03029953,0.07857935,-0.01051414,-0.08687261,-0.10683039,0.0532975,-0.00314678,-0.01472969,-0.04875854,-0.05484286,-0.21014425,0.02356693,-0.00489646,0.00086008,-0.07186007,-0.08870351,-0.01987216,-0.01329973,0.04273237,-0.06829945,0.03426929,0.01494225,0.00529784,0.00411163,-0.01556281,0.00355889,-0.03858979,0.0310983,-0.00119188,0.06074972,0.00643322,-0.08701908,0.01218169,0.02500788,0.01997908,0.06683429,0.020107,0.0431103,0.10488123,0.01106824,0.03161271,0.01901834,0.06379711,-0.19777589,0.07590535,-0.00696721,0.03038245,0.01580882,-0.01871081,0.00629779,0.03166857,0.01572837,0.04454051,-0.01046961,0.090533,0.04275059,-0.03164934,-0.04993227,-0.06470942,-0.04293121,0.03868347,-0.04646751,-0.01029945,0.01597468,-0.00461175,0.0012397,0.04167727,0.03418304,0.01517225,0.08538036,-0.05878993,0.01409885,-0.01272967,0.02640514,0.04403397,0.01498366,0.0245279,0.0574459,-0.04025204,0.01763638,0.11008187,0.03029827,0.00764792,0.01737762,-0.00981401,0.05272548,-0.0751195,-0.00461913,-0.01644354,-0.0154994,0.0534515,0.04024123,-0.01672566,0.00962304,-0.0257881,-0.03467811,-0.00308299,-0.00509791,-0.01120907,0.0272123,-0.00873876,-0.06375633,-0.01057226,0.01239818,0.0005381,0.02030501,0.01630078,0.02442657,0.07643715,0.00143001,0.03015502,0.05210877,0.02873072,-0.15371919,-0.03917484,-0.00379452,-0.00914222,-0.00146678,-0.03160424,-0.01012028,0.025914,-0.03870387,-0.04707076,0.01803428,-0.10817078,-0.04671258,0.08520843,-0.01381657,0.00654141,-0.01687664,-0.05986587,-0.00124499,-0.01685962,-0.02757373,-0.04928038,0.0051566,0.02058866,0.0844852,0.06938565,-0.04501125,-0.01368376,-0.03818387,-0.02752969,-0.04654794,0.13655725,-0.00830393,-0.09802587,-0.03063217,0.04068104,-0.03850697,-0.05485571,-0.03241837,-0.00255963,-0.08414537,0.03689463,0.11081297,-0.02293707,-0.07168676,-0.03785786,-0.04531495,-0.03620248,-0.00942008,-0.03571467,-0.03733231,-0.01142081,-0.03005289,-0.10343085,0.00779529,-0.04327589,0.01467703,0.06191925,-0.01899549,0.00346232,-0.05654432,-0.00962546,-0.04516139,-0.02363004,-0.03871113,-0.04932781,0.06702394,-0.03094626,0.01285973,0.01651838,0.01439415,0.02076014,-0.018439,0.05245171,-0.04562911,-0.05352445,0.1102727,0.03080472,-0.05365015,-0.00008591,0.06830408,0.05574843,-0.00421635,0.05215687,0.02034799,0.00691727,-0.01554083,0.02264968,0.01390839,0.02954752,-0.0924609,-0.1847112,-0.02642123,-0.03218704,0.01881908,-0.00190272,-0.03059157,0.03072504,-0.03675289,0.02522125,0.11536042,0.10974727,-0.06996559,0.00596496,0.07860059,0.00770266,0.0082198,-0.07806759,-0.02484306,-0.06028828,0.04088616,0.04700323,0.00130131,-0.00060596,-0.0574328,0.01824697,-0.02622891,0.12617174,-0.02044985,0.01680404,0.01648347,0.08507053,0.00215734,0.00436529,-0.03583467,-0.01786468,0.06112979,-0.04031869,0.00115027,-0.02969887,-0.00820813,-0.06641288,0.01156981,-0.00110111,-0.08704655,-0.03334231,0.02287704,0.00037404,0.0222827,-0.01223673,0.03772138,0.02446512,-0.01227007,0.05423392,0.02181135,0.0481008,-0.02011705,-0.07111675,-0.01773761,-0.03480104,0.05577077,-0.02476874,-0.07010852,0.04035438,-0.02941574,0.04759665,-0.00332244,-0.0019503,-0.00152445,0.00425946,0.00974762,-0.02488975,0.08460081,-0.01224373,0.05207285,-0.01863429,0.06262461,0.07158483,-0.03059074,0.01297418,-0.00679842,-0.00705982,-0.04063722,0.051641,0.06774425,0.08055075,0.03419023,0.0816884,0.03274826,0.01041331,-0.00154325,-0.00148547,-0.00832933,-0.03636589,0.03608365,0.03018791,0.04093594,-0.25613526,0.01701603,0.00292131,0.04128125,0.00750418,-0.06887791,0.00601442,-0.03788356,0.05051214,-0.0103991,0.05688437,0.01744768,0.03599809,-0.05738654,-0.02035653,-0.0042938,-0.00054955,-0.00872875,0.08304848,0.03176276,0.0242406,0.0218783,0.25387961,-0.00708974,-6.1e-7,0.02727322,-0.00485861,-0.0156998,-0.02090616,0.03753274,0.02521656,0.04857089,0.08331189,0.02018755,-0.05851681,0.02320228,-0.04561002,0.04744767,-0.00470852,0.01423426,-0.05683341,0.02803805,-0.03993895,-0.00549517,0.09619968,0.00324118,-0.02739136,-0.10860422,0.06619135,0.07345816,-0.07344949,-0.01844403,-0.05599665,-0.00502766,0.01835676,0.0215271,0.00834169,0.00689214,0.00192821,0.00631793,0.04236111,-0.01085149,0.04963471,0.02722538,0.01982851],"last_embed":{"hash":"f3mf8y","tokens":427}}},"text":null,"length":0,"last_read":{"hash":"f3mf8y","at":1753423462801},"key":"notes/saliu/Filters in Lottery, Lotto Jackpot, Skip Median, High Skips.md#Filters in Lottery, Lotto Jackpot, Skip Median, High Skips#<u>Resources in Lottery Software, Strategies, Lotto Systems</u>#{1}","lines":[113,138],"size":3173,"outlinks":[{"title":"_**Lotto, Lottery, Software, Strategy**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_","target":"https://saliu.com/Newsgroups.htm","line":5},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":7},{"title":"_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_","target":"https://saliu.com/forum/lotto-book.html","line":9},{"title":"_**<u>Skip System</u> Software**_","target":"https://saliu.com/skip-strategy.html","line":10},{"title":"_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_","target":"https://saliu.com/lotto-skips.html","line":11},{"title":"_**Lottery Utility Software**_","target":"https://saliu.com/lottery-utility.html","line":12},{"title":"_**Lotto Filters, Reduction Strategies in Lottery Software**_","target":"https://saliu.com/filters.html","line":13},{"title":"_**Play a Lotto Strategy, Lottery Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":14},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":15},{"title":"_**A Practical Essay on Artificial Intelligence, AI Chatbots Regarding Ion Saliu**_","target":"https://saliu.com/ai-chatbots-ion-saliu.html","line":16},{"title":"_**Lotto wheels**_","target":"https://saliu.com/lotto_wheels.html","line":17},{"title":"**software**","target":"https://saliu.com/infodown.html","line":19},{"title":"Improve lotto odds my a great margin using skip, frequency, pairing strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":21},{"title":"Forums","target":"https://forums.saliu.com/","line":23},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":23},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":23},{"title":"Contents","target":"https://saliu.com/content/index.html","line":23},{"title":"Home","target":"https://saliu.com/index.htm","line":23},{"title":"Software","target":"https://saliu.com/infodown.html","line":23},{"title":"Search","target":"https://saliu.com/Search.htm","line":23},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":23},{"title":"A study on filtering, median, pairs in lotto, lottery software, systems, strategies.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":25}],"class_name":"SmartBlock"},
