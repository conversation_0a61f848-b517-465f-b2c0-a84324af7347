"smart_sources:在ZWCAD中使用LISP程式語言以加速繪圖工作.md": {"path":"在ZWCAD中使用LISP程式語言以加速繪圖工作.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04921554,0.04819999,-0.02033845,-0.02461994,-0.02847168,0.00817513,-0.04195809,-0.00809804,0.03156462,0.01318831,-0.00564133,-0.03830247,0.04580265,0.04744476,0.0696076,-0.00684368,0.00020506,0.00834723,-0.04629729,-0.02459853,0.09345412,-0.03392263,0.05062532,-0.05803549,0.06615489,0.03272698,-0.02118832,-0.09338478,0.05181061,-0.17428225,-0.00079901,-0.02068605,0.017251,0.03920055,0.00181897,-0.04835411,-0.045286,0.07178498,-0.0550509,-0.00785071,0.0026497,0.05612507,0.01920607,-0.00032531,0.02724253,-0.10468633,-0.06323117,-0.03465289,-0.03152187,0.00563749,-0.06459124,-0.01955815,0.0216346,0.02793557,-0.02255882,0.05870435,0.02585002,0.04127467,-0.0148285,0.02352796,0.01594363,0.05339322,-0.23186947,0.07281572,0.00868607,-0.02886989,-0.02646298,-0.00684135,0.06033434,0.04184805,-0.05940552,0.03238743,0.0186985,0.00459862,-0.00920054,-0.04886442,0.00414866,-0.0729477,-0.00690242,-0.05375734,0.01845365,0.07315692,0.03552864,0.00763461,-0.00337465,0.00284229,0.02084232,-0.06641211,0.06309378,-0.03718029,0.00521165,-0.04678014,-0.07623714,0.07403552,-0.05446361,-0.00349066,-0.01437826,0.01874898,-0.06425006,0.1235408,-0.03938543,-0.00936941,-0.01680773,-0.02893165,0.01326516,0.00535112,0.07548196,-0.00666995,-0.04629147,-0.0165598,-0.02917117,-0.04482933,0.05154425,-0.03470002,0.03964486,0.02153582,0.02143987,0.00197665,0.02018708,0.06378132,0.04277736,0.01711065,0.0185718,-0.05457073,0.00376942,0.01049604,0.07031783,0.08924361,0.01531561,0.00269251,0.04052272,0.07359369,-0.01161396,0.0107422,-0.05650985,0.03348271,-0.00971932,0.0143232,0.00399839,0.00186756,0.00801453,-0.03529627,-0.05878934,-0.06479207,-0.01046452,0.04490455,-0.07425834,0.02316299,-0.00057435,-0.04632623,0.06023298,0.03166719,-0.0152908,-0.04291597,0.04847995,-0.00195692,0.08128247,0.13130291,-0.0768545,-0.00015501,0.00429615,-0.02455644,-0.05075692,0.12372315,0.00428425,-0.0689818,-0.0279715,0.01658359,0.00209829,-0.04825011,-0.03406062,0.01957734,-0.01785682,-0.00831695,0.09590497,0.01195557,-0.06889018,-0.04211799,0.03255895,0.06419165,0.04858357,0.00321644,-0.02191753,0.008468,0.00219359,-0.07407676,0.0110768,-0.03384711,0.00271778,0.02074988,-0.13966259,0.02425192,0.04183142,0.01015768,-0.01409219,-0.02489012,-0.00224886,-0.07790724,0.0178529,-0.05512727,0.08207136,0.04372334,0.00250725,0.00185162,0.01577554,-0.0486591,0.02612867,-0.03335968,-0.00451788,0.05942729,-0.02430991,0.03696273,0.03945132,-0.01299622,0.05194683,-0.01859661,-0.01993025,0.06397647,-0.0396887,0.02553789,0.04063046,-0.03006777,-0.10574137,-0.24367835,-0.06362396,0.06554511,-0.01828777,0.05587457,-0.05240044,0.00321705,-0.01600468,0.03205651,0.1077636,0.09345976,0.02545581,-0.021963,0.01845877,-0.04340284,-0.03305125,0.07317743,0.00875251,-0.05561844,0.03652082,0.02370167,-0.03120099,0.01161472,-0.02503839,0.01191392,-0.02255997,0.08542953,-0.01350769,0.08895627,0.04291796,0.05234805,0.04209637,0.00036619,-0.08213326,0.03813276,0.07106707,-0.06210318,-0.02035275,0.00780325,-0.03050627,0.0267603,0.0843902,-0.02581331,-0.14768235,-0.00792931,-0.02820929,-0.06218155,-0.04849705,-0.03771453,0.04883911,-0.00488609,0.00384234,-0.00361922,0.05210431,0.01686064,-0.05730775,-0.03979023,-0.0347579,-0.02202257,-0.03615465,-0.04293035,0.02081538,-0.00308088,-0.0693344,-0.00752731,0.01815674,-0.00536623,0.02219167,0.03905148,0.01425018,-0.04642673,0.16556753,0.00141286,-0.02742844,0.02477259,0.0358642,-0.03211598,-0.05850114,0.04479818,-0.04200431,0.06704447,0.0325898,0.03851411,0.02736079,0.07508366,0.03016501,0.04035146,-0.04348778,0.01588584,0.00671603,-0.05198867,-0.00713268,0.01693304,-0.01273734,0.05487614,-0.00820302,-0.27361068,0.04543613,0.05601664,-0.06027745,-0.01086505,0.02486591,0.00837612,-0.02934512,-0.04747241,0.0450623,-0.07844786,0.04502813,0.03503446,-0.02690487,0.0696826,-0.05144345,0.07125832,-0.01276672,-0.01518474,0.01783496,0.00966742,0.01473412,0.18504104,-0.00206324,0.06478543,0.00720508,0.01352856,-0.02429109,0.05735467,0.02766668,-0.03371029,0.0169358,0.04157217,-0.01199605,-0.02881045,0.0573067,0.00845125,-0.00294303,0.0035631,0.00572635,-0.00008744,-0.01058248,-0.05703864,0.03117217,0.08331892,0.0127109,0.01001151,-0.05745351,-0.03466409,0.00900487,-0.03907366,0.00593062,-0.00260344,0.00561589,-0.04140909,0.03231391,0.0219071,-0.02036455,-0.01653766,-0.06443401,0.03047089,-0.0328886,0.00185278,0.04717721,0.04493747],"last_embed":{"hash":"e3713b3054980c45599e3f28c7f1b075061d24380e8400f85002a6dde7447b9f","tokens":467}}},"last_read":{"hash":"e3713b3054980c45599e3f28c7f1b075061d24380e8400f85002a6dde7447b9f","at":1745995165078},"class_name":"SmartSource2","outlinks":[],"blocks":{"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告":[1,237],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#1. ZWCAD中使用LISP程式語言加速繪圖工作：方法與技巧":[3,8],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#1. ZWCAD中使用LISP程式語言加速繪圖工作：方法與技巧#{1}":[5,8],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#2. 搜尋ZWCAD LISP相關資訊以提高效率":[9,16],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#2. 搜尋ZWCAD LISP相關資訊以提高效率#{1}":[11,16],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#3. ZWCAD常用的LISP指令與函數":[17,49],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#3. ZWCAD常用的LISP指令與函數#{1}":[19,49],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#4. 編寫與載入自訂LISP程式至ZWCAD":[50,66],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#4. 編寫與載入自訂LISP程式至ZWCAD#{1}":[52,57],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#4. 編寫與載入自訂LISP程式至ZWCAD#{2}":[58,59],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#4. 編寫與載入自訂LISP程式至ZWCAD#{3}":[60,61],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#4. 編寫與載入自訂LISP程式至ZWCAD#{4}":[62,64],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#4. 編寫與載入自訂LISP程式至ZWCAD#{5}":[65,66],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件":[67,132],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{1}":[69,72],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{2}":[73,74],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{3}":[75,76],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{4}":[77,78],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{5}":[79,80],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{6}":[81,82],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{7}":[83,85],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#5. 使用LISP腳本創建與修改圖形物件#{8}":[86,132],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#6. 利用LISP進行批次處理與自動化工作流程":[133,146],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#6. 利用LISP進行批次處理與自動化工作流程#{1}":[135,146],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#7. 使用LISP從ZWCAD圖檔提取數據並生成中文報告":[147,191],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#7. 使用LISP從ZWCAD圖檔提取數據並生成中文報告#{1}":[149,191],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#8. ZWCAD LISP程式設計的相關教學資源與社群":[192,207],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#8. ZWCAD LISP程式設計的相關教學資源與社群#{1}":[194,207],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#9. 台中市神岡區ZWCAD LISP應用相關資源與使用者群體":[208,223],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#9. 台中市神岡區ZWCAD LISP應用相關資源與使用者群體#{1}":[210,223],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議":[224,237],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{1}":[226,229],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{2}":[230,230],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{3}":[231,231],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{4}":[232,232],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{5}":[233,233],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{6}":[234,234],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{7}":[235,236],"#在ZWCAD中使用LISP程式語言以加速繪圖工作：一份專家報告#10. 結論與建議#{8}":[237,237]},"last_import":{"mtime":1743065677288,"size":30069,"at":1743120565732,"hash":"e3713b3054980c45599e3f28c7f1b075061d24380e8400f85002a6dde7447b9f"},"key":"在ZWCAD中使用LISP程式語言以加速繪圖工作.md"},