"smart_sources:PromptingTools.jl/Text Utilities - PromptingTools.jl.md": {"path":"PromptingTools.jl/Text Utilities - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12018678,0.01061913,-0.02102408,-0.0452897,-0.03260273,-0.01695476,-0.04844176,0.06167347,0.04052635,-0.01654348,-0.02170311,0.00296297,-0.01170846,0.03046896,-0.00573073,0.0150523,-0.03842866,0.00058109,-0.09859627,-0.01533944,0.09031436,0.0531274,0.01455006,-0.03481365,0.02287747,0.06063697,0.0049111,-0.03612851,-0.00872488,-0.18809682,0.06419869,-0.01439547,0.02990611,-0.01973825,-0.05240813,-0.00708938,-0.05026828,0.04705865,-0.02087678,-0.0163022,0.01922256,0.00016775,-0.03551465,-0.02284624,-0.05295342,-0.11937834,-0.02999369,-0.04255448,0.00813024,-0.05839757,0.00648002,-0.00597702,0.05781688,-0.02006839,0.02463807,0.00230183,0.00240472,0.08185124,0.03196747,0.00707823,0.02905075,0.00929037,-0.19480093,0.17342621,0.01929225,0.02905139,-0.02175602,0.01193981,0.0278721,0.02418642,-0.02337497,0.02307982,0.00035967,0.06768402,0.0085446,-0.00653474,-0.03005402,-0.0203519,-0.00463282,-0.09385334,-0.0133222,-0.0146044,-0.01237514,0.00573574,-0.02828317,-0.01131512,-0.01448807,-0.00860087,0.04681548,0.00242303,0.00448778,-0.07801799,-0.00068601,0.03008286,-0.06017386,-0.02271974,0.00851421,-0.04081939,-0.0901252,0.13130577,-0.01258237,-0.00240967,0.01799935,-0.03150794,0.04565002,-0.06531036,0.0076316,-0.01469511,-0.02548126,0.02661353,-0.01133147,-0.03586119,-0.01485952,-0.06787006,-0.02103652,-0.02078021,0.00122197,-0.01884792,-0.00496217,-0.03494611,0.01180269,0.01737743,-0.00713278,0.0057179,0.02302369,0.03804278,0.04617025,0.04132996,0.00117818,0.02061926,0.06174734,0.00562061,-0.06084301,-0.01818022,-0.03984599,0.06308775,0.03384608,-0.06408712,0.03708324,-0.01343698,0.03981457,-0.00407658,-0.00630572,-0.09629749,0.00575584,0.04168252,-0.02494307,-0.01214324,-0.02509358,-0.04308958,0.03453021,0.04023763,-0.04276943,0.00400378,0.06832348,0.04383802,0.08490521,0.06346215,-0.01586046,0.02205163,-0.05277483,-0.01702098,-0.01427731,0.15705013,0.0240653,-0.05515401,-0.07513942,-0.0346687,-0.05297344,-0.03171165,0.02463647,0.03748885,-0.0580648,0.016478,0.05838182,0.03901751,-0.04895758,0.0220677,0.00112666,0.02014118,0.02620208,-0.05005365,-0.04634907,0.02207703,-0.00722576,-0.02307193,-0.01014169,-0.09366168,0.00077919,0.05222623,-0.05831676,0.02139016,0.05239803,-0.03530232,-0.00959436,-0.00989097,0.0029261,-0.0903011,0.04938192,-0.05670108,0.0640358,0.03860561,0.04160598,0.00544711,0.00127363,0.03010794,0.05333956,-0.00172288,0.09496873,0.00729869,-0.09797779,-0.05630673,0.08905797,-0.0116154,-0.02077366,-0.03748317,-0.04243082,0.03111142,0.00621297,0.06914943,-0.04216412,0.00069077,-0.04380097,-0.21738946,0.01413427,0.03251294,-0.06817894,0.05403204,-0.07735453,0.08513941,-0.04435546,-0.02934724,0.06064801,0.09903706,-0.03384963,0.03853199,-0.02094081,0.0066984,-0.00784696,-0.01508605,-0.00179038,0.0086271,0.04122363,0.01770552,-0.00046628,0.04462339,-0.06729637,-0.00407341,-0.0312375,0.10446073,0.04799925,0.05725713,-0.09742676,0.02338965,0.00482025,0.05940988,-0.0995641,0.00320485,0.00595456,-0.03732015,0.06569161,0.07897768,0.01317257,-0.03909645,0.04816751,-0.02606232,-0.04305685,-0.00781396,-0.03228856,-0.04693078,-0.04987542,-0.04720177,0.0199482,0.02964965,-0.04597519,0.015411,0.01550714,0.03856213,-0.00862396,-0.03731933,-0.04312608,-0.00290526,-0.01865772,0.03890972,-0.00824784,-0.01374354,-0.03524289,0.0400507,0.05610141,0.01521108,0.01000283,0.05777756,0.01430365,-0.03499337,0.10199778,0.02719704,0.08913017,-0.0235599,0.07623843,-0.00322405,-0.05251187,-0.00396082,-0.05291343,-0.04081665,0.05594111,0.02674126,0.01562399,0.01907762,0.05023297,0.02321548,-0.04732624,0.10006179,-0.05003235,-0.02095413,0.02380562,0.02302377,-0.02550514,0.04533323,0.0142082,-0.23389813,0.03588438,0.01923688,-0.0103997,-0.0171538,0.08370757,0.03666715,-0.00565291,-0.0060357,0.06933449,-0.06338646,0.0122413,-0.01439701,-0.04482237,0.05576113,0.04266452,0.04325058,0.00701027,0.00488597,-0.08325256,0.03705551,0.00865394,0.22420412,-0.03202688,0.0093024,0.01325637,0.00753022,-0.06459225,0.10392351,0.03817352,0.03556413,0.07059207,0.17253989,-0.00832056,0.05350114,-0.00293337,-0.02590676,-0.02534466,0.0183844,0.01298044,-0.01945264,0.05017328,-0.08098939,-0.03239969,0.03605987,-0.00319133,-0.00454263,-0.08145074,-0.02952489,0.03596418,-0.02501604,0.01633645,-0.03026839,0.00415751,0.0238045,-0.0183197,0.03769076,0.02201864,-0.06865219,-0.01826581,0.04659565,-0.03078344,0.09837177,0.01019497,-0.02161497],"last_embed":{"hash":"f36677f5c91f13a528137e267725948944b2deae62201a291394182f63de0260","tokens":464}}},"last_read":{"hash":"f36677f5c91f13a528137e267725948944b2deae62201a291394182f63de0260","at":1745995225974},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/extra_tools/text_utilities_intro/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"Highlights","target":"#Highlights \"Highlights\"","line":243},{"title":"References","target":"#References \"References\"","line":244},{"title":"​","target":"#Text-Utilities","line":246},{"title":"​","target":"#Highlights","line":250},{"title":"​","target":"#References","line":290},{"title":"#","target":"#PromptingTools.recursive_splitter-extra_tools-text_utilities_intro","line":292},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L34-L71","line":348},{"title":"`RecursiveCharacterTextSplitter`","target":"https://python.langchain.com/docs/modules/data_connection/document_transformers/recursive_text_splitter","line":360},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L119-L182","line":436},{"title":"#","target":"#PromptingTools.replace_words-extra_tools-text_utilities_intro","line":440},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L3-L19","line":469},{"title":"#","target":"#PromptingTools.wrap_string-extra_tools-text_utilities_intro","line":473},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L204-L218","line":495},{"title":"#","target":"#PromptingTools.length_longest_common_subsequence-extra_tools-text_utilities_intro","line":499},{"title":"https://cn.julialang.org/LeetCode.jl/dev/democards/problems/problems/1143.longest-common-subsequence/","target":"https://cn.julialang.org/LeetCode.jl/dev/democards/problems/problems/1143.longest-common-subsequence/","line":509},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L249-L285","line":552},{"title":"source","target":"https://github.com/svilupp/PromptingTools.jl/blob/0a1d1c73fba16b8afceb4c4cff5477596558d87f/src/utils.jl#L298-L340","line":611},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/extra_tools/text_utilities_intro.md","line":615},{"title":"Previous pageBuilding RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":617},{"title":"Next pageAgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":619},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":621},{"title":"Icons8","target":"https://icons8.com","line":621},{"title":"**VitePress**","target":"https://vitepress.dev","line":621}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,245],"##Reference#{1}":[209,242],"##Reference#{2}":[243,243],"##Reference#{3}":[244,245],"#Text Utilities [​](#Text-Utilities)":[246,623],"#Text Utilities [​](#Text-Utilities)#{1}":[248,249],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)":[250,289],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{1}":[252,253],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{2}":[254,255],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{3}":[256,257],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{4}":[258,259],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{5}":[260,261],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{6}":[262,264],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{7}":[265,276],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{8}":[277,278],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{9}":[279,280],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{10}":[281,282],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{11}":[283,284],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{12}":[285,287],"#Text Utilities [​](#Text-Utilities)#Highlights [​](#Highlights)#{13}":[288,289],"#Text Utilities [​](#Text-Utilities)#References [​](#References)":[290,623],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{1}":[292,305],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{2}":[306,307],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{3}":[308,309],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{4}":[310,312],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{5}":[313,318],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{6}":[319,320],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{7}":[321,322],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{8}":[323,325],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{9}":[326,363],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{10}":[364,365],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{11}":[366,367],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{12}":[368,370],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{13}":[371,376],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{14}":[377,378],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{15}":[379,381],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{16}":[382,383],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{17}":[384,385],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{18}":[386,387],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{19}":[388,389],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{20}":[390,391],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{21}":[392,394],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{22}":[395,451],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{23}":[452,453],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{24}":[454,455],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{25}":[456,458],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{26}":[459,512],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{27}":[513,514],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{28}":[515,517],"#Text Utilities [​](#Text-Utilities)#References [​](#References)#{29}":[518,623]},"last_import":{"mtime":1712727416268,"size":18546,"at":1740449882787,"hash":"f36677f5c91f13a528137e267725948944b2deae62201a291394182f63de0260"},"key":"PromptingTools.jl/Text Utilities - PromptingTools.jl.md"},