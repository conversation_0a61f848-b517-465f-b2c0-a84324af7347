
"smart_sources:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md": {"path":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0625053,-0.04751654,0.03530852,-0.02922094,-0.01978631,0.01267759,-0.0442044,-0.05870013,0.02883154,0.02101467,0.02795631,-0.05563723,0.03290548,0.02990764,-0.06112592,-0.0116814,0.01941847,-0.02855564,-0.06324762,0.03940862,0.02340559,-0.03154171,-0.00895348,-0.04592784,0.02539933,0.01073243,-0.02705432,-0.04837482,-0.05750052,-0.24141482,-0.00322233,0.0153456,0.01057957,-0.04240967,-0.02379492,-0.03057385,-0.02417478,-0.00638291,-0.0385575,0.04793234,0.0414364,-0.001083,-0.03711038,-0.0579624,0.03067927,-0.00994093,-0.02602442,0.00479497,0.09165917,0.00842159,-0.07193781,-0.00761374,0.02929276,0.04893625,0.05151861,0.00380184,0.0659186,0.1158539,0.03183414,0.03934547,0.05453659,0.11782542,-0.19183284,0.04186344,0.02230482,-0.01029171,0.00501193,-0.06419307,-0.04429391,0.02048969,-0.02324424,0.02370336,-0.00666881,0.08134406,0.01238562,-0.01134456,-0.02249914,-0.03303118,-0.07298117,0.0345816,-0.04299185,-0.02440986,0.00354568,-0.00824089,0.03140956,0.04824511,0.04590949,0.0671651,0.08374977,-0.05014402,0.04155903,-0.01308119,0.06027634,0.09157942,-0.00820402,0.0368862,0.03319439,0.02437983,-0.01988758,0.10647392,0.0293084,-0.0162284,-0.02088036,0.01594933,0.03054908,-0.02512967,-0.03240762,0.00905776,-0.01449772,0.01000036,0.0486497,-0.00797178,0.08289026,-0.01877577,-0.05412118,0.03529621,-0.00218377,-0.01880873,-0.00710996,0.02584581,-0.0273573,0.05196602,-0.00465972,0.02487108,-0.03466546,-0.02025831,0.07319809,0.04368791,0.00666718,0.02133796,-0.0104829,0.04432483,-0.15937074,-0.0565429,-0.04207793,-0.00638921,0.02917466,0.01003246,0.04074328,-0.020982,-0.00766249,-0.08675252,0.02212903,-0.0581539,-0.06024966,0.05060513,-0.01190436,-0.00178912,0.04521281,-0.03648528,-0.03754434,0.03068033,-0.06015179,-0.02602835,0.01129781,-0.00384262,0.08866847,0.06170696,-0.05477449,-0.03275278,0.02413953,-0.03272732,-0.0478269,0.09162489,-0.03007131,-0.0791989,-0.04417184,0.02346159,-0.0574421,-0.05309206,-0.01685555,0.01634885,-0.04556044,-0.0085149,0.02963437,-0.01483489,-0.05249935,-0.0274708,-0.05350703,0.00615105,0.048781,0.00246982,-0.0248298,0.03348472,-0.01460841,-0.09394015,-0.01497946,-0.03369329,0.10524233,0.05699348,-0.03454342,-0.03092898,-0.028363,-0.03867361,0.00288508,-0.05539192,-0.02638487,-0.04560415,0.05990174,-0.03458405,0.02308827,0.01423038,0.04731104,0.00979237,-0.02990233,0.06144476,-0.04905311,-0.05790128,0.04838255,0.02796355,-0.01737518,-0.00985694,0.04124798,0.05283404,-0.05995154,0.04460279,0.03054522,0.01494035,0.02285137,0.02653454,0.04234343,0.032285,-0.04192086,-0.21978365,-0.04677355,-0.0568843,0.01654185,0.00430278,-0.00068297,0.03282584,0.05068307,0.00752066,0.10304032,0.04247014,-0.07158168,-0.01659717,0.0128512,-0.01138717,-0.00127085,-0.08595037,-0.05704548,-0.05117602,0.05626553,-0.05006529,0.0248941,-0.03854995,-0.06559011,0.07799979,-0.06170386,0.1354264,0.0527158,-0.01245282,-0.03533004,0.09289776,0.01678039,0.01494686,-0.05047204,0.07110734,0.05349663,-0.02304206,0.05291163,-0.03080204,0.00762744,-0.051567,-0.01792635,-0.01555357,-0.0932762,-0.01033151,0.04615385,-0.0245444,-0.01289999,-0.00060025,0.04182664,0.03971559,0.0082328,0.04167402,0.06387886,0.07996554,0.00922562,-0.07431225,-0.00865356,0.04118614,0.01100617,0.0460352,-0.06950554,0.07871723,-0.01726302,0.04729535,0.01180661,-0.00191323,-0.03136474,0.04059276,0.02565349,0.0019291,0.11478366,0.00074858,0.02227558,-0.02713384,0.0355249,0.05087844,-0.04612938,0.02167048,0.0032804,0.02952392,-0.01741794,0.0904948,0.03284224,0.05134592,0.0397419,0.0434727,0.05913238,0.03164248,-0.03369651,-0.01127916,0.010084,-0.06286476,0.03567141,0.05981575,0.05156212,-0.22071156,0.00741537,-0.01027714,0.01666326,-0.03967182,-0.01373304,0.00584068,-0.00776718,0.05673818,-0.05914578,0.00490962,0.02991775,0.03912156,-0.10007051,-0.00177924,-0.00952941,-0.02641509,-0.02316584,0.09951849,0.01490391,0.07885778,0.07270398,0.21229528,-0.01146977,-0.06614288,0.0230185,0.00458968,-0.03450629,0.03121639,0.01537304,-0.04019125,0.01587828,0.01770795,0.04293155,-0.01902302,0.03342281,-0.07522564,-0.01764095,-0.00354998,0.02720937,-0.08107238,0.01806087,-0.02848112,-0.03853362,0.09987468,0.04579267,-0.01618695,-0.05824854,0.05934549,0.04406094,-0.06413209,-0.09171143,-0.07756393,-0.02074768,-0.03030656,0.03564607,-0.01307202,-0.01845264,0.00555397,-0.00192035,0.03094432,-0.01281297,0.0434948,-0.01388306,-0.04244928],"last_embed":{"hash":"1xw6s8j","tokens":503}}},"last_read":{"hash":"1xw6s8j","at":1753423446169},"class_name":"SmartSource","last_import":{"mtime":1753369691603,"size":6897,"at":1753423416052,"hash":"1xw6s8j"},"blocks":{"#---frontmatter---":[1,6],"#Correct, Fix Errors in Lottery, Lotto Results Files":[8,52],"#Correct, Fix Errors in Lottery, Lotto Results Files#{1}":[10,31],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)":[32,52],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{1}":[34,34],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}":[35,35],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{3}":[36,36],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{4}":[37,37],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{5}":[38,38],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{6}":[39,39],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{7}":[40,40],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{8}":[41,41],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{9}":[42,42],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{10}":[43,43],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{11}":[44,44],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{12}":[45,46],"#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{13}":[47,52]},"outlinks":[{"title":"The software checks for correctness the results, past winning numbers files for many lotto and lottery games.","target":"https://saliu.com/ScreenImgs/correct-lottery-results.gif","line":16},{"title":"Dynamic or Static Filters in Lottery, Lotto Analysis Software","target":"https://saliu.com/bbs/messages/919.html","line":20},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":32},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":34},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":36},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":38},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":40},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":41},{"title":"Lottery Utility Software","target":"https://saliu.com/lottery-utility.html","line":42},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":43},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":45},{"title":"Keep lotto, lottery data files in good shape, in correct formats.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":47},{"title":"Forums","target":"https://forums.saliu.com/","line":49},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":49},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":49},{"title":"Contents","target":"https://saliu.com/content/index.html","line":49},{"title":"Home","target":"https://saliu.com/index.htm","line":49},{"title":"Software","target":"https://saliu.com/infodown.html","line":49},{"title":"Search","target":"https://saliu.com/Search.htm","line":49},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":49},{"title":"PARSEL software corrects, fixes most errors in lotto, lottery data files, results, drawings files.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":51}],"metadata":{"created":"2025-07-24T23:08:11 (UTC +08:00)","tags":["lotto","lottery","software","errors","fix","correct","drawings","numbers","horse racing","pick lotteries","lotto","Keno","Powerball","Mega Millions","Euromillions"],"source":"https://saliu.com/bbs/messages/2.html","author":null}},"smart_blocks:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08602797,-0.05736484,0.0282153,-0.02398573,-0.03031181,0.00782625,-0.06935386,-0.0403941,0.02483009,0.00341205,0.01742966,-0.06842876,0.01655613,0.01355074,-0.03959708,-0.02002772,0.01358599,-0.03134469,-0.06100114,0.03530999,0.04408504,-0.01724977,-0.01406625,-0.02760934,0.03090554,-0.01030988,0.00214425,-0.03361326,-0.03387222,-0.20546672,-0.02201139,0.01228802,0.00366968,-0.02205959,-0.01500203,-0.02493794,-0.02354941,0.00279644,-0.017283,0.06148907,0.0351916,0.01258616,-0.04531546,-0.05489567,0.04302684,-0.01013579,-0.02943997,0.00975758,0.09103283,-0.0055359,-0.04107938,-0.00960029,0.00965259,0.0488861,0.07988422,0.01401016,0.09153106,0.08544792,0.05433656,0.04971001,0.0596919,0.10630883,-0.22369862,0.07186002,0.03780823,-0.01141371,0.01630994,-0.04860179,-0.05472634,0.03325377,-0.0095761,0.02093907,0.01754521,0.08505377,0.0001345,0.00708232,-0.05011785,-0.05458661,-0.07630455,0.0230861,-0.05635174,-0.0250795,-0.02237333,-0.03626442,0.02608382,0.03070169,0.04436231,0.09710422,0.07276756,-0.02552111,0.02505565,-0.04161538,0.08562674,0.09804223,-0.0370183,0.04448792,0.00590933,-0.00323242,-0.03151617,0.1131203,0.00746315,0.01342438,-0.01910946,0.01436557,0.02124066,-0.01657666,-0.02517421,0.00842118,-0.01920504,0.0084529,0.0399638,0.01835588,0.0809067,-0.03188324,-0.02374662,0.02230033,0.02253598,-0.00349571,0.0037451,0.03592782,-0.0334096,0.0441039,0.00620859,0.01418158,-0.02336057,-0.00424437,0.05997467,0.05433362,-0.00524498,-0.00426591,-0.02459448,0.04275565,-0.13953221,-0.0544196,-0.03920265,-0.00356058,0.03898751,-0.01009509,0.04256007,-0.04331229,0.00373009,-0.10205264,-0.0027573,-0.06949245,-0.05115646,0.0391916,0.00550165,0.00557354,0.02916998,-0.03268217,-0.06662907,0.01760693,-0.03849871,-0.02480626,0.01233678,0.01227777,0.09445661,0.08058891,-0.03988567,-0.01681362,0.03491281,-0.03407964,-0.07939086,0.09894818,-0.02994495,-0.09851914,-0.03985864,0.01252884,-0.03558087,-0.07494252,-0.02549765,0.02122343,-0.03885626,0.00301717,0.070912,0.00867673,0.00578669,-0.01468353,-0.06163171,0.00657728,0.02284681,-0.0113746,-0.03223543,0.0393663,-0.02898857,-0.10607134,-0.02832499,-0.04345505,0.09998243,0.07217988,-0.03208809,-0.05542735,-0.01028099,-0.02382815,0.00054356,-0.06022502,-0.02374908,-0.03587944,0.06532469,-0.02643241,0.01436743,0.03659946,0.01875912,-0.00298265,-0.03249377,0.05659174,-0.0457697,-0.06029172,0.04078456,0.01804632,-0.01268739,0.01083523,0.04452909,0.06067677,-0.06974945,0.03273629,0.02104371,-0.00198616,0.01476262,0.00993089,0.02855336,0.03935838,-0.04594884,-0.23070896,-0.0371553,-0.02954856,-0.00858512,-0.02538749,0.00479375,0.0382635,0.02707869,0.02321413,0.11676963,0.01519217,-0.07110022,-0.01333709,-0.01510989,-0.01056139,0.00246772,-0.07347722,-0.07674169,-0.01919698,0.05449318,-0.03088453,0.0342653,-0.05666872,-0.06648047,0.08668195,-0.04075504,0.12682436,0.07823848,-0.02313364,-0.04395491,0.12407567,0.01969556,0.03003211,-0.06825278,0.06418124,0.04331661,-0.04540536,0.04980735,-0.03927069,-0.02293919,-0.06039229,0.00513141,-0.01346413,-0.0829841,0.00044133,0.02883875,-0.00869962,-0.01577928,-0.00907783,0.02930592,0.03466915,0.01386822,0.03598154,0.07791886,0.07589335,0.03151416,-0.0726581,-0.01495437,0.0261722,0.01490415,0.02386266,-0.05893371,0.0617888,-0.01773023,0.02067777,0.01218784,-0.00789303,-0.05362617,0.0132817,0.02103929,0.02091581,0.08167902,0.00718648,0.01695986,-0.01669663,0.00446762,0.03221505,-0.03241568,0.043587,0.00600539,0.02488743,-0.03673731,0.09385449,0.0270672,0.0449948,0.06767467,0.01838595,0.02613184,0.02881568,-0.02720869,-0.01197391,0.02568053,-0.04679451,0.03849895,0.07986405,0.04001561,-0.23602243,0.01537298,0.03047631,0.00919086,-0.03565142,0.00597511,-0.01636052,-0.03033262,0.02576067,-0.05632735,0.01727647,0.04289512,0.00829015,-0.05272353,-0.02132237,0.00619433,-0.03409235,-0.03861894,0.09530535,0.01201846,0.05742785,0.05303147,0.21918061,0.02155872,-0.06391311,0.0219263,0.01089835,-0.01968893,0.02372643,0.01331442,-0.0216883,0.03894985,0.0154937,0.04445408,-0.01160056,0.04659349,-0.06223937,0.00319088,-0.01203836,0.03350223,-0.10819325,0.03068231,-0.0169328,-0.02215951,0.08934872,0.04696034,-0.0460154,-0.05255135,0.03722629,0.02897149,-0.07077055,-0.09058872,-0.04943094,-0.00051621,-0.02981903,0.05395628,0.00740811,-0.02239764,0.02062982,0.01026804,0.03784301,-0.01198779,0.03025328,-0.02190924,-0.01730839],"last_embed":{"hash":"8zeoow","tokens":111}}},"text":null,"length":0,"last_read":{"hash":"8zeoow","at":1753423445629},"key":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#---frontmatter---","lines":[1,6],"size":248,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05624773,-0.04364646,0.03956337,-0.02439226,-0.00752564,0.00497124,-0.0275338,-0.04547683,0.01686931,0.0227486,0.03738745,-0.04919637,0.03291655,0.02855159,-0.06091297,0.0056916,0.01908528,-0.01964775,-0.04732228,0.04709551,0.01930579,-0.02520502,-0.01910043,-0.05409868,0.03220462,0.02551915,-0.04002066,-0.06521073,-0.07905629,-0.24210405,-0.00428923,0.02633742,0.0033677,-0.04045909,-0.02367927,-0.03100195,-0.0252403,0.00008001,-0.03422622,0.0385868,0.04316762,-0.00137095,-0.03992286,-0.05385908,0.01492654,-0.01635396,-0.03022527,0.00206982,0.09003921,0.00660153,-0.08967298,-0.00245896,0.03289516,0.04693169,0.03947638,0.00212188,0.05561381,0.12507629,0.03167006,0.03408487,0.04868157,0.10568598,-0.17675652,0.03427257,0.01053329,-0.012019,-0.00544611,-0.05887062,-0.03412997,0.01920888,-0.01915614,0.02558806,-0.01172393,0.08275298,0.02327817,-0.02503549,0.00258385,-0.03682896,-0.05914298,0.03951641,-0.04824936,-0.02877879,0.0083427,0.0067013,0.02627848,0.04370664,0.04333979,0.05553103,0.08955481,-0.05767546,0.05765152,-0.00110432,0.04244941,0.08672665,-0.00800853,0.03516226,0.04803856,0.01449978,-0.01670743,0.11321253,0.03247266,-0.02071573,-0.02121501,0.021739,0.03504007,-0.03210209,-0.04480141,0.00876671,-0.02583751,0.0199633,0.05650752,-0.00698471,0.08542722,-0.01970407,-0.05440217,0.03534035,-0.0158944,-0.01613815,-0.02017603,0.01498483,-0.02588436,0.04323421,-0.0086316,0.02488345,-0.03753266,-0.0303074,0.06861506,0.05132658,0.00868859,0.03693346,0.00639469,0.05112398,-0.15548548,-0.05041622,-0.02812807,0.00083859,0.02079722,0.00198407,0.03664032,-0.0204512,-0.0167976,-0.07702697,0.0190086,-0.05276338,-0.05807823,0.05909619,-0.01668011,0.00572459,0.04540602,-0.04017143,-0.0319335,0.03400151,-0.06590994,-0.01036789,0.01820882,-0.01952632,0.09434801,0.04894593,-0.04818148,-0.03722383,0.0155361,-0.03436399,-0.03199307,0.09544167,-0.03318706,-0.06946032,-0.05250134,0.02824163,-0.0568967,-0.05141568,-0.01536012,0.01285166,-0.04336757,-0.00694983,0.0220018,-0.02456707,-0.05827601,-0.02594078,-0.0407116,-0.0049947,0.06638903,0.00948849,-0.02119113,0.03596301,-0.01121138,-0.09703816,-0.00247039,-0.0357809,0.10244359,0.04902266,-0.03381491,-0.03301156,-0.04143061,-0.04946479,-0.0021058,-0.04998219,-0.03567105,-0.04932027,0.04658287,-0.04670655,0.0327236,0.00234352,0.05415739,0.01484603,-0.02341693,0.05580109,-0.05103898,-0.05431626,0.05418522,0.02587264,-0.03034159,-0.01730587,0.04497438,0.04801782,-0.05720608,0.04087377,0.02813338,0.04152887,0.01852375,0.03587235,0.03546083,0.03476667,-0.03854683,-0.2151614,-0.05099684,-0.05692469,0.02598229,0.01118846,0.00246011,0.02272573,0.05111675,-0.00706619,0.10264312,0.06231186,-0.07814946,-0.00103658,0.01012453,-0.00908543,-0.00930622,-0.08724611,-0.04917827,-0.05529782,0.04462969,-0.05863391,0.01127574,-0.04333559,-0.06767394,0.07726625,-0.05781498,0.13043185,0.05169181,-0.00571197,-0.02876548,0.07469915,0.01643102,0.00665782,-0.05690221,0.06935738,0.06850126,-0.02093933,0.05652924,-0.0273071,0.01372257,-0.0494778,-0.01440494,-0.0202088,-0.08797721,-0.01479775,0.05037881,-0.03002896,-0.01415982,-0.00287018,0.04877862,0.03671902,0.00620912,0.0364883,0.06182075,0.07514349,-0.00334078,-0.07344875,0.00218851,0.03846616,0.00045958,0.05520258,-0.07704991,0.07552402,-0.01455832,0.05707886,0.02134652,0.00427,-0.04542276,0.04210351,0.0316301,-0.00570956,0.11979628,-0.01231388,0.01405834,-0.03903313,0.05402158,0.05610036,-0.05038104,0.02279609,-0.00480917,0.03416361,-0.01105328,0.09052348,0.03890715,0.05215333,0.0298961,0.05165106,0.05843185,0.03084205,-0.03238319,-0.00334794,0.00990262,-0.0716145,0.03363828,0.05021705,0.05498726,-0.21073914,0.00224316,-0.0124884,0.02486666,-0.03997445,-0.02957029,0.01366165,0.0064633,0.06509688,-0.05782235,0.00267915,0.01597081,0.03649437,-0.10817327,-0.00197459,-0.01527161,-0.01008867,-0.02309855,0.09698685,0.01740713,0.08177295,0.08283918,0.21172531,-0.02091382,-0.06444289,0.01366928,0.00897646,-0.03850498,0.02876747,0.02340175,-0.03648802,0.00302368,0.02522894,0.04674097,-0.01151749,0.02816467,-0.06423204,-0.01861325,0.01010945,0.03258966,-0.06950044,0.02332731,-0.04239889,-0.03128054,0.09421808,0.03960213,-0.02037049,-0.05405857,0.05842886,0.05065587,-0.07148764,-0.0933335,-0.09017884,-0.02671384,-0.03655418,0.02943841,-0.01608631,-0.01387187,-0.00033349,-0.00187817,0.02696467,-0.01902748,0.05806558,-0.00849622,-0.03970549],"last_embed":{"hash":"1xki3bs","tokens":425}}},"text":null,"length":0,"last_read":{"hash":"1xki3bs","at":1753423445664},"key":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files","lines":[8,52],"size":6643,"outlinks":[{"title":"The software checks for correctness the results, past winning numbers files for many lotto and lottery games.","target":"https://saliu.com/ScreenImgs/correct-lottery-results.gif","line":9},{"title":"Dynamic or Static Filters in Lottery, Lotto Analysis Software","target":"https://saliu.com/bbs/messages/919.html","line":13},{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":25},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":27},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":29},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":31},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":33},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":34},{"title":"Lottery Utility Software","target":"https://saliu.com/lottery-utility.html","line":35},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":36},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":38},{"title":"Keep lotto, lottery data files in good shape, in correct formats.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":40},{"title":"Forums","target":"https://forums.saliu.com/","line":42},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":42},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":42},{"title":"Contents","target":"https://saliu.com/content/index.html","line":42},{"title":"Home","target":"https://saliu.com/index.htm","line":42},{"title":"Software","target":"https://saliu.com/infodown.html","line":42},{"title":"Search","target":"https://saliu.com/Search.htm","line":42},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":42},{"title":"PARSEL software corrects, fixes most errors in lotto, lottery data files, results, drawings files.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":44}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0582177,-0.0435521,0.03796247,-0.02235669,-0.00859689,0.0090447,-0.02874824,-0.04468417,0.01715187,0.02260514,0.03568105,-0.0454914,0.03627994,0.02646527,-0.06019865,0.00933093,0.01945252,-0.021225,-0.04681307,0.04680519,0.02254502,-0.02618783,-0.01754657,-0.05206108,0.03617343,0.02256404,-0.03992141,-0.0624422,-0.07636841,-0.24104518,0.0001695,0.02612538,0.00229987,-0.04132944,-0.02750023,-0.03521452,-0.02483578,0.00050421,-0.03377865,0.038228,0.04263011,0.00009902,-0.0396551,-0.05282725,0.01389765,-0.01937249,-0.02628215,0.00138248,0.08885726,0.00789366,-0.09084731,-0.0022135,0.03289261,0.04613061,0.04427753,0.00426708,0.05812952,0.12356445,0.03231563,0.03588624,0.04804168,0.10432015,-0.17444509,0.03788242,0.00886603,-0.01104032,-0.00661471,-0.05615976,-0.03191415,0.0201591,-0.01729005,0.02820667,-0.01234392,0.08366444,0.02095331,-0.02331175,0.00201125,-0.03800551,-0.05988695,0.04118441,-0.04796304,-0.02649481,0.00856403,0.00506857,0.02346912,0.04263955,0.04439002,0.05194627,0.0869536,-0.0583783,0.05531575,-0.00212444,0.04254491,0.08502072,-0.00402431,0.03213121,0.04772189,0.01238194,-0.0152928,0.11029386,0.0331427,-0.02034287,-0.02068937,0.0219196,0.03627269,-0.03466416,-0.04600833,0.00842864,-0.02779472,0.0219702,0.05785788,-0.00662745,0.0856964,-0.01582527,-0.05582653,0.03766962,-0.01766988,-0.01594352,-0.01608974,0.01043887,-0.02367516,0.0455963,-0.0106888,0.02432652,-0.0363184,-0.03031204,0.06910304,0.04993754,0.0117537,0.04091095,0.01103141,0.05054268,-0.15611553,-0.04719486,-0.02500456,-0.00205508,0.01778207,0.00056824,0.03747274,-0.02207031,-0.0153323,-0.07497395,0.0166017,-0.05642105,-0.05563701,0.0621732,-0.01434141,0.00722941,0.04599211,-0.04245292,-0.03071911,0.03376793,-0.06490393,-0.0123028,0.0151406,-0.01983069,0.09561425,0.05441842,-0.04735893,-0.03708974,0.01343681,-0.03473771,-0.02992219,0.09657426,-0.03111849,-0.07297032,-0.05498729,0.02833265,-0.06017068,-0.05337954,-0.01297494,0.01359165,-0.04315767,-0.00244748,0.02206051,-0.02490348,-0.05808273,-0.0294248,-0.03541961,-0.00669036,0.06462162,0.00589738,-0.02473023,0.03276314,-0.01511197,-0.09744737,-0.00560355,-0.03440666,0.10129417,0.05155604,-0.03284626,-0.02876369,-0.03969194,-0.04532368,-0.00427857,-0.05192837,-0.03602473,-0.04858066,0.04740823,-0.04979824,0.02988551,0.00044407,0.0527571,0.01146137,-0.0254172,0.05354004,-0.04692902,-0.05658008,0.05928722,0.02761189,-0.0292455,-0.0191383,0.04532931,0.05073911,-0.0569744,0.04093592,0.03101508,0.04074722,0.02061527,0.03623369,0.03490396,0.03392937,-0.03668249,-0.21669871,-0.05201834,-0.05921081,0.02325,0.01231725,0.00197238,0.02375318,0.04882934,-0.0043753,0.10037663,0.06980304,-0.08383437,-0.00022375,0.01217734,-0.01102294,-0.00866079,-0.08743308,-0.04969591,-0.05213156,0.04504834,-0.06232507,0.01502705,-0.04143757,-0.06818101,0.07454885,-0.05648186,0.13143957,0.04520188,-0.00521182,-0.02927193,0.07633952,0.01478935,0.00450334,-0.06101624,0.06722002,0.06875505,-0.018773,0.05945179,-0.03054197,0.01141991,-0.05027122,-0.01690935,-0.01874775,-0.08963912,-0.0175535,0.05038372,-0.02966738,-0.01114648,-0.00469842,0.05023148,0.03749362,0.00519565,0.04093499,0.06080101,0.073309,-0.00437472,-0.0703076,0.00205012,0.04033307,0.0055938,0.0531448,-0.07670223,0.07490999,-0.01718789,0.0570336,0.01905479,0.00219729,-0.04373182,0.04099966,0.02990585,-0.0067387,0.11908212,-0.0132382,0.01670775,-0.03960988,0.05484452,0.0526633,-0.0504015,0.02439169,-0.00221381,0.03181002,-0.01096427,0.0870384,0.04476871,0.04997662,0.02911172,0.05579295,0.05857914,0.02782152,-0.03208287,-0.00366326,0.00446744,-0.07441968,0.03087697,0.0486308,0.05206402,-0.21659411,0.00156385,-0.0156137,0.02753861,-0.03966229,-0.03170042,0.01742609,0.01084853,0.06630397,-0.05739119,-0.00051585,0.01455906,0.037996,-0.10805142,-0.00159505,-0.01263852,-0.01199936,-0.02591591,0.09095546,0.01778411,0.08127703,0.0818942,0.21168108,-0.01891904,-0.06299653,0.01619704,0.01385004,-0.03483979,0.03063825,0.02177349,-0.03802966,0.00256334,0.02386473,0.04769142,-0.01407626,0.02397827,-0.06812279,-0.01941345,0.00872193,0.03034799,-0.06932808,0.02411549,-0.03890012,-0.02825405,0.09634423,0.04040765,-0.0211967,-0.0529434,0.05748209,0.0493957,-0.07260503,-0.09401305,-0.08977604,-0.0268325,-0.03569188,0.02898378,-0.01541613,-0.01318861,0.000635,0.00044472,0.02516125,-0.01805485,0.05846476,-0.00831266,-0.03742148],"last_embed":{"hash":"mwac8b","tokens":475}}},"text":null,"length":0,"last_read":{"hash":"mwac8b","at":1753423445805},"key":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#{1}","lines":[10,31],"size":4220,"outlinks":[{"title":"The software checks for correctness the results, past winning numbers files for many lotto and lottery games.","target":"https://saliu.com/ScreenImgs/correct-lottery-results.gif","line":7},{"title":"Dynamic or Static Filters in Lottery, Lotto Analysis Software","target":"https://saliu.com/bbs/messages/919.html","line":11}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07827865,-0.0219132,0.00829537,-0.04202148,-0.03705766,0.00949445,-0.0621659,-0.04416879,0.02048056,0.00696755,0.03162761,-0.03920643,0.04770138,-0.03620153,-0.02656386,-0.01823722,0.02948303,0.02273213,-0.03848542,0.00618779,0.03323713,-0.01920397,-0.049737,-0.06608672,0.04810195,0.02294653,-0.0242281,-0.06628419,-0.04112429,-0.22065191,-0.01275232,0.02004221,-0.00994833,-0.0488429,-0.04346147,-0.00789005,-0.03428672,0.00351423,-0.04846411,0.0268444,0.03770881,0.00716907,0.0031244,-0.02267938,0.04217071,-0.01872054,-0.00047799,0.01807604,0.08621106,-0.01458264,-0.09452223,0.00041471,0.02892522,0.03408684,0.05639116,0.04030997,0.06235768,0.12977242,0.00459057,0.03834746,0.04636896,0.11020355,-0.21140234,0.07436192,0.03948453,0.00579665,0.01296862,-0.03622591,-0.01255489,0.03774174,-0.02557918,0.01674893,0.01388814,0.09405092,0.0316013,-0.00428381,-0.04764978,-0.05494275,-0.04517749,0.03204762,-0.05644318,-0.01377579,0.01980296,0.01897995,0.00014933,0.04049106,0.05526785,0.05820374,0.10169578,-0.08155137,0.02782146,0.00607269,0.05007777,0.06011646,0.00881176,0.02155059,0.04132779,-0.02192121,-0.01517595,0.11624063,0.02957216,0.00175614,0.00569363,0.00612548,0.01720316,-0.06412905,-0.02773576,0.0014588,-0.03271591,0.03519457,0.06373845,-0.00836316,0.04562229,-0.03373095,-0.06267974,0.01220641,-0.01186529,-0.00894602,-0.00855229,-0.00111566,-0.02497259,0.01225949,0.0067662,0.02030556,-0.02609761,-0.02742191,0.02362345,0.0625116,-0.00535876,0.01736227,0.01493956,0.06315953,-0.14671031,-0.04994769,-0.02121802,-0.00968668,0.02719314,-0.00353576,0.01732179,0.00486745,-0.01590737,-0.09065962,0.00083603,-0.0843202,-0.04403999,0.07494983,-0.02264444,0.01788227,0.02195423,-0.03321544,-0.01376749,-0.00549553,-0.04813803,-0.03065732,0.02064974,-0.00150736,0.1350159,0.03318476,-0.0445441,-0.01151922,-0.01868482,-0.03075912,-0.0650536,0.13352853,-0.01242756,-0.08172837,-0.02434962,0.02824154,-0.05384889,-0.08592489,-0.02163426,-0.00460628,-0.05219259,0.00414037,0.07273369,-0.00156653,-0.0389691,-0.02678565,-0.02197257,0.00248884,0.01747369,-0.03666721,-0.02155579,0.03655985,-0.01469194,-0.0934239,-0.02086233,-0.02372663,0.06444906,0.05093346,-0.02788112,-0.01133488,-0.03405414,-0.02145972,-0.04008571,-0.04933259,-0.05698126,-0.05863725,0.07192271,-0.02973182,0.02846001,0.03910823,0.02510696,-0.0106204,-0.01777619,0.06116772,-0.05382039,-0.06241683,0.0493771,0.03261538,-0.0464511,0.01367324,0.05366978,0.04640261,-0.04771468,0.04867523,0.04394716,0.0151061,-0.01241998,0.0363843,0.00614423,0.04559664,-0.06180821,-0.19005592,-0.06755798,-0.04207131,-0.00394027,-0.0236658,0.00065987,0.03641761,-0.00831647,-0.00757714,0.08219021,0.07149154,-0.06945331,0.01947371,0.04406686,-0.00847639,-0.02243187,-0.07230978,-0.04960942,-0.06155814,0.04262257,-0.01185456,0.00178998,-0.02275921,-0.0691771,0.03790788,-0.04314368,0.13069932,0.00186276,-0.00158611,0.01363615,0.10546315,0.01354134,0.00263439,-0.05423614,0.0549542,0.08837733,-0.04633717,0.01321226,-0.03707781,0.01582666,-0.05997087,0.0059788,-0.00436989,-0.07790758,-0.03297862,0.02450391,-0.01333469,-0.00673592,-0.00662138,0.04679735,0.00316329,0.02026583,0.04037028,0.04849129,0.05043687,0.01716113,-0.05084718,-0.00274575,-0.01521044,0.02203305,-0.02231148,-0.0779636,0.05448551,-0.00187603,0.05925616,-0.00322331,-0.00884086,-0.05249589,0.03600289,0.02334416,0.00202249,0.10239236,-0.00868372,0.05629265,-0.01636304,0.02824755,0.06661771,-0.05615455,0.01039735,0.00145379,-0.04041358,-0.03941524,0.07822479,0.05851331,0.0807222,0.03626314,0.05866148,0.02786207,0.00023533,-0.0214656,0.00141469,0.01084715,-0.03353009,0.04740625,0.04828953,0.06017431,-0.24958013,0.0232041,-0.00231458,0.02493796,-0.04033704,-0.04634363,0.00892197,-0.03914171,0.03778372,-0.04179015,0.01804973,0.00927617,0.02142035,-0.07165726,0.02028138,-0.02743955,0.00307206,-0.03184797,0.09155557,0.01943633,0.04787299,0.07870959,0.24774009,0.02088935,-0.02812097,0.04021892,0.02469669,-0.01208914,0.02005542,0.04018619,-0.00941064,0.03975504,0.01017082,0.05647649,-0.03473643,0.02427491,-0.06692628,0.00539692,0.01629701,0.04691996,-0.08246554,0.01925885,-0.01040929,-0.00394874,0.11579148,0.03145793,-0.01336361,-0.07820628,0.05232712,0.05631856,-0.07183204,-0.04820508,-0.07320532,-0.03150669,-0.02107166,0.02158527,0.00308277,-0.01502333,0.01193527,-0.00578231,0.04991912,-0.0314503,0.06490423,0.00376504,-0.01016045],"last_embed":{"hash":"4q2g2q","tokens":423}}},"text":null,"length":0,"last_read":{"hash":"4q2g2q","at":1753423445966},"key":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)","lines":[32,52],"size":2367,"outlinks":[{"title":"Resources in Lottery, Software, Systems, Lotto Wheels, Strategies","target":"https://saliu.com/content/lottery.html","line":1},{"title":"_**Lotto, Lottery, Software, Strategy, Systems**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies","target":"https://saliu.com/Newsgroups.htm","line":5},{"title":"_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_","target":"https://saliu.com/MDI-lotto-guide.html","line":7},{"title":"_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_","target":"https://saliu.com/forum/lotto-book.html","line":9},{"title":"**Skips Lottery, Lotto, Gambling, Systems, Strategy**","target":"https://saliu.com/skip-strategy.html","line":10},{"title":"Lottery Utility Software","target":"https://saliu.com/lottery-utility.html","line":11},{"title":"**Lotto wheels**","target":"https://saliu.com/lotto_wheels.html","line":12},{"title":"**lottery software, lotto software**","target":"https://saliu.com/infodown.html","line":14},{"title":"Keep lotto, lottery data files in good shape, in correct formats.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":16},{"title":"Forums","target":"https://forums.saliu.com/","line":18},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":18},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":18},{"title":"Contents","target":"https://saliu.com/content/index.html","line":18},{"title":"Home","target":"https://saliu.com/index.htm","line":18},{"title":"Software","target":"https://saliu.com/infodown.html","line":18},{"title":"Search","target":"https://saliu.com/Search.htm","line":18},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":18},{"title":"PARSEL software corrects, fixes most errors in lotto, lottery data files, results, drawings files.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":20}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06317195,-0.05931611,0.02523901,-0.02323211,-0.03404178,0.00119046,-0.02547528,-0.04747168,0.01870394,0.00231947,0.02887682,-0.03720266,0.04058455,-0.00157789,-0.01813198,-0.0014901,0.03878289,0.0426443,-0.03833562,0.00758472,0.03403927,-0.00298016,-0.05759538,-0.06630796,0.02833813,0.04375302,-0.03141715,-0.08355839,-0.02038068,-0.17454323,-0.01988663,0.02283946,-0.02295682,-0.04979478,-0.04381484,-0.0446674,-0.04515336,0.0247486,-0.07620317,0.04064786,0.04976441,0.01278137,-0.0228992,-0.02075442,0.02010381,-0.01745185,0.01334142,0.01927261,0.10004921,-0.01913827,-0.06286991,0.01456194,0.00986725,0.04413899,0.03579192,0.03146605,0.0709646,0.1375639,0.00282599,0.0202996,0.03770167,0.09943922,-0.17914033,0.05941446,0.02495391,0.02675434,0.02386412,-0.03264773,-0.00922415,0.05055742,-0.0047189,0.01873877,0.01342098,0.09481375,0.03707741,-0.00879596,-0.02469128,-0.05879673,-0.04122826,0.03023905,-0.06125043,-0.04146482,0.0083712,0.03944049,-0.00794534,0.03590826,0.04961205,0.06426253,0.10793903,-0.09349647,0.02581826,0.00824395,0.05254276,0.07186763,0.01752033,0.01628193,0.03208834,-0.01701136,0.0057288,0.14430641,0.00540006,0.00066672,0.01427461,-0.00691417,0.01923276,-0.05202643,-0.04598077,0.00613774,-0.03891071,0.06375014,0.08023565,0.00304954,0.06803367,-0.03202458,-0.05908686,-0.00451243,-0.02970195,0.01753615,-0.00962963,-0.0096522,-0.03445203,0.02793828,-0.00353099,0.01213035,-0.02232644,-0.03518528,0.00727237,0.077254,-0.0241596,0.01237368,0.00569453,0.03698641,-0.14533715,-0.03769984,-0.02483832,-0.0258738,0.0323673,0.00183975,-0.00369714,0.02924403,-0.01828433,-0.07061355,0.02798238,-0.08979551,-0.04137248,0.07056431,-0.02480527,0.04451542,0.02300189,-0.02213475,-0.03358444,0.00430981,-0.05130647,-0.03554274,0.01483738,-0.0162031,0.12639651,-0.00441686,-0.0436409,0.01350569,-0.02344106,-0.02353333,-0.05808843,0.12531178,-0.03793672,-0.05859789,-0.03581537,0.02938699,-0.05155729,-0.07559374,-0.03050409,0.00596799,-0.05631265,0.00018354,0.09379011,-0.00945561,-0.0635955,-0.02858789,-0.02945509,-0.00682692,0.02388611,-0.02911986,-0.00962035,0.02823784,-0.00857408,-0.08796711,-0.02695601,-0.02827929,0.08205686,0.04729658,-0.03305,-0.00433908,-0.02918347,-0.04903551,-0.02770678,-0.04281022,-0.06574343,-0.04823866,0.06943836,-0.02141987,-0.00116832,0.04520589,0.02809948,0.01195973,-0.04695323,0.06159769,-0.04324172,-0.06507917,0.05063947,0.05037389,-0.07364862,0.00537453,0.0435634,0.04174301,-0.06276694,0.03881646,0.04216084,0.03423084,-0.02122916,0.02746658,-0.01121706,0.03874509,-0.0726435,-0.1904791,-0.07090633,-0.02774853,0.00126942,-0.00584612,0.00323381,0.0096165,0.00379509,-0.00283176,0.09375595,0.0707127,-0.08512032,0.02702445,0.04246003,-0.01408233,-0.03889125,-0.05906694,-0.04726489,-0.03463022,0.02365983,-0.02358526,-0.00630711,0.00182489,-0.09207665,0.03226875,-0.02556894,0.13515577,0.00484489,0.02165781,0.01738443,0.07335471,-0.00748021,-0.00664492,-0.04925404,0.06441408,0.08012147,-0.06435476,-0.00015711,-0.03047379,0.02080402,-0.03763286,-0.02877064,-0.00246882,-0.07176051,-0.00451466,0.01549553,-0.0320321,-0.00536951,-0.00705756,0.0573763,0.00039851,0.04220884,0.04476338,0.05850427,0.04817926,0.02508241,-0.05948592,-0.00589715,-0.0141693,0.01061089,-0.00463615,-0.06479061,0.04152713,-0.00253295,0.07306055,0.01688476,-0.02109922,-0.0739272,0.03089902,0.00029714,0.01228974,0.09950887,-0.00815047,0.04610072,-0.01084653,0.03445685,0.07731493,-0.07623748,0.00919126,0.01520085,-0.02123505,-0.04568231,0.08901896,0.07784219,0.0665254,0.02421279,0.04879522,0.03696056,-0.02050467,-0.03126521,0.01248995,0.0250678,-0.0421895,0.0368876,0.02656233,0.04558058,-0.237661,0.02650394,-0.00613472,0.03417959,-0.04839496,-0.05614371,0.01423089,-0.01034615,0.03017975,-0.05336462,0.02740535,-0.00307447,-0.00108231,-0.06825273,0.02699324,-0.01261166,0.00581567,-0.05165096,0.11124089,0.02229352,0.05270835,0.08456179,0.25674346,0.02089906,-0.00878473,0.02672164,0.0171051,-0.00763599,0.01335998,0.04874058,0.00049998,0.02507078,-0.01114863,0.06103841,-0.03721763,0.0455117,-0.0519097,0.01231464,0.0013843,0.05522358,-0.09062544,0.0084633,-0.03373482,0.02721948,0.10127098,0.01051129,-0.02325177,-0.07431835,0.04602809,0.0451271,-0.07895971,-0.04255854,-0.07042044,-0.03906711,-0.01507728,0.02020965,0.01320195,-0.02061331,0.00878636,-0.02017512,0.02999569,-0.040211,0.06475833,-0.0032603,-0.03057317],"last_embed":{"hash":"1m22jsz","tokens":100}}},"text":null,"length":0,"last_read":{"hash":"1m22jsz","at":1753423446104},"key":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{2}","lines":[35,35],"size":202,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0570109,-0.02394257,0.03284193,-0.05557599,-0.00364972,-0.00641358,-0.01652483,-0.03929406,0.0232307,-0.01060147,0.0430977,-0.03763935,0.05644693,-0.02681415,-0.04200401,-0.00705023,0.02634336,0.02223532,-0.04286021,-0.00539602,0.02113846,0.01096241,-0.04192778,-0.06791215,0.05575643,0.01795612,-0.04657062,-0.09107143,-0.05931514,-0.20601884,-0.01756304,0.02251078,-0.01826599,-0.02592474,-0.02953244,-0.00297167,-0.01817415,-0.00215899,-0.00684843,0.02666991,0.05217543,-0.00148287,-0.0257486,0.00169075,0.0334757,-0.01007264,-0.00845907,0.01513676,0.05750193,-0.01024958,-0.10294642,0.00255196,0.04508535,0.01985198,0.02251321,0.04509986,0.05726483,0.12405146,0.03790053,0.05459085,0.05172278,0.09477477,-0.20242028,0.05447276,0.06198943,0.00017896,0.00210189,-0.05938057,-0.03462426,0.03138368,-0.0317366,-0.00269729,0.01546178,0.09350487,0.05873415,-0.02361474,-0.05571082,-0.06642002,-0.0588419,0.04441405,-0.04802499,-0.00890092,0.02237007,0.0264511,-0.01368368,0.02769278,0.06910187,0.04997045,0.11399364,-0.0664454,0.04742881,0.02255479,0.04002838,0.0696675,0.0132436,0.01829513,0.02345337,-0.0360029,0.02279492,0.14418954,-0.012502,-0.00237128,0.00033044,-0.00031836,0.00743671,-0.06301836,-0.02510968,0.02060226,-0.01162619,0.02523499,0.04404276,-0.00023485,0.03318002,-0.03765889,-0.07115144,0.01041348,-0.02364374,-0.01642085,-0.02299565,0.01118408,-0.01807139,-0.00486994,0.00773416,0.01553333,-0.0231922,-0.04296857,0.04791675,0.07988527,-0.03081746,0.00173353,0.00929344,0.04169268,-0.15711184,-0.03419092,-0.03053088,0.00896996,0.03039289,-0.00834364,0.03087665,0.01828287,-0.01637506,-0.07513427,0.00953257,-0.07339738,-0.0446882,0.08958697,-0.01613212,0.02517142,0.02272695,-0.03457046,-0.00859637,-0.00200384,-0.05283042,-0.00978918,0.02107738,-0.01205845,0.12137123,0.02101184,-0.0141783,-0.00873948,-0.012205,-0.04861615,-0.0547206,0.12838967,-0.0388918,-0.06703231,-0.02433299,0.00942282,-0.0583749,-0.08705837,0.02311841,-0.01832459,-0.05587941,0.00460754,0.03311952,0.00076538,-0.01938514,-0.02046391,-0.01671959,-0.00689566,0.03296523,-0.03860534,-0.02934874,0.06094677,-0.01912694,-0.0858027,-0.02707553,-0.00330231,0.05380211,0.04974402,-0.02363639,-0.01819953,-0.05923583,-0.02449793,-0.00577532,-0.06025409,-0.04236917,-0.05050882,0.06847722,-0.06248619,0.01917748,0.04061697,0.02066406,0.00268476,-0.00062445,0.06209883,-0.07945953,-0.03904482,0.04076485,0.03804452,-0.06358211,0.01509548,0.03133417,0.02768581,-0.03981163,0.03857699,0.04462304,-0.00049551,0.00862732,0.0475875,0.00368305,0.03974873,-0.03317359,-0.20689668,-0.0749267,-0.02740779,-0.00500174,-0.02379978,-0.00031015,0.01642029,0.00307472,-0.01291937,0.10561182,0.05874314,-0.05753698,0.02464338,0.02226586,0.00083597,-0.01969295,-0.06686216,-0.05330915,-0.07898258,0.05065931,-0.0397558,0.00181214,-0.04374084,-0.04915166,0.04552319,-0.0314479,0.11460963,-0.0108483,0.00289169,0.0101776,0.09579663,-0.00162338,0.0157392,-0.07502402,0.05768044,0.08381585,-0.05097544,0.01331916,-0.04335252,0.02159739,-0.06588141,0.01678648,0.00276288,-0.07244363,-0.01110538,0.0399162,0.01812991,-0.03419644,-0.01054862,0.02609471,-0.00338178,0.00502121,0.03958394,0.0900686,0.06233193,0.00868387,-0.04188152,0.02461146,-0.01999919,-0.0060644,-0.04144825,-0.08234114,0.07219074,0.00747268,0.06488501,0.01315999,-0.00966889,-0.06352374,0.03168548,0.0183831,0.00824479,0.11226043,-0.02049294,0.03029308,-0.03240899,0.05219835,0.06906356,-0.06676066,-0.00092973,0.02012614,0.00482388,-0.05317105,0.09123223,0.02996494,0.05383854,0.03541716,0.05520882,0.00662181,0.00550381,-0.01961387,0.02514263,-0.00668516,-0.02507072,0.0456748,0.03937378,0.046935,-0.22376867,0.02380229,-0.01013505,0.02785722,-0.03881447,-0.03962392,0.01419914,-0.02586618,0.04631338,-0.03494954,0.02840706,0.010196,-0.0017954,-0.07964787,0.01287014,-0.02490252,0.04390623,-0.05744237,0.09489951,0.02321837,0.05668635,0.07390099,0.26206219,0.0251545,-0.03039574,0.03544604,0.02568855,-0.02462134,0.00374335,0.02971912,0.00779825,0.01251841,-0.00541488,0.05174031,-0.04508517,0.01899512,-0.05814302,0.03866987,0.03962663,0.05121496,-0.07611964,0.01462874,-0.04227098,-0.03377869,0.10299484,0.03212192,-0.0283003,-0.06083613,0.03997174,0.03125856,-0.05810334,-0.062942,-0.07699934,-0.03357184,-0.03790101,0.00955642,-0.01401512,0.00146925,0.04068096,0.00291107,0.07561051,-0.02584077,0.04848472,0.02126375,-0.01197631],"last_embed":{"hash":"pazr3d","tokens":106}}},"text":null,"length":0,"last_read":{"hash":"pazr3d","at":1753423446136},"key":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{4}","lines":[37,37],"size":231,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07347728,-0.04507338,0.03162138,-0.02261789,0.01111971,0.00768423,-0.06289487,-0.03956163,-0.00439105,-0.00292449,-0.00164286,-0.07475721,0.01459386,-0.00737885,-0.02727856,-0.0062696,0.02771392,0.01093054,-0.04718943,0.05771215,0.03526571,-0.00439451,-0.01263791,-0.03866522,0.03750336,0.00502991,-0.02742324,-0.06672277,-0.05292606,-0.21780366,-0.02807874,-0.00066081,-0.0212657,-0.02282634,-0.04728486,-0.00279616,-0.01723575,0.02852361,-0.01547083,0.04906272,0.05268841,0.00425922,-0.02425457,-0.04659379,0.0250906,0.00193781,-0.01515154,-0.00039515,0.09490879,-0.02272929,-0.10320829,0.00442182,0.01341872,0.03264296,0.04912805,0.05075321,0.0824274,0.13691884,0.0053444,0.04002425,0.05164851,0.09313004,-0.20717223,0.05192905,0.02652588,-0.02660212,-0.00015908,-0.04854014,-0.02477911,0.0342418,-0.02929801,0.00381139,0.00579422,0.09865268,0.02512052,-0.03745352,-0.02433522,-0.06133372,-0.04571108,0.02900712,-0.07298686,-0.03505034,-0.00467445,0.01047454,0.02302961,0.0239142,0.03720177,0.06268299,0.05760595,-0.07201298,0.03678318,-0.02036441,0.04858623,0.07910051,-0.00922937,0.03681164,0.00407753,-0.00295275,-0.02044599,0.12378933,0.01534888,0.00335181,-0.00529438,0.04483732,0.02840464,-0.04308881,-0.03813457,0.03210642,-0.02322296,0.04663715,0.06192831,-0.01786084,0.04874531,-0.03350886,-0.07456414,0.01167705,-0.00210422,0.00756226,-0.03696589,-0.00358593,-0.02972553,0.05215362,0.01266061,0.01101654,-0.01205336,-0.04371084,0.03921452,0.04630309,0.0100768,0.03721249,-0.00711838,0.06886014,-0.14089121,-0.07158414,-0.05243962,0.01121589,0.02624853,-0.00065061,0.01012944,-0.02611776,0.00322724,-0.12661994,-0.0181646,-0.04519492,-0.04926436,0.07413271,-0.0399394,0.04054433,0.04689409,-0.02209483,-0.04830015,0.0303643,-0.03701286,-0.01777876,0.00633989,-0.01326479,0.11262529,0.03530466,-0.04527447,0.00687104,-0.00413871,-0.05864018,-0.03762028,0.11634576,-0.02271325,-0.03258896,-0.05051197,0.01262423,-0.03638932,-0.066145,-0.00818874,0.00007198,-0.01401051,0.00404587,0.03891052,0.00301048,-0.02016978,-0.02543984,-0.04577781,0.00387097,0.005728,-0.00747975,-0.00040021,0.0359097,-0.02230674,-0.10118227,-0.0085894,-0.04093694,0.09463021,0.05246792,-0.04541021,-0.05410401,-0.01499673,-0.03912089,-0.00416693,-0.04845106,-0.04615214,-0.0445072,0.05361993,-0.02172436,0.01847103,0.03797431,0.01674464,-0.00956486,-0.06088748,0.07532957,-0.06334514,-0.06485287,0.02880422,0.0035142,-0.02638857,0.02193027,0.06043672,0.03711539,-0.04202893,0.03097468,0.06387488,0.0186931,0.00440865,0.04149254,0.01593935,0.03189555,-0.05298796,-0.21405013,-0.05252095,-0.05228579,-0.02454804,-0.00933237,-0.00390908,0.02772851,0.038195,-0.00694961,0.08007149,0.05393678,-0.06372193,0.00586653,0.01115991,0.00414026,-0.02775761,-0.04860094,-0.03674106,-0.0506743,0.04538892,-0.05957194,0.01319818,-0.02945866,-0.05734222,0.07112315,-0.04054146,0.13952081,0.04112379,-0.00336597,-0.00513485,0.09068662,0.02767592,0.01086694,-0.07489868,0.09895413,0.06270954,-0.07875529,0.03808742,-0.04550723,0.00479663,-0.0306219,0.01356397,-0.01117901,-0.07221726,-0.01093414,0.02550577,-0.03281346,-0.03228704,-0.01692692,0.02920426,0.01540012,0.01507224,0.05056171,0.06283052,0.07064137,0.0184633,-0.05944419,-0.01878574,0.01508656,-0.00419921,0.00258424,-0.0428168,0.08551774,-0.01706102,0.05318379,0.00664418,0.00480981,-0.06467534,0.02777639,0.03467521,-0.01327701,0.10911027,-0.01581629,0.03722057,-0.00391741,0.02245371,0.00071622,-0.0400201,0.0160341,0.01932362,0.0207714,-0.04218537,0.09493533,0.03690867,0.06654231,0.03527584,0.05097579,0.01073934,0.02486582,-0.01732028,0.0318845,0.02474911,-0.03851047,0.05907078,0.07157365,0.0491063,-0.2381238,0.00596708,-0.01535864,0.00994776,-0.04096772,-0.0287504,0.00744408,-0.01165743,0.03938777,-0.05155136,0.02102219,0.03012654,0.01665531,-0.07405082,-0.00900757,-0.01248565,-0.02978295,-0.03424149,0.12670426,0.00251066,0.0621331,0.0684457,0.25403616,0.01682578,-0.0610579,0.0055137,0.03815305,-0.0139031,0.02475956,0.02395442,-0.01392942,0.00983872,0.0009492,0.07061274,-0.01345337,0.05415735,-0.06132084,-0.01405443,0.02346841,0.04099248,-0.07520913,0.03351373,-0.01898062,0.0108891,0.10279408,0.01154547,-0.00099506,-0.03559341,0.05845737,0.03016018,-0.04554325,-0.07309671,-0.05136525,-0.00036214,-0.04990061,0.00954891,-0.00253317,-0.01602862,0.01231757,-0.01223798,0.04475299,-0.00824114,0.04713583,-0.01235094,-0.00882558],"last_embed":{"hash":"imh0nu","tokens":315}}},"text":null,"length":0,"last_read":{"hash":"imh0nu","at":1753423446169},"key":"notes/saliu/Correct, Fix Errors in Lottery, Lotto Results Files.md#Correct, Fix Errors in Lottery, Lotto Results Files#[Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)#{13}","lines":[47,52],"size":639,"outlinks":[{"title":"Keep lotto, lottery data files in good shape, in correct formats.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":1},{"title":"Forums","target":"https://forums.saliu.com/","line":3},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":3},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":3},{"title":"Contents","target":"https://saliu.com/content/index.html","line":3},{"title":"Home","target":"https://saliu.com/index.htm","line":3},{"title":"Software","target":"https://saliu.com/infodown.html","line":3},{"title":"Search","target":"https://saliu.com/Search.htm","line":3},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":3},{"title":"PARSEL software corrects, fixes most errors in lotto, lottery data files, results, drawings files.","target":"https://saliu.com/bbs/messages/HLINE.gif","line":5}],"class_name":"SmartBlock"},
