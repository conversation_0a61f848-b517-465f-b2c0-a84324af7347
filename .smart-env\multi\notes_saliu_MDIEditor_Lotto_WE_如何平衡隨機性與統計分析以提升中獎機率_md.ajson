
"smart_sources:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md": {"path":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10689279,-0.00581519,0.00583001,-0.02334081,0.01377805,0.02177174,0.01565248,-0.02081821,0.04400792,-0.04647918,-0.01414877,-0.07839727,0.04138818,-0.00782058,0.05597508,-0.04948419,0.01165817,-0.02536497,-0.05307985,-0.05042339,0.08005986,-0.028299,-0.03001059,-0.04389457,0.05514411,-0.01539433,0.02419282,-0.02591301,-0.00982494,-0.16971032,0.01533731,0.00383267,0.01611676,-0.05202472,-0.01504541,-0.00070134,-0.01710337,0.05993674,-0.02662295,0.02467103,0.01148211,0.00352494,0.03365,-0.03483362,0.02356778,-0.04878009,-0.01833056,-0.05915667,0.05190194,-0.01950446,-0.05484629,-0.02802904,0.0374002,0.01974985,0.03910051,0.04209978,0.01236923,0.05971053,0.05678039,-0.00148359,0.03259154,0.04627885,-0.20461099,0.03031249,-0.0496016,-0.04783193,-0.01666483,-0.04773326,0.02762553,0.05060558,-0.02340685,0.00213602,-0.06206073,0.04473788,0.01966849,-0.03923345,-0.00352217,-0.05327288,-0.06224269,0.00877256,-0.02145584,-0.01123512,0.01548463,-0.0296896,-0.03036457,0.06869581,0.01607361,0.00142329,-0.0251072,-0.00831146,0.02384908,-0.01963329,-0.00595172,0.09239855,-0.04857229,0.01667041,0.04068315,0.05950674,-0.06567516,0.11538976,0.01704505,0.03748737,0.00863782,-0.05918616,0.01998242,0.00544526,0.02570092,-0.03112774,-0.03168691,0.03163613,-0.0012655,-0.00454674,0.03380167,-0.04320581,-0.01045366,0.01909944,0.04925343,-0.00059422,-0.06332862,-0.00013359,0.00310129,0.01250333,-0.00640274,-0.00999592,0.01004448,0.00647711,0.04382175,0.06506315,0.05547992,0.01718019,0.01450429,0.01866538,-0.09839724,-0.03097794,0.02437102,0.00012119,-0.00832069,-0.01956566,0.04085013,-0.02633372,-0.05545884,-0.0500691,-0.01732983,-0.12030921,-0.06642154,0.09375598,-0.04181291,-0.02185613,0.00957613,-0.07214064,0.01186189,0.04533572,-0.04423231,-0.01224009,-0.00809057,0.01038376,0.05673509,0.14336161,-0.01969378,-0.0356176,-0.00387187,-0.01982459,-0.06698013,0.14947203,-0.00425006,-0.07677889,-0.04679246,0.01949996,0.00803922,-0.04637252,0.00969221,-0.02349789,-0.03971655,0.04983595,0.09387103,0.00155195,0.02127913,-0.03364681,-0.06343187,-0.02172366,-0.01855617,0.02046647,-0.10878793,0.03619856,-0.01004086,-0.0781868,0.02170252,-0.01580904,0.03345109,0.01239022,-0.02314751,0.01815424,0.07173984,0.05307496,-0.06929635,-0.03769922,-0.05641645,-0.03950633,0.0372986,-0.04157205,0.11110841,0.00328931,-0.04554361,-0.01953476,-0.02203407,0.04045974,0.00059357,-0.01461234,0.01976686,0.05529713,-0.01093408,0.06198125,0.0086771,0.03759497,0.03091021,0.04028811,-0.01098873,0.01292344,0.0104002,0.01868306,0.05553684,0.02968516,-0.08711368,-0.23797578,-0.04349419,-0.02652032,-0.05318522,0.00577979,-0.02637997,-0.00759596,0.00461587,0.0894575,0.08587487,0.07377816,0.03691309,-0.06241589,0.02833598,-0.01599564,0.00612471,0.02568128,0.01095375,-0.02661779,0.01376541,0.05200498,0.09185573,-0.03320458,0.03698742,0.0613106,-0.03419736,0.11759409,-0.02971688,0.01091097,0.0518726,0.09542631,0.06325322,0.01071007,-0.07725374,-0.00435293,0.02496995,-0.11705787,-0.00310445,-0.0937538,-0.01532211,0.00640333,0.03127546,-0.02828876,-0.0818092,-0.02105602,-0.02861322,-0.01745408,-0.00501886,-0.06439955,0.01411761,-0.03392734,0.00797766,0.0094604,0.01440012,0.02065551,-0.04081848,-0.01300316,-0.05895645,-0.0487447,0.01869421,-0.0050968,-0.01077559,0.06484189,-0.04185872,-0.02943435,0.01356458,-0.00876495,-0.00500153,-0.00959756,0.06826727,-0.05607499,0.11315543,-0.00163014,-0.00901155,-0.0257751,0.00228928,-0.05223262,-0.02054279,0.00820377,-0.07065405,0.04825462,0.05273512,0.0155858,0.07682692,0.06527728,0.04988147,0.01969513,0.00295146,0.01046239,-0.00649213,0.01938909,-0.02983025,-0.01528337,-0.01695778,0.05347704,0.01193356,-0.30417788,0.01097772,-0.01901636,-0.00266543,-0.0005052,0.02881839,-0.00118205,0.01607456,0.01985054,0.02434925,0.02023687,0.07845915,-0.01186528,-0.05545664,-0.03900679,-0.02819062,-0.00432096,0.01348367,0.07292116,-0.01422648,-0.00425671,-0.02207656,0.23802495,0.04115614,0.02020453,-0.01888871,0.00460477,-0.00687284,0.00600526,0.00097038,0.00049777,0.0016416,0.10497442,0.00529777,0.04640184,0.07767705,-0.02208126,0.04083216,-0.00474949,0.01923475,-0.02659673,0.03480133,-0.09815035,0.01202163,0.12340746,0.04350632,-0.0163126,-0.05439893,0.01458362,0.10466614,-0.01445023,-0.01163779,-0.01115382,0.01908142,-0.02993777,0.0052745,0.00940555,-0.00441633,0.03718781,0.05060108,0.05916091,0.01241618,0.02313319,0.07438708,0.02864417],"last_embed":{"hash":"ns876r","tokens":480}}},"last_read":{"hash":"ns876r","at":1753495687683},"class_name":"SmartSource","last_import":{"mtime":1753431474625,"size":6784,"at":1753495676958,"hash":"ns876r"},"blocks":{"#":[1,48],"##{1}":[7,14],"##{2}":[15,20],"##{3}":[21,26],"##{4}":[27,31],"##{5}":[32,35],"##{6}":[36,40],"##{7}":[41,48]},"outlinks":[]},"smart_blocks:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md#": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1057727,-0.00712239,0.00426814,-0.02330543,0.00940395,0.02227932,0.01657806,-0.01961636,0.04429475,-0.0453831,-0.0142054,-0.07795832,0.04507997,-0.00981894,0.05584336,-0.04948908,0.01176316,-0.02649731,-0.05502408,-0.04874904,0.08057314,-0.02777593,-0.03006082,-0.04308303,0.05579871,-0.0138711,0.02455046,-0.0274225,-0.00987137,-0.17013967,0.01489961,0.00264864,0.01863056,-0.05299081,-0.01398034,-0.00029464,-0.01726978,0.06282289,-0.02766704,0.02412426,0.01141889,0.00258589,0.03305311,-0.03198312,0.02354528,-0.04629765,-0.0195782,-0.0614284,0.05029451,-0.02039856,-0.05520925,-0.03022747,0.03647563,0.01932343,0.03773585,0.03892545,0.01484253,0.06009443,0.0581154,-0.00206197,0.02887003,0.04798731,-0.20426834,0.03198876,-0.05091316,-0.04752837,-0.0163062,-0.05001518,0.03024749,0.05109512,-0.02248646,0.00261335,-0.06060817,0.04322669,0.0180886,-0.03601166,-0.00429387,-0.0576805,-0.06061991,0.00849408,-0.02350452,-0.01157206,0.01633188,-0.028388,-0.02758558,0.06729891,0.01502633,-0.00074681,-0.02404789,-0.00696796,0.02383356,-0.02191885,-0.00357819,0.09358928,-0.04920839,0.0139475,0.04037504,0.05778894,-0.06554639,0.11655651,0.01774047,0.03701172,0.00925714,-0.06001455,0.01622983,0.00494766,0.02631536,-0.03251849,-0.03092723,0.03153282,-0.00270273,-0.0060401,0.03574831,-0.04511062,-0.01110484,0.01817627,0.04934045,-0.00375462,-0.06246999,0.00256469,0.00307529,0.01262176,-0.00665135,-0.01083084,0.01108828,0.00746767,0.04305339,0.06634429,0.05565856,0.01706953,0.01417067,0.01900278,-0.09910402,-0.03004663,0.02546424,-0.00077346,-0.00971289,-0.01974292,0.04109942,-0.02466258,-0.05381073,-0.04891925,-0.01508713,-0.12333465,-0.06651329,0.09468637,-0.04035155,-0.02160611,0.00777144,-0.07150889,0.00893143,0.04335541,-0.04315859,-0.01267548,-0.00735748,0.00986774,0.05520645,0.1430696,-0.01996056,-0.03332306,-0.0062872,-0.01914049,-0.06790369,0.14861263,-0.00449404,-0.07651599,-0.04683665,0.01882847,0.00707984,-0.04477283,0.01174442,-0.02130431,-0.03867171,0.05011487,0.09326725,0.00276539,0.02286638,-0.0358697,-0.06372712,-0.02257853,-0.01965673,0.01847505,-0.10889632,0.03565324,-0.00915522,-0.07682802,0.02319111,-0.01861396,0.03275511,0.01358479,-0.02478797,0.01802088,0.07157266,0.05459587,-0.06964951,-0.03919244,-0.05584562,-0.03966463,0.03707824,-0.04045336,0.10970175,0.00348103,-0.04649751,-0.01783993,-0.02173155,0.04400593,0.00175404,-0.01644552,0.0220992,0.05601478,-0.0105655,0.06043901,0.00982829,0.03822767,0.03282803,0.04023338,-0.01225592,0.01129445,0.01131184,0.01555556,0.05589114,0.03311594,-0.08707505,-0.23733149,-0.04216079,-0.02518364,-0.05100496,0.00460335,-0.03087107,-0.00879081,0.00356245,0.08954173,0.08793372,0.07278199,0.03802383,-0.05921691,0.02689863,-0.01516776,0.0052716,0.02572562,0.01079049,-0.0237879,0.0157601,0.05110757,0.09327384,-0.03022249,0.03488104,0.06209918,-0.03197251,0.11876086,-0.03121443,0.00875125,0.05501568,0.09450729,0.06193863,0.00942859,-0.07549023,-0.00344692,0.02444617,-0.11815805,-0.00366432,-0.09118864,-0.01608274,0.00618542,0.03134672,-0.02973464,-0.08093246,-0.02091073,-0.02794008,-0.0170057,-0.0046755,-0.06517137,0.01506082,-0.03003184,0.00845221,0.01327757,0.01335612,0.02059232,-0.04049081,-0.01397572,-0.05942271,-0.05004147,0.0198102,-0.00267031,-0.01132453,0.06473798,-0.04079821,-0.0300444,0.0134426,-0.00816145,-0.00583868,-0.00985545,0.06747813,-0.05459055,0.11323138,-0.00098379,-0.00871592,-0.02662629,0.00296283,-0.05234325,-0.02192793,0.00773575,-0.0706319,0.04890845,0.05468513,0.01726394,0.07676923,0.06440037,0.05081232,0.01956116,0.00209211,0.01019328,-0.00612339,0.01939032,-0.02835089,-0.01571477,-0.01554652,0.05233809,0.01277611,-0.304764,0.00869265,-0.02175952,0.00053813,-0.00169891,0.02727314,-0.00183646,0.01468057,0.02055324,0.02583488,0.01772386,0.07940643,-0.01055243,-0.05598941,-0.03762249,-0.02813261,-0.00487024,0.01286911,0.07442432,-0.01394079,-0.00355541,-0.02339689,0.23861557,0.03900682,0.0191347,-0.0200178,0.00626959,-0.00597396,0.00650876,-0.00025031,-0.00091381,0.00184841,0.10652351,0.00951849,0.04542844,0.07610241,-0.02406853,0.03707991,-0.00480682,0.01665616,-0.02845184,0.03370921,-0.09597877,0.01085138,0.12395202,0.04339045,-0.01650831,-0.05573501,0.01545493,0.10340864,-0.0168908,-0.01377718,-0.01306129,0.01963875,-0.03061092,0.00605317,0.00840053,-0.00468955,0.03853979,0.04916942,0.0565408,0.01446864,0.02400792,0.07699727,0.02960607],"last_embed":{"hash":"ns876r","tokens":485}}},"text":null,"length":0,"last_read":{"hash":"ns876r","at":1753495687010},"key":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md#","lines":[1,48],"size":2846,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09781446,-0.00064431,0.00677209,-0.02801678,0.00349215,0.03377008,0.0244121,-0.00396307,0.04251456,-0.04052982,-0.02277449,-0.0614164,0.04197859,0.00356651,0.06390809,-0.0514495,0.00752845,-0.02464707,-0.05301741,-0.05154844,0.08297805,-0.01483852,-0.03445401,-0.04094623,0.05130348,-0.01726078,0.02832535,-0.02665764,0.00046924,-0.17549285,0.01661506,0.01102174,0.01566509,-0.05169433,-0.01442939,0.00867358,-0.01724892,0.06709068,-0.03544781,0.03332527,0.00946862,-0.00249499,0.03760544,-0.05665221,0.02025361,-0.04599832,-0.01380346,-0.06003685,0.04357429,-0.01041105,-0.0519954,-0.03893674,0.04566027,0.03727822,0.03966451,0.05092257,0.00092553,0.06185341,0.06408028,-0.01529882,0.02471151,0.03478607,-0.21181369,0.02965605,-0.06451711,-0.04558198,-0.00774734,-0.04311422,0.02482697,0.05451344,-0.00749985,0.00125898,-0.05611022,0.04199725,0.01204808,-0.041146,-0.01334228,-0.05953608,-0.06195852,0.00706117,-0.00862303,-0.01254517,0.01813105,-0.02564638,-0.01902735,0.05333926,0.02172646,-0.00477379,-0.0256476,-0.004684,0.02399622,-0.0272506,-0.01140596,0.09917098,-0.03686756,0.01402166,0.04418353,0.05539864,-0.05212479,0.11144856,0.03062704,0.05034479,0.00621801,-0.06298901,0.02605406,0.00607741,0.01539718,-0.03840778,-0.04115785,0.0194379,0.00953466,0.0068929,0.03993405,-0.04437383,-0.01770695,-0.00095167,0.04694657,0.00450081,-0.04928873,-0.01378411,-0.00240105,-0.00213673,0.0041672,-0.00537538,0.02239281,0.00178065,0.04007637,0.07246602,0.0602925,0.02395142,0.02449825,0.00780086,-0.10683744,-0.03184799,0.03214142,0.00204488,0.0035312,-0.01687703,0.04682017,-0.0284002,-0.06489813,-0.04366403,-0.01485089,-0.11673595,-0.07206835,0.09935862,-0.04009159,-0.01386384,0.00892029,-0.06996624,0.0073607,0.04065492,-0.0359989,-0.01300093,-0.00234398,0.01671575,0.05614785,0.14849927,-0.02009477,-0.03729637,-0.01133141,-0.03226407,-0.06712619,0.14156641,0.002102,-0.0795927,-0.05566755,0.02347013,0.00029331,-0.04121586,0.00631523,-0.03422868,-0.04233222,0.04716602,0.09436494,0.01509726,0.01500156,-0.01178142,-0.06573825,-0.02295805,-0.01549352,0.01197146,-0.10527508,0.01512265,-0.00407346,-0.08376136,0.02870371,-0.01670228,0.03128937,0.01575929,-0.01725163,0.0118766,0.06208657,0.05655789,-0.05509057,-0.04486059,-0.0595185,-0.02897867,0.04774506,-0.0420388,0.09654643,-0.00383436,-0.03960226,-0.02328892,-0.03428835,0.04820869,-0.01622581,-0.00664774,0.03253208,0.04144828,-0.01821846,0.06626253,0.01374871,0.04626906,0.0251948,0.0325818,-0.01416203,0.01892114,0.01235096,0.01854199,0.05712368,0.0190368,-0.0849923,-0.22995608,-0.03269843,-0.02238922,-0.04531528,0.00696471,-0.01755345,-0.01338548,-0.0085079,0.0789522,0.09999684,0.06843261,0.03487831,-0.03102978,0.02797898,-0.01344966,0.00772226,0.01222735,0.01649897,-0.03459969,0.01593169,0.03817406,0.08951508,-0.02965475,0.03072699,0.06801859,-0.04590448,0.12449995,-0.02225345,0.00530039,0.03883126,0.09663346,0.05487904,0.0029029,-0.05507696,0.00285679,0.02578649,-0.12020334,-0.01556421,-0.09207552,-0.02876637,-0.01098615,0.02950785,-0.03110884,-0.07998566,-0.01762439,-0.0175657,-0.01836161,0.01019689,-0.07685588,0.02050229,-0.02327743,-0.00468261,0.01350137,0.01005653,0.0195901,-0.05222896,-0.02900294,-0.064992,-0.0506919,0.02567765,-0.02190912,-0.00946997,0.05235797,-0.04465944,-0.03153557,0.01613729,-0.00042531,-0.01291875,-0.00705098,0.07098294,-0.05775221,0.12621541,-0.01126096,0.00407044,-0.0278087,0.00417378,-0.04083688,-0.01747625,0.02384132,-0.07279773,0.03786428,0.03403659,-0.01232526,0.0781863,0.07037589,0.04255513,0.02828061,0.00168043,0.0148836,-0.00929883,0.01233173,-0.01992799,-0.01547654,-0.0015124,0.04600171,0.00277454,-0.29840657,0.01586556,-0.00665503,0.00476766,0.00531552,0.01254471,-0.00374489,-0.00183943,0.02902418,0.01984062,0.02308626,0.08035813,-0.01201924,-0.04035928,-0.04284845,-0.01693614,-0.01350458,0.0064323,0.06841701,-0.01584958,0.00523399,-0.01258455,0.23895504,0.04250753,0.00557182,-0.02662267,0.00176657,-0.01108504,-0.00712175,-0.00045361,-0.01335296,-0.01056855,0.119853,-0.00384576,0.04702627,0.0893556,-0.02481835,0.04346308,-0.00550255,0.03183553,-0.0304294,0.03626724,-0.10314093,0.00729959,0.12366558,0.03759335,-0.01383932,-0.05432403,0.00210969,0.11450204,-0.00966345,-0.01414751,-0.02036048,0.02740746,-0.01831007,0.00126579,0.01666719,-0.00562807,0.03751518,0.06583846,0.04722333,0.01189543,0.01189634,0.07696357,0.02375239],"last_embed":{"hash":"41yv48","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"41yv48","at":1753495687186},"key":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{1}","lines":[7,14],"size":661,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{2}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08301914,0.01916425,0.00122111,0.00039315,0.01908761,0.00522733,0.0370278,-0.02314935,0.05269593,-0.04170411,-0.00746565,-0.04973124,0.03030594,-0.02146781,0.05243255,-0.01668324,-0.00297025,-0.00706395,-0.03345367,-0.05357058,0.07168991,-0.04034743,-0.03163541,-0.04577631,0.07177182,0.0108108,0.02539768,-0.01999458,-0.01836578,-0.18652704,-0.00443068,0.00700592,0.0316681,-0.0718307,-0.01568471,0.01328151,-0.02781726,0.04854395,-0.02865069,0.01675715,0.03832246,0.00229473,0.05812819,-0.04195843,0.01450083,-0.02797978,-0.02014631,-0.03455155,0.04282183,-0.01549583,-0.06563167,-0.03081687,0.03085189,0.02686343,0.04310662,0.0561429,0.03211947,0.08796547,0.03924248,-0.01603348,0.03641961,0.06725286,-0.19235881,0.02935323,-0.04481784,-0.06749297,0.01138031,-0.07090648,0.03187675,0.05948098,-0.02753406,-0.01074782,-0.04804192,0.04850899,-0.01143756,-0.04698905,0.02916602,-0.05407932,-0.03739024,-0.00628129,-0.04465545,-0.01295095,0.01219378,-0.01314884,-0.0162098,0.04300816,0.02603668,0.01149757,-0.04610406,-0.03887445,0.02682607,-0.01378677,-0.02066743,0.06810422,-0.04320509,0.03177498,0.04983921,0.05935253,-0.0809694,0.10311454,0.02912943,0.05454196,0.00185186,-0.06444171,0.03423322,-0.0081696,0.01495605,-0.05488451,-0.06383567,0.00115591,0.01158323,0.01457647,0.0394284,-0.0469732,-0.00632227,0.01882044,0.05519434,0.00916515,-0.0657493,-0.02789097,0.02812933,0.03066821,0.02974939,-0.01065773,0.0025187,-0.05261153,0.03818158,0.07624777,0.06802185,0.00201378,0.02459076,0.00368171,-0.09478481,-0.0475756,0.02727837,0.00723933,0.00662746,-0.00674639,0.01998719,-0.05105015,-0.06448509,-0.04882132,-0.02098541,-0.11013417,-0.03849535,0.07846082,-0.04528501,-0.02142309,0.01287299,-0.04029211,0.00000188,0.02907369,-0.02080242,-0.02143014,-0.00881451,0.00874136,0.0830132,0.14272362,-0.01608152,-0.03713332,-0.00490629,-0.02422402,-0.08519034,0.15606314,-0.03793224,-0.06267422,-0.02194693,0.03694403,0.00956303,-0.05117702,-0.00395943,-0.01287018,-0.03814428,0.02504517,0.10324282,0.00362617,0.02498153,-0.00056027,-0.03359363,-0.00694549,0.01161243,-0.002774,-0.09985096,0.01360144,-0.01104776,-0.07236033,0.04635166,0.02445448,0.04228686,0.00729925,-0.06252905,0.03418374,0.01525772,0.04985806,-0.07047275,-0.03062757,-0.08318332,-0.05048416,0.05425801,-0.03153382,0.09865253,-0.00765858,-0.02384385,-0.03130549,0.01228998,0.05610735,0.02454316,-0.01370862,0.00194988,0.04699173,-0.04737337,0.07845168,0.00593776,0.05168536,0.02032511,0.02865709,-0.00166124,0.0524064,-0.04262374,-0.00569395,0.06099771,0.00561991,-0.08480459,-0.22688954,-0.00545334,-0.01301452,-0.07596772,0.02951473,-0.03358128,0.00032494,0.00464182,0.07645549,0.05563856,0.04757443,0.0322692,-0.04213331,0.02125635,0.00136371,-0.03364083,0.02768017,-0.00564353,-0.04056019,-0.00023395,0.03449561,0.07506607,-0.02687297,0.02392427,0.04865564,-0.06978931,0.10637309,-0.00349863,0.00985915,0.01879149,0.08143631,0.02004169,0.00400346,-0.05872755,0.01149613,0.01883383,-0.10524167,-0.02128926,-0.07272164,-0.04233683,-0.0031367,0.03747083,-0.07560234,-0.08130719,0.0158492,-0.00994105,-0.01628869,0.00997216,-0.05642899,0.02753875,-0.00321005,0.02310318,-0.02271492,0.04014926,0.01482591,-0.0646233,-0.05201021,-0.03928223,-0.03981423,0.02645217,-0.03305914,-0.01349652,0.05613403,-0.01833392,-0.02271438,0.03266011,-0.01074455,-0.01242593,0.01932357,0.07276154,-0.08340368,0.11683515,0.0003945,-0.01051935,-0.00698393,0.00896372,-0.03001115,-0.05655747,0.00934072,-0.03878505,0.05264335,0.0343235,-0.00460804,0.03832828,0.06154443,0.0515225,0.01551124,0.01354787,0.01119153,-0.01748169,0.02024699,-0.02285929,-0.01476009,-0.02516442,0.03360881,0.00201206,-0.30425331,0.0018691,-0.03344241,-0.01353046,-0.01564264,0.0187738,0.01278919,-0.00952089,0.01242652,-0.01135392,0.00365725,0.08716401,0.01693608,-0.04029112,-0.01494816,-0.03141691,0.00218989,0.0282315,0.05135012,-0.01073536,0.00088874,-0.0104217,0.24661553,0.045884,0.01114008,0.03179736,0.02314842,-0.01610982,0.00818124,-0.00746808,-0.00195796,-0.01419791,0.10720754,0.00405891,0.05539929,0.07603478,-0.04005253,0.04828864,0.0114605,0.04117922,-0.02439786,0.02019326,-0.10950911,0.02686727,0.13559636,0.03542416,0.01020513,-0.07650082,0.02459323,0.10362902,-0.0067531,-0.00717184,-0.01888745,0.02439457,-0.03139672,0.02204091,0.01055245,-0.00925615,0.04971927,0.0573476,0.03128225,0.01572837,0.03579974,0.07682934,0.04230043],"last_embed":{"hash":"1s4a5h2","tokens":342}}},"text":null,"length":0,"last_read":{"hash":"1s4a5h2","at":1753495687342},"key":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{2}","lines":[15,20],"size":388,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11233542,0.01178512,0.02250699,-0.02835875,0.02212237,0.03564074,0.03403781,0.00634619,0.08208258,-0.01211285,0.00405272,-0.04701438,0.03186606,0.01059041,0.04877655,-0.05979559,0.02440006,-0.03633416,-0.02442974,-0.05631586,0.07349735,-0.03892231,-0.05967233,-0.06461293,0.04275841,0.02410123,0.0298884,-0.06027666,-0.02336587,-0.15989934,0.01249363,-0.01375971,0.01717955,-0.06860139,-0.04561727,-0.00545783,-0.01731774,0.06864567,-0.02374908,0.03976577,-0.00414031,-0.02391895,0.03990735,-0.0379505,0.01868026,-0.04381324,-0.04124315,-0.0634781,0.03945793,-0.0231675,-0.05838597,-0.01991071,0.05563138,0.00432312,0.03691816,0.07286439,0.03240646,0.0447429,0.05674742,-0.01992201,0.01978144,0.01407163,-0.20437516,0.02844094,-0.03458992,-0.01391515,0.01766572,-0.0384138,0.03485578,0.04270655,-0.04335964,0.02747219,-0.05794317,0.04396295,0.03307793,-0.0323644,0.01585222,-0.04146143,-0.07055213,0.00969038,0.01557968,-0.03010461,-0.01717632,-0.01914261,-0.02833241,0.06535896,-0.00046168,-0.00043215,-0.02147677,-0.00477278,0.02460901,-0.04824185,0.0032487,0.05611855,-0.03285622,-0.00760288,0.06257156,0.03074074,-0.08341895,0.08683828,0.05142584,0.05239501,0.0334352,-0.04050481,0.04457851,-0.02000571,0.02332417,-0.0206688,-0.03910787,-0.00271548,0.00059871,0.0242029,0.02954526,-0.0462813,-0.00682353,0.00272969,0.05119549,0.03389103,-0.03194395,-0.00502964,0.00808411,0.00480321,0.00745757,-0.01286079,0.03008164,-0.00803712,0.00788811,0.07272474,0.05061775,0.02029197,0.01037413,0.00150453,-0.06221588,-0.06447104,0.0198562,0.03810047,-0.00874605,0.02523292,0.05503421,-0.01283182,-0.04440181,-0.04982958,-0.01577127,-0.12412953,-0.07075133,0.09727692,-0.07252713,0.0049914,0.01403251,-0.05355967,0.01518546,0.03499692,-0.02179787,-0.03034945,-0.01120279,-0.01844172,0.06240949,0.14912474,-0.00392352,-0.03980864,-0.0569832,-0.01594252,-0.07441027,0.14910093,0.03913924,-0.06642374,-0.04758893,0.0163578,0.00412986,-0.06801383,-0.0186048,-0.04419012,-0.05648286,0.02632602,0.10462117,0.01247213,0.03070636,-0.0100989,-0.04709559,-0.00815795,0.00974036,0.01610935,-0.06606998,0.02082351,-0.00579024,-0.10308951,-0.01227377,-0.05132095,-0.01837604,-0.01930217,-0.0271142,0.02215477,0.04574015,0.05270304,-0.06331643,-0.04143698,-0.06570963,0.00072181,0.02501458,-0.04780379,0.12496244,-0.00036717,-0.00281114,-0.03644185,-0.00296476,0.06522247,0.01349571,-0.01942032,0.0245328,0.04263543,-0.03305039,0.04078552,0.03052413,0.01291239,0.01866831,0.06040373,-0.03189751,0.03114197,-0.00933914,-0.00088252,0.03546184,0.0494711,-0.09181203,-0.24000704,-0.06123187,-0.01689059,-0.04412716,0.0105899,-0.01403609,0.01603626,-0.00014261,0.08600123,0.09673554,0.05488346,0.0112336,-0.06556378,0.03809436,-0.01830188,-0.02102954,-0.00105439,-0.00186568,-0.03158088,0.01587826,0.05632019,0.09846572,-0.04188524,0.02017106,0.03477668,-0.01780784,0.13724434,-0.03169068,0.00164293,0.00512396,0.08888879,0.05222114,0.00013636,-0.02213953,0.02632267,0.05043326,-0.09901275,-0.03478541,-0.07619023,-0.01758601,-0.03868484,0.04233536,-0.03651562,-0.08672155,-0.04038259,-0.04834031,-0.02167788,-0.03909361,-0.06126934,0.05148008,-0.03428575,0.00039082,0.01569124,0.01786289,0.04289553,-0.00313653,-0.03598052,-0.03340055,-0.05245538,0.03816783,-0.02336741,-0.02521668,0.03289513,-0.01729832,-0.02142145,0.00813584,-0.02372851,0.02920368,0.01917345,0.05835089,-0.04636306,0.11441886,-0.00880873,-0.02683464,-0.00046023,0.03038562,-0.04969078,-0.02359712,-0.00898464,-0.04583646,0.08556725,0.04618081,-0.02596425,0.04272595,0.04784989,0.00664689,0.05698495,0.03280612,0.00177353,-0.02558641,-0.04855708,-0.00809607,-0.02931615,0.00109993,0.06937393,-0.01337972,-0.29096356,-0.00069983,0.00071241,-0.00349843,0.01573473,0.02510025,0.00114539,-0.02288622,0.00427902,0.04827551,0.02329074,0.04679988,-0.00287276,-0.03967165,-0.02486649,-0.01312688,-0.01571258,0.04919334,0.05324002,-0.0481976,-0.01306682,-0.01269269,0.23512864,0.02783974,0.00046308,-0.02177787,0.01210934,0.025131,0.01810475,0.00119833,0.01668662,0.01646361,0.09466325,-0.02797735,0.01295694,0.0469514,-0.02919148,0.0727703,0.0237855,0.03731642,-0.0242231,0.04042527,-0.10571258,0.02051306,0.11033432,0.01207395,0.01556581,-0.06982169,0.04305903,0.12094375,0.00229978,0.0440728,-0.00409925,-0.0018121,-0.00369714,-0.02163626,0.00390788,-0.01552641,0.02334893,0.04307613,0.0313281,0.02346302,0.04029507,0.0808485,0.02582864],"last_embed":{"hash":"19lv0rq","tokens":331}}},"text":null,"length":0,"last_read":{"hash":"19lv0rq","at":1753495687441},"key":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{3}","lines":[21,26],"size":364,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{4}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.14244367,-0.00247862,-0.02363841,-0.01135114,0.02779861,0.03411748,0.02181568,0.00396067,0.04070412,-0.0191035,0.00497063,-0.05847776,0.04448659,0.00739036,0.06467552,-0.04473959,-0.0007275,-0.0139621,-0.045897,-0.07342444,0.06130414,-0.05036124,-0.04006946,-0.0509884,0.07782249,0.00334703,0.02673279,-0.01836609,0.00360539,-0.17104609,0.01492981,-0.00857434,0.04578327,-0.02838543,-0.00565775,0.00294648,-0.00411135,0.03110189,-0.03329164,0.02389607,0.01971692,0.00109447,0.05862299,-0.01841951,0.02915949,-0.05118362,-0.01918031,-0.04308795,0.03783645,-0.02997497,-0.09491955,-0.02900654,0.0435551,0.02052493,0.04034375,0.03113335,0.03713816,0.03187887,0.04749231,-0.03409278,0.04016833,0.05236632,-0.20127308,0.02557864,-0.00971981,-0.02310534,-0.03604633,-0.03757405,0.03505558,0.0683549,-0.04508931,-0.00553869,-0.07229036,0.03147633,0.02732926,-0.04163733,0.02073684,-0.03234145,-0.05195361,0.00937389,-0.02920065,-0.00432772,0.01791557,-0.03605361,-0.0301613,0.0581838,0.0173327,-0.00237667,-0.03012853,-0.00662954,0.01786234,0.00067213,-0.01164812,0.06126412,-0.03183278,0.03898282,0.04512358,0.05485789,-0.04732526,0.11126478,0.0201441,0.03654959,0.01876746,-0.06349178,0.03092403,0.00560815,0.03687453,-0.04479665,-0.01560018,0.01437398,-0.00684745,-0.00507852,0.01212108,-0.0524701,0.00668299,0.00606241,0.03214902,0.03317166,-0.05959771,0.00049969,0.00924915,0.00296669,0.02982244,0.01387047,-0.00395367,-0.03452179,0.01411834,0.07945835,0.0569262,-0.02478414,0.02886154,0.01968518,-0.11204629,-0.00855277,0.00601679,-0.02236691,0.01043864,-0.00438384,0.06219526,-0.02463167,-0.05699888,-0.04836191,-0.02566648,-0.10553437,-0.07671623,0.12469567,-0.01577054,-0.00562767,-0.02195913,-0.05104215,0.0201161,0.03601456,-0.04218856,-0.02586878,0.00628447,0.03185991,0.05599067,0.12347469,-0.00930589,-0.02025057,-0.0286834,-0.03154583,-0.07629511,0.18903786,0.01925192,-0.06148057,-0.03418201,0.02802063,0.03291992,-0.05178588,0.03167111,-0.00071347,-0.02948565,0.05627364,0.07532732,-0.03948711,0.00088099,-0.02070281,-0.02780964,-0.0139681,0.00406181,0.01614046,-0.08909874,0.03002467,0.01576635,-0.06595057,0.00071197,0.01891048,0.02202409,-0.00572391,-0.0059101,0.00112362,0.04658513,0.067381,-0.08876019,-0.04478908,-0.03623893,-0.0193544,0.03874858,-0.05437784,0.1048618,-0.0200687,-0.04601256,-0.03018278,-0.03638625,0.00551928,-0.0002695,0.00249966,0.00054707,0.03831688,-0.00807882,0.05657759,0.00818077,0.04337674,-0.0064839,0.04748283,0.0013286,0.03242844,-0.00245897,0.00659862,0.03045843,0.02486619,-0.08666334,-0.2257738,-0.04407163,-0.00186026,-0.04171392,0.02659788,-0.02976404,-0.02948937,-0.01092128,0.09297434,0.05595925,0.05077436,0.03144073,-0.05388534,-0.00252106,-0.01041162,0.0302469,-0.00943791,0.00373021,-0.05851148,0.05297454,0.04290848,0.07167315,-0.04834833,0.0267856,0.08431422,-0.04000551,0.11165028,-0.00932389,0.02345504,0.04922751,0.06872068,0.03937501,-0.01535115,-0.07672999,-0.00611003,0.02641771,-0.08648181,-0.02224055,-0.0749558,-0.05648168,0.01026305,0.03446441,-0.02483647,-0.08534323,-0.03011978,-0.03265166,-0.01713939,-0.00772356,-0.03735536,0.00776314,-0.0432945,0.00759584,0.03064163,0.02693006,0.00826483,-0.04924281,-0.01942238,-0.0669361,-0.03609063,0.03477822,0.00550543,-0.03631666,0.05573991,-0.02780914,-0.02098014,-0.02400057,-0.0144067,-0.01510482,-0.01341241,0.06158081,-0.0551776,0.12656131,0.0332684,-0.0171506,-0.00003607,0.00225801,-0.04447872,-0.06320275,0.00306564,-0.05424379,0.02772137,0.01324853,0.01112003,0.06058903,0.07795156,0.03088187,0.02959695,0.02021208,0.03106887,-0.01201917,0.01522214,-0.05604184,-0.01046695,0.00392919,0.05662713,0.02915907,-0.30376232,0.03975955,-0.01893716,-0.00717634,-0.00063763,0.01838729,0.02890864,-0.01019464,0.00430134,-0.00677526,0.02099987,0.09000271,0.0223703,-0.03806606,-0.04734204,-0.03297961,0.00392738,-0.01045001,0.08496182,-0.00550578,-0.00860562,-0.00926737,0.23551276,0.0296601,0.04766161,-0.01929073,0.00954004,0.02501632,0.00676508,-0.01721131,0.01579806,-0.00529381,0.06740122,0.00172746,0.05264071,0.08086788,-0.03194925,0.03685644,0.00167288,0.04403601,-0.03743719,0.0073507,-0.11258814,0.00316005,0.15388325,0.0519159,-0.00794315,-0.08816249,0.02073365,0.12148982,0.00228793,-0.04185602,0.00146769,0.01411503,-0.0238797,0.01531457,0.00569253,-0.03005373,0.03199891,0.04557356,0.03888873,-0.0095117,0.00883079,0.07563218,0.00931427],"last_embed":{"hash":"1jtt0n2","tokens":218}}},"text":null,"length":0,"last_read":{"hash":"1jtt0n2","at":1753495687541},"key":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{4}","lines":[27,31],"size":239,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{6}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09790846,-0.00105517,0.02489931,-0.03970098,-0.00522439,0.06363871,0.05925776,0.01508379,0.08665992,-0.02395032,-0.01197299,-0.07187017,0.01548677,0.00830745,0.02286079,-0.04903297,-0.00323941,-0.03808594,-0.03568553,-0.0453218,0.08608194,-0.04903003,-0.03407141,-0.0736489,0.08099992,-0.01248768,0.02254611,-0.03953246,-0.00041103,-0.15005869,0.01508567,0.02805625,0.01253704,-0.04068959,-0.05961686,-0.01880889,-0.02771386,0.05263077,-0.02687479,0.03299095,0.02143761,-0.0292525,0.04047115,-0.02954213,0.0372921,-0.06248565,-0.00494789,-0.05034264,0.08166293,-0.01506094,-0.03894164,0.00035338,0.0504491,-0.01456005,0.04521074,0.01208918,0.03212814,0.04887194,0.07725294,0.01218819,-0.00187554,0.03642178,-0.22207282,0.01688218,-0.04697511,-0.03265199,-0.02508465,-0.00882744,0.04972832,0.06515716,-0.03855959,0.02531417,-0.02344127,0.02184005,0.03707076,-0.04539409,0.00018153,-0.04664638,-0.09047315,0.02318324,-0.00437667,0.0087884,0.01832544,-0.04139441,0.000211,0.07450354,-0.00441276,-0.01600909,-0.02666132,-0.00042475,0.02211043,0.01603604,-0.0187985,0.06488847,-0.0655698,0.01808013,0.06465517,0.02810464,-0.09440368,0.08846726,0.05912979,0.03376479,-0.03399185,-0.04612304,0.0544809,-0.01202366,0.02682181,-0.01090035,-0.02017551,-0.01963416,-0.00718672,0.03032564,0.04267556,-0.04802144,-0.02743117,0.02402519,0.03730296,0.00089147,-0.04725747,0.01479356,0.03857149,0.01080735,-0.02227261,0.0119715,0.01208363,-0.01305524,0.00644882,0.06374048,0.04684285,0.02408627,0.02859994,0.0117256,-0.09460956,-0.0457317,0.00604945,0.02501224,-0.00843467,-0.00281575,0.0529275,-0.00788141,-0.0433977,-0.03983867,0.00714302,-0.11842293,-0.04910209,0.07208993,-0.00277633,-0.03952438,-0.00387106,-0.0277415,0.00318034,0.04508194,-0.02565389,-0.00963028,0.0087911,-0.0254119,0.06888565,0.16891971,0.00768075,-0.06066908,-0.04971204,-0.05102527,-0.0274547,0.16938613,0.01974732,-0.07543869,-0.04665035,0.01104292,-0.01642153,-0.10160336,-0.01127711,-0.04015141,-0.07761487,0.04037624,0.08975682,0.00375608,0.03990826,-0.03632964,-0.03862862,-0.00523603,0.01287925,-0.01934624,-0.06407675,0.00394451,-0.02541282,-0.07717049,-0.00678734,-0.05194093,0.00649329,-0.00351695,-0.047527,0.00838587,-0.0053047,0.05590539,-0.07563197,-0.0435026,-0.0593795,-0.02998257,0.03384702,-0.03527449,0.08902392,-0.01782649,-0.02623744,-0.02412843,-0.01656427,0.05817644,-0.01176833,-0.02928959,-0.01053284,0.04692192,-0.01190992,0.05387278,0.01948107,0.02257737,0.00619938,0.0420104,0.00390217,0.04690365,-0.02168264,0.01291779,0.06778568,0.03417395,-0.07385019,-0.22926825,-0.05058493,-0.03128053,-0.0592512,0.01334555,0.00791756,0.02609875,-0.03576782,0.09405269,0.07300422,0.06284894,0.00863026,-0.07368889,0.02902891,-0.01443077,-0.0124782,0.00062644,-0.04009146,-0.02801625,0.02017725,0.03716687,0.07035881,-0.07671412,0.02998987,0.05069786,-0.03127055,0.14049786,-0.00638025,0.0108491,-0.00424131,0.10930744,0.05267646,-0.02344011,-0.03590694,0.00815199,0.00375584,-0.07956905,0.02990143,-0.07834039,-0.03440759,-0.02369818,0.05797148,-0.01456259,-0.06968104,-0.05044682,-0.01189773,-0.02926117,0.00364431,-0.03321554,0.04119258,0.02169167,0.00946432,0.02648408,0.02086018,0.04525767,-0.01768959,-0.00258686,-0.04200721,-0.06036483,0.03067246,-0.01830813,-0.00266254,0.05602586,-0.02620276,0.00198886,0.02372403,0.00556195,-0.00972753,0.02329392,0.0584172,-0.06663228,0.10683194,0.01862642,-0.01169267,0.0310217,0.01163471,-0.03983103,-0.04429116,-0.0054297,-0.03290589,0.06006175,0.04524081,0.02583958,0.04819557,0.05357799,0.02672384,0.07481216,0.04809486,0.0163665,-0.02542545,-0.04486784,-0.02589799,-0.05059125,-0.02199623,0.02641916,-0.00504118,-0.32615459,0.01619847,-0.00402126,0.00808991,0.00832489,0.00905678,0.00485725,-0.00464441,-0.03567915,0.02128771,0.02024953,0.05619866,-0.00744191,-0.04809402,-0.03918569,-0.01673139,-0.01882492,0.02236314,0.04420156,-0.01132308,0.00778516,0.03278546,0.2352186,0.0209259,0.01262781,-0.01434948,0.02419959,0.02048925,0.02468988,-0.00910492,0.0045162,-0.006143,0.08336968,-0.00163324,0.04647391,0.04330695,-0.01460102,0.05065093,0.02537968,0.00671952,-0.02525426,0.04816456,-0.0833364,0.00736974,0.11020498,0.03516501,-0.00573182,-0.04227468,0.03181311,0.07288047,-0.03943932,-0.02707197,0.00046099,0.00192922,-0.02534937,-0.01639492,0.00174418,-0.02734781,0.03052631,0.06967239,0.02341241,0.04959055,0.03045906,0.06020959,0.02276914],"last_embed":{"hash":"zcuhak","tokens":277}}},"text":null,"length":0,"last_read":{"hash":"zcuhak","at":1753495687603},"key":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{6}","lines":[36,40],"size":321,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{7}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10576508,0.00212524,0.00857993,-0.01872283,0.00318651,0.01728112,-0.00066137,-0.01343709,0.06008927,-0.0575102,-0.008814,-0.0723535,0.04972366,-0.00719756,0.05925274,-0.0480998,0.01383825,-0.04626013,-0.06415123,-0.05749862,0.07647425,-0.0460442,-0.02471167,-0.06852713,0.05727416,0.01023338,0.0290035,-0.03623106,0.00156742,-0.17000365,0.01673664,0.02850762,0.01623862,-0.043594,-0.03720484,0.00192015,-0.02135298,0.05720054,-0.03602486,0.03139526,0.00957409,-0.00803025,0.04175851,-0.0447101,0.02991989,-0.02359143,-0.0295229,-0.04221743,0.05365741,0.00088646,-0.0309196,-0.03127832,0.03106222,0.02773243,0.03269333,0.06239262,0.03164159,0.05576141,0.03677662,0.00080904,0.0337869,0.03482623,-0.20844515,0.01256879,-0.0362431,-0.04310921,-0.01305886,-0.04032399,0.03501938,0.04272645,-0.02742744,0.01324031,-0.05645134,0.05368501,0.02908979,-0.03428438,-0.00264199,-0.03769871,-0.0511977,0.02007084,-0.01591166,0.00676062,-0.01256151,-0.00694057,-0.01462303,0.08393404,0.02541956,0.00858156,-0.02742316,-0.01429806,0.00095554,-0.04086341,-0.00846814,0.06888022,-0.02898627,0.03855046,0.05111749,0.04632633,-0.07806961,0.10224837,0.03902806,0.04681427,0.01820517,-0.06659668,0.0169393,0.01325951,0.0350702,-0.03747204,-0.03173354,-0.01120074,0.00952585,0.02511786,0.00390229,-0.05862284,-0.02511517,0.00813515,0.04032215,0.00534989,-0.07299659,-0.00580291,0.00444232,0.01594608,-0.00146181,-0.0213532,0.01226871,-0.03854208,0.017762,0.07422299,0.06086503,-0.00253876,0.01318002,0.02653337,-0.09265333,-0.07425249,0.00874675,-0.00846614,-0.0024694,-0.02428904,0.06288506,-0.00512269,-0.04473824,-0.05352439,-0.0016383,-0.1114175,-0.072532,0.08708599,-0.03854434,-0.0071414,0.01040872,-0.03482805,0.01373627,0.05430537,-0.02270124,-0.02583324,0.01410088,0.00111248,0.05949409,0.16048302,0.01149202,-0.03086339,-0.02526353,-0.02583657,-0.06544855,0.16602761,-0.01838139,-0.0718534,-0.05121959,0.06600709,0.03745215,-0.05694217,0.01319507,-0.0365388,-0.02294214,0.04249851,0.09165871,-0.000619,0.03268134,-0.01869677,-0.05178364,-0.00784888,-0.00184422,0.02177138,-0.1063768,0.00365679,-0.01330057,-0.09292553,0.0044407,-0.03019809,0.0169313,0.00598671,-0.03884228,0.04639575,0.04515229,0.0591977,-0.06536242,-0.05033305,-0.0517457,-0.0384873,0.04074528,-0.02997853,0.106335,0.0047048,-0.0374606,-0.04385052,-0.0057349,0.04015891,0.02051489,0.0009073,-0.00417999,0.06502959,-0.01134119,0.06935866,0.01839094,0.05354859,0.00183517,0.04467285,-0.01204296,0.03170098,0.00353755,0.03417723,0.04307904,-0.00553748,-0.08974516,-0.23746222,-0.03208503,-0.03952259,-0.05440658,-0.0002726,-0.02853864,0.01548471,-0.00311688,0.08050799,0.06575635,0.06465915,0.04666089,-0.05165503,0.00762366,-0.01869393,-0.02554993,0.01080676,0.00793279,-0.03038825,0.02276335,0.03477738,0.07656795,-0.00926759,0.04232899,0.05755289,-0.03805936,0.13297673,-0.01022429,0.02587411,0.01442903,0.10440896,0.06713276,0.00944352,-0.0704818,0.00157605,0.02368792,-0.10066173,-0.00405537,-0.09976885,-0.01710534,0.00617045,0.03679443,-0.02873459,-0.07971966,-0.01275317,-0.05369306,-0.02009424,-0.01304452,-0.07064262,0.00892939,-0.01487275,-0.01217673,0.01772144,0.01834117,0.01402923,-0.03296191,-0.05059444,-0.03254686,-0.04542258,-0.00685962,0.00588311,0.0023804,0.08378088,-0.02062821,-0.02223827,0.01545432,-0.01843403,-0.00043571,-0.00177156,0.04408241,-0.06272119,0.11559142,-0.01105783,0.01402817,-0.01116028,-0.00411222,-0.05722775,-0.03685485,0.00127289,-0.04892785,0.04558594,0.01930688,0.0091991,0.05346008,0.05859217,0.03124589,0.02055519,0.02661607,0.02416356,-0.02374657,0.00331412,-0.01756346,-0.02503964,-0.00944162,0.03628906,0.01115987,-0.3070921,0.03068721,-0.01229018,-0.00237458,-0.01487647,0.02000225,0.01333837,-0.01577647,0.0033263,0.01537968,0.01837546,0.06362602,0.00181314,-0.05017766,-0.03310756,-0.01183053,0.00390924,0.00528179,0.05440073,-0.03855334,-0.00561807,-0.01354281,0.24758358,0.04628129,-0.00108403,-0.00899327,0.01963221,0.00702632,0.01519303,-0.00840892,-0.00011002,-0.01033858,0.0823444,0.01144366,0.05395123,0.10191203,-0.02022859,0.01631378,-0.01107008,0.03565424,-0.02315604,0.01847866,-0.09845466,0.02572844,0.11297741,0.01617802,-0.02030659,-0.0589527,0.00834039,0.09376027,-0.00891023,-0.00234804,-0.01659922,0.01238428,-0.02737239,0.01236154,0.03373115,-0.00015685,0.04730139,0.03505686,0.07078564,0.03543917,0.02781723,0.08243919,0.030436],"last_embed":{"hash":"1xdwt8b","tokens":374}}},"text":null,"length":0,"last_read":{"hash":"1xdwt8b","at":1753495687683},"key":"notes/saliu/MDIEditor Lotto WE 如何平衡隨機性與統計分析以提升中獎機率.md##{7}","lines":[41,48],"size":382,"outlinks":[],"class_name":"SmartBlock"},
