{"defaultSearchEngine": "duckduck<PERSON>", "customSearchEngine": [{"name": "duckduck<PERSON>", "url": "https://duckduckgo.com/?q="}], "hoverPopover": true, "ignoreList": ["notion", "craft"], "alwaysShowCustomSearch": true, "showOtherSearchEngines": true, "showSearchBarInPage": true, "markdownPath": "/markdown", "customHighlightFormat": false, "highlightFormat": "[{CONTENT}]({URL})", "highlightInSameTab": false, "openInSameTab": false, "showRefreshButton": false, "openInObsidianWeb": false, "useCustomIcons": false, "useWebview": false, "useIconList": true, "darkMode": true, "randomBackground": false, "lastOpenedFiles": false, "bookmarkManager": {"openBookMark": false, "saveBookMark": false, "sendToReadWise": false, "pagination": "12", "category": "- Computer\n    - 算法\n    - 数据结构\n- obsidian\n    - surfing\n    - dataview\n", "defaultCategory": "ROOT", "defaultColumnList": ["name", "description", "url", "category", "tags", "created", "modified", "action"], "defaultFilterType": "tree"}, "treeData": [], "enableHtmlPreview": true, "supportLivePreviewInlineUrl": false, "enableTreeView": false, "focusSearchBarViaKeyboard": true}