"smart_sources:SimpleChains.md": {"path":"SimpleChains.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02888298,-0.03221776,-0.01338335,-0.03411024,-0.00522459,0.05728438,-0.09548644,0.03936471,0.04841448,0.00039262,0.04059084,-0.05259854,0.01237783,0.07331831,-0.00037362,-0.01752042,-0.0271098,0.03956807,-0.05352554,0.04972764,-0.00365232,-0.03401113,-0.01458356,-0.062558,0.00438439,0.00198995,0.03211195,0.00108263,-0.02743539,-0.23020653,0.07323488,-0.06688948,0.02261451,-0.00389583,-0.06980629,-0.027623,-0.02481595,0.00568364,0.01451213,0.04125081,-0.00543745,0.00797014,0.00236044,-0.01645096,0.00982401,-0.06382554,-0.03126094,-0.02470286,0.03516425,0.00183,-0.064007,-0.03758479,0.01346118,-0.0210648,-0.03391612,0.02041179,0.06306971,0.06153511,0.04615826,0.00631522,-0.01161764,0.04195967,-0.1684182,0.08921607,0.02547738,-0.01404124,-0.01010269,0.05394793,0.07534582,0.08811401,-0.01331246,0.02023248,0.03471367,0.03043019,-0.00745527,-0.06145534,0.0132385,-0.00067085,-0.00137071,-0.03335869,-0.03913265,0.01986038,-0.00739553,0.01371971,-0.00830106,0.01110942,0.08457375,-0.01071175,0.04307209,-0.01305338,0.01061344,-0.0181858,-0.00567424,0.03365957,-0.05990516,0.030206,0.02947509,0.00011743,-0.06314345,0.12310852,-0.02389754,0.05366318,0.02861221,-0.02378503,0.03094529,-0.07333246,-0.05479834,-0.01145544,-0.02335354,-0.03887818,-0.01723813,-0.01037535,-0.0096142,-0.07835439,-0.00633656,-0.00495947,-0.02944303,0.01352927,0.00670834,0.02423346,0.02684066,-0.02274503,0.05800499,0.01420494,0.00841405,0.03792432,-0.02864148,0.07752998,-0.02258527,0.053098,0.05444823,0.00471719,-0.08800004,0.02539702,0.02131733,0.02654647,0.02643797,0.0178747,-0.04693214,-0.00326875,-0.05003996,0.00685729,0.01918625,-0.08734839,-0.05144388,0.0771141,0.02116525,0.02678749,-0.00096741,-0.0899431,-0.0457845,0.03701236,-0.04374048,-0.07807159,0.05760799,0.05131073,0.08396446,-0.00271908,-0.05642778,-0.03985177,-0.09488142,-0.08477847,-0.05218303,0.12485815,0.00523236,-0.07725142,0.00956947,-0.01150294,0.02297563,-0.04510473,0.02315523,0.0416867,-0.03128588,0.03569972,0.11199167,-0.00984537,-0.07872497,0.00677358,0.0224687,0.02136016,-0.01565839,-0.03740407,-0.04438886,0.0088675,-0.02012124,-0.04964681,-0.03041322,-0.03594184,-0.06937908,0.05849842,-0.04452113,0.05359643,-0.004927,0.02095711,-0.03902165,0.01500033,0.02513603,0.00278525,0.03691494,-0.02245561,0.07820906,-0.0243849,-0.0155644,0.0183181,-0.06702586,-0.025131,0.0555976,-0.01374986,0.06317122,0.05140616,0.00470547,0.02037432,0.0226023,-0.06486434,-0.06134929,0.02212186,-0.01626555,0.03704933,0.01361253,0.02495232,0.01868582,-0.05824718,-0.06326592,-0.22626941,-0.01378388,0.08558374,0.00378051,0.0966652,-0.07481723,0.03488002,-0.0083194,-0.02401275,0.09849937,0.12169463,-0.03687733,-0.001858,-0.04812869,-0.00015326,-0.00633123,0.00830773,-0.00159904,-0.03497044,0.00112086,-0.06235782,0.00575537,-0.00883528,-0.102464,0.06193544,0.05885961,0.11779594,-0.01679685,0.0658957,-0.03029907,0.01562159,-0.03928922,-0.03052568,-0.05449054,-0.02991384,0.00502282,0.06961816,0.07675996,-0.03701756,-0.01563196,-0.05933911,0.00861295,0.03834973,-0.10439824,-0.02295492,0.00881566,-0.01507148,-0.02841093,-0.03520256,-0.03312787,0.02477457,-0.03915291,0.07138768,0.07083659,0.03250149,-0.01915577,-0.0002986,-0.03012407,-0.01198854,0.05382016,-0.00179105,-0.03720757,0.00644929,-0.01114975,0.0624826,-0.03104616,0.0386987,-0.01184952,0.00153971,0.00653317,-0.04013821,0.08185039,0.04610311,0.08259247,0.07816139,-0.00508028,-0.06771881,-0.06951518,-0.02779467,0.02994264,0.00953649,0.00718859,0.02479337,0.08630833,0.01765216,0.0707248,0.10371077,-0.01232447,0.04120329,-0.01882537,-0.03430781,-0.01200205,-0.02460357,0.07016795,0.08639735,-0.04098633,-0.25007063,0.03931614,-0.03341844,0.10221177,0.00358076,-0.0293322,0.04905229,-0.01743745,-0.0072593,0.03098701,-0.02756771,0.00625442,0.10491966,-0.02440475,-0.01695111,-0.00233833,0.02530482,-0.02785075,0.03656393,-0.01352548,0.03054213,0.03827775,0.21744198,-0.04562638,0.02602895,0.02718296,-0.04422524,-0.01080967,0.07474678,0.04552525,-0.01525579,-0.00266674,-0.0032102,-0.00852411,0.05900792,0.06186683,-0.01465908,-0.00127809,0.00396285,-0.020396,0.00088106,0.00271126,-0.01609881,0.02010283,0.0553944,-0.0502022,-0.03417238,-0.05588387,-0.03778721,0.01661137,-0.0659076,0.05414972,0.03930551,-0.00471495,-0.02671947,0.05849443,0.04101586,-0.00910134,-0.06867039,-0.01790128,0.04723481,-0.09808989,0.05464947,0.01733563,-0.06975031],"last_embed":{"hash":"73ee13a1b26fd4252536dbea4f795c892138cd25fc62bef131d1c48006266556","tokens":471}}},"last_read":{"hash":"73ee13a1b26fd4252536dbea4f795c892138cd25fc62bef131d1c48006266556","at":1745995158761},"class_name":"SmartSource2","outlinks":[],"blocks":{"#":[1,2],"##Step 1: Setup Your Environment":[3,13],"##Step 1: Setup Your Environment#{1}":[5,13],"##Step 2: Import Required Libraries":[14,24],"##Step 2: Import Required Libraries#{1}":[16,24],"##Step 3: Load and Preprocess Data":[25,36],"##Step 3: Load and Preprocess Data#{1}":[27,36],"##Step 4: Prepare Training and Test Sets":[37,46],"##Step 4: Prepare Training and Test Sets#{1}":[39,46],"##Step 5: Define the Neural Network Architecture":[47,63],"##Step 5: Define the Neural Network Architecture#{1}":[49,63],"##Step 6: Set Up Training Parameters":[64,73],"##Step 6: Set Up Training Parameters#{1}":[66,73],"##Step 7: Train the Model":[74,92],"##Step 7: Train the Model#{1}":[76,92],"##Step 8: Evaluate the Model":[93,106],"##Step 8: Evaluate the Model#{1}":[95,106],"##Conclusion":[107,119],"##Conclusion#{1}":[109,119]},"last_import":{"mtime":1734508512891,"size":4129,"at":1740449881604,"hash":"73ee13a1b26fd4252536dbea4f795c892138cd25fc62bef131d1c48006266556"},"key":"SimpleChains.md"},