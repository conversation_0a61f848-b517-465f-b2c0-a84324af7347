
"smart_sources:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md": {"path":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11041804,-0.06223417,-0.02895985,-0.00408346,-0.07275172,0.05616283,-0.03651633,0.04455402,0.03904765,0.0022079,-0.01380941,-0.0302416,0.03895495,0.01114867,-0.03478883,-0.02914073,-0.03801494,0.0040397,-0.03286748,-0.01005523,0.10422456,-0.06248895,-0.07101232,-0.09183172,0.06494458,0.04415944,-0.01502977,-0.04016487,-0.00707826,-0.22194384,-0.01742991,0.0647691,0.02362237,-0.05584418,-0.06532623,-0.040178,-0.01813202,0.0944103,-0.04559542,0.05135741,0.01303609,0.01466265,-0.03499476,0.00575606,0.03859959,-0.01740205,-0.01254263,-0.00380125,0.00425666,-0.03192211,-0.0853896,0.00189856,-0.01540407,0.00508339,0.06399701,0.00641794,0.07212256,0.07192708,0.02235356,0.03836384,0.04247689,0.05713536,-0.19569987,0.07208329,0.00731924,0.0127953,-0.03007518,-0.03990953,0.03714446,0.05452693,0.03101444,0.01494341,0.00352999,0.03895485,0.06542991,0.00598314,-0.03463371,-0.08896168,-0.03556241,0.02013719,-0.04108691,-0.00328522,-0.018605,-0.01398327,-0.02263768,0.03555375,0.01593473,0.02374026,0.06228007,-0.07519039,0.05664929,0.01115751,0.03086717,0.06074005,0.01293389,0.01786906,0.00802499,-0.03060006,0.020647,0.10178391,0.01318284,-0.00324764,0.03367828,0.02850064,0.04426061,-0.02320444,-0.02008113,-0.07949673,-0.00784842,0.0093161,0.04531838,0.00472646,0.04105367,-0.01903615,-0.03123902,-0.03090936,0.01851709,0.00172558,0.03869503,-0.00321986,-0.03976614,0.0644858,0.0169395,-0.01329976,-0.01172083,0.01896258,-0.0415876,0.07457526,0.002717,0.0198064,0.05101253,0.0306276,-0.10145832,-0.04108645,-0.00414146,-0.01056194,0.00748903,-0.01652807,0.00677065,0.02094282,0.00162575,-0.05273693,0.03290171,-0.11012426,-0.06095341,0.06898611,0.02862346,0.02136282,0.02790139,0.01472146,-0.00178744,-0.00128473,-0.04588931,-0.06003641,-0.02098509,-0.0042252,0.11039334,0.09363502,-0.03335113,0.01604563,-0.03351105,-0.04656624,-0.04270433,0.15542962,-0.01600913,-0.09870063,-0.00472715,0.05043634,-0.01061769,-0.09467115,-0.02490109,0.01983776,-0.05413091,0.03392128,0.1194492,-0.01799612,-0.03406483,-0.06072935,-0.01139481,0.00597687,0.0100796,-0.06798822,-0.04691437,0.00583778,-0.05471197,-0.07719377,0.00224672,-0.02413238,0.00555836,0.06019557,0.00734176,0.02629864,-0.07970348,0.02361794,-0.04293258,-0.05206848,-0.03770006,-0.05702382,0.04306182,-0.03590999,-0.0582696,0.00465506,0.00516293,0.00988482,-0.02292788,0.00853398,0.02510822,-0.03445854,0.06371819,0.03108144,-0.01760868,-0.0236631,0.00255844,0.0648435,-0.03467757,0.03593067,0.04155857,0.03281683,0.01178509,-0.00347805,-0.0055141,0.02270849,-0.09878896,-0.17040087,-0.0571347,-0.04363517,-0.03172391,0.01339126,-0.02430112,0.04315699,-0.02549495,0.02014137,0.04954345,0.08793307,-0.04136162,-0.01786473,0.00213755,-0.03737619,-0.02415206,-0.08603335,-0.04898643,-0.03203241,0.04426984,-0.01524182,0.03816535,-0.03700963,-0.07680825,-0.01540073,-0.05527276,0.13647245,0.04536808,-0.01936284,0.00909033,0.07522777,0.00787327,-0.00112624,-0.03581484,0.01333012,0.03004421,-0.04065025,0.02656656,-0.05474832,-0.02534626,-0.06138513,0.03808553,0.00468634,-0.05709436,-0.05644822,-0.00374995,-0.02183026,0.01919824,-0.00412696,0.07170306,0.01251909,0.0206921,0.04889024,-0.00199367,0.04517043,-0.02250518,-0.05976654,-0.02258833,-0.01700537,0.07919825,-0.00783608,-0.00454195,0.0488479,-0.03156323,0.04296493,0.03140238,-0.02284531,-0.05685653,-0.00289375,-0.00762653,0.01899797,0.12664355,0.02950237,0.01926245,0.01739587,0.01229376,0.07262223,-0.02126922,0.04999251,0.01136064,-0.02276523,-0.08709322,0.04567155,0.07120775,0.06334496,0.03360435,0.0297126,0.02555755,-0.02380827,-0.00992114,-0.03004867,0.02519153,-0.02221868,0.0272823,0.02875086,0.04366599,-0.27127069,0.05208926,0.00402692,0.05574597,-0.00123712,-0.0297488,0.0377725,-0.01001775,-0.00117211,-0.06774549,0.04873655,0.03479154,0.01618494,-0.01930665,-0.00669149,-0.02342359,-0.00786191,-0.02726476,0.06849518,0.02350475,0.03001629,0.0424878,0.25393647,0.01545114,-0.0120628,0.00094122,0.02584517,0.02302836,0.01219389,0.07287452,-0.01617521,0.03628325,0.05098503,0.01408803,-0.04881115,0.05660028,-0.02369343,0.0176205,-0.01174706,0.01587113,-0.063301,-0.01887956,-0.01904674,0.054987,0.11864249,0.06268961,-0.02048316,-0.08585479,0.06453207,0.03715664,-0.06484532,-0.07820353,-0.06523639,-0.01745189,0.00182039,0.02118949,0.0321682,-0.00361854,-0.01223261,0.0065238,0.01716358,0.0159231,0.08325167,0.0232597,0.02340145],"last_embed":{"hash":"7cwrzd","tokens":464}}},"last_read":{"hash":"7cwrzd","at":1753423484408},"class_name":"SmartSource","last_import":{"mtime":1753266233544,"size":37926,"at":1753423416052,"hash":"7cwrzd"},"blocks":{"#---frontmatter---":[1,6],"#Lottery Mathematics, Lotto Mathematics, Lottery Math":[8,237],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#{1}":[10,15],"#Lottery Mathematics, Lotto Mathematics, Lottery Math##I. [Protomathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath)":[16,21],"#Lottery Mathematics, Lotto Mathematics, Lottery Math##I. [Protomathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath)#{1}":[17,21],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>":[22,207],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{1}":[24,58],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{2}":[59,60],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{3}":[61,62],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{4}":[63,64],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{5}":[65,66],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{6}":[67,68],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{7}":[69,70],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{8}":[71,72],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{9}":[73,74],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{10}":[75,76],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{11}":[77,132],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{12}":[133,134],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{13}":[135,161],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{14}":[162,163],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{15}":[164,190],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{16}":[191,192],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{17}":[193,207],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>3. Lottery Links and Resources at this Web Site</u>":[208,211],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>3. Lottery Links and Resources at this Web Site</u>#{1}":[210,211],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies":[212,215],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#Resources in Lottery, Software, Systems, Lotto Wheels, Strategies#{1}":[214,215],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#[Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://saliu.com/content/probability.html)":[216,237],"#Lottery Mathematics, Lotto Mathematics, Lottery Math#[Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://saliu.com/content/probability.html)#{1}":[218,237]},"outlinks":[{"title":"Learn lotto mathematics, odd, groups of lottery numbers, lotto wheels, designs, systems.","target":"https://saliu.com/gambling-lottery-lotto/Line-1.gif","line":14},{"title":"Protomathematics of Lottery","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath","line":16},{"title":"Mathematics of Lottery","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Mathematics","line":17},{"title":"Lottery Mathematics Links, Resources, Software","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Links","line":18},{"title":"This is the introduction to lottery mathematics combined with the best lotto software.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":20},{"title":"_**Lotto Wheels, Reduced Lotto Systems, Lottery Wheeling Myth**_","target":"https://saliu.com/bbs/messages/11.html","line":55},{"title":"The best lotto wheels for all lotto games are created by lottery wheeling software.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":85},{"title":"The best software for all lottery and lotto games is known as Bright.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":114},{"title":"The mathematics of lottery strategies or systems starts with Single lotto numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":122},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":128},{"title":"**SoftwareLotto**: _**Special upgrades to the lottery utility software for 5- and 6-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/lotto-software.htm","line":191},{"title":"Ion Saliu's Probability Book on mathematics of lottery, math of lotto.","target":"https://saliu.com/gambling-lottery-lotto/probability-book-Saliu.jpg","line":203},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":203},{"title":"Get useful resources in lottery mathematics, Software, theories, systems, strategies.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":206},{"title":"\n    \n    ## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies\n    \n    ","target":"https://saliu.com/content/lottery.html","line":210},{"title":"Resources in Theory of Probability, Mathematics, Statistics, Combinatorics","target":"https://saliu.com/content/probability.html","line":216},{"title":"_**Lotto Lottery Software**_","target":"https://saliu.com/LottoWin.htm","line":218},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":220},{"title":"_**Lottery Pairs System, Lotto Pair Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":221},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":222},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":223},{"title":"_**Mathematical Presentation of Lottery, Including Software Systems**_","target":"https://saliu.com/lottery.html","line":224},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":225},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":226},{"title":"_**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":227},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":228},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":229},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":230},{"title":"Best lottery mathematics book, lotto maths established by Ion Parpaluck Saliu.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":232},{"title":"Forums","target":"https://forums.saliu.com/","line":234},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":234},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":234},{"title":"Contents","target":"https://saliu.com/content/index.html","line":234},{"title":"Help","target":"https://saliu.com/Help.htm","line":234},{"title":"Home","target":"https://saliu.com/index.htm","line":234},{"title":"Software","target":"https://saliu.com/infodown.html","line":234},{"title":"Search","target":"https://saliu.com/Search.htm","line":234},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":234},{"title":"Thanks for visiting the site of lottery, lotto of mathematics and software!","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":236}],"metadata":{"created":"2025-07-23T18:23:53 (UTC +08:00)","tags":["lottery","mathematics","math","software","systems","system","strategy","strategies","software","wheels","designs","professors","university","treatise","formulae","formula","theorem","odds"],"source":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm","author":null}},"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12355649,-0.07314215,-0.02010711,-0.01764475,-0.07064826,0.0467243,-0.00088769,0.03967664,0.05397703,0.01428891,-0.01372773,-0.03734397,0.03730834,0.02060597,-0.02070624,-0.02767914,-0.02329604,-0.01005454,-0.03743679,0.00548944,0.12356615,-0.05160588,-0.08780982,-0.1000171,0.05906513,0.03502423,-0.02502749,-0.04845618,-0.00227654,-0.16905923,-0.01700868,0.06763151,0.00585984,-0.06363066,-0.04612371,-0.06093242,-0.02165338,0.09157779,-0.01679418,0.06775828,0.0200712,0.02071419,-0.03705402,-0.00125699,0.02599373,-0.02067584,-0.01984528,0.00171338,0.01405625,-0.01400333,-0.06055611,0.00065253,-0.01276094,0.02813596,0.05192227,-0.00842696,0.05381143,0.0450304,0.03938011,0.04896685,0.04468651,0.03420603,-0.20958291,0.06307002,0.00147447,0.01104999,-0.0178945,-0.04266163,0.0064404,0.04221038,0.04713325,0.01593096,-0.00646256,0.05159718,0.05427599,0.00947803,-0.03673727,-0.09929106,-0.02879154,0.01845873,-0.03074738,-0.01194553,-0.04207428,-0.01971113,-0.01313721,0.01973963,0.03117408,0.05579562,0.0701481,-0.05282347,0.04211471,0.01503366,0.0553446,0.0637969,-0.00372585,0.01987284,0.01308475,-0.02381072,-0.00260816,0.11640788,-0.01730164,0.01632483,0.02072874,0.03218084,0.0329781,-0.00614993,-0.03805565,-0.07363413,-0.02796431,0.01087083,0.04752809,0.02442847,0.0720195,-0.05913815,-0.0059305,-0.01969238,0.01743367,0.00213016,0.05884909,-0.00193034,-0.05131554,0.05077811,0.00711686,-0.01233288,-0.00031141,0.03000521,-0.02189472,0.08798362,-0.00376169,0.02792502,0.04025713,0.01432608,-0.10432117,-0.02857433,-0.01441855,-0.00478289,0.04153508,-0.04402372,0.00199726,0.01374211,0.00288354,-0.0387814,0.02575114,-0.09490348,-0.03932124,0.07310577,0.03957566,0.00228194,0.0473376,0.01473244,-0.01708096,-0.02155121,-0.02024557,-0.05110645,-0.00075605,-0.00347493,0.11952564,0.09181853,-0.0144749,0.00898521,-0.02250672,-0.05700328,-0.04737741,0.16723046,-0.03235123,-0.10846039,0.00728277,0.06488056,0.00164074,-0.09525469,-0.00027894,0.01525532,-0.04874162,0.04182236,0.14729635,-0.00986334,0.02183684,-0.04263074,-0.0359549,0.00473812,0.00525467,-0.0572588,-0.04931587,0.01165017,-0.07115623,-0.06276044,0.00155095,-0.03100044,0.00902006,0.06815387,-0.00081968,0.00456622,-0.09601421,0.01645939,-0.03477706,-0.04082957,-0.04578613,-0.04876107,0.05250691,-0.02777424,-0.04325354,-0.00754525,-0.00551375,0.02373643,-0.01149273,0.013576,0.01408514,-0.03123926,0.03135037,0.04436506,-0.00107425,0.00059874,0.00460031,0.05906663,-0.05437566,0.03879704,0.02636693,0.01537112,0.0109578,-0.02143701,-0.01584325,0.01030548,-0.08647039,-0.19046201,-0.04555776,-0.03171887,-0.04706796,0.00692767,-0.00733069,0.02247004,-0.02004963,0.01505492,0.09211157,0.06618807,-0.04345998,-0.02039033,-0.02235383,-0.00947398,-0.02686902,-0.07577931,-0.06940193,-0.01342303,0.01813946,-0.00010615,0.00261039,-0.05134954,-0.08564512,-0.0086216,-0.01687771,0.13473345,0.07799553,-0.02148489,-0.00640532,0.08988924,-0.00519887,0.009931,-0.04019643,0.00487116,0.01896698,-0.06040697,0.03899075,-0.05016072,-0.05097708,-0.07193319,0.04975588,0.0030502,-0.04332102,-0.01499545,-0.00212046,0.00104048,-0.00538496,-0.00054007,0.06864394,0.00667692,-0.01012031,0.02642295,0.02214987,0.04749602,0.00393407,-0.04335585,-0.0074785,-0.00979246,0.05720345,-0.01064218,-0.02019139,0.03744169,-0.02808516,-0.00861903,0.03939978,-0.02310459,-0.07561919,-0.0127016,-0.01780885,0.01701969,0.11126134,0.0492221,-0.00683215,0.00583717,-0.00407776,0.07069193,-0.01504472,0.05708302,0.0086118,-0.01302809,-0.08394881,0.05033139,0.06318386,0.07033475,0.03549071,0.01808842,0.02570265,-0.00388222,-0.00784612,-0.02732856,0.010487,-0.0327067,-0.0023352,0.03223205,0.01603242,-0.26850533,0.06503427,0.01481472,0.06048707,0.01157928,-0.0247899,0.01930434,-0.01332521,-0.04164729,-0.04922028,0.06607316,0.04461141,0.01782992,-0.03034708,-0.01740056,-0.02086911,-0.00244781,-0.04468656,0.04845472,0.04457645,0.0340059,0.04770992,0.25161889,0.02982532,-0.01565209,0.01114901,0.03009182,0.03210492,0.00815835,0.05887702,-0.01379387,0.01409993,0.05476574,-0.01828698,-0.0489328,0.06158098,-0.03331763,0.04084172,-0.01156674,0.02377111,-0.08728819,-0.01670554,-0.03761769,0.0508666,0.10313158,0.07096285,-0.04013242,-0.07769756,0.0519187,0.02877384,-0.05594261,-0.08919987,-0.06748263,0.00262266,-0.00098879,0.0368644,0.04178162,-0.01323556,-0.00296697,-0.0119886,0.00481751,0.01147545,0.08167864,0.01007279,0.01346077],"last_embed":{"hash":"1fw4ifj","tokens":110}}},"text":null,"length":0,"last_read":{"hash":"1fw4ifj","at":1753423482910},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#---frontmatter---","lines":[1,6],"size":287,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09920098,-0.03319638,-0.03260516,-0.00649851,-0.06923932,0.06597564,-0.03810173,0.04117532,0.02336915,-0.01668741,-0.00698593,-0.03002805,0.03119069,0.00679391,-0.0323226,-0.0125132,-0.0411058,0.00946966,-0.03508284,-0.01053964,0.08143268,-0.07979952,-0.06060531,-0.08245345,0.06398834,0.04309445,-0.00846985,-0.04981432,0.00478362,-0.24102727,-0.01761275,0.05262114,0.03154707,-0.04517971,-0.06280706,-0.01862105,-0.01954476,0.09051403,-0.05999462,0.04496213,0.01535464,0.00928375,-0.02937214,0.0054703,0.04157953,-0.01437467,-0.00461686,-0.00389573,0.00058465,-0.0327214,-0.06974141,0.00452022,-0.00327285,-0.00875189,0.06624806,0.0059932,0.08482806,0.07138272,0.01922395,0.02913209,0.03556253,0.07659147,-0.19677232,0.06245193,0.01911663,0.02505517,-0.03031845,-0.03533837,0.04683901,0.06538524,0.02251748,0.01947113,-0.00599851,0.03596389,0.05611415,0.00825848,-0.0309069,-0.07635276,-0.03164868,0.03382099,-0.04105493,0.00637014,-0.00728078,-0.00298172,-0.03515713,0.03520383,0.0041978,0.01537936,0.04856289,-0.07706211,0.06712365,0.01054165,-0.00465205,0.06141577,0.01801383,0.01593747,0.02027357,-0.04360614,0.05217851,0.10487019,0.01665397,-0.00437752,0.03355889,0.03323659,0.04036888,-0.04199612,-0.01551527,-0.08877388,-0.00419782,0.00840379,0.04017145,0.00533541,0.0297238,0.00376824,-0.05523047,-0.03395908,-0.00816594,-0.00856228,0.02828292,-0.00933772,-0.04440468,0.04843638,0.02470496,-0.02160467,-0.03662055,0.00648641,-0.03229129,0.08042461,0.02181906,0.01089436,0.0362173,0.03781196,-0.09106596,-0.04969902,-0.00454463,-0.00266831,0.00804466,-0.0022673,0.00380465,0.01461918,0.02099435,-0.06074064,0.03973739,-0.11296895,-0.05426648,0.0572194,0.01273067,0.01839463,0.00978098,0.0261305,-0.00392094,-0.01289076,-0.0709349,-0.06406965,-0.02508258,-0.00961065,0.09912062,0.09385326,-0.02819407,0.018426,-0.0378039,-0.03452181,-0.03371998,0.14818223,0.00104781,-0.07621399,-0.00552096,0.03545507,-0.02107583,-0.10326136,-0.03911203,0.02828968,-0.05734472,0.03884978,0.0762422,-0.00323742,-0.07514708,-0.06221321,-0.00074881,0.01867273,-0.00159535,-0.06609525,-0.03517498,0.00644629,-0.03223896,-0.08777287,0.01807784,-0.00523705,0.0056747,0.06598066,0.00512742,0.04515697,-0.06355541,0.02666332,-0.04329557,-0.05321533,-0.02935712,-0.0600226,0.03800367,-0.04011628,-0.05020082,0.0106155,0.00828083,0.01485875,-0.03001018,0.01740823,0.03762019,-0.03456914,0.08750413,0.03186525,-0.02879141,-0.02943858,0.0142992,0.05167478,-0.0121471,0.038007,0.04815215,0.03705941,0.0162074,0.02628407,0.00023892,0.00901906,-0.08518186,-0.17266533,-0.06293901,-0.0357376,-0.01498312,0.02567781,-0.03097443,0.04833047,-0.0285839,0.01390424,0.0335579,0.08574514,-0.05394629,-0.0148421,0.03007321,-0.04220394,-0.01618477,-0.0955845,-0.0505452,-0.04028368,0.03803694,-0.02426294,0.05686721,-0.03401005,-0.06789842,-0.00704663,-0.05481252,0.14794423,0.02378159,-0.01705083,0.00317111,0.0646668,0.00197162,-0.00561581,-0.0276012,0.0141378,0.02392182,-0.0328225,0.00753472,-0.04433101,-0.0196859,-0.05885239,0.02766985,0.0093705,-0.05764567,-0.05712212,-0.01420963,-0.02974951,0.03520807,-0.00757507,0.06903385,0.02017183,0.02616787,0.05988795,-0.01223193,0.0370242,-0.04953329,-0.07476627,-0.01683482,-0.01596204,0.08756389,-0.00100655,0.00699801,0.0602176,-0.02741786,0.06068428,0.02904226,-0.02029711,-0.03933607,-0.00320897,0.0093466,0.01599749,0.11417948,0.01180311,0.01540949,0.01637589,0.02026889,0.06199843,-0.01924815,0.05381331,0.01002848,-0.02586538,-0.0927989,0.0245923,0.06248338,0.05831026,0.03931608,0.03650285,0.02826743,-0.02364698,-0.01710338,-0.00425188,0.02645434,-0.02380732,0.05319928,0.02586192,0.04061476,-0.27604404,0.04540818,-0.01139086,0.04885955,-0.016344,-0.01915079,0.03858091,0.01031964,0.02062041,-0.06339169,0.05349166,0.02909365,0.02986565,-0.00807097,0.00046021,-0.0391496,-0.01172723,-0.02980009,0.06804337,0.00146573,0.03884142,0.03499459,0.25966975,0.0023802,-0.00292427,-0.0154046,0.03401598,0.00135426,-0.01054142,0.07016286,-0.00594293,0.03088075,0.04338584,0.00092559,-0.04003963,0.05106662,-0.02037233,0.00870069,-0.01489087,0.00858832,-0.0613606,-0.01814232,-0.01866903,0.03994069,0.1370389,0.0581344,-0.01938297,-0.09239902,0.06640629,0.04658751,-0.06844462,-0.07425212,-0.05887009,-0.02946508,0.00119635,0.01033229,0.0167688,-0.01885043,-0.02027314,0.02351252,0.02714946,0.02328563,0.09072776,0.03269251,0.02314854],"last_embed":{"hash":"jk9ofl","tokens":455}}},"text":null,"length":0,"last_read":{"hash":"jk9ofl","at":1753423482945},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math","lines":[8,237],"size":37595,"outlinks":[{"title":"Learn lotto mathematics, odd, groups of lottery numbers, lotto wheels, designs, systems.","target":"https://saliu.com/gambling-lottery-lotto/Line-1.gif","line":7},{"title":"Protomathematics of Lottery","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath","line":9},{"title":"Mathematics of Lottery","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Mathematics","line":10},{"title":"Lottery Mathematics Links, Resources, Software","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Links","line":11},{"title":"This is the introduction to lottery mathematics combined with the best lotto software.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":13},{"title":"_**Lotto Wheels, Reduced Lotto Systems, Lottery Wheeling Myth**_","target":"https://saliu.com/bbs/messages/11.html","line":48},{"title":"The best lotto wheels for all lotto games are created by lottery wheeling software.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":78},{"title":"The best software for all lottery and lotto games is known as Bright.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":107},{"title":"The mathematics of lottery strategies or systems starts with Single lotto numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":115},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":121},{"title":"**SoftwareLotto**: _**Special upgrades to the lottery utility software for 5- and 6-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/lotto-software.htm","line":184},{"title":"Ion Saliu's Probability Book on mathematics of lottery, math of lotto.","target":"https://saliu.com/gambling-lottery-lotto/probability-book-Saliu.jpg","line":196},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":196},{"title":"Get useful resources in lottery mathematics, Software, theories, systems, strategies.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":199},{"title":"\n    \n    ## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies\n    \n    ","target":"https://saliu.com/content/lottery.html","line":203},{"title":"Resources in Theory of Probability, Mathematics, Statistics, Combinatorics","target":"https://saliu.com/content/probability.html","line":209},{"title":"_**Lotto Lottery Software**_","target":"https://saliu.com/LottoWin.htm","line":211},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":213},{"title":"_**Lottery Pairs System, Lotto Pair Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":214},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":215},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":216},{"title":"_**Mathematical Presentation of Lottery, Including Software Systems**_","target":"https://saliu.com/lottery.html","line":217},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":218},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":219},{"title":"_**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":220},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":221},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":222},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":223},{"title":"Best lottery mathematics book, lotto maths established by Ion Parpaluck Saliu.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":225},{"title":"Forums","target":"https://forums.saliu.com/","line":227},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":227},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":227},{"title":"Contents","target":"https://saliu.com/content/index.html","line":227},{"title":"Help","target":"https://saliu.com/Help.htm","line":227},{"title":"Home","target":"https://saliu.com/index.htm","line":227},{"title":"Software","target":"https://saliu.com/infodown.html","line":227},{"title":"Search","target":"https://saliu.com/Search.htm","line":227},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":227},{"title":"Thanks for visiting the site of lottery, lotto of mathematics and software!","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":229}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08157834,-0.04194208,-0.01463549,-0.00541771,-0.0681957,0.0607437,0.01765758,0.01450717,0.0251413,-0.0225909,-0.0028071,-0.03510725,0.0347194,0.01704603,-0.04735371,-0.01743804,-0.02434723,0.02319158,-0.08567505,0.0134477,0.07580924,-0.04695317,-0.07223225,-0.06734207,0.05133877,0.02776508,-0.02655901,-0.05543599,-0.02102526,-0.18723288,-0.01962861,0.07884104,0.02282678,-0.05292337,-0.06332833,-0.04838113,-0.03974773,0.08080655,-0.05485715,0.04858935,0.04213384,0.01834815,-0.04902618,0.00441329,0.04043387,-0.01989243,0.00448918,-0.00625139,0.03469874,-0.03659634,-0.06634887,0.021246,-0.01673396,0.01688617,0.05841478,-0.00316821,0.06185342,0.08072409,0.03878421,0.02250221,0.04708297,0.05484681,-0.18337142,0.06091685,0.00448307,0.02911695,-0.02708956,-0.01410293,0.00281845,0.07945853,0.02017176,0.02733009,-0.01156593,0.06527258,0.03494898,0.00712712,-0.03476694,-0.07740951,-0.01963989,0.04161978,-0.03774355,-0.0129026,-0.0230424,-0.0160952,-0.01602786,0.01438414,0.02960442,0.01468588,0.05600479,-0.07745232,0.04740015,0.04014713,0.01334264,0.07445991,0.03098077,-0.00283581,0.04264298,-0.0549399,0.03861943,0.1116039,0.0154583,0.00926617,0.0333336,0.02857688,0.02676768,-0.03556053,-0.02549722,-0.05494589,-0.00654089,0.02605747,0.0507605,0.02193212,0.04772147,-0.01487268,-0.04816598,-0.0171312,0.00232791,0.0149609,0.01412871,-0.01099794,-0.05771684,0.04546106,0.04659345,-0.01018951,-0.0091443,-0.00258301,-0.00670144,0.10734527,0.01748913,0.03757122,0.04119992,0.02614032,-0.09958547,-0.0249048,-0.02179345,-0.00537066,0.03758825,0.00479906,-0.01438098,0.02838949,0.0133061,-0.06234504,0.04813359,-0.13438173,-0.03838605,0.06653511,0.01335338,0.02270152,0.01828985,0.00920229,-0.00893472,-0.01984097,-0.02528497,-0.04982761,-0.01809048,-0.01093852,0.116689,0.07580491,-0.02734035,0.01397373,-0.047796,-0.04044546,-0.034247,0.13575386,-0.00463853,-0.06688191,-0.00970514,-0.01839946,-0.0342802,-0.10610906,-0.0346886,0.00792431,-0.07705136,0.04987144,0.1071149,-0.00395214,-0.04383994,-0.06050016,-0.02930528,0.00071035,0.00218038,-0.05501241,-0.02707302,0.0104937,-0.02694545,-0.08009628,-0.00128587,-0.01380423,0.02138366,0.07159109,-0.00277367,0.03427547,-0.09845673,-0.01486793,-0.04438787,-0.06510834,-0.04115373,-0.08016651,0.03302223,-0.03898559,-0.05022045,0.00709982,-0.01965075,-0.00032922,-0.0421236,0.03123431,0.02663392,-0.03440478,0.0638688,0.0271336,-0.01665538,-0.02371472,0.01488614,0.06742855,-0.00978236,0.04495046,0.05024964,0.04725105,-0.00014757,0.0107698,0.01763866,0.00224524,-0.07886686,-0.17608643,-0.06096827,-0.01895909,-0.02579666,0.00738781,-0.01599268,0.02597073,-0.03053837,0.02656156,0.07431886,0.10830437,-0.0805321,-0.00370651,0.02901064,-0.0274032,-0.02076025,-0.13113984,-0.07331169,-0.03429625,0.04847206,-0.01953371,0.05908481,-0.0376758,-0.08325642,-0.00471354,-0.03129988,0.13920522,0.03394766,0.00520842,-0.0004151,0.07527936,0.01959729,-0.01572399,0.01568282,0.03273169,0.02119767,-0.05996845,0.0006209,-0.06338938,-0.03053454,-0.04205,0.03834571,-0.02734049,-0.03552923,-0.05967882,-0.00794937,-0.00998597,0.05047375,0.00141614,0.08311215,0.02259671,0.021368,0.05997084,0.00134825,0.04381745,-0.0072054,-0.06724267,0.02195278,-0.02115792,0.07894378,-0.01281477,-0.00255061,0.03341669,-0.02974286,0.05808423,0.03415287,-0.03596051,-0.05183979,-0.00799673,-0.00039946,-0.0018399,0.07825942,0.02414865,0.00767379,-0.00324963,0.0362002,0.07724367,-0.04749439,0.04028388,-0.00028553,0.00273018,-0.08605319,0.02134852,0.0744093,0.06195802,0.01235246,0.01461541,0.03437678,-0.00839026,0.00314905,-0.00243006,0.01280106,-0.03391938,0.02026471,0.01241763,0.03895931,-0.27359191,0.04434153,-0.01283364,0.05000461,-0.02112791,-0.01499182,0.02325692,-0.01125069,-0.00494926,-0.03792202,0.07156533,0.02711883,0.02665082,-0.04139172,-0.00360034,-0.03542726,0.00079615,-0.03873347,0.0791076,0.02937129,0.05229433,0.0457575,0.26358628,0.01468072,0.00547439,-0.01013001,0.02888849,-0.00854843,-0.01040212,0.0513378,0.02185976,0.01104963,0.0504113,-0.00634186,-0.04359977,0.0625654,-0.01806053,0.02634219,-0.01295631,0.03325046,-0.05115786,-0.03640395,-0.03393554,0.04609126,0.12955888,0.030568,-0.01630848,-0.09211762,0.05697017,0.03633292,-0.05901596,-0.07568862,-0.0838847,-0.03263088,0.01388525,0.01626415,0.01499686,-0.03027312,0.00253692,0.01190751,0.00215233,-0.00592041,0.0827397,-0.01409342,-0.00366508],"last_embed":{"hash":"179en7b","tokens":114}}},"text":null,"length":0,"last_read":{"hash":"179en7b","at":1753423483118},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#{1}","lines":[10,15],"size":319,"outlinks":[{"title":"Learn lotto mathematics, odd, groups of lottery numbers, lotto wheels, designs, systems.","target":"https://saliu.com/gambling-lottery-lotto/Line-1.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math##I. [Protomathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08593692,-0.04366676,-0.03029228,-0.00960811,-0.05917517,0.04567139,0.00234205,0.03152514,0.02572941,-0.00373446,-0.00402497,-0.05579659,0.01230393,0.0134424,-0.00575928,-0.0732714,-0.03259695,0.01627595,-0.02631955,0.00617305,0.11611038,-0.05472207,-0.06918662,-0.06081071,0.07675891,0.05425371,-0.01829597,-0.03979422,0.00270731,-0.20075543,-0.01179572,0.05081902,0.01090619,-0.07923592,-0.06892149,-0.0455666,-0.01283874,0.08088177,-0.07898169,0.07644086,0.02308414,-0.01272969,-0.04150491,-0.01041716,0.03576837,-0.04583612,0.02159452,-0.01173835,0.04060562,-0.00783497,-0.09039245,0.02603628,-0.0057087,0.01454579,0.06434038,0.02995534,0.0883769,0.08206782,0.00800529,0.02469555,0.03410179,0.08353373,-0.18166757,0.07359049,0.01502761,-0.00023708,-0.00879569,-0.03150061,0.01486067,0.03823015,0.00995989,0.02187928,-0.00648748,0.04015902,0.04214829,-0.04748143,-0.0321489,-0.06321763,-0.02113956,0.00857453,-0.05542808,0.02111151,-0.00543735,-0.03171682,0.00098516,0.06899632,-0.00743192,0.01642708,0.04236568,-0.08738632,0.0540099,-0.01250882,0.03411864,0.04375906,0.00152204,0.0253411,0.02228861,-0.04464832,0.03842971,0.1211918,0.01911067,-0.03125232,0.0297521,0.0311412,0.03721583,-0.02089889,-0.02980489,-0.07764003,0.03748257,0.02061155,0.02927634,-0.02095741,0.06051751,-0.02567016,-0.03082021,-0.04641129,-0.00643767,-0.01781904,0.05218212,-0.00914303,-0.02593559,0.0544588,0.02620456,-0.014243,0.01227881,-0.02145448,-0.02961031,0.06086472,0.01301609,0.02891395,0.05359998,0.03386417,-0.11080739,-0.04671161,-0.03324974,0.00439671,0.0281782,-0.00782525,0.02299498,0.02020189,-0.00350707,-0.09483503,0.02749144,-0.1041514,-0.02896395,0.09273146,-0.00510756,0.01746309,0.02350026,0.01122859,-0.01259271,0.01067966,-0.04377025,-0.07238062,-0.01901782,-0.00560192,0.10136112,0.09510778,-0.03511066,0.02930477,-0.01970625,-0.06289455,-0.02859209,0.18394086,-0.02702058,-0.08915591,-0.02462191,0.02781684,-0.03306568,-0.0700932,-0.02966303,0.03047411,-0.05449329,0.02847322,0.11445346,-0.03483107,-0.01451083,-0.05674997,-0.01789848,0.0449076,-0.01360029,-0.08706695,-0.05023578,0.02149231,-0.03753041,-0.05721199,-0.00161289,-0.03848844,0.00644567,0.0583006,-0.01396749,0.00594788,-0.08359513,0.02043096,-0.0351374,-0.01047865,-0.05080445,-0.06159625,0.04515544,-0.02066895,-0.02528647,0.02296324,0.02091344,0.02162774,-0.04132908,0.03406286,0.00742524,-0.02881079,0.06205526,0.0208018,-0.01977107,-0.00366538,0.01201363,0.06323244,-0.03428773,0.01064504,0.07368549,0.0099662,-0.01771862,0.04293333,0.02798954,0.00708865,-0.09230515,-0.18369509,-0.04681118,-0.04922627,-0.04693989,0.02453751,-0.05775354,0.04187454,-0.0353073,0.02118845,0.04800797,0.10902482,-0.05054009,-0.01139052,0.012136,-0.03608629,0.00208155,-0.06790642,-0.06176642,-0.02683195,0.03242076,-0.0384934,0.03033631,-0.00865926,-0.06512269,-0.00179866,-0.02821027,0.14105453,0.04967276,0.00048911,0.00579028,0.062656,0.00457372,-0.01872345,-0.04711565,0.00144394,0.01428779,-0.04767014,0.03034928,-0.04297199,-0.01847785,-0.07579152,0.04338662,0.01983232,-0.04463211,-0.02381736,-0.0103187,-0.04106055,0.03737833,0.01835328,0.07041983,0.01397225,0.0260845,0.0329066,0.01043145,0.05242035,-0.00462407,-0.04686249,-0.05267018,-0.0470787,0.06861346,-0.0162464,0.01009242,0.02586935,-0.03639819,0.04156049,0.04982778,-0.02287374,-0.05153715,0.01257004,0.01423393,-0.01489939,0.09818763,0.03328695,0.00453185,0.00697414,0.02661153,0.08151741,-0.02100934,0.05247714,-0.00631027,-0.00073902,-0.08343156,0.03276816,0.05763375,0.03350363,0.03954361,0.06773454,0.02829881,-0.02796667,-0.01509774,-0.01368669,0.04268435,0.00263662,0.0177409,0.01608676,0.04951945,-0.27705926,0.03835567,-0.01037573,0.0582079,0.00649608,-0.02278942,0.0385359,-0.03242621,-0.01168068,-0.07284465,0.07400408,0.01953861,0.03248091,-0.0343396,-0.02705324,-0.02618897,-0.00340607,-0.04812963,0.06128322,0.01948575,0.02984561,0.04340724,0.23650807,0.01619553,-0.02630497,-0.00753842,0.02113886,0.01482314,0.01833584,0.03812376,-0.01348457,0.01585722,0.04219737,0.01438998,-0.01268135,0.04328033,-0.01667615,0.02976974,0.03627965,-0.00874189,-0.06803113,-0.01072092,-0.05645952,0.0487744,0.09938039,0.05769253,-0.03784182,-0.06787985,0.06406405,0.04412476,-0.06096443,-0.09608477,-0.0371327,0.009215,-0.01092726,0.00218613,0.03295443,-0.02327972,-0.01258306,0.00758983,0.0436302,0.02962209,0.0624942,0.03021803,0.02865782],"last_embed":{"hash":"v16jch","tokens":213}}},"text":null,"length":0,"last_read":{"hash":"v16jch","at":1753423483155},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math##I. [Protomathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath)","lines":[16,21],"size":472,"outlinks":[{"title":"Protomathematics of Lottery","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath","line":1},{"title":"Mathematics of Lottery","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Mathematics","line":2},{"title":"Lottery Mathematics Links, Resources, Software","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Links","line":3},{"title":"This is the introduction to lottery mathematics combined with the best lotto software.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":5}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math##I. [Protomathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07924679,-0.0432496,-0.03139884,-0.00783273,-0.05951826,0.04468118,-0.00148099,0.02791163,0.02348705,-0.00202062,-0.00682968,-0.05905588,0.01556678,0.01603809,-0.00229587,-0.07282781,-0.03472094,0.01791581,-0.02963672,0.00561531,0.11426239,-0.0507782,-0.06881906,-0.05596152,0.07873076,0.0611636,-0.02488879,-0.03861608,0.00346388,-0.20322004,-0.00919368,0.04588725,0.00364096,-0.07625157,-0.07254088,-0.05018561,-0.01300859,0.08549295,-0.07754465,0.07118002,0.01869148,-0.01136813,-0.03860027,-0.01399701,0.03393854,-0.04864183,0.02134297,-0.00591797,0.0400624,-0.00612967,-0.08507337,0.02601322,-0.00371101,0.01758815,0.06553942,0.03043163,0.08412085,0.07839132,0.00434356,0.03141556,0.03164777,0.08106039,-0.18220453,0.07687809,0.01313877,0.0004545,-0.00778174,-0.03307569,0.01615311,0.04158193,0.00195607,0.0229112,-0.00205543,0.04307156,0.0417529,-0.0501086,-0.03364791,-0.06958583,-0.02342219,0.01360293,-0.0591355,0.01883168,-0.00274294,-0.02749542,0.00095794,0.06413782,-0.00630901,0.01272644,0.03411222,-0.08313749,0.05939318,-0.01336569,0.02830364,0.0373703,0.00425418,0.02115444,0.02074141,-0.04722911,0.04022264,0.12133142,0.02427237,-0.03763057,0.02903396,0.0260524,0.0404568,-0.02160822,-0.03698882,-0.07561165,0.03827086,0.01408963,0.0235917,-0.02175853,0.05725562,-0.02800396,-0.02675678,-0.04681411,-0.01107688,-0.02253946,0.05849716,-0.01610795,-0.02463511,0.051071,0.02560341,-0.0179147,0.01088783,-0.0212253,-0.03006685,0.05975427,0.00820494,0.03126173,0.0554449,0.03100762,-0.11088967,-0.04316114,-0.03424676,0.0039698,0.02635334,-0.00565342,0.02331293,0.02147636,-0.00154752,-0.09123942,0.02799527,-0.10687478,-0.02598716,0.0916079,-0.00924762,0.01870063,0.02357886,0.00952215,-0.01186978,0.007607,-0.04160829,-0.07157756,-0.01805969,-0.00222736,0.10686915,0.08959822,-0.03113407,0.03153736,-0.02651183,-0.06259459,-0.03201861,0.18907346,-0.03071711,-0.09006422,-0.02360395,0.02588072,-0.03112659,-0.06732453,-0.0322734,0.0343659,-0.05266089,0.03442627,0.11497248,-0.03313266,-0.01833363,-0.0527781,-0.01298543,0.05021348,-0.01472987,-0.09204186,-0.0558374,0.01879882,-0.03991484,-0.05271012,-0.00736186,-0.02648513,-0.00009907,0.06084384,-0.01889352,0.00801561,-0.08756527,0.0140538,-0.03287815,-0.00994885,-0.05042141,-0.05764147,0.04119005,-0.02340519,-0.02281692,0.02324296,0.01676052,0.02266095,-0.03831941,0.03206976,0.01348156,-0.02575962,0.06273978,0.02040801,-0.02081859,-0.0054242,0.00843337,0.063076,-0.03176975,0.00771979,0.07477229,0.01150142,-0.0227712,0.04963539,0.02628596,0.00911269,-0.0899321,-0.18763313,-0.04477901,-0.04679281,-0.04587311,0.02927235,-0.05913563,0.03869419,-0.03937778,0.01326319,0.04839687,0.10839114,-0.04623014,-0.01289085,0.01392722,-0.0403852,0.00300844,-0.06596874,-0.06020015,-0.02667818,0.03142888,-0.03883489,0.03298189,-0.00377244,-0.06717238,-0.00124496,-0.02679026,0.13811494,0.04474077,-0.00265416,0.00276746,0.06132785,0.00775883,-0.02235984,-0.04325837,0.00175149,0.00896037,-0.03984104,0.02522408,-0.03725993,-0.02447129,-0.07618367,0.04639986,0.02484387,-0.04399686,-0.01958252,-0.01425175,-0.0392955,0.03976479,0.01787615,0.06521244,0.01599472,0.02635631,0.03046694,0.0041974,0.04854117,-0.00607282,-0.04464579,-0.04839705,-0.04921138,0.06562205,-0.01496411,0.00601088,0.01834369,-0.03206123,0.03989804,0.05135687,-0.01876363,-0.04974865,0.0131377,0.01425812,-0.01367869,0.09051274,0.02897488,0.00520768,0.01188347,0.02770096,0.08267316,-0.02392415,0.05772454,-0.00618531,0.00408634,-0.07848542,0.0323666,0.06153935,0.03379059,0.03708836,0.07487528,0.02617476,-0.02614851,-0.01760388,-0.01444578,0.04482221,0.00476695,0.02087006,0.01631231,0.0477502,-0.27916262,0.03627266,-0.01711645,0.06111631,0.00362184,-0.02512131,0.03751034,-0.02957422,-0.00806353,-0.07660019,0.07707958,0.02121801,0.03762415,-0.03586928,-0.02658821,-0.02288809,-0.00269564,-0.04691222,0.05972528,0.01745214,0.02868341,0.04616577,0.24150272,0.01162742,-0.02661293,-0.00335654,0.02153277,0.01489928,0.02070308,0.03598442,-0.00952572,0.01221715,0.04463626,0.00987856,-0.01016534,0.05092257,-0.01211513,0.03512655,0.04038399,-0.00855842,-0.06943318,-0.01055529,-0.0555364,0.04674633,0.09852139,0.06080682,-0.03781987,-0.06498579,0.0642233,0.04632919,-0.06152324,-0.0876883,-0.03683053,0.01142916,-0.00937883,0.00245231,0.03799459,-0.02353794,-0.01398468,0.00703407,0.04439161,0.0309129,0.06104216,0.03508374,0.03004182],"last_embed":{"hash":"mmpnvr","tokens":177}}},"text":null,"length":0,"last_read":{"hash":"mmpnvr","at":1753423483218},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math##I. [Protomathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath)#{1}","lines":[17,21],"size":364,"outlinks":[{"title":"Mathematics of Lottery","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Mathematics","line":1},{"title":"Lottery Mathematics Links, Resources, Software","target":"https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Links","line":2},{"title":"This is the introduction to lottery mathematics combined with the best lotto software.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":4}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08780014,-0.04211857,0.00916672,-0.00560095,-0.10069065,0.061308,-0.02969318,0.05758219,0.03485819,0.01428395,-0.00899639,-0.00931774,0.03503508,0.00488086,-0.01066455,0.02945617,-0.04341207,0.02475769,-0.0247365,-0.02663289,0.12261678,-0.09077396,-0.08705754,-0.07935929,0.06096517,0.05073405,-0.00840918,-0.02379712,0.01158123,-0.23722701,-0.00172266,0.0472746,0.01653316,-0.04034594,-0.03934108,-0.00064388,-0.01539624,0.08349555,-0.06558632,0.04996428,0.0086945,0.02450487,-0.00753159,0.01110219,0.03908859,-0.02870234,-0.02270807,0.001097,-0.03506229,-0.03188231,-0.03835266,-0.01943444,0.02242348,-0.01773219,0.03656595,0.00798478,0.07895394,0.05496142,0.03430272,0.03275495,0.02567485,0.06000674,-0.18705192,0.04873902,0.01904265,0.01482443,-0.02628496,-0.06211324,0.0624257,0.07421972,0.0702315,-0.00978753,-0.00590025,0.03465586,0.06562015,0.00896748,-0.03118059,-0.07853713,-0.04037848,0.02533265,-0.0577507,-0.00219237,-0.00087305,0.01313389,-0.04136659,0.01051296,0.02876602,0.00542363,0.0428212,-0.06682006,0.07699016,-0.00766616,-0.00728904,0.04354901,-0.01940719,-0.0073562,-0.00096058,-0.02681146,0.04703348,0.10764776,0.03853823,-0.0066387,0.03243154,0.02536625,0.01234893,-0.04126541,-0.04019943,-0.07904915,-0.02445678,-0.00208372,0.05671815,0.00746295,0.02186682,0.02363986,-0.05992195,-0.01678,0.01283734,-0.0265076,0.03117544,0.00334208,-0.04043473,0.05119848,0.01474271,-0.0206548,-0.04410549,0.0085191,-0.03169886,0.06296871,0.00415987,-0.0088377,0.03097408,0.0181312,-0.07286766,-0.05194688,0.04978043,0.00546489,-0.00474192,0.03369917,0.04134294,0.00341927,0.02222251,-0.03976803,0.0263612,-0.09294107,-0.05636892,0.04909275,-0.00958273,0.04492059,0.02835617,-0.01139982,0.0253542,-0.03477661,-0.09712336,-0.05402469,-0.0008446,0.00414615,0.07453687,0.08130288,-0.04957718,0.02198895,-0.03220239,-0.02823852,-0.02523814,0.17464346,-0.03113627,-0.07730826,0.02402937,0.06099907,-0.00588316,-0.09954842,-0.02133312,0.05213462,-0.07505057,0.04639574,0.06830104,-0.00378545,-0.11077854,-0.02815855,0.01612073,0.01779476,0.02686432,-0.07261195,-0.04372106,0.00919083,-0.03689043,-0.08394278,0.01680891,0.01434683,0.02688361,0.0361741,0.0228928,0.04052454,-0.0293932,0.0417056,-0.03821769,-0.05876085,-0.02119379,-0.04942835,0.03803239,-0.04135161,-0.03239165,0.02077474,0.0258974,0.05359418,-0.01252505,0.01250824,0.02608569,-0.03562624,0.09511496,0.05550526,-0.06665555,-0.04218196,0.00918388,0.04590524,-0.0227522,0.04197015,0.05556723,0.03966406,0.03492986,-0.00037102,0.00069319,0.00973714,-0.07357827,-0.18681014,-0.07584085,-0.03779227,0.00361581,0.05096031,-0.02203955,0.02410083,-0.02625798,-0.01247016,0.03675418,0.06499553,-0.05209846,-0.02736023,0.02928887,-0.05382726,-0.01430357,-0.07051218,-0.0413806,-0.04487551,0.00592536,-0.00123772,0.0607358,-0.05888992,-0.09521496,-0.00923103,-0.04286168,0.14096017,0.00037713,-0.0295351,-0.01740793,0.03462789,-0.02906535,0.02517775,-0.00280229,-0.01914549,0.02544418,-0.03512774,-0.01777856,-0.02352946,-0.01516403,-0.04577123,0.014243,-0.00276704,-0.06895237,-0.03230253,-0.02239795,-0.03104514,0.0198897,-0.01635805,0.05762224,0.01364467,0.00741342,0.05483806,-0.00994286,0.0340128,-0.05820489,-0.0605803,-0.04269294,-0.00145944,0.06455979,0.00997227,-0.03332048,0.0757973,-0.01948543,0.04176126,0.01777161,-0.01095221,-0.04895875,-0.00373451,-0.00477342,0.02569376,0.14981066,0.00726687,0.01503764,0.01828128,-0.00086172,0.05386779,0.02636582,0.04939167,0.03170051,0.00084836,-0.06565671,0.01565856,0.0382227,0.02973988,0.03620251,0.03526489,0.02870295,-0.02737414,-0.00761137,-0.02551736,0.03798423,-0.03061759,0.05140358,0.02459359,0.01187991,-0.25603956,0.0803479,-0.04857621,0.05896961,-0.02408812,-0.03753405,0.0397311,0.04838575,0.03246672,-0.06052991,0.03628995,0.03190497,0.0352585,0.00757649,0.02055479,-0.05280872,0.00430287,-0.03893313,0.0589367,-0.01418264,0.06867478,0.01670372,0.25807688,-0.02658327,0.00480627,0.00396333,0.00792654,0.01917773,-0.02059367,0.05122973,-0.02302935,0.01321381,0.02841879,-0.02188041,-0.0230573,0.02987803,-0.02191672,0.01045289,-0.0027179,-0.00072869,-0.04608189,-0.04594849,-0.02672337,0.027681,0.13356489,0.07454759,-0.02696034,-0.07890847,0.07429136,0.03792337,-0.06369289,-0.04561922,-0.05967143,-0.06360822,0.00297272,0.03136706,0.01110947,0.01224965,-0.0191748,0.00220769,0.02699884,0.00427685,0.06892604,0.04879456,0.02472796],"last_embed":{"hash":"1tpds05","tokens":486}}},"text":null,"length":0,"last_read":{"hash":"1tpds05","at":1753423483270},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>","lines":[22,207],"size":33907,"outlinks":[{"title":"_**Lotto Wheels, Reduced Lotto Systems, Lottery Wheeling Myth**_","target":"https://saliu.com/bbs/messages/11.html","line":34},{"title":"The best lotto wheels for all lotto games are created by lottery wheeling software.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":64},{"title":"The best software for all lottery and lotto games is known as Bright.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":93},{"title":"The mathematics of lottery strategies or systems starts with Single lotto numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":101},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":107},{"title":"**SoftwareLotto**: _**Special upgrades to the lottery utility software for 5- and 6-number lotto games**_","target":"https://saliu.com/gambling-lottery-lotto/lotto-software.htm","line":170},{"title":"Ion Saliu's Probability Book on mathematics of lottery, math of lotto.","target":"https://saliu.com/gambling-lottery-lotto/probability-book-Saliu.jpg","line":182},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":182},{"title":"Get useful resources in lottery mathematics, Software, theories, systems, strategies.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":185}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08626389,-0.03384971,0.00655922,-0.00683866,-0.09620843,0.06290988,-0.03730056,0.05716671,0.03387956,0.01718836,-0.01200846,-0.01129507,0.0370299,0.00252317,-0.01385461,0.02521667,-0.04200958,0.02411317,-0.02456283,-0.02899112,0.12565096,-0.08900519,-0.09020718,-0.07461607,0.06329659,0.04763753,-0.0076382,-0.02551121,0.01423727,-0.23809735,0.00206911,0.04457188,0.01550883,-0.03685255,-0.03987532,0.00398004,-0.0166573,0.08006878,-0.06636988,0.05176715,0.00890232,0.02415483,-0.00487507,0.00942701,0.04089723,-0.02688167,-0.02703804,0.00422696,-0.03566854,-0.03350625,-0.03735577,-0.01780347,0.02246135,-0.01867324,0.03643535,0.00666814,0.08209211,0.05439245,0.03332611,0.03145311,0.02828801,0.05739222,-0.18770128,0.04911789,0.01827045,0.01816688,-0.02535257,-0.06457305,0.0648293,0.07431055,0.07053542,-0.01178936,-0.004875,0.03118195,0.06363135,0.00682282,-0.0297903,-0.07853107,-0.03834534,0.02706882,-0.0595265,-0.00300975,-0.00261798,0.01090698,-0.04128155,0.00999989,0.02497818,0.00276706,0.04440646,-0.07006768,0.07150708,-0.01328415,-0.00215663,0.04456583,-0.0185346,-0.01133132,-0.00433496,-0.02688097,0.0485098,0.10383391,0.03762572,-0.00324875,0.03609932,0.0232933,0.01228956,-0.03911634,-0.03963416,-0.08097989,-0.02355496,-0.00063033,0.05453214,0.00966764,0.02372567,0.0252882,-0.05419845,-0.01394227,0.00702158,-0.02521336,0.03020085,0.00408598,-0.03961208,0.04733824,0.01503003,-0.01993168,-0.04559825,0.00672719,-0.03304172,0.06049185,0.00194171,-0.00984368,0.03079043,0.01865204,-0.07461097,-0.05271266,0.04852099,0.00628456,-0.00715526,0.03114521,0.04354025,0.00575593,0.01955624,-0.03702752,0.02671647,-0.09734108,-0.0561173,0.05091915,-0.00685093,0.04637949,0.02710024,-0.00731222,0.02012893,-0.03228058,-0.099604,-0.05556664,-0.00139168,0.00874095,0.07309534,0.0814164,-0.04737481,0.02041986,-0.03130995,-0.02773855,-0.0244068,0.17394133,-0.03041614,-0.07878508,0.02421027,0.06419864,-0.00966733,-0.1006692,-0.01985065,0.05308514,-0.07450509,0.05029041,0.06676067,-0.00583554,-0.11012591,-0.02823356,0.01421974,0.02140978,0.02554546,-0.07397337,-0.04262084,0.00865868,-0.03634845,-0.08437891,0.0187657,0.0160766,0.02697474,0.03859748,0.02193235,0.04231816,-0.02744191,0.04631483,-0.03680047,-0.05402481,-0.0197359,-0.04798979,0.0368839,-0.04449819,-0.03001563,0.02529561,0.02465264,0.0508385,-0.01100182,0.01086353,0.02453656,-0.03565702,0.09640206,0.05724133,-0.07301316,-0.04298829,0.01020703,0.0449787,-0.02248882,0.04390896,0.05596004,0.03792937,0.03579732,0.00220087,-0.00018116,0.0098044,-0.0728073,-0.18784828,-0.0739942,-0.03892843,0.00016623,0.05106732,-0.02548861,0.02445474,-0.02734519,-0.01218994,0.03378395,0.0638973,-0.04800804,-0.02688924,0.03253377,-0.05547449,-0.01139159,-0.06673852,-0.03877539,-0.0455359,0.00261302,-0.0041191,0.05911627,-0.05807157,-0.09007124,-0.00903484,-0.04687291,0.14254476,-0.00226951,-0.03234977,-0.01817332,0.0323009,-0.03053938,0.02686978,-0.00651156,-0.02581247,0.02323034,-0.03352505,-0.02286291,-0.02026504,-0.0164471,-0.04414801,0.01332568,-0.00193769,-0.06752343,-0.03315456,-0.02267409,-0.03233745,0.014339,-0.01479822,0.05969162,0.01358122,0.00881514,0.05519674,-0.01167651,0.0349112,-0.06171135,-0.05989809,-0.04172546,-0.00069667,0.06464752,0.00589951,-0.03507452,0.07614395,-0.02030889,0.04082271,0.01901401,-0.00993631,-0.04143916,-0.00417428,-0.00247056,0.02803952,0.14899288,0.00719762,0.01741851,0.01675807,-0.00306531,0.05710135,0.0284965,0.0466257,0.03092555,-0.00069473,-0.06272063,0.01824892,0.04035111,0.03154615,0.03931228,0.03946845,0.02367967,-0.028817,-0.00835447,-0.02333491,0.0386228,-0.03063085,0.0515202,0.02551296,0.01458739,-0.25767285,0.0807688,-0.04917802,0.05447885,-0.0224202,-0.03562694,0.03880216,0.04731805,0.03228821,-0.06438947,0.03684644,0.03246842,0.03598718,0.01026706,0.01631989,-0.05264576,0.00559424,-0.03923141,0.0616891,-0.01760954,0.06695507,0.01754037,0.26026872,-0.03189218,0.00638335,0.00794904,0.00634119,0.01823958,-0.02126626,0.05493134,-0.02144828,0.01508813,0.02912476,-0.01699819,-0.0244382,0.03376076,-0.02375144,0.01014301,-0.00315393,0.00044692,-0.04901948,-0.0439766,-0.02732509,0.02498931,0.13210052,0.07621925,-0.02900569,-0.07581963,0.07252804,0.03699891,-0.06195616,-0.04500976,-0.05979894,-0.06175644,0.00701075,0.03101414,0.00845947,0.01206798,-0.01725963,0.00608,0.02511653,0.00878682,0.06367752,0.04769281,0.02666405],"last_embed":{"hash":"19co2g3","tokens":480}}},"text":null,"length":0,"last_read":{"hash":"19co2g3","at":1753423483450},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{1}","lines":[24,58],"size":7577,"outlinks":[{"title":"_**Lotto Wheels, Reduced Lotto Systems, Lottery Wheeling Myth**_","target":"https://saliu.com/bbs/messages/11.html","line":32}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{11}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05731406,-0.0543232,-0.0052161,-0.03180536,-0.08878181,0.05683637,0.02381468,0.02813697,0.05915152,0.00685704,0.00014195,0.00059286,0.0575039,-0.00523535,-0.00132852,0.00351027,-0.02035343,0.00856121,-0.10177322,0.0054705,0.09513381,-0.05152009,-0.07064342,-0.10423958,0.04119869,0.02161789,-0.02697737,-0.04752518,-0.02335366,-0.23482856,0.0079282,0.05549967,0.02228701,-0.07273016,-0.05921405,-0.04099461,-0.05726327,0.07791398,-0.08016791,0.02180303,0.00047616,0.03948748,0.00606731,-0.00273471,0.00212251,-0.02116806,-0.00387655,0.01858954,-0.00150276,0.00050009,-0.04955655,-0.00382962,-0.00878327,0.03945529,0.04050281,-0.01324165,0.04184685,0.10537259,0.02563117,0.06335402,0.05535633,0.10389852,-0.16775575,0.03446048,0.0107437,0.0144308,-0.03035211,-0.0244564,0.00671126,0.07398508,0.01616646,0.02597688,0.00196272,0.07106788,0.05041544,-0.0420031,-0.04799251,-0.084485,-0.03377461,0.03533291,-0.05071723,-0.02513894,-0.02380424,-0.00213724,-0.01650329,0.01311806,0.04171252,0.0202166,0.04376565,-0.04982832,0.04095426,0.052974,0.00614894,0.024174,0.03092793,0.01763785,0.01937469,-0.0339632,0.02363719,0.09966186,-0.01138217,-0.00270294,-0.00863078,0.00851653,0.02163818,-0.03345717,-0.02485256,-0.03523651,-0.04306181,0.01521922,0.05505873,-0.00808509,0.05310586,-0.00027945,-0.07630064,-0.0219379,0.02495362,0.00442715,0.01462984,0.01370384,-0.03548998,0.06702805,0.03464728,-0.05629623,-0.01110969,-0.01827075,0.00515933,0.07579551,-0.01263699,0.0301903,0.04962453,-0.01486656,-0.08274098,-0.01214374,0.01411646,-0.04873748,0.01362519,0.03502564,-0.0025078,0.0452434,0.00831121,-0.03326606,0.07336572,-0.08885928,-0.0164427,0.04610218,-0.00449472,0.03290541,0.03960234,-0.0088743,-0.00545442,-0.02265494,-0.06280124,-0.05246671,-0.00263723,0.00048436,0.10872491,0.05123333,-0.04289697,0.01352556,-0.03708846,-0.02142121,-0.02407353,0.13456504,-0.04970048,-0.06924519,-0.03402016,0.05534562,-0.03365982,-0.09818424,0.00157665,0.01569364,-0.0597686,0.01911763,0.09108864,-0.02472856,-0.11815036,-0.02354181,0.00370981,0.00730105,0.0443422,-0.02604986,-0.02542091,0.01230765,-0.04067355,-0.05107577,0.00183136,0.02850807,0.01352829,0.02444309,-0.00988047,0.04100958,-0.03855658,0.01720906,-0.01715173,-0.01987528,-0.02786755,-0.04760744,0.05543742,-0.02944143,-0.02257237,-0.01195456,0.01716478,0.06120771,-0.01608878,0.00956653,0.01468759,-0.04809582,0.08621602,0.04781214,-0.02788907,-0.03347018,0.02746892,0.08137906,-0.04156306,0.03538691,0.02123745,0.03444078,-0.00656475,0.01076747,-0.00895562,-0.0007281,-0.04473944,-0.18081488,-0.07758503,-0.04387579,-0.01006613,0.02483477,-0.01034853,-0.01693284,-0.05170709,-0.0001109,0.0458111,0.08256591,-0.06719706,-0.02209157,0.03837161,-0.04529246,-0.00987434,-0.13314909,-0.01993819,-0.05158588,0.0791763,0.00303443,0.01507207,-0.0412841,-0.11962922,0.00700307,-0.04398648,0.12609546,-0.00006382,0.00245833,-0.00395397,0.04858714,0.00317207,-0.00026518,0.0388176,0.03080372,0.02848816,-0.04624783,-0.02375341,-0.02863617,-0.012422,-0.07782117,0.03301799,-0.01719081,-0.10610777,-0.02843567,-0.02321135,-0.02843698,0.04658239,0.0107403,0.09169443,0.03891133,0.04282978,0.04278696,0.00678403,0.05759069,-0.03703456,-0.0496876,0.03791555,0.00927064,0.07949226,0.01903007,-0.04594597,0.05811777,-0.01136025,0.05837446,0.05187766,-0.03009848,-0.05175849,-0.00087134,-0.01302519,0.01103818,0.07409965,0.01659998,0.0349033,-0.02116664,0.01460537,0.05798348,0.01361654,0.02280897,-0.00722825,0.00341274,-0.06348106,0.01612114,0.08306146,0.03958948,-0.02274626,0.03387995,0.06347786,-0.00532147,0.00511685,-0.02772097,0.0534145,-0.04473736,0.01547283,-0.0365893,0.03773839,-0.24235618,0.05271284,-0.06043664,0.06599974,-0.03765162,-0.03226652,0.04904042,0.03742961,0.00120835,-0.04935287,0.05747602,0.03217967,0.02572541,-0.05946875,0.02562256,-0.04358827,0.02046491,-0.02966338,0.06678725,0.02035001,0.10295862,0.05042429,0.25345773,-0.00549949,0.012281,-0.00364525,0.03870501,0.04387747,0.00047877,0.02306308,-0.05008189,-0.00423614,0.07304675,-0.02695517,-0.02992146,0.01655773,-0.00999096,0.01744895,-0.02919209,-0.00296638,-0.05290009,-0.0345215,-0.05704994,0.06291401,0.14282751,0.0449825,-0.01207241,-0.0881803,0.0497336,0.04013592,-0.08453605,-0.0479299,-0.05434184,-0.0546636,0.00034259,0.0286922,0.02170359,0.00279205,-0.00621269,0.0008905,0.00956256,0.00977771,0.0714419,0.00490769,-0.03464399],"last_embed":{"hash":"w4sj51","tokens":466}}},"text":null,"length":0,"last_read":{"hash":"w4sj51","at":1753423483581},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{11}","lines":[77,132],"size":8976,"outlinks":[{"title":"The best lotto wheels for all lotto games are created by lottery wheeling software.","target":"https://saliu.com/ScreenImgs/lotto-wheels.gif","line":9},{"title":"The best software for all lottery and lotto games is known as Bright.","target":"https://saliu.com/ScreenImgs/lotto-b60.gif","line":38},{"title":"The mathematics of lottery strategies or systems starts with Single lotto numbers.","target":"https://saliu.com/gambling-lottery-lotto/lottery-book.gif","line":46},{"title":"![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.","target":"https://saliu.com/ScreenImgs/best-lotto-utilities.gif","line":52}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{12}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04497661,-0.05110436,-0.01090279,-0.03448671,-0.08686002,0.11570632,0.04535405,0.01978132,0.05221789,0.02416582,0.01908011,-0.02552292,0.00853141,0.02013684,-0.03322979,0.00472525,-0.05846033,-0.02234978,-0.05378836,0.00016297,0.115701,-0.05741779,-0.09189542,-0.09964168,0.05727122,0.04972518,-0.00161471,-0.07255186,-0.04942039,-0.20755818,0.02426552,0.03665338,0.01035964,-0.08937173,-0.07378066,-0.05379099,-0.01542648,0.10777549,-0.05872919,0.06244383,0.009284,-0.0209778,0.00353861,-0.00119272,0.00568522,-0.04029037,-0.0666486,-0.00996779,0.03444991,0.00370653,-0.02758967,0.02739603,0.00222915,-0.00367764,0.03447662,0.01504216,0.06370322,0.05005142,0.07196789,0.04290801,0.02001855,0.08094977,-0.16805632,0.04976498,0.00981153,0.00179131,-0.02878906,-0.04146386,-0.00740748,0.08649672,-0.00054645,0.05914184,0.01455975,0.03426869,0.07769443,-0.02148175,-0.03334427,-0.05444521,-0.04057176,0.0527944,-0.07780748,0.0250412,-0.02700134,-0.01172942,0.0027369,0.03376257,0.02295996,0.01249007,0.04974168,-0.00522497,0.0595909,0.06448594,-0.05967321,-0.01274636,0.01353964,0.02476857,0.02422775,-0.03909274,0.03963543,0.10148955,0.02723176,0.0025603,-0.00433038,0.0151701,0.02021478,-0.04108891,-0.04095424,-0.0423076,-0.02197122,0.00360961,0.01875438,-0.00184344,0.07308155,-0.03175979,-0.02488395,-0.00500138,-0.02256793,0.0045215,0.02822266,0.01067926,-0.01096796,0.03443342,-0.02397057,-0.01859096,0.03975022,0.02477545,-0.00832086,0.08364053,0.02703828,0.01424011,0.03647649,-0.03679566,-0.08146554,-0.04196063,0.00248435,0.01120937,0.00854391,0.04823413,0.0228138,0.03888887,-0.0072722,-0.04908593,0.04205954,-0.0661687,-0.05983871,0.10378942,-0.03898846,0.03390856,0.03541488,-0.01228594,-0.0059819,0.00254282,-0.04862425,-0.0625224,0.00495789,-0.03860539,0.03832136,0.08772186,-0.02762024,-0.0103518,-0.0619498,-0.05396263,0.00688371,0.13539,-0.01872896,-0.02086547,-0.01360229,-0.00781947,-0.02195027,-0.11030004,0.00062544,-0.01649196,-0.04285676,0.01063217,0.08282762,0.00646202,-0.03413904,-0.02707699,0.02425653,0.00062337,0.03013136,-0.02200407,-0.01902202,0.00756347,-0.05352131,-0.03401456,-0.00603578,-0.04347144,-0.007158,0.06747282,0.01001863,0.00365165,-0.07157309,0.04083052,-0.03315955,-0.03457061,-0.04169101,-0.03513565,0.03894348,-0.02572747,0.00369927,-0.02566129,0.02195577,-0.0120475,-0.02702001,0.06268477,0.03412099,-0.03723805,0.06881115,0.0439509,-0.006665,-0.05012822,-0.01964769,0.05560121,-0.0178428,0.05987582,0.04892221,0.01494168,0.00996116,-0.00154212,0.01390655,-0.00844423,-0.02370595,-0.16378218,-0.06761133,-0.00296182,0.00732294,0.0236201,0.01967971,0.03422888,-0.0348058,0.02916535,0.09801019,0.08190632,-0.04989163,-0.05265479,0.05297326,-0.05714893,-0.00188534,-0.1384149,-0.03235277,-0.0671073,0.06977532,-0.00862496,0.05035636,-0.08866559,-0.03934252,0.03909442,-0.01475246,0.14721702,-0.00193371,0.00872124,-0.01772843,0.07532141,0.02635266,-0.01767785,0.06528201,0.00377995,-0.02165505,-0.06477843,-0.02670497,-0.082205,-0.00722191,-0.06269574,0.01852253,-0.02253542,-0.07408848,-0.08139299,-0.01386906,-0.03087573,0.05691511,0.02695921,0.08457108,0.02026741,-0.00857753,0.07039715,0.01329402,0.07668999,-0.01844825,-0.0589424,-0.00083869,-0.03127084,0.07079487,0.03594448,-0.03366455,0.04083398,0.01291052,0.06653839,0.0318873,-0.00595294,-0.03016911,0.01266774,0.0065287,-0.01188936,0.07993762,0.02146727,-0.0063452,0.00078607,0.03124803,0.05813479,-0.01580646,0.02343513,-0.01974646,0.10314782,-0.08348973,-0.02483591,0.03916896,0.01419475,-0.03982198,0.06539153,0.08091971,-0.01740669,-0.00352834,-0.06373069,0.03831197,-0.05929883,-0.00106826,-0.03800557,0.00328737,-0.25985259,0.06885919,-0.05082606,0.08403926,-0.02534907,-0.03030601,0.03496681,0.05062362,-0.02753975,-0.02832457,0.04281491,0.0399747,0.03544477,-0.09056467,-0.01132883,-0.05000938,-0.03772876,-0.03353203,0.04935888,-0.00317776,0.10946677,0.02890656,0.20424904,-0.02598197,0.0187085,-0.00514865,0.03549528,0.01541608,0.00955319,0.00814801,-0.04153026,-0.00290499,0.07391446,-0.02727357,-0.0289905,0.02155139,0.013897,0.03234378,0.00800578,-0.0157746,-0.04903615,-0.00220029,-0.04073279,0.01301592,0.12561709,0.00711349,-0.04314563,-0.06440076,0.06643445,0.06690867,-0.09803939,-0.00635148,-0.03176682,-0.0637306,-0.03649607,0.00608779,-0.0033978,0.02100615,0.00509572,0.02104502,-0.01046036,0.02220833,0.06380963,0.01394408,-0.02659322],"last_embed":{"hash":"oz251u","tokens":230}}},"text":null,"length":0,"last_read":{"hash":"oz251u","at":1753423483733},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{12}","lines":[133,134],"size":824,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{13}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05617072,-0.04376243,-0.03991437,0.00015288,-0.08777656,0.09633972,-0.01171523,0.01967816,0.06786433,-0.0012572,0.0094116,0.00180647,0.00019801,0.02357058,-0.03124466,-0.01910217,-0.02667224,-0.03655453,-0.10091858,0.00260591,0.0779435,-0.06765922,-0.04221199,-0.09529947,0.06169494,0.02839194,0.00842047,-0.07172627,-0.02199457,-0.25322676,0.02262651,0.05878986,0.01516039,-0.07761583,-0.05463892,-0.04551464,-0.05593422,0.10664227,-0.03256181,0.06008895,0.00645177,-0.01119064,-0.01709422,-0.01846102,0.0240365,0.00678166,-0.05170036,-0.01663294,0.03236871,0.01505893,-0.03709682,-0.02908428,0.02062031,0.01945464,0.04612654,0.0050861,0.05724026,0.0603467,0.05411109,0.02520379,0.03237095,0.06568573,-0.1585619,0.02773323,-0.01740705,-0.00886155,-0.02616677,-0.03851376,-0.0094557,0.06698783,0.02461921,0.03497486,-0.02093759,0.0458837,0.0714569,-0.02736403,-0.04643929,-0.03651616,-0.02917196,0.03427922,-0.058173,-0.01406416,-0.01548087,-0.00398338,-0.00521494,0.02901648,0.03454655,0.03340389,0.04400099,-0.02776516,0.04869162,0.05644305,-0.01587163,0.02652034,-0.00115001,0.01414927,0.03296561,-0.03768085,0.01424569,0.08535463,0.03537117,0.02438573,0.03762495,0.04464708,0.02833052,-0.0360795,-0.04929696,-0.04702437,-0.01780831,0.00546963,0.04542815,0.00711602,0.07432894,-0.06233166,-0.06663346,0.0152985,-0.00021872,-0.01078095,0.01626277,0.01597996,-0.05140855,0.04652048,-0.03175052,-0.05905891,0.01793472,0.03257244,-0.00645069,0.07742752,0.03988983,0.00251178,0.03199305,-0.00805235,-0.0933442,-0.05608629,-0.05733163,-0.00103027,0.02197154,0.0343875,0.01576625,0.02168899,0.02834517,-0.03105282,0.03527958,-0.09521462,-0.00679613,0.08275639,0.01766142,0.04449937,0.03560183,0.01304233,-0.01324925,0.00730108,-0.04810674,-0.09162357,-0.02004186,-0.00828157,0.02527853,0.11381856,-0.04754657,0.02773458,-0.00299201,-0.04904829,-0.00896309,0.12754005,-0.03021584,-0.05741786,-0.02359816,0.02154627,-0.00625919,-0.09854626,-0.02180014,0.01288545,-0.019381,0.02244558,0.09981016,-0.01747212,-0.10216726,-0.06310129,-0.00720327,0.02147936,-0.00061205,-0.00629668,-0.02167784,0.0368145,-0.05554386,-0.09299894,0.042823,-0.0174291,0.00724529,0.0641914,0.0135654,0.05327285,-0.07510534,0.10177486,-0.01221077,-0.04801688,-0.00810761,-0.02951853,0.0423194,-0.0376442,-0.02418103,-0.02117693,0.03073832,-0.00679804,-0.00668657,0.05902347,0.05104848,-0.06379412,0.08967465,0.06045383,-0.02510974,-0.01646476,0.01218386,0.0843242,-0.02315885,0.02798829,0.02848801,0.00899396,0.05992573,-0.00807967,0.03945183,0.00517091,-0.0213045,-0.17986771,-0.07836673,-0.01404942,-0.01773862,0.04161413,-0.02026721,0.02356754,-0.00819801,0.03337197,0.06944256,0.05488846,-0.03470488,-0.03119807,0.0559067,-0.04338284,-0.02238982,-0.11749558,-0.01632922,-0.05614325,0.0666093,-0.03352264,0.06276473,-0.03503467,-0.06544665,0.01641602,-0.03009138,0.1480106,0.02562495,-0.02507172,-0.02519612,0.07099976,0.01101617,-0.02750086,0.01410088,0.01213301,0.01325217,-0.0592002,-0.00337713,-0.03809847,-0.00888062,-0.05300513,0.00987404,-0.01363677,-0.07774326,-0.07095957,-0.01367746,-0.06177409,0.04682992,0.01445937,0.06836455,0.00172483,-0.01784051,0.05750138,-0.00422552,0.08308666,-0.02322099,-0.0862613,-0.01374476,-0.01978706,0.08093455,0.00789213,-0.02403984,0.07328867,0.00975861,0.0444664,0.06130604,0.00340962,-0.02684696,0.00804378,-0.0083261,-0.00367254,0.06854304,0.0277305,-0.01166852,-0.01135109,0.00941654,0.06116163,0.00288709,0.0230612,0.02163832,0.05608604,-0.11628187,-0.01153078,0.04666878,0.01197913,0.01127263,0.02715845,0.06278178,-0.05776859,-0.01644792,-0.0309806,0.04249463,-0.03770015,0.04210545,-0.01449573,-0.00141597,-0.23356795,0.04010032,-0.04309405,0.07848734,-0.00954296,-0.01103323,0.0519663,0.01546902,0.00078329,-0.04807162,0.05494403,0.03630473,0.04465815,-0.08865099,0.03572426,-0.03001628,-0.0371531,-0.03799592,0.04359774,-0.00093453,0.0897092,0.06405248,0.23237765,-0.02818113,-0.02679948,-0.01247734,0.02453795,0.00376949,-0.00600297,0.02420986,-0.02055,-0.01894442,0.0308205,-0.02117895,-0.02642471,-0.00451796,-0.00764478,0.0135078,-0.02232079,-0.01767319,-0.06904711,-0.02625369,-0.0667094,0.00332041,0.15412597,0.02729385,-0.03973764,-0.06208681,0.0678011,0.06651857,-0.02940753,-0.0532825,-0.03126163,-0.06154078,-0.01505853,0.00641983,0.0022655,0.01241832,-0.0010422,0.04454436,0.000577,0.03068031,0.04233029,0.01092012,-0.02173418],"last_embed":{"hash":"a20xaa","tokens":497}}},"text":null,"length":0,"last_read":{"hash":"a20xaa","at":1753423483799},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{13}","lines":[135,161],"size":7773,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{15}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04503148,-0.03575008,-0.03686065,-0.02418648,-0.09197462,0.07823364,-0.00967718,-0.00365163,0.07989139,-0.00324396,0.00640341,-0.05346782,0.05660824,0.03145015,-0.00708076,-0.03310647,-0.05402388,-0.03540046,-0.08472285,-0.01831675,0.11993579,-0.10064588,-0.07356128,-0.13052666,0.06015378,0.07912114,0.02169895,-0.09793772,-0.03194584,-0.24862806,0.02945086,0.03506506,-0.01477341,-0.06996063,-0.06786388,-0.03050202,-0.02489519,0.08927221,-0.02545762,0.02805202,-0.00075811,0.00029846,-0.02069243,-0.03595671,-0.00530546,-0.02411706,-0.05069205,-0.0097078,0.01456777,0.00005699,-0.04995684,-0.02434678,0.03600857,0.00961521,0.05197951,0.01597183,0.0492138,0.04559261,0.02083419,0.04886986,0.02077674,0.07700294,-0.15568404,0.01080371,0.01802418,-0.00858961,-0.04297251,-0.03250191,0.01330923,0.07821336,0.02937962,0.02280347,0.00881823,0.04297355,0.05205336,-0.04030294,-0.07036742,-0.03741317,-0.04560966,0.07752775,-0.07916291,-0.00168412,-0.01620185,0.01999907,0.0037917,0.01436288,0.00733601,0.0242219,-0.00569369,-0.05006893,0.00570022,0.02804399,-0.01548764,0.00605727,0.01823731,0.05885097,0.04308321,-0.02062519,0.03307354,0.10277074,0.01256486,0.02569051,0.0241051,0.01984426,0.01779862,-0.05915936,-0.05157737,-0.06512767,0.02160941,-0.02573741,0.02535134,-0.03311386,0.05398893,-0.03298085,-0.04115329,0.00920958,0.00029058,0.03105233,0.02110539,0.00270189,-0.01109609,0.06200022,-0.03121901,-0.03842306,0.018589,0.00654806,-0.0107328,0.07927778,0.03837731,0.05384211,0.06319144,0.00799706,-0.07632197,-0.04477865,-0.03369507,-0.00765477,0.00108236,0.02950339,0.03965226,0.03454006,0.05069689,-0.05950495,0.03735723,-0.08241362,0.00218385,0.12554544,0.00446163,0.00844925,0.03862664,0.02212917,-0.01788679,-0.00700914,-0.03821498,-0.08851781,0.00176542,-0.06358967,0.0970556,0.08273744,-0.01264143,0.03967457,-0.01033085,-0.0546911,-0.00361335,0.12754758,-0.00367278,-0.0687101,0.00247102,0.02645136,-0.01970947,-0.07395285,0.01573977,0.03216368,-0.03426483,0.00304615,0.07407627,-0.01383366,-0.09223697,-0.05128055,0.02306991,0.04415131,-0.02868242,0.00209851,-0.04010675,0.00665274,-0.04817104,-0.03637242,0.02375001,-0.03022143,-0.00336257,0.05902897,0.03031866,0.02058318,-0.07485836,0.08198979,-0.01679509,-0.04654439,-0.01747481,-0.01286834,0.0008485,-0.03223125,-0.0227629,-0.01454328,0.01840054,0.01842496,-0.0091823,0.05977115,0.00959546,-0.03023001,0.04086517,0.05081083,-0.01864925,-0.0355973,0.00350411,0.05594252,-0.0368895,0.03373629,0.01528226,0.00034591,0.0079028,0.01657756,0.01124743,-0.00049005,-0.02957343,-0.18718168,-0.07048544,-0.00693566,-0.02008068,0.0627161,-0.02955833,0.00890311,-0.02846197,0.01502306,0.06237655,0.08200829,-0.05670951,-0.00937733,0.05378804,-0.04537068,-0.00259346,-0.09885049,0.01123512,-0.0278597,0.07539652,-0.03100473,0.06708589,-0.04732089,-0.04082477,0.03272583,-0.02203462,0.15251081,0.0377035,-0.01808695,-0.02875896,0.04334638,0.01566927,-0.05792375,-0.00328446,-0.0006474,0.0286585,-0.03070064,-0.03666081,-0.01890733,-0.00128134,-0.06397726,-0.00489656,-0.00483723,-0.07796399,-0.03314348,-0.018141,-0.0505524,0.04547084,0.01771476,0.09104237,0.02768039,-0.0187872,0.0608468,-0.01000001,0.01519382,-0.01506694,-0.07687433,-0.01451623,-0.02712907,0.07661442,0.03591446,-0.01316984,0.05106458,0.00060247,0.04547592,0.04386791,-0.02195754,-0.03700894,0.02757842,0.00291749,0.00927957,0.05929592,0.0461856,0.0484644,0.00073871,0.02585963,0.04596718,-0.00467869,0.01993141,0.01030824,0.07057261,-0.0743333,-0.01303981,0.00935298,-0.01706169,0.01027961,0.09302942,0.10261237,-0.02721263,0.00600278,-0.04301031,0.03153183,-0.02637464,0.05035509,-0.01534276,-0.0204377,-0.26259026,0.0430118,-0.04431702,0.04320058,0.00189902,-0.03498263,0.06270979,-0.00243374,-0.01394837,-0.07636552,0.06092595,0.06389297,0.028475,-0.0726973,0.02249594,-0.0123091,-0.0155161,-0.03802769,0.04614661,-0.02775909,0.07456189,0.02373918,0.25287476,0.00621516,0.02485605,0.01489873,0.02601138,0.03172388,-0.00862749,0.00768224,-0.04351157,-0.0082396,0.04766245,-0.03655695,-0.01302211,0.04974607,-0.026481,0.00712856,0.03391614,-0.01612413,-0.03301929,-0.05868742,-0.054904,-0.00310481,0.14675595,0.01149497,-0.04744533,-0.02368932,0.04540066,0.05596787,-0.08293297,-0.02747373,-0.0281743,-0.0368604,-0.01009602,0.00146094,-0.00150713,0.02862436,-0.01081352,0.00890786,0.00428801,0.03291379,0.0178208,0.02365698,0.014823],"last_embed":{"hash":"1336x2k","tokens":452}}},"text":null,"length":0,"last_read":{"hash":"1336x2k","at":1753423483983},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{15}","lines":[164,190],"size":5352,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{17}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.04225756,-0.03603521,-0.03779623,-0.04140711,-0.04823254,0.07880593,0.01090667,0.02885528,0.04391797,-0.00403215,0.0076655,-0.02795688,0.06704115,0.01632328,-0.02834658,-0.02935583,-0.0011112,-0.02505693,-0.05657835,0.01406294,0.12161896,-0.06656505,-0.07492979,-0.09261806,0.07426626,0.02595156,-0.01836977,-0.06163531,-0.03267652,-0.24223563,0.01970162,0.03802549,0.02086292,-0.05988459,-0.08456199,-0.04770536,0.00438383,0.08130164,-0.03960268,0.03704408,0.02567893,-0.00677453,0.00650851,-0.02101949,0.01357572,-0.02816424,-0.02328067,-0.01819463,-0.00702527,-0.00754739,-0.05405353,0.0112256,0.00631919,0.03589181,-0.00341774,0.01742121,0.05500446,0.07205579,0.07040342,0.05657946,0.01749135,0.06561055,-0.17810595,0.02901736,-0.02146239,-0.02222071,-0.02344022,-0.03789777,-0.00396522,0.04387644,0.01872578,0.08795212,0.0200526,0.0592496,0.03976556,-0.0641636,-0.02827516,-0.06119291,-0.06432082,0.05324167,-0.05875663,0.02800864,-0.00985862,-0.00159208,-0.00078267,0.03922047,0.0088183,-0.02021455,0.0553646,-0.00378351,0.05457101,0.0234008,-0.00420952,0.034012,0.01160509,0.00574656,0.025531,-0.03072932,0.00221747,0.09129446,0.05315014,0.02928164,0.02724159,0.01303637,0.04728577,-0.05492592,-0.03177752,-0.06145327,0.03935623,0.0165689,0.00052216,-0.01263546,0.07498541,-0.01614178,-0.02215686,0.0105515,0.03002068,0.01643254,0.04995939,0.01121698,-0.04758254,0.04612864,-0.0076772,-0.0418451,0.0371087,-0.02895965,-0.03165209,0.05082314,0.03256461,0.03211654,0.05909712,0.00425599,-0.07352658,-0.05510165,-0.00566057,0.0295133,0.01511328,0.04737057,0.03970098,0.03985175,0.00417845,-0.05371093,0.00973101,-0.0679287,-0.06171345,0.13279936,-0.01229471,-0.01035126,0.0027616,-0.00463117,0.03069888,-0.00141368,-0.07850346,-0.046981,0.01249025,-0.06131983,0.07207202,0.09100216,-0.06230707,0.00269809,-0.06447236,-0.04155523,-0.00397342,0.15171701,-0.01184536,-0.05803008,-0.00801423,0.01262028,-0.03158586,-0.05428294,0.03012233,0.00512564,-0.05981908,0.03622045,0.07597817,0.00099302,-0.02549465,-0.02837525,0.0105198,0.02343241,0.00892545,-0.05720127,-0.04957155,-0.0043383,-0.06587713,-0.03741478,-0.01923294,-0.02287501,-0.04164213,0.04106688,-0.03020254,0.00537775,-0.06737339,0.029675,-0.03460756,-0.02304311,0.00691792,-0.0336027,-0.00371898,-0.04155265,-0.03079411,-0.03306866,0.03374019,0.02188511,-0.05691523,0.05290236,0.01663573,-0.0448809,0.11233839,0.05409012,-0.01538241,-0.03297706,-0.00657761,0.0361699,-0.01250115,0.02443679,0.04145867,-0.0005799,-0.01152348,0.01757233,0.01151191,0.02383666,-0.07064166,-0.19874544,-0.06316066,-0.05647209,-0.02894171,0.07962375,-0.0154511,0.02717198,-0.02365768,0.0117551,0.10652304,0.06155604,-0.05292634,-0.0287435,0.04806065,-0.04440768,-0.03275581,-0.1221816,-0.05388262,-0.07332134,0.02755547,-0.01452076,0.05959912,-0.07327174,-0.06801082,0.03069046,-0.01871468,0.13746937,0.01412221,-0.00511952,-0.00475565,0.05151045,0.00529415,-0.01425255,0.03570652,0.00407611,0.02157257,-0.02721411,-0.01913461,-0.03810716,-0.00195607,-0.05028904,-0.00279327,0.00126744,-0.06422904,-0.0825118,0.02253004,-0.03372402,0.05677795,0.01536048,0.08939695,0.02798732,-0.02900022,0.09464141,-0.00371881,0.04975107,-0.03690543,-0.06491084,-0.01923327,0.00779728,0.07892755,-0.00590456,-0.00558134,0.07795648,-0.02206011,0.02727472,0.01652283,-0.03336425,-0.03222521,0.01541797,0.01137934,-0.01655678,0.09717516,-0.00829567,-0.00229241,-0.01231208,0.03640928,0.04952623,0.00698306,0.03499755,-0.03289124,0.06450157,-0.04196531,0.03187583,0.0341133,0.006756,0.00935717,0.07860047,0.06149941,-0.01554535,-0.00716037,-0.04376229,0.03967605,-0.04117903,0.02102707,0.00652645,0.01271478,-0.25875282,0.01422358,-0.03721943,0.06218713,-0.01921864,-0.05561627,0.03429123,0.0090022,-0.00750348,-0.04646499,0.05680046,0.03921458,0.05713892,-0.07122687,0.00532355,-0.03703928,-0.04217859,-0.06569675,0.05124534,0.01543207,0.0849096,0.05052488,0.24112649,-0.03190344,-0.03178965,0.00838501,0.05222796,0.04883495,-0.01688117,0.00736674,-0.03022529,0.00032032,0.08526406,-0.01380649,0.00451811,-0.00786348,-0.0209122,0.03480246,0.01862163,-0.02351298,-0.02052543,-0.0361993,-0.02881908,0.03923461,0.13080619,-0.01035141,-0.05964004,-0.07865492,0.06556522,0.06962473,-0.0570473,-0.01012517,-0.00491415,-0.03029114,0.01647447,0.01846793,0.00714358,0.02681663,-0.01176502,-0.00050927,-0.00967837,0.01621691,0.04376243,0.04496537,0.02265848],"last_embed":{"hash":"1ei684f","tokens":442}}},"text":null,"length":0,"last_read":{"hash":"1ei684f","at":1753423484128},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#<u>1. Protomathematics of Lottery</u>#{17}","lines":[193,207],"size":2465,"outlinks":[{"title":"Ion Saliu's Probability Book on mathematics of lottery, math of lotto.","target":"https://saliu.com/gambling-lottery-lotto/probability-book-Saliu.jpg","line":11},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":11},{"title":"Get useful resources in lottery mathematics, Software, theories, systems, strategies.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":14}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#[Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://saliu.com/content/probability.html)": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10579016,-0.04602235,-0.03672714,0.00936687,-0.06902637,0.0741577,-0.0069854,-0.00342414,0.04342317,0.00030293,-0.01779569,-0.03536273,0.04376889,0.0188009,-0.00728471,-0.02546066,-0.01854404,0.03313687,-0.04060988,0.0103873,0.10302879,-0.07670746,-0.06890573,-0.09968644,0.04659577,0.02138188,-0.02867661,-0.04965729,-0.01245904,-0.20762071,0.02734883,0.02518166,-0.00624051,-0.05997029,-0.07663909,-0.03136243,-0.04528359,0.04832835,-0.08866481,0.02921475,0.02207547,0.00591849,-0.01685532,-0.00452803,0.04554802,-0.04691327,0.01508776,0.0176329,0.05031192,-0.01783052,-0.05862859,0.01835342,-0.0147745,0.02629665,0.070448,0.06310601,0.06443297,0.08783074,-0.00264815,0.0305955,0.0047181,0.08273814,-0.18127103,0.05409607,0.00679105,0.03644006,-0.00454517,-0.00141888,0.02566071,0.04189362,0.0089837,0.0282206,0.00772959,0.04730314,0.0354159,0.01871871,-0.03272223,-0.0403698,-0.01240095,0.02753133,-0.0513381,-0.00990851,-0.01691318,0.0213901,0.00393731,0.07082763,0.0166122,0.01490426,0.09813751,-0.08255135,0.00509416,0.05379145,0.03470397,0.02600588,0.05180348,0.01140026,0.03715606,-0.01904424,0.00545539,0.10579338,0.0130525,-0.00941266,-0.0002721,0.00106866,0.02434515,-0.09649293,-0.03478885,-0.0619151,-0.0160473,0.05632905,0.04933089,-0.02138865,0.04000376,-0.01190116,-0.04547216,0.00265586,0.01146357,0.03531763,0.03361,-0.01518308,-0.08277822,0.0358179,0.02081561,0.00470437,-0.00203499,-0.0077219,-0.02887668,0.0845625,0.01516966,0.02916702,0.03660288,0.00670437,-0.10573521,-0.04540805,-0.01393159,-0.03423592,0.04302709,-0.01731199,-0.01956921,0.03351571,-0.00586013,-0.05165408,0.06386836,-0.10197603,-0.02225098,0.07881381,0.02429015,-0.00756451,0.01557467,0.00420755,0.00435431,-0.01516668,-0.01890029,-0.10265831,-0.00321155,0.00809884,0.10584491,0.07492434,-0.05351874,0.03440275,-0.04386665,-0.02350978,-0.04569731,0.16577174,0.00623521,-0.09228035,-0.0045327,0.04164994,-0.02376871,-0.07994916,-0.01748578,0.0233545,-0.03702671,-0.01293916,0.12140837,-0.01467656,-0.09548035,-0.07906325,-0.01318152,0.0043313,-0.011023,-0.04930823,0.00465877,-0.00137901,-0.03312492,-0.0659628,0.0099461,-0.03870497,-0.01911799,0.0387453,-0.03858516,0.00334664,-0.0536692,-0.00050154,-0.04292594,-0.02204978,-0.04556813,-0.05779245,0.04671429,0.01070325,-0.02883542,-0.01809707,-0.00124272,-0.00730751,-0.04826542,0.02144484,0.00909509,-0.05717584,0.07483726,0.04695969,0.00657656,-0.01221259,0.0393931,0.05284688,-0.05977619,0.03116617,0.03096069,0.01175402,-0.01441158,0.02193355,-0.00360421,0.02253962,-0.09019755,-0.16528931,-0.05956226,-0.04880168,-0.02786631,0.02570225,-0.04126842,0.03018684,-0.05044563,0.02461196,0.08711267,0.09737254,-0.06384265,-0.01395937,0.0509816,-0.03769061,-0.0370819,-0.07030077,-0.03486205,-0.05497729,0.03441754,-0.00330339,0.02165578,0.01028169,-0.09166961,0.01391396,-0.02222993,0.13670948,0.0100697,0.03295274,0.04106074,0.03419308,0.02352035,-0.01823775,-0.04090066,0.01946144,0.06364228,-0.00311682,-0.01694518,-0.04550778,-0.0022879,-0.0663514,0.015617,0.01851014,-0.08199541,-0.05704409,0.00124715,-0.02774663,0.03836941,-0.00571143,0.05386328,0.03745548,0.00061797,0.0649273,0.01287778,0.03452517,0.01374839,-0.05337601,0.00252367,-0.03127033,0.04870649,-0.01493174,-0.03337696,-0.0198629,-0.01352283,0.04917114,0.00608014,-0.01209446,-0.03794242,0.0244355,0.00510475,0.00452148,0.083813,0.03272329,0.06042949,0.02544431,0.01399872,0.10582248,-0.04805622,0.00143897,0.00984007,-0.02626442,-0.05549224,0.04593861,0.06175362,0.07158571,-0.00389872,0.05169922,0.04787966,-0.0055047,-0.01297945,-0.01267733,0.02255877,-0.03924145,0.03038362,0.03471955,0.03256179,-0.27357256,0.05500672,-0.02388609,0.05275002,-0.02293865,-0.04286027,0.02190076,-0.03827224,0.00521966,-0.03877865,0.06155429,0.01044576,0.01144128,-0.04157775,-0.00198246,-0.02437165,0.01690275,-0.04211324,0.09822966,0.03589615,0.03080208,0.05667895,0.25318894,0.00402486,0.01090817,0.0146689,-0.00028471,0.03510271,-0.00167879,0.04207272,0.01453893,0.01171654,0.04509551,-0.02370947,-0.02965851,0.07046685,-0.02457413,0.01700941,-0.00057485,-0.00909775,-0.05708595,-0.02224322,-0.00932521,0.04823893,0.10724677,0.02286172,-0.01461293,-0.07905076,0.0573929,0.06602426,-0.09684683,-0.01975695,-0.07595518,-0.02224751,0.00618408,0.02810312,0.0342068,-0.02793145,-0.00375601,-0.01372808,0.03642594,-0.04236642,0.07116664,0.02236009,-0.01302621],"last_embed":{"hash":"19amy2e","tokens":417}}},"text":null,"length":0,"last_read":{"hash":"19amy2e","at":1753423484269},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#[Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://saliu.com/content/probability.html)","lines":[216,237],"size":2631,"outlinks":[{"title":"Resources in Theory of Probability, Mathematics, Statistics, Combinatorics","target":"https://saliu.com/content/probability.html","line":1},{"title":"_**Lotto Lottery Software**_","target":"https://saliu.com/LottoWin.htm","line":3},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":5},{"title":"_**Lottery Pairs System, Lotto Pair Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":6},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":7},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":8},{"title":"_**Mathematical Presentation of Lottery, Including Software Systems**_","target":"https://saliu.com/lottery.html","line":9},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":10},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":11},{"title":"_**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":12},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":13},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":14},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":15},{"title":"Best lottery mathematics book, lotto maths established by Ion Parpaluck Saliu.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":17},{"title":"Forums","target":"https://forums.saliu.com/","line":19},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":19},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":19},{"title":"Contents","target":"https://saliu.com/content/index.html","line":19},{"title":"Help","target":"https://saliu.com/Help.htm","line":19},{"title":"Home","target":"https://saliu.com/index.htm","line":19},{"title":"Software","target":"https://saliu.com/infodown.html","line":19},{"title":"Search","target":"https://saliu.com/Search.htm","line":19},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":19},{"title":"Thanks for visiting the site of lottery, lotto of mathematics and software!","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#[Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://saliu.com/content/probability.html)#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10489476,-0.04272338,-0.03820197,0.01266922,-0.06628279,0.07256685,-0.00833215,-0.00250903,0.04824854,0.00029449,-0.01801909,-0.03415954,0.04066283,0.01886032,-0.00900401,-0.02093462,-0.019984,0.03082182,-0.03738426,0.01237264,0.10155175,-0.07554055,-0.06422544,-0.10017737,0.04519867,0.01994654,-0.02705468,-0.05201707,-0.01230065,-0.2063444,0.0276626,0.02195995,-0.00838942,-0.05922594,-0.07961044,-0.03040512,-0.04401056,0.04817121,-0.08798648,0.02625116,0.01904562,0.00794199,-0.02052708,-0.00383828,0.04363083,-0.04794861,0.01738752,0.01670703,0.0498506,-0.01642514,-0.05832261,0.01676348,-0.01790411,0.02224621,0.06768988,0.06395824,0.06116626,0.09085155,-0.0045589,0.0312045,-0.00205744,0.08351994,-0.18173063,0.05489677,0.0049015,0.04058568,-0.0082104,-0.00018197,0.02684343,0.04005409,0.00860197,0.02912217,0.00816452,0.05030408,0.03758133,0.02028893,-0.02890154,-0.03800077,-0.01400327,0.0303589,-0.04869213,-0.01046124,-0.01765935,0.02513868,0.00018107,0.07114165,0.0190687,0.01219529,0.10046192,-0.08437295,0.0045649,0.05569149,0.02984435,0.02322458,0.05093525,0.01177877,0.03942019,-0.01760614,0.00647575,0.10333848,0.01160366,-0.00872024,-0.00358941,-0.00172978,0.02392228,-0.09719823,-0.03762227,-0.06189185,-0.0179475,0.05557451,0.05029185,-0.02147663,0.03698642,-0.01197748,-0.04870232,0.00528315,0.01222375,0.0353966,0.03150209,-0.01369561,-0.08826682,0.03330571,0.02387138,0.0046164,-0.00363394,-0.00907283,-0.02728123,0.08268137,0.0144674,0.02660672,0.03671521,0.00471029,-0.10312702,-0.04429081,-0.01318513,-0.03765136,0.0401611,-0.01676074,-0.02238339,0.03189101,-0.00640246,-0.04980839,0.06176377,-0.10564308,-0.02552095,0.08030771,0.02684661,-0.00601256,0.01458889,0.00217035,0.00449721,-0.01177237,-0.0181823,-0.10322588,-0.00132353,0.00778202,0.10689574,0.07283971,-0.05664667,0.03520821,-0.04761453,-0.02344976,-0.04335817,0.16476545,0.00645546,-0.09108415,-0.00817422,0.0424543,-0.02228506,-0.07992727,-0.01692419,0.0229634,-0.03409846,-0.01395718,0.11858767,-0.01311233,-0.10178047,-0.07892677,-0.01158102,0.00126619,-0.01098323,-0.0473043,0.00740638,-0.00254582,-0.03389353,-0.06719369,0.01093181,-0.03640009,-0.01812193,0.03946881,-0.03944983,0.00295585,-0.04749705,-0.00024082,-0.04477325,-0.02162641,-0.04563511,-0.0576405,0.04632208,0.00891189,-0.03096544,-0.02094376,-0.00431456,-0.00864609,-0.04997697,0.01991636,0.00825065,-0.0589525,0.07705598,0.04185528,0.00659731,-0.00904447,0.04053758,0.05105026,-0.06103875,0.03282823,0.02663297,0.01720648,-0.01529313,0.0241652,-0.00482015,0.02311741,-0.09012319,-0.16688827,-0.05959355,-0.04896913,-0.0275736,0.02406271,-0.04124074,0.02861081,-0.05226027,0.02334277,0.08477007,0.09848127,-0.06198147,-0.01257431,0.0558843,-0.03731035,-0.03669845,-0.06591736,-0.03280004,-0.05469897,0.03674085,-0.00081643,0.01863283,0.0127376,-0.089812,0.0150339,-0.0240235,0.13685857,0.00912768,0.03212032,0.0441521,0.02949206,0.02577785,-0.01834906,-0.04535209,0.01968986,0.06594056,0.00133509,-0.01802599,-0.04130493,-0.00271495,-0.06125721,0.01577012,0.01931562,-0.08597914,-0.0609417,0.00089594,-0.02556043,0.04070143,-0.00947217,0.05305048,0.03885482,0.0021695,0.06875476,0.01207076,0.03122296,0.01243084,-0.05432402,0.00611214,-0.03206826,0.04664578,-0.01189216,-0.03382951,-0.02304272,-0.01318176,0.05370143,0.00082227,-0.00778947,-0.03594628,0.027231,0.00517972,0.007243,0.08509836,0.02687521,0.06102114,0.027347,0.01266832,0.10552663,-0.05176604,0.00015617,0.01325996,-0.02542564,-0.05437215,0.04576816,0.06425379,0.06995808,-0.00205639,0.05408921,0.04769835,-0.00468835,-0.01246995,-0.00993098,0.02174285,-0.04068426,0.03323771,0.03663182,0.03377336,-0.27394462,0.05577636,-0.02323034,0.05332171,-0.02428131,-0.04426468,0.02128548,-0.0398238,0.00735747,-0.03390082,0.06295504,0.00786421,0.01178764,-0.03846894,-0.00009384,-0.02417317,0.01839147,-0.03935761,0.10097728,0.03484463,0.02943503,0.05407565,0.25085324,0.0014515,0.01280481,0.01466792,-0.00162295,0.03474694,-0.00207951,0.04190457,0.01677054,0.01069067,0.04496353,-0.02585001,-0.03283303,0.0731074,-0.02172374,0.01848599,-0.00707373,-0.01216078,-0.05340685,-0.01951025,-0.00590665,0.04863642,0.10813935,0.02241284,-0.01560527,-0.07809748,0.05464927,0.0663294,-0.09799124,-0.01599999,-0.07480647,-0.02418157,0.00834644,0.02842187,0.03364084,-0.02884262,-0.00323328,-0.01248479,0.03820524,-0.04165758,0.07131762,0.02682357,-0.01009537],"last_embed":{"hash":"1ugvtpm","tokens":417}}},"text":null,"length":0,"last_read":{"hash":"1ugvtpm","at":1753423484408},"key":"notes/saliu/Lottery Mathematics, Lotto Mathematics, Lottery Math.md#Lottery Mathematics, Lotto Mathematics, Lottery Math#[Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://saliu.com/content/probability.html)#{1}","lines":[218,237],"size":2498,"outlinks":[{"title":"_**Lotto Lottery Software**_","target":"https://saliu.com/LottoWin.htm","line":1},{"title":"_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_","target":"https://saliu.com/bbs/messages/638.html","line":3},{"title":"_**Lottery Pairs System, Lotto Pair Strategy**_","target":"https://saliu.com/bbs/messages/645.html","line":4},{"title":"**<u>Lottery Skip Systems</u>**","target":"https://saliu.com/skip-strategy.html","line":5},{"title":"_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_","target":"https://saliu.com/reverse-strategy.html","line":6},{"title":"_**Mathematical Presentation of Lottery, Including Software Systems**_","target":"https://saliu.com/lottery.html","line":7},{"title":"_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_","target":"https://saliu.com/neural-networking-lottery.html","line":8},{"title":"_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_","target":"https://saliu.com/strategy-gambling-lottery.html","line":9},{"title":"_**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**_","target":"https://saliu.com/delta-lotto-software.html","line":10},{"title":"_**Lotto Decades, Last Digits, Systems, Strategies, Software**_","target":"https://saliu.com/decades.html","line":11},{"title":"_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_","target":"https://saliu.com/markov-chains-lottery.html","line":12},{"title":"**the best lottery software**","target":"https://saliu.com/infodown.html","line":13},{"title":"Best lottery mathematics book, lotto maths established by Ion Parpaluck Saliu.","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":15},{"title":"Forums","target":"https://forums.saliu.com/","line":17},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":17},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":17},{"title":"Contents","target":"https://saliu.com/content/index.html","line":17},{"title":"Help","target":"https://saliu.com/Help.htm","line":17},{"title":"Home","target":"https://saliu.com/index.htm","line":17},{"title":"Software","target":"https://saliu.com/infodown.html","line":17},{"title":"Search","target":"https://saliu.com/Search.htm","line":17},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":17},{"title":"Thanks for visiting the site of lottery, lotto of mathematics and software!","target":"https://saliu.com/gambling-lottery-lotto/HLINE.gif","line":19}],"class_name":"SmartBlock"},
