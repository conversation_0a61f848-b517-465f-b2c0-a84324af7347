"smart_sources:Obsidian/Obsidian Companion.md": {"path":"Obsidian/Obsidian Companion.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.02582082,-0.00175338,0.03673466,-0.02046532,-0.02581701,-0.01430309,-0.00774147,0.10029052,-0.0425639,-0.03876118,0.04406574,-0.0233354,0.01371418,0.03798992,0.06049973,-0.00131819,-0.03234276,0.07554092,0.0187286,-0.02069149,0.00716784,-0.08723961,0.04971474,0.00166548,-0.05853705,0.04573788,0.00625565,-0.09215401,-0.02048062,-0.19627373,0.03558311,0.03203005,-0.01755504,-0.00634825,-0.05877111,0.03714186,-0.04254191,-0.00679436,-0.04050489,0.02492955,0.01131864,0.02060677,0.00834646,-0.03257547,-0.00461002,-0.04361978,-0.00292633,-0.0155289,0.02933269,-0.09618378,0.02655538,-0.03991272,-0.02745021,-0.0033208,0.00222189,0.01928799,0.08746052,0.02998571,0.00470893,0.07567145,0.07159323,0.04458809,-0.13773216,0.09931962,-0.0067043,0.03657418,-0.02172223,-0.02562496,-0.01640169,0.00839658,0.03047273,0.08515616,-0.05019685,0.00782512,0.01339336,0.02385495,0.03816772,-0.00927175,0.01516441,0.02730112,0.01197825,0.07858647,-0.04987023,0.02211551,0.02687837,-0.04304016,0.01687976,-0.02709555,-0.00695511,-0.02542612,-0.0557228,-0.03831064,0.03966872,0.03098752,0.01372823,0.03382744,0.06123227,-0.04485171,-0.08771916,0.13262933,-0.03233791,-0.01538268,0.02468972,-0.03541865,0.05316588,-0.01512404,-0.00536815,-0.07068606,-0.01970625,0.07444558,0.01852407,0.04948593,-0.00608515,-0.06661264,0.03253951,0.05657636,-0.0279869,-0.00268523,0.0303972,0.01241881,-0.02820203,0.02323467,0.08501175,-0.01771096,0.05555085,0.00748457,0.08385165,0.01661155,0.05007592,0.12528427,0.07813577,0.02760457,-0.01039608,-0.07329658,-0.03081326,-0.00528533,-0.04510169,-0.06334333,-0.02116646,0.00776407,-0.01820817,0.03863339,0.02998571,-0.05398266,0.06961259,0.0994031,-0.0416463,0.02232428,-0.05316414,-0.05270567,0.02609037,0.05444734,-0.04504986,-0.05079415,0.02109186,-0.02187569,0.06707203,0.05561281,-0.08831093,0.07802906,-0.00228435,-0.11308684,-0.02509846,0.05300082,0.01627958,-0.12251096,-0.0764512,0.03718633,0.00711624,-0.02764335,-0.00284916,0.00372201,-0.08301489,0.01102549,0.10081602,-0.03610111,-0.07078537,-0.0401499,-0.01720736,0.02132714,-0.00642371,0.00850475,-0.02615103,-0.01247472,-0.0128463,-0.0631741,-0.00795426,-0.0726084,0.02407301,-0.01104319,0.02922148,0.02169324,0.0207517,0.02758389,0.05312348,-0.00418972,-0.02330311,-0.03295529,-0.0726921,0.01451609,0.01460051,-0.03037617,0.01516761,-0.0207433,-0.05053755,0.01448937,-0.01025112,-0.04635524,0.03610444,-0.03223751,-0.00920225,-0.03175059,0.08189335,0.02402098,-0.05063784,0.0259693,-0.00445665,-0.00961913,0.02082651,0.09258921,-0.00788846,-0.00492427,-0.0300853,-0.19593877,-0.0447703,0.02942027,0.01977867,0.02657808,-0.12854235,0.08988047,0.01122629,-0.0052661,0.04494106,0.07726283,0.00267392,0.06040263,0.05819816,0.0162859,0.05155315,-0.01228721,0.03316233,-0.05344095,0.02464354,-0.01203364,0.03091557,-0.02695873,-0.10201646,-0.02537744,0.00877497,0.18361746,0.02371663,0.023089,0.01204326,-0.01138446,0.07761988,-0.01563232,-0.19349875,-0.03303356,0.04659111,0.03343869,0.0440319,-0.01544535,-0.06637295,-0.07554916,0.09560167,-0.0202772,-0.11668082,-0.00613834,-0.03332292,-0.00961981,-0.01872192,-0.02260118,0.03339776,0.04717174,0.06367689,-0.03183508,0.04830823,-0.03579756,-0.05502103,-0.0167191,-0.02349186,-0.03233745,0.01154629,-0.01918124,-0.02610423,-0.01374071,-0.07552756,-0.00089957,0.03223592,0.02702016,0.00954528,0.08581262,-0.05015919,-0.01619561,0.09340288,-0.00731531,0.08972623,-0.00091741,-0.06737388,0.02160289,0.03373212,-0.02299455,0.032359,0.00654572,-0.04142528,0.05091787,0.00236854,-0.03554958,0.02733152,-0.01634678,0.01159629,0.04163218,0.01663043,-0.03791898,0.06173022,-0.02370433,-0.04365421,0.08447404,0.02985846,-0.24057953,0.05063301,0.04401277,-0.07459293,-0.0259608,0.00411451,-0.03212741,-0.02885014,-0.0078802,-0.02459357,0.03964346,0.00793428,-0.05225542,-0.06391603,0.00202128,0.07097176,0.02953255,-0.02227118,-0.03088315,-0.04971705,-0.01588224,0.05056205,0.17718706,-0.01068935,-0.01317902,0.01492027,-0.04673871,0.01188469,0.05348063,-0.04245569,-0.05102158,0.02626596,0.05340553,-0.03382155,0.01798888,0.03184868,-0.02411896,0.0454447,0.01552931,0.0025654,0.03879258,0.02782445,0.03304486,0.01594673,0.05671181,-0.00789336,-0.03894054,0.03705509,-0.07693017,0.04153386,-0.0364906,0.01218674,-0.04071683,-0.01339826,0.06070157,0.00889143,-0.00182429,0.00826551,-0.02749523,-0.02486252,-0.016844,-0.04731496,0.06634404,0.06761184,-0.01358316],"last_embed":{"hash":"81fb0e38ef106d34c20b7348acd2277cbe1ca803ef55d8d5750343a36a8864ac","tokens":512}}},"last_read":{"hash":"81fb0e38ef106d34c20b7348acd2277cbe1ca803ef55d8d5750343a36a8864ac","at":1745995208315},"class_name":"SmartSource2","outlinks":[{"title":"codemirror-extension-inline-suggestion","target":"https://github.com/saminzadeh/codemirror-extension-inline-suggestion","line":7},{"title":"codemirror-companion-extension","target":"https://www.npmjs.com/package/codemirror-companion-extension","line":7},{"title":"demo","target":"https://raw.githubusercontent.com/rizerphe/obsidian-companion/main/screenshots/demo.gif","line":11},{"title":"Obsidian Community Plugin Directory","target":"https://obsidian.md/plugins?id=companion","line":15},{"title":"Companion","target":"https://obsidian.md/plugins?id=companion","line":17},{"title":"OpenAI API Keys","target":"https://platform.openai.com/account/api-keys","line":26},{"title":"plugin's GitHub repository","target":"https://github.com/rizerphe/obsidian-companion","line":48},{"title":"codemirror-companion-extension","target":"https://www.npmjs.com/package/codemirror-companion-extension","line":88}],"blocks":{"#Obsidian Companion":[1,8],"#Obsidian Companion#{1}":[3,8],"#Demo":[9,12],"#Demo#{1}":[11,12],"#Installation":[13,21],"#Installation#{1}":[15,16],"#Installation#{2}":[17,17],"#Installation#{3}":[18,18],"#Installation#{4}":[19,19],"#Installation#{5}":[20,21],"#How to Use":[22,58],"#How to Use#{1}":[24,25],"#How to Use#{2}":[26,26],"#How to Use#{3}":[27,27],"#How to Use#{4}":[28,28],"#How to Use#{5}":[29,29],"#How to Use#{6}":[30,30],"#How to Use#{7}":[31,31],"#How to Use#{8}":[32,32],"#How to Use#{9}":[33,34],"#How to Use#{10}":[35,36],"#How to Use#How to Use (Mobile)":[37,49],"#How to Use#How to Use (Mobile)#{1}":[39,40],"#How to Use#How to Use (Mobile)#{2}":[41,41],"#How to Use#How to Use (Mobile)#{3}":[42,42],"#How to Use#How to Use (Mobile)#{4}":[43,43],"#How to Use#How to Use (Mobile)#{5}":[44,44],"#How to Use#How to Use (Mobile)#{6}":[45,45],"#How to Use#How to Use (Mobile)#{7}":[46,47],"#How to Use#How to Use (Mobile)#{8}":[48,49],"#How to Use#Groq":[50,53],"#How to Use#Groq#{1}":[52,53],"#How to Use#Ollama":[54,58],"#How to Use#Ollama#{1}":[56,58],"#Presets":[59,71],"#Presets#{1}":[61,63],"#Presets#{2}":[64,64],"#Presets#{3}":[65,65],"#Presets#{4}":[66,66],"#Presets#{5}":[67,67],"#Presets#{6}":[68,69],"#Presets#{7}":[70,71],"#Completion providers":[72,90],"#Completion providers#{1}":[74,75],"#Completion providers#{2}":[76,76],"#Completion providers#{3}":[77,77],"#Completion providers#{4}":[78,78],"#Completion providers#{5}":[79,80],"#Completion providers#{6}":[81,90],"#---frontmatter---":[86,null]},"last_import":{"mtime":1741249381088,"size":7619,"at":1741911372548,"hash":"81fb0e38ef106d34c20b7348acd2277cbe1ca803ef55d8d5750343a36a8864ac"},"key":"Obsidian/Obsidian Companion.md"},