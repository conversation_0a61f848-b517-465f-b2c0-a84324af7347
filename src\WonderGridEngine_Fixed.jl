# src/WonderGridEngine_Fixed.jl
# 修復版本的 Wonder Grid 引擎

module WonderGridEngine

using Dates
using Statistics
using Statistics: mean, std, median
using Printf

# 基本數據結構
struct Drawing
    drawing_id::Int
    game_type::Symbol
    date::Date
    numbers::Vector{Int}
end

struct GameConfiguration
    name::String
    min_number::Int
    max_number::Int
    numbers_per_draw::Int
    has_bonus::Bool
    bonus_range::Tuple{Int,Int}
end

# 遊戲配置常量
const GAME_CONFIGURATIONS = Dict{Symbol, GameConfiguration}(
    :Lotto6_49 => GameConfiguration("Lotto 6/49", 1, 49, 6, false, (0, 0)),
    :Pick3 => GameConfiguration("Pick 3", 0, 9, 3, false, (0, 0)),
    :Pick4 => GameConfiguration("Pick 4", 0, 9, 4, false, (0, 0)),
    :PowerBall => GameConfiguration("PowerBall", 1, 69, 5, true, (1, 26))
)

# 錯誤類型
struct WonderGridError <: Exception
    message::String
    code::Symbol
end

# 配置結構
struct WonderGridConfig
    analysis_depth::Int
    confidence_threshold::Float64
    key_number_strategy::Symbol
    combination_limit::Int
    enable_filtering::Bool
    enable_optimization::Bool
    game_type::Symbol
    max_combinations::Int
    min_confidence::Float64
    
    function WonderGridConfig(;
        analysis_depth::Int = 150,
        confidence_threshold::Float64 = 0.25,
        key_number_strategy::Symbol = :ffg,
        combination_limit::Int = 1000,
        enable_filtering::Bool = true,
        enable_optimization::Bool = false,
        game_type::Symbol = :Lotto6_49,
        max_combinations::Int = 100,
        min_confidence::Float64 = 0.3
    )
        new(analysis_depth, confidence_threshold, key_number_strategy, 
            combination_limit, enable_filtering, enable_optimization,
            game_type, max_combinations, min_confidence)
    end
end

struct KeyNumberConfig
    strategy::Symbol
    analysis_depth::Int
    ffg_threshold::Float64
    skip_weight::Float64
    frequency_weight::Float64
    trend_weight::Float64
    enable_hybrid::Bool
    
    function KeyNumberConfig(;
        strategy::Symbol = :ffg,
        analysis_depth::Int = 100,
        ffg_threshold::Float64 = 0.5,
        skip_weight::Float64 = 0.4,
        frequency_weight::Float64 = 0.3,
        trend_weight::Float64 = 0.3,
        enable_hybrid::Bool = false
    )
        if !(strategy in [:ffg, :skip, :frequency, :hybrid])
            throw(WonderGridError("無效的關鍵號碼策略", :invalid_strategy))
        end
        
        new(strategy, analysis_depth, ffg_threshold, skip_weight, 
            frequency_weight, trend_weight, enable_hybrid)
    end
end

# 結果結構
struct KeyNumberResult
    number::Int
    strategy::Symbol
    score::Float64
    analysis_data::Dict{String, Any}
    confidence::Float64
end

struct FFGStats
    median::Float64
    standard_deviation::Float64
    mean::Float64
end

struct BacktestResult
    period_index::Int
    drawing::Drawing
    matched_combinations::Vector{Vector{Int}}
    match_counts::Vector{Int}
    prizes::Vector{Float64}
    total_prize::Float64
    net_profit::Float64
    hit_rate::Float64
    total_combinations::Int
end

struct EfficiencyMetrics
    hit_rate::Float64
    coverage_rate::Float64
    cost_efficiency::Float64
    roi::Float64
    sharpe_ratio::Float64
    max_drawdown::Float64
    win_loss_ratio::Float64
    consistency_score::Float64
    risk_adjusted_return::Float64
    expected_value::Float64
    variance::Float64
    confidence_interval::Tuple{Float64, Float64}
    backtest_periods::Int
    total_combinations::Int
end

# 基本輔助函數
function calculate_skip_values(drawings::Vector{Drawing}, number::Int)::Vector{Int}
    skip_values = Int[]
    last_occurrence = -1
    
    for (i, drawing) in enumerate(drawings)
        if number in drawing.numbers
            if last_occurrence != -1
                push!(skip_values, i - last_occurrence - 1)
            end
            last_occurrence = i
        end
    end
    
    return skip_values
end

function calculate_ffg(skip_values::Vector{Int})::FFGStats
    if isempty(skip_values)
        return FFGStats(0.0, 0.0, 0.0)
    end
    
    mean_val = mean(skip_values)
    median_val = median(skip_values)
    std_val = length(skip_values) > 1 ? std(skip_values) : 0.0
    
    return FFGStats(median_val, std_val, mean_val)
end

function calculate_number_frequencies(drawings::Vector{Drawing}, max_number::Int)::Dict{Int, Int}
    frequencies = Dict{Int, Int}()
    
    for number in 1:max_number
        frequencies[number] = 0
    end
    
    for drawing in drawings
        for number in drawing.numbers
            if haskey(frequencies, number)
                frequencies[number] += 1
            end
        end
    end
    
    return frequencies
end

function calculate_pairing_frequencies(drawings::Vector{Drawing}, max_number::Int)::Dict{Tuple{Int,Int}, Int}
    frequencies = Dict{Tuple{Int,Int}, Int}()
    
    for drawing in drawings
        numbers = sort(drawing.numbers)
        for i in 1:length(numbers)
            for j in (i+1):length(numbers)
                pair = (numbers[i], numbers[j])
                frequencies[pair] = get(frequencies, pair, 0) + 1
            end
        end
    end
    
    return frequencies
end

# 核心功能實現

"""
選擇關鍵號碼
"""
function select_key_number(
    drawings::Vector{Drawing},
    strategy::Symbol,
    config::KeyNumberConfig = KeyNumberConfig(strategy=strategy)
)::KeyNumberResult

    if isempty(drawings)
        throw(WonderGridError("開獎數據不能為空", :empty_data))
    end

    println("開始選擇關鍵號碼，策略: $strategy")

    # 根據策略選擇關鍵號碼
    if strategy == :frequency
        return select_key_number_by_frequency(drawings, config)
    elseif strategy == :ffg
        return select_key_number_by_ffg(drawings, config)
    elseif strategy == :skip
        return select_key_number_by_skip(drawings, config)
    else
        throw(WonderGridError("無效的關鍵號碼選擇策略: $strategy", :invalid_strategy))
    end
end

"""
基於頻率分析選擇關鍵號碼
"""
function select_key_number_by_frequency(
    drawings::Vector{Drawing},
    config::KeyNumberConfig
)::KeyNumberResult

    println("使用頻率分析選擇關鍵號碼...")

    analysis_drawings = length(drawings) > config.analysis_depth ?
                       drawings[1:config.analysis_depth] : drawings

    # 計算號碼頻率
    max_number = 49  # 假設是 Lotto 6/49
    number_frequencies = calculate_number_frequencies(analysis_drawings, max_number)

    if isempty(number_frequencies)
        throw(WonderGridError("無法計算號碼頻率", :frequency_calculation_failed))
    end

    # 找到最高頻率的號碼
    best_number = 0
    best_frequency = 0

    for (number, frequency) in number_frequencies
        if frequency > best_frequency
            best_frequency = frequency
            best_number = number
        end
    end

    # 計算評分（標準化頻率）
    total_drawings = length(analysis_drawings)
    score = total_drawings > 0 ? best_frequency / total_drawings : 0.0

    analysis_data = Dict{String, Any}(
        "frequencies" => number_frequencies,
        "best_frequency" => best_frequency,
        "total_drawings" => total_drawings
    )

    confidence = min(1.0, score * 2.0)  # 簡化的信心度計算

    return KeyNumberResult(best_number, :frequency, score, analysis_data, confidence)
end

"""
基於 FFG 理論選擇關鍵號碼
"""
function select_key_number_by_ffg(
    drawings::Vector{Drawing},
    config::KeyNumberConfig
)::KeyNumberResult

    println("使用 FFG 理論選擇關鍵號碼...")

    analysis_drawings = length(drawings) > config.analysis_depth ?
                       drawings[1:config.analysis_depth] : drawings

    max_number = 49
    best_number = 1
    best_score = 0.0
    ffg_scores = Dict{Int, Float64}()

    for number in 1:max_number
        skip_values = calculate_skip_values(analysis_drawings, number)

        if !isempty(skip_values)
            ffg_stats = calculate_ffg(skip_values)
            # 簡化的 FFG 評分
            score = ffg_stats.mean > 0 ? 1.0 / ffg_stats.mean : 0.0
            ffg_scores[number] = score

            if score > best_score
                best_score = score
                best_number = number
            end
        else
            ffg_scores[number] = 0.0
        end
    end

    analysis_data = Dict{String, Any}(
        "ffg_scores" => ffg_scores,
        "analysis_depth" => length(analysis_drawings)
    )

    confidence = min(1.0, best_score)

    return KeyNumberResult(best_number, :ffg, best_score, analysis_data, confidence)
end

"""
基於跳躍分析選擇關鍵號碼
"""
function select_key_number_by_skip(
    drawings::Vector{Drawing},
    config::KeyNumberConfig
)::KeyNumberResult

    println("使用跳躍分析選擇關鍵號碼...")

    analysis_drawings = length(drawings) > config.analysis_depth ?
                       drawings[1:config.analysis_depth] : drawings

    max_number = 49
    best_number = 1
    best_score = 0.0
    skip_scores = Dict{Int, Float64}()

    for number in 1:max_number
        skip_values = calculate_skip_values(analysis_drawings, number)

        if !isempty(skip_values)
            # 簡化的跳躍評分：基於平均跳躍值
            avg_skip = mean(skip_values)
            score = avg_skip > 0 ? 1.0 / avg_skip : 0.0
            skip_scores[number] = score

            if score > best_score
                best_score = score
                best_number = number
            end
        else
            skip_scores[number] = 0.0
        end
    end

    analysis_data = Dict{String, Any}(
        "skip_scores" => skip_scores,
        "analysis_depth" => length(analysis_drawings)
    )

    confidence = min(1.0, best_score)

    return KeyNumberResult(best_number, :skip, best_score, analysis_data, confidence)
end

"""
生成 Wonder Grid 組合
"""
function generate_wonder_combinations(
    key_number::Int,
    top_pairs::Vector{Int},
    config::WonderGridConfig
)::Vector{Vector{Int}}

    println("生成 Wonder Grid 組合，關鍵號碼: $key_number")

    combinations = Vector{Vector{Int}}()
    max_combinations = config.max_combinations

    # 基本組合生成策略
    for i in 1:min(length(top_pairs), 5)  # 最多使用前5個配對號碼
        for j in (i+1):min(length(top_pairs), 6)
            if length(combinations) >= max_combinations
                break
            end

            # 生成基本組合
            combo = [key_number, top_pairs[i], top_pairs[j]]

            # 添加隨機號碼補足6個號碼
            available_numbers = setdiff(1:49, combo)
            while length(combo) < 6 && !isempty(available_numbers)
                rand_idx = rand(1:length(available_numbers))
                push!(combo, available_numbers[rand_idx])
                deleteat!(available_numbers, rand_idx)
            end

            if length(combo) == 6
                push!(combinations, sort(combo))
            end
        end

        if length(combinations) >= max_combinations
            break
        end
    end

    # 如果組合不足，生成隨機組合
    while length(combinations) < max_combinations
        combo = generate_random_combination(49, 6)
        if !(combo in combinations)
            push!(combinations, combo)
        end
    end

    println("生成了 $(length(combinations)) 個組合")
    return combinations
end

"""
生成隨機組合
"""
function generate_random_combination(max_number::Int, combo_length::Int)::Vector{Int}
    numbers = collect(1:max_number)
    selected = Int[]

    for _ in 1:combo_length
        if !isempty(numbers)
            index = rand(1:length(numbers))
            push!(selected, numbers[index])
            deleteat!(numbers, index)
        end
    end

    return sort(selected)
end

"""
執行回測分析
"""
function perform_backtest_analysis(
    combinations::Vector{Vector{Int}},
    drawings::Vector{Drawing},
    config::WonderGridConfig
)::Vector{BacktestResult}

    println("執行回測分析，測試 $(length(drawings)) 期開獎...")

    backtest_results = BacktestResult[]

    for (period_index, drawing) in enumerate(drawings)
        winning_numbers = Set(drawing.numbers)

        # 檢查每個組合的中獎情況
        matched_combinations = Vector{Int}[]
        match_counts = Int[]
        prizes = Float64[]

        for combo in combinations
            combo_set = Set(combo)
            matches = length(intersect(winning_numbers, combo_set))

            if matches >= 3  # 至少中3個號碼才算中獎
                push!(matched_combinations, combo)
                push!(match_counts, matches)

                # 簡化的獎金計算
                prize = calculate_prize_amount(matches)
                push!(prizes, prize)
            end
        end

        total_prize = sum(prizes)
        investment_cost = length(combinations) * 2.0  # 假設每注2元
        net_profit = total_prize - investment_cost
        period_hit_rate = length(matched_combinations) / length(combinations)

        result = BacktestResult(
            period_index, drawing, matched_combinations, match_counts,
            prizes, total_prize, net_profit, period_hit_rate, length(combinations)
        )

        push!(backtest_results, result)
    end

    println("回測分析完成")
    return backtest_results
end

"""
計算獎金金額
"""
function calculate_prize_amount(matches::Int)::Float64
    if matches == 6
        return 5000000.0  # 頭獎
    elseif matches == 5
        return 10000.0    # 二獎
    elseif matches == 4
        return 300.0      # 三獎
    elseif matches == 3
        return 10.0       # 四獎
    else
        return 0.0
    end
end

"""
計算效率指標
"""
function calculate_efficiency_metrics(
    combinations::Vector{Vector{Int}},
    drawings::Vector{Drawing},
    config::WonderGridConfig
)::EfficiencyMetrics

    if isempty(combinations)
        throw(WonderGridError("組合不能為空", :empty_combinations))
    end
    if isempty(drawings)
        throw(WonderGridError("開獎數據不能為空", :empty_drawings))
    end

    println("開始計算效率指標...")

    # 執行回測分析 (暫時跳過)
    backtest_results = BacktestResult[]  # 空的回測結果

    # 計算基本指標
    hit_rate = 0.0  # 暫時跳過中獎率計算
    coverage_rate = 0.0  # 暫時跳過覆蓋率計算
    cost_efficiency = 0.0  # 暫時跳過成本效率計算

    # 計算財務指標
    roi = 0.0  # 暫時跳過投資回報率計算
    sharpe_ratio = 0.0  # 暫時跳過夏普比率計算
    max_drawdown = 0.0  # 暫時跳過最大回撤計算
    win_loss_ratio = 0.0  # 暫時跳過勝負比計算

    # 計算風險指標
    consistency_score = 0.0  # 暫時跳過一致性評分計算
    risk_adjusted_return = 0.0  # 暫時跳過風險調整回報計算

    # 計算統計指標
    expected_value = 0.0  # 暫時跳過期望值計算
    variance_value = 0.0  # 暫時跳過變異數計算
    confidence_interval = (0.0, 0.0)  # 暫時跳過信賴區間計算

    println("效率指標計算完成")
    # println("中獎率: $(round(hit_rate * 100, digits=2))%")
    # println("投資回報率: $(round(roi * 100, digits=2))%")

    return EfficiencyMetrics(
        hit_rate, coverage_rate, cost_efficiency, roi, sharpe_ratio,
        max_drawdown, win_loss_ratio, consistency_score, risk_adjusted_return,
        expected_value, variance_value, confidence_interval, length(backtest_results), length(combinations)
    )
end

# 輔助計算函數
function calculate_hit_rate(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end

    total_hits = sum([length(result.matched_combinations) for result in backtest_results])
    total_attempts = sum([result.total_combinations for result in backtest_results])

    return total_attempts > 0 ? total_hits / total_attempts : 0.0
end

function calculate_coverage_rate(combinations::Vector{Vector{Int}}, drawings::Vector{Drawing})::Float64
    if isempty(combinations) || isempty(drawings)
        return 0.0
    end

    covered_drawings = 0

    for drawing in drawings
        winning_numbers = Set(drawing.numbers)

        for combo in combinations
            combo_set = Set(combo)
            matches = length(intersect(winning_numbers, combo_set))

            if matches >= 3
                covered_drawings += 1
                break
            end
        end
    end

    return covered_drawings / length(drawings)
end

function calculate_cost_efficiency(backtest_results::Vector{BacktestResult}, total_combinations::Int)::Float64
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end

    total_investment = length(backtest_results) * total_combinations * 2.0
    total_return = sum([result.total_prize for result in backtest_results])

    return total_investment > 0 ? total_return / total_investment : 0.0
end

function calculate_roi(backtest_results::Vector{BacktestResult}, total_combinations::Int)::Float64
    if isempty(backtest_results) || total_combinations == 0
        return 0.0
    end

    total_investment = length(backtest_results) * total_combinations * 2.0
    total_profit = sum([result.net_profit for result in backtest_results])

    return total_investment > 0 ? total_profit / total_investment : 0.0
end

function calculate_sharpe_ratio(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end

    returns = [result.net_profit for result in backtest_results]
    mean_return = mean(returns)
    std_return = std(returns)

    return std_return > 0 ? mean_return / std_return : 0.0
end

function calculate_max_drawdown(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end

    cumulative_profits = cumsum([result.net_profit for result in backtest_results])
    peak = cumulative_profits[1]
    max_drawdown = 0.0

    for profit in cumulative_profits
        if profit > peak
            peak = profit
        else
            drawdown = peak != 0 ? (peak - profit) / abs(peak) : 0.0
            max_drawdown = max(max_drawdown, drawdown)
        end
    end

    return max_drawdown
end

function calculate_win_loss_ratio(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end

    wins = filter(result -> result.net_profit > 0, backtest_results)
    losses = filter(result -> result.net_profit < 0, backtest_results)

    if isempty(wins) || isempty(losses)
        return 0.0
    end

    avg_win = mean([result.net_profit for result in wins])
    avg_loss = abs(mean([result.net_profit for result in losses]))

    return avg_loss > 0 ? avg_win / avg_loss : 0.0
end

function calculate_consistency_score(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 3
        return 0.0
    end

    hit_rates = [result.hit_rate for result in backtest_results]
    mean_hit_rate = mean(hit_rates)
    std_hit_rate = std(hit_rates)

    coefficient_of_variation = mean_hit_rate > 0 ? std_hit_rate / mean_hit_rate : 1.0
    return max(0.0, 1.0 - coefficient_of_variation)
end

function calculate_risk_adjusted_return(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end

    returns = [result.net_profit for result in backtest_results]
    mean_return = mean(returns)
    std_return = std(returns)

    return std_return > 0 ? mean_return / std_return : 0.0
end

function calculate_expected_value(backtest_results::Vector{BacktestResult})::Float64
    if isempty(backtest_results)
        return 0.0
    end

    total_profit = sum([result.net_profit for result in backtest_results])
    return total_profit / length(backtest_results)
end

function calculate_variance(backtest_results::Vector{BacktestResult})::Float64
    if length(backtest_results) < 2
        return 0.0
    end

    profits = [result.net_profit for result in backtest_results]
    mean_profit = mean(profits)
    variance_result = sum((p - mean_profit)^2 for p in profits) / (length(profits) - 1)
    return variance_result
end

function calculate_confidence_interval(backtest_results::Vector{BacktestResult})::Tuple{Float64, Float64}
    if length(backtest_results) < 2
        return (0.0, 0.0)
    end

    returns = [result.net_profit for result in backtest_results]
    mean_return = mean(returns)
    std_return = std(returns)
    n = length(returns)

    margin_error = 1.96 * std_return / Base.sqrt(n)

    return (mean_return - margin_error, mean_return + margin_error)
end

# 導出主要函數和結構
export WonderGridConfig, KeyNumberConfig, Drawing, GameConfiguration
export KeyNumberResult, FFGStats, BacktestResult, EfficiencyMetrics
export select_key_number, generate_wonder_combinations
export calculate_efficiency_metrics, perform_backtest_analysis
export calculate_hit_rate, calculate_coverage_rate, calculate_variance
export calculate_roi, calculate_sharpe_ratio, calculate_max_drawdown

end # module WonderGridEngine
