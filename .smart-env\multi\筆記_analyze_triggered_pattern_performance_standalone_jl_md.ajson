
"smart_sources:筆記/analyze_triggered_pattern_performance_standalone.jl.md": {"path":"筆記/analyze_triggered_pattern_performance_standalone.jl.md","last_embed":{"hash":null},"embeddings":{},"last_read":{"hash":"v3h63l","at":1751936235334},"class_name":"SmartSource","last_import":{"mtime":1751935211425,"size":47392,"at":1751936235355,"hash":"v3h63l"},"blocks":{"#include(\"src/analyze_triggered_pattern_performance_standalone.jl\")":[1,2],"#載入共享模組和必要的套件":[3,5],"#載入共享模組和必要的套件#{1}":[4,5],"#--- 套件載入 ---":[6,12],"#--- 套件載入 ---#{1}":[7,12],"#Lottery Pattern Analysis and Prediction Tool":[13,116],"#Lottery Pattern Analysis and Prediction Tool#1. 總覽 (Overview)":[15,20],"#Lottery Pattern Analysis and Prediction Tool#1. 總覽 (Overview)#{1}":[17,20],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)":[21,31],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{1}":[23,23],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{2}":[24,24],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{3}":[25,25],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{4}":[26,26],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{5}":[27,27],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{6}":[28,28],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{7}":[29,29],"#Lottery Pattern Analysis and Prediction Tool#2. 核心功能 (Key Features)#{8}":[30,31],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)":[32,77],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.1. 環境準備 (Prerequisites)":[34,42],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.1. 環境準備 (Prerequisites)#{1}":[36,36],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.1. 環境準備 (Prerequisites)#{2}":[37,42],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.1. 環境準備 (Prerequisites)#{3}":[38,42],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.2. 執行步驟 (Execution Steps)":[43,77],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.2. 執行步驟 (Execution Steps)#{1}":[45,48],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.2. 執行步驟 (Execution Steps)#{2}":[49,70],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.2. 執行步驟 (Execution Steps)#{3}":[55,70],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.2. 執行步驟 (Execution Steps)#{4}":[71,77],"#Lottery Pattern Analysis and Prediction Tool#3. 如何使用 (How to Use)#3.2. 執行步驟 (Execution Steps)#{5}":[73,77],"#Lottery Pattern Analysis and Prediction Tool#4. 工作流程說明 (Workflow Explained)":[78,91],"#Lottery Pattern Analysis and Prediction Tool#4. 工作流程說明 (Workflow Explained)#{1}":[80,81],"#Lottery Pattern Analysis and Prediction Tool#4. 工作流程說明 (Workflow Explained)#{2}":[82,85],"#Lottery Pattern Analysis and Prediction Tool#4. 工作流程說明 (Workflow Explained)#{3}":[86,91],"#Lottery Pattern Analysis and Prediction Tool#5. 輸出檔案說明 (Output Files)":[92,116],"#Lottery Pattern Analysis and Prediction Tool#5. 輸出檔案說明 (Output Files)#{1}":[94,95],"#Lottery Pattern Analysis and Prediction Tool#5. 輸出檔案說明 (Output Files)#{2}":[96,96],"#Lottery Pattern Analysis and Prediction Tool#5. 輸出檔案說明 (Output Files)#{3}":[97,97],"#Lottery Pattern Analysis and Prediction Tool#5. 輸出檔案說明 (Output Files)#{4}":[98,99],"#Lottery Pattern Analysis and Prediction Tool#5. 輸出檔案說明 (Output Files)#{5}":[100,116],"#數據分析範圍":[117,120],"#數據分析範圍#{1}":[118,120],"#模式回溯期數":[121,124],"#模式回溯期數#{1}":[122,124],"#當模式觸發時，向後觀察的未來期數 (通常為 1)":[125,127],"#當模式觸發時，向後觀察的未來期數 (通常為 1)#{1}":[126,127],"#高表現模式過濾條件":[128,133],"#高表現模式過濾條件#{1}":[129,133],"#查找 Gx 組合的目標期號 (用於單次執行)":[134,136],"#查找 Gx 組合的目標期號 (用於單次執行)#{1}":[135,136],"#結果儲存設定":[137,140],"#結果儲存設定#{1}":[138,140],"#A0031 特有參數":[141,164],"#A0031 特有參數#{1}":[142,164],"#準備要寫入的資料列":[165,181],"#準備要寫入的資料列#{1}":[166,181],"#將開獎號碼轉換為標準的 JSON 陣列字串":[182,185],"#將開獎號碼轉換為標準的 JSON 陣列字串#{1}":[183,185],"#檢查檔案是否存在以決定是否寫入表頭":[186,188],"#檢查檔案是否存在以決定是否寫入表頭#{1}":[187,188],"#將資料列轉換為 DataFrame":[189,191],"#將資料列轉換為 DataFrame#{1}":[190,191],"#寫入或附加到 CSV 檔案":[192,202],"#寫入或附加到 CSV 檔案#{1}":[193,202],"#= --- 以下為舊的 Markdown 日誌記錄功能，已停用 ---":[203,242],"#= --- 以下為舊的 Markdown 日誌記錄功能，已停用 ---#{1}":[204,242],"#--- 結果 ---":[243,255],"#--- 結果 ---#{1}":[244,255],"#--- 分析 ---":[256,265],"#--- 分析 ---#{1}":[257,265],"#--- 未來應用建議 ---":[266,311],"#--- 未來應用建議 ---#{1}":[267,275],"#--- 未來應用建議 ---#{2}":[276,276],"#--- 未來應用建議 ---#{3}":[277,277],"#--- 未來應用建議 ---#{4}":[278,311],"#--- 結束 LoggingModule ---":[312,318],"#--- 結束 LoggingModule ---#{1}":[314,318],"#export analyze_batch_performance, generate_summary_report, plot_comparison_curves":[319,325],"#export analyze_batch_performance, generate_summary_report, plot_comparison_curves#{1}":[321,325],"#Arguments":[326,332],"#Arguments#{1}":[327,327],"#Arguments#{2}":[328,328],"#Arguments#{3}":[329,329],"#Arguments#{4}":[330,330],"#Arguments#{5}":[331,332],"#1. 載入數據":[333,336],"#1. 載入數據#{1}":[334,336],"#2. 篩選數據":[337,343],"#2. 篩選數據#{1}":[338,343],"#為了穩健比較，不過濾 nothing":[344,355],"#為了穩健比較，不過濾 nothing#{1}":[345,355],"#為了計算累計指標，按評估期號排序":[356,358],"#為了計算累計指標，按評估期號排序#{1}":[357,358],"#3. 計算關鍵績效指標 (KPIs)":[359,368],"#3. 計算關鍵績效指標 (KPIs)#{1}":[360,368],"#計算最大回撤 (Max Drawdown)":[369,374],"#計算最大回撤 (Max Drawdown)#{1}":[370,374],"#4. 繪製圖表 (如果啟用)":[375,377],"#4. 繪製圖表 (如果啟用)#{1}":[376,377],"#建立一個對檔案名安全的參數字串":[378,382],"#建立一個對檔案名安全的參數字串#{1}":[379,382],"#建立圖表":[383,399],"#建立圖表#{1}":[384,399],"#5. 顯示結果":[400,470],"#5. 顯示結果#{1}":[401,470],"#--- 顯示在終端機 ---":[471,482],"#--- 顯示在終端機 ---#{1}":[472,482],"#--- 寫入檔案 ---":[483,536],"#--- 寫入檔案 ---#{1}":[484,536],"#--- 結束 BatchAnalysisModule ---":[537,538],"#--- 主執行流程 ---":[539,542],"#載入外部定義的手動模式":[543,546],"#載入外部定義的手動模式#{1}":[544,546],"#export run_backtest, run_analysis, get_default_config, generate_summary_report, plot_comparison_curves":[547,549],"#export run_backtest, run_analysis, get_default_config, generate_summary_report, plot_comparison_curves#{1}":[549,549],"#--- 1. 資料準備 ---":[550,555],"#--- 1. 資料準備 ---#{1}":[551,555],"#--- 2. 歷史表現分析 ---":[556,560],"#--- 2. 歷史表現分析 ---#{1}":[557,560],"#--- 3. 篩選高表現模式 ---":[561,577],"#--- 3. 篩選高表現模式 ---#{1}":[562,577],"#--- 4. 根據模式查找當期 Gx ---":[578,583],"#--- 4. 根據模式查找當期 Gx ---#{1}":[579,583],"#基於統計篩選的模式 (修正: 使用傳入的 config 中的 start_period)":[584,591],"#基於統計篩選的模式 (修正: 使用傳入的 config 中的 start_period)#{1}":[585,591],"#--- 5. 結果儲存與評估 ---":[592,605],"#--- 5. 結果儲存與評估 ---#{1}":[593,605],"#計算所有未來期數的總中獎號碼":[606,623],"#計算所有未來期數的總中獎號碼#{1}":[607,623],"#--- 自動記錄日誌 ---":[624,625],"#--- 自動記錄日誌 ---#{1}":[625,625],"#建立一個臨時的 config 來記錄，包含當前的 target_period":[626,628],"#建立一個臨時的 config 來記錄，包含當前的 target_period#{1}":[627,628],"#準備傳遞給日誌函式的結果":[629,638],"#準備傳遞給日誌函式的結果#{1}":[630,638],"#新的 CSV 日誌記錄":[639,658],"#新的 CSV 日誌記錄#{1}":[640,658],"#1. 執行一次耗時的分析，並將結果儲存起來":[659,662],"#1. 執行一次耗時的分析，並將結果儲存起來#{1}":[660,662],"#3. 迴圈調用評估函式，這一步會非常快":[663,699],"#3. 迴圈調用評估函式，這一步會非常快#{1}":[664,699],"#1. 根據目標模式自動設定 config":[700,704],"#1. 根據目標模式自動設定 config#{1}":[701,704],"#暫時禁用過濾，以獲取所有統計數據":[705,710],"#暫時禁用過濾，以獲取所有統計數據#{1}":[706,710],"#2. 執行歷史分析以獲取所有統計數據":[711,711],"#perform_historical_analysis 會返回一個包含 all_stats 的 NamedTuple":[712,716],"#perform_historical_analysis 會返回一個包含 all_stats 的 NamedTuple#{1}":[713,716],"#3. 篩選出包含目標 gx_wins_pattern 的所有統計數據":[717,720],"#3. 篩選出包含目標 gx_wins_pattern 的所有統計數據#{1}":[718,720],"#4. 顯示結果":[721,730],"#4. 顯示結果#{1}":[722,730],"#為了更好的可讀性，對結果進行排序":[731,755],"#為了更好的可讀性，對結果進行排序#{1}":[732,755],"#Arguments[2]":[756,761],"#Arguments[2]#{1}":[757,761],"#Returns":[762,813],"#Returns#{1}":[763,813],"#Arguments[3]":[814,819],"#Arguments[3]#{1}":[815,819],"#Returns[2]":[820,870],"#Returns[2]#{1}":[821,870],"#--- 為了保持腳本可獨立執行，定義 main 函式 ---":[871,873],"#--- 為了保持腳本可獨立執行，定義 main 函式 ---#{1}":[872,873],"#--- 使用範例 1: 執行標準回測與分析 ---":[874,874],"#說明：這會執行一個完整的端到端流程，適合評估一組參數在多個期數上的表現。":[875,884],"#說明：這會執行一個完整的端到端流程，適合評估一組參數在多個期數上的表現。#{1}":[876,884],"#--- 使用範例 2: 查詢特定 gx_wins_pattern 的 hit_rate ---":[885,885],"#說明：這會針對您提供的特定模式，找出所有相關的歷史統計數據。":[886,891],"#說明：這會針對您提供的特定模式，找出所有相關的歷史統計數據。#{1}":[887,891],"#--- 使用範例 3: 計算特定 gx_wins_pattern 的總出球率 ---":[892,892],"#說明：這個函數獨立計算，不考慮 cum 指標。它直接計算一個 gx_wins 模式出現後，":[893,893],"#在所有情況下，下一期的平均出球率。":[894,902],"#在所有情況下，下一期的平均出球率。#{1}":[895,902],"#[0,0,0,1,0,0,1,0,1]":[903,906],"#[0,0,0,1,0,0,1,0,1]#{1}":[904,906],"#--- 使用範例 4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合 ---":[907,907],"#說明: 這個函數會找出在某一個時間點，哪些 Gx 組合的歷史中獎序列正好形成了您指定的模式。":[908,917],"#說明: 這個函數會找出在某一個時間點，哪些 Gx 組合的歷史中獎序列正好形成了您指定的模式。#{1}":[909,917],"#[0,0,0,1,0,0,1,0,1][2]":[918,931],"#[0,0,0,1,0,0,1,0,1][2]#{1}":[919,931],"#如果此腳本是主程序，則執行 main()":[932,932],"#if abspath(PROGRAM_FILE) == @__FILE__":[933,933],"#您可以透過修改 use_example 的值來切換不同的執行功能":[934,934],"#1: 執行標準回測與分析":[935,935],"#2: 查詢特定 gx_wins_pattern 的 hit_rate":[936,936],"#3: 計算特定 gx_wins_pattern 的總出球率":[937,937],"#4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合":[938,939],"#4: 在特定期號查找匹配特定 gx_wins 模式的 Gx 組合#{1}":[939,939],"#end":[940,1023],"#end#{1}":[942,1023],"#TODO: 改進清單":[1024,1025],"#TODO: 改進清單#{1}":[1025,1025],"#改進清單":[1026,1060],"#改進清單#效能優化":[1028,1033],"#改進清單#效能優化#{1}":[1029,1029],"#改進清單#效能優化#{2}":[1030,1030],"#改進清單#效能優化#{3}":[1031,1031],"#改進清單#效能優化#{4}":[1032,1033],"#改進清單#功能擴展":[1034,1039],"#改進清單#功能擴展#{1}":[1035,1035],"#改進清單#功能擴展#{2}":[1036,1036],"#改進清單#功能擴展#{3}":[1037,1037],"#改進清單#功能擴展#{4}":[1038,1039],"#改進清單#代碼重構":[1040,1045],"#改進清單#代碼重構#{1}":[1041,1041],"#改進清單#代碼重構#{2}":[1042,1042],"#改進清單#代碼重構#{3}":[1043,1043],"#改進清單#代碼重構#{4}":[1044,1045],"#改進清單#新功能":[1046,1052],"#改進清單#新功能#{1}":[1047,1047],"#改進清單#新功能#{2}":[1048,1048],"#改進清單#新功能#{3}":[1049,1049],"#改進清單#新功能#{4}":[1050,1050],"#改進清單#新功能#{5}":[1051,1052],"#改進清單#用戶體驗":[1053,1060],"#改進清單#用戶體驗#{1}":[1054,1054],"#改進清單#用戶體驗#{2}":[1055,1055],"#改進清單#用戶體驗#{3}":[1056,1056],"#改進清單#用戶體驗#{4}":[1057,1057],"#改進清單#用戶體驗#{5}":[1058,1060],"#to-do list":[1061,1061],"#- [x] 如何修改 `analyze_triggered_pattern_performance_standalone.jl` 腳本，讓它在每次評估後自動將結果寫入這個日誌文件？":[1062,1062],"#- [x] 如何修改日誌記錄功能，讓「結論與備註」可以根據利潤率自動生成初步的文字？":[1063,1063],"#- [x] 請幫我修改 `evaluate_for_target_period` 函式，使其能夠根據 `CONFIG.n_future_draws` 的值來正確評估多期的獲利。":[1064,1064],"#- [ ] 請幫我修改 `log_evaluation_to_markdown` 函式，讓它在日誌中能更清晰地展示多期評估的細節，例如每期的開獎號碼和對應的獲利情況。":[1065,1065],"#- [x] 在 `CONFIG` 中，`gx_wins_lookback` 和 `pattern_lookback` 這些參數的命名有點不直觀，可以幫我重構一下，讓它們的意義更清晰嗎？同時也請更新所有使用到它們的地方。":[1066,1066],"#- [ ] 增加一個功能，將多個最佳參數組合生成的 Gx 建議進行合併和去重，並根據其在不同最佳參數集中的出現頻率或加權得分進行排序，提供一個最終的、精簡的投資列表。":[1067,1067],"#階段四項目1撰寫自動化執行器 (Runner Script)":[1068,1069]},"outlinks":[]},