#!/usr/bin/env julia

# 調試測試
println("開始調試測試...")

try
    using Dates
    include("src/WonderGridEngine.jl")
    using .WonderGridEngine
    
    println("✓ 模組載入成功")
    
    # 創建更豐富的測試數據（與完整測試相同）
    test_drawings = [
        WonderGridEngine.Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 5, 12, 23, 34, 45]),
        WonderGridEngine.Drawing(2, :Lotto6_49, Date(2024, 1, 2), [2, 8, 15, 28, 35, 46]),
        WonderGridEngine.Drawing(3, :Lotto6_49, Date(2024, 1, 3), [3, 9, 16, 29, 36, 47]),
        WonderGridEngine.Drawing(4, :Lotto6_49, Date(2024, 1, 4), [4, 10, 17, 30, 37, 48]),
        WonderGridEngine.Drawing(5, :Lotto6_49, Date(2024, 1, 5), [1, 11, 18, 31, 38, 49]),
        WonderGridEngine.Drawing(6, :<PERSON><PERSON>6_49, Date(2024, 1, 6), [5, 12, 19, 32, 39, 40]),
        WonderGridEngine.Drawing(7, :Lotto6_49, Date(2024, 1, 7), [6, 13, 20, 33, 41, 42]),
        <PERSON>GridEngine.Drawing(8, :Lotto6_49, Date(2024, 1, 8), [7, 14, 21, 34, 43, 44]),
        WonderGridEngine.Drawing(9, :Lotto6_49, Date(2024, 1, 9), [8, 15, 22, 35, 45, 46]),
        WonderGridEngine.Drawing(10, :Lotto6_49, Date(2024, 1, 10), [1, 9, 16, 23, 36, 47])
    ]
    
    println("✓ 測試數據創建成功 ($(length(test_drawings)) 期)")
    
    # 測試配置創建
    config = WonderGridEngine.WonderGridConfig(
        analysis_range = 50,
        top_pair_percentage = 0.25,
        key_number_strategy = :ffg,
        combination_limit = 100,
        enable_purge = true,
        enable_lie = false,
        game_type = :Lotto6_49
    )
    println("✓ 配置創建成功")
    
    # 步驟 1: 計算配對統計
    println("步驟 1: 計算配對統計...")
    pair_statistics = WonderGridEngine.calculate_advanced_pair_statistics(test_drawings, config)
    println("✓ 配對統計計算成功，找到 $(length(pair_statistics)) 個配對")
    
    # 步驟 2: 選擇關鍵號碼
    println("步驟 2: 選擇關鍵號碼...")
    key_result = WonderGridEngine.select_key_number(test_drawings, config.key_number_strategy)
    key_number = key_result.number
    println("✓ 關鍵號碼選擇成功: $key_number")
    
    # 步驟 3: 篩選頂級配對
    println("步驟 3: 篩選頂級配對...")
    println("調用 select_top_pairs_advanced 函數...")
    println("參數類型:")
    println("  pair_statistics: $(typeof(pair_statistics))")
    println("  key_number: $(typeof(key_number))")
    println("  config: $(typeof(config))")
    
    top_pairs = WonderGridEngine.select_top_pairs_advanced(pair_statistics, key_number, config)
    println("✓ 頂級配對選擇成功，選出 $(length(top_pairs)) 個配對")
    
    println("✓ 所有步驟測試通過")
    
catch e
    println("✗ 測試失敗: $e")
    println("錯誤詳情:")
    showerror(stdout, e, catch_backtrace())
    println()
end

println("調試測試完成")
