# Saliu Lottery System 代碼庫描述

## 系統概述

這是一個基於 Ion Saliu 數學理論的彩票分析系統，使用 Julia 語言開發。系統實現了完整的彩票過濾器理論、馬可夫鏈分析、跳躍系統和 Wonder Grid 策略。

## 核心架構

### 主要目錄結構
```
├── src/                    # 核心源代碼
├── notes/saliu/           # Ion Saliu 理論文檔
├── docs/                  # 系統文檔
├── 筆記/                  # 開發筆記
└── .smart-env/           # 智能環境配置
```

## 核心功能模組

### 1. 數據管理系統
- **DATA* 檔案**：真實開獎歷史數據
- **SIM* 檔案**：隨機模擬數據（非字典順序）
- **D* 檔案**：合併的大型數據檔案（需 1200 萬行以上）

### 2. 過濾器引擎
基於 Ion Saliu 的動態過濾器理論：
- **ONE 到 SIX 過濾器**：數學過濾器系統
- **Ion5 過濾器**：需要大型 D6 數據檔案
- **Del6 過濾器**：分析相鄰數字差異
- **Past Draws 過濾器**：歷史開獎分析

### 3. 跳躍系統 (Skip System)
- **FFG 理論**：賭博基本公式應用
- **跳躍分析**：號碼間隔統計
- **中位數計算**：基於 FFG 的機率分析
- **號碼池篩選**：動態號碼選擇

### 4. Wonder Grid 策略
- **配對頻率分析**：號碼配對統計
- **BEST6 檔案**：最常見配對
- **WORST6 檔案**：最不常見配對
- **7x7 矩陣**：完美方形樂透矩陣

### 5. LIE 消除技術
- **反向策略**：排除低概率組合
- **謊言消除**：故意設定不中獎過濾器
- **組合縮減**：大幅減少投注數量

## 軟體工具整合

### 核心軟體組件
- **Bright6**：主要樂透分析軟體
- **Super Utilities**：高速數據處理工具
- **SkipSystem**：跳躍系統專用軟體
- **FileLines**：跨策略交叉引用工具
- **MDIEditor Lotto WE**：圖形化分析介面

### 報告生成系統
- **W* 報告**：中獎分析報告
- **MD* 報告**：中位數報告
- **GR* 報告**：組別統計報告
- **FR* 報告**：頻率分析報告
- **SK* 報告**：跳躍間隔報告

## 技術特色

### 1. 大數據處理
- 支援高達 2GB 檔案處理
- 32/64 位元 Windows 相容
- 極高處理速度（相比 16 位元 DOS）

### 2. 動態過濾
- 適應號碼變化行為
- 顯著提高中獎機率
- 自動異常值檢測

### 3. 跨平台策略
- 命令提示字元 (LotWon)
- 圖形化介面 (MDIEditor Lotto WE)
- 統一策略檔案格式

## 數學理論基礎

### FFG (賭博基本公式)
- 機率計算核心
- 中位數和標準差分析
- 統計學原理應用

### 馬可夫鏈分析
- 號碼跟隨關係
- 配對頻率統計
- 轉移機率計算

### 組合數學
- 字典順序算法
- 組合排列生成
- CSN (組合序號) 計算

## 系統優勢

1. **科學基礎**：基於嚴格數學理論
2. **實證效果**：全球數千玩家驗證
3. **高效過濾**：大幅減少組合數量
4. **動態適應**：自動調整策略參數
5. **跨策略整合**：多種方法組合應用

## 開發狀態

系統包含完整的需求文件、設計文件和任務列表，涵蓋：
- 8 個主要需求
- 7 個核心引擎組件
- 13 個任務分組
- 39 個具體實施任務

這個代碼庫代表了彩票分析領域最先進的數學方法實現，為用戶提供基於科學原理的投注策略制定工具。