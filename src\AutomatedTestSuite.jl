# src/AutomatedTestSuite.jl

module AutomatedTestSuite

using Dates
using Test
using Random
using Statistics
using ..WonderGridEngine: Drawing, WonderGridConfig, execute_wonder_grid_strategy

export TestSuite, TestCase, TestResult, TestRunner,
       create_test_suite, add_test_case, run_test_suite,
       generate_test_report, run_regression_tests,
       create_unit_tests, create_integration_tests, create_e2e_tests

# 測試案例
struct TestCase
    name::String
    description::String
    test_function::Function
    category::Symbol  # :unit, :integration, :e2e, :performance
    priority::Symbol  # :low, :medium, :high, :critical
    timeout_seconds::Float64
    setup_function::Union{Function, Nothing}
    teardown_function::Union{Function, Nothing}
    
    function TestCase(name, description, test_function, category=:unit, priority=:medium, 
                     timeout_seconds=30.0, setup_function=nothing, teardown_function=nothing)
        new(name, description, test_function, category, priority, timeout_seconds, setup_function, teardown_function)
    end
end

# 測試結果
struct TestResult
    test_case::TestCase
    status::Symbol  # :passed, :failed, :error, :timeout, :skipped
    execution_time::Float64
    error_message::String
    stack_trace::String
    timestamp::DateTime
    
    function TestResult(test_case, status, execution_time, error_message="", stack_trace="")
        new(test_case, status, execution_time, error_message, stack_trace, now())
    end
end

# 測試套件
mutable struct TestSuite
    name::String
    description::String
    test_cases::Vector{TestCase}
    setup_function::Union{Function, Nothing}
    teardown_function::Union{Function, Nothing}
    parallel_execution::Bool
    
    function TestSuite(name, description="", setup_function=nothing, teardown_function=nothing, parallel_execution=false)
        new(name, description, TestCase[], setup_function, teardown_function, parallel_execution)
    end
end

# 測試運行器
mutable struct TestRunner
    suites::Vector{TestSuite}
    results::Vector{TestResult}
    config::Dict{String, Any}
    
    function TestRunner()
        config = Dict{String, Any}(
            "verbose" => true,
            "stop_on_failure" => false,
            "generate_report" => true,
            "report_format" => :html
        )
        new(TestSuite[], TestResult[], config)
    end
end

"""
創建測試套件
"""
function create_test_suite(name::String, description::String="")::TestSuite
    suite = TestSuite(name, description)
    
    println("📋 創建測試套件: $name")
    if !isempty(description)
        println("  描述: $description")
    end
    
    return suite
end

"""
添加測試案例
"""
function add_test_case(suite::TestSuite, test_case::TestCase)
    push!(suite.test_cases, test_case)
    println("  ➕ 添加測試案例: $(test_case.name) [$(test_case.category)]")
end

"""
運行測試套件
"""
function run_test_suite(runner::TestRunner, suite::TestSuite)::Vector{TestResult}
    println("\n🚀 運行測試套件: $(suite.name)")
    println("  測試案例數量: $(length(suite.test_cases))")
    
    suite_results = Vector{TestResult}()
    
    # 執行套件設置
    if suite.setup_function !== nothing
        try
            suite.setup_function()
            println("✓ 套件設置完成")
        catch e
            println("❌ 套件設置失敗: $e")
            return suite_results
        end
    end
    
    # 按優先級排序測試案例
    sorted_cases = sort(suite.test_cases, by = tc -> priority_order(tc.priority))
    
    # 運行測試案例
    for (i, test_case) in enumerate(sorted_cases)
        println("  [$i/$(length(sorted_cases))] 運行: $(test_case.name)")
        
        result = run_single_test(test_case)
        push!(suite_results, result)
        push!(runner.results, result)
        
        # 顯示結果
        status_symbol = get_status_symbol(result.status)
        println("    $status_symbol $(result.status) ($(round(result.execution_time, digits=3))s)")
        
        if result.status == :failed && !isempty(result.error_message)
            println("      錯誤: $(result.error_message)")
        end
        
        # 檢查是否需要在失敗時停止
        if runner.config["stop_on_failure"] && result.status in [:failed, :error]
            println("  ⏹️  因失敗而停止測試")
            break
        end
    end
    
    # 執行套件清理
    if suite.teardown_function !== nothing
        try
            suite.teardown_function()
            println("✓ 套件清理完成")
        catch e
            println("⚠️  套件清理失敗: $e")
        end
    end
    
    # 顯示套件摘要
    display_suite_summary(suite_results)
    
    return suite_results
end

"""
運行單個測試
"""
function run_single_test(test_case::TestCase)::TestResult
    start_time = time()
    
    try
        # 執行測試設置
        if test_case.setup_function !== nothing
            test_case.setup_function()
        end
        
        # 執行測試（帶超時）
        test_result = nothing
        execution_time = @elapsed begin
            test_result = test_case.test_function()
        end
        
        # 檢查超時
        if execution_time > test_case.timeout_seconds
            return TestResult(test_case, :timeout, execution_time, "測試超時")
        end
        
        # 執行測試清理
        if test_case.teardown_function !== nothing
            test_case.teardown_function()
        end
        
        return TestResult(test_case, :passed, execution_time)
        
    catch e
        execution_time = time() - start_time
        
        if isa(e, Test.TestSetException)
            # 測試失敗
            return TestResult(test_case, :failed, execution_time, string(e), "")
        else
            # 測試錯誤
            error_msg = string(e)
            stack_trace = sprint(showerror, e, catch_backtrace())
            return TestResult(test_case, :error, execution_time, error_msg, stack_trace)
        end
    end
end

"""
獲取優先級順序
"""
function priority_order(priority::Symbol)::Int
    priority_map = Dict(:critical => 1, :high => 2, :medium => 3, :low => 4)
    return get(priority_map, priority, 5)
end

"""
獲取狀態符號
"""
function get_status_symbol(status::Symbol)::String
    status_symbols = Dict(
        :passed => "✅",
        :failed => "❌", 
        :error => "💥",
        :timeout => "⏰",
        :skipped => "⏭️"
    )
    return get(status_symbols, status, "❓")
end

"""
顯示套件摘要
"""
function display_suite_summary(results::Vector{TestResult})
    total = length(results)
    passed = count(r -> r.status == :passed, results)
    failed = count(r -> r.status == :failed, results)
    errors = count(r -> r.status == :error, results)
    timeouts = count(r -> r.status == :timeout, results)
    
    println("\n📊 測試摘要:")
    println("  總計: $total")
    println("  通過: $passed ✅")
    println("  失敗: $failed ❌")
    println("  錯誤: $errors 💥")
    println("  超時: $timeouts ⏰")
    
    if total > 0
        success_rate = (passed / total) * 100
        println("  成功率: $(round(success_rate, digits=1))%")
    end
    
    if !isempty(results)
        total_time = sum(r.execution_time for r in results)
        avg_time = total_time / length(results)
        println("  總時間: $(round(total_time, digits=2))s")
        println("  平均時間: $(round(avg_time, digits=3))s")
    end
end

"""
創建單元測試
"""
function create_unit_tests()::TestSuite
    suite = create_test_suite("單元測試", "測試個別函數和組件")
    
    # 測試 Drawing 結構
    add_test_case(suite, TestCase(
        "test_drawing_creation",
        "測試 Drawing 結構的創建",
        () -> begin
            @testset "Drawing Creation" begin
                drawing = Drawing(1, :Lotto6_49, Date(2024, 1, 1), [1, 2, 3, 4, 5, 6])
                @test drawing.id == 1
                @test drawing.game_type == :Lotto6_49
                @test length(drawing.numbers) == 6
                @test all(n -> 1 <= n <= 49, drawing.numbers)
            end
        end,
        :unit,
        :high
    ))
    
    # 測試 WonderGridConfig 結構
    add_test_case(suite, TestCase(
        "test_config_creation",
        "測試 WonderGridConfig 結構的創建",
        () -> begin
            @testset "Config Creation" begin
                config = WonderGridConfig()
                @test config.analysis_range > 0
                @test config.top_pair_percentage > 0.0
                @test config.combination_limit > 0
                @test config.game_type in [:Lotto6_49, :PowerBall, :MegaMillions]
            end
        end,
        :unit,
        :high
    ))
    
    # 測試關鍵號碼選擇
    add_test_case(suite, TestCase(
        "test_key_number_selection",
        "測試關鍵號碼選擇功能",
        () -> begin
            @testset "Key Number Selection" begin
                drawings = create_test_drawings(10)
                
                for strategy in [:ffg, :skip, :frequency]
                    result = WonderGridEngine.select_key_number(drawings, strategy)
                    @test 1 <= result.number <= 49
                    @test result.score >= 0.0
                    @test 0.0 <= result.confidence <= 1.0
                end
            end
        end,
        :unit,
        :critical
    ))
    
    return suite
end

"""
創建集成測試
"""
function create_integration_tests()::TestSuite
    suite = create_test_suite("集成測試", "測試組件間的交互")
    
    # 測試完整策略執行
    add_test_case(suite, TestCase(
        "test_strategy_execution",
        "測試完整的 Wonder Grid 策略執行",
        () -> begin
            @testset "Strategy Execution" begin
                drawings = create_test_drawings(15)
                config = WonderGridConfig(combination_limit = 10)
                
                result = execute_wonder_grid_strategy(drawings, config)
                
                @test 1 <= result.key_number <= 49
                @test !isempty(result.top_pairs)
                @test !isempty(result.combinations)
                @test result.generation_time > 0.0
                @test !isempty(result.efficiency_metrics)
            end
        end,
        :integration,
        :critical,
        60.0  # 較長的超時時間
    ))
    
    return suite
end

"""
創建端到端測試
"""
function create_e2e_tests()::TestSuite
    suite = create_test_suite("端到端測試", "測試完整的用戶場景")
    
    # 測試多種配置的策略執行
    add_test_case(suite, TestCase(
        "test_multiple_configurations",
        "測試多種配置下的策略執行",
        () -> begin
            @testset "Multiple Configurations" begin
                drawings = create_test_drawings(20)
                
                configs = [
                    WonderGridConfig(key_number_strategy = :ffg, combination_limit = 5),
                    WonderGridConfig(key_number_strategy = :skip, combination_limit = 8),
                    WonderGridConfig(key_number_strategy = :frequency, combination_limit = 12)
                ]
                
                for (i, config) in enumerate(configs)
                    result = execute_wonder_grid_strategy(drawings, config)
                    @test !isempty(result.combinations)
                    @test length(result.combinations) <= config.combination_limit
                end
            end
        end,
        :e2e,
        :high,
        120.0  # 更長的超時時間
    ))
    
    return suite
end

"""
創建測試數據
"""
function create_test_drawings(count::Int)::Vector{Drawing}
    drawings = Vector{Drawing}()
    
    for i in 1:count
        # 生成隨機但有效的開獎號碼
        numbers = sort(sample(1:49, 6, replace=false))
        drawing = Drawing(count - i + 1, :Lotto6_49, Date(2024, 1, 1) + Day(i-1), numbers)
        push!(drawings, drawing)
    end
    
    return drawings
end

"""
簡單的無重複隨機抽樣
"""
function sample(collection, n::Int; replace::Bool=true)
    if replace
        return [rand(collection) for _ in 1:n]
    else
        if n > length(collection)
            throw(ArgumentError("樣本大小不能超過集合大小"))
        end
        shuffled = shuffle(collect(collection))
        return shuffled[1:n]
    end
end

"""
生成測試報告
"""
function generate_test_report(runner::TestRunner, output_file::String="test_report.html")
    println("📋 生成測試報告...")

    if runner.config["report_format"] == :html
        generate_html_report(runner, output_file)
    elseif runner.config["report_format"] == :json
        generate_json_report(runner, replace(output_file, ".html" => ".json"))
    else
        generate_text_report(runner, replace(output_file, ".html" => ".txt"))
    end

    println("✓ 測試報告已生成: $output_file")
end

"""
生成HTML測試報告
"""
function generate_html_report(runner::TestRunner, filename::String)
    open(filename, "w") do io
        println(io, "<!DOCTYPE html>")
        println(io, "<html><head><title>自動化測試報告</title>")
        println(io, "<style>")
        println(io, "body { font-family: Arial, sans-serif; margin: 20px; }")
        println(io, "table { border-collapse: collapse; width: 100%; margin: 10px 0; }")
        println(io, "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }")
        println(io, "th { background-color: #f2f2f2; }")
        println(io, ".passed { background-color: #d4edda; }")
        println(io, ".failed { background-color: #f8d7da; }")
        println(io, ".error { background-color: #f8d7da; }")
        println(io, ".timeout { background-color: #fff3cd; }")
        println(io, ".summary { background-color: #e9ecef; padding: 15px; margin: 10px 0; }")
        println(io, "</style></head><body>")

        println(io, "<h1>自動化測試報告</h1>")
        println(io, "<p><strong>生成時間:</strong> $(now())</p>")

        # 測試摘要
        write_test_summary(io, runner.results)

        # 按類別分組的測試結果
        write_test_results_by_category(io, runner.results)

        # 失敗測試詳情
        write_failed_tests_details(io, runner.results)

        println(io, "</body></html>")
    end
end

"""
寫入測試摘要
"""
function write_test_summary(io::IO, results::Vector{TestResult})
    total = length(results)
    passed = count(r -> r.status == :passed, results)
    failed = count(r -> r.status == :failed, results)
    errors = count(r -> r.status == :error, results)
    timeouts = count(r -> r.status == :timeout, results)

    println(io, "<div class=\"summary\">")
    println(io, "<h2>測試摘要</h2>")
    println(io, "<p><strong>總測試數:</strong> $total</p>")
    println(io, "<p><strong>通過:</strong> $passed ✅</p>")
    println(io, "<p><strong>失敗:</strong> $failed ❌</p>")
    println(io, "<p><strong>錯誤:</strong> $errors 💥</p>")
    println(io, "<p><strong>超時:</strong> $timeouts ⏰</p>")

    if total > 0
        success_rate = (passed / total) * 100
        println(io, "<p><strong>成功率:</strong> $(round(success_rate, digits=1))%</p>")

        total_time = sum(r.execution_time for r in results)
        println(io, "<p><strong>總執行時間:</strong> $(round(total_time, digits=2)) 秒</p>")
    end
    println(io, "</div>")
end

"""
按類別寫入測試結果
"""
function write_test_results_by_category(io::IO, results::Vector{TestResult})
    categories = unique([r.test_case.category for r in results])

    for category in categories
        category_results = filter(r -> r.test_case.category == category, results)

        println(io, "<h2>$(uppercase(string(category))) 測試</h2>")
        println(io, "<table>")
        println(io, "<tr><th>測試名稱</th><th>狀態</th><th>執行時間(s)</th><th>優先級</th></tr>")

        for result in category_results
            css_class = string(result.status)
            println(io, "<tr class=\"$css_class\">")
            println(io, "<td>$(result.test_case.name)</td>")
            println(io, "<td>$(result.status)</td>")
            println(io, "<td>$(round(result.execution_time, digits=3))</td>")
            println(io, "<td>$(result.test_case.priority)</td>")
            println(io, "</tr>")
        end

        println(io, "</table>")
    end
end

"""
寫入失敗測試詳情
"""
function write_failed_tests_details(io::IO, results::Vector{TestResult})
    failed_results = filter(r -> r.status in [:failed, :error], results)

    if !isempty(failed_results)
        println(io, "<h2>失敗測試詳情</h2>")

        for result in failed_results
            println(io, "<div style=\"border: 1px solid #ccc; margin: 10px 0; padding: 10px;\">")
            println(io, "<h3>$(result.test_case.name)</h3>")
            println(io, "<p><strong>狀態:</strong> $(result.status)</p>")
            println(io, "<p><strong>錯誤信息:</strong></p>")
            println(io, "<pre>$(result.error_message)</pre>")

            if !isempty(result.stack_trace)
                println(io, "<p><strong>堆疊追蹤:</strong></p>")
                println(io, "<pre>$(result.stack_trace)</pre>")
            end
            println(io, "</div>")
        end
    end
end

"""
運行回歸測試
"""
function run_regression_tests(runner::TestRunner, baseline_results::Vector{TestResult})::Dict{String, Any}
    println("🔄 運行回歸測試...")

    regression_report = Dict{String, Any}(
        "passed" => true,
        "new_failures" => Vector{String}(),
        "fixed_tests" => Vector{String}(),
        "performance_regressions" => Vector{Dict{String, Any}}(),
        "summary" => Dict{String, Any}()
    )

    # 比較測試結果
    for current_result in runner.results
        # 找到對應的基準結果
        baseline_result = findfirst(r -> r.test_case.name == current_result.test_case.name, baseline_results)

        if baseline_result === nothing
            continue  # 新測試，跳過
        end

        baseline = baseline_results[baseline_result]

        # 檢查新失敗
        if baseline.status == :passed && current_result.status in [:failed, :error]
            regression_report["passed"] = false
            push!(regression_report["new_failures"], current_result.test_case.name)
        end

        # 檢查修復的測試
        if baseline.status in [:failed, :error] && current_result.status == :passed
            push!(regression_report["fixed_tests"], current_result.test_case.name)
        end

        # 檢查性能回歸（執行時間增加超過50%）
        if baseline.status == :passed && current_result.status == :passed
            time_increase = (current_result.execution_time - baseline.execution_time) / baseline.execution_time
            if time_increase > 0.5  # 50%的性能回歸閾值
                push!(regression_report["performance_regressions"], Dict{String, Any}(
                    "test_name" => current_result.test_case.name,
                    "baseline_time" => baseline.execution_time,
                    "current_time" => current_result.execution_time,
                    "increase_percent" => time_increase * 100
                ))
            end
        end
    end

    # 生成摘要
    regression_report["summary"] = Dict{String, Any}(
        "total_tests" => length(runner.results),
        "new_failures_count" => length(regression_report["new_failures"]),
        "fixed_tests_count" => length(regression_report["fixed_tests"]),
        "performance_regressions_count" => length(regression_report["performance_regressions"])
    )

    # 顯示回歸測試結果
    display_regression_summary(regression_report)

    return regression_report
end

"""
顯示回歸測試摘要
"""
function display_regression_summary(report::Dict{String, Any})
    println("\n📊 回歸測試摘要:")

    if report["passed"]
        println("  ✅ 回歸測試通過")
    else
        println("  ❌ 檢測到回歸問題")
    end

    summary = report["summary"]
    println("  總測試數: $(summary["total_tests"])")
    println("  新失敗: $(summary["new_failures_count"])")
    println("  已修復: $(summary["fixed_tests_count"])")
    println("  性能回歸: $(summary["performance_regressions_count"])")

    # 顯示新失敗的測試
    if !isempty(report["new_failures"])
        println("\n  新失敗的測試:")
        for test_name in report["new_failures"]
            println("    - $test_name")
        end
    end

    # 顯示性能回歸
    if !isempty(report["performance_regressions"])
        println("\n  性能回歸:")
        for regression in report["performance_regressions"]
            println("    - $(regression["test_name"]): +$(round(regression["increase_percent"], digits=1))%")
        end
    end
end

"""
運行所有測試套件
"""
function run_all_tests()::TestRunner
    println("🎯 運行所有自動化測試...")

    runner = TestRunner()

    # 創建並運行單元測試
    unit_suite = create_unit_tests()
    run_test_suite(runner, unit_suite)

    # 創建並運行集成測試
    integration_suite = create_integration_tests()
    run_test_suite(runner, integration_suite)

    # 創建並運行端到端測試
    e2e_suite = create_e2e_tests()
    run_test_suite(runner, e2e_suite)

    # 生成測試報告
    if runner.config["generate_report"]
        generate_test_report(runner)
    end

    return runner
end

end # module AutomatedTestSuite
