"smart_sources:PromptingTools.jl/Theme 1- [Theme Description] - PromptingTools.jl.md": {"path":"PromptingTools.jl/Theme 1- [Theme Description] - PromptingTools.jl.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10320042,0.0022797,-0.02058239,-0.04395205,-0.04885519,0.03568297,-0.05077169,0.02745002,0.05778652,-0.01099151,-0.01437087,0.02438995,0.00848576,0.05588437,-0.00267788,0.03282708,-0.02100372,0.04348493,-0.09485352,-0.00886766,0.10780283,0.05223041,0.01046549,-0.06146303,-0.0098364,0.02773723,0.0008297,-0.03952757,0.00791628,-0.18650497,0.05508884,-0.01181143,0.01930118,0.00926737,-0.03912701,0.02320404,-0.05222846,0.07388379,-0.03308724,0.0201941,0.03416735,0.00633237,-0.02715332,-0.02029197,-0.05825499,-0.11825198,-0.02017823,-0.03280271,-0.01095371,-0.05171442,0.02378924,-0.02063094,0.02704893,-0.01614752,0.02285942,0.00094001,-0.00554485,0.06658048,0.02256257,0.00359637,0.00899673,-0.00204088,-0.20086241,0.14375514,0.0149036,0.02980797,-0.01188987,0.00698089,0.01106368,0.06672999,-0.03424507,0.05865764,0.0178687,0.05871594,0.00784815,-0.02244034,-0.02711178,-0.01315222,0.00096272,-0.08777227,-0.04344282,-0.0301521,-0.04370616,-0.02888924,-0.02144897,-0.0000255,-0.02509694,-0.00195974,0.0400581,0.01132057,0.02073939,-0.09588818,0.02105144,0.0075434,-0.05916218,-0.02088082,0.00843607,-0.02215083,-0.09114593,0.12913549,-0.03343278,-0.01308149,0.05515462,-0.00790774,0.06706965,-0.05680842,0.0396417,-0.03810405,-0.01277235,0.0126373,-0.01379756,-0.04044512,-0.01422196,-0.08443867,0.00869139,-0.03014502,-0.03011283,0.00732719,0.03838984,-0.05072761,0.02917138,0.01113958,0.02222348,-0.02551941,-0.00321919,0.04764846,0.0484132,0.01278044,0.0114217,0.03733151,0.05341749,0.04337821,-0.07190609,-0.01243132,-0.02872196,0.05989742,0.05099664,-0.05201033,0.00442521,-0.01214188,0.01698319,0.00913503,-0.00372829,-0.11469052,0.0184621,0.03083456,-0.02093709,0.00758073,-0.00273391,-0.00610266,-0.0057272,0.05380554,-0.0504178,0.0197503,0.05396496,0.07389818,0.07423519,0.06335231,0.0140565,0.04026968,-0.03224028,-0.03517963,0.00046676,0.13004568,0.02448618,-0.07131596,-0.08533543,-0.01091971,-0.0472103,-0.05631194,0.01654309,0.02632977,-0.04799384,0.01396042,0.08535125,0.04519089,-0.05515906,0.02521987,-0.00637475,0.01953469,0.04970526,-0.04067475,-0.03113536,-0.00976684,-0.02938828,-0.05688135,0.02521598,-0.07317074,-0.00004333,0.03642685,-0.0572184,0.00913453,0.0533281,-0.01821496,-0.00568607,-0.00411368,-0.00301792,-0.09338463,0.05157412,-0.04034546,0.05963662,0.03340191,0.03488319,0.03921362,-0.00982507,0.0269234,0.05686073,0.0079486,0.08423787,0.01340314,-0.09937596,-0.05656992,0.05233741,0.01698473,-0.00477641,-0.04560849,-0.04461624,0.04113424,0.04841741,0.069265,-0.02496084,0.00873464,-0.06308059,-0.21946606,0.03132427,0.01835721,-0.07306521,0.04886778,-0.06134209,0.10075114,-0.04936867,-0.04308493,0.04772028,0.11676464,-0.05309222,0.05865664,-0.03605165,-0.01677469,-0.02249844,-0.02354106,0.01568063,0.02133548,0.00061643,0.02920886,-0.00340671,0.019428,-0.09764385,0.00191496,-0.03850571,0.11767963,0.07456385,0.03949007,-0.08235465,0.01640593,-0.02214614,0.02308231,-0.13020678,0.02713686,0.01327399,-0.0260024,0.0251766,0.05900019,0.01243651,-0.02886883,0.03545314,-0.02958943,-0.051299,-0.02629501,-0.03852099,-0.03638112,-0.03347396,-0.05180908,0.0309721,0.0280366,-0.07349864,0.01125072,0.01165259,0.01284568,-0.04482708,-0.04290185,-0.0536862,-0.00054419,0.02560207,0.00764517,-0.01426345,0.01468688,-0.0449781,0.03448473,0.04690039,0.00484364,0.01324076,0.07895551,-0.00439817,-0.01436786,0.10436144,0.03301655,0.04950331,0.01450244,0.04380948,0.01134825,-0.04367301,-0.01695832,-0.03055447,-0.04753453,0.04695231,0.01968821,0.00530346,0.01363819,0.04777685,0.01261285,-0.04675111,0.09480412,-0.05662181,-0.04139747,0.02484341,0.02392248,0.0056572,0.02785128,0.01404032,-0.25028142,0.03086789,-0.02392369,0.04097598,-0.01857421,0.07288248,0.04372994,-0.0030879,-0.03573379,0.04556151,-0.03401073,-0.01290127,0.00597407,-0.02653376,0.04516169,0.02427328,0.03964959,0.00253553,0.02123331,-0.07139879,0.02477824,0.03553031,0.2280167,-0.02074994,0.01818857,-0.034302,0.01425987,-0.08166969,0.0625966,0.04547071,0.00896803,0.06443064,0.15556307,0.00230601,0.03367696,0.01483894,-0.04491375,-0.00250787,0.02764465,0.01289764,-0.04464779,0.04107533,-0.03520897,-0.02020024,0.06400348,-0.02927212,0.00626609,-0.04967908,-0.05729405,0.03803502,0.00560652,0.0167854,-0.04322527,-0.01829153,0.0121654,-0.0002473,0.02874312,0.0333295,-0.03927665,-0.02053377,0.07395814,0.00554538,0.1002457,-0.01943091,-0.02826509],"last_embed":{"hash":"88e2f76779c86b73caf57b06d361ef605423f2ffc0be89b7e924b3438127b566","tokens":465}}},"last_read":{"hash":"88e2f76779c86b73caf57b06d361ef605423f2ffc0be89b7e924b3438127b566","at":1745995226222},"class_name":"SmartSource2","outlinks":[{"title":"Skip to content","target":"#VPContent","line":1},{"title":"![","target":"https://siml.earth/PromptingTools.jl/dev/prompts/persona-task/PromptingTools.jl/dev/logo.png","line":3},{"title":"Getting Started","target":"/PromptingTools.jl/dev/getting_started","line":7},{"title":"How It Works","target":"/PromptingTools.jl/dev/how_it_works","line":7},{"title":"Home","target":"/PromptingTools.jl/dev/index","line":7},{"title":"Various examples","target":"/PromptingTools.jl/dev/examples/readme_examples","line":11},{"title":"Using AITemplates","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":13},{"title":"Local models with Ollama.ai","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":15},{"title":"Google AIStudio","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":17},{"title":"Custom APIs (Mistral, Llama.cpp)","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":19},{"title":"Building RAG Application","target":"/PromptingTools.jl/dev/examples/building_RAG","line":21},{"title":"Text Utilities","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":25},{"title":"AgentTools","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":27},{"title":"RAGTools","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":29},{"title":"APITools","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":31},{"title":"F.A.Q.","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":33},{"title":"General","target":"/PromptingTools.jl/dev/prompts/general","line":37},{"title":"Persona-Task","target":"/PromptingTools.jl/dev/prompts/persona-task","line":39},{"title":"Visual","target":"/PromptingTools.jl/dev/prompts/visual","line":41},{"title":"Classification","target":"/PromptingTools.jl/dev/prompts/classification","line":43},{"title":"Extraction","target":"/PromptingTools.jl/dev/prompts/extraction","line":45},{"title":"Agents","target":"/PromptingTools.jl/dev/prompts/agents","line":47},{"title":"RAG","target":"/PromptingTools.jl/dev/prompts/RAG","line":49},{"title":"PromptingTools.jl","target":"/PromptingTools.jl/dev/reference","line":53},{"title":"Experimental Modules","target":"/PromptingTools.jl/dev/reference_experimental","line":55},{"title":"RAGTools","target":"/PromptingTools.jl/dev/reference_ragtools","line":57},{"title":"AgentTools","target":"/PromptingTools.jl/dev/reference_agenttools","line":59},{"title":"APITools","target":"/PromptingTools.jl/dev/reference_apitools","line":61},{"title":"\n\nHome\n\n","target":"/PromptingTools.jl/dev/index","line":75},{"title":"\n\nGetting Started\n\n","target":"/PromptingTools.jl/dev/getting_started","line":81},{"title":"\n\nHow It Works\n\n","target":"/PromptingTools.jl/dev/how_it_works","line":87},{"title":"\n\nVarious examples\n\n","target":"/PromptingTools.jl/dev/examples/readme_examples","line":95},{"title":"\n\nUsing AITemplates\n\n","target":"/PromptingTools.jl/dev/examples/working_with_aitemplates","line":101},{"title":"\n\nLocal models with Ollama.ai\n\n","target":"/PromptingTools.jl/dev/examples/working_with_ollama","line":107},{"title":"\n\nGoogle AIStudio\n\n","target":"/PromptingTools.jl/dev/examples/working_with_google_ai_studio","line":113},{"title":"\n\nCustom APIs (Mistral, Llama.cpp)\n\n","target":"/PromptingTools.jl/dev/examples/working_with_custom_apis","line":119},{"title":"\n\nBuilding RAG Application\n\n","target":"/PromptingTools.jl/dev/examples/building_RAG","line":125},{"title":"\n\nText Utilities\n\n","target":"/PromptingTools.jl/dev/extra_tools/text_utilities_intro","line":133},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/agent_tools_intro","line":139},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/extra_tools/rag_tools_intro","line":145},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/extra_tools/api_tools_intro","line":151},{"title":"\n\nF.A.Q.\n\n","target":"/PromptingTools.jl/dev/frequently_asked_questions","line":157},{"title":"\n\nGeneral\n\n","target":"/PromptingTools.jl/dev/prompts/general","line":165},{"title":"\n\nPersona-Task\n\n","target":"/PromptingTools.jl/dev/prompts/persona-task","line":171},{"title":"\n\nVisual\n\n","target":"/PromptingTools.jl/dev/prompts/visual","line":177},{"title":"\n\nClassification\n\n","target":"/PromptingTools.jl/dev/prompts/classification","line":183},{"title":"\n\nExtraction\n\n","target":"/PromptingTools.jl/dev/prompts/extraction","line":189},{"title":"\n\nAgents\n\n","target":"/PromptingTools.jl/dev/prompts/agents","line":195},{"title":"\n\nRAG\n\n","target":"/PromptingTools.jl/dev/prompts/RAG","line":201},{"title":"\n\nPromptingTools.jl\n\n","target":"/PromptingTools.jl/dev/reference","line":209},{"title":"\n\nExperimental Modules\n\n","target":"/PromptingTools.jl/dev/reference_experimental","line":215},{"title":"\n\nRAGTools\n\n","target":"/PromptingTools.jl/dev/reference_ragtools","line":221},{"title":"\n\nAgentTools\n\n","target":"/PromptingTools.jl/dev/reference_agenttools","line":227},{"title":"\n\nAPITools\n\n","target":"/PromptingTools.jl/dev/reference_apitools","line":233},{"title":"Persona-Task Templates","target":"#Persona-Task-Templates \"Persona-Task Templates\"","line":243},{"title":"Template: AnalystChaptersInTranscript","target":"#Template:-AnalystChaptersInTranscript \"Template: AnalystChaptersInTranscript\"","line":244},{"title":"Template: AnalystDecisionsInTranscript","target":"#Template:-AnalystDecisionsInTranscript \"Template: AnalystDecisionsInTranscript\"","line":245},{"title":"Template: AnalystThemesInResponses","target":"#Template:-AnalystThemesInResponses \"Template: AnalystThemesInResponses\"","line":246},{"title":"Template: AssistantAsk","target":"#Template:-AssistantAsk \"Template: AssistantAsk\"","line":247},{"title":"Template: DetailOrientedTask","target":"#Template:-DetailOrientedTask \"Template: DetailOrientedTask\"","line":248},{"title":"Template: DrafterEmailBrief","target":"#Template:-DrafterEmailBrief \"Template: DrafterEmailBrief\"","line":249},{"title":"Template: JuliaExpertAsk","target":"#Template:-JuliaExpertAsk \"Template: JuliaExpertAsk\"","line":250},{"title":"Template: JuliaExpertCoTTask","target":"#Template:-JuliaExpertCoTTask \"Template: JuliaExpertCoTTask\"","line":251},{"title":"Template: JuliaExpertTestCode","target":"#Template:-JuliaExpertTestCode \"Template: JuliaExpertTestCode\"","line":252},{"title":"Template: JuliaRecapCoTTask","target":"#Template:-JuliaRecapCoTTask \"Template: JuliaRecapCoTTask\"","line":253},{"title":"Template: JuliaRecapTask","target":"#Template:-JuliaRecapTask \"Template: JuliaRecapTask\"","line":254},{"title":"Template: StorytellerExplainSHAP","target":"#Template:-StorytellerExplainSHAP \"Template: StorytellerExplainSHAP\"","line":255},{"title":"Xml-Formatted Templates","target":"#Xml-Formatted-Templates \"Xml-Formatted Templates\"","line":256},{"title":"Template: JuliaExpertAskXML","target":"#Template:-JuliaExpertAskXML \"Template: JuliaExpertAskXML\"","line":257},{"title":"Template: JuliaExpertCoTTaskXML","target":"#Template:-JuliaExpertCoTTaskXML \"Template: JuliaExpertCoTTaskXML\"","line":258},{"title":"Template: JuliaExpertTestCodeXML","target":"#Template:-JuliaExpertTestCodeXML \"Template: JuliaExpertTestCodeXML\"","line":259},{"title":"​","target":"#Persona-Task-Templates","line":265},{"title":"​","target":"#Template:-AnalystChaptersInTranscript","line":267},{"title":"jxnl's Youtube Chapters prompt","target":"https://github.com/jxnl/youtubechapters-backend/blob/main/summary_app/md_summarize.py","line":275},{"title":"​","target":"#Template:-AnalystDecisionsInTranscript","line":337},{"title":"jxnl's Youtube Chapters prompt","target":"https://github.com/jxnl/youtubechapters-backend/blob/main/summary_app/md_summarize.py","line":345},{"title":"​","target":"#Template:-AnalystThemesInResponses","line":405},{"title":"​","target":"#theme-1-theme-description","line":438},{"title":"​","target":"#theme-2-theme-description","line":442},{"title":"​","target":"#Template:-AssistantAsk","line":472},{"title":"​","target":"#Template:-DetailOrientedTask","line":503},{"title":"​","target":"#Template:-DrafterEmailBrief","line":540},{"title":"​","target":"#Template:-JuliaExpertAsk","line":594},{"title":"​","target":"#Template:-JuliaExpertCoTTask","line":625},{"title":"​","target":"#Template:-JuliaExpertTestCode","line":671},{"title":"​","target":"#Template:-JuliaRecapCoTTask","line":752},{"title":"​","target":"#Template:-JuliaRecapTask","line":803},{"title":"​","target":"#Template:-StorytellerExplainSHAP","line":854},{"title":"Tell me a story!","target":"https://arxiv.org/abs/2309.17057","line":856},{"title":"​","target":"#Xml-Formatted-Templates","line":915},{"title":"​","target":"#Template:-JuliaExpertAskXML","line":917},{"title":"​","target":"#Template:-JuliaExpertCoTTaskXML","line":948},{"title":"​","target":"#Template:-JuliaExpertTestCodeXML","line":994},{"title":"Edit this page","target":"https://github.com/svilupp/PromptingTools.jl/edit/main/docs/src/prompts/persona-task.md","line":1075},{"title":"Previous pageGeneral","target":"/PromptingTools.jl/dev/prompts/general","line":1077},{"title":"Next pageVisual","target":"/PromptingTools.jl/dev/prompts/visual","line":1079},{"title":"**Documenter.jl**","target":"https://documenter.juliadocs.org/stable/","line":1081},{"title":"Icons8","target":"https://icons8.com","line":1081},{"title":"**VitePress**","target":"https://vitepress.dev","line":1081}],"blocks":{"#":[1,92],"##Examples":[93,130],"##Examples#{1}":[95,130],"##Extra Tools":[131,162],"##Extra Tools#{1}":[133,162],"##Prompt Templates":[163,206],"##Prompt Templates#{1}":[165,206],"##Reference":[207,264],"##Reference#{1}":[209,242],"##Reference#{2}":[243,255],"##Reference#{3}":[256,260],"##Reference#{4}":[261,264],"##Persona-Task Templates [​](#Persona-Task-Templates)":[265,289],"##Persona-Task Templates [​](#Persona-Task-Templates)#Template: AnalystChaptersInTranscript [​](#Template:-AnalystChaptersInTranscript)":[267,289],"##Persona-Task Templates [​](#Persona-Task-Templates)#Template: AnalystChaptersInTranscript [​](#Template:-AnalystChaptersInTranscript)#{1}":[269,270],"##Persona-Task Templates [​](#Persona-Task-Templates)#Template: AnalystChaptersInTranscript [​](#Template:-AnalystChaptersInTranscript)#{2}":[271,272],"##Persona-Task Templates [​](#Persona-Task-Templates)#Template: AnalystChaptersInTranscript [​](#Template:-AnalystChaptersInTranscript)#{3}":[273,274],"##Persona-Task Templates [​](#Persona-Task-Templates)#Template: AnalystChaptersInTranscript [​](#Template:-AnalystChaptersInTranscript)#{4}":[275,276],"##Persona-Task Templates [​](#Persona-Task-Templates)#Template: AnalystChaptersInTranscript [​](#Template:-AnalystChaptersInTranscript)#{5}":[277,279],"##Persona-Task Templates [​](#Persona-Task-Templates)#Template: AnalystChaptersInTranscript [​](#Template:-AnalystChaptersInTranscript)#{6}":[280,289],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]":[290,359],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#{1}":[292,293],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]":[294,359],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#{1}":[295,296],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#{2}":[297,298],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#{3}":[299,336],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#Template: AnalystDecisionsInTranscript [​](#Template:-AnalystDecisionsInTranscript)":[337,359],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#Template: AnalystDecisionsInTranscript [​](#Template:-AnalystDecisionsInTranscript)#{1}":[339,340],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#Template: AnalystDecisionsInTranscript [​](#Template:-AnalystDecisionsInTranscript)#{2}":[341,342],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#Template: AnalystDecisionsInTranscript [​](#Template:-AnalystDecisionsInTranscript)#{3}":[343,344],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#Template: AnalystDecisionsInTranscript [​](#Template:-AnalystDecisionsInTranscript)#{4}":[345,346],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#Template: AnalystDecisionsInTranscript [​](#Template:-AnalystDecisionsInTranscript)#{5}":[347,349],"#Chapter 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Section 1.1: [Descriptive Title] [Timestamp as HH:MM:SS]#Template: AnalystDecisionsInTranscript [​](#Template:-AnalystDecisionsInTranscript)#{6}":[350,359],"#Key Decision 1: [Descriptive Title] [Timestamp as HH:MM:SS]":[360,367],"#Key Decision 1: [Descriptive Title] [Timestamp as HH:MM:SS]#{1}":[361,362],"#Key Decision 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Next Steps for Decision 1":[363,367],"#Key Decision 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Next Steps for Decision 1#{1}":[364,365],"#Key Decision 1: [Descriptive Title] [Timestamp as HH:MM:SS]#Next Steps for Decision 1#{2}":[366,367],"#Other Next Steps":[368,437],"#Other Next Steps#{1}":[369,404],"#Other Next Steps#{2}":[370,404],"#Other Next Steps##Template: AnalystThemesInResponses [​](#Template:-AnalystThemesInResponses)":[405,437],"#Other Next Steps##Template: AnalystThemesInResponses [​](#Template:-AnalystThemesInResponses)#{1}":[407,408],"#Other Next Steps##Template: AnalystThemesInResponses [​](#Template:-AnalystThemesInResponses)#{2}":[409,410],"#Other Next Steps##Template: AnalystThemesInResponses [​](#Template:-AnalystThemesInResponses)#{3}":[411,412],"#Other Next Steps##Template: AnalystThemesInResponses [​](#Template:-AnalystThemesInResponses)#{4}":[413,414],"#Other Next Steps##Template: AnalystThemesInResponses [​](#Template:-AnalystThemesInResponses)#{5}":[415,417],"#Other Next Steps##Template: AnalystThemesInResponses [​](#Template:-AnalystThemesInResponses)#{6}":[418,437],"#Theme 1: [Theme Description] [​](#theme-1-theme-description)":[438,441],"#Theme 1: [Theme Description] [​](#theme-1-theme-description)#{1}":[440,441],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)":[442,718],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)#{1}":[444,471],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)#{2}":[446,471],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: AssistantAsk [​](#Template:-AssistantAsk)":[472,502],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: AssistantAsk [​](#Template:-AssistantAsk)#{1}":[474,475],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: AssistantAsk [​](#Template:-AssistantAsk)#{2}":[476,477],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: AssistantAsk [​](#Template:-AssistantAsk)#{3}":[478,479],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: AssistantAsk [​](#Template:-AssistantAsk)#{4}":[480,481],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: AssistantAsk [​](#Template:-AssistantAsk)#{5}":[482,484],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: AssistantAsk [​](#Template:-AssistantAsk)#{6}":[485,502],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DetailOrientedTask [​](#Template:-DetailOrientedTask)":[503,539],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DetailOrientedTask [​](#Template:-DetailOrientedTask)#{1}":[505,506],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DetailOrientedTask [​](#Template:-DetailOrientedTask)#{2}":[507,508],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DetailOrientedTask [​](#Template:-DetailOrientedTask)#{3}":[509,510],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DetailOrientedTask [​](#Template:-DetailOrientedTask)#{4}":[511,512],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DetailOrientedTask [​](#Template:-DetailOrientedTask)#{5}":[513,515],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DetailOrientedTask [​](#Template:-DetailOrientedTask)#{6}":[516,539],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DrafterEmailBrief [​](#Template:-DrafterEmailBrief)":[540,593],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DrafterEmailBrief [​](#Template:-DrafterEmailBrief)#{1}":[542,543],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DrafterEmailBrief [​](#Template:-DrafterEmailBrief)#{2}":[544,545],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DrafterEmailBrief [​](#Template:-DrafterEmailBrief)#{3}":[546,547],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DrafterEmailBrief [​](#Template:-DrafterEmailBrief)#{4}":[548,549],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DrafterEmailBrief [​](#Template:-DrafterEmailBrief)#{5}":[550,552],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: DrafterEmailBrief [​](#Template:-DrafterEmailBrief)#{6}":[553,593],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertAsk [​](#Template:-JuliaExpertAsk)":[594,624],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertAsk [​](#Template:-JuliaExpertAsk)#{1}":[596,597],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertAsk [​](#Template:-JuliaExpertAsk)#{2}":[598,599],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertAsk [​](#Template:-JuliaExpertAsk)#{3}":[600,601],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertAsk [​](#Template:-JuliaExpertAsk)#{4}":[602,603],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertAsk [​](#Template:-JuliaExpertAsk)#{5}":[604,606],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertAsk [​](#Template:-JuliaExpertAsk)#{6}":[607,624],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertCoTTask [​](#Template:-JuliaExpertCoTTask)":[625,670],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertCoTTask [​](#Template:-JuliaExpertCoTTask)#{1}":[627,628],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertCoTTask [​](#Template:-JuliaExpertCoTTask)#{2}":[629,630],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertCoTTask [​](#Template:-JuliaExpertCoTTask)#{3}":[631,632],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertCoTTask [​](#Template:-JuliaExpertCoTTask)#{4}":[633,634],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertCoTTask [​](#Template:-JuliaExpertCoTTask)#{5}":[635,637],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertCoTTask [​](#Template:-JuliaExpertCoTTask)#{6}":[638,670],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertTestCode [​](#Template:-JuliaExpertTestCode)":[671,718],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertTestCode [​](#Template:-JuliaExpertTestCode)#{1}":[673,674],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertTestCode [​](#Template:-JuliaExpertTestCode)#{2}":[675,676],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertTestCode [​](#Template:-JuliaExpertTestCode)#{3}":[677,678],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertTestCode [​](#Template:-JuliaExpertTestCode)#{4}":[679,680],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertTestCode [​](#Template:-JuliaExpertTestCode)#{5}":[681,683],"#Theme 2: [Theme Description] [​](#theme-2-theme-description)##Template: JuliaExpertTestCode [​](#Template:-JuliaExpertTestCode)#{6}":[684,718],"#<any setup code and shared inputs go here>":[719,720],"#Test for correct addition of positive numbers":[721,723],"#Test for correct addition of positive numbers#{1}":[722,723],"#Test for correct addition with a negative number":[724,726],"#Test for correct addition with a negative number#{1}":[725,726],"#Test for correct addition with zero":[727,729],"#Test for correct addition with zero#{1}":[728,729],"#Test for correct addition of large numbers":[730,1040],"#Test for correct addition of large numbers#{1}":[731,751],"#Test for correct addition of large numbers##Template: JuliaRecapCoTTask [​](#Template:-JuliaRecapCoTTask)":[752,802],"#Test for correct addition of large numbers##Template: JuliaRecapCoTTask [​](#Template:-JuliaRecapCoTTask)#{1}":[754,755],"#Test for correct addition of large numbers##Template: JuliaRecapCoTTask [​](#Template:-JuliaRecapCoTTask)#{2}":[756,757],"#Test for correct addition of large numbers##Template: JuliaRecapCoTTask [​](#Template:-JuliaRecapCoTTask)#{3}":[758,759],"#Test for correct addition of large numbers##Template: JuliaRecapCoTTask [​](#Template:-JuliaRecapCoTTask)#{4}":[760,761],"#Test for correct addition of large numbers##Template: JuliaRecapCoTTask [​](#Template:-JuliaRecapCoTTask)#{5}":[762,764],"#Test for correct addition of large numbers##Template: JuliaRecapCoTTask [​](#Template:-JuliaRecapCoTTask)#{6}":[765,802],"#Test for correct addition of large numbers##Template: JuliaRecapTask [​](#Template:-JuliaRecapTask)":[803,853],"#Test for correct addition of large numbers##Template: JuliaRecapTask [​](#Template:-JuliaRecapTask)#{1}":[805,806],"#Test for correct addition of large numbers##Template: JuliaRecapTask [​](#Template:-JuliaRecapTask)#{2}":[807,808],"#Test for correct addition of large numbers##Template: JuliaRecapTask [​](#Template:-JuliaRecapTask)#{3}":[809,810],"#Test for correct addition of large numbers##Template: JuliaRecapTask [​](#Template:-JuliaRecapTask)#{4}":[811,812],"#Test for correct addition of large numbers##Template: JuliaRecapTask [​](#Template:-JuliaRecapTask)#{5}":[813,815],"#Test for correct addition of large numbers##Template: JuliaRecapTask [​](#Template:-JuliaRecapTask)#{6}":[816,853],"#Test for correct addition of large numbers##Template: StorytellerExplainSHAP [​](#Template:-StorytellerExplainSHAP)":[854,914],"#Test for correct addition of large numbers##Template: StorytellerExplainSHAP [​](#Template:-StorytellerExplainSHAP)#{1}":[856,857],"#Test for correct addition of large numbers##Template: StorytellerExplainSHAP [​](#Template:-StorytellerExplainSHAP)#{2}":[858,859],"#Test for correct addition of large numbers##Template: StorytellerExplainSHAP [​](#Template:-StorytellerExplainSHAP)#{3}":[860,861],"#Test for correct addition of large numbers##Template: StorytellerExplainSHAP [​](#Template:-StorytellerExplainSHAP)#{4}":[862,863],"#Test for correct addition of large numbers##Template: StorytellerExplainSHAP [​](#Template:-StorytellerExplainSHAP)#{5}":[864,866],"#Test for correct addition of large numbers##Template: StorytellerExplainSHAP [​](#Template:-StorytellerExplainSHAP)#{6}":[867,914],"#---frontmatter---":[887,889],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)":[915,1040],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertAskXML [​](#Template:-JuliaExpertAskXML)":[917,947],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertAskXML [​](#Template:-JuliaExpertAskXML)#{1}":[919,920],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertAskXML [​](#Template:-JuliaExpertAskXML)#{2}":[921,922],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertAskXML [​](#Template:-JuliaExpertAskXML)#{3}":[923,924],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertAskXML [​](#Template:-JuliaExpertAskXML)#{4}":[925,926],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertAskXML [​](#Template:-JuliaExpertAskXML)#{5}":[927,929],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertAskXML [​](#Template:-JuliaExpertAskXML)#{6}":[930,947],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertCoTTaskXML [​](#Template:-JuliaExpertCoTTaskXML)":[948,993],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertCoTTaskXML [​](#Template:-JuliaExpertCoTTaskXML)#{1}":[950,951],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertCoTTaskXML [​](#Template:-JuliaExpertCoTTaskXML)#{2}":[952,953],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertCoTTaskXML [​](#Template:-JuliaExpertCoTTaskXML)#{3}":[954,955],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertCoTTaskXML [​](#Template:-JuliaExpertCoTTaskXML)#{4}":[956,957],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertCoTTaskXML [​](#Template:-JuliaExpertCoTTaskXML)#{5}":[958,960],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertCoTTaskXML [​](#Template:-JuliaExpertCoTTaskXML)#{6}":[961,993],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertTestCodeXML [​](#Template:-JuliaExpertTestCodeXML)":[994,1040],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertTestCodeXML [​](#Template:-JuliaExpertTestCodeXML)#{1}":[996,997],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertTestCodeXML [​](#Template:-JuliaExpertTestCodeXML)#{2}":[998,999],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertTestCodeXML [​](#Template:-JuliaExpertTestCodeXML)#{3}":[1000,1001],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertTestCodeXML [​](#Template:-JuliaExpertTestCodeXML)#{4}":[1002,1003],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertTestCodeXML [​](#Template:-JuliaExpertTestCodeXML)#{5}":[1004,1006],"#Test for correct addition of large numbers#Xml-Formatted Templates [​](#Xml-Formatted-Templates)#Template: JuliaExpertTestCodeXML [​](#Template:-JuliaExpertTestCodeXML)#{6}":[1007,1040],"#<any setup code and shared inputs go here>[2]":[1041,1042],"#Test for correct addition of positive numbers[2]":[1043,1045],"#Test for correct addition of positive numbers[2]#{1}":[1044,1045],"#Test for correct addition with a negative number[2]":[1046,1048],"#Test for correct addition with a negative number[2]#{1}":[1047,1048],"#Test for correct addition with zero[2]":[1049,1051],"#Test for correct addition with zero[2]#{1}":[1050,1051],"#Test for correct addition of large numbers[2]":[1052,1083],"#Test for correct addition of large numbers[2]#{1}":[1053,1083]},"last_import":{"mtime":1712727885449,"size":29925,"at":1740449882799,"hash":"88e2f76779c86b73caf57b06d361ef605423f2ffc0be89b7e924b3438127b566"},"key":"PromptingTools.jl/Theme 1- [Theme Description] - PromptingTools.jl.md"},