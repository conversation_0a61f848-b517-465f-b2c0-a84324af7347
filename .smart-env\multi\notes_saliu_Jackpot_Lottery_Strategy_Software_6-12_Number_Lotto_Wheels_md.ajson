
"smart_sources:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md": {"path":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1201915,-0.02101338,-0.01191018,0.00276798,-0.05753428,0.03626443,-0.0162064,0.01798872,0.03862609,0.01345268,0.02691199,0.02411827,0.03547898,-0.02138039,-0.03892273,-0.02180966,0.01563433,-0.02636649,-0.03626423,-0.02689888,0.06377795,-0.05519667,-0.02914087,-0.07928092,0.00867628,0.00310615,-0.02473683,-0.04854687,-0.06051296,-0.24518426,0.01942141,0.01242994,0.02195378,-0.05890718,-0.08473993,0.01611984,-0.0601189,0.03307901,-0.04661074,0.04296507,-0.00029768,0.031278,0.0052131,-0.00185394,0.02923948,-0.03519892,0.00399511,0.00631626,0.06970689,0.00238996,-0.06053269,0.03010146,-0.01310246,0.04582356,0.09824263,0.05355467,0.02003746,0.08509883,0.01590647,0.05170298,0.03032874,0.06087939,-0.21337502,0.03680595,-0.02016185,0.03837333,-0.02405898,-0.01680712,-0.00339489,0.03826612,0.05427167,0.01630982,-0.02514931,0.02258073,0.08064545,-0.04658393,-0.06258792,-0.0263431,-0.01969214,0.02693712,-0.0340054,-0.00082975,-0.02984068,-0.01883565,0.00726674,0.03171559,0.04770538,0.04788782,0.06487221,-0.06172419,0.02829206,0.04704912,0.05128675,0.01562675,0.01433622,0.03997289,0.06746192,-0.0194005,-0.01579257,0.08396432,-0.00090427,0.01255788,0.00222884,-0.00678031,0.02914626,-0.05002594,0.00412599,-0.04144429,-0.01580187,0.04795121,0.06444298,0.03800666,0.07875386,-0.03998303,-0.04265046,-0.0194138,-0.01373025,0.02617079,-0.01749324,0.00843985,-0.03418259,0.04434076,0.01514322,-0.01687979,0.0048421,-0.00337278,0.03000555,0.06015065,0.0082636,0.02752184,0.02763361,-0.00119325,-0.12221152,-0.03572628,-0.03229306,-0.03694294,-0.03808459,-0.04664799,-0.0204846,0.00618856,-0.02848165,-0.07872305,0.04337398,-0.06745439,-0.03027295,0.03444358,0.03031432,-0.00586124,0.02804816,0.05243532,0.00446504,-0.01886236,-0.03188107,-0.04175532,0.01539431,0.0184084,0.09732068,0.08692583,-0.04129811,-0.00878229,-0.02731058,-0.05431996,-0.01188098,0.09236424,0.0020359,-0.12204178,-0.01969089,0.05777963,0.00055593,-0.10149076,-0.03694488,0.00244883,-0.03918598,0.01727839,0.0807436,-0.00752974,-0.05133136,-0.03479812,-0.03658582,-0.00022047,-0.02973959,-0.01388651,-0.03581377,0.01035115,-0.04798934,-0.06707816,0.03088212,-0.05470139,0.03087547,0.06124666,-0.01781356,0.02242488,-0.04902066,0.04433633,-0.02771733,-0.00572273,-0.03461817,-0.06715486,0.09450969,-0.04067882,-0.04787247,-0.00341843,0.00095588,-0.00636721,-0.0681958,0.01416483,-0.0299474,-0.0710505,0.05947129,0.06132894,-0.03000375,0.01325536,0.03637815,0.06861655,-0.09403998,0.05697747,-0.0262589,-0.00055922,0.01482348,-0.02237586,-0.00262887,-0.01136713,-0.04486877,-0.18856359,-0.04174593,-0.07156894,-0.01452713,0.01616531,-0.01352048,0.01022853,-0.04527833,0.0605684,0.0318826,0.08807001,-0.04923556,0.00232815,0.04759049,0.00522069,-0.01275773,-0.07503089,-0.02980813,-0.03410214,0.06855141,0.01356361,-0.017781,-0.01655479,-0.0572558,-0.00702973,-0.05891061,0.14309394,0.05164916,0.03155938,-0.00666637,0.07238799,0.01159511,0.00174058,-0.04429393,0.02790014,-0.00340943,-0.01189472,-0.01579681,-0.05774794,0.01476602,-0.05577688,0.00526799,0.02168159,-0.11667485,-0.03284173,-0.00084791,-0.03004564,0.0333903,-0.00589742,0.08675712,0.05076834,0.0440781,0.05520355,0.01547095,0.05435309,-0.04338154,-0.06506094,-0.01798411,0.00507266,0.04356237,-0.05370835,-0.03388992,0.06544619,-0.01931322,0.06835032,0.03628557,-0.01092827,0.00549132,0.0150271,-0.03969039,-0.01573255,0.09235656,-0.00257451,0.04424315,-0.01984683,0.00555588,0.07692531,-0.06589692,0.01397516,-0.01318737,-0.00638342,-0.06066765,0.06357265,0.06929482,0.04427853,0.01059505,0.05613026,0.0555403,-0.0108047,0.01695982,0.00371619,0.01480656,-0.03340771,0.02611994,0.02919208,0.02517311,-0.25411689,0.00445912,0.01355669,0.04476443,-0.00469388,0.00910038,0.07931976,-0.00392176,0.0388024,-0.00538868,0.07236957,0.04566256,0.00833035,-0.06172272,0.05626599,-0.00028046,-0.04683385,-0.02473372,0.08509015,0.01678831,0.05258007,0.0702442,0.22613457,-0.01980693,0.02046724,0.00859823,0.01585202,0.01571197,0.00667415,0.05535294,-0.02342637,0.03329249,0.04593478,-0.03477702,-0.05975825,0.00352428,-0.02440792,0.02950476,-0.06162945,0.00953362,-0.08053286,0.0128174,-0.02469151,0.02085872,0.14503542,0.03162762,-0.02827419,-0.10783841,0.06967735,0.02394646,-0.05428813,-0.07802709,-0.04970942,-0.01015228,0.01697319,0.03954853,0.035698,-0.03433684,0.01350122,0.00373454,0.04984575,0.02313842,0.08735667,0.02342689,-0.00087303],"last_embed":{"hash":"1k6ikaz","tokens":473}}},"last_read":{"hash":"1k6ikaz","at":1753423477808},"class_name":"SmartSource","last_import":{"mtime":1753363610876,"size":18048,"at":1753423416052,"hash":"1k6ikaz"},"blocks":{"#---frontmatter---":[1,6],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels":[8,150],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#{1}":[10,17],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u><i>Jackpot</i> Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software</u>":[18,19],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#By Ion Saliu, _★ The Best Lottery Player in History_":[20,25],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#By Ion Saliu, _★ The Best Lottery Player in History_#{1}":[22,25],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>":[26,58],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>#{1}":[28,48],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>#{2}":[49,50],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>#{3}":[51,58],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>":[59,97],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#{1}":[61,72],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#{2}":[73,73],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#{3}":[74,75],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#Now, the big _lotto strategy_ that did hit the _jackpot_.":[76,97],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#Now, the big _lotto strategy_ that did hit the _jackpot_.#{1}":[78,97],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>":[98,150],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#{1}":[100,123],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>":[124,150],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{1}":[126,126],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{2}":[127,127],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{3}":[128,128],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{4}":[129,129],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{5}":[130,130],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{6}":[131,131],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{7}":[132,132],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{8}":[133,133],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{9}":[134,135],"#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{10}":[136,150]},"outlinks":[{"title":"![Ion Saliu Parpaluck has won the lottery multiple times since 1980s in Romania and United States.","target":"https://saliu.com/AxiomIon.jpg","line":16},{"title":"The lotto strategy that hit the Pennsylvania Lottery jackpot in 1986.","target":"https://saliu.com/HLINE.gif","line":22},{"title":"_**Make Lotto Wheels, Systems: Manually, or in Lotto Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":49},{"title":"Play a powerful lotto strategy with 12-number combinations in lotto-6 games worldwide.","target":"https://saliu.com/HLINE.gif","line":57},{"title":"_**Randomness, Degree of Randomness, Degree of Certainty, True Random Numbers Generators**_","target":"https://saliu.com/bbs/messages/683.html","line":73},{"title":"_**BASIC source code software to generate true random & unique numbers**_","target":"https://saliu.com/random-numbers.html","line":74},{"title":"The best-ever lotto player created a strategy that hit the lotto jackpot in Pennsylvania Lottery.","target":"https://saliu.com/HLINE.gif","line":96},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":127},{"title":"_**Lotto Software, Wheels: 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":129},{"title":"_**Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":130},{"title":"_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_","target":"https://saliu.com/bbs/messages/532.html","line":131},{"title":"_**Lottery, Gambling Experiences: Robbed of Prize Money!**_","target":"https://saliu.com/bbs/messages/535.html","line":132},{"title":"_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_","target":"https://saliu.com/lotto-triples.html","line":134},{"title":"Ion Saliu's Probability Book on mathematics of lotto strategy.","target":"https://saliu.com/probability-book-Saliu.jpg","line":142},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":142},{"title":"The best lotto wheels are also created by the best lottery player in history, Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":145},{"title":"Forums","target":"https://forums.saliu.com/","line":147},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":147},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":147},{"title":"Contents","target":"https://saliu.com/content/index.html","line":147},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":147},{"title":"Home","target":"https://saliu.com/index.htm","line":147},{"title":"Search","target":"https://saliu.com/Search.htm","line":147},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":147},{"title":"Lotto strategy by the best lottery player ever hit the jackpot generating 12-number combinations.","target":"https://saliu.com/HLINE.gif","line":149}],"metadata":{"created":"2025-07-24T21:26:40 (UTC +08:00)","tags":["strategy","systems","lotto","wheels","software","lottery","jackpot","computer","generate","random","play","win","lost lotto jackpot"],"source":"https://saliu.com/lotto-jackpot-lost.html","author":null}},"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#---frontmatter---": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10726266,-0.05275839,-0.02220705,-0.00756657,-0.05139726,0.06553621,-0.0177034,0.01176178,0.05639522,-0.00341717,0.01508084,0.00821662,0.04516015,-0.01203955,-0.01448509,-0.01483134,0.00053505,-0.0180972,-0.03622947,-0.01994534,0.08825722,-0.031193,-0.06997338,-0.08875855,-0.01136945,-0.00957556,-0.02099492,-0.06334842,-0.04769728,-0.16681199,0.01383471,0.00804703,0.02127778,-0.0326063,-0.06104775,-0.0247565,-0.0540773,0.04930792,-0.05010989,0.03199759,0.00953911,0.01630266,-0.01159199,0.00548106,0.03796903,-0.03839331,0.00108699,0.0083673,0.06780483,-0.00520371,-0.05857537,0.01195402,-0.01891624,0.04798948,0.09832024,0.03663166,0.03628447,0.07670176,0.03977812,0.03674927,0.04178253,0.04109314,-0.21674444,0.02838382,-0.01710697,0.03360553,-0.00414923,-0.02731179,-0.00702078,0.04138712,0.06538529,0.02965905,-0.00445794,0.04635487,0.06645169,-0.01898093,-0.04571025,-0.05083444,-0.0375028,0.011761,-0.04401587,-0.00992976,-0.02147971,-0.01680974,-0.01141348,0.01397917,0.05356547,0.07515331,0.07392179,-0.0363076,0.00927605,0.04397516,0.0625227,0.02471993,-0.04061905,0.01662111,0.05746036,-0.01272786,-0.01903516,0.10107605,-0.01765296,0.02920935,-0.0048415,-0.00795461,0.0397296,-0.01920225,-0.01154621,-0.02725787,-0.03185348,0.02481831,0.07423245,0.04155435,0.09080987,-0.06746772,-0.0294509,-0.0056598,0.00128442,0.04452186,-0.01064483,0.02071091,-0.03021672,0.02992053,0.02419941,-0.01853685,0.01484493,0.03006013,0.02285964,0.06674235,-0.0022868,0.03959667,0.00853812,0.0142916,-0.12475536,-0.02057354,-0.02352099,-0.01728965,-0.01478624,-0.07129589,-0.02075088,-0.01326087,-0.03375231,-0.05179225,0.03470165,-0.09122744,-0.02749714,0.04729951,0.03144904,-0.00494653,0.01141806,0.00812637,-0.01573319,-0.01489009,-0.01534435,-0.04773257,0.00942665,0.02797478,0.11216296,0.08648233,-0.0323907,-0.00109838,-0.0297367,-0.0643424,-0.03200771,0.11100227,-0.00183929,-0.13641393,-0.02880849,0.0616759,0.01381373,-0.11610682,-0.04601734,-0.00313908,-0.05389713,0.01245889,0.11267968,-0.00223005,-0.03458542,-0.03515997,-0.03090185,-0.00637019,-0.02037334,-0.01743609,-0.03683916,0.01421916,-0.05152328,-0.05976483,0.03031418,-0.04464481,0.03665993,0.05270098,-0.027613,-0.02407585,-0.04564019,0.0198531,-0.03376181,-0.02212062,-0.04586443,-0.0647506,0.09203473,-0.04567235,-0.01634472,0.00959677,-0.00879097,0.01836027,-0.06844689,0.00668551,-0.02993226,-0.07786075,0.05214247,0.05918713,-0.01259845,0.03825847,0.0603357,0.06191533,-0.08230665,0.05463828,-0.01644008,-0.00210024,0.02313178,-0.02263431,-0.00489994,-0.01208214,-0.05280321,-0.20616601,-0.01782629,-0.03841621,-0.01478986,-0.01246282,-0.00116959,0.00964317,-0.03909039,0.05331101,0.0564238,0.09703237,-0.0482728,0.00767458,0.02480439,-0.00242636,0.00024202,-0.07743174,-0.05513882,-0.01132437,0.07355329,0.01976568,-0.00552239,-0.02823211,-0.10437631,0.00399706,-0.02444573,0.14430723,0.04884937,0.03481779,-0.01716269,0.10794225,0.01207885,-0.02308015,-0.04649516,0.01942777,-0.00015373,-0.03535917,0.00540627,-0.0830097,-0.00823482,-0.08099257,0.02656992,0.02033755,-0.11389897,-0.01163246,-0.00773707,-0.02236572,0.00905092,0.00067382,0.07732603,0.02634078,0.03789061,0.04860262,0.02261198,0.06010185,-0.011132,-0.05253592,-0.02986758,-0.01312045,0.02337218,-0.03660739,-0.03748417,0.05939742,-0.04095596,0.03431927,0.01177116,-0.03105632,-0.03376539,-0.00668546,-0.03798243,-0.00590536,0.07043888,0.02836712,0.02370383,-0.03142181,-0.01763636,0.07075528,-0.04904079,0.01674655,-0.01610169,-0.01742871,-0.06127069,0.07472514,0.08984539,0.07437643,0.03162969,0.03749306,0.04963464,0.02030374,0.01153014,0.00203048,0.00889023,-0.02818922,0.00685294,0.04932229,0.01457458,-0.26324621,0.02807539,0.02970943,0.07362835,0.00100092,0.00778157,0.07492366,0.02669088,0.00162084,0.02321415,0.05897557,0.05383197,-0.00566661,-0.03886869,0.02837485,-0.00887405,-0.01149521,-0.03414782,0.06295291,0.03580303,0.02167882,0.06186952,0.23617177,0.00204283,0.01927669,0.00758311,0.02107641,0.01489833,0.01885999,0.06361169,-0.00554824,0.02634528,0.04380509,-0.01822717,-0.05535482,0.01606439,-0.03908329,0.03516133,-0.02936272,0.01671252,-0.08478383,0.02872477,-0.03814107,0.00926586,0.12325474,0.02799374,-0.02478309,-0.10631834,0.04091776,0.02501539,-0.05778708,-0.07820063,-0.06818818,-0.01669032,0.02002451,0.06188758,0.05446858,-0.03618192,0.02457204,0.001095,0.0352961,-0.02237353,0.07983677,-0.00681677,-0.00903446],"last_embed":{"hash":"1gxf7lc","tokens":104}}},"text":null,"length":0,"last_read":{"hash":"1gxf7lc","at":1753423476010},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#---frontmatter---","lines":[1,6],"size":225,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0997545,-0.01545449,-0.01206098,-0.00693789,-0.05563414,0.03328761,-0.00386291,0.00694595,0.0277845,0.00906274,0.02682965,0.02651132,0.03968468,-0.01234844,-0.04605104,-0.01366483,0.00834378,-0.01530037,-0.05634366,-0.02414827,0.05213519,-0.07253373,-0.02872786,-0.07254265,0.03511099,0.02035337,-0.03744984,-0.07238359,-0.05819741,-0.25082785,0.02003176,0.02505864,0.04193497,-0.07332703,-0.09860536,0.00400326,-0.06917243,0.04951505,-0.06117794,0.04370321,0.00329989,0.02959498,0.00964626,-0.00149212,0.0174549,-0.03977152,-0.00907676,0.01079838,0.071108,0.00655322,-0.0443352,0.01801134,-0.00621938,0.05138549,0.09237827,0.05077862,0.01618072,0.07119961,0.019174,0.05230741,0.01002455,0.08183323,-0.19976802,0.03721923,-0.02109687,0.041557,-0.02082101,-0.01236311,-0.00988876,0.05907274,0.05485356,0.03453148,-0.02811063,0.01114904,0.07154143,-0.05505479,-0.08400693,-0.02231925,-0.01642026,0.03306152,-0.04549284,-0.01534962,-0.0288053,-0.02395493,0.01004156,0.03774453,0.04330989,0.04063325,0.05654662,-0.05514075,0.03861182,0.07249683,0.02280322,0.00858612,0.03932907,0.03919444,0.06122598,-0.05011578,-0.00232678,0.09357893,0.00616406,0.00256725,-0.00281444,-0.01578049,0.01443127,-0.05506538,-0.02422748,-0.04218168,-0.01209864,0.05507835,0.06246432,0.0343435,0.07053272,-0.03718897,-0.05754824,-0.01124151,-0.01545415,0.01661341,-0.00960617,0.00769718,-0.01926158,0.02327921,0.0098458,-0.01290151,0.00135823,0.00612473,0.02549381,0.07650669,0.02652617,0.02386967,0.02202317,0.00862843,-0.1305283,-0.04429706,-0.03520315,-0.03927524,-0.02825266,-0.0260287,-0.02308683,0.00976865,-0.02915089,-0.08687167,0.05423994,-0.07530303,-0.00968386,0.05576437,0.01554801,-0.00567834,0.01461138,0.03671605,-0.00946855,-0.01079131,-0.07087345,-0.04643048,-0.00254086,0.00034459,0.0932882,0.0757162,-0.04335592,0.00818561,-0.03882077,-0.03699592,0.00617615,0.10322747,0.00341752,-0.10073528,-0.01033129,0.04268162,-0.02924987,-0.09501681,-0.0475633,-0.00558897,-0.0417011,0.04509741,0.07920539,-0.00481648,-0.06770136,-0.01956006,-0.02619984,-0.00661291,-0.02621383,-0.0057781,-0.03402504,-0.01139404,-0.03088224,-0.06786595,0.03771276,-0.03829754,0.03179327,0.05180372,-0.02845723,0.03510606,-0.05311105,0.02695077,-0.02544016,-0.00515524,-0.03407559,-0.06177299,0.09508673,-0.02248046,-0.03946424,-0.01303814,-0.00114723,0.00378226,-0.05070108,0.03694022,-0.02463683,-0.06588355,0.05761164,0.0637325,-0.0367559,0.00414832,0.02783258,0.0663864,-0.07208309,0.04554583,-0.01612148,-0.00624667,0.00974248,-0.01452102,0.02437244,0.00022374,-0.05249267,-0.17425814,-0.04456232,-0.04303106,0.00013496,0.01972481,-0.02044307,0.00588447,-0.04633269,0.04409186,0.04572602,0.08042271,-0.05407432,-0.0058213,0.0688254,0.00387161,-0.01581829,-0.08664303,-0.02473228,-0.05428106,0.05735554,0.01864283,-0.01933417,-0.01938781,-0.05066309,0.00668596,-0.03968998,0.13560504,0.04024282,0.02246462,0.0087647,0.07530618,0.02121747,0.00102833,-0.00434425,0.02903565,-0.00839964,-0.02037389,-0.04035197,-0.03798251,0.01397503,-0.04775002,0.01250179,0.00351608,-0.13297875,-0.02234147,0.00304261,-0.0421452,0.05716414,0.01406734,0.06272576,0.0622451,0.0247995,0.06653512,0.025175,0.06240325,-0.04946092,-0.07667789,-0.00952817,-0.00447011,0.04945073,-0.04240226,-0.0470826,0.05115702,0.00889762,0.06831557,0.03934857,-0.01635858,0.00530537,0.02493176,-0.0268201,-0.02089009,0.08026081,0.00206265,0.04624594,-0.02205349,0.01697575,0.0567347,-0.05891078,0.019935,-0.01874746,0.01875551,-0.05263713,0.03695099,0.06918559,0.03147597,-0.00957134,0.05265148,0.07802499,-0.01456392,0.0195372,0.01898825,0.02815752,-0.03646331,0.06025783,0.01401554,0.02641841,-0.24683717,0.01638073,-0.0157956,0.04806264,0.00515204,0.01142146,0.07190833,0.00164408,0.03714763,-0.01597862,0.0606912,0.05923774,0.02018113,-0.06744694,0.06294274,-0.01614056,-0.05160763,-0.04266461,0.07578281,0.01188787,0.0593891,0.0591789,0.23141347,-0.0167596,0.04215207,0.02196111,0.01209201,0.01982643,-0.00367882,0.03661569,-0.04226506,0.02029349,0.05215468,-0.05205517,-0.0578382,-0.00253626,-0.01195876,0.00683254,-0.06229041,0.01771296,-0.08703024,-0.00244719,-0.01304179,0.00273534,0.16318542,0.0192878,-0.03229732,-0.11733335,0.07953367,0.03641575,-0.04833522,-0.07350177,-0.05204727,-0.01542351,0.01265676,0.03127087,0.025397,-0.02217264,0.00516926,0.00880682,0.0348162,0.02579673,0.07641856,0.04771025,-0.00845026],"last_embed":{"hash":"e4kk5c","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"e4kk5c","at":1753423476043},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels","lines":[8,150],"size":17781,"outlinks":[{"title":"![Ion Saliu Parpaluck has won the lottery multiple times since 1980s in Romania and United States.","target":"https://saliu.com/AxiomIon.jpg","line":9},{"title":"The lotto strategy that hit the Pennsylvania Lottery jackpot in 1986.","target":"https://saliu.com/HLINE.gif","line":15},{"title":"_**Make Lotto Wheels, Systems: Manually, or in Lotto Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":42},{"title":"Play a powerful lotto strategy with 12-number combinations in lotto-6 games worldwide.","target":"https://saliu.com/HLINE.gif","line":50},{"title":"_**Randomness, Degree of Randomness, Degree of Certainty, True Random Numbers Generators**_","target":"https://saliu.com/bbs/messages/683.html","line":66},{"title":"_**BASIC source code software to generate true random & unique numbers**_","target":"https://saliu.com/random-numbers.html","line":67},{"title":"The best-ever lotto player created a strategy that hit the lotto jackpot in Pennsylvania Lottery.","target":"https://saliu.com/HLINE.gif","line":89},{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":120},{"title":"_**Lotto Software, Wheels: 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":122},{"title":"_**Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":123},{"title":"_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_","target":"https://saliu.com/bbs/messages/532.html","line":124},{"title":"_**Lottery, Gambling Experiences: Robbed of Prize Money!**_","target":"https://saliu.com/bbs/messages/535.html","line":125},{"title":"_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_","target":"https://saliu.com/lotto-triples.html","line":127},{"title":"Ion Saliu's Probability Book on mathematics of lotto strategy.","target":"https://saliu.com/probability-book-Saliu.jpg","line":135},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":135},{"title":"The best lotto wheels are also created by the best lottery player in history, Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":138},{"title":"Forums","target":"https://forums.saliu.com/","line":140},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":140},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":140},{"title":"Contents","target":"https://saliu.com/content/index.html","line":140},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":140},{"title":"Home","target":"https://saliu.com/index.htm","line":140},{"title":"Search","target":"https://saliu.com/Search.htm","line":140},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":140},{"title":"Lotto strategy by the best lottery player ever hit the jackpot generating 12-number combinations.","target":"https://saliu.com/HLINE.gif","line":142}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09608939,-0.0167082,-0.01981593,-0.01196985,-0.06339276,0.04284171,0.01267874,-0.00244992,0.0435364,0.01472949,0.01710149,-0.00411113,0.03496105,-0.01059569,-0.03250398,-0.00410103,0.01074617,-0.03220338,-0.04860071,-0.04332687,0.09473991,-0.05279722,-0.03788061,-0.09462909,0.01475,0.00848845,-0.03600168,-0.06657534,-0.05401336,-0.23249757,0.03027268,0.01988167,0.013171,-0.06563509,-0.08424951,-0.01611477,-0.06815803,0.05129456,-0.04789304,0.02929863,-0.00050308,0.03868451,0.00264795,-0.00717867,0.02922024,-0.0585322,0.01677267,0.02366487,0.08587694,0.00878009,-0.05540622,0.01659507,-0.00994588,0.02782028,0.09666613,0.0486329,0.02075157,0.07253347,0.01456254,0.0500364,0.01815502,0.07219788,-0.21125996,0.01763271,-0.02189807,0.01695343,-0.01404441,-0.02069896,-0.03222103,0.05745586,0.07678253,0.02303587,-0.0174761,0.02517208,0.07663408,-0.05101033,-0.08396886,-0.02689734,-0.02166764,0.03977994,-0.04613436,-0.00019365,-0.01706262,-0.01329324,0.01340401,0.03185417,0.04219417,0.04703544,0.07629185,-0.05381329,0.02405507,0.06994671,0.01521665,0.01360691,0.01395469,0.04356037,0.0585815,-0.05501691,0.01675226,0.10622898,-0.00501038,0.00051615,-0.00757026,-0.0160439,0.00854077,-0.04094284,-0.0230395,-0.03402111,-0.01624262,0.0622981,0.05511314,0.02448933,0.08266136,-0.03492671,-0.06324521,-0.00744369,-0.01680975,-0.00223767,-0.01903763,0.01199797,-0.01782021,0.02688179,0.01054065,-0.01268889,0.00624996,-0.00290215,0.02957002,0.06961743,0.01232977,0.03501045,0.02189898,0.02077687,-0.12406925,-0.04130335,-0.02531217,-0.04006368,-0.01236598,-0.03035567,-0.02600217,-0.00078917,-0.01748242,-0.08772727,0.05266421,-0.08160682,-0.0156227,0.06076534,0.02151547,0.00380505,0.01047892,0.03285805,-0.01911332,-0.01702619,-0.03720953,-0.05172359,-0.00553051,0.01540866,0.08115479,0.09263853,-0.02669923,-0.00779603,-0.03511211,-0.04910558,0.01149789,0.10774268,-0.0195764,-0.10058089,-0.01353705,0.04975686,-0.01352005,-0.09367142,-0.04214897,0.00607164,-0.05055247,0.03634917,0.09852827,-0.01109228,-0.04074212,-0.02523629,-0.0355856,0.00100989,-0.04326756,-0.01511318,-0.03969311,0.01173611,-0.03289251,-0.06086888,0.03716242,-0.04941275,0.04717033,0.05171137,-0.01878736,0.04588332,-0.04867505,0.03336112,-0.03159885,-0.00347045,-0.03477894,-0.05069494,0.09668743,-0.03496268,-0.0200165,0.01619032,-0.00935768,0.00008012,-0.03184787,0.01834583,-0.03123513,-0.06036193,0.02613062,0.07367781,-0.0333074,0.00777645,0.04240006,0.07019749,-0.09084498,0.05167953,-0.02540091,-0.0122072,0.01497643,-0.0184511,0.01990591,-0.0122902,-0.04484419,-0.18578064,-0.0384876,-0.04129501,-0.00965402,0.02536302,-0.0029303,0.00872893,-0.04854473,0.08200937,0.02654548,0.08508404,-0.07795654,0.00831371,0.07683525,0.00180991,-0.00576866,-0.08339287,-0.02064187,-0.03652453,0.0662958,0.02218944,-0.01796552,-0.0122341,-0.06089732,0.01518555,-0.04357228,0.14579932,0.04852498,0.01890955,0.00201701,0.07294004,0.00419985,0.00221307,-0.00935912,0.0359446,-0.00502238,-0.01053508,-0.05003544,-0.05207345,0.00399796,-0.03533413,0.01567646,0.00421828,-0.10789166,-0.01869451,-0.01239544,-0.03145898,0.03960183,0.00957462,0.05901216,0.04937989,0.03752174,0.06354155,0.02516779,0.05552368,-0.03918378,-0.06901718,-0.01052079,-0.01759491,0.03995566,-0.04258218,-0.04704063,0.06495082,0.00179931,0.05435753,0.03664346,-0.01666944,-0.02821359,0.00083409,-0.03352556,-0.01407498,0.07292917,0.01030194,0.04436622,-0.0232277,0.01616302,0.07886931,-0.05789813,0.00954544,0.00086019,0.01266494,-0.04661473,0.03177101,0.05407348,0.03300486,-0.01713944,0.06791096,0.08109244,-0.01720354,0.02121184,0.02859279,0.00196558,-0.03342203,0.0421764,0.01392781,0.0454837,-0.26111886,0.01987366,-0.00552286,0.05861216,0.00642233,-0.00129172,0.08013292,-0.00685171,0.03311251,-0.01185652,0.06468707,0.06280097,0.02287458,-0.05170566,0.06308159,-0.01585164,-0.0381075,-0.03663496,0.08846419,0.00771852,0.03480276,0.05992891,0.22576386,-0.0028106,0.04924877,0.02047636,0.01583266,0.02579995,-0.00093127,0.03873515,-0.03110831,0.02043027,0.03805619,-0.03667045,-0.06227134,0.02806284,-0.02240461,0.01897075,-0.05497523,0.02553027,-0.09130872,0.01389285,-0.0317719,-0.00008291,0.14501049,0.01240755,-0.0445342,-0.10294419,0.06759696,0.02834076,-0.055912,-0.079533,-0.05330609,-0.03458602,0.00691382,0.02072272,0.0227685,-0.03394401,0.00316773,0.00347507,0.03199541,0.02018768,0.05470333,0.03940497,-0.00555518],"last_embed":{"hash":"1xfgbb5","tokens":157}}},"text":null,"length":0,"last_read":{"hash":"1xfgbb5","at":1753423476207},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#{1}","lines":[10,17],"size":427,"outlinks":[{"title":"![Ion Saliu Parpaluck has won the lottery multiple times since 1980s in Romania and United States.","target":"https://saliu.com/AxiomIon.jpg","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1031409,-0.04145167,-0.00861166,0.01009459,-0.04988886,0.03310844,0.00602393,0.01030232,0.04198729,-0.01085497,0.03599658,0.03769699,0.05174392,-0.00403446,-0.03306902,-0.01888546,-0.00805807,-0.02212362,-0.05626561,0.00062555,0.0369361,-0.05429836,-0.04233135,-0.06421784,0.03581786,0.03384596,-0.05423548,-0.07798383,-0.05527104,-0.24167758,0.02384479,0.02982035,0.0582913,-0.08905538,-0.09074904,0.0068233,-0.05404017,0.03919199,-0.06023965,0.04697419,0.01646828,0.01886088,0.00132351,-0.02424869,0.01470327,-0.03729251,-0.00773559,0.01736845,0.04054278,0.00342774,-0.03902296,0.00223367,-0.01659424,0.06815348,0.06785794,0.04213748,0.02534154,0.06024244,0.02181527,0.03917073,0.02476102,0.06113451,-0.18394208,0.04593175,-0.01786575,0.04248042,0.00162252,-0.02693692,0.01279477,0.07297281,0.05365797,0.04544544,-0.04293512,0.03264219,0.06985795,-0.04377761,-0.06668576,-0.02803174,-0.01789986,0.02198314,-0.04428929,-0.01398409,-0.02205445,-0.02273245,-0.00733649,0.03448405,0.05105079,0.04600317,0.06419164,-0.03297973,0.04043564,0.07277215,0.03972932,0.02068054,0.03277903,0.00335543,0.07895829,-0.02266999,-0.01781514,0.0961078,0.02300857,-0.01579389,0.0035916,0.00073282,0.0230152,-0.04336333,-0.03404077,-0.0379811,-0.011578,0.04584747,0.0807093,0.03921059,0.06032011,-0.04641267,-0.0622993,0.00730083,-0.0134576,0.02262857,-0.00352819,0.00289917,-0.03283449,0.00952149,0.00763456,-0.00662337,-0.00434077,-0.01522299,0.01675278,0.08189826,0.01376193,0.00529128,0.02367868,-0.00763881,-0.14507054,-0.04138003,-0.0500778,-0.01948889,-0.03106533,-0.03637653,-0.01096967,0.03562038,-0.03284167,-0.06649704,0.04119718,-0.07359516,-0.00732152,0.05320156,0.01505364,-0.02395689,0.01375158,0.02367354,-0.00405896,-0.01772165,-0.0613136,-0.04393233,0.00222249,-0.0131576,0.09114894,0.07398099,-0.06677384,0.00060175,-0.03060468,-0.03681432,-0.02160027,0.1047294,0.01814545,-0.11117211,-0.01008924,0.03484253,-0.01585308,-0.09086525,-0.04325417,-0.01245096,-0.02947132,0.05173008,0.08119822,-0.0018963,-0.07607859,-0.02602702,-0.02303252,-0.00901227,-0.01786202,-0.01574049,-0.01119502,0.01468935,-0.04224287,-0.089029,0.037907,-0.02998357,0.02120847,0.03726785,-0.04685982,0.01087309,-0.07864005,0.01940553,-0.03489302,-0.01224116,-0.02173406,-0.06746759,0.08316123,-0.02012553,-0.05056599,-0.03849914,0.01608955,0.00567225,-0.05222651,0.04658316,-0.01201346,-0.05837065,0.07615601,0.07115611,-0.02960065,0.01587151,0.05258492,0.06028806,-0.04499875,0.0354418,0.00187376,0.00857918,0.02180938,-0.02621099,0.04049858,0.00650626,-0.04465049,-0.18366884,-0.01946069,-0.04434956,0.00161072,0.01190872,-0.02337957,0.01892904,-0.04215532,0.02573792,0.05893127,0.06793469,-0.05820912,0.00426307,0.05174786,0.00793705,-0.03171618,-0.08929871,-0.05162758,-0.06574995,0.0583837,0.00413245,-0.01402151,-0.03744926,-0.07653904,-0.00188017,-0.01893109,0.13342953,0.03237878,0.02753201,0.0051623,0.07798751,0.02456413,-0.0084715,-0.01952838,0.01627424,0.01893795,-0.03006786,-0.04001616,-0.03649568,0.0144542,-0.07222547,0.02372762,-0.00273219,-0.12728538,-0.04714461,0.03729277,-0.02742612,0.0717653,0.0271625,0.06087471,0.05045142,-0.00549926,0.05604833,0.03991069,0.07107944,-0.03181856,-0.0789264,-0.00049295,0.00482828,0.06173119,-0.02894759,-0.01902501,0.05061263,-0.00290621,0.06261115,0.02660552,-0.03876521,-0.00540651,0.02052347,-0.03182503,-0.02019871,0.0908675,-0.00393015,0.00081416,-0.01638033,0.00783314,0.05479111,-0.04874414,0.00944001,-0.00909286,0.00907368,-0.07340667,0.03155463,0.08687072,0.04289263,-0.00070463,0.02959484,0.05291348,-0.00826064,0.02907331,0.00473255,0.02094252,-0.04725432,0.0682723,0.0252663,0.02655794,-0.22803253,0.02732123,-0.01562303,0.08658398,0.01046828,0.01290226,0.07355835,0.0230226,0.02120587,-0.01403678,0.07374517,0.0673831,0.03321918,-0.07903633,0.08117932,-0.02615168,-0.04108907,-0.05627713,0.05537226,0.03595819,0.06056572,0.05605808,0.2271021,-0.00434071,0.02854377,0.02704074,0.01871646,0.01871744,0.01282491,0.02578332,-0.03571749,-0.01059815,0.03492872,-0.06055826,-0.05768899,-0.0473938,-0.01222319,0.01221943,-0.01708027,0.00143246,-0.0605783,0.00359176,-0.00712476,-0.00540958,0.18199068,0.02844416,-0.03477142,-0.11295805,0.08568648,0.04778206,-0.02873633,-0.08228741,-0.05312355,-0.01190049,0.03444494,0.02780486,0.00998567,-0.01507442,0.00282001,-0.00006297,0.0223923,-0.00686691,0.07778878,0.01969485,-0.02814087],"last_embed":{"hash":"bormv3","tokens":463}}},"text":null,"length":0,"last_read":{"hash":"bormv3","at":1753423476251},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>","lines":[26,58],"size":4430,"outlinks":[{"title":"_**Make Lotto Wheels, Systems: Manually, or in Lotto Wheeling Software**_","target":"https://saliu.com/lottowheel.html","line":24},{"title":"Play a powerful lotto strategy with 12-number combinations in lotto-6 games worldwide.","target":"https://saliu.com/HLINE.gif","line":32}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1067716,-0.04008266,-0.00802368,0.0079993,-0.05372126,0.03088327,0.00288674,0.0126465,0.04590334,-0.01131424,0.03494421,0.03919462,0.0530254,-0.00311614,-0.03123277,-0.01957425,-0.00868025,-0.02589299,-0.05389214,-0.0018289,0.04030432,-0.05429145,-0.03921646,-0.0649657,0.04030536,0.03238589,-0.05279566,-0.07815241,-0.05270458,-0.24302131,0.02610828,0.02797928,0.05655578,-0.08897396,-0.09297623,0.01092771,-0.05117015,0.04299463,-0.05864843,0.04982051,0.01728283,0.01820207,0.00314401,-0.02488552,0.01122938,-0.03762981,-0.00748989,0.01941863,0.03852635,0.00258839,-0.03640754,0.00605474,-0.0173175,0.06635958,0.06669225,0.0372706,0.03063324,0.06068895,0.02083586,0.04039883,0.02369077,0.06107482,-0.18507466,0.0501323,-0.01799387,0.04700657,0.00285344,-0.02094725,0.01137653,0.07523306,0.05330059,0.04492663,-0.04184169,0.03117363,0.07306515,-0.04425173,-0.06462361,-0.02732475,-0.01745353,0.02491795,-0.0417023,-0.01162606,-0.02420334,-0.02281269,-0.00809977,0.03580031,0.04957971,0.04579607,0.06630049,-0.03474459,0.04269757,0.06910645,0.03225746,0.02071818,0.03241493,0.00148627,0.07980075,-0.02037201,-0.01261552,0.09733576,0.02308667,-0.0169731,0.00407522,0.00458824,0.02603066,-0.04208922,-0.03381859,-0.04582765,-0.01353605,0.04611565,0.07910335,0.03776313,0.06021823,-0.04830354,-0.06081374,0.00570537,-0.01410758,0.02137202,0.00042907,-0.00149291,-0.03072332,0.01103571,0.00837526,-0.0077563,-0.00859424,-0.01667894,0.01703612,0.08006879,0.01414699,0.00450455,0.02561553,-0.01041096,-0.14808841,-0.04381408,-0.05007352,-0.0191827,-0.03100727,-0.0341498,-0.01104647,0.03583502,-0.03260092,-0.06273473,0.04167835,-0.07818034,-0.00885892,0.05308502,0.01710567,-0.02036422,0.0129087,0.02061355,0.00095333,-0.02008785,-0.06029664,-0.04773148,0.00316161,-0.01148443,0.08973266,0.07390721,-0.06564288,-0.00464987,-0.03076418,-0.03249793,-0.01972031,0.09992922,0.01915164,-0.11048944,-0.00992399,0.03744356,-0.01700149,-0.09264086,-0.04152349,-0.01251427,-0.03115693,0.0490043,0.07918331,-0.00076241,-0.07977429,-0.02583902,-0.02933428,-0.00544335,-0.01782415,-0.01332562,-0.00952581,0.01489195,-0.04399825,-0.09132014,0.03660213,-0.02991119,0.01847093,0.03931745,-0.04498778,0.01171285,-0.08232792,0.0174476,-0.03602073,-0.00943701,-0.02409521,-0.06429847,0.08043172,-0.0203485,-0.05094909,-0.03928323,0.01696234,0.00852239,-0.05109774,0.04129928,-0.00935006,-0.05890606,0.08406638,0.0700528,-0.0273947,0.01552313,0.04890214,0.05800191,-0.04633641,0.03708458,0.00174028,0.01156947,0.01780957,-0.02272712,0.03940808,0.00636225,-0.04318457,-0.18320028,-0.02157689,-0.04363194,0.00357244,0.0084689,-0.02285756,0.02119634,-0.04121846,0.02281587,0.05669295,0.06446335,-0.05912227,0.0067918,0.05612079,0.00790682,-0.03245425,-0.08666594,-0.04813625,-0.0637084,0.05740236,0.00383405,-0.01199836,-0.03983654,-0.07886285,-0.0011394,-0.01923132,0.13276079,0.03028821,0.02385039,0.00145187,0.07255462,0.01974005,-0.00855897,-0.01919106,0.01421257,0.01959936,-0.02503911,-0.04214904,-0.03667686,0.01470713,-0.0709811,0.0218189,-0.00203263,-0.12546885,-0.05383833,0.03529466,-0.03075065,0.07060987,0.02462594,0.06052737,0.04998152,-0.00422923,0.05723206,0.04145708,0.06954344,-0.03514123,-0.07950842,-0.00029848,0.00720971,0.05739629,-0.02701306,-0.02147721,0.05536073,-0.00309197,0.06522959,0.0272939,-0.03378862,-0.00483301,0.02392932,-0.03140729,-0.02070349,0.09334312,-0.00433188,-0.00027364,-0.01338341,0.00661184,0.05354656,-0.05020331,0.0086773,-0.01031765,0.00947256,-0.07293025,0.03594076,0.0839022,0.04203581,-0.00035062,0.03572129,0.05277053,-0.00803064,0.0295209,0.00463965,0.01826578,-0.0463993,0.06992889,0.02692399,0.02525217,-0.22854735,0.02698798,-0.0157807,0.08689515,0.01002897,0.01183445,0.07425658,0.02656141,0.02434796,-0.01674793,0.07302886,0.06294776,0.03336403,-0.07598177,0.07813304,-0.02863907,-0.04101299,-0.05303766,0.05751143,0.03430221,0.06295311,0.05520155,0.22541124,-0.00355071,0.02692134,0.02388454,0.01769208,0.01794257,0.01101771,0.02554854,-0.03175787,-0.01020847,0.03383804,-0.05893894,-0.05779726,-0.04599384,-0.01012719,0.01094368,-0.01998019,0.00348225,-0.0597537,0.00706563,-0.00360372,-0.00773297,0.18473038,0.02983155,-0.03115635,-0.1158339,0.08404437,0.0469293,-0.0355126,-0.08403888,-0.05192433,-0.0135252,0.03676643,0.03015295,0.0095089,-0.01356983,-0.00096149,-0.0018111,0.02496368,-0.00386328,0.07917831,0.02037308,-0.0315226],"last_embed":{"hash":"1ktgkvd","tokens":472}}},"text":null,"length":0,"last_read":{"hash":"1ktgkvd","at":1753423476405},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>#{1}","lines":[28,48],"size":2557,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>#{3}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0840646,-0.03300902,-0.0100956,-0.00687138,-0.06739527,0.0430871,0.02211384,0.01000805,0.05751117,-0.00049362,0.0121147,0.02088763,0.01540266,-0.00520382,-0.0382419,-0.01721663,-0.01348622,-0.02583665,-0.06924338,0.00435827,0.02379451,-0.06016193,-0.0560002,-0.07265743,0.03281978,0.01400579,-0.0433561,-0.07416907,-0.04952382,-0.23789713,0.0602208,0.03650089,0.02599581,-0.06532224,-0.08577659,-0.00294455,-0.04581796,0.04069291,-0.03726214,0.02605686,0.01237257,0.03725407,0.00734559,-0.0245479,0.03249307,-0.05850982,-0.00587644,0.00345654,0.05272608,0.01112087,-0.02664254,0.02170394,-0.01796806,0.03881506,0.06548041,0.0456724,0.05046306,0.08894163,0.02615638,0.03190032,0.01587993,0.06228315,-0.19759649,0.02994138,-0.02994707,0.00920025,-0.01470859,-0.00881155,-0.02286456,0.0850678,0.07846511,0.01899159,0.009498,0.02970859,0.0719941,-0.03253278,-0.06821642,-0.03380531,-0.02883982,0.03380122,-0.03807121,0.00651667,-0.02737297,-0.0116123,0.00671346,0.04695105,0.0248562,0.04126286,0.05778004,-0.03825366,0.03094315,0.04834439,0.00164429,0.02269135,0.01322415,0.01041562,0.07230826,-0.02110871,-0.00617305,0.08778755,0.04423058,-0.00369935,-0.00442006,0.01112589,0.01941148,-0.04105831,-0.02945176,-0.04268906,-0.01126773,0.06011218,0.06912744,0.04331438,0.07891627,-0.03604465,-0.06574092,0.02417742,-0.02128019,0.01446845,0.00654079,-0.00664253,-0.04233723,0.02243232,-0.01489098,-0.0090944,-0.03176298,-0.01945356,0.06038801,0.07425176,0.00531492,0.00505229,0.01470047,-0.00607736,-0.13538682,-0.03838615,-0.06376424,-0.01176417,-0.03377488,-0.01992358,-0.00846304,0.01568881,-0.02216934,-0.0680839,0.0352301,-0.09775507,-0.02351985,0.04476019,0.0287414,0.00161048,0.01941715,0.02677965,-0.00854149,-0.01834795,-0.04205225,-0.05822526,-0.02551696,0.00021492,0.05742624,0.10375188,-0.06192839,-0.00422922,-0.03746352,-0.05476419,-0.01256798,0.07980319,-0.00981482,-0.11248076,-0.01164353,0.02247794,-0.01359215,-0.11809558,-0.03585203,-0.00498752,-0.05616224,0.02853535,0.08641689,-0.00586342,-0.0634397,-0.02779682,-0.0377075,-0.00921107,-0.02152935,-0.01740876,-0.02509571,0.00760764,-0.02610598,-0.04930122,0.02675515,-0.06047631,0.02110216,0.04059303,-0.03488277,0.03394821,-0.08959465,0.04239109,-0.03808872,-0.00457035,-0.02616817,-0.05822558,0.0764931,-0.02460778,-0.02559056,-0.01562561,-0.00394943,0.00760406,-0.03659171,0.03089907,-0.02560379,-0.06797809,0.05318807,0.0643188,-0.01976318,0.02914282,0.04520059,0.08808357,-0.08074852,0.04071832,0.01107587,0.01167828,0.02046792,-0.0279905,0.02655411,-0.00728185,-0.02343272,-0.17835085,-0.04234126,-0.05141729,0.00704108,-0.00609487,0.00442733,0.02404273,-0.03625657,0.05843808,0.05008084,0.1039842,-0.06852309,0.00422955,0.06784561,0.00419791,-0.0136553,-0.09184649,-0.02012638,-0.03320486,0.07569021,0.01405815,-0.01075867,-0.01980815,-0.0835297,0.01979909,-0.01309477,0.14915515,0.05801378,-0.01094666,-0.02223385,0.070537,-0.00073725,0.00745076,-0.02846519,0.01016877,0.02710135,0.0002453,-0.04087018,-0.08091892,0.00306464,-0.06123558,0.01386291,0.00405616,-0.11059333,-0.03963156,0.01275503,-0.02867291,0.06939574,0.01185116,0.07566029,0.04377712,0.03589306,0.04393565,0.01893613,0.06888387,-0.02236712,-0.06202391,-0.01489229,-0.00617109,0.0687917,-0.04046293,-0.05301548,0.06044445,0.00904036,0.05355116,0.02727267,-0.00280816,-0.02694037,0.01330535,-0.02723043,-0.02227665,0.06007238,0.01679699,0.03297596,0.00572796,0.00129739,0.09740869,-0.03741485,0.04227472,0.01936402,-0.00418684,-0.05659023,0.01990476,0.07699329,0.05170918,-0.00071095,0.06611073,0.04885772,-0.02076789,0.01277249,0.00200483,0.02245573,-0.03364739,0.05853728,0.01051348,0.02964298,-0.2326781,0.0305461,-0.00626881,0.05797995,-0.01938813,-0.00152593,0.09089491,0.01397328,0.02297387,-0.02670238,0.09958887,0.067027,0.02746246,-0.08274224,0.09642027,-0.0271507,-0.06090706,-0.01380264,0.05930923,0.03044033,0.05333446,0.07058217,0.22389071,-0.01129165,0.0056256,0.01281845,0.01672442,0.01178535,0.01092843,0.0370315,-0.02284891,0.00841008,0.03637137,-0.04629966,-0.06242569,-0.02716274,-0.0247876,0.00250869,-0.03270463,0.04360364,-0.07425817,0.02732471,0.00228842,-0.00591803,0.15292905,0.04227845,-0.00630599,-0.11853661,0.07525726,0.05600052,-0.02873749,-0.0912283,-0.04912211,-0.03827073,0.01821875,0.02462041,-0.01112434,-0.02085359,0.00236614,0.01503422,0.01127024,-0.01535397,0.04653545,0.0101733,-0.02795093],"last_embed":{"hash":"1r9tiq4","tokens":458}}},"text":null,"length":0,"last_read":{"hash":"1r9tiq4","at":1753423476545},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>I. Software Author, Lottery Winner</u>#{3}","lines":[51,58],"size":1709,"outlinks":[{"title":"Play a powerful lotto strategy with 12-number combinations in lotto-6 games worldwide.","target":"https://saliu.com/HLINE.gif","line":7}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10903896,-0.02771606,-0.00193146,-0.03551241,-0.0370851,0.05716198,-0.00488032,0.0102619,0.07400002,-0.01677583,0.03727754,0.0413815,0.03052123,-0.00644036,-0.01144281,0.00754086,0.00449661,-0.05069821,-0.05216217,0.00230212,0.03647104,-0.04293236,-0.04652858,-0.08187272,0.03056196,0.00105051,-0.04444973,-0.05458053,-0.06138162,-0.23693781,0.03880776,0.02611784,0.05479651,-0.05595108,-0.07884877,-0.01542309,-0.04868603,0.05215791,-0.0649137,0.04110353,0.01209813,-0.00648162,-0.00191356,-0.01425846,0.03995524,-0.06403932,-0.0014013,0.00837369,0.05413701,0.02519768,-0.03162228,-0.0032743,0.00777713,0.03697111,0.06808248,0.04307334,0.0240853,0.07498747,0.0474102,0.03006861,0.02265044,0.07378422,-0.18840566,0.02520862,-0.00476034,0.02003511,-0.00954267,-0.01771975,-0.00139733,0.06678814,0.06870635,0.04226261,0.00550073,0.04748731,0.06365349,-0.04150162,-0.04636056,-0.02673814,-0.03723202,0.0399995,-0.02875178,-0.03577371,-0.02195927,-0.0156332,-0.01429133,0.04291861,0.00885241,0.0593846,0.06579288,-0.04240312,0.01869691,0.05221789,0.02343343,0.01518164,0.0114986,0.02354676,0.06124121,-0.01567106,0.0009684,0.08823776,0.03204733,-0.01232479,0.00902877,-0.00248656,0.04041578,-0.0295851,-0.03099622,-0.03898293,-0.02113663,0.03311794,0.07240117,0.04410053,0.08915189,-0.01726009,-0.06659754,0.00750295,-0.01432678,0.01856165,0.0053619,0.00158646,-0.04119248,0.02976638,-0.0054413,-0.02127931,-0.02058588,-0.01455307,0.04347205,0.0916899,-0.00130102,0.00130251,0.02954519,-0.00491169,-0.13340752,-0.04581901,-0.03050276,0.00042123,-0.02402967,-0.0419788,0.01263005,-0.00307565,-0.042251,-0.05585233,0.03707202,-0.07915268,-0.01722342,0.03609578,0.04174299,0.00421411,0.01997681,0.0197232,-0.00134633,-0.0305984,-0.04711384,-0.05406237,0.00348122,-0.00106844,0.07758074,0.10188806,-0.0539158,-0.00252323,-0.03330063,-0.03270959,-0.00481181,0.05719088,0.01112625,-0.1334442,-0.03355541,0.02578293,0.00118817,-0.08513245,-0.02690461,-0.014063,-0.05685079,0.03283664,0.0724831,-0.00840731,-0.06520996,-0.02547542,-0.05144822,-0.01938074,0.00072499,-0.05197844,-0.04497281,0.02340168,-0.02044753,-0.0865939,0.04837075,-0.03374754,0.03358437,0.0479044,-0.01544624,0.01470071,-0.07413476,0.02686253,-0.01257331,-0.03164724,-0.01582703,-0.04885602,0.09214935,-0.02495139,-0.05104873,-0.02718617,0.01220617,0.02741791,-0.04636563,0.03811198,-0.02549314,-0.06309673,0.08299299,0.08261679,-0.03669462,0.01688543,0.06936569,0.08547471,-0.07355455,0.06080501,-0.00024994,-0.00210297,0.02348129,-0.01224728,0.02119722,-0.00215854,-0.04313997,-0.19311835,-0.02489049,-0.046527,0.00581561,0.00402034,-0.02519727,0.00038201,-0.03796603,0.05620697,0.04749461,0.08485354,-0.0751221,0.02194996,0.04100452,-0.00670578,-0.02103273,-0.07161405,-0.04383561,-0.04162077,0.07057932,-0.00330363,0.01360661,-0.03554541,-0.09575634,0.00012001,-0.02770747,0.14622477,0.0517449,0.01964729,-0.00606295,0.064025,-0.00521522,-0.00165687,-0.03416152,-0.00497355,0.01402974,0.01179592,0.00566369,-0.08728728,0.02135244,-0.1052439,0.00431195,0.00795712,-0.12311813,-0.02497209,0.00770764,-0.01661027,0.03057908,-0.00547724,0.06659041,0.06705597,0.01076373,0.05229919,0.01261264,0.06121988,-0.03780668,-0.04668414,-0.01140689,0.03012249,0.04368845,-0.00957517,-0.03516789,0.0433422,-0.01782124,0.02963638,0.0049015,0.00245939,0.00155432,0.00189447,-0.03634897,-0.00995861,0.07898234,0.02895779,0.01766393,0.0207609,0.0081021,0.07376712,-0.04719817,0.02218427,-0.00659961,0.00859235,-0.06881864,0.03170098,0.08699216,0.03480319,0.00805465,0.06627736,0.0689939,-0.01366373,0.00886956,0.00687992,-0.00061362,-0.03394828,0.02959244,0.01597075,0.03910569,-0.24426948,0.02010788,-0.00617844,0.07703772,-0.00701826,0.00057135,0.06669,0.02866576,0.03996303,0.01477089,0.03901426,0.0578494,0.02972594,-0.09287026,0.0409297,-0.04622182,-0.04471397,-0.00989846,0.05179528,0.01457887,0.05281847,0.0528486,0.24241969,-0.01085179,-0.01280872,0.01089827,0.0175153,0.0195261,-0.00457718,-0.00673029,-0.03368608,0.0093804,0.02266178,-0.05290079,-0.05036062,-0.03936217,-0.03735397,0.03437719,-0.04224239,0.02164637,-0.07568375,0.01917606,-0.02169991,-0.00068764,0.157832,0.04096433,-0.00210304,-0.10463837,0.083394,0.07574569,-0.06295113,-0.08858902,-0.04148712,-0.01728949,0.01382909,0.04446322,-0.02115883,0.00838462,-0.00796256,-0.01688557,0.05299475,0.00534817,0.06974733,0.01348751,-0.0233899],"last_embed":{"hash":"j594fj","tokens":495}}},"text":null,"length":0,"last_read":{"hash":"j594fj","at":1753423476704},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>","lines":[59,97],"size":5757,"outlinks":[{"title":"_**Randomness, Degree of Randomness, Degree of Certainty, True Random Numbers Generators**_","target":"https://saliu.com/bbs/messages/683.html","line":15},{"title":"_**BASIC source code software to generate true random & unique numbers**_","target":"https://saliu.com/random-numbers.html","line":16},{"title":"The best-ever lotto player created a strategy that hit the lotto jackpot in Pennsylvania Lottery.","target":"https://saliu.com/HLINE.gif","line":38}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11093827,-0.02645746,-0.00118829,-0.03553175,-0.03290635,0.05900922,-0.00548279,0.01036436,0.07286783,-0.017449,0.03502652,0.0417155,0.02990054,-0.00520907,-0.00938155,0.00572476,0.00352464,-0.04704611,-0.05458378,0.00313203,0.03483165,-0.03937791,-0.04660991,-0.07646775,0.03203411,0.00419845,-0.04334993,-0.05530149,-0.06040715,-0.239241,0.03912023,0.0259984,0.0506402,-0.05494967,-0.08131605,-0.01388643,-0.04956546,0.05156467,-0.06109653,0.04020684,0.01352296,-0.00539245,-0.00436803,-0.01523546,0.03675802,-0.06537015,-0.00157446,0.00925618,0.05315249,0.02706354,-0.02979132,-0.00342405,0.00846008,0.03427728,0.06898526,0.04544999,0.02643162,0.07498965,0.04728671,0.02970053,0.02098025,0.07192638,-0.19203962,0.02666343,-0.00594732,0.01857146,-0.00815667,-0.01382126,-0.00152426,0.06612463,0.06996084,0.04154955,0.00712078,0.04537411,0.06359796,-0.04109016,-0.04575399,-0.0299408,-0.04036551,0.03990699,-0.02828743,-0.03357257,-0.02179287,-0.01902334,-0.01170683,0.04354333,0.00614822,0.05849922,0.06577511,-0.04320818,0.02140227,0.04706918,0.02438955,0.01551597,0.01004365,0.02765567,0.06066918,-0.01460428,0.00024071,0.08666097,0.03345832,-0.00950095,0.01150171,-0.00271202,0.04019809,-0.02986923,-0.0299618,-0.04083494,-0.01634067,0.03360941,0.07197595,0.04507568,0.08677904,-0.01539672,-0.06523135,0.00866431,-0.01629178,0.02085418,0.00169033,0.00218007,-0.03900947,0.02689888,-0.00628516,-0.01942422,-0.02419728,-0.02041706,0.0437309,0.09125406,-0.00235181,0.0015408,0.03015314,-0.00494634,-0.13403915,-0.04827285,-0.03265034,0.00168315,-0.02587506,-0.04361242,0.01228291,-0.00089873,-0.0440615,-0.05851817,0.03568621,-0.0796039,-0.01326589,0.03796322,0.03945352,0.0043196,0.02088684,0.02196228,-0.00475672,-0.03362054,-0.04802297,-0.05591219,0.00615194,-0.00467969,0.07372726,0.1035694,-0.05348995,-0.00278868,-0.03720037,-0.03052382,-0.00593376,0.05526458,0.00675828,-0.13618617,-0.03386723,0.02313486,0.00249345,-0.08291887,-0.0273519,-0.01508699,-0.05368967,0.03380059,0.07663206,-0.00906554,-0.06270778,-0.02730504,-0.05243755,-0.01994728,0.00219992,-0.05309382,-0.04526152,0.02444446,-0.01819063,-0.08442663,0.04953742,-0.03704617,0.03416147,0.04777925,-0.01635261,0.01690396,-0.07610011,0.03027632,-0.01434735,-0.02860422,-0.01768786,-0.04851587,0.09336448,-0.02315864,-0.05193709,-0.02647821,0.01114026,0.02701261,-0.04189564,0.0389371,-0.02643135,-0.06144496,0.08219055,0.08347373,-0.03785956,0.01864013,0.07195885,0.08699466,-0.07032243,0.06049827,-0.00371061,0.0008664,0.01921611,-0.00936802,0.02169152,-0.00320491,-0.04276204,-0.19314249,-0.02248295,-0.04674027,0.00521373,0.00041701,-0.02711984,0.00142536,-0.03884986,0.05453257,0.04597959,0.08557624,-0.07446586,0.02258455,0.04488865,-0.00596312,-0.01969193,-0.06875968,-0.04343981,-0.04044529,0.07353064,-0.00400978,0.01356088,-0.03663324,-0.09333047,0.00173457,-0.02659138,0.14527304,0.05218693,0.0184522,-0.0084638,0.06118834,-0.00659713,-0.00103113,-0.03610882,-0.00631248,0.01499342,0.01704194,0.00188879,-0.08750027,0.01829657,-0.10567684,0.00475691,0.00482596,-0.12240843,-0.02627469,0.00829005,-0.01544411,0.03440371,-0.00647148,0.06530797,0.07229234,0.01294574,0.0554793,0.01070763,0.06194697,-0.03715253,-0.04948601,-0.01266641,0.03198361,0.04450576,-0.00885287,-0.03228646,0.04363426,-0.01801691,0.03178394,0.00358101,0.00295731,0.00311833,-0.0007667,-0.03630339,-0.01172167,0.07393856,0.02671541,0.01815888,0.0228269,0.00982489,0.07479293,-0.04903593,0.02214373,-0.00743108,0.01022937,-0.0684801,0.03349055,0.0842135,0.03428391,0.01158585,0.0682194,0.06572972,-0.01485593,0.00794905,0.00478656,-0.00308279,-0.03603452,0.03220266,0.0158984,0.0403658,-0.24639878,0.02253153,-0.00475054,0.07353214,-0.0093677,0.00278952,0.06553126,0.02460252,0.03950373,0.01332417,0.04039972,0.05801458,0.03124563,-0.09158305,0.04302344,-0.04699356,-0.04253053,-0.00728088,0.05169066,0.01467391,0.05191276,0.05484555,0.2417942,-0.01259331,-0.0134837,0.01268118,0.01519763,0.01852536,-0.00484541,-0.00886803,-0.03419416,0.00948879,0.02091178,-0.05086479,-0.0515159,-0.03807102,-0.03793583,0.03360483,-0.0420737,0.02401144,-0.07542056,0.01933629,-0.01958969,-0.00018069,0.15842801,0.03863423,-0.00114817,-0.10220444,0.08263779,0.07526673,-0.06155623,-0.08881982,-0.0419074,-0.01455505,0.01468222,0.04114426,-0.02229266,0.00861268,-0.0081866,-0.01694861,0.05458583,0.00709789,0.06930002,0.0138632,-0.02200308],"last_embed":{"hash":"1l73ow6","tokens":479}}},"text":null,"length":0,"last_read":{"hash":"1l73ow6","at":1753423476890},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#{1}","lines":[61,72],"size":2021,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#Now, the big _lotto strategy_ that did hit the _jackpot_.": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0590287,-0.03880609,-0.01174194,-0.03091462,-0.05887007,0.05334669,0.00641521,-0.00014784,0.06094594,-0.0156096,0.01714091,0.03519303,0.05418822,0.00147512,-0.02539293,-0.00569592,0.00045183,-0.04980208,-0.06742035,-0.00215951,0.04567284,-0.05997951,-0.04842607,-0.072681,0.02014435,0.00820051,-0.03088834,-0.07126113,-0.05277529,-0.24350625,0.0303931,0.06185514,0.07286098,-0.05496614,-0.09862014,-0.0129156,-0.07027916,0.06840368,-0.0439386,0.03806428,0.00580839,0.01277248,0.00932472,-0.00504476,0.01283886,-0.05767247,-0.01175109,0.01019962,0.07917699,0.01597454,-0.02863473,-0.02136675,-0.00910604,0.05006162,0.07146857,0.02862028,0.04175581,0.08096296,0.04773131,0.03206698,0.03204064,0.09010226,-0.19467875,0.02492557,-0.02366612,0.0334354,-0.02295052,-0.01935631,-0.0045833,0.10272188,0.06041349,0.03236806,-0.00843471,0.03700205,0.05786605,-0.03695924,-0.08451368,-0.02649768,-0.03506209,0.04847684,-0.02071909,-0.01175443,-0.02199326,-0.02157708,-0.00509284,0.01773298,0.03223724,0.03687669,0.04329511,-0.02980287,0.04444059,0.07193855,0.01458776,-0.00255644,0.0160394,-0.00102859,0.05900718,-0.05263947,-0.03645943,0.07424845,0.03297723,-0.00861118,0.00374016,-0.00414143,0.01846411,-0.02204806,-0.02880503,-0.05622469,-0.02808821,0.04399764,0.08190165,0.04750677,0.05912659,-0.03678652,-0.06696312,0.03334241,-0.00274723,0.01687114,-0.01195345,0.01801301,-0.02473341,-0.00118383,0.01583138,-0.02378978,-0.01681153,-0.00840047,0.03255904,0.10367963,0.00651665,-0.00102127,-0.01397915,-0.01601415,-0.1158345,-0.03291404,-0.04429112,-0.00759667,-0.0125683,-0.02933165,0.0148407,0.00594302,-0.03862211,-0.05968382,0.06982917,-0.06385296,0.00743078,0.05985373,0.03609043,0.00257425,0.02852223,0.02652916,-0.00823491,-0.00725247,-0.07233181,-0.05166219,-0.02689266,-0.01112717,0.0842438,0.08430148,-0.06173677,-0.00623625,-0.04414913,-0.0390922,0.00338354,0.06995999,-0.0013378,-0.08755623,-0.02511105,0.01349726,-0.01520933,-0.10546058,-0.04351188,-0.01867333,-0.05721698,0.04120909,0.09140153,0.00571788,-0.05644632,-0.00654135,-0.02631956,0.00644845,0.01782894,-0.01996792,-0.02362558,0.00564369,-0.01885959,-0.06790537,0.02461131,-0.01959323,0.0314684,0.03324086,-0.03478876,0.02408364,-0.07349733,0.03116695,-0.01136691,-0.02133109,-0.0023336,-0.04621223,0.07524286,-0.00128063,0.00038049,-0.0326208,0.01119601,0.01199305,-0.05901968,0.05929445,-0.02550776,-0.06997449,0.07208248,0.06818063,-0.04962914,0.02720784,0.03746906,0.08187396,-0.07160527,0.04928502,0.0146571,0.00628914,0.02820934,-0.01407936,0.04069893,-0.01149122,-0.02985016,-0.17788523,-0.05187079,-0.03074577,0.01362988,-0.00943308,-0.01742691,0.00957049,-0.02069219,0.0416595,0.07087254,0.07084263,-0.07943073,-0.01385324,0.03014471,-0.03613674,-0.02802408,-0.11758941,-0.04315336,-0.04410554,0.06664233,-0.00009168,-0.00254635,-0.03531023,-0.08274933,0.01396037,-0.01374988,0.15244585,0.03707443,0.01150792,-0.01224435,0.08114549,0.0063653,0.01067237,-0.00462165,0.0296749,0.00584079,-0.01163418,-0.05264825,-0.06150803,0.01325566,-0.09092947,-0.00531559,-0.02129413,-0.12934634,-0.02193521,0.01316331,-0.03435496,0.03138629,-0.00614379,0.07433435,0.0434618,0.02205027,0.02842426,0.01684263,0.07097759,-0.02541547,-0.07469373,-0.0202471,0.00513222,0.05015492,-0.01064093,-0.03291598,0.05551462,-0.02771115,0.04695357,0.03289456,-0.02363569,-0.01023783,0.00842127,-0.01994623,-0.01432844,0.08064635,0.02528691,0.02230225,-0.00156056,-0.00866406,0.05085825,-0.04101454,0.03732256,-0.00387328,0.0165497,-0.06688609,0.0128558,0.08143687,0.03691679,-0.03438383,0.05930968,0.06410104,-0.00078419,0.00452093,-0.02814246,0.02698035,-0.02209202,0.03340368,0.02577314,0.03458247,-0.23674905,0.04190806,-0.01365633,0.07652682,0.00389319,-0.00217204,0.07840028,0.01032809,0.03267007,0.00153454,0.06349964,0.07350031,0.02332793,-0.06689218,0.04733445,-0.03281137,-0.04546387,-0.01534173,0.05488234,0.01905694,0.06792653,0.05109465,0.23836082,-0.00538411,0.0126524,0.01666809,-0.00173014,0.04818402,0.00266505,0.0214674,-0.04900038,-0.00047422,0.05181744,-0.05407367,-0.05835782,-0.03760286,-0.01368079,0.01666223,-0.02914688,0.04327445,-0.06311276,0.01736412,-0.03558847,0.00015605,0.16870612,0.02605429,-0.01367489,-0.11478409,0.06194662,0.05990904,-0.04908558,-0.08147285,-0.04430513,-0.01633949,0.02294571,0.0346753,-0.00159477,0.01085926,-0.00344015,-0.00381735,0.01650935,0.00252399,0.06225503,0.02330318,-0.04575767],"last_embed":{"hash":"lpodu2","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"lpodu2","at":1753423477054},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#Now, the big _lotto strategy_ that did hit the _jackpot_.","lines":[76,97],"size":3416,"outlinks":[{"title":"The best-ever lotto player created a strategy that hit the lotto jackpot in Pennsylvania Lottery.","target":"https://saliu.com/HLINE.gif","line":21}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#Now, the big _lotto strategy_ that did hit the _jackpot_.#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05977022,-0.03858748,-0.01122177,-0.02815915,-0.05976437,0.0524179,0.00519273,-0.00001006,0.06030426,-0.01382722,0.01793158,0.03513913,0.0560499,0.00212724,-0.02484274,-0.00637129,-0.00118789,-0.0503066,-0.06755786,-0.00214282,0.04678084,-0.05963537,-0.04978686,-0.07476534,0.02154696,0.00811721,-0.03098455,-0.07244689,-0.05338641,-0.24466157,0.03051816,0.06050955,0.07162052,-0.05456502,-0.09858395,-0.01224883,-0.07051506,0.06758738,-0.0475257,0.03822944,0.00422588,0.01358274,0.00841997,-0.00664546,0.01244414,-0.05943755,-0.01193127,0.01021083,0.07965034,0.01757287,-0.02776508,-0.02006009,-0.00823707,0.05257425,0.06958844,0.02962318,0.0405471,0.08098546,0.04839137,0.03319447,0.03082968,0.09132878,-0.19461778,0.02484748,-0.02399841,0.03400357,-0.02231053,-0.01952739,-0.00442957,0.10267104,0.06264068,0.03369671,-0.00868406,0.03666987,0.058998,-0.03712856,-0.08401817,-0.02675054,-0.03601172,0.049936,-0.02244518,-0.01062512,-0.02143225,-0.02228456,-0.00617729,0.01703762,0.03083068,0.03689555,0.0436255,-0.02961337,0.04342916,0.07283295,0.0143025,-0.00227843,0.01623992,-0.00005745,0.05855504,-0.05359882,-0.0340942,0.07495289,0.03445869,-0.00808589,0.00437527,-0.00413014,0.01977921,-0.02237858,-0.02906127,-0.05563191,-0.02804702,0.04326419,0.08161006,0.04645475,0.06007665,-0.03666946,-0.06769855,0.03355225,-0.00452754,0.01780438,-0.01142029,0.01914874,-0.02458964,0.00083598,0.01547946,-0.02403976,-0.01740372,-0.00884474,0.03274028,0.10428948,0.00629432,0.00025058,-0.0127192,-0.01467568,-0.11438691,-0.03347119,-0.04293726,-0.00949679,-0.01225653,-0.02769071,0.01437065,0.00631339,-0.03683967,-0.05713362,0.06973291,-0.06371261,0.00612524,0.06040524,0.03597866,0.00141322,0.0290382,0.02658217,-0.00666331,-0.00831214,-0.07273058,-0.05208145,-0.02755223,-0.01221966,0.08631364,0.08397444,-0.06063386,-0.00480317,-0.04490498,-0.03866995,0.00301546,0.07057069,-0.00205261,-0.08728956,-0.02412544,0.01498559,-0.01648916,-0.1039299,-0.04415364,-0.01807375,-0.05574821,0.039786,0.09052698,0.00728282,-0.05894479,-0.00764763,-0.02627991,0.00576116,0.01713469,-0.02070597,-0.02319597,0.00450586,-0.0183776,-0.06791113,0.02451913,-0.02067422,0.03043034,0.03507685,-0.03626394,0.02238477,-0.0727008,0.03164798,-0.0120992,-0.02207269,-0.00200445,-0.04501472,0.0751102,-0.00203522,0.00087533,-0.03337668,0.01254599,0.01328797,-0.05852668,0.05850958,-0.02436581,-0.07179216,0.07025404,0.06857722,-0.05144036,0.02608844,0.03615402,0.08131413,-0.0692947,0.04946858,0.01314955,0.00760841,0.02851703,-0.01336014,0.03799212,-0.01229891,-0.03009223,-0.17823374,-0.05248204,-0.03213615,0.01495142,-0.00821029,-0.01781974,0.00699132,-0.02040238,0.0404246,0.06808001,0.07067131,-0.07905845,-0.01251803,0.03073044,-0.03601443,-0.02684156,-0.11792149,-0.04156502,-0.04313475,0.06561303,0.00121712,-0.00273971,-0.03368754,-0.08497176,0.01297574,-0.01452952,0.15127356,0.03606184,0.01335157,-0.01371718,0.08103029,0.00542648,0.0084488,-0.00321646,0.02976792,0.00762447,-0.01218639,-0.05207392,-0.0616213,0.01222739,-0.09204733,-0.00465323,-0.0201793,-0.13020726,-0.02014201,0.01235329,-0.0351417,0.03252994,-0.00436298,0.07612782,0.04248042,0.02156817,0.03011023,0.01809129,0.07001284,-0.02442982,-0.07410422,-0.01963555,0.00508959,0.04983529,-0.00791515,-0.03126714,0.05544018,-0.02775457,0.04832424,0.03330872,-0.02279096,-0.01129814,0.00927177,-0.01838621,-0.01299283,0.08039032,0.02544396,0.02330742,-0.000477,-0.00738351,0.05031092,-0.04232564,0.03598497,-0.00647877,0.01675811,-0.06578989,0.01338027,0.08351824,0.03532534,-0.03463636,0.05931618,0.06374978,-0.00083653,0.00511723,-0.02816475,0.02753788,-0.02349303,0.0363871,0.02581689,0.03373709,-0.23640896,0.0428322,-0.01372738,0.07564478,0.00479384,-0.00185029,0.07924449,0.01117849,0.03182559,0.00020997,0.06274048,0.07487971,0.02364886,-0.06576036,0.04727837,-0.03441041,-0.04354242,-0.0165667,0.05451541,0.01909486,0.06648268,0.0501229,0.23747317,-0.00426092,0.0129213,0.01633575,-0.00128711,0.04848711,0.00237885,0.01994968,-0.05034043,0.00135194,0.0501342,-0.0553432,-0.05832794,-0.03667778,-0.01396434,0.01549612,-0.02977931,0.04317334,-0.06578586,0.01676567,-0.03565956,0.00150098,0.16770025,0.0264234,-0.01403121,-0.1148779,0.06270259,0.05835686,-0.04952484,-0.08053466,-0.04559607,-0.01702609,0.02276518,0.03627222,-0.00059421,0.01078212,-0.00336198,-0.00602156,0.01590929,0.00049379,0.06338177,0.02208844,-0.04640911],"last_embed":{"hash":"1mky203","tokens":469}}},"text":null,"length":0,"last_read":{"hash":"1mky203","at":1753423477208},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>#Now, the big _lotto strategy_ that did hit the _jackpot_.#{1}","lines":[78,97],"size":3353,"outlinks":[{"title":"The best-ever lotto player created a strategy that hit the lotto jackpot in Pennsylvania Lottery.","target":"https://saliu.com/HLINE.gif","line":19}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07909209,-0.03139535,-0.01024134,-0.03805611,-0.05518125,0.04871112,-0.01119291,-0.0182726,0.05087008,-0.00045033,0.0166678,0.01623963,0.05993653,-0.02020073,0.00076288,0.01001078,0.01001422,-0.0225773,-0.08634599,-0.03177518,0.06662072,-0.04651739,-0.05282201,-0.08228418,0.01042894,0.00203347,-0.05174104,-0.06112812,-0.04966013,-0.22985172,0.0562848,0.03261342,0.04131385,-0.06067515,-0.08433759,-0.02151364,-0.07108771,0.0752811,-0.06455521,0.01485767,0.00215867,0.01132357,0.01792275,0.00678152,0.0011287,-0.06369827,0.00108403,0.01368033,0.06012742,-0.00813352,-0.02142362,-0.0378145,0.01714871,0.05076214,0.03943162,0.04063242,0.02126441,0.09805312,0.02994853,0.05359502,0.02859526,0.09856973,-0.1834854,0.04521675,-0.04143677,0.01276489,-0.0300683,-0.02343086,0.00419694,0.08330129,0.03825384,0.04486237,-0.00894677,0.05488754,0.04949565,-0.05429198,-0.05769715,-0.05150767,-0.00684159,0.03749518,-0.07572871,-0.01314075,-0.00373497,-0.00277488,0.01080818,0.02125574,0.03891971,0.02642403,0.05636132,-0.00809581,0.04790106,0.07115605,-0.01510085,0.01042739,0.01746191,0.00018969,0.05725126,-0.05110392,-0.00916661,0.10049939,0.02558947,-0.0036366,-0.01673689,-0.02652667,0.01782763,-0.05742757,-0.05215905,-0.01903907,-0.03876655,0.0283473,0.10185567,0.02135412,0.06322632,-0.04459063,-0.06417756,0.02912516,-0.0275327,-0.00641846,-0.00051906,-0.0010259,-0.02054617,0.00921921,-0.00431254,-0.02852104,0.00859895,-0.00015941,0.03494595,0.08101237,0.00380643,0.03034653,0.00880299,0.02525152,-0.11960572,-0.02701146,-0.01325116,-0.01471047,-0.01532256,-0.02226508,-0.01838967,0.02016246,-0.03125046,-0.04695999,0.05073147,-0.09719639,0.01576321,0.07550585,0.00415966,0.02141769,-0.02846852,-0.04200752,-0.01313405,-0.00584578,-0.02248839,-0.0364487,0.02458939,0.00857632,0.09413137,0.06444163,-0.06075901,-0.00004349,-0.06077279,-0.01212281,0.00865564,0.08109137,0.00192771,-0.07860119,-0.00819449,0.0567571,-0.04326455,-0.09916688,-0.04979521,0.03083282,-0.03749875,0.0172138,0.0967325,-0.02810701,-0.09670638,-0.00626016,0.01790669,-0.02032939,-0.00236567,-0.01945267,-0.03597866,0.00528399,-0.02938112,-0.0973032,0.05655831,-0.01318676,-0.00381021,0.03228601,-0.04507945,0.0180867,-0.04018932,0.00221089,-0.01756926,-0.01533296,-0.0445842,-0.0394963,0.08690244,-0.06943519,0.02775915,-0.0104525,0.02781874,0.03886738,-0.02514051,0.04864437,-0.02190822,-0.09118617,0.11230702,0.05556981,-0.06267481,0.00858364,0.05788728,0.08825816,-0.08192535,0.06990064,-0.01242666,0.03491348,0.01595907,-0.01859316,0.01564196,0.01507534,-0.00728814,-0.19613233,-0.0012001,-0.003901,0.05105465,0.01584216,-0.0095481,-0.00423462,-0.04177669,0.01849551,0.06663971,0.08203133,-0.06571669,0.00111232,0.05631264,-0.00092015,-0.02656715,-0.12506893,-0.04718493,-0.04121073,0.06557412,0.01315398,-0.01762813,-0.01331575,-0.08323704,0.03181963,-0.03105773,0.11790671,0.00319688,0.00256538,-0.00486257,0.06728032,-0.01215329,-0.00763318,0.00832605,0.03124888,0.02553958,-0.01902225,-0.00755405,-0.03216057,-0.00137767,-0.05276028,0.03948303,-0.01956561,-0.1273406,-0.01272388,-0.01127883,-0.0377317,0.07519215,0.02519419,0.06258226,0.02276783,0.03902445,0.04341483,0.0196326,0.06777811,-0.06211158,-0.08468018,0.0201766,-0.00344283,0.02241979,-0.02688675,-0.07519453,0.06043429,-0.00680919,0.05943318,0.01828427,-0.00695369,-0.0095671,0.02332796,-0.0097837,-0.02326198,0.0536763,-0.02570532,0.06471173,-0.02718059,0.01569241,0.06289012,-0.04041835,0.02397883,-0.01708115,0.01418071,-0.04549558,0.01792594,0.10644495,0.04378864,-0.02635024,0.05479566,0.08029404,0.03430174,0.0039998,-0.00725407,0.01217623,-0.01343664,0.06293034,0.00183174,0.02036029,-0.23888451,0.01135449,-0.03069039,0.08361214,-0.02231162,-0.02977805,0.0869926,0.03078797,0.01284795,0.00167436,0.01005027,0.04767403,0.04364079,-0.09852486,0.04835706,-0.03298123,-0.00264989,-0.02791424,0.04434934,0.00417192,0.07452653,0.03779662,0.22368903,-0.01012103,0.0205992,0.03369847,0.02192768,0.02896709,0.00039729,0.0223284,-0.02593875,0.01673199,0.05173805,-0.04044563,-0.03869727,-0.01880385,-0.01857563,-0.01511849,-0.02111633,0.00518227,-0.0480482,0.03865775,-0.03234658,-0.01536834,0.13739309,0.01454016,-0.00925791,-0.09105115,0.02749656,0.0372118,-0.07247474,-0.05818747,-0.02756732,-0.05115286,0.03554723,0.03319543,0.02512124,0.0087353,-0.00174352,0.00565171,0.03020414,-0.03639553,0.07317443,0.02833442,-0.03921496],"last_embed":{"hash":"1ift0zr","tokens":433}}},"text":null,"length":0,"last_read":{"hash":"1ift0zr","at":1753423477366},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>","lines":[98,150],"size":6800,"outlinks":[{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":30},{"title":"_**Lotto Software, Wheels: 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":32},{"title":"_**Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":33},{"title":"_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_","target":"https://saliu.com/bbs/messages/532.html","line":34},{"title":"_**Lottery, Gambling Experiences: Robbed of Prize Money!**_","target":"https://saliu.com/bbs/messages/535.html","line":35},{"title":"_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_","target":"https://saliu.com/lotto-triples.html","line":37},{"title":"Ion Saliu's Probability Book on mathematics of lotto strategy.","target":"https://saliu.com/probability-book-Saliu.jpg","line":45},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":45},{"title":"The best lotto wheels are also created by the best lottery player in history, Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":48},{"title":"Forums","target":"https://forums.saliu.com/","line":50},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":50},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":50},{"title":"Contents","target":"https://saliu.com/content/index.html","line":50},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":50},{"title":"Home","target":"https://saliu.com/index.htm","line":50},{"title":"Search","target":"https://saliu.com/Search.htm","line":50},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":50},{"title":"Lotto strategy by the best lottery player ever hit the jackpot generating 12-number combinations.","target":"https://saliu.com/HLINE.gif","line":52}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#{1}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08117181,-0.03281859,-0.00992643,-0.03713744,-0.05156152,0.04681774,-0.01701835,-0.01819021,0.05356117,-0.00091161,0.02243086,0.02467486,0.05852218,-0.01765643,0.00129725,0.01159011,0.00800082,-0.02613889,-0.08031667,-0.03140771,0.06206701,-0.04554967,-0.05011136,-0.07936726,0.01587958,0.00118274,-0.05399844,-0.05885286,-0.05285254,-0.22942273,0.05881082,0.03043696,0.04166451,-0.06124069,-0.08583961,-0.02170741,-0.06371608,0.07353587,-0.06854542,0.01674614,0.00355908,0.0159276,0.01570449,0.00473181,0.00303986,-0.06215283,0.00316176,0.0099877,0.04689725,-0.00250212,-0.02434429,-0.03058692,0.01519909,0.05393224,0.03794242,0.03967569,0.01870119,0.09864376,0.02946514,0.05512752,0.02706665,0.09912906,-0.18155357,0.04336258,-0.0369593,0.01475972,-0.02924488,-0.02707192,0.00525125,0.07850181,0.03528339,0.04719104,-0.00672298,0.05558801,0.05170134,-0.05312075,-0.05742655,-0.05113756,-0.00517021,0.03822409,-0.06990255,-0.01380359,-0.00545393,-0.00260523,0.00393137,0.01700431,0.03475413,0.01881285,0.05850664,-0.01182328,0.04350421,0.0726093,-0.01329703,0.01403328,0.02811987,-0.00340766,0.06051027,-0.04422741,-0.00607814,0.09774929,0.02248966,-0.00362058,-0.01602351,-0.02741306,0.0228303,-0.05711002,-0.05632019,-0.02635157,-0.04027311,0.02677118,0.10055128,0.02167879,0.06952207,-0.04251117,-0.06154989,0.01994031,-0.02611061,-0.00443829,0.01023434,-0.0055874,-0.02396316,0.01318251,-0.00558091,-0.02961516,0.00840065,-0.00147547,0.03345595,0.0861859,0.00093538,0.02345409,0.01001323,0.01470028,-0.11937679,-0.02514995,-0.01042646,-0.01424734,-0.01754969,-0.02341835,-0.01304621,0.01549262,-0.02830385,-0.04168942,0.05496469,-0.09679736,0.00739232,0.07659288,0.00967481,0.02614631,-0.02631271,-0.03694084,-0.00733668,-0.00720265,-0.0240602,-0.04228164,0.02307545,0.00628675,0.09466723,0.06339858,-0.06043673,-0.00107115,-0.05717312,-0.01338591,0.01119314,0.07611714,0.00111827,-0.08229426,-0.01003777,0.05883643,-0.04131332,-0.09562174,-0.04883515,0.03077289,-0.03460808,0.02153884,0.08677616,-0.02064894,-0.09810703,-0.00912789,0.01476393,-0.01884931,-0.00174615,-0.01910931,-0.03934503,0.00569029,-0.03497322,-0.09717204,0.05394076,-0.00767762,-0.00363837,0.03604277,-0.04536294,0.01981607,-0.0388421,0.00551437,-0.01461233,-0.0183995,-0.04204604,-0.03652408,0.08890181,-0.07109603,0.01362819,-0.01747438,0.03085907,0.03954325,-0.02897312,0.04560195,-0.01371634,-0.09912141,0.11580176,0.05543167,-0.05889432,0.00436906,0.05655642,0.09120896,-0.08210132,0.07368302,-0.01138465,0.03286533,0.01634446,-0.0230008,0.01407365,0.01729077,-0.0161652,-0.19928694,-0.00595379,-0.0082165,0.05133042,0.02379973,-0.00926728,-0.00816712,-0.04446034,0.01926571,0.0677768,0.0745801,-0.067847,0.00250674,0.06018819,-0.0033411,-0.02529709,-0.119835,-0.04471328,-0.04681492,0.06443245,0.01389615,-0.01120581,-0.01287781,-0.08283885,0.02964,-0.0313874,0.12186503,-0.00760047,0.0002708,-0.00331844,0.06641705,-0.00771569,-0.00595193,0.00768438,0.03555971,0.02934499,-0.01548817,-0.00916849,-0.03757266,0.00071363,-0.05916599,0.03851002,-0.01169175,-0.12712704,-0.02051645,-0.01441239,-0.03768937,0.07286039,0.02060759,0.06465291,0.02632544,0.03785762,0.04474599,0.01737387,0.06739124,-0.0647846,-0.08269001,0.01883413,-0.00223887,0.02692619,-0.02023395,-0.07878429,0.05758563,-0.0074227,0.05427444,0.00745238,0.000132,-0.00274713,0.02515356,-0.00931692,-0.02406573,0.06277639,-0.03192975,0.05922122,-0.02204142,0.01797663,0.06730839,-0.04070559,0.02339114,-0.01663786,0.01881505,-0.04713234,0.01502363,0.10408036,0.03675979,-0.03132559,0.05365714,0.08248432,0.02974267,0.00394131,-0.01238145,0.01317126,-0.01848958,0.06095685,0.00416114,0.01973981,-0.24065442,0.01062223,-0.02813952,0.07983895,-0.02067111,-0.0307506,0.08184788,0.03244558,0.0190096,-0.00081087,0.00737183,0.04893859,0.04841204,-0.10044772,0.05225917,-0.03253047,-0.00595575,-0.02551297,0.04418718,0.00361367,0.07433584,0.04001432,0.22517829,-0.01395935,0.01557231,0.03547088,0.02408918,0.03586204,-0.00068236,0.020081,-0.02443941,0.01937971,0.05127416,-0.04348349,-0.03619656,-0.02196502,-0.01989516,-0.01610791,-0.02467419,0.0063865,-0.05312412,0.03697199,-0.03089881,-0.01357477,0.14099306,0.02418852,-0.00821678,-0.09069755,0.03435453,0.04025814,-0.07454436,-0.05642008,-0.02674441,-0.05654507,0.0364592,0.03497158,0.02442869,0.00838345,-0.00394345,0.00609237,0.03017615,-0.03172094,0.07447747,0.03347724,-0.03743076],"last_embed":{"hash":"qvgmwx","tokens":474}}},"text":null,"length":0,"last_read":{"hash":"qvgmwx","at":1753423477509},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#{1}","lines":[100,123],"size":4411,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09623257,-0.0516715,-0.04257023,-0.02058193,-0.06422801,0.05996953,-0.02556418,-0.01215248,0.05165023,-0.0065929,-0.00084126,-0.00410893,0.0349465,-0.03076831,0.00433557,-0.01139897,0.03269111,-0.01236782,-0.07430889,-0.03879596,0.08204354,-0.02757165,-0.03930596,-0.07879906,0.02233313,0.03031671,-0.03993749,-0.06968583,-0.05183268,-0.22611208,0.04429023,0.02413779,0.03222219,-0.06140393,-0.07385639,-0.00342897,-0.07360074,0.0610521,-0.08331159,0.02805964,-0.00920121,0.02718422,-0.00563522,-0.01908616,0.00561547,-0.07775917,-0.00287422,0.02432074,0.08010505,-0.0168139,-0.07046002,-0.00668668,-0.00383873,0.05666478,0.07346189,0.05814791,0.02304638,0.08732874,-0.00134651,0.04968237,0.03612788,0.08233179,-0.21460402,0.03345497,-0.00161195,0.03137142,-0.01727676,-0.0210877,0.00488092,0.07346675,0.03630255,0.02617317,-0.01353891,0.04377165,0.0519022,-0.06159736,-0.05792489,-0.02716059,-0.02751922,0.01677495,-0.05201915,-0.0018547,0.00134387,-0.01265895,0.00942133,0.0315665,0.04264509,0.03480838,0.05448772,-0.0433121,0.00816653,0.05060967,0.00830472,0.01436262,0.019205,0.00940969,0.05937815,-0.05441437,-0.00277649,0.09899735,0.00696276,0.00799813,-0.00633172,-0.01590372,0.05210029,-0.06662772,-0.03143146,-0.04746322,-0.03170056,0.03742984,0.09887344,0.05596249,0.06076717,-0.03639906,-0.07244958,0.00456499,-0.03111017,-0.01330059,-0.00107338,0.00904912,0.01110427,0.01169419,0.00266398,-0.01756317,0.00909548,-0.00075761,0.00934795,0.06323288,0.00546733,0.03491154,0.02318888,0.03349047,-0.1378804,-0.03140645,-0.06397122,-0.0253828,-0.02273627,-0.03356068,-0.01348501,0.03414885,-0.02700479,-0.0873453,0.05924691,-0.08815739,-0.00181128,0.06435057,0.0043231,0.00803147,-0.00141224,0.0187544,-0.01373242,0.00793376,-0.02096757,-0.03987557,0.01131221,-0.02543193,0.09357884,0.07116902,-0.05920232,0.0110494,-0.01613519,-0.04268261,0.00904632,0.09563095,0.01578796,-0.10410465,-0.00966212,0.06798084,-0.03098559,-0.10085366,-0.04273751,0.0120962,-0.04813973,0.01867883,0.11251142,-0.03942252,-0.05738061,-0.02658927,0.02611803,-0.00198599,-0.03678945,-0.02161322,-0.00039738,0.0113406,-0.03346256,-0.08721435,0.04060394,-0.04993924,0.02386073,0.02228538,-0.01759599,0.01826157,-0.01949779,0.02812773,-0.0389717,0.00316424,-0.05476449,-0.05074162,0.08975311,-0.03222371,0.00769347,0.00788741,0.04278999,0.00276345,-0.03350296,0.0377034,-0.02843817,-0.06290627,0.05526872,0.04856683,-0.04280721,0.03334652,0.05723929,0.06327553,-0.08416775,0.06902406,-0.00378245,0.0088627,-0.00073918,0.02393434,0.00315819,0.03008892,-0.02334594,-0.20120925,-0.02012648,-0.03953721,0.00253787,0.02625857,-0.00744102,-0.00751508,-0.01861929,0.02757165,0.04254852,0.09106303,-0.07498866,0.01972319,0.06287855,0.02359469,-0.01173395,-0.09608553,-0.02624878,-0.0350369,0.07995767,0.002517,-0.03134223,-0.02379335,-0.06692763,0.01522081,-0.01613455,0.11224981,0.04634062,0.00051187,-0.00646428,0.06405067,0.00335627,-0.02532829,-0.03262139,0.04701402,0.02772689,-0.01885959,-0.0222088,-0.01688533,-0.00416627,-0.07042279,0.02825721,0.01461088,-0.12917466,-0.01105796,-0.01235927,-0.03864101,0.02617094,0.0241552,0.06294992,0.0303843,0.04701019,0.04013364,0.03733867,0.05932462,-0.03871892,-0.07718057,-0.00181985,-0.00604551,0.03658067,-0.02119646,-0.05827751,0.07573862,-0.01231113,0.05438961,0.01886955,-0.02961897,-0.00071613,0.01005393,-0.01431037,-0.01322648,0.07632941,0.01519766,0.04628995,-0.01275599,0.01388899,0.06912633,-0.04584277,0.000202,-0.00920909,-0.01600027,-0.0754877,0.02914668,0.07410379,0.05164555,-0.00140408,0.0730265,0.074375,0.01315717,0.00274415,0.00200607,0.00515617,-0.02487908,0.05886441,0.02788072,0.05913078,-0.24267884,0.01492001,-0.00623878,0.07524599,-0.01878217,0.0013417,0.09253775,-0.00177628,0.01736206,-0.01980723,0.06237015,0.03491762,0.02553955,-0.0774308,0.05915224,-0.0094436,-0.00569308,-0.04839786,0.0571718,0.01448787,0.0385802,0.06306197,0.21483982,-0.0194384,0.03410117,0.02418943,0.0194629,0.03688385,0.02178635,0.0385727,-0.02825889,0.00390409,0.03369607,-0.01096774,-0.06698903,0.00438567,-0.03313131,0.00039431,-0.01684737,0.03237133,-0.07166016,0.02899256,-0.04093046,0.0221802,0.13127878,0.00116788,-0.03218595,-0.1161859,0.04837434,0.03749738,-0.05876022,-0.08129077,-0.03975347,-0.02111069,0.03522814,0.02816204,0.02658981,-0.02198845,-0.01694055,-0.0030344,0.035133,-0.01343448,0.04534299,0.04449274,-0.0059074],"last_embed":{"hash":"n55vff","tokens":434}}},"text":null,"length":0,"last_read":{"hash":"n55vff","at":1753423477668},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>","lines":[124,150],"size":2301,"outlinks":[{"title":"_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_","target":"https://saliu.com/12-number-lotto-combinations.html","line":4},{"title":"_**Lotto Software, Wheels: 10-Number Combinations in Lotto 5 Games**_","target":"https://saliu.com/lotto-10-5-combinations.html","line":6},{"title":"_**Lotto Strategies**_","target":"https://forums.saliu.com/lottery-strategies-start.html","line":7},{"title":"_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_","target":"https://saliu.com/bbs/messages/532.html","line":8},{"title":"_**Lottery, Gambling Experiences: Robbed of Prize Money!**_","target":"https://saliu.com/bbs/messages/535.html","line":9},{"title":"_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_","target":"https://saliu.com/lotto-triples.html","line":11},{"title":"Ion Saliu's Probability Book on mathematics of lotto strategy.","target":"https://saliu.com/probability-book-Saliu.jpg","line":19},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":19},{"title":"The best lotto wheels are also created by the best lottery player in history, Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":22},{"title":"Forums","target":"https://forums.saliu.com/","line":24},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":24},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":24},{"title":"Contents","target":"https://saliu.com/content/index.html","line":24},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":24},{"title":"Home","target":"https://saliu.com/index.htm","line":24},{"title":"Search","target":"https://saliu.com/Search.htm","line":24},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":24},{"title":"Lotto strategy by the best lottery player ever hit the jackpot generating 12-number combinations.","target":"https://saliu.com/HLINE.gif","line":26}],"class_name":"SmartBlock"},
"smart_blocks:notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{10}": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11414219,-0.04335933,-0.02897601,-0.00730483,-0.07156547,0.07411446,-0.01516471,0.03770515,0.06453177,0.00166402,0.00504194,-0.00699295,0.02596293,-0.00308164,-0.01576282,-0.00005032,0.01135622,-0.01303346,-0.09326717,-0.02873018,0.09393903,-0.05662135,-0.03876145,-0.11426865,0.03150599,0.01442185,-0.0231248,-0.04762819,-0.03041743,-0.23387739,0.0089983,0.02222323,0.01561857,-0.05814745,-0.10203201,-0.00519778,-0.03867863,0.0659594,-0.04362022,0.03674958,0.00948036,0.02787142,0.00066453,0.0003344,0.016287,-0.05920329,0.01090114,0.01419107,0.06728643,-0.00246255,-0.05474526,0.00492388,-0.01010822,0.03194819,0.0712215,0.04578323,0.01409021,0.07403561,0.01593202,0.06424408,0.00217702,0.08731887,-0.229476,0.02417033,-0.02030372,0.01197392,-0.0388363,0.00058205,-0.00228736,0.05168973,0.04103974,0.03249234,0.00599847,0.05329918,0.07822271,-0.04868833,-0.04761235,-0.03731605,-0.03618991,0.02738608,-0.04197738,0.01174442,-0.01997178,-0.01946729,0.01426218,0.04263633,0.03460359,0.02574137,0.0481264,-0.04104762,0.0376865,0.0539948,0.01531609,0.01826758,0.01229605,0.0296329,0.0414498,-0.05062271,0.01870198,0.09237517,0.01748599,0.01556658,0.00851783,0.0014981,0.03988811,-0.05327918,-0.02213691,-0.04225205,-0.01359616,0.01838095,0.07829975,0.04642297,0.04309901,-0.03911082,-0.08011384,0.00055418,-0.01075776,-0.00855194,-0.00122307,0.00756415,-0.01317371,0.02703166,0.01361094,-0.01119057,0.01546355,-0.01388472,0.00665856,0.06229936,0.0080548,0.03776899,0.02131694,0.01729825,-0.11365503,-0.05667365,-0.0447675,-0.02767483,-0.03020978,-0.03236976,-0.01865829,0.03156081,-0.00790211,-0.07233046,0.02830104,-0.08901171,-0.0222249,0.0418504,0.01023924,-0.00609628,-0.00478722,0.01636847,-0.00614156,-0.00243226,-0.01663811,-0.02976815,0.0377488,-0.01515938,0.0818684,0.08477356,-0.04934189,0.00737232,-0.03967095,-0.0535564,0.00407963,0.10652929,-0.01814246,-0.09104505,-0.03905945,0.0601358,-0.01756254,-0.10313801,-0.03468506,-0.00434759,-0.04049858,0.03705263,0.10023122,-0.0148893,-0.04812673,-0.01613079,-0.01009081,0.00200396,-0.0376536,-0.04039846,-0.02234234,0.00555369,-0.04620156,-0.07587104,0.03745458,-0.04344294,0.01629876,0.05888805,-0.00489359,0.03063947,-0.03715133,0.03193206,-0.06035756,-0.00378849,-0.04307225,-0.05466412,0.06669827,-0.02506351,-0.03199589,-0.00273541,0.00042417,0.00351959,-0.0637724,0.03207803,-0.02231983,-0.04948966,0.07511598,0.04509025,-0.01554558,0.01317487,0.02926822,0.06000276,-0.08873109,0.07236677,0.00018317,0.01522404,0.00173868,0.00139123,0.02313167,-0.00810478,-0.0523004,-0.19547005,-0.0322407,-0.04961501,-0.00548571,0.03865892,-0.0145993,0.01754755,-0.04076108,0.04128843,0.03178795,0.08494627,-0.06925854,0.01311793,0.05292883,0.00606747,-0.02813293,-0.10958853,-0.04543843,-0.04201251,0.08142148,0.00922854,-0.00801826,-0.03179212,-0.05526784,0.01786346,-0.03686542,0.12433208,0.05864258,0.03136737,-0.00482734,0.08065604,-0.00130377,-0.02034703,-0.00487961,0.03847109,0.00568066,-0.02963444,-0.02251166,-0.05914321,-0.00406406,-0.05227477,0.02416683,0.01826646,-0.13345346,-0.03490119,-0.0234004,-0.0243363,0.03542045,0.01858161,0.05160717,0.05700469,0.03530944,0.04612008,0.01198276,0.05395127,-0.03451396,-0.06660472,0.00199471,-0.0323659,0.05069344,-0.01822204,-0.02019665,0.07652208,-0.00510112,0.0582635,0.00889801,-0.04408499,-0.01748614,0.01196022,-0.0221963,-0.02326729,0.08106654,0.02260492,0.04391765,0.01609954,-0.0065364,0.07485262,-0.06994288,-0.01055269,-0.00948272,-0.01105855,-0.05474246,0.04053634,0.06054046,0.03545995,-0.00861042,0.07416402,0.06480306,-0.00873953,0.00431697,-0.0002948,0.0156083,-0.03164139,0.05508764,0.03325458,0.05644106,-0.25518546,0.01257143,-0.01252545,0.0834612,-0.0080689,-0.00540838,0.08027341,-0.0122337,0.02720513,-0.01897881,0.06689566,0.04525504,0.03054485,-0.05902115,0.0523429,-0.03174239,-0.02809929,-0.04560961,0.06279777,0.01592937,0.05623623,0.05029123,0.22875105,0.00401886,0.01947534,0.02705351,0.02194884,0.0325755,0.01227532,0.02877847,-0.03908664,0.01989282,0.03736096,-0.02703042,-0.06147455,0.03423157,-0.02575939,0.01815952,-0.03466696,0.00303777,-0.03920913,0.02498611,-0.02862554,0.03795477,0.15456544,0.00250712,-0.03727242,-0.09069742,0.07305954,0.04426509,-0.0635943,-0.05955884,-0.04528084,-0.02417905,0.01914753,0.02006626,0.05214238,-0.01559228,0.00292944,-0.00460458,0.03828918,0.01107262,0.07871145,0.0369224,0.00251604],"last_embed":{"hash":"se6r6a","tokens":505}}},"text":null,"length":0,"last_read":{"hash":"se6r6a","at":1753423477808},"key":"notes/saliu/Jackpot Lottery Strategy Software 6-12 Number Lotto Wheels.md#Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels#<u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>#<u>Update October 2, 2010</u>#{10}","lines":[136,150],"size":1242,"outlinks":[{"title":"Ion Saliu's Probability Book on mathematics of lotto strategy.","target":"https://saliu.com/probability-book-Saliu.jpg","line":7},{"title":"**_Probability Theory, Live!_**","target":"https://saliu.com/probability-book.html","line":7},{"title":"The best lotto wheels are also created by the best lottery player in history, Ion Saliu.","target":"https://saliu.com/HLINE.gif","line":10},{"title":"Forums","target":"https://forums.saliu.com/","line":12},{"title":"New Writings","target":"https://saliu.com/bbs/index.html","line":12},{"title":"Odds, Generator","target":"https://saliu.com/calculator_generator.html","line":12},{"title":"Contents","target":"https://saliu.com/content/index.html","line":12},{"title":"Fundamental Formula","target":"https://saliu.com/formula.htm","line":12},{"title":"Home","target":"https://saliu.com/index.htm","line":12},{"title":"Search","target":"https://saliu.com/Search.htm","line":12},{"title":"Sitemap","target":"https://saliu.com/sitemap/index.html","line":12},{"title":"Lotto strategy by the best lottery player ever hit the jackpot generating 12-number combinations.","target":"https://saliu.com/HLINE.gif","line":14}],"class_name":"SmartBlock"},
